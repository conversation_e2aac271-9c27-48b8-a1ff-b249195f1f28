﻿<!DOCTYPE html>
<html>
	<head>
		<title>AutoFormen einfügen und formatieren</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add an autoshape to your presentation and adjust its properties." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>AutoFormen einfügen und formatieren</h1>
			<h3>AutoForm einfügen</h3>
			<p>Um eine AutoForm in eine Folie im <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> <b>einzufügen</b>:</p>
			<ol>
				<li>Wählen Sie in der Folienliste links die Folie aus, der Sie eine AutoForm hinzufügen wollen.</li>
				<li>
					Klicken Sie auf das Symbol <div class="icon icon-insertautoshape"></div> <b>Form</b> auf der Registerkarte <b>Startseite</b> oder klicken Sie auf die <div class="big big-shapegallery"></div> <b>Formengalerie</b> auf der Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.
				</li>
				<li>Wählen Sie eine der verfügbaren Gruppen von AutoFormen aus der <b>Formengalerie</b> aus: <em>Zuletzt verwendet</em>, <em>Standardformen</em>, <em>Geformte Pfeile</em>, <em>Mathematik</em>, <em>Diagramme</em>, <em>Sterne &amp; Bänder</em>, <em>Legenden</em>, <em>Schaltflächen</em>, <em>Rechtecke</em>, <em>Linien</em>.</li>
				<li>Klicken Sie in der gewählten Gruppe auf die gewünschte AutoForm.</li>
				<li>
					Positionieren Sie den Mauszeiger an der Stelle, an der Sie eine Form hinzufügen möchten.
					<p class="note">Sie können klicken und ziehen, um die Form auszudehnen.</p>
				</li>
				<li>
					Sobald die AutoForm hinzugefügt wurde, können Sie <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">Größe, Position und Eigenschaften</a> ändern.
					<p class="note">Um eine Bildunterschrift innerhalb der AutoForm hinzuzufügen, wählen Sie die Form auf der Folie aus und geben Sie den Text ein. Ein solcher Text wird Bestandteil der AutoForm (wenn Sie die AutoForm verschieben oder drehen, wird der Text ebenfalls verschoben oder gedreht).</p>
				</li>
			</ol>
			<p>Es ist auch möglich, einem Folienlayout eine AutoForm hinzuzufügen. Weitere Informationen finden Sie in dieser <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">Artikel</a>.</p>
			<hr />
			<h3>Einstellungen der AutoForm anpassen</h3>
			<p>Einige Eigenschaften der AutoFormen können in der Registerkarte <b>Formeinstellungen</b> in der rechten Seitenleiste geändert werden. Klicken Sie dazu auf die AutoForm und wählen Sie das Symbol <b>Formeinstellungen</b> <span class="icon icon-shape_settings_icon"></span> in der rechten Seitenleiste aus. Hier können die folgenden Eigenschaften geändert werden:</p>
			<p><img class="floatleft" alt="Registerkarte Formeinstellungen" src="../images/shapesettingstab.png" /></p>
			<ul style="margin-left: 280px;">
				<li>
					<b>Füllung</b> - zum Ändern der Füllung einer AutoForm. Folgende Optionen stehen Ihnen zur Verfügung:
					<ul>
						<li><b>Farbfüllung</b> - um die homogene Farbe zu bestimmen, mit der Sie die gewählte Form füllen wollen.</li>
						<li><b>Füllung mit Farbverlauf</b> - um die Form mit einem sanften Übergang von einer Farbe zu einer anderen zu füllen.</li>
						<li><b>Bild oder Textur</b> - um ein Bild oder eine vorgegebene Textur als Hintergrund der Form zu nutzen.</li>
						<li><b>Muster</b> - um die Form mit einem zweifarbigen Design zu füllen, das aus regelmäßig wiederholten Elementen besteht.</li>
						<li><b>Keine Füllung</b> - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten.</li>
					</ul>
					<p>Weitere Informationen zu diesen Optionen finden Sie im Abschnitt <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">Objekte ausfüllen und Farben auswählen</a>.</p>
				</li>
				<li id="shapestroke">
					<b>Strich</b> - in dieser Gruppe können Sie Strichbreite und -farbe der AutoForm ändern.
					<ul>
						<li>Um die <b>Breite</b> der Striche zu ändern, wählen Sie eine der verfügbaren Optionen im Listenmenü <b>Größe</b> aus. Die folgenden Optionen stehen Ihnen zur Verfügung: 0,5 Pt., 1 Pt., 1,5 Pt., 2,25 Pt., 3 Pt., 4,5 Pt., 6 Pt. Alternativ können Sie die Option <b>Keine Linie</b> auswählen, wenn Sie keine Umrandung wünschen.</li>
						<li>Um die <b>Farbe</b> der Striche zu ändern, klicken Sie auf das farbige Feld und <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">wählen Sie die gewünschte Farbe</a> aus. Sie können die gewählte <b>Designfarbe</b>, eine <b>Standardfarbe</b> oder eine <b>benutzerdefinierte Farbe</b> auswählen.</li>
						<li>Um den <b>Typ</b> der Striche zu ändern, wählen Sie die gewünschte Option aus der entsprechenden Drop-Down-Liste aus (standardmäßig wird eine durchgezogene Linie verwendet, diese können Sie in eine der verfügbaren gestrichelten Linien ändern).</li>
					</ul>
				</li>
				<li>
					<b>Drehen</b> dient dazu die Form um 90 Grad im oder gegen den Uhrzeigersinn zu drehen oder die Form horizontal oder vertikal zu spiegeln. Wählen Sie eine der folgenden Optionen:
					<ul>
						<li><div class="icon icon-rotatecounterclockwise"></div> um die Form um 90 Grad gegen den Uhrzeigersinn zu drehen</li>
						<li><div class="icon icon-rotateclockwise"></div> um die Form um 90 Grad im Uhrzeigersinn zu drehen</li>
						<li><div class="icon icon-fliplefttoright"></div> um die Form horizontal zu spiegeln (von links nach rechts)</li>
						<li><div class="icon icon-flipupsidedown"></div> um die Form vertikal zu spiegeln (von oben nach unten)</li>
					</ul>
				</li>
				<li><b>AutoForm ändern</b> - verwenden Sie diesen Abschnitt, um die aktuelle AutoForm durch eine andere Form aus der Drop-Down-Liste zu ersetzen.</li>
				<li><b>Schatten anzeigen</b> - aktivieren Sie diese Option, um die Form mit Schatten anzuzeigen.</li>
			</ul>
			<hr />
			<p>Um die <b>erweiterte Einstellungen</b> der AutoForm zu ändern, klicken Sie mit der rechten Maustaste auf die Form und wählen Sie die Option <b>Form - erweiterte Einstellungen</b> im Menü aus, oder klicken Sie in der rechten Seitenleiste auf die Verknüpfung <b>Erweiterte Einstellungen anzeigen</b>. Das Fenster mit den Formeigenschaften wird geöffnet:</p>
			<p><img alt="Formeigenschaften - Registerkarte Größe" src="../images/shape_properties1.png" /></p>
			<p>Auf der Registerkarte <b>Positionierung</b> können Sie die <b>Breite</b> und/oder <b>Höhe</b> der AutoForm ändern. Wenn Sie auf die Schaltfläche <b>Seitenverhältnis beibehalten</b> <span class="icon icon-constantproportions"></span> klicken (in diesem Fall sieht es so aus <span class="icon icon-constantproportionsactivated"></span>), werden Breite und Höhe zusammen geändert, wobei das ursprüngliche Seitenverhältnis der automatischen Form beibehalten wird.</p>
			<p>Sie können die genaue Position auch mit den Feldern <b>Horizontal</b> und <b>Vertikal</b> sowie dem Feld <b>Ab</b> festlegen, wo Sie auf Einstellungen wie <b>Obere linke Ecke</b> und <b>Zentriert</b> zugreifen können.</p>
			<p><img alt="Form - Erweiterte Einstellungen" src="../images/shape_properties5.png" /></p>
			<p>Die Registerkarte <b>Rotation</b> umfasst die folgenden Parameter:</p>
			<ul>
				<li><b>Winkel</b> - mit dieser Option lässt sich die Form in einem genau festgelegten Winkel drehen. Geben Sie den erforderlichen Wert in Grad in das Feld ein oder stellen Sie diesen mit den Pfeilen rechts ein.</li>
				<li><b>Gekippt</b> - aktivieren Sie das Kontrollkästchen <b>Horizontal</b>, um die Form horizontal zu spiegeln (von links nach rechts), oder aktivieren Sie das Kontrollkästchen <b>Vertikal</b>, um die Form vertikal zu spiegeln (von oben nach unten).</li>
			</ul>
			<p><img alt="Formeigenschaften - Registerkarte Stärken &amp; Pfeile" src="../images/shape_properties.png" /></p>
			<p>Die Registerkarte <b>Stärken &amp; Pfeile</b> enthält folgende Parameter:</p>
			<ul>
				<li>
					<b>Linienart</b> - in dieser Gruppe können Sie die folgenden Parameter bestimmen:
					<ul>
						<li>
							<b>Abschlusstyp</b> - legen Sie den Stil für den Abschluss der Linie fest, diese Option besteht nur bei Formen mit offener Kontur wie Linien, Polylinien usw.:
							<ul>
								<li><b>Flach</b> - für flache Endpunkte.</li>
								<li><b>Rund</b> - für runde Endpunkte.</li>
								<li><b>Eckig</b> - quadratisches Linienende.</li>
							</ul>
						</li>
						<li>
							<b>Verknüpfungstyp</b> - legen Sie die Art der Verknüpfung von zwei Linien fest, z.B. kann diese Option auf Polylinien oder die Ecken von Dreiecken bzw. Vierecken angewendet werden:
							<ul>
								<li><b>Rund</b> - die Ecke wird abgerundet.</li>
								<li><b>Schräge Kante</b> - die Ecke wird schräg abgeschnitten.</li>
								<li><b>Winkel</b> - spitze Ecke. Dieser Typ passt gut bei AutoFormen mit spitzen Winkeln.</li>
							</ul>
							<p class="note">Der Effekt wird auffälliger, wenn Sie eine hohe Konturbreite verwenden.</p>
						</li>
					</ul>
				</li>
				<li><b>Pfeile</b> - diese Option ist verfügbar, wenn eine Form aus der Gruppe <b>Linien</b> ausgewählt ist. Hier können Sie die <b>Start</b>- und <b>Endlinienart</b> sowie die <b>Start-</b> und <b>Endgröße</b> des Pfeils festlegen, indem Sie die entsprechende Option aus den Drop-Down-Listen auswählen.</li>
			</ul>
			<p id="internalmargins"><img alt="Formeigenschaften - Registerkarte Ränder" src="../images/shape_properties3.png" /></p>
			<p>Die Registerkarte <b>Textfeld</b> enthält die folgenden Parameter:</p>
			<ul>
				<li><b>Automatisch anpassen</b>, um die Art und Weise zu ändern, wie Text innerhalb der Form angezeigt wird: <b>Ohne automatische Anpassung</b>, <b>Text bei Überlauf verkleinern</b>, <b>Die Form am Text anpassen</b>.</li>
				<li><b>Ränder um den Text</b>, um die inneren Ränder der automatischen Form wie <b>Oben</b>, <b>Unten</b>, <b>Links</b> und <b>Rechts</b> zu ändern (d. h. den Abstand zwischen dem Text innerhalb die Form und die Grenzen der AutoForm).</li>
			</ul>
			<p class="note">Diese Registerkarte ist nur verfügbar, wenn der AutoForm ein Text hinzugefügt wurde, ansonsten wird die Registerkarte ausgeblendet.</p>
			<p id="columns"><img alt="Formeigenschaften - Spalten" src="../images/shape_properties2.png" /></p>
			<p>Über die Registerkarte <b>Spalten</b> ist es möglich, der AutoForm Textspalten hinzuzufügen und die gewünschte <b>Anzahl von Spalten</b> (bis zu 16) und den <b>Abstand zwischen Spalten</b> festzulegen. Wenn Sie auf <b>OK</b> klicken, erscheint der bereits vorhandene Text, oder jeder beliebige Text den Sie in die AutoForm eingeben, in den Spalten und geht flüssig von einer Spalte in die nächste über.</p>
			<p><img alt="Formeigenschaften - Alternativtext" src="../images/shape_properties4.png" /></p>
			<p>Die Registerkarte <b>Alternativer Text</b> ermöglicht die Eingabe eines <b>Titels</b> und einer <b>Beschreibung</b>, die Personen mit Sehbehinderungen oder kognitiven Beeinträchtigungen vorgelesen werden kann, damit sie besser verstehen können, welche Informationen in der Form enthalten sind.</p>
			<hr />
			<p>Um die hinzugefügte AutoForm zu <b>ersetzen</b>, klicken Sie diese mit der linken Maustaste an, wechseln Sie in die Registerkarte <b>Formeinstellungen</b> in der rechten Seitenleiste und wählen Sie unter <b>AutoForm ändern</b> in der Liste die gewünschte Form aus.</p>
			<p>Um die hinzugefügte AutoForm zu <b>löschen</b>, klicken Sie dieses mit der linken Maustaste an und drücken Sie die Taste <b>Entfernen</b> auf Ihrer Tastatur.</p>
			<p>Um mehr über die <b>Ausrichtung</b> einer AuftoForm auf einer Folie zu erfahren oder mehrere AutoFormen <b>anzuordnen</b>, lesen Sie den Abschnitt <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">Objekte auf einer Folie ausrichten und anordnen</a>.</p>
			<hr />
			<h3>AutoFormen mithilfe von Verbindungen anbinden</h3>
			<p>Sie können Autoformen mithilfe von Linien mit Verbindungspunkten verbinden, um Abhängigkeiten zwischen Objekten zu demonstrieren (z.B. wenn Sie ein Flussdiagramm erstellen wollen). Gehen Sie dazu vor wie folgt:</p>
			<ol>
				<li>Klicken Sie in der oberen Symbolleiste in den Registerkarten <b>Start</b> oder <b>Einfügen</b> auf <div class="icon icon-insertautoshape"></div> <b>Form</b>.</li>
				<li>
					Wählen Sie die Gruppe <b>Linien</b> im Menü aus.
					<p><img alt="Formen - Linien" src="../images/connectors.png" /></p>
				</li>
				<li>Klicken Sie auf die gewünschte Form in der ausgewählten Gruppe (mit Ausnahme der letzten drei Formen, bei denen es sich nicht um Konnektoren handelt: <em>Kurve</em>, <em>Skizze</em> und <em>Freihand</em>).</li>
				<li>
					Bewegen Sie den Mauszeiger über die erste AutoForm und klicken Sie auf einen der Verbindungspunkte <div class="icon icon-connectionpoint"></div>, die auf dem Umriss der Form zu sehen sind.
					<p><span class="big big-connectors_firstshape"></span></p>
				</li>
				<li>
					Bewegen Sie den Mauszeiger in Richtung der zweiten AutoForm und klicken Sie auf den gewünschten Verbindungspunkt auf dem Umriss der Form.
					<p><span class="big big-connectors_secondshape"></span></p>
				</li>
			</ol>
			<p>Wenn Sie die verbundenen AutoFormen verschieben, bleiben die Verbindungen an die Form gebunden und bewegen sich mit den Formen zusammen.</p>
			<p><span class="big big-connectors_moveshape"></span></p>
			<p>Alternativ können Sie die Verbindungen auch von den Formen lösen und an andere Verbindungspunkte anbinden.</p>
		</div>
	</body>
</html>