{"DATE": {"a": "(year; month; day)", "d": "Returns the number that represents the date in the date-time code"}, "DATEDIF": {"a": "( start-date; end-date; unit )", "d": "Returns the difference between two date values (start date and end date), based on the interval (unit) specified"}, "DATEVALUE": {"a": "(date_text)", "d": "Converts a date in the form of text to a number that represents the date in the date-time code"}, "DAY": {"a": "(serial_number)", "d": "Returns the day of the month, a number from 1 to 31."}, "DAYS": {"a": "(end_date; start_date)", "d": "Returns the number of days between the two dates."}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "Returns the number of days between two dates based on a 360-day year (twelve 30-day months)"}, "EDATE": {"a": "(start_date; months)", "d": "Returns the serial number of the date that is the indicated number of months before or after the start date"}, "EOMONTH": {"a": "(start_date; months)", "d": "Returns the serial number of the last day of the month before or after a specified number of months"}, "HOUR": {"a": "(serial_number)", "d": "Returns the hour as a number from 0 (12:00 A.M.) to 23 (11:00 P.M.)."}, "ISOWEEKNUM": {"a": "(date)", "d": "Returns the ISO week number in the year for a given date"}, "MINUTE": {"a": "(serial_number)", "d": "Returns the minute, a number from 0 to 59."}, "MONTH": {"a": "(serial_number)", "d": "Returns the month, a number from 1 (January) to 12 (December)."}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "Returns the number of whole workdays between two dates"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "Returns the number of whole workdays between two dates with custom weekend parameters"}, "NOW": {"a": "()", "d": "Returns the current date and time formatted as a date and time."}, "SECOND": {"a": "(serial_number)", "d": "Returns the second, a number from 0 to 59."}, "TIME": {"a": "(hour; minute; second)", "d": "Converts hours, minutes and seconds given as numbers to a serial number, formatted with a time format"}, "TIMEVALUE": {"a": "(time_text)", "d": "Converts a text time to a serial number for a time, a number from 0 (12:00:00 AM) to 0.999988426 (11:59:59 PM). Format the number with a time format after entering the formula"}, "TODAY": {"a": "()", "d": "Returns the current date formatted as a date."}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "Returns a number from 1 to 7 identifying the day of the week of a date."}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "Returns the week number in the year"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "Returns the serial number of the date before or after a specified number of workdays"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "Returns the serial number of the date before or after a specified number of workdays with custom weekend parameters"}, "YEAR": {"a": "(serial_number)", "d": "Returns the year of a date, an integer in the range 1900-9999."}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "Returns the year fraction representing the number of whole days between start_date and end_date"}, "BESSELI": {"a": "(x; n)", "d": "Returns the modified <PERSON>ssel function In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Returns the <PERSON>ssel function Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Returns the modified <PERSON>ssel function Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Returns the Bessel function Yn(x)"}, "BIN2DEC": {"a": "(number)", "d": "Converts a binary number to decimal"}, "BIN2HEX": {"a": "(number; [places])", "d": "Converts a binary number to hexadecimal"}, "BIN2OCT": {"a": "(number; [places])", "d": "Converts a binary number to octal"}, "BITAND": {"a": "(number1; number2)", "d": "Returns a bitwise 'And' of two numbers"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Returns a number shifted left by shift_amount bits"}, "BITOR": {"a": "(number1; number2)", "d": "Returns a bitwise 'Or' of two numbers"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Returns a number shifted right by shift_amount bits"}, "BITXOR": {"a": "(number1; number2)", "d": "Returns a bitwise 'Exclusive Or' of two numbers"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "Converts real and imaginary coefficients into a complex number"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "Converts a number from one measurement system to another"}, "DEC2BIN": {"a": "(number; [places])", "d": "Converts a decimal number to binary"}, "DEC2HEX": {"a": "(number; [places])", "d": "Converts a decimal number to hexadecimal"}, "DEC2OCT": {"a": "(number; [places])", "d": "Converts a decimal number to octal"}, "DELTA": {"a": "(number1; [number2])", "d": "Tests whether two numbers are equal"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "Returns the error function"}, "ERF.PRECISE": {"a": "(X)", "d": "Returns the error function"}, "ERFC": {"a": "(x)", "d": "Returns the complementary error function"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returns the complementary error function"}, "GESTEP": {"a": "(number; [step])", "d": "Tests whether a number is greater than a threshold value"}, "HEX2BIN": {"a": "(number; [places])", "d": "Converts a Hexadecimal number to binary"}, "HEX2DEC": {"a": "(number)", "d": "Converts a hexadecimal number to decimal"}, "HEX2OCT": {"a": "(number; [places])", "d": "Converts a hexadecimal number to octal"}, "IMABS": {"a": "(inumber)", "d": "Returns the absolute value (modulus) of a complex number"}, "IMAGINARY": {"a": "(inumber)", "d": "Returns the imaginary coefficient of a complex number"}, "IMARGUMENT": {"a": "(inumber)", "d": "Returns the argument q, an angle expressed in radians"}, "IMCONJUGATE": {"a": "(inumber)", "d": "Returns the complex conjugate of a complex number"}, "IMCOS": {"a": "(inumber)", "d": "Returns the cosine of a complex number"}, "IMCOSH": {"a": "(inumber)", "d": "Returns the hyperbolic cosine of a complex number"}, "IMCOT": {"a": "(inumber)", "d": "Returns the cotangent of a complex number"}, "IMCSC": {"a": "(inumber)", "d": "Returns the cosecant of a complex number"}, "IMCSCH": {"a": "(inumber)", "d": "Returns the hyperbolic cosecant of a complex number"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "Returns the quotient of two complex numbers"}, "IMEXP": {"a": "(inumber)", "d": "Returns the exponential of a complex number"}, "IMLN": {"a": "(inumber)", "d": "Returns the natural logarithm of a complex number"}, "IMLOG10": {"a": "(inumber)", "d": "Returns the base-10 logarithm of a complex number"}, "IMLOG2": {"a": "(inumber)", "d": "Returns the base-2 logarithm of a complex number"}, "IMPOWER": {"a": "(inumber; number)", "d": "Returns a complex number raised to an integer power"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "Returns the product of 1 to 255 complex numbers"}, "IMREAL": {"a": "(inumber)", "d": "Returns the real coefficient of a complex number"}, "IMSEC": {"a": "(inumber)", "d": "Returns the secant of a complex number"}, "IMSECH": {"a": "(inumber)", "d": "Returns the hyperbolic secant of a complex number"}, "IMSIN": {"a": "(inumber)", "d": "Returns the sine of a complex number"}, "IMSINH": {"a": "(inumber)", "d": "Returns the hyperbolic sine of a complex number"}, "IMSQRT": {"a": "(inumber)", "d": "Returns the square root of a complex number"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "Returns the difference of two complex numbers"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "Returns the sum of complex numbers"}, "IMTAN": {"a": "(inumber)", "d": "Returns the tangent of a complex number"}, "OCT2BIN": {"a": "(number; [places])", "d": "Converts an octal number to binary"}, "OCT2DEC": {"a": "(number)", "d": "Converts an octal number to decimal"}, "OCT2HEX": {"a": "(number; [places])", "d": "Converts an octal number to hexadecimal"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "Averages the values in a column in a list or database that match conditions you specify"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "Counts the cells containing numbers in the field (column) of records in the database that match the conditions you specify"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "Counts nonblank cells in the field (column) of records in the database that match the conditions you specify"}, "DGET": {"a": "(database; field; criteria)", "d": "Extracts from a database a single record that matches the conditions you specify"}, "DMAX": {"a": "(database; field; criteria)", "d": "Returns the largest number in the field (column) of records in the database that match the conditions you specify"}, "DMIN": {"a": "(database; field; criteria)", "d": "Returns the smallest number in the field (column) of records in the database that match the conditions you specify"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "Multiplies the values in the field (column) of records in the database that match the conditions you specify"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "Estimates the standard deviation based on a sample from selected database entries"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "Calculates the standard deviation based on the entire population of selected database entries"}, "DSUM": {"a": "(database; field; criteria)", "d": "Adds the numbers in the field (column) of records in the database that match the conditions you specify"}, "DVAR": {"a": "(database; field; criteria)", "d": "Estimates variance based on a sample from selected database entries"}, "DVARP": {"a": "(database; field; criteria)", "d": "Calculates variance based on the entire population of selected database entries"}, "CHAR": {"a": "(number)", "d": "Returns the character specified by the code number from the character set for your computer"}, "CLEAN": {"a": "(text)", "d": "Removes all nonprintable characters from text"}, "CODE": {"a": "(text)", "d": "Returns a numeric code for the first character in a text string, in the character set used by your computer"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Joins several text strings into one text string"}, "CONCAT": {"a": "(text1; ...)", "d": "Concatenates a list or range of text strings"}, "DOLLAR": {"a": "(number; [decimals])", "d": "Converts a number to text, using currency format"}, "EXACT": {"a": "(text1; text2)", "d": "Checks whether two text strings are exactly the same, and returns TRUE or FALSE. EXACT is case-sensitive"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "Returns the starting position of one text string within another text string. FIND is case-sensitive"}, "FINDB": {"a": "( string-1; string-2; [start-pos] )", "d": "Finds the specified substring (string-1) within a string (string-2) and is intended for languages the double-byte character set (DBCS) like Japanese, Chinese, Korean etc."}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "Rounds a number to the specified number of decimals and returns the result as text with or without commas"}, "LEFT": {"a": "(text; [num_chars])", "d": "Returns the specified number of characters from the start of a text string"}, "LEFTB": {"a": "( string; [number-chars] )", "d": "Extracts the substring from the specified string starting from the left character and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc."}, "LEN": {"a": "(text)", "d": "Returns the number of characters in a text string"}, "LENB": {"a": "( string )", "d": "Analyses the specified string and returns the number of characters it contains and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc."}, "LOWER": {"a": "(text)", "d": "Converts all letters in a text string to lowercase"}, "MID": {"a": "(text; start_num; num_chars)", "d": "Returns the characters from the middle of a text string, given a starting position and length"}, "MIDB": {"a": "( string; start-pos; number-chars )", "d": "Extracts the characters from the specified string starting from any position and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc."}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Converts text to number in a locale-independent manner"}, "PROPER": {"a": "(text)", "d": "Converts a text string to proper case; the first letter in each word to uppercase, and all other letters to lowercase"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Replaces part of a text string with a different text string"}, "REPLACEB": {"a": "( string-1; start-pos; number-chars; string-2 )", "d": "Replaces a set of characters, based on the number of characters and the start position you specify, with a new set of characters and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc."}, "REPT": {"a": "(text; number_times)", "d": "Repeats text a given number of times. Use REPT to fill a cell with a number of instances of a text string"}, "RIGHT": {"a": "(text; [num_chars])", "d": "Returns the specified number of characters from the end of a text string"}, "RIGHTB": {"a": "( string; [number-chars] )", "d": "Extracts a substring from a string starting from the right-most character, based on the specified number of characters and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc."}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "Returns the number of the character at which a specific character or text string is first found, reading left to right (not case-sensitive)"}, "SEARCHB": {"a": "( string-1; string-2; [start-pos] )", "d": "Returns the location of the specified substring in a string and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc."}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "Replaces existing text with new text in a text string"}, "T": {"a": "(value)", "d": "Checks whether a value is text, and returns the text if it is, or returns double quotes (empty text) if it is not"}, "TEXT": {"a": "(value; format_text)", "d": "Converts a value to text in a specific number format"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "Concatenates a list or range of text strings using a delimiter"}, "TRIM": {"a": "(text)", "d": "Removes all spaces from a text string except for single spaces between words"}, "UNICHAR": {"a": "(number)", "d": "Returns the Unicode character referenced by the given numeric value"}, "UNICODE": {"a": "(text)", "d": "Returns the number (code point) corresponding to the first character of the text"}, "UPPER": {"a": "(text)", "d": "Converts a text string to all uppercase letters"}, "VALUE": {"a": "(text)", "d": "Converts a text string that represents a number to a number"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "Returns the average of the absolute deviations of data points from their mean. Arguments can be numbers or names, arrays or references that contain numbers"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "Returns the average (arithmetic mean) of its arguments, which can be numbers or names, arrays or references that contain numbers"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "Returns the average (arithmetic mean) of its arguments, evaluating text and FALSE in arguments as 0; TRUE evaluates as 1. Arguments can be numbers, names, arrays or references"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "Finds average (arithmetic mean) for the cells specified by a given condition or criteria"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "Finds average (arithmetic mean) for the cells specified by a given set of conditions or criteria"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Returns the cumulative beta probability density function"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Returns the inverse of the cumulative beta probability density function (BETADIST)"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "Returns the beta probability distribution function"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Returns the inverse of the cumulative beta probability density function (BETA.DIST)"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Returns the individual term binomial distribution probability"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Returns the individual term binomial distribution probability"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "Returns the probability of a trial result using a binomial distribution"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "Returns the smallest value for which the cumulative binomial distribution is greater than or equal to a criterion value"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "Returns the right-tailed probability of the chi-squared distribution"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Returns the inverse of the right-tailed probability of the chi-squared distribution"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Returns the test for independence: the value from the chi-squared distribution for the statistic and the appropriate degrees of freedom"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Returns the left-tailed probability of the chi-squared distribution"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "Returns the right-tailed probability of the chi-squared distribution"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "Returns the inverse of the left-tailed probability of the chi-squared distribution"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "Returns the inverse of the right-tailed probability of the chi-squared distribution"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "Returns the test for independence: the value from the chi-squared distribution for the statistic and the appropriate degrees of freedom"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "Returns the confidence interval for a population mean, using a normal distribution"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "Returns the confidence interval for a population mean, using a normal distribution"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "Returns the confidence interval for a population mean, using a Student's T distribution"}, "CORREL": {"a": "(array1; array2)", "d": "Returns the correlation coefficient between two data sets"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "Counts the number of cells in a range that contain numbers"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "Counts the number of cells in a range that are not empty"}, "COUNTBLANK": {"a": "(range)", "d": "Counts the number of empty cells in a specified range of cells"}, "COUNTIF": {"a": "(range; criteria)", "d": "Counts the number of cells within a range that meet the given condition"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "Counts the number of cells specified by a given set of conditions or criteria"}, "COVAR": {"a": "(array1; array2)", "d": "Returns covariance, the average of the products of deviations for each data point pair in two data sets"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "Returns population covariance, the average of the products of deviations for each data point pair in two data sets"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "Returns sample covariance, the average of the products of deviations for each data point pair in two data sets"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Returns the smallest value for which the cumulative binomial distribution is greater than or equal to a criterion value"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "Returns the sum of squares of deviations of data points from their sample mean"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Returns the exponential distribution"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "Returns the exponential distribution"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Returns the (right-tailed) F probability distribution (degree of diversity) for two data sets"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Returns the inverse of the (right-tailed) F probability distribution: if p = FDIST(x,...), then FINV(p,...) = x"}, "FTEST": {"a": "(array1; array2)", "d": "Returns the result of an F-test, the two-tailed probability that the variances in Array1 and Array2 are not significantly different"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "Returns the (left-tailed) F probability distribution (degree of diversity) for two data sets"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Returns the (right-tailed) F probability distribution (degree of diversity) for two data sets"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Returns the inverse of the (left-tailed) F probability distribution: if p = F.DIST(x,...), then F.INV(p,...) = x"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Returns the inverse of the (right-tailed) F probability distribution: if p = F.DIST.RT(x,...), then F.INV.RT(p,...) = x"}, "F.TEST": {"a": "(array1; array2)", "d": "Returns the result of an F-test, the two-tailed probability that the variances in Array1 and Array2 are not significantly different"}, "FISHER": {"a": "(x)", "d": "Returns the Fisher transformation"}, "FISHERINV": {"a": "(y)", "d": "Returns the inverse of the Fisher transformation: if y = FISHER(x), then FISHERINV(y) = x"}, "FORECAST": {"a": "(x; known_y's; known_x's)", "d": "Calculates, or predicts, a future value along a linear trend by using existing values"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Returns the forecasted value for a specific future target date using exponential smoothing method."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Returns a confidence interval for the forecast value at the specified target date."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Returns the length of the repetitive pattern an application detects for the specified time series."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Returns the requested statistic for the forecast."}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "Calculates, or predicts, a future value along a linear trend by using existing values"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "Calculates how often values occur within a range of values and then returns a vertical array of numbers that have one more element than Bins_array"}, "GAMMA": {"a": "(x)", "d": "Returns the Gamma function value"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the gamma distribution"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the gamma distribution"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Returns the inverse of the gamma cumulative distribution: if p = GAMMADIST(x,...), then GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "Returns the inverse of the gamma cumulative distribution: if p = GAMMA.DIST(x,...), then GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Returns the natural logarithm of the gamma function"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returns the natural logarithm of the gamma function"}, "GAUSS": {"a": "(x)", "d": "Returns 0.5 less than the standard normal cumulative distribution"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "Returns the geometric mean of an array or range of positive numeric data"}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Returns numbers in an exponential growth trend matching known data points"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "Returns the harmonic mean of a data set of positive numbers: the reciprocal of the arithmetic mean of reciprocals"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "Returns the hypergeometric distribution"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "Returns the hypergeometric distribution"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Calculates the point at which a line will intersect the y-axis by using a best-fit regression line plotted through the known x-values and y-values"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "Returns the kurtosis of a data set"}, "LARGE": {"a": "(array; k)", "d": "Returns the k-th largest value in a data set. For example, the fifth largest number"}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Returns statistics that describe a linear trend matching known data points, by fitting a straight line using the least squares method"}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Returns statistics that describe an exponential curve matching known data points"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the lognormal cumulative distribution function of x, where ln(x) is normally distributed with parameters Mean and Standard_dev"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Returns the lognormal distribution of x, where ln(x) is normally distributed with parameters Mean and Standard_dev"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the lognormal cumulative distribution function of x, where ln(x) is normally distributed with parameters Mean and Standard_dev"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Returns the cumulative lognormal distribution of x, where ln(x) is normally distributed with parameters Mean and Standard_dev"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "Returns the largest value in a set of values. Ignores logical values and text"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "Returns the largest value in a set of values. Does not ignore logical values and text"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Returns the maximum value among cells specified by a given set of conditions or criteria"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "Returns the median, or the number in the middle of the set of given numbers"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "Returns the smallest number in a set of values. Ignores logical values and text"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "Returns the smallest value in a set of values. Does not ignore logical values and text"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Returns the minimum value among cells specified by a given set of conditions or criteria"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Returns the most frequently occurring, or repetitive, value in an array or range of data"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "Returns a vertical array of the most frequently occurring, or repetitive, values in an array or range of data. For a horizontal array, use =TRANSPOSE(MODE.MULT(number1,number2,...))"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "Returns the most frequently occurring, or repetitive, value in an array or range of data"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "Returns the negative binomial distribution, the probability that there will be Number_f failures before the Number_s-th success, with Probability_s probability of a success"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "Returns the negative binomial distribution, the probability that there will be Number_f failures before the Number_s-th success, with Probability_s probability of a success"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Returns the normal distribution for the specified mean and standard deviation"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Returns the normal cumulative distribution for the specified mean and standard deviation"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the normal cumulative distribution for the specified mean and standard deviation"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the normal cumulative distribution for the specified mean and standard deviation"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "Returns the standard normal distribution (has a mean of zero and a standard deviation of one)"}, "NORMSDIST": {"a": "(z)", "d": "Returns the standard normal cumulative distribution (has a mean of zero and a standard deviation of one)"}, "NORM.S.INV": {"a": "(probability)", "d": "Returns the inverse of the standard normal cumulative distribution (has a mean of zero and a standard deviation of one)"}, "NORMSINV": {"a": "(probability)", "d": "Returns the inverse of the standard normal cumulative distribution (has a mean of zero and a standard deviation of one)"}, "PEARSON": {"a": "(array1; array2)", "d": "Returns the Pearson product moment correlation coefficient, r"}, "PERCENTILE": {"a": "(array; k)", "d": "Returns the k-th percentile of values in a range"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "Returns the k-th percentile of values in a range, where k is in the range 0..1, exclusive"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "Returns the k-th percentile of values in a range, where k is in the range 0..1, inclusive"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Returns the rank of a value in a data set as a percentage of the data set"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "Returns the rank of a value in a data set as a percentage of the data set as a percentage (0..1, exclusive) of the data set"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "Returns the rank of a value in a data set as a percentage of the data set as a percentage (0..1, inclusive) of the data set"}, "PERMUT": {"a": "(number; number_chosen)", "d": "Returns the number of permutations for a given number of objects that can be selected from the total objects"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "Returns the number of permutations for a given number of objects (with repetitions) that can be selected from the total objects"}, "PHI": {"a": "(x)", "d": "Returns the value of the density function for a standard normal distribution"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "Returns the <PERSON><PERSON><PERSON> distribution"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "Returns the <PERSON><PERSON><PERSON> distribution"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "Returns the probability that values in a range are between two limits or equal to a lower limit"}, "QUARTILE": {"a": "(array; quart)", "d": "Returns the quartile of a data set"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "Returns the quartile of a data set, based on percentile values from 0..1, inclusive"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "Returns the quartile of a data set, based on percentile values from 0..1, exclusive"}, "RANK": {"a": "(number; ref; [order])", "d": "Returns the rank of a number in a list of numbers: its size relative to other values in the list"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "Returns the rank of a number in a list of numbers: its size relative to other values in the list; if more than one value has the same rank, the average rank is returned"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "Returns the rank of a number in a list of numbers: its size relative to other values in the list; if more than one value has the same rank, the top rank of that set of values is returned"}, "RSQ": {"a": "(known_y's; known_x's)", "d": "Returns the square of the Pearson product moment correlation coefficient through the given data points"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "Returns the skewness of a distribution: a characterisation of the degree of asymmetry of a distribution around its mean"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "Returns the skewness of a distribution based on a population: a characterisation of the degree of asymmetry of a distribution around its mean"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Returns the slope of the linear regression line through the given data points"}, "SMALL": {"a": "(array; k)", "d": "Returns the k-th smallest value in a data set. For example, the fifth smallest number"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "Returns a normalised value from a distribution characterised by a mean and standard deviation"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Estimates standard deviation based on a sample (ignores logical values and text in the sample)"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "Calculates standard deviation based on the entire population given as arguments (ignores logical values and text)"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "Estimates standard deviation based on a sample (ignores logical values and text in the sample)"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "Estimates standard deviation based on a sample, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "Calculates standard deviation based on the entire population given as arguments (ignores logical values and text)"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "Calculates standard deviation based on an entire population, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1"}, "STEYX": {"a": "(known_y's; known_x's)", "d": "Returns the standard error of the predicted y-value for each x in a regression"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "Returns the Student's t-distribution"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Returns the two-tailed inverse of the Student's t-distribution"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Returns the left-tailed Student's t-distribution"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "Returns the two-tailed Student's t-distribution"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "Returns the right-tailed Student's t-distribution"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "Returns the left-tailed inverse of the Student's t-distribution"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "Returns the two-tailed inverse of the Student's t-distribution"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "Returns the probability associated with a Student's t-Test"}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Returns numbers in a linear trend matching known data points, using the least squares method"}, "TRIMMEAN": {"a": "(array; percent)", "d": "Returns the mean of the interior portion of a set of data values"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "Returns the probability associated with a Student's t-Test"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "Estimates variance based on a sample (ignores logical values and text in the sample)"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "Calculates variance based on the entire population (ignores logical values and text in the population)"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "Estimates variance based on a sample (ignores logical values and text in the sample)"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "Estimates variance based on a sample, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "Calculates variance based on the entire population (ignores logical values and text in the population)"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "Calculates variance based on the entire population, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the <PERSON><PERSON> distribution"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the <PERSON><PERSON> distribution"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "Returns the one-tailed P-value of a z-test"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Returns the one-tailed P-value of a z-test"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "Returns the accrued interest for a security that pays periodic interest."}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "Returns the accrued interest for a security that pays interest at maturity"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Returns the prorated linear depreciation of an asset for each accounting period."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Returns the prorated linear depreciation of an asset for each accounting period."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of days from the beginning of the coupon period to the settlement date"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of days in the coupon period that contains the settlement date"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of days from the settlement date to the next coupon date"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the next coupon date after the settlement date"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of coupons payable between the settlement date and maturity date"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the previous coupon date before the settlement date"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Returns the cumulative interest paid between two periods"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Returns the cumulative principal paid on a loan between two periods"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "Returns the depreciation of an asset for a specified period using the fixed-declining balance method"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "Returns the depreciation of an asset for a specified period using the double-declining balance method or some other method you specify"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Returns the discount rate for a security"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "Converts a dollar price, expressed as a fraction, into a dollar price, expressed as a decimal number"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "Converts a dollar price, expressed as a decimal number, into a dollar price, expressed as a fraction"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Returns the annual duration of a security with periodic interest payments"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "Returns the effective annual interest rate"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Returns the future value of an investment based on periodic, constant payments and a constant interest rate"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "Returns the future value of an initial principal after applying a series of compound interest rates"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "Returns the interest rate for a fully invested security"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Returns the interest payment for a given period for an investment, based on periodic, constant payments and a constant interest rate"}, "IRR": {"a": "(values; [guess])", "d": "Returns the internal rate of return for a series of cash flows"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Returns the interest paid during a specific period of an investment"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Returns the <PERSON>ley modified duration for a security with an assumed par value of $100"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Returns the internal rate of return for a series of periodic cash flows, considering both cost of investment and interest on reinvestment of cash"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "Returns the annual nominal interest rate"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Returns the number of periods for an investment based on periodic, constant payments and a constant interest rate"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Returns the net present value of an investment based on a discount rate and a series of future payments (negative values) and income (positive values)"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "Returns the price per $100 face value of a security with an odd first period"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "Returns the yield of a security with an odd first period"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "Returns the price per $100 face value of a security with an odd last period"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "Returns the yield of a security with an odd last period"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "Returns the number of periods required by an investment to reach a specified value"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "Calculates the payment for a loan based on constant payments and a constant interest rate"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Returns the payment on the principal for a given investment based on periodic, constant payments and a constant interest rate"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "Returns the price per $100 face value of a security that pays periodic interest"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "Returns the price per $100 face value of a discounted security"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "Returns the price per $100 face value of a security that pays interest at maturity"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Returns the present value of an investment: the total amount that a series of future payments is worth now"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "Returns the interest rate per period of a loan or an investment. For example, use 6%/4 for quarterly payments at 6% APR"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "Returns the amount received at maturity for a fully invested security"}, "RRI": {"a": "(nper; pv; fv)", "d": "Returns an equivalent interest rate for the growth of an investment"}, "SLN": {"a": "(cost; salvage; life)", "d": "Returns the straight-line depreciation of an asset for one period"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "Returns the sum-of-years' digits depreciation of an asset for a specified period"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "Returns the bond-equivalent yield for a treasury bill"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "Returns the price per $100 face value for a treasury bill"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "Returns the yield for a treasury bill"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "Returns the depreciation of an asset for any period you specify, including partial periods, using the double-declining balance method or some other method you specify"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Returns the internal rate of return for a schedule of cash flows"}, "XNPV": {"a": "(rate; values; dates)", "d": "Returns the net present value for a schedule of cash flows"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "Returns the yield on a security that pays periodic interest"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Returns the annual yield for a discounted security. For example, a treasury bill"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "Returns the annual yield of a security that pays interest at maturity"}, "ABS": {"a": "(number)", "d": "Returns the absolute value of a number, a number without its sign"}, "ACOS": {"a": "(number)", "d": "Returns the arccosine of a number, in radians in the range 0 to Pi. The arccosine is the angle whose cosine is Number"}, "ACOSH": {"a": "(number)", "d": "Returns the inverse hyperbolic cosine of a number"}, "ACOT": {"a": "(number)", "d": "Returns the arccotangent of a number, in radians in the range 0 to Pi."}, "ACOTH": {"a": "(number)", "d": "Returns the inverse hyperbolic cotangent of a number"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "Returns an aggregate in a list or database"}, "ARABIC": {"a": "(text)", "d": "Converts a Roman numeral to Arabic"}, "ASC": {"a": "( text )", "d": "For Double-byte character set (DBCS) languages, the function changes full-width (double-byte) characters to half-width (single-byte) characters"}, "ASIN": {"a": "(number)", "d": "Returns the arcsine of a number in radians, in the range -Pi/2 to Pi/2"}, "ASINH": {"a": "(number)", "d": "Returns the inverse hyperbolic sine of a number"}, "ATAN": {"a": "(number)", "d": "Returns the arctangent of a number in radians, in the range -Pi/2 to Pi/2"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Returns the arctangent of the specified x and y coordinates, in radians between -Pi and Pi, excluding -Pi"}, "ATANH": {"a": "(number)", "d": "Returns the inverse hyperbolic tangent of a number"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Converts a number into a text representation with the given radix (base)"}, "CEILING": {"a": "(number; significance)", "d": "Rounds a number up, to the nearest multiple of significance"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Rounds a number up, to the nearest integer or to the nearest multiple of significance"}, "CEILING.PRECISE": {"a": "( x; [significance] )", "d": "Returns a number that is rounded up to the nearest integer or to the nearest multiple of significance"}, "COMBIN": {"a": "(number; number_chosen)", "d": "Returns the number of combinations for a given number of items"}, "COMBINA": {"a": "(number; number_chosen)", "d": "Returns the number of combinations with repetitions for a given number of items"}, "COS": {"a": "(number)", "d": "Returns the cosine of an angle"}, "COSH": {"a": "(number)", "d": "Returns the hyperbolic cosine of a number"}, "COT": {"a": "(number)", "d": "Returns the cotangent of an angle"}, "COTH": {"a": "(number)", "d": "Returns the hyperbolic cotangent of a number"}, "CSC": {"a": "(number)", "d": "Returns the cosecant of an angle"}, "CSCH": {"a": "(number)", "d": "Returns the hyperbolic cosecant of an angle"}, "DECIMAL": {"a": "(number; radix)", "d": "Converts a text representation of a number in a given base into a decimal number"}, "DEGREES": {"a": "(angle)", "d": "Converts radians to degrees"}, "ECMA.CEILING": {"a": "( x; significance )", "d": "Rounds the number up to the nearest multiple of significance"}, "EVEN": {"a": "(number)", "d": "Rounds a positive number up and negative number down to the nearest even integer"}, "EXP": {"a": "(number)", "d": "Returns e raised to the power of a given number"}, "FACT": {"a": "(number)", "d": "Returns the factorial of a number, equal to 1*2*3*...* Number"}, "FACTDOUBLE": {"a": "(number)", "d": "Returns the double factorial of a number"}, "FLOOR": {"a": "(number; significance)", "d": "Rounds a number down to the nearest multiple of significance"}, "FLOOR.PRECISE": {"a": "( x; [significance] )", "d": "Returns a number that is rounded down to the nearest integer or to the nearest multiple of significance"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Rounds a number down, to the nearest integer or to the nearest multiple of significance"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "Returns the greatest common divisor"}, "INT": {"a": "(number)", "d": "Rounds a number down to the nearest integer"}, "ISO.CEILING": {"a": "( number; [significance] )", "d": "Returns a number that is rounded up to the nearest integer or to the nearest multiple of significance regardless of the sign of the number. However, if the number or the significance is zero, zero is returned."}, "LCM": {"a": "(number1; [number2]; ...)", "d": "Returns the least common multiple"}, "LN": {"a": "(number)", "d": "Returns the natural logarithm of a number"}, "LOG": {"a": "(number; [base])", "d": "Returns the logarithm of a number to the base you specify"}, "LOG10": {"a": "(number)", "d": "Returns the base-10 logarithm of a number"}, "MDETERM": {"a": "(array)", "d": "Returns the matrix determinant of an array"}, "MINVERSE": {"a": "(array)", "d": "Returns the inverse matrix for the matrix stored in an array"}, "MMULT": {"a": "(array1; array2)", "d": "Returns the matrix product of two arrays, an array with the same number of rows as array1 and columns as array2"}, "MOD": {"a": "(number; divisor)", "d": "Returns the remainder after a number is divided by a divisor"}, "MROUND": {"a": "(number; multiple)", "d": "Returns a number rounded to the desired multiple"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "Returns the multinomial of a set of numbers"}, "MUNIT": {"a": "(dimension)", "d": "Returns the unit matrix for the specified dimension"}, "ODD": {"a": "(number)", "d": "Rounds a positive number up and negative number down to the nearest odd integer"}, "PI": {"a": "()", "d": "Returns the value of Pi, 3.14159265358979, accurate to 15 digits"}, "POWER": {"a": "(number; power)", "d": "Returns the result of a number raised to a power"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "Multiplies all the numbers given as arguments"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "Returns the integer portion of a division"}, "RADIANS": {"a": "(angle)", "d": "Converts degrees to radians"}, "RAND": {"a": "()", "d": "Returns a random number greater than or equal to 0 and less than 1, evenly distributed (changes on recalculation)"}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "Returns an array of random numbers"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "Returns a random number between the numbers you specify"}, "ROMAN": {"a": "(number; [form])", "d": "Converts an Arabic numeral to Roman, as text"}, "ROUND": {"a": "(number; num_digits)", "d": "Rounds a number to a specified number of digits"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "Rounds a number down, towards zero"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "Rounds a number up, away from zero"}, "SEC": {"a": "(number)", "d": "Returns the secant of an angle"}, "SECH": {"a": "(number)", "d": "Returns the hyperbolic secant of an angle"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "Returns the sum of a power series based on the formula"}, "SIGN": {"a": "(number)", "d": "Returns the sign of a number: 1 if the number is positive, zero if the number is zero, or -1 if the number is negative"}, "SIN": {"a": "(number)", "d": "Returns the sine of an angle"}, "SINH": {"a": "(number)", "d": "Returns the hyperbolic sine of a number"}, "SQRT": {"a": "(number)", "d": "Returns the square root of a number"}, "SQRTPI": {"a": "(number)", "d": "Returns the square root of (number * Pi)"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "Returns a subtotal in a list or database"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "Adds all the numbers in a range of cells"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "Adds the cells specified by a given condition or criteria"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "Adds the cells specified by a given set of conditions or criteria"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "Returns the sum of the products of corresponding ranges or arrays"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "Returns the sum of the squares of the arguments. The arguments can be numbers, arrays, names or references to cells that contain numbers"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "Sums the differences between the squares of two corresponding ranges or arrays"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "Returns the sum total of the sums of squares of numbers in two corresponding ranges or arrays"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "Sums the squares of the differences in two corresponding ranges or arrays"}, "TAN": {"a": "(number)", "d": "Returns the tangent of an angle"}, "TANH": {"a": "(number)", "d": "Returns the hyperbolic tangent of a number"}, "TRUNC": {"a": "(number; [num_digits])", "d": "Truncates a number to an integer by removing the decimal, or fractional, part of the number"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "Creates a cell reference as text, given specified row and column numbers"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "Chooses a value or action to perform from a list of values, based on an index number"}, "COLUMN": {"a": "([reference])", "d": "Returns the column number of a reference"}, "COLUMNS": {"a": "(array)", "d": "Returns the number of columns in an array or reference"}, "FORMULATEXT": {"a": "(reference)", "d": "Returns a formula as a string"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "Looks for a value in the top row of a table or array of values and returns the value in the same column from a row you specify"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "Creates a shortcut or jump that opens a document stored on your hard drive, a network server, or on the Internet"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "Returns a value or reference of the cell at the intersection of a particular row and column, in a given range"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "Returns the reference specified by a text string"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "Looks up a value either from a one-row or one-column range or from an array. Provided for backwards compatibility"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "Returns the relative position of an item in an array that matches a specified value in a specified order"}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "Returns a reference to a range that is a given number of rows and columns from a given reference"}, "ROW": {"a": "([reference])", "d": "Returns the row number of a reference"}, "ROWS": {"a": "(array)", "d": "Returns the number of rows in a reference or array"}, "TRANSPOSE": {"a": "(array)", "d": "Converts a vertical range of cells to a horizontal range, or vice versa"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "Returns the unique values from a range or array."}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "Looks for a value in the leftmost column of a table and then returns a value in the same row from a column that you specify. By default, the table must be sorted in an ascending order"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "Searches a range or an array for a match and returns the corresponding item from a second range or array. By default, an exact match is used"}, "CELL": {"a": "(info_type; [reference])", "d": "Returns information about the formatting, location, or contents of a cell"}, "ERROR.TYPE": {"a": "(error_val)", "d": "Returns a number matching an error value."}, "ISBLANK": {"a": "(value)", "d": "Checks whether a reference is to an empty cell, and returns TRUE or FALSE"}, "ISERR": {"a": "(value)", "d": "Checks whether a value is an error other than #N/A, and returns TRUE or FALSE"}, "ISERROR": {"a": "(value)", "d": "Checks whether a value is an error, and returns TRUE or FALSE"}, "ISEVEN": {"a": "(number)", "d": "Returns TRUE if the number is even"}, "ISFORMULA": {"a": "(reference)", "d": "Checks whether a reference is to a cell containing a formula, and returns TRUE or FALSE"}, "ISLOGICAL": {"a": "(value)", "d": "Checks whether a value is a logical value (TRUE or FALSE), and returns TRUE or FALSE"}, "ISNA": {"a": "(value)", "d": "Checks whether a value is #N/A, and returns TRUE or FALSE"}, "ISNONTEXT": {"a": "(value)", "d": "Checks whether a value is not text (blank cells are not text), and returns TRUE or FALSE"}, "ISNUMBER": {"a": "(value)", "d": "Checks whether a value is a number, and returns TRUE or FALSE"}, "ISODD": {"a": "(number)", "d": "Returns TRUE if the number is odd"}, "ISREF": {"a": "(value)", "d": "Checks whether a value is a reference, and returns TRUE or FALSE"}, "ISTEXT": {"a": "(value)", "d": "Checks whether a value is text, and returns TRUE or FALSE"}, "N": {"a": "(value)", "d": "Converts non-number value to a number, dates to serial numbers, TRUE to 1, anything else to 0 (zero)"}, "NA": {"a": "()", "d": "Returns the error value #N/A (value not available)"}, "SHEET": {"a": "([value])", "d": "Returns the sheet number of the referenced sheet"}, "SHEETS": {"a": "([reference])", "d": "Returns the number of sheets in a reference"}, "TYPE": {"a": "(value)", "d": "Returns an integer representing the data type of a value: number = 1; text = 2; logical value = 4; error value = 16; array = 64; compound data = 128"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "Checks whether all arguments are TRUE, and returns TRUE if all arguments are TRUE"}, "FALSE": {"a": "()", "d": "Returns the logical value FALSE"}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "Checks whether a condition is met, and returns one value if TRUE, and another value if FALSE"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "Checks whether one or more conditions are met and returns a value corresponding to the first TRUE condition"}, "IFERROR": {"a": "(value; value_if_error)", "d": "Returns value_if_error if expression is an error and the value of the expression itself otherwise"}, "IFNA": {"a": "(value; value_if_na)", "d": "Returns the value you specify if the expression resolves to #N/A, otherwise returns the result of the expression"}, "NOT": {"a": "(logical)", "d": "Changes FALSE to TRUE, or TRUE to FALSE"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "Checks whether any of the arguments are TRUE, and returns TRUE or FALSE. Returns FALSE only if all arguments are FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "Evaluates an expression against a list of values and returns the result corresponding to the first matching value. If there is no match, an optional default value is returned"}, "TRUE": {"a": "()", "d": "Returns the logical value TRUE"}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "Returns a logical 'Exclusive Or' of all arguments"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returns text that’s before delimiting characters."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returns text that’s after delimiting characters."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Splits text into rows or columns using delimiters."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Wraps a row or column vector after a specified number of values."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Vertically stacks arrays into one array."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Horizontally stacks arrays into one array."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Returns rows from an array or reference."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Returns columns from an array or reference."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Returns the array as one column."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Returns the array as one row."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Wraps a row or column vector after a specified number of values."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Returns rows or columns from array start or end."}, "DROP": {"a": "(array, rows, [columns])", "d": "Drops rows or columns from array start or end."}}