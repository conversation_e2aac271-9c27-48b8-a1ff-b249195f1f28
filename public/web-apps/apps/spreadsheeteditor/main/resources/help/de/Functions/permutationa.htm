﻿<!DOCTYPE html>
<html>
	<head>
		<title>VARIATIONEN2-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>VARIATIONEN2-Funktion</h1>
			<p>Die Funktion <b>VARIATIONEN2</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt die Anzahl der Permutationen für eine angegebene Anzahl von Objekten zurück (mit Wiederholungen), die aus der Gesamtmenge der Objekte ausgewählt werden können.</p>
			<p>Die Formelsyntax der Funktion <b>VARIATIONEN2</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>VARIATIONEN2(Zahl;gewählte_Zahl)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
				<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist eine ganze Zahl zur Angabe der Gesamtzahl von Objekten. Ein nummerischer Wert, der größer oder gleich 0 ist.</p> 
				<p style="text-indent: 50px;"><b><em>gewählte_Zahl</em></b> ist eine ganze Zahl zur Angabe der Anzahl von Objekten in jeder Permutation. Ein nummerischer Wert, der größer oder gleich 0 ist und kleiner als <b><em>Zahl</em></b>.</p> 
				<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>VARIATIONEN2</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>VARIATIONEN2</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="VARIATIONEN2-Funktion" src="../images/permutationa.png" /></p>
		</div>
	</body>
</html>