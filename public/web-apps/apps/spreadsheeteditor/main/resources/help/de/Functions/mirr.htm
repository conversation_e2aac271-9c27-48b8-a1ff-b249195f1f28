﻿<!DOCTYPE html>
<html>
	<head>
		<title>QIKV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>QIKV-Funktion</h1>
			<p>Die Funktion <b>QIKV</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie gibt einen modifizierten internen Zinsfuß zurück, bei dem positive und negative Cashflows mit unterschiedlichen Zinssätzen finanziert werden.</p>
			<p>Die Formelsyntax der Funktion <b>QIKV</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>QIKV(Werte;Investition;Reinvestition)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Werte</em></b> ist eine Matrix oder ein Bezug auf Zellen, in denen die Zahlen stehen, für die Sie den internen Zinsfuß berechnen möchten. Mindestens einer der Werte muss negativ und mindestens einer muss positiv sein.</p> 
				<p style="text-indent: 50px;"><b><em>Investition</em></b> ist der Zinssatz, den Sie für die gezahlten Gelder ansetzen.</p> 
				<p style="text-indent: 50px;"><b><em>Reinvestition</em></b> ist der Zinssatz, den Sie für reinvestierte Gelder erzielen.</p> 
			
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>QIKV</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>QIKV</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="QIKV-Funktion" src="../images/mirr.png" /></p>
		</div>
	</body>
</html>