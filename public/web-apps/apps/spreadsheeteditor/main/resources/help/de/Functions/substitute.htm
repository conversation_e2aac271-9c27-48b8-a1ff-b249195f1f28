﻿<!DOCTYPE html>
<html>
	<head>
		<title>WECHSELN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WECHSELN-Funktion</h1>
			<p>Die Funktion <b>WECHSELN</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie ersetzt eine vorliegenden Zeichenfolge durch neuen Text.</p>
			<p>Die Formelsyntax der Funktion <b>WECHSELN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>WECHSELN(Text;Alter_Text;Neuer_Text;[ntes_Auftreten])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Text</em></b> ist Text oder der Bezug auf eine Zelle, die den Text enthält, in dem Zeichen ausgetauscht werden sollen.</p> 
			<p style="text-indent: 50px;"><b><em>Alter_Text</em></b> ist der Text, den Sie ersetzen möchten.</p> 
			<p style="text-indent: 50px;"><b><em>Neuer_Text</em></b> ist der Text, durch den Sie den alten Text ersetzen möchten.</p> 
			<p style="text-indent: 50px;"><b><em>ntes_Auftreten</em></b> gibt an, an welchen Stellen im alten Text durch neuen Text ersetzt werden sollen Dieses Argument ist optional. Wird es ausgelassen, ersetzt die Funktion alle Vorkommen im <b><em>Text</em></b>.</p>
			<p>Die Daten können manuell eingegeben werden oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>WECHSELN</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WECHSELN</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="WECHSELN-Funktion" src="../images/substitute.png" /></p>
		</div>
	</body>
</html>