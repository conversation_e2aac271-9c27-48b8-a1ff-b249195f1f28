﻿<!DOCTYPE html>
<html>
	<head>
		<title>Kommunikation in Echtzeit</title>
		<meta charset="utf-8" />
		<meta name="description" content="Tips on using the Chat tool" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type="text/css" rel="stylesheet" href="../../images/sprite.css" />
		<script type="text/javascript" src="../callback.js"></script>
		<script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Kommunikation in Echtzeit</h1>
			<p>Der <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beibehalten: Sie können die Dateien und Ordner <a href="https://helpcenter.onlyoffice.com/de/userguides/groups-guides-documents-share-documents.aspx" onclick="onhyperlinkclick(this)">freigeben</a>; an Präsentationen in Echtzeit <a href="../HelpfulHints/CollaborativeEditing.htm" onclick="onhyperlinkclick(this)">zusammenarbeiten</a>; bestimmte Teile Ihrer Präsentationen, die zusätzliche Eingaben Dritter erfordern, <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">kommentieren</a>; die <a href="../HelpfulHints/VersionHistory.htm" onclick="onhyperlinkclick(this)">Versionen von Präsentationen</a> für die zukünftige Verwendung speichern.</p>
			<p>Im <b>Präsentationseditor</b> können Sie mit Ihren Mitbearbeitern in Echtzeit kommunizieren, indem Sie das integrierte <b>Chat</b>-Tool sowie eine Reihe nützlicher Plugins verwenden, z. B. <a href="../UsageInstructions/CommunicationPlugins.htm" onclick="onhyperlinkclick(this)">Telegram oder Rainbow</a>.</p>
			<p>Um auf das <b>Chat</b>-Tool zuzugreifen und eine Nachricht für andere Benutzer zu hinterlassen:</p>
			<ol>
				<li>Klicken Sie auf das Symbol <div class="icon icon-chaticon"></div> in der linken Symbolleiste.</li>
				<li>Geben Sie Ihren Text in das entsprechende Feld unten ein.</li>
				<li>Klicken Sie die Schaltfläche <b>Senden</b>.</li>
			</ol>
			<p class="note">Die Chat-Nachrichten werden nur während einer Sitzung gespeichert. Um den Inhalt der Präsentation zu diskutieren, verwenden Sie besser <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">Kommentare</a>, die bis zu ihrer Löschung gespeichert werden.</p>
			<p>Alle von Benutzern hinterlassenen Nachrichten werden auf der linken Seite angezeigt. Wenn es neue Nachrichten gibt, die Sie noch nicht gelesen haben, sieht das Chat-Symbol so aus - <span class="icon icon-chaticon_new"></span>.</p>
			<p>Um das Fenster mit Chat-Nachrichten zu schließen, klicken Sie erneut auf das Symbol <span class="icon icon-chaticon"></span>.</p>
		</div>
	</body>
</html>