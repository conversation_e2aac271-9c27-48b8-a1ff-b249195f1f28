﻿<!DOCTYPE html>
<html>
	<head>
		<title>T-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>T-Funktion</h1>
			<p>Die Funktion <b>T</b> gehört zur Gruppe der Text- und Datenfunktionen. Mit der Funktion lässt sich prüfen, ob der Wert in der Zelle (oder als Argument verwendet) Text ist oder nicht. Liegt kein Text vor, gibt die Funktion eine leere Zeichenfolge zurück. Liegt Text vor, gibt die Funktion den tatsächlichen Wert zurück.</p>
			<p>Die Formelsyntax der Funktion <b>T</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>T(Wert)</em></b></p> 
			<p><b><em>Wert</em></b> ist der zu testende Wert, der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>T</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>T</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p><em>Beispiel:</em></p>
			<p>Es liegt das folgende Argument vor: <em>Wert</em> = <b>A1</b>, dabei gilt <b>A1</b> ist <b>Datum und Zeit</b>. Also gibt die Funktion den <b>Datum und Zeit</b> zurück.</p>
			<p style="text-indent: 150px;"><img alt="T-Funktion: Text" src="../images/ttext.png" /></p>
			<p>Ändert man die Daten in <b>A1</b> in einen Zahlenwert, gibt die Funktion eine leere Zeichenfolge zurück.</p>
			<p style="text-indent: 150px;"><img alt="T-Funktion: Zahlenwert" src="../images/tnumber.png" /></p>
		</div>
	</body>
</html>