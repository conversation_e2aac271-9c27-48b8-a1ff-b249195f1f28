var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "About Presentation Editor", 
        "body": "Presentation Editor is an online application that lets you look through and edit presentations directly in your browser . Using Presentation Editor, you can perform various editing operations like in any desktop editor, print the edited presentations keeping all the formatting details or download them onto your computer hard disk drive as PPTX, PDF, ODP, POTX, PDF/A, OTP files. To view the current software version and licensor details in the online version, click the icon at the left sidebar. To view the current software version and licensor details in the desktop version, select the About menu item at the left sidebar of the main program window."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Advanced Settings of Presentation Editor", 
        "body": "Presentation Editor lets you change its advanced settings. To access them, open the File tab at the top toolbar and select the Advanced Settings... option. You can also click the View settings icon on the right side of the editor header and select the Advanced settings option. The advanced settings are: Spell Checking is used to turn on/off the spell checking option. Alternate Input is used to turn on/off hieroglyphs. Alignment Guides is used to turn on/off alignment guides that appear when you move objects and allow you to position them on the slide precisely. Autosave is used in the online version to turn on/off automatic saving of changes you make while editing. Autorecover - is used in the desktop version to turn on/off the option that allows to automatically recover documents in case of the unexpected program closing. Co-editing Mode is used to select the display of the changes made during the co-editing: By default the Fast mode is selected, the users who take part in the document co-editing will see the changes in real time once they are made by other users. If you prefer not to see other user changes (so that they do not disturb you, or for some other reason), select the Strict mode and all the changes will be shown only after you click the Save icon notifying you that there are changes from other users. Default Zoom Value is used to set the default zoom value selecting it in the list of available options from 50% to 200%. You can also choose the Fit to Slide or Fit to Width option. Font Hinting is used to select the type a font is displayed in Presentation Editor: Choose As Windows if you like the way fonts are usually displayed on Windows, i.e. using Windows font hinting. Choose As OS X if you like the way fonts are usually displayed on a Mac, i.e. without any font hinting at all. Choose Native if you want your text to be displayed with the hinting embedded into font files. Default cache mode - used to select the cache mode for the font characters. It’s not recommended to switch it without any reason. It can be helpful in some cases only, for example, when an issue in the Google Chrome browser with the enabled hardware acceleration occurs. Presentation Editor has two cache modes: In the first cache mode, each letter is cached as a separate picture. In the second cache mode, a picture of a certain size is selected where letters are placed dynamically and a mechanism of allocating/removing memory in this picture is also implemented. If there is not enough memory, a second picture is created, etc. The Default cache mode setting applies two above mentioned cache modes separately for different browsers: When the Default cache mode setting is enabled, Internet Explorer (v. 9, 10, 11) uses the second cache mode, other browsers use the first cache mode. When the Default cache mode setting is disabled, Internet Explorer (v. 9, 10, 11) uses the first cache mode, other browsers use the second cache mode. Unit of Measurement is used to specify what units are used on the rulers and in properties windows for measuring elements parameters such as width, height, spacing, margins etc. You can select the Centimeter, Point, or Inch option. To save the changes you made, click the Apply button."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Collaborative Presentation Editing", 
        "body": "Presentation Editor offers you the possibility to work at a presentation collaboratively with other users. This feature includes: simultaneous multi-user access to the edited presentation visual indication of objects that are being edited by other users real-time changes display or synchronization of changes with one button click chat to share ideas concerning particular presentation parts comments containing the description of a task or problem that should be solved (it's also possible to work with comments in the offline mode, without connecting to the online version) Connecting to the online version In the desktop editor, open the Connect to cloud option of the left-side menu in the main program window. Connect to your cloud office specifying your account login and password. Co-editing Presentation Editor allows to select one of the two available co-editing modes: Fast is used by default and shows the changes made by other users in real time. Strict is selected to hide other user changes until you click the Save icon to save your own changes and accept the changes made by others. The mode can be selected in the Advanced Settings. It's also possible to choose the necessary mode using the Co-editing Mode icon at the Collaboration tab of the top toolbar: Note: when you co-edit a presentation in the Fast mode, the possibility to Redo the last undone operation is not available. When a presentation is being edited by several users simultaneously in the Strict mode, the edited objects (autoshapes, text objects, tables, images, charts) are marked with dashed lines of different colors. The object that you are editing is surrounded by the green dashed line. Red dashed lines indicate that objects are being edited by other users. By hovering the mouse cursor over one of the edited passages, the name of the user who is editing it at the moment is displayed. The Fast mode will show the actions and the names of the co-editors once they are editing the text. The number of users who are working at the current presentation is specified on the right side of the editor header - . If you want to see who exactly are editing the file now, you can click this icon or open the Chat panel with the full list of the users. When no users are viewing or editing the file, the icon in the editor header will look like allowing you to manage the users who have access to the file right from the document: invite new users giving them permissions to edit, read or comment the presentation, or deny some users access rights to the file. Click this icon to manage the access to the file; this can be done both when there are no other users who view or co-edit the document at the moment and when there are other users and the icon looks like . It's also possible to set access rights using the Sharing icon at the Collaboration tab of the top toolbar. As soon as one of the users saves his/her changes by clicking the icon, the others will see a note within the status bar stating that they have updates. To save the changes you made, so that other users can view them, and get the updates saved by your co-editors, click the icon in the left upper corner of the top toolbar. The updates will be highlighted for you to check what exactly has been changed. Chat You can use this tool to coordinate the co-editing process on-the-fly, for example, to arrange with your collaborators about who is doing what, which paragraph you are going to edit now etc. The chat messages are stored during one session only. To discuss the document content it is better to use comments which are stored until you decide to delete them. To access the chat and leave a message for other users, click the icon at the left sidebar, or switch to the Collaboration tab of the top toolbar and click the Chat button, enter your text into the corresponding field below, press the Send button. All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - . To close the panel with chat messages, click the icon at the left sidebar or the Chat button at the top toolbar once again. Comments It's possible to work with comments in the offline mode, without connecting to the online version. To leave a comment to a certain object (text box, shape etc.): select an object where you think there is an error or problem, switch to the Insert or Collaboration tab of the top toolbar and click the Comment button, or right-click the selected object and select the Add Сomment option from the menu, enter the needed text, click the Add Comment/Add button. The object you commented will be marked with the icon. To view the comment, just click on this icon. To add a comment to a certain slide, select the slide and use the Comment button at the Insert or Collaboration tab of the top toolbar. The added comment will be displayed in the upper left corner of the slide. To create a presentation-level comment which is not related to a certain object or slide, click the icon at the left sidebar to open the Comments panel and use the Add Comment to Document link. The presentation-level comments can be viewed at the Comments panel. Comments related to objects and slides are also available here. Any other user can answer to the added comment asking questions or reporting on the work he/she has done. For this purpose, click the Add Reply link situated under the comment, type in your reply text in the entry field and press the Reply button. If you are using the Strict co-editing mode, new comments added by other users will become visible only after you click the icon in the left upper corner of the top toolbar. You can manage the added comments using the icons in the comment balloon or at the Comments panel on the left: edit the currently selected by clicking the icon, delete the currently selected by clicking the icon, close the currently selected discussion by clicking the icon if the task or problem you stated in your comment was solved, after that the discussion you opened with your comment gets the resolved status. To open it again, click the icon. Adding mentions When entering comments, you can use the mentions feature that allows to attract somebody's attention to the comment and send a notification to the mentioned user via email and Talk. To add a mention enter the \"+\" or \"@\" sign anywhere in the comment text - a list of the portal users will open. To simplify the search process, you can start typing a name in the comment field - the user list will change as you type. Select the necessary person from the list. If the file has not yet been shared with the mentioned user, the Sharing Settings window will open. Read only access type is selected by default. Change it if necessary and click OK. The mentioned user will receive an email notification that he/she has been mentioned in a comment. If the file has been shared, the user will also receive a corresponding notification. To remove comments, click the Remove button at the Collaboration tab of the top toolbar, select the necessary option from the menu: Remove Current Comments - to remove the currently selected comment. If some replies have beed added to the comment, all its replies will be removed as well. Remove My Comments - to remove comments you added without removing comments added by other users. If some replies have beed added to your comment, all its replies will be removed as well. Remove All Comments - to remove all the comments in the presentation that you and other users added. To close the panel with comments, click the icon at the left sidebar once again."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Keyboard Shortcuts", 
        "body": "Windows/LinuxMac OS Working with Presentation Open 'File' panel Alt+F ⌥ Option+F Open the File panel to save, download, print the current presentation, view its info, create a new presentation or open an existing one, access Presentation Editor help or advanced settings. Open 'Search' dialog box Ctrl+F ^ Ctrl+F, &#8984; Cmd+F Open the Search dialog box to start searching for a character/word/phrase in the currently edited presentation. Open 'Comments' panel Ctrl+⇧ Shift+H ^ Ctrl+⇧ Shift+H, &#8984; Cmd+⇧ Shift+H Open the Comments panel to add your own comment or reply to other users' comments. Open comment field Alt+H ⌥ Option+H Open a data entry field where you can add the text of your comment. Open 'Chat' panel Alt+Q ⌥ Option+Q Open the Chat panel and send a message. Save presentation Ctrl+S ^ Ctrl+S, &#8984; Cmd+S Save all the changes to the presentation currently edited with Presentation Editor. The active file will be saved with its current file name, location, and file format. Print presentation Ctrl+P ^ Ctrl+P, &#8984; Cmd+P Print the presentation with one of the available printers or save it to a file. Download As... Ctrl+⇧ Shift+S ^ Ctrl+⇧ Shift+S, &#8984; Cmd+⇧ Shift+S Open the Download as... panel to save the currently edited presentation to the computer hard disk drive in one of the supported formats: PPTX, PDF, ODP, POTX, PDF/A, OTP. Full screen F11 Switch to the full screen view to fit Presentation Editor into your screen. Help menu F1 F1 Open Presentation Editor Help menu. Open existing file (Desktop Editors) Ctrl+O On the Open local file tab in Desktop Editors, opens the standard dialog box that allows to select an existing file. Close file (Desktop Editors) Ctrl+W, Ctrl+F4 ^ Ctrl+W, &#8984; Cmd+W Close the current presentation window in Desktop Editors. Element contextual menu ⇧ Shift+F10 ⇧ Shift+F10 Open the selected element contextual menu. Navigation The first slide Home Home, Fn+← Go to the first slide of the currently edited presentation. The last slide End End, Fn+→ Go to the last slide of the currently edited presentation. Next slide Page Down Page Down, Fn+↓ Go to the next slide of the currently edited presentation. Previous slide Page Up Page Up, Fn+↑ Go to the previous slide of the currently edited presentation. Zoom In Ctrl++ ^ Ctrl+=, &#8984; Cmd+= Zoom in the currently edited presentation. Zoom Out Ctrl+- ^ Ctrl+-, &#8984; Cmd+- Zoom out the currently edited presentation. Performing Actions on Slides New slide Ctrl+M ^ Ctrl+M Create a new slide and add it after the selected one in the list. Duplicate slide Ctrl+D &#8984; Cmd+D Duplicate the selected slide in the list. Move slide up Ctrl+↑ &#8984; Cmd+↑ Move the selected slide above the previous one in the list. Move slide down Ctrl+↓ &#8984; Cmd+↓ Move the selected slide below the following one in the list. Move slide to beginning Ctrl+⇧ Shift+↑ &#8984; Cmd+⇧ Shift+↑ Move the selected slide to the very first position in the list. Move slide to end Ctrl+⇧ Shift+↓ &#8984; Cmd+⇧ Shift+↓ Move the selected slide to the very last position in the list. Performing Actions on Objects Create a copy Ctrl + drag, Ctrl+D ^ Ctrl + drag, ^ Ctrl+D, &#8984; Cmd+D Hold down the Ctrl key when dragging the selected object or press Ctrl+D (&#8984; Cmd+D for Mac) to create its copy. Group Ctrl+G &#8984; Cmd+G Group the selected objects. Ungroup Ctrl+⇧ Shift+G &#8984; Cmd+⇧ Shift+G Ungroup the selected group of objects. Select the next object ↹ Tab ↹ Tab Select the next object after the currently selected one. Select the previous object ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Select the previous object before the currently selected one. Draw straight line or arrow ⇧ Shift + drag (when drawing lines/arrows) ⇧ Shift + drag (when drawing lines/arrows) Draw a straight vertical/horizontal/45-degree line or arrow. Modifying Objects Constrain movement ⇧ Shift + drag ⇧ Shift + drag Constrain the movement of the selected object horizontally or vertically. Set 15-degree-rotation ⇧ Shift + drag (when rotating) ⇧ Shift + drag (when rotating) Constrain the rotation angle to 15 degree increments. Maintain proportions ⇧ Shift + drag (when resizing) ⇧ Shift + drag (when resizing) Maintain the proportions of the selected object when resizing. Movement pixel by pixel Ctrl+← → ↑ ↓ &#8984; Cmd+← → ↑ ↓ Hold down the Ctrl (&#8984; Cmd for Mac) key and use the keybord arrows to move the selected object by one pixel at a time. Working with Tables Move to the next cell in a row ↹ Tab ↹ Tab Go to the next cell in a table row. Move to the previous cell in a row ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Go to the previous cell in a table row. Move to the next row ↓ ↓ Go to the next row in a table. Move to the previous row ↑ ↑ Go to the previous row in a table. Start new paragraph ↵ Enter ↵ Return Start a new paragraph within a cell. Add new row ↹ Tab in the lower right table cell. ↹ Tab in the lower right table cell. Add a new row at the bottom of the table. Previewing Presentation Start preview from the beginning Ctrl+F5 ^ Ctrl+F5 Start a presentation from the beginning. Navigate forward ↵ Enter, Page Down, →, ↓, ␣ Spacebar ↵ Return, Page Down, →, ↓, ␣ Spacebar Display the next transition effect or advance to the next slide. Navigate backward Page Up, ←, ↑ Page Up, ←, ↑ Display the previous transition effect or return to the previous slide. Close preview Esc Esc End a presentation. Undo and Redo Undo Ctrl+Z ^ Ctrl+Z, &#8984; Cmd+Z Reverse the latest performed action. Redo Ctrl+Y ^ Ctrl+Y, &#8984; Cmd+Y Repeat the latest undone action. Cut, Copy, and Paste Cut Ctrl+X, ⇧ Shift+Delete &#8984; Cmd+X Cut the selected object and send it to the computer clipboard memory. The cut object can be later inserted to another place in the same presentation. Copy Ctrl+C, Ctrl+Insert &#8984; Cmd+C Send the selected object to the computer clipboard memory. The copied object can be later inserted to another place in the same presentation. Paste Ctrl+V, ⇧ Shift+Insert &#8984; Cmd+V Insert the previously copied object from the computer clipboard memory to the current cursor position. The object can be previously copied from the same presentation. Insert hyperlink Ctrl+K ^ Ctrl+K, &#8984; Cmd+K Insert a hyperlink which can be used to go to a web address or to a certain slide in the presentation. Copy style Ctrl+⇧ Shift+C ^ Ctrl+⇧ Shift+C, &#8984; Cmd+⇧ Shift+C Copy the formatting from the selected fragment of the currently edited text. The copied formatting can be later applied to another text fragment in the same presentation. Apply style Ctrl+⇧ Shift+V ^ Ctrl+⇧ Shift+V, &#8984; Cmd+⇧ Shift+V Apply the previously copied formatting to the text in the currently edited text box. Selecting with the Mouse Add to the selected fragment ⇧ Shift ⇧ Shift Start the selection, hold down the ⇧ Shift key and click where you need to end the selection. Selecting using the Keyboard Select all Ctrl+A ^ Ctrl+A, &#8984; Cmd+A Select all the slides (in the slides list) or all the objects within the slide (in the slide editing area) or all the text (within the text box) - depending on where the mouse cursor is located. Select text fragment ⇧ Shift+→ ← ⇧ Shift+→ ← Select the text character by character. Select text from cursor to beginning of line ⇧ Shift+Home Select a text fragment from the cursor to the beginning of the current line. Select text from cursor to end of line ⇧ Shift+End Select a text fragment from the cursor to the end of the current line. Select one character to the right ⇧ Shift+→ ⇧ Shift+→ Select one character to the right of the cursor position. Select one character to the left ⇧ Shift+← ⇧ Shift+← Select one character to the left of the cursor position. Select to the end of a word Ctrl+⇧ Shift+→ Select a text fragment from the cursor to the end of a word. Select to the beginning of a word Ctrl+⇧ Shift+← Select a text fragment from the cursor to the beginning of a word. Select one line up ⇧ Shift+↑ ⇧ Shift+↑ Select one line up (with the cursor at the beginning of a line). Select one line down ⇧ Shift+↓ ⇧ Shift+↓ Select one line down (with the cursor at the beginning of a line). Text Styling Bold Ctrl+B ^ Ctrl+B, &#8984; Cmd+B Make the font of the selected text fragment bold giving it more weight. Italic Ctrl+I ^ Ctrl+I, &#8984; Cmd+I Make the font of the selected text fragment italicized giving it some right side tilt. Underline Ctrl+U ^ Ctrl+U, &#8984; Cmd+U Make the selected text fragment underlined with the line going under the letters. Strikeout Ctrl+5 ^ Ctrl+5, &#8984; Cmd+5 Make the selected text fragment struck out with the line going through the letters. Subscript Ctrl+⇧ Shift+&gt;</kbd> &#8984; Cmd+⇧ Shift+&gt;</kbd> Make the selected text fragment smaller and place it to the lower part of the text line, e.g. as in chemical formulas. Superscript Ctrl+⇧ Shift+&lt;</kbd> &#8984; Cmd+⇧ Shift+&lt;</kbd> Make the selected text fragment smaller and place it to the upper part of the text line, e.g. as in fractions. Bulleted list Ctrl+⇧ Shift+L ^ Ctrl+⇧ Shift+L, &#8984; Cmd+⇧ Shift+L Create an unordered bulleted list from the selected text fragment or start a new one. Remove formatting Ctrl+␣ Spacebar Remove formatting from the selected text fragment. Increase font Ctrl+] ^ Ctrl+], &#8984; Cmd+] Increase the size of the font for the selected text fragment 1 point. Decrease font Ctrl+[ ^ Ctrl+[, &#8984; Cmd+[ Decrease the size of the font for the selected text fragment 1 point. Align center Ctrl+E Center the text between the left and the right edges. Align justified Ctrl+J Justify the text in the paragraph adding additional space between words so that the left and the right text edges were aligned with the paragraph margins. Align right Ctrl+R Align right with the text lined up by the right side of the text box, the left side remains unaligned. Align left Ctrl+L Align left with the text lined up by the left side of the text box, the right side remains unaligned. Increase left indent Ctrl+M ^ Ctrl+M Increase the paragraph left indent by one tabulation position. Decrease left indent Ctrl+⇧ Shift+M ^ Ctrl+⇧ Shift+M Decrease the paragraph left indent by one tabulation position. Delete one character to the left ← Backspace ← Backspace Delete one character to the left of the cursor. Delete one character to the right Delete Fn+Delete Delete one character to the right of the cursor. Moving around in text Move one character to the left ← ← Move the cursor one character to the left. Move one character to the right → → Move the cursor one character to the right. Move one line up ↑ ↑ Move the cursor one line up. Move one line down ↓ ↓ Move the cursor one line down. Move to the beginning of a word or one word to the left Ctrl+← &#8984; Cmd+← Move the cursor to the beginning of a word or one word to the left. Move one word to the right Ctrl+→ &#8984; Cmd+→ Move the cursor one word to the right. Move to next placeholder Ctrl+↵ Enter ^ Ctrl+↵ Return, &#8984; Cmd+↵ Return Move to the next title or body text placeholder. If it is the last placeholder on a slide, this will insert a new slide with the same slide layout as the original slide Jump to the beginning of the line Home Home Put the cursor to the beginning of the currently edited line. Jump to the end of the line End End Put the cursor to the end of the currently edited line. Jump to the beginning of the text box Ctrl+Home Put the cursor to the beginning of the currently edited text box. Jump to the end of the text box Ctrl+End Put the cursor to the end of the currently edited text box."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "View Settings and Navigation Tools", 
        "body": "Presentation Editor offers several tools to help you view and navigate through your presentation: zoom, previous/next slide buttons, slide number indicator. Adjust the View Settings To adjust default view settings and set the most convenient mode to work with the presentation, click the View settings icon on the right side of the editor header and select which interface elements you want to be hidden or shown. You can select the following options from the View settings drop-down list: Hide Toolbar - hides the top toolbar that contains commands while tabs remain visible. When this option is enabled, you can click any tab to display the toolbar. The toolbar is displayed until you click anywhere outside it. To disable this mode, click the View settings icon and click the Hide Toolbar option once again. The top toolbar will be displayed all the time. Note: alternatively, you can just double-click any tab to hide the top toolbar or display it again. Hide Status Bar - hides the bottommost bar where the Slide Number Indicator and Zoom buttons are situated. To show the hidden Status Bar click this option once again. Hide Rulers - hides rulers which are used to set up tab stops and paragraph indents within the text boxes. To show the hidden Rulers click this option once again. The right sidebar is minimized by default. To expand it, select any object/slide and click the icon of the currently activated tab on the right. To minimize the right sidebar, click the icon once again. The left sidebar width is adjusted by simple drag-and-drop: move the mouse cursor over the left sidebar border so that it turns into the bidirectional arrow and drag the border to the left to reduce the sidebar width or to the right to extend it. Use the Navigation Tools To navigate through your presentation, use the following tools: The Zoom buttons are situated in the right lower corner and are used to zoom in and out the current presentation. To change the currently selected zoom value that is displayed in percent, click it and select one of the available zoom options from the list or use the Zoom in or Zoom out buttons. Click the Fit width icon to fit the slide width to the visible part of the working area. To fit the whole slide to the visible part of the working area, click the Fit slide icon. Zoom settings are also available in the View settings drop-down list that can be useful if you decide to hide the Status Bar. Note: you can set a default zoom value. Switch to the File tab of the top toolbar, go to the Advanced Settings... section, choose the necessary Default Zoom Value from the list and click the Apply button. To go to the previous or next slide when editing the presentation, you can use the and buttons at the top and bottom of the vertical scroll bar located to the right of the slide. The Slide Number Indicator shows the current slide as a part of all the slides in the current presentation (slide 'n' of 'nn'). Click this caption to open the window where you can enter the slide number and quickly go to it. If you decide to hide the Status Bar, this tool will become inaccessible."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Search Function", 
        "body": "Search and Replace Function To search for the needed characters, words or phrases used in the currently edited presentation, click the icon situated at the left sidebar or use the Ctrl+F key combination. The Find and Replace window will open: Type in your inquiry into the corresponding data entry field. Specify search parameters by clicking the icon and checking the necessary options: Case sensitive - is used to find only the occurrences typed in the same case as your inquiry (e.g. if your inquiry is 'Editor' and this option is selected, such words as 'editor' or 'EDITOR' etc. will not be found). To disable this option click it once again. Click one of the arrow buttons on the right. The search will be performed either towards the beginning of the presentation (if you click the button) or towards the end of the presentation (if you click the button) from the current position. The first slide in the selected direction that contains the characters you entered will be highlighted in the slide list and displayed in the working area with the required characters outlined. If it is not the slide you are looking for, click the selected button again to find the next slide containing the characters you entered. To replace one or more occurrences of the found characters click the Replace link below the data entry field or use the Ctrl+H key combination. The Find and Replace window will change: Type in the replacement text into the bottom data entry field. Click the Replace button to replace the currently selected occurrence or the Replace All button to replace all the found occurrences. To hide the replace field, click the Hide Replace link."
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Spell-checking", 
        "body": "Presentation Editor allows you to check the spelling of your text in a certain language and correct mistakes while editing. In the desktop version, it's also possible to add words into a custom dictionary which is common for all three editors. First of all, choose a language for your presentation. Click the icon on the right side of the status bar. In the window that appears, select the necessary language and click OK. The selected language will be applied to the whole presentation. To choose a different language for any piece of text within the presentation, select the necessary text passage with the mouse and use the menu at the status bar. To enable the spell checking option, you can: click the Spell checking icon at the status bar, or open the File tab of the top toolbar, select the Advanced Settings... option, check the Turn on spell checking option box and click the Apply button. Incorrectly spelled words will be underlined by a red line. Right click on the necessary word to activate the menu and: choose one of the suggested similar words spelled correctly to replace the misspelled word with the suggested one. If too many variants are found, the More variants... option appears in the menu; use the Ignore option to skip just that word and remove underlining or Ignore All to skip all the identical words repeated in the text; if the current word is missed in the dictionary, you can add it to the custom dictionary. This word will not be treated as a mistake next time. This option is available in the desktop version. select a different language for this word. To disable the spell checking option, you can: click the Spell checking icon at the status bar, or open the File tab of the top toolbar, select the Advanced Settings... option, uncheck the Turn on spell checking option box and click the Apply button."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Supported Formats of Electronic Presentations", 
        "body": "Supported Formats of Electronic Presentation Presentation is a set of slides that may include different type of content such as images, media files, text, effects etc. Presentation Editor handles the following presentation formats: Formats Description View Edit Download PPT File format used by Microsoft PowerPoint + + PPTX Office Open XML Presentation Zipped, XML-based file format developed by Microsoft for representing spreadsheets, charts, presentations, and word processing documents + + + POTX PowerPoint Open XML Document Template Zipped, XML-based file format developed by Microsoft for presentation templates. A POTX template contains formatting settings, styles etc. and can be used to create multiple presentations with the same formatting + + + ODP OpenDocument Presentation File format that represents presentation document created by Impress application, which is a part of OpenOffice based office suites + + + OTP OpenDocument Presentation Template OpenDocument file format for presentation templates. An OTP template contains formatting settings, styles etc. and can be used to create multiple presentations with the same formatting + + + PDF Portable Document Format File format used to represent documents in a manner independent of application software, hardware, and operating systems + PDF/A Portable Document Format / A An ISO-standardized version of the Portable Document Format (PDF) specialized for use in the archiving and long-term preservation of electronic documents. +"
    },
   {
        "id": "HelpfulHints/UsingChat.htm", 
        "title": "Using the Chat Tool", 
        "body": "ONLYOFFICE Presentation Editor offers you the possibility to chat with other users to share ideas concerning particular presentation parts. To access the chat and leave a message for other users, click the icon at the left sidebar, enter your text into the corresponding field below, press the Send button. All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - . To close the panel with chat messages, click the icon once again."
    },
   {
        "id": "ProgramInterface/CollaborationTab.htm", 
        "title": "Collaboration tab", 
        "body": "The Collaboration tab allows to organize collaborative work on the presentation. In the online version, you can share the file, select a co-editing mode, manage comments. In the commenting mode, you can add and remove comments and use chat. In the desktop version, you can manage comments. Online Presentation Editor window: Desktop Presentation Editor window: Using this tab, you can: specify sharing settings (available in the online version only), switch between the Strict and Fast co-editing modes (available in the online version only), add or remove comments to the presentation, open the Chat panel (available in the online version only)."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "File tab", 
        "body": "The File tab allows to perform some basic operations on the current file. Online Presentation Editor window: Desktop Presentation Editor window: Using this tab, you can: in the online version, save the current file (in case the Autosave option is disabled), download as (save the document in the selected format to the computer hard disk drive), save copy as (save a copy of the document in the selected format to the portal documents), print or rename it, in the desktop version, save the current file keeping the current format and location using the Save option or save the current file with a different name, location or format using the Save as option, print the file. protect the file using a password, change or remove the password (available in the desktop version only); create a new presentation or open a recently edited one (available in the online version only), view general information about the presentation or change some file properties, manage access rights (available in the online version only), access the editor Advanced Settings, in the desktop version, open the folder where the file is stored in the File explorer window. In the online version, open the folder of the Documents module where the file is stored in a new browser tab."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Home tab", 
        "body": "The Home tab opens by default when you open a presentation. It allows to set general slide parameters, format text, insert some objects, align and arrange them. Online Presentation Editor window: Desktop Presentation Editor window: Using this tab, you can: manage slides and start slideshow, format text within a text box, insert text boxes, pictures, shapes, align and arrange objects on a slide, copy/clear text formatting, change a theme, color scheme or slide size."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Insert tab", 
        "body": "The Insert tab allows to add visual objects and comments into your presentation. Online Presentation Editor window: Desktop Presentation Editor window: Using this tab, you can: insert tables, insert text boxes and Text Art objects, pictures, shapes, charts, insert comments and hyperlinks, insert footers, date and time, slide numbers. insert equations, symbols."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Plugins tab", 
        "body": "The Plugins tab allows to access advanced editing features using available third-party components. Here you can also use macros to simplify routine operations. Online Presentation Editor window: Desktop Presentation Editor window: The Settings button allows to open the window where you can view and manage all installed plugins and add your own ones. The Macros button allows to open the window where you can create your own macros and run them. To learn more about macros you can refer to our API Documentation. Currently, the following plugins are available: Send allows to send the presentation via email using the default desktop mail client (available in the desktop version only), Audio allows to insert audio records stored on the hard disk drive into your presentation (available in the desktop version only, not available for Mac OS), Video allows to insert video records stored on the hard disk drive into your presentation (available in the desktop version only, not available for Mac OS), Note: to be able to playback video, you'll need to install codecs, for example, K-Lite. Highlight code allows to highlight syntax of the code selecting the necessary language, style, background color, PhotoEditor allows to edit images: crop, flip, rotate them, draw lines and shapes, add icons and text, load a mask and apply filters such as Grayscale, Invert, Sepia, Blur, Sharpen, Emboss, etc., Symbol Table allows to insert special symbols into your text (available in the desktop version only), Thesaurus allows to search for synonyms and antonyms of a word and replace it with the selected one, Translator allows to translate the selected text into other languages, YouTube allows to embed YouTube videos into your presentation. To learn more about plugins please refer to our API Documentation. All the currently existing open source plugin examples are available on GitHub."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Introducing the Presentation Editor user interface", 
        "body": "Presentation Editor uses a tabbed interface where editing commands are grouped into tabs by functionality. Online Presentation Editor window: Desktop Presentation Editor window: The editor interface consists of the following main elements: Editor header displays the logo, opened documents tabs, presentation name and menu tabs. In the left part of the Editor header there are the Save, Print file, Undo and Redo buttons. In the right part of the Editor header the user name is displayed as well as the following icons: Open file location - in the desktop version, it allows to open the folder where the file is stored in the File explorer window. In the online version, it allows to open the folder of the Documents module where the file is stored in a new browser tab. - allows to adjust View Settings and access the editor Advanced Settings. Manage document access rights - (available in the online version only) allows to set access rights for the documents stored in the cloud. Top toolbar displays a set of editing commands depending on the selected menu tab. Currently, the following tabs are available: File, Home, Insert, Collaboration, Protection, Plugins. The Copy and Paste options are always available at the left part of the Top toolbar regardless of the selected tab. Status bar at the bottom of the editor window contains the Start slideshow icon, some navigation tools: slide number indicator and zoom buttons. The Status bar also displays some notifications (such as \"All changes saved\" etc.) and allows to set text language and enable spell checking. Left sidebar contains the following icons: - allows to use the Search and Replace tool, - allows to open the Comments panel, - (available in the online version only) allows to open the Chat panel, as well as the icons that allow to contact our support team and view the information about the program. Right sidebar allows to adjust additional parameters of different objects. When you select a particular object on a slide, the corresponding icon is activated at the right sidebar. Click this icon to expand the right sidebar. Horizontal and vertical Rulers help you place objects on a slide and allow to set up tab stops and paragraph indents within the text boxes. Working area allows to view presentation content, enter and edit data. Scroll bar on the right allows to scroll the presentation up and down. For your convenience you can hide some components and display them again when it is necessary. To learn more on how to adjust view settings please refer to this page."
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Add hyperlinks", 
        "body": "To add a hyperlink, place the cursor to a position within the text box where a hyperlink will be added, switch to the Insert tab of the top toolbar, click the Hyperlink icon at the top toolbar, after that the Hyperlink Settings will appear where you can specify the hyperlink parameters: Select a link type you wish to insert: Use the External Link option and enter a URL in the format http://www.example.com in the Link to field below if you need to add a hyperlink leading to an external website. Use the Slide In This Presentation option and select one of the options below if you need to add a hyperlink leading to a certain slide in the same presentation. You can check one of the following radiobuttons: Next Slide, Previous Slide, First Slide, Last Slide, Slide with the specified number. Display - enter a text that will get clickable and lead to the web address/slide specified in the upper field. ScreenTip text - enter a text that will become visible in a small pop-up window that provides a brief note or label pertaining to the hyperlink being pointed to. Click the OK button. To add a hyperlink, you can also use the Ctrl+K key combination or click with the right mouse button at a position where a hyperlink will be added and select the Hyperlink option in the right-click menu. Note: it's also possible to select a character, word or word combination with the mouse or using the keyboard and then open the Hyperlink Settings window as described above. In this case, the Display field will be filled with the text fragment you selected. By hovering the cursor over the added hyperlink, the ScreenTip will appear containing the text you specified. You can follow the link by pressing the CTRL key and clicking the link in your presentation. To edit or delete the added hyperlink, click it with the right mouse button, select the Hyperlink option in the right-click menu and then the action you want to perform - Edit Hyperlink or Remove Hyperlink."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Align and arrange objects on a slide", 
        "body": "The added autoshapes, images, charts or text boxes can be aligned, grouped, ordered, distributed horizontally and vertically on a slide. To perform any of these actions, first select a separate object or several objects in the slide editing area. To select several objects, hold down the Ctrl key and left-click the necessary objects. To select a text box, click on its border, not the text within it. After that you can use either the icons at the Home tab of the top toolbar described below or the analogous options from the right-click menu. Align objects To align two or more selected objects, Click the Align shape icon at the Home tab of the top toolbar and select one of the following options: Align to Slide to align objects relative to the edges of the slide, Align Selected Objects (this option is selected by default) to align objects relative to each other, Click the Align shape icon once again and select the necessary alignment type from the list: Align Left - to line up the objects horizontally by the left edge of the leftmost object/left edge of the slide, Align Center - to line up the objects horizontally by their centers/center of the slide, Align Right - to line up the objects horizontally by the right edge of the rightmost object/right edge of the slide, Align Top - to line up the objects vertically by the top edge of the topmost object/top edge of the slide, Align Middle - to line up the objects vertically by their middles/middle of the slide, Align Bottom - to line up the objects vertically by the bottom edge of the bottommost object/bottom edge of the slide. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available alignment options. If you want to align a single object, it can be aligned relative to the edges of the slide. The Align to Slide option is selected by default in this case. Distribute objects To distribute three or more selected objects horizontally or vertically so that the equal distance appears between them, Click the Align shape icon at the Home tab of the top toolbar and select one of the following options: Align to Slide to distribute objects between the edges of the slide, Align Selected Objects (this option is selected by default) to distribute objects between two outermost selected objects, Click the Align shape icon once again and select the necessary distribution type from the list: Distribute Horizontally - to distribute objects evenly between the leftmost and rightmost selected objects/left and right edges of the slide. Distribute Vertically - to distribute objects evenly between the topmost and bottommost selected objects/top and bottom edges of the slide. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available distribution options. Note: the distribution options are disabled if you select less than three objects. Group objects To group two or more selected objects or ungroup them, click the Arrange shape icon at the Home tab of the top toolbar and select the necessary option from the list: Group - to join several objects into a group so that they can be simultaneously rotated, moved, resized, aligned, arranged, copied, pasted, formatted like a single object. Ungroup - to ungroup the selected group of the previously joined objects. Alternatively, you can right-click the selected objects, choose the Arrange option from the contextual menu and then use the Group or Ungroup option. Note: the Group option is disabled if you select less than two objects. The Ungroup option is available only when a group of the previously joined objects is selected. Arrange objects To arrange the selected object(s) (i.e. to change their order when several objects overlap each other), click the Arrange shape icon at the Home tab of the top toolbar and select the necessary arrangement type from the list. Bring To Foreground - to move the object(s) in front of all other objects, Send To Background - to move the object(s) behind all other objects, Bring Forward - to move the selected object(s) by one level forward as related to other objects. Send Backward - to move the selected object(s) by one level backward as related to other objects. Alternatively, you can right-click the selected object(s), choose the Arrange option from the contextual menu and then use one of the available arrangement options."
    },
   {
        "id": "UsageInstructions/ApplyTransitions.htm", 
        "title": "Apply transitions", 
        "body": "A transition is an effect that appears between two slides when one slide advances to the next one during a demonstration. You can apply the same transition to all slides or apply different transitions to each separate slide and adjust the transition properties. To apply a transition to a single slide or several selected slides: Select the necessary slide (or several slides in the slide list) you want to apply a transition to. The Slide settings tab will be activated on the right sidebar. To open it click the Slide settings icon on the right. Alternatively, you can right-click a slide in the slide editing area and select the Slide Settings option from the contextual menu. In the Effect drop-down list, select the transition you want to use. The following transitions are available: Fade, Push, Wipe, Split, Uncover, Cover, Clock, Zoom. In the drop-down list below, select one of the available effect options. They define exactly how the effect appears. For example, if the Zoom transition is selected, the Zoom In, Zoom Out and Zoom and Rotate options are available. Specify how long you want the transition to last. In the Duration box, enter or select the necessary time value, measured in seconds. Press the Preview button to view the slide with the applied transition in the slide editing area. Specify how long you want the slide to be displayed until it advances to another one: Start on click – check this box if you don't want to restrict the time while the selected slide is being displayed. The slide will advance to another one only when you click on it with the mouse. Delay – use this option if you want the selected slide to be displayed for a specified time until it advances to the next one. Check this box and enter or select the necessary time value, measured in seconds. Note: if you check only the Delay box, the slides will advance automatically in a specified time interval. If you check both the Start on click and the Delay boxes and set the delay value, the slides will advance automatically as well, but you will also be able to click a slide to advance from it to the next. To apply a transition to all the slides in your presentation: perform the procedure described above and press the Apply to All Slides button. To delete a transition: select the necessary slide and choose the None option in the Effect list. To delete all transitions: select any slide, choose the None option in the Effect list and press the Apply to All Slides button."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Copy/clear formatting", 
        "body": "To copy a certain text formatting, select the text passage which formatting you need to copy with the mouse or using the keyboard, click the Copy style icon at the Home tab of the top toolbar (the mouse pointer will look like this ), select the text passage you want to apply the same formatting to. To apply the copied formatting to multiple text passages, select the text passage which formatting you need to copy with the mouse or using the keyboard, double-click the Copy style icon at the Home tab of the top toolbar (the mouse pointer will look like this and the Copy style icon will remain selected: ), select the necessary text passages one by one to apply the same formatting to each of them, to exit this mode, click the Copy style icon once again or press the Esc key on the keyboard. To quickly remove the formatting that you have applied to a text passage, select the text passage which formatting you want to remove, click the Clear style icon at the Home tab of the top toolbar."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Copy/paste data, undo/redo your actions", 
        "body": "Use basic clipboard operations To cut, copy and paste selected objects (slides, text passages, autoshapes) in the current presentation or undo/redo your actions use the corresponding options from the right-click menu, or keyboard shortcuts, or icons available at any tab of the top toolbar: Cut – select an object and use the Cut option from the right-click menu to delete the selection and send it to the computer clipboard memory. The cut data can be later inserted to another place in the same presentation. Copy – select an object and use the Copy option from the right-click menu or the Copy icon at the top toolbar to copy the selection to the computer clipboard memory. The copied object can be later inserted to another place in the same presentation. Paste – find the place in your presentation where you need to paste the previously copied object and use the Paste option from the right-click menu or the Paste icon at the top toolbar. The object will be inserted at the current cursor position. The object can be previously copied from the same presentation. In the online version, the following key combinations are only used to copy or paste data from/into another presentation or some other program, in the desktop version, both the corresponding buttons/menu options and key combinations can be used for any copy/paste operations: Ctrl+C key combination for copying; Ctrl+V key combination for pasting; Ctrl+X key combination for cutting. Use the Paste Special feature Once the copied data is pasted, the Paste Special button appears next to the inserted text passage/object. Click this button to select the necessary paste option. When pasting text passages, the following options are available: Use destination theme - allows to apply the formatting specified by the theme of the current presentation. This option is used by default. Keep source formatting - allows to keep the source formatting of the copied text. Picture - allows to paste the text as an image so that it cannot be edited. Keep text only - allows to paste the text without its original formatting. When pasting objects (autoshapes, charts, tables) the following options are available: Use destination theme - allows to apply the formatting specified by the theme of the current presentation. This option is used by default. Picture - allows to paste the object as an image so that it cannot be edited. Use the Undo/Redo operations To perform the undo/redo operations, use the corresponding icons in the left part of the editor header or keyboard shortcuts: Undo – use the Undo icon to undo the last operation you performed. Redo – use the Redo icon to redo the last undone operation. You can also use the Ctrl+Z key combination for undoing or Ctrl+Y for redoing. Note: when you co-edit a presentation in the Fast mode, the possibility to Redo the last undone operation is not available."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Create lists", 
        "body": "To create a list in your document, place the cursor to the position where a list will be started (this can be a new line or the already entered text), switch to the Home tab of the top toolbar, select the list type you would like to start: Unordered list with markers is created using the Bullets icon situated at the top toolbar Ordered list with digits or letters is created using the Numbering icon situated at the top toolbar Note: click the downward arrow next to the Bullets or Numbering icon to select how the list is going to look like. now each time you press the Enter key at the end of the line a new ordered or unordered list item will appear. To stop that, press the Backspace key and continue with the common text paragraph. You can also change the text indentation in the lists and their nesting using the Decrease indent , and Increase indent icons at the Home tab of the top toolbar. Note: the additional indentation and spacing parameters can be changed at the right sidebar and in the advanced settings window. To learn more about it, read the Insert and format your text section. Change the list settings To change the bulleted or numbered list settings, such as a bullet type, size and color: click an existing list item or select the text you want to format as a list, click the Bullets or Numbering icon at the Home tab of the top toolbar, select the List Settings option, the List Settings window will open. The bulleted list settings window looks like this: The numbered list settings window looks like this: For the bulleted list, you can choose a character used as a bullet, while for the numbered list you can choose what number the list Starts at. The Size and Color options are the same both for the bulleted and numbered lists. Size - allows to select the necessary bullet/number size depending on the current size of the text. It can take a value from 25% to 400%. Color - allows to select the necessary bullet/number color. You can select one of the theme colors, or standard colors on the palette, or specify a custom color. Bullet - allows to select the necessary character used for the bulleted list. When you click on the Bullet field, the Symbol window opens that allows to choose one of the available characters. To learn more on how to work with symbols, you can refer to this article. Start at - allows to select the nesessary sequence number a numbered list starts from. click OK to apply the changes and close the settings window."
    },
   {
        "id": "UsageInstructions/FillObjectsSelectColor.htm", 
        "title": "Fill objects and select colors", 
        "body": "You can apply different fills for the slide, autoshape and Text Art font background. Select an object To change the slide background fill, select the necessary slides in the slide list. The Slide settings tab will be activated at the the right sidebar. To change the autoshape fill, left-click the necessary autoshape. The Shape settings tab will be activated at the the right sidebar. To change the Text Art font fill, left-click the necessary text object. The Text Art settings tab will be activated at the the right sidebar. Set the necessary fill type Adjust the selected fill properties (see the detailed description below for each fill type) Note: for the autoshapes and Text Art font, regardless of the selected fill type, you can also set an Opacity level dragging the slider or entering the percent value manually. The default value is 100%. It corresponds to the full opacity. The 0% value corresponds to the full transparency. The following fill types are available: Color Fill - select this option to specify the solid color you want to fill the inner space of the selected shape/slide with. Click on the colored box below and select the necessary color from the available color sets or specify any color you like: Theme Colors - the colors that correspond to the selected theme/color scheme of the presentation. Once you apply a different theme or color scheme, the Theme Colors set will change. Standard Colors - the default colors set. Custom Color - click on this caption if there is no needed color in the available palettes. Select the necessary colors range moving the vertical color slider and set the specific color dragging the color picker within the large square color field. Once you select a color with the color picker, the appropriate RGB and sRGB color values will be displayed in the fields on the right. You can also specify a color on the base of the RGB color model entering the necessary numeric values into the R, G, B (Red, Green, Blue) fields or enter the sRGB hexadecimal code into the field marked with the # sign. The selected color appears in the New preview box. If the object was previously filled with any custom color, this color is displayed in the Current box so you can compare the original and modified colors. When the color is specified, click the Add button: The custom color will be applied to your object and added to the Custom color palette of the menu. Note: just the same color types you can use when selecting the color of the autoshape stroke, adjusting the font color, or changing the table background or border color. Gradient Fill - select this option to fill the slide/shape with two colors which smoothly change from one to another. Style - choose one of the available options: Linear (colors change in a straight line i.e. along a horizontal/vertical axis or diagonally at a 45 degree angle) or Radial (colors change in a circular path from the center to the edges). Direction - choose a template from the menu. If the Linear gradient is selected, the following directions are available : top-left to bottom-right, top to bottom, top-right to bottom-left, right to left, bottom-right to top-left, bottom to top, bottom-left to top-right, left to right. If the Radial gradient is selected, only one template is available. Gradient - click on the left slider under the gradient bar to activate the color box which corresponds to the first color. Click on the color box on the right to choose the first color in the palette. Drag the slider to set the gradient stop i.e. the point where one color changes into another. Use the right slider under the gradient bar to specify the second color and set the gradient stop. Picture or Texture - select this option to use an image or a predefined texture as the shape/slide background. If you wish to use an image as a backgroung for the shape/slide, you can add an image From File selecting it on your computer HDD or From URL inserting the appropriate URL address in the opened window. If you wish to use a texture as a backgroung for the shape/slide, drop-down the From Texture menu and select the necessary texture preset. Currently, the following textures are available: Canvas, Carton, Dark Fabric, Grain, Granite, Grey Paper, Knit, Leather, Brown Paper, Papyrus, Wood. In case the selected Picture has less or more dimensions than the autoshape or slide has, you can choose the Stretch or Tile setting from the drop-down list. The Stretch option allows to adjust the image size to fit the slide or autoshape size so that it could fill the space completely. The Tile option allows to display only a part of the bigger image keeping its original dimensions, or repeat the smaller image keeping its original dimensions over the slide or autoshape surface so that it could fill the space completely. Note: any selected Texture preset fills the space completely, but you can apply the Stretch effect if necessary. Pattern - select this option to fill the slide/shape with a two-colored design composed of regularly repeated elements. Pattern - select one of the predefined designs from the menu. Foreground color - click this color box to change the color of the pattern elements. Background color - click this color box to change the color of the pattern background. No Fill - select this option if you don't want to use any fill."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "Insert and format autoshapes", 
        "body": "Insert an autoshape To add an autoshape on a slide, in the slide list on the left, select the slide you want to add the autoshape to, click the Shape icon at the Home or Insert tab of the top toolbar, select one of the available autoshape groups: Basic Shapes, Figured Arrows, Math, Charts, Stars & Ribbons, Callouts, Buttons, Rectangles, Lines, click on the necessary autoshape within the selected group, in the slide editing area, place the mouse cursor where you want the shape to be put, Note: you can click and drag to stretch the shape. once the autoshape is added you can change its size, position and properties. Note: to add a caption within the autoshape make sure the shape is selected on the slide and start typing your text. The text you add in this way becomes a part of the autoshape (when you move or rotate the shape, the text moves or rotates with it). It's also possible to add an autoshape to a slide layout. To learn more, please refer to this article. Adjust autoshape settings Some of the autoshape settings can be altered using the Shape settings tab of the right sidebar. To activate it click the autoshape and choose the Shape settings icon on the right. Here you can change the following properties: Fill - use this section to select the autoshape fill. You can choose the following options: Color Fill - to specify the solid color you want to apply to the selected shape. Gradient Fill - to fill the shape with two colors which smoothly change from one to another. Picture or Texture - to use an image or a predefined texture as the shape background. Pattern - to fill the shape with a two-colored design composed of regularly repeated elements. No Fill - select this option if you don't want to use any fill. For more detailed information on these options please refer to the Fill objects and select colors section. Stroke - use this section to change the autoshape stroke width, color or type. To change the stroke width, select one of the available options from the Size drop-down list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Or select the No Line option if you don't want to use any stroke. To change the stroke color, click on the colored box below and select the necessary color. You can use the selected theme color, a standard color or choose a custom color. To change the stroke type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Rotation is used to rotate the shape by 90 degrees clockwise or counterclockwise as well as to flip the shape horizontally or vertically. Click one of the buttons: to rotate the shape by 90 degrees counterclockwise to rotate the shape by 90 degrees clockwise to flip the shape horizontally (left to right) to flip the shape vertically (upside down) Change Autoshape - use this section to replace the current autoshape with another one selected from the dropdown list. Show shadow - check this option to display shape with shadow. To change the advanced settings of the autoshape, right-click the shape and select the Shape Advanced Settings option from the contextual menu or left-click it and press the Show advanced settings link at the right sidebar. The shape properties window will be opened: The Size tab allows to change the autoshape Width and/or Height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original autoshape aspect ratio. The Rotation tab contains the following parameters: Angle - use this option to rotate the shape by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. Flipped - check the Horizontally box to flip the shape horizontally (left to right) or check the Vertically box to flip the shape vertically (upside down). The Weights & Arrows tab contains the following parameters: Line Style - this option group allows to specify the following parameters: Cap Type - this option allows to set the style for the end of the line, therefore it can be applied only to the shapes with the open outline, such as lines, polylines etc.: Flat - the end points will be flat. Round - the end points will be rounded. Square - the end points will be square. Join Type - this option allows to set the style for the intersection of two lines, for example, it can affect a polyline or the corners of the triangle or rectangle outline: Round - the corner will be rounded. Bevel - the corner will be cut off angularly. Miter - the corner will be pointed. It goes well to shapes with sharp angles. Note: the effect will be more noticeable if you use a large outline width. Arrows - this option group is available if a shape from the Lines shape group is selected. It allows to set the arrow Start and End Style and Size by selecting the appropriate option from the drop-down lists. The Text Padding tab allows to change the autoshape Top, Bottom, Left and Right internal margins (i.e. the distance between the text within the shape and the autoshape borders). Note: this tab is only available if text is added within the autoshape, otherwise the tab is disabled. The Columns tab allows to add columns of text within the autoshape specifying the necessary Number of columns (up to 16) and Spacing between columns. Once you click OK, the text that already exists or any other text you enter within the autoshape will appear in columns and will flow from one column to another. The Alternative Text tab allows to specify a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the shape. To replace the added autoshape, left-click it and use the Change Autoshape drop-down list at the Shape settings tab of the right sidebar. To delete the added autoshape, left-click it and press the Delete key on the keyboard. To learn how to align an autoshape on the slide or arrange several autoshapes, refer to the Align and arrange objects on a slide section. Join autoshapes using connectors You can connect autoshapes using lines with connection points to demonstrate dependencies between the objects (e.g. if you want to create a flowchart). To do that, click the Shape icon at the Home or Insert tab of the top toolbar, select the Lines group from the menu, click the necessary shape within the selected group (excepting the last three shapes which are not connectors, namely Curve, Scribble and Freeform), hover the mouse cursor over the first autoshape and click one of the connection points that appear on the shape outline, drag the mouse cursor towards the second autoshape and click the necessary connection point on its outline. If you move the joined autoshapes, the connector remains attached to the shapes and moves together with them. You can also detach the connector from the shapes and then attach it to any other connection points."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Insert and edit charts", 
        "body": "Insert a chart To insert a chart into your presentation, put the cursor at the place where you want to add a chart, switch to the Insert tab of the top toolbar, click the Chart icon at the top toolbar, select the needed chart type from the available ones - Column, Line, Pie, Bar, Area, XY (Scatter), Stock, Note: for Column, Line, Pie, or Bar charts, a 3D format is also available. after that the Chart Editor window will appear where you can enter the necessary data into the cells using the following controls: and for copying and pasting the copied data and for undoing and redoing actions for inserting a function and for decreasing and increasing decimal places for changing the number format, i.e. the way the numbers you enter appear in cells change the chart settings clicking the Edit Chart button situated in the Chart Editor window. The Chart - Advanced Settings window will open. The Type &amp Data tab allows you to select the chart type as well as the data you wish to use to create a chart. Select a chart Type you wish to insert: Column, Line, Pie, Bar, Area, XY (Scatter), Stock. Check the selected Data Range and modify it, if necessary, clicking the Select Data button and entering the desired data range in the following format: Sheet1!A1:B4. Choose the way to arrange the data. You can either select the Data series to be used on the X axis: in rows or in columns. The Layout tab allows you to change the layout of chart elements. Specify the Chart Title position in regard to your chart selecting the necessary option from the drop-down list: None to not display a chart title, Overlay to overlay and center a title on the plot area, No Overlay to display the title above the plot area. Specify the Legend position in regard to your chart selecting the necessary option from the drop-down list: None to not display a legend, Bottom to display the legend and align it to the bottom of the plot area, Top to display the legend and align it to the top of the plot area, Right to display the legend and align it to the right of the plot area, Left to display the legend and align it to the left of the plot area, Left Overlay to overlay and center the legend to the left on the plot area, Right Overlay to overlay and center the legend to the right on the plot area. Specify the Data Labels (i.e. text labels that represent exact values of data points) parameters: specify the Data Labels position relative to the data points selecting the necessary option from the drop-down list. The available options vary depending on the selected chart type. For Column/Bar charts, you can choose the following options: None, Center, Inner Bottom, Inner Top, Outer Top. For Line/XY (Scatter)/Stock charts, you can choose the following options: None, Center, Left, Right, Top, Bottom. For Pie charts, you can choose the following options: None, Center, Fit to Width, Inner Top, Outer Top. For Area charts as well as for 3D Column, Line and Bar charts, you can choose the following options: None, Center. select the data you wish to include into your labels checking the corresponding boxes: Series Name, Category Name, Value, enter a character (comma, semicolon etc.) you wish to use for separating several labels into the Data Labels Separator entry field. Lines - is used to choose a line style for Line/XY (Scatter) charts. You can choose one of the following options: Straight to use straight lines between data points, Smooth to use smooth curves between data points, or None to not display lines. Markers - is used to specify whether the markers should be displayed (if the box is checked) or not (if the box is unchecked) for Line/XY (Scatter) charts. Note: the Lines and Markers options are available for Line charts and XY (Scatter) charts only. The Axis Settings section allows to specify if you wish to display Horizontal/Vertical Axis or not selecting the Show or Hide option from the drop-down list. You can also specify Horizontal/Vertical Axis Title parameters: Specify if you wish to display the Horizontal Axis Title or not selecting the necessary option from the drop-down list: None to not display a horizontal axis title, No Overlay to display the title below the horizontal axis. Specify the Vertical Axis Title orientation selecting the necessary option from the drop-down list: None to not display a vertical axis title, Rotated to display the title from bottom to top to the left of the vertical axis, Horizontal to display the title horizontally to the left of the vertical axis. The Gridlines section allows to specify which of the Horizontal/Vertical Gridlines you wish to display selecting the necessary option from the drop-down list: Major, Minor, or Major and Minor. You can hide the gridlines at all using the None option. Note: the Axis Settings and Gridlines sections will be disabled for Pie charts since charts of this type have no axes and gridlines. Note: the Vertical/Horizontal Axis tabs will be disabled for Pie charts since charts of this type have no axes. The Vertical Axis tab allows you to change the parameters of the vertical axis also referred to as the values axis or y-axis which displays numeric values. Note that the vertical axis will be the category axis which displays text labels for the Bar charts, therefore in this case the Vertical Axis tab options will correspond to the ones described in the next section. For the XY (Scatter) charts, both axes are value axes. The Axis Options section allows to set the following parameters: Minimum Value - is used to specify a lowest value displayed at the vertical axis start. The Auto option is selected by default, in this case the minimum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Maximum Value - is used to specify a highest value displayed at the vertical axis end. The Auto option is selected by default, in this case the maximum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Axis Crosses - is used to specify a point on the vertical axis where the horizontal axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value on the vertical axis. Display Units - is used to determine a representation of the numeric values along the vertical axis. This option can be useful if you're working with great numbers and wish the values on the axis to be displayed in more compact and readable way (e.g. you can represent 50 000 as 50 by using the Thousands display units). Select desired units from the drop-down list: Hundreds, Thousands, 10 000, 100 000, Millions, 10 000 000, 100 000 000, Billions, Trillions, or choose the None option to return to the default units. Values in reverse order - is used to display values in an opposite direction. When the box is unchecked, the lowest value is at the bottom and the highest value is at the top of the axis. When the box is checked, the values are ordered from top to bottom. The Tick Options section allows to adjust the appearance of tick marks on the vertical scale. Major tick marks are the larger scale divisions which can have labels displaying numeric values. Minor tick marks are the scale subdivisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed, if the corresponding option is set at the Layout tab. The Major/Minor Type drop-down lists contain the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. The Label Options section allows to adjust the appearance of major tick mark labels which display values. To specify a Label Position in regard to the vertical axis, select the necessary option from the drop-down list: None to not display tick mark labels, Low to display tick mark labels to the left of the plot area, High to display tick mark labels to the right of the plot area, Next to axis to display tick mark labels next to the axis. The Horizontal Axis tab allows you to change the parameters of the horizontal axis also referred to as the categories axis or x-axis which displays text labels. Note that the horizontal axis will be the value axis which displays numeric values for the Bar charts, therefore in this case the Horizontal Axis tab options will correspond to the ones described in the previous section. For the XY (Scatter) charts, both axes are value axes. The Axis Options section allows to set the following parameters: Axis Crosses - is used to specify a point on the horizontal axis where the vertical axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value (that corresponds to the first and last category) on the horizontal axis. Axis Position - is used to specify where the axis text labels should be placed: On Tick Marks or Between Tick Marks. Values in reverse order - is used to display categories in an opposite direction. When the box is unchecked, categories are displayed from left to right. When the box is checked, the categories are ordered from right to left. The Tick Options section allows to adjust the appearance of tick marks on the horizontal scale. Major tick marks are the larger divisions which can have labels displaying category values. Minor tick marks are the smaller divisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed, if the corresponding option is set at the Layout tab. You can adjust the following tick mark parameters: Major/Minor Type - is used to specify the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. Interval between Marks - is used to specify how many categories should be displayed between two adjacent tick marks. The Label Options section allows to adjust the appearance of labels which display categories. Label Position - is used to specify where the labels should be placed in regard to the horizontal axis. Select the necessary option from the drop-down list: None to not display category labels, Low to display category labels at the bottom of the plot area, High to display category labels at the top of the plot area, Next to axis to display category labels next to the axis. Axis Label Distance - is used to specify how closely the labels should be placed to the axis. You can specify the necessary value in the entry field. The more the value you set, the more the distance between the axis and labels is. Interval between Labels - is used to specify how often the labels should be displayed. The Auto option is selected by default, in this case labels are displayed for every category. You can select the Manual option from the drop-down list and specify the necessary value in the entry field on the right. For example, enter 2 to display labels for every other category etc. The Alternative Text tab allows to specify a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the chart. once the chart is added you can also change its size and position. You can specify the chart position on the slide dragging it vertically or horizontally. You can also add a chart into a text placeholder pressing the Chart icon within it and selecting the necessary chart type: It's also possible to add a chart to a slide layout. To learn more, please refer to this article. Edit chart elements To edit the chart Title, select the default text with the mouse and type in your own one instead. To change the font formatting within text elements, such as the chart title, axes titles, legend entries, data labels etc., select the necessary text element by left-clicking it. Then use icons at the Home tab of the top toolbar to change the font type, style, size, or color. When the chart is selected, the Shape settings icon is also available on the right, since a shape is used as a background for the chart. You can click this icon to open the Shape settings tab at the right sidebar and adjust the shape Fill, Stroke and Wrapping Style. Note that you cannot change the shape type. Using the Shape Settings tab at the right panel you can not only adjust the chart area itself, but also change the chart elements, such as plot area, data series, chart title, legend etc and apply different fill types to them. Select the chart element clicking it with the left mouse button and choose the preferred fill type: solid color, gradient, texture or picture, pattern. Specify the fill parameters and set the Opacity level if necessary. When you select a vertical or horizontal axis or gridlines, the stroke settings are only available at the Shape Settings tab: color, width and type. For more details on how to work with shape colors, fills and stroke, you can refer to this page. Note: the Show shadow option is also available at the Shape settings tab, but it is disabled for chart elements. To delete a chart element, select it by left-clicking and press the Delete key on the keyboard. You can also rotate 3D charts using the mouse. Left-click within the plot area and hold the mouse button. Drag the cursor without releasing the mouse button to change the 3D chart orientation. Adjust chart settings The chart size, type and style as well as data used to create the chart can be altered using the right sidebar. To activate it click the chart and choose the Chart settings icon on the right. The Size section allows you to change the chart width and/or height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original chart aspect ratio. The Change Chart Type section allows you to change the selected chart type and/or style using the corresponding drop-down menu. To select the necessary chart Style, use the second drop-down menu in the Change Chart Type section. The Edit Data button allows you to open the Chart Editor window and start editing data as described above. Note: to quickly open the 'Chart Editor' window you can also double-click the chart on the slide. The Show advanced settings option at the right sidebar allows to open the Chart - Advanced Settings window where you can set the alternative text: To delete the inserted chart, left-click it and press the Delete key on the keyboard. To learn how to align a chart on the slide or arrange several objects, refer to the Align and arrange objects on a slide section."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Insert equations", 
        "body": "Presentation Editor allows you to build equations using the built-in templates, edit them, insert special characters (including mathematical operators, Greek letters, accents etc.). Add a new equation To insert an equation from the gallery, switch to the Insert tab of the top toolbar, click the arrow next to the Equation icon at the top toolbar, in the opened drop-down list select the equation category you need. The following categories are currently available: Symbols, Fractions, Scripts, Radicals, Integrals, Large Operators, Brackets, Functions, Accents, Limits and Logarithms, Operators, Matrices, click the certain symbol/equation in the corresponding set of templates. The selected symbol/equation box will be inserted in the center of the current slide. If you do not see the equation box border, click anywhere within the equation - the border will be displayed as a dashed line. The equation box can be freely moved, resized or rotated on the slide. To do that click on the equation box border (it will be displayed as a solid line) and use corresponding handles. Each equation template represents a set of slots. Slot is a position for each element that makes up the equation. An empty slot (also called as a placeholder) has a dotted outline . You need to fill in all the placeholders specifying the necessary values. Enter values The insertion point specifies where the next character you enter will appear. To position the insertion point precisely, click within a placeholder and use the keyboard arrows to move the insertion point by one character left/right. Once the insertion point is positioned, you can fill in the placeholder: enter the desired numeric/literal value using the keyboard, insert a special character using the Symbols palette from the Equation menu at the Insert tab of the top toolbar, add another equation template from the palette to create a complex nested equation. The size of the primary equation will be automatically adjusted to fit its content. The size of the nested equation elements depends on the primary equation placeholder size, but it cannot be smaller than the sub-subscript size. To add some new equation elements you can also use the right-click menu options: To add a new argument that goes before or after the existing one within Brackets, you can right-click on the existing argument and select the Insert argument before/after option from the menu. To add a new equation within Cases with several conditions from the Brackets group, you can right-click on an empty placeholder or entered equation within it and select the Insert equation before/after option from the menu. To add a new row or a column in a Matrix, you can right-click on a placeholder within it, select the Insert option from the menu, then select Row Above/Below or Column Left/Right. Note: currently, equations cannot be entered using the linear format, i.e. \\sqrt(4&x^3). When entering the values of the mathematical expressions, you do not need to use Spacebar as the spaces between the characters and signs of operations are set automatically. If the equation is too long and does not fit to a single line within the text box, automatic line breaking occurs as you type. You can also insert a line break in a specific position by right-clicking on a mathematical operator and selecting the Insert manual break option from the menu. The selected operator will start a new line. To delete the added manual line break, right-click on the mathematical operator that starts a new line and select the Delete manual break option. Format equations By default, the equation within the text box is horizontally centered and vertically aligned to the top of the text box. To change its horizontal/vertical alignment, put the cursor within the the equation box (the text box borders will be displayed as dashed lines) and use the corresponding icons at the Home tab of the top toolbar. To increase or decrease the equation font size, click anywhere within the equation box and select the necessary font size from the list at the Home tab of the top toolbar. All the equation elements will change correspondingly. The letters within the equation are italicized by default. If necessary, you can change the font style (bold, italic, strikeout) or color for a whole equation or its part. The underlined style can be applied to the entire equation only, not to individual characters. Select the necessary part of the equation by clicking and dragging. The selected part will be highlighted blue. Then use the necessary buttons at the Home tab of the top toolbar to format the selection. For example, you can remove the italic format for ordinary words that are not variables or constants. To modify some equation elements you can also use the right-click menu options: To change the Fractions format, you can right-click on a fraction and select the Change to skewed/linear/stacked fraction option from the menu (the available options differ depending on the selected fraction type). To change the Scripts position relating to text, you can right-click on the equation that includes scripts and select the Scripts before/after text option from the menu. To change the argument size for Scripts, Radicals, Integrals, Large Operators, Limits and Logarithms, Operators as well as for overbraces/underbraces and templates with grouping characters from the Accents group, you can right-click on the argument you want to change and select the Increase/Decrease argument size option from the menu. To specify whether an empty degree placeholder should be displayed or not for a Radical, you can right-click on the radical and select the Hide/Show degree option from the menu. To specify whether an empty limit placeholder should be displayed or not for an Integral or Large Operator, you can right-click on the equation and select the Hide/Show top/bottom limit option from the menu. To change the limits position relating to the integral or operator sign for Integrals or Large Operators, you can right-click on the equation and select the Change limits location option from the menu. The limits can be displayed to the right of the operator sign (as subscripts and superscripts) or directly above and below the operator sign. To change the limits position relating to text for Limits and Logarithms and templates with grouping characters from the Accents group, you can right-click on the equation and select the Limit over/under text option from the menu. To choose which of the Brackets should be displayed, you can right-click on the expression within them and select the Hide/Show opening/closing bracket option from the menu. To control the Brackets size, you can right-click on the expression within them. The Stretch brackets option is selected by default so that the brackets can grow according to the expression within them, but you can deselect this option to prevent brackets from stretching. When this option is activated, you can also use the Match brackets to argument height option. To change the character position relating to text for overbraces/underbraces or overbars/underbars from the Accents group, you can right-click on the template and select the Char/Bar over/under text option from the menu. To choose which borders should be displayed for a Boxed formula from the Accents group, you can right-click on the equation and select the Border properties option from the menu, then select Hide/Show top/bottom/left/right border or Add/Hide horizontal/vertical/diagonal line. To specify whether empty placeholders should be displayed or not for a Matrix, you can right-click on it and select the Hide/Show placeholder option from the menu. To align some equation elements you can use the right-click menu options: To align equations within Cases with several conditions from the Brackets group, you can right-click on an equation, select the Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align a Matrix vertically, you can right-click on the matrix, select the Matrix Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align elements within a Matrix column horizontally, you can right-click on a placeholder within the column, select the Column Alignment option from the menu, then select the alignment type: Left, Center, or Right. Delete equation elements To delete a part of the equation, select the part you want to delete by dragging the mouse or holding down the Shift key and using the arrow buttons, then press the Delete key on the keyboard. A slot can only be deleted together with the template it belongs to. To delete the entire equation, click on the equation box border (it will be displayed as a solid line) and and press the Delete key on the keyboard. To delete some equation elements you can also use the right-click menu options: To delete a Radical, you can right-click on it and select the Delete radical option from the menu. To delete a Subscript and/or Superscript, you can right-click on the expression that contains them and select the Remove subscript/superscript option from the menu. If the expression contains scripts that go before text, the Remove scripts option is available. To delete Brackets, you can right-click on the expression within them and select the Delete enclosing characters or Delete enclosing characters and separators option from the menu. If the expression within Brackets inclides more than one argument, you can right-click on the argument you want to delete and select the Delete argument option from the menu. If Brackets enclose more than one equation (i.e. Cases with several conditions), you can right-click on the equation you want to delete and select the Delete equation option from the menu. To delete a Limit, you can right-click on it and select the Remove limit option from the menu. To delete an Accent, you can right-click on it and select the Remove accent character, Delete char or Remove bar option from the menu (the available options differ depending on the selected accent). To delete a row or a column of a Matrix, you can right-click on the placeholder within the row/column you need to delete, select the Delete option from the menu, then select Delete Row/Column."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Insert footers", 
        "body": "Footers allow to add some additional info on a slide, such as date and time, slide number, or a text. To insert a footer in a presentation: switch to the Insert tab, click the Edit footer button at the top toolbar, the Footer Settings window will open. Check the data you want to add into the footer. The changes are displayed in the preview window on the right. check the Date and time box to insert a date or time in a selected format. The selected date will be added to the left field of the slide footer. Specify the necessary data format: Update automatically - check this radio button if you want to automatically update the date and time according to the current date and time. Then select the necessary date and time Format and Language from the lists. Fixed - check this radio button if you do not want to automatically update the date and time. check the Slide number box to insert the current slide number. The slide number will be added in the right field of the slide footer. check Text in footer box to insert any text. Enter the necessary text in the entry field below. The text will be added in the central field of the slide footer. check the Don't show on the title slide option, if necessary, click the Apply to all button to apply changes to all slides or use the Apply button to apply the changes to the current slide only. To quickly insert a date or a slide number into the footer of the selected slide, you can use the Show slide Number and Show Date and Time options at the Slide Settings tab of the right sidebar. In this case, the selected settings will be applied to the current slide only. The date and time or slide number added in such a way can be adjusted later using the Footer Settings window. To edit the added footer, click the Edit footer button at the top toolbar, make the necessary changes in the Footer Settings window, and click the Apply or Apply to All button to save the changes. Insert date and time and slide number into the text box It's also possible to insert date and time or slide number into the selected text box using the corresponding buttons at the Insert tab of the top toolbar. Insert date and time put the mouse cursor within the text box where you want to insert the date and time, click the Date &amp; Time button at the Insert tab of the top toolbar, select the necessary Language from the list and choose the necessary date and time Format in the Date &amp; Time window, if necessary, check the Update automatically box or press the Set as default box to set the selected date and time format as default for the specified language, click the OK button to apply the changes. The date and time will be inserted in the current cursor position. To edit the inserted date and time, select the inserted date and time in the text box, click the Date &amp; Time button at the Insert tab of the top toolbar, choose the necessary format in the Date &amp; Time window, click the OK button. Insert a slide number put the mouse cursor within the text box where you want to insert the slide number, click the Slide Number button at the Insert tab of the top toolbar, check the Slide number box in the Footer Settings window, click the OK button to apply the changes. The slide number will be inserted in the current cursor position."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Insert and adjust images", 
        "body": "Insert an image In Presentation Editor, you can insert images in the most popular formats into your presentation. The following image formats are supported: BMP, GIF, JPEG, JPG, PNG. To add an image on a slide, in the slide list on the left, select the slide you want to add the image to, click the Image icon at the Home or Insert tab of the top toolbar, select one of the following options to load the image: the Image from File option will open the standard dialog window for file selection. Browse your computer hard disk drive for the necessary file and click the Open button the Image from URL option will open the window where you can enter the necessary image web address and click the OK button the Image from Storage option will open the Select data source window. Select an image stored on your portal and click the OK button once the image is added you can change its size and position. You can also add an image into a text placeholder pressing the Image from file in it and selecting the necessary image stored on your PC, or use the Image from URL button and specify the image URL address: It's also possible to add an image to a slide layout. To learn more, please refer to this article. Adjust image settings The right sidebar is activated when you left-click an image and choose the Image settings icon on the right. It contains the following sections: Size - is used to view the current image Width and Height or restore the image Actual Size if necessary. The Crop button is used to crop the image. Click the Crop button to activate cropping handles which appear on the image corners and in the center of each its side. Manually drag the handles to set the cropping area. You can move the mouse cursor over the cropping area border so that it turns into the icon and drag the area. To crop a single side, drag the handle located in the center of this side. To simultaneously crop two adjacent sides, drag one of the corner handles. To equally crop two opposite sides of the image, hold down the Ctrl key when dragging the handle in the center of one of these sides. To equally crop all sides of the image, hold down the Ctrl key when dragging any of the corner handles. When the cropping area is specified, click the Crop button once again, or press the Esc key, or click anywhere outside of the cropping area to apply the changes. After the cropping area is selected, it's also possible to use the Fill and Fit options available from the Crop drop-down menu. Click the Crop button once again and select the option you need: If you select the Fill option, the central part of the original image will be preserved and used to fill the selected cropping area, while other parts of the image will be removed. If you select the Fit option, the image will be resized so that it fits the cropping area height or width. No parts of the original image will be removed, but empty spaces may appear within the selected cropping area. Replace Image - is used to load another image instead of the current one selecting the desired source. You can select one of the options: From File or From URL. The Replace image option is also available in the right-click menu. Rotation is used to rotate the image by 90 degrees clockwise or counterclockwise as well as to flip the image horizontally or vertically. Click one of the buttons: to rotate the image by 90 degrees counterclockwise to rotate the image by 90 degrees clockwise to flip the image horizontally (left to right) to flip the image vertically (upside down) When the image is selected, the Shape settings icon is also available on the right. You can click this icon to open the Shape settings tab at the right sidebar and adjust the shape Stroke type, size and color as well as change the shape type selecting another shape from the Change Autoshape menu. The shape of the image will change correspondingly. At the Shape Settings tab, you can also use the Show shadow option to add a shadow to the image. To change the advanced settings of the image, right-click the image and select the Image Advanced Settings option from the contextual menu or left-click the image and press the Show advanced settings link at the right sidebar. The image properties window will be opened: The Placement tab allows you to set the following image properties: Size - use this option to change the image width and/or height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original image aspect ratio. To restore the actual size of the added image, click the Actual Size button. Position - use this option to change the image position on the slide (the position is calculated from the top and the left side of the slide). The Rotation tab contains the following parameters: Angle - use this option to rotate the image by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. Flipped - check the Horizontally box to flip the image horizontally (left to right) or check the Vertically box to flip the image vertically (upside down). The Alternative Text tab allows to specify a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image. To delete the inserted image, left-click it and press the Delete key on the keyboard. To learn how to align an image on the slide or arrange several images, refer to the Align and arrange objects on a slide section."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Inserire simboli e caratteri", 
        "body": "Durante il processo di lavoro potrebbe essere necessario inserire un simbolo che non si trova sulla tastiera. Per inserire tali simboli nel tuo documento, usa l’opzione Inserisci simbolo e segui questi semplici passaggi: posiziona il cursore nella posizione in cui deve essere inserito un simbolo speciale, passa alla scheda Inserisci della barra degli strumenti in alto, fai clic sull’icona Simbolo, viene visualizzata la scheda di dialogo Simbolo da cui è possibile selezionare il simbolo appropriato, utilizza la sezione Intervallo per trovare rapidamente il simbolo necessario. Tutti i simboli sono divisi in gruppi specifici, ad esempio seleziona \"Simboli di valuta” se desideri inserire un carattere di valuta. se questo carattere non è nel set, seleziona un carattere diverso. Molti di loro hanno anche caratteri diversi dal set standard. in alternativa, immetti il valore esadecimale Unicode del simbolo desiderato nel campo valore Unicode HEX. Questo codice si trova nella Mappa caratteri. i simboli utilizzati in precedenza vengono visualizzati anche nel campo Simboli usati di recente, fai clic su Inserisci. Il carattere selezionato verrà aggiunto al documento. Inserire simboli ASCII La tabella ASCII viene anche utilizzata per aggiungere caratteri. Per fare ciò, tieni premuto il tasto ALT e usa il tastierino numerico per inserire il codice carattere. Nota: assicurarsi di utilizzare il tastierino numerico, non i numeri sulla tastiera principale. Per abilitare il tastierino numerico, premere il tasto Bloc Num. Ad esempio, per aggiungere ad un paragrafo il carattere (§), premere e tenere premuto il tasto ALT mentre si digita 789 e quindi rilasciare il tasto ALT. Inserire simboli usando la tabella Unicode Ulteriori caratteri e simboli possono essere trovati anche nella tabella dei simboli di Windows. Per aprire questa tabella, effettuate una delle seguenti operazioni: nel campo Ricerca scrivi 'Tabella caratteri' e aprila, in alternativa premi contemporaneamente Win + R, quindi nella seguente finestra digita charmap.exe e fai clic su OK. Nella Mappa caratteri aperta, selezionare uno dei Set di caratteri, Gruppi e Caratteri. Quindi, fai clic sui caratteri necessari, copiali negli appunti e incollali nella posizione corretta del documento."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Insert and format tables", 
        "body": "Insert a table To insert a table onto a slide, select the slide where a table will be added, switch to the Insert tab of the top toolbar, click the Table icon at the top toolbar, select the option to create a table: either a table with predefined number of cells (10 by 8 cells maximum) If you want to quickly add a table, just select the number of rows (8 maximum) and columns (10 maximum). or a custom table In case you need more than 10 by 8 cell table, select the Insert Custom Table option that will open the window where you can enter the necessary number of rows and columns respectively, then click the OK button. once the table is added you can change its properties and position. You can also add a table into a text placeholder pressing the Table icon within it and selecting the necessary number of cells or using the Insert Custom Table option: To resize a table, drag the handles situated on its edges until the table reaches the necessary size. You can also manually change the width of a certain column or the height of a row. Move the mouse cursor over the right border of the column so that the cursor turns into the bidirectional arrow and drag the border to the left or right to set the necessary width. To change the height of a single row manually, move the mouse cursor over the bottom border of the row until the cursor turns into the bidirectional arrow and drag it up or down. You can specify the table position on the slide dragging it vertically or horizontally. Note: to move around in a table you can use keyboard shortcuts. It's also possible to add a table to a slide layout. To learn more, please refer to this article. Adjust table settings Most of the table properties as well as its structure can be altered using the right sidebar. To activate it click the table and choose the Table settings icon on the right. The Rows and Columns sections on the top allow you to emphasize certain rows/columns applying a specific formatting to them, or highlight different rows/columns with the different background colors to clearly distinguish them. The following options are available: Header - emphasizes the topmost row in the table with a special formatting. Total - emphasizes the bottommost row in the table with a special formatting. Banded - enables the background color alternation for odd and even rows. First - emphasizes the leftmost column in the table with a special formatting. Last - emphasizes the rightmost column in the table with a special formatting. Banded - enables the background color alternation for odd and even columns. The Select From Template section allows you to choose one of the predefined tables styles. Each template combines certain formatting parameters, such as a background color, border style, row/column banding etc. Depending on the options checked in the Rows and/or Columns sections above, the templates set will be displayed differently. For example, if you've checked the Header option in the Rows section and the Banded option in the Columns section, the displayed templates list will include only templates with the header row and banded columns enabled: The Borders Style section allows you to change the applied formatting that corresponds to the selected template. You can select the entire table or a certain cells range you want to change the formatting for and set all the parameters manually. Border parameters - set the border width using the list (or choose the No borders option), select its Color in the available palettes and determine the way it will be displayed in the cells clicking on the icons: Background color - select the color for the background within the selected cells. The Rows & Columns section allows you to perform the following operations: Select a row, column, cell (depending on the cursor position), or the entire table. Insert a new row above or below the selected one as well as a new column to the left or to the right of the selected one. Delete a row, column (depending on the cursor position or the selection), or the entire table. Merge Cells - to merge previously selected cells into a single one. Split Cell... - to split any previously selected cell into a certain number of rows and columns. This option opens the following window: Enter the Number of Columns and Number of Rows that the selected cell should be split into and press OK. Note: the options of the Rows & Columns section are also accessible from the right-click menu. The Cell Size section is used to adjust the width and height of the currently selected cell. In this section, you can also Distribute rows so that all the selected cells have equal height or Distribute columns so that all the selected cells have equal width. The Distribute rows/columns options are also accessible from the right-click menu. Adjust table advanced settings To change the advanced table settings, click the table with the right mouse button and select the Table Advanced Settings option from the right-click menu or click the Show advanced settings link at the right sidebar. The table properties window will be opened: The Margins tab allows to set the space between the text within the cells and the cell border: enter necessary Cell Margins values manually, or check the Use default margins box to apply the predefined values (if necessary, they can also be adjusted). The Alternative Text tab allows to specify a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the table. To format the entered text within the table cells, you can use icons at the Home tab of the top toolbar. The right-click menu that appears when you click the table with the right mouse button includes two additional options: Cell vertical alignment - it allows you to set the preferred type of the text vertical alignment within the selected cells: Align Top, Align Center, or Align Bottom. Hyperlink - it allows you to insert a hyperlink into the selected cell."
    },
   {
        "id": "UsageInstructions/InsertText.htm", 
        "title": "Insert and format your text", 
        "body": "Insert your text You can add a new text in three different ways: Add a text passage within the corresponding text placeholder provided on the slide layout. To do that just put the cursor within the placeholder and type in your text or paste it using the Ctrl+V key combination in place of the according default text. Add a text passage anywhere on a slide. You can insert a text box (a rectangular frame that allows to enter text within it) or a Text Art object (a text box with a predefined font style and color that allows to apply some text effects). Depending on the necessary text object type you can do the following: to add a text box, click the Text Box icon at the Home or Insert tab of the top toolbar, then click where you want to insert the text box, hold the mouse button and drag the text box border to specify its size. When you release the mouse button, the insertion point will appear in the added text box, allowing you to enter your text. Note: it's also possible to insert a text box by clicking the Shape icon at the top toolbar and selecting the shape from the Basic Shapes group. to add a Text Art object, click the Text Art icon at the Insert tab of the top toolbar, then click on the desired style template – the Text Art object will be added in the center of the slide. Select the default text within the text box with the mouse and replace it with your own text. Add a text passage within an autoshape. Select a shape and start typing your text. Click outside of the text object to apply the changes and return to the slide. The text within the text object is a part of the latter (when you move or rotate the text object, the text moves or rotates with it). As an inserted text object represents a rectangular frame (it has invisible text box borders by default) with text in it and this frame is a common autoshape, you can change both the shape and text properties. To delete the added text object, click on the text box border and press the Delete key on the keyboard. The text within the text box will also be deleted. Format a text box Select the text box clicking on its border to be able to change its properties. When the text box is selected, its borders are displayed as solid (not dashed) lines. to resize, move, rotate the text box use the special handles on the edges of the shape. to edit the text box fill, stroke, replace the rectangular box with a different shape, or access the shape advanced settings, click the Shape settings icon on the right sidebar and use the corresponding options. to align a text box on the slide, rotate or flip it, arrange text boxes as related to other objects, right-click on the text box border and use the contextual menu options. to create columns of text within the text box, right-click on the text box border, click the Shape Advanced Settings option and switch to the Columns tab in the Shape - Advanced Settings window. Format the text within the text box Click the text within the text box to be able to change its properties. When the text is selected, the text box borders are displayed as dashed lines. Note: it's also possible to change text formatting when the text box (not the text itself) is selected. In such a case, any changes will be applied to all the text within the text box. Some font formatting options (font type, size, color and decoration styles) can be applied to a previously selected portion of the text separately. Align your text within the text box The text is aligned horizontally in four ways: left, right, center or justified. To do that: place the cursor to the position where you want the alignment to be applied (this can be a new line or already entered text), drop-down the Horizontal align list at the Home tab of the top toolbar, select the alignment type you would like to apply: the Align text left option allows you to line up your text by the left side of the text box (the right side remains unaligned). the Align text center option allows you to line up your text by the center of the text box (the right and the left sides remains unaligned). the Align text right option allows you to line up your text by the right side of the text box (the left side remains unaligned). the Justify option allows you to line up your text by both the left and the right sides of the text box (additional spacing is added where necessary to keep the alignment). Note: these parameters can also be found in the Paragraph - Advanced Settings window. The text is aligned vertically in three ways: top, middle or bottom. To do that: place the cursor to the position where you want the alignment to be applied (this can be a new line or already entered text), drop-down the Vertical align list at the Home tab of the top toolbar, select the alignment type you would like to apply: the Align text to the top option allows you to line up your text by the top of the text box. the Align text to the middle option allows you to line up your text by the center of the text box. the Align text to the bottom option allows you to line up your text by the bottom of the text box. Change the text direction To Rotate the text within the text box, right-click the text, select the Text Direction option and then choose one of the available options: Horizontal (is selected by default), Rotate Text Down (sets a vertical direction, from top to bottom) or Rotate Text Up (sets a vertical direction, from bottom to top). Adjust font type, size, color and apply decoration styles You can select the font type, its size and color as well as apply various font decoration styles using the corresponding icons situated at the Home tab of the top toolbar. Note: in case you want to apply the formatting to the text already present in the presentation, select it with the mouse or using the keyboard and apply the formatting. Font Is used to select one of the fonts from the list of the available ones. If a required font is not available in the list, you can download and install it on your operating system, after that the font will be available for use in the desktop version. Font size Is used to select among the preset font size values from the dropdown list (the default values are: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 and 96). It's also possible to manually enter a custom value to the font size field and then press Enter. Font color Is used to change the color of the letters/characters in the text. Click the downward arrow next to the icon to select the color. Bold Is used to make the font bold giving it more weight. Italic Is used to make the font italicized giving it some right side tilt. Underline Is used to make the text underlined with the line going under the letters. Strikeout Is used to make the text struck out with the line going through the letters. Superscript Is used to make the text smaller and place it to the upper part of the text line, e.g. as in fractions. Subscript Is used to make the text smaller and place it to the lower part of the text line, e.g. as in chemical formulas. Set line spacing and change paragraph indents You can set the line height for the text lines within the paragraph as well as the margins between the current and the preceding or the subsequent paragraph. To do that, put the cursor within the paragraph you need, or select several paragraphs with the mouse, use the corresponding fields of the Text settings tab at the right sidebar to achieve the desired results: Line Spacing - set the line height for the text lines within the paragraph. You can select among three options: at least (sets the minimum line spacing that is needed to fit the largest font or graphic on the line), multiple (sets line spacing that can be expressed in numbers greater than 1), exactly (sets fixed line spacing). You can specify the necessary value in the field on the right. Paragraph Spacing - set the amount of space between paragraphs. Before - set the amount of space before the paragraph. After - set the amount of space after the paragraph. Note: these parameters can also be found in the Paragraph - Advanced Settings window. To quickly change the current paragraph line spacing, you can also use the Line spacing icon at the Home tab of the top toolbar selecting the needed value from the list: 1.0, 1.15, 1.5, 2.0, 2.5, or 3.0 lines. To change the paragraph offset from the left side of the text box, put the cursor within the paragraph you need, or select several paragraphs with the mouse and use the respective icons at the Home tab of the top toolbar: Decrease indent and Increase indent . Adjust paragraph advanced settings To open the Paragraph - Advanced Settings window, right-click the text and choose the Text Advanced Settings option from the menu. It's also possible to put the cursor within the paragraph you need - the Text settings tab will be activated at the right sidebar. Press the Show advanced settings link. The paragraph properties window will be opened: The Indents & Spacing tab allows to: change the alignment type for the paragraph text, change the paragraph indents as related to internal margins of the text box, Left - set the paragraph offset from the left internal margin of the text box specifying the necessary numeric value, Right - set the paragraph offset from the right internal margin of the text box specifying the necessary numeric value, Special - set an indent for the first line of the paragraph: select the corresponding menu item ((none), First line, Hanging) and change the default numeric value specified for First Line or Hanging, change the paragraph line spacing. You can also use the horizontal ruler to set indents. Select the necessary paragraph(s) and drag the indent markers along the ruler. First Line Indent marker is used to set the offset from the left internal margin of the text box for the first line of the paragraph. Hanging Indent marker is used to set the offset from the left internal margin of the text box for the second and all the subsequent lines of the paragraph. Left Indent marker is used to set the entire paragraph offset from the left internal margin of the text box. Right Indent marker is used to set the paragraph offset from the right internal margin of the text box. Note: if you don't see the rulers, switch to the Home tab of the top toolbar, click the View settings icon at the upper right corner and uncheck the Hide Rulers option to display them. The Font tab contains the following parameters: Strikethrough is used to make the text struck out with the line going through the letters. Double strikethrough is used to make the text struck out with the double line going through the letters. Superscript is used to make the text smaller and place it to the upper part of the text line, e.g. as in fractions. Subscript is used to make the text smaller and place it to the lower part of the text line, e.g. as in chemical formulas. Small caps is used to make all letters lower case. All caps is used to make all letters upper case. Character Spacing is used to set the space between the characters. Increase the default value to apply the Expanded spacing, or decrease the default value to apply the Condensed spacing. Use the arrow buttons or enter the necessary value in the box. All the changes will be displayed in the preview field below. The Tab tab allows to change tab stops i.e. the position the cursor advances to when you press the Tab key on the keyboard. Default Tab is set at 2.54 cm. You can decrease or increase this value using the arrow buttons or enter the necessary one in the box. Tab Position - is used to set custom tab stops. Enter the necessary value in this box, adjust it more precisely using the arrow buttons and press the Specify button. Your custom tab position will be added to the list in the field below. Alignment - is used to set the necessary alignment type for each of the tab positions in the list above. Select the necessary tab position in the list, choose the Left, Center or Right option from the Alignment drop-down list and press the Specify button. Left - lines up your text by the left side at the tab stop position; the text moves to the right from the tab stop as you type. Such a tab stop will be indicated on the horizontal ruler by the marker. Center - centres the text at the tab stop position. Such a tab stop will be indicated on the horizontal ruler by the marker. Right - lines up your text by the right side at the tab stop position; the text moves to the left from the tab stop as you type. Such a tab stop will be indicated on the horizontal ruler by the marker. To delete tab stops from the list select a tab stop and press the Remove or Remove All button. To set tab stops you can also use the horizontal ruler: Click the tab selector button in the upper left corner of the working area to choose the necessary tab stop type: Left , Center , Right . Click on the bottom edge of the ruler where you want to place the tab stop. Drag it along the ruler to change its position. To remove the added tab stop drag it out of the ruler. Note: if you don't see the rulers, switch to the Home tab of the top toolbar, click the View settings icon at the upper right corner and uncheck the Hide Rulers option to display them. Edit a Text Art style Select a text object and click the Text Art settings icon on the right sidebar. Change the applied text style selecting a new Template from the gallery. You can also change the basic style additionally by selecting a different font type, size etc. Change the font fill and stroke. The available options are the same as the ones for autoshapes. Apply a text effect by selecting the necessary text transformation type from the Transform gallery. You can adjust the degree of the text distortion by dragging the pink diamond-shaped handle."
    },
   {
        "id": "UsageInstructions/ManageSlides.htm", 
        "title": "Manage slides", 
        "body": "By default, a newly created presentation has one blank Title Slide. You can create new slides, copy a slide to be able to paste it to another place in the slide list, duplicate slides, move slides to change their order in the slide list, delete unnecessary slides, mark some slides as hidden. To create a new Title and Content slide: click the Add Slide icon at the Home or Insert tab of the top toolbar, or right-click any slide in the list and select the New Slide option from the contextual menu, or press the Ctrl+M key combination. To create a new slide with a different layout: click the arrow next to the Add Slide icon at the Home or Insert tab of the top toolbar, select a slide with the necessary layout from the menu. Note: you can change the layout of the added slide anytime. For additional information on how to do that refer to the Set slide parameters section. A new slide will be inserted after the selected one in the list of the existing slides on the left. To duplicate a slide: right-click the necessary slide in the list of the existing slides on the left, select the Duplicate Slide option from the contextual menu. The duplicated slide will be inserted after the selected one in the slide list. To copy a slide: in the list of the existing slides on the left, select the slide you need to copy, press the Ctrl+C key combination, in the slide list, select the slide that the copied one should be pasted after, press the Ctrl+V key combination. To move an existing slide: left-click the necessary slide in the list of the existing slides on the left, without releasing the mouse button, drag it to the necessary place in the list (a horizontal line indicates a new location). To delete an unnecessary slide: right-click the slide you want to delete in the list of the existing slides on the left, select the Delete Slide option from the contextual menu. To mark a slide as hidden: right-click the slide you want to hide in the list of the existing slides on the left, select the Hide Slide option from the contextual menu. The number that corresponds to the hidden slide in the slide list on the left will be crossed out. To display the hidden slide as a regular one in the slide list, click the Hide Slide option once again. Note: use this option if you do not want to demonstrate some slides to your audience, but want to be able to access them if necessary. If you start the slideshow in the Presenter mode, you can see all the existing slides in the list on the left, while hidden slides numbers are crossed out. If you wish to show a slide marked as hidden to others, just click it in the slide list on the left - the slide will be displayed. To select all the existing slides at once: right-click any slide in the list of the existing slides on the left, select the Select All option from the contextual menu. To select several slides: hold down the Ctrl key, select the necessary slides left-clicking them in the list of the existing slides on the left. Note: all the key combinations that can be used to manage slides are listed at the Keyboard Shortcuts page."
    },
   {
        "id": "UsageInstructions/ManipulateObjects.htm", 
        "title": "Manipulate objects on a slide", 
        "body": "You can resize, move, rotate different objects on a slide manually using the special handles. You can also specify the dimensions and position of some objects exactly using the right sidebar or Advanced Settings window. Note: the list of keyboard shortcuts that can be used when working with objects is available here. Resize objects To change the autoshape/image/chart/table/text box size, drag small squares situated on the object edges. To maintain the original proportions of the selected object while resizing, hold down the Shift key and drag one of the corner icons. To specify the precise width and height of a chart, select it on a slide and use the Size section of the right sidebar that will be activated. To specify the precise dimensions of an image or autoshape, right-click the necessary object on the slide and select the Image/Shape Advanced Settings option from the menu. Specify necessary values on the Size tab of the Advanced Settings window and press OK. Reshape autoshapes When modifying some shapes, for example Figured arrows or Callouts, the yellow diamond-shaped icon is also available. It allows to adjust some aspects of the shape, for example, the length of the head of an arrow. Move objects To alter the autoshape/image/chart/table/text box position, use the icon that appears after hovering your mouse cursor over the object. Drag the object to the necessary position without releasing the mouse button. To move the object by the one-pixel increments, hold down the Ctrl key and use the keybord arrows. To move the object strictly horizontally/vertically and prevent it from moving in a perpendicular direction, hold down the Shift key when dragging. To specify the precise position of an image, right-click it on a slide and select the Image Advanced Settings option from the menu. Specify necessary values in the Position section of the Advanced Settings window and press OK. Rotate objects To manually rotate an autoshape/image/text box, hover the mouse cursor over the rotation handle and drag it clockwise or counterclockwise. To constrain the rotation angle to 15 degree increments, hold down the Shift key while rotating. To rotate the object by 90 degrees counterclockwise/clockwise or flip the object horizontally/vertically you can use the Rotation section of the right sidebar that will be activated once you select the necessary object. To open it, click the Shape settings or the Image settings icon to the right. Click one of the buttons: to rotate the object by 90 degrees counterclockwise to rotate the object by 90 degrees clockwise to flip the object horizontally (left to right) to flip the object vertically (upside down) It's also possible to right-click the object, choose the Rotate option from the contextual menu and then use one of the available rotation options. To rotate the object by an exactly specified angle, click the Show advanced settings link at the right sidebar and use the Rotation tab of the Advanced Settings window. Specify the necessary value measured in degrees in the Angle field and click OK."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Create a new presentation or open an existing one", 
        "body": "To create a new presentation In the online editor click the File tab of the top toolbar, select the Create New option. In the desktop editor in the main program window, select the Presentation menu item from the Create new section of the left sidebar - a new file will open in a new tab, when all the necessary changes are made, click the Save icon in the upper left corner or switch to the File tab and choose the Save as menu item. in the file manager window, select the file location, specify its name, choose the format you want to save the presentation to (PPTX, Presentation template (POTX), ODP, OTP, PDF or PDFA) and click the Save button. To open an existing presentation In the desktop editor in the main program window, select the Open local file menu item at the left sidebar, choose the necessary presentation from the file manager window and click the Open button. You can also right-click the necessary presentation in the file manager window, select the Open with option and choose the necessary application from the menu. If the office documents files are associated with the application, you can also open presentations by double-clicking the file name in the file explorer window. All the directories that you have accessed using the desktop editor will be displayed in the Recent folders list so that you can quickly access them afterwards. Click the necessary folder to select one of the files stored in it. To open a recently edited presentation In the online editor click the File tab of the top toolbar, select the Open Recent... option, choose the presentation you need from the list of recently edited documents. In the desktop editor in the main program window, select the Recent files menu item at the left sidebar, choose the presentation you need from the list of recently edited documents. To open the folder where the file is stored in a new browser tab in the online version, in the file explorer window in the desktop version, click the Open file location icon on the right side of the editor header. Alternatively, you can switch to the File tab of the top toolbar and select the Open file location option."
    },
   {
        "id": "UsageInstructions/PreviewPresentation.htm", 
        "title": "Preview your presentation", 
        "body": "Start the preview To preview your currently edited presentation, you can: click the Start slideshow icon at the Home tab of the top toolbar or on the left side of the status bar, or select a certain slide within the slide list on the left, right-click it and choose the Start Slideshow option from the contextual menu. The preview will start from the currently selected slide. You can also click the arrow next to the Start slideshow icon at the Home tab of the top toolbar and select one of the available options: Show from Beginning - to start the preview from the very first slide, Show from Current slide - to start the preview from the currently selected slide, Show presenter view - to start the preview in the Presenter mode that allows to demonstrate the presentation to your audience without slide notes while viewing the presentation with the slide notes on a different monitor. Show Settings - to open a settings window that allows to set only one option: Loop continuously until 'Esc' is pressed. Check this option if necessary and click OK. If you enable this option, the presentation will be displayed until you press the Escape key on the keyboard, i.e. when the last slide of the presentation is reached, you will be able to go to the first slide again etc. If you disable this option, once the last slide of the presentation is reached, a black screen appears informing you that the presentation is finished and you can exit from the Preview. Use the Preview mode In the Preview mode, you can use the following controls at the bottom left corner: the Previous slide button allows you to return to the preceding slide. the Pause presentation button allows you to stop previewing. When the button is pressed, it turns into the button. the Start presentation button allows you to resume previewing. When the button is pressed, it turns into the button. the Next slide button allows you to advance the following slide. the Slide number indicator displays the current slide number as well as the overall number of slides in the presentation. To go to a certain slide in the preview mode, click on the Slide number indicator, enter the necessary slide number in the opened window and press Enter. the Full screen button allows you to switch to full screen mode. the Exit full screen button allows you to exit full screen mode. the Close slideshow button allows you to exit the preview mode. You can also use the keyboard shortcuts to navigate between the slides in the preview mode. Use the Presenter mode Note: in the desktop version, the presenter mode can be activated only if the second monitor is connected. In the Presenter mode, you can view your presentations with slide notes in a separate window, while demonstrating it without notes on a different monitor. The notes for each slide are displayed below the slide preview area. To navigate between slides you can use the and buttons or click slides in the list on the left. The hidden slide numbers are crossed out in the slide list on the left. If you wish to show a slide marked as hidden to others, just click it in the slide list on the left - the slide will be displayed. You can use the following controls below the slide preview area: the Timer displays the elapsed time of the presentation in the hh.mm.ss format. the Pause presentation button allows you to stop previewing. When the button is pressed, it turns into the button. the Start presentation button allows you to resume previewing. When the button is pressed, it turns into the button. the Reset button allows to reset the elapsed time of the presentation. the Previous slide button allows you to return to the preceding slide. the Next slide button allows you to advance the following slide. the Slide number indicator displays the current slide number as well as the overall number of slides in the presentation. the Pointer button allows you to highlight something on the screen when showing the presentation. When this option is enabled, the button looks like this: . To point to some objects hover your mouse pointer over the slide preview area and move the pointer around the slide. The pointer will look the following way: . To disable this option, click the button once again. the End slideshow button allows you to exit the Presenter mode."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Save/print/download your presentation", 
        "body": "Saving By default, online Рresentation Editor automatically saves your file each 2 seconds when you work on it preventing your data loss in case of the unexpected program closing. If you co-edit the file in the Fast mode, the timer requests for updates 25 times a second and saves the changes if they have been made. When the file is being co-edited in the Strict mode, changes are automatically saved at 10-minute intervals. If you need, you can easily select the preferred co-editing mode or disable the Autosave feature on the Advanced Settings page. To save your presentation manually in the current format and location, press the Save icon in the left part of the editor header, or use the Ctrl+S key combination, or click the File tab of the top toolbar and select the Save option. Note: in the desktop version, to prevent data loss in case of the unexpected program closing you can turn on the Autorecover option at the Advanced Settings page. In the desktop version, you can save the presentation with another name, in a new location or format, click the File tab of the top toolbar, select the Save as... option, choose one of the available formats depending on your needs: PPTX, ODP, PDF, PDFA. You can also choose the Рresentation template (POTX or OTP) option. Downloading In the online version, you can download the resulting presentation onto your computer hard disk drive, click the File tab of the top toolbar, select the Download as... option, choose one of the available formats depending on your needs: PPTX, PDF, ODP, POTX, PDF/A, OTP. Saving a copy In the online version, you can save a copy of the file on your portal, click the File tab of the top toolbar, select the Save Copy as... option, choose one of the available formats depending on your needs: PPTX, PDF, ODP, POTX, PDF/A, OTP, select a location of the file on the portal and press Save. Printing To print out the current presentation, click the Print icon in the left part of the editor header, or use the Ctrl+P key combination, or click the File tab of the top toolbar and select the Print option. It's also possible to print the selected slides using the Print Selection option from the contextual menu. In the desktop version, the file will be printed directly. In the online version, a PDF file will be generated on the basis of the presentation. You can open and print it out, or save onto your computer hard disk drive or removable medium to print it out later. Some browsers (e.g. Chrome and Opera) support direct printing."
    },
   {
        "id": "UsageInstructions/SetSlideParameters.htm", 
        "title": "Set slide parameters", 
        "body": "To customize your presentation, you can select a theme, color scheme, slide size and orientation for the entire presentation, change the background fill or slide layout for each separate slide, apply transitions between the slides. It's also possible to add explanatory notes to each slide that can be helpful when demonstrating the presentation in the Presenter mode. Themes allow you to quickly change the presentation design, notably the slides background appearance, predefined fonts for titles and texts and the color scheme that is used for the presentation elements. To select a theme for the presentation, click on the necessary predefined theme from the themes gallery on the right side of the top toolbar Home tab. The selected theme will be applied to all the slides if you have not previously selected certain slides to apply the theme to. To change the selected theme for one or more slides, you can right-click the selected slides in the list on the left (or right-click a slide in the editing area), select the Change Theme option from the contextual menu and choose the necessary theme. Color Schemes affect the predefined colors used for the presentation elements (fonts, lines, fills etc.) and allow you to maintain color consistency throughout the entire presentation. To change a color scheme, click the Change color scheme icon at the Home tab of the top toolbar and select the necessary scheme from the drop-down list. The selected color scheme will be highlighted in the list and applied to all the slides. To change a slide size for all the slides in the presentation, click the Select slide size icon at the Home tab of the top toolbar and select the necessary option from the drop-down list. You can select: one of the two quick-access presets - Standard (4:3) or Widescreen (16:9), the Advanced Settings option that opens the Slide Size Settings window where you can select one of the available presets or set a Custom size specifying the desired Width and Height values. The available presets are: Standard (4:3), Widescreen (16:9), Widescreen (16:10), Letter Paper (8.5x11 in), Ledger Paper (11x17 in), A3 Paper (297x420 mm), A4 Paper (210x297 mm), B4 (ICO) Paper (250x353 mm), B5 (ICO) Paper (176x250 mm), 35 mm Slides, Overhead, Banner. The Slide Orientation menu allows to change the currently selected orientation type. The default orientation type is Landscape that can be switched to Portrait. To change a background fill: in the slide list on the left, select the slides you want to apply the fill to. Or click at any blank space within the currently edited slide in the slide editing area to change the fill type for this separate slide. at the Slide settings tab of the right sidebar, select the necessary option: Color Fill - select this option to specify the solid color you want to apply to the selected slides. Gradient Fill - select this option to fill the slide with two colors which smoothly change from one to another. Picture or Texture - select this option to use an image or a predefined texture as the slide background. Pattern - select this option to fill the slide with a two-colored design composed of regularly repeated elements. No Fill - select this option if you don't want to use any fill. For more detailed information on these options please refer to the Fill objects and select colors section. Transitions help make your presentation more dynamic and keep your audience's attention. To apply a transition: in the slide list on the left, select the slides you want to apply a transition to, choose a transition in the Effect drop-down list on the Slide settings tab, Note: to open the Slide settings tab you can click the Slide settings icon on the right or right-click the slide in the slide editing area and select the Slide Settings option from the contextual menu. adjust the transition properties: choose a transition variation, duration and the way to advance slides, click the Apply to All Slides button if you want to apply the same transition to all slides in the presentation. For more detailed information on these options please refer to the Apply transitions section. To change a slide layout: in the slide list on the left, select the slides you want to apply a new layout to, click the Change slide layout icon at the Home tab of the top toolbar, select the necessary layout from the menu. Alternatively, you can right-click the necessary slide in the list on the left or in the editing area, select the Change Layout option from the contextual menu and choose the necessary layout. Note: currently, the following layouts are available: Title Slide, Title and Content, Section Header, Two Content, Comparison, Title Only, Blank, Content with Caption, Picture with Caption, Title and Vertical Text, Vertical Title and Text. To add objects to a slide layout: click the Change slide layout icon and select a layout you want to add an object to, using the Insert tab of the top toolbar, add the necessary object to the slide (image, table, chart, shape), then right-click on this object and select Add to Layout option, at the Home tab click Change slide layout and apply the changed layout. Selected objects will be added to the current theme's layout. Note: objects placed on a slide this way cannot be selected, resized, or moved. To return the slide layout to its original state: in the slide list on the left, select the slides that you want to return to the default state, Note: hold down the Ctrl key and select one slide at a time to select several slides at once, or hold down the Shift key to select all slides from the current to the selected. right-click on one of the slides and select the Reset slide option in the context menu, All text frames and objects located on slides will be reset and situated in accordinance with the slide layout. To add notes to a slide: in the slide list on the left, select the slide you want to add a note to, click the Click to add notes caption below the slide editing area, type in the text of your note. Note: you can format the text using the icons at the Home tab of the top toolbar. When you start the slideshow in the Presenter mode, you will be able to see all the slide notes below the slide preview area."
    },
   {
        "id": "UsageInstructions/ViewPresentationInfo.htm", 
        "title": "View presentation information", 
        "body": "To access the detailed information about the currently edited presentation, click the File tab of the top toolbar and select the Presentation Info option. General Information The spreadsheet information includes a number of the file properties which describe the spreadsheet. Some of these properties are updated automatically, and some of them can be edited. Location - the folder in the Documents module where the file is stored. Owner - the name of the user who have created the file. Uploaded - the date and time when the file has been created. These properties are available in the online version only. Title, Subject, Comment - these properties allow to simplify your documents classification. You can specify the necessary text in the properties fields. Last Modified - the date and time when the file was last modified. Last Modified By - the name of the user who have made the latest change in the presentation if it has been shared and it can be edited by several users. Application - the application the presentation was created with. Author - the person who have created the file. You can enter the necessary name in this field. Press Enter to add a new field that allows to specify one more author. If you changed the file properties, click the Apply button to apply the changes. Note: Online Editors allow you to change the presentation title directly from the editor interface. To do that, click the File tab of the top toolbar and select the Rename... option, then enter the necessary File name in a new window that opens and click OK. Permission Information In the online version, you can view the information about permissions to the files stored in the cloud. Note: this option is not available for users with the Read Only permissions. To find out, who have rights to view or edit the presentation, select the Access Rights... option at the left sidebar. You can also change currently selected access rights by pressing the Change access rights button in the Persons who have rights section. To close the File pane and return to presentation editing, select the Close Menu option."
    }
]