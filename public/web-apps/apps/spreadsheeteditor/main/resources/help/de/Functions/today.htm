﻿<!DOCTYPE html>
<html>
	<head>
		<title>HEUTE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>HEUTE-Funktion</h1>
			<p>Die Funktion <b>HEUTE</b> gehört zur Gruppe der Datums- und Uhrzeitfunktionen. Mit dieser Funktion lässt sich das aktuelle Datum im Format <em>MM/TT/JJ</em> wiedergeben. Für die Syntax dieser Funktion sind <b>keine</b> Argumente erforderlich.</p>
			<p>Die Syntax der Funktion <b>HEUTE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>HEUTE()</em></b></p> 
			<p>-Funktion anwenden:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>HEUTE</b>.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="HEUTE-Funktion" src="../images/today.png" /></p>
		</div>
	</body>
</html>