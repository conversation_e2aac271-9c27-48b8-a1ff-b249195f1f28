﻿<!DOCTYPE html>
<html>
	<head>
		<title>MTRANS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>MTRANS-Funktion</h1>
			<p>Die Funktion <b>MTRANS</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Mit der Funktion wird ein vertikaler Zellbereich als horizontaler Bereich zurückgegeben oder umgekehrt.</p>
			<p>Die Formelsyntax der Funktion <b>MTRANS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>MTRANS(Matrix)</em></b></p> 
			<p><em>Dabei ist</em></p> 
			<p style="text-indent: 50px;"><em><b>Matrix</b></em> ist die Matrix oder der Zellbereich in einem Arbeitsblatt, die bzw. den Sie transponieren möchten.</p>
			<p>Anwenden der Funktion <b>MTRANS</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus. <!--<p class="note"> the selected range must contain the same number of rows as the original range has columns and the same number of columns as the original range has rows.</p>--></li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>MTRANS</b>.</li>
			<li>Wählen Sie den Zellenbereich mit der Maus aus oder geben Sie das gewünschte Argument manuell ein, in der Form A1:B2 usw.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="MTRANS-Funktion" src="../images/transpose.png" /></p>
		</div>
	</body>
</html>