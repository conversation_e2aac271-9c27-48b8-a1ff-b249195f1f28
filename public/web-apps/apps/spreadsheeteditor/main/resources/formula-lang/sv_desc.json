{"DATE": {"a": "(<PERSON><PERSON>; m<PERSON>na<PERSON>; dag)", "d": "Return<PERSON>r numret som representerar datumet i datum-tidkoden"}, "DATEDIF": {"a": "(startdatum; stoppdatum; enhet)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> ant<PERSON>, m<PERSON><PERSON><PERSON> eller år mellan två datum"}, "DATEVALUE": {"a": "(datumtext)", "d": "Konverterar ett datum i form av text till ett tal som representerar datumet i datum-tidskod"}, "DAY": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> dagen i m<PERSON>, ett tal mellan 1 och 31."}, "DAYS": {"a": "(stoppdatum; startdatum)", "d": "<PERSON><PERSON><PERSON> antalet dagar mellan två datum."}, "DAYS360": {"a": "(startdatum; stoppdatum; [metod])", "d": "<PERSON><PERSON><PERSON> antalet dagar mellan två datum baserat på ett år med 360 dagar (tolv 30-da<PERSON><PERSON> månader)."}, "EDATE": {"a": "(start<PERSON>tum; månader)", "d": "<PERSON><PERSON><PERSON> se<PERSON>ret till det datum som ligger angivet antal månader innan eller efter startdatumet"}, "EOMONTH": {"a": "(start<PERSON>tum; månader)", "d": "Returnerar ett serienummer till den sista dagen i månaden före eller efter ett angivet antal månader"}, "HOUR": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> timmen som ett tal mellan 0 (12:00) och 23 (11:00)."}, "ISOWEEKNUM": {"a": "(date)", "d": "<PERSON><PERSON><PERSON> (ISO) för ett visst datum"}, "MINUTE": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> minuten, ett tal från 0 till 59."}, "MONTH": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON>, ett tal mellan 1 (januari) och 12 (december)."}, "NETWORKDAYS": {"a": "(startdatum; stoppdatum; [lediga])", "d": "<PERSON><PERSON><PERSON> antalet hela arb<PERSON>gar mellan två datum"}, "NETWORKDAYS.INTL": {"a": "(startdatum; stoppdatum; [helg]; [lediga])", "d": "<PERSON><PERSON>r antalet hela arb<PERSON>gar mellan två datum med egna helgparametrar."}, "NOW": {"a": "()", "d": "<PERSON><PERSON>r dagens datum och aktuell tid formaterat som datum och tid."}, "SECOND": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON>, ett tal från 0 till 59."}, "TIME": {"a": "(timme; minut; sekund)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> timmar, minuter och sekunder angivna som tal till serienummer, formaterade med tidsformat."}, "TIMEVALUE": {"a": "(tidstext)", "d": "Konverterar en texttid till ett serienummer för en tid, ett tal från 0 (00:00:00) till  0,999988426 (23:59:59). Formatera talet med ett tidsformat när du angett formeln"}, "TODAY": {"a": "()", "d": "Returnerar dagens datum formaterat som ett datum."}, "WEEKDAY": {"a": "(serienummer; [returtyp])", "d": "Returnerar ett tal mellan 1 och 7 som identifierar veckodagen för ett datum."}, "WEEKNUM": {"a": "(tal; [returtyp])", "d": "Omvandlar ett serienummer till ett veckonummer"}, "WORKDAY": {"a": "(startdatum; dagar; [lediga])", "d": "<PERSON><PERSON>r serienumret till datumet före eller efter ett givet antal arbetsdagar"}, "WORKDAY.INTL": {"a": "(startdatum; dagar; [helg]; [lediga])", "d": "Returnerar datumets serienummer före eller efter ett angivet antal arbetsdagar med egna helgparametrar."}, "YEAR": {"a": "(serienummer)", "d": "<PERSON><PERSON><PERSON> <PERSON><PERSON> för ett datum, ett heltal mellan 1900-9999."}, "YEARFRAC": {"a": "(startdatum; stoppdatum; [bas])", "d": "Returnerar ett tal som representerar antal hela dagar av ett år mellan startdatum och stoppdatum "}, "BESSELI": {"a": "(x; n)", "d": "Returnerar den modifierade Bessel-funktionen"}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON>"}, "BESSELK": {"a": "(x; n)", "d": "Returnerar den modifierade Bessel-funktionen"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON>"}, "BIN2DEC": {"a": "(tal)", "d": "Konverterar ett binärt tal till ett decimalt"}, "BIN2HEX": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett binärt tal till ett hexadecimalt"}, "BIN2OCT": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett binärt tal till ett oktalt"}, "BITAND": {"a": "(tal1; tal2)", "d": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>' bitvis för två tal."}, "BITLSHIFT": {"a": "(tal; flytta_antal)", "d": "Return<PERSON>r ett tal som flyttats till vänster med angivet flytta_antal bitar."}, "BITOR": {"a": "(tal1; tal2)", "d": "<PERSON><PERSON><PERSON> '<PERSON><PERSON>' bitvis för två tal."}, "BITRSHIFT": {"a": "(tal; flytta_antal)", "d": "<PERSON><PERSON>r ett tal som flyttats till höger med angivet flytta_antal bitar."}, "BITXOR": {"a": "(tal1; tal2)", "d": "Returnerar '<PERSON>k<PERSON><PERSON><PERSON> eller' bitvis för två tal."}, "COMPLEX": {"a": "(realdel; imaginärdel; [suffix])", "d": "Konverterar en real- och en imaginärkoefficient till ett komplext tal"}, "CONVERT": {"a": "(tal; ursprungsenhet; ny_enhet)", "d": "Konverterar ett tal från en enhet till en annan"}, "DEC2BIN": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett decimalt tal till ett binärt"}, "DEC2HEX": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett decimalt tal till ett hexadecimalt"}, "DEC2OCT": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett decimalt tal till oktalt"}, "DELTA": {"a": "(tal1; [tal2])", "d": "Testar om två värden är lika"}, "ERF": {"a": "(undre_gräns; [övre_gräns])", "d": "<PERSON><PERSON><PERSON> f<PERSON>en"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON> f<PERSON>en"}, "ERFC": {"a": "(x)", "d": "Returnerar den komplimenterande felfunktionen"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returnerar den komplimenterande felfunktionen"}, "GESTEP": {"a": "(tal; [steg])", "d": "Testar om ett tal är större än ett gränsvärde"}, "HEX2BIN": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett hexadecimalt tal till ett binärt"}, "HEX2DEC": {"a": "(tal)", "d": "Konverterar ett hexadecimalt tal till ett decimalt"}, "HEX2OCT": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett hexadecimalt tal till ett oktalt"}, "IMABS": {"a": "(ital)", "d": "Returnerar absolutvärdet av ett komplext tal"}, "IMAGINARY": {"a": "(ital)", "d": "Returnerar den imaginära koefficienten av ett komplext tal"}, "IMARGUMENT": {"a": "(ital)", "d": "Returnerar argumentet theta, en vinkel uttryckt i radianer"}, "IMCONJUGATE": {"a": "(ital)", "d": "Returnerar det komplexa konjugatet till ett komplext tal"}, "IMCOS": {"a": "(ital)", "d": "Returnerar cosinus av ett komplext tal"}, "IMCOSH": {"a": "(tal)", "d": "Returnerar hyperbolisk cosinus för ett komplext tal."}, "IMCOT": {"a": "(tal)", "d": "Returnerar cotangens för ett komplext tal."}, "IMCSC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> cosekant för ett komplext tal."}, "IMCSCH": {"a": "(tal)", "d": "Returnerar hyperbolisk cosekant för ett komplext tal."}, "IMDIV": {"a": "(ital1; ital2)", "d": "Returnerar kvoten av två komplexa tal"}, "IMEXP": {"a": "(ital)", "d": "Returnerar e upphöjt till ett komplext tal"}, "IMLN": {"a": "(ital)", "d": "Returnerar den naturliga logaritmen av ett komplext tal"}, "IMLOG10": {"a": "(ital)", "d": "Returnerar 10-logarit<PERSON> av ett komplext tal"}, "IMLOG2": {"a": "(ital)", "d": "Returnerar 2-logaritmen av ett komplext tal"}, "IMPOWER": {"a": "(ital; tal)", "d": "Returnerar ett komplext tal upphöjt till en heltalsexponent"}, "IMPRODUCT": {"a": "(ital1; [ital2]; ...)", "d": "Returnerar produkten av 1 till 255 komplexa tal"}, "IMREAL": {"a": "(ital)", "d": "Returnerar realkoefficienten av ett komplext tal"}, "IMSEC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> sekant för ett komplext tal."}, "IMSECH": {"a": "(tal)", "d": "Returnerar hyperbolisk sekant för ett komplext tal."}, "IMSIN": {"a": "(ital)", "d": "Returnerar sinus av ett komplext tal"}, "IMSINH": {"a": "(tal)", "d": "Returnerar hyperbolisk sinus för ett komplext tal."}, "IMSQRT": {"a": "(ital)", "d": "Returnerar kvadratroten av ett komplext tal"}, "IMSUB": {"a": "(ital1; ital2)", "d": "<PERSON><PERSON><PERSON> differensen mellan två komplexa tal"}, "IMSUM": {"a": "(ital1; [ital2]; ...)", "d": "<PERSON><PERSON>r summan av komplexa tal"}, "IMTAN": {"a": "(tal)", "d": "Returnerar tangens för ett komplext tal."}, "OCT2BIN": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett oktalt tal till ett binärt"}, "OCT2DEC": {"a": "(tal)", "d": "Konverterar ett oktalt tal till ett decimalt"}, "OCT2HEX": {"a": "(tal; [antal_siffror])", "d": "Konverterar ett oktalt tal till ett hexadecimalt"}, "DAVERAGE": {"a": "(databas; fält; villkorsområde)", "d": "Beräknar medelvärdet för en kolumns värden i en lista eller databas enligt de villkor du angivit."}, "DCOUNT": {"a": "(databas; fält; villkorsområde)", "d": "Räknar antalet celler i fältet (kolumnen) med poster i databasen som innehåller tal som matchar de villkor du angivit."}, "DCOUNTA": {"a": "(databas; fält; villkorsområde)", "d": "R<PERSON>k<PERSON> icketomma celler i fältet (kolumnen) med poster i databasen som matchar villkoren du anger."}, "DGET": {"a": "(databas; fält; villkorsområde)", "d": "Tar fram en enda post ur en databas enligt de villkor du anger."}, "DMAX": {"a": "(databas; fält; villkorsområde)", "d": "<PERSON><PERSON>r det största talet i ett fält (en kolumn) med poster, i från databasen, som stämmer överens med de villkor du angav."}, "DMIN": {"a": "(databas; fält; villkorsområde)", "d": "<PERSON><PERSON>r det minsta talet i ett fält (kolumn) med poster, i från databasen, som stämmer överens med de villkor du angav."}, "DPRODUCT": {"a": "(databas; fält; villkorsområde)", "d": "Multiplicerar värdena i fältet (kolumnen), med poster, i databasen som matchar de villkor du angav."}, "DSTDEV": {"a": "(databas; fält; villkorsområde)", "d": "Uppskattar standardavvikelsen baserad på ett sampel från valda databasposter."}, "DSTDEVP": {"a": "(databas; fält; villkorsområde)", "d": "Beräknar standardavvikelsen baserad på hela populationen av valda databasposter."}, "DSUM": {"a": "(databas; fält; villkorsområde)", "d": "Adderar talen i fältet (kolumnen) av poster i en databas som matchar villkoren du anger."}, "DVAR": {"a": "(databas; fält; villkorsområde)", "d": "Uppskattar variansen baserad på ett sampel från valda databasposter."}, "DVARP": {"a": "(databas; fält; villkorsområde)", "d": "Beräknar variansen på hela populationen av valda databasposter."}, "CHAR": {"a": "(tal)", "d": "<PERSON><PERSON>r tecknet som anges av koden från datorns teckenuppsättning."}, "CLEAN": {"a": "(text)", "d": "Tar bort alla icke-utskrivbara tecken från texten."}, "CODE": {"a": "(text)", "d": "Returnerar en numerisk kod för det första tecknet i en textsträng, i teckenuppsättningen som används av din dator."}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Sammanfogar flera textsträngar till en."}, "CONCAT": {"a": "(text1; ...)", "d": "Sammanfogar en lista eller ett intervall av textsträngar"}, "DOLLAR": {"a": "(tal; [decimaler])", "d": "Konverterar ett tal till text med valutaformat."}, "EXACT": {"a": "(text1; text2)", "d": "Kontrollerar om två textsträngar är exakt likadana och returnerar värdet SANT eller FALSKT. EXAKT är skifteslägeskänsligt."}, "FIND": {"a": "(sök; inom_text; [startpos])", "d": "Returnerar startpositionen för en textsträng inom en annan textsträng. SÖK är skiftlägeskänsligt."}, "FINDB": {"a": "(sök; inom_text; [startpos])", "d": "<PERSON><PERSON><PERSON> efter en textsträng i en annan textsträng och returnerar numret på startpositionen för den första strängen från det första tecknet i den andra textsträngen, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska"}, "FIXED": {"a": "(tal; [decimaler]; [ej_komma])", "d": "<PERSON>dar av ett tal till det angivna antalet decimaler och returnerar resultatet som text med eller utan kommatecken."}, "LEFT": {"a": "(text; [antal_tecken])", "d": "<PERSON><PERSON><PERSON> det angivna antalet tecken från början av en textsträng."}, "LEFTB": {"a": "(text; [antal_tecken])", "d": "Returnerar det första tecknet eller tecknen i en textsträng, baserat på antal angivna byte, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska"}, "LEN": {"a": "(text)", "d": "Return<PERSON>r antalet tecken i en textsträng."}, "LENB": {"a": "(text)", "d": "Returnerar antalet byte som används för att representera tecknen i en textsträng, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska"}, "LOWER": {"a": "(text)", "d": "Konverterar samtliga bokstäver i en text till gemener."}, "MID": {"a": "(text; startpos; antal_tecken)", "d": "<PERSON><PERSON>r tecknen från mitten av en textsträng med en startposition och längd som du anger."}, "MIDB": {"a": "(text; startpos; antal_tecken)", "d": "Returnerar ett angivet antal tecken ur en textsträng med bö<PERSON><PERSON> från en angiven position, baserat på antalet byte du anger, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska"}, "NUMBERVALUE": {"a": "(text; [decimaltecken]; [tusentalsavgränsare])", "d": "Konverterar text till ett tal oberoende av språk."}, "PROPER": {"a": "(text)", "d": "Konverterar en textsträng: ändrar första bokstaven i varje ord till versal och konverterar alla andra bokstäver till gemener"}, "REPLACE": {"a": "(gammal_text; startpos; antal_tecken; ny_text)", "d": "<PERSON><PERSON><PERSON><PERSON> del av textsträng med en annan."}, "REPLACEB": {"a": "(gammal_text; startpos; antal_tecken; ny_text)", "d": "<PERSON><PERSON><PERSON><PERSON> en del av en textsträng, baserat på det antal tecken du anger, med en annan textsträng, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska"}, "REPT": {"a": "(text; antal_gånger)", "d": "Upprepar en text ett bestämt antal gånger. Använd REP för att fylla en cell med samma textsträng flera gånger."}, "RIGHT": {"a": "(text; [antal_tecken])", "d": "<PERSON><PERSON>r det angivna antalet tecken från slutet av en textsträng."}, "RIGHTB": {"a": "(text; [antal_tecken])", "d": "Return<PERSON>r det eller de sista tecknen i en texts<PERSON>äng, baserat på det antal byte du anger, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska"}, "SEARCH": {"a": "(sök; inom; [startpos])", "d": "Returnerar antalet tecken vilka ett givet tecken eller textsträng söker efter först, läser från höger till vänster (ej skiftlägeskänslig)."}, "SEARCHB": {"a": "(sök; inom; [startpos])", "d": "<PERSON><PERSON><PERSON> efter en textsträng i en annan textsträng och returnerar numret på startpositionen för den första strängen från det första tecknet i den andra textsträngen, är avsedd att användas med språk som använder DBCS-teckenuppsättningen (Double-Byte Character Set) -  japanska, kinesiska och koreanska"}, "SUBSTITUTE": {"a": "(text; gammal_text; ny_text; [antal_förekomster])", "d": "<PERSON><PERSON><PERSON><PERSON> gammal text med ny text i en textsträng."}, "T": {"a": "(värde)", "d": "Kontrollerar om ett värde är text och returnerar texten om det är det, annars returneras dubbla citattecken (ingen text)."}, "TEXT": {"a": "(värde; format)", "d": "Konverterar ett värde till text i ett specifikt talformat."}, "TEXTJOIN": {"a": "(avg<PERSON><PERSON><PERSON><PERSON>; ignorera_tom; text1; ...)", "d": "En avgränsare används för att sammanfoga en lista eller ett intervall av textsträngar"}, "TRIM": {"a": "(text)", "d": "Tar bort alla blanksteg från en textsträng förutom enkla blanksteg mellan ord."}, "UNICHAR": {"a": "(tal)", "d": "Returnerar Unicode-tecknet som det numeriska värdet refererar till."}, "UNICODE": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> talet (kodpunkten) som motsvarar det första tecknet i texten."}, "UPPER": {"a": "(text)", "d": "Konverterar text till versaler."}, "VALUE": {"a": "(text)", "d": "Konverterar en textsträng som representerar ett tal till ett tal"}, "AVEDEV": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar medelvärdet från de absoluta avvikelsernas datapunkter från deras medelvärde. Argument kan vara tal eller namn, matriser eller referenser som innehåller tal."}, "AVERAGE": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON>r medelvärdet av argumenten som kan vara tal, namn, matriser eller referenser som innehåller tal."}, "AVERAGEA": {"a": "(värde1; [värde2]; ...)", "d": "Returnerar medelvärdet (aritmetiskt medelvärde) av argumenten, ber<PERSON><PERSON><PERSON> text och FALSKT i argument som 0; SANT utvärderas som 1. Argument kan vara tal, namn, matriser eller referenser."}, "AVERAGEIF": {"a": "(o<PERSON>r<PERSON><PERSON>; vill<PERSON>; [medel<PERSON><PERSON><PERSON><PERSON>])", "d": "Hittar medelvärdet (aritmetiskt medelvärde) för de celler som anges av en given uppsättning villkor"}, "AVERAGEIFS": {"a": "(medel<PERSON><PERSON><PERSON><PERSON>; villkor<PERSON>mr<PERSON>de; villkor; ...)", "d": "Hittar medelvärdet (aritmetiskt medelvärde) för de celler som anges av en given uppsättning villkor"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Returnera den kumulativa betafördelningsfunktionen."}, "BETAINV": {"a": "(sannolikhet; alfa; beta; [A]; [B])", "d": "Returnera inversen till den kumulativa betafördelningsfunktionen (BETAFÖRD)"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativ; [A]; [B])", "d": "Returnerar funktionen för <PERSON>tsfördelning."}, "BETA.INV": {"a": "(Sannolikhet; alfa; beta; [A]; [B])", "d": "Returnerar inversen till den kumulativa betafördelningsfunktionen (BETA.FÖRD)."}, "BINOMDIST": {"a": "(antal_l; försök; sannolikhet_l; kumulativ)", "d": "Returnera den individuella binomialfördelningen"}, "BINOM.DIST": {"a": "(antal_l; försök; sannolikhet_l; kumulativ)", "d": "Returnerar den individuella binomialfördelningen."}, "BINOM.DIST.RANGE": {"a": "(f<PERSON><PERSON><PERSON><PERSON>; sannolikhet_l; antal_l; [antal_l2])", "d": "Return<PERSON>r sannolikheten för ett testresultat med en binomialfördelning."}, "BINOM.INV": {"a": "(försök; sannolikhet_l; alfa)", "d": "Returnerar det minsta värdet för vilket den kumulativa binomialfördelningen är större än eller lika med ett villkorsvärde."}, "CHIDIST": {"a": "(x; frihetsgrader)", "d": "Returnera den ensidiga sannolikheten av chi2-fördelningen"}, "CHIINV": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnera inversen till chi2-fördelningen"}, "CHITEST": {"a": "(observerat_omr; förväntat_omr)", "d": "Returnerar oberoendetesten: värdet från chi2-fördelningen för statistiken och lämpligt antal frihetsgrader"}, "CHISQ.DIST": {"a": "(x; frihetsgrad; kumulativ)", "d": "Returnerar den vänstersidiga sannolikheten för chi2-fördelningen."}, "CHISQ.DIST.RT": {"a": "(x; fri<PERSON>tsgrad)", "d": "Returnerar den högersidiga sannolikheten för chi2-fördelningen."}, "CHISQ.INV": {"a": "(sannolikhet; frihetsgrad)", "d": "Returnerar inversen till den vänstersidiga sannolikheten för chi2-fördelningen."}, "CHISQ.INV.RT": {"a": "(sannolikhet; frihetsgrad)", "d": "Returnerar inversen till den högersidiga sannolikheten för chi2-fördelningen."}, "CHISQ.TEST": {"a": "(observerat_omr; förväntat_omr)", "d": "Returnerar oberoendetesten: värdet från chi2-fördelningen för statistiken och lämpligt antal frihetsgrader."}, "CONFIDENCE": {"a": "(alfa; standardavvikelse; storlek)", "d": "Returnera konfidensintervallet för en populations medelvärde med en normalfördelning"}, "CONFIDENCE.NORM": {"a": "(alfa; standardavvikelse; storlek)", "d": "Returnerar konfidensintervallet för en populations medelvärde, med normalfördelning."}, "CONFIDENCE.T": {"a": "(alfa; standardavvikelse; storlek)", "d": "<PERSON><PERSON><PERSON> konfidensintervallet för en <PERSON> medel<PERSON>rde, med students T-fördelning"}, "CORREL": {"a": "(matris1; matris2)", "d": "Returnerar korrelationskoefficienten mellan två datamängder."}, "COUNT": {"a": "(värde1; [värde2]; ...)", "d": "Räk<PERSON> antalet celler i ett område som innehåller tal"}, "COUNTA": {"a": "(värde1; [värde2]; ...)", "d": "Räk<PERSON> antalet celler i ett område som inte är tomma."}, "COUNTBLANK": {"a": "(område)", "d": "<PERSON><PERSON><PERSON><PERSON> antal tomma celler i ett angivet område."}, "COUNTIF": {"a": "(omr<PERSON><PERSON>; villkor)", "d": "<PERSON><PERSON><PERSON><PERSON> antalet celler som motsvarar givet villkor i ett område."}, "COUNTIFS": {"a": "(vill<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; vill<PERSON>; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> antalet celler som anges av en given uppsättning villkor"}, "COVAR": {"a": "(matris1; matris2)", "d": "<PERSON><PERSON><PERSON>, medelvärdet av produkterna av avvikelser för varje datapunktspar i två datamängder"}, "COVARIANCE.P": {"a": "(matris1; matris2)", "d": "Returnerar populationens kovarians, medelvärdet av produkterna av avvikelser för varje datapunktspar i två datamängder."}, "COVARIANCE.S": {"a": "(matris1; matris2)", "d": "Returnerar samplets kovarians, medelvärdet av produkterna av avvikelser för varje datapunktspar i två datamängder."}, "CRITBINOM": {"a": "(försök; sannolikhet_l; alfa)", "d": "Returnera det minsta värdet för vilket den kumulativa binomialfördelningen är större än eller lika med villkorsvärdet"}, "DEVSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar summan av kvadratavvikelserna av datapunkter från deras sampel medelvärde."}, "EXPONDIST": {"a": "(x; lambda; kumulativ)", "d": "Returnera exponentialfördelningen"}, "EXPON.DIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerar exponentialfördelningen."}, "FDIST": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnera den högersidiga F-sannolikhetsfördelningen (grad av skillnad) för två datamängder"}, "FINV": {"a": "(sannolikhet; frihetsgrader1; frihetsgrader2)", "d": "Returnera inversen till den högersidiga F-sannolikhetsfördelningen: om p = FFÖRD(x,...), då FINV(p,...) = x."}, "FTEST": {"a": "(matris1; matris2)", "d": "Returnerar resultatet av ett F-test, den tvåsidiga sannolikheten att varianserna i Matris1 och Matris2 inte skiljer sig åt markant"}, "F.DIST": {"a": "(x; frihetsgrad1; frihetsgrad2; kumulativ)", "d": "Returnerar den vänstersidiga F-sannolikhetsfördelningen (grad av skillnad) för två datamängder."}, "F.DIST.RT": {"a": "(x; frihetsgrad1; frihetsgrad2)", "d": "Returnerar den högersidiga F-sannolikhetsfördelningen (grad av skillnad) för två datamängder."}, "F.INV": {"a": "(sannolikhet; frihetsgrad1; frihetsgrad2)", "d": "Returnerar inversen till den vänstersidiga F-sannolikhetsfördelningen: om p = F.FÖRD(x,...), då F.INV(p,...) = x."}, "F.INV.RT": {"a": "(sannolikhet; frihetsgrad1; frihetsgrad2)", "d": "Returnerar inversen till den högersidiga F-sannolikhetsfördelningen: om p = F.FÖRD.RT(x,...), då F.INV.RT(p,...) = x."}, "F.TEST": {"a": "(matris1; matris2)", "d": "Returnerar resultatet av en F-test, den tvåsidiga sannolikheten att varianserna i Matris1 och Matris2 inte är markant olika."}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON>."}, "FISHERINV": {"a": "(y)", "d": "Returnerar inversen till Fisher-transformationen: om y = FISHER(x), då är FISHERINV(y) = x."}, "FORECAST": {"a": "(x; kända_y; kända_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> f<PERSON>, ett framtida värde längs en linjär trend genom att använda befintliga värden"}, "FORECAST.ETS": {"a": "(m<PERSON><PERSON>tum; värden; tidslinje; [säsongsberoende]; [färdigställande_av_data]; [aggregering])", "d": "Returnerar det beräknade värdet för ett specifikt framtida måldatum med exponentiell utjämning."}, "FORECAST.ETS.CONFINT": {"a": "(m<PERSON><PERSON><PERSON>; värden; tidslinje; [konfidensnivå]; [säsongsberoende]; [färdigställande_av_data]; [aggregering])", "d": "Returnerar ett konfidensintervall för prognosvärdet vid angivet måldatum."}, "FORECAST.ETS.SEASONALITY": {"a": "(värden; tidslinje; [färdigställande_av_data]; [aggregering])", "d": "Returnerar längden på det repetitiva mönster som identifieras för de angivna tidsserierna."}, "FORECAST.ETS.STAT": {"a": "(värden; tidslinje; statistisk_typ; [säsongsberoende]; [färdigställande_av_data]; [aggregering])", "d": "<PERSON><PERSON><PERSON> begärd statistik för prognosen."}, "FORECAST.LINEAR": {"a": "(x; kända_y; kända_x)", "d": "Beräknar eller förutsäger ett framtida värde längs en linjär trendlinje genom att använda redan existerande värden."}, "FREQUENCY": {"a": "(datamatris; fackmatris)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> hur ofta värden uppstår inom ett område med värden och returnerar en vertikal matris med tal som har ett element mer än en fackmatris."}, "GAMMA": {"a": "(x)", "d": "Returnerar värdet för gammafunktionen."}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerar gammafördelningen"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerar gammafördelningen."}, "GAMMAINV": {"a": "(sannolikhet; alfa; beta)", "d": "Returnera inversen till den kumulativa gammafördelningen: om p = GAMMAFÖRD(x,...), då GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(sannolikhet; alfa; beta)", "d": "Returnerar inversen till den kumulativa gammafördelningen: om p = GAMMA.FÖRD(x,...), då GAMMA.INV(p,...) = x."}, "GAMMALN": {"a": "(x)", "d": "Returnerar den naturliga logaritmen för gammafunktionen."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnerar den naturliga logaritmen för gammafunktionen."}, "GAUSS": {"a": "(x)", "d": "Returnerar 0,5 mindre än den kumulativa standardnormalfördelningen."}, "GEOMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det geometriska medelvärdet av en matris eller av ett område positiva numeriska data."}, "GROWTH": {"a": "(kända_y; [kända_x]; [nya_x]; [konst])", "d": "Returnerar tal i en exponentiell tillväxttrend som matchar kända datapunkter"}, "HARMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det harmoniska medelvärdet av ett dataområde med positiva tal: motsvarigheten av det aritmetiska medelvärdet av motsvarigheter."}, "HYPGEOM.DIST": {"a": "(sampel; antal_sampel; population; antal_population; kumulativ)", "d": "Returnerar den hypergeometriska fördelningen."}, "HYPGEOMDIST": {"a": "(sampel; antal_sampel; population; antal_population)", "d": "Returnera den hypergeometriska fördelningen"}, "INTERCEPT": {"a": "(kända_y; kända_x)", "d": "Beräknar punkten där en linje korsar y-axeln genom att använda en regressionslinje ritad genom de kända x- och y-värdena"}, "KURT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar en datamängds fördelning."}, "LARGE": {"a": "(matris; n)", "d": "Returnerar det n:te största värdet i en datamängd, t ex det femte största talet."}, "LINEST": {"a": "(kända_y; [kända_x]; [konst]; [statistik])", "d": "Returnerar statistik som beskriver en linjär trend som matchar kända datapunkter, genom att passa in en rak linje och använda minsta kvadrat-metoden"}, "LOGEST": {"a": "(kända_y; [kända_x]; [konst]; [statistik])", "d": "Returnerar statistik som beskriver en exponentiell kurva som matchar kända datapunkter"}, "LOGINV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnera inversen till den kumulativa lognormalfördelningsfunktionen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse"}, "LOGNORM.DIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse; kumulativ)", "d": "Returnerar lognormalfördelningen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse."}, "LOGNORM.INV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnerar den kumulativa lognormalfördelningsfunktionen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse."}, "LOGNORMDIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse)", "d": "Returnera den kumulativa lognormalfördelningen av x, där ln(x) normalfördelas med parametrarna Medelvärde och Standardavvikelse"}, "MAX": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det maximala värdet i en lista av argument. Ignorerar logiska värden och text."}, "MAXA": {"a": "(värde1; [värde2]; ...)", "d": "Returnerar det högsta värdet i en mängd värden. Ignorerar inte logiska värden och text."}, "MAXIFS": {"a": "(max_intervall; kriterie_intervall; kriterier; ...)", "d": "<PERSON><PERSON><PERSON> det högsta värdet bland celler som anges av en given uppsättning villkor eller kriterier"}, "MEDIAN": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON> medianen eller talet i mitten av de angivna talen."}, "MIN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det minsta värdet i en lista av argument. Ignorerar logiska värden och text."}, "MINA": {"a": "(värde1; [värde2]; ...)", "d": "Returnerar det lägsta värdet i en mängd värden. Ignorerar inte logiska värden och text."}, "MINIFS": {"a": "(minsta_intervall; kriterie_intervall; kriterier; ...)", "d": "<PERSON><PERSON>r det minsta värdet bland celler som anges av en given uppsättning villkor eller kriterier"}, "MODE": {"a": "(tal1; [tal2]; ...)", "d": "Return<PERSON>r det vanligast förekommande eller mest repeterade värdet i en matris eller ett dataområde"}, "MODE.MULT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar en vertikal matris med de vanligast förekommande eller mest repeterade värdena i en matris eller ett dataområde. För en horisontell matris använder du =TRANSPONERA(TYPVÄRDE.FLERA(tal1,tal2,...))."}, "MODE.SNGL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar det vanligast förekommande eller mest repeterade värdet i en matris eller ett dataområde."}, "NEGBINOM.DIST": {"a": "(antal_m; antal_l; sannolikhet_l; kumulativ)", "d": "Returnerar den negativa binomialfördelningen, sannolikheten att Antal_M försök ska misslyckas innan Antal_L lyckas, med Sannolikhet_Ls sannolikhet att lyckas."}, "NEGBINOMDIST": {"a": "(antal_m; antal_l; sannolikhet_l)", "d": "Returnera den negativa binomialfördelningen, sannolikheten att Antal_M försök ska misslyckas innan Antal_L lyckas, med Sannolikhet_Ls sannolikhet att lyckas."}, "NORM.DIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse; kumulativ)", "d": "Returnerar normalfördelningen för det angivna medelvärdet och standardavvikelsen."}, "NORMDIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse; kumulativ)", "d": "Returnerar den kumulativa normalfördelningen för det angivna medelvärdet och standardavvikelsen"}, "NORM.INV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnerar inversen till den kumulativa normalfördelningen för det angivna medelvärdet och standardavvikelsen."}, "NORMINV": {"a": "(sannolikhet; medelvärde; standardavvikelse)", "d": "Returnerar inversen till den kumulativa normalfördelningen för det angivna medelvärdet och standardavvikelsen"}, "NORM.S.DIST": {"a": "(z; kumulativ)", "d": "Returnerar standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett)."}, "NORMSDIST": {"a": "(z)", "d": "Returnerar den kumulativa standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett)"}, "NORM.S.INV": {"a": "(sannolikhet)", "d": "Returnerar inversen till den kumulativa standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett)."}, "NORMSINV": {"a": "(sannolikhet)", "d": "Returnerar inversen till den kumulativa standardnormalfördelningen (har ett medelvärde på noll och en standardavvikelse på ett)"}, "PEARSON": {"a": "(matris1; matris2)", "d": "<PERSON><PERSON><PERSON> k<PERSON>ficienten till <PERSON><PERSON>, r."}, "PERCENTILE": {"a": "(matris; n)", "d": "Returnerar den n:te percentilen av värden i ett område"}, "PERCENTILE.EXC": {"a": "(matris; n)", "d": "Returnerar den n:te percentilen av värden i ett område, där n är i intervallet 0..1 exklusiv."}, "PERCENTILE.INC": {"a": "(matris; n)", "d": "Returnerar den n:te percentilen av värden i ett område, där n är i intervallet 0..1 inklusiv."}, "PERCENTRANK": {"a": "(matris; x; [signifikans])", "d": "Returnerar rangen för ett värde i en datamängd i procent av datamängden"}, "PERCENTRANK.EXC": {"a": "(matris; x; [signifikans])", "d": "Returnerar rangen för ett värde i en datamängd som en andel i procent (0..1 exklusiv) av datamängden."}, "PERCENTRANK.INC": {"a": "(matris; x; [signifikans])", "d": "Returnerar rangen för ett värde i en datamängd som en andel i procent (0..1 inklusiv) av datamängden."}, "PERMUT": {"a": "(tal; valt_tal)", "d": "Returnerar antal permutationer för ett givet antal objekt som kan väljas från de totala antalet objekt."}, "PERMUTATIONA": {"a": "(tal; valt_tal)", "d": "Returnerar antal permutationer för ett givet antal objekt (med repetitioner) som kan väljas från det totala antalet objekt."}, "PHI": {"a": "(x)", "d": "Returnerar värdet för densitetsfunktionen för en standardnormalfördelning."}, "POISSON": {"a": "(x; medel<PERSON><PERSON><PERSON>; kumulativ)", "d": "<PERSON><PERSON><PERSON>-fördeln<PERSON>"}, "POISSON.DIST": {"a": "(x; medel<PERSON><PERSON><PERSON>; kumulativ)", "d": "<PERSON><PERSON><PERSON>fördeln<PERSON>."}, "PROB": {"a": "(x_omr<PERSON><PERSON>; sannolikhetsområde; undre_gräns; [övre_gräns])", "d": "Return<PERSON>r sannolikheten att värden i ett område ligger mellan två gränser eller är lika med en lägre gräns."}, "QUARTILE": {"a": "(matris; k<PERSON><PERSON>)", "d": "Returnerar kvartilen av en datamängd"}, "QUARTILE.INC": {"a": "(matris; k<PERSON><PERSON>)", "d": "Returnerar kvartilen av en datamängd, utifrån percentilvärden från 0..1 inklusiv."}, "QUARTILE.EXC": {"a": "(matris; k<PERSON><PERSON>)", "d": "Returnerar kvartilen av en datamängd, utifrån percentilvärden från 0..1 exklusiv."}, "RANK": {"a": "(tal; ref; [ordning])", "d": "Returnera rangordningen för ett tal i en lista med tal: dess storlek i relation till andra värden i listan"}, "RANK.AVG": {"a": "(tal; ref; [ordning])", "d": "Returnerar rangordningen för ett tal i en lista med tal: dess storlek i relation till andra värden i listan; om fler än ett värde har samma rang returneras medelvärdet."}, "RANK.EQ": {"a": "(tal; ref; [ordning])", "d": "Returnerar rangordningen för ett tal i en lista med tal: dess storlek i relation till andra värden i listan; om fler än ett värde har samma rang returneras den högsta rangen i den mängden."}, "RSQ": {"a": "(kända_y; kända_x)", "d": "Returnerar korrelationskoefficienten till Pearsons momentprodukt genom de givna datapunkterna"}, "SKEW": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar snedheten i en fördelning, d.v.s. graden av asymmetri kring en fördelnings medelvärde."}, "SKEW.P": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar snedheten i en fördelning baserat på en population, d.v.s. graden av asymmetri kring en fördelnings medelvärde."}, "SLOPE": {"a": "(kända_y; kända_x)", "d": "Returnerar lutningen av en linjär regressionslinje genom de angivna datapunkterna"}, "SMALL": {"a": "(matris; n)", "d": "Returnerar det n:te minsta värdet i en datamängd, t ex det femte minsta talet."}, "STANDARDIZE": {"a": "(x; medel<PERSON><PERSON><PERSON>; standardavvikelse)", "d": "Returnerar ett normaliserat värde från en fördelning karaktäriserad av medelvärden och standardavvikelser."}, "STDEV": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar standardavvikelsen utifrån ett exempel (logiska värden och text i exemplet ignoreras)"}, "STDEV.P": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar standardavvikelsen baserad på hela populationen angiven som argument (ignorerar logiska värden och text)."}, "STDEV.S": {"a": "(tal1; [tal2]; ...)", "d": "Uppskattar standardavvikelsen baserad på ett sampel (ignorerar logiska värden och text i samplet)."}, "STDEVA": {"a": "(värde1; [värde2]; ...)", "d": "Uppskattar standardavvikelsen baserad på ett sampel inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1."}, "STDEVP": {"a": "(tal1; [tal2]; ...)", "d": "Beräk<PERSON> standardavvikelsen utifrån hela populationen angiven som argument (logiska värden och text ignoreras)"}, "STDEVPA": {"a": "(värde1; [värde2]; ...)", "d": "Beräknar standard avvikelsen baserad på hela populationen, inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1."}, "STEYX": {"a": "(kända_y; kända_x)", "d": "Returnerar standardfelet för ett förutspått y-värde för varje x-värde i en regression"}, "TDIST": {"a": "(x; frihetsgrader; sidor)", "d": "Returnera studentens t-fördelning"}, "TINV": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnerar den tvåsidiga inversen till studentens t-fördelning"}, "T.DIST": {"a": "(x; frihetsgrader; kumulativ)", "d": "Returnerar vänstersidig students t-fördelning."}, "T.DIST.2T": {"a": "(x; frihetsgrader)", "d": "Returnerar tvåsidig students t-fördelning."}, "T.DIST.RT": {"a": "(x; frihetsgrader)", "d": "Returnerar högersidig students t-fördelning."}, "T.INV": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnerar den vänstersidiga inversen till students t-fördelning."}, "T.INV.2T": {"a": "(sannolikhet; frihetsgrader)", "d": "Returnerar den tvåsidiga inversen till students t-fördelning."}, "T.TEST": {"a": "(matris1; matris2; sidor; typ)", "d": "<PERSON><PERSON><PERSON> sann<PERSON>n associerad med students t-test."}, "TREND": {"a": "(kända_y; [kända_x]; [nya_x]; [konst])", "d": "Returnerar tal i en linjär trend som matchar kända datapunkter genom att använda minsta kvadrat-metoden"}, "TRIMMEAN": {"a": "(matris; procent)", "d": "Returnerar medelvärdet av mittenpunkterna i en mängd data."}, "TTEST": {"a": "(matris1; matris2; sidor; typ)", "d": "Returnera sannolikheten associerad med en students t-test"}, "VAR": {"a": "(tal1; [tal2]; ...)", "d": "Beräk<PERSON> variansen utifrån ett exempel (logiska värden och text i exemplet ignoreras)"}, "VAR.P": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar variansen baserad på hela populationen (ignorerar logiska värden och text i populationen)."}, "VAR.S": {"a": "(tal1; [tal2]; ...)", "d": "Uppskattar variansen baserad på ett sampel (ignorerar logiska värden och text i samplet)."}, "VARA": {"a": "(värde1; [värde2]; ...)", "d": "Uppskattar variansen baserad på ett sampel, inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1."}, "VARP": {"a": "(tal1; [tal2]; ...)", "d": "Beräknar variansen utifrån hela populationen (logiska värden och text i populationen ignoreras)"}, "VARPA": {"a": "(värde1; [värde2]; ...)", "d": "Beräknar variansen baserad på hela populationen, inklusive logiska värden och text. Text och det logiska värdet FALSKT har värdet 0; det logiska värdet SANT har värdet 1."}, "WEIBULL": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnera <PERSON>-fördelningen"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "<PERSON><PERSON><PERSON>fördeln<PERSON>."}, "Z.TEST": {"a": "(matris; x; [sigma])", "d": "Returnerar det ensidiga P-värdet av en z-test."}, "ZTEST": {"a": "(matris; x; [sigma])", "d": "Returnerar det ensidiga P-värdet av en z-test"}, "ACCRINT": {"a": "(utgiv<PERSON>; första_ränta; betalning; kupongränta; nominellt; frekvens; [bastyp]; [beräkningsmetod])", "d": "Return<PERSON><PERSON> den upplupna räntan för värdepapper som ger periodisk ränta."}, "ACCRINTM": {"a": "(utgi<PERSON><PERSON>; fö<PERSON>all; kupongrä<PERSON>; nominellt; [bas])", "d": "Return<PERSON>r den upplupna räntan för värdepapper som ger avkastning på förfallodagen"}, "AMORDEGRC": {"a": "(kostnad; inköpsdatum; första_perioden; restvärde; period; takt; [bas])", "d": "Returnerar den linjära avskrivningen för en tillgång för varje redovisningsperiod."}, "AMORLINC": {"a": "(kostnad; inköpsdatum; första_perioden; restvärde; period; takt; [bas])", "d": "Returnerar den linjära avskrivningen för en tillgång för varje redovisningsperiod."}, "COUPDAYBS": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> antal dagar från början av kupongperioden till likviddagen"}, "COUPDAYS": {"a": "(betalning; förfall; frekvens; [bas])", "d": "Returnerar antal dagar i kupongperioden som innehåller likviddagen"}, "COUPDAYSNC": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON>r antal dagar från likviddagen till nästa kupongdatum"}, "COUPNCD": {"a": "(betalning; förfall; frekvens; [bas])", "d": "Returnerar nästa kupongdatum efter likviddagen"}, "COUPNUM": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> antalet kuponger som ska betalas mellan likviddag och förfallodag"}, "COUPPCD": {"a": "(betalning; förfall; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> se<PERSON>e k<PERSON> före likviddagen"}, "CUMIPMT": {"a": "(ränta; antal_perioder; nuvärde; startperiod; slutperiod; typ)", "d": "<PERSON><PERSON><PERSON> den ackumulerade räntan som betalats mellan två perioder"}, "CUMPRINC": {"a": "(ränta; antal_perioder; nuvärde; startperiod; slutperiod; typ)", "d": "<PERSON><PERSON><PERSON> det ackumulerade kapitalbeloppet som har betalats på ett lån mellan två perioder"}, "DB": {"a": "(kostnad; restvärde; livslängd; period; [måna<PERSON>])", "d": "Returnerar avskrivningen för en tillgång under en angiven tid enligt metoden för fast degressiv avskrivning."}, "DDB": {"a": "(kostnad; restvärde; livslängd; period; [faktor])", "d": "Returnerar en tillgångs värdeminskning under en viss period med hjälp av dubbel degressiv avskrivning eller någon annan metod som du anger."}, "DISC": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; pris; in<PERSON><PERSON><PERSON>; [bas])", "d": "Returnerar diskonteringsräntan för ett värdepapper"}, "DOLLARDE": {"a": "(br<PERSON><PERSON><PERSON>; heltal)", "d": "Omvandlar ett pris uttryckt som ett bråk till ett decimaltal"}, "DOLLARFR": {"a": "(decimaltal; heltal)", "d": "Omvandlar ett tal uttryckt som ett decimaltal till ett tal uttryckt som ett bråk"}, "DURATION": {"a": "(betalning; förfall; kupong; avkastning; frekvens; [bas])", "d": "Returnerar den årliga löptiden för ett värdepapper med periodiska räntebetalningar"}, "EFFECT": {"a": "(nominalränta; antal_perioder)", "d": "Returnerar den effektiva årsräntan"}, "FV": {"a": "(ränta; periodantal; betalning; [nuvärde]; [typ])", "d": "Return<PERSON>r det framtida värdet av en investering, baserat på en periodisk, konstant betalning och en konstant ränta."}, "FVSCHEDULE": {"a": "(kapital; räntor)", "d": "Returnerar ett framtida värde av ett begynnelsekapital beräknat på flera olika räntenivåer"}, "INTRATE": {"a": "(betalning; förfall; investering; in<PERSON><PERSON>sen; [bas])", "d": "Returnerar räntesatsen för ett fullinvesterat värdepapper"}, "IPMT": {"a": "(ränta; period; periodantal; nuvärde; [slutvärde]; [typ])", "d": "Returnerar ränteinbetalningen för en investering och vald period, baserat på periodiska, konstanta betalningar och en konstant ränta."}, "IRR": {"a": "(v<PERSON><PERSON>; [gissning])", "d": "Returnerar avkastningsgraden för en serie penningflöden."}, "ISPMT": {"a": "(ränta; period; periodantal; nuvärde)", "d": "Returnerar den ränta som betalats under en viss period för en investering."}, "MDURATION": {"a": "(betalning; förfall; kupong; avkastning; frekvens; [bas])", "d": "<PERSON><PERSON><PERSON> den ändrade <PERSON>-löptiden för ett värdepapper med ett nominellt värde på 1 000 kr."}, "MIRR": {"a": "(värden; kapitalränta; återinvesteringsränta)", "d": "Returnerar avkastningsgraden för en serie periodiska penningflöden med tanke på både investeringskostnader och räntan på återinvesteringen av pengar."}, "NOMINAL": {"a": "(effektiv_ränta; antal_perioder)", "d": "Returnerar den årliga nominella räntesatsen"}, "NPER": {"a": "(ränta; betalning; nuvärde; [slutvärde]; [typ])", "d": "<PERSON><PERSON>r antalet perioder för en investering baserad på periodisk betalning och en konstant ränta."}, "NPV": {"a": "(ränta; värde1; [värde2]; ...)", "d": "Returnerar nuvärdet av en serie betalningar baserad på en diskonteringsränta och serier med framtida betalningar (negativa värden) och inkomster (positiva värden)."}, "ODDFPRICE": {"a": "(betalning; förfall; utgivning; kup_dag_1; ränta; avkastning; inlösen; frekvens; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper med en udda första period"}, "ODDFYIELD": {"a": "(betalning; förfall; utgivning; kup_dag_1; ränta; pris; inl<PERSON>sen; frekvens; [bas])", "d": "Returnerar avkastningen för ett värdepapper med en udda första period"}, "ODDLPRICE": {"a": "(betalning; förfall; sista_kup_dag; ränta; avkastning; inlösen; frekvens; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper med en udda sista period"}, "ODDLYIELD": {"a": "(betalning; förfall; sista_kup_dag; ränta; pris; in<PERSON><PERSON>sen; frekvens; [bas])", "d": "Returnerar avkastningen för ett värdepapper med en udda sista period"}, "PDURATION": {"a": "(ränta; nuvärde; slutvärde)", "d": "Return<PERSON>r antalet perioder som krävs för en investering att uppnå ett visst värde."}, "PMT": {"a": "(ränta; periodantal; nuvärde; [slutvärde]; [typ])", "d": "Beräknar betalningen av ett lån baserat på regelbundna betalningar och en konstant ränta."}, "PPMT": {"a": "(ränta; period; periodantal; nuvärde; [slutvärde]; [typ])", "d": "Returnerar amorteringsinbetalningen för en investering baserat på periodiska, konstanta betalningar och en konstant ränta."}, "PRICE": {"a": "(beta<PERSON><PERSON>; förfall; ränta; avkastning; inlösen; frekvens; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper som ger periodisk ränta"}, "PRICEDISC": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>; inl<PERSON>sen; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett diskonterat värdepapper"}, "PRICEMAT": {"a": "(betalning; förfall; utgivning; ränta; avkastning; [bas])", "d": "Returnerar priset per 1 000 kr nominellt värde för ett värdepapper som ger avkastning på förfallodagen"}, "PV": {"a": "(ränta; periodantal; betalning; [slutvärde]; [typ])", "d": "Returnerar nuvärdet av en investering: den totala summan som en serie med framtida betalningar är värd nu."}, "RATE": {"a": "(periodantal; betalning; nuvärde; [slutvärde]; [typ]; [gissning])", "d": "Returnerar räntesatsen per period för ett lån eller en investering. Använd t.ex. 6 %/4 för kvartalsvisa betalningar med 6 % ränta."}, "RECEIVED": {"a": "(betalning; förfall; investering; ränta; [bas])", "d": "Returnerar beloppet som utbetalas på förfallodagen för ett betalt värdepapper"}, "RRI": {"a": "(periodantal; nuvärde; slutvärde)", "d": "<PERSON><PERSON><PERSON> motsvarande ränta för en investerings tillväxt."}, "SLN": {"a": "(kostnad; restvärde; livslängd)", "d": "Returnerar den linjära avskrivningen för en tillgång under en period."}, "SYD": {"a": "(kostnad; restvärde; livslängd; period)", "d": "Returnerar den årliga avskrivningssumman för en tillgång under en angiven period."}, "TBILLEQ": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>)", "d": "Returnerar avkastningen motsvarande obligationsräntan för statsskuldväxlar"}, "TBILLPRICE": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>)", "d": "Returnerar priset per 1 000 kr nominellt värde för en statsskuldväxel"}, "TBILLYIELD": {"a": "(beta<PERSON>ing; fö<PERSON>all; pris)", "d": "Returnerar avkastningen för en statsskuldväxel"}, "VDB": {"a": "(kostnad; restvärde; livslängd; startperiod; slutperiod; [faktor]; [inget_byte])", "d": "Returnerar avskrivningen för en tillgång under en angiven period eller del av period genom dubbel degressiv avskrivning eller annan metod som du anger."}, "XIRR": {"a": "(v<PERSON><PERSON>; datum; [gissning])", "d": "Returnerar internräntan för ett schema över betalningsströmmar som inte nödvändigtvis är periodiska"}, "XNPV": {"a": "(rä<PERSON>; värden; datum)", "d": "Return<PERSON><PERSON> det diskonterade nuvärdet för ett schema över betalningsströmmar som inte nödvändigtvis är periodiska"}, "YIELD": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; rä<PERSON>; pris; in<PERSON><PERSON><PERSON>; frek<PERSON><PERSON>; [bas])", "d": "Returnerar avkastningen för ett värdepapper som betalar periodisk ränta"}, "YIELDDISC": {"a": "(beta<PERSON><PERSON>; fö<PERSON><PERSON>; pris; in<PERSON><PERSON><PERSON>; [bas])", "d": "Returnerar den årliga avkastningen för diskonterade värdepapper"}, "YIELDMAT": {"a": "(beta<PERSON><PERSON>; fö<PERSON>all; utgiv<PERSON>; rä<PERSON>; pris; [bas])", "d": "Returnerar den årliga avkastningen för ett värdepapper som ger ränta på förfallodagen"}, "ABS": {"a": "(tal)", "d": "Returnerar absolutvärdet av ett tal. Ett tal utan tecken."}, "ACOS": {"a": "(tal)", "d": "Returnerar arcus cosinus för ett tal, i radianer i intervallet 0 till <PERSON>. Arcus cosinus är vinkeln vars cosinus är Tal."}, "ACOSH": {"a": "(tal)", "d": "Returnerar inverterad hyperbolisk cosinus för ett tal."}, "ACOT": {"a": "(tal)", "d": "<PERSON><PERSON>r arcus cotangens för ett tal, i radianer i intervallet 0 till Pi."}, "ACOTH": {"a": "(tal)", "d": "Returnerar inverterad hyperbolisk cotangens för ett tal."}, "AGGREGATE": {"a": "(funktion; alternativ; ref1; ...)", "d": "Returnerar en mängdfunktion i en lista eller databas."}, "ARABIC": {"a": "(text)", "d": "Konverterar romerska siffror till arabiska."}, "ASC": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> med DBCS-teckenuppsättningar (teckenuppsättningar med dubbla byte) ändrar den gär funktionen tecken med hel bredd (två byte) till halvbreddstecken (en byte)"}, "ASIN": {"a": "(tal)", "d": "Returnerar arcus sinus för ett tal i radianer, i intervallet -Pi/2 till Pi/2."}, "ASINH": {"a": "(tal)", "d": "Returnerar hyperbolisk arcus sinus för ett tal."}, "ATAN": {"a": "(tal)", "d": "<PERSON><PERSON>r arcus tangens för ett tal i radianer, i intervallet -Pi/2 till Pi/2."}, "ATAN2": {"a": "(x; y)", "d": "Return<PERSON>r arcus tangens för de angivna x- och y-koordinaterna, i radianer mellan -Pi och Pi, vilket exkluderar -Pi."}, "ATANH": {"a": "(tal)", "d": "Returnerar inverterad hyperbolisk tangens för ett tal."}, "BASE": {"a": "(tal; talbas; [minimilängd])", "d": "Konverterar ett tal till textformat med en given talbas."}, "CEILING": {"a": "(tal; signifikans)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal uppåt, till närmaste signifikanta multipel."}, "CEILING.MATH": {"a": "(tal; [signifikans]; [typvärde])", "d": "A<PERSON><PERSON><PERSON> ett tal uppåt till närmaste heltal eller till närmaste signifikanta multipel."}, "CEILING.PRECISE": {"a": "(tal; [signifikans])", "d": "Return<PERSON>r ett tal som har rundats upp till närmaste heltal eller närmaste signifikanta multipel"}, "COMBIN": {"a": "(antal; valt_antal)", "d": "<PERSON><PERSON>r antalet kombinationer för ett givet antal objekt."}, "COMBINA": {"a": "(tal; valt_tal)", "d": "<PERSON><PERSON>r antalet kombinationer med repetitioner för ett givet antal objekt."}, "COS": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> cosinus för en vinkel."}, "COSH": {"a": "(tal)", "d": "Return<PERSON>r hyperboliskt cosinus för ett tal."}, "COT": {"a": "(tal)", "d": "Returnerar cotangens för en vinkel."}, "COTH": {"a": "(tal)", "d": "Returnerar hyperbolisk cotangens för ett tal."}, "CSC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> cosekant för en vinkel."}, "CSCH": {"a": "(tal)", "d": "Returnerar hyperbolisk cosekant för en vinkel."}, "DECIMAL": {"a": "(tal; talbas)", "d": "Konverterar ett tal i textformat i en given bas till ett decimaltal."}, "DEGREES": {"a": "(vinkel)", "d": "Konverterar radianer till grader."}, "ECMA.CEILING": {"a": "(tal; signifikans)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal uppåt, till närmaste signifikanta multipel"}, "EVEN": {"a": "(tal)", "d": "Rundar av ett positivt tal uppåt, och ett negativt tal nedåt, till närmaste jämna heltal."}, "EXP": {"a": "(tal)", "d": "Returnerar e upphöjt till ett angivet tal."}, "FACT": {"a": "(tal)", "d": "Returnerar ett tals fakultet. D.v.s. produkten av 1*2*3*...*tal."}, "FACTDOUBLE": {"a": "(tal)", "d": "Return<PERSON>r dubbelfakulteten för ett tal"}, "FLOOR": {"a": "(tal; signifikans)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal nedåt, till närmaste signifikanta multipel."}, "FLOOR.PRECISE": {"a": "(tal; [signifikans])", "d": "Return<PERSON>r ett tal som har rundats av nedåt till närmaste heltal eller närmaste signifikanta multipel"}, "FLOOR.MATH": {"a": "(tal; [signifikans]; [typvärde])", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal nedåt till närmaste heltal eller närmaste signifikanta multipel."}, "GCD": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar den största gemensamma nämnaren"}, "INT": {"a": "(tal)", "d": "Rundar av ett tal till närmaste heltal."}, "ISO.CEILING": {"a": "(tal; [signifikans])", "d": "Returnerar ett tal som har rundats upp till närmaste heltal eller närmaste signifikanta multipel. Oavsett talets tecken rundas talet uppåt. Om talet eller signifikansen är noll returneras dock noll."}, "LCM": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON>r minsta gemensamma multipel"}, "LN": {"a": "(tal)", "d": "Returnerar den naturliga logaritmen för ett tal."}, "LOG": {"a": "(tal; [bas])", "d": "Returnerar logaritmen av ett tal för basen som du anger."}, "LOG10": {"a": "(tal)", "d": "Returnerar 10-<PERSON><PERSON><PERSON><PERSON> för ett tal."}, "MDETERM": {"a": "(matris)", "d": "<PERSON><PERSON>r matrisen som är avgörandet av en matris."}, "MINVERSE": {"a": "(matris)", "d": "<PERSON><PERSON>r en invers av en matris för matrisen som är lagrad i en matris."}, "MMULT": {"a": "(matris1; matris2)", "d": "Returnerar matrisprodukten av två matriser, en matris med samma antal rader som matris1 och samma antal kolumner som matris2."}, "MOD": {"a": "(tal; divisor)", "d": "Returnerar resten efter att ett tal har dividerats med en divisor."}, "MROUND": {"a": "(tal; multipel)", "d": "<PERSON><PERSON>r ett tal avrundat till en given multipel"}, "MULTINOMIAL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar multinomialen för en uppsättning tal"}, "MUNIT": {"a": "(dimension)", "d": "<PERSON><PERSON>r enhetsmatrisen för den angivna dimensionen."}, "ODD": {"a": "(tal)", "d": "Rundar av ett positivt tal uppåt, och ett negativt nedåt, till närmaste udda heltal."}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> pi, 3,14159265358979, med 15 decimaler."}, "POWER": {"a": "(tal; exponent)", "d": "Returnerar resultatet av ett tal upphöjt till en exponent."}, "PRODUCT": {"a": "(tal1; [tal2]; ...)", "d": "Multiplicerar alla tal angivna som argument."}, "QUOTIENT": {"a": "(tä<PERSON><PERSON><PERSON>; nämnare)", "d": "Returnerar heltalsdelen av en division"}, "RADIANS": {"a": "(vinkel)", "d": "Konverterar grader till radianer."}, "RAND": {"a": "()", "d": "Returnerar ett slumptal större än eller lika med 0 och mindre än 1 (ändringar sker vid omberäkning)."}, "RANDARRAY": {"a": "([rader]; [kolumner]; [min]; [max]; [heltal])", "d": "Returnerar en matris med slumptal"}, "RANDBETWEEN": {"a": "(nedre; övre)", "d": "<PERSON><PERSON><PERSON> ett slumptal mellan de tal som du anger"}, "ROMAN": {"a": "(tal; [format])", "d": "Konverterar arabiska siffror till romerska, som text."}, "ROUND": {"a": "(tal; decimaler)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal till ett angivet antal decimaler."}, "ROUNDDOWN": {"a": "(tal; decimaler)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal nedåt mot noll."}, "ROUNDUP": {"a": "(tal; decimaler)", "d": "<PERSON><PERSON><PERSON><PERSON> ett tal uppåt från noll."}, "SEC": {"a": "(tal)", "d": "<PERSON><PERSON><PERSON> sekant för en vinkel."}, "SECH": {"a": "(tal)", "d": "Returnerar hyperbolisk sekant för en vinkel."}, "SERIESSUM": {"a": "(x; n; m; koefficienter)", "d": "Returnerar summan av en potensserie baserad på formeln"}, "SIGN": {"a": "(tal)", "d": "Return<PERSON>r ett tals tecken: 1 om talet är positivt, noll om talet är noll eller -1 om talet är negativt."}, "SIN": {"a": "(tal)", "d": "Returnerar sinus för en vinkel."}, "SINH": {"a": "(tal)", "d": "Returnerar hyperbolisk sinus för ett tal."}, "SQRT": {"a": "(tal)", "d": "Returnerar ett tals kvadratrot."}, "SQRTPI": {"a": "(tal)", "d": "Return<PERSON>r k<PERSON> av (tal * pi)"}, "SUBTOTAL": {"a": "(funktionsnr; ref1; ...)", "d": "Returnerar en delsumma i en lista eller databas."}, "SUM": {"a": "(tal1; [tal2]; ...)", "d": "Adderar samtliga tal i ett cellområde."}, "SUMIF": {"a": "(o<PERSON>r<PERSON><PERSON>; vill<PERSON>; [summaområ<PERSON>])", "d": "<PERSON><PERSON><PERSON> celler enligt ett angivet villkor."}, "SUMIFS": {"a": "(summa<PERSON><PERSON><PERSON><PERSON>; vill<PERSON><PERSON>mr<PERSON>de; villkor; ...)", "d": "<PERSON><PERSON><PERSON> de celler som anges av en given uppsättning villkor"}, "SUMPRODUCT": {"a": "(matris1; [matris2]; [matris3]; ...)", "d": "Returnerar summan av produkter av korresponderande områden eller matriser."}, "SUMSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerar summan av argumentens kvadrater. Argumenten kan vara tal, matriser, namn eller referenser till celler som innehåller tal."}, "SUMX2MY2": {"a": "(xmatris; ymatris)", "d": "Summerar skillnaderna mellan kvadraterna i två motsvarande områden eller matriser."}, "SUMX2PY2": {"a": "(xmatris; ymatris)", "d": "<PERSON><PERSON><PERSON>man av summan av kvadraterna på tal i två motsvarande områden eller matriser."}, "SUMXMY2": {"a": "(xmatris; ymatris)", "d": "Summerar kvadraten på skillnaderna mellan två motsvarande områden eller matriser."}, "TAN": {"a": "(tal)", "d": "<PERSON><PERSON>r en vinkels tangent."}, "TANH": {"a": "(tal)", "d": "Returnerar hyperbolisk tangens för ett tal."}, "TRUNC": {"a": "(tal; [antal_siffror])", "d": "Avkortar ett tal till ett heltal genom att ta bort decimaler."}, "ADDRESS": {"a": "(rad; kolumn; [abs]; [a1]; [bladnamn])", "d": "Skapar en cellreferens som text med ett angivet antal rader och kolumner."}, "CHOOSE": {"a": "(index; värde1; [värde2]; ...)", "d": "Väljer ett värde eller en åtgärd som ska utföras från en lista av värden, baserad på ett indexnummer."}, "COLUMN": {"a": "([ref])", "d": "<PERSON><PERSON><PERSON> kolum<PERSON>et för en referens."}, "COLUMNS": {"a": "(matris)", "d": "Return<PERSON>r antalet kolumner i en matris eller en referens."}, "FORMULATEXT": {"a": "(ref)", "d": "Return<PERSON>r en formel som en sträng."}, "HLOOKUP": {"a": "(letauppvärde; tabell; radindex; [ungefärlig])", "d": "Letar i översta raden av en tabell eller matris med värden och returnerar värdet i samma kolumn från en rad som du anger."}, "HYPERLINK": {"a": "(Lä<PERSON>_placering; [vänligt_namn])", "d": "Skapar en genväg eller ett hopp som öppnar ett dokument som är lagrat på din hårddisk, en server på nätverket eller på Internet."}, "INDEX": {"a": "(matris; rad; [kolumn]!ref; rad; [kolumn]; [omr<PERSON><PERSON>])", "d": "Returnerar ett värde eller referens för cellen vid skärningen av en viss rad och kolumn i ett givet område."}, "INDIRECT": {"a": "(reftext; [a1])", "d": "<PERSON><PERSON><PERSON> referensen angiven av en textsträng."}, "LOOKUP": {"a": "(letauppvärde; letauppvektor; [resultatvektor]!letauppvärde; matris)", "d": "Returnerar ett värde antingen från ett enrads- eller enkolumnsområde eller från en matris. Finns med för bakåtkompatibilitet."}, "MATCH": {"a": "(letauppvärde; letauppvektor; [typ])", "d": "Returnerar elementets relativa position i en matris som matchar ett angivet värde i en angiven ordning."}, "OFFSET": {"a": "(ref; rader; kolumner; [höjd]; [bredd])", "d": "Returnerar en referens till ett område som är ett givet antal rader och kolumner från en given referens."}, "ROW": {"a": "([ref])", "d": "Returnerar en referens radnummer."}, "ROWS": {"a": "(matris)", "d": "<PERSON><PERSON>r antal rader i en referens eller matris."}, "TRANSPOSE": {"a": "(matris)", "d": "Konverterar ett vertikalt cellområde till ett horisontellt och tvärtom."}, "UNIQUE": {"a": "(matris; [by_col]; [exactly_once])", "d": " Returnerar unika värden från ett intervall eller en matris."}, "VLOOKUP": {"a": "(letau<PERSON><PERSON><PERSON><PERSON>; tabell<PERSON>ris; kolumnindex; [ungefärlig])", "d": "Söker efter ett värde i den vänstra kolumnen i tabellen och returnerar sedan ett värde från en kolumn som du anger i samma rad. Som standard måste tabellen vara sorterad i stigande ordning."}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "s<PERSON><PERSON> efter ett intervall eller en matris för en matchning och returnerar motsvarande objekt från ett andra intervall eller en andra matris. Som standard används en exakt matchning"}, "CELL": {"a": "(infotyp; [referens])", "d": "Returnerar information om formatering, placering eller innehåll för en cell"}, "ERROR.TYPE": {"a": "(felvärde)", "d": "<PERSON><PERSON><PERSON> ett tal som motsvarar ett felvärde."}, "ISBLANK": {"a": "(värde)", "d": "Kontrollerar om värdet refererar till en tom cell och returnerar SANT eller FALSKT."}, "ISERR": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett annat fel än #Saknas (otillgängligt värde) och returnerar SANT eller FALSKT"}, "ISERROR": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett fel och returnerar SANT eller FALSKT"}, "ISEVEN": {"a": "(tal)", "d": "Returnerar SANT om talet är jämt"}, "ISFORMULA": {"a": "(ref)", "d": "Kontrollerar om värdet refererar till en cell som innehåller en formel och returnerar SANT eller FALSKT."}, "ISLOGICAL": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett logiskt värde (SANT eller FALSKT) och returnerar SANT eller FALSKT."}, "ISNA": {"a": "(värde)", "d": "Kontrollerar om ett värde är #Saknas (otillgängligt värde) och returnerar SANT eller FALSKT"}, "ISNONTEXT": {"a": "(värde)", "d": "Kontrollerar om ett värde inte är text (tomma celler är inte text) och returnerar SANT eller FALSKT."}, "ISNUMBER": {"a": "(värde)", "d": "Kontrollerar om ett värde är ett tal och returnerar SANT eller FALSKT."}, "ISODD": {"a": "(tal)", "d": "Returnerar SANT om talet är udda"}, "ISREF": {"a": "(värde)", "d": "Kontrollerar om ett värde är en referens och returnerar SANT eller FALSKT."}, "ISTEXT": {"a": "(värde)", "d": "Kontrollerar om ett värde är text och returnerar SANT eller FALSKT."}, "N": {"a": "(värde)", "d": "Konverterar värden som inte är tal till tal, datum till serienummer, SANT till 1 och allt annat till 0 (noll)."}, "NA": {"a": "()", "d": "Returnerar felvärdet #Saknas (värdet är inte tillgängligt)"}, "SHEET": {"a": "([värde])", "d": "<PERSON><PERSON><PERSON> bladnummer för bladet som refereras."}, "SHEETS": {"a": "([ref])", "d": "<PERSON><PERSON>r antalet blad i en referens."}, "TYPE": {"a": "(värde)", "d": "Returnerar ett heltal som anger ett värdes datatyp: tal=1; text=2; logiskt värde=4; felvärde=16; matris=64; sammansatta data = 128"}, "AND": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerar om alla argument utvärderas till SANT och returnerar SANT om alla dess argument är lika med SANT."}, "FALSE": {"a": "()", "d": "Returnerar det logiska värdet FALSKT"}, "IF": {"a": "(logiskt_test; [värde_om_sant]; [värde_om_falskt])", "d": "Kontrollerar om ett villkor uppfylls och returnerar ett värde om ett villkor beräknas till SANT och ett annat värde om det beräknas till FALSKT."}, "IFS": {"a": "(logiskt_test; värde_om_sant; ...)", "d": "Kontrollerar om ett eller flera villkor uppfylls och returnerar ett värde som motsvarar det första villkoret som är SANT"}, "IFERROR": {"a": "(värde; värde_om_fel)", "d": "Return<PERSON>r värde_om_fel om uttrycket är ett fel, i annat fall värdet för uttrycket"}, "IFNA": {"a": "(värde; värde_om_saknas)", "d": "Returnerar ett angivet värde om uttrycket ger #SAKNAS, annars returneras resultatet av uttrycket."}, "NOT": {"a": "(logisk)", "d": "Ändrar FALSKT till SANT eller SANT till FALSKT."}, "OR": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerar om något av argumenten har värdet SANT och returnerar SANT eller FALSKT. Returnerar FALSKT bara om alla argument har värdet FALSKT."}, "SWITCH": {"a": "(uttryck; värde1; resultat1; [standard_eller_värde2]; [resultat2]; ...)", "d": "Beräknar ett uttryck mot en lista med värden och returnerar resultatet som motsvarar det första matchande värdet. Om inget matchar returneras ett valfritt standardvärde"}, "TRUE": {"a": "()", "d": "Returnerar det logiska värdet SANT."}, "XOR": {"a": "(logisk1; [logisk2]; ...)", "d": "Return<PERSON>r ett logiskt 'Exklusivt eller' för alla argument."}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returnerar text som är före avgränsande tecken."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returnerar text som är efter avgränsande tecken."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Delar upp text i rader eller kolumner med avgränsare."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Radbryter en rad- eller kolumnvektor efter angivet antal värden."}, "VSTACK": {"a": "(matris1, [matris2], ...)", "d": "Staplar matriser lo<PERSON>t i en matris."}, "HSTACK": {"a": "(matris1, [matris2], ...)", "d": "Staplar matriser vågrätt i en matris."}, "CHOOSEROWS": {"a": "(matris, rad1, [rad2], ...)", "d": "<PERSON><PERSON><PERSON> raderna i en matris eller referens."}, "CHOOSECOLS": {"a": "(matris, kolumn1, [kolumn2], ...)", "d": "<PERSON><PERSON>r kolumnerna i en matris eller referens."}, "TOCOL": {"a": "(matris, [ignorera], [scan_by_column])", "d": "Returnerar matrisen som en kolumn."}, "TOROW": {"a": "(matris, [ignorera], [scan_by_column])", "d": " <PERSON><PERSON>r matrisen som en rad."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Radbryter en rad- eller kolumnvektor efter angivet antal värden."}, "TAKE": {"a": "(matris, rader, [kolumner])", "d": "Returnerar rader eller kolumner fr<PERSON>n matrisens start eller slut."}, "DROP": {"a": "(matris, rader, [kolumner])", "d": "Tar bort rader eller kolumner fr<PERSON>n matrisens start eller slut."}}