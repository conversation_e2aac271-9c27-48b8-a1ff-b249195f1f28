﻿<!DOCTYPE html>
<html>
	<head>
		<title>KURS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>KURS-Funktion</h1>
			<p>Die Funktion <b>KURS</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie gibt den Kurs pro 100 € Nennwert eines Wertpapiers zurück, das periodisch Zinsen auszahlt.</p>
			<p>Die Formelsyntax der Funktion <b>KURS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>KURS(Abrechnung;Fälligkeit;Satz;Rendite;Rückzahlung;Häufigkeit;[Basis])</em></b></p> 
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Abrechnung</em></b> Der Abrechnungstermin des Wertpapierkaufs.</p>
            <p style="text-indent: 50px;"><b><em>Fälligkeit</em></b> ist der Fälligkeitstermin des Wertpapiers.</p>
            <p style="text-indent: 50px;"><b><em>Satz</em></b> ist der jährliche Nominalzins (Kuponzinssatz) des Wertpapiers.</p>
            <p style="text-indent: 50px;"><b><em>Rendite</em></b> ist die jährliche Rendite des Wertpapiers.</p>
            <p style="text-indent: 50px;"><b><em>Rückzahlung</em></b> ist der Rückzahlungswert des Wertpapiers pro 100 € Nennwert.</p>
            <p style="text-indent: 50px;"><b><em>Häufigkeit</em></b> ist die Anzahl der Zinszahlungen pro Jahr. Die möglichen Werte sind: 1 für jährliche Zahlungen; 2 für halbjährliche Zahlungen; 4 für vierteljährliche Zahlungen.</p>
            <p style="text-indent: 50px;"><b><em>Basis</em></b> ist die zu verwendende Jahresbasis. Ein nummerischer Wert, größer oder gleich 0 und kleiner oder gleich 4. Dieses Argument ist optional. Mögliche Basiswerte:</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Basis</b></td>
                    <td><b>Datumssystem</b></td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>US (NASD) 30/360</td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>Tatsächlich/tatsächlich</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Tatsächlich/360</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Tatsächlich/365</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>Europäisch 30/360</td>
                </tr>
            </table>
            <p class="note"><b>Hinweis:</b> Daten müssen mit der Funktion DATUM eingegeben werden.</p>
            <p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>KURS</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>KURS</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="KURS-Funktion" src="../images/price.png" /></p>
		</div>
	</body>
</html>