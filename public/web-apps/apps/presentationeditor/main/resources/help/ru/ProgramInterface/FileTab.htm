<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Файл</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора презентаций - Вкладка Файл" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вкладка Файл</h1>
            <p>Вкладка <b>Файл</b> позволяет выполнить некоторые базовые операции с текущим файлом.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора презентаций:</p>
                <p><img alt="Вкладка Файл" src="../images/interface/filetab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора презентаций:</p>
                <p><img alt="Вкладка Файл" src="../images/interface/desktop_filetab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>
                    <span class="onlineDocumentFeatures">в <em>онлайн-версии</em> <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">сохранить</a> текущий файл (если отключена опция <b>Автосохранение</b>), <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">скачать как</a> (сохранить документ в выбранном формате на жестком диске компьютера), <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">сохранить копию как</a>  (сохранить копию документа в выбранном формате на портале), <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">распечатать</a> или <a href="../UsageInstructions/ViewDocInfo.htm" onclick="onhyperlinkclick(this)">переименовать</a> его,</span>
                    <span class="desktopDocumentFeatures">в <em>десктопной версии</em> <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">сохранить</a> текущий файл в текущем формате и местоположении с помощью опции <b>Сохранить</b>, сохранить файл под другим именем, в другом местоположении или в другом формате с помощью опции <b> Сохранить как</b>, <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">распечатать</a> файл,</span>
                </li>
                <li>защитить файл с помощью <a href="../HelpfulHints/Password.htm" onclick="onhyperlinkclick(this)">пароля</a>, изменить или удалить пароль;</li>
                <li class="desktopDocumentFeatures">защитить файл с помощью цифровой подписи (доступно только в <em>десктопной версии</em>);</li>
                <li class="onlineDocumentFeatures"><a href="../UsageInstructions/OpenCreateNew.htm" onclick="onhyperlinkclick(this)">создать</a> новую презентацию или открыть недавно отредактированную (доступно только в <em>онлайн-версии</em>),</li>
                <li>просмотреть <a href="../UsageInstructions/ViewPresentationInfo.htm" onclick="onhyperlinkclick(this)">общие сведения</a> о презентации  или изменить некоторые свойства файла,</li>
                <li class="onlineDocumentFeatures">управлять <a href="../UsageInstructions/ViewPresentationInfo.htm" onclick="onhyperlinkclick(this)">правами доступа</a> (доступно только в <em>онлайн-версии</em>),</li>
                <li>открыть <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">дополнительные параметры</a> редактора,</li>
                <li><span class="desktopDocumentFeatures">в <em>десктопной версии</em> открыть в окне <b>Проводника</b> папку, в которой сохранен файл.</span><span class="onlineDocumentFeatures"> В <em>онлайн-версии</em> открыть в новой вкладке браузера папку модуля <b>Документы</b>, в которой сохранен файл.</span></li>
            </ul>
		</div>
	</body>
</html>