{"Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Insira a súa mensaxe aquí", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "O obxecto está desabilitado porque está sendo editado por outro usuario.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Aviso", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON>", "Common.define.chartData.textAreaStackedPer": "<PERSON><PERSON> 100% ", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "<PERSON>umna a<PERSON>", "Common.define.chartData.textBarNormal3d": "Columna 3D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Columna 3D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON> am<PERSON>", "Common.define.chartData.textBarStacked3d": "Columna 3D apilada", "Common.define.chartData.textBarStackedPer": "Columna amontoada 100%", "Common.define.chartData.textBarStackedPer3d": "Columna 3D amoreada 100%", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Columna", "Common.define.chartData.textCombo": "Combinado", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> - Columna a<PERSON>", "Common.define.chartData.textComboBarLine": "Columna <PERSON>", "Common.define.chartData.textComboBarLineSecondary": "Columna agrupada - Liña no eixo secundario", "Common.define.chartData.textComboCustom": "Combinación personalizada", "Common.define.chartData.textDoughnut": "<PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Barra agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3D agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON> amontoada", "Common.define.chartData.textHBarStacked3d": "Barra 3D apilada", "Common.define.chartData.textHBarStackedPer": "Barra amontoada 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D amoreada 100%", "Common.define.chartData.textLine": "Liña", "Common.define.chartData.textLine3d": "Liña 3D", "Common.define.chartData.textLineMarker": "Liña con marcadores", "Common.define.chartData.textLineStacked": "<PERSON><PERSON>", "Common.define.chartData.textLineStackedMarker": "Liña amontoada con marcadores", "Common.define.chartData.textLineStackedPer": "Liña amontoada 100%", "Common.define.chartData.textLineStackedPerMarker": "Liña amontoada con marcadores 100%", "Common.define.chartData.textPie": "Sector do círculo", "Common.define.chartData.textPie3d": "Circular 3D", "Common.define.chartData.textPoint": "XY (Dispersión)", "Common.define.chartData.textScatter": "Dispersión", "Common.define.chartData.textScatterLine": "Dispersión coas liñas rectas", "Common.define.chartData.textScatterLineMarker": "Dispersión coas liñas rectas e marcadores", "Common.define.chartData.textScatterSmooth": "Dispersión con liñas suavizadas", "Common.define.chartData.textScatterSmoothMarker": "Dispersión coas liñas suavizadas e marcadores", "Common.define.chartData.textStock": "De cotizacións", "Common.define.chartData.textSurface": "Superficie", "Common.define.effectData.textAcross": "Horizontal", "Common.define.effectData.textAppear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textArcDown": "Arco para abaixo", "Common.define.effectData.textArcLeft": "Arco cara a esquerda", "Common.define.effectData.textArcRight": "Arco cara a dereita", "Common.define.effectData.textArcs": "Arcos", "Common.define.effectData.textArcUp": "Arco para ariba", "Common.define.effectData.textBasic": "Básico", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON>", "Common.define.effectData.textBasicZoom": "Ampliación básica", "Common.define.effectData.textBean": "Faba", "Common.define.effectData.textBlinds": "Persianas", "Common.define.effectData.textBlink": "Intermitente", "Common.define.effectData.textBoldFlash": "Flash en grosa", "Common.define.effectData.textBoldReveal": "Revelar en grosa", "Common.define.effectData.textBoomerang": "Búmerang", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "Rebote á esquerda", "Common.define.effectData.textBounceRight": "Rebote á dereita", "Common.define.effectData.textBox": "Caixa", "Common.define.effectData.textBrushColor": "Cor do pincel", "Common.define.effectData.textCenterRevolve": "Xirar cara ao centro", "Common.define.effectData.textCheckerboard": "Cadros bicolores", "Common.define.effectData.textCircle": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCollapse": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textColorPulse": "Pulso de color", "Common.define.effectData.textComplementaryColor": "Cor complementaria", "Common.define.effectData.textComplementaryColor2": "Cor complementaria 2", "Common.define.effectData.textCompress": "Comprimir", "Common.define.effectData.textContrast": "Contraste", "Common.define.effectData.textContrastingColor": "<PERSON><PERSON> de contraste", "Common.define.effectData.textCredits": "C<PERSON>dit<PERSON>", "Common.define.effectData.textCrescentMoon": "Lúa crecente", "Common.define.effectData.textCurveDown": "Curva para abaixo", "Common.define.effectData.textCurvedSquare": "Cadrado curvado", "Common.define.effectData.textCurvedX": "X curvado", "Common.define.effectData.textCurvyLeft": "Curvas á esquerda", "Common.define.effectData.textCurvyRight": "Curvas á dereita", "Common.define.effectData.textCurvyStar": "Estrel<PERSON> curvada", "Common.define.effectData.textCustomPath": "<PERSON>uta <PERSON>", "Common.define.effectData.textCuverUp": "Curva para arriba", "Common.define.effectData.textDarken": "Escurecer", "Common.define.effectData.textDecayingWave": "<PERSON>da descendente", "Common.define.effectData.textDesaturate": "Saturación reducida", "Common.define.effectData.textDiagonalDownRight": "Diagonal cara abaixo dereita", "Common.define.effectData.textDiagonalUpRight": "Diagonal para arriba dereita", "Common.define.effectData.textDiamond": "Diamante", "Common.define.effectData.textDisappear": "Desapa<PERSON>cer", "Common.define.effectData.textDissolveIn": "Disolver en", "Common.define.effectData.textDissolveOut": "Disolver fóra", "Common.define.effectData.textDown": "Abaixo", "Common.define.effectData.textDrop": "Colocar", "Common.define.effectData.textEmphasis": "Efecto de énfase", "Common.define.effectData.textEntrance": "Efecto de entrada", "Common.define.effectData.textEqualTriangle": "Trián<PERSON><PERSON>", "Common.define.effectData.textExciting": "Emocionante", "Common.define.effectData.textExit": "Efecto de saída", "Common.define.effectData.textExpand": "Expandir", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFigureFour": "Figura 8 catro veces", "Common.define.effectData.textFillColor": "Cor para encher", "Common.define.effectData.textFlip": "<PERSON><PERSON>", "Common.define.effectData.textFloat": "Flotante", "Common.define.effectData.textFloatDown": "Flotante cara abaixo", "Common.define.effectData.textFloatIn": "Flotante dentro", "Common.define.effectData.textFloatOut": "Flotante fóra", "Common.define.effectData.textFloatUp": "Flotante arriba", "Common.define.effectData.textFlyIn": "Voar en", "Common.define.effectData.textFlyOut": "Voar fora", "Common.define.effectData.textFontColor": "<PERSON><PERSON> da fonte", "Common.define.effectData.textFootball": "Fútbol", "Common.define.effectData.textFromBottom": "<PERSON><PERSON>", "Common.define.effectData.textFromBottomLeft": "Desde a parte inferior esquerda", "Common.define.effectData.textFromBottomRight": "Desde a parte inferior dereita", "Common.define.effectData.textFromLeft": "Desde a esquerda", "Common.define.effectData.textFromRight": "Desde a dereita", "Common.define.effectData.textFromTop": "Desde enriba", "Common.define.effectData.textFromTopLeft": "Desde a parte superior esquerda", "Common.define.effectData.textFromTopRight": "Desde a parte superior dereita", "Common.define.effectData.textFunnel": "Funil", "Common.define.effectData.textGrowShrink": "Aumentar/Encoller", "Common.define.effectData.textGrowTurn": "Aumentar e xirar", "Common.define.effectData.textGrowWithColor": "Aumentar coa cor", "Common.define.effectData.textHeart": "Corazón", "Common.define.effectData.textHeartbeat": "Latexo", "Common.define.effectData.textHexagon": "Hexágono", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Figura 8 horizontal", "Common.define.effectData.textHorizontalIn": "Horizontal entrante", "Common.define.effectData.textHorizontalOut": "Horizontal saínte", "Common.define.effectData.textIn": "En", "Common.define.effectData.textInFromScreenCenter": "Aumentar desde o centro de pantalla", "Common.define.effectData.textInSlightly": "Achegar lixeiramente", "Common.define.effectData.textInToScreenBottom": "Achegar cara a parte inferior da pantalla", "Common.define.effectData.textInvertedSquare": "Cadrado invertido", "Common.define.effectData.textInvertedTriangle": "Triángulo invertido", "Common.define.effectData.textLeft": "E<PERSON>rda", "Common.define.effectData.textLeftDown": "Esquerda e abaixo", "Common.define.effectData.textLeftUp": "Esquerda e enriba", "Common.define.effectData.textLighten": "Alumear", "Common.define.effectData.textLineColor": "<PERSON><PERSON> <PERSON> liña", "Common.define.effectData.textLines": "Liña<PERSON>", "Common.define.effectData.textLinesCurves": "Liñas curvas", "Common.define.effectData.textLoopDeLoop": "Bucle do bucle", "Common.define.effectData.textLoops": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textModerate": "Moderado", "Common.define.effectData.textNeutron": "Neutrón", "Common.define.effectData.textObjectCenter": "Centro do obxecto", "Common.define.effectData.textObjectColor": "Cor do obxecto", "Common.define.effectData.textOctagon": "Octágono", "Common.define.effectData.textOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOutFromScreenBottom": "Alonxar desde a zona inferior da pantalla", "Common.define.effectData.textOutSlightly": "Alonxar lixeiramente", "Common.define.effectData.textOutToScreenCenter": "Alonxar cara ao centro da pantalla", "Common.define.effectData.textParallelogram": "Paralelograma", "Common.define.effectData.textPath": "Ruta do movemento", "Common.define.effectData.textPeanut": "Cacahuete", "Common.define.effectData.textPeekIn": "Des<PERSON><PERSON>gar cara arriba", "Common.define.effectData.textPeekOut": "Des<PERSON><PERSON>gar cara abaixo", "Common.define.effectData.textPentagon": "Pentágono", "Common.define.effectData.textPinwheel": "Molinete", "Common.define.effectData.textPlus": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPointStar": "Estrela de puntas", "Common.define.effectData.textPointStar4": "Estrela de 4 puntas", "Common.define.effectData.textPointStar5": "Estrela de 5 puntas", "Common.define.effectData.textPointStar6": "Estrela de 6 puntas", "Common.define.effectData.textPointStar8": "Estrela de 8 puntas", "Common.define.effectData.textPulse": "<PERSON>mpul<PERSON>", "Common.define.effectData.textRandomBars": "Barras aleatorias", "Common.define.effectData.textRight": "Dereita", "Common.define.effectData.textRightDown": "Dereita e abaixo", "Common.define.effectData.textRightTriangle": "Triángulo rectángulo", "Common.define.effectData.textRightUp": "Dereita e enriba", "Common.define.effectData.textRiseUp": "Des<PERSON><PERSON>gar cara arriba", "Common.define.effectData.textSCurve1": "Curva S 1", "Common.define.effectData.textSCurve2": "Curva S 2", "Common.define.effectData.textShape": "Forma", "Common.define.effectData.textShapes": "Formas", "Common.define.effectData.textShimmer": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShrinkTurn": "Reducir e xirar", "Common.define.effectData.textSineWave": "Onda sinusoidal", "Common.define.effectData.textSinkDown": "Afundirse", "Common.define.effectData.textSlideCenter": "Centro da diapositiva", "Common.define.effectData.textSpecial": "Especial", "Common.define.effectData.textSpin": "<PERSON><PERSON>", "Common.define.effectData.textSpinner": "Centrifugado", "Common.define.effectData.textSpiralIn": "Espiral cara dentro", "Common.define.effectData.textSpiralLeft": "Espiral cara a esquerda", "Common.define.effectData.textSpiralOut": "Espiral cara fóra", "Common.define.effectData.textSpiralRight": "Espiral cara a dereita", "Common.define.effectData.textSplit": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpoke1": "1 radio", "Common.define.effectData.textSpoke2": "2 radios", "Common.define.effectData.textSpoke3": "3 radios", "Common.define.effectData.textSpoke4": "4 radios", "Common.define.effectData.textSpoke8": "8 radios", "Common.define.effectData.textSpring": "Resorte", "Common.define.effectData.textSquare": "Cadrado", "Common.define.effectData.textStairsDown": "Escaleiras a<PERSON>o", "Common.define.effectData.textStretch": "<PERSON><PERSON>", "Common.define.effectData.textStrips": "Raias", "Common.define.effectData.textSubtle": "<PERSON><PERSON>", "Common.define.effectData.textSwivel": "<PERSON><PERSON>", "Common.define.effectData.textSwoosh": "As<PERSON><PERSON>", "Common.define.effectData.textTeardrop": "Bágoa", "Common.define.effectData.textTeeter": "Tambalear", "Common.define.effectData.textToBottom": "<PERSON> a<PERSON>", "Common.define.effectData.textToBottomLeft": "Cara abaixo á esquerda", "Common.define.effectData.textToBottomRight": "Cara abaixo á dereita", "Common.define.effectData.textToLeft": "Á esquerda", "Common.define.effectData.textToRight": "<PERSON> dereita", "Common.define.effectData.textToTop": "Cara arriba", "Common.define.effectData.textToTopLeft": "Cara arriba á esquerda", "Common.define.effectData.textToTopRight": "Cara arriba á dereita", "Common.define.effectData.textTransparency": "Transparencia", "Common.define.effectData.textTrapezoid": "Trapecio", "Common.define.effectData.textTurnDown": "<PERSON><PERSON> cara a<PERSON>o", "Common.define.effectData.textTurnDownRight": "<PERSON>rar cara abaixo e á dereita", "Common.define.effectData.textTurns": "<PERSON><PERSON>", "Common.define.effectData.textTurnUp": "<PERSON><PERSON> cara arriba", "Common.define.effectData.textTurnUpRight": "<PERSON><PERSON> cara arriba á dereita", "Common.define.effectData.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textUp": "Arriba", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Figura 8 vertical", "Common.define.effectData.textVerticalIn": "Vertical entrante", "Common.define.effectData.textVerticalOut": "Vertical saínte", "Common.define.effectData.textWave": "On<PERSON>", "Common.define.effectData.textWedge": "Triangular", "Common.define.effectData.textWheel": "Roda", "Common.define.effectData.textWhip": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textWipe": "Eliminar", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Ampliar", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON>", "Common.Translation.warnFileLocked": "O ficheiro está a editarse noutro aplicativo. Podes continuar editándoo e gardándoo como copia.", "Common.Translation.warnFileLockedBtnEdit": "Crear unha copia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Nova cor personalizada", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON> b<PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON> b<PERSON>", "Common.UI.ComboDataView.emptyComboText": "<PERSON> estilo", "Common.UI.ExtendedColorDialog.addButtonText": "Engadir", "Common.UI.ExtendedColorDialog.textCurrent": "Actual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor introducido é incorrecto. <br> Insira un valor do 000000 a FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor introducido é incorrecto. <br> Insira un valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sen cor", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Agochar o contrasinal", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Amosar o contrasinal", "Common.UI.SearchBar.textFind": "At<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON> busca", "Common.UI.SearchBar.tipNextResult": "Seguinte resultado", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir a configuración avanzada", "Common.UI.SearchBar.tipPreviousResult": "Anterior resultado", "Common.UI.SearchDialog.textHighlight": "<PERSON>zar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto da substitución", "Common.UI.SearchDialog.textSearchStart": "Insira o seu texto aquí", "Common.UI.SearchDialog.textTitle": "Buscar e reemprazar", "Common.UI.SearchDialog.textTitle2": "Buscar", "Common.UI.SearchDialog.textWholeWords": "Só palabras completas", "Common.UI.SearchDialog.txtBtnHideReplace": "Agochar substitución", "Common.UI.SearchDialog.txtBtnReplace": "Substituír", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituír todo", "Common.UI.SynchronizeTip.textDontShow": "Non volver a amosar esta mensaxe", "Common.UI.SynchronizeTip.textSynchronize": "Outro usuario cambiou o documento. <br> Prema para gardar os cambios e recarga as actualizacións.", "Common.UI.ThemeColorPalette.textRecentColors": "Colores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores do tema", "Common.UI.Themes.txtThemeClassicLight": "Clásico claro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Igual que o sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Non", "Common.UI.Window.okButtonText": "Aceptar", "Common.UI.Window.textConfirmation": "Confirmación", "Common.UI.Window.textDontShow": "Non volver a amosar esta mensaxe", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Información", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "Si", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "enderezo:", "Common.Views.About.txtLicensee": "LICENZA", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "correo electrónico:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versión", "Common.Views.AutoCorrectDialog.textAdd": "Engadir", "Common.Views.AutoCorrectDialog.textApplyText": "Aplicar mentres escribe", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorrección de texto", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformato mentres escribe", "Common.Views.AutoCorrectDialog.textBulleted": "Listas con viñetas automáticas", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Engadir punto co dobre espazo", "Common.Views.AutoCorrectDialog.textFLCells": "Poñer en maiúsculas a primeira letra das celas da táboa", "Common.Views.AutoCorrectDialog.textFLSentence": "Poñer en maiúscula a primeira letra dunha oración", "Common.Views.AutoCorrectDialog.textHyperlink": "Rutas da rede e Internet por hiperligazóns", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON><PERSON> (--) con gui<PERSON> longo (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorrección matemática", "Common.Views.AutoCorrectDialog.textNumbered": "Listas con numeración automática", "Common.Views.AutoCorrectDialog.textQuotes": "\"Aspas rectas\" con \"aspas tipográficas\"", "Common.Views.AutoCorrectDialog.textRecognized": "Funcións recoñecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expresións son expresións matemáticas recoñecidas. Non se cursarán automaticamente.", "Common.Views.AutoCorrectDialog.textReplace": "Substituír", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON><PERSON> mentres escribe", "Common.Views.AutoCorrectDialog.textReplaceType": "<PERSON><PERSON><PERSON><PERSON> o texto mentres escribe", "Common.Views.AutoCorrectDialog.textReset": "Restablecer", "Common.Views.AutoCorrectDialog.textResetAll": "Restablecer valores predeterminados", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorrección", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funcións recoñecidas deben conter só letras do A ao Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Calquera expresión que engadise, eliminarase e as eliminadas restauraranse. Desexa continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A corrección automática para %1 xa existe. Quere substituíla?", "Common.Views.AutoCorrectDialog.warnReset": "Calquera autocorrección que engadise eliminase e os modificados serán restaurados aos seus valores orixinais. Desexa continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A entrada de autocorrección de% 1 restablecerase ao seu valor orixinal. Queres continuar?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor do A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor do Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "Desde enriba", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON>", "Common.Views.Comments.textAdd": "Engadir", "Common.Views.Comments.textAddComment": "Engadir comentario", "Common.Views.Comments.textAddCommentToDoc": "Engadir comentario ao documento", "Common.Views.Comments.textAddReply": "Engadir resposta", "Common.Views.Comments.textAll": "Todo", "Common.Views.Comments.textAnonym": "Convidado(a)", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> os comentarios", "Common.Views.Comments.textComments": "Comentarios", "Common.Views.Comments.textEdit": "Aceptar", "Common.Views.Comments.textEnterCommentHint": "Insira o seu comentario aquí", "Common.Views.Comments.textHintAddComment": "Engadir comentario", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolto", "Common.Views.Comments.textSort": "Ordenar comentarios", "Common.Views.Comments.textViewResolved": "Non ten permiso para volver a abrir o documento", "Common.Views.Comments.txtEmpty": "Sen comentarios no documento", "Common.Views.CopyWarningDialog.textDontShow": "Non volver a amosar esta mensaxe", "Common.Views.CopyWarningDialog.textMsg": "As accións de copiar, cortar e pegar empregando os botóns da barra de ferramentas do editor e as accións do menú contextual realizaranse só nesta pestana do editor. <br> <br> Para copiar ou pegar en ou desde aplicativos fóra da pestana do editor, use as seguintes combinacións de teclado:", "Common.Views.CopyWarningDialog.textTitle": "Accións de copiar, cortar e pegar", "Common.Views.CopyWarningDialog.textToCopy": "para Copiar", "Common.Views.CopyWarningDialog.textToCut": "para Cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Pegar", "Common.Views.DocumentAccessDialog.textLoading": "Cargando...", "Common.Views.DocumentAccessDialog.textTitle": "Configuración para compartir", "Common.Views.ExternalDiagramEditor.textTitle": "Editor de gráfico", "Common.Views.Header.labelCoUsersDescr": "Usuarios que están editando o ficheiro:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Configuración avanzada", "Common.Views.Header.textBack": "Abrir ubicación do ficheiro", "Common.Views.Header.textCompactView": "Agochar barra de ferramentas", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON> regras", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON>as", "Common.Views.Header.textHideStatusBar": "Agochar barra de estado", "Common.Views.Header.textRemoveFavorite": "Eliminar dos Favoritos", "Common.Views.Header.textSaveBegin": "Gardando...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "Todos os cambios foron gardados", "Common.Views.Header.textSaveExpander": "Todos os cambios foron gardados", "Common.Views.Header.textShare": "Compartir", "Common.Views.Header.textZoom": "Ampliar", "Common.Views.Header.tipAccessRights": "Xestionar dereitos de acceso ao documento", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "<PERSON><PERSON>", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Gardar", "Common.Views.Header.tipSearch": "Buscar", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Abrir nunha xanela independente", "Common.Views.Header.tipUsers": "Ver usuarios", "Common.Views.Header.tipViewSettings": "Amosar configuración", "Common.Views.Header.tipViewUsers": "Ver usuarios e administrar dereitos de acceso ao documento", "Common.Views.Header.txtAccessRights": "Cambiar dereitos de acceso", "Common.Views.Header.txtRename": "Renomear", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON> historial", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Agochar cambios detal<PERSON>", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Amosar cambios detallad<PERSON>", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "<PERSON><PERSON><PERSON> da imaxe:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo debe ser unha URL no formato \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Hai que especificar número válido de filas e columnas.", "Common.Views.InsertTableDialog.txtColumns": "Número de columnas", "Common.Views.InsertTableDialog.txtMaxText": "O valor máximo para este campo é {0}.", "Common.Views.InsertTableDialog.txtMinText": "O valor mínimo para este campo é {0}.", "Common.Views.InsertTableDialog.txtRows": "Número <PERSON>", "Common.Views.InsertTableDialog.txtTitle": "Tamaño da táboa", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON> celda", "Common.Views.LanguageDialog.labelSelect": "Seleccionar o idioma do documento", "Common.Views.ListSettingsDialog.textBulleted": "Con viñetas", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.tipChange": "Cambiar viñeta", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>ñ<PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Cor", "Common.Views.ListSettingsDialog.txtNewBullet": "Nova viñeta", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% do texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON> en", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Configuración da lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Codificación", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtOpenFile": "Insira o contrasinal para abrir o ficheiro", "Common.Views.OpenDialog.txtPassword": "Contrasinal", "Common.Views.OpenDialog.txtProtected": "Unha vez que se inseriu o contrasinal e aberto o ficheiro, o contrasinal actual ao ficheiro restablecerase", "Common.Views.OpenDialog.txtTitle": "Elixir opcións de %1", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON>cheiro protexido", "Common.Views.PasswordDialog.txtDescription": "Estableza un contrasinal para protexer este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "O contrasinal de confirmación non é idéntico", "Common.Views.PasswordDialog.txtPassword": "Contrasinal", "Common.Views.PasswordDialog.txtRepeat": "Repetir o contrasinal", "Common.Views.PasswordDialog.txtTitle": "Estableza un contrasinal", "Common.Views.PasswordDialog.txtWarning": "Aviso: se perde ou esquece o contrasinal, non se poderá recuperar. Consérvao nun lugar seguro.", "Common.Views.PluginDlg.textLoading": "Cargando", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textLoading": "Cargando", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Encriptar con contrasinal", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> ou elimine o contrasinal", "Common.Views.Protection.hintSignature": "Enfadir sinatura dixital ou liña de sinatura", "Common.Views.Protection.txtAddPwd": "Engadir contrasinal", "Common.Views.Protection.txtChangePwd": "Cambiar contrasinal", "Common.Views.Protection.txtDeletePwd": "Eliminar contrasinal", "Common.Views.Protection.txtEncrypt": "Encriptar", "Common.Views.Protection.txtInvisibleSignature": "Engadir siantura dixital", "Common.Views.Protection.txtSignature": "Asinar o documento", "Common.Views.Protection.txtSignatureLine": "Engadir liña de sinatura", "Common.Views.RenameDialog.textName": "Nome do ficheiro", "Common.Views.RenameDialog.txtInvalidName": "O nome do ficheiro non debe conter os seguintes símbolos:", "Common.Views.ReviewChanges.hintNext": "Ao seguinte cambio", "Common.Views.ReviewChanges.hintPrev": "Ao cambio anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Co-edición a tempo real. Todos os cambios gárdanse de forma automática.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Use o bot<PERSON> \"Gardar\" para sincronizar os cambios que ou ti ou outros realizastes", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.tipCoAuthMode": "Estableza o modo de co-edición", "Common.Views.ReviewChanges.tipCommentRem": "Eliminar comentarios", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Eliminar comentarios actuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentarios", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver os comentarios actuais", "Common.Views.ReviewChanges.tipHistory": "Amosar a versión do historial", "Common.Views.ReviewChanges.tipRejectCurrent": "Rexeitar cambios actuais", "Common.Views.ReviewChanges.tipReview": "Seguemento de cambios", "Common.Views.ReviewChanges.tipReviewView": "Seleccionar o modo no que quere que se presenten os cambios", "Common.Views.ReviewChanges.tipSetDocLang": "Establecer o idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.tipSharing": "Xestionar dereitos de acceso ao documento", "Common.Views.ReviewChanges.txtAccept": "Aceptar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceptar todos os cambios", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceptar cambios", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.txtChat": "Conversa", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "O modo Co-edición", "Common.Views.ReviewChanges.txtCommentRemAll": "Eliminar todos los comentarios", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Eliminar comentarios actuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Eliminar os meus comentarios", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Eliminar os meus actuais comentarios", "Common.Views.ReviewChanges.txtCommentRemove": "Eliminar", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentários", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver os comentarios actuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver os meus comentarios", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver os meus comentarios actuais", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtFinal": "Todos os cambios aceptados (vista previa)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historial de versións", "Common.Views.ReviewChanges.txtMarkup": "Todos os cambios (Edición)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcación", "Common.Views.ReviewChanges.txtNext": "Se<PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Todos os cambios rexeitados (Vista previa)", "Common.Views.ReviewChanges.txtOriginalCap": "Orixinal", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Rexeitar todos os cambios", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON> camb<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Rexeitar cambios actuais", "Common.Views.ReviewChanges.txtSharing": "Compartir", "Common.Views.ReviewChanges.txtSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.txtTurnon": "Seguemento dos cambios", "Common.Views.ReviewChanges.txtView": "Modo de visualización", "Common.Views.ReviewPopover.textAdd": "Engadir", "Common.Views.ReviewPopover.textAddReply": "Engadir resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "Aceptar", "Common.Views.ReviewPopover.textMention": "+mención proporcionará acceso ao documento e enviará un correo electrónico", "Common.Views.ReviewPopover.textMentionNotify": "+mención notificará o usuario por correo electrónico", "Common.Views.ReviewPopover.textOpenAgain": "Abrir novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Non ten permiso para volver a abrir o documento", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Cargando", "Common.Views.SaveAsDlg.textTitle": "Cartafol para gardar", "Common.Views.SearchPanel.textCaseSensitive": "Diferenciar maiús<PERSON>s de minúsculas", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON> busca", "Common.Views.SearchPanel.textFind": "At<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Buscar e substituír", "Common.Views.SearchPanel.textMatchUsingRegExp": "Relacionar con expresións regulares", "Common.Views.SearchPanel.textNoMatches": "Non hai coincidencias", "Common.Views.SearchPanel.textNoSearchResults": "Non se obtiveron resultados", "Common.Views.SearchPanel.textReplace": "Substituír", "Common.Views.SearchPanel.textReplaceAll": "Substituír todo", "Common.Views.SearchPanel.textReplaceWith": "Substituír con", "Common.Views.SearchPanel.textSearchResults": "Resultados da busca: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Hai moitos resultados para amosar aquí", "Common.Views.SearchPanel.textWholeWords": "Só palabras completas", "Common.Views.SearchPanel.tipNextResult": "Seguinte resultado", "Common.Views.SearchPanel.tipPreviousResult": "Anterior resultado", "Common.Views.SelectFileDlg.textLoading": "Cargando", "Common.Views.SelectFileDlg.textTitle": "Seleccionar fonte de <PERSON>", "Common.Views.SignDialog.textBold": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "Certificar", "Common.Views.SignDialog.textChange": "Cambiar", "Common.Views.SignDialog.textInputName": "Inserir nome de quen asina", "Common.Views.SignDialog.textItalic": "Cursiva", "Common.Views.SignDialog.textNameError": "O nome do asinante non debe estar vacío.", "Common.Views.SignDialog.textPurpose": "Propósito ao asinar este documento", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> imaxe", "Common.Views.SignDialog.textSignature": "A sinatura vese como", "Common.Views.SignDialog.textTitle": "Asinar documento", "Common.Views.SignDialog.textUseImage": "ou premer 'Selecci<PERSON>r I<PERSON>' para usar unha imaxe como sinatura", "Common.Views.SignDialog.textValid": "Válido desde %1 ata %2", "Common.Views.SignDialog.tipFontName": "<PERSON>me da fonte", "Common.Views.SignDialog.tipFontSize": "<PERSON><PERSON><PERSON>e", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir a quen asine engadir comentarios no diálogo de sinatura", "Common.Views.SignSettingsDialog.textInfoEmail": "Correo electrónico", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON><PERSON><PERSON> de quen asina", "Common.Views.SignSettingsDialog.textInstructions": "Instrucións para o asinante", "Common.Views.SignSettingsDialog.textShowDate": "Presentar data da sinatura", "Common.Views.SignSettingsDialog.textTitle": "Preparación da sinatura", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo é obrigatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Signo de Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "Aspas dobres de peche", "Common.Views.SymbolTableDialog.textDOQuote": "Aspas dobres de apertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Espazo longo", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON> curto", "Common.Views.SymbolTableDialog.textEnSpace": "Espazo curto", "Common.Views.SymbolTableDialog.textFont": "Fonte", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON> sen ruptura", "Common.Views.SymbolTableDialog.textNBSpace": "Espazo de non separación", "Common.Views.SymbolTableDialog.textPilcrow": "Sinal de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 do espazo", "Common.Views.SymbolTableDialog.textRange": "Ra<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Símbolos utilizados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Sinal da marca rexistrada", "Common.Views.SymbolTableDialog.textSCQuote": "Aspas simples de peche", "Common.Views.SymbolTableDialog.textSection": "Sinal da sección", "Common.Views.SymbolTableDialog.textShortcut": "Teclas de atallo", "Common.Views.SymbolTableDialog.textSHyphen": "G<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Aspas simples de apertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca comercial", "Common.Views.UserNameDialog.textDontShow": "Non volver a preguntarme", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "A etiqueta non debe estar vacía.", "PE.Controllers.LeftMenu.leavePageText": "Todos os cambios non gardados deste documento perderanse.<br><PERSON><PERSON> \"Cancelar\" despois \"Gardar\" para gardalos. Prema \"Aceptar\" para desfacer todos os cambios non gardados.", "PE.Controllers.LeftMenu.newDocumentTitle": "Presentación sen nome", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Aviso", "PE.Controllers.LeftMenu.requestEditRightsText": "Solicitando dereitos de edición...", "PE.Controllers.LeftMenu.textLoadHistory": "Cargando historial de versións...", "PE.Controllers.LeftMenu.textNoTextFound": "Non se puideron atopar os datos que estabas buscando. Axuste as opcións de busca.", "PE.Controllers.LeftMenu.textReplaceSkipped": "A substitución foi realizada. {0} ocorrencias foron ignoradas.", "PE.Controllers.LeftMenu.textReplaceSuccess": "A busca foi realizada. Ocorrencias substituídas: {0}", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON> t<PERSON>", "PE.Controllers.Main.applyChangesTextText": "Cargando datos...", "PE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.convertationTimeoutText": "Excedeu o tempo límite de conversión.", "PE.Controllers.Main.criticalErrorExtText": "Prema \"Aceptar\" para volver á lista de documentos.", "PE.Controllers.Main.criticalErrorTitle": "Erro", "PE.Controllers.Main.downloadErrorText": "Fallou a descarga.", "PE.Controllers.Main.downloadTextText": "Descargando a presentación...", "PE.Controllers.Main.downloadTitleText": "Descargando presentación", "PE.Controllers.Main.errorAccessDeny": "Estás intentando realizar unha acción que non tes permitido facer.<br>Por favor, contacta co teu administrador de Servidor de Documentos.", "PE.Controllers.Main.errorBadImageUrl": "A URL da imaxe é incorrecta", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Perdeuse a conexión co servidor. O documento non pode ser editado agora.", "PE.Controllers.Main.errorComboSeries": "Para crear un gráfico combinado, seleccione ao menos dúas series de datos.", "PE.Controllers.Main.errorConnectToServer": "Non se puido gardar o documento. Comprobe a configuración de conexión ou contacte co administrador. <br> <PERSON><PERSON> premas no botón \"Aceptar\", solicitaráselle que descargue o documento.", "PE.Controllers.Main.errorDatabaseConnection": "<PERSON>rro externo.<br><PERSON>rro de conexión de base de datos.Por favor, póñase en contacto co soporte se o erro se mantén.", "PE.Controllers.Main.errorDataEncrypted": "Recibíronse cambios cifrados, e non poden ser cifrados", "PE.Controllers.Main.errorDataRange": "Intervalo de datos incorrecto.", "PE.Controllers.Main.errorDefaultMessage": "Código do erro: %1", "PE.Controllers.Main.errorEditingDownloadas": "Ocorreu un erro ao traballar no documento.<br>Utilice a opción 'Descargar como' para gardar unha copia do ficheiro no seu computador.", "PE.Controllers.Main.errorEditingSaveas": "Ocorreu un erro ao traballar no documento.<br>utilice a opción 'Gardar como...\" para gardar unha copia do ficheiro no seu computador.", "PE.Controllers.Main.errorEmailClient": "Non se puido atopar ningún cliente de correo electrónico", "PE.Controllers.Main.errorFilePassProtect": "O documento está protexido por un contrasinal e non pode ser aberto.", "PE.Controllers.Main.errorFileSizeExceed": "O tamaño do ficheiro excede os límites establecidos para o teu servidor.<br>Por favor, contacte co seu administrador de Servidor de Documentos para máis detalles.", "PE.Controllers.Main.errorForceSave": "Ocorreu un erro ao gardar o ficheiro. Utilice a opción 'Descargar como' para gardar o ficheiro no seu computador ou inténtao máis tarde.", "PE.Controllers.Main.errorKeyEncrypt": "Descritor da clave descoñecido", "PE.Controllers.Main.errorKeyExpire": "O descritor de clave expirou", "PE.Controllers.Main.errorLoadingFont": "Tipos de letra non cargados.<br>Por favor contacte o administrador do servidor de documentos.", "PE.Controllers.Main.errorProcessSaveResult": "Erro ao gardar.", "PE.Controllers.Main.errorServerVersion": "A versión do editor foi actualizada. A páxina será recargada para aplicar os cambios.", "PE.Controllers.Main.errorSessionAbsolute": "A sesión de edición de documentos caducou. Recarga a páxina.", "PE.Controllers.Main.errorSessionIdle": "O documento leva pouco tempo editándose. Recarga a páxina.", "PE.Controllers.Main.errorSessionToken": "A conexión co servidor interrompeuse. Recarga a páxina.", "PE.Controllers.Main.errorSetPassword": "Non se puido establecer o contrasinal.", "PE.Controllers.Main.errorStockChart": "Orde das filas incorrecta. Para crear un gráfico de cotizacións introduza os datos na folla de tal modo:<br> prezo de apertura, prezo máximo, prezo mínimo e prezo do peche.", "PE.Controllers.Main.errorToken": "O token de seguridade do documento non está formado correctamente. <br> Póñase en contacto co administrador do servidor de documentos.", "PE.Controllers.Main.errorTokenExpire": "O token de seguridade do documento caducou. <br> Póñase en contacto co administrador do servidor de documentos.", "PE.Controllers.Main.errorUpdateVersion": "Cambiouse a versión do ficheiro. A páxina será actualizada.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Restaurouse a conexión a Internet. A versión do ficheiro cambiou.<br>Antes de continuar traballando, necesitas descargar o ficheiro ou copiar o seu contido para estar seguro de que nada se perde e despois recargar a páxina.", "PE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON> mesmo, non se pode acceder ao ficheiro.", "PE.Controllers.Main.errorUsersExceed": "Superouse a cantidade de usuarios permitidos polo plan de prezos", "PE.Controllers.Main.errorViewerDisconnect": "Perdeuse a conexión. Aínda pode visualizar o documento,<br>pero on pode descargalo ou imprimilo ata que a conexión sexa restaurada e a páxina recargada.", "PE.Controllers.Main.leavePageText": "Ten cambios sen gardar nesta presentación. Prema en \"Permanecer nesta páxina\" e logo en \"Gardar\" para gardalos. Prema en \"Deixar esta páxina\" para descartar todos os cambios sen gardar.", "PE.Controllers.Main.leavePageTextOnClose": "Todos os cambios non gardados desta presentación perderanse.<br><PERSON><PERSON> \"Cancelar\" despois \"Gardar\" para gardalos. Prema \"Aceptar\" para desfacer todos os cambios non gardados.", "PE.Controllers.Main.loadFontsTextText": "Cargando datos...", "PE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadFontTextText": "Cargando datos...", "PE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadImagesTextText": "Cargando imaxes...", "PE.Controllers.Main.loadImagesTitleText": "Cargando imaxes", "PE.Controllers.Main.loadImageTextText": "Cargando imaxe...", "PE.Controllers.Main.loadImageTitleText": "Cargando imaxe", "PE.Controllers.Main.loadingDocumentTextText": "Cargando presentación...", "PE.Controllers.Main.loadingDocumentTitleText": "Cargando a presentación", "PE.Controllers.Main.loadThemeTextText": "Cargando tema...", "PE.Controllers.Main.loadThemeTitleText": "Cargando tema", "PE.Controllers.Main.notcriticalErrorTitle": "Aviso", "PE.Controllers.Main.openErrorText": "Ocorreu un erro ao abrir o ficheiro.", "PE.Controllers.Main.openTextText": "Abrindo presentación...", "PE.Controllers.Main.openTitleText": "Abrindo presentación", "PE.Controllers.Main.printTextText": "Imprimindo presentación...", "PE.Controllers.Main.printTitleText": "Imprimindo presentación", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON>a", "PE.Controllers.Main.requestEditFailedMessageText": "Alguén está editando esta presentación agora. Por favor inténteo de novo máis tarde.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON> den<PERSON>", "PE.Controllers.Main.saveErrorText": "Ocorreu un erro ao gardar o ficheiro.", "PE.Controllers.Main.saveErrorTextDesktop": "Este ficheiro non se pode gardar nin crear. <br> Os motivos posibles son: <br> 1. O ficheiro é de só lectura. <br> 2. O ficheiro está a ser editado por outros usuarios. <br> 3. O disco está cheo ou corrompido.", "PE.Controllers.Main.saveTextText": "Gardando presentación...", "PE.Controllers.Main.saveTitleText": "Gardando presentac<PERSON>", "PE.Controllers.Main.scriptLoadError": "A conexión é lenta e algúns dos compoñentes non foron cargados. Debe recargar a páxina.", "PE.Controllers.Main.splitDividerErrorText": "O número de filas debe ser un divisor de %1.", "PE.Controllers.Main.splitMaxColsErrorText": "O número de columnas debe ser inferior a %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "O número de filas debe ser inferior a %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "Aplicar a todas as ecuacións", "PE.Controllers.Main.textBuyNow": "Visitar sitio web", "PE.Controllers.Main.textChangesSaved": "Todos os cambios foron gardados", "PE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Prema para pechar o consello", "PE.Controllers.Main.textContactUs": "Contactar co equipo de vendas", "PE.Controllers.Main.textConvertEquation": "Esta ecuación creouse cunha versión antiga do editor de ecuacións que xa non é compatible. Para editalo, converte a ecuación ao formato Office Math ML. <br> Converter agora?", "PE.Controllers.Main.textCustomLoader": "Observe que, de acordo cos termos da licenza, non ten dereito a cambiar o cargador.<br>Entre en contacto co noso Departamento de Vendas para obter unha cotización.", "PE.Controllers.Main.textDisconnect": "Perdeuse a conexión", "PE.Controllers.Main.textGuest": "Convidado(a)", "PE.Controllers.Main.textHasMacros": "O ficheiro contén macros automáticas.<br>Quere executar macros?", "PE.Controllers.Main.textLearnMore": "Para saber máis", "PE.Controllers.Main.textLoadingDocument": "Cargando a presentación", "PE.Controllers.Main.textLongName": "Insira un nome que teña menos de 128 caracteres.", "PE.Controllers.Main.textNoLicenseTitle": "Alcanzouse o límite da licenza", "PE.Controllers.Main.textPaidFeature": "Característica de pago", "PE.Controllers.Main.textReconnect": "Restaurouse a conexión", "PE.Controllers.Main.textRemember": "Recordar a miña elección para todos os ficheiros", "PE.Controllers.Main.textRememberMacros": "Recorda a miña selección por todas as macros", "PE.Controllers.Main.textRenameError": "O nome de usuario non pode estar vacío.", "PE.Controllers.Main.textRenameLabel": "Insira un nome que se usará para a colaboración", "PE.Controllers.Main.textRequestMacros": "Unha macro fai unha petición á URL. Quere permitirlla a %1?", "PE.Controllers.Main.textShape": "Forma", "PE.Controllers.Main.textStrict": "Modo estrito", "PE.Controllers.Main.textText": "Тexto", "PE.Controllers.Main.textTryUndoRedo": "As funcións Desfacer / Refacer están desactivadas para o modo de coedición rápida. <br> Faga clic no botón \"Modo estrito\" para cambiar ao modo de coedición estricto para editar o ficheiro sen interferencias doutros usuarios e enviar os seus cambios só despois de gardalos. eles. Pode cambiar entre os modos de coedición usando o editor Configuración avanzada.", "PE.Controllers.Main.textTryUndoRedoWarn": "As funcións Desfacer/Refacer están activadas para o modo de coedición rápido.", "PE.Controllers.Main.titleLicenseExp": "A licenza expirou", "PE.Controllers.Main.titleServerVersion": "Editor actual<PERSON><PERSON>", "PE.Controllers.Main.txtAddFirstSlide": "Prema para engadir a primeira diapositiva", "PE.Controllers.Main.txtAddNotes": "Prema para engadir notas", "PE.Controllers.Main.txtArt": "O teu texto aquí", "PE.Controllers.Main.txtBasicShapes": "Formas básicas", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "PE.Controllers.Main.txtClipArt": "Galería de imaxes", "PE.Controllers.Main.txtDateTime": "Data e hora", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Título do gráfico", "PE.Controllers.Main.txtEditingMode": "Establecer o modo de edición...", "PE.Controllers.Main.txtErrorLoadHistory": "Erro ao cargar o historial", "PE.Controllers.Main.txtFiguredArrows": "Formas da frecha", "PE.Controllers.Main.txtFooter": "Rodapé", "PE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "Imaxe", "PE.Controllers.Main.txtLines": "Liña<PERSON>", "PE.Controllers.Main.txtLoading": "Cargando...", "PE.Controllers.Main.txtMath": "Matemáticas", "PE.Controllers.Main.txtMedia": "Multimedia", "PE.Controllers.Main.txtNeedSynchronize": "Ten actualizacións", "PE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtPicture": "Imaxe", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSeries": "Serie", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Chamada coa liña 1 (bordo e barra de énfase)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Chamada coa liña 2 (bordo e barra de énfase)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Chamada coa liña 3 (bordo e barra de énfase)", "PE.Controllers.Main.txtShape_accentCallout1": "Chamada coa liña 1 (barra de énfase)", "PE.Controllers.Main.txtShape_accentCallout2": "Chamada coa liña 2 (barra de énfa<PERSON>)", "PE.Controllers.Main.txtShape_accentCallout3": "Chamada coa liña 3 (barra de énfase)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON><PERSON> ou Botón Anterior", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Botón de inicio", "PE.Controllers.Main.txtShape_actionButtonBlank": "Botón en branco", "PE.Controllers.Main.txtShape_actionButtonDocument": "Botón Documento", "PE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Botón Adiante ou Seguinte", "PE.Controllers.Main.txtShape_actionButtonHelp": "Botón A<PERSON>", "PE.Controllers.Main.txtShape_actionButtonHome": "Botón Inicio", "PE.Controllers.Main.txtShape_actionButtonInformation": "Botón Información", "PE.Controllers.Main.txtShape_actionButtonMovie": "Botón Vídeo ", "PE.Controllers.Main.txtShape_actionButtonReturn": "Botón <PERSON>", "PE.Controllers.Main.txtShape_actionButtonSound": "Bo<PERSON><PERSON> Son", "PE.Controllers.Main.txtShape_arc": "Arco", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> do<PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector angular de frecha", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector angular de frecha dobre", "PE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON>a dobrada cara enriba", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Arco de bloque", "PE.Controllers.Main.txtShape_borderCallout1": "Chamada coa liña 1", "PE.Controllers.Main.txtShape_borderCallout2": "Chamada coa liña 2", "PE.Controllers.Main.txtShape_borderCallout3": "Chamada coa liña 3", "PE.Controllers.Main.txtShape_bracePair": "Clave dobre", "PE.Controllers.Main.txtShape_callout1": "Chamada coa liñea 1 (sen bordo)", "PE.Controllers.Main.txtShape_callout2": "Chamada coa liña 2 (sen bordo)", "PE.Controllers.Main.txtShape_callout3": "Chamada coa liña 3 (sen bordo)", "PE.Controllers.Main.txtShape_can": "Сilindro", "PE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON> angulares", "PE.Controllers.Main.txtShape_chord": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_circularArrow": "Frecha circular", "PE.Controllers.Main.txtShape_cloud": "Nube", "PE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON> de nube", "PE.Controllers.Main.txtShape_corner": "Canto", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector curvado da frecha", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector curvado de frecha dobre", "PE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON>echa curvada cara abaixo", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Frecha curvada cara á esquerda", "PE.Controllers.Main.txtShape_curvedRightArrow": "Frecha curvada cara á dereita", "PE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON>echa curvada cara enriba", "PE.Controllers.Main.txtShape_decagon": "Decágono", "PE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "PE.Controllers.Main.txtShape_diamond": "Diamante", "PE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "PE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON> onda", "PE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>a cara abaixo", "PE.Controllers.Main.txtShape_downArrowCallout": "<PERSON><PERSON> da frecha cara abaixo", "PE.Controllers.Main.txtShape_ellipse": "Elipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "Cinta curvada cara abaixo", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Cinta curvada cara arriba", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Proceso alternativo", "PE.Controllers.Main.txtShape_flowChartCollate": "Intercalar", "PE.Controllers.Main.txtShape_flowChartConnector": "Conector", "PE.Controllers.Main.txtShape_flowChartDecision": "Decisión", "PE.Controllers.Main.txtShape_flowChartDelay": "Atraso", "PE.Controllers.Main.txtShape_flowChartDisplay": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDocument": "Documento", "PE.Controllers.Main.txtShape_flowChartExtract": "Extracto", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Datos", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Almacenamento interno", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Disco magnético", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Almacenamento de acceso directo", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Almacenamento de acceso secuencial", "PE.Controllers.Main.txtShape_flowChartManualInput": "Entrada manual", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Operación manual", "PE.Controllers.Main.txtShape_flowChartMerge": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Multidocumento", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Conector f<PERSON><PERSON> da páxin<PERSON>", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Datos almacenados", "PE.Controllers.Main.txtShape_flowChartOr": "Diagrama do fluxo: Ou", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Proceso predefinido", "PE.Controllers.Main.txtShape_flowChartPreparation": "Preparación", "PE.Controllers.Main.txtShape_flowChartProcess": "Proceso", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Tarxeta", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Cinta perforada", "PE.Controllers.Main.txtShape_flowChartSort": "Ordenar", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "E", "PE.Controllers.Main.txtShape_flowChartTerminator": "Terminador", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_frame": "<PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "Medio marco", "PE.Controllers.Main.txtShape_heart": "Corazón", "PE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_hexagon": "Hexágono", "PE.Controllers.Main.txtShape_homePlate": "Pentágono", "PE.Controllers.Main.txtShape_horizontalScroll": "Desprazamento horizontal", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosión 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosión 2", "PE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON><PERSON> es<PERSON>", "PE.Controllers.Main.txtShape_leftArrowCallout": "Chamada de frecha á esquerda", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON> clave", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON> corchete", "PE.Controllers.Main.txtShape_leftRightArrow": "Frecha esquerda e dereita", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Chamada de frecha esquerda e dereita", "PE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON><PERSON>a <PERSON>, dereita e arriba", "PE.Controllers.Main.txtShape_leftUpArrow": "Frecha esquerda e enriba", "PE.Controllers.Main.txtShape_lightningBolt": "Relámpago", "PE.Controllers.Main.txtShape_line": "Liña", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> do<PERSON>", "PE.Controllers.Main.txtShape_mathDivide": "División", "PE.Controllers.Main.txtShape_mathEqual": "Igual", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "PE.Controllers.Main.txtShape_mathNotEqual": "Non é igual", "PE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_moon": "Lúa", "PE.Controllers.Main.txtShape_noSmoking": "Non", "PE.Controllers.Main.txtShape_notchedRightArrow": "Frecha á dereita entallada", "PE.Controllers.Main.txtShape_octagon": "Octágono", "PE.Controllers.Main.txtShape_parallelogram": "Paralelograma", "PE.Controllers.Main.txtShape_pentagon": "Pentágono", "PE.Controllers.Main.txtShape_pie": "Sector do círculo", "PE.Controllers.Main.txtShape_plaque": "Asinar", "PE.Controllers.Main.txtShape_plus": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline1": "<PERSON> man alzada", "PE.Controllers.Main.txtShape_polyline2": "Forma libre", "PE.Controllers.Main.txtShape_quadArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_quadArrowCallout": "Chamada de frecha cuá<PERSON>", "PE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "Faixa cara abaixo", "PE.Controllers.Main.txtShape_ribbon2": "Cinta cara arriba", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> dereita", "PE.Controllers.Main.txtShape_rightArrowCallout": "Chamada de frecha á dereita", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON> clave", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON> corchete", "PE.Controllers.Main.txtShape_round1Rect": "Redondar rectángulo de esquina sinxela", "PE.Controllers.Main.txtShape_round2DiagRect": "Redondar rectángulo de esquina diagonal", "PE.Controllers.Main.txtShape_round2SameRect": "Redondar rectángulo de esquina do mesmo lado", "PE.Controllers.Main.txtShape_roundRect": "Rectángulo con esquinas redondeadas", "PE.Controllers.Main.txtShape_rtTriangle": "Triángulo rectángulo", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON> sorrinte", "PE.Controllers.Main.txtShape_snip1Rect": "Recortar rectángulo de esquina sinxela", "PE.Controllers.Main.txtShape_snip2DiagRect": "Recortar rectángulo de esquina diagonal", "PE.Controllers.Main.txtShape_snip2SameRect": "Recortar rectángulo de esquina do mesmo lado", "PE.Controllers.Main.txtShape_snipRoundRect": "Recortar e redondar rectángulo de esquina sinxela", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "Estrela de 10 puntos", "PE.Controllers.Main.txtShape_star12": "Estrela de 12 puntos", "PE.Controllers.Main.txtShape_star16": "Estrela de 16 puntos", "PE.Controllers.Main.txtShape_star24": "Estrela de 24 puntos", "PE.Controllers.Main.txtShape_star32": "Estrela de 32 puntos", "PE.Controllers.Main.txtShape_star4": "Estrela de 4 puntos", "PE.Controllers.Main.txtShape_star5": "Estrela de 5 puntos", "PE.Controllers.Main.txtShape_star6": "Estrela de 6 puntos", "PE.Controllers.Main.txtShape_star7": "Estrela de 7 puntos", "PE.Controllers.Main.txtShape_star8": "Estrela de 8 puntos", "PE.Controllers.Main.txtShape_stripedRightArrow": "Frecha á dereita con bandas", "PE.Controllers.Main.txtShape_sun": "Sol", "PE.Controllers.Main.txtShape_teardrop": "Bágoa", "PE.Controllers.Main.txtShape_textRect": "Caixa de texto", "PE.Controllers.Main.txtShape_trapezoid": "Trapecio", "PE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON>a cara arriba", "PE.Controllers.Main.txtShape_upArrowCallout": "Chamada de frecha cara arriba", "PE.Controllers.Main.txtShape_upDownArrow": "Frecha cara arriba e abaixo", "PE.Controllers.Main.txtShape_uturnArrow": "Frecha en U", "PE.Controllers.Main.txtShape_verticalScroll": "Disposición vertical", "PE.Controllers.Main.txtShape_wave": "On<PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Chamada rectangular", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Chamada rectangular redondeada", "PE.Controllers.Main.txtSldLtTBlank": "En branco", "PE.Controllers.Main.txtSldLtTChart": "Gráfico", "PE.Controllers.Main.txtSldLtTChartAndTx": "Gráfico e texto", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Galería de imaxes e texto", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Galería de imaxes e texto vertical", "PE.Controllers.Main.txtSldLtTCust": "Personalizar", "PE.Controllers.Main.txtSldLtTDgm": "Diagrama", "PE.Controllers.Main.txtSldLtTFourObj": "Catro obxectos", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Multimedia e texto", "PE.Controllers.Main.txtSldLtTObj": "Título e Obxecto", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Obxecto e dous obxectos", "PE.Controllers.Main.txtSldLtTObjAndTx": "Obxecto e texto", "PE.Controllers.Main.txtSldLtTObjOnly": "Obxecto", "PE.Controllers.Main.txtSldLtTObjOverTx": "Obxecto enriba do texto", "PE.Controllers.Main.txtSldLtTObjTx": "T<PERSON><PERSON>lo, obxecto e lenda", "PE.Controllers.Main.txtSldLtTPicTx": "Imaxe e lenda", "PE.Controllers.Main.txtSldLtTSecHead": "Cabeceira da sección", "PE.Controllers.Main.txtSldLtTTbl": "Táboa", "PE.Controllers.Main.txtSldLtTTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON> tí<PERSON>lo", "PE.Controllers.Main.txtSldLtTTwoColTx": "Texto a dúas columnas", "PE.Controllers.Main.txtSldLtTTwoObj": "Dous obxectos", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Dous obxectos e objeto", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Dous obxectos e texto", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Dous obxectos enriba do texto", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Dous textos e dous obxectos", "PE.Controllers.Main.txtSldLtTTx": "Тexto", "PE.Controllers.Main.txtSldLtTTxAndChart": "Texto e Gráfico", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Texto e Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Texto e multimedia", "PE.Controllers.Main.txtSldLtTTxAndObj": "Texto e Objeto", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Texto e dous obxectos", "PE.Controllers.Main.txtSldLtTTxOverObj": "Texto sobre Objeto", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Título vertical e Texto", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Título vertical e Texto sobre gráfico", "PE.Controllers.Main.txtSldLtTVertTx": "Texto vertical", "PE.Controllers.Main.txtSlideNumber": "Número da diapositiva", "PE.Controllers.Main.txtSlideSubtitle": "Subtítulo da diapositiva", "PE.Controllers.Main.txtSlideText": "Texto da diapositiva", "PE.Controllers.Main.txtSlideTitle": "<PERSON><PERSON><PERSON><PERSON> da diapositiva", "PE.Controllers.Main.txtStarsRibbons": "Cintas e estrelas", "PE.Controllers.Main.txtTheme_basic": "Básico", "PE.Controllers.Main.txtTheme_blank": "En branco", "PE.Controllers.Main.txtTheme_classic": "Clásico", "PE.Controllers.Main.txtTheme_corner": "Canto", "PE.Controllers.Main.txtTheme_dotted": "<PERSON> <PERSON>", "PE.Controllers.Main.txtTheme_green": "Verde", "PE.Controllers.Main.txtTheme_green_leaf": "Folla verde", "PE.Controllers.Main.txtTheme_lines": "Liña<PERSON>", "PE.Controllers.Main.txtTheme_office": "Oficina", "PE.Controllers.Main.txtTheme_office_theme": "Tema do Office", "PE.Controllers.Main.txtTheme_official": "Oficial", "PE.Controllers.Main.txtTheme_pixel": "Píxel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Tartaruga", "PE.Controllers.Main.txtXAxis": "Eixo X", "PE.Controllers.Main.txtYAxis": "Eixo Y", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON>.", "PE.Controllers.Main.unsupportedBrowserErrorText": "O seu navegador non é compatible.", "PE.Controllers.Main.uploadImageExtMessage": "Formato da imaxe descoñecido.", "PE.Controllers.Main.uploadImageFileCountMessage": "Non hai imaxes subidas.", "PE.Controllers.Main.uploadImageSizeMessage": "A imaxe é demasiado grande. O tamaño máximo é de 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Cargando imaxe...", "PE.Controllers.Main.uploadImageTitleText": "Cargando imaxe", "PE.Controllers.Main.waitText": "Agarde...", "PE.Controllers.Main.warnBrowserIE9": "O aplicativo ten baixa capacidade no IE9. Use IE10 ou superior", "PE.Controllers.Main.warnBrowserZoom": "A configuración da ampliación actual do seu navegador non é totalmente compatible. Restableza a ampliación predeterminada premendo Ctrl + 0.", "PE.Controllers.Main.warnLicenseExceeded": "Alcanzou o límite de conexións simultáneas con %1 editores. Este documento abrirase só para a súa visualización.<br><PERSON>r favor, contacte co seu administrador para recibir máis información.", "PE.Controllers.Main.warnLicenseExp": "A túa licenza caducou. <br> Actualiza a túa licenza e actualiza a páxina.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licenza expirada.<br>Non ten acceso á funcionalidade de edición de documentos.<br>por favor, póñase en contacto co seu administrador.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "A licenza precisa ser renovada.<br>Ten un acceso limitado á funcionalidade de edición de documentos.<br>Por favor, póñase en contacto co seu administrador para obter un acceso completo", "PE.Controllers.Main.warnLicenseUsersExceeded": "Alcanzou o límite de usuarios para os editores de %1. <PERSON><PERSON> favor, contacte co se uadministrador para recibir máis información.", "PE.Controllers.Main.warnNoLicense": "Alcanzou o límite de conexións simultáneas con %1 editores. Este documento abrirase só para as úa visualización.<br>Contacte co equipo de vendas de %1 para coñecer os termos de actualización persoal.", "PE.Controllers.Main.warnNoLicenseUsers": "Alcanzou o límite de usuarios para os editores de %1.<br>Contacte co equipo de vendas de %1 para coñecer os termos de actualización persoal.", "PE.Controllers.Main.warnProcessRightsChange": "O dereito de edición dp ficheiro é denegado.", "PE.Controllers.Search.notcriticalErrorTitle": "Aviso", "PE.Controllers.Search.warnReplaceString": "{0} non é un caracter especial válido para a caixa Substituír por.", "PE.Controllers.Statusbar.textDisconnect": "<b>Perdeuse a conexión</b><br>Intentando conectarse. Comproba a configuración de conexión.", "PE.Controllers.Statusbar.zoomText": "Ampliar {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "A fonte que vai gardar non está dispoñible no dispositivo actual. <br> O estilo de texto amosarase empregando unha das fontes do sistema, a fonte gardada utilizarase cando estea dispoñible. <br> Quere continuar?", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textBracket": "Corchetes", "PE.Controllers.Toolbar.textEmptyImgUrl": "Hai que especificar URL de imaxe.", "PE.Controllers.Toolbar.textFontSizeErr": "O valor introducido é incorrecto. <br> Insira un valor numérico entre 1 e 300", "PE.Controllers.Toolbar.textFraction": "Fraccións", "PE.Controllers.Toolbar.textFunction": "Funcións", "PE.Controllers.Toolbar.textInsert": "Inserir", "PE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Operadores grandes", "PE.Controllers.Toolbar.textLimitAndLog": "Límites e logaritmos", "PE.Controllers.Toolbar.textMatrix": "Matrices", "PE.Controllers.Toolbar.textOperator": "Operadores", "PE.Controllers.Toolbar.textRadical": "Radicais", "PE.Controllers.Toolbar.textScript": "Índices", "PE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textWarning": "Aviso", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Frecha superior dereita e esquerda", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Frecha superior esquerda", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Frecha superior dereita", "PE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "PE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Fórmula encadrada (con marcador de posición)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Fórmula encadrada (exemplo)", "PE.Controllers.Toolbar.txtAccent_Check": "Verificar", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave <PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Clave Superior", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "Barra superior con ABC", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR e con barra superposta", "PE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON> puntos", "PE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON> puntos", "PE.Controllers.Toolbar.txtAccent_Dot": "Punt<PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra dobre sobreposta", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Caracter de agrupación inferior", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Caracter de agrupación superior", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpón superior cara á esquerda", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpón superior cara á dereita", "PE.Controllers.Toolbar.txtAccent_Hat": "Circunflexo", "PE.Controllers.Toolbar.txtAccent_Smile": "Acento breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "Til", "PE.Controllers.Toolbar.txtBracket_Angle": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Corchetes con separadores", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Corchetes con separadores", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Curve": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Corchetes con separadores", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (dúas condicións)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (tres condicións)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Obxecto de pila", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Obxecto de pila", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente de binomio", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente de binomio", "PE.Controllers.Toolbar.txtBracket_Line": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_LineDouble": "Corchetes", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_LowLim": "Corchetes", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Round": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Corchetes con separadores", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Square": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Corchetes", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Corchetes", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtBracket_UppLim": "Corchetes", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Corchete único", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Corchete único", "PE.Controllers.Toolbar.txtFractionDiagonal": "Fracción nesgada", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Der<PERSON>da", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Der<PERSON>da", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Der<PERSON>da", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Der<PERSON>da", "PE.Controllers.Toolbar.txtFractionHorizontal": "Fracción lineal", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi sobre 2", "PE.Controllers.Toolbar.txtFractionSmall": "Fracción pequena", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Función de coseno inversa", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Función de coseno inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Función de cotangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Función de cotangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Función de cosecante inversa", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Función de cosecante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Función de secante inversa", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Función de secante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Función de seno inversa", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Función de seno inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Función de tanxente inversa", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Función de tangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_Cos": "Función de coseno", "PE.Controllers.Toolbar.txtFunction_Cosh": "Función de coseno hiperbólica", "PE.Controllers.Toolbar.txtFunction_Cot": "Función de cotangente", "PE.Controllers.Toolbar.txtFunction_Coth": "Función de cotangente hiperbólica", "PE.Controllers.Toolbar.txtFunction_Csc": "Función de cosecante", "PE.Controllers.Toolbar.txtFunction_Csch": "Función de cosecante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Seno zeta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sec": "Función de secante", "PE.Controllers.Toolbar.txtFunction_Sech": "Función de secante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Sin": "Función de seno", "PE.Controllers.Toolbar.txtFunction_Sinh": "Función de seno hiperbólica", "PE.Controllers.Toolbar.txtFunction_Tan": "Función da tanxente", "PE.Controllers.Toolbar.txtFunction_Tanh": "Función de tanxente hiperbólica", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Teta diferencial", "PE.Controllers.Toolbar.txtIntegral_dx": "Derivada x", "PE.Controllers.Toolbar.txtIntegral_dy": "Derivada y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralDouble": "Integral dobre", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral dobre", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral dobre", "PE.Controllers.Toolbar.txtIntegralOriented": "Integral de contorno", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de superficie", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superficie", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superficie", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de contorno", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de volume", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volume", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de volume", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralTriple": "Integral triple", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral triple", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral triple", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-produto", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-produto", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-produto", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-produto", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-produto", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unión", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Letra V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Letra V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Letra V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Letra V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Letra V", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersección", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersección", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersección", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersección", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersección", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Unión", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unión", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unión", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unión", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unión", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo do límite", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo do máximo", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Límite", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "PE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_1_2": "Matriz vacía 1x2", "PE.Controllers.Toolbar.txtMatrix_1_3": "Matriz vacía 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "Matriz vacía 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "Matriz vacía 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz vacía con corchetes", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Matriz vacía con corchetes", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz vacía con corchetes", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz vacía con corchetes", "PE.Controllers.Toolbar.txtMatrix_2_3": "Matriz vacía 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "Matriz vacía 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "Matriz vacía 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "Matriz vacía 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Puntos en liña de base", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Puntos en liña media", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Puntos diagonais", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Puntos verticais", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON>riz da identidade 2x2", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz da identidade 3x3", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz de identidade 3x3", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz de identidade 3x3", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Frecha inferior dereita e esquerda", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Frecha superior dereita e esquerda", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Frecha inferior esquerda", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Frecha superior esquerda", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Frecha inferior dereita", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Frecha superior dereita", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Dous puntos igual", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Produce con delta", "PE.Controllers.Toolbar.txtOperator_Definition": "Igual por definición", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Frecha inferior dereita e esquerda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Frecha superior dereita e esquerda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Frecha inferior esquerda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Frecha superior esquerda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Frecha inferior dereita", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Frecha superior dereita", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Igual igual", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Menos igual", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Máis igual", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> cadrada con índice", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radical con índice", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> cadrada", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Subscrito", "PE.Controllers.Toolbar.txtScriptSubSup": "Subíndice-Superíndice", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Subíndice-superíndice es<PERSON>do", "PE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "PE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "PE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "Case igual a", "PE.Controllers.Toolbar.txtSymbol_ast": "Operador asterisco", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "PE.Controllers.Toolbar.txtSymbol_bullet": "Operador de viñeta", "PE.Controllers.Toolbar.txtSymbol_cap": "Intersección", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "Elipse horizontal da liña media", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "PE.Controllers.Toolbar.txtSymbol_cup": "Unión", "PE.Controllers.Toolbar.txtSymbol_ddots": "Elipse en diagonal de dereita a esquerda", "PE.Controllers.Toolbar.txtSymbol_degree": "Grados", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Signo de división", "PE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON>a cara abaixo", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Conxunto vacío", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Épsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Igual", "PE.Controllers.Toolbar.txtSymbol_equiv": "Idéntico a", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Existe", "PE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grados Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "PE.Controllers.Toolbar.txtSymbol_gg": "Moito superior a", "PE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "PE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "PE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> es<PERSON>", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON> es<PERSON>-der<PERSON>a", "PE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "PE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "PE.Controllers.Toolbar.txtSymbol_ll": "Moito inferior a", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON> m<PERSON>", "PE.Controllers.Toolbar.txtSymbol_mu": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Non igual a", "PE.Controllers.Toolbar.txtSymbol_ni": "Contén como membro", "PE.Controllers.Toolbar.txtSymbol_not": "Non entrar", "PE.Controllers.Toolbar.txtSymbol_notexists": "Non existe", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_o": "Ómicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "PE.Controllers.Toolbar.txtSymbol_percent": "Porcentaxe", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>a", "PE.Controllers.Toolbar.txtSymbol_qed": "Fin da proba", "PE.Controllers.Toolbar.txtSymbol_rddots": "Elipse en diagonal de esquerda a dereita", "PE.Controllers.Toolbar.txtSymbol_rho": "Ro", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> dereita", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de radical", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON>r tanto", "PE.Controllers.Toolbar.txtSymbol_theta": "Zeta", "PE.Controllers.Toolbar.txtSymbol_times": "Signo de multiplicación", "PE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON>a cara arriba", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Épsilon (variante)", "PE.Controllers.Toolbar.txtSymbol_varphi": "Variante fi", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "Variante ro", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Variante zeta", "PE.Controllers.Toolbar.txtSymbol_vdots": "Elipse vertical", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Axustar á diapositiva", "PE.Controllers.Viewport.textFitWidth": "Axustar <PERSON> anchura", "PE.Views.Animation.str0_5": "0,5 s (moi rápido)", "PE.Views.Animation.str1": "1 s (rápido)", "PE.Views.Animation.str2": "2 s (medio)", "PE.Views.Animation.str20": "20 s (moi lento)", "PE.Views.Animation.str3": "3 s (lento)", "PE.Views.Animation.str5": "5 s (moi lento)", "PE.Views.Animation.strDelay": "Atraso", "PE.Views.Animation.strDuration": "Duración", "PE.Views.Animation.strRepeat": "<PERSON><PERSON>r", "PE.Views.Animation.strRewind": "Rebobinar", "PE.Views.Animation.strStart": "Iniciar", "PE.Views.Animation.strTrigger": "Desencadeador", "PE.Views.Animation.textAutoPreview": "Vista previa automática", "PE.Views.Animation.textMoreEffects": "<PERSON><PERSON> m<PERSON> efectos", "PE.Views.Animation.textMoveEarlier": "Mover antes", "PE.Views.Animation.textMoveLater": "Mover despois", "PE.Views.Animation.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textNoRepeat": "(ningún)", "PE.Views.Animation.textOnClickOf": "Ao premer con", "PE.Views.Animation.textOnClickSequence": "Secuencia de premas", "PE.Views.Animation.textStartAfterPrevious": "<PERSON><PERSON><PERSON> da <PERSON>", "PE.Views.Animation.textStartOnClick": "<PERSON><PERSON> premer", "PE.Views.Animation.textStartWithPrevious": "Coa anterior", "PE.Views.Animation.textUntilEndOfSlide": "Ata o fin da diapositiva", "PE.Views.Animation.textUntilNextClick": "Ata o seguinte prema", "PE.Views.Animation.txtAddEffect": "Engadir animación", "PE.Views.Animation.txtAnimationPane": "Panel de animación", "PE.Views.Animation.txtParameters": "Parámetros", "PE.Views.Animation.txtPreview": "Vista previa", "PE.Views.Animation.txtSec": "S", "PE.Views.AnimationDialog.textPreviewEffect": "Vista previa do efecto", "PE.Views.AnimationDialog.textTitle": "Máis efectos", "PE.Views.ChartSettings.textAdvanced": "Amosar configuración avanzado", "PE.Views.ChartSettings.textChartType": "Cambiar tipo de gráfico", "PE.Views.ChartSettings.textEditData": "<PERSON><PERSON>", "PE.Views.ChartSettings.textHeight": "Altura", "PE.Views.ChartSettings.textKeepRatio": "Proporcións constantes", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Descrición", "PE.Views.ChartSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textCenter": "Centro", "PE.Views.ChartSettingsAdvanced.textFrom": "De", "PE.Views.ChartSettingsAdvanced.textHeight": "Altura", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Proporcións constantes", "PE.Views.ChartSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.ChartSettingsAdvanced.textPosition": "Posición", "PE.Views.ChartSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textTitle": "Gráfico - Configuración avanzada", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON>ulo <PERSON> superior", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertical", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Establecer formato predeterminado para {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Establecer como o valor predeterminado", "PE.Views.DateTimeDialog.textFormat": "Formatos", "PE.Views.DateTimeDialog.textLang": "Idioma", "PE.Views.DateTimeDialog.textUpdate": "Actualizar automaticamente", "PE.Views.DateTimeDialog.txtTitle": "Data e hora", "PE.Views.DocumentHolder.aboveText": "Enriba", "PE.Views.DocumentHolder.addCommentText": "Engadir comentario", "PE.Views.DocumentHolder.addToLayoutText": "Engadir ao Deseño", "PE.Views.DocumentHolder.advancedImageText": "COnfiguración avanzada da imaxe", "PE.Views.DocumentHolder.advancedParagraphText": "Configuración avanzada do parágrafo", "PE.Views.DocumentHolder.advancedShapeText": "Configuración avanzada da forma", "PE.Views.DocumentHolder.advancedTableText": "Configuración avanzada da táboa", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.belowText": "Abaixo", "PE.Views.DocumentHolder.cellAlignText": "Aliñación vertical da celda", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "Ao centro", "PE.Views.DocumentHolder.columnText": "Columna", "PE.Views.DocumentHolder.deleteColumnText": "Eliminar columna", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> fila", "PE.Views.DocumentHolder.deleteTableText": "Eliminar táboa", "PE.Views.DocumentHolder.deleteText": "Eliminar", "PE.Views.DocumentHolder.direct270Text": "<PERSON>rar texto cara enriba", "PE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON> texto cara abaixo", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "Dirección do texto", "PE.Views.DocumentHolder.editChartText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.hyperlinkText": "Hiperligazón", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> todo", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON>na <PERSON>", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> dereita", "PE.Views.DocumentHolder.insertColumnText": "Inserir columna", "PE.Views.DocumentHolder.insertRowAboveText": "Fila de enriba", "PE.Views.DocumentHolder.insertRowBelowText": "Fila de abaixo", "PE.Views.DocumentHolder.insertRowText": "Inserir fila", "PE.Views.DocumentHolder.insertText": "Inserir", "PE.Views.DocumentHolder.langText": "Seleccionar idioma", "PE.Views.DocumentHolder.leftText": "Á esquerda", "PE.Views.DocumentHolder.loadSpellText": "Cargando variantes...", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "PE.Views.DocumentHolder.mniCustomTable": "Inserir táboa personalizada", "PE.Views.DocumentHolder.moreText": "Máis variantes...", "PE.Views.DocumentHolder.noSpellVariantsText": "Non hai variantes", "PE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "Eliminar hiperligazón", "PE.Views.DocumentHolder.rightText": "<PERSON> dereita", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.spellcheckText": "Сorrección ortográfica", "PE.Views.DocumentHolder.splitCellsText": "Di<PERSON><PERSON> celda...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON> celda", "PE.Views.DocumentHolder.tableText": "Táboa", "PE.Views.DocumentHolder.textArrangeBack": "Enviar ao fondo", "PE.Views.DocumentHolder.textArrangeBackward": "Enviar atrás", "PE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON> adiante", "PE.Views.DocumentHolder.textArrangeFront": "Traer á fronte", "PE.Views.DocumentHolder.textCopy": "Copiar", "PE.Views.DocumentHolder.textCrop": "Recortar", "PE.Views.DocumentHolder.textCropFill": "Encher", "PE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCut": "Cortar", "PE.Views.DocumentHolder.textDistributeCols": "Distribuír columnas", "PE.Views.DocumentHolder.textDistributeRows": "Distribu<PERSON><PERSON>", "PE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "PE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "PE.Views.DocumentHolder.textFromFile": "Do fi<PERSON>iro", "PE.Views.DocumentHolder.textFromStorage": "Desde almacenamento", "PE.Views.DocumentHolder.textFromUrl": "De URL", "PE.Views.DocumentHolder.textNextPage": "Seguinte diapositiva", "PE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textPrevPage": "Diapositiva anterior", "PE.Views.DocumentHolder.textReplace": "Substituír imaxe", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Xirar 90° á esquerda", "PE.Views.DocumentHolder.textRotate90": "Xirar 90° á dereita", "PE.Views.DocumentHolder.textShapeAlignBottom": "Aliñar á parte inferior", "PE.Views.DocumentHolder.textShapeAlignCenter": "Aliñar no centro", "PE.Views.DocumentHolder.textShapeAlignLeft": "Aliñar á esquerda", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Aliñar no medio", "PE.Views.DocumentHolder.textShapeAlignRight": "Aliñar á <PERSON>eita", "PE.Views.DocumentHolder.textShapeAlignTop": "Aliñar á parte superior", "PE.Views.DocumentHolder.textSlideSettings": "Configuración da diapositiva", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "Este elemento está sendo actualmente editado por outro usuario.", "PE.Views.DocumentHolder.toDictionaryText": "Engadir ao Dicionario", "PE.Views.DocumentHolder.txtAddBottom": "Engadir bordo inferior", "PE.Views.DocumentHolder.txtAddFractionBar": "Engadir barra de fracción", "PE.Views.DocumentHolder.txtAddHor": "Engadir liña horizontal", "PE.Views.DocumentHolder.txtAddLB": "Engadir liña inferior esquerda", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> bordo es<PERSON>", "PE.Views.DocumentHolder.txtAddLT": "Engadir liña superior esquerda", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON> bordo dereito", "PE.Views.DocumentHolder.txtAddTop": "Engadir bordo superior", "PE.Views.DocumentHolder.txtAddVer": "Engadir liña vertical", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> ao <PERSON>", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Fondo", "PE.Views.DocumentHolder.txtBorderProps": "Propiedades do bordo", "PE.Views.DocumentHolder.txtBottom": "Inferior", "PE.Views.DocumentHolder.txtChangeLayout": "Cambiar dese<PERSON>", "PE.Views.DocumentHolder.txtChangeTheme": "Cambiar tema", "PE.Views.DocumentHolder.txtColumnAlign": "Aliñación da columna", "PE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>o", "PE.Views.DocumentHolder.txtDeleteArg": "Eliminar argumento", "PE.Views.DocumentHolder.txtDeleteBreak": "Borrar salto manual", "PE.Views.DocumentHolder.txtDeleteChars": "Eliminar caracteres encerrados", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminar caracteres encerrados e separadores", "PE.Views.DocumentHolder.txtDeleteEq": "Eliminar ecuación", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Eliminar caracter", "PE.Views.DocumentHolder.txtDeleteRadical": "Eliminar radical", "PE.Views.DocumentHolder.txtDeleteSlide": "Eliminar diapositiva", "PE.Views.DocumentHolder.txtDistribHor": "Distribuír horizontalmente", "PE.Views.DocumentHolder.txtDistribVert": "Distribuír verticalmente", "PE.Views.DocumentHolder.txtDuplicateSlide": "Duplicar diapositiva", "PE.Views.DocumentHolder.txtFractionLinear": "Cambiar á fracción lineal", "PE.Views.DocumentHolder.txtFractionSkewed": "Cambiar á fracción inclinada", "PE.Views.DocumentHolder.txtFractionStacked": "Cambiar á fracción amontonada", "PE.Views.DocumentHolder.txtGroup": "Grupo", "PE.Views.DocumentHolder.txtGroupCharOver": "Caracter sobre o texto", "PE.Views.DocumentHolder.txtGroupCharUnder": "Caracter debaixo do texto", "PE.Views.DocumentHolder.txtHideBottom": "Agochar bordo inferior", "PE.Views.DocumentHolder.txtHideBottomLimit": "Agochar límite inferior", "PE.Views.DocumentHolder.txtHideCloseBracket": "Agochar corchete de peche", "PE.Views.DocumentHolder.txtHideDegree": "Agochar grado", "PE.Views.DocumentHolder.txtHideHor": "Agochar li<PERSON>", "PE.Views.DocumentHolder.txtHideLB": "Agochar liña inferior esquerda", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON> bordo es<PERSON>", "PE.Views.DocumentHolder.txtHideLT": "Agochar liña superior esquerda", "PE.Views.DocumentHolder.txtHideOpenBracket": "Agochar corchete de apertura", "PE.Views.DocumentHolder.txtHidePlaceholder": "Agochar marcador de posición", "PE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON> bordo der<PERSON>o", "PE.Views.DocumentHolder.txtHideTop": "Agochar bordo superior", "PE.Views.DocumentHolder.txtHideTopLimit": "Agochar límite superior", "PE.Views.DocumentHolder.txtHideVer": "Agochar liña vertical", "PE.Views.DocumentHolder.txtIncreaseArg": "Aumentar o tamaño do argumento", "PE.Views.DocumentHolder.txtInsertArgAfter": "Inserir argumento despois", "PE.Views.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "PE.Views.DocumentHolder.txtInsertBreak": "Inserir salto manual", "PE.Views.DocumentHolder.txtInsertEqAfter": "Inserir ecuación despois", "PE.Views.DocumentHolder.txtInsertEqBefore": "Inserir ecuación antes", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> só o texto", "PE.Views.DocumentHolder.txtLimitChange": "Cambiar ubicación dos límites", "PE.Views.DocumentHolder.txtLimitOver": "Límite sobre o texto", "PE.Views.DocumentHolder.txtLimitUnder": "Límite debaixo do texto", "PE.Views.DocumentHolder.txtMatchBrackets": "Coincidir corchetes co alto dos argumentos", "PE.Views.DocumentHolder.txtMatrixAlign": "Aliñamento de matriz", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Desprazar a diapositiva á fin", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Desprazar a diapositiva ao principio", "PE.Views.DocumentHolder.txtNewSlide": "Nova diapositiva", "PE.Views.DocumentHolder.txtOverbar": "Barra sobre texto", "PE.Views.DocumentHolder.txtPasteDestFormat": "Use o tema de destino", "PE.Views.DocumentHolder.txtPastePicture": "Imaxe", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Manter formatación original", "PE.Views.DocumentHolder.txtPressLink": "Prema {0} e na ligazón", "PE.Views.DocumentHolder.txtPreview": "Iniciar <PERSON>", "PE.Views.DocumentHolder.txtPrintSelection": "Imp<PERSON><PERSON>", "PE.Views.DocumentHolder.txtRemFractionBar": "Eliminar a barra de fracción", "PE.Views.DocumentHolder.txtRemLimit": "Eliminar límite", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Eliminar caracter do acento", "PE.Views.DocumentHolder.txtRemoveBar": "Eliminar barra", "PE.Views.DocumentHolder.txtRemScripts": "Eliminar ídnices", "PE.Views.DocumentHolder.txtRemSubscript": "Eliminar subíndice", "PE.Views.DocumentHolder.txtRemSuperscript": "Eliminar superíndice", "PE.Views.DocumentHolder.txtResetLayout": "Restablecer diapositiva", "PE.Views.DocumentHolder.txtScriptsAfter": "Índices despois do texto", "PE.Views.DocumentHolder.txtScriptsBefore": "Índices antes do texto", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "PE.Views.DocumentHolder.txtShowBottomLimit": "Amosar límite inferior", "PE.Views.DocumentHolder.txtShowCloseBracket": "Amosar corchete de peche", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON> grao", "PE.Views.DocumentHolder.txtShowOpenBracket": "Amosar corchete de apertura", "PE.Views.DocumentHolder.txtShowPlaceholder": "Amosar marcador de posición", "PE.Views.DocumentHolder.txtShowTopLimit": "Amosar límite superior", "PE.Views.DocumentHolder.txtSlide": "Diapositiva", "PE.Views.DocumentHolder.txtSlideHide": "<PERSON><PERSON><PERSON><PERSON>os<PERSON>", "PE.Views.DocumentHolder.txtStretchBrackets": "Alongar corchetes", "PE.Views.DocumentHolder.txtTop": "Parte superior", "PE.Views.DocumentHolder.txtUnderbar": "Barra abaixo de texto", "PE.Views.DocumentHolder.txtUngroup": "Desagrupar", "PE.Views.DocumentHolder.txtWarnUrl": "Prema nesta ligazón pode ser prexudicial para o seu dispositivo e os seus datos.<br> Ten a certeza de que quere continuar?", "PE.Views.DocumentHolder.vertAlignText": "Aliñamento vertical", "PE.Views.DocumentPreview.goToSlideText": "Ir a diapositiva", "PE.Views.DocumentPreview.slideIndexText": "Diapositiva {0} de {1}", "PE.Views.DocumentPreview.txtClose": "<PERSON>echar <PERSON>ac<PERSON>", "PE.Views.DocumentPreview.txtEndSlideshow": "Rematar presentación", "PE.Views.DocumentPreview.txtExitFullScreen": "<PERSON><PERSON><PERSON> da pantalla completa", "PE.Views.DocumentPreview.txtFinalMessage": "Fin da previsualización da diapositiva. Prema para saír.", "PE.Views.DocumentPreview.txtFullScreen": "Pantalla completa", "PE.Views.DocumentPreview.txtNext": "Seguinte diapositiva", "PE.Views.DocumentPreview.txtPageNumInvalid": "Número da diapositiva inválida", "PE.Views.DocumentPreview.txtPause": "Deter presentación", "PE.Views.DocumentPreview.txtPlay": "Iniciar <PERSON>", "PE.Views.DocumentPreview.txtPrev": "Diapositiva anterior", "PE.Views.DocumentPreview.txtReset": "Restablecer", "PE.Views.FileMenu.btnAboutCaption": "Sobre", "PE.Views.FileMenu.btnBackCaption": "Abrir ubicación do ficheiro", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnCreateNewCaption": "Crear novo", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> como", "PE.Views.FileMenu.btnExitCaption": "Saír", "PE.Views.FileMenu.btnFileOpenCaption": "Abrir", "PE.Views.FileMenu.btnHelpCaption": "Axuda", "PE.Views.FileMenu.btnHistoryCaption": "Historial de versións", "PE.Views.FileMenu.btnInfoCaption": "Información sobre presentación", "PE.Views.FileMenu.btnPrintCaption": "Imprimir", "PE.Views.FileMenu.btnProtectCaption": "Protexer", "PE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "PE.Views.FileMenu.btnRenameCaption": "Renomear", "PE.Views.FileMenu.btnReturnCaption": "Volver á presentación", "PE.Views.FileMenu.btnRightsCaption": "Dereitos de acceso", "PE.Views.FileMenu.btnSaveAsCaption": "Gardar como", "PE.Views.FileMenu.btnSaveCaption": "Gardar", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Gardar copia como", "PE.Views.FileMenu.btnSettingsCaption": "Configuracións avanzadas", "PE.Views.FileMenu.btnToEditCaption": "Editar presentación", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Presentación en branco", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Crear novo", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON> autor", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Engadir texto", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicativo", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Cambiar dereitos de acceso", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comentario", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificación por", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificación", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Propietario", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Ubicación", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persoas que teñen dereitos", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Subido", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Cambiar dereitos de acceso", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Persoas que teñen dereitos", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Con contrasinal", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protexer presentación", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Con sinatura", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar presentación", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "A edición eliminará as sinaturas da presentación.<br>Ten a certeza de que quere continuar?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Esta presentación protexeuse cun contrasinal", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "As sinaturas válidas engadíronse á presentación. A presentación está protexida e non se pode editar.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algunhas das sinaturas dixitais na presentación son inválidas ou non se puideron verificar. A presentación está protexida e non se pode editar.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver sinaturas", "PE.Views.FileMenuPanels.Settings.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "O modo Co-edición", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Busca das fontes", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Omitir p<PERSON> en MAIÚSCULAS", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON> con números", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Configuración das macros", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Amosar o botón Opcións de pegado cando se pegue contido", "PE.Views.FileMenuPanels.Settings.strStrict": "Estrito", "PE.Views.FileMenuPanels.Settings.strTheme": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strUnit": "Unidade de medida", "PE.Views.FileMenuPanels.Settings.strZoom": "Valor de ampliación predeterminado", "PE.Views.FileMenuPanels.Settings.text10Minutes": "A cada 10 minutos", "PE.Views.FileMenuPanels.Settings.text30Minutes": "A cada 30 minutos", "PE.Views.FileMenuPanels.Settings.text5Minutes": "A cada 5 minutos", "PE.Views.FileMenuPanels.Settings.text60Minutes": "A cada hora", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON> de aliñamento", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Recuperación automática", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Gardar automaticamente", "PE.Views.FileMenuPanels.Settings.textDisabled": "Desactivado", "PE.Views.FileMenuPanels.Settings.textForceSave": "Gardar versións intermedias", "PE.Views.FileMenuPanels.Settings.textMinute": "A cada minuto", "PE.Views.FileMenuPanels.Settings.txtAll": "Ver todo", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opcións de autocorrección", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Modo de caché predeterminado", "PE.Views.FileMenuPanels.Settings.txtCm": "Centímetro", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Colaboración", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editar e gardar", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Coedición en tempo real. Todos os cambios se gardarán automaticamente", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Axustar á diapositiva", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Axustar <PERSON> anchura", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Xeroglíficos", "PE.Views.FileMenuPanels.Settings.txtInch": "Pulgada", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON>er últimos", "PE.Views.FileMenuPanels.Settings.txtMac": "coma OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "PE.Views.FileMenuPanels.Settings.txtProofing": "Corrección", "PE.Views.FileMenuPanels.Settings.txtPt": "Punt<PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Activar todo", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Activar todas as macros sen notificación", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Сorrección ortográfica", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Desactivar todo", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Desactivar todas as macros sen notificación", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Usar o bot<PERSON> \"Gardar\" para sincronizar os teus cambios e os dos demais", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Usar a tecla Alt para navegar pola interface do usuario usando o teclado", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Usar a tecla Opcións para navegar pola interface do usuario usando o teclado", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Amosar notificación", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Desactivar todas as macros con notificación", "PE.Views.FileMenuPanels.Settings.txtWin": "coma Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON> de t<PERSON>allo", "PE.Views.HeaderFooterDialog.applyAllText": "Aplicar a todos", "PE.Views.HeaderFooterDialog.applyText": "Aplicar", "PE.Views.HeaderFooterDialog.diffLanguage": "Non podes usar un formato de data nun idioma diferente ao da diapositiva mestra. <br> Para cambiar o mestre, preme en \"Aplicar a todos\" no canto de \"Aplicar\"", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Aviso", "PE.Views.HeaderFooterDialog.textDateTime": "Data e hora", "PE.Views.HeaderFooterDialog.textFixed": "Fixado", "PE.Views.HeaderFooterDialog.textFooter": "Texto no rodapé", "PE.Views.HeaderFooterDialog.textFormat": "Formatos", "PE.Views.HeaderFooterDialog.textLang": "Idioma", "PE.Views.HeaderFooterDialog.textNotTitle": "Non amosar en diapositiva de título", "PE.Views.HeaderFooterDialog.textPreview": "Vista previa", "PE.Views.HeaderFooterDialog.textSlideNum": "Número da diapositiva", "PE.Views.HeaderFooterDialog.textTitle": "COnfiguración do rodapé", "PE.Views.HeaderFooterDialog.textUpdate": "Actualizar automaticamente", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Vincular a", "PE.Views.HyperlinkSettingsDialog.textDefault": "Fragmento do texto seleccionado", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Insira lenda aquí", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Insira ligazón aquí", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserir información sobre ferramentas aquí", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Ligazón externa", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Diapositiva nesta presentación", "PE.Views.HyperlinkSettingsDialog.textSlides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTipText": "Información en pantalla", "PE.Views.HyperlinkSettingsDialog.textTitle": "Configuración da hiperligazón", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatorio", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Primeira diapositiva", "PE.Views.HyperlinkSettingsDialog.txtLast": "Última diapositiva", "PE.Views.HyperlinkSettingsDialog.txtNext": "Seguinte diapositiva", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo debe ser unha URL no formato \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Diapositiva anterior", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres. ", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Diapositiva", "PE.Views.ImageSettings.textAdvanced": "Amosar configuración avanzado", "PE.Views.ImageSettings.textCrop": "Recortar", "PE.Views.ImageSettings.textCropFill": "Encher", "PE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropToShape": "Cortar para dar forma", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "Editar obxecto", "PE.Views.ImageSettings.textFitSlide": "Axustar á diapositiva", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "Do fi<PERSON>iro", "PE.Views.ImageSettings.textFromStorage": "Desde almacenamento", "PE.Views.ImageSettings.textFromUrl": "De URL", "PE.Views.ImageSettings.textHeight": "Altura", "PE.Views.ImageSettings.textHint270": "Xirar 90° á esquerda", "PE.Views.ImageSettings.textHint90": "Xirar 90° á dereita", "PE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "PE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "PE.Views.ImageSettings.textInsert": "Substituír imaxe", "PE.Views.ImageSettings.textOriginalSize": "Tamaño real", "PE.Views.ImageSettings.textRecentlyUsed": "Usados recentemente", "PE.Views.ImageSettings.textRotate90": "Xirar 90°", "PE.Views.ImageSettings.textRotation": "Rotación", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Descrición", "PE.Views.ImageSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Centro", "PE.Views.ImageSettingsAdvanced.textFlipped": "Volteado", "PE.Views.ImageSettingsAdvanced.textFrom": "De", "PE.Views.ImageSettingsAdvanced.textHeight": "Altura", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporcións constantes", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.ImageSettingsAdvanced.textPosition": "Posición", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotación", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "Imaxe - configuración avanzada", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON>ulo <PERSON> superior", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "PE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "Sobre", "PE.Views.LeftMenu.tipChat": "Conversa", "PE.Views.LeftMenu.tipComments": "Comentarios", "PE.Views.LeftMenu.tipPlugins": "Plugins", "PE.Views.LeftMenu.tipSearch": "Buscar", "PE.Views.LeftMenu.tipSlides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSupport": "Comentarios e soporte", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtDeveloper": "MODO DO DESENVOLVEDOR", "PE.Views.LeftMenu.txtEditor": "Editor de presentacións", "PE.Views.LeftMenu.txtLimit": "Limitar acceso", "PE.Views.LeftMenu.txtTrial": "MODO DE PROBA", "PE.Views.LeftMenu.txtTrialDev": "Modo de programador de proba", "PE.Views.ParagraphSettings.strLineHeight": "Espazo entre liñas", "PE.Views.ParagraphSettings.strParagraphSpacing": "Espazo entre parágrafos", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "Amosar configuración avanzado", "PE.Views.ParagraphSettings.textAt": "En", "PE.Views.ParagraphSettings.textAtLeast": "Polo menos", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "Exactamente", "PE.Views.ParagraphSettings.txtAutoText": "Automático", "PE.Views.ParagraphSettingsAdvanced.noTabs": "As lapelas especificadas aparecerán neste campo", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Todas en maiúsculas", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Dobre riscado", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Reti<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espazo entre liñas", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Dereita", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fonte", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Sangrías e espazo", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Maiús<PERSON>s pequenas", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Espazo", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Riscado", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "PE.Views.ParagraphSettingsAdvanced.strTabs": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espazo entre caracteres", "PE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON><PERSON> predeterminada", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Efectos", "PE.Views.ParagraphSettingsAdvanced.textExact": "Exactamente", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira liña", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendido", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Xustificado", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ningún)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Eliminar", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Eliminar todo", "PE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Ao centro", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posición da lapela", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Dereita", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Configuración avanzada", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "PE.Views.RightMenu.txtChartSettings": "Configuración do gráfico", "PE.Views.RightMenu.txtImageSettings": "Configuración da imaxe", "PE.Views.RightMenu.txtParagraphSettings": "Configuración do parágrafo", "PE.Views.RightMenu.txtShapeSettings": "Configuración da forma", "PE.Views.RightMenu.txtSignatureSettings": "Configuración da sinatura", "PE.Views.RightMenu.txtSlideSettings": "Configuración da diapositiva", "PE.Views.RightMenu.txtTableSettings": "Configuración da táboa", "PE.Views.RightMenu.txtTextArtSettings": "Configuración do Text Art ", "PE.Views.ShapeSettings.strBackground": "<PERSON><PERSON> <PERSON> fondo", "PE.Views.ShapeSettings.strChange": "Cambiar autoforma", "PE.Views.ShapeSettings.strColor": "Cor", "PE.Views.ShapeSettings.strFill": "Encher", "PE.Views.ShapeSettings.strForeground": "Cor de primeiro plano", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strShadow": "<PERSON><PERSON> sombra", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "Liña", "PE.Views.ShapeSettings.strTransparency": "Opacidade", "PE.Views.ShapeSettings.strType": "Tipo", "PE.Views.ShapeSettings.textAdvanced": "Amosar configuración avanzado", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "O valor introducido é incorrecto. <br> Insira un valor numérico entre 0 e 1584 puntos.", "PE.Views.ShapeSettings.textColor": "Cor para encher", "PE.Views.ShapeSettings.textDirection": "Dirección ", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON>", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "Do fi<PERSON>iro", "PE.Views.ShapeSettings.textFromStorage": "Desde almacenamento", "PE.Views.ShapeSettings.textFromUrl": "De URL", "PE.Views.ShapeSettings.textGradient": "Puntos de degradado ", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textHint270": "Xirar 90° á esquerda", "PE.Views.ShapeSettings.textHint90": "Xirar 90° á dereita", "PE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "PE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "PE.Views.ShapeSettings.textImageTexture": "Imaxde ou textura", "PE.Views.ShapeSettings.textLinear": "Lineal", "PE.Views.ShapeSettings.textNoFill": "Sen encher", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPosition": "Posición", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Usados recentemente", "PE.Views.ShapeSettings.textRotate90": "Xirar 90°", "PE.Views.ShapeSettings.textRotation": "Rotación", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> imaxe", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "De textura", "PE.Views.ShapeSettings.textTile": "Mosaico", "PE.Views.ShapeSettings.tipAddGradientPoint": "Engadir punto de degradado", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "Cartón", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "PE.Views.ShapeSettings.txtGrain": "Grano", "PE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "Papel gris", "PE.Views.ShapeSettings.txtKnit": "Texido", "PE.Views.ShapeSettings.txtLeather": "Coiro", "PE.Views.ShapeSettings.txtNoBorders": "Sen liña", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "Madeira", "PE.Views.ShapeSettingsAdvanced.strColumns": "Columnas", "PE.Views.ShapeSettingsAdvanced.strMargins": "Marxes interiores", "PE.Views.ShapeSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrición", "PE.Views.ShapeSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Autoaxustar", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Tam<PERSON>ño inicial", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Inferior", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "PE.Views.ShapeSettingsAdvanced.textCenter": "Centro", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Número de columnas", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Tamaño final", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "PE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Volteado", "PE.Views.ShapeSettingsAdvanced.textFrom": "De", "PE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de combinación", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporcións constantes", "PE.Views.ShapeSettingsAdvanced.textLeft": "E<PERSON>rda", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "Non axustar automaticamente", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.ShapeSettingsAdvanced.textPosition": "Posición", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "A<PERSON>ust<PERSON> ta<PERSON> da forma ao texto", "PE.Views.ShapeSettingsAdvanced.textRight": "Dereita", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotación", "PE.Views.ShapeSettingsAdvanced.textRound": "Redondeado", "PE.Views.ShapeSettingsAdvanced.textShrink": "Comprimir o texto ao desbordarse", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Espazo entre columnas", "PE.Views.ShapeSettingsAdvanced.textSquare": "Cadrado", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Caixa de texto", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forma - configuración avanzada", "PE.Views.ShapeSettingsAdvanced.textTop": "Parte superior", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON>ulo <PERSON> superior", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertical", "PE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Grosores e frechas", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "PE.Views.SignatureSettings.strDelete": "Elimine a sinatura", "PE.Views.SignatureSettings.strDetails": "Detalles da sinatura", "PE.Views.SignatureSettings.strInvalid": "Sinaturas inválidas", "PE.Views.SignatureSettings.strSign": "Asinar", "PE.Views.SignatureSettings.strSignature": "Asinatura", "PE.Views.SignatureSettings.strValid": "Sinaturas válidas", "PE.Views.SignatureSettings.txtContinueEditing": "Editar de todas maneira<PERSON>", "PE.Views.SignatureSettings.txtEditWarning": "A edición eliminará as sinaturas da presentación.<br>Ten a certeza de que quere continuar?", "PE.Views.SignatureSettings.txtRemoveWarning": "Desexa eliminar esta sinatura?<br> Non se pode desfacer.", "PE.Views.SignatureSettings.txtSigned": "As sinaturas válidas engadíronse á presentación. A presentación está protexida e non se pode editar.", "PE.Views.SignatureSettings.txtSignedInvalid": "Algunhas das sinaturas dixitais na presentación son inválidas ou non se puideron verificar. A presentación está protexida e non se pode editar.", "PE.Views.SlideSettings.strBackground": "<PERSON><PERSON> <PERSON> fondo", "PE.Views.SlideSettings.strColor": "Cor", "PE.Views.SlideSettings.strDateTime": "Amosar data e hora", "PE.Views.SlideSettings.strFill": "Fondo", "PE.Views.SlideSettings.strForeground": "Cor de primeiro plano", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strSlideNum": "Amosar número da diapositiva", "PE.Views.SlideSettings.strTransparency": "Opacidade", "PE.Views.SlideSettings.textAdvanced": "Amosar configuración avanzado", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Cor para encher", "PE.Views.SlideSettings.textDirection": "Dirección ", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON>", "PE.Views.SlideSettings.textFromFile": "Do fi<PERSON>iro", "PE.Views.SlideSettings.textFromStorage": "Desde almacenamento", "PE.Views.SlideSettings.textFromUrl": "De URL", "PE.Views.SlideSettings.textGradient": "Puntos de degradado ", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textImageTexture": "Imaxe ou textura", "PE.Views.SlideSettings.textLinear": "Lineal", "PE.Views.SlideSettings.textNoFill": "Sen encher", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPosition": "Posición", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Anular cambios", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> imaxe", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "De textura", "PE.Views.SlideSettings.textTile": "Mosaico", "PE.Views.SlideSettings.tipAddGradientPoint": "Engadir punto de degradado", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "Cartón", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "PE.Views.SlideSettings.txtGrain": "Grano", "PE.Views.SlideSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "Papel gris", "PE.Views.SlideSettings.txtKnit": "Texido", "PE.Views.SlideSettings.txtLeather": "Coiro", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "Madeira", "PE.Views.SlideshowSettings.textLoop": "Repetir o ciclo ata premer 'Esc'", "PE.Views.SlideshowSettings.textTitle": "Amosar a configuración", "PE.Views.SlideSizeSettings.strLandscape": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.strPortrait": "Retrato ", "PE.Views.SlideSizeSettings.textHeight": "Altura", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientación da diapositiva", "PE.Views.SlideSizeSettings.textSlideSize": "Tam<PERSON><PERSON> da diapositiva", "PE.Views.SlideSizeSettings.textTitle": "Configuración do tamaño da diapositiva", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Diapositivas de 35 mm", "PE.Views.SlideSizeSettings.txtA3": "Papel A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Folla A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Folla B4 (ICO) (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "Folla B5 (ICO) (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtCustom": "Personalizar", "PE.Views.SlideSizeSettings.txtLedger": "Papel de Contabilidade (11x17 en)", "PE.Views.SlideSizeSettings.txtLetter": "<PERSON><PERSON> (8.5x11 en)", "PE.Views.SlideSizeSettings.txtOverhead": "Transparencia", "PE.Views.SlideSizeSettings.txtStandard": "<PERSON><PERSON><PERSON><PERSON> (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Pantalla panorámica", "PE.Views.Statusbar.goToPageText": "Ir a diapositiva", "PE.Views.Statusbar.pageIndexText": "Diapositiva {0} de {1}", "PE.Views.Statusbar.textShowBegin": "Amosar presentación desde o principio", "PE.Views.Statusbar.textShowCurrent": "<PERSON><PERSON> desde a diapositiva actual", "PE.Views.Statusbar.textShowPresenterView": "Observar vista do presentador", "PE.Views.Statusbar.tipAccessRights": "Xestionar dereitos de acceso ao documento", "PE.Views.Statusbar.tipFitPage": "Axustar á diapositiva", "PE.Views.Statusbar.tipFitWidth": "Axustar <PERSON> anchura", "PE.Views.Statusbar.tipPreview": "Iniciar <PERSON>", "PE.Views.Statusbar.tipSetLang": "Establecer idioma do texto", "PE.Views.Statusbar.tipZoomFactor": "Ampliar", "PE.Views.Statusbar.tipZoomIn": "Achegar", "PE.Views.Statusbar.tipZoomOut": "Alonxar", "PE.Views.Statusbar.txtPageNumInvalid": "Número da diapositiva inválida", "PE.Views.TableSettings.deleteColumnText": "Eliminar columna", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> fila", "PE.Views.TableSettings.deleteTableText": "Eliminar táboa", "PE.Views.TableSettings.insertColumnLeftText": "Inserir columna á esquerda", "PE.Views.TableSettings.insertColumnRightText": "Inserir columna àá dereita", "PE.Views.TableSettings.insertRowAboveText": "Inserir fila enriba", "PE.Views.TableSettings.insertRowBelowText": "Inserir fila abaixo", "PE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON><PERSON><PERSON> celda", "PE.Views.TableSettings.selectColumnText": "Seleccionar columna", "PE.Views.TableSettings.selectRowText": "Seleccionar fila", "PE.Views.TableSettings.selectTableText": "Seleccionar táboa", "PE.Views.TableSettings.splitCellsText": "Di<PERSON><PERSON> celda...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON> celda", "PE.Views.TableSettings.textAdvanced": "Amosar configuración avanzado", "PE.Views.TableSettings.textBackColor": "<PERSON><PERSON> <PERSON> fondo", "PE.Views.TableSettings.textBanded": "Con bandas", "PE.Views.TableSettings.textBorderColor": "Cor", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON> bord<PERSON>", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON> da celda", "PE.Views.TableSettings.textColumns": "Columnas", "PE.Views.TableSettings.textDistributeCols": "Distribuír columnas", "PE.Views.TableSettings.textDistributeRows": "Distribu<PERSON><PERSON>", "PE.Views.TableSettings.textEdit": "Filas e columnas", "PE.Views.TableSettings.textEmptyTemplate": "Sen modelos", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeight": "Altura", "PE.Views.TableSettings.textLast": "Último", "PE.Views.TableSettings.textRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Seleccione bordos que desea cambiar aplicando estilo seleccionado", "PE.Views.TableSettings.textTemplate": "Seleccionar a partir do modelo", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Fixar bordo exterior e todas liñas interiores", "PE.Views.TableSettings.tipBottom": "Fixar só bordo exterior inferior", "PE.Views.TableSettings.tipInner": "Fixar só liñas interiores", "PE.Views.TableSettings.tipInnerHor": "Fixar só liñas horizontais interiores", "PE.Views.TableSettings.tipInnerVert": "Fixar só liñas verticais interiores", "PE.Views.TableSettings.tipLeft": "Fixar só bordo exterior esquerdo", "PE.Views.TableSettings.tipNone": "Non fixar bordos", "PE.Views.TableSettings.tipOuter": "<PERSON><PERSON><PERSON> só bordo exterior", "PE.Views.TableSettings.tipRight": "<PERSON><PERSON>r só bordo exterior dereito", "PE.Views.TableSettings.tipTop": "Fixar só bordo exterior superior", "PE.Views.TableSettings.txtNoBorders": "<PERSON> b<PERSON>", "PE.Views.TableSettings.txtTable_Accent": "Acentuación", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON><PERSON> claro", "PE.Views.TableSettings.txtTable_MediumStyle": "Estilo medio", "PE.Views.TableSettings.txtTable_NoGrid": "<PERSON>", "PE.Views.TableSettings.txtTable_NoStyle": "<PERSON> estilo", "PE.Views.TableSettings.txtTable_TableGrid": "Cuadrícula da táboa", "PE.Views.TableSettings.txtTable_ThemedStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.TableSettingsAdvanced.textAltDescription": "Descrición", "PE.Views.TableSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "Inferior", "PE.Views.TableSettingsAdvanced.textCenter": "Centro", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Usar marxes predeterminadas", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON> predeterminados", "PE.Views.TableSettingsAdvanced.textFrom": "De", "PE.Views.TableSettingsAdvanced.textHeight": "Altura", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Proporcións constantes", "PE.Views.TableSettingsAdvanced.textLeft": "E<PERSON>rda", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON>es de celdas", "PE.Views.TableSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.TableSettingsAdvanced.textPosition": "Posición", "PE.Views.TableSettingsAdvanced.textRight": "Dereita", "PE.Views.TableSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTitle": "Táboa - Configuración avanzada", "PE.Views.TableSettingsAdvanced.textTop": "Parte superior", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON>ulo <PERSON> superior", "PE.Views.TableSettingsAdvanced.textVertical": "Vertical", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "<PERSON><PERSON> <PERSON> fondo", "PE.Views.TextArtSettings.strColor": "Cor", "PE.Views.TextArtSettings.strFill": "Encher", "PE.Views.TextArtSettings.strForeground": "Cor de primeiro plano", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strStroke": "Liña", "PE.Views.TextArtSettings.strTransparency": "Opacidade", "PE.Views.TextArtSettings.strType": "Tipo", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "O valor introducido é incorrecto. <br> Insira un valor numérico entre 0 e 1584 puntos.", "PE.Views.TextArtSettings.textColor": "Cor para encher", "PE.Views.TextArtSettings.textDirection": "Dirección ", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON>", "PE.Views.TextArtSettings.textFromFile": "Do fi<PERSON>iro", "PE.Views.TextArtSettings.textFromUrl": "De URL", "PE.Views.TextArtSettings.textGradient": "Puntos de degradado ", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textImageTexture": "Imaxe ou textura", "PE.Views.TextArtSettings.textLinear": "Lineal", "PE.Views.TextArtSettings.textNoFill": "Sen encher", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPosition": "Posición", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "Cadro do persoal", "PE.Views.TextArtSettings.textTexture": "De textura", "PE.Views.TextArtSettings.textTile": "Mosaico", "PE.Views.TextArtSettings.textTransform": "Transformar", "PE.Views.TextArtSettings.tipAddGradientPoint": "Engadir punto de degradado", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "Cartón", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "PE.Views.TextArtSettings.txtGrain": "Grano", "PE.Views.TextArtSettings.txtGranite": "Granite", "PE.Views.TextArtSettings.txtGreyPaper": "Papel gris", "PE.Views.TextArtSettings.txtKnit": "Texido", "PE.Views.TextArtSettings.txtLeather": "Coiro", "PE.Views.TextArtSettings.txtNoBorders": "Sen liña", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "Madeira", "PE.Views.Toolbar.capAddSlide": "Engadir diapositiva", "PE.Views.Toolbar.capBtnAddComment": "Engadir comentario", "PE.Views.Toolbar.capBtnComment": "Comentario", "PE.Views.Toolbar.capBtnDateTime": "Data e hora", "PE.Views.Toolbar.capBtnInsHeader": "Rodapé", "PE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "PE.Views.Toolbar.capBtnSlideNum": "Número da diapositiva", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Gráfico", "PE.Views.Toolbar.capInsertEquation": "Ecuación", "PE.Views.Toolbar.capInsertHyperlink": "Hiperligazón", "PE.Views.Toolbar.capInsertImage": "Imaxe", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "Táboa", "PE.Views.Toolbar.capInsertText": "Caixa de texto", "PE.Views.Toolbar.capInsertVideo": "Vídeo", "PE.Views.Toolbar.capTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capTabHome": "<PERSON><PERSON>o", "PE.Views.Toolbar.capTabInsert": "Inserir", "PE.Views.Toolbar.mniCapitalizeWords": "Poñer en maiúsculas cada palabra", "PE.Views.Toolbar.mniCustomTable": "Inserir táboa personalizada", "PE.Views.Toolbar.mniImageFromFile": "Imaxe do ficheiro", "PE.Views.Toolbar.mniImageFromStorage": "Imaxe do Almacenamento", "PE.Views.Toolbar.mniImageFromUrl": "Imaxe da URL", "PE.Views.Toolbar.mniInsertSSE": "Inserir folla de cálculo", "PE.Views.Toolbar.mniLowerCase": "minúscula", "PE.Views.Toolbar.mniSentenceCase": "Tipo oración.", "PE.Views.Toolbar.mniSlideAdvanced": "Configuracións avanzadas", "PE.Views.Toolbar.mniSlideStandard": "<PERSON><PERSON><PERSON><PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "tIPO iNVERSO", "PE.Views.Toolbar.mniUpperCase": "MAIÚSCULA", "PE.Views.Toolbar.strMenuNoFill": "Sen encher", "PE.Views.Toolbar.textAlignBottom": "Aliñar texto á parte inferior", "PE.Views.Toolbar.textAlignCenter": "Centrar texto", "PE.Views.Toolbar.textAlignJust": "Xustificar", "PE.Views.Toolbar.textAlignLeft": "Aliñar texto á esquerda", "PE.Views.Toolbar.textAlignMiddle": "Aliñar texto ao medio", "PE.Views.Toolbar.textAlignRight": "Aliñar texto á dereita", "PE.Views.Toolbar.textAlignTop": "Aliñar texto á parte superior", "PE.Views.Toolbar.textArrangeBack": "Enviar ao fondo", "PE.Views.Toolbar.textArrangeBackward": "Enviar atrás", "PE.Views.Toolbar.textArrangeForward": "<PERSON><PERSON><PERSON> adiante", "PE.Views.Toolbar.textArrangeFront": "Traer á fronte", "PE.Views.Toolbar.textBold": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsCustom": "Personalizar columnas", "PE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON> columna", "PE.Views.Toolbar.textColumnsThree": "Tres columnas", "PE.Views.Toolbar.textColumnsTwo": "Dúas columnas", "PE.Views.Toolbar.textItalic": "Cursiva", "PE.Views.Toolbar.textListSettings": "COnfiguración da lista", "PE.Views.Toolbar.textRecentlyUsed": "Usados recentemente", "PE.Views.Toolbar.textShapeAlignBottom": "Aliñar á parte inferior", "PE.Views.Toolbar.textShapeAlignCenter": "Aliñar no centro", "PE.Views.Toolbar.textShapeAlignLeft": "Aliñar á esquerda", "PE.Views.Toolbar.textShapeAlignMiddle": "Aliñar no medio", "PE.Views.Toolbar.textShapeAlignRight": "Aliñar á <PERSON>eita", "PE.Views.Toolbar.textShapeAlignTop": "Aliñar á parte superior", "PE.Views.Toolbar.textShowBegin": "Amosar presentación desde o principio", "PE.Views.Toolbar.textShowCurrent": "<PERSON><PERSON> desde a diapositiva actual", "PE.Views.Toolbar.textShowPresenterView": "Observar vista do presentador", "PE.Views.Toolbar.textShowSettings": "Amosar a configuración", "PE.Views.Toolbar.textStrikeout": "Riscado", "PE.Views.Toolbar.textSubscript": "Subscrito", "PE.Views.Toolbar.textSuperscript": "Sobrescrito", "PE.Views.Toolbar.textTabAnimation": "Animación", "PE.Views.Toolbar.textTabCollaboration": "Colaboración", "PE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabHome": "<PERSON><PERSON>o", "PE.Views.Toolbar.textTabInsert": "Inserir", "PE.Views.Toolbar.textTabProtect": "Protección", "PE.Views.Toolbar.textTabTransitions": "Transicións", "PE.Views.Toolbar.textTabView": "Vista", "PE.Views.Toolbar.textTitleError": "Erro", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipAddSlide": "Engadir dispositiva", "PE.Views.Toolbar.tipBack": "Volver", "PE.Views.Toolbar.tipChangeCase": "Cambiar maiúsculas e minúsculas", "PE.Views.Toolbar.tipChangeChart": "Cambiar tipo de gráfico", "PE.Views.Toolbar.tipChangeSlide": "Cambiar deseño da diapositiva", "PE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON> estilo", "PE.Views.Toolbar.tipColorSchemas": "Cambiar combinación de cores", "PE.Views.Toolbar.tipColumns": "Inserir columnas", "PE.Views.Toolbar.tipCopy": "Copiar", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "PE.Views.Toolbar.tipDateTime": "Insira a data e hora actuais", "PE.Views.Toolbar.tipDecFont": "Diminuír o tamaño da fonte", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipEditHeader": "<PERSON><PERSON>", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> da fonte", "PE.Views.Toolbar.tipFontName": "Fonte", "PE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>e", "PE.Views.Toolbar.tipHAligh": "Aliñación horizontal", "PE.Views.Toolbar.tipHighlightColor": "Cor do realce", "PE.Views.Toolbar.tipIncFont": "Aumentar ta<PERSON> da fonte", "PE.Views.Toolbar.tipIncPrLeft": "Aumentar <PERSON>", "PE.Views.Toolbar.tipInsertAudio": "Inserir audio", "PE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "PE.Views.Toolbar.tipInsertEquation": "Inserir ecuación", "PE.Views.Toolbar.tipInsertHyperlink": "<PERSON>ga<PERSON>", "PE.Views.Toolbar.tipInsertImage": "Inserir imaxe", "PE.Views.Toolbar.tipInsertShape": "Inserir forma automática", "PE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "PE.Views.Toolbar.tipInsertTable": "Inserir táboa", "PE.Views.Toolbar.tipInsertText": "Inserir caixa do texto", "PE.Views.Toolbar.tipInsertTextArt": "Inserir arte do texto", "PE.Views.Toolbar.tipInsertVideo": "Inserir vídeo", "PE.Views.Toolbar.tipLineSpace": "Espazo entre liñas", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersArrow": "Viñetas de flecha", "PE.Views.Toolbar.tipMarkersCheckmark": "Viñetas de marca de verificación", "PE.Views.Toolbar.tipMarkersDash": "Viñetas g<PERSON>", "PE.Views.Toolbar.tipMarkersFRhombus": "Rombos recheos", "PE.Views.Toolbar.tipMarkersFRound": "Viñetas redondas recheas", "PE.Views.Toolbar.tipMarkersFSquare": "Viñetas cadradas recheas", "PE.Views.Toolbar.tipMarkersHRound": "Viñetas redondas ocas", "PE.Views.Toolbar.tipMarkersStar": "Viñetas de estrela", "PE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "Numeración", "PE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPreview": "Iniciar <PERSON>", "PE.Views.Toolbar.tipPrint": "Imprimir", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "Gardar", "PE.Views.Toolbar.tipSaveCoauth": "Garde os cambios para que outros usuarios os poidan ver.", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON>a", "PE.Views.Toolbar.tipShapeArrange": "Arranxar forma", "PE.Views.Toolbar.tipSlideNum": "Insira o número da diapositiva", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON> da diapositiva", "PE.Views.Toolbar.tipSlideTheme": "Te<PERSON> da diapositiva", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Aliñamento vertical", "PE.Views.Toolbar.tipViewSettings": "Amosar configuración", "PE.Views.Toolbar.txtDistribHor": "Distribuír horizontalmente", "PE.Views.Toolbar.txtDistribVert": "Distribuír verticalmente", "PE.Views.Toolbar.txtDuplicateSlide": "Duplicar diapositiva", "PE.Views.Toolbar.txtGroup": "Grupo", "PE.Views.Toolbar.txtObjectsAlign": "Aliñar obxectos seleccionados", "PE.Views.Toolbar.txtScheme1": "Oficina", "PE.Views.Toolbar.txtScheme10": "Mediana", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "Opulento", "PE.Views.Toolbar.txtScheme14": "Mirador", "PE.Views.Toolbar.txtScheme15": "Or<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme16": "Papel", "PE.Views.Toolbar.txtScheme17": "Solsticio", "PE.Views.Toolbar.txtScheme18": "Técnico", "PE.Views.Toolbar.txtScheme19": "Viaxes", "PE.Views.Toolbar.txtScheme2": "Escala de grises", "PE.Views.Toolbar.txtScheme20": "Urbano", "PE.Views.Toolbar.txtScheme21": "Inspiración", "PE.Views.Toolbar.txtScheme22": "Nova oficina", "PE.Views.Toolbar.txtScheme3": "Vértice", "PE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "PE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme6": "Concorrencia", "PE.Views.Toolbar.txtScheme7": "Equidade", "PE.Views.Toolbar.txtScheme8": "Fluxo", "PE.Views.Toolbar.txtScheme9": "Fundición", "PE.Views.Toolbar.txtSlideAlign": "Aliñar á diapositiva", "PE.Views.Toolbar.txtUngroup": "Desagrupar", "PE.Views.Transitions.strDelay": "Atraso", "PE.Views.Transitions.strDuration": "Duración", "PE.Views.Transitions.strStartOnClick": "Iniciar ao premer", "PE.Views.Transitions.textBlack": "En negro", "PE.Views.Transitions.textBottom": "Inferior", "PE.Views.Transitions.textBottomLeft": "Abaixo á esquerda", "PE.Views.Transitions.textBottomRight": "Abaixo á dereita", "PE.Views.Transitions.textClock": "Reloxo", "PE.Views.Transitions.textClockwise": "No sentido das agullas do reloxo", "PE.Views.Transitions.textCounterclockwise": "No sentido contrario ás agullas do reloxo", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "Horizontal entrante", "PE.Views.Transitions.textHorizontalOut": "Horizontal saínte", "PE.Views.Transitions.textLeft": "E<PERSON>rda", "PE.Views.Transitions.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textPush": "Empurrar", "PE.Views.Transitions.textRight": "<PERSON> dereita", "PE.Views.Transitions.textSmoothly": "Suavemente", "PE.Views.Transitions.textSplit": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textTop": "Parte superior", "PE.Views.Transitions.textTopLeft": "Parte superior esquerda", "PE.Views.Transitions.textTopRight": "Parte superior dereita", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "Vertical entrante", "PE.Views.Transitions.textVerticalOut": "Vertical saínte", "PE.Views.Transitions.textWedge": "Triangular", "PE.Views.Transitions.textWipe": "Eliminar", "PE.Views.Transitions.textZoom": "Ampliar", "PE.Views.Transitions.textZoomIn": "Achegar", "PE.Views.Transitions.textZoomOut": "Alonxar", "PE.Views.Transitions.textZoomRotate": "Ampliar e rotación", "PE.Views.Transitions.txtApplyToAll": "Aplicar a todas as diapositivas", "PE.Views.Transitions.txtParameters": "Parámetros", "PE.Views.Transitions.txtPreview": "Vista previa", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textAlwaysShowToolbar": "Amosar sempre a barra de ferramentas", "PE.Views.ViewTab.textFitToSlide": "Axustar á diapositiva", "PE.Views.ViewTab.textFitToWidth": "Axustar <PERSON> anchura", "PE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON>", "PE.Views.ViewTab.textNotes": "Notas", "PE.Views.ViewTab.textRulers": "Regras", "PE.Views.ViewTab.textStatusBar": "Barra de estado", "PE.Views.ViewTab.textZoom": "Ampliar"}