<!DOCTYPE html>
<html>
	<head>
		<title>Вставка и настройка изображений</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в презентацию изображение и настройте его размер и положение." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка и настройка изображений</h1>
            <h3>Вставка изображения</h3>
			<p>В онлайн-редакторе презентаций можно вставлять в презентацию изображения самых популярных форматов. Поддерживаются следующие форматы изображений: <b>BMP</b>, <b>GIF</b>, <b>JPEG</b>, <b>JPG</b>, <b>PNG</b>.</p>
			<p>Для <b>добавления</b> изображения на слайд:</p>
			<ol>
				<li>в списке слайдов слева выберите тот слайд, на который требуется добавить изображение,</li>
				<li>щелкните по значку <div class = "icon icon-image"></div> <b>Изображение</b> на вкладке <b>Главная</b> или <b>Вставка</b> верхней панели инструментов,</li>
				<li>для загрузки изображения выберите одну из следующих опций:
					<ul>
						<li>при выборе опции <b>Изображение из файла</b> откроется стандартное диалоговое окно для выбора файлов. Выберите нужный файл на жестком диске компьютера и нажмите кнопку <b>Открыть</b>
                           <p class="note">В <em>онлайн-редакторе</em> вы можете выбрать сразу несколько изображений.</p>
                        </li>
						<li>при выборе опции <b>Изображение по URL</b> откроется окно, в котором можно ввести веб-адрес нужного изображения, а затем нажать кнопку <b>OK</b></li>
                        <li class="onlineDocumentFeatures">при выборе опции  <b>Изображение из хранилища</b> откроется окно <b>Выбрать источник данных</b>. Выберите изображение, сохраненное на вашем портале, и нажмите кнопку <b>OK</b></li>
					</ul>
				</li>
				<li>после того как изображение будет добавлено, можно изменить его <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">размер и положение</a>.</li>
			</ol>
            <p>Вы также можете добавить изображение внутри текстовой рамки, нажав на кнопку <span class="icon icon-placeholder_imagefromfile"></span> <b>Изображение из файла</b> в ней и выбрав нужное изображение, сохраненное на компьютере, или используйте  кнопку <span class="icon icon-placeholder_imagefromurl"></span> <b>Изображение по URL</b> и укажите URL-адрес изображения:</p>
            <p><img alt="Добавление диаграммы в текстовую рамку" src="../images/placeholder_object.png" /></p>
            <p>Также можно добавить изображение в макет слайда. Для получения дополнительной информации вы можете обратиться к этой <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">статье</a>.</p>
			<hr />
            <h3>Изменение параметров изображения</h3>
			<p>Правая боковая панель активируется при щелчке по изображению левой кнопкой мыши и выборе значка <b>Параметры изображения</b> <span class="icon icon-image_settings_icon"></span> справа. Вкладка содержит следующие разделы:</p>
            <img alt="Вкладка Параметры изображения" src="../images/imagesettingstab.png" />
            <p><b>Размер</b> - используется, чтобы просмотреть текущую <b>Ширину</b> и <b>Высоту</b> изображения или при необходимости восстановить <b>Реальный размер</b> изображения.</p>
            <p>Кнопка <b>Обрезать</b> используется, чтобы обрезать изображение. Нажмите кнопку <b>Обрезать</b>, чтобы активировать маркеры обрезки, которые появятся в углах изображения и в центре каждой его стороны. Вручную перетаскивайте маркеры, чтобы задать область обрезки. Вы можете навести курсор мыши на границу области обрезки, чтобы курсор превратился в значок <span class="icon icon-arrow"></span>, и перетащить область обрезки. </p>
            <ul>
                <li>Чтобы обрезать одну сторону, перетащите маркер, расположенный в центре этой стороны.</li>
                <li>Чтобы одновременно обрезать две смежных стороны, перетащите один из угловых маркеров.</li>
                <li>Чтобы равномерно обрезать две противоположные стороны изображения, удерживайте нажатой клавишу <em>Ctrl</em> при перетаскивании маркера в центре одной из этих сторон. </li>
                <li>Чтобы равномерно обрезать все стороны изображения, удерживайте нажатой клавишу <em>Ctrl</em> при перетаскивании любого углового маркера.</li>
            </ul>
            <p>Когда область обрезки будет задана, еще раз нажмите на кнопку <b>Обрезать</b>, или нажмите на клавишу <em>Esc</em>, или щелкните мышью за пределами области обрезки, чтобы применить изменения.</p>
            <p>После того, как область обрезки будет задана, также можно использовать опции <b>Обрезать</b>, <b>Заливка</b> и <b>Вписать</b>, доступные в выпадающем меню <b>Обрезать</b>. Нажмите кнопку <b>Обрезать</b> еще раз и выберите нужную опцию: </p>
            <ul>
                <li>При выборе опции <b>Обрезать</b> изображение будет заполнять определенную форму. Вы можете выбрать фигуру из галереи, которая открывается при наведении указателя мыши на опцию <b>Обрезать по фигуре</b>. Вы по-прежнему можете использовать опции <b>Заливка</b> и <b>Вписать</b>, чтобы настроить, как изображение будет соответствовать фигуре.</li>
                <li>При выборе опции <b>Заливка</b> центральная часть исходного изображения будет сохранена и использована в качестве заливки выбранной области обрезки, в то время как остальные части изображения будут удалены.</li>
                <li>При выборе опции <b>Вписать</b> размер изображения будет изменен, чтобы оно соответствовало высоте или ширине области обрезки. Никакие части исходного изображения не будут удалены, но внутри выбранной области обрезки могут появится пустые пространства.</li>
            </ul>
            <p><b>Заменить изображение</b> - используется, чтобы загрузить другое изображение вместо текущего, выбрав нужный источник. Можно выбрать одну из опций: <b>Из файла</b>, <b>Из хранилища</b> или <b>По URL</b>. Опция <b>Заменить изображение</b> также доступна в контекстном меню, вызываемом правой кнопкой мыши.</p>
            <p><b>Поворот</b> - используется, чтобы повернуть изображение на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить изображение слева направо или сверху вниз. Нажмите на одну из кнопок:</p>
            <ul>
                <li><div class = "icon icon-rotatecounterclockwise"></div> чтобы повернуть изображение на 90 градусов против часовой стрелки</li>
                <li><div class = "icon icon-rotateclockwise"></div> чтобы повернуть изображение на 90 градусов по часовой стрелке</li>
                <li><div class = "icon icon-fliplefttoright"></div> чтобы отразить изображение по горизонтали (слева направо)</li>
                <li><div class = "icon icon-flipupsidedown"></div> чтобы отразить изображение по вертикали (сверху вниз)</li>
            </ul>
            <p>Когда изображение выделено, справа также доступен значок <b>Параметры фигуры</b> <span class="icon icon-shape_settings_icon"></span>. Можно щелкнуть по нему, чтобы открыть вкладку <b>Параметры фигуры</b> на правой боковой панели и настроить тип, толщину и цвет <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)"><b>Контуров</b></a> фигуры, а также изменить тип фигуры, выбрав другую фигуру в меню <b>Изменить автофигуру</b>. Форма изображения изменится соответствующим образом.</p>
            <p>На вкладке <b>Параметры фигуры</b> также можно использовать опцию <b>Отображать тень</b>, чтобы добавить тень к изображеню.</p>
            <p><img alt="Вкладка Параметры фигуры" src="../images/right_image_shape.png" /></p>
            <hr />
			<p>Чтобы изменить <b>дополнительные параметры</b> изображения, щелкните по нему правой кнопкой мыши и выберите из контекстного меню опцию <b>Дополнительные параметры изображения</b> или щелкните по изображению левой кнопкой мыши и нажмите на ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно свойств изображения:</p>
            <p><img alt="Свойства изображения" src="../images/image_properties.png" /></p>
			<p>Вкладка <b>Положение</b> позволяет задать следующие свойства изображения:</p>
			<ul>
				<li><b>Размер</b> - используйте эту опцию, чтобы изменить ширину и/или высоту изображения. Если нажата кнопка <b>Сохранять пропорции</b> <div class = "icon icon-constantproportions"></div> (в этом случае она выглядит так: <div class = "icon icon-constantproportionsactivated"></div>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон изображения. Чтобы восстановить реальный размер добавленного изображения, нажмите кнопку <b>Реальный размер</b>.</li>
				<li><b>Положение</b> - задайте точную позицию, используя поля <b>По горизонтали</b> и <b>По вертикали</b>, а также поле <b>От</b>, где доступны параметры <b>Верхний левый угол</b> и <b>По центру</b>.</li>
			</ul>
            <p><img alt="Свойства изображения: Поворот" src="../images/image_properties2.png" /></p>
            <p>Вкладка <b>Поворот</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Угол</b> - используйте эту опцию, чтобы повернуть изображение на точно заданный угол. Введите в поле нужное значение в градусах или скорректируйте его, используя стрелки справа. </li>
                <li><b>Отражено</b> - отметьте галочкой опцию <b>По горизонтали</b>, чтобы отразить изображение по горизонтали (слева направо), или отметьте галочкой опцию <b>По вертикали</b>, чтобы отразить изображение по вертикали (сверху вниз).</li>
            </ul>
            <p><img alt="Свойства изображения" src="../images/image_properties1.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение.</p>
			<hr />
			<p>Чтобы <b>удалить</b> вставленное изображение, щелкните по нему левой кнопкой мыши и нажмите клавишу <b>Delete</b> на клавиатуре.</p>
			<p>Чтобы узнать, как <b>выровнять</b> изображение на слайде или <b>расположить в определенном порядке</b> несколько изображений, обратитесь к разделу <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">Выравнивание и упорядочивание объектов на слайде</a>.</p>
		</div>
	</body>
</html>