﻿<!DOCTYPE html>
<html>
	<head>
		<title>NV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>NV-Funktion</h1>
			<p>Die <b>NV</b>-Funktion ist eine der Informationsfunktionen. Sie gibt den Fehlerwert #NV zurück Die Syntax der Funktion NV erfordert <b>keine</b> Argumente.</p>
			<p>Syntax der <b>NV-</b> Funktion:</p> 
			<p style="text-indent: 150px;"><b><em>NV()</em></b></p> 
			<p>Anwendung der <b>NV-Funktion</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die <b>NV-Funktion</b>.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="NV-Funktion" src="../images/na.png" /></p>
		</div>
	</body>
</html>