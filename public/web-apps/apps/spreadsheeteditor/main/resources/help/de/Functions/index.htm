﻿<!DOCTYPE html>
<html>
	<head>
		<title>INDEX-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>INDEX-Funktion</h1>
			<p>Die Funktion <b>INDEX</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Sie gibt einen Wert oder den Bezug auf einen Wert aus einer Tabelle oder einem Bereich zurück. Die Funktion <b>INDEX</b> kann auf zwei Arten verwendet werden. <!--The array form is used to return a value within a single range of cells. The reference form is used to return a value within an array that contains several ranges of cells.--></p>
			<p>In der Matrixversion ist die Formelsyntax der Funktion <b>INDEX</b>:</p> 
			<p style="text-indent: 150px;"><b><em>INDEX(Matrix;Zeile;[Spalte])</em></b></p> 
            <p>In der Bezugsversion ist die Formelsyntax der Funktion <b>INDEX</b>:</p>
            <p style="text-indent: 150px;"><b><em>INDEX(Bezug;Zeile;[Spalte];[Bereich])</em></b></p>
            <p><em>Dabei gilt:</em></p> 
            <p style="text-indent: 50px;"><em><b>Matrix</b></em> ist ein Zellbereich oder eine Matrixkonstante.</p>
            <p style="text-indent: 50px;"><em><b>Bezug</b></em> ist der Bezug auf einen oder mehrere Zellbereiche.</p>
            <p style="text-indent: 50px;"><em><b>Zeile</b></em> markiert die Zeile in der Matrix, aus der ein Wert zurückgegeben werden soll. Wird kein Wert angegeben, muss <em><b>Spalte</b></em> angegeben werden.</p>
			<p style="text-indent: 50px;"><em><b>Spalte</b></em> markiert die Spalte, aus der ein Wert zurückgegeben werden soll. Wird kein Wert angegeben, muss <em><b>Zeile</b></em> angegeben werden.</p>
            <p style="text-indent: 50px;"><em><b>Bereich</b></em> ist der zu verwendende Bereich für den Fall, dass das die Matrix mehrere Bereiche enthält. Dieses Argument ist optional. Wird es ausgelassen, verwendet die Funktion <b><em>Bereich</em></b> 1.</p>
			<p>Die Argumente können manuell eingegeben werden oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>INDEX</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>INDEX</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="INDEX-Funktion" src="../images/index_1.png" /></p>
		</div>
	</body>
</html>