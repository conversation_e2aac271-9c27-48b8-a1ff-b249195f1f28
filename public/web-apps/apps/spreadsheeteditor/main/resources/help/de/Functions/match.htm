﻿<!DOCTYPE html>
<html>
	<head>
		<title>VERGLEICH-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>VERGLEICH-Funktion</h1>
			<p>Die Funktion <b>VERGLEICH</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Sie sucht in einem Bereich von Zellen nach einem angegebenen Element und gibt anschließend die relative Position dieses Elements im Bereich zurück.</p>
			<p>Die Formelsyntax der Funktion <b>VERGLEICH</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>VERGLEICH(Suchkriterium;Suchmatrix;[Vergleichstyp])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><em><b>Suchkriterium</b></em> ist der Wert mit dem Sie Elemente in der <em><b>Suchmatrix</b></em> abgleichen möchten. Hierbei kann es sich um einen Wert (Zahl, Text oder logischen Wert) oder einen Zellbezug auf eine Zahl, einen Text oder einen logischen Wert handeln.</p>
			<p style="text-indent: 50px;"><em><b>Suchmatrix</b></em> ist der Zellbereich, der durchsucht wird.</p>
			<p style="text-indent: 50px;"><em><b>Vergleichstyp</b></em> gibt an, auf welche Weise die Werte in der Suchmatrix mit dem Wert für Suchkriterium abgeglichen werden sollen. Dieses Argument ist optional. Die folgenden Typen stehen zur Auswahl:</p>
				<table style="width: 40%">
				<tr>
					<td><b>Vergleichstyp</b></td>
					<td><b>Verhalten</b></td>
				</tr>
				<tr>
					<td>1 oder nicht angegeben</td>
					<td>Die Werte müssen in aufsteigender Reihenfolge angeordnet sein. Liegt keine genaue Übereinstimmung vor, gibt die Funktion den größten Wert zurück, der kleiner ist als <em><b>Suchmatrix</b></em>.</td>
				</tr>
				<tr>
					<td>0</td>
					<td>Die Werte dürfen in beliebiger Reihenfolge angeordnet sein. Liegt keine genaue Übereinstimmung vor, gibt die Funktion den Fehlerwert #NV zurück.</td>
				</tr>
				<tr>
					<td>-1</td>
					<td>Die Werte müssen in absteigender Reihenfolge angeordnet sein. Liegt keine genaue Übereinstimmung vor, gibt die Funktion den kleinsten Wert zurück, der größer ist als <em><b>Suchmatrix</b></em>.</td>
				</tr>
			</table>
			<p>Anwendung der Funktion <b>VERGLEICH</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>VERGLEICH</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="VERGLEICH-Funktion" src="../images/match.png" /></p>
		</div>
	</body>
</html>