﻿<!DOCTYPE html>
<html>
	<head>
		<title>VAR.P-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>VAR.P-Funktion</h1>
			<p>Die Funktion <b>VAR.P</b> gehört zur Gruppe der statistischen Funktionen. Sie berechnet die Varianz auf der Grundlage der gesamten Population (logische Werte und Text werden ignoriert).</p>
			<p>Die Formelsyntax der Funktion <b>VAR.P</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>VAR.P(Zahl1;[Zahl2],...)</em></b></p> 
			<p><b><em>Zahl1/2/...</em></b> kann bis zu 254 Zahlenwerte umfassen, die manuell eingegeben werden oder in die Zelle eingeschlossen sind, auf die Sie Bezug nehmen.</p>
			<p class="note"><b>Hinweis:</b> Leere Zellen, Wahrheitswerte, Texte oder Fehlerwerte in Zellenbezügen oder Matrizen werden ignoriert. Wahrheitswerte und Zahlen in Textform, die Sie direkt in die Liste der Argumente eingeben, werden berücksichtigt.</p>
			<p>Anwendung der Funktion <b>VAR.P</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>VAR.P</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="VAR.P-Funktion" src="../images/var-p.png" /></p>
		</div>
	</body>
</html>