{"cancelButtonText": "Отказ", "Common.Controllers.Chat.notcriticalErrorTitle": "Внимание", "Common.Controllers.Chat.textEnterMessage": "Въведете съобщението си тук", "Common.define.chartData.textArea": "Площ", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCharts": "Диаграми", "Common.define.chartData.textColumn": "Колона", "Common.define.chartData.textColumnSpark": "Колона", "Common.define.chartData.textLine": "Линия", "Common.define.chartData.textLineSpark": "Линия", "Common.define.chartData.textPie": "Кръгова", "Common.define.chartData.textPoint": "XY (точкова)", "Common.define.chartData.textSparks": "Блещукащи", "Common.define.chartData.textStock": "Борсова", "Common.define.chartData.textSurface": "Повърхност", "Common.define.chartData.textWinLossSpark": "Печалба/Загуба", "Common.define.conditionalData.textError": "Грешка", "Common.define.conditionalData.textFormula": "Формула", "Common.Translation.textMoreButton": "Повече", "Common.UI.ButtonColored.textAutoColor": "Автоматичен", "Common.UI.ButtonColored.textNewColor": "Нов Потребителски Цвят", "Common.UI.ComboBorderSize.txtNoBorders": "Няма граници", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Няма граници", "Common.UI.ComboDataView.emptyComboText": "Няма стилове", "Common.UI.ExtendedColorDialog.addButtonText": "Добави", "Common.UI.ExtendedColorDialog.textCurrent": "Текущ", "Common.UI.ExtendedColorDialog.textHexErr": "Въведената стойност е неправилна. <br> Моля, въведете стойност между 000000 и FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Въведената стойност е неправилна. <br> Въведете числова стойност между 0 и 255.", "Common.UI.HSBColorPicker.textNoColor": "Няма цвят", "Common.UI.SearchDialog.textHighlight": "Маркирайте резултатите", "Common.UI.SearchDialog.textMatchCase": "Различаващ главни от малки букви", "Common.UI.SearchDialog.textReplaceDef": "Въведете заместващия текст", "Common.UI.SearchDialog.textSearchStart": "Въведете текста тук", "Common.UI.SearchDialog.textTitle": "Намерете и заменете", "Common.UI.SearchDialog.textTitle2": "Намирам", "Common.UI.SearchDialog.textWholeWords": "Само цели думи", "Common.UI.SearchDialog.txtBtnHideReplace": "Скриване на замяна", "Common.UI.SearchDialog.txtBtnReplace": "Заменете", "Common.UI.SearchDialog.txtBtnReplaceAll": "Замяна на всички", "Common.UI.SynchronizeTip.textDontShow": "Не показвайте това съобщение отново", "Common.UI.SynchronizeTip.textSynchronize": "Документът е променен от друг потребител. <br> <PERSON><PERSON><PERSON><PERSON>, кликнете върху, за да запазите промените си и да презаредите актуализациите.", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартни цветове", "Common.UI.ThemeColorPalette.textThemeColors": "Цветовете на темата", "Common.UI.Window.cancelButtonText": "Отказ", "Common.UI.Window.closeButtonText": "Затвори", "Common.UI.Window.noButtonText": "Не", "Common.UI.Window.okButtonText": "Добре", "Common.UI.Window.textConfirmation": "Потвърждаване", "Common.UI.Window.textDontShow": "Не показвайте това съобщение отново", "Common.UI.Window.textError": "Грешка", "Common.UI.Window.textInformation": "Информация", "Common.UI.Window.textWarning": "Внимание", "Common.UI.Window.yesButtonText": "Да", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "адрес:", "Common.Views.About.txtLicensee": "ЛИЦЕНЗОПОЛУЧАТЕЛЯТ", "Common.Views.About.txtLicensor": "НОСИТЕЛЯТ", "Common.Views.About.txtMail": "електронна поща:", "Common.Views.About.txtPoweredBy": "Задвижвани от", "Common.Views.About.txtTel": "тел.: ", "Common.Views.About.txtVersion": "Версия", "Common.Views.AutoCorrectDialog.textDelete": "Изтрий", "Common.Views.AutoCorrectDialog.textRestore": "Възстанови", "Common.Views.Chat.textSend": "Изпращам", "Common.Views.Comments.textAdd": "Добави", "Common.Views.Comments.textAddComment": "Добави коментар ", "Common.Views.Comments.textAddCommentToDoc": "Добави коментар към документа", "Common.Views.Comments.textAddReply": "Добави отговор", "Common.Views.Comments.textAnonym": "Гост", "Common.Views.Comments.textCancel": "Отказ", "Common.Views.Comments.textClose": "Затвори", "Common.Views.Comments.textComments": "Коментари", "Common.Views.Comments.textEdit": "Добре", "Common.Views.Comments.textEnterCommentHint": "Въведете коментара си тук", "Common.Views.Comments.textHintAddComment": "Добави коментар", "Common.Views.Comments.textOpenAgain": "Отвори отново", "Common.Views.Comments.textReply": "Отговор", "Common.Views.Comments.textResolve": "Решение", "Common.Views.Comments.textResolved": "<PERSON>е<PERSON><PERSON>н", "Common.Views.CopyWarningDialog.textDontShow": "Не показвайте това съобщение отново", "Common.Views.CopyWarningDialog.textMsg": "Действията за копиране, изрязване и поставяне с помощта на бутоните на лентата с инструменти на редактора и действията в контекстното меню ще се изпълняват само в този раздел на редактора. <br> <br> За да копирате или поставите в или от приложения извън раздела за редактори, използвайте следните комбинации от клавиатури:", "Common.Views.CopyWarningDialog.textTitle": "Действия за копиране, изрязване и поставяне", "Common.Views.CopyWarningDialog.textToCopy": "за копиране", "Common.Views.CopyWarningDialog.textToCut": "за изрязване", "Common.Views.CopyWarningDialog.textToPaste": "за поставяне", "Common.Views.DocumentAccessDialog.textLoading": "Зареждане ...", "Common.Views.DocumentAccessDialog.textTitle": "Настройки за споделяне", "Common.Views.Header.labelCoUsersDescr": "Понастоящем документът се редактира от няколко потребители.", "Common.Views.Header.textAdvSettings": "Разширени настройки", "Common.Views.Header.textBack": "Mестоположението на файла", "Common.Views.Header.textCompactView": "Скриване на лентата с инструменти", "Common.Views.Header.textHideLines": "Скриване на владетели", "Common.Views.Header.textHideStatusBar": "Скриване на лентата на състоянието", "Common.Views.Header.textSaveBegin": "Се запазва ...", "Common.Views.Header.textSaveChanged": "Променено", "Common.Views.Header.textSaveEnd": "Всички промени са запазени", "Common.Views.Header.textSaveExpander": "Всички промени са запазени", "Common.Views.Header.textZoom": "Мащ<PERSON>б", "Common.Views.Header.tipAccessRights": "Управление на правата за достъп до документи", "Common.Views.Header.tipDownload": "Свали файл", "Common.Views.Header.tipGoEdit": "Редактиране на текущия файл", "Common.Views.Header.tipPrint": "Печат на файла", "Common.Views.Header.tipRedo": "Повтори", "Common.Views.Header.tipSave": "Запази", "Common.Views.Header.tipUndo": "Отмени", "Common.Views.Header.tipViewSettings": "Преглед на настройките", "Common.Views.Header.tipViewUsers": "Преглеждайте потребителите и управлявайте правата за достъп до документи", "Common.Views.Header.txtAccessRights": "Промяна на правата за достъп", "Common.Views.Header.txtRename": "Преименувам", "Common.Views.History.textRestore": "Възстанови", "Common.Views.History.textShow": "Разширете", "Common.Views.ImageFromUrlDialog.textUrl": "Поставете URL адрес на изображение:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Това поле е задължително", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Това поле трябва да е URL адрес във формат \"http://www.example.com\"", "Common.Views.ListSettingsDialog.txtColor": "Цвят", "Common.Views.OpenDialog.closeButtonText": "Затвори файла", "Common.Views.OpenDialog.txtAdvanced": "Допълнително", "Common.Views.OpenDialog.txtColon": "Дебело черво", "Common.Views.OpenDialog.txtComma": "Запетая", "Common.Views.OpenDialog.txtDelimiter": "Разделител", "Common.Views.OpenDialog.txtEncoding": "Кодиране", "Common.Views.OpenDialog.txtIncorrectPwd": "Паролата е неправилна.", "Common.Views.OpenDialog.txtOpenFile": "Въведете парола, за да отворите файла", "Common.Views.OpenDialog.txtOther": "Друг", "Common.Views.OpenDialog.txtPassword": "Парола", "Common.Views.OpenDialog.txtPreview": "Предварителен преглед", "Common.Views.OpenDialog.txtProtected": "След като въведете паролата и отворите файла, текущата парола за файла ще бъде нулирана.", "Common.Views.OpenDialog.txtSemicolon": "Точка и запетая", "Common.Views.OpenDialog.txtSpace": "Пространство", "Common.Views.OpenDialog.txtTab": "Раздел", "Common.Views.OpenDialog.txtTitle": "Изберете опции %1", "Common.Views.OpenDialog.txtTitleProtected": "Защитен файл", "Common.Views.PasswordDialog.txtDescription": "Задайте парола, за да защитите този документ", "Common.Views.PasswordDialog.txtIncorrectPwd": "Паролата за потвърждение не е идентична", "Common.Views.PasswordDialog.txtPassword": "Парола", "Common.Views.PasswordDialog.txtRepeat": "Повтори паролата", "Common.Views.PasswordDialog.txtTitle": "Задайте парола", "Common.Views.PasswordDialog.txtWarning": "Внимание: Ако загубите или забравите паролата, тя не може да се възстанови. Го съхранявайте на сигурно място.", "Common.Views.PluginDlg.textLoading": "Зареждане", "Common.Views.Plugins.groupCaption": "Добавки", "Common.Views.Plugins.strPlugins": "Добавки", "Common.Views.Plugins.textLoading": "Зареждане", "Common.Views.Plugins.textStart": "Начало", "Common.Views.Plugins.textStop": "Спри се", "Common.Views.Protection.hintAddPwd": "Шифроване с парола", "Common.Views.Protection.hintPwd": "Промяна или изтриване на парола", "Common.Views.Protection.hintSignature": "Добавете цифров подпис или линия за подпис", "Common.Views.Protection.txtAddPwd": "Добавяне на парола", "Common.Views.Protection.txtChangePwd": "Промяна на паролата", "Common.Views.Protection.txtDeletePwd": "Изтриване на паролата", "Common.Views.Protection.txtEncrypt": "Шифроване", "Common.Views.Protection.txtInvisibleSignature": "Добавете електронен подпис", "Common.Views.Protection.txtSignature": "Под<PERSON>ис", "Common.Views.Protection.txtSignatureLine": "Добавете линия за подпис", "Common.Views.RenameDialog.textName": "Име на файл", "Common.Views.RenameDialog.txtInvalidName": "Името на файла не може да съдържа нито един от следните знаци: ", "Common.Views.ReviewChanges.hintNext": "За следващата промяна", "Common.Views.ReviewChanges.hintPrev": "Към предишна промяна", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Съвместно редактиране в реално време. Всички промени се запазват автоматично.", "Common.Views.ReviewChanges.strStrict": "Стриктен", "Common.Views.ReviewChanges.strStrictDesc": "Използвайте бутона „Запазване“, за да синхронизирате промените, които правите вие ​​и другите.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Приеми текущата промяна", "Common.Views.ReviewChanges.tipCoAuthMode": "Задайте режим на съвместно редактиране", "Common.Views.ReviewChanges.tipHistory": "Показване на историята на версиите", "Common.Views.ReviewChanges.tipRejectCurrent": "Отхвърляне на текущата промяна", "Common.Views.ReviewChanges.tipReview": "Проследнение на промените", "Common.Views.ReviewChanges.tipReviewView": "Изберете режима, в който искате да се показват промените", "Common.Views.ReviewChanges.tipSetDocLang": "Задайте език на документа", "Common.Views.ReviewChanges.tipSetSpelling": "Проверка на правописа", "Common.Views.ReviewChanges.tipSharing": "Управление на правата за достъп до документи", "Common.Views.ReviewChanges.txtAccept": "Приемам", "Common.Views.ReviewChanges.txtAcceptAll": "Приемете всички промени", "Common.Views.ReviewChanges.txtAcceptChanges": "Приемане на промените", "Common.Views.ReviewChanges.txtAcceptCurrent": "Приеми текущата промяна", "Common.Views.ReviewChanges.txtChat": "Чат", "Common.Views.ReviewChanges.txtClose": "Затвори", "Common.Views.ReviewChanges.txtCoAuthMode": "Режим на съвместно редактиране", "Common.Views.ReviewChanges.txtCommentRemove": "Премахване", "Common.Views.ReviewChanges.txtDocLang": "Език", "Common.Views.ReviewChanges.txtFinal": "Всички промени са приети (визуализация)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "История на версиите", "Common.Views.ReviewChanges.txtMarkup": "Всички промени (редактиране)", "Common.Views.ReviewChanges.txtMarkupCap": "Промени", "Common.Views.ReviewChanges.txtNext": "Следващия", "Common.Views.ReviewChanges.txtOriginal": "Всички отхвърлени промени (предварителен преглед)", "Common.Views.ReviewChanges.txtOriginalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtPrev": "Предишен", "Common.Views.ReviewChanges.txtReject": "Отхвърляне", "Common.Views.ReviewChanges.txtRejectAll": "Отхвърляне на всички промени", "Common.Views.ReviewChanges.txtRejectChanges": "Отхвърляне на промените", "Common.Views.ReviewChanges.txtRejectCurrent": "Отхвърляне на текущата промяна", "Common.Views.ReviewChanges.txtSharing": "Споделяне", "Common.Views.ReviewChanges.txtSpelling": "Проверка на правописа", "Common.Views.ReviewChanges.txtTurnon": "Проследяване на промените", "Common.Views.ReviewChanges.txtView": "Режим на дисплея", "Common.Views.ReviewPopover.textAdd": "Добави", "Common.Views.ReviewPopover.textAddReply": "Добави отговор", "Common.Views.ReviewPopover.textCancel": "Отказ", "Common.Views.ReviewPopover.textClose": "Затвори", "Common.Views.ReviewPopover.textEdit": "Добре", "Common.Views.ReviewPopover.textOpenAgain": "Отвори отново", "Common.Views.ReviewPopover.textReply": "Отговор", "Common.Views.ReviewPopover.textResolve": "Решение", "Common.Views.ReviewPopover.txtDeleteTip": "Изтрий", "Common.Views.SaveAsDlg.textLoading": "Зареждане", "Common.Views.SaveAsDlg.textTitle": "Папка за запис", "Common.Views.SearchPanel.textLookIn": "Погледни в", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textLoading": "Зареждане", "Common.Views.SelectFileDlg.textTitle": "Изберете източник на данни", "Common.Views.SignDialog.textBold": "Получер", "Common.Views.SignDialog.textCertificate": "Сертификат", "Common.Views.SignDialog.textChange": "Промяна", "Common.Views.SignDialog.textInputName": "Въведете името на подписващото лице", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textPurpose": "Цел за подписване на този документ", "Common.Views.SignDialog.textSelect": "Изберете", "Common.Views.SignDialog.textSelectImage": "Изберете изображение", "Common.Views.SignDialog.textSignature": "Подписът изглежда като", "Common.Views.SignDialog.textTitle": "Подпишете документ", "Common.Views.SignDialog.textUseImage": "или кликнете върху „Избор на изображение“, за да използвате снимка като подпис", "Common.Views.SignDialog.textValid": "Валидно от %1 до %2", "Common.Views.SignDialog.tipFontName": "Име на шрифта", "Common.Views.SignDialog.tipFontSize": "Размер на шрифта", "Common.Views.SignSettingsDialog.textAllowComment": "Позволете на сигналиста да добавите коментар в диалога за подпис", "Common.Views.SignSettingsDialog.textInfoEmail": "Електронна поща", "Common.Views.SignSettingsDialog.textInfoName": "Име", "Common.Views.SignSettingsDialog.textInfoTitle": "Заглавие на подписващия", "Common.Views.SignSettingsDialog.textInstructions": "Инструкции за подписващия", "Common.Views.SignSettingsDialog.textShowDate": "Покажете датата на знака в реда за подпис", "Common.Views.SignSettingsDialog.textTitle": "Настройка на подпис", "Common.Views.SignSettingsDialog.txtEmpty": "Това поле е задължително", "SSE.Controllers.DataTab.textWizard": "Текст в колони", "SSE.Controllers.DataTab.txtExpand": "Разширете", "SSE.Controllers.DocumentHolder.alignmentText": "Подравняване", "SSE.Controllers.DocumentHolder.centerText": "Център", "SSE.Controllers.DocumentHolder.deleteColumnText": "Изтриване на колона", "SSE.Controllers.DocumentHolder.deleteRowText": "Изтриване на ред", "SSE.Controllers.DocumentHolder.deleteText": "Изтрий", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Референтната връзка не съществува. Моля, коригирайте връзката или я изтрийте.", "SSE.Controllers.DocumentHolder.guestText": "Гост", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Колона вляво", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Колона вдясно", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Ред по-горе", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Ред по-долу", "SSE.Controllers.DocumentHolder.insertText": "Вмъкни", "SSE.Controllers.DocumentHolder.leftText": "Наляво", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Внимание", "SSE.Controllers.DocumentHolder.rightText": "Прав", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Ширина на колоната {0} символа ({1} пиксела)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Височина на реда {0} точки ({1} пиксела)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Натиснете CTRL и кликнете върху връзката", "SSE.Controllers.DocumentHolder.textInsertLeft": "Вмъкване в ляво", "SSE.Controllers.DocumentHolder.textInsertTop": "Вмъкване централно", "SSE.Controllers.DocumentHolder.textSym": "сим", "SSE.Controllers.DocumentHolder.tipIsLocked": "Този елемент се редактира от друг потребител.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Над средното", "SSE.Controllers.DocumentHolder.txtAddBottom": "Добавяне на долната граница", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Добавете лента за фракции", "SSE.Controllers.DocumentHolder.txtAddHor": "Добавете хоризонтална линия", "SSE.Controllers.DocumentHolder.txtAddLB": "Доабви лява долна линия", "SSE.Controllers.DocumentHolder.txtAddLeft": "Добави лява рамка", "SSE.Controllers.DocumentHolder.txtAddLT": "Доабви лява горна линия", "SSE.Controllers.DocumentHolder.txtAddRight": "Добавете дясна граница", "SSE.Controllers.DocumentHolder.txtAddTop": "Добавяне на горната граница", "SSE.Controllers.DocumentHolder.txtAddVer": "Добавете вертикална линия", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Подравняване по характер", "SSE.Controllers.DocumentHolder.txtAll": "(Всичко)", "SSE.Controllers.DocumentHolder.txtAnd": "и", "SSE.Controllers.DocumentHolder.txtBegins": "Започва с", "SSE.Controllers.DocumentHolder.txtBelowAve": "Под средното", "SSE.Controllers.DocumentHolder.txtBlanks": "(Заготовки)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Гранични свойства", "SSE.Controllers.DocumentHolder.txtBottom": "Отдоло", "SSE.Controllers.DocumentHolder.txtColumn": "Колона", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Изравняване на колона", "SSE.Controllers.DocumentHolder.txtContains": "Съдържа", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Намалете размера на аргумента", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Изтриване на аргумент", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Изтриване на ръчно прекъсване", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Изтриване на заграждащи символи", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Изтрийте обграждащите символи и разделители", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Изтриване на уравнението", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Изтриване на char", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Изтриване на радикал", "SSE.Controllers.DocumentHolder.txtEnds": "Завършва със", "SSE.Controllers.DocumentHolder.txtEquals": "Равно на", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Равен на цвета на клетката", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Равен на цвета на шрифта", "SSE.Controllers.DocumentHolder.txtExpand": "Разширяване и сортиране", "SSE.Controllers.DocumentHolder.txtExpandSort": "Данните до селекцията няма да бъдат сортирани. Искате ли да разширите избора, за да включите съседните данни или да продължите с сортирането само на избраните в момента клетки?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Отдоло", "SSE.Controllers.DocumentHolder.txtFilterTop": "Отгоре", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Промяна към линейна фракция", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Промяна на изкривена фракция", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Промяна на натрупаната фракция", "SSE.Controllers.DocumentHolder.txtGreater": "По-голям от", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "По-голяма или равна на", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Char над текста", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char под текста", "SSE.Controllers.DocumentHolder.txtHeight": "Висо<PERSON>ина", "SSE.Controllers.DocumentHolder.txtHideBottom": "Скриване на долната граница", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Скриване на долния лимит", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Скриване на затварящата скоба", "SSE.Controllers.DocumentHolder.txtHideDegree": "Скриване на степен", "SSE.Controllers.DocumentHolder.txtHideHor": "Скриване на хоризонталната линия", "SSE.Controllers.DocumentHolder.txtHideLB": "Скриване на долния ред вляво", "SSE.Controllers.DocumentHolder.txtHideLeft": "Скриване на лявата граница", "SSE.Controllers.DocumentHolder.txtHideLT": "Скриване на левия горен ред", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Скриване на отварящата скоба", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Скриване на контейнера", "SSE.Controllers.DocumentHolder.txtHideRight": "Скриване на дясната граница", "SSE.Controllers.DocumentHolder.txtHideTop": "Скриване на горната граница", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Скриване на горната граница", "SSE.Controllers.DocumentHolder.txtHideVer": "Скриване на вертикалната линия", "SSE.Controllers.DocumentHolder.txtImportWizard": "Съветник за импортиране на текст", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Увеличете размера на аргумента", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Вмъкване на аргумент след", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Вмъкнете аргумент преди", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Поставете ръчна почивка", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Поставете уравнение след", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Вмъкнете преди това уравнението", "SSE.Controllers.DocumentHolder.txtItems": "елементи", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Запазете само текста", "SSE.Controllers.DocumentHolder.txtLess": "По-малко от", "SSE.Controllers.DocumentHolder.txtLessEquals": "По-малко или равно на", "SSE.Controllers.DocumentHolder.txtLimitChange": "Място за промяна на ограниченията", "SSE.Controllers.DocumentHolder.txtLimitOver": "Ограничете текста", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Ограничете по текст", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Сравнете скобите с височината на аргумента", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Матрично подравняване", "SSE.Controllers.DocumentHolder.txtNoChoices": "Няма избор за запълване на клетката. <br> За подмяна могат да се избират само текстови стойности от колоната.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Не започва с", "SSE.Controllers.DocumentHolder.txtNotContains": "Не съдържа", "SSE.Controllers.DocumentHolder.txtNotEnds": "Не завършва с", "SSE.Controllers.DocumentHolder.txtNotEquals": "Не е равно", "SSE.Controllers.DocumentHolder.txtOr": "или", "SSE.Controllers.DocumentHolder.txtOverbar": "Завършете текста", "SSE.Controllers.DocumentHolder.txtPaste": "Паста", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Формула без граници", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Формула + ширина на колоната", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Форматиране на дестинация", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Поставете само форматирането", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Формула + формат на номера", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Поставете само формулата", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Формула + всички формати", "SSE.Controllers.DocumentHolder.txtPasteLink": "Поставете връзката", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Свързана снимка", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Обединяване на условното форматиране", "SSE.Controllers.DocumentHolder.txtPastePicture": "Снимка", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Форматиране на източника", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Транспониране", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Стойност + всички форматиране", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Стойност + формат на номера", "SSE.Controllers.DocumentHolder.txtPasteValues": "Поставете само стойността", "SSE.Controllers.DocumentHolder.txtPercent": "на сто", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Възстановяване на авторазширяване на таблицата", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Премахване на фракционната лента", "SSE.Controllers.DocumentHolder.txtRemLimit": "Премахване на ограничението", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Премахване на акцент", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Премахване на лентата", "SSE.Controllers.DocumentHolder.txtRemScripts": "Премахване на скриптове", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Премахване на индекса", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Премахване на горен индекс", "SSE.Controllers.DocumentHolder.txtRowHeight": "Височина ред", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Скриптове след текст", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Скриптове преди текст", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Показване на долната граница", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Показване на затварящата скоба", "SSE.Controllers.DocumentHolder.txtShowDegree": "Покажете степен", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Показване на скоба за отваряне", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Показване на контейнер", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Показване на горната граница", "SSE.Controllers.DocumentHolder.txtSorting": "Сортиране", "SSE.Controllers.DocumentHolder.txtSortSelected": "Сортирането е избрано", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Разтягащи скоби", "SSE.Controllers.DocumentHolder.txtTop": "Отгоре", "SSE.Controllers.DocumentHolder.txtUnderbar": "Бар под текст", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Отмяна на автоматичното разширяване на таблицата", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Използване на съветника за импортиране на текст", "SSE.Controllers.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Логичен", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Търсене и справка", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Текст и данни", "SSE.Controllers.LeftMenu.newDocumentTitle": "Електронна таблица без име", "SSE.Controllers.LeftMenu.textByColumns": "По колони", "SSE.Controllers.LeftMenu.textByRows": "По редове", "SSE.Controllers.LeftMenu.textFormulas": "Формули", "SSE.Controllers.LeftMenu.textItemEntireCell": "Цялото съдържание на клетката", "SSE.Controllers.LeftMenu.textLookin": "Погледни вътре", "SSE.Controllers.LeftMenu.textNoTextFound": "Данните, които търсите, не можаха да бъдат намерени. Моля, коригирайте опциите си за търсене.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Замяната е направена. {0} събития бяха пропуснати.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Търсенето е направено. Заместени случаи: {0}", "SSE.Controllers.LeftMenu.textSearch": "Търсене", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Стойности", "SSE.Controllers.LeftMenu.textWarning": "Внимание", "SSE.Controllers.LeftMenu.textWithin": "В рамките на", "SSE.Controllers.LeftMenu.textWorkbook": "Работна книга", "SSE.Controllers.LeftMenu.txtUntitled": "Неозаглавен", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ако продължите да записвате в този формат, всички функции, с изключение на текста, ще бъдат загубени. <br> Сигурни ли сте, че искате да продължите?", "SSE.Controllers.Main.confirmMoveCellRange": "Диапазонът на дестинационните клетки може да съдържа данни. Продължете операцията?", "SSE.Controllers.Main.confirmPutMergeRange": "Изходните данни съдържаха обединени клетки. <br> Преди да бъдат поставени в таблицата, те бяха развързани.", "SSE.Controllers.Main.convertationTimeoutText": "Превишава се времето на изтичане на реализациите.", "SSE.Controllers.Main.criticalErrorExtText": "Натиснете \"OK\", за да се върнете към списъка с документи.", "SSE.Controllers.Main.criticalErrorTitle": "Грешка", "SSE.Controllers.Main.downloadErrorText": "Изтеглянето се провали.", "SSE.Controllers.Main.downloadTextText": "Електронната таблица се изтегли ...", "SSE.Controllers.Main.downloadTitleText": "Изтегляне на електронна таблица", "SSE.Controllers.Main.errorAccessDeny": "Опитвате се да извършите действие, за което нямате права. <br> Моля, свържете се с администратора на сървъра за документи.", "SSE.Controllers.Main.errorArgsRange": "Грешка във въведената формула. <br> Използва се неправилен диапазон на аргумента.", "SSE.Controllers.Main.errorAutoFilterChange": "Операцията не е разрешена, тъй като се опитва да измести клетките в таблица на работния ви лист.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Операцията не може да бъде извършена за избраните клетки, тъй като не можете да преместите част от таблицата. <br> Изберете друг диапазон от данни, така че цялата таблица да бъде изместена и опитайте отново.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Операцията не може да бъде извършена за избрания диапазон от клетки. <br> Изберете един и същ диапазон от данни, различен от съществуващия, и опитайте отново.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Операцията не може да се извърши, защото областта съдържа филтрирани клетки. <br> М<PERSON><PERSON><PERSON>, отворете филтрираните елементи и опитайте отново.", "SSE.Controllers.Main.errorBadImageUrl": "URL адресът на изображението е неправилен", "SSE.Controllers.Main.errorChangeArray": "Не можете да променяте част от масив.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Връзката със сървъра е загубена. Документът не може да бъде редактиран в момента.", "SSE.Controllers.Main.errorConnectToServer": "Документът не може да бъде запасен. Моля, проверете настройките за връзка или се свържете с администратора си.<br>Когато щракнете върху бутона 'OK', ще бъдете подканени да изтеглите документа.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Тази команда не може да се използва с многократни селекции. <br> Изберете единичен обхват и опитайте отново.", "SSE.Controllers.Main.errorCountArg": "Грешка в въведената формула. <br> Използва се неправилен брой аргументи.", "SSE.Controllers.Main.errorCountArgExceed": "Грешка във въведената формула. <br> Брой аргументи е надвишен.", "SSE.Controllers.Main.errorCreateDefName": "Съществуващите имена на обхвати не могат да бъдат редактирани и новите не могат да бъдат създадени <br> в момента, тъй като някои от тях се редактират.", "SSE.Controllers.Main.errorDatabaseConnection": "Външна грешка. <br> Грешка при свързване към база данни. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorDataEncrypted": "Получени са криптирани промени, които не могат да бъдат дешифрирани.", "SSE.Controllers.Main.errorDataRange": "Неправилен обхват от данни.", "SSE.Controllers.Main.errorDefaultMessage": "Код на грешка: %1", "SSE.Controllers.Main.errorEditingDownloadas": "Възникна грешка по време на работа с документа. <br> Използвайте опцията 'Download as', за да запишете архивното копие на файла на твърдия диск на компютъра.", "SSE.Controllers.Main.errorEditingSaveas": "Възникна грешка по време на работа с документа. <br> Използвайте опцията „Запазване като ...“, за да запишете архивното копие на файла на твърдия диск на компютъра.", "SSE.Controllers.Main.errorEmailClient": "Не може да се намери имейл клиент.", "SSE.Controllers.Main.errorFilePassProtect": "Файлът е защитен с парола и не може да бъде отворен.", "SSE.Controllers.Main.errorFileRequest": "Външна грешка. <br> Грешка в заявката за файл. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorFileVKey": "Външна грешка. <br> Неправилен ключ за защита. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorFillRange": "Избраният диапазон от клетки не може да се запълни. <br> Всички обединени клетки трябва да са с еднакъв размер.", "SSE.Controllers.Main.errorForceSave": "При запазването на файла възникна грешка. Моля, използвайте опцията \"Изтегляне като\", за да запишете файла на твърдия диск на компютъра или опитайте отново по-късно.", "SSE.Controllers.Main.errorFormulaName": "Грешка във въведената формула. <br> Използва се неправилно име на формула.", "SSE.Controllers.Main.errorFormulaParsing": "Вътрешна грешка при анализиране на формулата.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Текстовите стойности във формули са ограничени до 255 знака.<br>Използвайте функцията CONCATENATE или оператора за свързване (&amp;).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Функцията се отнася за лист, който не съществува. <br> Моля, проверете данните и опитайте отново.", "SSE.Controllers.Main.errorInvalidRef": "Въведете правилно име за избора или валидна референция, към която да отидете.", "SSE.Controllers.Main.errorKeyEncrypt": "Дескриптор на неизвестен ключ", "SSE.Controllers.Main.errorKeyExpire": "Дескрипторът на ключовете е изтекъл", "SSE.Controllers.Main.errorLockedAll": "Операцията не може да се извърши, тъй като листа е заключен от друг потребител.", "SSE.Controllers.Main.errorLockedCellPivot": "Не можете да променяте данни в обобщена таблица.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Листът не може да бъде преименуван в момента, тъй като се преименува от друг потребител", "SSE.Controllers.Main.errorMaxPoints": "Максималният брой точки в серия на графиката е 4096.", "SSE.Controllers.Main.errorMoveRange": "Не може да се промени част от обединена клетка", "SSE.Controllers.Main.errorMultiCellFormula": "Формулирайте с множество клетки, които не са позволени в таблицата.", "SSE.Controllers.Main.errorNoDataToParse": "Не са избрани данни за анализиране.", "SSE.Controllers.Main.errorOpenWarning": "Дължината на една от формулите във файла надвишава разрешения брой знаци и е премахната.", "SSE.Controllers.Main.errorOperandExpected": "Въведеният синтаксис на функцията не е правилен. Моля, проверете дали липсва една от скобите - '(' или ')'.", "SSE.Controllers.Main.errorPasteMaxRange": "Областта за копиране и поставяне не съвпада. <br> <PERSON><PERSON><PERSON><PERSON>, изберете област със същия размер или кликнете върху първата клетка в ред, за да поставите копираните клетки.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "За съжаление, не е възможно да се отпечатат повече от 1500 страници едновременно в текущата версия на програмата. <br> Това ограничение ще бъде премахнато в предстоящите издания.", "SSE.Controllers.Main.errorProcessSaveResult": "Запазване не бе успешно", "SSE.Controllers.Main.errorServerVersion": "Версията на редактора е актуализирана. Страницата ще бъде презаредена, за да приложи промените.", "SSE.Controllers.Main.errorSessionAbsolute": "Сесията за редактиране на документ изтече. Моля, презаредете страницата.", "SSE.Controllers.Main.errorSessionIdle": "Документът не е редактиран дълго време. Моля, презаредете страницата.", "SSE.Controllers.Main.errorSessionToken": "Връзката със сървъра е прекъсната. Моля, презаредете страницата.", "SSE.Controllers.Main.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Controllers.Main.errorToken": "Токенът за защита на документа не е правилно оформен. <br> Моля, свържете се с вашия администратор на сървър за документи.", "SSE.Controllers.Main.errorTokenExpire": "Токенът за защита на документа е изтекъл. <br> <PERSON><PERSON><PERSON><PERSON>, свържете се с администратора на документа.", "SSE.Controllers.Main.errorUnexpectedGuid": "Външна грешка. <br> Неочакван GUID. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorUpdateVersion": "Версията на файла е променена. Страницата ще бъде презаредена.", "SSE.Controllers.Main.errorUserDrop": "Файлът не може да бъде достъпен в момента.", "SSE.Controllers.Main.errorUsersExceed": "Превишен е броят на потребителите, позволени от ценовия план", "SSE.Controllers.Main.errorViewerDisconnect": "Връзката е загубена. Все още можете да преглеждате документа,<br>но няма да можете да го изтеглите или отпечатате, докато връзката бъде възстановена.", "SSE.Controllers.Main.errorWrongBracketsCount": "Грешка във въведената формула. <br> Използва се грешен брой скоби.", "SSE.Controllers.Main.errorWrongOperator": "Грешка в въведената формула. Използва се грешен оператор. <br> <PERSON><PERSON><PERSON><PERSON>, коригирайте грешката.", "SSE.Controllers.Main.leavePageText": "Имате незапазени промени в тази електронна таблица. Кликнете върху „Остани на тази страница“ и след това върху „Запазване“, за да ги запазите. Кликнете върху „Оставете тази страница“, за да отхвърлите всички незапазени промени.", "SSE.Controllers.Main.loadFontsTextText": "Данните се зареждат ...", "SSE.Controllers.Main.loadFontsTitleText": "Зареждане на данни", "SSE.Controllers.Main.loadFontTextText": "Данните се заредете ...", "SSE.Controllers.Main.loadFontTitleText": "Зареждане на данни", "SSE.Controllers.Main.loadImagesTextText": "Изображения се зареждат ...", "SSE.Controllers.Main.loadImagesTitleText": "Зареждане на изображения", "SSE.Controllers.Main.loadImageTextText": "Изображението се се зарежда ...", "SSE.Controllers.Main.loadImageTitleText": "Зареждане на изображението", "SSE.Controllers.Main.loadingDocumentTitleText": "Електронната таблица се зарежда", "SSE.Controllers.Main.notcriticalErrorTitle": "Внимание", "SSE.Controllers.Main.openErrorText": "Възникна грешка при отварянето на файла", "SSE.Controllers.Main.openTextText": "Отваря се електронната таблица ...", "SSE.Controllers.Main.openTitleText": "Отваряне на електронна таблица", "SSE.Controllers.Main.pastInMergeAreaError": "Не може да се промени част от обединена клетка", "SSE.Controllers.Main.printTextText": "Отпечатване на електронна таблица ...", "SSE.Controllers.Main.printTitleText": "Отпечатване на електронна таблица", "SSE.Controllers.Main.reloadButtonText": "Презареждане на страницата", "SSE.Controllers.Main.requestEditFailedMessageText": "Някой редактира този документ в момента. Моля, опитайте отново по-късно.", "SSE.Controllers.Main.requestEditFailedTitleText": "Отказан достъп", "SSE.Controllers.Main.saveErrorText": "Възникна грешка при запазването на файла", "SSE.Controllers.Main.saveTextText": "Запазва се електронната таблица ...", "SSE.Controllers.Main.saveTitleText": "Запазване на електронна таблица", "SSE.Controllers.Main.scriptLoadError": "Връзката е твърде бавна, някои от компонентите не могат да бъдат заредени. Моля, презаредете страницата.", "SSE.Controllers.Main.textAnonymous": "Анонимен", "SSE.Controllers.Main.textBuyNow": "Посетете уебсайта", "SSE.Controllers.Main.textChangesSaved": "Всички промени са запазени", "SSE.Controllers.Main.textClose": "Затвори", "SSE.Controllers.Main.textCloseTip": "Кликнете, за да затворите върха", "SSE.Controllers.Main.textConfirm": "Потвърждаване", "SSE.Controllers.Main.textContactUs": "Свържете се с продажбите", "SSE.Controllers.Main.textCustomLoader": "Моля, имайте предвид, че според условията на лиценза нямате право да сменяте товарача. <br> Моля, свържете се с нашия отдел Продажби, за да получите оферта.", "SSE.Controllers.Main.textGuest": "Гост", "SSE.Controllers.Main.textLoadingDocument": "Електронната таблица се зарежда", "SSE.Controllers.Main.textNo": "Не", "SSE.Controllers.Main.textNoLicenseTitle": "Ограничение за връзка ONLYOFFICE", "SSE.Controllers.Main.textPaidFeature": "Пла<PERSON><PERSON>на функция", "SSE.Controllers.Main.textPleaseWait": "Операцията може да отнеме повече време от очакваното. Моля изчакай...", "SSE.Controllers.Main.textShape": "Форма", "SSE.Controllers.Main.textStrict": "Строг режим", "SSE.Controllers.Main.textText": "Текст", "SSE.Controllers.Main.textTryUndoRedo": "Функциите за отмяна/възстановяване са деактивирани за режима Бързо съвместно редактиране. <br> Кликнете върху бутона „Строг режим“, за да превключите в режим на стриктно съвместно редактиране, за да редактирате файла без намеса на други потребители и да изпращате промените само след като ги запазите тях. Можете да превключвате между режимите за съвместно редактиране с помощта на редактора Разширени настройки.", "SSE.Controllers.Main.textYes": "Да", "SSE.Controllers.Main.titleLicenseExp": "Лицензът е изтекъл", "SSE.Controllers.Main.titleServerVersion": "Редакторът е актуализиран", "SSE.Controllers.Main.txtAccent": "Акцент", "SSE.Controllers.Main.txtArt": "Вашият текст тук", "SSE.Controllers.Main.txtBasicShapes": "Основни форми", "SSE.Controllers.Main.txtButtons": "Бутони", "SSE.Controllers.Main.txtCallouts": "Допълнителните описания", "SSE.Controllers.Main.txtCharts": "Диаграми", "SSE.Controllers.Main.txtDiagramTitle": "Заглавие на диаграмата", "SSE.Controllers.Main.txtEditingMode": "Задаване на режим на редактиране ...", "SSE.Controllers.Main.txtFiguredArrows": "Фигурни стрели", "SSE.Controllers.Main.txtGroup": "Гру<PERSON>а", "SSE.Controllers.Main.txtLines": "Линии", "SSE.Controllers.Main.txtMath": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>и<PERSON>", "SSE.Controllers.Main.txtPrintArea": "Печат_зона", "SSE.Controllers.Main.txtRectangles": "Правоъгълници", "SSE.Controllers.Main.txtSeries": "Серия", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Извикваща линия 1 (граница и акцент)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Извивка на линия 2 (граница и акцент)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Извикваща линия 3 (граница и акцент)", "SSE.Controllers.Main.txtShape_accentCallout1": "Позиция от ред 1 (акцентна лента)", "SSE.Controllers.Main.txtShape_accentCallout2": "Извикваща линия 2 (акцент)", "SSE.Controllers.Main.txtShape_accentCallout3": "Извикваща линия 3 (акцентна лента)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON>у<PERSON><PERSON><PERSON> \"Назад\" или \"Предишен\"", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Нача<PERSON>ен бутон", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Празен бутон", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Бутон за документи", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Бутон за край", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Напред или Следващ бутон", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Бутон за помощ", "SSE.Controllers.Main.txtShape_actionButtonHome": "Нача<PERSON>ен бутон", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Бутон за информация", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Бутон за филми", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Бутон за връщане", "SSE.Controllers.Main.txtShape_actionButtonSound": "Бутон за звука", "SSE.Controllers.Main.txtShape_arc": "Дъга", "SSE.Controllers.Main.txtShape_bentArrow": "Сгъната стрелка", "SSE.Controllers.Main.txtShape_bentConnector5": "Коляновият конектор", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Съединител за стрелки с лакът", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Конектор с двойна стрелка", "SSE.Controllers.Main.txtShape_bentUpArrow": "Сгъната стрелка нагоре", "SSE.Controllers.Main.txtShape_bevel": "Откос", "SSE.Controllers.Main.txtShape_blockArc": "Блок arc", "SSE.Controllers.Main.txtShape_borderCallout1": "Извикваща линия 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Извикваща линия 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Линия за линия 3", "SSE.Controllers.Main.txtShape_bracePair": "Двойна скоба", "SSE.Controllers.Main.txtShape_callout1": "Очертание от ред 1 (без граница)", "SSE.Controllers.Main.txtShape_callout2": "Очертание от ред 2 (без граница)", "SSE.Controllers.Main.txtShape_callout3": "Обложка за линия 3 (без граница)", "SSE.Controllers.Main.txtShape_can": "Мога", "SSE.Controllers.Main.txtShape_chevron": "Орнамент във формата на", "SSE.Controllers.Main.txtShape_chord": "Акорд", "SSE.Controllers.Main.txtShape_circularArrow": "Кръгла стрелка", "SSE.Controllers.Main.txtShape_cloud": "Облак", "SSE.Controllers.Main.txtShape_cloudCallout": "Облаковидно изнесено означение", "SSE.Controllers.Main.txtShape_corner": "Ъглов", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Извитият конектор", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Съединител с крива стрелка", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Съединител с двойна стрелка", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Извита надолу стрелка", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Лява стрелка", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Извита дясна стрелка", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Извита стрелка нагоре", "SSE.Controllers.Main.txtShape_decagon": "Десетоъгълник", "SSE.Controllers.Main.txtShape_diagStripe": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на лента", "SSE.Controllers.Main.txtShape_diamond": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Дванадесетоъгълник", "SSE.Controllers.Main.txtShape_donut": "Поничка", "SSE.Controllers.Main.txtShape_doubleWave": "Дво<PERSON>на вълна", "SSE.Controllers.Main.txtShape_downArrow": "Стрелка надолу", "SSE.Controllers.Main.txtShape_downArrowCallout": "Стрелка надолу", "SSE.Controllers.Main.txtShape_ellipse": "Елипса", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Лентата е извита надолу", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Лентата е извита нагоре", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: Алтернативен процес", "SSE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: Сортиране", "SSE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: Съединител", "SSE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: Решение", "SSE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: Закъснение", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: Показване", "SSE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: До<PERSON>у<PERSON><PERSON><PERSON>т", "SSE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: Извличане", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: Да<PERSON>ни", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: Вътре<PERSON>на памет", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: Магнитен диск", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: Съхранение с директен достъп", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: Съхранение с последователен достъп", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: Ръчен вход", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: Ръчна операция", "SSE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: Обединяване", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: Мул<PERSON>и<PERSON><PERSON><PERSON>умент ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: Съединител извън страницата", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: Съхранени данни", "SSE.Controllers.Main.txtShape_flowChartOr": "Блок-схема: Или", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: Предварително дефиниран процес", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: Подготовка", "SSE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: Процес", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: Карта", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: Перфорир<PERSON>на лента", "SSE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: Сортиране", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: Сумираща свързване", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: Знак за край", "SSE.Controllers.Main.txtShape_foldedCorner": "Сгънат ъгъл", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON>ол<PERSON><PERSON><PERSON> кадър", "SSE.Controllers.Main.txtShape_heart": "Сърце", "SSE.Controllers.Main.txtShape_heptagon": "Седмоъгълник", "SSE.Controllers.Main.txtShape_hexagon": "Шестоъгълник", "SSE.Controllers.Main.txtShape_homePlate": "Пентагона", "SSE.Controllers.Main.txtShape_horizontalScroll": "Хоризонтален превъртане", "SSE.Controllers.Main.txtShape_irregularSeal1": "Експлозия 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Експлозия 2", "SSE.Controllers.Main.txtShape_leftArrow": "Лява стрелка", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Стрелка наляво", "SSE.Controllers.Main.txtShape_leftBrace": "Ляв скоба", "SSE.Controllers.Main.txtShape_leftBracket": "Лява скоба", "SSE.Controllers.Main.txtShape_leftRightArrow": "Лява стрелка надясно", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Лява дясна стрелка", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Стрелка наляво нагоре", "SSE.Controllers.Main.txtShape_leftUpArrow": "Стрелка наляво нагоре", "SSE.Controllers.Main.txtShape_lightningBolt": "Светкавица", "SSE.Controllers.Main.txtShape_line": "Линия", "SSE.Controllers.Main.txtShape_lineWithArrow": "Стрелка", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Дв<PERSON><PERSON>на стрелка", "SSE.Controllers.Main.txtShape_mathDivide": "Делене", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON>ав<PERSON>н", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Умножение", "SSE.Controllers.Main.txtShape_mathNotEqual": "Не е равно", "SSE.Controllers.Main.txtShape_mathPlus": "Плюс", "SSE.Controllers.Main.txtShape_moon": "Луна", "SSE.Controllers.Main.txtShape_noSmoking": "Символ \"Не\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Стрелка надясно", "SSE.Controllers.Main.txtShape_octagon": "Осмоъгълник", "SSE.Controllers.Main.txtShape_parallelogram": "Успоредник", "SSE.Controllers.Main.txtShape_pentagon": "Пентагона", "SSE.Controllers.Main.txtShape_pie": "Кръгова", "SSE.Controllers.Main.txtShape_plaque": "Знак", "SSE.Controllers.Main.txtShape_plus": "Плюс", "SSE.Controllers.Main.txtShape_polyline1": "Драсканица", "SSE.Controllers.Main.txtShape_polyline2": "Свободна форма", "SSE.Controllers.Main.txtShape_quadArrow": "Четириядрена стрелка", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Квар<PERSON><PERSON><PERSON>на стрелка", "SSE.Controllers.Main.txtShape_rect": "Правоъгълник", "SSE.Controllers.Main.txtShape_ribbon": "Долна лента", "SSE.Controllers.Main.txtShape_ribbon2": "Лентата нагоре", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON>на стрелка", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Стрелка надясно", "SSE.Controllers.Main.txtShape_rightBrace": "Надясно", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON>на скоба", "SSE.Controllers.Main.txtShape_round1Rect": "Кръгъл правоъгълник с един ъгъл", "SSE.Controllers.Main.txtShape_round2DiagRect": "Кръгъл правоъгълник с диагонален ъгъл", "SSE.Controllers.Main.txtShape_round2SameRect": "Кръгла правоъгълник с една и съща страна", "SSE.Controllers.Main.txtShape_roundRect": "Кръгъл ъгъл правоъгълник", "SSE.Controllers.Main.txtShape_rtTriangle": "Прав триъгълник", "SSE.Controllers.Main.txtShape_smileyFace": "Усмивка на лицето", "SSE.Controllers.Main.txtShape_snip1Rect": "Правоъгълник с единичен ъгъл", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Правоъгълник с диагонален ъгъл", "SSE.Controllers.Main.txtShape_snip2SameRect": "Отрязан правоъгълник от същия страничен ъгъл", "SSE.Controllers.Main.txtShape_snipRoundRect": "Правоъгълник с изрязани и кръгли ъгли", "SSE.Controllers.Main.txtShape_spline": "Крива", "SSE.Controllers.Main.txtShape_star10": "10-точкова звезда", "SSE.Controllers.Main.txtShape_star12": "12-точкова звезда", "SSE.Controllers.Main.txtShape_star16": "16-точкова звезда", "SSE.Controllers.Main.txtShape_star24": "24-точкова звезда", "SSE.Controllers.Main.txtShape_star32": "Звезда с 32 точки", "SSE.Controllers.Main.txtShape_star4": "4-точкова звезда", "SSE.Controllers.Main.txtShape_star5": "5-точкова звезда", "SSE.Controllers.Main.txtShape_star6": "6-точкова звезда", "SSE.Controllers.Main.txtShape_star7": "7-точкова звезда", "SSE.Controllers.Main.txtShape_star8": "8-точкова звезда", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Стрелка надясно", "SSE.Controllers.Main.txtShape_sun": "Слънце", "SSE.Controllers.Main.txtShape_teardrop": "Капко образно", "SSE.Controllers.Main.txtShape_textRect": "Текстово поле", "SSE.Controllers.Main.txtShape_trapezoid": "Тра<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Триъгълник", "SSE.Controllers.Main.txtShape_upArrow": "Стрелка нагоре", "SSE.Controllers.Main.txtShape_upArrowCallout": "Стрелка нагоре", "SSE.Controllers.Main.txtShape_upDownArrow": "Стрелка нагоре надолу", "SSE.Controllers.Main.txtShape_uturnArrow": "Стрелка за завъртане", "SSE.Controllers.Main.txtShape_verticalScroll": "Вертикален скрол", "SSE.Controllers.Main.txtShape_wave": "Вълна", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Овална извивка", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Правоъгълна извивка", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Закръглена правоъгълна извивка", "SSE.Controllers.Main.txtStarsRibbons": "Звезди и панделки", "SSE.Controllers.Main.txtStyle_Bad": "Лошо", "SSE.Controllers.Main.txtStyle_Calculation": "Изчисление", "SSE.Controllers.Main.txtStyle_Check_Cell": "Клетка за проверка", "SSE.Controllers.Main.txtStyle_Comma": "Запетая", "SSE.Controllers.Main.txtStyle_Currency": "Валута", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Обяснителен текст", "SSE.Controllers.Main.txtStyle_Good": "Добре", "SSE.Controllers.Main.txtStyle_Heading_1": "Заглавие 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Функция 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Функция 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Функция 4", "SSE.Controllers.Main.txtStyle_Input": "Вход", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Свързана клетка", "SSE.Controllers.Main.txtStyle_Neutral": "Неутрален", "SSE.Controllers.Main.txtStyle_Normal": "Нормален", "SSE.Controllers.Main.txtStyle_Note": "Забележка", "SSE.Controllers.Main.txtStyle_Output": "Продукция", "SSE.Controllers.Main.txtStyle_Percent": "На сто", "SSE.Controllers.Main.txtStyle_Title": "Заглавие", "SSE.Controllers.Main.txtStyle_Total": "Обща сума", "SSE.Controllers.Main.txtStyle_Warning_Text": "Предупреденилен текст", "SSE.Controllers.Main.txtTable": "Таблица", "SSE.Controllers.Main.txtXAxis": "X ос", "SSE.Controllers.Main.txtYAxis": "Y ос", "SSE.Controllers.Main.unknownErrorText": "Неизвестна грешка.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Вашият браузър не се поддържа.", "SSE.Controllers.Main.uploadImageExtMessage": "Неизвестен формат на изображението.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Няма качени изображения.", "SSE.Controllers.Main.uploadImageSizeMessage": "Превишено е ограничението за максимален размер на изображението.", "SSE.Controllers.Main.uploadImageTextText": "Качва се изображението ...", "SSE.Controllers.Main.uploadImageTitleText": "Качване на изображение", "SSE.Controllers.Main.waitText": "Моля, изчакайте...", "SSE.Controllers.Main.warnBrowserIE9": "Приложението има ниски възможности за IE9. Използвайте IE10 или по-висока", "SSE.Controllers.Main.warnBrowserZoom": "Текущата настройка за мащабиране на браузъра ви не се поддържа напълно. Моля, нулирайте стойността по подразбиране, като натиснете Ctrl + 0.", "SSE.Controllers.Main.warnLicenseExceeded": "Броят на едновременните връзки към сървъра за документи е превишен и документът ще бъде отворен само за преглед. <br> За повече информация се обърнете към администратора.", "SSE.Controllers.Main.warnLicenseExp": "Вашият лиценз е изтекъл. <br> <PERSON><PERSON><PERSON><PERSON>, актуализирайте лиценза си и опреснете страницата.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Броят на едновременните потребители е надхвърлен и документът ще бъде отворен само за преглед. <br> За повече информация се свържете с администратора си.", "SSE.Controllers.Main.warnNoLicense": "Тази версия на редакторите на %1 има някои ограничения за едновременни връзки към сървъра за документи. <br> Ако имате нужда от повече, моля обмислете закупуването на търговски лиценз.", "SSE.Controllers.Main.warnNoLicenseUsers": "Тази версия на редакторите на %1 има някои ограничения за едновременни потребители. <br> Ако имате нужда от повече, моля обмислете закупуването на търговски лиценз.", "SSE.Controllers.Main.warnProcessRightsChange": "На вас е отказано правото да редактирате файла.", "SSE.Controllers.Print.strAllSheets": "Всички листове", "SSE.Controllers.Print.textWarning": "Внимание", "SSE.Controllers.Print.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Controllers.Print.warnCheckMargings": "Маржовете са неправилни", "SSE.Controllers.Statusbar.errorLastSheet": "Работната книга трябва да има поне един видим работен лист.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Не можете да изтриете работния лист.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.warnDeleteSheet": "Работният лист може да съдържа данни. Наистина ли искате да продължите?", "SSE.Controllers.Statusbar.zoomText": "Мащаб {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Шрифтът, който ще запазите, не е наличен в текущото устройство. <br> Стилът на текста ще се покаже с помощта на един от системните шрифтове, запазеният шрифт ще се използва, когато е налице. <br> Искате ли да продължите ?", "SSE.Controllers.Toolbar.errorMaxRows": "ГРЕШКА! Максималният брой поредици данни за диаграма е 255", "SSE.Controllers.Toolbar.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Controllers.Toolbar.textAccent": "Акценти", "SSE.Controllers.Toolbar.textBracket": "Скоби", "SSE.Controllers.Toolbar.textFontSizeErr": "Въведената стойност е неправилна. <br> Въведете числова стойност между 1 и 409", "SSE.Controllers.Toolbar.textFraction": "Фракции", "SSE.Controllers.Toolbar.textFunction": "Функция", "SSE.Controllers.Toolbar.textInsert": "Вмъкни", "SSE.Controllers.Toolbar.textIntegral": "Интеграли", "SSE.Controllers.Toolbar.textLargeOperator": "Големи оператори", "SSE.Controllers.Toolbar.textLimitAndLog": "Граници и логаритми", "SSE.Controllers.Toolbar.textLongOperation": "Дълга операция", "SSE.Controllers.Toolbar.textMatrix": "Матрици", "SSE.Controllers.Toolbar.textOperator": "Операторите", "SSE.Controllers.Toolbar.textPivot": "Обобщена таблица", "SSE.Controllers.Toolbar.textRadical": "Радикалите", "SSE.Controllers.Toolbar.textScript": "Скриптове", "SSE.Controllers.Toolbar.textSymbols": "Символи", "SSE.Controllers.Toolbar.textWarning": "Внимание", "SSE.Controllers.Toolbar.txtAccent_Accent": "Ос<PERSON><PERSON>р", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Стрелка горе вдясно", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Стрелка наляво по-горе", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Стрелка нагоре отдясно", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Долен ред", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Бар горе", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Буквена формула (с контейнер)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Буквена формула (пример)", "SSE.Controllers.Toolbar.txtAccent_Check": "Проверка", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Долна скоба", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Горна скоба", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Вектор А", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC с горна черта", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y с горна черта", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Тройна точка", "SSE.Controllers.Toolbar.txtAccent_DDot": "Двойна точка", "SSE.Controllers.Toolbar.txtAccent_Dot": "Точка", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Двойна лента", "SSE.Controllers.Toolbar.txtAccent_Grave": "Ударение", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Групиране по-долу", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Символът за групиране по-горе", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON> харпун отгоре", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Отдясно се намира харпунът по-горе", "SSE.Controllers.Toolbar.txtAccent_Hat": "Шапка", "SSE.Controllers.Toolbar.txtAccent_Smile": "Знак за краткост", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Тилда", "SSE.Controllers.Toolbar.txtBracket_Angle": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Curve": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Случаи (две условия)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Случаи (три условия)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Стек предмет", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Стек предмет", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Примерни случаи", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Биномиален коефициент", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Биномиален коефициент", "SSE.Controllers.Toolbar.txtBracket_Line": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Скоби", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Скоби", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Round": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Square": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Скоби", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Скоби", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Скоби", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtExpand": "Разширяване и сортиране", "SSE.Controllers.Toolbar.txtExpandSort": "Данните до селекцията няма да бъдат сортирани. Искате ли да разширите избора, за да включите съседните данни или да продължите с сортирането само на избраните в момента клетки?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Изкривена фракция", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Диференциал", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Диференциал", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Диференциал", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Диференциал", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Линейна фракция", "SSE.Controllers.Toolbar.txtFractionPi_2": "Пи над 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Малка фракция", "SSE.Controllers.Toolbar.txtFractionVertical": "Натрупана фракция", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Обратна косинусна функция", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Хиперболична инверсна косинусна функция", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Обратна котангенсна функция", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Хиперболична инверсна котангенс функция", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Обратна косекантна функция", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Хиперболична обратна косекантна функция", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Обратна секансова функция", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Хиперболична инверсна секундна функция", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Обратна функция на синуса", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Хиперболична инверсна синусова функция", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Функция за обратна допирателна", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Хиперболична инверсна тангенциална функция", "SSE.Controllers.Toolbar.txtFunction_Cos": "Косинусна функция", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Хиперболична косинусна функция", "SSE.Controllers.Toolbar.txtFunction_Cot": "Котангенсова функция", "SSE.Controllers.Toolbar.txtFunction_Coth": "Хиперболична котангенсна функция", "SSE.Controllers.Toolbar.txtFunction_Csc": "Функция на косекант", "SSE.Controllers.Toolbar.txtFunction_Csch": "Хиперболична косекантна функция", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Косинус 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Формула на допирателната", "SSE.Controllers.Toolbar.txtFunction_Sec": "Секансова функция", "SSE.Controllers.Toolbar.txtFunction_Sech": "Хиперболична секундна функция", "SSE.Controllers.Toolbar.txtFunction_Sin": "Синусова функция", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Хиперболична функция на синуса", "SSE.Controllers.Toolbar.txtFunction_Tan": "Функция на допирателната", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Хиперболична допирателна функция", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtIntegral": "Интеграл", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Диференциална тета", "SSE.Controllers.Toolbar.txtIntegral_dx": "Диференциал x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Диференциал y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Интеграл", "SSE.Controllers.Toolbar.txtIntegralDouble": "Двойна интегрална", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Двойна интегрална", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Двойна интегрална", "SSE.Controllers.Toolbar.txtIntegralOriented": "Контурен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Повърхностен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Повърхностен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Повърхностен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Интегрален обем", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Интегрален обем", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Интегрален обем", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Интеграл", "SSE.Controllers.Toolbar.txtIntegralTriple": "Троен интеграл", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Троен интеграл", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Троен интеграл", "SSE.Controllers.Toolbar.txtInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Съюз", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Ограничен пример", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Максимален пример", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON>и<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Естествен логаритъм", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Лог<PERSON><PERSON><PERSON><PERSON>ъм", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Лог<PERSON><PERSON><PERSON><PERSON>ъм", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Максимален", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Мини<PERSON>ум", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Празна матрица 1 x 2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Празна матрица 1 x 3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Празна матрица 2 x 1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Празна матрица 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Празна матрица 2 x 3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Празна матрица 3 x 1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Празна матрица 3 x 2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Празна матрица 3 x 3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Базови точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Средни точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Диагонални точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Вертикални точки", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Разредена матрица", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Разредена матрица", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Матрица за идентичност 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 матрица за идентичност", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 матрица за идентичност", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 матрица за идентичност", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Долу с дясна лява стрелка", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрелка горе вдясно", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрелка наляво долу", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрелка наляво по-горе", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Долу вдясно стрелка", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрелка нагоре отдясно", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Колонът е равен", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Добивите", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Делта добиви", "SSE.Controllers.Toolbar.txtOperator_Definition": "Равен на дефиницията", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Делта е равно на", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Долу с дясна лява стрелка", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Стрелка горе вдясно", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрелка наляво долу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрелка наляво по-горе", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Долу вдясно стрелка", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрелка нагоре отдясно", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Равноправен", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Минус равен", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс равни", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Измерено от", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Радикален", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Радикален", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Квад<PERSON>а<PERSON><PERSON><PERSON> корен със степен", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Кубичен корен", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Радикално със степен", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Корен квадратен", "SSE.Controllers.Toolbar.txtScriptCustom_1": "Скрипт", "SSE.Controllers.Toolbar.txtScriptCustom_2": "Скрипт", "SSE.Controllers.Toolbar.txtScriptCustom_3": "Скрипт", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Скрипт", "SSE.Controllers.Toolbar.txtScriptSub": "До<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSup": "Долен-горен", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Ляв индекс-горен индекс", "SSE.Controllers.Toolbar.txtScriptSup": "Горен индекс", "SSE.Controllers.Toolbar.txtSorting": "Сортиране", "SSE.Controllers.Toolbar.txtSortSelected": "Сортиране е избрано", "SSE.Controllers.Toolbar.txtSymbol_about": "Приблизително", "SSE.Controllers.Toolbar.txtSymbol_additional": "Допълнение", "SSE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Алфа", "SSE.Controllers.Toolbar.txtSymbol_approx": "Почти равно на", "SSE.Controllers.Toolbar.txtSymbol_ast": "Оператор на звездичка", "SSE.Controllers.Toolbar.txtSymbol_beta": "Бета", "SSE.Controllers.Toolbar.txtSymbol_beth": "Залагане", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Оператор на куршум", "SSE.Controllers.Toolbar.txtSymbol_cap": "Пресичане", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Кубичен корен", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Хоризонтална елипса на средна линия", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Градуса по Целзий", "SSE.Controllers.Toolbar.txtSymbol_chi": "Хи", "SSE.Controllers.Toolbar.txtSymbol_cong": "Приблизително равен на", "SSE.Controllers.Toolbar.txtSymbol_cup": "Съюз", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Долу в дясно диагонално елипса", "SSE.Controllers.Toolbar.txtSymbol_degree": "Степени", "SSE.Controllers.Toolbar.txtSymbol_delta": "Делта", "SSE.Controllers.Toolbar.txtSymbol_div": "Знак за разделяне", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Стрелка надолу", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Празен комплект", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Епсилон", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON>ав<PERSON>н", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Идентичен на", "SSE.Controllers.Toolbar.txtSymbol_eta": "Ета", "SSE.Controllers.Toolbar.txtSymbol_exists": "Съществуват", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Факториел", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градуси по Фаренхайт", "SSE.Controllers.Toolbar.txtSymbol_forall": "За всички", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Гама", "SSE.Controllers.Toolbar.txtSymbol_geq": "По-голяма или равна на", "SSE.Controllers.Toolbar.txtSymbol_gg": "Много по-голяма от", "SSE.Controllers.Toolbar.txtSymbol_greater": "По-голям от", "SSE.Controllers.Toolbar.txtSymbol_in": "Елемент на", "SSE.Controllers.Toolbar.txtSymbol_inc": "Увеличение", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Безкрайност", "SSE.Controllers.Toolbar.txtSymbol_iota": "Йота", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Капа", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Ламбда", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Лява стрелка", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрелка наляво-надясно", "SSE.Controllers.Toolbar.txtSymbol_leq": "По-малко или равно на", "SSE.Controllers.Toolbar.txtSymbol_less": "По-малко от", "SSE.Controllers.Toolbar.txtSymbol_ll": "Много по-малко", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Мин<PERSON>с плюс", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "SSE.Controllers.Toolbar.txtSymbol_neq": "Не е равно на", "SSE.Controllers.Toolbar.txtSymbol_ni": "Съдържа като член", "SSE.Controllers.Toolbar.txtSymbol_not": "Не се подписва", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Не съществува", "SSE.Controllers.Toolbar.txtSymbol_nu": "Ну", "SSE.Controllers.Toolbar.txtSymbol_o": "Омикрон", "SSE.Controllers.Toolbar.txtSymbol_omega": "Омега", "SSE.Controllers.Toolbar.txtSymbol_partial": "Частичен диференциал", "SSE.Controllers.Toolbar.txtSymbol_percent": "Процент", "SSE.Controllers.Toolbar.txtSymbol_phi": "Фи", "SSE.Controllers.Toolbar.txtSymbol_pi": "Пи", "SSE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "SSE.Controllers.Toolbar.txtSymbol_pm": "Плюс минус", "SSE.Controllers.Toolbar.txtSymbol_propto": "Пропорционално на", "SSE.Controllers.Toolbar.txtSymbol_psi": "Пси", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Четвърти корен", "SSE.Controllers.Toolbar.txtSymbol_qed": "Край на доказателството", "SSE.Controllers.Toolbar.txtSymbol_rddots": "До дясната диагонална точка", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ро", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON>на стрелка", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Сигма", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Радик<PERSON><PERSON><PERSON><PERSON> знак", "SSE.Controllers.Toolbar.txtSymbol_tau": "Тау", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Следователно", "SSE.Controllers.Toolbar.txtSymbol_theta": "Тета", "SSE.Controllers.Toolbar.txtSymbol_times": "Знак за умножение", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Стрелка нагоре", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ипсилон", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Епсилон вариант", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Фи вариант", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Пи вариант", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ро вариант", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Сигма вариант", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Тета вариант", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Вертикална елипса", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Зета", "SSE.Controllers.Toolbar.warnLongOperation": "Операцията, която ще изпълните, може да отнеме доста време за изпълнение. <br> Наистина ли искате да продължите?", "SSE.Controllers.Toolbar.warnMergeLostData": "Само данните от горната лява клетка ще останат в обединената клетка. <br> Наистина ли искате да продължите?", "SSE.Controllers.Viewport.textFreezePanes": "Фиксирай прозорците", "SSE.Controllers.Viewport.textHideFBar": "Скриване на лентата за формули", "SSE.Controllers.Viewport.textHideGridlines": "Скриване на решетки", "SSE.Controllers.Viewport.textHideHeadings": "Скриване на заглавията", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Разширени настройки", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Персонализи<PERSON><PERSON>н филтър", "SSE.Views.AutoFilterDialog.textAddSelection": "Добавяне на текуща селекция за филтриране", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Заготовки}", "SSE.Views.AutoFilterDialog.textSelectAll": "Избери всички", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Изберете всички резултати от търсенето", "SSE.Views.AutoFilterDialog.textWarning": "Внимание", "SSE.Views.AutoFilterDialog.txtAboveAve": "Над средното", "SSE.Views.AutoFilterDialog.txtBegins": "Започва с...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Под средното", "SSE.Views.AutoFilterDialog.txtBetween": "Между ...", "SSE.Views.AutoFilterDialog.txtClear": "Изчисти", "SSE.Views.AutoFilterDialog.txtContains": "Съдържа...", "SSE.Views.AutoFilterDialog.txtEmpty": "Въведете клетъчен филтър", "SSE.Views.AutoFilterDialog.txtEnds": "Завършва със...", "SSE.Views.AutoFilterDialog.txtEquals": "Равно на...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Филтриране по цвят на клетките", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Филтриране по цвят на шрифта", "SSE.Views.AutoFilterDialog.txtGreater": "По-велик от...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "По-голяма или равна на ...", "SSE.Views.AutoFilterDialog.txtLess": "По-малко от...", "SSE.Views.AutoFilterDialog.txtLessEquals": "По-малко или равно на ...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Не започва с ...", "SSE.Views.AutoFilterDialog.txtNotContains": "Не съдържа...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Не завършва с ...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Не е равно ...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Филтър за номера", "SSE.Views.AutoFilterDialog.txtReapply": "Повторно", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Сортиране по цвят на клетките", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Сортиране по цвят на шрифта", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Сортиране по низходящ ред", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Сортиране във възходящ ред", "SSE.Views.AutoFilterDialog.txtTextFilter": "Текстов филтър", "SSE.Views.AutoFilterDialog.txtTitle": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.AutoFilterDialog.txtTop10": "Топ 10", "SSE.Views.AutoFilterDialog.warnNoSelected": "Трябва да изберете поне една стойност", "SSE.Views.CellEditor.textManager": "Мениджър на имена", "SSE.Views.CellEditor.tipFormula": "Вмъкване на функция", "SSE.Views.CellRangeDialog.errorMaxRows": "ГРЕШКА! Максималният брой поредици данни за диаграма е 255", "SSE.Views.CellRangeDialog.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Views.CellRangeDialog.txtEmpty": "Това поле е задължително", "SSE.Views.CellRangeDialog.txtInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.CellRangeDialog.txtTitle": "Изберете диапазон от данни", "SSE.Views.CellSettings.textAngle": "Ъгъл", "SSE.Views.CellSettings.textBackColor": "Цвят на фона", "SSE.Views.CellSettings.textBorderColor": "Цвят", "SSE.Views.CellSettings.textBorders": "Стил на границите", "SSE.Views.CellSettings.textColor": "Цветово пълнене", "SSE.Views.CellSettings.textForeground": "Цвят на преден план", "SSE.Views.CellSettings.textGradientColor": "Цвят", "SSE.Views.CellSettings.textOrientation": "Ориентация на текста", "SSE.Views.CellSettings.textPosition": "Позиция", "SSE.Views.CellSettings.textSelectBorders": "Изберете граници, които искате да промените, като използвате избрания по-горе стил", "SSE.Views.CellSettings.tipAll": "Задайте външната граница и всички вътрешни линии", "SSE.Views.CellSettings.tipBottom": "Задайте само външната долна граница", "SSE.Views.CellSettings.tipDiagD": "Задайте диагонална надолу граница", "SSE.Views.CellSettings.tipDiagU": "Задайте диагонална нагоре граница", "SSE.Views.CellSettings.tipInner": "Задайте само вътрешни линии", "SSE.Views.CellSettings.tipInnerHor": "Задайте само хоризонтални вътрешни линии", "SSE.Views.CellSettings.tipInnerVert": "Задайте само вертикални вътрешни линии", "SSE.Views.CellSettings.tipLeft": "Задайте само лявата граница", "SSE.Views.CellSettings.tipNone": "Без граници", "SSE.Views.CellSettings.tipOuter": "Задайте само външната граница", "SSE.Views.CellSettings.tipRight": "Задайте само външна дясна граница", "SSE.Views.CellSettings.tipTop": "Задайте само външна горна граница", "SSE.Views.ChartDataDialog.textDelete": "Премахване", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Име на серията", "SSE.Views.ChartSettings.strLineWeight": "Тегло на линията", "SSE.Views.ChartSettings.strSparkColor": "Цвят", "SSE.Views.ChartSettings.strTemplate": "Шабл<PERSON>н", "SSE.Views.ChartSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ChartSettings.textBorderSizeErr": "Въведената стойност е неправилна. <br> Въведете стойност между 0 pt и 1584 pt.", "SSE.Views.ChartSettings.textChartType": "Промяна на типа на диаграмата", "SSE.Views.ChartSettings.textEditData": "Редактиране на данни и местоположение", "SSE.Views.ChartSettings.textFirstPoint": "Първа точка", "SSE.Views.ChartSettings.textHeight": "Висо<PERSON>ина", "SSE.Views.ChartSettings.textHighPoint": "Висока точка", "SSE.Views.ChartSettings.textKeepRatio": "Постоянни пропорции", "SSE.Views.ChartSettings.textLastPoint": "Последна точка", "SSE.Views.ChartSettings.textLowPoint": "Ниска точка", "SSE.Views.ChartSettings.textMarkers": "Маркери", "SSE.Views.ChartSettings.textNegativePoint": "Отрицателна точка", "SSE.Views.ChartSettings.textRanges": "Диапазон на данните", "SSE.Views.ChartSettings.textSelectData": "Изберете данни", "SSE.Views.ChartSettings.textShow": "Пока<PERSON>и", "SSE.Views.ChartSettings.textSize": "Размер", "SSE.Views.ChartSettings.textStyle": "Стил", "SSE.Views.ChartSettings.textType": "Тип", "SSE.Views.ChartSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ГРЕШКА! Максималният брой точки в серия на графиката е 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "Максимален брой поредици данни за диаграма е 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Views.ChartSettingsDlg.textAlt": "Алтернативен текст", "SSE.Views.ChartSettingsDlg.textAltDescription": "Описание", "SSE.Views.ChartSettingsDlg.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, автосистемата, диаграмата или таблицата.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Заглавие", "SSE.Views.ChartSettingsDlg.textAuto": "Автоматичен", "SSE.Views.ChartSettingsDlg.textAutoEach": "Авто за всеки", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Ос Кръстове", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Опции за ос", "SSE.Views.ChartSettingsDlg.textAxisPos": "Позиция на ос", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Настройки на ос", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Между отметки с отметки", "SSE.Views.ChartSettingsDlg.textBillions": "Мил<PERSON><PERSON><PERSON><PERSON>и", "SSE.Views.ChartSettingsDlg.textBottom": "Отдоло", "SSE.Views.ChartSettingsDlg.textCategoryName": "Име на категория", "SSE.Views.ChartSettingsDlg.textCenter": "Център", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Елементи на диаграмата & <br> Легенда на диаграмата", "SSE.Views.ChartSettingsDlg.textChartTitle": "Заглавие на диаграмата", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON>р<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.ChartSettingsDlg.textDataColumns": "в колони", "SSE.Views.ChartSettingsDlg.textDataLabels": "Етикети за данни", "SSE.Views.ChartSettingsDlg.textDataRows": "в редове", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Легенда на дисплея", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Скрити и празни клетки", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Свържете данни с линия", "SSE.Views.ChartSettingsDlg.textFit": "Поставя се в ширина", "SSE.Views.ChartSettingsDlg.textFixed": "Определен", "SSE.Views.ChartSettingsDlg.textGaps": "Пропуски", "SSE.Views.ChartSettingsDlg.textGridLines": "Мрежови линии", "SSE.Views.ChartSettingsDlg.textGroup": "Група Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Скрий", "SSE.Views.ChartSettingsDlg.textHigh": "Висок", "SSE.Views.ChartSettingsDlg.textHorAxis": "Хоризонтална ос", "SSE.Views.ChartSettingsDlg.textHorizontal": "Хоризонтален", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Стотици", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "В", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Вътрешно дъно", "SSE.Views.ChartSettingsDlg.textInnerTop": "Вътрешен връх", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.ChartSettingsDlg.textLabelDist": "Разстояние на етикета на оста", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Интервал между етикети", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Опции за етикети", "SSE.Views.ChartSettingsDlg.textLabelPos": "Позиция на етикета", "SSE.Views.ChartSettingsDlg.textLayout": "Оформление", "SSE.Views.ChartSettingsDlg.textLeft": "Наляво", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Ляво наслагване", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Отдоло", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Наляво", "SSE.Views.ChartSettingsDlg.textLegendPos": "Легенда", "SSE.Views.ChartSettingsDlg.textLegendRight": "Прав", "SSE.Views.ChartSettingsDlg.textLegendTop": "Отгоре", "SSE.Views.ChartSettingsDlg.textLines": "Линии", "SSE.Views.ChartSettingsDlg.textLocationRange": "Обхват на местоположението", "SSE.Views.ChartSettingsDlg.textLow": "Нисък", "SSE.Views.ChartSettingsDlg.textMajor": "Гол<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Майор и Мала", "SSE.Views.ChartSettingsDlg.textMajorType": "Основен тип", "SSE.Views.ChartSettingsDlg.textManual": "Наръчник", "SSE.Views.ChartSettingsDlg.textMarkers": "Маркери", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Интервал между знаците", "SSE.Views.ChartSettingsDlg.textMaxValue": "Максима<PERSON>на стойност", "SSE.Views.ChartSettingsDlg.textMillions": "Мили<PERSON><PERSON>и", "SSE.Views.ChartSettingsDlg.textMinor": "Незначителен", "SSE.Views.ChartSettingsDlg.textMinorType": "Малък тип", "SSE.Views.ChartSettingsDlg.textMinValue": "Мини<PERSON><PERSON><PERSON><PERSON> стойност", "SSE.Views.ChartSettingsDlg.textNextToAxis": "До ос", "SSE.Views.ChartSettingsDlg.textNone": "Нито един", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Няма наслагване", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Отбелязани марки", "SSE.Views.ChartSettingsDlg.textOut": "От", "SSE.Views.ChartSettingsDlg.textOuterTop": "Вън<PERSON>ен връх", "SSE.Views.ChartSettingsDlg.textOverlay": "Настилка", "SSE.Views.ChartSettingsDlg.textReverse": "Стойности в обратен ред", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Обратен ред", "SSE.Views.ChartSettingsDlg.textRight": "Прав", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Право наслагване", "SSE.Views.ChartSettingsDlg.textRotated": "Въртя", "SSE.Views.ChartSettingsDlg.textSameAll": "Същото е за всички", "SSE.Views.ChartSettingsDlg.textSelectData": "Изберете данни", "SSE.Views.ChartSettingsDlg.textSeparator": "Сепаратор за етикети за данни", "SSE.Views.ChartSettingsDlg.textSeriesName": "Име на серията", "SSE.Views.ChartSettingsDlg.textShow": "Пока<PERSON>и", "SSE.Views.ChartSettingsDlg.textShowBorders": "Показва граници на диаграмата", "SSE.Views.ChartSettingsDlg.textShowData": "Показване на данни в скрити редове и колони", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Показване на празни клетки като", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Показване на ос", "SSE.Views.ChartSettingsDlg.textShowValues": "Показване на стойностите на диаграмата", "SSE.Views.ChartSettingsDlg.textSingle": "Единична Sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "Гл<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Спарклайн диапазони", "SSE.Views.ChartSettingsDlg.textStraight": "Направо", "SSE.Views.ChartSettingsDlg.textStyle": "Стил", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Хиляди", "SSE.Views.ChartSettingsDlg.textTickOptions": "Отметнете опции", "SSE.Views.ChartSettingsDlg.textTitle": "Диаграма - Разширени настройки", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Спарклайн - Разширени настройки", "SSE.Views.ChartSettingsDlg.textTop": "Отгоре", "SSE.Views.ChartSettingsDlg.textTrillions": "Трилиони", "SSE.Views.ChartSettingsDlg.textType": "Тип", "SSE.Views.ChartSettingsDlg.textTypeData": "Тип & данни", "SSE.Views.ChartSettingsDlg.textUnits": "Дисплейни единици", "SSE.Views.ChartSettingsDlg.textValue": "Стойност", "SSE.Views.ChartSettingsDlg.textVertAxis": "Вертикална ос", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X Заглавие на ос", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Заглавие на ос", "SSE.Views.ChartSettingsDlg.textZero": "Нула", "SSE.Views.ChartSettingsDlg.txtEmpty": "Това поле е задължително", "SSE.Views.ChartTypeDialog.textSeries": "Серия", "SSE.Views.DataTab.capBtnGroup": "Гру<PERSON>а", "SSE.Views.DataTab.capBtnTextToCol": "Текст в колони", "SSE.Views.DataTab.capBtnUngroup": "Разгрупира", "SSE.Views.DataValidationDialog.strSettings": "Настройки", "SSE.Views.DataValidationDialog.textAllow": "Позволява", "SSE.Views.DataValidationDialog.textData": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textFormula": "Формула", "SSE.Views.DataValidationDialog.textMax": "Максимален", "SSE.Views.DataValidationDialog.textMessage": "Съобщение", "SSE.Views.DigitalFilterDialog.capAnd": "И", "SSE.Views.DigitalFilterDialog.capCondition1": "равно на", "SSE.Views.DigitalFilterDialog.capCondition10": "не завършва с", "SSE.Views.DigitalFilterDialog.capCondition11": "съдържа", "SSE.Views.DigitalFilterDialog.capCondition12": "не съдържа", "SSE.Views.DigitalFilterDialog.capCondition2": "не е равно", "SSE.Views.DigitalFilterDialog.capCondition3": "е по-голяма от", "SSE.Views.DigitalFilterDialog.capCondition4": "е по-голямо или равно на", "SSE.Views.DigitalFilterDialog.capCondition5": "е по-малко от", "SSE.Views.DigitalFilterDialog.capCondition6": "е по-малко или равно на", "SSE.Views.DigitalFilterDialog.capCondition7": "започва с", "SSE.Views.DigitalFilterDialog.capCondition8": "не започва с", "SSE.Views.DigitalFilterDialog.capCondition9": "завършва със", "SSE.Views.DigitalFilterDialog.capOr": "Или", "SSE.Views.DigitalFilterDialog.textNoFilter": "без филтър", "SSE.Views.DigitalFilterDialog.textShowRows": "Показване на редове, където", "SSE.Views.DigitalFilterDialog.textUse1": "Използвате ли? да представи всеки един символ", "SSE.Views.DigitalFilterDialog.textUse2": "Използвайте *, за да представите всяка серия от характер", "SSE.Views.DigitalFilterDialog.txtTitle": "Персонализи<PERSON><PERSON>н филтър", "SSE.Views.DocumentHolder.advancedImgText": "Разширени настройки на изображението", "SSE.Views.DocumentHolder.advancedShapeText": "Разширени настройки за формата", "SSE.Views.DocumentHolder.bottomCellText": "Подравняване отдолу", "SSE.Views.DocumentHolder.bulletsText": "Маркери и номериране", "SSE.Views.DocumentHolder.centerCellText": "Подравняване на средата", "SSE.Views.DocumentHolder.chartText": "Разширени настройки на диаграмата", "SSE.Views.DocumentHolder.deleteColumnText": "Колона", "SSE.Views.DocumentHolder.deleteRowText": "Ред", "SSE.Views.DocumentHolder.deleteTableText": "Таблица", "SSE.Views.DocumentHolder.direct270Text": "Завъртете текста нагоре", "SSE.Views.DocumentHolder.direct90Text": "Завъртете текста надолу", "SSE.Views.DocumentHolder.directHText": "Хоризонтален", "SSE.Views.DocumentHolder.directionText": "Текстова посока", "SSE.Views.DocumentHolder.editChartText": "Редактиране на данни", "SSE.Views.DocumentHolder.editHyperlinkText": "Редактиране на хипервръзка", "SSE.Views.DocumentHolder.insertColumnLeftText": "Колона вляво", "SSE.Views.DocumentHolder.insertColumnRightText": "Колона вдясно", "SSE.Views.DocumentHolder.insertRowAboveText": "Ред по-горе", "SSE.Views.DocumentHolder.insertRowBelowText": "Ред по-долу", "SSE.Views.DocumentHolder.originalSizeText": "Размер по подразбиране", "SSE.Views.DocumentHolder.removeHyperlinkText": "Премахване на хипервръзка", "SSE.Views.DocumentHolder.selectColumnText": "Цяла колона", "SSE.Views.DocumentHolder.selectDataText": "Данни в колоната", "SSE.Views.DocumentHolder.selectRowText": "Ред", "SSE.Views.DocumentHolder.selectTableText": "Таблица", "SSE.Views.DocumentHolder.strDelete": "Премахване на подпис", "SSE.Views.DocumentHolder.strDetails": "Подробности за подпис", "SSE.Views.DocumentHolder.strSetup": "Настройка на подпис", "SSE.Views.DocumentHolder.strSign": "Знак", "SSE.Views.DocumentHolder.textAlign": "Изравнете", "SSE.Views.DocumentHolder.textArrange": "Подредете", "SSE.Views.DocumentHolder.textArrangeBack": "Изпращане до фона", "SSE.Views.DocumentHolder.textArrangeBackward": "Изпращане назад", "SSE.Views.DocumentHolder.textArrangeForward": "Изведи напред", "SSE.Views.DocumentHolder.textArrangeFront": "Доведете до преден план", "SSE.Views.DocumentHolder.textCrop": "Изрежете", "SSE.Views.DocumentHolder.textCropFill": "Напълнете", "SSE.Views.DocumentHolder.textCropFit": "Регулирате", "SSE.Views.DocumentHolder.textEntriesList": "Изберете от падащия списък", "SSE.Views.DocumentHolder.textFlipH": "Отрязва по хоризонтала", "SSE.Views.DocumentHolder.textFlipV": "Отрязване по вертикала", "SSE.Views.DocumentHolder.textFreezePanes": "Фиксирай прозорците", "SSE.Views.DocumentHolder.textFromFile": "От файл ", "SSE.Views.DocumentHolder.textFromUrl": "От URL", "SSE.Views.DocumentHolder.textMoreFormats": "Още формати", "SSE.Views.DocumentHolder.textNone": "Нито един", "SSE.Views.DocumentHolder.textReplace": "Заменете изображението", "SSE.Views.DocumentHolder.textRotate": "Завъртане", "SSE.Views.DocumentHolder.textRotate270": "Завъртете на 90 ° обратно на часовниковата стрелка", "SSE.Views.DocumentHolder.textRotate90": "Завъртете на 90 ° по посока на часовниковата стрелка", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Подравняване отдолу", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Подравняване на центъра", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Подравняване вляво", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Подравняване на средата", "SSE.Views.DocumentHolder.textShapeAlignRight": "Подравняване надясно", "SSE.Views.DocumentHolder.textShapeAlignTop": "Подравняване отгоре", "SSE.Views.DocumentHolder.textUndo": "Отмени", "SSE.Views.DocumentHolder.textUnFreezePanes": "Размразете панелите", "SSE.Views.DocumentHolder.topCellText": "Подравняване отгоре", "SSE.Views.DocumentHolder.txtAccounting": "Счетоводство", "SSE.Views.DocumentHolder.txtAddComment": "Добави коментар", "SSE.Views.DocumentHolder.txtAddNamedRange": "Определяне на име", "SSE.Views.DocumentHolder.txtArrange": "Подредете", "SSE.Views.DocumentHolder.txtAscending": "Възходящ", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Автоматично монтиране на ширината на колоната", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Автоматично монтиране на височината на ред", "SSE.Views.DocumentHolder.txtClear": "Изчисти", "SSE.Views.DocumentHolder.txtClearAll": "Всички", "SSE.Views.DocumentHolder.txtClearComments": "Коментари", "SSE.Views.DocumentHolder.txtClearFormat": "Формат", "SSE.Views.DocumentHolder.txtClearHyper": "Хипервръзки", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Изчистване на избраните групи на Sparkline", "SSE.Views.DocumentHolder.txtClearSparklines": "Изчистване на избраните искрящи линии", "SSE.Views.DocumentHolder.txtClearText": "Текст", "SSE.Views.DocumentHolder.txtColumn": "Цяла колона", "SSE.Views.DocumentHolder.txtColumnWidth": "Задайте ширина на колоната", "SSE.Views.DocumentHolder.txtCopy": "Копие", "SSE.Views.DocumentHolder.txtCurrency": "Валута", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Ширина на персонализираната колона", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Височина по избор", "SSE.Views.DocumentHolder.txtCut": "Разрез", "SSE.Views.DocumentHolder.txtDate": "Дата", "SSE.Views.DocumentHolder.txtDelete": "Изтрий", "SSE.Views.DocumentHolder.txtDescending": "Низходящо", "SSE.Views.DocumentHolder.txtDistribHor": "Разпределете хоризонтално", "SSE.Views.DocumentHolder.txtDistribVert": "Разпределете вертикално", "SSE.Views.DocumentHolder.txtEditComment": "Редактиране на коментара", "SSE.Views.DocumentHolder.txtFilter": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.DocumentHolder.txtFilterCellColor": "Филтриране по цвят на клетката", "SSE.Views.DocumentHolder.txtFilterFontColor": "Филтриране по цвят на шрифта", "SSE.Views.DocumentHolder.txtFilterValue": "Филтриране по стойност на избраната клетка", "SSE.Views.DocumentHolder.txtFormula": "Вмъкване на функция", "SSE.Views.DocumentHolder.txtFraction": "Фракция", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGroup": "Гру<PERSON>а", "SSE.Views.DocumentHolder.txtHide": "Скрий", "SSE.Views.DocumentHolder.txtInsert": "Вмъкни", "SSE.Views.DocumentHolder.txtInsHyperlink": "Хипервръзка", "SSE.Views.DocumentHolder.txtNumber": "Номер", "SSE.Views.DocumentHolder.txtNumFormat": "Формат на номера", "SSE.Views.DocumentHolder.txtPaste": "Паста", "SSE.Views.DocumentHolder.txtPercentage": "Процент", "SSE.Views.DocumentHolder.txtReapply": "Повторно", "SSE.Views.DocumentHolder.txtRow": "Цял ред", "SSE.Views.DocumentHolder.txtRowHeight": "Задайте височина на редовете", "SSE.Views.DocumentHolder.txtScientific": "Научен", "SSE.Views.DocumentHolder.txtSelect": "Изберете", "SSE.Views.DocumentHolder.txtShiftDown": "Преместете клетките надолу", "SSE.Views.DocumentHolder.txtShiftLeft": "Преместване на клетките вляво", "SSE.Views.DocumentHolder.txtShiftRight": "Преместете клетките надясно", "SSE.Views.DocumentHolder.txtShiftUp": "Преместете клетки нагоре", "SSE.Views.DocumentHolder.txtShow": "Пока<PERSON>и", "SSE.Views.DocumentHolder.txtShowComment": "Показване на коментара", "SSE.Views.DocumentHolder.txtSort": "Вид", "SSE.Views.DocumentHolder.txtSortCellColor": "Избрани цветове на клетката отгоре", "SSE.Views.DocumentHolder.txtSortFontColor": "Цвят на избрания шрифт отгоре", "SSE.Views.DocumentHolder.txtSparklines": "Блещукащи", "SSE.Views.DocumentHolder.txtText": "Текст", "SSE.Views.DocumentHolder.txtTextAdvanced": "Разширени настройки на текста", "SSE.Views.DocumentHolder.txtTime": "Път", "SSE.Views.DocumentHolder.txtUngroup": "Разгрупира", "SSE.Views.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.vertAlignText": "Вертикално подравняване", "SSE.Views.FileMenu.btnBackCaption": "Mестоположението на файла", "SSE.Views.FileMenu.btnCloseMenuCaption": "Затваряне на менюто", "SSE.Views.FileMenu.btnCreateNewCaption": "Създай нов", "SSE.Views.FileMenu.btnDownloadCaption": "Изтеглете като", "SSE.Views.FileMenu.btnHelpCaption": "Помощ", "SSE.Views.FileMenu.btnInfoCaption": "Информация за електронна таблица", "SSE.Views.FileMenu.btnPrintCaption": "Печат", "SSE.Views.FileMenu.btnProtectCaption": "Защитавам", "SSE.Views.FileMenu.btnRecentFilesCaption": "Отваряне на последните", "SSE.Views.FileMenu.btnRenameCaption": "Преименуване", "SSE.Views.FileMenu.btnReturnCaption": "Назад към електронна таблица", "SSE.Views.FileMenu.btnRightsCaption": "Права за достъп", "SSE.Views.FileMenu.btnSaveAsCaption": "Запази като", "SSE.Views.FileMenu.btnSaveCaption": "Запази", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Запазване на копието като", "SSE.Views.FileMenu.btnSettingsCaption": "Разширени настройки", "SSE.Views.FileMenu.btnToEditCaption": "Редактиране на електронна таблица", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Прило<PERSON>и", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Добави автор", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Приложение", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Промяна на правата за достъп", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Местоположение", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON>и<PERSON><PERSON>, които имат права", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Заглавие на електронната таблица", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Промяна на правата за достъп", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON>и<PERSON><PERSON>, които имат права", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Прило<PERSON>и", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Режим на съвместно редактиране", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Подказване на шрифт", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Език на формулата", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Пример: SUM; MIN; MAX; БРОЯ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Регионални настройки", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Пример: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Стриктен", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Единица за измерване", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Стойност на мащаба по подразбиране", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "На всеки 10 минути", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "На всеки 30 минути", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "На всеки 5 минути", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Всеки час", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Автовъзстановяване", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Автоматично записване", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Хора с увреждания", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Запазване в сървър", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Всяка минута", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Рефе<PERSON><PERSON><PERSON><PERSON><PERSON>н стил", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Санти<PERSON>етър", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Немски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Английски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Испански", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Френски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Италиански", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "като OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Ме<PERSON><PERSON><PERSON>н", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Полски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Точка", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Руски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "като Windows", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Внимание", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "С парола", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Защитете електронната таблица", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "С подпис", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Редактирайте електронната таблица", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Редактирането ще премахне подписите от електронната таблица. <br> Наистина ли искате да продължите?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Тази електронна таблица е защитена с парола", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Тази електронна таблица трябва да бъде подписана.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "В електронната таблица са добавени валидни подписи. Електронната таблица е защитена от редактиране.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Някои от цифровите подписи в електронната таблица са невалидни или не можаха да бъдат потвърдени. Електронната таблица е защитена от редактиране.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Преглед на подписи", "SSE.Views.FormatRulesEditDlg.fillColor": "Цвят на запълване", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Всички граници", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Автоматичен", "SSE.Views.FormatRulesEditDlg.textColor": "Цвят на текста", "SSE.Views.FormatRulesEditDlg.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.FormatRulesEditDlg.textFormat": "Формат", "SSE.Views.FormatRulesEditDlg.textFormula": "Формула", "SSE.Views.FormatRulesEditDlg.textMaximum": "Максимален", "SSE.Views.FormatRulesEditDlg.textNewColor": "Нов Потребителски Цвят", "SSE.Views.FormatRulesEditDlg.textPosition": "Позиция", "SSE.Views.FormatRulesEditDlg.txtFraction": "Фракция", "SSE.Views.FormatRulesManagerDlg.guestText": "Гост", "SSE.Views.FormatRulesManagerDlg.textDelete": "Изтрий", "SSE.Views.FormatRulesManagerDlg.textFormat": "Формат", "SSE.Views.FormatSettingsDialog.textCategory": "Категория", "SSE.Views.FormatSettingsDialog.textDecimal": "Десетичен", "SSE.Views.FormatSettingsDialog.textFormat": "Формат", "SSE.Views.FormatSettingsDialog.textSeparator": "Използвайте 1000 разделител", "SSE.Views.FormatSettingsDialog.textSymbols": "Символи", "SSE.Views.FormatSettingsDialog.textTitle": "Формат на номера", "SSE.Views.FormatSettingsDialog.txtAccounting": "Счетоводство", "SSE.Views.FormatSettingsDialog.txtAs10": "Като десети (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Като стотни (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Като шестнайсети (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Като половинки (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Като четвърти (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Като осми (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Валута", "SSE.Views.FormatSettingsDialog.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.FormatSettingsDialog.txtDate": "Дата", "SSE.Views.FormatSettingsDialog.txtFraction": "Фракция", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "Нито един", "SSE.Views.FormatSettingsDialog.txtNumber": "Номер", "SSE.Views.FormatSettingsDialog.txtPercentage": "Процент", "SSE.Views.FormatSettingsDialog.txtSample": "Извадка:", "SSE.Views.FormatSettingsDialog.txtScientific": "Научен", "SSE.Views.FormatSettingsDialog.txtText": "Текст", "SSE.Views.FormatSettingsDialog.txtTime": "Път", "SSE.Views.FormatSettingsDialog.txtUpto1": "До една цифра (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "До две цифри (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "До три цифри (131/135)", "SSE.Views.FormulaDialog.sDescription": "Описание", "SSE.Views.FormulaDialog.textGroupDescription": "Изберете функционална група", "SSE.Views.FormulaDialog.textListDescription": "Изберете функция", "SSE.Views.FormulaDialog.txtTitle": "Вмъкване на функция", "SSE.Views.FormulaTab.textAutomatic": "Автоматичен", "SSE.Views.FormulaTab.txtAdditional": "Допълнителен", "SSE.Views.FormulaTab.txtCalculation": "Изчисление", "SSE.Views.FormulaTab.txtFormula": "Функция", "SSE.Views.FormulaWizard.textFunction": "Функция", "SSE.Views.FormulaWizard.textLogical": "логичен", "SSE.Views.HeaderFooterDialog.textColor": "Цвят на текста", "SSE.Views.HeaderFooterDialog.textEven": "Дори страница", "SSE.Views.HeaderFooterDialog.textInsert": "Вмъкни", "SSE.Views.HeaderFooterDialog.textNewColor": "Нов Потребителски Цвят", "SSE.Views.HeaderFooterDialog.tipFontSize": "Размер на шрифта", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Пок<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Връзка към", "SSE.Views.HyperlinkSettingsDialog.strRange": "Диа<PERSON>азон", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Избрана област", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Въведете надпис тук", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Въведете връзката тук", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Въведете подсказка тук", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Вън<PERSON><PERSON>н линк", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Вътрешен диапазон от данни", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Текст на екрана", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Настройки за хипервръзки", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Това поле е задължително", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Това поле трябва да е URL адрес във формат \"http://www.example.com\"", "SSE.Views.ImageSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ImageSettings.textCrop": "Изрежете", "SSE.Views.ImageSettings.textCropFill": "Напълнете", "SSE.Views.ImageSettings.textCropFit": "Регулирате", "SSE.Views.ImageSettings.textEdit": "Редактиране", "SSE.Views.ImageSettings.textEditObject": "Редактиране на обект", "SSE.Views.ImageSettings.textFlip": "Отразява", "SSE.Views.ImageSettings.textFromFile": "От файл ", "SSE.Views.ImageSettings.textFromUrl": "От URL", "SSE.Views.ImageSettings.textHeight": "Висо<PERSON>ина", "SSE.Views.ImageSettings.textHint270": "Завъртете на 90 ° обратно на часовниковата стрелка", "SSE.Views.ImageSettings.textHint90": "Завъртете на 90 ° по посока на часовниковата стрелка", "SSE.Views.ImageSettings.textHintFlipH": "Отрязва по хоризонтала", "SSE.Views.ImageSettings.textHintFlipV": "Отрязване по вертикала", "SSE.Views.ImageSettings.textInsert": "Замяна на изображението", "SSE.Views.ImageSettings.textKeepRatio": "Постоянни пропорции", "SSE.Views.ImageSettings.textOriginalSize": "Размер по подразбиране", "SSE.Views.ImageSettings.textRotate90": "Завъртане на 90 °", "SSE.Views.ImageSettings.textRotation": "Завъртане", "SSE.Views.ImageSettings.textSize": "Размер", "SSE.Views.ImageSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAlt": "Алтернативен текст", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, автосистемата, диаграмата или таблицата.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Заглавие", "SSE.Views.ImageSettingsAdvanced.textAngle": "Ъгъл", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Огледален", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Хоризонтално", "SSE.Views.ImageSettingsAdvanced.textRotation": "Завъртане", "SSE.Views.ImageSettingsAdvanced.textTitle": "Изображение - Разширени настройки", "SSE.Views.ImageSettingsAdvanced.textVertically": "Вертикално", "SSE.Views.LeftMenu.tipAbout": "Относно", "SSE.Views.LeftMenu.tipChat": "Чат", "SSE.Views.LeftMenu.tipComments": "Коментари", "SSE.Views.LeftMenu.tipFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Добавки", "SSE.Views.LeftMenu.tipSearch": "Търсене", "SSE.Views.LeftMenu.tipSupport": "Обратна връзка и поддръжка", "SSE.Views.LeftMenu.txtDeveloper": "РЕЖИМ ЗА ПРОГРАМИСТИ", "SSE.Views.LeftMenu.txtTrial": "ПРОВЕРКА", "SSE.Views.MainSettingsPrint.okButtonText": "Запази", "SSE.Views.MainSettingsPrint.strBottom": "Отдоло", "SSE.Views.MainSettingsPrint.strLandscape": "Пей<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "Наляво", "SSE.Views.MainSettingsPrint.strMargins": "Полета", "SSE.Views.MainSettingsPrint.strPortrait": "Портрет", "SSE.Views.MainSettingsPrint.strPrint": "Печат", "SSE.Views.MainSettingsPrint.strRight": "Прав", "SSE.Views.MainSettingsPrint.strTop": "Отгоре", "SSE.Views.MainSettingsPrint.textActualSize": "Действителен размер", "SSE.Views.MainSettingsPrint.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.MainSettingsPrint.textFitCols": "Поставете всички колони на една страница", "SSE.Views.MainSettingsPrint.textFitPage": "Поставете лист на една страница", "SSE.Views.MainSettingsPrint.textFitRows": "Поставете всички редове на една страница", "SSE.Views.MainSettingsPrint.textPageOrientation": "Ориентация на страницата", "SSE.Views.MainSettingsPrint.textPageScaling": "Мащабиране", "SSE.Views.MainSettingsPrint.textPageSize": "Размер на страницата", "SSE.Views.MainSettingsPrint.textPrintGrid": "Печат на решетки", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Печат заглавия на редове и колони", "SSE.Views.MainSettingsPrint.textSettings": "Настройки за", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Съществуващите имена на обхвати не могат да бъдат редактирани и новите не могат да бъдат създадени <br> в момента, тъй като някои от тях се редактират.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Определено име", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Внимание", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Работна книга", "SSE.Views.NamedRangeEditDlg.textDataRange": "Диапазон на данните", "SSE.Views.NamedRangeEditDlg.textExistName": "ГРЕШКА! Обхватът с такова име вече съществува", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Името трябва да започва с буква или долна черта и не трябва да съдържа невалидни знаци.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ГРЕШКА! Този елемент се редактира от друг потребител.", "SSE.Views.NamedRangeEditDlg.textName": "Име", "SSE.Views.NamedRangeEditDlg.textReservedName": "Името, което се опитвате да използвате, вече е посочено в клетъчни формули. Моля, използвайте друго име.", "SSE.Views.NamedRangeEditDlg.textScope": "Об<PERSON><PERSON><PERSON>т", "SSE.Views.NamedRangeEditDlg.textSelectData": "Изберете данни", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Това поле е задължително", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Редактиране на името", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Ново име", "SSE.Views.NamedRangePasteDlg.textNames": "Наименувани диапазони", "SSE.Views.NamedRangePasteDlg.txtTitle": "Поставяне на името", "SSE.Views.NameManagerDlg.closeButtonText": "Затвори", "SSE.Views.NameManagerDlg.guestText": "Гост", "SSE.Views.NameManagerDlg.textDataRange": "Диапазон на данните", "SSE.Views.NameManagerDlg.textDelete": "Изтрий", "SSE.Views.NameManagerDlg.textEdit": "Редактиране", "SSE.Views.NameManagerDlg.textEmpty": "Все още не са създадени имена на диапазони. <br> Създайте поне един именуван диапазон и той ще се появи в това поле.", "SSE.Views.NameManagerDlg.textFilter": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.NameManagerDlg.textFilterAll": "Всички", "SSE.Views.NameManagerDlg.textFilterDefNames": "Определени имена", "SSE.Views.NameManagerDlg.textFilterSheet": "Имената, включени в листа", "SSE.Views.NameManagerDlg.textFilterTableNames": "Имената на таблиците", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Им<PERSON><PERSON>, включени в работна книга", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Няма намерени диапазони, които да отговарят на филтъра ви.", "SSE.Views.NameManagerDlg.textRanges": "Наименувани диапазони", "SSE.Views.NameManagerDlg.textScope": "Об<PERSON><PERSON><PERSON>т", "SSE.Views.NameManagerDlg.textWorkbook": "Работна книга", "SSE.Views.NameManagerDlg.tipIsLocked": "Този елемент се редактира от друг потребител.", "SSE.Views.NameManagerDlg.txtTitle": "Мениджър на имена", "SSE.Views.PageMarginsDialog.textBottom": "Отдоло", "SSE.Views.PageMarginsDialog.textLeft": "Наляво", "SSE.Views.PageMarginsDialog.textRight": "Прав", "SSE.Views.PageMarginsDialog.textTitle": "Полета", "SSE.Views.PageMarginsDialog.textTop": "Отгоре", "SSE.Views.ParagraphSettings.strLineHeight": "Интервал между редовете", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Разстояние между абзаците", "SSE.Views.ParagraphSettings.strSpacingAfter": "След", "SSE.Views.ParagraphSettings.strSpacingBefore": "Преди", "SSE.Views.ParagraphSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ParagraphSettings.textAt": "При", "SSE.Views.ParagraphSettings.textAtLeast": "Поне", "SSE.Views.ParagraphSettings.textAuto": "Многократни", "SSE.Views.ParagraphSettings.textExact": "Точно", "SSE.Views.ParagraphSettings.txtAutoText": "Автоматичен", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Посочените раздели ще се появят в това поле", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Всички шапки", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Двойно зачертаване", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Наляво", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Прав", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Отстъп и разположение", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Малки букви", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Зачеркнато", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "До<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Горен индекс", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Табуляция", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Подравняване", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Разстояние между знаците", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Разделът по подразбиране", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Ефекти", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Точно", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Премахване", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Премахнете всички", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Посочете", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Център", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Наляво", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Позиция на раздела", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Прав", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Параграф - Разширени настройки", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Автоматичен", "SSE.Views.PivotGroupDialog.textAuto": "Автоматичен", "SSE.Views.PivotSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.PivotSettings.textColumns": "Колони", "SSE.Views.PivotSettings.textFields": "Изберете полета", "SSE.Views.PivotSettings.textFilters": "Филтри", "SSE.Views.PivotSettings.textRows": "Редове", "SSE.Views.PivotSettings.textValues": "Стойности", "SSE.Views.PivotSettings.txtAddColumn": "Добавяне към колони", "SSE.Views.PivotSettings.txtAddFilter": "Добавяне към филтри", "SSE.Views.PivotSettings.txtAddRow": "Добавяне към редове", "SSE.Views.PivotSettings.txtAddValues": "Добавяне към стойности", "SSE.Views.PivotSettings.txtFieldSettings": "Настройки на полето", "SSE.Views.PivotSettings.txtMoveBegin": "Преместване в началото", "SSE.Views.PivotSettings.txtMoveColumn": "Преместване в колони", "SSE.Views.PivotSettings.txtMoveDown": "Премести надолу", "SSE.Views.PivotSettings.txtMoveEnd": "Преместване към край", "SSE.Views.PivotSettings.txtMoveFilter": "Преместване в филтри", "SSE.Views.PivotSettings.txtMoveRow": "Преместване в редове", "SSE.Views.PivotSettings.txtMoveUp": "Премести нагоре", "SSE.Views.PivotSettings.txtMoveValues": "Преместване към стойности", "SSE.Views.PivotSettings.txtRemove": "Премахване на полето", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, автосистемата, диаграмата или таблицата.", "SSE.Views.PivotTable.capBlankRows": "Празни редове", "SSE.Views.PivotTable.capGrandTotals": "Големи суми", "SSE.Views.PivotTable.capLayout": "Оформление на отчета", "SSE.Views.PivotTable.capSubtotals": "Междинните суми", "SSE.Views.PivotTable.mniBottomSubtotals": "Показване на всички междинни суми в долната част на групата", "SSE.Views.PivotTable.mniInsertBlankLine": "Вмъкване на празен ред след всяка позиция", "SSE.Views.PivotTable.mniLayoutCompact": "Показване в компактен формуляр", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Не повтаряйте всички етикети на артикули", "SSE.Views.PivotTable.mniLayoutOutline": "Показване в очертана форма", "SSE.Views.PivotTable.mniLayoutRepeat": "Повторете всички етикети на елемента", "SSE.Views.PivotTable.mniLayoutTabular": "Показване в таблична форма", "SSE.Views.PivotTable.mniNoSubtotals": "Не показвайте междинни суми", "SSE.Views.PivotTable.mniOffTotals": "Изкл. за редове и колони", "SSE.Views.PivotTable.mniOnColumnsTotals": "Само за колони", "SSE.Views.PivotTable.mniOnRowsTotals": "Само за редове", "SSE.Views.PivotTable.mniOnTotals": "Включено за редове и колони", "SSE.Views.PivotTable.mniRemoveBlankLine": "Премахване на празната линия след всяка позиция", "SSE.Views.PivotTable.mniTopSubtotals": "Покажи всички междинни суми в началото на групата", "SSE.Views.PivotTable.textColBanded": "Колони с ленти", "SSE.Views.PivotTable.textColHeader": "Заглавия на колони", "SSE.Views.PivotTable.textRowBanded": "Обвързани редове", "SSE.Views.PivotTable.textRowHeader": "Заглавия на редове", "SSE.Views.PivotTable.tipCreatePivot": "Вмъкване на обобщена таблица", "SSE.Views.PivotTable.tipGrandTotals": "Показване или скриване на общите суми", "SSE.Views.PivotTable.tipRefresh": "Актуализирайте информацията от източника на данни", "SSE.Views.PivotTable.tipSelect": "Изберете цялата върхова таблица", "SSE.Views.PivotTable.tipSubtotals": "Показване или скриване на междинни суми", "SSE.Views.PivotTable.txtCreate": "Вмъкване на таблица", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.PivotTable.txtRefresh": "Обновяване", "SSE.Views.PivotTable.txtSelect": "Изберете", "SSE.Views.PrintSettings.btnDownload": "Запазване и изтегляне", "SSE.Views.PrintSettings.btnPrint": "Запазване и печат", "SSE.Views.PrintSettings.strBottom": "Отдоло", "SSE.Views.PrintSettings.strLandscape": "Пей<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "Наляво", "SSE.Views.PrintSettings.strMargins": "Полета", "SSE.Views.PrintSettings.strPortrait": "Портрет", "SSE.Views.PrintSettings.strPrint": "Печат", "SSE.Views.PrintSettings.strRight": "Прав", "SSE.Views.PrintSettings.strShow": "Пока<PERSON>и", "SSE.Views.PrintSettings.strTop": "Отгоре", "SSE.Views.PrintSettings.textActualSize": "Действителен размер", "SSE.Views.PrintSettings.textAllSheets": "Всички листове", "SSE.Views.PrintSettings.textCurrentSheet": "Текущ лист", "SSE.Views.PrintSettings.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.PrintSettings.textFitCols": "Поставете всички колони на една страница", "SSE.Views.PrintSettings.textFitPage": "Поставете лист на една страница", "SSE.Views.PrintSettings.textFitRows": "Поставете всички редове на една страница", "SSE.Views.PrintSettings.textHideDetails": "Скриване на подробности", "SSE.Views.PrintSettings.textIgnore": "Игнорирай областта за печат", "SSE.Views.PrintSettings.textLayout": "Оформление", "SSE.Views.PrintSettings.textPageOrientation": "Ориентация на страницата", "SSE.Views.PrintSettings.textPageScaling": "Мащабиране", "SSE.Views.PrintSettings.textPageSize": "Размер на страницата", "SSE.Views.PrintSettings.textPrintGrid": "Печат на решетки", "SSE.Views.PrintSettings.textPrintHeadings": "Печат заглавия на редове и колони", "SSE.Views.PrintSettings.textPrintRange": "Диапазон на печат", "SSE.Views.PrintSettings.textRange": "Диа<PERSON>азон", "SSE.Views.PrintSettings.textSelection": "Селекция", "SSE.Views.PrintSettings.textSettings": "Настройки на листа", "SSE.Views.PrintSettings.textShowDetails": "Покажи детайли", "SSE.Views.PrintSettings.textShowGrid": "Показване на мрежи", "SSE.Views.PrintSettings.textShowHeadings": "Показване на заглавията на редове и колони", "SSE.Views.PrintSettings.textTitle": "Настройки за печат", "SSE.Views.PrintSettings.textTitlePDF": "Настройки на PDF", "SSE.Views.PrintWithPreview.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.PrintWithPreview.txtPageOrientation": "Ориентация на страницата", "SSE.Views.PrintWithPreview.txtPageSize": "Размер на страницата", "SSE.Views.ProtectRangesDlg.guestText": "Гост", "SSE.Views.ProtectRangesDlg.textDelete": "Изтрий", "SSE.Views.RightMenu.txtCellSettings": "Настройки на клетката", "SSE.Views.RightMenu.txtChartSettings": "Настройки на диаграмата", "SSE.Views.RightMenu.txtImageSettings": "Настройки на изображението", "SSE.Views.RightMenu.txtParagraphSettings": "Настройки за текст", "SSE.Views.RightMenu.txtPivotSettings": "Настройки на обобщените таблици", "SSE.Views.RightMenu.txtSettings": "Общи настройки", "SSE.Views.RightMenu.txtShapeSettings": "Настройки на формата", "SSE.Views.RightMenu.txtSignatureSettings": "Настройки за подпис", "SSE.Views.RightMenu.txtSparklineSettings": "Настройки за спарклайн", "SSE.Views.RightMenu.txtTableSettings": "Настройки на таблицата", "SSE.Views.RightMenu.txtTextArtSettings": "Настройки за текстово изкуство", "SSE.Views.ScaleDialog.textAuto": "Автоматичен", "SSE.Views.ScaleDialog.textHeight": "Висо<PERSON>ина", "SSE.Views.SetValueDialog.txtMaxText": "Максималната стойност за това поле е {0}", "SSE.Views.SetValueDialog.txtMinText": "Минималната стойност за това поле е {0}", "SSE.Views.ShapeSettings.strBackground": "Цвят на фона", "SSE.Views.ShapeSettings.strChange": "Промяна на форма", "SSE.Views.ShapeSettings.strColor": "Цвят", "SSE.Views.ShapeSettings.strFill": "Напълнете", "SSE.Views.ShapeSettings.strForeground": "Цвят на преден план", "SSE.Views.ShapeSettings.strPattern": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strSize": "Размер", "SSE.Views.ShapeSettings.strStroke": "Удар", "SSE.Views.ShapeSettings.strTransparency": "Непрозрачност", "SSE.Views.ShapeSettings.strType": "Тип", "SSE.Views.ShapeSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ShapeSettings.textAngle": "Ъгъл", "SSE.Views.ShapeSettings.textBorderSizeErr": "Въведената стойност е неправилна. <br> Въведете стойност между 0 pt и 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Цветово пълнене", "SSE.Views.ShapeSettings.textDirection": "Посока", "SSE.Views.ShapeSettings.textEmptyPattern": "Без модел", "SSE.Views.ShapeSettings.textFlip": "Отразява", "SSE.Views.ShapeSettings.textFromFile": "От файл ", "SSE.Views.ShapeSettings.textFromUrl": "От URL", "SSE.Views.ShapeSettings.textGradient": "Град<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textGradientFill": "Градиентно запълване", "SSE.Views.ShapeSettings.textHint270": "Завъртете на 90 ° обратно на часовниковата стрелка", "SSE.Views.ShapeSettings.textHint90": "Завъртете на 90 ° по посока на часовниковата стрелка", "SSE.Views.ShapeSettings.textHintFlipH": "Отрязва по хоризонтала", "SSE.Views.ShapeSettings.textHintFlipV": "Отрязване по вертикала", "SSE.Views.ShapeSettings.textImageTexture": "Картина или текстура", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoFill": "Без попълване", "SSE.Views.ShapeSettings.textOriginalSize": "Оригинален размер", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Позиция", "SSE.Views.ShapeSettings.textRadial": "Радиа<PERSON><PERSON>н", "SSE.Views.ShapeSettings.textRotate90": "Завъртане на 90 °", "SSE.Views.ShapeSettings.textRotation": "Завъртане", "SSE.Views.ShapeSettings.textSelectTexture": "Изберете", "SSE.Views.ShapeSettings.textStretch": "Разтягане", "SSE.Views.ShapeSettings.textStyle": "Стил", "SSE.Views.ShapeSettings.textTexture": "От текстура", "SSE.Views.ShapeSettings.textTile": "Плочка", "SSE.Views.ShapeSettings.txtBrownPaper": "Кафява хартия", "SSE.Views.ShapeSettings.txtCanvas": "Платно", "SSE.Views.ShapeSettings.txtCarton": "Карто<PERSON><PERSON>на кутия", "SSE.Views.ShapeSettings.txtDarkFabric": "Тъмна тъкан", "SSE.Views.ShapeSettings.txtGrain": "Зърно", "SSE.Views.ShapeSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Сива хартия", "SSE.Views.ShapeSettings.txtKnit": "Плета", "SSE.Views.ShapeSettings.txtLeather": "Кожа", "SSE.Views.ShapeSettings.txtNoBorders": "Няма линия", "SSE.Views.ShapeSettings.txtPapyrus": "Папир<PERSON>с", "SSE.Views.ShapeSettings.txtWood": "Дърво", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Колони", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Попълване на текст", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Алтернативен текст", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, автосистемата, диаграмата или таблицата.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Заглавие", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Ъгъл", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Cтрелките", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Начален размер", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Започнете стила", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Откос", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Отдоло", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Тип на капачката", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Брой колони", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Размер на края", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON> стил", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Плосък", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Огледален", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Висо<PERSON>ина", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Хоризонтално", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Тип на присъединяване", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Постоянни пропорции", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Наляво", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Стил на линията", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Митра", "SSE.Views.ShapeSettingsAdvanced.textRight": "Прав", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Завъртане", "SSE.Views.ShapeSettingsAdvanced.textRound": "Кръгъл", "SSE.Views.ShapeSettingsAdvanced.textSize": "Размер", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Разстояние между колоните", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Ква<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Форма - Разширени настройки", "SSE.Views.ShapeSettingsAdvanced.textTop": "Отгоре", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Вертикално", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Теглилки и стрелки", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Внимание", "SSE.Views.SignatureSettings.strDelete": "Премахване на подпис", "SSE.Views.SignatureSettings.strDetails": "Подробности за подпис", "SSE.Views.SignatureSettings.strInvalid": "Невалидни подписи", "SSE.Views.SignatureSettings.strRequested": "Искани подписи", "SSE.Views.SignatureSettings.strSetup": "Настройка на подпис", "SSE.Views.SignatureSettings.strSign": "Знак", "SSE.Views.SignatureSettings.strSignature": "Под<PERSON>ис", "SSE.Views.SignatureSettings.strSigner": "Подписал", "SSE.Views.SignatureSettings.strValid": "Валидни подписи", "SSE.Views.SignatureSettings.txtContinueEditing": "Все пак редактирайте", "SSE.Views.SignatureSettings.txtEditWarning": "Редактирането ще премахне подписите от електронната таблица. <br> Наистина ли искате да продължите?", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Тази електронна таблица трябва да бъде подписана.", "SSE.Views.SignatureSettings.txtSigned": "В електронната таблица са добавени валидни подписи. Електронната таблица е защитена от редактиране.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Някои от цифровите подписи в електронната таблица са невалидни или не можаха да бъдат потвърдени. Електронната таблица е защитена от редактиране.", "SSE.Views.SlicerSettings.textAsc": "Възходящ", "SSE.Views.SlicerSettings.textHeight": "Висо<PERSON>ина", "SSE.Views.SlicerSettings.textPosition": "Позиция", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Висо<PERSON>ина", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, автосистемата, диаграмата или таблицата.", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Възходящ", "SSE.Views.SortDialog.textAsc": "Възходящ", "SSE.Views.SortDialog.textAuto": "Автоматичен", "SSE.Views.SortDialog.textFontColor": "Цвят на шрифта", "SSE.Views.SpecialPasteDialog.textFormats": "Формати", "SSE.Views.SpecialPasteDialog.textFormulas": "Формули", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Копиране до края)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Преместване в края)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Копиране преди лист", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Преместване преди лист", "SSE.Views.Statusbar.filteredRecordsText": "{0} от {1} записа са филтрирани", "SSE.Views.Statusbar.filteredText": "Режим филтър", "SSE.Views.Statusbar.itemCopy": "Копие", "SSE.Views.Statusbar.itemDelete": "Изтрий", "SSE.Views.Statusbar.itemHidden": "Скрит", "SSE.Views.Statusbar.itemHide": "Скрий", "SSE.Views.Statusbar.itemInsert": "Вмъкни", "SSE.Views.Statusbar.itemMaximum": "Максимален", "SSE.Views.Statusbar.itemMove": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "Преименувам", "SSE.Views.Statusbar.itemTabColor": "Цвят на раздела", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Работният лист с такова име вече съществува.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Името на листа не може да съдържа следните знаци: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Име на листа", "SSE.Views.Statusbar.textAverage": "СРЕДНО АРИТМЕТИЧНО", "SSE.Views.Statusbar.textCount": "БРОЯ", "SSE.Views.Statusbar.textMax": "MAX", "SSE.Views.Statusbar.textMin": "MIN", "SSE.Views.Statusbar.textNewColor": "Нов потребителски цвят", "SSE.Views.Statusbar.textNoColor": "Няма цвят", "SSE.Views.Statusbar.textSum": "SUM", "SSE.Views.Statusbar.tipAddTab": "Добавете работен лист", "SSE.Views.Statusbar.tipFirst": "Превъртете до първия лист", "SSE.Views.Statusbar.tipLast": "Преминете към последния лист", "SSE.Views.Statusbar.tipNext": "Превъртете списъка с листа право", "SSE.Views.Statusbar.tipPrev": "Отидете в списъка с листа", "SSE.Views.Statusbar.tipZoomFactor": "Мащ<PERSON>б", "SSE.Views.Statusbar.tipZoomIn": "Увеличение", "SSE.Views.Statusbar.tipZoomOut": "Отдалечавам", "SSE.Views.Statusbar.zoomText": "Мащаб {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Операцията не може да бъде извършена за избрания диапазон от клетки. <br> Изберете един и същ диапазон от данни, различен от съществуващия, и опитайте отново.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Операцията не може да бъде завършена за избрания диапазон от клетки. <br> Изберете диапазон, така че първият ред на таблицата да е в същия ред <br> и получената таблица се припокрива с текущия.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Операцията не може да бъде завършена за избрания диапазон от клетки. <br> Изберете диапазон, който не включва други таблици.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Формулирайте с множество клетки, които не са позволени в таблицата.", "SSE.Views.TableOptionsDialog.txtEmpty": "Това поле е задължително", "SSE.Views.TableOptionsDialog.txtFormat": "Създаване на таблица", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.TableOptionsDialog.txtTitle": "Заглавие", "SSE.Views.TableSettings.deleteColumnText": "Изтриване на колона", "SSE.Views.TableSettings.deleteRowText": "Изтриване на ред", "SSE.Views.TableSettings.deleteTableText": "Изтриване на таблицата", "SSE.Views.TableSettings.insertColumnLeftText": "Вмъкване на колона вляво", "SSE.Views.TableSettings.insertColumnRightText": "Вмъкване на колона вдясно", "SSE.Views.TableSettings.insertRowAboveText": "Вмъкване на ред отгоре", "SSE.Views.TableSettings.insertRowBelowText": "Вмъкнете ред по-долу", "SSE.Views.TableSettings.notcriticalErrorTitle": "Внимание", "SSE.Views.TableSettings.selectColumnText": "Изберете цяла колона", "SSE.Views.TableSettings.selectDataText": "Изберете данни за колона", "SSE.Views.TableSettings.selectRowText": "Изберете ред", "SSE.Views.TableSettings.selectTableText": "Изберете таблица", "SSE.Views.TableSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.TableSettings.textBanded": "На ивици", "SSE.Views.TableSettings.textColumns": "Колони", "SSE.Views.TableSettings.textConvertRange": "Преобразуване в обхват", "SSE.Views.TableSettings.textEdit": "Редове и колони", "SSE.Views.TableSettings.textEmptyTemplate": "Няма шаблони", "SSE.Views.TableSettings.textExistName": "ГРЕШКА! Вече съществува диапазон с такова име", "SSE.Views.TableSettings.textFilter": "Бутон за филтриране", "SSE.Views.TableSettings.textFirst": "Първи", "SSE.Views.TableSettings.textHeader": "Заглавие", "SSE.Views.TableSettings.textInvalidName": "ГРЕШКА! Невалидно име на таблица", "SSE.Views.TableSettings.textIsLocked": "Този елемент се редактира от друг потребител.", "SSE.Views.TableSettings.textLast": "Последно", "SSE.Views.TableSettings.textLongOperation": "Дълга операция", "SSE.Views.TableSettings.textReservedName": "Името, което се опитвате да използвате, вече е посочено в клетъчни формули. Моля, използвайте друго име.", "SSE.Views.TableSettings.textResize": "Преоразмеряване на таблицата", "SSE.Views.TableSettings.textRows": "Редове", "SSE.Views.TableSettings.textSelectData": "Изберете данни", "SSE.Views.TableSettings.textTableName": "Име на таблицата", "SSE.Views.TableSettings.textTemplate": "Изберете от шаблон", "SSE.Views.TableSettings.textTotal": "Обща сума", "SSE.Views.TableSettings.warnLongOperation": "Операцията, която ще изпълните, може да отнеме доста време за изпълнение. <br> Наистина ли искате да продължите?", "SSE.Views.TableSettingsAdvanced.textAlt": "Алтернативен текст", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.TableSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, автосистемата, диаграмата или таблицата.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Заглавие", "SSE.Views.TableSettingsAdvanced.textTitle": "Таблица - Разширени настройки", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.TextArtSettings.strBackground": "Цвят на фона", "SSE.Views.TextArtSettings.strColor": "Цвят", "SSE.Views.TextArtSettings.strFill": "Напълнете", "SSE.Views.TextArtSettings.strForeground": "Цвят на преден план", "SSE.Views.TextArtSettings.strPattern": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "Размер", "SSE.Views.TextArtSettings.strStroke": "Удар", "SSE.Views.TextArtSettings.strTransparency": "Непрозрачност", "SSE.Views.TextArtSettings.strType": "Тип", "SSE.Views.TextArtSettings.textAngle": "Ъгъл", "SSE.Views.TextArtSettings.textBorderSizeErr": "Въведената стойност е неправилна. <br> Въведете стойност между 0 pt и 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Цветово пълнене", "SSE.Views.TextArtSettings.textDirection": "Посока", "SSE.Views.TextArtSettings.textEmptyPattern": "Без модел", "SSE.Views.TextArtSettings.textFromFile": "От файл ", "SSE.Views.TextArtSettings.textFromUrl": "От URL", "SSE.Views.TextArtSettings.textGradient": "Град<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textGradientFill": "Градиентно запълване", "SSE.Views.TextArtSettings.textImageTexture": "Картина или текстура", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Без попълване", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Позиция", "SSE.Views.TextArtSettings.textRadial": "Радиа<PERSON><PERSON>н", "SSE.Views.TextArtSettings.textSelectTexture": "Изберете", "SSE.Views.TextArtSettings.textStretch": "Разтягане", "SSE.Views.TextArtSettings.textStyle": "Стил", "SSE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "SSE.Views.TextArtSettings.textTexture": "От текстура", "SSE.Views.TextArtSettings.textTile": "Плочка", "SSE.Views.TextArtSettings.textTransform": "Трансформирайте", "SSE.Views.TextArtSettings.txtBrownPaper": "Кафява хартия", "SSE.Views.TextArtSettings.txtCanvas": "Платно", "SSE.Views.TextArtSettings.txtCarton": "Карто<PERSON><PERSON>на кутия", "SSE.Views.TextArtSettings.txtDarkFabric": "Тъмна тъкан", "SSE.Views.TextArtSettings.txtGrain": "Зърно", "SSE.Views.TextArtSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Сива хартия", "SSE.Views.TextArtSettings.txtKnit": "Плета", "SSE.Views.TextArtSettings.txtLeather": "Кожа", "SSE.Views.TextArtSettings.txtNoBorders": "Няма линия", "SSE.Views.TextArtSettings.txtPapyrus": "Папир<PERSON>с", "SSE.Views.TextArtSettings.txtWood": "Дърво", "SSE.Views.Toolbar.capBtnAddComment": "Добави коментар", "SSE.Views.Toolbar.capBtnColorSchemas": "Цветова схема", "SSE.Views.Toolbar.capBtnComment": "Коментар", "SSE.Views.Toolbar.capBtnInsHeader": "Горен/долен колонтитул", "SSE.Views.Toolbar.capBtnMargins": "Полета", "SSE.Views.Toolbar.capBtnPageOrient": "Ориентация", "SSE.Views.Toolbar.capBtnPageSize": "Размер", "SSE.Views.Toolbar.capBtnPrintArea": "Площ за печат", "SSE.Views.Toolbar.capImgAlign": "Изравнете", "SSE.Views.Toolbar.capImgBackward": "Изпращане назад", "SSE.Views.Toolbar.capImgForward": "Изведи напред", "SSE.Views.Toolbar.capImgGroup": "Гру<PERSON>а", "SSE.Views.Toolbar.capInsertChart": "Диаграма", "SSE.Views.Toolbar.capInsertEquation": "Уравнение", "SSE.Views.Toolbar.capInsertHyperlink": "Хипервръзка", "SSE.Views.Toolbar.capInsertImage": "Изображение", "SSE.Views.Toolbar.capInsertShape": "Форма", "SSE.Views.Toolbar.capInsertTable": "Таблица", "SSE.Views.Toolbar.capInsertText": "Текстово поле", "SSE.Views.Toolbar.mniImageFromFile": "Изображение от файл", "SSE.Views.Toolbar.mniImageFromStorage": "Изображение от хранилището", "SSE.Views.Toolbar.mniImageFromUrl": "Изображение от URL адрес", "SSE.Views.Toolbar.textAddPrintArea": "Добавяне към областта за печат", "SSE.Views.Toolbar.textAlignBottom": "Подравняване отдолу", "SSE.Views.Toolbar.textAlignCenter": "Подравняване на центъра ", "SSE.Views.Toolbar.textAlignJust": "Двустранно", "SSE.Views.Toolbar.textAlignLeft": "Подравняване вляво", "SSE.Views.Toolbar.textAlignMiddle": "Подравняване на средата", "SSE.Views.Toolbar.textAlignRight": "Подравняване надясно", "SSE.Views.Toolbar.textAlignTop": "Подравняване отгоре", "SSE.Views.Toolbar.textAllBorders": "Всички граници", "SSE.Views.Toolbar.textAuto": "Автоматичен", "SSE.Views.Toolbar.textAutoColor": "Автоматичен", "SSE.Views.Toolbar.textBold": "Получер", "SSE.Views.Toolbar.textBordersColor": "Цвят на границата", "SSE.Views.Toolbar.textBordersStyle": "Стил на границата", "SSE.Views.Toolbar.textBottom": "Отдоло:", "SSE.Views.Toolbar.textBottomBorders": "Долни граници", "SSE.Views.Toolbar.textCenterBorders": "Вътрешни вертикални граници", "SSE.Views.Toolbar.textClearPrintArea": "Изчистване на зоната за печат", "SSE.Views.Toolbar.textClockwise": "Ъгъл по часовниковата стрелка", "SSE.Views.Toolbar.textCounterCw": "Ъгъл обратно на часовниковата стрелка", "SSE.Views.Toolbar.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.Toolbar.textDelLeft": "Преместване на клетките вляво", "SSE.Views.Toolbar.textDelUp": "Преместете клетки нагоре", "SSE.Views.Toolbar.textDiagDownBorder": "Диа<PERSON><PERSON><PERSON>лна надолу граница", "SSE.Views.Toolbar.textDiagUpBorder": "Диагонална граница нагоре", "SSE.Views.Toolbar.textEntireCol": "Цяла колона", "SSE.Views.Toolbar.textEntireRow": "Цял ред", "SSE.Views.Toolbar.textHeight": "Висо<PERSON>ина", "SSE.Views.Toolbar.textHorizontal": "Хоризонтален текст", "SSE.Views.Toolbar.textInsDown": "Преместете клетките надолу", "SSE.Views.Toolbar.textInsideBorders": "Вътрешни граници", "SSE.Views.Toolbar.textInsRight": "Преместете клетките надясно", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLandscape": "Пей<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeft": "Наляво:", "SSE.Views.Toolbar.textLeftBorders": "Ляви граници", "SSE.Views.Toolbar.textMarginsLast": "Последно персонализирано", "SSE.Views.Toolbar.textMarginsNarrow": "Тесен", "SSE.Views.Toolbar.textMarginsNormal": "Нормален", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Хоризонтални граници", "SSE.Views.Toolbar.textMoreFormats": "Още формати", "SSE.Views.Toolbar.textNewColor": "Нов потребителски цвят", "SSE.Views.Toolbar.textNoBorders": "Няма граници", "SSE.Views.Toolbar.textOutBorders": "Външни граници", "SSE.Views.Toolbar.textPageMarginsCustom": "Персонализирани полета", "SSE.Views.Toolbar.textPortrait": "Портрет", "SSE.Views.Toolbar.textPrint": "Печат", "SSE.Views.Toolbar.textPrintOptions": "Настройки за печат", "SSE.Views.Toolbar.textRight": "Дясно:", "SSE.Views.Toolbar.textRightBorders": "Дясна граница", "SSE.Views.Toolbar.textRotateDown": "Завъртете текста надолу", "SSE.Views.Toolbar.textRotateUp": "Завъртете текста нагоре", "SSE.Views.Toolbar.textScaleCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.Toolbar.textSetPrintArea": "Задайте област на печат", "SSE.Views.Toolbar.textStrikeout": "За<PERSON><PERSON><PERSON><PERSON><PERSON>т", "SSE.Views.Toolbar.textSubscript": "До<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubSuperscript": "Долен/Горен индекс", "SSE.Views.Toolbar.textSuperscript": "Горен индекс", "SSE.Views.Toolbar.textTabCollaboration": "Сътрудничество", "SSE.Views.Toolbar.textTabData": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "досие", "SSE.Views.Toolbar.textTabFormula": "Формула", "SSE.Views.Toolbar.textTabHome": "У дома", "SSE.Views.Toolbar.textTabInsert": "Вмъкни", "SSE.Views.Toolbar.textTabLayout": "Оформление", "SSE.Views.Toolbar.textTabProtect": "Защита", "SSE.Views.Toolbar.textTabView": "Изглед", "SSE.Views.Toolbar.textTop": "Връх:", "SSE.Views.Toolbar.textTopBorders": "Топ граници", "SSE.Views.Toolbar.textUnderline": "Подчертавам", "SSE.Views.Toolbar.textVertical": "Вертикален текст", "SSE.Views.Toolbar.textZoom": "Мащ<PERSON>б", "SSE.Views.Toolbar.tipAlignBottom": "Подравняване отдолу", "SSE.Views.Toolbar.tipAlignCenter": "Подравняване на центъра", "SSE.Views.Toolbar.tipAlignJust": "Двустранно", "SSE.Views.Toolbar.tipAlignLeft": "Подравняване вляво", "SSE.Views.Toolbar.tipAlignMiddle": "Подравняване на средата", "SSE.Views.Toolbar.tipAlignRight": "Подравняване надясно", "SSE.Views.Toolbar.tipAlignTop": "Подравняване отгоре", "SSE.Views.Toolbar.tipAutofilter": "Сортиране и филтриране", "SSE.Views.Toolbar.tipBack": "Обратно", "SSE.Views.Toolbar.tipBorders": "<PERSON>ра<PERSON><PERSON><PERSON>и", "SSE.Views.Toolbar.tipCellStyle": "Стил на клетката", "SSE.Views.Toolbar.tipChangeChart": "Промяна на типа на диаграмата", "SSE.Views.Toolbar.tipClearStyle": "Изчисти", "SSE.Views.Toolbar.tipColorSchemas": "Промяна на цветовата схема", "SSE.Views.Toolbar.tipCopy": "Копие", "SSE.Views.Toolbar.tipCopyStyle": "Копирайте стила", "SSE.Views.Toolbar.tipDecDecimal": "Намаляване на десетичната запетая", "SSE.Views.Toolbar.tipDecFont": "Размер на шрифта", "SSE.Views.Toolbar.tipDeleteOpt": "Изтриване на клетки", "SSE.Views.Toolbar.tipDigStyleAccounting": "Счетоводен стил", "SSE.Views.Toolbar.tipDigStyleCurrency": "Валу<PERSON><PERSON>н стил", "SSE.Views.Toolbar.tipDigStylePercent": "Процент стил", "SSE.Views.Toolbar.tipEditChart": "Редактиране на диаграма", "SSE.Views.Toolbar.tipFontColor": "Цвят на шрифта", "SSE.Views.Toolbar.tipFontName": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontSize": "Размер на шрифта", "SSE.Views.Toolbar.tipHAlighOle": "Хоризонтално подравняване", "SSE.Views.Toolbar.tipImgAlign": "Подравняване на обекти", "SSE.Views.Toolbar.tipImgGroup": "Групови обекти", "SSE.Views.Toolbar.tipIncDecimal": "Увеличаване на десетичната запетая", "SSE.Views.Toolbar.tipIncFont": "Увеливане на размера на шрифта", "SSE.Views.Toolbar.tipInsertChart": "Поставете диаграма", "SSE.Views.Toolbar.tipInsertChartSpark": "Поставете диаграма", "SSE.Views.Toolbar.tipInsertEquation": "Вмъкване на уравнение", "SSE.Views.Toolbar.tipInsertHyperlink": "Добавете хипервръзка", "SSE.Views.Toolbar.tipInsertImage": "Вмъкване на изображение", "SSE.Views.Toolbar.tipInsertOpt": "Вмъкване на клетки", "SSE.Views.Toolbar.tipInsertShape": "Вмъкване на автоматична форма", "SSE.Views.Toolbar.tipInsertText": "Вмъкни текстовото поле", "SSE.Views.Toolbar.tipInsertTextart": "Вмъкни текст Чл", "SSE.Views.Toolbar.tipMerge": "Сливам", "SSE.Views.Toolbar.tipNumFormat": "Формат на номера", "SSE.Views.Toolbar.tipPageMargins": "Полета на страницата", "SSE.Views.Toolbar.tipPageOrient": "Ориентация на страницата", "SSE.Views.Toolbar.tipPageSize": "Размер на страницата", "SSE.Views.Toolbar.tipPaste": "Паста", "SSE.Views.Toolbar.tipPrColor": "Цвят на фона", "SSE.Views.Toolbar.tipPrint": "Печат", "SSE.Views.Toolbar.tipPrintArea": "Площ за печат", "SSE.Views.Toolbar.tipRedo": "Повтори", "SSE.Views.Toolbar.tipSave": "Запази", "SSE.Views.Toolbar.tipSaveCoauth": "Запазете промените си, за да ги видят другите потребители.", "SSE.Views.Toolbar.tipSendBackward": "Изпращане назад", "SSE.Views.Toolbar.tipSendForward": "Изведи напред", "SSE.Views.Toolbar.tipSynchronize": "Документът е променен от друг потребител. Моля, кликнете, за да запазите промените си и да презаредите актуализациите.", "SSE.Views.Toolbar.tipTextOrientation": "Ориентация", "SSE.Views.Toolbar.tipUndo": "Отмени", "SSE.Views.Toolbar.tipVAlighOle": "Вертикално подравняване", "SSE.Views.Toolbar.tipWrap": "Преливане на текст", "SSE.Views.Toolbar.txtAccounting": "Счетоводство", "SSE.Views.Toolbar.txtAdditional": "Допълнителен", "SSE.Views.Toolbar.txtAscending": "Възходящ", "SSE.Views.Toolbar.txtClearAll": "Всички", "SSE.Views.Toolbar.txtClearComments": "Коментари", "SSE.Views.Toolbar.txtClearFilter": "Изчистване на филтъра", "SSE.Views.Toolbar.txtClearFormat": "Формат", "SSE.Views.Toolbar.txtClearFormula": "Функция", "SSE.Views.Toolbar.txtClearHyper": "Хипервръзки", "SSE.Views.Toolbar.txtClearText": "Текст", "SSE.Views.Toolbar.txtCurrency": "Валута", "SSE.Views.Toolbar.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.Toolbar.txtDate": "Дата", "SSE.Views.Toolbar.txtDateTime": "Време за среща", "SSE.Views.Toolbar.txtDescending": "Низходящо", "SSE.Views.Toolbar.txtDollar": "$ Долар", "SSE.Views.Toolbar.txtEuro": "€ Евро", "SSE.Views.Toolbar.txtExp": "Показателен", "SSE.Views.Toolbar.txtFilter": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.Toolbar.txtFormula": "Вмъкване на функция", "SSE.Views.Toolbar.txtFraction": "Фракция", "SSE.Views.Toolbar.txtFranc": "CHF швейцарски франк", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtInteger": "Цяло число", "SSE.Views.Toolbar.txtManageRange": "Мениджър на имена", "SSE.Views.Toolbar.txtMergeAcross": "Обединяване", "SSE.Views.Toolbar.txtMergeCells": "Сливане на клетки", "SSE.Views.Toolbar.txtMergeCenter": "Обединяване и център", "SSE.Views.Toolbar.txtNamedRange": "Наименувани диапазони", "SSE.Views.Toolbar.txtNewRange": "Определяне на име", "SSE.Views.Toolbar.txtNoBorders": "Няма граници", "SSE.Views.Toolbar.txtNumber": "Номер", "SSE.Views.Toolbar.txtPasteRange": "Поставяне на името", "SSE.Views.Toolbar.txtPercentage": "Процент", "SSE.Views.Toolbar.txtPound": "£ Паунд", "SSE.Views.Toolbar.txtRouble": "₽ Рубли", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "Медиана", "SSE.Views.Toolbar.txtScheme11": "Метро", "SSE.Views.Toolbar.txtScheme12": "<PERSON>о<PERSON><PERSON>л", "SSE.Views.Toolbar.txtScheme13": "Разкошен", "SSE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme15": "Произход", "SSE.Views.Toolbar.txtScheme16": "Ха<PERSON>тия", "SSE.Views.Toolbar.txtScheme17": "Слънцестоене", "SSE.Views.Toolbar.txtScheme18": "Техника", "SSE.Views.Toolbar.txtScheme19": "Пътуване", "SSE.Views.Toolbar.txtScheme2": "Нюанси на сивото", "SSE.Views.Toolbar.txtScheme20": "Градски", "SSE.Views.Toolbar.txtScheme21": "Ентусиазъм", "SSE.Views.Toolbar.txtScheme3": "Връх", "SSE.Views.Toolbar.txtScheme4": "Аспект", "SSE.Views.Toolbar.txtScheme5": "Градски", "SSE.Views.Toolbar.txtScheme6": "Стечение", "SSE.Views.Toolbar.txtScheme7": "Справедливост", "SSE.Views.Toolbar.txtScheme8": "Поток", "SSE.Views.Toolbar.txtScheme9": "Леярна", "SSE.Views.Toolbar.txtScientific": "Научен", "SSE.Views.Toolbar.txtSearch": "Търсене", "SSE.Views.Toolbar.txtSort": "Вид", "SSE.Views.Toolbar.txtSortAZ": "Сортиране във възходящ ред", "SSE.Views.Toolbar.txtSortZA": "Сортиране по низходящ ред", "SSE.Views.Toolbar.txtSpecial": "Специален", "SSE.Views.Toolbar.txtTableTemplate": "Форматирайте като шаблон на таблица", "SSE.Views.Toolbar.txtText": "Текст", "SSE.Views.Toolbar.txtTime": "Път", "SSE.Views.Toolbar.txtUnmerge": "Изтеглете клетките", "SSE.Views.Toolbar.txtYen": "¥ Йена", "SSE.Views.Top10FilterDialog.textType": "Пока<PERSON>и", "SSE.Views.Top10FilterDialog.txtBottom": "Отдоло", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "На сто", "SSE.Views.Top10FilterDialog.txtTitle": "Топ 10 на автофилтъра", "SSE.Views.Top10FilterDialog.txtTop": "Отгоре", "SSE.Views.ViewManagerDlg.guestText": "Гост", "SSE.Views.ViewManagerDlg.textDelete": "Изтрий", "SSE.Views.ViewTab.textGridlines": "Мрежови линии"}