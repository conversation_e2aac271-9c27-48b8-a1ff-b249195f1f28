﻿<!DOCTYPE html>
<html>
	<head>
		<title>ERSTERWERT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ERSTERWERT-Funktion</h1>
			<p>Die Funktion <b>ERSTERWERT</b> gehört zur Gruppe der logischen Funktionen. Mit der Funktion wird ein Wert (als Ausdruck bezeichnet) anhand einer Liste mit Werten ausgewertet. Als Ergebnis wird der erste übereinstimmende Wert zurückgegeben. Wenn es keine Übereinstimmung gibt, kann ein optionaler Standardwert zurückgegeben werden.</p>
			<p>Die Formelsyntax der Funktion <b>ERSTERWERT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ERSTERWERT(Ausdruck; Wert1; Ergebnis1; [Standardwert oder Wert2; Ergebnis2];…[Standardwert oder Wert3; Ergebnis3])</em></b></p> 
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Ausdruck</em></b> ist der Ausdruck, der mit <b><em>Wert1...Wert126</em></b> verglichen wird.</p>
            <p style="text-indent: 50px;"><b><em>Wert1...Wert126</em></b> ist ein Wert, der mit dem <b><em>Ausdruck</em></b> verglichen wird.</p>
            <p style="text-indent: 50px;"><b><em>Ergebnis1…Ergebnis126</em></b> ist der Wert, der zurückgegeben wird, wenn das entsprechende Argument <b><em>Wert1...Wert126</em></b> dem <b><em>Ausdruck</em></b> entspricht.</p>
            <p style="text-indent: 50px;"><b><em>Standardwert</em></b> ist der Wert, der für den Fall zurückgegeben wird, dass in den Ausdrücken keine Übereinstimmungen gefunden werden. Dieses Argument ist optional. Wird kein <b><em>Standardwert</em></b> und es liegen keine Übereinstimmungen vor, gibt die Funktion den Fehler #NV! zurück.</p>
            <p class="note"><b>Hinweis</b>: Sie können bis zu <b>254</b> Argumente eingeben, bzw. 126 Werte-paare und Ergebnisse.</p>
			<p>Anwendung der Funktion <b>ERSTERWERT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Logisch</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ERSTERWERT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ERSTERWERT-Funktion" src="../images/switch.png" /></p>
		</div>
	</body>
</html>