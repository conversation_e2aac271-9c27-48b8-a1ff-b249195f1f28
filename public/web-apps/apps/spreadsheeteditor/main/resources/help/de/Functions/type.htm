﻿<!DOCTYPE html>
<html>
	<head>
		<title>TYP-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>TYP-Funktion</h1>
			<p>Die Funktion <b>TYP</b> gehört zur Gruppe der Informationsfunktionen. Sie gibt eine Zahl zurück, die den Datentyp eines Werts angibt.</p>
			<p>Die Formelsyntax der Funktion <b>TYP</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>TYP(Wert)</em></b></p> 
			<p><b><em>Wert</em></b> gibt den Wert wieder der geprüft werden soll. Sie können den zu überprüfenden Bereich manuell eingeben oder mit der Maus auswählen. Mögliche Werte und das von TYP zurückgegebene Ergebnis:</p>
			<table style="width: 20%">
				<tr>
					<td><b>Wert</b></td>
					<td><b>Von TYP zurückgegebenes Ergebnis</b></td>
				</tr>
				<tr>
					<td>Zahl</td>
					<td>1</td>
				</tr>
				<tr>
					<td>Text</td>
					<td>2</td>
				</tr>
				<tr>
					<td>Wahrheitswert</td>
					<td>4</td>
				</tr>
				<tr>
					<td>Fehlerwert</td>
					<td>16</td>
				</tr>
				<tr>
					<td>Matrix</td>
					<td>64</td>
				</tr>
			</table>
			<p>Anwendung der Funktion <b>TYP</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>TYP</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="TYP-Funktion" src="../images/type.png" /></p>
		</div>
	</body>
</html>