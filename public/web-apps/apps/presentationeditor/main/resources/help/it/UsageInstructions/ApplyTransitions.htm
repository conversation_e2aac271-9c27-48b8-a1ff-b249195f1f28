﻿<!DOCTYPE html>
<html>
	<head>
		<title>Apply transitions</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add animation effects between slides" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Apply transitions</h1>
			<p>A <b>transition</b> is an effect that appears between two slides when one slide advances to the next one during a demonstration. You can apply the same transition to all slides or apply different transitions to each separate slide and adjust the transition properties.</p>
			<p><b>To apply a transition to a single slide</b> or several selected slides:</p>
			<p><img class="floatleft"alt="Slide settings tab" src="../images/slidesettingstab.png" /></p>
			<ol style="margin-left: 280px;">
				<li>Select the necessary slide (or several slides in the slide list) you want to apply a transition to. The <b>Slide settings</b> tab will be activated on the right sidebar. To open it click the <b>Slide settings</b> <div class = "icon icon-slide_settings_icon"></div> icon on the right. Alternatively, you can right-click a slide in the slide editing area and select the <b>Slide Settings</b> option from the contextual menu.
				</li>
				<li>In the <b>Effect</b> drop-down list, select the transition you want to use.
				<p>The following transitions are available: Fade, Push, Wipe, Split, Uncover, Cover, Clock, Zoom.</p>
				</li>
				<li>In the drop-down list below, select one of the available effect options. They define exactly how the effect appears. For example, if the Zoom transition is selected, the Zoom In, Zoom Out and Zoom and Rotate options are available.</li>
				<li>Specify how long you want the transition to last. In the <b>Duration</b> box, enter or select the necessary time value, measured in seconds.</li>
				<li>Press the <b>Preview</b> button to view the slide with the applied transition in the slide editing area.</li>
				<li>Specify how long you want the slide to be displayed until it advances to another one:
				    <ul>
				    <li><b>Start on click</b> – check this box if you don't want to restrict the time while the selected slide is being displayed. The slide will advance to another one only when you click on it with the mouse.</li>
				    <li><b>Delay</b> – use this option if you want the selected slide to be displayed for a specified time until it advances to the next one. Check this box and enter or select the necessary time value, measured in seconds.
				    <p class="note"><b>Note</b>: if you check only the <b>Delay</b> box, the slides will advance automatically in a specified time interval. If you check both the <b>Start on click</b> and the <b>Delay</b> boxes and set the delay value, the slides will advance automatically as well, but you will also be able to click a slide to advance from it to the next.</p>
				    </li>
				    </ul>
				</li>
			</ol>
			<p><b>To apply a transition to all the slides</b> in your presentation: perform the procedure described above and press the <b>Apply to All Slides</b> button.</p>
			<p><b>To delete a transition</b>: select the necessary slide and choose the <b>None</b> option in the <b>Effect</b> list.</p>
			<p><b>To delete all transitions</b>: select any slide, choose the <b>None</b> option in the <b>Effect</b> list and press the <b>Apply to All Slides</b> button.</p>
		</div>
	</body>
</html>
