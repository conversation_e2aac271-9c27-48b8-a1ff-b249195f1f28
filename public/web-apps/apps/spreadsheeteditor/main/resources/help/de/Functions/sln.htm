﻿<!DOCTYPE html>
<html>
	<head>
		<title>LIA-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>LIA-Funktion</h1>
			<p>Die Funktion <b>LIA</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Gibt die lineare Abschreibung eines Wirtschaftsgutes pro Periode zurück.</p>
			<p>Die Formelsyntax der Funktion <b>LIA</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>LIA(Ansch_Wert;Restwert;Nutzungsdauer)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
            <p style="text-indent: 50px;"><b><em>Ansch_Wert</em></b> sind die Anschaffungskosten des Anlageguts.</p>
            <p style="text-indent: 50px;"><b><em>Restwert</em></b> ist der Restwert, den das Anlagegut am Ende der Nutzungsdauer hat.</p>
            <p style="text-indent: 50px;"><b><em>Nutzungsdauer</em></b> ist die Anzahl der Perioden, über die das Wirtschaftsgut abgeschrieben wird.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>LIA</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>LIA</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="LIA-Funktion" src="../images/sln.png" /></p>
		</div>
	</body>
</html>