﻿<!DOCTYPE html>
<html>
	<head>
		<title>SEKUNDE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>SEKUNDE-Funktion</h1>
			<p>Die Funktion <b>SEKUNDE</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie wandelt eine fortlaufende Zahl (0-59) in Sekunde um.</p>
			<p>Die Formelsyntax der Funktion <b>SEKUNDE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>SEKUNDE(Zahl)</em></b></p> 
			<p>Das Argument <b><em>Zahl</em></b> kann manuell eingegeben werden oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p class="note"><b>Hinweis</b>: <b><em>Zahl</em></b> als Zeitangabe kann als Textzeichenfolge in Anführungszeichen (beispielsweise „13:39:15&quot;), als Dezimalzahl (beispielsweise 0,56; dieser Wert stellt 13:26:24 Uhr dar) oder als Ergebnis anderer Formeln oder Funktionen (beispielsweise das Ergebnis der Funktion JETZT im Standardformat - 9/26/12 13:39) angegeben werden.</p>
			<p>Anwendung der Funktion <b>SEKUNDE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>SEKUNDE</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="SEKUNDE-Funktion" src="../images/second.png" /></p>
		</div>
	</body>
</html>