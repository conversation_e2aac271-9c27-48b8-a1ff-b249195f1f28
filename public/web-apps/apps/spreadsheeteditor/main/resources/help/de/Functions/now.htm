﻿<!DOCTYPE html>
<html>
	<head>
		<title>JETZT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>JETZT-Funktion</h1>
			<p>Die Funktion <b>JETZT</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Mit dieser Funktion lässt sich das aktuelle Datum im Format und die aktuelle Uhrzeit im Format <em>MM/TT/JJ; hh:mm</em> wiedergeben. Für die Syntax dieser Funktion sind keine Argumente erforderlich.</p>
			<p>Die Formelsyntax der <b>JETZT</b>-Funktion lautet:</p> 
			<p style="text-indent: 150px;"><b><em>JETZT()</em></b></p> 
			<p><b>JETZT</b>-Funktion anwenden:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>JETZT</b>.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="JETZT-Funktion" src="../images/now.png" /></p>
		</div>
	</body>
</html>