﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZINS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ZINS-Funktion</h1>
			<p>Die Funktion <b>ZINS</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie berechnet den Zinssatz für eine Investition basierend auf einem konstanten Zahlungsplan.</p>
			<p>Die Formelsyntax der Funktion <b>ZINS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ZINS(Zzr, Rmz, Bw, Zw, [F], [Schätzwert])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zzr</em></b> ist die Gesamtanzahl der Zahlungszeiträume für die Investition.</p> 
				<p style="text-indent: 50px;"><b><em>Rmz</em></b> ist der Zahlungsbetrag.</p> 
				<p style="text-indent: 50px;"><b><em>Bw</em></b> ist der Barwert oder der heutige Gesamtwert einer Reihe zukünftiger Zahlungen.</p>
                <p style="text-indent: 50px;"><b><em>Zw</em></b> ist ein zukünftiger Wert (d. h. ein Restguthaben, das übrigbleibt, nachdem die letzte Zahlung getätigt wurde). Dieses Argument ist optional. Fehlt das Argument <b><em>F</em></b>, wird es als 0 (Null) angenommen.</p>
				<p style="text-indent: 50px;"><b><em>F</em></b> gibt an, wann die Zahlungen fällig sind. Dieses Argument ist optional. Fehlt das Argument oder ist auf 0 festgelegt, nimmt die Funktion an, dass die Zahlungen am Ende der Periode fällig sind. Ist <b><em>F</em></b> mit 1 angegeben, sind die Zahlungen zum Anfang der Periode fällig.</p>
                <p style="text-indent: 50px;"><b><em>Schätzwert</em></b> entspricht der Schätzung bezüglich der Höhe des Zinssatzes. Dieses Argument ist optional. Wird es ausgelassen, geht die Funktion vom <b><em>Schätzwert</em></b> 10 % aus.</p>
			<p class="note"><b>Hinweis:</b> Für alle Argumente gilt, dass Geldbeträge, die Sie auszahlen (zum Beispiel Spareinlagen), durch negative Zahlen und Geldbeträge, die Sie einnehmen (zum Beispiel Dividenden), durch positive Zahlen dargestellt werden. Sie sollten unbedingt darauf achten, dass Sie für <em>Schätzwert</em> und <em>Zzr</em> zueinander passende Zeiteinheiten verwenden. Verwenden Sie beispielsweise bei monatliche Zahlungen N%/12 für <em>Schätzwert</em> und N*12 für <em>Zzr</em>, für vierteljährliche Zahlungen N%/4 für <em>Schätzwert</em> and N*4 für <em>Zzr</em> und für jährliche Zahlungen N% für <em>Schätzwert</em> und N für <em>Zzr</em>.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>ZINS</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ZINS</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ZINS-Funktion" src="../images/rate.png" /></p>
		</div>
	</body>
</html>