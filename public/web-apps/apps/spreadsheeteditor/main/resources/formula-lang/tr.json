{"DATE": "TARİH", "DATEDIF": "DATEDIF", "DATEVALUE": "TARİHSAYISI", "DAY": "GÜN", "DAYS": "GÜNSAY", "DAYS360": "GÜN360", "EDATE": "SERİTARİH", "EOMONTH": "SERİAY", "HOUR": "SAAT", "ISOWEEKNUM": "ISOHAFTASAY", "MINUTE": "DAKİKA", "MONTH": "AY", "NETWORKDAYS": "TAMİŞGÜNÜ", "NETWORKDAYS.INTL": "TAMİŞGÜNÜ.ULUSL", "NOW": "ŞİMDİ", "SECOND": "SANİYE", "TIME": "ZAMAN", "TIMEVALUE": "ZAMANSAYISI", "TODAY": "BUGÜN", "WEEKDAY": "HAFTANINGÜNÜ", "WEEKNUM": "HAFTASAY", "WORKDAY": "İŞGÜNÜ", "WORKDAY.INTL": "İŞGÜNÜ.ULUSL", "YEAR": "YIL", "YEARFRAC": "YILORAN", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN2DEC", "BIN2HEX": "BIN2HEX", "BIN2OCT": "BIN2OCT", "BITAND": "BİTVE", "BITLSHIFT": "BİTSOLAKAYDIR", "BITOR": "BİTVEYA", "BITRSHIFT": "BİTSAĞAKAYDIR", "BITXOR": "BİTÖZELVEYA", "COMPLEX": "KARMAŞIK", "CONVERT": "ÇEVİR", "DEC2BIN": "DEC2BIN", "DEC2HEX": "DEC2HEX", "DEC2OCT": "DEC2OCT", "DELTA": "DELTA", "ERF": "HATAİŞLEV", "ERF.PRECISE": "HATAİŞLEV.DUYARLI", "ERFC": "TÜMHATAİŞLEV", "ERFC.PRECISE": "TÜMHATAİŞLEV.DUYARLI", "GESTEP": "BESINIR", "HEX2BIN": "HEX2BIN", "HEX2DEC": "HEX2DEC", "HEX2OCT": "HEX2OCT", "IMABS": "SANMUTLAK", "IMAGINARY": "SANAL", "IMARGUMENT": "SANBAĞ_DEĞİŞKEN", "IMCONJUGATE": "SANEŞLENEK", "IMCOS": "SANCOS", "IMCOSH": "SANCOSH", "IMCOT": "SANCOT", "IMCSC": "SANCSC", "IMCSCH": "SANCSCH", "IMDIV": "SANBÖL", "IMEXP": "SANÜS", "IMLN": "SANLN", "IMLOG10": "SANLOG10", "IMLOG2": "SANLOG2", "IMPOWER": "SANKUVVET", "IMPRODUCT": "SANÇARP", "IMREAL": "SANGERÇEK", "IMSEC": "SANSEC", "IMSECH": "SANSECH", "IMSIN": "SANSIN", "IMSINH": "SANSINH", "IMSQRT": "SANKAREKÖK", "IMSUB": "SANTOPLA", "IMSUM": "SANÇIKAR", "IMTAN": "SANTAN", "OCT2BIN": "OCT2BIN", "OCT2DEC": "OCT2DEC", "OCT2HEX": "OCT2HEX", "DAVERAGE": "VSEÇORT", "DCOUNT": "VSEÇSAY", "DCOUNTA": "VSEÇSAYDOLU", "DGET": "VAL", "DMAX": "VSEÇMAK", "DMIN": "VSEÇMİN", "DPRODUCT": "VSEÇÇARP", "DSTDEV": "VSEÇSTDSAPMA", "DSTDEVP": "VSEÇSTDSAPMAS", "DSUM": "VSEÇTOPLA", "DVAR": "VSEÇVAR", "DVARP": "VSEÇVARS", "CHAR": "DAMGA", "CLEAN": "TEMİZ", "CODE": "KOD", "CONCATENATE": "BİRLEŞTİR", "CONCAT": "ARALIKBİRLEŞTİR", "DOLLAR": "LİRA", "EXACT": "ÖZDEŞ", "FIND": "BUL", "FINDB": "FINDB", "FIXED": "SAYIDÜZENLE", "LEFT": "SOLDAN", "LEFTB": "LEFTB", "LEN": "UZUNLUK", "LENB": "LENB", "LOWER": "KÜÇÜKHARF", "MID": "PARÇAAL", "MIDB": "MIDB", "NUMBERVALUE": "SAYIDEĞERİ", "PROPER": "YAZIM.DÜZENİ", "REPLACE": "DEĞİŞTİR", "REPLACEB": "REPLACEB", "REPT": "YİNELE", "RIGHT": "SAĞDAN", "RIGHTB": "RIGHTB", "SEARCH": "MBUL", "SEARCHB": "SEARCHB", "SUBSTITUTE": "YERİNEKOY", "T": "M", "T.TEST": "T.TEST", "TEXT": "METNEÇEVİR", "TEXTJOIN": "METİNBİRLEŞTİR", "TREND": "EĞİLİM", "TRIM": "KIRP", "TRIMMEAN": "KIRPORTALAMA", "TTEST": "TTEST", "UNICHAR": "UNICODEKARAKTERİ", "UNICODE": "UNICODE", "UPPER": "BÜYÜKHARF", "VALUE": "SAYIYAÇEVİR", "AVEDEV": "ORTSAP", "AVERAGE": "ORTALAMA", "AVERAGEA": "ORTALAMAA", "AVERAGEIF": "EĞERORTALAMA", "AVERAGEIFS": "ÇOKEĞERORTALAMA", "BETADIST": "BETADAĞ", "BETAINV": "BETATERS", "BETA.DIST": "BETA.DAĞ", "BETA.INV": "BETA.TERS", "BINOMDIST": "BİNOMDAĞ", "BINOM.DIST": "BİNOM.DAĞ", "BINOM.DIST.RANGE": "BİNOM.DAĞ.ARALIK", "BINOM.INV": "BİNOM.TERS", "CHIDIST": "KİKAREDAĞ", "CHIINV": "KİKARETERS", "CHITEST": "KİKARETEST", "CHISQ.DIST": "KİKARE.DAĞ", "CHISQ.DIST.RT": "KİKARE.DAĞ.SAĞK", "CHISQ.INV": "KİKARE.TERS", "CHISQ.INV.RT": "KİKARE.TERS.SAĞK", "CHISQ.TEST": "KİKARE.TEST", "CONFIDENCE": "GÜVENİRLİK", "CONFIDENCE.NORM": "GÜVENİLİRLİK.NORM", "CONFIDENCE.T": "GÜVENİLİRLİK.T", "CORREL": "KORELASYON", "COUNT": "BAĞ_DEĞ_SAY", "COUNTA": "BAĞ_DEĞ_DOLU_SAY", "COUNTBLANK": "BOŞLUKSAY", "COUNTIF": "EĞERSAY", "COUNTIFS": "ÇOKEĞERSAY", "COVAR": "KOVARYANS", "COVARIANCE.P": "KOVARYANS.P", "COVARIANCE.S": "KOVARYANS.S", "CRITBINOM": "KRİTİKBİNOM", "DEVSQ": "SAPKARE", "EXPON.DIST": "ÜSTEL.DAĞ", "EXPONDIST": "ÜSTELDAĞ", "FDIST": "FDAĞ", "FINV": "FTERS", "FTEST": "FTEST", "F.DIST": "F.DAĞ", "F.DIST.RT": "F.DAĞ.SAĞK", "F.INV": "F.TER<PERSON>", "F.INV.RT": "F.TERS.SAĞK", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHERTERS", "FORECAST": "TAHMİN", "FORECAST.ETS": "TAHMİN.ETS", "FORECAST.ETS.CONFINT": "TAHMİN.ETS.GVNARAL", "FORECAST.ETS.SEASONALITY": "TAHMİN.ETS.MEVSİMSELLİK", "FORECAST.ETS.STAT": "TAHMİN.ETS.İSTAT", "FORECAST.LINEAR": "TAHMİN.DOĞRUSAL", "FREQUENCY": "SIKLIK", "GAMMA": "GAMA", "GAMMADIST": "GAMADAĞ", "GAMMA.DIST": "GAMA.DAĞ", "GAMMAINV": "GAMATERS", "GAMMA.INV": "GAMA.TERS", "GAMMALN": "GAMALN", "GAMMALN.PRECISE": "GAMALN.DUYARLI", "GAUSS": "GAUSS", "GEOMEAN": "GEOORT", "GROWTH": "BÜYÜME", "HARMEAN": "HARORT", "HYPGEOM.DIST": "HİPERGEOM.DAĞ", "HYPGEOMDIST": "HİPERGEOMDAĞ", "INTERCEPT": "KESMENOKTASI", "KURT": "BASIKLIK", "LARGE": "BÜYÜK", "LINEST": "DOT", "LOGEST": "LOT", "LOGINV": "LOGTERS", "LOGNORM.DIST": "LOGNORM.DAĞ", "LOGNORM.INV": "LOGNORM.TERS", "LOGNORMDIST": "LOGNORMDAĞ", "MAX": "MAK", "MAXA": "MAKA", "MAXIFS": "ÇOKEĞERMAK", "MEDIAN": "ORTANCA", "MIN": "MİN", "MINA": "MİNA", "MINIFS": "ÇOKEĞERMİN", "MODE": "ENÇOK_OLAN", "MODE.MULT": "ENÇOK_OLAN.ÇOK", "MODE.SNGL": "ENÇOK_OLAN.TEK", "NEGBINOM.DIST": "NEGBİNOM.DAĞ", "NEGBINOMDIST": "NEGBİNOMDAĞ", "NORM.DIST": "NORM.DAĞ", "NORM.INV": "NORM.TERS", "NORM.S.DIST": "NORM.S.DAĞ", "NORM.S.INV": "NORM.S.TERS", "NORMDIST": "NORMDAĞ", "NORMINV": "NORMTERS", "NORMSDIST": "NORMSDAĞ", "NORMSINV": "NORMSTERS", "PEARSON": "PEARSON", "PERCENTILE": "YÜZDEBİRLİK", "PERCENTILE.EXC": "YÜZDEBİRLİK.HRC", "PERCENTILE.INC": "YÜZDEBİRLİK.DHL", "PERCENTRANK": "YÜZDERANK", "PERCENTRANK.EXC": "YÜZDERANK.HRC", "PERCENTRANK.INC": "YÜZDERANK.DHL", "PERMUT": "PERMÜTASYON", "PERMUTATIONA": "PERMÜTASYONA", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.DAĞ", "PROB": "OLASILIK", "QUARTILE": "DÖRTTEBİRLİK", "QUARTILE.INC": "DÖRTTEBİRLİK.DHL", "QUARTILE.EXC": "DÖRTTEBİRLİK.HRC", "RANK.AVG": "RANK.ORT", "RANK.EQ": "RANK.EŞİT", "RANK": "RANK", "RSQ": "RKARE", "SKEW": "ÇARPIKLIK", "SKEW.P": "ÇARPIKLIK.P", "SLOPE": "EĞİM", "SMALL": "KÜÇÜK", "STANDARDIZE": "STANDARTLAŞTIRMA", "STDEV": "STDSAPMA", "STDEV.P": "STDSAPMA.P", "STDEV.S": "STDSAPMA.S", "STDEVA": "STDSAPMAA", "STDEVP": "STDSAPMAS", "STDEVPA": "STDSAPMASA", "STEYX": "STHYX", "TDIST": "TDAĞ", "TINV": "TTERS", "T.DIST": "T.DAĞ", "T.DIST.2T": "T.DAĞ.2K", "T.DIST.RT": "T.DAĞ.SAĞK", "T.INV": "T.TER<PERSON>", "T.INV.2T": "T.TERS.2K", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VARS", "VARPA": "VARSA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.DAĞ", "Z.TEST": "Z.TEST", "ZTEST": "ZTEST", "ACCRINT": "GERÇEKFAİZ", "ACCRINTM": "GERÇEKFAİZV", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "KUPONGÜNBD", "COUPDAYS": "KUPONGÜN", "COUPDAYSNC": "KUPONGÜNDSK", "COUPNCD": "KUPONGÜNSKT", "COUPNUM": "KUPONSAYI", "COUPPCD": "KUPONGÜNÖKT", "CUMIPMT": "TOPÖDENENFAİZ", "CUMPRINC": "TOPANAPARA", "DB": "AZALANBAKİYE", "DDB": "ÇİFTAZALANBAKİYE", "DISC": "İNDİRİM", "DOLLARDE": "LİRAON", "DOLLARFR": "LİRAKES", "DURATION": "SÜRE", "EFFECT": "ETKİN", "FV": "GD", "FVSCHEDULE": "GDPROGRAM", "INTRATE": "FAİZORANI", "IPMT": "FAİZTUTARI", "IRR": "İÇ_VERİM_ORANI", "ISPMT": "ISPMT", "MDURATION": "MSÜRE", "MIRR": "D_İÇ_VERİM_ORANI", "NOMINAL": "NOMİNAL", "NPER": "TAKSİT_SAYISI", "NPV": "NBD", "ODDFPRICE": "TEKYDEĞER", "ODDFYIELD": "TEKYÖDEME", "ODDLPRICE": "TEKSDEĞER", "ODDLYIELD": "TEKSÖDEME", "PDURATION": "PSÜRE", "PMT": "DEVRESEL_ÖDEME", "PPMT": "ANA_PARA_ÖDEMESİ", "PRICE": "DEĞER", "PRICEDISC": "DEĞERİND", "PRICEMAT": "DEĞERVADE", "PV": "BD", "RATE": "FAİZ_ORANI", "RECEIVED": "GETİRİ", "RRI": "GERÇEKLEŞENYATIRIMGETİRİSİ", "SLN": "DA", "SYD": "YAT", "TBILLEQ": "HTAHEŞ", "TBILLPRICE": "HTAHDEĞER", "TBILLYIELD": "HTAHÖDEME", "VDB": "DAB", "XIRR": "AİÇVERİMORANI", "XNPV": "ANBD", "YIELD": "ÖDEME", "YIELDDISC": "ÖDEMEİND", "YIELDMAT": "ÖDEMEVADE", "ABS": "MUTLAK", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "TOPLAMA", "ARABIC": "ARAP", "ASC": "ASC", "ASIN": "ASİN", "ASINH": "ASİNH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "TABAN", "CEILING": "TAVANAYUVARLA", "CEILING.MATH": "TAVANAYUVARLA.MATEMATİK", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "KOMBİNASYON", "COMBINA": "KOMBİNASYONA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "ONDALIK", "DEGREES": "DERECE", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "ÇİFT", "EXP": "ÜS", "FACT": "ÇARPINIM", "FACTDOUBLE": "ÇİFTFAKTÖR", "FLOOR": "TABANAYUVARLA", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "TABANAYUVARLA.MATEMATİK", "GCD": "OBEB", "INT": "TAMSAYI", "ISO.CEILING": "ISO.CEILING", "LCM": "OKEK", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "DETERMİNANT", "MINVERSE": "DİZEY_TERS", "MMULT": "DÇARP", "MOD": "MOD", "MROUND": "KYUVARLA", "MULTINOMIAL": "ÇOKTERİMLİ", "MUNIT": "BİRİMMATRİS", "ODD": "TEK", "PI": "Pİ", "POWER": "KUVVET", "PRODUCT": "ÇARPIM", "QUOTIENT": "BÖLÜM", "RADIANS": "RADYAN", "RAND": "S_SAYI_ÜRET", "RANDARRAY": "RASGDİZİ", "RANDBETWEEN": "RASTGELEARADA", "ROMAN": "ROMEN", "ROUND": "YUVARLA", "ROUNDDOWN": "AŞAĞIYUVARLA", "ROUNDUP": "YUKARIYUVARLA", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SERİTOPLA", "SIGN": "İŞARET", "SIN": "SİN", "SINH": "SİNH", "SQRT": "KAREKÖK", "SQRTPI": "KAREKÖKPİ", "SUBTOTAL": "ALTTOPLAM", "SUM": "TOPLA", "SUMIF": "ETOPLA", "SUMIFS": "ÇOKETOPLA", "SUMPRODUCT": "TOPLA.ÇARPIM", "SUMSQ": "TOPKARE", "SUMX2MY2": "TOPX2EY2", "SUMX2PY2": "TOPX2AY2", "SUMXMY2": "TOPXEY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "NSAT", "ADDRESS": "ADRES", "CHOOSE": "ELEMAN", "COLUMN": "SÜTUN", "COLUMNS": "SÜTUNSAY", "FORMULATEXT": "FORMÜLMETNİ", "HLOOKUP": "YATAYARA", "HYPERLINK": "KÖPRÜ", "INDEX": "İNDİS", "INDIRECT": "DOLAYLI", "LOOKUP": "ARA", "MATCH": "KAÇINCI", "OFFSET": "KAYDIR", "ROW": "SATIR", "ROWS": "SATIRSAY", "TRANSPOSE": "DEVRİK_DÖNÜŞÜM", "UNIQUE": "BENZERSİZ", "VLOOKUP": "DÜŞEYARA", "XLOOKUP": "ÇAPRAZARA", "CELL": "CELL", "ERROR.TYPE": "HATA.TİPİ", "ISBLANK": "EBOŞSA", "ISERR": "EHATA", "ISERROR": "EHATALIYSA", "ISEVEN": "ÇİFTMİ", "ISFORMULA": "EFORMÜLSE", "ISLOGICAL": "EMANTIKSALSA", "ISNA": "EYOKSA", "ISNONTEXT": "EMETİNDEĞİLSE", "ISNUMBER": "ESAYIYSA", "ISODD": "TEKMİ", "ISREF": "EREFSE", "ISTEXT": "EMETİNSE", "N": "S", "NA": "YOKSAY", "SHEET": "SAYFA", "SHEETS": "SAYFALAR", "TYPE": "TÜR", "AND": "VE", "FALSE": "YANLIŞ", "IF": "EĞER", "IFS": "ÇOKEĞER", "IFERROR": "EĞERHATA", "IFNA": "EĞERYOKSA", "NOT": "DEĞİL", "OR": "YADA", "SWITCH": "İLKEŞLEŞEN", "TRUE": "DOĞRU", "XOR": "ÖZELVEYA", "TEXTBEFORE": "ÖNCEKİMETİN", "TEXTAFTER": "SONRAKİMETİN", "TEXTSPLIT": "METİNBÖL", "WRAPROWS": "SATIRSAR", "VSTACK": "DÜŞEYYIĞ", "HSTACK": "YATAYYIĞ", "CHOOSEROWS": "SATIRSEÇ", "CHOOSECOLS": "SÜTUNSEÇ", "TOCOL": "SÜTUNA", "TOROW": "SATIRA", "WRAPCOLS": "SÜTUNSAR", "TAKE": "AL", "DROP": "BIRAK", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}