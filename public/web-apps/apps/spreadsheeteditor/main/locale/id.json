{"cancelButtonText": "Batalkan", "Common.Controllers.Chat.notcriticalErrorTitle": "Peringatan", "Common.Controllers.Chat.textEnterMessage": "Tuliskan pesan Anda di sini", "Common.Controllers.History.notcriticalErrorTitle": "Peringatan", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Area yang ditumpuk", "Common.define.chartData.textAreaStackedPer": "Area bertumpuk 100%", "Common.define.chartData.textBar": "Palang", "Common.define.chartData.textBarNormal": "<PERSON><PERSON> kolom klaster", "Common.define.chartData.textBarNormal3d": "Kolom cluster 3-D", "Common.define.chartData.textBarNormal3dPerspective": "Kolom 3-D", "Common.define.chartData.textBarStacked": "Diagram kolom bert<PERSON>", "Common.define.chartData.textBarStacked3d": "Kolom bertumpuk 3-D", "Common.define.chartData.textBarStackedPer": "<PERSON><PERSON><PERSON> 100%", "Common.define.chartData.textBarStackedPer3d": "<PERSON><PERSON><PERSON> 100% 3-D", "Common.define.chartData.textCharts": "Bagan", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Area yang ditumpuk - kolom klaster", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON> kolom klaster - garis", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON> kolom klaster - garis pada sumbu sekunder", "Common.define.chartData.textComboCustom": "Custom kombinasi", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "<PERSON><PERSON> batang klaster", "Common.define.chartData.textHBarNormal3d": "Diagram Batang Cluster 3-D", "Common.define.chartData.textHBarStacked": "Diagram batang bertumpuk", "Common.define.chartData.textHBarStacked3d": "Diagram batang bertumpuk 3-D", "Common.define.chartData.textHBarStackedPer": "Diagram batang bertumpuk 100%", "Common.define.chartData.textHBarStackedPer3d": "Diagram batang bertumpuk 100% 3-D", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Garis 3-D", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> dengan tanda", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Diagram garis bert<PERSON>uk", "Common.define.chartData.textLineStackedMarker": "Diagram garis bert<PERSON>uk dengan marker", "Common.define.chartData.textLineStackedPer": "<PERSON><PERSON> be<PERSON> 100%", "Common.define.chartData.textLineStackedPerMarker": "G<PERSON> bertumpuk 100% dengan marker", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textPie3d": "Pie 3-D", "Common.define.chartData.textPoint": "XY (<PERSON><PERSON><PERSON>)", "Common.define.chartData.textScatter": "Sebar", "Common.define.chartData.textScatterLine": "Diagram sebar dengan garis lurus", "Common.define.chartData.textScatterLineMarker": "Diagram sebar dengan garis lurus dan marker", "Common.define.chartData.textScatterSmooth": "Diagram sebar dengan garis mulus", "Common.define.chartData.textScatterSmoothMarker": "Diagram sebar dengan garis mulus dan marker", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textWinLossSpark": "Menang/Kalah", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Tanpa pengaturan format", "Common.define.conditionalData.text1Above": "1 std dev di atas", "Common.define.conditionalData.text1Below": "1 std dev di bawah", "Common.define.conditionalData.text2Above": "2 std dev di atas", "Common.define.conditionalData.text2Below": "2 std dev di bawah", "Common.define.conditionalData.text3Above": "3 std dev di atas", "Common.define.conditionalData.text3Below": "3 std dev di bawah", "Common.define.conditionalData.textAbove": "Di atas", "Common.define.conditionalData.textAverage": "<PERSON>a-rata", "Common.define.conditionalData.textBegins": "<PERSON><PERSON><PERSON> da<PERSON>", "Common.define.conditionalData.textBelow": "<PERSON> bawah", "Common.define.conditionalData.textBetween": "Diantara", "Common.define.conditionalData.textBlank": "Kosong", "Common.define.conditionalData.textBlanks": "Tidak berisi/kosong", "Common.define.conditionalData.textBottom": "<PERSON>wa<PERSON>", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Bar data", "Common.define.conditionalData.textDate": "Tanggal", "Common.define.conditionalData.textDuplicate": "Duplikat", "Common.define.conditionalData.textEnds": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEqAbove": "<PERSON>a dengan atau diatas", "Common.define.conditionalData.textEqBelow": "<PERSON>a dengan atau dibawah", "Common.define.conditionalData.textEqual": "<PERSON><PERSON> den<PERSON>", "Common.define.conditionalData.textError": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON> error", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreaterEq": "<PERSON><PERSON><PERSON> atau <PERSON>", "Common.define.conditionalData.textIconSets": "Icon sets", "Common.define.conditionalData.textLast7days": "Dalam 7 hari terakhir", "Common.define.conditionalData.textLastMonth": "bulan lalu", "Common.define.conditionalData.textLastWeek": "minggu lalu", "Common.define.conditionalData.textLess": "<PERSON><PERSON>", "Common.define.conditionalData.textLessEq": "<PERSON><PERSON> atau <PERSON>", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON> be<PERSON>", "Common.define.conditionalData.textNotBetween": "Tidak diantara", "Common.define.conditionalData.textNotBlanks": "Tidak ada yang kosong", "Common.define.conditionalData.textNotContains": "Tidak memiliki", "Common.define.conditionalData.textNotEqual": "Tidak Sama Den<PERSON>", "Common.define.conditionalData.textNotErrors": "<PERSON><PERSON><PERSON> be<PERSON> error", "Common.define.conditionalData.textText": "Teks", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON><PERSON> ini", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> ini", "Common.define.conditionalData.textToday": "<PERSON> ini", "Common.define.conditionalData.textTomorrow": "Besok", "Common.define.conditionalData.textTop": "Atas", "Common.define.conditionalData.textUnique": "Unik", "Common.define.conditionalData.textValue": "<PERSON><PERSON>", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentProcess": "<PERSON><PERSON> Aksen", "Common.define.smartArt.textAlternatingFlow": "<PERSON><PERSON>", "Common.define.smartArt.textAlternatingHexagons": "<PERSON><PERSON>", "Common.define.smartArt.textAlternatingPictureBlocks": "Blok Gambar Bolak-Balik", "Common.define.smartArt.textAlternatingPictureCircles": "Lingkaran Gambar <PERSON>", "Common.define.smartArt.textArchitectureLayout": "Tata Letak Arsitektur", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON>", "Common.define.smartArt.textAscendingPictureAccentProcess": "Proses <PERSON><PERSON><PERSON>", "Common.define.smartArt.textBalance": "Seimbang", "Common.define.smartArt.textBasicBendingProcess": "<PERSON><PERSON> <PERSON>", "Common.define.smartArt.textBasicBlockList": "Daftar Blok Dasar", "Common.define.smartArt.textBasicChevronProcess": "<PERSON>ses <PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "<PERSON><PERSON>", "Common.define.smartArt.textBasicProcess": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicRadial": "<PERSON><PERSON>", "Common.define.smartArt.textBasicTarget": "Target Dasar", "Common.define.smartArt.textBasicTimeline": "<PERSON><PERSON>", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Daftar Akses <PERSON>", "Common.define.smartArt.textBendingPictureBlocks": "Blok Gambar Meliuk", "Common.define.smartArt.textBendingPictureCaption": "Keterangan Gambar Meliuk", "Common.define.smartArt.textBendingPictureCaptionList": "Daftar Keterangan Gambar Meliuk", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Teks Semi-Transparan G<PERSON>", "Common.define.smartArt.textBlockCycle": "Lingkaran Blok", "Common.define.smartArt.textBubblePictureList": "Daftar Gambar Gelembung", "Common.define.smartArt.textCaptionedPictures": "Gambar Dengan Keterangan", "Common.define.smartArt.textChevronAccentProcess": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Common.define.smartArt.textChevronList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCircleAccentTimeline": "<PERSON><PERSON>", "Common.define.smartArt.textCircleArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textCirclePictureHierarchy": "<PERSON>era<PERSON><PERSON>", "Common.define.smartArt.textCircleProcess": "<PERSON><PERSON>", "Common.define.smartArt.textCircleRelationship": "Hubungan <PERSON>", "Common.define.smartArt.textCircularBendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textCircularPictureCallout": "Panggila<PERSON>", "Common.define.smartArt.textClosedChevronProcess": "Proses Ch<PERSON>ron <PERSON>", "Common.define.smartArt.textContinuousArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textContinuousBlockProcess": "Proses Blok Berkelanjutan", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textContinuousPictureList": "Daftar Gambar Berkelanjutan", "Common.define.smartArt.textConvergingArrows": "Panah <PERSON>at", "Common.define.smartArt.textConvergingRadial": "Radial Memusat", "Common.define.smartArt.textConvergingText": "Teks Memusat", "Common.define.smartArt.textCounterbalanceArrows": "Panah <PERSON>", "Common.define.smartArt.textCycle": "Siklus", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Daftar Blok Turun", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDivergingArrows": "Panah <PERSON>", "Common.define.smartArt.textDivergingRadial": "<PERSON><PERSON>", "Common.define.smartArt.textEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textFramedTextPicture": "Gambar Teks Terbingkai", "Common.define.smartArt.textFunnel": "Corong", "Common.define.smartArt.textGear": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGroupedList": "Daftar yang <PERSON>", "Common.define.smartArt.textHalfCircleOrganizationChart": "Bagan Organisasi Seteng<PERSON>n", "Common.define.smartArt.textHexagonCluster": "Kluster Segi Enam", "Common.define.smartArt.textHexagonRadial": "<PERSON><PERSON>", "Common.define.smartArt.textHierarchy": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalBulletList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalHierarchy": "Hierarki Horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hierarki Be<PERSON>abe<PERSON>", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hierarki Multi-Level Horizontal", "Common.define.smartArt.textHorizontalOrganizationChart": "Bagan Organisasi Horizontal", "Common.define.smartArt.textHorizontalPictureList": "Daftar Gambar Horizontal", "Common.define.smartArt.textIncreasingArrowProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textIncreasingCircleProcess": "Proses <PERSON><PERSON> Meningkat", "Common.define.smartArt.textInterconnectedBlockProcess": "Proses Blok yang <PERSON>ing Terhubung", "Common.define.smartArt.textInterconnectedRings": "<PERSON><PERSON><PERSON> yang <PERSON>hu<PERSON>", "Common.define.smartArt.textInvertedPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textLabeledHierarchy": "<PERSON>era<PERSON><PERSON>", "Common.define.smartArt.textLinearVenn": "Venn Linear", "Common.define.smartArt.textLinedList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Bagan <PERSON> dan <PERSON>", "Common.define.smartArt.textNestedTarget": "Target Bertumpuk", "Common.define.smartArt.textNondirectionalCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textOpposingArrows": "<PERSON><PERSON>", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Bagan Organisasi", "Common.define.smartArt.textOther": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Proses Berf<PERSON>", "Common.define.smartArt.textPicture": "Gambar", "Common.define.smartArt.textPictureAccentBlocks": "Blok Aksen Gambar", "Common.define.smartArt.textPictureAccentList": "Daftar Aksen G<PERSON>bar", "Common.define.smartArt.textPictureAccentProcess": "Proses Aksen <PERSON>", "Common.define.smartArt.textPictureCaptionList": "Daftar Keterangan Gambar", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "<PERSON><PERSON>", "Common.define.smartArt.textPictureLineup": "Deretan Gambar", "Common.define.smartArt.textPictureOrganizationChart": "Bagan Organisasi Gambar", "Common.define.smartArt.textPictureStrips": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPieProcess": "Proses <PERSON>", "Common.define.smartArt.textPlusAndMinus": "Plus dan Minus", "Common.define.smartArt.textProcess": "Proses", "Common.define.smartArt.textProcessArrows": "Panah Proses", "Common.define.smartArt.textProcessList": "Daftar Proses", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialCluster": "Kluster Radial", "Common.define.smartArt.textRadialCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialList": "<PERSON>ftar Radial", "Common.define.smartArt.textRadialPictureList": "<PERSON>ftar Gambar Radial", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Proses Acak ke Hasil", "Common.define.smartArt.textRelationship": "Hubungan", "Common.define.smartArt.textRepeatingBendingProcess": "Proses <PERSON><PERSON>", "Common.define.smartArt.textReverseList": "Daftar Terbalik", "Common.define.smartArt.textSegmentedCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textSegmentedProcess": "<PERSON>ses <PERSON>", "Common.define.smartArt.textSegmentedPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textSnapshotPictureList": "Daftar Gambar Snapshot", "Common.define.smartArt.textSpiralPicture": "Gambar S<PERSON>ral", "Common.define.smartArt.textSquareAccentList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStackedList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepDownProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepUpProcess": "Proses Meningkat", "Common.define.smartArt.textSubStepProcess": "Proses Sub-Langkah", "Common.define.smartArt.textTabbedArc": "<PERSON><PERSON>", "Common.define.smartArt.textTableHierarchy": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textTableList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTabList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTargetList": "Daftar Target", "Common.define.smartArt.textTextCycle": "Siklus <PERSON>", "Common.define.smartArt.textThemePictureAccent": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textThemePictureAlternatingAccent": "<PERSON><PERSON><PERSON>-Balik Gambar Te<PERSON>", "Common.define.smartArt.textThemePictureGrid": "<PERSON><PERSON>", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Daftar Aksen Gambar Be<PERSON>", "Common.define.smartArt.textTitledPictureBlocks": "Blok Gambar Berjudul", "Common.define.smartArt.textTitlePictureLineup": "Deretan <PERSON>", "Common.define.smartArt.textTrapezoidList": "Daftar Trapesium", "Common.define.smartArt.textUpwardArrow": "Panah ke Atas", "Common.define.smartArt.textVaryingWidthList": "<PERSON><PERSON><PERSON> den<PERSON>", "Common.define.smartArt.textVerticalAccentList": "Daftar Aksen Vertikal", "Common.define.smartArt.textVerticalArrowList": "<PERSON>ftar Panah Vertikal", "Common.define.smartArt.textVerticalBendingProcess": "Arah Proses Vertikal", "Common.define.smartArt.textVerticalBlockList": "Daftar Blok Vertikal", "Common.define.smartArt.textVerticalBoxList": "Daftar Kotak Vertikal", "Common.define.smartArt.textVerticalBracketList": "Daftar Tanda Kurung Vertikal", "Common.define.smartArt.textVerticalBulletList": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON>ev<PERSON>", "Common.define.smartArt.textVerticalCircleList": "Daftar Lingkaran Vertikal", "Common.define.smartArt.textVerticalCurvedList": "Daftar Kurva Vertikal", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalPictureAccentList": "Daftar Aksen Gambar Vertikal", "Common.define.smartArt.textVerticalPictureList": "Daftar Gambar Vertikal", "Common.define.smartArt.textVerticalProcess": "Proses Vertikal", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Dokumen terkunci untuk diedit. <PERSON>a dapat membuat perubahan dan menyimpannya sebagai salinan lokal nanti.", "Common.Translation.tipFileReadOnly": "Dokumen hanya dapat dibaca dan dikunci untuk pengeditan. Anda dapat membuat perubahan dan menyimpan salinan lokalnya nanti.", "Common.Translation.warnFileLocked": "File sedang diedit di aplikasi lain. Anda bisa melanjutkan edit dan menyimpannya sebagai salinan.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>t salinan", "Common.Translation.warnFileLockedBtnView": "<PERSON>uka untuk dilihat", "Common.UI.ButtonColored.textAutoColor": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ButtonColored.textNewColor": "Tam<PERSON><PERSON> warna khusus baru", "Common.UI.ComboBorderSize.txtNoBorders": "Tidak ada pembatas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Tidak ada pembatas", "Common.UI.ComboDataView.emptyComboText": "Tidak ada model", "Common.UI.ExtendedColorDialog.addButtonText": "Tambahkan", "Common.UI.ExtendedColorDialog.textCurrent": "Saat ini", "Common.UI.ExtendedColorDialog.textHexErr": "Input yang dimasukkan salah.<br><PERSON><PERSON><PERSON> masukkan input antara 000000 dan FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Input yang <PERSON>a masukkan salah.<br><PERSON><PERSON><PERSON> masukkan input numerik antara 0 dan 255.", "Common.UI.HSBColorPicker.textNoColor": "Tidak ada <PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Sembunyikan password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON><PERSON> password", "Common.UI.SearchBar.textFind": "Temukan", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON> pengaturan lan<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "<PERSON><PERSON>", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON> hasil", "Common.UI.SearchDialog.textMatchCase": "Harus sama persis", "Common.UI.SearchDialog.textReplaceDef": "Tuliskan teks pengganti", "Common.UI.SearchDialog.textSearchStart": "Tuliskan teks Anda di sini", "Common.UI.SearchDialog.textTitle": "<PERSON>i dan ganti", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> kata saja", "Common.UI.SearchDialog.txtBtnHideReplace": "Sembunyikan Replace", "Common.UI.SearchDialog.txtBtnReplace": "Ganti", "Common.UI.SearchDialog.txtBtnReplaceAll": "Ganti semua", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.UI.SynchronizeTip.textSynchronize": "Dokumen telah diubah oleh pengguna lain.<br><PERSON>lakan klik untuk menyimpan perubahan dan memuat ulang pembaruan.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Sama seperti sistem", "Common.UI.Window.cancelButtonText": "Batalkan", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Tidak", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Informasi", "Common.UI.Window.textWarning": "Peringatan", "Common.UI.Window.yesButtonText": "Ya", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "alamat:", "Common.Views.About.txtLicensee": "PEMEGANG LISENSI", "Common.Views.About.txtLicensor": "PEMBERI LISENSI", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel:", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Tambahkan", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Terap<PERSON> saat anda bekerja", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat sambil Anda mengetik", "Common.Views.AutoCorrectDialog.textBy": "oleh", "Common.Views.AutoCorrectDialog.textDelete": "Hapus", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet dan jalur jaringan dengan hyperlink.", "Common.Views.AutoCorrectDialog.textMathCorrect": "AutoCorrect Matematika", "Common.Views.AutoCorrectDialog.textNewRowCol": "Sertakan kolom dan baris baru di tabel", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON> yang <PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Ekspresi ini merupakan ekspresi matematika. Ekspresi ini tidak akan dimiringkan secara otomatis.", "Common.Views.AutoCorrectDialog.textReplace": "Ganti", "Common.Views.AutoCorrectDialog.textReplaceText": "Ganti sambil Anda men<PERSON>ik", "Common.Views.AutoCorrectDialog.textReplaceType": "Ganti teks saat Anda mengetik", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON>ur ulang kembali ke awal", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON><PERSON>i yang diterima harus memiliki huruf A sampai Z, huruf besar atau huruf kecil.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON> eksp<PERSON>i yang Anda tambahkan akan dihilangkan dan yang sudah terhapus akan dikembalikan. A<PERSON>kah Anda ingin melanjutkan?", "Common.Views.AutoCorrectDialog.warnReplace": "Entri autocorrect untuk %1 sudah ada. Apakah Anda ingin menggantinya?", "Common.Views.AutoCorrectDialog.warnReset": "Semua autocorrect yang Anda tambahkan akan dihilangkan dan yang sudah diganti akan dikembalikan ke nilai awalnya. Apakah Anda ingin melanjutkan?", "Common.Views.AutoCorrectDialog.warnRestore": "Entri autocorrect untuk %1 akan di reset ke nilai awal. <PERSON><PERSON><PERSON>h Anda ingin melanjutkan?", "Common.Views.Chat.textSend": "<PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Penulis A sampai Z", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON> Z sampai A", "Common.Views.Comments.mniDateAsc": "Tertua", "Common.Views.Comments.mniDateDesc": "Terbaru", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON>", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON> atas", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON> bawah", "Common.Views.Comments.textAdd": "Tambahkan", "Common.Views.Comments.textAddComment": "Tambahkan Komentar", "Common.Views.Comments.textAddCommentToDoc": "Tambahkan komentar untuk dokumen", "Common.Views.Comments.textAddReply": "Tambah<PERSON>", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON>", "Common.Views.Comments.textCancel": "Batalkan", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Tutup komentar", "Common.Views.Comments.textComments": "Komentar", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Tuliskan komentar Anda di sini", "Common.Views.Comments.textHintAddComment": "Tambahkan komentar", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Diselesaikan", "Common.Views.Comments.textSort": "Sortir komentar", "Common.Views.Comments.textViewResolved": "Anda tidak memiliki izin membuka kembali komentar", "Common.Views.Comments.txtEmpty": "Tidak ada komentar pada sheet.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>an tampilkan pesan ini lagi", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON>, potong dan tempel menggunakan tombol editor toolbar dan menu konteks dapat dilakukan hanya dengan tab editor ni saja.<br><br>Untuk menyalin atau menempel ke atau dari aplikasi di luar tab editor, gunakan kombinasi tombol keyboard berikut ini:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON>, Potong dan <PERSON>", "Common.Views.CopyWarningDialog.textToCopy": "untuk <PERSON>", "Common.Views.CopyWarningDialog.textToCut": "untuk Potong", "Common.Views.CopyWarningDialog.textToPaste": "untuk Tempel", "Common.Views.DocumentAccessDialog.textLoading": "Memuat...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.EditNameDialog.textLabel": "Label:", "Common.Views.EditNameDialog.textLabelError": "Label tidak boleh kosong.", "Common.Views.Header.labelCoUsersDescr": "Pengguna yang sedang mengedit file:", "Common.Views.Header.textAddFavorite": "Tandai sebagai favorit", "Common.Views.Header.textAdvSettings": "Pen<PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "<PERSON><PERSON> Dokumen", "Common.Views.Header.textCompactView": "Sembunyikan Toolbar", "Common.Views.Header.textHideLines": "Sembunyikan Mistar", "Common.Views.Header.textHideStatusBar": "Gabungkan sheet dan bar status", "Common.Views.Header.textRemoveFavorite": "Hilangkan da<PERSON>", "Common.Views.Header.textSaveBegin": "Menyimpan...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON> te<PERSON>", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON> te<PERSON>", "Common.Views.Header.textShare": "Bagikan", "Common.Views.Header.textZoom": "Pembesaran", "Common.Views.Header.tipAccessRights": "Atur perizinan akses dokumen", "Common.Views.Header.tipDownload": "Unduh File", "Common.Views.Header.tipGoEdit": "Edit file saat ini", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Cetak cepat", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Simpan", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "Batalkan", "Common.Views.Header.tipUndock": "Buka dock ke jendela terpisah", "Common.Views.Header.tipUsers": "<PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON>", "Common.Views.Header.tipViewUsers": "Tampilkan user dan atur hak akses dokumen", "Common.Views.Header.txtAccessRights": "Ubah hak akses", "Common.Views.Header.txtRename": "Ganti nama", "Common.Views.History.textCloseHistory": "Tutup riwayat", "Common.Views.History.textHide": "Collapse", "Common.Views.History.textHideAll": "Sembunyikan <PERSON> perubahan", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Tampilkan <PERSON> perubahan", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Tempel URL gambar:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ruas ini harus berupa URL dengan format \"http://www.contoh.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Start slideshow", "Common.Views.ListSettingsDialog.textFromFile": "Dari file", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromUrl": "Dari URL", "Common.Views.ListSettingsDialog.textNumbering": "Bernomor", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "Ubah butir", "Common.Views.ListSettingsDialog.txtBullet": "Butir", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Gambar", "Common.Views.ListSettingsDialog.txtImport": "Impor", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON>ir baru", "Common.Views.ListSettingsDialog.txtNewImage": "Gambar baru", "Common.Views.ListSettingsDialog.txtNone": "Tidak ada", "Common.Views.ListSettingsDialog.txtOfText": "% dari teks", "Common.Views.ListSettingsDialog.txtSize": "Ukuran", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> pada", "Common.Views.ListSettingsDialog.txtSymbol": "Simbol", "Common.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> daftar", "Common.Views.ListSettingsDialog.txtType": "Tipe", "Common.Views.OpenDialog.closeButtonText": "Tutup file", "Common.Views.OpenDialog.textInvalidRange": "Rentang sel tidak valid", "Common.Views.OpenDialog.textSelectData": "Pilih data", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Titik dua", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Pembatas", "Common.Views.OpenDialog.txtDestData": "Pilih lokasi untuk data", "Common.Views.OpenDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "Common.Views.OpenDialog.txtEncoding": "Enkoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "Password salah.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON>n kata sandi untuk buka file", "Common.Views.OpenDialog.txtOther": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON> Anda memasukkan password dan membuka file, password file saat ini akan di reset.", "Common.Views.OpenDialog.txtSemicolon": "Titik koma", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Pilih %1 opsi", "Common.Views.OpenDialog.txtTitleProtected": "File terproteksi", "Common.Views.PasswordDialog.txtDescription": "Buat password untuk melindungi dokumen ini", "Common.Views.PasswordDialog.txtIncorrectPwd": "Password konfirmasi tidak sama", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON> password", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON> kata sandi", "Common.Views.PasswordDialog.txtWarning": "Peringatan: Tidak bisa dipulihkan jika Anda kehilangan atau lupa kata sandi. Simpan di tempat yang aman.", "Common.Views.PluginDlg.textLoading": "Memuat", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Tutup plugin", "Common.Views.Plugins.textLoading": "Memuat", "Common.Views.Plugins.textStart": "<PERSON><PERSON>", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON> dengan password", "Common.Views.Protection.hintDelPwd": "<PERSON><PERSON> kata sandi", "Common.Views.Protection.hintPwd": "Ganti atau hapus password", "Common.Views.Protection.hintSignature": "Tambah tanda tangan digital atau garis tanda tangan", "Common.Views.Protection.txtAddPwd": "Tambah password", "Common.Views.Protection.txtChangePwd": "Ubah kata sandi", "Common.Views.Protection.txtDeletePwd": "Nama file", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Tambah tanda tangan digital", "Common.Views.Protection.txtSignature": "<PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON> garis tanda tangan", "Common.Views.RenameDialog.textName": "Nama file", "Common.Views.RenameDialog.txtInvalidName": "Nama file tidak boleh berisi karakter seperti:", "Common.Views.ReviewChanges.hintNext": "<PERSON> berik<PERSON>", "Common.Views.ReviewChanges.hintPrev": "<PERSON>han sebelumnya", "Common.Views.ReviewChanges.strFast": "Cepat", "Common.Views.ReviewChanges.strFastDesc": "Co-editing real-time. <PERSON><PERSON><PERSON> disimpan otomatis.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Gunakan tombol 'Simpan' untuk menyelaraskan perubahan yang Anda dan orang lain buat.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON> perubahan saat ini", "Common.Views.ReviewChanges.tipCoAuthMode": "Atur mode co-editing", "Common.Views.ReviewChanges.tipCommentRem": "Hilangkan komentar", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Hilangkan komentar saat ini", "Common.Views.ReviewChanges.tipCommentResolve": "Selesaikan komentar", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Selesaikan komentar saat ini", "Common.Views.ReviewChanges.tipHistory": "Tampilkan riwayat versi", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON> perubahan saat ini", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Pilih mode yang per<PERSON>hannya ingin Anda tampilkan", "Common.Views.ReviewChanges.tipSetDocLang": "Atur Bahasa Dokumen", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipSharing": "Atur perizinan akses dokumen", "Common.Views.ReviewChanges.txtAccept": "Terima", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON> semua per<PERSON>han", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Mode Edit <PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "Hilangkan semua komentar", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Hilangkan komentar saat ini", "Common.Views.ReviewChanges.txtCommentRemMy": "Hilangkan komentar saya", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Hilangkan komentar saya saat ini", "Common.Views.ReviewChanges.txtCommentRemove": "Hapus", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON> semua komentar", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Selesaikan komentar saat ini", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON>ai<PERSON> komentar saya", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Se<PERSON>aikan komentar saya saat ini", "Common.Views.ReviewChanges.txtDocLang": "Bahasa", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON><PERSON> (Preview)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Riwayat Versi", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> (Editing)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Selanjutnya", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> (Preview)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Sebelumnya", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSharing": "Bagikan", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Mode Tampilan", "Common.Views.ReviewPopover.textAdd": "Tambahkan", "Common.Views.ReviewPopover.textAddReply": "Tambah<PERSON>", "Common.Views.ReviewPopover.textCancel": "Batalkan", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Tuliskan komentar Anda di sini", "Common.Views.ReviewPopover.textMention": "+mention akan memberikan akses ke dokumen dan mengirimkan email", "Common.Views.ReviewPopover.textMentionNotify": "+mention akan menging<PERSON>kan user lewat email", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Anda tidak memiliki izin membuka kembali komentar", "Common.Views.ReviewPopover.txtDeleteTip": "Hapus", "Common.Views.ReviewPopover.txtEditTip": "Sunting", "Common.Views.SaveAsDlg.textLoading": "Memuat", "Common.Views.SaveAsDlg.textTitle": "Folder untuk simpan", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON><PERSON> kolom", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON><PERSON> baris", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON> huruf besar-kecil", "Common.Views.SearchPanel.textCell": "<PERSON>l", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "<PERSON>kumen telah diubah.", "Common.Views.SearchPanel.textFind": "Temukan", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON><PERSON> dan ganti", "Common.Views.SearchPanel.textFormula": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFormulas": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON> isi sel", "Common.Views.SearchPanel.textLookIn": "<PERSON><PERSON>", "Common.Views.SearchPanel.textMatchUsingRegExp": "Cocokkan dengan ekspresi reguler", "Common.Views.SearchPanel.textName": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoMatches": "Tidak ada yang cocok", "Common.Views.SearchPanel.textNoSearchResults": "Tidak ada hasil pencarian", "Common.Views.SearchPanel.textReplace": "Ganti", "Common.Views.SearchPanel.textReplaceAll": "Ganti Semua", "Common.Views.SearchPanel.textReplaceWith": "Ganti dengan", "Common.Views.SearchPanel.textSearch": "<PERSON><PERSON>", "Common.Views.SearchPanel.textSearchAgain": "{0}Lakukan pencarian baru{1} untuk hasil yang akurat.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchOptions": "<PERSON><PERSON> pencarian", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON> pencarian: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON>lih Ren<PERSON>g data", "Common.Views.SearchPanel.textSheet": "Sheet", "Common.Views.SearchPanel.textSpecificRange": "Rentang spesifik", "Common.Views.SearchPanel.textTooManyResults": "<PERSON><PERSON><PERSON>u banyak hasil untuk ditampilkan di sini", "Common.Views.SearchPanel.textValue": "<PERSON><PERSON>", "Common.Views.SearchPanel.textValues": "<PERSON><PERSON>", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> kata saja", "Common.Views.SearchPanel.textWithin": "<PERSON>", "Common.Views.SearchPanel.textWorkbook": "Workbook", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "<PERSON><PERSON>", "Common.Views.SelectFileDlg.textLoading": "Memuat", "Common.Views.SelectFileDlg.textTitle": "Pilih sumber data", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ganti", "Common.Views.SignDialog.textInputName": "<PERSON><PERSON><PERSON><PERSON> nama penanda<PERSON>", "Common.Views.SignDialog.textItalic": "Miring", "Common.Views.SignDialog.textNameError": "<PERSON>a penanda<PERSON>gan tidak boleh kosong.", "Common.Views.SignDialog.textPurpose": "Tu<PERSON>an menandatangani dokumen ini", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "Tandatangan terlihat seperti", "Common.Views.SignDialog.textTitle": "Tanda Tangan Dokumen", "Common.Views.SignDialog.textUseImage": "atau klik '<PERSON>lih G<PERSON>' untuk menjadikan gambar sebagai tandatangan", "Common.Views.SignDialog.textValid": "Valid dari %1 sampai %2", "Common.Views.SignDialog.tipFontName": "Nama font", "Common.Views.SignDialog.tipFontSize": "Ukuran font", "Common.Views.SignSettingsDialog.textAllowComment": "Izinkan penandatangan untuk menambahkan komentar di dialog tanda tangan", "Common.Views.SignSettingsDialog.textDefInstruction": "Sebelum menandatangani dokumen ini, pastikan konten yang akan Anda tanda tangani sudah benar.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail penandatangan yang disarankan", "Common.Views.SignSettingsDialog.textInfoName": "Penandatangan yang disarankan", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON><PERSON> penanda<PERSON>gan yang disarankan", "Common.Views.SignSettingsDialog.textInstructions": "Instruksi untuk penandatangan", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON> tanggal di garis tandatangan", "Common.Views.SignSettingsDialog.textTitle": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Nilai Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Tanda hak cipta", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON> ganda penutup", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON> ganda pembuka", "Common.Views.SymbolTableDialog.textEllipsis": "Ellipsis horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Tanda sambung tak-putus", "Common.Views.SymbolTableDialog.textNBSpace": "Spasi tak-putus", "Common.Views.SymbolTableDialog.textPilcrow": "Simbol Pilcrow", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Rentang", "Common.Views.SymbolTableDialog.textRecent": "Simbol yang baru digunakan", "Common.Views.SymbolTableDialog.textRegistered": "Tandatangan Teregistrasi", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON> tunggal penutup", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON> shortcut", "Common.Views.SymbolTableDialog.textSHyphen": "Soft Hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON><PERSON> tunggal pembuka", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simbol", "Common.Views.SymbolTableDialog.textTitle": "Simbol", "Common.Views.SymbolTableDialog.textTradeMark": "Simbol merk dagang ", "Common.Views.UserNameDialog.textDontShow": "<PERSON>an tanya saya lagi", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label tidak boleh kosong.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "Anda perlu menyatakan URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Teks ke kolom", "SSE.Controllers.DataTab.txtDataValidation": "Validasi data", "SSE.Controllers.DataTab.txtErrorExternalLink": "Kesalahan: pem<PERSON><PERSON><PERSON><PERSON> gagal", "SSE.Controllers.DataTab.txtExpand": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Data di sebelah pilihan tidak akan dihilangkan. Apakah Anda ingin memperluas pilihan untuk menyertakan data yang berdekatan atau lanjut sortasi dengan hanya sel yang dipilih?", "SSE.Controllers.DataTab.txtExtendDataValidation": "<PERSON><PERSON><PERSON> berisi beberapa sel tanpa pengaturan Validasi Data.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin memper<PERSON> Validasi Data ke sel ini?", "SSE.Controllers.DataTab.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DataTab.txtRemDuplicates": "Ha<PERSON> duplik<PERSON>", "SSE.Controllers.DataTab.txtRemoveDataValidation": "<PERSON><PERSON><PERSON> berisi lebih dari satu jenis validasi.<br><PERSON><PERSON> pengaturan saat ini dan lanjutkan?", "SSE.Controllers.DataTab.txtRemSelected": "Hapus di pilihan", "SSE.Controllers.DataTab.txtUrlTitle": "Paste URL data", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Tengah", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "Hapus Baris", "SSE.Controllers.DocumentHolder.deleteText": "Hapus", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Link referensi tidak ada. <PERSON><PERSON><PERSON> koreksi atau hapus link.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Baris di Atas", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "<PERSON>sip<PERSON>", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Peringatan", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opsi AutoCorrect", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON> {0} simbol ({1} piksel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Tinggi Baris {0} points ({1} pixels)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Klik link untuk membuka atau klik dan tahan tombol mouse untuk memilih sel.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Sisipkan kolom ke sisi kiri", "SSE.Controllers.DocumentHolder.textInsertTop": "Sisipkan baris di atas", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.textStopExpand": "Stop memperluas tabel otomatis", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Di atas rata-rata", "SSE.Controllers.DocumentHolder.txtAddBottom": "Tambah pembatas bawah", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Tambah bar pecahan", "SSE.Controllers.DocumentHolder.txtAddHor": "Tambah garis horizontal", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON> garis kiri bawah", "SSE.Controllers.DocumentHolder.txtAddLeft": "Tambah pembatas kiri", "SSE.Controllers.DocumentHolder.txtAddLT": "Tambah garis kiri atas", "SSE.Controllers.DocumentHolder.txtAddRight": "Tambah pembatas kiri", "SSE.Controllers.DocumentHolder.txtAddTop": "Tambah pembatas atas", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON> garis vertikal", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON>a dengan karakter", "SSE.Controllers.DocumentHolder.txtAll": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Kembalikan seluruh isi tabel atau kolom tabel tertentu termasuk header kolom, data dan total baris", "SSE.Controllers.DocumentHolder.txtAnd": "dan", "SSE.Controllers.DocumentHolder.txtBegins": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Controllers.DocumentHolder.txtBelowAve": "<PERSON> bawah rata-rata", "SSE.Controllers.DocumentHolder.txtBlanks": "(Kosong)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Properti pembatas", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON>wa<PERSON>", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON>a kolom", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Tautan disalin ke papan klip", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Kembalikan sel data dari tabel atau kolom tabel yang dihitung", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Kurangi ukuran argumen", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Hapus argumen", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Hapus break manual", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Hapus karakter terlampir", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON> karakter dan separator terlampir", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON> char", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Hapus radikal", "SSE.Controllers.DocumentHolder.txtEnds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEquals": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "<PERSON>a dengan warna sel", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "<PERSON>a dengan warna font", "SSE.Controllers.DocumentHolder.txtExpand": "<PERSON><PERSON><PERSON> dan sortir", "SSE.Controllers.DocumentHolder.txtExpandSort": "Data di sebelah pilihan tidak akan disortasi. Apakah Anda ingin memperluas pilihan untuk menyertakan data yang berdekatan atau lanjut sortasi dengan hanya sel yang dipilih?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON>wa<PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Atas", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Ubah ke pecahan linear", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> ke pecahan", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> ke pecahan bert<PERSON>uk", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON><PERSON> atau <PERSON>", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON>kter di atas teks", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON> di bawah teks", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Kembalikan header kolom untuk tabel atau tabel kolom spesifik", "SSE.Controllers.DocumentHolder.txtHeight": "Tingg<PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "Sembunyikan pembatas bawah", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai batas bawah", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Sembunyikan tanda kurung tutup", "SSE.Controllers.DocumentHolder.txtHideDegree": "Sembunyikan degree", "SSE.Controllers.DocumentHolder.txtHideHor": "Sembunyikan garis horizontal", "SSE.Controllers.DocumentHolder.txtHideLB": "Sembunyikan garis bawah kiri", "SSE.Controllers.DocumentHolder.txtHideLeft": "Sembunyikan pembatas kiri", "SSE.Controllers.DocumentHolder.txtHideLT": "Sembunyikan garis atas kiri", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Sembunyikan tanda kurung buka", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Sembunyikan placeholder", "SSE.Controllers.DocumentHolder.txtHideRight": "Sembunyikan pembatas kanan", "SSE.Controllers.DocumentHolder.txtHideTop": "Sembunyikan pembatas atas", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Se<PERSON><PERSON><PERSON><PERSON> nilai batas atas", "SSE.Controllers.DocumentHolder.txtHideVer": "Sembunyikan garis vertikal", "SSE.Controllers.DocumentHolder.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Tingkatkan ukuran argumen", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Sisipkan argumen setelah", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Sisipkan argumen sebelum", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Sisi<PERSON>kan break manual", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON><PERSON> persa<PERSON>an set<PERSON>h", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "<PERSON>sip<PERSON> persamaan sebelum", "SSE.Controllers.DocumentHolder.txtItems": "items", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Pertahankan hanya teks", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtLessEquals": "<PERSON><PERSON> atau <PERSON>", "SSE.Controllers.DocumentHolder.txtLimitChange": "Ganti lokasi limit", "SSE.Controllers.DocumentHolder.txtLimitOver": "Batasi di atas teks", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Batasi di bawah teks", "SSE.Controllers.DocumentHolder.txtLockSort": "Ditemukan data di sebelah pilihan <PERSON>, tapi, <PERSON>a tidak memiliki izin untuk mengubah sel tersebut.<br><PERSON><PERSON><PERSON><PERSON> Anda ingin tetap lanjut dengan pilihan saat ini?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Sesuaikan tanda kurung dengan tinggi argumen", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON>a matriks", "SSE.Controllers.DocumentHolder.txtNoChoices": "Tidak ada pilihan untuk mengisi sel.<br><PERSON><PERSON> nilai teks dari kolom yang bisa dipilih untuk diganti.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Tidak diawali dari", "SSE.Controllers.DocumentHolder.txtNotContains": "Tidak memiliki", "SSE.Controllers.DocumentHolder.txtNotEnds": "Tidak <PERSON> dengan", "SSE.Controllers.DocumentHolder.txtNotEquals": "Tidak sama dengan", "SSE.Controllers.DocumentHolder.txtOr": "atau", "SSE.Controllers.DocumentHolder.txtOverbar": "Bar di atas teks", "SSE.Controllers.DocumentHolder.txtPaste": "Tempel", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula tanpa pembatas", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + lebar kolom", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Pemformatan tujuan", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Paste hanya pemformatan", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + nomor format", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Paste hanya formula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + semua format", "SSE.Controllers.DocumentHolder.txtPasteLink": "Paste link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Merge format bersyarat", "SSE.Controllers.DocumentHolder.txtPastePicture": "Gambar", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Pemformatan sumber", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpose", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Nilai + semua formatting", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Nilai + nomor format", "SSE.Controllers.DocumentHolder.txtPasteValues": "Paste hanya nilai", "SSE.Controllers.DocumentHolder.txtPercent": "persen", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Redo perluasan otomatis tabel", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Hilangkan bar pecahan", "SSE.Controllers.DocumentHolder.txtRemLimit": "Hilangkan limit", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Hilangkan aksen karakter", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Hilangkan diagram batang", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghilangkan tandatangan ini?<br>Proses tidak bisa dikembalikan.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Hilangkan skrip", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Hilangkan subscript", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Hilangkan superscript", "SSE.Controllers.DocumentHolder.txtRowHeight": "Tinggi Baris", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "<PERSON>ripts setelah teks", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts sebelum teks", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON> batas bawah", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON> kurung penutup", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON> degree", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON> kurung pembuka", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Tampilkan placeholder", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON> batas atas", "SSE.Controllers.DocumentHolder.txtSorting": "Sorting", "SSE.Controllers.DocumentHolder.txtSortSelected": "Sortir dipilih", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Regangkan dalam kurung", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Pilih hanya baris ini dari kolom yang ditentukan", "SSE.Controllers.DocumentHolder.txtTop": "Atas", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Kembalikan total baris dari tabel atau kolom tabel spesifik", "SSE.Controllers.DocumentHolder.txtUnderbar": "Bar di bawah teks", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Undo perluasan otomatis tabel", "SSE.Controllers.DocumentHolder.txtUseTextImport": "<PERSON><PERSON><PERSON> wahana pandu impor teks", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Klik link ini bisa berbahaya untuk perangkat dan data Anda.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin tetap lanjut?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "Ku<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Database", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "<PERSON><PERSON> dan <PERSON>", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finansial", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informasi", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 terakhir digunakan", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logikal", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Lookup dan refer<PERSON>i", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematika dan trigonometri", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistikal", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Teks dan data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Spreadsheet tanpa nama", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON><PERSON> kolom", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON> baris", "SSE.Controllers.LeftMenu.textFormulas": "Formulas", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON> isi sel", "SSE.Controllers.LeftMenu.textLoadHistory": "Loading versi riwayat...", "SSE.Controllers.LeftMenu.textLookin": "Melihat <PERSON>alam", "SSE.Controllers.LeftMenu.textNoTextFound": "Data yang Anda cari tidak ditemukan. <PERSON>lakan atur opsi pencarian <PERSON>a.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Penggantian telah di<PERSON>n. Ada {0} yang di<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Pencarian telah <PERSON>. <PERSON> {0} yang diganti {0}", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSheet": "Sheet", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "Peringatan", "SSE.Controllers.LeftMenu.textWithin": "<PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "Workbook", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ji<PERSON> Anda lanjut simpan dengan format ini, semua fitur kecuali teks akan hilang.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin melanjutkan?", "SSE.Controllers.Main.confirmAddCellWatches": "<PERSON><PERSON>an ini akan men<PERSON>bahkan {0} pengawas sel.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin melanjutkan?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Tindakan ini hanya akan menambahkan {0} pengawas sel atas alasan penghematan memori.<br><PERSON><PERSON><PERSON><PERSON> Anda akan melanjutkan?", "SSE.Controllers.Main.confirmMaxChangesSize": "Ukuran tindakan melebihi batas yang ditetapkan untuk server <PERSON><PERSON>.<br><PERSON><PERSON> \"Batalkan\" untuk membatalkan tindakan terakhir Anda atau tekan \"Lanjutkan\" untuk menyimpan tindakan secara lokal (Anda perlu mengunduh file atau menyalin isinya untuk memastikan tidak ada yang hilang).", "SSE.Controllers.Main.confirmMoveCellRange": "Rentang sel yang dituju bisa berisi data. Lanjutkan operasi?", "SSE.Controllers.Main.confirmPutMergeRange": "Sumber data berisi sel yang digabungkan.<br>Telah dipisahkan sebelum ditempelkan ke tabel.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formula di baris header akan dihilangkan dan dikonversi menjadi teks statis.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin melanju<PERSON>?", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON>tu konversi habis.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> \"OK\" untuk kembali ke daftar dokumen.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> gagal.", "SSE.Controllers.Main.downloadTextText": "<PERSON><PERSON><PERSON><PERSON> spread sheet...", "SSE.Controllers.Main.downloadTitleText": "Mengunduh Spread sheet", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON> duplikat tidak ditemukan.", "SSE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON> mencoba melakukan sesuatu yang tidak memiliki izin.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorArgsRange": "Kesalahan dalam formula yang dimasukkan.<br>Rentang argumen yang digunakan salah.", "SSE.Controllers.Main.errorAutoFilterChange": "Operasi tidak diizinkan karena mencoba memindahkan sel dari tabel di worksheet Anda.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operasi tidak bisa dilakukan pada sel yang dipilih karena Anda tidak bisa memindahkan bagian dari tabel.<br><PERSON><PERSON>h rentang data lain agar seluruh tabel bergeser dan coba lagi.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operasi tidak bisa dilakukan pada rentang sel yang dipilih.<br><PERSON>lih rentang data seragam yang berbeda dari yang sudah ada dan coba lagi.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operasi tidak bisa dilakukan karena ada sel yang difilter di area.<br>Silakan tampilkan kembali elemet yang difilter dan coba lagi.", "SSE.Controllers.Main.errorBadImageUrl": "URL Gambar salah", "SSE.Controllers.Main.errorCannotUngroup": "Tidak bisa memisahkan grup. Untuk memulai outline, pilih detail baris atau kolom dan kelompokkan.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Ada tidak bisa menggunakan perintah ini di sheet yang diproteksi. Untuk menggunakan perintah ini, batalkan proteksi sheet.<br><PERSON><PERSON> mungkin diminta untuk memasukkan password.", "SSE.Controllers.Main.errorChangeArray": "Anda tidak bisa mengganti bagian dari array.", "SSE.Controllers.Main.errorChangeFilteredRange": "Hal ini akan mengubah rentang yang difilter pada worksheet Anda.<br>Untuk menyelesaikan perintah ini, silahkan hapus AutoFilters.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Sel atau grafik yang Anda coba untuk ganti berada di sheet yang diproteksi.<br>Untu<PERSON> melakukan per<PERSON>, batalkan proteksi sheet. <PERSON>a mungkin diminta untuk mengisi password.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Koneksi server terputus. Saat ini dokumen tidak dapat diedit.", "SSE.Controllers.Main.errorConnectToServer": "Dokumen tidak bisa disimpan. <PERSON>lakan periksa pengaturan koneksi atau hubungi admin Anda.<br><PERSON><PERSON><PERSON> klik tombol 'OK', <PERSON><PERSON> akan diminta untuk download dokumen.", "SSE.Controllers.Main.errorCopyMultiselectArea": "<PERSON><PERSON>ah ini tidak bisa digunakan dengan pilihan lebih dari satu.<br><PERSON><PERSON>h satu rentang dan coba lagi.", "SSE.Controllers.Main.errorCountArg": "Ke<PERSON>ahan dalam formula yang dimasukkan.<br><PERSON><PERSON><PERSON> argumen yang digunakan tidak tepat.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON><PERSON><PERSON> dalam formula yang dimasukkan.<br><PERSON><PERSON><PERSON> argumen melewati batas.", "SSE.Controllers.Main.errorCreateDefName": "Rentang nama yang ada tidak bisa di edit dan tidak bisa membuat yang baru<br>jika ada beberapa yang sedang diedit.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> eksternal.<br>Koneksi database bermasalah. <PERSON><PERSON>an hubungi layanan bantuan jika tetap terjadi error.", "SSE.Controllers.Main.errorDataEncrypted": "Perubahan enkripsi sudah diterima dan tidak bisa diuraikan.", "SSE.Controllers.Main.errorDataRange": "Rentang data salah.", "SSE.Controllers.Main.errorDataValidate": "Nilai yang dimasukkan tidak tepat.<br>User memiliki batasan nilai yang bisa dimasukkan ke sel ini.", "SSE.Controllers.Main.errorDefaultMessage": "Kode k<PERSON>alahan: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Anda mencoba menghapus kolom yang memiliki sel yang terkunci. Sel yang terkunci tidak bisa dihapus ketika worksheet diproteksi.<br>Untuk menghapus sel yang terkunci, buka proteksi sheet. Anda mungkin diminta untuk mengisi password.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Anda mencoba menghapus baris yang memiliki sel yang terkunci. Sel yang terkunci tidak bisa dihapus ketika worksheet diproteksi.<br>Untuk menghapus sel yang terkunci, buka proteksi sheet. Anda mungkin diminta untuk mengisi password.", "SSE.Controllers.Main.errorDirectUrl": "<PERSON>lakan verifikasi tautan ke dokumen.<br>Tautan ini harus merupakan tautan langsung menuju file yang akan diunduh.", "SSE.Controllers.Main.errorEditingDownloadas": "<PERSON> kesalahan saat bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> opsi 'Download sebagai' untuk menyimpan file salinan backup ke komputer Anda.", "SSE.Controllers.Main.errorEditingSaveas": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> opsi 'Simpan sebagai...' untuk menyimpan salinan pencadangan file ke harddisk.", "SSE.Controllers.Main.errorEditView": "Tampilan sheet yang ada tidak dapat diedit dan yang baru tidak dapat dibuat saat ini karena beberapa di antaranya sedang diedit.", "SSE.Controllers.Main.errorEmailClient": "<PERSON><PERSON> klein tidak bisa ditemukan.", "SSE.Controllers.Main.errorFilePassProtect": "Dokumen dilindungi dengan kata sandi dan tidak dapat dibuka.", "SSE.Controllers.Main.errorFileRequest": "Error eksternal.<br><PERSON><PERSON><PERSON> permintaan file. <PERSON><PERSON><PERSON> hubungi layanan bantuan jika tetap terjadi error.", "SSE.Controllers.Main.errorFileSizeExceed": "Ukuran file melewati batas server <PERSON><PERSON>.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>a untuk detail.", "SSE.Controllers.Main.errorFileVKey": "Error eksternal.<br><PERSON><PERSON> kea<PERSON>n sa<PERSON>. <PERSON><PERSON><PERSON> hubungi layanan bantuan jika tetap terjadi error.", "SSE.Controllers.Main.errorFillRange": "Tidak bisa mengisi rentang sel yang dipilih.<br><PERSON><PERSON><PERSON> sel yang di merge harus memiliki ukuran yang sama.", "SSE.Controllers.Main.errorForceSave": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menyimpan file. <PERSON><PERSON><PERSON> gunakan opsi 'Unduh sebagai' untuk menyimpan file ke harddisk atau coba lagi nanti.", "SSE.Controllers.Main.errorFormulaName": "Ke<PERSON>ahan dalam formula yang dimasukkan.<br>Nama formula yang digunakan tidak tepat.", "SSE.Controllers.Main.errorFormulaParsing": "Kesalahan internal saat menguraikan rumus", "SSE.Controllers.Main.errorFrmlMaxLength": "Panjang formula <PERSON>a melewati batas 8192 karakter.<br><PERSON><PERSON><PERSON> edit dan coba kembali.", "SSE.Controllers.Main.errorFrmlMaxReference": "Anda tidak bisa memasukkan formula ini karena memiliki terlalu banyak nilai,<br>referensi sel, dan/atau nama.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "<PERSON>lai teks dalam formula dibatasi 255 karakter.<br><PERSON><PERSON><PERSON> fungsi PENGGABUNGAN atau operator penggabungan (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Fungsi yang menuju sheet tidak ada.<br><PERSON><PERSON>an periksa data kembali.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operasi tidak bisa diselesaikan untuk rentang sel yang dipilih.<br><PERSON><PERSON><PERSON> rentang agar baris pertama tabel berada di baris yang sama<b>dan men<PERSON><PERSON><PERSON>an tabel yang overlap dengan tabel saat ini.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operasi tidak bisa diselesaikan untuk rentang sel yang dipilih.<br><PERSON><PERSON><PERSON> rentang yang tidak termasuk di tabel lain.", "SSE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file tidak cocok dengan ekstensi file.", "SSE.Controllers.Main.errorInconsistentExtDocx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan dokumen teks (mis. docx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "<PERSON><PERSON><PERSON> kesalahan terjadi ketika membuka file.<br>Isi file berhubungan dengan satu dari format berikut: pdf/djvu/xps/oxps, tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan presentasi (mis. pptx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan spreadsheet (mis. xlsx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.Controllers.Main.errorInvalidRef": "Masukkan nama yang tepat untuk pilihan atau referensi valid sebagai tujuan.", "SSE.Controllers.Main.errorKeyEncrypt": "Deskriptor kunci tidak dikenal", "SSE.Controllers.Main.errorKeyExpire": "Deskriptor kunci tidak berfungsi", "SSE.Controllers.Main.errorLabledColumnsPivot": "Untuk membuat tabel pivot, gunakan data yang diatur menjadi list dengan kolom yang dilabel.", "SSE.Controllers.Main.errorLoadingFont": "Font tidak bisa dimuat.<br><PERSON><PERSON><PERSON> kontak admin Server <PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Referensi untuk lokasi atau range data tidak valid.", "SSE.Controllers.Main.errorLockedAll": "Operasi tidak bisa dilakukan karena sheet dikunci oleh user lain.", "SSE.Controllers.Main.errorLockedCellPivot": "Anda tidak bisa mengubah data di dalam tabel pivot.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Nama sheet tidak bisa diubah sekarang karena sedang diganti oleh user lain", "SSE.Controllers.Main.errorMaxPoints": "<PERSON><PERSON><PERSON> titik maksimum dalam seri per grafik adalah 4096.", "SSE.Controllers.Main.errorMoveRange": "Tidak bisa mengganti bagian dari sel yang digabungkan", "SSE.Controllers.Main.errorMoveSlicerError": "Slicer tabel tidak bisa disalin dari satu workbook ke lainnya.<br><PERSON><PERSON>an coba lagi dengan memilih seluruh tabel dan slicer.", "SSE.Controllers.Main.errorMultiCellFormula": "Formula array multi sel tidak diizinkan di tabel.", "SSE.Controllers.Main.errorNoDataToParse": "Tidak ada data yang dipilih untuk diuraikan.", "SSE.Controllers.Main.errorOpenWarning": "Salah satu file formula melewati batas 8192 karakter.<br>Formula dihapus.", "SSE.Controllers.Main.errorOperandExpected": "Syntax fungsi yang dimasukkan tidak tepat. <PERSON><PERSON>an periksa lagi apakah Anda terlewat tanda kurung - '(' atau ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Password yang Anda sediakan tidak tepat.<br>Pastikan bahwa CAPS LOCK sudah mati dan pastikan sudah menggunakan huruf besar dengan tepat.", "SSE.Controllers.Main.errorPasteMaxRange": "Area copy dan paste tidak cocok.<br>Silakan pilih area dengan ukuran yang sama atau klik sel pertama di baris untuk paste sel yang di copy.", "SSE.Controllers.Main.errorPasteMultiSelect": "Tindakan ini tidak dapat dilakukan pada beberapa pilihan rentang.<br><PERSON><PERSON>h satu rentang dan coba lagi.", "SSE.Controllers.Main.errorPasteSlicerError": "Slicer tabel tidak bisa disalin dari satu workbook ke lainnya.", "SSE.Controllers.Main.errorPivotGroup": "Tidak bisa mengelompokkan pilihan itu.", "SSE.Controllers.Main.errorPivotOverlap": "Laporan tabel pivot tidak bisa tumpang tindih dengan tabel.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Laporan Tabel Pivot disimpan tanpa data pokok.<br><PERSON><PERSON><PERSON> tombol 'Refresh' untuk memperbarui laporan.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Mohon maaf karena tidak bisa print lebih dari 1500 halaman dalam sekali waktu dengan versi program sekarang.<br>Hal ini akan bisa dilakukan di versi selanjutnya.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON>", "SSE.Controllers.Main.errorServerVersion": "Versi editor sudah di update. <PERSON>aman akan dimuat ulang untuk menerapkan perubahan.", "SSE.Controllers.Main.errorSessionAbsolute": "W<PERSON><PERSON> edit dokumen sudah selesai. Silakan muat ulang halaman.", "SSE.Controllers.Main.errorSessionIdle": "Dokumen sudah lama tidak diedit. <PERSON><PERSON>an muat ulang halaman.", "SSE.Controllers.Main.errorSessionToken": "Koneksi ke server terganggu. Silakan muat ulang halaman.", "SSE.Controllers.Main.errorSetPassword": "Password tidak bisa diatur.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Lokasi referensi tidak valid karena sel tidak ada di kolom atau baris yang sama. <br><PERSON><PERSON><PERSON> sel yang semuanya ada di satu kolom atau baris.", "SSE.Controllers.Main.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "SSE.Controllers.Main.errorToken": "Token keamanan dokumen tidak dibentuk dengan tepat.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorTokenExpire": "Token keamanan dokumen sudah kadaluwarsa.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorUnexpectedGuid": "Error eksternal.<br>Unexpected GUID. <PERSON><PERSON><PERSON> hubungi layanan bantuan jika tetap terjadi error.", "SSE.Controllers.Main.errorUpdateVersion": "Versi file telah diubah. Halaman tidak akan dimuat ulang.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Koneksi internet sudah kembali dan versi file sudah diganti.<br>Sebelum Anda bisa melanjutkan kerja, Anda perlu mengunduh file atau salin konten untuk memastikan tidak ada yang hilang, lalu muat ulang halaman ini.", "SSE.Controllers.Main.errorUserDrop": "File tidak bisa diakses sekarang.", "SSE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON><PERSON> pengguna telah melebihi jumlah yang diijinkan dalam paket harga.", "SSE.Controllers.Main.errorViewerDisconnect": "Koneksi terputus. <PERSON>a tetap bisa melihat dokumen,<br>tapi tidak bisa download atau print sampai koneksi terhubung dan halaman dimuat ulang.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON><PERSON> dalam formula yang dimasukkan.<br><PERSON><PERSON><PERSON> tanda kurung yang dipakai salah.", "SSE.Controllers.Main.errorWrongOperator": "Eror ketika memasukkan formula. Penggunaan operator tidak tepat.<br><PERSON><PERSON><PERSON> per<PERSON>.", "SSE.Controllers.Main.errorWrongPassword": "Password yang Anda sediakan tidak tepat.", "SSE.Controllers.Main.errRemDuplicates": "<PERSON>lai duplikat yang ditemukan dan dihapus: {0}, nilai unik tersisa: {1}.", "SSE.Controllers.Main.leavePageText": "Anda memiliki perubahan yang belum tersimpan di spreadsheet ini. <PERSON><PERSON> \"Tetap di Halaman Ini” kemudian \"Simpan” untuk menyimpan perubahan tersebut. Klik ‘Tinggalkan Halaman Ini’ untuk membatalkan semua perubahan yang belum disimpan.", "SSE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON><PERSON> perubahan yang belum disimpan dalam spreadsheet ini akan hilang.<br> <PERSON><PERSON> \"<PERSON><PERSON>\" lalu \"<PERSON><PERSON><PERSON>\" untuk menyimpan. <PERSON><PERSON> \"OK\" untuk membuang semua perubahan yang tidak tersimpan.", "SSE.Controllers.Main.loadFontsTextText": "Memuat data...", "SSE.Controllers.Main.loadFontsTitleText": "Memuat Data", "SSE.Controllers.Main.loadFontTextText": "Memuat data...", "SSE.Controllers.Main.loadFontTitleText": "Memuat Data", "SSE.Controllers.Main.loadImagesTextText": "Memuat gambar...", "SSE.Controllers.Main.loadImagesTitleText": "Memuat Gambar", "SSE.Controllers.Main.loadImageTextText": "Memuat gambar...", "SSE.Controllers.Main.loadImageTitleText": "Memuat Gambar", "SSE.Controllers.Main.loadingDocumentTitleText": "Memuat spread sheet", "SSE.Controllers.Main.notcriticalErrorTitle": "Peringatan", "SSE.Controllers.Main.openErrorText": "Eror ketika membuka file.", "SSE.Controllers.Main.openTextText": "Membuka spreadsheet...", "SSE.Controllers.Main.openTitleText": "Membuka spreadsheet", "SSE.Controllers.Main.pastInMergeAreaError": "Tidak bisa mengganti bagian dari sel yang digabungkan", "SSE.Controllers.Main.printTextText": "Mencetak spreadsheet...", "SSE.Controllers.Main.printTitleText": "Mencetak spreadsheet", "SSE.Controllers.Main.reloadButtonText": "Muat <PERSON>", "SSE.Controllers.Main.requestEditFailedMessageText": "Saat ini dokumen sedang diedit. <PERSON>lakan coba beberapa saat lagi.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON>r ketika menyi<PERSON> file.", "SSE.Controllers.Main.saveErrorTextDesktop": "File tidak bisa disimpan atau dibuat.<br><PERSON><PERSON><PERSON> yang mungkin adalah: <br>1. File hanya bisa dibaca. <br>2. <PERSON> sedang diedit user lain. <br>3. <PERSON><PERSON><PERSON> penuh atau terkorupsi.", "SSE.Controllers.Main.saveTextText": "Menyimpan spreadsheet...", "SSE.Controllers.Main.saveTitleText": "Menyimpan spreadsheet", "SSE.Controllers.Main.scriptLoadError": "Koneksi terlalu lambat dan beberapa komponen tidak bisa dibuka Si<PERSON>an muat ulang halaman.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Terapkan untuk semua persa<PERSON>an", "SSE.Controllers.Main.textBuyNow": "Kunjungi situs web", "SSE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Klik untuk menutup tips", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textContactUs": "Hubungi sales", "SSE.Controllers.Main.textContinue": "Lanjutkan", "SSE.Controllers.Main.textConvertEquation": "Persamaan ini dibuat dengan editor persa<PERSON><PERSON> versi lama yang sudah tidak didukung. Untuk edit, konversikan persamaan ke format Office Math ML.<br><PERSON>n<PERSON><PERSON> sekarang?", "SSE.Controllers.Main.textCustomLoader": "<PERSON><PERSON> diketahui bahwa berdasarkan syarat dari lisensi, <PERSON><PERSON> tidak bisa untuk mengganti loader.<br><PERSON><PERSON><PERSON> hubungi Departemen Penjualan kami untuk mendapatkan harga.", "SSE.Controllers.Main.textDisconnect": "<PERSON><PERSON><PERSON><PERSON> terputus", "SSE.Controllers.Main.textFillOtherRows": "<PERSON>i baris lainnya", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula mengisi {0} baris memiliki data. Mengisi baris kosong lainnya mungkin memerlukan waktu beberapa menit", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula mengisi {0} baris pertama. Mengisi baris kosong lainnya mungkin memerlukan waktu beberapa menit", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula yang mengisi hanya {0} baris pertama memiliki data dengan alasan penyimpanan memori. Ada {1} baris lain yang memiliki data di sheet ini. Anda bisa mengisinya dengan manual.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula hanya mengisi {0} baris pertama dengan alasan penyimpanan memori. Baris lain di sheet ini tidak memiliki data.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "File berisi macros otomatis.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin menja<PERSON>an macros?", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLoadingDocument": "Memuat spread sheet", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON> nama maksimum 128 karakter.", "SSE.Controllers.Main.textNeedSynchronize": "<PERSON> pembaruan", "SSE.Controllers.Main.textNo": "Tidak", "SSE.Controllers.Main.textNoLicenseTitle": "Batas lisensi sudah tercapai", "SSE.Controllers.Main.textPaidFeature": "<PERSON><PERSON>", "SSE.Controllers.Main.textPleaseWait": "Operasi mungkin akan membutuhkan waktu lebih lama dari perkiraan. Silahkan menunggu...", "SSE.Controllers.Main.textReconnect": "Koneksi terhubung kembali", "SSE.Controllers.Main.textRemember": "Ingat pilihan saya untuk semua file", "SSE.Controllers.Main.textRememberMacros": "Ingat pilihan saya untuk semua makro", "SSE.Controllers.Main.textRenameError": "<PERSON>a pengguna tidak boleh kosong.", "SSE.Controllers.Main.textRenameLabel": "Ma<PERSON>kkan nama untuk digunakan di kolaborasi", "SSE.Controllers.Main.textRequestMacros": "Sebuah makro melakukan permintaan ke URL. Apakah Anda akan mengizinkan permintaan ini ke %1?", "SSE.Controllers.Main.textShape": "Bentuk", "SSE.Controllers.Main.textStrict": "Mode strict", "SSE.Controllers.Main.textText": "Teks", "SSE.Controllers.Main.textTryQuickPrint": "Anda telah memilih Cetak cepat: seluruh dokumen akan dicetak pada printer yang terakhir dipilih atau baku.<br><PERSON><PERSON><PERSON><PERSON> Anda hendak melanjutkan?", "SSE.Controllers.Main.textTryUndoRedo": "Fungsi Undo/Redo dinonaktifkan untuk mode Co-editing Cepat.<br>Klik tombol 'Mode strict' untuk mengganti ke Mode Strict Co-editing untuk edit file tanpa gangguan dari user lain dan kirim perubahan Anda hanya setelah Anda menyimpannya. Anda bisa mengganti mode co-editing menggunakan editor di pengaturan lanjut.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Fungsi Undo/Redo dinonaktifkan untuk mode Co-editing Cepat.", "SSE.Controllers.Main.textUndo": "Batalkan", "SSE.Controllers.Main.textYes": "Ya", "SSE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON> ka<PERSON>", "SSE.Controllers.Main.titleServerVersion": "Editor mengu<PERSON><PERSON>", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.Main.txtArt": "Teks Anda di sini", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtBlank": "(kosong)", "SSE.Controllers.Main.txtButtons": "Tombol", "SSE.Controllers.Main.txtByField": "%1 dari %2", "SSE.Controllers.Main.txtCallouts": "Balon Kata", "SSE.Controllers.Main.txtCharts": "Bagan", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtColLbls": "Label Kolom", "SSE.Controllers.Main.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtConfidential": "Konfidensial", "SSE.Controllers.Main.txtDate": "Tanggal", "SSE.Controllers.Main.txtDays": "<PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtEditingMode": "Mengatur mode editing...", "SSE.Controllers.Main.txtErrorLoadHistory": "Memuat riwayat gagal", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "File", "SSE.Controllers.Main.txtGrandTotal": "Grand Total", "SSE.Controllers.Main.txtGroup": "Grup", "SSE.Controllers.Main.txtHours": "jam", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematika", "SSE.Controllers.Main.txtMinutes": "menit", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Multi-Select", "SSE.Controllers.Main.txtOr": "%1 atau %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Halaman %1 dari %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "Disiap<PERSON> o<PERSON>h", "SSE.Controllers.Main.txtPrintArea": "Print_Area", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Ku<PERSON>r", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "Label Baris", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "Seri", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Garis Callout 1 (<PERSON> dan <PERSON>)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Garis Callout 2 (<PERSON> dan <PERSON>)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Garis Callout 3 (<PERSON> dan <PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout1": "Garis Callout 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "Garis Callout 2 (<PERSON><PERSON>ent Bar)", "SSE.Controllers.Main.txtShape_accentCallout3": "Garis Callout 3 (<PERSON><PERSON>ent Bar)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tombol Kembali atau Sebelumnya", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Tombol Dokumen", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Tombol Akhir", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON> Selanjutnya", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Tombol <PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Tombol Informasi", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Tombol Movie", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Tombol Kembali", "SSE.Controllers.Main.txtShape_actionButtonSound": "Tombol Suara", "SSE.Controllers.Main.txtShape_arc": "Arc", "SSE.Controllers.Main.txtShape_bentArrow": "Panah <PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Konektor Siku", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Panah Konektor Siku", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Panah Ganda Konektor Siku", "SSE.Controllers.Main.txtShape_bentUpArrow": "Panah Kelok Atas", "SSE.Controllers.Main.txtShape_bevel": "Miring", "SSE.Controllers.Main.txtShape_blockArc": "Block Arc", "SSE.Controllers.Main.txtShape_borderCallout1": "Garis Callout 1", "SSE.Controllers.Main.txtShape_borderCallout2": "<PERSON><PERSON> Callout 2", "SSE.Controllers.Main.txtShape_borderCallout3": "<PERSON><PERSON> Callout 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Garis Callout 1 (No Border)", "SSE.Controllers.Main.txtShape_callout2": "<PERSON><PERSON>out 2 (No <PERSON>)", "SSE.Controllers.Main.txtShape_callout3": "<PERSON><PERSON> Callout 3 (No <PERSON>)", "SSE.Controllers.Main.txtShape_can": "Bisa", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Chord", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloud": "Cloud", "SSE.Controllers.Main.txtShape_cloudCallout": "Cloud Callout", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "Ku<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Konektor <PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Panah Konektor <PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Panah Ganda Konektor <PERSON>", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Panah Kelok Bawah", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Panah Kelok Kiri", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Panah Kelok <PERSON>", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Panah Kelok Atas", "SSE.Controllers.Main.txtShape_decagon": "Decagon", "SSE.Controllers.Main.txtShape_diagStripe": "Strip Diagonal", "SSE.Controllers.Main.txtShape_diamond": "Diamond", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "SSE.Controllers.Main.txtShape_donut": "Donut", "SSE.Controllers.Main.txtShape_doubleWave": "Gelombang Ganda", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrowCallout": "Seranta Panah Bawah", "SSE.Controllers.Main.txtShape_ellipse": "Elips", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Pita Kelok Bawah", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Pita Kelok Atas", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagram Alir: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartCollate": "Diag<PERSON> Alir: Collate", "SSE.Controllers.Main.txtShape_flowChartConnector": "Diagram Alir: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Diagram Alir: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDelay": "Diagram Alir: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Diagram Alir: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Diagram Alir: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartExtract": "Diagram Alir: Ekstrak", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Diagram Alir: Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagram Alir: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagram Alir: Magnetic Disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagram Alir: Direct Access Storage", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagram Alir: Sequential Access Storage", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Diagram Alir: Input Manual", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Diagram Alir: Operasi Manual", "SSE.Controllers.Main.txtShape_flowChartMerge": "Diagram Alir: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Diagram Alir: Multidokumen ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagram Alir: Off-page Penghubung", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagram Alir: Stored Data", "SSE.Controllers.Main.txtShape_flowChartOr": "Diagram Alir: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagram Alir: Predefined Process", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Diagram Alir: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartProcess": "Diagram Alir: Proses", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagram Alir: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagram Alir: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Diagram Alir: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagram Alir: Summing Junction", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Diagram Alir: Terminator", "SSE.Controllers.Main.txtShape_foldedCorner": "Sudut Folder", "SSE.Controllers.Main.txtShape_frame": "Kerang<PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "Setengah Bingkai", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Heptagon", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON>roll <PERSON>tal", "SSE.Controllers.Main.txtShape_irregularSeal1": "Ledakan 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Ledakan 2", "SSE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftUpArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lightningBolt": "Petir", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Panah", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "Divisi", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathNotEqual": "Tidak Sama", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "\"No\" simbol", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Panah Takik <PERSON>", "SSE.Controllers.Main.txtShape_octagon": "Oktagon", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "Plus", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Scribble", "SSE.Controllers.Main.txtShape_polyline2": "<PERSON><PERSON><PERSON> be<PERSON>", "SSE.Controllers.Main.txtShape_quadArrow": "Panah Empat Mata", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Seranta Panah Empat Mata", "SSE.Controllers.Main.txtShape_rect": "Kotak", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon2": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightArrow": "Tanda Panah ke Kanan", "SSE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "<PERSON>segi <PERSON> Sudut Lengkung Single", "SSE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON> Sudut Lengkung Diagonal", "SSE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON> Sudut Lengkung Sama Sisi", "SSE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON> Sudu<PERSON>", "SSE.Controllers.Main.txtShape_rtTriangle": "Segitiga Siku-Siku", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Snip Persegi <PERSON> Sudut Single", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Snip Persegi Panjang Sudut Lengkung Diagonal", "SSE.Controllers.Main.txtShape_snip2SameRect": "Snip Persegi Panjang Sudut Lengkung Sama Sisi", "SSE.Controllers.Main.txtShape_snipRoundRect": "<PERSON><PERSON><PERSON> dan <PERSON> Sudut Lengkung Single", "SSE.Controllers.Main.txtShape_spline": "<PERSON>g<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "Bintang Titik-10", "SSE.Controllers.Main.txtShape_star12": "Bintang Titik-12", "SSE.Controllers.Main.txtShape_star16": "Bintang Titik-16", "SSE.Controllers.Main.txtShape_star24": "Bintang Titik-24", "SSE.Controllers.Main.txtShape_star32": "Bintang Titik-32", "SSE.Controllers.Main.txtShape_star4": "Bintang Titik-4", "SSE.Controllers.Main.txtShape_star5": "Bintang Titik-5", "SSE.Controllers.Main.txtShape_star6": "Bintang Titik-6", "SSE.Controllers.Main.txtShape_star7": "Bintang Titik-7", "SSE.Controllers.Main.txtShape_star8": "Bintang Titik-8", "SSE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON>-<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_sun": "Matahari", "SSE.Controllers.Main.txtShape_teardrop": "Teardrop", "SSE.Controllers.Main.txtShape_textRect": "Kotak Teks", "SSE.Controllers.Main.txtShape_trapezoid": "Trapesium", "SSE.Controllers.Main.txtShape_triangle": "Segitiga", "SSE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON> k<PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upDownArrow": "Panah Atas Bawah", "SSE.Controllers.Main.txtShape_uturnArrow": "Panah Balik", "SSE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wave": "Gelombang", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Callout Oval", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Callout <PERSON>", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Callout <PERSON><PERSON><PERSON> Sudut <PERSON>", "SSE.Controllers.Main.txtStarsRibbons": "Bintang & Pita", "SSE.Controllers.Main.txtStyle_Bad": "Buruk", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "<PERSON><PERSON><PERSON> sel", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Good": "Bagus", "SSE.Controllers.Main.txtStyle_Heading_1": "Heading 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Heading 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Heading 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Heading 4", "SSE.Controllers.Main.txtStyle_Input": "Input", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Neutral": "Netral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "Catatan", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Teks Peringa<PERSON>", "SSE.Controllers.Main.txtTab": "Tab", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRange": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRangeDescription": "Masukkan password untuk mengubah rentang ini:", "SSE.Controllers.Main.txtUnlockRangeWarning": "<PERSON>tang yang ingin Anda ubah dilindungi password", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON>", "SSE.Controllers.Main.txtXAxis": "Sumbu X", "SSE.Controllers.Main.txtYAxis": "Sumbu Y", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "Kesalahan tidak diketahui.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> tidak didukung.", "SSE.Controllers.Main.uploadDocExtMessage": "Format dokumen tidak diketahui.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Tidak ada dokumen yang diupload.", "SSE.Controllers.Main.uploadDocSizeMessage": "Batas ukuran maksimum dokumen terlampaui.", "SSE.Controllers.Main.uploadImageExtMessage": "Format gambar tidak dikenal.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Tidak ada gambar yang di<PERSON>h.", "SSE.Controllers.Main.uploadImageSizeMessage": "Melebihi ukuran maksimal file. Ukuran maksimum adalah 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Mengunggah gambar...", "SSE.Controllers.Main.uploadImageTitleText": "Mengunggah Gambar", "SSE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.warnBrowserIE9": "Aplikasi ini tidak berjalan dengan baik di IE9. Gunakan IE10 atau versi yang terbaru.", "SSE.Controllers.Main.warnBrowserZoom": "Pengaturan pembesaran tampilan pada browser <PERSON><PERSON> saat ini tidak didukung sepenuhnya. <PERSON><PERSON>an atur ulang ke tampilan standar dengan menekan Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "Anda sudah mencapai batas untuk koneksi bersa<PERSON> ke %1 editor. Dokumen ini akan dibuka untuk dilihat saja.<br>Hubungi admin Anda untuk mempelajari lebih lanjut.", "SSE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON>si Anda sudah kedal<PERSON>.<br><PERSON><PERSON><PERSON> perbarui lisensi Anda dan segarkan halaman.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "<PERSON><PERSON><PERSON> ka<PERSON>.<br><PERSON><PERSON> tidak memiliki akses untuk editing dokumen secara keseluruhan.<br><PERSON><PERSON><PERSON> hubungi admin Anda.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Lisensi perlu diperbaharui.<br>Anda memiliki akses terbatas untuk edit dokumen.<br><PERSON><PERSON><PERSON> hubungi admin Anda untuk mendapatkan akses penuh", "SSE.Controllers.Main.warnLicenseUsersExceeded": "<PERSON><PERSON> sudah mencapai batas user untuk %1 editor. Hubungi admin Anda untuk mempelajari lebih lanjut.", "SSE.Controllers.Main.warnNoLicense": "<PERSON>a sudah mencapai batas untuk koneksi bersa<PERSON>an ke %1 editor. Dokumen ini akan dibuka untuk dilihat saja.<br>Hubungi %1 tim sales untuk syarat personal upgrade.", "SSE.Controllers.Main.warnNoLicenseUsers": "Anda sudah mencapai batas user untuk %1 editor. Hubungi %1 tim sales untuk syarat personal upgrade.", "SSE.Controllers.Main.warnProcessRightsChange": "Hak Anda untuk mengedit file ditolak.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON> pertama", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON> pertama", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON> yang <PERSON>n", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON> yang <PERSON>n", "SSE.Controllers.Print.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Controllers.Print.textNoRepeat": "<PERSON><PERSON>", "SSE.Controllers.Print.textRepeat": "Ulangi...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textWarning": "Peringatan", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON> tidak tepat", "SSE.Controllers.Search.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Controllers.Search.textNoTextFound": "Data yang Anda cari tidak ditemukan. <PERSON>lakan atur opsi pencarian <PERSON>a.", "SSE.Controllers.Search.textReplaceSkipped": "Penggantian telah dilakukan. {0} kemunculan telah dilewatkan.", "SSE.Controllers.Search.textReplaceSuccess": "Pencarian telah dilakukan. {0} kemunculan telah diganti", "SSE.Controllers.Statusbar.errorLastSheet": "Workbook harus setidaknya memiliki satu worksheet yang terlihat.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Tidak bisa menghapus worksheet.", "SSE.Controllers.Statusbar.strSheet": "Sheet", "SSE.Controllers.Statusbar.textDisconnect": "<b><PERSON><PERSON><PERSON><PERSON> terput<PERSON></b><br><PERSON><PERSON><PERSON>gh<PERSON>. Silakan periksa pengaturan koneksi.", "SSE.Controllers.Statusbar.textSheetViewTip": "<PERSON>a di mode <PERSON><PERSON>lan Sheet. Filter dan pengurutan hanya terlihat oleh Anda dan orang yang masih ada di tampilan ini.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "<PERSON>a di mode <PERSON><PERSON>lan Sheet. Filter hanya dapat dilihat oleh Anda dan orang yang masih ada di tampilan ini.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Worksheet yang dipilih mungkin memiliki data. A<PERSON>kah Anda yakin ingin melanjutkan?", "SSE.Controllers.Statusbar.zoomText": "Perbesar {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Font yang akan Anda simpan tidak tersedia di perangkat sekarang.<br>Style teks akan ditampilkan menggunakan salah satu font sistem, font yang disimpan akan digunakan jika sudah tersedia.<br><PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan?", "SSE.Controllers.Toolbar.errorComboSeries": "Untuk membuat grafik kombinasi, pilih setidaknya dua seri data.", "SSE.Controllers.Toolbar.errorMaxRows": "KESALAHAN! Jumlah seri data maksimum per grafik adalah 255.", "SSE.Controllers.Toolbar.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Direksional", "SSE.Controllers.Toolbar.textFontSizeErr": "Input yang dimasukkan salah.<br><PERSON><PERSON><PERSON> ma<PERSON>kkan input numerik antara 1 dan 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textInsert": "<PERSON>sip<PERSON>", "SSE.Controllers.Toolbar.textIntegral": "Integral", "SSE.Controllers.Toolbar.textLargeOperator": "Operator <PERSON>", "SSE.Controllers.Toolbar.textLimitAndLog": "<PERSON>it dan <PERSON>", "SSE.Controllers.Toolbar.textLongOperation": "Operasi Panjang", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operator", "SSE.Controllers.Toolbar.textPivot": "<PERSON>bel Pivot", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Rating", "SSE.Controllers.Toolbar.textRecentlyUsed": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Bentuk", "SSE.Controllers.Toolbar.textSymbols": "Simbol", "SSE.Controllers.Toolbar.textWarning": "Peringatan", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON>-<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON>da Panah ke Kanan <PERSON>as", "SSE.Controllers.Toolbar.txtAccent_Bar": "Palang", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "SSE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> (dengan placeholder)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Kotak Formula(Contoh)", "SSE.Controllers.Toolbar.txtAccent_Check": "Periksa", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "<PERSON><PERSON> At<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Dengan Overbar", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y dengan overbar", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Triple Dot", "SSE.Controllers.Toolbar.txtAccent_DDot": "Titik Dua", "SSE.Controllers.Toolbar.txtAccent_Dot": "Titik", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Overbar Ganda", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Pengelompokan Karakter Di Bawah", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Pengelompokan Karakter Di Atas", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Panah Tiga Kiri Atas", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON> kanan di bawah", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON>i", "SSE.Controllers.Toolbar.txtAccent_Smile": "Prosodi", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON> (<PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Kasus (Tiga Kondisi)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Tumpuk objek", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Tumpuk objek", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Koefisien binomial", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Koefisien binomial", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON> sel", "SSE.Controllers.Toolbar.txtExpand": "<PERSON><PERSON><PERSON> dan sortir", "SSE.Controllers.Toolbar.txtExpandSort": "Data di sebelah pilihan tidak akan disortasi. Apakah Anda ingin memperluas pilihan untuk menyertakan data yang berdekatan atau lanjut sortasi dengan hanya sel yang dipilih?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON> miring", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Diferensial", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Diferensial", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Diferensial", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Diferensial", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Pecahan Linear", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi Dibagi 2", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON>han kecil", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Fungsi Kosin Hiperbolik Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Fungsi Kotangen Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Fungsi Kotangen Hiperbolik Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON><PERSON> Ko<PERSON>s <PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Fungsi Kosekan Hiperbolik Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Fungsi Sekans Hiperbolik Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Fungsi Sin Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Fungsi Sin Hiperbolik Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Fungsi Tangen Terbalik", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Fungsi Tangen Hiperbolik Terbalik", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Coth": "Fungsi Kotangen Hiperbolik", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Csch": "Fungsi Kosekans Hiperbolik", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON> sekan", "SSE.Controllers.Toolbar.txtFunction_Sech": "Fungsi Sekans Hiperbolik", "SSE.Controllers.Toolbar.txtFunction_Sin": "Fungsi sinus", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Fungsi Sin Hiperbolik", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON>i tangen", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Fungsi Tangen Hiperbolik", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Kustom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data dan model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "<PERSON><PERSON>, Buruk, dan <PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "<PERSON><PERSON> nama", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Format nomor", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "<PERSON><PERSON><PERSON> dan <PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Kustom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON><PERSON> sel", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Theta Diferensial", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferensial x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferensial y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Integral Ganda", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral Ganda", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral Ganda", "SSE.Controllers.Toolbar.txtIntegralOriented": "Integral <PERSON>ur", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral <PERSON>ur", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON><PERSON> integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON> integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON><PERSON> integral", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral <PERSON>ur", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral volume", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral", "SSE.Controllers.Toolbar.txtInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko-Produk", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko-Produk", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko-Produk", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko-Produk", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko-Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritma Natural", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarit<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarit<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Ditemukan data di sebelah pilihan <PERSON>, tapi, <PERSON>a tidak memiliki izin untuk mengubah sel tersebut.<br><PERSON><PERSON><PERSON><PERSON> Anda ingin tetap lanjut dengan pilihan saat ini?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "matriks kosong 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "matriks kosong 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "matriks kosong 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "matriks kosong 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON>gan <PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_3": "matriks kosong 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "matriks kosong 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "matriks kosong 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "matriks kosong 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> garis dasar", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Titik Tengah", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Titik Diagonal", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Titik vertikal", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse Matrix", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse Matrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "matriks identitas 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "matriks identitas 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "matriks identitas 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "matriks identitas 3x3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON>da Panah <PERSON>-<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON>-<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Panah <PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON> kanan di bawah", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON>da Panah ke Kanan <PERSON>as", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Titik Dua", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Hasil <PERSON>", "SSE.Controllers.Toolbar.txtOperator_Definition": "Setara Menurut De<PERSON>isi", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Setara Dengan", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON>da Panah <PERSON>-<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON>-<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Panah <PERSON>", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON> kanan di bawah", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON>da Panah ke Kanan <PERSON>as", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Setara Setara", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Sama <PERSON>", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON> dengan pangkat", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Akar Pangkat Tiga", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON> pangkat dua", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Subskrip", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subskrip-SuperskripKiri", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Subskrip-SuperskripKiri", "SSE.Controllers.Toolbar.txtScriptSup": "Superskrip", "SSE.Controllers.Toolbar.txtSorting": "Sorting", "SSE.Controllers.Toolbar.txtSortSelected": "Sortir dipilih", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "Komplemen", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operator tanda bintang", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operator butir", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Akar Pangkat Tiga", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Elipsis <PERSON> Horisontal", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cup": "Union", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Tanda Pembagi", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Panah Bawa<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Himpunan Kosong", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identik Dengan", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON> di sana", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Derajat Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Untuk Semua", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> atau <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_in": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Tak Terbatas", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> atau <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Tidak Sama Den<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ni": "Sertakan sebagai Anggota", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Tidak ada di sana", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Diferensial Parsial", "SSE.Controllers.Toolbar.txtSymbol_percent": "Persentase", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proposional Dengan", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON>em<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Elipsis diagonal kanan atas", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Tanda Panah ke Kanan", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> karena itu", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON> k<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Varian Epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "V<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Varian <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON> vertikal", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Tabel Gaya Medium", "SSE.Controllers.Toolbar.warnLongOperation": "Operasi yang akan Anda lakukan mungkin membutukan waktu yang cukup lama untuk selesai.<br><PERSON><PERSON><PERSON><PERSON> anda yakin untuk lanjut?", "SSE.Controllers.Toolbar.warnMergeLostData": "Hanya data dari sel atas-kiri akan tetap berada di sel merging. <br><PERSON><PERSON><PERSON><PERSON> <PERSON>a ingin melanjutkan?", "SSE.Controllers.Viewport.textFreezePanes": "Freeze Panes", "SSE.Controllers.Viewport.textFreezePanesShadow": "Tampillkan bayangan panel beku", "SSE.Controllers.Viewport.textHideFBar": "Sembuntikan bar formula", "SSE.Controllers.Viewport.textHideGridlines": "Sembunyikan Gridlines", "SSE.Controllers.Viewport.textHideHeadings": "Sembunyikan Heading", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separator desimal", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Pengaturan digunakan untuk mengetahui data numerik", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Pengatura<PERSON> lan<PERSON>t", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(tidak ada)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON>an", "SSE.Views.AutoFilterDialog.textAddSelection": "Tambah pilihan saat ini ke filter", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Kosong}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "SSE.Views.AutoFilterDialog.textSelectAllResults": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.textWarning": "Peringatan", "SSE.Views.AutoFilterDialog.txtAboveAve": "Di atas rata-rata", "SSE.Views.AutoFilterDialog.txtBegins": "<PERSON><PERSON><PERSON> dari...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON> bawah rata-rata", "SSE.Views.AutoFilterDialog.txtBetween": "Diantara...", "SSE.Views.AutoFilterDialog.txtClear": "Hapus", "SSE.Views.AutoFilterDialog.txtContains": "Be<PERSON>i...", "SSE.Views.AutoFilterDialog.txtEmpty": "Masukkan filter sel", "SSE.Views.AutoFilterDialog.txtEnds": "<PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtEquals": "<PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filter dari warna sel", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filter dari warna font", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON><PERSON> atau <PERSON>...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filter label", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON> dari...", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON><PERSON> atau <PERSON>...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Tidak diawali dari...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Tidak diantara...", "SSE.Views.AutoFilterDialog.txtNotContains": "Tidak berisi...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Tidak di<PERSON>i dengan...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Tidak sama dengan...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Nomor filter", "SSE.Views.AutoFilterDialog.txtReapply": "Terapkan Ulang", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Sortasi dari warna sel", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Sortasi dari warna font", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Sortir Tertinggi ke Terendah", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Sortir Terendah ke Tertinggi", "SSE.Views.AutoFilterDialog.txtSortOption": "Lebih banyak opsi sortasi...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filter teks", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtTop10": "10 Teratas", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filter nilai", "SSE.Views.AutoFilterDialog.warnFilterError": "Anda setidaknya membutuhkan satu ruas di area Nilai untuk menerapkan filter nilai.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Anda harus memilih setidaknya satu nilai", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellEditor.tipFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "KESALAHAN! Jumlah seri data maksimum per grafik adalah 255.", "SSE.Views.CellRangeDialog.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.CellRangeDialog.txtInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.strShrink": "Shrink untuk pas", "SSE.Views.CellSettings.strWrap": "Lipat teks", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackground": "<PERSON><PERSON> la<PERSON>", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON> warna", "SSE.Views.CellSettings.textCondFormat": "Format bersyarat", "SSE.Views.CellSettings.textControl": "Kontrol <PERSON>", "SSE.Views.CellSettings.textDataBars": "Bar Data", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "<PERSON><PERSON> la<PERSON> depan", "SSE.Views.CellSettings.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textIndent": "Indent", "SSE.Views.CellSettings.textItems": "Items", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textNewRule": "Peratura<PERSON>", "SSE.Views.CellSettings.textNoFill": "Tidak ada <PERSON>", "SSE.Views.CellSettings.textOrientation": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPattern": "Pola", "SSE.Views.CellSettings.textPatternFill": "Pola", "SSE.Views.CellSettings.textPosition": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON> pembatas yang ingin Anda ubah dengan menerarpkan model yang telah dipilih di atas", "SSE.Views.CellSettings.textSelection": "Dari pilihan saat ini", "SSE.Views.CellSettings.textThisPivot": "Dari pivot ini", "SSE.Views.CellSettings.textThisSheet": "<PERSON>i worksheet ini", "SSE.Views.CellSettings.textThisTable": "Dari tabel ini", "SSE.Views.CellSettings.tipAddGradientPoint": "Tambah titik gradien", "SSE.Views.CellSettings.tipAll": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipBottom": "Buat Pembatas Bawah-<PERSON><PERSON>", "SSE.Views.CellSettings.tipDiagD": "Pengaturan Pembatas Bawah Diagonal", "SSE.Views.CellSettings.tipDiagU": "Pengaturan Pembatas Atas Diagonal", "SSE.Views.CellSettings.tipInner": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipInnerHor": "<PERSON><PERSON><PERSON> Horisontal Dalam <PERSON>", "SSE.Views.CellSettings.tipInnerVert": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipLeft": "Buat Pembatas <PERSON>-<PERSON><PERSON>", "SSE.Views.CellSettings.tipNone": "Tanpa <PERSON>em<PERSON>as", "SSE.Views.CellSettings.tipOuter": "Buat Pembatas Luar Saja", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "SSE.Views.CellSettings.tipRight": "Buat Pembatas <PERSON>-<PERSON><PERSON>", "SSE.Views.CellSettings.tipTop": "Buat Pembatas Atas-Luar <PERSON>", "SSE.Views.ChartDataDialog.errorInFormula": "<PERSON> kesalahan di formula yang Anda masukkan.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Referensi tidak valid. Referensi harus ditukukan ke worksheet terbuka.", "SSE.Views.ChartDataDialog.errorMaxPoints": "<PERSON><PERSON><PERSON> titik maksimum dalam seri per grafik adalah 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Jumlah seri data maksimum per grafik adalah 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Referensi tidak valid. Refensi untuk judul, nilai, ukuran, atau label data harus berupa sel, baris, atau kolom tunggal.", "SSE.Views.ChartDataDialog.errorNoValues": "Untuk membuat grafik, setidaknya seri harus memiliki satu nilai.", "SSE.Views.ChartDataDialog.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "SSE.Views.ChartDataDialog.textAdd": "Tambahkan", "SSE.Views.ChartDataDialog.textCategory": "Label Sumbu Horizontal (Kategori)", "SSE.Views.ChartDataDialog.textData": "Rentang data grafik", "SSE.Views.ChartDataDialog.textDelete": "Hapus", "SSE.Views.ChartDataDialog.textDown": "<PERSON>wa<PERSON>", "SSE.Views.ChartDataDialog.textEdit": "Sunting", "SSE.Views.ChartDataDialog.textInvalidRange": "Rentang sel tidak valid", "SSE.Views.ChartDataDialog.textSelectData": "Pilih data", "SSE.Views.ChartDataDialog.textSeries": "<PERSON>tri legenda (seri)", "SSE.Views.ChartDataDialog.textSwitch": "<PERSON><PERSON>/Kolom", "SSE.Views.ChartDataDialog.textTitle": "Data bagan", "SSE.Views.ChartDataDialog.textUp": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "<PERSON> kesalahan di formula yang Anda masukkan.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Referensi tidak valid. Referensi harus ditukukan ke worksheet terbuka.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "<PERSON><PERSON><PERSON> titik maksimum dalam seri per grafik adalah 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Jumlah seri data maksimum per grafik adalah 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Referensi tidak valid. Refensi untuk judul, nilai, ukuran, atau label data harus berupa sel, baris, atau kolom tunggal.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Untuk membuat grafik, setidaknya seri harus memiliki satu nilai.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Rentang sel tidak valid", "SSE.Views.ChartDataRangeDialog.textSelectData": "Pilih data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Rentang label sumbu", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Label sumbu", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Edit seri", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtYValues": "<PERSON><PERSON>", "SSE.Views.ChartSettings.errorMaxRows": "Jumlah seri data maksimum per grafik adalah 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Template", "SSE.Views.ChartSettings.text3dDepth": "<PERSON><PERSON><PERSON> (% dari dasar)", "SSE.Views.ChartSettings.text3dHeight": "Tinggi (% dari dasar)", "SSE.Views.ChartSettings.text3dRotation": "Rotasi 3D", "SSE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "SSE.Views.ChartSettings.textAutoscale": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan tidak tepat.<br><PERSON><PERSON><PERSON> masukkan nilai antara 0 pt dan 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Ubah tipe", "SSE.Views.ChartSettings.textChartType": "Ubah Tipe Bagan", "SSE.Views.ChartSettings.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textDown": "<PERSON>wa<PERSON>", "SSE.Views.ChartSettings.textEditData": "Edit Data dan <PERSON>", "SSE.Views.ChartSettings.textFirstPoint": "Titik <PERSON>tama", "SSE.Views.ChartSettings.textHeight": "Tingg<PERSON>", "SSE.Views.ChartSettings.textHighPoint": "Titik Tinggi", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Titik <PERSON>", "SSE.Views.ChartSettings.textLeft": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textLowPoint": "Titik Rendah", "SSE.Views.ChartSettings.textMarkers": "Markers", "SSE.Views.ChartSettings.textNarrow": "Bidang tilik sempit", "SSE.Views.ChartSettings.textNegativePoint": "Titik negatif", "SSE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRanges": "Rentang Data", "SSE.Views.ChartSettings.textRight": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textRightAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSelectData": "Pilih data", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "Ukuran", "SSE.Views.ChartSettings.textStyle": "Model", "SSE.Views.ChartSettings.textSwitch": "<PERSON><PERSON>/Kolom", "SSE.Views.ChartSettings.textType": "Tipe", "SSE.Views.ChartSettings.textUp": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textWiden": "Perlebar bidang tampilan", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "Rotasi X", "SSE.Views.ChartSettings.textY": "R<PERSON><PERSON> Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "KESALAHAN! Ju<PERSON>lah titik maksimum dalam seri per grafik adalah 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "KESALAHAN! Jumlah seri data maksimum per grafik adalah 255.", "SSE.Views.ChartSettingsDlg.errorStockChart": "Urutan baris salah. Untuk membuat diagram garis, masukkan data pada lembar kerja dengan urutan berikut ini:<br> harga pembu<PERSON><PERSON>, harga maks<PERSON><PERSON>, harga <PERSON>, harga penutupan.", "SSE.Views.ChartSettingsDlg.textAbsolute": "<PERSON><PERSON> pindah atau ubah dengan sel", "SSE.Views.ChartSettingsDlg.textAlt": "Teks alternatif", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, autoshape, grafik, atau tabel.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAutoEach": "<PERSON><PERSON><PERSON><PERSON> untuk masing-masing", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Sumbu Berpotongan", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Opsi sumbu", "SSE.Views.ChartSettingsDlg.textAxisPos": "Posisi sumbu", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Pengaturan sumbu", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Di antara tanda centang", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON>wa<PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON> ka<PERSON>i", "SSE.Views.ChartSettingsDlg.textCenter": "Tengah", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "<PERSON><PERSON><PERSON> bagan &<br><PERSON><PERSON> bagan", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON><PERSON> bagan", "SSE.Views.ChartSettingsDlg.textCross": "Silang", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "Di kolom", "SSE.Views.ChartSettingsDlg.textDataLabels": "Label data", "SSE.Views.ChartSettingsDlg.textDataRows": "Di baris", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON><PERSON><PERSON> legenda", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON> dan <PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Hubungkan titik data dengan garis", "SSE.Views.ChartSettingsDlg.textFit": "<PERSON><PERSON> lebar", "SSE.Views.ChartSettingsDlg.textFixed": "Fixed", "SSE.Views.ChartSettingsDlg.textFormat": "Format label", "SSE.Views.ChartSettingsDlg.textGaps": "Gaps", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Kelompokkan Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Sembunyikan", "SSE.Views.ChartSettingsDlg.textHideAxis": "Sembunyikan sumbu", "SSE.Views.ChartSettingsDlg.textHigh": "Tingg<PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "Sumbu horizontal", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horisontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Ratusan", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerTop": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.ChartSettingsDlg.textLabelDist": "Jarak label sumbu", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Interval antar Label ", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Opsi label", "SSE.Views.ChartSettingsDlg.textLabelPos": "Posisi label", "SSE.Views.ChartSettingsDlg.textLayout": "Layout", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Overlay kiri", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON>wa<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "Keterangan", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Atas", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON> ", "SSE.Views.ChartSettingsDlg.textLocationRange": "Rentang lokasi", "SSE.Views.ChartSettingsDlg.textLogScale": "<PERSON><PERSON><PERSON> logari<PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "Rendah", "SSE.Views.ChartSettingsDlg.textMajor": "Major", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Mayor dan minor", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON>e mayor", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "Markers", "SSE.Views.ChartSettingsDlg.textMarksInterval": "<PERSON><PERSON><PERSON> antar <PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Minor", "SSE.Views.ChartSettingsDlg.textMinorType": "Tipe minor", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON> minimum", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Di sebelah sumbu", "SSE.Views.ChartSettingsDlg.textNone": "Tidak ada", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON> overlay", "SSE.Views.ChartSettingsDlg.textOneCell": "Pindahkan tapi tidak digabungkan dengan sel", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Di Tanda Centang", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOverlay": "Overlay", "SSE.Views.ChartSettingsDlg.textReverse": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textReverseOrder": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON>a untuk semua", "SSE.Views.ChartSettingsDlg.textSelectData": "Pilih data", "SSE.Views.ChartSettingsDlg.textSeparator": "Pemisah label data", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON><PERSON><PERSON> batas grafik", "SSE.Views.ChartSettingsDlg.textShowData": "Tampilkan data di baris dan kolom tersembunyi", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON><PERSON><PERSON><PERSON> sel kosong sebagai", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON> gra<PERSON>k", "SSE.Views.ChartSettingsDlg.textSingle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Snapping <PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Rentang Sparkline", "SSE.Views.ChartSettingsDlg.textStraight": "Straight", "SSE.Views.ChartSettingsDlg.textStyle": "Model", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Ribu", "SSE.Views.ChartSettingsDlg.textTickOptions": "Opsi Tick", "SSE.Views.ChartSettingsDlg.textTitle": "Bagan - Pengaturan lan<PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Pengaturan Lanjut", "SSE.Views.ChartSettingsDlg.textTop": "Atas", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Pindahkan dan gabungkan dengan sel", "SSE.Views.ChartSettingsDlg.textType": "Tipe", "SSE.Views.ChartSettingsDlg.textTypeData": "Tipe & Data", "SSE.Views.ChartSettingsDlg.textUnits": "Unit tampilan", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "Sumbu vertikal", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Ju<PERSON>l sumbu X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "<PERSON><PERSON><PERSON> sumbu Y", "SSE.Views.ChartSettingsDlg.textZero": "Nol", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.ChartTypeDialog.errorComboSeries": "Untuk membuat grafik kombinasi, pilih setidaknya dua seri data.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Tipe grafik yang dipilih memerlukan sumbu sekunder yang digunakan di grafik saat ini. Pilih tipe grafik yang lain.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textSeries": "Seri", "SSE.Views.ChartTypeDialog.textStyle": "Model", "SSE.Views.ChartTypeDialog.textTitle": "Tipe bagan", "SSE.Views.ChartTypeDialog.textType": "Tipe", "SSE.Views.CreatePivotDialog.textDataRange": "Rentang data sumber", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON>lih lokasi untuk tabel", "SSE.Views.CreatePivotDialog.textExist": "Worksheet saat ini", "SSE.Views.CreatePivotDialog.textInvalidRange": "Rentang sel tidak valid", "SSE.Views.CreatePivotDialog.textNew": "Worksheet baru", "SSE.Views.CreatePivotDialog.textSelectData": "Pilih data", "SSE.Views.CreatePivotDialog.textTitle": "Buat tabel pivot", "SSE.Views.CreatePivotDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.CreateSparklineDialog.textDataRange": "Rentang data sumber", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON>, lokasi penempetan sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Rentang sel tidak valid", "SSE.Views.CreateSparklineDialog.textSelectData": "Pilih data", "SSE.Views.CreateSparklineDialog.textTitle": "Buat Sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.DataTab.capBtnGroup": "Grup", "SSE.Views.DataTab.capBtnTextCustomSort": "Atur sortasi", "SSE.Views.DataTab.capBtnTextDataValidation": "Validasi data", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Ha<PERSON> duplik<PERSON>", "SSE.Views.DataTab.capBtnTextToCol": "Teks ke kolom", "SSE.Views.DataTab.capBtnUngroup": "Pisahkan dari grup", "SSE.Views.DataTab.capDataExternalLinks": "Tautan eksternal", "SSE.Views.DataTab.capDataFromText": "Dapatkan Data", "SSE.Views.DataTab.mniFromFile": "Dari TXT/CSV Lokal", "SSE.Views.DataTab.mniFromUrl": "Dari Alamat Web TXT/CSV", "SSE.Views.DataTab.textBelow": "Ringkasan baris di bawah detail.", "SSE.Views.DataTab.textClear": "Bersihkan Outline", "SSE.Views.DataTab.textColumns": "Pisahkan Grup Kolom", "SSE.Views.DataTab.textGroupColumns": "Kelompokkan Kolom", "SSE.Views.DataTab.textGroupRows": "Kelompokkan Baris", "SSE.Views.DataTab.textRightOf": "<PERSON><PERSON><PERSON> di sebelah kanan <PERSON>", "SSE.Views.DataTab.textRows": "Pisahkan Grup Baris", "SSE.Views.DataTab.tipCustomSort": "Atur sortasi", "SSE.Views.DataTab.tipDataFromText": "Dapatkan data dari file Text/CSV", "SSE.Views.DataTab.tipDataValidation": "Validasi data", "SSE.Views.DataTab.tipExternalLinks": "Lihat file lain yang ditautkan ke spreadsheet ini", "SSE.Views.DataTab.tipGroup": "Kelompokkan rentang dari sel", "SSE.Views.DataTab.tipRemDuplicates": "Hapus baris duplikat dari sheet", "SSE.Views.DataTab.tipToColumns": "Pisahkan teks sel menjadi kolom", "SSE.Views.DataTab.tipUngroup": "Pisahkan grup rentang sel", "SSE.Views.DataValidationDialog.errorFormula": "<PERSON>lai saat ini mengevaluasi error. <PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan?", "SSE.Views.DataValidationDialog.errorInvalid": "<PERSON><PERSON> yang <PERSON>a masukkan di ruas \"{0}\" tidak valid.", "SSE.Views.DataValidationDialog.errorInvalidDate": "<PERSON><PERSON> yang Anda masukkan ke ruas \"{0}\" tidak valid.", "SSE.Views.DataValidationDialog.errorInvalidList": "Sumber daftar harus berupa daftar yang dibatasi, atau referensi ke satu baris atau kolom.", "SSE.Views.DataValidationDialog.errorInvalidTime": "<PERSON><PERSON><PERSON> yang <PERSON>a masukkan ke ruas \"{0}\" tidak valid.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "<PERSON><PERSON><PERSON> \"{1}\" harus lebih dari atau sama dengan ruas \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Anda harus memasukkan kedua nilai di ruas \"{0}\" dan ruas \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Anda harus memasukkan nilai di ruas \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "<PERSON>a rentang yang Anda pilih tidak bisa ditemukan.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "<PERSON><PERSON> negatif tidak bisa digunakan pada kondisi \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "<PERSON><PERSON><PERSON> \"{0}\" harus berupa sebuah nilai numerik, eksp<PERSON>i numerik, atau mengacu ke sel yang memuat sebuah nilai numerik.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON> kes<PERSON>han", "SSE.Views.DataValidationDialog.strInput": "<PERSON><PERSON><PERSON><PERSON> pesan", "SSE.Views.DataValidationDialog.strSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "Ijinkan", "SSE.Views.DataValidationDialog.textApply": "Terap<PERSON> perubahan ini ke semua sel lain dengan pengaturan yang sama", "SSE.Views.DataValidationDialog.textCellSelected": "Saat sel dipilih, tampilkan pesan masukan ini", "SSE.Views.DataValidationDialog.textCompare": "<PERSON><PERSON><PERSON> den<PERSON>", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON> akhir", "SSE.Views.DataValidationDialog.textEndTime": "<PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.DataValidationDialog.textError": "<PERSON><PERSON> k<PERSON>han", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Abaikan kosong", "SSE.Views.DataValidationDialog.textInput": "<PERSON><PERSON><PERSON><PERSON> pesan", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Pilih data", "SSE.Views.DataValidationDialog.textShowDropDown": "<PERSON><PERSON><PERSON><PERSON> list drop-down di sel", "SSE.Views.DataValidationDialog.textShowError": "<PERSON><PERSON><PERSON><PERSON> peringatan eror setelah data tidak valid dimasukkan", "SSE.Views.DataValidationDialog.textShowInput": "<PERSON><PERSON><PERSON><PERSON> pesan yang masuk ketika sel dipilih", "SSE.Views.DataValidationDialog.textSource": "Sumber", "SSE.Views.DataValidationDialog.textStartDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartTime": "<PERSON><PERSON><PERSON> mulai", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "Model", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Saat pengguna memasukkan data yang tidak valid, tunjukkan peringatan kesalahan ini", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtBetween": "diantara", "SSE.Views.DataValidationDialog.txtDate": "Tanggal", "SSE.Views.DataValidationDialog.txtDecimal": "Desimal", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEqual": "sama dengan", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "<PERSON><PERSON><PERSON> atau <PERSON>", "SSE.Views.DataValidationDialog.txtLength": "Panjang", "SSE.Views.DataValidationDialog.txtLessThan": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "<PERSON><PERSON> atau <PERSON>", "SSE.Views.DataValidationDialog.txtList": "List", "SSE.Views.DataValidationDialog.txtNotBetween": "tidak diantara", "SSE.Views.DataValidationDialog.txtNotEqual": "Tidak sama dengan", "SSE.Views.DataValidationDialog.txtOther": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartTime": "<PERSON><PERSON><PERSON> mulai", "SSE.Views.DataValidationDialog.txtTextLength": "Panjang teks", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "Bilangan bulat", "SSE.Views.DigitalFilterDialog.capAnd": "<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition1": "sama dengan", "SSE.Views.DigitalFilterDialog.capCondition10": "tidak di<PERSON>hiri dengan", "SSE.Views.DigitalFilterDialog.capCondition11": "be<PERSON>i", "SSE.Views.DigitalFilterDialog.capCondition12": "tidak memiliki", "SSE.Views.DigitalFilterDialog.capCondition2": "Tidak sama dengan", "SSE.Views.DigitalFilterDialog.capCondition3": "lebih besar dari", "SSE.Views.DigitalFilterDialog.capCondition4": "lebih besar dari atau sama dengan", "SSE.Views.DigitalFilterDialog.capCondition5": "lebih kecil dari", "SSE.Views.DigitalFilterDialog.capCondition6": "lebih kecil dari atau sama dengan", "SSE.Views.DigitalFilterDialog.capCondition7": "dimulai dari", "SSE.Views.DigitalFilterDialog.capCondition8": "tidak diawali dari", "SSE.Views.DigitalFilterDialog.capCondition9": "<PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capOr": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.textNoFilter": "tanpa filter", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON><PERSON> baris ketika", "SSE.Views.DigitalFilterDialog.textUse1": "Gunakan ? untuk menampilkan semua karakter tunggal", "SSE.Views.DigitalFilterDialog.textUse2": "Gunakan * untuk menampilkan semua rang<PERSON>an karakter", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON>an", "SSE.Views.DocumentHolder.advancedEquationText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedImgText": "Pengaturan Lanjut untuk Gambar", "SSE.Views.DocumentHolder.advancedShapeText": "Pengaturan Lanjut untuk Bentuk", "SSE.Views.DocumentHolder.advancedSlicerText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.allLinearText": "Semua - Linear", "SSE.Views.DocumentHolder.allProfText": "Semua - Profesional", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.bulletsText": "Butir & Penomoran", "SSE.Views.DocumentHolder.centerCellText": "Rata <PERSON>", "SSE.Views.DocumentHolder.chartDataText": "Pilih <PERSON>gan", "SSE.Views.DocumentHolder.chartText": "Pengaturan Lanjut untuk Bagan", "SSE.Views.DocumentHolder.chartTypeText": "Ubah Tipe Bagan", "SSE.Views.DocumentHolder.currLinearText": "Saat Ini - Linear", "SSE.Views.DocumentHolder.currProfText": "Saat Ini - Profesional", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Rotasi Teks Keatas", "SSE.Views.DocumentHolder.direct90Text": "Rotasi Teks Kebawah", "SSE.Views.DocumentHolder.directHText": "Horisontal", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.editChartText": "Edit Data", "SSE.Views.DocumentHolder.editHyperlinkText": "Edit Hyperlink", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "Baris di Atas", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTex", "SSE.Views.DocumentHolder.originalSizeText": "Ukuran Sebenarnya", "SSE.Views.DocumentHolder.removeHyperlinkText": "Hapus Hyperlink", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "Data Kolom", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.strDelete": "Hilangkan Tandatangan", "SSE.Views.DocumentHolder.strDetails": "Det<PERSON>", "SSE.Views.DocumentHolder.strSetup": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.strSign": "Tandatangan", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Jalankan di Background", "SSE.Views.DocumentHolder.textArrangeBackward": "Mundurkan", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textAverage": "<PERSON>a-rata", "SSE.Views.DocumentHolder.textBullets": "Butir", "SSE.Views.DocumentHolder.textCount": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Fit", "SSE.Views.DocumentHolder.textEditPoints": "<PERSON>", "SSE.Views.DocumentHolder.textEntriesList": "<PERSON><PERSON><PERSON> dari daftar drop-down", "SSE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFreezePanes": "Freeze Panes", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromUrl": "Dari URL", "SSE.Views.DocumentHolder.textListSettings": "List Pengaturan", "SSE.Views.DocumentHolder.textMacro": "Tetapkan Makro", "SSE.Views.DocumentHolder.textMax": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "Lebih banyak fungsi", "SSE.Views.DocumentHolder.textMoreFormats": "Lebih banyak format", "SSE.Views.DocumentHolder.textNone": "Tidak ada", "SSE.Views.DocumentHolder.textNumbering": "Penomoran", "SSE.Views.DocumentHolder.textReplace": "Ganti Gambar", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Rotasi 90° Berlawanan Jarum Jam", "SSE.Views.DocumentHolder.textRotate90": "Rotasi 90° Searah Jarum Jam", "SSE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Rata <PERSON>", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "Batalkan", "SSE.Views.DocumentHolder.textUnFreezePanes": "Batal bekukan panel", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON> panah", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Butir tanda centang", "SSE.Views.DocumentHolder.tipMarkersDash": "<PERSON><PERSON><PERSON> putus-putus", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Butir belah ketupat isi", "SSE.Views.DocumentHolder.tipMarkersFRound": "<PERSON><PERSON> ling<PERSON>n isi", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Butir persegi isi", "SSE.Views.DocumentHolder.tipMarkersHRound": "Butir bundar hollow", "SSE.Views.DocumentHolder.tipMarkersStar": "<PERSON><PERSON> bintang", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "Tambahkan komentar", "SSE.Views.DocumentHolder.txtAddNamedRange": "Tentukan nama", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "O<PERSON><PERSON>s pas lebar kolom", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Otomatis pas tinggi baris", "SSE.Views.DocumentHolder.txtClear": "Hapus", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Komentar", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Hyperlink", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Bersihkan Grup Sparkline yang Di<PERSON>h", "SSE.Views.DocumentHolder.txtClearSparklines": "Bersihkan Sparklines yang <PERSON>", "SSE.Views.DocumentHolder.txtClearText": "Teks", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON><PERSON> kolom", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCondFormat": "Format bersyarat", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON> kolom ubahan", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Tinggi baris ubahan", "SSE.Views.DocumentHolder.txtCustomSort": "Atur sortasi", "SSE.Views.DocumentHolder.txtCut": "Potong", "SSE.Views.DocumentHolder.txtDate": "Tanggal", "SSE.Views.DocumentHolder.txtDelete": "Hapus", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDistribHor": "Distribusikan arah horizontal", "SSE.Views.DocumentHolder.txtDistribVert": "Distribusikan arah vertikal", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON> komentar", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filter dari warna sel", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filter dari warna font", "SSE.Views.DocumentHolder.txtFilterValue": "Filter menurut nilai sel yang dipilih", "SSE.Views.DocumentHolder.txtFormula": "<PERSON>si<PERSON><PERSON> fungsi", "SSE.Views.DocumentHolder.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGetLink": "Dapatkan tautan ke rentang ini", "SSE.Views.DocumentHolder.txtGroup": "Grup", "SSE.Views.DocumentHolder.txtHide": "Sembunyikan", "SSE.Views.DocumentHolder.txtInsert": "<PERSON>sip<PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hyperlink", "SSE.Views.DocumentHolder.txtNumber": "Angka", "SSE.Views.DocumentHolder.txtNumFormat": "Format nomor", "SSE.Views.DocumentHolder.txtPaste": "Tempel", "SSE.Views.DocumentHolder.txtPercentage": "Persentase", "SSE.Views.DocumentHolder.txtReapply": "Terapkan Ulang", "SSE.Views.DocumentHolder.txtRefresh": "Segarkan", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON><PERSON> baris", "SSE.Views.DocumentHolder.txtRowHeight": "Atur Tinggi Baris", "SSE.Views.DocumentHolder.txtScientific": "Saintifik", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftLeft": "Pindahkan sel ke kiri", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON> sel ke kanan", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON> sel ke atas", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON><PERSON> komentar", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "<PERSON><PERSON><PERSON>as", "SSE.Views.DocumentHolder.txtSortFontColor": "Pilih Warna font di atas", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtText": "Teks", "SSE.Views.DocumentHolder.txtTextAdvanced": "Pengaturan Lanjut untuk Paragraf", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtUngroup": "Pisahkan dari grup", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON>ata<PERSON> vertikal", "SSE.Views.ExternalLinksDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textDelete": "<PERSON><PERSON><PERSON>an", "SSE.Views.ExternalLinksDlg.textDeleteAll": "<PERSON><PERSON><PERSON> semua tautan", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textSource": "Sumber", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Tak dikenal", "SSE.Views.ExternalLinksDlg.textUpdate": "<PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textUpdateAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "SSE.Views.ExternalLinksDlg.textUpdating": "Memperbarui...", "SSE.Views.ExternalLinksDlg.txtTitle": "Tautan eksternal", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotals", "SSE.Views.FieldSettingsDialog.textReport": "Form Laporan", "SSE.Views.FieldSettingsDialog.textTitle": "Pengaturan ruas", "SSE.Views.FieldSettingsDialog.txtAverage": "<PERSON>a-rata", "SSE.Views.FieldSettingsDialog.txtBlank": "Sisipkan baris kosong setelah masing-masing item", "SSE.Views.FieldSettingsDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON> di bawah grup", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompak", "SSE.Views.FieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCountNums": "Hitung angka", "SSE.Views.FieldSettingsDialog.txtCustomName": "<PERSON><PERSON> nama", "SSE.Views.FieldSettingsDialog.txtEmpty": "Tampilkan item tanpa data", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Outline", "SSE.Views.FieldSettingsDialog.txtProduct": "Produk", "SSE.Views.FieldSettingsDialog.txtRepeat": "Ulangi label item pada setiap baris", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Tampilkan subtotal", "SSE.Views.FieldSettingsDialog.txtSourceName": "Nama sumber:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Fungsi untuk Subtotal", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Tampilkan di atas grup", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON> Dokumen", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> se<PERSON>ai", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "Bantuan", "SSE.Views.FileMenu.btnHistoryCaption": "Riwayat Versi", "SSE.Views.FileMenu.btnInfoCaption": "Info Spreadsheet", "SSE.Views.FileMenu.btnPrintCaption": "Cetak", "SSE.Views.FileMenu.btnProtectCaption": "Proteks<PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON> yang <PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "Ganti nama", "SSE.Views.FileMenu.btnReturnCaption": "Ke<PERSON><PERSON> ke Spreadsheet", "SSE.Views.FileMenu.btnRightsCaption": "Hak Akses", "SSE.Views.FileMenu.btnSaveAsCaption": "Simpan sebagai", "SSE.Views.FileMenu.btnSaveCaption": "Simpan", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Simpan Salinan sebagai", "SSE.Views.FileMenu.btnSettingsCaption": "Pen<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnToEditCaption": "Edit Spreadsheet", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Spreadsheet Kosong", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Terapkan", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Tambah<PERSON> penulis", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tambah teks", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikasi", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Ubah hak akses", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentar", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Dibuat", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Pemilik", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Orang yang memiliki hak", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subyek", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tag", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Diunggah", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Ubah hak akses", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Orang yang memiliki hak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Terapkan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Mode Edit <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Gunakan sistem tanggal 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separator desimal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "<PERSON><PERSON> bah<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Cepat", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Bahasa formula", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Contoh: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "A<PERSON><PERSON>n kata dalam HURUF BESAR", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Abaikan kata dengan angka", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Pengat<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Tam<PERSON>lkan tombol Opsi Paste ketika konten sedang dipaste", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Referensi Style R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Pengaturan Regional", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Contoh: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "<PERSON><PERSON><PERSON>an komentar pada sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>han dari pengguna lain", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "<PERSON><PERSON><PERSON><PERSON> komentar yang telah diselesaikan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Tema interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Gunakan pemisah berdasarkan pengaturan regional", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Tiap 10 Menit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Tiap 30 Menit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Tiap 5 Menit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Tiap <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "<PERSON><PERSON><PERSON> otomatis", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Menyimpan versi intermedier", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Referensi Style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opsi AutoCorrect...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgaria", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Mode cache default", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Menghitung", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Sentimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Denmark", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "<PERSON><PERSON><PERSON><PERSON> dan pen<PERSON>n", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanyol", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "<PERSON><PERSON><PERSON><PERSON> bersama waktu-nyata. <PERSON><PERSON><PERSON> disimpan secara otomatis", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finlandia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungaria", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Inci", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korea", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "sebagai OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polandia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Proofing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Titik", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON> (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Tam<PERSON>lkan tombol Cetak Cepat dalam header editor", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Dokumen akan dicetak pada printer yang terakhir dipilih atau baku", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Wilayah", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romania", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Rusia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Aktifkan Semua", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Aktifkan semua macros tanpa notifikasi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovakia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Nonak<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Nonaktifkan semua macros tanpa notifikasi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "<PERSON><PERSON><PERSON> tombol \"Simpan\" untuk menyelaraskan perubahan yang Anda dan orang lain lakukan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Swedia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Gunakan tombol Alt untuk menavigasi antarmuka pengguna menggunakan keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Gunakan tombol Option untuk menavigasi antarmuka pengguna menggunakan keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnam", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Nonaktifkan semua macros dengan notifikasi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "sebagai Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "China", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Peringatan", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>gan password", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteksi spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON>gan tanda tangan", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edit spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "<PERSON><PERSON><PERSON> akan menghilangkan tanda tangan dari spreadsheet.<br><PERSON><PERSON><PERSON><PERSON><PERSON>?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Spreadsheet ini diproteksi oleh password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Spreadsheet ini perlu ditandatangani.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Tanda tangan valid sudah ditambahkan ke spreadhseet. Spreadsheet diproteksi untuk diedit.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Beberapa tanda tangan digital di spreadsheet tidak valid atau tidak bisa diverifikasi. Spreadsheet diproteksi untuk diedit.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON> tanda tangan", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON> warna", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Peringatan", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 skala warna", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 skala warna", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON><PERSON> tepi", "SSE.Views.FormatRulesEditDlg.textAppearance": "Penampilan batang", "SSE.Views.FormatRulesEditDlg.textApply": "Terapkan ke rentang", "SSE.Views.FormatRulesEditDlg.textAutomatic": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textAxis": "Sumbu", "SSE.Views.FormatRulesEditDlg.textBarDirection": "<PERSON><PERSON> batang", "SSE.Views.FormatRulesEditDlg.textBold": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "Pembatas", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON> te<PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON> tepi", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "<PERSON><PERSON> bawah", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Tidak bisa menambahkan format bersyarat.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Titik tengah sel", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textClear": "Hapus", "SSE.Views.FormatRulesEditDlg.textColor": "Warna teks", "SSE.Views.FormatRulesEditDlg.textContext": "Konteks", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Pembatas Bawah Diagonal", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Pembatas Atas Diagonal", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Masukkan formula yang valid.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Formula yang Anda masukkan tidak mengevaluasi angka, tanggal, waktu atau string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "<PERSON><PERSON> yang Anda masukkan bukan angka, tanggal, jam, atau string yang valid.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "<PERSON><PERSON> dari {0} harus lebih besar dari nilai {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "<PERSON><PERSON><PERSON><PERSON> angka antara {0} dan {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabel": "ketika {0} {1} dan", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "ketika {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "ketika bernilai", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Rentang data satu atau beberapa ikon tumpang tindih.<br>Atur nilai rentang data ikon agar tidak tumpang tindih.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON> <PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Rentang data tidak valid.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.FormatRulesEditDlg.textItalic": "Miring", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "<PERSON><PERSON> ke kanan", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Tepi kiri", "SSE.Views.FormatRulesEditDlg.textLongBar": "bar terpanjang", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Titik tengah", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minpoint", "SSE.Views.FormatRulesEditDlg.textNegative": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNewColor": "Tam<PERSON><PERSON> warna khusus baru", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Tidak ada tepi", "SSE.Views.FormatRulesEditDlg.textNone": "Tidak ada", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Satu atau lebih nilai yang ditetapkan bukan persentase yang valid.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "<PERSON><PERSON> {0} yang ditentukan bukan persentase yang valid.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Satu atau lebih nilai yang ditetapkan bukan persentil yang valid.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "<PERSON><PERSON> {0} yang ditentukan bukan persentil yang valid.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Pembatas Lu<PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercentile": "Persentil", "SSE.Views.FormatRulesEditDlg.textPosition": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPositive": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPresets": "Presets", "SSE.Views.FormatRulesEditDlg.textPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Anda tidak dapat menggunakan referensi relatif dalam kriteria format bersyarat untuk skala warna, batang data, dan kumpulan ikon", "SSE.Views.FormatRulesEditDlg.textReverse": "Urutan I<PERSON>", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Kanan sampai kiri", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "Sama seperti positif", "SSE.Views.FormatRulesEditDlg.textSelectData": "Pilih data", "SSE.Views.FormatRulesEditDlg.textShortBar": "bar terpendek", "SSE.Views.FormatRulesEditDlg.textShowBar": "Tampilkan hanya bar", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Tam<PERSON>lkan hanya icon", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Tipe referensi ini tidak dapat digunakan dalam formula format bersyarat.<br>Ubah referensi ke sel tunggal, atau gunakan referensi dengan fungsi worksheet, seperti =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solid", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Strikeout", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subskrip", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superskrip", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON> atas", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON> bawah", "SSE.Views.FormatRulesEditDlg.tipBorders": "Pembatas", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Format nomor", "SSE.Views.FormatRulesEditDlg.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Tanggal", "SSE.Views.FormatRulesEditDlg.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.FormatRulesEditDlg.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON> ikon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Angka", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Persentase", "SSE.Views.FormatRulesEditDlg.txtScientific": "Saintifik", "SSE.Views.FormatRulesEditDlg.txtText": "Teks", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Edit Aturan Pemformatan", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Aturan pemformatan baru", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev di atas rata-rata", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev di bawah rata-rata", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev di atas rata-rata", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev di bawah rata-rata", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev di atas rata-rata", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev di bawah rata-rata", "SSE.Views.FormatRulesManagerDlg.textAbove": "Di atas rata-rata", "SSE.Views.FormatRulesManagerDlg.textApply": "Terapkan ke", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "<PERSON><PERSON> sel dimulai dari", "SSE.Views.FormatRulesManagerDlg.textBelow": "<PERSON> bawah rata-rata", "SSE.Views.FormatRulesManagerDlg.textBetween": "antara {0} dan {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON> sel", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON><PERSON> warna berg<PERSON>", "SSE.Views.FormatRulesManagerDlg.textContains": "<PERSON><PERSON> sel memiliki", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Sel memiliki nilai yang kosong", "SSE.Views.FormatRulesManagerDlg.textContainsError": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> error", "SSE.Views.FormatRulesManagerDlg.textDelete": "Hapus", "SSE.Views.FormatRulesManagerDlg.textDown": "Pindah aturan ke bawah", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEdit": "Sunting", "SSE.Views.FormatRulesManagerDlg.textEnds": "<PERSON><PERSON> sel di<PERSON><PERSON>i dengan", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "<PERSON>a dengan atau diatas rata-rata", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "<PERSON>a dengan atau dibawah rata-rata", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Icon set", "SSE.Views.FormatRulesManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "tidak diantara {0} dan {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "<PERSON><PERSON> sel tidak memiliki", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Sel memiliki nilai yang tidak kosong", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "<PERSON><PERSON> tidak memiliki error", "SSE.Views.FormatRulesManagerDlg.textRules": "Peraturan", "SSE.Views.FormatRulesManagerDlg.textScope": "Tampilkan peraturan format untuk", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Pilih data", "SSE.Views.FormatRulesManagerDlg.textSelection": "<PERSON><PERSON>han saat ini", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Pivot ini", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Worksheet ini", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> ini", "SSE.Views.FormatRulesManagerDlg.textUnique": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textUp": "Pindah aturan ke atas", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Format bersyarat", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Desimal", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Terhubung ke sumber", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON><PERSON><PERSON> pemisah 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Simbol", "SSE.Views.FormatSettingsDialog.textTitle": "Format nomor", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON><PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Silakan masukkan format nomor custom dengan teliti. Editor Spreadsheet tidak memeriksa format pengaturan untuk error yang dapat memengaruhi file xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Tanggal", "SSE.Views.FormatSettingsDialog.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "Tidak ada", "SSE.Views.FormatSettingsDialog.txtNumber": "Angka", "SSE.Views.FormatSettingsDialog.txtPercentage": "Persentase", "SSE.Views.FormatSettingsDialog.txtSample": "Sampel", "SSE.Views.FormatSettingsDialog.txtScientific": "Saintifik", "SSE.Views.FormatSettingsDialog.txtText": "Teks", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Lebih dari satu digit (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Lebih dari dua digit (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Lebih dari tiga digit (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "<PERSON>lih Grup Fungsi", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtSearch": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "<PERSON>si<PERSON><PERSON> fungsi", "SSE.Views.FormulaTab.textAutomatic": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Ka<PERSON><PERSON>si sheet saat ini", "SSE.Views.FormulaTab.textCalculateWorkbook": "<PERSON><PERSON><PERSON><PERSON> workbook", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "<PERSON><PERSON><PERSON><PERSON> seluruh workbook", "SSE.Views.FormulaTab.tipWatch": "Tambahkan sel ke daftar Jendela Pengawas", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Autosum", "SSE.Views.FormulaTab.txtAutosumTip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormulaTip": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtMore": "Lebih banyak fungsi", "SSE.Views.FormulaTab.txtRecent": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtWatch": "<PERSON><PERSON> Pengawas", "SSE.Views.FormulaWizard.textAny": "<PERSON>pa saja", "SSE.Views.FormulaWizard.textArgument": "Argumen", "SSE.Views.FormulaWizard.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textFunctionRes": "<PERSON><PERSON>i", "SSE.Views.FormulaWizard.textHelp": "Bantuan di fungsi ini", "SSE.Views.FormulaWizard.textLogical": "logikal", "SSE.Views.FormulaWizard.textNoArgs": "Fungsi ini tidak memiliki argumen", "SSE.Views.FormulaWizard.textNumber": "Angka", "SSE.Views.FormulaWizard.textRef": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textText": "Teks", "SSE.Views.FormulaWizard.textTitle": "Argumen fungsi", "SSE.Views.FormulaWizard.textValue": "Hasil formula", "SSE.Views.HeaderFooterDialog.textAlign": "<PERSON><PERSON><PERSON> dengan margin halaman", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textBold": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "Tengah", "SSE.Views.HeaderFooterDialog.textColor": "Warna teks", "SSE.Views.HeaderFooterDialog.textDate": "Tanggal", "SSE.Views.HeaderFooterDialog.textDiffFirst": "<PERSON><PERSON> pertama yang berbeda", "SSE.Views.HeaderFooterDialog.textDiffOdd": "<PERSON><PERSON> ganjil dan genap yang berbeda", "SSE.Views.HeaderFooterDialog.textEven": "Halaman Genap", "SSE.Views.HeaderFooterDialog.textFileName": "Nama file", "SSE.Views.HeaderFooterDialog.textFirst": "Hal<PERSON> pertama", "SSE.Views.HeaderFooterDialog.textFooter": "Footer", "SSE.Views.HeaderFooterDialog.textHeader": "Header", "SSE.Views.HeaderFooterDialog.textInsert": "<PERSON>sip<PERSON>", "SSE.Views.HeaderFooterDialog.textItalic": "Miring", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "String teks yang Anda masukkan terlalu panjang. Kurangi jumlah karakter yang digunakan.", "SSE.Views.HeaderFooterDialog.textNewColor": "Tam<PERSON><PERSON> warna khusus baru", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "<PERSON><PERSON><PERSON><PERSON> halaman", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON> halaman", "SSE.Views.HeaderFooterDialog.textPresets": "Presets", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Skala dengan dokumen", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textStrikeout": "<PERSON><PERSON> ganda", "SSE.Views.HeaderFooterDialog.textSubscript": "Subskrip", "SSE.Views.HeaderFooterDialog.textSuperscript": "Superskrip", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Pen<PERSON><PERSON><PERSON> Header/Footer", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON> bawah", "SSE.Views.HeaderFooterDialog.tipFontName": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontSize": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Tampilan", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strRange": "Rentang", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Sheet", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON>g yang dipilih", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Masukkan caption di sini", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Masukkan link di sini", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Masukkan tooltip disini", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Tautan eksternal", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "<PERSON><PERSON><PERSON><PERSON>an", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Rentang data internal", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON>a yang di<PERSON>kan", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Pilih data", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Sheet", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Teks ScreenTip", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Pengaturan Hyperlink", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ruas ini harus berupa URL dengan format \"http://www.contoh.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "R<PERSON>s ini dibatasi 2083 karakter", "SSE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Fit", "SSE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON> men<PERSON>uk", "SSE.Views.ImageSettings.textEdit": "Sunting", "SSE.Views.ImageSettings.textEditObject": "<PERSON>", "SSE.Views.ImageSettings.textFlip": "Flip", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textFromUrl": "Dari URL", "SSE.Views.ImageSettings.textHeight": "Tingg<PERSON>", "SSE.Views.ImageSettings.textHint270": "Rotasi 90° Berlawanan Jarum Jam", "SSE.Views.ImageSettings.textHint90": "Rotasi 90° Searah Jarum Jam", "SSE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textInsert": "Ganti Gambar", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "Ukuran Sebenarnya", "SSE.Views.ImageSettings.textRecentlyUsed": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textRotate90": "Rotasi 90°", "SSE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textSize": "Ukuran", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "<PERSON><PERSON> pindah atau ubah dengan sel", "SSE.Views.ImageSettingsAdvanced.textAlt": "Teks alternatif", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, autoshape, grafik, atau tabel.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Secara Horizontal", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Pindahkan tapi tidak digabungkan dengan sel", "SSE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textSnap": "Snapping <PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Gambar - Pen<PERSON><PERSON><PERSON> la<PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Pindahkan dan gabungkan dengan sel", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipAbout": "Tentang", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Komentar", "SSE.Views.LeftMenu.tipFile": "File", "SSE.Views.LeftMenu.tipPlugins": "Plugins", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSupport": "Masukan & Dukungan", "SSE.Views.LeftMenu.txtDeveloper": "MODE DEVELOPER", "SSE.Views.LeftMenu.txtEditor": "Penyunting Spreadsheet", "SSE.Views.LeftMenu.txtLimit": "<PERSON><PERSON>", "SSE.Views.LeftMenu.txtTrial": "MODE TRIAL", "SSE.Views.LeftMenu.txtTrialDev": "Mode Trial Developer", "SSE.Views.MacroDialog.textMacro": "Nama macro", "SSE.Views.MacroDialog.textTitle": "Tetapkan makro", "SSE.Views.MainSettingsPrint.okButtonText": "Simpan", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON>wa<PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Landscape", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Portrait", "SSE.Views.MainSettingsPrint.strPrint": "Cetak", "SSE.Views.MainSettingsPrint.strPrintTitles": "Print judul", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Atas", "SSE.Views.MainSettingsPrint.textActualSize": "Ukuran Sebenarnya", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "Atur Opsi", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textFitPage": "Sesuaikan <PERSON> kedala<PERSON>", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "Sc<PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Print G<PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Print Heading <PERSON><PERSON> da<PERSON>", "SSE.Views.MainSettingsPrint.textRepeat": "Ulangi...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Ulangi kolom di kiri", "SSE.Views.MainSettingsPrint.textRepeatTop": "Ulangi baris di atas", "SSE.Views.MainSettingsPrint.textSettings": "Pengaturan untuk", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Rentang nama yang ada tidak bisa di edit dan tidak bisa membuat yang baru<br>jika ada beberapa yang sedang diedit.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "<PERSON>a yang di<PERSON>kan", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Peringatan", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Rentang data", "SSE.Views.NamedRangeEditDlg.textExistName": "KESALAHAN! Rentang dengan nama itu sudah ada", "SSE.Views.NamedRangeEditDlg.textInvalidName": "<PERSON>a harus dimulai dengan huruf atau garis bawah dan tidak boleh memiliki karakter yang tidak valid", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.NamedRangeEditDlg.textIsLocked": "KESALAHAN! Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON>a yang Anda coba gunakan sudah direferensikan dalam rumus sel. <PERSON><PERSON> gunakan nama lain.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Pilih data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "<PERSON> nama", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON>a baru", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON>g yang bernama", "SSE.Views.NamedRangePasteDlg.txtTitle": "Paste Name", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Rentang data", "SSE.Views.NameManagerDlg.textDelete": "Hapus", "SSE.Views.NameManagerDlg.textEdit": "Sunting", "SSE.Views.NameManagerDlg.textEmpty": "Tidak ada rentang bernama yang sudah dibuat.<br>Buat setidaknya satu rentang bernama dan itu akan muncul di ruas ini.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON>a yang di<PERSON>kan", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON> ke Sheet", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON> tabel", "SSE.Views.NameManagerDlg.textFilterWorkbook": "<PERSON><PERSON> nama <PERSON> ke Workbook", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Tidak ada rentang bernama yang sesuai dengan filter Anda.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON>g yang bernama", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON> nama", "SSE.Views.NameManagerDlg.warnDelete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus nama {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON>wa<PERSON>", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Atas", "SSE.Views.ParagraphSettings.strLineHeight": "Spasi Antar Baris", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingAfter": "Sesudah", "SSE.Views.ParagraphSettings.strSpacingBefore": "Sebelum", "SSE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "SSE.Views.ParagraphSettings.textAt": "Pada", "SSE.Views.ParagraphSettings.textAtLeast": "Sekurang-kurangnya", "SSE.Views.ParagraphSettings.textAuto": "Banyak", "SSE.Views.ParagraphSettings.textExact": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Tab yang ditentukan akan muncul pada ruas ini", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON> ka<PERSON>al semua", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Garis coret ganda", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Indentasi", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Spasi Antar Baris", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Setela<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Sebelum", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Spesial", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "oleh", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indentasi & Peletakan", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON> ganda", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subskrip", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superskrip", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Banyak", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Spasi antar karakter", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON> baku", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efek", "SSE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Menggantung", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(tidak ada)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Hapus", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Tentukan", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Tengah", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posisi Tab", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraf - Pengaturan lan<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "sama dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "tidak di<PERSON>hiri dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "be<PERSON>i", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "tidak memiliki", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "diantara", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "tidak diantara", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "Tidak sama dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "lebih besar dari", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "lebih besar dari atau sama dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "lebih kecil dari", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "lebih kecil dari atau sama dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "dimulai dari", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "tidak diawali dari", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Tampilkan item yang labelnya:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Tampilkan item yang:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Gunakan ? untuk menampilkan semua karakter tunggal", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Gunakan * untuk menampilkan semua rang<PERSON>an karakter", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "dan", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filter label", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textBy": "oleh", "SSE.Views.PivotGroupDialog.textDays": "<PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "<PERSON><PERSON><PERSON> pada", "SSE.Views.PivotGroupDialog.textError": "Ruas ini harus berupa suatu nilai numerik", "SSE.Views.PivotGroupDialog.textGreaterError": "Angka terakhir harus lebih besar dari angka awal.", "SSE.Views.PivotGroupDialog.textHour": "jam", "SSE.Views.PivotGroupDialog.textMin": "menit", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textQuart": "Ku<PERSON>r", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "<PERSON><PERSON><PERSON> pada", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Mengelompokkan", "SSE.Views.PivotSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFilters": "Filter", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "Tambah ke Kolom", "SSE.Views.PivotSettings.txtAddFilter": "Tambah ke Filter", "SSE.Views.PivotSettings.txtAddRow": "Tambah ke Baris", "SSE.Views.PivotSettings.txtAddValues": "Tambah ke Nilai", "SSE.Views.PivotSettings.txtFieldSettings": "Pen<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveBegin": "Pindah ke Awal", "SSE.Views.PivotSettings.txtMoveColumn": "Pindah ke Kolom", "SSE.Views.PivotSettings.txtMoveDown": "Pi<PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveEnd": "Pindah ke akhir", "SSE.Views.PivotSettings.txtMoveFilter": "Pindah ke Filter", "SSE.Views.PivotSettings.txtMoveRow": "Pindah ke Baris", "SSE.Views.PivotSettings.txtMoveUp": "Pindah Keatas", "SSE.Views.PivotSettings.txtMoveValues": "Pindah ke Nilai", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.strLayout": "<PERSON>a dan tata letak", "SSE.Views.PivotSettingsAdvanced.textAlt": "Teks Alternatif", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, autoshape, grafik atau tabel.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Paskan otomatis lebar kolom saat pembaruan", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Rentang data", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Sumber data", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Tampilkan ruas di area filter laporan", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON>, lalu atas", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Grand Total", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Header ruas", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.PivotSettingsAdvanced.textOver": "Atas, lalu turun", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Pilih data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "<PERSON><PERSON><PERSON><PERSON> untuk kolom", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Tampilkan header ruas untuk baris dan kolom", "SSE.Views.PivotSettingsAdvanced.textShowRows": "<PERSON><PERSON><PERSON><PERSON> untuk baris", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tabel Pivot - Pengaturan lan<PERSON>t", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Ruas filter laporan per kolom", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "<PERSON><PERSON>s filter laporan per baris", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.PivotSettingsAdvanced.txtName": "<PERSON><PERSON>", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "Grand Total", "SSE.Views.PivotTable.capLayout": "Layout <PERSON>", "SSE.Views.PivotTable.capSubtotals": "Subtotals", "SSE.Views.PivotTable.mniBottomSubtotals": "Tampilkan semua Subtotal di Bawah Grup", "SSE.Views.PivotTable.mniInsertBlankLine": "<PERSON><PERSON><PERSON><PERSON>aris Kosong setelah Masing-masing item", "SSE.Views.PivotTable.mniLayoutCompact": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> Bentuk Kompak", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Jangan <PERSON> Semua Label Item", "SSE.Views.PivotTable.mniLayoutOutline": "Tam<PERSON>lkan dalam Bentuk Outline", "SSE.Views.PivotTable.mniLayoutRepeat": "Ulangi Semua Label item", "SSE.Views.PivotTable.mniLayoutTabular": "<PERSON><PERSON><PERSON><PERSON> dalam Bentuk Tabular", "SSE.Views.PivotTable.mniNoSubtotals": "<PERSON><PERSON>", "SSE.Views.PivotTable.mniOffTotals": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "SSE.Views.PivotTable.mniOnColumnsTotals": "Hidup untuk Kolom Sa<PERSON>", "SSE.Views.PivotTable.mniOnRowsTotals": "Hidup untuk Baris Sa<PERSON>", "SSE.Views.PivotTable.mniOnTotals": "<PERSON><PERSON><PERSON> untuk <PERSON> dan <PERSON>", "SSE.Views.PivotTable.mniRemoveBlankLine": "Hapus Baris Ko<PERSON>g set<PERSON>", "SSE.Views.PivotTable.mniTopSubtotals": "Tam<PERSON><PERSON>an semua Subtotal di Atas Grup", "SSE.Views.PivotTable.textColBanded": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textColHeader": "Header <PERSON><PERSON>", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "Header <PERSON>", "SSE.Views.PivotTable.tipCreatePivot": "Sisipkan Tabel Pivot", "SSE.Views.PivotTable.tipGrandTotals": "<PERSON><PERSON><PERSON><PERSON> atau sembunyikan total keseluruhan", "SSE.Views.PivotTable.tipRefresh": "Update informasi dari sumber data", "SSE.Views.PivotTable.tipRefreshCurrent": "Perbarui informasi dari sumber data untuk tabel saat ini", "SSE.Views.PivotTable.tipSelect": "<PERSON><PERSON><PERSON> seluruh tabel pivot", "SSE.Views.PivotTable.tipSubtotals": "<PERSON><PERSON><PERSON><PERSON> atau sembunyikan subtotal", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Kustom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "<PERSON>bel Pivot", "SSE.Views.PivotTable.txtRefresh": "Refresh", "SSE.Views.PivotTable.txtRefreshAll": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Tabel Pivot Gaya Gelap", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Tabel Pivot Gaya Te<PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Tabel Pivot Gaya Medium", "SSE.Views.PrintSettings.btnDownload": "Simpan & Download", "SSE.Views.PrintSettings.btnPrint": "Simpan & Print", "SSE.Views.PrintSettings.strBottom": "<PERSON>wa<PERSON>", "SSE.Views.PrintSettings.strLandscape": "Landscape", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Portrait", "SSE.Views.PrintSettings.strPrint": "Cetak", "SSE.Views.PrintSettings.strPrintTitles": "Cetak judul", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "Atas", "SSE.Views.PrintSettings.textActualSize": "Ukuran sebenarnya", "SSE.Views.PrintSettings.textAllSheets": "Semua sheet", "SSE.Views.PrintSettings.textCurrentSheet": "Sheet saat ini", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "<PERSON><PERSON> u<PERSON>an", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textFitPage": "Sesuaikan <PERSON> kedala<PERSON>", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textHideDetails": "Sembunyikan detail", "SSE.Views.PrintSettings.textIgnore": "Abaikan area cetak", "SSE.Views.PrintSettings.textLayout": "Layout", "SSE.Views.PrintSettings.textPageOrientation": "Orientasi ha<PERSON>", "SSE.Views.PrintSettings.textPageScaling": "Sc<PERSON>", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON> halaman", "SSE.Views.PrintSettings.textPrintGrid": "Cetak garis kisi", "SSE.Views.PrintSettings.textPrintHeadings": "Cetak tajuk baris dan kolom", "SSE.Views.PrintSettings.textPrintRange": "Rentang cetak", "SSE.Views.PrintSettings.textRange": "Rentang", "SSE.Views.PrintSettings.textRepeat": "Ulangi...", "SSE.Views.PrintSettings.textRepeatLeft": "Ulangi kolom di kiri", "SSE.Views.PrintSettings.textRepeatTop": "Ulangi baris di atas", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Pengaturan Sheet", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowHeadings": "<PERSON><PERSON><PERSON><PERSON> Heading <PERSON><PERSON> dan <PERSON>", "SSE.Views.PrintSettings.textTitle": "Pengaturan cetak", "SSE.Views.PrintSettings.textTitlePDF": "Pengaturan PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON> pertama", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON> pertama", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON> yang <PERSON>n", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON> yang <PERSON>n", "SSE.Views.PrintTitlesDialog.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.PrintTitlesDialog.textLeft": "Ulangi kolom di kiri", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textRepeat": "Ulangi...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "Cetak judul", "SSE.Views.PrintTitlesDialog.textTop": "Ulangi baris di atas", "SSE.Views.PrintWithPreview.txtActualSize": "Ukuran Sebenarnya", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Terapkan ke semua sheet", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON>wa<PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Sheet saat ini", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "Atur Opsi", "SSE.Views.PrintWithPreview.txtEmptyTable": "Tidak ada yang bisa diprint karena tabelnya kosong", "SSE.Views.PrintWithPreview.txtFitCols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtFitPage": "Sesuaikan <PERSON> kedala<PERSON>", "SSE.Views.PrintWithPreview.txtFitRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Garis grid dan heading", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Pen<PERSON><PERSON><PERSON> Header/Footer", "SSE.Views.PrintWithPreview.txtIgnore": "Abaikan Area Print", "SSE.Views.PrintWithPreview.txtLandscape": "Landscape", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "dari {0}", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "<PERSON><PERSON> halaman salah", "SSE.Views.PrintWithPreview.txtPageOrientation": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPortrait": "Portrait", "SSE.Views.PrintWithPreview.txtPrint": "Cetak", "SSE.Views.PrintWithPreview.txtPrintGrid": "Print G<PERSON>", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Print Heading <PERSON><PERSON> da<PERSON>", "SSE.Views.PrintWithPreview.txtPrintRange": "Rentang Print", "SSE.Views.PrintWithPreview.txtPrintTitles": "Print judul", "SSE.Views.PrintWithPreview.txtRepeat": "Ulangi...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Ulangi kolom di kiri", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Ulangi baris di atas", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Simpan", "SSE.Views.PrintWithPreview.txtScaling": "Sc<PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Pengaturan dari sheet", "SSE.Views.PrintWithPreview.txtSheet": "Sheet: {0}", "SSE.Views.PrintWithPreview.txtTop": "Atas", "SSE.Views.ProtectDialog.textExistName": "KESALAHAN! Rentang dengan judul itu sudah ada", "SSE.Views.ProtectDialog.textInvalidName": "Rentang judul harus diawali dengan huruf dan hanya boleh berisi huruf, ang<PERSON>, dan spasi.", "SSE.Views.ProtectDialog.textInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.ProtectDialog.textSelectData": "Pilih data", "SSE.Views.ProtectDialog.txtAllow": "Izinkan semua pengguna sheet ini untuk", "SSE.Views.ProtectDialog.txtAutofilter": "<PERSON><PERSON><PERSON>Filter", "SSE.Views.ProtectDialog.txtDelCols": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtDelRows": "Hapus Baris", "SSE.Views.ProtectDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.ProtectDialog.txtFormatCells": "Format sel", "SSE.Views.ProtectDialog.txtFormatCols": "Format kolom", "SSE.Views.ProtectDialog.txtFormatRows": "Format baris", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Password konfirmasi tidak sama", "SSE.Views.ProtectDialog.txtInsCols": "Sisipkan kolom", "SSE.Views.ProtectDialog.txtInsHyper": "Sisipkan hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "Sisipkan Baris", "SSE.Views.ProtectDialog.txtObjs": "<PERSON>", "SSE.Views.ProtectDialog.txtOptional": "opsional", "SSE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "<PERSON><PERSON><PERSON> dan Grafik Pivot", "SSE.Views.ProtectDialog.txtProtect": "Proteks<PERSON>", "SSE.Views.ProtectDialog.txtRange": "Rentang", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON> password", "SSE.Views.ProtectDialog.txtScen": "<PERSON> s<PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "<PERSON><PERSON><PERSON> sel yang terkunci", "SSE.Views.ProtectDialog.txtSelUnLocked": "<PERSON><PERSON><PERSON> sel yang tidak dikunci", "SSE.Views.ProtectDialog.txtSheetDescription": "<PERSON><PERSON><PERSON> perubahan yang tidak diinginkan oleh orang lain dengan membatasi kemampuan mereka untuk mengedit.", "SSE.Views.ProtectDialog.txtSheetTitle": "Proteksi Sheet", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Peringatan: Tidak bisa dipulihkan jika Anda kehilangan atau lupa kata sandi. Simpan di tempat yang aman.", "SSE.Views.ProtectDialog.txtWBDescription": "Untuk mencegah pengguna lain menampilkan worksheet tersembunyi, men<PERSON><PERSON><PERSON>, me<PERSON><PERSON><PERSON>, men<PERSON><PERSON><PERSON>, atau menyembunyikan worksheet dan mengganti nama worksheet, Anda bisa melindungi struktur workbook dengan password.", "SSE.Views.ProtectDialog.txtWBTitle": "Proteksi struktur Workbook", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Hapus", "SSE.Views.ProtectRangesDlg.textEdit": "Sunting", "SSE.Views.ProtectRangesDlg.textEmpty": "Tidak ada rentang yang diizinkan untuk diubah.", "SSE.Views.ProtectRangesDlg.textNew": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textProtect": "Proteksi Sheet", "SSE.Views.ProtectRangesDlg.textPwd": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Rentang", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Rentang dibuka dengan password saat sheet diproteksi (ini hanya berfungsi untuk sel yang terkunci)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON> rent<PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "Rentang baru", "SSE.Views.ProtectRangesDlg.txtNo": "Tidak", "SSE.Views.ProtectRangesDlg.txtTitle": "Izinkan pengguna mengedit rentang", "SSE.Views.ProtectRangesDlg.txtYes": "Ya", "SSE.Views.ProtectRangesDlg.warnDelete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus nama {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "<PERSON><PERSON><PERSON> menghapus nilai duplikat, pilih satu atau lebih kolom yang memiliki duplikat.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Data saya memiliki header", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Ha<PERSON> duplik<PERSON>", "SSE.Views.RightMenu.txtCellSettings": "Pen<PERSON><PERSON><PERSON> sel", "SSE.Views.RightMenu.txtChartSettings": "Pengaturan <PERSON>", "SSE.Views.RightMenu.txtImageSettings": "Pengaturan Gambar", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtPivotSettings": "Pengaturan tabel pivot", "SSE.Views.RightMenu.txtSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON><PERSON><PERSON> tanda tangan", "SSE.Views.RightMenu.txtSlicerSettings": "Pengaturan slicer", "SSE.Views.RightMenu.txtSparklineSettings": "Pengaturan sparkline", "SSE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Pengaturan Text Art", "SSE.Views.ScaleDialog.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textError": "<PERSON><PERSON> yang dimasukkan tidak tepat.", "SSE.Views.ScaleDialog.textFewPages": "<PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Paskan ke", "SSE.Views.ScaleDialog.textHeight": "Tingg<PERSON>", "SSE.Views.ScaleDialog.textManyPages": "<PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "<PERSON><PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textTitle": "Pengat<PERSON><PERSON>", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON> maksimal untuk ruas ini adalah {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON> minimal untuk ruas ini adalah {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON> la<PERSON>", "SSE.Views.ShapeSettings.strChange": "Ubah Bentuk Otomatis", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "SSE.Views.ShapeSettings.strPattern": "Pola", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON> bayangan", "SSE.Views.ShapeSettings.strSize": "Ukuran", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Opasitas", "SSE.Views.ShapeSettings.strType": "Tipe", "SSE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan tidak tepat.<br><PERSON><PERSON><PERSON> masukkan nilai antara 0 pt dan 1584 pt.", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textEmptyPattern": "Tidak ada Pola", "SSE.Views.ShapeSettings.textFlip": "Flip", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromUrl": "Dari URL", "SSE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textHint270": "Rotasi 90° Berlawanan Jarum Jam", "SSE.Views.ShapeSettings.textHint90": "Rotasi 90° Searah Jarum Jam", "SSE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textImageTexture": "Gambar atau Tekstur", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoFill": "Tidak ada <PERSON>", "SSE.Views.ShapeSettings.textOriginalSize": "Ukuran Original", "SSE.Views.ShapeSettings.textPatternFill": "Pola", "SSE.Views.ShapeSettings.textPosition": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textRotate90": "Rotasi 90°", "SSE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "Rentangkan", "SSE.Views.ShapeSettings.textStyle": "Model", "SSE.Views.ShapeSettings.textTexture": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Tambah titik gradien", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON>rtas Coklat", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "Tidak ada <PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Pengganjal teks", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "<PERSON><PERSON> pindah atau ubah dengan sel", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Teks alternatif", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, autoshape, grafik, atau tabel.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Tanda panah", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> mulai", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON> mulai", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Miring", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Tipe cap", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON> akhir", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Datar", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Flipped", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Tingg<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Secara Horizontal", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Gabungkan <PERSON>e", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON> garis", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Siku-siku", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Pindahkan tapi tidak digabungkan dengan sel", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Izinkan teks keluar dari bentuk", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Ubah ukuran bentuk agar cocok ke teks", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Ukuran", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Snapping <PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Spacing di antara kolom", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Kotak teks", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Bentuk - Pengaturan <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "Atas", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Pindahkan dan gabungkan dengan sel", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Bobot & Panah", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Peringatan", "SSE.Views.SignatureSettings.strDelete": "Hilangkan Tandatangan", "SSE.Views.SignatureSettings.strDetails": "Det<PERSON>", "SSE.Views.SignatureSettings.strInvalid": "Tanda tangan tidak valid", "SSE.Views.SignatureSettings.strRequested": "Penandatangan yang diminta", "SSE.Views.SignatureSettings.strSetup": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.strSign": "Tandatangan", "SSE.Views.SignatureSettings.strSignature": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.strSigner": "Penandatangan", "SSE.Views.SignatureSettings.strValid": "Tanda tangan valid", "SSE.Views.SignatureSettings.txtContinueEditing": "Tetap edit", "SSE.Views.SignatureSettings.txtEditWarning": "<PERSON><PERSON><PERSON> akan menghilangkan tanda tangan dari spreadsheet.<br><PERSON><PERSON><PERSON><PERSON><PERSON>?", "SSE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghilangkan tandatangan ini?<br>Proses tidak bisa dikembalikan.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Spreadsheet ini perlu ditandatangani.", "SSE.Views.SignatureSettings.txtSigned": "Tanda tangan valid sudah ditambahkan ke spreadhseet. Spreadsheet diproteksi untuk diedit.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Beberapa tanda tangan digital di spreadsheet tidak valid atau tidak bisa diverifikasi. Spreadsheet diproteksi untuk diedit.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.strHideNoData": "Sembunyikan Item <PERSON>", "SSE.Views.SlicerSettings.strIndNoData": "Tunjukkan item tanpa data secara visual", "SSE.Views.SlicerSettings.strShowDel": "Tampilkan item yang dihapus dari sumber data", "SSE.Views.SlicerSettings.strShowNoData": "Tampilkan item tanpa data terakhir", "SSE.Views.SlicerSettings.strSorting": "<PERSON><PERSON><PERSON> dan pemfi<PERSON>an", "SSE.Views.SlicerSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "SSE.Views.SlicerSettings.textAsc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textAZ": "A sampai Z", "SSE.Views.SlicerSettings.textButtons": "Tombol", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "Tingg<PERSON>", "SSE.Views.SlicerSettings.textHor": "Horisontal", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textLargeSmall": "Terbesar ke Terkecil", "SSE.Views.SlicerSettings.textLock": "<PERSON>ak<PERSON><PERSON><PERSON> pengu<PERSON>an ukuran atau pemindahan", "SSE.Views.SlicerSettings.textNewOld": "terbaru sampai tertua", "SSE.Views.SlicerSettings.textOldNew": "tertua ke terbaru", "SSE.Views.SlicerSettings.textPosition": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSize": "Ukuran", "SSE.Views.SlicerSettings.textSmallLarge": "kecil ke besar", "SSE.Views.SlicerSettings.textStyle": "Model", "SSE.Views.SlicerSettings.textVert": "Vertikal", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z ke A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Tombol", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Tingg<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Sembunyikan Item <PERSON>", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Tunjukkan item tanpa data secara visual", "SSE.Views.SlicerSettingsAdvanced.strReferences": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Tampilkan item yang dihapus dari sumber data", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "<PERSON><PERSON><PERSON><PERSON> header", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Tampilkan item tanpa data terakhir", "SSE.Views.SlicerSettingsAdvanced.strSize": "Ukuran", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sortasi & Pemfilteran", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Model", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Gaya & Ukuran", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "<PERSON><PERSON> pindah atau ubah dengan sel", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Teks alternatif", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, autoshape, grafik atau tabel.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A sampai Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Beri nama untuk dipakai di formula", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Header", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "Terbesar ke Terkecil", "SSE.Views.SlicerSettingsAdvanced.textName": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "terbaru sampai tertua", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "tertua ke terbaru", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Pindahkan tapi tidak digabungkan dengan sel", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "kecil ke besar", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Snapping <PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON>a sumber", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Pengaturan - <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Pindahkan dan gabungkan dengan sel", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z ke A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.SortDialog.errorEmpty": "<PERSON><PERSON><PERSON> kriteria sortasi harus memiliki kolom atau baris yang sudah ditentukan.", "SSE.Views.SortDialog.errorMoreOneCol": "<PERSON><PERSON>h dari satu kolom dipilih", "SSE.Views.SortDialog.errorMoreOneRow": "Le<PERSON>h dari satu baris dipilih", "SSE.Views.SortDialog.errorNotOriginalCol": "<PERSON><PERSON><PERSON> yang <PERSON>a pilih tidak di rentang asli yang dipilih.", "SSE.Views.SortDialog.errorNotOriginalRow": "<PERSON><PERSON> yang <PERSON>a pilih tidak berada di rentang asli yang dipilih.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 sedang diurutkan menurut warna yang sama lebih dari 1 kali.<br>Hapus kriteria sortasi duplikat dan coba lagi", "SSE.Views.SortDialog.errorSameColumnValue": "%1 sedang diurutkan berdasarkan nilai lebih dari 1 kali.<br>Hapus kriteria sortasi duplikat dan coba lagi", "SSE.Views.SortDialog.textAdd": "Tambah level", "SSE.Views.SortDialog.textAsc": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textAZ": "A sampai Z", "SSE.Views.SortDialog.textBelow": "<PERSON> bawah", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> sel", "SSE.Views.SortDialog.textColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textCopy": "Copy level", "SSE.Views.SortDialog.textDelete": "Hapus level", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "Pindah ke level bawah", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON>", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(Lebih banyak kolom...)", "SSE.Views.SortDialog.textMoreRows": "(Lebih banyak baris...)", "SSE.Views.SortDialog.textNone": "Tidak ada", "SSE.Views.SortDialog.textOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Sortir pada", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.SortDialog.textThenBy": "Then by", "SSE.Views.SortDialog.textTop": "Atas", "SSE.Views.SortDialog.textUp": "Pindah ke level atas", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z ke A", "SSE.Views.SortDialog.txtInvalidRange": "Rentang sel tidak valid.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "<PERSON><PERSON><PERSON> naik (A sampai Z) dengan", "SSE.Views.SortFilterDialog.textDesc": "<PERSON><PERSON><PERSON> (Z sampai A) dengan", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SortOptionsDialog.textCase": "Harus sama persis", "SSE.Views.SortOptionsDialog.textHeaders": "Data saya memiliki header", "SSE.Views.SortOptionsDialog.textLeftRight": "So<PERSON>si kiri ke kanan", "SSE.Views.SortOptionsDialog.textOrientation": "Orientasi", "SSE.Views.SortOptionsDialog.textTitle": "Pen<PERSON><PERSON><PERSON>", "SSE.Views.SortOptionsDialog.textTopBottom": "Sortasi dari atas ke bawah", "SSE.Views.SpecialPasteDialog.textAdd": "Tambahkan", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "<PERSON><PERSON> kosong", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON> kolom", "SSE.Views.SpecialPasteDialog.textComments": "Komentar", "SSE.Views.SpecialPasteDialog.textDiv": "Bagi", "SSE.Views.SpecialPasteDialog.textFFormat": "Formula & Pemformatan", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formula & nomor format", "SSE.Views.SpecialPasteDialog.textFormats": "Format", "SSE.Views.SpecialPasteDialog.textFormulas": "Formulas", "SSE.Views.SpecialPasteDialog.textFWidth": "Formula & lebar kolom", "SSE.Views.SpecialPasteDialog.textMult": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textNone": "Tidak ada", "SSE.Views.SpecialPasteDialog.textOperation": "Operasi", "SSE.Views.SpecialPasteDialog.textPaste": "Tempel", "SSE.Views.SpecialPasteDialog.textSub": "Mengurang<PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpose", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Nilai & Pemformatan", "SSE.Views.SpecialPasteDialog.textVNFormat": "Nilai & nomor format", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON><PERSON><PERSON> k<PERSON>uali batas", "SSE.Views.Spellcheck.noSuggestions": "Tidak ada saran spelling", "SSE.Views.Spellcheck.textChange": "Ganti", "SSE.Views.Spellcheck.textChangeAll": "Ubah Semua", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtAddToDictionary": "Tambah ke Kamus", "SSE.Views.Spellcheck.txtClosePanel": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtComplete": "Pemeriksaan spelling sudah se<PERSON>ai", "SSE.Views.Spellcheck.txtDictionaryLanguage": "<PERSON><PERSON>", "SSE.Views.Spellcheck.txtNextTip": "Pergi ke kata berikutnya", "SSE.Views.Spellcheck.txtSpelling": "Spelling", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON> sa<PERSON> a<PERSON>)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Pinda<PERSON> ke akhir)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Paste sebelum sheet", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Pindahkan sebelum sheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} dari {1} catatan difilter", "SSE.Views.Statusbar.filteredText": "Mode filter", "SSE.Views.Statusbar.itemAverage": "<PERSON>a-rata", "SSE.Views.Statusbar.itemCopy": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemCount": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemDelete": "Hapus", "SSE.Views.Statusbar.itemHidden": "Tersemb<PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "Sembunyikan", "SSE.Views.Statusbar.itemInsert": "<PERSON>sip<PERSON>", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMove": "Pindah", "SSE.Views.Statusbar.itemProtect": "Proteks<PERSON>", "SSE.Views.Statusbar.itemRename": "Ganti nama", "SSE.Views.Statusbar.itemStatus": "Status penyimpanan", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Worksheet dengan nama ini sudah ada.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Nama sheet tidak boleh berisi karakter berikut: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON>", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.sheetIndexText": "Sheet {0} dari {1}", "SSE.Views.Statusbar.textAverage": "<PERSON>a-rata", "SSE.Views.Statusbar.textCount": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textMax": "<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Tam<PERSON><PERSON> warna khusus baru", "SSE.Views.Statusbar.textNoColor": "Tidak ada <PERSON>", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Tambah worksheet", "SSE.Views.Statusbar.tipFirst": "Gulir ke sheet pertama", "SSE.Views.Statusbar.tipLast": "G<PERSON>r ke sheet terakhir", "SSE.Views.Statusbar.tipListOfSheets": "List Sheet", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON> daftar sheet ke kanan", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON>r daftar sheet ke kiri", "SSE.Views.Statusbar.tipZoomFactor": "Pembesaran", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Pisahkan grup Sheet", "SSE.Views.Statusbar.zoomText": "Perbesar {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operasi tidak bisa dilakukan pada rentang sel yang dipilih.<br><PERSON>lih rentang data seragam yang berbeda dari yang sudah ada dan coba lagi.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operasi tidak bisa diselesaikan untuk rentang sel yang dipilih.<br><PERSON><PERSON><PERSON> rentang agar baris pertama tabel berada di baris yang sama<b>dan men<PERSON><PERSON><PERSON>an tabel yang overlap dengan tabel saat ini.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operasi tidak bisa diselesaikan untuk rentang sel yang dipilih.<br><PERSON><PERSON><PERSON> rentang yang tidak termasuk di tabel lain.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Formula array multi sel tidak diizinkan di tabel.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON><PERSON> ini diperlukan", "SSE.Views.TableOptionsDialog.txtFormat": "Buat tabel", "SSE.Views.TableOptionsDialog.txtInvalidRange": "KESALAHAN! Rentang sel tidak valid", "SSE.Views.TableOptionsDialog.txtNote": "Header harus tetap di baris yang sama, dan rentang tabel yang dihasilkan harus tumpang tindih dengan rentang tabel asli.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>", "SSE.Views.TableSettings.deleteRowText": "Hapus Baris", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnLeftText": "<PERSON>si<PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnRightText": "<PERSON>si<PERSON><PERSON>", "SSE.Views.TableSettings.insertRowAboveText": "Sisipkan Baris di Atas", "SSE.Views.TableSettings.insertRowBelowText": "Sisipkan Baris di Bawah", "SSE.Views.TableSettings.notcriticalErrorTitle": "Peringatan", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> pengaturan lan<PERSON>t", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Ubah ke rentang", "SSE.Views.TableSettings.textEdit": "Baris & Kolom", "SSE.Views.TableSettings.textEmptyTemplate": "Tidak ada template", "SSE.Views.TableSettings.textExistName": "KESALAHAN! Rentang dengan nama ini sudah ada", "SSE.Views.TableSettings.textFilter": "Tombol filter", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "Header", "SSE.Views.TableSettings.textInvalidName": "KESALAHAN! Nama tabel tidak tepat", "SSE.Views.TableSettings.textIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "Operasi panjang", "SSE.Views.TableSettings.textPivot": "Sisipkan Tabel Pivot", "SSE.Views.TableSettings.textRemDuplicates": "Ha<PERSON> duplik<PERSON>", "SSE.Views.TableSettings.textReservedName": "<PERSON>a yang Anda coba gunakan sudah direferensikan dalam rumus sel. <PERSON><PERSON> gunakan nama lain.", "SSE.Views.TableSettings.textResize": "Ubah ukuran tabel", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Pilih data", "SSE.Views.TableSettings.textSlicer": "Sisipkan slicer", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON>", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.warnLongOperation": "Operasi yang akan Anda lakukan mungkin membutukan waktu yang cukup lama untuk selesai.<br><PERSON><PERSON><PERSON><PERSON> anda yakin untuk lanjut?", "SSE.Views.TableSettingsAdvanced.textAlt": "Teks alternatif", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Representasi alternatif berbasis teks dari informasi objek visual, yang akan dibaca kepada orang dengan gangguan penglihatan atau kognitif untuk membantu mereka lebih memahami informasi yang ada dalam gambar, autoshape, grafik atau tabel.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabel - Pen<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "Kustom", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "Tabel Gaya Medium", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON> la<PERSON>", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "SSE.Views.TextArtSettings.strPattern": "Pola", "SSE.Views.TextArtSettings.strSize": "Ukuran", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Opasitas", "SSE.Views.TextArtSettings.strType": "Tipe", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan tidak tepat.<br><PERSON><PERSON><PERSON> masukkan nilai antara 0 pt dan 1584 pt.", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "Tidak ada Pola", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromUrl": "Dari URL", "SSE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textImageTexture": "Gambar atau Tekstur", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Tidak ada <PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "Pola", "SSE.Views.TextArtSettings.textPosition": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "Rentangkan", "SSE.Views.TextArtSettings.textStyle": "Model", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "Transformasikan", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Tambah titik gradien", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Hilangkan titik gradien", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON>rtas Coklat", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "Tidak ada <PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Tambahkan komentar", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "Komentar", "SSE.Views.Toolbar.capBtnInsHeader": "Header & Footer", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Simbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientasi", "SSE.Views.Toolbar.capBtnPageSize": "Ukuran", "SSE.Views.Toolbar.capBtnPrintArea": "Print Area", "SSE.Views.Toolbar.capBtnPrintTitles": "Print judul", "SSE.Views.Toolbar.capBtnScale": "Skala ke Fit", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Mundurkan", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgGroup": "Grup", "SSE.Views.Toolbar.capInsertChart": "Bagan", "SSE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "SSE.Views.Toolbar.capInsertImage": "Gambar", "SSE.Views.Toolbar.capInsertShape": "Bentuk", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Kotak Teks", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON> dari <PERSON>", "SSE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromUrl": "Gambar dari URL", "SSE.Views.Toolbar.textAddPrintArea": "Tambah ke Area Print", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON>", "SSE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON>", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> kiri", "SSE.Views.Toolbar.textAlignMiddle": "Rata <PERSON>", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON>", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAutoColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBold": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "Bawah: ", "SSE.Views.Toolbar.textBottomBorders": "Batas <PERSON>", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textClearPrintArea": "Bersihkan Area Print", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON><PERSON> Jarum Jam", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON> warna", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCustom": "Kustom", "SSE.Views.Toolbar.textDataBars": "Bar Data", "SSE.Views.Toolbar.textDelLeft": "Pindahkan sel ke kiri", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON> ke <PERSON>", "SSE.Views.Toolbar.textDiagDownBorder": "Pembatas Bawah Diagonal", "SSE.Views.Toolbar.textDiagUpBorder": "Pembatas Atas Diagonal", "SSE.Views.Toolbar.textDone": "Se<PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "Sunting Area yang Tampak", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON><PERSON> baris", "SSE.Views.Toolbar.textFewPages": "<PERSON><PERSON>", "SSE.Views.Toolbar.textHeight": "Tingg<PERSON>", "SSE.Views.Toolbar.textHideVA": "Sembunyikan Area yang Tampak", "SSE.Views.Toolbar.textHorizontal": "Teks <PERSON>tal", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItalic": "Miring", "SSE.Views.Toolbar.textItems": "Items", "SSE.Views.Toolbar.textLandscape": "Landscape", "SSE.Views.Toolbar.textLeft": "<PERSON><PERSON>: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textManageRule": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textManyPages": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Custom Terakhir", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMoreFormats": "Lebih banyak format", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON><PERSON> banyak halaman", "SSE.Views.Toolbar.textNewColor": "Tam<PERSON><PERSON> warna khusus baru", "SSE.Views.Toolbar.textNewRule": "Peratura<PERSON>", "SSE.Views.Toolbar.textNoBorders": "Tidak ada pembatas", "SSE.Views.Toolbar.textOnePage": "<PERSON><PERSON>", "SSE.Views.Toolbar.textOutBorders": "Pembatas Lu<PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "Custom Margin", "SSE.Views.Toolbar.textPortrait": "Portrait", "SSE.Views.Toolbar.textPrint": "Cetak", "SSE.Views.Toolbar.textPrintGridlines": "Print G<PERSON>", "SSE.Views.Toolbar.textPrintHeadings": "Cetak Tajuk", "SSE.Views.Toolbar.textPrintOptions": "Pengaturan Print", "SSE.Views.Toolbar.textRight": "Kanan: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Rotasi Teks Kebawah", "SSE.Views.Toolbar.textRotateUp": "Rotasi Teks Keatas", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSelection": "Dari pilihan saat ini", "SSE.Views.Toolbar.textSetPrintArea": "Atur Area Print", "SSE.Views.Toolbar.textShowVA": "Tampilkan Area yang Tampak", "SSE.Views.Toolbar.textStrikeout": "<PERSON><PERSON> ganda", "SSE.Views.Toolbar.textSubscript": "Subskrip", "SSE.Views.Toolbar.textSubSuperscript": "Subskrip/Superskrip", "SSE.Views.Toolbar.textSuperscript": "Superskrip", "SSE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabFile": "File", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "<PERSON>sip<PERSON>", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "Proteks<PERSON>", "SSE.Views.Toolbar.textTabView": "Lihat", "SSE.Views.Toolbar.textThisPivot": "Dari pivot ini", "SSE.Views.Toolbar.textThisSheet": "<PERSON>i worksheet ini", "SSE.Views.Toolbar.textThisTable": "Dari tabel ini", "SSE.Views.Toolbar.textTop": "Atas: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON> bawah", "SSE.Views.Toolbar.textVertical": "Teks Vertikal", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Pembesaran", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON>a bawah", "SSE.Views.Toolbar.tipAlignCenter": "Rata tengah", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignMiddle": "Rata di tengah", "SSE.Views.Toolbar.tipAlignRight": "<PERSON>a kanan", "SSE.Views.Toolbar.tipAlignTop": "Rata atas", "SSE.Views.Toolbar.tipAutofilter": "<PERSON><PERSON><PERSON> dan <PERSON>", "SSE.Views.Toolbar.tipBack": "Kembali", "SSE.Views.Toolbar.tipBorders": "Pembatas", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeChart": "Ubah Tipe Bagan", "SSE.Views.Toolbar.tipClearStyle": "Hapus", "SSE.Views.Toolbar.tipColorSchemas": "Ubah Skema Warna", "SSE.Views.Toolbar.tipCondFormat": "Format bersyarat", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCut": "Potong", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON> desimal", "SSE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleAccounting": "Style Akutansi", "SSE.Views.Toolbar.tipDigStyleCurrency": "Style <PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON>sen", "SSE.Views.Toolbar.tipEditChart": "<PERSON>", "SSE.Views.Toolbar.tipEditChartData": "Pilih data", "SSE.Views.Toolbar.tipEditChartType": "Ubah Tipe Bagan", "SSE.Views.Toolbar.tipEditHeader": "<PERSON> Header at<PERSON>", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipHAlighOle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> ob<PERSON>", "SSE.Views.Toolbar.tipImgGroup": "Satukan objek", "SSE.Views.Toolbar.tipIncDecimal": "Tambah desimal", "SSE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertChart": "Sisipkan Bagan", "SSE.Views.Toolbar.tipInsertChartSpark": "Sisipkan Bagan", "SSE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertHorizontalText": "Sisipkan kotak teks horizontal", "SSE.Views.Toolbar.tipInsertHyperlink": "Tambahkan hyperlink", "SSE.Views.Toolbar.tipInsertImage": "Sisipkan Gambar", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON><PERSON> sel", "SSE.Views.Toolbar.tipInsertShape": "Sisipkan Bentuk Otomatis", "SSE.Views.Toolbar.tipInsertSlicer": "Sisipkan slicer", "SSE.Views.Toolbar.tipInsertSmartArt": "Sisipkan SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Sisipkan sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Sisipkan simbol", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertText": "Sisipkan Teks", "SSE.Views.Toolbar.tipInsertTextart": "Sisipkan Text Art", "SSE.Views.Toolbar.tipInsertVerticalText": "Sisipkan kotak teks vertikal", "SSE.Views.Toolbar.tipMerge": "Merge dan center", "SSE.Views.Toolbar.tipNone": "Tidak ada", "SSE.Views.Toolbar.tipNumFormat": "Format nomor", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON> halaman", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPaste": "Tempel", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON> warna", "SSE.Views.Toolbar.tipPrint": "Cetak", "SSE.Views.Toolbar.tipPrintArea": "Print Area", "SSE.Views.Toolbar.tipPrintTitles": "Print judul", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "Simpan", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON>an per<PERSON>han yang Anda buat agar dapat dilihat oleh pengguna lain", "SSE.Views.Toolbar.tipScale": "Skala ke fit", "SSE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "SSE.Views.Toolbar.tipSendBackward": "Mundurkan", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSynchronize": "Dokumen telah diubah oleh pengguna lain. Silakan klik untuk menyimpan perubahan dan memuat ulang pembaruan.", "SSE.Views.Toolbar.tipTextFormatting": "Alat pemformatan teks lainnya", "SSE.Views.Toolbar.tipTextOrientation": "Orientasi", "SSE.Views.Toolbar.tipUndo": "Batalkan", "SSE.Views.Toolbar.tipVAlighOle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipVisibleArea": "Area yang tampak", "SSE.Views.Toolbar.tipWrap": "Lipat teks", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Komentar", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON> filter", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "Hyperlink", "SSE.Views.Toolbar.txtClearText": "Teks", "SSE.Views.Toolbar.txtCurrency": "<PERSON>", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "Tanggal", "SSE.Views.Toolbar.txtDateTime": "Tanggal & Jam", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Eksponensial", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFranc": "CHF Swiss franc", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtInteger": "Integer", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "Gabungkan Sampai", "SSE.Views.Toolbar.txtMergeCells": "Gabungkan Sel", "SSE.Views.Toolbar.txtMergeCenter": "Merge & Center", "SSE.Views.Toolbar.txtNamedRange": "Named ranges", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNoBorders": "Tidak ada pembatas", "SSE.Views.Toolbar.txtNumber": "Angka", "SSE.Views.Toolbar.txtPasteRange": "Paste Name", "SSE.Views.Toolbar.txtPercentage": "Persentase", "SSE.Views.Toolbar.txtPound": "£ Pound", "SSE.Views.Toolbar.txtRouble": "₽ Rubel", "SSE.Views.Toolbar.txtScheme1": "Office", "SSE.Views.Toolbar.txtScheme10": "Median", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme14": "Jendela Oriel", "SSE.Views.Toolbar.txtScheme15": "Origin", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "<PERSON><PERSON><PERSON> balik matahari", "SSE.Views.Toolbar.txtScheme18": "Teknik", "SSE.Views.Toolbar.txtScheme19": "Trek", "SSE.Views.Toolbar.txtScheme2": "Grayscale", "SSE.Views.Toolbar.txtScheme20": "Urban", "SSE.Views.Toolbar.txtScheme21": "Semangat", "SSE.Views.Toolbar.txtScheme22": "Office Baru", "SSE.Views.Toolbar.txtScheme3": "Puncak", "SSE.Views.Toolbar.txtScheme4": "Aspek", "SSE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme6": "Himpunan", "SSE.Views.Toolbar.txtScheme7": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme8": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme9": "Cetakan", "SSE.Views.Toolbar.txtScientific": "Saintifik", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "<PERSON><PERSON><PERSON> naik", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON> turun", "SSE.Views.Toolbar.txtSpecial": "Spesial", "SSE.Views.Toolbar.txtTableTemplate": "Format sebagai template tabel", "SSE.Views.Toolbar.txtText": "Teks", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Lepas Gabung Sel", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON>wa<PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "oleh", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Auto Filter Top 10", "SSE.Views.Top10FilterDialog.txtTop": "Atas", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filter Top 10", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Pengat<PERSON><PERSON> ruas nilai", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "<PERSON>a-rata", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 dari %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Hitung angka", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "<PERSON><PERSON> nama", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON> ka<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "<PERSON><PERSON><PERSON> dari", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Persentase Se<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Persentase dari kolom", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "<PERSON>sen dari total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Persentase dari baris", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produk", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Dijalankan Total di", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON> se<PERSON>ai", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Nama sumber:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Simpulkan nilai ruas dengan", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Hapus", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplikat", "SSE.Views.ViewManagerDlg.textEmpty": "Belum ada tampilan yang dibuat.", "SSE.Views.ViewManagerDlg.textGoTo": "<PERSON>gi ke tampilan", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON><PERSON> nama maksimum 128 karakter.", "SSE.Views.ViewManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRename": "Ganti nama", "SSE.Views.ViewManagerDlg.textRenameError": "Nama tampilan tidak boleh kosong.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Ubah nama tampilan", "SSE.Views.ViewManagerDlg.textViews": "Tampilan sheet", "SSE.Views.ViewManagerDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.ViewManagerDlg.txtTitle": "Pengaturan <PERSON>et", "SSE.Views.ViewManagerDlg.warnDeleteView": "Anda mencoba menghapus tampilan '%1' yang saat ini diaktifkan.<br>Tutup tampilan ini dan hapus?", "SSE.Views.ViewTab.capBtnFreeze": "Freeze Panes", "SSE.Views.ViewTab.capBtnSheetView": "Tampilan sheet", "SSE.Views.ViewTab.textAlwaysShowToolbar": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Gabungkan Sheet dan <PERSON>", "SSE.Views.ViewTab.textCreate": "<PERSON><PERSON>", "SSE.Views.ViewTab.textDefault": "standar", "SSE.Views.ViewTab.textFormula": "Bar formula", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFreezeRow": "Bekukan Baris Teratas", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "Headings", "SSE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON>", "SSE.Views.ViewTab.textLeftMenu": "<PERSON> Kiri", "SSE.Views.ViewTab.textManager": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textRightMenu": "<PERSON> kanan", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Tampillkan bayangan panel beku", "SSE.Views.ViewTab.textUnFreeze": "Batal Bekukan Panel", "SSE.Views.ViewTab.textZeros": "Tampilkan zeros", "SSE.Views.ViewTab.textZoom": "Pembesaran", "SSE.Views.ViewTab.tipClose": "Tutup tampilan sheet", "SSE.Views.ViewTab.tipCreate": "Buat tampilan sheet", "SSE.Views.ViewTab.tipFreeze": "Freeze panes", "SSE.Views.ViewTab.tipInterfaceTheme": "<PERSON><PERSON> ant<PERSON>", "SSE.Views.ViewTab.tipSheetView": "Tampilan sheet", "SSE.Views.WatchDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textAdd": "Tambahkan pengawas", "SSE.Views.WatchDialog.textBook": "Buku", "SSE.Views.WatchDialog.textCell": "<PERSON>l", "SSE.Views.WatchDialog.textDelete": "Hapus pengawas", "SSE.Views.WatchDialog.textDeleteAll": "<PERSON><PERSON> semua", "SSE.Views.WatchDialog.textFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textName": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "<PERSON><PERSON>", "SSE.Views.WatchDialog.txtTitle": "Jendela pengawas", "SSE.Views.WBProtection.hintAllowRanges": "Izinkan edit rentang", "SSE.Views.WBProtection.hintProtectSheet": "Proteksi sheet", "SSE.Views.WBProtection.hintProtectWB": "<PERSON><PERSON><PERSON><PERSON> workbook", "SSE.Views.WBProtection.txtAllowRanges": "Izinkan edit rentang", "SSE.Views.WBProtection.txtHiddenFormula": "Formula tersembunyi", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON> <PERSON>rk<PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Bentuk Dikunci", "SSE.Views.WBProtection.txtLockedText": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtProtectSheet": "Proteksi sheet", "SSE.Views.WBProtection.txtProtectWB": "<PERSON><PERSON><PERSON><PERSON> workbook", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Masukkan password untuk membuka proteksi sheet", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Buka Proteksi Sheet", "SSE.Views.WBProtection.txtWBUnlockDescription": "<PERSON><PERSON><PERSON><PERSON> password untuk membuka proteksi workbook", "SSE.Views.WBProtection.txtWBUnlockTitle": "Buka Proteksi Workbook"}