﻿<!DOCTYPE html>
<html>
	<head>
		<title>ARBEITSTAG-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ARBEITSTAG-Funktion</h1>
			<p>Die Funktion <b>ARBEITSTAG</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie die fortlaufende Zahl des Datums vor oder nach einer bestimmten Anzahl von Arbeitstagen zurück. Nicht zu den Arbeitstagen gezählt werden Wochenenden sowie die Tage, die als Ferientage oder Feiertage (Freie_Tage) angegeben werden.</p>
			<p>Die Formelsyntax der Funktion <b>ARBEITSTAG</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ARBEITSTAG(Ausgangsdatum;Tage;[Freie_Tage])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Ausgangsdatum</em></b> ist das erste Datum des Zeitraums, der mit der Funktion <a href="Date.htm" onclick="onhyperlinkclick(this)">Datum</a> oder einer anderen Datums- und Uhrzeitfunktion eingegeben wurde.</p>
			<p style="text-indent: 50px;"><b><em>Tage</em></b> ist die Anzahl der nicht auf ein Wochenende oder auf einen Feiertag fallenden Tage vor oder nach dem . Ein negativer Wert für <b><em>Tage</em></b> bedeutet ein vergangenes Datum das vor dem angegebenen  liegt. Ein positiver Wert für <b><em>Tage</em></b> bedeutet ein zukünftiges Datum das nach dem angegebenen kommt.</p>
            <p style="text-indent: 50px;"><b><em>Freie_Tage</em></b> ist eine optionale Liste einer oder mehrerer Datumsangaben, die alle Arten von arbeitsfreien Tagen repräsentieren kann, die aus dem Arbeitskalender ausgeschlossen werden sollen. Sie können dieses Argument mit der Funktion <a href="Date.htm" onclick="onhyperlinkclick(this)">Datum</a> oder einer anderen Datums- und Uhrzeitfunktion eingeben oder einen Zellbereich mit Daten angeben.</p>
			<p>Anwendung der Funktion <b>ARBEITSTAG</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ARBEITSTAG</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ARBEITSTAG-Funktion" src="../images/workday.png" /></p>
		</div>
	</body>
</html>