{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON><PERSON> din besked her", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textArea": "<PERSON><PERSON>r<PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Stablet område", "Common.define.chartData.textAreaStackedPer": "100% stablet område", "Common.define.chartData.textBar": "<PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Grupperet kolonne", "Common.define.chartData.textBarNormal3d": "3-D grupperet kolonne", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON> kolonne", "Common.define.chartData.textBarStacked": "Stablet kolonne", "Common.define.chartData.textBarStacked3d": "3-D stablet kolonne", "Common.define.chartData.textBarStackedPer": "100% stablet kolonne", "Common.define.chartData.textBarStackedPer3d": "3-D 100% stablet kolonne", "Common.define.chartData.textCharts": "Diagrammer", "Common.define.chartData.textColumn": "Kolonne", "Common.define.chartData.textColumnSpark": "Kolonne", "Common.define.chartData.textCombo": "Kombination", "Common.define.chartData.textComboAreaBar": "Stablet område - grupperet kolonne", "Common.define.chartData.textComboBarLine": "Grupperet kolonne - linje", "Common.define.chartData.textComboBarLineSecondary": "Grupperet kolonne - linje på", "Common.define.chartData.textComboCustom": "Brugerdefineret kombination", "Common.define.chartData.textDoughnut": "Donut", "Common.define.chartData.textHBarNormal": "Grupperet søjle", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> grupperet søjle", "Common.define.chartData.textHBarStacked": "Stablet søjle", "Common.define.chartData.textHBarStacked3d": "3-D stablet søjle", "Common.define.chartData.textHBarStackedPer": "100% stablet søjle", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% stablet søjle", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-<PERSON> linje", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> med <PERSON>ø<PERSON>", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Stablet linje", "Common.define.chartData.textLineStackedMarker": "Stablet linje med markører", "Common.define.chartData.textLineStackedPer": "100% stablet linje", "Common.define.chartData.textLineStackedPerMarker": "100% stablet linje med markører", "Common.define.chartData.textPie": "Cirkeldiagram", "Common.define.chartData.textPie3d": "3-D lagkage", "Common.define.chartData.textPoint": "XY (Spredning)", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter med lige linjer", "Common.define.chartData.textScatterLineMarker": "Scatter med lige linjer og markører", "Common.define.chartData.textScatterSmooth": "Scatter med jævne linjer", "Common.define.chartData.textScatterSmoothMarker": "Scatter med jævne linjer og markører", "Common.define.chartData.textSparks": "Minidiagram", "Common.define.chartData.textStock": "Aktie", "Common.define.chartData.textSurface": "Overflade", "Common.define.chartData.textWinLossSpark": "Vind/tab", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "\nIntet format angivet", "Common.define.conditionalData.text1Above": "1. afvigelse ovenfor", "Common.define.conditionalData.text1Below": "1. af<PERSON><PERSON><PERSON> for værdier under", "Common.define.conditionalData.text2Above": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> for væ<PERSON><PERSON> over", "Common.define.conditionalData.text2Below": "2. a<PERSON><PERSON><PERSON><PERSON> for værdier under", "Common.define.conditionalData.text3Above": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> for v<PERSON><PERSON><PERSON> over", "Common.define.conditionalData.text3Below": "3. a<PERSON><PERSON><PERSON><PERSON> for værdier under", "Common.define.conditionalData.textAbove": "Over", "Common.define.conditionalData.textAverage": "Gennemsnit", "Common.define.conditionalData.textBegins": "Begynd med", "Common.define.conditionalData.textBelow": "Under", "Common.define.conditionalData.textBetween": "i mellem", "Common.define.conditionalData.textBlank": "Blank", "Common.define.conditionalData.textBlanks": "Indeholder tomme emner", "Common.define.conditionalData.textBottom": "Bund", "Common.define.conditionalData.textContains": "Indeholder", "Common.define.conditionalData.textDataBar": "Datalinje", "Common.define.conditionalData.textDate": "Da<PERSON>", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Slutter med", "Common.define.conditionalData.textEqAbove": "Lige med eller over", "Common.define.conditionalData.textEqBelow": "Lige til eller under", "Common.define.conditionalData.textEqual": "<PERSON><PERSON><PERSON><PERSON> til", "Common.define.conditionalData.textError": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textErrors": "Indeholder fejl", "Common.define.conditionalData.textFormula": "Formular", "Common.define.conditionalData.textGreater": "Større end", "Common.define.conditionalData.textGreaterEq": "<PERSON><PERSON><PERSON> end eller lig med", "Common.define.conditionalData.textIconSets": "<PERSON><PERSON> sæt", "Common.define.conditionalData.textLast7days": "I de sidste 7 dage", "Common.define.conditionalData.textLastMonth": "Sidste måned", "Common.define.conditionalData.textLastWeek": "Sidste uge", "Common.define.conditionalData.textLess": "Mindre end", "Common.define.conditionalData.textLessEq": "Mindre end eller lig med", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON><PERSON> m<PERSON>", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON><PERSON> uge", "Common.define.conditionalData.textNotBetween": "ikke mellem", "Common.define.conditionalData.textNotBlanks": "Indeholder ikke tomme felter", "Common.define.conditionalData.textNotContains": "Indeholder ikke", "Common.define.conditionalData.textNotEqual": "<PERSON>k<PERSON> lig med", "Common.define.conditionalData.textNotErrors": "Indeholder ikke tomme felter", "Common.define.conditionalData.textText": "Tekst", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON>", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> uge", "Common.define.conditionalData.textToday": "I dag", "Common.define.conditionalData.textTomorrow": "I morgen", "Common.define.conditionalData.textTop": "Top", "Common.define.conditionalData.textUnique": "Enestående", "Common.define.conditionalData.textValue": "<PERSON><PERSON><PERSON> er", "Common.define.conditionalData.textYesterday": "<PERSON> går", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.warnFileLocked": "Dokumentet er i brug af en anden applikation. Du kan fortsætte med at redigere og gemme en kopi.", "Common.Translation.warnFileLockedBtnEdit": "Opret en kopi", "Common.Translation.warnFileLockedBtnView": "<PERSON><PERSON> for visning", "Common.UI.ButtonColored.textAutoColor": "Automatisk", "Common.UI.ButtonColored.textNewColor": "Brugerdefineret farve", "Common.UI.ComboBorderSize.txtNoBorders": "Ingen rammer", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Ingen rammer", "Common.UI.ComboDataView.emptyComboText": "Ingen stilarter", "Common.UI.ExtendedColorDialog.addButtonText": "Tilføj", "Common.UI.ExtendedColorDialog.textCurrent": "Nuværende", "Common.UI.ExtendedColorDialog.textHexErr": "Den indtastede værdi er ikke korrekt.<br>Venligst indtast en værdi mellem 000000 og FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Ny", "Common.UI.ExtendedColorDialog.textRGBErr": "Den indtastede værdi er ikke korrekt.<br>Venligst indtast en numerisk værdi mellem 0 og 255.", "Common.UI.HSBColorPicker.textNoColor": "Ingen farve", "Common.UI.SearchDialog.textHighlight": "Fremhæv resultater", "Common.UI.SearchDialog.textMatchCase": "Afhængigt af store og små bogstaver", "Common.UI.SearchDialog.textReplaceDef": "Skriv din erstatningstekst", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> din tekst her", "Common.UI.SearchDialog.textTitle": "Find og erstat", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "<PERSON>n hele ord", "Common.UI.SearchDialog.txtBtnHideReplace": "Skjul erstat", "Common.UI.SearchDialog.txtBtnReplace": "Erstat", "Common.UI.SearchDialog.txtBtnReplaceAll": "Erstat alle", "Common.UI.SynchronizeTip.textDontShow": "Vis ikke denne meddelelse igen", "Common.UI.SynchronizeTip.textSynchronize": "Dokumentet er blevet ændret af en anden bruger.<br><PERSON><PERSON><PERSON><PERSON><PERSON> tryk for at gemme ændringerne og genindlæs opdateringerne.", "Common.UI.ThemeColorPalette.textStandartColors": "Standard farve", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON> far<PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klassisk lys", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Lys", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Luk", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bekræftelse", "Common.UI.Window.textDontShow": "Vis ikke denne meddelelse igen", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Oplysninger", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON>", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "<PERSON><PERSON><PERSON>:", "Common.Views.About.txtLicensee": "Licenstager", "Common.Views.About.txtLicensor": "Licensgiver", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Drevet af", "Common.Views.About.txtTel": "tlf.:", "Common.Views.About.txtVersion": "Version", "Common.Views.AutoCorrectDialog.textAdd": "Tilføj", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Anvend mens du arbejder", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorrektur", "Common.Views.AutoCorrectDialog.textAutoFormat": "Løbende autoformatering", "Common.Views.AutoCorrectDialog.textBy": "By", "Common.Views.AutoCorrectDialog.textDelete": "Slet", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet- og netværksstier med hyperlinks", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematisk autokorrektur", "Common.Views.AutoCorrectDialog.textNewRowCol": "Inkludér nye rækker og kolonner i tabel", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON> funk<PERSON>er", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Følgende udtryk er anerkendte matematiske udtryk. De bliver ikke automatisk kursiveret.", "Common.Views.AutoCorrectDialog.textReplace": "Erstat", "Common.Views.AutoCorrectDialog.textReplaceText": "Udskift mens du skriver", "Common.Views.AutoCorrectDialog.textReplaceType": "Erstat tekst mens du skriver", "Common.Views.AutoCorrectDialog.textReset": "Nulstil", "Common.Views.AutoCorrectDialog.textResetAll": "Nulstil til standard", "Common.Views.AutoCorrectDialog.textRestore": "Gendan", "Common.Views.AutoCorrectDialog.textTitle": "Autokorrektur", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON>dte funktioner må kun indeholde bogstaverne A til Z, store og små bogstaver.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON>, du har til<PERSON>ø<PERSON>, f<PERSON><PERSON>, og de fjernede gendanne<PERSON>. Ønsker du at fortsætte?", "Common.Views.AutoCorrectDialog.warnReplace": "Autokorrekturindtastningen for %1 findes allerede. Ønsker du at erstatte den?", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON>hver autokorrektur, du tilføjede, fjer<PERSON>, og de ændrede gendannes til deres oprindelige værdier. Ønsker du at fortsætte?", "Common.Views.AutoCorrectDialog.warnRestore": "Autokorrekturindtastningen for %1 nulstilles til sin oprindelige værdi. Ønsker du at fortsætte?", "Common.Views.Chat.textSend": "Send", "Common.Views.Comments.mniAuthorAsc": "Forfatter A til Z", "Common.Views.Comments.mniAuthorDesc": "Forfatter Z til A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Nyeste", "Common.Views.Comments.mniPositionAsc": "<PERSON>a toppen", "Common.Views.Comments.mniPositionDesc": "Fra bunden", "Common.Views.Comments.textAdd": "Tilføj", "Common.Views.Comments.textAddComment": "Tilføj kommentar", "Common.Views.Comments.textAddCommentToDoc": "Tilføj kommentar til dokument", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Luk", "Common.Views.Comments.textClosePanel": "Luk kommentarer", "Common.Views.Comments.textComments": "kommentarer", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> din kommentar her", "Common.Views.Comments.textHintAddComment": "Tilføj kommentar", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON> igen", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Sorter kommentarer", "Common.Views.CopyWarningDialog.textDontShow": "Vis ikke denne meddelelse igen", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON>, klip og sæt ind handlinger ved brug af redigeringsværktøjets værktøjsbarknapper og kontekst menu handlinger vil kun blive foretaget i redigeringsfanen. <br><br> for at kopier og sætte ind til eller fra programmer uden for redigeringsværktøjet, skal du bruge følgende tastaturtaster: ", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, Klip og Indsæt handlinger", "Common.Views.CopyWarningDialog.textToCopy": "For kopiering", "Common.Views.CopyWarningDialog.textToCut": "For klipning", "Common.Views.CopyWarningDialog.textToPaste": "for sæt ind", "Common.Views.DocumentAccessDialog.textLoading": "Indlæser...", "Common.Views.DocumentAccessDialog.textTitle": "Delingsindstillinger", "Common.Views.EditNameDialog.textLabel": "Mærkat:", "Common.Views.EditNameDialog.textLabelError": "Mærkat kan ikke være blank.", "Common.Views.Header.labelCoUsersDescr": "Brugere som redigerer dokumentet:", "Common.Views.Header.textAddFavorite": "<PERSON><PERSON><PERSON> som favorit", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "Common.Views.Header.textBack": "Gå til dokumentplacering", "Common.Views.Header.textCompactView": "Skjul værktøjslinie", "Common.Views.Header.textHideLines": "Skjul lineal", "Common.Views.Header.textHideStatusBar": "Skjul statuslinje", "Common.Views.Header.textRemoveFavorite": "<PERSON><PERSON><PERSON> fra <PERSON>", "Common.Views.Header.textSaveBegin": "Gemmer...", "Common.Views.Header.textSaveChanged": "Modificeret", "Common.Views.Header.textSaveEnd": "Alle ændringer er blevet gemt", "Common.Views.Header.textSaveExpander": "Alle ændringer er blevet gemt", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": " <PERSON><PERSON><PERSON><PERSON> adgangsrettigheder for dokument", "Common.Views.Header.tipDownload": "Hent fil", "Common.Views.Header.tipGoEdit": "Rediger nuværende fil", "Common.Views.Header.tipPrint": "Udskriv fil", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Gem", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Lås af i seperat vindue", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON> in<PERSON>", "Common.Views.Header.tipViewUsers": "Vis brugere og håndter dokumentrettighederne ", "Common.Views.Header.txtAccessRights": "Skift adgangsrettigheder", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Luk historik", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON> æ<PERSON>", "Common.Views.History.textRestore": "Gendan", "Common.Views.History.textShow": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "<PERSON><PERSON> de<PERSON> æ<PERSON>", "Common.Views.History.textVer": "Ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Indsæt et billede URL: ", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Feltet skal være en URL i \"http://www.example.com\" formatet", "Common.Views.ListSettingsDialog.textBulleted": "Punkttegnet", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "Skift kugle", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "Nyt punkt", "Common.Views.ListSettingsDialog.txtNone": "Ingen", "Common.Views.ListSettingsDialog.txtOfText": "% af tekst", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Start ved", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.OpenDialog.closeButtonText": "Luk fil", "Common.Views.OpenDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.textSelectData": "Vælg data", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Kolon", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Afgrænser", "Common.Views.OpenDialog.txtDestData": "<PERSON><PERSON><PERSON><PERSON>, hvor dataene skal placeres", "Common.Views.OpenDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "Kodeordet er forkert", "Common.Views.OpenDialog.txtOpenFile": "Angiv en adgangskode for at åbne filen", "Common.Views.OpenDialog.txtOther": "<PERSON>", "Common.Views.OpenDialog.txtPassword": "Kodeord", "Common.Views.OpenDialog.txtPreview": "Forhåndvisning", "Common.Views.OpenDialog.txtProtected": "Når du indtastet kodeorderet og åbner filen, nulstilles det aktuelle kodeord til filen. ", "Common.Views.OpenDialog.txtSemicolon": "Semikolon", "Common.Views.OpenDialog.txtSpace": "Mellemrum", "Common.Views.OpenDialog.txtTab": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtTitle": "Vælg %1 indstillinger", "Common.Views.OpenDialog.txtTitleProtected": "Beskyttet fil", "Common.Views.PasswordDialog.txtDescription": "Indstil et kodeord for at beskytte dette dokument", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bekræftelsesadgangskode er ikke identisk", "Common.Views.PasswordDialog.txtPassword": "Kodeord", "Common.Views.PasswordDialog.txtRepeat": "Gentag kodeord", "Common.Views.PasswordDialog.txtTitle": "Indstil kodeord", "Common.Views.PasswordDialog.txtWarning": "Advarsel! Hvis du mister eller glem<PERSON> ad<PERSON>, kan den ikke genoprettes. Opbevar den et sikkert sted.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "Tilføjelsesprogrammer", "Common.Views.Plugins.strPlugins": "Tilføjelsesprogrammer", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Protection.hintAddPwd": "Krypter med adgangskode", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> eller slet kodeord", "Common.Views.Protection.hintSignature": "Tilføj digital underskrift eller underskiftslinje", "Common.Views.Protection.txtAddPwd": "Tilføj kodeord", "Common.Views.Protection.txtChangePwd": "Skrift kodeord", "Common.Views.Protection.txtDeletePwd": "Slet kodeord", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Tilføj digital underskift", "Common.Views.Protection.txtSignature": "Underskrift", "Common.Views.Protection.txtSignatureLine": "Tilføj underskriftslinje", "Common.Views.RenameDialog.textName": "Filnavn", "Common.Views.RenameDialog.txtInvalidName": "Filnavnet må ikke indeholde nogle af følgende tegn:", "Common.Views.ReviewChanges.hintNext": "Til næste ændring", "Common.Views.ReviewChanges.hintPrev": "Til forrige ændring", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Realtids co-redigering. Alle ændringer bliver gemt automatisk", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "Brug \"gem\" knappen for at synkronisere ændringer du og andre laver. ", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accept<PERSON><PERSON> nuvæ<PERSON> æ<PERSON>", "Common.Views.ReviewChanges.tipCoAuthMode": "Aktiver samredigeringstilstanden", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON><PERSON> kommentarer", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON>jern nuværende kommentarer", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON> kommentarer", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON>s aktuelle kommentarer", "Common.Views.ReviewChanges.tipHistory": "Vis versionshistorik", "Common.Views.ReviewChanges.tipRejectCurrent": "A<PERSON>vis nuværende ændring", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON><PERSON> den tilstand, du vil have, at ændringerne skal vises", "Common.Views.ReviewChanges.tipSetDocLang": "Vælg dokument sprog", "Common.Views.ReviewChanges.tipSetSpelling": "Stavekontrol", "Common.Views.ReviewChanges.tipSharing": " <PERSON><PERSON><PERSON><PERSON> adgangsrettigheder for dokument", "Common.Views.ReviewChanges.txtAccept": "Accepter", "Common.Views.ReviewChanges.txtAcceptAll": "Accept<PERSON><PERSON> alle ænd<PERSON>er", "Common.Views.ReviewChanges.txtAcceptChanges": "Accepter æ<PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accept<PERSON><PERSON> nuvæ<PERSON> æ<PERSON>", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Luk", "Common.Views.ReviewChanges.txtCoAuthMode": "Fællesredigeringstilstand", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON><PERSON> alle kommentarer", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON>jern nuværende kommentarer", "Common.Views.ReviewChanges.txtCommentRemMy": "Fjern mine kommentarer", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Fjern mine nuværende kommentarer", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON> alle kommentarer", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON>s aktuelle kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMy": "Løs mine kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Løs mine aktuelle kommentarer", "Common.Views.ReviewChanges.txtDocLang": "Sp<PERSON>", "Common.Views.ReviewChanges.txtFinal": "Alle ændringer accepteret (Forhåndsvisning)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "Versions historik", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> <PERSON> (redigering)", "Common.Views.ReviewChanges.txtMarkupCap": "Opmærkning", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> <PERSON> a<PERSON> (forhåndsvisning)", "Common.Views.ReviewChanges.txtOriginalCap": "Orginal", "Common.Views.ReviewChanges.txtPrev": "Til forrige æ<PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "A<PERSON>vis alle ændringer", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "A<PERSON>vis nuværende ændring", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Stavekontrol", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Visningstilstand", "Common.Views.ReviewPopover.textAdd": "Tilføj", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Luk", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+mention vil give adgang til dokumentet og sende en e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+mention vil notificere brugeren via e-mail", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON> igen", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtDeleteTip": "Slet", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Map<PERSON> til at gemme", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "Vælg datakilde", "Common.Views.SignDialog.textBold": "Fed", "Common.Views.SignDialog.textCertificate": "Cerfitikant", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Indsæt underskrivers navn", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Underskrivernavn må ikke være tomt.", "Common.Views.SignDialog.textPurpose": "Form<PERSON>l med at underskrive dette dokument", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "Underskriften ser ud som", "Common.Views.SignDialog.textTitle": "Underskriv dokument", "Common.Views.SignDialog.textUseImage": "<PERSON>r tryk '<PERSON><PERSON><PERSON><PERSON> billede' for at bruge et billede som underskrift", "Common.Views.SignDialog.textValid": "Gyldig fra %1 til %2", "Common.Views.SignDialog.tipFontName": "Skrifttypenavn", "Common.Views.SignDialog.tipFontSize": "Skriftstørrelse", "Common.Views.SignSettingsDialog.textAllowComment": "Tillad underskriver at tilføje en kommentar i underskrift dialogen", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Navn", "Common.Views.SignSettingsDialog.textInfoTitle": "Underskrivers titel", "Common.Views.SignSettingsDialog.textInstructions": "Instruktioner for underskriver", "Common.Views.SignSettingsDialog.textShowDate": "Vis underskrivningsdato på underskriftslinien", "Common.Views.SignSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX-værdi", "Common.Views.SymbolTableDialog.textCopyright": "Ophavsret Symbol", "Common.Views.SymbolTableDialog.textDCQuote": "Afsluttende citationstegn", "Common.Views.SymbolTableDialog.textDOQuote": "Åbningscitationstegn", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON> ellipse", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Em mellemrum", "Common.Views.SymbolTableDialog.textEnDash": "En bindestreg", "Common.Views.SymbolTableDialog.textEnSpace": "En mellemrum", "Common.Views.SymbolTableDialog.textFont": "Skrifttype", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON> bind<PERSON>g", "Common.Views.SymbolTableDialog.textNBSpace": "Ingen-brud mellemrum", "Common.Views.SymbolTableDialog.textPilcrow": "Afsnitstegn", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em mellemrum", "Common.Views.SymbolTableDialog.textRange": "Rækkevidde", "Common.Views.SymbolTableDialog.textRecent": "Senest anvendte symboler", "Common.Views.SymbolTableDialog.textRegistered": "Regis<PERSON>ret tegn", "Common.Views.SymbolTableDialog.textSCQuote": "Enkelt lukket citat", "Common.Views.SymbolTableDialog.textSection": "Sektion tegn", "Common.Views.SymbolTableDialog.textShortcut": "Genvejstast", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Enkelt åben kvote", "Common.Views.SymbolTableDialog.textSpecial": "Specielle tegn", "Common.Views.SymbolTableDialog.textSymbols": "Symboler", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Varemærke tegn", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON><PERSON><PERSON> mig ikke igen", "Common.Views.UserNameDialog.textLabel": "Mærkat:", "Common.Views.UserNameDialog.textLabelError": "Mærkat kan ikke være blank.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "Du skal angive URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Tekst til kolonner", "SSE.Controllers.DataTab.txtDataValidation": "Datavalidering", "SSE.Controllers.DataTab.txtExpand": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Dataene ved siden af markeringen fjernes ikke. Vil du udvide markeringen til at omfatte de tilstødende data eller kun fortsætte med de aktuelt valgte celler?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Udvalget indeholder nogle celler uden datavalideringsindstillinger.<br>Ønsker du at udevide datavalidering til disse celler?", "SSE.Controllers.DataTab.txtImportWizard": "Guide til tekstimport", "SSE.Controllers.DataTab.txtRemDuplicates": "Fjern duplikater", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Udvalget indeholder mere end en type validering.<br>Slet nuværende indstillinger og fortsæt?", "SSE.Controllers.DataTab.txtRemSelected": "<PERSON><PERSON><PERSON> i valgte", "SSE.Controllers.DataTab.txtUrlTitle": "Indsæt en data-URL", "SSE.Controllers.DocumentHolder.alignmentText": "Tilpasning", "SSE.Controllers.DocumentHolder.centerText": "Centrum", "SSE.Controllers.DocumentHolder.deleteColumnText": "Slet kolonne", "SSE.Controllers.DocumentHolder.deleteRowText": "Slet række", "SSE.Controllers.DocumentHolder.deleteText": "Slet", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Link referencen eksiterer ikke. Ret linket eller slet det.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Venstre kolonne", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> over", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON><PERSON> under", "SSE.Controllers.DocumentHolder.insertText": "indsæt", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Autokorrektur optioner", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Kolonnebredde {0} symboler ({1} pixel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON><PERSON> højde {0} punkter ({1} pixels)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Klik på linket for at åbne eller klik og hold museknap nede, for at vælge celle.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Indsæt venstre", "SSE.Controllers.DocumentHolder.textInsertTop": "Indsæt top", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Indsæt speciel", "SSE.Controllers.DocumentHolder.textStopExpand": "Stop automatisk voksende tabeller", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Elementet bliver redigeret af en anden bruger.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Over gennemsnit", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON> ramme ", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Tilføj fraktionsbar", "SSE.Controllers.DocumentHolder.txtAddHor": "Tilføj horisontal linie", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON><PERSON><PERSON><PERSON> venstre nederste linie", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON> venstre ramme", "SSE.Controllers.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON><PERSON>j venstre øverste linie", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON><PERSON><PERSON> højre ramme", "SSE.Controllers.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ramme", "SSE.Controllers.DocumentHolder.txtAddVer": "Til<PERSON><PERSON>j lodret linie", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Tilpas til karakterer ", "SSE.Controllers.DocumentHolder.txtAll": "(alle)", "SSE.Controllers.DocumentHolder.txtAnd": "og", "SSE.Controllers.DocumentHolder.txtBegins": "Begynd med", "SSE.Controllers.DocumentHolder.txtBelowAve": "Under middel", "SSE.Controllers.DocumentHolder.txtBlanks": "(Blanks)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtBottom": "Bund", "SSE.Controllers.DocumentHolder.txtColumn": "Kolonne", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> til<PERSON>ning", "SSE.Controllers.DocumentHolder.txtContains": "Indeholder", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Formindsk argumentstørrelse", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Slet argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Slet manuelt linieskift", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Slet omklamrende tegn", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Slet omsluttende tegn og separatøre", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Slet ligning", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Slet tegn", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "slet radikal", "SSE.Controllers.DocumentHolder.txtEnds": "Slut med", "SSE.Controllers.DocumentHolder.txtEquals": "Lig med", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Ens med cellefarve", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Ens med skriftfarven", "SSE.Controllers.DocumentHolder.txtExpand": "<PERSON><PERSON><PERSON> og sorter", "SSE.Controllers.DocumentHolder.txtExpandSort": "Dataen ved siden af det valgte vil ikke blive sorteret. Vil du udvide det valgte område til også, at inkludere den omkringliggende data, eller fortsætte med kun at sortere de valgte celler?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Bund", "SSE.Controllers.DocumentHolder.txtFilterTop": "Top", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Skift til lineær fraktion ", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Skift til skæv fraktion", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Skift til stablet fraktion", "SSE.Controllers.DocumentHolder.txtGreater": "Større end", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON><PERSON> end eller lig med", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON> over tekst", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Karakter under tekst", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>te ramme", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Skjul nederste grænse", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Skjul lukkende klamme", "SSE.Controllers.DocumentHolder.txtHideDegree": "Skjul vinkel", "SSE.Controllers.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> van<PERSON>t linie", "SSE.Controllers.DocumentHolder.txtHideLB": "Skjul venstre nederste linie", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON> venstre ramme", "SSE.Controllers.DocumentHolder.txtHideLT": "Skjul venstre øverste linie", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Skjul den åbnende klamme ", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Skjul pladsholder", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON> højre ramme", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON> ø<PERSON> ramme", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Skjul øverste grænse", "SSE.Controllers.DocumentHolder.txtHideVer": "Sk<PERSON>l lodret linie", "SSE.Controllers.DocumentHolder.txtImportWizard": "Guide til tekstimport", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Forstør argument st<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Indsæt argument efter", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Indsæt argument før", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Indsæt manuelt skift", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Indsæt ligning efter", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Indsæt ligning før", "SSE.Controllers.DocumentHolder.txtItems": "Element", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Behold kun tekst", "SSE.Controllers.DocumentHolder.txtLess": "Mindre end", "SSE.Controllers.DocumentHolder.txtLessEquals": "Mindre end eller lig med", "SSE.Controllers.DocumentHolder.txtLimitChange": "Skift afgrænsningspladseringer", "SSE.Controllers.DocumentHolder.txtLimitOver": "Beg<PERSON><PERSON><PERSON><PERSON> over tekst", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Begrænsning under tekst", "SSE.Controllers.DocumentHolder.txtLockSort": "Data findes ved siden af ​​dit valg, men du har ikke tilstrækkelige tilladelser til at ændre disse celler.<br>Vil du fortsætte med det aktuelle valg?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Tilpas klammer til argument højde", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON> just<PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "Der er ingen valgmuligheder for at fylde cellerne.<br><PERSON><PERSON> tekstværdier fra kolonnen kan vælges som erstatning.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Begynder ikke med", "SSE.Controllers.DocumentHolder.txtNotContains": "Indeholder ikke", "SSE.Controllers.DocumentHolder.txtNotEnds": "Slutter ikke med", "SSE.Controllers.DocumentHolder.txtNotEquals": "<PERSON>r ikke lig med", "SSE.Controllers.DocumentHolder.txtOr": "eller", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON> over tekst", "SSE.Controllers.DocumentHolder.txtPaste": "Indsæt", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formel uden rammer", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formel + kolonne bredde", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Destinations formatttering", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Indsæt kun formattering", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formel + talformat", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Indsæt kun formel", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formel + al formattering", "SSE.Controllers.DocumentHolder.txtPasteLink": "Indsæt link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Forbundet billeder", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Fusioner betinget formattering", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Kildeformattering", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Værdi + al formattering", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Værdi + tal format", "SSE.Controllers.DocumentHolder.txtPasteValues": "Indsæt kun værdi", "SSE.Controllers.DocumentHolder.txtPercent": "Procent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Fortryd automatiskforstørrelse af tabel", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Fjern frak<PERSON>bar", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON><PERSON> begræ<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Fjern accent tegn", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Fjern bar", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Vil du fjerne denne signatur?<br>Det kan ikke fortrydes.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Slet scripts", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Slet subscript", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Slet superscript", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON><PERSON> hø<PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scripts efter tekst", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts før tekst", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON>is nederste grænse", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON> lukke klamme", "SSE.Controllers.DocumentHolder.txtShowDegree": "Vis grader", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON> <PERSON> klamme", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Vis placeholder", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON> ø<PERSON> grænse", "SSE.Controllers.DocumentHolder.txtSorting": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtSortSelected": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>e", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Stræk klamme", "SSE.Controllers.DocumentHolder.txtTop": "Top", "SSE.Controllers.DocumentHolder.txtUnderbar": "Linje under tekst", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "<PERSON><PERSON><PERSON> tabel autoud<PERSON><PERSON>e", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Brug guiden til import af tekst", "SSE.Controllers.DocumentHolder.txtWarnUrl": "<PERSON><PERSON> du klikker på dette link, kan det være skadeligt for din enhed og dine data.<br><PERSON>r du sikker på, at du vil fortsætte?", "SSE.Controllers.DocumentHolder.txtWidth": "Bredde", "SSE.Controllers.FormulaDialog.sCategoryAll": "Alle", "SSE.Controllers.FormulaDialog.sCategoryCube": "Terning", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Database", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "<PERSON>to og tid", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Ingeniørarbejde", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finansiel", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Oplysninger", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 senest brugte", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logisk", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Opslag og reference", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematik og trigonometri", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistisk", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Tekst og data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Unavngivet regneark", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON> kolo<PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Indlæser versionshistorik...", "SSE.Controllers.LeftMenu.textLookin": "Se ind", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON>en du har søgt, kunne ikke findes. Venligst ændre dine søgerkriterier.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Erstatningen er blevet oprettet. {0} gentagelser blev sprunget over.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Søgningen er blevet gennemført. Forekomster erstattet: {0}", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSheet": "Ark", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWithin": "Indeholdende", "SSE.Controllers.LeftMenu.textWorkbook": "Arbejdsbog", "SSE.Controllers.LeftMenu.txtUntitled": "Unavngivet", "SSE.Controllers.LeftMenu.warnDownloadAs": "<PERSON><PERSON> du fortsætter med at gemme i dette format, vil alle funktioner på nær teksten blive tabt.<br> <PERSON>r du sikker på at du vil fortsætte?", "SSE.Controllers.Main.confirmMoveCellRange": "Destinations celleintervallet kan indeholde data. Fortsæt operationen?", "SSE.Controllers.Main.confirmPutMergeRange": "Kilde dataene indeholder fusionerede celler.<br>De er blevet skilt ad før de blev indsat i tabellen.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formler i overskriftsrækken vil blive fjernet og konverteret til statisk tekst.<br>Vil du fortsætte?", "SSE.Controllers.Main.convertationTimeoutText": "Konverteringstidsfrist er overskredet", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> \"OK\" for at retunere til dokument listen", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "Download fejlet.", "SSE.Controllers.Main.downloadTextText": "He<PERSON> regneark...", "SSE.Controllers.Main.downloadTitleText": "<PERSON><PERSON> re<PERSON>", "SSE.Controllers.Main.errNoDuplicates": "Ingen duplikate værdier fundet.", "SSE.Controllers.Main.errorAccessDeny": "<PERSON> at foretage en handling, som du ikke har rettighederne til.<br>venligst kontakt din Document Server administrator.", "SSE.Controllers.Main.errorArgsRange": "En fejl i den indtastede formel. <br> <PERSON><PERSON>omr<PERSON><PERSON> anven<PERSON>.", "SSE.Controllers.Main.errorAutoFilterChange": "Handlingen er ikke till<PERSON>t, da den forsøger at rykke celler i en tabel i dit ark.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Handlingen kunne ikke udføres for de valgte celler, da du ikke kan flytte dele af et diagram.<br>Vælg et andet datainterval så hele tabellen bliver rykket og prøv igen.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Handlingen kunne ikke gennemføres for de valgte celler.<br>Vælg et ensartet datainterval forskelligt fra det eksisterende og prøv igen.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "<PERSON>en kan ikke <PERSON>, da området indeholder filtrerede celler.<br>Fjern filtreringen og prøv igen.", "SSE.Controllers.Main.errorBadImageUrl": "Billede URL er forkert", "SSE.Controllers.Main.errorCannotUngroup": "Kan ikke af-gruppere. For at starte et omrids, vælg detalje-rækker eller kolonner og gruppér dem.", "SSE.Controllers.Main.errorChangeArray": "Du kan ikke ændre en del af en matrix.", "SSE.Controllers.Main.errorChangeFilteredRange": "Dette vil ændre et filtreret interval i dit regneark.<br><PERSON><PERSON><PERSON> AutoFilters for at afslutte.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "<PERSON><PERSON> eller diagrammet, du forsøger at ændre, er på et beskyttet ark.<br>For at foretage en ændring skal du fjerne beskyttelsen af ​​arket. Du kan blive bedt om at indtaste en adgangskode.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Server forbindelse tabt. Dokumentet kan ikke redigeres lige nu.", "SSE.Controllers.Main.errorConnectToServer": "Dokumentet kunne ikke gemmes. Check venligst din netværksforbindelse eller kontakt din administrator.<br><PERSON><PERSON><PERSON> du klikker på 'OK' knappen, vil du blive bedt om at downloade dokumentet.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Kommandoen kan ikke bruges med flere valg.<br><PERSON><PERSON><PERSON><PERSON> et enkelt interval og prøv igen.", "SSE.Controllers.Main.errorCountArg": "En fejl i den indtastede formel. <br> <PERSON><PERSON> antal argumenter anvendes.", "SSE.Controllers.Main.errorCountArgExceed": "En fejl i den indtastede formel. <br> Antal argumenter overskredet.", "SSE.Controllers.Main.errorCreateDefName": "De eksisterende navngivne intervaller kan ikke redigeres, og de nye kan ikke oprettes<br>i øjeblik<PERSON>, da nogle af dem er i gang med at blive redigeret.", "SSE.Controllers.Main.errorDatabaseConnection": "Ekstern fejl.<br>Database forbindelses fejl. Kontakt venligst support hvis fejlen bliver ved med at være der. ", "SSE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON><PERSON><PERSON> ændringer er blevet modtaget, men de kan ikke dekrypteres. ", "SSE.Controllers.Main.errorDataRange": "<PERSON>ert datainterval", "SSE.Controllers.Main.errorDataValidate": "Den indtastede værdi er ikke gyldig. <br> En bruger har begrænsede værdier, der kan indtastes i denne celle.", "SSE.Controllers.Main.errorDefaultMessage": "Fejlkode: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "<PERSON> for<PERSON> at slette en kolonne, der indeholder en låst celle. Låste celler kan ikke slette<PERSON>, mens regnearket er beskyttet.<br>For at slette en låst celle skal du fjerne beskyttelsen af ​​arket. Du kan blive bedt om at indtaste en adgangskode.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "<PERSON> for<PERSON> at slette en række, der indeholder en låst celle. Låste celler kan ikke slette<PERSON>, mens regnearket er beskyttet.<br>For at slette en låst celle skal du fjerne beskyttelsen af ​​arket. Du kan blive bedt om at indtaste en adgangskode.", "SSE.Controllers.Main.errorEditingDownloadas": "Der opstod en fejl under arbejdet med dokumentet. <br> Brug \"download som\" valgmuligheden for at gemme en sikkerhedsversion til din computers harddisk.", "SSE.Controllers.Main.errorEditingSaveas": "Der opstod en fejl under arbejdet med dokumentet. <br> Brug \"gem som...\" valg<PERSON>ligheden for at gemme en sikkerhedsversion til din computers harddisk.", "SSE.Controllers.Main.errorEditView": "Den eksisterende arkvisning kan ikke redigeres og nye kan ikke oprettes p.t., da enkelte redigeres.", "SSE.Controllers.Main.errorEmailClient": "Ingen e-mail klient fundet.", "SSE.Controllers.Main.errorFilePassProtect": "Dokumentet er beskyttet af et kodeord og kunne ikke åbnes.", "SSE.Controllers.Main.errorFileRequest": "Ekstern fejl.<br>Fil anmodningsfejl. Kontakt venligst support hvis fejlen fortsætter.", "SSE.Controllers.Main.errorFileSizeExceed": "<PERSON><PERSON> stø<PERSON>se overgår begrænsningen, som er sat for din server.<br>Kontakt venligst til dokumentserver administrator for detaljer.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON> fejl.<br><PERSON><PERSON>. Kontakt venligst support hvis fejlen fortsætter. ", "SSE.Controllers.Main.errorFillRange": "<PERSON>nne ikke fylde de valgte celler.<br>Alle de fusionerede celler skal være samme størrelse. ", "SSE.Controllers.Main.errorForceSave": "Der skete en fejl under gemning af filen. Brug venligst 'Download som' for at gemme filen på din computers harddisk eller prøv igen senere.", "SSE.Controllers.Main.errorFormulaName": "En fejl i den indtastede formel. <br> <PERSON><PERSON> formelnavn bruges.", "SSE.Controllers.Main.errorFormulaParsing": "Intern fejl under parsing af formlen", "SSE.Controllers.Main.errorFrmlMaxLength": "Længden på din formel overstiger grænsen på 8192 karakterer.<br>Venligst redigér og prøv igen.", "SSE.Controllers.Main.errorFrmlMaxReference": "Du kan ikke gemme denne formel da den har for mange værdier,<br>cellehenvisninger og/eller navne.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Tekstværdier i formler er begrænset til 255 tegn. <br> Brug CONCATENATE-funktionen eller sammenhængende operator (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funktionen referer til et ark der ikke eksiterer.<br><PERSON><PERSON><PERSON> venligst dine data og prøv igen.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Handlingen kunne ikke gennemføres for den valgte rækkevidde.<br>Vælg en rækkevidde så den første tabelrække er på samme række<br>og den resulterende tabel overlapper den nuværende. ", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Handlingen kunne ikke gennemføres for den valgte celle rækkevidde.<br>Vælg en rækkevidde som ikke inkluderer andre tabeller. ", "SSE.Controllers.Main.errorInvalidRef": "Skriv et korrekt navn for at markeringen eller en gyldig reference at gå til.", "SSE.Controllers.Main.errorKeyEncrypt": "Ukendte nøgle descriptor", "SSE.Controllers.Main.errorKeyExpire": "Nøgle beskrivelse udløbet", "SSE.Controllers.Main.errorLabledColumnsPivot": "For at oprette en pivottabel skal du bruge data, der er organiseret som en liste med mærkede kolonner.", "SSE.Controllers.Main.errorLoadingFont": "Skrifttyper er ikke indlæst.<br>Kontakt din dokument server administrator.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Referencen for placeringen eller dataområdet er ikke gyldig.", "SSE.Controllers.Main.errorLockedAll": "<PERSON>ling<PERSON> kunne ikke <PERSON>, da arket er blevet låst af en anden bruger.", "SSE.Controllers.Main.errorLockedCellPivot": "Du kan ikke ændre data i en pivot tabel", "SSE.Controllers.Main.errorLockedWorksheetRename": "Navnet på arket kan ikke skiftes, da det bliver skiftet af en anden bruger i øjeblikket.", "SSE.Controllers.Main.errorMaxPoints": "Det maksimale antal point i serien pr. diagram er 4096", "SSE.Controllers.Main.errorMoveRange": "Kan ikke ændre en del af en fusioneret celle", "SSE.Controllers.Main.errorMoveSlicerError": "Tabel-slicers kan ikke kopieres fra en workbook til en anden. <br><PERSON><PERSON><PERSON><PERSON> at vælge hele tabellen og slicers.", "SSE.Controllers.Main.errorMultiCellFormula": "Multi-felt matrix formler tillades ikke i tabeller", "SSE.Controllers.Main.errorNoDataToParse": "Ingen data blev valgt til at analysere.", "SSE.Controllers.Main.errorOpenWarning": "Længden af en af dine formler i filen overstiger<br>det tilladte antal tegn og er blevet fjernet.", "SSE.Controllers.Main.errorOperandExpected": "Den indtastede funktionssyntaks er ikke korrekt. Tjek venligst om der mangler en parentes - '(' eller ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "<PERSON> adgangskode, du har angivet, er ikke korrekt.<br><PERSON><PERSON><PERSON><PERSON><PERSON>, at CAPS LOCK-tasten er slået fra, og sørg for at bruge den korrekte brug af store bogstaver.", "SSE.Controllers.Main.errorPasteMaxRange": "Det kopierede område matcher ikke med det valgte.<br>V<PERSON><PERSON>g venligst en område med den samme størrelse, eller vælg den første celle i en række for at indsætte de kopierede celler.", "SSE.Controllers.Main.errorPasteMultiSelect": "<PERSON><PERSON> handling kan ikke udføres på et udvalg af flere områder.<br><PERSON><PERSON><PERSON><PERSON> et enkelt område, og prøv igen.", "SSE.Controllers.Main.errorPasteSlicerError": "Tabel-slicers kan ikke kopieres fra en workbook til en anden.", "SSE.Controllers.Main.errorPivotGroup": "Kan ikke gruppere dette", "SSE.Controllers.Main.errorPivotOverlap": "En pivottabel-rapport kan ikke overlappe en tabel.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Pivottabelrapporten blev gemt uden de underliggende data.<br>B<PERSON> knappen 'Opdater' til at opdatere rapporten.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Det er desværre ikke muligt at printe mere end 1500 sider på en gang i den nuværende program version.<br><PERSON><PERSON> begrænsning fjernes i de kommen opdateringer.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON> ikke gemme", "SSE.Controllers.Main.errorServerVersion": "Programmet er blevet opdateret. Siden vil blive genindlæst for at anvende ændringerne. ", "SSE.Controllers.Main.errorSessionAbsolute": "Sessionen for dokumentredigering er udløbet. Genindlæs venligst siden. ", "SSE.Controllers.Main.errorSessionIdle": "Dokumentet er ikke blevet redigeret i et stykke tid. Genindlæs venligst siden.", "SSE.Controllers.Main.errorSessionToken": "Forbindelsen til serveren er blevet afbrudt. Venligst genindlæs siden.", "SSE.Controllers.Main.errorSetPassword": "Kodeord kunne ikke gemmes.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Placeringsreference er ikke gyldig, fordi cellerne ikke alle er i den samme kolonne eller række.<br><PERSON><PERSON><PERSON><PERSON> celler, der alle er i en enkelt kolonne eller række.", "SSE.Controllers.Main.errorStockChart": "<PERSON><PERSON> r<PERSON>ø<PERSON>. For at bygge et aktiediagram placer dataen på arket i følgende orden:<br><PERSON><PERSON><PERSON><PERSON><PERSON>, maks pris, min. pris, lukke pris. ", "SSE.Controllers.Main.errorToken": "Dokumentets sikkerhedstoken er ikke oprettet korrekt.<br> Kontakt venligst din administrator for Document Server.", "SSE.Controllers.Main.errorTokenExpire": "Dokumentets sikkerhedstoken er udløbet.<br>Kontakt venligst din administrator for Document Server. ", "SSE.Controllers.Main.errorUnexpectedGuid": "Ekstern fejl.<br>Uventet GUID. Kontakt venligst support hvis fejlen fortsætter.", "SSE.Controllers.Main.errorUpdateVersion": "Filversionen er blevet ændret. Siden vil blive genindlæst.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internetforbindelsen er blevet genoprettet, og filversionen er blevet ændret.<br><PERSON><PERSON><PERSON> du kan fortsætte arbejdet, skal du hente filen eller kopiere indholdet for at sikre, at intet vil blive tabt - og derefter genindlæse denne side.", "SSE.Controllers.Main.errorUserDrop": "Der kan ikke opnås adgang til filen lige nu. ", "SSE.Controllers.Main.errorUsersExceed": "Det maksimale antal af brugere tilladt i din aftale er nået. ", "SSE.Controllers.Main.errorViewerDisconnect": "Forbindesen er tabt. Du kan stadig se dokumentet, <br> men du vil ikke være i stand til at hente eller udskrive det, før forbin<PERSON>sen er genetableret. ", "SSE.Controllers.Main.errorWrongBracketsCount": "En fejl i den indtastede formel. <br> <PERSON><PERSON> antal parenteser bruges.", "SSE.Controllers.Main.errorWrongOperator": "En fejl i den indtastede formel. <PERSON><PERSON> bruger er brugt. <br> <PERSON><PERSON> fej<PERSON>.", "SSE.Controllers.Main.errorWrongPassword": "<PERSON>gangs<PERSON>, du har angivet, er ikke korrekt.", "SSE.Controllers.Main.errRemDuplicates": "Duplikate værdier fundet og slettet: {0}, unikke værdier tilbage: {1}", "SSE.Controllers.Main.leavePageText": "Du har ikke gemte ændringer i dette ark. Klik 'blive på denen side' og 'Gem' for at gemme dem. Klik 'Forlad denne side' for at slette de ikke gemte ændringer.", "SSE.Controllers.Main.leavePageTextOnClose": "Alle ikke-gemte ændringer i dette regneark vil gå tabt.<br> <PERSON><PERSON> på \"<PERSON><PERSON>er\" og derefter på \"Gem\" for at gemme dem. Klik på \"OK\" for at kassere alle ikke-gemte ændringer.", "SSE.Controllers.Main.loadFontsTextText": "Indlæser data...", "SSE.Controllers.Main.loadFontsTitleText": "Indlæser data", "SSE.Controllers.Main.loadFontTextText": "Indlæser data...", "SSE.Controllers.Main.loadFontTitleText": "Indlæser data", "SSE.Controllers.Main.loadImagesTextText": "In<PERSON><PERSON><PERSON> billeder...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON><PERSON>er", "SSE.Controllers.Main.loadImageTextText": "Indlæser billede...", "SSE.Controllers.Main.loadImageTitleText": "Indlæser billede", "SSE.Controllers.Main.loadingDocumentTitleText": "Indlæser regneark", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "Der skete en fejl under åbningen af filen.", "SSE.Controllers.Main.openTextText": "<PERSON><PERSON> regneark...", "SSE.Controllers.Main.openTitleText": "<PERSON><PERSON>", "SSE.Controllers.Main.pastInMergeAreaError": "Kan ikke ændre en del af en fusioneret celle", "SSE.Controllers.Main.printTextText": "Udskriver regneark...", "SSE.Controllers.Main.printTitleText": "Udskriver regneark", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> siden", "SSE.Controllers.Main.requestEditFailedMessageText": "En anden redigerer dokumentet lige nu. Prøv igen senere.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> næ<PERSON>", "SSE.Controllers.Main.saveErrorText": "Der skete en fejl da filen blev forsøgt gemt.", "SSE.Controllers.Main.saveErrorTextDesktop": "Filen kan ikke gemmes eller oprettes.<br><PERSON><PERSON><PERSON> årsager kan være:<br>1. <PERSON><PERSON> er read-only. <br>2. <PERSON><PERSON> and en anden bruger.<br>3. <PERSON>sken er fuld eller beskadiget.", "SSE.Controllers.Main.saveTextText": "Gemmer regneark...", "SSE.Controllers.Main.saveTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.scriptLoadError": "<PERSON><PERSON><PERSON><PERSON> er for langsom, nogle af komponenterne kunne ikke indlæses. Venligst genindlæs siden.", "SSE.Controllers.Main.textAnonymous": "Anonym", "SSE.Controllers.Main.textApplyAll": "<PERSON><PERSON><PERSON> på alle ligninger", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "SSE.Controllers.Main.textChangesSaved": "Alle ændringer er gemt", "SSE.Controllers.Main.textClose": "Luk", "SSE.Controllers.Main.textCloseTip": "<PERSON><PERSON> for at lukke tippet", "SSE.Controllers.Main.textConfirm": "Bekræftelse", "SSE.Controllers.Main.textContactUs": "Kontakt salg", "SSE.Controllers.Main.textConvertEquation": "\nDenne ligning blev oprettet med en gammel version af ligningseditoren, som ikke længere understøttes. For at redigere den skal du konvertere ligningen til Office Math ML-formatet.<br>Konverter nu?", "SSE.Controllers.Main.textCustomLoader": "Be<PERSON>ærk, at du i henhold til licensbetingelserne ikke har ret til at skifte loaderen.<br>Kontakt venligt vores salgsafdeling for at få en kvote.", "SSE.Controllers.Main.textDisconnect": "Forbindelsen er afbrudt", "SSE.Controllers.Main.textFillOtherRows": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formelfyldte {0} rækker har data. Det kan tage et par minutter at udfylde andre tomme rækker.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formel udfyldte første {0} ræ<PERSON>. Det kan tage et par minutter at udfylde andre tomme rækker.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formel udfyldt kun de første {0} rækker har data efter hukommelseslagringsårsag. Der er andre {1} rækker med data i dette ark. Du kan udfylde dem manuelt.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formel udfyldte kun de første {0} rækker efter hukommelseslagringsårsag. <PERSON> rækker i dette ark har ikke data.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "Filen indeholder makroer.<br><PERSON><PERSON><PERSON> <PERSON> at køre makroer?", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON> mere", "SSE.Controllers.Main.textLoadingDocument": "Indlæser regneark", "SSE.Controllers.Main.textLongName": "Indtast et navn på mindre end 128 bogstaver.", "SSE.Controllers.Main.textNeedSynchronize": "<PERSON> har op<PERSON>ringer", "SSE.Controllers.Main.textNo": "<PERSON><PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "Licensbegrænsning nået", "SSE.Controllers.Main.textPaidFeature": "Betalt funktion", "SSE.Controllers.Main.textPleaseWait": "Handlingen tager måske længere en forventet. Vent venligst...", "SSE.Controllers.Main.textRemember": "Husk mit valg til alle filer", "SSE.Controllers.Main.textRenameError": "Brugernavn må ikke være tomt.", "SSE.Controllers.Main.textRenameLabel": "Indtast et navn til brug for samarbejde", "SSE.Controllers.Main.textShape": "Form", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON> tilstand", "SSE.Controllers.Main.textText": "Tekst", "SSE.Controllers.Main.textTryUndoRedo": "<PERSON><PERSON>d funktionen er blevet slået fra i den hurtige co-redigerngstilstand.<b>Tryk på 'Striks tilstand' knappen for at skifte til den strikse co-redigeringstilstand for at redigere filen uden at andre brugere kan foretage ændringer før efter du har gemt dem. Du kan skifte i mellem co-redigeringstilstanden ved at bruge de avancerede indstillinger.  ", "SSE.Controllers.Main.textTryUndoRedoWarn": "\nFortryd/Gentag-funktionerne er deaktiveret i tilstanden Hurtig co-redigering.", "SSE.Controllers.Main.textYes": "<PERSON>a", "SSE.Controllers.Main.titleLicenseExp": "Licens er udløbet", "SSE.Controllers.Main.titleServerVersion": "Redigeringsværktøj opdateret", "SSE.Controllers.Main.txtAccent": "Accent", "SSE.Controllers.Main.txtAll": "(alle)", "SSE.Controllers.Main.txtArt": "<PERSON> tekst her", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> former", "SSE.Controllers.Main.txtBlank": "(blank)", "SSE.Controllers.Main.txtButtons": "<PERSON>nap<PERSON>", "SSE.Controllers.Main.txtByField": "%1 af %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "Diagrammer", "SSE.Controllers.Main.txtClearFilter": "Ryd filter", "SSE.Controllers.Main.txtColLbls": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtColumn": "Kolonne", "SSE.Controllers.Main.txtConfidential": "Fortrolig", "SSE.Controllers.Main.txtDate": "Da<PERSON>", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Diagram titel", "SSE.Controllers.Main.txtEditingMode": "<PERSON><PERSON><PERSON><PERSON> redigeringstilstand...", "SSE.Controllers.Main.txtErrorLoadHistory": "Fejl ved indlæsningen af historik", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON> figure", "SSE.Controllers.Main.txtFile": "Fil", "SSE.Controllers.Main.txtGrandTotal": "Samlet beløb", "SSE.Controllers.Main.txtGroup": "Gruppe", "SSE.Controllers.Main.txtHours": "Timer", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematik", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Multi-vælg", "SSE.Controllers.Main.txtOr": "%1 eller %2", "SSE.Controllers.Main.txtPage": "Side", "SSE.Controllers.Main.txtPageOf": "Side %1 af %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "Forberedt af", "SSE.Controllers.Main.txtPrintArea": "Print_Område", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "Serie", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Linje talebobbel 1 (Omrids og Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Linje talebobbel 2 (Omrids og Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Linje talebobbel 3 (O<PERSON>rids og Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout1": "Linje talebobbel 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "<PERSON>je <PERSON> 2 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout3": "Linje talebo<PERSON>l 3 (Accent Bar)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tilbage eller forudg<PERSON>ende knap", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Begyndende knap", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Blank knap", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Dokument knap", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Slut knap", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON>em eller næste-knap", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Hjælp-knap", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON>-knap", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Informations-knap", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Film-knap", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Tilbage-knap", "SSE.Controllers.Main.txtShape_actionButtonSound": "Lyd-knap", "SSE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> pil", "SSE.Controllers.Main.txtShape_bentConnector5": "Albue-forbindelse", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Albue-pil forbindelse", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Albue dobbelt-pil forbindelse", "SSE.Controllers.Main.txtShape_bentUpArrow": "Pil buet opad", "SSE.Controllers.Main.txtShape_bevel": "Facet", "SSE.Controllers.Main.txtShape_blockArc": "Blokeringsbue", "SSE.Controllers.Main.txtShape_borderCallout1": "Linje Talebobbel 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Linje <PERSON>l 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Linje <PERSON>bo<PERSON>l 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON> bø<PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Linje Talebobbel 1 (Ingen grænse)", "SSE.Controllers.Main.txtShape_callout2": "Linje Talebobbel 2 (Ingen Grænse)", "SSE.Controllers.Main.txtShape_callout3": "Linje Talebobbel 3 (<PERSON>gen grænse)", "SSE.Controllers.Main.txtShape_can": "<PERSON>n", "SSE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chord": "Akkord", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> pil", "SSE.Controllers.Main.txtShape_cloud": "Sky", "SSE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON><PERSON><PERSON> (Sky)", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "Terning", "SSE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "<PERSON><PERSON><PERSON> pil-forbin<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON> dobbelt-pil forbin<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON>e pil", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "<PERSON>uet pil til venstre", "SSE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON>t pil til højre", "SSE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON>t til opad", "SSE.Controllers.Main.txtShape_decagon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diagStripe": "Diagonal stribe", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Tolvkant", "SSE.Controllers.Main.txtShape_donut": "Donut", "SSE.Controllers.Main.txtShape_doubleWave": "Dobbeltbølge", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrowCallout": "Talebobbel (Pil ned)", "SSE.Controllers.Main.txtShape_ellipse": "Ellipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "<PERSON><PERSON><PERSON>e sløjfe", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "<PERSON><PERSON><PERSON> sløjfe opad", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowdiagram: Alternativ process", "SSE.Controllers.Main.txtShape_flowChartCollate": "Flowdiagram: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartConnector": "Flowdiagram: Forbind", "SSE.Controllers.Main.txtShape_flowChartDecision": "Flowdiagram: Valg", "SSE.Controllers.Main.txtShape_flowChartDelay": "Flowdiagram: Forsink", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Flowdiagram: Vis", "SSE.Controllers.Main.txtShape_flowChartDocument": "Flowdiagram: Dokument", "SSE.Controllers.Main.txtShape_flowChartExtract": "Flowdiagram: Udtræk", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Flowdiagram: Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowdiagram: <PERSON>n opbevaring", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowdiagram: Magnetisk Disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowdiagram: <PERSON><PERSON><PERSON> adgang opbevaring", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowdiagram: Opbevaring af sekventiel adgang", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Flowdiagram: Manuelt Input", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Flowdiagram: Manuel <PERSON>", "SSE.Controllers.Main.txtShape_flowChartMerge": "Flowdiagram: Sammenflet", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Flowdiagram: Multidokument", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowdiagram: Af-side forbindelse", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowdiagram: Opbevaret Data", "SSE.Controllers.Main.txtShape_flowChartOr": "Flowdiagram: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowdiagram: Forudbestemt Process", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Flowdiagram: Forberedelse", "SSE.Controllers.Main.txtShape_flowChartProcess": "Flowdiagram: Process", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowdiagram: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowdiagram: <PERSON><PERSON> b<PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Flowdiagram: Sorter", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowdiagram: <PERSON><PERSON><PERSON><PERSON><PERSON> knudepunkt", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Flowdiagram: Terminator", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON>v ramme", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Syvkant", "SSE.Controllers.Main.txtShape_hexagon": "Sekskant", "SSE.Controllers.Main.txtShape_homePlate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON>dret rul", "SSE.Controllers.Main.txtShape_irregularSeal1": "Eksplosion 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Eksplosion 2", "SSE.Controllers.Main.txtShape_leftArrow": "Venstre pil", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Talebobbel (Pil venstre)", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON> bø<PERSON>", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON> para<PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "<PERSON><PERSON><PERSON> højre pil", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Talebobbel (Pil højre/venstre)", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Venstre-højre-op pil", "SSE.Controllers.Main.txtShape_leftUpArrow": "Venstre-op pil", "SSE.Controllers.Main.txtShape_lightningBolt": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON>l", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Dobbeltpil", "SSE.Controllers.Main.txtShape_mathDivide": "Opdeling", "SSE.Controllers.Main.txtShape_mathEqual": "Lig med", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathNotEqual": "Ikke lig", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "\"Nej\" symbol", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Hakket højre-pil", "SSE.Controllers.Main.txtShape_octagon": "Ottekant", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "SSE.Controllers.Main.txtShape_pentagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_pie": "Cirkeldiagram", "SSE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline2": "Fri form", "SSE.Controllers.Main.txtShape_quadArrow": "Firedobbelt pil", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Talebobbel (Firedobbelt pil)", "SSE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon2": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON><PERSON> pil", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Talebobbel (Højre pil)", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "Rund et-hjørnet rektangel", "SSE.Controllers.Main.txtShape_round2DiagRect": "Rund diagonal hjø<PERSON> rektangel", "SSE.Controllers.Main.txtShape_round2SameRect": "Rund samme-sidet hjørnerektangel", "SSE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON><PERSON> med rundt hjørne", "SSE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON><PERSON> trekant", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "<PERSON><PERSON> enkelt hjørne rektangel", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Klip <PERSON> hjø<PERSON> rektangel", "SSE.Controllers.Main.txtShape_snip2SameRect": "Klip samme-side hjørnet rektangel", "SSE.Controllers.Main.txtShape_snipRoundRect": "K<PERSON> og rundt et-hjørnet rektangel", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "10-Point Stjerne", "SSE.Controllers.Main.txtShape_star12": "12-Points Stjerne", "SSE.Controllers.Main.txtShape_star16": "16-Points Stjerne", "SSE.Controllers.Main.txtShape_star24": "24-Points Stjerne", "SSE.Controllers.Main.txtShape_star32": "32-Points Stjerne", "SSE.Controllers.Main.txtShape_star4": "4-Points Stjerne", "SSE.Controllers.Main.txtShape_star5": "5-Points Stjerne", "SSE.Controllers.Main.txtShape_star6": "6-Points Stjerne", "SSE.Controllers.Main.txtShape_star7": "7-Points Stjerne", "SSE.Controllers.Main.txtShape_star8": "8-Points Stjerne", "SSE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON><PERSON> hø<PERSON>-pil", "SSE.Controllers.Main.txtShape_sun": "Sol", "SSE.Controllers.Main.txtShape_teardrop": "Dr<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_textRect": "Tekstboks", "SSE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Trekant", "SSE.Controllers.Main.txtShape_upArrow": "Op pil", "SSE.Controllers.Main.txtShape_upArrowCallout": "Talebobbel (Pil op)", "SSE.Controllers.Main.txtShape_upDownArrow": "Op-ned pil", "SSE.Controllers.Main.txtShape_uturnArrow": "U-vendings pil", "SSE.Controllers.Main.txtShape_verticalScroll": "Lodret rul", "SSE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Talebobbel ", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON><PERSON><PERSON> Rektangulær <PERSON> ", "SSE.Controllers.Main.txtStarsRibbons": "Stjerner og bånd", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.Main.txtStyle_Check_Cell": "T<PERSON>k celle", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Valuta", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Forklarende tekst", "SSE.Controllers.Main.txtStyle_Good": "God", "SSE.Controllers.Main.txtStyle_Heading_1": "Overskrift 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Overskrift 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Overskrift 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Overskrift 4", "SSE.Controllers.Main.txtStyle_Input": "Input", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Forbundet celler", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "Note", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "Procent", "SSE.Controllers.Main.txtStyle_Title": "Titel", "SSE.Controllers.Main.txtStyle_Total": "I alt", "SSE.Controllers.Main.txtStyle_Warning_Text": "Advarselstekst", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Tid", "SSE.Controllers.Main.txtUnlock": "lås op", "SSE.Controllers.Main.txtUnlockRange": "\n<PERSON><PERSON><PERSON> r<PERSON>vid<PERSON> op", "SSE.Controllers.Main.txtUnlockRangeDescription": "Indtast adgangskoden for at ændre dette område:", "SSE.Controllers.Main.txtUnlockRangeWarning": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, du forsøger at ændre, er beskyttet med adgangskode.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtXAxis": "X akse", "SSE.Controllers.Main.txtYAxis": "Y akse", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "Ukendt fejl.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Din browser understøttes ikke", "SSE.Controllers.Main.uploadDocExtMessage": "Ukendt dokumentformat.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Ingen dokumenter uploadet.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maksimal dokumentstørrelse overskredet.", "SSE.Controllers.Main.uploadImageExtMessage": "Ukendt billedeformat.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Ingen billeder uploadet", "SSE.Controllers.Main.uploadImageSizeMessage": "Bill<PERSON>t er for stort. Den maksimale størrelse er 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON><PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.waitText": "Vent venligst...", "SSE.Controllers.Main.warnBrowserIE9": "Programmet har dårlig kompatibilitet med Internet Explorer 9. Brug i stedet Internet Explorer 10 eller højere", "SSE.Controllers.Main.warnBrowserZoom": "Din browsers nuværende zoom indstilling er ikke understøttet. Venligst genddan til normal forstørrelse ved at trykke Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "Antallet af samtidige forbindelser til serveren overstiger det tilladte antal, og dokumentet åbnes i visningstilstand.<br>Kontakt venligst din administrator for mere information.", "SSE.Controllers.Main.warnLicenseExp": "Din licens er udløbet.<br>Opdater venligst din licens og genindlæs siden.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licens udløbet. <br><PERSON> har ikke adgang til at redigere. <br>Kontakt venligst din administrator.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Licens skal fornyes.<br><PERSON> har begrænset adgang til at redigere dokumenter.<br>Kontakt venligst din administrator for at få fuld adgang.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Det tilladte antal af samtidige brugere er oversteget, og dokumentet vil blive åbnet i visningstilstand.<br>Kontakt venligst din administrator for mere information. ", "SSE.Controllers.Main.warnNoLicense": "Du har n<PERSON><PERSON> gr<PERSON><PERSON><PERSON> for antal samtidige forbindelser til %1 værktøjer. Dokumentet åbnes for læsning.<br>Kontakt %1 salgsteamet for betingelser for opgradering.", "SSE.Controllers.Main.warnNoLicenseUsers": "<PERSON> har nå<PERSON> gr<PERSON><PERSON>n for brugere af %1 redigeringsværktøj. Kontakt %1 salgsafdeling for at høre om dine opgraderingsmuligheder.", "SSE.Controllers.Main.warnProcessRightsChange": "Du er blevet nægtet rettighederne til at redigere denne fil.", "SSE.Controllers.Print.strAllSheets": "Alle ark", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON>e kolonne", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Controllers.Print.textNoRepeat": "Gentag ikke", "SSE.Controllers.Print.textRepeat": "Gentag...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtCustom": "Brugerdefineret", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON><PERSON> er ukorrekt", "SSE.Controllers.Statusbar.errorLastSheet": "Arbejdsbog skal have mindst et synligt ark.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Kan ikke slette regnearket.", "SSE.Controllers.Statusbar.strSheet": "Ark", "SSE.Controllers.Statusbar.textSheetViewTip": "Du er i regnearksvisningstilstand. Filtrering og sortering vises kun for dig og brugere som stadig bruger denne visning.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Du er i regnearksvisningstilstand. Filtre er kun synlige for dig og brugere som stadig bruger denne visning.", "SSE.Controllers.Statusbar.warnDeleteSheet": "<PERSON>et kan indeholde data. Er du sikker på at du vil fortsætte?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Skrifttypen du gemmer er ikke tilgængeligt på din nuværende enhed.<br>Skrifttypen vil blive vist som en af dem dit system understøtter, den gemte skrifttype vil bruge brugt når den er tilgængelig.<br>Vil du fortsætte?", "SSE.Controllers.Toolbar.errorComboSeries": "For at lave et kombinationsdiagram, vælg mindst to serier med data.", "SSE.Controllers.Toolbar.errorMaxRows": "FEJL! Det maksimale antal af dataserier pr. diagram er 255", "SSE.Controllers.Toolbar.errorStockChart": "<PERSON><PERSON> r<PERSON>ø<PERSON>. For at bygge et aktiediagram placer dataen på arket i følgende orden:<br><PERSON><PERSON><PERSON><PERSON><PERSON>, maks pris, min. pris, lukke pris. ", "SSE.Controllers.Toolbar.textAccent": "Accenter", "SSE.Controllers.Toolbar.textBracket": "Beslag", "SSE.Controllers.Toolbar.textDirectional": "Retningsbestemt", "SSE.Controllers.Toolbar.textFontSizeErr": "Den indtastede værdi er ikke korrekt.<br>Indtast venligst en numerisk værdi mellem 1 og 409", "SSE.Controllers.Toolbar.textFraction": "Frak<PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "Indikatorer", "SSE.Controllers.Toolbar.textInsert": "Indsæt", "SSE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Store operatører ", "SSE.Controllers.Toolbar.textLimitAndLog": "Afgrænsninger og logaritmer", "SSE.Controllers.Toolbar.textLongOperation": "Lang operation", "SSE.Controllers.Toolbar.textMatrix": "<PERSON>rice<PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operatører ", "SSE.Controllers.Toolbar.textPivot": "Pivottabel", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Form", "SSE.Controllers.Toolbar.textSymbols": "Symboler", "SSE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Højre-Venstre pil over", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Venstregående pil over", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Højregående pil over", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "In<PERSON><PERSON>t formel (Med pladsholder)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Indrammet formel (Eksempel)", "SSE.Controllers.Toolbar.txtAccent_Check": "Tjek", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC med øvre bar", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y med overbar", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Triple Dot", "SSE.Controllers.Toolbar.txtAccent_DDot": "Dobbelt prik", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> øvre linie", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grav", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Grup<PERSON>r tegn under", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "<PERSON><PERSON><PERSON><PERSON> tegn over", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> harpoon over", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> harpoon over", "SSE.Controllers.Toolbar.txtAccent_Hat": "Hat", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Beslag", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parentes med separatorer", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parentes med separatorer", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Curve": "Beslag", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parentes med separatorer", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Sager (<PERSON> bet<PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Sager (<PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Stak objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Stak objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON><PERSON> på <PERSON>r", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial koefficient", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial koefficient", "SSE.Controllers.Toolbar.txtBracket_Line": "Beslag", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Parentes", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Parentes", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Round": "Parentes", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentes med separatorer", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Square": "Beslag", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Beslag", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Parentes", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Beslag", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Parentes", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Beslag", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON><PERSON> klamme", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON> celler", "SSE.Controllers.Toolbar.txtExpand": "<PERSON><PERSON><PERSON> og sorter", "SSE.Controllers.Toolbar.txtExpandSort": "Dataen ved siden af det valgte vil ikke blive sorteret. Vil du udvide det valgte område til også, at inkludere den omkringliggende data, eller fortsætte med kun at sortere de valgte celler?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Skrå fraktion", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Forskellen", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Forskellen", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Forskellen", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Differential", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Lineær fraktion", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi over 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Lille fraktion", "SSE.Controllers.Toolbar.txtFractionVertical": "Stablet fraktion", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Invers cosinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "hyperbolsk invers cosinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Invers cotangent funktion", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "hyperbolsk invers cotangent funktion", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "invers cosekant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "hyperbolsk invers cosekant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Invers sekant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "hyperbolsk invers sekant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Invers sinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "hyperbolsk invers sinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Invers tangent funktion", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "hyperbolsk invers tangent funktion", "SSE.Controllers.Toolbar.txtFunction_Cos": "cosinus funktion", "SSE.Controllers.Toolbar.txtFunction_Cosh": "hyperbolsk cosinus funktion ", "SSE.Controllers.Toolbar.txtFunction_Cot": "Cotangens funktion", "SSE.Controllers.Toolbar.txtFunction_Coth": "hyperbolsk cotangens", "SSE.Controllers.Toolbar.txtFunction_Csc": "Cosekant funktion", "SSE.Controllers.Toolbar.txtFunction_Csch": "hyperbolsk cosekant funktion", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangens formel", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "hyperbolsk sekant funktion", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sinus funktion", "SSE.Controllers.Toolbar.txtFunction_Sinh": "hyperbolsk sinus funktion", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangens funktion", "SSE.Controllers.Toolbar.txtFunction_Tanh": "hyperbolsk tangent funktion", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON><PERSON> celler", "SSE.Controllers.Toolbar.txtIntegral": "integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "integral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Dobbelt integral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dobbelt integral", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dobbelt integral", "SSE.Controllers.Toolbar.txtIntegralOriented": "Integreret kontur", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integreret kontur", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Overflade integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Overflade integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Overflade integral", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integreret kontur", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume integral", "SSE.Controllers.Toolbar.txtIntegralSubSup": "integral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Triple Integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple Integral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple Integral", "SSE.Controllers.Toolbar.txtInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Delprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Delprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Delprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Delprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Delprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "kryds", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "kryds", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "kryds", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "kryds", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "kryds", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Begrænset eksempel", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Ma<PERSON><PERSON><PERSON> eksempel", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Beg<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON>g logaritme", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritme", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritme", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Data findes ved siden af ​​dit valg, men du har ikke tilstrækkelige tilladelser til at ændre disse celler.<br>Vil du fortsætte med det aktuelle valg?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Tom matrix med klammer", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Tom matrix med klammer", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Tom matrix med klammer", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Tom matrix med klammer", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Tom Matrix", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Basislinje prikker", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midterlinie prikker", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonale prikker", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON><PERSON><PERSON> matrice", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON><PERSON><PERSON> matrice", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identitetsmatrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Identitetsmatrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identitetsmatrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Identitetsmatrix", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Højre-Venstre pil under", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Højre-Venstre pil over", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Venstregående pil under", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Venstregående pil over", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Højregående pil under", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Højregående pil over", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "kolon ligmed ", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta udbytter", "SSE.Controllers.Toolbar.txtOperator_Definition": "Lig med pr definition", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta er lig med", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Højre-Venstre pil under", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Højre-Venstre pil over", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Venstregående pil under", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Venstregående pil over", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Højregående pil under", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Højregående pil over", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Lig med lig med", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus lig med", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus lig med", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Målt af", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Kvadratrod med grader", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Kvadratrod", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radikalt med grader", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Kvadra<PERSON>den", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Subscript", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subscript-Superscript", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Venstre subscript-superscript", "SSE.Controllers.Toolbar.txtScriptSup": "Superscript", "SSE.Controllers.Toolbar.txtSorting": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSortSelected": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>e", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "Komplimenter", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Næsten identisk med", "SSE.Controllers.Toolbar.txtSymbol_ast": "Stjerne operatør", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Indsats", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Punkt operatør", "SSE.Controllers.Toolbar.txtSymbol_cap": "kryds", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Ku<PERSON>k<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON><PERSON> ellipse midterlinie", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Grader <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Cirka lig med", "SSE.Controllers.Toolbar.txtSymbol_cup": "Union", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON> hø<PERSON> diagonal ellipse", "SSE.Controllers.Toolbar.txtSymbol_degree": "Grader", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Divider tegn", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON> sæt", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Lig med", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identisk med", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "faktoriel", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grader F<PERSON><PERSON>heit", "SSE.Controllers.Toolbar.txtSymbol_forall": "For alle", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> end eller lig med", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON> større end", "SSE.Controllers.Toolbar.txtSymbol_greater": "Større end", "SSE.Controllers.Toolbar.txtSymbol_in": "Elementer af", "SSE.Controllers.Toolbar.txtSymbol_inc": "Stigning", "SSE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Venstre pil", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Venstre-Højre pil", "SSE.Controllers.Toolbar.txtSymbol_leq": "Mindre end eller lig med", "SSE.Controllers.Toolbar.txtSymbol_less": "Mindre end", "SSE.Controllers.Toolbar.txtSymbol_ll": "Meget mindre end", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON>k<PERSON> lig med", "SSE.Controllers.Toolbar.txtSymbol_ni": "Inde<PERSON> som medlem", "SSE.Controllers.Toolbar.txtSymbol_not": "Intet tegn", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Der findes ikke", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON> for<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_percent": "Procent", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proportionelt til ", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON> rod", "SSE.Controllers.Toolbar.txtSymbol_qed": "Afslutning af bevis", "SSE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON><PERSON> opad diagonal ellipse", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON><PERSON> pil", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Radikalt tegn", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "Multplikationstegn", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Op pil", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma Variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Lodrette ellipser", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Tabeludseende Mørk", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Tabeludseende Lys", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Tabeludseende Mellem", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON>lingen du er ved at udføre kan tage en del tid at udføre.<br><PERSON>r du sikker på at du vil fortsætte?", "SSE.Controllers.Toolbar.warnMergeLostData": "Kun data for øverste venstre celle forbliver i fusionerede celler.<br><PERSON>r du sikker på at du vil fortsætte?", "SSE.Controllers.Viewport.textFreezePanes": "Freeze Panes", "SSE.Controllers.Viewport.textFreezePanesShadow": "Vis låste panelers skygge", "SSE.Controllers.Viewport.textHideFBar": "Skjul formel bar", "SSE.Controllers.Viewport.textHideGridlines": "<PERSON><PERSON><PERSON><PERSON> gitte<PERSON>", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON> overskrifter", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Decimaltegn", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Tusindstalsseparator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Indstillinger til genkendelse af numeriske data", "SSE.Views.AdvancedSeparatorDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Brugerdefineret filter", "SSE.Views.AutoFilterDialog.textAddSelection": "Tilføj nuværende valg for at filtrere", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON> alle", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Vælg alle søgeresultater", "SSE.Views.AutoFilterDialog.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAboveAve": "Over gennemsnit", "SSE.Views.AutoFilterDialog.txtBegins": "Begynd med...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Under middel", "SSE.Views.AutoFilterDialog.txtBetween": "Mellem...", "SSE.Views.AutoFilterDialog.txtClear": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "Indeholder...", "SSE.Views.AutoFilterDialog.txtEmpty": "Skriv celle filter", "SSE.Views.AutoFilterDialog.txtEnds": "Slut med...", "SSE.Views.AutoFilterDialog.txtEquals": "Lig med...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrer efter vellefarve", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrer efter skriftfarve", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON> end...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "St<PERSON><PERSON> end eller lig med...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Navngiv filter", "SSE.Views.AutoFilterDialog.txtLess": "Mindre end...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Mindre end eller lig med...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Begynder ikke med...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Ikke mellem...", "SSE.Views.AutoFilterDialog.txtNotContains": "Indeholder ikke", "SSE.Views.AutoFilterDialog.txtNotEnds": "Slutter ikke med...", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON>r ikke lig med...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Nummer filter", "SSE.Views.AutoFilterDialog.txtReapply": "Genanvend", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON><PERSON><PERSON> efter cell<PERSON>arve", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON><PERSON><PERSON> efter sk<PERSON>", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "<PERSON><PERSON><PERSON><PERSON> hø<PERSON> til lavest", "SSE.Views.AutoFilterDialog.txtSortLow2High": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>t til høje<PERSON>", "SSE.Views.AutoFilterDialog.txtSortOption": "<PERSON>lere sort<PERSON><PERSON><PERSON><PERSON><PERSON>r", "SSE.Views.AutoFilterDialog.txtTextFilter": "Tekstfilter", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Værdi-filter", "SSE.Views.AutoFilterDialog.warnFilterError": "Du skal have mindst ét felt i området Værdier, for at benytte et værdi-filter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Du skal vælge mindst en værdi", "SSE.Views.CellEditor.textManager": "Navneh<PERSON>ndtering", "SSE.Views.CellEditor.tipFormula": "Indsæt funktion", "SSE.Views.CellRangeDialog.errorMaxRows": "FEJL! Det maksimale antal af dataserier pr. diagram er 255", "SSE.Views.CellRangeDialog.errorStockChart": "<PERSON><PERSON> r<PERSON>ø<PERSON>. For at bygge et aktiediagram placer dataen på arket i følgende orden:<br><PERSON><PERSON><PERSON><PERSON><PERSON>, maks pris, min. pris, lukke pris. ", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.CellRangeDialog.txtInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON>g datainterval", "SSE.Views.CellSettings.strShrink": "Formindsk for at passe", "SSE.Views.CellSettings.strWrap": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>t", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Baggrundsfarve", "SSE.Views.CellSettings.textBackground": "Baggrundsfarve", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textColor": "Farvefyld", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textCondFormat": "Betinget formatering", "SSE.Views.CellSettings.textControl": "Tekststyring", "SSE.Views.CellSettings.textDataBars": "Datalinjer", "SSE.Views.CellSettings.textDirection": "Retning", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Forgrundsfarve", "SSE.Views.CellSettings.textGradient": "Gradientpunkter", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Gradient udfyldning", "SSE.Views.CellSettings.textIndent": "Indryk", "SSE.Views.CellSettings.textItems": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "Administ<PERSON> regler", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "Intet fyld", "SSE.Views.CellSettings.textOrientation": "Tekstorientering", "SSE.Views.CellSettings.textPattern": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Position", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> rammer som du vil ændre til stilarten valgt ovenover", "SSE.Views.CellSettings.textSelection": "Fra det aktuelle udvalg", "SSE.Views.CellSettings.textThisPivot": "\n<PERSON>a denne pivot", "SSE.Views.CellSettings.textThisSheet": "<PERSON>a <PERSON><PERSON> a<PERSON>", "SSE.Views.CellSettings.textThisTable": "<PERSON>a denne tabel", "SSE.Views.CellSettings.tipAddGradientPoint": "Tilfø<PERSON>", "SSE.Views.CellSettings.tipAll": "<PERSON>æ<PERSON>g ydre ramme og alle indre linier", "SSE.Views.CellSettings.tipBottom": "<PERSON>æ<PERSON>g kun ydre nederste ramme", "SSE.Views.CellSettings.tipDiagD": "Vælg diagonal ne<PERSON><PERSON><PERSON><PERSON><PERSON> ramme", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON><PERSON> diagonal ø<PERSON>t ramme", "SSE.Views.CellSettings.tipInner": "<PERSON><PERSON><PERSON><PERSON> kun indre linier", "SSE.Views.CellSettings.tipInnerHor": "<PERSON><PERSON><PERSON>g kun vandret inderste linier", "SSE.Views.CellSettings.tipInnerVert": "<PERSON>æ<PERSON>g kun lodrette indre linier", "SSE.Views.CellSettings.tipLeft": "<PERSON>æ<PERSON>g kun ydre venstre ramme", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON><PERSON>g ingen rammer", "SSE.Views.CellSettings.tipOuter": "<PERSON><PERSON><PERSON>g kun ydre rammer", "SSE.Views.CellSettings.tipRemoveGradientPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipRight": "<PERSON><PERSON><PERSON>g kun højre ramme", "SSE.Views.CellSettings.tipTop": "Vælg kun ydre øverste ramme", "SSE.Views.ChartDataDialog.errorInFormula": "Der er en fejl i den indtastede formel.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Henvisningen er ugyldig. Henvisninger skal være til et åbent ark.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Det maksimale antal punkter i serier pr. ark er 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Det maksimale antal dataserier pr. diagram er 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Henvisningen er ugyldig. Hen<PERSON>ninger for titler, væ<PERSON><PERSON>, stø<PERSON><PERSON> eller datamærkater, skal være en enkelt celle, række eller kolonne.", "SSE.Views.ChartDataDialog.errorNoValues": "For at lave et diagram, skal serien indeholde mindst én værdi.", "SSE.Views.ChartDataDialog.errorStockChart": "<PERSON><PERSON> ræk<PERSON>følge. For at bygge et aktiediagram, placér da data på arket i følgende rækkefølge:<br><PERSON><PERSON><PERSON><PERSON><PERSON>, maks. pris, min. pris, luk<PERSON><PERSON><PERSON>. ", "SSE.Views.ChartDataDialog.textAdd": "Tilføj", "SSE.Views.ChartDataDialog.textCategory": "<PERSON><PERSON><PERSON> (kategori) akse", "SSE.Views.ChartDataDialog.textData": "Diagramdatainterval", "SSE.Views.ChartDataDialog.textDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textDown": "<PERSON>", "SSE.Views.ChartDataDialog.textEdit": "<PERSON>ig<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textSelectData": "Vælg data", "SSE.Views.ChartDataDialog.textSeries": "<PERSON><PERSON><PERSON> (serie)", "SSE.Views.ChartDataDialog.textSwitch": "Skift række/kolonne", "SSE.Views.ChartDataDialog.textTitle": "Diagramdata", "SSE.Views.ChartDataDialog.textUp": "Op", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Der er en fejl i den indtastede formel.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Henvisningen er ugyldig. Henvisninger skal være til et åbent ark.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Det maksimale antal punkter i serier pr. ark er 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Det maksimale antal dataserier pr. diagram er 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Henvisningen er ugyldig. Hen<PERSON>ninger for titler, væ<PERSON><PERSON>, stø<PERSON><PERSON> eller datamærkater, skal være en enkelt celle, række eller kolonne.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "For at lave et diagram, skal serien indeholde mindst én værdi.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "<PERSON><PERSON> ræk<PERSON>følge. For at bygge et aktiediagram, placér da data på arket i følgende rækkefølge:<br><PERSON><PERSON><PERSON><PERSON><PERSON>, maks. pris, min. pris, luk<PERSON><PERSON><PERSON>. ", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.textSelectData": "Vælg data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Aksemærkat for interval", "SSE.Views.ChartDataRangeDialog.txtChoose": "Vælg interval", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Serienavn", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Aksemærkater", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Redigér serie", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtYValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON> væ<PERSON>", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Skabelon", "SSE.Views.ChartSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "SSE.Views.ChartSettings.textBorderSizeErr": "Den indtastede værdi er ikke korrekt.<br>venligst indtast en numerisk værdi mellem 0 pt og 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Skift type", "SSE.Views.ChartSettings.textChartType": "Skift diagramtype", "SSE.Views.ChartSettings.textEditData": "Rediger data og lokation", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "Højeste punkt", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON>er", "SSE.Views.ChartSettings.textLastPoint": "Sidste punkt", "SSE.Views.ChartSettings.textLowPoint": "Laveste punkt", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNegativePoint": "Negativt punkt", "SSE.Views.ChartSettings.textRanges": "Data rækkevidde", "SSE.Views.ChartSettings.textSelectData": "Vælg data", "SSE.Views.ChartSettings.textShow": "Vis", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "Stilart", "SSE.Views.ChartSettings.textType": "Type", "SSE.Views.ChartSettings.textWidth": "Bredde", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "Fejl! Det maksimale antal af punkter i serie pr. diagram er 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "FEJL! Det maksimale antal af dataserier pr. diagram er 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "<PERSON><PERSON> r<PERSON>ø<PERSON>. For at bygge et aktiediagram placer dataen på arket i følgende orden:<br><PERSON><PERSON><PERSON><PERSON><PERSON>, maks pris, min. pris, lukke pris. ", "SSE.Views.ChartSettingsDlg.textAbsolute": "Flyt- og tilpas ikke med felter", "SSE.Views.ChartSettingsDlg.textAlt": "Alternativ tekst", "SSE.Views.ChartSettingsDlg.textAltDescription": "Beskrivelse", "SSE.Views.ChartSettingsDlg.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive læst til folk med syns- eller læringsudfordringer, for at hjælpe dem til at forstå den information der kan findes i et billede, autofigur, diagram eller tabel.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Titel", "SSE.Views.ChartSettingsDlg.textAuto": "Automatisk", "SSE.Views.ChartSettingsDlg.textAutoEach": "Auto til hver", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Axis Crosses", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "Akseposition", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Axis Settings", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Titel", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Mellem Tick Marks", "SSE.Views.ChartSettingsDlg.textBillions": "Milliarder", "SSE.Views.ChartSettingsDlg.textBottom": "Bund", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "Centrum", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Chart Elements & <br> Chart Legend", "SSE.Views.ChartSettingsDlg.textChartTitle": "Diagram titel", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Brugerdefineret", "SSE.Views.ChartSettingsDlg.textDataColumns": "<PERSON> kolonner", "SSE.Views.ChartSettingsDlg.textDataLabels": "Data etiketter", "SSE.Views.ChartSettingsDlg.textDataRows": "<PERSON> rækker", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Skjulte og tomme celler", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Forbind datapunkter med linje", "SSE.Views.ChartSettingsDlg.textFit": "T<PERSON><PERSON> til bredde", "SSE.Views.ChartSettingsDlg.textFixed": "Fast", "SSE.Views.ChartSettingsDlg.textFormat": "Mærkatformat", "SSE.Views.ChartSettingsDlg.textGaps": "Mellemrum", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Grupèr minidiagram", "SSE.Views.ChartSettingsDlg.textHide": "Skjul", "SSE.Views.ChartSettingsDlg.textHideAxis": "Skjul akse", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON> akse", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON><PERSON><PERSON><PERSON> van<PERSON> a<PERSON>e", "SSE.Views.ChartSettingsDlg.textHorizontal": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Hundrede", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "i", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Inderste bund", "SSE.Views.ChartSettingsDlg.textInnerTop": "Inderste top", "SSE.Views.ChartSettingsDlg.textInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Views.ChartSettingsDlg.textLabelDist": "Akselmærkeafstand", "SSE.Views.ChartSettingsDlg.textLabelInterval": "<PERSON><PERSON> imellem et<PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Etiket indstillinger", "SSE.Views.ChartSettingsDlg.textLabelPos": "Etiket indstillinger", "SSE.Views.ChartSettingsDlg.textLayout": "Layout", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON><PERSON><PERSON> overlay", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Bund", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "Beskrivelse", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Top", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Lokations rækkevidde", "SSE.Views.ChartSettingsDlg.textLow": "Lav", "SSE.Views.ChartSettingsDlg.textMajor": "St<PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Stor og lille", "SSE.Views.ChartSettingsDlg.textMajorType": "Stor type", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "<PERSON><PERSON> mellem <PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON> værdi", "SSE.Views.ChartSettingsDlg.textMillions": "Millioner", "SSE.Views.ChartSettingsDlg.textMinor": "Lille", "SSE.Views.ChartSettingsDlg.textMinorType": "Lille type", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimum værdi", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Næste til aksen", "SSE.Views.ChartSettingsDlg.textNone": "Ingen", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Intet overlay", "SSE.Views.ChartSettingsDlg.textOneCell": "Flyt men skalér ikke med felter", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "På checkbokse", "SSE.Views.ChartSettingsDlg.textOut": "Ud", "SSE.Views.ChartSettingsDlg.textOuterTop": "Ydre top", "SSE.Views.ChartSettingsDlg.textOverlay": "Overlay", "SSE.Views.ChartSettingsDlg.textReverse": "Værdier i omvendt rækkefølge", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Baglæns reækkefølge", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON><PERSON><PERSON> overlay", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "Ens for alle", "SSE.Views.ChartSettingsDlg.textSelectData": "Vælg data", "SSE.Views.ChartSettingsDlg.textSeparator": "Data etiket separatore ", "SSE.Views.ChartSettingsDlg.textSeriesName": "Serie navn", "SSE.Views.ChartSettingsDlg.textShow": "Vis", "SSE.Views.ChartSettingsDlg.textShowBorders": "Vis diagram rammer", "SSE.Views.ChartSettingsDlg.textShowData": "Vis data i skjulte rækker og kolonner", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Vis tomme celler som", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON> akse", "SSE.Views.ChartSettingsDlg.textShowValues": "Vis diagram værdier", "SSE.Views.ChartSettingsDlg.textSingle": "Enkelt minidiagram", "SSE.Views.ChartSettingsDlg.textSmooth": "Jævn", "SSE.Views.ChartSettingsDlg.textSnap": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Minidiagram intervaller", "SSE.Views.ChartSettingsDlg.textStraight": "Lige", "SSE.Views.ChartSettingsDlg.textStyle": "Stilart", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "<PERSON><PERSON><PERSON><PERSON> inds<PERSON>linger", "SSE.Views.ChartSettingsDlg.textTitle": "Diagram - a<PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Minidiagram - a<PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.ChartSettingsDlg.textTop": "Top", "SSE.Views.ChartSettingsDlg.textTrillions": "B<PERSON>oner", "SSE.Views.ChartSettingsDlg.textTwoCell": "Flyt og skalér med felter", "SSE.Views.ChartSettingsDlg.textType": "Type", "SSE.Views.ChartSettingsDlg.textTypeData": "Type og data", "SSE.Views.ChartSettingsDlg.textUnits": "<PERSON><PERSON> en<PERSON>er", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "Lodret akse", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "<PERSON><PERSON><PERSON><PERSON><PERSON> van<PERSON> a<PERSON>e", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X akse titel", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y akse titel", "SSE.Views.ChartSettingsDlg.textZero": "Nul", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.ChartTypeDialog.errorComboSeries": "For at lave et kombinationsdiagram, vælg mindst to serier med data.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Den valgte diagramtype kræver at den sekundære akse, bruges af det eksisterende diagram. Vælg en anden diagramtype.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textSeries": "Serie", "SSE.Views.ChartTypeDialog.textStyle": "Stilart", "SSE.Views.ChartTypeDialog.textTitle": "Diagramtype", "SSE.Views.ChartTypeDialog.textType": "Type", "SSE.Views.CreatePivotDialog.textDataRange": "Kildedataområde", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, hvor du vil placere tabellen", "SSE.Views.CreatePivotDialog.textExist": "Eksisterende regneark", "SSE.Views.CreatePivotDialog.textInvalidRange": "Ugyld<PERSON> felt-rækkevidde", "SSE.Views.CreatePivotDialog.textNew": "<PERSON><PERSON> re<PERSON>", "SSE.Views.CreatePivotDialog.textSelectData": "Vælg data", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON> pivottabel", "SSE.Views.CreatePivotDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.CreateSparklineDialog.textDataRange": "Kildedataområde", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, hvor mini diagrammer skal placeres ", "SSE.Views.CreateSparklineDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CreateSparklineDialog.textSelectData": "Vælg data", "SSE.Views.CreateSparklineDialog.textTitle": "Opret mini diagrammer", "SSE.Views.CreateSparklineDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.DataTab.capBtnGroup": "Gruppe", "SSE.Views.DataTab.capBtnTextCustomSort": "Brugerdefineret sortering", "SSE.Views.DataTab.capBtnTextDataValidation": "Datavalidering", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Fjern duplikater", "SSE.Views.DataTab.capBtnTextToCol": "Tekst til kolonner", "SSE.Views.DataTab.capBtnUngroup": "Fjern fra gruppe", "SSE.Views.DataTab.capDataFromText": "Hent data", "SSE.Views.DataTab.mniFromFile": "Fra lokal TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Fra TXT/CSV-webadresse", "SSE.Views.DataTab.textBelow": "Opsummer rækker under <PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textClear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> om<PERSON>", "SSE.Views.DataTab.textColumns": "<PERSON><PERSON>n kolonner fra gruppe", "SSE.Views.DataTab.textGroupColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textGroupRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textRightOf": "Opsummer kolonner til højre for detalje", "SSE.Views.DataTab.textRows": "<PERSON>jern gruppen af rækker", "SSE.Views.DataTab.tipCustomSort": "Brugerdefineret sortering", "SSE.Views.DataTab.tipDataFromText": "Hent data fra tekst/CSV-fil", "SSE.Views.DataTab.tipDataValidation": "Datavalidering", "SSE.Views.DataTab.tipGroup": "Grupper række af felter", "SSE.Views.DataTab.tipRemDuplicates": "Fjern duplikate rækker fra et ark", "SSE.Views.DataTab.tipToColumns": "Opdel celletekst i kolonner", "SSE.Views.DataTab.tipUngroup": "<PERSON>jern gruppen af celler", "SSE.Views.DataValidationDialog.errorFormula": "Værdien vurderes som fejl. Ønsker du at fortsætte?", "SSE.Views.DataValidationDialog.errorInvalid": "Den indtastede værdi for feltet \"{0}\" er ugyldig.", "SSE.Views.DataValidationDialog.errorInvalidDate": "<PERSON> indtastede dato for feltet \"{0}\" er ugyldig.", "SSE.Views.DataValidationDialog.errorInvalidList": "Kildelisten skal være en afgrænset liste eller en henvisning til en enkelt række eller kolonne.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Det indtastede tidspunkt for feltet \"{0}\" er ugyldigt.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "<PERSON>lt<PERSON> \"{1}\" skal være større end eller lig med feltet \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "<PERSON> skal indtaste en værdi i både felt \"{0}\" og felt \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Du skal indtaste en værdi i feltet \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Et navngivent interval du har specificeret, kan ikke findes. ", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negative værdier kan ikke bruges i betingelser \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Feltet \"{0}\" skal være en numerisk værdi, et numerisk udtryk eller henvise til en celle indeholdende en numerisk værdi.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.strInput": "Indtastningsbesked", "SSE.Views.DataValidationDialog.strSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAlert": "Alarm", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "<PERSON><PERSON><PERSON> disse ændringer i alle celler med tilsvarende indstillinger", "SSE.Views.DataValidationDialog.textCellSelected": "<PERSON><PERSON><PERSON> en celle er valgt, vi denne indtastnings-meddelelse", "SSE.Views.DataValidationDialog.textCompare": "Sammenlign med", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndTime": "Sluttidspunkt", "SSE.Views.DataValidationDialog.textError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textFormula": "Formel", "SSE.Views.DataValidationDialog.textIgnore": "Igno<PERSON><PERSON> blanke", "SSE.Views.DataValidationDialog.textInput": "Indtastningsbesked", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "Besked", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Vælg data", "SSE.Views.DataValidationDialog.textShowDropDown": "Vis drop-downliste i celle", "SSE.Views.DataValidationDialog.textShowError": "Vis fejlmelding efter indtastning af ugyldige data ", "SSE.Views.DataValidationDialog.textShowInput": "Vis indtastningsbesked når celle er valgt", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Startdato", "SSE.Views.DataValidationDialog.textStartTime": "Starttidspunkt", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "Stilart", "SSE.Views.DataValidationDialog.textTitle": "Titel", "SSE.Views.DataValidationDialog.textUserEnters": "Når en bruger indtaster ugyldige data, vis denne fej<PERSON>ked", "SSE.Views.DataValidationDialog.txtAny": "Enhver værdi", "SSE.Views.DataValidationDialog.txtBetween": "i mellem", "SSE.Views.DataValidationDialog.txtDate": "Da<PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON><PERSON><PERSON><PERSON> tid", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEqual": "er lig med", "SSE.Views.DataValidationDialog.txtGreaterThan": "større end", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "stø<PERSON> end eller lig med", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "mindre end", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "mindre end eller lig med", "SSE.Views.DataValidationDialog.txtList": "Liste", "SSE.Views.DataValidationDialog.txtNotBetween": "ikke mellem", "SSE.Views.DataValidationDialog.txtNotEqual": "er ikke", "SSE.Views.DataValidationDialog.txtOther": "And<PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "Startdato", "SSE.Views.DataValidationDialog.txtStartTime": "Starttidspunkt", "SSE.Views.DataValidationDialog.txtTextLength": "Tekstlængde", "SSE.Views.DataValidationDialog.txtTime": "Tidspunkt", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON> tal", "SSE.Views.DigitalFilterDialog.capAnd": "og", "SSE.Views.DigitalFilterDialog.capCondition1": "Lig med", "SSE.Views.DigitalFilterDialog.capCondition10": "Slutter ikke med", "SSE.Views.DigitalFilterDialog.capCondition11": "Indeholder", "SSE.Views.DigitalFilterDialog.capCondition12": "Indeholder ikke", "SSE.Views.DigitalFilterDialog.capCondition2": "<PERSON>r ikke lig med", "SSE.Views.DigitalFilterDialog.capCondition3": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition4": "<PERSON><PERSON> <PERSON>t<PERSON><PERSON> eller lig med", "SSE.Views.DigitalFilterDialog.capCondition5": "Er mindre end", "SSE.Views.DigitalFilterDialog.capCondition6": "Er mindre end eller lig med", "SSE.Views.DigitalFilterDialog.capCondition7": "Begynd med", "SSE.Views.DigitalFilterDialog.capCondition8": "Begynder ikke med", "SSE.Views.DigitalFilterDialog.capCondition9": "Slut med", "SSE.Views.DigitalFilterDialog.capOr": "Eller", "SSE.Views.DigitalFilterDialog.textNoFilter": "Intet filter", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON> ræ<PERSON> hvor", "SSE.Views.DigitalFilterDialog.textUse1": "Brug ? for at repræsentere et vilkårligt tegn", "SSE.Views.DigitalFilterDialog.textUse2": "Brug * for at præsentere en vilkårlig serie af tegn", "SSE.Views.DigitalFilterDialog.txtTitle": "Brugerdefineret filter", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.DocumentHolder.advancedShapeText": "Former a<PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.DocumentHolder.advancedSlicerText": "Slicer a<PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.DocumentHolder.bottomCellText": "Tilpas knap", "SSE.Views.DocumentHolder.bulletsText": "<PERSON><PERSON> og nummerering", "SSE.Views.DocumentHolder.centerCellText": "Tilpas til midten", "SSE.Views.DocumentHolder.chartText": "Diagram avance<PERSON>e inds<PERSON>linger", "SSE.Views.DocumentHolder.deleteColumnText": "Kolonne", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Roter tekst op", "SSE.Views.DocumentHolder.direct90Text": "Roter tekst nedad", "SSE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.directionText": "Tekst retning", "SSE.Views.DocumentHolder.editChartText": "Rediger data", "SSE.Views.DocumentHolder.editHyperlinkText": "Rediger hyperlink", "SSE.Views.DocumentHolder.insertColumnLeftText": "Venstre kolonne", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> over", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON><PERSON> under", "SSE.Views.DocumentHolder.originalSizeText": "Faktisk størrelse", "SSE.Views.DocumentHolder.removeHyperlinkText": "Slet Hyperlink", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON> kolo<PERSON>n", "SSE.Views.DocumentHolder.selectDataText": "Kolonne data", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.strDelete": "Fjern underskrift", "SSE.Views.DocumentHolder.strDetails": "Underskrift detaljer", "SSE.Views.DocumentHolder.strSetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strSign": "Underskriv", "SSE.Views.DocumentHolder.textAlign": "Tilpas", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Flyt til baggrund", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "Ryk frem", "SSE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON> til forgrunden", "SSE.Views.DocumentHolder.textAverage": "Gennemsnit", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCrop": "Beskær", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Tilpas", "SSE.Views.DocumentHolder.textEntriesList": "<PERSON><PERSON><PERSON><PERSON> fra drop-down listen", "SSE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFlipV": "Vend lodret", "SSE.Views.DocumentHolder.textFreezePanes": "Freeze Panes", "SSE.Views.DocumentHolder.textFromFile": "Fra fil", "SSE.Views.DocumentHolder.textFromStorage": "Fra lager", "SSE.Views.DocumentHolder.textFromUrl": "Fra URL", "SSE.Views.DocumentHolder.textListSettings": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMacro": "Tildel makro", "SSE.Views.DocumentHolder.textMax": "Ma<PERSON>.", "SSE.Views.DocumentHolder.textMin": "<PERSON>.", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON><PERSON> funk<PERSON>er", "SSE.Views.DocumentHolder.textMoreFormats": "Flere formatter", "SSE.Views.DocumentHolder.textNone": "ingen", "SSE.Views.DocumentHolder.textNumbering": "Nummerering", "SSE.Views.DocumentHolder.textReplace": "Erstat billede", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Roter 90° mod uret", "SSE.Views.DocumentHolder.textRotate90": "Roter 90° med uret", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Tilpas knap", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Tilpas til midten", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Tilpas til venstre", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Tilpas til midten", "SSE.Views.DocumentHolder.textShapeAlignRight": "Tilpas til højre", "SSE.Views.DocumentHolder.textShapeAlignTop": "Tilpas til toppen", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Sum", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Unfreeze Panes", "SSE.Views.DocumentHolder.textVar": "Was", "SSE.Views.DocumentHolder.topCellText": "Tilpas til toppen", "SSE.Views.DocumentHolder.txtAccounting": "Regnskab", "SSE.Views.DocumentHolder.txtAddComment": "Tilføj kommentar", "SSE.Views.DocumentHolder.txtAddNamedRange": "Definer navn", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "stigende", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Auto Fit Column Width", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Auto Fit Row Højde", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "Alle", "SSE.Views.DocumentHolder.txtClearComments": "kommentarer", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Hyperlinks", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Ryd valgte udvalgte sparkline grupper", "SSE.Views.DocumentHolder.txtClearSparklines": "Slet udvalgte Sparklines", "SSE.Views.DocumentHolder.txtClearText": "Tekst", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON> kolo<PERSON>n", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON><PERSON> kolonne bredde", "SSE.Views.DocumentHolder.txtCondFormat": "Betinget formatering", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCurrency": "Valuta", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Brugerdefineret kolonne bredde", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Brugerdefineret række højde", "SSE.Views.DocumentHolder.txtCustomSort": "Brugerdefineret sortering", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDate": "Da<PERSON>", "SSE.Views.DocumentHolder.txtDelete": "Slet", "SSE.Views.DocumentHolder.txtDescending": "Aftagende", "SSE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON> lodret", "SSE.Views.DocumentHolder.txtEditComment": "Rediger kommentar", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrer efter cellefarver", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrer efter skriftfarve", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrer efter valgte cellers værdi", "SSE.Views.DocumentHolder.txtFormula": "Indsæt funktion", "SSE.Views.DocumentHolder.txtFraction": "Fraktion", "SSE.Views.DocumentHolder.txtGeneral": "General ", "SSE.Views.DocumentHolder.txtGroup": "Gruppe", "SSE.Views.DocumentHolder.txtHide": "Skjul", "SSE.Views.DocumentHolder.txtInsert": "indsæt", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hyperlink", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "Tal format", "SSE.Views.DocumentHolder.txtPaste": "Indsæt", "SSE.Views.DocumentHolder.txtPercentage": "Procent", "SSE.Views.DocumentHolder.txtReapply": "Genanvend", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON><PERSON> række højde", "SSE.Views.DocumentHolder.txtScientific": "Videnskabelig", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON> celler <PERSON>", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON> celler til venstre", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON> celler til højre", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON> celler op", "SSE.Views.DocumentHolder.txtShow": "Vis", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON> kommentar", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.txtSortCellColor": "Valgte cellefarve på toppen", "SSE.Views.DocumentHolder.txtSortFontColor": "Valgte skriftfarve øverst", "SSE.Views.DocumentHolder.txtSparklines": "Minidiagram", "SSE.Views.DocumentHolder.txtText": "Tekst", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON><PERSON><PERSON><PERSON> in<PERSON> for Afsnit ", "SSE.Views.DocumentHolder.txtTime": "Tid", "SSE.Views.DocumentHolder.txtUngroup": "Fjern fra gruppe", "SSE.Views.DocumentHolder.txtWidth": "Bredde", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON> justering", "SSE.Views.ExternalLinksDlg.textDelete": "Bryd links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "<PERSON><PERSON>d alle links", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "subtotaler", "SSE.Views.FieldSettingsDialog.textReport": "Rapportformular", "SSE.Views.FieldSettingsDialog.textTitle": "Feltindstillinger", "SSE.Views.FieldSettingsDialog.txtAverage": "Gennemsnitlig", "SSE.Views.FieldSettingsDialog.txtBlank": "Insæt tomme rækker efter hverenkelt genstand", "SSE.Views.FieldSettingsDialog.txtBottom": "Vis nederst i gruppen", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompakt", "SSE.Views.FieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON> tal", "SSE.Views.FieldSettingsDialog.txtCustomName": "Brugerdefineret navn", "SSE.Views.FieldSettingsDialog.txtEmpty": "Vis genstande uden data", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.FieldSettingsDialog.txtRepeat": "Gentag etiketter på hver række", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Vis subtotaler", "SSE.Views.FieldSettingsDialog.txtSourceName": "Kildenavn:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Sum", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funktioner for subtotaler", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabelform", "SSE.Views.FieldSettingsDialog.txtTop": "Vis øverst i gruppen", "SSE.Views.FieldSettingsDialog.txtVar": "Was", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Gå til dokumentplacering", "SSE.Views.FileMenu.btnCloseMenuCaption": "Luk menu", "SSE.Views.FileMenu.btnCreateNewCaption": "Opret ny", "SSE.Views.FileMenu.btnDownloadCaption": "Hent som", "SSE.Views.FileMenu.btnExitCaption": "A<PERSON>lut", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Version historik", "SSE.Views.FileMenu.btnInfoCaption": "Regneark info", "SSE.Views.FileMenu.btnPrintCaption": "Print", "SSE.Views.FileMenu.btnProtectCaption": "Beskyt", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON> nylige", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "Tilbage til regneark", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveAsCaption": "Gem som", "SSE.Views.FileMenu.btnSaveCaption": "Gem", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Gem kopi som", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.FileMenu.btnToEditCaption": "Rediger regneark", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>t regne<PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Opret ny", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tilføj tekst", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applikation", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Skift adgangsrettigheder", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Oprettet", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Sidst redigeret af", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Sidst redigeret", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokation", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON> der har rettigheder", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titel", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Skift adgangsrettigheder", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON> der har rettigheder", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Fællesredigeringstilstand", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Decimal-adskiller", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Skrifttype hentydning", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formelsprog", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Eksempel: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "<PERSON><PERSON> knappen for Indsæt-optioner når indhold indsættes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regionale indstillinger", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Eksempel:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Interface tema", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Tu<PERSON>der separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "M<PERSON>leen<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Brug separatorer baseret på regionale indstillinger", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Standard zoomværdi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Hvert 10. minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Hvert 30. minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Hvert 5. minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Hver time", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Automatisk gendannelse", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Gem automatisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "deaktive<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "<PERSON><PERSON><PERSON> mellem versioner", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON> minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Reference stil", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "hviderussisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "bulgarsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "catalansk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Standard cache tilstand", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "tjekkisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Dansk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Tysk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "græsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Engelsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spansk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "finsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Fransk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "ungarsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "indonesisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italiensk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "japansk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "koreansk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "lettisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "som OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "med<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "hollandsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "portugisisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "portugisisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "rumænsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Russisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "<PERSON>ktiv<PERSON><PERSON> alle", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Akt<PERSON><PERSON><PERSON> alle makroer uden meddelelse", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "slovakisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "slovensk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alle", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "<PERSON>ak<PERSON>v<PERSON><PERSON> alle makroer uden meddelelse", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "svensk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "tyrkisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "ukrainsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "vietnamesisk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "<PERSON><PERSON> besked", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Deaktivér alle makroer med", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "som Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "kinesisk", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Med adgangskode", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Beskyt regneark", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Med underskrift", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Rediger regneark", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Redigering vil fjerne underskrifterne fra regnearket.<br>Fortsæt?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Arket er beskyttet af en kodeord", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Arket skal underskrives.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Gyldige underskrifter er blevet tilføjet til arket. Arket er beskytter for redigering.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Nogle af de digitale underskrifter i regnearket er ugyldige eller kunne ikke verificeres. Regnearket er beskyttet for redigering.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON> underskrifter", "SSE.Views.FormatRulesEditDlg.fillColor": "Udfyldningsfarve", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 Farveskala", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 Farveskala", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Alle rammer", "SSE.Views.FormatRulesEditDlg.textAppearance": "<PERSON><PERSON><PERSON>lke indstilling", "SSE.Views.FormatRulesEditDlg.textApply": "<PERSON><PERSON><PERSON> p<PERSON>", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatisk", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "<PERSON><PERSON><PERSON><PERSON><PERSON> retning", "SSE.Views.FormatRulesEditDlg.textBold": "Fed", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON>nt farve", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bundgrænser", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Kan ikke tilføje den betingede formatering.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Cellens midtpunkt", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Indsæt lodrette rammer", "SSE.Views.FormatRulesEditDlg.textClear": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textColor": "Tekstfarve", "SSE.Views.FormatRulesEditDlg.textContext": "Sammenhæng", "SSE.Views.FormatRulesEditDlg.textCustom": "Brugerdefineret", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON><PERSON> ne<PERSON> kant", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diagonal opad kant", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Indtast en gyldig formel.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "<PERSON> formel, du indtastede, evalueres ikke til et tal, en dato, et klokkeslæt eller en streng.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Indtast en værdi.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "<PERSON>, du indt<PERSON><PERSON>, er ikke et gyldigt tal, dato, klok<PERSON><PERSON><PERSON>t eller streng.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "\n<PERSON><PERSON><PERSON><PERSON> for {0} skal være større end værdien for {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Indtast et tal mellem {0} og {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formular", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "n<PERSON>r {0} {1} og", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "n<PERSON>r {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "n<PERSON>r værdien er", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Et eller flere ikondataområder overlapper hinanden.<br><PERSON><PERSON> i<PERSON>, så områderne ikke overlapper.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON> stil", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Inden for grænser", "SSE.Views.FormatRulesEditDlg.textInvalid": "Ugyldigt datainterval.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "FEJL! Ugyldigt celleområde", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Element", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Venstre til højre", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON><PERSON> ramme", "SSE.Views.FormatRulesEditDlg.textLongBar": "længste linje", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON> van<PERSON>tte rammer", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Midtpunkt", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minpunkt", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativ", "SSE.Views.FormatRulesEditDlg.textNewColor": "Brugerdefineret farve", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Ingen rammer", "SSE.Views.FormatRulesEditDlg.textNone": "ingen", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "En eller flere af de angivne værdier er ikke en gyldig procentdel.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Den angivne {0}-værdi er ikke en gyldig procentdel.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "En eller flere af de angivne værdier er ikke en gyldig percentil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Den angivne {0}-værdi er ikke en gyldig procentdel.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON> rammer", "SSE.Views.FormatRulesEditDlg.textPercent": "Procent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "Positiv", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "Forhåndvisning", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Du kan ikke bruge relative referencer i betingede formateringskriterier for farveskalaer, databjælker og ikonsæt.", "SSE.Views.FormatRulesEditDlg.textReverse": "Omvendt rækkefølge af ikoner", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Højre til venstre", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON><PERSON> rammer", "SSE.Views.FormatRulesEditDlg.textRule": "Regel", "SSE.Views.FormatRulesEditDlg.textSameAs": "\nDet samme som positivt", "SSE.Views.FormatRulesEditDlg.textSelectData": "Vælg data", "SSE.Views.FormatRulesEditDlg.textShortBar": "korteste linje", "SSE.Views.FormatRulesEditDlg.textShowBar": "vis kun linje", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Vis kun ikon", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Denne type reference kan ikke bruges i en betinget formateringsformel.<br>Skift referencen til en enkelt celle, eller brug referencen med en regnearksfunktion, såsom =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Masiv", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Overstreg", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscript", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superscript", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON><PERSON> rammer", "SSE.Views.FormatRulesEditDlg.textUnderline": "Understreg", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Tal format", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Regnskab", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Valuta", "SSE.Views.FormatRulesEditDlg.txtDate": "Da<PERSON>", "SSE.Views.FormatRulesEditDlg.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.FormatRulesEditDlg.txtFraction": "Brøk", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Generel", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Intet ikon", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Procent", "SSE.Views.FormatRulesEditDlg.txtScientific": "Videnskabelig", "SSE.Views.FormatRulesEditDlg.txtText": "Tekst", "SSE.Views.FormatRulesEditDlg.txtTime": "Tid", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Rediger formateringsregel", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "\nNy formateringsregel", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> over g<PERSON><PERSON><PERSON><PERSON><PERSON> over", "SSE.Views.FormatRulesManagerDlg.text1Below": "1. af<PERSON><PERSON><PERSON> for værdier under gennemsnittet", "SSE.Views.FormatRulesManagerDlg.text2Above": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> for væ<PERSON><PERSON> over gennemsnittet", "SSE.Views.FormatRulesManagerDlg.text2Below": "2. af<PERSON><PERSON><PERSON> for værdier under genne<PERSON><PERSON>tte<PERSON>", "SSE.Views.FormatRulesManagerDlg.text3Above": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> for væ<PERSON><PERSON> over genne<PERSON>nittet", "SSE.Views.FormatRulesManagerDlg.text3Below": "3. a<PERSON><PERSON><PERSON><PERSON> for værdier under genne<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textAbove": "Over gennemsnit", "SSE.Views.FormatRulesManagerDlg.textApply": "<PERSON><PERSON><PERSON> til", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Celleværdi begynder med", "SSE.Views.FormatRulesManagerDlg.textBelow": "Under middel", "SSE.Views.FormatRulesManagerDlg.textBetween": "er mellem {0} og {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textContains": "Celleværdien indeholder", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cellen indeholder en tom værdi", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Cellen indeholder en fejl", "SSE.Views.FormatRulesManagerDlg.textDelete": "Slet", "SSE.Views.FormatRulesManagerDlg.textDown": "\nFlyt reglen ned", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON> væ<PERSON>", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Celleværdien slutter med", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Lige til eller over gennemsnittet", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Lige til eller under gennemsnittet", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "<PERSON><PERSON> sæt", "SSE.Views.FormatRulesManagerDlg.textNew": "Ny", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "er ikke mellem {0} og {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Celleværdien indeholder ikke", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cellen indeholder ikke en tom værdi", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cellen indeholder ikke en fejl", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Vis <PERSON><PERSON><PERSON> for", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Vælg data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Aktuelt udvalg", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON> pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> tabel", "SSE.Views.FormatRulesManagerDlg.textUnique": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textUp": "\nFlyt reglen op", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Dette element er ved at blive redigeret af en anden bruger.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Betinget formatering", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Forbundet med kilde", "SSE.Views.FormatSettingsDialog.textSeparator": "Brug 1000 separator", "SSE.Views.FormatSettingsDialog.textSymbols": "Symboler", "SSE.Views.FormatSettingsDialog.textTitle": "Tal format", "SSE.Views.FormatSettingsDialog.txtAccounting": "Regnskab", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON> tie<PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON> <PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON> hal<PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON> fjer<PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Valuta", "SSE.Views.FormatSettingsDialog.txtCustom": "Brugerdefineret", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Indtast venligst selvvalgt nummerformat nøjagtigt. Spreadsheet Editor tjekker ikke selvvalgte formater for fejl, der kan påvirke xlsx-filen.", "SSE.Views.FormatSettingsDialog.txtDate": "Da<PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "Fraktion", "SSE.Views.FormatSettingsDialog.txtGeneral": "General ", "SSE.Views.FormatSettingsDialog.txtNone": "ingen", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Procent", "SSE.Views.FormatSettingsDialog.txtSample": "Prøve", "SSE.Views.FormatSettingsDialog.txtScientific": "Videnskabelig", "SSE.Views.FormatSettingsDialog.txtText": "Tekst", "SSE.Views.FormatSettingsDialog.txtTime": "Tid", "SSE.Views.FormatSettingsDialog.txtUpto1": "Op til et ciffer (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Op til to cifre (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Op til tre cifre (131/135)", "SSE.Views.FormulaDialog.sDescription": "Beskrivelse", "SSE.Views.FormulaDialog.textGroupDescription": "Vælg funktionsgruppe", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "An<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "Indsæt funktion", "SSE.Views.FormulaTab.textAutomatic": "Automatisk", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Udregn nuværende ark", "SSE.Views.FormulaTab.textCalculateWorkbook": "Udregn projektmappe", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON>gn", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Udregn hele projekt<PERSON>ppen", "SSE.Views.FormulaTab.txtAdditional": "Ekstra", "SSE.Views.FormulaTab.txtAutosum": "Autosum", "SSE.Views.FormulaTab.txtAutosumTip": "Summering", "SSE.Views.FormulaTab.txtCalculation": "Beregning", "SSE.Views.FormulaTab.txtFormula": "Funktion", "SSE.Views.FormulaTab.txtFormulaTip": "Indsæt funktion", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON><PERSON> funk<PERSON>er", "SSE.Views.FormulaTab.txtRecent": "<PERSON><PERSON><PERSON> for nyligt", "SSE.Views.FormulaWizard.textAny": "vilkårlig", "SSE.Views.FormulaWizard.textArgument": "Variabel", "SSE.Views.FormulaWizard.textFunction": "Funktion", "SSE.Views.FormulaWizard.textFunctionRes": "Funktionsresultat", "SSE.Views.FormulaWizard.textHelp": "H<PERSON>ælp med denne funktion", "SSE.Views.FormulaWizard.textLogical": "logisk", "SSE.Views.FormulaWizard.textNoArgs": "Denne funktion har ingen variable.", "SSE.Views.FormulaWizard.textNumber": "antal", "SSE.Views.FormulaWizard.textRef": "hen<PERSON><PERSON>", "SSE.Views.FormulaWizard.textText": "tekst", "SSE.Views.FormulaWizard.textTitle": "Funktionsvariabler", "SSE.Views.FormulaWizard.textValue": "Formelresultat", "SSE.Views.HeaderFooterDialog.textAlign": "Tilpas til side-margener", "SSE.Views.HeaderFooterDialog.textAll": "Alle sider", "SSE.Views.HeaderFooterDialog.textBold": "Fed", "SSE.Views.HeaderFooterDialog.textCenter": "Centrum", "SSE.Views.HeaderFooterDialog.textColor": "Tekstfarve", "SSE.Views.HeaderFooterDialog.textDate": "Da<PERSON>", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Anden forreste side", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Forskellige ulige og lige sider", "SSE.Views.HeaderFooterDialog.textEven": "Lige side", "SSE.Views.HeaderFooterDialog.textFileName": "Filnavn", "SSE.Views.HeaderFooterDialog.textFirst": "Første side", "SSE.Views.HeaderFooterDialog.textFooter": "Sidefod", "SSE.Views.HeaderFooterDialog.textHeader": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textInsert": "Indsæt", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "Den indtastede tekststreng er for lang. Reducer antallet af anvendte tegn.", "SSE.Views.HeaderFooterDialog.textNewColor": "Brugerdefineret farve", "SSE.Views.HeaderFooterDialog.textOdd": "Ulige side", "SSE.Views.HeaderFooterDialog.textPageCount": "Side antal", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Skaler med dokument", "SSE.Views.HeaderFooterDialog.textSheet": "Ark navn", "SSE.Views.HeaderFooterDialog.textStrikeout": "Overstreg", "SSE.Views.HeaderFooterDialog.textSubscript": "sænket", "SSE.Views.HeaderFooterDialog.textSuperscript": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTime": "Tid", "SSE.Views.HeaderFooterDialog.textTitle": "Sidehoved/sidefod indstillinger", "SSE.Views.HeaderFooterDialog.textUnderline": "Understreg", "SSE.Views.HeaderFooterDialog.tipFontName": "Skrifttype", "SSE.Views.HeaderFooterDialog.tipFontSize": "Skriftstørrelse", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "V<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Link til", "SSE.Views.HyperlinkSettingsDialog.strRange": "Rækkevidde", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Ark", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Valgte interval", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON>k<PERSON><PERSON> billed<PERSON><PERSON>t her", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Indsæt link her", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Indtast værktøjstip her", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Eksternt link", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Få link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Intern data rækkevidde", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Views.HyperlinkSettingsDialog.textNames": "Definerede navne", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Vælg data", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Ark", "SSE.Views.HyperlinkSettingsDialog.textTipText": "SkærmTip tekst", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink indstillinger", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Feltet skal være en URL i \"http://www.example.com\" formatet", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Feltet er begrænset til 2083 karakterer", "SSE.Views.ImageSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "SSE.Views.ImageSettings.textCrop": "Beskær", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Tilpas", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Rediger objekt", "SSE.Views.ImageSettings.textFlip": "Vend", "SSE.Views.ImageSettings.textFromFile": "Fra fil", "SSE.Views.ImageSettings.textFromStorage": "Fra lager", "SSE.Views.ImageSettings.textFromUrl": "Fra URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "Roter 90° mod uret", "SSE.Views.ImageSettings.textHint90": "Roter 90° med uret", "SSE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textHintFlipV": "Vend lodret", "SSE.Views.ImageSettings.textInsert": "Erstat billede", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON>er", "SSE.Views.ImageSettings.textOriginalSize": "Faktisk størrelse", "SSE.Views.ImageSettings.textRotate90": "Roter 90°", "SSE.Views.ImageSettings.textRotation": "Rotation", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "Bredde", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Flyt- og tilpas ikke med felter", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternativ tekst", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Beskrivelse", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive oplæst til folk med syns- eller læringsudfordringer, for at hjælpe dem til at forstå den information der kan findes i et billede, autofigur, diagram eller tabel.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Flyt men skalér ikke med felter", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ImageSettingsAdvanced.textSnap": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Flyt og skalér med felter", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipAbout": "Om", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "kommentarer", "SSE.Views.LeftMenu.tipFile": "Fil", "SSE.Views.LeftMenu.tipPlugins": "Tilføjelsesprogrammer", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Stavekontrol", "SSE.Views.LeftMenu.tipSupport": "Feedback & support", "SSE.Views.LeftMenu.txtDeveloper": "Udviklingstilstand", "SSE.Views.LeftMenu.txtLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> adgang", "SSE.Views.LeftMenu.txtTrial": "Prøvetilstand", "SSE.Views.LeftMenu.txtTrialDev": "Udvikler prøve-tilstand", "SSE.Views.MacroDialog.textMacro": "Makro navn", "SSE.Views.MacroDialog.textTitle": "Tildel makro", "SSE.Views.MainSettingsPrint.okButtonText": "Gem", "SSE.Views.MainSettingsPrint.strBottom": "Bund", "SSE.Views.MainSettingsPrint.strLandscape": "Landskab", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrint": "Print", "SSE.Views.MainSettingsPrint.strPrintTitles": "Print Titler", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Top", "SSE.Views.MainSettingsPrint.textActualSize": "Faktisk størrelse", "SSE.Views.MainSettingsPrint.textCustom": "Brugerdefineret", "SSE.Views.MainSettingsPrint.textCustomOptions": "Brugerdefinerede indstillinger", "SSE.Views.MainSettingsPrint.textFitCols": "Saml alle kolonner på en side", "SSE.Views.MainSettingsPrint.textFitPage": "Tilpas ark til en side", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON> alle rækker på en side", "SSE.Views.MainSettingsPrint.textPageOrientation": "Sideorientering", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Sidestørrelse", "SSE.Views.MainSettingsPrint.textPrintGrid": "Udskriv gitterlinier", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Udskriv række og kolonne overskrifter", "SSE.Views.MainSettingsPrint.textRepeat": "Gentag...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Gentag kolonner til venstre", "SSE.Views.MainSettingsPrint.textRepeatTop": "Gentag rækker ø<PERSON>t", "SSE.Views.MainSettingsPrint.textSettings": "<PERSON><PERSON><PERSON><PERSON> for", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "De eksisterende navngivne intervaller kan ikke redigeres, og de nye kan ikke oprettes<br>i øjeblik<PERSON>, da nogle af dem er i gang med at blive redigeret.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defineret navn", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Arbejdsbog", "SSE.Views.NamedRangeEditDlg.textDataRange": "Data rækkevidde", "SSE.Views.NamedRangeEditDlg.textExistName": "FEJL! En rækkevidde med dette navn eksisterer allerede", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Navnet skal starte med et bogstav eller en underscore og må ikke indeholde ugyldige tegn.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Views.NamedRangeEditDlg.textIsLocked": "FEJL! Dette element bliver redigeret af en anden bruger. ", "SSE.Views.NamedRangeEditDlg.textName": "Navn", "SSE.Views.NamedRangeEditDlg.textReservedName": "Navnet du forsøger at bruge er allerede refereret i en celle formular. Benyt venligst et andet navn.", "SSE.Views.NamedRangeEditDlg.textScope": "Anvendelsesområde", "SSE.Views.NamedRangeEditDlg.textSelectData": "Vælg data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Rediger navn", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nyt navn", "SSE.Views.NamedRangePasteDlg.textNames": "Navngivne rækkevidder", "SSE.Views.NamedRangePasteDlg.txtTitle": "Indsæt navn", "SSE.Views.NameManagerDlg.closeButtonText": "Luk", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Data rækkevidde", "SSE.Views.NameManagerDlg.textDelete": "Slet", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Ingen navngivne rækkervidder er blevet oprettet endnu.<br>Lav mindst en navngiven rækkevidde og de vil blive vist i dette felt.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "Alle", "SSE.Views.NameManagerDlg.textFilterDefNames": "Definerede navne", "SSE.Views.NameManagerDlg.textFilterSheet": "Names Scoped to Sheet", "SSE.Views.NameManagerDlg.textFilterTableNames": "Tabel navn", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Names Scoped to Workbook", "SSE.Views.NameManagerDlg.textNew": "Ny", "SSE.Views.NameManagerDlg.textnoNames": "Ingen navngivne rækkevidder som matcher dit filter kunne findes. ", "SSE.Views.NameManagerDlg.textRanges": "Navngivne rækkevidder", "SSE.Views.NameManagerDlg.textScope": "Anvendelsesområde", "SSE.Views.NameManagerDlg.textWorkbook": "Arbejdsbog", "SSE.Views.NameManagerDlg.tipIsLocked": "Elementet bliver redigeret af en anden bruger.", "SSE.Views.NameManagerDlg.txtTitle": "Navneh<PERSON>ndtering", "SSE.Views.NameManagerDlg.warnDelete": "<PERSON>r du sikker på at du vil slette navnet {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Bund", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Top", "SSE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Afsnit afstand", "SSE.Views.ParagraphSettings.strSpacingAfter": "efter", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "SSE.Views.ParagraphSettings.textAt": "Til", "SSE.Views.ParagraphSettings.textAtLeast": "Mindst", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "automatisk", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "De specificerende faner vil blive vist i dette felt. ", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Alle caps", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON> gennemstregning", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Led", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciel", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Af", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Skrifttype", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indrykninger og afstand", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Small caps", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Afstand", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Gennemstregning", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Tilpasning", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON> afstand", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Standard fane", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON><PERSON> linie", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Hængende", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ingen)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON> alle", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Specifer", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centrum", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Fane position", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "er lig med", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "slutter ikke med", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "indeholder", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "indeholder ikke", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "i mellem", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "ikke mellem", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "er ikke lig med", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "er større end", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "er større end eller lig med", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "er mindre end", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "er mindre end eller lig med", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "begynder med", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "begynder ikke med", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "slutter med", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Vis emner gældende for mærkat:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Vis emner for hvilket:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Brug ? for at repræsentere et vilkårligt tegn", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Brug * for at vise en vilkårlig serie af tegn", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "og", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Navngiv filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Værdi-filter", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "Af", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "Slutter ved", "SSE.Views.PivotGroupDialog.textError": "<PERSON><PERSON> felt skal indeholde en numerisk værdi", "SSE.Views.PivotGroupDialog.textGreaterError": "Slutnummeret skal være større end startnummeret.", "SSE.Views.PivotGroupDialog.textHour": "Timer", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "Antal dage", "SSE.Views.PivotGroupDialog.textQuart": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "Startende ved", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "Tilføj til kolonner", "SSE.Views.PivotSettings.txtAddFilter": "Tilføj til filtre", "SSE.Views.PivotSettings.txtAddRow": "Tilfø<PERSON> til rækker", "SSE.Views.PivotSettings.txtAddValues": "Tilføj til værdier", "SSE.Views.PivotSettings.txtFieldSettings": "Feltindstillinger", "SSE.Views.PivotSettings.txtMoveBegin": "<PERSON>t til begyndelsen", "SSE.Views.PivotSettings.txtMoveColumn": "Flyt til kolonner", "SSE.Views.PivotSettings.txtMoveDown": "Flyt ned", "SSE.Views.PivotSettings.txtMoveEnd": "Flyt til slutningen", "SSE.Views.PivotSettings.txtMoveFilter": "Flyt til filtrer", "SSE.Views.PivotSettings.txtMoveRow": "Flyt til rækker", "SSE.Views.PivotSettings.txtMoveUp": "Flyt op", "SSE.Views.PivotSettings.txtMoveValues": "Flyt til værdier", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON> felt", "SSE.Views.PivotSettingsAdvanced.strLayout": "Navn og layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternativ tekst", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Beskrivelse", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive læst til folk med syns- eller læringsudfordringer for at hjælpe dem til at forstå den information der kan findes i et billede, autoshape, diagram eller tabel", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Data rækkevidde", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Datakilde", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "<PERSON>is felter i rapport-filter område", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON>, s<PERSON> over", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "<PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Feltoversk<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Views.PivotSettingsAdvanced.textOver": "Over, så under", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Vælg data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Vis for kolonner", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "<PERSON><PERSON>k<PERSON>er for rækker og kolonner", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Vis for rækker", "SSE.Views.PivotSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Rapporter filterfelter pr. kolonne", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Rapporter filterfelter pr. ræ<PERSON>ke", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.PivotSettingsAdvanced.txtName": "Navn", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "<PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.PivotTable.capLayout": "Rapporter layout", "SSE.Views.PivotTable.capSubtotals": "subtotaler", "SSE.Views.PivotTable.mniBottomSubtotals": "Vis alle subtotaler i bunden af gruppen", "SSE.Views.PivotTable.mniInsertBlankLine": "Indsæt tom linie efter hvert element", "SSE.Views.PivotTable.mniLayoutCompact": "Vis i kompakt form", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Gentag ikke alle elementetiketter", "SSE.Views.PivotTable.mniLayoutOutline": "Vis i omridset form", "SSE.Views.PivotTable.mniLayoutRepeat": "Gentag alle element etiketter", "SSE.Views.PivotTable.mniLayoutTabular": "Vis i tabelform ", "SSE.Views.PivotTable.mniNoSubtotals": "Vis ikke subtotaler", "SSE.Views.PivotTable.mniOffTotals": "Af for rækker og kolonner", "SSE.Views.PivotTable.mniOnColumnsTotals": "Kun for kolonner", "SSE.Views.PivotTable.mniOnRowsTotals": "<PERSON><PERSON> for rækker", "SSE.Views.PivotTable.mniOnTotals": "For rækker og kolonner", "SSE.Views.PivotTable.mniRemoveBlankLine": "<PERSON><PERSON>n tom linie efter hvert element", "SSE.Views.PivotTable.mniTopSubtotals": "Vis alle subtotaleri toppen af gruppen", "SSE.Views.PivotTable.textColBanded": "Banded kolonner", "SSE.Views.PivotTable.textColHeader": "Kolonneoversk<PERSON><PERSON>", "SSE.Views.PivotTable.textRowBanded": "Banded Rows", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON>krift<PERSON>", "SSE.Views.PivotTable.tipCreatePivot": "Indsæt pivottabel", "SSE.Views.PivotTable.tipGrandTotals": "<PERSON><PERSON> eller skjul samlede beløber", "SSE.Views.PivotTable.tipRefresh": "Opdater information fra datakilder", "SSE.Views.PivotTable.tipSelect": "<PERSON><PERSON><PERSON><PERSON> hele pivotta<PERSON>en", "SSE.Views.PivotTable.tipSubtotals": "<PERSON>is eller skjul subtotaler", "SSE.Views.PivotTable.txtCreate": "Indsæt tabel", "SSE.Views.PivotTable.txtGroupPivot_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtPivotTable": "Pivottabel", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.btnDownload": "Gem og download", "SSE.Views.PrintSettings.btnPrint": "Gem og print", "SSE.Views.PrintSettings.strBottom": "Bund", "SSE.Views.PrintSettings.strLandscape": "Landskab", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPrint": "Print", "SSE.Views.PrintSettings.strPrintTitles": "Print Titler", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Vis", "SSE.Views.PrintSettings.strTop": "Top", "SSE.Views.PrintSettings.textActualSize": "Faktisk størrelse", "SSE.Views.PrintSettings.textAllSheets": "Alle ark", "SSE.Views.PrintSettings.textCurrentSheet": "Nuværende ark", "SSE.Views.PrintSettings.textCustom": "Brugerdefineret", "SSE.Views.PrintSettings.textCustomOptions": "Brugerdefinerede indstillinger", "SSE.Views.PrintSettings.textFitCols": "Saml alle kolonner på en side", "SSE.Views.PrintSettings.textFitPage": "Tilpas ark til en side", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON> alle rækker på en side", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorer print-område", "SSE.Views.PrintSettings.textLayout": "Layout", "SSE.Views.PrintSettings.textPageOrientation": "Sideorientering", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Sidestørrelse", "SSE.Views.PrintSettings.textPrintGrid": "Udskriv gitterlinier", "SSE.Views.PrintSettings.textPrintHeadings": "Udskriv række og kolonne overskrifter", "SSE.Views.PrintSettings.textPrintRange": "Udskriv rækkevidde", "SSE.Views.PrintSettings.textRange": "Rækkevidde", "SSE.Views.PrintSettings.textRepeat": "Gentag...", "SSE.Views.PrintSettings.textRepeatLeft": "Gentag kolonner til venstre", "SSE.Views.PrintSettings.textRepeatTop": "Gentag rækker ø<PERSON>t", "SSE.Views.PrintSettings.textSelection": "Udvæ<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "<PERSON> in<PERSON>", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "<PERSON><PERSON> gitte<PERSON>", "SSE.Views.PrintSettings.textShowHeadings": "Vis række og kolonne overskrifter", "SSE.Views.PrintSettings.textTitle": "Udskrift indstillinger", "SSE.Views.PrintSettings.textTitlePDF": "PDF indstilliniger", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON>e kolonne", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Views.PrintTitlesDialog.textLeft": "Gentag kolonner til venstre", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Gentag ikke", "SSE.Views.PrintTitlesDialog.textRepeat": "Gentag...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "Print Titler", "SSE.Views.PrintTitlesDialog.textTop": "Gentag rækker ø<PERSON>t", "SSE.Views.PrintWithPreview.txtBottom": "Bund", "SSE.Views.PrintWithPreview.txtSave": "Gem", "SSE.Views.ProtectDialog.textExistName": "FEJL! Rækkevidde med en sådan titel findes allerede", "SSE.Views.ProtectDialog.textInvalidName": "Områdetitlen skal begynde med et bogstav og må kun indeholde bogstaver, tal og mellemrum.", "SSE.Views.ProtectDialog.textInvalidRange": "FEJL! Ugyldigt celleområde", "SSE.Views.ProtectDialog.textSelectData": "Vælg data", "SSE.Views.ProtectDialog.txtAllow": "Tillad alle brugere af dette ark", "SSE.Views.ProtectDialog.txtAutofilter": "Brug AutoFilter", "SSE.Views.ProtectDialog.txtDelCols": "Slet kolonner", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON> ræ<PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.ProtectDialog.txtFormatCells": "Formater celler", "SSE.Views.ProtectDialog.txtFormatCols": "Formater kolonner", "SSE.Views.ProtectDialog.txtFormatRows": "Formater rækker", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Bekræftelsesadgangskoden er ikke identisk", "SSE.Views.ProtectDialog.txtInsCols": "Indsæt kolonner", "SSE.Views.ProtectDialog.txtInsHyper": "Indsæt hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON>dsæ<PERSON> rækker", "SSE.Views.ProtectDialog.txtObjs": "<PERSON><PERSON> objekter", "SSE.Views.ProtectDialog.txtOptional": "valg<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPassword": "Kodeord", "SSE.Views.ProtectDialog.txtPivot": "Brug pivottabel og pivotdiagram", "SSE.Views.ProtectDialog.txtProtect": "Beskyt", "SSE.Views.ProtectDialog.txtRange": "Rækkevidde", "SSE.Views.ProtectDialog.txtRangeName": "Titel", "SSE.Views.ProtectDialog.txtRepeat": "\nGentag adgangskode", "SSE.Views.ProtectDialog.txtScen": "<PERSON><PERSON> scenarier", "SSE.Views.ProtectDialog.txtSelLocked": "<PERSON><PERSON><PERSON><PERSON> celler", "SSE.Views.ProtectDialog.txtSelUnLocked": "<PERSON><PERSON><PERSON><PERSON> celler", "SSE.Views.ProtectDialog.txtSheetDescription": "Forebyg uønskede ændringer fra andre ved at begrænse deres evne til at redigere.", "SSE.Views.ProtectDialog.txtSheetTitle": "Beskyt ark", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.ProtectDialog.txtWarning": "Advarsel! Hvis du mister eller glem<PERSON> ad<PERSON>, kan den ikke genoprettes. Opbevar den et sikkert sted.", "SSE.Views.ProtectDialog.txtWBDescription": "\nFor at forhindre andre brugere i at se skjulte regneark, til<PERSON><PERSON><PERSON>, flytte, slette eller skjule regneark og omdøbe regneark, kan du beskytte strukturen af ​​din projektmappe med en adgangskode.", "SSE.Views.ProtectDialog.txtWBTitle": "Beskyt projektmappestrukturen", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Slet", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Ingen områder tilladt til redigering.", "SSE.Views.ProtectRangesDlg.textNew": "Ny", "SSE.Views.ProtectRangesDlg.textProtect": "Beskyt ark", "SSE.Views.ProtectRangesDlg.textPwd": "Kodeord", "SSE.Views.ProtectRangesDlg.textRange": "Rækkevidde", "SSE.Views.ProtectRangesDlg.textRangesDesc": "\nOmråder låst op med en adgangskode, når arket er beskyttet (dette virker kun for l<PERSON><PERSON> celler)", "SSE.Views.ProtectRangesDlg.textTitle": "Titel", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Dette element er ved at blive redigeret af en anden bruger.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON>iger rækkevidde", "SSE.Views.ProtectRangesDlg.txtNewRange": "\nNy serie", "SSE.Views.ProtectRangesDlg.txtNo": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtTitle": "<PERSON><PERSON> brugere at redigere områder", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON>a", "SSE.Views.ProtectRangesDlg.warnDelete": "<PERSON>r du sikker på, at du vil slette navnet {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "<PERSON><PERSON> du vil slette dup<PERSON>v<PERSON>rdi<PERSON>, skal du vælge en eller flere kolonner, der indeholder duplikater.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Min data har sidehoveder", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON> alle", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Fjern duplikater", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON> inds<PERSON>linger", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON> indstillinger", "SSE.Views.RightMenu.txtPivotSettings": "Pivot tabelindstillinger", "SSE.Views.RightMenu.txtSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtShapeSettings": "Former indstillinger", "SSE.Views.RightMenu.txtSignatureSettings": "Underskrift indstillinger", "SSE.Views.RightMenu.txtSlicerSettings": "Slicer-in<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSparklineSettings": "Minidiagram indstillinger", "SSE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Textstil indstllinger", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "Den indt<PERSON>ede værdi er forkert.", "SSE.Views.ScaleDialog.textFewPages": "sider", "SSE.Views.ScaleDialog.textFitTo": "Tilpas til", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "sider", "SSE.Views.ScaleDialog.textOnePage": "Side", "SSE.Views.ScaleDialog.textScaleTo": "<PERSON><PERSON><PERSON> til", "SSE.Views.ScaleDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>indst<PERSON><PERSON>", "SSE.Views.ScaleDialog.textWidth": "Bredde", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON> maksima<PERSON> væ<PERSON> for de<PERSON> felt er {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON> maksima<PERSON> væ<PERSON> for de<PERSON> felt er {0}", "SSE.Views.ShapeSettings.strBackground": "Baggrundsfarve", "SSE.Views.ShapeSettings.strChange": "Skift autoform", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Forgrundsfarve", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON>e", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strType": "Type", "SSE.Views.ShapeSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Den indtastede værdi er ikke korrekt.<br>venligst indtast en numerisk værdi mellem 0 pt og 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Farvefyld", "SSE.Views.ShapeSettings.textDirection": "Retning", "SSE.Views.ShapeSettings.textEmptyPattern": "Intet mønster", "SSE.Views.ShapeSettings.textFlip": "Vend", "SSE.Views.ShapeSettings.textFromFile": "Fra fil", "SSE.Views.ShapeSettings.textFromStorage": "Fra lager", "SSE.Views.ShapeSettings.textFromUrl": "Fra URL", "SSE.Views.ShapeSettings.textGradient": "Gradientpunkter", "SSE.Views.ShapeSettings.textGradientFill": "Gradient udfyldning", "SSE.Views.ShapeSettings.textHint270": "Roter 90° mod uret", "SSE.Views.ShapeSettings.textHint90": "Roter 90° med uret", "SSE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textHintFlipV": "Vend lodret", "SSE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> eller struktur", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoFill": "Intet fyld", "SSE.Views.ShapeSettings.textOriginalSize": "Original størrelse", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Placering", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRotate90": "Roter 90°", "SSE.Views.ShapeSettings.textRotation": "Rotation", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "Stræk", "SSE.Views.ShapeSettings.textStyle": "Stilart", "SSE.Views.ShapeSettings.textTexture": "<PERSON>a struktur", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Tilfø<PERSON>", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "SSE.Views.ShapeSettings.txtCanvas": "læ<PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Sort stof", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "Ingen linie", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "Træ", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Tekst fyld", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Flyt- og tilpas ikke med felter", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternativ tekst", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Beskrivelse", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive oplæst til folk med syns- eller læringsudfordringer, for at hjælpe dem til at forstå den information der kan findes i et billede, autofigur, diagram eller tabel.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoTilpas", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Be<PERSON>nd stø<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Begynd stil", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Facet", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Bund", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Spids type", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Afslutning størrelse", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Afslutning formattering", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Flad", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Join Type", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>er", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON> stil", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Flyt men skalér ikke med felter", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "<PERSON><PERSON> tekst at overgå form.", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON><PERSON> st<PERSON> på form for at tilpasse tekst", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ShapeSettingsAdvanced.textRound": "Rund", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON>d mellem kolonner", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Tekstboks", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Former - a<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "Top", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Flyt og skalér med felter", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Vægte og pile", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Bredde", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDelete": "Fjern underskrift", "SSE.Views.SignatureSettings.strDetails": "Underskrift detaljer", "SSE.Views.SignatureSettings.strInvalid": "Ugyldige underskrifter", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON><PERSON> underskrifter", "SSE.Views.SignatureSettings.strSetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSign": "Underskriv", "SSE.Views.SignatureSettings.strSignature": "Underskrift", "SSE.Views.SignatureSettings.strSigner": "Underskriver", "SSE.Views.SignatureSettings.strValid": "Gyl<PERSON>ge underskrifter", "SSE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON> alligevel", "SSE.Views.SignatureSettings.txtEditWarning": "Redigering vil fjerne underskrifterne fra regnearket.<br>Fortsæt?", "SSE.Views.SignatureSettings.txtRemoveWarning": "<PERSON>ns<PERSON> du at fjerne denne signatur?<br><PERSON> handling kan ikke omgøres.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Arket skal underskrives.", "SSE.Views.SignatureSettings.txtSigned": "Gyldige underskrifter er blevet tilføjet til arket. Arket er beskytter for redigering.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Nogle af de digitale underskrifter i regnearket er ugyldige eller kunne ikke verificeres. Regnearket er beskyttet for redigering.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Indsæt slicere", "SSE.Views.SlicerSettings.strHideNoData": "Skjul uden indhold", "SSE.Views.SlicerSettings.strIndNoData": "Vis emner uden data", "SSE.Views.SlicerSettings.strShowDel": "<PERSON>is s<PERSON>de emner fra data<PERSON>lden", "SSE.Views.SlicerSettings.strShowNoData": "Vis emner uden data sidst", "SSE.Views.SlicerSettings.strSorting": "Sortering og filtrering", "SSE.Views.SlicerSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "SSE.Views.SlicerSettings.textAsc": "Stigende", "SSE.Views.SlicerSettings.textAZ": "A til z", "SSE.Views.SlicerSettings.textButtons": "<PERSON>nap<PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON>e proportioner", "SSE.Views.SlicerSettings.textLargeSmall": "stø<PERSON> til mindst", "SSE.Views.SlicerSettings.textLock": "<PERSON>ak<PERSON><PERSON><PERSON><PERSON> æ<PERSON> af størrelse eller flytning", "SSE.Views.SlicerSettings.textNewOld": "nyeste til ældste", "SSE.Views.SlicerSettings.textOldNew": "ældste til nyeste", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "mindst til størst", "SSE.Views.SlicerSettings.textStyle": "Stilart", "SSE.Views.SlicerSettings.textVert": "Lodret", "SSE.Views.SlicerSettings.textWidth": "Bredde", "SSE.Views.SlicerSettings.textZA": "Z til A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON>nap<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Skjul uden indhold", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Vis emner uden data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "<PERSON>is s<PERSON>de emner fra data<PERSON>lden", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Visningsoverskrift", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Vis emner uden data sidst", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sortering og filtrering", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stilart", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Stilart og størrelse", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Bredde", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Flyt ikke eller ændr størrelse med", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternativ tekst", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Beskrivelse", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Den alternative, tekstbaserede, repræsentation af det visuelle objekts information, som læses for folk med syns- eller forståelseshandicap, for at hjælpe dem til at bedre forstå hvilke oplysninger der findes i billede, autofigur, diagram eller tabel. ", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Stigende", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A til z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Navn til brug i formler", "SSE.Views.SlicerSettingsAdvanced.textHeader": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON>e proportioner", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "stø<PERSON> til mindst", "SSE.Views.SlicerSettingsAdvanced.textName": "Navn", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "nyeste til ældste", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "ældste til nyeste", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Flyt men skalér ikke med celler", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "mindst til størst", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Cellejustering", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Kildenavn", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - a<PERSON><PERSON><PERSON> in<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Flyt og skalér med celler", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z til A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "<PERSON><PERSON> felt er påkrævet", "SSE.Views.SortDialog.errorEmpty": "Alle sorteringskriterier skal have en kolonne eller række specificeret.", "SSE.Views.SortDialog.errorMoreOneCol": "Mere en én kolonne er valgt.", "SSE.Views.SortDialog.errorMoreOneRow": "Mere end én række er valgt.", "SSE.Views.SortDialog.errorNotOriginalCol": "Den valgte kolonne findes ikke i det oprindelige valgte interval.", "SSE.Views.SortDialog.errorNotOriginalRow": "Den valgte række findes ikke i det oprindelige valgte interval.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 bliver sorteret af den samme farve mere end en gang.<br><PERSON><PERSON> det dobbelte sorteringskriterie og prøv igen.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 bliver sorteret af værdier mere end en gang.<br><PERSON><PERSON> det dobbelte sorteringskriterie og prøv igen.", "SSE.Views.SortDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON> niveau", "SSE.Views.SortDialog.textAsc": "Stigende", "SSE.Views.SortDialog.textAuto": "Automatisk", "SSE.Views.SortDialog.textAZ": "A til Z", "SSE.Views.SortDialog.textBelow": "Under", "SSE.Views.SortDialog.textCellColor": "Cellefarve", "SSE.Views.SortDialog.textColumn": "Kolonne", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON><PERSON> ni<PERSON>", "SSE.Views.SortDialog.textDelete": "Slet niveau", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "Flyt niveau ned", "SSE.Views.SortDialog.textFontColor": "Skriftfarve", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(F<PERSON>e kolonner...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON> rækker...)", "SSE.Views.SortDialog.textNone": "Ingen", "SSE.Views.SortDialog.textOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "Rækkefølge", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Sorter på", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "SSE.Views.SortDialog.textThenBy": "Så ved", "SSE.Views.SortDialog.textTop": "Top", "SSE.Views.SortDialog.textUp": "Flyt niveau op", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z til A", "SSE.Views.SortDialog.txtInvalidRange": "<PERSON><PERSON><PERSON><PERSON> felt-r<PERSON>kkevidde.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SortFilterDialog.textAsc": "Stigende (a til z) efter", "SSE.Views.SortFilterDialog.textDesc": "Faldende (z til a) efter", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SortOptionsDialog.textCase": "Afhængigt af store og små bogstaver", "SSE.Views.SortOptionsDialog.textHeaders": "Min data har sidehoveder", "SSE.Views.SortOptionsDialog.textLeftRight": "Sorter fra venstre til højre", "SSE.Views.SortOptionsDialog.textOrientation": "Orientering", "SSE.Views.SortOptionsDialog.textTitle": "Sorteringsindstillinger", "SSE.Views.SortOptionsDialog.textTopBottom": "Sorter øverst til nederst", "SSE.Views.SpecialPasteDialog.textAdd": "Tilføj", "SSE.Views.SpecialPasteDialog.textAll": "Alle", "SSE.Views.SpecialPasteDialog.textBlanks": "Spring tomme over", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textComments": "kommentarer", "SSE.Views.SpecialPasteDialog.textDiv": "Adskil", "SSE.Views.SpecialPasteDialog.textFFormat": "Formler & formattering", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formler & talformater", "SSE.Views.SpecialPasteDialog.textFormats": "Formater", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "Formler & kolonne bredde", "SSE.Views.SpecialPasteDialog.textMult": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textNone": "ingen", "SSE.Views.SpecialPasteDialog.textOperation": "Handling", "SSE.Views.SpecialPasteDialog.textPaste": "Indsæt", "SSE.Views.SpecialPasteDialog.textSub": "Trække fra", "SSE.Views.SpecialPasteDialog.textTitle": "Indsæt speciel", "SSE.Views.SpecialPasteDialog.textTranspose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Værdier & formattering", "SSE.Views.SpecialPasteDialog.textVNFormat": "Værdier & nummerformater", "SSE.Views.SpecialPasteDialog.textWBorders": "Alle undtagen grænser", "SSE.Views.Spellcheck.noSuggestions": "Ingen stave-forslag", "SSE.Views.Spellcheck.textChange": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON><PERSON> alle", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "Ignorer alt", "SSE.Views.Spellcheck.txtAddToDictionary": "Tilføj til Ordbog", "SSE.Views.Spellcheck.txtComplete": "Stavekontrol er afsluttet", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Sprogvalg for ordbog", "SSE.Views.Spellcheck.txtNextTip": "Gå til næste ord", "SSE.Views.Spellcheck.txtSpelling": "Stavning", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON><PERSON> til slut)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON>t til slutning)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Indsæt før ark", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Flyt før ark", "SSE.Views.Statusbar.filteredRecordsText": "{0} af {1} poster filtreret", "SSE.Views.Statusbar.filteredText": "Filtreringstilstand", "SSE.Views.Statusbar.itemAverage": "Gennemsnit", "SSE.Views.Statusbar.itemCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemCount": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemDelete": "Slet", "SSE.Views.Statusbar.itemHidden": "Skjult", "SSE.Views.Statusbar.itemHide": "Skjul", "SSE.Views.Statusbar.itemInsert": "indsæt", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMove": "Flyt", "SSE.Views.Statusbar.itemProtect": "Beskyt", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "Gemmer status", "SSE.Views.Statusbar.itemSum": "Sum", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON> farve", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Et ark med dette navn eksiterer allerede.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Et arknavn kan ikke indeholde følgende tegn: \\ / *? []:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Ark navn", "SSE.Views.Statusbar.selectAllSheets": "Væ<PERSON>g alle ark", "SSE.Views.Statusbar.sheetIndexText": "Ark {0} af {1}", "SSE.Views.Statusbar.textAverage": "Gennemsnit", "SSE.Views.Statusbar.textCount": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Brugerdefineret farve", "SSE.Views.Statusbar.textNoColor": "Ingen farve", "SSE.Views.Statusbar.textSum": "SUM", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipFirst": "Rul til første ark", "SSE.Views.Statusbar.tipLast": "Rul til sidste ark", "SSE.Views.Statusbar.tipNext": "Rulle ark liste højre", "SSE.Views.Statusbar.tipPrev": "Rulle ark liste venstre", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "Zoom ind", "SSE.Views.Statusbar.tipZoomOut": "Zoom ud", "SSE.Views.Statusbar.ungroupSheets": "Fjern gruppen af ark", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Handlingen kunne ikke gennemføres for de valgte celler.<br>Vælg et ensartet datainterval forskelligt fra det eksisterende og prøv igen.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Handlingen kunne ikke gennemføres for den valgte rækkevidde.<br>Vælg en rækkevidde så den første tabelrække er på samme række<br>og den resulterende tabel overlapper den nuværende. ", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Handlingen kunne ikke gennemføres for den valgte celle rækkevidde.<br>Vælg en rækkevidde som ikke inkluderer andre tabeller. ", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Multi-felt matrix formler tillades ikke i tabeller", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON> felt skal udfyldes", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON> tabel", "SSE.Views.TableOptionsDialog.txtInvalidRange": "FEJL! Ugyldig cellerækkevidde", "SSE.Views.TableOptionsDialog.txtNote": "Overskrifter skal blive i samme række og det resulterende tabel-interval, skal overlappe det oprindelige tabel-interval.", "SSE.Views.TableOptionsDialog.txtTitle": "Titel", "SSE.Views.TableSettings.deleteColumnText": "Slet kolonne", "SSE.Views.TableSettings.deleteRowText": "Slet række", "SSE.Views.TableSettings.deleteTableText": "Slet tabel", "SSE.Views.TableSettings.insertColumnLeftText": "Indsæt venstre kolonne", "SSE.Views.TableSettings.insertColumnRightText": "Indsæt højre kolonne", "SSE.Views.TableSettings.insertRowAboveText": "Indsæt række over", "SSE.Views.TableSettings.insertRowBelowText": "Indsæt række under", "SSE.Views.TableSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> hele kolonne", "SSE.Views.TableSettings.selectDataText": "Vælg kolonne data", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON> ræk<PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabel", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textAdvanced": "Vis avance<PERSON>e in<PERSON>", "SSE.Views.TableSettings.textBanded": "Sammensluttet", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Konverter til rækkevidde", "SSE.Views.TableSettings.textEdit": "Rækker og kolonner", "SSE.Views.TableSettings.textEmptyTemplate": "Ingen skabeloner", "SSE.Views.TableSettings.textExistName": "FEJL! En rækkevidde med dette navn eksisterer allerede", "SSE.Views.TableSettings.textFilter": "Filter knap", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "FEJL! Ugyldigt tabel navn", "SSE.Views.TableSettings.textIsLocked": "Elementet bliver redigeret af en anden bruger.", "SSE.Views.TableSettings.textLast": "sidste", "SSE.Views.TableSettings.textLongOperation": "Lang operation", "SSE.Views.TableSettings.textPivot": "Indsæt pivottabel", "SSE.Views.TableSettings.textRemDuplicates": "Fjern duplikater", "SSE.Views.TableSettings.textReservedName": "Navnet du forsøger at bruge er allerede refereret i en celle formular. Benyt venligst et andet navn.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON><PERSON><PERSON> på tabel", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Vælg data", "SSE.Views.TableSettings.textSlicer": "Indsæt slicer", "SSE.Views.TableSettings.textTableName": "Tabel navn", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>g fra skabelon", "SSE.Views.TableSettings.textTotal": "I alt", "SSE.Views.TableSettings.warnLongOperation": "<PERSON>lingen du er ved at udføre kan tage en del tid at udføre.<br><PERSON>r du sikker på at du vil fortsætte?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternativ tekst", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Beskrivelse", "SSE.Views.TableSettingsAdvanced.textAltTip": "Den alternative tekstbaserede repræsentation af det visuelle objekt, som vil blive læst til folk med syns- eller læringsudfordringer for at hjælpe dem til at forstå den information der kan findes i et billede, autoshape, diagram eller tabel", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.TableSettingsAdvanced.textTitle": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strBackground": "Baggrundsfarve", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "Forgrundsfarve", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strType": "Type", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "Den indtastede værdi er ikke korrekt.<br>venligst indtast en numerisk værdi mellem 0 pt og 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Farvefyld", "SSE.Views.TextArtSettings.textDirection": "Retning", "SSE.Views.TextArtSettings.textEmptyPattern": "Intet mønster", "SSE.Views.TextArtSettings.textFromFile": "Fra fil", "SSE.Views.TextArtSettings.textFromUrl": "Fra URL", "SSE.Views.TextArtSettings.textGradient": "Gradientpunkter", "SSE.Views.TextArtSettings.textGradientFill": "Gradient udfyldning", "SSE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON> eller struktur", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Intet fyld", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Placering", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "Stræk", "SSE.Views.TextArtSettings.textStyle": "Stilart", "SSE.Views.TextArtSettings.textTemplate": "Skabelon", "SSE.Views.TextArtSettings.textTexture": "<PERSON>a struktur", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "Transformer", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Tilfø<PERSON>", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "SSE.Views.TextArtSettings.txtCanvas": "læ<PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Sort stof", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "Ingen linie", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "Træ", "SSE.Views.Toolbar.capBtnAddComment": "Tilføj kommentar", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "Kommentar", "SSE.Views.Toolbar.capBtnInsHeader": "Sidehoved/sidefod", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientering", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Printområ<PERSON>", "SSE.Views.Toolbar.capBtnPrintTitles": "Print Titler", "SSE.Views.Toolbar.capBtnScale": "<PERSON><PERSON><PERSON><PERSON> til at passe", "SSE.Views.Toolbar.capImgAlign": "Tilpas", "SSE.Views.Toolbar.capImgBackward": "<PERSON><PERSON>", "SSE.Views.Toolbar.capImgForward": "Ryk frem", "SSE.Views.Toolbar.capImgGroup": "Gruppe", "SSE.Views.Toolbar.capInsertChart": "Diagram", "SSE.Views.Toolbar.capInsertEquation": "Formel", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Form", "SSE.Views.Toolbar.capInsertSpark": "Mini diagram", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Tekstboks", "SSE.Views.Toolbar.mniImageFromFile": "Billede fra fil", "SSE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON> fra opbevaring", "SSE.Views.Toolbar.mniImageFromUrl": "Billede fra URL", "SSE.Views.Toolbar.textAddPrintArea": "Tilføj til print-område", "SSE.Views.Toolbar.textAlignBottom": "Tilpas knap", "SSE.Views.Toolbar.textAlignCenter": "Tilpas til midten", "SSE.Views.Toolbar.textAlignJust": "beret<PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "Tilpas til venstre", "SSE.Views.Toolbar.textAlignMiddle": "Tilpas til midten", "SSE.Views.Toolbar.textAlignRight": "Tilpas til højre", "SSE.Views.Toolbar.textAlignTop": "Tilpas til toppen", "SSE.Views.Toolbar.textAllBorders": "<PERSON>e grænser", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatisk", "SSE.Views.Toolbar.textBold": "Fed", "SSE.Views.Toolbar.textBordersColor": "Rammefarve", "SSE.Views.Toolbar.textBordersStyle": "Grænsestil", "SSE.Views.Toolbar.textBottom": "Nederst:", "SSE.Views.Toolbar.textBottomBorders": "Bundgrænser", "SSE.Views.Toolbar.textCenterBorders": "Indsæt lodrette rammer", "SSE.Views.Toolbar.textClearPrintArea": "<PERSON><PERSON>", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textClockwise": "<PERSON>kel med uret", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCounterCw": "<PERSON>kel mod uret", "SSE.Views.Toolbar.textDataBars": "Datalinjer", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON> celler til venstre", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON> celler op", "SSE.Views.Toolbar.textDiagDownBorder": "Diagonal nedad ramme", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonal opad ramme", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON> kolo<PERSON>n", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON>", "SSE.Views.Toolbar.textFewPages": "sider", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON> tekst", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON> celler <PERSON>", "SSE.Views.Toolbar.textInsideBorders": "<PERSON>ds<PERSON><PERSON> rammer", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON> celler til højre", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLandscape": "Landskab", "SSE.Views.Toolbar.textLeft": "Venstre: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON> ramme", "SSE.Views.Toolbar.textManageRule": "Administ<PERSON> regler", "SSE.Views.Toolbar.textManyPages": "sider", "SSE.Views.Toolbar.textMarginsLast": "Sidste brugerdefinerede", "SSE.Views.Toolbar.textMarginsNarrow": "Smal", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Bredde", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON> van<PERSON>tte rammer", "SSE.Views.Toolbar.textMoreFormats": "Flere formatter", "SSE.Views.Toolbar.textMorePages": "Flere sider", "SSE.Views.Toolbar.textNewColor": "Brugerdefineret farve", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "Ingen rammer", "SSE.Views.Toolbar.textOnePage": "Side", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON> rammer", "SSE.Views.Toolbar.textPageMarginsCustom": "Tilpass<PERSON> margener", "SSE.Views.Toolbar.textPortrait": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "Print", "SSE.Views.Toolbar.textPrintOptions": "Udskrift indstillinger", "SSE.Views.Toolbar.textRight": "Højre:", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON><PERSON> rammer", "SSE.Views.Toolbar.textRotateDown": "Roter tekst nedad", "SSE.Views.Toolbar.textRotateUp": "Roter tekst op", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Brugerdefineret", "SSE.Views.Toolbar.textSelection": "Fra det aktuelle udvalg", "SSE.Views.Toolbar.textSetPrintArea": "<PERSON><PERSON>", "SSE.Views.Toolbar.textStrikeout": "Strikeout", "SSE.Views.Toolbar.textSubscript": "Subscript", "SSE.Views.Toolbar.textSubSuperscript": "Subscript/Superscript", "SSE.Views.Toolbar.textSuperscript": "Superscript", "SSE.Views.Toolbar.textTabCollaboration": "Samarbejde", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabFile": "Fil", "SSE.Views.Toolbar.textTabFormula": "Formular", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "Indsæt", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "Beskyttelse", "SSE.Views.Toolbar.textTabView": "V<PERSON><PERSON>", "SSE.Views.Toolbar.textThisPivot": "\n<PERSON>a denne pivot", "SSE.Views.Toolbar.textThisSheet": "<PERSON>a <PERSON><PERSON> a<PERSON>", "SSE.Views.Toolbar.textThisTable": "<PERSON>a denne tabel", "SSE.Views.Toolbar.textTop": "Top:", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON> rammer", "SSE.Views.Toolbar.textUnderline": "Understreg", "SSE.Views.Toolbar.textVertical": "Lodret tekst", "SSE.Views.Toolbar.textWidth": "Bredde", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Tilpas knap", "SSE.Views.Toolbar.tipAlignCenter": "Tilpas til midten", "SSE.Views.Toolbar.tipAlignJust": "beret<PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "Tilpas til venstre", "SSE.Views.Toolbar.tipAlignMiddle": "Tilpas til midten", "SSE.Views.Toolbar.tipAlignRight": "Tilpas til højre", "SSE.Views.Toolbar.tipAlignTop": "Tilpas til toppen", "SSE.Views.Toolbar.tipAutofilter": "Sortèr og filtrer", "SSE.Views.Toolbar.tipBack": "Tilbage", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Cell Style", "SSE.Views.Toolbar.tipChangeChart": "Skift diagramtype", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Skift farveskema", "SSE.Views.Toolbar.tipCondFormat": "Betinget formatering", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "Kopier formatering", "SSE.Views.Toolbar.tipDecDecimal": "Formindsk decimaler", "SSE.Views.Toolbar.tipDecFont": "Formindsk skriftstørrelsen", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON> celler", "SSE.Views.Toolbar.tipDigStyleAccounting": "Regnskabsstil", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON> stil", "SSE.Views.Toolbar.tipDigStylePercent": "Procent stil", "SSE.Views.Toolbar.tipEditChart": "Rediger diagram", "SSE.Views.Toolbar.tipEditChartData": "Vælg data", "SSE.Views.Toolbar.tipEditChartType": " Skift diagramtype", "SSE.Views.Toolbar.tipEditHeader": "Rediger header eller sidefod", "SSE.Views.Toolbar.tipFontColor": "Skriftfarve", "SSE.Views.Toolbar.tipFontName": "Skrifttype", "SSE.Views.Toolbar.tipFontSize": "Skriftstørrelse", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON> obje<PERSON>", "SSE.Views.Toolbar.tipImgGroup": "<PERSON><PERSON> obje<PERSON><PERSON>", "SSE.Views.Toolbar.tipIncDecimal": "Forøg decimal", "SSE.Views.Toolbar.tipIncFont": "Forøg skriftstørrel<PERSON>", "SSE.Views.Toolbar.tipInsertChart": "Indsæt diagram", "SSE.Views.Toolbar.tipInsertChartSpark": "Indsæt diagram", "SSE.Views.Toolbar.tipInsertEquation": "Indsæt ligning", "SSE.Views.Toolbar.tipInsertHyperlink": "Tilføj Hyperlink", "SSE.Views.Toolbar.tipInsertImage": "Indsæt billede", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON><PERSON> celler", "SSE.Views.Toolbar.tipInsertShape": "Indsæt automatisk form", "SSE.Views.Toolbar.tipInsertSlicer": "Indsæt slicer", "SSE.Views.Toolbar.tipInsertSpark": "Indsæt mini tabel", "SSE.Views.Toolbar.tipInsertSymbol": "Indsæt symbol", "SSE.Views.Toolbar.tipInsertTable": "Indsæt tabel", "SSE.Views.Toolbar.tipInsertText": "Indsæt tekstboks", "SSE.Views.Toolbar.tipInsertTextart": "Indsæt Text art", "SSE.Views.Toolbar.tipMerge": "Sammenlæg og centrér", "SSE.Views.Toolbar.tipNumFormat": "Tal format", "SSE.Views.Toolbar.tipPageMargins": "<PERSON> margener", "SSE.Views.Toolbar.tipPageOrient": "Sideorientering", "SSE.Views.Toolbar.tipPageSize": "Side størrelse", "SSE.Views.Toolbar.tipPaste": "Indsæt", "SSE.Views.Toolbar.tipPrColor": "Udfyldningsfarve", "SSE.Views.Toolbar.tipPrint": "Print", "SSE.Views.Toolbar.tipPrintArea": "Printområ<PERSON>", "SSE.Views.Toolbar.tipPrintTitles": "Print titler", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "Gem", "SSE.Views.Toolbar.tipSaveCoauth": "Gem dine ændringer så de andre brugere kan se dem.", "SSE.Views.Toolbar.tipScale": "<PERSON><PERSON><PERSON><PERSON> til at passe", "SSE.Views.Toolbar.tipSendBackward": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSendForward": "Ryk frem", "SSE.Views.Toolbar.tipSynchronize": "Dokumentet er blevet ændret af en anden bruger. Venligst tryk for at gemme dine ændringer og genindlæs opdateringerne. ", "SSE.Views.Toolbar.tipTextOrientation": "Orientering", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>t", "SSE.Views.Toolbar.txtAccounting": "Regnskab", "SSE.Views.Toolbar.txtAdditional": "Ekstra", "SSE.Views.Toolbar.txtAscending": "stigende", "SSE.Views.Toolbar.txtAutosumTip": "Summering", "SSE.Views.Toolbar.txtClearAll": "Alle", "SSE.Views.Toolbar.txtClearComments": "kommentarer", "SSE.Views.Toolbar.txtClearFilter": "Ren<PERSON> filter", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Funktion", "SSE.Views.Toolbar.txtClearHyper": "Hyperlinks", "SSE.Views.Toolbar.txtClearText": "Tekst", "SSE.Views.Toolbar.txtCurrency": "Valuta", "SSE.Views.Toolbar.txtCustom": "Brugerdefineret", "SSE.Views.Toolbar.txtDate": "Da<PERSON>", "SSE.Views.Toolbar.txtDateTime": "<PERSON>to og tid", "SSE.Views.Toolbar.txtDescending": "Aftagende", "SSE.Views.Toolbar.txtDollar": "$ Dollar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Eksponentiel ", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Indsæt funktion", "SSE.Views.Toolbar.txtFraction": "Fraktion", "SSE.Views.Toolbar.txtFranc": "CHF schweiziske franc", "SSE.Views.Toolbar.txtGeneral": "General ", "SSE.Views.Toolbar.txtInteger": "Integer", "SSE.Views.Toolbar.txtManageRange": "Navneh<PERSON>ndtering", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON> over for", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON> celler", "SSE.Views.Toolbar.txtMergeCenter": "<PERSON><PERSON> og centrer", "SSE.Views.Toolbar.txtNamedRange": "Navngivne rækkevidder", "SSE.Views.Toolbar.txtNewRange": "Definer navn", "SSE.Views.Toolbar.txtNoBorders": "Ingen rammer", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Indsæt navn", "SSE.Views.Toolbar.txtPercentage": "Procent", "SSE.Views.Toolbar.txtPound": "£ Pund", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON>le", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "median", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Opulent", "SSE.Views.Toolbar.txtScheme14": "Oriel", "SSE.Views.Toolbar.txtScheme15": "Oprindelse", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Solhverv", "SSE.Views.Toolbar.txtScheme18": "Teknik", "SSE.Views.Toolbar.txtScheme19": "Trek", "SSE.Views.Toolbar.txtScheme2": "Gråtoner", "SSE.Views.Toolbar.txtScheme20": "Urban", "SSE.Views.Toolbar.txtScheme21": "Verve", "SSE.Views.Toolbar.txtScheme22": "Nyt Office", "SSE.Views.Toolbar.txtScheme3": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme4": "aspekt", "SSE.Views.Toolbar.txtScheme5": "Borgerlig", "SSE.Views.Toolbar.txtScheme6": "<PERSON>mmen<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme7": "Egenkapital", "SSE.Views.Toolbar.txtScheme8": "Flow", "SSE.Views.Toolbar.txtScheme9": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "Videnskabelig", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.txtSortAZ": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSpecial": "Speciel", "SSE.Views.Toolbar.txtTableTemplate": "Format som tabelskabelon", "SSE.Views.Toolbar.txtText": "Tekst", "SSE.Views.Toolbar.txtTime": "Tid", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON><PERSON> celler", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "Vis", "SSE.Views.Top10FilterDialog.txtBottom": "Bund", "SSE.Views.Top10FilterDialog.txtBy": "ved", "SSE.Views.Top10FilterDialog.txtItems": "Element", "SSE.Views.Top10FilterDialog.txtPercent": "Procent", "SSE.Views.Top10FilterDialog.txtSum": "Sum", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 AutoFilter", "SSE.Views.Top10FilterDialog.txtTop": "Top", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top-10 filter", "SSE.Views.ValueFieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Gennemsnitlig", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Basisfelt", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Basisgenstand", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 af %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON> tal", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Brugerdefineret navn", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Forskellen fra", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Ingen udregning", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Procent af", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Procentforskel fra", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Procent af kolonne", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Procent af Total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "<PERSON><PERSON> a<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Kører i alt", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Vis værdier som", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Kildenavn:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Sum", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Opsummer værdifelt ved", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Was", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Luk", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Slet", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "Ingen visninger er lavet endnu.", "SSE.Views.ViewManagerDlg.textGoTo": "Go til visning", "SSE.Views.ViewManagerDlg.textLongName": "Indtast et navn på mindre end 128 bogstaver.", "SSE.Views.ViewManagerDlg.textNew": "Ny", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "Navn på visning må ikke være tomt.", "SSE.Views.ViewManagerDlg.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> visning", "SSE.Views.ViewManagerDlg.textViews": "Arkvisninger", "SSE.Views.ViewManagerDlg.tipIsLocked": "Dette element redigeres af en anden bruger.", "SSE.Views.ViewManagerDlg.txtTitle": "Opsæt arkvisning", "SSE.Views.ViewManagerDlg.warnDeleteView": "<PERSON> at slette den aktiverede visning '%1'.<br><PERSON><PERSON> denne visning og slet den?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON><PERSON> paneler", "SSE.Views.ViewTab.capBtnSheetView": "Arkvisning", "SSE.Views.ViewTab.textClose": "Luk", "SSE.Views.ViewTab.textCreate": "Ny", "SSE.Views.ViewTab.textDefault": "Standard", "SSE.Views.ViewTab.textFormula": "<PERSON>els<PERSON>j<PERSON>", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON> første kolonne", "SSE.Views.ViewTab.textFreezeRow": "<PERSON><PERSON> <PERSON><PERSON> række", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Interface tema", "SSE.Views.ViewTab.textManager": "Administrér visning", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON><PERSON> paneler op", "SSE.Views.ViewTab.textZeros": "Vis nuller", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Luk ark-visning", "SSE.Views.ViewTab.tipCreate": "<PERSON>ret ark-visning", "SSE.Views.ViewTab.tipFreeze": "<PERSON>s paneler", "SSE.Views.ViewTab.tipInterfaceTheme": "Interface tema", "SSE.Views.ViewTab.tipSheetView": "Arkvisning", "SSE.Views.WatchDialog.textBook": "Bog", "SSE.Views.WBProtection.hintAllowRanges": "<PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectSheet": "Beskyt ark", "SSE.Views.WBProtection.hintProtectWB": "Beskyt projektmappe", "SSE.Views.WBProtection.txtAllowRanges": "<PERSON><PERSON>", "SSE.Views.WBProtection.txtHiddenFormula": "Skjulte formler", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON>st celle", "SSE.Views.WBProtection.txtLockedShape": "Form låst", "SSE.Views.WBProtection.txtLockedText": "<PERSON><PERSON><PERSON> tekst", "SSE.Views.WBProtection.txtProtectSheet": "Beskyt ark", "SSE.Views.WBProtection.txtProtectWB": "Beskyt projektmappe", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Indtast en adgangskode for at fjerne beskyttelsen af ​​arket", "SSE.Views.WBProtection.txtSheetUnlockTitle": "<PERSON><PERSON><PERSON> be<PERSON>ttelse af ark", "SSE.Views.WBProtection.txtWBUnlockDescription": "Indtast en adgangskode for at fjerne beskyttelsen af ​​projektmappen", "SSE.Views.WBProtection.txtWBUnlockTitle": "ubeskyt projektmappe"}