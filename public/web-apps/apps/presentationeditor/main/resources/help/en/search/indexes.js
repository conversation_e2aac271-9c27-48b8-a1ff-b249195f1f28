var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "About the Presentation Editor", 
        "body": "The Presentation Editor is an online application that lets you look through and edit presentations directly in your browser . Using the Presentation Editor, you can perform various editing operations like in any desktop editor, print the edited presentations keeping all the formatting details or download them onto the hard disk drive of your computer as PPTX, PDF, ODP, POTX, PDF/A, OTP files. To view the current software version, build number, and licensor details in the online version, click the icon on the left sidebar. To view the current software version and licensor details in the desktop version for Windows, select the About menu item on the left sidebar of the main program window. In the desktop version for Mac OS, open the ONLYOFFICE menu at the top of the screen and select the About ONLYOFFICE menu item."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Advanced Settings of the Presentation Editor", 
        "body": "The Presentation Editor allows you to change its advanced settings. To access them, open the File tab on the top toolbar and select the Advanced Settings option. The advanced settings are grouped as follows: Editing and saving Autosave is used in the online version to turn on/off automatic saving of changes you make while editing. Autorecover is used in the desktop version to turn on/off the option that allows you to automatically recover presentations if the program closes unexpectedly. Show the Paste Options button when the content is pasted. The corresponding icon will appear when you paste content in the presentation. Collaboration The Co-editing mode subsection allows you to set the preferable mode for seeing changes made to the presentation when working in collaboration. Fast (by default). The users who take part in the presentation co-editing will see the changes in real time once they are made by other users. Strict. All the changes made by co-editors will be shown only after you click the Save icon that will notify you about new changes. Show changes from other users. This feature allows to see changes made by other users in the presentation opened for viewing only in the Live Viewer mode. Proofing The Spell Checking option is used to turn on/off the spell checking. Ignore words in UPPERCASE. Words typed in capital letters are ignored during the spell checking. Ignore words with numbers. Words with numbers in them are ignored during the spell checking. The AutoCorrect options menu allows you to access the autocorrect settings such as replacing text as you type, recognizing functions, automatic formatting etc. Workspace The Alignment Guides option is used to turn on/off alignment guides that appear when you move objects. It allows for a more precise object positioning on the slide. The Hieroglyphs option is used to turn on/off the display of hieroglyphs. The Use Alt key to navigate the user interface using the keyboard option is used to enable using the Alt / Optionkey in keyboard shortcuts. The Interface theme option is used to change the color scheme of the editor’s interface. The Same as system option makes the editor follow the interface theme of your system. The Light color scheme incorporates standard blue, white, and light gray colors with less contrast in UI elements suitable for working during daytime. The Classic Light color scheme incorporates standard blue, white, and light gray colors. The Dark color scheme incorporates black, dark gray, and light gray colors suitable for working during nighttime. The Contrast Dark color scheme incorporates black, dark gray, and white colors with more contrast in UI elements highlighting the working area of the file. Note: Apart from the available Light, Classic Light, Dark, and Contrast Dark interface themes, ONLYOFFICE editors can now be customized with your own color theme. Please follow these instructions to learn how you can do that. The Unit of Measurement option is used to specify what units are used on the rulers and in properties of objects when setting such parameters as width, height, spacing, margins etc. The available units are Centimeter, Point, and Inch. The Default Zoom Value option is used to set the default zoom value, selecting it in the list of available options from 50% to 500%. You can also choose the Fit to Slide or Fit to Width option. The Font Hinting option is used to select how fonts are displayed in the Presentation Editor. Choose As Windows if you like the way fonts are usually displayed on Windows, i.e. using Windows font hinting. Choose As OS X if you like the way fonts are usually displayed on a Mac, i.e. without any font hinting at all. Choose Native if you want your text to be displayed with the hinting embedded into font files. Default cache mode - used to select the cache mode for the font characters. It’s not recommended to switch it without any reason. It can be helpful in some cases only, for example, when the Google Chrome browser has problems with the enabled hardware acceleration. The Presentation Editor has two cache modes: In the first cache mode, each letter is cached as a separate picture. In the second cache mode, a picture of a certain size is selected where letters are placed dynamically and a mechanism of allocating/removing memory in this picture is also implemented. If there is not enough memory, a second picture is created, etc. The Default cache mode setting applies two above mentioned cache modes separately for different browsers: When the Default cache mode setting is enabled, Internet Explorer (v. 9, 10, 11) uses the second cache mode, other browsers use the first cache mode. When the Default cache mode setting is disabled, Internet Explorer (v. 9, 10, 11) uses the first cache mode, other browsers use the second cache mode. The Macros Settings option is used to set macros display with a notification. Choose Disable All to disable all macros within the presentation. Choose Show Notification to receive notifications about macros within the presentation. Choose Enable All to automatically run all macros within the presentation. To save the changes you made, click the Apply button."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Co-editing presentations in real time", 
        "body": "The Presentation Editor allows you to maintain constant team-wide approach to work flow: share files and folders, communicate right in the editor, comment certain parts of your presentations that require additional third-party input, save presentation versions for future use. In Presentation Editor you can collaborate on presentations in real time using two modes: Fast or Strict. The modes can be selected in the Advanced Settings. It's also possible to choose the necessary mode using the Co-editing Mode icon on the Collaboration tab of the top toolbar: The number of users who are working on the current presentation is specified on the right side of the editor header - . If you want to see who exactly is editing the file now, you can click this icon or open the Chat panel with the full list of the users. Fast mode The Fast mode is used by default and shows the changes made by other users in real time. When you co-edit a presentation in this mode, the possibility to Redo the last undone operation is not available. This mode will show the actions and the names of the co-editors. When a presentation is being edited by several users simultaneously in this mode, the edited objects are marked with dashed lines of different colors. By hovering the mouse cursor over one of the edited passages, the name of the user who is editing it at the moment is displayed. Strict mode The Strict mode is selected to hide changes made by other users until you click the Save   icon to save your changes and accept the changes made by co-authors. When a presentation is being edited by several users simultaneously in the Strict mode, the edited objects (autoshapes, text objects, tables, images, charts) are marked with dashed lines of different colors. The object that you are editing is surrounded by the green dashed line. Red dashed lines indicate that objects are being edited by other users. As soon as one of the users saves their changes by clicking the icon, the others will see a note within the status bar stating that they have updates. To save the changes you made, so that other users can view them, and get the updates saved by your co-editors, click the icon in the left upper corner of the top toolbar. The updates will be highlighted for you to check what exactly has been changed. Live Viewer mode The Live Viewer mode is used to see the changes made by other users in real time when the presentation is opened by a user with the View only access rights. For the mode to function properly, make sure that the Show changes from other users checkbox is active in the editor's Advanced Settings. Anonymous Portal users who are not registered and do not have a profile are considered to be anonymous, although they still can collaborate on documents. To have a name assigned to them, the anonymous user should enter a name they prefer in the corresponding field appearing in the right top corner of the screen when they open the document for the first time. Activate the “Don’t ask me again” checkbox to preserve the name."
    },
   {
        "id": "HelpfulHints/Commenting.htm", 
        "title": "Commenting", 
        "body": "The Presentation Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on presentations in real time, communicate right in the editor, save presentation versions for future use. In Presentation Editor you can leave comments to the content of presentations without actually editing it. Unlike chat messages, the comments stay until deleted. Leaving comments and replying to them To leave a comment to a certain object (text box, shape etc.): select an object where you think there is an error or problem, switch to the Insert or Collaboration tab of the top toolbar and click the Comment button, or right-click the selected object and select the Add Сomment option from the menu, enter the needed text, click the Add Comment/Add button. The object you commented will be marked with the icon. To view the comment, just click on this icon. To add a comment to a certain slide, select the slide and use the Comment button on the Insert or Collaboration tab of the top toolbar. The added comment will be displayed in the upper left corner of the slide. To create a presentation-level comment which is not related to a certain object or slide, click the icon on the left sidebar to open the Comments panel and use the Add Comment to Document link. The presentation-level comments can be viewed on the Comments panel. Comments related to objects and slides are also available here. Any other user can answer to the added comment asking questions or reporting on the work they have done. For this purpose, click the Add Reply link situated under the comment, type in your reply text in the entry field and press the Reply button. If you are using the Strict co-editing mode, new comments added by other users will become visible only after you click the icon in the left upper corner of the top toolbar. Managing comments You can manage the added comments using the icons in the comment balloon or on the Comments panel on the left: sort the added comments by clicking the icon: by date: Newest or Oldest. by author: Author from A to Z or Author from Z to A. by group: All or choose a certain group from the list. This sorting option is available if you are running a version that includes this functionality. edit the currently selected by clicking the icon, delete the currently selected by clicking the icon, close the currently selected discussion by clicking the icon if the task or problem you stated in your comment was solved, after that the discussion you opened with your comment gets the resolved status. To open it again, click the icon, if you want to manage comments in a bunch, open the Resolve drop-down menu on the Collaboration tab. Select one of the options for resolving comments: resolve current comments, resolve my comments or resolve all comments in the presentation. Adding mentions You can only add mentions to the comments made to the presentation content and not to the presentation itself. When entering comments, you can use the mentions feature that allows you to attract somebody's attention to the comment and send a notification to the mentioned user via email and Talk. To add a mention, Enter the \"+\" or \"@\" sign anywhere in the comment text - a list of the portal users will open. To simplify the search process, you can start typing a name in the comment field - the user list will change as you type. Select the necessary person from the list. If the file has not yet been shared with the mentioned user, the Sharing Settings window will open. Read only access type is selected by default. Change it if necessary. Click OK. The mentioned user will receive an email notification that they have been mentioned in a comment. If the file has been shared, the user will also receive a corresponding notification. Removing comments To remove comments, click the Remove button on the Collaboration tab of the top toolbar, select the necessary option from the menu: Remove Current Comments - to remove the currently selected comment. If some replies have been added to the comment, all its replies will be removed as well. Remove My Comments - to remove comments you added without removing comments added by other users. If some replies have been added to your comment, all its replies will be removed as well. Remove All Comments - to remove all the comments in the presentation that you and other users added. To close the panel with comments, click the icon on the left sidebar once again."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Keyboard Shortcuts", 
        "body": "Keyboard Shortcuts for Key Tips Use keyboard shortcuts for a faster and easier access to the features of the Presentation Editor without using a mouse. Press Alt key to turn on all key tips for the editor header, the top toolbar, the right and left sidebars and the status bar. Press the letter that corresponds to the item you wish to use. The additional key tips may appear depending on the key you press. The first key tips hide when additional key tips appear. For example, to access the Insert tab, press Alt to see all primary key tips. Press letter I to access the Insert tab and you will see all the available shortcuts for this tab. Then press the letter that corresponds to the item you wish to configure. Press Alt to hide all key tips, or press Escape to go back to the previous group of key tips. Find the most common keyboard shortcuts in the list below: Windows/Linux Mac OS Working with Presentation Open 'File' panel Alt+F ^ Ctrl+⌥ Option+F Open the File panel to save, download, print the current presentation, view its info, create a new presentation or open an existing one, access the Presentation Editor help or advanced settings. Open 'Search' dialog box Ctrl+F ^ Ctrl+F, &#8984; Cmd+F Open the Search dialog box to start searching for a character/word/phrase in the currently edited presentation. Open 'Comments' panel Ctrl+⇧ Shift+H ^ Ctrl+⇧ Shift+H, &#8984; Cmd+⇧ Shift+H Open the Comments panel to add your own comment or reply to other users' comments. Open comment field Alt+H &#8984; Cmd+⌥ Option+A Open a data entry field where you can add the text of your comment. Open 'Chat' panel Alt+Q ^ Ctrl+⌥ Option+Q Open the Chat panel and send a message. Save presentation Ctrl+S ^ Ctrl+S, &#8984; Cmd+S Save all the changes to the presentation currently edited with the Presentation Editor. The active file will be saved under its current name, in the same location and file format. Print presentation Ctrl+P ^ Ctrl+P, &#8984; Cmd+P Print the presentation with one of the available printers or save it to a file. Download As... Ctrl+⇧ Shift+S ^ Ctrl+⇧ Shift+S, &#8984; Cmd+⇧ Shift+S Open the Download as... panel to save the currently edited presentation to the hard disk drive of your computer in one of the supported formats: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. Full screen F11 Switch to the full screen view to fit the Presentation Editor into your screen. Help menu F1 F1 Open the Presentation Editor Help menu. Open existing file (Desktop Editors) Ctrl+O On the Open local file tab in Desktop Editors, opens the standard dialog box that allows selecting an existing file. Close file (Desktop Editors) Ctrl+W, Ctrl+F4 ^ Ctrl+W, &#8984; Cmd+W Close the current presentation window in Desktop Editors. Element contextual menu ⇧ Shift+F10 ⇧ Shift+F10 Open the selected element contextual menu. Reset the ‘Zoom’ parameter Ctrl+0 ^ Ctrl+0 or &#8984; Cmd+0 Reset the ‘Zoom’ parameter of the current presentation to the default 'Fit to slide' value. Navigation The first slide Home Home, Fn+← Go to the first slide of the currently edited presentation. The last slide End End, Fn+→ Go to the last slide of the currently edited presentation. Next slide Page Down Page Down, Fn+↓ Go to the next slide of the currently edited presentation. Previous slide Page Up Page Up, Fn+↑ Go to the previous slide of the currently edited presentation. Zoom In Ctrl++ ^ Ctrl+=, &#8984; Cmd+= Zoom in the currently edited presentation. Zoom Out Ctrl+- ^ Ctrl+-, &#8984; Cmd+- Zoom out the currently edited presentation. Navigate between controls in modal dialogues ↹ Tab/⇧ Shift+↹ Tab ↹ Tab/⇧ Shift+↹ Tab Navigate between controls to give focus to the next or previous control in modal dialogues. Performing Actions on Slides New slide Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Create a new slide and add it after the selected one in the list. Duplicate slide Ctrl+D &#8984; Cmd+D Duplicate the selected slide in the list. Move slide up Ctrl+↑ &#8984; Cmd+↑ Move the selected slide above the previous one in the list. Move slide down Ctrl+↓ &#8984; Cmd+↓ Move the selected slide below the following one in the list. Move slide to beginning Ctrl+⇧ Shift+↑ &#8984; Cmd+⇧ Shift+↑ Move the selected slide to the very first position in the list. Move slide to end Ctrl+⇧ Shift+↓ &#8984; Cmd+⇧ Shift+↓ Move the selected slide to the very last position in the list. Performing Actions on Objects Create a copy Ctrl + drag, Ctrl+D ^ Ctrl + drag, &#8984; Cmd + drag, ^ Ctrl+D, &#8984; Cmd+D Hold down the Ctrl key when dragging the selected object or press Ctrl+D (&#8984; Cmd+D for Mac) to create its copy. Group Ctrl+G &#8984; Cmd+G Group the selected objects. Ungroup Ctrl+⇧ Shift+G &#8984; Cmd+⇧ Shift+G Ungroup the selected group of objects. Select the next object ↹ Tab ↹ Tab Select the next object after the currently selected one. Select the previous object ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Select the previous object before the currently selected one. Draw straight line or arrow ⇧ Shift + drag (when drawing lines/arrows) ⇧ Shift + drag (when drawing lines/arrows) Draw a straight vertical/horizontal/45-degree line or arrow. Modifying Objects Constrain movement ⇧ Shift + drag ⇧ Shift + drag Constrain the movement of the selected object horizontally or vertically. Set 15-degree-rotation ⇧ Shift + drag (when rotating) ⇧ Shift + drag (when rotating) Constrain the rotation angle to 15 degree increments. Maintain proportions ⇧ Shift + drag (when resizing) ⇧ Shift + drag (when resizing) Maintain the proportions of the selected object when resizing. Movement pixel by pixel Ctrl+← → ↑ ↓ &#8984; Cmd+← → ↑ ↓ Hold down the Ctrl (&#8984; Cmd for Mac) key and use the keybord arrows to move the selected object by one pixel at a time. Working with Tables Move to the next cell in a row ↹ Tab ↹ Tab Go to the next cell in a table row. Move to the previous cell in a row ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Go to the previous cell in a table row. Move to the next row ↓ ↓ Go to the next row in a table. Move to the previous row ↑ ↑ Go to the previous row in a table. Start new paragraph ↵ Enter ↵ Return Start a new paragraph within a cell. Add new row ↹ Tab in the lower right table cell. ↹ Tab in the lower right table cell. Add a new row at the bottom of the table. Previewing Presentation Start preview from the beginning Ctrl+F5 ^ Ctrl+F5 Start a presentation from the beginning. Navigate forward ↵ Enter, Page Down, →, ↓, ␣ Spacebar ↵ Return, Page Down, →, ↓, ␣ Spacebar Display the next transition effect or advance to the next slide. Navigate backward Page Up, ←, ↑ Page Up, ←, ↑ Display the previous transition effect or return to the previous slide. Close preview Esc Esc End a presentation. Undo and Redo Undo Ctrl+Z ^ Ctrl+Z, &#8984; Cmd+Z Reverse the latest performed action. Redo Ctrl+Y ^ Ctrl+Y, &#8984; Cmd+Y Repeat the latest undone action. Cut, Copy, and Paste Cut Ctrl+X, ⇧ Shift+Delete &#8984; Cmd+X Cut the selected object and send it to the computer clipboard memory. The cut object can be later inserted to another place in the same presentation. Copy Ctrl+C, Ctrl+Insert &#8984; Cmd+C Send the selected object to the computer clipboard memory. The copied object can be later inserted to another place in the same presentation. Paste Ctrl+V, ⇧ Shift+Insert &#8984; Cmd+V Insert the previously copied object from the computer clipboard memory to the current cursor position. The object can be previously copied from the same presentation. Insert hyperlink Ctrl+K ^ Ctrl+K, &#8984; Cmd+K Insert a hyperlink which can be used to go to a web address or to a certain slide in the presentation. Copy style Ctrl+⇧ Shift+C ^ Ctrl+⇧ Shift+C, &#8984; Cmd+⇧ Shift+C Copy the formatting from the selected fragment of the currently edited text. The copied formatting can be later applied to another text fragment in the same presentation. Apply style Ctrl+⇧ Shift+V ^ Ctrl+⇧ Shift+V, &#8984; Cmd+⇧ Shift+V Apply the previously copied formatting to the text in the currently edited text box. Selecting with the Mouse Add to the selected fragment ⇧ Shift ⇧ Shift Start the selection, hold down the ⇧ Shift key and click where you need to end the selection. Selecting using the Keyboard Select all Ctrl+A ^ Ctrl+A, &#8984; Cmd+A Select all the slides (in the slides list) or all the objects within the slide (in the slide editing area) or all the text (within the text box) - depending on where the mouse cursor is located. Select text fragment ⇧ Shift+→ ← ⇧ Shift+→ ← Select the text character by character. Select text from cursor to beginning of line ⇧ Shift+Home Select a text fragment from the cursor to the beginning of the current line. Select text from cursor to end of line ⇧ Shift+End Select a text fragment from the cursor to the end of the current line. Select one character to the right ⇧ Shift+→ ⇧ Shift+→ Select one character to the right of the cursor position. Select one character to the left ⇧ Shift+← ⇧ Shift+← Select one character to the left of the cursor position. Select to the end of a word Ctrl+⇧ Shift+→ Select a text fragment from the cursor to the end of a word. Select to the beginning of a word Ctrl+⇧ Shift+← Select a text fragment from the cursor to the beginning of a word. Select one line up ⇧ Shift+↑ ⇧ Shift+↑ Select one line up (with the cursor at the beginning of a line). Select one line down ⇧ Shift+↓ ⇧ Shift+↓ Select one line down (with the cursor at the beginning of a line). Text Styling Bold Ctrl+B ^ Ctrl+B, &#8984; Cmd+B Make the font of the selected text fragment bold giving it a heavier appearance. Italic Ctrl+I ^ Ctrl+I, &#8984; Cmd+I Make the font of the selected text fragment slightly slanted to the right. Underline Ctrl+U ^ Ctrl+U, &#8984; Cmd+U Make the selected text fragment underlined with a line going under the letters. Strikeout Ctrl+5 ^ Ctrl+5, &#8984; Cmd+5 Make the selected text fragment struck out with a line going through the letters. Subscript Ctrl+⇧ Shift+&gt; &#8984; Cmd+⇧ Shift+&gt; Make the selected text fragment smaller placing it to the lower part of the text line, e.g. as in chemical formulas. Superscript Ctrl+⇧ Shift+&lt; &#8984; Cmd+⇧ Shift+&lt; Make the selected text fragment smaller placing it to the upper part of the text line, e.g. as in fractions. Bulleted list Ctrl+⇧ Shift+L ^ Ctrl+⇧ Shift+L, &#8984; Cmd+⇧ Shift+L Create an unordered bulleted list from the selected text fragment or start a new one. Remove formatting Ctrl+␣ Spacebar Remove formatting from the selected text fragment. Increase font Ctrl+] ^ Ctrl+], &#8984; Cmd+] Increase the size of the font for the selected text fragment 1 point. Decrease font Ctrl+[ ^ Ctrl+[, &#8984; Cmd+[ Decrease the size of the font for the selected text fragment 1 point. Align center Ctrl+E Center the text between the left and the right edges. Align justified Ctrl+J Justify the text in the paragraph adding additional space between words so that the left and the right text edges will be aligned with the paragraph margins. Align right Ctrl+R Align right with the text lined up on the right side of the text box, the left side remains unaligned. Align left Ctrl+L Align left with the text lined up on the left side of the text box, the right side remains unaligned. Increase left indent Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Increase the paragraph left indent by one tabulation position. Decrease left indent Ctrl+⇧ Shift+M ^ Ctrl+⇧ Shift+M, &#8984; Cmd+⇧ Shift+M Decrease the paragraph left indent by one tabulation position. Delete one character to the left ← Backspace ← Backspace Delete one character to the left of the cursor. Delete one character to the right Delete Fn+Delete Delete one character to the right of the cursor. Moving around in text Move one character to the left ← ← Move the cursor one character to the left. Move one character to the right → → Move the cursor one character to the right. Move one line up ↑ ↑ Move the cursor one line up. Move one line down ↓ ↓ Move the cursor one line down. Move to the beginning of a word or one word to the left Ctrl+← &#8984; Cmd+← Move the cursor to the beginning of a word or one word to the left. Move one word to the right Ctrl+→ &#8984; Cmd+→ Move the cursor one word to the right. Move to next placeholder Ctrl+↵ Enter ^ Ctrl+↵ Return, &#8984; Cmd+↵ Return Move to the next title or body text placeholder. If it is the last placeholder on a slide, this will insert a new slide with the same slide layout as the original slide Jump to the beginning of the line Home Home Put the cursor to the beginning of the currently edited line. Jump to the end of the line End End Put the cursor to the end of the currently edited line. Jump to the beginning of the text box Ctrl+Home Put the cursor to the beginning of the currently edited text box. Jump to the end of the text box Ctrl+End Put the cursor to the end of the currently edited text box."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "View Settings and Navigation Tools", 
        "body": "The Presentation Editor offers several tools to help you view and navigate through your presentation: zoom, previous/next slide buttons and slide number indicator. Adjust the View Settings To adjust default view settings and set the most convenient mode to work with the presentation, go to the View tab. You can select the following options: Zoom - to set the required zoom value from 50% to 500% from the drop-down list. Fit to Slide - to fit the whole slide to the visible part of the working area. Fit to Width - to fit the slide width to the visible part of the working area. Interface Theme - choose one of the available interface themes from the drop-down menu: Same as system, Light, Classic Light, Dark, Contrast Dark. Notes - when disabled, hides the notes section below the slide. This section can also be hidden/shown by dragging it with the mouse cursor. Rulers - when disabled, hides rulers which are used to set up tab stops and paragraph indents within the text boxes. To show the hidden Rulers, click this option once again. Guides – choose the preferred guide type to properly position objects on the slide. The available options are vertical, horizontal and smart guides for better positioning. Gridlines – choose the preferred grid size from available templates or set a custom one, and whether to snap objects to grid or not, for better object positioning. Always Show Toolbar - when this option is disabled, the top toolbar that contains commands will be hidden while tab names remain visible. Alternatively, you can just double-click any tab to hide the top toolbar or display it again. Status Bar - when disabled, hides the bottommost bar where the Slide Number Indicator and Zoom buttons are located. To show the hidden Status Bar, click this option once again. Left Panel - when disabled, hides the left panel where Search, Slides, Comments, etc. buttons are located. To show the left panel, check this box. Right Panel - when disabled, hides the right panel where Settings are located. To show the right panel, check this box. The right sidebar is minimized by default. To expand it, select any object/slide and click the icon of the currently activated tab on the right. To minimize the right sidebar, click the icon once again. The left sidebar width is adjusted by simple drag-and-drop: move the mouse cursor over the left sidebar border so that it turns into the bidirectional arrow and drag the border to the left to reduce the sidebar width or to the right to extend it. Use the Navigation Tools To navigate through your presentation, use the following tools: The Zoom buttons are situated in the right lower corner and are used to zoom in and out the current presentation. To change the currently selected zoom value that is displayed in percent, click it and select one of the available zoom options from the list (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) or use the Zoom in or Zoom out buttons. Click the Fit to Width icon to fit the slide width to the visible part of the working area. To fit the whole slide to the visible part of the working area, click the Fit to Slide icon. Zoom settings are also available on the View tab. You can set a default zoom value. Switch to the File tab of the top toolbar, go to the Advanced Settings... section, choose the necessary Default Zoom Value from the list and click the Apply button. To go to the previous or next slide when editing the presentation, you can use the and buttons at the top and bottom of the vertical scroll bar located to the right of the slide. The Slide Number Indicator shows the current slide as a part of all the slides in the current presentation (slide 'n' of 'nn'). Click this caption to open the window where you can enter the slide number and quickly go to it. If you decide to hide the Status Bar, this tool will become inaccessible."
    },
   {
        "id": "HelpfulHints/Password.htm", 
        "title": "Protecting presentations with a password", 
        "body": "You can protect your presentations with a password that is required to enter the editing mode by your co-authors. The password can be changed or removed later on. The password cannot be restored if you lose or forget it. Please keep it in a safe place. Setting a password go to the File tab at the top toolbar, choose the Protect option, click the Add password button, set a password in the Password field and repeat it in the Repeat password field below, then click OK. Click to show or hide password characters when entered. Changing a password go to the File tab at the top toolbar, choose the Protect option, click the Change password button, set a password in the Password field and repeat it in the Repeat password field below, then click OK. Deleting a password go to the File tab at the top toolbar, choose the Protect option, click the Delete password button."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Search and Replace Function", 
        "body": "To search for the needed characters, words or phrases in the Presentation Editor, click the icon situated on the left sidebar, the icon situated in the upper right corner, or use the Ctrl+F (Command+F for MacOS) key combination to open the small Find panel or the Ctrl+H key combination to open the full Find panel. A small Find panel will open in the upper right corner of the working area. To access the advanced settings, click the icon. The Find and replace panel will open: Type in your inquiry into the corresponding Find data entry field. If you need to replace one or more occurrences of the found characters, type in the replacement text into the corresponding Replace with data entry field. You can choose to replace a single currently highlighted occurrence or replace all occurrences by clicking the corresponding Replace and Replace All buttons. To navigate between the found occurrences, click one of the arrow buttons. The button shows the next occurrence while the button shows the previous one. Specify search parameters by checking the necessary options below the entry fields: Case sensitive - is used to find only the occurrences typed in the same case as your inquiry (e.g. if your inquiry is 'Editor' and this option is selected, such words as 'editor' or 'EDITOR' etc. will not be found). Whole words only - is used to highlight whole words only. The first slide in the selected direction that contains the characters you entered will be highlighted in the slide list and displayed in the working area with the required characters outlined. If it is not the slide you are looking for, click the selected button again to find the next slide containing the characters you entered."
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Spell-checking", 
        "body": "The Presentation Editor allows you to check the spelling of your text in a certain language and correct mistakes while editing. In the desktop version, it's also possible to add words into a custom dictionary which is common for all three editors. Starting from version 6.3, the ONLYOFFICE editors support the SharedWorker interface for smoother operation without significant memory consumption. If your browser does not support SharedWorker then just Worker will be active. For more information about SharedWorker please refer to this article. First of all, choose a language for your presentation. Click the icon on the right side of the status bar. In the opened window, select the necessary language and click OK. The selected language will be applied to the whole presentation. To choose a different language for any piece of text within the presentation, select the necessary text passage with the mouse and use the menu on the status bar. To enable the spell checking option, you can: click the Spell checking icon at the status bar, or open the File tab of the top toolbar, select the Advanced Settings option, check the Turn on spell checking option box and click the Apply button. Incorrectly spelled words will be underlined with a red line. Right click on the necessary word to activate the menu and: choose one of the suggested similar words spelled correctly to replace the misspelled word with the suggested one. If too many variants are found, the More variants... option appears in the menu; use the Ignore option to skip just that word and remove underlining or Ignore All to skip all the identical words repeated in the text; if the current word is missed in the dictionary, you can add it to the custom dictionary. This word will not be treated as a mistake next time. This option is available in the desktop version. select a different language for this word. To disable the spell checking option, you can: click the Spell checking icon on the status bar, or open the File tab of the top toolbar, select the Advanced Settings option, uncheck the Turn on spell checking option box and click the Apply button."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Supported Formats of Electronic Presentations", 
        "body": "Supported Formats of Electronic Presentation A presentation is a set of slides that may include different types of content such as images, media files, text, effects, etc. The Presentation Editor handles the following presentation formats. While uploading or opening the file for editing, it will be converted to the Office Open XML (PPTX) format. It's done to speed up the file processing and increase the interoperability. The following table contains the formats which can be opened for viewing and/or editing. Formats Description View natively View after conversion to OOXML Edit natively Edit after conversion to OOXML ODP OpenDocument Presentation File format that represents presentations created by Impress application, which is a part of OpenOffice based office suites + + OTP OpenDocument Presentation Template OpenDocument file format for presentation templates. An OTP template contains formatting settings, styles, etc. and can be used to create multiple presentations with the same formatting + + POTX PowerPoint Open XML Document Template Zipped, XML-based file format developed by Microsoft for presentation templates. A POTX template contains formatting settings, styles, etc. and can be used to create multiple presentations with the same formatting + + PPSX Microsoft PowerPoint Slide Show Presentation file format used for slide show playback + + PPT File format used by Microsoft PowerPoint + + PPTX Office Open XML Presentation Zipped, XML-based file format developed by Microsoft for representing spreadsheets, charts, presentations, and word processing documents + + The following table contains the formats in which you can download a presentation from the File -> Download as menu. Input format Can be downloaded as ODP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX OTP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX POTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPSX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPT JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX You can also refer to the conversion matrix on api.onlyoffice.com to see possibility of conversion your presentations into the most known file formats."
    },
   {
        "id": "HelpfulHints/UsingChat.htm", 
        "title": "Communicating in real time", 
        "body": "The Presentation Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on presentations in real time, comment certain parts of your presentations that require additional third-party input, save presentation versions for future use. In Presentation Editor you can communicate with your co-editors in real time using the built-in Chat tool as well as a number of useful plugins, i.e. Telegram or Rainbow. To access the Chat tool and leave a message for other users, click the icon at the left sidebar, enter your text into the corresponding field below, press the Send button. The chat messages are stored during one session only. To discuss the presentation content, it is better to use comments which are stored until they are deleted. All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - . To close the panel with chat messages, click the icon once again."
    },
   {
        "id": "HelpfulHints/VersionHistory.htm", 
        "title": "Version history", 
        "body": "The Presentation Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on presentations in real time, communicate right in the editor, comment certain parts of your presentations that require additional third-party input. In Presentation Editor you can view the version history of the presentation you collaborate on. Viewing version history: To view all the changes made to the presentation, go to the File tab, select the Version History option at the left sidebar or go to the Collaboration tab, open the history of versions using the  Version History icon at the top toolbar. You'll see the list of the presentation versions and revisions with the indication of each version/revision author and creation date and time. For presentation versions, the version number is also specified (e.g. ver. 2). Viewing versions: To know exactly which changes have been made in each separate version/revision, you can view the one you need by clicking it on the left sidebar. The changes made by the version/revision author are marked with the color which is displayed next to the author's name on the left sidebar. To return to the current version of the presentation, use the Close History option on the top of the version list. Restoring versions: If you need to roll back to one of the previous versions of the presentation, click the Restore link below the selected version/revision. To learn more about managing versions and intermediate revisions, as well as restoring previous versions, please read the following article."
    },
   {
        "id": "ProgramInterface/AnimationTab.htm", 
        "title": "Animation tab", 
        "body": "The Animation tab of the Presentation Editor allows you to manage animation effects. You can add animation effects, determine how the animation effects move, and configure other animation effects parameters to customize your presentation. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: Using this tab, you can: select an animation effect, set the appropriate movement parameters for each animation effect, add multiple animations, change the order of the animation effects using Move Earlier or Move Later options, preview the animation effect, set the animation timing options such as duration, delay and repeat options for animation effects, enable and disable the rewind."
    },
   {
        "id": "ProgramInterface/CollaborationTab.htm", 
        "title": "Collaboration tab", 
        "body": "The Collaboration tab in the Presentation Editor allows collaborating on presentations. In the online version, you can share a file, select a co-editing mode and manage comments. In the commenting mode, you can add and remove comments and use the chat. In the desktop version, you can only manage comments. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: Using this tab, you can: specify the sharing settings (available in the online version only), switch between the Strict and Fast co-editing modes (available in the online version only), add comments to your presentation and remove them, open the Chat panel (available in the online version only), track the version history (available in the online version only)."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "File tab", 
        "body": "The File tab in the Presentation Editor allows performing some basic file operations. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: Using this tab, you can: in the online version, save the current file (in case the Autosave option is disabled), download as (save the document in the selected format to the hard disk drive of your computer), save copy as (save a copy of the document in the selected format to the portal documents), print or rename it, in the desktop version, save the current file keeping the current format and location using the Save option or save the current file under a different name and change its location or format using the Save as option, print the file. protect the file using a password, change or remove the password; protect the file using a digital signature (available in the desktop version only); create a new presentation or open a recently edited one (available in the online version only), view general information about the presentation or change some file properties, manage access rights (available in the online version only), access the Advanced Settings of the editor, in the desktop version, open the folder, where the file is stored, in the File Explorer window. In the online version, open the folder of the Documents module, where the file is stored, in a new browser tab."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Home tab", 
        "body": "The Home tab in the Presentation Editor opens by default when you open a presentation. It allows you to set general slide parameters, format text, insert some objects, align and arrange them. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: Using this tab, you can: manage slides and start a slideshow, format text within a text box, insert text boxes, pictures, shapes, align and arrange objects on a slide, copy/clear text formatting, change a theme, color scheme or slide size."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Insert tab", 
        "body": "The Insert tab in the Presentation Editor allows adding visual objects and comments to your presentation. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: Using this tab, you can: insert tables, insert text boxes and Text Art objects, pictures, shapes, charts, insert comments and hyperlinks, insert footers, date and time, slide numbers. insert equations, symbols, insert audio and video records stored on the hard disk drive of your computer (available in the desktop version only, not available for Mac OS). Note: to be able to playback video, you'll need to install codecs, for example, K-Lite."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Plugins tab", 
        "body": "The Plugins tab in the Presentation Editor makes it possible to access the advanced editing features using the available third-party components. Here you can also use macros to simplify routine operations. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: The Settings button allows you to open the window where you can view and manage all installed plugins and add your own ones. The Macros button allows to open the window where you can create your own macros and run them. To learn more about macros, please refer to our API Documentation. Currently, the following plugins are available: Send allows sending the presentation via email using the default desktop mail client (available in the desktop version only), Highlight code allows highlighting the code syntax by selecting the necessary language, style, background color, etc., Photo Editor allows editing images: cropping, flipping, rotating, drawing lines and shapes, adding icons and text, loading a mask and applying filters such as Grayscale, Invert, Sepia, Blur, Sharpen, Emboss, etc., Thesaurus allows finding synonyms and antonyms for the selected word and replacing it with the chosen one, Translator allows translating the selected text into other languages, Note: this plugin doesn't work in Internet Explorer. YouTube allows embedding YouTube videos into your presentation. To learn more about plugins, please refer to our API Documentation. All the currently existing open source plugin examples are available on GitHub."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Introducing the user interface of the Presentation Editor", 
        "body": "The Presentation Editor uses a tabbed interface where editing commands are grouped into tabs according to their functionality. Main window of the Online Presentation Editor: Main window of the Desktop Presentation Editor: The editor interface consists of the following main elements: The Editor header displays the logo, tabs for all opened presentations with their names and menu tabs. On the left side of the Editor header, the Save, Print file, Undo and Redo buttons are located. On the right side of the Editor header, along with the user name the following icons are displayed: Open file location - in the desktop version, it allows opening the folder, where the file is stored, in the File Explorer window. In the online version, it allows opening the folder of the Documents module, where the file is stored, in a new browser tab. Share - (available in the online version only) allows setting access rights for the documents stored in the cloud. Mark as favorite - click the star to add a file to favorites as to make it easy to find. The added file is just a shortcut so the file itself remains stored in its original location. Deleting a file from favorites does not remove the file from its original location. Search - allows to search the presentation for a particular word or symbol, etc. The Top toolbar displays a set of editing commands depending on the selected menu tab. Currently, the following tabs are available: File, Home, Insert, Transitions, Animation, Collaboration, Protection, Plugins. The Copy, Paste, Cut and Select All options are always available on the left side of the Top toolbar regardless of the selected tab. The Status bar at the bottom of the editor window contains the Start slideshow icon, some navigation tools: slide number indicator and zoom buttons. The Status bar also displays some notifications (such as \"All changes saved\", ‘Connection is lost’ when there is no connection and the editor is trying to reconnect etc.) and allows setting the text language and enable spell checking. The Left sidebar contains the following icons: - allows using the Search and Replace tool, - allows viewing slides and navigating them, - allows opening the Comments panel, - (available in the online version only) allows opening the Chat panel, - (available in the online version only) allows contacting our support team, - (available in the online version only) allows viewing the information about the program. The Right sidebar allows adjusting additional parameters of different objects. When you select a particular object on a slide, the corresponding icon is activated on the right sidebar. Click this icon to expand the right sidebar. The horizontal and vertical Rulers help you place objects on a slide and allow you to set up tab stops and paragraph indents within the text boxes. The Working area allows viewing the presentation content, entering and editing data. The Scroll bar on the right allows scrolling the presentation up and down. For your convenience, you can hide some components and display them again when necessary. To learn more on how to adjust the view settings, please refer to this page."
    },
   {
        "id": "ProgramInterface/TransitionsTab.htm", 
        "title": "Transitions tab", 
        "body": "The Transitions tab in the Presentation Editor allows you to manage slide transitions. You can add transition effects, set the transition speed and configure other slide transition parameters to customize your presentation. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: Using this tab, you can: select a transition effect, set appropriate parameter values for each transition effect, define a transition duration, preview a transition after setup, specify how long you want the slide to be displayed by checking the Start on click and Delay options, apply the transition to all slides by clicking the Apply to all Slides button."
    },
   {
        "id": "ProgramInterface/ViewTab.htm", 
        "title": "View tab", 
        "body": "The View tab of the Presentation Editor allows you to manage how your presentation looks like while you are working on it. The corresponding window of the Online Presentation Editor: The corresponding window of the Desktop Presentation Editor: View options available on this tab: Zoom allows to zoom in and zoom out your document. Fit to Slide allows to resize the slide so that the screen displays the whole slide. Fit to Width allows to resize the slide so that the slide scales to fit the width of the screen. Interface Theme allows to change interface theme by choosing a Same as system, Light, Classic Light, Dark or Contrast Dark theme. The following options allow you to configure the elements to display or to hide. Check the elements to make them visible: Notes to make the notes panel always visible. Rulers to make rulers always visible. Guides and Gridlines to properly position objects on the slide. Always Show Toolbar to make the top toolbar always visible. Status Bar to make the status bar always visible. Left Panel to make the left panel visible. Right Panel to make the right panel visible."
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Add hyperlinks", 
        "body": "To add a hyperlink in the Presentation Editor, place the cursor within the text box where a hyperlink should be added, switch to the Insert tab of the top toolbar, click the Hyperlink icon on the top toolbar, after that the Hyperlink Settings window will appear where you can specify the hyperlink parameters: Select a link type you wish to insert: Use the External Link option and enter a URL in the http://www.example.com format in the Link to field below if you need to add a hyperlink leading to an external website. If you need to add a hyperlink to a local file, enter the URL in the file://path/Presentation.pptx (for Windows) or file:///path/Presentation.pptx (for MacOS and Linux) format in the Link to field. The file://path/Presentation.pptx or file:///path/Presentation.pptx hyperlink type can be opened only in the desktop version of the editor. In the web editor you can only add the link without being able to open it. Use the Slide In This Presentation option and select one of the options below if you need to add a hyperlink leading to a certain slide in the same presentation. The following options are available: Next Slide, Previous Slide, First Slide, Last Slide, Slide with the specified number. Display - enter a text that will get clickable and lead to the web address/slide specified in the upper field. ScreenTip text - enter a text that will become visible in a small pop-up window with a brief note or label pertaining to the hyperlink to be pointed. Click the OK button. To add a hyperlink, you can also use the Ctrl+K key combination or click with the right mouse button where a hyperlink should be added and select the Hyperlink option in the right-click menu. Note: it's also possible to select a character, word or word combination with the mouse or using the keyboard and then open the Hyperlink Settings window as described above. In this case, the Display field will be filled with the text fragment you selected. By hovering the cursor over the added hyperlink, the ScreenTip will appear containing the text you specified. You can follow the link by pressing the CTRL key and clicking the link in your presentation. To edit or delete the added hyperlink, click it with the right mouse button, select the Hyperlink option in the right-click menu and then the action you want to perform - Edit Hyperlink or Remove Hyperlink."
    },
   {
        "id": "UsageInstructions/AddingAnimations.htm", 
        "title": "Adding animations", 
        "body": "Animation is a visual effect that allows you to animate text, objects and graphics as to make your presentation more dynamic and emphasize important information. You can control the movement, color and size of the text, objects and graphics. Applying an animation effect switch to the Animation tab on the top toolbar, select a text, an object or a graphic element you want to apply the animation effect to, select an Animation effect from the animations gallery, select the animation effect movement direction by clicking Parameters next to the animations gallery. The parameters on the list depend on the effect you apply. You can preview animation effects on the current slide. By default, animation effects will play automatically when you add them to a slide but you can turn it off. Click the Preview drop-down on the Animation tab, and select a preview mode: Preview to show a preview when you click the Preview button, AutoPreview to show a preview automatically when you add an animation to a slide or replace an existing one. Types of animations All animation effects are listed in the animations gallery. Click the drop-down arrow to open it. Each animation effect is represented by a star-shaped icon. The animations are grouped according to the point at which they occur: Entrance effects determine how objects appear on a slide, and are colored green in the gallery. Emphasis effects change the size or color of the object to add emphasis on an object and to draw attention of the audience, and are colored yellow or two colored in the gallery. Exit effects determine how objects disappear from a slide, and are colored red in the gallery. Motion Paths determines the movement of an object and the path it follows. The icons in the gallery represent the suggested path. The Custom Path option is also available. To learn more, please read the following article. Scroll down the animations gallery to see all effects included in the gallery. If you don’t see the needed animation in the gallery, click the Show More Effects option at the bottom of the gallery. Here you will find the full list of the animation effects. Effects are additionally grouped by the visual impact they have on audience. Entrance, Emphasis, and Exit effects are grouped by Basic, Subtle, Moderate, and Exciting. Motion Path effects are grouped by Basic, Subtle, and Moderate. Applying Multiple Animations You can add more than one animation effect to the same object. To add one more animation, click the Add Animation button on the Animation tab. The list of animation effects will open. Repeat Steps 3 and 4 above for applying an animation. If you use the Animation gallery, and not the Add Animation button, the first animation effect will substitute for a new one. A small square next to the object shows the sequence numbers of the effects applied. As soon as you add several effects to an object, the Multiple animation icon appears in the animations gallery. Changing the order of the animation effects on a slide Click the animation square mark. Сlick or arrows on the Animation tab to change the order of appearance on the slide. Setting animation timing Use the timing options on the Animation tab to set the start, the duration, the delay, the repetition and the rewind of the animations on a slide. Animation Start Options On click – animation starts when you click the slide. This is the default option. With previous – animation starts when previous animation effect starts and effects appear simultaneously. After previous – animation starts right after the previous animation effect. Note: animation effects are automatically numbered on a slide. All animations set to With previous and After previous take the number of the animation they are connected to as they will appear automatically. Animation Trigger Options Click the Trigger button and select one of the appropriate options: On Click Sequence – to start the next animation in sequence each time you click anywhere on the slide. This is the default option. On Click of - to start animation when you click the object that you select from the drop-down list. Other timing options Duration – use this option to determine how long you want an animation to be displayed. Select one of the available options from the menu, or type in the necessary time value. Delay – use this option if you want the selected animation to be displayed within a specified period of time, or if you need a pause between the effects. Use arrows to select the necessary time value, or enter the necessary value measured in seconds. Repeat – use this option if you want to display an animation more than once. Click the Repeat box and select one of the available options, or enter your value. Rewind – check this box if you want to return the object to its original state when the animation ends."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Align and arrange objects on a slide", 
        "body": "In the Presentation Editor, the added autoshapes, images, charts or text boxes can be aligned, grouped, ordered, distributed horizontally and vertically on the slide. To perform any of these actions, first select a separate object or several objects in the slide editing area. To select several objects, hold down the Ctrl key and left-click the necessary objects. To select a text box, click on its border, not the text within it. After that you can use either the icons on the Home tab of the top toolbar described below or the analogous options from the right-click menu. Align objects To align two or more selected objects, Click the Align shape icon on the Home tab of the top toolbar and select one of the following options: Align to Slide to align objects relative to the edges of the slide, Align Selected Objects (this option is selected by default) to align objects relative to each other, Click the Align shape icon once again and select the necessary alignment type from the list: Align Left - to line up the objects horizontally on the left side of the leftmost object/left edge of the slide, Align Center - to line up the objects horizontally in their centers/center of the slide, Align Right - to line up the objects horizontally on the right side of the rightmost object/right edge of the slide, Align Top - to line up the objects vertically to the top edge of the topmost object/top edge of the slide, Align Middle - to line up the objects vertically in their middles/middle of the slide, Align Bottom - to line up the objects vertically to the bottom edge of the bottommost object/bottom edge of the slide. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available alignment options. If you want to align a single object, it can be aligned relative to the edges of the slide. The Align to Slide option is selected by default in this case. Distribute objects To distribute three or more selected objects horizontally or vertically so that the equal distance appears between them, Click the Align icon on the Home tab of the top toolbar and select one of the following options: Align to Slide to distribute objects between the edges of the slide, Align Selected Objects (this option is selected by default) to distribute objects between two outermost selected objects, Click the Align shape icon once again and select the necessary distribution type from the list: Distribute Horizontally - to distribute objects evenly between the leftmost and rightmost selected objects/left and right edges of the slide. Distribute Vertically - to distribute objects evenly between the topmost and bottommost selected objects/top and bottom edges of the slide. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available distribution options. Note: the distribution options are disabled if you select less than three objects. Group objects To group two or more selected objects or ungroup them, click the Arrange shape icon on the Home tab of the top toolbar and select the necessary option from the list: Group - to combine several objects into a group so that they can be simultaneously rotated, moved, resized, aligned, arranged, copied, pasted, formatted like a single object. Ungroup - to ungroup the selected group of the previously combined objects. Alternatively, you can right-click the selected objects, choose the Arrange option from the contextual menu and then use the Group or Ungroup option. Note: the Group option is disabled if you select less than two objects. The Ungroup option is available only when a group of the previously joined objects is selected. Arrange objects To arrange the selected object(s) (i.e. to change their order when several objects overlap each other), click the Arrange shape icon on the Home tab of the top toolbar and select the necessary arrangement type from the list. Bring To Foreground - to move the object(s) in front of all other objects, Send To Background - to move the object(s) behind all other objects, Bring Forward - to move the selected object(s) one level forward as related to other objects. Send Backward - to move the selected object(s) one level backward as related to other objects. Alternatively, you can right-click the selected object(s), choose the Arrange option from the contextual menu and then use one of the available arrangement options."
    },
   {
        "id": "UsageInstructions/ApplyTransitions.htm", 
        "title": "Apply transitions", 
        "body": "A transition is an effect that appears when one slide advances to the next one during presentation. In the Presentation Editor, you can apply the same transition to all slides or different transitions to each separate slide and adjust the transition parameters. To apply a transition to a single slide or several selected slides: Switch to the Transitions tab on the top toolbar. Select a slide (or several slides in the slide list) you want to apply a transition to. Select one of the available transition effects on the Transition tab: None, Fade, Push, Wipe, Split, Uncover, Cover, Clock, Zoom. Click the Parameters button to select one of the available effect options that define exactly how the effect appears. For example, the options available for Zoom effect are Zoom In, Zoom Out and Zoom and Rotate. Specify how long you want the transition to last. In the Duration box, enter or select the necessary time value, measured in seconds. Press the Preview button to view the slide with the transition applied in the slide editing area. Specify how long you want the slide to be displayed until it advances to the next one: Start on click – check this box if you don't want to restrict the time to display the selected slide. The slide will advance to the next one only when you click it with the mouse. Delay – use this option if you want the selected slide to be displayed within a specified period of time until it advances to the next one. Check this box and enter or select the necessary time value, measured in seconds. Note: if you check only the Delay box, the slides will advance automatically within a specified time interval. If you check both the Start on click and the Delay boxes and set the delay value, the slides will advance automatically as well, but you will also be able to click a slide to advance it to the next. To apply a transition to all slides in your presentation, click the Apply to All Slides button on the Transitions tab. To delete a transition, select the necessary slide and choose None among the transition effect options on the Transitions tab. To delete all transitions, select any slide, choose None among the transition effect options and press the Apply to All Slides button on the Transitions tab."
    },
   {
        "id": "UsageInstructions/CommunicationPlugins.htm", 
        "title": "Communicate while editing", 
        "body": "In ONLYOFFICE Presentation Editor, you can always keep in touch with colleagues and use popular online messengers, such as Telegram and Rainbow. Telegram and Rainbow plugins are not installed by default. To find information on how to install them, please, refer to the corresponding article: Adding plugins to the ONLYOFFICE Desktop Editors Adding plugins to ONLYOFFICE Cloud, or Adding new plugins to server editors . Telegram To start chatting in the Telegram plugin, Switch to the Plugins tab and click Telegram, enter your phone number into the corresponding field, check the Keep me signed in checkbox if you want to save credentials for the current session and click the Next button, enter the code you've received in your Telegram app, or log in using the QR code, open Telegram app on your phone, go to Settings > Devices > Scan QR, scan the image to Log in. Now you can use Telegram for instant messaging within ONLYOFFICE editors interface. Rainbow To start chatting in the Rainbow plugin, Switch to the Plugins tab and click Rainbow, register a new account by clicking the Sign up button, or log into an already created one. To do this, enter your email into the corresponding field and click Continue, then enter your account password, check the Keep my session alive checkbox if you want to save credentials for the current session, and click the Connect button. Now you're all set and can simultaneously chat in Rainbow and work within ONLYOFFICE editors interface."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Copy/clear formatting", 
        "body": "To copy a certain text formatting in the Presentation Editor, select the text passage whose formatting you need to copy with the mouse or using the keyboard, click the Copy style icon on the Home tab of the top toolbar (the mouse pointer will look like this ), select the text passage you want to apply the same formatting to. To apply the copied formatting to multiple text passages, select the text passage whose formatting you need to copy with the mouse or using the keyboard, double-click the Copy style icon on the Home tab of the top toolbar (the mouse pointer will look like this and the Copy style icon will remain selected: ), select the necessary text passages one by one to apply the same formatting to each of them, to exit this mode, click the Copy style icon once again or press the Esc key on the keyboard. To quickly remove the formatting that you have applied to a text passage, select the text passage which formatting you want to remove, click the Clear style icon on the Home tab of the top toolbar."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Copy/paste data, undo/redo your actions", 
        "body": "Use basic clipboard operations To cut, copy and paste the selected objects (slides, text passages, autoshapes) in the Presentation Editor or undo/redo your actions, use the corresponding options from the right-click menu, keyboard shortcuts or icons available on any tab of the top toolbar: Cut – select an object and use the Cut option from the right-click menu, or the Cut icon on the top toolbar to delete the selection and send it to the computer clipboard memory. The cut data can be later inserted to another place in the same presentation. Copy – select an object and use the Copy option from the right-click menu or the Copy icon on the top toolbar to copy the selection to the computer clipboard memory. The copied object can be later inserted to another place in the same presentation. Paste – find the place in your presentation where you need to paste the previously copied object and use the Paste option from the right-click menu or the Paste icon on the top toolbar. The object will be inserted to the current cursor position. The object can be previously copied from the same presentation. In the online version, the following key combinations are only used to copy or paste data from/into another presentation or some other program, in the desktop version, both the corresponding buttons/menu options and key combinations can be used for any copy/paste operations: Ctrl+C key combination for copying; Ctrl+V key combination for pasting; Ctrl+X key combination for cutting. Use the Paste Special feature Note: For collaborative editing, the Paste Special feature is available in the Strict co-editing mode only. Once the copied data is pasted, the Paste Special button appears next to the inserted text passage/object. Click this button to select the necessary paste option or use the Ctrl key in combination with the letter key given in the brackets next to the option. When pasting text passages, the following options are available: Use destination theme (Ctrl+H) - allows applying the formatting specified by the theme of the current presentation. This option is used by default. Keep source formatting (Ctrl+K) - allows keeping the source formatting of the copied text. Picture (Ctrl+U) - allows pasting the text as an image so that it cannot be edited. Keep text only (Ctrl+T) - allows pasting the text without its original formatting. When pasting objects (autoshapes, charts, tables), the following options are available: Use destination theme (Ctrl+H) - allows applying the formatting specified by the theme of the current presentation. This option is used by default. Picture (Ctrl+U) - allows pasting the object as an image so that it cannot be edited. To enable / disable the automatic appearance of the Paste Special button after pasting, go to the File tab > Advanced Settings and check / uncheck the Show the Paste Options button when the content is pasted checkbox. Use the Undo/Redo operations To undo/redo your actions, use the corresponding icons on the left side of the editor header or keyboard shortcuts: Undo – use the Undo icon to undo the last operation you performed. Redo – use the Redo icon to redo the last undone operation. You can also use the Ctrl+Z key combination for undoing or Ctrl+Y for redoing. Note: when you co-edit a presentation in the Fast mode, the possibility to Redo the last undone operation is not available."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Create lists", 
        "body": "To create a list in the Presentation Editor, place the cursor where a list should start (this can be a new line or the already entered text), switch to the Home tab of the top toolbar, select the list type you would like to start: Unordered list with markers is created using the Bullets icon situated on the top toolbar Ordered list with digits or letters is created using the Numbering icon situated at the top toolbar Note: click the downward arrow next to the Bullets or Numbering icon to select how the list is going to look like. now each time you press the Enter key at the end of the line, a new ordered or unordered list item will appear. To stop that, press the Backspace key and continue with the common text paragraph. You can also change the text indentation in the lists and their nesting using the Decrease indent and Increase indent icons on the Home tab of the top toolbar. Note: the additional indentation and spacing parameters can be changed on the right sidebar and in the advanced settings window. To learn more about it, read the Insert and format your text section. Change the list settings To change the bulleted or numbered list settings, such as a bullet type, size and color: click an existing list item or select the text you want to format as a list, click the Bullets or Numbering icon on the Home tab of the top toolbar, select the List Settings option, the List Settings window will open. The bulleted list settings window looks like this: Type - allows you to select the necessary character used for the list. When you click the New bullet option, the Symbol window opens, and you can choose one of the available characters. You can also add a new symbol. To learn more on how to work with symbols, please refer to this article. When you click the New image option, a new Import field appears where you can choose new images for bullets From File, From URL, or From Storage. Size - allows you to select the necessary bullet size depending on the current size of the text. It can be a value ranging from 25% to 400%. Color - allows you to select the necessary bullet color. You can select one of the theme colors, or standard colors on the palette, or specify a custom color. The numbered list settings window looks like this: Type - allows you to select the necessary number format used for the list. Size - allows you to select the necessary number size depending on the current size of the text. It can be a value ranging from 25% to 400%. Start at - allows you to select the necessary sequence number a numbered list starts from. Color - allows you to select the necessary number color. You can select one of the theme colors, or standard colors on the palette, or specify a custom color. click OK to apply the changes and close the settings window."
    },
   {
        "id": "UsageInstructions/FillObjectsSelectColor.htm", 
        "title": "Fill objects and select colors", 
        "body": "In the Presentation Editor, you can apply different fills for the slide, autoshape and Text Art font background. Select an object To change the slide background fill, select the necessary slides in the slide list. The Slide settings tab will be activated on the right sidebar. To change the autoshape fill, left-click the necessary autoshape. The Shape settings tab will be activated on the right sidebar. To change the Text Art font fill, left-click the necessary text object. The Text Art settings tab will be activated on the right sidebar. Set the necessary fill type Adjust the selected fill properties (see the detailed description below for each fill type) For the autoshapes and Text Art font, regardless of the selected fill type, you can also set an Opacity level by dragging the slider or entering the percent value manually. The default value is 100%. It corresponds to the full opacity. The 0% value corresponds to the full transparency. The following fill types are available: Color Fill - select this option to specify the solid color to fill the inner space of the selected shape/slide. Click on the colored box below and select the necessary color from the available color sets or specify any color you like: Theme Colors - the colors that correspond to the selected theme/color scheme of the presentation. Once you apply a different theme or color scheme, the Theme Colors set will change. Standard Colors - the default colors set. Custom Color - click on this caption if there is no needed color in the available palettes. Select the necessary color range by moving the vertical color slider and set the specific color by dragging the color picker within the large square color field. Once you select a color with the color picker, the appropriate RGB and sRGB color values will be displayed in the fields on the right. You can also specify a color on the base of the RGB color model by entering the necessary numeric values into the R, G, B (Red, Green, Blue) fields or enter the sRGB hexadecimal code into the field marked with the # sign. The selected color will appear in the New preview box. If the object was previously filled with any custom color, this color is displayed in the Current box so you can compare the original and modified colors. When the color is specified, click the Add button: The custom color will be applied to your object and added to the Custom color palette of the menu. You can use the same color types when selecting the color of the autoshape stroke, adjusting the font color, or changing the table background or border color. Gradient Fill - select this option to fill the slide/shape with two colors which smoothly change from one to another. Click the  Shape settings icon to open the Fill menu: Style - choose one of the available options: Linear (colors change in a straight line i.e. along a horizontal/vertical axis or diagonally at a 45 degree angle) or Radial (colors change in a circular path from the center to the edges). Direction - the direction preview window displays the selected gradient color, click the arrow to choose a template from the menu. If the Linear gradient is selected, the following directions are available : top-left to bottom-right, top to bottom, top-right to bottom-left, right to left, bottom-right to top-left, bottom to top, bottom-left to top-right, left to right. If the Radial gradient is selected, only one template is available. Angle - set the numeric value for a precise color transition angle. Gradient Points are specific points of color transition. Use the Add Gradient Point button or a slider bar to add a gradient point, and the Remove Gradient Point button to delete one. You can add up to 10 gradient points. Each of the following gradient points added does not affect the current gradient appearance. Use the slider bar to change the location of the gradient point or specify the Position in percentage for a precise location. To apply a color to the gradient point, click on the required point on the slider bar, and then click Color to choose the color you want. Picture or Texture - select this option to use an image or a predefined texture as the shape/slide background. If you wish to use an image as a background for the shape/slide, click the Select Picture button and add an image From File by selecting it on your computer hard disc drive, From URL by inserting the appropriate URL address into the opened window, or From Storage by selecting the required image stored on your portal. If you wish to use a texture as a background for the shape/slide, drop-down the From Texture menu and select the necessary texture preset. Currently, the following textures are available: Canvas, Carton, Dark Fabric, Grain, Granite, Grey Paper, Knit, Leather, Brown Paper, Papyrus, Wood. In case the selected Picture has less or more dimensions than the autoshape or slide has, you can choose the Stretch or Tile setting from the drop-down list. The Stretch option allows you to adjust the image size to fit the slide or autoshape size so that it could fill the space completely. The Tile option allows you to display only a part of the bigger image keeping its original dimensions, or repeat the smaller image keeping its original dimensions over the slide or autoshape surface so that it could fill the space completely. Any selected Texture preset fills the space completely, but you can apply the Stretch effect if necessary. Pattern - select this option to fill the slide/shape with a two-colored design composed of regularly repeated elements. Pattern - select one of the predefined designs from the menu. Foreground color - click this color box to change the color of the pattern elements. Background color - click this color box to change the color of the pattern background. No Fill - select this option if you don't want to use any fill."
    },
   {
        "id": "UsageInstructions/HighlightedCode.htm", 
        "title": "Insert highlighted code", 
        "body": "In the Presentation Editor, you can embed highlighted code with the already adjusted style in accordance with the programming language and coloring style of the program you have chosen. Go to your presentation and place the cursor at the location where you want to include the code. Switch to the Plugins tab and choose Highlight code. Specify the programming Language. Select a Style of the code so that it appears as if it were open in this program. Specify if you want to replace tabs with spaces. Choose Background color. To do this, manually move the cursor over the palette or insert the RGB/HSL/HEX value. Click OK to insert the code."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "Insert and format autoshapes", 
        "body": "Insert an autoshape To add an autoshape to a slide in the Presentation Editor, in the slide list on the left, select the slide you want to add the autoshape to, click the Shape icon on the Home tab or the Shape gallery dropdown arrow on the Insert tab of the top toolbar, select one of the available autoshape groups from the Shape Gallery: Recently Used, Basic Shapes, Figured Arrows, Math, Charts, Stars & Ribbons, Callouts, Buttons, Rectangles, Lines, click on the necessary autoshape within the selected group, in the slide editing area, place the mouse cursor where you want the shape to be put, Note: you can click and drag to stretch the shape. once the autoshape is added, you can change its size, position and properties. Note: to add a caption within the autoshape, make sure the shape is selected on the slide and start typing your text. The text you add in this way becomes a part of the autoshape (when you move or rotate the shape, the text moves or rotates with it). It's also possible to add an autoshape to a slide layout. To learn more, please refer to this article. Adjust autoshape settings Some of the autoshape settings can be altered using the Shape settings tab of the right sidebar. To activate it, click the autoshape and choose the Shape settings icon on the right. Here you can change the following properties: Fill - use this section to select the autoshape fill. You can choose the following options: Color Fill - to specify the solid color you want to apply to the selected shape. Gradient Fill - to fill the shape with two colors which smoothly change from one to another. Picture or Texture - to use an image or a predefined texture as the shape background. Pattern - to fill the shape with a two-colored design composed of regularly repeated elements. No Fill - select this option if you don't want to use any fill. For more detailed information on these options, please refer to the Fill objects and select colors section. Line - use this section to change the width, color or type of the autoshape line. To change the line width, select one of the available options from the Size drop-down list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Or select the No Line option if you don't want to use any line. To change the line color, click on the colored box below and select the necessary color. You can use the selected theme color, a standard color or choose a custom color. To change the line type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Rotation is used to rotate the shape by 90 degrees clockwise or counterclockwise as well as to flip the shape horizontally or vertically. Click one of the buttons: to rotate the shape by 90 degrees counterclockwise to rotate the shape by 90 degrees clockwise to flip the shape horizontally (left to right) to flip the shape vertically (upside down) Change Autoshape - use this section to replace the current autoshape with another one selected from the dropdown list. Show shadow - check this option to display shape with shadow. To change the advanced settings of the autoshape, right-click the shape and select the Shape Advanced Settings option from the contextual menu or left-click it and press the Show advanced settings link on the right sidebar. The shape properties window will be opened: The Placement tab allows you to change the autoshape Width and/or Height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original autoshape aspect ratio. You can also set the exact position using the Horizontal and Vertical fields, as well as the From field where you can access such settings as Top Left Corner and Center. The Rotation tab contains the following parameters: Angle - use this option to rotate the shape by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. Flipped - check the Horizontally box to flip the shape horizontally (left to right) or check the Vertically box to flip the shape vertically (upside down). The Weights & Arrows tab contains the following parameters: Line Style - this option allows specifying the following parameters: Cap Type - this option allows setting the style for the end of the line, therefore it can be applied only to the shapes with the open outline, such as lines, polylines, etc.: Flat - the end points will be flat. Round - the end points will be rounded. Square - the end points will be square. Join Type - this option allows setting the style for the intersection of two lines, for example, it can affect a polyline or the corners of the triangle or rectangle outline: Round - the corner will be rounded. Bevel - the corner will be cut off angularly. Miter - the corner will be pointed. It goes well to shapes with sharp angles. Note: the effect will be more noticeable if you use a large outline width. Arrows - this option group is available if a shape from the Lines shape group is selected. It allows you to set the arrow Start and End Style and Size by selecting the appropriate option from the drop-down lists. The Text Box tab contains the following parameters: AutoFit - to change the way text is displayed within the shape: Do not Autofit, Shrink text on overflow, Resize shape to fit text. Text Padding - to change the autoshape Top, Bottom, Left and Right internal margins (i.e., the distance between the text within the shape and the autoshape borders). Note: this tab is only available if text is added within the autoshape, otherwise the tab is disabled. The Columns tab allows adding columns of text within the autoshape specifying the necessary Number of columns (up to 16) and Spacing between columns. Once you click OK, the text that already exists or any other text you enter within the autoshape will appear in columns and will flow from one column to another. The Alternative Text tab allows specifying the Title and Description which will be read to people with vision or cognitive impairments to help them better understand the contents of the shape. To replace the added autoshape, left-click it and use the Change Autoshape drop-down list on the Shape settings tab of the right sidebar. To delete the added autoshape, left-click it and press the Delete key. To learn how to align an autoshape on the slide or arrange several autoshapes, refer to the Align and arrange objects on a slide section. Join autoshapes using connectors You can connect autoshapes using lines with connection points to demonstrate dependencies between the objects (e.g. if you want to create a flowchart). To do that, click the Shape icon on the Home or Insert tab of the top toolbar, select the Lines group from the menu, click the necessary shape within the selected group (excepting the last three shapes which are not connectors, namely Curve, Scribble and Freeform), hover the mouse cursor over the first autoshape and click one of the connection points that appear on the shape outline, drag the mouse cursor towards the second autoshape and click the necessary connection point on its outline. If you move the joined autoshapes, the connector remains attached to the shapes and moves together with them. You can also detach the connector from the shapes and then attach it to any other connection points."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Insert and edit charts", 
        "body": "Insert a chart To insert a chart in the Presentation Editor, Put the cursor where you want to add a chart. Switch to the Insert tab of the top toolbar. Click the Chart icon on the top toolbar. Select the needed chart type from the available ones: Column Charts Clustered column Stacked column 100% stacked column 3-D Clustered Column 3-D Stacked Column 3-D 100% stacked column 3-D Column Line Charts Line Stacked line 100% stacked line Line with markers Stacked line with markers 100% stacked line with markers 3-D Line Pie Charts Pie Doughnut 3-D Pie Bar Charts Clustered bar Stacked bar 100% stacked bar 3-D clustered bar 3-D stacked bar 3-D 100% stacked bar Area Charts Area Stacked area 100% stacked area Stock Charts XY (Scatter) Charts Scatter Stacked bar Scatter with smooth lines and markers Scatter with smooth lines Scatter with straight lines and markers Scatter with straight lines Combo Charts Clustered column - line Clustered column - line on secondary axis Stacked area - clustered column Custom combination Note: ONLYOFFICE Presentation Editor supports the following types of charts that were created with third-party editors: Pyramid, Bar (Pyramid), Horizontal/Vertical Cylinders, Horizontal/Vertical Cones. You can open the file containing such a chart and modify it using the available chart editing tools. After that the Chart Editor window will appear where you can enter the necessary data into the cells using the following controls: and for copying and pasting the copied data and for undoing and redoing actions for inserting a function and for decreasing and increasing decimal places for changing the number format, i.e. the way the numbers you enter appear in cells for choosing a different type of chart. Click the Select Data button situated in the Chart Editor window. The Chart Data window will open. Use the Chart Data dialog to manage Chart Data Range, Legend Entries (Series), Horizontal (Category) Axis Label and Switch Row/Column. Chart Data Range - select data for your chart. Click the icon on the right of the Chart data range box to select data range. Legend Entries (Series) - add, edit, or remove legend entries. Type or select series name for legend entries. In Legend Entries (Series), click Add button. In Edit Series, type a new legend entry or click the icon on the right of the Select name box. Horizontal (Category) Axis Labels - change text for category labels. In Horizontal (Category) Axis Labels, click Edit. In Axis label range, type the labels you want to add or click the icon on the right of the Axis label range box to select data range. Switch Row/Column - rearrange the worksheet data that is configured in the chart not in the way that you want it. Switch rows to columns to display data on a different axis. Click OK button to apply the changes and close the window. Click the Change Chart Type button in the Chart Editor window to choose chart type and style. Select a chart from the available sections: Column, Line, Pie, Bar, Area, Stock, XY (Scatter), or Combo. When you choose Combo Charts, the Chart Type window lists chart series and allows choosing the types of charts to combine and selecting data series to place on a seconary axis. Change the chart settings by clicking the Edit Chart button situated in the Chart Editor window. The Chart - Advanced Settings window will open. The Layout tab allows you to change the layout of chart elements. Specify the Chart Title position in regard to your chart selecting the necessary option from the drop-down list: None not to display the title of a chart, Overlay to overlay and center the title in the plot area, No Overlay to display the title above the plot area. Specify the Legend position in regard to your chart selecting the necessary option from the drop-down list: None not to display a legend, Bottom to display the legend and align it to the bottom of the plot area, Top to display the legend and align it to the top of the plot area, Right to display the legend and align it to the right of the plot area, Left to display the legend and align it to the left of the plot area, Left Overlay to overlay and center the legend to the left on the plot area, Right Overlay to overlay and center the legend to the right on the plot area. Specify the Data Labels (i.e. text labels that represent exact values of data points) parameters: Specify the Data Labels position relative to the data points selecting the necessary option from the drop-down list. The available options vary depending on the selected chart type. For Column/Bar charts, you can choose the following options: None, Center, Inner Bottom, Inner Top, Outer Top. For Line/XY (Scatter)/Stock charts, you can choose the following options: None, Center, Left, Right, Top, Bottom. For Pie charts, you can choose the following options: None, Center, Fit to Width, Inner Top, Outer Top. For Area charts as well as for 3D Column, Line, Bar and Combo charts, you can choose the following options: None, Center. Select the data you wish to include into your labels by checking the corresponding boxes: Series Name, Category Name, Value, Enter a character (comma, semicolon, etc.) you wish to use to separate several labels into the Data Labels Separator entry field. Lines - is used to choose a line style for Line/XY (Scatter) charts. You can choose one of the following options: Straight to use straight lines among data points, Smooth to use smooth curves among data points, or None not to display lines. Markers - is used to specify whether the markers should be displayed (if the box is checked) or not (if the box is unchecked) for Line/XY (Scatter) charts. Note: the Lines and Markers options are available for Line charts and XY (Scatter) charts only. The Vertical Axis tab allows you to change the parameters of the vertical axis also referred to as the values axis or y-axis which displays numeric values. Note that the vertical axis will be the category axis which displays text labels for the Bar charts, therefore in this case the Vertical Axis tab options will correspond to the ones described in the next section. For the XY (Scatter) charts, both axes are value axes. Note: the Axis Settings and Gridlines sections will be disabled for Pie charts since charts of this type have no axes and gridlines. Select Hide to hide vertical axis in the chart, leave it unchecked to have vertical axis displayed. Specify Title orientation by selecting the necessary option from the drop-down list: None to not display a vertical axis title Rotated to display the title from bottom to top to the left of the vertical axis, Horizontal to display the title horizontally to the left of the vertical axis. Minimum Value - is used to specify the lowest value displayed at the vertical axis start. The Auto option is selected by default, in this case the minimum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Maximum Value - is used to specify the highest value displayed at the vertical axis end. The Auto option is selected by default, in this case the maximum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Axis Crosses - is used to specify a point on the vertical axis where the horizontal axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value on the vertical axis. Display Units - is used to determine the representation of the numeric values along the vertical axis. This option can be useful if you're working with great numbers and wish the values on the axis to be displayed in a more compact and readable way (e.g. you can represent 50 000 as 50 by using the Thousands display units). Select desired units from the drop-down list: Hundreds, Thousands, 10 000, 100 000, Millions, 10 000 000, 100 000 000, Billions, Trillions, or choose the None option to return to the default units. Values in reverse order - is used to display values in the opposite direction. When the box is unchecked, the lowest value is at the bottom and the highest value is at the top of the axis. When the box is checked, the values are ordered from top to bottom. The Tick Options section allows adjusting the appearance of tick marks on the vertical scale. Major tick marks are the larger scale divisions which can have labels displaying numeric values. Minor tick marks are the scale subdivisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed if the corresponding option is set on the Layout tab. The Major/Minor Type drop-down lists contain the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. The Label Options section allows adjusting the appearance of major tick mark labels which display values. To specify a Label Position in regard to the vertical axis, select the necessary option from the drop-down list: None to not display tick mark labels, Low to display tick mark labels to the left of the plot area, High to display tick mark labels to the right of the plot area, Next to axis to display tick mark labels next to the axis. To specify a Label Format click the Label Format button and choose a category as it deems appropriate. Available label format categories: General Number Scientific Accounting Currency Date Time Percentage Fraction Text Custom Label format options vary depending on the selected category. For more information on changing number format, go to this page. Check Linked to source to keep number formatting from the data source in the chart. Note: Secondary axes are supported in Combo charts only. Secondary axes are useful in Combo charts when data series vary considerably or mixed types of data are used to plot a chart. Secondary Axes make it easier to read and understand a combo chart. The Secondary Vertical /Horizontal Axis tab appears when you choose an appropriate data series for a combo chart. All the settings and options on the Secondary Vertical/Horizontal Axis tab are the same as the settings on the Vertical/Horizontal Axis. For a detailed description of the Vertical/Horizontal Axis options, see description above/below. The Horizontal Axis tab allows you to change the parameters of the horizontal axis also referred to as the categories axis or x-axis which displays text labels. Note that the horizontal axis will be the value axis which displays numeric values for the Bar charts, therefore in this case the Horizontal Axis tab options will correspond to the ones described in the previous section. For the XY (Scatter) charts, both axes are value axes. Select Hide to hide horizontal axis in the chart, leave it unchecked to have horizontal axis displayed. Specify Title orientation by selecting the necessary option from the drop-down list: None when you don’t want to display a horizontal axis title, No Overlay  to display the title below the horizontal axis, Gridlines is used to specify the Horizontal Gridlines to display by selecting the necessary option from the drop-down list: None,  Major, Minor, or Major and Minor. Axis Crosses - is used to specify a point on the horizontal axis where the vertical axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value (that corresponds to the first and last category) on the horizontal axis. Axis Position - is used to specify where the axis text labels should be placed: On Tick Marks or Between Tick Marks. Values in reverse order - is used to display categories in the opposite direction. When the box is unchecked, categories are displayed from left to right. When the box is checked, the categories are ordered from right to left. The Tick Options section allows adjusting the appearance of tick marks on the horizontal scale. Major tick marks are the larger divisions which can have labels displaying category values. Minor tick marks are the smaller divisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed if the corresponding option is set on the Layout tab. You can adjust the following tick mark parameters: Major/Minor Type - is used to specify the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. Interval between Marks - is used to specify how many categories should be displayed between two adjacent tick marks. The Label Options section allows adjusting the appearance of labels which display categories. Label Position - is used to specify where the labels should be placed in regard to the horizontal axis. Select the necessary option from the drop-down list: None to not display category labels, Low to display category labels at the bottom of the plot area, High to display category labels at the top of the plot area, Next to axis to display category labels next to the axis. Axis Label Distance - is used to specify how closely the labels should be placed to the axis. You can specify the necessary value in the entry field. The more the value you set, the more the distance between the axis and labels is. Interval between Labels - is used to specify how often the labels should be displayed. The Auto option is selected by default, in this case labels are displayed for every category. You can select the Manual option from the drop-down list and specify the necessary value in the entry field on the right. For example, enter 2 to display labels for every other category etc. To specify a Label Format click the Label Format button and choose a category as it deems appropriate. Available label format categories: General Number Scientific Accounting Currency Date Time Percentage Fraction Text Custom Label format options vary depending on the selected category. For more information on changing number format, go to this page. Check Linked to source to keep number formatting from the data source in the chart. The Cell Snapping tab contains the following parameters: Move and size with cells - this option allows you to snap the chart to the cell behind it. If the cell moves (e.g. if you insert or delete some rows/columns), the chart will be moved together with the cell. If you increase or decrease the width or height of the cell, the chart will change its size as well. Move but don't size with cells - this option allows to snap the chart to the cell behind it preventing the chart from being resized. If the cell moves, the chart will be moved together with the cell, but if you change the cell size, the chart dimensions remain unchanged. Don't move or size with cells - this option allows to prevent the chart from being moved or resized if the cell position or size was changed. The Alternative Text tab allows specifying the Title and Description which will be read to people with vision or cognitive impairments to help them better understand the contents of the chart. Once the chart is added, you can also change its size and position. You can specify the chart position on the slide by dragging it vertically or horizontally. You can also add a chart into a text placeholder by pressing the Chart icon within it and selecting the necessary chart type: It's also possible to add a chart to a slide layout. To learn more, please refer to this article. Edit chart elements To edit the chart Title, select the default text with the mouse and type in your own one instead. To change the font formatting within text elements, such as the chart title, axes titles, legend entries, data labels, etc., select the necessary text element by left-clicking it. Then use the corresponding icons on the Home tab of the top toolbar to change the font type, style, size, or color. When the chart is selected, the Shape settings icon is also available on the right, since the shape is used as the background for the chart. You can click this icon to open the Shape settings tab on the right sidebar and adjust the shape Fill, Stroke and Wrapping Style. Note that you cannot change the shape type. Using the Shape Settings tab on the right panel, you can not only adjust the chart area itself, but also change the chart elements, such as plot area, data series, chart title, legend, etc. and apply different fill types to them. Select the chart element by clicking it with the left mouse button and choose the preferred fill type: solid color, gradient, texture or picture, pattern. Specify the fill parameters and set the Opacity level if necessary. When you select a vertical or horizontal axis or gridlines, the stroke settings are only available on the Shape Settings tab: color, width and type. For more details on how to work with shape colors, fills and stroke, please refer to this page. Note: the Show shadow option is also available on the Shape settings tab, but it is disabled for chart elements. If you need to resize chart elements, left-click to select the needed element and drag one of 8 white squares located along the perimeter of the element. To change the position of the element, left-click on it, make sure your cursor changed to , hold the left mouse button and drag the element to the needed position. To delete a chart element, select it by left-clicking and press the Delete key. You can also rotate 3D charts using the mouse. Left-click within the plot area and hold the mouse button. Drag the cursor without releasing the mouse button to change the 3D chart orientation. Adjust chart settings The chart size, type and style as well as the data used to create the chart can be altered using the right sidebar. To activate it, click the chart and choose the Chart settings icon on the right. The Size section allows you to change the chart width and/or height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original chart aspect ratio. The Change Chart Type section allows you to change the type of the selected chart type and/or its style using the corresponding drop-down menu. To select the necessary chart Style, use the second drop-down menu in the Change Chart Type section. The Edit Data button allows you to open the Chart Editor window and start editing data as described above. Note: to quickly open the 'Chart Editor' window, you can also double-click the chart on the slide. Additionally, 3D Rotation settings are available for 3D charts: X rotation - set the required value for the X axis rotation using the keyboard or via the Left and Right arrows to the right. Y rotation - set the required value for the Y axis rotation using the keyboard or via the Up and Down arrows to the right. Perspective - set the required value for depth rotation using the keyboard or via the Narrow field of view and Widen field of view arrows to the right. Right Angle Axis - is used to set the right angle axis view. Autoscale - check this box to autoscale the depth and height values of the chart, or uncheck this box to set the depth and height values manually. Depth (% of base) - set the required depth value using the keyboard or via the arrows. Height (% of base) - set the required height value using the keyboard or via the arrows. Default Rotation - set the 3D parameters to their default. Please note that you cannot edit each element of the chart; the settings will be applied to the chart as a whole. The Show advanced settings option on the right sidebar allows you to open the Chart - Advanced Settings window where you adjust the following parameters: The Placement tab allows you to set the following properties: Size - use this option to change the image width and/or height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original image aspect ratio. Position - set the exact position using the Horizontal and Vertical fields, as well as the From field where you can access such settings as Top Left Corner and Center. The Alternative Text tab allows specifying the Title and Description which will be read to people with vision or cognitive impairments to help them better understand the contents of the chart. To delete the inserted chart, left-click it and press the Delete key. To learn how to align a chart on the slide or arrange several objects, refer to the Align and arrange objects on a slide section."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Insert equations", 
        "body": "The Presentation Editor allows you to create equations using the built-in templates, edit them, insert special characters (including mathematical operators, Greek letters, accents, etc.). Add a new equation To insert an equation from the gallery, put the cursor where you want to place the equation, switch to the Insert tab of the top toolbar, click the arrow next to the Equation icon on the top toolbar, select the required equation category in the toolbar above the inserted equation, or in the opened drop-down list select the equation category you need. The following categories are currently available: Symbols, Fractions, Scripts, Radicals, Integrals, Large Operators, Brackets, Functions, Accents, Limits and Logarithms, Operators, Matrices, click the Equation Settings symbol in the toolbar above the inserted equation to access more settings, e.g., Unicode or LaTeX, Professional or Linear, and Change to Inline, click the certain symbol/equation in the corresponding set of templates. The selected symbol/equation box will be inserted in the center of the current slide. If you do not see the equation box border, click anywhere within the equation - the border will be displayed as a dashed line. The equation box can be freely moved, resized or rotated on the slide. To do that, click on the equation box border (it will be displayed as a solid line) and use the corresponding handles. Each equation template represents a set of slots. A slot is a position for each element that makes up the equation. An empty slot (also called as a placeholder) has a dotted outline . You need to fill in all the placeholders specifying the necessary values. Enter values The insertion point specifies where the next character you enter will appear. To position the insertion point precisely, click within a placeholder and use the keyboard arrows to move the insertion point one character left/right. Once the insertion point is positioned, you can fill in the placeholder: enter the desired numeric/literal value using the keyboard, insert a special character using the Symbols palette from the Equation menu on the Insert tab of the top toolbar or typing them from the keyboard (see the Math AutoСorrect option description), add another equation template from the palette to create a complex nested equation. The size of the primary equation will be automatically adjusted to fit its content. The size of the nested equation elements depends on the primary equation placeholder size, but it cannot be smaller than the sub-subscript size. To add some new equation elements, you can also use the right-click menu options: To add a new argument that goes before or after the existing one within Brackets, you can right-click on the existing argument and select the Insert argument before/after option from the menu. To add a new equation within Cases with several conditions from the Brackets group, you can right-click on an empty placeholder or entered equation within it and select the Insert equation before/after option from the menu. To add a new row or a column in a Matrix, you can right-click on a placeholder within it, select the Insert option from the menu, then select Row Above/Below or Column Left/Right. Note: currently, equations cannot be entered using the linear format, i.e. \\sqrt(4&x^3). When entering the values of mathematical expressions, you do not need to use Spacebar because spaces between the characters and signs of operations are set automatically. If the equation is too long and does not fit to a single line within the text box, automatic line breaking appears while you are typing. You can also insert a line break in a specific position by right-clicking on a mathematical operator and selecting the Insert manual break option from the menu. The selected operator will start a new line. To delete the added manual line break, right-click on the mathematical operator that starts a new line and select the Delete manual break option. Format equations By default, the equation within the text box is horizontally centered and vertically aligned to the top of the text box. To change its horizontal/vertical alignment, put the cursor within the the equation box (the text box borders will be displayed as dashed lines) and use the corresponding icons on the Home tab of the top toolbar. To increase or decrease the equation font size, click anywhere within the equation box and select the necessary font size from the list on the Home tab of the top toolbar. All the equation elements will change correspondingly. The letters within the equation are italicized by default. If necessary, you can change the font style (bold, italic, strikeout) or color for a whole equation or its part. The underlined style can be applied to the entire equation only, not to individual characters. Select the necessary part of the equation by clicking and dragging. The selected part will be highlighted blue. Then use the necessary buttons on the Home tab of the top toolbar to format the selection. For example, you can remove the italic format for ordinary words that are not variables or constants. To modify some equation elements, you can also use the right-click menu options: To change the Fractions format, you can right-click on a fraction and select the Change to skewed/linear/stacked fraction option from the menu (the available options differ depending on the selected fraction type). To change the Scripts position relating to text, you can right-click on the equation that includes scripts and select the Scripts before/after text option from the menu. To change the argument size for Scripts, Radicals, Integrals, Large Operators, Limits and Logarithms, Operators as well as for overbraces/underbraces and templates with grouping characters from the Accents group, you can right-click on the argument you want to change and select the Increase/Decrease argument size option from the menu. To specify whether an empty degree placeholder should be displayed or not for a Radical, you can right-click on the radical and select the Hide/Show degree option from the menu. To specify whether an empty limit placeholder should be displayed or not for an Integral or Large Operator, you can right-click on the equation and select the Hide/Show top/bottom limit option from the menu. To change the limits position relating to the integral or operator sign for Integrals or Large Operators, you can right-click on the equation and select the Change limits location option from the menu. The limits can be displayed to the right of the operator sign (as subscripts and superscripts) or directly above and below the operator sign. To change the limits position relating to text for Limits and Logarithms and templates with grouping characters from the Accents group, you can right-click on the equation and select the Limit over/under text option from the menu. To choose which of the Brackets should be displayed, you can right-click on the expression within them and select the Hide/Show opening/closing bracket option from the menu. To control the Brackets size, you can right-click on the expression within them. The Stretch brackets option is selected by default so that the brackets can grow according to the expression within them, but you can deselect this option to prevent brackets from stretching. When this option is activated, you can also use the Match brackets to argument height option. To change the character position relating to text for overbraces/underbraces or overbars/underbars from the Accents group, you can right-click on the template and select the Char/Bar over/under text option from the menu. To choose which borders should be displayed for a Boxed formula from the Accents group, you can right-click on the equation and select the Border properties option from the menu, then select Hide/Show top/bottom/left/right border or Add/Hide horizontal/vertical/diagonal line. To specify whether empty placeholders should be displayed or not for a Matrix, you can right-click on it and select the Hide/Show placeholder option from the menu. To align some equation elements, you can use the right-click menu options: To align equations within Cases with several conditions from the Brackets group, you can right-click on an equation, select the Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align a Matrix vertically, you can right-click on the matrix, select the Matrix Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align elements within a Matrix column horizontally, you can right-click on a placeholder within the column, select the Column Alignment option from the menu, then select the alignment type: Left, Center, or Right. Delete equation elements To delete a part of the equation, select the part you want to delete by dragging the mouse or holding down the Shift key and using the arrow buttons, then press the Delete key. A slot can only be deleted together with the template it belongs to. To delete the entire equation, click on the equation box border (it will be displayed as a solid line) and and press the Delete key. To delete some equation elements, you can also use the right-click menu options: To delete a Radical, you can right-click on it and select the Delete radical option from the menu. To delete a Subscript and/or Superscript, you can right-click on the expression that contains them and select the Remove subscript/superscript option from the menu. If the expression contains scripts that go before text, the Remove scripts option is available. To delete Brackets, you can right-click on the expression within them and select the Delete enclosing characters or Delete enclosing characters and separators option from the menu. If the expression within Brackets inclides more than one argument, you can right-click on the argument you want to delete and select the Delete argument option from the menu. If Brackets enclose more than one equation (i.e. Cases with several conditions), you can right-click on the equation you want to delete and select the Delete equation option from the menu. To delete a Limit, you can right-click on it and select the Remove limit option from the menu. To delete an Accent, you can right-click on it and select the Remove accent character, Delete char or Remove bar option from the menu (the available options differ depending on the selected accent). To delete a row or a column of a Matrix, you can right-click on the placeholder within the row/column you need to delete, select the Delete option from the menu, then select Delete Row/Column. Convert equations If you open an existing document containing equations which were created with an old version of equation editor (for example, with MS Office versions before 2007), you need to convert these equations to the Office Math ML format to be able to edit them. To convert an equation, double-click it. The warning window will appear: To convert the selected equation only, click the Yes button in the warning window. To convert all equations in this document, check the Apply to all equations box and click Yes. Once the equation is converted, you can edit it."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Insert footers", 
        "body": "Footers allow adding some additional info to a slide, such as date and time, slide number, or a text. To insert a footer in the Presentation Editor: switch to the Insert tab, click the Edit footer button on the top toolbar, the Footer Settings window will open. Check the data you want to add to the footer. The changes are displayed in the preview window on the right. check the Date and time box to insert a date or time in the selected format. The selected date will be added to the left field of the slide footer. Specify the necessary data format: Update automatically - check this radio button if you want to automatically update the date and time according to the current date and time. Then select the necessary date and time Format and Language from the lists. Fixed - check this radio button if you do not want to automatically update the date and time. check the Slide number box to insert the current slide number. The slide number will be added in the right field of the slide footer. check Text in footer box to insert any text. Enter the necessary text in the entry field below. The text will be added in the central field of the slide footer. check the Don't show on the title slide option if necessary, click the Apply to all button to apply changes to all slides or use the Apply button to apply the changes to the current slide only. To quickly insert a date or a slide number to the footer of the selected slide, you can use the Show slide Number and Show Date and Time options on the Slide Settings tab of the right sidebar. In this case, the selected settings will be applied to the current slide only. The date and time or slide number added in such a way can be adjusted later using the Footer Settings window. To edit the added footer, click the Edit footer button on the top toolbar, make the necessary changes in the Footer Settings window, and click the Apply or Apply to All button to save the changes. Insert date and time and slide number into the text box It's also possible to insert date and time or slide number into the selected text box using the corresponding buttons on the Insert tab of the top toolbar. Insert date and time put the mouse cursor within the text box where you want to insert the date and time, click the Date &amp; Time button on the Insert tab of the top toolbar, select the necessary Language from the list and choose the necessary date and time Format in the Date &amp; Time window, if necessary, check the Update automatically box or press the Set as default box to set the selected date and time format as default for the specified language, click the OK button to apply the changes. The date and time will be inserted in the current cursor position. To edit the inserted date and time, select the inserted date and time in the text box, click the Date &amp; Time button on the Insert tab of the top toolbar, choose the necessary format in the Date &amp; Time window, click the OK button. Insert a slide number put the mouse cursor within the text box where you want to insert the slide number, click the Slide Number button on the Insert tab of the top toolbar, check the Slide number box in the Footer Settings window, click the OK button to apply the changes. The slide number will be inserted in the current cursor position."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Insert and adjust images", 
        "body": "Insert an image In the Presentation Editor, you can insert images in the most popular formats into your presentation. The following image formats are supported: BMP, GIF, JPEG, JPG, PNG. To add an image on a slide, in the slide list on the left, select the slide you want to add the image to, click the Image icon on the Home or Insert tab of the top toolbar, select one of the following options to load the image: the Image from File option will open the standard dialog window so that you can choose a file. Browse the hard disk drive your computer to select the necessary file and click the Open button In the online editor, you can select several images at once. the Image from URL option will open the window where you can enter the web address of the necessary image and click the OK button the Image from Storage option will open the Select data source window. Select an image stored on your portal and click the OK button once the image is added, you can change its size and position. You can also add an image into a text placeholder pressing the Image from file in it and selecting the necessary image stored on your PC, or use the Image from URL button and specify the image URL address: It's also possible to add an image to a slide layout. To learn more, please refer to this article. Adjust image settings The right sidebar is activated when you left-click an image and choose the Image settings icon on the right. It contains the following sections: Size - is used to view the Width and Height of the current image or restore its Actual Size if necessary. The Crop button is used to crop the image. Click the Crop button to activate cropping handles which appear on the image corners and in the center of its each side. Manually drag the handles to set the cropping area. You can move the mouse cursor over the cropping area border so that it turns into the icon and drag the area. To crop a single side, drag the handle located in the center of this side. To simultaneously crop two adjacent sides, drag one of the corner handles. To equally crop two opposite sides of the image, hold down the Ctrl key when dragging the handle in the center of one of these sides. To equally crop all sides of the image, hold down the Ctrl key when dragging any of the corner handles. When the cropping area is specified, click the Crop button once again, or press the Esc key, or click anywhere outside of the cropping area to apply the changes. After the cropping area is selected, it's also possible to use the Crop to shape, Fill and Fit options available from the Crop drop-down menu. Click the Crop button once again and select the option you need: If you select the Crop to shape option, the picture will fill a certain shape. You can select a shape from the gallery, which opens when you hover your mouse pointer over the Crop to Shape option. You can still use the Fill and Fit options to choose the way your picture matches the shape. If you select the Fill option, the central part of the original image will be preserved and used to fill the selected cropping area, while other parts of the image will be removed. If you select the Fit option, the image will be resized so that it fits the cropping area height or width. No parts of the original image will be removed, but empty spaces may appear within the selected cropping area. Replace Image - is used to load another image instead of the current one from the desired source. You can select one of the options: From File, From Storage, or From URL. The Replace image option is also available in the right-click menu. Rotation is used to rotate the image by 90 degrees clockwise or counterclockwise as well as to flip the image horizontally or vertically. Click one of the buttons: to rotate the image by 90 degrees counterclockwise to rotate the image by 90 degrees clockwise to flip the image horizontally (left to right) to flip the image vertically (upside down) When the image is selected, the Shape settings icon is also available on the right. You can click this icon to open the Shape settings tab on the right sidebar and adjust the Line type, size and color of the shape as well as change its type by selecting another shape from the Change Autoshape menu. The shape of the image will change correspondingly. On the Shape Settings tab, you can also use the Show shadow option to add a shadow to the image. To change the advanced settings of the image, right-click the image and select the Image Advanced Settings option from the contextual menu or left-click the image and press the Show advanced settings link on the right sidebar. The image properties window will be opened: The Placement tab allows you to set the following image properties: Size - use this option to change the image width and/or height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original image aspect ratio. To restore the actual size of the added image, click the Actual Size button. Position - set the exact position using the Horizontal and Vertical fields, as well as the From field where you can access such settings as Top Left Corner and Center. The Rotation tab contains the following parameters: Angle - use this option to rotate the image by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. Flipped - check the Horizontally box to flip the image horizontally (left to right) or check the Vertically box to flip the image vertically (upside down). The Alternative Text tab allows specifying the Title and Description which will be read to people with vision or cognitive impairments to help them better understand the contents of the image. To delete the inserted image, left-click it and press the Delete key. To learn how to align an image on the slide or arrange several images, refer to the Align and arrange objects on a slide section."
    },
   {
        "id": "UsageInstructions/InsertSmartArt.htm", 
        "title": "Insert SmartArt objects in ONLYOFFICE Presentation Editor", 
        "body": "Insert SmartArt objects SmartArt graphics are used to create a visual representation of a hierarchical structure by choosing a layout that fits best. Insert SmartArt objects or edit the ones added in third-party editors. To insert a SmartArt object, go to the Insert tab, click the SmartArt button, hover over one of the available layout styles, e.g., List or Process, choose one of the available layout types from the list appeared to the right of the highlighted menu item. You can customize the SmartArt settings in the right panel: Please note that color, style and form type settings can be customized individually. Fill - use this section to select the SmartArt object fill. You can choose the following options: Color Fill - select this option to specify the solid color to fill the inner space of the selected SmartArt object. Gradient Fill - use this option to fill the shape with two or more fading colors. Customize your gradient fill with no constraints. Click the Shape settings icon to open the Fill menu on the right sidebar: Available menu options: Style - choose between Linear or Radial: Linear is used  when you need your colors to flow from left-to-right, top-to-bottom, or at any angle you chose in a single direction. The Direction preview window displays the selected gradient color, click the arrow to choose a preset gradient direction. Use Angle settings for a precise gradient angle. Radial is used to move from the center as it starts at a single point and emanates outward. Gradient Point is a specific point for transition from one color to another. Use the Add Gradient Point button or slider bar to add a gradient point. You can add up to 10 gradient points. Each next gradient point added will in no way affect the current gradient fill appearance. Use the Remove Gradient Point button to delete a certain gradient point. Use the slider bar to change the location of the gradient point or specify Position in percentage for precise location. To apply a color to a gradient point, click a point on the slider bar, and then click Color to choose the color you want. Picture or Texture - select this option to use an image or a predefined texture as the SmartArt object background. If you wish to use an image as a background for the SmartArt object, you can add an image From File by selecting it on your computer hard disc drive, From URL by inserting the appropriate URL address into the opened window, or From Storage by selecting the required image stored on your portal. If you wish to use a texture as a background for the SmartArt object, open the From Texture menu and select the necessary texture preset. Currently, the following textures are available: canvas, carton, dark fabric, grain, granite, grey paper, knit, leather, brown paper, papyrus, wood. In case the selected Picture has less or more dimensions than the SmartArt object has, you can choose the Stretch or Tile setting from the dropdown list. The Stretch option allows you to adjust the image size to fit the SmartArt object size so that it could fill the space completely. The Tile option allows you to display only a part of the bigger image keeping its original dimensions or repeat the smaller image keeping its original dimensions over the SmartArt object surface so that it could fill the space completely. Note: any selected Texture preset fills the space completely, but you can apply the Stretch effect if necessary. Pattern - select this option to fill the SmartArt object with a two-colored design composed of regularly repeated elements. Pattern - select one of the predefined designs from the menu. Foreground color - click this color box to change the color of the pattern elements. Background color - click this color box to change the color of the pattern background. No Fill - select this option if you don't want to use any fill. Line - use this section to change the width, color or type of the SmartArt object line. To change the line width, select one of the available options from the Size dropdown list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Alternatively, select the No Line option if you don't want to use any line. To change the line color, click on the colored box below and select the necessary color. To change the line type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Show shadow - check this box to make the SmartArt object cast a shadow. Click the Show advanced settings link to open the advanced settings."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Insert symbols and characters", 
        "body": "When working on a presentation in the Presentation Editor, you may need to insert a symbol which is not available on your keyboard. To insert such symbols into your presentation, use the Insert symbol option and follow these simple steps: place the cursor where a special symbol should be inserted, switch to the Insert tab of the top toolbar, click the Symbol, The Symbol dialog box will appear, and you will be able to select the required symbol, use the Range section to quickly find the necessary symbol. All symbols are divided into specific groups, for example, select 'Currency Symbols' if you want to insert a currency character. If this character is not in the set, select a different font. Many of them also have characters that differ from the standard set. Or, enter the Unicode hex value of the required symbol into the Unicode hex value field. This code can be found in the Character map. You can also use the Special characters tab to choose a special character from the list. The previously used symbols are also displayed in the Recently used symbols field, click Insert. The selected character will be added to the presentation. Insert ASCII symbols ASCII table is also used to add characters. To do this, hold down the ALT key and use the numeric keypad to enter the character code. Note: be sure to use the numeric keypad, not the numbers on the main keyboard. To enable the numeric keypad, press the Num Lock key. For example, to add a paragraph character (§), press and hold down ALT while typing 789, and then release the ALT key. Insert symbols using Unicode table Additional characters and symbols might also be found via Windows symbol table. To open this table, do one of the following: in the Search field, write 'Character table' and open it, simultaneously press Win + R, and then in the following window type charmap.exe and click OK. In the opened Character Map, select one of the Character sets, Groups, and Fonts. Next, click on the necessary characters, copy them to the clipboard, and paste in the right place of the presentation."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Insert and format tables", 
        "body": "Insert a table To insert a table into a slide in the Presentation Editor, select the slide where a table should be added, switch to the Insert tab of the top toolbar, click the Table icon on the top toolbar, select one of the following options to create a table: either a table with a predefined number of cells (10 x 8 cells maximum) If you want to quickly add a table, just select the number of rows (8 maximum) and columns (10 maximum). or a custom table In case you need more than a 10 x 8 cell table, select the Insert Custom Table option that will open the window where you can enter the necessary number of rows and columns respectively, then click the OK button. if you want to insert a table as an OLE object: Select the Insert Spreadsheet option in the Table menu on the Insert tab. The corresponding window appears where you can enter the required data and format it using the Spreadsheet Editor formatting tools such as choosing font, type and style, setting number format, inserting functions, formatting tables etc. The header contains the Visible area button in the top right corner of the window. Choose the Edit Visible Area option to select the area that will be shown when the object is inserted into the presentation; other data is not lost, it is just hidden. Click Done when ready. Click the Show Visible Area button to see the selected area that will have a blue border. When ready, click the Save & Exit button. once the table is added, you can change its properties and position. You can also add a table into a text placeholder by pressing the Table icon within it and selecting the necessary number of cells or using the Insert Custom Table option: To resize a table, drag the handles situated on its edges until the table reaches the necessary size. You can also manually change the width of a certain column or the height of a row. Move the mouse cursor over the right border of the column so that the cursor turns into the bidirectional arrow and drag the border to the left or right to set the necessary width. To change the height of a single row manually, move the mouse cursor over the bottom border of the row until the cursor turns into the bidirectional arrow and drag it up or down. You can specify the table position on the slide by dragging it vertically or horizontally. Note: to move around in a table, you can use keyboard shortcuts. It's also possible to add a table to a slide layout. To learn more, please refer to this article. Adjust table settings Most of the table properties as well as its structure can be altered by using the right sidebar. To activate it, click the table and choose the Table settings icon on the right. The Rows and Columns sections on the top allow you to emphasize certain rows/columns by applying a specific formatting to them, or highlight different rows/columns with different background colors to clearly distinguish them. The following options are available: Header - emphasizes the topmost row in the table with special formatting. Total - emphasizes the bottommost row in the table with special formatting. Banded - enables the background color alternation for odd and even rows. First - emphasizes the leftmost column in the table with special formatting. Last - emphasizes the rightmost column in the table with special formatting. Banded - enables the background color alternation for odd and even columns. The Select From Template section allows you to choose one of the predefined tables styles. Each template combines certain formatting parameters, such as a background color, border style, row/column banding etc. Depending on the options checked in the Rows and/or Columns sections above, the templates set will be displayed differently. For example, if you've checked the Header option in the Rows section and the Banded option in the Columns section, the displayed templates list will include only templates with the header row and banded columns enabled: The Borders Style section allows you to change the applied formatting that corresponds to the selected template. You can select the entire table or a certain cell range and set all the parameters manually. Border parameters - set the border width using the list (or choose the No borders option), select its Color in the available palettes and determine the way it will be displayed in the cells when clicking on the icons: Background color - select the color for the background within the selected cells. The Rows & Columns section allows you to perform the following operations: Select a row, column, cell (depending on the cursor position), or the entire table. Insert a new row above or below the selected one as well as a new column to the left or to the right of the selected one. Delete a row, column (depending on the cursor position or the selection), or the entire table. Merge Cells - to merge previously selected cells into a single one. Split Cell... - to split any previously selected cell into a certain number of rows and columns. This option opens the following window: Enter the Number of Columns and Number of Rows that the selected cell should be split into and press OK. Note: the options of the Rows & Columns section are also accessible from the right-click menu. The Cell Size section is used to adjust the width and height of the currently selected cell. In this section, you can also Distribute rows so that all the selected cells are of equal height or Distribute columns so that all the selected cells are of equal width. The Distribute rows/columns options are also accessible from the right-click menu. Adjust table advanced settings To change the advanced table settings, click the table with the right mouse button and select the Table Advanced Settings option from the right-click menu or click the Show advanced settings link on the right sidebar. The table properties window will be opened: The Placement tab allows you to set the following table properties: Size - use this option to change the table width and/or height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original image aspect ratio. Position - set the exact position using the Horizontal and Vertical fields, as well as the From field where you can access such settings as Top Left Corner and Center. The Margins tab allows setting the space between the text within the cells and the cell border: enter necessary Cell Margins values manually, or check the Use default margins box to apply the predefined values (if necessary, they can also be adjusted). The Alternative Text tab allows specifying the Title and Description which will be read to people with vision or cognitive impairments to help them better understand the contents of the table. To format the entered text within the table cells, you can use icons on the Home tab of the top toolbar. The right-click menu, which appears when you click the table with the right mouse button, includes two additional options: Cell vertical alignment - it allows you to set the preferred type of the text vertical alignment within the selected cells: Align Top, Align Center, or Align Bottom. Hyperlink - it allows you to insert a hyperlink into the selected cell."
    },
   {
        "id": "UsageInstructions/InsertText.htm", 
        "title": "Insert and format your text", 
        "body": "Insert your text box into presentation In the Presentation Editor, you can add a new text in three different ways: Add a text passage within the corresponding text placeholder on the slide layout. To do that, just put the cursor within the placeholder and type in your text or paste it using the Ctrl+V key combination instead of the default text. Add a text passage anywhere on a slide. You can insert a text box (a rectangular frame that allows you to enter some text within it) or a Text Art object (a text box with a predefined font style and color that allows you to apply some text effects). Depending on the necessary text object type, you can do the following: to add a text box, click the Text Box icon on the Home or Insert tab of the top toolbar, choose one of the following options: Insert horizontal text box or Insert vertical text box, then click where you want to insert the text box, hold the mouse button and drag the text box border to specify its size. When you release the mouse button, the insertion point will appear in the added text box, allowing you to enter your text. It's also possible to insert a text box by clicking the Shape icon on the top toolbar and selecting the shape from the Basic Shapes group. to add a Text Art object, click the Text Art icon on the Insert tab of the top toolbar, then click on the desired style template – the Text Art object will be added in the center of the slide. Select the default text within the text box with the mouse and replace it with your own text. Add a text passage within an autoshape. Select a shape and start typing your text. Click outside of the text object to apply the changes and return to the slide. The text within the text object is a part of the latter (when you move or rotate the text object, the text moves or rotates with it). As an inserted text object represents a rectangular frame (it has invisible text box borders by default) with text in it and this frame is a common autoshape, you can change both the shape and text properties. To delete the added text object, click on the text box border and press the Delete key. The text within the text box will also be deleted. Format a text box Select the text box by clicking on its border to change its properties. When the text box is selected, its borders are displayed as solid (not dashed) lines. to resize, move, rotate the text box, use the special handles on the edges of the shape. to edit the text box fill, line, replace the rectangular box with a different shape, or access the shape advanced settings, click the Shape settings icon on the right sidebar and use the corresponding options. to align a text box on the slide, rotate or flip it, arrange text boxes as related to other objects, right-click on the text box border and use the contextual menu options. to create columns of text within the text box, click the corresponding icon on the text formatting toolbar and choose the preferable option, or right-click on the text box border, click the Shape Advanced Settings option and switch to the Columns tab in the Shape - Advanced Settings window. Format the text within the text box Click the text within the text box to change its properties. When the text is selected, the text box borders are displayed as dashed lines. Note: it's also possible to change text formatting when the text box (not the text itself) is selected. In such a case, any changes will be applied to the whole text within the text box. Some font formatting options (font type, size, color and decoration styles) can be applied to the previously selected part of the text separately. Align your text within the text box The text is aligned horizontally in four ways: left, right, center or justified. To do that: place the cursor to the position where you want the alignment to be applied (this can be a new line or already entered text), drop-down the Horizontal align list on the Home tab of the top toolbar, select the alignment type you would like to apply: the Align text left option allows you to line up your text on the left side of the text box (the right side remains unaligned). the Align text center option allows you to line up your text in the center of the text box (the right and the left sides remains unaligned). the Align text right option allows you to line up your text on the right side of the text box (the left side remains unaligned). the Justify option allows you to line up your text both on the left and on the right sides of the text box (additional spacing is added where necessary to keep the alignment). Note: these parameters can also be found in the Paragraph - Advanced Settings window. The text is aligned vertically in three ways: top, middle or bottom. To do that: place the cursor to the position where you want the alignment to be applied (this can be a new line or already entered text), drop-down the Vertical align list on the Home tab of the top toolbar, select the alignment type you would like to apply: the Align text to the top option allows you to line up your text to the top of the text box. the Align text to the middle option allows you to line up your text in the center of the text box. the Align text to the bottom option allows you to line up your text to the bottom of the text box. Change the text direction To Rotate the text within the text box, right-click the text, select the Text Direction option and then choose one of the available options: Horizontal (selected by default), Rotate Text Down (used to set a vertical direction, from top to bottom) or Rotate Text Up (used to set a vertical direction, from bottom to top). Adjust font type, size, color and apply decoration styles You can select the font type, its size and color as well as apply various font decoration styles using the corresponding icons situated on the Home tab of the top toolbar. Note: in case you want to apply the formatting to the text already present in the presentation, select it with the mouse or using the keyboard and apply the formatting. You can also place the mouse cursor within the necessary word to apply the formatting to this word only. Font Used to select one of the fonts from the list of the available ones. If the required font is not available in the list, you can download and install it on your operating system, and the font will be available for use in the desktop version. Font size Used to choose from the preset font size values in the dropdown list (the default values are: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 and 96). It's also possible to manually enter a custom value up to 300 pt in the font size field. Press Enter to confirm. Increment font size Used to change the font size making it one point bigger each time the button is pressed. Decrement font size Used to change the font size making it one point smaller each time the button is pressed. Change case Used to change the font case. Sentence case. - the case matches that of a common sentence. lowercase - all letters are small. UPPERCASE - all letters are capital. Capitalize Each Word - each word starts with a capital letter. tOGGLE cASE - reverse the case of the selected text or the word where the mouse cursor is positioned. Highlight color Used to mark separate sentences, phrases, words, or even characters by adding a color band that imitates the highlighter pen effect throughout the text. You can select the required part of the text and click the downward arrow next to the icon to select a color in the palette (this color set does not depend on the selected Color scheme and includes 16 colors) - the color will be applied to the selected text. Alternatively, you can first choose a highlight color and then start selecting the text with the mouse - the mouse pointer will look like this and you'll be able to highlight several different parts of your text sequentially. To stop highlighting, just click the icon once again. To delete the highlight color, choose the No Fill option. Font color Used to change the color of the letters/characters in the text. Click the downward arrow next to the icon to select the color. Bold Used to make the font bold giving it a heavier appearance. Italic Used to make the font slightly slanted to the right. Underline Used to make the text underlined with a line going under the letters. Strikeout Used to make the text struck out with a line going through the letters. Superscript Used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions. Subscript Used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas. Set line spacing and change paragraph indents You can set the line height for the text lines within the paragraph as well as the margins between the current and the previous or the following paragraph. To do that, put the cursor within the required paragraph or select several paragraphs with the mouse, use the corresponding fields of the Paragraph settings tab on the right sidebar to achieve the desired results: Line Spacing - set the line height for the text lines within the paragraph. You can select among two options: multiple (sets line spacing that can be expressed in numbers greater than 1), exactly (sets fixed line spacing). You can specify the necessary value in the field on the right. Paragraph Spacing - set the amount of space between paragraphs. Before - set the amount of space before the paragraph. After - set the amount of space after the paragraph. Note: these parameters can also be found in the Paragraph - Advanced Settings window. To quickly change the current paragraph line spacing, you can also use the Line spacing icon on the Home tab of the top toolbar selecting the required value from the list: 1.0, 1.15, 1.5, 2.0, 2.5, or 3.0 lines. To change the paragraph offset from the left side of the text box, put the cursor within the required paragraph, or select several paragraphs with the mouse and use the respective icons on the Home tab of the top toolbar: Decrease indent and Increase indent . Adjust paragraph advanced settings To open the Paragraph - Advanced Settings window, right-click the text and choose the Paragraph Advanced Settings option from the menu. It's also possible to put the cursor within the required paragraph - the Paragraph settings tab will be activated on the right sidebar. Press the Show advanced settings link. The paragraph properties window will be opened: The Indents & Spacing tab allows you to: change the alignment type for the paragraph text, change the paragraph indents as related to internal margins of the text box, Left - set the paragraph offset from the left internal margin of the text box specifying the necessary numeric value, Right - set the paragraph offset from the right internal margin of the text box specifying the necessary numeric value, Special - set an indent for the first line of the paragraph: select the corresponding menu item ((none), First line, Hanging) and change the default numeric value specified for First Line or Hanging, change the paragraph line spacing. You can also use the horizontal ruler to set indents. Select the necessary paragraph(s) and drag the indent markers along the ruler. First Line Indent marker is used to set the offset from the left internal margin of the text box for the first line of the paragraph. Hanging Indent marker is used to set the offset from the left internal margin of the text box for the second and all the subsequent lines of the paragraph. Left Indent marker is used to set the entire paragraph offset from the left internal margin of the text box. Right Indent marker is used to set the paragraph offset from the right internal margin of the text box. Note: if you don't see the rulers, switch to the Home tab of the top toolbar, click the View settings icon at the upper right corner and uncheck the Hide Rulers option to display them. The Font tab contains the following parameters: Strikethrough is used to make the text struck out with a line going through the letters. Double strikethrough is used to make the text struck out with a double line going through the letters. Superscript is used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions. Subscript is used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas. Small caps is used to make all letters lower case. All caps is used to make all letters upper case. Character Spacing is used to set the space between the characters. Increase the default value to apply the Expanded spacing, or decrease the default value to apply the Condensed spacing. Use the arrow buttons or enter the necessary value in the box. All the changes will be displayed in the preview field below. The Tab tab allows you to change tab stops i.e. the position the cursor advances to when you press the Tab key. Default Tab is set at 2.54 cm. You can decrease or increase this value using the arrow buttons or enter the necessary one in the box. Tab Position - is used to set custom tab stops. Enter the necessary value in this box, adjust it more precisely using the arrow buttons and press the Specify button. Your custom tab position will be added to the list in the field below. Alignment - is used to set the necessary alignment type for each of the tab positions in the list above. Select the necessary tab position in the list, choose the Left, Center or Right option from the Alignment drop-down list and press the Specify button. Left - lines up your text on the left side at the tab stop position; the text moves to the right from the tab stop as you type. Such a tab stop will be indicated on the horizontal ruler by the marker. Center - centres the text at the tab stop position. Such a tab stop will be indicated on the horizontal ruler by the marker. Right - lines up your text on the right side at the tab stop position; the text moves to the left from the tab stop as you type. Such a tab stop will be indicated on the horizontal ruler by the marker. To delete tab stops from the list, select a tab stop and press the Remove or Remove All button. To set tab stops, you can also use the horizontal ruler: Click the tab selector button in the upper left corner of the working area to choose the necessary tab stop type: Left , Center , Right . Click on the bottom edge of the ruler where you want to place the tab stop. Drag it along the ruler to change its position. To remove the added tab stop, drag it out of the ruler. Note: if you don't see the rulers, switch to the Home tab of the top toolbar, click the View settings icon at the upper right corner and uncheck the Hide Rulers option to display them. Edit a Text Art style Select a text object and click the Text Art settings icon on the right sidebar. Change the applied text style selecting a new Template from the gallery. You can also change the basic style additionally by selecting a different font type, size etc. Change the font fill and line. The available options are the same as the ones for autoshapes. Apply a text effect by selecting the necessary text transformation type from the Transform gallery. You can adjust the degree of the text distortion by dragging the pink diamond-shaped handle."
    },
   {
        "id": "UsageInstructions/ManageSlides.htm", 
        "title": "Manage slides", 
        "body": "By default, a newly created presentation has one blank Title Slide. In the Presentation Editor, you can create new slides, copy a slide to paste it later to another place in the slide list, duplicate slides, move slides to change their order, delete unnecessary slides and mark some slides as hidden. To create a new Title and Content slide: click the Add Slide icon on the Home or Insert tab of the top toolbar, or right-click any slide in the list and select the New Slide option from the contextual menu, or press the Ctrl+M key combination. To create a new slide with a different layout: click the arrow next to the Add Slide icon on the Home or Insert tab of the top toolbar, select a slide with the necessary layout from the menu. Note: you can change the layout of the added slide anytime. For additional information on how to do that, please refer to the Set slide parameters section. A new slide will be inserted after the selected one in the list of the existing slides on the left. To duplicate slides: select a slide, or multiple slides in the list of the existing slides on the left, right-click the mouse button and select the Duplicate Slide option from the context menu, or go to the Home or the Insert tab, click the Add slide button and select the Duplicate Slide menu option. The duplicated slide will be inserted after the selected one in the slide list. To copy slides: in the list of the existing slides on the left, select a slide or multiple slides you need to copy, press the Ctrl+C key combination, in the slide list, select the slide after which the copied slide should be pasted. press the Ctrl+V key combination. Once the copied slide is pasted, the Paste Special button appears next to the inserted slide. Click this button to select the necessary paste option or use the Ctrl key in combination with the letter key given in the brackets next to the option. The following Paste Special options are available: Use destination theme (Ctrl+H) - allows applying the formatting specified by the theme of the current presentation. This option is used by default. Keep source formatting (Ctrl+K) - allows keeping the source formatting of the copied slide. Picture (Ctrl+U) - allows pasting the slide as an image so that it cannot be edited. To move existing slides: left-click the necessary slide or slides in the list of the existing slides on the left, without releasing the mouse button, drag it to the necessary place in the list (a horizontal line indicates a new location). To delete unnecessary slides: right-click the slide or slides you want to delete in the list of the existing slides on the left, select the Delete Slide option from the contextual menu. To mark slides as hidden: right-click the slide or slides you want to hide in the list of the existing slides on the left, select the Hide Slide option from the contextual menu. The number that corresponds to the hidden slide in the slide list on the left will be crossed out. To display the hidden slide as a regular one in the slide list, click the Hide Slide option once again. Note: use this option if you do not want to demonstrate some slides to your audience, but want to be able to access them if necessary. If you start the slideshow in the Presenter mode, you can see all the existing slides in the list on the left, while hidden slides numbers are crossed out. If you wish to show a slide marked as hidden to others, just click it in the slide list on the left - the slide will be displayed. To select all the existing slides at once: right-click any slide in the list of the existing slides on the left, select the Select All option from the contextual menu. To select several slides: hold down the Ctrl key, select the necessary slides by left-clicking them in the list of the existing slides on the left. Note: all the key combinations that can be used to manage slides are listed on the Keyboard Shortcuts page."
    },
   {
        "id": "UsageInstructions/ManipulateObjects.htm", 
        "title": "Manipulate objects on a slide", 
        "body": "In the Presentation Editor, you can resize, move, rotate different objects on a slide manually using the special handles. You can also specify the dimensions and position of some objects exactly using the right sidebar or Advanced Settings window. Note: the list of keyboard shortcuts that can be used when working with objects is available here. Resize objects To change the autoshape/image/chart/table/text box size, drag small squares situated on the object edges. To maintain the original proportions of the selected object while resizing, hold down the Shift key and drag one of the corner icons. To specify the precise width and height of a chart, select it on a slide and use the Size section of the right sidebar that will be activated. To specify the precise dimensions of an image or autoshape, right-click the necessary object on the slide and select the Image/Shape Advanced Settings option from the menu. Specify necessary values on the Size tab of the Advanced Settings window and press OK. Reshape autoshapes When modifying some shapes, for example Figured arrows or Callouts, the yellow diamond-shaped icon is also available. It allows adjusting some aspects of the shape, for example, the length of the head of an arrow. To reshape an autoshape, you can also use the Edit Points option from the context menu. The Edit Points option is used to customize or to change the curvature of your shape. To activate a shape’s editable anchor points, right-click the shape and choose Edit Points from the menu. The black squares that become active are the points where two lines meet, and the red line outlines the shape. Click and drag it to reposition the point, and to change the shape outline. Once you click the anchor point, two blue lines with white squares at the ends will appear. These are Bezier handles that allow you to create and a curve and to change a curve’s smoothness. As long as the anchor points are active, you can add and delete them: To add a point to a shape, hold Ctrl and click the position where you want to add an anchor point. To delete a point, hold Ctrl and click the unnecessary point. Move objects To alter the autoshape/image/chart/table/text box position, use the icon that appears after hovering your mouse cursor over the object. Drag the object to the necessary position without releasing the mouse button. To move the object by the one-pixel increments, hold down the Ctrl key and use the keybord arrows. To move the object strictly horizontally/vertically and prevent it from moving in a perpendicular direction, hold down the Shift key when dragging. To specify the precise position of an image, right-click it on a slide and select the Image Advanced Settings option from the menu. Specify necessary values in the Position section of the Advanced Settings window and press OK. Rotate objects To manually rotate an autoshape/image/text box, hover the mouse cursor over the rotation handle and drag it clockwise or counterclockwise. To constrain the rotation angle to 15 degree increments, hold down the Shift key while rotating. To rotate the object by 90 degrees counterclockwise/clockwise or flip the object horizontally/vertically, you can use the Rotation section of the right sidebar that will be activated once you select the necessary object. To open it, click the Shape settings or the Image settings icon to the right. Click one of the buttons: to rotate the object by 90 degrees counterclockwise to rotate the object by 90 degrees clockwise to flip the object horizontally (left to right) to flip the object vertically (upside down) It's also possible to right-click the object, choose the Rotate option from the contextual menu and then use one of the available rotation options. To rotate the object by an exactly specified angle, click the Show advanced settings link on the right sidebar and use the Rotation tab of the Advanced Settings window. Specify the necessary value measured in degrees in the Angle field and click OK."
    },
   {
        "id": "UsageInstructions/MathAutoCorrect.htm", 
        "title": "AutoCorrect Features", 
        "body": "The AutoCorrect features in ONLYOFFICE Presentation Editor are used to automatically format text when detected or insert special math symbols by recognizing particular character usage. The available AutoCorrect options are listed in the corresponding dialog box. To access it, go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options. The AutoCorrect dialog box consists of four tabs: Math Autocorrect, Recognized Functions, AutoFormat As You Type, and Text AutoCorrect. Math AutoCorrect When working with equations, you can insert a lot of symbols, accents and mathematical operation signs typing them on the keyboard instead of choosing a template from the gallery. In the equation editor, place the insertion point within the necessary placeholder, type a math autocorrect code, then press Spacebar. The entered code will be converted into the corresponding symbol, and the space will be eliminated. The codes are case sensitive. You can add, modify, restore, and remove autocorrect entries from the AutoCorrect list. Go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> Math AutoCorrect. Adding an entry to the AutoCorrect list Enter the autocorrect code you want to use in the Replace box. Enter the symbol to be assigned to the code you entered in the By box. Click the Add button. Modifying an entry on the AutoCorrect list Select the entry to be modified. You can change the information in both fields: the code in the Replace box or the symbol in the By box. Click the Replace button. Removing entries from the AutoCorrect list Select an entry to remove from the list. Click the Delete button. To restore the previously deleted entries, select the entry to be restored from the list and click the Restore button. Use the Reset to default button to restore default settings. Any autocorrect entry you added will be removed and the changed ones will be restored to their original values. To disable Math AutoCorrect and to avoid automatic changes and replacements, uncheck the Replace text as you type box. The table below contains all the currently supported codes available in the Presentation Editor. The full list of the supported codes can also be found on the File tab in the Advanced Settings... -> Spell checking -> Proofing section. The supported codes Code Symbol Category !! Symbols ... Dots :: Operators := Operators /< Relational operators /> Relational operators /= Relational operators \\above Above/Below scripts \\acute Accents \\aleph Hebrew letters \\alpha Greek letters \\Alpha Greek letters \\amalg Binary operators \\angle Geometry notation \\aoint Integrals \\approx Relational operators \\asmash Arrows \\ast Binary operators \\asymp Relational operators \\atop Operators \\bar Over/Underbar \\Bar Accents \\because Relational operators \\begin Delimiters \\below Above/Below scripts \\bet Hebrew letters \\beta Greek letters \\Beta Greek letters \\beth Hebrew letters \\bigcap Large operators \\bigcup Large operators \\bigodot Large operators \\bigoplus Large operators \\bigotimes Large operators \\bigsqcup Large operators \\biguplus Large operators \\bigvee Large operators \\bigwedge Large operators \\binomial Equations \\bot Logic notation \\bowtie Relational operators \\box Symbols \\boxdot Binary operators \\boxminus Binary operators \\boxplus Binary operators \\bra Delimiters \\break Symbols \\breve Accents \\bullet Binary operators \\cap Binary operators \\cbrt Square roots and radicals \\cases Symbols \\cdot Binary operators \\cdots Dots \\check Accents \\chi Greek letters \\Chi Greek letters \\circ Binary operators \\close Delimiters \\clubsuit Symbols \\coint Integrals \\cong Relational operators \\coprod Math operators \\cup Binary operators \\dalet Hebrew letters \\daleth Hebrew letters \\dashv Relational operators \\dd Double-struck letters \\Dd Double-struck letters \\ddddot Accents \\dddot Accents \\ddot Accents \\ddots Dots \\defeq Relational operators \\degc Symbols \\degf Symbols \\degree Symbols \\delta Greek letters \\Delta Greek letters \\Deltaeq Operators \\diamond Binary operators \\diamondsuit Symbols \\div Binary operators \\dot Accents \\doteq Relational operators \\dots Dots \\doublea Double-struck letters \\doubleA Double-struck letters \\doubleb Double-struck letters \\doubleB Double-struck letters \\doublec Double-struck letters \\doubleC Double-struck letters \\doubled Double-struck letters \\doubleD Double-struck letters \\doublee Double-struck letters \\doubleE Double-struck letters \\doublef Double-struck letters \\doubleF Double-struck letters \\doubleg Double-struck letters \\doubleG Double-struck letters \\doubleh Double-struck letters \\doubleH Double-struck letters \\doublei Double-struck letters \\doubleI Double-struck letters \\doublej Double-struck letters \\doubleJ Double-struck letters \\doublek Double-struck letters \\doubleK Double-struck letters \\doublel Double-struck letters \\doubleL Double-struck letters \\doublem Double-struck letters \\doubleM Double-struck letters \\doublen Double-struck letters \\doubleN Double-struck letters \\doubleo Double-struck letters \\doubleO Double-struck letters \\doublep Double-struck letters \\doubleP Double-struck letters \\doubleq Double-struck letters \\doubleQ Double-struck letters \\doubler Double-struck letters \\doubleR Double-struck letters \\doubles Double-struck letters \\doubleS Double-struck letters \\doublet Double-struck letters \\doubleT Double-struck letters \\doubleu Double-struck letters \\doubleU Double-struck letters \\doublev Double-struck letters \\doubleV Double-struck letters \\doublew Double-struck letters \\doubleW Double-struck letters \\doublex Double-struck letters \\doubleX Double-struck letters \\doubley Double-struck letters \\doubleY Double-struck letters \\doublez Double-struck letters \\doubleZ Double-struck letters \\downarrow Arrows \\Downarrow Arrows \\dsmash Arrows \\ee Double-struck letters \\ell Symbols \\emptyset Set notations \\emsp Space characters \\end Delimiters \\ensp Space characters \\epsilon Greek letters \\Epsilon Greek letters \\eqarray Symbols \\equiv Relational operators \\eta Greek letters \\Eta Greek letters \\exists Logic notations \\forall Logic notations \\fraktura Fraktur letters \\frakturA Fraktur letters \\frakturb Fraktur letters \\frakturB Fraktur letters \\frakturc Fraktur letters \\frakturC Fraktur letters \\frakturd Fraktur letters \\frakturD Fraktur letters \\frakture Fraktur letters \\frakturE Fraktur letters \\frakturf Fraktur letters \\frakturF Fraktur letters \\frakturg Fraktur letters \\frakturG Fraktur letters \\frakturh Fraktur letters \\frakturH Fraktur letters \\frakturi Fraktur letters \\frakturI Fraktur letters \\frakturk Fraktur letters \\frakturK Fraktur letters \\frakturl Fraktur letters \\frakturL Fraktur letters \\frakturm Fraktur letters \\frakturM Fraktur letters \\frakturn Fraktur letters \\frakturN Fraktur letters \\frakturo Fraktur letters \\frakturO Fraktur letters \\frakturp Fraktur letters \\frakturP Fraktur letters \\frakturq Fraktur letters \\frakturQ Fraktur letters \\frakturr Fraktur letters \\frakturR Fraktur letters \\frakturs Fraktur letters \\frakturS Fraktur letters \\frakturt Fraktur letters \\frakturT Fraktur letters \\frakturu Fraktur letters \\frakturU Fraktur letters \\frakturv Fraktur letters \\frakturV Fraktur letters \\frakturw Fraktur letters \\frakturW Fraktur letters \\frakturx Fraktur letters \\frakturX Fraktur letters \\fraktury Fraktur letters \\frakturY Fraktur letters \\frakturz Fraktur letters \\frakturZ Fraktur letters \\frown Relational operators \\funcapply Binary operators \\G Greek letters \\gamma Greek letters \\Gamma Greek letters \\ge Relational operators \\geq Relational operators \\gets Arrows \\gg Relational operators \\gimel Hebrew letters \\grave Accents \\hairsp Space characters \\hat Accents \\hbar Symbols \\heartsuit Symbols \\hookleftarrow Arrows \\hookrightarrow Arrows \\hphantom Arrows \\hsmash Arrows \\hvec Accents \\identitymatrix Matrices \\ii Double-struck letters \\iiint Integrals \\iint Integrals \\iiiint Integrals \\Im Symbols \\imath Symbols \\in Relational operators \\inc Symbols \\infty Symbols \\int Integrals \\integral Integrals \\iota Greek letters \\Iota Greek letters \\itimes Math operators \\j Symbols \\jj Double-struck letters \\jmath Symbols \\kappa Greek letters \\Kappa Greek letters \\ket Delimiters \\lambda Greek letters \\Lambda Greek letters \\langle Delimiters \\lbbrack Delimiters \\lbrace Delimiters \\lbrack Delimiters \\lceil Delimiters \\ldiv Fraction slashes \\ldivide Fraction slashes \\ldots Dots \\le Relational operators \\left Delimiters \\leftarrow Arrows \\Leftarrow Arrows \\leftharpoondown Arrows \\leftharpoonup Arrows \\leftrightarrow Arrows \\Leftrightarrow Arrows \\leq Relational operators \\lfloor Delimiters \\lhvec Accents \\limit Limits \\ll Relational operators \\lmoust Delimiters \\Longleftarrow Arrows \\Longleftrightarrow Arrows \\Longrightarrow Arrows \\lrhar Arrows \\lvec Accents \\mapsto Arrows \\matrix Matrices \\medsp Space characters \\mid Relational operators \\middle Symbols \\models Relational operators \\mp Binary operators \\mu Greek letters \\Mu Greek letters \\nabla Symbols \\naryand Operators \\nbsp Space characters \\ne Relational operators \\nearrow Arrows \\neq Relational operators \\ni Relational operators \\norm Delimiters \\notcontain Relational operators \\notelement Relational operators \\notin Relational operators \\nu Greek letters \\Nu Greek letters \\nwarrow Arrows \\o Greek letters \\O Greek letters \\odot Binary operators \\of Operators \\oiiint Integrals \\oiint Integrals \\oint Integrals \\omega Greek letters \\Omega Greek letters \\ominus Binary operators \\open Delimiters \\oplus Binary operators \\otimes Binary operators \\over Delimiters \\overbar Accents \\overbrace Accents \\overbracket Accents \\overline Accents \\overparen Accents \\overshell Accents \\parallel Geometry notation \\partial Symbols \\pmatrix Matrices \\perp Geometry notation \\phantom Symbols \\phi Greek letters \\Phi Greek letters \\pi Greek letters \\Pi Greek letters \\pm Binary operators \\pppprime Primes \\ppprime Primes \\pprime Primes \\prec Relational operators \\preceq Relational operators \\prime Primes \\prod Math operators \\propto Relational operators \\psi Greek letters \\Psi Greek letters \\qdrt Square roots and radicals \\quadratic Square roots and radicals \\rangle Delimiters \\Rangle Delimiters \\ratio Relational operators \\rbrace Delimiters \\rbrack Delimiters \\Rbrack Delimiters \\rceil Delimiters \\rddots Dots \\Re Symbols \\rect Symbols \\rfloor Delimiters \\rho Greek letters \\Rho Greek letters \\rhvec Accents \\right Delimiters \\rightarrow Arrows \\Rightarrow Arrows \\rightharpoondown Arrows \\rightharpoonup Arrows \\rmoust Delimiters \\root Symbols \\scripta Scripts \\scriptA Scripts \\scriptb Scripts \\scriptB Scripts \\scriptc Scripts \\scriptC Scripts \\scriptd Scripts \\scriptD Scripts \\scripte Scripts \\scriptE Scripts \\scriptf Scripts \\scriptF Scripts \\scriptg Scripts \\scriptG Scripts \\scripth Scripts \\scriptH Scripts \\scripti Scripts \\scriptI Scripts \\scriptk Scripts \\scriptK Scripts \\scriptl Scripts \\scriptL Scripts \\scriptm Scripts \\scriptM Scripts \\scriptn Scripts \\scriptN Scripts \\scripto Scripts \\scriptO Scripts \\scriptp Scripts \\scriptP Scripts \\scriptq Scripts \\scriptQ Scripts \\scriptr Scripts \\scriptR Scripts \\scripts Scripts \\scriptS Scripts \\scriptt Scripts \\scriptT Scripts \\scriptu Scripts \\scriptU Scripts \\scriptv Scripts \\scriptV Scripts \\scriptw Scripts \\scriptW Scripts \\scriptx Scripts \\scriptX Scripts \\scripty Scripts \\scriptY Scripts \\scriptz Scripts \\scriptZ Scripts \\sdiv Fraction slashes \\sdivide Fraction slashes \\searrow Arrows \\setminus Binary operators \\sigma Greek letters \\Sigma Greek letters \\sim Relational operators \\simeq Relational operators \\smash Arrows \\smile Relational operators \\spadesuit Symbols \\sqcap Binary operators \\sqcup Binary operators \\sqrt Square roots and radicals \\sqsubseteq Set notation \\sqsuperseteq Set notation \\star Binary operators \\subset Set notation \\subseteq Set notation \\succ Relational operators \\succeq Relational operators \\sum Math operators \\superset Set notation \\superseteq Set notation \\swarrow Arrows \\tau Greek letters \\Tau Greek letters \\therefore Relational operators \\theta Greek letters \\Theta Greek letters \\thicksp Space characters \\thinsp Space characters \\tilde Accents \\times Binary operators \\to Arrows \\top Logic notation \\tvec Arrows \\ubar Accents \\Ubar Accents \\underbar Accents \\underbrace Accents \\underbracket Accents \\underline Accents \\underparen Accents \\uparrow Arrows \\Uparrow Arrows \\updownarrow Arrows \\Updownarrow Arrows \\uplus Binary operators \\upsilon Greek letters \\Upsilon Greek letters \\varepsilon Greek letters \\varphi Greek letters \\varpi Greek letters \\varrho Greek letters \\varsigma Greek letters \\vartheta Greek letters \\vbar Delimiters \\vdash Relational operators \\vdots Dots \\vec Accents \\vee Binary operators \\vert Delimiters \\Vert Delimiters \\Vmatrix Matrices \\vphantom Arrows \\vthicksp Space characters \\wedge Binary operators \\wp Symbols \\wr Binary operators \\xi Greek letters \\Xi Greek letters \\zeta Greek letters \\Zeta Greek letters \\zwnj Space characters \\zwsp Space characters ~= Relational operators -+ Binary operators +- Binary operators << Relational operators <= Relational operators -> Arrows >= Relational operators >> Relational operators Recognized Functions In this tab, you will find the list of math expressions that will be recognized by the Equation editor as functions and therefore will not be automatically italicized. For the list of recognized functions go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> Recognized Functions. To add an entry to the list of recognized functions, enter the function in the blank field and click the Add button. To remove an entry from the list of recognized functions, select the function to be removed and click the Delete button. To restore the previously deleted entries, select the entry to be restored from the list and click the Restore button. Use the Reset to default button to restore default settings. Any function you added will be removed and the removed ones will be restored. AutoFormat as You Type By default, the editor formats the text while you are typing according to the auto-formatting presets: replaces quotation marks, converts hyphens to dashes, converts text recognized as internet or network path into a hyperlink, starts a bullet list or a numbered list when a list is detected. The Add period with double-space option allows to add a period when you double tap the spacebar. Enable or disable it as appropriate.By default, this option is disabled for Linux and Windows, and is enabled for macOS. To enable or disable the auto-formatting presets, go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> AutoFormat As You Type. Text AutoCorrect You can set the editor to capitalize the first word of each sentence automatically. The option is enabled by default. To disable this option, go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> Text AutoCorrect and uncheck the Capitalize first letter of sentences option."
    },
   {
        "id": "UsageInstructions/MotionPath.htm", 
        "title": "Creating a motion path animation", 
        "body": "Motion path is a part of the animation gallery effects that determines the movement of an object and the path it follows. The icons in the gallery represent the suggested path. The animation gallery is available on the Animation tab at the top toolbar. Applying a motion path animation effect switch to the Animation tab on the top toolbar, select a text, an object or a graphic element you want to apply the animation effect to, select one of the premade motion path patterns from the Motion Paths section in the animations gallery (Lines, Arcs, etc.), or choose the Custom Path option if you want to create a path of your own. You can preview animation effects on the current slide. By default, animation effects will play automatically when you add them to a slide but you can turn it off. Click the Preview drop-down on the Animation tab, and select a preview mode: Preview to show a preview when you click the Preview button, AutoPreview to show a preview automatically when you add an animation to a slide or replace an existing one. Adding a custom path animation effect To draw a custom path, Click the object you want to give a custom path animation to. Mark the path points with the left mouse button. One click with a left mouse button will draw a line, while holding the left mouse button allows you to make any curve you want. The starting point of the path will be marked with a green directional arrow, the ending point will be a red one. When ready, click the left mouse button twice or press the Esc button to stop drawing your path. Editing motion path points To edit the motion path points, select the path object, click with the right mouse button to open the context menu and choose the Edit Points option. Drag the black squares to adjust the position of the nodes of the path points; drag the white squares to adjust the direction at the entry and exit points of the node. Press Esc or anywhere outside the path object to exit the editing mode. You can scale the motion path by clicking it and dragging the square points at the edges of the object."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Create a new presentation or open an existing one", 
        "body": "In the Presentation Editor, you can open a recently edited presentation, rename it, create a new one, or return to the list of existing presentations. To create a new presentation In the online editor click the File tab of the top toolbar, select the Create New option. In the desktop editor in the main program window, select the Presentation menu item from the Create new section of the left sidebar - a new file will open in a new tab, when all the necessary changes are made, click the Save icon in the upper left corner or switch to the File tab and choose the Save as menu item. in the file manager window, select the file location, specify its name, choose the format you want to save the presentation to (PPTX, Presentation template (POTX), ODP, OTP, PDF or PDFA) and click the Save button. To open an existing presentation In the desktop editor in the main program window, select the Open local file menu item on the left sidebar, choose the necessary presentation from the file manager window and click the Open button. You can also right-click the necessary presentation in the file manager window, select the Open with option and choose the necessary application from the menu. If the office documents files are associated with the application, you can also open presentations by double-clicking the file name in the file explorer window. All the directories that you have accessed using the desktop editor will be displayed in the Recent folders list so that you can quickly access them afterwards. Click the necessary folder to select one of the files stored in it. To open a recently edited presentation In the online editor click the File tab of the top toolbar, select the Open Recent option, choose the presentation you need from the list of recently edited documents. In the desktop editor in the main program window, select the Recent files menu item on the left sidebar, choose the presentation you need from the list of recently edited documents. To rename an opened presentation In the online editor click the presentation name at the top of the page, enter a new presentation name, press Enter to accept the changes. To open the folder where the file is stored in a new browser tab in the online version, in the file explorer window in the desktop version, click the Open file location icon on the right side of the editor header. Alternatively, you can switch to the File tab of the top toolbar and select the Open file location option."
    },
   {
        "id": "UsageInstructions/PhotoEditor.htm", 
        "title": "Edit an image", 
        "body": "ONLYOFFICE Presentation Editor comes with a very powerful photo editor, that allows you to adjust the image with filters and make all kinds of annotations. Select an image in your presentation. Switch to the Plugins tab and choose Photo Editor. You are now in the editing environment. Below the image you will find the following checkboxes and slider filters: Grayscale, Sepia, Sepia 2, Blur, Emboss, Invert, Sharpen; Remove White (Threshhold, Distance), Gradient transparency, Brightness, Noise, Pixelate, Color Filter; Tint, Multiply, Blend. Below the filters you will find buttons for Undo, Redo and Resetting; Delete, Delete all; Crop (Custom, Square, 3:2, 4:3, 5:4, 7:5, 16:9); Flip (Flip X, Flip Y, Reset); Rotate (30 degree, -30 degree,Manual rotation slider); Draw (Free, Straight, Color, Size slider); Shape (Recrangle, Circle, Triangle, Fill, Stroke, Stroke size); Icon (Arrows, Stars, Polygon, Location, Heart, Bubble, Custom icon, Color); Text (Bold, Italic, Underline, Left, Center, Right, Color, Text size); Mask. Feel free to try all of these and remember you can always undo them. When finished, click the OK button. The edited picture is now included in the presentation."
    },
   {
        "id": "UsageInstructions/PreviewPresentation.htm", 
        "title": "Preview your presentation", 
        "body": "Start the preview Note: If you download a presentation that was created using a third-party application, you can see a preview of the animation effects, if any. To preview the current presentation in the Presentation Editor, you can: click the Start slideshow icon on the Home tab of the top toolbar or on the left side of the status bar, or select a certain slide within the slide list on the left, right-click it and choose the Start Slideshow option from the contextual menu. The preview will start from the currently selected slide. You can also click the arrow next to the Start slideshow icon on the Home tab of the top toolbar and select one of the available options: Show from Beginning - to start the preview from the very first slide, Show from Current slide - to start the preview from the currently selected slide, Show presenter view - to start the preview in the Presenter mode that allows you to show the presentation to your audience without slide notes while viewing the presentation with the slide notes on a different monitor. Show Settings - to open the settings window that allows you to set only one option: Loop continuously until 'Esc' is pressed. Check this option if necessary and click OK. If you enable this option, the presentation will be displayed until you press the Escape key, i.e. when the last slide of the presentation is reached, you will be able to go to the first slide again, etc. If you disable this option, once the last slide of the presentation is reached, a black screen will appear indicating that the presentation is finished, and you can exit from the Preview. Use the Preview mode In the Preview mode, you can use the following controls at the bottom left corner: the Previous slide button allows you to return to the previous slide. the Pause presentation button allows you to stop previewing. When the button is pressed, it turns into the button. the Start presentation button allows you to resume previewing. When the button is pressed, it turns into the button. the Next slide button allows you to advance to the following slide. the Slide number indicator displays the current slide number as well as the overall number of slides in the presentation. To go to a certain slide in the preview mode, click on the Slide number indicator, enter the necessary slide number in the opened window and press Enter. the Full screen button allows you to switch to the full screen mode. the Exit full screen button allows you to exit the full screen mode. the Close slideshow button allows you to exit the preview mode. You can also use the keyboard shortcuts to navigate between the slides in the preview mode. Use the Presenter mode Note: in the desktop version, the presenter mode can be activated only if the second monitor is connected. In the Presenter mode, you can view your presentations with slide notes in a separate window, while demonstrating it without notes on a different monitor. The notes for each slide are displayed below the slide preview area. To navigate among the slides, you can use the and buttons or click slides in the list on the left. The hidden slide numbers are crossed out in the slide list on the left. If you wish to show a slide marked as hidden to others, just click it in the slide list on the left - the slide will be displayed. You can use the following controls below the slide preview area: the Timer displays the elapsed time of the presentation in the hh.mm.ss format. the Pause presentation button allows you to stop previewing. When the button is pressed, it turns into the button. the Start presentation button allows you to resume previewing. When the button is pressed, it turns into the button. the Reset button allows to reset the elapsed time of the presentation. the Previous slide button allows you to return to the previous slide. the Next slide button allows you to advance to the following slide. the Slide number indicator displays the current slide number as well as the overall number of slides in the presentation. the Pointer button allows you to highlight something on the screen when showing the presentation. When this option is enabled, the button looks like this: . To point some objects, hover your mouse pointer over the slide preview area and move the pointer around the slide. The pointer will look the following way: . To disable this option, click the button once again. the End slideshow button allows you to exit the Presenter mode."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Save/print/download your presentation", 
        "body": "Saving By default, the online Presentation Editor automatically saves your file every 2 seconds when you are working on it preventing your data loss if the program closes unexpectedly. If you co-edit the file in the Fast mode, the timer requests for updates 25 times a second and saves the changes if there are any. When the file is co-edited in the Strict mode, changes are automatically saved within 10-minute intervals. If you need, you can easily select the preferred co-editing mode or disable the Autosave feature on the Advanced Settings page. To save your presentation manually in the current format and location, press the Save icon on the left side of the editor header, or use the Ctrl+S key combination, or click the File tab of the top toolbar and select the Save option. In the desktop version, to prevent data loss if the program closes unexpectedly, you can turn on the Autorecover option on the Advanced Settings page. In the desktop version, you can save the presentation under a different name, in a new location or format, click the File tab of the top toolbar, select the Save as option, choose one of the available formats depending on your needs: PPTX, ODP, PDF, PDF/A, PNG, JPG. You can also choose the Рresentation template (POTX or OTP) option. Downloading In the online version, you can download the resulting presentation onto the hard disk drive of your computer, click the File tab of the top toolbar, select the Download as option, choose one of the available formats depending on your needs: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. Saving a copy In the online version, you can save a copy of the file on your portal, click the File tab of the top toolbar, select the Save Copy as option, choose one of the available formats depending on your needs: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. select a location of the file on the portal and press Save. Printing To print out the current presentation, click the Print icon on the left side of the editor header, or use the Ctrl+P key combination, or click the File tab of the top toolbar and select the Print option. The Firefox browser enables printing without downloading the document as a .pdf file first. It's also possible to print the selected slides using the Print Selection option from the contextual menu both in the Edit and View modes (Right Mouse Button Click on the selected slides and choose option Print selection). In the desktop version, the file will be printed directly. In the online version, a PDF file based on your presentation will be generated. You can open and print it out, or save onto the hard disk drive of your computer or a removable medium to print it out later. Some browsers (e.g. Chrome and Opera) support direct printing."
    },
   {
        "id": "UsageInstructions/SetSlideParameters.htm", 
        "title": "Set slide parameters", 
        "body": "To customize your presentation in the Presentation Editor, you can select a theme, color scheme, slide size and orientation for the entire presentation, change the background fill or slide layout for each separate slide, apply transitions between the slides. It's also possible to add explanatory notes to each slide that can be helpful when demonstrating the presentation in the Presenter mode. Themes allow you to quickly change the presentation design, notably the slides background appearance, predefined fonts for titles and texts and the color scheme that is used for the presentation elements. To select a theme for the presentation, click on the necessary predefined theme from the themes gallery on the right side of the top toolbar on the Home tab. The selected theme will be applied to all the slides if you have not previously selected certain slides to apply the theme to. To change the selected theme for one or more slides, you can right-click the selected slides in the list on the left (or right-click a slide in the editing area), select the Change Theme option from the contextual menu and choose the necessary theme. Color Schemes affect the predefined colors used for the presentation elements (fonts, lines, fills etc.) and allow you to maintain color consistency throughout the entire presentation. To change a color scheme, click the Change color scheme icon on the Home tab of the top toolbar and select the necessary scheme from the drop-down list. The selected color scheme will be highlighted in the list and applied to all the slides. To change the size of all the slides in the presentation, click the Select slide size icon on the Home tab of the top toolbar and select the necessary option from the drop-down list. You can select: one of the two quick-access presets - Standard (4:3) or Widescreen (16:9), the Advanced Settings option that opens the Slide Size Settings window where you can select one of the available presets or set a Custom size specifying the desired Width and Height values. The available presets are: Standard (4:3), Widescreen (16:9), Widescreen (16:10), Letter Paper (8.5x11 in), Ledger Paper (11x17 in), A3 Paper (297x420 mm), A4 Paper (210x297 mm), B4 (ICO) Paper (250x353 mm), B5 (ICO) Paper (176x250 mm), 35 mm Slides, Overhead, Banner. The Slide Orientation menu allows changing the currently selected orientation type. The default orientation type is Landscape that can be switched to Portrait. To change a background fill: in the slide list on the left, select the slides you want to apply the fill to. Or click at any blank space within the currently edited slide in the slide editing area to change the fill type for this separate slide. on the Slide settings tab of the right sidebar, select the necessary option: Color Fill - select this option to specify the solid color you want to apply to the selected slides. Gradient Fill - select this option to fill the slide with two colors which smoothly change from one to another. Picture or Texture - select this option to use an image or a predefined texture as the slide background. Pattern - select this option to fill the slide with a two-colored design composed of regularly repeated elements. No Fill - select this option if you don't want to use any fill. Opacity - drag the slider or enter the percent value manually. The default value is 100%. It corresponds to the full opacity. The 0% value corresponds to the full transparency. For more detailed information on these options, please refer to the Fill objects and select colors section. Transitions help make your presentation more dynamic and keep your audience's attention. To apply a transition: in the slide list on the left, select the slides you want to apply a transition to, choose a transition in the Effect drop-down list on the Slide settings tab, To open the Slide settings tab, you can click the Slide settings icon on the right or right-click the slide in the slide editing area and select the Slide Settings option from the contextual menu. adjust the transition properties: choose a transition variation, duration and the way to advance slides, click the Apply to All Slides button if you want to apply the same transition to all slides in the presentation. For more detailed information on these options, please refer to the Apply transitions section. To change a slide layout: in the slide list on the left, select the slides you want to apply a new layout to, click the Change slide layout icon on the Home tab of the top toolbar, select the necessary layout from the menu. Alternatively, you can right-click the necessary slide in the list on the left or in the editing area, select the Change Layout option from the contextual menu and choose the necessary layout. Currently, the following layouts are available: Title Slide, Title and Content, Section Header, Two Content, Comparison, Title Only, Blank, Content with Caption, Picture with Caption, Title and Vertical Text, Vertical Title and Text. To add objects to a slide layout: click the Change slide layout icon and select a layout you want to add an object to, using the Insert tab of the top toolbar, add the necessary object to the slide (image, table, chart, shape), then right-click on this object and select Add to Layout option, on the Home tab, click Change slide layout and apply the changed layout. The selected objects will be added to the current theme's layout. Objects placed on a slide this way cannot be selected, resized, or moved. To return the slide layout to its original state: in the slide list on the left, select the slides that you want to return to the default state, Hold down the Ctrl key and select one slide at a time to select several slides at once, or hold down the Shift key to select all slides from the current to the selected. right-click on one of the slides and select the Reset slide option in the context menu, All text frames and objects located on slides will be reset and situated in accordinance with the slide layout. To add notes to a slide: in the slide list on the left, select the slide you want to add a note to, click the Click to add notes caption below the slide editing area, type in the text of your note. You can format the text using the icons on the Home tab of the top toolbar. When you start the slideshow in the Presenter mode, you will be able to see all the slide notes below the slide preview area."
    },
   {
        "id": "UsageInstructions/SupportSmartArt.htm", 
        "title": "Support of SmartArt in ONLYOFFICE Presentation Editor", 
        "body": "SmartArt graphics are used to create a visual representation of a hierarchical structure by choosing a layout that fits best. ONLYOFFICE Presentation Editor supports SmartArt graphics that were inserted using third-party editors. You can open a file containing SmartArt and edit it as a graphic object using the available editing tools. Once you click the SmartArt graphic border or the border of its element, the following tabs become active on the right sidebar to customize a layout: Slide settings to change the slide background fill, opacity, and to show or to hide slide number, date and time. See Set Slide Parameters and Insert footers for details. Shape settings to configure the shapes used on a layout. You can change shapes, edit the fill, the lines, the wrapping style, the position, the weights and arrows, the text box and the alternative text. Paragraph settings to configure indents and spacing, fonts and tabs. See Text formatting section for a detailed description of every option. This tab becomes active for SmartArt elements only. Text Art settings to configure the Text Art style that is used in a SmartArt graphic to highlight the text. You can change the Text Art template, the fill type, color and opacity, the line size, color and type. This tab becomes active for SmartArt elements only. Right-click the border of a SmartArt graphic or its element to access the following formatting options: Arrange to arrange the objects using the following options: Bring to Foreground, Send to Background, Bring Forward and Bring Backward are available for SmartArt. Group and Ungroup are available for the SmartArt elements and depend on whether they are grouped or not. Align to align the graphic or the objects using the following options: Aligh Left, Align Center, Align Right, Align Top, Align Middle, Align Bottom, Distribute Horizontally, and Distribute Vertically. Rotate to choose the rotation direction for the selected element on a SmartArt graphic: Rotate 90° Clockwise, Rotate 90° Counterclockwise.The Rotate option becomes active for SmartArt elements only. Shape Advanced Settings to access additional shape formatting options. Add Comment to leave a comment to a certain SmartArt graphic or its element, Add to layout to add the SmartArt graphic to the slide layout. Right-click a SmartArt graphic element to access the following text formatting options: Vertical Alignment to choose the text alignment inside the selected SmarArt element: Align Top, Align Middle, Align Bottom. Text Direction to choose the text direction inside the selected SmarArt element: Horizontal, Rotate Text Down, Rotate Text Up. Paragraph Advanced Settings to access additional paragraph formatting options. Add Comment to leave a comment to a certain SmartArt graphic or its element. Hyperlink to add a hyperlink to the SmartArt element."
    },
   {
        "id": "UsageInstructions/Thesaurus.htm", 
        "title": "Replace a word by a synonym", 
        "body": "If you are using the same word multiple times, or a word is just not quite the word you are looking for, ONLYOFFICE Presentation Editor lets you look up synonyms. It will show you the antonyms too. Select the word in your presentation. Switch to the Plugins tab and choose Thesaurus. The synonyms and antonyms will show up in the left sidebar. Click a word to replace the word in your presentation."
    },
   {
        "id": "UsageInstructions/Translator.htm", 
        "title": "Translate text", 
        "body": "In the Presentation Editor, you can translate your presentation from and to numerous languages. Select the text that you want to translate. Switch to the Plugins tab and choose Translator, the Translator appears in a sidebar on the left. Click the drop-down box and choose the preferred language. The text will be translated to the required language. Changing the language of your result: Click the drop-down box and choose the preferred language. The translation will change immediately."
    },
   {
        "id": "UsageInstructions/ViewPresentationInfo.htm", 
        "title": "View the information about your presentation", 
        "body": "To access the detailed information about the currently edited presentation in the Presentation Editor, click the File tab of the top toolbar and select the Presentation Info option. General Information The presentation information includes a number of file properties which describe the presentation. Some of these properties are updated automatically, and some of them can be edited. Location - the folder in the Documents module where the file is stored. Owner - the name of the user who has created the file. Uploaded - the date and time when the file has been created. These properties are available in the online version only. Title, Subject, Comment - these properties allow you to simplify the classification of your presentations. You can specify the necessary text in the properties fields. Last Modified - the date and time when the file was last modified. Last Modified By - the name of the user who has made the latest change to the presentation if it was shared and can be edited by several users. Application - the application the presentation was created with. Author - the person who has created the file. You can enter the necessary name in this field. Press Enter to add a new field that allows you to specify one more author. If you changed the file properties, click the Apply button to apply the changes. Online Editors allow you to change the presentation title directly from the editor interface. To do that, click the File tab of the top toolbar and select the Rename option, then enter the necessary File name in a new window that opens and click OK. Permission Information In the online version, you can view the information about permissions to the files stored in the cloud. This option is not available for users with the Read Only permissions. To find out who has the rights to view or edit the presentation, select the Access Rights... option on the left sidebar. You can also change currently selected access rights by pressing the Change access rights button in the Persons who have rights section. Version History In the online version, you can view the version history for the files stored in the cloud. This option is not available for users with the Read Only permissions. To view all the changes made to this presentation, select the Version History option at the left sidebar. It's also possible to open the history of versions using the Version History icon on the Collaboration tab of the top toolbar. You'll see the list of this presentation versions (major changes) and revisions (minor changes) with the indication of each version/revision author and creation date and time. For presentation versions, the version number is also specified (e.g. ver. 2). To know exactly which changes have been made in each separate version/revision, you can view the one you need by clicking it on the left sidebar. The changes made by the version/revision author are marked with the color which is displayed next to the author's name on the left sidebar. You can use the Restore link below the selected version/revision to restore it. To return to the current version of the presentation, use the Close History option on the top of the version list. To close the File pane and return to presentation editing, select the Close Menu option."
    },
   {
        "id": "UsageInstructions/YouTube.htm", 
        "title": "Include a video", 
        "body": "In the Presentation Editor, you can include a video in your presentation. It will be shown as an image. By double-clicking the image the video dialog opens. Here you can start the video. Copy the URL of the video you want to include. (the complete address shown in the address line of your browser) Go to your presentation and place the cursor at the location where you want to include the video. Switch to the Plugins tab and choose YouTube. Paste the URL and click OK. Check if it is the correct video and click the OK button below the video. The video is now included in your presentation."
    }
]