﻿<!DOCTYPE html>
<html>
	<head>
		<title>KÜRZEN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>KÜRZEN-Funktion</h1>
			<p>Die Funktion <b>KÜRZEN</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie gibt eine Zahl zurück, die auf die angegebene Stellenzahl abgeschnitten (gekürzt) wird.</p>
			<p>Die Formelsyntax der Funktion <b>KÜRZEN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>KÜRZEN(Zahl;[Anzahl_Stellen])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist die Zahl, deren Stellen Sie abschneiden möchten.</p> 
			<p style="text-indent: 50px;"><b><em>Anzahl_Stellen</em></b> ist eine Zahl, die angibt, wie viele Nachkommastellen erhalten bleiben sollen. Dieses Argument ist optional. Wenn das Argument nicht angegeben wird, nimmt die Funktion den Standardwert 0 an.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>KÜRZEN</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>KÜRZEN</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="KÜRZEN-Funktion" src="../images/trunc.png" /></p>
		</div>
	</body>
</html>