﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZEILEN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ZEILEN-Funktion</h1>
			<p>Die Funktion <b>ZEILEN</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Sie gibt die Anzahl der Zeilen in einem Bezug oder einer Matrix zurück.</p>
			<p>Die Formelsyntax der Funktion <b>ZEILEN</b> lautet:</p> 
			<p style="text-indent: 150px;"><b><em>ZEILEN(Matrix)</em></b></p> 
			<p>Dabei ist <b><em>Matrix</em></b> eine Matrixformel oder ein Bezug auf einen Zellbereich, dessen Zeilenanzahl Sie abfragen möchten.</p>
			<p>Anwenden der Funktion <b>ZEILEN</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste.</li>
			<li>Klicken Sie auf die Funktion <b>ZEILEN</b>.</li>
			<li>Geben Sie das gewünschte Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ZEILEN-Funktion" src="../images/rows.png" /></p>
		</div>
	</body>
</html>