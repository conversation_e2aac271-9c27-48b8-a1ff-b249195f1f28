{"cancelButtonText": "Atcelt", "Common.Controllers.Chat.notcriticalErrorTitle": "Warning", "Common.Controllers.Chat.textEnterMessage": "Ievadiet savu ziņu <PERSON>eit", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "No borders", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "No borders", "Common.UI.ComboDataView.emptyComboText": "No styles", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Current", "Common.UI.ExtendedColorDialog.textHexErr": "The entered value is incorrect.<br>Please enter a value between 000000 and FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "The entered value is incorrect.<br>Please enter a numeric value between 0 and 255.", "Common.UI.HSBColorPicker.textNoColor": "Nav kr<PERSON><PERSON>", "Common.UI.SearchDialog.textHighlight": "Highlight results", "Common.UI.SearchDialog.textMatchCase": "Case sensitive", "Common.UI.SearchDialog.textReplaceDef": "Enter the replacement text", "Common.UI.SearchDialog.textSearchStart": "Enter your text here", "Common.UI.SearchDialog.textTitle": "Find and Replace", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "Whole words only", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "Replace All", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON><PERSON><PERSON> nerād<PERSON>t šo zi<PERSON>u", "Common.UI.SynchronizeTip.textSynchronize": "Doku<PERSON> ir mainījies.<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentu, lai red<PERSON> i<PERSON>.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON>", "Common.UI.Window.cancelButtonText": "Cancel", "Common.UI.Window.closeButtonText": "Close", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Don't show this message again", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Warning", "Common.UI.Window.yesButtonText": "Yes", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "address: ", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "<PERSON><PERSON><PERSON> komentāru dokumentam", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "Atcelt", "Common.Views.Comments.textClose": "Aizvērt", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "Rediģēt", "Common.Views.Comments.textEnterCommentHint": "Ievadiet J<PERSON> koment<PERSON>", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Atvērt vēlreiz", "Common.Views.Comments.textReply": "Atbildēt", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CopyWarningDialog.textDontShow": "Don't show this message again", "Common.Views.CopyWarningDialog.textMsg": "Drošības apsvērumu dēļ labo klikšķi izvēlnē ir atslēgti kopēt un ielīmēt funkcijas. J<PERSON><PERSON> joprojām varat darīt to, izman<PERSON><PERSON>t savu tastatūru:", "Common.Views.CopyWarningDialog.textTitle": "ONLYOFFICE Kopēt & Ielīmēt Funkcijas", "Common.Views.CopyWarningDialog.textToCopy": "lai noko<PERSON>tu", "Common.Views.CopyWarningDialog.textToCut": "for Cut", "Common.Views.CopyWarningDialog.textToPaste": "lai i<PERSON><PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "Loading...", "Common.Views.DocumentAccessDialog.textTitle": "Sharing Settings", "Common.Views.Header.labelCoUsersDescr": "Dokumentu šobrīd tiek rediģē vairāki lietotāji.", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "Iet uz Dokumenti", "Common.Views.Header.textCompactView": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveBegin": "Sagla<PERSON><PERSON><PERSON><PERSON><PERSON>...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Visas izmaiņas saglabātas", "Common.Views.Header.textSaveExpander": "Visas izmaiņas saglabātas", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipGoEdit": "Rediģēt šībrīža failu", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "Atsaukt", "Common.Views.Header.tipViewUsers": "<PERSON><PERSON><PERSON><PERSON>t lietotājus un pārvaldīt dokumentu piekļ<PERSON>", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ImageFromUrlDialog.textUrl": "Ielīmēt attēla URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> la<PERSON> ir <PERSON>", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON> lauks jāb<PERSON>t URL formātā \"http://www.example.com\"", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> failu", "Common.Views.OpenDialog.txtColon": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.Views.OpenDialog.txtIncorrectPwd": "Parole nav pareiza.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON>eva<PERSON>t paroli, lai atvērtu failu", "Common.Views.OpenDialog.txtOther": "Citi", "Common.Views.OpenDialog.txtPassword": "Parole", "Common.Views.OpenDialog.txtPreview": "Priekšskatījums", "Common.Views.OpenDialog.txtSemicolon": "Semikols", "Common.Views.OpenDialog.txtSpace": "Space", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Choose %1 options", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fails", "Common.Views.PasswordDialog.txtDescription": "<PERSON> pasa<PERSON>tu <PERSON>o dokumentu, uz<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtIncorrectPwd": "Aps<PERSON>rinājuma <PERSON>", "Common.Views.PasswordDialog.txtPassword": "Parole", "Common.Views.PasswordDialog.txtRepeat": "Atk<PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtWarning": "Uzmanību! Pazaudētu vai aizmirstu paroli nevar atgūt. Glabājiet drošā vietā.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar paroli", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vai d<PERSON>st paroli", "Common.Views.Protection.hintSignature": "<PERSON><PERSON><PERSON> para<PERSON>tu vai paraksta līniju", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignature": "Paraksts", "Common.Views.Protection.txtSignatureLine": "Paraksta līnija", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "<PERSON>aila nosaukums nedrīkst saturēt šādas z<PERSON>:", "Common.Views.ReviewChanges.hintNext": "Uz nākamo izmaiņu", "Common.Views.ReviewChanges.hintPrev": "Uz iepriekšē<PERSON> izmaiņu", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Kopīga rediģēšana reāllaikā. Visas izmaiņas tiek automātiski saglabātas.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"Saglabā<PERSON>\" tausti<PERSON><PERSON>, lai sinhronizētu sevis un citu veiktās izmaiņas.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON>g<PERSON> rediģēšanas režīmu", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> versiju vēsturi", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON><PERSON><PERSON> š<PERSON>brīža izmaiņas", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.tipReviewView": "Izvēlēties režīmu, kurā vēlaties atainot izmaiņas", "Common.Views.ReviewChanges.tipSetDocLang": "Uzst<PERSON><PERSON><PERSON>t dokumenta valodu", "Common.Views.ReviewChanges.tipSetSpelling": "Pareizrakstī<PERSON>", "Common.Views.ReviewChanges.tipSharing": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Pieņemt visas izmaiņas", "Common.Views.ReviewChanges.txtAcceptChanges": "Pieņemt izmaiņas", "Common.Views.ReviewChanges.txtAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.txtChat": "Čats", "Common.Views.ReviewChanges.txtClose": "Aizvērt", "Common.Views.ReviewChanges.txtCoAuthMode": "Ko<PERSON>īgā<PERSON> rediģēšanas režīms", "Common.Views.ReviewChanges.txtDocLang": "Valoda", "Common.Views.ReviewChanges.txtFinal": "Visas izmaiņ<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtFinalCap": "Gala", "Common.Views.ReviewChanges.txtHistory": "Versiju vēsture", "Common.Views.ReviewChanges.txtMarkup": "Visa<PERSON> <PERSON><PERSON><PERSON> (rediģēšana)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Visas izmai<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtOriginalCap": "Oriģināls", "Common.Views.ReviewChanges.txtPrev": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Noraidīt visas izmaiņas", "Common.Views.ReviewChanges.txtRejectChanges": "Nora<PERSON><PERSON><PERSON> izmaiņ<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON><PERSON><PERSON> š<PERSON>brīža izmaiņas", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Pareizrakstī<PERSON>", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.txtView": "Attēlošanas režī<PERSON>", "Common.Views.SignDialog.textBold": "Treknraksts", "Common.Views.SignDialog.textCertificate": "sert<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Ievadiet parakstīt<PERSON><PERSON> vārdu", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textPurpose": "Šī dokumenta parakstīšanas mērķis", "Common.Views.SignDialog.textSelect": "Izvēlēties", "Common.Views.SignDialog.textSelectImage": "Izvēlēties attēlu", "Common.Views.SignDialog.textSignature": "Kā izskatās paraksts", "Common.Views.SignDialog.textTitle": "Parakstīt dokumentu", "Common.Views.SignDialog.textUseImage": "vai spiediet \"Izvē<PERSON>ē<PERSON> attēlu\", lai i<PERSON><PERSON>tu attēlu kā parakstu", "Common.Views.SignDialog.textValid": "Derīgs no %1 līdz %2", "Common.Views.SignDialog.tipFontName": "Fonts", "Common.Views.SignDialog.tipFontSize": "Fonta izmērs", "Common.Views.SignSettingsDialog.textAllowComment": "Atļaut parakstī<PERSON><PERSON><PERSON>m pievienot komentāru paraksta logā", "Common.Views.SignSettingsDialog.textInfoEmail": "E-pasts", "Common.Views.SignSettingsDialog.textInfoName": "Name", "Common.Views.SignSettingsDialog.textInfoTitle": "Parakstī<PERSON><PERSON><PERSON> amats", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> datumu paraksta līnijā", "Common.Views.SignSettingsDialog.textTitle": "Paraksta uzstādīšana", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> la<PERSON> ir j<PERSON>", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Centrā", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> kolonnu", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON> rindu", "SSE.Controllers.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.errorInvalidLink": "<PERSON><PERSON> atsauce nepastā<PERSON>. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON> saiti vai to dz<PERSON><PERSON><PERSON>.", "SSE.Controllers.DocumentHolder.guestText": "Guest", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON> kolonna", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> augst<PERSON>k", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.leftText": "<PERSON> kreisi", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.rightText": "Pa labi", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Column Width {0} symbols ({1} pixels)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Row Height {0} points ({1} pixels)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Press CTRL and click link", "SSE.Controllers.DocumentHolder.textInsertLeft": "Insert Left", "SSE.Controllers.DocumentHolder.textInsertTop": "Insert Top", "SSE.Controllers.DocumentHolder.textSym": "sim", "SSE.Controllers.DocumentHolder.tipIsLocked": "This element is being edited by another user.", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON>not līniju k<PERSON> apakšējā stūrī", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> lī<PERSON>ju kreisajā augšējā stūrī", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Saskaņot ar simbolu", "SSE.Controllers.DocumentHolder.txtBorderProps": "Robežu parametri", "SSE.Controllers.DocumentHolder.txtBottom": "Apakšā", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Kolonnas izlīdzināšana", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Samazināt argumenta izmēru", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Dzēst argumentu", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON><PERSON> man<PERSON><PERSON><PERSON> at<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON><PERSON> iet<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON><PERSON> ietvero<PERSON> zīmes un atdalītājus", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Dzēst vienādojumu", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON><PERSON> simbolu", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON><PERSON> radi<PERSON>", "SSE.Controllers.DocumentHolder.txtExpand": "<PERSON><PERSON><PERSON><PERSON><PERSON> un šķirot", "SSE.Controllers.DocumentHolder.txtExpandSort": "Dati blakus izvēlētajam laukam netiks šķiroti. Vai vēlaties paplašināt izvēlēto diapazonu, lai tajā atrastos blakus esošie dati vai turpinātn ar šobrīd izvēlētajām šūnām?", "SSE.Controllers.DocumentHolder.txtFractionLinear": "<PERSON>īt uz lineāru da<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON> uz <PERSON> da<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON><PERSON> uz vert<PERSON>", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Simbols virs teksta", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Simbols zem teksta", "SSE.Controllers.DocumentHolder.txtHeight": "Height", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Nerādīt a<PERSON>šējo ierobežojumu", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideLB": "Nerādīt kreiso apakšējo līniju", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "Nerā<PERSON><PERSON>t k<PERSON>o augšējo līniju", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> labo <PERSON>", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>š<PERSON> ierobežojumu", "SSE.Controllers.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Palielināt argumenta izmēru", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Ievietot argumentu pēc", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Ievietot argumentu pirms", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Ieviet<PERSON> man<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Ievietot vien<PERSON><PERSON><PERSON><PERSON> p<PERSON>c", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Ievietot vienā<PERSON><PERSON><PERSON> pirms", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON> v<PERSON>u", "SSE.Controllers.DocumentHolder.txtLimitOver": "Robeža p<PERSON>", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Robeža zem teksta", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON><PERSON> argumenta augstumam", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "Nav variantu šū<PERSON> a<PERSON>.<br>Tikai teksts no kolonnas var tikt ievietots.", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula bez robežām", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + kolonnas platums", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Gala formatējums", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Ielīmēt tikai formatējumu", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + skait<PERSON>u formāts", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Ielīmēt tikai formulu", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + viss formatējums", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> saiti", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteMerge": "<PERSON><PERSON><PERSON><PERSON> nosacījuma <PERSON>ēju<PERSON>", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Avotu formatēšana", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Vērtība + viss formatējums", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Vērtība + skaitļa formatējums", "SSE.Controllers.DocumentHolder.txtPasteValues": "Ielīmēt tikai vērtību", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Atsaukt tabulas automā<PERSON>ko <PERSON>", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "SSE.Controllers.DocumentHolder.txtRemLimit": "Noņemt limitu", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Noņemt diakritisko zīmi", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Noņ<PERSON><PERSON> j<PERSON>", "SSE.Controllers.DocumentHolder.txtRemScripts": "<PERSON><PERSON><PERSON><PERSON> sk<PERSON>tus", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Noņemt apakšrakstu", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Noņemt augšrakstu", "SSE.Controllers.DocumentHolder.txtRowHeight": "Row Height", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skripti p<PERSON> te<PERSON>ta", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Skripti pirms teksta", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> ierobežojumu", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "SSE.Controllers.DocumentHolder.txtSorting": "Šķirošana", "SSE.Controllers.DocumentHolder.txtSortSelected": "Šķirot izvēlēto", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtTop": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtUnderbar": "Josla zem teksta", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Atsaukt tabulas automā<PERSON>ko <PERSON>", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.newDocumentTitle": "Izklājlapa bez <PERSON>ukuma", "SSE.Controllers.LeftMenu.textByColumns": "By columns", "SSE.Controllers.LeftMenu.textByRows": "By rows", "SSE.Controllers.LeftMenu.textFormulas": "Formulas", "SSE.Controllers.LeftMenu.textItemEntireCell": "Entire cell contents", "SSE.Controllers.LeftMenu.textLookin": "Look in", "SSE.Controllers.LeftMenu.textNoTextFound": "Teksts nav atrasts", "SSE.Controllers.LeftMenu.textReplaceSkipped": "The replacement has been made. Some occurrences were skipped.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "All occurrences have been replaced", "SSE.Controllers.LeftMenu.textSearch": "Search", "SSE.Controllers.LeftMenu.textSheet": "Sheet", "SSE.Controllers.LeftMenu.textValues": "Values", "SSE.Controllers.LeftMenu.textWarning": "Warning", "SSE.Controllers.LeftMenu.textWithin": "Within", "SSE.Controllers.LeftMenu.textWorkbook": "Workbook", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ja jūs izvēlēsieties turpināt saglabāt šajā formātā visas diagrammas un attēli tiks zaudēti.<br>Vai tiešām vēlaties turpināt?", "SSE.Controllers.Main.confirmMoveCellRange": "The destination cell range can contain data. Continue the operation?", "SSE.Controllers.Main.confirmPutMergeRange": "Avotu dati saturēja sapludin<PERSON> šū<PERSON>.<br>Tie tika atvienoti pirms ielīm<PERSON><PERSON><PERSON> tabulā.", "SSE.Controllers.Main.convertationTimeoutText": "Konversijas ta<PERSON> p<PERSON>.", "SSE.Controllers.Main.criticalErrorExtText": "Press \"OK\" to return to document list.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.downloadTextText": "Le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>...", "SSE.Controllers.Main.downloadTitleText": "Le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "SSE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON><PERSON> mēģināt veikt da<PERSON>, kuru nedrīkstat veikt.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "SSE.Controllers.Main.errorArgsRange": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON>r <PERSON><PERSON><PERSON><PERSON> nederīgs argumentu diapazons.", "SSE.Controllers.Main.errorAutoFilterChange": "The operation is not allowed, as it is attempting to shift cells in a table on your worksheet.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "The operation could not be done for the selected cells as you cannot move a part of the table.<br>Select another data range so that the whole table was shifted and try again.", "SSE.Controllers.Main.errorAutoFilterDataRange": "The operation could not be done for the selected range of cells.<br>Select a uniform data range inside or outside the table and try again.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "The operation cannot be performed because the area contains filtered cells.<br>Please unhide the filtered elements and try again.", "SSE.Controllers.Main.errorBadImageUrl": "Image URL is incorrect", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Server connection lost. The document cannot be edited right now.", "SSE.Controllers.Main.errorConnectToServer": "Dokumentu neizdevās nog<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet savienojuma uzstādījumus vai sazinieties ar savu administratoru.<br><PERSON><PERSON><PERSON><PERSON><PERSON> \"OK\", jūs varēsit lejupielādēt dokumentu.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Šo komandu nevar izman<PERSON>t nesaistītiem diapazoniem.<br>Izvēlieties vienu diapazonu un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorCountArg": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> nepareizs argumentu skaits.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON>r <PERSON><PERSON><PERSON><PERSON> argumentu s<PERSON>.", "SSE.Controllers.Main.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON><PERSON> savienojuma kļūda. <PERSON><PERSON><PERSON><PERSON>, sazinieties ar atbalstu.", "SSE.Controllers.Main.errorDataRange": "Incorrect data range.", "SSE.Controllers.Main.errorDefaultMessage": "Kļūdas kods: %1", "SSE.Controllers.Main.errorFilePassProtect": "Fails ir aizsargāts ar paroli un to nevar atvērt.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> pie<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, sazinieties ar atbalstu.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON><PERSON>.<br>Nederīgs drošības kods. <PERSON><PERSON><PERSON><PERSON>, sazinieties ar atbalstu.", "SSE.Controllers.Main.errorFillRange": "Could not fill the selected range of cells.<br>All the merged cells need to be the same size.", "SSE.Controllers.Main.errorForceSave": "Faila noglab<PERSON><PERSON><PERSON> laikā radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> iespēju \"Lejupielādēt kā\", lai noglabātu failu datora cietajā diskā, vai mēģiniet vēlāk vēlreiz.", "SSE.Controllers.Main.errorFormulaName": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON><PERSON> <PERSON><PERSON><PERSON> nepareizs formulas nosaukums.", "SSE.Controllers.Main.errorFormulaParsing": "Notika iekšēja kļūda analizējot formulu.", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funkcija attiecas uz lapu, kas ne<PERSON>t<PERSON>v.<br><PERSON><PERSON><PERSON><PERSON>, pārbaudiet datus un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorInvalidRef": "Enter a correct name for the selection or a valid reference to go to.", "SSE.Controllers.Main.errorKeyEncrypt": "Unknown key descriptor", "SSE.Controllers.Main.errorKeyExpire": "Key descriptor expired", "SSE.Controllers.Main.errorLockedAll": "The operation could not be done as the sheet has been locked by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "<PERSON><PERSON><PERSON> ne<PERSON>at <PERSON>t datus, kas atrodas \"pivot\" tabulā.", "SSE.Controllers.Main.errorLockedWorksheetRename": "The sheet cannot be renamed at the moment as it is being renamed by another user", "SSE.Controllers.Main.errorMaxPoints": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sērijas skaits diagrammā ir 4096.", "SSE.Controllers.Main.errorMoveRange": "Cannot change part of a merged cell", "SSE.Controllers.Main.errorOpenWarning": "The length of one of the formulas in the file exceeded<br>the allowed number of characters and it was removed.", "SSE.Controllers.Main.errorOperandExpected": "Operand expected", "SSE.Controllers.Main.errorPasteMaxRange": "The copy and paste area does not match.<br>Please select an area with the same size or click the first cell in a row to paste the copied cells.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Diemžēl vienlaikus nav iespējams izdrukāt vairāk par 1500 lapām ar šībrīža programmas versiju.<br>Šis ierobežojums tiks noņemts jaunākajām versijām.", "SSE.Controllers.Main.errorProcessSaveResult": "Saving failed", "SSE.Controllers.Main.errorServerVersion": "Redaktora versija ir at<PERSON>. Lapa tiks pā<PERSON>, lai pie<PERSON>rotu i<PERSON>.", "SSE.Controllers.Main.errorSessionAbsolute": "Beidzies dokumenta rediģēšanas sesijas laiks. <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON><PERSON> lapu.", "SSE.Controllers.Main.errorSessionIdle": "Dokuments nav ticis rediģēts ilgāku laiku. <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON><PERSON> lapu.", "SSE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON><PERSON><PERSON>ts savienojums serverim. <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON><PERSON> lapu.", "SSE.Controllers.Main.errorStockChart": "Neder<PERSON><PERSON> rindu kārt<PERSON>ba. Lai izveidotu akciju diagrammu novietojiet datus lapā šādā secībā:<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Controllers.Main.errorToken": "Nav pareizi noformēts dokumenta drošības marķieri.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumenta servera administratoru.", "SSE.Controllers.Main.errorTokenExpire": "Ir beid<PERSON>t dokumenta drošības marķiera termiņš. <br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "SSE.Controllers.Main.errorUnexpectedGuid": "<PERSON><PERSON><PERSON><PERSON>.<br>Ned<PERSON>īgs GUID. <PERSON>, sazinieties ar atbalstu.", "SSE.Controllers.Main.errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "SSE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "SSE.Controllers.Main.errorUsersExceed": "Count of users was exceed", "SSE.Controllers.Main.errorViewerDisconnect": "Pārtraukts savienojums. <PERSON><PERSON><PERSON>m varat aplūkot dokumentu,<br>ta<PERSON>u nevarē<PERSON>t lejupielādēt vai drukāt, līdz nav atjaunots savienojums.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> nepareiz<PERSON> i<PERSON> s<PERSON>.", "SSE.Controllers.Main.errorWrongOperator": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> nepareizs operātors.", "SSE.Controllers.Main.leavePageText": "Jums ir nesaglabātas izmaiņas šajā izklājlapā. Nooklikšķiniet 'Stay on this Page' pēc tam 'Saglabāt' lai saglabātu izmaiņas. Nospiediet 'Leave this Page' lai atceltu visas izmaiņas.", "SSE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Controllers.Main.loadFontTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> attēlus...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> attē<PERSON>...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON> k<PERSON>", "SSE.Controllers.Main.openTextText": "Atvēras Izklājlapa...", "SSE.Controllers.Main.openTitleText": "Atvēras Izklājlapa", "SSE.Controllers.Main.pastInMergeAreaError": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Controllers.Main.printTextText": "<PERSON><PERSON><PERSON>...", "SSE.Controllers.Main.printTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "SSE.Controllers.Main.requestEditFailedMessageText": "Someone is editing this document right now. Please try again later.", "SSE.Controllers.Main.requestEditFailedTitleText": "Access denied", "SSE.Controllers.Main.saveErrorText": "<PERSON>aila nogla<PERSON><PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON>", "SSE.Controllers.Main.saveTextText": "Saglabā Izklājlapu...", "SSE.Controllers.Main.saveTitleText": "Saglabā Izklā<PERSON>lapu", "SSE.Controllers.Main.textAnonymous": "Anonymous", "SSE.Controllers.Main.textBuyNow": "Apmeklēt vietni", "SSE.Controllers.Main.textCloseTip": "Click to close the tip", "SSE.Controllers.Main.textConfirm": "Confirmation", "SSE.Controllers.Main.textContactUs": "Sazināties ar p<PERSON><PERSON>", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textNo": "No", "SSE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE pieslēguma ierobežojums", "SSE.Controllers.Main.textPleaseWait": "The operation might take more time than expected. Please wait...", "SSE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textStrict": "Strict mode", "SSE.Controllers.Main.textText": "Teksts", "SSE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "SSE.Controllers.Main.textYes": "Yes", "SSE.Controllers.Main.titleLicenseExp": "Licencei beid<PERSON>", "SSE.Controllers.Main.titleServerVersion": "Atjaunināts redaktors", "SSE.Controllers.Main.txtAccent": "Ak<PERSON>s", "SSE.Controllers.Main.txtArt": "Your text here", "SSE.Controllers.Main.txtBasicShapes": "Basic Shapes", "SSE.Controllers.Main.txtButtons": "Buttons", "SSE.Controllers.Main.txtCallouts": "Callouts", "SSE.Controllers.Main.txtCharts": "Charts", "SSE.Controllers.Main.txtDiagramTitle": "Diagram Title", "SSE.Controllers.Main.txtEditingMode": "Set editing mode...", "SSE.Controllers.Main.txtFiguredArrows": "Figured Arrows", "SSE.Controllers.Main.txtLines": "Lines", "SSE.Controllers.Main.txtMath": "Math", "SSE.Controllers.Main.txtRectangles": "Rectangles", "SSE.Controllers.Main.txtSeries": "Series", "SSE.Controllers.Main.txtStarsRibbons": "Stars & Ribbons", "SSE.Controllers.Main.txtStyle_Bad": "Slik<PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "Aprēķins", "SSE.Controllers.Main.txtStyle_Check_Cell": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Paskaidrojums", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Virsraksts 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Virsraksts 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Virsraksts 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Virsraksts 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Neutral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Normal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "Izvade", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Title": "Nosa<PERSON>ms", "SSE.Controllers.Main.txtStyle_Total": "Kopā", "SSE.Controllers.Main.txtStyle_Warning_Text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "SSE.Controllers.Main.txtXAxis": "X Axis", "SSE.Controllers.Main.txtYAxis": "Y Axis", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> p<PERSON>kprogramma nav atbalstīta.", "SSE.Controllers.Main.uploadImageExtMessage": "Nezinā<PERSON> attēla formāts.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Nav aug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> i<PERSON> ir pā<PERSON>.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> attē<PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "SSE.Controllers.Main.warnBrowserIE9": "The application has low capabilities on IE9. Use IE10 or higher", "SSE.Controllers.Main.warnBrowserZoom": "Pārlūkprogrammas pašreizējais tālummaiņas iestatījums netiek pilnībā atbalstīts. Lū<PERSON><PERSON> atiestatīt noklusēju<PERSON> tā<PERSON>, nospiežot Ctrl+0.", "SSE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON><PERSON> licencei ir beid<PERSON> term<PERSON>.<br><PERSON><PERSON><PERSON><PERSON>, atjauniniet savu licenci un pārlādējiet lapu.", "SSE.Controllers.Main.warnNoLicense": "Šai %1 editors versijai ir noteikti ierobežojumi saistībā ar vienlaicīgu pieslēgšanos dokumentu serverim.<br>Ja jums ir nepiecieša<PERSON> vair<PERSON>k, l<PERSON><PERSON><PERSON>, apsveriet Jūsu šībrīža licences līmeņa paaugstināšanu vai komerciālās licences iegādi.", "SSE.Controllers.Main.warnNoLicenseUsers": "Šai %1 editors versijai ir noteikti ierobežojumi saistībā ar vairāku lietotāju vienlaicīgu darbību.<br>Ja jums ir nepiecieša<PERSON> vairāk, l<PERSON><PERSON><PERSON>, apsveriet paaugstināt šībrīža licences līmeni vai komerciālās licences iegādi.", "SSE.Controllers.Main.warnProcessRightsChange": "You have been denied the right to edit the file.", "SSE.Controllers.Print.strAllSheets": "All Sheets", "SSE.Controllers.Print.textWarning": "Warning", "SSE.Controllers.Print.warnCheckMargings": "Margins are incorrect", "SSE.Controllers.Statusbar.errorLastSheet": "Darbgrāmatai jāb<PERSON>t vismaz viena redzama darblapa.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Cannot delete the worksheet.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON>", "SSE.Controllers.Statusbar.warnDeleteSheet": "Darblapa var saturēt datus. Vai tiešām vēlaties turpināt?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the system fonts, the saved font will be used when it is available.<br>Do you want to continue?", "SSE.Controllers.Toolbar.errorMaxRows": "KĻŪDA! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu sēriju skaits diagrammā ir 255", "SSE.Controllers.Toolbar.errorStockChart": "Neder<PERSON><PERSON> rindu kārt<PERSON>ba. Lai izveidotu akciju diagrammu novietojiet datus lapā šādā secībā:<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Controllers.Toolbar.textAccent": "Uzsvari", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFontSizeErr": "The entered value is incorrect.<br>Please enter a numeric value between 1 and 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Funkcijas", "SSE.Controllers.Toolbar.textIntegral": "<PERSON>teg<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Lielie operatori", "SSE.Controllers.Toolbar.textLimitAndLog": "Robežas un logaritmi", "SSE.Controllers.Toolbar.textLongOperation": "Ilga darbība", "SSE.Controllers.Toolbar.textMatrix": "Matricas", "SSE.Controllers.Toolbar.textOperator": "Operatori", "SSE.Controllers.Toolbar.textPivot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textSymbols": "Simboli", "SSE.Controllers.Toolbar.textWarning": "Warning", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON>z<PERSON>var<PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON>a augšā pa kreisi", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON>a augšā pa kreisi", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON>a augšā pa labi", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Apakš<PERSON>la", "SSE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Formula rāmī (ar vietturi)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula rāmī (piemērs)", "SSE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Apak<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Apvie<PERSON>jo<PERSON><PERSON> iekava no augšas", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektors A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC ar aug<PERSON><PERSON><PERSON> j<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y ar jos<PERSON> pāri", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Trīspunkte", "SSE.Controllers.Toolbar.txtAccent_DDot": "Divpunkte", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> j<PERSON> p<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Grupēt apakšējo <PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Grupēt au<PERSON>š<PERSON>", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa kreisi", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa labi", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON><PERSON><PERSON> ar <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (divi no<PERSON><PERSON><PERSON><PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (trī<PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Steka objekts", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Steka objekts", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "G<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtExpand": "<PERSON><PERSON><PERSON><PERSON><PERSON> un šķirot", "SSE.Controllers.Toolbar.txtExpandSort": "Dati blakus izvēlētajam laukam netiks šķiroti. Vai vēlaties paplašināt izvēlēto diapazonu, lai tajā atrastos blakus esošie dati vai turpinātn ar šobrīd izvēlētajām šūnām?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Diagon<PERSON><PERSON> dalījums", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionPi_2": "<PERSON> dalīts ar divi", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Apgriez<PERSON>s k<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperboliskais apgrieztais kosīnuss", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Apgrieztais kotange<PERSON>s", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperboliskais apgrieztais kotangenss", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Atpgrieztais kosekanss", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperboliskais apgrieztais kosekanss", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Apgrieztais se<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperboliskais apgrieztais sekanss", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON>pg<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperboliskais apgrieztais sīnuss", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Apgriez<PERSON><PERSON> tan<PERSON>s", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperboliskais apgrieztais tangenss", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hiperboliskais kosīnuss", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangenss", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hiperboliskais kotangenss", "SSE.Controllers.Toolbar.txtFunction_Csc": "Kosekanss", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hiperboliskā kosekanse", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensa formula", "SSE.Controllers.Toolbar.txtFunction_Sec": "Sekanss", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hiperboliskais sekanss", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hiperboliskais sīnuss", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hiperboliskais tangenss", "SSE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferenci<PERSON><PERSON> dθ", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferenciā<PERSON> x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferen<PERSON><PERSON><PERSON> y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Ķīlis", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Ķīlis", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Ķīlis", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Ķīlis", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Ķīlis", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Kopražojums", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Kopražojums", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Kopražojums", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Kopražojums", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Kopražojums", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkts", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-veida", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-veida", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-veida", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-veida", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-veida", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkts", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkts", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkts", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkts", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkts", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summa", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limits", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Nat<PERSON><PERSON><PERSON>s logaritms", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritms", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritms", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimums", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Tukša matrica 1 x 2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Tukša matrica 1 x 3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Tukša matrica 2 x 1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON>kša matrica 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON>kša matrica 2 x 3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Tukša matrica 3 x 1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Tukša matrica 3 x 2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Tukša matrica 3 x 3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pamatlīnijas punkti", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Vid<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Retā matrica", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Retā matrica", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Identitātes matrica 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Identitātes matrica 3 x 3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Identitātes matrica 3 x 3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Identitātes matrica 3 x 3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Bulta apakšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON>a augšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Bulta apakšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON>a augšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Bulta apakšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON>a augšā pa labi", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Divpunktu vienāds", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Izeja", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Izejas delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON><PERSON> pēc defin<PERSON>", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta vienāda ar", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Bulta apakšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON>a augšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Bulta apakšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON>a augšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Bulta apakšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON>a augšā pa labi", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Vienāds vienāds", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus vienāds", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar pak<PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Apakšteksts", "SSE.Controllers.Toolbar.txtScriptSubSup": "Apakšraksts-augšraksts", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Kreisais apakšraksts-augšraksts", "SSE.Controllers.Toolbar.txtScriptSup": "Augšraksts", "SSE.Controllers.Toolbar.txtSorting": "Šķirošana", "SSE.Controllers.Toolbar.txtSortSelected": "Šķirot izvēlēto", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> ar ", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operators-zvaigznīte", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Aizzīmes operators", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Viduslī<PERSON>jas horizontālā elipse", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "Hi", "SSE.Controllers.Toolbar.txtSymbol_cong": "Aptuveni vienāds ar", "SSE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Diagon<PERSON><PERSON><PERSON> elipse lejā pa labi", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> le<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identisks ar", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_forall": "Visiem", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> par", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON> nekā", "SSE.Controllers.Toolbar.txtSymbol_in": "elements", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Bezgalība", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Bulta pa kreisi", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Bulta pa labi un kreisi", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON>āk nekā vai vienāds ar", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON><PERSON><PERSON> nek<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON><PERSON> par", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Mīnuss pluss", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Nav vien<PERSON>ds ar", "SSE.Controllers.Toolbar.txtSymbol_ni": "Satur kā dalībnieks", "SSE.Controllers.Toolbar.txtSymbol_not": "Negatī<PERSON><PERSON> z<PERSON>me", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Nepastāv", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus un mīnus", "SSE.Controllers.Toolbar.txtSymbol_propto": "<PERSON>por<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> sakne", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON> <PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Diagonā<PERSON>ā elipse augšā pa labi", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ro", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Bulta pa labi", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon (variants)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Fi (variants)", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi (variants)", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ro (variants)", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma (variants)", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta (variants)", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elipse", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ksi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON><PERSON><PERSON><PERSON>, ko gras<PERSON><PERSON> veikt, var prasīt diezgan daudz laika.<br>Vai vēlaties turpināt?", "SSE.Controllers.Toolbar.warnMergeLostData": "Only the data from the upper-left cell will remain in the merged cell. <br>Are you sure you want to continue?", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Custom Filter", "SSE.Views.AutoFilterDialog.textAddSelection": "Pievienot izvēlēto fragmentu filtram", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Select All", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Izvē<PERSON>ē<PERSON> visus meklē<PERSON> rezultātus", "SSE.Views.AutoFilterDialog.textWarning": "Warning", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON> vidējā", "SSE.Views.AutoFilterDialog.txtBegins": "<PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON><PERSON> vidēj<PERSON>", "SSE.Views.AutoFilterDialog.txtBetween": "Starp...", "SSE.Views.AutoFilterDialog.txtClear": "Nodzēst visu", "SSE.Views.AutoFilterDialog.txtContains": "Satur...", "SSE.Views.AutoFilterDialog.txtEmpty": "Enter cell filter", "SSE.Views.AutoFilterDialog.txtEnds": "<PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtEquals": "Vienāds...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc <PERSON> kr<PERSON>s", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc fonta krā<PERSON>s", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON><PERSON> par...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON><PERSON><PERSON> par vai vienāds ar..", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON>zā<PERSON> par...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Ma<PERSON><PERSON><PERSON> par vai vienāds ar...", "SSE.Views.AutoFilterDialog.txtNotBegins": "<PERSON><PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtNotContains": "Nesatur...", "SSE.Views.AutoFilterDialog.txtNotEnds": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Nav vienāds ar...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Numuru filtrs", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON><PERSON><PERSON><PERSON> no jauna", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Šķirot pēc šūnu kr<PERSON>s", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Šķirot pēc fonta krāsas", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Šķirot no augstākā uz zemāko", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Šķirot no zemākā uz augstāko", "SSE.Views.AutoFilterDialog.txtTextFilter": "Teksta filtrs", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtTop10": "Pirmie 10", "SSE.Views.AutoFilterDialog.warnNoSelected": "You must choose at least one value", "SSE.Views.CellEditor.textManager": "Name Manager", "SSE.Views.CellEditor.tipFormula": "Insert Function", "SSE.Views.CellRangeDialog.errorMaxRows": "ERROR! The maximum number of data series per chart is 255", "SSE.Views.CellRangeDialog.errorStockChart": "Neder<PERSON><PERSON> rindu kārt<PERSON>ba. Lai izveidotu akciju diagrammu novietojiet datus lapā šādā secībā:<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Views.CellRangeDialog.txtEmpty": "This field is required", "SSE.Views.CellRangeDialog.txtInvalidRange": "ERROR! Invalid cells range", "SSE.Views.CellRangeDialog.txtTitle": "Select Data Range", "SSE.Views.ChartSettings.strLineWeight": "Līnijas biezums", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textAdvanced": "Show advanced settings", "SSE.Views.ChartSettings.textBorderSizeErr": "Ievadītā vērtība nav pareiza.<br><PERSON><PERSON><PERSON><PERSON>, ievadiet vērtību starp 0 pt un 1584 pt.", "SSE.Views.ChartSettings.textChartType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "SSE.Views.ChartSettings.textEditData": "Edit Data", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON>rmais <PERSON>", "SSE.Views.ChartSettings.textHeight": "Height", "SSE.Views.ChartSettings.textHighPoint": "Augstais punkts", "SSE.Views.ChartSettings.textKeepRatio": "Constant Proportions", "SSE.Views.ChartSettings.textLastPoint": "<PERSON>ē<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textMarkers": "Marķieri", "SSE.Views.ChartSettings.textNegativePoint": "Negatīvais punkts", "SSE.Views.ChartSettings.textRanges": "Datu diapazons", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "Size", "SSE.Views.ChartSettings.textStyle": "Style", "SSE.Views.ChartSettings.textType": "Veids", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "KĻŪDA! <PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON><PERSON> sērijas punktu skaits diagrammā ir 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ERROR! The maximum number of data series per chart is 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Neder<PERSON><PERSON> rindu kārt<PERSON>ba. Lai izveidotu akciju diagrammu novietojiet datus lapā šādā secībā:<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartSettingsDlg.textAlt": "Alternatīvs teksts", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automā<PERSON><PERSON> katram", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Axis Crosses", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Axis Options", "SSE.Views.ChartSettingsDlg.textAxisPos": "Axis Position", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Axis Settings", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Between Tick Marks", "SSE.Views.ChartSettingsDlg.textBillions": "Billions", "SSE.Views.ChartSettingsDlg.textBottom": "Apakšā", "SSE.Views.ChartSettingsDlg.textCategoryName": "Category Name", "SSE.Views.ChartSettingsDlg.textCenter": "Center", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Chart Elements &<br>Chart Legend", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCross": "Cross", "SSE.Views.ChartSettingsDlg.textCustom": "Custom", "SSE.Views.ChartSettingsDlg.textDataColumns": "<PERSON><PERSON> s<PERSON><PERSON> kolo<PERSON>", "SSE.Views.ChartSettingsDlg.textDataLabels": "Data Labels", "SSE.Views.ChartSettingsDlg.textDataRows": "<PERSON><PERSON> s<PERSON><PERSON> rind<PERSON>s", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un tukšās <PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyLine": "<PERSON><PERSON><PERSON> datu <PERSON> ar l<PERSON>", "SSE.Views.ChartSettingsDlg.textFit": "Saskaņot ar platumu", "SSE.Views.ChartSettingsDlg.textFixed": "Fixed", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Gridlines", "SSE.Views.ChartSettingsDlg.textGroup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grupa", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON>de", "SSE.Views.ChartSettingsDlg.textHigh": "High", "SSE.Views.ChartSettingsDlg.textHorAxis": "Horizontal Axis", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Hundreds", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "In", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Inner Bottom", "SSE.Views.ChartSettingsDlg.textInnerTop": "Inner Top", "SSE.Views.ChartSettingsDlg.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.ChartSettingsDlg.textLabelDist": "Axis Label Distance", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Interval between Labels ", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Label Options", "SSE.Views.ChartSettingsDlg.textLabelPos": "Label Position", "SSE.Views.ChartSettingsDlg.textLayout": "Layout", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON> kreisi", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Left Overlay", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON> kreisi", "SSE.Views.ChartSettingsDlg.textLegendPos": "Legend", "SSE.Views.ChartSettingsDlg.textLegendRight": "Pa labi", "SSE.Views.ChartSettingsDlg.textLegendTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "Lines ", "SSE.Views.ChartSettingsDlg.textLocationRange": "Atrašanās vietas diapazons", "SSE.Views.ChartSettingsDlg.textLow": "Low", "SSE.Views.ChartSettingsDlg.textMajor": "Major", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Major and Minor", "SSE.Views.ChartSettingsDlg.textMajorType": "Major Type", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "Markers", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Interval between Marks", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maximum Value", "SSE.Views.ChartSettingsDlg.textMillions": "Millions", "SSE.Views.ChartSettingsDlg.textMinor": "Minor", "SSE.Views.ChartSettingsDlg.textMinorType": "Minor Type", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimum Value", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Next to axis", "SSE.Views.ChartSettingsDlg.textNone": "None", "SSE.Views.ChartSettingsDlg.textNoOverlay": "No Overlay", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "On Tick Marks", "SSE.Views.ChartSettingsDlg.textOut": "Out", "SSE.Views.ChartSettingsDlg.textOuterTop": "Outer Top", "SSE.Views.ChartSettingsDlg.textOverlay": "Overlay", "SSE.Views.ChartSettingsDlg.textReverse": "Values in reverse order", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Apgrieztā secībā", "SSE.Views.ChartSettingsDlg.textRight": "Pa labi", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Right Overlay", "SSE.Views.ChartSettingsDlg.textRotated": "Rotated", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON><PERSON><PERSON> visiem", "SSE.Views.ChartSettingsDlg.textSelectData": "Select Data", "SSE.Views.ChartSettingsDlg.textSeparator": "Data Labels Separator", "SSE.Views.ChartSettingsDlg.textSeriesName": "Series Name", "SSE.Views.ChartSettingsDlg.textShow": "Show", "SSE.Views.ChartSettingsDlg.textShowBorders": "Display chart borders", "SSE.Views.ChartSettingsDlg.textShowData": "<PERSON><PERSON><PERSON><PERSON><PERSON> slēpto rindu un kolonnu datus", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON><PERSON><PERSON><PERSON><PERSON> tukš<PERSON> š<PERSON> kā", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON> asi", "SSE.Views.ChartSettingsDlg.textShowValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grafika vērt<PERSON>", "SSE.Views.ChartSettingsDlg.textSingle": "Atsevišķs spārk<PERSON>s", "SSE.Views.ChartSettingsDlg.textSmooth": "Smooth", "SSE.Views.ChartSettingsDlg.textSparkRanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStraight": "Straight", "SSE.Views.ChartSettingsDlg.textStyle": "Style", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Thousands", "SSE.Views.ChartSettingsDlg.textTickOptions": "Tick Options", "SSE.Views.ChartSettingsDlg.textTitle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "S<PERSON><PERSON><PERSON><PERSON><PERSON> – papildu i<PERSON>ī<PERSON>", "SSE.Views.ChartSettingsDlg.textTop": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrillions": "Trillions", "SSE.Views.ChartSettingsDlg.textType": "Type", "SSE.Views.ChartSettingsDlg.textTypeData": "Type & Data", "SSE.Views.ChartSettingsDlg.textUnits": "Display Units", "SSE.Views.ChartSettingsDlg.textValue": "Value", "SSE.Views.ChartSettingsDlg.textVertAxis": "Vertical Axis", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X ass virsraksts", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y ass virsraksts", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON> la<PERSON> ir <PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "And", "SSE.Views.DigitalFilterDialog.capCondition1": "equals", "SSE.Views.DigitalFilterDialog.capCondition10": "does not end with", "SSE.Views.DigitalFilterDialog.capCondition11": "contains", "SSE.Views.DigitalFilterDialog.capCondition12": "does not contain", "SSE.Views.DigitalFilterDialog.capCondition2": "does not equal", "SSE.Views.DigitalFilterDialog.capCondition3": "is greater than", "SSE.Views.DigitalFilterDialog.capCondition4": "is greater than or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "is less than", "SSE.Views.DigitalFilterDialog.capCondition6": "is less than or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "begins with", "SSE.Views.DigitalFilterDialog.capCondition8": "does not begin with", "SSE.Views.DigitalFilterDialog.capCondition9": "ends with", "SSE.Views.DigitalFilterDialog.capOr": "Or", "SSE.Views.DigitalFilterDialog.textNoFilter": "no filter", "SSE.Views.DigitalFilterDialog.textShowRows": "Show rows where", "SSE.Views.DigitalFilterDialog.textUse1": "Use ? to present any single character", "SSE.Views.DigitalFilterDialog.textUse2": "Use * to present any series of character", "SSE.Views.DigitalFilterDialog.txtTitle": "Custom Filter", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON> i<PERSON>", "SSE.Views.DocumentHolder.advancedShapeText": "Shape Advanced Settings", "SSE.Views.DocumentHolder.bottomCellText": "Align Bottom", "SSE.Views.DocumentHolder.bulletsText": "Aizzīmes un numerācija", "SSE.Views.DocumentHolder.centerCellText": "Align Center", "SSE.Views.DocumentHolder.chartText": "Chart Advanced Settings", "SSE.Views.DocumentHolder.deleteColumnText": "Kolonna", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "Tabula", "SSE.Views.DocumentHolder.direct270Text": "Rotate at 270°", "SSE.Views.DocumentHolder.direct90Text": "Rotate at 90°", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Text Direction", "SSE.Views.DocumentHolder.editChartText": "Edit Data", "SSE.Views.DocumentHolder.editHyperlinkText": "Edit Hyperlink", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON> kolonna", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> augst<PERSON>k", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "Remove Hyperlink", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON>u kolonnu", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON><PERSON> dati", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "Tabula", "SSE.Views.DocumentHolder.strDelete": "Noņemt parakstu", "SSE.Views.DocumentHolder.strDetails": "Paraksta de<PERSON>ļas", "SSE.Views.DocumentHolder.strSetup": "Paraksta uzstādīšana", "SSE.Views.DocumentHolder.strSign": "Parakstīt", "SSE.Views.DocumentHolder.textArrangeBack": "Send to Background", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "Pārnest uz priekšu", "SSE.Views.DocumentHolder.textArrangeFront": "Bring to Foreground", "SSE.Views.DocumentHolder.textEntriesList": "Izvēlēties no nolaižamā saraksta", "SSE.Views.DocumentHolder.textFreezePanes": "Freeze Panes", "SSE.Views.DocumentHolder.textFromFile": "No faila", "SSE.Views.DocumentHolder.textFromUrl": "No URL", "SSE.Views.DocumentHolder.textMoreFormats": "<PERSON><PERSON><PERSON><PERSON>ātu", "SSE.Views.DocumentHolder.textNone": "neviens", "SSE.Views.DocumentHolder.textReplace": "Aizvietot attēlu", "SSE.Views.DocumentHolder.textUndo": "Atsaukt", "SSE.Views.DocumentHolder.textUnFreezePanes": "Unfreeze Panes", "SSE.Views.DocumentHolder.topCellText": "Align Top", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "Add Comment", "SSE.Views.DocumentHolder.txtAddNamedRange": "Define Name", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automātiskā saskaņ<PERSON>šana ar kolonnas platumu", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automātiska <PERSON>ņ<PERSON>šana ar rindas augstumu", "SSE.Views.DocumentHolder.txtClear": "Nodzēst visu", "SSE.Views.DocumentHolder.txtClearAll": "All", "SSE.Views.DocumentHolder.txtClearComments": "Comments", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Hyperlinks", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Att<PERSON><PERSON><PERSON><PERSON> izv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u grupas", "SSE.Views.DocumentHolder.txtClearSparklines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON>u kolonnu", "SSE.Views.DocumentHolder.txtColumnWidth": "Kolonnas platums", "SSE.Views.DocumentHolder.txtCopy": "Nokopēt", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Pielāgotais kolonnas platums", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Pielāgotais rindas augstums", "SSE.Views.DocumentHolder.txtCut": "Izgriezt", "SSE.Views.DocumentHolder.txtDate": "Datums", "SSE.Views.DocumentHolder.txtDelete": "Izdzēst", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.DocumentHolder.txtEditComment": "Edit Comment", "SSE.Views.DocumentHolder.txtFilter": "Filtrs", "SSE.Views.DocumentHolder.txtFilterCellColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc <PERSON> krā<PERSON>s", "SSE.Views.DocumentHolder.txtFilterFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc fonta krā<PERSON>s", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrēt pēc izvēlēt<PERSON><PERSON> vērtības", "SSE.Views.DocumentHolder.txtFormula": "Funkcijas ievietošana", "SSE.Views.DocumentHolder.txtFraction": "Daļskaitlis", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGroup": "Group", "SSE.Views.DocumentHolder.txtHide": "Paslēpt", "SSE.Views.DocumentHolder.txtInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hyperlink", "SSE.Views.DocumentHolder.txtNumber": "Skaits", "SSE.Views.DocumentHolder.txtNumFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtReapply": "<PERSON><PERSON><PERSON><PERSON> no jauna", "SSE.Views.DocumentHolder.txtRow": "<PERSON>isu rindu", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON> augstums", "SSE.Views.DocumentHolder.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSelect": "Izvēlēties", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz leju", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa k<PERSON>i", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Izvēlēties šūnas krāsu augšā", "SSE.Views.DocumentHolder.txtSortFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fonta krāsu augšā", "SSE.Views.DocumentHolder.txtSparklines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtText": "Teksts", "SSE.Views.DocumentHolder.txtTextAdvanced": "Text Advanced Settings", "SSE.Views.DocumentHolder.txtTime": "Laiks", "SSE.Views.DocumentHolder.txtUngroup": "Ungroup", "SSE.Views.DocumentHolder.txtWidth": "Platums", "SSE.Views.DocumentHolder.vertAlignText": "Vertical Alignment", "SSE.Views.FileMenu.btnBackCaption": "Iet uz Dokumenti", "SSE.Views.FileMenu.btnCloseMenuCaption": "Aizvērt izvēlni", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON>zve<PERSON><PERSON> jaunu", "SSE.Views.FileMenu.btnDownloadCaption": "Lejupielā<PERSON><PERSON><PERSON> kā", "SSE.Views.FileMenu.btnHelpCaption": "Palīdzī<PERSON>", "SSE.Views.FileMenu.btnInfoCaption": "Dokumenta informācija", "SSE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "Atpakaļ uz izklājlapu", "SSE.Views.FileMenu.btnRightsCaption": "Access Rights", "SSE.Views.FileMenu.btnSaveAsCaption": "Save as", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnToEditCaption": "Rediģēt dokumentu", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autors", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Change access rights", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Vieta", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON> kuriem i<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Izklājlapas Nosaukums", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Change access rights", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON> kuriem i<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Co-editing mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Fast", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formula Language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regional Settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Example: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unit of Measurement", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Noklusēju<PERSON> tā<PERSON>iņas vērtība", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Every 10 Minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Every 30 Minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Every 5 Minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Every Hour", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Automātiskā <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Autosave", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Disabled", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Noglabāt serverī", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Every Minute", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "De<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "English", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Col<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "as OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Native", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Point", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Russian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "as Windows", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON>z<PERSON><PERSON><PERSON><PERSON> rēķintabulu", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Rediģēt dokumentu", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Rediģēšana no tabulas noņems parakstus.<br>Vai tiešām vēlaties turpināt?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Šo rēķintabulu aizsargā parole", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "<PERSON><PERSON> rēķintabula ir j<PERSON>.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Rēķintabulai ir pievienoti derīgi paraksti. Rēķintabulu nevar rediģēt.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Daži rēķintabulas digitālie paraksti nav derīgi vai tos nevar pārbaud<PERSON>t. Rēķintabulu nevar rediģēt.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "SSE.Views.FormatRulesEditDlg.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.FormatSettingsDialog.textCategory": "Kategorija", "SSE.Views.FormatSettingsDialog.textDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON>zmantot tū<PERSON> at<PERSON>", "SSE.Views.FormatSettingsDialog.textSymbols": "Simboli", "SSE.Views.FormatSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON><PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "Daļskaitlis", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "Neviens", "SSE.Views.FormatSettingsDialog.txtNumber": "Skaits", "SSE.Views.FormatSettingsDialog.txtPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtSample": "Paraugs:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "Teksts", "SSE.Views.FormatSettingsDialog.txtTime": "Laiks", "SSE.Views.FormatSettingsDialog.txtUpto1": "Līdz vienam ciparam (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON><PERSON>z diviem cipariem (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Līdz trim cipariem (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Izvēlieties funkcijas grupu", "SSE.Views.FormulaDialog.textListDescription": "Izvēlieties funkciju", "SSE.Views.FormulaDialog.txtTitle": "<PERSON>ev<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON><PERSON> ar", "SSE.Views.HyperlinkSettingsDialog.strRange": "Diapazons", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Selected range", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Ievadiet parakstu šeit", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Ievadiet saiti šeit", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Ievadiet rīka padomu <PERSON>eit", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Iek<PERSON><PERSON>jo datu diapazons", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Ekrāna Padoma Teksts", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hipersait<PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> la<PERSON> ir <PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON> lauks jāb<PERSON>t URL formātā  \"http://www.example.com\"", "SSE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.ImageSettings.textEdit": "Rediģēt", "SSE.Views.ImageSettings.textEditObject": "Rediģēt objektu", "SSE.Views.ImageSettings.textFromFile": "From File", "SSE.Views.ImageSettings.textFromUrl": "From URL", "SSE.Views.ImageSettings.textHeight": "Height", "SSE.Views.ImageSettings.textInsert": "Replace Image", "SSE.Views.ImageSettings.textKeepRatio": "Constant Proportions", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textSize": "Size", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.ImageSettingsAdvanced.textTitle": "Attēls - Papildu Iestatījumi", "SSE.Views.LeftMenu.tipAbout": "About", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Comments", "SSE.Views.LeftMenu.tipFile": "Fails", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSupport": "Feedback & Support", "SSE.Views.LeftMenu.txtDeveloper": "IZSTRĀDĀTĀJA REŽĪMS", "SSE.Views.LeftMenu.txtTrial": "IZMĒĢINĀJUMA REŽĪMS", "SSE.Views.MainSettingsPrint.okButtonText": "Save", "SSE.Views.MainSettingsPrint.strBottom": "Bottom", "SSE.Views.MainSettingsPrint.strLandscape": "Landscape", "SSE.Views.MainSettingsPrint.strLeft": "Left", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Portrait", "SSE.Views.MainSettingsPrint.strPrint": "Print", "SSE.Views.MainSettingsPrint.strRight": "Right", "SSE.Views.MainSettingsPrint.strTop": "Top", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "SSE.Views.MainSettingsPrint.textFitCols": "Salikt visas kolonnas vienā lap<PERSON>", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON><PERSON> lapu vienā lap<PERSON>", "SSE.Views.MainSettingsPrint.textFitRows": "Salikt visas rindas vien<PERSON> lap<PERSON>", "SSE.Views.MainSettingsPrint.textPageOrientation": "Page Orientation", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Print Gridlines", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Print Rows and Columns Headings", "SSE.Views.MainSettingsPrint.textSettings": "Settings for", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defined name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Data Range", "SSE.Views.NamedRangeEditDlg.textExistName": "ERROR! Range with such a name already exists", "SSE.Views.NamedRangeEditDlg.textInvalidName": "ERROR! Invalid range name", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERROR! Invalid cell range", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERROR! This element is being edited by another user.", "SSE.Views.NamedRangeEditDlg.textName": "Name", "SSE.Views.NamedRangeEditDlg.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Select Data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "This field is required", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Edit Name", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "New Name", "SSE.Views.NamedRangePasteDlg.textNames": "Named Ranges", "SSE.Views.NamedRangePasteDlg.txtTitle": "Paste Name", "SSE.Views.NameManagerDlg.closeButtonText": "Close", "SSE.Views.NameManagerDlg.guestText": "Guest", "SSE.Views.NameManagerDlg.textDataRange": "Data Range", "SSE.Views.NameManagerDlg.textDelete": "Delete", "SSE.Views.NameManagerDlg.textEdit": "Edit", "SSE.Views.NameManagerDlg.textEmpty": "No named ranges have been created yet.<br>Create at least one named range and it will appear in this field.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "All", "SSE.Views.NameManagerDlg.textFilterDefNames": "Defined names", "SSE.Views.NameManagerDlg.textFilterSheet": "Names Scoped to Sheet", "SSE.Views.NameManagerDlg.textFilterTableNames": "Table names", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Names Scoped to Workbook", "SSE.Views.NameManagerDlg.textNew": "New", "SSE.Views.NameManagerDlg.textnoNames": "No named ranges matching your filter could be found.", "SSE.Views.NameManagerDlg.textRanges": "Named Ranges", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.NameManagerDlg.txtTitle": "Name Manager", "SSE.Views.ParagraphSettings.strLineHeight": "Line Spacing", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Spacing", "SSE.Views.ParagraphSettings.strSpacingAfter": "After", "SSE.Views.ParagraphSettings.strSpacingBefore": "Before", "SSE.Views.ParagraphSettings.textAdvanced": "Show advanced settings", "SSE.Views.ParagraphSettings.textAt": "At", "SSE.Views.ParagraphSettings.textAtLeast": "At least", "SSE.Views.ParagraphSettings.textAuto": "Multiple", "SSE.Views.ParagraphSettings.textExact": "Exactly", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "The specified tabs will appear in this field", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "All caps", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Double strikethrough", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Right", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indents & Placement", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Small caps", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Strikethrough", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Alignment", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Character Spacing", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Effects", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Remove", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remove All", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Specify", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Left", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tab Position", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Right", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraph - Advanced Settings", "SSE.Views.PivotSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.PivotSettings.textColumns": "Kolonnas", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laukus", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddFilter": "<PERSON><PERSON><PERSON> filt<PERSON>m", "SSE.Views.PivotSettings.txtAddRow": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddValues": "<PERSON><PERSON><PERSON> v<PERSON>", "SSE.Views.PivotSettings.txtFieldSettings": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveBegin": "Pārvietot uz sākumu", "SSE.Views.PivotSettings.txtMoveColumn": "Pārvietot uz kolonnām", "SSE.Views.PivotSettings.txtMoveDown": "Pārvietot uz leju", "SSE.Views.PivotSettings.txtMoveEnd": "Pārvietot uz beigām", "SSE.Views.PivotSettings.txtMoveFilter": "Pārvietot uz filtriem", "SSE.Views.PivotSettings.txtMoveRow": "Pārvietot uz rindām", "SSE.Views.PivotSettings.txtMoveUp": "Pārvietot uz augšu", "SSE.Views.PivotSettings.txtMoveValues": "Pārvietot uz vērtībām", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "Lielās gala vērt<PERSON>bas", "SSE.Views.PivotTable.capLayout": "Ziņojuma izklājums", "SSE.Views.PivotTable.capSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Rādīt visas apakšvērtības grupas apakšā", "SSE.Views.PivotTable.mniInsertBlankLine": "<PERSON><PERSON><PERSON><PERSON><PERSON> tukšu līniju pēc katras vienības", "SSE.Views.PivotTable.mniLayoutCompact": "<PERSON><PERSON><PERSON><PERSON><PERSON> kompaktā režīmā", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Neatkārtotu visu vienību birkas", "SSE.Views.PivotTable.mniLayoutOutline": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> re<PERSON>", "SSE.Views.PivotTable.mniLayoutRepeat": "Atkārtot visu vienību birkas", "SSE.Views.PivotTable.mniLayoutTabular": "Rā<PERSON>īt tabulas režīmā", "SSE.Views.PivotTable.mniNoSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniOffTotals": "Atspējot rindām un kolonnām", "SSE.Views.PivotTable.mniOnColumnsTotals": "Iespē<PERSON><PERSON> vien<PERSON>gi kolonn<PERSON>m", "SSE.Views.PivotTable.mniOnRowsTotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>gi rind<PERSON>m", "SSE.Views.PivotTable.mniOnTotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rindām un kolonnām", "SSE.Views.PivotTable.mniRemoveBlankLine": "Noņemt tukšo līniju pēc katras vienības", "SSE.Views.PivotTable.mniTopSubtotals": "Rādīt visas apakšvērtības grupas augšā", "SSE.Views.PivotTable.textColBanded": "<PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.PivotTable.textColHeader": "Kolonnas galvenes", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON> r<PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON> galvenes", "SSE.Views.PivotTable.tipCreatePivot": "<PERSON>ev<PERSON><PERSON> r<PERSON>", "SSE.Views.PivotTable.tipGrandTotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai slēpt kopsummas", "SSE.Views.PivotTable.tipRefresh": "Atjaunināt informāciju no datu avota", "SSE.Views.PivotTable.tipSelect": "Izvēlēties visu rakurstabulu", "SSE.Views.PivotTable.tipSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai slēpt a<PERSON>", "SSE.Views.PivotTable.txtCreate": "Ievietot tabulu", "SSE.Views.PivotTable.txtRefresh": "Atsvaidzināt", "SSE.Views.PivotTable.txtSelect": "Izvēlēties", "SSE.Views.PrintSettings.btnPrint": "Save & Print", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "Ainava", "SSE.Views.PrintSettings.strLeft": "<PERSON> kreisi", "SSE.Views.PrintSettings.strMargins": "Piemales", "SSE.Views.PrintSettings.strPortrait": "Portrets", "SSE.Views.PrintSettings.strPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "Pa labi", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textActualSize": "Actual Size", "SSE.Views.PrintSettings.textAllSheets": "All Sheets", "SSE.Views.PrintSettings.textCurrentSheet": "Current Sheet", "SSE.Views.PrintSettings.textFitCols": "Salikt visas kolonnas vienā lap<PERSON>", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON><PERSON> lapu vienā lap<PERSON>", "SSE.Views.PrintSettings.textFitRows": "Salikt visas rindas vien<PERSON> lap<PERSON>", "SSE.Views.PrintSettings.textHideDetails": "Hide Details", "SSE.Views.PrintSettings.textLayout": "Layout", "SSE.Views.PrintSettings.textPageOrientation": "Lapas orientācija", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Lapas izmērs", "SSE.Views.PrintSettings.textPrintGrid": "Druk<PERSON>t režģa līnijas", "SSE.Views.PrintSettings.textPrintHeadings": "<PERSON><PERSON><PERSON><PERSON> rindas un kollonas no<PERSON>", "SSE.Views.PrintSettings.textPrintRange": "Print Range", "SSE.Views.PrintSettings.textRange": "Diapazons", "SSE.Views.PrintSettings.textSelection": "Selection", "SSE.Views.PrintSettings.textSettings": "Lapas uzstādīju<PERSON>", "SSE.Views.PrintSettings.textShowDetails": "Show Details", "SSE.Views.PrintSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "Chart Settings", "SSE.Views.RightMenu.txtImageSettings": "Image Settings", "SSE.Views.RightMenu.txtParagraphSettings": "Text Settings", "SSE.Views.RightMenu.txtPivotSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSettings": "Common Settings", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSignatureSettings": "Paraksta uzstādījumi", "SSE.Views.RightMenu.txtSparklineSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtTableSettings": "Tabulas i<PERSON>ī<PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Text Art Settings", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērtība šajā jomā ir {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība šajā jomā ir {0}", "SSE.Views.ShapeSettings.strBackground": "Background color", "SSE.Views.ShapeSettings.strChange": "Change Autoshape", "SSE.Views.ShapeSettings.strColor": "Color", "SSE.Views.ShapeSettings.strFill": "Fill", "SSE.Views.ShapeSettings.strForeground": "Foreground color", "SSE.Views.ShapeSettings.strPattern": "Pattern", "SSE.Views.ShapeSettings.strSize": "Size", "SSE.Views.ShapeSettings.strStroke": "Stroke", "SSE.Views.ShapeSettings.strTransparency": "Opacity", "SSE.Views.ShapeSettings.strType": "Veids", "SSE.Views.ShapeSettings.textAdvanced": "Show advanced settings", "SSE.Views.ShapeSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Color Fill", "SSE.Views.ShapeSettings.textDirection": "Direction", "SSE.Views.ShapeSettings.textEmptyPattern": "No Pattern", "SSE.Views.ShapeSettings.textFromFile": "From File", "SSE.Views.ShapeSettings.textFromUrl": "From URL", "SSE.Views.ShapeSettings.textGradient": "Gradient", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.ShapeSettings.textImageTexture": "Picture or Texture", "SSE.Views.ShapeSettings.textLinear": "Linear", "SSE.Views.ShapeSettings.textNoFill": "No Fill", "SSE.Views.ShapeSettings.textOriginalSize": "Original Size", "SSE.Views.ShapeSettings.textPatternFill": "Pattern", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textSelectTexture": "Select", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "Style", "SSE.Views.ShapeSettings.textTexture": "From Texture", "SSE.Views.ShapeSettings.textTile": "Tile", "SSE.Views.ShapeSettings.txtBrownPaper": "Brown Paper", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.ShapeSettings.txtGrain": "Grain", "SSE.Views.ShapeSettings.txtGranite": "Granite", "SSE.Views.ShapeSettings.txtGreyPaper": "Gray Paper", "SSE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Leather", "SSE.Views.ShapeSettings.txtNoBorders": "No Line", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "<PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Kolonnas", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Arrows", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Begin Style", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Bottom", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Cap Type", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "End Size", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "End Style", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Flat", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Height", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Join Type", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Constant Proportions", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Left", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Line Style", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRight": "Right", "SSE.Views.ShapeSettingsAdvanced.textRound": "Round", "SSE.Views.ShapeSettingsAdvanced.textSize": "Size", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Atstarpe starp kolonn<PERSON>m", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Square", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Shape - Advanced Settings", "SSE.Views.ShapeSettingsAdvanced.textTop": "Top", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Weights & Arrows", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDelete": "Noņemt parakstu", "SSE.Views.SignatureSettings.strDetails": "Paraksta de<PERSON>ļas", "SSE.Views.SignatureSettings.strInvalid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Paraksta uzstādīšana", "SSE.Views.SignatureSettings.strSign": "Parakstīt", "SSE.Views.SignatureSettings.strSignature": "Paraksts", "SSE.Views.SignatureSettings.strSigner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "Vienalga rediģēt", "SSE.Views.SignatureSettings.txtEditWarning": "Rediģēšana no tabulas noņems parakstus.<br>Vai tiešām vēlaties turpināt?", "SSE.Views.SignatureSettings.txtRequestedSignatures": "<PERSON><PERSON> rēķintabula ir j<PERSON>.", "SSE.Views.SignatureSettings.txtSigned": "Rēķintabulai ir pievienoti derīgi paraksti. Rēķintabulu nevar rediģēt.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Daži rēķintabulas digitālie paraksti nav derīgi vai tos nevar pārbaud<PERSON>t. Rēķintabulu nevar rediģēt.", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Copy to end)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Move to end)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Copy before sheet", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Move before sheet", "SSE.Views.Statusbar.filteredRecordsText": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>: {0} no {1}", "SSE.Views.Statusbar.filteredText": "Filtra režīms", "SSE.Views.Statusbar.itemCopy": "Nokopēt", "SSE.Views.Statusbar.itemDelete": "Izdzēst", "SSE.Views.Statusbar.itemHidden": "Paslēpts", "SSE.Views.Statusbar.itemHide": "Paslēpt", "SSE.Views.Statusbar.itemInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMove": "Pārvietot", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "Tab Color", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Worksheet with such a name already exists.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "A sheet name cannot contain the following characters: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Sheet Name", "SSE.Views.Statusbar.textAverage": "AVERAGE", "SSE.Views.Statusbar.textCount": "COUNT", "SSE.Views.Statusbar.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.Statusbar.textNoColor": "No Color", "SSE.Views.Statusbar.textSum": "SUM", "SSE.Views.Statusbar.tipAddTab": "Add worksheet", "SSE.Views.Statusbar.tipFirst": "Scroll to First Sheet", "SSE.Views.Statusbar.tipLast": "Scroll to Last Sheet", "SSE.Views.Statusbar.tipNext": "Scroll Sheet List Right", "SSE.Views.Statusbar.tipPrev": "Scroll Sheet List Left", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.zoomText": "Lielums {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "The operation could not be done for the selected range of cells.<br>Select a uniform data range inside or outside the table and try again.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Darbī<PERSON> neizdevās izpildīt izvēlētajā šūnu diapazon<PERSON>.<br>Izv<PERSON><PERSON><PERSON> diapazonu, lai pirmā tabulas rinda būtu tajā pašā rindā<br> un gala tabula pārklātos ar esošo.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Darb<PERSON><PERSON> neizdevās izpild<PERSON>t izvēlētajā <PERSON>ūnu diapazon<PERSON>.<br>Izv<PERSON><PERSON><PERSON> diapazonu, kas neietver citas tabulas.", "SSE.Views.TableOptionsDialog.txtEmpty": "This field is required", "SSE.Views.TableOptionsDialog.txtFormat": "Create table", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ERROR! Invalid cells range", "SSE.Views.TableOptionsDialog.txtTitle": "Title", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> kolonnu", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON> rindu", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON> tabulu", "SSE.Views.TableSettings.insertColumnLeftText": "Ievietot kolonnu pa kreisi", "SSE.Views.TableSettings.insertColumnRightText": "Ieviet<PERSON> kolonnu pa labi", "SSE.Views.TableSettings.insertRowAboveText": "Ievietot rindu augstāk", "SSE.Views.TableSettings.insertRowBelowText": "Ievietot rindu <PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectColumnText": "Izvēlēties visu kolonnu", "SSE.Views.TableSettings.selectDataText": "Izvēlē<PERSON> kolonnas datus", "SSE.Views.TableSettings.selectRowText": "Izvēlēties rindu", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> ta<PERSON>u", "SSE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "Kolonnas", "SSE.Views.TableSettings.textConvertRange": "Konvertēt uz diapazonu", "SSE.Views.TableSettings.textEdit": "Rindas & Kolonnas", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON>z <PERSON>ē<PERSON>", "SSE.Views.TableSettings.textExistName": "KĻŪDA! Jau pastāv diapazons ar <PERSON><PERSON><PERSON> nosaukumu", "SSE.Views.TableSettings.textFilter": "Filtra poga", "SSE.Views.TableSettings.textFirst": "pir<PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "G<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "KĻŪDA! Nederīgs tabulas nosaukums", "SSE.Views.TableSettings.textIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "Ilga darbība", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, kuru mēģināt lietot, jau atrodas <PERSON>nu formulā<PERSON>. <PERSON><PERSON><PERSON><PERSON>, izvēlieties citu.", "SSE.Views.TableSettings.textResize": "Tabulas izmēra maiņa", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Views.TableSettings.textTableName": "Tabulas no<PERSON>", "SSE.Views.TableSettings.textTemplate": "Izvēlēties no veidnes", "SSE.Views.TableSettings.textTotal": "Kopā", "SSE.Views.TableSettings.warnLongOperation": "<PERSON><PERSON><PERSON><PERSON>, ko gras<PERSON><PERSON> veikt, var prasīt diezgan daudz laika.<br>Vai vēlaties turpināt?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabula - Papildu i<PERSON>ī<PERSON>", "SSE.Views.TextArtSettings.strBackground": "Background color", "SSE.Views.TextArtSettings.strColor": "Color", "SSE.Views.TextArtSettings.strFill": "Fill", "SSE.Views.TextArtSettings.strForeground": "Foreground color", "SSE.Views.TextArtSettings.strPattern": "Pattern", "SSE.Views.TextArtSettings.strSize": "Size", "SSE.Views.TextArtSettings.strStroke": "Stroke", "SSE.Views.TextArtSettings.strTransparency": "Opacity", "SSE.Views.TextArtSettings.strType": "Veids", "SSE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Color Fill", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "No Pattern", "SSE.Views.TextArtSettings.textFromFile": "From File", "SSE.Views.TextArtSettings.textFromUrl": "From URL", "SSE.Views.TextArtSettings.textGradient": "Gradient", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "No Fill", "SSE.Views.TextArtSettings.textPatternFill": "Pattern", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "Select", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "Style", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "From Texture", "SSE.Views.TextArtSettings.textTile": "Tile", "SSE.Views.TextArtSettings.textTransform": "Transform", "SSE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granite", "SSE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "SSE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Leather", "SSE.Views.TextArtSettings.txtNoBorders": "No Line", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON>", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChart": "Di<PERSON>ram<PERSON>", "SSE.Views.Toolbar.capInsertEquation": "Vienādojums", "SSE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertTable": "Tabula", "SSE.Views.Toolbar.capInsertText": "Uzraksts", "SSE.Views.Toolbar.mniImageFromFile": "Att<PERSON><PERSON> no faila", "SSE.Views.Toolbar.mniImageFromUrl": "Attēls no url", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON><PERSON><PERSON>ā<PERSON> uz lēju", "SSE.Views.Toolbar.textAlignCenter": "Līdzināt uz centru", "SSE.Views.Toolbar.textAlignJust": "Taisnot<PERSON>", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa kreisi", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz vidu", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.Toolbar.textAllBorders": "Visas Apmales", "SSE.Views.Toolbar.textBold": "Treknraksts", "SSE.Views.Toolbar.textBordersColor": "Apmales krāsa", "SSE.Views.Toolbar.textBordersStyle": "Robežas stils", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textClockwise": "<PERSON>le Clockwise", "SSE.Views.Toolbar.textCounterCw": "Angle Counterclockwise", "SSE.Views.Toolbar.textDelLeft": "Shift Cells Left", "SSE.Views.Toolbar.textDelUp": "Shift Cells Up", "SSE.Views.Toolbar.textDiagDownBorder": "Diagonal Down Border", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonal Up Border", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON>n", "SSE.Views.Toolbar.textEntireRow": "Entire Row", "SSE.Views.Toolbar.textHorizontal": "Horizontal Text", "SSE.Views.Toolbar.textInsDown": "Shift Cells Down", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInsRight": "Shift Cells Right", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON><PERSON><PERSON>ātu", "SSE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.Toolbar.textNoBorders": "Nav apmales", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Rotate Text Down", "SSE.Views.Toolbar.textRotateUp": "Rotate Text Up", "SSE.Views.Toolbar.textStrikeout": "Izs<PERSON>ī<PERSON>šana", "SSE.Views.Toolbar.textSubscript": "Apakšteksts", "SSE.Views.Toolbar.textSubSuperscript": "Apakšraksts/augšraksts", "SSE.Views.Toolbar.textSuperscript": "Augšraksts", "SSE.Views.Toolbar.textTabCollaboration": "Sad<PERSON>bī<PERSON>", "SSE.Views.Toolbar.textTabFile": "Fails", "SSE.Views.Toolbar.textTabHome": "Sā<PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabProtect": "Aizsardzība", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textUnderline": "Pasvītrots", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Align Bottom", "SSE.Views.Toolbar.tipAlignCenter": "Align Center", "SSE.Views.Toolbar.tipAlignJust": "Justified", "SSE.Views.Toolbar.tipAlignLeft": "Align Left", "SSE.Views.Toolbar.tipAlignMiddle": "Align Middle", "SSE.Views.Toolbar.tipAlignRight": "Align Right", "SSE.Views.Toolbar.tipAlignTop": "Align Top", "SSE.Views.Toolbar.tipAutofilter": "Sort and Filter", "SSE.Views.Toolbar.tipBack": "Atpakaļ", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Cell Style", "SSE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON><PERSON> stilu", "SSE.Views.Toolbar.tipColorSchemas": "Change Color Scheme", "SSE.Views.Toolbar.tipCopy": "Nokopēt", "SSE.Views.Toolbar.tipCopyStyle": "Copy Style", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecFont": "Decrement font size", "SSE.Views.Toolbar.tipDeleteOpt": "Delete Cells", "SSE.Views.Toolbar.tipDigStyleAccounting": "Accounting Style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Currency Style", "SSE.Views.Toolbar.tipDigStylePercent": "Percent Style", "SSE.Views.Toolbar.tipEditChart": "Edit Chart", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "Fonts", "SSE.Views.Toolbar.tipFontSize": "<PERSON>ont<PERSON> lie<PERSON>", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipIncFont": "Increment font size", "SSE.Views.Toolbar.tipInsertChart": "Ieviet<PERSON>", "SSE.Views.Toolbar.tipInsertChartSpark": "<PERSON>ev<PERSON><PERSON> diagrammu", "SSE.Views.Toolbar.tipInsertEquation": "Ievietot vienā<PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "Insert Cells", "SSE.Views.Toolbar.tipInsertShape": "Insert Autoshape", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertTextart": "Ievietot Text Art objektu", "SSE.Views.Toolbar.tipMerge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "Fona krāsa", "SSE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRedo": "Redo", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "Save your changes for the other users to see them.", "SSE.Views.Toolbar.tipSynchronize": "The document has been changed by another user. Please click to save your changes and reload the updates.", "SSE.Views.Toolbar.tipTextOrientation": "Orientation", "SSE.Views.Toolbar.tipUndo": "Undo", "SSE.Views.Toolbar.tipWrap": "Aplauzt tekstu", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Comments", "SSE.Views.Toolbar.txtClearFilter": "Clear Filter", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "Formula", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearText": "Teksts", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Custom", "SSE.Views.Toolbar.txtDate": "Datums", "SSE.Views.Toolbar.txtDateTime": "Datums & Laiks", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.Toolbar.txtDollar": "$ dolārs", "SSE.Views.Toolbar.txtEuro": "€ Eiro", "SSE.Views.Toolbar.txtExp": "Eksponen<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Ievietot formulu", "SSE.Views.Toolbar.txtFraction": "Daļskaitlis", "SSE.Views.Toolbar.txtFranc": "CHF Šveices franks", "SSE.Views.Toolbar.txtGeneral": "Vispārī<PERSON>", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pāri", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Sa<PERSON><PERSON>dināt pa centru", "SSE.Views.Toolbar.txtNamedRange": "Named Ranges", "SSE.Views.Toolbar.txtNewRange": "Define Name", "SSE.Views.Toolbar.txtNoBorders": "Nav apmales", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vā<PERSON>u", "SSE.Views.Toolbar.txtPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPound": "£ mārciņa", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme1": "Jewels", "SSE.Views.Toolbar.txtScheme10": "Median", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Opulent", "SSE.Views.Toolbar.txtScheme14": "Oriel", "SSE.Views.Toolbar.txtScheme15": "Origin", "SSE.Views.Toolbar.txtScheme16": "Paper", "SSE.Views.Toolbar.txtScheme17": "Solstice", "SSE.Views.Toolbar.txtScheme18": "Technic", "SSE.Views.Toolbar.txtScheme19": "Trek", "SSE.Views.Toolbar.txtScheme2": "<PERSON>", "SSE.Views.Toolbar.txtScheme20": "Urban", "SSE.Views.Toolbar.txtScheme21": "Verve", "SSE.Views.Toolbar.txtScheme3": "Fire Inside", "SSE.Views.Toolbar.txtScheme4": "Autumn", "SSE.Views.Toolbar.txtScheme5": "<PERSON>", "SSE.Views.Toolbar.txtScheme6": "Concourse", "SSE.Views.Toolbar.txtScheme7": "Equity", "SSE.Views.Toolbar.txtScheme8": "Flow", "SSE.Views.Toolbar.txtScheme9": "Foundry", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "Search", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Sort A to Z", "SSE.Views.Toolbar.txtSortZA": "Sort Z to A", "SSE.Views.Toolbar.txtSpecial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTableTemplate": "Format as Table Template", "SSE.Views.Toolbar.txtText": "Teksts", "SSE.Views.Toolbar.txtTime": "Laiks", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtYen": "¥ jena", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "Apakšā", "SSE.Views.Top10FilterDialog.txtItems": "Elements", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automātiskais filtrs", "SSE.Views.Top10FilterDialog.txtTop": "<PERSON><PERSON>"}