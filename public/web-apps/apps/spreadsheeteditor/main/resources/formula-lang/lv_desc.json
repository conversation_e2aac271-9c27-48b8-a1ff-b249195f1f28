{"DATE": {"a": "(gads; mēnesis; diena)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas ap<PERSON><PERSON><PERSON><PERSON> datumu datuma/laika kodā"}, "DATEDIF": {"a": "(sākuma_datums; beigu_datums; mēr<PERSON><PERSON><PERSON><PERSON>)", "d": "Aprēķina dienu, mēne<PERSON>u vai gadu skaitu starp diviem datumiem"}, "DATEVALUE": {"a": "(datums_teksts)", "d": "Pārveido datumu teksta formātā par skaitli, kas apzīmē datumu datuma/laika kodā"}, "DAY": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON> dienu - skaitli no 1 līdz 31."}, "DAYS": {"a": "(beigu_datums; sākuma_datums)", "d": "Atgriež dienu skaitu starp diviem datumiem."}, "DAYS360": {"a": "(sākuma_datums; beigu_datums; [metode])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu starp diviem da<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ka gadā ir 360 dienas (divpadsmit 30 dienu mēneši)"}, "EDATE": {"a": "(sākuma_datums; mēne<PERSON>i)", "d": "Atgriež datuma sērijas numuru, kas ir nor<PERSON><PERSON><PERSON><PERSON>s mēnešu skaits pirms vai pēc sākuma datuma"}, "EOMONTH": {"a": "(sākuma_datums; mēne<PERSON>i)", "d": "<PERSON><PERSON><PERSON><PERSON> tās mēne<PERSON> pēdējās dienas sērijas numuru, kas ir pirms vai pēc noteikta mēnešu skaita"}, "HOUR": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež stundu kā skaitli no 0 (12:00 A.M.) līdz 23 (11:00 P.M.)."}, "ISOWEEKNUM": {"a": "(datums)", "d": "Atgriež gada ISO nedēļas numuru noteiktam datumam"}, "MINUTE": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež minūti kā skaitli no 0 līdz 59."}, "MONTH": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež mēnesi - skaitli no 1 (jan<PERSON><PERSON><PERSON>) līdz 12 (de<PERSON><PERSON><PERSON>)."}, "NETWORKDAYS": {"a": "(sākuma_datums; beigu_datums; [brīvdienas])", "d": "Atgriež pilnu darbdienu skaitu starp diviem datumiem"}, "NETWORKDAYS.INTL": {"a": "(sākuma_datums; beigu_datums; [nedē<PERSON><PERSON>_nogale]; [brīv<PERSON><PERSON>])", "d": "Atgriež pilnu darbdienu skaitu starp diviem datumiem ar pielāgotu nedēļas nogales parametru"}, "NOW": {"a": "()", "d": "Atgriež šīsdienas datumu un laiku, kas formatēts kā datums un laiks."}, "SECOND": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež sekundi kā skaitli no 0 līdz 59."}, "TIME": {"a": "(stunda; minūte; sekunde)", "d": "<PERSON><PERSON><PERSON><PERSON>, min<PERSON><PERSON> un sekundes, kas norā<PERSON><PERSON><PERSON> kā skaitļi, par se<PERSON><PERSON><PERSON> skaitli, kas formatēts laika formātā"}, "TIMEVALUE": {"a": "(laiks)", "d": "<PERSON><PERSON><PERSON><PERSON> teksta laiku seriāl<PERSON> skaitl<PERSON>, kas ap<PERSON><PERSON><PERSON><PERSON> laiku, - skai<PERSON><PERSON> no 0 (00:00:00) līdz 0.999988426 (23:59:59). Formatējiet skaitli laika formātā pēc formulas ievadīšanas"}, "TODAY": {"a": "()", "d": "Atgriež pa<PERSON><PERSON><PERSON><PERSON><PERSON> datumu, kas ir formatēts kā datums."}, "WEEKDAY": {"a": "(se<PERSON><PERSON><PERSON><PERSON>_skaitlis; [atgrie<PERSON><PERSON>_tips])", "d": "Atgriež skaitli no 1 līdz 7, kas a<PERSON><PERSON><PERSON><PERSON><PERSON> ned<PERSON><PERSON><PERSON> die<PERSON>."}, "WEEKNUM": {"a": "(s<PERSON><PERSON><PERSON>_numurs; [atg<PERSON><PERSON><PERSON>_tips])", "d": "Atgriež gada nedēļas numuru"}, "WORKDAY": {"a": "(sākuma_datums; dienas; [brīvdienas])", "d": "Atgriež tā datuma sērijas numuru, kas ir pirms vai pēc noteiktā darbdienas numura"}, "WORKDAY.INTL": {"a": "(sāku<PERSON>_datums; dienas; [nedē<PERSON><PERSON>_nogale]; [brīvdienas])", "d": "Atgriež tā datuma sērijas numuru, kas ir pirms vai pēc noteiktā darbdienas numura ar pielāgotiem nedēļas nogales parametriem"}, "YEAR": {"a": "(seri<PERSON><PERSON><PERSON>_skaitlis)", "d": "Atgriež gadu - veselu skaitli robežās no 1900 līdz 9999."}, "YEARFRAC": {"a": "(sākuma_datums; beigu_datums; [pamats])", "d": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>, kas ataino veselu dienu skaitu laika posmā starp sākuma_datumu un beigu_datumu"}, "BESSELI": {"a": "(x; n)", "d": "Atgriež modificēto Beseļa funkciju In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Atgriež Beseļa funkciju Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Atgriež modificēto Beseļa funkciju Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Atgriež Beseļa funkciju Yn(x)"}, "BIN2DEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON> deci<PERSON>"}, "BIN2HEX": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "BIN2OCT": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON> okt<PERSON>"}, "BITAND": {"a": "(skaitlis1; skaitlis2)", "d": "Bitu veidā atgriež divu skaitļu vērtību \"Un\""}, "BITLSHIFT": {"a": "(skaitl<PERSON>; pārvie<PERSON>š_apjoms)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas pār<PERSON><PERSON><PERSON> pa kreisi pa pārvie<PERSON>šanas_apjoma bitiem"}, "BITOR": {"a": "(skaitlis1; skaitlis2)", "d": "Bitu veidā atgriež divu skaitļu vērtību \"Vai\""}, "BITRSHIFT": {"a": "(skaitl<PERSON>; pārvie<PERSON>š_apjoms)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas pār<PERSON><PERSON><PERSON> pa labi pa pār<PERSON><PERSON><PERSON><PERSON>_daud<PERSON>ma bitiem"}, "BITXOR": {"a": "(skaitlis1; skaitlis2)", "d": "Bitu veidā atgriež divu skaitļu vērtību \"Izņemot/Vai\""}, "COMPLEX": {"a": "(re<PERSON><PERSON>_skaitlis; i_skaitlis; [sufikss])", "d": "Kon<PERSON><PERSON> reālus un iedomātus koeficientus saliktā skaitlī"}, "CONVERT": {"a": "(skai<PERSON><PERSON>; no_vien<PERSON><PERSON>; līdz_vienībai)", "d": "Konvert<PERSON> skaitli no vienas mērvienību sistēmas citā"}, "DEC2BIN": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "DEC2HEX": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>"}, "DEC2OCT": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oktāl<PERSON>"}, "DELTA": {"a": "(skaitlis1; [skaitlis2])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai divi skaitļi ir vienādi"}, "ERF": {"a": "(apakšējā_robeža; [augšējā_robeža])", "d": "Atgriež kļūdas <PERSON>"}, "ERF.PRECISE": {"a": "(X)", "d": "Atgriež kļūdas <PERSON>"}, "ERFC": {"a": "(x)", "d": "Atgriež papildu kļūdas funkciju"}, "ERFC.PRECISE": {"a": "(x)", "d": "Atgriež papildu kļūdas funkciju"}, "GESTEP": {"a": "(skaitlis; [solis])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai skaitlis ir lielāks par sliekšņa vērtību"}, "HEX2BIN": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "HEX2DEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitli decimā<PERSON>"}, "HEX2OCT": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tli oktāl<PERSON>"}, "IMABS": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa absolūto v<PERSON> (moduli)"}, "IMAGINARY": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa iedomā<PERSON> k<PERSON>"}, "IMARGUMENT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež argumentu q, leņķi, kas izteikts radiānos"}, "IMCONJUGATE": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa saliktu konjugātu"}, "IMCOS": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa kos<PERSON>"}, "IMCOSH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa hiperbolisko kosinusu"}, "IMCOT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa kotangensu"}, "IMCSC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa kosekansu"}, "IMCSCH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa hiperbolisko kosekansu"}, "IMDIV": {"a": "(iskaitlis1; iskaitlis2)", "d": "Atgriež divu saliktu skaitļu dalījumu"}, "IMEXP": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa eksponentu"}, "IMLN": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa naturālo logaritmu"}, "IMLOG10": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa bāzes 10 logaritmu"}, "IMLOG2": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa bāzes 2 logaritmu"}, "IMPOWER": {"a": "(iskaitl<PERSON>; skaitlis)", "d": "<PERSON>g<PERSON><PERSON>, kas k<PERSON><PERSON> veselā pakāpē"}, "IMPRODUCT": {"a": "(iskaitlis1; [iskaitlis2]; ...)", "d": "Atgriež produktam saliktus skaitļus no 1 līdz 255"}, "IMREAL": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skai<PERSON>a <PERSON>"}, "IMSEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa se<PERSON>"}, "IMSECH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa hiperbolisko sekanti"}, "IMSIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa sinusu"}, "IMSINH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa hiperbolisko sinusu"}, "IMSQRT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež salikta skaitļa kvadrātsakni"}, "IMSUB": {"a": "(iskaitlis1; iskaitlis2)", "d": "Atgriež divu saliktu skaitļu starpību"}, "IMSUM": {"a": "(iskaitlis1; [iskaitlis2]; ...)", "d": "Atgriež salikta skaitļa summu"}, "IMTAN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež saliktā skaitļa tangensu"}, "OCT2BIN": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON>"}, "OCT2DEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitli decimāl<PERSON>"}, "OCT2HEX": {"a": "(skaitlis; [vietas])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tl<PERSON> he<PERSON>āl<PERSON>"}, "DAVERAGE": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina vidējo vērtību no vērtībām norādītajiem nosacījumiem atbilstošas datu bāzes ierakstu laukā (kolonnā)"}, "DCOUNT": {"a": "(datu_bāze; lauks; kritēriji)", "d": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> i<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nosa<PERSON>ījumiem atbil<PERSON> datu bāzes ierakstu la<PERSON> (kolonnā)"}, "DCOUNTA": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Saskaita netukšās <PERSON> iera<PERSON>tu lauk<PERSON> (kolonn<PERSON>) datu bāzē, kas atbilst norādītajiem kritērijiem"}, "DGET": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Izgūst no datu bāzes vienu iera<PERSON>tu, kas atbilst norādī<PERSON>jiem nosa<PERSON>ījumiem"}, "DMAX": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Atgriež vislielāko skaitli norādītajiem nosacījumiem atbilstošas datu bāzes ierakstu la<PERSON> (kolonnā)"}, "DMIN": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Atgriež vismazāko skaitli norādītajiem nosacījumiem atbilstošas datu bāzes ierakstu lauk<PERSON> (kolonnā)"}, "DPRODUCT": {"a": "(datu_bāze; lauks; kritēriji)", "d": "<PERSON><PERSON><PERSON>, kura<PERSON> atrodas datu bāzes iera<PERSON> (kolonnā), kas atbilst nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kritērijiem"}, "DSTDEV": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina <PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>to datu bāzes ievadņu i<PERSON>i"}, "DSTDEVP": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>tu datu bāzes ievadņu visu populāciju"}, "DSUM": {"a": "(datu_bāze; lauks; kritēriji)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> nor<PERSON>jiem nosacījumiem atbilstoša<PERSON> datu bāzes ierakstu laukā (kolonnā)"}, "DVAR": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>to datu bāzes ievadņu i<PERSON>i"}, "DVARP": {"a": "(datu_bāze; lauks; kritēriji)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> atlas<PERSON>tu datu bāzes ievadņu visu populāciju"}, "CHAR": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru nor<PERSON>da koda numurs datora raks<PERSON><PERSON><PERSON><PERSON> kopā"}, "CLEAN": {"a": "(teksts)", "d": "No teksta izņem visas nedrukājamā<PERSON> r<PERSON>"}, "CODE": {"a": "(teksts)", "d": "Atgriež teksta virknes pirmās rakstzīmes skaitlisko kodu datorā izmantotajā rakstzīmju kopā"}, "CONCATENATE": {"a": "(teksts1; [teksts2]; ...)", "d": "Savieno vairākas teksta virknes vienā"}, "CONCAT": {"a": "(teksts1; ...)", "d": "Savieno teksta virkņu sarakstu vai diapazonu"}, "DOLLAR": {"a": "(skai<PERSON><PERSON>; [decim<PERSON>lskaitļi])", "d": "Konvertē skaitli par tekstu, i<PERSON><PERSON><PERSON><PERSON> valūtas formātu"}, "EXACT": {"a": "(teksts1; teksts2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai divas teksta virknes ir pilnīgi vienādas un atgriež TRUE vai FALSE. Funkcija EXACT ir reģistrjutīga"}, "FIND": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "Atgriež vienas teksta virknes sākuma pozīciju citā teksta virknē. Funkcija FIND ir reģistrjutīga"}, "FINDB": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "Atgriež vienu teksta virkni otrā teksta virknē un atgriež pirmās teksta virknes sākuma pozīcijas numuru no otrās teksta virknes pirmās r<PERSON>, ir par<PERSON><PERSON><PERSON><PERSON> valo<PERSON>, k<PERSON><PERSON> i<PERSON> dub<PERSON> r<PERSON><PERSON>op<PERSON> (DBCS) - j<PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu"}, "FIXED": {"a": "(skai<PERSON><PERSON>; [deci<PERSON><PERSON><PERSON><PERSON><PERSON>]; [bez_komatiem])", "d": "Noapaļo skaitli līdz norādītajam decimāldaļu skaitam un atgriež rezultātu kā tekstu ar komatiem vai bez tiem"}, "LEFT": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> skaitu no teksta virknes sākuma"}, "LEFTB": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "Atgriež pirmo rakstzīmi vai rakstzī<PERSON> teksta virkn<PERSON>, pamatojoties uz norād<PERSON>to bait<PERSON> s<PERSON>, ir pared<PERSON><PERSON><PERSON> valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu"}, "LEN": {"a": "(teksts)", "d": "Atgriež rakstzīmju skaitu teksta virknē"}, "LENB": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, kas tiek i<PERSON><PERSON><PERSON> rakstzī<PERSON><PERSON> attēloša<PERSON> teksta virknē, ir pared<PERSON><PERSON>ta valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu"}, "LOWER": {"a": "(teksts)", "d": "Visus burtus teksta virknē konvertē par mazajiem burtiem"}, "MID": {"a": "(teksts; sākuma_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits)", "d": "Atgriež rakstzīmes no teksta virknes vidus, nor<PERSON><PERSON><PERSON> sākuma pozīciju un garumu"}, "MIDB": {"a": "(teksts; sākuma_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits)", "d": "Atgriež noteiktas rakstzī<PERSON> no teksta virknes, s<PERSON><PERSON> no norād<PERSON>tās vietas un pamatojoties uz norād<PERSON>to baitu s<PERSON>, ir pared<PERSON><PERSON>ta valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON>op<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu"}, "NUMBERVALUE": {"a": "(teksts; [decim<PERSON>lzīme]; [grupu_atdalītājs])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tekstu par skaitli no lokalizācijas neatkarīgā veidā"}, "PROPER": {"a": "(teksts)", "d": "Pārveido teksta virkni pareizajā burtu reģistrā; katra vārda pirmo burtu par lielo, bet pārējos - par mazajiem"}, "REPLACE": {"a": "(vecais_teksts; s<PERSON>ku<PERSON>_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits; jaunais_teksts)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> teksta virknes ar citu teksta virkni"}, "REPLACEB": {"a": "(vecais_teksts; s<PERSON>ku<PERSON>_num; raks<PERSON><PERSON><PERSON><PERSON>_skaits; jaunais_teksts)", "d": "Aiz<PERSON><PERSON><PERSON> teksta virknes daļu ar citu teksta virkni, pamatojoties uz norād<PERSON><PERSON> bait<PERSON> s<PERSON>, ir pared<PERSON><PERSON><PERSON> valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON>op<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu"}, "REPT": {"a": "(teksts; skaitlis_reizes)", "d": "Atkārto tekstu norād<PERSON>to re<PERSON> skaitu. Izmantojiet funkciju REPT, lai aizpild<PERSON>tu šūnu ar noteiktu skaitu teksta virkņu"}, "RIGHT": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> skaitu no teksta virknes beigām"}, "RIGHTB": {"a": "(teksts; [skait<PERSON><PERSON>_rakstz])", "d": "Atgriež pēdējo rakstzīmi vai rakstzīmes teksta virknē, pamatojoties uz norād<PERSON>to bait<PERSON> s<PERSON>, ir pared<PERSON><PERSON>ta valod<PERSON>, k<PERSON><PERSON> i<PERSON><PERSON> dub<PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON>op<PERSON> (DBCS) - j<PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu"}, "SEARCH": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "<PERSON>grie<PERSON> raks<PERSON><PERSON><PERSON> numuru, pie kuras noteikta rakstzīme vai teksta virkne atrasta pirmo reizi, lasot virzienā no kreisās uz labo pusi (nav reģistrjutīgi)"}, "SEARCHB": {"a": "(atrast_tekstu; tekstā; [sākuma_num])", "d": "Atrod vienu teksta virkni otrā teksta virknē un atgriež pirmās teksta virknes sākuma atrašanās vietas numuru, skaitot no otrās teksta virknes pirmā<PERSON> r<PERSON>, ir pared<PERSON><PERSON><PERSON> valod<PERSON>, k<PERSON><PERSON> i<PERSON> dub<PERSON> r<PERSON> kop<PERSON> (DBCS) - j<PERSON><PERSON><PERSON><PERSON>, ķīniešu un korejiešu"}, "SUBSTITUTE": {"a": "(teksts; vecais_teksts; jaunais_teksts; [gad<PERSON><PERSON><PERSON>_numurs])", "d": "Teksta virknē a<PERSON>stāj esošu tekstu ar jaunu tekstu"}, "T": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir teksts un atgriež tekstu, ja tā ir vai dubultpēdiņ<PERSON> (tuk<PERSON><PERSON> tekstu), ja tā nav"}, "TEXT": {"a": "(vērtība; formāts_teksts)", "d": "Konvertē vērtību par tekstu noteiktā skaitļu formātā"}, "TEXTJOIN": {"a": "(nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; ignorēt_tukšu; teksts1; ...)", "d": "Savieno teksta virkņu sarakstu vai di<PERSON>u, i<PERSON><PERSON><PERSON><PERSON>"}, "TRIM": {"a": "(tekstst)", "d": "<PERSON><PERSON><PERSON><PERSON> visas atstarpes no teksta virknes, iz<PERSON><PERSON><PERSON> vienplatuma atstarpes starp vārdiem"}, "UNICHAR": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež unikoda r<PERSON>, uz ko atsaucas noteiktā skaitliskā vērtība"}, "UNICODE": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON> (koda punktu), kas atbilst teksta pirmajai r<PERSON>"}, "UPPER": {"a": "(teksts)", "d": "Konvertē teksta virkni uz visiem lielajiem burtiem"}, "VALUE": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>, ka<PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON> skaitl<PERSON>, par skaitli"}, "AVEDEV": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež vidējo vērtību datu punktu absolūtajām novirzēm no vidējā. Argumenti var būt skaitļi vai nosaukumi, mas<PERSON><PERSON> vai atsauces, kur<PERSON><PERSON> i<PERSON><PERSON> skaitļi"}, "AVERAGE": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> savu <PERSON> v<PERSON> (aritmētisko), kas var būt skait<PERSON>i vai nosaukumi, mas<PERSON><PERSON> vai atsauce<PERSON>, k<PERSON><PERSON><PERSON> ir skait<PERSON>i"}, "AVERAGEA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Atgriež argumentu vid<PERSON> (vidē<PERSON> aritmētis<PERSON>), novērtējuma tekstu un FALSE argumentos kā 0; TRUE tiek novērtēts kā 1. <PERSON>rg<PERSON>nti var būt s<PERSON>, <PERSON><PERSON><PERSON><PERSON>, mas<PERSON><PERSON> vai atsauces"}, "AVERAGEIF": {"a": "(diapazons; kritēriji; [vidējais_diapazons])", "d": "<PERSON><PERSON> vid<PERSON> (vid<PERSON><PERSON>) <PERSON><PERSON><PERSON><PERSON><PERSON>, ko norāda dotais nosacījums vai kritērijs"}, "AVERAGEIFS": {"a": "(vidējais_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON> vid<PERSON> (vid<PERSON><PERSON>) <PERSON><PERSON><PERSON><PERSON><PERSON>, ko norāda dotā nosacījumu kopa vai kritēriji"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Atgriež kumulatīvo beta varbūtības blīvuma funkciju"}, "BETAINV": {"a": "(varb<PERSON><PERSON><PERSON>ba; alfa; beta; [A]; [B])", "d": "Atgriež apgrieztu kumulatīvo beta varbūtības blīvuma funkciju (BETADIST)"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulatīvā; [A]; [B])", "d": "Atgriež beta varbūtības sadalījuma funkciju"}, "BETA.INV": {"a": "(varb<PERSON><PERSON><PERSON>ba; alfa; beta; [A]; [B])", "d": "Atgriež apgrieztu kumulatīvo beta varbūtības blīvuma funkciju (BETA.DIST)"}, "BINOMDIST": {"a": "(labvēlizn_skaits; mēģinājumi; labvēlizn_varbūtība; kumulatīvā)", "d": "Atgriež atsevišķas izteiksmes binomiālā sadalījuma varbūtību"}, "BINOM.DIST": {"a": "(labvēlizn_skaits; mēģinājumi; labvēlizn_varbūtība; kumulatīvā)", "d": "Atgriež binomiālā sadalījuma atsevišķas vērtības varbūtību"}, "BINOM.DIST.RANGE": {"a": "(mēģinājumi; labvēlizn_varbūtība; izdošan<PERSON>s_skaits; [izdošan<PERSON>s_skaits2])", "d": "Atgriež mēģinājuma rezultātu i<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> binomu sadali"}, "BINOM.INV": {"a": "(mēģinājumi; labvēlizn_varbūtība; alfa)", "d": "Atgriež vismaz<PERSON><PERSON> v<PERSON>, k<PERSON> kumulatīvais binomiālais sadalījums ir lielāks vai vienāds ar kritērija vērtību"}, "CHIDIST": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež labā zara hī kvadrātā sadalījuma varbūtību"}, "CHIINV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež apgrieztu hī kvadrāta sadalījuma labā zara varbūtību"}, "CHITEST": {"a": "(faktiskais_diapazons; sagaidāmais_diapazons)", "d": "Atg<PERSON>ž neatkarības pārbaudi: statiskās un piemērotās brīvības pakāpes hī kvadrātā sadalījuma vērtību"}, "CHISQ.DIST": {"a": "(x; brīvī<PERSON>_pakāpe; kumulatīvā)", "d": "Atgriež kreisā zara hī kvadrātā sadalījuma varbūtību"}, "CHISQ.DIST.RT": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež labā zara hī kvadrātā sadalījuma varbūtību"}, "CHISQ.INV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež apgrieztu kreisā zara hī kvadrātā sadalījuma varbūtību"}, "CHISQ.INV.RT": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež apgrieztu labā zara hī kvadrātā sadalījuma varbūtību"}, "CHISQ.TEST": {"a": "(faktiskais_diapazons; sagaidāmais_diapazons)", "d": "Atg<PERSON>ž neatkarības pārbaudi: statiskās un piemērotās brīvības pakāpes hī kvadrātā sadalījuma vērtību"}, "CONFIDENCE": {"a": "(alfa; standartnovirze; lielums)", "d": "Atgriež populācijas vidējā ticamības intervālu, <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "CONFIDENCE.NORM": {"a": "(alfa; standartnovirze; lielums)", "d": "Atgriež populācijas vidējā ticamības intervālu, <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "CONFIDENCE.T": {"a": "(alfa; standartnovirze; lielums)", "d": "Atgriež populācijas vidējā ticamības intervālu, <PERSON><PERSON><PERSON><PERSON><PERSON> t <PERSON>alījumu"}, "CORREL": {"a": "(masīvs1; masīvs2)", "d": "Atgriež divu datu kopu korelācijas koeficientu"}, "COUNT": {"a": "(vērtība1; [vērtība2]; ...)", "d": "<PERSON><PERSON><PERSON>, cik <PERSON><PERSON><PERSON><PERSON><PERSON> ir s<PERSON>i"}, "COUNTA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Di<PERSON><PERSON><PERSON> sa<PERSON>, cik <PERSON><PERSON> nav tukšas"}, "COUNTBLANK": {"a": "(diapazons)", "d": "<PERSON><PERSON><PERSON>, cik tukšu šūnu ir norādītajā šūnu diapazonā"}, "COUNTIF": {"a": "(diapazons; kritēriji)", "d": "<PERSON><PERSON><PERSON>, cik <PERSON>ūnu diapazonā atbilst noteiktajam nosacījumam"}, "COUNTIFS": {"a": "(kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON>, ko norāda dotā nosacījumu kopa vai kritēriji"}, "COVAR": {"a": "(masīvs1; masīvs2)", "d": "Atgriež kovariāciju - katra divu kopu datu punktu pāra noviržu reizinājuma vidējo"}, "COVARIANCE.P": {"a": "(masīvs1; masīvs2)", "d": "Atgriež populācijas kovariāciju - katra divu kopu datu punktu pāra noviržu reizināju<PERSON> vidējo"}, "COVARIANCE.S": {"a": "(masīvs1; masīvs2)", "d": "Atgriež izlases kovariāciju - katra divu kopu datu punktu pāra noviržu reizinājuma vidējo"}, "CRITBINOM": {"a": "(izmēģinājumi; varbūtība_s; alfa)", "d": "Atgriež v<PERSON><PERSON><PERSON><PERSON> v<PERSON>, k<PERSON><PERSON> kumulat<PERSON> binomu sadalījums ir lielāks vai vienāds ar kritērija vērtību"}, "DEVSQ": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu punktu to novir<PERSON>u kvadrātu summu, kuras aprēķinātas no to izla<PERSON>u vidējā"}, "EXPONDIST": {"a": "(x; lambda; kumulatīvā)", "d": "Atgriež eksponenciālo <PERSON>"}, "EXPON.DIST": {"a": "(x; lambda; kumulatīva)", "d": "Atgriež eksponenciālo <PERSON>"}, "FDIST": {"a": "(x; brīvības_pakāpe1; brīvības_pakāpe2)", "d": "Atgriež divu datu kopu (labā zara) F varbūtības sadalījumu (atšķirības pakāpi)"}, "FINV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; brīvības_pak1; brīvības_pak2)", "d": "Atgriež apgrieztu (labā zara) F varbūtības sadalījumu: ja p = FDIST(x,...), FINV(p,...) = x"}, "FTEST": {"a": "(masīvs1; masīvs2)", "d": "Atgriež F testa rezultātus - divu zaru varbūtību, ka dispersijas Masīvā1 un Masīvā2 nav nozīmīgi atšķirīgas"}, "F.DIST": {"a": "(x; brīvības_pakāpe1; brīvības_pakāpe2; kumulatīvā)", "d": "Atgriež divu datu kopu (kreisā zara) F varbūtības sadalījumu (atšķirības pakāpi)"}, "F.DIST.RT": {"a": "(x; brīvības_pakāpe1; brīvības_pakāpe2)", "d": "Atgriež divu datu kopu (labā zara) F varbūtības sadalījumu (atšķirības pakāpi)"}, "F.INV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; brīvības_pak1; brīvības_pak2)", "d": "Atgriež apgrieztu (kreisā zara) F varbūtības sadalījumu: ja p = F.DIST(x,...), F.INV(p,...) = x"}, "F.INV.RT": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; brīvības_pak1; brīvības_pak2)", "d": "Atgriež apgrieztu (labā zara) F varbūtības sadalījumu: ja p = F.DIST.RT(x,...), F.INV.RT(p,...) = x"}, "F.TEST": {"a": "(masīvs1; masīvs2)", "d": "Atgriež F testa rezultātus - divu zaru varbūtību, ka dispersijas Masīvā1 un Masīvā2 nav nozīmīgi atšķirīgas"}, "FISHER": {"a": "(x)", "d": "Atgriež Fišera transformāciju"}, "FISHERINV": {"a": "(y)", "d": "Atgriež apgrieztu Fišera transformāciju: ja y = FISHER(x), FISHERINV(y) = x"}, "FORECAST": {"a": "(x; zināmie_y; zināmie_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> esoš<PERSON> vērt<PERSON>, aprēķina vai prognozē nākotnes vērtību saskaņā ar lineāru tendences līkni"}, "FORECAST.ETS": {"a": "(mērķa_datums; vērtības; laika_grafiks; [sezonalitāte]; [datu_p<PERSON><PERSON>na]; [apkopoju<PERSON>])", "d": "Atgriež prognozēto vērtību konkrētam nākotnes mērķa datumam, i<PERSON><PERSON><PERSON><PERSON> eksponenci<PERSON><PERSON> l<PERSON> metodi."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Atgriež ticamības intervālu prognozētajai vērtībai norādītajā mērķa datumā."}, "FORECAST.ETS.SEASONALITY": {"a": "(v<PERSON><PERSON><PERSON><PERSON>; laika_grafiks; [datu_p<PERSON><PERSON><PERSON>]; [ap<PERSON><PERSON>ju<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> tā atkārtotās modeļa garu<PERSON>, ko programma noteica norādītajai laika sērijai."}, "FORECAST.ETS.STAT": {"a": "(v<PERSON><PERSON><PERSON><PERSON>; laika_grafiks; statistikas_tips; [sezon<PERSON><PERSON><PERSON>te]; [datu_p<PERSON><PERSON><PERSON><PERSON>]; [apkopoju<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> piepra<PERSON> prognozes statistiku."}, "FORECAST.LINEAR": {"a": "(x; zināmie_y; zināmie_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> esoš<PERSON> vērt<PERSON>, aprēķina vai prognozē nākotnes vērtību saskaņā ar lineāru tendences līkni"}, "FREQUENCY": {"a": "(datu_masīvs; intervālu_masīvs)", "d": "Aprēķina, cik bieži vērtības sastopamas vērtību diapazonā un pēc tam atgriež vertikālu skaitļu masīvu, kurā ir par vienu elementu vairāk nekā Intervālu_masīvā"}, "GAMMA": {"a": "(x)", "d": "Atgriež gamma funkcijas vērtību"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež gamma sadalījumu"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež gamma sadalījumu"}, "GAMMAINV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; alfa; beta)", "d": "Atgriež apgrieztu gamma kumulatīvo sadalījumu: ja p = GAMMADIST(x,...), GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; alfa; beta)", "d": "Atgriež apgrieztu gamma kumulatīvo sadalījumu: ja p = GAMMA.DIST(x,...), GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Atgriež gamma funkcijas naturālo logaritmu"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Atgriež gamma funkcijas naturālo logaritmu"}, "GAUSS": {"a": "(x)", "d": "<PERSON>g<PERSON><PERSON> par 0,5 mazāk nekā standarta parastais kumulatīvais sadalīju<PERSON>"}, "GEOMEAN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež pozitīvu skaitlisku datu masīva vai diapazona vidējo ģeometrisko"}, "GROWTH": {"a": "(zināmie_y; [zināmie_x]; [jaunie_x]; [konst])", "d": "Atgriež skaitļus eksponenciālā progresijā saskaņā ar zināmo datu punktiem"}, "HARMEAN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež pozitīvu skaitļu kopas vidējo harmonisko: apgriezto skaitļu apgriezto vidējo aritmētisko"}, "HYPGEOM.DIST": {"a": "(izlase_labvēlizn; skaits_izlasē; populācija_labvēlizn; skaits_pop; kumulatīvā)", "d": "Atgriež hiperģeometrisko sadalījumu"}, "HYPGEOMDIST": {"a": "(izlase_labvēlizn; skaitlis_izlase; populācija_labvēlizn; skaitlis_pop)", "d": "Atgriež hiperģeometrisku sadalījumu"}, "INTERCEPT": {"a": "(zināmie_y; zināmie_x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> regres<PERSON>, kas novilkta caur zināmajām x vērtībām un y vērtībām un visvairāk tām atbilst, aprēķina punktu, kurā taisne krustosies ar y asi"}, "KURT": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu kopas ekscesa koeficientu"}, "LARGE": {"a": "(masīvs; k)", "d": "Atgriež datu kopas k-to lielāko vērtību. <PERSON><PERSON><PERSON><PERSON>, piekto liel<PERSON>ko skaitli"}, "LINEST": {"a": "(zināmie_y; [zināmie_x]; [konst]; [statist])", "d": "<PERSON><PERSON><PERSON><PERSON> statist<PERSON>, kas raksturo lineāru tendences līkni pa zināmo datu punktiem, novelkot taisni ar ma<PERSON>ā<PERSON> k<PERSON>dr<PERSON> metodi"}, "LOGEST": {"a": "(zināmie_y; [zināmie_x]; [konst]; [statist])", "d": "<PERSON><PERSON><PERSON><PERSON> statist<PERSON>, kas raksturo ar zināmajiem datu punktiem saskanošo eksponentlīkni"}, "LOGINV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež apgrieztu logaritmiski normālu kumulatīvu x sadalījuma funkciju, kur ln(x) parasti ir sadalīts ar parametriem Vidējais un Standartnovirze"}, "LOGNORM.DIST": {"a": "(x; vidē<PERSON><PERSON>; standart<PERSON>ir<PERSON>; kumulatīvā)", "d": "Atgriež x logaritmiski normā<PERSON>, kur ln(x) ir normāli sadalīts ar parametriem Vidējais un Standartnovirze"}, "LOGNORM.INV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež apgrieztu logaritmiski normālu kumulatīvu x sadalījuma funkciju, kur ln(x) ir normāli sadalīts ar parametriem Vidējais un Standartnovirze"}, "LOGNORMDIST": {"a": "(x; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež kumulatīvo logaritmiski normālo x sadalījumu, kur ln(x) parasti ir sadalīts ar parametriem Vidējais un Standartnovirze"}, "MAX": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež visliel<PERSON>ko vērtību no vērtību kopas. Ignorē loģiskās vērtības un tekstu"}, "MAXA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Atgriež lielāko vērtību kopas vērtību. Neignorē loģiskās vērtības un tekstu"}, "MAXIFS": {"a": "(maks<PERSON><PERSON><PERSON><PERSON>_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> maks<PERSON><PERSON> v<PERSON>, ko norāda attiecīgā nosacījumu vai kritēriju kopa"}, "MEDIAN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež mediānu jeb skaitli norā<PERSON><PERSON><PERSON> skait<PERSON>u kopas vidū"}, "MIN": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež vismazāko skaitli vērtību kopā. Ignorē loģiskās vērtības un tekstu"}, "MINA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Atgriež mazāko vērtību kopas vērtību. Neignorē loģiskās vērtības un tekstu"}, "MINIFS": {"a": "(minimālais_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> minim<PERSON><PERSON> v<PERSON>, ko norāda attiecīgā nosacījumu vai kritēriju kopa"}, "MODE": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu masīvā vai diapazonā visbiežāk sastopamo vai atkārtoto vērtību"}, "MODE.MULT": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež vertikālu visbiežāk sastopamo vai atkārtojošos vērtību masīvu kā datu masīvu vai diapazonu. Horizontālam masīvam izmantojiet =TRANSPOSE(MODE.MULT(skaitlis1,skaitlis2,...))"}, "MODE.SNGL": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu masīvā vai diapazonā visbiežāk sastopamo vai atkārtoto vērtību"}, "NEGBINOM.DIST": {"a": "(nelabvēlizn_skaits; labvēlizn_skaits; labvēlizn_varbūtība; kumulatīvā)", "d": "Atgriež negatīvu binomiālo <PERSON>, var<PERSON><PERSON><PERSON><PERSON><PERSON>, ka būs (nelabvēlizn_ skaits) nelabvēlīgi iznākumi pirms (labvēlizn_skaits). labvēlīgā iznākuma ar (labvēlizn_varbūtība) varbūtību"}, "NEGBINOMDIST": {"a": "(nelabvēlizn_skaits; labvēlizn_skaits; labvēlizn_varbūtība)", "d": "Atgriež negatīvu binomiālo <PERSON>, var<PERSON><PERSON><PERSON><PERSON><PERSON>, ka būs (nelabvēlizn_skaits) nelabvēlīgi iznākumi pirms (labvēlizn_skaits). labvēlīgā iznākumā ar (labvēlizn_varbūtība) varbūtību"}, "NORM.DIST": {"a": "(x; vidē<PERSON><PERSON>; standart<PERSON>ir<PERSON>; kumulatīvā)", "d": "<PERSON><PERSON><PERSON>ž norā<PERSON><PERSON><PERSON><PERSON> vidējā lie<PERSON>a un standartnovirzes norm<PERSON><PERSON> sad<PERSON>"}, "NORMDIST": {"a": "(x; vidē<PERSON><PERSON>; standart<PERSON>ir<PERSON>; kumulatīvā)", "d": "Atg<PERSON>ž norādī<PERSON><PERSON> vidējā lieluma un standartnovirzes normālo kumulatīvo sadalījumu"}, "NORM.INV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež apgrieztu norād<PERSON><PERSON><PERSON><PERSON> vidējās vērtības un standartnovirzes normālo kumulatīvo sadalījumu"}, "NORMINV": {"a": "(var<PERSON><PERSON><PERSON><PERSON><PERSON>; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atgriež nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vidējās vērtības un standartnovirzes apgrieztu normālo kumulatīvo sadalījumu"}, "NORM.S.DIST": {"a": "(z; kumulatīvā)", "d": "<PERSON>g<PERSON><PERSON> standarta norm<PERSON> (tā vidē<PERSON><PERSON> ir nulle, bet standartnovirze - viens)"}, "NORMSDIST": {"a": "(z)", "d": "Atgriež standarta normālo kum<PERSON> sadalīju<PERSON> (tā vidē<PERSON><PERSON> ir nulle, bet standartnovirze - viens)"}, "NORM.S.INV": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež apgrieztu standarta normālo kumulatīvo sadalījumu (tā vidē<PERSON>is ir nulle, bet standartnovirze - viens)"}, "NORMSINV": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Atgriež apgrieztu standarta normālo kumulatīvo sadalījumu (tā vidē<PERSON>is ir nulle, bet standartnovirze - viens)"}, "PEARSON": {"a": "(masīvs1; masīvs2)", "d": "Atgriež Pīrsona korelācijas koeficientu r"}, "PERCENTILE": {"a": "(masīvs; k)", "d": "Atgriež diapazona vērtību k-to procentili"}, "PERCENTILE.EXC": {"a": "(masīvs; k)", "d": "Atgriež diapazona vērtību k-to procentili, kur k ir diapazons no 0 līdz 1 neieskaitot"}, "PERCENTILE.INC": {"a": "(masīvs; k)", "d": "Atgriež diapazona vērtību k-to procentili, kur k ir diapazons no 0 līdz 1 ieskaitot"}, "PERCENTRANK": {"a": "(masīvs; x; [nozī<PERSON><PERSON>ba])", "d": "Atgriež datu kopā ietilps<PERSON>šas vērtības rangu procentuāli no datu kopas"}, "PERCENTRANK.EXC": {"a": "(masīvs; x; [nozī<PERSON><PERSON>ba])", "d": "Atgriež datu kopā ietilpstošas vērtības rangu kā procentu (no 0 līdz 1 neieskaitot) no datu kopas"}, "PERCENTRANK.INC": {"a": "(masīvs; x; [nozī<PERSON><PERSON>ba])", "d": "Atgriež datu kopā ietilpstošas vērtības rangu kā procentu (no 0 līdz 1 ieskaitot) no datu kopas"}, "PERMUT": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "Atgriež no visiem objektiem izvēlēta noteikta objektu skaita permutāciju skaitu"}, "PERMUTATIONA": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "Atgriež no visiem objektiem izvēlēta noteikta objektu skaita permutāciju skaitu"}, "PHI": {"a": "(x)", "d": "Atg<PERSON>ž standarta normālā sadalījuma blīvuma funkcijas vērtību"}, "POISSON": {"a": "(x; vidējais; kumulatīvā)", "d": "Atgriež Puasona sadalījumu"}, "POISSON.DIST": {"a": "(x; vidējais; kumulatīvā)", "d": "Atgriež Puasona sadalījumu"}, "PROB": {"a": "(x_diapazons; varb_diapazons; zemākā_robeža; [augstākā_robeža])", "d": "<PERSON><PERSON><PERSON><PERSON>, ka vērtības diapazonā atrodas starp abām robežām vai ir vienādas ar zemāko robežu"}, "QUARTILE": {"a": "(masīvs; kvar<PERSON>)", "d": "Atgriež datu kopas k<PERSON>i"}, "QUARTILE.INC": {"a": "(masīvs; kvar<PERSON>)", "d": "Atgriež datu k<PERSON>, izmantojot procentiles vērtības no 0 līdz 1 ieskaitot"}, "QUARTILE.EXC": {"a": "(masīvs; kvar<PERSON>)", "d": "Atgriež datu k<PERSON>, izman<PERSON>jot procentiles vērtības no 0 līdz 1 neieskaitot"}, "RANK": {"a": "(skaitlis; ats; [kārtība])", "d": "Atgriež skaitļa rangu skaitļu sarakstā: tā lielumu attiecībā pret pārējām vērtībām sarakstā"}, "RANK.AVG": {"a": "(skaitlis; ats; [kārtība])", "d": "Atgriež skaitļa rangu skaitļu sarakstā: tā lielumu attiecībā pret pārējām vērtībām sarakstā; ja vairākām vērtībām ir vienāds rangs, tiek atgriezts vidējais rangs"}, "RANK.EQ": {"a": "(skaitlis; ats; [kārtība])", "d": "Atgriež skaitļa rangu skaitļu sarakstā: tā lielumu attiecībā pret pārējām vērtībām sarakstā; ja vairākām vērtībām ir rangs, tiek atgriezts augstākais šīs vērtību kopas rangs"}, "RSQ": {"a": "(zināmie_y; zināmie_x)", "d": "Atgriež Pīrsona korelācijas koeficienta kvadrātu norādītajos datu punktos"}, "SKEW": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež sadalījuma asimetriju: sadalījuma asimetrijas pakāpi attiecībā pret vidējo"}, "SKEW.P": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež sadalī<PERSON><PERSON> asime<PERSON>, pamatojoties uz kopskaitu: sadalījuma asimetrijas pakāpi attiecībā pret vidējo"}, "SLOPE": {"a": "(zināmie_y; zināmie_x)", "d": "Atgriež lineārā<PERSON> regresijas taisnes slīpumu caur norādītajiem datu punktiem"}, "SMALL": {"a": "(masīvs; k)", "d": "Atgriež datu kopas k-to mazāko vērtī<PERSON>. <PERSON><PERSON><PERSON><PERSON>, piekto maz<PERSON>ko skaitli"}, "STANDARDIZE": {"a": "(x; vid<PERSON><PERSON><PERSON>; standart<PERSON>irze)", "d": "Atg<PERSON>ž tāda sadalījuma normalizētu vērtību, kuru raksturo vidējais un standartnovirze"}, "STDEV": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)"}, "STDEV.P": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> visu populāciju kā argumentus (ignorē loģiskās vērtības un tekstu)"}, "STDEV.S": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)"}, "STDEVA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, ies<PERSON><PERSON>t loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1"}, "STDEVP": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina stand<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> visu populāciju kā argumentus (ignorē loģiskās vērtības un tekstu)"}, "STDEVPA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Aprēķina <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vērā visu populāciju, arī loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1"}, "STEYX": {"a": "(zināmie_y; zināmie_x)", "d": "Atgriež regresijas katra zināmā x prognozētās y vērtības standarta kļūdu"}, "TDIST": {"a": "(x; brīv<PERSON><PERSON>_pak; zari)", "d": "Atgriež Stjūdenta t sadalījumu"}, "TINV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež divu zaru apgriezto Stjūdenta t sadalījumu"}, "T.DIST": {"a": "(x; brīvī<PERSON>_pakāpe; kumulatīvā)", "d": "Atgriež kreisā zara Stjūdenta t sadalījumu"}, "T.DIST.2T": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež divu zaru Stjūdenta t sadalījumu"}, "T.DIST.RT": {"a": "(x; brīv<PERSON><PERSON>_pakāpe)", "d": "Atgriež labā zara Stjūdenta t sadalījumu"}, "T.INV": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež kreisā zara apgriezto Stjūdenta t sadalījumu"}, "T.INV.2T": {"a": "(varb<PERSON><PERSON><PERSON><PERSON>; brīvības_pakāpe)", "d": "Atgriež divu zaru apgriezto Stjūdenta t sadalījumu"}, "T.TEST": {"a": "(masīvs1; masīvs2; zari; tips)", "d": "Atgriež Stjūdenta t testam atbilstošu varbūtību"}, "TREND": {"a": "(zināmie_y; [zināmie_x]; [jaunie_x]; [konst])", "d": "<PERSON><PERSON><PERSON>jot ma<PERSON>, at<PERSON><PERSON><PERSON> skaitļ<PERSON> l<PERSON>, kas atbilst zināmiem datu punktiem"}, "TRIMMEAN": {"a": "(masīvs; procents)", "d": "Atgriež datu vērtības kopas iekšēj<PERSON>s da<PERSON> vidējo"}, "TTEST": {"a": "(masīvs1; masīvs2; zari; tips)", "d": "Atgriež Stjūdenta t testam atbilstošu varbūtību"}, "VAR": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> i<PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)"}, "VAR.P": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> visu populāciju (ignorē loģiskās vērtības un tekstu populācijā)"}, "VAR.S": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> i<PERSON> (izlas<PERSON> ignorē loģiskās vērtības un tekstu)"}, "VARA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, i<PERSON><PERSON><PERSON>t loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1"}, "VARP": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Aprēķina dispersiju, i<PERSON><PERSON><PERSON><PERSON> visu populāciju (ignorē loģiskās vērtības un tekstu populācijā)"}, "VARPA": {"a": "(vērtība1; [vērtība2]; ...)", "d": "Aprēķina dispersiju, <PERSON><PERSON>ot vērā visu populāciju, arī loģiskās vērtības un tekstu. Tekstam un loģiskajai vērtībai FALSE ir vērtība 0; loģiskajai vērtībai TRUE ir vērtība 1"}, "WEIBULL": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež Veibula sadalījumu"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulatīvā)", "d": "Atgriež Veibula sadalījumu"}, "Z.TEST": {"a": "(masīvs; x; [sigma])", "d": "Atgriež viena zara z testa P vērtību"}, "ZTEST": {"a": "(masīvs; x; [sigma])", "d": "Atgriež viena zara z testa P vērtību"}, "ACCRINT": {"a": "(emisi<PERSON>; pirmie_procenti; apmaksa; likme; nom; biež<PERSON>; [bāze]; [apr_metode])", "d": "<PERSON><PERSON><PERSON><PERSON> tāda vērtspapīra uzkr<PERSON> procentus, par kuru tiek periodiski maksāti procenti."}, "ACCRINTM": {"a": "(emisija; apm<PERSON>a; likme; nom; [pamats])", "d": "<PERSON>g<PERSON><PERSON> tāda vērtspapīra uz<PERSON>r<PERSON><PERSON> procentus, kuru <PERSON>, tiek maksāti procenti"}, "AMORDEGRC": {"a": "(cena; datums_iegādāts; pirmais_periods; likvidācija; periods; likme; [pamats])", "d": "Atgriež katra grāmatošanas perioda proporcionāli sadalītu lineāru aktīvu amortizāciju."}, "AMORLINC": {"a": "(cena; datums_iegādāts; pirmais_periods; likvidācija; periods; likme; [pamats])", "d": "Atgriež katra grāmatošanas perioda proporcionāli sadalītu lineāru aktīvu amortizāciju."}, "COUPDAYBS": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu no kupona perioda sākuma līdz apmaksas datumam"}, "COUPDAYS": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu k<PERSON>a period<PERSON>, ietverot apm<PERSON>as datumu"}, "COUPDAYSNC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON> dienu skaitu no apmaksas datuma līdz nākamajam kupona datumam"}, "COUPNCD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "Atgriež nākamo kupona datumu pēc apm<PERSON>as dienas"}, "COUPNUM": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas maksāja<PERSON> no apmaksas datuma līdz d<PERSON><PERSON><PERSON> datumam"}, "COUPPCD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; biežums; [bāze])", "d": "Atgriež iepriekšējo kupona datumu pirms apmaksas datuma"}, "CUMIPMT": {"a": "(likme; skg; pv; sākuma_periods; beigu_periods; tips)", "d": "<PERSON>griež kum<PERSON> procentus, kas maksāti par aizdevumu starp diviem periodiem"}, "CUMPRINC": {"a": "(likme; skg; pv; sākuma_periods; beigu_periods; tips)", "d": "<PERSON>g<PERSON>ž kumulat<PERSON><PERSON>, kas maksāta par aizdevumu starp diviem periodiem"}, "DB": {"a": "(vērt<PERSON>ba; likvid<PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; periods; [mēnesis])", "d": "Atgriež aktīvu amortizāciju noteiktā periodā, izman<PERSON>jot aritmētiski degresīvo nolietojuma aprēķināšanas metodi"}, "DDB": {"a": "(vērt<PERSON><PERSON>; likvid<PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; periods; [koeficients])", "d": "Atgriež aktīvu amortizāciju norādītā periodā, izmantojot ģeometriski degresīvo nolietojuma aprēķināšanas metodi vai kādu citu norādīto metodi"}, "DISC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; c; iz<PERSON><PERSON><PERSON><PERSON>; [bāze])", "d": "Atgriež vērtspapīra diskonta likmi"}, "DOLLARDE": {"a": "(daļa_do<PERSON><PERSON><PERSON>; daļa)", "d": "<PERSON><PERSON><PERSON><PERSON> cenu <PERSON>, kas izte<PERSON>ta kā da<PERSON>a, do<PERSON><PERSON><PERSON> cen<PERSON>, kas izteikta kā deci<PERSON>"}, "DOLLARFR": {"a": "(decim<PERSON><PERSON><PERSON><PERSON><PERSON>_dolārs; daļa)", "d": "<PERSON><PERSON><PERSON><PERSON> cenu <PERSON>, kas izte<PERSON>ta kā de<PERSON>, do<PERSON><PERSON><PERSON> cen<PERSON>, kas izteikta kā <PERSON>"}, "DURATION": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; kupons; peļņa; biež<PERSON>; [bāze])", "d": "Atgriež vērtspapīra ikgadējo ilgumu ar periodiskiem procentu maksājumiem"}, "EFFECT": {"a": "(nomināls_likme; skg)", "d": "Atgriež faktisko ikgadējo procentu likmi"}, "FV": {"a": "(likme; per_sk; maks; [nv]; [tips])", "d": "<PERSON><PERSON><PERSON><PERSON> tādas investīcijas nākotnes v<PERSON>rt<PERSON>, k<PERSON><PERSON> pama<PERSON> ir periodiski, konstanti maksājumi un konstanta procentu likme"}, "FVSCHEDULE": {"a": "(pamatsumma; grafiks)", "d": "Atgriež sākotnēj<PERSON>s pamatsummas nākotnes vērtību pēc salikto procentu likmju sērijas lie<PERSON>"}, "INTRATE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; invest<PERSON><PERSON><PERSON>; iz<PERSON><PERSON>; [bāze])", "d": "Atgriež pilnībā investēta vērtspapīra procentu likmi"}, "IPMT": {"a": "(likme; per; per_sk; pv; [nv]; [tips])", "d": "Atgriež noteikta perioda procentu maksāju<PERSON> par invest<PERSON>, ja tiek veikti periodiski, konstanti maksājumi un ir konstanta procentu likme"}, "IRR": {"a": "(vē<PERSON><PERSON><PERSON>; [minējums])", "d": "Atgriež iekšējo ienākumu normu (internal rate of return, IRR) sērijai naudas plūsmas"}, "ISPMT": {"a": "(likme; per; persk; pv)", "d": "<PERSON><PERSON><PERSON><PERSON> procentus, kas jā<PERSON><PERSON>ā noteiktā investī<PERSON> periodā"}, "MDURATION": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; kupons; peļņa; biež<PERSON>; [pamats])", "d": "Atgriež Makaolija modificēto vērtspapīra ilgumu ar 100 EUR pieņemtu nominālvērtību"}, "MIRR": {"a": "(v<PERSON><PERSON><PERSON><PERSON>; finansiālā_likme; p<PERSON><PERSON><PERSON>t_likme)", "d": "Atgriež iekšējo ienākumu normu (internal rate of return) sērijai periodisku naudas plūsm<PERSON>, <PERSON><PERSON>ot vērā gan investīcijas i<PERSON>, gan procentus par naudas pārinvestē<PERSON>"}, "NOMINAL": {"a": "(faktiskā_likme; skg)", "d": "Atgriež ikgadējo nominālo procentu likmi"}, "NPER": {"a": "(likme; maks; pv; [nv]; [tips])", "d": "Atgriež investīci<PERSON> periodu skaitu, ja tiek veikti periodiski, konstanti maksājumi un ir konstanta procentu likme"}, "NPV": {"a": "(likme; vērtība1; [vērtība2]; ...)", "d": "Atgriež investīcijas pašreizējo neto vērtību, i<PERSON><PERSON><PERSON><PERSON> diskonta likmi un turpmāku maksājumu virkni (negatīvas vērtības) un ienākumus (pozitīvas vērtības)"}, "ODDFPRICE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; pir<PERSON><PERSON>_kupons; likme; peļ<PERSON>a; iz<PERSON><PERSON>ša<PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapīra cenu par 100 EUR nominālvērt<PERSON>bu, kura pirmais periods ir nepāra"}, "ODDFYIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; pir<PERSON><PERSON>_kupons; likme; pr; izpi<PERSON>ša<PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapī<PERSON> p<PERSON>, kura pirmais periods ir nepāra"}, "ODDLPRICE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; pēd<PERSON><PERSON>e_procenti; likme; pe<PERSON><PERSON>a; iz<PERSON><PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapīra cenu uz 100 EUR nominālvērtību, kura pēdē<PERSON>is periods ir nepāra"}, "ODDLYIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; pēd<PERSON><PERSON>e_procenti; likme; pr; iz<PERSON><PERSON><PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atgriež tāda vērtspapīra cenu, kura pēd<PERSON><PERSON> periods ir nepāra"}, "PDURATION": {"a": "(likme; pv; nv)", "d": "<PERSON>g<PERSON><PERSON>u s<PERSON>tu, kuru laikā ieguldījums sasniedz noteiktu vērtību"}, "PMT": {"a": "(likme; persk; pv; [nv]; [tips])", "d": "Aprēķina a<PERSON><PERSON><PERSON><PERSON>, ja tiek veikti konstanti maksājumi ar konstantu procentu likmi"}, "PPMT": {"a": "(likme; per; per_sk; pv; [nv]; [tips])", "d": "Atgriež noteiktas investīcijas pamatsummas ma<PERSON><PERSON><PERSON>, ja tiek veikti periodisk<PERSON>, konstanti maksā<PERSON><PERSON> un ir konstanta procentu likme"}, "PRICE": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; likme; peļ<PERSON>a; iz<PERSON><PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "Atg<PERSON>ž vērtspapīra cenu par 100 EUR nominālvē<PERSON><PERSON><PERSON>, ma<PERSON><PERSON><PERSON><PERSON> periodiskus procentus"}, "PRICEDISC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; diskonts; izpi<PERSON><PERSON>na; [bāze])", "d": "Atgriež diskontēta vērtspapīra cenu par 100 EUR nominālvērtību"}, "PRICEMAT": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; likme; peļņa; [bāze])", "d": "Atgriež cenu vērtspapīram par 100 EUR nominālvērtību, par kuru procentus maksā d<PERSON>ot"}, "PV": {"a": "(likme; per_sk; maks; [nv]; [tips])", "d": "Atgriež investīcijas pašreizējo vērtību: turpmāk veicamu maksājumu virknes vērtības kopsumma šobrīd"}, "RATE": {"a": "(per_sk; maks; pv; [nv]; [tips]; [minējums])", "d": "Atgriež viena perioda procentu likmi aizdevuma vai investīcijas perioda laikā. <PERSON><PERSON><PERSON><PERSON>, <PERSON>zman<PERSON>jiet 6%/4, ja tiek veikti ikceturkšņa maksājumi ar ikgadējo procentu likmi (APR) 6%"}, "RECEIVED": {"a": "(a<PERSON><PERSON>a; dz<PERSON><PERSON><PERSON>_datums; investīcijas; diskonts; [bāze])", "d": "Atgriež pilnībā investēta vērtspapīra dzēša<PERSON> datumā saņemamo summu"}, "RRI": {"a": "(per_sk; pv; nv)", "d": "Atgriež ieguldījuma pieauguma procentu likmes ekvivalentu"}, "SLN": {"a": "(vērt<PERSON><PERSON>; lik<PERSON><PERSON><PERSON><PERSON>_vērt; ka<PERSON><PERSON><PERSON><PERSON>_laiks)", "d": "Atgriež aktīvu lineāro amortizāciju vienā periodā"}, "SYD": {"a": "(vērt<PERSON><PERSON>; lik<PERSON><PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; per)", "d": "Atgriež aktīvu amortizāciju noteiktā periodā, aprēķinot ar gada ciparu summas metodi"}, "TBILLEQ": {"a": "(a<PERSON><PERSON>a; dz<PERSON><PERSON><PERSON>_datums; diskonts)", "d": "Atgriež valsts kases vekseļ<PERSON> p<PERSON>, kas ekvi<PERSON>a obligācijām"}, "TBILLPRICE": {"a": "(a<PERSON><PERSON>a; dz<PERSON><PERSON><PERSON>_datums; diskonts)", "d": "Atgriež valsts kases vekseļa cenu par 100 ASV dolāru nominālvērtību"}, "TBILLYIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; pr)", "d": "Atgriež valsts kases vekseļa peļņu"}, "VDB": {"a": "(vērt<PERSON>ba; likvid<PERSON><PERSON><PERSON>_vērt; kal<PERSON><PERSON><PERSON>_laiks; sāk_periods; beigu_periods; [koeficients]; [nav_pārslēgš])", "d": "Atgriež aktīvu amortizāciju jebkurā norādītā periodā, ar<PERSON> periodos, izmantojot ģeometriski degresīvo nolietojuma aprēķināšanas metodi vai kādu citu norādītu metodi"}, "XIRR": {"a": "(vē<PERSON><PERSON><PERSON>; datumi; [minējums])", "d": "Atgrie<PERSON> nauda<PERSON> plū<PERSON> grafika iekšējo i<PERSON>sīguma normu"}, "XNPV": {"a": "(likme; vērt<PERSON>ba; datumi)", "d": "Atgriež naudas plūsmas grafika pašreizējo neto vērtību"}, "YIELD": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; likme; c; iz<PERSON><PERSON>; bie<PERSON><PERSON>; [bāze])", "d": "<PERSON><PERSON><PERSON><PERSON>, ko devis vērtspapīrs ar periodisku procentu izmaksu"}, "YIELDDISC": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; c; iz<PERSON><PERSON><PERSON><PERSON>; [bāze])", "d": "Atgriež diskontēta vērtspapīra ikgadējo peļņu. <PERSON><PERSON><PERSON><PERSON>, valsts kases vekselis"}, "YIELDMAT": {"a": "(a<PERSON><PERSON><PERSON>; dz<PERSON><PERSON><PERSON>_datums; emisija; likme; c; [bāze])", "d": "<PERSON>g<PERSON>ž ikgad<PERSON><PERSON> vē<PERSON>pap<PERSON> p<PERSON>, par kuru procentus maksā <PERSON>"}, "ABS": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa absolūto vērtību - skaitli bez zīmes"}, "ACOS": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa arkkosinusu radiānos diapazonā no 0 līdz Pi. Arkkosinuss ir leņķis, kura kosinus<PERSON> ir S<PERSON>tl<PERSON>"}, "ACOSH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa apgriezto hiperbolisko kosinusu"}, "ACOT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa arkkotangensu radiānos diapazonā no 0 līdz Pi."}, "ACOTH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa inverso hiperbolisko kotangensu"}, "AGGREGATE": {"a": "(funkcijas_num; opcijas; ats1; ...)", "d": "Atgriež apkopojumu kā sarakstu vai datu bāzi"}, "ARABIC": {"a": "(teksts)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> romi<PERSON>u ciparu par arābu ciparu"}, "ASC": {"a": "(teksts)", "d": "Dubultbaitu r<PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) valodām funkcija nomaina pilna platuma (dubultbaitu) rakstzī<PERSON> ar <PERSON> (vienbaita) rakstzīmēm"}, "ASIN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa arksīnusu radiānos diapazonā no -Pi/2 līdz Pi/2"}, "ASINH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa apgriezto hiperbolisko sinusu"}, "ATAN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> skai<PERSON><PERSON><PERSON> r<PERSON>, diapazonā no -Pi/2 līdz Pi/2"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Atgriež <PERSON>d<PERSON> x un y koordinātu arktangensu radiānos no -Pi un Pi, izslēdzot -Pi"}, "ATANH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa apgriezto hiperbolisko tangensu"}, "BASE": {"a": "(skaitl<PERSON>; bāze; [min_garums])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitli par teksta atveidojumu ar doto bāzi"}, "CEILING": {"a": "(skaitl<PERSON>; būtisku<PERSON>)", "d": "Noapaļo skaitli uz augšu līdz tuvāka<PERSON>m bū<PERSON><PERSON><PERSON><PERSON> skai<PERSON>, kas dal<PERSON><PERSON> bez at<PERSON>"}, "CEILING.MATH": {"a": "(skaitlis; [būtisku<PERSON>]; [rež<PERSON><PERSON>])", "d": "Noapaļo skaitli uz augšu līdz tuvākajam veselajam skaitlim vai līdz tuvākajam nozīmīgajam skaitlim, kas dal<PERSON><PERSON> bez at<PERSON>uma"}, "CEILING.PRECISE": {"a": "(skaitl<PERSON>; [būtisku<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapa<PERSON>ots līdz tuvākajam veselajam skaitlim vai tuvākajam būtis<PERSON><PERSON><PERSON> skaitlim, kas da<PERSON><PERSON><PERSON> bez at<PERSON>"}, "COMBIN": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vienumu skaita kombin<PERSON><PERSON><PERSON> skaitu"}, "COMBINA": {"a": "(skaitlis; izvēlētais_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vienumu skaita kombin<PERSON><PERSON><PERSON> skaitu"}, "COS": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež leņķa kosinusu"}, "COSH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa hiperbolisko kosinusu"}, "COT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa kotangensu"}, "COTH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež skaitļa hiperbolisko kotangensu"}, "CSC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa kosekansu"}, "CSCH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa hiperbolisko kosekansu"}, "DECIMAL": {"a": "(skai<PERSON><PERSON>; bāze)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tļa teksta atveidojumu dotā bāzē par decimālskaitli"}, "DEGREES": {"a": "(leņķis)", "d": "<PERSON><PERSON><PERSON><PERSON> radiānus par grādiem"}, "ECMA.CEILING": {"a": "(skaitl<PERSON>; būtisku<PERSON>)", "d": "Noapaļo skaitli uz augšu līdz tuvāka<PERSON>m bū<PERSON><PERSON><PERSON><PERSON> skai<PERSON>, kas dal<PERSON><PERSON> bez at<PERSON>"}, "EVEN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Noapaļo pozitīvu skaitli uz augšu, bet negatīvu - uz leju līdz tuvākajam veselajam pārskaitlim"}, "EXP": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> e, kas kāpin<PERSON>ts norādītajā pakāpē"}, "FACT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON>, kas vien<PERSON> ar 1*2*3*...* <PERSON><PERSON><PERSON><PERSON>"}, "FACTDOUBLE": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa dubulto faktori<PERSON>"}, "FLOOR": {"a": "(skaitl<PERSON>; būtisku<PERSON>)", "d": "Noapaļo skaitli uz leju līdz tuvākajam būtis<PERSON><PERSON><PERSON> skaitlim, kas dal<PERSON><PERSON> bez at<PERSON>"}, "FLOOR.PRECISE": {"a": "(skaitl<PERSON>; [būtisku<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapa<PERSON> uz leju līdz tuvākajam veselajam skaitlim vai tuvākajam būtiska<PERSON>m skaitlim, kas da<PERSON><PERSON><PERSON> bez at<PERSON>"}, "FLOOR.MATH": {"a": "(skaitlis; [būtisku<PERSON>]; [rež<PERSON><PERSON>])", "d": "Noapaļo skaitli uz leju līdz tuvākajam veselajam skaitlim vai līdz tuvākajam nozīmīgajam skaitlim, kas dal<PERSON><PERSON> bez at<PERSON>uma"}, "GCD": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "<PERSON>g<PERSON><PERSON>l<PERSON><PERSON> k<PERSON>"}, "INT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Noapaļo skaitli līdz tuvākajam veselajam skaitlim"}, "ISO.CEILING": {"a": "(skaitl<PERSON>; [būtisku<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapaļots līdz tuvākajam veselajam skaitlim vai tuvākajam būtiska<PERSON>m skaitlim, kas dal<PERSON><PERSON> bez atlikuma. Neatkarīgi no skaitļa <PERSON>, tas tiek noapaļots uz augšu. Ta<PERSON><PERSON> ja skaitlis vai būtiskais skaitlis ir nulle, tiek atgriezta nulle."}, "LCM": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atg<PERSON>ž ma<PERSON><PERSON><PERSON>, ar kuru dal<PERSON><PERSON> bez at<PERSON>uma"}, "LN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa naturālo logaritmu"}, "LOG": {"a": "(skaitlis; [bāze])", "d": "Atgriež skaitļa logaritmu norādītā<PERSON> bāzei"}, "LOG10": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa bāzes 10 logaritmu"}, "MDETERM": {"a": "(masīvs)", "d": "Atgriež masīva matricas determinantu"}, "MINVERSE": {"a": "(masīvs)", "d": "Atgriež masīvā glabātas matricas apgriezto matricu"}, "MMULT": {"a": "(masīvs1; masīvs2)", "d": "Atgriež divu masīvu reizinājumu - masīvu ar to pašu rindu skaitu kā masīvs1 un ar to pašu kolonnu skaitu kā masīvs2"}, "MOD": {"a": "(skai<PERSON><PERSON>; dal<PERSON>t<PERSON>js)", "d": "Atgriež atlikumu pēc skaitļa dalī<PERSON> ar dal<PERSON>"}, "MROUND": {"a": "(skaitlis; dalāmais_skaitlis)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas noapaļ<PERSON> līdz vēlamajam dalāmajam skaitlim"}, "MULTINOMIAL": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež datu kopas multinomiālu"}, "MUNIT": {"a": "(dimensija)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dimensijas vien<PERSON> matricu"}, "ODD": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Noapaļo p<PERSON>itīvu skaitli uz augšu, bet negatīvu - uz leju līdz tuvākajam veselajam nepāra skaitlim"}, "PI": {"a": "()", "d": "Atgriež vērtību Pi - 3,14159265358979 ar precizitāti līdz 15 cipariem"}, "POWER": {"a": "(skai<PERSON><PERSON>; pak<PERSON>pe)", "d": "Atgriež skaitļa k<PERSON>āšanas rezultātu"}, "PRODUCT": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "<PERSON><PERSON><PERSON> visus s<PERSON>, kas sniegti k<PERSON>i"}, "QUOTIENT": {"a": "(skait<PERSON>t<PERSON>js; saucējs)", "d": "<PERSON>g<PERSON>ž da<PERSON><PERSON><PERSON><PERSON> veselo da<PERSON>"}, "RADIANS": {"a": "(leņķis)", "d": "Konvertē grādus par radiāniem"}, "RAND": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON>, kas liel<PERSON>ks vai vienāds ar nulli un mazāks par 1 un ir vienm<PERSON>r<PERSON><PERSON> sadal<PERSON> (veicot pārrēķinu, mainās)"}, "RANDARRAY": {"a": "([rindas]; [ailes]; [min]; [max]; [vesels_skaitlis])", "d": "Atgriež gadījumskaitļu ma<PERSON>īvu"}, "RANDBETWEEN": {"a": "(mazākais; lielākais)", "d": "Atgriež nejauši izvēlētu skaitli starp norādītajiem skaitļiem"}, "ROMAN": {"a": "(skaitlis; [forma])", "d": "Konvertē arābu skaitļus uz romiešu skaitļiem kā tekstu"}, "ROUND": {"a": "(skaitlis; ciparu_skaits)", "d": "Noapaļo skaitli līdz norādītajam ciparu skaitam"}, "ROUNDDOWN": {"a": "(skaitlis; ciparu_skaits)", "d": "Noapaļo skaitli uz leju virzienā uz nulli"}, "ROUNDUP": {"a": "(skaitlis; ciparu_skaits)", "d": "Noapaļo skaitli uz augšu virzienā no nulles"}, "SEC": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa sekanti"}, "SECH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "atgriež leņķa hiper<PERSON>isko se<PERSON>ti"}, "SERIESSUM": {"a": "(x; n; m; koe<PERSON>i)", "d": "Atgriež pakāpju sērijas summu, kas balst<PERSON>ta formulā"}, "SIGN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "<PERSON>griež skait<PERSON>a <PERSON>: 1, ja skaitlis ir pozitīv<PERSON>, nulli, ja skaitlis ir nulle, vai -1, ja skaitlis ir negatīvs"}, "SIN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež leņķa sinusu"}, "SINH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa hiperbolisko sinusu"}, "SQRT": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa kvadrātsakni"}, "SQRTPI": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež k<PERSON> (skaitlis * Pi)"}, "SUBTOTAL": {"a": "(funkcijas_num; ats1; ...)", "d": "Atgriež starpsummu sarakstā vai datu bāzē"}, "SUM": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Saskaita visus šūnu di<PERSON>zon<PERSON> esošos skait<PERSON>us"}, "SUMIF": {"a": "(diapazons; kritēriji; [summas_diapazons])", "d": "Saskaita noteikta nosacījuma vai kritēriju nor<PERSON><PERSON><PERSON><PERSON>"}, "SUMIFS": {"a": "(summas_diapazons; kritēriju_diapazons; kritēriji; ...)", "d": "<PERSON><PERSON><PERSON>, ko norāda dotā nosacījumu kopa vai kritēriji"}, "SUMPRODUCT": {"a": "(masīvs1; [masīvs2]; [masīvs3]; ...)", "d": "Atgriež atbilstošo diapazonu vai masīvu reizinājumu summu"}, "SUMSQ": {"a": "(skaitlis1; [skaitlis2]; ...)", "d": "Atgriež argumentu kvadrātu summu. Argumenti var būt skait<PERSON>, <PERSON><PERSON><PERSON><PERSON>, no<PERSON><PERSON><PERSON> vai atsauces uz <PERSON>, k<PERSON><PERSON><PERSON> ir skait<PERSON>i"}, "SUMX2MY2": {"a": "(masīvs_x; masīvs_y)", "d": "Sasummē divu atbilstošu diapazonu vai masīvu kvadrātu atšķirības"}, "SUMX2PY2": {"a": "(masīvs_x; masīvs_y)", "d": "Atgriež divu atbilstošu diapazonu vai masīvu skaitļu kvadrātu summu kopsummu"}, "SUMXMY2": {"a": "(masīvs_x; masīvs_y)", "d": "Sasummē divu atbilstošu diapazonu vai masīvu atšķirību kvadrātus"}, "TAN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež leņķa tangensu"}, "TANH": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež skaitļa hiperbolisko tangensu"}, "TRUNC": {"a": "(skaitlis; [ciparu_skaits])", "d": "<PERSON><PERSON><PERSON> skaitli lī<PERSON>z ve<PERSON> s<PERSON>, <PERSON><PERSON><PERSON><PERSON> skait<PERSON>a de<PERSON>"}, "ADDRESS": {"a": "(rindas_num; kolonnas_num; [abs_num]; [a1]; [lapas])", "d": "Izve<PERSON> at<PERSON> kā te<PERSON>tu, ja nor<PERSON><PERSON><PERSON><PERSON> rindas un kolonnas numuri"}, "CHOOSE": {"a": "(indeksa_num; vērtība1; [vērtība2]; ...)", "d": "<PERSON>zv<PERSON><PERSON> vērtību vai veicamo darbību no vērtību saraksta atkarībā no indeksa skaitļa"}, "COLUMN": {"a": "([atsauce])", "d": "Atgriež atsauces kolonnas numuru"}, "COLUMNS": {"a": "(masīvs)", "d": "Atgriež kolonnu skaitu atsaucē vai masīvā"}, "FORMULATEXT": {"a": "(atsauce)", "d": "Atgriež formulu kā virkni"}, "HLOOKUP": {"a": "(uzmeklējamā_vērtība; tabulas_masīvs; rinda_indeksa_num; [diapazona_uzmeklēšana])", "d": "Meklē vērtību tabulas vai vērtību masīva augšējā rindā un atgriež to tajā pašā rindas kolonnā, kuru norāda"}, "HYPERLINK": {"a": "(saite_uz_atraš_vietu; [p<PERSON><PERSON><PERSON><PERSON>_vārds])", "d": "Izveido saīsni vai <PERSON>, kas atver datora cietajā diskā, tīkla serverī vai internetā glabātu dokumentu"}, "INDEX": {"a": "(masīvs; rindas_numurs; [kolonnas_numurs]!atsauce; rindas_numurs; [kolonnas_numurs]; [apga<PERSON>a_numurs])", "d": "Atgriež vērt<PERSON><PERSON> vai <PERSON>, kas atrodas noteiktas rindas un kolonnas krustpunktā norādītajā diapazonā"}, "INDIRECT": {"a": "(ats_teksts; [a1])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas <PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>ta v<PERSON>"}, "LOOKUP": {"a": "(uzmeklējamā_vērtība; uzmekl<PERSON><PERSON><PERSON>_vektors; [rezultāta_vektors]!uzmeklējamā_vērtība; masīvs)", "d": "Uzmeklē vērtību vienas rindas vai vienas kolonnas diapazonā vai masīvā. Paredzēts atpakaļsaderībai"}, "MATCH": {"a": "(uzmeklējamā_vērtība; uzmeklē<PERSON><PERSON>_masīvs; [atbilstības_tips])", "d": "Atg<PERSON>ž tāda masīva vienuma relatīvo pozīciju, kur<PERSON> atbilst norādītai vērtībai norādītā kārtībā"}, "OFFSET": {"a": "(atsauce; rindas; kolonnas; [augstums]; [platums])", "d": "Atgriež atsauci uz diapazonu, kas ir noteikts rindu un kolonnu skaits no noteiktas atsauces"}, "ROW": {"a": "([atsauce])", "d": "<PERSON>g<PERSON><PERSON> atsauces rindas numuru"}, "ROWS": {"a": "(masīvs)", "d": "Atgriež rindu skaitu atsaucē vai masīvā"}, "TRANSPOSE": {"a": "(masīvs)", "d": "Konvertē vertikālu šūnu diapazonu par horizontālu un pretēji"}, "UNIQUE": {"a": "(masīvs; [pēc_kolonnas]; [tieši_vienre<PERSON>])", "d": "Atgriež unikālās vērtības no diapazona vai masīva."}, "VLOOKUP": {"a": "(uzmeklējamā_vērtība; tabulas_masīvs; kolonna_indeksa_num; [diapazona_uzmeklēšana])", "d": "Meklē vērtību tabulas pēdējā kolonnā pa kreisi un pēc tam atgriež vērtību tajā pašā kolonnas rindā, kuru norāda. Pēc noklusējuma tabulai jābūt sakārtotai augošā secībā"}, "XLOOKUP": {"a": "(uzmeklējamā_vērtība; uzmekl<PERSON><PERSON><PERSON>_masīvs; atgrie<PERSON><PERSON>_masīvs; [ja_nav_atrasts]; [atbilst<PERSON><PERSON>_režīms]; [mekl<PERSON><PERSON><PERSON>_režīms])", "d": "Meklē diapazonā vai masīvā atbilstību un atgriež atbilstošo vienumu no otra diapazona vai masīva. Pēc noklusējuma tiek izmantota precīza atbilstība"}, "CELL": {"a": "(info_tips; [atsauce])", "d": "Atgriež informāciju par šū<PERSON>, atrašanās vietu vai saturu"}, "ERROR.TYPE": {"a": "(k<PERSON><PERSON><PERSON>_vērt)", "d": "<PERSON><PERSON><PERSON><PERSON>, kas atbilst kļūdas vērtībai."}, "ISBLANK": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai atsauce nav uz tukšu šūnu un atgriež TRUE vai FALSE"}, "ISERR": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir <PERSON>, kas nav #N/A, un atgriež TRUE vai FALSE"}, "ISERROR": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir k<PERSON>, un atgriež TRUE vai FALSE"}, "ISEVEN": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež TRUE, ja ir <PERSON><PERSON><PERSON>"}, "ISFORMULA": {"a": "(atsauce)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai atsauce ir uz <PERSON>, kur<PERSON> ir formula, un atgriež TRUE vai FALSE"}, "ISLOGICAL": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir loģiskā vērtība (TRUE vai FALSE) un atgriež TRUE vai FALSE"}, "ISNA": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir #N/A un atgriež TRUE vai FALSE"}, "ISNONTEXT": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība nav teksts (tuk<PERSON><PERSON> nav teksts) un atgriež TRUE vai FALSE"}, "ISNUMBER": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir skaitlis un atgriež TRUE vai FALSE"}, "ISODD": {"a": "(s<PERSON><PERSON><PERSON>)", "d": "Atgriež TRUE, ja ir <PERSON><PERSON><PERSON>"}, "ISREF": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir atsauce un atgriež TRUE vai FALSE"}, "ISTEXT": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai vērtība ir teksts un atgriež TRUE vai FALSE"}, "N": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>, kas nav skai<PERSON>, par skaitli, datumus - par seriā<PERSON><PERSON> skaitļiem, TRUE uz 1, visu citu - uz 0 (nulli)"}, "NA": {"a": "()", "d": "Atgriež kļūdas vērtību #N/A (vērtība nav pieejama)"}, "SHEET": {"a": "([vērtība])", "d": "Atgriež atsauces lapas numuru"}, "SHEETS": {"a": "([atsauce])", "d": "<PERSON><PERSON><PERSON><PERSON>u skaitu at<PERSON>"}, "TYPE": {"a": "(v<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> veselu skaitli, kas ap<PERSON><PERSON><PERSON><PERSON> vērtības datu tipu: skaitlis = 1; teksts = 2; loģiskā vērtība = 4; k<PERSON><PERSON><PERSON> vērtība = 16; masīvs = 64; salik<PERSON> dati = 128"}, "AND": {"a": "(loģiskā1; [loģiskā2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai visi argumenti ir TRUE, un atgriež TRUE, ja visi argumenti ir TRUE"}, "FALSE": {"a": "()", "d": "Atgriež loģisko vērtību FALSE"}, "IF": {"a": "(loģiskais_tests; [vērtība_ja_true]; [vērtība_ja_false])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai ir ievē<PERSON>, un atgriež vienu vērt<PERSON>, ja TRUE, bet citu vērtību, ja - FALSE"}, "IFS": {"a": "(loģiskais_tests; vērtība_ja_patiess; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai ir izpild<PERSON>ts viens vai vairāki no<PERSON>, un atgriež vērt<PERSON>, kas atbilst pirmajam PATIESAJAM nosacījumam"}, "IFERROR": {"a": "(vērtība; vērtība_ja_kļūda)", "d": "<PERSON>griež vērtī<PERSON>_ja_k<PERSON><PERSON><PERSON>, ja izteik<PERSON>e ir k<PERSON>, bet pretējā gadīju<PERSON> — pašas izteiksmes vērtību"}, "IFNA": {"a": "(vērtība; vērtība_ja_nav_pieej)", "d": "Atg<PERSON><PERSON> nor<PERSON><PERSON><PERSON><PERSON> v<PERSON>, ja izteiksmes atrisinājums ir #N/A; citos gadījumos atgriež izteiksmes vērtību"}, "NOT": {"a": "(loģiskā)", "d": "Maina FALSE uz TRUE vai TRUE uz FALSE"}, "OR": {"a": "(loģiskā1; [loģiskā2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai kāds no argumentiem ir TRUE, un atgriež TRUE vai FALSE. Atgriež FALSE tikai tad, ja visi argumenti ir FALSE"}, "SWITCH": {"a": "(iz<PERSON><PERSON>sm<PERSON>; vērtība1; rezultāts1; [noklusēju<PERSON>_vai_vērtība2]; [rezultāts2]; ...)", "d": "Novērtē izteiksmi pret vērtību sarakstu un atgriež rezultātu, kas atbilst pirmajai atbilstošajai vērtībai. Ja atbilstības nav, tiek atgriezta neobligāta noklusējuma vērtība"}, "TRUE": {"a": "()", "d": "Atgriež loģisko vērtību TRUE"}, "XOR": {"a": "(loģiskā_vērtība1; [loģiskā_vērtība2]; ...)", "d": "No visiem argumentiem atgriež loģisko vērtību \"Izņemot/Vai\""}, "TEXTBEFORE": {"a": "(teksts, norobežotājs, [instances_num], [atbilstības_režīms], [atbilstības_beigas], [ja_nav_atrasts])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas ir pirms norobežošanas rakstzīmēm."}, "TEXTAFTER": {"a": "(teksts, norobežotājs, [instances_num], [atbilstības_režīms], [atbilstības_beigas], [ja_nav_atrasts])", "d": "<PERSON><PERSON><PERSON><PERSON>, kas ir pēc norobež<PERSON> r<PERSON>tzī<PERSON>."}, "TEXTSPLIT": {"a": "(teksts, kolonnu_norobežotājs, [rindu_norobežotājs], [ignorēt_tukšu], [atbilst<PERSON><PERSON>_rež<PERSON><PERSON>], [pilda_ar])", "d": "Sad<PERSON> tekstu rind<PERSON> vai kolo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>."}, "WRAPROWS": {"a": "(vektors, wrap_count, [pad_with])", "d": "Aplauzt rindas vai kolonnas vektoru pēc norādītā vērtību skaita."}, "VSTACK": {"a": "(masīvs1, [masīvs2], ...)", "d": "Vert<PERSON><PERSON><PERSON> sagrupē masīvus vienā masīvā."}, "HSTACK": {"a": "(masīvs1, [masīvs2], ...)", "d": "Horizontāli sagrupē masīvus vienā masīvā."}, "CHOOSEROWS": {"a": "(masīvs, row_num1, [row_num2], ...)", "d": "Atgriež rindas no masīva vai atsauces."}, "CHOOSECOLS": {"a": "(masīvs, col_num1, [col_num2], ...)", "d": "Atgriež kolonnas no masīva vai atsauces."}, "TOCOL": {"a": "(masīvs, [ignorēt], [scan_by_column])", "d": "Atgriež masīvu kā vienu kolonnu."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Atgriež masīvu kā vienu rindu."}, "WRAPCOLS": {"a": "(vektors, wrap_count, [pad_with])", "d": " Aplauzt rindas vai kolonnas vektoru pēc norādītā vērtību skaita."}, "TAKE": {"a": "(masīvs, rindas, [kolonnas])", "d": "Atgriež rindas vai kolonnas no masīva sākuma vai beigām."}, "DROP": {"a": "(masīvs, rindas, [kolonnas])", "d": "Nomet rindas vai kolonnas no masīva sākuma vai beigām."}}