{"DATE": {"a": "(година; месец; ден)", "d": "Връща числото, което представлява датата в кода за дата и час"}, "DATEDIF": {"a": "(начална_дата; крайна_дата; единица)", "d": "Изчислява броя на дните, месеците или годините между две дати"}, "DATEVALUE": {"a": "(дата_текст)", "d": "Преобразува дата от текстов вид в число, което представя датата в кода за дата и час"}, "DAY": {"a": "(пореден_номер)", "d": "Връща деня от месеца – число от 1 до 31."}, "DAYS": {"a": "(крайна_дата; начална_дата)", "d": "Връща броя на дните между двете дати."}, "DAYS360": {"a": "(начална_дата; крайна_дата; [метод])", "d": "Връща броя на дните между две дати на базата на година от 360 дни (дванайсет месеца от 30 дни)"}, "EDATE": {"a": "(начална_дата; месеци)", "d": "Връща поредния номер на датата, която е определен брой месеци преди или след началната дата"}, "EOMONTH": {"a": "(начална_дата; месеци)", "d": "Връща поредния номер на последния ден на месеца, който е преди или след определен брой месеци"}, "HOUR": {"a": "(пореден_номер)", "d": "Връща часа като число от 0 (12:00 преди обяд) до 23 (11:00 следобед.)."}, "ISOWEEKNUM": {"a": "(дата)", "d": "Връща номера на седмицата (по ISO) на годината за дадена дата"}, "MINUTE": {"a": "(пореден_номер)", "d": "Връща минутата – число от 0 до 59."}, "MONTH": {"a": "(пореден_номер)", "d": "Връща месеца – число от 1 (януари) до 12 (декември)."}, "NETWORKDAYS": {"a": "(начална_дата; крайна_дата; [празници])", "d": "Връща броя на пълните работни дни между две дати"}, "NETWORKDAYS.INTL": {"a": "(начална_дата; крайна_дата; [уикенд]; [празници])", "d": "Връща броя на пълните работни дни между две дати със задавани по избор параметри за почивните дни"}, "NOW": {"a": "()", "d": "Връща текущите дата и час, форматирани като дата и час."}, "SECOND": {"a": "(пореден_номер)", "d": "Връща секундата – число от 0 до 59."}, "TIME": {"a": "(час; минута; секунда)", "d": "Преобразува часове, минути и секунди, зададени като числа, в пореден номер, форматиран с часови формат"}, "TIMEVALUE": {"a": "(време_текст)", "d": "Преобразува време от текстов вид в серийно число за време – число от 0 (12:00:00 AM) до 0,999988426 (11:59:59 PM). След въвеждането на формулата форматирайте числото с часови формати"}, "TODAY": {"a": "()", "d": "Връща текущата дата, форматирана като дата."}, "WEEKDAY": {"a": "(пореден_номер; [връщане_тип])", "d": "Връща число от 1 до 7, показващо деня от седмицата при зададена дата."}, "WEEKNUM": {"a": "(пореден_номер; [връщане_тип])", "d": "Връща номерата на седмиците в годината"}, "WORKDAY": {"a": "(начална_дата; дни; [празници])", "d": "Връща поредния номер на датата преди или след определен брой работни дни от началната дата"}, "WORKDAY.INTL": {"a": "(начална_дата; дни; [уикенд]; [празници])", "d": "Връща пореден номер на датата преди или след определен брой работни дни с параметри по избор за почивните дни"}, "YEAR": {"a": "(пореден_номер)", "d": "Връща годината от дата – цяло число в диапазона 1900 – 9999."}, "YEARFRAC": {"a": "(начална_дата; крайна_дата; [база])", "d": "Връща частта от годината, представляваща броя цели дни между началната и крайната дата"}, "BESSELI": {"a": "(x; n)", "d": "Връща модифицираната Беселова функция In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Връща Беселовата функция Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Връща модифицираната Беселова функция Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Връща Беселовата функция Yn(x)"}, "BIN2DEC": {"a": "(число)", "d": " Превръща едно двоично число в десетично"}, "BIN2HEX": {"a": "(число; [позиции])", "d": " Превръща едно двоично число в шестнайсетично"}, "BIN2OCT": {"a": "(число; [позиции])", "d": " Превръща едно двоично число в осмично"}, "BITAND": {"a": "(число1; число2)", "d": "Връща побитово \"И\" на две числа"}, "BITLSHIFT": {"a": "(число; размер_на_изместване)", "d": "Връща число, изместено наляво с размер_на_изместване бита"}, "BITOR": {"a": "(число1; число2)", "d": "Връща побитово \"Или\" на две числа"}, "BITRSHIFT": {"a": "(число; размер_на_изместване)", "d": "Връща число, изместено надясно с размер_на_изместване бита"}, "BITXOR": {"a": "(число1; число2)", "d": "Връща побитово \"Изключващо Или\" на две числа"}, "COMPLEX": {"a": "(реално_число; i_число; [суфикс])", "d": "Преобразува реалния и имагинерния коефициент в комплексно число"}, "CONVERT": {"a": "(число; от_единица; в_единица)", "d": "Преобразува числена стойност от една измервателна система в друга"}, "DEC2BIN": {"a": "(число; [позиции])", "d": "Превръща едно десетично число в двоично"}, "DEC2HEX": {"a": "(число; [позиции])", "d": " Превръща едно десетично число в шестнайсетично"}, "DEC2OCT": {"a": "(число; [позиции])", "d": " Превръща едно десетично число в осмично"}, "DELTA": {"a": "(число1; [число2])", "d": "Проверява дали две числа са равни"}, "ERF": {"a": "(долна_граница; [горна_граница])", "d": "Връща функцията на грешките"}, "ERF.PRECISE": {"a": "(X)", "d": "Връща функцията на грешки"}, "ERFC": {"a": "(x)", "d": "Връща функцията на допълнителните грешки"}, "ERFC.PRECISE": {"a": "(x)", "d": "Връща функцията на допълнителните грешки"}, "GESTEP": {"a": "(число; [стъпка])", "d": "Проверява дали едно число е по-голямо от една прагова стойност"}, "HEX2BIN": {"a": "(число; [позиции])", "d": "Преобразува едно шестнайсетично число в двоично"}, "HEX2DEC": {"a": "(число)", "d": " Преобразува едно шестнайсетично число в десетично"}, "HEX2OCT": {"a": "(число; [позиции])", "d": " Преобразува едно шестнайсетично число в осмично"}, "IMABS": {"a": "(iчисло)", "d": "Връща абсолютната стойност (модула) на едно комплексно число"}, "IMAGINARY": {"a": "(iчисло)", "d": "Връща имагинерния коефициент на едно комплексно число"}, "IMARGUMENT": {"a": "(iчисло)", "d": "Връща аргумента q, ъгъл изразен в радиани"}, "IMCONJUGATE": {"a": "(iчисло)", "d": "Връща комплексното спрегнато число на едно комплексно число"}, "IMCOS": {"a": "(iчисло)", "d": "Връща косинуса на едно комплексно число"}, "IMCOSH": {"a": "(комплексно_число)", "d": "Връща хиперболичния косинус на комплексно число"}, "IMCOT": {"a": "(число)", "d": "Връща котангенса от комплексно число"}, "IMCSC": {"a": "(число)", "d": "Връща косеканса от комплексно число"}, "IMCSCH": {"a": "(число)", "d": "Връща хиперболичния косеканс от комплексно число"}, "IMDIV": {"a": "(iчисло1; iчисло2)", "d": "Връща частното на две комплексни числа"}, "IMEXP": {"a": "(iчисло)", "d": "Връща експонентата на едно комплексно число"}, "IMLN": {"a": "(iчисло)", "d": "Връща натуралния логаритъм на едно комплексно число"}, "IMLOG10": {"a": "(iчисло)", "d": "Връща логаритъма с основа 10 на едно комплексно число"}, "IMLOG2": {"a": "(iчисло)", "d": "Връща логаритъма с основа 2 на едно комплексно число"}, "IMPOWER": {"a": "(iчисло; число)", "d": "Връща комплексно число, повдигнато на цяла степен"}, "IMPRODUCT": {"a": "(iчисло1; [iчисло2]; ...)", "d": "Връща произведението на 1 до 255 комплексни числа"}, "IMREAL": {"a": "(iчисло)", "d": "Връща реалния коефициент на едно комплексно число"}, "IMSEC": {"a": "(число)", "d": "Връща секанса от комплексно число"}, "IMSECH": {"a": "(число)", "d": "Връща хиперболичния секанс на комплексно число"}, "IMSIN": {"a": "(iчисло)", "d": "Връща синуса на едно комплексно число"}, "IMSINH": {"a": "(комплексно_число)", "d": "Връща хиперболичния синус на комплексно число"}, "IMSQRT": {"a": "(iчисло)", "d": "Връща квадратния корен на едно комплексно число"}, "IMSUB": {"a": "(iчисло1; iчисло2)", "d": "Връща разликата на две комплексни числа"}, "IMSUM": {"a": "(iчисло1; [iчисло2]; ...)", "d": "Връща сумата на комплексни числа"}, "IMTAN": {"a": "(число)", "d": "Връща тангенса от комплексно число"}, "OCT2BIN": {"a": "(число; [позиции])", "d": "Превръща едно осмично число в двоично"}, "OCT2DEC": {"a": "(число)", "d": " Превръща едно осмично число в десетично"}, "OCT2HEX": {"a": "(число; [позиции])", "d": "Превръща едно осмично число в шестнайсетично"}, "DAVERAGE": {"a": "(база_данни; поле; критерий)", "d": "Осреднява стойностите в колона от списък или база_данни, които отговарят на зададените от вас условия"}, "DCOUNT": {"a": "(база_данни; поле; критерий)", "d": "Преброява клетките, съдържащи числа в полето (колоната) от записите в базата данни, които отговарят на зададените от вас условия"}, "DCOUNTA": {"a": "(база_данни; поле; критерий)", "d": "Преброява непразните клетки в полето (колоната) на записите в база_данни, които отговарят на зададени от вас условия"}, "DGET": {"a": "(база_данни; поле; критерий)", "d": "Извлича от база данни единствен запис, който изпълнява зададени от вас условия"}, "DMAX": {"a": "(база_данни; поле; критерий)", "d": " Връща най-голямото число в полето (колоната) от записите в базата данни, които отговарят на зададените от вас условия"}, "DMIN": {"a": "(база_данни; поле; критерий)", "d": "Връща най-малкото число в полето (колоната) от записите в базата данни, които отговарят на зададените от вас условия"}, "DPRODUCT": {"a": "(база_данни; поле; критерий)", "d": "Умножава стойностите в полето (колоната) на записите в базата данни, които отговарят на зададени от вас условия"}, "DSTDEV": {"a": "(база_данни; поле; критерий)", "d": "Оценява стандартното отклонение, базирано на извадка от избрани елементи от база данни"}, "DSTDEVP": {"a": "(база_данни; поле; критерий)", "d": "Изчислява стандартното отклонение на базата на цялата генерална съвкупност от избрани елементи от база данни"}, "DSUM": {"a": "(база_данни; поле; критерий)", "d": "Сумира числата в полето (колоната) от записите в базата данни, които отговарят на зададените от вас условия"}, "DVAR": {"a": "(база_данни; поле; критерий)", "d": "Оценява дисперсия, базирана на извадка от избрани елементи от база данни"}, "DVARP": {"a": "(база_данни; поле; критерий)", "d": "Изчислява дисперсия на базата на цялата генерална съвкупност от избрани елементи от база данни"}, "CHAR": {"a": "(число)", "d": "Връща знака, съответстващ на кодовото число от набора знаци на вашия компютър"}, "CLEAN": {"a": "(текст)", "d": "Премахва всички непечатаеми знаци от текст"}, "CODE": {"a": "(текст)", "d": "Връща числовия код на първия знак в текстов низ, съставен от набора знаци, използван от вашия компютър"}, "CONCATENATE": {"a": "(текст1; [текст2]; ...)", "d": "Съединява няколко текстови низа в един текстов низ"}, "CONCAT": {"a": "(текст1; ...)", "d": "Конкатенира списък или диапазон от текстови низове"}, "DOLLAR": {"a": "(число; [десетични])", "d": "Преобразува число в текст, като използва валутен формат"}, "EXACT": {"a": "(текст1; текст2)", "d": "Проверява дали два текстови низа са напълно еднакви и връща TRUE или FALSE. EXACT различава малките и главните букви"}, "FIND": {"a": "(намери_текст; в_текст; [начален_ном])", "d": "Връща началната позиция на един текстов низ в друг текстов низ. FIND различава малките и главните букви"}, "FINDB": {"a": "(намери_текст; в_текст; [начален_ном])", "d": "Търся един текстов низ в друг текстов низ и връща номера на началната позиция на първия знак на първия текстов низ спрямо втория, може да се използва с езици, които използват набора знаци от по два байта (DBCS) - японски, китайски и корейски"}, "FIXED": {"a": "(число; [десетични]; [брой_запетаи])", "d": "Закръглява число до зададения брой десетични знаци и връща резултата като текст със или без запетаи"}, "LEFT": {"a": "(текст; [брой_знаци])", "d": "Връща зададения брой знаци от началото на текстов низ"}, "LEFTB": {"a": "(текст; [брой_знаци])", "d": "Връща първия знак или първите знаци в текстов низ, в зависимост от броя байтове, който зададете, може да се използва с езици, които използват набора знаци от по два байта (DBCS) - японски, китайски и корейски"}, "LEN": {"a": "(текст)", "d": "Връща броя на знаците в текстов низ"}, "LENB": {"a": "(текст)", "d": "Връща броя байтове за знаци в текстов низ, може да се използва с езици, които използват набора знаци от по два байта (DBCS) - японски, китайски и корейски"}, "LOWER": {"a": "(текст)", "d": "Преобразува всички букви от текстов низ в малки букви"}, "MID": {"a": "(текст; начален_ном; брой_знаци)", "d": "Връща знаците от средата на текстов низ при зададени начална позиция и дължина"}, "MIDB": {"a": "(текст; начален_ном; брой_знаци)", "d": "Връща определен брой знаци от текстов низ, започвайки от зададена от вас позиция, на базата на зададен от вас брой байтове, може да се използва с езици, които използват набора знаци от по два байта (DBCS) - японски, китайски и корейски"}, "NUMBERVALUE": {"a": "(текст; [десетичен_разделител]; [разделител_на_групи])", "d": "Преобразува текст в число по езиково независим начин"}, "PROPER": {"a": "(текст)", "d": "Преобразува по подходящ начин буквите в текстов низ; първата буква от всяка дума в главна, а всички други букви в малки"}, "REPLACE": {"a": "(стар_текст; начален_ном; брой_знаци; нов_текст)", "d": "Замества част от текстов низ с друг текстов низ"}, "REPLACEB": {"a": "(стар_текст; начален_ном; брой_знаци; нов_текст)", "d": "Замества част от текстов низ, на базата на броя байтове, които зададете, с различен текстов низ, може да се използва с езици, които използват набора знаци от по два байта (DBCS) - японски, китайски и корейски"}, "REPT": {"a": "(текст; брой_пъти)", "d": "Повтаря текст зададен брой пъти. Използвайте REPT, за да запълните клетка с множество екземпляри от текстов низ"}, "RIGHT": {"a": "(текст; [брой_знаци])", "d": "Връща зададения брой знаци от края на текстов низ"}, "RIGHTB": {"a": "(текст; [брой_знаци])", "d": "Връща последния знак или последните знаци в текстов низ в зависимост от броя байтове, който зададете, може да се използва с езици, които използват набора знаци от по два байта (DBCS) - японски, китайски и корейски"}, "SEARCH": {"a": "(намери_текст; в_текст; [начален_ном])", "d": "Връща броя на знаците, с които определен знак или текстов низ е намерен първоначално при обхождане отляво надясно (без да се отчитат разликите между малки и главни букви)"}, "SEARCHB": {"a": "(намери_текст; в_текст; [начален_ном])", "d": "Търся един текстов низ в друг текстов низ и връща номера на началната позиция на първия текстов низ спрямо първия знак на втория текстов низ, може да се използва с езици, които използват набора знаци от по два байта (DBCS) - японски, китайски и корейски"}, "SUBSTITUTE": {"a": "(текст; стар_текст; нов_текст; [срещане_ном])", "d": "Замества с нов текст съществуващ текст в текстов низ"}, "T": {"a": "(стойност)", "d": "Проверява дали една стойност е текст и връща текста, ако е, или двойни кавички (празен текст), ако не е"}, "TEXT": {"a": "(value; format_text)", "d": "                                                                                                                                                                                                      Преобразува стойност в текст със зададен числов формат"}, "TEXTJOIN": {"a": "(разделител; игнориране_празни; текст1; ...)", "d": "Конкатенира списък или диапазон от текстови низове, като използва разделител"}, "TRIM": {"a": "(текст)", "d": "Премахва всички интервали от текстов низ, освен единичните интервали между думите"}, "UNICHAR": {"a": "(число)", "d": "Връща Unicode знака, сочен от дадената числена стойност"}, "UNICODE": {"a": "(текст)", "d": "Връща номера (кодова точка), който съответства на първия знак на текста"}, "UPPER": {"a": "(текст)", "d": "Преобразува всички букви от текстов низ в главни букви"}, "VALUE": {"a": "(текст)", "d": "Преобразува в число текстов низ, съдържащ число"}, "AVEDEV": {"a": "(число1; [число2]; ...)", "d": "Връща средната стойност на абсолютните отклонения на точки данни от тяхната средна стойност. Аргументите могат да бъдат или числа, или имена, масиви или препратки, съдържащи числа"}, "AVERAGE": {"a": "(число1; [число2]; ...)", "d": "Връща средната стойност (средно аритметичното) на аргументите си, които могат да бъдат числа или имена, масиви или препратки, съдържащи числа"}, "AVERAGEA": {"a": "(стойност1; [стойност2]; ...)", "d": "Връща средната стойност (аритметичното средно) на аргументите си, като оценява текст и FALSE в аргументите като 0; TRUE се оценява като 1. Аргументите могат да бъдат имена на числа, масиви или препратки"}, "AVERAGEIF": {"a": "(диапазон; критерий; [среден_диапазон])", "d": "Намира средното (средно аритметичното) за клетките, удовлетворяващи дадено условие или критерии"}, "AVERAGEIFS": {"a": "(среден_диапазон; критерий_диапазон; критерий; ...)", "d": " Намира средното (средно аритметичното) за клетките, удовлетворяващи даден набор от условия или критерии"}, "BETADIST": {"a": "(x; алфа; бета; [A]; [B])", "d": "Връща кумулативната бета функция на вероятностната плътност"}, "BETAINV": {"a": "(вероятност; алфа; бета; [A]; [B])", "d": "Връща обратната кумулативна бета функция на вероятностната плътност (BETADIST)"}, "BETA.DIST": {"a": "(x; алфа; бета; кумулативна; [A]; [B])", "d": "Връща функцията за бета разпределение на вероятностите"}, "BETA.INV": {"a": "(вероятност; алфа; бета; [A]; [B])", "d": "Връща обратната кумулативна бета функция на вероятностната плътност (BETA.DIST)"}, "BINOMDIST": {"a": "(число_s; опити; вероятност_s; кумулативна)", "d": "Връща вероятност за биномиално разпределение на отделния член"}, "BINOM.DIST": {"a": "(число_s; опити; вероятност_s; кумулативна)", "d": "Връща вероятност за биномиално разпределение на отделния член"}, "BINOM.DIST.RANGE": {"a": "(опити; вероятност_s; брой_s; [брой_s2])", "d": "Връща вероятността за резултат от опит с използване на биномиално разпределение"}, "BINOM.INV": {"a": "(опити; вероятност_s; алфа)", "d": "Връща най-малката стойност, за която кумулативното биномиално разпределение е по-голямо или равно на една стойност, избрана за критерий"}, "CHIDIST": {"a": "(x; степ_свобода)", "d": "Връща едностранната вероятност на разпределението хи-квадрат"}, "CHIINV": {"a": "(вероятност; степ_свобода)", "d": "Връща обратната на едностранната вероятност на хи-квадрат разпределението"}, "CHITEST": {"a": "(действителен_диапазон; очакван_диапазон)", "d": "Връща теста за независимост: стойността от разпределението хи-квадрат за статистиката и съответните степени на свобода"}, "CHISQ.DIST": {"a": "(x; степ_свобода; кумулативна)", "d": "Връща ограниченото отляво разпределение на вероятност хи-квадрат"}, "CHISQ.DIST.RT": {"a": "(x; степ_свобода)", "d": "Връща ограниченото отдясно разпределение на вероятност хи-квадрат"}, "CHISQ.INV": {"a": "(вероятност; степ_свобода)", "d": "Връща обратното на ограниченото отдясно разпределение на вероятност хи-квадрат"}, "CHISQ.INV.RT": {"a": "(вероятност; степ_свобода)", "d": "Връща обратното на ограниченото отдясно разпределение на вероятност хи-квадрат"}, "CHISQ.TEST": {"a": "(действителен_диапазон; очакван_диапазон)", "d": "Връща теста за независимост: стойността от разпределението хи-квадрат за статистиката и съответните степени на свобода"}, "CONFIDENCE": {"a": "(алфа; стандартно_откл; размер)", "d": "Връща доверителния интервал за средната стойност на генералната съвкупност"}, "CONFIDENCE.NORM": {"a": "(алфа; стандартно_откл; размер)", "d": "Връща доверителния интервал за средната стойност на генералната съвкупност, използвайки нормално разпределение"}, "CONFIDENCE.T": {"a": "(алфа; стандартно_откл; размер)", "d": "Връща доверителния интервал за средната стойност на генералната съвкупност, използвайки T-разпределението на Стюдънт"}, "CORREL": {"a": "(масив1; масив2)", "d": "Връща коефициента на корелация между два набора данни"}, "COUNT": {"a": "(стойност1; [стойност2]; ...)", "d": "Преброява клетките, които съдържат числа"}, "COUNTA": {"a": "(стойност1; [стойност2]; ...)", "d": "Преброява клетките в диапазона, които не са празни"}, "COUNTBLANK": {"a": "(диапазон)", "d": "Преброява празните клетки в зададен диапазон от клетки"}, "COUNTIF": {"a": "(диа<PERSON><PERSON><PERSON><PERSON>н; критерий)", "d": "Преброява клетките в диапазон, отговарящи на зададено условие"}, "COUNTIFS": {"a": "(критерий_диапазон; критерий; ...)", "d": "Преброява броя на клетките, удовлетворяващи даден набор от условия или критерии"}, "COVAR": {"a": "(масив1; масив2)", "d": "Връща ковариацията – средната стойност на произведенията от отклоненията за всяка двойка от точки от данни в два набора от данни"}, "COVARIANCE.P": {"a": "(масив1; масив2)", "d": "Връща ковариацията на генералната съвкупност – средната стойност на произведенията от отклоненията за всяка двойка от точки от данни в два набора от данни"}, "COVARIANCE.S": {"a": "(масив1; масив2)", "d": "Връща ковариацията на извадката – средната стойност на произведенията от отклоненията за всяка двойка от точки от данни в два набора от данни"}, "CRITBINOM": {"a": "(опити; вероятност_s; алфа)", "d": "Връща най-малката стойност, за която кумулативното биномиално разпределение е по-голямо или равно на една стойност, избрана за критерий"}, "DEVSQ": {"a": "(число1; [число2]; ...)", "d": "Връща сумата на квадратите на отклоненията на точки данни от средната стойност на тяхната извадка"}, "EXPONDIST": {"a": "(x; ламбда; кумулативна)", "d": "Връща експоненциалното разпределение"}, "EXPON.DIST": {"a": "(x; ламбда; кумулативна)", "d": "Връща експоненциалното разпределение"}, "FDIST": {"a": "(x; степ_свобода1; степ_свобода2)", "d": "Връща вероятностното разпределение F (степента на различие) за два набора данни"}, "FINV": {"a": "(вероятност; степ_свобода1; степ_свобода2)", "d": "Връща обратното на вероятностното разпределение F: ако p = FDIST(x,...), тогава FINV(p,...) = x"}, "FTEST": {"a": "(масив1; масив2)", "d": "Връща резултата от F-тест – двустранната вероятност, че дисперсиите в \"масив1\" и \"масив2\" не са съществено различни"}, "F.DIST": {"a": "(x; степ_свобода1; степ_свобода2; кумулативна)", "d": "Връща (ограничено отляво) F разпределение на вероятността (степента на различие) за два набора данни"}, "F.DIST.RT": {"a": "(x; степ_свобода1; степ_свобода2)", "d": "Връща (ограничено отдясно) F разпределение на вероятността (степента на различие) за два набора данни"}, "F.INV": {"a": "(вероятност; степ_свобода1; степ_свобода2)", "d": "Връща обратното на (ограничено отляво) F разпределението на вероятността: ако p = F.DIST(x,...), тогава F.INV(p,...) = x"}, "F.INV.RT": {"a": "(вероятност; степ_свобода1; степ_свобода2)", "d": "Връща обратното на (ограничено отдясно) F разпределението на вероятността: ако p = F.DIST.RT(x,...), тогава F.INV.RT(p,...) = x"}, "F.TEST": {"a": "(масив1; масив2)", "d": "Връща резултата от F-тест – двустранната вероятност, че дисперсиите в \"масив1\" и \"масив2\" не са съществено различни"}, "FISHER": {"a": "(x)", "d": "Връща трансформацията на Фишер"}, "FISHERINV": {"a": "(y)", "d": "Връща обратната трансформация на Фишер: ако y = FISHER(x), тогава FISHERINV(y) = x"}, "FORECAST": {"a": "(x; известни_y; известни_x)", "d": "Изчислява или прогнозира бъдеща стойност при линейна тенденция, като използва съществуващи стойности"}, "FORECAST.ETS": {"a": "(целева_дата; стойности; времева_линия; [сезонност]; [допълване_данни]; [агрегиране])", "d": "Връща прогнозната стойност за определена целева дата в бъдещето, като използва метода на експоненциално изглаждане."}, "FORECAST.ETS.CONFINT": {"a": "(целева_дата; стойности; времева_линия; [степен_вероятност]; [сезонност]; [допълване_данни]; [агрегиране])", "d": "Връща интервала на вероятност за прогнозната стойност на зададената целева дата."}, "FORECAST.ETS.SEASONALITY": {"a": "(стойности; времева_линия; [допълване_данни]; [агрегиране])", "d": "Връща дължината на повтарящия се модел, който приложението открива за определени времеви серии."}, "FORECAST.ETS.STAT": {"a": "(стойности; времева_линия; тип_статистика; [сезонност]; [допълване_данни]; [агрегиране])", "d": "Връща исканата статистика за прогнозата."}, "FORECAST.LINEAR": {"a": "(x; известни_y; известни_x)", "d": "Изчислява или прогнозира бъдеща стойност при линейна тенденция, като използва съществуващи стойности"}, "FREQUENCY": {"a": "(данни_масив; двоични_масив)", "d": "Изчислява колко често се срещат стойностите в диапазон от стойности и връща вертикален масив от числа, който има един елемент повече от \"двоични_масив\""}, "GAMMA": {"a": "(x)", "d": "Връща стойността на гама функцията"}, "GAMMADIST": {"a": "(x; алфа; бета; кумулативна)", "d": "Връща гама разпределението"}, "GAMMA.DIST": {"a": "(x; алфа; бета; кумулативна)", "d": "Връща гама разпределението"}, "GAMMAINV": {"a": "(вероятност; алфа; бета)", "d": "Връща обратното на кумулативното гама разпределение: ако p = GAMMADIST(x,...), тогава GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(вероятност; алфа; бета)", "d": "Връща обратното на кумулативното гама разпределение: ако p = GAMMA.DIST(x,...), тогава GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Връща натуралните логаритми от гама функцията"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Връща натуралните логаритми от гама функцията"}, "GAUSS": {"a": "(x)", "d": "Връща с 0,5 по-малко от стандартното нормално кумулативно разпределение"}, "GEOMEAN": {"a": "(число1; [число2]; ...)", "d": "Връща геометричната средна стойност на масив или диапазон от положителни числови данни"}, "GROWTH": {"a": "(известни_y; [известни_x]; [нови_x]; [конст])", "d": " Връща числа в тенденция с експоненциално нарастване, отговаряща на известни точки данни"}, "HARMEAN": {"a": "(число1; [число2]; ...)", "d": "Връща хармоничната средна стойност на набор данни от положителни числа - реципрочната стойност на аритметичното средно на реципрочните стойности"}, "HYPGEOM.DIST": {"a": "(извадка_s; число_извадка; съвкупност_s; число_съвк; кумулативна)", "d": "Връща хипергеометричното разпределение"}, "HYPGEOMDIST": {"a": "(извадка_s; брой_извадка; ген_съвкупност_s; брой_съвк)", "d": "Връща хипергеометричното разпределение"}, "INTERCEPT": {"a": "(известни_y; известни_x)", "d": "Изчислява точката, в която една линия ще пресече оста y, като използва регресионна линия с най-добро приближение, прекарана през известните x-стойности и y-стойности"}, "KURT": {"a": "(число1; [число2]; ...)", "d": "Връща ексцеса на набор данни"}, "LARGE": {"a": "(масив; k)", "d": "Връща k-тата по големина стойност в набор данни, например петото по големина число"}, "LINEST": {"a": "(известни_y; [известни_x]; [конст]; [състояния])", "d": "Връща статистики, описващи линейната тенденция, отговаряща на известни точки данни, чрез интерполиране с права линия, като използва метода на най-малките квадрати"}, "LOGEST": {"a": "(известни_y; [известни_x]; [конст]; [състояния])", "d": " Връща статистики, описващи експоненциална крива, отговаряща на известни точки данни"}, "LOGINV": {"a": "(вероятност; средно; стандартно_откл)", "d": "Връща обратното на логонормалната кумулативна функция на разпределение на x, където ln(x) е нормално разпределена с параметри \"средно\" и \"стандартно_откл\""}, "LOGNORM.DIST": {"a": "(x; средно; стандартно_откл; кумулативна)", "d": "Връща логнормално разпределение на x, където ln(x) е нормално разпределено с параметри \"средно\" и \"стандартно_откл\""}, "LOGNORM.INV": {"a": "(вероятност; средно; стандартно_откл)", "d": "Връща обратното на логонормалната кумулативна функция на разпределение на x, където ln(x) е нормално разпределена с параметри \"средно\" и \"стандартно_откл\""}, "LOGNORMDIST": {"a": "(x; средно; стандартно_откл)", "d": "Връща кумулативното логонормално разпределение на x, където ln(x) е нормално разпределена с параметри \"средно\" и \"стандартно_откл\""}, "MAX": {"a": "(число1; [число2]; ...)", "d": "Връща най-голямата стойност в набор от стойности. Игнорира логически стойности и текст"}, "MAXA": {"a": "(стойност1; [стойност2]; ...)", "d": "Връща най-голямата стойност в набор от стойности. Не игнорира логически стойности и текст"}, "MAXIFS": {"a": "(макс_диапазон; критерий_диапазон; критерий; ...)", "d": "Връща максималната стойност от клетките, зададени от даден набор от условия или критерии"}, "MEDIAN": {"a": "(число1; [число2]; ...)", "d": "Връща медианата или числото в средата на набора от зададени числа"}, "MIN": {"a": "(число1; [число2]; ...)", "d": "Връща най-малкото число в набор от стойности. Игнорира логически стойности и текст"}, "MINA": {"a": "(стойност1; [стойност2]; ...)", "d": "Връща най-малката стойност в набор от стойности. Не игнорира логически стойности и текст"}, "MINIFS": {"a": "(мин_диапазон; критерий_диапазон; критерий; ...)", "d": "Връща минималната стойност от клетките, зададени от даден набор от условия или критерии"}, "MODE": {"a": "(число1; [число2]; ...)", "d": "Връща най-често срещащата се или повтаряща се стойност в масив или диапазон от данни – мода"}, "MODE.MULT": {"a": "(число1; [число2]; ...)", "d": "Връща вертикален масив на най-често появяващите се или повтарящи се стойности в масив или диапазон от данни. За хоризонтален масив използвайте =TRANSPOSE(MODE.MULT(число1,число2,...))"}, "MODE.SNGL": {"a": "(число1; [число2]; ...)", "d": "Връща най-често срещащата се или повтаряща се стойност в масив или диапазон от данни – мода"}, "NEGBINOM.DIST": {"a": "(число_f; число_s; вероятност_s; кумулативна)", "d": "Връща отрицателното биномиално разпределение – вероятността да има \"число_f\" неуспешни опита преди \"число_s\"-я успех, с вероятност за успех \"вероятност_s\""}, "NEGBINOMDIST": {"a": "(число_f; число_s; вероятност_s)", "d": "Връща отрицателното биномиално разпределение – вероятността да има \"число_f\" неуспешни опита преди \"число_s\"-я успех, с вероятност за успех \"вероятност_s\""}, "NORM.DIST": {"a": "(x; средно; стандартно_откл; кумулативна)", "d": "Връща нормалното разпределение за зададени средна стойност и стандартно отклонение"}, "NORMDIST": {"a": "(x; средно; стандартно_откл; кумулативна)", "d": "Връща нормалното кумулативно разпределение при зададени средна стойност и стандартно отклонение"}, "NORM.INV": {"a": "(вероятност; средно; стандартно_откл)", "d": "Връща обратното на нормалното кумулативно разпределение за зададени средна стойност и стандартно отклонение"}, "NORMINV": {"a": "(вероятност; средно; стандартно_откл)", "d": "Връща обратното на нормалното кумулативно разпределение за зададени средна стойност и стандартно отклонение"}, "NORM.S.DIST": {"a": "(z; кумулативна)", "d": "Връща стандартното нормално разпределение (има средна стойност нула и стандартно отклонение единица)"}, "NORMSDIST": {"a": "(z)", "d": "Връща стандартното нормално кумулативно разпределение (със средна стойност нула и стандартно отклонение единица)"}, "NORM.S.INV": {"a": "(вероятност)", "d": "Връща обратното на стандартното нормално кумулативно разпределение (със средна стойност нула и стандартното отклонение единица)"}, "NORMSINV": {"a": "(вероятност)", "d": "Връща обратното на стандартното нормално кумулативно разпределение (със средна стойност нула и стандартното отклонение единица)"}, "PEARSON": {"a": "(масив1; масив2)", "d": "Връща коефициента на моментна корелация на Пиърсоново произведение r"}, "PERCENTILE": {"a": "(масив; k)", "d": "Връща k-тия процентил на стойностите в диапазон"}, "PERCENTILE.EXC": {"a": "(масив; k)", "d": "Връща k-тия процентил на стойностите в диапазон, където k е в диапазона 0..1, включително"}, "PERCENTILE.INC": {"a": "(масив; k)", "d": "Връща k-тия процентил на стойностите в диапазон, където k е в диапазона 0..1, включително"}, "PERCENTRANK": {"a": "(масив; x; [значимост])", "d": "Връща ранга на една стойност в набор данни във вид на процент от набора данни"}, "PERCENTRANK.EXC": {"a": "(масив; x; [значимост])", "d": "Връща ранга на стойност в набор от данни като процент (0..1, включително) от набора данни"}, "PERCENTRANK.INC": {"a": "(масив; x; [значимост])", "d": "Връща ранга на стойност в набор от данни като процент (0..1, включително) от набора данни"}, "PERMUT": {"a": "(число; число_избрано)", "d": "Връща броя на пермутациите за зададено множество обекти, избрани от всички обекти"}, "PERMUTATIONA": {"a": "(число; избран_брой)", "d": "Връща броя пермутации за даден брой обекти (с повторения), които могат да бъдат избрани от всички обекти"}, "PHI": {"a": "(x)", "d": "Връща стойността на функцията на плътността на вероятностите за стандартно нормално разпределение"}, "POISSON": {"a": "(x; средно; кумулативна)", "d": "Връща разпределението на Поасон"}, "POISSON.DIST": {"a": "(x; средно; кумулативна)", "d": "Връща разпределението на Поасон"}, "PROB": {"a": "(x_диапазон; вероятн_диапазон; долна_граница; [горна_граница])", "d": "Връща вероятността стойностите в един диапазон да са между две граници или равни на една долна граница"}, "QUARTILE": {"a": "(масив; кварт)", "d": "Връща квартила на набор данни"}, "QUARTILE.INC": {"a": "(масив; кварт)", "d": "Връща квартила на набор от данни, базиран на процентилни стойности от 0..1, включително"}, "QUARTILE.EXC": {"a": "(масив; кварт)", "d": "Връща квартила на набор от данни, базиран на процентилни стойности от 0..1, включително"}, "RANK": {"a": "(число; препр; [ред])", "d": "Връща ранга на число в списък от числа: големината на числото спрямо другите стойности в списъка"}, "RANK.AVG": {"a": "(число; препр; [ред])", "d": "Връща ранга на число в списък от числа: големината на числото спрямо другите стойности в списъка; ако повече от едно числа имат еднакъв ранг, връща се средният ранг"}, "RANK.EQ": {"a": "(число; препр; [ред])", "d": "Връща ранга на число в списък от числа: големината на числото спрямо другите стойности в списъка; ако повече от едно числа имат еднакъв ранг, връща се най-високият ранг на този набор от стойности"}, "RSQ": {"a": "(известни_y; известни_x)", "d": "Връща квадрата на коефициента на моментна корелация на Пиърсоново произведение през дадените точки данни"}, "SKEW": {"a": "(число1; [число2]; ...)", "d": "Връща несиметричността на разпределение - характеристика на степента на асиметрия на едно разпределение около неговата средна стойност"}, "SKEW.P": {"a": "(число1; [число2]; ...)", "d": "Връща несиметричността на разпределение на базата на генерална съвкупност: характеризиране на асиметрията на разпределение около средната му стойност"}, "SLOPE": {"a": "(известни_y; известни_x)", "d": "Връща наклона на линията на линейна регресия през зададените точки данни"}, "SMALL": {"a": "(масив; k)", "d": "Връща k-тата най-малка стойност в набор данни, например петото най-малко число"}, "STANDARDIZE": {"a": "(x; средно; стандартно_откл)", "d": "Връща нормализирана стойност от разпределение, определено чрез средна стойност и стандартно отклонение"}, "STDEV": {"a": "(число1; [число2]; ...)", "d": "Връща най-често срещащата се или повтаряща се стойност в масив или диапазон от данни – мода"}, "STDEV.P": {"a": "(число1; [число2]; ...)", "d": "Изчислява стандартното отклонение на базата на цялата генерална съвкупност, зададена като аргументи (игнорира логически стойности и текст)"}, "STDEV.S": {"a": "(число1; [число2]; ...)", "d": "Изчислява стандартното отклонение на базата на извадка (игнорира логически стойности и текст в извадката)"}, "STDEVA": {"a": "(стойност1; [стойност2]; ...)", "d": "Оценява стандартното отклонение на базата на извадка, включваща логически стойности и текст. Текстът и логическата стойност FALSE имат стойност 0; логическата стойност TRUE има стойност 1"}, "STDEVP": {"a": "(число1; [число2]; ...)", "d": "Изчислява стандартното отклонение на базата на цялата генерална съвкупност, зададена като аргументи (игнорира логически стойности и текст)"}, "STDEVPA": {"a": "(стойност1; [стойност2]; ...)", "d": "Изчислява стандартното отклонение на базата на цялата генерална съвкупност, включително логическите стойности и текста. Текстът и логическата стойност FALSE имат стойност 0; логическата стойност TRUE има стойност 1"}, "STEYX": {"a": "(известни_y; известни_x)", "d": "Връща стандартната грешка на предсказаната стойност на y за всяко x в регресия"}, "TDIST": {"a": "(x; степ_свобода; опашки)", "d": "Връща t-разпределението на Стюдънт"}, "TINV": {"a": "(вероятност; степ_свобода)", "d": "Връща обратното на t-разпределението на Стюдънт"}, "T.DIST": {"a": "(x; степ_свобода; кумулативна)", "d": "Връща ограниченото отляво t-разпределение на Стюдънт"}, "T.DIST.2T": {"a": "(x; степ_свобода)", "d": "Връща двустранно ограниченото t-разпределение на Стюдънт"}, "T.DIST.RT": {"a": "(x; степ_свобода)", "d": "Връща ограниченото отдясно t-разпределение на Стюдънт"}, "T.INV": {"a": "(вероятност; степ_свобода)", "d": "Връща ограниченото отляво t-разпределение на Стюдънт"}, "T.INV.2T": {"a": "(вероятност; степ_свобода)", "d": "Връща обратното на двустранно ограниченото t-разпределение на Стюдънт"}, "T.TEST": {"a": "(масив1; масив2; опашки; тип)", "d": "Връща вероятността, свързана с t-теста на Стюдънт"}, "TREND": {"a": "(известни_y; [известни_x]; [нови_x]; [конст])", "d": "Връща числа в линейна тенденция, отговаряща на известни точки данни, като използва метода на най-малките квадрати"}, "TRIMMEAN": {"a": "(масив; процент)", "d": "Връща средната стойност на вътрешна част от стойности  в набор от данни"}, "TTEST": {"a": "(масив1; масив2; опашки; тип)", "d": "Връща вероятността, свързана с t-теста на Стюдънт"}, "VAR": {"a": "(число1; [число2]; ...)", "d": "Изчислява дисперсия, базирана на извадка (игнорира логическите стойности и текста в извадката)"}, "VAR.P": {"a": "(число1; [число2]; ...)", "d": "Изчислява дисперсия на базата на цялата генерална съвкупност (игнорира логическите стойности и текста в съвкупността)"}, "VAR.S": {"a": "(число1; [число2]; ...)", "d": "Изчислява дисперсия, базирана на извадка (игнорира логическите стойности и текста в извадката)"}, "VARA": {"a": "(стойност1; [стойност2]; ...)", "d": "Оценява дисперсия на базата на извадка, включваща логически стойности и текст. Текстът и логическата стойност FALSE имат стойност 0; логическата стойност TRUE има стойност 1"}, "VARP": {"a": "(число1; [число2]; ...)", "d": "Изчислява дисперсия на базата на цялата генерална съвкупност (игнорира логическите стойности и текста в съвкупността)"}, "VARPA": {"a": "(стойност1; [стойност2]; ...)", "d": "Изчислява дисперсия на базата на цялата генерална съвкупност, включително логически стойности и текст. Текстът и логическата стойност FALSE имат стойност 0; логическата стойност TRUE има стойност 1"}, "WEIBULL": {"a": "(x; алфа; бета; кумулативна)", "d": "Връща разпределението на Уейбул"}, "WEIBULL.DIST": {"a": "(x; алфа; бета; кумулативна)", "d": "Връща разпределението на Уейбул"}, "Z.TEST": {"a": "(масив; x; [сигма])", "d": "Връща едностранната P-стойност на z-тест"}, "ZTEST": {"a": "(масив; x; [сигма])", "d": "Връща едностранната P-стойност на z-тест"}, "ACCRINT": {"a": "(издание; първа_лихва; разчет; ставка; пар; честота; [база]; [изч_метод])", "d": "Връща натрупаната лихва за ценни книжа, по които периодично се плаща лихва."}, "ACCRINTM": {"a": "(издание; разчет; ставка; пар; [база])", "d": "Връща натрупаната лихва за ценни книжа, по които се плаща лихва при погасяване"}, "AMORDEGRC": {"a": "(стойност; дата_покупка; първи_период; ликвидационна_стойност; период; ставка; [база])", "d": "Връща величината на пропорционално разпределената амортизация на актива за всеки осчетоводяван период."}, "AMORLINC": {"a": "(стойност; дата_покупка; първи_период; ликвидационна_стойност; период; ставка; [база])", "d": "Връща величината на пропорционално разпределената амортизация на актива за всеки осчетоводяван период."}, "COUPDAYBS": {"a": "(разчет; погасяване; честота; [база])", "d": "Връща броя на дните от началото на действие на купона до датата на разчета"}, "COUPDAYS": {"a": "(разчет; погасяване; честота; [база])", "d": "Връща броя на дните в периода на купона, който съдържа датата на разчета"}, "COUPDAYSNC": {"a": "(разчет; погасяване; честота; [база])", "d": "Връща броя на дните от датата на разчета до датата на следващия купон"}, "COUPNCD": {"a": "(разчет; погасяване; честота; [база])", "d": "Връща номера на датата на следващия купон след датата на разчета"}, "COUPNUM": {"a": "(разчет; погасяване; честота; [база])", "d": "Връща броя на изплащаните купони между датата на разчета и датата на погасяване на ценните книжа"}, "COUPPCD": {"a": "(разчет; погасяване; честота; [база])", "d": "Връща поредния номер на датата на предишния купон преди датата на разчета"}, "CUMIPMT": {"a": "(ставка; периоди; pv; начален_период; краен_период; тип)", "d": "Връща кумулативната (с натрупване) стойност на лихвата, изплащана между два периода"}, "CUMPRINC": {"a": "(ставка; периоди; pv; начален_период; краен_период; тип)", "d": "Връща кумулативната (с натрупване) сума, изплащана за погасяване основната сума на заема в промеждутъка между два периода"}, "DB": {"a": "(стойност; ликвидационна_стойност; живот; период; [месец])", "d": "Връща амортизацията на актив за зададен период, като използва балансния метод с фиксирано намаление"}, "DDB": {"a": "(стойност; ликвидационна_стойност; живот; период; [коефициент])", "d": "Връща амортизацията на актив за зададен период, като използва балансния метод с двойно намаляване или някой друг зададен от вас метод"}, "DISC": {"a": "(разчет; погасяване; pr; изкупуване; [база])", "d": "Връща ставката за отстъпка за ценни книжа"}, "DOLLARDE": {"a": "(дробен_долар; дроб)", "d": "Преобразува цена в долари, изразена като дроб, в цена в долари, изразена като десетично число"}, "DOLLARFR": {"a": "(десетичен_долар; дроб)", "d": "Преобразува цена в долари, изразена като десетично число, в цена в долари, изразена като дроб"}, "DURATION": {"a": "(разчет; погасяване; купон; доходност; честота; [база])", "d": "Връща годишното времетраене на ценни книжа, по които се изплаща периодична лихва"}, "EFFECT": {"a": "(номина<PERSON><PERSON>_ставка; периоди_г)", "d": "Връща фактическата (ефективна) годишна лихвена ставка"}, "FV": {"a": "(процент; периоди; плащ; [pv]; [тип])", "d": "Връща бъдещата стойност на инвестиция, базирана на периодични постоянни плащания и постоянна лихва"}, "FVSCHEDULE": {"a": "(главница; график)", "d": "Връща бъдещото значение на първоначалната основна сума след прилагане на редица (планови) сложни лихвени ставки"}, "INTRATE": {"a": "(разчет; погасяване; инвестиция; изкупуване; [база])", "d": "Връща лихвената ставка за напълно инвестирани ценни книжа"}, "IPMT": {"a": "(ставка; период; периоди; pv; [fv]; [тип])", "d": "Връща лихвеното плащане за даден период за инвестиция, базирана на периодични постоянни плащания и постоянна лихва"}, "IRR": {"a": "(стойности; [предположение])", "d": "Връща вътрешната норма на печалба за серия от парични потоци"}, "ISPMT": {"a": "(ставка; пер; периоди; pv)", "d": "Връща лихвата, плащана по време на определен период на инвестиция"}, "MDURATION": {"a": "(разчет; погасяване; купон; доходност; честота; [база])", "d": "Връща модифицираното времетраене на Макалей за ценни книжа с предполагаема номинална стойност 100 лв."}, "MIRR": {"a": "(стойности; финансиране_коефициент; реинвестиране_коефициент)", "d": "Връща вътрешната норма на печалба за серия от периодични парични потоци, отчитайки както цената на инвестицията, така и лихвата при реинвестиране на наличностите"}, "NOMINAL": {"a": "(факт_ставка; периоди)", "d": "Връща номиналната годишна лихвена ставка"}, "NPER": {"a": "(ставка; плащ; pv; [fv]; [тип])", "d": "Връща броя периоди за инвестиция, базирана на периодични постоянни плащания и постоянна лихва"}, "NPV": {"a": "(ставка; стойност1; [стойност2]; ...)", "d": "Връща нетната настояща стойност на инвестиция, базирана на дисконтова ставка, и серия от бъдещи плащания (отрицателни стойности) и приход (положителни стойности)"}, "ODDFPRICE": {"a": "(разчет; погасяване; издаване; пръв_купон; ставка; доходност; изкупуване; честота; [база])", "d": "Връща цената на ценни книжа с номинална стойност 100 лв. и с нередовен пръв период"}, "ODDFYIELD": {"a": "(разчет; погасяване; издаване; пръв_купон; ставка; pr; изкупуване; честота; [база])", "d": "Връща доход по ценни книжа с нередовен пръв период"}, "ODDLPRICE": {"a": "(разчет; погасяване; последна_лихва; ставка; доходност; изкупуване; честота; [база])", "d": "Връща цената за номинална стойност 100 лв.на ценни книжа с нередовен последен период на купона"}, "ODDLYIELD": {"a": "(разчет; погасяване; последна_лихва; ставка; pr; изкупуване; честота; [база])", "d": "Връща доходността на ценни книжа с нередовен последен период"}, "PDURATION": {"a": "(оценка; настояща_стойност; бъдеща_стойност)", "d": "Връща броя периоди, необходими, за да достигне дадена инвестиция указана стойност"}, "PMT": {"a": "(ставка; периоди; pv; [fv]; [тип])", "d": "Изчислява изплащането за заем на базата на постоянни плащания и постоянна лихва"}, "PPMT": {"a": "(ставка; период; периоди; pv; [fv]; [тип])", "d": "Връща плащането върху главницата за дадена инвестиция, базирана на периодични постоянни плащания и постоянна лихва"}, "PRICE": {"a": "(разчет; погасяване; ставка; доходност; изкупуване; честота; [база])", "d": "Връща цената за номинална стойност 100 лв. на ценни книжа, по които се изплаща периодична лихва"}, "PRICEDISC": {"a": "(разчет; погасяване; отстъпка; изкупуване; [база])", "d": "Връща цената за номинална стойност 100 лв. на ценни книжа, за които е направена отстъпка"}, "PRICEMAT": {"a": "(разчет; погасяване; издание; ставка; доходност; [база])", "d": "Връща цената за 100 лв. номинална стойност на ценни книжа, по които се изплаща лихва при погасяване"}, "PV": {"a": "(ставка; периоди; плащ; [fv]; [тип])", "d": "Връща настоящата стойност на инвестиция: общата сума, която серията от бъдещи плащания струва сега"}, "RATE": {"a": "(периоди; плащ; pv; [fv]; [тип]; [предположение])", "d": "Връща лихвата за период на заем или инвестиция. Например използвайте 6%/4 за тримесечни плащания при 6% APR"}, "RECEIVED": {"a": "(разчет; погасяване; инвестиция; отстъпка; [база])", "d": "Връща сумата, получена към срока на погасяване за напълно инвестирани ценни книжа"}, "RRI": {"a": "(периоди; настояща_стойност; бъдеща_стойност)", "d": "Връща еквивалентна лихва за нарастването на инвестиция"}, "SLN": {"a": "(стойност; ликвидационна_стойност; живот)", "d": "Връща линейната амортизация на един актив за един период"}, "SYD": {"a": "(стойност; ликвидационна_стойност; живот; пер)", "d": "Връща амортизацията на актив за зададен период, изчислена по метода \"Сума на годините\""}, "TBILLEQ": {"a": "(разчет; погасяване; отстъпка)", "d": "Връща еквивалентен доход за съкровищни бонове"}, "TBILLPRICE": {"a": "(разчет; погасяване; отстъпка)", "d": "Връща цената за номинална стойност 100 лв. на един съкровищен бон"}, "TBILLYIELD": {"a": "(разчет; погасяване; pr)", "d": "Връща доходността на съкровищен бон"}, "VDB": {"a": "(стойност; ликвидационна_стойност; живот; начален_период; краен_период; [коефициент]; [не_превключвай])", "d": "Връща амортизацията на актив за произволен зададен период, включващ частични периоди, като използва балансния метод с двойно намаляване или някой друг зададен от вас метод"}, "XIRR": {"a": "(стойности; дати; [предположение])", "d": "Връща вътрешната ставка на доходност за график от парични потоци"}, "XNPV": {"a": "(ставка; стойности; дати)", "d": "Връща чистата приведена стойност за график от парични потоци"}, "YIELD": {"a": "(разчет; погасяване; ставка; pr; изкупуване; честота; [база])", "d": "Връща доходността на ценни книжа, по които се плаща периодична лихва"}, "YIELDDISC": {"a": "(разчет; погасяване; pr; изкупуване; [база])", "d": "Връща годишната доходност на ценни книжа, за които е направена отстъпка. Например един съкровищен бон"}, "YIELDMAT": {"a": "(разчет; погасяване; издание; ставка; pr; [база])", "d": "Връща годишната доходност на ценни книжа, по които се изплаща лихва при погасяване"}, "ABS": {"a": "(число)", "d": "Връща абсолютната стойност на число, която представлява числото без знака му"}, "ACOS": {"a": "(число)", "d": "Връща аркускосинуса от число в радиани в диапазона от 0 до Pi. Аркускосинусът е ъгълът, чийто косинус е \"число\""}, "ACOSH": {"a": "(число)", "d": "Връща обратния хиперболичен косинус от число"}, "ACOT": {"a": "(число)", "d": "Връща аркускотангенса от число в радиани в диапазона от 0 до пи."}, "ACOTH": {"a": "(число)", "d": "Връща обратния хиперболичен котангенс от число"}, "AGGREGATE": {"a": "(function_ном; опции; препр1; ...)", "d": "Връща агрегат в списък или база данни"}, "ARABIC": {"a": "(текст)", "d": "Преобразува римска цифра в арабска"}, "ASC": {"a": "(текст)", "d": "За езици с набори от двубайтови знаци (DBCS), функцията променя знаците с пълна ширина (двубайтови) в знаци с половин ширина (еднобайтови)"}, "ASIN": {"a": "(число)", "d": "Връща аркуссинуса от число в радиани в диапазона -Pi/2 до Pi/2"}, "ASINH": {"a": "(число)", "d": "Връща обратния хиперболичен синус от число"}, "ATAN": {"a": "(число)", "d": "Връща аркустангенса от число в радиани в диапазона от -Pi/2 до Pi/2"}, "ATAN2": {"a": "(x_числ; y_числ)", "d": "Връща аркустангенса от зададените x и y координати в радиани между -Pi и Pi, с изключение на -Pi"}, "ATANH": {"a": "(число)", "d": "Връща обратния хиперболичен тангенс от число"}, "BASE": {"a": "(число; основа; [минимална_дължина])", "d": "Преобразува число в текстово представяне с дадена основа (база)"}, "CEILING": {"a": "(число; значимост)", "d": "Закръглява число нагоре до най-близкото кратно на значимост"}, "CEILING.MATH": {"a": "(число; [значимост]; [режим])", "d": "Закръглява число нагоре до най-близкото цяло число или най-близкото кратно на \"значимост\""}, "CEILING.PRECISE": {"a": "(число; [значимост])", "d": "Връща число, което е закръглено нагоре до най-близкото цяло число или до най-близкото кратно на значимост"}, "COMBIN": {"a": "(число; число_избрано)", "d": "Връща броя комбинации за зададен брой елементи"}, "COMBINA": {"a": "(число; избран_брой)", "d": "Връща броя комбинации с повторения за даден брой от елементи"}, "COS": {"a": "(число)", "d": "Връща косинуса от ъгъл"}, "COSH": {"a": "(число)", "d": "Връща хиперболичния косинус от число"}, "COT": {"a": "(число)", "d": "Връща котангенса от ъгъл"}, "COTH": {"a": "(число)", "d": "Връща хиперболичния котангенс от число"}, "CSC": {"a": "(число)", "d": "Връща косеканса от ъгъл"}, "CSCH": {"a": "(число)", "d": "Връща хиперболичния косеканс от ъгъл"}, "DECIMAL": {"a": "(число; основа)", "d": "Преобразува текстово представяне на число с дадена база в десетично число"}, "DEGREES": {"a": "(ъгъл)", "d": "Преобразува радиани в градуси"}, "ECMA.CEILING": {"a": "(число; [значимост])", "d": "Закръглява число нагоре до най-близкото кратно на значимост"}, "EVEN": {"a": "(число)", "d": "Закръглява положително число нагоре и отрицателно число надолу до най-близкото четно цяло число"}, "EXP": {"a": "(число)", "d": "Връща \"e\" на степен зададено число"}, "FACT": {"a": "(число)", "d": "Връща факториела на число, който е равен на 1*2*3*...* число"}, "FACTDOUBLE": {"a": "(число)", "d": "Връща двойния факториел на едно число"}, "FLOOR": {"a": "(число; значимост)", "d": "Закръглява число надолу до най-близкото кратно на \"значимост\""}, "FLOOR.PRECISE": {"a": "(число; [значимост])", "d": "Връща число, което е закръглено надолу до най-близкото цяло число или до най-близкото кратно на значимост"}, "FLOOR.MATH": {"a": "(число; [значимост]; [режим])", "d": "Закръглява число надолу до най близкото цяло число или най-близкото кратно на \"значимост\""}, "GCD": {"a": "(число1; [число2]; ...)", "d": "Връща най-големия общ делител"}, "INT": {"a": "(число)", "d": "Закръглява число надолу до най-близкото цяло число"}, "ISO.CEILING": {"a": "(число; [значимост])", "d": "Връща число, което е закръглено нагоре до най-близкото цяло число или до най-близкото кратно на значимост. Числото се закръглява нагоре, независимо от знака си. Ако обаче числото или значимост са нула, връща се нула."}, "LCM": {"a": "(число1; [число2]; ...)", "d": "Връща най-малкото общо кратно"}, "LN": {"a": "(число)", "d": "Връща натуралния логаритъм от число"}, "LOG": {"a": "(число; [основа])", "d": "Връща логаритъма от число при зададена от вас основа"}, "LOG10": {"a": "(число)", "d": "Връща логаритъма с основа 10 от число"}, "MDETERM": {"a": "(масив)", "d": "Връща детерминантата на матрица, представена като масив"}, "MINVERSE": {"a": "(масив)", "d": "Връща обратната матрица на матрица, записана в масив"}, "MMULT": {"a": "(масив1; масив2)", "d": "Връща матричното произведение на два масива, което представлява масив със същия брой редове като \"масив1\" и същия брой колони като \"масив2\""}, "MOD": {"a": "(число; делител)", "d": "Връща остатъка, след като едно число е разделено с делител"}, "MROUND": {"a": "(число; точност)", "d": "Връща закръглено число с желана точност"}, "MULTINOMIAL": {"a": "(число1; [число2]; ...)", "d": "Връща отношението на факториела на сума стойности към произведението от факториелите на стойностите на едно множество от числа"}, "MUNIT": {"a": "(размер)", "d": "Връща единична матрица за указания размер"}, "ODD": {"a": "(число)", "d": "Закръглява положително число нагоре и отрицателно число надолу до най-близкото нечетно цяло число"}, "PI": {"a": "()", "d": "Връща стойността на Pi, 3,14159265358979 с точност до 15 цифри"}, "POWER": {"a": "(число; степен)", "d": "Връща резултата от повдигането на число на степен"}, "PRODUCT": {"a": "(число1; [число2]; ...)", "d": "Умножава всички числа, зададени като аргументи"}, "QUOTIENT": {"a": "(числител; знам<PERSON>на<PERSON>е<PERSON>)", "d": "Връща цялата част при деление"}, "RADIANS": {"a": "(ъгъл)", "d": "Преобразува градуси в радиани"}, "RAND": {"a": "()", "d": "Връща случайно число, по-голямо или равно на 0 и по-малко от 1, равномерно разпределено (променя се при преизчисляване)"}, "RANDARRAY": {"a": "([редове]; [колони]; [мин]; [макс]; [цяло_число])", "d": "Връща масив от случайни числа"}, "RANDBETWEEN": {"a": "(долно; горно)", "d": "Връща случайно число между две зададени числа"}, "ROMAN": {"a": "(число; [форма])", "d": "Преобразува арабско число в римско във вид на текст"}, "ROUND": {"a": "(число; брой_цифри)", "d": "Закръглява число до зададен брой цифри"}, "ROUNDDOWN": {"a": "(число; брой_цифри)", "d": "Закръглява число надолу към нулата"}, "ROUNDUP": {"a": "(число; брой_цифри)", "d": "Закръглява число нагоре, в обратна на нулата посока"}, "SEC": {"a": "(число)", "d": "Връща секанса от ъгъл"}, "SECH": {"a": "(число)", "d": "Връща хиперболичния секанс от ъгъл"}, "SERIESSUM": {"a": "(x; n; m; коефициенти)", "d": "Връща сумата на един степенен ред, основан на формула"}, "SIGN": {"a": "(число)", "d": "Връща знака на число: 1, ако числото е положително, нула, ако числото е нула, или -1, ако числото е отрицателно"}, "SIN": {"a": "(число)", "d": "Връща синуса от ъгъл"}, "SINH": {"a": "(число)", "d": "Връща хиперболичния синус от число"}, "SQRT": {"a": "(число)", "d": "Връща квадратния корен от число"}, "SQRTPI": {"a": "(число)", "d": "Връща квадратния корен на (числото * пи)"}, "SUBTOTAL": {"a": "(функция_ном; препр1; ...)", "d": "Връща междинна сума в списък или база данни"}, "SUM": {"a": "(число1; [число2]; ...)", "d": "Сумира всички числа в диапазон от клетки"}, "SUMIF": {"a": "(диапазон; критерий; [сум_диапазон])", "d": "Сумира клетките, зададени с определено условие или критерий"}, "SUMIFS": {"a": "(сум_диапазон; критерий_диапазон; критерий; ...)", "d": "Сумира клетките, които удовлетворяват даден набор от условия или критерии"}, "SUMPRODUCT": {"a": "(масив1; [масив2]; [масив3]; ...)", "d": "Връща сумата на произведенията от съответните диапазони или масиви"}, "SUMSQ": {"a": "(число1; [число2]; ...)", "d": "Връща сумата на квадратите на аргументите. Аргументите могат да бъдат числа, имена на масиви или препратки към клетки, съдържащи числа"}, "SUMX2MY2": {"a": "(масив_x; масив_y)", "d": "Сумира разликите между квадратите на два съответстващи си диапазона или масива"}, "SUMX2PY2": {"a": "(масив_x; масив_y)", "d": " Връща общата сума на квадратите на числата в два съответстващи си диапазона или масива"}, "SUMXMY2": {"a": "(масив_x; масив_y)", "d": "Сумира квадратите на разликите в два съответстващи си диапазона или масиви"}, "TAN": {"a": "(число)", "d": "Връща тангенса от ъгъл"}, "TANH": {"a": "(число)", "d": "Връща хиперболичния тангенс от число"}, "TRUNC": {"a": "(число; [брой_цифри])", "d": "Отрязва число до цяло число, като премахва десетичната, или дробната, част от числото"}, "ADDRESS": {"a": "(ред_ном; колона_ном; [абс_ном]; [a1]; [лист_текст])", "d": "Създава препратка към клетка във вид на текст при зададени номера на ред и колона"}, "CHOOSE": {"a": "(индекс_числ; стойност1; [стойност2]; ...)", "d": "Избира стойност или действие за изпълнение от списък стойности на базата на индекс"}, "COLUMN": {"a": "([препратка])", "d": "Връща номера на колоната на препратка"}, "COLUMNS": {"a": "(масив)", "d": "Връща броя на колоните в масив или препратка"}, "FORMULATEXT": {"a": "(препратка)", "d": "Връща формула като низ"}, "HLOOKUP": {"a": "(справочна_стойност; масив_таблица; рез_индекс_номер; [диапазон_справка])", "d": "Търси стойност в горния ред на таблица или масив от стойности и връща стойността в същата колона на реда, който сте задали"}, "HYPERLINK": {"a": "(връзка_местоположение; [истинско_име])", "d": "Създава пряк път или преход за отваряне на документ, записан на вашия твърд диск, мрежов сървър или в интернет"}, "INDEX": {"a": "(масив; ред_ном; [колона_ном]!препратка; ред_ном; [колона_ном]; [област_ном])", "d": "Връща стойност или препратка към клетката в пресечната точка на зададени ред и колона, в зададен диапазон"}, "INDIRECT": {"a": "(препр_текст; [a1])", "d": "Връща препратка, зададена с текстов низ"}, "LOOKUP": {"a": "(справка_стойност; справка_вектор; [резултат_вектор]!справка_стойност; масив)", "d": "Търси стойност или от едноредов, или от едноколонен диапазон, или от масив. Предоставя се за обратна съвместимост"}, "MATCH": {"a": "(справка_стойност; справка_масив; [съвпадение_тип])", "d": "Връща относителното положение на елемент в масив, което отговаря на зададена стойност при зададена подредба"}, "OFFSET": {"a": "(препратка; редове; колони; [височина]; [ширина])", "d": "Връща препратка към диапазон, за който са зададени броя на редовете и колоните спрямо дадена препратка"}, "ROW": {"a": "([препратка])", "d": "Връща номера на реда на препратка"}, "ROWS": {"a": "(масив)", "d": "Връща броя на редовете в препратка или масив"}, "TRANSPOSE": {"a": "(масив)", "d": "Преобразува вертикален диапазон от клетки в хоризонтален и обратно"}, "UNIQUE": {"a": "(масив; [по_кол]; [точно_веднъж])", "d": "Връща уникалните стойности от диапазон или масив."}, "VLOOKUP": {"a": "(справка_стойност; масив_таблица; кол_индекс_ном; [диапазон_справка])", "d": "Търси стойност в най-лявата колона на таблица и връща стойност в същия ред на колона, която зададете. По подразбиране таблицата трябва да бъде сортирана в нарастващ ред"}, "XLOOKUP": {"a": "(стойност_справка; масив_справка; масив_връщане; [ако_не_е_намерено]; [режим_съвпадение]; [режим_търсене])", "d": "Търси в диапазон или масив и връща съответния елемент от втори диапазон или масив. По подразбиране се използва точно съвпадение"}, "CELL": {"a": "(инфо_тип; [препратка])", "d": "Връща информация за форматирането, местоположението или съдържанието на клетка"}, "ERROR.TYPE": {"a": "(грешка_стойност)", "d": "Връща число, отговарящо на грешна стойност."}, "ISBLANK": {"a": "(стойност)", "d": "Проверява дали една препратка е към празна клетка и връща TRUE или FALSE"}, "ISERR": {"a": "(стойност)", "d": "Проверява дали една стойност е грешка, различна от #N/A, и връща TRUE или FALSE"}, "ISERROR": {"a": "(стойност)", "d": "Проверява дали една стойност е грешка и връща TRUE или FALSE"}, "ISEVEN": {"a": "(число)", "d": "Връща значение TRUE, ако числото е четно"}, "ISFORMULA": {"a": "(препратка)", "d": "Проверява дали дадена препратка е към клетка, която съдържа формула, и връща ИСТИНА или НЕИСТИНА"}, "ISLOGICAL": {"a": "(стойност)", "d": "Проверява дали една стойност е логическа стойност (TRUE или FALSE) и връща TRUE или FALSE"}, "ISNA": {"a": "(стойност)", "d": "Проверява дали една стойност е #N/A и връща TRUE или FALSE"}, "ISNONTEXT": {"a": "(стойност)", "d": "Проверява дали една стойност не е текст (празните клетки не са текст) и връща TRUE или FALSE"}, "ISNUMBER": {"a": "(стойност)", "d": "Проверява дали една стойност е число и връща TRUE или FALSE"}, "ISODD": {"a": "(число)", "d": "Връща значение TRUE, ако числото е нечетно"}, "ISREF": {"a": "(стойност)", "d": "Проверява дали една стойност е препратка и връща TRUE или FALSE"}, "ISTEXT": {"a": "(стойност)", "d": "Проверява дали една стойност е текст и връща TRUE или FALSE"}, "N": {"a": "(стойност)", "d": "Преобразува нечислова стойност в число, дати - в поредни номера, TRUE - в 1, а всичко друго - в 0 (нула)"}, "NA": {"a": "()", "d": "Връща грешната стойност #N/A (липсва стойност)"}, "SHEET": {"a": "([стойност])", "d": "Връща номера на листа за посочения лист"}, "SHEETS": {"a": "([препратка])", "d": "Връща броя на листовете в препратка"}, "TYPE": {"a": "(стойност)", "d": "Връща цяло число, представляващо типа данни за стойност: число = 1; текст = 2; логическа стойност = 4; грешна стойност = 16; масив = 64; сложни данни = 128"}, "AND": {"a": "(логически1; [логически2]; ...)", "d": "Проверява дали всички аргументи са TRUE и връща TRUE, ако всички аргументи са TRUE"}, "FALSE": {"a": "()", "d": "Връща логическата стойност FALSE"}, "IF": {"a": "(логически_тест; [стойност_ако_вярно]; [стойност_ако_невярно])", "d": "Проверява дали е спазено зададено условие и връща една стойност при TRUE и друга стойност при FALSE"}, "IFS": {"a": "(логически_тест; стойност_ако_вярно; ...)", "d": "Проверява дали са изпълнени едно или повече условия и връща стойност, съответстваща на първото вярно условие"}, "IFERROR": {"a": "(стойност; стойност_ако_грешка)", "d": "Връща \"стойност_ако_грешка\", ако изразът е грешен, а в противен случай - самата стойност на израза"}, "IFNA": {"a": "(стойност; стойност_ако_няма)", "d": "Връща стойността, която задавате, ако изразът се свежда до #N/A, в противен случай връща израза"}, "NOT": {"a": "(логически)", "d": "Променя FALSE в TRUE или TRUE във FALSE"}, "OR": {"a": "(логически1; [логически2]; ...)", "d": "Проверява дали някой от аргументите е TRUE и връща TRUE или FALSE. FALSE се връща само ако всички аргументи са FALSE"}, "SWITCH": {"a": "(израз; стойност1; резултат1; [по_подразбиране_или_стойност2]; [резултат2]; ...)", "d": "Оценява израз спрямо списък от стойности и връща резултата, съответстващ на първата съвпадаща стойност. Ако няма съвпадение, се връща незадължителна стойност по избор"}, "TRUE": {"a": "()", "d": "Връща логическата стойност TRUE"}, "XOR": {"a": "(логическо1; [логическо2]; ...)", "d": "Връща логическо \"Изключващо или\" на всички аргументи"}, "TEXTBEFORE": {"a": "(текст, разделител, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Връща текст, който е преди разделяне на знаци."}, "TEXTAFTER": {"a": "(текст, разделител, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Връща текст, който е след разделяне на знаци."}, "TEXTSPLIT": {"a": "(текст, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": " Разделя текста на редове или колони с помощта на разделители."}, "WRAPROWS": {"a": "(вектор, wrap_count, [pad_with])", "d": " Пренася вектор на ред или колона след указан брой стойности."}, "VSTACK": {"a": "(масив1, [масив2], ...)", "d": " Вертикално наслагва масиви в един масив."}, "HSTACK": {"a": "(масив1, [масив2], ...)", "d": " Хоризонтално наслагва масиви в един масив."}, "CHOOSEROWS": {"a": "(ма<PERSON>и<PERSON>, row_num1, [row_num2], ...)", "d": " Връща редове от масив или препратка."}, "CHOOSECOLS": {"a": "(ма<PERSON><PERSON><PERSON>, col_num1, [col_num2], ...)", "d": " Връща колони от масив или препратка."}, "TOCOL": {"a": "(масив, [игнорирай], [scan_by_column])", "d": " Връща масива като една колона."}, "TOROW": {"a": "(масив, [игнор<PERSON><PERSON><PERSON><PERSON>], [сканиране_по_колона])", "d": " Връща масива като един ред."}, "WRAPCOLS": {"a": "(вектор, wrap_count, [pad_with])", "d": " Пренася вектор на ред или колона след указан брой стойности."}, "TAKE": {"a": "(масив, редове, [колони])", "d": " Връща редове или колони от началото или края на масива."}, "DROP": {"a": "(масив, редове, [колони])", "d": " Пада редове или колони от началото или края на масива."}}