{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Inserir sua mensagem aqui", "Common.Controllers.History.notcriticalErrorTitle": "Aviso", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> empi<PERSON>", "Common.define.chartData.textAreaStackedPer": "100% <PERSON><PERSON> al<PERSON>a", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "Colunas agrupadas", "Common.define.chartData.textBarNormal3d": "3-D Coluna agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Coluna 3-D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON> al<PERSON>hada", "Common.define.chartData.textBarStacked3d": "Coluna empilhada 3-D", "Common.define.chartData.textBarStackedPer": "100% <PERSON>una alinhada", "Common.define.chartData.textBarStackedPer3d": "3-D 100% <PERSON><PERSON> alinhada", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Coluna", "Common.define.chartData.textColumnSpark": "Coluna", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> empilhada - coluna agrupada", "Common.define.chartData.textComboBarLine": "Coluna agrupada - linha", "Common.define.chartData.textComboBarLineSecondary": "Coluna agrupada - linha no eixo secundário", "Common.define.chartData.textComboCustom": "Combinação personalizada", "Common.define.chartData.textDoughnut": "Rosquin<PERSON>", "Common.define.chartData.textHBarNormal": "Barras agrupadas", "Common.define.chartData.textHBarNormal3d": "3-D Barra agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON>", "Common.define.chartData.textHBarStacked3d": "Barra empilhada 3-D", "Common.define.chartData.textHBarStackedPer": "100% Barra alinhada", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% <PERSON><PERSON> al<PERSON>a", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Linha 3-D", "Common.define.chartData.textLineMarker": "Linha com marcadores", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStackedMarker": "Linha empilhada com marcadores", "Common.define.chartData.textLineStackedPer": "100% Ali<PERSON><PERSON>", "Common.define.chartData.textLineStackedPerMarker": "Alinhado com 100%", "Common.define.chartData.textPie": "Gráfico de pizza", "Common.define.chartData.textPie3d": "Torta 3-D", "Common.define.chartData.textPoint": "Gráfico de pontos", "Common.define.chartData.textScatter": "Di<PERSON>são", "Common.define.chartData.textScatterLine": "Dispersão com linhas retas", "Common.define.chartData.textScatterLineMarker": "Dispersão com linhas retas e marcadores", "Common.define.chartData.textScatterSmooth": "Dispersão com linhas suaves", "Common.define.chartData.textScatterSmoothMarker": "Dispersão com linhas suaves e marcadores", "Common.define.chartData.textSparks": "Minigráficos", "Common.define.chartData.textStock": "Gráfico de ações", "Common.define.chartData.textSurface": "Superfície", "Common.define.chartData.textWinLossSpark": "Ganhos/Perdas", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Nenhum conjunto de formatos", "Common.define.conditionalData.text1Above": "1 desvio padrão acima", "Common.define.conditionalData.text1Below": "1 desvio padrão abaixo", "Common.define.conditionalData.text2Above": "2 desvios pad<PERSON><PERSON> aci<PERSON>", "Common.define.conditionalData.text2Below": "2 desvios padrão abaixo", "Common.define.conditionalData.text3Above": "3 desvios pad<PERSON><PERSON> aci<PERSON>", "Common.define.conditionalData.text3Below": "3 desvios padrão abaixo", "Common.define.conditionalData.textAbove": "Acima", "Common.define.conditionalData.textAverage": "Média", "Common.define.conditionalData.textBegins": "Começa com", "Common.define.conditionalData.textBelow": "Abaixo", "Common.define.conditionalData.textBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Branco", "Common.define.conditionalData.textBlanks": "Contém espaços em branco", "Common.define.conditionalData.textBottom": "Inferior", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Barra de dados", "Common.define.conditionalData.textDate": "Data", "Common.define.conditionalData.textDuplicate": "Dup<PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Termina com", "Common.define.conditionalData.textEqAbove": "Igual ou superior a", "Common.define.conditionalData.textEqBelow": "Igual ou inferior a", "Common.define.conditionalData.textEqual": "Igual a", "Common.define.conditionalData.textError": "Erro", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON> erros", "Common.define.conditionalData.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreater": "Superior a", "Common.define.conditionalData.textGreaterEq": "Superior a ou igual a", "Common.define.conditionalData.textIconSets": "Conjuntos de ícones", "Common.define.conditionalData.textLast7days": "Nos últimos 7 dias", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON><PERSON> passado", "Common.define.conditionalData.textLastWeek": "Semana passada", "Common.define.conditionalData.textLess": "Inferior a", "Common.define.conditionalData.textLessEq": "Inferior a ou igual a", "Common.define.conditionalData.textNextMonth": "<PERSON>r<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNextWeek": "Próxima semana", "Common.define.conditionalData.textNotBetween": "Não entre...", "Common.define.conditionalData.textNotBlanks": "Não contém espaços em branco", "Common.define.conditionalData.textNotContains": "Não contém", "Common.define.conditionalData.textNotEqual": "Não igual a", "Common.define.conditionalData.textNotErrors": "Não contém erros", "Common.define.conditionalData.textText": "Тexto", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON> mês", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> semana", "Common.define.conditionalData.textToday": "Hoje", "Common.define.conditionalData.textTomorrow": "Amanhã", "Common.define.conditionalData.textTop": "Superior", "Common.define.conditionalData.textUnique": "Único", "Common.define.conditionalData.textValue": "O valor é", "Common.define.conditionalData.textYesterday": "Ontem", "Common.define.smartArt.textAccentedPicture": "Imagem com Ênfase", "Common.define.smartArt.textAccentProcess": "Processo em Destaque", "Common.define.smartArt.textAlternatingFlow": "Fluxo alternado", "Common.define.smartArt.textAlternatingHexagons": "Hexágonos alternados", "Common.define.smartArt.textAlternatingPictureBlocks": "Blocos de imagem alternados", "Common.define.smartArt.textAlternatingPictureCircles": "Círculos de imagens alternadas", "Common.define.smartArt.textArchitectureLayout": "Layout de arquitetura", "Common.define.smartArt.textArrowRibbon": "Seta em Forma de Fita", "Common.define.smartArt.textAscendingPictureAccentProcess": "Processo de acentuação da imagem ascendente", "Common.define.smartArt.textBalance": "<PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Processo Básico de Dobragem", "Common.define.smartArt.textBasicBlockList": "Lista básica de blocos", "Common.define.smartArt.textBasicChevronProcess": "Processo Básico em Divisas", "Common.define.smartArt.textBasicCycle": "Ciclo Básico", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Torta Básica", "Common.define.smartArt.textBasicProcess": "Processo Básico", "Common.define.smartArt.textBasicPyramid": "Pirâmide Básica", "Common.define.smartArt.textBasicRadial": "Radial Básico", "Common.define.smartArt.textBasicTarget": "Alvo Básico", "Common.define.smartArt.textBasicTimeline": "Linha do tempo básica", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n b<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Lista de Acentos de Imagem Dobrada", "Common.define.smartArt.textBendingPictureBlocks": "<PERSON><PERSON><PERSON> Imagem", "Common.define.smartArt.textBendingPictureCaption": "<PERSON><PERSON><PERSON> a legenda da imagem", "Common.define.smartArt.textBendingPictureCaptionList": "Lista de legendas de imagens dobradas", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Dobrando o Texto Semitransparente da Imagem", "Common.define.smartArt.textBlockCycle": "Ciclo de b<PERSON>o", "Common.define.smartArt.textBubblePictureList": "Lista de imagens de bolhas", "Common.define.smartArt.textCaptionedPictures": "Imagens legendadas", "Common.define.smartArt.textChevronAccentProcess": "Processo de Ênfase em Divisas", "Common.define.smartArt.textChevronList": "Lista de Divisas", "Common.define.smartArt.textCircleAccentTimeline": "Linha do tempo de destaque do círculo", "Common.define.smartArt.textCircleArrowProcess": "Processo de seta circular", "Common.define.smartArt.textCirclePictureHierarchy": "Hierarquia de imagem do círculo", "Common.define.smartArt.textCircleProcess": "<PERSON><PERSON>", "Common.define.smartArt.textCircleRelationship": "Relacionamento do Círculo", "Common.define.smartArt.textCircularBendingProcess": "Processo de dobra circular", "Common.define.smartArt.textCircularPictureCallout": "Texto explicativo de imagem circular", "Common.define.smartArt.textClosedChevronProcess": "Processo Fechado em Divisas", "Common.define.smartArt.textContinuousArrowProcess": "Processo de Seta Contínua", "Common.define.smartArt.textContinuousBlockProcess": "Processo de Bloco Contínuo", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textContinuousPictureList": "Lista de Imagens Contínua", "Common.define.smartArt.textConvergingArrows": "Setas convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergente", "Common.define.smartArt.textConvergingText": "Texto convergente", "Common.define.smartArt.textCounterbalanceArrows": "Setas de contrapeso", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Lista de Bloqueios Descendentes", "Common.define.smartArt.textDescendingProcess": "Processo descendente", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDivergingArrows": "Flechas divergentes", "Common.define.smartArt.textDivergingRadial": "Radial divergente", "Common.define.smartArt.textEquation": "Equação", "Common.define.smartArt.textFramedTextPicture": "Imagem de texto emoldurada", "Common.define.smartArt.textFunnel": "Funil", "Common.define.smartArt.textGear": "Engrenagem", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON> de <PERSON>", "Common.define.smartArt.textGroupedList": "Lista Agrupada", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organograma de meio círculo", "Common.define.smartArt.textHexagonCluster": "Conjunto Hexagonal", "Common.define.smartArt.textHexagonRadial": "Radial <PERSON>", "Common.define.smartArt.textHierarchy": "Hierarquia", "Common.define.smartArt.textHierarchyList": "Lista de hierarquia", "Common.define.smartArt.textHorizontalBulletList": "Lista de marcadores horizontais", "Common.define.smartArt.textHorizontalHierarchy": "Hierarquia Horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hierarquia Horizontal Rotulada", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hierarquia horizontal multinível", "Common.define.smartArt.textHorizontalOrganizationChart": "Organograma Horizontal", "Common.define.smartArt.textHorizontalPictureList": "Lista de imagens horizontais", "Common.define.smartArt.textIncreasingArrowProcess": "Processo de seta crescente", "Common.define.smartArt.textIncreasingCircleProcess": "Aumentando o Processo do Círculo", "Common.define.smartArt.textInterconnectedBlockProcess": "Processo de bloco interconectado", "Common.define.smartArt.textInterconnectedRings": "<PERSON><PERSON><PERSON>ec<PERSON>", "Common.define.smartArt.textInvertedPyramid": "Pirâmide invertida", "Common.define.smartArt.textLabeledHierarchy": "Hierarquia rotulada", "Common.define.smartArt.textLinearVenn": "Venn Linear", "Common.define.smartArt.textLinedList": "<PERSON>a alinhada", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclo multidirecional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organograma de Nome e Título", "Common.define.smartArt.textNestedTarget": "<PERSON><PERSON>", "Common.define.smartArt.textNondirectionalCycle": "Ciclo Não Direcional", "Common.define.smartArt.textOpposingArrows": "Setas Opostas", "Common.define.smartArt.textOpposingIdeas": "Ideias opost<PERSON>", "Common.define.smartArt.textOrganizationChart": "Organograma", "Common.define.smartArt.textOther": "Outro", "Common.define.smartArt.textPhasedProcess": "Processo em fases", "Common.define.smartArt.textPicture": "Imagem", "Common.define.smartArt.textPictureAccentBlocks": "Blocos de destaque de imagem", "Common.define.smartArt.textPictureAccentList": "Lista de destaques da imagem", "Common.define.smartArt.textPictureAccentProcess": "Processo de destaque da imagem", "Common.define.smartArt.textPictureCaptionList": "Lista de legendas de imagens", "Common.define.smartArt.textPictureFrame": "Porta-retrato", "Common.define.smartArt.textPictureGrid": "Grade de imagens", "Common.define.smartArt.textPictureLineup": "Alinhamento de imagens", "Common.define.smartArt.textPictureOrganizationChart": "Organograma de imagens", "Common.define.smartArt.textPictureStrips": "Tiras de imagem", "Common.define.smartArt.textPieProcess": "Processo em Pizza", "Common.define.smartArt.textPlusAndMinus": "Mais e menos", "Common.define.smartArt.textProcess": "Processo", "Common.define.smartArt.textProcessArrows": "Setas de processo", "Common.define.smartArt.textProcessList": "Lista de processos", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Lista de pirâmides", "Common.define.smartArt.textRadialCluster": "Aglomerado Radial", "Common.define.smartArt.textRadialCycle": "Ciclo radial", "Common.define.smartArt.textRadialList": "Lista radial", "Common.define.smartArt.textRadialPictureList": "Lista de imagens radiais", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Processo aleatório para resultado", "Common.define.smartArt.textRelationship": "Relação", "Common.define.smartArt.textRepeatingBendingProcess": "Repetindo o processo de dobra", "Common.define.smartArt.textReverseList": "Lista reversa", "Common.define.smartArt.textSegmentedCycle": "Ciclo Segmentado", "Common.define.smartArt.textSegmentedProcess": "Processo segmentado", "Common.define.smartArt.textSegmentedPyramid": "Pirâmide segmentada", "Common.define.smartArt.textSnapshotPictureList": "Lista de fotos instantâneas", "Common.define.smartArt.textSpiralPicture": "Imagem em espiral", "Common.define.smartArt.textSquareAccentList": "Lista de Acentos Quadrados", "Common.define.smartArt.textStackedList": "Lista empilhada", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "Processo escalonado", "Common.define.smartArt.textStepDownProcess": "Processo de redução", "Common.define.smartArt.textStepUpProcess": "Processo de intensificação", "Common.define.smartArt.textSubStepProcess": "Processo de subetapas", "Common.define.smartArt.textTabbedArc": "Arco com abas", "Common.define.smartArt.textTableHierarchy": "Hierar<PERSON><PERSON> da Tabela", "Common.define.smartArt.textTableList": "Lista de Tabelas", "Common.define.smartArt.textTabList": "Lista de guias", "Common.define.smartArt.textTargetList": "Lista de alvos", "Common.define.smartArt.textTextCycle": "Ciclo de texto", "Common.define.smartArt.textThemePictureAccent": "Destaque da Imagem do Tema", "Common.define.smartArt.textThemePictureAlternatingAccent": "Acento Alternado da Imagem do Tema", "Common.define.smartArt.textThemePictureGrid": "Grade de imagens do tema", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Lista de Acentos de Imagem Intitulada", "Common.define.smartArt.textTitledPictureBlocks": "Blocos de imagens intitulados", "Common.define.smartArt.textTitlePictureLineup": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textTrapezoidList": "Lista de trapézios", "Common.define.smartArt.textUpwardArrow": "Seta para cima", "Common.define.smartArt.textVaryingWidthList": "Lista de largura variável", "Common.define.smartArt.textVerticalAccentList": "Lista de acentos verticais", "Common.define.smartArt.textVerticalArrowList": "Lista de setas verticais", "Common.define.smartArt.textVerticalBendingProcess": "Processo de dobra vertical", "Common.define.smartArt.textVerticalBlockList": "Lista de Bloqueios Verticais", "Common.define.smartArt.textVerticalBoxList": "Lista de caixas verticais", "Common.define.smartArt.textVerticalBracketList": "Lista de colchetes verticais", "Common.define.smartArt.textVerticalBulletList": "Lista de marcadores verticais", "Common.define.smartArt.textVerticalChevronList": "Lista Vertical em Divisas", "Common.define.smartArt.textVerticalCircleList": "Lista de círculos verticais", "Common.define.smartArt.textVerticalCurvedList": "Lista Curva Vertical", "Common.define.smartArt.textVerticalEquation": "Equação Vertical", "Common.define.smartArt.textVerticalPictureAccentList": "Lista Vertical de Acentos de Imagem", "Common.define.smartArt.textVerticalPictureList": "Lista de imagens verticais", "Common.define.smartArt.textVerticalProcess": "Processo Vertical", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "O documento está bloqueado para edição. Você pode fazer alterações e salvá-lo como cópia local mais tarde.", "Common.Translation.tipFileReadOnly": "O documento é somente leitura e está bloqueado para edição. Você pode fazer alterações e salvar sua cópia local posteriormente.", "Common.Translation.warnFileLocked": "Documento está em uso por outra aplicação. Você pode continuar editando e salvá-lo como uma cópia.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON> uma c<PERSON>pia", "Common.Translation.warnFileLockedBtnView": "Aberto para visualização", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Nova cor personalizada", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboDataView.emptyComboText": "Sem estilos", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Atual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor inserido está incorreto.<br>Insira um valor entre 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor inserido está incorreto.<br>Insira um valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sem cor", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar palavra-chave", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON> senha", "Common.UI.SearchBar.textFind": "Localizar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.UI.SearchBar.tipNextResult": "Próximo resultado", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON> as configura<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Destacar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto de substituição", "Common.UI.SearchDialog.textSearchStart": "Inserir seu texto aqui", "Common.UI.SearchDialog.textTitle": "Localizar e Substituir", "Common.UI.SearchDialog.textTitle2": "Localizar", "Common.UI.SearchDialog.textWholeWords": "Palavras inteiras apenas", "Common.UI.SearchDialog.txtBtnHideReplace": "Ocultar Substituição", "Common.UI.SearchDialog.txtBtnReplace": "Substituir", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituir tudo", "Common.UI.SynchronizeTip.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.SynchronizeTip.textSynchronize": "O documento foi alterado por outro usuário.<br>Clique para salvar suas alterações e recarregar as atualizações.", "Common.UI.ThemeColorPalette.textRecentColors": "Cores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON> padron<PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores de tema", "Common.UI.Themes.txtThemeClassicLight": "Clássico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste escuro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "O mesmo que sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Não", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmação", "Common.UI.Window.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Informações", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "<PERSON>m", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "endereço:", "Common.Views.About.txtLicensee": "LICENÇA", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Aplicar enquanto você trabalha", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorreção", "Common.Views.AutoCorrectDialog.textAutoFormat": "Auto Formatação conforme você digita", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Excluir", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e caminhos de rede com hyperlinks", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorreção matemática", "Common.Views.AutoCorrectDialog.textNewRowCol": "Incluir novas linhas e colunas na tabela", "Common.Views.AutoCorrectDialog.textRecognized": "Funções Reconhecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expressões são expressões matemáticas reconhecidas. Eles não ficarão em itálico automaticamente.", "Common.Views.AutoCorrectDialog.textReplace": "Substituir", "Common.Views.AutoCorrectDialog.textReplaceText": "Substituir ao Digitar", "Common.Views.AutoCorrectDialog.textReplaceType": "Substitua o texto enquanto você digita", "Common.Views.AutoCorrectDialog.textReset": "Redefinir", "Common.Views.AutoCorrectDialog.textResetAll": "Voltar para predefinições", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorreção", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funções reconhecidas devem conter apenas as letras de A a Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Qualquer expressão que tenha acrescentado será removida e as expressões removidas serão restauradas. Quer continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A entrada de correção automática para %1 já existe. Quer substituir?", "Common.Views.AutoCorrectDialog.warnReset": "Qualquer autocorrecção que tenha adicionado será removida e as alterações serão restauradas aos seus valores originais. Quer continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A entrada de autocorreção para %1 será redefinida para seu valor original. Você quer continuar?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> anti<PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "De cima", "Common.Views.Comments.mniPositionDesc": "Do fundo", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Adicionar comentário ao documento", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.Comments.textAll": "<PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textComments": "Comentários", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Inserir seu coment<PERSON>rio aqui", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolvido", "Common.Views.Comments.textSort": "Ordenar comentá<PERSON>s", "Common.Views.Comments.textViewResolved": "Não tem permissão para reabrir comentários", "Common.Views.Comments.txtEmpty": "Não há comentários na planilha.", "Common.Views.CopyWarningDialog.textDontShow": "Não exibir esta mensagem novamente", "Common.Views.CopyWarningDialog.textMsg": "As ações copiar, cortar e colar usando os botões da barra de ferramentas do editor e as ações de menu de contexto serão realizadas apenas nesta aba do editor.<br><br>Para copiar ou colar para ou de aplicativos externos a aba do editor, use as seguintes combinações do teclado:", "Common.Views.CopyWarningDialog.textTitle": "Copiar, Cortar e Colar", "Common.Views.CopyWarningDialog.textToCopy": "para Copiar", "Common.Views.CopyWarningDialog.textToCut": "para Cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Colar", "Common.Views.DocumentAccessDialog.textLoading": "Carregando ...", "Common.Views.DocumentAccessDialog.textTitle": "Configurações de compartilhamento", "Common.Views.EditNameDialog.textLabel": "Etiqueta:", "Common.Views.EditNameDialog.textLabelError": "O rótulo não deve estar vazio.", "Common.Views.Header.labelCoUsersDescr": "Usuários que estão editando o arquivo:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Configurações avançadas", "Common.Views.Header.textBack": "Local do arquivo aberto", "Common.Views.Header.textCompactView": "Ocultar barra de ferramentas", "Common.Views.Header.textHideLines": "Ocultar r<PERSON>", "Common.Views.Header.textHideStatusBar": "Ocultar barra de status", "Common.Views.Header.textReadOnly": "<PERSON>nte leitura", "Common.Views.Header.textRemoveFavorite": "Remover dos Favoritos", "Common.Views.Header.textSaveBegin": "Salvando...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON> as alteraç<PERSON>es foram salvas", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON> as alteraç<PERSON>es foram salvas", "Common.Views.Header.textShare": "Compartilhar", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Gerenciar direitos de acesso ao documento", "Common.Views.Header.tipDownload": "Transferir arquivo", "Common.Views.Header.tipGoEdit": "Editar arquivo atual", "Common.Views.Header.tipPrint": "Imprimir arquivo", "Common.Views.Header.tipPrintQuick": "Impressão rápida", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON>", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Desencaixe em janela separada", "Common.Views.Header.tipUsers": "Ver usuários", "Common.Views.Header.tipViewSettings": "Configurações de exibição", "Common.Views.Header.tipViewUsers": "Ver usuários e gerenciar direitos de acesso ao documento", "Common.Views.Header.txtAccessRights": "Alterar direitos de acesso", "Common.Views.Header.txtRename": "Renomear", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar alterações detalhadas ", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Mostrar alterações detalhadas", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Colar uma URL de imagem:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Marcadores", "Common.Views.ListSettingsDialog.textFromFile": "Do Arquivo", "Common.Views.ListSettingsDialog.textFromStorage": "De armazenamento", "Common.Views.ListSettingsDialog.textFromUrl": "Da URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.textSelect": "Selecione de", "Common.Views.ListSettingsDialog.tipChange": "Alterar marcador", "Common.Views.ListSettingsDialog.txtBullet": "Ponto", "Common.Views.ListSettingsDialog.txtColor": "Cor", "Common.Views.ListSettingsDialog.txtImage": "Imagem", "Common.Views.ListSettingsDialog.txtImport": "Importação", "Common.Views.ListSettingsDialog.txtNewBullet": "Novo marcador", "Common.Views.ListSettingsDialog.txtNewImage": "Nova imagem", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% do texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON> á<PERSON>", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Configurações da lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON> a<PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Intervalo de células inválido", "Common.Views.OpenDialog.textSelectData": "Selecionar dados", "Common.Views.OpenDialog.txtAdvanced": "Avançado", "Common.Views.OpenDialog.txtColon": "Cólon", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Delimiter", "Common.Views.OpenDialog.txtDestData": "Escolha onde colocar os dados", "Common.Views.OpenDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.OpenDialog.txtEncoding": "Encoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON> incorreta.", "Common.Views.OpenDialog.txtOpenFile": "Inserir a Senha para Abrir o Arquivo", "Common.Views.OpenDialog.txtOther": "Outro", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Pré-visualizar", "Common.Views.OpenDialog.txtProtected": "Depois de inserir a senha e abrir o arquivo, a senha atual do arquivo será redefinida.", "Common.Views.OpenDialog.txtSemicolon": "Ponto e vírgula", "Common.Views.OpenDialog.txtSpace": "Space", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Choose %1 options", "Common.Views.OpenDialog.txtTitleProtected": "Arquivo protegido", "Common.Views.PasswordDialog.txtDescription": "Defina uma senha para proteger este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "A senha de confirmação não é idêntica", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON> a senha", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON> se<PERSON>a", "Common.Views.PasswordDialog.txtWarning": "Cuidado: se você perder ou esquecer a senha, não será possível recuperá-la. Guarde-o em local seguro.", "Common.Views.PluginDlg.textLoading": "Carregamento", "Common.Views.Plugins.groupCaption": "Plug-ins", "Common.Views.Plugins.strPlugins": "Plug-ins", "Common.Views.Plugins.textClosePanel": "Fechar plug-in", "Common.Views.Plugins.textLoading": "Carregamento", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Criptografar com senha", "Common.Views.Protection.hintDelPwd": "Excluir senha", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> ou excluir senha", "Common.Views.Protection.hintSignature": "Adicionar assinatura digital ou linha de assinatura", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "Excluir senha", "Common.Views.Protection.txtEncrypt": "Criptografar", "Common.Views.Protection.txtInvisibleSignature": "Adicionar assinatura digital", "Common.Views.Protection.txtSignature": "Assinatura", "Common.Views.Protection.txtSignatureLine": "Adicionar linha de assinatura", "Common.Views.RenameDialog.textName": "Nome do arquivo", "Common.Views.RenameDialog.txtInvalidName": "Nome de arquivo não pode conter os seguintes caracteres:", "Common.Views.ReviewChanges.hintNext": "Para a próxima alteração", "Common.Views.ReviewChanges.hintPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Coedição em tempo real. Todas as alterações são salvas automaticamente.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Use o botão 'Salvar' para sincronizar as alterações que você e outras pessoas fazeram.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.tipCoAuthMode": "Definir o modo de co-edição", "Common.Views.ReviewChanges.tipCommentRem": "Remover comentários", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentários", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.tipHistory": "<PERSON>rar histó<PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeitar alteração atual", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Selecione o modo em que você deseja que as alterações sejam exibidas", "Common.Views.ReviewChanges.tipSetDocLang": "Definir idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.tipSharing": "Gerenciar direitos de acesso a documentos", "Common.Views.ReviewChanges.txtAccept": "Aceitar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceitar to<PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceitar alterações", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de coedição", "Common.Views.ReviewChanges.txtCommentRemAll": "Excluir Todos os Comentários", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Excluir comentários atuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Excluir meus comentários", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Remover meus comentários atuais", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentários", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver meus comentários", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver meus comentários atuais", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> as alteraç<PERSON>es aceitas (Visualização)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Histórico da versões", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> as alteraç<PERSON><PERSON> (Edição)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcação", "Common.Views.ReviewChanges.txtNext": "Próximo", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> as alteraç<PERSON><PERSON> rejeitadas (Visualização)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeitar alterações", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeitar alteração atual", "Common.Views.ReviewChanges.txtSharing": "Compartilhar", "Common.Views.ReviewChanges.txtSpelling": "Verificação Ortográfica", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Modo de exibição", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Insira seu coment<PERSON>rio aqui", "Common.Views.ReviewPopover.textMention": "+menção fornecerá acesso ao documento e enviará um e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+menção notificará o usuário por e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Abra novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Não tem permissão para reabrir comentários", "Common.Views.ReviewPopover.txtDeleteTip": "Excluir", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Carregando", "Common.Views.SaveAsDlg.textTitle": "Pasta para salvar", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON> colunas", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON> linhas", "Common.Views.SearchPanel.textCaseSensitive": "Diferenciar maiús<PERSON>s de minúsculas", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.Views.SearchPanel.textContentChanged": "Documento alterado.", "Common.Views.SearchPanel.textFind": "Localizar", "Common.Views.SearchPanel.textFindAndReplace": "Localizar e substituir", "Common.Views.SearchPanel.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textItemEntireCell": "Inser<PERSON> con<PERSON>lu<PERSON>", "Common.Views.SearchPanel.textLookIn": "<PERSON><PERSON><PERSON> <PERSON>", "Common.Views.SearchPanel.textMatchUsingRegExp": "Corresponder usando expressões regulares", "Common.Views.SearchPanel.textName": "Nome", "Common.Views.SearchPanel.textNoMatches": "Nenhuma correspondê<PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "Nenhum resultado de pesquisa", "Common.Views.SearchPanel.textReplace": "Substituir", "Common.Views.SearchPanel.textReplaceAll": "Substituir tudo", "Common.Views.SearchPanel.textReplaceWith": "Substituir com", "Common.Views.SearchPanel.textSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchAgain": "{0}Realize uma nova pesquisa{1} para obter resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "A pesquisa parou", "Common.Views.SearchPanel.textSearchOptions": "Opções de pesquisa", "Common.Views.SearchPanel.textSearchResults": "Resultados da pesquisa: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "Selecionar intervalo de dados", "Common.Views.SearchPanel.textSheet": "Fol<PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Faixa específica", "Common.Views.SearchPanel.textTooManyResults": "Há muitos resultados para mostrar aqui", "Common.Views.SearchPanel.textValue": "Valor", "Common.Views.SearchPanel.textValues": "Valores", "Common.Views.SearchPanel.textWholeWords": "Palavras inteiras apenas", "Common.Views.SearchPanel.textWithin": "<PERSON><PERSON>", "Common.Views.SearchPanel.textWorkbook": "Pasta de trabalho", "Common.Views.SearchPanel.tipNextResult": "Próximo resultado", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "Carregando", "Common.Views.SelectFileDlg.textTitle": "Selecionar fonte de dados", "Common.Views.SignDialog.textBold": "Negrito", "Common.Views.SignDialog.textCertificate": "Certificado", "Common.Views.SignDialog.textChange": "Alterar", "Common.Views.SignDialog.textInputName": "Campo Nome do assinante", "Common.Views.SignDialog.textItalic": "Itálico", "Common.Views.SignDialog.textNameError": "Nome de assinante não deve estar vazio.", "Common.Views.SignDialog.textPurpose": "Objetivo de assinar este documento", "Common.Views.SignDialog.textSelect": "Selecionar", "Common.Views.SignDialog.textSelectImage": "Selecionar imagem", "Common.Views.SignDialog.textSignature": "A assinatura parece", "Common.Views.SignDialog.textTitle": "Assinar o Documento", "Common.Views.SignDialog.textUseImage": "ou clique em 'Selecionar imagem' para usar uma imagem como assinatura", "Common.Views.SignDialog.textValid": "Válido de %1 a %2", "Common.Views.SignDialog.tipFontName": "<PERSON>me da fonte", "Common.Views.SignDialog.tipFontSize": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir que o assinante adicione um comentário na caixa de diálogo de assinatura", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de assinar este documento, verifique se o conteúdo que está a assinar está correto.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "Título do assinante", "Common.Views.SignSettingsDialog.textInstructions": "Instruções para o assinante", "Common.Views.SignSettingsDialog.textShowDate": "Mostrar data de assinatura na linha de assinatura", "Common.Views.SignSettingsDialog.textTitle": "Configuração de assinatura", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.SymbolTableDialog.textCharacter": "Caractere", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Assinatura de copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON> duplas", "Common.Views.SymbolTableDialog.textDOQuote": "Abertura de aspas duplas", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Travessão", "Common.Views.SymbolTableDialog.textEmSpace": "Espaço", "Common.Views.SymbolTableDialog.textEnDash": "Travessão", "Common.Views.SymbolTableDialog.textEnSpace": "Espaço", "Common.Views.SymbolTableDialog.textFont": "Fonte", "Common.Views.SymbolTableDialog.textNBHyphen": "Hífen sem quebra", "Common.Views.SymbolTableDialog.textNBSpace": "Espaço sem interrupção", "Common.Views.SymbolTableDialog.textPilcrow": "Indicador de parágrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em espaço", "Common.Views.SymbolTableDialog.textRange": "Intervalo", "Common.Views.SymbolTableDialog.textRecent": "Símbolos usados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Símbolo de marca registrada", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON><PERSON> aspas simples", "Common.Views.SymbolTableDialog.textSection": "Sinal de seção", "Common.Views.SymbolTableDialog.textShortcut": "Tecla de atalho", "Common.Views.SymbolTableDialog.textSHyphen": "Hífen suave", "Common.Views.SymbolTableDialog.textSOQuote": "Abertura de aspas simples", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca registrada", "Common.Views.UserNameDialog.textDontShow": "Não perguntar novamente", "Common.Views.UserNameDialog.textLabel": "Rótulo:", "Common.Views.UserNameDialog.textLabelError": "Etiqueta não deve estar vazia.", "SSE.Controllers.DataTab.strSheet": "Fol<PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "O link para uma fonte externa foi adicionado. Você pode atualizar esses links na guia Dados.", "SSE.Controllers.DataTab.textColumns": "Colunas", "SSE.Controllers.DataTab.textDontUpdate": "Não atualize", "SSE.Controllers.DataTab.textEmptyUrl": "Você precisa especificar o URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textUpdate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Texto para colunas", "SSE.Controllers.DataTab.txtDataValidation": "Validação de dados", "SSE.Controllers.DataTab.txtErrorExternalLink": "Erro: falha na atualização", "SSE.Controllers.DataTab.txtExpand": "Expandir", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Os dados próximos à seleção não serão removidos. Deseja expandir a seleção para incluir os dados adjacentes ou continuar apenas com as células atualmente selecionadas?", "SSE.Controllers.DataTab.txtExtendDataValidation": "A seleção contém algumas células sem configurações de validação de dados.<br> Você deseja estender a validação de dados a essas células?", "SSE.Controllers.DataTab.txtImportWizard": "Assistente de importação de texto", "SSE.Controllers.DataTab.txtRemDuplicates": "Remover Duplicatas", "SSE.Controllers.DataTab.txtRemoveDataValidation": "A seleção contém mais de um tipo de validação.<br>Configurações de corrente de partida e continua?", "SSE.Controllers.DataTab.txtRemSelected": "Remover em selecionado", "SSE.Controllers.DataTab.txtUrlTitle": "<PERSON> um URL de dados", "SSE.Controllers.DataTab.warnUpdateExternalData": "Esta pasta de trabalho contém links para uma ou mais fontes externas que podem não ser seguras.<br>Se você confia nos links, atualize-os para obter os dados mais recentes.", "SSE.Controllers.DocumentHolder.alignmentText": "Alinhamento", "SSE.Controllers.DocumentHolder.centerText": "Centro", "SSE.Controllers.DocumentHolder.deleteColumnText": "Excluir coluna", "SSE.Controllers.DocumentHolder.deleteRowText": "Excluir linha", "SSE.Controllers.DocumentHolder.deleteText": "Excluir", "SSE.Controllers.DocumentHolder.errorInvalidLink": "A referência de link não existe. Corrija o link ou o exclua.", "SSE.Controllers.DocumentHolder.guestText": "Visitante", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Coluna direita", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "Inserir", "SSE.Controllers.DocumentHolder.leftText": "E<PERSON>rda", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Aviso", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opções de autocorreção", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON> da coluna {0} símbolos ({1} pixels)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Altura da linha {0} pontos ({1} pixels)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Pressione CTRL e clique no link", "SSE.Controllers.DocumentHolder.textInsertLeft": "Inserir à esquerda", "SSE.Controllers.DocumentHolder.textInsertTop": "Inserir a<PERSON>", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Colar especial", "SSE.Controllers.DocumentHolder.textStopExpand": "Pare de expandir tabelas automaticamente", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Este elemento está sendo editado por outro usuário.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON><PERSON> da média", "SSE.Controllers.DocumentHolder.txtAddBottom": "Adicionar borda inferior", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Adicionar barra de fração", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLB": "Adicionar linha inferior esquerda", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON> borda es<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "Adicionar linha superior esquerda", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>r borda direita", "SSE.Controllers.DocumentHolder.txtAddTop": "Adicionar borda superior", "SSE.Controllers.DocumentHolder.txtAddVer": "Adicionar lin<PERSON>", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> ao caractere", "SSE.Controllers.DocumentHolder.txtAll": "(Todos)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Retorna todo o conteúdo da tabela ou colunas especificadas da tabela, incluindo cabeçalhos de coluna, dados e linhas totais", "SSE.Controllers.DocumentHolder.txtAnd": "e", "SSE.Controllers.DocumentHolder.txtBegins": "Começa com", "SSE.Controllers.DocumentHolder.txtBelowAve": "<PERSON><PERSON><PERSON><PERSON> da média", "SSE.Controllers.DocumentHolder.txtBlanks": "(<PERSON> branco)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON>a", "SSE.Controllers.DocumentHolder.txtBottom": "Inferior", "SSE.Controllers.DocumentHolder.txtColumn": "Coluna", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Alinhamento de colunas", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copiado para a área de transferência", "SSE.Controllers.DocumentHolder.txtDataTableHint": "<PERSON><PERSON><PERSON> as c<PERSON><PERSON><PERSON> de dados da tabela ou colunas de tabela especificadas", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON> de <PERSON>o", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Excluir argumento", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Excluir quebra manual", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Excluir caracteres anexos ", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Excluir separadores e caracteres anexos", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Excluir equação", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Excluir caractere", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Excluir radical", "SSE.Controllers.DocumentHolder.txtEnds": "Termina com", "SSE.Controllers.DocumentHolder.txtEquals": "Igua<PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Igual à cor da célula", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Igual à cor da fonte", "SSE.Controllers.DocumentHolder.txtExpand": "Expandir e classificar", "SSE.Controllers.DocumentHolder.txtExpandSort": "Os dados próximos à seleção não serão classificados. Você quer expandir a seleção para incluir os dados adjacentes ou continuar com classificando apenas as células selecionadas atualmente?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Inferior", "SSE.Controllers.DocumentHolder.txtFilterTop": "Superior", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Alterar para fração linear", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Alterar para fração inclinada", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Alterar para fração empilhada", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON> que", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON> que ou igual a", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Caractere sobre texto", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Caractere sob texto", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Devolve os cabeçalhos das colunas para a tabela ou colunas de tabela especificadas", "SSE.Controllers.DocumentHolder.txtHeight": "Altura", "SSE.Controllers.DocumentHolder.txtHideBottom": "Ocultar borda inferior", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Ocultar limite inferior", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Ocultar colchete de fechamento", "SSE.Controllers.DocumentHolder.txtHideDegree": "Ocultar grau", "SSE.Controllers.DocumentHolder.txtHideHor": "Ocultar linha horizontal", "SSE.Controllers.DocumentHolder.txtHideLB": "Ocultar linha inferior esquerda", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON>r borda esquer<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "Ocultar linha superior esquerda", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Ocultar colchete de abertura", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Ocultar espaço reservado", "SSE.Controllers.DocumentHolder.txtHideRight": "O<PERSON>ltar borda direita", "SSE.Controllers.DocumentHolder.txtHideTop": "Ocultar borda superior", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Ocultar limite superior", "SSE.Controllers.DocumentHolder.txtHideVer": "Ocultar linha vertical", "SSE.Controllers.DocumentHolder.txtImportWizard": "Assistente de importação de texto", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Aumentar o tamanho do argumento", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Inserir argumento após", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Inserir quebra manual", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Inserir equação a seguir", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Inserir equação à frente", "SSE.Controllers.DocumentHolder.txtItems": "itens", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> apenas texto", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON> que", "SSE.Controllers.DocumentHolder.txtLessEquals": "Menos que ou igual a", "SSE.Controllers.DocumentHolder.txtLimitChange": "Alterar localização de limites", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limite sobre o texto", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limite sob o texto", "SSE.Controllers.DocumentHolder.txtLockSort": "Os dados são encontrados ao lado de sua seleção, mas você não tem permissão suficiente para alterar essas células.<br> Você deseja continuar com a seleção atual?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Combinar parênteses com a altura do argumento", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Alinhamento de matriz", "SSE.Controllers.DocumentHolder.txtNoChoices": "Não há escolhas para preenchimento de célula.<br>Apenas valores de texto da coluna podem ser selecionados para substituição.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Não começa com", "SSE.Controllers.DocumentHolder.txtNotContains": "Não contém", "SSE.Controllers.DocumentHolder.txtNotEnds": "Não termina com", "SSE.Controllers.DocumentHolder.txtNotEquals": "Não é igual a", "SSE.Controllers.DocumentHolder.txtOr": "ou", "SSE.Controllers.DocumentHolder.txtOverbar": "Barra sobre texto", "SSE.Controllers.DocumentHolder.txtPaste": "Colar", "SSE.Controllers.DocumentHolder.txtPasteBorders": "<PERSON><PERSON><PERSON><PERSON> sem bordas", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Fórmula + largura da coluna", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formatação de destino", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Colar apenas formatação", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Fórmula + formato do número", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Colar apenas fórmula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Fórmula + toda formatação", "SSE.Controllers.DocumentHolder.txtPasteLink": "Colar link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Imagem vinculada", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Mesclar formatação condicional", "SSE.Controllers.DocumentHolder.txtPastePicture": "Imagem", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formatação da fonte", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpor", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Valor + toda formatação", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Valor + formato do número", "SSE.Controllers.DocumentHolder.txtPasteValues": "Colar apenas valor", "SSE.Controllers.DocumentHolder.txtPercent": "por cento", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Refazer tabela de expansão automática", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Remover barra de fração", "SSE.Controllers.DocumentHolder.txtRemLimit": "Remover limite", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Remover caractere de acento", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Remover barra", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Você quer remover esta assinatura? <br><PERSON><PERSON> não pode ser desfeito.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Remover scripts", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Remover subscrito", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Remover sobrescrito", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scripts após o texto", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts antes do texto", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Mostrar limite inferior", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Mostrar colchetes de fechamento", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> gra<PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON> co<PERSON> de abertura", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Exibir espaço reservado", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Exibir limite superior", "SSE.Controllers.DocumentHolder.txtSorting": "Classificação", "SSE.Controllers.DocumentHolder.txtSortSelected": "Classificar selecionado", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Esticar colchetes", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Escolha apenas esta linha da coluna especificada", "SSE.Controllers.DocumentHolder.txtTop": "Parte superior", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Devolve o total de linhas para a tabela ou colunas de tabela especificadas", "SSE.Controllers.DocumentHolder.txtUnderbar": "Barra abaixo de texto", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Desfazer expansão automática da tabela", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Usar assistente de importação de texto", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Clicar neste link pode ser prejudicial ao seu dispositivo e dados.<br>Você tem certeza de que quer continuar?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "Todos", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Base de dados", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Data e hora", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Engenharia", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financeiro", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informação", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 usados pela última vez", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Lógico", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Pesquisa e Referência", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matemática e trigonometria", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Estatístico", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Texto e dados", "SSE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON>ha sem nome", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON> colunas", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON> linhas", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "Inser<PERSON> con<PERSON>lu<PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Carregando o histórico de versões...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON><PERSON> <PERSON>", "SSE.Controllers.LeftMenu.textNoTextFound": "Os dados que você tem estado procurando não podem ser encontrados. Ajuste suas opções de pesquisa.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "A pesquisa foi realizada. Ocorrências substituídas: {0}", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSheet": "Fol<PERSON>", "SSE.Controllers.LeftMenu.textValues": "Valores", "SSE.Controllers.LeftMenu.textWarning": "Aviso", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "Pasta de trabalho", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "SSE.Controllers.LeftMenu.warnDownloadAs": "Se você continuar salvando neste formato, todos os recursos exceto o texto serão perdidos.<br>Você tem certeza que quer continuar?", "SSE.Controllers.Main.confirmAddCellWatches": "Esta ação adicionará {0} células de observação.<br>Quer continuar?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Esta ação adicionará apenas {0} células de observação por motivo de poupança da memória.<br>Quer continuar?", "SSE.Controllers.Main.confirmMaxChangesSize": "O tamanho das ações excede a limitação definida para seu servidor.<br>Pressione \"Desfazer\" para cancelar sua última ação ou pressione \"Continue\" para manter a ação localmente (você precisa baixar o arquivo ou copiar seu conteúdo para garantir que nada seja perdido).", "SSE.Controllers.Main.confirmMoveCellRange": "O intervalo de célula de destino pode conter dados. Continuar a operação?", "SSE.Controllers.Main.confirmPutMergeRange": "Os dados fontes contêm células mescladas.<br><PERSON>as foram desmescladas antes de serem coladas na tabela.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "As fórmulas na linha do cabeçalho serão removidas e convertidas em texto estático. <br> <PERSON><PERSON><PERSON> continuar?", "SSE.Controllers.Main.convertationTimeoutText": "Tempo limite de conversão excedido.", "SSE.Controllers.Main.criticalErrorExtText": "Pressione \"OK\" para voltar para a lista de documentos.", "SSE.Controllers.Main.criticalErrorTitle": "Erro", "SSE.Controllers.Main.downloadErrorText": "Transferência falhou.", "SSE.Controllers.Main.downloadTextText": "Transferindo planilha...", "SSE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errNoDuplicates": "Nenhum valor duplicado encontrado.", "SSE.Controllers.Main.errorAccessDeny": "Você está tentando executar uma ação para a qual não tem direitos.<br>Entre em contato com o administrador do Document Server.", "SSE.Controllers.Main.errorArgsRange": "Um erro na fórmula inserida.<br>Intervalo de argumentos incorretos está sendo usado.", "SSE.Controllers.Main.errorAutoFilterChange": "A operação não é permitida, uma vez que ela está tentando deslocar células na tabela em sua planilha.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "A operação não pode ser feita para as c<PERSON><PERSON>las selecionadas, uma vez que você não pode mover uma parte da tabela.<br>Selecione outra faixa de dados de modo que toda a tabela seja deslocada e tente novamente.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Não foi possível concluir a operação para o intervalo de células selecionado.<br>Selecione um intervalo de dados uniforme interno ou externo à tabela e tente novamente.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "A operação não pode ser realizada por que a área contém células filtrads.<br>Torne visíveis os elementos fritados e tente novamente.", "SSE.Controllers.Main.errorBadImageUrl": "URL de imagem está incorreta", "SSE.Controllers.Main.errorCannotPasteImg": "Não podemos colar esta imagem da área de transferência, mas você pode salvá-la em seu dispositivo e\ninsira-o a partir daí ou copie a imagem sem texto e cole-a na planilha.", "SSE.Controllers.Main.errorCannotUngroup": "Não é possível desagrupar. Para iniciar um esboço, selecione as linhas ou colunas detalhadas e agrupe-as.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Você não pode usar este comando em uma planilha protegida. Para usar este comando, desproteja a planilha.<br>Você pode ser solicitado a inserir uma senha.", "SSE.Controllers.Main.errorChangeArray": "Você não pode alterar parte de uma matriz.", "SSE.Controllers.Main.errorChangeFilteredRange": "Isso mudará um intervalo filtrado em sua planilha. <br> Para concluir esta tarefa, remova os Filtros automáticos.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "A célula ou gráfico que você está tentando alterar está em uma página protegida. <br> Para fazer uma alteração, desproteja a página. Você pode ser solicitado a inserir uma senha.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Conexão com servidor perdida. O documento não pode ser editado neste momento.", "SSE.Controllers.Main.errorConnectToServer": "O documento não pode ser gravado. Verifique as configurações de conexão ou entre em contato com o administrador.<br>Quando você clicar no botão 'OK', você será solicitado a transferir o documento.", "SSE.Controllers.Main.errorConvertXml": "O arquivo tem um formato não suportado.<br>Apenas o formato de Planilha XML 2003 pode ser usado.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Este comando não pode ser usado com várias seleções.<br>Selecione um intervalo único e tente novamente.", "SSE.Controllers.Main.errorCountArg": "Um erro na fórmula inserida.<br>Número incorreto de argumentos está sendo usado.", "SSE.Controllers.Main.errorCountArgExceed": "Um erro na fórmula inserida.<br>Número de argumentos está excedido.", "SSE.Controllers.Main.errorCreateDefName": "Os intervalos nomeados existentes não podem ser editados e os novos não podem ser criados <br> no momento, pois alguns deles estão sendo editados.", "SSE.Controllers.Main.errorDatabaseConnection": "Erro externo.<br><PERSON><PERSON> de conexão ao banco de dados. Entre em contato com o suporte caso o erro persista.", "SSE.Controllers.Main.errorDataEncrypted": "Alterações criptografadas foram recebidas, não podem ser decifradas.", "SSE.Controllers.Main.errorDataRange": "Intervalo de dados incorreto.", "SSE.Controllers.Main.errorDataValidate": "O valor que você digitou não é válido.<br>Um usuário restringiu valores que podem ser inseridos nesta célula.", "SSE.Controllers.Main.errorDefaultMessage": "Código do erro: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Você está tentando excluir uma coluna que contém uma célula bloqueada. As células bloqueadas não podem ser excluídas enquanto a planilha está protegida. <br> Para excluir uma célula bloqueada, desproteja a planilha. Você pode ser solicitado a inserir uma senha.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Você está tentando apagar uma linha que contém uma célula trancada. As células bloqueadas não podem ser apagadas enquanto a folha de trabalho estiver protegida.<br>Para apagar uma célula bloqueada, desproteja a folha. Você poderá ser solicitado a digitar uma senha.", "SSE.Controllers.Main.errorDirectUrl": "Por favor, verifique o link para o documento.<br>Este link deve ser o link direto para baixar o arquivo.", "SSE.Controllers.Main.errorEditingDownloadas": "Ocorreu um erro. <br> Use a opção 'Transferir como' para gravar a cópia de backup em seu computador.", "SSE.Controllers.Main.errorEditingSaveas": "Ocorreu um erro durante o trabalho com o documento.<br>Use a opção 'Salvar como ...' para salvar a cópia de backup do arquivo no disco rígido do computador.", "SSE.Controllers.Main.errorEditView": "A vista de folha existente não pode ser editada e as novas não podem ser criadas no momento, pois algumas delas estão sendo editadas.", "SSE.Controllers.Main.errorEmailClient": "Nenhum cliente de email foi encontrado.", "SSE.Controllers.Main.errorFilePassProtect": "O documento é protegido por senha e não pode ser aberto.", "SSE.Controllers.Main.errorFileRequest": "Erro externo.<br>Erro de solicitação de arquivo. Entre em contato com o suporte caso o erro persista.", "SSE.Controllers.Main.errorFileSizeExceed": "O tamanho do arquivo excede a limitação definida para o seu servidor.<br>Entre em contato com o administrador do servidor de documentos para obter detalhes.", "SSE.Controllers.Main.errorFileVKey": "Erro externo.<br>Chave de segurança incorreta. Entre em contato com o suporte caso o erro persista.", "SSE.Controllers.Main.errorFillRange": "Não foi possível preencher o intervalo selecionado de células.<br><PERSON><PERSON> as células mescladas precisam ser do mesmo tamanho.", "SSE.Controllers.Main.errorForceSave": "Ocorreu um erro na gravação. Favor utilizar a opção 'Transferir como' para gravar o arquivo em seu computador ou tente novamente mais tarde.", "SSE.Controllers.Main.errorFormulaName": "Um erro na fórmula inserida.<br>Nome da fórmula incorreto está sendo usado.", "SSE.Controllers.Main.errorFormulaParsing": "Erro interno ao analisar a fórmula.", "SSE.Controllers.Main.errorFrmlMaxLength": "O comprimento da sua fórmula excede o limite de 8.192 caracteres. <br> Edite-a e tente novamente.", "SSE.Controllers.Main.errorFrmlMaxReference": "Você não pode inserir esta fórmula porque ela tem muitos valores,<br> referências de células, e/ou nomes.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Os valores de texto nas fórmulas são limitados a 255 caracteres.<br>Use a função CONCATENATE ou o operador de concatenação (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "A função se refere a uma folha que não existe.<br>Verifique os dados e tente novamente.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Não foi possível concluir a operação para o intervalo de células selecionado.<br>Selecione um intervalo para que a primeira linha da tabela fique na mesma linha<br>e a tabela resultante se sobreponha à atual.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Não foi possível concluir a operação para o intervalo de células selecionado.<br>Selecione um intervalo que não inclua outras tabelas.", "SSE.Controllers.Main.errorInconsistentExt": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo não corresponde à extensão do arquivo.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a documentos de texto (por exemplo, docx), mas o arquivo tem a extensão inconsistente: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a um dos seguintes formatos: pdf/djvu/xps/oxps, mas o arquivo tem a extensão inconsistente: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a apresentações (por exemplo, pptx), mas o arquivo tem a extensão inconsistente: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a planilhas (por exemplo, xlsx), mas o arquivo tem a extensão inconsistente: %1.", "SSE.Controllers.Main.errorInvalidRef": "Inserir um nome correto para a seleção ou referência válida para ir para.", "SSE.Controllers.Main.errorKeyEncrypt": "Descrição de chave desconhecida", "SSE.Controllers.Main.errorKeyExpire": "Descritor de chave expirado", "SSE.Controllers.Main.errorLabledColumnsPivot": "Para criar uma tabela dinâmica, você deve usar os dados organizados como uma lista com colunas rotuladas.", "SSE.Controllers.Main.errorLoadingFont": "As fontes não foram carregadas. <br> Entre em contato com o administrador do Document Server.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "A referência para o local ou intervalo de dados não é válida.", "SSE.Controllers.Main.errorLockedAll": "The operation could not be done as the sheet has been locked by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "Você não pode alterar a data em uma tabela dinâmica.", "SSE.Controllers.Main.errorLockedWorksheetRename": "A folha não pode ser renomeada no momento uma vez que está sendo renomeada por outro usuário", "SSE.Controllers.Main.errorMaxPoints": "O número máximo de pontos em série por gráfico é 4096.", "SSE.Controllers.Main.errorMoveRange": "Não é possível alterar parte de uma célula mesclada", "SSE.Controllers.Main.errorMoveSlicerError": "As segmentações de dados da tabela não podem ser copiadas de uma pasta de trabalho para outra. <br> Tente novamente selecionando a tabela inteira e as segmentações de dados.", "SSE.Controllers.Main.errorMultiCellFormula": "Fórmulas de matriz de várias células não são permitidas em tabelas.", "SSE.Controllers.Main.errorNoDataToParse": "Nenhum dado foi selecionado para analisar.", "SSE.Controllers.Main.errorOpenWarning": "Uma das fórmulas do arquivo excede o limite de 8192 caracteres.<br>A fórmula foi removida.", "SSE.Controllers.Main.errorOperandExpected": "A sintaxe de função inserida não está correta. Verifique se você se esqueceu de um dos parênteses - '(' ou ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "A senha fornecida não está correta. <br> Verifique se a tecla CAPS LOCK está desligada e use a capitalização correta.", "SSE.Controllers.Main.errorPasteMaxRange": "The copy and paste area does not match.<br>Please select an area with the same size or click the first cell in a row to paste the copied cells.", "SSE.Controllers.Main.errorPasteMultiSelect": "Esta ação não pode ser feita em uma seleção de gama múltipla.<br>Selecionar uma única gama e tentar novamente.", "SSE.Controllers.Main.errorPasteSlicerError": "A Segmentação de Dados de tabela não podem ser copiados de uma pasta de trabalho para outra.", "SSE.Controllers.Main.errorPivotGroup": "Não é possível agrupar essa seleção.", "SSE.Controllers.Main.errorPivotOverlap": "Um relatório de tabela dinâmica não pode se sobrepor a uma tabela.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "The Pivot Table report was saved without the underlying data.<br>Use the 'Refresh' button to update the report.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Infelizmente, não é possível imprimir mais de 1500 páginas de uma vez na versão de programa atual.<br>Esta restrição será removida nos lançamentos futuros.", "SSE.Controllers.Main.errorProcessSaveResult": "Salvamento falhou", "SSE.Controllers.Main.errorServerVersion": "A versão do editor foi atualizada. A página será recarregada para aplicar as alterações.", "SSE.Controllers.Main.errorSessionAbsolute": "A sessão de edição de documentos expirou. Atualize a página.", "SSE.Controllers.Main.errorSessionIdle": "O documento ficou sem edição por muito tempo. Atualize a página.", "SSE.Controllers.Main.errorSessionToken": "A conexão com o servidor foi interrompida. Atualize a página.", "SSE.Controllers.Main.errorSetPassword": "Não foi possível definir a senha.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "A referência de local não é válida porque as células não estão todas na mesma coluna ou linha. <br> Selecione células que estão todas em uma única coluna ou linha.", "SSE.Controllers.Main.errorStockChart": "Ordem da linha incorreta. Para criar um gráfico de ações coloque os dados na planilha na seguinte ordem:<br>preço de abertura, preço máx., preço mín., preço de fechamento.", "SSE.Controllers.Main.errorToken": "O token de segurança do documento não foi formado corretamente.<br>Entre em contato com o administrador do Document Server.", "SSE.Controllers.Main.errorTokenExpire": "O token de segurança do documento expirou.<br>Entre em contato com o administrador do Document Server.", "SSE.Controllers.Main.errorUnexpectedGuid": "Erro externo.<br>GUID inesperado. Entre em contato com o suporte caso o erro persista.", "SSE.Controllers.Main.errorUpdateVersion": "A versão do arquivo foi alterada. A página será recarregada.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "A conexão a internet foi restaurada, e a versão do arquivo foi alterada. <br> Antes de continuar seu trabalho, transfira o arquivo ou copie seu conteúdo para assegurar que nada seja perdido, e então, recarregue esta página.", "SSE.Controllers.Main.errorUserDrop": "O arquivo não pode ser acessado agora.", "SSE.Controllers.Main.errorUsersExceed": "O número de usuários permitidos pelo plano de preços foi excedido", "SSE.Controllers.Main.errorViewerDisconnect": "A conexão foi perdida. Você ainda pode ver o documento,<br>mas não pode fazer o download ou imprimir até que a conexão seja restaurada.", "SSE.Controllers.Main.errorWrongBracketsCount": "Um erro na fórmula inserida.<br><PERSON>úmero errado de parênteses está sendo usado.", "SSE.Controllers.Main.errorWrongOperator": "Um erro na fórmula inserida.<br>Operador errado está sendo usado.", "SSE.Controllers.Main.errorWrongPassword": "A senha que você forneceu não está correta.", "SSE.Controllers.Main.errRemDuplicates": "Valores duplicados encontrados e excluídos: {0}, valores exclusivos restantes: {1}.", "SSE.Controllers.Main.leavePageText": "Você não salvou as alterações nesta planilha. Clique em \"Ficar na página\" e, em seguida, em \"Salvar\" para salvá-las. Clique em \"Sair desta página\" para descartar as alterações não salvas.", "SSE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON> as alterações não salvas nesta planilha serão perdidas. <br> Clique em \"Cancelar\" e em \"Salvar\" para salvá-las. Clique em \"OK\" para descartar todas as alterações não salvas.", "SSE.Controllers.Main.loadFontsTextText": "Carregando dados...", "SSE.Controllers.Main.loadFontsTitleText": "Carregando dados", "SSE.Controllers.Main.loadFontTextText": "Carregando dados...", "SSE.Controllers.Main.loadFontTitleText": "Carregando dados", "SSE.Controllers.Main.loadImagesTextText": "Carregando imagens...", "SSE.Controllers.Main.loadImagesTitleText": "Carregando imagens", "SSE.Controllers.Main.loadImageTextText": "Carregando imagem...", "SSE.Controllers.Main.loadImageTitleText": "Carregando imagem", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON> planil<PERSON>", "SSE.Controllers.Main.notcriticalErrorTitle": "Aviso", "SSE.Controllers.Main.openErrorText": "Ocorreu um erro ao abrir o arquivo", "SSE.Controllers.Main.openTextText": "Abrindo planilha...", "SSE.Controllers.Main.openTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.pastInMergeAreaError": "Não é possível alterar parte de uma célula mesclada", "SSE.Controllers.Main.printTextText": "Imprimindo plan<PERSON>...", "SSE.Controllers.Main.printTitleText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.reloadButtonText": "Re<PERSON><PERSON><PERSON> p<PERSON>gina", "SSE.Controllers.Main.requestEditFailedMessageText": "Alguém está editando este documento neste momento. Tente novamente mais tarde.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "Ocorreu um erro ao salvar o arquivo", "SSE.Controllers.Main.saveErrorTextDesktop": "Este arquivo não pode ser salvo ou criado.<br>Possíveis razões são: <br>1. O arquivo é somente leitura. <br>2. O arquivo está sendo editado por outros usuários. <br>3. O disco está cheio ou corrompido.", "SSE.Controllers.Main.saveTextText": "<PERSON><PERSON><PERSON>...", "SSE.Controllers.Main.saveTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.scriptLoadError": "A conexão está muito lenta, alguns dos componentes não puderam ser carregados. Por favor recarregue a página.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Aplicar a todas as equações", "SSE.Controllers.Main.textBuyNow": "Visitar site", "SSE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> as alteraç<PERSON>es foram salvas", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Clique para fechar a dica", "SSE.Controllers.Main.textConfirm": "Confirmação", "SSE.Controllers.Main.textContactUs": "Contate as vendas", "SSE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "Esta equação foi criada com uma versão antiga do editor de equação que não é mais compatível. Para editá-lo, converta a equação para o formato Office Math ML. <br> Converter agora?", "SSE.Controllers.Main.textCustomLoader": "Observe que, de acordo com os termos da licença, você não tem permissão para alterar o carregador.<br>Entre em contato com nosso departamento de vendas para obter uma cotação.", "SSE.Controllers.Main.textDisconnect": "A conexão está perdida", "SSE.Controllers.Main.textFillOtherRows": "<PERSON><PERSON><PERSON> outras linhas", "SSE.Controllers.Main.textFormulaFilledAllRows": "As linhas {0} preenchidas com fórmula contêm dados. O preenchimento de outras linhas vazias pode levar alguns minutos.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "A fórmula preencheu as primeiras {0} linhas. O preenchimento de outras linhas vazias pode levar alguns minutos.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "<PERSON><PERSON><PERSON><PERSON> preenchida apenas as primeiras {0} linhas têm dados por motivo de economia de memória. Existem outras {1} linhas com dados nesta planilha. Você pode preenchê-los manualmente.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "A fórmula foi preenchida apenas as primeiras {0} linhas por motivo para salvar a memória. Outras linhas nesta planilha não possuem dados.", "SSE.Controllers.Main.textGuest": "Convidado (a)", "SSE.Controllers.Main.textHasMacros": "O arquivo contém macros automáticas. <br> Deseja executar macros?", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON> mais", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON> planil<PERSON>", "SSE.Controllers.Main.textLongName": "Digite um nome que tenha menos de 128 caracteres.", "SSE.Controllers.Main.textNeedSynchronize": "Você tem atualizações", "SSE.Controllers.Main.textNo": "Não", "SSE.Controllers.Main.textNoLicenseTitle": "Limite de licença atingido", "SSE.Controllers.Main.textPaidFeature": "Recurso pago", "SSE.Controllers.Main.textPleaseWait": "A operação pode demorar mais tempo do que o esperado. Aguarde...", "SSE.Controllers.Main.textReconnect": "A conexão é restaurada", "SSE.Controllers.Main.textRemember": "Le<PERSON><PERSON> da minha escolha para todos os arquivos. ", "SSE.Controllers.Main.textRememberMacros": "<PERSON><PERSON><PERSON> minha escolha para todas as macros", "SSE.Controllers.Main.textRenameError": "O nome de usuário não pode estar vazio.", "SSE.Controllers.Main.textRenameLabel": "Insira um nome a ser usado para colaboração", "SSE.Controllers.Main.textRequestMacros": "Uma macro faz uma solicitação para URL. Deseja permitir a solicitação para %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "Strict mode", "SSE.Controllers.Main.textText": "Тexto", "SSE.Controllers.Main.textTryQuickPrint": "Você selecionou Impressão rápida: todo o documento será impresso na última impressora selecionada ou padrão.<br>Deseja continuar?", "SSE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "SSE.Controllers.Main.textTryUndoRedoWarn": "As funções Desfazer/Refazer estão desabilitadas para o modo de coedição rápido", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textYes": "<PERSON>m", "SSE.Controllers.Main.titleLicenseExp": "Licença expirada", "SSE.Controllers.Main.titleServerVersion": "Editor atual<PERSON><PERSON>", "SSE.Controllers.Main.txtAccent": "Ace<PERSON>", "SSE.Controllers.Main.txtAll": "(Todos)", "SSE.Controllers.Main.txtArt": "Seu texto aqui", "SSE.Controllers.Main.txtBasicShapes": "Formas básicas", "SSE.Controllers.Main.txtBlank": "<PERSON> branco", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 de %2", "SSE.Controllers.Main.txtCallouts": "Textos explicativos", "SSE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtColLbls": "Etiquetas da coluna", "SSE.Controllers.Main.txtColumn": "Coluna", "SSE.Controllers.Main.txtConfidential": "Confidencial", "SSE.Controllers.Main.txtDate": "Data", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Título do <PERSON>a", "SSE.Controllers.Main.txtEditingMode": "Definir modo de edição...", "SSE.Controllers.Main.txtErrorLoadHistory": "O carregamento de histórico falhou", "SSE.Controllers.Main.txtFiguredArrows": "Setas figuradas", "SSE.Controllers.Main.txtFile": "Arquivo", "SSE.Controllers.Main.txtGrandTotal": "Total geral", "SSE.Controllers.Main.txtGroup": "Grupo", "SSE.Controllers.Main.txtHours": "horas", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matemática", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "Meses", "SSE.Controllers.Main.txtMultiSelect": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtOr": "%1 ou %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Página %1 de %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "Preparado por", "SSE.Controllers.Main.txtPrintArea": "<PERSON><PERSON>_<PERSON>_<PERSON>", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON>.", "SSE.Controllers.Main.txtQuarters": "Trimestres", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "Etiquetas de linha", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "Série", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Texto explicativo da linha 1 (borda e barra de destaque)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Texto explicativo da linha 2 (borda e barra de destaque)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Texto explicativo da linha 3 (borda e barra de destaque)", "SSE.Controllers.Main.txtShape_accentCallout1": "Texto explicativo da linha 1 (barra de destaque)", "SSE.Controllers.Main.txtShape_accentCallout2": "Texto explicativo da linha 2 (barra de destaque)", "SSE.Controllers.Main.txtShape_accentCallout3": "Texto explicativo da linha 3 (barra de destaque)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Botão Voltar ou Anterior", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Botão Inicial", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Botão em branco", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Botão Documento", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Botão Terminar", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON> ou Avançar", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Botão Ajuda", "SSE.Controllers.Main.txtShape_actionButtonHome": "Botão Home", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Botão de Informação", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Botão Vídeo", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Botão Retornar", "SSE.Controllers.Main.txtShape_actionButtonSound": "Botão de som", "SSE.Controllers.Main.txtShape_arc": "Arco", "SSE.Controllers.Main.txtShape_bentArrow": "Seta curvada", "SSE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector angular de seta", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector de seta dupla angulada", "SSE.Controllers.Main.txtShape_bentUpArrow": "Seta para cima dobrada", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Arco de bloco", "SSE.Controllers.Main.txtShape_borderCallout1": "Texto explicativo de linha 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Texto explicativo da linha 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Texto explicativo da linha 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Texto explicativo da linha 1 (sem borda)", "SSE.Controllers.Main.txtShape_callout2": "Texto explicativo da linha 2 (sem borda)", "SSE.Controllers.Main.txtShape_callout3": "Texto explicativo da linha 3 (sem borda)", "SSE.Controllers.Main.txtShape_can": "Pode", "SSE.Controllers.Main.txtShape_chevron": "Divisa", "SSE.Controllers.Main.txtShape_chord": "Acorde", "SSE.Controllers.Main.txtShape_circularArrow": "Seta circular", "SSE.Controllers.Main.txtShape_cloud": "Nuvem", "SSE.Controllers.Main.txtShape_cloudCallout": "Texto explicativo em nuvem", "SSE.Controllers.Main.txtShape_corner": "Canto", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Conector <PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector de seta curvada", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector de seta dupla curvado", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Seta curvada para baixo", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Seta esquerda curvada", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Seta direita curvada", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Seta curvada para cima", "SSE.Controllers.Main.txtShape_decagon": "Decágono", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "SSE.Controllers.Main.txtShape_diamond": "Diamante", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "SSE.Controllers.Main.txtShape_donut": "Rosquin<PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON> dupla", "SSE.Controllers.Main.txtShape_downArrow": "Seta para baixo", "SSE.Controllers.Main.txtShape_downArrowCallout": "Texto explicativo em seta para baixo", "SSE.Controllers.Main.txtShape_ellipse": "Elipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Fita curvada para baixo", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Fita curvada", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Fluxograma: Processo Alternativo", "SSE.Controllers.Main.txtShape_flowChartCollate": "Fluxograma: Agrupar", "SSE.Controllers.Main.txtShape_flowChartConnector": "Fluxograma: Conector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Fluxograma: Decisão", "SSE.Controllers.Main.txtShape_flowChartDelay": "Fluxograma: Atraso", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Fluxograma: Exibir", "SSE.Controllers.Main.txtShape_flowChartDocument": "Fluxograma: Documento", "SSE.Controllers.Main.txtShape_flowChartExtract": "Fluxograma: Extrair", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Fluxograma: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Fluxograma: Armazenamento Interno", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Fluxograma: Disco Magnético", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Fluxograma: Armazenamento de Acesso Direto", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Fluxograma: Armazenamento de Acesso Sequencial", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Fluxograma: Entrada Manual", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Fluxograma: Operação Manual", "SSE.Controllers.Main.txtShape_flowChartMerge": "Fluxograma: Mesclar", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Fluxograma: V<PERSON><PERSON>s Documentos", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Fluxograma: Conector fora da página", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Fluxograma: Dados <PERSON>", "SSE.Controllers.Main.txtShape_flowChartOr": "Fluxograma: Ou", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Fluxograma: Processo Predefinido", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Fluxograma: Preparação", "SSE.Controllers.Main.txtShape_flowChartProcess": "Fluxograma: Processo", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Fluxograma: Cartão", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Fluxograma: <PERSON><PERSON> perfurada", "SSE.Controllers.Main.txtShape_flowChartSort": "Fluxograma: Classificar", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Fluxograma: So<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Fluxograma: Terminação", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON> do<PERSON>", "SSE.Controllers.Main.txtShape_frame": "Quadro", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "Coração", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "Hexágono", "SSE.Controllers.Main.txtShape_homePlate": "Pentágono", "SSE.Controllers.Main.txtShape_horizontalScroll": "Rolagem horizontal", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosão 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosão 2", "SSE.Controllers.Main.txtShape_leftArrow": "Seta esquerda", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Texto explicativo à esquerda", "SSE.Controllers.Main.txtShape_leftBrace": "Chave aberta", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Seta: da direita para esquerda", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Seta para a esquerda direita", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Seta direita para cima", "SSE.Controllers.Main.txtShape_leftUpArrow": "Seta esquerda para cima", "SSE.Controllers.Main.txtShape_lightningBolt": "Raio", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON>a dupla", "SSE.Controllers.Main.txtShape_mathDivide": "Divisão", "SSE.Controllers.Main.txtShape_mathEqual": "Igual", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "SSE.Controllers.Main.txtShape_mathNotEqual": "Não igual", "SSE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Símbolo \"Não\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Seta direita entalhada", "SSE.Controllers.Main.txtShape_octagon": "Octógono", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelogramo", "SSE.Controllers.Main.txtShape_pentagon": "Pentágono", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline1": "Rabisco", "SSE.Controllers.Main.txtShape_polyline2": "Forma livre", "SSE.Controllers.Main.txtShape_quadArrow": "Quad Arrow", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Texto explicativo em seta quádrupla", "SSE.Controllers.Main.txtShape_rect": "Re<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "Fita para baixo", "SSE.Controllers.Main.txtShape_ribbon2": "Faixa para Cima", "SSE.Controllers.Main.txtShape_rightArrow": "Seta direita", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Texto explicativo em seta à direita", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON> a direita", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "Retângulo de canto redondo simples", "SSE.Controllers.Main.txtShape_round2DiagRect": "Retân<PERSON>lo de canto diagonal redondo", "SSE.Controllers.Main.txtShape_round2SameRect": "Retângulo com canto redondo do mesmo lado", "SSE.Controllers.Main.txtShape_roundRect": "Retângulo de canto redondo", "SSE.Controllers.Main.txtShape_rtTriangle": "Triângulo Retângulo", "SSE.Controllers.Main.txtShape_smileyFace": "Rosto sorridente", "SSE.Controllers.Main.txtShape_snip1Rect": "Retângulo de canto único recortado", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Retângulo de canto diagonal recortado", "SSE.Controllers.Main.txtShape_snip2SameRect": "Retângulo com canto recortado do mesmo lado", "SSE.Controllers.Main.txtShape_snipRoundRect": "Retângulo com canto recortado e arredondado", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "Estrela de 10 pontos", "SSE.Controllers.Main.txtShape_star12": "Estrela de 12 pontos", "SSE.Controllers.Main.txtShape_star16": "Estrela de 16 pontos", "SSE.Controllers.Main.txtShape_star24": "Estrela de 24 pontos", "SSE.Controllers.Main.txtShape_star32": "Estrela de 32 pontos", "SSE.Controllers.Main.txtShape_star4": "Estrela de 4 pontos", "SSE.Controllers.Main.txtShape_star5": "Estrela de 5 pontos", "SSE.Controllers.Main.txtShape_star6": "Estrela de 6 pontos", "SSE.Controllers.Main.txtShape_star7": "Estrela de 7 pontos", "SSE.Controllers.Main.txtShape_star8": "Estrela de 8 pontos", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Seta para a direita listrada", "SSE.Controllers.Main.txtShape_sun": "Sol", "SSE.Controllers.Main.txtShape_teardrop": "Lágrima", "SSE.Controllers.Main.txtShape_textRect": "Caixa de texto", "SSE.Controllers.Main.txtShape_trapezoid": "Trapé<PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Triângulo", "SSE.Controllers.Main.txtShape_upArrow": "Seta para cima", "SSE.Controllers.Main.txtShape_upArrowCallout": "Texto explicativo em seta para cima", "SSE.Controllers.Main.txtShape_upDownArrow": "Seta para cima e para baixo", "SSE.Controllers.Main.txtShape_uturnArrow": "Seta em forma de U", "SSE.Controllers.Main.txtShape_verticalScroll": "Rolagem vertical", "SSE.Controllers.Main.txtShape_wave": "On<PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Texto explicativo oval", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Texto explicativo retangular", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Texto explicativo retangular arredondado", "SSE.Controllers.Main.txtStarsRibbons": "Estrelas e arco-íris", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Verificar célula", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Texto explicativo", "SSE.Controllers.Main.txtStyle_Good": "Bo<PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Cabeçalho 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Cabeçalho 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Cabeçalho 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Cabeçalho 4", "SSE.Controllers.Main.txtStyle_Input": "Entrada", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON><PERSON><PERSON> vinculada", "SSE.Controllers.Main.txtStyle_Neutral": "Neutro", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON>a", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Por cento", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Texto de aviso", "SSE.Controllers.Main.txtTab": "Aba", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Tempo", "SSE.Controllers.Main.txtUnlock": "Desb<PERSON>que<PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Desbloquear intervalo", "SSE.Controllers.Main.txtUnlockRangeDescription": "Digite a senha para alterar este intervalo:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Um intervalo que você está tentando alterar está protegido por senha.", "SSE.Controllers.Main.txtValues": "Valores", "SSE.Controllers.Main.txtXAxis": "Eixo X", "SSE.Controllers.Main.txtYAxis": "Eixo Y", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> desconhecido.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>u navegador não é suportado.", "SSE.Controllers.Main.uploadDocExtMessage": "Formato de documento desconhecido.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nenhum documento car<PERSON>.", "SSE.Controllers.Main.uploadDocSizeMessage": "Tamanho máximo do documento excedido.", "SSE.Controllers.Main.uploadImageExtMessage": "Formato de imagem desconhecido.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Sem imagens carregadas.", "SSE.Controllers.Main.uploadImageSizeMessage": "Tamanho limite máximo da imagem excedido. O tamanho máximo é de 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Carregando imagem...", "SSE.Controllers.Main.uploadImageTitleText": "Carregando imagem", "SSE.Controllers.Main.waitText": "Aguarde...", "SSE.Controllers.Main.warnBrowserIE9": "O aplicativo tem baixa capacidade no IE9. Usar IE10 ou superior", "SSE.Controllers.Main.warnBrowserZoom": "A configuração de zoom atual de seu navegador não é completamente suportada. Redefina para o zoom padrão pressionando Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "Você atingiu o limite de conexões simultâneas para editores %1. Este documento será aberto apenas para visualização.<br>Entre em contato com seu administrador para saber mais.", "SSE.Controllers.Main.warnLicenseExp": "Sua licença expirou.<br>Atualize sua licença e atualize a página.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "A licença expirou.<br>Você não tem acesso à funcionalidade de edição de documentos.<br>Por favor, contate seu administrador.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "A licença precisa ser renovada. <br> Você tem acesso limitado à funcionalidade de edição de documentos. <br> Entre em contato com o administrador para obter acesso total.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Você atingiu o limite de usuários para editores %1. Entre em contato com seu administrador para saber mais.", "SSE.Controllers.Main.warnNoLicense": "Você atingiu o limite de conexões simultâneas para editores %1. Este documento será aberto apenas para visualização.<br>Entre em contato com a equipe de vendas da %1 para obter os termos de atualização pessoais.", "SSE.Controllers.Main.warnNoLicenseUsers": "Você atingiu o limite de usuários para editores %1.<br>Entre em contato com a equipe de vendas da %1 para obter os termos de atualização pessoais.", "SSE.Controllers.Main.warnProcessRightsChange": "Foi negado a você o direito de editar o arquivo.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON> as folhas", "SSE.Controllers.Print.textFirstCol": "Primeira coluna", "SSE.Controllers.Print.textFirstRow": "Primeira linha", "SSE.Controllers.Print.textFrozenCols": "Colunas congeladas", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON> congeladas", "SSE.Controllers.Print.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Controllers.Print.textNoRepeat": "Não repetir", "SSE.Controllers.Print.textRepeat": "Repetir...", "SSE.Controllers.Print.textSelectRange": "Selecionar intervalo", "SSE.Controllers.Print.textWarning": "Aviso", "SSE.Controllers.Print.txtCustom": "Personalizado", "SSE.Controllers.Print.warnCheckMargings": "Margens estão incorretas", "SSE.Controllers.Search.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Controllers.Search.textNoTextFound": "Os dados que você tem estado procurando não podem ser encontrados. Ajuste suas opções de pesquisa.", "SSE.Controllers.Search.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "SSE.Controllers.Search.textReplaceSuccess": "A pesquisa foi feita. {0} ocorrências foram substituídas", "SSE.Controllers.Statusbar.errorLastSheet": "Pasta de trabalho deve ter no mínimo uma planilha visível.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Não é possível excluir uma planilha.", "SSE.Controllers.Statusbar.strSheet": "Fol<PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>A conexão foi perdida</b><br>Tentando conectar. Verifique as configurações de conexão.", "SSE.Controllers.Statusbar.textSheetViewTip": "Você está no modo Visualização da folha. Filtros e classificação são visíveis apenas para você e para aqueles que ainda estão nesta exibição.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Você está no modo Folha De exibição. Os filtros são visíveis apenas para você e para aqueles que ainda estão nesta visão.", "SSE.Controllers.Statusbar.warnDeleteSheet": "A planilha deve conter dados. Você tem certeza de que deseja continuar?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "A fonte que você vai salvar não está disponível no dispositivo atual. <br> O estilo do texto será exibido usando uma das fontes do dispositivo, a fonte salva será usada quando estiver disponível. <br> <PERSON><PERSON><PERSON> continuar ?", "SSE.Controllers.Toolbar.errorComboSeries": "Para criar uma tabela de combinação, selecione pelo menos duas séries de dados.", "SSE.Controllers.Toolbar.errorMaxPoints": "O máximo número de pontos em séries por gráfico é 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ERRO! O número máximo de séries de dado por gráfico é 255", "SSE.Controllers.Toolbar.errorStockChart": "Ordem da linha incorreta. Para criar um gráfico de ações coloque os dados na planilha na seguinte ordem:<br> preço de abertura, preço máx., preço mín., preço de fechamento.", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Direcional", "SSE.Controllers.Toolbar.textFontSizeErr": "O valor inserido está incorreto.<br>Insira um valor numérico entre 1 e 409", "SSE.Controllers.Toolbar.textFraction": "Frações", "SSE.Controllers.Toolbar.textFunction": "Funções", "SSE.Controllers.Toolbar.textIndicator": "Indicadores", "SSE.Controllers.Toolbar.textInsert": "Inserir", "SSE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Grandes operadores", "SSE.Controllers.Toolbar.textLimitAndLog": "Limites e logaritmos", "SSE.Controllers.Toolbar.textLongOperation": "Operação longa", "SSE.Controllers.Toolbar.textMatrix": "Matrizes", "SSE.Controllers.Toolbar.textOperator": "Operadores", "SSE.Controllers.Toolbar.textPivot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRadical": "Radicais", "SSE.Controllers.Toolbar.textRating": "Classificações", "SSE.Controllers.Toolbar.textRecentlyUsed": "Usado recentemente", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Formas", "SSE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "Aviso", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Seta para direita-esquerda acima", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Seta adiante para cima", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Seta para direita acima", "SSE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON> (com Espaço Reservado)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "F<PERSON>rmula embalada(Exemplo)", "SSE.Controllers.Toolbar.txtAccent_Check": "Verificar", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave <PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Chave Superior", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vetor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "Barra superior com ABC", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y com barra superior", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Ponto <PERSON>lo", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Dot": "Ponto", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra superior dupla", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Agrupamento de caracteres abaixo", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Agrupamento de caracteres acima", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpão adiante para cima", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpão para direita acima", "SSE.Controllers.Toolbar.txtAccent_Hat": "Acento circunflexo", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Til", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parênteses com separadores", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parênteses com separadores", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Colchete de ângulo reto", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Colchete angular esquerdo", "SSE.Controllers.Toolbar.txtBracket_Curve": "Colchetes", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Colchetes com separador", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Colchete direito", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Colchete esquerdo", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (Duas Condições)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (Três Condições)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Objeto empil<PERSON>o entre parênteses", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente binominal", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binomial entre parênteses angulares", "SSE.Controllers.Toolbar.txtBracket_Line": "Barras verticais", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Barra vertical direita", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Barra vertical esquerda", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Barras verticais duplas", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Barra vertical dupla direita", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Barra vertical dupla esquerda", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON> dire<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parênteses com separadores", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parê<PERSON><PERSON> direito", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "Colchetes", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Espaço reservado entre dois colchetes direitos", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Colchetes invertidos", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Colchete direito", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Colchete esquerdo", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Espaço reservado entre dois colchetes esquerdos", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Colchetes duplos", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Colchete duplo direito", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Col<PERSON>e duplo es<PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Teto", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON> direito", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtDeleteCells": "Excluir <PERSON>", "SSE.Controllers.Toolbar.txtExpand": "Expandir e classificar", "SSE.Controllers.Toolbar.txtExpandSort": "Os dados próximos à seleção não serão classificados. Você quer expandir a seleção para incluir os dados adjacentes ou continuar com classificando apenas as células selecionadas atualmente?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Fração inclinada", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx sobre dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "limite delta y sobre limite delta x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "y parcial sobre x parcial", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Delta y sobre delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Fração linear", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi sobre 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Fração pequena", "SSE.Controllers.Toolbar.txtFractionVertical": "Fração Empilhada", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Função cosseno inverso", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Função cosseno inverso hiperbólico", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Função cotangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Função cotangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Função cossecante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Função cossecante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Função secante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Função secante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Função seno inverso", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Função seno inverso hiperbólico", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Função tangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Função tangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Cos": "Função cosseno", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Função cosseno hiperbólico", "SSE.Controllers.Toolbar.txtFunction_Cot": "Função cotangente", "SSE.Controllers.Toolbar.txtFunction_Coth": "Função cotangente hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Csc": "Função cossecante", "SSE.Controllers.Toolbar.txtFunction_Csch": "Função co-secante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON>ta seno", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON> da tangente", "SSE.Controllers.Toolbar.txtFunction_Sec": "Função secante", "SSE.Controllers.Toolbar.txtFunction_Sech": "Função secante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Sin": "Função de seno", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Função seno hiperbólico", "SSE.Controllers.Toolbar.txtFunction_Tan": "Função da tangente", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Função tangente hiperbólica", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Personalizado", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Dados e Modelo", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Bom, Mau e Neutro", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Sem nome", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Formato de número", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Estilos de células temáticas", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Títulos e Cabeçalhos", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Personalizado", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Escuro", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Médio", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Teta diferencial", "SSE.Controllers.Toolbar.txtIntegral_dx": "Derivada x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Derivada y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral com limites empilhados", "SSE.Controllers.Toolbar.txtIntegralDouble": "Integra<PERSON> dupla", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral dupla com limites empilhados", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral dupla com limites", "SSE.Controllers.Toolbar.txtIntegralOriented": "Integral de linha", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno com limites empilhados", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de Superfície", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superfície com limites empilhados", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superfície com limites", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de contorno com limites", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de Volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volume com limites empilhados", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de volume com limites", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral com limites", "SSE.Controllers.Toolbar.txtIntegralTriple": "Integral Tripla", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral tripla com limites empilhados", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral tripla com limites", "SSE.Controllers.Toolbar.txtInvalidRange": "ERRO! Intervalo de célula inválido", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Lógico e", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Lógico E com limite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Lógico E com limites", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Lógico E com limite inferior subscrito", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Lógico E com limites subscritos/sobrescritos", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproduto com limite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproduto com limites", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-produto com limite inferior subscrito", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproduto com limites subscritos/sobrescritos", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Soma sobre k de n escolha k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Soma de i igual a zero a n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Exemplo de soma usando dois índices", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Exemplo de produto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Exemplo de união", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Lógico ou", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Lógico Ou com limite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Lógico Ou com limites", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Lógico Ou com limite inferior subscrito", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Ou Lógico com limites subscritos/sobrescritos", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Interseção", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Interseção com limite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Interseção com limites", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Interseção com limite inferior subscrito", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Interseção com limites subscritos/sobrescritos", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produto com limite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produto com limites", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produto com limite inferior subscrito", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produto com limites subscritos/sobrescritos", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Soma com limite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Soma com limites", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Soma com limite inferior subscrito", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Soma com limites subscritos/sobrescritos", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "União", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "União com limite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "União com limites", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "União com limite inferior subscrito", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "União com limites subscritos/sobrescritos", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo limite", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo máximo", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "SSE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLockSort": "Os dados são encontrados ao lado de sua seleção, mas você não tem permissão suficiente para alterar essas células.<br> Você deseja continuar com a seleção atual?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON><PERSON> 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON><PERSON> 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON><PERSON> 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON><PERSON> 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz 2 por 2 vazia em barras verticais duplas", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Determinante 2 por 2 vazio", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz 2 por 2 vazia entre parênteses", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz 2 por 2 vazia entre parênteses", "SSE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON><PERSON> 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON><PERSON> Vazi<PERSON> 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON><PERSON> 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON><PERSON> 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pontos de linha de base", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Pontos de linha média", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Pontos diagonais", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Pontos verticais", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> entre parêntes<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Matriz esparsa em parênteses", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON>riz da identidade 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "<PERSON>riz da identidade 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz da identidade 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz da identidade 3x3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Seta para direita esquerda abaixo", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Seta para direita-esquerda acima", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Seta adiante para baixo", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Seta adiante para cima", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Seta para direita abaixo", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Seta para direita acima", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dois-pontos-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Resultados de Delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Igual a por definição", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Seta para direita esquerda abaixo", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Seta para direita-esquerda acima", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Seta adiante para baixo", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Seta adiante para cima", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Seta para direita abaixo", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Seta para direita acima", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Sinal de Igual-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Sinal de Menos-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Sinal de Mais-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Lado direito da fórmula quadrática", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Raiz quadrada de a ao quadrado mais b ao quadrado", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Raiz quadrada com grau", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Raiz cúbica", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radical com grau", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Raiz quadrada", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x subscrito y ao quadrado", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e elevado a menos i ômega t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x ao quadrado", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y sobrescrito à esquerda n subscrito à esquerda um", "SSE.Controllers.Toolbar.txtScriptSub": "Subscrito", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subscrito-Sobrescrito", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Subscrito à Esquerda-Sobrescrito", "SSE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "SSE.Controllers.Toolbar.txtSorting": "Classificação", "SSE.Controllers.Toolbar.txtSortSelected": "Classificar selecionado", "SSE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Quase igual a", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operador de asterisco", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operador de marcador", "SSE.Controllers.Toolbar.txtSymbol_cap": "Interseção", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Raiz cúbica", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Reticências horizontais de linha média", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "SSE.Controllers.Toolbar.txtSymbol_cup": "União", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Reticências diagonal para baixo à direita", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Sinal de divisão", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Seta para baixo", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vazio", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsílon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Igual", "SSE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON><PERSON> a", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Existe", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Fatorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON>us Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "SSE.Controllers.Toolbar.txtSymbol_gg": "Muito superior a", "SSE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "SSE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "SSE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinidade", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Capa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Seta para esquerda", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Seta esquerda-direita", "SSE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "SSE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "SSE.Controllers.Toolbar.txtSymbol_ll": "Muito inferior a", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Sinal de Menos-Sinal de <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Não igual a", "SSE.Controllers.Toolbar.txtSymbol_ni": "Contém como membro", "SSE.Controllers.Toolbar.txtSymbol_not": "Não entrar", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Não existe", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Ômega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "SSE.Controllers.Toolbar.txtSymbol_percent": "Porcentagem", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_pm": "Sinal de Menos-Sinal de Igual", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Raiz quadrada", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> da prova", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Reticências diagonal direita para cima", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rô", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Seta para direita", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de Radical", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Portanto", "SSE.Controllers.Toolbar.txtSymbol_theta": "Teta", "SSE.Controllers.Toolbar.txtSymbol_times": "Sinal de multiplicação", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Seta para cima", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON> de Epsílon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Variante de fi", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Reticências verticais", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "<PERSON><PERSON><PERSON> es<PERSON> da ta<PERSON>a", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Estilo de Tabela Médio", "SSE.Controllers.Toolbar.warnLongOperation": "A operação que você está prestes a realizar pode levar muito tempo para concluir.<br>Você tem certeza de que deseja continuar?", "SSE.Controllers.Toolbar.warnMergeLostData": "Apenas os dados da célula superior esquerda permanecerá na célula mesclada.<br>Você tem certeza de que deseja continuar? ", "SSE.Controllers.Viewport.textFreezePanes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "Mostrar sombra dos painéis congelados", "SSE.Controllers.Viewport.textHideFBar": "Ocultar barra de fórmula<PERSON>", "SSE.Controllers.Viewport.textHideGridlines": "Ocultar linhas de grade", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separador decimal", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Separador de milhares", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Configurações usadas para reconhecer dados numéricos", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Qualificador de texto", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Configurações avançadas", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(nenhum)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Filtro personalizado", "SSE.Views.AutoFilterDialog.textAddSelection": "Adicionar seleção atual para filtrar", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Brancos}", "SSE.Views.AutoFilterDialog.textSelectAll": "Selecionar todos", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Selecionar todos os resultados", "SSE.Views.AutoFilterDialog.textWarning": "Aviso", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON><PERSON> da média", "SSE.Views.AutoFilterDialog.txtBegins": "Começa com...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON><PERSON><PERSON><PERSON> da média", "SSE.Views.AutoFilterDialog.txtBetween": "Entre...", "SSE.Views.AutoFilterDialog.txtClear": "Limpar", "SSE.Views.AutoFilterDialog.txtContains": "Contém...", "SSE.Views.AutoFilterDialog.txtEmpty": "Inserir filtro de célula", "SSE.Views.AutoFilterDialog.txtEnds": "Termina com...", "SSE.Views.AutoFilterDialog.txtEquals": "É igual a...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrar por cor das células", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrar por cor da fonte", "SSE.Views.AutoFilterDialog.txtGreater": "Superior a...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Superior a ou igual a...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtro de rótulos", "SSE.Views.AutoFilterDialog.txtLess": "Inferior a...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Inferior a ou igual a...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Não começa com...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Não entre...", "SSE.Views.AutoFilterDialog.txtNotContains": "Não contém...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Não termina com...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Não é igual...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Filtro de número", "SSE.Views.AutoFilterDialog.txtReapply": "Reaplicar", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Classificar por cor das células", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Classificar por cor da fonte", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Classificar do maior para o menor", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Classificar do menor para o maior", "SSE.Views.AutoFilterDialog.txtSortOption": "Mais opções de classificação ...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtro de texto", "SSE.Views.AutoFilterDialog.txtTitle": "Filtro", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtro de valor", "SSE.Views.AutoFilterDialog.warnFilterError": "Você precisa de pelo menos um campo na área valores, para aplicar um filtro de valor.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Você deve escolher no mínimo um valor", "SSE.Views.CellEditor.textManager": "Manager", "SSE.Views.CellEditor.tipFormula": "Inserir função", "SSE.Views.CellRangeDialog.errorMaxRows": "ERRO! O número máximo de séries de dado por gráfico é 255", "SSE.Views.CellRangeDialog.errorStockChart": "Ordem da linha incorreta. Para criar um gráfico de ações coloque os dados na planilha na seguinte ordem:<br> preço de abertura, preço máx., preço mín., preço de fechamento.", "SSE.Views.CellRangeDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.CellRangeDialog.txtInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.CellRangeDialog.txtTitle": "Selecionar intervalo de dados", "SSE.Views.CellSettings.strShrink": "Reduzir para caber", "SSE.Views.CellSettings.strWrap": "Ajustar texto", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Cor de fundo", "SSE.Views.CellSettings.textBackground": "Cor do plano de fundo", "SSE.Views.CellSettings.textBorderColor": "Cor", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "Regras claras", "SSE.Views.CellSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.CellSettings.textColorScales": "Escalas de cores", "SSE.Views.CellSettings.textCondFormat": "Formatação condicional", "SSE.Views.CellSettings.textControl": "Controle de texto", "SSE.Views.CellSettings.textDataBars": "Barras de dados", "SSE.Views.CellSettings.textDirection": "Direção", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Cor do primeiro plano", "SSE.Views.CellSettings.textGradient": "Pontos de gradiente", "SSE.Views.CellSettings.textGradientColor": "Cor", "SSE.Views.CellSettings.textGradientFill": "Preenchimento Gradiente", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "itens", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "Gerenciar Regras", "SSE.Views.CellSettings.textNewRule": "Nova regra", "SSE.Views.CellSettings.textNoFill": "Sem preenchimento", "SSE.Views.CellSettings.textOrientation": "Orientação do texto", "SSE.Views.CellSettings.textPattern": "Padrão", "SSE.Views.CellSettings.textPatternFill": "Padrão", "SSE.Views.CellSettings.textPosition": "Posição", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> as bordas que você deseja alterar aplicando o estilo escolhido acima", "SSE.Views.CellSettings.textSelection": "Da seleção atual", "SSE.Views.CellSettings.textThisPivot": "De uma tabela dinâmica", "SSE.Views.CellSettings.textThisSheet": "A partir desta folha de trabalho", "SSE.Views.CellSettings.textThisTable": "A partir desta tabela", "SSE.Views.CellSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "SSE.Views.CellSettings.tipAll": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipBottom": "<PERSON><PERSON>", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON><PERSON> borda inferior diagonal", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON> borda diagonal superior", "SSE.Views.CellSettings.tipInner": "Definir apenas linhas internas", "SSE.Views.CellSettings.tipInnerHor": "Definir apenas linhas internas horizontais", "SSE.Views.CellSettings.tipInnerVert": "Definir apenas linhas internas verticais", "SSE.Views.CellSettings.tipLeft": "<PERSON><PERSON>", "SSE.Views.CellSettings.tipNone": "Definir sem bordas", "SSE.Views.CellSettings.tipOuter": "Definir bordas externas", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "SSE.Views.CellSettings.tipRight": "<PERSON><PERSON>", "SSE.Views.CellSettings.tipTop": "Borda Superior", "SSE.Views.ChartDataDialog.errorInFormula": "Há um erro na fórmula que você inseriu.", "SSE.Views.ChartDataDialog.errorInvalidReference": "A referência não é válida. A referência deve ser a uma planilha aberta.", "SSE.Views.ChartDataDialog.errorMaxPoints": "O número máximo de pontos em série por gráfico é 4.096.", "SSE.Views.ChartDataDialog.errorMaxRows": "O número máximo de séries de dados por gráfico é 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "A referência não é válida. As referências para títulos, valores, tamanhos ou rótulos de dados devem ser uma única célula, linha ou coluna.", "SSE.Views.ChartDataDialog.errorNoValues": "Para criar um gráfico, a série deve conter pelo menos um valor.", "SSE.Views.ChartDataDialog.errorStockChart": "Ordem incorreta das linhas. Para construir um gráfico de ações, coloque os dados na folha na seguinte ordem: <br> preço de abertura, preço máximo, preço mínimo, preço de fechamento.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "Rótulos de eixo horizontal (categoria)", "SSE.Views.ChartDataDialog.textData": "Faixa de dados do gráfico", "SSE.Views.ChartDataDialog.textDelete": "Remover", "SSE.Views.ChartDataDialog.textDown": "Abaixo", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.ChartDataDialog.textSelectData": "Selecionar dados", "SSE.Views.ChartDataDialog.textSeries": "Entradas de legenda (série)", "SSE.Views.ChartDataDialog.textSwitch": "<PERSON><PERSON> linha / coluna", "SSE.Views.ChartDataDialog.textTitle": "Dados do gráfico", "SSE.Views.ChartDataDialog.textUp": "Para cima", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Há um erro na fórmula que você inseriu.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "A referência não é válida. A referência deve ser a uma planilha aberta.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "O número máximo de pontos em série por gráfico é 4.096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "O número máximo de séries de dados por gráfico é 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "A referência não é válida. As referências para títulos, valores, tamanhos ou rótulos de dados devem ser uma única célula, linha ou coluna.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Para criar um gráfico, a série deve conter pelo menos um valor.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Ordem incorreta das linhas. Para construir um gráfico de ações, coloque os dados na folha na seguinte ordem: <br> preço de abertura, preço máximo, preço mínimo, preço de fechamento.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.ChartDataRangeDialog.textSelectData": "Selecionar dados", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Faixa de rótulos do eixo", "SSE.Views.ChartDataRangeDialog.txtChoose": "Escolha o intervalo", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Nome da série", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Rótulos do eixo", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtValues": "Valores", "SSE.Views.ChartDataRangeDialog.txtXValues": "Valores X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Valores Y", "SSE.Views.ChartSettings.errorMaxRows": "O número máximo de séries de dados por gráfico é 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON><PERSON><PERSON> da linha", "SSE.Views.ChartSettings.strSparkColor": "Cor", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON>", "SSE.Views.ChartSettings.text3dDepth": "Profundidade (% da base)", "SSE.Views.ChartSettings.text3dHeight": "Altura (% da base)", "SSE.Views.ChartSettings.text3dRotation": "Rotação 3D", "SSE.Views.ChartSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "SSE.Views.ChartSettings.textAutoscale": "Autoescala", "SSE.Views.ChartSettings.textBorderSizeErr": "O valor inserido está incorreto.<br>Insira um valor entre 0 pt e 1.584 pt.", "SSE.Views.ChartSettings.textChangeType": "Alterar <PERSON>", "SSE.Views.ChartSettings.textChartType": "Alterar tipo de gráfico", "SSE.Views.ChartSettings.textDefault": "Rotação padrão", "SSE.Views.ChartSettings.textDown": "Abaixo", "SSE.Views.ChartSettings.textEditData": "<PERSON><PERSON> dad<PERSON>", "SSE.Views.ChartSettings.textFirstPoint": "Primeiro ponto", "SSE.Views.ChartSettings.textHeight": "Altura", "SSE.Views.ChartSettings.textHighPoint": "Ponto alto", "SSE.Views.ChartSettings.textKeepRatio": "Proporções constantes", "SSE.Views.ChartSettings.textLastPoint": "Último ponto", "SSE.Views.ChartSettings.textLeft": "E<PERSON>rda", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON> bai<PERSON>o", "SSE.Views.ChartSettings.textMarkers": "Marcadores", "SSE.Views.ChartSettings.textNarrow": "Campo de visão estreito", "SSE.Views.ChartSettings.textNegativePoint": "Ponto negativo", "SSE.Views.ChartSettings.textPerspective": "Perspectiva", "SSE.Views.ChartSettings.textRanges": "Intervalo de dados", "SSE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRightAngle": "Eixos de ângulo reto", "SSE.Views.ChartSettings.textSelectData": "Selecionar dados", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "<PERSON><PERSON> linha / coluna", "SSE.Views.ChartSettings.textType": "Tipo", "SSE.Views.ChartSettings.textUp": "Para cima", "SSE.Views.ChartSettings.textWiden": "Ampliar o campo de visão", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "Rotação X", "SSE.Views.ChartSettings.textY": "Rotação Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERRO! O número máximo de pontos em série por gráfico é 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ERRO! O número máximo de séries de dado por gráfico é 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Ordem da linha incorreta. Para criar um gráfico de ações coloque os dados na planilha na seguinte ordem:<br>preço de abertura, preço máx., preço mín., preço de fechamento.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Não mova ou dimensione com células", "SSE.Views.ChartSettingsDlg.textAlt": "Texto Alternativo", "SSE.Views.ChartSettingsDlg.textAltDescription": "Descrição", "SSE.Views.ChartSettingsDlg.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "Automático", "SSE.Views.ChartSettingsDlg.textAutoEach": "Auto para cada", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Eixos cruzam", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Opções de eixo", "SSE.Views.ChartSettingsDlg.textAxisPos": "Posição de eixos", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Configurações de Eixos", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Entre marcas de escala", "SSE.Views.ChartSettingsDlg.textBillions": "Bilhões", "SSE.Views.ChartSettingsDlg.textBottom": "Inferior", "SSE.Views.ChartSettingsDlg.textCategoryName": "Nome da categoria", "SSE.Views.ChartSettingsDlg.textCenter": "Centro", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementos do gráfico e<br>Legenda do gráfico", "SSE.Views.ChartSettingsDlg.textChartTitle": "Título do gráfico", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Personalizar", "SSE.Views.ChartSettingsDlg.textDataColumns": "em colunas", "SSE.Views.ChartSettingsDlg.textDataLabels": "Ró<PERSON><PERSON> de dados", "SSE.Views.ChartSettingsDlg.textDataRows": "em linhas", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON><PERSON> legenda", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Células ocultas e vazias", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Conectar pontos de dados com linha", "SSE.Views.ChartSettingsDlg.textFit": "<PERSON><PERSON><PERSON> largura", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Formato da etiqueta", "SSE.Views.ChartSettingsDlg.textGaps": "Intervalos", "SSE.Views.ChartSettingsDlg.textGridLines": "Gridlines", "SSE.Views.ChartSettingsDlg.textGroup": "Agrupar minigráfico", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON>de", "SSE.Views.ChartSettingsDlg.textHideAxis": "Ocultar eixo", "SSE.Views.ChartSettingsDlg.textHigh": "Alto", "SSE.Views.ChartSettingsDlg.textHorAxis": "Eixo horizontal", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Eixo Horizontal Secundário", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100.000.000 ", "SSE.Views.ChartSettingsDlg.textHundreds": "Centenas", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100.000 ", "SSE.Views.ChartSettingsDlg.textIn": "Em", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Parte inferior interna", "SSE.Views.ChartSettingsDlg.textInnerTop": "Parte superior interna", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.ChartSettingsDlg.textLabelDist": "Distância da etiqueta de eixos", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervalo entre Etiquetas", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Opções de etiqueta", "SSE.Views.ChartSettingsDlg.textLabelPos": "Posição da etiqueta", "SSE.Views.ChartSettingsDlg.textLayout": "Layout", "SSE.Views.ChartSettingsDlg.textLeft": "E<PERSON>rda", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Sobreposição esquerda", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Inferior", "SSE.Views.ChartSettingsDlg.textLegendLeft": "E<PERSON>rda", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Parte superior", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Intervalo de localização", "SSE.Views.ChartSettingsDlg.textLogScale": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "Baixo", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorType": "Tipo principal", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "Marcadores", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervalo entre Marcas", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON>or máxi<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON>ipo menor", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON> m<PERSON>", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Próximo ao eixo", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Sem sobreposição", "SSE.Views.ChartSettingsDlg.textOneCell": "Mover, mas não dimensionar, com células", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Nas marcas de escala", "SSE.Views.ChartSettingsDlg.textOut": "Fora", "SSE.Views.ChartSettingsDlg.textOuterTop": "Fora do topo", "SSE.Views.ChartSettingsDlg.textOverlay": "Sobreposição", "SSE.Views.ChartSettingsDlg.textReverse": "Valores em ordem reversa", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Ordem reversa", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Sobreposição direita", "SSE.Views.ChartSettingsDlg.textRotated": "Girado", "SSE.Views.ChartSettingsDlg.textSameAll": "O mesmo para todos", "SSE.Views.ChartSettingsDlg.textSelectData": "Selecionar dados", "SSE.Views.ChartSettingsDlg.textSeparator": "Separador de rótulos de dados", "SSE.Views.ChartSettingsDlg.textSeriesName": "Nome da série", "SSE.Views.ChartSettingsDlg.textShow": "Show", "SSE.Views.ChartSettingsDlg.textShowBorders": "Exibir bordas do gráfico", "SSE.Views.ChartSettingsDlg.textShowData": "Exibir dados em linhas e colunas ocultas", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON><PERSON><PERSON> vazias como", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "Exibir valores do gráfico", "SSE.Views.ChartSettingsDlg.textSingle": "Minigráfico único", "SSE.Views.ChartSettingsDlg.textSmooth": "Suave", "SSE.Views.ChartSettingsDlg.textSnap": "Captura de células", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Intervalos de brilhos", "SSE.Views.ChartSettingsDlg.textStraight": "Reto", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10.000.000 ", "SSE.Views.ChartSettingsDlg.textTenThousands": "10.000 ", "SSE.Views.ChartSettingsDlg.textThousands": "Milhares", "SSE.Views.ChartSettingsDlg.textTickOptions": "Opções de escala", "SSE.Views.ChartSettingsDlg.textTitle": "Gráfico - Configurações avançadas", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Minigráfico - Configurações avançadas", "SSE.Views.ChartSettingsDlg.textTop": "Parte superior", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON>l<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Mover e dimensionar com células", "SSE.Views.ChartSettingsDlg.textType": "Tipo", "SSE.Views.ChartSettingsDlg.textTypeData": "Tipo e Dados", "SSE.Views.ChartSettingsDlg.textUnits": "Exibir unidades", "SSE.Views.ChartSettingsDlg.textValue": "Valor", "SSE.Views.ChartSettingsDlg.textVertAxis": "Eixo vertical", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Eixo Vertical Secundário", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Título do eixo X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Título do eixo Y", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "Este campo é obrigatório", "SSE.Views.ChartTypeDialog.errorComboSeries": "Para criar uma tabela de combinação, selecione pelo menos duas séries de dados.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "O tipo de gráfico selecionado requer o eixo secundário que um gráfico existente está usando. Selecione outro tipo de gráfico.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textSeries": "Série", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Tipo de Gráfico", "SSE.Views.ChartTypeDialog.textType": "Tipo", "SSE.Views.CreatePivotDialog.textDataRange": "Intervalo de dados de origem", "SSE.Views.CreatePivotDialog.textDestination": "Escolha onde colocar a tabela", "SSE.Views.CreatePivotDialog.textExist": "<PERSON><PERSON><PERSON> existente", "SSE.Views.CreatePivotDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.CreatePivotDialog.textNew": "Nova planilha", "SSE.Views.CreatePivotDialog.textSelectData": "Selecionar dados", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON><PERSON> tabela din<PERSON>a", "SSE.Views.CreatePivotDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.CreateSparklineDialog.textDataRange": "Intervalo de dados de origem", "SSE.Views.CreateSparklineDialog.textDestination": "E<PERSON>l<PERSON>, onde colocar os sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.CreateSparklineDialog.textSelectData": "Selecionar dados", "SSE.Views.CreateSparklineDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.CreateSparklineDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.DataTab.capBtnGroup": "Grupo", "SSE.Views.DataTab.capBtnTextCustomSort": "Classificação personalizada", "SSE.Views.DataTab.capBtnTextDataValidation": "Validação de dados", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Remover duplicatas", "SSE.Views.DataTab.capBtnTextToCol": "Texto para colunas", "SSE.Views.DataTab.capBtnUngroup": "Desagrupar", "SSE.Views.DataTab.capDataExternalLinks": "Links externos", "SSE.Views.DataTab.capDataFromText": "Obter dados", "SSE.Views.DataTab.mniFromFile": "De TXT / CSV local", "SSE.Views.DataTab.mniFromUrl": "Do endereço da web TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "Do XML local", "SSE.Views.DataTab.textBelow": "Resumo das linhas abaixo dos detalhes", "SSE.Views.DataTab.textClear": "Limpar Estrutura de Tópicos", "SSE.Views.DataTab.textColumns": "Desagrupar colunas", "SSE.Views.DataTab.textGroupColumns": "Agrupar colunas", "SSE.Views.DataTab.textGroupRows": "Agrupar linhas", "SSE.Views.DataTab.textRightOf": "Colunas de resumo à direita dos detalhes", "SSE.Views.DataTab.textRows": "Desagrupar linhas", "SSE.Views.DataTab.tipCustomSort": "Classificação personalizada", "SSE.Views.DataTab.tipDataFromText": "Obtenha dados do arquivo Texto/CSV", "SSE.Views.DataTab.tipDataValidation": "Validação de dados", "SSE.Views.DataTab.tipExternalLinks": "Ver outros arquivos aos quais esta planilha está vinculada", "SSE.Views.DataTab.tipGroup": "Agrupar intervalo de células", "SSE.Views.DataTab.tipRemDuplicates": "Remover linhas duplicadas de uma planilha", "SSE.Views.DataTab.tipToColumns": "Separe o texto da célula em colunas", "SSE.Views.DataTab.tipUngroup": "Desagrupar intervalo de células", "SSE.Views.DataValidationDialog.errorFormula": "O valor avaliado atualmente é um erro. Você quer continuar?", "SSE.Views.DataValidationDialog.errorInvalid": "O valor inserido para o campo \"{0}\" é inválido.", "SSE.Views.DataValidationDialog.errorInvalidDate": "A data entrada para o campo \"{0}\" é inválida.", "SSE.Views.DataValidationDialog.errorInvalidList": "A fonte da lista deve ser uma lista delimitada, ou uma referência a uma única linha ou coluna.", "SSE.Views.DataValidationDialog.errorInvalidTime": "O tempo entrado para o campo \"{0}\" é inválido.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "O campo \"{1}\" deve ser maior ou igual ao campo \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Você deve inserir um valor nos campos \"{0}\" e \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Você deve inserir um valor no campo \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Uma faixa nomeada que você especificou não pode ser encontrada.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Os valores negativos não podem ser utilizados em condições \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "O campo \"{0}\" deve ser um valor numérico, expressão numérica ou referir-se a uma célula contendo um valor numérico.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON> de erro", "SSE.Views.DataValidationDialog.strInput": "Mensagem de entrada", "SSE.Views.DataValidationDialog.strSettings": "Configurações", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Aplicar estas mudanças a todas as outras células com as mesmas configurações", "SSE.Views.DataValidationDialog.textCellSelected": "Quando a célula é selecionada, mostrar esta mensagem de entrada", "SSE.Views.DataValidationDialog.textCompare": "Compare com", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Data de conclusão", "SSE.Views.DataValidationDialog.textEndTime": "Tempo final", "SSE.Views.DataValidationDialog.textError": "Mensagem de erro", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "Ignorar o espaço em branco", "SSE.Views.DataValidationDialog.textInput": "Mensagem de entrada", "SSE.Views.DataValidationDialog.textMax": "Máximo", "SSE.Views.DataValidationDialog.textMessage": "Mensagem", "SSE.Views.DataValidationDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textSelectData": "Selecionar dados", "SSE.Views.DataValidationDialog.textShowDropDown": "Mostrar lista suspensa na célula", "SSE.Views.DataValidationDialog.textShowError": "Mostrar alerta de erro após a inserção de dados inválidos", "SSE.Views.DataValidationDialog.textShowInput": "Mostrar mensagem de entrada quando a célula é selecionada", "SSE.Views.DataValidationDialog.textSource": "Fonte", "SSE.Views.DataValidationDialog.textStartDate": "Data de início", "SSE.Views.DataValidationDialog.textStartTime": "Hora de início", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Quando o usuário inserir dados inválidos, mostrar este alerta de erro", "SSE.Views.DataValidationDialog.txtAny": "Qualquer valor", "SSE.Views.DataValidationDialog.txtBetween": "entre", "SSE.Views.DataValidationDialog.txtDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Tempo transcorrido", "SSE.Views.DataValidationDialog.txtEndDate": "Data de conclusão", "SSE.Views.DataValidationDialog.txtEndTime": "Tempo final", "SSE.Views.DataValidationDialog.txtEqual": "É igual a", "SSE.Views.DataValidationDialog.txtGreaterThan": "Superior a", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Superior a ou igual a", "SSE.Views.DataValidationDialog.txtLength": "Comprimento", "SSE.Views.DataValidationDialog.txtLessThan": "Inferior a", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Inferior a ou igual a", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "Não entre...", "SSE.Views.DataValidationDialog.txtNotEqual": "não é igual a", "SSE.Views.DataValidationDialog.txtOther": "Outro", "SSE.Views.DataValidationDialog.txtStartDate": "Data de início", "SSE.Views.DataValidationDialog.txtStartTime": "Hora de início", "SSE.Views.DataValidationDialog.txtTextLength": "Tamanho do texto", "SSE.Views.DataValidationDialog.txtTime": "Tempo", "SSE.Views.DataValidationDialog.txtWhole": "Número <PERSON>iro", "SSE.Views.DigitalFilterDialog.capAnd": "E", "SSE.Views.DigitalFilterDialog.capCondition1": "igual", "SSE.Views.DigitalFilterDialog.capCondition10": "não termina com", "SSE.Views.DigitalFilterDialog.capCondition11": "contém", "SSE.Views.DigitalFilterDialog.capCondition12": "não contém", "SSE.Views.DigitalFilterDialog.capCondition2": "não é igual a", "SSE.Views.DigitalFilterDialog.capCondition3": "é superior a", "SSE.Views.DigitalFilterDialog.capCondition4": "é superior ou igual a", "SSE.Views.DigitalFilterDialog.capCondition5": "é inferior a", "SSE.Views.DigitalFilterDialog.capCondition6": "é inferior ou igual a", "SSE.Views.DigitalFilterDialog.capCondition7": "começa com", "SSE.Views.DigitalFilterDialog.capCondition8": "não começa com", "SSE.Views.DigitalFilterDialog.capCondition9": "termina com", "SSE.Views.DigitalFilterDialog.capOr": "Ou", "SSE.Views.DigitalFilterDialog.textNoFilter": "sem filtro", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON> onde", "SSE.Views.DigitalFilterDialog.textUse1": "Usar ? para apresentar qualquer caractere único", "SSE.Views.DigitalFilterDialog.textUse2": "Usar * para apresentar qualquer série de caracteres", "SSE.Views.DigitalFilterDialog.txtTitle": "Filtro personalizado", "SSE.Views.DocumentHolder.advancedEquationText": "Definições de equações", "SSE.Views.DocumentHolder.advancedImgText": "Configurações avançadas de imagem", "SSE.Views.DocumentHolder.advancedShapeText": "Configurações avançadas de forma", "SSE.Views.DocumentHolder.advancedSlicerText": "Segmentação de Dados- Config. avançadas", "SSE.Views.DocumentHolder.allLinearText": "Tudo - Linear", "SSE.Views.DocumentHolder.allProfText": "Tudo - Profissional", "SSE.Views.DocumentHolder.bottomCellText": "Alinhar à parte inferior", "SSE.Views.DocumentHolder.bulletsText": "Marcadores e numeração", "SSE.Views.DocumentHolder.centerCellText": "Alinhar ao centro", "SSE.Views.DocumentHolder.chartDataText": "Selecionar dados do gráfico", "SSE.Views.DocumentHolder.chartText": "Configurações avançadas de gráfico", "SSE.Views.DocumentHolder.chartTypeText": "Alterar tipo de gráfico", "SSE.Views.DocumentHolder.currLinearText": "Atual - Linear", "SSE.Views.DocumentHolder.currProfText": "Atual - Profissional", "SSE.Views.DocumentHolder.deleteColumnText": "Coluna", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Rotate at 270°", "SSE.Views.DocumentHolder.direct90Text": "Rotate at 90°", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Text Direction", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON> dad<PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "SSE.Views.DocumentHolder.insertColumnRightText": "Coluna direita", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTex", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "Remover hiperlink", "SSE.Views.DocumentHolder.selectColumnText": "Coluna inteira", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON> da coluna", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strDelete": "Remover assinatura", "SSE.Views.DocumentHolder.strDetails": "Detalhes da assinatura", "SSE.Views.DocumentHolder.strSetup": "Configuração de assinatura", "SSE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "Organizar", "SSE.Views.DocumentHolder.textArrangeBack": "Enviar para plano de fundo", "SSE.Views.DocumentHolder.textArrangeBackward": "Enviar para trás", "SSE.Views.DocumentHolder.textArrangeForward": "Trazer para frente", "SSE.Views.DocumentHolder.textArrangeFront": "Trazer para primeiro plano", "SSE.Views.DocumentHolder.textAverage": "Média", "SSE.Views.DocumentHolder.textBullets": "Marcadores", "SSE.Views.DocumentHolder.textCount": "Contagem", "SSE.Views.DocumentHolder.textCrop": "Cortar", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Ajustar", "SSE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textEntriesList": "Selecionar da lista suspensa", "SSE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "SSE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "SSE.Views.DocumentHolder.textFreezePanes": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromFile": "De arquivo", "SSE.Views.DocumentHolder.textFromStorage": "De armazenamento", "SSE.Views.DocumentHolder.textFromUrl": "De URL", "SSE.Views.DocumentHolder.textListSettings": "Configurações da lista", "SSE.Views.DocumentHolder.textMacro": "Atribuir Macro", "SSE.Views.DocumentHolder.textMax": "Máx", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "<PERSON><PERSON> formatos", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numeração", "SSE.Views.DocumentHolder.textReplace": "Substituir imagem", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Girar 90° no sentido anti-horário", "SSE.Views.DocumentHolder.textRotate90": "Girar 90° no sentido horário", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Alinhar à parte inferior", "SSE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON> no meio", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Alinhar à esquerda", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON> no meio", "SSE.Views.DocumentHolder.textShapeAlignRight": "Alinhar à direita", "SSE.Views.DocumentHolder.textShapeAlignTop": "Alinhar em cima", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON>las de flecha", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Marcas de verificação", "SSE.Views.DocumentHolder.tipMarkersDash": "Marcadores de roteiro", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Vinhetas rômbicas cheias", "SSE.Views.DocumentHolder.tipMarkersFRound": "Balas redondas cheias", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Balas quadradas cheias", "SSE.Views.DocumentHolder.tipMarkersHRound": "Balas redondas ocas", "SSE.Views.DocumentHolder.tipMarkersStar": "Balas de estrelas", "SSE.Views.DocumentHolder.topCellText": "Alinhar à parte superior", "SSE.Views.DocumentHolder.txtAccounting": "Contabilidade", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Define Name", "SSE.Views.DocumentHolder.txtArrange": "Organizar", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "<PERSON>rg<PERSON> da coluna de ajuste automático", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Altura da coluna de ajuste automático", "SSE.Views.DocumentHolder.txtClear": "Limpar", "SSE.Views.DocumentHolder.txtClearAll": "Todos", "SSE.Views.DocumentHolder.txtClearComments": "Comentários", "SSE.Views.DocumentHolder.txtClearFormat": "Formato", "SSE.Views.DocumentHolder.txtClearHyper": "Hiperlinks", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Limpar grupos de minigráfico selecionados", "SSE.Views.DocumentHolder.txtClearSparklines": "Limpar minigráficos selecionados", "SSE.Views.DocumentHolder.txtClearText": "Texto", "SSE.Views.DocumentHolder.txtColumn": "Coluna inteira", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON> da coluna", "SSE.Views.DocumentHolder.txtCondFormat": "Formatação condicional", "SSE.Views.DocumentHolder.txtCopy": "Copiar", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON><PERSON> la<PERSON> da coluna", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Personalizar altura da coluna", "SSE.Views.DocumentHolder.txtCustomSort": "Classificação personalizada", "SSE.Views.DocumentHolder.txtCut": "Cortar", "SSE.Views.DocumentHolder.txtDate": "Data", "SSE.Views.DocumentHolder.txtDelete": "Excluir", "SSE.Views.DocumentHolder.txtDescending": "Decrescente", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilter": "Filtro", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrar por cor da célula", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrar por cor da fonte", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrar por valor da célula selecionada", "SSE.Views.DocumentHolder.txtFormula": "Inserir função", "SSE.Views.DocumentHolder.txtFraction": "Fração", "SSE.Views.DocumentHolder.txtGeneral": "G<PERSON>", "SSE.Views.DocumentHolder.txtGetLink": "Obter link para este intervalo", "SSE.Views.DocumentHolder.txtGroup": "Grupo", "SSE.Views.DocumentHolder.txtHide": "Ocultar", "SSE.Views.DocumentHolder.txtInsert": "Inserir", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperlink", "SSE.Views.DocumentHolder.txtNumber": "Número", "SSE.Views.DocumentHolder.txtNumFormat": "Formato numérico", "SSE.Views.DocumentHolder.txtPaste": "Colar", "SSE.Views.DocumentHolder.txtPercentage": "Percentagem", "SSE.Views.DocumentHolder.txtReapply": "Reaplicar", "SSE.Views.DocumentHolder.txtRefresh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON>ura da linha", "SSE.Views.DocumentHolder.txtScientific": "Científico", "SSE.Views.DocumentHolder.txtSelect": "Selecionar", "SSE.Views.DocumentHolder.txtShiftDown": "Deslocar células para baixo", "SSE.Views.DocumentHolder.txtShiftLeft": "Deslocar células para a esquerda", "SSE.Views.DocumentHolder.txtShiftRight": "Deslocar células para a direita", "SSE.Views.DocumentHolder.txtShiftUp": "Deslocar células para cima", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSort": "Classificar", "SSE.Views.DocumentHolder.txtSortCellColor": "Selecionar cor da célula no topo", "SSE.Views.DocumentHolder.txtSortFontColor": "<PERSON><PERSON><PERSON><PERSON> cor da fonte no topo", "SSE.Views.DocumentHolder.txtSparklines": "Minigráficos", "SSE.Views.DocumentHolder.txtText": "Texto", "SSE.Views.DocumentHolder.txtTextAdvanced": "Configurações avançadas de parágrafo", "SSE.Views.DocumentHolder.txtTime": "Tempo", "SSE.Views.DocumentHolder.txtUngroup": "Desagrupar", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Alinhamento vertical", "SSE.Views.ExternalLinksDlg.closeButtonText": "En<PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textDelete": "Quebrar links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Quebrar todos os links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textSource": "Fonte", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Desconhecido", "SSE.Views.ExternalLinksDlg.textUpdate": "Atualizar valores", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Atualize tudo", "SSE.Views.ExternalLinksDlg.textUpdating": "Atualizando...", "SSE.Views.ExternalLinksDlg.txtTitle": "Links externos", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotais", "SSE.Views.FieldSettingsDialog.textReport": "Formulário de relatório", "SSE.Views.FieldSettingsDialog.textTitle": "Configurações de campo", "SSE.Views.FieldSettingsDialog.txtAverage": "Média", "SSE.Views.FieldSettingsDialog.txtBlank": "Inserir linhas em branco após cada item", "SSE.Views.FieldSettingsDialog.txtBottom": "Mostrar na parte inferior do grupo", "SSE.Views.FieldSettingsDialog.txtCompact": "Compactar", "SSE.Views.FieldSettingsDialog.txtCount": "Contagem", "SSE.Views.FieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Nome personalizado", "SSE.Views.FieldSettingsDialog.txtEmpty": "Mostrar itens sem dados", "SSE.Views.FieldSettingsDialog.txtMax": "Máx", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Esboço", "SSE.Views.FieldSettingsDialog.txtProduct": "Produ<PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repita os rótulos dos itens em cada linha", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Mostrar subtotais", "SSE.Views.FieldSettingsDialog.txtSourceName": "<PERSON>me da fonte:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funções para subtotais", "SSE.Views.FieldSettingsDialog.txtTabular": "Forma da Tabela", "SSE.Views.FieldSettingsDialog.txtTop": "Mostrar no topo do grupo", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Local do arquivo aberto", "SSE.Views.FileMenu.btnCloseMenuCaption": "Fechar menu", "SSE.Views.FileMenu.btnCreateNewCaption": "Criar novo...", "SSE.Views.FileMenu.btnDownloadCaption": "Transferir como", "SSE.Views.FileMenu.btnExitCaption": "En<PERSON><PERSON>", "SSE.Views.FileMenu.btnFileOpenCaption": "Abrir", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Hist<PERSON><PERSON><PERSON> <PERSON> ve<PERSON>ão", "SSE.Views.FileMenu.btnInfoCaption": "Informações da planilha", "SSE.Views.FileMenu.btnPrintCaption": "Imprimir", "SSE.Views.FileMenu.btnProtectCaption": "Proteger", "SSE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "SSE.Views.FileMenu.btnRenameCaption": "Renomear", "SSE.Views.FileMenu.btnReturnCaption": "Voltar para planilha", "SSE.Views.FileMenu.btnRightsCaption": "Direitos de Acesso", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Salvar cópia como", "SSE.Views.FileMenu.btnSettingsCaption": "Configurações avançadas", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Folha de Cálculo em Branco", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Criar novo", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Adicionar Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Adicionar texto", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicação", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Alterar direitos de acesso", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comente", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificação por", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificação", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Localização", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Pessoas que têm direitos", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etiquetas", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON> planil<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Carregado", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Alterar direitos de acesso", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Pessoas que têm direitos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Modo de coedição", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use o sistema de data de 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separador decimal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Idioma do dicionário", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Dicas de fonte", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Linguagem de fórmula", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Exemplo: SOMA; MÍNIMO; MÁXIMO; CONT.NÚM", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorar palavras MAIÚSCULAS", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignorar palavras com números", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Configurações de macros", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Mostrar o botão Opções de colagem quando o conteúdo for colado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Estilo de Referência R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regional Settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Example: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Mostrar comentários na planilha", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Mostrar alterações de outros usuários", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "<PERSON>rar coment<PERSON>rios resolvidos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Estrito", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Tema de interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "separador de milhares", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unidade de medida", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Usar separadores com base nas configurações regionais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Valor de zoom padrão", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "A cada 10 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "A cada 30 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "A cada 5 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "A cada hora", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Recuperação automática", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Salvamento automático", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Desabilitado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "<PERSON><PERSON> para servidor", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "A cada minuto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Referência de Estilos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opções de autocorreção...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Bielorrusso", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Búlgaro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalão", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Modo de cache padrão", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculando", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centímetro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Colaboração", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Tcheco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "De<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editando e salvando", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "English", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Espanhol", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Co-edição em tempo real. Todas as alterações são salvas automaticamente", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonésio", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Polegada", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italiano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "como SO X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Ponto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Mostrar o botão Impressão rápida no cabeçalho do editor", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "O documento será impresso na última impressora selecionada ou padrão", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Região", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romeno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Russian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Habilitar todos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Eslovaco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Esloveno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Desabilitar tudo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "<PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Use o bot<PERSON> \"Salvar\" para sincronizar as alterações que você e outras pessoas fazem", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Sueco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ucraniano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Use a tecla Alt para navegar na interface do usuário usando o teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Use a tecla Option para navegar na interface do usuário usando o teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Mostrar notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros com uma notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "como Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Atenção", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON> senha", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger <PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Com assinatura", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "A edição removerá as assinaturas da planilha.<br>Tem certeza de que deseja continuar?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Esta planilha foi protegida por senha", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Esta planilha precisa ser assinada.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Assinaturas válidas foram adicionadas à planilha. A planilha está protegida contra edição.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algumas assinaturas digitais da planilha são inválidas ou não puderam ser verificadas. A planilha está protegida contra edição.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON>r assina<PERSON>s", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Aviso", "SSE.Views.FormatRulesEditDlg.text2Scales": "Escala Bicolor", "SSE.Views.FormatRulesEditDlg.text3Scales": "Escala Tricolor", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON> as bordas", "SSE.Views.FormatRulesEditDlg.textAppearance": "Aparência de barra", "SSE.Views.FormatRulesEditDlg.textApply": "Aplicar ao intervalo", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automático", "SSE.Views.FormatRulesEditDlg.textAxis": "Eixo", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Direção da Barra", "SSE.Views.FormatRulesEditDlg.textBold": "Negrito", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON> bordas", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bordas inferiores", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Não é possível adicionar a formatação condicional.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Ponto médio da célula", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Bordas verticais interiores", "SSE.Views.FormatRulesEditDlg.textClear": "Limpar", "SSE.Views.FormatRulesEditDlg.textColor": "Cor do texto", "SSE.Views.FormatRulesEditDlg.textContext": "Contexto", "SSE.Views.FormatRulesEditDlg.textCustom": "Personalizado", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Borda inferior diagonal", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Borda superior diagonal", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Insira uma fórmula válida.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "A fórmula que você inseriu não avalia para um número, data, hora ou string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Digite um valor.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "O valor inserido não é um número, data, hora ou string válido.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "O valor para o {0} deve ser maior do que o valor para o {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Digite um número entre {0} e {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Formato", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradiente", "SSE.Views.FormatRulesEditDlg.textIconLabel": "quando {0} {1} e", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "quando {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "quando o valor é", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Um ou mais intervalos de dados de ícones se sobrepõem.<br>Ajustar valores de intervalos de dados de ícones para que os intervalos não se sobreponham.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Bordas interiores", "SSE.Views.FormatRulesEditDlg.textInvalid": "Faixa de dados inválida.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.FormatRulesEditDlg.textItalic": "Itálico", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Da esquerda para a direita", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "barra mais longa", "SSE.Views.FormatRulesEditDlg.textMaximum": "Máximo", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Ponto máxi<PERSON>", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Bordas horizontais interiores", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Ponto médio", "SSE.Views.FormatRulesEditDlg.textMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativo", "SSE.Views.FormatRulesEditDlg.textNewColor": "Nova cor personalizada", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON> bordas", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Um ou mais dos valores especificados não é uma porcentagem válida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "O valor especificado {0} não é uma porcentagem válida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Um ou mais dos valores especificados não é um percentil válido.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "O valor especificado {0} não é um percentil válido.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON> externas", "SSE.Views.FormatRulesEditDlg.textPercent": "Por cento", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Posição", "SSE.Views.FormatRulesEditDlg.textPositive": "Positivo", "SSE.Views.FormatRulesEditDlg.textPresets": "Predefinições", "SSE.Views.FormatRulesEditDlg.textPreview": "Pré-visualizar", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Não é possível utilizar referências relativas em critérios de formatação condicional para escalas de cores, barras de dados e conjuntos de ícones.", "SSE.Views.FormatRulesEditDlg.textReverse": "Ordem Reversa dos Ícones", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Da direita para a esquerda", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "Regra", "SSE.Views.FormatRulesEditDlg.textSameAs": "O mesmo que positivo", "SSE.Views.FormatRulesEditDlg.textSelectData": "Selecionar dados", "SSE.Views.FormatRulesEditDlg.textShortBar": "barra mais curta", "SSE.Views.FormatRulesEditDlg.textShowBar": "Mostrar apenas barra", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Mostrar apenas ícone", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Este tipo de referência não pode ser usado em uma fórmula de formatação condicional.<br>Alterar a referência para uma única célula, ou usar a referência com uma função de planilha, tal como =SOMA(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscrito", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Sobrescrito", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Bordas superiores", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Formato Numérico", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Contabilidade", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Data", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Este campo é obrigatório", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fração", "SSE.Views.FormatRulesEditDlg.txtGeneral": "G<PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNumber": "Número", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Porcentagem", "SSE.Views.FormatRulesEditDlg.txtScientific": "Científico", "SSE.Views.FormatRulesEditDlg.txtText": "Тexto", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Editar regra de formatação", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nova regra de formatação", "SSE.Views.FormatRulesManagerDlg.guestText": "Convidado(a)", "SSE.Views.FormatRulesManagerDlg.lockText": "Bloqueado", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 desvio padrão acima da Média", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 desvio padrão abaixo da Média", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 desvios padrão acima da Média", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 desvios padrão abaixo da Média", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 desvios padrão acima da Média", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 desvios padrão abaixo da Média", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON><PERSON> da média", "SSE.Views.FormatRulesManagerDlg.textApply": "Aplicar à", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "O valor da célula começa com", "SSE.Views.FormatRulesManagerDlg.textBelow": "<PERSON><PERSON><PERSON><PERSON> da média", "SSE.Views.FormatRulesManagerDlg.textBetween": "<PERSON><PERSON> entre {0} e {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON>lula", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON><PERSON> de cor graduada", "SSE.Views.FormatRulesManagerDlg.textContains": "O valor da célula contém", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "A célula contém um valor em branco", "SSE.Views.FormatRulesManagerDlg.textContainsError": "A célula contém um erro", "SSE.Views.FormatRulesManagerDlg.textDelete": "Excluir", "SSE.Views.FormatRulesManagerDlg.textDown": "Mover regra para baixo", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Valores duplicados", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "O valor da célula termina com", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Igual ou acima da média", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Igual ou inferior à média", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formato", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Conjunto de ícones", "SSE.Views.FormatRulesManagerDlg.textNew": "Novo", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "não se encontra entre {0} e {1}.", "SSE.Views.FormatRulesManagerDlg.textNotContains": "O valor da célula não contém", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "A célula não contém um valor em branco", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "A célula não contém um erro", "SSE.Views.FormatRulesManagerDlg.textRules": "Regras", "SSE.Views.FormatRulesManagerDlg.textScope": "Mostrar regras de formatação para", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Selecionar dados", "SSE.Views.FormatRulesManagerDlg.textSelection": "<PERSON><PERSON><PERSON> atual", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Esta folha de trabalho", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> tabela", "SSE.Views.FormatRulesManagerDlg.textUnique": "Valores únicos", "SSE.Views.FormatRulesManagerDlg.textUp": "Mover regra para cima", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Este elemento está sendo editado por outro usuário.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Formatação condicional", "SSE.Views.FormatSettingsDialog.textCategory": "Categoria", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Formato", "SSE.Views.FormatSettingsDialog.textLinked": "Ligado à fonte", "SSE.Views.FormatSettingsDialog.textSeparator": "Usar separador 1.000", "SSE.Views.FormatSettingsDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "Formato de número", "SSE.Views.FormatSettingsDialog.txtAccounting": "Contabilidade", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON> décimos (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON> centésimos (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Em décimo sexto (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Em metades (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON> quartos (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Em oitavos (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Personalizar", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Insira o formato de número personalizado com cuidado. O Editor de planilhas não verifica os formatos personalizados em busca de erros que possam afetar o arquivo xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Data", "SSE.Views.FormatSettingsDialog.txtFraction": "Fração", "SSE.Views.FormatSettingsDialog.txtGeneral": "G<PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "Número", "SSE.Views.FormatSettingsDialog.txtPercentage": "Porcentagem", "SSE.Views.FormatSettingsDialog.txtSample": "Amos<PERSON>:", "SSE.Views.FormatSettingsDialog.txtScientific": "Científico", "SSE.Views.FormatSettingsDialog.txtText": "Тexto", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Até um dígito (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Até dois dígitos (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON> tr<PERSON><PERSON> (131/135)", "SSE.Views.FormulaDialog.sDescription": "Descrição", "SSE.Views.FormulaDialog.textGroupDescription": "Selecionar grupo de função", "SSE.Views.FormulaDialog.textListDescription": "Selecionar função", "SSE.Views.FormulaDialog.txtRecommended": "Recomendado", "SSE.Views.FormulaDialog.txtSearch": "Procurar", "SSE.Views.FormulaDialog.txtTitle": "Inserir função", "SSE.Views.FormulaTab.textAutomatic": "Automático", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calcular planilha atual", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calcular pasta de trabalho", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Calcular", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calcular a pasta de trabalho inteira", "SSE.Views.FormulaTab.tipWatch": "Adicionar c<PERSON>lulas à lista da Janela de observação", "SSE.Views.FormulaTab.txtAdditional": "Adicional", "SSE.Views.FormulaTab.txtAutosum": "Soma automática", "SSE.Views.FormulaTab.txtAutosumTip": "So<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Função", "SSE.Views.FormulaTab.txtFormulaTip": "Inserir função", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON> formatos", "SSE.Views.FormulaTab.txtRecent": "Usado recentemente", "SSE.Views.FormulaTab.txtWatch": "Janela de observação", "SSE.Views.FormulaWizard.textAny": "<PERSON>ual<PERSON>", "SSE.Views.FormulaWizard.textArgument": "Argumento", "SSE.Views.FormulaWizard.textFunction": "Função", "SSE.Views.FormulaWizard.textFunctionRes": "Resultado da função", "SSE.Views.FormulaWizard.textHelp": "<PERSON><PERSON><PERSON> nesta função", "SSE.Views.FormulaWizard.textLogical": "Lógico", "SSE.Views.FormulaWizard.textNoArgs": "Esta função não tem argumentos", "SSE.Views.FormulaWizard.textNumber": "Número", "SSE.Views.FormulaWizard.textRef": "Referência", "SSE.Views.FormulaWizard.textText": "Тexto", "SSE.Views.FormulaWizard.textTitle": "Argumentos de função", "SSE.Views.FormulaWizard.textValue": "Resultado da fórmula", "SSE.Views.HeaderFooterDialog.textAlign": "<PERSON>nhar <PERSON> as ma<PERSON><PERSON> <PERSON><PERSON>a", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON> as p<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textBold": "Negrito", "SSE.Views.HeaderFooterDialog.textCenter": "Centro", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON>r de texto", "SSE.Views.HeaderFooterDialog.textDate": "Data", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Primeira página diferente", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Páginas ímpares e pares diferentes", "SSE.Views.HeaderFooterDialog.textEven": "Página par", "SSE.Views.HeaderFooterDialog.textFileName": "Nome do arquivo", "SSE.Views.HeaderFooterDialog.textFirst": "Primeira página", "SSE.Views.HeaderFooterDialog.textFooter": "Rodapé", "SSE.Views.HeaderFooterDialog.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textInsert": "Inserir", "SSE.Views.HeaderFooterDialog.textItalic": "Itálico", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "A sequência de texto que você inseriu é muito longa. Reduza o número de caracteres usados.", "SSE.Views.HeaderFooterDialog.textNewColor": "Nova cor personalizada", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "Número de páginas", "SSE.Views.HeaderFooterDialog.textPageNum": "Número da página", "SSE.Views.HeaderFooterDialog.textPresets": "Predefinições", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Escala com documento", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON>me da folha", "SSE.Views.HeaderFooterDialog.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSubscript": "Subscrito", "SSE.Views.HeaderFooterDialog.textSuperscript": "Sobrescrito", "SSE.Views.HeaderFooterDialog.textTime": "Tempo", "SSE.Views.HeaderFooterDialog.textTitle": "Configurações de Cabeçalho/Rodapé", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "Fonte", "SSE.Views.HeaderFooterDialog.tipFontSize": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Vincular a", "SSE.Views.HyperlinkSettingsDialog.strRange": "Intervalo", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Fol<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copiar", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Intervalo selecionado", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Inserir legenda aqui", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Inserir link aqui", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserir dica de ferramenta aqui", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Link externo", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Obter link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Intervalo de dados interno", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON> definidos", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Selecionar dados", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Fol<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Texto da dica de tela", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Configurações de hiperlink", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo é limitado a 2083 caracteres. ", "SSE.Views.ImageSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "SSE.Views.ImageSettings.textCrop": "Cortar", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Ajustar", "SSE.Views.ImageSettings.textCropToShape": "Cortar para dar forma", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "Do Arquivo", "SSE.Views.ImageSettings.textFromStorage": "De armazenamento", "SSE.Views.ImageSettings.textFromUrl": "Da URL", "SSE.Views.ImageSettings.textHeight": "Altura", "SSE.Views.ImageSettings.textHint270": "Girar 90° no sentido anti-horário", "SSE.Views.ImageSettings.textHint90": "Girar 90° no sentido horário", "SSE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "SSE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "SSE.Views.ImageSettings.textInsert": "Substituir imagem", "SSE.Views.ImageSettings.textKeepRatio": "Proporções constantes", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "Usado recentemente", "SSE.Views.ImageSettings.textRotate90": "Girar 90°", "SSE.Views.ImageSettings.textRotation": "Rotação", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Não mova ou dimensione com células", "SSE.Views.ImageSettingsAdvanced.textAlt": "Texto Alternativo", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.ImageSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Virado", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Mover, mas não dimensionar, com células", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotação", "SSE.Views.ImageSettingsAdvanced.textSnap": "Captura de células", "SSE.Views.ImageSettingsAdvanced.textTitle": "Imagem - Configurações avançadas", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Mover e dimensionar com células", "SSE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ImportFromXmlDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, onde colocar os dados", "SSE.Views.ImportFromXmlDialog.textExist": "<PERSON><PERSON><PERSON> existente", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.ImportFromXmlDialog.textNew": "Nova planilha", "SSE.Views.ImportFromXmlDialog.textSelectData": "Selecionar dados", "SSE.Views.ImportFromXmlDialog.textTitle": "Importar dados", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.LeftMenu.tipAbout": "Sobre", "SSE.Views.LeftMenu.tipChat": "Gráfico", "SSE.Views.LeftMenu.tipComments": "Comentários", "SSE.Views.LeftMenu.tipFile": "Arquivo", "SSE.Views.LeftMenu.tipPlugins": "Plug-ins", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Verificação ortográfica", "SSE.Views.LeftMenu.tipSupport": "Feedback e Suporte", "SSE.Views.LeftMenu.txtDeveloper": "MODO DE DESENVOLVEDOR", "SSE.Views.LeftMenu.txtEditor": "Editor <PERSON> <PERSON><PERSON>", "SSE.Views.LeftMenu.txtLimit": "Limitar o acesso", "SSE.Views.LeftMenu.txtTrial": "MODO DE TESTE", "SSE.Views.LeftMenu.txtTrialDev": "<PERSON><PERSON> desen<PERSON>or de teste", "SSE.Views.MacroDialog.textMacro": "Nome da macro", "SSE.Views.MacroDialog.textTitle": "Atribuir Macro", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "Inferior", "SSE.Views.MainSettingsPrint.strLandscape": "Paisagem", "SSE.Views.MainSettingsPrint.strLeft": "E<PERSON>rda", "SSE.Views.MainSettingsPrint.strMargins": "Margens", "SSE.Views.MainSettingsPrint.strPortrait": "Retrato", "SSE.Views.MainSettingsPrint.strPrint": "Imprimir", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Parte superior", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "Personalizado", "SSE.Views.MainSettingsPrint.textCustomOptions": "Opções personalizadas", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON><PERSON> as colu<PERSON> em uma página", "SSE.Views.MainSettingsPrint.textFitPage": "Ajustar folha em uma página", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON><PERSON> as linhas em uma página", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientação da página", "SSE.Views.MainSettingsPrint.textPageScaling": "Dimensionar", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Imprimir linhas de grade", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Imprimir títulos de linha e coluna", "SSE.Views.MainSettingsPrint.textRepeat": "Repetir...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "<PERSON><PERSON> as colunas à esquerda", "SSE.Views.MainSettingsPrint.textRepeatTop": "<PERSON><PERSON> as linhas no topo", "SSE.Views.MainSettingsPrint.textSettings": "Configurações para", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Os intervalos nomeados existentes não podem ser editados e os novos não podem ser criados <br> no momento, pois alguns deles estão sendo editados.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defined name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Manual", "SSE.Views.NamedRangeEditDlg.textDataRange": "Intervalo de dados", "SSE.Views.NamedRangeEditDlg.textExistName": "ERROR! Range with such a name already exists", "SSE.Views.NamedRangeEditDlg.textInvalidName": "ERROR! Invalid range name", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERROR! Invalid cell range", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERROR! This element is being edited by another user.", "SSE.Views.NamedRangeEditDlg.textName": "Name", "SSE.Views.NamedRangeEditDlg.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Selecionar dados", "SSE.Views.NamedRangeEditDlg.txtEmpty": "This field is required", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Editar nome", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Novo nome", "SSE.Views.NamedRangePasteDlg.textNames": "Intervalos nomeados", "SSE.Views.NamedRangePasteDlg.txtTitle": "Nome da pasta", "SSE.Views.NameManagerDlg.closeButtonText": "Close", "SSE.Views.NameManagerDlg.guestText": "Guest", "SSE.Views.NameManagerDlg.lockText": "Bloqueado", "SSE.Views.NameManagerDlg.textDataRange": "Intervalo de dados", "SSE.Views.NameManagerDlg.textDelete": "Delete", "SSE.Views.NameManagerDlg.textEdit": "Edit", "SSE.Views.NameManagerDlg.textEmpty": "Nenhum intervalo nomeado foi criado ainda. <br> <PERSON>rie pelo menos um intervalo nomeado e ele aparecerá neste campo.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "All", "SSE.Views.NameManagerDlg.textFilterDefNames": "Defined names", "SSE.Views.NameManagerDlg.textFilterSheet": "Nomes com escopo para planilha", "SSE.Views.NameManagerDlg.textFilterTableNames": "Table names", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Nomes com escopo para pasta de trabalho", "SSE.Views.NameManagerDlg.textNew": "New", "SSE.Views.NameManagerDlg.textnoNames": "Nenhum intervalo nomeado correspondente ao seu filtro foi encontrado.", "SSE.Views.NameManagerDlg.textRanges": "Intervalos Nomeadas", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Manual", "SSE.Views.NameManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.NameManagerDlg.txtTitle": "Gerenciador de nomes", "SSE.Views.NameManagerDlg.warnDelete": "Tem certeza de que deseja excluir o nome {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Inferior", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "Margens", "SSE.Views.PageMarginsDialog.textTop": "Superior", "SSE.Views.ParagraphSettings.strLineHeight": "Espaçamento de linha", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Espaçamento", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "SSE.Views.ParagraphSettings.textAt": "Em", "SSE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Exatamente", "SSE.Views.ParagraphSettings.txtAutoText": "Automático", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "As abas especificadas aparecerão neste campo", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON> ma<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaçamento entre linhas", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "depois", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Por", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fonte", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Recuos e Posicionamento", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Espaçamento", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Taxado", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Aba", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Alinhamento", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espaçamento entre caracteres", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Aba padrão", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efeitos", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exatamente", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira linha", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Suspensão", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(<PERSON>enhum)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Remover", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Excluir todos", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centro", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posição da aba", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Configurações avançadas", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "É igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "Não termina com", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "Não contém", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "entre", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "Não entre...", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "Não é igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "É maior do que", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "É maior ou igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "É menor que", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "É menor ou igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "Começa com", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "Não começa com", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "Termina com", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Mostrar itens para os quais o rótulo:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Mostrar itens para os quais:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Usar ? apresentar qualquer um caractere", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Use * para apresentar qualquer série de caracteres", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "E", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtro de rótulos", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtro de valor", "SSE.Views.PivotGroupDialog.textAuto": "auto", "SSE.Views.PivotGroupDialog.textBy": "por", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "Terminando em", "SSE.Views.PivotGroupDialog.textError": "Este campo deve ser um valor numérico", "SSE.Views.PivotGroupDialog.textGreaterError": "O número final deve ser maior do que o número inicial", "SSE.Views.PivotGroupDialog.textHour": "horas", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "Meses", "SSE.Views.PivotGroupDialog.textNumDays": "Número de <PERSON>as", "SSE.Views.PivotGroupDialog.textQuart": "Trimestres", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "Começando em", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Agrupamento", "SSE.Views.PivotSettings.textAdvanced": "Mostrar configurações avançadas", "SSE.Views.PivotSettings.textColumns": "Colunas", "SSE.Views.PivotSettings.textFields": "Selecionar campos", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Valores", "SSE.Views.PivotSettings.txtAddColumn": "Adicionar <PERSON> colu<PERSON>", "SSE.Views.PivotSettings.txtAddFilter": "Adicionar aos filtros", "SSE.Views.PivotSettings.txtAddRow": "Adicionar <PERSON> l<PERSON>has", "SSE.Views.PivotSettings.txtAddValues": "Adicionar a valores", "SSE.Views.PivotSettings.txtFieldSettings": "Configurações de campo", "SSE.Views.PivotSettings.txtMoveBegin": "Mover para o início", "SSE.Views.PivotSettings.txtMoveColumn": "Mover para colunas", "SSE.Views.PivotSettings.txtMoveDown": "Mover para baixo", "SSE.Views.PivotSettings.txtMoveEnd": "Mover para o fim", "SSE.Views.PivotSettings.txtMoveFilter": "Mover para filtros", "SSE.Views.PivotSettings.txtMoveRow": "Mover para linhas", "SSE.Views.PivotSettings.txtMoveUp": "Mover para cima", "SSE.Views.PivotSettings.txtMoveValues": "Mover para valores", "SSE.Views.PivotSettings.txtRemove": "Remover campo", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nome e Layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Texto Alternativo", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.PivotSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto das informações do objeto visual, que serão lidas às pessoas com deficiência visual ou cognitiva para ajudá-las a entender melhor quais informações existem na imagem, na forma automática, no gráfico ou na tabela.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Ajustar automaticamente a largura das colunas ao atualizar", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Intervalo de dados", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Fonte de dados", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Exibir campos na área de filtro de relatório", "SSE.Views.PivotSettingsAdvanced.textDown": "Abaixo, depois", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Totais gerais", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Cabeçalhos de campo", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.PivotSettingsAdvanced.textOver": "Acabou, depois caiu", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Selecionar dados", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Mostrar para colunas", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Mostrar cabeçalhos de campo para linhas e colunas", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Mostrar para linhas", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tabela dinâmica - Configurações avançadas", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Campos de filtro de relatório por coluna", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Campos de filtro de relatório por linha", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Este campo é obrigatório", "SSE.Views.PivotSettingsAdvanced.txtName": "Nome", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON> em branco", "SSE.Views.PivotTable.capGrandTotals": "Totais gerais", "SSE.Views.PivotTable.capLayout": "Layout do relatório", "SSE.Views.PivotTable.capSubtotals": "Subtotais", "SSE.Views.PivotTable.mniBottomSubtotals": "Mostrar todos os subtotais na parte inferior do grupo", "SSE.Views.PivotTable.mniInsertBlankLine": "Inserir linha em branco após cada item", "SSE.Views.PivotTable.mniLayoutCompact": "Mostrar em formato compacto", "SSE.Views.PivotTable.mniLayoutNoRepeat": "<PERSON>ão repita todas as etiquetas de itens", "SSE.Views.PivotTable.mniLayoutOutline": "Mostrar no formulário de estrutura de tópicos", "SSE.Views.PivotTable.mniLayoutRepeat": "<PERSON><PERSON><PERSON> to<PERSON> as etiquetas de itens", "SSE.Views.PivotTable.mniLayoutTabular": "Mostrar em forma de tabela", "SSE.Views.PivotTable.mniNoSubtotals": "Não mostrar subtotais", "SSE.Views.PivotTable.mniOffTotals": "Desativado para linhas e colunas", "SSE.Views.PivotTable.mniOnColumnsTotals": "Ativado apenas para colunas", "SSE.Views.PivotTable.mniOnRowsTotals": "Ativado apenas para linhas", "SSE.Views.PivotTable.mniOnTotals": "Ativado para linhas e colunas", "SSE.Views.PivotTable.mniRemoveBlankLine": "Remover linha em branco após cada item", "SSE.Views.PivotTable.mniTopSubtotals": "Mostrar todos os subtotais na parte superior do grupo", "SSE.Views.PivotTable.textColBanded": "Colunas com faixas", "SSE.Views.PivotTable.textColHeader": "Cabeçalhos de coluna", "SSE.Views.PivotTable.textRowBanded": "Linhas em faixas", "SSE.Views.PivotTable.textRowHeader": "Cabeçalhos de linha", "SSE.Views.PivotTable.tipCreatePivot": "Inserir tabela dinâ<PERSON>a", "SSE.Views.PivotTable.tipGrandTotals": "<PERSON><PERSON> ou ocultar totais gerais", "SSE.Views.PivotTable.tipRefresh": "Atualize as informações da fonte de dados", "SSE.Views.PivotTable.tipRefreshCurrent": "<PERSON><PERSON><PERSON><PERSON> as inform<PERSON>ç<PERSON><PERSON> da fonte de dados para a tabela atual", "SSE.Views.PivotTable.tipSelect": "Selecionar tabela dinâmica inteira", "SSE.Views.PivotTable.tipSubtotals": "Mostrar ou ocultar subtotais", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Personalizado", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Escuro", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Médio", "SSE.Views.PivotTable.txtPivotTable": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefreshAll": "Atualize tudo", "SSE.Views.PivotTable.txtSelect": "Selecionar", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table de estilo escuro", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table de estilo claro", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot table de estilo médio", "SSE.Views.PrintSettings.btnDownload": "Salvar & Transferir", "SSE.Views.PrintSettings.btnPrint": "Salvar e Imprimir", "SSE.Views.PrintSettings.strBottom": "Inferior", "SSE.Views.PrintSettings.strLandscape": "Paisagem", "SSE.Views.PrintSettings.strLeft": "E<PERSON>rda", "SSE.Views.PrintSettings.strMargins": "Margens", "SSE.Views.PrintSettings.strPortrait": "Retrato", "SSE.Views.PrintSettings.strPrint": "Imprimir", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Mostrar", "SSE.Views.PrintSettings.strTop": "Parte superior", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON> atual", "SSE.Views.PrintSettings.textCustom": "Personalizado", "SSE.Views.PrintSettings.textCustomOptions": "Opções personalizadas", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON><PERSON> as colu<PERSON> em uma página", "SSE.Views.PrintSettings.textFitPage": "Ajustar folha em uma página", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON><PERSON> as linhas em uma página", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorar <PERSON>", "SSE.Views.PrintSettings.textLayout": "Layout", "SSE.Views.PrintSettings.textPageOrientation": "Orientação da página", "SSE.Views.PrintSettings.textPageScaling": "Dimensionar", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintGrid": "Imprimir linhas de grade", "SSE.Views.PrintSettings.textPrintHeadings": "Imprimir títulos de linha e coluna", "SSE.Views.PrintSettings.textPrintRange": "Imprimir intervalo", "SSE.Views.PrintSettings.textRange": "Intervalo", "SSE.Views.PrintSettings.textRepeat": "Repetir...", "SSE.Views.PrintSettings.textRepeatLeft": "<PERSON><PERSON> as colunas à esquerda", "SSE.Views.PrintSettings.textRepeatTop": "<PERSON><PERSON> as linhas no topo", "SSE.Views.PrintSettings.textSelection": "Se<PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Configurações da folha", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "Mostrar linhas de grade", "SSE.Views.PrintSettings.textShowHeadings": "Mostrar títulos de linhas e colunas", "SSE.Views.PrintSettings.textTitle": "Configurações de impressão", "SSE.Views.PrintSettings.textTitlePDF": "Configurações de PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "Primeira coluna", "SSE.Views.PrintTitlesDialog.textFirstRow": "Primeira linha", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Colunas congeladas", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON> congeladas", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.PrintTitlesDialog.textLeft": "<PERSON><PERSON> as colunas à esquerda", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Não repetir", "SSE.Views.PrintTitlesDialog.textRepeat": "Repetir...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Selecionar intervalo", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTop": "<PERSON><PERSON> as linhas no topo", "SSE.Views.PrintWithPreview.txtActualSize": "<PERSON><PERSON><PERSON> at<PERSON>", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Aplicar a todas as planilhas", "SSE.Views.PrintWithPreview.txtBottom": "Inferior", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON> atual", "SSE.Views.PrintWithPreview.txtCustom": "Personalizar", "SSE.Views.PrintWithPreview.txtCustomOptions": "Opções personalizadas", "SSE.Views.PrintWithPreview.txtEmptyTable": "Não há nada para imprimir porque a tabela está vazia", "SSE.Views.PrintWithPreview.txtFitCols": "<PERSON><PERSON><PERSON> as colu<PERSON> em uma página", "SSE.Views.PrintWithPreview.txtFitPage": "Ajustar folha em uma página", "SSE.Views.PrintWithPreview.txtFitRows": "<PERSON><PERSON><PERSON> as linhas em uma página", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Linhas de grade e títulos", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Configurações de Cabeçalho/Rodapé", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorar <PERSON>", "SSE.Views.PrintWithPreview.txtLandscape": "Paisagem", "SSE.Views.PrintWithPreview.txtLeft": "E<PERSON>rda", "SSE.Views.PrintWithPreview.txtMargins": "Margens", "SSE.Views.PrintWithPreview.txtOf": "de {0}", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Número da página inválido", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientação da página", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPortrait": "Retrato ", "SSE.Views.PrintWithPreview.txtPrint": "Imprimir", "SSE.Views.PrintWithPreview.txtPrintGrid": "Imprimir linhas de grade", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Imprimir títulos de linhas e colunas", "SSE.Views.PrintWithPreview.txtPrintRange": "Imprimir intervalo", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtRepeat": "Repetir...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "<PERSON><PERSON> as colunas à esquerda", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "<PERSON><PERSON> as linhas no topo", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtScaling": "Dimensionar", "SSE.Views.PrintWithPreview.txtSelection": "Se<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Configurações da planilha", "SSE.Views.PrintWithPreview.txtSheet": "Planilha: {0}", "SSE.Views.PrintWithPreview.txtTop": "Parte superior", "SSE.Views.ProtectDialog.textExistName": "ERRO! Já existe um intervalo com esse título", "SSE.Views.ProtectDialog.textInvalidName": "O título do intervalo deve começar com uma letra e pode conter apenas letras, números e espaços.", "SSE.Views.ProtectDialog.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.ProtectDialog.textSelectData": "Selecionar dados", "SSE.Views.ProtectDialog.txtAllow": "<PERSON><PERSON><PERSON> que todos os usuários desta planilha", "SSE.Views.ProtectDialog.txtAutofilter": "Usar AutoFiltro", "SSE.Views.ProtectDialog.txtDelCols": "Apa<PERSON> colunas", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON> linhas", "SSE.Views.ProtectDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.ProtectDialog.txtFormatCells": "Formatar celulas", "SSE.Views.ProtectDialog.txtFormatCols": "Colunas de formato", "SSE.Views.ProtectDialog.txtFormatRows": "Formatar linhas", "SSE.Views.ProtectDialog.txtIncorrectPwd": "A confirmação da senha não é idêntica", "SSE.Views.ProtectDialog.txtInsCols": "Inserir colunas", "SSE.Views.ProtectDialog.txtInsHyper": "Insira o hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Views.ProtectDialog.txtObjs": "<PERSON><PERSON> objet<PERSON>", "SSE.Views.ProtectDialog.txtOptional": "Opcional", "SSE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Usar Tabela Dinâmica e Gráfico Dinâmico", "SSE.Views.ProtectDialog.txtProtect": "Proteger", "SSE.Views.ProtectDialog.txtRange": "Intervalo", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON> a senha", "SSE.Views.ProtectDialog.txtScen": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "Selecione células bloqueadas", "SSE.Views.ProtectDialog.txtSelUnLocked": "Selecione células <PERSON>", "SSE.Views.ProtectDialog.txtSheetDescription": "Evite alterações indesejadas de outras pessoas, limitando sua capacidade de edição.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON>te<PERSON> folha", "SSE.Views.ProtectDialog.txtSort": "Classificar", "SSE.Views.ProtectDialog.txtWarning": "Cuidado: se você perder ou esquecer a senha, não será possível recuperá-la. Guarde-o em local seguro.", "SSE.Views.ProtectDialog.txtWBDescription": "Para evitar que outros usuários vejam planilhas ocultas, adicionando, movendo, excluindo ou ocultando planilhas e renomeando planilhas, você pode proteger a estrutura de sua pasta de trabalho com uma senha.", "SSE.Views.ProtectDialog.txtWBTitle": "Proteger a estrutura da pasta de trabalho", "SSE.Views.ProtectRangesDlg.guestText": "Convidado(a)", "SSE.Views.ProtectRangesDlg.lockText": "Bloqueado", "SSE.Views.ProtectRangesDlg.textDelete": "Excluir", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Nenhum intervalo permitido para edição.", "SSE.Views.ProtectRangesDlg.textNew": "Novo", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON>te<PERSON> folha", "SSE.Views.ProtectRangesDlg.textPwd": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Intervalo", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Intervalos desbloqueados por senha quando a planilha está protegida (isso funciona apenas para células bloqueadas).", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Este elemento está sendo editado por outro usuário.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON>o", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nova Classificação", "SSE.Views.ProtectRangesDlg.txtNo": "Não", "SSE.Views.ProtectRangesDlg.txtTitle": "Permitir que os usuários editem intervalos", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON>m", "SSE.Views.ProtectRangesDlg.warnDelete": "Tem certeza de que deseja excluir o nome {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Colunas", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Para excluir valores duplicados, selecione uma ou mais colunas que contêm duplicatas.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Meus dados têm cabeçalhos", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Selecionar todos", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Remover Duplicatas", "SSE.Views.RightMenu.txtCellSettings": "Configurações de célula", "SSE.Views.RightMenu.txtChartSettings": "Configurações do gráfico", "SSE.Views.RightMenu.txtImageSettings": "Configurações de imagem", "SSE.Views.RightMenu.txtParagraphSettings": "Configurações do parágrafo", "SSE.Views.RightMenu.txtPivotSettings": "Configurações de tabela dinâmica", "SSE.Views.RightMenu.txtSettings": "Configurações comuns", "SSE.Views.RightMenu.txtShapeSettings": "Configurações da forma", "SSE.Views.RightMenu.txtSignatureSettings": "Configurações de assinatura", "SSE.Views.RightMenu.txtSlicerSettings": "Config. da segmentação de dados", "SSE.Views.RightMenu.txtSparklineSettings": "Configurações de minigráfico", "SSE.Views.RightMenu.txtTableSettings": "Configurações da tabela", "SSE.Views.RightMenu.txtTextArtSettings": "Configurações de arte de texto", "SSE.Views.ScaleDialog.textAuto": "Automático", "SSE.Views.ScaleDialog.textError": "O valor inserido está incorreto.", "SSE.Views.ScaleDialog.textFewPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Ajustar a", "SSE.Views.ScaleDialog.textHeight": "Altura", "SSE.Views.ScaleDialog.textManyPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Dimensionar para", "SSE.Views.ScaleDialog.textTitle": "Configurações de escala", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "O valor máximo para este campo é {0}", "SSE.Views.SetValueDialog.txtMinText": "O valor mínimo para este campo é {0}", "SSE.Views.ShapeSettings.strBackground": "Cor do plano de fundo", "SSE.Views.ShapeSettings.strChange": "Alterar forma automática", "SSE.Views.ShapeSettings.strColor": "Cor", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Cor do plano de fundo", "SSE.Views.ShapeSettings.strPattern": "Padrão", "SSE.Views.ShapeSettings.strShadow": "Mostrar sombra", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Opacidade", "SSE.Views.ShapeSettings.strType": "Tipo", "SSE.Views.ShapeSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "O valor inserido está incorreto.<br>Insira um valor entre 0 pt e 1.584 pt.", "SSE.Views.ShapeSettings.textColor": "Preenchimento de cor", "SSE.Views.ShapeSettings.textDirection": "Direção", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "Do Arquivo", "SSE.Views.ShapeSettings.textFromStorage": "De armazenamento", "SSE.Views.ShapeSettings.textFromUrl": "Da URL", "SSE.Views.ShapeSettings.textGradient": "Pontos de gradiente", "SSE.Views.ShapeSettings.textGradientFill": "Preenchimento gradiente", "SSE.Views.ShapeSettings.textHint270": "Girar 90° no sentido anti-horário", "SSE.Views.ShapeSettings.textHint90": "Girar 90° no sentido horário", "SSE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "SSE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "SSE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "SSE.Views.ShapeSettings.textLinear": "Linear", "SSE.Views.ShapeSettings.textNoFill": "Sem preenchimento", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON> original", "SSE.Views.ShapeSettings.textPatternFill": "Padrão", "SSE.Views.ShapeSettings.textPosition": "Posição", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Usado recentemente", "SSE.Views.ShapeSettings.textRotate90": "Girar 90°", "SSE.Views.ShapeSettings.textRotation": "Rotação", "SSE.Views.ShapeSettings.textSelectImage": "Selecionar imagem", "SSE.Views.ShapeSettings.textSelectTexture": "Selecionar", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Da Textura", "SSE.Views.ShapeSettings.textTile": "Lado a lado", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON>pel pardo", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "Papelão", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "SSE.Views.ShapeSettings.txtGrain": "Granulação", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Papel cinza", "SSE.Views.ShapeSettings.txtKnit": "Encontro", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> linha", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Madeira", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Colunas", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Preenchimento de texto", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Não mova ou dimensione com células", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Texto Alternativo", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Set<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Ajuste automático", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> inicial", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Inferior", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Número de <PERSON>nas", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Tamanho final", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Virado", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de junção", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporções constantes", "SSE.Views.ShapeSettingsAdvanced.textLeft": "E<PERSON>rda", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Malhete", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Mover, mas não dimensionar, com células", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Permitir que o texto exceda a forma", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Redimensionar forma para caber no texto", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotação", "SSE.Views.ShapeSettingsAdvanced.textRound": "Rodada", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Captura de células", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Espaçamento entre colunas", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Quadrado", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Caixa de texto", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma - Configurações avançadas", "SSE.Views.ShapeSettingsAdvanced.textTop": "Parte superior", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Mover e dimensionar com células", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Pesos e Setas", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Atenção", "SSE.Views.SignatureSettings.strDelete": "Remover assinatura", "SSE.Views.SignatureSettings.strDetails": "Detalhes da assinatura", "SSE.Views.SignatureSettings.strInvalid": "Assinaturas inválidas", "SSE.Views.SignatureSettings.strRequested": "Assinaturas solicitadas", "SSE.Views.SignatureSettings.strSetup": "Configuração de assinatura", "SSE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSignature": "Assinatura", "SSE.Views.SignatureSettings.strSigner": "Signatário", "SSE.Views.SignatureSettings.strValid": "Assinaturas válidas", "SSE.Views.SignatureSettings.txtContinueEditing": "<PERSON>e mesmo assim", "SSE.Views.SignatureSettings.txtEditWarning": "A edição removerá as assinaturas da planilha.<br>Tem certeza de que deseja continuar?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Você quer remover esta assinatura? <br><PERSON><PERSON> não pode ser desfeito.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Esta planilha precisa ser assinada.", "SSE.Views.SignatureSettings.txtSigned": "Assinaturas válidas foram adicionadas à planilha. A planilha está protegida contra edição.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Algumas assinaturas digitais da planilha são inválidas ou não puderam ser verificadas. A planilha está protegida contra edição.", "SSE.Views.SlicerAddDialog.textColumns": "Colunas", "SSE.Views.SlicerAddDialog.txtTitle": "Inserir Segmentação de Dados", "SSE.Views.SlicerSettings.strHideNoData": "O<PERSON>ltar itens sem dados", "SSE.Views.SlicerSettings.strIndNoData": "Itens indicados visualmente sem dados", "SSE.Views.SlicerSettings.strShowDel": "Mostrar itens excluídos da fonte de dados", "SSE.Views.SlicerSettings.strShowNoData": "Mostrar itens sem dados por último", "SSE.Views.SlicerSettings.strSorting": "Classificação e filtragem", "SSE.Views.SlicerSettings.textAdvanced": "Mostrar configurações avançadas", "SSE.Views.SlicerSettings.textAsc": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textAZ": "De A a Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "Colunas", "SSE.Views.SlicerSettings.textDesc": "Descendente", "SSE.Views.SlicerSettings.textHeight": "Altura", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Proporções constantes", "SSE.Views.SlicerSettings.textLargeSmall": "Do maior para o menor", "SSE.Views.SlicerSettings.textLock": "Desativar o redimensionamento ou a movimentação", "SSE.Views.SlicerSettings.textNewOld": "Do mais novo ao mais antigo", "SSE.Views.SlicerSettings.textOldNew": "Do mais antigo ao mais novo", "SSE.Views.SlicerSettings.textPosition": "Posição", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "do menor para o maior", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z até A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Colunas", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Altura", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "O<PERSON>ltar itens sem dados", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Itens indicados visualmente sem dados", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Referências", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Mostrar itens excluídos da fonte de dados", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Cabeçalho de exibição", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Mostrar itens sem dados por último", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Classificação e filtragem", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Estilo e tamanho", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Não mova ou dimensione com células", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Texto Alternativo", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação do objeto visual, que será lida para as pessoas com deficiência visual ou cognitiva para ajudá-las a entender melhor quais informações existem na imagem, forma automática, gráfico ou tabela.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAZ": "De A a Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Decrescente", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Nome para usar em fórmulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Proporções constantes", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "Do maior para o menor", "SSE.Views.SlicerSettingsAdvanced.textName": "Nome", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "Do mais novo ao mais antigo", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "Do mais antigo ao mais novo", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Mover, mas não dimensione com as c<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "do menor para o maior", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Captura de células", "SSE.Views.SlicerSettingsAdvanced.textSort": "Classificar", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON>me da fonte", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Segmentação de Dados- Config. avançadas", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Mover e dimensionar com células", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z até A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Este campo é obrigatório", "SSE.Views.SortDialog.errorEmpty": "Todos os critérios de classificação devem ter uma coluna ou linha especificada.", "SSE.Views.SortDialog.errorMoreOneCol": "Mais de uma coluna está selecionada.", "SSE.Views.SortDialog.errorMoreOneRow": "Mais de uma linha está selecionada.", "SSE.Views.SortDialog.errorNotOriginalCol": "A coluna que você selecionou não está no intervalo selecionado original.", "SSE.Views.SortDialog.errorNotOriginalRow": "A linha que você selecionou não está no intervalo original selecionado.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 está sendo classificado pela mesma cor mais de uma vez. <br> Exclua os critérios de classificação duplicados e tente novamente.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 está sendo classificado por valores mais de uma vez. <br> Exclua os critérios de classificação duplicados e tente novamente.", "SSE.Views.SortDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textAsc": "<PERSON><PERSON>", "SSE.Views.SortDialog.textAuto": "Automático", "SSE.Views.SortDialog.textAZ": "De A a Z", "SSE.Views.SortDialog.textBelow": "Abaixo", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> <PERSON>", "SSE.Views.SortDialog.textColumn": "Coluna", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDelete": "Excluir nível", "SSE.Views.SortDialog.textDesc": "Descendente", "SSE.Views.SortDialog.textDown": "Mover nível para baixo", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON> da fonte", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON> co<PERSON> ...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON> lin<PERSON> ...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Opções", "SSE.Views.SortDialog.textOrder": "Ordem", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Classificar em", "SSE.Views.SortDialog.textSortBy": "Ordenar por", "SSE.Views.SortDialog.textThenBy": "<PERSON> seguida, por", "SSE.Views.SortDialog.textTop": "Superior", "SSE.Views.SortDialog.textUp": "Subir de nível", "SSE.Views.SortDialog.textValues": "Valores", "SSE.Views.SortDialog.textZA": "Z até A", "SSE.Views.SortDialog.txtInvalidRange": "Intervalo de células inválido.", "SSE.Views.SortDialog.txtTitle": "Ordenar", "SSE.Views.SortFilterDialog.textAsc": "Ascendente (de A a Z) por", "SSE.Views.SortFilterDialog.textDesc": "Descendo (Z a A) por", "SSE.Views.SortFilterDialog.txtTitle": "Classificar", "SSE.Views.SortOptionsDialog.textCase": "Maiúsculas e Minúsculas", "SSE.Views.SortOptionsDialog.textHeaders": "Meus dados têm cabeçalhos", "SSE.Views.SortOptionsDialog.textLeftRight": "Classificar da esquerda para a direita", "SSE.Views.SortOptionsDialog.textOrientation": "Orientação", "SSE.Views.SortOptionsDialog.textTitle": "Opções de classificação", "SSE.Views.SortOptionsDialog.textTopBottom": "Classificar de cima para baixo", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "Todos", "SSE.Views.SpecialPasteDialog.textBlanks": "Pular espaços em branco", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON><PERSON><PERSON> coluna", "SSE.Views.SpecialPasteDialog.textComments": "Comentários", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Fórmulas e formatação", "SSE.Views.SpecialPasteDialog.textFNFormat": "Fórmulas e formatos numéricos", "SSE.Views.SpecialPasteDialog.textFormats": "Formatos", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "Fórmulas e larguras de coluna", "SSE.Views.SpecialPasteDialog.textMult": "Multiplicar", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operação", "SSE.Views.SpecialPasteDialog.textPaste": "Colar", "SSE.Views.SpecialPasteDialog.textSub": "Subtrair", "SSE.Views.SpecialPasteDialog.textTitle": "Colar Especial", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpor", "SSE.Views.SpecialPasteDialog.textValues": "Valores", "SSE.Views.SpecialPasteDialog.textVFormat": "Valores e formatação", "SSE.Views.SpecialPasteDialog.textVNFormat": "Valores e formatos numéricos", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON><PERSON>, exceto as bordas", "SSE.Views.Spellcheck.noSuggestions": "Sem sugestões de ortografia", "SSE.Views.Spellcheck.textChange": "Alterar", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON>r tudo", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "Ignore tudo", "SSE.Views.Spellcheck.txtAddToDictionary": "Adicionar ao dicionário", "SSE.Views.Spellcheck.txtClosePanel": "Fechar ortografia", "SSE.Views.Spellcheck.txtComplete": "A verificação ortográfica foi concluída", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Idioma do dicionário", "SSE.Views.Spellcheck.txtNextTip": "Vá para a próxima palavra", "SSE.Views.Spellcheck.txtSpelling": "Ortografia", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON><PERSON> para o final)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Mover para o final)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "<PERSON><PERSON><PERSON> antes da folha", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Mover antes da folha", "SSE.Views.Statusbar.filteredRecordsText": "{0} de {1} registros filtrados", "SSE.Views.Statusbar.filteredText": "<PERSON>do de filtro", "SSE.Views.Statusbar.itemAverage": "Média", "SSE.Views.Statusbar.itemCopy": "Copiar", "SSE.Views.Statusbar.itemCount": "Contagem", "SSE.Views.Statusbar.itemDelete": "Excluir", "SSE.Views.Statusbar.itemHidden": "Oculto", "SSE.Views.Statusbar.itemHide": "Ocultar", "SSE.Views.Statusbar.itemInsert": "Inserir", "SSE.Views.Statusbar.itemMaximum": "Máximo", "SSE.Views.Statusbar.itemMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMove": "Mover", "SSE.Views.Statusbar.itemProtect": "Proteger", "SSE.Views.Statusbar.itemRename": "Renomear", "SSE.Views.Statusbar.itemStatus": "Salvando status", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON> <PERSON>", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Planilha com este nome já existe.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Um nome de folha não pode conter os seguintes caracteres: \\/*?[]: ou o caractere ' como primeiro ou último caractere", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON>me da folha", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as folhas", "SSE.Views.Statusbar.sheetIndexText": "Folha {0} de {1}", "SSE.Views.Statusbar.textAverage": "Média", "SSE.Views.Statusbar.textCount": "Contar", "SSE.Views.Statusbar.textMax": "Máx", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Nova cor personalizada", "SSE.Views.Statusbar.textNoColor": "Sem cor", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipFirst": "<PERSON><PERSON><PERSON> para primeira folha", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON> para última folha", "SSE.Views.Statusbar.tipListOfSheets": "Lista de planilhas", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON> lista de folha direita", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON> lista de folha esquerda", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "Ampliar", "SSE.Views.Statusbar.tipZoomOut": "Reduzir", "SSE.Views.Statusbar.ungroupSheets": "Desagrupar folhas", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Não foi possível concluir a operação para o intervalo de células selecionado.<br>Selecione um intervalo de dados uniforme interno ou externo à tabela e tente novamente.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operação não pode ser concluída para o intervalo de célula selecionado<br>Selecione um intervalo para que a primeira linha da tabela esteja na mesma linha<br>e a tabela resultante se sobreponha a atual.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "A operação não pode ser concluída para o intervalo de célula selecionado.<br>Selecione um intervalo que não inclua outras tabelas.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Fórmulas de matriz de várias células não são permitidas em tabelas.", "SSE.Views.TableOptionsDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON> tabela", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.TableOptionsDialog.txtNote": "Os cabeçalhos devem permanecer na mesma linha e o intervalo da tabela resultante deve se sobrepor ao intervalo da tabela original.", "SSE.Views.TableOptionsDialog.txtTitle": "Titulo", "SSE.Views.TableSettings.deleteColumnText": "Excluir coluna", "SSE.Views.TableSettings.deleteRowText": "Excluir linha", "SSE.Views.TableSettings.deleteTableText": "Excluir tabela", "SSE.Views.TableSettings.insertColumnLeftText": "Inserir coluna à esquerda", "SSE.Views.TableSettings.insertColumnRightText": "Inserir coluna à direita", "SSE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> linha acima", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON>ser<PERSON> linha a<PERSON>o", "SSE.Views.TableSettings.notcriticalErrorTitle": "Aviso", "SSE.Views.TableSettings.selectColumnText": "Selecionar coluna inteira", "SSE.Views.TableSettings.selectDataText": "Selecionar dados da coluna", "SSE.Views.TableSettings.selectRowText": "Selecionar linha", "SSE.Views.TableSettings.selectTableText": "Selecionar tabela", "SSE.Views.TableSettings.textActions": "Ações da tabela", "SSE.Views.TableSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "SSE.Views.TableSettings.textBanded": "Em tiras", "SSE.Views.TableSettings.textColumns": "Colunas", "SSE.Views.TableSettings.textConvertRange": "Converter para intervalo", "SSE.Views.TableSettings.textEdit": "Linhas e Colunas", "SSE.Views.TableSettings.textEmptyTemplate": "Sem modelos", "SSE.Views.TableSettings.textExistName": "ERRO! Um intervalo com este nome já existe", "SSE.Views.TableSettings.textFilter": "Botão de filtro", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "ERRO! Nome da tabela inválido", "SSE.Views.TableSettings.textIsLocked": "Este elemento está sendo editado por outro usuário.", "SSE.Views.TableSettings.textLast": "Último", "SSE.Views.TableSettings.textLongOperation": "Operação longa", "SSE.Views.TableSettings.textPivot": "Inserir tabela dinâ<PERSON>a", "SSE.Views.TableSettings.textRemDuplicates": "Remover duplicatas", "SSE.Views.TableSettings.textReservedName": "O nome que você está tentando usar já está referenciado nas fórmulas da célula. Use algum outro nome.", "SSE.Views.TableSettings.textResize": "Redimensionar tabela", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Selecionar dados", "SSE.Views.TableSettings.textSlicer": "Inserir Segmentação de Dados", "SSE.Views.TableSettings.textTableName": "<PERSON>me da tabela", "SSE.Views.TableSettings.textTemplate": "Selecionar a partir do modelo", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.warnLongOperation": "A operação que você está prestes a realizar pode levar muito tempo para concluir.<br>Você tem certeza de que deseja continuar?", "SSE.Views.TableSettingsAdvanced.textAlt": "Texto Alternativo", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.TableSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabela - Configurações avançadas", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "Personalizado", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "Escuro", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "Médio", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "<PERSON><PERSON><PERSON> es<PERSON> da ta<PERSON>a", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "Estilo de Tabela Médio", "SSE.Views.TextArtSettings.strBackground": "Background color", "SSE.Views.TextArtSettings.strColor": "Color", "SSE.Views.TextArtSettings.strFill": "Fill", "SSE.Views.TextArtSettings.strForeground": "Foreground color", "SSE.Views.TextArtSettings.strPattern": "Pattern", "SSE.Views.TextArtSettings.strSize": "Size", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Opacity", "SSE.Views.TextArtSettings.strType": "Tipo", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Color Fill", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "No Pattern", "SSE.Views.TextArtSettings.textFromFile": "From File", "SSE.Views.TextArtSettings.textFromUrl": "From URL", "SSE.Views.TextArtSettings.textGradient": "Gradiente", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "No Fill", "SSE.Views.TextArtSettings.textPatternFill": "Pattern", "SSE.Views.TextArtSettings.textPosition": "Posição", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "Select", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "From Texture", "SSE.Views.TextArtSettings.textTile": "Tile", "SSE.Views.TextArtSettings.textTransform": "Transform", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "SSE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granite", "SSE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "SSE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Leather", "SSE.Views.TextArtSettings.txtNoBorders": "No Line", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "Madeira", "SSE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnColorSchemas": "Esquema de cores", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Cabeçalho/Rodapé", "SSE.Views.Toolbar.capBtnInsSlicer": "Segmentação de Dados", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "SSE.Views.Toolbar.capBtnMargins": "Margens", "SSE.Views.Toolbar.capBtnPageOrient": "Orientação", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnScale": "Dimensionar", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Enviar para trás", "SSE.Views.Toolbar.capImgForward": "Trazer para frente", "SSE.Views.Toolbar.capImgGroup": "Grupo", "SSE.Views.Toolbar.capInsertChart": "Gráfico", "SSE.Views.Toolbar.capInsertEquation": "Equação", "SSE.Views.Toolbar.capInsertHyperlink": "Hiperlink", "SSE.Views.Toolbar.capInsertImage": "Imagem", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Minigráfico", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Caixa de texto", "SSE.Views.Toolbar.capInsertTextart": "Arte de texto", "SSE.Views.Toolbar.mniImageFromFile": "Imagem do arquivo", "SSE.Views.Toolbar.mniImageFromStorage": "Imagem de armazenamento", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON> da URL", "SSE.Views.Toolbar.textAddPrintArea": "Adicionar à área de impressão", "SSE.Views.Toolbar.textAlignBottom": "Alinhar à parte inferior", "SSE.Views.Toolbar.textAlignCenter": "Alinhar ao centro", "SSE.Views.Toolbar.textAlignJust": "Justificado", "SSE.Views.Toolbar.textAlignLeft": "Alinhar à esquerda", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON>ar ao meio", "SSE.Views.Toolbar.textAlignRight": "Alinhar à direita", "SSE.Views.Toolbar.textAlignTop": "Alinhar à parte superior", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON> as bordas", "SSE.Views.Toolbar.textAuto": "Automático", "SSE.Views.Toolbar.textAutoColor": "Automático", "SSE.Views.Toolbar.textBold": "Negrito", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON> <PERSON> b<PERSON>a", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "Inferior:", "SSE.Views.Toolbar.textBottomBorders": "Bordas inferiores", "SSE.Views.Toolbar.textCenterBorders": "Bordas verticais interiores", "SSE.Views.Toolbar.textClearPrintArea": "Limpar á<PERSON>", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON> regras", "SSE.Views.Toolbar.textClockwise": "Ângulo no sentido horário", "SSE.Views.Toolbar.textColorScales": "Escalas de cores", "SSE.Views.Toolbar.textCounterCw": "Ângulo no sentido antihorário", "SSE.Views.Toolbar.textCustom": "Personalizado", "SSE.Views.Toolbar.textDataBars": "Barras de dados", "SSE.Views.Toolbar.textDelLeft": "Deslocar células para a esquerda", "SSE.Views.Toolbar.textDelUp": "Deslocar células para cima", "SSE.Views.Toolbar.textDiagDownBorder": "Borda inferior diagonal", "SSE.Views.Toolbar.textDiagUpBorder": "Borda superior diagonal", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "Editar <PERSON> v<PERSON>", "SSE.Views.Toolbar.textEntireCol": "Coluna inteira", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON>", "SSE.Views.Toolbar.textFewPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHeight": "Altura", "SSE.Views.Toolbar.textHideVA": "Ocultar á<PERSON> visí<PERSON>", "SSE.Views.Toolbar.textHorizontal": "Texto horizontal", "SSE.Views.Toolbar.textInsDown": "Deslocar células para baixo", "SSE.Views.Toolbar.textInsideBorders": "Bordas interiores", "SSE.Views.Toolbar.textInsRight": "Deslocar células para a direita", "SSE.Views.Toolbar.textItalic": "Itálico", "SSE.Views.Toolbar.textItems": "itens", "SSE.Views.Toolbar.textLandscape": "Paisagem", "SSE.Views.Toolbar.textLeft": "Esquerdo:", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textManageRule": "Gerenciar Regras", "SSE.Views.Toolbar.textManyPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Último personalizado", "SSE.Views.Toolbar.textMarginsNarrow": "Limitar", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Largo", "SSE.Views.Toolbar.textMiddleBorders": "Bordas horizontais interiores", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON> formatos", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON>", "SSE.Views.Toolbar.textNewColor": "Nova cor personalizada", "SSE.Views.Toolbar.textNewRule": "Nova regra", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON> bordas", "SSE.Views.Toolbar.textOnePage": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON> externas", "SSE.Views.Toolbar.textPageMarginsCustom": "Margens personalizadas", "SSE.Views.Toolbar.textPortrait": "Retrato", "SSE.Views.Toolbar.textPrint": "Imprimir", "SSE.Views.Toolbar.textPrintGridlines": "Imprimir linhas de grade", "SSE.Views.Toolbar.textPrintHeadings": "Cabeçalhos de impressão", "SSE.Views.Toolbar.textPrintOptions": "Configurações de impressão", "SSE.Views.Toolbar.textRight": "<PERSON><PERSON>ito: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "G<PERSON>r o texto para baixo", "SSE.Views.Toolbar.textRotateUp": "Girar Texto para Cima", "SSE.Views.Toolbar.textScale": "Escala", "SSE.Views.Toolbar.textScaleCustom": "Personalizado", "SSE.Views.Toolbar.textSelection": "Da seleção atual", "SSE.Views.Toolbar.textSetPrintArea": "Definir á<PERSON>", "SSE.Views.Toolbar.textShowVA": "Mostrar área visível", "SSE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubscript": "Subscrito", "SSE.Views.Toolbar.textSubSuperscript": "Subscrito/Sobrescrito", "SSE.Views.Toolbar.textSuperscript": "Sobrescrito", "SSE.Views.Toolbar.textTabCollaboration": "Colaboração", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "Arquivo", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "Página Inicial", "SSE.Views.Toolbar.textTabInsert": "Inserir", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "Proteção", "SSE.Views.Toolbar.textTabView": "Visualizar", "SSE.Views.Toolbar.textThisPivot": "De uma tabela dinâmica", "SSE.Views.Toolbar.textThisSheet": "A partir desta folha de trabalho", "SSE.Views.Toolbar.textThisTable": "A partir desta tabela", "SSE.Views.Toolbar.textTop": "Superior: ", "SSE.Views.Toolbar.textTopBorders": "Bordas superiores", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textVertical": "Texto vertical", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Alinhar à parte inferior", "SSE.Views.Toolbar.tipAlignCenter": "Alinhar ao centro", "SSE.Views.Toolbar.tipAlignJust": "Justificado", "SSE.Views.Toolbar.tipAlignLeft": "Alinhar à esquerda", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON>ar ao meio", "SSE.Views.Toolbar.tipAlignRight": "Alinhar à direita", "SSE.Views.Toolbar.tipAlignTop": "Alinhar à parte superior", "SSE.Views.Toolbar.tipAutofilter": "Classificar e Filtrar", "SSE.Views.Toolbar.tipBack": "Voltar", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeChart": "Alterar tipo de gráfico", "SSE.Views.Toolbar.tipClearStyle": "Limpar", "SSE.Views.Toolbar.tipColorSchemas": "Alterar esquema de cor", "SSE.Views.Toolbar.tipCondFormat": "Formatação condicional", "SSE.Views.Toolbar.tipCopy": "Copiar", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "SSE.Views.Toolbar.tipCut": "Cortar", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON>r casas de<PERSON>is", "SSE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON><PERSON> da <PERSON>e", "SSE.Views.Toolbar.tipDeleteOpt": "Excluir <PERSON>", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON> moe<PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "Estilo de Porcentagem", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartData": "Selecionar dados", "SSE.Views.Toolbar.tipEditChartType": "Alterar o tipo de gráfico", "SSE.Views.Toolbar.tipEditHeader": "Editar cab<PERSON>ho ou rodapé", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> da fonte", "SSE.Views.Toolbar.tipFontName": "Fonte", "SSE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipHAlighOle": "Alinhamento horizontal", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> ob<PERSON>", "SSE.Views.Toolbar.tipImgGroup": "Agrupar objetos", "SSE.Views.Toolbar.tipIncDecimal": "Aumentar números decimais", "SSE.Views.Toolbar.tipIncFont": "Aumentar tamanho da fonte", "SSE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "SSE.Views.Toolbar.tipInsertChartSpark": "Inserir g<PERSON>", "SSE.Views.Toolbar.tipInsertEquation": "Inserir equação", "SSE.Views.Toolbar.tipInsertHorizontalText": "Inserir caixa de texto horizontal", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "Inserir imagem", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertShape": "Inserir Forma Automática", "SSE.Views.Toolbar.tipInsertSlicer": "Inserir Segmentação de Dados", "SSE.Views.Toolbar.tipInsertSmartArt": "Inserir SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Inserir sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> tabela", "SSE.Views.Toolbar.tipInsertText": "Inserir caixa de texto", "SSE.Views.Toolbar.tipInsertTextart": "Inserir arte de texto", "SSE.Views.Toolbar.tipInsertVerticalText": "Inserir caixa de texto vertical", "SSE.Views.Toolbar.tipMerge": "Mesclar e centralizar", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "Formato de número", "SSE.Views.Toolbar.tipPageMargins": "Margens da página", "SSE.Views.Toolbar.tipPageOrient": "Orientação da página", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPaste": "Colar", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.Toolbar.tipPrint": "Imprimir", "SSE.Views.Toolbar.tipPrintArea": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintQuick": "Impressão rápida", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON>var suas alterações para que os outros usuários as vejam.", "SSE.Views.Toolbar.tipScale": "Dimensionar", "SSE.Views.Toolbar.tipSelectAll": "Selecionar todos", "SSE.Views.Toolbar.tipSendBackward": "Enviar para trás", "SSE.Views.Toolbar.tipSendForward": "Trazer para frente", "SSE.Views.Toolbar.tipSynchronize": "O documento foi alterado por outro usuário. Clique para salvar suas alterações e recarregar as atualizações.", "SSE.Views.Toolbar.tipTextFormatting": "Mais ferramentas de formatação de texto", "SSE.Views.Toolbar.tipTextOrientation": "Orientação", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Alinhamento vertical", "SSE.Views.Toolbar.tipVisibleArea": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "Ajustar texto", "SSE.Views.Toolbar.txtAccounting": "Contabilidade", "SSE.Views.Toolbar.txtAdditional": "Adicional", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "So<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "Todos", "SSE.Views.Toolbar.txtClearComments": "Comentários", "SSE.Views.Toolbar.txtClearFilter": "Limpar filtro", "SSE.Views.Toolbar.txtClearFormat": "Formato", "SSE.Views.Toolbar.txtClearFormula": "Função", "SSE.Views.Toolbar.txtClearHyper": "Hiperlinks", "SSE.Views.Toolbar.txtClearText": "Texto", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Personalizar", "SSE.Views.Toolbar.txtDate": "Data", "SSE.Views.Toolbar.txtDateTime": "Data e Hora", "SSE.Views.Toolbar.txtDescending": "Decrescente", "SSE.Views.Toolbar.txtDollar": "$ Dólar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponencial", "SSE.Views.Toolbar.txtFilter": "Filtro", "SSE.Views.Toolbar.txtFormula": "Inserir função", "SSE.Views.Toolbar.txtFraction": "Fração", "SSE.Views.Toolbar.txtFranc": "Franco suíço CHF", "SSE.Views.Toolbar.txtGeneral": "G<PERSON>", "SSE.Views.Toolbar.txtInteger": "Integral", "SSE.Views.Toolbar.txtManageRange": "Gerenciador de nomes", "SSE.Views.Toolbar.txtMergeAcross": "Mesclar através", "SSE.Views.Toolbar.txtMergeCells": "Mesclar célu<PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Mesclar e Centrar", "SSE.Views.Toolbar.txtNamedRange": "Intervalos Nomeadas", "SSE.Views.Toolbar.txtNewRange": "Define Name", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON> bordas", "SSE.Views.Toolbar.txtNumber": "Número", "SSE.Views.Toolbar.txtPasteRange": "Colar Nome", "SSE.Views.Toolbar.txtPercentage": "Porcentagem", "SSE.Views.Toolbar.txtPound": "£ Libra", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON>lo", "SSE.Views.Toolbar.txtScheme1": "Office", "SSE.Views.Toolbar.txtScheme10": "Mediana", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Opulento", "SSE.Views.Toolbar.txtScheme14": "Balcão envidraçado", "SSE.Views.Toolbar.txtScheme15": "Origem", "SSE.Views.Toolbar.txtScheme16": "Papel", "SSE.Views.Toolbar.txtScheme17": "<PERSON>st<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme18": "Técnica", "SSE.Views.Toolbar.txtScheme19": "Viagem", "SSE.Views.Toolbar.txtScheme2": "Escala de cinza", "SSE.Views.Toolbar.txtScheme20": "Urbano", "SSE.Views.Toolbar.txtScheme21": "Verve", "SSE.Views.Toolbar.txtScheme22": "Novo Office", "SSE.Views.Toolbar.txtScheme3": "Ápice", "SSE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "SSE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme6": "Concurso", "SSE.Views.Toolbar.txtScheme7": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme8": "Fluxo", "SSE.Views.Toolbar.txtScheme9": "Fundição", "SSE.Views.Toolbar.txtScientific": "Científico", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "Classificar", "SSE.Views.Toolbar.txtSortAZ": "Classificar do menor para o maior", "SSE.Views.Toolbar.txtSortZA": "Classificar do maior para o menor", "SSE.Views.Toolbar.txtSpecial": "Especial", "SSE.Views.Toolbar.txtTableTemplate": "Formato como Modelo de tabela", "SSE.Views.Toolbar.txtText": "Texto", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Desfaz a mesclagem de células", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "Inferior", "SSE.Views.Top10FilterDialog.txtBy": "por", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "Por cento", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 AutoFilter", "SSE.Views.Top10FilterDialog.txtTop": "Parte superior", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtro dos 10 principais", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Configurações do campo Valor", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Média", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Campo base", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Item base", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 de %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Contagem", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Nome personalizado", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "A diferença de", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Máx", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON> c<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Porcentagem de", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Diferença percentual de", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Porcentagem da coluna", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Porcentagem do total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Porcentagem de linha", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produ<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Total em execução", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Mostrar valores como", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "<PERSON>me da fonte:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Resuma o campo de valor por", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Convidado", "SSE.Views.ViewManagerDlg.lockText": "Bloqueado", "SSE.Views.ViewManagerDlg.textDelete": "Excluir", "SSE.Views.ViewManagerDlg.textDuplicate": "Dup<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "Nenhuma vista foi criada ainda.", "SSE.Views.ViewManagerDlg.textGoTo": "Ir para a Visualização", "SSE.Views.ViewManagerDlg.textLongName": "Digite um nome que tenha menos de 128 caracteres.", "SSE.Views.ViewManagerDlg.textNew": "Novo", "SSE.Views.ViewManagerDlg.textRename": "Renomear", "SSE.Views.ViewManagerDlg.textRenameError": "O nome não deve estar vazio.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Renomear vista", "SSE.Views.ViewManagerDlg.textViews": "Vistas de folha", "SSE.Views.ViewManagerDlg.tipIsLocked": "Este elemento está sendo editado por outro usuário.", "SSE.Views.ViewManagerDlg.txtTitle": "Gerenciador de visualização de folha", "SSE.Views.ViewManagerDlg.warnDeleteView": "Você está tentando excluir o modo de exibição atualmente ativado '%1'.<br><PERSON><PERSON><PERSON> este modo de exibição e excluí-lo?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "Visualização de folha", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Sempre mostrar a barra de ferramentas", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Ocultar barra de status", "SSE.Views.ViewTab.textCreate": "Novo", "SSE.Views.ViewTab.textDefault": "Padrão", "SSE.Views.ViewTab.textFormula": "Barra de fórmula", "SSE.Views.ViewTab.textFreezeCol": "Congelar a primeira coluna", "SSE.Views.ViewTab.textFreezeRow": "<PERSON><PERSON><PERSON> linha superior", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON> de <PERSON>", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Tema de interface", "SSE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "Gerenciamento de visualização", "SSE.Views.ViewTab.textRightMenu": "<PERSON><PERSON> dire<PERSON>", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Mostrar sombra dos painéis congelados", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textZeros": "Mostrar zeros", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Fechar visualização de folha", "SSE.Views.ViewTab.tipCreate": "Criar visualização de folha", "SSE.Views.ViewTab.tipFreeze": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "Tema de interface", "SSE.Views.ViewTab.tipSheetView": "Visualização de folha", "SSE.Views.WatchDialog.closeButtonText": "En<PERSON><PERSON>", "SSE.Views.WatchDialog.textAdd": "Adicionar observação", "SSE.Views.WatchDialog.textBook": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textCell": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textDelete": "Eliminar observação", "SSE.Views.WatchDialog.textDeleteAll": "Excluir tudo", "SSE.Views.WatchDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textName": "Nome", "SSE.Views.WatchDialog.textSheet": "Fol<PERSON>", "SSE.Views.WatchDialog.textValue": "Valor", "SSE.Views.WatchDialog.txtTitle": "Janela de observação", "SSE.Views.WBProtection.hintAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON>te<PERSON> folha", "SSE.Views.WBProtection.hintProtectWB": "Proteger a pasta de trabalho", "SSE.Views.WBProtection.txtAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON><PERSON>la <PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Forma bloqueada", "SSE.Views.WBProtection.txtLockedText": "Texto de bloqueio", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON>te<PERSON> folha", "SSE.Views.WBProtection.txtProtectWB": "Proteger a pasta de trabalho", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Digite uma senha para desproteger a folha", "SSE.Views.WBProtection.txtSheetUnlockTitle": "<PERSON>olha desprotegida", "SSE.Views.WBProtection.txtWBUnlockDescription": "Digite uma senha para desproteger a pasta de trabalho", "SSE.Views.WBProtection.txtWBUnlockTitle": "Desproteger pasta de trabalho"}