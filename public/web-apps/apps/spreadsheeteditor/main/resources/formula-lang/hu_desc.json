{"DATE": {"a": "(év; hónap; nap)", "d": "Eredménye a dátumot dátum- és időértékben megadó szám"}, "DATEDIF": {"a": "(kez<PERSON><PERSON>_dátum; z<PERSON><PERSON><PERSON>_dátum; egység)", "d": "<PERSON><PERSON><PERSON> d<PERSON> kö<PERSON>ok, hónapok vagy évek számát számítja ki"}, "DATEVALUE": {"a": "(dátum_szöveg)", "d": "Szövegként megadott dátumot olyan sz<PERSON>mm<PERSON> alak<PERSON>t <PERSON>t, amely dátum- és időértékben adja meg a dátumot."}, "DAY": {"a": "(időérték)", "d": "A hónap napját adja meg 1 és 31 közötti számmal"}, "DAYS": {"a": "(kez<PERSON><PERSON>_dátum; z<PERSON><PERSON><PERSON>_dátum)", "d": "A két dátum kö<PERSON>ötti napok számát adja eredményül."}, "DAYS360": {"a": "(kez<PERSON><PERSON>_dátum; z<PERSON><PERSON><PERSON>_dátum; [mó<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> d<PERSON> közé eső napok számát adja meg a 360 napos naptár (tizenkét 30 napos hónap) alapján."}, "EDATE": {"a": "(kezd<PERSON>_dátum; hónapok)", "d": "A kezdő_dátum-nál hónapok hónappal korábbi vagy későbbi dátum dátumértékét adja eredményül"}, "EOMONTH": {"a": "(kezd<PERSON>_dátum; hónapok)", "d": "A megadott számú hónapnál korábbi vagy későbbi hónap utolsó napjának dátumértékét adja eredményül."}, "HOUR": {"a": "(időérték)", "d": "Az órát adja meg 0 (0:00, a<PERSON><PERSON>) és 23 (este 11:00) közötti sz<PERSON>mmal."}, "ISOWEEKNUM": {"a": "(d<PERSON><PERSON>)", "d": "<PERSON><PERSON> adott d<PERSON>hoz tartozó hét ISO rendszer szerinti sorszámát adja eredményül az évben."}, "MINUTE": {"a": "(időérték)", "d": "A percet adja meg 0 és 59 közötti számmal."}, "MONTH": {"a": "(időérték)", "d": "A hónapot adja meg 1 (január) és 12 (december) közötti számmal."}, "NETWORKDAYS": {"a": "(kezd<PERSON>_dátum; vég_dátum; [ünne<PERSON><PERSON>])", "d": "<PERSON><PERSON>t adja meg, hogy a két dátum kö<PERSON>ött hány teljes munkana<PERSON> van."}, "NETWORKDAYS.INTL": {"a": "(kezd<PERSON>_dátum; vég_dátum; [hétvége]; [ü<PERSON><PERSON><PERSON>])", "d": "A<PERSON>t adja meg, hogy a két dátum között és az egyéni hétvége-paraméterek mellett hány teljes munkana<PERSON> van"}, "NOW": {"a": "()", "d": "Az aktuális dátumot és időpontot adja meg dátum és idő formátumban."}, "SECOND": {"a": "(időérték)", "d": "A másodpercet adja meg 0 és 59 közötti számmal."}, "TIME": {"a": "(óra; perc; másodperc)", "d": "<PERSON><PERSON>, perc, másodperc alakban megadott időpont időértékét adja meg időformátum alakban"}, "TIMEVALUE": {"a": "(idő_szöveg)", "d": "A szövegként megadott időpontot időértékké, azaz 0 (0:00:00, v<PERSON><PERSON><PERSON>) és 0,999988426 (este 11:59:59) köz<PERSON>tti számmá alakítja át. A képlet beírását követően időformátumot kell hozzárendelni"}, "TODAY": {"a": "()", "d": "Visszatérési értéke az aktuális dátum dátumkén<PERSON> formáz<PERSON>."}, "WEEKDAY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; [ered<PERSON><PERSON>_tí<PERSON>a])", "d": "<PERSON>gy d<PERSON> a hét egy napját azonosító számot ad eredményül, 1-t<PERSON>l 7-ig."}, "WEEKNUM": {"a": "(sors<PERSON><PERSON><PERSON>; [ered<PERSON><PERSON>_tí<PERSON>a])", "d": "<PERSON><PERSON> adott dá<PERSON>z tartozó hét sorszámát adja eredményül az évben."}, "WORKDAY": {"a": "(kezd<PERSON>_dátum; napok; [ünne<PERSON>k])", "d": "A kezdő dátumnál napok munkanappal korábbi vagy későbbi dátum dátumértékét adja eredményül."}, "WORKDAY.INTL": {"a": "(kezd<PERSON>_dátum; napok; [hétvége]; [ü<PERSON><PERSON><PERSON>])", "d": "A megadott szá<PERSON>ú munkanap el<PERSON>tti vagy utáni dátumértéket adja meg, az egyéni hétvége-paraméterek mellett"}, "YEAR": {"a": "(időérték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az adott dátum melyik évre esik (1900 és 9999 közötti egész szám)."}, "YEARFRAC": {"a": "(kezd<PERSON>_dátum; vég_dátum; [alap])", "d": "A kezdő_dátum és a vég_dátum közötti teljes napok számát törtévként fejezi ki."}, "BESSELI": {"a": "(x; n)", "d": "Az In(x) módosított Bessel-függvény értékét adja eredményül."}, "BESSELJ": {"a": "(x; n)", "d": "A Jn(x) Bessel-függvény értékét adja er<PERSON>mé<PERSON>ül."}, "BESSELK": {"a": "(x; n)", "d": "A Kn(x) módosított Bessel-függvény értékét adja eredményül."}, "BESSELY": {"a": "(x; n)", "d": "Az Yn(x) módosított Bessel-függvény értékét adja eredményül."}, "BIN2DEC": {"a": "(szám)", "d": "<PERSON><PERSON><PERSON> s<PERSON> de<PERSON> alakít á<PERSON>."}, "BIN2HEX": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON> s<PERSON> hexadeci<PERSON> alakít <PERSON>."}, "BIN2OCT": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON> s<PERSON> alakít <PERSON>."}, "BITAND": {"a": "(szám1; szám2)", "d": "<PERSON><PERSON><PERSON> „és” műveletet végez"}, "BITLSHIFT": {"a": "(sz<PERSON>m; el<PERSON><PERSON><PERSON>_mérté<PERSON>)", "d": "<PERSON><PERSON> el<PERSON>ás_mértéke bittel balra tolt sz<PERSON>mot adja v<PERSON>za"}, "BITOR": {"a": "(szám1; szám2)", "d": "<PERSON><PERSON><PERSON> s<PERSON> „vagy” műveletet végez"}, "BITRSHIFT": {"a": "(sz<PERSON>m; el<PERSON><PERSON><PERSON>_mérté<PERSON>)", "d": "<PERSON><PERSON> el<PERSON>ás_mértéke bittel jobbra tolt számot adja vissza"}, "BITXOR": {"a": "(szám1; szám2)", "d": "<PERSON><PERSON><PERSON> „kizárólagos vagy” műveletet végez"}, "COMPLEX": {"a": "(val<PERSON>_szám; képzetes_szám; [képz_jel])", "d": "Valós és képzetes részekből komplex számot képez."}, "CONVERT": {"a": "(szám; miből; mibe)", "d": "Mértékegységeket vált át."}, "DEC2BIN": {"a": "(szám; [jegyek])", "d": "Decim<PERSON><PERSON> s<PERSON><PERSON> alakí<PERSON>."}, "DEC2HEX": {"a": "(szám; [jegyek])", "d": "Decimális sz<PERSON>mot hexadecimális<PERSON>á alakít á<PERSON>."}, "DEC2OCT": {"a": "(szám; [jegyek])", "d": "Decimális sz<PERSON><PERSON> ok<PERSON>á alakít át."}, "DELTA": {"a": "(szám1; [szám2])", "d": "Azt vizsg<PERSON><PERSON><PERSON>, hogy két érték egyenlő-e."}, "ERF": {"a": "(als<PERSON>_határ; [f<PERSON><PERSON>_határ])", "d": "A hibaintegrál vagy hibafüggvény értékét adja eredményül."}, "ERF.PRECISE": {"a": "(x)", "d": "A hibaintegrál értékét adja eredményül."}, "ERFC": {"a": "(x)", "d": "A hibaintegrál komplemensének értékét adja eredményül."}, "ERFC.PRECISE": {"a": "(x)", "d": "A hibaintegrál komplemensének értékét adja eredményül."}, "GESTEP": {"a": "(szám; [kü<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON>t viz<PERSON>g<PERSON><PERSON><PERSON>, hogy egy szám nagyobb-e egy adott küszöbértéknél."}, "HEX2BIN": {"a": "(szám; [jegyek])", "d": "Hexadecimális s<PERSON><PERSON><PERSON> alakít <PERSON>."}, "HEX2DEC": {"a": "(szám)", "d": "Hexadecimális számot decimális<PERSON>á alakít át."}, "HEX2OCT": {"a": "(szám; [jegyek])", "d": "Hexadecimális sz<PERSON>mot oktális<PERSON>á alakít át."}, "IMABS": {"a": "(k_szám)", "d": "Komplex szám abszolút érték<PERSON>t (modulusát) adja eredmé<PERSON>ül."}, "IMAGINARY": {"a": "(k_szám)", "d": "Komplex szám képzetes részét adja eredményül."}, "IMARGUMENT": {"a": "(k_szám)", "d": "A komplex szám radiánban kifejezett argumentumát adja eredményül."}, "IMCONJUGATE": {"a": "(k_szám)", "d": "Komplex szám komplex konjugáltját adja eredményül."}, "IMCOS": {"a": "(k_szám)", "d": "Komplex szám koszinuszát adja eredményül."}, "IMCOSH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus koszinuszát számítja ki"}, "IMCOT": {"a": "(k_szám)", "d": "Egy komplex szám kotangensét számítja ki"}, "IMCSC": {"a": "(k_szám)", "d": "Egy komplex szám koszekánsát számítja ki"}, "IMCSCH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus koszekánsát számítja ki"}, "IMDIV": {"a": "(k_szám1; k_szám2)", "d": "Két komplex szám hányadosát adja eredményül."}, "IMEXP": {"a": "(k_szám)", "d": "Az e szám komplex kitevőjű hatványát adja eredményül."}, "IMLN": {"a": "(k_szám)", "d": "Komplex szám természetes logaritmusát adja eredményül."}, "IMLOG10": {"a": "(k_szám)", "d": "Komplex szám tízes alapú logaritmusát adja eredményül."}, "IMLOG2": {"a": "(k_szám)", "d": "Komplex szám kettes alapú logaritmusát adja eredményül."}, "IMPOWER": {"a": "(k_szám; szám)", "d": "Komplex szám hatványát adja eredményül."}, "IMPRODUCT": {"a": "(k_szám1; [k_szám2]; ...)", "d": "1–255 komplex szám szorzatának kiszámítása"}, "IMREAL": {"a": "(k_szám)", "d": "Komplex szám valós részét adja eredményül."}, "IMSEC": {"a": "(k_szám)", "d": "Egy komplex szám sze<PERSON>ánsát számítja ki"}, "IMSECH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus szekánsát számítja ki"}, "IMSIN": {"a": "(k_szám)", "d": "Komplex szám szinuszát adja eredményül."}, "IMSINH": {"a": "(k_szám)", "d": "Egy komplex szám hiperbolikus szinuszát számítja ki"}, "IMSQRT": {"a": "(k_szám)", "d": "Komplex szám négyzetgyökét adja eredményül."}, "IMSUB": {"a": "(k_szám1; k_szám2)", "d": "Két komplex szám különbségét adja eredményül."}, "IMSUM": {"a": "(k_szám1; [k_szám2]; ...)", "d": "Komplex számok összegének visszakeresése"}, "IMTAN": {"a": "(k_szám)", "d": "Egy komplex szám tan<PERSON>ét számítja ki"}, "OCT2BIN": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> alak<PERSON>."}, "OCT2DEC": {"a": "(szám)", "d": "<PERSON><PERSON><PERSON><PERSON> sz<PERSON><PERSON> deci<PERSON>á alakít <PERSON>."}, "OCT2HEX": {"a": "(szám; [jegyek])", "d": "<PERSON><PERSON><PERSON><PERSON> sz<PERSON>mot hexadecimális<PERSON>á alakít <PERSON>."}, "DAVERAGE": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "Egy lista- v<PERSON><PERSON> ad<PERSON>zisoszlopban lévő azon értékek átlagát számítja ki, melyek megfelelnek a megadott feltételeknek"}, "DCOUNT": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON><PERSON> adat<PERSON><PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiban megszámolja, h<PERSON><PERSON> da<PERSON> s<PERSON><PERSON> van egy adott mezőben (oszlopban)."}, "DCOUNTA": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON><PERSON> adat<PERSON><PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiban megszámolja, h<PERSON><PERSON> darab nem üres cella van egy adott mezőben (oszlopban)."}, "DGET": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON><PERSON> ad<PERSON>ból egyetlen olyan mezőt vesz ki, amely megfelel a megadott feltételeknek."}, "DMAX": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mező<PERSON> (oszlopban) lévő legnagyobb számot adja er<PERSON>ményül"}, "DMIN": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mező<PERSON> (oszlopban) lévő legkisebb számot adja eredményül."}, "DPRODUCT": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mezőben (oszlopban) összeszorozza az értékeket"}, "DSTDEV": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "Az adatbázis kiválasztott elemei mint minta alapján becslést ad a szórásra."}, "DSTDEVP": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatbá<PERSON>s szűrt rekordjainak megadott mezőjében található értékek (nem mint minta, hanem a teljes sokaság) alapján kiszámítja a szórást."}, "DSUM": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatb<PERSON><PERSON><PERSON> adott feltételeknek eleget tevő rekordjaiból álló mező<PERSON> (oszlopban) összeadja az értékeket."}, "DVAR": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "Az adatbázis szűrt rekordjainak megadott mezőjében található értékek mint minta alapján becslést ad a varianciára; eredményül e becsült értéket adja."}, "DVARP": {"a": "(adatb<PERSON>zis; mező; kritérium)", "d": "<PERSON>z adatbá<PERSON>s szűrt rekordjainak megadott mezőjében található értékek (nem mint minta, hanem a teljes sokaság) alapján kiszámítja a varianciát."}, "CHAR": {"a": "(szám)", "d": "A kódszám által meghatározott karaktert adja eredményül a számítógépen beállított karakterkészletből"}, "CLEAN": {"a": "(szöveg)", "d": "A szövegből eltünteti az összes nem kinyomtatható karaktert."}, "CODE": {"a": "(szöveg)", "d": "Egy szövegdarab első karakterének numerikus kódját adja eredményül a számítógép által használt karakterkészlet szerint"}, "CONCATENATE": {"a": "(szöveg1; [szöveg2]; ...)", "d": "Több szövegdarabot egyetlen szöveggé fűz <PERSON>ze"}, "CONCAT": {"a": "(szöveg1; ...)", "d": "Összefűz egy szöveges karakterláncokból álló listát vagy tartományt"}, "DOLLAR": {"a": "(sz<PERSON>m; [tized<PERSON>k])", "d": "Egy számot pénznem formátumú szöveggé alak<PERSON>."}, "EXACT": {"a": "(szöveg1; szöveg2)", "d": "Két szövegrészt has<PERSON><PERSON>, az eredmény IGAZ vagy HAMIS. Az AZONOS függvény megkülönbözteti a kis- és nagybetűket"}, "FIND": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Megkeres egy szövegrészt egy m<PERSON>, eredményül a talált szövegrész kezdőpozíciójának számát adja, a kis- és nagybetűket megkülönbözteti"}, "FINDB": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Egy karaktersorozatban egy másikat kere<PERSON>, és eredményül az első karakterlánc első karakterének helyét adja a második karakterlánc elejétől számítva, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai"}, "FIXED": {"a": "(szám; [tizedesek]; [nincs_pont])", "d": "Egy szá<PERSON> adott szá<PERSON>ú tizedesjegyre kerekít és szöveg formában adja vissza ezreselválasztó jelekkel vagy azok nélkül."}, "LEFT": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "Egy szövegrész elejétől megadott számú karaktert ad eredményül"}, "LEFTB": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "A szöveg első karaktereit adja vissza a megadott bájtszám alapján, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai"}, "LEN": {"a": "(szöveg)", "d": "Egy szöveg karakterekben mért hosszát adja eredményül."}, "LENB": {"a": "(szöveg)", "d": "A szöveg karaktereinek ábrázolására hasz<PERSON>lt bájtok számát adja v<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai"}, "LOWER": {"a": "(szöveg)", "d": "Egy szövegrészben lévő összes betűt kisbetűvé alakít át"}, "MID": {"a": "(szöveg; honnant<PERSON><PERSON>; h<PERSON>y_karakter)", "d": "Eredményként megadott számú karaktert ad egy szövegből a megadott sorszámú karaktertől kezdődően"}, "MIDB": {"a": "(szöveg; honnant<PERSON><PERSON>; h<PERSON>y_karakter)", "d": "A karakterek által elfoglalt bájtok alapján megadott pozíciójú karaktertől kezdve adott számú karaktert ad vissza szövegből, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai"}, "NUMBERVALUE": {"a": "(szöveg; [tizedes_elv]; [ezres_elv])", "d": "Szöveget konvertál számmá területi beállítástól független módon."}, "PROPER": {"a": "(szöveg)", "d": "Egy szövegrész minden szavának kezdőbetűjét nagybetűre, az összes többi betűt pedig kisbetűre cseréli"}, "REPLACE": {"a": "(régi_szöveg; honna<PERSON><PERSON><PERSON>; h<PERSON><PERSON>_karakter; új_szöveg)", "d": "Szövegdarab megadott részét eltérő szövegdarabbal cseréli ki."}, "REPLACEB": {"a": "(régi_szöveg; honna<PERSON><PERSON><PERSON>; h<PERSON><PERSON>_karakter; új_szöveg)", "d": "<PERSON><PERSON> adott bájtszám alapján a szöveg adott részét másik karaktersorozatra cseréli, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai"}, "REPT": {"a": "(szöveg; hányszor)", "d": "Megadott alkalommal megismétel egy szövegdarabot. A SOKSZOR függvény segítségével egy szövegdarab számos példányával tölthet fel egy cellát."}, "RIGHT": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "Egy szövegrész végétől megadott számú karaktert ad eredményül"}, "RIGHTB": {"a": "(szöveg; [h<PERSON><PERSON>_karakter])", "d": "Szöveg utolsó karaktereit adja vissza a megadott bájtszám alapján, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai"}, "SEARCH": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Azt a karaktersorszámot adja meg, ahol egy adott karakter vagy szövegdarab először fordul el<PERSON> balról jobbra haladva, a kis- és nagybetűket azonosnak tekintve."}, "SEARCHB": {"a": "(keres_szöveg; szöveg; [kezdet])", "d": "Egy szöveges karakterláncot keres egy második karakterláncban, és visszaadja az első karakterlánc kezdő pozíciójának számát a második karakterlánc első karakterében, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DBCS) karakterkészletet alkalmazó nyelvekhez készült - a japán, a kínai és a koreai"}, "SUBSTITUTE": {"a": "(szöveg; régi_szöveg; új_szöveg; [melyiket])", "d": "Egy szövegdarabban a régi_szöveg előfordulásait az új_szövegre cseréli ki."}, "T": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték szöveg-e, é<PERSON> ha igen, az eredmény a szöveg; ha nem szöveg, az eredmény kettős idézőjel (üres szöveg)"}, "TEXT": {"a": "(érték; format_text)", "d": "Értéket konvertál egy adott számformátumú szöveggé"}, "TEXTJOIN": {"a": "(elválasztó; üreset_mellőz; szöveg1; ...)", "d": "Elválasztó karakterrel összefűz egy karakterláncokból álló listát vagy tartományt"}, "TRIM": {"a": "(szöveg)", "d": "Egy szövegből eltávolítja az összes szóközt a szavak közti egyszeres szóközök kivételével."}, "UNICHAR": {"a": "(szám)", "d": "Az adott numerikus érték által hivatkozott Unicode-karaktert adja eredményül."}, "UNICODE": {"a": "(szöveg)", "d": "A szöveg első karakterének megfelelő számot (kódpontot) adja eredményül."}, "UPPER": {"a": "(szöveg)", "d": "Szövegrészt nagybetűssé alakít át"}, "VALUE": {"a": "(szöveg)", "d": "S<PERSON><PERSON><PERSON> szöveget számmá alakít á<PERSON>."}, "AVEDEV": {"a": "(szám1; [szám2]; ...)", "d": "Az adatpontoknak átlaguktól való átlagos abszolút eltérését számítja ki. Az argumentumok számok vagy számokat tartalmazó nevek, tömbök vagy hivatkozások lehetnek."}, "AVERAGE": {"a": "(szám1; [szám2]; ...)", "d": "Az argumentumok átlagát (számtani Középértékét) számolja ki Az argumentumok nevek, tömbök vagy számokat tartalmazó hivatkozások lehetnek."}, "AVERAGEA": {"a": "(érték1; [érték2]; ...)", "d": "Arg<PERSON><PERSON><PERSON><PERSON><PERSON> (számtani közepét) adja meg, a szöveget és a HAMIS értéket 0-nak veszi; az IGAZ értéket 1-nek. Az argumentumok számok, neve<PERSON>, tömbök vagy hivatkozások lehetnek."}, "AVERAGEIF": {"a": "(tartomány; kritérium; [átlag_tartomány])", "d": "<PERSON><PERSON> <PERSON>ott feltétel vagy kritérium által meghatározott cellák <PERSON> (számtani közepét) számítja ki"}, "AVERAGEIFS": {"a": "(átlag_tartomány; kritériumtartomány; kritérium; ...)", "d": "<PERSON><PERSON> <PERSON><PERSON> felt<PERSON>- vagy kritériumkészlet által meghatározott cellák <PERSON> (számtani közepét) számítja ki"}, "BETADIST": {"a": "(x; alfa; béta; [A]; [B])", "d": "A bétaeloszlás sűrűségfüggvényének értékét számítja ki."}, "BETAINV": {"a": "(valószínűség; alfa; béta; [A]; [B])", "d": "A bétaeloszlás sűrűségfüggvény (BÉTA.ELOSZLÁS) inverzét számítja ki."}, "BETA.DIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv; [A]; [B])", "d": "A béta valószínűségi eloszlás értékét számítja ki"}, "BETA.INV": {"a": "(valószínűség; alfa; béta; [A]; [B])", "d": "A bétaeloszlás sűrűségfüggvénye (BÉTA.ELOSZL) inverzét számítja ki."}, "BINOMDIST": {"a": "(sikeresek; kísérletek; siker_valószínűsége; eloszlásfv)", "d": "A diszkrét binomiális eloszlás valószínűségértékét számítja ki."}, "BINOM.DIST": {"a": "(sikeresek; kísérletek; siker_valószínűsége; eloszlásfv)", "d": "A diszkrét binomiális eloszlás valószínűségértékét számítja ki."}, "BINOM.DIST.RANGE": {"a": "(kísérletek; siker_valószínűsége; sikeresek; [sikeresek_2])", "d": "<PERSON><PERSON> adott kimenetel valószínűségét számítja ki binomiális eloszlás esetén."}, "BINOM.INV": {"a": "(kísérletek; siker_valószínűsége; alfa)", "d": "Azt a legkisebb számot adja er<PERSON>, amely<PERSON> a binomiális eloszlásfüggvény értéke nem kisebb egy adott határértéknél."}, "CHIDIST": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A khi-négyzet eloszlás jobbszélű valószínűségértékét számítja ki."}, "CHIINV": {"a": "(valószínűség; szabadságfok)", "d": "A khi-négyzet eloszlás jobbszélű inverzét számítja ki."}, "CHITEST": {"a": "(tényleges_tartomány; várható_tartomány)", "d": "Függetlenségvizsgálatot hajt végre, eredményül a khi-négyzet eloszláshoz rendelt értéket adja a statisztika és a szabadságfokok megfelelő száma szerint."}, "CHISQ.DIST": {"a": "(x; s<PERSON><PERSON>ságfok; eloszlásfv)", "d": "A khi-négyzet eloszlás balszélű valószínűségértékét számítja ki"}, "CHISQ.DIST.RT": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A khi-négyzet eloszlás jobbszélű valószínűségértékét számítja ki"}, "CHISQ.INV": {"a": "(valószínűség; szabadságfok)", "d": "A khi-négyzet eloszlás balszélű inverzét számítja ki."}, "CHISQ.INV.RT": {"a": "(valószínűség; szabadságfok)", "d": "A khi-négyzet eloszlás jobbszélű inverzét számítja ki."}, "CHISQ.TEST": {"a": "(tényleges_tartomány; várható_tartomány)", "d": "Függetlenségvizsgálatot hajt végre, eredményül a khi-négyzet eloszláshoz rendelt értéket adja a statisztika és a szabadságfokok megfelelő száma szerint."}, "CONFIDENCE": {"a": "(alfa; szórás; méret)", "d": "Egy statisztikai sokaság várható értékének megbízhatósági intervallumát adja eredmé<PERSON>ül norm<PERSON>l el<PERSON><PERSON>"}, "CONFIDENCE.NORM": {"a": "(alfa; szórás; méret)", "d": "Egy statisztikai sokaság várható értékének megbízhatósági intervallumát adja eredményül a normális el<PERSON><PERSON>"}, "CONFIDENCE.T": {"a": "(alfa; szórás; méret)", "d": "Egy statisztikai sokaság várható értékének megbízhatósági intervallumát adja eredményül a Student-féle t-próba <PERSON>"}, "CORREL": {"a": "(tömb1; tömb2)", "d": "<PERSON><PERSON><PERSON> ad<PERSON><PERSON><PERSON> e<PERSON>ütthatóját számítja ki."}, "COUNT": {"a": "(érték1; [érték2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy h<PERSON>y olyan cella van egy tart<PERSON>, amely s<PERSON><PERSON><PERSON> tarta<PERSON>."}, "COUNTA": {"a": "(érték1; [érték2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy hány nem üres cella található egy tartományban."}, "COUNTBLANK": {"a": "(tartomány)", "d": "Kijelölt cellatartományban megszámlálja az üres cellákat"}, "COUNTIF": {"a": "(tartomány; kritérium)", "d": "Egy tartományban összeszámolja azokat a nem üres cellákat, amelyek eleget tesznek a megadott feltételeknek."}, "COUNTIFS": {"a": "(kritériumtartomány; kritérium; ...)", "d": "<PERSON><PERSON> adott <PERSON>- vagy kritériumkészlet által meghatározott cellatartomány celláinak számát állapítja meg"}, "COVAR": {"a": "(tömb1; tömb2)", "d": "A kova<PERSON>, a<PERSON>z két adathal<PERSON>z minden egyes adatpontpárja esetén vett eltérések szorzatának átlagát számítja ki."}, "COVARIANCE.P": {"a": "(tömb1; tömb2)", "d": "A sokaság kova<PERSON>, azaz a két adathalmaz minden egyes adatpontpárja esetén vett eltérések szorzatának átlagát számítja ki"}, "COVARIANCE.S": {"a": "(tömb1; tömb2)", "d": "A minta kovarianciáját, azaz a két adathalmaz minden egyes adatpontpárja esetén vett eltérések szorzatának átlagát számítja ki"}, "CRITBINOM": {"a": "(kísérletek; siker_valószínűsége; alfa)", "d": "Azt a legkisebb számot adja er<PERSON>, amely<PERSON> a binomiális eloszlásfüggvény értéke nem kisebb egy adott határértéknél."}, "DEVSQ": {"a": "(szám1; [szám2]; ...)", "d": "Az egyes adatpontok középértéktől való eltérésnégyzeteinek összegét adja eredményül"}, "EXPONDIST": {"a": "(x; lambda; eloszlásfv)", "d": "Az exponenciális eloszlás értékét számítja ki"}, "EXPON.DIST": {"a": "(x; lambda; eloszlásfv)", "d": "Az exponenciális eloszlás értékét számítja ki"}, "FDIST": {"a": "(x; szabadságfok1; szabadságfok2)", "d": "Az F-elos<PERSON>lás (jobbszélű) értékét (az eltérés fokát) számítja ki két adathalmazra."}, "FINV": {"a": "(valószínűség; szabadságfok1; szabadságfok2)", "d": "Az F-elos<PERSON><PERSON><PERSON> (jobbszélű) értékét számítja ki: ha p = F.ELOSZLÁS(x,...), akkor INVERZ.F(p,...) = x"}, "FTEST": {"a": "(tömb1; tömb2)", "d": "Az F-próba értékét adja eredményül (annak a kétszélű valószínűségét, hogy a két tömb varianciája nem tér el szignifikánsan)"}, "F.DIST": {"a": "(x; szabadságfok1; szabadságfok2; eloszlásfv)", "d": "A balszélű F-eloszlás é<PERSON> (az eltérés fokát) számítja ki két adathalmazra."}, "F.DIST.RT": {"a": "(x; szabadságfok1; szabadságfok2)", "d": "A jobbszélű F-eloszlás <PERSON> (az eltérés fokát) számítja ki két adathalmazra."}, "F.INV": {"a": "(valószínűség; szabadságfok1; szabadságfok2)", "d": "A balszélű F-eloszlás inverzének értékét számítja ki: ha p = F.ELOSZL(x,...), akkor F.INVERZ(p,...) = x"}, "F.INV.RT": {"a": "(valószínűség; szabadságfok1; szabadságfok2)", "d": "A jobbszélű F-eloszlás inverzének értékét számítja ki: ha p = F.ELOSZLÁS.JOBB(x,...), akkor F.INVERZ.JOBB(p,...) = x"}, "F.TEST": {"a": "(tömb1; tömb2)", "d": "Az F-próba értékét adja eredményül (annak a kétszélű valószínűségét, hogy a két tömb varianciája nem tér el szignifikánsan)"}, "FISHER": {"a": "(x)", "d": "Fisher-transzformációt hajt végre"}, "FISHERINV": {"a": "(y)", "d": "A Fisher-transzformáció inverzét hajtja végre: ha y = FISHER(x), akkor INVERZ.FISHER(y) = x"}, "FORECAST": {"a": "(x; ismert_y-ok; ismert_x-ek)", "d": "<PERSON>z ismert értékek alapján lineáris regress<PERSON><PERSON> segítségével jövőbeli értéket számít ki vagy becsül meg."}, "FORECAST.ETS": {"a": "(cél_d<PERSON><PERSON>; ért<PERSON>kek; id<PERSON>sor; [szezonalitás]; [adatkiegészítés]; [aggregáció])", "d": "Ez a függvény a megadott jövőbeli céldátumra vonatkozó előre jelzett értéket adja vissza exponenciális simítás alkalma<PERSON>."}, "FORECAST.ETS.CONFINT": {"a": "(cél_d<PERSON><PERSON>; ért<PERSON>kek; id<PERSON>sor; [konfidenciaszint]; [szezonalitás]; [adatkiegészítés]; [aggreg<PERSON><PERSON>ó])", "d": "Ez a függvény a megadott céldátumra előre jelzett érték konfidencia-intervallumát adja v<PERSON>."}, "FORECAST.ETS.SEASONALITY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; id<PERSON><PERSON>; [adatkiegészítés]; [aggregáció])", "d": "Ez a függvény a megadott idősorban az alkalmazás által észlelt ismétlődő minta hosszát adja vissza."}, "FORECAST.ETS.STAT": {"a": "(<PERSON>rt<PERSON><PERSON>k; id<PERSON>sor; statisztika_típusa; [szezonalitás]; [adatkiegészítés]; [aggregáció])", "d": "Ez a függvény az előrejelzés megadott statisztikai adatait adja vissza."}, "FORECAST.LINEAR": {"a": "(x; ismert_y-ok; ismert_x-ek)", "d": "<PERSON>z ismert értékek alapján lineáris regress<PERSON><PERSON> segítségével jövőbeli értéket számít ki vagy becsül meg."}, "FREQUENCY": {"a": "(adattömb; csoport_tömb)", "d": "A gyakorisági vagy empirikus eloszlás értékét (milyen gyakran fordulnak elő az értékek egy értéktartományban) a csoport_tömbnél eggyel több elemet tartalmazó függőleges tömbként adja eredményül."}, "GAMMA": {"a": "(x)", "d": "A Gamma-függvény értékét számítja ki."}, "GAMMADIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "A gammaeloszlás értékét számítja ki"}, "GAMMA.DIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "A gammaeloszlás értékét számítja ki"}, "GAMMAINV": {"a": "(valószínűség; alfa; béta)", "d": "A gammaeloszlás eloszlásfüggvénye inverzének értékét számítja ki: ha p = GAMMA.ELOSZLÁS(x,...), akkor INVERZ.GAMMA(p,...) = x."}, "GAMMA.INV": {"a": "(valószínűség; alfa; béta)", "d": "A gammaeloszlás eloszlásfüggvénye inverzének értékét számítja ki: ha p = GAMMA.ELOSZL(x,...), akkor GAMMA.INVERZ(p,...) = x."}, "GAMMALN": {"a": "(x)", "d": "A gamma-függvény természetes logaritmusát számítja ki"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "A gamma-függvény természetes logaritmusát számítja ki."}, "GAUSS": {"a": "(x)", "d": "A standard normális eloszlás eloszlásfüggvényének értékénél 0,5-del kisebb értéket ad vissza."}, "GEOMEAN": {"a": "(szám1; [szám2]; ...)", "d": "Pozitív számértékekből álló tömb vagy tartomány mértani középértékét számítja ki."}, "GROWTH": {"a": "(ismert_y-ok; [ismert_x-ek]; [új_x-k]; [konstans])", "d": "Az eredmény az ismert adatpontoknak megfelelő, exponenciális trend szerint növekvő számok sorozata"}, "HARMEAN": {"a": "(szám1; [szám2]; ...)", "d": "Pozitív számok halmazának harmonikus átlagát számítja ki: a számok reciprokai számtani közepének a reciprokát"}, "HYPGEOM.DIST": {"a": "(minta_s; h<PERSON>y_minta; sokaság_s; sokaság_mérete; eloszlásfv)", "d": "A hipergeometriai eloszlás értékét számítja ki"}, "HYPGEOMDIST": {"a": "(minta_s; h<PERSON>y_minta; sokaság_s; sokaság_mérete)", "d": "A hipergeometriai eloszlás értékét számítja ki"}, "INTERCEPT": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "Az ismert x és y értékekre legjobban illeszked<PERSON> regressziós egyenes segítségével az egyenes y-tengel<PERSON>el való met<PERSON>t hatá<PERSON>zza meg."}, "KURT": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> ad<PERSON>osságát számítja ki"}, "LARGE": {"a": "(tömb; k)", "d": "<PERSON><PERSON> adathalmaz k-adik legnagyobb elemét adja eredményül. Például az ötödik legnagyobb számot"}, "LINEST": {"a": "(ismert_y-ok; [ismert_x-ek]; [konstans]; [stat])", "d": "Visszatérési értéke a statisztikai adatok olyan lineá<PERSON>, amely ismert adatpontok egyeztetésével a legkisebb négyzetek módszerével az adatokra legjobban illeszkedő egyenes paramétereit tartalmazza"}, "LOGEST": {"a": "(ismert_y-ok; [ismert_x-ek]; [konstans]; [stat])", "d": "Visszatérési értéke az ismert adatpontokhoz legjobban illeszkedő exponenciális görbét leíró statisztikai adatok"}, "LOGINV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A lognormális eloszlás inverzét számítja ki x-re; ln(x) norm<PERSON>lis eloszlását a középérték és szórás paraméterei adják meg."}, "LOGNORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; s<PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A lognormális eloszlásfüggvény értékét számítja ki x-re; ln(x) normális eloszlását a középérték és szórás paraméterek adják meg"}, "LOGNORM.INV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A lognormális eloszlás inverzét számítja ki x-re; ln(x) norm<PERSON>lis eloszlását a középérték és szórás paraméterek adják meg."}, "LOGNORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; szórás)", "d": "A lognormális eloszlásfüggvény értékét számítja ki x-re; ln(x) normális eloszlását a középérték és szórás paraméterei adják meg"}, "MAX": {"a": "(szám1; [szám2]; ...)", "d": "Egy értékhalmazban szereplő legnagyobb számot adja meg. A logikai értékeket és szövegeket figyelmen kívül hagyja."}, "MAXA": {"a": "(érték1; [érték2]; ...)", "d": "Egy értékhalmazban szereplő legnagyobb értéket adja eredményül. A logikai értékeket és a szövegeket is figyelembe veszi."}, "MAXIFS": {"a": "(max_tartomány; kritérium_tartomány; kritériumok; ...)", "d": "A megadott feltétel- vagy kritériumkészlet által meghatározott cellák közül a legnagyobb értékűt adja eredményül"}, "MEDIAN": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> s<PERSON>mhal<PERSON>z <PERSON> (a halmaz közepén lévő számot) számítja ki."}, "MIN": {"a": "(szám1; [szám2]; ...)", "d": "Egy értékhalmazban lévő legkisebb számot adja meg. A logikai értékeket és a szövegeket figyelmen kívül hagyja."}, "MINA": {"a": "(érték1; [érték2]; ...)", "d": "Egy értékhalmazban szereplő legkisebb értéket adja eredményül. A logikai értékeket és a szövegeket is figyelembe veszi."}, "MINIFS": {"a": "(min_tartomány; kritérium_tartomány; kritériumok; ...)", "d": "A megadott feltétel- vagy kritériumkészlet által meghatározott cellák közül a legkisebb értékűt adja eredményül"}, "MODE": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> tömbből vagy adattartományból kiválasztja a leggyakrabban előforduló vagy ismétlődő számot."}, "MODE.MULT": {"a": "(szám1; [szám2]; ...)", "d": "Egy tömbben vagy adattartományban leggyakrabban szereplő vagy ismétlődő értékek egy függőleges tömbjét adja vissza. Vízszintes tömbhöz használja a =TRANSZPONÁLÁS(MÓDUSZ.TÖBB(szám1,szám2,...))"}, "MODE.SNGL": {"a": "(szám1; [szám2]; ...)", "d": "<PERSON><PERSON> tömbből vagy adattartományból kiválasztja a leggyakrabban előforduló vagy ismétlődő számot"}, "NEGBINOM.DIST": {"a": "(kudarc_s<PERSON>m; si<PERSON><PERSON>k; valószínűség; eloszlásfv)", "d": "A negatív binomiális eloszlás értékét adja meg; annak a valószínűségét, hogy adott számú kudarc lesz a sikerek adott számú bekövetkezése előtt a siker adott valószínűsége esetén"}, "NEGBINOMDIST": {"a": "(kudarc_sz<PERSON>m; si<PERSON><PERSON>k; valószínűség)", "d": "A negatív binomiális eloszlás értékét adja meg; annak a valószínűsége, hogy megadott számú kudarc lesz a sikerek megadott számú bekövetkezése előtt a siker adott valószínűsége esetén"}, "NORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; s<PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A normál eloszlás eloszlásfüggvényének értékét számítja ki a megadott középérték és szórás esetén"}, "NORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; s<PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A normális eloszlás értékét számítja ki a megadott középérték és szórás esetén."}, "NORM.INV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki a megadott középérték és szórás esetén."}, "NORMINV": {"a": "(valószínűség; közép<PERSON>rték; szórás)", "d": "A normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki a megadott középérték és szórás esetén."}, "NORM.S.DIST": {"a": "(z; eloszlásfv)", "d": "A standard normális eloszlás eloszlásfüggvényének értékét számítja ki (a standard normális eloszlás középértéke 0, szórása 1)"}, "NORMSDIST": {"a": "(z)", "d": "A standard normális eloszlás eloszlásfüggvényének értékét számítja ki. A standard normális eloszlás középértéke 0, szórása 1."}, "NORM.S.INV": {"a": "(valószínűség)", "d": "A standard normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki. A standard normális eloszlás középértéke 0, szórása 1."}, "NORMSINV": {"a": "(valószínűség)", "d": "A standard normális eloszlás eloszlásfüggvénye inverzének értékét számítja ki. A standard normális eloszlás középértéke 0, szórása 1."}, "PEARSON": {"a": "(tömb1; tömb2)", "d": "A Pearson-<PERSON><PERSON><PERSON> (r) számítja ki "}, "PERCENTILE": {"a": "(tömb; k)", "d": "Egy tartományban található értékek k-adik percentilisét, a<PERSON><PERSON>kosztályát adja eredményül."}, "PERCENTILE.EXC": {"a": "(tömb; k)", "d": "Egy tartományban található értékek k-adik percentilisét, a<PERSON><PERSON>ékosztályát adja eredményül, ahol k a 0 és 1 közötti tartományban található, a végpontok nélkül"}, "PERCENTILE.INC": {"a": "(tömb; k)", "d": "Egy tartományban található értékek k-adik percentilisét, a<PERSON><PERSON>ékosztályát adja eredményül, ahol k a 0 és 1 közötti tartományban található, a végpontokat is beleértve"}, "PERCENTRANK": {"a": "(tömb; x; [pontoss<PERSON>g])", "d": "Egy értéknek egy adathalmazon belül vett s<PERSON>ázal<PERSON> (elhelyezkedését) adja meg."}, "PERCENTRANK.EXC": {"a": "(tömb; x; [pontoss<PERSON>g])", "d": "Egy értéknek egy adathalmazon belül vett százalékos <PERSON> (elhelyezkedését) adja meg (0 és 1 között, a végpontok nélkül)"}, "PERCENTRANK.INC": {"a": "(tömb; x; [pontoss<PERSON>g])", "d": "Egy értéknek egy adathalmazon belül vett százalékos <PERSON> (elhelyezkedését) adja meg (0 és 1 között, a végpontokat is beleértve)"}, "PERMUT": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú objektum k-ad osztá<PERSON>ú ismétlés nélküli variációinak számát számítja ki."}, "PERMUTATIONA": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú objektum k-ad osztá<PERSON><PERSON> ismétléses variációinak számát számítja ki"}, "PHI": {"a": "(x)", "d": "A standard normális elosz<PERSON>ás sűrűségfüggvényének értékét számítja ki."}, "POISSON": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A Poisson-eloszlás értékét számítja ki."}, "POISSON.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; eloszlásfv)", "d": "A Poisson-eloszlás értékét számítja ki."}, "PROB": {"a": "(x_tartomány; val_tartomány; als<PERSON>_határ; [fels<PERSON>_határ])", "d": "Annak v<PERSON>ószínűségét számítja ki, hogy adott értékek két határérték közé <PERSON>, vagy az alsó határértékkel egy<PERSON>lőek"}, "QUARTILE": {"a": "(tömb; kvart)", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> (negyedszintjét) számítja ki."}, "QUARTILE.INC": {"a": "(tömb; kvart)", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (negyedszintjét) számítja ki az értékek percentilise, a<PERSON>z százalékosztálya alapján (0 és 1 között, a végpontokat is beleértve)."}, "QUARTILE.EXC": {"a": "(tömb; kvart)", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (negyedszintjét) számítja ki az értékek percentilise, a<PERSON>z százalékosztálya alapján (0 és 1 között, a végpontok nélkül)."}, "RANK": {"a": "(szám; hiv; [sorrend])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy szám nagysága alapján hányadik egy számsorozatban."}, "RANK.AVG": {"a": "(szám; hiv; [sorrend])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy szám nagysága alapján hányadik egy számsorozatban; ha több érték sorszáma a<PERSON>, a sorszámok átlagát adja v<PERSON>za"}, "RANK.EQ": {"a": "(szám; hiv; [sorrend])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy szám nagysága alapján hányadik egy számsorozatban; ha több érték sorszáma azon<PERSON>, a halmazhoz tartozó legmagasabb sorszámot adja vissza"}, "RSQ": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "Kiszámítja a Pearson-féle szorzatmomentum korrelációs együtthatójának négyzetét a megadott adatpontok esetén."}, "SKEW": {"a": "(szám1; [szám2]; ...)", "d": "Egy eloszlás ferdeségét határozza meg; a ferdeség az eloszlás átlaga körül vett aszimmetria mértékét adja meg."}, "SKEW.P": {"a": "(szám1; [szám2]; ...)", "d": "Egy eloszlás ferdeségét határozza meg egy sokaság al<PERSON>; a ferdeség az eloszlás átl<PERSON> körül vett aszimmetria mértékét adja meg."}, "SLOPE": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "A megadott adatpontokon át h<PERSON>ó lineáris regress<PERSON> egyenes meredekségét számítja ki."}, "SMALL": {"a": "(tömb; k)", "d": "<PERSON><PERSON> adathalmaz k-adik legkisebb elemét adja eredményül. Például az ötödik legkisebb számot"}, "STANDARDIZE": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; szórás)", "d": "Középértékkel és szórással megadott eloszlásból normalizált értéket ad eredményül."}, "STDEV": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a szórásra (a mintában lévő logikai értékeket és szöveget nem veszi figyelembe)."}, "STDEV.P": {"a": "(szám1; [szám2]; ...)", "d": "Az argumentumokkal megadott statisztikai sokaság egészéből kiszámítja annak szórását (a logikai értékeket és a szövegeket figyelmen kívül hagyja)"}, "STDEV.S": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a szórásra (a mintában lévő logikai értékeket és szöveget nem veszi figyelembe)"}, "STDEVA": {"a": "(érték1; [érték2]; ...)", "d": "Minta alapján becslést ad a sokaság szórására, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít."}, "STDEVP": {"a": "(szám1; [szám2]; ...)", "d": "Az argumentumokkal megadott statisztikai sokaság egészéből kiszámítja annak szórását (a logikai értékeket és a szövegeket figyelmen kívül hagyja)."}, "STDEVPA": {"a": "(érték1; [érték2]; ...)", "d": "A statisztikai sokaság egészéből kiszámítja a szórást, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít."}, "STEYX": {"a": "(ismert_y-ok; ismert_x-ek)", "d": "A regresszióban az egyes x-értékek alapján meghatározott y-értékek standard hibáját számítja ki."}, "TDIST": {"a": "(x; sza<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; sz<PERSON><PERSON>)", "d": "A Student-féle t-el<PERSON><PERSON><PERSON><PERSON> értékét számítja ki."}, "TINV": {"a": "(valószínűség; szabadságfok)", "d": "A Student-féle t-eloszlás kétszélű inverzét számítja ki."}, "T.DIST": {"a": "(x; s<PERSON><PERSON>ságfok; eloszlásfv)", "d": "A Student-<PERSON><PERSON><PERSON> b<PERSON>z<PERSON>lű t-eloszlás értékét számítja ki"}, "T.DIST.2T": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A Student-féle t-eloszlás kétszélű értékét számítja ki."}, "T.DIST.RT": {"a": "(x; s<PERSON><PERSON><PERSON>ág<PERSON><PERSON>)", "d": "A Student-féle t-el<PERSON><PERSON><PERSON><PERSON> jobbszélű értékét számítja ki."}, "T.INV": {"a": "(valószínűség; szabadságfok)", "d": "A Student-féle t-eloszlás balszélű inverzét számítja ki"}, "T.INV.2T": {"a": "(valószínűség; szabadságfok)", "d": "A Student-féle t-eloszlás kétszélű inverzét számítja ki"}, "T.TEST": {"a": "(tömb1; tömb2; s<PERSON><PERSON><PERSON>; t<PERSON><PERSON>)", "d": "A Student-féle t-próbához tartozó valószínűséget számítja ki"}, "TREND": {"a": "(ismert_y-ok; [ismert_x-ek]; [új_x-ek]; [konstans])", "d": "Visszatérési érték a legkisebb négyzetek módszere szerint az ismert adatpontokra fektetett egyenes segítségével lineá<PERSON> trend számértéke"}, "TRIMMEAN": {"a": "(tömb; százalék)", "d": "<PERSON><PERSON> ad<PERSON>kből álló halmaz középső részének átlagát számítja ki"}, "TTEST": {"a": "(tömb1; tömb2; s<PERSON><PERSON><PERSON>; t<PERSON><PERSON>)", "d": "A Student-féle t-próbához tartozó valószínűséget számítja ki"}, "VAR": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a varianciára (a mintában lévő logikai értékeket és szövegeket figyelmen kívül hagyja)."}, "VAR.P": {"a": "(szám1; [szám2]; ...)", "d": "Egy statisztikai sokaság varianciáját számítja ki (a sokaságban lévő logikai értékeket és szövegeket figyelmen kívül hagyja)"}, "VAR.S": {"a": "(szám1; [szám2]; ...)", "d": "Minta alapján becslést ad a varianciára (a mintában lévő logikai értékeket és szövegeket figyelmen kívül hagyja)"}, "VARA": {"a": "(érték1; [érték2]; ...)", "d": "Minta alapján becslést ad a sokaság varianciájára, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít."}, "VARP": {"a": "(szám1; [szám2]; ...)", "d": "Egy statisztikai sokaság varianciáját számítja ki (a sokaságban lévő logikai értékeket és szövegeket figyelmen kívül hagyja)."}, "VARPA": {"a": "(érték1; [érték2]; ...)", "d": "A statisztikai sokaság egészéből kiszámítja a varianciát, a logikai értékek és a szövegek figyelembevételével. A szöveg és a HAMIS logikai érték 0-nak, az IGAZ logikai érték 1-nek számít."}, "WEIBULL": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "<PERSON>bull-f<PERSON><PERSON> eloszlás <PERSON>rtékét számítja ki"}, "WEIBULL.DIST": {"a": "(x; alfa; b<PERSON>ta; eloszlásfv)", "d": "<PERSON>bull-f<PERSON><PERSON> eloszlás <PERSON>rtékét számítja ki"}, "Z.TEST": {"a": "(tömb; x; [szigma])", "d": "Az egyszélű z-próbával kapott P-értéket (az aggregált elsőfajú hiba nagyságát) számítja ki."}, "ZTEST": {"a": "(tömb; x; [szigma])", "d": "Az egyszélű z-próbával kapott P-értéket (az aggregált elsőfajú hiba nagyságát) számítja ki."}, "ACCRINT": {"a": "(kibocs<PERSON>tás; <PERSON><PERSON><PERSON>_ka<PERSON>; elsz<PERSON>molás; ráta; névérték; gyakoriság; [alap]; [szám_mód])", "d": "Időszakosan kamatozó értékpapír felhalmozott kamatát adja eredményül."}, "ACCRINTM": {"a": "(kibocsátás; elszámolás; ráta; névérték; [alap])", "d": "Lejáratkor kamatozó értékpapír felhalmozott kamatát adja eredményül."}, "AMORDEGRC": {"a": "(költség; be<PERSON><PERSON><PERSON><PERSON>_d<PERSON>; <PERSON><PERSON><PERSON>_id<PERSON><PERSON><PERSON>; marad<PERSON>y<PERSON>k; id<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; [alap])", "d": "<PERSON>gy adott tárgyi eszköznek az egyes könyvelési időszakokra eső, a<PERSON><PERSON><PERSON><PERSON> felosz<PERSON>tt lineáris értékcsökkenését számítja ki."}, "AMORLINC": {"a": "(költség; be<PERSON><PERSON><PERSON><PERSON>_d<PERSON>; <PERSON><PERSON><PERSON>_id<PERSON><PERSON><PERSON>; marad<PERSON>y<PERSON>k; id<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; [alap])", "d": "<PERSON>gy adott tárgyi eszköznek az egyes könyvelési időszakokra eső, a<PERSON><PERSON><PERSON><PERSON> felosz<PERSON>tt lineáris értékcsökkenését számítja ki."}, "COUPDAYBS": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "A szelvénykifizetési időszak kezdetétől az elszámolási dátumig tartó napok számát adja eredményül."}, "COUPDAYS": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "<PERSON><PERSON> elszá<PERSON> időpont<PERSON>t is tartalmazó szelvénykifizetési időszak hosszát adja eredményül."}, "COUPDAYSNC": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "Az elszámolás időpontjától a következő szelvénykifizetési dátumig tartó napok számát adja eredményül."}, "COUPNCD": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "<PERSON>z elszámolást követő legelső szelvénykifizetési dátumot adja eredményül."}, "COUPNUM": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "Az elszámolás és a lejárat időpontja között kifizetendő szelvények számát adja eredményül."}, "COUPPCD": {"a": "(elsz<PERSON><PERSON>l<PERSON>; lej<PERSON><PERSON>; gyakoriság; [alap])", "d": "<PERSON><PERSON><PERSON> el<PERSON>tti utolsó szelvénykifizetési dátumot adja eredményül."}, "CUMIPMT": {"a": "(r<PERSON><PERSON>; időszakok_száma; j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; kez<PERSON><PERSON>_időszak; <PERSON><PERSON><PERSON><PERSON>_időszak; típus)", "d": "Két fizetési időszak között kifizetett kamat halmozott értékét adja eredményül."}, "CUMPRINC": {"a": "(r<PERSON><PERSON>; időszakok_száma; j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; kez<PERSON><PERSON>_időszak; <PERSON><PERSON><PERSON><PERSON>_időszak; típus)", "d": "Két fizetési időszak között kifizetett részletek halmozott (kamatot nem tartalmazó) értékét adja eredményül."}, "DB": {"a": "(költség; maradván<PERSON>k; le<PERSON><PERSON><PERSON><PERSON>_id<PERSON>; id<PERSON><PERSON><PERSON>; [hónap])", "d": "Eredményül egy eszköz adott időszak alatti értékcsökkenését számítja ki a lineáris leírási modell alkalmazásával."}, "DDB": {"a": "(költség; marad<PERSON>k; le<PERSON><PERSON><PERSON><PERSON>_id<PERSON>; id<PERSON><PERSON><PERSON>; [faktor])", "d": "Egy eszköz értékcsökkenését számítja ki egy adott időszakra vonat<PERSON> a progresszív vagy egyéb megadott leírási modell alkalmazásával."}, "DISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; <PERSON>r; v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; [alap])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>r <PERSON>ítolási rátáját adja er<PERSON>ményül."}, "DOLLARDE": {"a": "(tört_ért<PERSON>k; tört_nevez<PERSON>)", "d": "Közönséges törtként megadott számot tizedes törtté alakít át."}, "DOLLARFR": {"a": "(tized<PERSON>_<PERSON>rt<PERSON>k; tört_nevez<PERSON>)", "d": "Tizedes törtként megadott számot közönséges törtté alakít át."}, "DURATION": {"a": "(elsz<PERSON><PERSON><PERSON>ás; le<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; hozam; gyakoris<PERSON>g; [alap])", "d": "Időszakonkénti kamatfizetésű értékpapír éves <PERSON>-kamatlábérzékenységét adja eredményül."}, "EFFECT": {"a": "(névleges_kamatláb; időszak_per_év)", "d": "<PERSON>z éves tényleges kamatláb értékét adja eredményül."}, "FV": {"a": "(r<PERSON><PERSON>; időszakok_száma; r<PERSON><PERSON><PERSON>; [j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]; [típus])", "d": "Egy befektetés jövőbeli értékét számítja ki, időszakonkénti állandó összegű befizetéseket és állandó kamatlábat véve alapul."}, "FVSCHEDULE": {"a": "(tőke; ütemezés)", "d": "A kezdőtőke adott kamatlábak szerint megnövelt jövőbeli értékét adja eredményül."}, "INTRATE": {"a": "(elsz<PERSON>molás; lej<PERSON>rat; befektetés; visszaváltás; [alap])", "d": "<PERSON><PERSON> le<PERSON> telje<PERSON> lekötött értékpapír ka<PERSON>r<PERSON>áj<PERSON>t adja er<PERSON>."}, "IPMT": {"a": "(r<PERSON><PERSON>; id<PERSON><PERSON><PERSON>; id<PERSON>szakok_száma; j<PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_érték]; [típus])", "d": "Egy befektetés részletfizetési összegeinek nagyságát számítja ki egy adott időszakra időszakonkénti, állandó összegű részletek és állandó kamatl<PERSON>."}, "IRR": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; [be<PERSON><PERSON><PERSON>])", "d": "A megadott pénzáramlás-számsor (cash flow) be<PERSON><PERSON> megtérülési rátáját számítja ki."}, "ISPMT": {"a": "(r<PERSON><PERSON>; id<PERSON>sz<PERSON>; időszakok_száma; jelenérték)", "d": "A befektetés időtartamának adott időszakára eső kamatösszeget számítja ki."}, "MDURATION": {"a": "(elsz<PERSON><PERSON><PERSON>ás; le<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; hozam; gyakoris<PERSON>g; [alap])", "d": "Egy 100 Ft névértékű értékpapír <PERSON>-féle módosított árfolyam-kamatlábérzékenységét adja eredmé<PERSON>ül."}, "MIRR": {"a": "(ért<PERSON><PERSON>k; pénzü<PERSON><PERSON>_kamat; újrabefektetési_ráta)", "d": "A befektetés belső megtérülési rátáját számítja ki ismétlődő pénzáramlások esetén a befektetés költségét és az újrabefektetett összegek után jár<PERSON> kamatot is figyelembe véve."}, "NOMINAL": {"a": "(tényleges_kamatláb; időszak_per_év)", "d": "<PERSON>z éves névleges kamatláb értékét adja eredményül."}, "NPER": {"a": "(r<PERSON><PERSON>; r<PERSON><PERSON><PERSON>; j<PERSON><PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_<PERSON>]; [típus])", "d": "A befektetési időszakok számát adja meg időszakonkénti, állandó összegű részletfizetések és állandó ka<PERSON>."}, "NPV": {"a": "(r<PERSON>ta; érték1; [érték2]; ...)", "d": "Egy befektetés nettó jelenértékét számítja ki ismert diszkontráta és jövőbeli befizetések (negatív értékek), illetve bev<PERSON> (pozitív értékek) mellett."}, "ODDFPRICE": {"a": "(elszámolás; lej<PERSON>rat; kibocsátás; el<PERSON><PERSON>_szelvény; ráta; hozam; visszaváltás; gyakoriság; [alap])", "d": "Egy 100 Ft névértékű, a futamidő elején töredékidőszakos értékpapír árát adja eredményül."}, "ODDFYIELD": {"a": "(elsz<PERSON>molás; lej<PERSON>rat; kibocsátás; el<PERSON><PERSON>_szelvény; r<PERSON><PERSON>; ár; visszav<PERSON>ltás; gyakoriság; [alap])", "d": "<PERSON><PERSON>, a futamidő elején töredékidőszakos értékpapír ho<PERSON>át adja eredményül."}, "ODDLPRICE": {"a": "(elsz<PERSON><PERSON>l<PERSON>; le<PERSON><PERSON><PERSON>; uto<PERSON><PERSON>_kamat; ráta; hozam; visszaváltás; gyakoriság; [alap])", "d": "Egy 100 Ft névértékű, a futamidő végén töredékidőszakos értékpapír árát adja eredményül."}, "ODDLYIELD": {"a": "(elsz<PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; uto<PERSON><PERSON>_kamat; ráta; ár; visszaváltás; gyakoriság; [alap])", "d": "<PERSON><PERSON>, a futamidő végén töredékidőszakos értékpapír hozamát adja eredményül."}, "PDURATION": {"a": "(<PERSON><PERSON><PERSON>; j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Kiszámítja az ahhoz szükséges időszakok számát, hogy egy befektetés elérjen egy megadott értéket."}, "PMT": {"a": "(r<PERSON><PERSON>; időszakok_száma; j<PERSON><PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_<PERSON>ék]; [típus])", "d": "A kölcsönre vonatkozó törlesztőösszeget számítja ki állandó nagyságú törlesztőrészletek és állandó kamatl<PERSON>."}, "PPMT": {"a": "(r<PERSON><PERSON>; id<PERSON><PERSON><PERSON>; id<PERSON>szakok_száma; j<PERSON><PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_érték]; [típus])", "d": "Egy befektetés tőketörlesztésének nagyságát számítja ki egy adott időszakra időszakonkénti, állandó összegű részletfizetések és állandó kamatl<PERSON>."}, "PRICE": {"a": "(elsz<PERSON>molás; lej<PERSON><PERSON>; r<PERSON><PERSON>; hozam; visszavált<PERSON>; gyakoriság; [alap])", "d": "Egy 100 Ft névértékű, időszakosan kamatozó értékpapír árát adja eredményül."}, "PRICEDISC": {"a": "(elszámolás; lej<PERSON>rat; leszámítolás; visszaváltás; [alap])", "d": "Egy 100 Ft névértékű leszámítolt értékpapír árát adja eredményül."}, "PRICEMAT": {"a": "(elszámolás; lej<PERSON>rat; kibocsátás; ráta; hozam; [alap])", "d": "Egy 100 Ft névértékű, a lejáratkor kamatozó értékpapír árát adja eredmé<PERSON>ül."}, "PV": {"a": "(r<PERSON><PERSON>; időszakok_száma; r<PERSON><PERSON><PERSON>; [jö<PERSON><PERSON><PERSON><PERSON>_<PERSON>rték]; [típus])", "d": "Egy befektetés jelenértékét számítja ki: azt a jelenbeli egyösszegű befizetést, amely egyenértékű a jövőbeli befizetések összegével."}, "RATE": {"a": "(időszakok_száma; r<PERSON><PERSON><PERSON>; j<PERSON><PERSON><PERSON>rt<PERSON>k; [jö<PERSON><PERSON><PERSON>i_érték]; [típus]; [be<PERSON><PERSON><PERSON>])", "d": "Egy kölcsön vagy befektetés időtartama alatt az egy időszakra eső kamatláb nagyságát számítja ki. Például 6%-os <PERSON><PERSON> kamatláb negyedévenkénti fizetéssel 6%/4."}, "RECEIVED": {"a": "(elszámolás; lej<PERSON>rat; befektetés; leszámítolás; [alap])", "d": "Egy lejá<PERSON>ig teljesen lekötött értékpapír lejáratakor kapott összeget adja eredményül."}, "RRI": {"a": "(időszakok_száma; jelen<PERSON>rt<PERSON>k; jövőérték)", "d": "Kiszámít egy befektetés növekedésével egyenértékű kamatlábat."}, "SLN": {"a": "(költség; marad<PERSON>k; leír<PERSON>i_idő)", "d": "Egy tárgyi eszköz egy időszakra eső amortizációját adja meg, bruttó érték szerinti lineáris leírási kulcsot alkalmazva."}, "SYD": {"a": "(költség; maradván<PERSON>k; le<PERSON><PERSON><PERSON><PERSON>_id<PERSON>; id<PERSON>szak)", "d": "Egy tárgyi eszköz értékcsökkenését számítja ki adott időszakra az évek számjegyösszegével dolgozó módszer alapján."}, "TBILLEQ": {"a": "(kiegy<PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; leszámítolás)", "d": "Egy kincstárjegy kötvény-egyenértékű hozamát adja eredményül."}, "TBILLPRICE": {"a": "(kiegy<PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; leszámítolás)", "d": "Egy 100 Ft névértékű kincstárjegy árát adja eredményül."}, "TBILLYIELD": {"a": "(k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; <PERSON>r)", "d": "<PERSON><PERSON> kin<PERSON>tárjegy hozamát adja eredményül."}, "VDB": {"a": "(költség; marad<PERSON>k; <PERSON><PERSON><PERSON><PERSON><PERSON>_idő; kez<PERSON><PERSON>_időszak; <PERSON><PERSON><PERSON><PERSON>_időszak; [faktor]; [nem_vált])", "d": "Egy tárgyi eszköz amortizációját számítja ki egy megadott vagy részidőszakra a dupla gyorsaságú csökkenő egyenleg módszerének, vagy más megadott módszernek az alkalmazásával."}, "XIRR": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; d<PERSON><PERSON><PERSON>; [be<PERSON><PERSON><PERSON>])", "d": "Ütemezett pénzáramlás (cash flow) belső megtérülési rátáját adja eredményül."}, "XNPV": {"a": "(r<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; d<PERSON><PERSON><PERSON>)", "d": "Ütemezett pénzáramlás (cash flow) nettó jelenértékét adja eredményül."}, "YIELD": {"a": "(elsz<PERSON><PERSON>lás; le<PERSON><PERSON><PERSON>; r<PERSON><PERSON>; <PERSON>r; v<PERSON><PERSON>v<PERSON>; gyakoriság; [alap])", "d": "Időszakosan kamatozó értékpapír ho<PERSON>t adja eredményül."}, "YIELDDISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; le<PERSON><PERSON><PERSON>; <PERSON>r; v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; [alap])", "d": "<PERSON><PERSON> leszámítolt értékpapír (péld<PERSON>ul kincstárjegy) <PERSON><PERSON> ho<PERSON> adja <PERSON>ül."}, "YIELDMAT": {"a": "(elsz<PERSON>molás; lej<PERSON>rat; kibocsátás; r<PERSON><PERSON>; ár; [alap])", "d": "Lejáratkor kamatozó értékpapír éves <PERSON> adja eredményül."}, "ABS": {"a": "(szám)", "d": "Egy szám abszolút értékét adja eredményül (a számot előjel nélkül)"}, "ACOS": {"a": "(szám)", "d": "Egy szám arkusz koszinuszát adja meg radiánban, a 0 - Pi tartományban. Az arkusz koszinusz az a szög, amelynek a koszinusza a megadott szám"}, "ACOSH": {"a": "(szám)", "d": "Egy szám area koszinusz hiperbolikusát számítja ki."}, "ACOT": {"a": "(szám)", "d": "Egy szám arkusz kotangensét adja meg radiánban, a 0–Pi tartományban"}, "ACOTH": {"a": "(szám)", "d": "Egy s<PERSON>m inverz hiperbolikus kotangensét adja meg"}, "AGGREGATE": {"a": "(függv_szám; beállítások; hiv1; ...)", "d": "Összegzést ad eredményül listában vagy adatbázi<PERSON>ban"}, "ARABIC": {"a": "(szöveg)", "d": "Római sz<PERSON>mot arab számmá alakí<PERSON>t"}, "ASC": {"a": "(szöveg)", "d": "Ez a függvény a k<PERSON>tb<PERSON><PERSON><PERSON> (DBCS) karakterkészletet használó nyelveknél egybájtos karakterekre cseréli a kétbájtos karaktereket"}, "ASIN": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON> a<PERSON> s<PERSON> adja meg radi<PERSON>, a -Pi/2 - Pi/2 tartományban"}, "ASINH": {"a": "(szám)", "d": "Egy szám area szinusz hiperbolikusát számítja ki."}, "ATAN": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON> ark<PERSON> tan<PERSON>t adja meg radi<PERSON>, a -Pi/2 -Pi/2 tartományban"}, "ATAN2": {"a": "(x_szám; y_szám)", "d": "A megadott x- és y-koordináták alapján számítja ki az arkusz tangens értéket radiánban -Pi és Pi között, -<PERSON> kivételével"}, "ATANH": {"a": "(szám)", "d": "A szám tangens hiperbolikusát számítja ki."}, "BASE": {"a": "(szám; alap; [min_hossz])", "d": "Átalakít egy számot a megadott alapú (számrendszerű) szöveges alakra."}, "CEILING": {"a": "(szám; pontosság)", "d": "Egy számot a pontosságként megadott érték legközelebb eső többszörösére kerekít fel."}, "CEILING.MATH": {"a": "(szám; [pontosság]; [mód])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít fel."}, "CEILING.PRECISE": {"a": "(szám; [pontosság])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít"}, "COMBIN": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú elem összes lehetséges kombinációinak számát számítja ki"}, "COMBINA": {"a": "(szám; hány_kiválasztott)", "d": "Adott számú elem összes lehetséges ismétléses kombinációinak számát számítja ki"}, "COS": {"a": "(szám)", "d": "<PERSON><PERSON> szög k<PERSON>zinuszát számítja ki."}, "COSH": {"a": "(szám)", "d": "Egy sz<PERSON>m koszinusz hiperbolikusát számítja ki."}, "COT": {"a": "(szám)", "d": "<PERSON><PERSON> szög kotangensét számítja ki"}, "COTH": {"a": "(szám)", "d": "Egy szám hiperbolikus kotangensét számítja ki"}, "CSC": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON>ög k<PERSON>zekánsát számítja ki"}, "CSCH": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON><PERSON>g hiperbolikus koszekánsát számítja ki"}, "DECIMAL": {"a": "(szám; alap)", "d": "Egy szám megadott számrendszerbeli szöveges alakját decimális számmá alakítja."}, "DEGREES": {"a": "(szög)", "d": "Radiánt fokká alakít <PERSON>t"}, "ECMA.CEILING": {"a": "(szám; pontosság)", "d": "Egy számot a pontosságként megadott érték legközelebb eső többszörösére kerekít fel"}, "EVEN": {"a": "(szám)", "d": "Egy pozitív számot felfel<PERSON>, egy negatív számot pedig lefelé kerekít a legközelebbi páros egész számra"}, "EXP": {"a": "(szám)", "d": "e-nek adott kitevőjű hatványát számítja ki."}, "FACT": {"a": "(szám)", "d": "Egy szám faktoriálisát számítja ki. A szám faktoriálisa = 1*2*3*...*szám."}, "FACTDOUBLE": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON>m dupla faktoriális<PERSON>t adja eredményül."}, "FLOOR": {"a": "(szám; pontosság)", "d": "<PERSON>gy s<PERSON><PERSON><PERSON> le<PERSON> k<PERSON>, a pontosságként megadott érték legközelebb eső többszörösére."}, "FLOOR.PRECISE": {"a": "(szám; [pontosság])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére lefe<PERSON> kerek<PERSON>t"}, "FLOOR.MATH": {"a": "(szám; [pontosság]; [mód])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít le."}, "GCD": {"a": "(szám1; [szám2]; ...)", "d": "A legnagyobb közös osztót számítja ki"}, "INT": {"a": "(szám)", "d": "Egy szá<PERSON> le<PERSON> kerekít a legközelebbi egészre."}, "ISO.CEILING": {"a": "(szám; [pontosság])", "d": "Egy számot a legközelebbi egészre vagy a pontosságként megadott érték legközelebb eső többszörösére kerekít. A szám előjelétől függetlenül a számot felfelé kerekíti. Ugyanakkor ha a szám vagy a pontosságként megadott érték nulla, a függvény nullát ad vissza."}, "LCM": {"a": "(szám1; [szám2]; ...)", "d": "A legkisebb közös többszöröst számítja ki"}, "LN": {"a": "(szám)", "d": "Egy sz<PERSON>m természetes logaritmusát számítja ki."}, "LOG": {"a": "(szám; [alap])", "d": "<PERSON><PERSON> szám megadott alapú logaritmusát számítja ki."}, "LOG10": {"a": "(szám)", "d": "Egy szám 10-es alapú logaritmusát számítja ki."}, "MDETERM": {"a": "(tömb)", "d": "<PERSON><PERSON> tömb mátrix-determinánsát számítja ki."}, "MINVERSE": {"a": "(tömb)", "d": "<PERSON>gy tömbben tárolt mátrix inverz mátrixát adja er<PERSON>ül."}, "MMULT": {"a": "(tömb1; tömb2)", "d": "Két tömb mátrix-szorzatát adja meg. Az eredménytömbnek ugyanannyi sora lesz, mint tömb1-nek és ugyanannyi oszlopa, mint tömb2-nek."}, "MOD": {"a": "(szám; osztó)", "d": "A számnak az osztóval való elosztása után kapott maradékát adja eredményül."}, "MROUND": {"a": "(szám; pontosság)", "d": "A pontosság legközelebbi többszörösére kerekített értéket ad eredményül."}, "MULTINOMIAL": {"a": "(szám1; [szám2]; ...)", "d": "Egy számkészlet polinomját számítja ki"}, "MUNIT": {"a": "(dimenzió)", "d": "A megadott dimenziójú egységmátrixot adja vissza."}, "ODD": {"a": "(szám)", "d": "Egy pozitív számot felfel<PERSON>, egy negatív számot pedig lefelé kerekít a legközelebbi páratlan egész számra"}, "PI": {"a": "()", "d": "A pi értékét adja vissza 15 jegy pontossággal (3,14159265358979)."}, "POWER": {"a": "(szám; kitevő)", "d": "<PERSON><PERSON> sz<PERSON>m adott kitevőjű hatványát számítja ki."}, "PRODUCT": {"a": "(szám1; [szám2]; ...)", "d": "Az összes argumentumként megadott szám szorzatát számítja ki"}, "QUOTIENT": {"a": "(számláló; nevező)", "d": "<PERSON><PERSON> hán<PERSON>dos egész részét adja eredményül."}, "RADIANS": {"a": "(szög)", "d": "Fokot radiánná alakít á<PERSON>."}, "RAND": {"a": "()", "d": "0-n<PERSON>l nagyobb vagy azzal egyenlő és 1-nél kisebb egyenletesen elosztott véletlenszámot ad eredményül (az újraszámítástól függően)."}, "RANDARRAY": {"a": "([sorok]; [oszlopok]; [min]; [max]; [egész])", "d": "Véletlenszám tömböt ad eredményül"}, "RANDBETWEEN": {"a": "(alsó; felső)", "d": "<PERSON><PERSON><PERSON> adott szám közé eső véletlen számot <PERSON>."}, "ROMAN": {"a": "(szám; [alak])", "d": "Egy arab számot szövegként római számra alakí<PERSON> át"}, "ROUND": {"a": "(szám; hány_számjegy)", "d": "<PERSON><PERSON> sz<PERSON><PERSON> adott szá<PERSON>ú számjegyre kerekít."}, "ROUNDDOWN": {"a": "(szám; hány_számjegy)", "d": "<PERSON><PERSON> s<PERSON><PERSON><PERSON>, a nulla felé kere<PERSON>t."}, "ROUNDUP": {"a": "(szám; hány_számjegy)", "d": "<PERSON><PERSON> s<PERSON><PERSON><PERSON>, a nullától távolabbra kerekít."}, "SEC": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON><PERSON>g s<PERSON>án<PERSON>t számítja ki"}, "SECH": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON><PERSON>g hiperbolikus szekánsát számítja ki"}, "SERIESSUM": {"a": "(x; n; m; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Hatványsor összegét adja eredményül."}, "SIGN": {"a": "(szám)", "d": "Egy szám előjelét határozza meg: pozitív szám esetén 1, z<PERSON>rus esetén 0, negatív szám esetén -1."}, "SIN": {"a": "(szám)", "d": "<PERSON><PERSON> s<PERSON>g s<PERSON>t számítja ki."}, "SINH": {"a": "(szám)", "d": "<PERSON><PERSON> sz<PERSON>m szin<PERSON>z hiperbolikusát számítja ki."}, "SQRT": {"a": "(szám)", "d": "Egy szám négyzetgyökét számítja ki"}, "SQRTPI": {"a": "(szám)", "d": "A (szám * pi) érték négyzetgyökét adja v<PERSON>za."}, "SUBTOTAL": {"a": "(függv_szám; hiv1; ...)", "d": "Listában vagy adatbázisban részösszeget ad vissza"}, "SUM": {"a": "(szám1; [szám2]; ...)", "d": "Az összes számot összeadja egy adott cellatartományban."}, "SUMIF": {"a": "(tartomány; kritérium; [összeg_tartomány])", "d": "A megadott feltételnek vagy kritériumnak eleget tevő cellákban található értékeket adja ö<PERSON>ze."}, "SUMIFS": {"a": "(összegtartomány; kritériumtartomány; kritérium; ...)", "d": "A megadott feltétel- vagy kritériumkészletnek eleget tevő cellákban található értékeket adja ö<PERSON>ze"}, "SUMPRODUCT": {"a": "(tömb1; [tömb2]; [tömb3]; ...)", "d": "Eredményül a megadott tartományok vagy tömbök számelemei szorzatának az összegét adja"}, "SUMSQ": {"a": "(szám1; [szám2]; ...)", "d": "Argumentumai négyzetének összegét számítja ki. Az argumentumok számok, nevek, tömbök vagy számokat tartalmazó hivatkozások lehetnek."}, "SUMX2MY2": {"a": "(tömb_x; tömb_y)", "d": "Két tartomány vagy tömb megfelelő elemei négyzeteinek a különbségét összegezi"}, "SUMX2PY2": {"a": "(tömb_x; tömb_y)", "d": "Két tartomány vagy tömb megfelelő elemei összegének a négyzetösszegét összegezi"}, "SUMXMY2": {"a": "(tömb_x; tömb_y)", "d": "<PERSON><PERSON>t tartomány vagy tömb megfelelő elemei különbségének négyzetösszegét számítja ki"}, "TAN": {"a": "(szám)", "d": "<PERSON><PERSON> szög tangens<PERSON>t számítja ki."}, "TANH": {"a": "(szám)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> egy szám tangens hiperbolikusát adja v<PERSON>za"}, "TRUNC": {"a": "(szám; [hány_szám<PERSON><PERSON>])", "d": "Egy számot egé<PERSON><PERSON><PERSON> c<PERSON>, hogy a szám tizedes- vagy tö<PERSON> eltávolítja."}, "ADDRESS": {"a": "(sor_szám; oszlop_szám; [típus]; [a1]; [munkalapnév])", "d": "A megadott sor- és oszlopszám alapján cellahivatkozást hoz létre szöveges formában."}, "CHOOSE": {"a": "(index; érték1; [érték2]; ...)", "d": "Értékek egy listájából választ ki egy elemet vagy végrehajtandó <PERSON>, indexszám <PERSON>j<PERSON>"}, "COLUMN": {"a": "([hi<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> oszlopszámát adja eredményül."}, "COLUMNS": {"a": "(tömb)", "d": "Tömbben vagy hivatkozásban található oszlopok számát adja eredményül."}, "FORMULATEXT": {"a": "(hivatkozás)", "d": "Egy képletet karakterláncként ad vissza."}, "HLOOKUP": {"a": "(keresési_érték; táblázattömb; sorindex; [tartományi_keresés])", "d": "A táblázat vagy értéktömb felső sorában megkeresi a megadott értéket, és a megtalált értékhez tartozó oszlopból a megadott sorban elhelyezkedő értéket adja eredményül."}, "HYPERLINK": {"a": "(hivat<PERSON><PERSON><PERSON>_hely; [rö<PERSON>_név])", "d": "<PERSON><PERSON><PERSON>t vagy ugróhivatkozást létesít a merevlemezen, h<PERSON><PERSON><PERSON><PERSON><PERSON> kiszolgálón vagy az interneten tárolt dokumentum megnyitásához."}, "INDEX": {"a": "(tömb; sor_szám; [osz<PERSON>_szám]!hivat<PERSON><PERSON>ás; sor_szám; [oszlop_szám]; [ter<PERSON><PERSON>_szám])", "d": "Értéket vagy hivatkozást ad vissza egy adott tartomány bizonyos sorának és oszlopának metszéspontjában lévő cellából"}, "INDIRECT": {"a": "(hiv_szöveg; [a1])", "d": "Szövegdarab által meghatározott hivatkozást ad eredményül."}, "LOOKUP": {"a": "(keres<PERSON>i_érték; keres<PERSON>i_vektor; [ered<PERSON><PERSON>_vektor]!keresési_érték; tömb)", "d": "Egy sorból vagy egy oszlopból álló tartományban vagy tömbben keres meg értékeket. A korábbi verziókkal való kompatibilitásra szolgál"}, "MATCH": {"a": "(kere<PERSON><PERSON><PERSON>_<PERSON>; tá<PERSON>; [egyez<PERSON>_típus])", "d": "<PERSON><PERSON> adott értéknek megfelelő tömbelem viszonylagos helyét adja meg adott sorrendben"}, "OFFSET": {"a": "(hivatkozás; sorok; oszlopok; [magasság]; [sz<PERSON>lesség])", "d": "Megadott magasságú és szélességű hivatkozást ad meg egy hivatkozástól számított megadott sornyi és oszlopnyi távolságra."}, "ROW": {"a": "([hi<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> hi<PERSON> sorának számát adja meg."}, "ROWS": {"a": "(tömb)", "d": "<PERSON><PERSON> vagy tömb sorainak számát adja meg."}, "TRANSPOSE": {"a": "(tömb)", "d": "Függőleges cellatartományból vízszinteset állí<PERSON>, vagy fordítva"}, "UNIQUE": {"a": "(tömb; [by_col]; [exactly_once])", "d": " Egy tartomány vagy tömb egyedi értékeit adja v<PERSON>."}, "VLOOKUP": {"a": "(keresési_érték; tábl<PERSON>zat; oszlopindex; [tartományi_keresés])", "d": "Egy táblá<PERSON>t bal sz<PERSON>lső oszlopában megkeres egy értéket, és a megtalált értékhez tartozó sorból a megadott oszlopban elhelyezkedő értéket adja eredményül; alapesetben a táblázatnak növekvő sorrendbe rendezettnek kell lennie."}, "XLOOKUP": {"a": "(keres<PERSON><PERSON>_<PERSON>rt<PERSON><PERSON>; keres<PERSON>i_tömb; viss<PERSON><PERSON>nd<PERSON>_tömb; [ha_nincs_tal<PERSON><PERSON>]; [egy<PERSON><PERSON><PERSON><PERSON>_mód]; [keres<PERSON><PERSON>_mód])", "d": "Egyezéseket keres valamely tartományban vagy tömbben, és egy második tartományból vagy tömbből adja vissza a megfelelő elemet. Alapértelmezés szerint pontos egyezést használ a program."}, "CELL": {"a": "(infó<PERSON><PERSON><PERSON>; [hivat<PERSON><PERSON><PERSON>])", "d": "Egy cella formáz<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vagy tartalmáról ad információt"}, "ERROR.TYPE": {"a": "(hi<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> egy hibaértékhez tartozó számot ad vissza."}, "ISBLANK": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a hivatkozás üres cellára mutat-e, és IGAZ vagy HAMIS értéket ad vissza"}, "ISERR": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az adott érték eltér-e a #HIÁNYZIK hibaértéktől, és IGAZ vagy HAMIS értéket ad vissza"}, "ISERROR": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy hiba-e az adott érték, és IGAZ vagy HAMIS értéket ad vissza"}, "ISEVEN": {"a": "(szám)", "d": "A függvény eredménye IGAZ, ha a szám páros."}, "ISFORMULA": {"a": "(hivatkozás)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy hivatkozás képletet tartalmazó cellára mutat-e, és IGAZ vagy HAMIS értéket ad vissza."}, "ISLOGICAL": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték logikai érték-e (IGAZ vagy HAMIS), és IGAZ vagy HAMIS értéket ad vissza"}, "ISNA": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték a #HIÁNYZIK, és IGAZ vagy HAMIS értéket ad vissza"}, "ISNONTEXT": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték tényleg nem szöveg (az üres cellák nem számítanak szövegnek), és IGAZ vagy HAMIS értéket ad eredményül"}, "ISNUMBER": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték szám-e, és IGAZ vagy HAMIS értéket ad vissza"}, "ISODD": {"a": "(szám)", "d": "A függvény eredménye IGAZ, ha a s<PERSON>ám páratlan."}, "ISREF": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték hivatkozás-e, és IGAZ vagy HAMIS értéket ad vissza"}, "ISTEXT": {"a": "(érték)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az érték szöveg-e, és IGAZ vagy HAMIS értéket ad vissza"}, "N": {"a": "(érték)", "d": "A nem szám értéket számmá, a dátumot dátumértékk<PERSON>, az IGAZ értékből 1, b<PERSON>rmi egyébből 0 (zérus) lesz"}, "NA": {"a": "()", "d": "Eredménye a #HIÁNYZIK (az érték nem áll rendelkezésre) hibaérték."}, "SHEET": {"a": "([<PERSON><PERSON><PERSON><PERSON>])", "d": "A hivatkozott lap lapszámá<PERSON> adja v<PERSON>."}, "SHEETS": {"a": "([hi<PERSON><PERSON><PERSON><PERSON>])", "d": "A hivatkozásban szereplő lapok számát adja vissza."}, "TYPE": {"a": "(érték)", "d": "<PERSON>z adott érték adattípusát jelölő egész számot adja eredményül: szám = 1; szöveg = 2; logikai érték = 4; hibaérték = 16; tömb = 64; összetett adatok = 128"}, "AND": {"a": "(logikai1; [logikai2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy minden argumentumára érvényes-e az IGAZ, és ha minden argumentuma IGAZ, eredménye IGAZ"}, "FALSE": {"a": "()", "d": "A HAMIS logikai értéket adja eredményül."}, "IF": {"a": "(logika<PERSON>_vizsg<PERSON><PERSON>; [érték_ha_igaz]; [érték_ha_hamis])", "d": "Ellenőrzi a feltétel megfelelését, és ha a megadott feltétel IGAZ, az egyik értéket adja v<PERSON>, ha HAMIS, akkor a  másikat"}, "IFS": {"a": "(logikai_teszt; <PERSON>rték_ha_igaz; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, hogy egy vagy több feltétel teljesül-e, és eredményül visszaadja az első IGAZ feltételnek megfelelő értéket"}, "IFERROR": {"a": "(érték; érték_hiba_esetén)", "d": "Ha a kifejezés hiba, akkor az érték_hiba_esetén értéket, máskülönben magát a kifejezés értékét adja v<PERSON>za"}, "IFNA": {"a": "(érték; érték_ha_hiányzik)", "d": "A megadott értéket adja vissza, ha a kifejezés #HIÁNYZIK eredményt ad, egyébként a kifejezés eredményét adja vissza."}, "NOT": {"a": "(logikai)", "d": "Az IGAZ vagy HAMIS értéket az ellenkezőjére váltja"}, "OR": {"a": "(logikai1; [logikai2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy valamelyik értékére érvényes-e az IGAZ, és IGAZ vagy HAMIS eredményt ad vissza. Eredménye csak akkor HAMIS, ha minden argumentuma HAMIS"}, "SWITCH": {"a": "(kifejez<PERSON>; érték1; eredmény1; [alapérték_vagy_érték2]; [eredmény2]; ...)", "d": "Ez a függvény egy kifejezést értékel ki egy értéklistán, és az első egyező értéknek megfelelő eredményt adja vissza. Ha nincs egyezés, egy tetszőlegesen beállított alapértéket ad vissza"}, "TRUE": {"a": "()", "d": "Az IGAZ logikai értéket adja eredményül."}, "XOR": {"a": "(logikai1; [logikai2]; ...)", "d": "Logikai „kizárólagos vagy” műveletet végez az összes argumentummal"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Karakterek elválasztását megelőző szöveget küld vissza."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Karakterek elválasztását követő szöveget küld vissza."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "A szöveget sorokra vagy oszlopokra osztja fel a határolókkal."}, "WRAPROWS": {"a": "(ve<PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON>_<PERSON>, [<PERSON><PERSON><PERSON><PERSON>_e<PERSON><PERSON>])", "d": "<PERSON>gy sor- vagy oszlopvektor tördelése megadott számú érték után."}, "VSTACK": {"a": "(tömb1, [tömb2], ...)", "d": " A tömböket függőlegesen egy tömbbe halmozza."}, "HSTACK": {"a": "(tömb1, [tömb2], ...)", "d": " A tömböket vízszintesen egy tömbbe halmozza."}, "CHOOSEROWS": {"a": "(tö<PERSON>, sor_száma1, [sor_száma2], ...)", "d": "Sorokat ad vissza tömbből vagy hivatkozásból."}, "CHOOSECOLS": {"a": "(tö<PERSON>, oszlop_száma1, [oszlop_száma2], ...)", "d": "Csak a megadott oszlopokat adja vissza tömbből vagy hivatkozásból"}, "TOCOL": {"a": "(tömb, [minden<PERSON><PERSON><PERSON>], [vizs<PERSON><PERSON><PERSON>_o<PERSON>])", "d": " <PERSON><PERSON> adja vissza a tömböt. "}, "TOROW": {"a": "(tömb, [minden<PERSON><PERSON><PERSON>], [vizs<PERSON><PERSON><PERSON>_o<PERSON>])", "d": "<PERSON><PERSON> sork<PERSON>t adja vissza a tömböt."}, "WRAPCOLS": {"a": "(ve<PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON>_<PERSON>, [<PERSON><PERSON><PERSON><PERSON>_e<PERSON><PERSON>])", "d": "<PERSON>gy sor- vagy oszlopvektor tördelése megadott számú érték után."}, "TAKE": {"a": "(tömb, sorok, [oszlopok])", "d": "Sorokat vagy oszlopokat ad vissza a tömb elejéről vagy végéről."}, "DROP": {"a": "(tömb, sorok, [oszlopok])", "d": "Sorokat vagy oszlopokat távolít el a tömb elejéről vagy végéről."}}