var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "О редакторе презентаций", 
        "body": "Редактор презентаций - это онлайн- приложение, которое позволяет просматривать и редактировать презентации непосредственно в браузере . Используя онлайн- редактор презентаций, можно выполнять различные операции редактирования, как в любом десктопном редакторе, распечатывать отредактированные презентации, сохраняя все детали форматирования, или сохранять их на жесткий диск компьютера как файлы в формате PPTX, PDF, ODP, POTX, PDF/A, OTP. Для просмотра текущей версии программы, номера сборки и информации о владельце лицензии в онлайн-версии щелкните по значку на левой боковой панели инструментов. Для просмотра текущей версии программы и информации о владельце лицензии в десктопной версии для Windows выберите пункт меню О программе на левой боковой панели в главном окне приложения. В десктопной версии для Mac OS откройте меню ONLYOFFICE в верхней части и выберите пункт меню О программе ONLYOFFICE."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Дополнительные параметры редактора презентаций", 
        "body": "Вы можете изменить дополнительные параметры редактора презентаций. Для перехода к ним откройте вкладку Файл на верхней панели инструментов и выберите опцию Дополнительные параметры. Дополнительные параметры сгруппированы следующим образом: Редактирование и сохранение Автосохранение используется в онлайн-версии для включения/отключения опции автоматического сохранения изменений, внесенных при редактировании. Автовосстановление используется в десктопной версии для включения/отключения опции автоматического восстановления презентации в случае непредвиденного закрытия программы. Показывать кнопку Параметры вставки при вставке содержимого. Соответствующая кнопка будет появляться при вставке содержимого в презентацию. Совместная работа Подраздел Режим совместного редактирования позволяет задать предпочтительный режим просмотра изменений, вносимых в презентацию при совместной работе. Быстрый (по умолчанию). Пользователи, участвующие в совместном редактировании презентации, будут видеть изменения в реальном времени, как только они внесены другими пользователями. Строгий. Все изменения, внесенные участниками совместной работы, будут отображаться только после того, как вы нажмете на кнопку Сохранить с оповещением о наличии новых изменений. Показывать изменения других пользователей. Эта функция позволяет видеть изменения, которые вносят другие пользователи, в презентации, открытой только на просмотр, в Режиме просмотра в реальном времени. Правописание Опция Проверка орфографии используется для включения/отключения опции проверки орфографии. Пропускать слова из ПРОПИСНЫХ БУКВ. Слова, написанные прописными буквами, игнорируются при проверке орфографии. Пропускать слова с цифрами. Слова, в которых есть цифры, игнорируются при проверке орфографии. Меню Параметры автозамены... позволяет открыть параметры автозамены, такие как замена текста при вводе, распознанные функции, автоформат при вводе и другие. Рабочая область Опция Направляющие выравнивания используется для включения/отключения направляющих выравнивания, которые появляются при перемещении объектов и позволяют точно расположить их на слайде. Опция Иероглифы используется для включения/отключения отображения иероглифов. Опция Использовать клавишу Alt для навигации по интерфейсу с помощью клавиатуры используется для включения использования клавиши Alt / Option в сочетаниях клавиш. Опция Тема интерфейса используется для изменения цветовой схемы интерфейса редактора. Опция Системная позволяет редактору соответствовать системной теме интерфейса. Светлая цветовая гамма включает стандартные синий, белый и светло-серый цвета с меньшим контрастом элементов интерфейса, подходящие для работы в дневное время. Классическая светлая цветовая гамма включает стандартные синий, белый и светло-серый цвета. Темная цветовая гамма включает черный, темно-серый и светло-серый цвета, подходящие для работы в ночное время. Контрастная темная цветовая гамма включает черный, темно-серый и белый цвета с большим контрастом элементов интерфейса, выделяющих рабочую область файла. Опция Включить темный режим используется, чтобы сделать рабочую область темнее, если для редактора выбрана Темная или Контрастная темная тема интерфейса. Поставьте галочку Включить темный режим, чтобы активировать эту опцию. Примечание: Помимо доступных тем интерфейса Светлая, Классическая светлая, Темная и Контрастная темная, в редакторах ONLYOFFICE теперь можно использовать собственную цветовую тему. Чтобы узнать, как это сделать, пожалуйста, следуйте данному руководству. Опция Единица измерения используется для определения единиц, которые должны использоваться на линейках и в окнах свойств для измерения параметров элементов, таких как ширина, высота, интервалы, поля и т.д. Можно выбрать опцию Сантиметр, Пункт или Дюйм. Опция Стандартное значение масштаба используется для установки стандартного значения масштаба путем его выбора из списка доступных вариантов от 50% до 500%. Можно также выбрать опцию По размеру слайда или По ширине. Опция Хинтинг шрифтов используется для выбора типа отображения шрифта в редакторе презентаций: Выберите опцию Как Windows, если вам нравится отображение шрифтов в операционной системе Windows, то есть с использованием хинтинга шрифтов Windows. Выберите опцию Как OS X, если вам нравится отображение шрифтов в операционной системе Mac, то есть вообще без хинтинга шрифтов. Выберите опцию Собственный, если хотите, чтобы текст отображался с хинтингом, встроенным в файлы шрифтов. Режим кэширования по умолчанию - используется для выбора режима кэширования символов шрифта. Не рекомендуется переключать без особых причин. Это может быть полезно только в некоторых случаях, например, при возникновении проблемы в браузере Google Chrome с включенным аппаратным ускорением. В редакторе презентаций есть два режима кэширования: В первом режиме кэширования каждая буква кэшируется как отдельная картинка. Во втором режиме кэширования выделяется картинка определенного размера, в которой динамически располагаются буквы, а также реализован механизм выделения и удаления памяти в этой картинке. Если памяти недостаточно, создается другая картинка, и так далее. Настройка Режим кэширования по умолчанию применяет два вышеуказанных режима кэширования по отдельности для разных браузеров: Когда настройка Режим кэширования по умолчанию включена, в Internet Explorer (v. 9, 10, 11) используется второй режим кэширования, в других браузерах используется первый режим кэширования. Когда настройка Режим кэширования по умолчанию выключена, в Internet Explorer (v. 9, 10, 11) используется первый режим кэширования, в других браузерах используется второй режим кэширования. Опция Настройки макросов используется для настройки отображения макросов с уведомлением. Выберите опцию Отключить все, чтобы отключить все макросы в презентации; Выберите опцию Показывать уведомление, чтобы получать уведомления о макросах в презентации; Выберите опцию Включить все, чтобы автоматически запускать все макросы в презентации. Чтобы сохранить внесенные изменения, нажмите кнопку Применить."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Совместное редактирование презентаций", 
        "body": "Редактор презентаций позволяет осуществлять постоянный общекомандный подход к рабочему процессу: предоставлять доступ к файлам и папкам, общаться прямо в редакторе, комментировать определенные фрагменты презентаций, требующие особого внимания, сохранять версии презентаций для дальнейшего использования. В редакторе презентаций вы можете работать над презентацией совместно, используя два режима: Быстрый или Строгий. Режим можно выбрать в Дополнительных настройках. Нужный режим также можно выбрать, используя значок Режим совместного редактирования на вкладке Совместная работа верхней панели инструментов: Количество пользователей, которые в данный момент работают над текущей презентацией, отображается в правой части шапки редактора - . Чтобы увидеть, кто именно редактирует файл в настоящий момент, можно щелкнуть по этому значку или открыть панель Чата с полным списком пользователей. Быстрый режим Быстрый режим используется по умолчанию, в нем изменения, вносимые другими пользователями, отображаются в реальном времени. При совместном редактировании презентации в Быстром режиме недоступна возможность Повторить последнее отмененное действие. В этом режиме действия и имена участников совместного редактирования отображаются непосредственно в процессе редактирования текста. Строгий режим Строгий режим позволяет скрывать изменения, внесенные другими пользователями, до тех пор, пока вы не нажмете значок Сохранить  , чтобы сохранить ваши изменения и принять изменения, внесенные другими. Когда презентацию редактируют одновременно несколько пользователей в Строгом режиме, редактируемые объекты (автофигуры, текстовые объекты, таблицы, изображения, диаграммы) помечаются пунктирными линиями разных цветов. Объект, который редактируете Вы, окружен зеленой пунктирной линией. Красные пунктирные линии означают, что объекты редактируются другими пользователями. Как только один из пользователей сохранит свои изменения, нажав на значок , все остальные увидят в строке состояния примечание, которое сообщает о наличии обновлений. Чтобы сохранить внесенные вами изменения и сделать их доступными для других пользователей, а также получить обновления, сохраненные другими пользователями, нажмите на значок в левом верхнем углу верхней панели инструментов. Обновления будут подсвечены, чтобы Вы могли проверить, что конкретно изменилось. Режим просмотра в реальном времени Режим просмотра в реальном времени используется для просмотра в реальном времени изменений, которые вносят другие пользователи, когда презентация открыта пользователем с правами доступа Только чтение. Для правильной работы этого режима убедитесь, что включена галочка Показывать изменения других пользователей в Дополнительных параметрах редактора. Аноним Пользователи портала, которые не зарегистрированы и не имеют профиля, считаются анонимными, хотя они по-прежнему могут совместно работать над документами. Чтобы добавить имя, анонимный пользователь должен ввести его в соответствующее поле, появляющееся в правом верхнем углу экрана при первом открытии документа. Установите флажок «Больше не спрашивать», чтобы сохранить имя."
    },
   {
        "id": "HelpfulHints/Commenting.htm", 
        "title": "Комментирование", 
        "body": "Редактор презентаций позволяет осуществлять постоянный общекомандный подход к рабочему процессу: предоставлять доступ к файлам и папкам, вести совместную работу над презентациями в режиме реального времени, общаться прямо в редакторе, сохранять версии презентаций для дальнейшего использования. В редакторе презентаций вы можете оставлять комментарии к содержимому презентаций, не редактируя его непосредственно. В отличие от сообщений в чате, комментарии хранятся, пока вы не удалите их. Добавление комментариев и ответ на них Чтобы оставить комментарий к определенному объекту (текстовому полю, фигуре и так далее): выделите объект, в котором, по Вашему мнению, содержится какая-то ошибка или проблема, переключитесь на вкладку Вставка или Совместная работа верхней панели инструментов и нажмите на кнопку Комментарий или щелкните правой кнопкой мыши по выделенному объекту и выберите в меню команду Добавить комментарий, введите нужный текст, нажмите кнопку Добавить. Объект, который вы прокомментировали, будет помечен значком . Для просмотра комментария щелкните по этому значку. Чтобы добавить комментарий к определенному слайду, выделите слайд и используйте кнопку Комментарий на вкладке Вставка или Совместная работа верхней панели инструментов. Добавленный комментарий будет отображаться в левом верхнем углу слайда. Чтобы создать комментарий уровня презентации, который не относится к определенному объекту или слайду, нажмите на значок на левой боковой панели, чтобы открыть панель Комментарии, и используйте ссылку Добавить комментарий к документу. Комментарии уровня презентации можно просмотреть на панели Комментарии. Здесь также доступны комментарии, относящиеся к объектам и слайдам. Любой другой пользователь может ответить на добавленный комментарий, чтобы дать ответ на вопросы или отчитаться о проделанной работе. Для этого надо нажать на ссылку Добавить ответ, расположенную под комментарием, ввести текст ответа в поле ввода и нажать кнопку Ответить. Если вы используете Строгий режим совместного редактирования, новые комментарии, добавленные другими пользователями, станут видимыми только после того, как вы нажмете на значок в левом верхнем углу верхней панели инструментов. Управление комментариями Вы можете управлять добавленными комментариями, используя значки в выноске комментария или на панели Комментарии слева: отсортируйте добавленные комментарии, нажав на значок : по дате: От старых к новым или От новых к старым. Это порядок сортировки выбран по умолчанию. по автору: По автору от А до Я или По автору от Я до А по группе: Все или выберите определенную группу из списка. отредактируйте выбранный комментарий, нажав значок , удалите выбранный комментарий, нажав значок , закройте выбранное обсуждение, нажав на значок , если задача или проблема, обозначенная в комментарии, решена; после этого обсуждение, которое вы открыли своим комментарием, приобретет статус решенного. Чтобы вновь его открыть, нажмите на значок . если вы хотите решить сразу несколько комментариев, на вкладке Совместная работа нажмите выпадающий список Решить и выберите один из вариантов решения комментариев: решить текущие комментарии, решить мои комментарии или решить все комментарии в документе. Добавление упоминаний Примечание: Упоминания можно добавлять в комментарии к тексту, а не в комментарии ко всей презентации. При вводе комментариев можно использовать функцию упоминаний, которая позволяет привлечь чье-либо внимание к комментарию и отправить оповещение упомянутому пользователю по электронной почте и в Чат. Чтобы добавить упоминание: Введите знак \"+\" или \"@\" в любом месте текста комментария - откроется список пользователей портала. Чтобы упростить процесс поиска, вы можете начать вводить имя в поле комментария - список пользователей будет меняться по мере ввода. Выберите из списка нужного человека. Если упомянутому пользователю еще не был предоставлен доступ к файлу, откроется окно Настройки совместного доступа. По умолчанию выбран тип доступа Только чтение. Измените его в случае необходимости. Нажмите кнопку OK. Упомянутый пользователь получит по электронной почте оповещение о том, что он был упомянут в комментарии. Если к файлу был предоставлен доступ, пользователь также получит соответствующее оповещение. Удаление комментариев Чтобы удалить комментарии, нажмите кнопку Удалить на вкладке Совместная работа верхней панели инструментов, выберите нужный пункт меню: Удалить текущие комментарии - чтобы удалить выбранный комментарий. Если к комментарию были добавлены ответы, все ответы к нему также будут удалены. Удалить мои комментарии - чтобы удалить добавленные вами комментарии, не удаляя комментарии, добавленные другими пользователями. Если к вашему комментарию были добавлены ответы, все ответы к нему также будут удалены. Удалить все комментарии - чтобы удалить все комментарии в презентации, добавленные вами и другими пользователями. Чтобы закрыть панель с комментариями, нажмите на значок еще раз."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Сочетания клавиш", 
        "body": "Подсказки для клавиш Используйте сочетания клавиш для более быстрого и удобного доступа к функциям Редактора презентаций без использования мыши. Нажмите клавишу Alt, чтобы показать все подсказки для клавиш верхней панели инструментов, правой и левой боковой панели, а также строке состояния. Нажмите клавишу, соответствующую элементу, который вы хотите использовать. В зависимости от нажатой клавиши, могут появляться дополнительные подсказки. Когда появляются дополнительные подсказки для клавиш, первичные - скрываются. Например, чтобы открыть вкладку Вставка, нажмите Alt и просмотрите все подсказки по первичным клавишам. Нажмите букву И, чтобы открыть вкладку Вставка и просмотреть все доступные сочетания клавиш для этой вкладки. Затем нажмите букву, соответствующую элементу, который вы хотите использовать. Нажмите Alt, чтобы скрыть все подсказки для клавиш, или Escape, чтобы вернуться к предыдущей группе подсказок для клавиш. Find the most common keyboard shortcuts in the list below: Windows/Linux Mac OS Работа с презентацией Открыть панель 'Файл' Alt+F ^ Ctrl+⌥ Option+F Открыть панель Файл, чтобы сохранить, скачать, распечатать текущую презентацию, просмотреть сведения о ней, создать новую презентацию или открыть существующую, получить доступ к Справке по онлайн-редактору презентаций или дополнительным параметрам. Открыть окно 'Поиск' Ctrl+F ^ Ctrl+F, &#8984; Cmd+F Открыть диалоговое окно Поиск, чтобы начать поиск символа/слова/фразы в редактируемой презентации. Открыть панель 'Комментарии' Ctrl+⇧ Shift+H ^ Ctrl+⇧ Shift+H, &#8984; Cmd+⇧ Shift+H Открыть панель Комментарии, чтобы добавить свой комментарий или ответить на комментарии других пользователей. Открыть поле комментария Alt+H &#8984; Cmd+⌥ Option+A Открыть поле ввода данных, в котором можно добавить текст комментария. Открыть панель 'Чат' Alt+Q ^ Ctrl+⌥ Option+Q Открыть панель Чат и отправить сообщение. Сохранить презентацию Ctrl+S ^ Ctrl+S, &#8984; Cmd+S Сохранить все изменения в редактируемой презентации. Активный файл будет сохранен с текущим именем, в том же местоположении и формате. Печать презентации Ctrl+P ^ Ctrl+P, &#8984; Cmd+P Распечатать презентацию на одном из доступных принтеров или сохранить в файл. Скачать как... Ctrl+⇧ Shift+S ^ Ctrl+⇧ Shift+S, &#8984; Cmd+⇧ Shift+S Открыть панель Скачать как..., чтобы сохранить редактируемую презентацию на жестком диске компьютера в одном из поддерживаемых форматов: PPTX, PDF, ODP, POTX, PDF/A, OTP. Полноэкранный режим F11 Переключиться в полноэкранный режим, чтобы развернуть онлайн-редактор презентаций на весь экран. Вызов справки F1 F1 Открыть меню Справка онлайн-редактора презентаций. Открыть существующий файл (десктопные редакторы) Ctrl+O На вкладке Открыть локальный файл в десктопных редакторах позволяет открыть стандартное диалоговое окно для выбора существующего файла. Закрыть файл (десктопные редакторы) Ctrl+W, Ctrl+F4 ^ Ctrl+W, &#8984; Cmd+W Закрыть выбранную презентацию в десктопных редакторах. Контекстное меню элемента ⇧ Shift+F10 ⇧ Shift+F10 Открыть контекстное меню выбранного элемента. Сбросить масштаб Ctrl+0 ^ Ctrl+0 или &#8984; Cmd+0 Сбросить масштаб текущей презентации до значения по умолчанию 'По размеру слайда'. Навигация Первый слайд Home Home, Fn+← Перейти к первому слайду редактируемой презентации. Последний слайд End End, Fn+→ Перейти к последнему слайду редактируемой презентации. Следующий слайд Page Down Page Down, Fn+↓ Перейти к следующему слайду редактируемой презентации. Предыдущий слайд Page Up Page Up, Fn+↑ Перейти к предыдущему слайду редактируемой презентации. Увеличить масштаб Ctrl++ ^ Ctrl+=, &#8984; Cmd+= Увеличить масштаб редактируемой презентации. Уменьшить масштаб Ctrl+- ^ Ctrl+-, &#8984; Cmd+- Уменьшить масштаб редактируемой презентации. Перейти между элементами управления Tab, Shift+Tab Tab, Shift+Tab Перейти на следующий или предыдущий элемент управления в модальных окнах. Выполнение действий со слайдами Новый слайд Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Создать новый слайд и добавить его после выделенного в списке слайдов. Дублировать слайд Ctrl+D &#8984; Cmd+D Дублировать выделенный в списке слайд. Переместить слайд вверх Ctrl+↑ &#8984; Cmd+↑ Поместить выделенный слайд над предыдущим в списке. Переместить слайд вниз Ctrl+↓ &#8984; Cmd+↓ Поместить выделенный слайд под последующим в списке. Переместить слайд в начало Ctrl+⇧ Shift+↑ &#8984; Cmd+⇧ Shift+↑ Переместить выделенный слайд в самое начало списка. Переместить слайд в конец Ctrl+⇧ Shift+↓ &#8984; Cmd+⇧ Shift+↓ Переместить выделенный слайд в самый конец списка. Выполнение действий с объектами Создать копию Ctrl + перетаскивание, Ctrl+D ^ Ctrl + перетаскивание, &#8984; Cmd + перетаскивание, ^ Ctrl+D, &#8984; Cmd+D Удерживайте клавишу Ctrl при перетаскивании выбранного объекта или нажмите Ctrl+D (&#8984; Cmd+D для Mac), чтобы создать копию объекта. Сгруппировать Ctrl+G &#8984; Cmd+G Сгруппировать выделенные объекты. Разгруппировать Ctrl+⇧ Shift+G &#8984; Cmd+⇧ Shift+G Разгруппировать выбранную группу объектов. Выделить следующий объект ↹ Tab ↹ Tab Выделить следующий объект после выбранного в данный момент. Выделить предыдущий объект ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Выделить предыдущий объект перед выбранным в данный момент. Нарисовать прямую линию или стрелку ⇧ Shift + перетаскивание (при рисовании линий или стрелок) ⇧ Shift + перетаскивание (при рисовании линий или стрелок) Нарисовать прямую линию или стрелку: горизонтальную, вертикальную или под углом 45 градусов. Модификация объектов Ограничить движение ⇧ Shift + перетаскивание ⇧ Shift + перетаскивание Ограничить перемещение выбранного объекта по горизонтали или вертикали. Задать угол поворота в 15 градусов ⇧ Shift + перетаскивание (при поворачивании) ⇧ Shift + перетаскивание (при поворачивании) Ограничить угол поворота шагом в 15 градусов. Сохранять пропорции ⇧ Shift + перетаскивание (при изменении размера) ⇧ Shift + перетаскивание (при изменении размера) Сохранять пропорции выбранного объекта при изменении размера. Попиксельное перемещение Ctrl+← → ↑ ↓ &#8984; Cmd+← → ↑ ↓ Удерживайте клавишу Ctrl (&#8984; Cmd для Mac) и используйте стрелки на клавиатуре, чтобы перемещать выбранный объект на один пиксель за раз. Работа с таблицами Перейти к следующей ячейке в строке ↹ Tab ↹ Tab Перейти к следующей ячейке в строке таблицы. Перейти к предыдущей ячейке в строке ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Перейти к предыдущей ячейке в строке таблицы. Перейти к следующей строке ↓ ↓ Перейти к следующей строке таблицы. Перейти к предыдущей строке ↑ ↑ Перейти к предыдущей строке таблицы. Начать новый абзац ↵ Enter ↵ Return Начать новый абзац внутри ячейки. Добавить новую строку ↹ Tab в правой нижней ячейке таблицы. ↹ Tab в правой нижней ячейке таблицы. Добавить новую строку внизу таблицы. Просмотр презентации Начать просмотр с начала Ctrl+F5 ^ Ctrl+F5 Запустить презентацию с начала. Перейти вперед ↵ Enter, Page Down, →, ↓, ␣ Spacebar ↵ Return, Page Down, →, ↓, ␣ Spacebar Показать следующий эффект перехода или перейти к следующему слайду. Перейти назад Page Up, ←, ↑ Page Up, ←, ↑ Показать предыдущий эффект перехода или вернуться к предыдущему слайду. Закрыть просмотр Esc Esc Закончить просмотр слайдов. Отмена и повтор Отменить Ctrl+Z ^ Ctrl+Z, &#8984; Cmd+Z Отменить последнее выполненное действие. Повторить Ctrl+Y ^ Ctrl+Y, &#8984; Cmd+Y Повторить последнее отмененное действие. Вырезание, копирование и вставка Вырезать Ctrl+X, ⇧ Shift+Delete &#8984; Cmd+X Вырезать выделенный объект и отправить его в буфер обмена компьютера. Вырезанный объект можно затем вставить в другое место этой же презентации. Копировать Ctrl+C, Ctrl+Insert &#8984; Cmd+C Отправить выделенный объект и в буфер обмена компьютера. Скопированный объект можно затем вставить в другое место этой же презентации. Вставить Ctrl+V, ⇧ Shift+Insert &#8984; Cmd+V Вставить ранее скопированный объект из буфера обмена компьютера в текущей позиции курсора. Объект может быть ранее скопирован из этой же презентации. Вставить гиперссылку Ctrl+K ^ Ctrl+K, &#8984; Cmd+K Вставить гиперссылку, которую можно использовать для перехода по веб-адресу или для перехода на определенный слайд этой презентации. Копировать форматирование Ctrl+⇧ Shift+C ^ Ctrl+⇧ Shift+C, &#8984; Cmd+⇧ Shift+C Скопировать форматирование из выделенного фрагмента редактируемого текста. Скопированное форматирование можно затем применить к другому тексту в этой же презентации. Применить форматирование Ctrl+⇧ Shift+V ^ Ctrl+⇧ Shift+V, &#8984; Cmd+⇧ Shift+V Применить ранее скопированное форматирование к тексту редактируемого текстового поля. Выделение с помощью мыши Добавить в выделенный фрагмент ⇧ Shift ⇧ Shift Начните выделение, удерживайте клавишу ⇧ Shift и щелкните там, где требуется закончить выделение. Выделение с помощью клавиатуры Выделить все Ctrl+A ^ Ctrl+A, &#8984; Cmd+A Выделить все слайды (в списке слайдов), или все объекты на слайде (в области редактирования слайда), или весь текст (внутри текстового поля) - в зависимости от того, где установлен курсор мыши. Выделить фрагмент текста ⇧ Shift+→ ← ⇧ Shift+→ ← Выделить текст посимвольно. Выделить текст с позиции курсора до начала строки ⇧ Shift+Home Выделить фрагмент текста с позиции курсора до начала текущей строки. Выделить текст с позиции курсора до конца строки ⇧ Shift+End Выделить фрагмент текста с позиции курсора до конца текущей строки. Выделить один символ справа ⇧ Shift+→ ⇧ Shift+→ Выделить один символ справа от позиции курсора. Выделить один символ слева ⇧ Shift+← ⇧ Shift+← Выделить один символ слева от позиции курсора. Выделить до конца слова Ctrl+⇧ Shift+→ Выделить фрагмент текста с позиции курсора до конца слова. Выделить до начала слова Ctrl+⇧ Shift+← Выделить фрагмент текста с позиции курсора до начала слова. Выделить одну строку выше ⇧ Shift+↑ ⇧ Shift+↑ Выделить одну строку выше (курсор находится в начале строки). Выделить одну строку ниже ⇧ Shift+↓ ⇧ Shift+↓ Выделить одну строку ниже (курсор находится в начале строки). Оформление текста Полужирный шрифт Ctrl+B ^ Ctrl+B, &#8984; Cmd+B Сделать шрифт в выделенном фрагменте текста полужирным, придав ему большую насыщенность. Курсив Ctrl+I ^ Ctrl+I, &#8984; Cmd+I Сделать шрифт в выделенном фрагменте текста курсивным, придав ему наклон вправо. Подчеркнутый шрифт Ctrl+U ^ Ctrl+U, &#8984; Cmd+U Подчеркнуть выделенный фрагмент текста чертой, проведенной под буквами. Зачеркнутый шрифт Ctrl+5 ^ Ctrl+5, &#8984; Cmd+5 Зачеркнуть выделенный фрагмент текста чертой, проведенной по буквам. Подстрочные знаки Ctrl+⇧ Shift+&gt;</kbd> &#8984; Cmd+⇧ Shift+&gt;</kbd> Сделать выделенный фрагмент текста мельче и поместить его в нижней части строки, например, как в химических формулах. Надстрочные знаки Ctrl+⇧ Shift+&lt;</kbd> &#8984; Cmd+⇧ Shift+&lt;</kbd> Сделать выделенный фрагмент текста мельче и поместить его в верхней части строки, например, как в дробях. Маркированный список Ctrl+⇧ Shift+L ^ Ctrl+⇧ Shift+L, &#8984; Cmd+⇧ Shift+L Создать из выделенного фрагмента текста неупорядоченный маркированный список или начать новый список. Убрать форматирование Ctrl+␣ Spacebar Убрать форматирование из выделенного фрагмента текста. Увеличить шрифт Ctrl+] ^ Ctrl+], &#8984; Cmd+] Увеличить на 1 пункт размер шрифта для выделенного фрагмента текста. Уменьшить шрифт Ctrl+[ ^ Ctrl+[, &#8984; Cmd+[ Уменьшить на 1 пункт размер шрифта для выделенного фрагмента текста. Выровнять по центру Ctrl+E Выровнять текст по центру между правым и левым краем текстового поля. Выровнять по ширине Ctrl+J Выровнять текст как по левому, так и по правому краю текстового поля (выравнивание осуществляется за счет добавления дополнительных интервалов там, где это необходимо). Выровнять по правому краю Ctrl+R Выровнять текст по правому краю текстового поля (левый край остается невыровненным). Выровнять по левому краю Ctrl+L Выровнять текст по левому краю текстового поля (правый край остается невыровненным). Увеличить отступ слева Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Увеличить отступ абзаца слева на одну позицию табуляции. Уменьшить отступ слева Ctrl+⇧ Shift+M ^ Ctrl+⇧ Shift+M, &#8984; Cmd+⇧ Shift+M Уменьшить отступ абзаца слева на одну позицию табуляции. Удалить один символ слева ← Backspace ← Backspace Удалить один символ слева от курсора. Удалить один символ справа Delete Fn+Delete Удалить один символ справа от курсора. Перемещение по тексту Перейти на один символ влево ← ← Переместить курсор на один символ влево. Перейти на один символ вправо → → Переместить курсор на один символ вправо. Перейти на одну строку вверх ↑ ↑ Переместить курсор на одну строку вверх. Перейти на одну строку вниз ↓ ↓ Переместить курсор на одну строку вниз. Перейти в начало слова или на одно слово влево Ctrl+← &#8984; Cmd+← Переместить курсор в начало слова или на одно слово влево. Перейти на одно слово вправо Ctrl+→ &#8984; Cmd+→ Переместить курсор на одно слово вправо. Перейти к следующему текстовому заполнителю Ctrl+↵ Enter ^ Ctrl+↵ Return, &#8984; Cmd+↵ Return Перейти к следующему текстовому заполнителю с заголовком или основным текстом слайда. Если это последний текстовый заполнитель на слайде, будет вставлен новый слайд с таким же макетом, как у исходного. Перейти в начало строки Home Home Установить курсор в начале редактируемой строки. Перейти в конец строки End End Установить курсор в конце редактируемой строки. Перейти в начало текстового поля Ctrl+Home Установить курсор в начале редактируемого текстового поля. Перейти в конец текстового поля Ctrl+End Установить курсор в конце редактируемого текстового поля."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "Параметры представления и инструменты навигации", 
        "body": "В редакторе презентаций доступен ряд инструментов, позволяющих облегчить просмотр и навигацию по презентации: масштаб, кнопки перехода на предыдущий/следующий слайд, указатель номера слайда. Настройте параметры представления Чтобы настроить стандартные параметры представления и установить наиболее удобный режим работы с презентацией, перейдите на вкладку Вид. Можно выбрать следующие опции: Масштаб – чтобы выбрать из выпадающего списка нужное значение масштаба от 50% до 500%. По размеру слайда - чтобы весь слайд целиком помещался в видимой части рабочей области. По ширине - чтобы ширина слайда соответствовала видимой части рабочей области. Тема интерфейса – выберите из выпадающего меню одну из доступных тем интерфейса: Системная, Светлая, Классическая светлая, Темная, Контрастная темная. Заметки - когда эта опция отключена, будет скрыта секция заметки, которая находится под слайдом. Данную секцию также можно скрыть/показать, перетащив её курсором мыши. Линейки - когда эта опция отключена, будут скрыты линейки, которые используются для становки позиций табуляции и отступов абзацев внутри текстовых полей. Чтобы отобразить скрытые линейки, щелкните по этой опции еще раз. Направляющие – выберите нужный тип направляющих для правильного позиционирования объектов на слайде. Доступны следующие варианты: вертикальные, горизонтальные и смарт-направляющие для лучшего позиционирования. Линии сетки – выберите нужный размер сетки из доступных шаблонов или задайте пользовательский размер, и укажите, надо ли привязать объекты к сетке, для лучшего позиционирования объектов. Всегда показывать панель инструментов – когда эта опция отключена, будет скрыта верхняя панель инструментов, которая содержит команды. Названия вкладок при этом остаются видимыми. Можно также дважды щелкнуть по любой вкладке, чтобы скрыть верхнюю панель инструментов или отобразить ее снова. Строка состояния - когда эта опция отключена, будет скрыта самая нижняя панель, на которой находится Указатель номера слайда и кнопки Масштаба. Чтобы отобразить скрытую строку состояния, щелкните по этой опции еще раз. Левая панель - когда эта опция отключена, будет скрыта левая панель, на которой расположены кнопки Поиск, Комментарии и т. д. Чтобы отобразить скрытую Левую панель, щелкните по этой опции еще раз. Правая панель - когда эта опция отключена, будет скрыта правая панель, на которой расположены Параметры. Чтобы отобразить скрытую Правую панель, щелкните по этой опции еще раз. Правая боковая панель свернута по умолчанию. Чтобы ее развернуть, выделите любой объект или слайд и щелкните по значку вкладки, которая в данный момент активирована (чтобы свернуть правую боковую панель, щелкните по этому значку еще раз). Левую боковую панель можно настроить путем простого перетаскивания: наведите курсор мыши на край левой боковой панели, чтобы курсор превратился в двунаправленную стрелку, и перетащите край панели влево, чтобы уменьшить ширину панели, или вправо, чтобы ее увеличить. Используйте инструменты навигации Для осуществления навигации по презентации используйте следующие инструменты: Кнопки Масштаб расположены в правом нижнем углу и используются для увеличения и уменьшения масштаба текущей презентации. Чтобы изменить выбранное в текущий момент значение масштаба в процентах, щелкните по нему и выберите в списке один из доступных параметров масштабирования (50% / 75% / 100% / 125% / 150% / 175% / 200%/ 300% / 400% / 500%) или используйте кнопки Увеличить или Уменьшить . Щелкните по значку По ширине , чтобы ширина слайда соответствовала видимой части рабочей области. Чтобы весь слайд целиком помещался в видимой части рабочей области, нажмите значок По размеру слайда . Параметры масштаба доступны также на вкладкe Вид. Можно задать значение масштаба по умолчанию. Откройте вкладку Файл на верхней панели инструментов, перейдите в раздел Дополнительные параметры..., выберите из списка нужное Стандартное значение масштаба и нажмите кнопку Применить. Для перехода на предыдущий или следующий слайд в ходе редактирования презентации можно использовать кнопки и , расположенные сверху и снизу вертикальной полосы прокрутки, которая находится справа от слайда. Указатель номера слайда показывает текущий слайд в составе всех слайдов текущей презентации (слайд 'n' из 'nn'). Щелкните по этой надписи, чтобы открыть окно, в котором можно ввести номер нужного слайда и быстро перейти к нему. Если Вы решите скрыть строку состояния, этот инструмент станет недоступен."
    },
   {
        "id": "HelpfulHints/Password.htm", 
        "title": "Защита презентаций с помощью пароля", 
        "body": "Вы можете защитить свои презентации при помощи пароля, который требуется вашим соавторам для входа в режим редактирования. Пароль можно изменить или удалить позже. Вы не сможете восстановить свой пароль, если потеряете его или забудете. Пожалуйста, храните его в надежном месте. Установка пароля перейдите на вкладку Файл на верхней панели инструментов, выберите опцию Защитить, нажмите кнопку Добавить пароль, введите пароль в поле Пароль и продублируйте его в поле Повторите пароль, затем нажмите ОК. Нажмите , чтобы показать или скрыть пароль. Смена пароля перейдите на вкладку Файл на верхней панели инструментов, выберите опцию Защитить, нажмите кнопку Изменить пароль, введите пароль в поле Пароль и продублируйте его в поле Повторите пароль, затем нажмите ОК. Удаление пароля перейдите на вкладку Файл на верхней панели инструментов, выберите опцию Защитить, нажмите кнопку Удалить пароль."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Функция поиска и замены", 
        "body": "Чтобы найти нужные символы, слова или фразы, которые используются в редактируемой презентации, нажмите на значок , расположенный на левой боковой панели, или значок , расположенный в правом верхнем углу. Вы также можете использовать сочетание клавиш Ctrl+F (Command+F для MacOS), чтобы открыть маленькую панель поиска, или сочетание клавиш Ctrl+H, чтобы открыть расширенную панель поиска. В правом верхнем углу рабочей области откроется маленькая панель Поиск. Чтобы открыть дополнительные параметры, нажмите значок или используйте сочетание клавиш Ctrl+H. Откроется панель Поиск и замена: Введите запрос в соответствующее поле ввода данных Поиск. Если требуется заменить одно или более вхождений найденных символов, введите текст для замены в соответствующее поле ввода данных Заменить на. Вы можете заменить одно выделенное в данный момент вхождение или заменить все вхождения, нажав соответствующие кнопки Заменить или Заменить все. Для навигации по найденным вхождениям нажмите одну из кнопок со стрелками. Кнопка показывает следующее вхождение, а кнопка показывает предыдущее. Задайте параметры поиска, отметив нужные опции, расположенные под полями ввода: С учетом регистра - используется для поиска только тех вхождений, которые набраны в таком же регистре, что и ваш запрос, (например, если Вы ввели запрос 'Редактор', такие слова, как 'редактор' или 'РЕДАКТОР' и т.д. не будут найдены). Только слово целиком - используется для подсветки только слов целиком. Первый слайд в в выбранном направлении, содержащий заданные символы, будет выделен в списке слайдов и отображен в рабочей области. Нужные символы будут подсвечены. Если это не тот слайд, который вы ищете, нажмите на выбранную кнопку еще раз, чтобы найти следующий слайд, содержащий заданные символы."
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Проверка орфографии", 
        "body": "В редакторе презентаций можно проверять правописание текста на определенном языке и исправлять ошибки в ходе редактирования. В десктопной версии также доступна возможность добавлять слова в пользовательский словарь, общий для всех трех редакторов. Прежде всего выберите язык презентации. Щелкните по значку в правой части строки состояния. В окне, которое появится, выберите нужный язык и нажмите кнопку OK. Выбранный язык будет применен ко всей презентации. Чтобы выбрать какой-то другой язык для любого фрагмента текста в этой презентации, выделите мышью нужную часть теста и используйте меню , которое находится в строке состояния. Для включения функции проверки орфографии можно сделать следующее: нажмите на значок Проверка орфографии в строке состояния или откройте вкладку Файл верхней панели инструментов, выберите опцию Дополнительные параметры, поставьте галочку рядом с опцией Включить проверку орфографии и нажмите кнопку Применить. Слова, написанные с ошибками, будут подчеркнуты красной чертой. Щелкните правой кнопкой мыши по нужному слову, чтобы вызвать меню, и: выберите одно из предложенных похожих слов, которые написаны правильно, чтобы заменить слово с ошибкой на предложенное слово. Если найдено слишком много вариантов, в меню появляется пункт Больше вариантов...; используйте опцию Пропустить, чтобы пропустить только это слово и убрать подчеркивание или Пропустить все, чтобы пропустить все идентичные слова, повторяющиеся в тексте; если текущее слово отсутствует в словаре, добавьте его в пользовательский словарь. В следующий раз это слово не будет расцениваться как ошибка. Эта возможность доступна в десктопной версии. выберите другой язык для этого слова. Для отключения функции проверки орфографии можно сделать следующее: нажмите на значок Проверка орфографии в строке состояния или откройте вкладку Файл верхней панели инструментов, выберите опцию Дополнительные параметры, уберите галочку рядом с опцией Включить проверку орфографии и нажмите кнопку Применить."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Поддерживаемые форматы электронных презентаций", 
        "body": "Презентация - это серия слайдов, которые могут содержать различные типы контента, такие как изображения, файлы мультимедиа, текст, эффекты и т.д. Редактор презентаций работает со следующими форматами презентаций. При загрузке или открытии файла на редактирование он будет сконвертирован в формат Office Open XML (PPTX). Это делается для ускорения обработки файла и повышения совместимости. Следующая таблица содержит форматы, которые можно открыть на просмотр и/или редактирование. Форматы Описание Просмотр в исходном формате Просмотр после конвертации в OOXML Редактирование в исходном формате Редактирование после конвертации в OOXML ODP OpenDocument Presentation Формат файлов, который представляет презентации, созданные приложением Impress, входящим в состав пакетов офисных приложений на базе OpenOffice + + OTP OpenDocument Presentation Template Формат текстовых файлов OpenDocument для шаблонов презентаций. Шаблон OTP содержит настройки форматирования, стили и т.д. и может использоваться для создания множества презентаций со схожим форматированием + + POTX PowerPoint Open XML Document Template разработанный компанией Microsoft формат файлов на основе XML, сжатых по технологии ZIP. Предназначен для шаблонов презентаций. Шаблон POTX содержит настройки форматирования, стили и т.д. и может использоваться для создания множества презентаций со схожим форматированием + + PPSX Microsoft PowerPoint Slide Show Формат файлов презентаций, используемый для воспроизведения слайд-шоу + + PPT Формат файлов, используемый программой Microsoft PowerPoint + + PPTX Office Open XML Presentation разработанный компанией Microsoft формат файлов на основе XML, сжатых по технологии ZIP. Предназначен для представления электронных таблиц, диаграмм, презентаций и текстовых документов + + Следующая таблица содержит форматы, в которые можно скачать презентацию из меню Файл -> Скачать как. Исходный формат Можно скачать как ODP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX OTP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX POTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPSX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPT JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX Вы также можете обратиться к матрице конвертации на сайте api.onlyoffice.com, чтобы узнать о возможности конвертации презентаций в самые известные форматы файлов."
    },
   {
        "id": "HelpfulHints/UsingChat.htm", 
        "title": "Общение в режиме реального времени", 
        "body": "Редактор презентаций позволяет осуществлять постоянный общекомандный подход к рабочему процессу: предоставлять доступ к файлам и папкам, вести совместную работу над презентациями в режиме реального времени, комментировать определенные фрагменты презентаций, требующие особого внимания, сохранять версии презентаций для дальнейшего использования. В редакторе презентаций вы можете общаться в режиме реального времени с другими пользователями, участвующими в процессе совместного редактирования, используя встроенный Чат, а также ряд полезных плагинов, например, Telegram или Rainbow. Чтобы войти в чат и оставить сообщение для других пользователей, нажмите на значок на левой боковой панели или переключитесь на вкладку Совместная работа верхней панели инструментов и нажмите на кнопку Чат, введите текст в соответствующем поле ниже, нажмите кнопку Отправить. Сообщения в чате хранятся только в течение одной сессии. Для обсуждения содержания презентации лучше использовать комментарии, которые хранятся до тех пор, пока вы не решите их удалить. Все сообщения, оставленные пользователями, будут отображаться на панели слева. Если есть новые сообщения, которые вы еще не прочитали, значок чата будет выглядеть так - . Чтобы закрыть панель с сообщениями чата, нажмите на значок на левой боковой панели или кнопку Чат на верхней панели инструментов еще раз."
    },
   {
        "id": "HelpfulHints/VersionHistory.htm", 
        "title": "История версий", 
        "body": "Редактор презентаций позволяет осуществлять постоянный общекомандный подход к рабочему процессу: предоставлять доступ к файлам и папкам, вести совместную работу над презентациями в режиме реального времени, общаться прямо в редакторе, комментировать определенные фрагменты презентаций, требующие особого внимания. В редакторе презентаций вы можете просматривать историю версий для презентации, над которой работаете совместно. Просмотр истории версий: Чтобы просмотреть все внесенные в презентацию изменения, перейдите на вкладку Файл, выберите опцию История версий на левой боковой панели или перейдите на вкладку Совместная работа, откройте историю версий, используя значок История версий на верхней панели инструментов. Вы увидите список версий и ревизий презентации с указанием автора и даты и времени создания каждой версии/ревизии. Для версий презентации также указан номер версии (например, вер. 2). Просмотр версий: Чтобы точно знать, какие изменения были внесены в каждой конкретной версии/ревизии, можно просмотреть нужную, нажав на нее на левой боковой панели. Изменения, внесенные автором версии/ревизии, помечены цветом, который показан рядом с именем автора на левой боковой панели. Чтобы вернуться к текущей версии презентации, нажмите на ссылку Закрыть историю над списком версий. Восстановление версий: Если вам надо вернуться к одной из предыдущих версий презентации, нажмите на ссылку Восстановить под выбранной версией или ревизией. Чтобы узнать больше об управлении версиями и промежуточными ревизиями, а также о восстановлении предыдущих версий, пожалуйста, прочитайте следующую статью."
    },
   {
        "id": "ProgramInterface/AnimationTab.htm", 
        "title": "Вкладка Анимация", 
        "body": "Вкладка Анимация Редактора презентаций позволяет управлять эффектами анимации. Вы можете добавлять эффекты анимации, определять их перемещение и настраивать другие параметры анимационных эффектов для индивидуальной настройки презентации. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: С помощью этой вкладки вы можете выполнить следующие действия: выбирать эффект анимации, установить соответствующие параметры движения для каждого эффекта анимации, добавлять несколько анимаций, изменять порядок эффектов анимации при помощи параметров Переместить раньше или Переместить позже, просмотреть эффект анимации, установить для анимации параметры времени, такие как длительность, задержка and повтор, включать и отключать перемотку назад."
    },
   {
        "id": "ProgramInterface/CollaborationTab.htm", 
        "title": "Вкладка Совместная работа", 
        "body": "Вкладка Совместная работа позволяет организовать совместную работу над презентацией. В онлайн-версии можно предоставлять доступ к файлу, выбирать режим совместного редактирования, управлять комментариями. В режиме комментирования вы можете добавлять и удалять комментарии и использовать чат. В десктопной версии можно управлять комментариями. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: С помощью этой вкладки вы можете выполнить следующие действия: задавать настройки совместного доступа (доступно только в онлайн-версии), переключаться между Строгим и Быстрым режимами совместного редактирования (доступно только в онлайн-версии), добавлять или удалять комментарии к презентации, открывать панель Чата (доступно только в онлайн-версии), отслеживать историю версий (доступно только в онлайн-версии)."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "Вкладка Файл", 
        "body": "Вкладка Файл позволяет выполнить некоторые базовые операции с текущим файлом. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: С помощью этой вкладки вы можете выполнить следующие действия: в онлайн-версии сохранить текущий файл (если отключена опция Автосохранение), скачать как (сохранить документ в выбранном формате на жестком диске компьютера), сохранить копию как (сохранить копию документа в выбранном формате на портале), распечатать или переименовать его, в десктопной версии сохранить текущий файл в текущем формате и местоположении с помощью опции Сохранить, сохранить файл под другим именем, в другом местоположении или в другом формате с помощью опции Сохранить как, распечатать файл, защитить файл с помощью пароля, изменить или удалить пароль; защитить файл с помощью цифровой подписи (доступно только в десктопной версии); создать новую презентацию или открыть недавно отредактированную (доступно только в онлайн-версии), просмотреть общие сведения о презентации или изменить некоторые свойства файла, управлять правами доступа (доступно только в онлайн-версии), открыть дополнительные параметры редактора, в десктопной версии открыть в окне Проводника папку, в которой сохранен файл. В онлайн-версии открыть в новой вкладке браузера папку модуля Документы, в которой сохранен файл."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Вкладка Главная", 
        "body": "Вкладка Главная открывается по умолчанию при открытии презентации. Она позволяет задать основные параметры слайда, форматировать текст, вставлять некоторые объекты, выравнивать и располагать их в определенном порядке. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: С помощью этой вкладки вы можете выполнить следующие действия: управлять слайдами и начать показ слайдов, форматировать текст внутри текстового поля, вставлять текстовые поля, изображения, фигуры, выравнивать и упорядочивать объекты на слайде, копировать и очищать форматирование текста, изменять тему, цветовую схему или размер слайдов."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Вкладка Вставка", 
        "body": "Вкладка Вставка Редактора презентаций позволяет добавлять в презентацию визуальные объекты и комментарии. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: С помощью этой вкладки вы можете выполнить следующие действия: вставлять таблицы, вставлять текстовые поля и объекты Text Art, изображения, фигуры, диаграммы, вставлять комментарии и гиперссылки, вставлять колонтитулы, дату и время, номера слайдов, вставлять уравнения, символы, вставлять аудио- и видеозаписи, хранящиеся на жестком диске вашего компьютера (доступно только в десктопной версии, недоступно для Mac OS). Примечание: для воспроизведения видео нужно установить кодеки, например K-Lite."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Вкладка Плагины", 
        "body": "Вкладка Плагины позволяет получить доступ к дополнительным возможностям редактирования, используя доступные сторонние компоненты. Здесь также можно использовать макросы для автоматизации рутинных задач. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: Кнопка Настройки позволяет открыть окно, в котором можно просмотреть все установленные плагины, управлять ими и добавлять свои собственные. Кнопка Макросы позволяет открыть окно, в котором можно создавать собственные макросы и запускать их. Для получения дополнительной информации о макросах, пожалуйста, обратитесь к нашей Документации по API. В настоящее время по умолчанию доступны следующие плагины: Отправить - позволяет отправить презентацию по электронной почте с помощью десктопного почтового клиента по умолчанию (доступно только в десктопной версии), Подсветка кода - позволяет подсвечивать синтаксис кода, выбирая нужный язык, стиль, цвет фона, Фоторедактор - позволяет редактировать изображения: обрезать, отражать, поворачивать их, рисовать линии и фигуры, добавлять иконки и текст, загружать маску и применять фильтры, такие как Оттенки серого, Инверсия, Сепия, Размытие, Резкость, Рельеф и другие, Синонимы - позволяет находить синонимы и антонимы какого-либо слова и заменять его на выбранный вариант, Переводчик позволяет переводить выделенный текст на другие языки, Примечание: этот плагин не работает в Internet Explorer. YouTube позволяет встраивать в презентацию видео с YouTube. Для получения дополнительной информации о плагинах, пожалуйста, обратитесь к нашей Документации по API. Все доступные примеры плагинов с открытым исходным кодом доступны на GitHub."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Знакомство с пользовательским интерфейсом редактора презентаций", 
        "body": "В редакторе презентаций используется вкладочный интерфейс, в котором команды редактирования сгруппированы во вкладки по функциональности. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: Интерфейс редактора состоит из следующих основных элементов: В Шапке редактора отображается логотип, вкладки открытых документов, название документа и вкладки меню. В левой части Шапки редактора расположены кнопки Сохранить, Напечатать файл, Отменить и Повторить. В правой части Шапки редактора отображается имя пользователя и находятся следующие значки: Открыть расположение файла, с помощью которого в десктопной версии можно открыть в окне Проводника папку, в которой сохранен файл. В онлайн-версии можно открыть в новой вкладке браузера папку модуля Документы, в которой сохранен файл . Доступ (доступно только в онлайн-версии), с помощью которого можно задать права доступа к документам, сохраненным в облаке. Добавить в избранное, чтобы добавить файл в избранное и упростить поиск. Добавленный файл - это просто ярлык, поэтому сам файл остается в исходном месте. Удаление файла из избранного не приводит к удалению файла из исходного местоположения. Поиск - позволяет искать в презентации определенное слово, символ и т.д. На Верхней панели инструментов отображается набор команд редактирования в зависимости от выбранной вкладки меню. В настоящее время доступны следующие вкладки: Файл, Главная, Вставка, Переходы, Анимация, Совместная работа, Защита, Вид, Плагины. Опции Копировать, Вставить, Вырезать и Выделить все всегда доступны в левой части Верхней панели инструментов, независимо от выбранной вкладки. В Строке состояния, расположенной внизу окна редактора, находится значок Начать показ слайдов, некоторые инструменты навигации: указатель номера слайда и кнопки масштаба. В Строке состояния отображаются некоторые оповещения (например, \"Все изменения сохранены\", \"Соединение потеряно\", когда нет соединения, и редактор пытается переподключиться и т.д.), с ее помощью также можно задать язык текста и включить проверку орфографии. На Левой боковой панели находятся следующие значки: - позволяет использовать инструмент поиска и замены, - позволяет просматривать слайды и перемещаться по ним, - позволяет открыть панель Комментариев (доступно только в онлайн-версии) - позволяет открыть панель Чата, (доступно только в онлайн-версии) - позволяет обратиться в службу технической поддержки, (доступно только в онлайн-версии) - позволяет посмотреть информацию о программе. Правая боковая панель позволяет настроить дополнительные параметры различных объектов. При выделении на слайде определенного объекта активируется соответствующий значок на правой боковой панели. Нажмите на этот значок, чтобы развернуть правую боковую панель. Горизонтальная и вертикальная Линейки помогают располагать объекты на слайде и позволяют настраивать позиции табуляции и отступы абзацев внутри текстовых полей. В Рабочей области вы можете просматривать содержимое презентации, вводить и редактировать данные. Полоса прокрутки, расположенная справа, позволяет прокручивать презентацию вверх и вниз. Для удобства вы можете скрыть некоторые элементы и снова отобразить их при необходимости. Для получения дополнительной информации о настройке параметров представления, пожалуйста, обратитесь к этой странице."
    },
   {
        "id": "ProgramInterface/TransitionsTab.htm", 
        "title": "Вкладка Переходы", 
        "body": "Вкладка Переходы Редактора презентаций позволяет управлять переходами между слайдами. Вы можете добавлять эффекты перехода, устанавливать скорость перехода и настраивать другие параметры перехода слайдов. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: С помощью этой вкладки вы можете выполнить следующие действия: выбирать эффект перехода, устанавливать соответствующие значения параметров для каждого эффекта перехода, определять a продолжительность перехода, предварительно предпросматривать переходы, настраивать необходимое время перехода к следующему слайду при помощи опций Запускать щелчком и Задержка, применять переход ко всем слайдам при помощи кнопки Применить ко всем слайдам."
    },
   {
        "id": "ProgramInterface/ViewTab.htm", 
        "title": "Вкладка Вид", 
        "body": "Вкладка Вид Редактора презентаций позволяет управлять тем, как выглядит ваша презентация во время работы над ней. Окно онлайн-редактора презентаций: Окно десктопного редактора презентаций: На этой вкладке доступны следующие параметры просмотра: Масштаб - позволяет увеличивать и уменьшать масштаб презентации, По размеру слайда - позволяет изменить размер страницы, чтобы на экране отображался весь слайд, По ширине - позволяет изменить размер слайда, чтобы он соответствовал ширине экрана, Тема интерфейса - позволяет изменить тему интерфейса на Системную, Светлую, Классическую светлую, Темную или Контрастную темную, Следующие параметры позволяют настроить отображение или скрытие элементов. Отметьте галочкой элементы, чтобы сделать их видимыми: Заметки - всегда отображать панель заметок, Линейки - всегда отображать линейки, Направляющие и Линии сетки - чтобы правильно позиционировать объекты на слайде, Всегда показывать панель инструментов - чтобы верхняя панель инструментов всегда отображалась, Строка состояния - чтобы строка состояния всегда отображалась, Левая панель - чтобы левая панель отображалась, Правая панель - чтобы правая панель отображалась."
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Добавление гиперссылок", 
        "body": "Для добавления гиперссылки: установите курсор на том месте внутри текстового поля, где требуется добавить гиперссылку, перейдите на вкладку Вставка верхней панели инструментов, нажмите значок Гиперссылка, после этого появится окно Параметры гиперссылки, в котором можно указать параметры гиперссылки: Выберите тип ссылки, которую требуется вставить: Используйте опцию Внешняя ссылка и введите URL в формате http://www.example.com в расположенном ниже поле Связать с, если требуется добавить гиперссылку, ведущую на внешний сайт. Если надо добавить гиперссылку на локальный файл, введите URL в формате file://path/Presentation.pptx (для Windows) или file:///path/Presentation.pptx (для MacOS и Linux) в поле Связать с. Гиперссылки file://path/Presentation.pptx или file:///path/Presentation.pptx можно открыть только в десктопной версии редактора. В веб-редакторе можно только добавить такую ссылку без возможности открыть ее. Используйте опцию Слайд в этой презентации и выберите один из вариантов ниже, если требуется добавить гиперссылку, ведущую на определенный слайд в этой же презентации. Можно выбрать один из следующих переключателей: Следующий слайд, Предыдущий слайд, Первый слайд, Последний слайд, Слайд с указанным номером. Отображать - введите текст, который должен стать ссылкой и будет вести по заданному веб-адресу или на слайд, указанный в поле выше. Текст подсказки - введите текст краткого примечания к гиперссылке, который будет появляться в маленьком всплывающем окне при наведении на гиперссылку курсора. нажмите кнопку OK. Для добавления гиперссылки можно также использовать сочетание клавиш Ctrl+K или щелкнуть правой кнопкой мыши там, где требуется добавить гиперссылку, и выбрать в контекстном меню команду Гиперссылка. Примечание: также можно выделить мышью или с помощью клавиатуры символ, слово или словосочетание, а затем открыть окно Параметры гиперссылки, как описано выше. В этом случае поле Отображать будет содержать выделенный текстовый фрагмент. При наведении курсора на добавленную гиперссылку появится подсказка с заданным текстом. Для перехода по ссылке нажмите клавишу CTRL и щелкните по ссылке в презентации. Для редактирования или удаления добавленной гиперссылки, щелкните по ней правой кнопкой мыши, выберите опцию Гиперссылка, а затем действие, которое хотите выполнить, - Изменить гиперссылку или Удалить гиперссылку."
    },
   {
        "id": "UsageInstructions/AddingAnimations.htm", 
        "title": "Добавление анимаций", 
        "body": "Анимация — это визуальный эффект, позволяющий анимировать текст, объекты и фигуры, чтобы сделать презентацию более динамичной и подчеркнуть важную информацию. Вы можете управлять движением, цветом и размером текста, объектов и фигур. Добавление эффекта анимации перейдите на вкладку Анимация верхней панели инструментов, выберите текст, объект или графический элемент, к которому вы хотите применить эффект анимации, выберите эффект Анимации из галереи анимаций, выберите направление движения эффекта анимации, нажав Параметры рядом с галереей анимаций. Параметры в списке зависят от эффекта, который вы применяете. Вы можете предварительно просмотреть эффекты анимации на текущем слайде. По умолчанию, когда вы добавляете анимационные эффекты на слайд, они воспроизводятся автоматически, но вы можете отключить эту функцию. Откройте раскрывающийся список Просмотр на вкладке Анимация и выберите режим предварительного просмотра: Просмотр - чтобы включать предпросмотр по нажатию кнопки Просмотр, Автопросмотр - чтобы предпросмотр начинался автоматически при добавлении анимации к слайду. Типы анимации Все эффекты анимаций перечислены в галерее анимаций. Щелкните стрелку раскрывающегося списка, чтобы открыть его. Каждый эффект анимации представлен значком в виде звезды. Анимации сгруппированы в соответствии со своими эффектами: Эффекты входа определяют, как объекты отображаются на слайде, и в галерее выделены зеленым цветом. Эффекты выделения изменяют размер или цвет объекта, чтобы добавить акцент на объект и привлечь внимание аудитории, и в галерее окрашены в желтый двухцветные цвета. Эффекты выхода определяют, как объекты исчезают со слайда, и в галерее выделены красным цветом. Пути перемещения определяют движение объекта и путь, по которому он следует. Значки в галерее обозначают предлагаемый путь. Также доступна опция Пользовательский путь. Для получения дополнительной информации прочитайте следующую статью. Прокрутите галерею анимаций вниз, чтобы увидеть все эффекты, находящиеся в галерее. Если вы не видите нужную анимацию в галерее, нажмите кнопку Показать больше эффектов в нижней части галереи. Здесь вы найдете полный список эффектов анимации. Эффекты дополнительно сгруппированы по визуальному воздействию, которое они оказывают на аудиторию. Эффекты входа, Эффекты выделения и Эффекты выхода делятся на следующие группы: Базовые, Простые, Средние и Сложные. Пути перемещения делятся на следующие группы: Базовые, Простые, Средние. Добавление нескольких анимаций К одному и тому же объекту можно добавить несколько эффектов анимации. Чтобы добавить еще одну анимацию, на вкладке Анимация нажмите кнопку Добавить анимацию. Откроется список эффектов анимации. Повторите шаги 3 и 4, указанные выше, для применения анимации. Если вы используете Галерею анимации, а не кнопку Добавить анимацию, первый эффект анимации заменит новый. Небольшой квадрат рядом с объектом показывает порядковые номера примененных эффектов. Как только вы добавите к объекту несколько эффектов, в галерее анимаций появится значок Несколько анимаций. Изменение порядка эффектов анимации на слайде Щелкните значок квадрата анимации. На вкладке Анимация нажмите на стрелку или для изменения порядка отображения на слайде. Параметры времени анимации На вкладке Анимация вы можете редактировать параметры времени анимации, такие как запуск, длительность, задержка, повтор и перемотка назад. Параметры запуска анимации По щелчку – анимация запускается при нажатии на слайд. Данная опция выбрана по умолчанию. Вместе с предыдущим — анимация начинается, когда запускается предыдущий эффект анимации и эффекты появляются одновременно. После предыдущего — анимация начинается сразу после предыдущего эффекта анимации. Примечание: эффекты анимации автоматически нумеруются на слайде. Все анимации, для которых установлено значение Вместе с предыдущим и После предыдущего, получают номер анимации, с которой они связаны. Параметры триггера анимации Нажмите кнопку Триггер и выберите одну из доступных опций: По последовательности щелчков – запускать следующую анимацию по очереди каждый раз, когда вы нажимаете в любом месте слайда. Данная опция выбрана по умолчанию. По щелчку на — запуск анимации при нажатии на объект, выбранный из раскрывающегося списка. Другие параметры времени Длительность – установите время, в течение которого должна отображаться анимация. Выберите один из доступных вариантов в списке или введите необходимое значение времени. Задержка — установите время задержки между запуском эффекта анимации, если вам нужна пауза между эффектами. С помощью стрелок выберите необходимое значение времени или введите необходимое значение в секундах. Повтор — установите количество повторов, если хотите отобразить анимацию более одного раза. Нажмите на поле Повтор и выберите один из доступных вариантов или введите свое значение. Перемотка назад — установите этот флажок, если хотите вернуть объект в исходное состояние после окончания анимации."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Выравнивание и упорядочивание объектов на слайде", 
        "body": "Добавленные автофигуры, изображения, диаграммы или текстовые поля на слайде можно выровнять, сгруппировать, расположить в определенном порядке, распределить по горизонтали и вертикали. Для выполнения любого из этих действий сначала выберите отдельный объект или несколько объектов в области редактирования слайда. Для выделения нескольких объектов удерживайте клавишу Ctrl и щелкайте по нужным объектам левой кнопкой мыши. Чтобы выделить текстовое поле, щелкайте по его границе, а не по тексту внутри него. После этого можно использовать или описанные ниже значки, расположенные на вкладке Главная верхней панели инструментов, или аналогичные команды контекстного меню, вызываемого правой кнопкой мыши. Выравнивание объектов Чтобы выровнять два или более выделенных объектов, Щелкните по значку Выравнивание фигур на вкладке Главная верхней панели инструментов и выберите один из следующих вариантов: Выровнять относительно слайда чтобы выровнять объекты относительно краев слайда, Выровнять выделенные объекты (эта опция выбрана по умолчанию) чтобы выровнять объекты относительно друг друга, Еще раз нажмите на значок Выравнивание фигур и выберите из списка нужный тип выравнивания: Выровнять по левому краю - чтобы выровнять объекты по горизонтали по левому краю самого левого объекта/по левому краю слайда, Выровнять по центру - чтобы выровнять объекты по горизонтали по их центру/по центру слайда, Выровнять по правому краю - чтобы выровнять объекты по горизонтали по правому краю самого правого объекта/по правому краю слайда, Выровнять по верхнему краю - чтобы выровнять объекты по вертикали по верхнему краю самого верхнего объекта/по верхнему краю слайда, Выровнять по середине - чтобы выровнять объекты по вертикали по их середине/по середине слайда, Выровнять по нижнему краю - чтобы выровнять объекты по вертикали по нижнему краю самого нижнего объекта/по нижнему краю слайда. Можно также щелкнуть по выделенным объектам правой кнопкой мыши, выбрать из контекстного меню пункт Выравнивание, а затем использовать доступные варианты выравнивания объектов. Если требуется выровнять один объект, его можно выровнять относительно краев слайда. В этом случае по умолчанию выбрана опция Выровнять относительно слайда. Распределение объектов Чтобы распределить три или более выбранных объектов по горизонтали или вертикали таким образом, чтобы между ними было равное расстояние, Щелкните по значку Выравнивание фигур на вкладке Главная верхней панели инструментов и выберите один из следующих вариантов: Выровнять относительно слайда - чтобы распределить объекты между краями слайда, Выровнять выделенные объекты (эта опция выбрана по умолчанию) - чтобы распределить объекты между двумя крайними выделенными объектами, Еще раз нажмите на значок Выравнивание фигур и выберите из списка нужный тип распределения: Распределить по горизонтали - чтобы равномерно распределить объекты между самым левым и самым правым выделенным объектом/левым и правым краем слайда. Распределить по вертикали - чтобы равномерно распределить объекты между самым верхним и самым нижним выделенным объектом/верхним и нижним краем слайда. Можно также щелкнуть по выделенным объектам правой кнопкой мыши, выбрать из контекстного меню пункт Выравнивание, а затем использовать доступные варианты распределения объектов. Примечание: параметры распределения неактивны, если выделено менее трех объектов. Группировка объектов Чтобы сгруппировать два или более выбранных объектов или разгруппировать их, щелкните по значку Порядок фигур на вкладке Главная верхней панели инструментов и выберите из списка нужную опцию: Сгруппировать - чтобы объединить несколько объектов в группу, так что их можно будет одновременно поворачивать, перемещать, изменять их размер, выравнивать, упорядочивать, копировать, вставлять, форматировать как один объект. Разгруппировать - чтобы разгруппировать выбранную группу ранее сгруппированных объектов. Можно также щелкнуть по выделенным объектам правой кнопкой мыши, выбрать из контекстного меню пункт Порядок, а затем использовать опцию Сгруппировать или Разгруппировать. Примечание: параметр Сгруппировать неактивен, если выделено менее двух объектов. Параметр Разгруппировать активен только при выделении ранее сгруппированных объектов. Упорядочивание объектов Чтобы определенным образом расположить выбранный объект или объекты (например, изменить их порядок, если несколько объектов накладываются друг на друга), щелкните по значку Порядок фигур на вкладке Главная верхней панели инструментов и выберите из списка нужный тип расположения: Перенести на передний план - чтобы переместить выбранный объект (объекты), так что он будет находиться перед всеми остальными объектами, Перенести на задний план - чтобы переместить выбранный объект (объекты), так что он будет находиться позади всех остальных объектов, Перенести вперед - чтобы переместить выбранный объект (объекты) на один уровень вперед по отношению к другим объектам. Перенести назад - чтобы переместить выбранный объект (объекты) на один уровень назад по отношению к другим объектам. Можно также щелкнуть по выделенному объекту или объектам правой кнопкой мыши, выбрать из контекстного меню пункт Порядок, а затем использовать доступные варианты расположения объектов."
    },
   {
        "id": "UsageInstructions/ApplyTransitions.htm", 
        "title": "Применение переходов", 
        "body": "Переход - это эффект, который появляется между двумя слайдами при смене одного слайда другим во время показа. В Редакторе презентаций можно применить один и тот же переход ко всем слайдам или разные переходы к каждому отдельному слайду и настроить свойства перехода. Для применения перехода к отдельному слайду или нескольким выделенным слайдам: Откройте вкладку Переходы на верхней панели инструментов. Выберите слайд (или несколько слайдов в списке слайдов), к которым вы хотите применить переход. Выберите один из доступных эффектов перехода: Нет, Выцветание, Задвигание, Появление, Панорама, Открывание, Наплыв, Часы, Масштабирование. Нажмите кнопку Параметры, чтобы выбрать один из доступных вариантов эффекта. Они определяют, как конкретно проявляется эффект. Например, если выбран переход Масштабирование, доступны варианты Увеличение, Уменьшение и Увеличение с поворотом. Укажите, как долго должен длиться переход. В поле Длит. (длительность), введите или выберите нужное временное значение, измеряемое в секундах. Нажмите кнопку Просмотр, чтобы просмотреть слайд с примененным переходом в области редактирования слайда. Укажите, как долго должен отображаться слайд, пока не сменится другим: Запускать щелчком – установите этот флажок, если не требуется ограничивать время отображения выбранного слайда. Слайд будет сменяться другим только при щелчке по нему мышью. Задержка – используйте эту опцию, если выбранный слайд должен отображаться в течение заданного времени, пока не сменится другим. Установите этот флажок и введите или выберите нужное временное значение, измеряемое в секундах. Примечание: если установить только флажок Задержка, слайды будут сменяться автоматически через заданный промежуток времени. Если установить и флажок Запускать щелчком, и флажок Задержка и задать время задержки, слайды тоже будут сменяться автоматически, но вы также сможете щелкнуть по слайду, чтобы перейти от него к следующему. Для применения перехода ко всем слайдам в презентации: выполните все действия, описанные выше, и нажмите на кнопку Применить ко всем слайдам. Для удаления перехода: выделите нужный слайд и выберите пункт Нет в среди вариантов Эффекта перехода. Для удаления всех переходов: выделите любой слайд, выберите пункт Нет среди вариантов Эффекта и нажмите на кнопку Применить ко всем слайдам."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Копирование/очистка форматирования текста", 
        "body": "Чтобы скопировать определенное форматирование текста, выделите мышью или с помощью клавиатуры фрагмент текста с форматированием, которое надо скопировать, нажмите значок Копировать стиль на вкладке Главная верхней панели инструментов (указатель мыши будет при этом выглядеть так: ), выделите фрагмент текста, к которому требуется применить то же самое форматирование. Чтобы применить скопированное форматирование ко множеству фрагментов текста, с помощью мыши или клавиатуры выделите фрагмент текста, форматирование которого надо скопировать, дважды нажмите значок Копировать стиль на вкладке Главная верхней панели инструментов (указатель мыши будет при этом выглядеть так: , а значок Копировать стиль будет оставаться нажатым: ), поочередно выделяйте нужные фрагменты текста, чтобы применить одинаковое форматирование к каждому из них, для выхода из этого режима еще раз нажмите значок Копировать стиль или нажмите клавишу Esc на клавиатуре. Чтобы быстро убрать из текста примененное форматирование, выделите фрагмент текста, форматирование которого надо убрать, нажмите значок Очистить стиль на вкладке Главная верхней панели инструментов."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Копирование / вставка данных, отмена / повтор действий", 
        "body": "Использование основных операций с буфером обмена Для вырезания, копирования и вставки выделенных объектов (слайдов, фрагментов текста, автофигур) в текущей презентации или отмены / повтора действий используйте соответствующие команды контекстного меню или значки, доступные на любой вкладке верхней панели инструментов: Вырезать – выделите объект и используйте опцию контекстного меню Вырезать или значок Вырезать на верхней панели инструментов, чтобы удалить выделенный фрагмент и отправить его в буфер обмена компьютера. Вырезанные данные можно затем вставить в другое место этой же презентации. Копировать – выделите объект и используйте опцию контекстного меню Копировать или значок Копировать на верхней панели инструментов, чтобы отправить выделенные данные в буфер обмена компьютера. Скопированный объект можно затем вставить в другое место этой же презентации. Вставить – найдите то место в презентации, куда надо вставить ранее скопированный объект и используйте значок Вставить . Объект будет вставлен в текущей позиции курсора. Объект может быть ранее скопирован из этой же презентации. В онлайн-версии для копирования данных из другой презентации или какой-то другой программы или вставки данных в них используются только сочетания клавиш, в десктопной версии для любых операций копирования и вставки можно использовать как кнопки на панели инструментов или опции контекстного меню, так и сочетания клавиш: сочетание клавиш Ctrl+C для копирования; сочетание клавиш Ctrl+V для вставки; сочетание клавиш Ctrl+X для вырезания. Использование функции Специальная вставка Примечание: Во время совместной работы Специальная вставка доступна только в Строгом режиме редактирования. После вставки скопированных данных рядом со вставленным текстовым фрагментом или объектом появляется кнопка Специальная вставка . Нажмите на эту кнопку, чтобы выбрать нужный параметр вставки, или используйте клавишу Ctrl в сочетании с буквой, указанной в скобках рядом с необходимым параметром. При вставке фрагментов текста доступны следующие параметры: Использовать конечную тему (Ctrl+H) - позволяет применить форматирование, определяемое темой текущей презентации. Эта опция используется по умолчанию. Сохранить исходное форматирование (Ctrl+K) - позволяет сохранить исходное форматирование скопированного текста. Изображение (Ctrl+U) - позволяет вставить текст как изображение, чтобы его нельзя было редактировать. Сохранить только текст (Ctrl+T) - позволяет вставить текст без исходного форматирования. При вставке объектов (автофигур, диаграмм, таблиц) доступны следующие параметры: Использовать конечную тему (Ctrl+H) - позволяет применить форматирование, определяемое темой текущей презентации. Эта опция выбрана по умолчанию. Изображение (Ctrl+U) - позволяет вставить объект как изображение, чтобы его нельзя было редактировать. Чтобы включить / отключить автоматическое появление кнопки Специальная вставка после вставки, перейдите на вкладку Файл > Дополнительные параметры и поставьте / снимите галочку Показывать кнопку Параметры вставки при вставке содержимого. Отмена / повтор действий Для выполнения операций отмены/повтора используйте соответствующие значки в левой части шапки редактора или сочетания клавиш: Отменить – используйте значок Отменить , чтобы отменить последнее выполненное действие. Повторить – используйте значок Повторить , чтобы повторить последнее отмененное действие. Можно также использовать сочетание клавиш Ctrl+Z для отмены или Ctrl+Y для повтора действия. Обратите внимание: при совместном редактировании презентации в Быстром режиме недоступна возможность Повторить последнее отмененное действие."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Создание списков", 
        "body": "Для создания списка в презентации, установите курсор в том месте, где начнется список (это может быть новая строка или уже введенный текст), перейдите на вкладку Главная верхней панели инструментов, выберите тип списка, который требуется создать: Неупорядоченный список с маркерами создается с помощью значка Маркированный список , расположенного на верхней панели инструментов; Упорядоченный список с цифрами или буквами создается с помощью значка Нумерованный список , расположенного на верхней панели инструментов; Примечание: нажмите направленную вниз стрелку рядом со значком Маркированный список или Нумерованный список, чтобы выбрать, как должен выглядеть список. теперь при каждом нажатии клавиши Enter в конце строки будет появляться новый элемент упорядоченного или неупорядоченного списка. Чтобы закончить список, нажмите клавишу Backspace и продолжайте текст обычного абзаца. На вкладке Главная верхней панели инструментов при помощи значков Уменьшить отступ и Увеличить отступ можно изменить отступы текста в списках. Примечание: дополнительные параметры отступов и интервалов можно изменить на правой боковой панели и в окне дополнительных параметров. Чтобы получить дополнительную информацию об этом, прочитайте раздел Вставка и форматирование текста. Изменение параметров списков Чтобы изменить параметры списка, такие как тип, размер и цвет маркеров или нумерации: щелкните по какому-либо пункту существующего списка или выделите текст, который требуется отформатировать как список, нажмите на кнопку Маркированный список или Нумерованный список на вкладке Главная верхней панели инструментов, выберите опцию Параметры списка, откроется окно Параметры списка. Окно настроек маркированного списка выглядит следующим образом: Тип - позволяет выбрать нужный символ, используемый для маркированного списка. При нажатии на поле Новый маркер открывается окно Символ, в котором можно выбрать один из доступных символов. Для получения дополнительной информации о работе с символами вы можете обратиться к этой статье. При нажатии на поле Новое изображение появляется новое поле Импорт, в котором можно выбрать новое изображение для маркеров Из файла, По URL или Из хранилища. Размер - позволяет выбрать нужный размер для каждого из маркеров в зависимости от текущего размера текста. Может принимать значение от 25% до 400%. Цвет - позволяет выбрать нужный цвет маркеров. Для этого выберите на палитре один из цветов темы или стандартных цветов или задайте пользовательский цвет. Окно настроек нумерованного списка выглядит следующим образом: Тип - позволяет выбрать нужный числовой формат, используемый для нумерованного списка. Размер - позволяет выбрать нужный размер для нумераций в зависимости от текущего размера текста. Может принимать значение от 25% до 400%. Начать с - позволяет выбрать нужный порядковый номер, с которого будет начинаться нумерованный список. Цвет - позволяет выбрать нужный цвет нумерации. Для этого выберите на палитре один из цветов темы или стандартных цветов или задайте пользовательский цвет. нажмите кнопку OK, чтобы применить изменения и закрыть окно настроек."
    },
   {
        "id": "UsageInstructions/FillObjectsSelectColor.htm", 
        "title": "Заливка объектов и выбор цветов", 
        "body": "Можно использовать различные заливки для фона слайда, автофигур и шрифта объектов Text Art. Выберите объект Чтобы изменить заливку фона слайда, выделите нужные слайды в списке слайдов. На правой боковой панели будет активирована вкладка Параметры слайда. Чтобы изменить заливку автофигуры, щелкните по нужной автофигуре левой кнопкой мыши. На правой боковой панели будет активирована вкладка Параметры фигуры. Чтобы изменить заливку шрифта объекта Text Art, выделите нужный текстовый объект. На правой боковой панели будет активирована вкладка Параметры объектов Text Art. Определите нужный тип заливки Настройте свойства выбранной заливки (подробное описание для каждого типа заливки смотрите ниже) Примечание: для автофигур и шрифта объектов Text Art, независимо от выбранного типа заливки, можно также задать уровень Непрозрачности, перетаскивая ползунок или вручную вводя значение в процентах. Значение, заданное по умолчанию, составляет 100%. Оно соответствует полной непрозрачности. Значение 0% соответствует полной прозрачности. Доступны следующие типы заливки: Заливка цветом - выберите эту опцию, чтобы задать сплошной цвет, которым требуется заполнить внутреннее пространство выбранной фигуры или слайда. Нажмите на цветной прямоугольник, расположенный ниже, и выберите нужный цвет из доступных наборов цветов или задайте любой цвет, который вам нравится: Цвета темы - цвета, соответствующие выбранной теме/цветовой схеме презентации. Как только вы примените какую-то другую тему или цветовую схему, набор Цвета темы изменится. Стандартные цвета - набор стандартных цветов. Пользовательский цвет - щелкните по этой надписи, если в доступных палитрах нет нужного цвета. Выберите нужный цветовой диапазон, перемещая вертикальный ползунок цвета, и определите конкретный цвет, перетаскивая инструмент для выбора цвета внутри большого квадратного цветового поля. Как только Вы выберете какой-то цвет, в полях справа отобразятся соответствующие цветовые значения RGB и sRGB. Также можно задать цвет на базе цветовой модели RGB, введя нужные числовые значения в полях R, G, B (красный, зеленый, синий), или указать шестнадцатеричный код sRGB в поле, отмеченном знаком #. Выбранный цвет появится в окне предпросмотра Новый. Если к объекту был ранее применен какой-то пользовательский цвет, этот цвет отображается в окне Текущий, так что вы можете сравнить исходный и измененный цвета. Когда цвет будет задан, нажмите на кнопку Добавить: Пользовательский цвет будет применен к объекту и добавлен в палитру Пользовательский цвет. Примечание: такие же типы цветов можно использовать при выборе цвета обводки автофигуры, настройке цвета шрифта или изменении цвета фона или границ таблицы. Градиентная заливка - выберите эту опцию, чтобы залить слайд или фигуру двумя цветами, плавно переходящими друг в друга. Стиль - выберите один из доступных вариантов: Линейный (цвета изменяются по прямой, то есть по горизонтальной/вертикальной оси или по диагонали под углом 45 градусов) или Радиальный (цвета изменяются по кругу от центра к краям). Направление - в окне предварительного просмотра направления отображается выбранный цвет градиента. Нажмите на стрелку, чтобы выбрать шаблон из меню. Если выбран Линейный градиент, доступны следующие направления : из левого верхнего угла в нижний правый, сверху вниз, из правого верхнего угла в нижний левый, справа налево, из правого нижнего угла в верхний левый, снизу вверх, из левого нижнего угла в верхний правый, слева направо. Если выбран Радиальный градиент, доступен только один шаблон. Угол - установите числовое значение для точного угла перехода цвета. Точка градиента - это определенная точка перехода от одного цвета к другому. Чтобы добавить точку градиента, Используйте кнопку Добавить точку градиента или ползунок. Вы можете добавить до 10 точек градиента. Каждая следующая добавленная точка градиента никоим образом не повлияет на внешний вид текущей градиентной заливки. Чтобы удалить определенную точку градиента, используйте кнопку Удалить точку градиента. Чтобы изменить положение точки градиента, используйте ползунок или укажите Положение в процентах для точного местоположения. Чтобы применить цвет к точке градиента, щелкните точку на панели ползунка, а затем нажмите Цвет, чтобы выбрать нужный цвет. Изображение или текстура - выберите эту опцию, чтобы использовать в качестве фона фигуры или слайда изображение или предустановленную текстуру. Если Вы хотите использовать изображение в качестве фона фигуры или слайда, нажмите кнопку Выбрать изображение и добавьте изображение Из файла, выбрав его на жестком диске компьютера, или По URL, вставив в открывшемся окне соответствующий URL-адрес, или Из хранилища, выбрав нужное изображение, сохраненное на портале. Если Вы хотите использовать текстуру в качестве фона фигуры или слайда, разверните меню Из текстуры и выберите нужную предустановленную текстуру. В настоящее время доступны следующие текстуры: Холст, Картон, Темная ткань, Песок, Гранит, Серая бумага, Вязание, Кожа, Крафт-бумага, Папирус, Дерево. В том случае, если выбранное изображение имеет большие или меньшие размеры, чем автофигура или слайд, можно выбрать из выпадающего списка параметр Растяжение или Плитка. Опция Растяжение позволяет подогнать размер изображения под размер слайда или автофигуры, чтобы оно могло полностью заполнить пространство. Опция Плитка позволяет отображать только часть большего изображения, сохраняя его исходные размеры, или повторять меньшее изображение, сохраняя его исходные размеры, по всей площади слайда или автофигуры, чтобы оно могло полностью заполнить пространство. Примечание: любая выбранная предустановленная текстура полностью заполняет пространство, но в случае необходимости можно применить эффект Растяжение. Узор - выберите эту опцию, чтобы залить слайд или фигуру с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами. Узор - выберите один из готовых рисунков в меню. Цвет переднего плана - нажмите на это цветовое поле, чтобы изменить цвет элементов узора. Цвет фона - нажмите на это цветовое поле, чтобы изменить цвет фона узора. Без заливки - выберите эту опцию, если Вы вообще не хотите использовать заливку."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "Вставка и форматирование автофигур", 
        "body": "Вставка автофигуры Для добавления автофигуры на слайд: в списке слайдов слева выберите тот слайд, на который требуется добавить автофигуру, щелкните по значку Фигура на вкладке Главная или по стрелочке рядом с Галереей фигур на вкладке Вставка верхней панели инструментов, выберите одну из доступных групп автофигур: Основные фигуры, Фигурные стрелки, Математические знаки, Схемы, Звезды и ленты, Выноски, Кнопки, Прямоугольники, Линии, щелкните по нужной автофигуре внутри выбранной группы, в области редактирования слайда установите курсор там, где требуется поместить автофигуру, Примечание: чтобы растянуть фигуру, можно перетащить курсор при нажатой кнопке мыши. после того, как автофигура будет добавлена, можно изменить ее размер, местоположение и свойства. Примечание: чтобы добавить надпись внутри фигуры, убедитесь, что фигура на слайде выделена, и начинайте печатать текст. Текст, добавленный таким способом, становится частью автофигуры (при перемещении или повороте автофигуры текст будет перемещаться или поворачиваться вместе с ней). Также можно добавить автофигуру в макет слайда. Для получения дополнительной информации вы можете обратиться к этой статье. Изменение параметров автофигуры Некоторые параметры автофигуры можно изменить с помощью вкладки Параметры фигуры на правой боковой панели. Чтобы ее активировать, щелкните по автофигуре и выберите значок Параметры фигуры справа. Здесь можно изменить следующие свойства: Заливка - используйте этот раздел, чтобы выбрать заливку автофигуры. Можно выбрать следующие варианты: Заливка цветом - чтобы задать сплошной цвет, который требуется применить к выбранной фигуре. Градиентная заливка - чтобы залить фигуру двумя цветами, плавно переходящими друг в друга. Изображение или текстура - чтобы использовать в качестве фона фигуры какое-то изображение или готовую текстуру. Узор - чтобы залить фигуру с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами. Без заливки - выберите эту опцию, если вы вообще не хотите использовать заливку. Чтобы получить более подробную информацию об этих опциях, обратитесь к разделу Заливка объектов и выбор цветов. Контур - используйте этот раздел, чтобы изменить толщину, цвет или тип контура. Для изменения толщины контура выберите из выпадающего списка Толщина одну из доступных опций. Доступны следующие опции: 0.5 пт, 1 пт, 1.5 пт, 2.25 пт, 3 пт, 4.5 пт, 6 пт. Или выберите опцию Без линии, если вы вообще не хотите использовать контур. Для изменения цвета контура щелкните по цветному прямоугольнику и выберите нужный цвет. Можно использовать цвет выбранной темы, стандартный цвет или выбрать пользовательский цвет. Для изменения типа контура выберите нужную опцию из соответствующего выпадающего списка (по умолчанию применяется сплошная линия, ее можно изменить на одну из доступных пунктирных линий). Поворот - используется, чтобы повернуть фигуру на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить фигуру слева направо или сверху вниз. Нажмите на одну из кнопок: чтобы повернуть фигуру на 90 градусов против часовой стрелки чтобы повернуть фигуру на 90 градусов по часовой стрелке чтобы отразить фигуру по горизонтали (слева направо) чтобы отразить фигуру по вертикали (сверху вниз) Изменить автофигуру - используйте этот раздел, чтобы заменить текущую автофигуру на другую, выбрав ее из выпадающего списка. Отображать тень - отметьте эту опцию, чтобы отображать фигуру с тенью. Для изменения дополнительных параметров автофигуры щелкните по ней правой кнопкой мыши и выберите из контекстного меню пункт Дополнительные параметры фигуры или щелкните левой кнопкой мыши и нажмите на ссылку Дополнительные параметры на правой боковой панели. Откроется окно свойств фигуры: На вкладке Положение можно изменить Ширину и/или Высоту автофигуры. Если нажата кнопка Сохранять пропорции (в этом случае она выглядит так: ), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон фигуры. Также можно задать точную позицию, используя поля По горизонтали и По вертикали, а также поле От, где доступны параметры Верхний левый угол и По центру. Вкладка Поворот содержит следующие параметры: Угол - используйте эту опцию, чтобы повернуть фигуру на точно заданный угол. Введите в поле нужное значение в градусах или скорректируйте его, используя стрелки справа. Отражено - отметьте галочкой опцию По горизонтали, чтобы отразить фигуру по горизонтали (слева направо), или отметьте галочкой опцию По вертикали, чтобы отразить фигуру по вертикали (сверху вниз). Вкладка Линии и стрелки содержит следующие параметры: Стиль линии - эта группа опций позволяет задать такие параметры: Тип окончания - эта опция позволяет задать стиль окончания линии, поэтому ее можно применить только для фигур с разомкнутым контуром, таких как линии, ломаные линии и т.д.: Плоский - конечные точки будут плоскими. Закругленный - конечные точки будут закругленными. Квадратный - конечные точки будут квадратными. Тип соединения - эта опция позволяет задать стиль пересечения двух линий, например, она может повлиять на контур ломаной линии или углов треугольника или прямоугольника: Закругленный - угол будет закругленным. Скошенный - угол будет срезан наискось. Прямой - угол будет заостренным. Хорошо подходит для фигур с острыми углами. Примечание: эффект будет лучше заметен при использовании контура большей толщины. Стрелки - эта группа опций доступна только в том случае, если выбрана фигура из группы автофигур Линии. Она позволяет задать Начальный и Конечный стиль и Размер стрелки, выбрав соответствующие опции из выпадающих списков. Вкладка Текстовое поле содержит следующие параметры: Автоподбор - чтобы изменить способ отображения текста внутри фигуры: Без автоподбора, Сжать текст при переполнении, Подгонять размер фигуры под текст. Поля вокруг текста - чтобы изменить внутренние поля автофигуры Сверху, Снизу, Слева и Справа (то есть расстояние между текстом внутри фигуры и границами автофигуры). Примечание: эта вкладка доступна, только если в автофигуру добавлен текст, в противном случае вкладка неактивна. На вкладке Колонки можно добавить колонки текста внутри автофигуры, указав нужное Количество колонок (не более 16) и Интервал между колонками. После того как вы нажмете кнопку ОК, уже имеющийся текст или любой другой текст, который вы введете, в этой автофигуре будет представлен в виде колонок и будет перетекать из одной колонки в другую. Вкладка Альтернативный текст позволяет задать Заголовок и Описание, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит фигура. Чтобы заменить добавленную автофигуру, щелкните по ней левой кнопкой мыши и используйте выпадающий список Изменить автофигуру на вкладке Параметры фигуры правой боковой панели. Чтобы удалить добавленную автофигуру, щелкните по ней левой кнопкой мыши и нажмите клавишу Delete на клавиатуре. Чтобы узнать, как выровнять автофигуру на слайде или расположить в определенном порядке несколько автофигур, обратитесь к разделу Выравнивание и упорядочивание объектов на слайде. Соединение автофигур с помощью соединительных линий Автофигуры можно соединять, используя линии с точками соединения, чтобы продемонстрировать зависимости между объектами (например, если вы хотите создать блок-схему). Для этого: щелкните по значку Фигура на вкладке Главная или Вставка верхней панели инструментов, выберите в меню группу Линии, щелкните по нужной фигуре в выбранной группе (кроме трех последних фигур, которые не являются соединительными линиями, а именно Кривая, Рисованная кривая и Произвольная форма), наведите указатель мыши на первую автофигуру и щелкните по одной из точек соединения , появившихся на контуре фигуры, перетащите указатель мыши ко второй фигуре и щелкните по нужной точке соединения на ее контуре. При перемещении соединенных автофигур соединительная линия остается прикрепленной к фигурам и перемещается вместе с ними. Можно также открепить соединительную линию от фигур, а затем прикрепить ее к любым другим точкам соединения."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Вставка и редактирование диаграмм", 
        "body": "Вставка диаграммы Для вставки диаграммы в презентацию, Установите курсор там, где требуется поместить диаграмму. Перейдите на вкладку Вставка верхней панели инструментов. Щелкните по значку Диаграмма на верхней панели инструментов. Выберите из доступных типов диаграммы: Гистограмма Гистограмма с группировкой Гистограмма с накоплением Нормированная гистограмма с накоплением Трехмерная гистограмма с группировкой Трехмерная гистограмма с накоплением Трехмерная нормированная гистограмма с накоплением Трехмерная гистограмма График График График с накоплением Нормированный график с накоплением График с маркерами График с накоплениями с маркерами Нормированный график с маркерами и накоплением Трехмерный график Круговая Круговая Кольцевая диаграмма Трехмерная круговая диаграмма Линейчатая Линейчатая с группировкой Линейчатая с накоплением Нормированная линейчатая с накоплением Трехмерная линейчатая с группировкой Трехмерная линейчатая с накоплением Трехмерная нормированная линейчатая с накоплением С областями С областями Диаграмма с областями с накоплением Нормированная с областями и накоплением Биржевая Точечная Точечная диаграмма Точечная с гладкими кривыми и маркерами Точечная с гладкими кривыми Точечная с прямыми отрезками и маркерами Точечная с прямыми отрезками Комбинированные Гистограмма с группировкой и график Гистограмма с группировкой и график на вспомогательной оси С областями с накоплением и гистограмма с группировкой Пользовательская комбинация Примечание: Редактор презентаций ONLYOFFICE поддерживает следующие типы диаграмм, созданных в сторонних редакторах: Пирамида, Гистограмма (пирамида), Горизонтальные/Вертикальные цилиндры, Горизонтальные/вертикальные конусы. Вы можете открыть файл, содержащий такую диаграмму, и изменить его, используя доступные инструменты редактирования диаграмм. После этого появится окно Редактор диаграмм, в котором можно ввести в ячейки необходимые данные при помощи следующих элементов управления: и для копирования и вставки скопированных данных и для отмены и повтора действий для вставки функции и для уменьшения и увеличения числа десятичных знаков для изменения числового формата, то есть того, каким образом выглядят введенные числа в ячейках для выбора диаграммы другого типа. Нажмите кнопку Выбрать данные, расположенную в окне Редактора диаграмм. Откроется окно Данные диаграммы. Используйте диалоговое окно Данные диаграммы для управления диапазоном данных диаграммы, элементами легенды (ряды), подписями горизонтальной оси (категории) и переключением строк / столбцов. Диапазон данных для диаграммы - выберите данные для вашей диаграммы. Щелкните значок справа от поля Диапазон данных для диаграммы, чтобы выбрать диапазон ячеек. Элементы легенды (ряды) - добавляйте, редактируйте или удаляйте записи легенды. Введите или выберите ряд для записей легенды. В Элементах легенды (ряды) нажмите кнопку Добавить. В диалоговом окне Изменить ряд выберите диапазон ячеек для легенды или нажмите на иконку справа от поля Имя ряда. Подписи горизонтальной оси (категории) - изменяйте текст подписи категории. В Подписях горизонтальной оси (категории) нажмите Редактировать. В поле Диапазон подписей оси введите названия для категорий или нажмите на иконку , чтобы выбрать диапазон ячеек. Переключить строку/столбец - переставьте местами данные, которые расположены на диаграмме. Переключите строки на столбцы, чтобы данные отображались на другой оси. Нажмите кнопку ОК, чтобы применить изменения и закрыть окно. Измените параметры диаграммы, нажав на кнопку Изменить тип диаграммы в окне Редактор диаграмм, чтобы выбрать тип и стиль диаграммы. Выберите диаграмму из доступных разделов: гистограмма, график, круговая, линейчатая, с областями, биржевая, точечная, комбинированные. Когда вы выбираете Комбинированные диаграммы, в окне Тип диаграммы расположены ряды диаграмм, для которых можно выбрать типы диаграмм и данные для размещения на вторичной оси. Измените параметры диаграммы, нажав кнопку Редактировать диаграмму в окне Редактор диаграмм. Откроется окно Диаграмма - Дополнительные параметры. На вкладке Макет можно изменить расположение элементов диаграммы: Укажите местоположение Заголовка диаграммы относительно диаграммы, выбрав нужную опцию из выпадающего списка: Нет, чтобы заголовок диаграммы не отображался, Наложение, чтобы наложить заголовок на область построения диаграммы и выровнять его по центру, Без наложения, чтобы показать заголовок над областью построения диаграммы. Укажите местоположение Условных обозначений относительно диаграммы, выбрав нужную опцию из выпадающего списка: Нет, чтобы условные обозначения не отображались, Снизу, чтобы показать условные обозначения и расположить их в ряд под областью построения диаграммы, Сверху, чтобы показать условные обозначения и расположить их в ряд над областью построения диаграммы, Справа, чтобы показать условные обозначения и расположить их справа от области построения диаграммы, Слева, чтобы показать условные обозначения и расположить их слева от области построения диаграммы, Наложение слева, чтобы наложить условные обозначения на область построения диаграммы и выровнять их по центру слева, Наложение справа, чтобы наложить условные обозначения на область построения диаграммы и выровнять их по центру справа. Определите параметры Подписей данных (то есть текстовых подписей, показывающих точные значения элементов данных): Укажите местоположение Подписей данных относительно элементов данных, выбрав нужную опцию из выпадающего списка. Доступные варианты зависят от выбранного типа диаграммы. Для Гистограмм и Линейчатых диаграмм можно выбрать следующие варианты: Нет, По центру, Внутри снизу, Внутри сверху, Снаружи сверху. Для Графиков и Точечных или Биржевых диаграмм можно выбрать следующие варианты: Нет, По центру, Слева, Справа, Сверху, Снизу. Для Круговых диаграмм можно выбрать следующие варианты: Нет, По центру, По ширине, Внутри сверху, Снаружи сверху. Для диаграмм С областями, а также для Гистограмм, Графиков и Линейчатых диаграмм в формате 3D можно выбрать следующие варианты: Нет, По центру. Выберите данные, которые вы хотите включить в ваши подписи, поставив соответствующие флажки: Имя ряда, Название категории, Значение, Введите символ (запятая, точка с запятой и т.д.), который вы хотите использовать для разделения нескольких подписей, в поле Разделитель подписей данных. Линии - используется для выбора типа линий для линейчатых/точечных диаграмм. Можно выбрать одну из следующих опций: Прямые для использования прямых линий между элементами данных, Сглаженные для использования сглаженных кривых линий между элементами данных или Нет для того, чтобы линии не отображались. Маркеры - используется для указания того, нужно показывать маркеры (если флажок поставлен) или нет (если флажок снят) на линейчатых/точечных диаграммах. Примечание: Опции Линии и Маркеры доступны только для Линейчатых диаграмм и Точечных диаграмм. Вкладка Вертикальная ось позволяет изменять параметры вертикальной оси, также называемой осью значений или осью Y, которая отображает числовые значения. Обратите внимание, что вертикальная ось будет осью категорий, которая отображает подпись для Гистограмм, таким образом, параметры вкладки Вертикальная ось будут соответствовать параметрам, описанным в следующем разделе. Для Точечных диаграмм обе оси являются осями значений. Примечание: Параметры оси и Линии сетки недоступны для круговых диаграмм, так как у круговых диаграмм нет осей и линий сетки. Нажмите Скрыть ось, чтобы скрыть вертикальную ось на диаграмме. Укажите ориентацию Заголовка, выбрав нужный вариант из раскрывающегося списка: Нет - не отображать название вертикальной оси, Повернутое - показать название снизу вверх слева от вертикальной оси, По горизонтали - показать название по горизонтали слева от вертикальной оси. Минимум - используется для указания наименьшего значения, которое отображается в начале вертикальной оси. По умолчанию выбрана опция Авто; в этом случае минимальное значение высчитывается автоматически в зависимости от выбранного диапазона данных. Можно выбрать из выпадающего списка опцию Фиксированный и указать в поле справа другое значение. Максимум - используется для указания наибольшего значения, которое отображается в конце вертикальной оси. По умолчанию выбрана опция Авто; в этом случае максимальное значение высчитывается автоматически в зависимости от выбранного диапазона данных. Можно выбрать из выпадающего списка опцию Фиксированный и указать в поле справа другое значение. Пересечение с осью - используется для указания точки на вертикальной оси, в которой она должна пересекаться с горизонтальной осью. По умолчанию выбрана опция Авто; в этом случае точка пересечения осей определяется автоматически в зависимости от выбранного диапазона данных. Можно выбрать из выпадающего списка опцию Значение и указать в поле справа другое значение или установить точку пересечения осей на Минимум/Максимум на вертикальной оси. Единицы отображения - используется для определения порядка числовых значений на вертикальной оси. Эта опция может пригодиться, если вы работаете с большими числами и хотите, чтобы отображение цифр на оси было более компактным и удобочитаемым (например, можно сделать так, чтобы 50 000 показывалось как 50, воспользовавшись опцией Тысячи). Выберите желаемые единицы отображения из выпадающего списка: Сотни, Тысячи, 10 000, 100 000, Миллионы, 10 000 000, 100 000 000, Миллиарды, Триллионы или выберите опцию Нет, чтобы вернуться к единицам отображения по умолчанию. Значения в обратном порядке - используется для отображения значений в обратном порядке. Когда этот флажок снят, наименьшее значение находится внизу, а наибольшее - наверху. Когда этот флажок отмечен, значения располагаются сверху вниз. Раздел Параметры делений позволяет настроить отображение делений на вертикальной шкале. Основной тип - это деления шкалы большего размера, на которых могут быть подписи с числовыми значениями. Дополнительный тип - это деления шкалы, которые помещаются между основными делениями и не имеют подписей. Отметки также определяют, где могут отображаться линии сетки, если соответствующий параметр установлен на вкладке Макет. В раскрывающихся списках Основной/Дополнительный тип содержатся следующие варианты размещения: Нет - не отображать основные/дополнительные деления, На пересечении - отображать основные/дополнительные деления по обе стороны от оси, Внутри - отображать основные/дополнительные деления внутри оси, Снаружи - отображать основные/дополнительные деления за пределами оси. Раздел Параметры подписи позволяет определить положение подписей основных делений, отображающих значения. Для того, чтобы задать Положение подписи относительно вертикальной оси, выберите нужную опцию из выпадающего списка: Нет - не отображать подписи, Ниже - показывать подписи слева от области диаграммы, Выше - показывать подписи справа от области диаграммы, Рядом с осью - показывать подписи рядом с осью. Чтобы указать Формат подписи, нажмите Формат подписи и в окне Числовой формат выберите подходящую категорию. Доступные категории подписей: Общий Числовой Научный Финансовый Денежный Дата Время Процентный Дробный Текстовый Особый Параметры формата подписи различаются в зависимости от выбранной категории. Для получения дополнительной информации об изменении числового формата, пожалуйста, обратитесь к этой странице. Установите флажок напротив Связать с источником, чтобы сохранить форматирование чисел из источника данных в диаграмме. Примечание: второстепенные оси поддерживаются только в Комбинированных диаграммах. Второстепенные оси полезны в комбинированных диаграммах, когда ряды данных значительно различаются или для построения диаграммы используются смешанные типы данных. Второстепенные оси упрощают чтение и понимание комбинированной диаграммы. Вкладка Вспомогательная вертикальная / горизонтальная ось появляется, когда вы выбираете соответствующий ряд данных для комбинированной диаграммы. Все настройки и параметры на вкладке Вспомогательная вертикальная/горизонтальная ось такие же, как настройки на вертикальной / горизонтальной оси. Подробное описание параметров Вертикальная / горизонтальная ось смотрите выше / ниже. Вкладка Горизонтальная ось позволяет изменять параметры горизонтальной оси, также называемой осью категорий или осью x, которая отображает текстовые метки. Обратите внимание, что горизонтальная ось будет осью значений, которая отображает числовые значения для Гистограмм, поэтому в этом случае параметры вкладки Горизонтальная ось будут соответствовать параметрам, описанным в предыдущем разделе. Для Точечных диаграмм обе оси являются осями значений. Нажмите Скрыть ось, чтобы скрыть горизонтальную ось на диаграмме. Укажите ориентацию Заголовка, выбрав нужный вариант из раскрывающегося списка: Нет - не отображать заголовок горизонтальной оси, Без наложения - отображать заголовок под горизонтальной осью, Линии сетки используется для отображения Горизонтальных линий сетки путем выбора необходимого параметра в раскрывающемся списке: Нет, Основные, Незначительное или Основные и Дополнительные. Пересечение с осью - используется для указания точки на горизонтальной оси, в которой вертикальная ось должна пересекать ее. По умолчанию выбрана опция Авто, в этом случае точка пересечения осей определяется автоматически в зависимости от выбранного диапазона данных. Из выпадающего списка можно выбрать опцию Значение и указать в поле справа другое значение или установить точку пересечения осей на Минимальном/Максимальном значении на вертикальной оси. Положение оси - используется для указания места размещения подписей на оси: на Делениях или Между делениями. Значения в обратном порядке - используется для отображения категорий в обратном порядке. Когда этот флажок снят, категории располагаются слева направо. Когда этот флажок отмечен, категории располагаются справа налево. Раздел Параметры делений позволяет определять местоположение делений на горизонтальной шкале. Деления основного типа - это более крупные деления шкалы, у которых могут быть подписи, отображающие значения категорий. Деления дополнительного типа - это более мелкие деления шкалы, которые располагаются между делениями основного типа и у которых нет подписей. Кроме того, деления шкалы указывают, где могут отображаться линии сетки, если на вкладке Макет выбрана соответствующая опция. Можно редактировать следующие параметры делений: Основной/Дополнительный тип - используется для указания следующих вариантов размещения: Нет, чтобы деления основного/дополнительного типа не отображались, На пересечении, чтобы отображать деления основного/дополнительного типа по обеим сторонам оси, Внутри чтобы отображать деления основного/дополнительного типа с внутренней стороны оси, Снаружи, чтобы отображать деления основного/дополнительного типа с наружной стороны оси. Интервал между делениями - используется для указания того, сколько категорий нужно показывать между двумя соседними делениями. Раздел Параметры подписи позволяет настроить внешний вид меток, отображающих категории. Положение подписи - используется для указания того, где следует располагать подписи относительно горизонтальной оси. Выберите нужную опцию из выпадающего списка: Нет, чтобы подписи категорий не отображались, Ниже, чтобы подписи категорий располагались снизу области диаграммы, Выше, чтобы подписи категорий располагались наверху области диаграммы, Рядом с осью, чтобы подписи категорий отображались рядом с осью. Расстояние до подписи - используется для указания того, насколько близко подписи должны располагаться от осей. Можно указать нужное значение в поле ввода. Чем это значение больше, тем дальше расположены подписи от осей. Интервал между подписями - используется для указания того, как часто нужно показывать подписи. По умолчанию выбрана опция Авто, в этом случае подписи отображаются для каждой категории. Можно выбрать опцию Вручную и указать нужное значение в поле справа. Например, введите 2, чтобы отображать подписи у каждой второй категории, и т.д. Чтобы указать Формат подписи, нажмите Формат подписи и в окне Числовой формат выберите подходящую категорию. Доступные категории подписей: Общий Числовой Научный Финансовый Денежный Дата Время Процентный Дробный Текстовый Особый Параметры формата подписи различаются в зависимости от выбранной категории. Для получения дополнительной информации об изменении числового формата, пожалуйста, обратитесь к этой странице. Установите флажок напротив Связать с источником, чтобы сохранить форматирование чисел из источника данных в диаграмме. Вкладка Привязка к ячейке содержит следующие параметры: Перемещать и изменять размеры вместе с ячейками - эта опция позволяет привязать диаграмму к ячейке позади нее. Если ячейка перемещается (например, при вставке или удалении нескольких строк/столбцов), диаграмма будет перемещаться вместе с ячейкой. При увеличении или уменьшении ширины или высоты ячейки размер диаграммы также будет изменяться. Перемещать, но не изменять размеры вместе с ячейками - эта опция позволяет привязать диаграмму к ячейке позади нее, не допуская изменения размера диаграммы. Если ячейка перемещается, диаграмма будет перемещаться вместе с ячейкой, но при изменении размера ячейки размеры диаграммы останутся неизменными. Не перемещать и не изменять размеры вместе с ячейками - эта опция позволяет запретить перемещение или изменение размера диаграммы при изменении положения или размера ячейки. Вкладка Альтернативный текст позволяет задать Заголовок и Описание, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит диаграмма. После того, как диаграмма будет добавлена, можно изменить ее размер и положение. Вы можете задать положение диаграммы на слайде, перетащив ее по вертикали или горизонтали. Также можно добавить диаграмму в текстовый заполнитель, нажав на значок Диаграмма внутри него и выбрав нужный тип диаграммы: Также можно добавить диаграмму в макет слайда. Для получения дополнительной информации вы можете обратиться к этой статье. Редактирование элементов диаграммы Чтобы изменить Заголовок диаграммы, выделите мышью стандартный текст и введите вместо него свой собственный. Чтобы изменить форматирование шрифта внутри текстовых элементов, таких как заголовок диаграммы, названия осей, элементы условных обозначений, подписи данных и так далее, выделите нужный текстовый элемент, щелкнув по нему левой кнопкой мыши. Затем используйте значки на вкладке Главная верхней панели инструментов, чтобы изменить тип, стиль, размер или цвет шрифта. При выборе диаграммы становится также активным значок Параметры фигуры справа, так как фигура используется в качестве фона для диаграммы. Можно щелкнуть по этому значку, чтобы открыть вкладку Параметры фигуры на правой боковой панели инструментов и изменить параметры Заливки и Обводки фигуры. Обратите, пожалуйста, внимание, что вы не можете изменить тип фигуры. C помощью вкладки Параметры фигуры на правой боковой панели можно изменить не только саму область диаграммы, но и элементы диаграммы, такие как область построения, ряды данных, заголовок диаграммы, легенда и другие, и применить к ним различные типы заливки. Выберите элемент диаграммы, нажав на него левой кнопкой мыши, и выберите нужный тип заливки: сплошной цвет, градиент, текстура или изображение, узор. Настройте параметры заливки и при необходимости задайте уровень прозрачности. При выделении вертикальной или горизонтальной оси или линий сетки на вкладке Параметры фигуры будут доступны только параметры обводки: цвет, толщина и тип линии. Для получения дополнительной информации о работе с цветами, заливками и обводкой фигур можно обратиться к этой странице. Обратите внимание: параметр Отображать тень также доступен на вкладке Параметры фигуры, но для элементов диаграммы он неактивен. Если вам нужно изменить размер элементов диаграммы, щелкните левой кнопкой мыши, чтобы выбрать нужный элемент, и перетащите один из 8 белых квадратов , расположенных по периметру элемента. Чтобы изменить положение элемента, щелкните по нему левой кнопкой мыши, убедитесь, что ваш курсор изменился на , удерживайте левую кнопку мыши и перетащите элемент в нужное положение. Чтобы удалить элемент диаграммы, выделите его, щелкнув левой кнопкой мыши, и нажмите клавишу Delete на клавиатуре. Можно также поворачивать 3D-диаграммы с помощью мыши. Щелкните левой кнопкой мыши внутри области построения диаграммы и удерживайте кнопку мыши. Не отпуская кнопку мыши, перетащите курсор, чтобы изменить ориентацию 3D-диаграммы. Изменение параметров диаграммы Размер, тип и стиль диаграммы, а также данные, используемые для построения диаграммы, можно изменить с помощью правой боковой панели. Чтобы ее активировать, щелкните по диаграмме и выберите значок Параметры диаграммы справа. Раздел Размер позволяет изменить ширину и/или высоту диаграммы. Если нажата кнопка Сохранять пропорции (в этом случае она выглядит так: ), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон диаграммы. Раздел Изменить тип диаграммы позволяет изменить выбранный тип и/или стиль диаграммы с помощью соответствующего выпадающего меню. Для выбора нужного Стиля диаграммы используйте второе выпадающее меню в разделе Изменить тип диаграммы. Кнопка Изменить данные позволяет вызвать окно Редактор диаграмм и начать редактирование данных, как описано выше. Примечание: чтобы быстро вызвать окно Редактор диаграмм, можно также дважды щелкнуть мышью по диаграмме на слайде. Для 3D-диаграмм также доступны настройки Трехмерного поворота: По оси X - задайте нужное значение для поворота по оси X с помощью клавиатуры или расположенных справа стрелок Влево и Вправо. По оси Y - задайте нужное значение для поворота по оси Y с помощью клавиатуры или расположенных справа стрелок Влево и Вправо. Перспектива - задайте нужное значение для поворота по глубине с помощью клавиатуры или расположенных справа стрелок Сузить поле зрения и Расширить поле зрения. Оси под прямым углом - используется, чтобы установить вид оси под прямым углом. Автомасштабирование - отметьте эту опцию, чтобы автоматически масштабировать значения глубины и высоты диаграммы, или снимите эту галочку, чтобы задать значения глубины и высоты вручную. Глубина (% от базовой) - задайте нужное значение глубины с помощью клавиатуры или стрелок. Высота (% от базовой) - задайте нужное значение высоты с помощью клавиатуры или стрелок. Поворот по умолчанию - установите параметры трехмерного поворота по умолчанию. Пожалуйста, обратите внимание, что нельзя отредактировать отдельные элементы диаграммы; настройки будут применены к диаграмме в целом. Опция Дополнительные параметры на правой боковой панели позволяет открыть окно Диаграмма - дополнительные параметры, в котором можно настроить следующие параметры: На вкладке Положение можно задать следующие свойства: Размер - используйте эту опцию, чтобы изменить ширину и/или высоту диаграммы. Если нажата кнопка Сохранять пропорции (в этом случае она выглядит так: ), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон диаграммы. Позиция - задайте точную позицию, используя поля По горизонтали и По вертикали, а также поле От, где доступны параметры Верхний левый угол и По центру. Вкладка Альтернативный текст позволяет задать Заголовок и Описание, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит диаграмма. Чтобы удалить добавленную диаграмму, щелкните по ней левой кнопкой мыши и нажмите клавишу Delete на клавиатуре. Чтобы узнать, как выровнять диаграмму на слайде или расположить в определенном порядке несколько объектов, обратитесь к разделу Выравнивание и упорядочивание объектов на слайде."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Вставка уравнений", 
        "body": "В редакторе презентаций вы можете создавать уравнения, используя встроенные шаблоны, редактировать их, вставлять специальные символы (в том числе математические знаки, греческие буквы, диакритические знаки и т.д.). Добавление нового уравнения Чтобы вставить уравнение из коллекции, установите курсор там, где требуется вставить уравнение, перейдите на вкладку Вставка верхней панели инструментов, нажмите на стрелку рядом со значком Уравнение на верхней панели инструментов, выберите нужную категорию уравнений на панели инструментов над вставленным уравнением, или выберите нужную категорию уравнений в открывшемся выпадающем списке. В настоящее время доступны следующие категории: Символы, Дроби, Индексы, Радикалы, Интегралы, Крупные операторы, Скобки, Функции, Диакритические знаки, Пределы и логарифмы, Операторы, Матрицы, нажмите на значок Параметры уравнений на панели инструментов над вставленным уравнением, чтобы открыть дополнительные настройки: Юникод или LaTeX, Профессиональный или Линейный, щелкните по определенному символу/уравнению в соответствующем наборе шаблонов. Выбранный символ или уравнение будут вставлены в центре текущего слайда. Если вы не видите границу рамки уравнения, щелкните внутри уравнения - граница будет отображена в виде пунктирной линии. Рамку уравнения можно свободно перемещать, изменять ее размер или поворачивать на слайде. Для этого щелкните по границе рамки уравнения (она будет отображена как сплошная линия) и используйте соответствующие маркеры. Каждый шаблон уравнения представляет собой совокупность слотов. Слот - это позиция для каждого элемента, образующего уравнение. Пустой слот, также называемый полем для заполнения, имеет пунктирный контур . Необходимо заполнить все поля, указав нужные значения. Ввод значений Курсор определяет, где появится следующий символ, который вы введете. Чтобы точно установить курсор, щелкните внутри поля для заполнения и используйте клавиши со стрелками на клавиатуре для перемещения курсора на один символ влево/вправо. Когда курсор будет установлен в нужную позицию, можно заполнить поле: введите требуемое цифровое или буквенное значение с помощью клавиатуры, вставьте специальный символ, используя палитру Символы из меню Уравнение на вкладке Вставка верхней панели инструментов или вводя их с клавиатуры (см. описание функции Автозамена математическими символами), добавьте шаблон другого уравнения с палитры, чтобы создать сложное вложенное уравнение. Размер начального уравнения будет автоматически изменен в соответствии с содержимым. Размер элементов вложенного уравнения зависит от размера поля начального уравнения, но не может быть меньше, чем размер мелкого индекса. Для добавления некоторых новых элементов уравнений можно также использовать пункты контекстного меню: Чтобы добавить новый аргумент, идущий до или после имеющегося аргумента в Скобках, можно щелкнуть правой кнопкой мыши по существующему аргументу и выбрать из контекстного меню пункт Вставить аргумент перед/после. Чтобы добавить новое уравнение в Наборах условий из группы Скобки, можно щелкнуть правой кнопкой мыши по пустому полю для заполнения или по введенному в него уравнению и выбрать из контекстного меню пункт Вставить уравнение перед/после. Чтобы добавить новую строку или новый столбец в Матрице, можно щелкнуть правой кнопкой мыши по полю для заполнения внутри нее, выбрать из контекстного меню пункт Добавить, а затем - опцию Строку выше/ниже или Столбец слева/справа. Примечание: в настоящее время не поддерживается ввод уравнений в линейном формате, то есть в виде \\sqrt(4&x^3). При вводе значений математических выражений не требуется использовать клавишу Пробел, так как пробелы между символами и знаками действий устанавливаются автоматически. Если уравнение слишком длинное и не помещается на одной строке внутри рамки уравнения, перенос на другую строку в процессе ввода осуществляется автоматически. Можно также вставить перенос строки в строго определенном месте, щелкнув правой кнопкой мыши по математическому оператору и выбрав из контекстного меню пункт Вставить принудительный разрыв. Выбранный оператор будет перенесен на новую строку. Чтобы удалить добавленный принудительный разрыв строки, щелкните правой кнопкой мыши по математическому оператору в начале новой строки и выберите пункт меню Удалить принудительный разрыв. Форматирование уравнений По умолчанию уравнение внутри рамки горизонтально выровнено по центру, а вертикально выровнено по верхнему краю рамки уравнения. Чтобы изменить горизонтальное или вертикальное выравнивание, установите курсор внутри рамки уравнения (контуры рамки будут отображены как пунктирные линии) и используйте соответствующие значки на вкладке Главная верхней панели инструментов. Чтобы увеличить или уменьшить размер шрифта в уравнении, щелкните мышью внутри рамки уравнения и выберите нужный размер шрифта из списка на вкладке Главная верхней панели инструментов. Все элементы уравнения изменятся соответственно. По умолчанию буквы в уравнении форматируются курсивом. В случае необходимости можно изменить стиль шрифта (выделение полужирным, курсив, зачеркивание) или цвет для всего уравнения или его части. Подчеркивание можно применить только ко всему уравнению, а не к отдельным символам. Выделите нужную часть уравнения путем перетаскивания. Выделенная часть будет подсвечена голубым цветом. Затем используйте нужные кнопки на вкладке Главная верхней панели инструментов, чтобы отформатировать выделенный фрагмент. Например, можно убрать форматирование курсивом для обычных слов, которые не являются переменными или константами. Для изменения некоторых элементов уравнений можно также использовать пункты контекстного меню: Чтобы изменить формат Дробей, можно щелкнуть правой кнопкой мыши по дроби и выбрать из контекстного меню пункт Изменить на диагональную/горизонтальную/вертикальную простую дробь (доступные опции отличаются в зависимости от типа выбранной дроби). Чтобы изменить положение Индексов относительно текста, можно щелкнуть правой кнопкой мыши по уравнению, содержащему индексы, и выбрать из контекстного меню пункт Индексы перед текстом/после текста. Чтобы изменить размер аргумента для уравнений из групп Индексы, Радикалы, Интегралы, Крупные операторы, Пределы и логарифмы, Операторы, а также для горизонтальных фигурных скобок и шаблонов с группирующим знаком из группы Диакритические знаки, можно щелкнуть правой кнопкой мыши по аргументу, который требуется изменить, и выбрать из контекстного меню пункт Увеличить/Уменьшить размер аргумента. Чтобы указать, надо ли отображать пустое поле для ввода степени в уравнении из группы Радикалы, можно щелкнуть правой кнопкой мыши по радикалу и выбрать из контекстного меню пункт Скрыть/Показать степень. Чтобы указать, надо ли отображать пустое поле для ввода предела в уравнении из группы Интегралы или Крупные операторы, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт Скрыть/Показать верхний/нижний предел. Чтобы изменить положение пределов относительно знака интеграла или оператора в уравнениях из группы Интегралы или Крупные операторы, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт Изменить положение пределов. Пределы могут отображаться справа от знака оператора (как верхние и нижние индексы) или непосредственно над и под знаком оператора. Чтобы изменить положение пределов относительно текста в уравнениях из группы Пределы и логарифмы и в шаблонах с группирующим знаком из группы Диакритические знаки, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт Предел над текстом/под текстом. Чтобы выбрать, какие из Скобок надо отображать, можно щелкнуть правой кнопкой мыши по выражению в скобках и выбрать из контекстного меню пункт Скрыть/Показать открывающую/закрывающую скобку. Чтобы управлять размером Скобок, можно щелкнуть правой кнопкой мыши по выражению в скобках. Пункт меню Растянуть скобки выбран по умолчанию, так что скобки могут увеличиваться в соответствии с размером выражения, заключенного в них, но вы можете снять выделение с этой опции, чтобы запретить растяжение скобок. Когда эта опция активирована, можно также использовать пункт меню Изменить размер скобок в соответствии с высотой аргумента. Чтобы изменить положение символа относительно текста для горизонтальных фигурных скобок или горизонтальной черты над/под уравнением из группы Диакритические знаки, можно щелкнуть правой кнопкой мыши по шаблону и и выбрать из контекстного меню пункт Символ/Черта над/под текстом. Чтобы выбрать, какие границы надо отображать для Уравнения в рамке из группы Диакритические знаки, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт Свойства границ, а затем - Скрыть/Показать верхнюю/нижнюю/левую/правую границу или Добавить/Скрыть горизонтальную/вертикальную/диагональную линию. Чтобы указать, надо ли отображать пустые поля для заполнения в Матрице, можно щелкнуть по ней правой кнопкой мыши и выбрать из контекстного меню пункт Скрыть/Показать поля для заполнения. Для выравнивания некоторых элементов уравнений можно использовать пункты контекстного меню: Чтобы выровнять уравнения в Наборах условий из группы Скобки, можно щелкнуть правой кнопкой мыши по уравнению, выбрать из контекстного меню пункт Выравнивание, а затем выбрать тип выравнивания: По верхнему краю, По центру или По нижнему краю. Чтобы выровнять Матрицу по вертикали, можно щелкнуть правой кнопкой мыши по матрице, выбрать из контекстного меню пункт Выравнивание матрицы, а затем выбрать тип выравнивания: По верхнему краю, По центру или По нижнему краю. Чтобы выровнять по горизонтали элементы внутри отдельного столбца Матрицы, можно щелкнуть правой кнопкой мыши по полю для заполнения внутри столбца, выбрать из контекстного меню пункт Выравнивание столбца, а затем выбрать тип выравнивания: По левому краю, По центру или По правому краю. Удаление элементов уравнения Чтобы удалить часть уравнения, выделите фрагмент, который требуется удалить, путем перетаскивания или удерживая клавишу Shift и используя клавиши со стрелками, затем нажмите на клавиатуре клавишу Delete. Слот можно удалить только вместе с шаблоном, к которому он относится. Чтобы удалить всё уравнение, щелкните по границе рамки уравнения, (она будет отображена как сплошная линия) и нажмите на клавиатуре клавишу Delete. Для удаления некоторых элементов уравнений можно также использовать пункты контекстного меню: Чтобы удалить Радикал, можно щелкнуть по нему правой кнопкой мыши и выбрать из контекстного меню пункт Удалить радикал. Чтобы удалить Нижний индекс и/или Верхний индекс, можно щелкнуть правой кнопкой мыши по содержащему их выражению и выбрать из контекстного меню пункт Удалить верхний индекс/нижний индекс. Если выражение содержит индексы, расположенные перед текстом, доступна опция Удалить индексы. Чтобы удалить Скобки, можно щелкнуть правой кнопкой мыши по выражению в скобках и выбрать из контекстного меню пункт Удалить вложенные знаки или Удалить вложенные знаки и разделители. Если выражение в Скобках содержит несколько аргументов, можно щелкнуть правой кнопкой мыши по аргументу, который требуется удалить, и выбрать из контекстного меню пункт Удалить аргумент. Если в Скобках заключено несколько уравнений (а именно, в Наборах условий), можно щелкнуть правой кнопкой мыши по уравнению, которое требуется удалить, и выбрать из контекстного меню пункт Удалить уравнение. Чтобы удалить Предел, можно щелкнуть по нему правой кнопкой мыши и выбрать из контекстного меню пункт Удалить предел. Чтобы удалить Диакритический знак, можно щелкнуть по нему правой кнопкой мыши и выбрать из контекстного меню пункт Удалить диакритический знак, Удалить символ или Удалить черту (доступные опции отличаются в зависимости от выбранного диакритического знака). Чтобы удалить строку или столбец Матрицы, можно щелкнуть правой кнопкой мыши по полю для заполнения внутри строки/столбца, который требуется удалить, выбрать из контекстного меню пункт Удалить, а затем - Удалить строку/столбец. Преобразование уравнений Если вы открываете существующий документ с уравнениями, которые были созданы с помощью старой версии редактора уравнений (например, в версиях, предшествующих MS Office 2007), эти уравнения необходимо преобразовать в формат Office Math ML, чтобы иметь возможность их редактировать. Чтобы преобразовать уравнение, дважды щелкните по нему. Откроется окно с предупреждением: Чтобы преобразовать только выбранное уравнение, нажмите кнопку Да в окне предупреждения. Чтобы преобразовать все уравнения в документе, поставьте галочку Применить ко всем уравнениям и нажмите кнопку Да. После преобразования уравнения вы сможете его редактировать."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Вставка колонтитулов", 
        "body": "Колонтитулы позволяют добавить на слайд дополнительную информацию, такую как дату и время, номер слайда или текст. Для вставки колонтитула в презентацию: перейдите на вкладку Вставка, нажмите кнопку Изменить нижний колонтитул на верхней панели инструментов, откроется окно Параметры нижнего колонтитула. Отметьте галочками данные, которые требуется добавить в нижний колонтитул. Изменения отображаются в окне предварительного просмотра справа. поставьте галочку Дата и время, чтобы вставить дату или время в выбранном формате. Выбранная дата будет добавлена в левое поле нижнего колонтитула слайда. Укажите нужный формат даты: Обновлять автоматически - отметьте эту радиокнопку, если требуется автоматически обновлять дату и время в соответствии с текущей датой и временем. Затем выберите из списков нужный Формат даты и времени и Язык. Фиксировано - отметьте эту радиокнопку, если не требуется автоматически обновлять дату и время. поставьте галочку Номер слайда, чтобы вставить номер текущего слайда. Номер слайда будет добавлен в правое поле нижнего колонтитула слайда. поставьте галочку Текст в нижнем колонтитуле, чтобы вставить любой текст. Введите нужный текст в расположенном ниже поле ввода. Текст будет добавлен в центральное поле нижнего колонтитула слайда. отметьте опцию Не показывать на титульном слайде, если это необходимо, нажмите кнопку Применить ко всем, чтобы применить изменения ко всем слайдам, или используйте кнопку Применить, чтобы применить изменения только к текущему слайду. Чтобы быстро вставить дату или номер слайда в нижний колонтитул выбранного слайда, можно использовать опции Показывать номер слайда и Показывать дату и время на вкладке Параметры слайда правой боковой панели. В этом случае выбранные настройки будут применены только к текущему слайду. Дату и время или номер слайда, добавленные таким образом, в дальнейшем можно настроить в окне Параметры нижнего колонтитула. Чтобы отредактировать добавленный нижний колонтитул, нажмите кнопку Изменить нижний колонтитул на верхней панели инструментов, внесите необходимые изменения в окне Параметры нижнего колонтитула и нажмите кнопку Применить или Применить ко всем, чтобы сохранить изменения. Вставка даты, времени и номера слайда в текстовое поле Также можно вставлять дату и время или номер слайда в выбранное текстовое поле, используя соответствующие кнопки на вкладке Вставка верхней панели инструментов. Вставка даты и времени установите курсор внутри текстового поля там, где требуется вставить дату и время, нажмите кнопку Дата и время на вкладке Вставка верхней панели инструментов, выберите нужный Язык из списка и выберите нужный Формат даты и времени в окне Дата и время, в случае необходимости поставьте галочку Обновлять автоматически и нажмите кнопку Установить по умолчанию, чтобы установить выбранный формат даты и времени как формат по умолчанию для указанного языка, нажмите кнопку ОК чтобы применить изменения. Дата и время будут вставлены в текущей позиции курсора. Чтобы отредактировать вставленную дату и время, выделите вставленную дату и время в текстовом поле, нажмите на кнопку Дата и время на вкладке Вставка верхней панели инструментов, выберите нужный формат в окне Дата и время, нажмите кнопку OK. Вставка номера слайда установите курсор внутри текстового поля там, где требуется вставить номер слайда, нажмите кнопку Номер слайда на вкладке Вставка верхней панели инструментов, поставьте галочку Номер слайда в окне Параметры нижнего колонтитула, нажмите кнопку Применить ко всем или Применить. Номер слайда будет вставлен в текущей позиции курсора."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Вставка и настройка изображений", 
        "body": "Вставка изображения В онлайн-редакторе презентаций можно вставлять в презентацию изображения самых популярных форматов. Поддерживаются следующие форматы изображений: BMP, GIF, JPEG, JPG, PNG. Для добавления изображения на слайд: в списке слайдов слева выберите тот слайд, на который требуется добавить изображение, щелкните по значку Изображение на вкладке Главная или Вставка верхней панели инструментов, для загрузки изображения выберите одну из следующих опций: при выборе опции Изображение из файла откроется стандартное диалоговое окно для выбора файлов. Выберите нужный файл на жестком диске компьютера и нажмите кнопку Открыть В онлайн-редакторе вы можете выбрать сразу несколько изображений. при выборе опции Изображение по URL откроется окно, в котором можно ввести веб-адрес нужного изображения, а затем нажать кнопку OK при выборе опции Изображение из хранилища откроется окно Выбрать источник данных. Выберите изображение, сохраненное на вашем портале, и нажмите кнопку OK после того как изображение будет добавлено, можно изменить его размер и положение. Вы также можете добавить изображение внутри текстовой рамки, нажав на кнопку Изображение из файла в ней и выбрав нужное изображение, сохраненное на компьютере, или используйте кнопку Изображение по URL и укажите URL-адрес изображения: Также можно добавить изображение в макет слайда. Для получения дополнительной информации вы можете обратиться к этой статье. Изменение параметров изображения Правая боковая панель активируется при щелчке по изображению левой кнопкой мыши и выборе значка Параметры изображения справа. Вкладка содержит следующие разделы: Размер - используется, чтобы просмотреть текущую Ширину и Высоту изображения или при необходимости восстановить Реальный размер изображения. Кнопка Обрезать используется, чтобы обрезать изображение. Нажмите кнопку Обрезать, чтобы активировать маркеры обрезки, которые появятся в углах изображения и в центре каждой его стороны. Вручную перетаскивайте маркеры, чтобы задать область обрезки. Вы можете навести курсор мыши на границу области обрезки, чтобы курсор превратился в значок , и перетащить область обрезки. Чтобы обрезать одну сторону, перетащите маркер, расположенный в центре этой стороны. Чтобы одновременно обрезать две смежных стороны, перетащите один из угловых маркеров. Чтобы равномерно обрезать две противоположные стороны изображения, удерживайте нажатой клавишу Ctrl при перетаскивании маркера в центре одной из этих сторон. Чтобы равномерно обрезать все стороны изображения, удерживайте нажатой клавишу Ctrl при перетаскивании любого углового маркера. Когда область обрезки будет задана, еще раз нажмите на кнопку Обрезать, или нажмите на клавишу Esc, или щелкните мышью за пределами области обрезки, чтобы применить изменения. После того, как область обрезки будет задана, также можно использовать опции Обрезать, Заливка и Вписать, доступные в выпадающем меню Обрезать. Нажмите кнопку Обрезать еще раз и выберите нужную опцию: При выборе опции Обрезать изображение будет заполнять определенную форму. Вы можете выбрать фигуру из галереи, которая открывается при наведении указателя мыши на опцию Обрезать по фигуре. Вы по-прежнему можете использовать опции Заливка и Вписать, чтобы настроить, как изображение будет соответствовать фигуре. При выборе опции Заливка центральная часть исходного изображения будет сохранена и использована в качестве заливки выбранной области обрезки, в то время как остальные части изображения будут удалены. При выборе опции Вписать размер изображения будет изменен, чтобы оно соответствовало высоте или ширине области обрезки. Никакие части исходного изображения не будут удалены, но внутри выбранной области обрезки могут появится пустые пространства. Заменить изображение - используется, чтобы загрузить другое изображение вместо текущего, выбрав нужный источник. Можно выбрать одну из опций: Из файла, Из хранилища или По URL. Опция Заменить изображение также доступна в контекстном меню, вызываемом правой кнопкой мыши. Поворот - используется, чтобы повернуть изображение на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить изображение слева направо или сверху вниз. Нажмите на одну из кнопок: чтобы повернуть изображение на 90 градусов против часовой стрелки чтобы повернуть изображение на 90 градусов по часовой стрелке чтобы отразить изображение по горизонтали (слева направо) чтобы отразить изображение по вертикали (сверху вниз) Когда изображение выделено, справа также доступен значок Параметры фигуры . Можно щелкнуть по нему, чтобы открыть вкладку Параметры фигуры на правой боковой панели и настроить тип, толщину и цвет Контуров фигуры, а также изменить тип фигуры, выбрав другую фигуру в меню Изменить автофигуру. Форма изображения изменится соответствующим образом. На вкладке Параметры фигуры также можно использовать опцию Отображать тень, чтобы добавить тень к изображеню. Чтобы изменить дополнительные параметры изображения, щелкните по нему правой кнопкой мыши и выберите из контекстного меню опцию Дополнительные параметры изображения или щелкните по изображению левой кнопкой мыши и нажмите на ссылку Дополнительные параметры на правой боковой панели. Откроется окно свойств изображения: Вкладка Положение позволяет задать следующие свойства изображения: Размер - используйте эту опцию, чтобы изменить ширину и/или высоту изображения. Если нажата кнопка Сохранять пропорции (в этом случае она выглядит так: ), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон изображения. Чтобы восстановить реальный размер добавленного изображения, нажмите кнопку Реальный размер. Положение - задайте точную позицию, используя поля По горизонтали и По вертикали, а также поле От, где доступны параметры Верхний левый угол и По центру. Вкладка Поворот содержит следующие параметры: Угол - используйте эту опцию, чтобы повернуть изображение на точно заданный угол. Введите в поле нужное значение в градусах или скорректируйте его, используя стрелки справа. Отражено - отметьте галочкой опцию По горизонтали, чтобы отразить изображение по горизонтали (слева направо), или отметьте галочкой опцию По вертикали, чтобы отразить изображение по вертикали (сверху вниз). Вкладка Альтернативный текст позволяет задать Заголовок и Описание, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение. Чтобы удалить вставленное изображение, щелкните по нему левой кнопкой мыши и нажмите клавишу Delete на клавиатуре. Чтобы узнать, как выровнять изображение на слайде или расположить в определенном порядке несколько изображений, обратитесь к разделу Выравнивание и упорядочивание объектов на слайде."
    },
   {
        "id": "UsageInstructions/InsertSmartArt.htm", 
        "title": "Вставка объектов SmartArt", 
        "body": "Графика SmartArt используется для создания визуального представления иерархической структуры при помощи выбора наиболее подходящего макета. Вставляйте новые графические объекты SmartArt или редактируйте объекты SmartArt, добавленные с помощью сторонних редакторов. Чтобы вставить объект SmartArt, перейдите на вкладку Вставка, нажмите кнопку SmartArt, наведите курсор на один из доступных стилей макета, например, Список или Процесс, выберите один из доступных типов макета из списка, появившегося справа от выделенного пункта меню. Вы можете настроить параметры SmartArt на правой панели: Заливка - используйте этот раздел, чтобы выбрать заливку объекта SmartArt. Можно выбрать следующие варианты: Заливка цветом - выберите эту опцию, чтобы задать сплошной цвет, которым требуется заполнить внутреннее пространство выбранного объекта SmartArt. Нажмите на цветной прямоугольник, расположенный ниже, и выберите нужный цвет из доступных наборов цветов или задайте любой цвет, который вам нравится. Градиентная заливка - выберите эту опцию, чтобы залить объект SmartArt двумя цветами, плавно переходящими друг в друга. Настраивайте градиентную заливку без ограничений. Нажмите значок Параметры фигуры , чтобы открыть меню Заливка на правой панели: Доступные пункты меню: Стиль - выберите Линейный или Радиальный: Линейный используется, когда вам нужно, чтобы цвета изменялись слева направо, сверху вниз или под любым выбранным вами углом в одном направлении. Чтобы выбрать предустановленное направление, щелкните на стрелку рядом с окном предварительного просмотра Направление или же задайте точное значение угла градиента в поле Угол. Радиальный используется, когда вам нужно, чтобы цвета изменялись по кругу от центра к краям. Точка градиента - это определенная точка перехода от одного цвета к другому. Чтобы добавить точку градиента, Используйте кнопку Добавить точку градиента или ползунок. Вы можете добавить до 10 точек градиента. Каждая следующая добавленная точка градиента никоим образом не повлияет на внешний вид текущей градиентной заливки. Чтобы удалить определенную точку градиента, используйте кнопку Удалить точку градиента. Чтобы изменить положение точки градиента, используйте ползунок или укажите Положение в процентах для точного местоположения. Чтобы применить цвет к точке градиента, щелкните точку на панели ползунка, а затем нажмите Цвет, чтобы выбрать нужный цвет. Изображение или текстура - выберите эту опцию, чтобы использовать в качестве фона объекта SmartArt какое-то изображение или готовую текстуру. Если вы хотите использовать изображение в качестве фона объекта SmartArt, можно добавить изображение Из файла, выбрав его на жестком диске компьютера, или По URL, вставив в открывшемся окне соответствующий URL-адрес, или Из хранилища, выбрав нужное изображение, сохраненное на портале. Если вы хотите использовать текстуру в качестве фона объекта SmartArt, разверните меню Из текстуры и выберите нужную предустановленную текстуру. В настоящее время доступны следующие текстуры: Холст, Картон, Темная ткань, Песок, Гранит, Серая бумага, Вязание, Кожа, Крафт-бумага, Папирус, Дерево. В том случае, если выбранное изображение имеет большие или меньшие размеры, чем объект SmartArt, можно выбрать из выпадающего списка параметр Растяжение или Плитка. Опция Растяжение позволяет подогнать размер изображения под размер объекта SmartArt, чтобы оно могло полностью заполнить пространство. Опция Плитка позволяет отображать только часть большего изображения, сохраняя его исходные размеры, или повторять меньшее изображение, сохраняя его исходные размеры, по всей площади объекта SmartArt, чтобы оно могло полностью заполнить пространство. Примечание: любая выбранная предустановленная текстура полностью заполняет пространство, но в случае необходимости можно применить эффект Растяжение. Узор - выберите эту опцию, чтобы залить объект SmartArt с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами. Узор - выберите один из готовых рисунков в меню. Цвет переднего плана - нажмите на это цветовое поле, чтобы изменить цвет элементов узора. Цвет фона - нажмите на это цветовое поле, чтобы изменить цвет фона узора. Без заливки - выберите эту опцию, если вы вообще не хотите использовать заливку. Контур - используйте этот раздел, чтобы изменить толщину, цвет или тип контура объекта SmartArt. Для изменения толщины контура выберите из выпадающего списка Толщина одну из доступных опций. Доступны следующие опции: 0.5 пт, 1 пт, 1.5 пт, 2.25 пт, 3 пт, 4.5 пт, 6 пт. Или выберите опцию Без линии, если вы вообще не хотите использовать контур. Для изменения цвета контура щелкните по цветному прямоугольнику и выберите нужный цвет. Для изменения типа контура выберите нужную опцию из соответствующего выпадающего списка (по умолчанию применяется сплошная линия, ее можно изменить на одну из доступных пунктирных линий). Стиль обтекания - используйте этот раздел, чтобы выбрать один из доступных стилей обтекания текстом - в тексте, вокруг рамки, по контуру, сквозное, сверху и снизу, перед текстом, за текстом. Отображать тень - отметьте эту опцию, чтобы отображать объект SmartArt с тенью. Нажмите ссылку Дополнительные параметры, чтобы открыть дополнительные параметры."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Вставка символов и знаков", 
        "body": "При работе может возникнуть необходимость поставить символ, которого нет на вашей клавиатуре. Для вставки таких символов используйте функцию Вставить символ. Для этого выполните следующие шаги: установите курсор, куда будет помещен символ, перейдите на вкладку Вставка верхней панели инструментов, щелкните по значку Символ, в открывшемся окне выберите необходимый символ, чтобы быстрее найти нужный символ, используйте раздел Набор. В нем все символы распределены по группам, например, выберите «Символы валют», если нужно вставить знак валют. Если же данный символ отсутствует в наборе, выберите другой шрифт. Во многих из них также есть символы, отличные от стандартного набора. Или же впишите в строку шестнадцатеричный Код знака из Юникод нужного вам символа. Данный код можно найти в Таблице символов. Также можно использовать вкладку Специальные символы для выбора специального символа из списка. Для быстрого доступа к нужным символам также используйте Ранее использовавшиеся символы, где хранятся несколько последних использованных символов, нажмите Вставить. Выбранный символ будет добавлен в презентацию. Вставка символов ASCII Для добавления символов также используется таблица символов ASCII. Для этого зажмите клавишу ALT и при помощи цифровой клавиатуры введите код знака. Обратите внимание: убедитесь, что используете цифровую клавиатуру, а не цифры на основной клавиатуре. Чтобы включить цифровую клавиатуру, нажмите клавишу Num Lock. Например, для добавления символа параграфа (§) нажмите и удерживайте клавишу ALT, введите цифры 789, а затем отпустите клавишу ALT. Вставка символов при помощи таблицы символов С помощью таблицы символов Windows так же можно найти символы, которых нет на клавиатуре. Чтобы открыть данную таблицу, выполните одно из следующих действий: В строке Поиск напишите «Таблица символов» и откройте ее Одновременно нажмите клавиши Win+R, в появившемся окне введите charmap.exe и щелкните ОК. В открывшемся окне Таблица символов выберите один из представленных Набор символов, их Группировку и Шрифт. Далее щелкните на нужные символы, скопируйте их в буфер обмена и вставьте в нужное место в презентации."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Вставка и форматирование таблиц", 
        "body": "Вставка таблицы Для вставки таблицы на слайд: выберите слайд, на который надо добавить таблицу, перейдите на вкладку Вставка верхней панели инструментов, нажмите значок Таблица на верхней панели инструментов, выберите опцию для создания таблицы: или таблица со стандартным количеством ячеек (максимум 10 на 8 ячеек) Если требуется быстро добавить таблицу, просто выделите мышью нужное количество строк (максимум 8) и столбцов (максимум 10). или пользовательская таблица Если Вам нужна таблица больше, чем 10 на 8 ячеек, выберите опцию Вставить пользовательскую таблицу, после чего откроется окно, в котором можно вручную ввести нужное количество строк и столбцов соответственно, затем нажмите кнопку OK. если вы хотите вставить таблицу как OLE-объект: Выберите опцию Вставить таблицу в меню Таблица на вкладке Вставка. Появится соответствующее окно, в котором вы можете ввести нужные данные и отформатировать их, используя инструменты форматирования Редактора электронных таблиц, такие как выбор шрифта, типа и стиля, настройка числового формата, вставка функций, форматированные таблицы и так далее. В шапке в правом верхнем углу окна находится кнопка Видимая область. Выберите опцию Редактировать видимую область, чтобы выбрать область, которая будет отображаться при вставке объекта в презентацию; другие данные не будут потеряны, а просто будут скрыты. Когда область будет выделена, нажмите кнопку Готово. Выберите опцию Показать видимую область, чтобы увидеть выбранную область, у которой будет голубая граница. Когда все будет готово, нажмите кнопку Сохранить и выйти. после того, как таблица будет добавлена, Вы сможете изменить ее свойства и положение. Вы также можете добавить таблицу внутри текстовой рамки, нажав на кнопку Таблица в ней и выбрав нужное количество ячеек или опцию Вставить пользовательскую таблицу: Чтобы изменить размер таблицы, перетаскивайте маркеры , расположенные по ее краям, пока таблица не достигнет нужного размера. Вы также можете вручную изменить ширину определенного столбца или высоту строки. Наведите курсор мыши на правую границу столбца, чтобы курсор превратился в двунаправленную стрелку , и перетащите границу влево или вправо, чтобы задать нужную ширину. Чтобы вручную изменить высоту отдельной строки, наведите курсор мыши на нижнюю границу строки, чтобы курсор превратился в двунаправленную стрелку , и перетащите границу вверх или вниз. Можно задать положение таблицы на слайде путем перетаскивания ее по вертикали или по горизонтали. Примечание: для перемещения по таблице можно использовать сочетания клавиш. Также можно добавить таблицу в макет слайда. Для получения дополнительной информации вы можете обратиться к этой статье. Изменение параметров таблицы Большинство свойств таблицы, а также ее структуру можно изменить с помощью правой боковой панели. Чтобы ее активировать, щелкните по таблице и выберите значок Параметры таблицы справа. Разделы Строки и Столбцы, расположенные наверху, позволяют выделить некоторые строки или столбцы при помощи особого форматирования, или выделить разные строки и столбцы с помощью разных цветов фона для их четкого разграничения. Доступны следующие опции: Заголовок - выделяет при помощи особого форматирования самую верхнюю строку в таблице. Итоговая - выделяет при помощи особого форматирования самую нижнюю строку в таблице. Чередовать - включает чередование цвета фона для четных и нечетных строк. Первый - выделяет при помощи особого форматирования крайний левый столбец в таблице. Последний - выделяет при помощи особого форматирования крайний правый столбец в таблице. Чередовать - включает чередование цвета фона для четных и нечетных столбцов. Раздел По шаблону позволяет выбрать один из готовых стилей таблиц. Каждый шаблон сочетает в себе определенные параметры форматирования, такие как цвет фона, стиль границ, чередование строк или столбцов и т.д. Набор шаблонов отображается по-разному в зависимости от параметров, указанных в разделах Строки и/или Столбцы выше. Например, если Вы отметили опцию Заголовок в разделе Строки и опцию Чередовать в разделе Столбцы, отображаемый список шаблонов будет содержать только шаблоны со строкой заголовка и чередованием столбцов: Раздел Стиль границ позволяет изменить примененное форматирование, соответствующее выбранному шаблону. Можно выделить всю таблицу или определенный диапазон ячеек, для которого необходимо изменить форматирование, и задать все параметры вручную. Параметры Границ - задайте толщину границы с помощью списка (или выберите опцию Без границ), выберите ее Цвет на доступных палитрах и определите, как они должны отображаться в ячейках, нажимая на значки: Цвет фона - выберите цвет фона внутри выбранных ячеек. Раздел Строки и столбцы позволяет выполнить следующие операции: Выбрать строку, столбец, ячейку (в зависимости от позиции курсора) или всю таблицу. Вставить новую строку выше или ниже выделенной, а также новый столбец слева или справа от выделенного. Удалить строку, столбец (в зависимости от позиции курсора или выделения) или всю таблицу. Объединить ячейки - чтобы объединить предварительно выделенные ячейки в одну. Разделить ячейку... - чтобы разделить предварительно выделенную ячейку на определенное количество строк и столбцов. Эта команда вызывает следующее окно: Укажите Количество столбцов и Количество строк, на которое необходимо разделить выбранную ячейку, и нажмите OK. Примечание: опции раздела Строки и столбцы также доступны из контекстного меню. Раздел Размер ячейки используется для изменения ширины и высоты выделенной ячейки. В этом разделе можно также Выровнять высоту строк, чтобы все выделенные ячейки имели одинаковую высоту, или Выровнять ширину столбцов, чтобы все выделенные ячейки имели одинаковую ширину. Опции Выровнять высоту строк / ширину столбцов также доступны из контекстного меню. Изменение дополнительных параметров таблицы Чтобы изменить дополнительные параметры таблицы, щелкните по таблице правой кнопкой мыши и выберите из контекстного меню опцию Дополнительные параметры таблицы или нажмите ссылку Дополнительные параметры на правой боковой панели. Откроется окно свойств таблицы: На вкладке Положение можно задать следующие свойства: Размер - используйте эту опцию, чтобы изменить ширину и/или высоту таблицы. Если нажата кнопка Сохранять пропорции (в этом случае она выглядит так: ), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон таблицы. Позиция - задайте точную позицию, используя поля По горизонтали и По вертикали, а также поле От, где доступны параметры Верхний левый угол и По центру. Вкладка Поля позволяет задать расстояние между текстом внутри ячейки и границами ячейки: введите нужные значения Полей ячейки вручную или установите флажок Использовать поля по умолчанию, чтобы применить предустановленные значения (при необходимости их тоже можно изменить). Вкладка Альтернативный текст позволяет задать Заголовок и Описание, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит таблица. Для форматирования введенного текста внутри ячеек таблицы можно использовать значки на вкладке Главная верхней панели инструментов. Контекстное меню, вызываемое правым щелчком мыши по таблице, содержит две дополнительных опции: Вертикальное выравнивание в ячейках - позволяет задать предпочтительный тип вертикального выравнивания текста внутри выделенных ячеек: По верхнему краю, По центру, или По нижнему краю. Гиперссылка - позволяет вставить гиперссылку в выделенную ячейку."
    },
   {
        "id": "UsageInstructions/InsertText.htm", 
        "title": "Как вставить текст в презентацию PowerPoint - ONLYOFFICE", 
        "body": "Вставка и форматирование текста презентации Вставка текста Новый текст можно добавить тремя разными способами: Добавить фрагмент текста внутри соответствующей текстовой рамки, предусмотренной на макете слайда. Для этого установите курсор внутри текстовой рамки и напишите свой текст или вставьте его, используя сочетание клавиш Ctrl+V, вместо соответствующего текста по умолчанию. Добавить фрагмент текста в любом месте на слайде. Можно вставить надпись (прямоугольную рамку, внутри которой вводится текст) или объект Text Art (текстовое поле с предварительно заданным стилем и цветом шрифта, позволяющее применять текстовые эффекты). В зависимости от требуемого типа текстового объекта можно сделать следующее: чтобы добавить текстовое поле, щелкните по значку Надпись на вкладке Главная или Вставка верхней панели инструментов, выберите одну из следующих опций: Вставить горизонтальную надпись или Вставить вертикальную надпись, затем щелкните там, где требуется поместить надпись, удерживайте кнопку мыши и перетаскивайте границу текстового поля, чтобы задать его размер. Когда вы отпустите кнопку мыши, в добавленном текстовом поле появится курсор, и вы сможете ввести свой текст. Примечание: надпись можно также вставить, если щелкнуть по значку Фигура на верхней панели инструментов и выбрать фигуру из группы Основные фигуры. чтобы добавить объект Text Art, щелкните по значку Text Art на вкладке Вставка верхней панели инструментов, затем щелкните по нужному шаблону стиля – объект Text Art будет добавлен в центре слайда. Выделите мышью стандартный текст внутри текстового поля и напишите вместо него свой текст. Добавить фрагмент текста внутри автофигуры. Выделите фигуру и начинайте печатать текст. Щелкните за пределами текстового объекта, чтобы применить изменения и вернуться к слайду. Текст внутри текстового объекта является его частью (при перемещении или повороте текстового объекта текст будет перемещаться или поворачиваться вместе с ним). Поскольку вставленный текстовый объект представляет собой прямоугольную рамку (у нее по умолчанию невидимые границы) с текстом внутри, а эта рамка является обычной автофигурой, можно изменять свойства и фигуры, и текста. Чтобы удалить добавленный текстовый объект, щелкните по краю текстового поля и нажмите клавишу Delete на клавиатуре. Текст внутри текстового поля тоже будет удален. Форматирование текстовых полей презентации Выделите текстовое поле, щелкнув по его границе, чтобы можно было изменить его свойства. Когда текстовое поле выделено, его границы отображаются как сплошные, а не пунктирные линии. чтобы изменить размер текстового поля, переместить, повернуть его, используйте специальные маркеры по краям фигуры. чтобы изменить заливку, контур текстового поля, заменить прямоугольное поле на какую-то другую фигуру или открыть дополнительные параметры фигуры, щелкните по значку Параметры фигуры на правой боковой панели и используйте соответствующие опции. чтобы выровнять текстовое поле на слайде, повернуть или отразить поле, расположить текстовые поля в определенном порядке относительно других объектов, щелкните правой кнопкой мыши по границе текстового поля и используйте опции контекстного меню. чтобы создать колонки текста внутри текстового поля, щелкните правой кнопкой мыши по границе текстового поля, нажмите на пункт меню Дополнительные параметры фигуры и перейдите на вкладку Колонки в окне Фигура - дополнительные параметры. Форматирование текста внутри текстового поля Щелкните по тексту внутри текстового поля, чтобы можно было изменить его свойства. Когда текст выделен, границы текстового поля отображаются как пунктирные линии. Примечание: форматирование текста можно изменить и в том случае, если выделено текстовое поле, а не сам текст. В этом случае любые изменения будут применены ко всему тексту в текстовом поле. Некоторые параметры форматирования шрифта (тип, размер, цвет и стили оформления шрифта) можно отдельно применить к предварительно выделенному фрагменту текста. Выравнивание текста внутри текстового поля Горизонтально текст выравнивается четырьмя способами: по левому краю, по правому краю, по центру или по ширине. Для этого: установите курсор в том месте, где требуется применить выравнивание (это может быть новая строка или уже введенный текст), разверните список Горизонтальное выравнивание на вкладке Главная верхней панели инструментов, выберите тип выравнивания, который вы хотите применить: опция Выравнивание текста по левому краю позволяет выровнять текст по левому краю текстового поля (правый край остается невыровненным). опция Выравнивание текста по центру позволяет выровнять текст по центру текстового поля (правый и левый края остаются невыровненными). опция Выравнивание текста по правому краю позволяет выровнять текст по правому краю текстового поля (левый край остается невыровненным). опция Выравнивание текста по ширине позволяет выровнять текст как по левому, так и по правому краю текстового поля (выравнивание осуществляется за счет добавления дополнительных интервалов там, где это необходимо). Примечание: эти параметры также можно найти в окне Абзац - Дополнительные параметры. Вертикально текст выравнивается тремя способами: по верхнему краю, по середине или по нижнему краю. Для этого: установите курсор в том месте, где требуется применить выравнивание (это может быть новая строка или уже введенный текст), разверните список Вертикальное выравнивание на вкладке Главная верхней панели инструментов, выберите тип выравнивания, который вы хотите применить: опция Выравнивание текста по верхнему краю позволяет выровнять текст по верхнему краю текстового поля. опция Выравнивание текста по середине позволяет выровнять текст по центру текстового поля. опция Выравнивание текста по нижнему краю позволяет выровнять текст по нижнему краю текстового поля. Изменение направления текста Чтобы повернуть текст внутри текстового поля, щелкните по тексту правой кнопкой мыши, выберите опцию Направление текста, а затем выберите один из доступных вариантов: Горизонтальное (выбран по умолчанию), Повернуть текст вниз (задает вертикальное направление, сверху вниз) или Повернуть текст вверх (задает вертикальное направление, снизу вверх). Настройка типа, размера, цвета шрифта и применение стилей оформления Можно выбрать тип, размер и цвет шрифта, а также применить различные стили оформления шрифта, используя соответствующие значки, расположенные на вкладке Главная верхней панели инструментов. Примечание: если необходимо применить форматирование к тексту, который уже есть в презентации, выделите его мышью или с помощью клавиатуры, а затем примените форматирование. Также можно поместить курсор мыши в нужное слово, чтобы применить форматирование только к этому слову. Шрифт Используется для выбора шрифта из списка доступных. Если требуемый шрифт отсутствует в списке, его можно скачать и установить в вашей операционной системе, после чего он будет доступен для использования в десктопной версии. Размер шрифта Используется для выбора предустановленного значения размера шрифта из выпадающего списка (доступны следующие стандартные значения: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 и 96). Также можно вручную ввести произвольное значение до 300 пунктов в поле ввода и нажать клавишу Enter. Увеличить размер шрифта Используется для изменения размера шрифта, делая его на один пункт крупнее при каждом нажатии на кнопку. Уменьшить размер шрифта Используется для изменения размера шрифта, делая его на один пункт мельче при каждом нажатии на кнопку. Изменить регистр Используется для изменения регистра шрифта. Как в предложениях. - регистр совпадает с обычным предложением. нижнеий регистр - все буквы маленькие. ВЕРХНИЙ РЕГИСТР - все буквы прописные. Каждое Слово С Прописной - каждое слово начинается с заглавной буквы. иЗМЕНИТЬ рЕГИСТР - поменять регистр выделенного текста или слова, в котором находится курсор мыши. Цвет выделения Используется для выделения отдельных предложений, фраз, слов или даже символов путем добавления цветовой полосы, имитирующей отчеркивание текста маркером. Можно выделить нужную часть текста, а потом нажать направленную вниз стрелку рядом с этим значком, чтобы выбрать цвет на палитре (этот набор цветов не зависит от выбранной Цветовой схемы и включает в себя 16 цветов), и этот цвет будет применен к выбранному тексту. Или же можно сначала выбрать цвет выделения, а потом начать выделять текст мышью - указатель мыши будет выглядеть так: - и появится возможность выделить несколько разных частей текста одну за другой. Чтобы остановить выделение текста, просто еще раз щелкните по значку. Для очистки цвета выделения воспользуйтесь опцией Без заливки. Цвет шрифта Используется для изменения цвета букв/символов в тексте. Для выбора цвета нажмите направленную вниз стрелку рядом со значком. Полужирный Используется для придания шрифту большей насыщенности. Курсив Используется для придания шрифту наклона вправо. Подчеркнутый Используется для подчеркивания текста чертой, проведенной под буквами. Зачеркнутый Используется для зачеркивания текста чертой, проведенной по буквам. Надстрочные знаки Используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях. Подстрочные знаки Используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах. Задание междустрочного интервала и изменение отступов абзацев Можно задать высоту строки для строк текста в абзаце, а также поля между текущим и предыдущим или последующим абзацем. Для этого: установите курсор в пределах нужного абзаца, или выделите мышью несколько абзацев, используйте соответствующие поля вкладки Параметры текста на правой боковой панели, чтобы добиться нужного результата: Междустрочный интервал - задайте высоту строки для строк текста в абзаце. Вы можете выбрать одну из двух опций: множитель (устанавливает междустрочный интервал, который может быть выражен в числах больше 1), точно (устанавливает фиксированный междустрочный интервал). Необходимое значение можно указать в поле справа. Интервал между абзацами - задайте величину свободного пространства между абзацами. Перед - задайте величину свободного пространства перед абзацем. После - задайте величину свободного пространства после абзаца. Примечание: эти параметры также можно найти в окне Абзац - Дополнительные параметры. Чтобы быстро изменить междустрочный интервал в текущем абзаце, можно также использовать значок Междустрочный интервал на вкладке Главная верхней панели инструментов, выбрав нужное значение из списка: 1.0, 1.15, 1.5, 2.0, 2.5, или 3.0 строки. Чтобы изменить смещение абзаца от левого края текстового поля, установите курсор в пределах нужного абзаца или выделите мышью несколько абзацев и используйте соответствующие значки на вкладке Главная верхней панели инструментов: Уменьшить отступ и Увеличить отступ . Изменение дополнительных параметров абзаца Чтобы открыть окно Абзац - Дополнительные параметры, щелкните по тексту правой кнопкой мыши и выберите в контекстном меню пункт Дополнительные параметры текста. Также можно установить курсор в пределах нужного абзаца - на правой боковой панели будет активирована вкладка Параметры текста. Нажмите на ссылку Дополнительные параметры. Откроется окно свойств абзаца: На вкладке Отступы и интервалы можно выполнить следующие действия: изменить тип выравнивания текста внутри абзаца, изменить отступы абзаца от внутренних полей текстового объекта, Слева - задайте смещение всего абзаца от левого внутреннего поля текстового блока, указав нужное числовое значение, Справа - задайте смещение всего абзаца от правого внутреннего поля текстового блока, указав нужное числовое значение, Первая строка - задайте отступ для первой строки абзаца, выбрав соответствующий пункт меню ((нет), Отступ, Выступ) и изменив числовое значение для Отступа или Выступа, заданное по умолчанию, изменить междустрочный интервал внутри абзаца. Чтобы задать отступы, можно также использовать горизонтальную линейку. Выделите нужный абзац или абзацы и перетащите маркеры отступов по линейке. Маркер отступа первой строки используется, чтобы задать смещение от левого внутреннего поля текстового объекта для первой строки абзаца. Маркер выступа используется, чтобы задать смещение от левого внутреннего поля текстового объекта для второй и всех последующих строк абзаца. Маркер отступа слева используется, чтобы задать смещение от левого внутреннего поля текстового объекта для всего абзаца. Маркер отступа справа используется, чтобы задать смещение абзаца от правого внутреннего поля текстового объекта. Примечание: если вы не видите линеек, перейдите на вкладку Главная верхней панели инструментов, щелкните по значку Параметры представления в правом верхнем углу и снимите отметку с опции Скрыть линейки, чтобы отобразить их. Вкладка Шрифт содержит следующие параметры: Зачёркивание - используется для зачеркивания текста чертой, проведенной по буквам. Двойное зачёркивание - используется для зачеркивания текста двойной чертой, проведенной по буквам. Надстрочные - используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях. Подстрочные - используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах. Малые прописные - используется, чтобы сделать все буквы строчными. Все прописные - используется, чтобы сделать все буквы прописными. Межзнаковый интервал - используется, чтобы задать расстояние между символами. Увеличьте значение, заданное по умолчанию, чтобы применить Разреженный интервал, или уменьшите значение, заданное по умолчанию, чтобы применить Уплотненный интервал. Используйте кнопки со стрелками или введите нужное значение в поле ввода. Все изменения будут отображены в расположенном ниже поле предварительного просмотра. На вкладке Табуляция можно изменить позиции табуляции, то есть те позиции, куда переходит курсор при нажатии клавиши Tab на клавиатуре. Позиция табуляции По умолчанию имеет значение 2.54 см. Это значение можно уменьшить или увеличить, используя кнопки со стрелками или введя в поле нужное значение. Позиция - используется, чтобы задать пользовательские позиции табуляции. Введите в этом поле нужное значение, настройте его более точно, используя кнопки со стрелками, и нажмите на кнопку Задать. Пользовательская позиция табуляции будет добавлена в список в расположенном ниже поле. Выравнивание - используется, чтобы задать нужный тип выравнивания для каждой из позиций табуляции в расположенном выше списке. Выделите нужную позицию табуляции в списке, выберите в выпадающем списке Выравнивание опцию По левому краю, По центру или По правому краю и нажмите на кнопку Задать. По левому краю - выравнивает текст по левому краю относительно позиции табуляции; при наборе текст движется вправо от позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером . По центру - центрирует текст относительно позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером . По правому краю - выравнивает текст по правому краю относительно позиции табуляции; при наборе текст движется влево от позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером . Для удаления позиций табуляции из списка выделите позицию табуляции и нажмите кнопку Удалить или Удалить все. Для установки позиций табуляции можно также использовать горизонтальную линейку: Выберите нужный тип позиции табуляции, нажав на кнопку в левом верхнем углу рабочей области: По левому краю , По центру , По правому краю . Щелкните по нижнему краю линейки в том месте, где требуется установить позицию табуляции. Для изменения местоположения позиции табуляции перетащите ее по линейке. Для удаления добавленной позиции табуляции перетащите ее за пределы линейки. Примечание: если вы не видите линеек, перейдите на вкладку Главная верхней панели инструментов, щелкните по значку Параметры представления в правом верхнем углу и снимите отметку с опции Скрыть линейки, чтобы отобразить их. Изменение стиля объекта Text Art Выделите текстовый объект и щелкните по значку Параметры объектов Text Art на правой боковой панели. Измените примененный стиль текста, выбрав из галереи новый Шаблон. Можно также дополнительно изменить этот базовый стиль, выбрав другой тип, размер шрифта и т.д. Измените заливку и контур шрифта. Доступны точно такие же опции, как и для автофигур. Примените текстовый эффект, выбрав нужный тип трансформации текста из галереи Трансформация. Можно скорректировать степень искривления текста, перетаскивая розовый маркер в форме ромба."
    },
   {
        "id": "UsageInstructions/ManageSlides.htm", 
        "title": "Управление слайдами", 
        "body": "По умолчанию новая созданная презентация содержит один пустой титульный слайд. Вы можете создавать новые слайды, копировать слайды, чтобы затем можно было вставить их в другое место в списке слайдов, дублировать слайды, перемещать слайды, чтобы изменить их порядок в списке, удалять ненужные слайды, помечать отдельные слайды как скрытые. Для создания нового слайда с макетом Заголовок и объект: щелкните по значку Добавить слайд на вкладке Главная или Вставка верхней панели инструментов или щелкните правой кнопкой мыши по любому слайду в списке и выберите в контекстном меню пункт Новый слайд или нажмите сочетание клавиш Ctrl+M. Для создания нового слайда с другим макетом: нажмите на стрелку рядом со значком Добавить слайд на вкладке Главная или Вставка верхней панели инструментов, выберите в меню слайд с нужным макетом. Примечание: вы в любое время сможете изменить макет добавленного слайда. Чтобы получить дополнительную информацию о том, как это сделать, обратитесь к разделу Настройка параметров слайда. Новый слайд будет вставлен после выделенного слайда в списке существующих слайдов слева. Для дублирования слайда: щелкните правой кнопкой мыши по нужному слайду в списке существующих слайдов слева, щелкните правой кнопкой мыши и в контекстном меню выберите пункт Дублировать слайд, либо на вкладках Главная или Вставка нажмите кнопку Добавить слайд и выберите пункт Дублировать слайд. Дублированный слайд будет вставлен после выделенного слайда в списке слайдов. Для копирования слайда: в списке существующих слайдов слева выделите слайд, который требуется скопировать, нажмите сочетание клавиш Ctrl+C, выделите в списке слайд, после которого требуется вставить скопированный, нажмите сочетание клавиш Ctrl+V. После вставки скопированного слайда рядом со вставленным слайдом появляется кнопка Специальная вставка. Нажмите на эту кнопку, чтобы выбрать нужный параметр вставки, или используйте клавишу Ctrl в сочетании с буквой, указанной в скобках рядом с необходимым параметром. Доступны следующие параметры Специальной вставки: Использовать конечную тему (Ctrl+H) - позволяет применить форматирование, определенное темой текущей презентации. Эта опция используется по умолчанию. Сохранить исходное форматирование (Ctrl+K) - позволяет сохранить исходное форматирование скопированного слайда. Изображение (Ctrl+U) - позволяет вставить слайд как изображение, чтобы его нельзя было редактировать. Для перемещения существующего слайда: щелкните левой кнопкой мыши по нужному слайду в списке существующих слайдов слева, не отпуская кнопку мыши, перетащите его на нужное место в списке (горизонтальная линия обозначает новую позицию). Для удаления ненужного слайда: в списке существующих слайдов слева щелкните правой кнопкой мыши по слайду, который требуется удалить, в контекстном меню выберите пункт Удалить слайд. Для скрытия слайда: в списке существующих слайдов слева щелкните правой кнопкой мыши по слайду, который требуется скрыть, в контекстном меню выберите пункт Скрыть слайд. Номер, соответствующий скрытому слайду, будет перечеркнут в списке слайдов слева. Чтобы отображать скрытый слайд как обычный слайд в списке, щелкните по опции Скрыть слайд еще раз. Примечание: эту опцию можно использовать, если вы не хотите демонстрировать зрителям некоторые слайды, но вам требуется, чтобы в случае необходимости к этим слайдам можно было получить доступ. При запуске показа слайдов в режиме докладчика все имеющиеся слайды отображаются в списке слева, при этом номера скрытых слайдов перечеркнуты. Если вы захотите показать зрителям слайд, помеченный как скрытый, просто щелкните по нему в списке слайдов слева - слайд будет отображен. Для выделения всех существующих слайдов сразу: щелкните правой кнопкой мыши по любому слайду в списке существующих слайдов слева, в контекстном меню выберите пункт Выделить все. Для выделения нескольких слайдов: удерживайте клавишу Ctrl, выделите нужные слайды в списке существующих слайдов слева, щелкая по ним левой кнопкой мыши. Примечание: все сочетания клавиш, которые можно использовать для управления слайдами, перечислены на странице Сочетания клавиш."
    },
   {
        "id": "UsageInstructions/ManipulateObjects.htm", 
        "title": "Работа с объектами на слайде", 
        "body": "Можно изменять размер различных объектов, перемещать и поворачивать их на слайде вручную при помощи специальных маркеров. Можно также точно задать размеры некоторых объектов и их положение с помощью правой боковой панели или окна Дополнительные параметры. Примечание: список сочетаний клавиш, которые можно использовать при работе с объектами, доступен здесь. Изменение размера объектов Для изменения размера автофигуры/изображения/диаграммы/таблицы/текстового поля перетаскивайте маленькие квадраты , расположенные по краям объекта. Чтобы сохранить исходные пропорции выбранного объекта при изменении размера, удерживайте клавишу Shift и перетаскивайте один из угловых значков. Чтобы задать точную ширину и высоту диаграммы, выделите ее на слайде и используйте раздел Размер на правой боковой панели, которая будет активирована. Чтобы задать точные размеры изображения или автофигуры, щелкните правой кнопкой мыши по нужному объекту на слайде и выберите пункт меню Дополнительные параметры изображения/фигуры. Укажите нужные значения на вкладке Размер окна Дополнительные параметры и нажмите кнопку OK. Изменение формы автофигур При изменении некоторых фигур, например, фигурных стрелок или выносок, также доступен желтый значок в форме ромба . Он позволяет изменять отдельные параметры формы, например, длину указателя стрелки. Чтобы изменить форму автофигуры, вы также можете использовать опцию Изменить точки в контекстном меню. Изменить точки используется для редактирования формы или изменения кривизны автофигуры. Чтобы активировать редактируемые опорные точки фигуры, щелкните по фигуре правой кнопкой мыши и в контекстном меню выберите пункт Изменить точки. Черные квадраты, которые становятся активными, — это точки, где встречаются две линии, а красная линия очерчивает фигуру. Щелкните и перетащите квадрат, чтобы изменить положение точки и изменить контур фигуры. После сдвига опорной точки фигуры, появятся две синие линии с белыми квадратами на концах. Это кривые Безье, которые позволяют создавать кривую и изменять ее значение. Пока опорные точки активны, вы можете добавлять и удалять их: Чтобы добавить точку к фигуре, удерживайте Ctrl и щелкните место, где вы хотите добавить опорную точку. Чтобы удалить точку, удерживайте Ctrl и щелкните по ненужной точке. Перемещение объектов Для изменения местоположения автофигуры/изображения/диаграммы/таблицы/текстового поля используйте значок , который появляется после наведения курсора мыши на объект. Перетащите объект на нужное место, не отпуская кнопку мыши. Чтобы перемещать объект с шагом в один пиксель, удерживайте клавишу Ctrl и используйте стрелки на клавиатуре. Чтобы перемещать объект строго по горизонтали/вертикали и предотвратить его смещение в перпендикулярном направлении, при перетаскивании удерживайте клавишу Shift. Чтобы задать точное положение изображения, щелкните правой кнопкой мыши по изображению на слайде и выберите пункт меню Дополнительные параметры изображения. Укажите нужные значения в разделе Положение окна Дополнительные параметры и нажмите кнопку OK. Поворот объектов Чтобы вручную повернуть автофигуру/изображение/текстовое поле, наведите курсор мыши на маркер поворота и перетащите его по часовой стрелке или против часовой стрелки. Чтобы ограничить угол поворота шагом в 15 градусов, при поворачивании удерживайте клавишу Shift. Чтобы повернуть объект на 90 градусов против часовой стрелки или по часовой стрелке или отразить объект по горизонтали или по вертикали, можно использовать раздел Поворот на правой боковой панели, которая будет активирована, как только вы выделите нужный объект. Чтобы открыть ее, нажмите на значок Параметры фигуры или Параметры изображения справа. Нажмите на одну из кнопок: чтобы повернуть объект на 90 градусов против часовой стрелки чтобы повернуть объект на 90 градусов по часовой стрелке чтобы отразить объект по горизонтали (слева направо) чтобы отразить объект по вертикали (сверху вниз) Также можно щелкнуть правой кнопкой мыши по объекту, выбрать из контекстного меню пункт Поворот, а затем использовать один из доступных вариантов поворота объекта. Чтобы повернуть объект на точно заданный угол, нажмите на ссылку Дополнительные параметры на правой боковой панели и используйте вкладку Поворот в окне Дополнительные параметры. Укажите нужное значение в градусах в поле Угол и нажмите кнопку OK."
    },
   {
        "id": "UsageInstructions/MathAutoCorrect.htm", 
        "title": "Функции автозамены", 
        "body": "Функции автозамены используются для автоматического форматирования текста при обнаружении или вставки специальных математических символов путем распознавания определенных символов. Доступные параметры автозамены перечислены в соответствующем диалоговом окне. Чтобы его открыть, перейдите на вкладку Файл -> Дополнительные параметры -> Правописание -> Параметры автозамены. Диалоговое окно Автозамена содержит четыре вкладки: Автозамена математическими символами, Распознанные функции, Автоформат при вводе и Автозамена. Автозамена математическими символами При работе с уравнениями множество символов, диакритических знаков и знаков математических действий можно добавить путем ввода с клавиатуры, а не выбирая шаблон из коллекции. В редакторе уравнений установите курсор в нужном поле для ввода, введите специальный код и нажмите Пробел. Введенный код будет преобразован в соответствующий символ, а пробел будет удален. Примечание: коды чувствительны к регистру. Вы можете добавлять, изменять, восстанавливать и удалять записи автозамены из списка автозамены. Перейдите во вкладку Файл -> Дополнительные параметры -> Правописание -> Параметры автозамены -> Автозамена математическими символами. Чтобы добавить запись в список автозамены, Введите код автозамены, который хотите использовать, в поле Заменить. Введите символ, который будет присвоен введенному вами коду, в поле На. Щелкните на кнопку Добавить. Чтобы изменить запись в списке автозамены, Выберите запись, которую хотите изменить. Вы можете изменить информацию в полях Заменить для кода и На для символа. Щелкните на кнопку Добавить. Чтобы удалить запись из списка автозамены, Выберите запись, которую хотите удалить. Щелкните на кнопку Удалить. Чтобы восстановить ранее удаленные записи, выберите из списка запись, которую нужно восстановить, и нажмите кнопку Восстановить. Чтобы восстановить настройки по умолчанию, нажмите кнопку Сбросить настройки. Любая добавленная вами запись автозамены будет удалена, а измененные значения будут восстановлены до их исходных значений. Чтобы отключить автозамену математическими символами и избежать автоматических изменений и замен, снимите флажок Заменять текст при вводе. В таблице ниже приведены все поддерживаемые в настоящее время коды, доступные в Редакторе презентаций. Полный список поддерживаемых кодов также можно найти на вкладке Файл -> Дополнительыне параметры... -> Правописание -> Параметры автозамены -> Автозамена математическими символами. Поддерживаемые коды Код Символ Категория !! Символы ... Точки :: Операторы := Операторы /< Операторы отношения /> Операторы отношения /= Операторы отношения \\above Символы Above/Below \\acute Акценты \\aleph Буквы иврита \\alpha Греческие буквы \\Alpha Греческие буквы \\amalg Бинарные операторы \\angle Геометрические обозначения \\aoint Интегралы \\approx Операторы отношений \\asmash Стрелки \\ast Бинарные операторы \\asymp Операторы отношений \\atop Операторы \\bar Черта сверху/снизу \\Bar Акценты \\because Операторы отношений \\begin Разделители \\below Символы Above/Below \\bet Буквы иврита \\beta Греческие буквы \\Beta Греческие буквы \\beth Буквы иврита \\bigcap Крупные операторы \\bigcup Крупные операторы \\bigodot Крупные операторы \\bigoplus Крупные операторы \\bigotimes Крупные операторы \\bigsqcup Крупные операторы \\biguplus Крупные операторы \\bigvee Крупные операторы \\bigwedge Крупные операторы \\binomial Уравнения \\bot Логические обозначения \\bowtie Операторы отношений \\box Символы \\boxdot Бинарные операторы \\boxminus Бинарные операторы \\boxplus Бинарные операторы \\bra Разделители \\break Символы \\breve Акценты \\bullet Бинарные операторы \\cap Бинарные операторы \\cbrt Квадратные корни и радикалы \\cases Символы \\cdot Бинарные операторы \\cdots Точки \\check Акценты \\chi Греческие буквы \\Chi Греческие буквы \\circ Бинарные операторы \\close Разделители \\clubsuit Символы \\coint Интегралы \\cong Операторы отношений \\coprod Математические операторы \\cup Бинарные операторы \\dalet Буквы иврита \\daleth Буквы иврита \\dashv Операторы отношений \\dd Дважды начерченные буквы \\Dd Дважды начерченные буквы \\ddddot Акценты \\dddot Акценты \\ddot Акценты \\ddots Точки \\defeq Операторы отношений \\degc Символы \\degf Символы \\degree Символы \\delta Греческие буквы \\Delta Греческие буквы \\Deltaeq Операторы \\diamond Бинарные операторы \\diamondsuit Символы \\div Бинарные операторы \\dot Акценты \\doteq Операторы отношений \\dots Точки \\doublea Дважды начерченные буквы \\doubleA Дважды начерченные буквы \\doubleb Дважды начерченные буквы \\doubleB Дважды начерченные буквы \\doublec Дважды начерченные буквы \\doubleC Дважды начерченные буквы \\doubled Дважды начерченные буквы \\doubleD Дважды начерченные буквы \\doublee Дважды начерченные буквы \\doubleE Дважды начерченные буквы \\doublef Дважды начерченные буквы \\doubleF Дважды начерченные буквы \\doubleg Дважды начерченные буквы \\doubleG Дважды начерченные буквы \\doubleh Дважды начерченные буквы \\doubleH Дважды начерченные буквы \\doublei Дважды начерченные буквы \\doubleI Дважды начерченные буквы \\doublej Дважды начерченные буквы \\doubleJ Дважды начерченные буквы \\doublek Дважды начерченные буквы \\doubleK Дважды начерченные буквы \\doublel Дважды начерченные буквы \\doubleL Дважды начерченные буквы \\doublem Дважды начерченные буквы \\doubleM Дважды начерченные буквы \\doublen Дважды начерченные буквы \\doubleN Дважды начерченные буквы \\doubleo Дважды начерченные буквы \\doubleO Дважды начерченные буквы \\doublep Дважды начерченные буквы \\doubleP Дважды начерченные буквы \\doubleq Дважды начерченные буквы \\doubleQ Дважды начерченные буквы \\doubler Дважды начерченные буквы \\doubleR Дважды начерченные буквы \\doubles Дважды начерченные буквы \\doubleS Дважды начерченные буквы \\doublet Дважды начерченные буквы \\doubleT Дважды начерченные буквы \\doubleu Дважды начерченные буквы \\doubleU Дважды начерченные буквы \\doublev Дважды начерченные буквы \\doubleV Дважды начерченные буквы \\doublew Дважды начерченные буквы \\doubleW Дважды начерченные буквы \\doublex Дважды начерченные буквы \\doubleX Дважды начерченные буквы \\doubley Дважды начерченные буквы \\doubleY Дважды начерченные буквы \\doublez Дважды начерченные буквы \\doubleZ Дважды начерченные буквы \\downarrow Стрелки \\Downarrow Стрелки \\dsmash Стрелки \\ee Дважды начерченные буквы \\ell Символы \\emptyset Обозначения множествs \\emsp Знаки пробела \\end Разделители \\ensp Знаки пробела \\epsilon Греческие буквы \\Epsilon Греческие буквы \\eqarray Символы \\equiv Операторы отношений \\eta Греческие буквы \\Eta Греческие буквы \\exists Логические обозначенияs \\forall Логические обозначенияs \\fraktura Буквы готического шрифта \\frakturA Буквы готического шрифта \\frakturb Буквы готического шрифта \\frakturB Буквы готического шрифта \\frakturc Буквы готического шрифта \\frakturC Буквы готического шрифта \\frakturd Буквы готического шрифта \\frakturD Буквы готического шрифта \\frakture Буквы готического шрифта \\frakturE Буквы готического шрифта \\frakturf Буквы готического шрифта \\frakturF Буквы готического шрифта \\frakturg Буквы готического шрифта \\frakturG Буквы готического шрифта \\frakturh Буквы готического шрифта \\frakturH Буквы готического шрифта \\frakturi Буквы готического шрифта \\frakturI Буквы готического шрифта \\frakturk Буквы готического шрифта \\frakturK Буквы готического шрифта \\frakturl Буквы готического шрифта \\frakturL Буквы готического шрифта \\frakturm Буквы готического шрифта \\frakturM Буквы готического шрифта \\frakturn Буквы готического шрифта \\frakturN Буквы готического шрифта \\frakturo Буквы готического шрифта \\frakturO Буквы готического шрифта \\frakturp Буквы готического шрифта \\frakturP Буквы готического шрифта \\frakturq Буквы готического шрифта \\frakturQ Буквы готического шрифта \\frakturr Буквы готического шрифта \\frakturR Буквы готического шрифта \\frakturs Буквы готического шрифта \\frakturS Буквы готического шрифта \\frakturt Буквы готического шрифта \\frakturT Буквы готического шрифта \\frakturu Буквы готического шрифта \\frakturU Буквы готического шрифта \\frakturv Буквы готического шрифта \\frakturV Буквы готического шрифта \\frakturw Буквы готического шрифта \\frakturW Буквы готического шрифта \\frakturx Буквы готического шрифта \\frakturX Буквы готического шрифта \\fraktury Буквы готического шрифта \\frakturY Буквы готического шрифта \\frakturz Буквы готического шрифта \\frakturZ Буквы готического шрифта \\frown Операторы отношений \\funcapply Бинарные операторы \\G Греческие буквы \\gamma Греческие буквы \\Gamma Греческие буквы \\ge Операторы отношений \\geq Операторы отношений \\gets Стрелки \\gg Операторы отношений \\gimel Буквы иврита \\grave Акценты \\hairsp Знаки пробела \\hat Акценты \\hbar Символы \\heartsuit Символы \\hookleftarrow Стрелки \\hookrightarrow Стрелки \\hphantom Стрелки \\hsmash Стрелки \\hvec Акценты \\identitymatrix Матрицы \\ii Дважды начерченные буквы \\iiint Интегралы \\iint Интегралы \\iiiint Интегралы \\Im Символы \\imath Символы \\in Операторы отношений \\inc Символы \\infty Символы \\int Интегралы \\integral Интегралы \\iota Греческие буквы \\Iota Греческие буквы \\itimes Математические операторы \\j Символы \\jj Дважды начерченные буквы \\jmath Символы \\kappa Греческие буквы \\Kappa Греческие буквы \\ket Разделители \\lambda Греческие буквы \\Lambda Греческие буквы \\langle Разделители \\lbbrack Разделители \\lbrace Разделители \\lbrack Разделители \\lceil Разделители \\ldiv Дробная черта \\ldivide Дробная черта \\ldots Точки \\le Операторы отношений \\left Разделители \\leftarrow Стрелки \\Leftarrow Стрелки \\leftharpoondown Стрелки \\leftharpoonup Стрелки \\leftrightarrow Стрелки \\Leftrightarrow Стрелки \\leq Операторы отношений \\lfloor Разделители \\lhvec Акценты \\limit Лимиты \\ll Операторы отношений \\lmoust Разделители \\Longleftarrow Стрелки \\Longleftrightarrow Стрелки \\Longrightarrow Стрелки \\lrhar Стрелки \\lvec Акценты \\mapsto Стрелки \\matrix Матрицы \\medsp Знаки пробела \\mid Операторы отношений \\middle Символы \\models Операторы отношений \\mp Бинарные операторы \\mu Греческие буквы \\Mu Греческие буквы \\nabla Символы \\naryand Операторы \\nbsp Знаки пробела \\ne Операторы отношений \\nearrow Стрелки \\neq Операторы отношений \\ni Операторы отношений \\norm Разделители \\notcontain Операторы отношений \\notelement Операторы отношений \\notin Операторы отношений \\nu Греческие буквы \\Nu Греческие буквы \\nwarrow Стрелки \\o Греческие буквы \\O Греческие буквы \\odot Бинарные операторы \\of Операторы \\oiiint Интегралы \\oiint Интегралы \\oint Интегралы \\omega Греческие буквы \\Omega Греческие буквы \\ominus Бинарные операторы \\open Разделители \\oplus Бинарные операторы \\otimes Бинарные операторы \\over Разделители \\overbar Акценты \\overbrace Акценты \\overbracket Акценты \\overline Акценты \\overparen Акценты \\overshell Акценты \\parallel Геометрические обозначения \\partial Символы \\pmatrix Матрицы \\perp Геометрические обозначения \\phantom Символы \\phi Греческие буквы \\Phi Греческие буквы \\pi Греческие буквы \\Pi Греческие буквы \\pm Бинарные операторы \\pppprime Штрихи \\ppprime Штрихи \\pprime Штрихи \\prec Операторы отношений \\preceq Операторы отношений \\prime Штрихи \\prod Математические операторы \\propto Операторы отношений \\psi Греческие буквы \\Psi Греческие буквы \\qdrt Квадратные корни и радикалы \\quadratic Квадратные корни и радикалы \\rangle Разделители \\Rangle Разделители \\ratio Операторы отношений \\rbrace Разделители \\rbrack Разделители \\Rbrack Разделители \\rceil Разделители \\rddots Точки \\Re Символы \\rect Символы \\rfloor Разделители \\rho Греческие буквы \\Rho Греческие буквы \\rhvec Акценты \\right Разделители \\rightarrow Стрелки \\Rightarrow Стрелки \\rightharpoondown Стрелки \\rightharpoonup Стрелки \\rmoust Разделители \\root Символы \\scripta Буквы рукописного шрифта \\scriptA Буквы рукописного шрифта \\scriptb Буквы рукописного шрифта \\scriptB Буквы рукописного шрифта \\scriptc Буквы рукописного шрифта \\scriptC Буквы рукописного шрифта \\scriptd Буквы рукописного шрифта \\scriptD Буквы рукописного шрифта \\scripte Буквы рукописного шрифта \\scriptE Буквы рукописного шрифта \\scriptf Буквы рукописного шрифта \\scriptF Буквы рукописного шрифта \\scriptg Буквы рукописного шрифта \\scriptG Буквы рукописного шрифта \\scripth Буквы рукописного шрифта \\scriptH Буквы рукописного шрифта \\scripti Буквы рукописного шрифта \\scriptI Буквы рукописного шрифта \\scriptk Буквы рукописного шрифта \\scriptK Буквы рукописного шрифта \\scriptl Буквы рукописного шрифта \\scriptL Буквы рукописного шрифта \\scriptm Буквы рукописного шрифта \\scriptM Буквы рукописного шрифта \\scriptn Буквы рукописного шрифта \\scriptN Буквы рукописного шрифта \\scripto Буквы рукописного шрифта \\scriptO Буквы рукописного шрифта \\scriptp Буквы рукописного шрифта \\scriptP Буквы рукописного шрифта \\scriptq Буквы рукописного шрифта \\scriptQ Буквы рукописного шрифта \\scriptr Буквы рукописного шрифта \\scriptR Буквы рукописного шрифта \\scripts Буквы рукописного шрифта \\scriptS Буквы рукописного шрифта \\scriptt Буквы рукописного шрифта \\scriptT Буквы рукописного шрифта \\scriptu Буквы рукописного шрифта \\scriptU Буквы рукописного шрифта \\scriptv Буквы рукописного шрифта \\scriptV Буквы рукописного шрифта \\scriptw Буквы рукописного шрифта \\scriptW Буквы рукописного шрифта \\scriptx Буквы рукописного шрифта \\scriptX Буквы рукописного шрифта \\scripty Буквы рукописного шрифта \\scriptY Буквы рукописного шрифта \\scriptz Буквы рукописного шрифта \\scriptZ Буквы рукописного шрифта \\sdiv Дробная черта \\sdivide Дробная черта \\searrow Стрелки \\setminus Бинарные операторы \\sigma Греческие буквы \\Sigma Греческие буквы \\sim Операторы отношений \\simeq Операторы отношений \\smash Стрелки \\smile Операторы отношений \\spadesuit Символы \\sqcap Бинарные операторы \\sqcup Бинарные операторы \\sqrt Квадратные корни и радикалы \\sqsubseteq Обозначения множеств \\sqsuperseteq Обозначения множеств \\star Бинарные операторы \\subset Обозначения множеств \\subseteq Обозначения множеств \\succ Операторы отношений \\succeq Операторы отношений \\sum Математические операторы \\superset Обозначения множеств \\superseteq Обозначения множеств \\swarrow Стрелки \\tau Греческие буквы \\Tau Греческие буквы \\therefore Операторы отношений \\theta Греческие буквы \\Theta Греческие буквы \\thicksp Знаки пробела \\thinsp Знаки пробела \\tilde Акценты \\times Бинарные операторы \\to Стрелки \\top Логические обозначения \\tvec Стрелки \\ubar Акценты \\Ubar Акценты \\underbar Акценты \\underbrace Акценты \\underbracket Акценты \\underline Акценты \\underparen Акценты \\uparrow Стрелки \\Uparrow Стрелки \\updownarrow Стрелки \\Updownarrow Стрелки \\uplus Бинарные операторы \\upsilon Греческие буквы \\Upsilon Греческие буквы \\varepsilon Греческие буквы \\varphi Греческие буквы \\varpi Греческие буквы \\varrho Греческие буквы \\varsigma Греческие буквы \\vartheta Греческие буквы \\vbar Разделители \\vdash Операторы отношений \\vdots Точки \\vec Акценты \\vee Бинарные операторы \\vert Разделители \\Vert Разделители \\Vmatrix Матрицы \\vphantom Стрелки \\vthicksp Знаки пробела \\wedge Бинарные операторы \\wp Символы \\wr Бинарные операторы \\xi Греческие буквы \\Xi Греческие буквы \\zeta Греческие буквы \\Zeta Греческие буквы \\zwnj Знаки пробела \\zwsp Знаки пробела ~= Операторы отношений -+ Бинарные операторы +- Бинарные операторы << Операторы отношений <= Операторы отношений -> Стрелки >= Операторы отношений >> Операторы отношений Распознанные функции На этой вкладке вы найдете список математических выражений, которые будут распознаваться редактором формул как функции и поэтому не будут автоматически выделены курсивом. Чтобы просмотреть список распознанных функций, перейдите на вкладку Файл -> Дополнительные параметры -> Правописание -> Параметры автозамены -> Распознанные функции. Чтобы добавить запись в список распознаваемых функций, введите функцию в пустое поле и нажмите кнопку Добавить. Чтобы удалить запись из списка распознанных функций, выберите функцию, которую нужно удалить, и нажмите кнопку Удалить. Чтобы восстановить ранее удаленные записи, выберите в списке запись, которую нужно восстановить, и нажмите кнопку Восстановить. Чтобы восстановить настройки по умолчанию, нажмите кнопку Сбросить настройки. Любая добавленная вами функция будет удалена, а удаленные - восстановлены. Автоформат при вводе По умолчанию, редактор применяет форматирование во время набора текста в соответствии с предустановками автоматического форматирования: заменяет кавычки, автоматически запускает маркированный список или нумерованный список при обнаружении списка и заменяет кавычки или преобразует дефисы в тире. Опция Добавлять точку двойным пробелом по умолчанию отключена. Включите данную опцию, если хотите, чтобы точка автоматически добавлялась при двойном нажатии клавиши пробела. Чтобы включить или отключить предустановки автоматического форматирования, перейдите на вкладку Файл -> Дополнительные параметры -> Проверка правописания -> Параметры автозамены -> Автоформат при вводе. Автозамена текста Вы можете настроить редактор на автоматическое использование заглавной буквы в каждом предложении. Данная опция включена по умолчанию. Чтобы отключить эту функцию, перейдите на вкладку Файл -> Дополнительные параметры -> Правописание -> Параметры автозамены -> Автозамена текста и снимите флажок с Делать первые буквы предложений прописными."
    },
   {
        "id": "UsageInstructions/MotionPath.htm", 
        "title": "Создание анимации пути перемещения", 
        "body": "Пути перемещения входят в состав галереи эффектов анимации, они определяют движение объекта и путь, по которому он следует. Значки в галерее обозначают предлагаемый путь. Галерея анимаций доступна на вкладке Анимация верхней панели инструментов. Применение эффекта анимации пути перемещения перейдите на вкладку Анимация верхней панели инструментов, выберите текст, объект или графический элемент, к которому вы хотите применить эффект анимации, выберите один из готовых шаблонов пути перемещения в разделе Пути перемещения галереи анимаций (Линии, Дуги и другие) или выберите опцию Пользовательский путь, если вы хотите создать собственный путь. Вы можете предварительно просмотреть эффекты анимации на текущем слайде. По умолчанию, когда вы добавляете анимационные эффекты на слайд, они воспроизводятся автоматически, но вы можете отключить эту функцию. Откройте раскрывающийся список Просмотр на вкладке Анимация и выберите режим предварительного просмотра: Просмотр - чтобы включать предпросмотр по нажатию кнопки Просмотр, Автопросмотр - чтобы предпросмотр начинался автоматически при добавлении анимации к слайду. Добавление эффекта анимации пользовательского пути Чтобы нарисовать пользовательский путь, Щелкните по объекту, для которого требуется задать анимацию Пользовательский путь. Отметьте точки пути левой кнопкой мыши. При каждом клике левой кнопкой мыши рисуется линия, а удерживание левой кнопки мыши позволяет создать любую нужную кривую. Начальная точка пути будет обозначена зеленой стрелкой, а конечная точка - красной. Когда все будет готово, дважды щелкните левой кнопкой мыши или нажмите клавишу Esc, чтобы закончить рисование пути. Редактирование точек пути перемещения Чтобы отредактировать точки пути перемещения, выделите объект пути, щелкните правой кнопкой мыши, чтобы открыть контекстное меню, и выберите опцию Изменить точки. Перетаскивайте черные квадраты, чтобы скорректировать позицию узлов точек пути; перетаскивайте белые квадраты, чтобы изменить направление линии на входе и выходе из узла. Нажмите Esc или щелкните за пределами объекта пути, чтобы выйти из режима редактирования. Вы можете масштабировать путь перемещения, щелкнув по нему и перетаскивая квадратные маркеры по краям объекта."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Создание новой презентации или открытие существующей", 
        "body": "В редакторе презентаций вы можете открыть презентацию, которую вы недавно редактировали, переименовать её, создать новую или вернуться к списку существующих презентаций. Чтобы создать новую презентацию В онлайн-редакторе нажмите на вкладку Файл на верхней панели инструментов, выберите опцию Создать новую. В десктопном редакторе в главном окне программы выберите пункт меню Презентация в разделе Создать на левой боковой панели - новый файл откроется в новой вкладке, после внесения в презентацию необходимых изменений нажмите на значок Сохранить в левом верхнем углу или откройте вкладку Файл и выберите пункт меню Сохранить как. в окне проводника выберите местоположение файла на жестком диске, задайте название презентации, выберите формат сохранения (PPTX, Шаблон презентации (POTX), ODP, OTP, PDF или PDFA) и нажмите кнопку Сохранить. Чтобы открыть существующую презентацию В десктопном редакторе в главном окне программы выберите пункт меню Открыть локальный файл на левой боковой панели, выберите нужную презентацию в окне проводника и нажмите кнопку Открыть. Можно также щелкнуть правой кнопкой мыши по нужному файлу в окне проводника, выбрать опцию Открыть с помощью и затем выбрать в меню нужное приложение. Если файлы офисных документов ассоциированы с приложением, презентации также можно открывать двойным щелчком мыши по названию файла в окне проводника. Все каталоги, к которым вы получали доступ с помощью десктопного редактора, будут отображены в разделе Открыть локальный файл в списке Последние папки, чтобы в дальнейшем вы могли быстро их открыть. Щелкните по нужной папке, чтобы выбрать один из находящихся в ней файлов. Чтобы открыть недавно отредактированную презентацию В онлайн-редакторе нажмите на вкладку Файл на верхней панели инструментов, выберите опцию Открыть последние, выберите нужную презентацию из списка недавно отредактированных презентаций. В десктопном редакторе в главном окне программы выберите пункт меню Последние файлы на левой боковой панели, выберите нужную презентацию из списка недавно измененных документов. Чтобы переименовать открытую презентацию В онлайн-редакторе щелкните по имени презентации наверху страницы, введите новое имя презентации, нажмите Enter, чтобы принять изменения. Чтобы открыть папку, в которой сохранен файл, в новой вкладке браузера в онлайн-версии, в окне проводника в десктопной версии, нажмите на значок Открыть расположение файла в правой части шапки редактора. Можно также перейти на вкладку Файл на верхней панели инструментов и выбрать опцию Открыть расположение файла."
    },
   {
        "id": "UsageInstructions/PreviewPresentation.htm", 
        "title": "Просмотр презентации", 
        "body": "Запуск просмотра слайдов Примечание: Если вы загружаете презентацию с эффектами анимации, созданную в стороннем приложении, вы можете их предварительно просмотреть. Чтобы просмотреть презентацию, которую вы в данный момент редактируете, можно сделать следующее: щелкните по значку Начать показ слайдов на вкладке Главная верхней панели инструментов или в левой части строки состояния или выберите определенный слайд в списке слайдов слева, щелкните по нему правой кнопкой мыши и выберите в контекстном меню пункт Начать показ слайдов. Просмотр начнется с выделенного в данный момент слайда. Можно также нажать на стрелку рядом со значком Начать показ слайдов на вкладке Главная верхней панели инструментов и выбрать одну из доступных опций: Показ слайдов с начала - чтобы начать показ слайдов с самого первого слайда, Показ слайдов с текущего слайда - чтобы начать показ слайдов со слайда, выделенного в данный момент, Показ слайдов в режиме докладчика - чтобы начать показ слайдов в режиме докладчика, позволяющем демонстрировать презентацию зрителям, не показывая заметок к слайдам, и одновременно просматривать презентацию с заметками к слайдам на другом мониторе. Параметры показа слайдов - чтобы открыть окно настроек, позволяющее задать только один параметр: Непрерывный цикл до нажатия клавиши 'Esc'. Отметьте эту опцию в случае необходимости и нажмите кнопку OK. Если эта опция включена, презентация будет отображаться до тех пор, пока вы не нажмете клавишу Escape на клавиатуре, то есть, при достижении последнего слайда в презентации вы сможете снова перейти к первому слайду и так далее. Если эта опция отключена, то при достижении последнего слайда в презентации появится черный экран с информацией о том, что презентация завершена и вы можете выйти из режима просмотра. Использование режима просмотра В режиме просмотра можно использовать следующие элементы управления, расположенные в левом нижнем углу: кнопка Предыдущий слайд позволяет вернуться к предыдущему слайду. кнопка Приостановить презентацию позволяет приостановить просмотр. После нажатия эта кнопка превращается в кнопку . кнопка Запустить презентацию позволяет возобновить просмотр. После нажатия эта кнопка превращается в кнопку . кнопка Следующий слайд позволяет перейти к следующему слайду. Указатель номера слайда отображает номер текущего слайда, а также общее количество слайдов в презентации. Для перехода к определенному слайду в режиме просмотра щелкните по Указателю номера слайда, введите в открывшемся окне номер нужного слайда и нажмите клавишу Enter. кнопка Полноэкранный режим позволяет перейти в полноэкранный режим. кнопка Выйти из полноэкранного режима позволяет выйти из полноэкранного режима. кнопка Завершить показ слайдов позволяет выйти из режима просмотра. Для навигации по слайдам в режиме просмотра можно также использовать сочетания клавиш. Использование режима докладчика Примечание: в десктопной версии редакторов режим докладчика можно активировать только со вторым подключенным монитором. В режиме докладчика вы можете просматривать презентацию с заметками к слайдам в отдельном окне, и одновременно демонстрировать презентацию зрителям на другом мониторе, не показывая заметок к слайдам. Заметки к каждому слайду отображаются под областью просмотра слайда. Для навигации по слайдам можно использовать кнопки и или щелкать по слайдам в списке слева. Номера скрытых слайдов в списке перечеркнуты. Если вы захотите показать зрителям слайд, помеченный как скрытый, просто щелкните по нему в списке слайдов слева - слайд будет отображен. Можно использовать следующие элементы управления, расположенные под областью просмотра слайда: Таймер показывает истекшее время презентации в формате чч.мм.сс. кнопка Приостановить презентацию позволяет приостановить просмотр. После нажатия эта кнопка превращается в кнопку . кнопка Запустить презентацию позволяет возобновить просмотр. После нажатия эта кнопка превращается в кнопку . кнопка Сброс позволяет сбросить истекшее время презентации. кнопка Предыдущий слайд позволяет вернуться к предыдущему слайду. кнопка Следующий слайд позволяет перейти к следующему слайду. Указатель номера слайда отображает номер текущего слайда, а также общее количество слайдов в презентации. кнопка Указка позволяет выделить что-то на экране при показе презентации. Когда эта опция включена, кнопка выглядит следующим образом: . Чтобы указать на какие-то объекты, наведите курсор мыши на область просмотра слайда и перемещайте указку по слайду. Указка будет выглядеть так: . Чтобы отключить эту опцию, нажмите кнопку еще раз. кнопка Завершить показ слайдов позволяет выйти из режима докладчика."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Сохранение / печать / скачивание презентации", 
        "body": "Сохранение По умолчанию онлайн-редактор презентаций автоматически сохраняет файл каждые 2 секунды, когда вы работаете над ним, чтобы не допустить потери данных в случае непредвиденного закрытия программы. Если вы совместно редактируете файл в Быстром режиме, таймер запрашивает наличие изменений 25 раз в секунду и сохраняет их, если они были внесены. При совместном редактировании файла в Строгом режиме изменения автоматически сохраняются каждые 10 минут. При необходимости можно легко выбрать предпочтительный режим совместного редактирования или отключить функцию автоматического сохранения на странице Дополнительные параметры. Чтобы сохранить презентацию вручную в текущем формате и местоположении, нажмите значок Сохранить в левой части шапки редактора, или используйте сочетание клавиш Ctrl+S, или нажмите на вкладку Файл на верхней панели инструментов и выберите опцию Сохранить. Чтобы не допустить потери данных в десктопной версии в случае непредвиденного закрытия программы, вы можете включить опцию Автовосстановление на странице Дополнительные параметры. Чтобы в десктопной версии сохранить презентацию под другим именем, в другом местоположении или в другом формате, нажмите на вкладку Файл на верхней панели инструментов, выберите опцию Сохранить как, выберите один из доступных форматов: PPTX, ODP, PDF, PDF/A, PNG, JPG. Также можно выбрать вариант Шаблон презентации POTX или OTP. Скачивание Чтобы в онлайн-версии скачать готовую презентацию и сохранить ее на жестком диске компьютера, нажмите на вкладку Файл на верхней панели инструментов, выберите опцию Скачать как, выберите один из доступных форматов: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. Сохранение копии Чтобы в онлайн-версии сохранить копию презентации на портале, нажмите на вкладку Файл на верхней панели инструментов, выберите опцию Сохранить копию как, выберите один из доступных форматов: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG, выберите местоположение файла на портале и нажмите Сохранить. Печать Чтобы распечатать текущую презентацию, нажмите значок Напечатать файл в левой части шапки редактора, или используйте сочетание клавиш Ctrl+P, или нажмите на вкладку Файл на верхней панели инструментов и выберите опцию Печать. В браузере Firefox возможна печатать презентации без предварительной загрузки в виде файла .pdf. Также можно распечатать выделенные слайды с помощью пункта контекстного меню Напечатать выделенное как в режиме Редактирования, так и в режиме Просмотра (кликните правой кнопкой мыши по выделенным слайдам и выберите опцию Напечатать выделенное). В десктопной версии презентация будет напрямую отправлена на печать. В онлайн-версии на основе данной презентации будет сгенерирован файл PDF. Вы можете открыть и распечатать его, или сохранить его на жестком диске компьютера или съемном носителе чтобы распечатать позже. В некоторых браузерах, например Хром и Опера, есть встроенная возможность для прямой печати."
    },
   {
        "id": "UsageInstructions/SetSlideParameters.htm", 
        "title": "Настройка параметров слайда", 
        "body": "Чтобы настроить внешний вид презентации, можно выбрать тему, цветовую схему, размер и ориентацию слайдов для всей презентации, изменить заливку фона или макет слайда для каждого отдельного слайда, применить переходы между слайдами. Также можно добавить поясняющие заметки к каждому слайду, которые могут пригодиться при показе презентации в режиме докладчика. Темы позволяют быстро изменить дизайн презентации, а именно оформление фона слайдов, предварительно заданные шрифты для заголовков и текстов и цветовую схему, которая используется для элементов презентации. Для выбора темы презентации щелкните по нужной готовой теме из галереи тем, расположенной в правой части вкладки Главная верхней панели инструментов. Выбранная тема будет применена ко всем слайдам, если вы предварительно не выделили определенные слайды, к которым надо применить эту тему. Чтобы изменить выбранную тему для одного или нескольких слайдов, щелкните правой кнопкой мыши по выделенным слайдам в списке слева (или щелкните правой кнопкой мыши по слайду в области редактирования слайда), выберите в контекстном меню пункт Изменить тему, а затем выберите нужную тему. Цветовые схемы влияют на предварительно заданные цвета, используемые для элементов презентации (шрифтов, линий, заливок и т.д.) и позволяют обеспечить сочетаемость цветов во всей презентации. Для изменения цветовой схемы презентации щелкните по значку Изменение цветовой схемы на вкладке Главная верхней панели инструментов и выберите нужную схему из выпадающего списка. Выбранная схема будет выделена в списке и применена ко всем слайдам. Для изменения размера всех слайдов в презентации, щелкните по значку Выбор размеров слайда на вкладке Главная верхней панели инструментов и выберите нужную опцию из выпадающего списка. Можно выбрать: один из двух быстродоступных пресетов - Стандартный (4:3) или Широкоэкранный (16:9), команду Дополнительные параметры, которая вызывает окно Настройки размера слайда, где можно выбрать один из имеющихся предустановленных размеров или задать Пользовательский размер, указав нужные значения Ширины и Высоты. Доступны следующие предустановленные размеры: Стандартный (4:3), Широкоэкранный (16:9), Широкоэкранный (16:10), Лист Letter (8.5x11 дюймов), Лист Ledger (11x17 дюймов), Лист A3 (297x420 мм), Лист A4 (210x297 мм), Лист B4 (ICO) (250x353 мм), Лист B5 (ICO) (176x250 мм), Слайды 35 мм, Прозрачная пленка, Баннер. Меню Ориентация слайда позволяет изменить выбранный в настоящий момент тип ориентации слайда. По умолчанию используется Альбомная ориентация, которую можно изменить на Книжную. Для изменения заливки фона: в списке слайдов слева выделите слайды, к которым требуется применить заливку. Или в области редактирования слайдов щелкните по любому свободному месту внутри слайда, который в данный момент редактируется, чтобы изменить тип заливки для этого конкретного слайда. на вкладке Параметры слайда на правой боковой панели выберите нужную опцию: Заливка цветом - выберите эту опцию, чтобы задать сплошной цвет, который требуется применить к выбранным слайдам. Градиентная заливка - выберите эту опцию, чтобы чтобы залить слайд двумя цветами, плавно переходящими друг в друга. Изображение или текстура - выберите эту опцию, чтобы использовать в качестве фона слайда какое-то изображение или готовую текстуру. Узор - выберите эту опцию, чтобы залить слайд с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами. Без заливки - выберите эту опцию, если вы вообще не хотите использовать заливку. Непрозрачность - перетащите ползунок или введите процентное значение вручную. Значение по умолчанию - 100%. Это соответствует полной непрозрачности. Значение 0% соответствует полной прозрачности. Чтобы получить более подробную информацию об этих опциях, обратитесь к разделу Заливка объектов и выбор цветов. Переходы помогают сделать презентацию более динамичной и удерживать внимание аудитории. Для применения перехода: в списке слайдов слева выделите слайды, к которым требуется применить переход, на вкладке Параметры слайда выберите переход из выпадающего списка Эффект, Примечание: чтобы открыть вкладку Параметры слайда, можно щелкнуть по значку Параметры слайда справа или щелкнуть правой кнопкой мыши по слайду в области редактирования слайда и выбрать в контекстном меню пункт Параметры слайда. настройте свойства перехода: выберите вариант перехода, его длительность и то, каким образом должны сменяться слайды, если требуется применить один и тот же переход ко всем слайдам в презентации, нажмите кнопку Применить ко всем слайдам. Чтобы получить более подробную информацию об этих опциях, обратитесь к разделу Применение переходов. Для изменения макета слайда: в списке слайдов слева выделите слайды, для которых требуется применить новый макет, щелкните по значку Изменить макет слайда на вкладке Главная верхней панели инструментов, выберите в меню нужный макет. Вы можете также щелкнуть правой кнопкой мыши по нужному слайду в списке слева, выбрать в контекстном меню пункт Изменить макет и выбрать нужный макет. Примечание: в настоящий момент доступны следующие макеты: Титульный слайд, Заголовок и объект, Заголовок раздела, Два объекта, Сравнение, Только заголовок, Пустой слайд, Объект с подписью, Рисунок с подписью, Заголовок и вертикальный текст, Вертикальный заголовок и текст. Для добавления объектов к макету слайда: щелкните по значку Изменить макет слайда и выберите макет, к которому вы хотите добавить объект, при помощи вкладки Вставка верхней панели инструментов добавьте нужный объект на слайд (изображение, таблица, диаграмма, автофигура), далее нажмите правой кнопкой мыши на данный объект и выберите пункт Добавить в макет, на вкладке Главная нажмите Изменить макет слайда и примените измененный макет. Выделенные объекты будут добавлены к текущему макету темы. Примечание: расположенные таким образом объекты на слайде не могут быть выделены, изменены или передвинуты. Для возвращения макета слада в исходное состояние: в списке слайдов слева выделите слайды, которые нужно вернуть в состояние по умолчанию, Примечание: чтобы выбрать несколько сладов сразу, зажмите клавишу Ctrl и по одному выделяйте нужные или зажмите клавишу Shift, чтобы выделить все слайды от текущего до выбранного. щелкните правой кнопкой мыши по одному из слайдов и в контекстном меню выберите опцию Сбросить макет слайда, Ко всем выделенным слайдам вернется первоначальное положение текстовых рамок и объектов в соответствии с макетами. Для добавления заметок к слайду: в списке слайдов слева выберите слайд, к которому требуется добавить заметку, щелкните по надписи Нажмите, чтобы добавить заметки под областью редактирования слайда, введите текст заметки. Примечание: текст можно отформатировать с помощью значков на вкладке Главная верхней панели инструментов. При показе слайдов в режиме докладчика заметки к слайду будут отображаться под областью просмотра слайда."
    },
   {
        "id": "UsageInstructions/SupportSmartArt.htm", 
        "title": "Поддержка SmartArt в редакторе резентаций ONLYOFFICE", 
        "body": "Поддержка SmartArt в редакторе презентаций ONLYOFFICE Графика SmartArt используется для создания визуального представления иерархической структуры при помощи выбора наиболее подходящего макета. Редактор презентаций ONLYOFFICE поддерживает графические объекты SmartArt, добавленную с помощью сторонних редакторов. Вы можете открыть файл, содержащий SmartArt, и редактировать его как графический объект с помощью доступных инструментов. Если выделить графический объект SmartArt или его элемент, на правой боковой панели станут доступны следующие вкладки: Параметры слайда - для изменения заливки и прозрачности фона слайда, а также для отображения или скрытия номера слайда, даты и времени. Обратитесь к Настройке параметров слайда и Вставке колонтитулов для получения дополнительной информации. Параметры фигуры - для редактирования фигур, используемых в макете. Вы можете изменять размер формы, редактировать заливку, контур, толщину, стиль обтекания, положение, линии и стрелки, текстовое поле и альтернативный текст. Параметры абзаца - для редактирования отступов и интервалов, шрифтов и табуляций. Обратитесь к разделу Форматирование текста для подробного описания каждого параметра. Эта вкладка становится активной только для объектов SmartArt. Параметры объекта Text Art - для редактирования стиля Text Art, который используется SmartArt для выделения текста. Вы можете изменить шаблон Text Art, тип заливки, цвет и непрозрачность, толщину линии, цвет и тип. Эта вкладка становится активной только для объектов SmartArt. Щелкните правой кнопкой мыши по SmartArt или по границе данного элемента, чтобы получить доступ к следующим параметрам форматирования: Порядок - упорядочить объекты, используя следующие параметры: Перенести на передний план, Перенести на задний план, Перенести вперед, Перенести назад , Сгруппировать и Разгруппировать. Поворот - изменить направление вращения для выбранного элемента на SmartArt: Повернуть на 90° по часовой стрелке, Повернуть на 90° против часовой стрелки. Этот параметр становится активным только для объектов SmartArt. Назначить макрос - обеспечить быстрый и легкий доступ к макросу в презентации. Дополнительные параметры фигуры - для доступа к дополнительным параметрам форматирования фигуры. Щелкните правой кнопкой мыши по графическому объекту SmartArt, чтобы получить доступ к следующим параметрам форматирования текста: Выравнивание по вертикали - выбрать выравнивание текста внутри выбранного объекта SmartArt: Выровнять по верхнему краю, Выровнять по середине, Выровнять по нижнему краю. Направление текста - выбрать направление текста внутри выбранного объекта SmartArt: Горизонтальное, Повернуть текст вниз, Повернуть текст вверх. Гиперссылка - добавить гиперссылку к объекту SmartArt. Обратитесь к Дополнительным параметрам абзаца, чтобы получить информацию о дополнительных параметрах форматирования абзаца."
    },
   {
        "id": "UsageInstructions/ViewPresentationInfo.htm", 
        "title": "Просмотр сведений о презентации", 
        "body": "Чтобы получить доступ к подробным сведениям о редактируемой презентации, нажмите на вкладку Файл на верхней панели инструментов и выберите опцию Сведения о презентации. Общие сведения Сведения о презентации включают в себя ряд свойств файла, описывающих презентацию. Некоторые из этих свойств обновляются автоматически, а некоторые из них можно редактировать. Размещение - папка в модуле Документы, в которой хранится файл. Владелец - имя пользователя, который создал файл. Загружена - дата и время создания файла. Эти свойства доступны только в онлайн-версии. Название, Тема, Комментарий - эти свойства позволяют упростить классификацию презентаций. Вы можете задать нужный текст в полях свойств. Последнее изменение - дата и время последнего изменения файла. Автор последнего изменения - имя пользователя, сделавшего последнее изменение в презентации, если к ней был предоставлен доступ, и ее могут редактировать несколько пользователей. Приложение - приложение, в котором была создана презентация. Автор - имя человека, создавшего файл. В этом поле вы можете ввести нужное имя. Нажмите Enter, чтобы добавить новое поле, позволяющее указать еще одного автора. Если вы изменили свойства файла, нажмите кнопку Применить, чтобы применить изменения. Примечание: используя онлайн-редакторы, вы можете изменить название презентации непосредственно из интерфейса редактора. Для этого нажмите на вкладку Файл на верхней панели инструментов и выберите опцию Переименовать, затем введите нужное Имя файла в новом открывшемся окне и нажмите кнопку OK. Сведения о правах доступа В онлайн-версии вы можете просматривать сведения о правах доступа к файлам, сохраненным в облаке. Примечание: эта опция недоступна для пользователей с правами доступа Только чтение. Чтобы узнать, у кого есть права на просмотр и редактирование этой презентации, выберите опцию Права доступа на левой боковой панели. Вы можете также изменить выбранные в настоящий момент права доступа, нажав на кнопку Изменить права доступа в разделе Люди, имеющие права. История версий В онлайн-версии вы можете просматривать историю версий для файлов, сохраненных в облаке. Примечание: эта опция недоступна для пользователей с правами доступа Только чтение. Чтобы просмотреть все внесенные в презентацию изменения, выберите опцию История версий на левой боковой панели. Историю версий можно также открыть, используя значок История версий на вкладке Совместная работа верхней панели инструментов. Вы увидите список версий (существенных изменений) и ревизий (незначительных изменений) этой презентации с указанием автора и даты и времени создания каждой версии/ревизии. Для версий презентации также указан номер версии (например, вер. 2). Чтобы точно знать, какие изменения были внесены в каждой конкретной версии/ревизии, можно просмотреть нужную, нажав на нее на левой боковой панели. Изменения, внесенные автором версии/ревизии, помечены цветом, который показан рядом с именем автора на левой боковой панели. Можно использовать ссылку Восстановить, расположенную под выбранной версией/ревизией, чтобы восстановить ее. Чтобы вернуться к текущей версии презентации, нажмите на ссылку Закрыть историю над списком версий. Чтобы закрыть панель Файл и вернуться к редактированию презентации, выберите опцию Закрыть меню."
    }
]