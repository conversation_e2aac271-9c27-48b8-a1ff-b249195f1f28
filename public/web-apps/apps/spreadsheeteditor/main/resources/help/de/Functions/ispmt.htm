﻿<!DOCTYPE html>
<html>
	<head>
		<title>ISPMT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ISPMT-Funktion</h1>
			<p>Die Funktion <b>ISPMT</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie berechnet die bei einem konstanten Zahlungsplan während eines bestimmten Zeitraums für eine Investition gezahlten Zinsen.</p>
			<p>Die Formelsyntax der Funktion <b>ISPMT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ISPMT(Zins;Zr;Zzr;Bw;[Zw];[F])</em></b></p> 
			<p><em>Dabei ist:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zins</em></b> die Effektivverzinsung für die Investition.</p>
                <p style="text-indent: 50px;"><b><em>Zr</em></b> der Zeitraum, für den Sie die Zinsen berechnen möchten. Der Wert muss zwischen <b><em>1</em></b> und <b><em>Zzr</em></b> liegen.</p> 
				<p style="text-indent: 50px;"><b><em>Zzr</em></b> die Gesamtanzahl der Zahlungszeiträume für die Investition.</p> 
				<p style="text-indent: 50px;"><b><em>Bw</em></b> der gegenwärtige Wert der Investition. Bei einem Kredit ist „Bw“ die Kreditsumme.</p>
            <p class="note"><b>Hinweis:</b> für alle Argumente werden die Beträge, die Sie zahlen, beispielsweise Einlagen für Sparguthaben oder andere Abhebungen, durch negative Zahlen dargestellt. Beträge, die Sie erhalten, beispielsweise Dividendenzahlungen und andere Einlagen, werden durch positive Zahlen dargestellt. Sie sollten unbedingt darauf achten, dass Sie für <em>Zins</em> und <em>Zzr</em> zueinander passende Zeiteinheiten verwenden. Verwenden Sie beispielsweise bei monatliche Zahlungen N%/12 für <em>Zins</em> und N*12 für <em>Zzr</em>, für vierteljährliche Zahlungen N%/4 für <em>Zins</em> and N*4 für <em>Zzr</em> und für jährliche Zahlungen N% für  und N für .</p>
            <p>Der nummerische Werte wird manuell eingegeben oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>ISPMT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ISPMT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ISPMT-Funktion" src="../images/ispmt.png" /></p>
		</div>
	</body>
</html>