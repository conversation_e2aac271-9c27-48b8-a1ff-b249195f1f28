{"cancelButtonText": "Չեղարկել", "Common.Controllers.Chat.notcriticalErrorTitle": "Զգուշացում", "Common.Controllers.Chat.textEnterMessage": "Ներածեք ուղերձն այստեղ", "Common.Controllers.History.notcriticalErrorTitle": "Զգուշացում", "Common.define.chartData.textArea": "Տարածք", "Common.define.chartData.textAreaStacked": "Շեղջված տարածք", "Common.define.chartData.textAreaStackedPer": " 100% շեղջված տարածք", "Common.define.chartData.textBar": "Գիծ", "Common.define.chartData.textBarNormal": "Շեղաձև սյունակ", "Common.define.chartData.textBarNormal3d": "3-չափ փնջավոր սյունակ", "Common.define.chartData.textBarNormal3dPerspective": "3-չափ սյունակ", "Common.define.chartData.textBarStacked": "Շեղջաձև սյունակ", "Common.define.chartData.textBarStacked3d": "3-չափ շեղջաձև սյունակ", "Common.define.chartData.textBarStackedPer": "100% շեղաձև սյունակ", "Common.define.chartData.textBarStackedPer3d": " 3-չափ 100% շեղջաձև սյունակ", "Common.define.chartData.textCharts": "Գծապատկերներ", "Common.define.chartData.textColumn": "Սյունակ", "Common.define.chartData.textColumnSpark": "Սյունակ", "Common.define.chartData.textCombo": "Համակցված ", "Common.define.chartData.textComboAreaBar": "Շեղջված տարածք - փնջավոր սյունակ", "Common.define.chartData.textComboBarLine": "Սյունաձև գծույթ՝ խմբավորմամբ -գիծ", "Common.define.chartData.textComboBarLineSecondary": "Սյունաձև գծույթ՝ խմբավորմամբ-գիծ փոքր առանցքի վրա:", "Common.define.chartData.textComboCustom": "Հարմարեցված համակցություն", "Common.define.chartData.textDoughnut": "Օղակաձև գծապատկեր", "Common.define.chartData.textHBarNormal": "Գոտեգծույթ", "Common.define.chartData.textHBarNormal3d": " 3-չափ փնջավոր գոտի", "Common.define.chartData.textHBarStacked": "Շեղջաձև գոտի", "Common.define.chartData.textHBarStacked3d": "3-չափ շեղջաձև գոտի", "Common.define.chartData.textHBarStackedPer": " 100% շեղջաձև գոտի", "Common.define.chartData.textHBarStackedPer3d": "3-չափ 100% շեղջաձև գոտի", "Common.define.chartData.textLine": "Գիծ", "Common.define.chartData.textLine3d": "3-չափ գիծ", "Common.define.chartData.textLineMarker": "Տող՝ նշիչներով", "Common.define.chartData.textLineSpark": "Գիծ", "Common.define.chartData.textLineStacked": "Շեղջված գիծ", "Common.define.chartData.textLineStackedMarker": "Շեղջված գիծ՝ նշիչներով", "Common.define.chartData.textLineStackedPer": "100%  շեղջված գիծ", "Common.define.chartData.textLineStackedPerMarker": "100% շեղջված գիծ՝ նշիչներով", "Common.define.chartData.textPie": "Բլիթ", "Common.define.chartData.textPie3d": "3-չափ բլիթ", "Common.define.chartData.textPoint": "XY (Ցրում)", "Common.define.chartData.textScatter": "Ցրված", "Common.define.chartData.textScatterLine": "Ցրել ուղիղ գծերով և նշիչներով", "Common.define.chartData.textScatterLineMarker": "Ցրել ուղիղ գծերով և նշիչներով", "Common.define.chartData.textScatterSmooth": "Ցրել սահուն գծերով", "Common.define.chartData.textScatterSmoothMarker": "Ցրել սահուն գծերով և նշիչներով", "Common.define.chartData.textSparks": "կայծգծեր", "Common.define.chartData.textStock": "Տվյալների տատանում", "Common.define.chartData.textSurface": "Մակերեսային", "Common.define.chartData.textWinLossSpark": "Հաղթել/Պարտություն", "Common.define.conditionalData.exampleText": "ԱաԲբԳգՕօՖֆ", "Common.define.conditionalData.noFormatText": "Ձևաչափ չկա", "Common.define.conditionalData.text1Above": "1 std dev above", "Common.define.conditionalData.text1Below": "1 սով. շեղ. վար", "Common.define.conditionalData.text2Above": "2 սով. շեղ. վեր", "Common.define.conditionalData.text2Below": "2 սով. շեղ. վար", "Common.define.conditionalData.text3Above": "3 սով. շեղ. վեր", "Common.define.conditionalData.text3Below": "3 սով. շեղ. վար", "Common.define.conditionalData.textAbove": "Վերև", "Common.define.conditionalData.textAverage": "ՄԻՋԻՆ", "Common.define.conditionalData.textBegins": "Սկսվում է ", "Common.define.conditionalData.textBelow": "ներքևում", "Common.define.conditionalData.textBetween": "միջև", "Common.define.conditionalData.textBlank": "Դատարկ սահիկ", "Common.define.conditionalData.textBlanks": "Պարունակում է դատարկ", "Common.define.conditionalData.textBottom": "Ներքև", "Common.define.conditionalData.textContains": "Պարունակում է", "Common.define.conditionalData.textDataBar": "Տվյալների բար", "Common.define.conditionalData.textDate": "Ամիս-ամսաթիվ", "Common.define.conditionalData.textDuplicate": "Կրկնօրինակել", "Common.define.conditionalData.textEnds": "Ավարտվում է ", "Common.define.conditionalData.textEqAbove": "Հավասար կամ ավելի բարձր", "Common.define.conditionalData.textEqBelow": "Հավասար կամ ցածր", "Common.define.conditionalData.textEqual": "Հավասար է", "Common.define.conditionalData.textError": "Սխալ", "Common.define.conditionalData.textErrors": "Պարունակում է սխալներ", "Common.define.conditionalData.textFormula": "Բանաձև ", "Common.define.conditionalData.textGreater": "Ավելի մեծ քան", "Common.define.conditionalData.textGreaterEq": "Ավելի մեծ կամ հավասար", "Common.define.conditionalData.textIconSets": "Պատկերակների կազմեր", "Common.define.conditionalData.textLast7days": "Վերջին 7 օրվա ընթացքում", "Common.define.conditionalData.textLastMonth": "Անցած ամիս", "Common.define.conditionalData.textLastWeek": "Անցած շաբաթ", "Common.define.conditionalData.textLess": "Ավելի քիչ քան ", "Common.define.conditionalData.textLessEq": "Պակաս կամ հավասար", "Common.define.conditionalData.textNextMonth": "Հաջորդ ամիս", "Common.define.conditionalData.textNextWeek": "Հաջորդ շաբաթ", "Common.define.conditionalData.textNotBetween": "ոչ թե միջև", "Common.define.conditionalData.textNotBlanks": "Բլանկներ չի պարունակում", "Common.define.conditionalData.textNotContains": "Չի պարունակում", "Common.define.conditionalData.textNotEqual": "Հավասար չէ՝", "Common.define.conditionalData.textNotErrors": "Չի պարունակում սխալներ", "Common.define.conditionalData.textText": "Տեքստ", "Common.define.conditionalData.textThisMonth": "Այս ամիս", "Common.define.conditionalData.textThisWeek": "Այս շաբաթ", "Common.define.conditionalData.textToday": "Այսօր", "Common.define.conditionalData.textTomorrow": "Վաղը", "Common.define.conditionalData.textTop": "Վերև", "Common.define.conditionalData.textUnique": "Յուրահատուկ", "Common.define.conditionalData.textValue": "Արժեքն է", "Common.define.conditionalData.textYesterday": "Երեկ", "Common.define.smartArt.textAccentedPicture": "Շեշտված նկար", "Common.define.smartArt.textAccentProcess": "Շեշտման ընթացք", "Common.define.smartArt.textAlternatingFlow": "Այլընտրական հոսք", "Common.define.smartArt.textAlternatingHexagons": "Այլընտրական վեցանկյունիներ", "Common.define.smartArt.textAlternatingPictureBlocks": "Այլընտրական նկարների կազմեր", "Common.define.smartArt.textAlternatingPictureCircles": "Այլընտրական նկարների շրջանակներ", "Common.define.smartArt.textArchitectureLayout": "Ճարտարապետական դասավորություն", "Common.define.smartArt.textArrowRibbon": "Սլաքի երիզ", "Common.define.smartArt.textAscendingPictureAccentProcess": "Աճող նկարների շեշտման ընթացք", "Common.define.smartArt.textBalance": "Հաշվեկշիռ", "Common.define.smartArt.textBasicBendingProcess": "Հիմնական ծռման ընթացք", "Common.define.smartArt.textBasicBlockList": "Հիմնական բաժնի ցուցակ", "Common.define.smartArt.textBasicChevronProcess": "Հիմնական ծպեղների ընթացք", "Common.define.smartArt.textBasicCycle": "Հիմնական շրջան", "Common.define.smartArt.textBasicMatrix": "Հիմնական մատրիցա", "Common.define.smartArt.textBasicPie": "Հիմնական բլիթ", "Common.define.smartArt.textBasicProcess": "Հիմնական ընթացք", "Common.define.smartArt.textBasicPyramid": "Հիմնական բուրգ", "Common.define.smartArt.textBasicRadial": "Հիմնական շառավիղ", "Common.define.smartArt.textBasicTarget": "Հիմնական նպատակ", "Common.define.smartArt.textBasicTimeline": "Հիմնական ժամագիծ", "Common.define.smartArt.textBasicVenn": "Հիմնական վրածածք", "Common.define.smartArt.textBendingPictureAccentList": "Ծռված նկարի շեշտման ցուցակ", "Common.define.smartArt.textBendingPictureBlocks": "Ծռված նկարների կազմեր", "Common.define.smartArt.textBendingPictureCaption": "Ծռված նկարի խորագիր", "Common.define.smartArt.textBendingPictureCaptionList": "Ծռված նկարի խորագրերի ցուցակ", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Ծռված նկարի կիսաթափանցիկ գրվածք", "Common.define.smartArt.textBlockCycle": "Բաժնի շրջան", "Common.define.smartArt.textBubblePictureList": "Դրսագրով նկարների ցուցակ", "Common.define.smartArt.textCaptionedPictures": "Խորագրով նկարներ", "Common.define.smartArt.textChevronAccentProcess": "Ծպեղների շեշտման ընթաց", "Common.define.smartArt.textChevronList": "Ծպեղների ցուցակ", "Common.define.smartArt.textCircleAccentTimeline": "Շրջանաձև շեշտման ժամագիծ", "Common.define.smartArt.textCircleArrowProcess": "Շրջանաձև սլաքի ընթացք", "Common.define.smartArt.textCirclePictureHierarchy": "Շրջանաձև նկարների աստիճանակարգություն", "Common.define.smartArt.textCircleProcess": "Շրջանաձև ընթացք", "Common.define.smartArt.textCircleRelationship": "Շրջանների հարաբերություն", "Common.define.smartArt.textCircularBendingProcess": "Շրջանային ծռման ընթացք", "Common.define.smartArt.textCircularPictureCallout": "Շրջանաձև նկարի դրսագիր", "Common.define.smartArt.textClosedChevronProcess": "Փակ ծպեղների ընթացք", "Common.define.smartArt.textContinuousArrowProcess": "Շարունակական սլաքի ընթացք", "Common.define.smartArt.textContinuousBlockProcess": "Շարունակական բաժնի ընթացք", "Common.define.smartArt.textContinuousCycle": "Շարունակական շրջան", "Common.define.smartArt.textContinuousPictureList": "Շարունակական նկարի ցուցակ", "Common.define.smartArt.textConvergingArrows": "Միակցող սլաքներ", "Common.define.smartArt.textConvergingRadial": "Զուգահեռ շառավիղ", "Common.define.smartArt.textConvergingText": "Զուգամետ գրվածք", "Common.define.smartArt.textCounterbalanceArrows": "Հակակշիռ սլաքներ", "Common.define.smartArt.textCycle": "Շրջան", "Common.define.smartArt.textCycleMatrix": "Շրջանային մատրիցա", "Common.define.smartArt.textDescendingBlockList": "Նվազող կազմերի ցուցակ", "Common.define.smartArt.textDescendingProcess": "Նվազող ընթացք", "Common.define.smartArt.textDetailedProcess": "Մանրամասն ընթացք", "Common.define.smartArt.textDivergingArrows": "Հակադիր սլաքներ", "Common.define.smartArt.textDivergingRadial": "Ցրված շառավիղ", "Common.define.smartArt.textEquation": "Հավասարում", "Common.define.smartArt.textFramedTextPicture": "Շրջանակված գրվածքով նկար", "Common.define.smartArt.textFunnel": "Ձագարաձև", "Common.define.smartArt.textGear": "Ատամնանիվ", "Common.define.smartArt.textGridMatrix": "Ցանցավոր մատրիցա", "Common.define.smartArt.textGroupedList": "Խմբավորված ցուցակ", "Common.define.smartArt.textHalfCircleOrganizationChart": "Կիսաշրջանաձև կազմակերպության գծապատկեր", "Common.define.smartArt.textHexagonCluster": "Վեցանկյունիների բույլ", "Common.define.smartArt.textHexagonRadial": "Վեցանկյունիների շառավիղ", "Common.define.smartArt.textHierarchy": "Ստորակարգ", "Common.define.smartArt.textHierarchyList": "Ստորակարգի ցուցակ", "Common.define.smartArt.textHorizontalBulletList": "Հորիզոնական պարբերակի ցուցակ", "Common.define.smartArt.textHorizontalHierarchy": "Հորիզոնական ստորակարգ", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Հորիզոնական պիտակված ստորակարգ", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Հորիզոնական բազմակակարդակ աստիճանակարգություն", "Common.define.smartArt.textHorizontalOrganizationChart": "Հորիզոնական կազմակերպության գծապատկեր", "Common.define.smartArt.textHorizontalPictureList": "Հորիզոնական նկարների ցուցակ", "Common.define.smartArt.textIncreasingArrowProcess": "Աճող սլաքի ընթացք", "Common.define.smartArt.textIncreasingCircleProcess": "Աճող շրջանաձև ընթացք", "Common.define.smartArt.textInterconnectedBlockProcess": "Փոխկապակցված կազմերի ընթացք", "Common.define.smartArt.textInterconnectedRings": "Փոխկապակցված օղակներ", "Common.define.smartArt.textInvertedPyramid": "Հակադարձված բուրգ", "Common.define.smartArt.textLabeledHierarchy": "Պիտակված ստորակարգ", "Common.define.smartArt.textLinearVenn": "Գծային վրածածք", "Common.define.smartArt.textLinedList": "Գծված ցուցակ", "Common.define.smartArt.textList": "Ցուցակ", "Common.define.smartArt.textMatrix": "Մատրիցա", "Common.define.smartArt.textMultidirectionalCycle": "Բազմուղի շրջան", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Անուններով և պաշտոններով կազմակերպության գծապատկեր", "Common.define.smartArt.textNestedTarget": "Ներդրված թիրախ", "Common.define.smartArt.textNondirectionalCycle": "Ոչ ուղղորդված շրջան", "Common.define.smartArt.textOpposingArrows": "Հակադրող սլաքներ", "Common.define.smartArt.textOpposingIdeas": "Հակադրող առաջարկներ", "Common.define.smartArt.textOrganizationChart": "Կազմակերպության գծապատկեր", "Common.define.smartArt.textOther": "Այլ", "Common.define.smartArt.textPhasedProcess": "Փուլային ընթացք", "Common.define.smartArt.textPicture": "Նկար", "Common.define.smartArt.textPictureAccentBlocks": "Նկարների շեշտման կազմեր", "Common.define.smartArt.textPictureAccentList": "Նկարի շեշտման ցուցակ", "Common.define.smartArt.textPictureAccentProcess": "Նկարի շեշտման ընթացք", "Common.define.smartArt.textPictureCaptionList": "Նկարի խորագրերի ցուցակ", "Common.define.smartArt.textPictureFrame": "ՆկարիՇրջանակ", "Common.define.smartArt.textPictureGrid": "Նկարների ցանց", "Common.define.smartArt.textPictureLineup": "Նկարների շարան", "Common.define.smartArt.textPictureOrganizationChart": "Նկարի կազմակերպության գծապատկեր", "Common.define.smartArt.textPictureStrips": "Նկարների գծեր", "Common.define.smartArt.textPieProcess": "Բլիթային գծապատկերով ընթացք", "Common.define.smartArt.textPlusAndMinus": "Գումարած և հանած", "Common.define.smartArt.textProcess": "Ընթացք", "Common.define.smartArt.textProcessArrows": "Ընթացքի սլաքներ", "Common.define.smartArt.textProcessList": "Ընթացքների ցուցակ", "Common.define.smartArt.textPyramid": "Բուրգ", "Common.define.smartArt.textPyramidList": "Բուրգի ցուցակ", "Common.define.smartArt.textRadialCluster": "Շառավիղների բույլ", "Common.define.smartArt.textRadialCycle": "Շառավղային շրջան", "Common.define.smartArt.textRadialList": "Շառավղի ցուցակ", "Common.define.smartArt.textRadialPictureList": "Շառավղային նկարների ցուցակ", "Common.define.smartArt.textRadialVenn": "Շառավղային վրածածք", "Common.define.smartArt.textRandomToResultProcess": "Պատահականից դեպի արդյունք ընթացք", "Common.define.smartArt.textRelationship": "Հարաբերություն", "Common.define.smartArt.textRepeatingBendingProcess": "Կրկնվող ծռման ընթացք", "Common.define.smartArt.textReverseList": "Հետադարձ ցուցակ", "Common.define.smartArt.textSegmentedCycle": "Մասնատված շրջան", "Common.define.smartArt.textSegmentedProcess": "Մասնատված ընթացք", "Common.define.smartArt.textSegmentedPyramid": "Մասնատված բուրգ", "Common.define.smartArt.textSnapshotPictureList": "Ճեպապատկերների ցուցակ", "Common.define.smartArt.textSpiralPicture": "Պարուրաձև նկար", "Common.define.smartArt.textSquareAccentList": "Քառակուսի շեշտման ցուցակ", "Common.define.smartArt.textStackedList": "Շեղջված ցուցակ", "Common.define.smartArt.textStackedVenn": "Շեղջված վրածածք", "Common.define.smartArt.textStaggeredProcess": "Աստիճանայաին ընթացք", "Common.define.smartArt.textStepDownProcess": "Իջնող ընթացք", "Common.define.smartArt.textStepUpProcess": "Բարձրացող ընթացք", "Common.define.smartArt.textSubStepProcess": "Ենթաքայլերով ընթացք", "Common.define.smartArt.textTabbedArc": "Ներդիրավոր աղեղ", "Common.define.smartArt.textTableHierarchy": "Աղյուսակի ստորակարգ", "Common.define.smartArt.textTableList": "Աղյուսակային ցուցակ", "Common.define.smartArt.textTabList": "Ներդիրների ցուցակ", "Common.define.smartArt.textTargetList": "Նպատակակետի ցուցակ", "Common.define.smartArt.textTextCycle": "Գրվածքի շրջան", "Common.define.smartArt.textThemePictureAccent": "Ոճի նկարի շեշտում", "Common.define.smartArt.textThemePictureAlternatingAccent": "Ոճի նկարի այլընտրական շեշտում", "Common.define.smartArt.textThemePictureGrid": "Ոճի նկարի ցանց", "Common.define.smartArt.textTitledMatrix": "Անվանված մատրիցա", "Common.define.smartArt.textTitledPictureAccentList": "Անվանված նկարների շեշտման ցուցակ", "Common.define.smartArt.textTitledPictureBlocks": "Անվանված նկարների կազմեր", "Common.define.smartArt.textTitlePictureLineup": "Ոճի նկարների շարան", "Common.define.smartArt.textTrapezoidList": "Սեղանի ցուցակ", "Common.define.smartArt.textUpwardArrow": "Վեր սլացող սլաք", "Common.define.smartArt.textVaryingWidthList": "Փոփոխվող լայնությունների ցուցակ", "Common.define.smartArt.textVerticalAccentList": "Ուղղաձիգ շեշտման ցուցակ", "Common.define.smartArt.textVerticalArrowList": "Ուղղաձիգ սլաքի ցուցակ", "Common.define.smartArt.textVerticalBendingProcess": "Ուղղաձիգ ծռման ընթացք", "Common.define.smartArt.textVerticalBlockList": "Ուղղաձիգ կապանի ցուցակ", "Common.define.smartArt.textVerticalBoxList": "Ուղղաձիգ ցուցակատուփ", "Common.define.smartArt.textVerticalBracketList": "Ուղղաձիգ ուղղանկյուն փակագծերի ցուցակ", "Common.define.smartArt.textVerticalBulletList": "Ուղղաձիգ պարբերակների ցուցակ", "Common.define.smartArt.textVerticalChevronList": "Ուղղաձիգ ծպեղների ցուցակ", "Common.define.smartArt.textVerticalCircleList": "Ուղղաձիգ շրջանով ցուցակ", "Common.define.smartArt.textVerticalCurvedList": "Ուղղաձիգ կորով ցուցակ", "Common.define.smartArt.textVerticalEquation": "Ուղղաձիգ հավասարում", "Common.define.smartArt.textVerticalPictureAccentList": "Ուղղաձիգ նկարի շեշտման ցուցակ", "Common.define.smartArt.textVerticalPictureList": "Ուղղաձիգ նկարի ցուցակ", "Common.define.smartArt.textVerticalProcess": "Ուղղաձիգ ընթացք", "Common.Translation.textMoreButton": "Ավելին", "Common.Translation.tipFileLocked": "Փաստաթուղթը կողպված է խմբագրման համար:Դուք կարող եք փոփոխություններ կատարել և հետագայում պահպանել այն որպես տեղական պատճեն:", "Common.Translation.tipFileReadOnly": "Փաստաթուղթը միայն կարդալու է և կողպված է խմբագրման համար:ուք կարող եք փոփոխություններ կատարել ևպահպանել դրա տեղային պատճենն ավելի ուշ :", "Common.Translation.warnFileLocked": "Ֆայլը խմբագրվում է մեկ այլ հավելվածում:Դուք կարող եք շարունակել խմբագրումը և պահպանել այն որպես պատճեն:", "Common.Translation.warnFileLockedBtnEdit": "Ստեղծել պատճեն", "Common.Translation.warnFileLockedBtnView": "Բացել դիտման համար", "Common.UI.ButtonColored.textAutoColor": "Ինքնաշխատ", "Common.UI.ButtonColored.textNewColor": "Հավելել նոր հարմարեցված գույն", "Common.UI.ComboBorderSize.txtNoBorders": "Առանց եզրագծերի", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Առանց եզրագծերի", "Common.UI.ComboDataView.emptyComboText": "Առանց ոճերի", "Common.UI.ExtendedColorDialog.addButtonText": "Հավելել", "Common.UI.ExtendedColorDialog.textCurrent": "Ընթացիկ", "Common.UI.ExtendedColorDialog.textHexErr": " Մուտքագրված արժեքը սխալ է: Խնդրում ենք մուտքագրել 000000-ից  FFFFFF թվային արժեք:", "Common.UI.ExtendedColorDialog.textNew": "Նոր", "Common.UI.ExtendedColorDialog.textRGBErr": "Մուտքագրված արժեքը սխալ է:Խնդրում ենք մուտքագրել 0-ից 255 թվային արժեք:", "Common.UI.HSBColorPicker.textNoColor": "Առանց գույն", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Թաքցնել գաղտնաբառը", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Ցուցադրել գաղտնաբառը", "Common.UI.SearchBar.textFind": "Գտնել", "Common.UI.SearchBar.tipCloseSearch": "Փակել որոնումը", "Common.UI.SearchBar.tipNextResult": "Հաջորդ արդյունքը", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Բացել ընդլայնված կարգավորումները", "Common.UI.SearchBar.tipPreviousResult": "Նախորդ արդյունքը", "Common.UI.SearchDialog.textHighlight": "Գունանշել արդյունքները", "Common.UI.SearchDialog.textMatchCase": "Հաշվի առնել տառաշարը", "Common.UI.SearchDialog.textReplaceDef": "Մուտքագրեք փոխարինման տեքստը", "Common.UI.SearchDialog.textSearchStart": "Այստեղ մուտքագրեք Ձեր տեքստը", "Common.UI.SearchDialog.textTitle": "Գտնել և փոխարինել", "Common.UI.SearchDialog.textTitle2": "Գտնել", "Common.UI.SearchDialog.textWholeWords": "Միայն ամբողջական բառերը", "Common.UI.SearchDialog.txtBtnHideReplace": "Թաքցնել Փոխարինելը", "Common.UI.SearchDialog.txtBtnReplace": "Փոխարինել", "Common.UI.SearchDialog.txtBtnReplaceAll": "Փոխարինել բոլորը", "Common.UI.SynchronizeTip.textDontShow": "Այս գրությունն այլևս ցույց չտալ", "Common.UI.SynchronizeTip.textSynchronize": "Փաստաթուղթն այլ օգտատիրոջ կողմից փոփոխվել է։<br>Սեղմեք, որ պահպանեք Ձեր փոփոխումները և բեռնեք թարմացումները։", "Common.UI.ThemeColorPalette.textRecentColors": "Վերջին գույները", "Common.UI.ThemeColorPalette.textStandartColors": "Ստանդարտ գույներ", "Common.UI.ThemeColorPalette.textThemeColors": "Ոճի գույներ", "Common.UI.Themes.txtThemeClassicLight": "Դասական լույս", "Common.UI.Themes.txtThemeContrastDark": "Մութ հակադրություն", "Common.UI.Themes.txtThemeDark": "Մութ", "Common.UI.Themes.txtThemeLight": "Լույս", "Common.UI.Themes.txtThemeSystem": "Նույնը, ինչ համակարգը", "Common.UI.Window.cancelButtonText": "Չեղարկել", "Common.UI.Window.closeButtonText": "Փակել", "Common.UI.Window.noButtonText": "Ոչ", "Common.UI.Window.okButtonText": "Լավ", "Common.UI.Window.textConfirmation": "Հաստատում", "Common.UI.Window.textDontShow": "Այս գրությունն այլևս ցույց չտալ", "Common.UI.Window.textError": "Սխալ", "Common.UI.Window.textInformation": "Տեղեկատվություն", "Common.UI.Window.textWarning": "Զգուշացում", "Common.UI.Window.yesButtonText": "Այո", "Common.Utils.Metric.txtCm": "սմ", "Common.Utils.Metric.txtPt": "կտ", "Common.Utils.String.textAlt": "Alt ստեղն", "Common.Utils.String.textCtrl": "Ctrl ստեղն", "Common.Utils.String.textShift": "Shift ստեղն", "Common.Views.About.txtAddress": "հասցե՝", "Common.Views.About.txtLicensee": "ԼԻՑԵՆԶԻԱ", "Common.Views.About.txtLicensor": "Լիցենզատու ", "Common.Views.About.txtMail": "էլ․ փոստ՝", "Common.Views.About.txtPoweredBy": "Օժանդակող՝", "Common.Views.About.txtTel": "հեռ.", "Common.Views.About.txtVersion": "Տարբերակ", "Common.Views.AutoCorrectDialog.textAdd": "Հավելել", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Դիմեք, երբ աշխատում եք", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Ինքնաշտկում", "Common.Views.AutoCorrectDialog.textAutoFormat": "Ինքնաձեւաչափում մուտքագրելիս", "Common.Views.AutoCorrectDialog.textBy": "Ըստ", "Common.Views.AutoCorrectDialog.textDelete": "Ջնջել", "Common.Views.AutoCorrectDialog.textHyperlink": "Ինտերնետ և ցանցային ուղիներ՝ հիպերհղումներով", "Common.Views.AutoCorrectDialog.textMathCorrect": "Մաթ ինքնաշտկում ", "Common.Views.AutoCorrectDialog.textNewRowCol": "Աղյուսակում ներառեք նոր տողեր և սյունակներ", "Common.Views.AutoCorrectDialog.textRecognized": "Ճանաչված գործառույթներ", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Հետեւյալ արտահայտությունները ճանաչված մաթեմատիկական արտահյտություններ են: Նրանք ինքնաբար չեն շեղվելու:", "Common.Views.AutoCorrectDialog.textReplace": "Փոխարինել", "Common.Views.AutoCorrectDialog.textReplaceText": "Փոխարինել՝ մուտքագրելիս", "Common.Views.AutoCorrectDialog.textReplaceType": "Փոխարինել տեքստը՝ մուտքագրելիս", "Common.Views.AutoCorrectDialog.textReset": "Վերակայել", "Common.Views.AutoCorrectDialog.textResetAll": "Վերակայել սկզբնադիր արժեքին", "Common.Views.AutoCorrectDialog.textRestore": "Վերականգնել", "Common.Views.AutoCorrectDialog.textTitle": "Ինքնաշտկում", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Ճանաչված գործառույթները պետք է պարունակեն միայն A-ից Z տառերը, մեծատառեր կամ փոքրատառեր:", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Ձեր ավելացրած ցանկացած արտահայտություն կհեռացվի և հեռացվածները կվերականգնվեն: Ցանկանու՞մ եք շարունակել։", "Common.Views.AutoCorrectDialog.warnReplace": "%1-ի ինքնաշտկման գրառումն արդեն գոյություն ունի:Ցանկանու՞մ եք այն փոխարինել:", "Common.Views.AutoCorrectDialog.warnReset": "Ձեր ավելացրած ցանկացած ավտոմատ ուղղում կհեռացվի և փոխվածները կվերականգնվեն իրենց սկզբնական արժեքներին: Ցանկանու՞մ եք շարունակել։", "Common.Views.AutoCorrectDialog.warnRestore": "%1-ի ինքնաշտկման գրառումը կվերակայվի իր սկզբնական արժեքին:Ցանկանու՞մ եք շարունակել։", "Common.Views.Chat.textSend": "ՈՒղարկել", "Common.Views.Comments.mniAuthorAsc": "Հեղինակ Ա-ից Զ", "Common.Views.Comments.mniAuthorDesc": "Հեղինակ Զ-ից Ա", "Common.Views.Comments.mniDateAsc": "Ամենահին", "Common.Views.Comments.mniDateDesc": "Նորագույն", "Common.Views.Comments.mniFilterGroups": "Զտել ըստ խմբի", "Common.Views.Comments.mniPositionAsc": "Վերևից", "Common.Views.Comments.mniPositionDesc": "Ներքևից ", "Common.Views.Comments.textAdd": "Հավելել", "Common.Views.Comments.textAddComment": "Ավելացնել", "Common.Views.Comments.textAddCommentToDoc": "Փաստաթղթում կցել մեկնաբանություն", "Common.Views.Comments.textAddReply": "Պատասխանել", "Common.Views.Comments.textAll": "Բոլորը", "Common.Views.Comments.textAnonym": "Հյուր", "Common.Views.Comments.textCancel": "Չեղարկել", "Common.Views.Comments.textClose": "Փակել", "Common.Views.Comments.textClosePanel": "Փակել մեկնաբանությունները:", "Common.Views.Comments.textComments": "Մեկնաբանություններ", "Common.Views.Comments.textEdit": "Լավ", "Common.Views.Comments.textEnterCommentHint": "Այստեղ մուտքագրեք Ձեր մեկնաբանությունը", "Common.Views.Comments.textHintAddComment": "Կցել մեկնաբանություն", "Common.Views.Comments.textOpenAgain": "Նորից բացել", "Common.Views.Comments.textReply": "Պատասխանել", "Common.Views.Comments.textResolve": "Լուծել", "Common.Views.Comments.textResolved": "Լուծված", "Common.Views.Comments.textSort": "Տեսակավորել մեկնաբանությունները", "Common.Views.Comments.textViewResolved": "Դուք մեկնաբանությունը վերաբացելու թույլտվություն չունեք", "Common.Views.Comments.txtEmpty": "Թերթում մեկնաբանություններ չկան։", "Common.Views.CopyWarningDialog.textDontShow": "Այս գրությունն այլևս ցույց չտալ", "Common.Views.CopyWarningDialog.textMsg": "Պատճենել, կտրել և փակցնել կարող եք գործիքագոտու կոճակների և համատեքստի ցանկի հրամանների միջոցով խմբագրիչի միայն այս ներդիրում։<br><br>Դրանից դուրս հավելվածներից պատճենելու կամ փակցնելու համար օգտագործեք հետևյալ ստեղնային համադրությունները.", "Common.Views.CopyWarningDialog.textTitle": "Պատճենելու, կտրելու և փակցնելու գործողություններ", "Common.Views.CopyWarningDialog.textToCopy": "պատճենման համար", "Common.Views.CopyWarningDialog.textToCut": "Կտրելու համար", "Common.Views.CopyWarningDialog.textToPaste": "փակցնելու համար", "Common.Views.DocumentAccessDialog.textLoading": "Բեռնում...", "Common.Views.DocumentAccessDialog.textTitle": "Համօգտագործման կարգավորումներ", "Common.Views.EditNameDialog.textLabel": "Պիտակ։", "Common.Views.EditNameDialog.textLabelError": "Պիտակը չպետք է դատարկ լինի", "Common.Views.Header.labelCoUsersDescr": "Փաստաթուղթը խմբագրողներ՝", "Common.Views.Header.textAddFavorite": "Նշել որպես հավանած տարբերակ", "Common.Views.Header.textAdvSettings": "Հավելյալ կարգավորումներ", "Common.Views.Header.textBack": "Բացել նիշքի պանակը", "Common.Views.Header.textCompactView": "Թաքցնել գործիքագոտին", "Common.Views.Header.textHideLines": "Թաքցնել քանոնները", "Common.Views.Header.textHideStatusBar": "Միավորել թերթիկը և կարգավիճակի գծերը", "Common.Views.Header.textReadOnly": "Միայն կարդալու", "Common.Views.Header.textRemoveFavorite": "Ջնջել ընտրված ցուցակից", "Common.Views.Header.textSaveBegin": "Պահում", "Common.Views.Header.textSaveChanged": "Փոփոխված", "Common.Views.Header.textSaveEnd": "Բոլոր փոփոխումները պահպանված են", "Common.Views.Header.textSaveExpander": "Բոլոր փոփոխումները պահպանված են", "Common.Views.Header.textShare": "Տարածել", "Common.Views.Header.textZoom": "Խոշորացնել", "Common.Views.Header.tipAccessRights": "Կառավարել փաստաթղթի մատչման իրավունքները", "Common.Views.Header.tipDownload": "Ներբեռնել նիշքը", "Common.Views.Header.tipGoEdit": "Խմբագրել ընթացիկ նիշքը", "Common.Views.Header.tipPrint": "Տպել նիշքը", "Common.Views.Header.tipPrintQuick": "Արագ տպում", "Common.Views.Header.tipRedo": "Վերարկել", "Common.Views.Header.tipSave": "Պահպանել", "Common.Views.Header.tipSearch": "Որոնել", "Common.Views.Header.tipUndo": "Հետարկել", "Common.Views.Header.tipUndock": " Ապահարակցել առանձին պատուհանի մեջ", "Common.Views.Header.tipUsers": "Դիտել օգտվողներին", "Common.Views.Header.tipViewSettings": "Դիտել կարգավորումները", "Common.Views.Header.tipViewUsers": "Դիտել օգտատերերին և կառավարել փաստաթղթի մատչման իրավունքները", "Common.Views.Header.txtAccessRights": "Փոխել մատչման իրավունքները", "Common.Views.Header.txtRename": "Վերանվանել", "Common.Views.History.textCloseHistory": "Փակել պատմությունը", "Common.Views.History.textHide": "Կոծկել", "Common.Views.History.textHideAll": "Թաքցնել մանրամասն փոփոխությունները", "Common.Views.History.textRestore": "Վերականգնել", "Common.Views.History.textShow": "Ընդարձակել", "Common.Views.History.textShowAll": "Ցուցադրել մանրամասն փոփոխությունները ", "Common.Views.History.textVer": "Վեր", "Common.Views.ImageFromUrlDialog.textUrl": "Փակցնել նկարի URL՝", "Common.Views.ImageFromUrlDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Այս դաշտը պետք է լինի URL \"http://www.example.com\" ձևաչափով", "Common.Views.ListSettingsDialog.textBulleted": "Պարբերանշված", "Common.Views.ListSettingsDialog.textFromFile": "Ֆայլից", "Common.Views.ListSettingsDialog.textFromStorage": "Պահեստից", "Common.Views.ListSettingsDialog.textFromUrl": "URL-ից", "Common.Views.ListSettingsDialog.textNumbering": "Համարակալված", "Common.Views.ListSettingsDialog.textSelect": "Ընտրել", "Common.Views.ListSettingsDialog.tipChange": "Փոխել պարբերակը", "Common.Views.ListSettingsDialog.txtBullet": "Պարբերակ", "Common.Views.ListSettingsDialog.txtColor": "Գույն", "Common.Views.ListSettingsDialog.txtImage": "Նկար", "Common.Views.ListSettingsDialog.txtImport": "Ներմուծել", "Common.Views.ListSettingsDialog.txtNewBullet": "Նոր պարբերակ", "Common.Views.ListSettingsDialog.txtNewImage": "Նոր պատկեր ", "Common.Views.ListSettingsDialog.txtNone": "Ոչ մեկը", "Common.Views.ListSettingsDialog.txtOfText": "գրվածքի %", "Common.Views.ListSettingsDialog.txtSize": "Չափ", "Common.Views.ListSettingsDialog.txtStart": "Մեկնարկել", "Common.Views.ListSettingsDialog.txtSymbol": "Նշան", "Common.Views.ListSettingsDialog.txtTitle": "Ցանկի կարգավորումներ", "Common.Views.ListSettingsDialog.txtType": "Տեսակ", "Common.Views.OpenDialog.closeButtonText": "Փակել նիշքը", "Common.Views.OpenDialog.textInvalidRange": "Վանդակների անթույլատրելի ընդգրկույթ", "Common.Views.OpenDialog.textSelectData": "Ընտրեք տվյալներ", "Common.Views.OpenDialog.txtAdvanced": "Հավելյալ", "Common.Views.OpenDialog.txtColon": "երկկետ", "Common.Views.OpenDialog.txtComma": "Ստորակետ", "Common.Views.OpenDialog.txtDelimiter": "Բաժանիչ", "Common.Views.OpenDialog.txtDestData": "Ընտրեք, թե որտեղ պետք է տեղադրվեն տվյալները", "Common.Views.OpenDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "Common.Views.OpenDialog.txtEncoding": "Կոդավորում", "Common.Views.OpenDialog.txtIncorrectPwd": "Գաղտնաբառը սխալ է:", "Common.Views.OpenDialog.txtOpenFile": "Մուտքագրել գաղտնաբառ՝ ֆայլը բացելու համար", "Common.Views.OpenDialog.txtOther": "Այլ", "Common.Views.OpenDialog.txtPassword": "Գաղտնաբառ", "Common.Views.OpenDialog.txtPreview": "Նախադիտել", "Common.Views.OpenDialog.txtProtected": "Երբ գաղտնաբառը գրեք ու նիշքը բացեք, ընթացիկ գաղտնաբառը կվերակայվի։", "Common.Views.OpenDialog.txtSemicolon": "Կետ-ստորակետ", "Common.Views.OpenDialog.txtSpace": "Բացատ", "Common.Views.OpenDialog.txtTab": "Սյունատ", "Common.Views.OpenDialog.txtTitle": "Ընտրել %1 ընտրանքներ", "Common.Views.OpenDialog.txtTitleProtected": "Պաշտպանված ֆայլ", "Common.Views.PasswordDialog.txtDescription": "Դնել գաղտնաբառ՝ փաստաթուղթը պաշտպանելու համար։", "Common.Views.PasswordDialog.txtIncorrectPwd": "Հաստատման գաղտնաբառը նույնը չէ", "Common.Views.PasswordDialog.txtPassword": "Գաղտնաբառ", "Common.Views.PasswordDialog.txtRepeat": "Կրկնել գաղտնաբառը", "Common.Views.PasswordDialog.txtTitle": "Սահմանել գաղտնաբառ", "Common.Views.PasswordDialog.txtWarning": "Զգուշացում․ գաղտնաբառը կորցնելու կամ մոռանալու դեպքում այն ​​չի կարող վերականգնվել։Խնդրում ենք պահել այն ապահով տեղում:", "Common.Views.PluginDlg.textLoading": "Բեռնվում է", "Common.Views.Plugins.groupCaption": "Պլագիններ", "Common.Views.Plugins.strPlugins": "Պլագիններ", "Common.Views.Plugins.textClosePanel": "Փակել օժանդակ ծրագիրը", "Common.Views.Plugins.textLoading": "Բեռնվում է", "Common.Views.Plugins.textStart": "Մեկնարկ", "Common.Views.Plugins.textStop": "Կանգ", "Common.Views.Protection.hintAddPwd": "Գաղտնագրել գաղտնաբառով", "Common.Views.Protection.hintDelPwd": "Ջնջել գաղտնաբառը", "Common.Views.Protection.hintPwd": "Փոխել կամ ջնջել գաղտնաբառը", "Common.Views.Protection.hintSignature": "Դնել թվանշային ստորագրություն կամ ստորագրության տող", "Common.Views.Protection.txtAddPwd": "Դնել գաղտնաբառ", "Common.Views.Protection.txtChangePwd": "Փոխել գաղտնաբառը", "Common.Views.Protection.txtDeletePwd": "Ջնջել գաղտնաբառը", "Common.Views.Protection.txtEncrypt": "Գաղտնագրել", "Common.Views.Protection.txtInvisibleSignature": "Դնել թվանշային ստորագրություն", "Common.Views.Protection.txtSignature": "Ստորագրություն", "Common.Views.Protection.txtSignatureLine": "Դնել ստորագրության տող", "Common.Views.RenameDialog.textName": "Նիշքի անուն", "Common.Views.RenameDialog.txtInvalidName": "Նիշքի անունը չի կարող ունենալ հետևյալ գրանշանները՝ ", "Common.Views.ReviewChanges.hintNext": "Հաջորդ փոփոխությանը", "Common.Views.ReviewChanges.hintPrev": "Նախորդ փոփոխությանը", "Common.Views.ReviewChanges.strFast": "Արագ", "Common.Views.ReviewChanges.strFastDesc": "Համատեղ խմբագրում իրական ժամանակում։ Բոլոր փոփոխումներն ինքնաշխատ պահպանվում են։", "Common.Views.ReviewChanges.strStrict": "Խիստ", "Common.Views.ReviewChanges.strStrictDesc": "«Պահպանել» կոճակով համաժամեցրեք Ձեր և այլոց փոփոխումները։", "Common.Views.ReviewChanges.tipAcceptCurrent": "Ընդունել ընթացիկ փոփոխումը", "Common.Views.ReviewChanges.tipCoAuthMode": "Սահմանել համատեղ խմբագրման ռեժիմը", "Common.Views.ReviewChanges.tipCommentRem": "Ջնջել մեկնաբանությունները", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Հեռացրել ընթացիկ մեկնաբանությունները", "Common.Views.ReviewChanges.tipCommentResolve": "Լուծել մեկնաբանությունները", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Լուծել ընթացիկ մեկնաբանությունները", "Common.Views.ReviewChanges.tipHistory": "Ցուցադրել տարբերակի պատմությունը", "Common.Views.ReviewChanges.tipRejectCurrent": "Մերժել ընթացիկ փոփոխությունը", "Common.Views.ReviewChanges.tipReview": "Հետագծել փոփոխությունները", "Common.Views.ReviewChanges.tipReviewView": "Ընտրել ռեժիմը, որում ցանկանում եք, որ փոփոխությունները ցուցադրվեն", "Common.Views.ReviewChanges.tipSetDocLang": "Ընտրել փաստաթղթի լեզուն", "Common.Views.ReviewChanges.tipSetSpelling": "Ուղղագրության ստուգում", "Common.Views.ReviewChanges.tipSharing": "Կառավարել փաստաթղթի մատչման իրավունքները", "Common.Views.ReviewChanges.txtAccept": "Ընդունել", "Common.Views.ReviewChanges.txtAcceptAll": "Ընդունել բոլոր փոփոխումները", "Common.Views.ReviewChanges.txtAcceptChanges": "Ընդունել փոփոխումները", "Common.Views.ReviewChanges.txtAcceptCurrent": "Ընդունել ընթացիկ փոփոխումը", "Common.Views.ReviewChanges.txtChat": "Զրույց", "Common.Views.ReviewChanges.txtClose": "Փակել", "Common.Views.ReviewChanges.txtCoAuthMode": "Համախմբագրման աշխատակարգ", "Common.Views.ReviewChanges.txtCommentRemAll": "Ջնջել բոլոր մեկնաբանությունները", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Հեռացրել ընթացիկ մեկնաբանությունները", "Common.Views.ReviewChanges.txtCommentRemMy": "Հեռացնել իմ մեկնաբանությունը", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Հեռացնել իմ ընթացիկ մեկնաբանությունները", "Common.Views.ReviewChanges.txtCommentRemove": "Ջնջել", "Common.Views.ReviewChanges.txtCommentResolve": "Լուծել", "Common.Views.ReviewChanges.txtCommentResolveAll": "Լուծել բոլոր մեկնաբանությունները", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Լուծել ընթացիկ մեկնաբանությունները", "Common.Views.ReviewChanges.txtCommentResolveMy": "Լուծել իմ մեկնաբանությունները", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Լուծել իմ ընթացիկ մեկնաբանությունները", "Common.Views.ReviewChanges.txtDocLang": "Լեզու", "Common.Views.ReviewChanges.txtFinal": "Բոլոր փոփոխումներն ընդունված են (նախադիտում)", "Common.Views.ReviewChanges.txtFinalCap": "Վերջնական", "Common.Views.ReviewChanges.txtHistory": "Տարբերակի պատմություն", "Common.Views.ReviewChanges.txtMarkup": "Բոլոր փոփոխումները (խմբագրում)", "Common.Views.ReviewChanges.txtMarkupCap": "նշարկում", "Common.Views.ReviewChanges.txtNext": "Հաջորդ", "Common.Views.ReviewChanges.txtOriginal": "Բոլոր փոփոխումները մերժված են (նախադիտում)", "Common.Views.ReviewChanges.txtOriginalCap": "Բնօրինակ", "Common.Views.ReviewChanges.txtPrev": "Նախորդ", "Common.Views.ReviewChanges.txtReject": "Մերժել", "Common.Views.ReviewChanges.txtRejectAll": "Մերժել բոլոր փոփոխումները", "Common.Views.ReviewChanges.txtRejectChanges": "Մերժել փոփոխումները", "Common.Views.ReviewChanges.txtRejectCurrent": "Մերժել ընթացիկ փոփոխությունը", "Common.Views.ReviewChanges.txtSharing": "Համօգտագործում", "Common.Views.ReviewChanges.txtSpelling": "Ուղղագրության ստուգում", "Common.Views.ReviewChanges.txtTurnon": "Հետագծել փոփոխությունները", "Common.Views.ReviewChanges.txtView": "Ցուցադրման ձև", "Common.Views.ReviewPopover.textAdd": "Հավելել", "Common.Views.ReviewPopover.textAddReply": "Պատասխանել", "Common.Views.ReviewPopover.textCancel": "Չեղարկել", "Common.Views.ReviewPopover.textClose": "Փակել", "Common.Views.ReviewPopover.textEdit": "Լավ", "Common.Views.ReviewPopover.textEnterComment": "Այստեղ մուտքագրեք Ձեր մեկնաբանությունը", "Common.Views.ReviewPopover.textMention": "+հիշատակումը փաստաթուղթը հասանելի կդարձնի և էլ. նամակ կուղարկի", "Common.Views.ReviewPopover.textMentionNotify": "+նշումը օգտատիրոջը կտեղեկացնի էլ.փոստի միջոցով", "Common.Views.ReviewPopover.textOpenAgain": "Նորից բացել", "Common.Views.ReviewPopover.textReply": "Պատասխանել", "Common.Views.ReviewPopover.textResolve": "Լուծել", "Common.Views.ReviewPopover.textViewResolved": "Դուք մեկնաբանությունը վերաբացելու թույլտվություն չունեք", "Common.Views.ReviewPopover.txtDeleteTip": "Ջնջել", "Common.Views.ReviewPopover.txtEditTip": "Խմբագրել", "Common.Views.SaveAsDlg.textLoading": "Բեռնվում է", "Common.Views.SaveAsDlg.textTitle": "Պահպանման պանակ", "Common.Views.SearchPanel.textByColumns": "Սյունակներով", "Common.Views.SearchPanel.textByRows": "Տողերով", "Common.Views.SearchPanel.textCaseSensitive": "Հաշվի առնել տառաշարը", "Common.Views.SearchPanel.textCell": "Վանդակ", "Common.Views.SearchPanel.textCloseSearch": "Փակել որոնումը", "Common.Views.SearchPanel.textContentChanged": "Փաստաթուղթը փոխվել է", "Common.Views.SearchPanel.textFind": "Գտնել", "Common.Views.SearchPanel.textFindAndReplace": "Գտնել և փոխարինել", "Common.Views.SearchPanel.textFormula": "Բանաձև ", "Common.Views.SearchPanel.textFormulas": "Բանաձևեր", "Common.Views.SearchPanel.textItemEntireCell": "Վանդակների ամբողջ պարունակությունը", "Common.Views.SearchPanel.textLookIn": "Որոնման տարածք", "Common.Views.SearchPanel.textMatchUsingRegExp": "Համեմատել՝ օգտագործելով կանոնավոր արտահայտություններ", "Common.Views.SearchPanel.textName": "Անուն", "Common.Views.SearchPanel.textNoMatches": "Համընկնում չկա", "Common.Views.SearchPanel.textNoSearchResults": "Որոնման արդյունքներ չկան", "Common.Views.SearchPanel.textReplace": "Փոխարինել", "Common.Views.SearchPanel.textReplaceAll": "Փոխարինել բոլորը", "Common.Views.SearchPanel.textReplaceWith": "Փոխարինել հետևյալով", "Common.Views.SearchPanel.textSearch": "Որոնել", "Common.Views.SearchPanel.textSearchAgain": "{0}Կատարել նոր որոնում{1}ճշգրիտ արդյունքների համար:", "Common.Views.SearchPanel.textSearchHasStopped": "Որոնումը դադարեցվել է", "Common.Views.SearchPanel.textSearchOptions": "Որոնման ընտրանքներ", "Common.Views.SearchPanel.textSearchResults": "Որոնման արդյունքներ՝ {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "Ընտրեք Տվյալների տիրույթ", "Common.Views.SearchPanel.textSheet": "Թերթ", "Common.Views.SearchPanel.textSpecificRange": "Հատուկ միջակայք", "Common.Views.SearchPanel.textTooManyResults": "Այստեղ ցուցադրելու համար չափազանց շատ արդյունքներ կան", "Common.Views.SearchPanel.textValue": "Արժեք", "Common.Views.SearchPanel.textValues": "Արժեքներ", "Common.Views.SearchPanel.textWholeWords": "Միայն ամբողջական բառերը", "Common.Views.SearchPanel.textWithin": "Շրջանակներում", "Common.Views.SearchPanel.textWorkbook": "Աշխատագիրք", "Common.Views.SearchPanel.tipNextResult": "Հաջորդ արդյունքը", "Common.Views.SearchPanel.tipPreviousResult": "Նախորդ արդյունքը", "Common.Views.SelectFileDlg.textLoading": "Բեռնվում է", "Common.Views.SelectFileDlg.textTitle": "Ընտրել տվյալների աղբյուր", "Common.Views.SignDialog.textBold": "Թավատառ", "Common.Views.SignDialog.textCertificate": "Վկայական", "Common.Views.SignDialog.textChange": "Փոխել", "Common.Views.SignDialog.textInputName": "Մուտքագրել ստորագրողի անունը", "Common.Views.SignDialog.textItalic": "Շեղատառ", "Common.Views.SignDialog.textNameError": "Ստորագրողի անունը չպետք է դատարկ լինի:", "Common.Views.SignDialog.textPurpose": "Փաստաթղթի ստորագրման նպատակը", "Common.Views.SignDialog.textSelect": "Ընտրել", "Common.Views.SignDialog.textSelectImage": "Ընտրել պատկեր", "Common.Views.SignDialog.textSignature": "Ստորագրության տեսքը՝", "Common.Views.SignDialog.textTitle": "Ստորագրել փաստաթուղթը", "Common.Views.SignDialog.textUseImage": "կամ սեղմել «Ընտրել պատկերը»՝ նկարը որպես ստորագրություն օգտագործելու համար", "Common.Views.SignDialog.textValid": "Վավեր է %1-ից մինչև %2", "Common.Views.SignDialog.tipFontName": "Տառատեսակի անուն", "Common.Views.SignDialog.tipFontSize": "Տառատեսակի չափ", "Common.Views.SignSettingsDialog.textAllowComment": "Թույլ տալ ստորագրողին հավելել մեկնաբանություն ստորագրության պատուհանում", "Common.Views.SignSettingsDialog.textDefInstruction": "Նախքան այս փաստաթուղթը ստորագրելը, ստուգեք, որ Ձեր ստորագրած բովանդակությունը ճիշտ է:", "Common.Views.SignSettingsDialog.textInfoEmail": "Էլ․ հասցե", "Common.Views.SignSettingsDialog.textInfoName": "Անուն", "Common.Views.SignSettingsDialog.textInfoTitle": "Ստորագրողի անվանումը", "Common.Views.SignSettingsDialog.textInstructions": "Հրահանգներ ստորագրողի համար", "Common.Views.SignSettingsDialog.textShowDate": "Ցուցադրել ստորագրության ամսաթիվը ստորագրության տողում", "Common.Views.SignSettingsDialog.textTitle": "Ստորագրության տեղակայում", "Common.Views.SignSettingsDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "Common.Views.SymbolTableDialog.textCharacter": "Գրանշան", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX արժեքը", "Common.Views.SymbolTableDialog.textCopyright": "Հեղինակային իրավունքի նշան", "Common.Views.SymbolTableDialog.textDCQuote": "Փակող կրկնակի չակերտ", "Common.Views.SymbolTableDialog.textDOQuote": "Բացել կրկնակի փակագծերը", "Common.Views.SymbolTableDialog.textEllipsis": "Հորիզոնական էլիպսներ", "Common.Views.SymbolTableDialog.textEmDash": "M-աչափ գիծ", "Common.Views.SymbolTableDialog.textEmSpace": "M-աչափ բացատ", "Common.Views.SymbolTableDialog.textEnDash": "Միջին գծիկ", "Common.Views.SymbolTableDialog.textEnSpace": "Միջին բացատ", "Common.Views.SymbolTableDialog.textFont": "Տառատեսակ ", "Common.Views.SymbolTableDialog.textNBHyphen": "Չընդատվող գծիկ", "Common.Views.SymbolTableDialog.textNBSpace": "Առանց ընդմիջման տարածություն", "Common.Views.SymbolTableDialog.textPilcrow": "Պարբերության նշան", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 M-աչափ բացատ", "Common.Views.SymbolTableDialog.textRange": "Ընդգրկույթ", "Common.Views.SymbolTableDialog.textRecent": "Վերջերս օգտագործված նշաններ", "Common.Views.SymbolTableDialog.textRegistered": "Գրանցված նշան", "Common.Views.SymbolTableDialog.textSCQuote": "Փակող միագիծ չակերտ", "Common.Views.SymbolTableDialog.textSection": "Բաժնի նշան", "Common.Views.SymbolTableDialog.textShortcut": "Դյուրանցման ստեղն", "Common.Views.SymbolTableDialog.textSHyphen": "Փափուկ տողադարձիչ", "Common.Views.SymbolTableDialog.textSOQuote": "Բացել մեկ փակագիծ", "Common.Views.SymbolTableDialog.textSpecial": "Հատուկ գրանշաններ", "Common.Views.SymbolTableDialog.textSymbols": "Նշաններ", "Common.Views.SymbolTableDialog.textTitle": "Նշան", "Common.Views.SymbolTableDialog.textTradeMark": "Ապրանքանիշի նշան", "Common.Views.UserNameDialog.textDontShow": "Այլևս չհարցնել", "Common.Views.UserNameDialog.textLabel": "Պիտակ։", "Common.Views.UserNameDialog.textLabelError": "Պիտակը չպետք է դատարկ լինի", "SSE.Controllers.DataTab.strSheet": "Թերթ", "SSE.Controllers.DataTab.textAddExternalData": "Արտաքին աղբյուրի հղումն ավելացվել է։ Նման հղումները կարող եք թարմացնել «Տվյալներ» ներդիրում:", "SSE.Controllers.DataTab.textColumns": "Սյունակներ", "SSE.Controllers.DataTab.textDontUpdate": "Մի թարմացրեք", "SSE.Controllers.DataTab.textEmptyUrl": "Դուք պետք է նշեք URL-ը", "SSE.Controllers.DataTab.textRows": "Տողեր", "SSE.Controllers.DataTab.textUpdate": "Արդիացնել", "SSE.Controllers.DataTab.textWizard": "Տեքստ սյունակներ", "SSE.Controllers.DataTab.txtDataValidation": "Տվյալների վավերացում", "SSE.Controllers.DataTab.txtErrorExternalLink": "Սխալ՝ արդիացումը ձախողվեց", "SSE.Controllers.DataTab.txtExpand": "Ընդարձակել", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Ընտրության կողքին գտնվող տվյալները չեն հեռացվի: Ցանկանու՞մ եք ընդլայնել ընտրությունը՝ ներառելով հարակից տվյալները, թե՞ շարունակել միայն ներկայումս ընտրված բջիջներով:", "SSE.Controllers.DataTab.txtExtendDataValidation": "Ընտրությունը պարունակում է որոշ բջիջներ՝ առանց տվյալների վավերացման կարգավորումների:<br>Ցանկանու՞մ եք ընդլայնել տվյալների վավերացումը այս բջիջների վրա:", "SSE.Controllers.DataTab.txtImportWizard": "Տեքստի ներմուծման հրաշագործ", "SSE.Controllers.DataTab.txtRemDuplicates": "Հեռացրեք կրկնօրինակները", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Ընտրությունը պարունակում է մեկից ավելի վավերացման տեսակներ:<br>Ջնջե՞լ ընթացիկ կարգավորումները և շարունակե՞լ:", "SSE.Controllers.DataTab.txtRemSelected": "Հեռացնել ընտրվածում", "SSE.Controllers.DataTab.txtUrlTitle": "Տեղադրել URL-ի տվյալների", "SSE.Controllers.DataTab.warnUpdateExternalData": "Այս աշխատանքային գիրքը պարունակում է հղումներ դեպի մեկ կամ մի քանի արտաքին աղբյուրներ, որոնք կարող են անապահով  լինել:<br> Եթե ​​վստահում եք հղումներին, թարմացրեք դրանք՝ վերջին տվյալները ստանալու համար:", "SSE.Controllers.DocumentHolder.alignmentText": "Հավասարեցում", "SSE.Controllers.DocumentHolder.centerText": "Կենտրոն", "SSE.Controllers.DocumentHolder.deleteColumnText": "Ջնջել սյունակ", "SSE.Controllers.DocumentHolder.deleteRowText": "Ջնջել տողը", "SSE.Controllers.DocumentHolder.deleteText": "Ջնջել", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Հղման հղումը գոյություն չունի:Խնդրում ենք ուղղել հղումը կամ ջնջել այն։", "SSE.Controllers.DocumentHolder.guestText": "Հյուր", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Սյունակ ձախից", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Սյունակ աջից", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Տող վերևում", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Տող ներքևում", "SSE.Controllers.DocumentHolder.insertText": "Զետեղել", "SSE.Controllers.DocumentHolder.leftText": "Ձախ", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Զգուշացում", "SSE.Controllers.DocumentHolder.rightText": "Աջ", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Ինքնաշտկման ընտրանքներ", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Սյունակի լայնությունը {0} խորհրդանիշ ({1} պիքսել)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Տողի բարձրությունը {0} միավոր ({1} պիքսել)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Սեղմեք հղման վրա՝ այն բացելու համար կամ սեղմեք և պահեք մկնիկի կոճակը՝ վանդակն ընտրելու համար:", "SSE.Controllers.DocumentHolder.textInsertLeft": "Տեղադրել ձախ", "SSE.Controllers.DocumentHolder.textInsertTop": "Տեղադրել վերևը", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Կպցնել հատուկ", "SSE.Controllers.DocumentHolder.textStopExpand": "Դադարեցրեք աղյուսակների ավտոմատ ընդլայնումը", "SSE.Controllers.DocumentHolder.textSym": "սիմ", "SSE.Controllers.DocumentHolder.tipIsLocked": "Այս տարրը խմբագրվում է մեկ այլ օգտվողի կողմից:", "SSE.Controllers.DocumentHolder.txtAboveAve": "Միջինից բարձր", "SSE.Controllers.DocumentHolder.txtAddBottom": "Դնել ստորին եզրագիծ", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Դնել կոտորակի գիծ", "SSE.Controllers.DocumentHolder.txtAddHor": "Դնել հորիզոնական գիծ", "SSE.Controllers.DocumentHolder.txtAddLB": "Դնել ձախ ստորին եզրագիծ", "SSE.Controllers.DocumentHolder.txtAddLeft": "Դնել ձախ եզրագիծ", "SSE.Controllers.DocumentHolder.txtAddLT": "Դնել ձախ վերին եզրագիծ", "SSE.Controllers.DocumentHolder.txtAddRight": "Դնել աջ եզրագիծ", "SSE.Controllers.DocumentHolder.txtAddTop": "Դնել վերին եզրագիծ", "SSE.Controllers.DocumentHolder.txtAddVer": "Դնել ուղղահայաց գիծ", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Հավասարեցում ըստ նիշի", "SSE.Controllers.DocumentHolder.txtAll": "(Բոլորը)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Վերադարձնում է աղյուսակի ողջ բովանդակությունը, կամ հատկորոշված աղյուսակի սյունակները` ներառյալ սյունակների էջագլուխները, տվյալները և միագումարի տողերը:", "SSE.Controllers.DocumentHolder.txtAnd": "և", "SSE.Controllers.DocumentHolder.txtBegins": "Սկսվում է ", "SSE.Controllers.DocumentHolder.txtBelowAve": "Միջինից ցածր", "SSE.Controllers.DocumentHolder.txtBlanks": "(Դատարկներ)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Եզրագծի հատկություններ", "SSE.Controllers.DocumentHolder.txtBottom": "Ներքև", "SSE.Controllers.DocumentHolder.txtColumn": "Սյունակ", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Սյունակի հավասարեցում", "SSE.Controllers.DocumentHolder.txtContains": "Պարունակում է", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Հղումը պատճենված է clipboard-ին", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Վերադարձնում էաղյուսակի կամ աղյուսակի հատկորոշված սյունակների տվյալների բջիջները", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Նվազեցնել արգումենտի չափը", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Ջնջել արգումենտը", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Ջնջել ձեռնադիր ընդհատումը", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Ջնջել ներդրված նիշերը", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Ջնջել ներդրված նիշերն ու բաժանիչները", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Ջնջել հավասարումը", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Ջնջել նիշ", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Ջնջել արմատը", "SSE.Controllers.DocumentHolder.txtEnds": "Ավարտվում է ", "SSE.Controllers.DocumentHolder.txtEquals": "Հավասար է", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Հավասար է վանդակների գույնին", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Հավասար է տառատեսակի գույնին", "SSE.Controllers.DocumentHolder.txtExpand": "Ընդարձակել և տեսակավորել", "SSE.Controllers.DocumentHolder.txtExpandSort": "Ընտրության կողքին գտնվող տվյալները չեն տեսակավորվի:Ցանկանու՞մ եք ընդլայնել ընտրությունը՝ ներառելով հարակից տվյալները, թե՞ շարունակել տեսակավորել միայն ներկայումս ընտրված վանդակները:", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Ներքև", "SSE.Controllers.DocumentHolder.txtFilterTop": "Վերև", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Փոխել հորիզոնական կոտորակի", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Փոխել շեղ կոտորակի", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Փոխել շեղջված կոտորակի ", "SSE.Controllers.DocumentHolder.txtGreater": "Ավելի մեծ քան", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Ավելի մեծ կամ հավասար", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Նիշը տեքստի վերևում", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Նիշքը տեքստի տակ", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Վերադարձնում է սյունակների էջագլուխները` աղյուսակի կամ աղյուսակի հատկորոշված սյունակների համար", "SSE.Controllers.DocumentHolder.txtHeight": "Բարձրություն", "SSE.Controllers.DocumentHolder.txtHideBottom": "Թաքցնել ստորին Եզրագիծը", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Թաքցնել ստորին սահմանափակումը", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Թաքցնել փակող փակագիծը", "SSE.Controllers.DocumentHolder.txtHideDegree": "Թաքցնել աստիճանը", "SSE.Controllers.DocumentHolder.txtHideHor": "Թաքցնել հորիզոնական գիծը", "SSE.Controllers.DocumentHolder.txtHideLB": "Թաքցնել ձախ ներքևի տողը", "SSE.Controllers.DocumentHolder.txtHideLeft": "Թաքցնել ձախ եզրագիծը", "SSE.Controllers.DocumentHolder.txtHideLT": "Թաքցնել ձախ վերին տողը", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Թաքցնել բացման փակագիծը", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Թաքցնել տեղապահը", "SSE.Controllers.DocumentHolder.txtHideRight": "Թաքցնել աջ եզրագիծը", "SSE.Controllers.DocumentHolder.txtHideTop": "Թաքցնել վերին եզրագիծը", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Թաքցնել վերին սահմանը", "SSE.Controllers.DocumentHolder.txtHideVer": "Թաքցնել ուղղահայաց գիծը", "SSE.Controllers.DocumentHolder.txtImportWizard": "Տեքստի ներմուծման հրաշագործ", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Մեծացնել արգումենտի չափը", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Զետեղել արգումենտ հետո՝", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Զետեղել արգումենտ առաջ՝", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Զետեղել ձեռքի ընդհատ", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Հավասարումը դնել հետո՝", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Հավասարումը դնել առաջ՝", "SSE.Controllers.DocumentHolder.txtItems": "Նյութեր", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Պահպանել միայն տեքստը", "SSE.Controllers.DocumentHolder.txtLess": "Ավելի քիչ քան ", "SSE.Controllers.DocumentHolder.txtLessEquals": "Պակաս կամ հավասար", "SSE.Controllers.DocumentHolder.txtLimitChange": "Փոխել սահմանների տեղադրությունը", "SSE.Controllers.DocumentHolder.txtLimitOver": "Տեքստի սահմանափակում", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Սահմանափակում տեքստի տակ", "SSE.Controllers.DocumentHolder.txtLockSort": "Տվյալները գտնվել են ձեր ընտրության կողքին, բայց դուք չունեք բավարար թույլտվություններ այդ վանդակները փոխելու համար:<br>Ցանկանու՞մ եք շարունակել ընթացիկ ընտրությունը:", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Փոփոխել փակագծերի չափը՝ ըստ արգումենտի բարձրության", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Մատրիցային հավասարեցում", "SSE.Controllers.DocumentHolder.txtNoChoices": "Վանդակը լցնելու համար ընտրություն չկա:<br>Փոխարինման համար կարող են ընտրվել միայն սյունակից տեքստային արժեքները:", "SSE.Controllers.DocumentHolder.txtNotBegins": "Չի սկսվում հետևյալով", "SSE.Controllers.DocumentHolder.txtNotContains": "Չի պարունակում", "SSE.Controllers.DocumentHolder.txtNotEnds": "չի ավարտվում հետևյալով", "SSE.Controllers.DocumentHolder.txtNotEquals": "Հավասար չէ", "SSE.Controllers.DocumentHolder.txtOr": "կամ", "SSE.Controllers.DocumentHolder.txtOverbar": "Տեքստի վրա գիծ", "SSE.Controllers.DocumentHolder.txtPaste": "Փակցնել", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Բանաձև առանց սահմանների", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Բանաձև + սյունակի լայնություն", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Նպատակակետի ձևավորում", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Կպցնել միայն ֆորմատավորում", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Բանաձև + թվի ձևաչափ", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Կպցնել միայն բանաձև", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Բանաձև + բոլոր ձևաչափումները", "SSE.Controllers.DocumentHolder.txtPasteLink": "Կպցնել հղումը", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Կապված նկար", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Միավորել պայմանական ձևաչափումը", "SSE.Controllers.DocumentHolder.txtPastePicture": "Նկար", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Աղբյուրի ձևաչափում", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Փոխադրել", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Արժեք + բոլոր ձևաչափումները", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Արժեք + թվի ձևաչափ", "SSE.Controllers.DocumentHolder.txtPasteValues": "Տեղադրեք միայն արժեքը", "SSE.Controllers.DocumentHolder.txtPercent": "Տոկոսային", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Կրկնել աղյուսակի ավտոմատ ընդլայնումը", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Հեռացնել կոտորակի գիծը", "SSE.Controllers.DocumentHolder.txtRemLimit": "Հեռացնել սահմանը", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Ջնջել շեշտադրման նշանը", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Ջնջել վահանակը", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Ցանկանու՞մ եք հեռացնել այս ստորագրությունը:<br>Այն հնարավոր չէ չեղարկել:", "SSE.Controllers.DocumentHolder.txtRemScripts": "Հեռացնել գրերը", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Հեռացնել վարգիրը", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Հեռացնել վերևի գիծը", "SSE.Controllers.DocumentHolder.txtRowHeight": "Շարքի բարձրությունը", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Սկրիպտներ տեքստից հետո", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Սկրիպտներ տեքստից առաջ", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Ցուցադրել ստորին սահմանը", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Ցուցադրել փակվող փակագիծը", "SSE.Controllers.DocumentHolder.txtShowDegree": "Ցուցադրել աստիճանը", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Ցուցադրել բացող ուղղանկյուն փակագիծը", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Ցուցադրել տեղապահը", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Ցուցադրել առավելագույն սահմանաչափը", "SSE.Controllers.DocumentHolder.txtSorting": "Տեսակավորում", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ընտրված տեսակավորում", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Ձգվող փակագծեր", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Ընտրեք նշված սյունակի միայն այս տողը", "SSE.Controllers.DocumentHolder.txtTop": "Վերև", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Վերադարձնում է աղյուսակի կամ աղյուսակի հատկորոշված սյունակների միագումարի տողերը", "SSE.Controllers.DocumentHolder.txtUnderbar": "Տեքստի տակ գիծ", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Չեղարկել աղյուսակի ավտոմատ ընդլայնումը", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Օգտագործեք տեքստի ներմուծման մոգ", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Այս հղմանը հետևելը կարող է վնասել ձեր սարքավորումն ու տվյալները:<br>Վստա՞հ եք, որ ցանկանում եք շարունակել:", "SSE.Controllers.DocumentHolder.txtWidth": "Լայնք", "SSE.Controllers.FormulaDialog.sCategoryAll": "Բոլորը", "SSE.Controllers.FormulaDialog.sCategoryCube": "Խորանարդ", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Տվյալների բազա", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Ամսաթիվ և ժամ", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Ճարտարագիտական", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Ֆինանսական", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Տեղեկատվություն", "SSE.Controllers.FormulaDialog.sCategoryLast10": "Վերջին 10 օգտագործվածներ", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Տրամաբանական", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Որոնում և հղումներ", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Մաթեմ. և եռանկյունաչափություն", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Վիճակագրական", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Տեքստ և տվյալներ", "SSE.Controllers.LeftMenu.newDocumentTitle": "Անանուն աղյուսակաթերթ", "SSE.Controllers.LeftMenu.textByColumns": "Սյունակներով", "SSE.Controllers.LeftMenu.textByRows": "Տողերով", "SSE.Controllers.LeftMenu.textFormulas": "Բանաձևեր", "SSE.Controllers.LeftMenu.textItemEntireCell": "Վանդակների ամբողջ պարունակությունը", "SSE.Controllers.LeftMenu.textLoadHistory": "Տարբերակների պատմության բեռնում...", "SSE.Controllers.LeftMenu.textLookin": "Որոնման տարածք", "SSE.Controllers.LeftMenu.textNoTextFound": "Ձեր փնտրած տվյալները չեն գտնվել:Խնդրում ենք կարգաբերել Ձեր որոնման ընտրանքները:", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Փոխարինումը կատարված է։{0} դեպք բաց է թողնվել:", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Որոնողական աշխատանքները կատարվել են։ Փոխարինված դեպքերը՝ {0}", "SSE.Controllers.LeftMenu.textSearch": "Որոնել", "SSE.Controllers.LeftMenu.textSheet": "Թերթ", "SSE.Controllers.LeftMenu.textValues": "Արժեքներ", "SSE.Controllers.LeftMenu.textWarning": "Զգուշացում", "SSE.Controllers.LeftMenu.textWithin": "Շրջանակներում", "SSE.Controllers.LeftMenu.textWorkbook": "Աշխատագիրք", "SSE.Controllers.LeftMenu.txtUntitled": "Անանուն", "SSE.Controllers.LeftMenu.warnDownloadAs": "Եթե շարունակեք պահպանումն այս ձևաչափով, բոլոր հատկությունները՝ տեքստից բացի, կկորչեն։<br>Վստա՞հ եք, որ ցանկանում եք շարունակել:", "SSE.Controllers.Main.confirmAddCellWatches": "Այս գործողությամբ կավելացվեն {0} բջջային ժամացույցներ:<br>Ցանկանու՞մ եք շարունակել։", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Այս գործողությամբ կավելացվեն միայն {0} բջջային ժամացույցներ՝ ըստ հիշողության պահպանման պատճառի:<br>Ցանկանու՞մ եք շարունակել։", "SSE.Controllers.Main.confirmMaxChangesSize": "Գործողությունների չափը գերազանցում է Ձեր սերվերի համար սահմանված սահմանափակումը:<br>Սեղմեք «Հետարկել»՝ Ձեր վերջին գործողությունը չեղարկելու համար կամ սեղմեք «Շարունակել»՝ գործողությունը տեղում պահելու համար (Դուք պետք է ներբեռնեք ֆայլը կամ պատճենեք դրա բովանդակությունը՝ համոզվելու համար, որ ոչինչ կորած չէ):", "SSE.Controllers.Main.confirmMoveCellRange": "Նպատակային բջիջների տիրույթը կարող է պարունակել տվյալներ: Շարունակե՞լ գործողությունը:", "SSE.Controllers.Main.confirmPutMergeRange": "Աղբյուրի տվյալները պարունակում էին միավորված բջիջներ:<br>Դրանք ապամիաձուլվել էին նախքան աղյուսակում տեղադրվելը:", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Վերնագրի տողի բանաձևերը կհեռացվեն և կվերածվեն ստատիկ տեքստի:<br>Ցանկանու՞մ եք շարունակել։", "SSE.Controllers.Main.convertationTimeoutText": "Փոխարկման սպասման ժամանակը սպառվել է։", "SSE.Controllers.Main.criticalErrorExtText": "Սեղմեք «լավ» ու վերադարձեք փաստաթղթերի ցանկին", "SSE.Controllers.Main.criticalErrorTitle": "Սխալ", "SSE.Controllers.Main.downloadErrorText": "Ներբեռնումը ձախողվեց։", "SSE.Controllers.Main.downloadTextText": "Աղյուսակաթերթի ներբեռնում...", "SSE.Controllers.Main.downloadTitleText": "Աղյուսակաթերթի ներբեռնում", "SSE.Controllers.Main.errNoDuplicates": "Կրկնվող արժեքներ չեն գտնվել:", "SSE.Controllers.Main.errorAccessDeny": "Դուք փորձում եք կատարել գործողություն, որի իրավունքը չունեք։<br>Դիմեք փաստաթղթերի ձեր սպասարկիչի վարիչին։", "SSE.Controllers.Main.errorArgsRange": "Բանաձևում սխալ կա՝<br>արգումենտի սխալ ընդգրկույթ։", "SSE.Controllers.Main.errorAutoFilterChange": "Գործողությունը չի թույլատրվում, քանի որ փորձ է արվում թերթում տեղաշարժել աղյուսակի վանդակներ։", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Ընտրված վանդակների համար հնարավոր չեղավ կատարել գործողությունը, քանի որ չեք կարող տեղափոխել աղյուսակի մի մասը։<br>Ընտրեք տվյալների ուրիշ ընդգրկույթ՝ ամբողջ աղյուսակը տեղափոխելու համար, և նորից փորձեք։", "SSE.Controllers.Main.errorAutoFilterDataRange": "Ընտրված վանդակների համար հնարավոր չեղավ կատարել գործողությունը։<br>Ընտրրեք տվյալների միատեսակ ընդգրկույթ, որը եղածից տարբեր է, և նորից փորձեք։", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Գործողությունը չի կարող կատարվել, քանի որ տարածքն ունի զտված վանդակներ։<br>Ապաթաքցրեք զտիչով թաքցված տարրերը և նորից փորձեք։", "SSE.Controllers.Main.errorBadImageUrl": "Նկարի URL-ը սխալ է", "SSE.Controllers.Main.errorCannotPasteImg": "Մենք չենք կարող տեղադրել այս պատկերը սեղմատախտակից, բայց դուք կարող եք պահել այն ձեր սարքում և տեղադրել այնտեղից, կամ կարող եք պատճենել պատկերն առանց տեքստի և տեղադրել այն աղյուսակում:", "SSE.Controllers.Main.errorCannotUngroup": "Հնարավոր չէ ապախմբավորել: Ուրվագիծ սկսելու համար ընտրեք մանրամասների տողերը կամ սյունակները և խմբավորեք դրանք:", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Դուք չեք կարող օգտագործել այս հրամանը պաշտպանված թերթիկի վրա:Այս հրամանն օգտագործելու համար պաշտպանազերծեք թերթիկը:<br>Ձեզանից կարող է պահանջվել մուտքագրել գաղտնաբառ:", "SSE.Controllers.Main.errorChangeArray": "Չեք կարող փոխել զանգվածի մի մասը։", "SSE.Controllers.Main.errorChangeFilteredRange": "Սա կփոխի ձեր աշխատաթերթի զտված ընդգրկույթը:<br>Այս առաջադրանքն ավարտելու համար խնդրում ենք հեռացնել Ավտոզտիչները:", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Բջիջը կամ գծապատկերը, որը փորձում եք փոխել, գտնվում է պաշտպանված թերթիկի վրա:<br>Փոփոխություն անելու համար հանեք թերթի պաշտպանությունը: Ձեզանից կարող է պահանջվել մուտքագրել գաղտնաբառ:", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Սպասարկիչի հետ կապն ընդհատվել է։ Փաստաթուղթն այս պահին չի կարող խմբագրվել։", "SSE.Controllers.Main.errorConnectToServer": "Փաստաթուղթը չպահպանվեց։ Ստուգեք միացման կարգավորումները կամ կապ հաստատեք ձեր վարիչի հետ։<br>Երբ սեղմեք «Լավ» կոճակը, ձեզ կառաջարկվի փաստաթուղթը ներբեռնել։", "SSE.Controllers.Main.errorConvertXml": "Ֆայլն ունի չաջակցվող ձևաչափ:<br> Միայն XML Աղյուսակաթերթ 2003 ձևաչափը կարող է օգտագործվել:", "SSE.Controllers.Main.errorCopyMultiselectArea": "Այս հրահանգը չի կարող կիրառվել բազմակի ընտրման դեպքում։<br>Ընտրեք միայն մեկ ընդգրկույթ և նորից փորձեք։", "SSE.Controllers.Main.errorCountArg": "Բանաձևում սխալ կա՝<br>արգումենտների սխալ քանակ։", "SSE.Controllers.Main.errorCountArgExceed": "Բանաձևում սխալ կա։<br>Արգումենտների քանակը գերազանցվել է։", "SSE.Controllers.Main.errorCreateDefName": "Անվանված առկա ընդգրկույթներն այս պահին չեն կարող խմբագրվել և նորերը չեն կարող ստեղծվել,<br>քանի որ դրանց մի մասը խմբագրվում է։", "SSE.Controllers.Main.errorDatabaseConnection": "Արտաքին սխալ։<br>Տվյալաշտեմարանին միանալու սխալ։ Եթե այն շարունակվի, դիմեք աջակցության ծառայությանը։", "SSE.Controllers.Main.errorDataEncrypted": "Ընդունվել են գաղտնագրված փոփոխությունները. դրանք չեն կարող վերծանվել։", "SSE.Controllers.Main.errorDataRange": "Տվյալների սխալ ընդգրկույթ։", "SSE.Controllers.Main.errorDataValidate": "Ձեր մուտքագրած արժեքը վավեր չէ:Օգտագործողը ունի սահմանափակ արժեքներ, որոնք կարող են մուտքագրվել այս վանդակում:", "SSE.Controllers.Main.errorDefaultMessage": "Սխալի կոդ՝ %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Դուք փորձում եք ջնջել մի սյունակ, որը պարունակում է կողպված բջիջ: Կողպված բջիջները չեն կարող ջնջվել, քանի դեռ աշխատանքային թերթը պաշտպանված է:<br>Կողպված բջիջը ջնջելու համար չպաշտպանեք թերթը: Ձեզանից կարող է պահանջվել մուտքագրել գաղտնաբառ:", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Դուք փորձում եք ջնջել մի տող, որը պարունակում է կողպված բջիջ: Կողպված բջիջները չեն կարող ջնջվել, քանի դեռ աշխատանքային թերթը պաշտպանված է:<br>Կողպված բջիջը ջնջելու համար չպաշտպանեք թերթը: Ձեզանից կարող է պահանջվել մուտքագրել գաղտնաբառ:", "SSE.Controllers.Main.errorDirectUrl": "Խնդրում ենք ստուգել փաստաթղթի հղումը:<br> Այս հղումը պետք է լինի ուղիղ հղում դեպի ներբեռնելու ֆայլը:", "SSE.Controllers.Main.errorEditingDownloadas": "Փաստաթղթի հետ աշխատանքի ընթացքում տեղի ունեցավ սխալ։<br>«Ներբեռնել որպես» հրամանով պահպանեք նիշքի պահուստային պատճենը Ձեր համակարգչի կոշտ սկավառակում։", "SSE.Controllers.Main.errorEditingSaveas": "Փաստաթղթի հետ աշխատանքի ընթացքում տեղի ունեցավ սխալ։<br>«Պահպանել որպես...» հրամանով պահպանեք նիշքի պահուստային պատճենը Ձեր համակարգչի կոշտ սկավառակում։", "SSE.Controllers.Main.errorEditView": "Գոյություն ունեցող թերթերի տեսքը հնարավոր չէ խմբագրել, իսկ նորերը չեն կարող ստեղծվել այս պահին, քանի որ դրանցից մի քանիսը խմբագրվում են:", "SSE.Controllers.Main.errorEmailClient": "Էլփոստի հաճախորդ չի գտնվել:", "SSE.Controllers.Main.errorFilePassProtect": "Նիշքն ունի գաղտնաբառ և չի կարող բացվել։", "SSE.Controllers.Main.errorFileRequest": "Արտաքին սխալ։<br>Նիշքի հարցման սխալ։ Եթե այն կրկնվի, դիմեք աջակցության ծառայությանը։", "SSE.Controllers.Main.errorFileSizeExceed": "Ֆայլի չափը գերազանցում է ձեր սերվերի համար սահմանված սահմանափակումը:<br> Մանրամասների համար խնդրում ենք կապվել Ձեր փաստաթղթերի սերվերի ադմինիստրատորի հետ:", "SSE.Controllers.Main.errorFileVKey": "Արտաքին սխալ։<br>Անվտանգության սխալ բանալի։ Եթե սխալը շարունակվի, դիմեք աջակցության ծառայությանը։", "SSE.Controllers.Main.errorFillRange": "Չհաջողվեց լրացնել վանդակների ընտրվածքը։<br>Բոլոր միավորված վանդակները պետք է նույնաչափ լինեն։", "SSE.Controllers.Main.errorForceSave": "Փաստաթղթի պահպանման ժամանակ տեղի ունեցավ սխալ։ «Ներբեռնել որպես» հրամանով պահպանեք նիշքը Ձեր համակարգչի կոշտ սկավառակում կամ ավելի ուշ նորից փորձեք։", "SSE.Controllers.Main.errorFormulaName": "Բանաձևում սխալ կա՝<br>բանաձևի սխալ անուն։", "SSE.Controllers.Main.errorFormulaParsing": "Ներքին սխալ՝ բանաձևը վերլուծելիս։", "SSE.Controllers.Main.errorFrmlMaxLength": "Բանաձևի չափը գերազանցում է թույլատրելի 8192 նիշը։<br>Խմբագրեք այն և նորից փորձեք։", "SSE.Controllers.Main.errorFrmlMaxReference": "Չեք կարող մտցնել այս բանաձևը, քանի որ այն ունի շատ արժեքներ,<br>վանդակների հղումներ և/կամ անուններ։", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Բանաձևի տեքստային արժեքները 255-ից ավելի շատ նիշ չպիտի ունենան։<br>Կիրառեք ՇՂԹԱՅԱԿՑԵԼ ֆունկցիան կամ շղթայակցման օպերատորը (&)։", "SSE.Controllers.Main.errorFrmlWrongReferences": "Ֆունկցիան հղում է գոյություն չունեցող աղյուսակաթերթի։<br>Ստուգեք տվյալներն ու նորից փորձեք։", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Գործողությունը չի կարող ավարտվել ընտրված բջիջների տիրույթի համար:<br>Ընտրեք ընդգրկույթ, որպեսզի աղյուսակի առաջին տողը լինի նույն տողում<br>և արդյունքում աղյուսակը համընկնի ընթացիկի հետ:", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Գործողությունը չի կարող ավարտվել ընտրված բջիջների տիրույթի համար:<br>Ընտրեք ընդգրկույթ, որը չի ներառում այլ աղյուսակներ:", "SSE.Controllers.Main.errorInconsistentExt": "Ֆայլը բացելիս սխալ է տեղի ունեցել:<br>Ֆայլի բովանդակությունը չի համապատասխանում ֆայլի ընդլայնմանը:", "SSE.Controllers.Main.errorInconsistentExtDocx": "Ֆայլը բացելիս սխալ է տեղի ունեցել:<br>Ֆայլի բովանդակությունը համապատասխանում է տեքստային փաստաթղթերին (օրինակ՝ docx), սակայն ֆայլն ունի անհամապատասխան ընդլայնում՝ %1:", "SSE.Controllers.Main.errorInconsistentExtPdf": "Ֆայլը բացելիս սխալ է տեղի ունեցել:Ֆայլի բովանդակությունը համապատասխանում է հետևյալ ձևաչափերից մեկին՝pdf/djvu/xps/oxps,բայց ֆայլն ունի անհամապատասխան ընդլայնում. %1:", "SSE.Controllers.Main.errorInconsistentExtPptx": "Ֆայլը բացելիս սխալ է տեղի ունեցել:<br>Ֆայլի բովանդակությունը համապատասխանում է ներկայացումներին (օրինակ՝ pptx), սակայն ֆայլն ունի անհամապատասխան ընդլայնում. %1:", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Ֆայլը բացելիս սխալ է տեղի ունեցել:<br>Ֆայլի բովանդակությունը համապատասխանում է աղյուսակներին (օր. xlsx), սակայն ֆայլն ունի անհամապատասխան ընդլայնում. %1:", "SSE.Controllers.Main.errorInvalidRef": "Մուտքագրեք ընտրվածքի համար ճիշտ անուն կամ անցման համար թույլատրելի հղում։", "SSE.Controllers.Main.errorKeyEncrypt": "Բանալու անհայտ նկարագրիչ", "SSE.Controllers.Main.errorKeyExpire": "Բանալու նկարագրիչի ժամկետը սպառվել է", "SSE.Controllers.Main.errorLabledColumnsPivot": "Առանցքային աղյուսակ ստեղծելու համար օգտագործեք տվյալներ, որոնք կազմակերպված են որպես ցանկ՝ պիտակավորված սյունակներով:", "SSE.Controllers.Main.errorLoadingFont": "Տառատեսակները բեռնված չեն:<br>Խնդրում ենք կապվել ձեր փաստաթղթերի սերվերի ադմինիստրատորի հետ:", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Տեղադրության կամ տվյալների տիրույթի հղումը վավեր չէ:", "SSE.Controllers.Main.errorLockedAll": "Գործողությունը չկատարվեց, քանի որ մեկ այլ օգտատեր կողպել է աղյուսակաթերթը։", "SSE.Controllers.Main.errorLockedCellPivot": "Դուք չեք կարող փոխել տվյալները առանցքային աղյուսակի ներսում:", "SSE.Controllers.Main.errorLockedWorksheetRename": "Թերթն այս պահին չի կարող վերանվանվել, քանի որ վերանվանվում է մի ուրիշ օգտատիրոջ կողմից։", "SSE.Controllers.Main.errorMaxPoints": "Գծապատկերի շարքի կետերի առավելագույն քանակը 4096 է։", "SSE.Controllers.Main.errorMoveRange": "Միավորված վանդակի մի մասը հնարավոր չէ փոխել", "SSE.Controllers.Main.errorMoveSlicerError": "Աղյուսակ կտրատող սարքերը չեն կարող պատճենվել աշխատանքային գրքույկից մյուսը:<br>Փորձեք նորից՝ ընտրելով ամբողջ աղյուսակը և կտրատողները:", "SSE.Controllers.Main.errorMultiCellFormula": "Զանգվածի բազմավանդակ բանաձևերը թույլատրված չեն աղյուսակներում։", "SSE.Controllers.Main.errorNoDataToParse": "Ոչ մի տվյալ չի ընտրվել վերլուծելու համար:", "SSE.Controllers.Main.errorOpenWarning": "Նիշքի բանաձևերից մեկն ուներ<br>նիշերի անթույլատրելի մեծ քանակ, ուստի ջնջվեց։", "SSE.Controllers.Main.errorOperandExpected": "Մուտքագրված ֆունկցիայի շարահյուսությունը սխալ է։ Ստուգեք՝ բաց չե՞ք թողել փակագծերից մեկը՝ ( կամ  )։", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Ձեր տրամադրած գաղտնաբառը ճիշտ չէ:<br>Ստուգեք, որ CAPS LOCK ստեղնը անջատված է և օգտագործեք ճիշտ գլխատառացումը:", "SSE.Controllers.Main.errorPasteMaxRange": "Պատճենման ու փակցման տարածքները չեն համընկնում։<br>Պատճենված վանդակները փակցնելու համար ընտրեք նույնաչափ տարածք կամ կտտացրեք տողի առաջին վանդակի վրա։", "SSE.Controllers.Main.errorPasteMultiSelect": "Այս գործողությունը չի կարող կատարվել բազմակի տիրույթի ընտրության դեպքում:<br>Ընտրեք մեկ տիրույթ և նորից փորձեք:", "SSE.Controllers.Main.errorPasteSlicerError": "Սեղանի կտրիչները չեն կարող պատճենվել մի աշխատանքային գրքույկից մյուսը:", "SSE.Controllers.Main.errorPivotGroup": "Հնարավոր չէ խմբավորել այդ ընտրությունը:", "SSE.Controllers.Main.errorPivotOverlap": "Ամփոփիչ աղյուսակի հաշվետվությունը չի կարող համընկնել աղյուսակի վրա:", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Ամփոփիչ աղյուսակի հաշվետվությունը պահվել է առանց հիմքում ընկած տվյալների:<br>Օգտագործեք «Թարմացնել» կոճակը՝ հաշվետվությունը թարմացնելու համար:", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Ցավոք, հնարավոր չէ ծրագրի այս տարբերակում միանգամից տպել 1500-ից ավելի էջ։<br>Այս սահմանափակումը կվերացվի գալիք թողարկումներում։", "SSE.Controllers.Main.errorProcessSaveResult": "Պահպանումը խափանվեց", "SSE.Controllers.Main.errorServerVersion": "Խմբագրիչի տարբերակը արդիացվել է։ Որպեսզի փոփոխումները տեղի ունենան, էջը նորից կբեռնվի։", "SSE.Controllers.Main.errorSessionAbsolute": "Փաստաթղթի խմբագրման գործաժամը սպառվել է։ Նորի՛ց բեռնեք էջը։", "SSE.Controllers.Main.errorSessionIdle": "Փաստաթուղթը երկար ժամանակ չի խմբագրվել։ Նորի՛ց բեռնեք էջը։", "SSE.Controllers.Main.errorSessionToken": "Սպասարկիչի հետ կապն ընդհատվել է։ Խնդրում ենք էջը թարմացնել։", "SSE.Controllers.Main.errorSetPassword": "Գաղտնաբառը չհաջողվեց սահմանել:", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Տեղադրության հղումը վավեր չէ, քանի որ բջիջները բոլորը նույն սյունակում կամ տողում չեն:<br>Ընտրեք վանդակները, որոնք բոլորը մեկ սյունակում կամ տողում են:", "SSE.Controllers.Main.errorStockChart": "Տողերի սխալ կարգ։ Բորսայի գծապատկեր ստանալու համար տվյալները թերթի վրա դասավորեք հետևյալ կերպ՝<br>բացման գին, առավելագույն գին, նվազագույն գին, փակման գին։ ", "SSE.Controllers.Main.errorToken": "Փաստաթղթի անվտանգության կտրոնը ճիշտ չի ձևակերպված։<br>Դիմեք փաստաթղթերի սպասարկիչի ձեր վարիչին։", "SSE.Controllers.Main.errorTokenExpire": "Փաստաթղթի անվտանգության կտրոնի ժամկետն անցել է։<br>Դիմեք փաստաթղթերի սպասարկիչի ձեր վարիչին։", "SSE.Controllers.Main.errorUnexpectedGuid": "Արտաքին սխալ։<br>Անսպասելի GUID։ Եթե սխալը շարունակվի, դիմեք աջակցության ծառայությանը։", "SSE.Controllers.Main.errorUpdateVersion": "Նիշքի տարբերակը փոխվել է։ Էջը նորից կբեռնվի։", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Կապը վերահաստատվել է,և ֆայլի տարբերակը փոխվել է։<br>Նախքան աշխատանքը շարունակելը ներբեռնեք ֆայլը կամ պատճենեք դրա պարունակությունը՝ վստահ լինելու, որ ոչինչ չի կորել, և ապա նորից բեռնեք այս էջը։", "SSE.Controllers.Main.errorUserDrop": "Այս պահին նիշքն անհասանելի է։", "SSE.Controllers.Main.errorUsersExceed": "Օգտատերերի՝ սակագնային պլանով թույլատրված քանակը գերազանցվել է։", "SSE.Controllers.Main.errorViewerDisconnect": "Միացումն ընդհատվել է։ Դուք կարող եք շարունակել դիտել փաստաթուղթը,<br>բայց չեք կարողանա ներբեռնել կամ տպել, մինչև միացումը չվերականգնվի։", "SSE.Controllers.Main.errorWrongBracketsCount": "Բանաձևում սխալ կա՝<br>փակագծերի սխալ քանակ։", "SSE.Controllers.Main.errorWrongOperator": "Բանաձևում սխալ կա՝ սխալ օպերատոր։<br>Ուղղեք սխալը։", "SSE.Controllers.Main.errorWrongPassword": "Ձեր տրամադրած գաղտնաբառը ճիշտ չէ:", "SSE.Controllers.Main.errRemDuplicates": "Գտնվել և ջնջվել են կրկնօրինակ արժեքներ՝ {0}, մնացել են եզակի արժեքներ՝ {1}:", "SSE.Controllers.Main.leavePageText": "Այս աղյուսակաթերթում ունեք չպահպանված փոփոխումներ։ Դրանք պահպանելու համար սեղմեք «Մնալ այս էջում», ապա՝ «Պահպանել»։ Չպահպանված փոփոխումները չեղարկելու համար սեղմեք «Լքել այս էջը»։", "SSE.Controllers.Main.leavePageTextOnClose": "Այս աղյուսակի բոլոր չպահված փոփոխությունները կկորչեն:<br> Սեղմեք «Չեղարկել», ապա «Պահել»՝ դրանք պահպանելու համար: Սեղմեք «OK»՝ չպահված բոլոր փոփոխությունները հեռացնելու համար:", "SSE.Controllers.Main.loadFontsTextText": "Տվյալների բեռնում...", "SSE.Controllers.Main.loadFontsTitleText": "Տվյալների բեռնում", "SSE.Controllers.Main.loadFontTextText": "Տվյալների բեռնում...", "SSE.Controllers.Main.loadFontTitleText": "Տվյալների բեռնում", "SSE.Controllers.Main.loadImagesTextText": "Նկարների բեռնում...", "SSE.Controllers.Main.loadImagesTitleText": "Նկարների բեռնում", "SSE.Controllers.Main.loadImageTextText": "Նկարի բեռնում...", "SSE.Controllers.Main.loadImageTitleText": "Նկարի բեռնում", "SSE.Controllers.Main.loadingDocumentTitleText": "Աղյուսակաթերթի բեռնում", "SSE.Controllers.Main.notcriticalErrorTitle": "Զգուշացում", "SSE.Controllers.Main.openErrorText": "Նիշքի բացման ժամանակ տեղի ունեցավ սխալ", "SSE.Controllers.Main.openTextText": "Աղյուսակաթերթի բացում...", "SSE.Controllers.Main.openTitleText": "Աղյուսակաթերթի բացում", "SSE.Controllers.Main.pastInMergeAreaError": "Միավորված վանդակի մի մասը հնարավոր չէ փոխել", "SSE.Controllers.Main.printTextText": "Աղյուսակաթերթի տպում...", "SSE.Controllers.Main.printTitleText": "Աղյուսակաթերթի տպում", "SSE.Controllers.Main.reloadButtonText": "Վերբեռնել էջը", "SSE.Controllers.Main.requestEditFailedMessageText": "Ինչ-որ մեկն այս պահին խմբագրում է փաստաթուղթը։ Ավելի ուշ նորի՛ց փորձեք։", "SSE.Controllers.Main.requestEditFailedTitleText": "Մատչումն արգելված է", "SSE.Controllers.Main.saveErrorText": "Նիշքի պահպանման ժամանակ տեղի ունեցավ սխալ", "SSE.Controllers.Main.saveErrorTextDesktop": "Այս ֆայլը հնարավոր չէ պահպանել կամ ստեղծել:<br>Հնարավոր պատճառներն են.<br>1. Ֆայլը միայն կարդալու է:2. Ֆայլը խմբագրվում է այլ օգտվողների կողմից:<br>Սկավառակը լցված է կամ վնասված է:", "SSE.Controllers.Main.saveTextText": "Պահպանել աղյուսակաթերթը...", "SSE.Controllers.Main.saveTitleText": "Պահպանել աղյուսակաթերթը", "SSE.Controllers.Main.scriptLoadError": "Կապը խիստ թույլ է, բաղադրիչների մի մասը չբեռնվեց։ Խնդրում ենք էջը թարմացնել։", "SSE.Controllers.Main.textAnonymous": "Անանուն", "SSE.Controllers.Main.textApplyAll": "Գործադրել բոլոր հավասարումների համար", "SSE.Controllers.Main.textBuyNow": "Այցելել կայք", "SSE.Controllers.Main.textChangesSaved": "Բոլոր փոփոխումները պահպանված են", "SSE.Controllers.Main.textClose": "Փակել", "SSE.Controllers.Main.textCloseTip": "Կտտացրեք՝ հուշումը փակելու համար", "SSE.Controllers.Main.textConfirm": "Հաստատում", "SSE.Controllers.Main.textContactUs": "Կապ վաճառքի բաժնի հետ", "SSE.Controllers.Main.textContinue": "Շարունակել", "SSE.Controllers.Main.textConvertEquation": "Այս հավասարումը ստեղծվել է հավասարումների խմբագրիչի հին տարբերակով, որն այլևս չի աջակցվում:Այն խմբագրելու համար հավասարումը փոխարկեք Office Math ML ձևաչափի:Փոխակերպե՞լ հիմա:", "SSE.Controllers.Main.textCustomLoader": "Խնդրում ենք նկատի ունենալ,որ ըստ թույլատրագրի պայմանների՝ Դուք իրավունք չունեք փոխելու բեռնման էկրանը։<br>Հարցման համար խնդրում ենք դիմել մեր վաճառքի բաժին։", "SSE.Controllers.Main.textDisconnect": "Կապը կորել է", "SSE.Controllers.Main.textFillOtherRows": "Լրացրեք այլ տողեր", "SSE.Controllers.Main.textFormulaFilledAllRows": "Բանաձևով լրացված {0} տողեր ունեն տվյալներ: Այլ դատարկ տողերի լրացումը կարող է տևել մի քանի րոպե:", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Բանաձևը լրացրեց առաջին {0} տողերը: Այլ դատարկ տողերի լրացումը կարող է տևել մի քանի րոպե:", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Լրացված բանաձևը միայն առաջին {0} տողերն ունեն տվյալներ՝ ըստ հիշողության պահպանման պատճառի: Այս թերթում կան ևս {1} տողեր: Դուք կարող եք դրանք լրացնել ձեռքով:", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Բանաձևը լրացրեց միայն առաջին {0} տողերը՝ ըստ հիշողության պահպանման պատճառի: Այս թերթի մյուս տողերը տվյալներ չունեն:", "SSE.Controllers.Main.textGuest": "Հյուր", "SSE.Controllers.Main.textHasMacros": "Ֆայլում կան ինքնաշխատ մակրոներ։<br>Գործարկե՞լ դրանք։", "SSE.Controllers.Main.textLearnMore": "Իմանալ ավելին", "SSE.Controllers.Main.textLoadingDocument": "Աղյուսակաթերթի բեռնում", "SSE.Controllers.Main.textLongName": "Մուտքագրել անուն՝ 128 նիշից պակաս:", "SSE.Controllers.Main.textNeedSynchronize": "Կան թարմացումներ", "SSE.Controllers.Main.textNo": "Ոչ", "SSE.Controllers.Main.textNoLicenseTitle": "Լիցենզիայի սահմանաչափը հասել է", "SSE.Controllers.Main.textPaidFeature": "Վճարովի գործառույթ", "SSE.Controllers.Main.textPleaseWait": "Վիրահատությունը կարող է սպասվածից ավելի շատ ժամանակ տևել: Խնդրում ենք սպասել...", "SSE.Controllers.Main.textReconnect": "Կապը վերականգնված է", "SSE.Controllers.Main.textRemember": "Հիշել իմ ընտրածը", "SSE.Controllers.Main.textRememberMacros": "Հիշել իմ ընտրությունը բոլոր մակրոների համար", "SSE.Controllers.Main.textRenameError": "Օգտվողի անունը չպետք է դատարկ լինի:", "SSE.Controllers.Main.textRenameLabel": "Մուտքագրել անուն, որը կօգտագործվի համատեղ աշխատանքի համար", "SSE.Controllers.Main.textRequestMacros": "Մակրոն հարցում է անում URL-ին: Ցանկանու՞մ եք թույլ տալ հարցումը %1-ին:", "SSE.Controllers.Main.textShape": "Պատկեր", "SSE.Controllers.Main.textStrict": "Խիստ աշխատակարգ", "SSE.Controllers.Main.textText": "Տեքստ", "SSE.Controllers.Main.textTryQuickPrint": "Դուք ընտրել եք Արագ տպում` ամբողջ փաստաթուղթը կտպվի վերջին ընտրված կամ սկզբնադիր տպիչի վրա:<br> Ցանկանու՞մ եք շարունակել։", "SSE.Controllers.Main.textTryUndoRedo": "Համախմբագրման արագ աշխատակարգում հետարկումն ու վերարկումն անջատված են։<br>«Խիստ աշխատակարգ»-ի սեղմումով անցեք համախմբագրման խիստ աշխատակարգին, որպեսզի նիշքը խմբագրեք առանց այլ օգտատերերի միջամտության և փոփոխումներն ուղարկեք միայն դրանք պահպանելուց հետո։ Կարող եք համախմբագրման աշխատակարգերը փոխել հավելյալ կարգավորումների միջոցով։", "SSE.Controllers.Main.textTryUndoRedoWarn": "Հետարկումն ու վերարկումն գործառույթներն անջատված են արագ համատեղ խմբագրման ռեժիմի համար:", "SSE.Controllers.Main.textUndo": "Հետարկել", "SSE.Controllers.Main.textYes": "Այո", "SSE.Controllers.Main.titleLicenseExp": "Լիցենզիայի ժամկետը լրացել է", "SSE.Controllers.Main.titleServerVersion": "Խմբագրիչը արդիացվել է", "SSE.Controllers.Main.txtAccent": "Շեշտ", "SSE.Controllers.Main.txtAll": "(Բոլորը)", "SSE.Controllers.Main.txtArt": "Ձեր տեքստը", "SSE.Controllers.Main.txtBasicShapes": "Հիմնական պատկերներ", "SSE.Controllers.Main.txtBlank": "(Դատարկ)", "SSE.Controllers.Main.txtButtons": "Կոճակներ", "SSE.Controllers.Main.txtByField": "%2-ից %1-ը", "SSE.Controllers.Main.txtCallouts": "Դրսագրեր", "SSE.Controllers.Main.txtCharts": "Գծապատկերներ", "SSE.Controllers.Main.txtClearFilter": "Մաքրել զտիչը", "SSE.Controllers.Main.txtColLbls": "Սյունակների պիտակներ", "SSE.Controllers.Main.txtColumn": "Սյունակ", "SSE.Controllers.Main.txtConfidential": "Կոնֆիդենցիալ", "SSE.Controllers.Main.txtDate": "Ամիս-ամսաթիվ", "SSE.Controllers.Main.txtDays": "օրեր", "SSE.Controllers.Main.txtDiagramTitle": "Գծապատկերի վերնագիր", "SSE.Controllers.Main.txtEditingMode": "Սահմանել ցուցակի խմբագրում․․․", "SSE.Controllers.Main.txtErrorLoadHistory": "Պատմության բեռնումը ձախողվեց", "SSE.Controllers.Main.txtFiguredArrows": "Ձևավոր սլաքներ", "SSE.Controllers.Main.txtFile": "Նիշք", "SSE.Controllers.Main.txtGrandTotal": "Ընդհանուր գումար", "SSE.Controllers.Main.txtGroup": "Խումբ", "SSE.Controllers.Main.txtHours": "Ժամեր", "SSE.Controllers.Main.txtLines": "Գծեր", "SSE.Controllers.Main.txtMath": "Մաթեմատիկական", "SSE.Controllers.Main.txtMinutes": "Րոպե", "SSE.Controllers.Main.txtMonths": "ամիսներ", "SSE.Controllers.Main.txtMultiSelect": "Բազմընտրում", "SSE.Controllers.Main.txtOr": "%1 կամ %2", "SSE.Controllers.Main.txtPage": "Էջ", "SSE.Controllers.Main.txtPageOf": "Էջ %1 %2-ից", "SSE.Controllers.Main.txtPages": "Էջեր", "SSE.Controllers.Main.txtPreparedBy": "Պատրաստված է", "SSE.Controllers.Main.txtPrintArea": "Տպման_տարածք", "SSE.Controllers.Main.txtQuarter": "Քրդ", "SSE.Controllers.Main.txtQuarters": "Քառորդներ", "SSE.Controllers.Main.txtRectangles": "Ուղղանկյուններ", "SSE.Controllers.Main.txtRow": "Տող", "SSE.Controllers.Main.txtRowLbls": "Տողի պիտակներ", "SSE.Controllers.Main.txtSeconds": "Վայրկյան", "SSE.Controllers.Main.txtSeries": "Շարքեր", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Գծային դրսագիր 1 (Եզրագիծ և շեշտագիծ)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Գծային դրսագիր 2 (Եզրագիծ և շեշտագիծ)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Գծային դրսագիր 3 (Եզրագիծ և շեշտագիծ)", "SSE.Controllers.Main.txtShape_accentCallout1": "Գծային դրսագիր 1(Շեշտագիծ)", "SSE.Controllers.Main.txtShape_accentCallout2": "Գծային դրսագիր 2 (Շեշտագիծ)", "SSE.Controllers.Main.txtShape_accentCallout3": "Գծային դրսագիր 3 (Շեշտագիծ)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "«Հետ» կոճակ", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "«Սկիզբ» կոճակ", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Դատարկ կոճակ", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Փաստաթղթի կոճակ", "SSE.Controllers.Main.txtShape_actionButtonEnd": "«Վերջ» կոճակ", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Առաջ կամ հաջորդ կոճակ", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Օգնության կոճակ", "SSE.Controllers.Main.txtShape_actionButtonHome": " <<Գլխավոր>> կոճակը", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Տեղեկատվական կոճակ", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Ֆիլմի կոճակ", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Հետադարձի կոճակ", "SSE.Controllers.Main.txtShape_actionButtonSound": "Ձայնի կոճակ", "SSE.Controllers.Main.txtShape_arc": "Աղեղ", "SSE.Controllers.Main.txtShape_bentArrow": "Բեկյալ սլաք", "SSE.Controllers.Main.txtShape_bentConnector5": "Բեկյալ կապակցիչ", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Բեկյալ կապակցիչ սլաքով", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Բեկյալ կապակցիչ կրկնասլաքով", "SSE.Controllers.Main.txtShape_bentUpArrow": "Վեր բեկյալ սլաք", "SSE.Controllers.Main.txtShape_bevel": "Շեղատ", "SSE.Controllers.Main.txtShape_blockArc": "Աղեղ", "SSE.Controllers.Main.txtShape_borderCallout1": "Գծային դրսագիր 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Գծային դրսագիր 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Գծային դրսագիր 2", "SSE.Controllers.Main.txtShape_bracePair": "Կրկնակի ձևավոր փակագիծ", "SSE.Controllers.Main.txtShape_callout1": "Գծային դրսագիր 1 (Առանց Եզրագծի)", "SSE.Controllers.Main.txtShape_callout2": "Գծային դրսագիր 2 (Առանց եզրագծի)", "SSE.Controllers.Main.txtShape_callout3": "Գծային դրսագիր 3 (Առանց եզրագծի)", "SSE.Controllers.Main.txtShape_can": "Ցիլինդր", "SSE.Controllers.Main.txtShape_chevron": "Շևրոն", "SSE.Controllers.Main.txtShape_chord": "Լարագիծ", "SSE.Controllers.Main.txtShape_circularArrow": "Շրջանասլաք", "SSE.Controllers.Main.txtShape_cloud": "Ամպ", "SSE.Controllers.Main.txtShape_cloudCallout": "Ամպ դրսագիր", "SSE.Controllers.Main.txtShape_corner": "Անկյուն", "SSE.Controllers.Main.txtShape_cube": "Խորանարդ", "SSE.Controllers.Main.txtShape_curvedConnector3": "Կոր կապակցիչ", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Սլաքով կոր կապակցիչ", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Սլաքներով կոր կապակցիչ", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Վար կոր սլաք", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Ձախ կոր սլաք", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Աջ կոր սլաք", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Վեր կոր սլաք", "SSE.Controllers.Main.txtShape_decagon": "Տասնանկյուն", "SSE.Controllers.Main.txtShape_diagStripe": "Անկյունագծային շերտ", "SSE.Controllers.Main.txtShape_diamond": "Շեղանկյուն", "SSE.Controllers.Main.txtShape_dodecagon": "Տասներկուանկյուն", "SSE.Controllers.Main.txtShape_donut": "Օղաբլիթ", "SSE.Controllers.Main.txtShape_doubleWave": "Կրկնակի ալիք", "SSE.Controllers.Main.txtShape_downArrow": "Վար սլաք", "SSE.Controllers.Main.txtShape_downArrowCallout": "Վար սլաքի դրսագիր", "SSE.Controllers.Main.txtShape_ellipse": "Էլիպս", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Ծալքերով ժապավեն 1", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Վեր կորացած ժապավեն", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Գործընթացի քարտեզ․ Այլընտրանքային գործընթաց", "SSE.Controllers.Main.txtShape_flowChartCollate": "Գործընթացի քարտեզ", "SSE.Controllers.Main.txtShape_flowChartConnector": " Գործընթաց․ կապակցիչ", "SSE.Controllers.Main.txtShape_flowChartDecision": "Հերթականության գրաֆիկ.որոշում", "SSE.Controllers.Main.txtShape_flowChartDelay": "Հերթականության գրաֆիկ.ուշացում", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Գործընթաց.էկրան", "SSE.Controllers.Main.txtShape_flowChartDocument": "Ընթացաքարտեզ՝ փաստաթուղթ", "SSE.Controllers.Main.txtShape_flowChartExtract": "Գործընթաց.քաղվածք", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Հերթականության գրաֆիկ.տվյալներ", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Գործընթաց.ներքին հիշողություն", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Գործընթաց.մագնիսական սկավառակ", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Հերթականության գրաֆիկ.ուղղակի մուտքի հիշողություն", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Գործընթաց.հաջորդական մուտքի հիշողություն", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Գործընթաց.ձեռքով մուտքագրում", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Գործընթաց.ձեռքով հսկողություն", "SSE.Controllers.Main.txtShape_flowChartMerge": "Գործընթաց.միավորել", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Ընթացաքարտեզ՝ մի քանի փաստաթուղթ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Գործընթաց.մեկ այլ էջի հղում ", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Գործընթացի քարտեզ. Պահեստավորված տվյալներ", "SSE.Controllers.Main.txtShape_flowChartOr": "Գործընթաց.կամ", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Գործընթաց.բնորոշ գործընթաց", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Գործընթաց.պատրաստում", "SSE.Controllers.Main.txtShape_flowChartProcess": "Գործընթաց.գործընթաց", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Գործընթացի քարտեզ․Քարտ", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Գործընթաց.ծակոտաժապավեն ", "SSE.Controllers.Main.txtShape_flowChartSort": "Գործընթացի քարտեզ. Տեսակավորում", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Գործընթացի քարտեզ. Գումարային միացում", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Գործընթացի քարտեզ. Ավարտող", "SSE.Controllers.Main.txtShape_foldedCorner": "Համաչափ անկյուն", "SSE.Controllers.Main.txtShape_frame": "Շրջանակ ", "SSE.Controllers.Main.txtShape_halfFrame": "Կես շրջանակ", "SSE.Controllers.Main.txtShape_heart": "Սիրտ ", "SSE.Controllers.Main.txtShape_heptagon": "Յոթանկյուն ", "SSE.Controllers.Main.txtShape_hexagon": "Վեցանկյուն ", "SSE.Controllers.Main.txtShape_homePlate": "Հնգանկյուն ", "SSE.Controllers.Main.txtShape_horizontalScroll": "Հորիզոնական ոլորում", "SSE.Controllers.Main.txtShape_irregularSeal1": "Պայթյուն 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Պայթյուն 2", "SSE.Controllers.Main.txtShape_leftArrow": "Ձախ սլաք", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Ձախ սլաքի դրսագիր", "SSE.Controllers.Main.txtShape_leftBrace": "Ձախ ձևավոր փակագիծ", "SSE.Controllers.Main.txtShape_leftBracket": "Ձախ կլոր փակագիծ", "SSE.Controllers.Main.txtShape_leftRightArrow": "Ձախ-աջ սլաք", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Ձախ-աջ սլաքով դրսագիր", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Ձախ-աջ-վեր սլաք", "SSE.Controllers.Main.txtShape_leftUpArrow": "Ձախ-վեր սլաք", "SSE.Controllers.Main.txtShape_lightningBolt": "Կայծակ ", "SSE.Controllers.Main.txtShape_line": "Գիծ", "SSE.Controllers.Main.txtShape_lineWithArrow": "Սլաք", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Կրկնասլաք", "SSE.Controllers.Main.txtShape_mathDivide": "Բաժանում", "SSE.Controllers.Main.txtShape_mathEqual": "Հավասար", "SSE.Controllers.Main.txtShape_mathMinus": "Մինուս ", "SSE.Controllers.Main.txtShape_mathMultiply": "Բազմապատկել", "SSE.Controllers.Main.txtShape_mathNotEqual": "Ոչ հավասար", "SSE.Controllers.Main.txtShape_mathPlus": "Գումարած", "SSE.Controllers.Main.txtShape_moon": "Մունի Տառատեսակ", "SSE.Controllers.Main.txtShape_noSmoking": "Արգելված է", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Ատամնավոր աջ սլաք", "SSE.Controllers.Main.txtShape_octagon": "Ութանկյուն", "SSE.Controllers.Main.txtShape_parallelogram": "Քառանկյունի", "SSE.Controllers.Main.txtShape_pentagon": "Հնգանկյուն ", "SSE.Controllers.Main.txtShape_pie": "Բլիթ", "SSE.Controllers.Main.txtShape_plaque": "Ստորագրել", "SSE.Controllers.Main.txtShape_plus": "Գումարած", "SSE.Controllers.Main.txtShape_polyline1": "Խզբզանք", "SSE.Controllers.Main.txtShape_polyline2": "Ազատ ձևի առարկա", "SSE.Controllers.Main.txtShape_quadArrow": "Քառակուսի սլաք", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Քառակուսի սլաքի դրսագիր", "SSE.Controllers.Main.txtShape_rect": "Ուղղանկյուն", "SSE.Controllers.Main.txtShape_ribbon": "Ստորին ժապավեն", "SSE.Controllers.Main.txtShape_ribbon2": "Վերին ժապավեն", "SSE.Controllers.Main.txtShape_rightArrow": "Աջ սլաք", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Աջ սլաքով դրսագիր", "SSE.Controllers.Main.txtShape_rightBrace": "Աջ ձևավոր փակագիծ", "SSE.Controllers.Main.txtShape_rightBracket": "Աջ ուղղանկյուն փակագիծ", "SSE.Controllers.Main.txtShape_round1Rect": "Ուղղանկյուն մեկ կլորացված անկյունով", "SSE.Controllers.Main.txtShape_round2DiagRect": "Ուղղանկյուն երկու կլորացված հակառակ անկյուններով", "SSE.Controllers.Main.txtShape_round2SameRect": "Ուղղանկյուն երկու կլորացված հարակից անկյուններով", "SSE.Controllers.Main.txtShape_roundRect": "Ուղղանկյուն կլորացված անկյուններով", "SSE.Controllers.Main.txtShape_rtTriangle": "Ուղիղ եռանկյուն", "SSE.Controllers.Main.txtShape_smileyFace": "Ժպտացող դեմք", "SSE.Controllers.Main.txtShape_snip1Rect": "Կտրված միանկյուն ուղղանկյուն", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Կտրված անկյունագծով ուղղանկյուն", "SSE.Controllers.Main.txtShape_snip2SameRect": "Կտրված երկանկյուն ուղղանկյուն", "SSE.Controllers.Main.txtShape_snipRoundRect": "Կտրված և կլոր միանկյուն ուղղանկյուն", "SSE.Controllers.Main.txtShape_spline": "Կոր", "SSE.Controllers.Main.txtShape_star10": "10-թևանի աստղ", "SSE.Controllers.Main.txtShape_star12": "12-թևանի աստղ", "SSE.Controllers.Main.txtShape_star16": "16-թևանի աստղ", "SSE.Controllers.Main.txtShape_star24": "24-թևանի աստղ", "SSE.Controllers.Main.txtShape_star32": "32-թևանի աստղ", "SSE.Controllers.Main.txtShape_star4": "4-թևանի աստղ", "SSE.Controllers.Main.txtShape_star5": "5-թևանի աստղ", "SSE.Controllers.Main.txtShape_star6": "6-թևանի աստղ", "SSE.Controllers.Main.txtShape_star7": "7-թևանի աստղ", "SSE.Controllers.Main.txtShape_star8": "8-թևանի աստղ", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Գծավոր աջ սլաք", "SSE.Controllers.Main.txtShape_sun": "Կիր", "SSE.Controllers.Main.txtShape_teardrop": "Արցունք", "SSE.Controllers.Main.txtShape_textRect": "Գրվածքի տուփ", "SSE.Controllers.Main.txtShape_trapezoid": "Սեղան", "SSE.Controllers.Main.txtShape_triangle": "Եռանկյուն", "SSE.Controllers.Main.txtShape_upArrow": "Վեր սլաք", "SSE.Controllers.Main.txtShape_upArrowCallout": "Վեր սլաքով դրսագիր", "SSE.Controllers.Main.txtShape_upDownArrow": "Վեր-վար սլաք", "SSE.Controllers.Main.txtShape_uturnArrow": "Շրջադարձ սլաք", "SSE.Controllers.Main.txtShape_verticalScroll": "Ուղղահայաց ոլորում", "SSE.Controllers.Main.txtShape_wave": "Ալիք", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Ձվածիր դրսագիր", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Ուղղանկյուն ձևաչափ", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Կլորավուն ուղղանկյան դրսագիր", "SSE.Controllers.Main.txtStarsRibbons": "Աստղիկներ և ժապավեններ", "SSE.Controllers.Main.txtStyle_Bad": "Վատ", "SSE.Controllers.Main.txtStyle_Calculation": "Հաշվարկ", "SSE.Controllers.Main.txtStyle_Check_Cell": "Ստուգավանդակ", "SSE.Controllers.Main.txtStyle_Comma": "Ստորակետ", "SSE.Controllers.Main.txtStyle_Currency": "Տարադրամ", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Բացատրական տեքստ", "SSE.Controllers.Main.txtStyle_Good": "Լավ", "SSE.Controllers.Main.txtStyle_Heading_1": "Գլխագիր 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Գլխագիր 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Գլխագիր 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Գլխագիր 4", "SSE.Controllers.Main.txtStyle_Input": "Մուտք", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Կապված վանդակ", "SSE.Controllers.Main.txtStyle_Neutral": "Չեզոք", "SSE.Controllers.Main.txtStyle_Normal": "Սովորական", "SSE.Controllers.Main.txtStyle_Note": "Ծանոթագրություն", "SSE.Controllers.Main.txtStyle_Output": "Արտածում", "SSE.Controllers.Main.txtStyle_Percent": "Տոկոսային", "SSE.Controllers.Main.txtStyle_Title": "Վերնագիր", "SSE.Controllers.Main.txtStyle_Total": "Ընդամենը", "SSE.Controllers.Main.txtStyle_Warning_Text": "Զգուշացման տեքստ", "SSE.Controllers.Main.txtTab": "Սյունատ", "SSE.Controllers.Main.txtTable": "Աղյուսակ", "SSE.Controllers.Main.txtTime": "Ժամանակ", "SSE.Controllers.Main.txtUnlock": "Ապակողպել", "SSE.Controllers.Main.txtUnlockRange": "Ապակողպել շարանը", "SSE.Controllers.Main.txtUnlockRangeDescription": "Մուտքագրեք գաղտնաբառը այս տիրույթը փոխելու համար.", "SSE.Controllers.Main.txtUnlockRangeWarning": "Շրջանակը, որը փորձում եք փոխել, պաշտպանված է գաղտնաբառով:", "SSE.Controllers.Main.txtValues": "Արժեքներ", "SSE.Controllers.Main.txtXAxis": "X առանցք", "SSE.Controllers.Main.txtYAxis": "Y առանցք", "SSE.Controllers.Main.txtYears": "Տարիներ", "SSE.Controllers.Main.unknownErrorText": "Անհայտ սխալ։", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Ձեր դիտարկիչը չի աջակցվում։", "SSE.Controllers.Main.uploadDocExtMessage": "Անհայտ փաստաթղթի ձևաչափ:", "SSE.Controllers.Main.uploadDocFileCountMessage": "Ոչ մի փաստաթուղթ չի վերբեռնվել:", "SSE.Controllers.Main.uploadDocSizeMessage": "Փաստաթղթի չափի առավելագույն սահմանաչափը գերազանցվել է:", "SSE.Controllers.Main.uploadImageExtMessage": "Նկարի անհայտ ձևաչափ։", "SSE.Controllers.Main.uploadImageFileCountMessage": "Ոչ մի նկար չի բեռնվել։", "SSE.Controllers.Main.uploadImageSizeMessage": "Պատկերը չափազանց մեծ է:Առավելագույն չափը 25 ՄԲ է:", "SSE.Controllers.Main.uploadImageTextText": "Նկարի վերբեռնում...", "SSE.Controllers.Main.uploadImageTitleText": "Նկարի վերբեռնում", "SSE.Controllers.Main.waitText": "Խնդրում ենք սպասել...", "SSE.Controllers.Main.warnBrowserIE9": "Հավելվածը ցածր հնարավորություններ ունի IE9-ի վրա:Օգտագործեք IE10 կամ ավելի բարձր", "SSE.Controllers.Main.warnBrowserZoom": "Ձեր դիտարկիչի դիտափոխման ներկա կարգավորումը չի աջակցվում ամբողջությամբ։ Ctrl+0 սեղմելով վերադարձեք սկզբնադիր դիտափոխմանը։", "SSE.Controllers.Main.warnLicenseExceeded": "Դուք հասել եք %1 խմբագրիչներին միաժամանակ միանալու սահմանափակմանը։ Այս փաստաթուղթը կբացվի միայն ընթերցման համար։<br>Մանրամասների համար դիմեք վարիչին։", "SSE.Controllers.Main.warnLicenseExp": "Ձեր թույլատրագրի ժամկետը սպառվել է։<br>Թարմացրեք թույլատրագիրը, ապա՝ էջը։", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Լիցենզիայի ժամկետը լրացել է<br>Դուք չունեք փաստաթղթերի խմբագրման գործառույթից օգտվելու հնարավորություն :<br> Խնդրում ենք կապվել Ձեր ադմինիստրատորի հետ:", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Լիցենզիան պետք է երկարաձգել:<br>Դուք ունեք փաստաթղթերի խմբագրման գործառույթի սահմանափակ հասանելիություն:<br> Խնդրում ենք կապվել Ձեր ադմինիստրատորի հետ՝ լիարժեք մուտք ստանալու համար</b>", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Դուք հասել եք %1 խմբագրիչներին միաժամանակ միանալու սահմանափակմանը։ Մանրամասների համար դիմեք վարիչին։", "SSE.Controllers.Main.warnNoLicense": "Դուք հասել եք %1 խմբագրիչներին միաժամանակ միանալու սահմանափակմանը։ Այս փաստաթուղթը կբացվի միայն ընթերցման համար։<br>Ծրագրի նորացման անհատական պայմանները քննարկելու համար գրեք վաճառքի բաժին։", "SSE.Controllers.Main.warnNoLicenseUsers": "Դուք հասել եք %1 խմբագրիչներին միաժամանակ միանալու սահմանափակմանը։ Ծրագրի նորացման անհատական պայմանները քննարկելու համար գրեք վաճառքի %1 բաժին։", "SSE.Controllers.Main.warnProcessRightsChange": "Ձեզ թույլ չի տրվում խմբագրել այս նիշքը։", "SSE.Controllers.Print.strAllSheets": "Բոլոր թերթեր", "SSE.Controllers.Print.textFirstCol": "Առաջին սյունակ", "SSE.Controllers.Print.textFirstRow": "Առաջին շարք", "SSE.Controllers.Print.textFrozenCols": "Սառեցված սյուներ", "SSE.Controllers.Print.textFrozenRows": "Սառեցված շարքեր", "SSE.Controllers.Print.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Controllers.Print.textNoRepeat": "Մի՛ կրկնիր", "SSE.Controllers.Print.textRepeat": "Կրկնել...", "SSE.Controllers.Print.textSelectRange": "Ընտրեք միջակայքը", "SSE.Controllers.Print.textWarning": "Զգուշացում", "SSE.Controllers.Print.txtCustom": "Հարմարեցված", "SSE.Controllers.Print.warnCheckMargings": "Լուսանցքները սխալ են", "SSE.Controllers.Search.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Controllers.Search.textNoTextFound": "Ձեր փնտրած տվյալները չեն գտնվել:Խնդրում ենք կարգաբերել Ձեր որոնման ընտրանքները:", "SSE.Controllers.Search.textReplaceSkipped": "Փոխարինումը կատարված է։{0} դեպք բաց է թողնվել:", "SSE.Controllers.Search.textReplaceSuccess": "Որոնումը կատարվել է։ {0} դեպք փոխարինվել է", "SSE.Controllers.Statusbar.errorLastSheet": "Աշխատագրքում պիտի լինի առնվազն մեկ տեսանելի աշխատաթերթ։", "SSE.Controllers.Statusbar.errorRemoveSheet": "Հնարավոր չէ ջնջել աշխատանքային թերթիկը:", "SSE.Controllers.Statusbar.strSheet": "Թերթ", "SSE.Controllers.Statusbar.textDisconnect": "Կապը ընդատվել է</b><br> Փորձում է միանալ: Խնդրում ենք ստուգել կապի կարգավորումները:", "SSE.Controllers.Statusbar.textSheetViewTip": "Դուք թերթի դիտման ռեժիմում եք: Զտիչները և տեսակավորումը տեսանելի են միայն ձեզ և նրանց, ովքեր դեռ այս տեսադաշտում են:", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Դուք թերթի դիտման ռեժիմում եք: Զտիչները տեսանելի են միայն ձեզ և նրանց, ովքեր դեռ այս տեսադաշտում են:", "SSE.Controllers.Statusbar.warnDeleteSheet": "Ընտրված աշխատաթերթերում կարող են լինել տվյալներ։ Շարունակե՞լ գործողությունը։", "SSE.Controllers.Statusbar.zoomText": "Խոշորացնել {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Տառատեսակը, որն ուզում եք պահպանել, այս սարքում տեղակայված չէ։<br>Տեքստի ոճը կցուցադրվի համակարգի տառատեսակներից մեկով, իսկ պահպանված տառատեսակը կգործածվի, երբ տեղակայվի։<br>Շարունակե՞լ։", "SSE.Controllers.Toolbar.errorComboSeries": "Համակցված աղյուսակ ստեղծելու համար ընտրել տվյալների առնվազն երկու շարք:", "SSE.Controllers.Toolbar.errorMaxPoints": "Գծապատկերի շարքի կետերի առավելագույն քանակը 4096 է։", "SSE.Controllers.Toolbar.errorMaxRows": "ՍԽԱԼ. Մեկ գծապատկերում տվյալների շարքերի առավելագույն քանակը 255 է", "SSE.Controllers.Toolbar.errorStockChart": "Տողերի սխալ կարգ։ Բորսայի գծապատկեր ստանալու համար տվյալները թերթի վրա դասավորեք հետևյալ կերպ՝<br>բացման գին, առավելագույն գին, նվազագույն գին, փակման գին։ ", "SSE.Controllers.Toolbar.textAccent": "Հնչյունատարբերիչ նշաններ", "SSE.Controllers.Toolbar.textBracket": "Փակագծեր", "SSE.Controllers.Toolbar.textDirectional": "Ուղղորդող", "SSE.Controllers.Toolbar.textFontSizeErr": "Մուտքագրված արժեքը սխալ է:<br>Խնդրում ենք մուտքագրել թվային արժեք 1-ից 409-ի միջև", "SSE.Controllers.Toolbar.textFraction": "Կոտորակներ", "SSE.Controllers.Toolbar.textFunction": "Գործառույթներ ", "SSE.Controllers.Toolbar.textIndicator": "ՑուցանիշներՑուցանիշներ", "SSE.Controllers.Toolbar.textInsert": "Զետեղել", "SSE.Controllers.Toolbar.textIntegral": "Ինտեգրալներ", "SSE.Controllers.Toolbar.textLargeOperator": "Այլ մեծ գործարկուներ", "SSE.Controllers.Toolbar.textLimitAndLog": "Սահմաններ և լոգարիթմներ", "SSE.Controllers.Toolbar.textLongOperation": "Երկարատև գործողություն", "SSE.Controllers.Toolbar.textMatrix": "Մատրիցներ", "SSE.Controllers.Toolbar.textOperator": "Գործարկուներ", "SSE.Controllers.Toolbar.textPivot": "Առանցքային աղյուսակ", "SSE.Controllers.Toolbar.textRadical": "Արմատներ", "SSE.Controllers.Toolbar.textRating": "Վարկանիշներ", "SSE.Controllers.Toolbar.textRecentlyUsed": "Վերջերս օգտագործված", "SSE.Controllers.Toolbar.textScript": "Սկրիպտներ", "SSE.Controllers.Toolbar.textShapes": "Պատկերներ", "SSE.Controllers.Toolbar.textSymbols": "Նշաններ", "SSE.Controllers.Toolbar.textWarning": "Զգուշացում", "SSE.Controllers.Toolbar.txtAccent_Accent": "Շեշտ", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Աջ-ձախ սլաք վերևում", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Վերին ձախ սլաք", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Աջագնա սլաք վերևում", "SSE.Controllers.Toolbar.txtAccent_Bar": "Գիծ", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Ներքնագիծ", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Վերնագիծ", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Շրջանակված բանաձև (լցատեքստով)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Շրջանակված բանաձև (օրինակ)", "SSE.Controllers.Toolbar.txtAccent_Check": "Ստուգել", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Ներքնափակագիծ", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Վերնափակագիծ", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Վեկտոր Ա", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "Վրագծված ABC", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y ՝ վերնագծով", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Եռակետ", "SSE.Controllers.Toolbar.txtAccent_DDot": "Երկկետ", "SSE.Controllers.Toolbar.txtAccent_Dot": "Կետ", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Կրկնակի վերնագիծ", "SSE.Controllers.Toolbar.txtAccent_Grave": "Բութ", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Խմբավորման նշանը ներքևում", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Խմբավորման նշանը վերևում", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Վերին ձախ կարթագիծ", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Ներքին աջ կարթագիծ", "SSE.Controllers.Toolbar.txtAccent_Hat": "Գլխարկ ", "SSE.Controllers.Toolbar.txtAccent_Smile": "Կարճանշան", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Ալիք", "SSE.Controllers.Toolbar.txtBracket_Angle": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Փակագծեր ու բաժանիչներ", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Փակագծեր ու բաժանիչներ", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Curve": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Փակագծեր ու բաժանիչներ", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Դեպքեր (երկու պայման)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Դեպքեր (երեք պայման)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Շեղջի առարկա", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Շեղջի առարկա", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Դեպքերի օրինակ", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Երկանդամ գործակից", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Երկանդամ գործակից", "SSE.Controllers.Toolbar.txtBracket_Line": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Round": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Փակագծեր ու բաժանիչներ", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Square": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Փակագծեր", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Մեկ ուղղանկյուն փակագիծ", "SSE.Controllers.Toolbar.txtDeleteCells": "Ջնջել վանդակներ", "SSE.Controllers.Toolbar.txtExpand": "Ընդարձակել և տեսակավորել", "SSE.Controllers.Toolbar.txtExpandSort": "Ընտրության կողքին գտնվող տվյալները չեն տեսակավորվի:Ցանկանու՞մ եք ընդլայնել ընտրությունը՝ ներառելով հարակից տվյալները, թե՞ շարունակել տեսակավորել միայն ներկայումս ընտրված վանդակները:", "SSE.Controllers.Toolbar.txtFractionDiagonal": " Շեղ կոտորակ", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Դիֆերենցիալ", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Դիֆերենցիալ", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Դիֆերենցիալ", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Դիֆերենցիալ", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Հորիզոնական կոտորակ", "SSE.Controllers.Toolbar.txtFractionPi_2": "Ֆի 2-ից ավելի", "SSE.Controllers.Toolbar.txtFractionSmall": "Փոքր կոտորակ", "SSE.Controllers.Toolbar.txtFractionVertical": "Շեղջված կոտորակ", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Հակադարձ կոսինուսի ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Հիպերբոլիկ հակադարձ կոսինուսի ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Հակադարձ կոտանգեսի ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Հիպերբոլիկ հակադարձ կոտանգենս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Հակադարձ կոսեկանտ ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Հիպերբոլիկ հակադարձ կոսեկանտ ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Հակադարձ սեկանս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Հիպերբոլիկ հակադարձ սեկանս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Հակադարձ սինուսային ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Հիպերբոլիկ հակադարձ սինուսային ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Հակադարձ տանգես ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Հիպերբոլիկ հակադարձ տանգես ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Cos": "Կոսինուս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Կոսինուս հիպերբոլիկ ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Cot": "Կոտանգենս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Coth": "Կոտանգենս հիպերբոլիկ ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Csc": "Կոսեկանս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Csch": "Կոսեկանս հիպերբոլիկ ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Սինուս թետա", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Կոսինուս 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Տանգենսի բանաձև", "SSE.Controllers.Toolbar.txtFunction_Sec": "Սեկանս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Sech": "Հիպերբոլիկ սեկանս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Sin": "Սինուս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Հիպերբոլիկ սինուսային ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Tan": "Տանգենս ֆունկցիա", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Հիպերբոլիկ տանգես ֆունկցիա", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Հարմարեցված", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Տվյալներ և մոդել", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Լավ, վատ և միջին", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Անանուն", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Թվերի ձևաչափ", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Ոճավորված վանդակների ոճեր", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Անվանումներ և էջագլուխներ", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Հարմարեցված", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Մութ", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Լույս", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Միջին", "SSE.Controllers.Toolbar.txtInsertCells": "Զետեղել վանդակներ", "SSE.Controllers.Toolbar.txtIntegral": "Ինտեգրալ ", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Դիֆերենցիալ թետա", "SSE.Controllers.Toolbar.txtIntegral_dx": "Դիֆերենցիալ x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Դիֆերենցիալ y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Ինտեգրալ ", "SSE.Controllers.Toolbar.txtIntegralDouble": "Կրկնակի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Կրկնակի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Կրկնակի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOriented": "Ուրվագծային ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Ուրվագծային ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Մակերեսային ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Մակերեսային ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Մակերեսային ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Ուրվագծային ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": " Ծավալի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": " Ծավալի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": " Ծավալի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Ինտեգրալ ", "SSE.Controllers.Toolbar.txtIntegralTriple": "Եռակի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Եռակի ինտեգրալ", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Եռակի ինտեգրալ", "SSE.Controllers.Toolbar.txtInvalidRange": "ՍԽԱԼ! Անվավեր վանդակների տիրույթ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Սեպանցում", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Սեպանցում", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Սեպանցում", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Սեպանցում", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Սեպանցում", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Համարտադրանք", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Համարտադրանք", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Համարտադրանք", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Համարտադրանք", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Համարտադրանք", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Արտադրանք ", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Միավորում", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Վե", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Վե", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Վե", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Վե", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Վե", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Հատում", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Հատում", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Հատում", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Հատում", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Հատում", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Արտադրանք ", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Արտադրանք ", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Արտադրանք ", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Արտադրանք ", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Արտադրանք ", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Ամփոփում", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Միավորում", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Միավորում", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Միավորում", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Միավորում", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Միավորում", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Սահմանափակել օրինակը", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Առավելագույն օրինակ", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Սահման", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Բնական լոգարիթմ", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Լոգարիթմ", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Լոգարիթմ", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Առավելագույն", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Նվազագույն", "SSE.Controllers.Toolbar.txtLockSort": "Տվյալները գտնվել են ձեր ընտրության կողքին, բայց դուք չունեք բավարար թույլտվություններ այդ վանդակները փոխելու համար:<br>Ցանկանու՞մ եք շարունակել ընթացիկ ընտրությունը:", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Դատարկ թվացանց՝ փակագծերով", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Դատարկ թվացանց՝ փակագծերով", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Դատարկ թվացանց՝ փակագծերով", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Դատարկ թվացանց՝ փակագծերով", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 դատարկ մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Հիմնագծի վրա կետեր", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Միջնագծի կետեր", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Անկյունագծի կետեր", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Ուղղաձիգ կետեր", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Նոսրացված մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Նոսրացված մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 ինքնության մատրիցա ", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 ինքնության մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 ինքնության մատրիցա", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 ինքնության մատրիցա", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Աջ-ձախ սլաք ներքևում", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Աջ-ձախ սլաք վերևում", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Ներքևի ձախ սլաք", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Վերին ձախ սլաք", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Աջագնա սլաք ներքևում", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Աջագնա սլաք վերևում", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Երկկետ հավասար", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Ապացուցում է", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Դելտա արդյունքներ", "SSE.Controllers.Toolbar.txtOperator_Definition": "Հավասար ըստ սահմանման", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Դելտան հավասար է", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Աջ-ձախ սլաք ներքևում", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Աջ-ձախ սլաք վերևում", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Ներքևի ձախ սլաք", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Վերին ձախ սլաք", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Աջագնա սլաք ներքևում", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Աջագնա սլաք վերևում", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Հավասար հավասար", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Մինուս հավասար", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Գումարած հավասար", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Չափված է․", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Արմատ", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Արմատ", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Քառակուսի արմատ՝ աստիճանով", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Խորանարդ արմատ", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Արմատ աստիճանով", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Քառակուսի արմատ", "SSE.Controllers.Toolbar.txtScriptCustom_1": "Սկրիպտ", "SSE.Controllers.Toolbar.txtScriptCustom_2": "Սկրիպտ", "SSE.Controllers.Toolbar.txtScriptCustom_3": "Սկրիպտ", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Սկրիպտ", "SSE.Controllers.Toolbar.txtScriptSub": "Վարգիր", "SSE.Controllers.Toolbar.txtScriptSubSup": "Վարգիր-վերգիր", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Ստորին և վերին ինդեքս ձախից", "SSE.Controllers.Toolbar.txtScriptSup": "Վերգիր", "SSE.Controllers.Toolbar.txtSorting": "Տեսակավորում", "SSE.Controllers.Toolbar.txtSortSelected": "Ընտրված տեսակավորում", "SSE.Controllers.Toolbar.txtSymbol_about": "Մոտավորապես", "SSE.Controllers.Toolbar.txtSymbol_additional": "Հավելում", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Ալեֆ", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Ալֆա", "SSE.Controllers.Toolbar.txtSymbol_approx": "Համարյա հավասար", "SSE.Controllers.Toolbar.txtSymbol_ast": "Աստղիկ գործարկու", "SSE.Controllers.Toolbar.txtSymbol_beta": "Բետա", "SSE.Controllers.Toolbar.txtSymbol_beth": "Բեթ", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Կետանշան գործարկու", "SSE.Controllers.Toolbar.txtSymbol_cap": "Հատում", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Խորանարդ արմատ", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Միջին գծի հորիզոնական էլիպսիս", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Աստիճան ըստ Ցելսիուսի", "SSE.Controllers.Toolbar.txtSymbol_chi": "Խի", "SSE.Controllers.Toolbar.txtSymbol_cong": "Մոտավորապես հավասար", "SSE.Controllers.Toolbar.txtSymbol_cup": "Միավորում", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Անկյունագծային բազմակետ ներքև աջ", "SSE.Controllers.Toolbar.txtSymbol_degree": "Աստիճաններ", "SSE.Controllers.Toolbar.txtSymbol_delta": "Դելտա", "SSE.Controllers.Toolbar.txtSymbol_div": "Բաժանման նշան", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Վար սլաք", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Դատարկ բազմություն", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Էփսիլոն", "SSE.Controllers.Toolbar.txtSymbol_equals": "Հավասար", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Նույնական է", "SSE.Controllers.Toolbar.txtSymbol_eta": "Էտա", "SSE.Controllers.Toolbar.txtSymbol_exists": "Այնտեղ առկա է", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Ֆակտորիալ", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Աստիճան ըստ Ֆարենհայտի", "SSE.Controllers.Toolbar.txtSymbol_forall": "Բոլորի համար", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Գամմա", "SSE.Controllers.Toolbar.txtSymbol_geq": "Ավելի մեծ կամ հավասար", "SSE.Controllers.Toolbar.txtSymbol_gg": "Շատ ավելի մեծ, քան", "SSE.Controllers.Toolbar.txtSymbol_greater": "Ավելի մեծ քան", "SSE.Controllers.Toolbar.txtSymbol_in": "Տարրն է հետևյալի՝", "SSE.Controllers.Toolbar.txtSymbol_inc": "Մեծացում", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Անվերջություն", "SSE.Controllers.Toolbar.txtSymbol_iota": "Յոտա", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Կապպա", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Լամբդա", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Ձախ սլաք", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Ձախ-աջ սլաք", "SSE.Controllers.Toolbar.txtSymbol_leq": "Պակաս կամ հավասար", "SSE.Controllers.Toolbar.txtSymbol_less": "Ավելի քիչ քան ", "SSE.Controllers.Toolbar.txtSymbol_ll": "Շատ ավելի քիչ, քան", "SSE.Controllers.Toolbar.txtSymbol_minus": "Մինուս ", "SSE.Controllers.Toolbar.txtSymbol_mp": "Մինուս գումարած", "SSE.Controllers.Toolbar.txtSymbol_mu": "Մու", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Նաբլա", "SSE.Controllers.Toolbar.txtSymbol_neq": "Հավասար չէ՝", "SSE.Controllers.Toolbar.txtSymbol_ni": "Պարունակում է որպես անդամ", "SSE.Controllers.Toolbar.txtSymbol_not": "Մերժման նշան", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Այնտեղ առկա չէ", "SSE.Controllers.Toolbar.txtSymbol_nu": "Նու", "SSE.Controllers.Toolbar.txtSymbol_o": "Օմիկրոն", "SSE.Controllers.Toolbar.txtSymbol_omega": "Օմեգա", "SSE.Controllers.Toolbar.txtSymbol_partial": "Մասնակի դիֆերենցիալ", "SSE.Controllers.Toolbar.txtSymbol_percent": "Տոկոսային", "SSE.Controllers.Toolbar.txtSymbol_phi": "Ֆի", "SSE.Controllers.Toolbar.txtSymbol_pi": "Ֆի", "SSE.Controllers.Toolbar.txtSymbol_plus": "Գումարած", "SSE.Controllers.Toolbar.txtSymbol_pm": "Գումարում և հանում", "SSE.Controllers.Toolbar.txtSymbol_propto": "Համաչափ է", "SSE.Controllers.Toolbar.txtSymbol_psi": "Պսի", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Չորրորդ արմատ", "SSE.Controllers.Toolbar.txtSymbol_qed": "Ապացույցի ավարտ", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Վեր աջ անկյունագծով էլիպս", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ռո", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Աջ սլաք", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Սիգմա", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Արմատի ​​նշան", "SSE.Controllers.Toolbar.txtSymbol_tau": "Տաու", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Հետևաբար", "SSE.Controllers.Toolbar.txtSymbol_theta": "Թետա", "SSE.Controllers.Toolbar.txtSymbol_times": "Բազմապատկման նշան", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Վեր սլաք", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Իփսիլոն", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Էփսիլոն տարբերակ", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Ֆի տարբերակ", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Ֆի տարբերակ", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ռո տարբերակ", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Սիգմա տարբերակ", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Տետա տարբերակ", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Ուղղաձիգ էլիպս", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Քսի", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Զետա", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Սեղանի ոճը մուգ", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Սեղանի ոճի լույս", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Սեղանի ոճը միջին", "SSE.Controllers.Toolbar.warnLongOperation": "Գործողությունը, որը պատրաստվում եք կատարել, կարող է բավականին շատ ժամանակ պահանջել ավարտելու համար:<br>Իսկապե՞ս ուզում եք շարունակել:", "SSE.Controllers.Toolbar.warnMergeLostData": "Գործողությունը կարող է ոչնչացնել ընտրված վանդակների տվյալները։<br>Շարունակե՞լ։", "SSE.Controllers.Viewport.textFreezePanes": "Փեղկերի սառեցում", "SSE.Controllers.Viewport.textFreezePanesShadow": "Ցույց տալ սառեցված ապակիների ստվերը", "SSE.Controllers.Viewport.textHideFBar": "Թաքցնել բանաձևի բարը", "SSE.Controllers.Viewport.textHideGridlines": "Թաքցնել ցանցագծերը", "SSE.Controllers.Viewport.textHideHeadings": "Թաքցնել գլխագրերը", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Տասնորդական բաժանարար", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Հազարների բաժանարար", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Պարամետրեր, որոնք օգտագործվում են թվային տվյալները ճանաչելու համար", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Տեքստի որակավորում", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Լրացուցիչ կարգավորումներ", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(ոչ մեկը)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Պատվերով զտիչ", "SSE.Views.AutoFilterDialog.textAddSelection": "Ընտրվածքը զտել", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Դատարկներ}", "SSE.Views.AutoFilterDialog.textSelectAll": "Ընտրել բոլորը", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Ընտրեք բոլոր որոնման արդյունքները", "SSE.Views.AutoFilterDialog.textWarning": "Զգուշացում", "SSE.Views.AutoFilterDialog.txtAboveAve": "Միջինից բարձր", "SSE.Views.AutoFilterDialog.txtBegins": "Սկսվում է...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Միջինից ցածր", "SSE.Views.AutoFilterDialog.txtBetween": "միջև․․․", "SSE.Views.AutoFilterDialog.txtClear": "Մաքրել", "SSE.Views.AutoFilterDialog.txtContains": "Պարունակում է...", "SSE.Views.AutoFilterDialog.txtEmpty": "Մուտքագրեք վանդակի ֆիլտրը", "SSE.Views.AutoFilterDialog.txtEnds": "Ավարտվում է...", "SSE.Views.AutoFilterDialog.txtEquals": "հավասար է․․․", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Զտել ըստ վանդակների գույնի", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Զտել ըստ տառատեսակի գույնի", "SSE.Views.AutoFilterDialog.txtGreater": "Մեծ է քան․․․", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Մեծ է կամ հավասար․․․", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Պիտակի զտիչ", "SSE.Views.AutoFilterDialog.txtLess": "Պակաս քան․․․", "SSE.Views.AutoFilterDialog.txtLessEquals": "Պակաս կամ հավասար...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Չի սկսվում հետևյալով․․․", "SSE.Views.AutoFilterDialog.txtNotBetween": "Միջևում չէ...", "SSE.Views.AutoFilterDialog.txtNotContains": "Չի պարունակում․․․", "SSE.Views.AutoFilterDialog.txtNotEnds": "չի ավարտվում հետևյալով․․․", "SSE.Views.AutoFilterDialog.txtNotEquals": "Հավասար չէ․․․", "SSE.Views.AutoFilterDialog.txtNumFilter": "Թվերի զտիչ", "SSE.Views.AutoFilterDialog.txtReapply": "Կրկին դիմել", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Դասավորել ըստ բջիջների գույնի", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Դասավորել ըստ տառատեսակի գույնի", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Տեսակավորել բարձրից ցածր:", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Տեսակավորել ցածրից բարձր:", "SSE.Views.AutoFilterDialog.txtSortOption": "Տեսակավորման ավելի շատ տարբերակներ...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Տեքստի զտիչ", "SSE.Views.AutoFilterDialog.txtTitle": "Զտիչ", "SSE.Views.AutoFilterDialog.txtTop10": "Թոփ 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Արժեքի զտիչ", "SSE.Views.AutoFilterDialog.warnFilterError": "Արժեքի զտիչ կիրառելու համար ձեզ անհրաժեշտ է առնվազն մեկ դաշտ Արժեքների տարածքում:", "SSE.Views.AutoFilterDialog.warnNoSelected": "Պետք է ընտրեք գոնե մեկ արժեք", "SSE.Views.CellEditor.textManager": "Անուն կառավարիչ", "SSE.Views.CellEditor.tipFormula": "Տեղադրեք գործառույթը", "SSE.Views.CellRangeDialog.errorMaxRows": "ՍԽԱԼ. Մեկ գծապատկերում տվյալների շարքերի առավելագույն քանակը 255 է", "SSE.Views.CellRangeDialog.errorStockChart": "Տողերի սխալ կարգ։ Բորսայի գծապատկեր ստանալու համար տվյալները թերթի վրա դասավորեք հետևյալ կերպ՝<br>բացման գին, առավելագույն գին, նվազագույն գին, փակման գին։ ", "SSE.Views.CellRangeDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.CellRangeDialog.txtInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.CellRangeDialog.txtTitle": "Ընտրեք Տվյալների տիրույթ", "SSE.Views.CellSettings.strShrink": "Նեղանալ, որպեսզի տեղավորվի", "SSE.Views.CellSettings.strWrap": "Տեքստի ծալում", "SSE.Views.CellSettings.textAngle": "Անկյուն", "SSE.Views.CellSettings.textBackColor": "Ֆոնի գույն", "SSE.Views.CellSettings.textBackground": "Ֆոնի գույն", "SSE.Views.CellSettings.textBorderColor": "Գույն", "SSE.Views.CellSettings.textBorders": "Եզրագծի ոճ", "SSE.Views.CellSettings.textClearRule": "Հստակ կանոններ", "SSE.Views.CellSettings.textColor": "Գույնի լցում", "SSE.Views.CellSettings.textColorScales": "Գունավոր կշեռքներ", "SSE.Views.CellSettings.textCondFormat": "Պայմանական ձևավորում", "SSE.Views.CellSettings.textControl": "Տեքստի վերահսկում", "SSE.Views.CellSettings.textDataBars": "Տվյալների բարեր", "SSE.Views.CellSettings.textDirection": "Ուղղություն", "SSE.Views.CellSettings.textFill": "Լցնել", "SSE.Views.CellSettings.textForeground": "Հետնագույն", "SSE.Views.CellSettings.textGradient": "Սահանցման կետեր", "SSE.Views.CellSettings.textGradientColor": "Գույն", "SSE.Views.CellSettings.textGradientFill": "Սահանցման լցում ", "SSE.Views.CellSettings.textIndent": "Նահանջ", "SSE.Views.CellSettings.textItems": "Նյութեր", "SSE.Views.CellSettings.textLinear": "Գծային", "SSE.Views.CellSettings.textManageRule": "Կառավարել Կանոնները", "SSE.Views.CellSettings.textNewRule": "Նոր կանոն", "SSE.Views.CellSettings.textNoFill": "Առանց լցման", "SSE.Views.CellSettings.textOrientation": "Տեքստի կողմնորոշում", "SSE.Views.CellSettings.textPattern": "Նախշ", "SSE.Views.CellSettings.textPatternFill": "Նախշ", "SSE.Views.CellSettings.textPosition": "Դիրք", "SSE.Views.CellSettings.textRadial": "Ճառագայթային", "SSE.Views.CellSettings.textSelectBorders": "Ընտրեք եզրագծերը, որոնք ցանկանում եք փոխել՝ կիրառելով վերը նշված ոճը", "SSE.Views.CellSettings.textSelection": "Ընթացիկ ընտրությունից", "SSE.Views.CellSettings.textThisPivot": "Այս առանցքից", "SSE.Views.CellSettings.textThisSheet": "Այս աշխատանքային թերթիկից", "SSE.Views.CellSettings.textThisTable": "Այս աղյուսակից", "SSE.Views.CellSettings.tipAddGradientPoint": "Ավելացնել սահանցման կետ", "SSE.Views.CellSettings.tipAll": "Սահմանել արտաքին եզրագիծը և բոլոր ներքին գծերը", "SSE.Views.CellSettings.tipBottom": "Սահմանել միայն արտաքին ստորին եզրագիծը", "SSE.Views.CellSettings.tipDiagD": "Սահմանեք անկյունագծային ներքև եզրագիծը", "SSE.Views.CellSettings.tipDiagU": "Սահմանեք անկյունագծային վերև եզրագիծը", "SSE.Views.CellSettings.tipInner": "Սահմանել միայն ներքին գծերը", "SSE.Views.CellSettings.tipInnerHor": "Սահմանել միայն հորիզոնական ներքին գծեր", "SSE.Views.CellSettings.tipInnerVert": "Սահմանել միայն ուղղահայաց ներքին գծեր", "SSE.Views.CellSettings.tipLeft": "Սահմանել միայն արտաքին ձախ եզրագիծը", "SSE.Views.CellSettings.tipNone": "Ոչ մի եզրագիծ չսահմանել", "SSE.Views.CellSettings.tipOuter": "Սահմանել միայն արտաքին եզրագիծը", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Հեռացնել գրադիենտ կետը", "SSE.Views.CellSettings.tipRight": "Սահմանել միայն արտաքին աջ եզրագիծը", "SSE.Views.CellSettings.tipTop": "Սահմանել միայն արտաքին վերին եզրագիծը", "SSE.Views.ChartDataDialog.errorInFormula": "Ձեր մուտքագրած բանաձևում սխալ կա:", "SSE.Views.ChartDataDialog.errorInvalidReference": "Հղումը վավեր չէ։ Հղումը պետք է լինի բաց աշխատանքային թերթիկին:", "SSE.Views.ChartDataDialog.errorMaxPoints": "Գծապատկերի շարքի կետերի առավելագույն քանակը 4096 է։", "SSE.Views.ChartDataDialog.errorMaxRows": "Մեկ գծապատկերում տվյալների շարքերի առավելագույն քանակը 255 է:", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Հղումը վավեր չէ։ Վերնագրերի, արժեքների, չափերի կամ տվյալների պիտակների հղումները պետք է լինեն մեկ բջիջ, տող կամ սյունակ:", "SSE.Views.ChartDataDialog.errorNoValues": "Գծապատկեր ստեղծելու համար շարքը պետք է պարունակի առնվազն մեկ արժեք:", "SSE.Views.ChartDataDialog.errorStockChart": "Տողերի սխալ կարգ։ Բորսայի գծապատկեր ստանալու համար տվյալները թերթի վրա դասավորեք հետևյալ կերպ՝<br>բացման գին, առավելագույն գին, նվազագույն գին, փակման գին։ ", "SSE.Views.ChartDataDialog.textAdd": "Հավելել", "SSE.Views.ChartDataDialog.textCategory": "Հորիզոնական (կատեգորիա) առանցքի պիտակներ", "SSE.Views.ChartDataDialog.textData": "Գծապատկերների տվյալների տիրույթ", "SSE.Views.ChartDataDialog.textDelete": "Ջնջել", "SSE.Views.ChartDataDialog.textDown": "Ներքև", "SSE.Views.ChartDataDialog.textEdit": "Խմբագրել", "SSE.Views.ChartDataDialog.textInvalidRange": "Վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.ChartDataDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.ChartDataDialog.textSeries": "Լեգենդի գրառումներ (Սերիա)", "SSE.Views.ChartDataDialog.textSwitch": "Փոխարկել տող/սյունակ", "SSE.Views.ChartDataDialog.textTitle": "Գծապատկերային տվյալներ", "SSE.Views.ChartDataDialog.textUp": "Վեր", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Ձեր մուտքագրած բանաձևում սխալ կա:", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Հղումը վավեր չէ։ Հղումը պետք է լինի բաց աշխատանքային թերթիկին:", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Գծապատկերի շարքի կետերի առավելագույն քանակը 4096 է։", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Մեկ գծապատկերում տվյալների շարքերի առավելագույն քանակը 255 է:", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Հղումը վավեր չէ։ Վերնագրերի, արժեքների, չափերի կամ տվյալների պիտակների հղումները պետք է լինեն մեկ բջիջ, տող կամ սյունակ:", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Գծապատկեր ստեղծելու համար շարքը պետք է պարունակի առնվազն մեկ արժեք:", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Տողերի սխալ կարգ։ Բորսայի գծապատկեր ստանալու համար տվյալները թերթի վրա դասավորեք հետևյալ կերպ՝<br>բացման գին, առավելագույն գին, նվազագույն գին, փակման գին։ ", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.ChartDataRangeDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Առանցքի պիտակների շարան", "SSE.Views.ChartDataRangeDialog.txtChoose": "Ընտրեք միջակայքը", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Սերիալի անվանումը", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Առանցքի պիտակներ", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Խմբագրել շարքը", "SSE.Views.ChartDataRangeDialog.txtValues": "Արժեքներ", "SSE.Views.ChartDataRangeDialog.txtXValues": "X արժեքներ", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y արժեքներ", "SSE.Views.ChartSettings.errorMaxRows": "Մեկ գծապատկերում տվյալների շարքերի առավելագույն քանակը 255 է:", "SSE.Views.ChartSettings.strLineWeight": "Գծի լայնություն", "SSE.Views.ChartSettings.strSparkColor": "Գույն", "SSE.Views.ChartSettings.strTemplate": "Ձևանմուշ", "SSE.Views.ChartSettings.text3dDepth": "Խորությունը (բազայի %)", "SSE.Views.ChartSettings.text3dHeight": "Բարձրություն (բազայի %)", "SSE.Views.ChartSettings.text3dRotation": "3D Պտտում", "SSE.Views.ChartSettings.textAdvanced": "Ցուցադրել լրացուցիչ կարգավորումները", "SSE.Views.ChartSettings.textAutoscale": "Ինքնասանդղակ", "SSE.Views.ChartSettings.textBorderSizeErr": " Մուտքագրված արժեքը սխալ է: Խնդրում ենք մուտքագրել 0կտ-ից 255կտ թվային արժեք:", "SSE.Views.ChartSettings.textChangeType": "Փոխել տեսակը", "SSE.Views.ChartSettings.textChartType": "Փոխել գծապատկերի տեսակը", "SSE.Views.ChartSettings.textDefault": "Սկզբնադիր շրջում", "SSE.Views.ChartSettings.textDown": "Ներքև", "SSE.Views.ChartSettings.textEditData": "Խմբագրել տվյալները և գտնվելու վայրը", "SSE.Views.ChartSettings.textFirstPoint": "Առաջին կետ", "SSE.Views.ChartSettings.textHeight": "Բարձրություն", "SSE.Views.ChartSettings.textHighPoint": "Բարձր կետ", "SSE.Views.ChartSettings.textKeepRatio": "Պահպանել համաչափությունը", "SSE.Views.ChartSettings.textLastPoint": "Վերջին կետ", "SSE.Views.ChartSettings.textLeft": "Ձախ", "SSE.Views.ChartSettings.textLowPoint": "Ցածր կետ", "SSE.Views.ChartSettings.textMarkers": "Մարկերներ", "SSE.Views.ChartSettings.textNarrow": "Նեղ տեսադաշտ", "SSE.Views.ChartSettings.textNegativePoint": "Բացասական կետ", "SSE.Views.ChartSettings.textPerspective": "Հեռանկար", "SSE.Views.ChartSettings.textRanges": "Տվյալների տիրույթ", "SSE.Views.ChartSettings.textRight": "Աջ", "SSE.Views.ChartSettings.textRightAngle": "Աջ անկյան առանցքներ", "SSE.Views.ChartSettings.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.ChartSettings.textShow": "Ցույց տալ", "SSE.Views.ChartSettings.textSize": "Չափ", "SSE.Views.ChartSettings.textStyle": "Ոճ", "SSE.Views.ChartSettings.textSwitch": "Փոխարկել տող/սյունակ", "SSE.Views.ChartSettings.textType": "Տեսակ", "SSE.Views.ChartSettings.textUp": "Վեր", "SSE.Views.ChartSettings.textWiden": "Լայն տեսադաշտ", "SSE.Views.ChartSettings.textWidth": "Լայնք", "SSE.Views.ChartSettings.textX": "X պտտում", "SSE.Views.ChartSettings.textY": "Y պտտում", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ՍԽԱԼ. Մեկ գծապատկերում միավորների առավելագույն քանակը 4096 է:", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ՍԽԱԼ. Մեկ գծապատկերում տվյալների շարքերի առավելագույն քանակը 255 է", "SSE.Views.ChartSettingsDlg.errorStockChart": "Տողերի սխալ կարգ։ Բորսայի գծապատկեր ստանալու համար տվյալները թերթի վրա դասավորեք հետևյալ կերպ՝<br>բացման գին, առավելագույն գին, նվազագույն գին, փակման գին։ ", "SSE.Views.ChartSettingsDlg.textAbsolute": "Մի շարժվեք կամ չափեք վանդակներով", "SSE.Views.ChartSettingsDlg.textAlt": "Այլընտրանքային տեքստ", "SSE.Views.ChartSettingsDlg.textAltDescription": "Նկարագրություն", "SSE.Views.ChartSettingsDlg.textAltTip": "Տեսողական առարկաների այլընտրական տեքստային ներկայացում, որը ընթերցվելու է տեսողության կամ մտավոր խանգարումներով մարդկանց համար՝ օգնելու նրանց ավելի լավ հասկանալ, թե ինչ տեղեկատվություն կա նկարի, պատկերի, գծապատկերի կամ աղյուսակի վրա։", "SSE.Views.ChartSettingsDlg.textAltTitle": "Վերնագիր", "SSE.Views.ChartSettingsDlg.textAuto": "Ինքնաշխատ", "SSE.Views.ChartSettingsDlg.textAutoEach": "Ինքնաշխատ՝ յուրաքանչյուրի համար", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Առանցքի հետ հատումներ", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Առանցքի ընտրանքներ", "SSE.Views.ChartSettingsDlg.textAxisPos": "Առանցքի դիրք", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Առանցքի կարգավորումներ", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Վերնագիր", "SSE.Views.ChartSettingsDlg.textBase": "Հիմք", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Բաժանումների միջև", "SSE.Views.ChartSettingsDlg.textBillions": "Միլիարդ", "SSE.Views.ChartSettingsDlg.textBottom": "Ներքև", "SSE.Views.ChartSettingsDlg.textCategoryName": "Կատեգորիայի անվանումը", "SSE.Views.ChartSettingsDlg.textCenter": "Կենտրոն", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Գծապատկերի տարրեր և<br>Գծապատկերների լեգենդ", "SSE.Views.ChartSettingsDlg.textChartTitle": "Գծապատկերի վերնագիր", "SSE.Views.ChartSettingsDlg.textCross": "Փոխհատում", "SSE.Views.ChartSettingsDlg.textCustom": "Հարմարեցված", "SSE.Views.ChartSettingsDlg.textDataColumns": "սյունակներում", "SSE.Views.ChartSettingsDlg.textDataLabels": "Տվյալների մեկնագրեր", "SSE.Views.ChartSettingsDlg.textDataRows": "շարքերում", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Ցուցադրել լեգենդը", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Թաքնված և դատարկ վանդակներ", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Միացրեք տվյալների կետերը գծի հետ", "SSE.Views.ChartSettingsDlg.textFit": "Հարմարեցնել լայնությանը", "SSE.Views.ChartSettingsDlg.textFixed": "Հաստատուն", "SSE.Views.ChartSettingsDlg.textFormat": "Պիտակի ձևաչափը", "SSE.Views.ChartSettingsDlg.textGaps": "Բացեր", "SSE.Views.ChartSettingsDlg.textGridLines": "Ցանցագծեր", "SSE.Views.ChartSettingsDlg.textGroup": "Խմբային կայծգիծ", "SSE.Views.ChartSettingsDlg.textHide": "Թաքցնել", "SSE.Views.ChartSettingsDlg.textHideAxis": "Թաքցնել առանցքը", "SSE.Views.ChartSettingsDlg.textHigh": "Բարձր", "SSE.Views.ChartSettingsDlg.textHorAxis": "Հորիզոնական առանցք", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Երկրորդական հորիզոնական առանցք", "SSE.Views.ChartSettingsDlg.textHorizontal": "Հորիզոնական", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Հարյուրներ", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Ներսում", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Ներսում ներքևից", "SSE.Views.ChartSettingsDlg.textInnerTop": "Ներսում վերևից", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.ChartSettingsDlg.textLabelDist": "Մեկնագրից հեռավորությունը", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Մեկնագրերի միջակայք", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Մեկնագրերի ընտրանքներ", "SSE.Views.ChartSettingsDlg.textLabelPos": "Մեկնագրի դիրք", "SSE.Views.ChartSettingsDlg.textLayout": "Դասավորություն ", "SSE.Views.ChartSettingsDlg.textLeft": "Ձախ", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Ձախ վերածածկ", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Ներքև", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Ձախ", "SSE.Views.ChartSettingsDlg.textLegendPos": "Պայմանանշան", "SSE.Views.ChartSettingsDlg.textLegendRight": "Աջ", "SSE.Views.ChartSettingsDlg.textLegendTop": "Վերև", "SSE.Views.ChartSettingsDlg.textLines": "Գծեր", "SSE.Views.ChartSettingsDlg.textLocationRange": "Տեղադրության միջակայք", "SSE.Views.ChartSettingsDlg.textLogScale": "Լոգարիթմական սանդղակ", "SSE.Views.ChartSettingsDlg.textLow": "Ցածր", "SSE.Views.ChartSettingsDlg.textMajor": "Հիմնական", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Հիմնական և հավելյալ", "SSE.Views.ChartSettingsDlg.textMajorType": "Հիմնական տիպ", "SSE.Views.ChartSettingsDlg.textManual": "Ձեռնադիր", "SSE.Views.ChartSettingsDlg.textMarkers": "Մարկերներ", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Միջակայքը նշանների միջև", "SSE.Views.ChartSettingsDlg.textMaxValue": "Առավելագույն արժեք", "SSE.Views.ChartSettingsDlg.textMillions": "Միլիոններ", "SSE.Views.ChartSettingsDlg.textMinor": "Հավելյալ", "SSE.Views.ChartSettingsDlg.textMinorType": "Հավելյալ տիպ", "SSE.Views.ChartSettingsDlg.textMinValue": "Նվազագույն արժեք", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Առանցքի կողքին", "SSE.Views.ChartSettingsDlg.textNone": "Ոչ մեկը", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Առանց վերածածկման", "SSE.Views.ChartSettingsDlg.textOneCell": "Տեղափոխել, բայց չչափել վանդակներով", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Մանրանիշերի վրա", "SSE.Views.ChartSettingsDlg.textOut": "Դրսից", "SSE.Views.ChartSettingsDlg.textOuterTop": "Դրսից վերևից", "SSE.Views.ChartSettingsDlg.textOverlay": "Վերածածկում", "SSE.Views.ChartSettingsDlg.textReverse": "Արժեքները՝ հակադարձ կարգով", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Հակադարձ հերթականություն", "SSE.Views.ChartSettingsDlg.textRight": "Աջ", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Աջ վերածածկ", "SSE.Views.ChartSettingsDlg.textRotated": "Պտտված", "SSE.Views.ChartSettingsDlg.textSameAll": "Նույնը բոլորի համար", "SSE.Views.ChartSettingsDlg.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.ChartSettingsDlg.textSeparator": "Տվյալների մեկնագրերի բաժանիչ", "SSE.Views.ChartSettingsDlg.textSeriesName": "Սերիալի անվանումը", "SSE.Views.ChartSettingsDlg.textShow": "Ցույց տալ", "SSE.Views.ChartSettingsDlg.textShowBorders": "Ցուցադրել գծապատկերների սահմանները", "SSE.Views.ChartSettingsDlg.textShowData": "Ցույց տալ տվյալները թաքնված տողերում և սյունակներում", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Ցույց տալ դատարկ բջիջները որպես", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Ցույց տալ առանցքը", "SSE.Views.ChartSettingsDlg.textShowValues": "Ցուցադրել գծապատկերների արժեքները", "SSE.Views.ChartSettingsDlg.textSingle": "միայնակ կայծգիծ", "SSE.Views.ChartSettingsDlg.textSmooth": "Հարթ", "SSE.Views.ChartSettingsDlg.textSnap": "Վանդակի խզում", "SSE.Views.ChartSettingsDlg.textSparkRanges": "կայծգծի շրջանակներ", "SSE.Views.ChartSettingsDlg.textStraight": "Ուղիղ", "SSE.Views.ChartSettingsDlg.textStyle": "Ոճ", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Հազարավորներ", "SSE.Views.ChartSettingsDlg.textTickOptions": "Մանրանիշերի ընտրանքներ", "SSE.Views.ChartSettingsDlg.textTitle": "Գծապատկեր - լրացուցիչ  կարգավորումներ", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "կայծգիծ- ընդլայնված կարգավորումներ", "SSE.Views.ChartSettingsDlg.textTop": "Վերև", "SSE.Views.ChartSettingsDlg.textTrillions": "Տրիլիոններ", "SSE.Views.ChartSettingsDlg.textTwoCell": "Տեղափոխել և չափել վանդակներով", "SSE.Views.ChartSettingsDlg.textType": "Տեսակ", "SSE.Views.ChartSettingsDlg.textTypeData": "Տեսակ և տվյալներ", "SSE.Views.ChartSettingsDlg.textUnits": "Ցուցադրման միավորներ", "SSE.Views.ChartSettingsDlg.textValue": "Արժեք", "SSE.Views.ChartSettingsDlg.textVertAxis": "Ուղղահայաց առանցք", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Երկրորդական ուղղահայաց առանցք", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X առանցքի վերնագիր", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y առանցքի վերնագիր", "SSE.Views.ChartSettingsDlg.textZero": "Զրո", "SSE.Views.ChartSettingsDlg.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.ChartTypeDialog.errorComboSeries": "Համակցված աղյուսակ ստեղծելու համար ընտրել տվյալների առնվազն երկու շարք:", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Ընտրված գծապատկերի տեսակը պահանջում է երկրորդական առանցք, որն օգտագործում է գոյություն ունեցող գծապատկերը: Ընտրեք գծապատկերի մեկ այլ տեսակ:", "SSE.Views.ChartTypeDialog.textSecondary": "Երկրորդական առանցք", "SSE.Views.ChartTypeDialog.textSeries": "Շարքեր", "SSE.Views.ChartTypeDialog.textStyle": "Ոճ", "SSE.Views.ChartTypeDialog.textTitle": "Գծապատկերի տեսակը", "SSE.Views.ChartTypeDialog.textType": "Տեսակ", "SSE.Views.CreatePivotDialog.textDataRange": "Աղբյուրի տվյալների տիրույթ", "SSE.Views.CreatePivotDialog.textDestination": "Ընտրեք սեղանի տեղը", "SSE.Views.CreatePivotDialog.textExist": "Առկա աշխատանքային թերթիկ", "SSE.Views.CreatePivotDialog.textInvalidRange": "Վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.CreatePivotDialog.textNew": "Նոր աշխատաթերթ", "SSE.Views.CreatePivotDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.CreatePivotDialog.textTitle": "Ստեղծեք առանցքային աղյուսակ", "SSE.Views.CreatePivotDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.CreateSparklineDialog.textDataRange": "Աղբյուրի տվյալների տիրույթ", "SSE.Views.CreateSparklineDialog.textDestination": "Ընտրեք, որտեղ տեղադրեք կայծակները", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.CreateSparklineDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.CreateSparklineDialog.textTitle": "Ստեղծել կայծագծեր", "SSE.Views.CreateSparklineDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.DataTab.capBtnGroup": "Խումբ", "SSE.Views.DataTab.capBtnTextCustomSort": "Հարմարեցրած տեսակավորում", "SSE.Views.DataTab.capBtnTextDataValidation": "Տվյալների վավերացում", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Հեռացրեք կրկնօրինակները", "SSE.Views.DataTab.capBtnTextToCol": "Տեքստ սյունակներ", "SSE.Views.DataTab.capBtnUngroup": "Ապախմբավորել", "SSE.Views.DataTab.capDataExternalLinks": "Արտաքին հղումներ", "SSE.Views.DataTab.capDataFromText": "Ստանալ տվյալներ", "SSE.Views.DataTab.mniFromFile": "Տեղական TXT/CSV-ից", "SSE.Views.DataTab.mniFromUrl": "TXT/CSV վեբ հասցեից", "SSE.Views.DataTab.mniFromXMLFile": "Տեղական XML-ից", "SSE.Views.DataTab.textBelow": "Մանրամասն ներքևում գտնվող ամփոփ տողերը", "SSE.Views.DataTab.textClear": "Հստակ ուրվագիծ", "SSE.Views.DataTab.textColumns": "Ապախմբավորել սյունակները", "SSE.Views.DataTab.textGroupColumns": "Խմբային սյունակներ", "SSE.Views.DataTab.textGroupRows": "Խմբային տողեր", "SSE.Views.DataTab.textRightOf": "Ամփոփ սյունակներ՝ մանրամասներից աջ", "SSE.Views.DataTab.textRows": "Ապախմբավորել տողերը", "SSE.Views.DataTab.tipCustomSort": "Հարմարեցրած տեսակավորում", "SSE.Views.DataTab.tipDataFromText": "Ստանալ տվյալներ Text/CSV ֆայլից", "SSE.Views.DataTab.tipDataValidation": "Տվյալների վավերացում", "SSE.Views.DataTab.tipExternalLinks": "Դիտել այլ ֆայլեր, որոնց այս աղյուսակը կապված է", "SSE.Views.DataTab.tipGroup": "Վանդակների խմբային տիրույթ", "SSE.Views.DataTab.tipRemDuplicates": "Հեռացրեք կրկնօրինակ տողերը թերթիկից", "SSE.Views.DataTab.tipToColumns": "Բջջային տեքստը բաժանեք սյունակների", "SSE.Views.DataTab.tipUngroup": "Ապախմբավորել բջիջների տիրույթը", "SSE.Views.DataValidationDialog.errorFormula": "Արժեքը ներկայումս գնահատվում է որպես սխալ: Ցանկանու՞մ եք շարունակել։", "SSE.Views.DataValidationDialog.errorInvalid": "«{0}» դաշտի համար ձեր մուտքագրած արժեքը անվավեր է:", "SSE.Views.DataValidationDialog.errorInvalidDate": "«{0}» դաշտի համար ձեր մուտքագրած ամսաթիվն անվավեր է:", "SSE.Views.DataValidationDialog.errorInvalidList": "Ցուցակի աղբյուրը պետք է լինի սահմանազատված ցուցակ կամ հղում մեկ տողի կամ սյունակի:", "SSE.Views.DataValidationDialog.errorInvalidTime": "«{0}» դաշտի համար մուտքագրած ժամանակը անվավեր է:", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "«{1}» դաշտը պետք է մեծ կամ հավասար լինի «{0}» դաշտին:", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Դուք պետք է արժեք մուտքագրեք թե՛ «{0}» և թե՛ «{1}» դաշտում:", "SSE.Views.DataValidationDialog.errorMustEnterValue": "«{0}» դաշտում պետք է մուտքագրեք արժեք:", "SSE.Views.DataValidationDialog.errorNamedRange": "Ձեր նշած անունով ընդգրկույթ չի գտնվում:", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Բացասական արժեքները չեն կարող օգտագործվել «{0}» պայմաններում:", "SSE.Views.DataValidationDialog.errorNotNumeric": "«{0}» դաշտը պետք է լինի թվային արժեք, թվային արտահայտություն կամ վերաբերի թվային արժեք պարունակող բջիջին:", "SSE.Views.DataValidationDialog.strError": "Սխալների ծանուցում", "SSE.Views.DataValidationDialog.strInput": "Մուտքագրման հաղորդագրություն", "SSE.Views.DataValidationDialog.strSettings": "Կարգավորումներ", "SSE.Views.DataValidationDialog.textAlert": "Զգուշացում", "SSE.Views.DataValidationDialog.textAllow": "Թույլատրել", "SSE.Views.DataValidationDialog.textApply": "Կիրառեք այս փոփոխությունները նույն կարգավորումներով մնացած բոլոր վանդակներում", "SSE.Views.DataValidationDialog.textCellSelected": "Երբ բջիջն ընտրված է, ցույց տվեք այս մուտքային հաղորդագրությունը", "SSE.Views.DataValidationDialog.textCompare": "Համեմատել", "SSE.Views.DataValidationDialog.textData": "Տվյալներ", "SSE.Views.DataValidationDialog.textEndDate": "Ավարտման ամսաթիվ", "SSE.Views.DataValidationDialog.textEndTime": "Ավարտի ժամանակ", "SSE.Views.DataValidationDialog.textError": "Սխալի հաղորդագրություն", "SSE.Views.DataValidationDialog.textFormula": "Բանաձև ", "SSE.Views.DataValidationDialog.textIgnore": "Անտեսել դատարկը", "SSE.Views.DataValidationDialog.textInput": "Մուտքագրման հաղորդագրություն", "SSE.Views.DataValidationDialog.textMax": "Առավելագույն", "SSE.Views.DataValidationDialog.textMessage": "Հաղորդագրություն", "SSE.Views.DataValidationDialog.textMin": "Նվազագույն", "SSE.Views.DataValidationDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.DataValidationDialog.textShowDropDown": "Ցուցադրել բացվող ցուցակը բջիջում", "SSE.Views.DataValidationDialog.textShowError": "Ցույց տալ սխալի մասին ահազանգը անվավեր տվյալների մուտքագրումից հետո", "SSE.Views.DataValidationDialog.textShowInput": "Ցույց տալ մուտքային հաղորդագրությունը, երբ ընտրված է բջիջը", "SSE.Views.DataValidationDialog.textSource": "Աղբյուր", "SSE.Views.DataValidationDialog.textStartDate": "Մեկնարկի ամսաթիվ", "SSE.Views.DataValidationDialog.textStartTime": "Մեկնարկի ժամանակը", "SSE.Views.DataValidationDialog.textStop": "Կանգ", "SSE.Views.DataValidationDialog.textStyle": "Ոճ", "SSE.Views.DataValidationDialog.textTitle": "Վերնագիր", "SSE.Views.DataValidationDialog.textUserEnters": "Երբ օգտվողը մուտքագրում է անվավեր տվյալներ, ցուցադրեք այս սխալի մասին ծանուցումը", "SSE.Views.DataValidationDialog.txtAny": "Ցանկացած արժեք", "SSE.Views.DataValidationDialog.txtBetween": "միջև", "SSE.Views.DataValidationDialog.txtDate": "Ամիս-ամսաթիվ", "SSE.Views.DataValidationDialog.txtDecimal": "Տասնորդական", "SSE.Views.DataValidationDialog.txtElTime": " Անցած ժամանակը", "SSE.Views.DataValidationDialog.txtEndDate": "Ավարտման ամսաթիվ", "SSE.Views.DataValidationDialog.txtEndTime": "Ավարտի ժամանակ", "SSE.Views.DataValidationDialog.txtEqual": "հավասար է", "SSE.Views.DataValidationDialog.txtGreaterThan": "Ավելի մեծ քան", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Ավելի մեծ կամ հավասար", "SSE.Views.DataValidationDialog.txtLength": "Երկարություն", "SSE.Views.DataValidationDialog.txtLessThan": "Ավելի քիչ քան ", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Պակաս կամ հավասար", "SSE.Views.DataValidationDialog.txtList": "Ցուցակ", "SSE.Views.DataValidationDialog.txtNotBetween": "ոչ թե միջև", "SSE.Views.DataValidationDialog.txtNotEqual": "Հավասար չէ", "SSE.Views.DataValidationDialog.txtOther": "Այլ", "SSE.Views.DataValidationDialog.txtStartDate": "Մեկնարկի ամսաթիվ", "SSE.Views.DataValidationDialog.txtStartTime": "Մեկնարկի ժամանակը", "SSE.Views.DataValidationDialog.txtTextLength": "Տեքստի երկարությունը", "SSE.Views.DataValidationDialog.txtTime": "Ժամանակ", "SSE.Views.DataValidationDialog.txtWhole": "Ամբողջ թիվ", "SSE.Views.DigitalFilterDialog.capAnd": "Եվ", "SSE.Views.DigitalFilterDialog.capCondition1": "հավասար է", "SSE.Views.DigitalFilterDialog.capCondition10": "չի ավարտվում հետևյալով", "SSE.Views.DigitalFilterDialog.capCondition11": "պարունակում է", "SSE.Views.DigitalFilterDialog.capCondition12": "չի պարունակում", "SSE.Views.DigitalFilterDialog.capCondition2": "Հավասար չէ", "SSE.Views.DigitalFilterDialog.capCondition3": "ավելի մեծ է, քան", "SSE.Views.DigitalFilterDialog.capCondition4": "մեծ է կամ հավասար է", "SSE.Views.DigitalFilterDialog.capCondition5": "պակաս է քան", "SSE.Views.DigitalFilterDialog.capCondition6": "փոքր է կամ հավասար է", "SSE.Views.DigitalFilterDialog.capCondition7": "Սկսվում է ", "SSE.Views.DigitalFilterDialog.capCondition8": "չի սկսվում հետևյալով", "SSE.Views.DigitalFilterDialog.capCondition9": "Ավարտվում է ", "SSE.Views.DigitalFilterDialog.capOr": "կամ", "SSE.Views.DigitalFilterDialog.textNoFilter": "ոչ մի ֆիլտր", "SSE.Views.DigitalFilterDialog.textShowRows": "Ցույց տալ տողերը, որտեղ", "SSE.Views.DigitalFilterDialog.textUse1": "Օգտագործե՞լ ներկայացնել ցանկացած առանձին կերպար", "SSE.Views.DigitalFilterDialog.textUse2": "Օգտագործեք *՝ կերպարների ցանկացած շարք ներկայացնելու համար", "SSE.Views.DigitalFilterDialog.txtTitle": "Պատվերով զտիչ", "SSE.Views.DocumentHolder.advancedEquationText": "Հավասարման կարգավորումներ", "SSE.Views.DocumentHolder.advancedImgText": "Պատկերի ընդլայնված կարգավորումներ ", "SSE.Views.DocumentHolder.advancedShapeText": "Ձևավորել լրացուցիչ  կարգավորումներ", "SSE.Views.DocumentHolder.advancedSlicerText": "Շերտազտիչ Ընդլայնված կարգավորումներ", "SSE.Views.DocumentHolder.allLinearText": "Ամբողջական գծային", "SSE.Views.DocumentHolder.allProfText": "Ամբողջական պրոֆեսիոնալ", "SSE.Views.DocumentHolder.bottomCellText": "Հավասարեցնել ներքևից", "SSE.Views.DocumentHolder.bulletsText": "Պարբերակներ և համարակալում", "SSE.Views.DocumentHolder.centerCellText": "Հավասարեցնել մեջտեղով", "SSE.Views.DocumentHolder.chartDataText": "Ընտրեք գծապատկերի տվյալները", "SSE.Views.DocumentHolder.chartText": "Գծապատկերի լրացուցիչ  կարգավորումներ", "SSE.Views.DocumentHolder.chartTypeText": "Փոխել գծապատկերի տեսակը", "SSE.Views.DocumentHolder.currLinearText": "Ընթացիկ - Գծային", "SSE.Views.DocumentHolder.currProfText": "Ընթացիկ-Պրոֆեսիոնալ", "SSE.Views.DocumentHolder.deleteColumnText": "Սյունակ", "SSE.Views.DocumentHolder.deleteRowText": "Տող", "SSE.Views.DocumentHolder.deleteTableText": "Աղյուսակ", "SSE.Views.DocumentHolder.direct270Text": "Տեքստը պտտել վեր", "SSE.Views.DocumentHolder.direct90Text": "Տեքստը պտտել ներքև", "SSE.Views.DocumentHolder.directHText": "Հորիզոնական", "SSE.Views.DocumentHolder.directionText": "Տեքստի ուղղություն", "SSE.Views.DocumentHolder.editChartText": "Խմբագրել տվյալները", "SSE.Views.DocumentHolder.editHyperlinkText": "Խմբագրել գերհղումը", "SSE.Views.DocumentHolder.insertColumnLeftText": "Սյունակ ձախից", "SSE.Views.DocumentHolder.insertColumnRightText": "Սյունակ աջից", "SSE.Views.DocumentHolder.insertRowAboveText": "Տող վերևում", "SSE.Views.DocumentHolder.insertRowBelowText": "Ներքևի տող", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Իրական չափ", "SSE.Views.DocumentHolder.removeHyperlinkText": "Հանել գերհղումը", "SSE.Views.DocumentHolder.selectColumnText": "Ամբողջ սյունակը", "SSE.Views.DocumentHolder.selectDataText": "Սյունակի տվյալները", "SSE.Views.DocumentHolder.selectRowText": "Տող", "SSE.Views.DocumentHolder.selectTableText": "Աղյուսակ", "SSE.Views.DocumentHolder.strDelete": "Ջնջել ստորագրությունը", "SSE.Views.DocumentHolder.strDetails": "Ստորագրության մանրամասներ", "SSE.Views.DocumentHolder.strSetup": "Ստորագրության տեղակայում", "SSE.Views.DocumentHolder.strSign": "Ստորագրել", "SSE.Views.DocumentHolder.textAlign": "Հավասարեցում", "SSE.Views.DocumentHolder.textArrange": "Դասավորել", "SSE.Views.DocumentHolder.textArrangeBack": "Տանել խորք", "SSE.Views.DocumentHolder.textArrangeBackward": "ՈՒղարկել հետ", "SSE.Views.DocumentHolder.textArrangeForward": "Բերել առաջ", "SSE.Views.DocumentHolder.textArrangeFront": "Բերել առջևք ", "SSE.Views.DocumentHolder.textAverage": "ՄԻՋԻՆ", "SSE.Views.DocumentHolder.textBullets": "Պարբերակներ", "SSE.Views.DocumentHolder.textCount": "Հաշվել", "SSE.Views.DocumentHolder.textCrop": "Եզրատել", "SSE.Views.DocumentHolder.textCropFill": "Լցնել", "SSE.Views.DocumentHolder.textCropFit": "Հարմարեցնել", "SSE.Views.DocumentHolder.textEditPoints": "Խմբագրել կետերը", "SSE.Views.DocumentHolder.textEntriesList": "Ընտրեք բացվող ցանկից", "SSE.Views.DocumentHolder.textFlipH": "Շրջել հորիզոնական", "SSE.Views.DocumentHolder.textFlipV": "Շրջել ուղղաձիգ", "SSE.Views.DocumentHolder.textFreezePanes": "Փեղկերի սառեցում", "SSE.Views.DocumentHolder.textFromFile": "Նիշքից", "SSE.Views.DocumentHolder.textFromStorage": "Պահեստից", "SSE.Views.DocumentHolder.textFromUrl": "URL-ից", "SSE.Views.DocumentHolder.textListSettings": "Ցանկի կարգավորումներ", "SSE.Views.DocumentHolder.textMacro": "Նշանակել մակրո", "SSE.Views.DocumentHolder.textMax": "Մաքս", "SSE.Views.DocumentHolder.textMin": "Նվազ.", "SSE.Views.DocumentHolder.textMore": "Ավելի շատ գործառույթներ", "SSE.Views.DocumentHolder.textMoreFormats": "Ավելի շատ ձևաչափեր", "SSE.Views.DocumentHolder.textNone": "Ոչ մեկը", "SSE.Views.DocumentHolder.textNumbering": "Համարակալում", "SSE.Views.DocumentHolder.textReplace": "Փոխարինել նկարը", "SSE.Views.DocumentHolder.textRotate": "Պտտել", "SSE.Views.DocumentHolder.textRotate270": "Պտտել 90° ժամացույցի սլաքի հակառակ ուղղությամբ", "SSE.Views.DocumentHolder.textRotate90": "Պտտել 90° ժամացույցի սլաքի ուղղությամբ", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Հավասարեցնել ներքևից", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Հավասարեցնել կենտրոնով", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Հավասարեցնել ձախից", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Հավասարեցնել մեջտեղով", "SSE.Views.DocumentHolder.textShapeAlignRight": "Հավասարեցնել աջից", "SSE.Views.DocumentHolder.textShapeAlignTop": "Հավասարեցնել վերևից", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Գումար", "SSE.Views.DocumentHolder.textUndo": "Հետարկել", "SSE.Views.DocumentHolder.textUnFreezePanes": "Ապասառեցնել փեղկերը", "SSE.Views.DocumentHolder.textVar": "Տրբ", "SSE.Views.DocumentHolder.tipMarkersArrow": "Սլաքաձև պարբերակներ", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Ստուգանիշ պարբերակներ", "SSE.Views.DocumentHolder.tipMarkersDash": "Գծիկ պարբերակներ", "SSE.Views.DocumentHolder.tipMarkersFRhombus": " Լցված շեղանկյուն պարբերակներ", "SSE.Views.DocumentHolder.tipMarkersFRound": "Լցված կլոր պարբերակներ", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Լցված քառակուսի պարբերակներ", "SSE.Views.DocumentHolder.tipMarkersHRound": "Դատարկ կլոր պարբերակներ", "SSE.Views.DocumentHolder.tipMarkersStar": "Աստղաձև պարբերակներ", "SSE.Views.DocumentHolder.topCellText": "Հավասարեցնել վերևից", "SSE.Views.DocumentHolder.txtAccounting": "Հաշվապահություն", "SSE.Views.DocumentHolder.txtAddComment": "Ավելացնել մեկնաբանություն", "SSE.Views.DocumentHolder.txtAddNamedRange": "Սահմանել անունը", "SSE.Views.DocumentHolder.txtArrange": "Դասավորել", "SSE.Views.DocumentHolder.txtAscending": "Աճման կարգով", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Ինքնահարմարեցնել սյունակի լայնքը", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Ինքնահարմարեցնել տողի բարձրությունը", "SSE.Views.DocumentHolder.txtClear": "Մաքրել", "SSE.Views.DocumentHolder.txtClearAll": "Բոլորը", "SSE.Views.DocumentHolder.txtClearComments": "Մեկնաբանություններ", "SSE.Views.DocumentHolder.txtClearFormat": "Ձևաչափ", "SSE.Views.DocumentHolder.txtClearHyper": "Հիպերհղումներ", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Մաքրել ընտրված կայծգծի խմբերը", "SSE.Views.DocumentHolder.txtClearSparklines": "Մաքրել ընտրված կայծերը", "SSE.Views.DocumentHolder.txtClearText": "Տեքստ", "SSE.Views.DocumentHolder.txtColumn": "Ամբողջ սյունակը", "SSE.Views.DocumentHolder.txtColumnWidth": "Սահմանեք սյունակի լայնությունը", "SSE.Views.DocumentHolder.txtCondFormat": "Պայմանական ձևավորում", "SSE.Views.DocumentHolder.txtCopy": "Պատճենել", "SSE.Views.DocumentHolder.txtCurrency": "Տարադրամ", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Հատուկ սյունակի լայնություն", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Հատուկ տողի բարձրություն", "SSE.Views.DocumentHolder.txtCustomSort": "Հարմարեցրած տեսակավորում", "SSE.Views.DocumentHolder.txtCut": "Կտրել", "SSE.Views.DocumentHolder.txtDate": "Ամիս-ամսաթիվ", "SSE.Views.DocumentHolder.txtDelete": "Ջնջել", "SSE.Views.DocumentHolder.txtDescending": "Նվազող", "SSE.Views.DocumentHolder.txtDistribHor": "Հորիզոնական բաշխում", "SSE.Views.DocumentHolder.txtDistribVert": "Ուղղահայաց բաշխում", "SSE.Views.DocumentHolder.txtEditComment": "Խմբագրել մեկնաբանությունը", "SSE.Views.DocumentHolder.txtFilter": "Զտիչ", "SSE.Views.DocumentHolder.txtFilterCellColor": "Զտել ըստ վանդակի գույնի", "SSE.Views.DocumentHolder.txtFilterFontColor": "Զտել ըստ տառատեսակի գույնի", "SSE.Views.DocumentHolder.txtFilterValue": "Զտել ըստ ընտրված վանդակի արժեքի", "SSE.Views.DocumentHolder.txtFormula": "Տեղադրել գործառույթը", "SSE.Views.DocumentHolder.txtFraction": "Կոտորակ", "SSE.Views.DocumentHolder.txtGeneral": "Ընդհանուր", "SSE.Views.DocumentHolder.txtGetLink": "Ստացեք այս տիրույթի հղումը", "SSE.Views.DocumentHolder.txtGroup": "Խումբ", "SSE.Views.DocumentHolder.txtHide": "Թաքցնել", "SSE.Views.DocumentHolder.txtInsert": "Զետեղել", "SSE.Views.DocumentHolder.txtInsHyperlink": "Գերհղում", "SSE.Views.DocumentHolder.txtNumber": "Թվային", "SSE.Views.DocumentHolder.txtNumFormat": "Թվերի ձևաչափ", "SSE.Views.DocumentHolder.txtPaste": "Փակցնել", "SSE.Views.DocumentHolder.txtPercentage": "Տոկոսային", "SSE.Views.DocumentHolder.txtReapply": "Կրկին դիմել", "SSE.Views.DocumentHolder.txtRefresh": "Թարմացնել", "SSE.Views.DocumentHolder.txtRow": "Ամբողջ շարքը", "SSE.Views.DocumentHolder.txtRowHeight": "Սահմանեք տողի բարձրությունը", "SSE.Views.DocumentHolder.txtScientific": "Գիտական", "SSE.Views.DocumentHolder.txtSelect": "Ընտրել", "SSE.Views.DocumentHolder.txtShiftDown": "Տեղափոխեք բջիջները ներքև", "SSE.Views.DocumentHolder.txtShiftLeft": "Տեղափոխեք վանդակները ձախ", "SSE.Views.DocumentHolder.txtShiftRight": "Տեղափոխեք բջիջները աջ", "SSE.Views.DocumentHolder.txtShiftUp": "Տեղափոխեք բջիջները վերև", "SSE.Views.DocumentHolder.txtShow": "Ցույց տալ", "SSE.Views.DocumentHolder.txtShowComment": "Ցույց տալ մեկնաբանությունը", "SSE.Views.DocumentHolder.txtSort": "Տեսակավորել", "SSE.Views.DocumentHolder.txtSortCellColor": "Ընտրված բջջային գույնը վերևում", "SSE.Views.DocumentHolder.txtSortFontColor": "Ընտրված տառատեսակի գույնը վերևում", "SSE.Views.DocumentHolder.txtSparklines": "կայծգծեր", "SSE.Views.DocumentHolder.txtText": "Տեքստ", "SSE.Views.DocumentHolder.txtTextAdvanced": "Պարբերության ընդլայնված կարգավորումներ", "SSE.Views.DocumentHolder.txtTime": "Ժամանակ", "SSE.Views.DocumentHolder.txtUngroup": "Ապախմբավորել", "SSE.Views.DocumentHolder.txtWidth": "Լայնք", "SSE.Views.DocumentHolder.unicodeText": "Յունիկոդ", "SSE.Views.DocumentHolder.vertAlignText": "Ուղղաձիգ հավասարեցում", "SSE.Views.ExternalLinksDlg.closeButtonText": "Փակել", "SSE.Views.ExternalLinksDlg.textDelete": "Ընդհատել հղումները", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Ընդհատել բոլոր հղումները", "SSE.Views.ExternalLinksDlg.textOk": "Լավ", "SSE.Views.ExternalLinksDlg.textSource": "Աղբյուր", "SSE.Views.ExternalLinksDlg.textStatus": "Կարգավիճակ", "SSE.Views.ExternalLinksDlg.textUnknown": "Անհայտ", "SSE.Views.ExternalLinksDlg.textUpdate": "Արդիացնել արժեքները", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Արդիացնել բոլորը", "SSE.Views.ExternalLinksDlg.textUpdating": "Արդիացում...", "SSE.Views.ExternalLinksDlg.txtTitle": "Արտաքին հղումներ", "SSE.Views.FieldSettingsDialog.strLayout": "Դասավորություն ", "SSE.Views.FieldSettingsDialog.strSubtotals": "Ենթագումարներ", "SSE.Views.FieldSettingsDialog.textReport": "Հաշվետվության ձև", "SSE.Views.FieldSettingsDialog.textTitle": "Դաշտի կարգավորումներ", "SSE.Views.FieldSettingsDialog.txtAverage": "ՄԻՋԻՆ", "SSE.Views.FieldSettingsDialog.txtBlank": "Տեղադրեք դատարկ տողեր յուրաքանչյուր կետից հետո", "SSE.Views.FieldSettingsDialog.txtBottom": "Ցույց տալ խմբի ներքևում", "SSE.Views.FieldSettingsDialog.txtCompact": "Կոմպակտ", "SSE.Views.FieldSettingsDialog.txtCount": "Հաշվել", "SSE.Views.FieldSettingsDialog.txtCountNums": "Հաշվեք թվեր", "SSE.Views.FieldSettingsDialog.txtCustomName": "Մուտքագրել հարմարեցված անուն", "SSE.Views.FieldSettingsDialog.txtEmpty": "Ցուցադրել առանց տվյալների տարրերը", "SSE.Views.FieldSettingsDialog.txtMax": "Մաքս", "SSE.Views.FieldSettingsDialog.txtMin": "Նվազ.", "SSE.Views.FieldSettingsDialog.txtOutline": "Ուրվագիծ", "SSE.Views.FieldSettingsDialog.txtProduct": "Արտադրանք ", "SSE.Views.FieldSettingsDialog.txtRepeat": "Կրկնեք տարրերի պիտակները յուրաքանչյուր տողում", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Ցույց տալ ենթագումարները", "SSE.Views.FieldSettingsDialog.txtSourceName": "Աղբյուրի անվանումը․", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Գումար", "SSE.Views.FieldSettingsDialog.txtSummarize": "Գործառույթներ ենթագումարների համար", "SSE.Views.FieldSettingsDialog.txtTabular": "Աղյուսակային", "SSE.Views.FieldSettingsDialog.txtTop": "Ցույց տալ խմբի վերևում", "SSE.Views.FieldSettingsDialog.txtVar": "Տրբ", "SSE.Views.FieldSettingsDialog.txtVarp": "Տրբհմխ", "SSE.Views.FileMenu.btnBackCaption": "Բացել նիշքի պանակը", "SSE.Views.FileMenu.btnCloseMenuCaption": "Փակել ընտրացանկը", "SSE.Views.FileMenu.btnCreateNewCaption": "Ստեղծել նորը", "SSE.Views.FileMenu.btnDownloadCaption": "Ներբեռնել որպես", "SSE.Views.FileMenu.btnExitCaption": "Փակել", "SSE.Views.FileMenu.btnFileOpenCaption": "Բացել", "SSE.Views.FileMenu.btnHelpCaption": "Օգնություն", "SSE.Views.FileMenu.btnHistoryCaption": "Տարբերակի պատմություն", "SSE.Views.FileMenu.btnInfoCaption": "Աղյուսակաթերթի մասին", "SSE.Views.FileMenu.btnPrintCaption": "Տպել", "SSE.Views.FileMenu.btnProtectCaption": "Պաշտպանել", "SSE.Views.FileMenu.btnRecentFilesCaption": "Բացել վերջինը", "SSE.Views.FileMenu.btnRenameCaption": "Վերանվանել", "SSE.Views.FileMenu.btnReturnCaption": "Վերադառնալ աղյուսակաթերթ", "SSE.Views.FileMenu.btnRightsCaption": "Մատչման իրավունքներ", "SSE.Views.FileMenu.btnSaveAsCaption": "Պահպանել որպես", "SSE.Views.FileMenu.btnSaveCaption": "Պահպանել", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Պահպանել պատճենը որպես", "SSE.Views.FileMenu.btnSettingsCaption": "Հավելյալ կարգավորումներ", "SSE.Views.FileMenu.btnToEditCaption": "Խմբագրել աղյուսակաթերթը", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Դատարկ աղյուսակ", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Ստեղծել նորը", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Գործադրել", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Հավելել հեղինակին", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Դնել տեքստ", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Հավելված", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Հեղինակ", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Փոխել մատչման իրավունքները", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Մեկնաբանություն", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Ստեղծված", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Վերջին փոփոխման հեղինակ", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Վերջին փոփոխումը", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Տնօրինող", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Տեղ", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Իրավունքներ ունեցող անձինք", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Նյութ", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Պիտակներ", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Վերնագիր", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Վերբեռնվել է", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Փոխել մատչման իրավունքները", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Իրավունքներ ունեցող անձինք", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Գործադրել", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Համախմբագրման աշխատակարգ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Օգտագործեք 1904 թվականի ամսաթվի համակարգը", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Տասնորդական բաժանարար", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Բառարանի լեզու", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Արագ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Տառատեսակի ուրվագծի հարթեցում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Բանաձևերի լեզու", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Օրինակ՝ SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Անտեսել ՄԵԾԱՏԱՌԵՐՈՎ բառերը", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Անտեսել թվերով բառերը", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Մակրոների կարգավորումներ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Ցուցադրել «Կպցնել ընտրանքներ» կոճակները`բովանդակությունը կպցնելիս", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Հղումների R1C1 ոճ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Տարածաշրջանային կարգավորումներ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Օրինակ:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Ցույց տալ մեկնաբանությունները թերթիկում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Ցուցադրել այլ օգտատերերի կատարած փոփոխությունները", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Ցույց տալ լուծված մեկնաբանությունները", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Խիստ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Ինտերֆեյսի թեմա", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Հազարների բաժանարար", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Չափման միավոր", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Օգտագործեք տարանջատիչներ՝ հիմնված տարածաշրջանային պարամետրերի վրա", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Սկզբնադիր խոշորացման արժեք", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "10 րոպեն մեկ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "30 րոպեն մեկ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "5 րոպեն մեկ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Ժամը մեկ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Ինքնավերականգնում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Ինքնաշխատ պահպանում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Անջատված", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Միջանկյալ տարբերակների պահպանում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Ամեն րոպե", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Հղման ոճ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Ինքնաշտկման ընտրանքներ․․․", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Բելառուսերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Բուլղարերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Կատալոներեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Հիշապահեստի լռելյայն աշխատաձև", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Հաշվարկում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Սանտիմետր", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Համագործակցություն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Չեխերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Դանիերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Գերմաներեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Խմբագրում և պահպանում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Հունարեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Անգլերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Իսպաներեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Իրական ժամանակում համատեղ խմբագրում:Բոլոր փոփոխությունները պահվում են ավտոմատ կերպով", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Ֆիններեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "ֆրանսերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Հունգարերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Ինդոնեզերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Մտչ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Իտալերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Ճապոներեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Կորեերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Լաոսերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Լատվիերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "ինչպես OS X-ում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Հիմնական", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "նորվեգերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Հոլանդերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Լեհերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Ստուգում ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Կետ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Պորտուգալերեն (Բրազիլիա)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Պորտուգալերեն (Պորտուգալիա)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Ցուցադրել «Արագ տպել» կոճակը խմբագրի վերնագրում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Փաստաթուղթը կտպվի վերջին ընտրված կամ սկզբնադիր տպիչի վրա", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Տարածաշրջան", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Ռումիներեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Ռուսերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Միացնել բոլորը", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Առանց ծանուցման միացնել բոլոր մակրոները", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Սլովակերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Սլովեներեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Անջատել ամեն ինչ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Առանց ծանուցման անջատել բոլոր մակրոները", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Օգտագործեք «Պահպանել» կոճակը՝ Ձեր և մյուսների կատարած փոփոխությունները համաժամեցնելու համար", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Շվեդերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Թուրքերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "ուկրաիներեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Օգտագործեք Alt ստեղնը՝ ստեղնաշարի միջոցով օգտագործողի միջերեսում նավարկելու համար", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Օգտագործեք  Ընտրանք ստեղնը՝ ստեղնաշարի միջոցով օգտագործողի միջերեսում նավարկելու համար", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Վիետնամերեն", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Ցուցադրել ծանուցումը", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Ծանուցմամբ անջատել բոլոր մակրոները", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "ինչպես Windows-ում", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Աշխատատարածք", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Չինական", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Զգուշացում", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Գաղտնաբառով", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Պաշտպանել աղյուսակաթերթը", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Ստորագրությամբ", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Խմբագրել աղյուսակաթերթը", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Խմբագրումը կվերացնի աղյուսակաթերթի ստորագրությունները։<br>Շարունակե՞լ։", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Այս աղյուսակաթերթը գաղտնաբառով պաշտպանված է", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Այս աղյուսակաթերթն անհրաժեշտ է ստորագրել։", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Աղյուսակաթերթում ավելացվել են վավեր ստորագրություններ։ Աղյուսակաթերթը պաշտպանված է և չի կարող խմբագրվել։", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Աղյուսակաթերթի որոշ թվանշային ստորագրություններ անվավեր են կամ չեն կարող ստուգվել։ Աղյուսակաթերթը պաշտպանված է և չի կարող խմբագրվել։", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Դիտել ստորագրությունները", "SSE.Views.FormatRulesEditDlg.fillColor": "Լցման գույն", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Զգուշացում", "SSE.Views.FormatRulesEditDlg.text2Scales": "2-գույնանի սանդղակ", "SSE.Views.FormatRulesEditDlg.text3Scales": "3-գույնանի սանդղակ", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Բոլոր եզրագծերը", "SSE.Views.FormatRulesEditDlg.textAppearance": "Գոտու տեսքը", "SSE.Views.FormatRulesEditDlg.textApply": "Գործադրելընդգրկույթի", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Ինքնաշխատ", "SSE.Views.FormatRulesEditDlg.textAxis": "Առանցք", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Գոտու ուղղությունը", "SSE.Views.FormatRulesEditDlg.textBold": "Թավատառ", "SSE.Views.FormatRulesEditDlg.textBorder": "Եզրագիծ", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Սահմանների գույնը", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Եզրագծի ոճ", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Ներքևի սահմաններ", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Անհնար է հավելել պայմանական ձեւաչափում", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Վանդակի միջակետը.", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Ուղղահայաց սահմանների ներսում", "SSE.Views.FormatRulesEditDlg.textClear": "Մաքրել", "SSE.Views.FormatRulesEditDlg.textColor": "Տեքստի գույն", "SSE.Views.FormatRulesEditDlg.textContext": "Համատեքստ", "SSE.Views.FormatRulesEditDlg.textCustom": "Հարմարեցված", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Անկյունագծային իջնող եզրագիծ", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Անկյունագծային բարձրացող եզրագիծ", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Մուտքագրեք վավեր բանաձև", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Ձեր մուտքագրած բանաձևը չի գնահատվում թվի, ամսաթվի, ժամանակի կամ տողի:", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Մուտքագրեք արժեք:", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Ձեր մուտքագրած արժեքը վավեր թիվ, ամսաթիվ, ժամ կամ տող չէ:", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "{0}-ի արժեքը պետք է ավելի մեծ լինի, քան {1}-ի արժեքը:", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Մուտքագրեք թիվ {0}-ի և {1}-ի միջև:", "SSE.Views.FormatRulesEditDlg.textFill": "Լցնել", "SSE.Views.FormatRulesEditDlg.textFormat": "Ձևաչափ", "SSE.Views.FormatRulesEditDlg.textFormula": "Բանաձև ", "SSE.Views.FormatRulesEditDlg.textGradient": "Գրադիենտ", "SSE.Views.FormatRulesEditDlg.textIconLabel": "երբ {0} {1} և", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "երբ {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "երբ արժեքը", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Մեկ կամ մի քանի պատկերակների տվյալների ընդգրկույթներ համընկնում են:<br>Կարգավորեք պատկերակների տվյալների տիրույթի արժեքները, որպեսզի միջակայքերը չհամընկնեն:", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Պատկերակի ոճ", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Ներքին եզրագծեր", "SSE.Views.FormatRulesEditDlg.textInvalid": "Անվավեր տվյալների տիրույթ:", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.FormatRulesEditDlg.textItalic": "Շեղատառ", "SSE.Views.FormatRulesEditDlg.textItem": "Նյութ", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Ձախից աջ", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Ձախ սահմաններ", "SSE.Views.FormatRulesEditDlg.textLongBar": "ամենաերկար բար", "SSE.Views.FormatRulesEditDlg.textMaximum": "Առավելագույն", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Առավելագույն կետ", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Հորիզոնական սահմանների ներսում", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Միջնակետ", "SSE.Views.FormatRulesEditDlg.textMinimum": "Նվազագույն", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Բարձր․ կետ", "SSE.Views.FormatRulesEditDlg.textNegative": "Բացասական", "SSE.Views.FormatRulesEditDlg.textNewColor": "Հավելել նոր հարմարեցված գույն", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Առանց եզրագծերի", "SSE.Views.FormatRulesEditDlg.textNone": "Ոչ մեկը", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Նշված արժեքներից մեկը կամ մի քանիսը վավեր տոկոս չեն:", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Նշված {0} արժեքը վավեր տոկոս չէ:", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Նշված արժեքներից մեկը կամ մի քանիսը վավեր տոկոսային չէ:", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Նշված {0} արժեքը վավեր տոկոսային չէ:", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Արտաքին Եզրագծեր", "SSE.Views.FormatRulesEditDlg.textPercent": "Տոկոսային", "SSE.Views.FormatRulesEditDlg.textPercentile": "Տոկոսիկ", "SSE.Views.FormatRulesEditDlg.textPosition": "Դիրք", "SSE.Views.FormatRulesEditDlg.textPositive": "Դրական", "SSE.Views.FormatRulesEditDlg.textPresets": "Նախադրվածներ", "SSE.Views.FormatRulesEditDlg.textPreview": "Նախադիտել", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Դուք չեք կարող օգտագործել հարաբերական հղումներ պայմանական ձևաչափման չափանիշներում գունային մասշտաբների, տվյալների գծերի և պատկերակների հավաքածուների համար:", "SSE.Views.FormatRulesEditDlg.textReverse": "Պատկերների հակառակ կարգը", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Աջից ձախ", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Աջ սահմաններ", "SSE.Views.FormatRulesEditDlg.textRule": "Կանոն", "SSE.Views.FormatRulesEditDlg.textSameAs": "Նույնը՝ դրական", "SSE.Views.FormatRulesEditDlg.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.FormatRulesEditDlg.textShortBar": "ամենակարճ բար", "SSE.Views.FormatRulesEditDlg.textShowBar": "Ցուցադրել միայն բարը", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Ցույց տալ միայն պատկերակը", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Հղման այս տեսակը չի կարող օգտագործվել պայմանական ձևաչափման բանաձևում:<br>Փոխեք հղումը մեկ բջիջի կամ օգտագործեք հղումը աշխատանքային թերթիկի գործառույթով, ինչպիսին է =SUM(A1:B5):", "SSE.Views.FormatRulesEditDlg.textSolid": "Պինդ", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Գծաջնջված", "SSE.Views.FormatRulesEditDlg.textSubscript": "Վարգիր", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Վերգիր", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Վերևի սահմանները", "SSE.Views.FormatRulesEditDlg.textUnderline": "Ընդգծված", "SSE.Views.FormatRulesEditDlg.tipBorders": "Եզրագծեր", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Թվերի ձևաչափ", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Հաշվապահություն", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Տարադրամ", "SSE.Views.FormatRulesEditDlg.txtDate": "Ամիս-ամսաթիվ", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.FormatRulesEditDlg.txtFraction": "Կոտորակ", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Ընդհանուր", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "պատկեր չկա", "SSE.Views.FormatRulesEditDlg.txtNumber": "Թվային", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Տոկոսային", "SSE.Views.FormatRulesEditDlg.txtScientific": "Գիտական", "SSE.Views.FormatRulesEditDlg.txtText": "Տեքստ", "SSE.Views.FormatRulesEditDlg.txtTime": "Ժամանակ", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Խմբագրել ֆորմատավորման կանոնը", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Ֆորմատավորման նոր կանոն", "SSE.Views.FormatRulesManagerDlg.guestText": "Հյուր", "SSE.Views.FormatRulesManagerDlg.lockText": "Կողպված է", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev above average", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 սով. շեղ. Միջինից ներքև", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 սվ շղ. Միջինից վերև", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 սով. շեղ. Միջինից ներքև", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 սով. շեղ. Միջինից վերև", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 սով. շեղ. Միջինից ներքև", "SSE.Views.FormatRulesManagerDlg.textAbove": "Միջինից բարձր", "SSE.Views.FormatRulesManagerDlg.textApply": "Դիմել", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Վանդակի արժեքը սկսվում է", "SSE.Views.FormatRulesManagerDlg.textBelow": "Միջինից ցածր", "SSE.Views.FormatRulesManagerDlg.textBetween": "գտնվում է {0}-ի և {1}-ի միջև", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Վանդակի արժեքը", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Գնահատված գունային սանդղակ", "SSE.Views.FormatRulesManagerDlg.textContains": "Վանդակի արժեքը պարունակում է", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Վանդակը պարունակում է դատարկ արժեք", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Վանդակը պարունակում է դատարկ արժեք", "SSE.Views.FormatRulesManagerDlg.textDelete": "Ջնջել", "SSE.Views.FormatRulesManagerDlg.textDown": "Տեղափոխել կանոնը ներքև", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Կրկնօրինակ արժեքներ", "SSE.Views.FormatRulesManagerDlg.textEdit": "Խմբագրել", "SSE.Views.FormatRulesManagerDlg.textEnds": "Վանդակի արժեքը ավարտվում է", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Հավասար կամ միջինից բարձր", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Հավասար կամ միջինից ցածր", "SSE.Views.FormatRulesManagerDlg.textFormat": "Ձևաչափ", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Սրբապատկերների հավաքածու", "SSE.Views.FormatRulesManagerDlg.textNew": "Նոր", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "{0}-ի և {1}-ի միջև չէ", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Վանդակի արժեքը չի պարունակում", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Վանդակը չի պարունակում դատարկ արժեք", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Վանդակը չի պարունակում սխալ", "SSE.Views.FormatRulesManagerDlg.textRules": "Կանոններ", "SSE.Views.FormatRulesManagerDlg.textScope": "Ցուցադրել ձևաչափման կանոնները", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.FormatRulesManagerDlg.textSelection": "Ընթացիկ ընտրություն", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Այս առանցքը", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Այս աշխատանքային թերթիկը", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Այս աղյուսակը", "SSE.Views.FormatRulesManagerDlg.textUnique": "Եզակի արժեքներ", "SSE.Views.FormatRulesManagerDlg.textUp": "Տեղափոխել կանոնը վերև", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Այս տարրը խմբագրվում է մեկ այլ օգտվողի կողմից:", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Պայմանական ձևավորում", "SSE.Views.FormatSettingsDialog.textCategory": "Կարգավիճակ", "SSE.Views.FormatSettingsDialog.textDecimal": "Տասնորդական", "SSE.Views.FormatSettingsDialog.textFormat": "Ձևաչափ", "SSE.Views.FormatSettingsDialog.textLinked": "Կապված է աղբյուրի հետ", "SSE.Views.FormatSettingsDialog.textSeparator": "Օգտագործեք 1000 տարանջատիչ", "SSE.Views.FormatSettingsDialog.textSymbols": "Նշաններ", "SSE.Views.FormatSettingsDialog.textTitle": "Թվերի ձևաչափ", "SSE.Views.FormatSettingsDialog.txtAccounting": "Հաշվապահություն", "SSE.Views.FormatSettingsDialog.txtAs10": "Տասներորդներ (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Հարյուրերորդներ (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Տասնվեցերորդներ (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Կեսեր (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Քառորդներ (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Ութերորդներ (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Տարադրամ", "SSE.Views.FormatSettingsDialog.txtCustom": "Հարմարեցված", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Խնդրում ենք ուշադիր մուտքագրել հատուկ համարի ձևաչափը: Աղյուսակների խմբագրիչը չի ստուգում հատուկ ձևաչափերը սխալների համար, որոնք կարող են ազդել xlsx ֆայլի վրա:", "SSE.Views.FormatSettingsDialog.txtDate": "Ամիս-ամսաթիվ", "SSE.Views.FormatSettingsDialog.txtFraction": "Կոտորակ", "SSE.Views.FormatSettingsDialog.txtGeneral": "Ընդհանուր", "SSE.Views.FormatSettingsDialog.txtNone": "Ոչ մեկը", "SSE.Views.FormatSettingsDialog.txtNumber": "Թվային", "SSE.Views.FormatSettingsDialog.txtPercentage": "Տոկոսային", "SSE.Views.FormatSettingsDialog.txtSample": "Նմուշ:", "SSE.Views.FormatSettingsDialog.txtScientific": "Գիտական", "SSE.Views.FormatSettingsDialog.txtText": "Տեքստ", "SSE.Views.FormatSettingsDialog.txtTime": "Ժամանակ", "SSE.Views.FormatSettingsDialog.txtUpto1": "Մինչև մեկ նիշ (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Մինչև երկու նիշ (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Մինչև երեք նիշ (131/135)", "SSE.Views.FormulaDialog.sDescription": "Նկարագրություն", "SSE.Views.FormulaDialog.textGroupDescription": "Ընտրեք Ֆունկցիոնալ խումբ", "SSE.Views.FormulaDialog.textListDescription": "Ընտրեք Գործառույթ", "SSE.Views.FormulaDialog.txtRecommended": "Առաջարկվում է", "SSE.Views.FormulaDialog.txtSearch": "Որոնել", "SSE.Views.FormulaDialog.txtTitle": "Տեղադրել գործառույթը", "SSE.Views.FormulaTab.textAutomatic": "Ինքնաշխատ", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Հաշվարկել ընթացիկ թերթիկը", "SSE.Views.FormulaTab.textCalculateWorkbook": "Հաշվարկել աշխատանքային գրքույկը", "SSE.Views.FormulaTab.textManual": "Ձեռնադիր", "SSE.Views.FormulaTab.tipCalculate": "Հաշվարկել", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Հաշվեք ամբողջ աշխատանքային գրքույկը", "SSE.Views.FormulaTab.tipWatch": "Ավելացնել վանդակներ Հետևման պատուհան ցանկում", "SSE.Views.FormulaTab.txtAdditional": "Հավելյալ", "SSE.Views.FormulaTab.txtAutosum": "Ինքնագումարում", "SSE.Views.FormulaTab.txtAutosumTip": "Ամփոփում", "SSE.Views.FormulaTab.txtCalculation": "Հաշվարկ", "SSE.Views.FormulaTab.txtFormula": "Ֆունկցիա", "SSE.Views.FormulaTab.txtFormulaTip": "Տեղադրել գործառույթը", "SSE.Views.FormulaTab.txtMore": "Ավելի շատ գործառույթներ", "SSE.Views.FormulaTab.txtRecent": "Վերջերս օգտագործված", "SSE.Views.FormulaTab.txtWatch": "Հետևման պատուհան", "SSE.Views.FormulaWizard.textAny": "ցանկացած", "SSE.Views.FormulaWizard.textArgument": "Արգումենտ", "SSE.Views.FormulaWizard.textFunction": "Ֆունկցիա", "SSE.Views.FormulaWizard.textFunctionRes": "Ֆունկցիայի փաստարկներ", "SSE.Views.FormulaWizard.textHelp": "Օգնություն այս գործառույթի համար", "SSE.Views.FormulaWizard.textLogical": "Տրամաբանական", "SSE.Views.FormulaWizard.textNoArgs": "Այս ֆունկցիան չունի փաստարկներ", "SSE.Views.FormulaWizard.textNumber": "Թվային", "SSE.Views.FormulaWizard.textRef": "հղում", "SSE.Views.FormulaWizard.textText": "Տեքստ", "SSE.Views.FormulaWizard.textTitle": "Ֆունկցիայի փաստարկներ", "SSE.Views.FormulaWizard.textValue": "Բանաձևի արդյունք", "SSE.Views.HeaderFooterDialog.textAlign": "Հավասարեցնել ըստ էջի լուսանցքների", "SSE.Views.HeaderFooterDialog.textAll": "Բոլոր էջեր", "SSE.Views.HeaderFooterDialog.textBold": "Թավատառ", "SSE.Views.HeaderFooterDialog.textCenter": "Կենտրոն", "SSE.Views.HeaderFooterDialog.textColor": "Տեքստի գույն", "SSE.Views.HeaderFooterDialog.textDate": "Ամիս-ամսաթիվ", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Տարբեր առաջին էջ", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Կենտ ու զույգ էջերը՝ տարբեր", "SSE.Views.HeaderFooterDialog.textEven": "Զույգ էջ", "SSE.Views.HeaderFooterDialog.textFileName": "Նիշքի անուն", "SSE.Views.HeaderFooterDialog.textFirst": "Առաջին էջ", "SSE.Views.HeaderFooterDialog.textFooter": "Էջատակ", "SSE.Views.HeaderFooterDialog.textHeader": "Էջագլուխ", "SSE.Views.HeaderFooterDialog.textInsert": "Զետեղել", "SSE.Views.HeaderFooterDialog.textItalic": "Շեղատառ", "SSE.Views.HeaderFooterDialog.textLeft": "Ձախ", "SSE.Views.HeaderFooterDialog.textMaxError": "Ձեր մուտքագրած տեքստային տողը չափազանց երկար է: Նվազեցրեք օգտագործվող նիշերի քանակը:", "SSE.Views.HeaderFooterDialog.textNewColor": "Հավելել նոր հարմարեցված գույն", "SSE.Views.HeaderFooterDialog.textOdd": "Կենտ էջ", "SSE.Views.HeaderFooterDialog.textPageCount": "Էջերի հաշվարկ", "SSE.Views.HeaderFooterDialog.textPageNum": "Էջահամար", "SSE.Views.HeaderFooterDialog.textPresets": "Նախադրվածներ", "SSE.Views.HeaderFooterDialog.textRight": "Աջ", "SSE.Views.HeaderFooterDialog.textScale": "Փոխել սանդղումը փաստաթղթի հետ", "SSE.Views.HeaderFooterDialog.textSheet": "Թերթի անվանում", "SSE.Views.HeaderFooterDialog.textStrikeout": "Վրագծում", "SSE.Views.HeaderFooterDialog.textSubscript": "Վարգիր", "SSE.Views.HeaderFooterDialog.textSuperscript": "Վերգիր", "SSE.Views.HeaderFooterDialog.textTime": "Ժամանակ", "SSE.Views.HeaderFooterDialog.textTitle": "Վերնագրի/ստորատակի կարգավորումներ", "SSE.Views.HeaderFooterDialog.textUnderline": "Ընդգծված", "SSE.Views.HeaderFooterDialog.tipFontName": "Տառատեսակ ", "SSE.Views.HeaderFooterDialog.tipFontSize": "Տառատեսակի չափ", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Ցուցադրել", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Հղում դեպի՝", "SSE.Views.HyperlinkSettingsDialog.strRange": "Ընդգրկույթ", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Թերթ", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Պատճենել", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Ընտրված ընդգրկույթ", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Մուտքագրել խորագիրը այստեղ", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Մուտքագրել հղումը այստեղ", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Մուտքագրեք  գործիքահուշը այստեղ", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Արտաքին հղում", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Ստանալ հղում", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Տվյալների ներքին ընդգրկույթ", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.HyperlinkSettingsDialog.textNames": "Սահմանված անուններ", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Թերթիկներ", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Էկրանային հուշում", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Գերհղման կարգավորումներ", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Այս դաշտը պետք է լինի URL \"http://www.example.com\" ձևաչափով", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Այս դաշտը սահմանափակված է 2083 նիշով", "SSE.Views.ImageSettings.textAdvanced": "Ցուցադրել լրացուցիչ կարգավորումները", "SSE.Views.ImageSettings.textCrop": "Եզրատել", "SSE.Views.ImageSettings.textCropFill": "Լցնել", "SSE.Views.ImageSettings.textCropFit": "Հարմարեցնել", "SSE.Views.ImageSettings.textCropToShape": "Կտրել ըստ պատկերի", "SSE.Views.ImageSettings.textEdit": "Խմբագրել", "SSE.Views.ImageSettings.textEditObject": "Խմբագրել առարկան", "SSE.Views.ImageSettings.textFlip": "Շրջել", "SSE.Views.ImageSettings.textFromFile": "Նիշքից", "SSE.Views.ImageSettings.textFromStorage": "Պահեստից", "SSE.Views.ImageSettings.textFromUrl": "URL-ից", "SSE.Views.ImageSettings.textHeight": "Բարձրություն", "SSE.Views.ImageSettings.textHint270": "Պտտել 90° ժամացույցի սլաքի հակառակ ուղղությամբ", "SSE.Views.ImageSettings.textHint90": "Պտտել 90° ժամացույցի սլաքի ուղղությամբ", "SSE.Views.ImageSettings.textHintFlipH": "Շրջել հորիզոնական", "SSE.Views.ImageSettings.textHintFlipV": "Շրջել ուղղաձիգ", "SSE.Views.ImageSettings.textInsert": "Փոխարինել նկարը", "SSE.Views.ImageSettings.textKeepRatio": "Պահպանել համաչափությունը", "SSE.Views.ImageSettings.textOriginalSize": "Իրական չափ", "SSE.Views.ImageSettings.textRecentlyUsed": "Վերջերս օգտագործված", "SSE.Views.ImageSettings.textRotate90": "Պտտել 90°", "SSE.Views.ImageSettings.textRotation": "Շրջում ", "SSE.Views.ImageSettings.textSize": "Չափ", "SSE.Views.ImageSettings.textWidth": "Լայնք", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Մի շարժվեք կամ չափեք վանդակներով", "SSE.Views.ImageSettingsAdvanced.textAlt": "Այլընտրանքային տեքստ", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Նկարագրություն", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Տեսողական առարկաների այլընտրական տեքստային ներկայացում, որը ընթերցվելու է տեսողության կամ մտավոր խանգարումներով մարդկանց համար՝ օգնելու նրանց ավելի լավ հասկանալ, թե ինչ տեղեկատվություն կա նկարի, պատկերի, գծապատկերի կամ աղյուսակի վրա։", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Վերնագիր", "SSE.Views.ImageSettingsAdvanced.textAngle": "Անկյուն", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Շրջված", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Հորիզոնական ", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Տեղափոխել, բայց չչափել վանդակներով", "SSE.Views.ImageSettingsAdvanced.textRotation": "Շրջում ", "SSE.Views.ImageSettingsAdvanced.textSnap": "Վանդակի խզում", "SSE.Views.ImageSettingsAdvanced.textTitle": "Պատկեր - ընդլայնված կարգավորումներ", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Տեղափոխել և չափել վանդակներով", "SSE.Views.ImageSettingsAdvanced.textVertically": "Ուղղահայաց", "SSE.Views.ImportFromXmlDialog.textDestination": "Ընտրեք, որտեղ տեղադրել տվյալները", "SSE.Views.ImportFromXmlDialog.textExist": "Առկա աշխատանքային թերթիկ", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.ImportFromXmlDialog.textNew": "Նոր աշխատաթերթ", "SSE.Views.ImportFromXmlDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.ImportFromXmlDialog.textTitle": "Ներմուծել տվյալները", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.LeftMenu.tipAbout": "Ծրագրի մասին", "SSE.Views.LeftMenu.tipChat": "Զրույց", "SSE.Views.LeftMenu.tipComments": "Մեկնաբանություններ", "SSE.Views.LeftMenu.tipFile": "Նիշք", "SSE.Views.LeftMenu.tipPlugins": "Պլագիններ", "SSE.Views.LeftMenu.tipSearch": "Որոնել", "SSE.Views.LeftMenu.tipSpellcheck": "Ուղղագրության ստուգում", "SSE.Views.LeftMenu.tipSupport": "Հետադարձ կապ և աջակցություն", "SSE.Views.LeftMenu.txtDeveloper": "ՄՇԱԿՈՂԻ ԱՇԽԱՏԱԿԱՐԳ", "SSE.Views.LeftMenu.txtEditor": "Աղյուսակաթերթի խմբագիր", "SSE.Views.LeftMenu.txtLimit": "Սահմանափակել մուտքը", "SSE.Views.LeftMenu.txtTrial": "ՓՈՐՁԱՐԿՄԱՆ ՌԵԺԻՄ", "SSE.Views.LeftMenu.txtTrialDev": "Փորձնական մշակողի ռեժիմ", "SSE.Views.MacroDialog.textMacro": "Մակրո անունը", "SSE.Views.MacroDialog.textTitle": "Նշանակել մակրո", "SSE.Views.MainSettingsPrint.okButtonText": "Պահպանել", "SSE.Views.MainSettingsPrint.strBottom": "Ներքև", "SSE.Views.MainSettingsPrint.strLandscape": "Հորիզոնական", "SSE.Views.MainSettingsPrint.strLeft": "Ձախ", "SSE.Views.MainSettingsPrint.strMargins": "Լուսանցքներ", "SSE.Views.MainSettingsPrint.strPortrait": "Ուղղաձիգ ", "SSE.Views.MainSettingsPrint.strPrint": "Տպել", "SSE.Views.MainSettingsPrint.strPrintTitles": "Տպել վերնագրեր", "SSE.Views.MainSettingsPrint.strRight": "Աջ", "SSE.Views.MainSettingsPrint.strTop": "Վերև", "SSE.Views.MainSettingsPrint.textActualSize": "Իրական չափ", "SSE.Views.MainSettingsPrint.textCustom": "Հարմարեցված", "SSE.Views.MainSettingsPrint.textCustomOptions": "Հարմարեցված ընտրանքներ", "SSE.Views.MainSettingsPrint.textFitCols": "Տեղադրել բոլոր սյունակները մեկ էջում", "SSE.Views.MainSettingsPrint.textFitPage": "Տեղավորել թերթը մեկ էջում", "SSE.Views.MainSettingsPrint.textFitRows": "Տեղավորել բոլոր տողերը մեկ էջում", "SSE.Views.MainSettingsPrint.textPageOrientation": "Էջի կողմնորոշում", "SSE.Views.MainSettingsPrint.textPageScaling": "Սանդղում", "SSE.Views.MainSettingsPrint.textPageSize": "Էջի չափ", "SSE.Views.MainSettingsPrint.textPrintGrid": "Տպել ցանցագծերը", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Տպել տողերի և սյունակների վերնագրերը", "SSE.Views.MainSettingsPrint.textRepeat": "Կրկնել...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Կրկնել սյունակները ձախ կողմում", "SSE.Views.MainSettingsPrint.textRepeatTop": "Կրկնել տողերը վերևում", "SSE.Views.MainSettingsPrint.textSettings": "Կարգավորումներ համար", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Անվանված առկա ընդգրկույթներն այս պահին չեն կարող խմբագրվել և նորերը չեն կարող ստեղծվել,<br>քանի որ դրանց մի մասը խմբագրվում է։", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Սահմանված անուն", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Զգուշացում", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Աշխատագիրք", "SSE.Views.NamedRangeEditDlg.textDataRange": "Տվյալների տիրույթ", "SSE.Views.NamedRangeEditDlg.textExistName": "ՍԽԱԼ. Նման անունով միջակայք արդեն գոյություն ունի", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Անունը պիտի սկսվի տառով կամ ստորագծով և պիտի չունենա անթույլատրելի գրանշաններ։", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ՍԽԱԼ! Անվավեր վանդակների տիրույթ", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ՍԽԱԼ. Այս տարրը խմբագրվում է մեկ այլ օգտվողի կողմից:", "SSE.Views.NamedRangeEditDlg.textName": "Անուն", "SSE.Views.NamedRangeEditDlg.textReservedName": "Անունը, որը դուք փորձում եք օգտագործել, արդեն նշված է բջջային բանաձևերում: Խնդրում ենք օգտագործել այլ անուն:", "SSE.Views.NamedRangeEditDlg.textScope": "Շրջանակ", "SSE.Views.NamedRangeEditDlg.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Խմբագրել անունը", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Նոր անուն", "SSE.Views.NamedRangePasteDlg.textNames": "Անվանված միջակայքեր", "SSE.Views.NamedRangePasteDlg.txtTitle": "Paste Name", "SSE.Views.NameManagerDlg.closeButtonText": "Փակել", "SSE.Views.NameManagerDlg.guestText": "Հյուր", "SSE.Views.NameManagerDlg.lockText": "Կողպված է", "SSE.Views.NameManagerDlg.textDataRange": "Տվյալների տիրույթ", "SSE.Views.NameManagerDlg.textDelete": "Ջնջել", "SSE.Views.NameManagerDlg.textEdit": "Խմբագրել", "SSE.Views.NameManagerDlg.textEmpty": "Անվանված ընդգրկույթներ դեռ չեն ստեղծվել:<br>Ստեղծեք առնվազն մեկ անունով միջակայք և այն կհայտնվի այս դաշտում:", "SSE.Views.NameManagerDlg.textFilter": "Զտիչ", "SSE.Views.NameManagerDlg.textFilterAll": "Բոլորը", "SSE.Views.NameManagerDlg.textFilterDefNames": "Սահմանված անուններ", "SSE.Views.NameManagerDlg.textFilterSheet": "Անուններ, որոնք ընդգրկված են թերթի վրա", "SSE.Views.NameManagerDlg.textFilterTableNames": "Սեղանի անունները", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Անուններ, որոնք նախատեսված են աշխատանքային գրքում", "SSE.Views.NameManagerDlg.textNew": "Նոր", "SSE.Views.NameManagerDlg.textnoNames": "Ձեր ֆիլտրին համապատասխանող անվանական տիրույթներ չեն գտնվել:", "SSE.Views.NameManagerDlg.textRanges": "Անվանված միջակայքեր", "SSE.Views.NameManagerDlg.textScope": "Շրջանակ", "SSE.Views.NameManagerDlg.textWorkbook": "Աշխատագիրք", "SSE.Views.NameManagerDlg.tipIsLocked": "Այս տարրը խմբագրվում է մեկ այլ օգտվողի կողմից:", "SSE.Views.NameManagerDlg.txtTitle": "Անվան կառավարիչ", "SSE.Views.NameManagerDlg.warnDelete": "Իսկապե՞ս ուզում եք ջնջել {0} անունը:", "SSE.Views.PageMarginsDialog.textBottom": "Ներքև", "SSE.Views.PageMarginsDialog.textLeft": "Ձախ", "SSE.Views.PageMarginsDialog.textRight": "Աջ", "SSE.Views.PageMarginsDialog.textTitle": "Լուսանցքներ", "SSE.Views.PageMarginsDialog.textTop": "Վերև", "SSE.Views.ParagraphSettings.strLineHeight": "Տողամիջոց", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Միջպարբերութային տարածք", "SSE.Views.ParagraphSettings.strSpacingAfter": "Հետո", "SSE.Views.ParagraphSettings.strSpacingBefore": "Առաջ", "SSE.Views.ParagraphSettings.textAdvanced": "Ցուցադրել լրացուցիչ կարգավորումները", "SSE.Views.ParagraphSettings.textAt": "Այստեղ՝", "SSE.Views.ParagraphSettings.textAtLeast": "Առնվազն", "SSE.Views.ParagraphSettings.textAuto": "Բազմակի", "SSE.Views.ParagraphSettings.textExact": "ճշգրիտ", "SSE.Views.ParagraphSettings.txtAutoText": "Ինքնաշխատ", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Նշված ներդիրները կհայտնվեն այս դաշտում։", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Միայն մեծատառեր", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Կրկնակի վրագծում", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "նահանջներ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Ձախ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Տողամիջոց", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Աջ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Հետո", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Առաջ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Հատուկ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Ըստ", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Տառատեսակ ", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Նահանջներ և միջատարածք", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Փոքրատառեր", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Միջատարածք", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Վրագծում", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Վարգիր", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Վերգիր", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "ՆԵՐԴԻՐՆԵՐ", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Հավասարեցում", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Բազմակի", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Միջնիշային տարածք", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Սկզբնադիր սյունատ", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Էֆեկտներ", "SSE.Views.ParagraphSettingsAdvanced.textExact": "ճշգրիտ", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Առաջին տող", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Կախում", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Լայնությամբ", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ոչ մեկը)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Ջնջել", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Ջնջել բոլորը", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Հատկորոշել", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Կենտրոն", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Ձախ", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Ներդիրի դիրքը", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Աջ", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Պարբերություն- ընդլայնված կարգավորումներ", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Ինքնաշխատ", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "հավասար է", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "չի ավարտվում հետևյալով", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "պարունակում է", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "չի պարունակում", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "միջև", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "ոչ թե միջև", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "Հավասար չէ", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "ավելի մեծ է, քան", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "մեծ է կամ հավասար է", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "պակաս է քան", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "փոքր է կամ հավասար է", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "Սկսվում է ", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "չի սկսվում հետևյալով", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "Ավարտվում է ", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Ցույց տալ այն տարրերը, որոնց պիտակը.", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Ցույց տալ տարրեր, որոնց համար՝", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Օգտագործե՞լ ներկայացնել ցանկացած առանձին կերպար", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Օգտագործեք *՝ կերպարների ցանկացած շարք ներկայացնելու համար", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "և", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Պիտակի զտիչ", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Արժեքի զտիչ", "SSE.Views.PivotGroupDialog.textAuto": "Ավտոմատ", "SSE.Views.PivotGroupDialog.textBy": "Ըստ", "SSE.Views.PivotGroupDialog.textDays": "օրեր", "SSE.Views.PivotGroupDialog.textEnd": "Ավարտ.", "SSE.Views.PivotGroupDialog.textError": "Այս դաշտը պետք է լինի թվային արժեք", "SSE.Views.PivotGroupDialog.textGreaterError": "Վերջնական թիվը պետք է մեծ լինի սկզբի համարից", "SSE.Views.PivotGroupDialog.textHour": "Ժամեր", "SSE.Views.PivotGroupDialog.textMin": "Րոպե", "SSE.Views.PivotGroupDialog.textMonth": "ամիսներ", "SSE.Views.PivotGroupDialog.textNumDays": "Օրերի քանակը", "SSE.Views.PivotGroupDialog.textQuart": "Քառորդներ", "SSE.Views.PivotGroupDialog.textSec": "Վայրկյան", "SSE.Views.PivotGroupDialog.textStart": "Սկսած", "SSE.Views.PivotGroupDialog.textYear": "Տարիներ", "SSE.Views.PivotGroupDialog.txtTitle": "Խմբավորում", "SSE.Views.PivotSettings.textAdvanced": "Ցուցադրել լրացուցիչ կարգավորումները", "SSE.Views.PivotSettings.textColumns": "Սյունակներ", "SSE.Views.PivotSettings.textFields": "Ընտրեք Դաշտեր", "SSE.Views.PivotSettings.textFilters": "Զտիչներ", "SSE.Views.PivotSettings.textRows": "Տողեր", "SSE.Views.PivotSettings.textValues": "Արժեքներ", "SSE.Views.PivotSettings.txtAddColumn": "Դնել սյունակներում", "SSE.Views.PivotSettings.txtAddFilter": "Հավելել զտիչներում", "SSE.Views.PivotSettings.txtAddRow": "Դնել տողերում", "SSE.Views.PivotSettings.txtAddValues": "Հավելել արժեքներում", "SSE.Views.PivotSettings.txtFieldSettings": "Դաշտի կարգավորումներ", "SSE.Views.PivotSettings.txtMoveBegin": "Անցնել դեպի սկիզբ", "SSE.Views.PivotSettings.txtMoveColumn": "Տեղափոխել դեպի սյունակներ", "SSE.Views.PivotSettings.txtMoveDown": "Իջնել", "SSE.Views.PivotSettings.txtMoveEnd": "Տեղափոխել դեպի վերջ", "SSE.Views.PivotSettings.txtMoveFilter": "Տեղափոխել դեպի զտիչներ", "SSE.Views.PivotSettings.txtMoveRow": "Տեղափոխել դեպի տողեր", "SSE.Views.PivotSettings.txtMoveUp": "Շարժվել վերև", "SSE.Views.PivotSettings.txtMoveValues": "Անցնել արժեքներ", "SSE.Views.PivotSettings.txtRemove": "Հեռացնել դաշտը", "SSE.Views.PivotSettingsAdvanced.strLayout": "Անունը և դասավորությունը", "SSE.Views.PivotSettingsAdvanced.textAlt": "Այլընտրանքային տեքստ", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Նկարագրություն", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Տեսողական օբյեկտի տեղեկատվության այլընտրանքային տեքստի վրա հիմնված ներկայացում, որը կկարդացվի տեսողության կամ ճանաչողական խանգարումներ ունեցող մարդկանց՝ օգնելու նրանց ավելի լավ հասկանալ, թե ինչ տեղեկատվություն կա պատկերի, ինքնաձևի, գծապատկերի կամ աղյուսակի վրա:", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Վերնագիր", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Ինքնատեղավորել սյունակլների լայնությունն` արդիացնելիս", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Տվյալների տիրույթ", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Տվյալների աղբյուր", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Ցուցադրել դաշտերը հաշվետվության ֆիլտրի տարածքում", "SSE.Views.PivotSettingsAdvanced.textDown": "Ներքև, հետո վերջ", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Հանրագումարներ", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Դաշտի վերնագրեր", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.PivotSettingsAdvanced.textOver": "Ավարտ, հետո ներքև", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Ցուցադրել սյունակների համար", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Ցույց տալ դաշտերի վերնագրերը տողերի և սյունակների համար", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Ցուցադրել տողերի համար", "SSE.Views.PivotSettingsAdvanced.textTitle": "Առանցքային աղյուսակ - Ընդլայնված կարգավորումներ", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Զեկուցեք ֆիլտրի դաշտերը յուրաքանչյուր սյունակի համար", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Հաղորդել զտիչ դաշտերը յուրաքանչյուր տողի համար", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.PivotSettingsAdvanced.txtName": "Անուն", "SSE.Views.PivotTable.capBlankRows": "Դատարկ տողեր", "SSE.Views.PivotTable.capGrandTotals": "Հանրագումարներ", "SSE.Views.PivotTable.capLayout": "Հաշվետվության դասավորությունը", "SSE.Views.PivotTable.capSubtotals": "Ենթագումարներ", "SSE.Views.PivotTable.mniBottomSubtotals": "Ցույց տալ բոլոր ենթագումարները խմբի ներքևում", "SSE.Views.PivotTable.mniInsertBlankLine": "Տեղադրեք դատարկ տող յուրաքանչյուր կետից հետո", "SSE.Views.PivotTable.mniLayoutCompact": "Ցույց տալ կոմպակտ ձևով", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Չկրկնել տարրերի բոլոր մեկնագրերը", "SSE.Views.PivotTable.mniLayoutOutline": "Ցույց տալ ուրվագծային ձևով", "SSE.Views.PivotTable.mniLayoutRepeat": "Կրկնել տարրերի բոլոր մեկնագրերը", "SSE.Views.PivotTable.mniLayoutTabular": "Ցույց տալ աղյուսակային ձևով", "SSE.Views.PivotTable.mniNoSubtotals": "Մի ցուցադրեք ենթագումարները", "SSE.Views.PivotTable.mniOffTotals": "Անջատված է տողերի և սյունակների համար", "SSE.Views.PivotTable.mniOnColumnsTotals": "Միացված է միայն սյունակների համար", "SSE.Views.PivotTable.mniOnRowsTotals": "Միացված է միայն տողերի համար", "SSE.Views.PivotTable.mniOnTotals": "Միացված է տողերի և սյունակների համար", "SSE.Views.PivotTable.mniRemoveBlankLine": "Հեռացրեք դատարկ տողը յուրաքանչյուր կետից հետո", "SSE.Views.PivotTable.mniTopSubtotals": "Ցուցադրել բոլոր ենթագումարները խմբի վերևում", "SSE.Views.PivotTable.textColBanded": "Միջարկվող սյունակներ", "SSE.Views.PivotTable.textColHeader": "Սյունակների վերնագրեր", "SSE.Views.PivotTable.textRowBanded": "Միջարկվող տողեր", "SSE.Views.PivotTable.textRowHeader": "Տողերի վերնագրեր", "SSE.Views.PivotTable.tipCreatePivot": "Տեղադրել առանցքային աղյուսակը", "SSE.Views.PivotTable.tipGrandTotals": "Ցույց տալ կամ թաքցնել ընդհանուր գումարները", "SSE.Views.PivotTable.tipRefresh": "Թարմացրեք տեղեկատվությունը տվյալների աղբյուրից", "SSE.Views.PivotTable.tipRefreshCurrent": "Արդիացնել տեղեկատվությունը ընթացիկ աղյուսակի տվյալների աղբյուրից", "SSE.Views.PivotTable.tipSelect": "Ընտրեք ամբողջ առանցքային աղյուսակը", "SSE.Views.PivotTable.tipSubtotals": "Ցույց տալ կամ թաքցնել ենթագումարները", "SSE.Views.PivotTable.txtCreate": "Զետեղել աղյուսակ", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Հարմարեցված", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Մութ", "SSE.Views.PivotTable.txtGroupPivot_Light": "Լույս", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Միջին", "SSE.Views.PivotTable.txtPivotTable": "Առանցքային աղյուսակ", "SSE.Views.PivotTable.txtRefresh": "Թարմացնել", "SSE.Views.PivotTable.txtRefreshAll": "Թարմացնել բոլորը", "SSE.Views.PivotTable.txtSelect": "Ընտրել", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Առանցքաղյուսակի ոճը մուգ", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Առանցքաղյուսակի ոճը բաց", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Առանցքաղյուսակի ոճը միջին", "SSE.Views.PrintSettings.btnDownload": "Պահպանել և ներբեռնել", "SSE.Views.PrintSettings.btnPrint": "Պահպանել և տպել", "SSE.Views.PrintSettings.strBottom": "Ներքև", "SSE.Views.PrintSettings.strLandscape": "Հորիզոնական", "SSE.Views.PrintSettings.strLeft": "Ձախ", "SSE.Views.PrintSettings.strMargins": "Լուսանցքներ", "SSE.Views.PrintSettings.strPortrait": "Ուղղաձիգ ", "SSE.Views.PrintSettings.strPrint": "Տպել", "SSE.Views.PrintSettings.strPrintTitles": "Տպել վերնագրեր", "SSE.Views.PrintSettings.strRight": "Աջ", "SSE.Views.PrintSettings.strShow": "Ցույց տալ", "SSE.Views.PrintSettings.strTop": "Վերև", "SSE.Views.PrintSettings.textActualSize": "Իրական չափ", "SSE.Views.PrintSettings.textAllSheets": "Բոլոր թերթեր", "SSE.Views.PrintSettings.textCurrentSheet": "Ընթացիկ թերթիկ", "SSE.Views.PrintSettings.textCustom": "Հարմարեցված", "SSE.Views.PrintSettings.textCustomOptions": "Հարմարեցված ընտրանքներ", "SSE.Views.PrintSettings.textFitCols": "Տեղադրել բոլոր սյունակները մեկ էջում", "SSE.Views.PrintSettings.textFitPage": "Տեղավորել թերթը մեկ էջում", "SSE.Views.PrintSettings.textFitRows": "Տեղավորել բոլոր տողերը մեկ էջում", "SSE.Views.PrintSettings.textHideDetails": "Թաքցնել մանրամասները", "SSE.Views.PrintSettings.textIgnore": "Անտեսել տպման տարածքը", "SSE.Views.PrintSettings.textLayout": "Դասավորություն ", "SSE.Views.PrintSettings.textPageOrientation": "Էջի կողմնորոշում", "SSE.Views.PrintSettings.textPageScaling": "Սանդղում", "SSE.Views.PrintSettings.textPageSize": "Էջի չափ", "SSE.Views.PrintSettings.textPrintGrid": "Տպել ցանցագծերը", "SSE.Views.PrintSettings.textPrintHeadings": "Տպել տողերի և սյունակների վերնագրերը", "SSE.Views.PrintSettings.textPrintRange": "Տպման տիրույթ", "SSE.Views.PrintSettings.textRange": "Ընդգրկույթ", "SSE.Views.PrintSettings.textRepeat": "Կրկնել...", "SSE.Views.PrintSettings.textRepeatLeft": "Կրկնել սյունակները ձախ կողմում", "SSE.Views.PrintSettings.textRepeatTop": "Կրկնել տողերը վերևում", "SSE.Views.PrintSettings.textSelection": "Ընտրություն", "SSE.Views.PrintSettings.textSettings": "Թերթի կարգավորումներ", "SSE.Views.PrintSettings.textShowDetails": "Ցույց տալ մանրամասները", "SSE.Views.PrintSettings.textShowGrid": "Ցուցադրել Ցանցային գծերը", "SSE.Views.PrintSettings.textShowHeadings": "Ցույց տալ տողերի և սյունակների վերնագրերը", "SSE.Views.PrintSettings.textTitle": "Տպման կարգավորումներ", "SSE.Views.PrintSettings.textTitlePDF": "PDF կարգավորումներ", "SSE.Views.PrintTitlesDialog.textFirstCol": "Առաջին սյունակ", "SSE.Views.PrintTitlesDialog.textFirstRow": "Առաջին շարք", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Սառեցված սյուներ", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Սառեցված շարքեր", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.PrintTitlesDialog.textLeft": "Կրկնել սյունակները ձախ կողմում", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Մի՛ կրկնիր", "SSE.Views.PrintTitlesDialog.textRepeat": "Կրկնել...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Ընտրեք միջակայքը", "SSE.Views.PrintTitlesDialog.textTitle": "Տպել վերնագրեր", "SSE.Views.PrintTitlesDialog.textTop": "Կրկնել տողերը վերևում", "SSE.Views.PrintWithPreview.txtActualSize": "Իրական չափ", "SSE.Views.PrintWithPreview.txtAllSheets": "Բոլոր թերթեր", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Կիրառեք բոլոր թերթերին", "SSE.Views.PrintWithPreview.txtBottom": "Ներքև", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Ընթացիկ թերթիկ", "SSE.Views.PrintWithPreview.txtCustom": "Հարմարեցված", "SSE.Views.PrintWithPreview.txtCustomOptions": "Հարմարեցված ընտրանքներ", "SSE.Views.PrintWithPreview.txtEmptyTable": "Տպելու բան չկա, քանի որ աղյուսակը դատարկ է", "SSE.Views.PrintWithPreview.txtFitCols": "Տեղադրել բոլոր սյունակները մեկ էջում", "SSE.Views.PrintWithPreview.txtFitPage": "Տեղավորել թերթը մեկ էջում", "SSE.Views.PrintWithPreview.txtFitRows": "Տեղավորել բոլոր տողերը մեկ էջում", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Ցանցային գծեր և վերնագրեր", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Վերնագրի/ստորատակի կարգավորումներ", "SSE.Views.PrintWithPreview.txtIgnore": "Անտեսել տպման տարածքը", "SSE.Views.PrintWithPreview.txtLandscape": "Հորիզոնական", "SSE.Views.PrintWithPreview.txtLeft": "Ձախ", "SSE.Views.PrintWithPreview.txtMargins": "Լուսանցքներ", "SSE.Views.PrintWithPreview.txtOf": "{0}-ից", "SSE.Views.PrintWithPreview.txtPage": "Էջ", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Էջի համարն անվավեր է", "SSE.Views.PrintWithPreview.txtPageOrientation": "Էջի կողմնորոշում", "SSE.Views.PrintWithPreview.txtPageSize": "Էջի չափ", "SSE.Views.PrintWithPreview.txtPortrait": "Ուղղաձիգ ", "SSE.Views.PrintWithPreview.txtPrint": "Տպել", "SSE.Views.PrintWithPreview.txtPrintGrid": "Տպել ցանցագծերը", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Տպել տողերի և սյունակների վերնագրերը", "SSE.Views.PrintWithPreview.txtPrintRange": "Տպման տիրույթ", "SSE.Views.PrintWithPreview.txtPrintTitles": "Տպել վերնագրեր", "SSE.Views.PrintWithPreview.txtRepeat": "Կրկնել...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Կրկնել սյունակները ձախ կողմում", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Կրկնել տողերը վերևում", "SSE.Views.PrintWithPreview.txtRight": "Աջ", "SSE.Views.PrintWithPreview.txtSave": "Պահպանել", "SSE.Views.PrintWithPreview.txtScaling": "Սանդղում", "SSE.Views.PrintWithPreview.txtSelection": "Ընտրություն", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Թերթի պարամետրերը", "SSE.Views.PrintWithPreview.txtSheet": "Թերթ՝ {0}", "SSE.Views.PrintWithPreview.txtTop": "Վերև", "SSE.Views.ProtectDialog.textExistName": "ՍԽԱԼ. Նման վերնագրով միջակայքն արդեն գոյություն ունի", "SSE.Views.ProtectDialog.textInvalidName": "Տարածքի վերնագիրը պետք է սկսվի տառով և կարող է պարունակել միայն տառեր, թվեր և բացատներ:", "SSE.Views.ProtectDialog.textInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.ProtectDialog.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.ProtectDialog.txtAllow": "Թույլատրել այս թերթի բոլոր օգտվողներին", "SSE.Views.ProtectDialog.txtAutofilter": "Օգտագործեք ավտոմատ զտիչ", "SSE.Views.ProtectDialog.txtDelCols": "Ջնջել սյունակները", "SSE.Views.ProtectDialog.txtDelRows": "Ջնջել տողերը", "SSE.Views.ProtectDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.ProtectDialog.txtFormatCells": "Ձևաչափեք վանդակները", "SSE.Views.ProtectDialog.txtFormatCols": "Ձևաչափեք սյունակները", "SSE.Views.ProtectDialog.txtFormatRows": "Ձևաչափեք տողերը", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Հաստատման գաղտնաբառը նույնը չէ", "SSE.Views.ProtectDialog.txtInsCols": "Զետեղել սյուներ", "SSE.Views.ProtectDialog.txtInsHyper": "Տեղադրել հիպերհղում", "SSE.Views.ProtectDialog.txtInsRows": "Տեղադրել տողեր", "SSE.Views.ProtectDialog.txtObjs": "Խմբագրել օբյեկտները", "SSE.Views.ProtectDialog.txtOptional": "ընտրովի", "SSE.Views.ProtectDialog.txtPassword": "Գաղտնաբառ", "SSE.Views.ProtectDialog.txtPivot": "Օգտագործեք PivotTable և PivotChart", "SSE.Views.ProtectDialog.txtProtect": "Պաշտպանել", "SSE.Views.ProtectDialog.txtRange": "Ընդգրկույթ", "SSE.Views.ProtectDialog.txtRangeName": "Վերնագիր", "SSE.Views.ProtectDialog.txtRepeat": "Կրկնել գաղտնաբառը", "SSE.Views.ProtectDialog.txtScen": "Խմբագրել սցենարները", "SSE.Views.ProtectDialog.txtSelLocked": "Ընտրեք կողպված բջիջները", "SSE.Views.ProtectDialog.txtSelUnLocked": "Ընտրեք ապակողպված բջիջները", "SSE.Views.ProtectDialog.txtSheetDescription": "Կանխեք ուրիշների կողմից անցանկալի փոփոխությունները՝ սահմանափակելով նրանց խմբագրելու հնարավորությունը:", "SSE.Views.ProtectDialog.txtSheetTitle": "Պաշտպանել թերթը", "SSE.Views.ProtectDialog.txtSort": "Տեսակավորել", "SSE.Views.ProtectDialog.txtWarning": "Զգուշացում․ գաղտնաբառը կորցնելու կամ մոռանալու դեպքում այն ​​չի կարող վերականգնվել։Խնդրում ենք պահել այն ապահով տեղում:", "SSE.Views.ProtectDialog.txtWBDescription": "Որպեսզի այլ օգտվողներ չդիտեն թաքնված աշխատաթերթերը, ավելացնել, տեղափոխել, ջնջել կամ թաքցնել աշխատաթերթերը և վերանվանել աշխատաթերթերը, կարող եք պաշտպանել ձեր աշխատանքային գրքի կառուցվածքը գաղտնաբառով:", "SSE.Views.ProtectDialog.txtWBTitle": "Պաշտպանել աշխատագրքի կառուցվածքը", "SSE.Views.ProtectRangesDlg.guestText": "Հյուր", "SSE.Views.ProtectRangesDlg.lockText": "Կողպված է", "SSE.Views.ProtectRangesDlg.textDelete": "Ջնջել", "SSE.Views.ProtectRangesDlg.textEdit": "Խմբագրել", "SSE.Views.ProtectRangesDlg.textEmpty": "Խմբագրման համար տիրույթներ չեն թույլատրվում:", "SSE.Views.ProtectRangesDlg.textNew": "Նոր", "SSE.Views.ProtectRangesDlg.textProtect": "Պաշտպանել թերթը", "SSE.Views.ProtectRangesDlg.textPwd": "Գաղտնաբառ", "SSE.Views.ProtectRangesDlg.textRange": "Ընդգրկույթ", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Շրջանակներ, որոնք ապակողպված են գաղտնաբառով, երբ թերթը պաշտպանված է (սա աշխատում է միայն կողպված բջիջների համար)", "SSE.Views.ProtectRangesDlg.textTitle": "Վերնագիր", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Այս տարրը խմբագրվում է մեկ այլ օգտվողի կողմից:", "SSE.Views.ProtectRangesDlg.txtEditRange": "Խմբագրել միջակայքը", "SSE.Views.ProtectRangesDlg.txtNewRange": "Նոր տեսականի", "SSE.Views.ProtectRangesDlg.txtNo": "Ոչ", "SSE.Views.ProtectRangesDlg.txtTitle": "Թույլ տվեք օգտվողներին խմբագրել միջակայքերը", "SSE.Views.ProtectRangesDlg.txtYes": "Այո", "SSE.Views.ProtectRangesDlg.warnDelete": "Իսկապե՞ս ուզում եք ջնջել {0} անունը:", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Սյունակներ", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Կրկնվող արժեքները ջնջելու համար ընտրեք մեկ կամ մի քանի սյունակներ, որոնք պարունակում են կրկնօրինակներ:", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Իմ տվյալները վերնագրեր ունեն", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Ընտրել բոլորը", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Հեռացրեք կրկնօրինակները", "SSE.Views.RightMenu.txtCellSettings": "Վանդակի կարգավորումներ", "SSE.Views.RightMenu.txtChartSettings": "Գծապատկերի կարգավորումներ", "SSE.Views.RightMenu.txtImageSettings": "Նկարի կարգավորումներ", "SSE.Views.RightMenu.txtParagraphSettings": "Պարբերության կարգավորումներ", "SSE.Views.RightMenu.txtPivotSettings": "Առանցքային աղյուսակի կարգավորումներ", "SSE.Views.RightMenu.txtSettings": "Ընդհանուր կարգավորումներ", "SSE.Views.RightMenu.txtShapeSettings": "Պատկերի կարգավորումներ", "SSE.Views.RightMenu.txtSignatureSettings": "Ստորագրության կարգավորումներ", "SSE.Views.RightMenu.txtSlicerSettings": "Շերտազտիչ կարգավորումներ", "SSE.Views.RightMenu.txtSparklineSettings": "կայծգծի կարգավորումներ", "SSE.Views.RightMenu.txtTableSettings": "Աղյուսակի կարգավորումներ", "SSE.Views.RightMenu.txtTextArtSettings": "Տեքստարվեստի կարգավորումներ", "SSE.Views.ScaleDialog.textAuto": "Ավտոմատ", "SSE.Views.ScaleDialog.textError": "Մուտքագրված արժեքը սխալ է:", "SSE.Views.ScaleDialog.textFewPages": "Էջեր", "SSE.Views.ScaleDialog.textFitTo": "Տեղավորել ըստ.", "SSE.Views.ScaleDialog.textHeight": "Բարձրություն", "SSE.Views.ScaleDialog.textManyPages": "Էջեր", "SSE.Views.ScaleDialog.textOnePage": "Էջ", "SSE.Views.ScaleDialog.textScaleTo": "Սանդղակ Դեպի", "SSE.Views.ScaleDialog.textTitle": "Սանդղակի կարգավորումներ", "SSE.Views.ScaleDialog.textWidth": "Լայնք", "SSE.Views.SetValueDialog.txtMaxText": "Այս դաշտի առավելագույն արժեքը {0} է", "SSE.Views.SetValueDialog.txtMinText": "Այս դաշտի նվազագույն արժեքը {0} է", "SSE.Views.ShapeSettings.strBackground": "Ֆոնի գույն", "SSE.Views.ShapeSettings.strChange": "Փոխել ինքնաձևը", "SSE.Views.ShapeSettings.strColor": "Գույն", "SSE.Views.ShapeSettings.strFill": "Լցնել", "SSE.Views.ShapeSettings.strForeground": "Հետնագույն", "SSE.Views.ShapeSettings.strPattern": "Նախշ", "SSE.Views.ShapeSettings.strShadow": "Ցուցադրել ստվերը", "SSE.Views.ShapeSettings.strSize": "Չափ", "SSE.Views.ShapeSettings.strStroke": "Գիծ", "SSE.Views.ShapeSettings.strTransparency": "Թափանցիկություն", "SSE.Views.ShapeSettings.strType": "Տեսակ", "SSE.Views.ShapeSettings.textAdvanced": "Ցուցադրել լրացուցիչ կարգավորումները", "SSE.Views.ShapeSettings.textAngle": "Անկյուն", "SSE.Views.ShapeSettings.textBorderSizeErr": " Մուտքագրված արժեքը սխալ է: Խնդրում ենք մուտքագրել 0կտ-ից 255կտ թվային արժեք:", "SSE.Views.ShapeSettings.textColor": "Գույնի լցում", "SSE.Views.ShapeSettings.textDirection": "Ուղղություն", "SSE.Views.ShapeSettings.textEmptyPattern": "Առանց օրինակի", "SSE.Views.ShapeSettings.textFlip": "Շրջել", "SSE.Views.ShapeSettings.textFromFile": "Նիշքից", "SSE.Views.ShapeSettings.textFromStorage": "Պահեստից", "SSE.Views.ShapeSettings.textFromUrl": "URL-ից", "SSE.Views.ShapeSettings.textGradient": "Սահանցման կետեր", "SSE.Views.ShapeSettings.textGradientFill": "Սահանցման լցում ", "SSE.Views.ShapeSettings.textHint270": "Պտտել 90° ժամացույցի սլաքի հակառակ ուղղությամբ", "SSE.Views.ShapeSettings.textHint90": "Պտտել 90° ժամացույցի սլաքի ուղղությամբ", "SSE.Views.ShapeSettings.textHintFlipH": "Շրջել հորիզոնական", "SSE.Views.ShapeSettings.textHintFlipV": "Շրջել ուղղաձիգ", "SSE.Views.ShapeSettings.textImageTexture": "Նկար կամ կառուցվածք", "SSE.Views.ShapeSettings.textLinear": "Գծային", "SSE.Views.ShapeSettings.textNoFill": "Առանց լցման", "SSE.Views.ShapeSettings.textOriginalSize": "Օրիգինալ չափս", "SSE.Views.ShapeSettings.textPatternFill": "Նախշ", "SSE.Views.ShapeSettings.textPosition": "Դիրք", "SSE.Views.ShapeSettings.textRadial": "Ճառագայթային", "SSE.Views.ShapeSettings.textRecentlyUsed": "Վերջերս օգտագործված", "SSE.Views.ShapeSettings.textRotate90": "Պտտել 90°", "SSE.Views.ShapeSettings.textRotation": "Շրջում ", "SSE.Views.ShapeSettings.textSelectImage": "Ընտրել նկար", "SSE.Views.ShapeSettings.textSelectTexture": "Ընտրել", "SSE.Views.ShapeSettings.textStretch": "Ձգել", "SSE.Views.ShapeSettings.textStyle": "Ոճ", "SSE.Views.ShapeSettings.textTexture": "Կառուցվածքից", "SSE.Views.ShapeSettings.textTile": "Սալիկ", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Ավելացնել սահանցման կետ", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Հեռացնել գրադիենտ կետը", "SSE.Views.ShapeSettings.txtBrownPaper": "Գորշ թուղթ", "SSE.Views.ShapeSettings.txtCanvas": "Կտավ", "SSE.Views.ShapeSettings.txtCarton": "Ստվարաթուղթ", "SSE.Views.ShapeSettings.txtDarkFabric": "Մուգ կտոր", "SSE.Views.ShapeSettings.txtGrain": "Հատիկավորության", "SSE.Views.ShapeSettings.txtGranite": "Գրանիտ ", "SSE.Views.ShapeSettings.txtGreyPaper": "Մոխրագույն թուղթ ", "SSE.Views.ShapeSettings.txtKnit": "Միացնել ", "SSE.Views.ShapeSettings.txtLeather": "Կաշի", "SSE.Views.ShapeSettings.txtNoBorders": "Առանց գծի", "SSE.Views.ShapeSettings.txtPapyrus": "Պապիրուս ", "SSE.Views.ShapeSettings.txtWood": "Փայտ", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Սյունակներ", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Տեքստի գունալցում", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Մի շարժվեք կամ չափեք վանդակներով", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Այլընտրանքային տեքստ", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Նկարագրություն", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Տեսողական առարկաների այլընտրական տեքստային ներկայացում, որը ընթերցվելու է տեսողության կամ մտավոր խանգարումներով մարդկանց համար՝ օգնելու նրանց ավելի լավ հասկանալ, թե ինչ տեղեկատվություն կա նկարի, պատկերի, գծապատկերի կամ աղյուսակի վրա։", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Վերնագիր", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Անկյուն", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Սլաքներ", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Ինքնահարմարեցում", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Սկզբնական չափ", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Սկզբնական ոճ", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Շեղատ", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Ներքև", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Մեծատառի տեսակ", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Սյունակների քանակ", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Վերջնական չափ", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Վերջնական ոճ", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Հարթ", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Շրջված", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Բարձրություն", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Հորիզոնական ", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Միացման տեսակ", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Պահպանել համաչափությունը", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Ձախ", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Տողի ոճ", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Բաղադրյալ", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Տեղափոխել, բայց չչափել վանդակներով", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Թույլ տալ, որ տեքստը լցվի", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Չափափոխել ձևը՝ տեքստին համապատասխանելու համար", "SSE.Views.ShapeSettingsAdvanced.textRight": "Աջ", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Շրջում ", "SSE.Views.ShapeSettingsAdvanced.textRound": "Կլոր", "SSE.Views.ShapeSettingsAdvanced.textSize": "Չափ", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Վանդակի խզում", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Միջսյունային տարածք", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Քառակուսի", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Գրվածքի տուփ", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Պատկեր - ընդլայնված կարգավորումներ", "SSE.Views.ShapeSettingsAdvanced.textTop": "Վերև", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Տեղափոխել և չափել վանդակներով", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Ուղղահայաց", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Գծեր և սլաքներ", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Լայնք", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Զգուշացում", "SSE.Views.SignatureSettings.strDelete": "Ջնջել ստորագրությունը", "SSE.Views.SignatureSettings.strDetails": "Ստորագրության մանրամասներ", "SSE.Views.SignatureSettings.strInvalid": "Անվավեր ստորագրություններ", "SSE.Views.SignatureSettings.strRequested": "Հայցվող ստորագրություններ", "SSE.Views.SignatureSettings.strSetup": "Ստորագրության տեղակայում", "SSE.Views.SignatureSettings.strSign": "Ստորագրել", "SSE.Views.SignatureSettings.strSignature": "Ստորագրություն", "SSE.Views.SignatureSettings.strSigner": "Ստորագրող", "SSE.Views.SignatureSettings.strValid": "Վավեր ստորագրություններ", "SSE.Views.SignatureSettings.txtContinueEditing": "Ամեն դեպքում խմբագրել", "SSE.Views.SignatureSettings.txtEditWarning": "Խմբագրումը կվերացնի աղյուսակաթերթի ստորագրությունները։<br>Շարունակե՞լ։", "SSE.Views.SignatureSettings.txtRemoveWarning": "Ցանկանու՞մ եք հեռացնել այս ստորագրությունը:<br>Այն հնարավոր չէ չեղարկել:", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Այս աղյուսակաթերթն անհրաժեշտ է ստորագրել։", "SSE.Views.SignatureSettings.txtSigned": "Աղյուսակաթերթում ավելացվել են վավեր ստորագրություններ։ Աղյուսակաթերթը պաշտպանված է և չի կարող խմբագրվել։", "SSE.Views.SignatureSettings.txtSignedInvalid": "Աղյուսակաթերթի որոշ թվանշային ստորագրություններ անվավեր են կամ չեն կարող ստուգվել։ Աղյուսակաթերթը պաշտպանված է և չի կարող խմբագրվել։", "SSE.Views.SlicerAddDialog.textColumns": "Սյունակներ", "SSE.Views.SlicerAddDialog.txtTitle": "Զետեղել շերտազտիչներ", "SSE.Views.SlicerSettings.strHideNoData": "Թաքցնել իրերը առանց տվյալների", "SSE.Views.SlicerSettings.strIndNoData": "Տեսողականորեն նշեք առանց տվյալների", "SSE.Views.SlicerSettings.strShowDel": "Ցուցադրել տվյալների աղբյուրից ջնջված տարրերը", "SSE.Views.SlicerSettings.strShowNoData": "Ցույց տալ առանց տվյալների վերջին տարրերը", "SSE.Views.SlicerSettings.strSorting": "Տեսակավորում և զտում", "SSE.Views.SlicerSettings.textAdvanced": "Ցուցադրել լրացուցիչ կարգավորումները", "SSE.Views.SlicerSettings.textAsc": "Աճման կարգով", "SSE.Views.SlicerSettings.textAZ": "Ա-ից Ֆ", "SSE.Views.SlicerSettings.textButtons": "Կոճակներ", "SSE.Views.SlicerSettings.textColumns": "Սյունակներ", "SSE.Views.SlicerSettings.textDesc": "Նվազող", "SSE.Views.SlicerSettings.textHeight": "Բարձրություն", "SSE.Views.SlicerSettings.textHor": "Հորիզոնական", "SSE.Views.SlicerSettings.textKeepRatio": "Պահպանել համաչափությունը", "SSE.Views.SlicerSettings.textLargeSmall": "Ամենամեծից ամենափոքրը", "SSE.Views.SlicerSettings.textLock": "Անջատել չափափոխումը կամ տեղափոխումը", "SSE.Views.SlicerSettings.textNewOld": "ամենանորից ամենահին", "SSE.Views.SlicerSettings.textOldNew": "ամենահինը նորագույնից", "SSE.Views.SlicerSettings.textPosition": "Դիրք", "SSE.Views.SlicerSettings.textSize": "Չափ", "SSE.Views.SlicerSettings.textSmallLarge": "ամենափոքրից ամենամեծը", "SSE.Views.SlicerSettings.textStyle": "Ոճ", "SSE.Views.SlicerSettings.textVert": "Ուղղահայաց", "SSE.Views.SlicerSettings.textWidth": "Լայնք", "SSE.Views.SlicerSettings.textZA": "Ֆ-ից Ա", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Կոճակներ", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Սյունակներ", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Բարձրություն", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Թաքցնել իրերը առանց տվյալների", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Տեսողականորեն նշեք առանց տվյալների", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Հղումներ", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Ցուցադրել տվյալների աղբյուրից ջնջված տարրերը", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Ցուցադրել վերնագիրը", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Ցույց տալ առանց տվյալների վերջին տարրերը", "SSE.Views.SlicerSettingsAdvanced.strSize": "Չափ", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Տեսակավորում և զտում", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Ոճ", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Ոճ և չափս", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Լայնք", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Մի շարժվեք կամ չափեք վանդակներով", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Այլընտրանքային տեքստ", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Նկարագրություն", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Տեսողական օբյեկտի տեղեկատվության այլընտրանքային տեքստի վրա հիմնված ներկայացում, որը կկարդացվի տեսողության կամ ճանաչողական խանգարումներ ունեցող մարդկանց՝ օգնելու նրանց ավելի լավ հասկանալ, թե ինչ տեղեկատվություն կա պատկերի, ինքնաձևի, գծապատկերի կամ աղյուսակի վրա:", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Վերնագիր", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Աճման կարգով", "SSE.Views.SlicerSettingsAdvanced.textAZ": "Ա-ից Ֆ", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Նվազող", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Բանաձևերում օգտագործելու անուն", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Էջագլուխ", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Պահպանել համաչափությունը", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "Ամենամեծից ամենափոքրը", "SSE.Views.SlicerSettingsAdvanced.textName": "Անուն", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "ամենանորից ամենահին", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "ամենահինը նորագույնից", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Տեղափոխել, բայց չչափել վանդակներով", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "ամենափոքրից ամենամեծը", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Վանդակի խզում", "SSE.Views.SlicerSettingsAdvanced.textSort": "Տեսակավորել", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Աղբյուրի անվանումը", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Շերտազտիչ- Ընդլայնված կարգավորումներ", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Տեղափոխել և չափել վանդակներով", "SSE.Views.SlicerSettingsAdvanced.textZA": "Ֆ-ից Ա", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.SortDialog.errorEmpty": "Տեսակավորման բոլոր չափանիշերը պետք է պարունակեն հատկորոշված մեկ տող կամ սյունակ: Ստուգեք ընտրված չափանիշը և կրկին փորձեք:", "SSE.Views.SortDialog.errorMoreOneCol": "Ընտրված է մեկից ավելի սյունակ:", "SSE.Views.SortDialog.errorMoreOneRow": "Ընտրված է մեկից ավելի տող:", "SSE.Views.SortDialog.errorNotOriginalCol": "Ձեր ընտրած սյունակը սկզբնական ընտրված տիրույթում չէ:", "SSE.Views.SortDialog.errorNotOriginalRow": "Ձեր ընտրած տողը բնօրինակ ընտրված տիրույթում չէ:", "SSE.Views.SortDialog.errorSameColumnColor": "{1}-ը տեսակավորված է ըստ նույն գույնի մի քանի անգամ:<br> Ջնջեք տեսակավորման կրկնօրինակ չափանիշը և կրկին փորձեք:", "SSE.Views.SortDialog.errorSameColumnValue": "1%-ը տեսակավորված է ըստ նույն գույնի մի քանի անգամ:<br> Ջնջեք տեսակավորման կրկնօրինակ չափանիշը և կրկին փորձեք:", "SSE.Views.SortDialog.textAdd": "Հավելել մակարդակ", "SSE.Views.SortDialog.textAsc": "Աճման կարգով", "SSE.Views.SortDialog.textAuto": "Ինքնաշխատ", "SSE.Views.SortDialog.textAZ": "Ա-ից Ֆ", "SSE.Views.SortDialog.textBelow": "ներքևում", "SSE.Views.SortDialog.textCellColor": "Վանդակի գույնը", "SSE.Views.SortDialog.textColumn": "Սյունակ", "SSE.Views.SortDialog.textCopy": "Պատճենման մակարդակ", "SSE.Views.SortDialog.textDelete": "Ջնջել մակարդակը", "SSE.Views.SortDialog.textDesc": "Նվազող", "SSE.Views.SortDialog.textDown": "Տեղափոխել մակարդակը ներքև", "SSE.Views.SortDialog.textFontColor": "Տառատեսակի գույն", "SSE.Views.SortDialog.textLeft": "Ձախ", "SSE.Views.SortDialog.textMoreCols": "(Ավել սյունակներ...)", "SSE.Views.SortDialog.textMoreRows": "(Ավել տողեր...)", "SSE.Views.SortDialog.textNone": "Ոչ մեկը", "SSE.Views.SortDialog.textOptions": "Ընտրանքներ", "SSE.Views.SortDialog.textOrder": "Պատվեր", "SSE.Views.SortDialog.textRight": "Աջ", "SSE.Views.SortDialog.textRow": "Տող", "SSE.Views.SortDialog.textSort": "Դասավորել", "SSE.Views.SortDialog.textSortBy": "Տեսակավորում ըստ", "SSE.Views.SortDialog.textThenBy": "Հետո ըստ", "SSE.Views.SortDialog.textTop": "Վերև", "SSE.Views.SortDialog.textUp": "Տեղափոխել մակարդակը վերև", "SSE.Views.SortDialog.textValues": "Արժեքներ", "SSE.Views.SortDialog.textZA": "Ֆ-ից Ա", "SSE.Views.SortDialog.txtInvalidRange": "Անվավեր բջիջների ընդգրկույթ:", "SSE.Views.SortDialog.txtTitle": "Տեսակավորել", "SSE.Views.SortFilterDialog.textAsc": "Աճող Ա-ից Ֆ", "SSE.Views.SortFilterDialog.textDesc": "Նվազող (Ա-ից Ֆ)", "SSE.Views.SortFilterDialog.txtTitle": "Տեսակավորել", "SSE.Views.SortOptionsDialog.textCase": "Հաշվի առնել տառաշարը", "SSE.Views.SortOptionsDialog.textHeaders": "Իմ տվյալները վերնագրեր ունեն", "SSE.Views.SortOptionsDialog.textLeftRight": "Տեսակավորել ձախից աջ", "SSE.Views.SortOptionsDialog.textOrientation": "Կողմնորոշում", "SSE.Views.SortOptionsDialog.textTitle": "Տեսակավորման ընտրանքներ", "SSE.Views.SortOptionsDialog.textTopBottom": "Դասավորել վերևից ներքև", "SSE.Views.SpecialPasteDialog.textAdd": "Հավելել", "SSE.Views.SpecialPasteDialog.textAll": "Բոլորը", "SSE.Views.SpecialPasteDialog.textBlanks": "Բաց թողեք դատարկ տեղերը", "SSE.Views.SpecialPasteDialog.textColWidth": "Սյունակների լայնությունները", "SSE.Views.SpecialPasteDialog.textComments": "Մեկնաբանություններ", "SSE.Views.SpecialPasteDialog.textDiv": "Բաժանել", "SSE.Views.SpecialPasteDialog.textFFormat": "Բանաձևեր և ձևաչափում", "SSE.Views.SpecialPasteDialog.textFNFormat": "Բանաձևեր և թվերի ձևաչափեր", "SSE.Views.SpecialPasteDialog.textFormats": "Ձևաչափեր", "SSE.Views.SpecialPasteDialog.textFormulas": "Բանաձևեր", "SSE.Views.SpecialPasteDialog.textFWidth": "Բանաձևեր և սյունակների լայնություններ", "SSE.Views.SpecialPasteDialog.textMult": "Բազմապատկել", "SSE.Views.SpecialPasteDialog.textNone": "Ոչ մեկը", "SSE.Views.SpecialPasteDialog.textOperation": "Գործողություն", "SSE.Views.SpecialPasteDialog.textPaste": "Փակցնել", "SSE.Views.SpecialPasteDialog.textSub": "հանել", "SSE.Views.SpecialPasteDialog.textTitle": "Կպցնել հատուկ", "SSE.Views.SpecialPasteDialog.textTranspose": "Փոխադրել", "SSE.Views.SpecialPasteDialog.textValues": "Արժեքներ", "SSE.Views.SpecialPasteDialog.textVFormat": "Արժեքներ և ձևավորում", "SSE.Views.SpecialPasteDialog.textVNFormat": "Արժեքներ և թվերի ձևաչափեր", "SSE.Views.SpecialPasteDialog.textWBorders": "Բոլորը, բացի սահմաններից", "SSE.Views.Spellcheck.noSuggestions": "Ուղղագրական առաջարկներ չկան", "SSE.Views.Spellcheck.textChange": "Փոխել", "SSE.Views.Spellcheck.textChangeAll": "Փոխել բոլորը", "SSE.Views.Spellcheck.textIgnore": "Անտեսել", "SSE.Views.Spellcheck.textIgnoreAll": "Անտեսել բոլորը", "SSE.Views.Spellcheck.txtAddToDictionary": "Հավելել բառարանում", "SSE.Views.Spellcheck.txtClosePanel": "Փակել ուղղագրությունը", "SSE.Views.Spellcheck.txtComplete": "Ուղղագրության ստուգումն ավարտված է", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Բառարանի լեզու", "SSE.Views.Spellcheck.txtNextTip": "Անցնել հաջորդ բառին", "SSE.Views.Spellcheck.txtSpelling": "Ուղղագրություն", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Պատճենել վերջում)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Տեղափոխել վերջ)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Կպցնել թերթից առաջ", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Տեղափոխել թերթից առաջ", "SSE.Views.Statusbar.filteredRecordsText": "{0} գրառումից զտվել է {1}-ը", "SSE.Views.Statusbar.filteredText": "Զտման ռեժիմ", "SSE.Views.Statusbar.itemAverage": "ՄԻՋԻՆ", "SSE.Views.Statusbar.itemCopy": "Պատճենել", "SSE.Views.Statusbar.itemCount": "Հաշվել", "SSE.Views.Statusbar.itemDelete": "Ջնջել", "SSE.Views.Statusbar.itemHidden": "Թաքնված", "SSE.Views.Statusbar.itemHide": "Թաքցնել", "SSE.Views.Statusbar.itemInsert": "Զետեղել", "SSE.Views.Statusbar.itemMaximum": "Առավելագույն", "SSE.Views.Statusbar.itemMinimum": "Նվազագույն", "SSE.Views.Statusbar.itemMove": "Տեղափոխել", "SSE.Views.Statusbar.itemProtect": "Պաշտպանել", "SSE.Views.Statusbar.itemRename": "Վերանվանել", "SSE.Views.Statusbar.itemStatus": "Պահպանում է կարգավիճակը", "SSE.Views.Statusbar.itemSum": "Գումար", "SSE.Views.Statusbar.itemTabColor": "Սյունատի գույն", "SSE.Views.Statusbar.itemUnProtect": "Չպաշտպանել", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Նման անունով աշխատանքային թերթիկ արդեն գոյություն ունի:", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Թերթի անունը չպիտի պարունակի հետևյալ գրանշանները՝ \\/*?[]. ", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Թերթի անվանում", "SSE.Views.Statusbar.selectAllSheets": "Ընտրեք Բոլոր թերթերը", "SSE.Views.Statusbar.sheetIndexText": "Թերթ {0} {1}-ից", "SSE.Views.Statusbar.textAverage": "ՄԻՋԻՆ", "SSE.Views.Statusbar.textCount": "Հաշվել", "SSE.Views.Statusbar.textMax": "Մաքս", "SSE.Views.Statusbar.textMin": "Նվազ.", "SSE.Views.Statusbar.textNewColor": "Հավելել նոր հարմարեցված գույն", "SSE.Views.Statusbar.textNoColor": "Առանց գույն", "SSE.Views.Statusbar.textSum": "Գումար", "SSE.Views.Statusbar.tipAddTab": "Հավելել թերթ", "SSE.Views.Statusbar.tipFirst": "Ոլորեք դեպի առաջին թերթիկը", "SSE.Views.Statusbar.tipLast": "Ոլորեք մինչև վերջին թերթիկը", "SSE.Views.Statusbar.tipListOfSheets": "Թերթերի ցուցակ", "SSE.Views.Statusbar.tipNext": "Ոլորեք թերթերի ցանկը աջ", "SSE.Views.Statusbar.tipPrev": "Ոլորեք թերթերի ցանկը ձախ", "SSE.Views.Statusbar.tipZoomFactor": "Խոշորացնել", "SSE.Views.Statusbar.tipZoomIn": "Մեծացնել", "SSE.Views.Statusbar.tipZoomOut": "Փոքրացնել", "SSE.Views.Statusbar.ungroupSheets": "Ապախմբավորել թերթերը", "SSE.Views.Statusbar.zoomText": "Խոշորացնել {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Ընտրված վանդակների համար հնարավոր չեղավ կատարել գործողությունը։<br>Ընտրրեք տվյալների միատեսակ ընդգրկույթ, որը եղածից տարբեր է, և նորից փորձեք։", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Գործողությունը չի կարող ավարտվել ընտրված բջիջների տիրույթի համար:<br>Ընտրեք ընդգրկույթ, որպեսզի աղյուսակի առաջին տողը լինի նույն տողում<br>և արդյունքում աղյուսակը համընկնի ընթացիկի հետ:", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Գործողությունը չի կարող ավարտվել ընտրված բջիջների տիրույթի համար:<br>Ընտրեք ընդգրկույթ, որը չի ներառում այլ աղյուսակներ:", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Զանգվածի բազմավանդակ բանաձևերը թույլատրված չեն աղյուսակներում։", "SSE.Views.TableOptionsDialog.txtEmpty": "Պահանջվում է լրացնել այս դաշտը:", "SSE.Views.TableOptionsDialog.txtFormat": "Ստեղծել աղյուսակ", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ՍԽԱԼ. վանդակների անթույլատրելի ընդգրկույթ", "SSE.Views.TableOptionsDialog.txtNote": "Վերնագրերը պետք է մնան նույն շարքում, և արդյունքում ստացված աղյուսակի միջակայքը պետք է համընկնի սկզբնական աղյուսակի միջակայքի հետ:", "SSE.Views.TableOptionsDialog.txtTitle": "Վերնագիր", "SSE.Views.TableSettings.deleteColumnText": "Ջնջել սյունակը", "SSE.Views.TableSettings.deleteRowText": "Ջնջել տողը", "SSE.Views.TableSettings.deleteTableText": "Ջնջել աղյուսակը", "SSE.Views.TableSettings.insertColumnLeftText": "Զետեղել սյունակ ձախից", "SSE.Views.TableSettings.insertColumnRightText": "Զետեղել սյունակ աջից", "SSE.Views.TableSettings.insertRowAboveText": "Զետեղել տող վերևում", "SSE.Views.TableSettings.insertRowBelowText": "Զետեղել տող ներքևից", "SSE.Views.TableSettings.notcriticalErrorTitle": "Զգուշացում", "SSE.Views.TableSettings.selectColumnText": "Ընտրեք Ամբողջ սյունակը", "SSE.Views.TableSettings.selectDataText": "Ընտրեք սյունակի տվյալները", "SSE.Views.TableSettings.selectRowText": "Ընտրել տող", "SSE.Views.TableSettings.selectTableText": "Ընտրել աղյուսակ", "SSE.Views.TableSettings.textActions": "Սեղանի գործողություններ", "SSE.Views.TableSettings.textAdvanced": "Ցուցադրել լրացուցիչ կարգավորումները", "SSE.Views.TableSettings.textBanded": "Միջարկվող", "SSE.Views.TableSettings.textColumns": "Սյունակներ", "SSE.Views.TableSettings.textConvertRange": "Փոխակերպել տիրույթի", "SSE.Views.TableSettings.textEdit": "Տողեր և սյունակներ", "SSE.Views.TableSettings.textEmptyTemplate": "Ձևանմուշները բացակայում են", "SSE.Views.TableSettings.textExistName": "ՍԽԱԼ! Նման անունով տիրույթ արդեն գոյություն ունի", "SSE.Views.TableSettings.textFilter": "Զտիչ կոճակ", "SSE.Views.TableSettings.textFirst": "Առաջին", "SSE.Views.TableSettings.textHeader": "Էջագլուխ", "SSE.Views.TableSettings.textInvalidName": "ՍԽԱԼ. Սեղանի անվավեր անուն", "SSE.Views.TableSettings.textIsLocked": "Այս տարրը խմբագրվում է մեկ այլ օգտվողի կողմից:", "SSE.Views.TableSettings.textLast": "Վերջին", "SSE.Views.TableSettings.textLongOperation": "Երկարատև գործողություն", "SSE.Views.TableSettings.textPivot": "Տեղադրել առանցքային աղյուսակը", "SSE.Views.TableSettings.textRemDuplicates": "Հեռացրեք կրկնօրինակները", "SSE.Views.TableSettings.textReservedName": "Անունը, որը դուք փորձում եք օգտագործել, արդեն նշված է բջջային բանաձևերում: Խնդրում ենք օգտագործել այլ անուն:", "SSE.Views.TableSettings.textResize": "Չափափոխել աղյուսակը", "SSE.Views.TableSettings.textRows": "Տողեր", "SSE.Views.TableSettings.textSelectData": "Ընտրեք տվյալներ", "SSE.Views.TableSettings.textSlicer": "Տեղադրել կտրիչ", "SSE.Views.TableSettings.textTableName": "Սեղանի անվանումը", "SSE.Views.TableSettings.textTemplate": "Ընտրել ձևանմուշից", "SSE.Views.TableSettings.textTotal": "Ընդամենը", "SSE.Views.TableSettings.warnLongOperation": "Գործողությունը, որը պատրաստվում եք կատարել, կարող է բավականին շատ ժամանակ պահանջել ավարտելու համար:<br>Իսկապե՞ս ուզում եք շարունակել:", "SSE.Views.TableSettingsAdvanced.textAlt": "Այլընտրանքային տեքստ", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Նկարագրություն", "SSE.Views.TableSettingsAdvanced.textAltTip": "Տեսողական օբյեկտի տեղեկատվության այլընտրանքային տեքստի վրա հիմնված ներկայացում, որը կկարդացվի տեսողության կամ ճանաչողական խանգարումներ ունեցող մարդկանց՝ օգնելու նրանց ավելի լավ հասկանալ, թե ինչ տեղեկատվություն կա պատկերի, ինքնաձևի, գծապատկերի կամ աղյուսակի վրա:", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Վերնագիր", "SSE.Views.TableSettingsAdvanced.textTitle": "Աղյուսակ - լրացուցիչ կարգավորումներ", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "Հարմարեցված", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "Մութ", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "Լույս", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "Միջին", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "Սեղանի ոճը մուգ", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "Սեղանի ոճի լույս", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "Սեղանի ոճը միջին", "SSE.Views.TextArtSettings.strBackground": "Ֆոնի գույն", "SSE.Views.TextArtSettings.strColor": "Գույն", "SSE.Views.TextArtSettings.strFill": "Լցնել", "SSE.Views.TextArtSettings.strForeground": "Հետնագույն", "SSE.Views.TextArtSettings.strPattern": "Նախշ", "SSE.Views.TextArtSettings.strSize": "Չափ", "SSE.Views.TextArtSettings.strStroke": "Գիծ", "SSE.Views.TextArtSettings.strTransparency": "Թափանցիկություն", "SSE.Views.TextArtSettings.strType": "Տեսակ", "SSE.Views.TextArtSettings.textAngle": "Անկյուն", "SSE.Views.TextArtSettings.textBorderSizeErr": " Մուտքագրված արժեքը սխալ է: Խնդրում ենք մուտքագրել 0կտ-ից 255կտ թվային արժեք:", "SSE.Views.TextArtSettings.textColor": "Գույնի լցում", "SSE.Views.TextArtSettings.textDirection": "Ուղղություն", "SSE.Views.TextArtSettings.textEmptyPattern": "Առանց Նախշի", "SSE.Views.TextArtSettings.textFromFile": "Նիշքից", "SSE.Views.TextArtSettings.textFromUrl": "URL-ից", "SSE.Views.TextArtSettings.textGradient": "Սահանցման կետեր", "SSE.Views.TextArtSettings.textGradientFill": "Սահանցման լցում ", "SSE.Views.TextArtSettings.textImageTexture": "Նկար կամ կառուցվածք", "SSE.Views.TextArtSettings.textLinear": "Գծային", "SSE.Views.TextArtSettings.textNoFill": "Առանց լցման", "SSE.Views.TextArtSettings.textPatternFill": "Նախշ", "SSE.Views.TextArtSettings.textPosition": "Դիրք", "SSE.Views.TextArtSettings.textRadial": "Ճառագայթային", "SSE.Views.TextArtSettings.textSelectTexture": "Ընտրել", "SSE.Views.TextArtSettings.textStretch": "Ձգել", "SSE.Views.TextArtSettings.textStyle": "Ոճ", "SSE.Views.TextArtSettings.textTemplate": "Ձևանմուշ", "SSE.Views.TextArtSettings.textTexture": "Կառուցվածքից", "SSE.Views.TextArtSettings.textTile": "Սալիկ", "SSE.Views.TextArtSettings.textTransform": "Փոխակերպել", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Ավելացնել սահանցման կետ", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Հեռացնել գրադիենտ կետը", "SSE.Views.TextArtSettings.txtBrownPaper": "Գորշ թուղթ", "SSE.Views.TextArtSettings.txtCanvas": "Կտավ", "SSE.Views.TextArtSettings.txtCarton": "Ստվարաթուղթ", "SSE.Views.TextArtSettings.txtDarkFabric": "Մուգ կտոր", "SSE.Views.TextArtSettings.txtGrain": "Հատիկավորության", "SSE.Views.TextArtSettings.txtGranite": "Գրանիտ ", "SSE.Views.TextArtSettings.txtGreyPaper": "Մոխրագույն թուղթ ", "SSE.Views.TextArtSettings.txtKnit": "Միացնել ", "SSE.Views.TextArtSettings.txtLeather": "Կաշի", "SSE.Views.TextArtSettings.txtNoBorders": "Առանց գծի", "SSE.Views.TextArtSettings.txtPapyrus": "Պապիրուս ", "SSE.Views.TextArtSettings.txtWood": "Փայտ", "SSE.Views.Toolbar.capBtnAddComment": "Ավելացնել մեկնաբանություն", "SSE.Views.Toolbar.capBtnColorSchemas": "Գունային սխեման", "SSE.Views.Toolbar.capBtnComment": "Մեկնաբանություն", "SSE.Views.Toolbar.capBtnInsHeader": "էջագլուխ/էջատակ", "SSE.Views.Toolbar.capBtnInsSlicer": "Շերտազտիչ", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Նշան", "SSE.Views.Toolbar.capBtnMargins": "Լուսանցքներ", "SSE.Views.Toolbar.capBtnPageOrient": "Կողմնորոշում", "SSE.Views.Toolbar.capBtnPageSize": "Չափ", "SSE.Views.Toolbar.capBtnPrintArea": "Տպման տարածք", "SSE.Views.Toolbar.capBtnPrintTitles": "Տպել վերնագրեր", "SSE.Views.Toolbar.capBtnScale": "Հարմարեցնել՝ սանդղելով", "SSE.Views.Toolbar.capImgAlign": "Հավասարեցում", "SSE.Views.Toolbar.capImgBackward": "ՈՒղարկել հետ", "SSE.Views.Toolbar.capImgForward": "Բերել առաջ", "SSE.Views.Toolbar.capImgGroup": "Խումբ", "SSE.Views.Toolbar.capInsertChart": "Գծապատկեր", "SSE.Views.Toolbar.capInsertEquation": "Հավասարում", "SSE.Views.Toolbar.capInsertHyperlink": "Գերհղում", "SSE.Views.Toolbar.capInsertImage": "Նկար", "SSE.Views.Toolbar.capInsertShape": "Պատկեր", "SSE.Views.Toolbar.capInsertSpark": "կայծգիծ", "SSE.Views.Toolbar.capInsertTable": "Աղյուսակ", "SSE.Views.Toolbar.capInsertText": "Գրվածքի տուփ", "SSE.Views.Toolbar.capInsertTextart": "Տեքստարվեստ", "SSE.Views.Toolbar.mniImageFromFile": "Նկար նիշքից", "SSE.Views.Toolbar.mniImageFromStorage": "Պատկեր պահեստից", "SSE.Views.Toolbar.mniImageFromUrl": "Պատկերը URL-ից", "SSE.Views.Toolbar.textAddPrintArea": "Դնել տպման տարածքում", "SSE.Views.Toolbar.textAlignBottom": "Հավասարեցնել ներքևից", "SSE.Views.Toolbar.textAlignCenter": "Հավասարեցնել կենտրոնով", "SSE.Views.Toolbar.textAlignJust": "Լայնությամբ", "SSE.Views.Toolbar.textAlignLeft": "Հավասարեցնել ձախից", "SSE.Views.Toolbar.textAlignMiddle": "Հավասարեցնել մեջտեղով", "SSE.Views.Toolbar.textAlignRight": "Հավասարեցնել աջից", "SSE.Views.Toolbar.textAlignTop": "Հավասարեցնել վերևից", "SSE.Views.Toolbar.textAllBorders": "Բոլոր եզրագծերը", "SSE.Views.Toolbar.textAuto": "Ավտոմատ", "SSE.Views.Toolbar.textAutoColor": "Ինքնաշխատ", "SSE.Views.Toolbar.textBold": "Թավ", "SSE.Views.Toolbar.textBordersColor": "Եզրագծի գույն", "SSE.Views.Toolbar.textBordersStyle": "Եզրագծի ոճ", "SSE.Views.Toolbar.textBottom": "Ներքև՝", "SSE.Views.Toolbar.textBottomBorders": "Ներքևի սահմաններ", "SSE.Views.Toolbar.textCenterBorders": "Ուղղահայաց սահմանների ներսում", "SSE.Views.Toolbar.textClearPrintArea": "Մաքրել տպման տարածքը", "SSE.Views.Toolbar.textClearRule": "Հստակ կանոններ", "SSE.Views.Toolbar.textClockwise": "Ժամասլաքի ուղղությամբ", "SSE.Views.Toolbar.textColorScales": "Գունավոր կշեռքներ", "SSE.Views.Toolbar.textCounterCw": "Ժամասլաքի հակառակ ուղղությամբ", "SSE.Views.Toolbar.textCustom": "Հարմարեցված", "SSE.Views.Toolbar.textDataBars": "Տվյալների բարեր", "SSE.Views.Toolbar.textDelLeft": "Տեղափոխեք վանդակները ձախ", "SSE.Views.Toolbar.textDelUp": "Տեղափոխեք բջիջները վերև", "SSE.Views.Toolbar.textDiagDownBorder": "Անկյունագծային իջնող եզրագիծ", "SSE.Views.Toolbar.textDiagUpBorder": "Անկյունագծային բարձրացող եզրագիծ", "SSE.Views.Toolbar.textDone": "Պատրաստ է", "SSE.Views.Toolbar.textEditVA": "Խմբագրել տեսանելի տարածքը", "SSE.Views.Toolbar.textEntireCol": "Ամբողջ սյունակը", "SSE.Views.Toolbar.textEntireRow": "Ամբողջ շարքը", "SSE.Views.Toolbar.textFewPages": "Էջեր", "SSE.Views.Toolbar.textHeight": "Բարձրություն", "SSE.Views.Toolbar.textHideVA": "Թաքցնել տեսանելի տարածքը", "SSE.Views.Toolbar.textHorizontal": "Հորիզոնական տեքստ", "SSE.Views.Toolbar.textInsDown": "Տեղափոխեք բջիջները ներքև", "SSE.Views.Toolbar.textInsideBorders": "Ներքին եզրագծեր", "SSE.Views.Toolbar.textInsRight": "Տեղափոխեք բջիջները աջ", "SSE.Views.Toolbar.textItalic": "Շեղատառ", "SSE.Views.Toolbar.textItems": "Նյութեր", "SSE.Views.Toolbar.textLandscape": "Հորիզոնական", "SSE.Views.Toolbar.textLeft": "Ձախ:", "SSE.Views.Toolbar.textLeftBorders": "Ձախ սահմաններ", "SSE.Views.Toolbar.textManageRule": "Կառավարել Կանոնները", "SSE.Views.Toolbar.textManyPages": "Էջեր", "SSE.Views.Toolbar.textMarginsLast": "Վերջին օգտագործումը", "SSE.Views.Toolbar.textMarginsNarrow": "Նեղ", "SSE.Views.Toolbar.textMarginsNormal": "Սովորական", "SSE.Views.Toolbar.textMarginsWide": "Լայն", "SSE.Views.Toolbar.textMiddleBorders": "Հորիզոնական սահմանների ներսում", "SSE.Views.Toolbar.textMoreFormats": "Ավելի շատ ձևաչափեր", "SSE.Views.Toolbar.textMorePages": "Ավելի շատ էջեր", "SSE.Views.Toolbar.textNewColor": "Հավելել նոր հարմարեցված գույն", "SSE.Views.Toolbar.textNewRule": "Նոր կանոն", "SSE.Views.Toolbar.textNoBorders": "Առանց եզրագծերի", "SSE.Views.Toolbar.textOnePage": "Էջ", "SSE.Views.Toolbar.textOutBorders": "Արտաքին Եզրագծեր", "SSE.Views.Toolbar.textPageMarginsCustom": "Հարմարեցրած լուսանցքներ", "SSE.Views.Toolbar.textPortrait": "Ուղղաձիգ ", "SSE.Views.Toolbar.textPrint": "Տպել", "SSE.Views.Toolbar.textPrintGridlines": "Տպել ցանցագծերը", "SSE.Views.Toolbar.textPrintHeadings": "Տպել վերնագրեր", "SSE.Views.Toolbar.textPrintOptions": "Տպման կարգավորումներ", "SSE.Views.Toolbar.textRight": "Աջ․", "SSE.Views.Toolbar.textRightBorders": "Աջ սահմաններ", "SSE.Views.Toolbar.textRotateDown": "Տեքստը պտտել ներքև", "SSE.Views.Toolbar.textRotateUp": "Տեքստը պտտել վեր", "SSE.Views.Toolbar.textScale": "Սանդղակ", "SSE.Views.Toolbar.textScaleCustom": "Հարմարեցված", "SSE.Views.Toolbar.textSelection": "Ընթացիկ ընտրությունից", "SSE.Views.Toolbar.textSetPrintArea": "Սահմանել տպման տարածքը", "SSE.Views.Toolbar.textShowVA": "Ցուցադրել տեսանելի տարածությունը", "SSE.Views.Toolbar.textStrikeout": "Վրագծում", "SSE.Views.Toolbar.textSubscript": "Վարգիր", "SSE.Views.Toolbar.textSubSuperscript": "Բաժանորդագրություն/Վերածանց", "SSE.Views.Toolbar.textSuperscript": "Վերգիր", "SSE.Views.Toolbar.textTabCollaboration": "Համագործակցություն", "SSE.Views.Toolbar.textTabData": "Տվյալներ", "SSE.Views.Toolbar.textTabFile": "Նիշք", "SSE.Views.Toolbar.textTabFormula": "Բանաձև ", "SSE.Views.Toolbar.textTabHome": "Գլխավոր", "SSE.Views.Toolbar.textTabInsert": "Զետեղել", "SSE.Views.Toolbar.textTabLayout": "Դասավորություն ", "SSE.Views.Toolbar.textTabProtect": "Պաշտպանություն", "SSE.Views.Toolbar.textTabView": "Դիտել", "SSE.Views.Toolbar.textThisPivot": "Այս առանցքից", "SSE.Views.Toolbar.textThisSheet": "Այս աշխատանքային թերթիկից", "SSE.Views.Toolbar.textThisTable": "Այս աղյուսակից", "SSE.Views.Toolbar.textTop": "Վերև․", "SSE.Views.Toolbar.textTopBorders": "Վերևի սահմանները", "SSE.Views.Toolbar.textUnderline": "Ընդգծված", "SSE.Views.Toolbar.textVertical": "Ուղղահայաց տեքստ", "SSE.Views.Toolbar.textWidth": "Լայնք", "SSE.Views.Toolbar.textZoom": "Խոշորացնել", "SSE.Views.Toolbar.tipAlignBottom": "Հավասարեցնել ներքևից", "SSE.Views.Toolbar.tipAlignCenter": "Հավասարեցնել կենտրոնով", "SSE.Views.Toolbar.tipAlignJust": "Լայնությամբ", "SSE.Views.Toolbar.tipAlignLeft": "Հավասարեցնել ձախից", "SSE.Views.Toolbar.tipAlignMiddle": "Հավասարեցնել մեջտեղով", "SSE.Views.Toolbar.tipAlignRight": "Հավասարեցնել աջից", "SSE.Views.Toolbar.tipAlignTop": "Հավասարեցնել վերևից", "SSE.Views.Toolbar.tipAutofilter": "Տեսակավորում և զտում", "SSE.Views.Toolbar.tipBack": "Հետ", "SSE.Views.Toolbar.tipBorders": "Եզրագծեր", "SSE.Views.Toolbar.tipCellStyle": "Վանդակի ոճ", "SSE.Views.Toolbar.tipChangeChart": "Փոխել գծապատկերի տեսակը", "SSE.Views.Toolbar.tipClearStyle": "Մաքրել", "SSE.Views.Toolbar.tipColorSchemas": "Փոխել գունավորումը", "SSE.Views.Toolbar.tipCondFormat": "Պայմանական ձևավորում", "SSE.Views.Toolbar.tipCopy": "Պատճենել", "SSE.Views.Toolbar.tipCopyStyle": "Պատճենել ոճը", "SSE.Views.Toolbar.tipCut": "Կտրել", "SSE.Views.Toolbar.tipDecDecimal": "Նվազեցնել տասնորդական", "SSE.Views.Toolbar.tipDecFont": "Փոքրացնել տառաչափը", "SSE.Views.Toolbar.tipDeleteOpt": "Ջնջել վանդակներ", "SSE.Views.Toolbar.tipDigStyleAccounting": "Հաշվապահական ոճ", "SSE.Views.Toolbar.tipDigStyleCurrency": "Արժույթի ոճը", "SSE.Views.Toolbar.tipDigStylePercent": "Տոկոսային ոճ", "SSE.Views.Toolbar.tipEditChart": "Խմբագրել գծապատկերը", "SSE.Views.Toolbar.tipEditChartData": "Ընտրեք տվյալներ", "SSE.Views.Toolbar.tipEditChartType": "Փոխել գծապատկերի տեսակը", "SSE.Views.Toolbar.tipEditHeader": "Խմբագրել էջագլուխը կամ էջատակը", "SSE.Views.Toolbar.tipFontColor": "Տառատեսակի գույն", "SSE.Views.Toolbar.tipFontName": "Տառատեսակ ", "SSE.Views.Toolbar.tipFontSize": "Տառատեսակի չափ", "SSE.Views.Toolbar.tipHAlighOle": "Հորիզոնական հավասարեցում", "SSE.Views.Toolbar.tipImgAlign": "Հավասարեցնել առարկաները", "SSE.Views.Toolbar.tipImgGroup": "Խմբավորել օբյեկտները", "SSE.Views.Toolbar.tipIncDecimal": "Մեծացնել տասնորդական", "SSE.Views.Toolbar.tipIncFont": "Մեծացնել տառատեսակի չափը", "SSE.Views.Toolbar.tipInsertChart": "Զետեղել գծապատկեր", "SSE.Views.Toolbar.tipInsertChartSpark": "Զետեղել գծապատկեր", "SSE.Views.Toolbar.tipInsertEquation": "Դնել հավասարում", "SSE.Views.Toolbar.tipInsertHorizontalText": "Զետեղել հորիզոնական գրվածքի տուփ", "SSE.Views.Toolbar.tipInsertHyperlink": "Դնել գերհղում", "SSE.Views.Toolbar.tipInsertImage": "Զետեղել նկար", "SSE.Views.Toolbar.tipInsertOpt": "Զետեղել վանդակներ", "SSE.Views.Toolbar.tipInsertShape": "Զետեղել ինքնաձև", "SSE.Views.Toolbar.tipInsertSlicer": "Տեղադրել կտրիչ", "SSE.Views.Toolbar.tipInsertSmartArt": "Զետեղել SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Զետեղել կայծագիծը", "SSE.Views.Toolbar.tipInsertSymbol": "Զետեղել նշան", "SSE.Views.Toolbar.tipInsertTable": "Զետեղել աղյուսակ", "SSE.Views.Toolbar.tipInsertText": "Դնել տեքստատուփ", "SSE.Views.Toolbar.tipInsertTextart": "Դնել տեքստարվեստից", "SSE.Views.Toolbar.tipInsertVerticalText": "Զետեղել ուղղահայաց գրվածքի տուփ", "SSE.Views.Toolbar.tipMerge": "Միաձուլել և կենտրոնացնել", "SSE.Views.Toolbar.tipNone": "Ոչ մեկը", "SSE.Views.Toolbar.tipNumFormat": "Թվերի ձևաչափ", "SSE.Views.Toolbar.tipPageMargins": "էջի լուսանցքներ", "SSE.Views.Toolbar.tipPageOrient": "Էջի կողմնորոշում", "SSE.Views.Toolbar.tipPageSize": "Էջի չափ", "SSE.Views.Toolbar.tipPaste": "Փակցնել", "SSE.Views.Toolbar.tipPrColor": "Լցման գույն", "SSE.Views.Toolbar.tipPrint": "Տպել", "SSE.Views.Toolbar.tipPrintArea": "Տպման տարածք", "SSE.Views.Toolbar.tipPrintQuick": "Արագ տպում", "SSE.Views.Toolbar.tipPrintTitles": "Տպել վերնագրեր", "SSE.Views.Toolbar.tipRedo": "Վերարկել", "SSE.Views.Toolbar.tipSave": "Պահպանել", "SSE.Views.Toolbar.tipSaveCoauth": "Պահպանեք բոլոր փոփոխումները, որպեսզի այլ օգտատերեր տեսնեն դրանք։", "SSE.Views.Toolbar.tipScale": "Հարմարեցնել՝ սանդղելով", "SSE.Views.Toolbar.tipSelectAll": "Ընտրել բոլորը", "SSE.Views.Toolbar.tipSendBackward": "ՈՒղարկել հետ", "SSE.Views.Toolbar.tipSendForward": "Բերել առաջ", "SSE.Views.Toolbar.tipSynchronize": "Փաստաթուղթն այլ օգտատիրոջ կողմից փոփոխվել է։ Սեղմեք, որ պահպանեք Ձեր փոփոխումները և բեռնեք թարմացումները։", "SSE.Views.Toolbar.tipTextFormatting": "Տեքստի ձևաչափման ավելի շատ գործիքներ", "SSE.Views.Toolbar.tipTextOrientation": "Կողմնորոշում", "SSE.Views.Toolbar.tipUndo": "Հետարկել", "SSE.Views.Toolbar.tipVAlighOle": "Հավասարեցնել ուղղաձիգ ", "SSE.Views.Toolbar.tipVisibleArea": "Տեսանելի տարածք", "SSE.Views.Toolbar.tipWrap": "Տեքստի ծալում", "SSE.Views.Toolbar.txtAccounting": "Հաշվապահություն", "SSE.Views.Toolbar.txtAdditional": "Հավելյալ", "SSE.Views.Toolbar.txtAscending": "Աճման կարգով", "SSE.Views.Toolbar.txtAutosumTip": "Ամփոփում", "SSE.Views.Toolbar.txtCellStyle": "Վանդակի ոճ", "SSE.Views.Toolbar.txtClearAll": "Բոլորը", "SSE.Views.Toolbar.txtClearComments": "Մեկնաբանություններ", "SSE.Views.Toolbar.txtClearFilter": "Մաքրել զտիչը", "SSE.Views.Toolbar.txtClearFormat": "Ձևաչափ", "SSE.Views.Toolbar.txtClearFormula": "Ֆունկցիա", "SSE.Views.Toolbar.txtClearHyper": "Հիպերհղումներ", "SSE.Views.Toolbar.txtClearText": "Տեքստ", "SSE.Views.Toolbar.txtCurrency": "Տարադրամ", "SSE.Views.Toolbar.txtCustom": "Հարմարեցված", "SSE.Views.Toolbar.txtDate": "Ամիս-ամսաթիվ", "SSE.Views.Toolbar.txtDateTime": "Ամսաթիվ/Ժամ", "SSE.Views.Toolbar.txtDescending": "Նվազող", "SSE.Views.Toolbar.txtDollar": "$ Դոլար", "SSE.Views.Toolbar.txtEuro": "€ Եվրո", "SSE.Views.Toolbar.txtExp": "Էքսպոնենցիալ", "SSE.Views.Toolbar.txtFilter": "Զտիչ", "SSE.Views.Toolbar.txtFormula": "Տեղադրել գործառույթը", "SSE.Views.Toolbar.txtFraction": "Կոտորակ", "SSE.Views.Toolbar.txtFranc": "CHF շվեյցարական ֆրանկ", "SSE.Views.Toolbar.txtGeneral": "Ընդհանուր", "SSE.Views.Toolbar.txtInteger": "Ամբողջ թիվ", "SSE.Views.Toolbar.txtManageRange": "Անվան կառավարիչ", "SSE.Views.Toolbar.txtMergeAcross": "Միաձուլել ամբողջը", "SSE.Views.Toolbar.txtMergeCells": "Միաձուլել վանդակները", "SSE.Views.Toolbar.txtMergeCenter": "Միաձուլել և կենտրոնացնել", "SSE.Views.Toolbar.txtNamedRange": "Անվանված միջակայքեր", "SSE.Views.Toolbar.txtNewRange": "Սահմանել անունը", "SSE.Views.Toolbar.txtNoBorders": "Առանց եզրագծերի", "SSE.Views.Toolbar.txtNumber": "Թվային", "SSE.Views.Toolbar.txtPasteRange": "Paste Name", "SSE.Views.Toolbar.txtPercentage": "Տոկոսային", "SSE.Views.Toolbar.txtPound": "£ Ֆունտ", "SSE.Views.Toolbar.txtRouble": "₽ Ռուբլի", "SSE.Views.Toolbar.txtScheme1": "Գրասենյակ", "SSE.Views.Toolbar.txtScheme10": "Կենտրոնային", "SSE.Views.Toolbar.txtScheme11": "Մետրո ", "SSE.Views.Toolbar.txtScheme12": "Մոդուլ ", "SSE.Views.Toolbar.txtScheme13": "Վառ", "SSE.Views.Toolbar.txtScheme14": "Արևելաոճ", "SSE.Views.Toolbar.txtScheme15": "Սկզբնաղբյուր", "SSE.Views.Toolbar.txtScheme16": "Թուղթ", "SSE.Views.Toolbar.txtScheme17": "Խավարում", "SSE.Views.Toolbar.txtScheme18": "Տեխնիկական", "SSE.Views.Toolbar.txtScheme19": "Ուղի", "SSE.Views.Toolbar.txtScheme2": "Գորշասանդղակ", "SSE.Views.Toolbar.txtScheme20": "Քաղաքաոճ", "SSE.Views.Toolbar.txtScheme21": "Գունեղ", "SSE.Views.Toolbar.txtScheme22": "Նոր գրասենյակ", "SSE.Views.Toolbar.txtScheme3": "Գագաթ", "SSE.Views.Toolbar.txtScheme4": "Հարաբերություն", "SSE.Views.Toolbar.txtScheme5": "Քաղաքացիական", "SSE.Views.Toolbar.txtScheme6": "Համագումար ", "SSE.Views.Toolbar.txtScheme7": "Սեփական կապիտալ", "SSE.Views.Toolbar.txtScheme8": "Հոսք", "SSE.Views.Toolbar.txtScheme9": "Հրատարակիչ", "SSE.Views.Toolbar.txtScientific": "Գիտական", "SSE.Views.Toolbar.txtSearch": "Որոնել", "SSE.Views.Toolbar.txtSort": "Տեսակավորել", "SSE.Views.Toolbar.txtSortAZ": "Տեսակավորել աճման կարգով", "SSE.Views.Toolbar.txtSortZA": "Տեսակավորել նվազման կարգով", "SSE.Views.Toolbar.txtSpecial": "Հատուկ", "SSE.Views.Toolbar.txtTableTemplate": "Ձևաչափեք որպես աղյուսակի ձևանմուշ", "SSE.Views.Toolbar.txtText": "Տեքստ", "SSE.Views.Toolbar.txtTime": "Ժամանակ", "SSE.Views.Toolbar.txtUnmerge": "Անջատել բջիջները", "SSE.Views.Toolbar.txtYen": "¥ Իեն", "SSE.Views.Top10FilterDialog.textType": "Ցույց տալ", "SSE.Views.Top10FilterDialog.txtBottom": "Ներքև", "SSE.Views.Top10FilterDialog.txtBy": "Ըստ", "SSE.Views.Top10FilterDialog.txtItems": "Նյութ", "SSE.Views.Top10FilterDialog.txtPercent": "Տոկոսային", "SSE.Views.Top10FilterDialog.txtSum": "Գումար", "SSE.Views.Top10FilterDialog.txtTitle": "Լավագույն 10 ավտոմատ զտիչ", "SSE.Views.Top10FilterDialog.txtTop": "Վերև", "SSE.Views.Top10FilterDialog.txtValueTitle": "Լավագույն 10 զտիչ", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Արժեքի դաշտի կարգավորումներ", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "ՄԻՋԻՆ", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Հիմնական դաշտ", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Հիմնական տարր", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%2-ից %1-ը", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Հաշվել", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Հաշվել թվեր", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Մուտքագրել հարմարեցված անուն", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Տարբերությունը", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Ցուցանիշ", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Մաքս", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Նվազ.", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Ոչ մի հաշվարկ", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "տոկոսը", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Տոկոսային տարբերությունը", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Սյունակի տոկոսը", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Ընդամենը տոկոս", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Շարքի տոկոսը", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Արտադրանք ", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Գումարի աշխատեցում հետևյալում...", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Ցույց տալ արժեքները որպես", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Աղբյուրի անվանումը․", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Գումար", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Ամփոփել արժեքի դաշտը ըստ", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Տրբ", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "Տրբհմխ", "SSE.Views.ViewManagerDlg.closeButtonText": "Փակել", "SSE.Views.ViewManagerDlg.guestText": "Հյուր", "SSE.Views.ViewManagerDlg.lockText": "Կողպված է", "SSE.Views.ViewManagerDlg.textDelete": "Ջնջել", "SSE.Views.ViewManagerDlg.textDuplicate": "Կրկնօրինակել", "SSE.Views.ViewManagerDlg.textEmpty": "Դիտումներ դեռ չեն ստեղծվել։", "SSE.Views.ViewManagerDlg.textGoTo": "Գնալ դիտելու համար", "SSE.Views.ViewManagerDlg.textLongName": "Մուտքագրել անուն՝ 128 նիշից պակաս:", "SSE.Views.ViewManagerDlg.textNew": "Նոր", "SSE.Views.ViewManagerDlg.textRename": "Վերանվանել", "SSE.Views.ViewManagerDlg.textRenameError": "Դիտման անունը չպետք է դատարկ լինի:", "SSE.Views.ViewManagerDlg.textRenameLabel": "Վերանվանել տեսքը", "SSE.Views.ViewManagerDlg.textViews": "Թերթի դիտումներ", "SSE.Views.ViewManagerDlg.tipIsLocked": "Այս տարրը խմբագրվում է մեկ այլ օգտվողի կողմից:", "SSE.Views.ViewManagerDlg.txtTitle": "Թերթի դիտման կառավարիչ", "SSE.Views.ViewManagerDlg.warnDeleteView": "Դուք փորձում եք ջնջել «%1» ներկայումս միացված տեսքը:<br>Փակե՞լ այս տեսքը և ջնջե՞լ այն:", "SSE.Views.ViewTab.capBtnFreeze": "Փեղկերի սառեցում", "SSE.Views.ViewTab.capBtnSheetView": "Թերթի տեսք", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Միշտ ցուցադրել գործիքագոտին", "SSE.Views.ViewTab.textClose": "Փակել", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Միավորել թերթիկը և կարգավիճակի գծերը", "SSE.Views.ViewTab.textCreate": "Նոր", "SSE.Views.ViewTab.textDefault": "Կանխադրված", "SSE.Views.ViewTab.textFormula": "Բանաձևերի գոտի", "SSE.Views.ViewTab.textFreezeCol": "Սառեցնել առաջին սյունակը", "SSE.Views.ViewTab.textFreezeRow": "Սառեցնել վերին տողը", "SSE.Views.ViewTab.textGridlines": "Ցանցագծեր", "SSE.Views.ViewTab.textHeadings": "Վերնագրեր", "SSE.Views.ViewTab.textInterfaceTheme": "Ինտերֆեյսի թեմա", "SSE.Views.ViewTab.textLeftMenu": "Ձախ վահանակ", "SSE.Views.ViewTab.textManager": "Դիտել կառավարիչը", "SSE.Views.ViewTab.textRightMenu": "Աջ վահանակ", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Ցույց տալ սառեցված ապակիների ստվերը", "SSE.Views.ViewTab.textUnFreeze": "Ապասառեցնել փեղկերը", "SSE.Views.ViewTab.textZeros": "Ցույց տալ զրոները", "SSE.Views.ViewTab.textZoom": "Խոշորացնել", "SSE.Views.ViewTab.tipClose": "Փակել թերթի տեսքը", "SSE.Views.ViewTab.tipCreate": "Ստեղծել թերթիկի տեսք", "SSE.Views.ViewTab.tipFreeze": "Փեղկերի սառեցում", "SSE.Views.ViewTab.tipInterfaceTheme": "Ինտերֆեյսի ոճ", "SSE.Views.ViewTab.tipSheetView": "Թերթի տեսք", "SSE.Views.WatchDialog.closeButtonText": "Փակել", "SSE.Views.WatchDialog.textAdd": "Հավելել ժամացույց", "SSE.Views.WatchDialog.textBook": "Գիրք", "SSE.Views.WatchDialog.textCell": "Վանդակ", "SSE.Views.WatchDialog.textDelete": "Ջնջել ժամացույցը", "SSE.Views.WatchDialog.textDeleteAll": "Ջնջել բոլորը", "SSE.Views.WatchDialog.textFormula": "Բանաձև ", "SSE.Views.WatchDialog.textName": "Անուն", "SSE.Views.WatchDialog.textSheet": "Թերթ", "SSE.Views.WatchDialog.textValue": "Արժեք", "SSE.Views.WatchDialog.txtTitle": "Հետևման պատուհան", "SSE.Views.WBProtection.hintAllowRanges": "Թույլատրել խմբագրել ընդգրկույթները", "SSE.Views.WBProtection.hintProtectSheet": "Պաշտպանել թերթը", "SSE.Views.WBProtection.hintProtectWB": "Պաշտպանել աշխատագիրքը", "SSE.Views.WBProtection.txtAllowRanges": "Թույլատրել խմբագրել ընդգրկույթները", "SSE.Views.WBProtection.txtHiddenFormula": "Թաքնված բանաձևեր", "SSE.Views.WBProtection.txtLockedCell": "Կողպված վանդակ", "SSE.Views.WBProtection.txtLockedShape": "Ձևը կողպված է", "SSE.Views.WBProtection.txtLockedText": "Կողպեք տեքստը", "SSE.Views.WBProtection.txtProtectSheet": "Պաշտպանել թերթը", "SSE.Views.WBProtection.txtProtectWB": "Պաշտպանել աշխատագիրքը", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Մուտքագրեք գաղտնաբառ՝ թերթը չպաշտպանելու համար", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Չպաշտպանել թերթիկը", "SSE.Views.WBProtection.txtWBUnlockDescription": "Մուտքագրեք գաղտնաբառ՝ աշխատանքային գրքույկը պաշտպանելու համար", "SSE.Views.WBProtection.txtWBUnlockTitle": "Չպաշտպանել աշխատանքային գրքույկը"}