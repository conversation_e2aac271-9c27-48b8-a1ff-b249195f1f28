﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZAHLENWERT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ZAHLENWERT-Funktion</h1>
			<p>Die Funktion <b>ZAHLENWERT</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie konvertiert Text in Zahlen auf eine Weise, die vom Gebietsschema unabhängig ist. Handelt es sich bei dem zu konvertierenden Text nicht um eine Zahl, gibt die Funktion den Fehlerwert <b>#WERT!</b> zurück.</p>
			<p>Die Formelsyntax der Funktion <b>ZAHLENWERT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ZAHLENWERT(Text;[Dezimaltrennzeichen];[Gruppentrennzeichen])</em></b></p> 
			<p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Text</em></b> ist der in eine Zahl zu konvertierende Text.</p>
            <p style="text-indent: 50px;"><b><em>Dezimaltrennzeichen</em></b> ist das Zeichen, das zum Trennen der ganzen Zahl von den Nachkommastellen des Ergebnisses verwendet wird. Dieses Argument ist optional. Liegt keine Angabe vor, werden die Trennzeichen des aktuellen Gebietsschemas verwendet.</p>
            <p style="text-indent: 50px;"><b><em>Gruppentrennzeichen</em></b> ist das Zeichen, das zum Trennen von Zahlengruppen verwendet wird, z. B. zwischen Tausender und Hunderter oder zwischen Millionen und Tausender. Dieses Argument ist optional. Liegt keine Angabe vor, werden die Trennzeichen des aktuellen Gebietsschemas verwendet.</p>
            <p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p>Anwenden der Funktion <b>ZAHLENWERT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ZAHLENWERT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ZAHLENWERT-Funktion" src="../images/numbervalue.png" /></p>
		</div>
	</body>
</html>