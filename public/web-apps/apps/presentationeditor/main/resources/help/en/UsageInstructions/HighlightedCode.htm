﻿<!DOCTYPE html>
<html>
<head>
    <title>Insert highlighted code</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of Hightlight code plugin for ONLYOFFICE editors, which allows to embed code with the already adjusted style into presentations" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Insert highlighted code</h1>
        <p>In the <a target="_blank" href="https://www.onlyoffice.com/en/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a>, you can embed highlighted code with the already adjusted style in accordance with the programming language and coloring style of the program you have chosen.</p>
        <ol>
            <li>Go to your presentation and place the cursor at the location where you want to include the code.</li>
            <li>Switch to the <b>Plugins</b> tab and choose <div class = "icon icon-highlight"></div> <b>Highlight code</b>.</li>
            <li>Specify the programming <b>Language</b>.</li>
            <li>Select a <b>Style</b> of the code so that it appears as if it were open in this program.</li>
            <li>Specify if you want to replace tabs with spaces.</li>
            <li>Choose <b>Background color</b>. To do this, manually move the cursor over the palette or insert the <b>RGB</b>/<b>HSL</b>/<b>HEX</b> value.</li>
            <li>Click <b>OK</b> to insert the code.</li>
        </ol>
        <img class="gif" alt="Highlight plugin gif" src="../../images/highlight_plugin.gif" width="600" />
    </div>
</body>
</html>