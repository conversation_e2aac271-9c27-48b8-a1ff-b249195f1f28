﻿<!DOCTYPE html>
<html>
	<head>
        <title>Introducing the user interface of the Presentation Editor</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Introducing the user interface of the Presentation Editor</h1>
        <p>The <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> uses a tabbed interface where editing commands are grouped into tabs according to their functionality.</p>
            <div class="onlineDocumentFeatures">
                <p>Main window of the Online Presentation Editor:</p>
                <p><img alt="Online Presentation Editor window" src="../images/interface/editorwindow.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Main window of the Desktop Presentation Editor:</p>
                <p><img alt="Desktop Presentation Editor window" src="../images/interface/desktop_editorwindow.png" /></p>
            </div>
            <p>The editor interface consists of the following main elements:</p>
            <ol>
                <li>
                    The <b>Editor header</b> displays the logo, <span class="desktopDocumentFeatures">tabs for all opened presentations with their names</span> and menu tabs.
                    <p>On the left side of the <b>Editor header</b>, the <b>Save</b>, <b>Print file</b>, <b>Undo</b> and <b>Redo</b> buttons are located.</p>
                    <p><span class="big big-leftpart"></span></p>
                    <p>On the right side of the <b>Editor header</b>, along with the user name the following icons are displayed:</p>
                    <ul>
                        <li><div class="icon icon-gotodocuments"></div> <b>Open file location</b> - <span class="desktopDocumentFeatures">in the <em>desktop version</em>, it allows opening the folder, where the file is stored, in the <b>File Explorer</b> window.</span> <span class="onlineDocumentFeatures"> In the <em>online version</em>, it allows opening the folder of the <b>Documents</b> module, where the file is stored, in a new browser tab.</span></li>
                        <li class="onlineDocumentFeatures"><div class="icon icon-access_rights"></div> <b>Share</b> - (available in the <em>online version</em> only) allows <a href="../HelpfulHints/CollaborativeEditing.htm" onclick="onhyperlinkclick(this)">setting access rights</a> for the documents stored in the cloud.</li>
                        <li><div class="icon icon-favorites_icon"></div> <b>Mark as favorite</b> - click the star to add a file to favorites as to make it easy to find. The added file is just a shortcut so the file itself remains stored in its original location. Deleting a file from favorites does not remove the file from its original location.</li>
                        <li><div class="icon icon-search_icon_header"></div> <b>Search</b> - allows to search the presentation for a particular word or symbol, etc.</li>
                    </ul>
                </li>
                <li>
                    The <b>Top toolbar</b> displays a set of editing commands depending on the selected menu tab. Currently, the following tabs are available: <a href="../ProgramInterface/FileTab.htm" onclick="onhyperlinkclick(this)">File</a>, <a href="../ProgramInterface/HomeTab.htm" onclick="onhyperlinkclick(this)">Home</a>, <a href="../ProgramInterface/InsertTab.htm" onclick="onhyperlinkclick(this)">Insert</a>, <a href="../ProgramInterface/TransitionsTab.htm" onclick="onhyperlinkclick(this)">Transitions</a>, <a href="../ProgramInterface/AnimationTab.htm" onclick="onhyperlinkclick(this)">Animation</a>, <a href="../ProgramInterface/CollaborationTab.htm" onclick="onhyperlinkclick(this)">Collaboration</a>, <span class="desktopDocumentFeatures">Protection,</span> <a href="../ProgramInterface/PluginsTab.htm" onclick="onhyperlinkclick(this)">Plugins</a>.
                    <p>The <span class="icon icon-copy"></span> <b>Copy</b>, <span class="icon icon-paste"></span> <b>Paste</b>, <span class="icon icon-cut"></span> <b>Cut</b> and <span class="icon icon-selectall"></span> <b>Select All</b> options are always available on the left side of the <b>Top toolbar</b> regardless of the selected tab.</p>
                </li>
                <li>The <b>Status bar</b> at the bottom of the editor window contains the <a href="../UsageInstructions/PreviewPresentation.htm" onclick="onhyperlinkclick(this)">Start slideshow</a> icon, some <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">navigation tools</a>: slide number indicator and zoom buttons. The <b>Status bar</b> also displays some notifications (such as "All changes saved", ‘Connection is lost’ when there is no connection and the editor is trying to reconnect etc.) and allows <a href="../HelpfulHints/SpellChecking.htm" onclick="onhyperlinkclick(this)">setting the text language and enable spell checking</a>.</li>
                <li>
                    The <b>Left sidebar</b> contains the following icons:
                        <ul>
                            <li><div class="icon icon-searchicon"></div> - allows using the <a href="../HelpfulHints/Search.htm" onclick="onhyperlinkclick(this)">Search and Replace</a> tool,</li>
                            <li><div class="icon icon-slides"></div> - allows viewing slides and navigating them,</li>
                            <li><div class="icon icon-commentsicon"></div> - allows opening the <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">Comments</a> panel,</li>
                            <li class="onlineDocumentFeatures"><div class="icon icon-chaticon"></div> - (available in the <em>online version</em> only) allows opening the <a href="../HelpfulHints/CollaborativeEditing.htm#chat" onclick="onhyperlinkclick(this)">Chat</a> panel,</li>
                            <li class="onlineDocumentFeatures"><div class="icon icon-feedbackicon"></div> - (available in the <em>online version</em> only) allows contacting our support team,</li>
                            <li class="onlineDocumentFeatures"><div class="icon icon-about"></div> - (available in the <em>online version</em> only) allows viewing the information about the program.</li>
                        </ul>
                </li>
                <li>The <b>Right sidebar</b> allows adjusting additional parameters of different objects. When you select a particular object on a slide, the corresponding icon is activated on the right sidebar. Click this icon to expand the right sidebar.</li>
                <li>The horizontal and vertical <b>Rulers</b> help you place objects on a slide and allow you to set up tab stops and paragraph indents within the text boxes.</li>
                <li>The <b>Working area</b> allows viewing the presentation content, entering and editing data.</li>
                <li>The <b>Scroll bar</b> on the right allows scrolling the presentation up and down.</li>
            </ol>
            <p>For your convenience, you can hide some components and display them again when necessary. To learn more on how to adjust the view settings, please refer to <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">this page</a>.</p>
           
		</div>
	</body>
</html>