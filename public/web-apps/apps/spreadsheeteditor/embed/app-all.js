/*!
 * Copyright (c) Ascensio System SIA 2025. All rights reserved
 *
 * http://www.onlyoffice.com 
 *
 * Version: 4.3.0 (build:904)
 */

if(void 0===Common)var Common={};if(Common.Locale=new function(){"use strict";var l10n=null,loadcallback,apply=!1,defLang="{{DEFAULT_LANG}}",currentLang=defLang,_4letterLangs=["pt-pt","zh-tw"],_applyLocalization=function(e){try{if(e&&(loadcallback=e),l10n){for(var a in l10n){var n=a.split(".");if(n&&n.length>2){for(var o=window,t=0;t<n.length-1;++t)void 0===o[n[t]]&&(o[n[t]]=new Object),o=o[n[t]];o&&(o[n[n.length-1]]=l10n[a])}}loadcallback&&loadcallback()}else apply=!0}catch(e){}},_get=function(prop,scope){var res="";return l10n&&scope&&scope.name&&(res=l10n[scope.name+"."+prop],!res&&scope.default&&(res=scope.default)),res||(scope?eval(scope.name).prototype[prop]:"")},_getCurrentLanguage=function(){return currentLang},_getDefaultLanguage=function(){return defLang},_getLoadedLanguage=function(){return loadedLang},_getUrlParameterByName=function(e){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var a=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(location.search);return null==a?"":decodeURIComponent(a[1].replace(/\+/g," "))},_requireLang=function(e){"string"!=typeof e&&(e=null);var a=e||_getUrlParameterByName("lang")||defLang,n=_4letterLangs.indexOf(a.replace("_","-").toLowerCase());a=n<0?a.split(/[\-_]/)[0]:_4letterLangs[n],currentLang=a,fetch("locale/"+a+".json").then((function(e){if(!e.ok){if(n>=0)throw new Error("4letters error");if(currentLang=defLang,a!=defLang)return fetch("locale/"+defLang+".json");throw new Error("server error")}return e.json()})).then((function(e){if(e.json){if(!e.ok)throw new Error("server error");return e.json()}throw l10n=e,new Error("loaded")})).then((function(e){l10n=e||{},apply&&_applyLocalization()})).catch((function(e){return/4letters/.test(e)?setTimeout((function(){_requireLang(a.split(/[\-_]/)[0])}),0):!/loaded/.test(e)&&currentLang!=defLang&&defLang&&defLang.length<3?setTimeout((function(){_requireLang(defLang)}),0):(l10n=l10n||{},apply&&_applyLocalization(),void("loaded"==e.message||(currentLang=null,console.log("fetch error: "+e))))}))};if(window.fetch)_requireLang();else{var polyfills=["../vendor/fetch/fetch.umd"];window.Promise?require(polyfills,_requireLang):require(["../vendor/es6-promise/es6-promise.auto.min"],(function(){require(polyfills,_requireLang)}))}return{apply:_applyLocalization,get:_get,getCurrentLanguage:_getCurrentLanguage,getDefaultLanguage:_getDefaultLanguage}},void 0===window.Common&&(window.Common={}),Common.Gateway=new function(){var e=this,a=$(e),n={init:function(e){a.trigger("init",e)},openDocument:function(e){a.trigger("opendocument",e)},showMessage:function(e){a.trigger("showmessage",e)},applyEditRights:function(e){a.trigger("applyeditrights",e)},processSaveResult:function(e){a.trigger("processsaveresult",e)},processRightsChange:function(e){a.trigger("processrightschange",e)},refreshHistory:function(e){a.trigger("refreshhistory",e)},setHistoryData:function(e){a.trigger("sethistorydata",e)},setEmailAddresses:function(e){a.trigger("setemailaddresses",e)},setActionLink:function(e){a.trigger("setactionlink",e.url)},processMailMerge:function(e){a.trigger("processmailmerge",e)},downloadAs:function(e){a.trigger("downloadas",e)},processMouse:function(e){a.trigger("processmouse",e)},internalCommand:function(e){a.trigger("internalcommand",e)},resetFocus:function(e){a.trigger("resetfocus",e)},setUsers:function(e){a.trigger("setusers",e)},showSharingSettings:function(e){a.trigger("showsharingsettings",e)},setSharingSettings:function(e){a.trigger("setsharingsettings",e)},insertImage:function(e){a.trigger("insertimage",e)},setMailMergeRecipients:function(e){a.trigger("setmailmergerecipients",e)},setRevisedFile:function(e){a.trigger("setrevisedfile",e)},setFavorite:function(e){a.trigger("setfavorite",e)},requestClose:function(e){a.trigger("requestclose",e)},blurFocus:function(e){a.trigger("blurfocus",e)},grabFocus:function(e){a.trigger("grabfocus",e)},setReferenceData:function(e){a.trigger("setreferencedata",e)}},o=function(e){window.parent&&window.JSON&&(e.frameEditorId=window.frameEditorId,window.parent.postMessage(window.JSON.stringify(e),"*"))},t=function(e){!function(e){if(e.origin===window.parentOrigin||e.origin===window.location.origin||"null"===e.origin&&("file://"===window.parentOrigin||"file://"===window.location.origin)){var a=e.data;if("[object String]"===Object.prototype.toString.apply(a)&&window.JSON){var o,t;try{o=window.JSON.parse(a)}catch(e){o=""}o&&(t=n[o.command])&&t.call(this,o.data)}}}(e)};return window.attachEvent?window.attachEvent("onmessage",t):window.addEventListener("message",t,!1),{appReady:function(){o({event:"onAppReady"})},requestEditRights:function(){o({event:"onRequestEditRights"})},requestHistory:function(){o({event:"onRequestHistory"})},requestHistoryData:function(e){o({event:"onRequestHistoryData",data:e})},requestRestore:function(e,a,n){o({event:"onRequestRestore",data:{version:e,url:a,fileType:n}})},requestEmailAddresses:function(){o({event:"onRequestEmailAddresses"})},requestStartMailMerge:function(){o({event:"onRequestStartMailMerge"})},requestHistoryClose:function(e){o({event:"onRequestHistoryClose"})},reportError:function(e,a){o({event:"onError",data:{errorCode:e,errorDescription:a}})},reportWarning:function(e,a){o({event:"onWarning",data:{warningCode:e,warningDescription:a}})},sendInfo:function(e){o({event:"onInfo",data:e})},setDocumentModified:function(e){o({event:"onDocumentStateChange",data:e})},internalMessage:function(e,a){o({event:"onInternalMessage",data:{type:e,data:a}})},updateVersion:function(){o({event:"onOutdatedVersion"})},downloadAs:function(e,a){o({event:"onDownloadAs",data:{url:e,fileType:a}})},requestSaveAs:function(e,a,n){o({event:"onRequestSaveAs",data:{url:e,title:a,fileType:n}})},collaborativeChanges:function(){o({event:"onCollaborativeChanges"})},requestRename:function(e){o({event:"onRequestRename",data:e})},metaChange:function(e){o({event:"onMetaChange",data:e})},documentReady:function(){o({event:"onDocumentReady"})},requestClose:function(){o({event:"onRequestClose"})},requestMakeActionLink:function(e){o({event:"onMakeActionLink",data:e})},requestUsers:function(){o({event:"onRequestUsers"})},requestSendNotify:function(e){o({event:"onRequestSendNotify",data:e})},requestInsertImage:function(e){o({event:"onRequestInsertImage",data:{c:e}})},requestMailMergeRecipients:function(){o({event:"onRequestMailMergeRecipients"})},requestCompareFile:function(){o({event:"onRequestCompareFile"})},requestSharingSettings:function(){o({event:"onRequestSharingSettings"})},requestCreateNew:function(){o({event:"onRequestCreateNew"})},requestReferenceData:function(e){o({event:"onRequestReferenceData",data:e})},pluginsReady:function(){o({event:"onPluginsReady"})},on:function(n,o){a.on(n,(function(a,n){o.call(e,n)}))}}},void 0===window.Common&&(window.Common={}),Common.component=Common.component||{},Common.Analytics=Common.component.Analytics=new function(){var e;return{initialize:function(a,n){if(void 0===a)throw"Analytics: invalid id.";if(void 0===n||"[object String]"!==Object.prototype.toString.apply(n))throw"Analytics: invalid category type.";e=n,$("head").append('<script type="text/javascript">var _gaq = _gaq || [];_gaq.push(["_setAccount", "'+a+'"]);_gaq.push(["_trackPageview"]);(function() {var ga = document.createElement("script"); ga.type = "text/javascript"; ga.async = true;ga.src = ("https:" == document.location.protocol ? "https://ssl" : "http://www") + ".google-analytics.com/ga.js";var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(ga, s);})();<\/script>')},trackEvent:function(a,n,o){if(void 0!==a&&"[object String]"!==Object.prototype.toString.apply(a))throw"Analytics: invalid action type.";if(void 0!==n&&"[object String]"!==Object.prototype.toString.apply(n))throw"Analytics: invalid label type.";if(void 0!==o&&("[object Number]"!==Object.prototype.toString.apply(o)||!isFinite(o)))throw"Analytics: invalid value type.";if("undefined"!=typeof _gaq){if("undefined"===e)throw"Analytics is not initialized.";_gaq.push(["_trackEvent",e,a,n,o])}}}},void 0===window.Common&&(window.Common={}),Common.util=Common.util||{},Common.util.LanguageInfo=new function(){var e={54:["af","Afrikaans"],1078:["af-ZA","Afrikaans (Suid Afrika)","Afrikaans (South Africa)"],28:["sq","Shqipe"],1052:["sq-AL","Shqipe (Shqipëria)","Albanian (Albania)"],132:["gsw","Elsässisch"],1156:["gsw-FR","Elsässisch (Frànkrisch)","Alsatian (France)"],94:["am","አማርኛ"],1118:["am-ET","አማርኛ (ኢትዮጵያ)","Amharic (Ethiopia)"],1:["ar","العربية‏"],5121:["ar-DZ","العربية (الجزائر)‏","Arabic (Algeria)"],15361:["ar-BH","العربية (البحرين)‏","Arabic (Bahrain)"],3073:["ar-EG","العربية (مصر)‏","Arabic (Egypt)"],2049:["ar-IQ","العربية (العراق)‏","Arabic (Iraq)"],11265:["ar-JO","العربية (الأردن)‏","Arabic (Jordan)"],13313:["ar-KW","العربية (الكويت)‏","Arabic (Kuwait)"],12289:["ar-LB","العربية (لبنان)‏","Arabic (Lebanon)"],4097:["ar-LY","العربية (ليبيا)‏","Arabic (Libya)"],6145:["ar-MA","العربية (المملكة المغربية)‏","Arabic (Morocco)"],8193:["ar-OM","العربية (عمان)‏","Arabic (Oman)"],16385:["ar-QA","العربية (قطر)‏","Arabic (Qatar)"],1025:["ar-SA","العربية (المملكة العربية السعودية)‏","Arabic (Saudi Arabia)"],10241:["ar-SY","العربية (سوريا)‏","Arabic (Syria)"],7169:["ar-TN","العربية (تونس)‏","Arabic (Tunisia)"],14337:["ar-AE","العربية (الإمارات العربية المتحدة)‏","Arabic (U.A.E.)"],9217:["ar-YE","العربية (اليمن)‏","Arabic (Yemen)"],43:["hy","Հայերեն"],1067:["hy-AM","Հայերեն (Հայաստան)","Armenian (Armenia)"],77:["as","অসমীয়া"],1101:["as-IN","অসমীয়া (ভাৰত)","Assamese (India)"],44:["az","Azərbaycan­ılı"],29740:["az-Cyrl","Азәрбајҹан дили"],2092:["az-Cyrl-AZ","Азәрбајҹан (Азәрбајҹан)","Azeri (Cyrillic, Azerbaijan)"],30764:["az-Latn","Azərbaycan­ılı"],1068:["az-Latn-AZ","Azərbaycan­ılı (Azərbaycan)","Azeri (Latin, Azerbaijan)"],109:["ba","Башҡорт"],1133:["ba-RU","Башҡорт (Россия)","Bashkir (Russia)"],45:["eu","Euskara"],1069:["eu-ES","Euskara (Euskara)","Basque (Basque)"],35:["be","Беларускі"],1059:["be-BY","Беларускі (Беларусь)","Belarusian (Belarus)"],69:["bn","বাংলা"],2117:["bn-BD","বাংলা (বাংলাদেশ)","Bengali (Bangladesh)"],1093:["bn-IN","বাংলা (ভারত)","Bengali (India)"],30746:["bs","bosanski"],25626:["bs-Cyrl","Босански (Ћирилица)"],8218:["bs-Cyrl-BA","Босански (Босна и Херцеговина)","Bosnian (Cyrillic) (Bosnia and Herzegovina)"],26650:["bs-Latn","Bosanski (Latinica)"],5146:["bs-Latn-BA","Bosanski (Bosna i Hercegovina)","Bosnian (Latin) (Bosnia and Herzegovina)"],126:["br","Brezhoneg"],1150:["br-FR","Brezhoneg (Frañs)","Breton (France)"],2:["bg","Български"],1026:["bg-BG","Български (България)","Bulgarian (Bulgaria)"],3:["ca","Català"],1027:["ca-ES","Català (Català)","Catalan (Catalan)"],2051:["ca-ES-valencia","Català (Valencià)","Catalan (Valencia)"],30724:["zh","中文"],4:["zh-Hans","中文(简体)","Chinese (Simplified)"],2052:["zh-CN","中文(中华人民共和国)","Chinese (People's Republic of China)"],4100:["zh-SG","中文(新加坡)","Chinese (Simplified, Singapore)"],31748:["zh-Hant","中文(繁體)","Chinese (Traditional)"],3076:["zh-HK","中文(香港特別行政區)","Chinese (Traditional, Hong Kong S.A.R.)"],5124:["zh-MO","中文(澳門特別行政區)","Chinese (Traditional, Macao S.A.R.)"],1028:["zh-TW","中文(台灣)","Chinese (Traditional, Taiwan)"],131:["co","Corsu"],1155:["co-FR","Corsu (France)","Corsican (France)"],26:["hr","Hrvatski"],1050:["hr-HR","Hrvatski (Hrvatska)","Croatian (Croatia)"],4122:["hr-BA","Hrvatski (Bosna i Hercegovina)","Croatian (Bosnia and Herzegovina)"],5:["cs","Čeština"],1029:["cs-CZ","Čeština (Česká republika)","Czech (Czech Republic)"],6:["da","Dansk"],1030:["da-DK","Dansk (Danmark)","Danish (Denmark)"],140:["prs","درى‏"],1164:["prs-AF","درى (افغانستان)‏","Dari (Afghanistan)"],101:["dv","ދިވެހިބަސް‏"],1125:["dv-MV","ދިވެހިބަސް (ދިވެހި ރާއްޖެ)‏","Divehi (Maldives)"],19:["nl","Nederlands"],2067:["nl-BE","Nederlands (België)","Dutch (Belgium)"],1043:["nl-NL","Nederlands (Nederland)","Dutch (Netherlands)"],9:["en","English"],3081:["en-AU","English (Australia)","English (Australia)"],10249:["en-BZ","English (Belize)","English (Belize)"],4105:["en-CA","English (Canada)","English (Canada)"],9225:["en-029","English (Caribbean)","English (Caribbean)"],16393:["en-IN","English (India)","English (India)"],6153:["en-IE","English (Ireland)","English (Ireland)"],8201:["en-JM","English (Jamaica)","English (Jamaica)"],17417:["en-MY","English (Malaysia)","English (Malaysia)"],5129:["en-NZ","English (New Zealand)","English (New Zealand)"],13321:["en-PH","English (Philippines)","English (Philippines)"],18441:["en-SG","English (Singapore)","English (Singapore)"],7177:["en-ZA","English (South Africa)","English (South Africa)"],11273:["en-TT","English (Trinidad y Tobago)","English (Trinidad y Tobago)"],2057:["en-GB","English (United Kingdom)","English (United Kingdom)"],1033:["en-US","English (United States)","English (United States)"],12297:["en-ZW","English (Zimbabwe)","English (Zimbabwe)"],15369:["en-HK","English (Hong Kong)","English (Hong Kong)"],14345:["en-ID","English (Indonesia)","English (Indonesia)"],37:["et","Eesti"],1061:["et-EE","Eesti (Eesti)","Estonian (Estonia)"],56:["fo","Føroyskt"],1080:["fo-FO","Føroyskt (Føroyar)","Faroese (Faroe Islands)"],100:["fil","Filipino"],1124:["fil-PH","Filipino (Pilipinas)","Filipino (Philippines)"],11:["fi","Suomi"],1035:["fi-FI","Suomi (Suomi)","Finnish (Finland)"],12:["fr","Français"],2060:["fr-BE","Français (Belgique)","French (Belgium)"],3084:["fr-CA","Français (Canada)","French (Canada)"],1036:["fr-FR","Français (France)","French (France)"],5132:["fr-LU","Français (Luxembourg)","French (Luxembourg)"],6156:["fr-MC","Français (Principauté de Monaco)","French (Principality of Monaco)"],4108:["fr-CH","Français (Suisse)","French (Switzerland)"],15372:["fr-HT","Français (Haïti)","French (Haiti)"],9228:["fr-CG","Français (Congo-Brazzaville)","French (Congo)"],12300:["fr-CI","Français (Côte d’Ivoire)","French (Cote d'Ivoire)"],11276:["fr-CM","Français (Cameroun)","French (Cameroon)"],14348:["fr-MA","Français (Maroc)","French (Morocco)"],13324:["fr-ML","Français (Mali)","French (Mali)"],8204:["fr-RE","Français (La Réunion)","French (Reunion)"],10252:["fr-SN","Français (Sénégal)","French (Senegal)"],7180:["fr-West","French"],98:["fy","Frysk"],1122:["fy-NL","Frysk (Nederlân)","Frisian (Netherlands)"],86:["gl","Galego"],1110:["gl-ES","Galego (Galego)","Galician (Galician)"],55:["ka","ქართული"],1079:["ka-GE","ქართული (საქართველო)","Georgian (Georgia)"],7:["de","Deutsch"],3079:["de-AT","Deutsch (Österreich)","German (Austria)"],1031:["de-DE","Deutsch (Deutschland)","German (Germany)"],5127:["de-LI","Deutsch (Liechtenstein)","German (Liechtenstein)"],4103:["de-LU","Deutsch (Luxemburg)","German (Luxembourg)"],2055:["de-CH","Deutsch (Schweiz)","German (Switzerland)"],8:["el","Ελληνικά"],1032:["el-GR","Ελληνικά (Ελλάδα)","Greek (Greece)"],111:["kl","Kalaallisut"],1135:["kl-GL","Kalaallisut (Kalaallit Nunaat)","Greenlandic (Greenland)"],71:["gu","ગુજરાતી"],1095:["gu-IN","ગુજરાતી (ભારત)","Gujarati (India)"],104:["ha","Hausa"],31848:["ha-Latn","Hausa (Latin)"],1128:["ha-Latn-NG","Hausa (Nigeria)","Hausa (Latin) (Nigeria)"],13:["he","עברית‏"],1037:["he-IL","עברית (ישראל)‏","Hebrew (Israel)"],57:["hi","हिंदी"],1081:["hi-IN","हिंदी (भारत)","Hindi (India)"],14:["hu","Magyar"],1038:["hu-HU","Magyar (Magyarország)","Hungarian (Hungary)"],15:["is","Íslenska"],1039:["is-IS","Íslenska (Ísland)","Icelandic (Iceland)"],112:["ig","Igbo"],1136:["ig-NG","Igbo (Nigeria)","Igbo (Nigeria)"],33:["id","Bahasa Indonesia"],1057:["id-ID","Bahasa Indonesia (Indonesia)","Indonesian (Indonesia)"],93:["iu","Inuktitut"],31837:["iu-Latn","Inuktitut (Qaliujaaqpait)"],2141:["iu-Latn-CA","Inuktitut (Kanatami) (kanata)","Inuktitut (Latin) (Canada)"],30813:["iu-Cans","ᐃᓄᒃᑎᑐᑦ (ᖃᓂᐅᔮᖅᐸᐃᑦ)"],1117:["iu-Cans-CA","ᐃᓄᒃᑎᑐᑦ (ᑲᓇᑕᒥ)","Inuktitut (Canada)"],60:["ga","Gaeilge"],2108:["ga-IE","Gaeilge (Éire)","Irish (Ireland)"],52:["xh","isiXhosa"],1076:["xh-ZA","isiXhosa (uMzantsi Afrika)","isiXhosa (South Africa)"],53:["zu","isiZulu"],1077:["zu-ZA","isiZulu (iNingizimu Afrika)","isiZulu (South Africa)"],16:["it","Italiano"],1040:["it-IT","Italiano (Italia)","Italian (Italy)"],2064:["it-CH","Italiano (Svizzera)","Italian (Switzerland)"],17:["ja","日本語"],1041:["ja-JP","日本語 (日本)","Japanese (Japan)"],75:["kn","ಕನ್ನಡ"],1099:["kn-IN","ಕನ್ನಡ (ಭಾರತ)","Kannada (India)"],63:["kk","Қазақ"],1087:["kk-KZ","Қазақ (Қазақстан)","Kazakh (Kazakhstan)"],83:["km","ខ្មែរ"],1107:["km-KH","ខ្មែរ (កម្ពុជា)","Khmer (Cambodia)"],134:["qut","K'iche"],1158:["qut-GT","K'iche (Guatemala)","K'iche (Guatemala)"],135:["rw","Kinyarwanda"],1159:["rw-RW","Kinyarwanda (Rwanda)","Kinyarwanda (Rwanda)"],65:["sw","Kiswahili"],1089:["sw-KE","Kiswahili (Kenya)","Kiswahili (Kenya)"],87:["kok","कोंकणी"],1111:["kok-IN","कोंकणी (भारत)","Konkani (India)"],18:["ko","한국어"],1042:["ko-KR","한국어 (대한민국)","Korean (Korea)"],64:["ky","Кыргыз"],1088:["ky-KG","Кыргыз (Кыргызстан)","Kyrgyz (Kyrgyzstan)"],84:["lo","ລາວ"],1108:["lo-LA","ລາວ (ສ.ປ.ປ. ລາວ)","Lao (Lao P.D.R.)"],38:["lv","Latviešu"],1062:["lv-LV","Latviešu (Latvija)","Latvian (Latvia)"],39:["lt","Lietuvių"],1063:["lt-LT","Lietuvių (Lietuva)","Lithuanian (Lithuania)"],31790:["dsb","Dolnoserbšćina"],2094:["dsb-DE","Dolnoserbšćina (Nimska)","Lower Sorbian (Germany)"],110:["lb","Lëtzebuergesch"],1134:["lb-LU","Lëtzebuergesch (Luxembourg)","Luxembourgish (Luxembourg)"],1071:["mk-MK","Македонски јазик (Македонија)","Macedonian (Former Yugoslav Republic of Macedonia)"],47:["mk","Македонски јазик"],62:["ms","Bahasa Melayu"],2110:["ms-BN","Bahasa Melayu (Brunei Darussalam)","Malay (Brunei Darussalam)"],1086:["ms-MY","Bahasa Melayu (Malaysia)","Malay (Malaysia)"],76:["ml","മലയാളം"],1100:["ml-IN","മലയാളം (ഭാരതം)","Malayalam (India)"],58:["mt","Malti"],1082:["mt-MT","Malti (Malta)","Maltese (Malta)"],129:["mi","Reo Māori"],1153:["mi-NZ","Reo Māori (Aotearoa)","Maori (New Zealand)"],122:["arn","Mapudungun"],1146:["arn-CL","Mapudungun (Chile)","Mapudungun (Chile)"],78:["mr","मराठी"],1102:["mr-IN","मराठी (भारत)","Marathi (India)"],124:["moh","Kanien'kéha"],1148:["moh-CA","Kanien'kéha (Canada)","Mohawk (Canada)"],80:["mn","Монгол хэл"],30800:["mn-Cyrl","Монгол хэл"],1104:["mn-MN","Монгол хэл (Монгол улс)","Mongolian (Cyrillic, Mongolia)"],31824:["mn-Mong","ᠮᠤᠨᠭᠭᠤᠯ ᠬᠡᠯᠡ"],2128:["mn-Mong-CN","ᠮᠤᠨᠭᠭᠤᠯ ᠬᠡᠯᠡ (ᠪᠦᠭᠦᠳᠡ ᠨᠠᠢᠷᠠᠮᠳᠠᠬᠤ ᠳᠤᠮᠳᠠᠳᠤ ᠠᠷᠠᠳ ᠣᠯᠣᠰ)","Mongolian (Traditional Mongolian) (People's Republic of China)"],97:["ne","नेपाली"],1121:["ne-NP","नेपाली (नेपाल)","Nepali (Nepal)"],2145:["ne-IN","नेपाली (भारत)","Nepali (India)"],20:["no","Norsk"],31764:["nb","Norsk (bokmål)"],1044:["nb-NO","Norsk, bokmål (Norge)","Norwegian, Bokmål (Norway)"],30740:["nn","Norsk (Nynorsk)"],2068:["nn-NO","Norsk, nynorsk (Noreg)","Norwegian, Nynorsk (Norway)"],130:["oc","Occitan"],1154:["oc-FR","Occitan (França)","Occitan (France)"],72:["or","ଓଡ଼ିଆ"],1096:["or-IN","ଓଡ଼ିଆ (ଭାରତ)","Oriya (India)"],99:["ps","پښتو‏"],1123:["ps-AF","پښتو (افغانستان)‏","Pashto (Afghanistan)"],41:["fa","فارسى‏"],1065:["fa-IR","فارسى (ایران)‏","Persian (Iran)"],21:["pl","Polski"],1045:["pl-PL","Polski (Polska)","Polish (Poland)"],22:["pt","Português"],1046:["pt-BR","Português (Brasil)","Portuguese (Brazil)"],2070:["pt-PT","Português (Portugal)","Portuguese (Portugal)"],70:["pa","ਪੰਜਾਬੀ"],1094:["pa-IN","ਪੰਜਾਬੀ (ਭਾਰਤ)","Punjabi (India)"],107:["quz","Runasimi"],1131:["quz-BO","Runasimi (Qullasuyu)","Quechua (Bolivia)"],2155:["quz-EC","Runasimi (Ecuador)","Quechua (Ecuador)"],3179:["quz-PE","Runasimi (Piruw)","Quechua (Peru)"],24:["ro","Română"],1048:["ro-RO","Română (România)","Romanian (Romania)"],2072:["ro-MD","Română (Moldova)","Romanian (Republic of Moldova)"],23:["rm","Rumantsch"],1047:["rm-CH","Rumantsch (Svizra)","Romansh (Switzerland)"],25:["ru","Русский"],1049:["ru-RU","Русский (Россия)","Russian (Russia)"],2073:["ru-MD","Русский (Молдавия)","Russian (Republic of Moldova)"],28731:["smn","Sämikielâ"],9275:["smn-FI","Sämikielâ (Suomâ)","Sami (Inari) (Finland)"],31803:["smj","Julevusámegiella"],4155:["smj-NO","Julevusámegiella (Vuodna)","Sami (Lule) (Norway)"],5179:["smj-SE","Julevusámegiella (Svierik)","Sami (Lule) (Sweden)"],59:["se","Davvisámegiella"],3131:["se-FI","Davvisámegiella (Suopma)","Sami (Northern) (Finland)"],1083:["se-NO","Davvisámegiella (Norga)","Sami (Northern) (Norway)"],2107:["se-SE","Davvisámegiella (Ruoŧŧa)","Sami (Northern) (Sweden)"],29755:["sms","Sääm´ǩiõll"],8251:["sms-FI","Sääm´ǩiõll (Lää´ddjânnam)","Sami (Skolt) (Finland)"],30779:["sma","åarjelsaemiengiele"],6203:["sma-NO","åarjelsaemiengiele (Nöörje)","Sami (Southern) (Norway)"],7227:["sma-SE","åarjelsaemiengiele (Sveerje)","Sami (Southern) (Sweden)"],79:["sa","संस्कृत"],1103:["sa-IN","संस्कृत (भारतम्)","Sanskrit (India)"],145:["gd","Gàidhlig"],1169:["gd-GB","Gàidhlig (An Rìoghachd Aonaichte)","Scottish Gaelic (United Kingdom)"],31770:["sr","Srpski"],27674:["sr-Cyrl","Српски (Ћирилица)"],7194:["sr-Cyrl-BA","Српски (Босна и Херцеговина)","Serbian (Cyrillic) (Bosnia and Herzegovina)"],12314:["sr-Cyrl-ME","Српски (Црна Гора)","Serbian (Cyrillic, Montenegro)"],3098:["sr-Cyrl-CS","Српски (Србија и Црна Гора (Претходно))","Serbian (Cyrillic, Serbia and Montenegro (Former))"],10266:["sr-Cyrl-RS","Српски (Србија)","Serbian (Cyrillic, Serbia)"],28698:["sr-Latn","Srpski (Latinica)"],6170:["sr-Latn-BA","Srpski (Bosna i Hercegovina)","Serbian (Latin, Bosnia and Herzegovina)"],11290:["sr-Latn-ME","Srpski (Crna Gora)","Serbian (Latin, Montenegro)"],2074:["sr-Latn-CS","Srpski (Srbija i Crna Gora (Prethodno))","Serbian (Latin, Serbia and Montenegro (Former))"],9242:["sr-Latn-RS","Srpski (Srbija, Latinica)","Serbian (Latin, Serbia)"],108:["nso","Sesotho sa Leboa"],1132:["nso-ZA","Sesotho sa Leboa (Afrika Borwa)","Sesotho sa Leboa (South Africa)"],50:["tn","Setswana"],1074:["tn-ZA","Setswana (Aforika Borwa)","Setswana (South Africa)"],91:["si","සිංහ"],1115:["si-LK","සිංහ (ශ්‍රී ලංකා)","Sinhala (Sri Lanka)"],27:["sk","Slovenčina"],1051:["sk-SK","Slovenčina (Slovenská republika)","Slovak (Slovakia)"],36:["sl","Slovenski"],1060:["sl-SI","Slovenski (Slovenija)","Slovenian (Slovenia)"],10:["es","Español"],11274:["es-AR","Español (Argentina)","Spanish (Argentina)"],16394:["es-BO","Español (Bolivia)","Spanish (Bolivia)"],13322:["es-CL","Español (Chile)","Spanish (Chile)"],9226:["es-CO","Español (Colombia)","Spanish (Colombia)"],5130:["es-CR","Español (Costa Rica)","Spanish (Costa Rica)"],7178:["es-DO","Español (República Dominicana)","Spanish (Dominican Republic)"],12298:["es-EC","Español (Ecuador)","Spanish (Ecuador)"],17418:["es-SV","Español (El Salvador)","Spanish (El Salvador)"],4106:["es-GT","Español (Guatemala)","Spanish (Guatemala)"],18442:["es-HN","Español (Honduras)","Spanish (Honduras)"],2058:["es-MX","Español (México)","Spanish (Mexico)"],19466:["es-NI","Español (Nicaragua)","Spanish (Nicaragua)"],6154:["es-PA","Español (Panamá)","Spanish (Panama)"],15370:["es-PY","Español (Paraguay)","Spanish (Paraguay)"],10250:["es-PE","Español (Perú)","Spanish (Peru)"],20490:["es-PR","Español (Puerto Rico)","Spanish (Puerto Rico)"],3082:["es-ES","Español (España, alfabetización internacional)","Spanish (Spain)"],21514:["es-US","Español (Estados Unidos)","Spanish (United States)"],14346:["es-UY","Español (Uruguay)","Spanish (Uruguay)"],8202:["es-VE","Español (Republica Bolivariana de Venezuela)","Spanish (Venezuela)"],1034:["es-ES_tradnl","Spanish"],22538:["es-419","Español (América Latina y el Caribe)","Spanish (Latin America and the Caribbean)"],23562:["es-CU","Español (Cuba)","Spanish (Cuba)"],29:["sv","Svenska"],2077:["sv-FI","Svenska (Finland)","Swedish (Finland)"],1053:["sv-SE","Svenska (Sverige)","Swedish (Sweden)"],90:["syr","ܣܘܪܝܝܐ‏"],1114:["syr-SY","ܣܘܪܝܝܐ (سوريا)‏","Syriac (Syria)"],40:["tg","Тоҷикӣ"],31784:["tg-Cyrl","Тоҷикӣ"],1064:["tg-Cyrl-TJ","Тоҷикӣ (Тоҷикистон)","Tajik (Cyrillic) (Tajikistan)"],95:["tzm","Tamazight"],31839:["tzm-Latn","Tamazight (Latin)"],2143:["tzm-Latn-DZ","Tamazight (Djazaïr)","Tamazight (Latin) (Algeria)"],73:["ta","தமிழ்"],1097:["ta-IN","தமிழ் (இந்தியா)","Tamil (India)"],68:["tt","Татар"],1092:["tt-RU","Татар (Россия)","Tatar (Russia)"],74:["te","తెలుగు"],1098:["te-IN","తెలుగు (భారత దేశం)","Telugu (India)"],30:["th","ไทย"],1054:["th-TH","ไทย (ไทย)","Thai (Thailand)"],81:["bo","བོད་ཡིག"],1105:["bo-CN","བོད་ཡིག (ཀྲུང་ཧྭ་མི་དམངས་སྤྱི་མཐུན་རྒྱལ་ཁབ།)","Tibetan (People's Republic of China)"],2129:["bo-BT","Tibetan (Bhutan)","Tibetan (Bhutan)"],31:["tr","Türkçe"],1055:["tr-TR","Türkçe (Türkiye)","Turkish (Turkey)"],66:["tk","Türkmençe"],1090:["tk-TM","Türkmençe (Türkmenistan)","Turkmen (Turkmenistan)"],34:["uk","Українська"],1058:["uk-UA","Українська (Україна)","Ukrainian (Ukraine)"],46:["hsb","Hornjoserbšćina"],1070:["hsb-DE","Hornjoserbšćina (Němska)","Upper Sorbian (Germany)"],32:["ur","اُردو‏"],1056:["ur-PK","اُردو (پاکستان)‏","Urdu (Islamic Republic of Pakistan)"],2080:["ur-IN","اُردو (بھارت)‏","Urdu (India)"],128:["ug","ئۇيغۇر يېزىقى‏"],1152:["ug-CN","(ئۇيغۇر يېزىقى (جۇڭخۇا خەلق جۇمھۇرىيىتى‏","Uighur (People's Republic of China)"],30787:["uz-Cyrl","Ўзбек"],2115:["uz-Cyrl-UZ","Ўзбек (Ўзбекистон)","Uzbek (Cyrillic, Uzbekistan)"],67:["uz","U'zbek"],31811:["uz-Latn","U'zbek"],1091:["uz-Latn-UZ","U'zbek (U'zbekiston Respublikasi)","Uzbek (Latin, Uzbekistan)"],42:["vi","Tiếng Việt"],1066:["vi-VN","Tiếng Việt (Việt Nam)","Vietnamese (Vietnam)"],82:["cy","Cymraeg"],1106:["cy-GB","Cymraeg (y Deyrnas Unedig)","Welsh (United Kingdom)"],136:["wo","Wolof"],1160:["wo-SN","Wolof (Sénégal)","Wolof (Senegal)"],133:["sah","Саха"],1157:["sah-RU","Саха (Россия)","Yakut (Russia)"],120:["ii","ꆈꌠꁱꂷ"],1144:["ii-CN","ꆈꌠꁱꂷ (ꍏꉸꏓꂱꇭꉼꇩ)","Yi (People's Republic of China)"],106:["yo","Yoruba"],1130:["yo-NG","Yoruba (Nigeria)","Yoruba (Nigeria)"],1126:["bin-NG","Bini (Nigeria)","Bini (Nigeria)"],1116:["chr-US","ᏣᎳᎩ (ᏌᏊ ᎢᏳᎾᎵᏍᏔᏅ ᏍᎦᏚᎩ)","Cherokee (United States)"],1127:["fuv-NG","Nigerian Fulfulde (Nigeria)","Nigerian Fulfulde (Nigeria)"],1138:["gaz-ET","West Central Oromo (Ethiopia)","West Central Oromo (Ethiopia)"],1140:["gn-PY","Guarani (Paraguay)","Guarani (Paraguay)"],1141:["haw-US","ʻŌlelo Hawaiʻi (ʻAmelika Hui Pū ʻIa)","Hawaiian (United States)"],1129:["ibb-NG","Ibibio (Nigeria)","Ibibio (Nigeria)"],1137:["kr-NG","Kanuri (Nigeria)","Kanuri (Nigeria)"],1112:["mni","Manipuri","Manipuri"],1109:["my-MM","Burmese (Myanmar)","Burmese (Myanmar)"],1145:["pap-AN","Papiamento, Netherlands Antilles","Papiamento, Netherlands Antilles"],2118:["pa-PK","Panjabi (Pakistan)","Panjabi (Pakistan)"],1165:["plt-MG","Plateau Malagasy (Madagascar)","Plateau Malagasy (Madagascar)"],1113:["sd-IN","Sindhi (India)","Sindhi (India)"],2137:["sd-PK","Sindhi (Pakistan)","Sindhi (Pakistan)"],1143:["so-SO","Soomaali (Soomaaliya)","Somali (Somalia)"],1072:["st-ZA","Southern Sotho (South Africa)","Southern Sotho (South Africa)"],1139:["ti-ER","ትግርኛ (ኤርትራ)","Tigrinya (Eritrea)"],2163:["ti-ET","ትግርኛ (ኢትዮጵያ)","Tigrinya (Ethiopia)"],1119:["tmz","Tamanaku"],3167:["tmz-MA","Tamaziɣt n laṭlaṣ (Meṛṛuk)","Tamanaku (Morocco)"],1073:["ts-ZA","Tsonga (South Africa)","Tsonga (South Africa)"],1075:["ven-ZA","South Africa","South Africa"]};return{getLocalLanguageName:function(a){return e[a]||["",a]},getLocalLanguageCode:function(a){if(a)for(var n in e)if(e[n][0].toLowerCase()===a.toLowerCase())return n;return null},getLanguages:function(){return e}}},!window.common&&(window.common={}),common.localStorage=new function(){var e,a,n={};Common.Gateway.on("internalcommand",(function(e){"localstorage"==e.type&&(n=e.keys)}));var o=function(e,a,o){if(i)try{localStorage.setItem(e,a)}catch(e){}else n[e]=a,!0===o&&Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:{name:a}})},t=function(e){return i?localStorage.getItem(e):void 0===n[e]?null:n[e]};try{var i=!!window.localStorage}catch(e){i=!1}return{getId:function(){return e},setId:function(a){e=a},getItem:t,getBool:function(e,a){var n=t(e);return a=a||!1,null!==n?0!=parseInt(n):a},setBool:function(e,a,n){o(e,a?1:0,n)},setItem:o,removeItem:function(e){i?localStorage.removeItem(e):delete n[e]},setKeysFilter:function(e){a=e},getKeysFilter:function(){return a},itemExists:function(e){return null!==t(e)},sync:function(){i||Common.Gateway.internalMessage("localstorage",{cmd:"get",keys:a})},save:function(){i||Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:n})}}},!window.common&&(window.common={}),!common.utils&&(common.utils={}),common.utils=new function(){var e=navigator.userAgent.toLowerCase();return{openLink:function(e){if(e){var a=window.open(e,"_blank");a&&a.focus()}},dialogPrint:function(e,a){if($("#id-print-frame").remove(),e){var n=document.createElement("iframe");n.id="id-print-frame",n.style.display="none",n.style.visibility="hidden",n.style.position="fixed",n.style.right="0",n.style.bottom="0",document.body.appendChild(n),n.onload=function(){try{n.contentWindow.focus(),n.contentWindow.print(),n.contentWindow.blur(),window.focus()}catch(e){a.asc_DownloadAs(new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.PDF))}},n.src=e}},htmlEncode:function(e){return $("<div/>").text(e).html()},fillUserInfo:function(e,a,n,o){var t=e||{};return t.anonymous=!t.id,!t.id&&(t.id=o),t.fullname=t.name?t.name:n,t.group&&(t.fullname=t.group.toString()+AscCommon.UserInfoParser.getSeparator()+t.fullname),t.guest=!t.name,t},fixedDigits:function(e,a,n){void 0===n&&(n="0");for(var o="",t=e.toString(),i=t.length;i<a;i++)o+=n;return o+t},getKeyByValue:function(e,a){for(var n in e)if(e.hasOwnProperty(n)&&e[n]===a)return n},isMac:/macintosh|mac os x/.test(e)}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.LoadMask=function(e){var a,n,o=e||$(document.body),t="",i=0,r=!1;return{show:function(){a&&n||(a=$('<div class="asc-loadmask-body" role="presentation" tabindex="-1"><i id="loadmask-spinner" class="asc-loadmask-image"></i><div class="asc-loadmask-title"></div></div>'),n=$('<div class="asc-loadmask"></div>')),$(".asc-loadmask-title",a).html(t),r||(r=!0,i=setTimeout((function(){o.append(n),o.append(a),a.css("min-width",$(".asc-loadmask-title",a).width()+105)}),500))},hide:function(){i&&(clearTimeout(i),i=0),n&&n.remove(),a&&a.remove(),n=a=null,r=!1},setTitle:function(e){if(t=e,o&&a){var n=$(".asc-loadmask-title",a);n.html(t),a.css("min-width",n.width()+105)}}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.modals=new function(){var e='<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="idm-title" aria-hidden="true"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button><h4 id="idm-title" class="modal-title">{title}</h4></div><div class="modal-body">{body}</div><div class="modal-footer">{footer}</div></div></div></div>',a='<div class="share-link"><input id="id-short-url" class="form-control" type="text" readonly/></div><div class="share-buttons"><span class="svg big-facebook" data-name="facebook"></span><span class="svg big-twitter" data-name="twitter"></span><span class="svg big-email" data-name="email"></span><div class="autotest" id="email" style="display: none"></div></div>';return{create:function(n,o){var t;if(!o&&(o="body"),"share"==n){if(window.config&&window.config.btnsShare){let e=[];for(const a of Object.keys(config.btnsShare))e.push(`<span class="svg big-${a}" data-name="${a}"></span>`);if(e){let n=$(a);n.find(".autotest").prevAll().remove(),n.eq(1).prepend(e.join("")),a=$("<div>").append(n).html()}}t=$(e.replace(/\{title}/,this.txtShare).replace(/\{body}/,a).replace(/\{footer}/,'<button id="btn-copyshort" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(o).attr("id","dlg-share")}else"embed"==n&&(t=$(e.replace(/\{title}/,this.txtEmbed).replace(/\{body}/,'<div class="size-manual"><span class="caption">{width}:</span><input id="txt-embed-width" class="form-control input-xs" type="text" value="400px"><input id="txt-embed-height" class="form-control input-xs right" type="text" value="600px"><span class="right caption">{height}:</span></div><textarea id="txt-embed-url" rows="4" class="form-control" readonly></textarea>').replace(/\{width}/,this.txtWidth).replace(/\{height}/,this.txtHeight).replace(/\{footer}/,'<button id="btn-copyembed" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(o).attr("id","dlg-embed"));return t},txtWidth:"Width",txtHeight:"Height",txtShare:"Share Link",txtCopy:"Copy to clipboard",txtEmbed:"Embed"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.modals=new function(){var e,a,n,o='<iframe allowtransparency="true" frameborder="0" scrolling="no" src="{embed-url}" width="{width}" height="{height}"></iframe>';function t(e,a){e.select(),document.execCommand("copy")||window.alert("Browser's error! Use keyboard shortcut [Ctrl] + [C]")}var i=function(){e=common.view.modals.create("share");var a=encodeURIComponent(n.shareUrl),o="mailto:?subject=I have shared a document with you: "+n.docTitle+"&body=I have shared a document with you: "+a;e.find("#btn-copyshort").on("click",t.bind(this,e.find("#id-short-url"))),e.find(".share-buttons > span").on("click",(function(e){if(window.config){const a=$(e.target).attr("data-name"),o=config.btnsShare[a];if(o&&o.getUrl)return void window.open(o.getUrl(n.shareUrl,n.docTitle),o.target||"",o.features||"menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600")}var t;switch($(e.target).attr("data-name")){case"facebook":t="https://www.facebook.com/sharer/sharer.php?u="+n.shareUrl+"&t="+encodeURI(n.docTitle),window.open(t,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"twitter":t="https://twitter.com/share?url="+a,n.docTitle&&(t+=encodeURIComponent("&text="+n.docTitle)),window.open(t,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"email":window.open(o,"_self")}})),e.find("#id-short-url").val(n.shareUrl),e.find(".share-buttons > #email.autotest").attr("data-test",o)};function r(){var e=a.find("#txt-embed-width"),t=a.find("#txt-embed-height"),i=parseInt(e.val()),r=parseInt(t.val());i<400&&(i=400),r<600&&(r=600),a.find("#txt-embed-url").text(o.replace("{embed-url}",n.embedUrl).replace("{width}",i).replace("{height}",r)),e.val(i+"px"),t.val(r+"px")}return{init:function(e){n=e},attach:function(s){s.share&&n.shareUrl&&(e||i(),$(s.share).on("click",(function(a){e.modal("show")}))),s.embed&&n.embedUrl&&(a||function(){var e=(a=common.view.modals.create("embed")).find("#txt-embed-url");e.text(o.replace("{embed-url}",n.embedUrl).replace("{width}",400).replace("{height}",600)),a.find("#btn-copyembed").on("click",t.bind(this,e)),a.find("#txt-embed-width, #txt-embed-height").on({keypress:function(e){13==e.keyCode&&r()},focusout:function(e){r()}})}(),$(s.embed).on("click",(function(e){a.modal("show")})))}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.SearchBar=new function(){var e='<div class="asc-window search-window" style="display: none;"><div class="body">{body}</div></div>';return{create:function(a){return!a&&(a="body"),$(e.replace(/\{body}/,'<input type="text" id="search-bar-text" placeholder="{textFind}" autocomplete="off"><div class="tools"><button id="search-bar-back" class="svg-icon search-arrow-up"></button><button id="search-bar-next" class="svg-icon search-arrow-down"></button><button id="search-bar-close" class="svg-icon search-close"></button></div>').replace(/\{textFind}/,this.textFind)).appendTo(a).attr("id","dlg-search")},disableNavButtons:function(e,a){var n=""===$("#search-bar-text").val()||!a;$("#search-bar-back").attr({disabled:n}),$("#search-bar-next").attr({disabled:n})},textFind:"Find"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.SearchBar=new function(){var e,a,n,o,t,i,r={searchText:""},s=function(e){r.searchText!==e&&(r.newSearchText=e,t=new Date,void 0===i&&(i=setInterval((function(){new Date-t<400||(r.searchText=r.newSearchText,c(),clearInterval(i),i=void 0)}),10)))},c=function(e,a){var n=new Asc.asc_CFindOptions;return n.asc_setFindWhat(r.searchText),n.asc_setScanForward("back"!=e),n.asc_setIsMatchCase(!1),n.asc_setIsWholeCell(!1),n.asc_setScanOnOnlySheet(Asc.c_oAscSearchBy.Sheet),n.asc_setScanByRows(!0),n.asc_setLookIn(Asc.c_oAscFindLookIn.Formulas),n.asc_setNeedRecalc(a),n.asc_setNotSearchEmptyCells(!0),!!o.asc_findText(n)||(common.view.SearchBar.disableNavButtons(),!1)},l=function(e,a,n){("keydown"===e&&13===n.keyCode||"keydown"!==e)&&(r.searchText=a,c(e)&&i&&(clearInterval(i),i=void 0))},d=function(e,a){common.view.SearchBar.disableNavButtons(e,a)},u=function(e){r.isHighlightedResults!==e&&(o.asc_selectSearchingResults(e),r.isHighlightedResults=e)},m=function(){c(void 0,!0)};return{init:function(e){n=e},setApi:function(e){(o=e)&&(o.asc_registerCallback("asc_onSetSearchCurrent",d),o.asc_registerCallback("asc_onActiveSheetChanged",m))},show:function(){if(e||(e=common.view.SearchBar.create(),"bottom"===n.toolbarDocked?e.css({right:"45px",bottom:"31px"}):e.css({right:"45px",top:"31px"}),(a=e.find("#search-bar-text")).on("input",(function(e){common.view.SearchBar.disableNavButtons(),s(a.val())})).on("keydown",(function(e){l("keydown",a.val(),e)})),e.find("#search-bar-back").on("click",(function(e){l("back",a.val())})),e.find("#search-bar-next").on("click",(function(e){l("next",a.val())})),e.find("#search-bar-close").on("click",(function(a){u(!1),e.hide()})),common.view.SearchBar.disableNavButtons()),!e.is(":visible")){u(!0);var t=o&&o.asc_GetSelectedText()||r.searchText;a.val(t),t.length>0&&s(t),e.show(),setTimeout((function(){a.focus(),a.select()}),10)}}}},void 0===SSE)var SSE={};SSE.ApplicationView=new function(){var e;return{create:function(){(e=$("#box-tools button")).addClass("dropdown-toggle").attr("data-toggle","dropdown").attr("aria-expanded","true"),e.parent().append('<ul class="dropdown-menu pull-right"><li><a id="idt-download"><span class="mi-icon svg-icon download"></span>'+this.txtDownload+'</a></li><li><a id="idt-print"><span class="mi-icon svg-icon print"></span>'+this.txtPrint+'</a></li><li class="divider"></li><li><a id="idt-search"><span class="mi-icon svg-icon search"></span>'+this.txtSearch+'</a></li><li class="divider"></li><li><a id="idt-share" data-toggle="modal"><span class="mi-icon svg-icon share"></span>'+this.txtShare+'</a></li><li><a id="idt-close" data-toggle="modal"><span class="mi-icon svg-icon go-to-location"></span>'+this.txtFileLocation+'</a></li><li class="divider"></li><li><a id="idt-embed" data-toggle="modal"><span class="mi-icon svg-icon embed"></span>'+this.txtEmbed+'</a></li><li><a id="idt-fullscreen"><span class="mi-icon svg-icon fullscr"></span>'+this.txtFullScreen+"</a></li></ul>")},tools:{get:function(a){return e.parent().find(a)}},txtDownload:"Download",txtPrint:"Print",txtShare:"Share",txtEmbed:"Embed",txtFullScreen:"Full Screen",txtFileLocation:"Open file location",txtSearch:"Search"}},SSE.ApplicationController=new function(){var e,a,n,o,t,i={},r={},s={},c={},l={},d=0,u=!1,m=[6,-15],g=-256;if("undefined"==typeof isBrowserSupported||isBrowserSupported())return common.localStorage.setId("text"),common.localStorage.setKeysFilter("sse-,asc.table"),common.localStorage.sync(),{create:function(){return u||(e=this,u=!0,$(window).resize((function(){a&&a.asc_Resize()})),window.onbeforeunload=N,(a=new Asc.spreadsheet_api({"id-view":"editor_sdk",embedded:!0}))&&(a.asc_registerCallback("asc_onEndAction",E),a.asc_registerCallback("asc_onError",x),a.asc_registerCallback("asc_onOpenDocumentProgress",C),a.asc_registerCallback("asc_onSheetsChanged",b),a.asc_registerCallback("asc_onActiveSheetChanged",f),Common.Gateway.on("init",h),Common.Gateway.on("opendocument",p),Common.Gateway.on("showmessage",I),Common.Gateway.appReady(),common.controller.SearchBar.setApi(a))),e},errorDefaultMessage:"Error code: %1",unknownErrorText:"Unknown error.",convertationTimeoutText:"Conversion timeout exceeded.",convertationErrorText:"Conversion failed.",downloadErrorText:"Download failed.",criticalErrorTitle:"Error",notcriticalErrorTitle:"Warning",scriptLoadError:"The connection is too slow, some of the components could not be loaded. Please reload the page.",errorFilePassProtect:"The file is password protected and cannot be opened.",errorAccessDeny:"You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.",errorUserDrop:"The file cannot be accessed right now.",unsupportedBrowserErrorText:"Your browser is not supported.",textOf:"of",downloadTextText:"Downloading spreadsheet...",waitText:"Please, wait...",textLoadingDocument:"Loading spreadsheet",txtClose:"Close",errorFileSizeExceed:"The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.",errorUpdateVersionOnDisconnect:"Internet connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.",textGuest:"Guest",textAnonymous:"Anonymous",errorForceSave:"An error occurred while saving the file. Please use the 'Download as' option to save the file to your computer hard drive or try again later.",errorLoadingFont:"Fonts are not loaded.<br>Please contact your Document Server administrator.",errorTokenExpire:"The document security token has expired.<br>Please contact your Document Server administrator.",openErrorText:"An error has occurred while opening the file",errorInconsistentExtDocx:"An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.",errorInconsistentExtXlsx:"An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.",errorInconsistentExtPptx:"An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.",errorInconsistentExtPdf:"An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.",errorInconsistentExt:"An error has occurred while opening the file.<br>The file content does not match the file extension."};function h(e){i=$.extend(i,e.config),s=$.extend(s,e.config.embedded),common.controller.modals.init(s),common.controller.SearchBar.init(s),"bottom"===s.toolbarDocked?($("#toolbar").addClass("bottom"),$(".viewer").addClass("bottom"),$("#box-tools").removeClass("dropdown").addClass("dropup"),m[1]=-40):($("#toolbar").addClass("top"),$(".viewer").addClass("top")),i.canBackToFolder=!1!==i.canBackToFolder&&i.customization&&i.customization.goback&&(i.customization.goback.url||i.customization.goback.requestClose&&i.canRequestClose);var n="string"==typeof i.region?i.region.toLowerCase():i.region;n=null!==(n=Common.util.LanguageInfo.getLanguages().hasOwnProperty(n)?n:Common.util.LanguageInfo.getLocalLanguageCode(n))?parseInt(n):i.lang?parseInt(Common.util.LanguageInfo.getLocalLanguageCode(i.lang)):1033,a.asc_setLocale(n)}function p(n){if(r=n.doc){c=$.extend(c,r.permissions);var o=new Asc.asc_CDocInfo,l=new Asc.asc_CUserInfo,d=!("object"==typeof i.customization&&"object"==typeof i.customization.anonymous&&!1===i.customization.anonymous.request),u="object"==typeof i.customization&&"object"==typeof i.customization.anonymous&&"string"==typeof i.customization.anonymous.label&&""!==i.customization.anonymous.label.trim()?common.utils.htmlEncode(i.customization.anonymous.label):e.textGuest,m=d?common.localStorage.getItem("guest-username"):null,g=common.utils.fillUserInfo(i.user,i.lang,m?m+" ("+u+")":e.textAnonymous,common.localStorage.getItem("guest-id")||"uid-"+Date.now());g.anonymous&&common.localStorage.setItem("guest-id",g.id),l.put_Id(g.id),l.put_FullName(g.fullname),l.put_IsAnonymousUser(g.anonymous),o.put_Id(r.key),o.put_Url(r.url),o.put_DirectUrl(r.directUrl),o.put_Title(r.title),o.put_Format(r.fileType),o.put_VKey(r.vkey),o.put_UserInfo(l),o.put_CallbackUrl(i.callbackUrl),o.put_Token(r.token),o.put_Permissions(r.permissions),o.put_EncryptedInfo(i.encryptionKeys),o.put_Lang(i.lang),o.put_Mode(i.mode);var h=!i.customization||!1!==i.customization.macros;o.asc_putIsEnabledMacroses(!!h),h=!i.customization||!1!==i.customization.plugins,o.asc_putIsEnabledPlugins(!!h),a&&(a.asc_registerCallback("asc_onGetEditorPermissions",S),a.asc_registerCallback("asc_onRunAutostartMacroses",M),a.asc_setDocInfo(o),a.asc_getEditorPermissions(i.licenseUrl,i.customerId),a.asc_enableKeyEvents(!0),Common.Analytics.trackEvent("Load","Start")),s.docTitle=r.title,(t=$("#title-doc-name")).text(s.docTitle||"")}}function f(e){var n=$("#worksheets");n.find("> li").removeClass("active"),n.find("#worksheet"+e).addClass("active"),a.asc_showWorksheet(e)}function b(){d=a.asc_getWorksheetsCount();var e=function(e){var a=$(this).attr("id").match(/\d+$/);a.length>0&&(a=parseInt(a[0]))>-1&&a<d&&f(a)},n=$("#worksheets");n.find("li").off(),n.empty();for(var o=0;o<d;o++){var t='<li id="worksheet{index}">{title}</li>'.replace(/\{index}/,o).replace(/\{title}/,a.asc_getWorksheetName(o).replace(/\s/g,"&nbsp;"));$(t).appendTo(n).on("click",e)}f(a.asc_getActiveWorksheetIndex())}function w(e,a){Common.Gateway.downloadAs(e,a)}function v(){!1!==c.print&&a.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86))}function y(e){common.utils.dialogPrint(e,a)}function k(){$("#loading-mask").fadeOut("slow")}function S(e){l.canBranding=e.asc_getCustomization(),l.canBranding&&function(e){if(e&&e.logo){var a=$("#header-logo");(e.logo.image||e.logo.imageEmbedded)&&(a.html('<img src="'+(e.logo.image||e.logo.imageEmbedded)+'" style="max-width:100px; max-height:20px;"/>'),a.css({"background-image":"none",width:"auto",height:"auto"}),e.logo.imageEmbedded&&console.log("Obsolete: The 'imageEmbedded' parameter of the 'customization.logo' section is deprecated. Please use 'image' parameter instead.")),e.logo.url?a.attr("href",e.logo.url):void 0!==e.logo.url&&(a.removeAttr("href"),a.removeAttr("target"))}}(i.customization);var n=t.parent(),o=n.position().left,r=n.next().outerWidth();o<r?n.css("padding-left",r-o):n.css("padding-right",o-r),A(Asc.c_oAscAsyncActionType.BlockInteraction,g),a.asc_setViewMode(!0),a.asc_LoadDocument()}function C(a){var n=(a.asc_getCurrentFont()+a.asc_getCurrentImage())/(a.asc_getFontsCount()+a.asc_getImagesCount());e.loadMask&&e.loadMask.setTitle(e.textLoadingDocument+": "+common.utils.fixedDigits(Math.min(Math.round(100*n),100),3,"  ")+"%")}function A(a,n){var o="";switch(n){case Asc.c_oAscAsyncAction.Print:o=e.downloadTextText;break;case g:o=e.textLoadingDocument+"           ";break;default:o=e.waitText}a==Asc.c_oAscAsyncActionType.BlockInteraction&&(e.loadMask||(e.loadMask=new common.view.LoadMask),e.loadMask.setTitle(o),e.loadMask.show())}function E(n,o){if(n===Asc.c_oAscAsyncActionType.BlockInteraction){if(o===Asc.c_oAscAsyncAction.Open){if(a){a.asc_Resize();var t=i.customization&&i.customization.zoom?parseInt(i.customization.zoom)/100:1;a.asc_setZoom(t>0?t:1)}!function(){k(),E(Asc.c_oAscAsyncActionType.BlockInteraction,g);var e,n=$("#box-tools .divider"),o=$("#box-tools a").length;!1===c.print&&($("#idt-print").hide(),o--),s.saveUrl&&!1!==c.download||($("#idt-download").hide(),o--),s.shareUrl||($("#idt-share").hide(),o--),i.canBackToFolder||($("#idt-close").hide(),o--),o<7&&($(n[0]).hide(),$(n[1]).hide()),s.embedUrl||($("#idt-embed").hide(),o--),s.fullscreenUrl||($("#idt-fullscreen").hide(),o--),o<1?$("#box-tools").addClass("hidden"):s.embedUrl||s.fullscreenUrl||$(n[2]).hide(),common.controller.modals.attach({share:"#idt-share",embed:"#idt-embed"}),a.asc_registerCallback("asc_onMouseMove",z),a.asc_registerCallback("asc_onHyperlinkClick",common.utils.openLink),a.asc_registerCallback("asc_onDownloadUrl",w),a.asc_registerCallback("asc_onPrint",v),a.asc_registerCallback("asc_onPrintUrl",y),a.asc_registerCallback("asc_onStartAction",A),Common.Gateway.on("processmouse",L),Common.Gateway.on("downloadas",T),Common.Gateway.on("requestclose",_),SSE.ApplicationView.tools.get("#idt-fullscreen").on("click",(function(){common.utils.openLink(s.fullscreenUrl)})),SSE.ApplicationView.tools.get("#idt-download").on("click",(function(){s.saveUrl&&!1!==c.download&&common.utils.openLink(s.saveUrl),Common.Analytics.trackEvent("Save")})),SSE.ApplicationView.tools.get("#idt-print").on("click",(function(){a.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86)),Common.Analytics.trackEvent("Print")})),SSE.ApplicationView.tools.get("#idt-close").on("click",(function(){i.customization&&i.customization.goback&&(i.customization.goback.requestClose&&i.canRequestClose?Common.Gateway.requestClose():i.customization.goback.url&&(!1!==i.customization.goback.blank?window.open(i.customization.goback.url,"_blank"):window.parent.location.href=i.customization.goback.url))})),SSE.ApplicationView.tools.get("#idt-search").on("click",(function(){common.controller.SearchBar.show()})),$("#id-btn-zoom-in").on("click",(function(){if(a){var e=Math.floor(10*a.asc_getZoom())/10;(e+=.1)>0&&!(e>5)&&a.asc_setZoom(e)}})),$("#id-btn-zoom-out").on("click",(function(){if(a){var e=Math.ceil(10*a.asc_getZoom())/10;!((e-=.1)<.1)&&a.asc_setZoom(e)}}));var t=!1;$(document).mousemove((function(a){$("#id-btn-zoom-in").fadeIn(),$("#id-btn-zoom-out").fadeIn(),t=!0,e||(e=setInterval((function(){t||($("#id-btn-zoom-in").fadeOut(),$("#id-btn-zoom-out").fadeOut(),clearInterval(e),e=void 0),t=!1}),2e3))}));var r=!1;$(document.body).on("show.bs.modal",".modal",(function(e){r=!0,a.asc_enableKeyEvents(!1)})).on("hidden.bs.modal",".modal",(function(e){r=!1,a.asc_enableKeyEvents(!0)})).on("hidden.bs.dropdown",".dropdown",(function(e){r||a.asc_enableKeyEvents(!0)})).on("blur","input, textarea",(function(e){r||/area_id/.test(e.target.id)||a.asc_enableKeyEvents(!0)})),$("#editor_sdk").on("click",(function(e){"canvas"==e.target.localName&&e.currentTarget.focus()})),$(document).on("mousewheel",(function(e){!e.ctrlKey&&!e.metaKey||e.altKey||(e.preventDefault(),e.stopPropagation())})),Common.Gateway.documentReady(),Common.Analytics.trackEvent("Load","Complete")}(),b()}e.loadMask&&e.loadMask.hide()}}function x(a,n,o){if(a==Asc.c_oAscError.ID.LoadingScriptError)return $("#id-critical-error-title").text(e.criticalErrorTitle),$("#id-critical-error-message").text(e.scriptLoadError),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){window.location.reload()})),void $("#id-critical-error-dialog").css("z-index",20002).modal("show");var t;switch(k(),E(Asc.c_oAscAsyncActionType.BlockInteraction,g),a){case Asc.c_oAscError.ID.Unknown:t=e.unknownErrorText;break;case Asc.c_oAscError.ID.ConvertationTimeout:t=e.convertationTimeoutText;break;case Asc.c_oAscError.ID.ConvertationError:t=e.convertationErrorText;break;case Asc.c_oAscError.ID.ConvertationOpenError:t=e.openErrorText;break;case Asc.c_oAscError.ID.DownloadError:t=e.downloadErrorText;break;case Asc.c_oAscError.ID.ConvertationPassword:t=e.errorFilePassProtect;break;case Asc.c_oAscError.ID.UserDrop:t=e.errorUserDrop;break;case Asc.c_oAscError.ID.ConvertationOpenLimitError:t=e.errorFileSizeExceed;break;case Asc.c_oAscError.ID.UpdateVersion:t=e.errorUpdateVersionOnDisconnect;break;case Asc.c_oAscError.ID.AccessDeny:t=e.errorAccessDeny;break;case Asc.c_oAscError.ID.ForceSaveButton:case Asc.c_oAscError.ID.ForceSaveTimeout:t=e.errorForceSave;break;case Asc.c_oAscError.ID.LoadingFontError:t=e.errorLoadingFont;break;case Asc.c_oAscError.ID.KeyExpire:t=e.errorTokenExpire;break;case Asc.c_oAscError.ID.ConvertationOpenFormat:t="pdf"===o?e.errorInconsistentExtPdf.replace("%1",r.fileType||""):"docx"===o?e.errorInconsistentExtDocx.replace("%1",r.fileType||""):"xlsx"===o?e.errorInconsistentExtXlsx.replace("%1",r.fileType||""):"pptx"===o?e.errorInconsistentExtPptx.replace("%1",r.fileType||""):e.errorInconsistentExt;break;default:t=e.errorDefaultMessage.replace("%1",a)}n==Asc.c_oAscError.Level.Critical?(Common.Gateway.reportError(a,t),$("#id-critical-error-title").text(e.criticalErrorTitle),$("#id-critical-error-message").html(t),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){window.location.reload()}))):(Common.Gateway.reportWarning(a,t),$("#id-critical-error-title").text(e.notcriticalErrorTitle),$("#id-critical-error-message").html(t),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){$("#id-critical-error-dialog").modal("hide")}))),$("#id-critical-error-dialog").modal("show"),Common.Analytics.trackEvent("Internal Error",a.toString())}function I(a){a&&(k(),$("#id-error-mask-title").text(e.criticalErrorTitle),$("#id-error-mask-text").text(a.msg),$("#id-error-mask").css("display","block"),Common.Analytics.trackEvent("External Error"))}function L(e){if("mouseup"==e.type){var n=document.getElementById("editor_sdk");if(n){var o=n.getBoundingClientRect(),t=window.event||arguments.callee.caller.arguments[0];a.asc_onMouseUp(t,e.x-o.left,e.y-o.top)}}}function _(){Common.Gateway.requestClose()}function T(){!1!==c.download?a.asc_DownloadAs(new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.XLSX,!0)):Common.Gateway.reportError(Asc.c_oAscError.ID.AccessDeny,e.errorAccessDeny)}function z(e){if(e.length){for(var a,t=e.length;t>0;t--)if(e[t-1].asc_getType()==Asc.c_oAscMouseMoveType.Hyperlink){a=e[t-1];break}a?(n||((n=$(".hyperlink-tooltip")).tooltip({container:"body",trigger:"manual"}),n.on("shown.bs.tooltip",(function(e){(o=n.data("bs.tooltip").tip()).css({left:n.ttpos[0]+m[0],top:n.ttpos[1]+m[1]}),o.find(".tooltip-arrow").css({left:10})}))),o?o.css({left:a.asc_getX()+m[0],top:a.asc_getY()+m[1]}):(n.ttpos=[a.asc_getX(),a.asc_getY()],n.tooltip("show"))):o&&(o.tooltip("hide"),o=!1)}}function M(){i.customization&&!1===i.customization.macros||a&&a.asc_runAutostartMacroses()}function N(){common.localStorage.save()}Common.Gateway.reportError(void 0,this.unsupportedBrowserErrorText)},(0,window.jQuery)((function(){Common.Locale.apply((function(){SSE.ApplicationView.create(),SSE.ApplicationController.create()}))}));