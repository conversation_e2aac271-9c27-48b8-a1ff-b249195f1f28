﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - Insert tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert tab</h1>
            <p>The <b>Insert</b> tab in the <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> allows adding visual objects and comments to your presentation.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Presentation Editor:</p>
                <p><img alt="Insert tab" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Presentation Editor:</p>
                <p><img alt="Insert tab" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li>insert <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tables</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">text boxes and Text Art objects</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">pictures</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">shapes</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">charts</a>,</li>
                <li>insert <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">comments</a> and <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">hyperlinks</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">footers, date and time, slide numbers</a>.</li>
                <li>insert <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">equations</a>, <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">symbols</a>,</li>
                <li class="desktopDocumentFeatures">insert audio and video records stored on the hard disk drive of your computer (available in the <em>desktop version</em> only, not available for Mac OS).
                    <p class="note">
                        <b>Note</b>: to be able to playback video, you'll need to install codecs, for example, <b>K-Lite</b>.
                    </p>
                </li>
            </ul>
		</div>
	</body>
</html>