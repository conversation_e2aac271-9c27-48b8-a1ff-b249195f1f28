﻿<!DOCTYPE html>
<html>
	<head>
		<title>Plugins tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - Plugins tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Plugins tab</h1>
            <p>The <b>Plugins</b> tab in the <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> makes it possible to access the advanced editing features using the available third-party components. Here you can also use macros to simplify routine operations.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Presentation Editor:</p>
                <p><img alt="Plugins tab" src="../images/interface/pluginstab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Presentation Editor:</p>
                <p><img alt="Plugins tab" src="../images/interface/desktop_pluginstab.png" /></p>
            </div>
            <p class="desktopDocumentFeatures">The <b>Settings</b> button allows you to open the window where you can view and manage all installed plugins and add your own ones.</p>
            <p>The <b>Macros</b> button allows to open the window where you can create your own macros and run them. To learn more about macros, please refer to our <a target="_blank" href="https://api.onlyoffice.com/plugin/macros" onclick="onhyperlinkclick(this)">API Documentation</a>.</p>
            <p>Currently, the following plugins are available:</p>		
            <ul>
                <li class="desktopDocumentFeatures"><b>Send</b> allows sending the presentation via email using the default desktop mail client (available in the <em>desktop version</em> only),</li>
                <li><a href="../UsageInstructions/HighlightedCode.htm" onclick="onhyperlinkclick(this)">Highlight code</a> allows highlighting the code syntax by selecting the necessary language, style, background color, etc.,</li>
                <li><a href="../UsageInstructions/PhotoEditor.htm" onclick="onhyperlinkclick(this)">Photo Editor</a> allows editing images: cropping, flipping, rotating, drawing lines and shapes, adding icons and text, loading a mask and applying filters such as Grayscale, Invert, Sepia, Blur, Sharpen, Emboss, etc.,</li>
                <li><a href="../UsageInstructions/Thesaurus.htm" onclick="onhyperlinkclick(this)">Thesaurus</a> allows finding synonyms and antonyms for the selected word and replacing it with the chosen one,</li>
                <li><a href="../UsageInstructions/Translator.htm" onclick="onhyperlinkclick(this)">Translator</a> allows translating the selected text into other languages,
                    <p class="note"><b>Note</b>: this plugin doesn't work in Internet Explorer.</p>
                </li>
                <li><a href="../UsageInstructions/YouTube.htm" onclick="onhyperlinkclick(this)">YouTube</a> allows embedding YouTube videos into your presentation.</li>
            </ul>
            <p>To learn more about plugins, please refer to our <a target="_blank" href="https://api.onlyoffice.com/plugin/basic" onclick="onhyperlinkclick(this)">API Documentation</a>. All the currently existing open source plugin examples are available on <a target="_blank" href="https://github.com/ONLYOFFICE/sdkjs-plugins" onclick="onhyperlinkclick(this)">GitHub</a>.</p>
		</div>
	</body>
</html>