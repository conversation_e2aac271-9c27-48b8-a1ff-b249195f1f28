{"cancelButtonText": "取消", "Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Chat.textEnterMessage": "在这里输入你的信息", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.define.chartData.textArea": "面积图", "Common.define.chartData.textAreaStacked": "堆积面积图", "Common.define.chartData.textAreaStackedPer": "百分比堆积面积图", "Common.define.chartData.textBar": "条形图", "Common.define.chartData.textBarNormal": "簇状柱形图", "Common.define.chartData.textBarNormal3d": "三维簇状柱形图", "Common.define.chartData.textBarNormal3dPerspective": "三维柱形图", "Common.define.chartData.textBarStacked": "堆积柱形图", "Common.define.chartData.textBarStacked3d": "三维堆积柱形图", "Common.define.chartData.textBarStackedPer": "百分比堆积柱形图", "Common.define.chartData.textBarStackedPer3d": "三维百分比堆积柱形图", "Common.define.chartData.textCharts": "流程图", "Common.define.chartData.textColumn": "柱状图", "Common.define.chartData.textColumnSpark": "列", "Common.define.chartData.textCombo": "组合图", "Common.define.chartData.textComboAreaBar": "堆积面积图 - 簇状柱形图", "Common.define.chartData.textComboBarLine": "簇状柱形图 - 折线图", "Common.define.chartData.textComboBarLineSecondary": "簇状柱形图 - 次坐标轴上的折线图", "Common.define.chartData.textComboCustom": "自定义组合", "Common.define.chartData.textDoughnut": "圆环图​​", "Common.define.chartData.textHBarNormal": "簇状条形图", "Common.define.chartData.textHBarNormal3d": "三维簇状条形图", "Common.define.chartData.textHBarStacked": "堆积条形图", "Common.define.chartData.textHBarStacked3d": "三维堆积条形图", "Common.define.chartData.textHBarStackedPer": "百分比堆积条形图", "Common.define.chartData.textHBarStackedPer3d": "三维百分比堆积条形图", "Common.define.chartData.textLine": "折线图", "Common.define.chartData.textLine3d": "三维折线图", "Common.define.chartData.textLineMarker": "带数据标记的折线图", "Common.define.chartData.textLineSpark": "线", "Common.define.chartData.textLineStacked": "堆积折线图", "Common.define.chartData.textLineStackedMarker": "带数据标记的堆积折线图", "Common.define.chartData.textLineStackedPer": "百分比堆积折线图", "Common.define.chartData.textLineStackedPerMarker": "带数据标记的百分比堆积折线图", "Common.define.chartData.textPie": "饼图", "Common.define.chartData.textPie3d": "三维饼图", "Common.define.chartData.textPoint": "散点图", "Common.define.chartData.textScatter": "散点图​​", "Common.define.chartData.textScatterLine": "带直线的散点图", "Common.define.chartData.textScatterLineMarker": "带直线和数据标记的散点图", "Common.define.chartData.textScatterSmooth": "带平滑线的散点图", "Common.define.chartData.textScatterSmoothMarker": "带平滑线和数据标记的散点图", "Common.define.chartData.textSparks": "迷你", "Common.define.chartData.textStock": "股价图", "Common.define.chartData.textSurface": "平面", "Common.define.chartData.textWinLossSpark": "赢/输", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "无设定格式", "Common.define.conditionalData.text1Above": "标准偏差高于1", "Common.define.conditionalData.text1Below": "标准偏差低于1", "Common.define.conditionalData.text2Above": "标准偏差高于2", "Common.define.conditionalData.text2Below": "标准偏差低于2", "Common.define.conditionalData.text3Above": "标准偏差高于3", "Common.define.conditionalData.text3Below": "标准偏差低于3", "Common.define.conditionalData.textAbove": "高于", "Common.define.conditionalData.textAverage": "平均值", "Common.define.conditionalData.textBegins": "始于", "Common.define.conditionalData.textBelow": "低于", "Common.define.conditionalData.textBetween": "介于", "Common.define.conditionalData.textBlank": "空白", "Common.define.conditionalData.textBlanks": "包含空白", "Common.define.conditionalData.textBottom": "底部", "Common.define.conditionalData.textContains": "包含", "Common.define.conditionalData.textDataBar": "数据栏", "Common.define.conditionalData.textDate": "日期", "Common.define.conditionalData.textDuplicate": "重复", "Common.define.conditionalData.textEnds": "结尾为", "Common.define.conditionalData.textEqAbove": "等于或高于", "Common.define.conditionalData.textEqBelow": "等于或低于", "Common.define.conditionalData.textEqual": "等于", "Common.define.conditionalData.textError": "错误", "Common.define.conditionalData.textErrors": "包含错误", "Common.define.conditionalData.textFormula": "公式", "Common.define.conditionalData.textGreater": "大于", "Common.define.conditionalData.textGreaterEq": "大于或等于", "Common.define.conditionalData.textIconSets": "图标集", "Common.define.conditionalData.textLast7days": "最近7天", "Common.define.conditionalData.textLastMonth": "上月", "Common.define.conditionalData.textLastWeek": "上星期", "Common.define.conditionalData.textLess": "小于", "Common.define.conditionalData.textLessEq": "小于或等于", "Common.define.conditionalData.textNextMonth": "下个月", "Common.define.conditionalData.textNextWeek": "下星期", "Common.define.conditionalData.textNotBetween": "不介于", "Common.define.conditionalData.textNotBlanks": "不包含空白", "Common.define.conditionalData.textNotContains": "不包含", "Common.define.conditionalData.textNotEqual": "不等于", "Common.define.conditionalData.textNotErrors": "不包含错误", "Common.define.conditionalData.textText": "文本", "Common.define.conditionalData.textThisMonth": "本月", "Common.define.conditionalData.textThisWeek": "本周", "Common.define.conditionalData.textToday": "今天", "Common.define.conditionalData.textTomorrow": "明天", "Common.define.conditionalData.textTop": "顶部", "Common.define.conditionalData.textUnique": "唯一", "Common.define.conditionalData.textValue": "值为", "Common.define.conditionalData.textYesterday": "昨天", "Common.define.smartArt.textAccentedPicture": "重音图片", "Common.define.smartArt.textAccentProcess": "重点流程", "Common.define.smartArt.textAlternatingFlow": "交替流", "Common.define.smartArt.textAlternatingHexagons": "交替六边形", "Common.define.smartArt.textAlternatingPictureBlocks": "交替图片块", "Common.define.smartArt.textAlternatingPictureCircles": "交替图片圆形", "Common.define.smartArt.textArchitectureLayout": "结构布局", "Common.define.smartArt.textArrowRibbon": "带形箭头", "Common.define.smartArt.textAscendingPictureAccentProcess": "升序图片重点流程", "Common.define.smartArt.textBalance": "平衡", "Common.define.smartArt.textBasicBendingProcess": "基本蛇形流程", "Common.define.smartArt.textBasicBlockList": "基本列表", "Common.define.smartArt.textBasicChevronProcess": "基本 V 形流程", "Common.define.smartArt.textBasicCycle": "基本循环", "Common.define.smartArt.textBasicMatrix": "基本矩阵", "Common.define.smartArt.textBasicPie": "基本饼图", "Common.define.smartArt.textBasicProcess": "基本流程", "Common.define.smartArt.textBasicPyramid": "基本棱锥图", "Common.define.smartArt.textBasicRadial": "基本射线图", "Common.define.smartArt.textBasicTarget": "基本目标图", "Common.define.smartArt.textBasicTimeline": "基本时间线", "Common.define.smartArt.textBasicVenn": "基本维恩图", "Common.define.smartArt.textBendingPictureAccentList": "蛇形图片重点列表", "Common.define.smartArt.textBendingPictureBlocks": "蛇形图片块", "Common.define.smartArt.textBendingPictureCaption": "蛇形图片题注", "Common.define.smartArt.textBendingPictureCaptionList": "蛇形图片题注列表", "Common.define.smartArt.textBendingPictureSemiTranparentText": "蛇形图片半透明文本", "Common.define.smartArt.textBlockCycle": "块循环", "Common.define.smartArt.textBubblePictureList": "气泡图片列表", "Common.define.smartArt.textCaptionedPictures": "题注图片", "Common.define.smartArt.textChevronAccentProcess": "V 形重点流程", "Common.define.smartArt.textChevronList": "V 型列表", "Common.define.smartArt.textCircleAccentTimeline": "圆形重点日程表", "Common.define.smartArt.textCircleArrowProcess": "圆箭头流程", "Common.define.smartArt.textCirclePictureHierarchy": "圆形图片层次结构", "Common.define.smartArt.textCircleProcess": "循环流程", "Common.define.smartArt.textCircleRelationship": "循环关系", "Common.define.smartArt.textCircularBendingProcess": "环状蛇形流程", "Common.define.smartArt.textCircularPictureCallout": "圆形图片标注", "Common.define.smartArt.textClosedChevronProcess": "闭合 V 形流程", "Common.define.smartArt.textContinuousArrowProcess": "连续箭头流程", "Common.define.smartArt.textContinuousBlockProcess": "连续块状流程", "Common.define.smartArt.textContinuousCycle": "连续循环", "Common.define.smartArt.textContinuousPictureList": "连续图片列表", "Common.define.smartArt.textConvergingArrows": "汇聚箭头", "Common.define.smartArt.textConvergingRadial": "聚合射线", "Common.define.smartArt.textConvergingText": "聚合文本", "Common.define.smartArt.textCounterbalanceArrows": "平衡箭头", "Common.define.smartArt.textCycle": "循环", "Common.define.smartArt.textCycleMatrix": "循环矩阵", "Common.define.smartArt.textDescendingBlockList": "降序块列表", "Common.define.smartArt.textDescendingProcess": "降序流程", "Common.define.smartArt.textDetailedProcess": "详细流程", "Common.define.smartArt.textDivergingArrows": "分叉箭头", "Common.define.smartArt.textDivergingRadial": "分离射线", "Common.define.smartArt.textEquation": "方程", "Common.define.smartArt.textFramedTextPicture": "带框架的文本图片", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "齿轮", "Common.define.smartArt.textGridMatrix": "网格矩阵", "Common.define.smartArt.textGroupedList": "分组列表", "Common.define.smartArt.textHalfCircleOrganizationChart": "半圆组织结构图", "Common.define.smartArt.textHexagonCluster": "六边形群集", "Common.define.smartArt.textHexagonRadial": "放射状六边形", "Common.define.smartArt.textHierarchy": "层次结构", "Common.define.smartArt.textHierarchyList": "层次结构列表", "Common.define.smartArt.textHorizontalBulletList": "水平项目符号列表", "Common.define.smartArt.textHorizontalHierarchy": "水平层次结构", "Common.define.smartArt.textHorizontalLabeledHierarchy": "水平标记的层次结构", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "水平多层层次结构", "Common.define.smartArt.textHorizontalOrganizationChart": "水平组织结构图", "Common.define.smartArt.textHorizontalPictureList": "水平图片列表", "Common.define.smartArt.textIncreasingArrowProcess": "递增箭头流程", "Common.define.smartArt.textIncreasingCircleProcess": "递增循环流程", "Common.define.smartArt.textInterconnectedBlockProcess": "互联块流程", "Common.define.smartArt.textInterconnectedRings": "互联环", "Common.define.smartArt.textInvertedPyramid": "倒棱锥图", "Common.define.smartArt.textLabeledHierarchy": "标记的层次结构", "Common.define.smartArt.textLinearVenn": "线性维恩图", "Common.define.smartArt.textLinedList": "线型列表", "Common.define.smartArt.textList": "列表", "Common.define.smartArt.textMatrix": "矩阵", "Common.define.smartArt.textMultidirectionalCycle": "多向循环", "Common.define.smartArt.textNameAndTitleOrganizationChart": "姓名和职务组织结构图", "Common.define.smartArt.textNestedTarget": "嵌套目标图", "Common.define.smartArt.textNondirectionalCycle": "不定向循环", "Common.define.smartArt.textOpposingArrows": "反向箭头", "Common.define.smartArt.textOpposingIdeas": "对立观点", "Common.define.smartArt.textOrganizationChart": "组织结构图", "Common.define.smartArt.textOther": "其他", "Common.define.smartArt.textPhasedProcess": "分阶段流程", "Common.define.smartArt.textPicture": "图片", "Common.define.smartArt.textPictureAccentBlocks": "图片重点块", "Common.define.smartArt.textPictureAccentList": "图片重点列表", "Common.define.smartArt.textPictureAccentProcess": "图片重点流程", "Common.define.smartArt.textPictureCaptionList": "图片题注列表", "Common.define.smartArt.textPictureFrame": "图片框", "Common.define.smartArt.textPictureGrid": "图片网格", "Common.define.smartArt.textPictureLineup": "图片排列", "Common.define.smartArt.textPictureOrganizationChart": "图片组织结构图", "Common.define.smartArt.textPictureStrips": "图片条纹", "Common.define.smartArt.textPieProcess": "饼图流程", "Common.define.smartArt.textPlusAndMinus": "加号和减号", "Common.define.smartArt.textProcess": "流程", "Common.define.smartArt.textProcessArrows": "流程箭头", "Common.define.smartArt.textProcessList": "流程列表", "Common.define.smartArt.textPyramid": "棱锥型", "Common.define.smartArt.textPyramidList": "棱锥型列表", "Common.define.smartArt.textRadialCluster": "射线群集", "Common.define.smartArt.textRadialCycle": "射线循环", "Common.define.smartArt.textRadialList": "射线列表", "Common.define.smartArt.textRadialPictureList": "放射状图片列表", "Common.define.smartArt.textRadialVenn": "射线维恩图", "Common.define.smartArt.textRandomToResultProcess": "随机至结果流程", "Common.define.smartArt.textRelationship": "关系", "Common.define.smartArt.textRepeatingBendingProcess": "重复蛇形流程", "Common.define.smartArt.textReverseList": "反转列表", "Common.define.smartArt.textSegmentedCycle": "分段循环", "Common.define.smartArt.textSegmentedProcess": "分段流程", "Common.define.smartArt.textSegmentedPyramid": "分段棱锥图", "Common.define.smartArt.textSnapshotPictureList": "快照图片列表", "Common.define.smartArt.textSpiralPicture": "螺旋图", "Common.define.smartArt.textSquareAccentList": "方形重点列表", "Common.define.smartArt.textStackedList": "堆叠列表", "Common.define.smartArt.textStackedVenn": "堆叠维恩图", "Common.define.smartArt.textStaggeredProcess": "交错流程", "Common.define.smartArt.textStepDownProcess": "步骤下移流程", "Common.define.smartArt.textStepUpProcess": "升级流程", "Common.define.smartArt.textSubStepProcess": "子步骤流程", "Common.define.smartArt.textTabbedArc": "拱状", "Common.define.smartArt.textTableHierarchy": "表层次结构", "Common.define.smartArt.textTableList": "表格列表", "Common.define.smartArt.textTabList": "选项卡列表", "Common.define.smartArt.textTargetList": "目标图列表", "Common.define.smartArt.textTextCycle": "文本循环", "Common.define.smartArt.textThemePictureAccent": "主题图片重点", "Common.define.smartArt.textThemePictureAlternatingAccent": "主题图片交替重点", "Common.define.smartArt.textThemePictureGrid": "主题图片网格", "Common.define.smartArt.textTitledMatrix": "带标题的矩阵", "Common.define.smartArt.textTitledPictureAccentList": "标题图片重点列表", "Common.define.smartArt.textTitledPictureBlocks": "标题图片块", "Common.define.smartArt.textTitlePictureLineup": "标题图片排列", "Common.define.smartArt.textTrapezoidList": "梯形列表", "Common.define.smartArt.textUpwardArrow": "向上箭头", "Common.define.smartArt.textVaryingWidthList": "不同宽度列表", "Common.define.smartArt.textVerticalAccentList": "垂直重点列表", "Common.define.smartArt.textVerticalArrowList": "垂直箭头列表", "Common.define.smartArt.textVerticalBendingProcess": "垂直蛇形流程", "Common.define.smartArt.textVerticalBlockList": "垂直块列表", "Common.define.smartArt.textVerticalBoxList": "垂直框列表", "Common.define.smartArt.textVerticalBracketList": "垂直括弧列表", "Common.define.smartArt.textVerticalBulletList": "垂直项目符号列表", "Common.define.smartArt.textVerticalChevronList": "垂直 V 形列表", "Common.define.smartArt.textVerticalCircleList": "垂直圆形列表", "Common.define.smartArt.textVerticalCurvedList": "垂直曲形列表", "Common.define.smartArt.textVerticalEquation": "垂直公式", "Common.define.smartArt.textVerticalPictureAccentList": "垂直图片重点列表", "Common.define.smartArt.textVerticalPictureList": "垂直图片列表", "Common.define.smartArt.textVerticalProcess": "垂直流程", "Common.Translation.textMoreButton": "更多", "Common.Translation.warnFileLocked": "另一个应用程序正在编辑本文件。你可以继续编辑，并另存为副本。", "Common.Translation.warnFileLockedBtnEdit": "创建副本", "Common.Translation.warnFileLockedBtnView": "打开以查看", "Common.UI.ButtonColored.textAutoColor": "自动", "Common.UI.ButtonColored.textNewColor": "添加新的自定义颜色", "Common.UI.ComboBorderSize.txtNoBorders": "无边框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "无边框", "Common.UI.ComboDataView.emptyComboText": "没有风格", "Common.UI.ExtendedColorDialog.addButtonText": "添加", "Common.UI.ExtendedColorDialog.textCurrent": "当前", "Common.UI.ExtendedColorDialog.textHexErr": "输入的值不正确。<br>请输入000000和FFFFFF之间的值。", "Common.UI.ExtendedColorDialog.textNew": "新", "Common.UI.ExtendedColorDialog.textRGBErr": "输入的值不正确。<br>请输入介于0和255之间的数值。", "Common.UI.HSBColorPicker.textNoColor": "没有颜色", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "隐藏密码", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "显示密码", "Common.UI.SearchBar.textFind": "查找", "Common.UI.SearchBar.tipCloseSearch": "关闭搜索", "Common.UI.SearchBar.tipNextResult": "下一个", "Common.UI.SearchBar.tipOpenAdvancedSettings": "打开高级设置", "Common.UI.SearchBar.tipPreviousResult": "上一个", "Common.UI.SearchDialog.textHighlight": "高亮效果", "Common.UI.SearchDialog.textMatchCase": "区分大小写", "Common.UI.SearchDialog.textReplaceDef": "输入替换文字", "Common.UI.SearchDialog.textSearchStart": "在这里输入你的文字", "Common.UI.SearchDialog.textTitle": "查找和替换", "Common.UI.SearchDialog.textTitle2": "查找", "Common.UI.SearchDialog.textWholeWords": "只有整个字", "Common.UI.SearchDialog.txtBtnHideReplace": "隐藏替换", "Common.UI.SearchDialog.txtBtnReplace": "替换", "Common.UI.SearchDialog.txtBtnReplaceAll": "全部替换", "Common.UI.SynchronizeTip.textDontShow": "不要再显示此消息", "Common.UI.SynchronizeTip.textSynchronize": "该文档已被其他用户更改。<br>请点击保存更改并重新加载更新。", "Common.UI.ThemeColorPalette.textRecentColors": "最近的颜色", "Common.UI.ThemeColorPalette.textStandartColors": "标准颜色", "Common.UI.ThemeColorPalette.textThemeColors": "主题颜色", "Common.UI.Themes.txtThemeClassicLight": "经典浅色", "Common.UI.Themes.txtThemeContrastDark": "反差暗色", "Common.UI.Themes.txtThemeDark": "深色", "Common.UI.Themes.txtThemeLight": "浅色", "Common.UI.Themes.txtThemeSystem": "和系统一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "关闭", "Common.UI.Window.noButtonText": "否", "Common.UI.Window.okButtonText": "确定", "Common.UI.Window.textConfirmation": "确认", "Common.UI.Window.textDontShow": "不要再显示此消息", "Common.UI.Window.textError": "错误:", "Common.UI.Window.textInformation": "信息", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "厘米", "Common.Utils.Metric.txtPt": "像素", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "地址：", "Common.Views.About.txtLicensee": "被许可人", "Common.Views.About.txtLicensor": "许可", "Common.Views.About.txtMail": "电子邮件：", "Common.Views.About.txtPoweredBy": "技术支持", "Common.Views.About.txtTel": "电话：", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "新增", "Common.Views.AutoCorrectDialog.textApplyAsWork": "工作时应用", "Common.Views.AutoCorrectDialog.textAutoCorrect": "自动更正", "Common.Views.AutoCorrectDialog.textAutoFormat": "输入时自动调整格式", "Common.Views.AutoCorrectDialog.textBy": "依据", "Common.Views.AutoCorrectDialog.textDelete": "删除", "Common.Views.AutoCorrectDialog.textHyperlink": "互联网和网络路径含有超链接", "Common.Views.AutoCorrectDialog.textMathCorrect": "数学自动修正", "Common.Views.AutoCorrectDialog.textNewRowCol": "表格中包含新的行和列", "Common.Views.AutoCorrectDialog.textRecognized": "可识别的函数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表达式被识别为数学公式。它们不会被自动设为斜体。", "Common.Views.AutoCorrectDialog.textReplace": "替换", "Common.Views.AutoCorrectDialog.textReplaceText": "键入时替换", "Common.Views.AutoCorrectDialog.textReplaceType": "输入时自动替换文字", "Common.Views.AutoCorrectDialog.textReset": "重置", "Common.Views.AutoCorrectDialog.textResetAll": "重置为默认", "Common.Views.AutoCorrectDialog.textRestore": "恢复", "Common.Views.AutoCorrectDialog.textTitle": "自动修正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "识别的函数只能包含字母A到Z，大写或小写。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "由您新增的表达式将被移除，由您移除的将被恢复。是否继续？", "Common.Views.AutoCorrectDialog.warnReplace": "％1的自动更正条目已存在。您要更换吗？", "Common.Views.AutoCorrectDialog.warnReset": "即将移除对自动更正功能所做的自定义设置，并恢复默认值。是否继续？", "Common.Views.AutoCorrectDialog.warnRestore": "％1的自动更正条目将被重置为其原始值。你想继续吗？", "Common.Views.Chat.textSend": "发送", "Common.Views.Comments.mniAuthorAsc": "作者 A到Z", "Common.Views.Comments.mniAuthorDesc": "作者 Z到A", "Common.Views.Comments.mniDateAsc": "最旧", "Common.Views.Comments.mniDateDesc": "最新", "Common.Views.Comments.mniFilterGroups": "按组筛选", "Common.Views.Comments.mniPositionAsc": "从顶部", "Common.Views.Comments.mniPositionDesc": "从底部", "Common.Views.Comments.textAdd": "添加", "Common.Views.Comments.textAddComment": "添加批注", "Common.Views.Comments.textAddCommentToDoc": "添加批注到文档", "Common.Views.Comments.textAddReply": "添加回复", "Common.Views.Comments.textAll": "所有", "Common.Views.Comments.textAnonym": "游客", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "关闭", "Common.Views.Comments.textClosePanel": "关闭批注", "Common.Views.Comments.textComments": "批注", "Common.Views.Comments.textEdit": "确定", "Common.Views.Comments.textEnterCommentHint": "在此输入您的批注", "Common.Views.Comments.textHintAddComment": "添加批注", "Common.Views.Comments.textOpenAgain": "再次打开", "Common.Views.Comments.textReply": "回复", "Common.Views.Comments.textResolve": "解决", "Common.Views.Comments.textResolved": "已解决", "Common.Views.Comments.textSort": "评论排序", "Common.Views.Comments.textViewResolved": "您没有重开评论的权限", "Common.Views.Comments.txtEmpty": "工作表中没有任何评论。", "Common.Views.CopyWarningDialog.textDontShow": "不要再显示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用编辑器工具栏按钮和上下文菜单操作复制，剪切和粘贴操作将仅在此编辑器选项卡中执行。<br> <br>要在编辑器选项卡之外复制或粘贴到应用程序，请使用以下键盘组合：", "Common.Views.CopyWarningDialog.textTitle": "复制，剪切和粘贴操作", "Common.Views.CopyWarningDialog.textToCopy": "复制", "Common.Views.CopyWarningDialog.textToCut": "剪切", "Common.Views.CopyWarningDialog.textToPaste": "粘贴", "Common.Views.DocumentAccessDialog.textLoading": "载入中……", "Common.Views.DocumentAccessDialog.textTitle": "共享设置", "Common.Views.EditNameDialog.textLabel": "标签：", "Common.Views.EditNameDialog.textLabelError": "标签不能空", "Common.Views.Header.labelCoUsersDescr": "正在编辑文件的用户：", "Common.Views.Header.textAddFavorite": "标记为收藏项", "Common.Views.Header.textAdvSettings": "高级设置", "Common.Views.Header.textBack": "打开文件所在位置", "Common.Views.Header.textCompactView": "查看紧凑工具栏", "Common.Views.Header.textHideLines": "隐藏标尺", "Common.Views.Header.textHideStatusBar": "合并工作表和状态栏", "Common.Views.Header.textRemoveFavorite": "从收藏夹中删除", "Common.Views.Header.textSaveBegin": "正在保存...", "Common.Views.Header.textSaveChanged": "已修改", "Common.Views.Header.textSaveEnd": "所有更改已保存", "Common.Views.Header.textSaveExpander": "所有更改已保存", "Common.Views.Header.textShare": "共享", "Common.Views.Header.textZoom": "放大", "Common.Views.Header.tipAccessRights": "管理文档访问权限", "Common.Views.Header.tipDownload": "下载文件", "Common.Views.Header.tipGoEdit": "编辑当前文件", "Common.Views.Header.tipPrint": "打印文件", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipSave": "保存", "Common.Views.Header.tipSearch": "搜索", "Common.Views.Header.tipUndo": "复原", "Common.Views.Header.tipUndock": "打开单独的窗口", "Common.Views.Header.tipUsers": "查看用户", "Common.Views.Header.tipViewSettings": "视图设置", "Common.Views.Header.tipViewUsers": "查看用户和管理文档访问权限", "Common.Views.Header.txtAccessRights": "更改访问权限", "Common.Views.Header.txtRename": "重命名", "Common.Views.History.textCloseHistory": "关闭历史记录", "Common.Views.History.textHide": "折叠", "Common.Views.History.textHideAll": "隐藏详细更改", "Common.Views.History.textRestore": "还原", "Common.Views.History.textShow": "展开", "Common.Views.History.textShowAll": "显示详细的更改", "Common.Views.History.textVer": "版本", "Common.Views.ImageFromUrlDialog.textUrl": "粘贴图片网址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "这是必填栏", "Common.Views.ImageFromUrlDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "Common.Views.ListSettingsDialog.textBulleted": "已添加项目符号", "Common.Views.ListSettingsDialog.textFromFile": "从文件导入", "Common.Views.ListSettingsDialog.textFromStorage": "来自存储设备", "Common.Views.ListSettingsDialog.textFromUrl": "从URL", "Common.Views.ListSettingsDialog.textNumbering": "标号", "Common.Views.ListSettingsDialog.textSelect": "选择", "Common.Views.ListSettingsDialog.tipChange": "修改项目点", "Common.Views.ListSettingsDialog.txtBullet": "项目符号", "Common.Views.ListSettingsDialog.txtColor": "颜色", "Common.Views.ListSettingsDialog.txtImage": "图片", "Common.Views.ListSettingsDialog.txtImport": "导入", "Common.Views.ListSettingsDialog.txtNewBullet": "添加一个新的项目点", "Common.Views.ListSettingsDialog.txtNewImage": "新图片", "Common.Views.ListSettingsDialog.txtNone": "无", "Common.Views.ListSettingsDialog.txtOfText": "文本的%", "Common.Views.ListSettingsDialog.txtSize": "大小", "Common.Views.ListSettingsDialog.txtStart": "开始", "Common.Views.ListSettingsDialog.txtSymbol": "符号", "Common.Views.ListSettingsDialog.txtTitle": "列表设置", "Common.Views.ListSettingsDialog.txtType": "类型", "Common.Views.OpenDialog.closeButtonText": "关闭文件", "Common.Views.OpenDialog.textInvalidRange": "无效的单元格范围", "Common.Views.OpenDialog.textSelectData": "选择数据", "Common.Views.OpenDialog.txtAdvanced": "进阶", "Common.Views.OpenDialog.txtColon": "冒号", "Common.Views.OpenDialog.txtComma": "逗号", "Common.Views.OpenDialog.txtDelimiter": "字段分隔符", "Common.Views.OpenDialog.txtDestData": "选择此数据的放置位置", "Common.Views.OpenDialog.txtEmpty": "此字段是必需的", "Common.Views.OpenDialog.txtEncoding": "编码", "Common.Views.OpenDialog.txtIncorrectPwd": "密码错误", "Common.Views.OpenDialog.txtOpenFile": "输入密码来打开文件", "Common.Views.OpenDialog.txtOther": "其他", "Common.Views.OpenDialog.txtPassword": "密码", "Common.Views.OpenDialog.txtPreview": "预览", "Common.Views.OpenDialog.txtProtected": "在您输入密码和打开文件后，该文件的当前密码将被重置。", "Common.Views.OpenDialog.txtSemicolon": "分号", "Common.Views.OpenDialog.txtSpace": "空格", "Common.Views.OpenDialog.txtTab": "标签", "Common.Views.OpenDialog.txtTitle": "选择%1个选项", "Common.Views.OpenDialog.txtTitleProtected": "受保护的文件", "Common.Views.PasswordDialog.txtDescription": "设置密码以保护此文档", "Common.Views.PasswordDialog.txtIncorrectPwd": "确认密码不一致", "Common.Views.PasswordDialog.txtPassword": "密码", "Common.Views.PasswordDialog.txtRepeat": "重复输入密码", "Common.Views.PasswordDialog.txtTitle": "设置密码", "Common.Views.PasswordDialog.txtWarning": "警告: 如果丢失或忘记密码，则无法将其恢复。将其保存在安全位置。", "Common.Views.PluginDlg.textLoading": "载入中", "Common.Views.Plugins.groupCaption": "插件", "Common.Views.Plugins.strPlugins": "插件", "Common.Views.Plugins.textClosePanel": "关闭插件", "Common.Views.Plugins.textLoading": "载入中", "Common.Views.Plugins.textStart": "开始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Protection.hintAddPwd": "使用密码加密", "Common.Views.Protection.hintDelPwd": "删除密码", "Common.Views.Protection.hintPwd": "修改或删除密码", "Common.Views.Protection.hintSignature": "添加数字签名或签名行", "Common.Views.Protection.txtAddPwd": "添加密码", "Common.Views.Protection.txtChangePwd": "修改密码", "Common.Views.Protection.txtDeletePwd": "删除密码", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "添加数字签名", "Common.Views.Protection.txtSignature": "签名", "Common.Views.Protection.txtSignatureLine": "添加签名行", "Common.Views.RenameDialog.textName": "文件名", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "下一个变化", "Common.Views.ReviewChanges.hintPrev": "以前的变化", "Common.Views.ReviewChanges.strFast": "自动", "Common.Views.ReviewChanges.strFastDesc": "自动共同编辑模式，自动保存修改痕迹。", "Common.Views.ReviewChanges.strStrict": "手动", "Common.Views.ReviewChanges.strStrictDesc": "使用“保存”按钮同步你和其他人的修改。", "Common.Views.ReviewChanges.tipAcceptCurrent": "接受当前的变化", "Common.Views.ReviewChanges.tipCoAuthMode": "设置共同编辑模式", "Common.Views.ReviewChanges.tipCommentRem": "移除批注", "Common.Views.ReviewChanges.tipCommentRemCurrent": "移除当前批注", "Common.Views.ReviewChanges.tipCommentResolve": "解决评论", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "解决当前评论", "Common.Views.ReviewChanges.tipHistory": "显示历史版本", "Common.Views.ReviewChanges.tipRejectCurrent": "拒绝当前的变化", "Common.Views.ReviewChanges.tipReview": "跟踪变化", "Common.Views.ReviewChanges.tipReviewView": "选择您希望显示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "设置文档语言", "Common.Views.ReviewChanges.tipSetSpelling": "拼写检查", "Common.Views.ReviewChanges.tipSharing": "管理文档访问权限", "Common.Views.ReviewChanges.txtAccept": "接受", "Common.Views.ReviewChanges.txtAcceptAll": "接受所有更改", "Common.Views.ReviewChanges.txtAcceptChanges": "接受更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "接受当前的变化", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "关闭", "Common.Views.ReviewChanges.txtCoAuthMode": "共同编辑模式", "Common.Views.ReviewChanges.txtCommentRemAll": "移除所有批注", "Common.Views.ReviewChanges.txtCommentRemCurrent": "移除当前批注", "Common.Views.ReviewChanges.txtCommentRemMy": "移除我的批注", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "移除我的当前批注", "Common.Views.ReviewChanges.txtCommentRemove": "删除", "Common.Views.ReviewChanges.txtCommentResolve": "解决", "Common.Views.ReviewChanges.txtCommentResolveAll": "解决所有评论", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "解决当前评论", "Common.Views.ReviewChanges.txtCommentResolveMy": "解决我的评论", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "解决我当前的评论", "Common.Views.ReviewChanges.txtDocLang": "语言", "Common.Views.ReviewChanges.txtFinal": "已接受所有更改（预览）", "Common.Views.ReviewChanges.txtFinalCap": "最终版", "Common.Views.ReviewChanges.txtHistory": "版本历史", "Common.Views.ReviewChanges.txtMarkup": "所有更改（编辑）", "Common.Views.ReviewChanges.txtMarkupCap": "标记", "Common.Views.ReviewChanges.txtNext": "下一个变化", "Common.Views.ReviewChanges.txtOriginal": "已拒绝所有更改（预览）", "Common.Views.ReviewChanges.txtOriginalCap": "原始版", "Common.Views.ReviewChanges.txtPrev": "以前的变化", "Common.Views.ReviewChanges.txtReject": "拒绝", "Common.Views.ReviewChanges.txtRejectAll": "拒绝所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "拒绝更改", "Common.Views.ReviewChanges.txtRejectCurrent": "拒绝当前的变化", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼写检查", "Common.Views.ReviewChanges.txtTurnon": "跟踪变化", "Common.Views.ReviewChanges.txtView": "显示模式", "Common.Views.ReviewPopover.textAdd": "添加", "Common.Views.ReviewPopover.textAddReply": "添加回复", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "关闭", "Common.Views.ReviewPopover.textEdit": "确定", "Common.Views.ReviewPopover.textMention": "+提供访问文档和发送电子邮件的权限", "Common.Views.ReviewPopover.textMentionNotify": "+通知将通过电子邮件通知用户", "Common.Views.ReviewPopover.textOpenAgain": "再次打开", "Common.Views.ReviewPopover.textReply": "回复", "Common.Views.ReviewPopover.textResolve": "解决", "Common.Views.ReviewPopover.textViewResolved": "您没有重开评论的权限", "Common.Views.ReviewPopover.txtDeleteTip": "删除", "Common.Views.ReviewPopover.txtEditTip": "编辑", "Common.Views.SaveAsDlg.textLoading": "载入中", "Common.Views.SaveAsDlg.textTitle": "保存文件夹", "Common.Views.SearchPanel.textByColumns": "按列", "Common.Views.SearchPanel.textByRows": "按行", "Common.Views.SearchPanel.textCaseSensitive": "区分大小写", "Common.Views.SearchPanel.textCell": "单元格", "Common.Views.SearchPanel.textCloseSearch": "关闭搜索", "Common.Views.SearchPanel.textContentChanged": "文件已更改。", "Common.Views.SearchPanel.textFind": "查找", "Common.Views.SearchPanel.textFindAndReplace": "查找和替换", "Common.Views.SearchPanel.textFormula": "公式", "Common.Views.SearchPanel.textFormulas": "公式", "Common.Views.SearchPanel.textItemEntireCell": "整个单元格内容", "Common.Views.SearchPanel.textLookIn": "看里面", "Common.Views.SearchPanel.textMatchUsingRegExp": "正则匹配", "Common.Views.SearchPanel.textName": "名称", "Common.Views.SearchPanel.textNoMatches": "找不到匹配信息", "Common.Views.SearchPanel.textNoSearchResults": "找不到搜索结果", "Common.Views.SearchPanel.textReplace": "替换", "Common.Views.SearchPanel.textReplaceAll": "全部替换", "Common.Views.SearchPanel.textReplaceWith": "替换为", "Common.Views.SearchPanel.textSearch": "搜索", "Common.Views.SearchPanel.textSearchAgain": "{0}进行新的搜索{1}以获得准确结果。", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchOptions": "搜索选项", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "选择数据范围", "Common.Views.SearchPanel.textSheet": "表格", "Common.Views.SearchPanel.textSpecificRange": "指定的范围", "Common.Views.SearchPanel.textTooManyResults": "这里显示的结果太多了", "Common.Views.SearchPanel.textValue": "值", "Common.Views.SearchPanel.textValues": "值", "Common.Views.SearchPanel.textWholeWords": "整词匹配", "Common.Views.SearchPanel.textWithin": "里边", "Common.Views.SearchPanel.textWorkbook": "工作簿", "Common.Views.SearchPanel.tipNextResult": "下一个", "Common.Views.SearchPanel.tipPreviousResult": "上一个", "Common.Views.SelectFileDlg.textLoading": "载入中", "Common.Views.SelectFileDlg.textTitle": "选择数据源", "Common.Views.SignDialog.textBold": "加粗", "Common.Views.SignDialog.textCertificate": "证书", "Common.Views.SignDialog.textChange": "修改", "Common.Views.SignDialog.textInputName": "输入签名者姓名", "Common.Views.SignDialog.textItalic": "斜体", "Common.Views.SignDialog.textNameError": "签名者名称不能为空", "Common.Views.SignDialog.textPurpose": "签署本文档的目的", "Common.Views.SignDialog.textSelect": "请选择", "Common.Views.SignDialog.textSelectImage": "选择图像", "Common.Views.SignDialog.textSignature": "签名类似", "Common.Views.SignDialog.textTitle": "签署文件", "Common.Views.SignDialog.textUseImage": "或单击“选择图像”将图像用作签名", "Common.Views.SignDialog.textValid": "从%1到%2有效", "Common.Views.SignDialog.tipFontName": "字体名称", "Common.Views.SignDialog.tipFontSize": "字体大小", "Common.Views.SignSettingsDialog.textAllowComment": "允许签名者在签名对话框中添加批注", "Common.Views.SignSettingsDialog.textDefInstruction": "在签署本文件之前，请确保您所签署的内容是正确。", "Common.Views.SignSettingsDialog.textInfoEmail": "建议签署人电邮", "Common.Views.SignSettingsDialog.textInfoName": "建议签署人", "Common.Views.SignSettingsDialog.textInfoTitle": "建议签署人职务", "Common.Views.SignSettingsDialog.textInstructions": "给签名者的说明", "Common.Views.SignSettingsDialog.textShowDate": "在签名行中显示签名日期", "Common.Views.SignSettingsDialog.textTitle": "签名设置", "Common.Views.SignSettingsDialog.txtEmpty": "这是必填栏", "Common.Views.SymbolTableDialog.textCharacter": "字符", "Common.Views.SymbolTableDialog.textCode": "Unicode十六进制值", "Common.Views.SymbolTableDialog.textCopyright": "版权所有标识", "Common.Views.SymbolTableDialog.textDCQuote": "后双引号", "Common.Views.SymbolTableDialog.textDOQuote": "前双引号", "Common.Views.SymbolTableDialog.textEllipsis": "横向省略号", "Common.Views.SymbolTableDialog.textEmDash": "破折号", "Common.Views.SymbolTableDialog.textEmSpace": "全角空格", "Common.Views.SymbolTableDialog.textEnDash": "半破折号", "Common.Views.SymbolTableDialog.textEnSpace": "半角空格", "Common.Views.SymbolTableDialog.textFont": "字体 ", "Common.Views.SymbolTableDialog.textNBHyphen": "不换行连词符", "Common.Views.SymbolTableDialog.textNBSpace": "不换行空格", "Common.Views.SymbolTableDialog.textPilcrow": "段落标识", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4全角空格", "Common.Views.SymbolTableDialog.textRange": "子集", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符号", "Common.Views.SymbolTableDialog.textRegistered": "注册商标标识", "Common.Views.SymbolTableDialog.textSCQuote": "后单引号", "Common.Views.SymbolTableDialog.textSection": "章节标识", "Common.Views.SymbolTableDialog.textShortcut": "快捷键", "Common.Views.SymbolTableDialog.textSHyphen": "软连词符", "Common.Views.SymbolTableDialog.textSOQuote": "前单引号", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符号", "Common.Views.SymbolTableDialog.textTitle": "符号", "Common.Views.SymbolTableDialog.textTradeMark": "商标标识", "Common.Views.UserNameDialog.textDontShow": "不再询问我", "Common.Views.UserNameDialog.textLabel": "标签：", "Common.Views.UserNameDialog.textLabelError": "标签不能空", "SSE.Controllers.DataTab.textColumns": "列", "SSE.Controllers.DataTab.textEmptyUrl": "你必须指定URL。", "SSE.Controllers.DataTab.textRows": "行", "SSE.Controllers.DataTab.textWizard": "文本分列向导", "SSE.Controllers.DataTab.txtDataValidation": "数据校验", "SSE.Controllers.DataTab.txtExpand": "扩大", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "所选内容旁边的数据将不会被删除。您要扩展选择范围以包括相邻数据还是仅继续使用当前选定的单元格？", "SSE.Controllers.DataTab.txtExtendDataValidation": "选定区域中的某些单元格尚未设置“数据有效性”。<br> 是否将“数据有效性”设置扩展到这些单元格?", "SSE.Controllers.DataTab.txtImportWizard": "文本导入向导", "SSE.Controllers.DataTab.txtRemDuplicates": "移除重复的元素", "SSE.Controllers.DataTab.txtRemoveDataValidation": "选定区域含有多种类型的数据有效性。<br> 是否清除当前设置并继续?", "SSE.Controllers.DataTab.txtRemSelected": "移除选定的", "SSE.Controllers.DataTab.txtUrlTitle": "粘贴URL数据", "SSE.Controllers.DocumentHolder.alignmentText": "对齐", "SSE.Controllers.DocumentHolder.centerText": "中心", "SSE.Controllers.DocumentHolder.deleteColumnText": "删除列", "SSE.Controllers.DocumentHolder.deleteRowText": "删除行", "SSE.Controllers.DocumentHolder.deleteText": "删除", "SSE.Controllers.DocumentHolder.errorInvalidLink": "链接引用不存在。请修改或删除链接。", "SSE.Controllers.DocumentHolder.guestText": "游客", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "列的左右", "SSE.Controllers.DocumentHolder.insertColumnRightText": "右列", "SSE.Controllers.DocumentHolder.insertRowAboveText": "上面的行", "SSE.Controllers.DocumentHolder.insertRowBelowText": "下面的行", "SSE.Controllers.DocumentHolder.insertText": "插入", "SSE.Controllers.DocumentHolder.leftText": "左", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "警告", "SSE.Controllers.DocumentHolder.rightText": "右", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "自动更正选项", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "列宽{0}符号（{1}像素）", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "行高{0}分（{1}个像素）", "SSE.Controllers.DocumentHolder.textCtrlClick": "单击链接打开，或单击并按住鼠标选择单元格。", "SSE.Controllers.DocumentHolder.textInsertLeft": "向左插入", "SSE.Controllers.DocumentHolder.textInsertTop": "插入顶部", "SSE.Controllers.DocumentHolder.textPasteSpecial": "特殊粘贴", "SSE.Controllers.DocumentHolder.textStopExpand": "停止自动扩展表", "SSE.Controllers.DocumentHolder.textSym": "符号", "SSE.Controllers.DocumentHolder.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Controllers.DocumentHolder.txtAboveAve": "平均水平以上", "SSE.Controllers.DocumentHolder.txtAddBottom": "添加下边框", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "添加分数栏", "SSE.Controllers.DocumentHolder.txtAddHor": "在相同样式的段落之间添加间隔", "SSE.Controllers.DocumentHolder.txtAddLB": "添加左下线", "SSE.Controllers.DocumentHolder.txtAddLeft": "添加左边框", "SSE.Controllers.DocumentHolder.txtAddLT": "添加左上线", "SSE.Controllers.DocumentHolder.txtAddRight": "添加右边框", "SSE.Controllers.DocumentHolder.txtAddTop": "添加上边框", "SSE.Controllers.DocumentHolder.txtAddVer": "添加垂直线", "SSE.Controllers.DocumentHolder.txtAlignToChar": "字符对齐", "SSE.Controllers.DocumentHolder.txtAll": "（全部）", "SSE.Controllers.DocumentHolder.txtAllTableHint": "返回表格或指定表格列的全部内容，包括列标题、数据和总行数", "SSE.Controllers.DocumentHolder.txtAnd": "和", "SSE.Controllers.DocumentHolder.txtBegins": "开头为", "SSE.Controllers.DocumentHolder.txtBelowAve": "在平均值以下", "SSE.Controllers.DocumentHolder.txtBlanks": "(空白)", "SSE.Controllers.DocumentHolder.txtBorderProps": "边框属性", "SSE.Controllers.DocumentHolder.txtBottom": "底部", "SSE.Controllers.DocumentHolder.txtColumn": "列", "SSE.Controllers.DocumentHolder.txtColumnAlign": "列对齐", "SSE.Controllers.DocumentHolder.txtContains": "包含", "SSE.Controllers.DocumentHolder.txtCopySuccess": "链接被复制到剪贴板上", "SSE.Controllers.DocumentHolder.txtDataTableHint": "返回表格或指定表格列的数据单元格", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "减少大小参数", "SSE.Controllers.DocumentHolder.txtDeleteArg": "删除参数", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "删除手动的断点", "SSE.Controllers.DocumentHolder.txtDeleteChars": "删除包围字符", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "删除封闭字符和分隔符", "SSE.Controllers.DocumentHolder.txtDeleteEq": "删除方程", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "删除字符", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "彻底删除", "SSE.Controllers.DocumentHolder.txtEnds": "结束于", "SSE.Controllers.DocumentHolder.txtEquals": "等于", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "等于单元格颜色", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "等于字体颜色", "SSE.Controllers.DocumentHolder.txtExpand": "展开和排序", "SSE.Controllers.DocumentHolder.txtExpandSort": "选择旁边的数据将不会被排序。要扩展选择以包括相邻数据还是继续排序当前选定的单元格？", "SSE.Controllers.DocumentHolder.txtFilterBottom": "底部", "SSE.Controllers.DocumentHolder.txtFilterTop": "顶部", "SSE.Controllers.DocumentHolder.txtFractionLinear": "改为线性分数", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "改为倾斜分数", "SSE.Controllers.DocumentHolder.txtFractionStacked": "改为堆积分数", "SSE.Controllers.DocumentHolder.txtGreater": "大于", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "大于或等于", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "字符在文字上", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "文字下的Char", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "返回表格或指定表格列的列头", "SSE.Controllers.DocumentHolder.txtHeight": "高低", "SSE.Controllers.DocumentHolder.txtHideBottom": "隐藏下边框", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "隐藏下限", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "隐藏右括号", "SSE.Controllers.DocumentHolder.txtHideDegree": "隐藏学位", "SSE.Controllers.DocumentHolder.txtHideHor": "隐藏水平线", "SSE.Controllers.DocumentHolder.txtHideLB": "隐藏左侧底线", "SSE.Controllers.DocumentHolder.txtHideLeft": "隐藏左边框", "SSE.Controllers.DocumentHolder.txtHideLT": "隐藏左顶线", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "隐藏开放支架", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "隐藏占位符", "SSE.Controllers.DocumentHolder.txtHideRight": "隐藏右边框", "SSE.Controllers.DocumentHolder.txtHideTop": "隐藏上边框", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "隐藏上限", "SSE.Controllers.DocumentHolder.txtHideVer": "隐藏垂直线", "SSE.Controllers.DocumentHolder.txtImportWizard": "文本导入向导", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "增加参数大小", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "插入参数", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "之前插入参数", "SSE.Controllers.DocumentHolder.txtInsertBreak": "插入手动中断", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "插入方程后", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "之前插入方程式", "SSE.Controllers.DocumentHolder.txtItems": "项", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "仅保留文字", "SSE.Controllers.DocumentHolder.txtLess": "小于", "SSE.Controllers.DocumentHolder.txtLessEquals": "小于或等于", "SSE.Controllers.DocumentHolder.txtLimitChange": "更改限制位置", "SSE.Controllers.DocumentHolder.txtLimitOver": "限制文字", "SSE.Controllers.DocumentHolder.txtLimitUnder": "在文本限制", "SSE.Controllers.DocumentHolder.txtLockSort": "在选择区附近找到数据，但是你没有足够的权限修改这些单元格。<br>你希望继续操作当前选择区吗？", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "匹配括号到参数高度", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "矩阵对齐", "SSE.Controllers.DocumentHolder.txtNoChoices": "填充单元格没有选择。<br>只能选择列中的文本值进行替换。", "SSE.Controllers.DocumentHolder.txtNotBegins": "不起始于", "SSE.Controllers.DocumentHolder.txtNotContains": "不含", "SSE.Controllers.DocumentHolder.txtNotEnds": "不结束于", "SSE.Controllers.DocumentHolder.txtNotEquals": "不等于", "SSE.Controllers.DocumentHolder.txtOr": "或", "SSE.Controllers.DocumentHolder.txtOverbar": "文本上一条", "SSE.Controllers.DocumentHolder.txtPaste": "粘贴", "SSE.Controllers.DocumentHolder.txtPasteBorders": "公式无边界", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "公式+列宽", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "目的地格式", "SSE.Controllers.DocumentHolder.txtPasteFormat": "仅粘贴格式", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "公式+数字格式", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "仅粘贴公式", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "公式+所有格式", "SSE.Controllers.DocumentHolder.txtPasteLink": "粘贴链接", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "链接图片", "SSE.Controllers.DocumentHolder.txtPasteMerge": "合并条件格式", "SSE.Controllers.DocumentHolder.txtPastePicture": "图片", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "源格式", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "颠倒", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "值+所有格式", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "值+数字格式", "SSE.Controllers.DocumentHolder.txtPasteValues": "仅粘贴值", "SSE.Controllers.DocumentHolder.txtPercent": "百分比", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "恢复表自动扩展", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "删除分数线", "SSE.Controllers.DocumentHolder.txtRemLimit": "删除限制", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "删除重音字符", "SSE.Controllers.DocumentHolder.txtRemoveBar": "删除栏", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "您想删除此签名吗? <br>这一操作无法被撤销。", "SSE.Controllers.DocumentHolder.txtRemScripts": "删除上下标", "SSE.Controllers.DocumentHolder.txtRemSubscript": "删除标", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "除去上标", "SSE.Controllers.DocumentHolder.txtRowHeight": "行高", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "文字后的上下标", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "文字前的脚本", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "显示下限", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "显示关闭括号", "SSE.Controllers.DocumentHolder.txtShowDegree": "显示学位", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "显示开放支架", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "显示占位符", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "显示上限", "SSE.Controllers.DocumentHolder.txtSorting": "排序", "SSE.Controllers.DocumentHolder.txtSortSelected": "排序选择", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "拉伸支架", "SSE.Controllers.DocumentHolder.txtThisRowHint": "仅选择指定列的这一行", "SSE.Controllers.DocumentHolder.txtTop": "顶部", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "返回表格或指定表格列的总行数", "SSE.Controllers.DocumentHolder.txtUnderbar": "在文本栏", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "撤消表自动扩展", "SSE.Controllers.DocumentHolder.txtUseTextImport": "使用文本导入向导", "SSE.Controllers.DocumentHolder.txtWarnUrl": "点击该链接可能会损害你的设备或数据。<br>你确定要继续吗？", "SSE.Controllers.DocumentHolder.txtWidth": "宽度", "SSE.Controllers.FormulaDialog.sCategoryAll": "全部", "SSE.Controllers.FormulaDialog.sCategoryCube": "立方体", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "数据库", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "日期和时间", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "工程", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "金融", "SSE.Controllers.FormulaDialog.sCategoryInformation": "信息", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10上次使用", "SSE.Controllers.FormulaDialog.sCategoryLogical": "合乎逻辑", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "查找和参考", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "数学和三角学", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "统计", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "文字和数据", "SSE.Controllers.LeftMenu.newDocumentTitle": "未命名的电子表格", "SSE.Controllers.LeftMenu.textByColumns": "通过列", "SSE.Controllers.LeftMenu.textByRows": "按行", "SSE.Controllers.LeftMenu.textFormulas": "公式", "SSE.Controllers.LeftMenu.textItemEntireCell": "整个单元格内容", "SSE.Controllers.LeftMenu.textLoadHistory": "载入版本历史记录中...", "SSE.Controllers.LeftMenu.textLookin": "在看", "SSE.Controllers.LeftMenu.textNoTextFound": "您搜索的数据无法找到。请调整您的搜索选项。", "SSE.Controllers.LeftMenu.textReplaceSkipped": "已经更换了。{0}次跳过。", "SSE.Controllers.LeftMenu.textReplaceSuccess": "他的搜索已经完成。发生次数：{0}", "SSE.Controllers.LeftMenu.textSearch": "搜索", "SSE.Controllers.LeftMenu.textSheet": "表格", "SSE.Controllers.LeftMenu.textValues": "值", "SSE.Controllers.LeftMenu.textWarning": "警告", "SSE.Controllers.LeftMenu.textWithin": "里边", "SSE.Controllers.LeftMenu.textWorkbook": "工作簿", "SSE.Controllers.LeftMenu.txtUntitled": "无标题", "SSE.Controllers.LeftMenu.warnDownloadAs": "如果您继续以此格式保存，除文本之外的所有功能将丢失。<br>您确定要继续吗？", "SSE.Controllers.Main.confirmAddCellWatches": "此操作将会添加 {0} 个单元格监视。<br>是否继续?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "此操作将只会添加 {0} 个单元格监视以节省内存。<br>是否继续?", "SSE.Controllers.Main.confirmMaxChangesSize": "行动的大小超过了对您服务器设置的限制。<br>按 \"撤消\"取消您的最后一次行动，或按\"继续\"在本地保留该行动（您需要下载文件或复制其内容以确保没有任何损失）。", "SSE.Controllers.Main.confirmMoveCellRange": "目的地小区范围可以包含数据。继续操作？", "SSE.Controllers.Main.confirmPutMergeRange": "源数据包含合并的单元格。<br>它们在粘贴到表格之前已经被取消了。", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "表头里的公式将会被删除，并转换成普通文本。<br>要继续吗？", "SSE.Controllers.Main.convertationTimeoutText": "转换超时", "SSE.Controllers.Main.criticalErrorExtText": "按“确定”返回该文件列表。", "SSE.Controllers.Main.criticalErrorTitle": "错误:", "SSE.Controllers.Main.downloadErrorText": "下载失败", "SSE.Controllers.Main.downloadTextText": "正在下载试算表...", "SSE.Controllers.Main.downloadTitleText": "下载电子表格", "SSE.Controllers.Main.errNoDuplicates": "未找重复值。", "SSE.Controllers.Main.errorAccessDeny": "您正在尝试执行您没有权限的操作。<br>请联系您的文档服务器管理员。", "SSE.Controllers.Main.errorArgsRange": "一个错误的输入公式。< br >使用不正确的参数范围。", "SSE.Controllers.Main.errorAutoFilterChange": "不允许操作，因为它正在尝试在工作表上的表格中移动单元格。", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "无法对所选单元格进行操作，因为您无法移动表格的一部分。<br>选择其他数据范围，以便整个表格被移动并重试。", "SSE.Controllers.Main.errorAutoFilterDataRange": "所选单元格区域无法进行操作。<br>选择与现有单元格不同的统一数据范围，然后重试。", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "无法执行操作，因为该区域包含已过滤的单元格。<br>请取消隐藏已过滤的元素，然后重试。", "SSE.Controllers.Main.errorBadImageUrl": "图片地址不正确", "SSE.Controllers.Main.errorCannotUngroup": "无法解组。若要开始轮廓，请选择详细信息行或列并对其进行分组。", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "受保护的工作表不允许进行该操作。要进行该操作，请先取消工作表的保护。<br>您可能需要输入密码。", "SSE.Controllers.Main.errorChangeArray": "您无法更改部分阵列。", "SSE.Controllers.Main.errorChangeFilteredRange": "这将更改工作表原有的筛选范围。<br>要完成此操作，请移除“自动筛选”。", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "尝试更改的单元格或图表位于受保护的工作表上。 <br>如要更改请把工作表解锁。会有可能要修您输入簿密码。", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "服务器连接丢失。该文档现在无法编辑", "SSE.Controllers.Main.errorConnectToServer": "这份文件无法保存。请检查连接设置或联系您的管理员。<br>当你点击“OK”按钮,系统将提示您下载文档。", "SSE.Controllers.Main.errorCopyMultiselectArea": "该命令不能与多个选择一起使用。<br>选择一个范围，然后重试。", "SSE.Controllers.Main.errorCountArg": "一个错误的输入公式。< br >正确使用数量的参数。", "SSE.Controllers.Main.errorCountArgExceed": "一个错误的输入公式。< br >超过数量的参数。", "SSE.Controllers.Main.errorCreateDefName": "无法编辑现有的命名范围，因此无法在其中编辑新的范围。", "SSE.Controllers.Main.errorDatabaseConnection": "外部错误。<br>数据库连接错误。如果错误仍然存​​在，请联系支持人员。", "SSE.Controllers.Main.errorDataEncrypted": "加密更改已收到，无法对其解密。", "SSE.Controllers.Main.errorDataRange": "数据范围不正确", "SSE.Controllers.Main.errorDataValidate": "您输入的值无效。<br>用户具有可输入此单元格的限制值。", "SSE.Controllers.Main.errorDefaultMessage": "错误代码：%1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "您正试图删除包含有锁定单元格的列。工作表受保护时无法删除锁定单元格。 <br> 若要删除锁定单元格，请先把工作表解锁。可能会提示您输入密码。", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "您正试图删除包含有锁定单元格的行。工作表受保护时无法删除锁定单元格。 <br> 若要删除锁定单元格，请先把工作表解锁。可能会提示您输入密码。", "SSE.Controllers.Main.errorDirectUrl": "请验证指向文档的链接。<br>此链接必须是指向要下载的文件的直接链接。", "SSE.Controllers.Main.errorEditingDownloadas": "在处理文档期间发生错误。<br>使用“下载为”选项将文件备份复制到您的计算机硬盘中。", "SSE.Controllers.Main.errorEditingSaveas": "在处理文档期间发生错误。<br>使用“另存为…”选项将文件备份复制到计算机硬盘中。", "SSE.Controllers.Main.errorEditView": "由于其中一些正在被编辑，因此目前无法编辑现有工作表视图，也无法创建新工作表视图。", "SSE.Controllers.Main.errorEmailClient": "未找到电子邮件客户端。", "SSE.Controllers.Main.errorFilePassProtect": "该文档受密码保护，无法被打开。", "SSE.Controllers.Main.errorFileRequest": "外部错误。<br>文件请求错误。如果错误仍然存​​在，请联系支持人员。", "SSE.Controllers.Main.errorFileSizeExceed": "文件大小超出了为服务器设置的限制.<br>有关详细信息，请与文档服务器管理员联系。", "SSE.Controllers.Main.errorFileVKey": "外部错误。<br>安全密钥错误。如果错误仍然存​​在，请联系支持人员。", "SSE.Controllers.Main.errorFillRange": "无法填充所选范围的单元格。<br>所有合并的单元格的大小必须相同。", "SSE.Controllers.Main.errorForceSave": "保存文件时发生错误请使用“下载为”选项将文件保存到计算机硬盘中或稍后重试。", "SSE.Controllers.Main.errorFormulaName": "一个错误的输入公式。< br >正确使用公式名称。", "SSE.Controllers.Main.errorFormulaParsing": "解析公式时出现内部错误。", "SSE.Controllers.Main.errorFrmlMaxLength": "公式不得超过8192个字符。<br>请编辑后重试。", "SSE.Controllers.Main.errorFrmlMaxReference": "无法输入此公式，因为它包含太多值、 <br> 单元格引用和/或名称。", "SSE.Controllers.Main.errorFrmlMaxTextLength": "公式中的文本值限制为255个字符。<br>使用连接函数或连接运算符（&）。", "SSE.Controllers.Main.errorFrmlWrongReferences": "该功能是指不存在的工作表。<br>请检查数据，然后重试。", "SSE.Controllers.Main.errorFTChangeTableRangeError": "所选单元格范围无法完成操作。<br>选择一个范围，使第一个表行位于同一行<br>上，并将生成的表与当前的列重叠。", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "所选单元格范围无法完成操作。<br>选择不包括其他表格的范围。", "SSE.Controllers.Main.errorInvalidRef": "输入选择的正确名称或有效参考。", "SSE.Controllers.Main.errorKeyEncrypt": "未知密钥描述", "SSE.Controllers.Main.errorKeyExpire": "密钥过期", "SSE.Controllers.Main.errorLabledColumnsPivot": "创建数据透视表时，必须使用组合为带有标志列列表的数据。", "SSE.Controllers.Main.errorLoadingFont": "字体未加载。<br>请联系文档服务器管理员。", "SSE.Controllers.Main.errorLocationOrDataRangeError": "引用的位置或数据范围是非法的。", "SSE.Controllers.Main.errorLockedAll": "由于工作表被其他用户锁定，因此无法进行操作。", "SSE.Controllers.Main.errorLockedCellPivot": "您无法更改透视表中的数据。", "SSE.Controllers.Main.errorLockedWorksheetRename": "此时由于其他用户重命名该表单，因此无法重命名该表", "SSE.Controllers.Main.errorMaxPoints": "每个图表序列的最大点值为4096。", "SSE.Controllers.Main.errorMoveRange": "不能改变合并单元的一部分", "SSE.Controllers.Main.errorMoveSlicerError": "无法将表切片器从一个工作簿复制到另一工作簿。 <br> 通过选择整个表和切片器，再试一遍。", "SSE.Controllers.Main.errorMultiCellFormula": "表格中不允许使用多单元格数组公式。", "SSE.Controllers.Main.errorNoDataToParse": "未选定要分析的数据。", "SSE.Controllers.Main.errorOpenWarning": "其中一个文件公式超过了8192个字符的限制。 <br> 该公式已被删除。", "SSE.Controllers.Main.errorOperandExpected": "输入的函数语法不正确。请检查你是否缺少一个括号 - '（'或'）'。", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "你提供的密码不正确。<br>确保大小写切换键处于关闭状态，以输入正确的大小写。", "SSE.Controllers.Main.errorPasteMaxRange": "复制和粘贴区域不匹配。<br>请选择相同尺寸的区域，或单击一行中的第一个单元格以粘贴复制的单元格。", "SSE.Controllers.Main.errorPasteMultiSelect": "无法在多个范围选择上完成此操作。<br> 请选择单个范围，然后重试。", "SSE.Controllers.Main.errorPasteSlicerError": "表切片器无法从一个工作簿复制到另一工作簿。", "SSE.Controllers.Main.errorPivotGroup": "选定区域不能分组。", "SSE.Controllers.Main.errorPivotOverlap": "透视表报告不能与表格重叠", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "数据透视表没有与下层数据共同保存。<br> 请使用“更新”按钮更新数据透视表。", "SSE.Controllers.Main.errorPrintMaxPagesCount": "不幸的是，不能在当前的程序版本中一次打印超过1500页。<br>这个限制将在即将发布的版本中被删除。", "SSE.Controllers.Main.errorProcessSaveResult": "保存失败", "SSE.Controllers.Main.errorServerVersion": "该编辑版本已经更新。该页面将被重新加载以应用更改。", "SSE.Controllers.Main.errorSessionAbsolute": "文档编辑会话已过期。请重新加载页面。", "SSE.Controllers.Main.errorSessionIdle": "该文件尚未编辑相当长的时间。请重新加载页面。", "SSE.Controllers.Main.errorSessionToken": "与服务器的连接已中断。请重新加载页面。", "SSE.Controllers.Main.errorSetPassword": "无法设置密码", "SSE.Controllers.Main.errorSingleColumnOrRowError": "由于单元格都不在同一列或行中，所以超链接非活动。 <br> 选择同一个列或行中的单元格。", "SSE.Controllers.Main.errorStockChart": "行顺序不正确。建立股票图表将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Controllers.Main.errorToken": "文档安全令牌未正确形成。<br>请与您的文件服务器管理员联系。", "SSE.Controllers.Main.errorTokenExpire": "文档安全令牌已过期。<br>请与您的文档服务器管理员联系。", "SSE.Controllers.Main.errorUnexpectedGuid": "外部错误。<br>意外GUID。如果错误仍然存​​在，请联系支持人员。", "SSE.Controllers.Main.errorUpdateVersion": "该文件版本已经改变了。该页面将被重新加载。", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "网络连接已恢复，文件版本已变更。<br>在继续工作之前，需要下载文件或复制其内容以避免丢失数据，然后重新加载此页。", "SSE.Controllers.Main.errorUserDrop": "该文件现在无法访问。", "SSE.Controllers.Main.errorUsersExceed": "超过了定价计划允许的用户数", "SSE.Controllers.Main.errorViewerDisconnect": "连接丢失了。您仍然可以查看文档，<br>但在连接恢复并页面被重新加载之前无法下载或打印。", "SSE.Controllers.Main.errorWrongBracketsCount": "一个错误的输入公式。< br >用括号打错了。", "SSE.Controllers.Main.errorWrongOperator": "公式输入发生错误。操作不当，请改正！", "SSE.Controllers.Main.errorWrongPassword": "你提供的密码不正确。", "SSE.Controllers.Main.errRemDuplicates": "发现重复值: {0}, 已经删除。 只留下唯一的值: {1}.", "SSE.Controllers.Main.leavePageText": "您在本电子表格中有未保存的更改。点击“留在这个页面”，然后点击“保存”保存。点击“离开此页面”，放弃所有未保存的更改。", "SSE.Controllers.Main.leavePageTextOnClose": "将丢失表格里所有的未保存更改。<br> 单击“取消”然后“保存”以保存好更改。单击“确定”以放弃所有未保存的更改。", "SSE.Controllers.Main.loadFontsTextText": "数据加载中…", "SSE.Controllers.Main.loadFontsTitleText": "数据加载中", "SSE.Controllers.Main.loadFontTextText": "数据加载中…", "SSE.Controllers.Main.loadFontTitleText": "数据加载中", "SSE.Controllers.Main.loadImagesTextText": "图片加载中…", "SSE.Controllers.Main.loadImagesTitleText": "图片加载中", "SSE.Controllers.Main.loadImageTextText": "图片加载中…", "SSE.Controllers.Main.loadImageTitleText": "图片加载中", "SSE.Controllers.Main.loadingDocumentTitleText": "加载电子表格", "SSE.Controllers.Main.notcriticalErrorTitle": "警告", "SSE.Controllers.Main.openErrorText": "打开文件时出现错误", "SSE.Controllers.Main.openTextText": "打开电子表格...", "SSE.Controllers.Main.openTitleText": "打开电子表格", "SSE.Controllers.Main.pastInMergeAreaError": "不能改变合并单元的一部分", "SSE.Controllers.Main.printTextText": "打印电子表格...", "SSE.Controllers.Main.printTitleText": "打印电子表格", "SSE.Controllers.Main.reloadButtonText": "重新加载页面", "SSE.Controllers.Main.requestEditFailedMessageText": "有人正在编辑此文档。请稍后再试。", "SSE.Controllers.Main.requestEditFailedTitleText": "访问被拒绝", "SSE.Controllers.Main.saveErrorText": "保存文件时发生错误", "SSE.Controllers.Main.saveErrorTextDesktop": "无法保存或创建此文件。<br>可能的原因是：<br> 1.此文件是只读的。 <br> 2.此文件正在由其他用户编辑。 <br> 3.磁盘已满或损坏。", "SSE.Controllers.Main.saveTextText": "正在保存电子表格...", "SSE.Controllers.Main.saveTitleText": "保存电子表格", "SSE.Controllers.Main.scriptLoadError": "连接速度过慢，部分组件无法被加载。请重新加载页面。", "SSE.Controllers.Main.textAnonymous": "匿名", "SSE.Controllers.Main.textApplyAll": "应用于所有公式", "SSE.Controllers.Main.textBuyNow": "访问网站", "SSE.Controllers.Main.textChangesSaved": "所有更改保存好了", "SSE.Controllers.Main.textClose": "关闭", "SSE.Controllers.Main.textCloseTip": "点击关闭提示", "SSE.Controllers.Main.textConfirm": "确认", "SSE.Controllers.Main.textContactUs": "联系销售", "SSE.Controllers.Main.textContinue": "发送", "SSE.Controllers.Main.textConvertEquation": "该公式是使用不再受支持的方程式编辑器的旧版本创建的。要对其进行编辑，请将公式转换为Office Math ML格式。 <br> 立即转换？", "SSE.Controllers.Main.textCustomLoader": "请注意，根据许可条款您无权更改加载程序。<br>请联系我们的销售部门获取报价。", "SSE.Controllers.Main.textDisconnect": "网络连接已断开。", "SSE.Controllers.Main.textFillOtherRows": "填充其他行", "SSE.Controllers.Main.textFormulaFilledAllRows": "公式填充的 {0} 个行有数据。填充其他空行可能需要几分钟的时间。", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "公式填充了前 {0} 个行。填充其他空行可能需要几分钟的时间。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "由于节省内存的原因，公式只填充了前 {0} 个有数据的行。该表中还有 {1} 个有数据的行。您可以手动填充。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "由于节省内存的原因，公式只填充了前 {0} 个行。该表中的其他行没有数据。", "SSE.Controllers.Main.textGuest": "来宾", "SSE.Controllers.Main.textHasMacros": "这个文件带有自动宏。<br> 是否要运行宏？", "SSE.Controllers.Main.textLearnMore": "了解更多", "SSE.Controllers.Main.textLoadingDocument": "正在加载电子表格", "SSE.Controllers.Main.textLongName": "请键入少于128个字符的名称。", "SSE.Controllers.Main.textNeedSynchronize": "您有更新", "SSE.Controllers.Main.textNo": "否", "SSE.Controllers.Main.textNoLicenseTitle": "已达到许可证限制。", "SSE.Controllers.Main.textPaidFeature": "付费功能", "SSE.Controllers.Main.textPleaseWait": "该操作可能需要比预期的更多的时间。请稍候...", "SSE.Controllers.Main.textReconnect": "连接已恢复", "SSE.Controllers.Main.textRemember": "针对所有的文件记住我的选择", "SSE.Controllers.Main.textRememberMacros": "为所有的宏记住我的选择", "SSE.Controllers.Main.textRenameError": "用户名不能为空。", "SSE.Controllers.Main.textRenameLabel": "输入名称，可用于协作。", "SSE.Controllers.Main.textRequestMacros": "宏发起一个请求至URL。你是否允许请求到％1？", "SSE.Controllers.Main.textShape": "形状", "SSE.Controllers.Main.textStrict": "手动模式", "SSE.Controllers.Main.textText": "文本", "SSE.Controllers.Main.textTryUndoRedo": "对于自动的协同编辑模式，取消/重做功能是禁用的。< br >单击“手动模式”按钮切换到手动协同编辑模式，这样，编辑该文件时只有您保存修改之后，其他用户才能访问这些修改。您可以使用编辑器高级设置易于切换编辑模式。", "SSE.Controllers.Main.textTryUndoRedoWarn": "自动共同编辑模式下，撤销/重做功能被禁用。", "SSE.Controllers.Main.textUndo": "撤消", "SSE.Controllers.Main.textYes": "是", "SSE.Controllers.Main.titleLicenseExp": "许可证过期", "SSE.Controllers.Main.titleServerVersion": "编辑器已更新", "SSE.Controllers.Main.txtAccent": "强调", "SSE.Controllers.Main.txtAll": "（全部）", "SSE.Controllers.Main.txtArt": "你的文本在此", "SSE.Controllers.Main.txtBasicShapes": "基本形状", "SSE.Controllers.Main.txtBlank": "（空白）", "SSE.Controllers.Main.txtButtons": "按钮", "SSE.Controllers.Main.txtByField": "%1/%2", "SSE.Controllers.Main.txtCallouts": "标注", "SSE.Controllers.Main.txtCharts": "流程图", "SSE.Controllers.Main.txtClearFilter": "清除过滤器", "SSE.Controllers.Main.txtColLbls": "列标签", "SSE.Controllers.Main.txtColumn": "列", "SSE.Controllers.Main.txtConfidential": "机密", "SSE.Controllers.Main.txtDate": "日期", "SSE.Controllers.Main.txtDays": "天", "SSE.Controllers.Main.txtDiagramTitle": "图表标题", "SSE.Controllers.Main.txtEditingMode": "设置编辑模式..", "SSE.Controllers.Main.txtErrorLoadHistory": "历史记录的加载失败", "SSE.Controllers.Main.txtFiguredArrows": "箭头汇总", "SSE.Controllers.Main.txtFile": "文件", "SSE.Controllers.Main.txtGrandTotal": "合计", "SSE.Controllers.Main.txtGroup": "组合", "SSE.Controllers.Main.txtHours": "小时", "SSE.Controllers.Main.txtLines": "线条", "SSE.Controllers.Main.txtMath": "公式形状", "SSE.Controllers.Main.txtMinutes": "分钟", "SSE.Controllers.Main.txtMonths": "月", "SSE.Controllers.Main.txtMultiSelect": "多选模式", "SSE.Controllers.Main.txtOr": "%1或%2", "SSE.Controllers.Main.txtPage": "页面", "SSE.Controllers.Main.txtPageOf": "第%1页，共%2页", "SSE.Controllers.Main.txtPages": "页面", "SSE.Controllers.Main.txtPreparedBy": "编制人", "SSE.Controllers.Main.txtPrintArea": "Print_Area", "SSE.Controllers.Main.txtQuarter": "季度", "SSE.Controllers.Main.txtQuarters": "季度", "SSE.Controllers.Main.txtRectangles": "矩形", "SSE.Controllers.Main.txtRow": "行", "SSE.Controllers.Main.txtRowLbls": "行标签", "SSE.Controllers.Main.txtSeconds": "秒", "SSE.Controllers.Main.txtSeries": "系列", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "线形标注1（边框和强调线）", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "线形标注2（边框和强调线）", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "线形标注3（边框和强调线）", "SSE.Controllers.Main.txtShape_accentCallout1": "线形标注1（强调线）", "SSE.Controllers.Main.txtShape_accentCallout2": "线形标注2（强调线）", "SSE.Controllers.Main.txtShape_accentCallout3": "线形标注3（强调线）", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "后退或上一个按钮", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "开始按钮", "SSE.Controllers.Main.txtShape_actionButtonBlank": "空白按钮", "SSE.Controllers.Main.txtShape_actionButtonDocument": "文件按钮", "SSE.Controllers.Main.txtShape_actionButtonEnd": "结束按钮", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "转发或下一个按钮", "SSE.Controllers.Main.txtShape_actionButtonHelp": "帮助按钮", "SSE.Controllers.Main.txtShape_actionButtonHome": "主页按钮", "SSE.Controllers.Main.txtShape_actionButtonInformation": "信息按钮", "SSE.Controllers.Main.txtShape_actionButtonMovie": "视频按钮", "SSE.Controllers.Main.txtShape_actionButtonReturn": "返回按钮", "SSE.Controllers.Main.txtShape_actionButtonSound": "声音按钮", "SSE.Controllers.Main.txtShape_arc": "弧", "SSE.Controllers.Main.txtShape_bentArrow": "圆角右箭头", "SSE.Controllers.Main.txtShape_bentConnector5": "肘形连接符", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "肘形箭头连接符", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "肘形双箭头连接符", "SSE.Controllers.Main.txtShape_bentUpArrow": "直角上箭头", "SSE.Controllers.Main.txtShape_bevel": "斜角", "SSE.Controllers.Main.txtShape_blockArc": "空心弧", "SSE.Controllers.Main.txtShape_borderCallout1": "线形标注1", "SSE.Controllers.Main.txtShape_borderCallout2": "线形标注2", "SSE.Controllers.Main.txtShape_borderCallout3": "线形标注3", "SSE.Controllers.Main.txtShape_bracePair": "双大括号", "SSE.Controllers.Main.txtShape_callout1": "线形标注1（无边框）", "SSE.Controllers.Main.txtShape_callout2": "线形标注2（无边框）", "SSE.Controllers.Main.txtShape_callout3": "线形标注3（无边框）", "SSE.Controllers.Main.txtShape_can": "圆柱", "SSE.Controllers.Main.txtShape_chevron": "V形", "SSE.Controllers.Main.txtShape_chord": "弦形", "SSE.Controllers.Main.txtShape_circularArrow": "环形箭头", "SSE.Controllers.Main.txtShape_cloud": "云形", "SSE.Controllers.Main.txtShape_cloudCallout": "云形标注", "SSE.Controllers.Main.txtShape_corner": "角", "SSE.Controllers.Main.txtShape_cube": "正方体", "SSE.Controllers.Main.txtShape_curvedConnector3": "曲线连接符", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "曲线箭头连接符", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "曲线双箭头连接符", "SSE.Controllers.Main.txtShape_curvedDownArrow": "下弧形箭头", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "左弧形箭头", "SSE.Controllers.Main.txtShape_curvedRightArrow": "右弧形箭头", "SSE.Controllers.Main.txtShape_curvedUpArrow": "上弧形箭头", "SSE.Controllers.Main.txtShape_decagon": "十边形", "SSE.Controllers.Main.txtShape_diagStripe": "斜纹", "SSE.Controllers.Main.txtShape_diamond": "菱形", "SSE.Controllers.Main.txtShape_dodecagon": "十二边形", "SSE.Controllers.Main.txtShape_donut": "环形", "SSE.Controllers.Main.txtShape_doubleWave": "双波形", "SSE.Controllers.Main.txtShape_downArrow": "下箭头", "SSE.Controllers.Main.txtShape_downArrowCallout": "下箭头标注", "SSE.Controllers.Main.txtShape_ellipse": "椭圆", "SSE.Controllers.Main.txtShape_ellipseRibbon": "下凸弯带形", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "上凸弯带形", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程图：可选流程", "SSE.Controllers.Main.txtShape_flowChartCollate": "流程图：对照", "SSE.Controllers.Main.txtShape_flowChartConnector": "流程图：接点", "SSE.Controllers.Main.txtShape_flowChartDecision": "流程图：决策", "SSE.Controllers.Main.txtShape_flowChartDelay": "流程图：延迟", "SSE.Controllers.Main.txtShape_flowChartDisplay": "流程图：展示", "SSE.Controllers.Main.txtShape_flowChartDocument": "流程图：文档", "SSE.Controllers.Main.txtShape_flowChartExtract": "流程图：摘录", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "流程图：数据", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "流程图：内部贮存", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程图：磁盘", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程图：直接访问存储器", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "流程图：顺序访问存储器", "SSE.Controllers.Main.txtShape_flowChartManualInput": "流程图：手动输入", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "流程图：手动操作", "SSE.Controllers.Main.txtShape_flowChartMerge": "流程图：合并", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "流程图：多文档 ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程图：离页连接符", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程图：存储数据", "SSE.Controllers.Main.txtShape_flowChartOr": "流程图：或", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程图：预定义过程", "SSE.Controllers.Main.txtShape_flowChartPreparation": "流程图：准备", "SSE.Controllers.Main.txtShape_flowChartProcess": "流程图：过程", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "流程图：卡片", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "流程图：资料带", "SSE.Controllers.Main.txtShape_flowChartSort": "流程图：分类", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "流程图：汇总连接", "SSE.Controllers.Main.txtShape_flowChartTerminator": "流程图：终止", "SSE.Controllers.Main.txtShape_foldedCorner": "折角形", "SSE.Controllers.Main.txtShape_frame": "框架", "SSE.Controllers.Main.txtShape_halfFrame": "半闭框", "SSE.Controllers.Main.txtShape_heart": "心形", "SSE.Controllers.Main.txtShape_heptagon": "七边形", "SSE.Controllers.Main.txtShape_hexagon": "六边形", "SSE.Controllers.Main.txtShape_homePlate": "五边形", "SSE.Controllers.Main.txtShape_horizontalScroll": "横卷形", "SSE.Controllers.Main.txtShape_irregularSeal1": "爆炸形1", "SSE.Controllers.Main.txtShape_irregularSeal2": "爆炸形2", "SSE.Controllers.Main.txtShape_leftArrow": "左箭头", "SSE.Controllers.Main.txtShape_leftArrowCallout": "左箭头标注", "SSE.Controllers.Main.txtShape_leftBrace": "左括号", "SSE.Controllers.Main.txtShape_leftBracket": "左中括号", "SSE.Controllers.Main.txtShape_leftRightArrow": "左右箭头", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭头标注", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "丁字箭头", "SSE.Controllers.Main.txtShape_leftUpArrow": "左上箭头", "SSE.Controllers.Main.txtShape_lightningBolt": "闪电形", "SSE.Controllers.Main.txtShape_line": "线", "SSE.Controllers.Main.txtShape_lineWithArrow": "箭头", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "双箭头", "SSE.Controllers.Main.txtShape_mathDivide": "除", "SSE.Controllers.Main.txtShape_mathEqual": "平等", "SSE.Controllers.Main.txtShape_mathMinus": "减去", "SSE.Controllers.Main.txtShape_mathMultiply": "乘", "SSE.Controllers.Main.txtShape_mathNotEqual": "不等于", "SSE.Controllers.Main.txtShape_mathPlus": "加", "SSE.Controllers.Main.txtShape_moon": "月形", "SSE.Controllers.Main.txtShape_noSmoking": "“NO”符号", "SSE.Controllers.Main.txtShape_notchedRightArrow": "燕尾形箭头", "SSE.Controllers.Main.txtShape_octagon": "八边形", "SSE.Controllers.Main.txtShape_parallelogram": "平行四边形", "SSE.Controllers.Main.txtShape_pentagon": "五边形", "SSE.Controllers.Main.txtShape_pie": "饼图", "SSE.Controllers.Main.txtShape_plaque": "符号", "SSE.Controllers.Main.txtShape_plus": "加", "SSE.Controllers.Main.txtShape_polyline1": "自由曲线", "SSE.Controllers.Main.txtShape_polyline2": "任意多边形", "SSE.Controllers.Main.txtShape_quadArrow": "十字箭头", "SSE.Controllers.Main.txtShape_quadArrowCallout": "十字箭头标注", "SSE.Controllers.Main.txtShape_rect": "矩形", "SSE.Controllers.Main.txtShape_ribbon": "下凸带形", "SSE.Controllers.Main.txtShape_ribbon2": "上凸带形", "SSE.Controllers.Main.txtShape_rightArrow": "右箭头", "SSE.Controllers.Main.txtShape_rightArrowCallout": "右箭头标注", "SSE.Controllers.Main.txtShape_rightBrace": "右括号", "SSE.Controllers.Main.txtShape_rightBracket": "右中括号", "SSE.Controllers.Main.txtShape_round1Rect": "单圆角矩形", "SSE.Controllers.Main.txtShape_round2DiagRect": "对角圆角矩形", "SSE.Controllers.Main.txtShape_round2SameRect": "同侧圆角矩形", "SSE.Controllers.Main.txtShape_roundRect": "圆角矩形", "SSE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "SSE.Controllers.Main.txtShape_smileyFace": "笑脸", "SSE.Controllers.Main.txtShape_snip1Rect": "剪去单角的矩形", "SSE.Controllers.Main.txtShape_snip2DiagRect": "剪去对角的矩形", "SSE.Controllers.Main.txtShape_snip2SameRect": "剪去同侧角的矩形", "SSE.Controllers.Main.txtShape_snipRoundRect": "剪去单圆角的矩形", "SSE.Controllers.Main.txtShape_spline": "曲线", "SSE.Controllers.Main.txtShape_star10": "十角星", "SSE.Controllers.Main.txtShape_star12": "十二角星", "SSE.Controllers.Main.txtShape_star16": "十六角星", "SSE.Controllers.Main.txtShape_star24": "二十四角星", "SSE.Controllers.Main.txtShape_star32": "三十二角星", "SSE.Controllers.Main.txtShape_star4": "四角星", "SSE.Controllers.Main.txtShape_star5": "五角星", "SSE.Controllers.Main.txtShape_star6": "六角星", "SSE.Controllers.Main.txtShape_star7": "七角星", "SSE.Controllers.Main.txtShape_star8": "八角星", "SSE.Controllers.Main.txtShape_stripedRightArrow": "虚尾箭头", "SSE.Controllers.Main.txtShape_sun": "太阳形", "SSE.Controllers.Main.txtShape_teardrop": "泪珠形", "SSE.Controllers.Main.txtShape_textRect": "文本框", "SSE.Controllers.Main.txtShape_trapezoid": "梯形", "SSE.Controllers.Main.txtShape_triangle": "三角形", "SSE.Controllers.Main.txtShape_upArrow": "上箭头", "SSE.Controllers.Main.txtShape_upArrowCallout": "上箭头标注", "SSE.Controllers.Main.txtShape_upDownArrow": "上下箭头", "SSE.Controllers.Main.txtShape_uturnArrow": "手杖形箭头", "SSE.Controllers.Main.txtShape_verticalScroll": "竖卷形", "SSE.Controllers.Main.txtShape_wave": "波浪线", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "椭圆形标注", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "矩形标注", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圆角矩形标注", "SSE.Controllers.Main.txtStarsRibbons": "星星和旗帜", "SSE.Controllers.Main.txtStyle_Bad": "差", "SSE.Controllers.Main.txtStyle_Calculation": "计算", "SSE.Controllers.Main.txtStyle_Check_Cell": "检查单元格", "SSE.Controllers.Main.txtStyle_Comma": "逗号", "SSE.Controllers.Main.txtStyle_Currency": "货币", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "说明文本", "SSE.Controllers.Main.txtStyle_Good": "好", "SSE.Controllers.Main.txtStyle_Heading_1": "标题1", "SSE.Controllers.Main.txtStyle_Heading_2": "标题2", "SSE.Controllers.Main.txtStyle_Heading_3": "标题3", "SSE.Controllers.Main.txtStyle_Heading_4": "标题4", "SSE.Controllers.Main.txtStyle_Input": "输入", "SSE.Controllers.Main.txtStyle_Linked_Cell": "关联的单元格", "SSE.Controllers.Main.txtStyle_Neutral": "中立", "SSE.Controllers.Main.txtStyle_Normal": "常规", "SSE.Controllers.Main.txtStyle_Note": "备注", "SSE.Controllers.Main.txtStyle_Output": "输出", "SSE.Controllers.Main.txtStyle_Percent": "百分比", "SSE.Controllers.Main.txtStyle_Title": "标题", "SSE.Controllers.Main.txtStyle_Total": "总数", "SSE.Controllers.Main.txtStyle_Warning_Text": "警告文本", "SSE.Controllers.Main.txtTab": "标签", "SSE.Controllers.Main.txtTable": "表格", "SSE.Controllers.Main.txtTime": "时间", "SSE.Controllers.Main.txtUnlock": "解锁", "SSE.Controllers.Main.txtUnlockRange": "解锁区域", "SSE.Controllers.Main.txtUnlockRangeDescription": "请输入密码以更改此范围:", "SSE.Controllers.Main.txtUnlockRangeWarning": "您尝试更改的区域有密码保护。", "SSE.Controllers.Main.txtValues": "值", "SSE.Controllers.Main.txtXAxis": "X轴", "SSE.Controllers.Main.txtYAxis": "Y轴", "SSE.Controllers.Main.txtYears": "年", "SSE.Controllers.Main.unknownErrorText": "未知错误", "SSE.Controllers.Main.unsupportedBrowserErrorText": "你的浏览器不支持", "SSE.Controllers.Main.uploadDocExtMessage": "未知的文档格式。", "SSE.Controllers.Main.uploadDocFileCountMessage": "没有上载的文档", "SSE.Controllers.Main.uploadDocSizeMessage": "超过最大文档大小限制。", "SSE.Controllers.Main.uploadImageExtMessage": "未知图像格式", "SSE.Controllers.Main.uploadImageFileCountMessage": "没有图片上传", "SSE.Controllers.Main.uploadImageSizeMessage": "图像太大。最大的大小为25MB。", "SSE.Controllers.Main.uploadImageTextText": "上传图片...", "SSE.Controllers.Main.uploadImageTitleText": "图片上传中", "SSE.Controllers.Main.waitText": "请稍候...", "SSE.Controllers.Main.warnBrowserIE9": "该应用程序在IE9上的功能很差。使用IE10或更高版本", "SSE.Controllers.Main.warnBrowserZoom": "您的浏览器当前缩放设置不完全支持。请按Ctrl + 0重设为默认缩放。", "SSE.Controllers.Main.warnLicenseExceeded": "您已达到同时连接%1编辑器的限制。该文档将被打开仅供查看。<br>请联系您的管理员以了解更多。", "SSE.Controllers.Main.warnLicenseExp": "您的许可证已过期。<br>请更新您的许可证并刷新页面。", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "授权过期<br>您不具备文件编辑功能的授权<br>请联系管理员。", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "授权需更新<br>您只有文件编辑功能的部分权限<br>请联系管理员以取得完整权限。", "SSE.Controllers.Main.warnLicenseUsersExceeded": "您已达到%1编辑器的用户限制。请联系您的管理员以了解更多。", "SSE.Controllers.Main.warnNoLicense": "您已达到同时连接%1编辑器的限制。该文档将被打开仅供查看。<br>请联系%1销售团队以了解个人升级条款。", "SSE.Controllers.Main.warnNoLicenseUsers": "您已达到%1编辑器的用户限制。请联系%1销售团队以了解个人升级条款。", "SSE.Controllers.Main.warnProcessRightsChange": "您被拒绝编辑文件的权限。", "SSE.Controllers.Print.strAllSheets": "所有表格", "SSE.Controllers.Print.textFirstCol": "第一列", "SSE.Controllers.Print.textFirstRow": "第一行", "SSE.Controllers.Print.textFrozenCols": "固定列", "SSE.Controllers.Print.textFrozenRows": "固定行", "SSE.Controllers.Print.textInvalidRange": "失败！单元格范围无效", "SSE.Controllers.Print.textNoRepeat": "不重复", "SSE.Controllers.Print.textRepeat": "重复...", "SSE.Controllers.Print.textSelectRange": "选取范围", "SSE.Controllers.Print.textWarning": "警告", "SSE.Controllers.Print.txtCustom": "自定义", "SSE.Controllers.Print.warnCheckMargings": "边距不正确", "SSE.Controllers.Search.textInvalidRange": "错误！无效的单元格范围", "SSE.Controllers.Search.textNoTextFound": "您搜索的数据无法找到。请调整您的搜索选项。", "SSE.Controllers.Search.textReplaceSkipped": "替换已完成。 {0}处跳过。", "SSE.Controllers.Search.textReplaceSuccess": "搜索完成。{0}处已被替换", "SSE.Controllers.Statusbar.errorLastSheet": "工作簿必须至少有一个可见的工作表", "SSE.Controllers.Statusbar.errorRemoveSheet": "不能删除工作表", "SSE.Controllers.Statusbar.strSheet": "表格", "SSE.Controllers.Statusbar.textDisconnect": "<b>连接失败</b><br>正在尝试连接。请检查连接设置。", "SSE.Controllers.Statusbar.textSheetViewTip": "您处于“表视图”模式。过滤器和排序仅对您和仍处于此视图的用户可见。", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "您处于“表视图”模式。过滤器仅对您和仍处于此视图的用户可见。", "SSE.Controllers.Statusbar.warnDeleteSheet": "所选定的工作表可能包含数据。您确定要继续吗？", "SSE.Controllers.Statusbar.zoomText": "缩放％{0}", "SSE.Controllers.Toolbar.confirmAddFontName": "您要保存的字体在当前设备上不可用。<br>使用其中一种系统字体显示文本样式，当可用时将使用保存的字体。<br>是否要继续？", "SSE.Controllers.Toolbar.errorComboSeries": "若要创建组合图，请选择至少两个数据系列。", "SSE.Controllers.Toolbar.errorMaxRows": "错误！每个图表的最大数据系列数为255", "SSE.Controllers.Toolbar.errorStockChart": "行顺序不正确。建立股票图表将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Controllers.Toolbar.textAccent": "导数符号", "SSE.Controllers.Toolbar.textBracket": "括号", "SSE.Controllers.Toolbar.textDirectional": "方向", "SSE.Controllers.Toolbar.textFontSizeErr": "输入的值不正确。<br>请输入1到409之间的数值", "SSE.Controllers.Toolbar.textFraction": "分数", "SSE.Controllers.Toolbar.textFunction": "三角函数", "SSE.Controllers.Toolbar.textIndicator": "标记", "SSE.Controllers.Toolbar.textInsert": "插入", "SSE.Controllers.Toolbar.textIntegral": "积分", "SSE.Controllers.Toolbar.textLargeOperator": "大型运算符", "SSE.Controllers.Toolbar.textLimitAndLog": "极限和对数", "SSE.Controllers.Toolbar.textLongOperation": "长操作", "SSE.Controllers.Toolbar.textMatrix": "矩阵", "SSE.Controllers.Toolbar.textOperator": "运算符", "SSE.Controllers.Toolbar.textPivot": "数据透视表", "SSE.Controllers.Toolbar.textRadical": "根式", "SSE.Controllers.Toolbar.textRating": "等级", "SSE.Controllers.Toolbar.textRecentlyUsed": "最近使用的", "SSE.Controllers.Toolbar.textScript": "上下标", "SSE.Controllers.Toolbar.textShapes": "形状", "SSE.Controllers.Toolbar.textSymbols": "符号", "SSE.Controllers.Toolbar.textWarning": "警告", "SSE.Controllers.Toolbar.txtAccent_Accent": "急性", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "右上方的箭头在上方", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "向左箭头", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "向右箭头上方", "SSE.Controllers.Toolbar.txtAccent_Bar": "条", "SSE.Controllers.Toolbar.txtAccent_BarBot": "下划线", "SSE.Controllers.Toolbar.txtAccent_BarTop": "划线", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "盒装配方（带占位符)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "盒装配方（示例）", "SSE.Controllers.Toolbar.txtAccent_Check": "检查", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "过度", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "A的矢量", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC带横杠", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y With Overbar", "SSE.Controllers.Toolbar.txtAccent_DDDot": "三个点", "SSE.Controllers.Toolbar.txtAccent_DDot": "双点", "SSE.Controllers.Toolbar.txtAccent_Dot": "点", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "双重横杠", "SSE.Controllers.Toolbar.txtAccent_Grave": "严重", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "分组字符下面", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "分组字符以上", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "向左鱼叉以上", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "向右上方的鱼叉", "SSE.Controllers.Toolbar.txtAccent_Hat": "帽子", "SSE.Controllers.Toolbar.txtAccent_Smile": "短音符", "SSE.Controllers.Toolbar.txtAccent_Tilde": "波浪号", "SSE.Controllers.Toolbar.txtBracket_Angle": "括号", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "带分隔符的方括号", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "带分隔符的方括号", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_Curve": "括号", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "带分隔符的方括号", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "案件（两个条件）", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "案件（三个条件）", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "堆栈对象", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "堆栈对象", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "案例示例", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "二项系数", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "二项系数", "SSE.Controllers.Toolbar.txtBracket_Line": "括号", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "括号", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_LowLim": "括号", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_Round": "括号", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "带分隔符的方括号", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_Square": "括号", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括号", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括号", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括号", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "括号", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtBracket_UppLim": "括号", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "单括号", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "单括号", "SSE.Controllers.Toolbar.txtDeleteCells": "删除单元格", "SSE.Controllers.Toolbar.txtExpand": "展开和排序", "SSE.Controllers.Toolbar.txtExpandSort": "选择旁边的数据将不会被排序。要扩展选择以包括相邻数据还是继续排序当前选定的单元格？", "SSE.Controllers.Toolbar.txtFractionDiagonal": "倾斜分数", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "SSE.Controllers.Toolbar.txtFractionHorizontal": "线性分数", "SSE.Controllers.Toolbar.txtFractionPi_2": "π/ 2", "SSE.Controllers.Toolbar.txtFractionSmall": "小分数", "SSE.Controllers.Toolbar.txtFractionVertical": "堆积分数", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "反余弦", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "双曲反余弦函数", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "反余切", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "双曲反正弦函数", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "反余割", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "双曲逆乘功能", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "反正割", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "双曲线逆分割函数", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲反正弦函数", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "反切线", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲反正切函数", "SSE.Controllers.Toolbar.txtFunction_Cos": "余弦", "SSE.Controllers.Toolbar.txtFunction_Cosh": "双曲余弦函数", "SSE.Controllers.Toolbar.txtFunction_Cot": "余切", "SSE.Controllers.Toolbar.txtFunction_Coth": "双曲正交函数", "SSE.Controllers.Toolbar.txtFunction_Csc": "余割", "SSE.Controllers.Toolbar.txtFunction_Csch": "双曲余弦函数", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "正弦θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "切线公式", "SSE.Controllers.Toolbar.txtFunction_Sec": "正割", "SSE.Controllers.Toolbar.txtFunction_Sech": "双曲正割函数", "SSE.Controllers.Toolbar.txtFunction_Sin": "正弦函数", "SSE.Controllers.Toolbar.txtFunction_Sinh": "双曲正弦函数", "SSE.Controllers.Toolbar.txtFunction_Tan": "切线", "SSE.Controllers.Toolbar.txtFunction_Tanh": "双曲正切函数", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "自定义", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "数据和模型", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "好、差和适中", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "无名称", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "数字格式", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "主题单元格样式", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "标题", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "自定义", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "深色的", "SSE.Controllers.Toolbar.txtGroupTable_Light": "浅色的", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "中等", "SSE.Controllers.Toolbar.txtInsertCells": "插入单元格", "SSE.Controllers.Toolbar.txtIntegral": "积分", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "差分θ", "SSE.Controllers.Toolbar.txtIntegral_dx": "差分x", "SSE.Controllers.Toolbar.txtIntegral_dy": "差分y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "积分", "SSE.Controllers.Toolbar.txtIntegralDouble": "双积分", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "双积分", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "双积分", "SSE.Controllers.Toolbar.txtIntegralOriented": "轮廓积分", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "轮廓积分", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "曲面积分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "曲面积分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "曲面积分", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "轮廓积分", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "体积积分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "体积积分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "体积积分", "SSE.Controllers.Toolbar.txtIntegralSubSup": "积分", "SSE.Controllers.Toolbar.txtIntegralTriple": "三重积分", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重积分", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重积分", "SSE.Controllers.Toolbar.txtInvalidRange": "错误！无效的单元格范围", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "副积", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "副积", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "副积", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "副积", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "副积", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "并集", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "三角", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "三角", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "三角", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "三角", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "三角", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交集", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "合计", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "并集", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "极限示例", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大范例", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "极限", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "自然对数", "SSE.Controllers.Toolbar.txtLimitLog_Log": "对数", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "对数", "SSE.Controllers.Toolbar.txtLimitLog_Max": "最大值", "SSE.Controllers.Toolbar.txtLimitLog_Min": "最小值", "SSE.Controllers.Toolbar.txtLockSort": "在选择区附近找到数据，但是你没有足够的权限修改这些单元格。<br>你希望继续操作当前选择区吗？", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2空矩阵", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "空矩阵与支架", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "空矩阵与支架", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "空矩阵与支架", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "空矩阵与支架", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3空矩阵", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1空矩阵", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2空矩阵", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3空矩阵", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基线点", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "中线点", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "对角点", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直点", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "稀疏矩阵", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "稀疏矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2个性矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3个性矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3个性矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3个性矩阵", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "右左箭头下面", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "右上方的箭头在上方", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "向左箭头", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "向左箭头", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下面箭头向右", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "向右箭头上方", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "冒号相等", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "统一", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "三角形区域", "SSE.Controllers.Toolbar.txtOperator_Definition": "相等于定义", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "数据相当于", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "右左箭头下面", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "右上方的箭头在上方", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "向左箭头", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "向左箭头", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下面箭头向右", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "向右箭头上方", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "等号等号", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "减号相等", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "加上等于", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "被测量", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "部首", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "部首", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "平方根", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "带有次数的根式", "SSE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "SSE.Controllers.Toolbar.txtScriptCustom_1": "脚本", "SSE.Controllers.Toolbar.txtScriptCustom_2": "脚本", "SSE.Controllers.Toolbar.txtScriptCustom_3": "脚本", "SSE.Controllers.Toolbar.txtScriptCustom_4": "脚本", "SSE.Controllers.Toolbar.txtScriptSub": "下标", "SSE.Controllers.Toolbar.txtScriptSubSup": "下标", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "左标-标注", "SSE.Controllers.Toolbar.txtScriptSup": "上标", "SSE.Controllers.Toolbar.txtSorting": "排序", "SSE.Controllers.Toolbar.txtSortSelected": "排序选择", "SSE.Controllers.Toolbar.txtSymbol_about": "大约", "SSE.Controllers.Toolbar.txtSymbol_additional": "补集", "SSE.Controllers.Toolbar.txtSymbol_aleph": "阿列夫", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "SSE.Controllers.Toolbar.txtSymbol_approx": "几乎相等", "SSE.Controllers.Toolbar.txtSymbol_ast": "星号运算符", "SSE.Controllers.Toolbar.txtSymbol_beta": "测试版", "SSE.Controllers.Toolbar.txtSymbol_beth": "打赌", "SSE.Controllers.Toolbar.txtSymbol_bullet": "加重号运算符", "SSE.Controllers.Toolbar.txtSymbol_cap": "交集", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "SSE.Controllers.Toolbar.txtSymbol_cdots": "中线水平省略号", "SSE.Controllers.Toolbar.txtSymbol_celsius": "摄氏度", "SSE.Controllers.Toolbar.txtSymbol_chi": "驰", "SSE.Controllers.Toolbar.txtSymbol_cong": "大致相等", "SSE.Controllers.Toolbar.txtSymbol_cup": "并集", "SSE.Controllers.Toolbar.txtSymbol_ddots": "向右对角线省略号", "SSE.Controllers.Toolbar.txtSymbol_degree": "度", "SSE.Controllers.Toolbar.txtSymbol_delta": "参加", "SSE.Controllers.Toolbar.txtSymbol_div": "除号", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "向下箭头", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "空集", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "小量", "SSE.Controllers.Toolbar.txtSymbol_equals": "平等", "SSE.Controllers.Toolbar.txtSymbol_equiv": "相同", "SSE.Controllers.Toolbar.txtSymbol_eta": "和", "SSE.Controllers.Toolbar.txtSymbol_exists": "有存在", "SSE.Controllers.Toolbar.txtSymbol_factorial": "阶乘", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "华氏度", "SSE.Controllers.Toolbar.txtSymbol_forall": "全部", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "大于或等于", "SSE.Controllers.Toolbar.txtSymbol_gg": "远大于", "SSE.Controllers.Toolbar.txtSymbol_greater": "大于", "SSE.Controllers.Toolbar.txtSymbol_in": "元素", "SSE.Controllers.Toolbar.txtSymbol_inc": "增量", "SSE.Controllers.Toolbar.txtSymbol_infinity": "无限", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "卡帕", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭头", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭头", "SSE.Controllers.Toolbar.txtSymbol_leq": "小于或等于", "SSE.Controllers.Toolbar.txtSymbol_less": "小于", "SSE.Controllers.Toolbar.txtSymbol_ll": "远小于", "SSE.Controllers.Toolbar.txtSymbol_minus": "减去", "SSE.Controllers.Toolbar.txtSymbol_mp": "减号加", "SSE.Controllers.Toolbar.txtSymbol_mu": "亩", "SSE.Controllers.Toolbar.txtSymbol_nabla": "微分算符", "SSE.Controllers.Toolbar.txtSymbol_neq": "不等于", "SSE.Controllers.Toolbar.txtSymbol_ni": "包含作为成员", "SSE.Controllers.Toolbar.txtSymbol_not": "未签名", "SSE.Controllers.Toolbar.txtSymbol_notexists": "没有存在", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "奥米克", "SSE.Controllers.Toolbar.txtSymbol_omega": "欧米茄", "SSE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "SSE.Controllers.Toolbar.txtSymbol_percent": "百分比", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "加", "SSE.Controllers.Toolbar.txtSymbol_pm": "加减", "SSE.Controllers.Toolbar.txtSymbol_propto": "成比例", "SSE.Controllers.Toolbar.txtSymbol_psi": "PSI", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "第四根", "SSE.Controllers.Toolbar.txtSymbol_qed": "审校结束", "SSE.Controllers.Toolbar.txtSymbol_rddots": "向右对角线省略号", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭头", "SSE.Controllers.Toolbar.txtSymbol_sigma": "适马", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "SSE.Controllers.Toolbar.txtSymbol_tau": "战斗", "SSE.Controllers.Toolbar.txtSymbol_therefore": "因此", "SSE.Controllers.Toolbar.txtSymbol_theta": "西塔", "SSE.Controllers.Toolbar.txtSymbol_times": "乘法符号", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭头", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon变体", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi 变量", "SSE.Controllers.Toolbar.txtSymbol_varpi": "π变量", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho 变量", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma变量", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "西塔", "SSE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略号", "SSE.Controllers.Toolbar.txtSymbol_xsi": "粒子", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "表样式黑暗", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "表样式光", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "表样式中等", "SSE.Controllers.Toolbar.warnLongOperation": "您即将执行的操作可能需要相当长的时间才能完成。<br>您确定要继续吗？", "SSE.Controllers.Toolbar.warnMergeLostData": "只有来自左上方单元格的数据将保留在合并的单元格中。<br>您确定要继续吗？", "SSE.Controllers.Viewport.textFreezePanes": "冻结面板", "SSE.Controllers.Viewport.textFreezePanesShadow": "显示冻结窗格的阴影", "SSE.Controllers.Viewport.textHideFBar": "隐藏编辑栏", "SSE.Controllers.Viewport.textHideGridlines": "隐藏网格线", "SSE.Controllers.Viewport.textHideHeadings": "隐藏标题", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "小数分隔符", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "千位分隔符", "SSE.Views.AdvancedSeparatorDialog.textLabel": "用于识别数值数据的设置", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "文本限定符", "SSE.Views.AdvancedSeparatorDialog.textTitle": "进阶设置", "SSE.Views.AdvancedSeparatorDialog.txtNone": "（无）", "SSE.Views.AutoFilterDialog.btnCustomFilter": "自定义筛选器", "SSE.Views.AutoFilterDialog.textAddSelection": "添加最新的选择到筛选依据中", "SSE.Views.AutoFilterDialog.textEmptyItem": "空白", "SSE.Views.AutoFilterDialog.textSelectAll": "全选", "SSE.Views.AutoFilterDialog.textSelectAllResults": "选择所有搜索结果", "SSE.Views.AutoFilterDialog.textWarning": "警告", "SSE.Views.AutoFilterDialog.txtAboveAve": "平均水平以上", "SSE.Views.AutoFilterDialog.txtBegins": "以......开始", "SSE.Views.AutoFilterDialog.txtBelowAve": "在平均值以下", "SSE.Views.AutoFilterDialog.txtBetween": "在......之间", "SSE.Views.AutoFilterDialog.txtClear": "清除", "SSE.Views.AutoFilterDialog.txtContains": "包含", "SSE.Views.AutoFilterDialog.txtEmpty": "进入单元格过滤器", "SSE.Views.AutoFilterDialog.txtEnds": "结束于...", "SSE.Views.AutoFilterDialog.txtEquals": "等于", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "按单元格颜色过滤", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "按字体颜色过滤", "SSE.Views.AutoFilterDialog.txtGreater": "大于...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "大于或等于...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "标签过滤器", "SSE.Views.AutoFilterDialog.txtLess": "少于...", "SSE.Views.AutoFilterDialog.txtLessEquals": "小于或等于", "SSE.Views.AutoFilterDialog.txtNotBegins": "不是从...开始", "SSE.Views.AutoFilterDialog.txtNotBetween": "不介于...", "SSE.Views.AutoFilterDialog.txtNotContains": "不含...", "SSE.Views.AutoFilterDialog.txtNotEnds": "不是以...结束", "SSE.Views.AutoFilterDialog.txtNotEquals": "不等于...", "SSE.Views.AutoFilterDialog.txtNumFilter": "号码过滤器", "SSE.Views.AutoFilterDialog.txtReapply": "Reapply", "SSE.Views.AutoFilterDialog.txtSortCellColor": "按元数据颜色排序", "SSE.Views.AutoFilterDialog.txtSortFontColor": "按字体颜色排序", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "排序最高到最低", "SSE.Views.AutoFilterDialog.txtSortLow2High": "排序最低到最高", "SSE.Views.AutoFilterDialog.txtSortOption": "其它排序选项...", "SSE.Views.AutoFilterDialog.txtTextFilter": "文字过滤器", "SSE.Views.AutoFilterDialog.txtTitle": "过滤", "SSE.Views.AutoFilterDialog.txtTop10": "前十位", "SSE.Views.AutoFilterDialog.txtValueFilter": "按值筛选", "SSE.Views.AutoFilterDialog.warnFilterError": "为了使筛选器生效，你至少需要一个值位于值区域中。", "SSE.Views.AutoFilterDialog.warnNoSelected": "您必须至少选择一个值", "SSE.Views.CellEditor.textManager": "名称管理", "SSE.Views.CellEditor.tipFormula": "插入功能", "SSE.Views.CellRangeDialog.errorMaxRows": "错误！每个图表的最大数据系列数为255", "SSE.Views.CellRangeDialog.errorStockChart": "行顺序不正确。建立股票图表将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.CellRangeDialog.txtEmpty": "这是必填栏", "SSE.Views.CellRangeDialog.txtInvalidRange": "错误！无效的单元格范围", "SSE.Views.CellRangeDialog.txtTitle": "选择数据范围", "SSE.Views.CellSettings.strShrink": "缩小字体填充", "SSE.Views.CellSettings.strWrap": "文字换行", "SSE.Views.CellSettings.textAngle": "角度", "SSE.Views.CellSettings.textBackColor": "背景颜色", "SSE.Views.CellSettings.textBackground": "背景颜色", "SSE.Views.CellSettings.textBorderColor": "颜色", "SSE.Views.CellSettings.textBorders": "边框风格", "SSE.Views.CellSettings.textClearRule": "清除规则", "SSE.Views.CellSettings.textColor": "颜色填充", "SSE.Views.CellSettings.textColorScales": "色阶", "SSE.Views.CellSettings.textCondFormat": "条件格式", "SSE.Views.CellSettings.textControl": "文本控制", "SSE.Views.CellSettings.textDataBars": "数据栏", "SSE.Views.CellSettings.textDirection": "方向", "SSE.Views.CellSettings.textFill": "填满", "SSE.Views.CellSettings.textForeground": "前景色", "SSE.Views.CellSettings.textGradient": "渐变点", "SSE.Views.CellSettings.textGradientColor": "颜色", "SSE.Views.CellSettings.textGradientFill": "渐变填充", "SSE.Views.CellSettings.textIndent": "缩进", "SSE.Views.CellSettings.textItems": "项目", "SSE.Views.CellSettings.textLinear": "线性", "SSE.Views.CellSettings.textManageRule": "管理规则", "SSE.Views.CellSettings.textNewRule": "新规则", "SSE.Views.CellSettings.textNoFill": "没有填充", "SSE.Views.CellSettings.textOrientation": "文字方向", "SSE.Views.CellSettings.textPattern": "模式", "SSE.Views.CellSettings.textPatternFill": "模式", "SSE.Views.CellSettings.textPosition": "位置", "SSE.Views.CellSettings.textRadial": "径向", "SSE.Views.CellSettings.textSelectBorders": "选择您要更改应用样式的边框", "SSE.Views.CellSettings.textSelection": "从当前选择", "SSE.Views.CellSettings.textThisPivot": "从此数据透视表", "SSE.Views.CellSettings.textThisSheet": "从此工作表", "SSE.Views.CellSettings.textThisTable": "从此表格", "SSE.Views.CellSettings.tipAddGradientPoint": "新增渐变点", "SSE.Views.CellSettings.tipAll": "设置外边框和所有内线", "SSE.Views.CellSettings.tipBottom": "仅设置外下边框", "SSE.Views.CellSettings.tipDiagD": "设置对角下边框", "SSE.Views.CellSettings.tipDiagU": "设置对角上边框", "SSE.Views.CellSettings.tipInner": "仅设置内线", "SSE.Views.CellSettings.tipInnerHor": "仅设置水平内线", "SSE.Views.CellSettings.tipInnerVert": "仅设置垂直内线", "SSE.Views.CellSettings.tipLeft": "仅设置左外边框", "SSE.Views.CellSettings.tipNone": "设置无边框", "SSE.Views.CellSettings.tipOuter": "仅设置外边框", "SSE.Views.CellSettings.tipRemoveGradientPoint": "删除渐变点", "SSE.Views.CellSettings.tipRight": "仅设置外边框", "SSE.Views.CellSettings.tipTop": "仅限外边框", "SSE.Views.ChartDataDialog.errorInFormula": "您输入的公式中存在错误。", "SSE.Views.ChartDataDialog.errorInvalidReference": "引用是非法的。引用必须指向一个已打开的工作表。", "SSE.Views.ChartDataDialog.errorMaxPoints": "每个图表最多可以包含4096个点。", "SSE.Views.ChartDataDialog.errorMaxRows": "每个图表最多可以包含255个数据序列。", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "该引用无效。标题，值，大小或数据标签的引用必须是单个单元格，行或列。", "SSE.Views.ChartDataDialog.errorNoValues": "若要创建图表，系列中必须至少包含一个值。", "SSE.Views.ChartDataDialog.errorStockChart": "行顺序不正确，建立股票图表应将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.ChartDataDialog.textAdd": "新增", "SSE.Views.ChartDataDialog.textCategory": "水平坐标标签(类别)", "SSE.Views.ChartDataDialog.textData": "图表数据区域", "SSE.Views.ChartDataDialog.textDelete": "删除", "SSE.Views.ChartDataDialog.textDown": "下", "SSE.Views.ChartDataDialog.textEdit": "编辑", "SSE.Views.ChartDataDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.ChartDataDialog.textSelectData": "选择数据", "SSE.Views.ChartDataDialog.textSeries": "图例项(系列)", "SSE.Views.ChartDataDialog.textSwitch": "切换行/列", "SSE.Views.ChartDataDialog.textTitle": "图表数据", "SSE.Views.ChartDataDialog.textUp": "上", "SSE.Views.ChartDataRangeDialog.errorInFormula": "您输入的公式中存在错误。", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "引用是非法的。引用必须指向一个已打开的工作表。", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "每个图表最多可以包含4096个点。", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "每个图表最多可以包含255个数据序列。", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "该引用无效。标题，值，大小或数据标签的引用必须是单个单元格，行或列。", "SSE.Views.ChartDataRangeDialog.errorNoValues": "若要创建图表，系列中必须至少包含一个值。", "SSE.Views.ChartDataRangeDialog.errorStockChart": "行顺序不正确，建立股票图表应将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.ChartDataRangeDialog.textSelectData": "选择数据", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "轴标签区域", "SSE.Views.ChartDataRangeDialog.txtChoose": "选择范围", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "系列名称", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "坐标轴标题", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "编辑​​系列", "SSE.Views.ChartDataRangeDialog.txtValues": "值", "SSE.Views.ChartDataRangeDialog.txtXValues": "X 值", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y 值", "SSE.Views.ChartSettings.errorMaxRows": "每个图表最多可以包含255个数据序列。", "SSE.Views.ChartSettings.strLineWeight": "线宽", "SSE.Views.ChartSettings.strSparkColor": "颜色", "SSE.Views.ChartSettings.strTemplate": "模板", "SSE.Views.ChartSettings.text3dDepth": "深度(原始深度百分比)", "SSE.Views.ChartSettings.text3dHeight": "高度(原始高度百分比)", "SSE.Views.ChartSettings.text3dRotation": "三维旋转", "SSE.Views.ChartSettings.textAdvanced": "显示高级设置", "SSE.Views.ChartSettings.textAutoscale": "自动缩放", "SSE.Views.ChartSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "SSE.Views.ChartSettings.textChangeType": "更改类型", "SSE.Views.ChartSettings.textChartType": "更改图表类型", "SSE.Views.ChartSettings.textDefault": "默认旋转", "SSE.Views.ChartSettings.textDown": "向下", "SSE.Views.ChartSettings.textEditData": "编辑数据和位置", "SSE.Views.ChartSettings.textFirstPoint": "第一点", "SSE.Views.ChartSettings.textHeight": "高低", "SSE.Views.ChartSettings.textHighPoint": "高点", "SSE.Views.ChartSettings.textKeepRatio": "不变比例", "SSE.Views.ChartSettings.textLastPoint": "最后一点", "SSE.Views.ChartSettings.textLeft": "向左", "SSE.Views.ChartSettings.textLowPoint": "低点", "SSE.Views.ChartSettings.textMarkers": "标记", "SSE.Views.ChartSettings.textNarrow": "缩小视野", "SSE.Views.ChartSettings.textNegativePoint": "负点", "SSE.Views.ChartSettings.textPerspective": "透视", "SSE.Views.ChartSettings.textRanges": "数据范围", "SSE.Views.ChartSettings.textRight": "向右", "SSE.Views.ChartSettings.textRightAngle": "直角坐标轴", "SSE.Views.ChartSettings.textSelectData": "选择数据", "SSE.Views.ChartSettings.textShow": "显示", "SSE.Views.ChartSettings.textSize": "大小", "SSE.Views.ChartSettings.textStyle": "类型", "SSE.Views.ChartSettings.textSwitch": "切换行/列", "SSE.Views.ChartSettings.textType": "类型", "SSE.Views.ChartSettings.textUp": "向上", "SSE.Views.ChartSettings.textWiden": "扩大视野", "SSE.Views.ChartSettings.textWidth": "宽度", "SSE.Views.ChartSettings.textX": "X 旋转", "SSE.Views.ChartSettings.textY": "Y 旋转", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "错误！每个图表序列的最大点值为4096。", "SSE.Views.ChartSettingsDlg.errorMaxRows": "错误！每个图表的最大数据系列数为255", "SSE.Views.ChartSettingsDlg.errorStockChart": "行顺序不正确。建立股票图表将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.ChartSettingsDlg.textAbsolute": "不要移动或调整单元格大小", "SSE.Views.ChartSettingsDlg.textAlt": "可选文本", "SSE.Views.ChartSettingsDlg.textAltDescription": "描述", "SSE.Views.ChartSettingsDlg.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "SSE.Views.ChartSettingsDlg.textAltTitle": "标题", "SSE.Views.ChartSettingsDlg.textAuto": "自动", "SSE.Views.ChartSettingsDlg.textAutoEach": "自动调整", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "轴十字架", "SSE.Views.ChartSettingsDlg.textAxisOptions": "轴的选择", "SSE.Views.ChartSettingsDlg.textAxisPos": "轴的位置", "SSE.Views.ChartSettingsDlg.textAxisSettings": "轴设置", "SSE.Views.ChartSettingsDlg.textAxisTitle": "标题", "SSE.Views.ChartSettingsDlg.textBase": "基础", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "刻度线之间", "SSE.Views.ChartSettingsDlg.textBillions": "十亿", "SSE.Views.ChartSettingsDlg.textBottom": "底部", "SSE.Views.ChartSettingsDlg.textCategoryName": "分类名称", "SSE.Views.ChartSettingsDlg.textCenter": "中心", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "图表元素＆图表图例", "SSE.Views.ChartSettingsDlg.textChartTitle": "图表标题", "SSE.Views.ChartSettingsDlg.textCross": "交叉", "SSE.Views.ChartSettingsDlg.textCustom": "自定义", "SSE.Views.ChartSettingsDlg.textDataColumns": "在列中", "SSE.Views.ChartSettingsDlg.textDataLabels": "数据标签", "SSE.Views.ChartSettingsDlg.textDataRows": "在行", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "显示图例", "SSE.Views.ChartSettingsDlg.textEmptyCells": "隐藏和空白的单元格", "SSE.Views.ChartSettingsDlg.textEmptyLine": "用线连接数据点", "SSE.Views.ChartSettingsDlg.textFit": "适合宽度", "SSE.Views.ChartSettingsDlg.textFixed": "固定", "SSE.Views.ChartSettingsDlg.textFormat": "标签格式", "SSE.Views.ChartSettingsDlg.textGaps": "间隙", "SSE.Views.ChartSettingsDlg.textGridLines": "网格线", "SSE.Views.ChartSettingsDlg.textGroup": "组合迷你图", "SSE.Views.ChartSettingsDlg.textHide": "隐藏", "SSE.Views.ChartSettingsDlg.textHideAxis": "隐藏轴", "SSE.Views.ChartSettingsDlg.textHigh": "高", "SSE.Views.ChartSettingsDlg.textHorAxis": "横轴", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "次要横坐标轴", "SSE.Views.ChartSettingsDlg.textHorizontal": "水平的", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "数以百计", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "在", "SSE.Views.ChartSettingsDlg.textInnerBottom": "内底", "SSE.Views.ChartSettingsDlg.textInnerTop": "内顶", "SSE.Views.ChartSettingsDlg.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.ChartSettingsDlg.textLabelDist": "轴标签的距离", "SSE.Views.ChartSettingsDlg.textLabelInterval": "标签之间的间隔 ", "SSE.Views.ChartSettingsDlg.textLabelOptions": "标签选项", "SSE.Views.ChartSettingsDlg.textLabelPos": "标签位置", "SSE.Views.ChartSettingsDlg.textLayout": "布局", "SSE.Views.ChartSettingsDlg.textLeft": "左", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "左叠加", "SSE.Views.ChartSettingsDlg.textLegendBottom": "底部", "SSE.Views.ChartSettingsDlg.textLegendLeft": "左", "SSE.Views.ChartSettingsDlg.textLegendPos": "传说", "SSE.Views.ChartSettingsDlg.textLegendRight": "右", "SSE.Views.ChartSettingsDlg.textLegendTop": "顶部", "SSE.Views.ChartSettingsDlg.textLines": "行 ", "SSE.Views.ChartSettingsDlg.textLocationRange": "位置范围", "SSE.Views.ChartSettingsDlg.textLogScale": "对数刻度", "SSE.Views.ChartSettingsDlg.textLow": "低", "SSE.Views.ChartSettingsDlg.textMajor": "主要", "SSE.Views.ChartSettingsDlg.textMajorMinor": "主要和次要", "SSE.Views.ChartSettingsDlg.textMajorType": "主要类型", "SSE.Views.ChartSettingsDlg.textManual": "手动更改", "SSE.Views.ChartSettingsDlg.textMarkers": "标记", "SSE.Views.ChartSettingsDlg.textMarksInterval": "标记之间的间隔", "SSE.Views.ChartSettingsDlg.textMaxValue": "最大值", "SSE.Views.ChartSettingsDlg.textMillions": "百万", "SSE.Views.ChartSettingsDlg.textMinor": "次要", "SSE.Views.ChartSettingsDlg.textMinorType": "次要类型", "SSE.Views.ChartSettingsDlg.textMinValue": "最小值", "SSE.Views.ChartSettingsDlg.textNextToAxis": "在轴旁边", "SSE.Views.ChartSettingsDlg.textNone": "无", "SSE.Views.ChartSettingsDlg.textNoOverlay": "没有叠加", "SSE.Views.ChartSettingsDlg.textOneCell": "移动但不按单元格大小调整", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "刻度标记", "SSE.Views.ChartSettingsDlg.textOut": "向外", "SSE.Views.ChartSettingsDlg.textOuterTop": "外顶", "SSE.Views.ChartSettingsDlg.textOverlay": "覆盖", "SSE.Views.ChartSettingsDlg.textReverse": "值相反的顺序", "SSE.Views.ChartSettingsDlg.textReverseOrder": "相反的顺序", "SSE.Views.ChartSettingsDlg.textRight": "右", "SSE.Views.ChartSettingsDlg.textRightOverlay": "正确覆盖", "SSE.Views.ChartSettingsDlg.textRotated": "旋转", "SSE.Views.ChartSettingsDlg.textSameAll": "全都相同", "SSE.Views.ChartSettingsDlg.textSelectData": "选择数据", "SSE.Views.ChartSettingsDlg.textSeparator": "数据标签分隔符", "SSE.Views.ChartSettingsDlg.textSeriesName": "系列名称", "SSE.Views.ChartSettingsDlg.textShow": "显示", "SSE.Views.ChartSettingsDlg.textShowBorders": "显示图表边框", "SSE.Views.ChartSettingsDlg.textShowData": "以隐藏的行和列显示数据", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "显示空单元格", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "显示轴", "SSE.Views.ChartSettingsDlg.textShowValues": "显示图表值", "SSE.Views.ChartSettingsDlg.textSingle": "单一的迷你图", "SSE.Views.ChartSettingsDlg.textSmooth": "光滑", "SSE.Views.ChartSettingsDlg.textSnap": "单元捕捉", "SSE.Views.ChartSettingsDlg.textSparkRanges": "迷你图范围", "SSE.Views.ChartSettingsDlg.textStraight": "直行", "SSE.Views.ChartSettingsDlg.textStyle": "类型", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "成千上万", "SSE.Views.ChartSettingsDlg.textTickOptions": "勾选选项", "SSE.Views.ChartSettingsDlg.textTitle": "图 - 高级设置", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "迷你图-高级设置", "SSE.Views.ChartSettingsDlg.textTop": "顶部", "SSE.Views.ChartSettingsDlg.textTrillions": "万亿", "SSE.Views.ChartSettingsDlg.textTwoCell": "随单元格移动和调整大小", "SSE.Views.ChartSettingsDlg.textType": "类型", "SSE.Views.ChartSettingsDlg.textTypeData": "类型和数据", "SSE.Views.ChartSettingsDlg.textUnits": "显示单位", "SSE.Views.ChartSettingsDlg.textValue": "值", "SSE.Views.ChartSettingsDlg.textVertAxis": "垂直轴", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "次要纵坐标轴", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X轴标题", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y轴标题", "SSE.Views.ChartSettingsDlg.textZero": "零", "SSE.Views.ChartSettingsDlg.txtEmpty": "这是必填栏", "SSE.Views.ChartTypeDialog.errorComboSeries": "若要创建组合图，请选择至少两个数据系列。", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "所选图表类型需要现有图表使用的次坐标轴。请选择其他图表类型。", "SSE.Views.ChartTypeDialog.textSecondary": "次坐标轴", "SSE.Views.ChartTypeDialog.textSeries": "系列", "SSE.Views.ChartTypeDialog.textStyle": "样式", "SSE.Views.ChartTypeDialog.textTitle": "图表类型", "SSE.Views.ChartTypeDialog.textType": "类型", "SSE.Views.CreatePivotDialog.textDataRange": "源数据区域", "SSE.Views.CreatePivotDialog.textDestination": "选择安放表格的位置", "SSE.Views.CreatePivotDialog.textExist": "已有的工作表", "SSE.Views.CreatePivotDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.CreatePivotDialog.textNew": "新建工作表", "SSE.Views.CreatePivotDialog.textSelectData": "选择数据", "SSE.Views.CreatePivotDialog.textTitle": "创建数据透视表", "SSE.Views.CreatePivotDialog.txtEmpty": "这是必填栏", "SSE.Views.CreateSparklineDialog.textDataRange": "源数据区域", "SSE.Views.CreateSparklineDialog.textDestination": "选择放置迷你图表的位置。", "SSE.Views.CreateSparklineDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.CreateSparklineDialog.textSelectData": "选择数据", "SSE.Views.CreateSparklineDialog.textTitle": "创建迷你图", "SSE.Views.CreateSparklineDialog.txtEmpty": "此字段是必需的", "SSE.Views.DataTab.capBtnGroup": "分组", "SSE.Views.DataTab.capBtnTextCustomSort": "自定义排序", "SSE.Views.DataTab.capBtnTextDataValidation": "数据校验", "SSE.Views.DataTab.capBtnTextRemDuplicates": "移除重复的元素", "SSE.Views.DataTab.capBtnTextToCol": "文本分列向导", "SSE.Views.DataTab.capBtnUngroup": "取消组合", "SSE.Views.DataTab.capDataFromText": "获取数据", "SSE.Views.DataTab.mniFromFile": "来自本地TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "从网络地址导入TEXT/CSV", "SSE.Views.DataTab.textBelow": "详细信息下的摘要行", "SSE.Views.DataTab.textClear": "清晰的轮廓", "SSE.Views.DataTab.textColumns": "取消列分组", "SSE.Views.DataTab.textGroupColumns": "分组列", "SSE.Views.DataTab.textGroupRows": "分组行", "SSE.Views.DataTab.textRightOf": "详细信息右侧的摘要列", "SSE.Views.DataTab.textRows": "取消行分组", "SSE.Views.DataTab.tipCustomSort": "自定义排序", "SSE.Views.DataTab.tipDataFromText": "从文本/CSV文件中获取数据", "SSE.Views.DataTab.tipDataValidation": "数据校验", "SSE.Views.DataTab.tipGroup": "单元组范围", "SSE.Views.DataTab.tipRemDuplicates": "从表单中移除重复的行", "SSE.Views.DataTab.tipToColumns": "将单元格文本分隔成列", "SSE.Views.DataTab.tipUngroup": "取消单元格范围分组", "SSE.Views.DataValidationDialog.errorFormula": "该值当前包含错误。是否继续?", "SSE.Views.DataValidationDialog.errorInvalid": "输入的 \"{0}\" 值无效。", "SSE.Views.DataValidationDialog.errorInvalidDate": "您为字段 “{0}” 输入的日期无效。", "SSE.Views.DataValidationDialog.errorInvalidList": "列表源必须是定界列表，或者是对单行或单列的引用。", "SSE.Views.DataValidationDialog.errorInvalidTime": "输入到 \"{0}\" 的时间无效。", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "“{1}”字段必须大于或等于“{0}”字段。", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "你必须输入在字段“{0}”和字段“{1}”都输入输入一个值。", "SSE.Views.DataValidationDialog.errorMustEnterValue": "您必须在\"{0}\"字段中输入值。", "SSE.Views.DataValidationDialog.errorNamedRange": "找不到指定的命名区域。", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "负值不能用在条件\"{0}\"中。", "SSE.Views.DataValidationDialog.errorNotNumeric": "字段“{0}”必须为一个数字，数字表达式，或者引用一个含数字的单元格。", "SSE.Views.DataValidationDialog.strError": "错误警报", "SSE.Views.DataValidationDialog.strInput": "输入信息", "SSE.Views.DataValidationDialog.strSettings": "设置", "SSE.Views.DataValidationDialog.textAlert": "提示", "SSE.Views.DataValidationDialog.textAllow": "允许", "SSE.Views.DataValidationDialog.textApply": "将这些变化应用于所有其他具有相同设置的单元格", "SSE.Views.DataValidationDialog.textCellSelected": "选定单元格时显示下列输入信息", "SSE.Views.DataValidationDialog.textCompare": "相比于", "SSE.Views.DataValidationDialog.textData": "数据", "SSE.Views.DataValidationDialog.textEndDate": "结束日期", "SSE.Views.DataValidationDialog.textEndTime": "结束时间", "SSE.Views.DataValidationDialog.textError": "错误消息", "SSE.Views.DataValidationDialog.textFormula": "公式", "SSE.Views.DataValidationDialog.textIgnore": "忽略空值", "SSE.Views.DataValidationDialog.textInput": "输入信息", "SSE.Views.DataValidationDialog.textMax": "最大值", "SSE.Views.DataValidationDialog.textMessage": "信息", "SSE.Views.DataValidationDialog.textMin": "最小值", "SSE.Views.DataValidationDialog.textSelectData": "选择数据", "SSE.Views.DataValidationDialog.textShowDropDown": "在单元格中显示下拉列表", "SSE.Views.DataValidationDialog.textShowError": "输入无效数据后显示错误提示", "SSE.Views.DataValidationDialog.textShowInput": "选择单元格时显示输入消息", "SSE.Views.DataValidationDialog.textSource": "来源", "SSE.Views.DataValidationDialog.textStartDate": "开始日期", "SSE.Views.DataValidationDialog.textStartTime": "开始时间", "SSE.Views.DataValidationDialog.textStop": "停止", "SSE.Views.DataValidationDialog.textStyle": "样式", "SSE.Views.DataValidationDialog.textTitle": "标题", "SSE.Views.DataValidationDialog.textUserEnters": "用户输入无效数据时显示下列出错警告", "SSE.Views.DataValidationDialog.txtAny": "任何值", "SSE.Views.DataValidationDialog.txtBetween": "介于", "SSE.Views.DataValidationDialog.txtDate": "日期", "SSE.Views.DataValidationDialog.txtDecimal": "十进制", "SSE.Views.DataValidationDialog.txtElTime": "运行时间", "SSE.Views.DataValidationDialog.txtEndDate": "结束日期", "SSE.Views.DataValidationDialog.txtEndTime": "结束时间", "SSE.Views.DataValidationDialog.txtEqual": "等于", "SSE.Views.DataValidationDialog.txtGreaterThan": "大于", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "大于或等于", "SSE.Views.DataValidationDialog.txtLength": "长度", "SSE.Views.DataValidationDialog.txtLessThan": "小于", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "小于或等于", "SSE.Views.DataValidationDialog.txtList": "列表", "SSE.Views.DataValidationDialog.txtNotBetween": "不介于", "SSE.Views.DataValidationDialog.txtNotEqual": "不等于", "SSE.Views.DataValidationDialog.txtOther": "其他", "SSE.Views.DataValidationDialog.txtStartDate": "开始日期", "SSE.Views.DataValidationDialog.txtStartTime": "开始时间", "SSE.Views.DataValidationDialog.txtTextLength": "文本长度", "SSE.Views.DataValidationDialog.txtTime": "时间", "SSE.Views.DataValidationDialog.txtWhole": "整数", "SSE.Views.DigitalFilterDialog.capAnd": "和", "SSE.Views.DigitalFilterDialog.capCondition1": "等于", "SSE.Views.DigitalFilterDialog.capCondition10": "不结束于", "SSE.Views.DigitalFilterDialog.capCondition11": "包含", "SSE.Views.DigitalFilterDialog.capCondition12": "不含", "SSE.Views.DigitalFilterDialog.capCondition2": "不等于", "SSE.Views.DigitalFilterDialog.capCondition3": "大于", "SSE.Views.DigitalFilterDialog.capCondition4": "大于或等于", "SSE.Views.DigitalFilterDialog.capCondition5": "小于", "SSE.Views.DigitalFilterDialog.capCondition6": "小于或等于", "SSE.Views.DigitalFilterDialog.capCondition7": "以......开始", "SSE.Views.DigitalFilterDialog.capCondition8": "不起始于", "SSE.Views.DigitalFilterDialog.capCondition9": "结束", "SSE.Views.DigitalFilterDialog.capOr": "或", "SSE.Views.DigitalFilterDialog.textNoFilter": "没有过滤器", "SSE.Views.DigitalFilterDialog.textShowRows": "显示行在哪里", "SSE.Views.DigitalFilterDialog.textUse1": "使用 ？呈现任何单个字符", "SSE.Views.DigitalFilterDialog.textUse2": "使用*来呈现任何一系列的角色", "SSE.Views.DigitalFilterDialog.txtTitle": "自定义筛选器", "SSE.Views.DocumentHolder.advancedEquationText": "方程式设置", "SSE.Views.DocumentHolder.advancedImgText": "高级图像设置", "SSE.Views.DocumentHolder.advancedShapeText": "形状高级设置", "SSE.Views.DocumentHolder.advancedSlicerText": "切片器高级设置", "SSE.Views.DocumentHolder.allLinearText": "全部 - 线性", "SSE.Views.DocumentHolder.allProfText": "全部 - 专业", "SSE.Views.DocumentHolder.bottomCellText": "靠下对齐", "SSE.Views.DocumentHolder.bulletsText": "符号和编号", "SSE.Views.DocumentHolder.centerCellText": "垂直居中", "SSE.Views.DocumentHolder.chartDataText": "选择图标数据", "SSE.Views.DocumentHolder.chartText": "图表高级设置", "SSE.Views.DocumentHolder.chartTypeText": "更改图表类型", "SSE.Views.DocumentHolder.currLinearText": "当前 - 线性", "SSE.Views.DocumentHolder.currProfText": "当前 - 专业", "SSE.Views.DocumentHolder.deleteColumnText": "列", "SSE.Views.DocumentHolder.deleteRowText": "行", "SSE.Views.DocumentHolder.deleteTableText": "表格", "SSE.Views.DocumentHolder.direct270Text": "向上旋转文字", "SSE.Views.DocumentHolder.direct90Text": "向下旋转文字", "SSE.Views.DocumentHolder.directHText": "水平的", "SSE.Views.DocumentHolder.directionText": "文字方向", "SSE.Views.DocumentHolder.editChartText": "编辑数据", "SSE.Views.DocumentHolder.editHyperlinkText": "编辑超链接", "SSE.Views.DocumentHolder.insertColumnLeftText": "列的左右", "SSE.Views.DocumentHolder.insertColumnRightText": "右列", "SSE.Views.DocumentHolder.insertRowAboveText": "上面的行", "SSE.Views.DocumentHolder.insertRowBelowText": "下面的行", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "实际大小", "SSE.Views.DocumentHolder.removeHyperlinkText": "删除超链接", "SSE.Views.DocumentHolder.selectColumnText": "整列", "SSE.Views.DocumentHolder.selectDataText": "列数据", "SSE.Views.DocumentHolder.selectRowText": "行", "SSE.Views.DocumentHolder.selectTableText": "表格", "SSE.Views.DocumentHolder.strDelete": "删除签名", "SSE.Views.DocumentHolder.strDetails": "签名详情", "SSE.Views.DocumentHolder.strSetup": "签名设置", "SSE.Views.DocumentHolder.strSign": "符号", "SSE.Views.DocumentHolder.textAlign": "对齐", "SSE.Views.DocumentHolder.textArrange": "安排", "SSE.Views.DocumentHolder.textArrangeBack": "发送到背景", "SSE.Views.DocumentHolder.textArrangeBackward": "下移一层", "SSE.Views.DocumentHolder.textArrangeForward": "上移一层", "SSE.Views.DocumentHolder.textArrangeFront": "放到最上面", "SSE.Views.DocumentHolder.textAverage": "平均值", "SSE.Views.DocumentHolder.textBullets": "项目符号", "SSE.Views.DocumentHolder.textCount": "计数", "SSE.Views.DocumentHolder.textCrop": "裁剪", "SSE.Views.DocumentHolder.textCropFill": "填满", "SSE.Views.DocumentHolder.textCropFit": "最佳", "SSE.Views.DocumentHolder.textEditPoints": "编辑点", "SSE.Views.DocumentHolder.textEntriesList": "从下拉列表中选择", "SSE.Views.DocumentHolder.textFlipH": "水平翻转", "SSE.Views.DocumentHolder.textFlipV": "垂直翻转", "SSE.Views.DocumentHolder.textFreezePanes": "冻结面板", "SSE.Views.DocumentHolder.textFromFile": "从文件导入", "SSE.Views.DocumentHolder.textFromStorage": "来自存储设备", "SSE.Views.DocumentHolder.textFromUrl": "从URL", "SSE.Views.DocumentHolder.textListSettings": "列表设置", "SSE.Views.DocumentHolder.textMacro": "指定宏", "SSE.Views.DocumentHolder.textMax": "最大值", "SSE.Views.DocumentHolder.textMin": "最小值", "SSE.Views.DocumentHolder.textMore": "其他函数", "SSE.Views.DocumentHolder.textMoreFormats": "更多格式", "SSE.Views.DocumentHolder.textNone": "无", "SSE.Views.DocumentHolder.textNumbering": "编号", "SSE.Views.DocumentHolder.textReplace": "替换图像", "SSE.Views.DocumentHolder.textRotate": "旋转", "SSE.Views.DocumentHolder.textRotate270": "逆时针旋转90°", "SSE.Views.DocumentHolder.textRotate90": "顺时针旋转90°", "SSE.Views.DocumentHolder.textShapeAlignBottom": "靠下对齐", "SSE.Views.DocumentHolder.textShapeAlignCenter": "居中对齐", "SSE.Views.DocumentHolder.textShapeAlignLeft": "左对齐", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "垂直居中", "SSE.Views.DocumentHolder.textShapeAlignRight": "右对齐", "SSE.Views.DocumentHolder.textShapeAlignTop": "靠上对齐", "SSE.Views.DocumentHolder.textStdDev": "标准差", "SSE.Views.DocumentHolder.textSum": "合计", "SSE.Views.DocumentHolder.textUndo": "复原", "SSE.Views.DocumentHolder.textUnFreezePanes": "解冻窗格", "SSE.Views.DocumentHolder.textVar": "方差", "SSE.Views.DocumentHolder.tipMarkersArrow": "箭头项目符号", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "选中标记项目符号", "SSE.Views.DocumentHolder.tipMarkersDash": "划线项目符号", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "实心菱形项目符号", "SSE.Views.DocumentHolder.tipMarkersFRound": "实心圆形项目符号", "SSE.Views.DocumentHolder.tipMarkersFSquare": "实心方形项目符号", "SSE.Views.DocumentHolder.tipMarkersHRound": "空心圆形项目符号", "SSE.Views.DocumentHolder.tipMarkersStar": "星形项目符号", "SSE.Views.DocumentHolder.topCellText": "靠上对齐", "SSE.Views.DocumentHolder.txtAccounting": "统计", "SSE.Views.DocumentHolder.txtAddComment": "添加批注", "SSE.Views.DocumentHolder.txtAddNamedRange": "定义名称", "SSE.Views.DocumentHolder.txtArrange": "安排", "SSE.Views.DocumentHolder.txtAscending": "升序", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "自动调整列宽", "SSE.Views.DocumentHolder.txtAutoRowHeight": "自动调整行距", "SSE.Views.DocumentHolder.txtClear": "清除", "SSE.Views.DocumentHolder.txtClearAll": "所有", "SSE.Views.DocumentHolder.txtClearComments": "批注", "SSE.Views.DocumentHolder.txtClearFormat": "格式", "SSE.Views.DocumentHolder.txtClearHyper": "超链接", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "清除所选的Sparkline组", "SSE.Views.DocumentHolder.txtClearSparklines": "清除所选的迷你曲", "SSE.Views.DocumentHolder.txtClearText": "文本", "SSE.Views.DocumentHolder.txtColumn": "整列", "SSE.Views.DocumentHolder.txtColumnWidth": "设置列宽", "SSE.Views.DocumentHolder.txtCondFormat": "条件格式", "SSE.Views.DocumentHolder.txtCopy": "复制", "SSE.Views.DocumentHolder.txtCurrency": "货币", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "自定义列宽", "SSE.Views.DocumentHolder.txtCustomRowHeight": "自定义行高度", "SSE.Views.DocumentHolder.txtCustomSort": "自定义排序", "SSE.Views.DocumentHolder.txtCut": "剪切", "SSE.Views.DocumentHolder.txtDate": "日期", "SSE.Views.DocumentHolder.txtDelete": "删除", "SSE.Views.DocumentHolder.txtDescending": "降序", "SSE.Views.DocumentHolder.txtDistribHor": "水平分布", "SSE.Views.DocumentHolder.txtDistribVert": "垂直分布", "SSE.Views.DocumentHolder.txtEditComment": "编辑批注", "SSE.Views.DocumentHolder.txtFilter": "过滤", "SSE.Views.DocumentHolder.txtFilterCellColor": "按单元格的颜色过滤", "SSE.Views.DocumentHolder.txtFilterFontColor": "按字体颜色过滤", "SSE.Views.DocumentHolder.txtFilterValue": "按选定单元格的值过滤", "SSE.Views.DocumentHolder.txtFormula": "插入功能", "SSE.Views.DocumentHolder.txtFraction": "分数", "SSE.Views.DocumentHolder.txtGeneral": "常规", "SSE.Views.DocumentHolder.txtGetLink": "获取该范围的链接", "SSE.Views.DocumentHolder.txtGroup": "分组", "SSE.Views.DocumentHolder.txtHide": "隐藏", "SSE.Views.DocumentHolder.txtInsert": "插入", "SSE.Views.DocumentHolder.txtInsHyperlink": "超链接", "SSE.Views.DocumentHolder.txtNumber": "数", "SSE.Views.DocumentHolder.txtNumFormat": "数字格式", "SSE.Views.DocumentHolder.txtPaste": "粘贴", "SSE.Views.DocumentHolder.txtPercentage": "百分比", "SSE.Views.DocumentHolder.txtReapply": "Reapply", "SSE.Views.DocumentHolder.txtRefresh": "刷新", "SSE.Views.DocumentHolder.txtRow": "整行", "SSE.Views.DocumentHolder.txtRowHeight": "设置行高", "SSE.Views.DocumentHolder.txtScientific": "科学", "SSE.Views.DocumentHolder.txtSelect": "选择", "SSE.Views.DocumentHolder.txtShiftDown": "向下移动单元格", "SSE.Views.DocumentHolder.txtShiftLeft": "移动单元格", "SSE.Views.DocumentHolder.txtShiftRight": "右移小区", "SSE.Views.DocumentHolder.txtShiftUp": "向上移动单元格", "SSE.Views.DocumentHolder.txtShow": "显示", "SSE.Views.DocumentHolder.txtShowComment": "显示批注", "SSE.Views.DocumentHolder.txtSort": "分类", "SSE.Views.DocumentHolder.txtSortCellColor": "所选单元格颜色在顶部", "SSE.Views.DocumentHolder.txtSortFontColor": "选定的字体颜色在上面", "SSE.Views.DocumentHolder.txtSparklines": "迷你", "SSE.Views.DocumentHolder.txtText": "文本", "SSE.Views.DocumentHolder.txtTextAdvanced": "段落高级设置", "SSE.Views.DocumentHolder.txtTime": "时间", "SSE.Views.DocumentHolder.txtUngroup": "取消组合", "SSE.Views.DocumentHolder.txtWidth": "宽度", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "垂直对齐", "SSE.Views.ExternalLinksDlg.closeButtonText": "关闭", "SSE.Views.ExternalLinksDlg.textDeleteAll": "断开所有链接", "SSE.Views.ExternalLinksDlg.textSource": "来源", "SSE.Views.FieldSettingsDialog.strLayout": "布局", "SSE.Views.FieldSettingsDialog.strSubtotals": "分类汇总", "SSE.Views.FieldSettingsDialog.textReport": "报告", "SSE.Views.FieldSettingsDialog.textTitle": "域设置", "SSE.Views.FieldSettingsDialog.txtAverage": "平均值", "SSE.Views.FieldSettingsDialog.txtBlank": "在每一项后面插入一个空行", "SSE.Views.FieldSettingsDialog.txtBottom": "在群组底部显示", "SSE.Views.FieldSettingsDialog.txtCompact": "紧致", "SSE.Views.FieldSettingsDialog.txtCount": "计数", "SSE.Views.FieldSettingsDialog.txtCountNums": "计数", "SSE.Views.FieldSettingsDialog.txtCustomName": "自定义名称", "SSE.Views.FieldSettingsDialog.txtEmpty": "显示无数据的项目", "SSE.Views.FieldSettingsDialog.txtMax": "最大值", "SSE.Views.FieldSettingsDialog.txtMin": "最小值", "SSE.Views.FieldSettingsDialog.txtOutline": "大纲", "SSE.Views.FieldSettingsDialog.txtProduct": "乘积", "SSE.Views.FieldSettingsDialog.txtRepeat": "在每行重复标记项目", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "显示分类汇总", "SSE.Views.FieldSettingsDialog.txtSourceName": "源名称：", "SSE.Views.FieldSettingsDialog.txtStdDev": "标准差", "SSE.Views.FieldSettingsDialog.txtStdDevp": "总体标准偏差", "SSE.Views.FieldSettingsDialog.txtSum": "合计", "SSE.Views.FieldSettingsDialog.txtSummarize": "用于小计的函数", "SSE.Views.FieldSettingsDialog.txtTabular": "表格", "SSE.Views.FieldSettingsDialog.txtTop": "在群组顶部显示", "SSE.Views.FieldSettingsDialog.txtVar": "方差", "SSE.Views.FieldSettingsDialog.txtVarp": "总体方差", "SSE.Views.FileMenu.btnBackCaption": "打开文件所在位置", "SSE.Views.FileMenu.btnCloseMenuCaption": "关闭菜单", "SSE.Views.FileMenu.btnCreateNewCaption": "新建", "SSE.Views.FileMenu.btnDownloadCaption": "下载为", "SSE.Views.FileMenu.btnExitCaption": "关闭", "SSE.Views.FileMenu.btnFileOpenCaption": "打开", "SSE.Views.FileMenu.btnHelpCaption": "帮助", "SSE.Views.FileMenu.btnHistoryCaption": "版本历史", "SSE.Views.FileMenu.btnInfoCaption": "电子表格信息", "SSE.Views.FileMenu.btnPrintCaption": "打印", "SSE.Views.FileMenu.btnProtectCaption": "保护", "SSE.Views.FileMenu.btnRecentFilesCaption": "打开最近", "SSE.Views.FileMenu.btnRenameCaption": "改名", "SSE.Views.FileMenu.btnReturnCaption": "回到电子表格", "SSE.Views.FileMenu.btnRightsCaption": "访问权", "SSE.Views.FileMenu.btnSaveAsCaption": "另存为", "SSE.Views.FileMenu.btnSaveCaption": "保存", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "将副本另存为", "SSE.Views.FileMenu.btnSettingsCaption": "高级设置", "SSE.Views.FileMenu.btnToEditCaption": "编辑电子表格", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "空白表格", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新建", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "应用", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "添加作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "添加文本", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "应用", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "更改访问权限", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "批注", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已创建", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "上次修改时间", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上次修改时间", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "创建者", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "有权利的人", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主题", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "标签", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "标题", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "上载", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "更改访问权限", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "有权利的人", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "应用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "共同编辑模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "使用1904年日期系统", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "小数分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "词典语言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "自动", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "字体微调方式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "公式语言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "例：SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "忽略大写单词", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "忽略带数字的单词", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "宏设置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "粘贴内容时显示“粘贴选项”按钮", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1参考样式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "区域设置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "例： ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "在表格中显示注释", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "显示其他用户的更改", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "显示已解决的注释", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "手动", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "界面主题", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "千位分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "测量单位", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "根据区域设置使用分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "默认缩放比率", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "每10分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "每30分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "每5分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "每隔一小时", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "自动恢复", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "自动保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "已禁用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "在保存中间版本", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "每一分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "参考风格", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "自动修正选项...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "白俄罗斯文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "保加利亚文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "加泰罗尼亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "默认缓存模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "计算", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "厘米", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "协作", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "捷克语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "丹麦语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "德语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "编辑并保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "希腊语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "英语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "西班牙语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "实时协同编辑。所有修改自动保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "芬兰语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "法语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "匈牙利语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "印度尼西亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "寸", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "意大利语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "日语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "韩语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "老挝语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "拉脱维亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "仿照 OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "本机系统原生", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "挪威语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "荷兰语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "抛光", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "校对", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "点", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "葡萄牙语(巴西)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "葡萄牙语(葡萄牙)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "地区", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "罗马尼亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "俄语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "启动所有项目", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "启动所有不带通知的宏", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "斯洛伐克语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "斯洛文尼亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "解除所有项目", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "解除所有不带通知的宏", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "使用“保存\"按钮同步你和其他人的修改", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "瑞典语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "土耳其语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "乌克兰语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "使用键盘上的Alt键导航至用户界面", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "使用键盘上的Option键导航至用户界面", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "越南语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "显示通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "解除所有带通知的宏", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "仿照 Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "工作空间", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "中文", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "使用密码", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "保护电子表格", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "使用签名", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "编辑电子表格", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "编辑将删除电子表格中的签名。<br>您确定要继续吗？", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此电子表格受密码保护", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "此电子表格需要签名。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效签名已添加到电子表格中。该电子表格已限制编辑。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "电子表格中的某些数字签名无效或无法验证。该电子表格已限制编辑。", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "查看签名", "SSE.Views.FormatRulesEditDlg.fillColor": "填充颜色", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.FormatRulesEditDlg.text2Scales": "双色刻度", "SSE.Views.FormatRulesEditDlg.text3Scales": "三色刻度", "SSE.Views.FormatRulesEditDlg.textAllBorders": "所有边框", "SSE.Views.FormatRulesEditDlg.textAppearance": "条形图外观", "SSE.Views.FormatRulesEditDlg.textApply": "应用于范围", "SSE.Views.FormatRulesEditDlg.textAutomatic": "自动", "SSE.Views.FormatRulesEditDlg.textAxis": "轴", "SSE.Views.FormatRulesEditDlg.textBarDirection": "条形图方向", "SSE.Views.FormatRulesEditDlg.textBold": "粗体", "SSE.Views.FormatRulesEditDlg.textBorder": "边框", "SSE.Views.FormatRulesEditDlg.textBordersColor": "边框颜色", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "边框样式", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "下边框", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "无法添加条件格式", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "单元格中点值", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "内部垂直边框", "SSE.Views.FormatRulesEditDlg.textClear": "清除", "SSE.Views.FormatRulesEditDlg.textColor": "文本颜色", "SSE.Views.FormatRulesEditDlg.textContext": "上下文", "SSE.Views.FormatRulesEditDlg.textCustom": "自定义", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "斜下框线", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "斜上框线", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "请输入一个有效公式。", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "你输入的公式不能求值出数字，日期，时间或字符串。", "SSE.Views.FormatRulesEditDlg.textEmptyText": "输入某一值。", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "输入的值不是有效的数字、日期、时间或字符串", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "{0} 值必须大于 {1} 值。", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "输入{0}到{1}之间的数字。", "SSE.Views.FormatRulesEditDlg.textFill": "填充", "SSE.Views.FormatRulesEditDlg.textFormat": "格式", "SSE.Views.FormatRulesEditDlg.textFormula": "公式", "SSE.Views.FormatRulesEditDlg.textGradient": "渐变", "SSE.Views.FormatRulesEditDlg.textIconLabel": "时间 {0} {1}和", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "时间 {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "当值是", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "一个或多个图标数据区域重叠。<br> 请调整图标数据区域值，以使区域不再重叠。", "SSE.Views.FormatRulesEditDlg.textIconStyle": "图标样式", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "内边框", "SSE.Views.FormatRulesEditDlg.textInvalid": "无效的数据范围。", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "错误！单元格范围无效。", "SSE.Views.FormatRulesEditDlg.textItalic": "斜体", "SSE.Views.FormatRulesEditDlg.textItem": "项目", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "从左到右", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "左边框", "SSE.Views.FormatRulesEditDlg.textLongBar": "最长数据条", "SSE.Views.FormatRulesEditDlg.textMaximum": "最大值", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "最大值点", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "内部水平边框", "SSE.Views.FormatRulesEditDlg.textMidpoint": "中点", "SSE.Views.FormatRulesEditDlg.textMinimum": "最小值", "SSE.Views.FormatRulesEditDlg.textMinpoint": "最小值点", "SSE.Views.FormatRulesEditDlg.textNegative": "负", "SSE.Views.FormatRulesEditDlg.textNewColor": "添加新的自定义颜色", "SSE.Views.FormatRulesEditDlg.textNoBorders": "无边框", "SSE.Views.FormatRulesEditDlg.textNone": "无", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "一个或多个指定值不是有效的百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "指定的 {0} 值为无效的百分数。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "一个或多个指定值不是有效的百分位。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "指定的 {0} 值为无效的百分点值。", "SSE.Views.FormatRulesEditDlg.textOutBorders": "外边框", "SSE.Views.FormatRulesEditDlg.textPercent": "百分比", "SSE.Views.FormatRulesEditDlg.textPercentile": "百分点值", "SSE.Views.FormatRulesEditDlg.textPosition": "位置", "SSE.Views.FormatRulesEditDlg.textPositive": "正", "SSE.Views.FormatRulesEditDlg.textPresets": "预置", "SSE.Views.FormatRulesEditDlg.textPreview": "预览", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "在用于色阶、数据栏和图标设置的格式条件中不能使用相对引用。", "SSE.Views.FormatRulesEditDlg.textReverse": "倒转图标顺序", "SSE.Views.FormatRulesEditDlg.textRight2Left": "从右到左", "SSE.Views.FormatRulesEditDlg.textRightBorders": "右边框", "SSE.Views.FormatRulesEditDlg.textRule": "规则", "SSE.Views.FormatRulesEditDlg.textSameAs": "与正值相同", "SSE.Views.FormatRulesEditDlg.textSelectData": "选择数据", "SSE.Views.FormatRulesEditDlg.textShortBar": "最短数据条", "SSE.Views.FormatRulesEditDlg.textShowBar": "仅显示数据条", "SSE.Views.FormatRulesEditDlg.textShowIcon": "仅显示图标", "SSE.Views.FormatRulesEditDlg.textSingleRef": "无法在条件格式公式中使用此引用类型。<br> 更改单个单元格的引用，或使用带工作表函数的引用，例如 =SUM(A1:B5)。", "SSE.Views.FormatRulesEditDlg.textSolid": "实线", "SSE.Views.FormatRulesEditDlg.textStrikeout": "删除线", "SSE.Views.FormatRulesEditDlg.textSubscript": "下标", "SSE.Views.FormatRulesEditDlg.textSuperscript": "上标", "SSE.Views.FormatRulesEditDlg.textTopBorders": "上边框", "SSE.Views.FormatRulesEditDlg.textUnderline": "下划线", "SSE.Views.FormatRulesEditDlg.tipBorders": "边框", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "数字格式", "SSE.Views.FormatRulesEditDlg.txtAccounting": "核算", "SSE.Views.FormatRulesEditDlg.txtCurrency": "货币", "SSE.Views.FormatRulesEditDlg.txtDate": "日期", "SSE.Views.FormatRulesEditDlg.txtEmpty": "此字段是必需的", "SSE.Views.FormatRulesEditDlg.txtFraction": "分数", "SSE.Views.FormatRulesEditDlg.txtGeneral": "常规", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "无图标", "SSE.Views.FormatRulesEditDlg.txtNumber": "数字", "SSE.Views.FormatRulesEditDlg.txtPercentage": "百分比", "SSE.Views.FormatRulesEditDlg.txtScientific": "科学型", "SSE.Views.FormatRulesEditDlg.txtText": "文本", "SSE.Views.FormatRulesEditDlg.txtTime": "时间", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "编辑格式规则", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "新建格式规则", "SSE.Views.FormatRulesManagerDlg.guestText": "来宾", "SSE.Views.FormatRulesManagerDlg.lockText": "锁定", "SSE.Views.FormatRulesManagerDlg.text1Above": "标准偏差高于平均值1", "SSE.Views.FormatRulesManagerDlg.text1Below": "标准偏差低于平均值1", "SSE.Views.FormatRulesManagerDlg.text2Above": "标准偏差高于平均值2", "SSE.Views.FormatRulesManagerDlg.text2Below": "标准偏差低于平均值2", "SSE.Views.FormatRulesManagerDlg.text3Above": "标准偏差高于平均值3", "SSE.Views.FormatRulesManagerDlg.text3Below": "标准偏差低于平均值3", "SSE.Views.FormatRulesManagerDlg.textAbove": "高于平均值", "SSE.Views.FormatRulesManagerDlg.textApply": "应用于", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "单元格值开头是", "SSE.Views.FormatRulesManagerDlg.textBelow": "低于平均值", "SSE.Views.FormatRulesManagerDlg.textBetween": "介于{0}到{1}之间", "SSE.Views.FormatRulesManagerDlg.textCellValue": "单元格值", "SSE.Views.FormatRulesManagerDlg.textColorScale": "渐变颜色刻度", "SSE.Views.FormatRulesManagerDlg.textContains": "单元格值包含", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "单元格包含空值", "SSE.Views.FormatRulesManagerDlg.textContainsError": "单元格包含错误", "SSE.Views.FormatRulesManagerDlg.textDelete": "删除", "SSE.Views.FormatRulesManagerDlg.textDown": "下移规则", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "重复值", "SSE.Views.FormatRulesManagerDlg.textEdit": "编辑", "SSE.Views.FormatRulesManagerDlg.textEnds": "单元格值结尾是", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "等于或高于平均值", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "等于或低于平均值", "SSE.Views.FormatRulesManagerDlg.textFormat": "格式", "SSE.Views.FormatRulesManagerDlg.textIconSet": "图标集", "SSE.Views.FormatRulesManagerDlg.textNew": "新", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "不介于{0}到{1}之间", "SSE.Views.FormatRulesManagerDlg.textNotContains": "单元格值不包含", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "单元格不包含空值", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "单元格不包含错误", "SSE.Views.FormatRulesManagerDlg.textRules": "规则", "SSE.Views.FormatRulesManagerDlg.textScope": "显示其格式规则", "SSE.Views.FormatRulesManagerDlg.textSelectData": "选择数据", "SSE.Views.FormatRulesManagerDlg.textSelection": "当前选择", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "此数据透视", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "当前工作表", "SSE.Views.FormatRulesManagerDlg.textThisTable": "当前表", "SSE.Views.FormatRulesManagerDlg.textUnique": "唯一值", "SSE.Views.FormatRulesManagerDlg.textUp": "上移规则", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "该元素正在由另一个用户编辑。", "SSE.Views.FormatRulesManagerDlg.txtTitle": "条件格式", "SSE.Views.FormatSettingsDialog.textCategory": "分类", "SSE.Views.FormatSettingsDialog.textDecimal": "十进制", "SSE.Views.FormatSettingsDialog.textFormat": "格式", "SSE.Views.FormatSettingsDialog.textLinked": "链接到源", "SSE.Views.FormatSettingsDialog.textSeparator": "使用1000分隔符", "SSE.Views.FormatSettingsDialog.textSymbols": "符号", "SSE.Views.FormatSettingsDialog.textTitle": "数字格式", "SSE.Views.FormatSettingsDialog.txtAccounting": "统计", "SSE.Views.FormatSettingsDialog.txtAs10": "十分钟", "SSE.Views.FormatSettingsDialog.txtAs100": "百分之一百", "SSE.Views.FormatSettingsDialog.txtAs16": "十六点", "SSE.Views.FormatSettingsDialog.txtAs2": "一半", "SSE.Views.FormatSettingsDialog.txtAs4": "四（2/4）", "SSE.Views.FormatSettingsDialog.txtAs8": "第八，八分之一的", "SSE.Views.FormatSettingsDialog.txtCurrency": "货币", "SSE.Views.FormatSettingsDialog.txtCustom": "自定义", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "请仔细输入自定义数字格式。电子表格编辑器不检查自定义格式的错误，可能会影响xlsx的文件。", "SSE.Views.FormatSettingsDialog.txtDate": "日期", "SSE.Views.FormatSettingsDialog.txtFraction": "分数", "SSE.Views.FormatSettingsDialog.txtGeneral": "常规", "SSE.Views.FormatSettingsDialog.txtNone": "无", "SSE.Views.FormatSettingsDialog.txtNumber": "数", "SSE.Views.FormatSettingsDialog.txtPercentage": "百分比", "SSE.Views.FormatSettingsDialog.txtSample": "样品：", "SSE.Views.FormatSettingsDialog.txtScientific": "科学", "SSE.Views.FormatSettingsDialog.txtText": "文本", "SSE.Views.FormatSettingsDialog.txtTime": "时间", "SSE.Views.FormatSettingsDialog.txtUpto1": "最多一位数（1/3）", "SSE.Views.FormatSettingsDialog.txtUpto2": "最多两位数（12/25）", "SSE.Views.FormatSettingsDialog.txtUpto3": "最多三位数（131/135）", "SSE.Views.FormulaDialog.sDescription": "描述", "SSE.Views.FormulaDialog.textGroupDescription": "选择功能分组", "SSE.Views.FormulaDialog.textListDescription": "选择功能", "SSE.Views.FormulaDialog.txtRecommended": "推荐", "SSE.Views.FormulaDialog.txtSearch": "搜索", "SSE.Views.FormulaDialog.txtTitle": "插入功能", "SSE.Views.FormulaTab.textAutomatic": "自动", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "计算当前工作表", "SSE.Views.FormulaTab.textCalculateWorkbook": "计算工作簿", "SSE.Views.FormulaTab.textManual": "手动更改", "SSE.Views.FormulaTab.tipCalculate": "计算", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "计算整个工作簿", "SSE.Views.FormulaTab.tipWatch": "将单元格添加到监视窗口列表", "SSE.Views.FormulaTab.txtAdditional": "另外", "SSE.Views.FormulaTab.txtAutosum": "自动求和", "SSE.Views.FormulaTab.txtAutosumTip": "总和", "SSE.Views.FormulaTab.txtCalculation": "计算", "SSE.Views.FormulaTab.txtFormula": "功能", "SSE.Views.FormulaTab.txtFormulaTip": "插入功能", "SSE.Views.FormulaTab.txtMore": "更多功能", "SSE.Views.FormulaTab.txtRecent": "最近使用的", "SSE.Views.FormulaTab.txtWatch": "监视窗口", "SSE.Views.FormulaWizard.textAny": "任何", "SSE.Views.FormulaWizard.textArgument": "参数", "SSE.Views.FormulaWizard.textFunction": "函数", "SSE.Views.FormulaWizard.textFunctionRes": "函数值", "SSE.Views.FormulaWizard.textHelp": "关于此函数的帮助", "SSE.Views.FormulaWizard.textLogical": "合乎逻辑", "SSE.Views.FormulaWizard.textNoArgs": "此函数无参数", "SSE.Views.FormulaWizard.textNumber": "数值", "SSE.Views.FormulaWizard.textRef": "引用", "SSE.Views.FormulaWizard.textText": "文本", "SSE.Views.FormulaWizard.textTitle": "函数输入值", "SSE.Views.FormulaWizard.textValue": "公式结果", "SSE.Views.HeaderFooterDialog.textAlign": "与页边距对齐", "SSE.Views.HeaderFooterDialog.textAll": "所有页面", "SSE.Views.HeaderFooterDialog.textBold": "加粗", "SSE.Views.HeaderFooterDialog.textCenter": "中心", "SSE.Views.HeaderFooterDialog.textColor": "文字颜色", "SSE.Views.HeaderFooterDialog.textDate": "日期", "SSE.Views.HeaderFooterDialog.textDiffFirst": "不同的第一页", "SSE.Views.HeaderFooterDialog.textDiffOdd": "不同的奇数页和偶数页", "SSE.Views.HeaderFooterDialog.textEven": "偶数页", "SSE.Views.HeaderFooterDialog.textFileName": "文件名", "SSE.Views.HeaderFooterDialog.textFirst": "首页", "SSE.Views.HeaderFooterDialog.textFooter": "页脚", "SSE.Views.HeaderFooterDialog.textHeader": "页眉", "SSE.Views.HeaderFooterDialog.textInsert": "插入", "SSE.Views.HeaderFooterDialog.textItalic": "斜体", "SSE.Views.HeaderFooterDialog.textLeft": "左", "SSE.Views.HeaderFooterDialog.textMaxError": "您输入的文本字符串太长。减少使用的字符数。", "SSE.Views.HeaderFooterDialog.textNewColor": "添加新的自定义颜色", "SSE.Views.HeaderFooterDialog.textOdd": "奇数页", "SSE.Views.HeaderFooterDialog.textPageCount": "页数", "SSE.Views.HeaderFooterDialog.textPageNum": "页码", "SSE.Views.HeaderFooterDialog.textPresets": "预设", "SSE.Views.HeaderFooterDialog.textRight": "右", "SSE.Views.HeaderFooterDialog.textScale": "按文档缩放", "SSE.Views.HeaderFooterDialog.textSheet": "工作表名称", "SSE.Views.HeaderFooterDialog.textStrikeout": "删除线", "SSE.Views.HeaderFooterDialog.textSubscript": "下标", "SSE.Views.HeaderFooterDialog.textSuperscript": "上标", "SSE.Views.HeaderFooterDialog.textTime": "时间", "SSE.Views.HeaderFooterDialog.textTitle": "页眉/页脚设置", "SSE.Views.HeaderFooterDialog.textUnderline": "下划线", "SSE.Views.HeaderFooterDialog.tipFontName": "字体 ", "SSE.Views.HeaderFooterDialog.tipFontSize": "字体大小", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "展示", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "链接到", "SSE.Views.HyperlinkSettingsDialog.strRange": "范围", "SSE.Views.HyperlinkSettingsDialog.strSheet": "表格", "SSE.Views.HyperlinkSettingsDialog.textCopy": "拷贝", "SSE.Views.HyperlinkSettingsDialog.textDefault": "选择范围", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "在这里输入标题", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "在这里输入链接", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "在这里输入工具提示", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "外部链接", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "获取链接", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "内部数据范围", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.HyperlinkSettingsDialog.textNames": "已定义名称", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "选择数据", "SSE.Views.HyperlinkSettingsDialog.textSheets": "表单", "SSE.Views.HyperlinkSettingsDialog.textTipText": "屏幕提示文字", "SSE.Views.HyperlinkSettingsDialog.textTitle": "超链接设置", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "这是必填栏", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此栏位限2083字符", "SSE.Views.ImageSettings.textAdvanced": "显示高级设置", "SSE.Views.ImageSettings.textCrop": "裁剪", "SSE.Views.ImageSettings.textCropFill": "填满", "SSE.Views.ImageSettings.textCropFit": "最佳", "SSE.Views.ImageSettings.textCropToShape": "裁剪为形状", "SSE.Views.ImageSettings.textEdit": "修改", "SSE.Views.ImageSettings.textEditObject": "编辑对象", "SSE.Views.ImageSettings.textFlip": "翻转", "SSE.Views.ImageSettings.textFromFile": "从文件导入", "SSE.Views.ImageSettings.textFromStorage": "来自存储设备", "SSE.Views.ImageSettings.textFromUrl": "从URL", "SSE.Views.ImageSettings.textHeight": "高低", "SSE.Views.ImageSettings.textHint270": "逆时针旋转90°", "SSE.Views.ImageSettings.textHint90": "顺时针旋转90°", "SSE.Views.ImageSettings.textHintFlipH": "水平翻转", "SSE.Views.ImageSettings.textHintFlipV": "垂直翻转", "SSE.Views.ImageSettings.textInsert": "替换图像", "SSE.Views.ImageSettings.textKeepRatio": "不变比例", "SSE.Views.ImageSettings.textOriginalSize": "实际大小", "SSE.Views.ImageSettings.textRecentlyUsed": "最近使用的", "SSE.Views.ImageSettings.textRotate90": "旋转90°", "SSE.Views.ImageSettings.textRotation": "旋转", "SSE.Views.ImageSettings.textSize": "大小", "SSE.Views.ImageSettings.textWidth": "宽度", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "不要移动或调整单元格大小", "SSE.Views.ImageSettingsAdvanced.textAlt": "可选文本", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ImageSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "标题", "SSE.Views.ImageSettingsAdvanced.textAngle": "角度", "SSE.Views.ImageSettingsAdvanced.textFlipped": "已翻转", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "水平", "SSE.Views.ImageSettingsAdvanced.textOneCell": "移动但不按单元格大小调整", "SSE.Views.ImageSettingsAdvanced.textRotation": "旋转", "SSE.Views.ImageSettingsAdvanced.textSnap": "单元捕捉", "SSE.Views.ImageSettingsAdvanced.textTitle": "图片 - 高级设置", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "随单元格移动和调整大小", "SSE.Views.ImageSettingsAdvanced.textVertically": "垂直", "SSE.Views.LeftMenu.tipAbout": "关于", "SSE.Views.LeftMenu.tipChat": "聊天", "SSE.Views.LeftMenu.tipComments": "批注", "SSE.Views.LeftMenu.tipFile": "文件", "SSE.Views.LeftMenu.tipPlugins": "插件", "SSE.Views.LeftMenu.tipSearch": "搜索", "SSE.Views.LeftMenu.tipSpellcheck": "拼写检查", "SSE.Views.LeftMenu.tipSupport": "反馈和支持", "SSE.Views.LeftMenu.txtDeveloper": "开发者模式", "SSE.Views.LeftMenu.txtEditor": "表格编辑器", "SSE.Views.LeftMenu.txtLimit": "限制访问", "SSE.Views.LeftMenu.txtTrial": "试用模式", "SSE.Views.LeftMenu.txtTrialDev": "试用开发者模式", "SSE.Views.MacroDialog.textMacro": "宏名称", "SSE.Views.MacroDialog.textTitle": "指定宏", "SSE.Views.MainSettingsPrint.okButtonText": "保存", "SSE.Views.MainSettingsPrint.strBottom": "底部", "SSE.Views.MainSettingsPrint.strLandscape": "横向", "SSE.Views.MainSettingsPrint.strLeft": "左", "SSE.Views.MainSettingsPrint.strMargins": "边距", "SSE.Views.MainSettingsPrint.strPortrait": "纵向", "SSE.Views.MainSettingsPrint.strPrint": "打印", "SSE.Views.MainSettingsPrint.strPrintTitles": "打印标题", "SSE.Views.MainSettingsPrint.strRight": "右", "SSE.Views.MainSettingsPrint.strTop": "顶部", "SSE.Views.MainSettingsPrint.textActualSize": "实际大小", "SSE.Views.MainSettingsPrint.textCustom": "自定义", "SSE.Views.MainSettingsPrint.textCustomOptions": "自定义选项", "SSE.Views.MainSettingsPrint.textFitCols": "将所有列适合一页", "SSE.Views.MainSettingsPrint.textFitPage": "在一页上安装工作表", "SSE.Views.MainSettingsPrint.textFitRows": "适合一页上的所有行", "SSE.Views.MainSettingsPrint.textPageOrientation": "页面方向", "SSE.Views.MainSettingsPrint.textPageScaling": "缩放", "SSE.Views.MainSettingsPrint.textPageSize": "页面大小", "SSE.Views.MainSettingsPrint.textPrintGrid": "打印网格线", "SSE.Views.MainSettingsPrint.textPrintHeadings": "打印行和列标题", "SSE.Views.MainSettingsPrint.textRepeat": "重复...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "在左侧重复一列", "SSE.Views.MainSettingsPrint.textRepeatTop": "在顶部重复一行", "SSE.Views.MainSettingsPrint.textSettings": "设置", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "无法编辑现有的命名范围，因此无法在其中编辑新的范围。", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "定义名称", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.NamedRangeEditDlg.strWorkbook": "工作簿", "SSE.Views.NamedRangeEditDlg.textDataRange": "数据范围", "SSE.Views.NamedRangeEditDlg.textExistName": "错误！具有这种名称的范围已经存在", "SSE.Views.NamedRangeEditDlg.textInvalidName": "名称必须以字母或下划线开头，不能包含无效字符。", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.NamedRangeEditDlg.textIsLocked": "错误！此元素正在被其他用户编辑。", "SSE.Views.NamedRangeEditDlg.textName": "名称", "SSE.Views.NamedRangeEditDlg.textReservedName": "您尝试使用的名称已在单元格公式中引用。请使用其他名称。", "SSE.Views.NamedRangeEditDlg.textScope": "范围", "SSE.Views.NamedRangeEditDlg.textSelectData": "选择数据", "SSE.Views.NamedRangeEditDlg.txtEmpty": "这是必填栏", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "编辑名称", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "新名称", "SSE.Views.NamedRangePasteDlg.textNames": "命名范围", "SSE.Views.NamedRangePasteDlg.txtTitle": "粘贴名称", "SSE.Views.NameManagerDlg.closeButtonText": "关闭", "SSE.Views.NameManagerDlg.guestText": "游客", "SSE.Views.NameManagerDlg.lockText": "锁定", "SSE.Views.NameManagerDlg.textDataRange": "数据范围", "SSE.Views.NameManagerDlg.textDelete": "删除", "SSE.Views.NameManagerDlg.textEdit": "编辑", "SSE.Views.NameManagerDlg.textEmpty": "没有创建命名范围。<br>创建至少一个命名范围，它将显示在此字段中。", "SSE.Views.NameManagerDlg.textFilter": "过滤", "SSE.Views.NameManagerDlg.textFilterAll": "所有", "SSE.Views.NameManagerDlg.textFilterDefNames": "定义名称", "SSE.Views.NameManagerDlg.textFilterSheet": "名称范围为表格", "SSE.Views.NameManagerDlg.textFilterTableNames": "表名", "SSE.Views.NameManagerDlg.textFilterWorkbook": "名称范围到工作簿", "SSE.Views.NameManagerDlg.textNew": "新", "SSE.Views.NameManagerDlg.textnoNames": "没有找到与您的过滤器匹配的命名范围", "SSE.Views.NameManagerDlg.textRanges": "命名范围", "SSE.Views.NameManagerDlg.textScope": "范围", "SSE.Views.NameManagerDlg.textWorkbook": "工作簿", "SSE.Views.NameManagerDlg.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.NameManagerDlg.txtTitle": "名称管理", "SSE.Views.NameManagerDlg.warnDelete": "您确定要删除{0}这个名字吗？", "SSE.Views.PageMarginsDialog.textBottom": "底部", "SSE.Views.PageMarginsDialog.textLeft": "左", "SSE.Views.PageMarginsDialog.textRight": "对", "SSE.Views.PageMarginsDialog.textTitle": "边距", "SSE.Views.PageMarginsDialog.textTop": "顶部", "SSE.Views.ParagraphSettings.strLineHeight": "行间距", "SSE.Views.ParagraphSettings.strParagraphSpacing": "段落间距", "SSE.Views.ParagraphSettings.strSpacingAfter": "段后", "SSE.Views.ParagraphSettings.strSpacingBefore": "段前", "SSE.Views.ParagraphSettings.textAdvanced": "显示高级设置", "SSE.Views.ParagraphSettings.textAt": "在", "SSE.Views.ParagraphSettings.textAtLeast": "至少", "SSE.Views.ParagraphSettings.textAuto": "多", "SSE.Views.ParagraphSettings.textExact": "精确地", "SSE.Views.ParagraphSettings.txtAutoText": "自动", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "指定的选项卡将显示在此字段中", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大写字母", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "双删除线", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "缩进", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行间距", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "对", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "段后", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "段前", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊格式", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "由", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字体", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "缩进和间距", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型大写字母", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "间距", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "删除线", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "下标", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "上标", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "标签", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "对齐", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "倍数", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字符间距", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "默认选项", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "SSE.Views.ParagraphSettingsAdvanced.textExact": "精确", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "首行缩进", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "悬挂缩进", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "正当", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（无）", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "删除", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "删除所有", "SSE.Views.ParagraphSettingsAdvanced.textSet": "指定", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "标签的位置", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 高级设置", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "自动", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "等于", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "不结束于", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "包含", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "不含", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "介于", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "不介于", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "不等于", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "大于", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "大于或等于", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "小于", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "小于或等于", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "开头为", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "不起始于", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "结束于", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "显示标签如下的项目：", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "显示符合以下条件的项目:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "使用 ? 表示任何单个字符", "SSE.Views.PivotDigitalFilterDialog.textUse2": "使用 * 表示任何系列字符", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "且", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "标签过滤器", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "按值筛选", "SSE.Views.PivotGroupDialog.textAuto": "自动", "SSE.Views.PivotGroupDialog.textBy": "依据", "SSE.Views.PivotGroupDialog.textDays": "天", "SSE.Views.PivotGroupDialog.textEnd": "终止于", "SSE.Views.PivotGroupDialog.textError": "此字段必须为数值。", "SSE.Views.PivotGroupDialog.textGreaterError": "结尾数必须大于开始数", "SSE.Views.PivotGroupDialog.textHour": "小时", "SSE.Views.PivotGroupDialog.textMin": "分钟", "SSE.Views.PivotGroupDialog.textMonth": "月", "SSE.Views.PivotGroupDialog.textNumDays": "天数", "SSE.Views.PivotGroupDialog.textQuart": "季度", "SSE.Views.PivotGroupDialog.textSec": "秒", "SSE.Views.PivotGroupDialog.textStart": "起始于", "SSE.Views.PivotGroupDialog.textYear": "年", "SSE.Views.PivotGroupDialog.txtTitle": "组合", "SSE.Views.PivotSettings.textAdvanced": "显示高级设置", "SSE.Views.PivotSettings.textColumns": "列", "SSE.Views.PivotSettings.textFields": "选择字段", "SSE.Views.PivotSettings.textFilters": "过滤", "SSE.Views.PivotSettings.textRows": "行", "SSE.Views.PivotSettings.textValues": "值", "SSE.Views.PivotSettings.txtAddColumn": "添加到列", "SSE.Views.PivotSettings.txtAddFilter": "添加到过滤", "SSE.Views.PivotSettings.txtAddRow": "添加到行", "SSE.Views.PivotSettings.txtAddValues": "添加到值", "SSE.Views.PivotSettings.txtFieldSettings": "字段设置", "SSE.Views.PivotSettings.txtMoveBegin": "移至开头", "SSE.Views.PivotSettings.txtMoveColumn": "移至列", "SSE.Views.PivotSettings.txtMoveDown": "下移", "SSE.Views.PivotSettings.txtMoveEnd": "移动到末尾", "SSE.Views.PivotSettings.txtMoveFilter": "移至筛选器", "SSE.Views.PivotSettings.txtMoveRow": "移至行", "SSE.Views.PivotSettings.txtMoveUp": "上移", "SSE.Views.PivotSettings.txtMoveValues": "移至值", "SSE.Views.PivotSettings.txtRemove": "删除字段", "SSE.Views.PivotSettingsAdvanced.strLayout": "名称与布局", "SSE.Views.PivotSettingsAdvanced.textAlt": "备选文本", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "说明", "SSE.Views.PivotSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示形式，将向有视力或认知障碍的人读取，以帮助他们更好地理解图像，自动成型，图表或表格中包含的信息。", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "标题", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "更新时自动调整列宽", "SSE.Views.PivotSettingsAdvanced.textDataRange": "数据范围", "SSE.Views.PivotSettingsAdvanced.textDataSource": "数据来源", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "解除过滤器位置的域", "SSE.Views.PivotSettingsAdvanced.textDown": "先下再上", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "总计", "SSE.Views.PivotSettingsAdvanced.textHeaders": "域的题头", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "失败！单元格范围无效", "SSE.Views.PivotSettingsAdvanced.textOver": "先上再下", "SSE.Views.PivotSettingsAdvanced.textSelectData": "选择数据", "SSE.Views.PivotSettingsAdvanced.textShowCols": "列的显示", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "显示行和列的字段标题", "SSE.Views.PivotSettingsAdvanced.textShowRows": "行的显示", "SSE.Views.PivotSettingsAdvanced.textTitle": "数据透视表 - 进阶设定", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "报告每列的过滤域", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "报告每行的过滤域", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "这是必填栏", "SSE.Views.PivotSettingsAdvanced.txtName": "名称", "SSE.Views.PivotTable.capBlankRows": "空白行", "SSE.Views.PivotTable.capGrandTotals": "总计", "SSE.Views.PivotTable.capLayout": "报告版式", "SSE.Views.PivotTable.capSubtotals": "分类汇总", "SSE.Views.PivotTable.mniBottomSubtotals": "在组的底部显示所有分类汇总", "SSE.Views.PivotTable.mniInsertBlankLine": "在每个项目后插入空行", "SSE.Views.PivotTable.mniLayoutCompact": "以紧凑形式显示", "SSE.Views.PivotTable.mniLayoutNoRepeat": "不重复所有项目标签", "SSE.Views.PivotTable.mniLayoutOutline": "以大纲形式显示", "SSE.Views.PivotTable.mniLayoutRepeat": "重复所有项目标签", "SSE.Views.PivotTable.mniLayoutTabular": "以表格形式显示", "SSE.Views.PivotTable.mniNoSubtotals": "不显示分类汇总", "SSE.Views.PivotTable.mniOffTotals": "对行和列禁用", "SSE.Views.PivotTable.mniOnColumnsTotals": "仅对列启用", "SSE.Views.PivotTable.mniOnRowsTotals": "仅对行启用", "SSE.Views.PivotTable.mniOnTotals": "对行和列启用", "SSE.Views.PivotTable.mniRemoveBlankLine": "删除每个项目后的空行", "SSE.Views.PivotTable.mniTopSubtotals": "在组的顶部显示所有分类汇总", "SSE.Views.PivotTable.textColBanded": "镶边列", "SSE.Views.PivotTable.textColHeader": "列标题", "SSE.Views.PivotTable.textRowBanded": "镶边行", "SSE.Views.PivotTable.textRowHeader": "行标题", "SSE.Views.PivotTable.tipCreatePivot": "插入透视表", "SSE.Views.PivotTable.tipGrandTotals": "显示或隐藏总计", "SSE.Views.PivotTable.tipRefresh": "更新数据源中的信息", "SSE.Views.PivotTable.tipSelect": "选择整张透视表", "SSE.Views.PivotTable.tipSubtotals": "显示或隐藏分类汇总", "SSE.Views.PivotTable.txtCreate": "插入表", "SSE.Views.PivotTable.txtGroupPivot_Custom": "自定义", "SSE.Views.PivotTable.txtGroupPivot_Dark": "深色的", "SSE.Views.PivotTable.txtGroupPivot_Light": "浅色的", "SSE.Views.PivotTable.txtGroupPivot_Medium": "中等", "SSE.Views.PivotTable.txtPivotTable": "数据透视表", "SSE.Views.PivotTable.txtRefresh": "刷新", "SSE.Views.PivotTable.txtSelect": "选择", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "数据透视表样式深色", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "数据透视表样式浅色", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "数据透视表样式中等深浅", "SSE.Views.PrintSettings.btnDownload": "保存并下载", "SSE.Views.PrintSettings.btnPrint": "保存并打印", "SSE.Views.PrintSettings.strBottom": "底部", "SSE.Views.PrintSettings.strLandscape": "横向", "SSE.Views.PrintSettings.strLeft": "左", "SSE.Views.PrintSettings.strMargins": "边距", "SSE.Views.PrintSettings.strPortrait": "肖像", "SSE.Views.PrintSettings.strPrint": "打印", "SSE.Views.PrintSettings.strPrintTitles": "打印标题", "SSE.Views.PrintSettings.strRight": "右", "SSE.Views.PrintSettings.strShow": "显示", "SSE.Views.PrintSettings.strTop": "顶部", "SSE.Views.PrintSettings.textActualSize": "实际大小", "SSE.Views.PrintSettings.textAllSheets": "所有表格", "SSE.Views.PrintSettings.textCurrentSheet": "当前工作表", "SSE.Views.PrintSettings.textCustom": "自定义", "SSE.Views.PrintSettings.textCustomOptions": "自定义选项", "SSE.Views.PrintSettings.textFitCols": "将所有列适合一页", "SSE.Views.PrintSettings.textFitPage": "在一页上安装工作表", "SSE.Views.PrintSettings.textFitRows": "适合一页上的所有行", "SSE.Views.PrintSettings.textHideDetails": "隐藏细节", "SSE.Views.PrintSettings.textIgnore": "忽略打印区域", "SSE.Views.PrintSettings.textLayout": "布局", "SSE.Views.PrintSettings.textPageOrientation": "页面方向", "SSE.Views.PrintSettings.textPageScaling": "缩放", "SSE.Views.PrintSettings.textPageSize": "页面大小", "SSE.Views.PrintSettings.textPrintGrid": "打印网格线", "SSE.Views.PrintSettings.textPrintHeadings": "打印行和列标题", "SSE.Views.PrintSettings.textPrintRange": "打印范围", "SSE.Views.PrintSettings.textRange": "范围", "SSE.Views.PrintSettings.textRepeat": "重复...", "SSE.Views.PrintSettings.textRepeatLeft": "在左侧重复一列", "SSE.Views.PrintSettings.textRepeatTop": "在顶部重复一行", "SSE.Views.PrintSettings.textSelection": "选择", "SSE.Views.PrintSettings.textSettings": "工作表设置", "SSE.Views.PrintSettings.textShowDetails": "显示详细资料", "SSE.Views.PrintSettings.textShowGrid": "显示网格线", "SSE.Views.PrintSettings.textShowHeadings": "显示行和列标题", "SSE.Views.PrintSettings.textTitle": "打印设置", "SSE.Views.PrintSettings.textTitlePDF": "PDF设置", "SSE.Views.PrintTitlesDialog.textFirstCol": "第一列", "SSE.Views.PrintTitlesDialog.textFirstRow": "第一行", "SSE.Views.PrintTitlesDialog.textFrozenCols": "固定列", "SSE.Views.PrintTitlesDialog.textFrozenRows": "固定行", "SSE.Views.PrintTitlesDialog.textInvalidRange": "失败！单元格范围无效", "SSE.Views.PrintTitlesDialog.textLeft": "在左侧重复一列", "SSE.Views.PrintTitlesDialog.textNoRepeat": "不重复", "SSE.Views.PrintTitlesDialog.textRepeat": "重复...", "SSE.Views.PrintTitlesDialog.textSelectRange": "选取范围", "SSE.Views.PrintTitlesDialog.textTitle": "打印标题", "SSE.Views.PrintTitlesDialog.textTop": "在顶部重复一行", "SSE.Views.PrintWithPreview.txtActualSize": "实际大小", "SSE.Views.PrintWithPreview.txtAllSheets": "全部工作表", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "应用到所有工作表", "SSE.Views.PrintWithPreview.txtBottom": "底部", "SSE.Views.PrintWithPreview.txtCurrentSheet": "当前工作表", "SSE.Views.PrintWithPreview.txtCustom": "自定义", "SSE.Views.PrintWithPreview.txtCustomOptions": "自定义选项", "SSE.Views.PrintWithPreview.txtEmptyTable": "没有什么可打印的，表为空", "SSE.Views.PrintWithPreview.txtFitCols": "将所有列适合一页", "SSE.Views.PrintWithPreview.txtFitPage": "在一页上安装工作表", "SSE.Views.PrintWithPreview.txtFitRows": "适合一页上的所有行", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "网格线和标题", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "页眉/页脚设置", "SSE.Views.PrintWithPreview.txtIgnore": "忽略打印区域", "SSE.Views.PrintWithPreview.txtLandscape": "横向", "SSE.Views.PrintWithPreview.txtLeft": "左侧", "SSE.Views.PrintWithPreview.txtMargins": "边距", "SSE.Views.PrintWithPreview.txtOf": "/ {0}", "SSE.Views.PrintWithPreview.txtPage": "页面", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "页码无效", "SSE.Views.PrintWithPreview.txtPageOrientation": "页面方向", "SSE.Views.PrintWithPreview.txtPageSize": "页面大小", "SSE.Views.PrintWithPreview.txtPortrait": "纵向", "SSE.Views.PrintWithPreview.txtPrint": "打印", "SSE.Views.PrintWithPreview.txtPrintGrid": "打印网格线", "SSE.Views.PrintWithPreview.txtPrintHeadings": "打印行和列标题", "SSE.Views.PrintWithPreview.txtPrintRange": "打印范围", "SSE.Views.PrintWithPreview.txtPrintTitles": "打印标题", "SSE.Views.PrintWithPreview.txtRepeat": "重复...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "在左侧重复一列", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "在顶部重复一行", "SSE.Views.PrintWithPreview.txtRight": "右侧", "SSE.Views.PrintWithPreview.txtSave": "保存", "SSE.Views.PrintWithPreview.txtScaling": "缩放", "SSE.Views.PrintWithPreview.txtSelection": "选择", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "工作表设置", "SSE.Views.PrintWithPreview.txtSheet": "工作表：{0}", "SSE.Views.PrintWithPreview.txtTop": "顶部", "SSE.Views.ProtectDialog.textExistName": "错误！一个有此标题的区域已经存在", "SSE.Views.ProtectDialog.textInvalidName": "范围标准必须为字母起头而只能包含数字、字母和空格。", "SSE.Views.ProtectDialog.textInvalidRange": "错误！单元格范围无效。", "SSE.Views.ProtectDialog.textSelectData": "选择数据", "SSE.Views.ProtectDialog.txtAllow": "允许该表的所有用户", "SSE.Views.ProtectDialog.txtAutofilter": "使用自动筛选", "SSE.Views.ProtectDialog.txtDelCols": "删除列", "SSE.Views.ProtectDialog.txtDelRows": "删除行​​", "SSE.Views.ProtectDialog.txtEmpty": "此字段是必需的", "SSE.Views.ProtectDialog.txtFormatCells": "设置单元格格式", "SSE.Views.ProtectDialog.txtFormatCols": "设置列格式", "SSE.Views.ProtectDialog.txtFormatRows": "设置行格式", "SSE.Views.ProtectDialog.txtIncorrectPwd": "确认的密码与先前输入的不一致。", "SSE.Views.ProtectDialog.txtInsCols": "插入列", "SSE.Views.ProtectDialog.txtInsHyper": "插入超链接", "SSE.Views.ProtectDialog.txtInsRows": "插入行", "SSE.Views.ProtectDialog.txtObjs": "编辑对象", "SSE.Views.ProtectDialog.txtOptional": "可选", "SSE.Views.ProtectDialog.txtPassword": "密码", "SSE.Views.ProtectDialog.txtPivot": "使用数据透视表和数据透视图", "SSE.Views.ProtectDialog.txtProtect": "保护", "SSE.Views.ProtectDialog.txtRange": "区域", "SSE.Views.ProtectDialog.txtRangeName": "标题", "SSE.Views.ProtectDialog.txtRepeat": "重复输入密码", "SSE.Views.ProtectDialog.txtScen": "编辑方案", "SSE.Views.ProtectDialog.txtSelLocked": "选定锁定的单元格", "SSE.Views.ProtectDialog.txtSelUnLocked": "选择未锁定的单元格", "SSE.Views.ProtectDialog.txtSheetDescription": "通过限制他人的编辑能力，防止他人进行不必要的修改。", "SSE.Views.ProtectDialog.txtSheetTitle": "保护工作表", "SSE.Views.ProtectDialog.txtSort": "排序", "SSE.Views.ProtectDialog.txtWarning": "警告: 如果丢失或忘记密码，则无法将其恢复。请妥善保存。", "SSE.Views.ProtectDialog.txtWBDescription": "为防止其他用户查看隐藏的工作表，添加、移动、删除或隐藏工作表和重命名工作表，您可以设定密码保护工作表架构。", "SSE.Views.ProtectDialog.txtWBTitle": "保护工作簿结构", "SSE.Views.ProtectRangesDlg.guestText": "来宾", "SSE.Views.ProtectRangesDlg.lockText": "锁定", "SSE.Views.ProtectRangesDlg.textDelete": "删除", "SSE.Views.ProtectRangesDlg.textEdit": "编辑", "SSE.Views.ProtectRangesDlg.textEmpty": "没有可编辑的区域。", "SSE.Views.ProtectRangesDlg.textNew": "新", "SSE.Views.ProtectRangesDlg.textProtect": "保护工作表", "SSE.Views.ProtectRangesDlg.textPwd": "密码", "SSE.Views.ProtectRangesDlg.textRange": "区域", "SSE.Views.ProtectRangesDlg.textRangesDesc": "工作表受保护时，范围为密码解锁 (只适用于锁定的单元格)", "SSE.Views.ProtectRangesDlg.textTitle": "标题", "SSE.Views.ProtectRangesDlg.tipIsLocked": "该元素正在由另一个用户编辑。", "SSE.Views.ProtectRangesDlg.txtEditRange": "编辑范围", "SSE.Views.ProtectRangesDlg.txtNewRange": "新区域", "SSE.Views.ProtectRangesDlg.txtNo": "否", "SSE.Views.ProtectRangesDlg.txtTitle": "允许用户编辑范围", "SSE.Views.ProtectRangesDlg.txtYes": "是", "SSE.Views.ProtectRangesDlg.warnDelete": "您确定要删除{0}这个名字吗？", "SSE.Views.RemoveDuplicatesDialog.textColumns": "列", "SSE.Views.RemoveDuplicatesDialog.textDescription": "若要删除重复值，请选择一个或多个包含重复值的列。", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "我的数据有题头", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "全选", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "移除重复的元素", "SSE.Views.RightMenu.txtCellSettings": "单元格设置", "SSE.Views.RightMenu.txtChartSettings": "图表设置", "SSE.Views.RightMenu.txtImageSettings": "图像设置", "SSE.Views.RightMenu.txtParagraphSettings": "段落设置", "SSE.Views.RightMenu.txtPivotSettings": "数据透视表设置", "SSE.Views.RightMenu.txtSettings": "通用设置", "SSE.Views.RightMenu.txtShapeSettings": "形状设置", "SSE.Views.RightMenu.txtSignatureSettings": "签名设置", "SSE.Views.RightMenu.txtSlicerSettings": "切片器设置", "SSE.Views.RightMenu.txtSparklineSettings": "迷你图设置", "SSE.Views.RightMenu.txtTableSettings": "表设置", "SSE.Views.RightMenu.txtTextArtSettings": "艺术字设置", "SSE.Views.ScaleDialog.textAuto": "自动", "SSE.Views.ScaleDialog.textError": "输入的值不正确。", "SSE.Views.ScaleDialog.textFewPages": "页面", "SSE.Views.ScaleDialog.textFitTo": "适应于", "SSE.Views.ScaleDialog.textHeight": "高度", "SSE.Views.ScaleDialog.textManyPages": "页面", "SSE.Views.ScaleDialog.textOnePage": "页面", "SSE.Views.ScaleDialog.textScaleTo": "缩放到", "SSE.Views.ScaleDialog.textTitle": "缩放设置", "SSE.Views.ScaleDialog.textWidth": "宽度", "SSE.Views.SetValueDialog.txtMaxText": "该字段的最大值为{0}", "SSE.Views.SetValueDialog.txtMinText": "该字段的最小值为{0}", "SSE.Views.ShapeSettings.strBackground": "背景颜色", "SSE.Views.ShapeSettings.strChange": "更改自动形状", "SSE.Views.ShapeSettings.strColor": "颜色", "SSE.Views.ShapeSettings.strFill": "填满", "SSE.Views.ShapeSettings.strForeground": "前景色", "SSE.Views.ShapeSettings.strPattern": "模式", "SSE.Views.ShapeSettings.strShadow": "显示阴影", "SSE.Views.ShapeSettings.strSize": "大小", "SSE.Views.ShapeSettings.strStroke": "线条", "SSE.Views.ShapeSettings.strTransparency": "不透明度", "SSE.Views.ShapeSettings.strType": "类型", "SSE.Views.ShapeSettings.textAdvanced": "显示高级设置", "SSE.Views.ShapeSettings.textAngle": "角度", "SSE.Views.ShapeSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "SSE.Views.ShapeSettings.textColor": "颜色填充", "SSE.Views.ShapeSettings.textDirection": "方向", "SSE.Views.ShapeSettings.textEmptyPattern": "无图案", "SSE.Views.ShapeSettings.textFlip": "翻转", "SSE.Views.ShapeSettings.textFromFile": "从文件导入", "SSE.Views.ShapeSettings.textFromStorage": "来自存储设备", "SSE.Views.ShapeSettings.textFromUrl": "从URL", "SSE.Views.ShapeSettings.textGradient": "渐变点", "SSE.Views.ShapeSettings.textGradientFill": "渐变填充", "SSE.Views.ShapeSettings.textHint270": "逆时针旋转90°", "SSE.Views.ShapeSettings.textHint90": "顺时针旋转90°", "SSE.Views.ShapeSettings.textHintFlipH": "水平翻转", "SSE.Views.ShapeSettings.textHintFlipV": "垂直翻转", "SSE.Views.ShapeSettings.textImageTexture": "图片或纹理", "SSE.Views.ShapeSettings.textLinear": "线性", "SSE.Views.ShapeSettings.textNoFill": "没有填充", "SSE.Views.ShapeSettings.textOriginalSize": "原始尺寸", "SSE.Views.ShapeSettings.textPatternFill": "模式", "SSE.Views.ShapeSettings.textPosition": "位置", "SSE.Views.ShapeSettings.textRadial": "径向", "SSE.Views.ShapeSettings.textRecentlyUsed": "最近使用的", "SSE.Views.ShapeSettings.textRotate90": "旋转90°", "SSE.Views.ShapeSettings.textRotation": "旋转", "SSE.Views.ShapeSettings.textSelectImage": "选取图片", "SSE.Views.ShapeSettings.textSelectTexture": "选择", "SSE.Views.ShapeSettings.textStretch": "伸展", "SSE.Views.ShapeSettings.textStyle": "类型", "SSE.Views.ShapeSettings.textTexture": "从纹理", "SSE.Views.ShapeSettings.textTile": "瓦", "SSE.Views.ShapeSettings.tipAddGradientPoint": "新增渐变点", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "删除渐变点", "SSE.Views.ShapeSettings.txtBrownPaper": "牛皮纸", "SSE.Views.ShapeSettings.txtCanvas": "画布", "SSE.Views.ShapeSettings.txtCarton": "纸板", "SSE.Views.ShapeSettings.txtDarkFabric": "深色织物", "SSE.Views.ShapeSettings.txtGrain": "颗粒", "SSE.Views.ShapeSettings.txtGranite": "花岗岩", "SSE.Views.ShapeSettings.txtGreyPaper": "灰纸", "SSE.Views.ShapeSettings.txtKnit": "针织", "SSE.Views.ShapeSettings.txtLeather": "Leather", "SSE.Views.ShapeSettings.txtNoBorders": "没有线", "SSE.Views.ShapeSettings.txtPapyrus": "纸莎草", "SSE.Views.ShapeSettings.txtWood": "木头", "SSE.Views.ShapeSettingsAdvanced.strColumns": "列", "SSE.Views.ShapeSettingsAdvanced.strMargins": "文字填充", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "不要移动或调整单元格大小", "SSE.Views.ShapeSettingsAdvanced.textAlt": "可选文本", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "标题", "SSE.Views.ShapeSettingsAdvanced.textAngle": "角度", "SSE.Views.ShapeSettingsAdvanced.textArrows": "箭头", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "自动适应", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "初始大小", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "初始风格", "SSE.Views.ShapeSettingsAdvanced.textBevel": "斜角", "SSE.Views.ShapeSettingsAdvanced.textBottom": "底部", "SSE.Views.ShapeSettingsAdvanced.textCapType": "帽类型", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "列数", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "结束尺寸", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "结束样式", "SSE.Views.ShapeSettingsAdvanced.textFlat": "平面", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "已翻转", "SSE.Views.ShapeSettingsAdvanced.textHeight": "高度", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "水平", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "加入类型", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "不变比例", "SSE.Views.ShapeSettingsAdvanced.textLeft": "左", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "线样式", "SSE.Views.ShapeSettingsAdvanced.textMiter": "米特", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "移动但不按单元格大小调整", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "允许文本压上图形", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "调整形状以适应文本", "SSE.Views.ShapeSettingsAdvanced.textRight": "右", "SSE.Views.ShapeSettingsAdvanced.textRotation": "旋转", "SSE.Views.ShapeSettingsAdvanced.textRound": "圆", "SSE.Views.ShapeSettingsAdvanced.textSize": "大小", "SSE.Views.ShapeSettingsAdvanced.textSnap": "单元捕捉", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "列之间的间距", "SSE.Views.ShapeSettingsAdvanced.textSquare": "四周型环绕", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "文本框", "SSE.Views.ShapeSettingsAdvanced.textTitle": "形状 - 高级设置", "SSE.Views.ShapeSettingsAdvanced.textTop": "顶部", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "随单元格移动和调整大小", "SSE.Views.ShapeSettingsAdvanced.textVertically": "垂直", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "重量和箭头", "SSE.Views.ShapeSettingsAdvanced.textWidth": "宽度", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "SSE.Views.SignatureSettings.strDelete": "删除签名", "SSE.Views.SignatureSettings.strDetails": "签名详情", "SSE.Views.SignatureSettings.strInvalid": "签名无效", "SSE.Views.SignatureSettings.strRequested": "请求的签名", "SSE.Views.SignatureSettings.strSetup": "签名设置", "SSE.Views.SignatureSettings.strSign": "符号", "SSE.Views.SignatureSettings.strSignature": "签名", "SSE.Views.SignatureSettings.strSigner": "签名者", "SSE.Views.SignatureSettings.strValid": "有效签名", "SSE.Views.SignatureSettings.txtContinueEditing": "继续编辑", "SSE.Views.SignatureSettings.txtEditWarning": "编辑将删除电子表格中的签名。<br>您确定要继续吗？", "SSE.Views.SignatureSettings.txtRemoveWarning": "您想删除此签名吗? <br>这一操作无法被撤销。", "SSE.Views.SignatureSettings.txtRequestedSignatures": "此电子表格需要签名。", "SSE.Views.SignatureSettings.txtSigned": "有效签名已添加到电子表格中。该电子表格已限制编辑。", "SSE.Views.SignatureSettings.txtSignedInvalid": "电子表格中的某些数字签名无效或无法验证。该电子表格已限制编辑。", "SSE.Views.SlicerAddDialog.textColumns": "列", "SSE.Views.SlicerAddDialog.txtTitle": "插入分法", "SSE.Views.SlicerSettings.strHideNoData": "隐藏没有数据的项目", "SSE.Views.SlicerSettings.strIndNoData": "直观地指示空数据项", "SSE.Views.SlicerSettings.strShowDel": "显示从数据源中删除的项目", "SSE.Views.SlicerSettings.strShowNoData": "最后显示空数据项", "SSE.Views.SlicerSettings.strSorting": "排序和筛选", "SSE.Views.SlicerSettings.textAdvanced": "显示高级设置", "SSE.Views.SlicerSettings.textAsc": "升序", "SSE.Views.SlicerSettings.textAZ": "A到Z", "SSE.Views.SlicerSettings.textButtons": "按钮", "SSE.Views.SlicerSettings.textColumns": "列", "SSE.Views.SlicerSettings.textDesc": "降序", "SSE.Views.SlicerSettings.textHeight": "高度", "SSE.Views.SlicerSettings.textHor": "横向的", "SSE.Views.SlicerSettings.textKeepRatio": "恒同比例", "SSE.Views.SlicerSettings.textLargeSmall": "由大到小", "SSE.Views.SlicerSettings.textLock": "解除移动或改变大小的功能", "SSE.Views.SlicerSettings.textNewOld": "由新到旧", "SSE.Views.SlicerSettings.textOldNew": "由旧到新", "SSE.Views.SlicerSettings.textPosition": "位置", "SSE.Views.SlicerSettings.textSize": "大小", "SSE.Views.SlicerSettings.textSmallLarge": "升序", "SSE.Views.SlicerSettings.textStyle": "样式", "SSE.Views.SlicerSettings.textVert": "垂直", "SSE.Views.SlicerSettings.textWidth": "宽度", "SSE.Views.SlicerSettings.textZA": "Z到A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "按钮", "SSE.Views.SlicerSettingsAdvanced.strColumns": "列", "SSE.Views.SlicerSettingsAdvanced.strHeight": "高度", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "隐藏没有数据的项目", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "直观地指示空数据项", "SSE.Views.SlicerSettingsAdvanced.strReferences": "引用", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "显示从数据源中删除的项目", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "显示题头", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "最后显示空数据项", "SSE.Views.SlicerSettingsAdvanced.strSize": "大小", "SSE.Views.SlicerSettingsAdvanced.strSorting": "排序和筛选", "SSE.Views.SlicerSettingsAdvanced.strStyle": "样式", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "样式和尺寸", "SSE.Views.SlicerSettingsAdvanced.strWidth": "宽度", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "不要移动或调整单元格大小", "SSE.Views.SlicerSettingsAdvanced.textAlt": "备选文本", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "说明", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示形式，将向有视力或认知障碍的人读取，以帮助他们更好地理解图像，自动成型，图表或表格中包含的信息。", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "标题", "SSE.Views.SlicerSettingsAdvanced.textAsc": "升序", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A到Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "降序", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "用在公式里的名称", "SSE.Views.SlicerSettingsAdvanced.textHeader": "题头", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "恒同比例", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "由大到小", "SSE.Views.SlicerSettingsAdvanced.textName": "名称", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "由新到旧", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "由旧到新", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "移动但不按单元格大小调整", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "升序", "SSE.Views.SlicerSettingsAdvanced.textSnap": "单元捕捉", "SSE.Views.SlicerSettingsAdvanced.textSort": "排序", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "源名称", "SSE.Views.SlicerSettingsAdvanced.textTitle": "切片器 - 高级设置", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "随单元格移动和调整大小", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z到A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "这是必填栏", "SSE.Views.SortDialog.errorEmpty": "所有排序条件都必须指定列或行。", "SSE.Views.SortDialog.errorMoreOneCol": "选择了多个列。", "SSE.Views.SortDialog.errorMoreOneRow": "选择了多行。", "SSE.Views.SortDialog.errorNotOriginalCol": "选定的列不在原始选定范围内。", "SSE.Views.SortDialog.errorNotOriginalRow": "选定的行不在原始选定范围内。", "SSE.Views.SortDialog.errorSameColumnColor": "%1多次按同一颜色排序。<br>删除重复的排序条件，然后重试。", "SSE.Views.SortDialog.errorSameColumnValue": "%1多次按值排序。<br>删除重复的排序条件，然后重试。", "SSE.Views.SortDialog.textAdd": "添加级别", "SSE.Views.SortDialog.textAsc": "升序", "SSE.Views.SortDialog.textAuto": "自动", "SSE.Views.SortDialog.textAZ": "A到Z", "SSE.Views.SortDialog.textBelow": "下面", "SSE.Views.SortDialog.textCellColor": "单元格颜色", "SSE.Views.SortDialog.textColumn": "列", "SSE.Views.SortDialog.textCopy": "复制级别", "SSE.Views.SortDialog.textDelete": "删除级别", "SSE.Views.SortDialog.textDesc": "降序", "SSE.Views.SortDialog.textDown": "层下移", "SSE.Views.SortDialog.textFontColor": "字体颜色", "SSE.Views.SortDialog.textLeft": "左", "SSE.Views.SortDialog.textMoreCols": "（其他列…）", "SSE.Views.SortDialog.textMoreRows": "（其他行…）", "SSE.Views.SortDialog.textNone": "无", "SSE.Views.SortDialog.textOptions": "选项", "SSE.Views.SortDialog.textOrder": "订单", "SSE.Views.SortDialog.textRight": "右", "SSE.Views.SortDialog.textRow": "行", "SSE.Views.SortDialog.textSort": "排序", "SSE.Views.SortDialog.textSortBy": "排序方式", "SSE.Views.SortDialog.textThenBy": "然后", "SSE.Views.SortDialog.textTop": "顶部", "SSE.Views.SortDialog.textUp": "层上移", "SSE.Views.SortDialog.textValues": "值", "SSE.Views.SortDialog.textZA": "Z到A", "SSE.Views.SortDialog.txtInvalidRange": "无效的单元格区域。", "SSE.Views.SortDialog.txtTitle": "分类", "SSE.Views.SortFilterDialog.textAsc": "升序 (A 到 Z) 依据", "SSE.Views.SortFilterDialog.textDesc": "降序 (Z 到 A) 依据", "SSE.Views.SortFilterDialog.txtTitle": "排序", "SSE.Views.SortOptionsDialog.textCase": "区分大小写", "SSE.Views.SortOptionsDialog.textHeaders": "我的数据有页眉", "SSE.Views.SortOptionsDialog.textLeftRight": "从左到右排序", "SSE.Views.SortOptionsDialog.textOrientation": "方向", "SSE.Views.SortOptionsDialog.textTitle": "排序选项", "SSE.Views.SortOptionsDialog.textTopBottom": "从上到下排序", "SSE.Views.SpecialPasteDialog.textAdd": "添加", "SSE.Views.SpecialPasteDialog.textAll": "所有", "SSE.Views.SpecialPasteDialog.textBlanks": "跳过空白", "SSE.Views.SpecialPasteDialog.textColWidth": "列宽度", "SSE.Views.SpecialPasteDialog.textComments": "批注", "SSE.Views.SpecialPasteDialog.textDiv": "分开", "SSE.Views.SpecialPasteDialog.textFFormat": "公式 & 排版", "SSE.Views.SpecialPasteDialog.textFNFormat": "公式 & 编号格式", "SSE.Views.SpecialPasteDialog.textFormats": "格式", "SSE.Views.SpecialPasteDialog.textFormulas": "公式", "SSE.Views.SpecialPasteDialog.textFWidth": "公式 & 列宽度", "SSE.Views.SpecialPasteDialog.textMult": "乘", "SSE.Views.SpecialPasteDialog.textNone": "无", "SSE.Views.SpecialPasteDialog.textOperation": "操作", "SSE.Views.SpecialPasteDialog.textPaste": "粘贴", "SSE.Views.SpecialPasteDialog.textSub": "减", "SSE.Views.SpecialPasteDialog.textTitle": "特殊粘贴", "SSE.Views.SpecialPasteDialog.textTranspose": "转置", "SSE.Views.SpecialPasteDialog.textValues": "值", "SSE.Views.SpecialPasteDialog.textVFormat": "值和格式", "SSE.Views.SpecialPasteDialog.textVNFormat": "值和数字格式", "SSE.Views.SpecialPasteDialog.textWBorders": "除边框外的所有元素", "SSE.Views.Spellcheck.noSuggestions": "没有拼写建议", "SSE.Views.Spellcheck.textChange": "修改", "SSE.Views.Spellcheck.textChangeAll": "全部更改", "SSE.Views.Spellcheck.textIgnore": "忽视", "SSE.Views.Spellcheck.textIgnoreAll": "忽略所有", "SSE.Views.Spellcheck.txtAddToDictionary": "添加到词典", "SSE.Views.Spellcheck.txtClosePanel": "关闭拼写检查", "SSE.Views.Spellcheck.txtComplete": "拼写检查已完成", "SSE.Views.Spellcheck.txtDictionaryLanguage": "词典语言", "SSE.Views.Spellcheck.txtNextTip": "转到下一个单词", "SSE.Views.Spellcheck.txtSpelling": "拼字", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "复制到末尾", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "移动到末尾", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "粘贴到工作表前面", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "在纸张之前移动", "SSE.Views.Statusbar.filteredRecordsText": "{0}个记录的{1}已过滤", "SSE.Views.Statusbar.filteredText": "过滤器模式", "SSE.Views.Statusbar.itemAverage": "平均值", "SSE.Views.Statusbar.itemCopy": "复制", "SSE.Views.Statusbar.itemCount": "计数", "SSE.Views.Statusbar.itemDelete": "删除", "SSE.Views.Statusbar.itemHidden": "隐蔽", "SSE.Views.Statusbar.itemHide": "隐藏", "SSE.Views.Statusbar.itemInsert": "插入", "SSE.Views.Statusbar.itemMaximum": "最大值", "SSE.Views.Statusbar.itemMinimum": "最小值", "SSE.Views.Statusbar.itemMove": "移动", "SSE.Views.Statusbar.itemProtect": "保护", "SSE.Views.Statusbar.itemRename": "重命名", "SSE.Views.Statusbar.itemStatus": "在保存状态", "SSE.Views.Statusbar.itemSum": "合计", "SSE.Views.Statusbar.itemTabColor": "标签颜色", "SSE.Views.Statusbar.itemUnProtect": "取消保护", "SSE.Views.Statusbar.RenameDialog.errNameExists": "具有这样一个名称的工作表已经存在。", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "表格名字中不能保护以下符号：\\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "工作表名称", "SSE.Views.Statusbar.selectAllSheets": "选择所有工作表", "SSE.Views.Statusbar.sheetIndexText": "第{1}页的第{0}页", "SSE.Views.Statusbar.textAverage": "平均值", "SSE.Views.Statusbar.textCount": "计数", "SSE.Views.Statusbar.textMax": "最大值", "SSE.Views.Statusbar.textMin": "最小值", "SSE.Views.Statusbar.textNewColor": "添加新的自定义颜色", "SSE.Views.Statusbar.textNoColor": "没有颜色", "SSE.Views.Statusbar.textSum": "求和", "SSE.Views.Statusbar.tipAddTab": "添加工作表", "SSE.Views.Statusbar.tipFirst": "滚动到第一张", "SSE.Views.Statusbar.tipLast": "滚动到最后一张", "SSE.Views.Statusbar.tipListOfSheets": "工作表列表", "SSE.Views.Statusbar.tipNext": "滚动表列表对", "SSE.Views.Statusbar.tipPrev": "向左滚动表单", "SSE.Views.Statusbar.tipZoomFactor": "放大", "SSE.Views.Statusbar.tipZoomIn": "放大", "SSE.Views.Statusbar.tipZoomOut": "缩小", "SSE.Views.Statusbar.ungroupSheets": "取消工作表分组", "SSE.Views.Statusbar.zoomText": "缩放％{0}", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "所选单元格区域无法进行操作。<br>选择与现有单元格不同的统一数据范围，然后重试。", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "所选单元格范围无法完成操作。<br>选择一个范围，使第一个表行位于同一行<br>上，并将生成的表与当前的列重叠。", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "所选单元格范围无法完成操作。<br>选择不包括其他表格的范围。", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "表格中不允许使用多单元格数组公式。", "SSE.Views.TableOptionsDialog.txtEmpty": "这是必填栏", "SSE.Views.TableOptionsDialog.txtFormat": "创建表格", "SSE.Views.TableOptionsDialog.txtInvalidRange": "错误！无效的单元格范围", "SSE.Views.TableOptionsDialog.txtNote": "标头必须保留在同一行中，并且结果表范围必须与原始表范围重叠。", "SSE.Views.TableOptionsDialog.txtTitle": "标题", "SSE.Views.TableSettings.deleteColumnText": "删除列", "SSE.Views.TableSettings.deleteRowText": "删除行", "SSE.Views.TableSettings.deleteTableText": "删除表", "SSE.Views.TableSettings.insertColumnLeftText": "在左侧插入列", "SSE.Views.TableSettings.insertColumnRightText": "向右侧插入列", "SSE.Views.TableSettings.insertRowAboveText": "插入行以上", "SSE.Views.TableSettings.insertRowBelowText": "在下面插入行", "SSE.Views.TableSettings.notcriticalErrorTitle": "警告", "SSE.Views.TableSettings.selectColumnText": "选择整个列", "SSE.Views.TableSettings.selectDataText": "选择列数据", "SSE.Views.TableSettings.selectRowText": "选择行", "SSE.Views.TableSettings.selectTableText": "选择表", "SSE.Views.TableSettings.textActions": "表操作", "SSE.Views.TableSettings.textAdvanced": "显示高级设置", "SSE.Views.TableSettings.textBanded": "带状", "SSE.Views.TableSettings.textColumns": "列", "SSE.Views.TableSettings.textConvertRange": "转换到范围", "SSE.Views.TableSettings.textEdit": "行和列", "SSE.Views.TableSettings.textEmptyTemplate": "没有模板", "SSE.Views.TableSettings.textExistName": "错误！具有这种名称的范围已经存在", "SSE.Views.TableSettings.textFilter": "过滤按钮", "SSE.Views.TableSettings.textFirst": "第一", "SSE.Views.TableSettings.textHeader": "页眉", "SSE.Views.TableSettings.textInvalidName": "错误！表名无效", "SSE.Views.TableSettings.textIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.TableSettings.textLast": "最后", "SSE.Views.TableSettings.textLongOperation": "长操作", "SSE.Views.TableSettings.textPivot": "插入透视表", "SSE.Views.TableSettings.textRemDuplicates": "移除重复的元素", "SSE.Views.TableSettings.textReservedName": "您尝试使用的名称已在单元格公式中引用。请使用其他名称。", "SSE.Views.TableSettings.textResize": "调整表大小", "SSE.Views.TableSettings.textRows": "行", "SSE.Views.TableSettings.textSelectData": "选择数据", "SSE.Views.TableSettings.textSlicer": "插入分法", "SSE.Views.TableSettings.textTableName": "表名称", "SSE.Views.TableSettings.textTemplate": "从模板中选择", "SSE.Views.TableSettings.textTotal": "总计", "SSE.Views.TableSettings.warnLongOperation": "您即将执行的操作可能需要相当长的时间才能完成。<br>您确定要继续吗？", "SSE.Views.TableSettingsAdvanced.textAlt": "可选文本", "SSE.Views.TableSettingsAdvanced.textAltDescription": "描述", "SSE.Views.TableSettingsAdvanced.textAltTip": "视觉对象信息的替代的基于文本的表示，将被视为具有视觉或认知障碍的人阅读，以帮助他们更好地了解图像，自动图像，图表或表中的哪些信息。", "SSE.Views.TableSettingsAdvanced.textAltTitle": "标题", "SSE.Views.TableSettingsAdvanced.textTitle": "表-高级设置", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "自定义", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "深色的", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "浅色的", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "中等", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "表格样式深色", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "表格样式浅色", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "表格样式中等深浅", "SSE.Views.TextArtSettings.strBackground": "背景颜色", "SSE.Views.TextArtSettings.strColor": "颜色", "SSE.Views.TextArtSettings.strFill": "填满", "SSE.Views.TextArtSettings.strForeground": "前景色", "SSE.Views.TextArtSettings.strPattern": "模式", "SSE.Views.TextArtSettings.strSize": "大小", "SSE.Views.TextArtSettings.strStroke": "线条", "SSE.Views.TextArtSettings.strTransparency": "不透明度", "SSE.Views.TextArtSettings.strType": "类型", "SSE.Views.TextArtSettings.textAngle": "角度", "SSE.Views.TextArtSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "SSE.Views.TextArtSettings.textColor": "颜色填充", "SSE.Views.TextArtSettings.textDirection": "方向", "SSE.Views.TextArtSettings.textEmptyPattern": "无图案", "SSE.Views.TextArtSettings.textFromFile": "从文件导入", "SSE.Views.TextArtSettings.textFromUrl": "从URL", "SSE.Views.TextArtSettings.textGradient": "渐变点", "SSE.Views.TextArtSettings.textGradientFill": "渐变填充", "SSE.Views.TextArtSettings.textImageTexture": "图片或纹理", "SSE.Views.TextArtSettings.textLinear": "线性", "SSE.Views.TextArtSettings.textNoFill": "没有填充", "SSE.Views.TextArtSettings.textPatternFill": "模式", "SSE.Views.TextArtSettings.textPosition": "位置", "SSE.Views.TextArtSettings.textRadial": "径向", "SSE.Views.TextArtSettings.textSelectTexture": "选择", "SSE.Views.TextArtSettings.textStretch": "伸展", "SSE.Views.TextArtSettings.textStyle": "类型", "SSE.Views.TextArtSettings.textTemplate": "模板", "SSE.Views.TextArtSettings.textTexture": "从纹理", "SSE.Views.TextArtSettings.textTile": "瓦", "SSE.Views.TextArtSettings.textTransform": "跟踪变化", "SSE.Views.TextArtSettings.tipAddGradientPoint": "新增渐变点", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "删除渐变点", "SSE.Views.TextArtSettings.txtBrownPaper": "牛皮纸", "SSE.Views.TextArtSettings.txtCanvas": "画布", "SSE.Views.TextArtSettings.txtCarton": "纸板", "SSE.Views.TextArtSettings.txtDarkFabric": "深色织物", "SSE.Views.TextArtSettings.txtGrain": "颗粒", "SSE.Views.TextArtSettings.txtGranite": "花岗岩", "SSE.Views.TextArtSettings.txtGreyPaper": "灰纸", "SSE.Views.TextArtSettings.txtKnit": "针织", "SSE.Views.TextArtSettings.txtLeather": "Leather", "SSE.Views.TextArtSettings.txtNoBorders": "没有线", "SSE.Views.TextArtSettings.txtPapyrus": "纸莎草", "SSE.Views.TextArtSettings.txtWood": "木头", "SSE.Views.Toolbar.capBtnAddComment": "添加批注", "SSE.Views.Toolbar.capBtnColorSchemas": "配色方案", "SSE.Views.Toolbar.capBtnComment": "批注", "SSE.Views.Toolbar.capBtnInsHeader": "页眉/页脚", "SSE.Views.Toolbar.capBtnInsSlicer": "切片器", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "符号", "SSE.Views.Toolbar.capBtnMargins": "边距", "SSE.Views.Toolbar.capBtnPageOrient": "方向", "SSE.Views.Toolbar.capBtnPageSize": "大小", "SSE.Views.Toolbar.capBtnPrintArea": "打印区域", "SSE.Views.Toolbar.capBtnPrintTitles": "打印标题", "SSE.Views.Toolbar.capBtnScale": "按比例调整", "SSE.Views.Toolbar.capImgAlign": "对齐", "SSE.Views.Toolbar.capImgBackward": "下移一层", "SSE.Views.Toolbar.capImgForward": "上移一层", "SSE.Views.Toolbar.capImgGroup": "分组", "SSE.Views.Toolbar.capInsertChart": "图表", "SSE.Views.Toolbar.capInsertEquation": "方程", "SSE.Views.Toolbar.capInsertHyperlink": "超链接", "SSE.Views.Toolbar.capInsertImage": "图片", "SSE.Views.Toolbar.capInsertShape": "形状", "SSE.Views.Toolbar.capInsertSpark": "迷你图", "SSE.Views.Toolbar.capInsertTable": "表格", "SSE.Views.Toolbar.capInsertText": "文本框", "SSE.Views.Toolbar.capInsertTextart": "艺术字", "SSE.Views.Toolbar.mniImageFromFile": "图片文件", "SSE.Views.Toolbar.mniImageFromStorage": "图片来自存储", "SSE.Views.Toolbar.mniImageFromUrl": "图片来自网络", "SSE.Views.Toolbar.textAddPrintArea": "添加到打印区域", "SSE.Views.Toolbar.textAlignBottom": "靠下对齐", "SSE.Views.Toolbar.textAlignCenter": "居中对齐", "SSE.Views.Toolbar.textAlignJust": "正当", "SSE.Views.Toolbar.textAlignLeft": "左对齐", "SSE.Views.Toolbar.textAlignMiddle": "垂直居中", "SSE.Views.Toolbar.textAlignRight": "右对齐", "SSE.Views.Toolbar.textAlignTop": "靠上对齐", "SSE.Views.Toolbar.textAllBorders": "所有边框", "SSE.Views.Toolbar.textAuto": "自动", "SSE.Views.Toolbar.textAutoColor": "自动", "SSE.Views.Toolbar.textBold": "加粗", "SSE.Views.Toolbar.textBordersColor": "边框颜色", "SSE.Views.Toolbar.textBordersStyle": "边框风格", "SSE.Views.Toolbar.textBottom": "底部： ", "SSE.Views.Toolbar.textBottomBorders": "下边框", "SSE.Views.Toolbar.textCenterBorders": "内部垂直边框", "SSE.Views.Toolbar.textClearPrintArea": "清除打印区域", "SSE.Views.Toolbar.textClearRule": "清除规则", "SSE.Views.Toolbar.textClockwise": "顺时针角度", "SSE.Views.Toolbar.textColorScales": "色阶", "SSE.Views.Toolbar.textCounterCw": "逆时针角度", "SSE.Views.Toolbar.textCustom": "自定义", "SSE.Views.Toolbar.textDataBars": "数据栏", "SSE.Views.Toolbar.textDelLeft": "移动单元格", "SSE.Views.Toolbar.textDelUp": "向上移动单元格", "SSE.Views.Toolbar.textDiagDownBorder": "斜下框线", "SSE.Views.Toolbar.textDiagUpBorder": "斜上框线", "SSE.Views.Toolbar.textDone": "完成", "SSE.Views.Toolbar.textEditVA": "编辑可见区域", "SSE.Views.Toolbar.textEntireCol": "整列", "SSE.Views.Toolbar.textEntireRow": "整行", "SSE.Views.Toolbar.textFewPages": "页面", "SSE.Views.Toolbar.textHeight": "高度", "SSE.Views.Toolbar.textHideVA": "隐藏可见区域", "SSE.Views.Toolbar.textHorizontal": "水平文本", "SSE.Views.Toolbar.textInsDown": "向下移动单元格", "SSE.Views.Toolbar.textInsideBorders": "内边框", "SSE.Views.Toolbar.textInsRight": "右移小区", "SSE.Views.Toolbar.textItalic": "斜体", "SSE.Views.Toolbar.textItems": "项目", "SSE.Views.Toolbar.textLandscape": "横向", "SSE.Views.Toolbar.textLeft": "左： ", "SSE.Views.Toolbar.textLeftBorders": "左边框", "SSE.Views.Toolbar.textManageRule": "管理规则", "SSE.Views.Toolbar.textManyPages": "页面", "SSE.Views.Toolbar.textMarginsLast": "最后自定义", "SSE.Views.Toolbar.textMarginsNarrow": "窄", "SSE.Views.Toolbar.textMarginsNormal": "常规", "SSE.Views.Toolbar.textMarginsWide": "宽", "SSE.Views.Toolbar.textMiddleBorders": "水平边框", "SSE.Views.Toolbar.textMoreFormats": "更多格式", "SSE.Views.Toolbar.textMorePages": "更多页", "SSE.Views.Toolbar.textNewColor": "添加新的自定义颜色", "SSE.Views.Toolbar.textNewRule": "新规则", "SSE.Views.Toolbar.textNoBorders": "无边框", "SSE.Views.Toolbar.textOnePage": "页面", "SSE.Views.Toolbar.textOutBorders": "外边框", "SSE.Views.Toolbar.textPageMarginsCustom": "自定义边距", "SSE.Views.Toolbar.textPortrait": "肖像", "SSE.Views.Toolbar.textPrint": "打印", "SSE.Views.Toolbar.textPrintGridlines": "打印网格线", "SSE.Views.Toolbar.textPrintHeadings": "打印标题", "SSE.Views.Toolbar.textPrintOptions": "打印设置", "SSE.Views.Toolbar.textRight": "右： ", "SSE.Views.Toolbar.textRightBorders": "右边框", "SSE.Views.Toolbar.textRotateDown": "向下旋转文字", "SSE.Views.Toolbar.textRotateUp": "向上旋转文字", "SSE.Views.Toolbar.textScale": "缩放", "SSE.Views.Toolbar.textScaleCustom": "自定义", "SSE.Views.Toolbar.textSelection": "从当前选择", "SSE.Views.Toolbar.textSetPrintArea": "设置打印区域", "SSE.Views.Toolbar.textShowVA": "显示可见的区域", "SSE.Views.Toolbar.textStrikeout": "删除线", "SSE.Views.Toolbar.textSubscript": "下标", "SSE.Views.Toolbar.textSubSuperscript": "下标/上标", "SSE.Views.Toolbar.textSuperscript": "上标", "SSE.Views.Toolbar.textTabCollaboration": "协作", "SSE.Views.Toolbar.textTabData": "数据", "SSE.Views.Toolbar.textTabFile": "文件", "SSE.Views.Toolbar.textTabFormula": "公式", "SSE.Views.Toolbar.textTabHome": "主页", "SSE.Views.Toolbar.textTabInsert": "插入", "SSE.Views.Toolbar.textTabLayout": "布局", "SSE.Views.Toolbar.textTabProtect": "保护", "SSE.Views.Toolbar.textTabView": "视图", "SSE.Views.Toolbar.textThisPivot": "从此数据透视表", "SSE.Views.Toolbar.textThisSheet": "从此工作表", "SSE.Views.Toolbar.textThisTable": "从此表格", "SSE.Views.Toolbar.textTop": "顶边： ", "SSE.Views.Toolbar.textTopBorders": "上边框", "SSE.Views.Toolbar.textUnderline": "下划线", "SSE.Views.Toolbar.textVertical": "竖排文字", "SSE.Views.Toolbar.textWidth": "宽度", "SSE.Views.Toolbar.textZoom": "放大", "SSE.Views.Toolbar.tipAlignBottom": "靠下对齐", "SSE.Views.Toolbar.tipAlignCenter": "居中对齐", "SSE.Views.Toolbar.tipAlignJust": "正当", "SSE.Views.Toolbar.tipAlignLeft": "左对齐", "SSE.Views.Toolbar.tipAlignMiddle": "垂直居中", "SSE.Views.Toolbar.tipAlignRight": "右对齐", "SSE.Views.Toolbar.tipAlignTop": "靠上对齐", "SSE.Views.Toolbar.tipAutofilter": "排序和过滤", "SSE.Views.Toolbar.tipBack": "返回", "SSE.Views.Toolbar.tipBorders": "边框", "SSE.Views.Toolbar.tipCellStyle": "元件样式", "SSE.Views.Toolbar.tipChangeChart": "更改图表类型", "SSE.Views.Toolbar.tipClearStyle": "清除", "SSE.Views.Toolbar.tipColorSchemas": "更改配色方案", "SSE.Views.Toolbar.tipCondFormat": "条件格式", "SSE.Views.Toolbar.tipCopy": "复制", "SSE.Views.Toolbar.tipCopyStyle": "复制样式", "SSE.Views.Toolbar.tipCut": "剪切", "SSE.Views.Toolbar.tipDecDecimal": "减少十进制", "SSE.Views.Toolbar.tipDecFont": "递减字体大小", "SSE.Views.Toolbar.tipDeleteOpt": "删除单元格", "SSE.Views.Toolbar.tipDigStyleAccounting": "统计类型", "SSE.Views.Toolbar.tipDigStyleCurrency": "货币风格", "SSE.Views.Toolbar.tipDigStylePercent": "百分比风格", "SSE.Views.Toolbar.tipEditChart": "编辑图表", "SSE.Views.Toolbar.tipEditChartData": "选择数据", "SSE.Views.Toolbar.tipEditChartType": "更改图表类型", "SSE.Views.Toolbar.tipEditHeader": "编辑页眉或页脚", "SSE.Views.Toolbar.tipFontColor": "字体颜色", "SSE.Views.Toolbar.tipFontName": "字体 ", "SSE.Views.Toolbar.tipFontSize": "字体大小", "SSE.Views.Toolbar.tipHAlighOle": "水平对齐", "SSE.Views.Toolbar.tipImgAlign": "对齐对象", "SSE.Views.Toolbar.tipImgGroup": "组合对象", "SSE.Views.Toolbar.tipIncDecimal": "增加十进制", "SSE.Views.Toolbar.tipIncFont": "增加字体大小", "SSE.Views.Toolbar.tipInsertChart": "插入图表", "SSE.Views.Toolbar.tipInsertChartSpark": "插入图表", "SSE.Views.Toolbar.tipInsertEquation": "插入方程", "SSE.Views.Toolbar.tipInsertHorizontalText": "插入横排文本框", "SSE.Views.Toolbar.tipInsertHyperlink": "添加超链接", "SSE.Views.Toolbar.tipInsertImage": "插入图片", "SSE.Views.Toolbar.tipInsertOpt": "插入单元格", "SSE.Views.Toolbar.tipInsertShape": "自动插入形状", "SSE.Views.Toolbar.tipInsertSlicer": "插入分法", "SSE.Views.Toolbar.tipInsertSmartArt": "插入 SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "插入迷你图", "SSE.Views.Toolbar.tipInsertSymbol": "插入符号", "SSE.Views.Toolbar.tipInsertTable": "插入表", "SSE.Views.Toolbar.tipInsertText": "插入文字", "SSE.Views.Toolbar.tipInsertTextart": "插入艺术字", "SSE.Views.Toolbar.tipInsertVerticalText": "插入竖排文本框", "SSE.Views.Toolbar.tipMerge": "合并且居中", "SSE.Views.Toolbar.tipNone": "无", "SSE.Views.Toolbar.tipNumFormat": "数字格式", "SSE.Views.Toolbar.tipPageMargins": "页边距", "SSE.Views.Toolbar.tipPageOrient": "页面方向", "SSE.Views.Toolbar.tipPageSize": "页面大小", "SSE.Views.Toolbar.tipPaste": "粘贴", "SSE.Views.Toolbar.tipPrColor": "填充颜色", "SSE.Views.Toolbar.tipPrint": "打印", "SSE.Views.Toolbar.tipPrintArea": "打印区域", "SSE.Views.Toolbar.tipPrintTitles": "打印标题", "SSE.Views.Toolbar.tipRedo": "重做", "SSE.Views.Toolbar.tipSave": "保存", "SSE.Views.Toolbar.tipSaveCoauth": "保存您的更改以供其他用户查看", "SSE.Views.Toolbar.tipScale": "按比例调整", "SSE.Views.Toolbar.tipSelectAll": "全选", "SSE.Views.Toolbar.tipSendBackward": "下移一层", "SSE.Views.Toolbar.tipSendForward": "上移一层", "SSE.Views.Toolbar.tipSynchronize": "该文档已被其他用户更改。请点击保存更改和重新加载更新。", "SSE.Views.Toolbar.tipTextFormatting": "更多格式化工具", "SSE.Views.Toolbar.tipTextOrientation": "方向", "SSE.Views.Toolbar.tipUndo": "复原", "SSE.Views.Toolbar.tipVAlighOle": "垂直对齐", "SSE.Views.Toolbar.tipVisibleArea": "可见区域", "SSE.Views.Toolbar.tipWrap": "文字换行", "SSE.Views.Toolbar.txtAccounting": "统计", "SSE.Views.Toolbar.txtAdditional": "另外", "SSE.Views.Toolbar.txtAscending": "升序", "SSE.Views.Toolbar.txtAutosumTip": "总和", "SSE.Views.Toolbar.txtCellStyle": "单元格样式", "SSE.Views.Toolbar.txtClearAll": "所有", "SSE.Views.Toolbar.txtClearComments": "批注", "SSE.Views.Toolbar.txtClearFilter": "清除过滤", "SSE.Views.Toolbar.txtClearFormat": "格式", "SSE.Views.Toolbar.txtClearFormula": "功能", "SSE.Views.Toolbar.txtClearHyper": "超链接", "SSE.Views.Toolbar.txtClearText": "文本", "SSE.Views.Toolbar.txtCurrency": "货币", "SSE.Views.Toolbar.txtCustom": "自定义", "SSE.Views.Toolbar.txtDate": "日期", "SSE.Views.Toolbar.txtDateTime": "日期和时间", "SSE.Views.Toolbar.txtDescending": "降序", "SSE.Views.Toolbar.txtDollar": "美元", "SSE.Views.Toolbar.txtEuro": "欧元", "SSE.Views.Toolbar.txtExp": "指数", "SSE.Views.Toolbar.txtFilter": "过滤", "SSE.Views.Toolbar.txtFormula": "插入功能", "SSE.Views.Toolbar.txtFraction": "分数", "SSE.Views.Toolbar.txtFranc": "瑞士法郎", "SSE.Views.Toolbar.txtGeneral": "常规", "SSE.Views.Toolbar.txtInteger": "整数", "SSE.Views.Toolbar.txtManageRange": "名称管理", "SSE.Views.Toolbar.txtMergeAcross": "跨越合并", "SSE.Views.Toolbar.txtMergeCells": "合并单元格", "SSE.Views.Toolbar.txtMergeCenter": "合并后居中", "SSE.Views.Toolbar.txtNamedRange": "命名范围", "SSE.Views.Toolbar.txtNewRange": "定义名称", "SSE.Views.Toolbar.txtNoBorders": "无边框", "SSE.Views.Toolbar.txtNumber": "数", "SSE.Views.Toolbar.txtPasteRange": "粘贴名称", "SSE.Views.Toolbar.txtPercentage": "百分比", "SSE.Views.Toolbar.txtPound": "英镑", "SSE.Views.Toolbar.txtRouble": "卢布", "SSE.Views.Toolbar.txtScheme1": "公司地址", "SSE.Views.Toolbar.txtScheme10": "中位数", "SSE.Views.Toolbar.txtScheme11": "组件", "SSE.Views.Toolbar.txtScheme12": "组件", "SSE.Views.Toolbar.txtScheme13": "富裕的", "SSE.Views.Toolbar.txtScheme14": "奥丽尔", "SSE.Views.Toolbar.txtScheme15": "原来的", "SSE.Views.Toolbar.txtScheme16": "纸", "SSE.Views.Toolbar.txtScheme17": "冬至", "SSE.Views.Toolbar.txtScheme18": "技术", "SSE.Views.Toolbar.txtScheme19": "行进", "SSE.Views.Toolbar.txtScheme2": "灰度", "SSE.Views.Toolbar.txtScheme20": "城市的", "SSE.Views.Toolbar.txtScheme21": "气势", "SSE.Views.Toolbar.txtScheme22": "新Office", "SSE.Views.Toolbar.txtScheme3": "顶点", "SSE.Views.Toolbar.txtScheme4": "方面", "SSE.Views.Toolbar.txtScheme5": "公民", "SSE.Views.Toolbar.txtScheme6": "中央大厅", "SSE.Views.Toolbar.txtScheme7": "公平", "SSE.Views.Toolbar.txtScheme8": "流动", "SSE.Views.Toolbar.txtScheme9": "发现", "SSE.Views.Toolbar.txtScientific": "科学", "SSE.Views.Toolbar.txtSearch": "搜索", "SSE.Views.Toolbar.txtSort": "分类", "SSE.Views.Toolbar.txtSortAZ": "升序排序", "SSE.Views.Toolbar.txtSortZA": "降序排序", "SSE.Views.Toolbar.txtSpecial": "特殊格式", "SSE.Views.Toolbar.txtTableTemplate": "格式为表格模板", "SSE.Views.Toolbar.txtText": "文本", "SSE.Views.Toolbar.txtTime": "时间", "SSE.Views.Toolbar.txtUnmerge": "取消合并", "SSE.Views.Toolbar.txtYen": "日元", "SSE.Views.Top10FilterDialog.textType": "显示", "SSE.Views.Top10FilterDialog.txtBottom": "底部", "SSE.Views.Top10FilterDialog.txtBy": "依据", "SSE.Views.Top10FilterDialog.txtItems": "项目", "SSE.Views.Top10FilterDialog.txtPercent": "百分", "SSE.Views.Top10FilterDialog.txtSum": "合计", "SSE.Views.Top10FilterDialog.txtTitle": "前十位自动过滤", "SSE.Views.Top10FilterDialog.txtTop": "顶部", "SSE.Views.Top10FilterDialog.txtValueTitle": "前10个筛选器", "SSE.Views.ValueFieldSettingsDialog.textTitle": "值字段设置", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "平均值", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "基域", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "基本项目", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%2的%1 ", "SSE.Views.ValueFieldSettingsDialog.txtCount": "计数", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "计数", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "自定义名称", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "不同于", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "索引", "SSE.Views.ValueFieldSettingsDialog.txtMax": "最大值", "SSE.Views.ValueFieldSettingsDialog.txtMin": "最小值", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "没有计算", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "百分差", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "占所有列的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "占所有的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "占所有行的百分比", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "乘积", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "总运行", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "值显示方式", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "源名称：", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "标准差", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "总体标准偏差", "SSE.Views.ValueFieldSettingsDialog.txtSum": "合计", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "汇总值字段", "SSE.Views.ValueFieldSettingsDialog.txtVar": "方差", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "总体方差", "SSE.Views.ViewManagerDlg.closeButtonText": "关闭", "SSE.Views.ViewManagerDlg.guestText": "游客", "SSE.Views.ViewManagerDlg.lockText": "锁定", "SSE.Views.ViewManagerDlg.textDelete": "删除", "SSE.Views.ViewManagerDlg.textDuplicate": "重复", "SSE.Views.ViewManagerDlg.textEmpty": "目前没有创建视图。", "SSE.Views.ViewManagerDlg.textGoTo": "转到视图", "SSE.Views.ViewManagerDlg.textLongName": "请键入少于128个字符的名称。", "SSE.Views.ViewManagerDlg.textNew": "新", "SSE.Views.ViewManagerDlg.textRename": "重命名", "SSE.Views.ViewManagerDlg.textRenameError": "视图名称不得为空。", "SSE.Views.ViewManagerDlg.textRenameLabel": "重命名视图", "SSE.Views.ViewManagerDlg.textViews": "工作表视图", "SSE.Views.ViewManagerDlg.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.ViewManagerDlg.txtTitle": "工作表视图的管理器", "SSE.Views.ViewManagerDlg.warnDeleteView": "您正在尝试删除当前启用的视图'％1'。 <br> 关闭并删除此视图吗？", "SSE.Views.ViewTab.capBtnFreeze": "冻结窗格", "SSE.Views.ViewTab.capBtnSheetView": "工作表视图", "SSE.Views.ViewTab.textAlwaysShowToolbar": "始终显示工具栏", "SSE.Views.ViewTab.textClose": "关闭", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "合并工作表和状态栏", "SSE.Views.ViewTab.textCreate": "新", "SSE.Views.ViewTab.textDefault": "默认", "SSE.Views.ViewTab.textFormula": "公式栏", "SSE.Views.ViewTab.textFreezeCol": "冻结首列", "SSE.Views.ViewTab.textFreezeRow": "冻结首行", "SSE.Views.ViewTab.textGridlines": "网格线", "SSE.Views.ViewTab.textHeadings": "标题", "SSE.Views.ViewTab.textInterfaceTheme": "界面主题", "SSE.Views.ViewTab.textManager": "查看管理器", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "显示冻结窗格的阴影", "SSE.Views.ViewTab.textUnFreeze": "取消冻结窗格", "SSE.Views.ViewTab.textZeros": "显示零", "SSE.Views.ViewTab.textZoom": "放大", "SSE.Views.ViewTab.tipClose": "关闭工作表视图", "SSE.Views.ViewTab.tipCreate": "创建工作表视图", "SSE.Views.ViewTab.tipFreeze": "冻结窗格", "SSE.Views.ViewTab.tipInterfaceTheme": "界面主题", "SSE.Views.ViewTab.tipSheetView": "工作表视图", "SSE.Views.WatchDialog.closeButtonText": "关闭", "SSE.Views.WatchDialog.textAdd": "添加监视", "SSE.Views.WatchDialog.textBook": "工作簿", "SSE.Views.WatchDialog.textCell": "单元格", "SSE.Views.WatchDialog.textDelete": "删除监视", "SSE.Views.WatchDialog.textDeleteAll": "删除全部", "SSE.Views.WatchDialog.textFormula": "公式", "SSE.Views.WatchDialog.textName": "姓名", "SSE.Views.WatchDialog.textSheet": "工作表", "SSE.Views.WatchDialog.textValue": "值", "SSE.Views.WatchDialog.txtTitle": "监视窗口", "SSE.Views.WBProtection.hintAllowRanges": "允许编辑范围", "SSE.Views.WBProtection.hintProtectSheet": "保护工作表", "SSE.Views.WBProtection.hintProtectWB": "保护工作簿​​", "SSE.Views.WBProtection.txtAllowRanges": "允许编辑范围", "SSE.Views.WBProtection.txtHiddenFormula": "隐藏公式", "SSE.Views.WBProtection.txtLockedCell": "锁定的单元格", "SSE.Views.WBProtection.txtLockedShape": "形状锁定了", "SSE.Views.WBProtection.txtLockedText": "锁定文本", "SSE.Views.WBProtection.txtProtectSheet": "保护工作表", "SSE.Views.WBProtection.txtProtectWB": "保护工作簿​​", "SSE.Views.WBProtection.txtSheetUnlockDescription": "输入密码解除保护", "SSE.Views.WBProtection.txtSheetUnlockTitle": "取消工作表保护", "SSE.Views.WBProtection.txtWBUnlockDescription": "输入密码解除保护", "SSE.Views.WBProtection.txtWBUnlockTitle": "取消工作簿保护"}