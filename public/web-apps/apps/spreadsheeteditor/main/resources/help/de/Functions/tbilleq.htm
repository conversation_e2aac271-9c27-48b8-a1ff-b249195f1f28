﻿<!DOCTYPE html>
<html>
	<head>
		<title>TBILLÄQUIV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>TBILLÄQUIV-Funktion</h1>
			<p>Die Funktion <b>TBILLÄQUIV</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie rechnet die Verzinsung eines Schatzwechsels (Treasury Bill) in die für Anleihen übliche einfache jährliche Verzinsung um.</p>
			<p>Die Formelsyntax der Funktion <b>TBILLÄQUIV</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>TBILLÄQUIV(Abrechnung;Fälligkeit;Disagio)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
                <p style="text-indent: 50px;"><b><em>Abrechnung</em></b> ist der Abrechnungstermin des Wertpapiers.</p>
                <p style="text-indent: 50px;"><b><em>Fälligkeit</em></b> ist der Fälligkeitstermin des Wertpapiers. Dieses Datum muss innerhalb eines Jahres nach dem Abrechnungstag liegen.</p> 
				<p style="text-indent: 50px;"><b><em>Disagio</em></b> ist der Abschlag (Disagio) des Wertpapiers.</p> 				
            <p class="note"><b>Hinweis:</b> Daten müssen mit der Funktion DATUM eingegeben werden.</p>
            <p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>TBILLÄQUIV</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>TBILLÄQUIV</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="TBILLÄQUIV-Funktion" src="../images/tbilleq.png" /></p>
		</div>
	</body>
</html>