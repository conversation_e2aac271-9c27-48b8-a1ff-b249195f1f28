﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZUFALLSBEREICH-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ZUFALLSBEREICH-Funktion</h1>
			<p>Die Funktion <b>ZUFALLSBEREICH</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie gibt eine Zufallszahl zurück, die größer oder gleich <b>Untere_Zahl</b> und kleiner oder gleich <b>Obere_Zahl</b> ist.</p>
			<p>Die Formelsyntax der Funktion <b>ZUFALLSBEREICH</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ZUFALLSBEREICH(Untere_Zahl;Obere_Zahl)</em></b></p>
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Untere_Zahl</em></b> ist die kleinste ganze Zahl, die ZUFALLSBEREICH als Ergebnis zurückgeben kann.</p>
			<p style="text-indent: 50px;"><b><em>Obere_Zahl</em></b> ist die größte ganze Zahl, die ZUFALLSBEREICH als Ergebnis zurückgeben kann.</p>
			<p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p> 
			<p class="note"><b>Hinweis</b>: Wenn <b><em>Untere_Zahl</em></b> größer als <b><em>Obere_Zahl</em></b> ist, gibt die Funktion den Fehlerwert <b>#NUM!</b> zurück.</p>
			<p>Anwendung der Funktion <b>ZUFALLSBEREICH</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ZUFALLSBEREICH</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ZUFALLSBEREICH-Funktion" src="../images/randbetween.png" /></p>
		</div>
	</body>
</html>