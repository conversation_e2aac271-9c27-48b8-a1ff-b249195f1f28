﻿<!DOCTYPE html>
<html>
	<head>
		<title>BW-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>BW-Funktion</h1>
			<p>Die Funktion <b>BW</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie berechnet den aktuellen Wert eines Darlehens oder einer Investition, wobei ein konstanter Zinssatz vorausgesetzt wird.</p>
			<p>Die Formelsyntax der Funktion <b>BW</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>BW(Zins;Zzr;Rmz;[Zw];[F])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zins</em></b> ist der Zinssatz pro Periode.</p>
				<p style="text-indent: 50px;"><b><em>Zzr</em></b> ist die Gesamtanzahl der Zahlungsperioden.</p>
				<p style="text-indent: 50px;"><b><em>Rmz</em></b> ist der Betrag (die Annuität), der in den einzelnen Perioden gezahlt wird.</p> 
				<p style="text-indent: 50px;"><b><em>Zw</em></b> ist der zukünftige Wert (Endwert) nach der letzten Zahlung. Dieses Argument ist optional. Fehlt das Argument <b><em>Zw</em></b>, wird es als 0 (Null) angenommen.</p>
				<p style="text-indent: 50px;"><b><em>F</em></b> gibt den Zeitraum an, wann die Zahlungen fällig sind. Dieses Argument ist optional. Fehlt das Argument oder ist auf 0 festgelegt, nimmt die Funktion an, dass die Zahlungen am Ende der Periode fällig sind. Ist <b><em>F</em></b> mit 1 angegeben, sind die Zahlungen zum Anfang der Periode fällig.</p>
				<p class="note"><b>Hinweis:</b> die abgegebenen Zahlungsmittel (z.B., die Spareinlagen) werden mit negativen Zahlen dargestellt; erhaltene Zahlungsmittel (z.B., die Dividenden) werden mit positiven Zahlen dargestellt. Sie sollten unbedingt darauf achten, dass Sie für <em>Zins</em> und <em>Zzr</em> zueinander passende Zeiteinheiten verwenden. Verwenden Sie beispielsweise bei monatliche Zahlungen N%/12 für <em>Zins</em> und N*12 für <em>Zzr</em>, für vierteljährliche Zahlungen N%/4 für <em>Zins</em> and N*4 für <em>Zzr</em> und für jährliche Zahlungen N% für  und N für .</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>BW</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>BW</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="BW-Funktion" src="../images/pv.png" /></p>
		</div>
	</body>
</html>