﻿<!DOCTYPE html>
<html>
	<head>
		<title>Home tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - Home tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Home tab</h1>
            <p>The <b>Home</b> tab opens by default when you open a presentation. It allows to set general slide parameters, format text, insert some objects, align and arrange them.</p>
            <div class="onlineDocumentFeatures">
                <p>Online <a href="https://www.onlyoffice.com/it/presentation-editor.aspx">Presentation Editor</a> window:</p>
                <p><img alt="Home tab" src="../images/interface/hometab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Desktop Presentation Editor window:</p>
                <p><img alt="Home tab" src="../images/interface/desktop_hometab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li>manage <a href="../UsageInstructions/ManageSlides.htm" onclick="onhyperlinkclick(this)">slides</a> and <a href="../UsageInstructions/PreviewPresentation.htm" onclick="onhyperlinkclick(this)">start slideshow</a>,</li>
                <li>format <a href="../UsageInstructions/InsertText.htm#formattext" onclick="onhyperlinkclick(this)">text</a> within a text box,</li>
                <li>insert <a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">text boxes</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">pictures</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">shapes</a>,</li>
                <li><a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">align and arrange objects</a> on a slide,</li>
                <li><a href="../UsageInstructions/CopyClearFormatting.htm" onclick="onhyperlinkclick(this)">copy/clear</a> text formatting,</li>
                <li>change a <a href="../UsageInstructions/SetSlideParameters.htm" onclick="onhyperlinkclick(this)">theme, color scheme or slide size</a>.</li>
            </ul>
		</div>
	</body>
</html>