﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZEILE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ZEILE-Funktion</h1>
			<p>Die Funktion <b>ZEILE</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Sie gibt die Zeilennummer eines Bezugs zurück.</p>
			<p>Die Formelsyntax der Funktion <b>ZEILE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ZEILE([Bezug])</em></b></p> 
			<p>Der <b><em>Bezug</em></b> ist die Zelle oder der Bereich von Zellen, deren bzw. dessen Zeilennummer zurückgegeben werden soll.</p>
			<p class="note"><b>Hinweis</b>: Das Argument <b><em>Bezug</em></b> ist optional. Fehlt das Argument Bezug, wird es als Bezug der Zelle angenommen, in der die Funktion <b>ZEILE</b> enthalten ist.</p>
			<p>Anwendung der Funktion <b>ZEILE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ZEILE</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ZEILE-Funktion" src="../images/row.png" /></p>
		</div>
	</body>
</html>