﻿<!DOCTYPE html>
<html>
	<head>
		<title>FINDEN/FINDENB-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>FINDEN/FINDENB-Funktion</h1>
			<p>Die Funktionen <b>FINDEN</b>/<b>FINDENB</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie sucht einen in einem anderen Textwert enthaltenen Textwert. Die Funktion <b>FINDEN</b> ist für Sprachen gedacht, die den Single-Byte-Zeichensatz (SBCS) verwenden, während <b>FINDENB</b> für Sprachen verwendet wird, die den Doppelbyte-Zeichensatz (DBCS) verwenden, wie Japanisch, Chinesisch, Koreanisch usw.</p>
			<p>Die Formelsyntax der Funktion <b>FINDEN/FINDENB</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>FINDEN(Suchtext;Text;[Erstes_Zeichen])</em></b></p> 
			<p style="text-indent: 150px;"><b><em>FINDENB(Suchtext;Text;[Erstes_Zeichen])</em></b></p> 
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Suchtext</em></b> gibt den Text an, den Sie suchen.</p>
			<p style="text-indent: 50px;"><b><em>Text</em></b> ist der Text, der den Text enthält, den Sie suchen möchten.</p>
			<p style="text-indent: 50px;"><b><em>Erstes_Zeichen</em></b> gibt an, bei welchem Zeichen die Suche begonnen werden soll. Das Argument Erstes_Zeichen ist optional. Fehlt das Argument beginnt die Funktion mit dem ersten Zeichen des Textes.</p>
			<p>Die Werte können manuell eingegeben werden oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p class="note"><b>Hinweis</b>: Liegen keine Übereinstimmungen vor, geben die Funktionen FINDEN und FINDENB den Fehlerwert <b>#WERT!</b> zurück.</p>
			<p>Anwendung der Funktionen <b>FINDEN/FINDENB</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>FINDEN oder FINDENB</b>:</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.<p class="note"><b>Hinweis</b>: die Funktionen FINDEN/FINDENB <b>unterscheiden zwischen Groß- und Kleinbuchstaben</b>.</p>
			</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="FINDEN/FINDENB-Funktion" src="../images/find.png" /></p>
		</div>
	</body>
</html>