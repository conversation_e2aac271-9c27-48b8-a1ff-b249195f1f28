﻿<!DOCTYPE html>
<html>
	<head>
		<title>IMSUB-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>IMSUB-Funktion</h1>
			<p>Die Funktion <b>IMSUB</b> gehört zur Gruppe der technischen Funktionen. Sie gibt die Differenz zweier komplexer Zahlen im Format x + yi oder x + yj zurück.</p>
			<p>Die Formelsyntax der Funktion <b>IMSUB</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>IMSUB(Komplexe_Zahl1;Komplexe_Zahl2)</em></b></p> 
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Komplexe_Zahl1</em></b> ist die komplexe Zahl, von der <b><em>Komplexe_Zahl2</em></b> subtrahiert werden soll.</p>
            <p style="text-indent: 50px;"><b><em>Komplexe_Zahl2</em></b> ist die komplexe Zahl, die von <b><em>Komplexe_Zahl1</em></b> subtrahiert werden soll.</p>
            <p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>IMSUB</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Technische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>IMSUB</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="IMSUB-Funktion" src="../images/imsub.png" /></p>
		</div>
	</body>
</html>