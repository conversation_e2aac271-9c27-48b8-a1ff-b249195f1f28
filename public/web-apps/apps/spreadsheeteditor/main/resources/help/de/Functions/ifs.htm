﻿<!DOCTYPE html>
<html>
	<head>
		<title>WENNS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="Die Funktion WENNS gehört zur Gruppe der logischen Funktionen. Erfahren Sie, wie Sie die WENNS-Formel in Tabellen und kompatiblen Dateien nutzen." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WENNS-Funktion</h1>
			<p>Die Funktion <b>WENNS</b> gehört zur Gruppe der logischen Funktionen. Die Funktion prüft, ob eine oder mehrere Bedingungen zutreffen und gibt den Wert zurück, der der ersten auf WAHR lautenden Bedingung entspricht.</p>
			<p>Die Formelsyntax der Funktion <b>WENNS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>([Logiktest1; Wert wenn Wahr1; [Logiktest2; Wert wenn Wahr2];…)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Logiktest1</em></b> ist die erste Bedingung, die auf WAHR oder FALSCH ausgewertet wird.</p>
            <p style="text-indent: 50px;"><b><em>Wert wenn Wahr1</em></b> ist der Wert, der zurückgegeben wird, wenn der <b><em>Logiktest1</em></b> WAHR ist.</p>
            <p style="text-indent: 50px;"><b><em>Logiktest2; Wert wenn Wahr2...</em></b> sind zusätzliche Bedingungen und Werte, die zurückgegeben werden sollen. Diese Argumente sind optional. Hinweis: Sie können bis zu 127 Bedingungen überprüfen.</p>
            <p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<h2>Wie funktioniert WENNS</h2>
			<p>Anwendung der Funktion <b>WENNS</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Logisch</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WENNS</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p><em>Beispiel:</em></p>
			<p>Die folgende Argumente liegen vor: <em>Logiktest1</em> = <b>A1&lt;100</b>, <em>Wert_wenn_wahr1</em> = <b>1</b>, <em>Logiktest2</em> = <b>A1&gt;100</b>, <em>Wert_wenn_wahr2</em> = <b>2</b>, dann gilt <b>A1</b> ist <b>120</b>. Der zweite logische Ausdruck ist <b>WAHR</b>. Also gibt die Funktion den Wert <b>2</b> zurück.</p>
			<p style="text-indent: 150px;"><img alt="WENNS-Funktion" src="../images/ifs.png" /></p>
		</div>
	</body>
</html>