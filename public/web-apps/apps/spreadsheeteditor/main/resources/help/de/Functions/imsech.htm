﻿<!DOCTYPE html>
<html>
	<head>
		<title>IMSECHYP-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>IMSECHYP-Funktion</h1>
			<p>Die Funktion <b>IMSECHYP</b> gehört zur Gruppe der technischen Funktionen. Sie wird genutzt, um den hyperbolischen Sekans einer komplexen Zahl zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>IMSECHYP</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>IMSECHYP(Komplexe_Zahl)</em></b></p> 
			<p><b><em>Komplexe_Zahl</em></b> ist eine komplexe Zahl, die im Format x + yi oder x + yj manuell eingegeben wird oder in der Zelle beinhaltet ist, auf die Sie Bezug nehmen.</p>
			<p>Anwenden der Funktion <b>IMSECHYP</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Technische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>IMSECHYP</b>:</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="IMSECHYP-Funktion" src="../images/imsech.png" /></p>
		</div>
	</body>
</html>