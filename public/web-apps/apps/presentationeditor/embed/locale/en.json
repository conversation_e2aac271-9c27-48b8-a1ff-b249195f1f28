{"common.view.modals.txtCopy": "Copy to clipboard", "common.view.modals.txtEmbed": "Embed", "common.view.modals.txtHeight": "Height", "common.view.modals.txtShare": "Share Link", "common.view.modals.txtWidth": "<PERSON><PERSON><PERSON>", "common.view.SearchBar.textFind": "Find", "PE.ApplicationController.convertationErrorText": "Conversion failed.", "PE.ApplicationController.convertationTimeoutText": "Conversion timeout exceeded.", "PE.ApplicationController.criticalErrorTitle": "Error", "PE.ApplicationController.downloadErrorText": "Download failed.", "PE.ApplicationController.downloadTextText": "Downloading presentation...", "PE.ApplicationController.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "PE.ApplicationController.errorDefaultMessage": "Error code: %1", "PE.ApplicationController.errorFilePassProtect": "The file is password protected and cannot be opened.", "PE.ApplicationController.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "PE.ApplicationController.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to your computer hard drive or try again later.", "PE.ApplicationController.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "PE.ApplicationController.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "PE.ApplicationController.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "PE.ApplicationController.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "PE.ApplicationController.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "PE.ApplicationController.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "PE.ApplicationController.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "PE.ApplicationController.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "PE.ApplicationController.errorUserDrop": "The file cannot be accessed right now.", "PE.ApplicationController.notcriticalErrorTitle": "Warning", "PE.ApplicationController.openErrorText": "An error has occurred while opening the file.", "PE.ApplicationController.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "PE.ApplicationController.textAnonymous": "Anonymous", "PE.ApplicationController.textGuest": "Guest", "PE.ApplicationController.textLoadingDocument": "Loading presentation", "PE.ApplicationController.textOf": "of", "PE.ApplicationController.txtClose": "Close", "PE.ApplicationController.unknownErrorText": "Unknown error.", "PE.ApplicationController.unsupportedBrowserErrorText": "Your browser is not supported.", "PE.ApplicationController.waitText": "Please, wait...", "PE.ApplicationView.txtDownload": "Download", "PE.ApplicationView.txtEmbed": "Embed", "PE.ApplicationView.txtFileLocation": "Open file location", "PE.ApplicationView.txtFullScreen": "Full Screen", "PE.ApplicationView.txtPrint": "Print", "PE.ApplicationView.txtSearch": "Search", "PE.ApplicationView.txtShare": "Share"}