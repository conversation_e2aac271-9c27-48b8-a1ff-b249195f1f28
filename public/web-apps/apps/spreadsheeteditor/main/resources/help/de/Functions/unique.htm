﻿<!DOCTYPE html>
<html>
<head>
    <title>EINDEUTIG-Funktion</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>EINDEUTIG-Funktion</h1>
        <p>Die Funktion <b>EINDEUTIG</b> ist eine der Nachschlage- und Verweisfunktionen. Sie wird verwendet, um eine Liste von eindeutigen Werten in einer Liste oder einem Bereich zurückzugeben.</p>
        <p>Die Formelsyntax der Funktion <b>EINDEUTIG</b> ist:</p>
        <p style="text-indent: 150px;"><b><em>EINDEUTIG(array,[nach_Spalte],[genau_einmal])</em></b></p>
        <p>dabei gilt</p>
        <p><b><em>array</em></b> ist der Bereich, aus dem eindeutige Werte zurückgegeben werden sollen.</p>
        <p><b><em>nach_Spalte</em></b> ist der optionale WAHR oder FALSCH Wert, der die Vergleichsmethode angibt: WAHR für Spalten und FALSCH für Zeilen.</p>
        <p><b><em>genau_einmal</em></b> ist der optionale WAHR oder FALSCH Wert, der die Rückgabemethode angibt: WAHR für einmal vorkommende Werte und FALSCH für alle eindeutigen Werte.</p>

        <p>Anwendung der Funktion <b>EINDEUTIG</b>:</p>
        <ol>
            <li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
            <li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
            <li>Wählen Sie die Gruppe <b>Nachschlage- und Verweisfunktionen</b> Funktionen aus der Liste aus.</li>
            <li>Klicken Sie auf die Funktion <b>EINDEUTIG</b>.</li>
            <li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
            <li>Drücken Sie die <b>Eingabetaste</b>.</li>
        </ol>
        <p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
        <p class="note">Um einen Wertebereich zurückzugeben, wählen Sie einen gewünschten Zellenbereich aus, geben Sie die Formel ein und drücken Sie die Tastenkombination <b>Strg+Umschalt+Eingabetaste</b>.</p>
        <p style="text-indent: 150px;"><img alt="EINDEUTIG-Funktion" src="../images/unique.png" /></p>
    </div>
</body>
</html>