﻿<!DOCTYPE html>
<html>
	<head>
		<title>LÄNGE/LÄNGEB-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>LÄNGE/LÄNGEB-Funktion</h1>
			<p>Die Funktion <b>LÄNGE/LÄNGEB</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie wird verwendet, um die angegebene Zeichenfolge zu analysieren und die Anzahl der enthaltenen Zeichen/Bytes zurückzugeben. Die Funktion <b>LÄNGE</b> ist für Sprachen gedacht, die den Single-Byte-Zeichensatz (SBCS) verwenden, während <b>LÄNGEB</b> für Sprachen verwendet wird, die den Doppelbyte-Zeichensatz (DBCS) verwenden, wie Japanisch, Chinesisch, Koreanisch usw.</p>
			<p>Die Formelsyntax der Funktion <b>LÄNGE/LÄNGEB</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>LÄNGE(Text)</em></b></p>
			<p style="text-indent: 150px;"><b><em>LÄNGEB(Text)</em></b></p> 
			<p><b><em>Text</em></b> ist der Text, dessen Länge Sie ermitteln möchten. Leerzeichen zählen als Zeichen.</p>
			<p>Anwendung der Funktionen <b>LÄNGE/LÄNGEB</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die gewünschte Funktion <b>LÄNGE</b> bzw. <b>LÄNGEB</b>:</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="LÄNGE/LÄNGEB-Funktion" src="../images/len.png" /></p>
		</div>
	</body>
</html>