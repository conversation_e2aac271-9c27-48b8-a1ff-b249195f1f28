{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn của bạn ở đây", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBar": "Gạch", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLine": "Đường kẻ", "Common.define.chartData.textLineSpark": "Đường kẻ", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON> b<PERSON>", "Common.define.chartData.textPoint": "XY (Phân tán)", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "<PERSON><PERSON> phi<PERSON>u", "Common.define.chartData.textSurface": "Bề mặt", "Common.define.chartData.textWinLossSpark": "Win/Loss", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON> có kiểu", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON> t<PERSON>i", "Common.UI.ExtendedColorDialog.textHexErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị thuộc từ 000000 đến FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị số thuộc từ 0 đến 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON> màu", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON> sáng kết quả", "Common.UI.SearchDialog.textMatchCase": "<PERSON>ân biệt chữ hoa chữ thường", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> văn bản thay thế", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> từ khóa của bạn ở đây", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON> và Thay thế", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Chỉ toàn bộ từ", "Common.UI.SearchDialog.txtBtnHideReplace": "Ẩn Thay thế", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON>hay thế", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON> thế tất cả", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.SynchronizeTip.textSynchronize": "Tài liệu đã được thay đổi bởi người dùng khác.<br><PERSON><PERSON> lòng nhấp để lưu thay đổi của bạn và tải lại các cập nhật.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Màu theme", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Đ<PERSON><PERSON>", "Common.UI.Window.noButtonText": "K<PERSON>ô<PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.Window.textError": "Lỗi", "Common.UI.Window.textInformation": "Thông tin", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "địa chỉ:", "Common.Views.About.txtLicensee": "NGƯỜI ĐƯỢC CẤP GIẤY PHÉP", "Common.Views.About.txtLicensor": "NGƯỜI CẤP GIẤY PHÉP", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Được hỗ trợ bởi", "Common.Views.About.txtTel": "ĐT.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "Common.Views.Comments.textAddCommentToDoc": "<PERSON><PERSON><PERSON><PERSON> bình luận vào tài liệu", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Đ<PERSON><PERSON>", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> bình luận của bạn ở đây", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "Common.Views.Comments.textOpenAgain": "Mở lại", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON> lờ<PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON> gi<PERSON>i quy<PERSON>", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, cắt và dán bằng cách sử dụng các nút trên thanh công cụ của trình soạn thảo và các tác vụ trình đơn ngữ cảnh sẽ chỉ được thực hiện trong tab trình soạn thảo này.<br><br> Để sao chép hoặc dán vào hoặc từ các ứng dụng bên ngoài tab trình soạn thảo sử dụng các kết hợp bàn phím sau đây:", "Common.Views.CopyWarningDialog.textTitle": "Sao ché<PERSON>, Cắt và Dán", "Common.Views.CopyWarningDialog.textToCopy": "để sao chép", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "<PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "<PERSON><PERSON> tả<PERSON>...", "Common.Views.DocumentAccessDialog.textTitle": "Cài đặt chia sẻ", "Common.Views.Header.labelCoUsersDescr": "<PERSON>ài liệu hiện đang được chỉnh sửa bởi nhiều người dùng.", "Common.Views.Header.textBack": "<PERSON><PERSON> tới Tài liệu", "Common.Views.Header.textSaveBegin": "<PERSON><PERSON> l<PERSON>...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON> sửa đổi", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON> lưu mọi thay đổi", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON> lưu mọi thay đổi", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON><PERSON><PERSON> lý quyền truy cập tài liệu", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Chỉnh sửa file hiện tại", "Common.Views.Header.tipPrint": "In file", "Common.Views.Header.tipViewUsers": "<PERSON>em người dùng và quản lý quyền truy cập tài liệu", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON> tên", "Common.Views.ImageFromUrlDialog.textUrl": "Dán URL hình ảnh:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "Common.Views.OpenDialog.txtDelimiter": "<PERSON><PERSON><PERSON> phân cách", "Common.Views.OpenDialog.txtEncoding": "Mã hóa", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng.", "Common.Views.OpenDialog.txtOpenFile": "Nhập mật khẩu để mở tệp", "Common.Views.OpenDialog.txtOther": "K<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Chọn %1 lựa chọn", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON> vệ", "Common.Views.PasswordDialog.txtWarning": "Chú ý: Nếu bạn mất hoặc quên mật khẩu, bạn không thể khôi phục mật khẩu.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON> t<PERSON>", "Common.Views.Plugins.groupCaption": "Plugin", "Common.Views.Plugins.strPlugins": "Plugin", "Common.Views.Plugins.textLoading": "<PERSON><PERSON> t<PERSON>", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Common.Views.Plugins.textStop": "Dừng", "Common.Views.RenameDialog.textName": "Tên file", "Common.Views.RenameDialog.txtInvalidName": "Tên file không đư<PERSON><PERSON> chứa bất kỳ ký tự nào sau đây:", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON>n chỉnh", "SSE.Controllers.DocumentHolder.centerText": "Trung tâm", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "Xóa", "SSE.Controllers.DocumentHolder.errorInvalidLink": "<PERSON><PERSON> chiếu liên kết không tồn tại. <PERSON><PERSON> lòng sửa liên kết hoặc xóa nó.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> trái", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> trên", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.leftText": "Trái", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON> rộng cột {0} ký hiệu ({1} pixel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON> cao hàng {0} đi<PERSON>m ({1} pixel)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Ấn CTRL và nhấp vào liên kết", "SSE.Controllers.DocumentHolder.textInsertLeft": "<PERSON><PERSON><PERSON> sang trái", "SSE.Controllers.DocumentHolder.textInsertTop": "<PERSON><PERSON><PERSON> lên đ<PERSON>u", "SSE.Controllers.DocumentHolder.tipIsLocked": "Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON> đườ<PERSON> viền dưới cùng", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON><PERSON> dấu phân số", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> đường kẻ ngang", "SSE.Controllers.DocumentHolder.txtAddLB": "Thê<PERSON> đường kẻ dưới cùng bên trái", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền trái", "SSE.Controllers.DocumentHolder.txtAddLT": "Thê<PERSON> đường kẻ trên cùng bên trái", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền bên phải", "SSE.Controllers.DocumentHolder.txtAddTop": "<PERSON>hê<PERSON> đường viền trên cùng", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON><PERSON> đường kẻ dọc", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON>n chỉnh theo ký tự", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đư<PERSON> vền", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> chỉnh cột", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đ<PERSON>i số", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> đ<PERSON>i số", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "SSE.Controllers.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON> các ký tự kèm theo", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON> các ký tự và dấu phân cách kèm theo", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON> biểu đồ", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> c<PERSON>c", "SSE.Controllers.DocumentHolder.txtExpand": "Mở rộng và sắp xếp", "SSE.Controllers.DocumentHolder.txtExpandSort": "Dữ liệu bên cạnh vùng đã chọn sẽ không được sắp xếp. Bạn muốn mở rộng vùng chọn để bao gồm dữ liệu liền kề hay tiếp tục sắp xếp các ô đã chọn hiện tại?", "SSE.Controllers.DocumentHolder.txtFractionLinear": "<PERSON><PERSON> sang phân số viết ngang", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> sang phân số viết lệch", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> đ<PERSON> sang phân số viết đứng", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON><PERSON> đồ trên văn bản", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> dư<PERSON><PERSON> văn bản", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Controllers.DocumentHolder.txtHideBottom": "Ẩn đường viền dưới cùng", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Ẩn giới hạn dưới", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Ẩn dấu ngoặc đóng", "SSE.Controllers.DocumentHolder.txtHideDegree": "Ẩn cấp độ", "SSE.Controllers.DocumentHolder.txtHideHor": "Ẩn đường kẻ ngang", "SSE.Controllers.DocumentHolder.txtHideLB": "Ẩn đường kẻ dưới cùng bên trái", "SSE.Controllers.DocumentHolder.txtHideLeft": "Ẩn đường viền trái", "SSE.Controllers.DocumentHolder.txtHideLT": "Ẩn đường kẻ trên cùng bên trái", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Ẩn dấu ngoặc mở", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Ẩn placeholder", "SSE.Controllers.DocumentHolder.txtHideRight": "Ẩn đường viền bên phải", "SSE.Controllers.DocumentHolder.txtHideTop": "Ẩn đường viền trên cùng", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Ẩn giới hạn trên cùng", "SSE.Controllers.DocumentHolder.txtHideVer": "Ẩn đường kẻ dọc", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đối số", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "<PERSON><PERSON><PERSON> đ<PERSON>i số sau", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "<PERSON><PERSON><PERSON> đ<PERSON>i số trước", "SSE.Controllers.DocumentHolder.txtInsertBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON>ng trình sau", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình trước", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON>hay đổi giới hạn địa điểm", "SSE.Controllers.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON><PERSON> hạn trên văn bản", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON><PERSON> hạn dưới văn bản", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Chỉnh dấu ngoặc phù hợp với độ cao đối số", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON> chỉnh ma trận", "SSE.Controllers.DocumentHolder.txtNoChoices": "<PERSON><PERSON><PERSON>ng có lựa chọn để điền vào ô.<br>Chỉ có thể chọn các giá trị văn bản từ cột này để thay thế.", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON><PERSON> trên v<PERSON><PERSON> bản", "SSE.Controllers.DocumentHolder.txtPaste": "Dán", "SSE.Controllers.DocumentHolder.txtPasteBorders": "<PERSON><PERSON><PERSON> thức không có đường viền", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "<PERSON><PERSON>ng thức + chiều rộng cột", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "<PERSON><PERSON><PERSON> dạng đ<PERSON>ch", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Chỉ dán định dạng", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "<PERSON><PERSON><PERSON> thức + định dạng số", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Chỉ dán công thức", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "<PERSON><PERSON><PERSON> thức + tất cả định dạng", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> liên kết", "SSE.Controllers.DocumentHolder.txtPasteMerge": "<PERSON><PERSON><PERSON> dạng gộp có điều kiện", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "<PERSON><PERSON><PERSON> dạng nguồn", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON> vị", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "<PERSON><PERSON><PERSON> trị + tất cả định dạng", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "<PERSON><PERSON><PERSON> trị + đ<PERSON><PERSON> dạng số", "SSE.Controllers.DocumentHolder.txtPasteValues": "Chỉ dán giá trị", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON> d<PERSON>u phân số", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON>óa gi<PERSON> hạn", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Xóa ký tự dấu phụ", "SSE.Controllers.DocumentHolder.txtRemoveBar": "<PERSON>óa v<PERSON>", "SSE.Controllers.DocumentHolder.txtRemScripts": "Xóa script", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Xóa chỉ số dưới", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Xóa chỉ số trên", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON> cao hàng", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Các script sau văn bản", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Các script trước văn bản", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON> thị giới hạn dưới", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Hiển thị dấu ngoặc đóng", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> thị cấp độ", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Hiển thị dấu ngoặc mở", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Hiển thị placeholder", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON> thị giới hạn trên", "SSE.Controllers.DocumentHolder.txtSorting": "<PERSON><PERSON><PERSON>p", "SSE.Controllers.DocumentHolder.txtSortSelected": "<PERSON><PERSON><PERSON> x<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "<PERSON>éo dài ngoặc", "SSE.Controllers.DocumentHolder.txtTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtUnderbar": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON><PERSON> bản", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> t<PERSON>h không tên", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON> thức", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON>n bộ nội dung ô", "SSE.Controllers.LeftMenu.textLookin": "<PERSON>hìn v<PERSON>o", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy dữ liệu bạn đang tìm kiếm. Vui lòng điều chỉnh các tùy chọn tìm kiếm của bạn.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON><PERSON> thực hiện thay thế. {0} lần xuất hiện đã bị bỏ qua.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> thực hiện tìm kiếm. Số lần thay thế: {0}", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON> t<PERSON>h", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON> trị", "SSE.Controllers.LeftMenu.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON> k<PERSON>ng", "SSE.Controllers.LeftMenu.textWorkbook": "Workbook", "SSE.Controllers.LeftMenu.warnDownloadAs": "Nếu bạn tiếp tục lưu ở định dạng này tất cả các tính năng trừ văn bản sẽ bị mất.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Controllers.Main.confirmMoveCellRange": "Phạm vi ô đích có thể chứa dữ liệu. Tiế<PERSON> tục thao tác?", "SSE.Controllers.Main.confirmPutMergeRange": "<PERSON><PERSON> liệu nguồn chứa các ô được gộp.<br><PERSON>úng đã không được gộp trước khi chúng được dán vào bảng này.", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON> quá thời gian chờ chuyển đổi.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON> \"OK\" để trở lại danh sách tài liệu.", "SSE.Controllers.Main.criticalErrorTitle": "Lỗi", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> về không thành công.", "SSE.Controllers.Main.downloadTextText": "<PERSON><PERSON> tải về bảng t<PERSON>h...", "SSE.Controllers.Main.downloadTitleText": "<PERSON><PERSON> tả<PERSON> về Bảng t<PERSON>h", "SSE.Controllers.Main.errorAccessDeny": "Bạn đang cố gắng thực hiện hành động mà bạn không có quyền.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> li<PERSON> củ<PERSON> bạn.", "SSE.Controllers.Main.errorArgsRange": "Lỗi trong công thức đã nhập.<br><PERSON><PERSON><PERSON> vi đối số không chính xác.", "SSE.Controllers.Main.errorAutoFilterChange": "<PERSON><PERSON> tác này không đ<PERSON><PERSON><PERSON>, vì nó đang cố gắng chuyển các ô trong một bảng vào trang tính của bạn.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "<PERSON>h<PERSON>ng thể thực hiện thao tác với các ô đã chọn vì bạn không thể di chuyển một phần của bảng.<br>Chọn phạm vi dữ liệu khác để toàn bộ bảng được di chuyển và thử lại.", "SSE.Controllers.Main.errorAutoFilterDataRange": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện thao tác cho phạm vi ô đã chọn.<br><PERSON>ọn phạm vi dữ liệu thống nhất khác với phạm vi hiện tại và thử lại.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện thao tác vì vùng này chứa các ô được lọc.<br><PERSON><PERSON> lòng bỏ ẩn các phần tử được lọc và thử lại.", "SSE.Controllers.Main.errorBadImageUrl": "URL hình ảnh không chính xác", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "<PERSON>ất kết nối server. K<PERSON>ông thể chỉnh sửa tài liệu ngay lúc này.", "SSE.Controllers.Main.errorConnectToServer": "<PERSON><PERSON><PERSON><PERSON> thể lưu tài liệu. Vui lòng kiểm tra cài đặt kết nối hoặc liên hệ với quản trị viên của bạn.<br><PERSON><PERSON> bạn nhấp vào nút 'OK', bạn sẽ được nhắc tải xuống tài liệu.", "SSE.Controllers.Main.errorCopyMultiselectArea": "<PERSON><PERSON><PERSON>ng thể sử dụng lệnh này với nhiều lựa chọn.<br><PERSON><PERSON><PERSON> một phạm vi và thử lại.", "SSE.Controllers.Main.errorCountArg": "Lỗi trong công thức đã nhập.<br><PERSON><PERSON><PERSON><PERSON> dùng đúng số lượng đối số.", "SSE.Controllers.Main.errorCountArgExceed": "Lỗi trong công thức đã nhập.<br><PERSON><PERSON> vượt quá số lượng đối số.", "SSE.Controllers.Main.errorCreateDefName": "Không thể chỉnh sửa các phạm vi được đặt tên hiện tại và hiện thời không thể tạo các phạm vi mới vì một số trong đó đang được chỉnh sửa.", "SSE.Controllers.Main.errorDatabaseConnection": "Lỗi bên ngoài.<br>Lỗi kết nối cơ sở dữ liệu. <PERSON>ui lòng liên hệ bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorDataRange": "Phạm vi dữ liệu không ch<PERSON>h xác.", "SSE.Controllers.Main.errorDefaultMessage": "Mã lỗi: %1", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON><PERSON> liệu được bảo vệ bằng mật khẩu và không thể mở được.", "SSE.Controllers.Main.errorFileRequest": "Lỗi bên ngoài.<br>Lỗi yêu cầu file. <PERSON><PERSON> lòng liên hệ với bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorFileVKey": "Lỗi bên ngoài.<br>Key bảo mật không chính xác. <PERSON><PERSON> lòng liên hệ với bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorFillRange": "<PERSON>h<PERSON>ng thể điền vào phạm vi các ô đã chọn.<br>T<PERSON><PERSON> cả các ô được gộp phải có cùng kích thước.", "SSE.Controllers.Main.errorFormulaName": "Lỗi trong công thức đã nhập.<br>Tê<PERSON> công thức không chính xác.", "SSE.Controllers.Main.errorFormulaParsing": "Lỗi nội bộ khi phân tích cú pháp công thức.", "SSE.Controllers.Main.errorFrmlWrongReferences": "<PERSON><PERSON>m này đề cập đến một trang tính không tồn tại.<br><PERSON><PERSON> lòng kiểm tra dữ liệu và thử lại.", "SSE.Controllers.Main.errorInvalidRef": "<PERSON><PERSON><PERSON><PERSON> đúng tên cho vùng chọn hoặc tham chiếu hợp lệ để đi đến.", "SSE.Controllers.Main.errorKeyEncrypt": "Key descriptor k<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorKeyExpire": "Key của descriptor đ<PERSON> hết hạn", "SSE.Controllers.Main.errorLockedAll": "<PERSON><PERSON> tác không thể được thực hiện vì trang tính đã bị khóa bởi một người dùng khác.", "SSE.Controllers.Main.errorLockedCellPivot": "<PERSON><PERSON><PERSON> không thể thay đổi dữ liệu bên trong pivot table.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Hiện tại bạn không thể đổi tên trang tính này vì nó đang được đổi tên bởi một người dùng khác", "SSE.Controllers.Main.errorMoveRange": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi phần ô đã gộp", "SSE.Controllers.Main.errorOpenWarning": "Chi<PERSON>u dài của một trong các công thức trong file này vượt quá<br>số ký tự cho phép và nó đã bị xóa.", "SSE.Controllers.Main.errorOperandExpected": "<PERSON><PERSON> pháp hàm được nhập vào không chính xác. <PERSON>ui lòng kiểm tra xem bạn có thiếu một trong các dấu ngoặc - '(' hoặc ')'.", "SSE.Controllers.Main.errorPasteMaxRange": "Vùng sao chép và dán không trùng khớp.<br><PERSON>ui lòng chọn vùng có cùng kích thước hoặc nhấp vào ô đầu tiên trong một hàng để dán các ô được sao chép.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON><PERSON>t tiếc là không thể cùng lúc in nhiều hơn 1500 trang trong phiên bản chương trình hiện tại.<br><PERSON>ạn chế này sẽ bị xóa trong các bản phát hành sắp tới.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON> không thành công", "SSE.Controllers.Main.errorServerVersion": "<PERSON><PERSON><PERSON> bản trình chỉnh sửa này đã được cập nhật. Trang sẽ được tải lại để áp dụng các thay đổi.", "SSE.Controllers.Main.errorSessionAbsolute": "Phiên chỉnh sửa tài liệu đã hết hạn. <PERSON><PERSON> lòng tải lại trang.", "SSE.Controllers.Main.errorSessionIdle": "Tài liệu đã không được chỉnh sửa trong một thời gian khá dài. <PERSON>ui lòng tải lại trang.", "SSE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON> nối với server bị gi<PERSON> đ<PERSON>. <PERSON><PERSON> lòng tải lại trang.", "SSE.Controllers.Main.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Controllers.Main.errorToken": "To<PERSON> bảo mật tài liệu không đư<PERSON><PERSON> tạo đúng.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "SSE.Controllers.Main.errorTokenExpire": "To<PERSON> bảo mật tài liệu đã hết hạn.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "SSE.Controllers.Main.errorUnexpectedGuid": "Lỗi bên ngoài.<br>GUID ngoài ý muốn. <PERSON><PERSON> lòng liên hệ với bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorUpdateVersion": "<PERSON><PERSON><PERSON> bản file này đã được thay đổi. Trang này sẽ được tải lại.", "SSE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON><PERSON> thể truy cập file ngay lúc này.", "SSE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON> vư<PERSON>t quá số người dùng đ<PERSON><PERSON><PERSON> phép của gói dịch vụ này", "SSE.Controllers.Main.errorViewerDisconnect": "<PERSON><PERSON>t kết nối. Bạn vẫn có thể xem tài liệu,<br>nhưng không thể tải về hoặc in cho đến khi kết nối được khôi phục.", "SSE.Controllers.Main.errorWrongBracketsCount": "Lỗi trong công thức đã nhập.<br>Đã sử dụng sai số dấu ngoặc.", "SSE.Controllers.Main.errorWrongOperator": "Lỗi trong công thức đã nhập. <PERSON><PERSON>ng sai toán tử.<br><PERSON><PERSON> lòng sửa lỗi.", "SSE.Controllers.Main.leavePageText": "Bạn có các thay đổi chưa lưu trong bảng tính này. Nhấp vào 'Ở lại Trang này' rồi 'Lư<PERSON>' để lưu chúng. Nhấp vào 'Rời trang này' để bỏ tất cả các thay đổi chưa lưu.", "SSE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON> tải dữ liệu...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON> t<PERSON> liệu", "SSE.Controllers.Main.loadFontTextText": "<PERSON><PERSON> tải dữ liệu...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON> t<PERSON> liệu", "SSE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON> tải bảng t<PERSON>h", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi mở file", "SSE.Controllers.Main.openTextText": "<PERSON><PERSON> mở bảng tính...", "SSE.Controllers.Main.openTitleText": "Mở bảng t<PERSON>h", "SSE.Controllers.Main.pastInMergeAreaError": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi phần ô đã gộp", "SSE.Controllers.Main.printTextText": "<PERSON><PERSON> in bảng tính...", "SSE.Controllers.Main.printTitleText": "<PERSON>ang in bảng tính", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.Main.requestEditFailedMessageText": "Hiện có ai đó đang chỉnh sửa tài liệu này. <PERSON><PERSON> lòng thử lại sau.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> bị từ chối", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi lưu file", "SSE.Controllers.Main.saveTextText": "<PERSON><PERSON> l<PERSON> bảng t<PERSON>...", "SSE.Controllers.Main.saveTitleText": "<PERSON><PERSON> l<PERSON> bảng <PERSON>", "SSE.Controllers.Main.textAnonymous": "Nặc danh", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON> c<PERSON>p trang web", "SSE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON><PERSON> để đóng gợi ý", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textContactUs": "<PERSON><PERSON><PERSON> hệ bộ phận bán hàng", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON> tải bảng t<PERSON>h", "SSE.Controllers.Main.textNo": "K<PERSON>ô<PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "<PERSON><PERSON><PERSON> bản mã nguồn mở ONLYOFFICE", "SSE.Controllers.Main.textPleaseWait": "<PERSON><PERSON> t<PERSON> này có thể lâu hơn dự kiến. <PERSON><PERSON> lòng chờ...", "SSE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON> d<PERSON>", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON> độ nghiêm ngặt", "SSE.Controllers.Main.textText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textTryUndoRedo": "<PERSON><PERSON><PERSON> chức năng Hoàn tác/<PERSON>à<PERSON> lại bị vô hiệu hóa cho chế độ đồng chỉnh sửa Nhanh.<br>Nhấp vào nút 'Chế độ nghiêm ngặt' để chuyển sang chế độ đồng chỉnh sửa Nghiêm ngặt để chỉnh sửa các file mà không có sự can thiệp của người dùng khác và gửi các thay đổi của bạn chỉ sau khi bạn đã lưu. Bạn có thể chuyển đổi giữa các chế độ đồng chỉnh sửa bằng cách sử dụng Cài đặt Nâng cao trình biên tập.", "SSE.Controllers.Main.textYes": "<PERSON><PERSON>", "SSE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> hết hạn", "SSE.Controllers.Main.titleServerVersion": "<PERSON><PERSON> cập nhật trình chỉnh sửa", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON> phụ", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON> bản của bạn ở đây", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON> d<PERSON>ng c<PERSON> bản", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtCallouts": "Callout", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON><PERSON> đồ", "SSE.Controllers.Main.txtDiagramTitle": "Tiêu đề biểu đồ", "SSE.Controllers.Main.txtEditingMode": "Đặt chế độ chỉnh sửa...", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON> tên có hình vẽ", "SSE.Controllers.Main.txtLines": "Đường kẻ", "SSE.Controllers.Main.txtMath": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON> chữ nhật", "SSE.Controllers.Main.txtSeries": "Chuỗi", "SSE.Controllers.Main.txtStarsRibbons": "Sao & Ruy-băng", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Ô đ<PERSON>h dấu kiểm", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "<PERSON><PERSON><PERSON> bản g<PERSON><PERSON> th<PERSON>ch", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Tiêu đề 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Tiêu đề 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Tiêu đề 3", "SSE.Controllers.Main.txtStyle_Heading_4": "T<PERSON>êu <PERSON> 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON> vào", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Ô đư<PERSON><PERSON> liên kết", "SSE.Controllers.Main.txtStyle_Neutral": "Trung tính", "SSE.Controllers.Main.txtStyle_Normal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON> ra", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Controllers.Main.txtStyle_Total": "<PERSON><PERSON><PERSON> cộng", "SSE.Controllers.Main.txtStyle_Warning_Text": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON>o", "SSE.Controllers.Main.txtXAxis": "Trục X", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON><PERSON><PERSON> Y", "SSE.Controllers.Main.unknownErrorText": "Lỗi không xác định.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Tr<PERSON><PERSON> du<PERSON>t của bạn không được hỗ trợ.", "SSE.Controllers.Main.uploadImageExtMessage": "Định dạng hình <PERSON>nh không xác đ<PERSON>nh.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh đư<PERSON> tải lên.", "SSE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON> vượt quá giới hạn kích thước tối đa của hình <PERSON>nh.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>", "SSE.Controllers.Main.warnBrowserIE9": "Ứng dụng vận hành kém trên IE9. Sử dụng IE10 hoặc cao hơn", "SSE.Controllers.Main.warnBrowserZoom": "Hiện cài đặt thu phóng trình duyệt của bạn không được hỗ trợ đầy đủ. <PERSON>ui lòng thiết lập lại chế độ thu phóng mặc định bằng cách nhấn Ctrl+0.", "SSE.Controllers.Main.warnLicenseExp": "G<PERSON><PERSON><PERSON> phép của bạn đã hết hạn.<br><PERSON><PERSON> lòng cập nhật gi<PERSON>y phép và làm mới trang.", "SSE.Controllers.Main.warnNoLicense": "Bạn đang sử dụng phiên bản nguồn mở của %1. <PERSON><PERSON>n bản có giới hạn các kết nối đồng thời với server tà<PERSON> liệu (20 kết nối cùng một lúc).<br><PERSON><PERSON><PERSON> bạn cần thêm, h<PERSON><PERSON> cân nhắc mua giấy phép thương mại.", "SSE.Controllers.Main.warnProcessRightsChange": "Bạn đã bị từ chối quyền chỉnh sửa file này.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON><PERSON> cả các trang t<PERSON>h", "SSE.Controllers.Print.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON> không ch<PERSON>h xác", "SSE.Controllers.Statusbar.errorLastSheet": "Workbook phải có ít nhất một bảng tính hiển thị.", "SSE.Controllers.Statusbar.errorRemoveSheet": "<PERSON><PERSON><PERSON><PERSON> thể xóa bảng t<PERSON>.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON> t<PERSON>h", "SSE.Controllers.Statusbar.warnDeleteSheet": "<PERSON><PERSON><PERSON> t<PERSON>h có thể chứa dữ liệu. Bạn có chắc là muốn tiếp tục?", "SSE.Controllers.Statusbar.zoomText": "<PERSON>hu phóng {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Phông chữ bạn sẽ lưu không có sẵn trên thiết bị hiện tại.<br>Ki<PERSON><PERSON> văn bản sẽ được hiển thị bằng một trong các phông chữ hệ thống, phông chữ đã lưu sẽ được sử dụng khi có sẵn.<br>Bạn có muốn tiếp tục?", "SSE.Controllers.Toolbar.errorMaxRows": "LỖI! Số chuỗi dữ liệu tối đa cho mỗi biểu đồ là 255", "SSE.Controllers.Toolbar.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON> phụ", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.textFontSizeErr": "Gi<PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON> lòng nhập một giá trị số giữa 1 và 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON> số", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.textLargeOperator": "<PERSON><PERSON> tử lớn", "SSE.Controllers.Toolbar.textLimitAndLog": "Giới hạn và Lô-ga", "SSE.Controllers.Toolbar.textLongOperation": "<PERSON><PERSON><PERSON> t<PERSON> dài", "SSE.Controllers.Toolbar.textMatrix": "<PERSON> trận", "SSE.Controllers.Toolbar.textOperator": "<PERSON><PERSON> tử", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> tên phải ở trên", "SSE.Controllers.Toolbar.txtAccent_Bar": "Gạch", "SSE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON><PERSON> trên", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> thứ<PERSON> đ<PERSON> (Có Placeholder)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON> thức đ<PERSON>g khung (Ví dụ)", "SSE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Ngoặc ôm ở dưới", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Ngoặc ôm ở trên", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC với Gạch trên", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Vớ<PERSON> trên", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Ba chấm", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON> đ<PERSON>i", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> trên k<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "<PERSON><PERSON> nhóm ký tự ở dưới", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "<PERSON><PERSON> nhóm ký tự ở trên", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON> c<PERSON>u trên hư<PERSON> về trái", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON> móc phải ở trên", "SSE.Controllers.Toolbar.txtAccent_Hat": "Mũ", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON><PERSON> ng<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (hai đi<PERSON><PERSON> ki<PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ba điều kiện)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON> dụ cho tr<PERSON><PERSON><PERSON> hợp", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON> số nhị thức", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON> số nhị thức", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtExpand": "Mở rộng và sắp xếp", "SSE.Controllers.Toolbar.txtExpandSort": "Dữ liệu bên cạnh vùng đã chọn sẽ không được sắp xếp. Bạn muốn mở rộng vùng chọn để bao gồm dữ liệu liền kề hay tiếp tục sắp xếp các ô đã chọn hiện tại?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON> số vi<PERSON><PERSON> l<PERSON>ch", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Vi phân", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Vi phân", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Vi phân", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Vi phân", "SSE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON> số viết ngang", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi hơn 2", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> số nhỏ", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON> số xếp chồng", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON> cos ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "<PERSON><PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON><PERSON><PERSON> cotg ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON><PERSON> cosec ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>-péc-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sec ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "<PERSON><PERSON><PERSON> sec <PERSON>y-p<PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON><PERSON> sin ngh<PERSON>ch đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON><PERSON> tan ngh<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON>m cosin", "SSE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON> cos <PERSON>-bôn", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON> co<PERSON>g", "SSE.Controllers.Toolbar.txtFunction_Coth": "<PERSON><PERSON><PERSON>b<PERSON>n", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON> cos", "SSE.Controllers.Toolbar.txtFunction_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>bôn", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON> thức tan", "SSE.Controllers.Toolbar.txtFunction_Sec": "Hàm sec", "SSE.Controllers.Toolbar.txtFunction_Sech": "<PERSON><PERSON><PERSON>n", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON> sin", "SSE.Controllers.Toolbar.txtFunction_Sinh": "<PERSON><PERSON><PERSON>n", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON> tan", "SSE.Controllers.Toolbar.txtFunction_Tanh": "<PERSON><PERSON><PERSON>n", "SSE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Vi phân của theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Vi phân của x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Vi phân của y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> phân kép", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân kép", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON> phân kép", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON> phân bề mặt", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "SSE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON> phân ba lớp", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "SSE.Controllers.Toolbar.txtInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON>í dụ giới hạn", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON> dụ Lớn nhất", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON> hạn", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON>-ga-r<PERSON>t tự nhiên", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Lô-ga-r<PERSON>t", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Lô-ga-r<PERSON>t", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Lớn n<PERSON>t", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Nhỏ nhất", "SSE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON> trận rỗng 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON> trận rỗng 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON> trận rỗng 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON> trận rỗng 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON> trận rỗng 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON> trận rỗng 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON> trận rỗng 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON> trận rỗng 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> chấm câu", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON>m gi<PERSON>a dòng", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> chấm chéo", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON> trận thưa", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON> trận thưa", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON> trận Đơn vị 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "<PERSON> trận Đơn vị 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "<PERSON> trận Đơn vị 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "<PERSON> trận Đơn vị 3x3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "<PERSON> chấm Bằng", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Lợi suất Delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Bằng với theo <PERSON> ngh<PERSON>a", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta bằng với", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Bằng bằng", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Trừ Bằng", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Cộng Bằng", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON> đo bằng", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> bậc hai có bậc", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hai", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Chỉ số dưới", "SSE.Controllers.Toolbar.txtScriptSubSup": "Chỉ số dưới-Chỉ số trên", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Chỉ số dưới-Chỉ số trên bên trái", "SSE.Controllers.Toolbar.txtScriptSup": "Chỉ số trên", "SSE.Controllers.Toolbar.txtSorting": "<PERSON><PERSON><PERSON>p", "SSE.Controllers.Toolbar.txtSortSelected": "<PERSON><PERSON><PERSON> x<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_about": "Xấp xỉ", "SSE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON> sung", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "SSE.Controllers.Toolbar.txtSymbol_approx": "Gần bằng với", "SSE.Controllers.Toolbar.txtSymbol_ast": "<PERSON><PERSON> t<PERSON> *", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON> tử Dấu đầu dòng", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON>m lửng nằm ngang giữa dòng", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Độ C", "SSE.Controllers.Toolbar.txtSymbol_chi": "X", "SSE.Controllers.Toolbar.txtSymbol_cong": "Xấp xỉ bằng với", "SSE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON><PERSON> lửng chéo xuống bên ph<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_degree": "Độ", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON> chia", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> tên xu<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> hợp rỗng", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Bằng", "SSE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON> tồn tại", "SSE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON> th<PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Độ F", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON> tất cả", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Lớn hơn hoặc bằng", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_in": "<PERSON>ần tử của", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON> h<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> tên trái", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON> tên <PERSON>-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leq": "Nhỏ hơn hoặc Bằng", "SSE.Controllers.Toolbar.txtSymbol_less": "Nhỏ hơn", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_minus": "Trừ", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Không bằng", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON><PERSON> ký hiệu", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON><PERSON> tồn tại", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "chữ 'o' ngắn", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Sai phân riêng", "SSE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>ng", "SSE.Controllers.Toolbar.txtSymbol_pm": "Cộng Trừ", "SSE.Controllers.Toolbar.txtSymbol_propto": "Tỷ lệ", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON> thứ tư", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> chứ<PERSON>h", "SSE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON> lửng chéo lên ph<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> tên bên ph<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> v<PERSON>y", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON> tên chỉ lên", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON><PERSON> thể Epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON><PERSON> thể của <PERSON>ho", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON><PERSON> thể theta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON> l<PERSON> d<PERSON>c", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON><PERSON> tác mà bạn sắp thực hiện có thể mất khá nhiều thời gian để hoàn thành.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Controllers.Toolbar.warnMergeLostData": "Chỉ dữ liệu từ ô phía trên bên trái sẽ vẫn nằm trong ô được gộp.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON> lọc tùy chỉnh", "SSE.Views.AutoFilterDialog.textAddSelection": "<PERSON><PERSON><PERSON><PERSON> lựa chọn hiện tại để lọc", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON> tất cả", "SSE.Views.AutoFilterDialog.textSelectAllResults": "<PERSON><PERSON><PERSON> tất cả kết quả tìm kiếm", "SSE.Views.AutoFilterDialog.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON>r<PERSON><PERSON> trung bình", "SSE.Views.AutoFilterDialog.txtBegins": "<PERSON><PERSON>t đầu với...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON><PERSON><PERSON><PERSON> trung bình", "SSE.Views.AutoFilterDialog.txtBetween": "Giữa...", "SSE.Views.AutoFilterDialog.txtClear": "Xóa", "SSE.Views.AutoFilterDialog.txtContains": "Chứa...", "SSE.Views.AutoFilterDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON> bộ lọc ô", "SSE.Views.AutoFilterDialog.txtEnds": "<PERSON><PERSON><PERSON> thúc bằng...", "SSE.Views.AutoFilterDialog.txtEquals": "Bằng...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "<PERSON><PERSON><PERSON> theo màu của ô", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "<PERSON><PERSON><PERSON> theo màu chữ", "SSE.Views.AutoFilterDialog.txtGreater": "L<PERSON><PERSON> hơn...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Lớn hơn hoặc bằng...", "SSE.Views.AutoFilterDialog.txtLess": "Nhỏ hơn...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Nhỏ hơn hoặc bằng...", "SSE.Views.AutoFilterDialog.txtNotBegins": "<PERSON><PERSON><PERSON><PERSON> bắt đầu bằng...", "SSE.Views.AutoFilterDialog.txtNotContains": "<PERSON><PERSON><PERSON><PERSON> chứa...", "SSE.Views.AutoFilterDialog.txtNotEnds": "kh<PERSON>ng kết thúc bằng...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Không bằng...", "SSE.Views.AutoFilterDialog.txtNumFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON><PERSON> d<PERSON> lại", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON><PERSON> xếp theo màu ô", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON><PERSON> xếp theo màu chữ", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "<PERSON><PERSON><PERSON> xếp cao nhất đến thấp nhất", "SSE.Views.AutoFilterDialog.txtSortLow2High": "<PERSON><PERSON><PERSON> x<PERSON><PERSON><PERSON> nhất lê<PERSON> nhất", "SSE.Views.AutoFilterDialog.txtTextFilter": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "SSE.Views.AutoFilterDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTop10": "Tốp 10", "SSE.Views.AutoFilterDialog.warnNoSelected": "Bạn phải chọn ít nhất một giá trị", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON> lý tên", "SSE.Views.CellEditor.tipFormula": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "LỖI! Số chuỗi dữ liệu tối đa cho mỗi biểu đồ là 255", "SSE.Views.CellRangeDialog.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.CellRangeDialog.txtInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON> phạm vi dữ liệu", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON> dày đường kẻ", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Template", "SSE.Views.ChartSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ChartSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "SSE.Views.ChartSettings.textChartType": "Thay đổi Loại biểu đồ", "SSE.Views.ChartSettings.textEditData": "Chỉnh sửa <PERSON> liệu và Vị trí", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON><PERSON><PERSON> đầu tiên", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON><PERSON><PERSON> cao", "SSE.Views.ChartSettings.textKeepRatio": "Tỷ lệ không đổi", "SSE.Views.ChartSettings.textLastPoint": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON>i", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON><PERSON><PERSON> thấp", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRanges": "Phạm vi dữ liệu", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON> thị", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxRows": "LỖI! Số chuỗi dữ liệu tối đa cho mỗi biểu đồ là 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Views.ChartSettingsDlg.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "<PERSON><PERSON> động", "SSE.Views.ChartSettingsDlg.textAutoEach": "T<PERSON> động cho từng", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "<PERSON><PERSON><PERSON><PERSON> giao nhau", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON> ch<PERSON> tr<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON> trí trục", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> tr<PERSON>", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c d<PERSON>u ki<PERSON>m", "SSE.Views.ChartSettingsDlg.textBillions": "Hàng tỷ", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON> da<PERSON> mục", "SSE.Views.ChartSettingsDlg.textCenter": "Trung tâm", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "<PERSON> tiết <PERSON><PERSON><PERSON><PERSON> đồ &<br><PERSON><PERSON> thích", "SSE.Views.ChartSettingsDlg.textChartTitle": "Tiêu đề biểu đồ", "SSE.Views.ChartSettingsDlg.textCross": "Chéo", "SSE.Views.ChartSettingsDlg.textCustom": "Tuỳ chỉnh", "SSE.Views.ChartSettingsDlg.textDataColumns": "trong cột", "SSE.Views.ChartSettingsDlg.textDataLabels": "<PERSON><PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.ChartSettingsDlg.textDataRows": "trong hàng", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON><PERSON> thị chú g<PERSON>i", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON><PERSON> ô ẩn và ô rỗng", "SSE.Views.ChartSettingsDlg.textEmptyLine": "<PERSON><PERSON>t nối các điểm dữ liệu với đường kẻ", "SSE.Views.ChartSettingsDlg.textFit": "Vừa với <PERSON> rộng", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON><PERSON> trống", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Sparkline Nhóm", "SSE.Views.ChartSettingsDlg.textHide": "Ẩn", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "Nằm ngang", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Trong", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON><PERSON><PERSON><PERSON> cùng bên trong", "SSE.Views.ChartSettingsDlg.textInnerTop": "Trên cùng bên trong", "SSE.Views.ChartSettingsDlg.textInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.ChartSettingsDlg.textLabelDist": "<PERSON><PERSON><PERSON><PERSON> cách nhãn trên trục", "SSE.Views.ChartSettingsDlg.textLabelInterval": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelPos": "<PERSON><PERSON> t<PERSON>", "SSE.Views.ChartSettingsDlg.textLayout": "Bố cục", "SSE.Views.ChartSettingsDlg.textLeft": "Trái", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON><PERSON><PERSON> chồng bên tr<PERSON>i", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Trái", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON><PERSON> th<PERSON>ch", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "Đường kẻ", "SSE.Views.ChartSettingsDlg.textLocationRange": "<PERSON>ạm vi địa điểm", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Chính và Phụ", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON><PERSON> công", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON> trị lớn nhất", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON> tri<PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "Loại phụ", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON><PERSON> trị nhỏ nhất", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON> trục", "SSE.Views.ChartSettingsDlg.textNone": "K<PERSON>ô<PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON><PERSON><PERSON> chồng", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Bằng dấu kiểm", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "<PERSON><PERSON><PERSON><PERSON> cùng bên ngo<PERSON>i", "SSE.Views.ChartSettingsDlg.textOverlay": "<PERSON><PERSON><PERSON> chồ<PERSON>", "SSE.Views.ChartSettingsDlg.textReverse": "<PERSON><PERSON><PERSON> trị theo thứ tự ngược", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Tr<PERSON><PERSON> tự đả<PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON><PERSON> chồng bên <PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON><PERSON><PERSON> tự cho Tất cả", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.ChartSettingsDlg.textSeparator": "<PERSON><PERSON><PERSON> phân cách nhãn dữ liệu", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON>n chuỗi", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON><PERSON> thị", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON>ển thị đường viền biểu đồ", "SSE.Views.ChartSettingsDlg.textShowData": "<PERSON><PERSON><PERSON> thị dữ liệu trong các hàng và cột ẩn", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON><PERSON><PERSON> thị các ô trống dưới dạng", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON> thị trục", "SSE.Views.ChartSettingsDlg.textShowValues": "<PERSON><PERSON><PERSON> thị các giá trị biểu đồ", "SSE.Views.ChartSettingsDlg.textSingle": "Sparkline đơn", "SSE.Views.ChartSettingsDlg.textSmooth": "Trơn", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Phạm vi Sparkline", "SSE.Views.ChartSettingsDlg.textStraight": "Thẳng", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON>n", "SSE.Views.ChartSettingsDlg.textTickOptions": "<PERSON><PERSON><PERSON> ch<PERSON>n d<PERSON>u k<PERSON>", "SSE.Views.ChartSettingsDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> đồ - <PERSON>ài đặt Nâng cao", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Cài đặt Nâng cao", "SSE.Views.ChartSettingsDlg.textTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrillions": "Hàng tỷ", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Loại & Dữ liệu", "SSE.Views.ChartSettingsDlg.textUnits": "<PERSON><PERSON><PERSON> thị đơn vị", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON> trị", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Ti<PERSON><PERSON> đề Trục X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "<PERSON>i<PERSON><PERSON> đ<PERSON>ụ<PERSON>", "SSE.Views.ChartSettingsDlg.textZero": "0", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.DigitalFilterDialog.capAnd": "Và", "SSE.Views.DigitalFilterDialog.capCondition1": "bằng", "SSE.Views.DigitalFilterDialog.capCondition10": "kh<PERSON>ng kết thúc bằng", "SSE.Views.DigitalFilterDialog.capCondition11": "ch<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>a", "SSE.Views.DigitalFilterDialog.capCondition2": "không bằng", "SSE.Views.DigitalFilterDialog.capCondition3": "l<PERSON><PERSON> h<PERSON>n", "SSE.Views.DigitalFilterDialog.capCondition4": "lớn hơn hoặc bằng", "SSE.Views.DigitalFilterDialog.capCondition5": "nhỏ hơn", "SSE.Views.DigitalFilterDialog.capCondition6": "nhỏ hơn hoặc bằng", "SSE.Views.DigitalFilterDialog.capCondition7": "b<PERSON>t đ<PERSON>u với", "SSE.Views.DigitalFilterDialog.capCondition8": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t đầu bằng", "SSE.Views.DigitalFilterDialog.capCondition9": "kết thúc bằng", "SSE.Views.DigitalFilterDialog.capOr": "Hoặc", "SSE.Views.DigitalFilterDialog.textNoFilter": "k<PERSON><PERSON><PERSON> có bộ lọc", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON> thị các hàng có", "SSE.Views.DigitalFilterDialog.textUse1": "Sử dụng ? để thể hiện ký tự đơn bất kỳ", "SSE.Views.DigitalFilterDialog.textUse2": "Sử dụng * để hiển thị chuỗi ký tự bất kỳ", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON> lọc tùy chỉnh", "SSE.Views.DocumentHolder.advancedImgText": "Cài đặt Hình <PERSON>nh <PERSON> cao", "SSE.Views.DocumentHolder.advancedShapeText": "Cài đặt Nâng cao Hình dạng", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.DocumentHolder.bulletsText": "<PERSON><PERSON><PERSON> đầu dòng và Số thứ tự", "SSE.Views.DocumentHolder.centerCellText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.chartText": "Cài đặt Nâng cao Biểu đồ", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản lên", "SSE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản x<PERSON>", "SSE.Views.DocumentHolder.directHText": "Nằm ngang", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "SSE.Views.DocumentHolder.editChartText": "Chỉnh sửa <PERSON> liệu", "SSE.Views.DocumentHolder.editHyperlinkText": "Chỉnh sửa <PERSON><PERSON><PERSON> liên kết", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> trái", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> trên", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON> si<PERSON>u liên k<PERSON>t", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> bộ cột", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON> li<PERSON> c<PERSON>t", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.DocumentHolder.textArrangeBackward": "Gửi về phía sau", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON> chuyển tiến lên", "SSE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON> lên <PERSON> cảnh", "SSE.Views.DocumentHolder.textEntriesList": "<PERSON><PERSON><PERSON> từ danh sách thả xuống", "SSE.Views.DocumentHolder.textFreezePanes": "Freeze Panes", "SSE.Views.DocumentHolder.textNone": "K<PERSON>ô<PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Unfreeze Panes", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>p", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Tự động vừa với chiều rộng của cột", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Tự động vừa với chiều cao của hàng", "SSE.Views.DocumentHolder.txtClear": "Xóa", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "SSE.Views.DocumentHolder.txtClearHyper": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "<PERSON><PERSON><PERSON> c<PERSON>c nhóm Sparkline đã Chọn", "SSE.Views.DocumentHolder.txtClearSparklines": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> Sparkline đã chọn", "SSE.Views.DocumentHolder.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON> bộ cột", "SSE.Views.DocumentHolder.txtColumnWidth": "Đặt chiều rộng cột", "SSE.Views.DocumentHolder.txtCopy": "Sao chép", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON><PERSON> rộng cột tùy chỉnh", "SSE.Views.DocumentHolder.txtCustomRowHeight": "<PERSON><PERSON><PERSON> cao cột tùy chỉnh", "SSE.Views.DocumentHolder.txtCut": "Cắt", "SSE.Views.DocumentHolder.txtDelete": "Xóa", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtEditComment": "Chỉnh sửa bình luận", "SSE.Views.DocumentHolder.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilterCellColor": "<PERSON><PERSON><PERSON> theo màu của ô", "SSE.Views.DocumentHolder.txtFilterFontColor": "<PERSON><PERSON><PERSON> theo màu chữ", "SSE.Views.DocumentHolder.txtFilterValue": "<PERSON><PERSON><PERSON> theo giá trị của ô đã chọn", "SSE.Views.DocumentHolder.txtFormula": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.DocumentHolder.txtGroup": "Nhóm", "SSE.Views.DocumentHolder.txtHide": "Ẩn", "SSE.Views.DocumentHolder.txtInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.DocumentHolder.txtPaste": "Dán", "SSE.Views.DocumentHolder.txtReapply": "<PERSON><PERSON> d<PERSON> lại", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON> bộ hàng", "SSE.Views.DocumentHolder.txtRowHeight": "Đặt chi<PERSON>u cao cột", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON><PERSON> ô xuống dưới", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON><PERSON> ô sang trái", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON><PERSON> ô sang phải", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON><PERSON> ô lên trên", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON> thị", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON> thị bình luận", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON>p", "SSE.Views.DocumentHolder.txtSortCellColor": "<PERSON><PERSON><PERSON> được chọn trên cùng", "SSE.Views.DocumentHolder.txtSortFontColor": "<PERSON><PERSON><PERSON> phông chữ được chọn trên cùng", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtTextAdvanced": "Cài đặt Nâng cao <PERSON> bản", "SSE.Views.DocumentHolder.txtUngroup": "Bỏ nhóm", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON> chỉnh dọc", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON> tới Tài liệu", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON> mới", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> về dưới dạng", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.FileMenu.btnInfoCaption": "Thông tin Bảng tính", "SSE.Views.FileMenu.btnPrintCaption": "In", "SSE.Views.FileMenu.btnRecentFilesCaption": "Mở gần đây", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON> tên", "SSE.Views.FileMenu.btnReturnCaption": "Quay lại B<PERSON>h", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON><PERSON> truy cập", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON> dạng", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSettingsCaption": "Cài đặt nâng cao", "SSE.Views.FileMenu.btnToEditCaption": "Chỉnh s<PERSON>a <PERSON>h", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Tác g<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>ảng t<PERSON>h", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Chế độ đồng chỉnh sửa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Phông chữ gợi ý", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "<PERSON><PERSON><PERSON> ngữ công thức", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Ví dụ: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "<PERSON><PERSON><PERSON><PERSON> lập khu vực", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "<PERSON><PERSON> dụ:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Nghiêm ngặt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Đơn vị đo lư<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "<PERSON><PERSON><PERSON> trị <PERSON> to Mặc định", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Mỗi 10 phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Mỗi 30 phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Mỗi 5 phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Mỗi giờ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Tự động khôi phục", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "<PERSON><PERSON> động lưu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Tắt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Lưu vào Server", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Mỗi phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON><PERSON> <PERSON>h", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Inch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "như OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "như Windows", "SSE.Views.FormatRulesEditDlg.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "<PERSON><PERSON><PERSON><PERSON> phân", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "SSE.Views.FormatSettingsDialog.textSeparator": "Sử dụng phân tách 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> dạng số", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON> toán", "SSE.Views.FormatSettingsDialog.txtAs10": "Bằng ph<PERSON>n mư<PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Bằng phần trăm (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Bằng ph<PERSON>n mư<PERSON><PERSON> s<PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Bằng m<PERSON><PERSON> n<PERSON>a (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Bằng ph<PERSON>n tư (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Bằng ph<PERSON>n tám (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Tuỳ chỉnh", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "<PERSON><PERSON> số", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON><PERSON> quát", "SSE.Views.FormatSettingsDialog.txtNumber": "Số", "SSE.Views.FormatSettingsDialog.txtPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.FormatSettingsDialog.txtSample": "Mẫu:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON><PERSON> gian", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON><PERSON><PERSON> đa một chữ số (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON>i đa hai chữ số (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON><PERSON> đa ba chữ số (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.HeaderFooterDialog.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON> thị", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON> kết t<PERSON>i", "SSE.Views.HyperlinkSettingsDialog.strRange": "Phạm vi", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON> t<PERSON>h", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Phạm vi đã chọn", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> đầu đề ở đây", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON><PERSON> liên kết ở đây", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Nhập tooltip ở đây", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON> k<PERSON> ng<PERSON>i", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Phạm vi dữ liệu nội bộ", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON><PERSON> b<PERSON>n <PERSON>ip", "SSE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> đặt <PERSON><PERSON>u liên kết", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "SSE.Views.ImageSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ImageSettings.textEdit": "Chỉnh sửa", "SSE.Views.ImageSettings.textEditObject": "Chỉnh sửa <PERSON><PERSON> tư<PERSON>", "SSE.Views.ImageSettings.textFromFile": "Từ file", "SSE.Views.ImageSettings.textFromUrl": "Từ URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON> thế <PERSON>nh", "SSE.Views.ImageSettings.textKeepRatio": "Tỷ lệ không đổi", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON> thước mặc định", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ImageSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Hình <PERSON>nh - Cài đặt Nâng cao", "SSE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.LeftMenu.tipFile": "File", "SSE.Views.LeftMenu.tipPlugins": "Plugin", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSupport": "<PERSON><PERSON><PERSON> & Hỗ trợ", "SSE.Views.LeftMenu.txtDeveloper": "CHẾ ĐỘ NHÀ PHÁT TRIỂN", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.MainSettingsPrint.strLandscape": "Nằm ngang", "SSE.Views.MainSettingsPrint.strLeft": "Trái", "SSE.Views.MainSettingsPrint.strMargins": "Lề", "SSE.Views.MainSettingsPrint.strPortrait": "Thẳng đứng", "SSE.Views.MainSettingsPrint.strPrint": "In", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON> th<PERSON> thực", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>t trên <PERSON> trang", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON><PERSON> cho vừa Bảng t<PERSON>h trên <PERSON> trang", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>ng trên <PERSON> trang", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON><PERSON><PERSON> trang", "SSE.Views.MainSettingsPrint.textPageScaling": "Cân chỉnh", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON> trang", "SSE.Views.MainSettingsPrint.textPrintGrid": "In đường lưới", "SSE.Views.MainSettingsPrint.textPrintHeadings": "In tiêu đề hàng và cột", "SSE.Views.MainSettingsPrint.textSettings": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p cho", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Không thể chỉnh sửa các phạm vi được đặt tên hiện tại và hiện thời không thể tạo các phạm vi mới vì một số trong đó đang được chỉnh sửa.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "<PERSON>ên đã định ngh<PERSON>a", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Phạm vi dữ liệu", "SSE.Views.NamedRangeEditDlg.textExistName": "LỖI! Phạ<PERSON> vi có tên như vậy đã tồn tại", "SSE.Views.NamedRangeEditDlg.textInvalidName": "<PERSON><PERSON>n phải bắt đầu bằng một chữ cái hoặc một gạch dưới và không được chứa các ký tự không hợp lệ.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.NamedRangeEditDlg.textIsLocked": "LỖI! Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "Tên bạn đang cố gắng sử dụng đã đư<PERSON><PERSON> tham chiếu trong các công thức ô. <PERSON><PERSON> lòng sử dụng tên khác.", "SSE.Views.NamedRangeEditDlg.textScope": "Phạm vi", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Chỉnh s<PERSON>a <PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON> m<PERSON>i", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON> vi được đặt tên", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.closeButtonText": "Đ<PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Phạm vi dữ liệu", "SSE.Views.NameManagerDlg.textDelete": "Xóa", "SSE.Views.NameManagerDlg.textEdit": "Chỉnh sửa", "SSE.Views.NameManagerDlg.textEmpty": "<PERSON><PERSON><PERSON> có phạm vi được đặt tên nào được tạo.<br>Tạo ít nhất một phạm vi được đặt tên và nó sẽ xuất hiện trong trường này.", "SSE.Views.NameManagerDlg.textFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON>ên đã định ngh<PERSON>a", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON> tìm cho trang tính", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.NameManagerDlg.textFilterWorkbook": "<PERSON><PERSON><PERSON><PERSON><PERSON> tìm cho Workbook", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phạm vi được đặt tên phù hợp với tiêu chí lọc của bạn.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON> vi được đặt tên", "SSE.Views.NameManagerDlg.textScope": "Phạm vi", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON> lý tên", "SSE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON><PERSON> cách dòng", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch đo<PERSON>n", "SSE.Views.ParagraphSettings.strSpacingAfter": "Sau", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ParagraphSettings.textAt": "Tại", "SSE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON> động", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "<PERSON><PERSON><PERSON> tab được chỉ định sẽ xuất hiện trong trường này", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Tất cả Drop cap", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON>ch đôi giữa chữ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Trái", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Phông chữ", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Thụt lề & Căn chỉnh", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Drop cap nhỏ", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON> gi<PERSON>a chữ", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Chỉ số dưới", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Chỉ số trên", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON>n chỉnh", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON> c<PERSON>ch trắng", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Tab mặc định", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Xóa", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON> tất cả", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Trung tâm", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Trái", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON> trí <PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON><PERSON> văn bản - <PERSON><PERSON>i đặt Nâng cao", "SSE.Views.PrintSettings.btnPrint": "Lưu & In", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.PrintSettings.strLandscape": "Nằm ngang", "SSE.Views.PrintSettings.strLeft": "Trái", "SSE.Views.PrintSettings.strMargins": "Lề", "SSE.Views.PrintSettings.strPortrait": "Thẳng đứng", "SSE.Views.PrintSettings.strPrint": "In", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON> th<PERSON> thực", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON><PERSON> cả các trang t<PERSON>h", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON> t<PERSON>h hiện tại", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>t trên <PERSON> trang", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON><PERSON> cho vừa Bảng t<PERSON>h trên <PERSON> trang", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>ng trên <PERSON> trang", "SSE.Views.PrintSettings.textHideDetails": "Ẩn chi tiết", "SSE.Views.PrintSettings.textLayout": "Bố cục", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON><PERSON><PERSON> trang", "SSE.Views.PrintSettings.textPageScaling": "Cân chỉnh", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON> trang", "SSE.Views.PrintSettings.textPrintGrid": "In đường lưới", "SSE.Views.PrintSettings.textPrintHeadings": "In tiêu đề hàng và cột", "SSE.Views.PrintSettings.textPrintRange": "Phạm vi in", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Cài đặt trang tính", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON> thị chi tiết", "SSE.Views.PrintSettings.textTitle": "Cài đặt In", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON>ài đặt hình ảnh", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON>ài đặt văn bản", "SSE.Views.RightMenu.txtSettings": "<PERSON>ài đặt chung", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON>ài đặt hình dạng", "SSE.Views.RightMenu.txtSparklineSettings": "Cài đặt Sparkline", "SSE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON>i đặt bảng", "SSE.Views.RightMenu.txtTextArtSettings": "Cài đặt chữ Nghệ thuật", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON><PERSON> trị lớn nhất cho trường này là {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON><PERSON> trị nhỏ nhất cho trường này là {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strChange": "Thay đ<PERSON>i Autoshape", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON> màu", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON> văn", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON> mờ", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON> màu", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "SSE.Views.ShapeSettings.textFromFile": "Từ file", "SSE.Views.ShapeSettings.textFromUrl": "Từ URL", "SSE.Views.ShapeSettings.textGradient": "Gradient", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "SSE.Views.ShapeSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> ban đầu", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON> văn", "SSE.Views.ShapeSettings.textRadial": "Tỏa tròn", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Từ Texture", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "SSE.Views.ShapeSettings.txtGrain": "Thớ gỗ", "SSE.Views.ShapeSettings.txtGranite": "Đá granite", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON> xen", "SSE.Views.ShapeSettings.txtLeather": "Da", "SSE.Views.ShapeSettings.txtNoBorders": "Không đường kẻ", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.ShapeSettings.txtWood": "Gỗ", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON><PERSON> padding cho vă<PERSON> bản", "SSE.Views.ShapeSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON> tên", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> thước khởi đầu", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON>u khởi đầu", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Số cột", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON> kết th<PERSON>c", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Phẳng", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Tỷ lệ không đổi", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Trái", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON> đường kẻ", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Góc 45 độ", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRound": "Tròn", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch gi<PERSON><PERSON> các c<PERSON>t", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTitle": "<PERSON><PERSON>nh dạng - <PERSON>ài đặt Nâng cao", "SSE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "<PERSON><PERSON> & <PERSON><PERSON><PERSON> tên", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON> ch<PERSON><PERSON> đến cuối)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON> chuyển đến cuối)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "<PERSON>o chép tr<PERSON><PERSON><PERSON> trang tính", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON> chuyển trư<PERSON><PERSON> trang tính", "SSE.Views.Statusbar.filteredRecordsText": "{0} trên {1} bản <PERSON><PERSON><PERSON><PERSON> l<PERSON>c", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON> độ bộ lọc", "SSE.Views.Statusbar.itemCopy": "Sao chép", "SSE.Views.Statusbar.itemDelete": "Xóa", "SSE.Views.Statusbar.itemHidden": "Ẩn", "SSE.Views.Statusbar.itemHide": "Ẩn", "SSE.Views.Statusbar.itemInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMove": "<PERSON>", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON> tên", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "<PERSON><PERSON><PERSON> t<PERSON>h với tên như vậy đã tồn tại.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Tên trang tính không đ<PERSON><PERSON><PERSON> chứa các ký tự sau: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON><PERSON> trang t<PERSON>h", "SSE.Views.Statusbar.textAverage": "TRUNG BÌNH", "SSE.Views.Statusbar.textCount": "COUNT", "SSE.Views.Statusbar.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON><PERSON> màu", "SSE.Views.Statusbar.textSum": "SUM", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON><PERSON> bảng <PERSON>h", "SSE.Views.Statusbar.tipFirst": "<PERSON><PERSON><PERSON><PERSON> bảng t<PERSON>h đầu tiên", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON><PERSON> bảng t<PERSON>h cuối cùng", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch bả<PERSON> t<PERSON> sang phải", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch bả<PERSON> t<PERSON> sang trái", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON> ph<PERSON>g", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON> to", "SSE.Views.Statusbar.tipZoomOut": "<PERSON>hu nhỏ", "SSE.Views.Statusbar.zoomText": "<PERSON>hu phóng {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện thao tác cho phạm vi ô đã chọn.<br><PERSON>ọn phạm vi dữ liệu thống nhất khác với phạm vi hiện tại và thử lại.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "<PERSON>hông thể hoàn tất thao tác cho phạm vi ô đã chọn.<br>Chọn một phạm vi sao cho cùng trùng hàng đầu của bảng<br> và bảng kết quả chồng chéo lên bảng hiện tại.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "<PERSON>hông thể hoàn tất thao tác cho phạm vi ô đã chọn.<br><PERSON>ọn một phạm vi không bao gồm các bảng khác.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON><PERSON> c<PERSON>t bên trái", "SSE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON> c<PERSON>t bên ph<PERSON>i", "SSE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> hàng bên trên", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> hàng bên <PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON> toàn bộ cột", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON> dữ liệu cột", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.TableSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON> d<PERSON>i màu", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> sang phạm vi", "SSE.Views.TableSettings.textEdit": "Hàng & Cột", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON>ng có template", "SSE.Views.TableSettings.textExistName": "LỖI! Phạ<PERSON> vi có tên như vậy đã tồn tại", "SSE.Views.TableSettings.textFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON> tiên", "SSE.Views.TableSettings.textHeader": "Header", "SSE.Views.TableSettings.textInvalidName": "LỖI! T<PERSON>n bảng không hợp lệ", "SSE.Views.TableSettings.textIsLocked": "Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.TableSettings.textLongOperation": "<PERSON><PERSON><PERSON> t<PERSON> dài", "SSE.Views.TableSettings.textReservedName": "Tên bạn đang cố gắng sử dụng đã đư<PERSON><PERSON> tham chiếu trong các công thức ô. <PERSON><PERSON> lòng sử dụng tên khác.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON> đ<PERSON>i kích th<PERSON><PERSON><PERSON> bảng", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON> từ Template", "SSE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON> cộng", "SSE.Views.TableSettings.warnLongOperation": "<PERSON><PERSON> tác mà bạn sắp thực hiện có thể mất khá nhiều thời gian để hoàn thành.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Views.TableSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Bảng - Cài đặt Nâng cao", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON> màu", "SSE.Views.TextArtSettings.strForeground": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON> văn", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON> mờ", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON> màu", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "SSE.Views.TextArtSettings.textFromFile": "Từ file", "SSE.Views.TextArtSettings.textFromUrl": "Từ URL", "SSE.Views.TextArtSettings.textGradient": "Gradient", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "SSE.Views.TextArtSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON> văn", "SSE.Views.TextArtSettings.textRadial": "Tỏa tròn", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "Từ Texture", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON><PERSON> đổi", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "SSE.Views.TextArtSettings.txtGrain": "Thớ gỗ", "SSE.Views.TextArtSettings.txtGranite": "Đá granite", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON> xen", "SSE.Views.TextArtSettings.txtLeather": "Da", "SSE.Views.TextArtSettings.txtNoBorders": "Không đường kẻ", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.TextArtSettings.txtWood": "Gỗ", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON><PERSON> đồ", "SSE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON> trình", "SSE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "<PERSON><PERSON><PERSON> d<PERSON>", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON> từ file", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON> từ URL", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON><PERSON> trung tâm", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON> đ<PERSON>u", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON> cả viền", "SSE.Views.Toolbar.textBold": "Đậm", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON><PERSON><PERSON> viền dư<PERSON>i cùng", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON><PERSON><PERSON> viền dọc bên trong", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON><PERSON> theo chiều kim đồng hồ", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON><PERSON><PERSON><PERSON> chiều kim đồng hồ", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON><PERSON><PERSON> ô sang trái", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON><PERSON> ô lên trên", "SSE.Views.Toolbar.textDiagDownBorder": "<PERSON><PERSON><PERSON><PERSON> viền đường chéo xuống", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON> viền đường chéo lên", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON> bộ cột", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON> bộ hàng", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON> b<PERSON>n ngang", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON><PERSON> ô xuống dưới", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON> viền trong", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON><PERSON> ô sang phải", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON> viền bên trái", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON> viền ngang bên trong", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> dạng", "SSE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON><PERSON> viền ngoài", "SSE.Views.Toolbar.textPrint": "In", "SSE.Views.Toolbar.textPrintOptions": "Cài đặt In", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON><PERSON> viền phải", "SSE.Views.Toolbar.textRotateDown": "<PERSON><PERSON><PERSON> v<PERSON>n bản x<PERSON>", "SSE.Views.Toolbar.textRotateUp": "<PERSON><PERSON><PERSON> v<PERSON>n bản lên", "SSE.Views.Toolbar.textTabFile": "File", "SSE.Views.Toolbar.textTabHome": "Trang chủ", "SSE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON><PERSON> viền trên cùng", "SSE.Views.Toolbar.textUnderline": "G<PERSON><PERSON> chân", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON> ph<PERSON>g", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON> trung tâm", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON> đ<PERSON>u", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "SSE.Views.Toolbar.tipAutofilter": "Sắp xếp v<PERSON>", "SSE.Views.Toolbar.tipBack": "Quay lại", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipClearStyle": "Xóa", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON>hay đ<PERSON>i <PERSON> màu", "SSE.Views.Toolbar.tipCopy": "Sao chép", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON>o ch<PERSON>p kiểu", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON> thập phân", "SSE.Views.Toolbar.tipDecFont": "Giảm cỡ chữ", "SSE.Views.Toolbar.tipDeleteOpt": "Xóa Ô", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON><PERSON> k<PERSON> toán", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON> ti<PERSON>n tệ", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON><PERSON> ph<PERSON>n trăm", "SSE.Views.Toolbar.tipEditChart": "Chỉnh sửa biểu đồ", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON> chữ", "SSE.Views.Toolbar.tipFontName": "Phông chữ", "SSE.Views.Toolbar.tipFontSize": "Cỡ chữ", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON> thập phân", "SSE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON> kích thước phông chữ", "SSE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> bi<PERSON>u đồ", "SSE.Views.Toolbar.tipInsertChartSpark": "<PERSON><PERSON>n biểu đồ hoặc Sparkline", "SSE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON> siêu liên kết", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertShape": "Chèn Autoshape", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "SSE.Views.Toolbar.tipInsertTextart": "<PERSON><PERSON><PERSON> chữ nghệ thuật", "SSE.Views.Toolbar.tipMerge": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "<PERSON><PERSON><PERSON> dạng số", "SSE.Views.Toolbar.tipPaste": "Dán", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrint": "In", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> thay đổi của bạn để những người dùng khác thấy chúng.", "SSE.Views.Toolbar.tipSynchronize": "Tài liệu đã được thay đổi bởi một người dùng khác. <PERSON><PERSON> lòng nhấp để lưu thay đổi của bạn và tải lại các cập nhật.", "SSE.Views.Toolbar.tipTextOrientation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON> dòng", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON> toán", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON> sung", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Xóa bộ lọc", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.Toolbar.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.Toolbar.txtCustom": "Tuỳ chỉnh", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDateTime": "Ngày & Giờ", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Đô la", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "<PERSON><PERSON><PERSON> th<PERSON>a", "SSE.Views.Toolbar.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFormula": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.Toolbar.txtFraction": "<PERSON><PERSON> số", "SSE.Views.Toolbar.txtFranc": "CHF franc <PERSON>", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON><PERSON> quát", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON> ng<PERSON>ên", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON> lý tên", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON> hợ<PERSON> ô sang cột & không canh giữa", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON> nhi<PERSON>u ô và không canh giữa", "SSE.Views.Toolbar.txtMergeCenter": "<PERSON><PERSON><PERSON> h<PERSON> & <PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON> vi được đặt tên", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.txtNumber": "Số", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.Toolbar.txtPound": "£ Bảng", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme1": "Office", "SSE.Views.Toolbar.txtScheme10": "Median", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Opulent", "SSE.Views.Toolbar.txtScheme14": "Oriel", "SSE.Views.Toolbar.txtScheme15": "Origin", "SSE.Views.Toolbar.txtScheme16": "Paper", "SSE.Views.Toolbar.txtScheme17": "Solstice", "SSE.Views.Toolbar.txtScheme18": "Technic", "SSE.Views.Toolbar.txtScheme19": "Trek", "SSE.Views.Toolbar.txtScheme2": "Grayscale", "SSE.Views.Toolbar.txtScheme20": "Urban", "SSE.Views.Toolbar.txtScheme21": "Verve", "SSE.Views.Toolbar.txtScheme3": "Apex", "SSE.Views.Toolbar.txtScheme4": "Aspect", "SSE.Views.Toolbar.txtScheme5": "Civic", "SSE.Views.Toolbar.txtScheme6": "Concourse", "SSE.Views.Toolbar.txtScheme7": "Equity", "SSE.Views.Toolbar.txtScheme8": "Flow", "SSE.Views.Toolbar.txtScheme9": "Foundry", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON>p", "SSE.Views.Toolbar.txtSortAZ": "<PERSON><PERSON><PERSON> xếp tăng d<PERSON>n", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.Toolbar.txtSpecial": "Đặc biệt", "SSE.Views.Toolbar.txtTableTemplate": "<PERSON><PERSON><PERSON> d<PERSON> n<PERSON>ư Template <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON><PERSON> gian", "SSE.Views.Toolbar.txtUnmerge": "Bỏ gộp ô", "SSE.Views.Toolbar.txtYen": "¥ Yên", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON> thị", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.Top10FilterDialog.txtTitle": "<PERSON><PERSON> động l<PERSON> Tốp 10", "SSE.Views.Top10FilterDialog.txtTop": "<PERSON><PERSON><PERSON>"}