﻿<!DOCTYPE html>
<html>
	<head>
		<title>TEXT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>TEXT-Funktion</h1>
			<p>Die Funktion <b>TEXT</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie formatiert eine Zahl und wandelt sie in Text um.</p>
			<p>Die Formelsyntax der Funktion <b>TEXT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>TEXT(Wert;Textformat)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Wert</em></b> ist ein numerischer Wert, der in Text umgewandelt werden soll.</p> 
			<p style="text-indent: 50px;"><b><em>Textformat</em></b> ist eine Textzeichenfolge, in der die Formatierung definiert ist, die auf den angegebenen Wert angewendet werden soll.</p> 
			<p>Der nummerische Wert wird manuell eingegeben oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>TEXT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>TEXT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="TEXT-Funktion" src="../images/text.png" /></p>
		</div>
	</body>
</html>