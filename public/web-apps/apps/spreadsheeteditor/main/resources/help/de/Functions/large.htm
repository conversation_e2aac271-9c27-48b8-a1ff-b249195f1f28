﻿<!DOCTYPE html>
<html>
	<head>
		<title>KGRÖSSTE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>KGRÖSSTE-Funktion</h1>
			<p>Die Funktion <b>KGRÖSSTE</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den k-größten Wert eines Datasets in einem bestimmten Zellbereich zurück</p>
			<p>Die Formelsyntax der Funktion <b>KGRÖSSTE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>KGRÖSSTE(Matrix;k)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Matrix</em></b> ist die Matrix oder der Datenbereich, deren k-größten Wert Sie bestimmen möchten.</p>
				<p style="text-indent: 50px;"><b><em>k</em></b> ist der Rang des Elements einer Matrix oder eines Zellbereichs, dessen Wert zurückgegeben werden soll. Ein nummerischer Wert, der größer ist als 0. Der Wert wird manuell eingegeben oder ist in die Zelle eingeschossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>KGRÖSSTE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>KGRÖSSTE</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="KGRÖSSTE-Funktion" src="../images/large.png" /></p>
		</div>
	</body>
</html>