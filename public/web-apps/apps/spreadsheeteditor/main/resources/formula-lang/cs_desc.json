{"DATE": {"a": "(year; month; day)", "d": "<PERSON>rá<PERSON><PERSON>, které představuje datum v kódu pro datum a čas."}, "DATEDIF": {"a": "(po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_datum; konncové_datum; jed<PERSON><PERSON>)", "d": "Vypočítá počet dnů, m<PERSON><PERSON><PERSON><PERSON><PERSON> nebo roků mezi dvěma daty"}, "DATEVALUE": {"a": "(datum)", "d": "Převede datum ve formátu textu na číslo, které představuje datum v kódu pro datum a čas."}, "DAY": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "Vrátí den v měsíci, číslo od 1 do 31."}, "DAYS": {"a": "(konec; začátek)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> počet dní mezi dvěma daty."}, "DAYS360": {"a": "(start; konec; [metoda])", "d": "Vrátí počet dnů mezi dvěma daty na základě roku s 360 dny (d<PERSON><PERSON><PERSON> mě<PERSON><PERSON><PERSON> s 30 dny)."}, "EDATE": {"a": "(za<PERSON><PERSON><PERSON>k; měsíce)", "d": "Vrátí pořadové číslo data, což je určený počet měs<PERSON><PERSON>ů před nebo po počátečním datu."}, "EOMONTH": {"a": "(za<PERSON><PERSON><PERSON>k; měsíce)", "d": "Vrátí pořadové číslo posledního dne měsíce před nebo po určeném počtu mě<PERSON>."}, "HOUR": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON>rá<PERSON><PERSON> hodiny jako číslo od 0 (12:00 dop.) do 23 (11:00 odp.)."}, "ISOWEEKNUM": {"a": "(datum)", "d": "Vrátí číslo týdne ISO v roce pro dané datum."}, "MINUTE": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, číslo od 0 do 59."}, "MONTH": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, číslo od 1 (leden) do 12 (prosinec)."}, "NETWORKDAYS": {"a": "(začátek; konec; [svátky])", "d": "Vrátí počet celých pracovních dnů mezi dvěma zadanými daty."}, "NETWORKDAYS.INTL": {"a": "(začátek; konec; [vík<PERSON>]; [svátky])", "d": "Vrátí počet celých pracovních dní mezi dvěma daty s vlastními parametry víkendu."}, "NOW": {"a": "()", "d": "Vrátí aktuální datum a čas formátované jako datum a čas."}, "SECOND": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, číslo od 0 do 59."}, "TIME": {"a": "(hodina; minuta; sekunda)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>, minuty a sekundy zadané jako čísla na pořadové číslo formátované pomocí formátu času."}, "TIMEVALUE": {"a": "(čas)", "d": "Převede čas ve formě textového řetězce na pořadové číslo vyjadřují<PERSON><PERSON> č<PERSON>, číslo od 0 (12:00:00 dop.) do 0,999988426 (11:59:59 odp.). Po zadání vzorce číslo zformátujte pomocí formátu času."}, "TODAY": {"a": "()", "d": "Vrátí aktuální datum formátované jako datum."}, "WEEKDAY": {"a": "(po<PERSON><PERSON><PERSON><PERSON>; [typ])", "d": "Vrátí číslo od 1 do 7 určující den v týdnu kalendářního data."}, "WEEKNUM": {"a": "(po<PERSON><PERSON>_číslo; [typ])", "d": "Vrátí číslo týdne v roce."}, "WORKDAY": {"a": "(za<PERSON><PERSON><PERSON>k; dny; [svátky])", "d": "Vrátí pořadové číslo data před nebo po zadaném počtu pracovních dnů."}, "WORKDAY.INTL": {"a": "(za<PERSON><PERSON><PERSON>k; dny; [vík<PERSON>]; [svátky])", "d": "Vrátí pořadové číslo data před nebo po zadaném počtu pracovních dnů s vlastními parametry víkendu."}, "YEAR": {"a": "(po<PERSON><PERSON><PERSON><PERSON>_číslo)", "d": "Vrátí rok kalendářního data, celé číslo v rozsahu od 1900 do 9999."}, "YEARFRAC": {"a": "(za<PERSON><PERSON>tek; konec; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí část roku vyjádřenou zlomkem a představující počet celých dnů mezi počátečním a koncovým datem."}, "BESSELI": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> mod<PERSON><PERSON><PERSON> In(x)."}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> funkci Jn(x)."}, "BESSELK": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> mod<PERSON><PERSON><PERSON> funkci Kn(x)."}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> Y<PERSON>(x)."}, "BIN2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Převede binární číslo na dekadické."}, "BIN2HEX": {"a": "(č<PERSON>lo; [místa])", "d": "Převede binární číslo na hexadecimální."}, "BIN2OCT": {"a": "(č<PERSON>lo; [místa])", "d": "Převede binární číslo na osmičkové."}, "BITAND": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> „A“ dvou čísel"}, "BITLSHIFT": {"a": "(č<PERSON>lo; velikost_posunu)", "d": "Vrátí číslo posunuté doleva o hodnotu velikost_posunu v bitech"}, "BITOR": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> „nebo“ ze dvou čísel"}, "BITRSHIFT": {"a": "(č<PERSON>lo; velikost_posunu)", "d": "Vrátí číslo posunuté doprava o hodnotu velikost_posunu v bitech"}, "BITXOR": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> „výhradní nebo“ ze dvou čísel"}, "COMPLEX": {"a": "(re<PERSON>l; imag; [p<PERSON><PERSON><PERSON><PERSON>])", "d": "Převede reálnou a imaginární část na komplexní číslo"}, "CONVERT": {"a": "(číslo; z; do)", "d": "Převede číslo do jiného jed<PERSON>kového měrného systému."}, "DEC2BIN": {"a": "(č<PERSON>lo; [místa])", "d": "Převede dekadické číslo na binární."}, "DEC2HEX": {"a": "(č<PERSON>lo; [místa])", "d": "Převede dekadické číslo na hexadecimální."}, "DEC2OCT": {"a": "(č<PERSON>lo; [místa])", "d": "Převede dekadické číslo na osmičkové."}, "DELTA": {"a": "(číslo1; [číslo2])", "d": "Testuje rovnost dvou čísel."}, "ERF": {"a": "(dolní_limit; [horní_limit])", "d": "Vrátí chybovou funkci."}, "ERF.PRECISE": {"a": "(x)", "d": "Vrátí chybovou funkci."}, "ERFC": {"a": "(x)", "d": "Vrátí doplňkovou chybovou funkci."}, "ERFC.PRECISE": {"a": "(x)", "d": "Vrátí doplňkovou chybovou funkci."}, "GESTEP": {"a": "(<PERSON><PERSON><PERSON>; [co])", "d": "<PERSON><PERSON><PERSON>, zda je číslo větší než mezní hodnota."}, "HEX2BIN": {"a": "(č<PERSON>lo; [místa])", "d": "Převede hexadecimální číslo na binární."}, "HEX2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Převede hexadecimální číslo na dekadické."}, "HEX2OCT": {"a": "(č<PERSON>lo; [místa])", "d": "Převede hexadecimální číslo na osmičkové."}, "IMABS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí absolutní hodnotu komplexního č<PERSON>la."}, "IMAGINARY": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí imaginární část komplexního čísla."}, "IMARGUMENT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí argument q (úhel v radiánech)."}, "IMCONJUGATE": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí komplexně sdružené číslo ke komplexnímu č<PERSON>lu."}, "IMCOS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí kosinus komplexního <PERSON>."}, "IMCOSH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosinus komplexního <PERSON>"}, "IMCOT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí kotangens komplexního čísla"}, "IMCSC": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí kosekans komplexního č<PERSON>la"}, "IMCSCH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosekans komplexního <PERSON>"}, "IMDIV": {"a": "(ičíslo1; ičíslo2)", "d": "Vrátí podíl dvou komplexních č<PERSON>el."}, "IMEXP": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí exponenciální tvar komplexního č<PERSON>."}, "IMLN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí přirozený logaritmus komplexního č<PERSON>la."}, "IMLOG10": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí de<PERSON>dický logaritmus komplexního č<PERSON>la."}, "IMLOG2": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí logaritmus komplexního čísla při základu 2."}, "IMPOWER": {"a": "(i<PERSON><PERSON><PERSON>; číslo)", "d": "Vrátí komplexní číslo umocněné na celé číslo."}, "IMPRODUCT": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "Vrátí součin 1 až 255 komplexních čísel."}, "IMREAL": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí reálnou část komplexního čísla."}, "IMSEC": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí sekans komplexního <PERSON>"}, "IMSECH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sekans komplexního <PERSON>"}, "IMSIN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí sinus komplexního č<PERSON>la."}, "IMSINH": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sinus komplexního č<PERSON>la"}, "IMSQRT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vrátí d<PERSON>hou od<PERSON>cninu komplexního <PERSON>."}, "IMSUB": {"a": "(ičíslo1; ičíslo2)", "d": "Vrátí rozdíl dvou komplexních <PERSON>."}, "IMSUM": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "Vrátí součet komplexních čísel."}, "IMTAN": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Vrátí tangens komplexního čísla"}, "OCT2BIN": {"a": "(č<PERSON>lo; [místa])", "d": "Převede osmičkové číslo na binární."}, "OCT2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Převede osmičkové číslo na dekadické."}, "OCT2HEX": {"a": "(č<PERSON>lo; [místa])", "d": "Převede osmičkové číslo na hexadecimální."}, "DAVERAGE": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí pr<PERSON><PERSON><PERSON><PERSON> hodnot ve sloupci seznamu nebo <PERSON>, kter<PERSON> spl<PERSON><PERSON><PERSON><PERSON> zadaná kritéria."}, "DCOUNT": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí počet buněk obsahujících čísla v poli (sloupci) záznamů databáze, které splňují zadaná kritéria."}, "DCOUNTA": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí počet neprázdných buněk v poli (sloupci) záznamů databáze, které splňují zadaná kritéria."}, "DGET": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vybere z databáze jeden z<PERSON>nam, který splňuje zadaná kritéria."}, "DMAX": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí maximální hodnotu v poli (sloupci) záznamů databáze, která splňuje zadaná kritéria."}, "DMIN": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí minimální hodnotu v poli (sloupci) záznamů databáze, která splňuje zadaná kritéria."}, "DPRODUCT": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vynásobí hodnoty v poli (sloupci) záznamů databáze, které splňují zadaná kritéria."}, "DSTDEV": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Odhadne směrodatnou odchylku výběru vybraných položek databáze."}, "DSTDEVP": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí směrodatnou odchylku základního souboru vybraných položek databáze."}, "DSUM": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Sečte čísla v poli (sloupci) záznamů databáze, které splňují zadaná kritéria."}, "DVAR": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Odhadne rozptyl výběru vybraných položek databáze."}, "DVARP": {"a": "(datab<PERSON><PERSON>; pole; kritéria)", "d": "Vrátí rozptyl základního souboru vybraných položek databáze."}, "CHAR": {"a": "(kód)", "d": "Vrátí znak určený číslem kódu ze znakové sady definované v používaném počítači."}, "CLEAN": {"a": "(text)", "d": "Odstraní z textu všechny netisknutelné znaky."}, "CODE": {"a": "(text)", "d": "Vrátí číselný kód prvního znaku textového řetězce ze znakové sady definované v používaném počítači."}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Sloučí několik textových řetězců do jednoho."}, "CONCAT": {"a": "(text1; ...)", "d": "Zřetězí seznam nebo oblast textových řetězců."}, "DOLLAR": {"a": "(č<PERSON>lo; [desetiny])", "d": "Převede číslo na text ve formátu měny."}, "EXACT": {"a": "(text1; text2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda jsou dva textové řetězce stejné a vrátí hodnotu PRAVDA nebo NEPRAVDA. Tato funkce rozlišuje malá a velká písmena."}, "FIND": {"a": "(co; kde; [start])", "d": "Vrátí počáteční pozici jednoho textového řetězce v jiném textovém řetězci. Tato funkce rozlišuje malá a velká písmena."}, "FINDB": {"a": "(co; kde; [start])", "d": "Vyhledají jeden textový řetězec v druhém textovém řetězci a vrátí číslo počáteční pozice prvního textového řetězce od prvního znaku druhého textového řetězce, je určena pro jazyky používající dvoubaj<PERSON> znakov<PERSON> sadu (DBCS) - japonština, čínština a korejština"}, "FIXED": {"a": "(č<PERSON>lo; [desetiny]; [bez_č<PERSON>rky])", "d": "Zaokrouhlí číslo na zadaný počet desetinných míst a vrátí výsledek ve formátu textu s čárkami nebo bez čárek."}, "LEFT": {"a": "(text; [znaky])", "d": "Vrátí zadaný počet znaků od počátku textového řetězce."}, "LEFTB": {"a": "(text; [znaky])", "d": "Vrátí první znak nebo znaky v textovém řetězci na základě zadaného počtu bajtů, je určena pro jazyky používající dvoubajtov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>š<PERSON> a korejština"}, "LEN": {"a": "(text)", "d": "Vrátí počet znaků textového řetězce."}, "LENB": {"a": "(text)", "d": "Vrátí po<PERSON>et baj<PERSON>, k<PERSON><PERSON> představují znaky v textovém řetězci, je určena pro jazyky používající dvoubajtovou znakov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, čínština a korejština"}, "LOWER": {"a": "(text)", "d": "Převede všechna písmena textového řetězce na malá."}, "MID": {"a": "(text; start; počet_znaků)", "d": "Vrátí znaky z textového řetězce, je-li zadána počáteční pozice a počet znaků."}, "MIDB": {"a": "(text; start; počet_znaků)", "d": "Vrátí určitý počet znaků (na základě zadaného počtu bajtů) z textového řetězce od zadané pozice, je určena pro jazyky používající dvoubajtov<PERSON> zna<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>š<PERSON> a korejština"}, "NUMBERVALUE": {"a": "(text; [odd<PERSON><PERSON><PERSON><PERSON>_desetin]; [odd<PERSON><PERSON><PERSON><PERSON>_skupin])", "d": "Převede text na číslo nezávisle na prostředí"}, "PROPER": {"a": "(text)", "d": "Převede textový řetězec na formát, kdy jsou první písmena všech slov velká a ostatní písmena malá."}, "REPLACE": {"a": "(starý; start; znaky; nový)", "d": "Nahradí č<PERSON>t textového řetězce jiným textovým řetězcem."}, "REPLACEB": {"a": "(starý; start; znaky; nový)", "d": "Nahradí na základě zadaného počtu bajtů část textového řetězce jiným textovým řetězcem, je určena pro jazyky používající dvoubajtov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> a korejština"}, "REPT": {"a": "(text; počet)", "d": "Několikrát zopakuje zadaný text. Použijte funkci OPAKOVAT, chcete-li vyplnit buňku určitým počtem výskytů textového řetězce."}, "RIGHT": {"a": "(text; [znaky])", "d": "Vrátí zadaný počet znaků od konce textového řetězce."}, "RIGHTB": {"a": "(text; [znaky])", "d": "Vrátí zadaný počet bajtů od konce textov<PERSON>ho řet<PERSON>zce, je určena pro jazyky používající dvoubajtov<PERSON> sadu (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>š<PERSON> a korejština"}, "SEARCH": {"a": "(co; kde; [start])", "d": "Vrátí číslo prvního nalezeného výskytu znaku nebo textového řetězce. Směr hledání je zleva doprava. Velká a malá písmena nejsou rozlišována."}, "SEARCHB": {"a": "(co; kde; [start])", "d": "Vyhledají jeden textový řetězec v rámci druhého textového řetězce a vrátí číslo počáteční pozice prvního textového řetězce od prvního znaku druhého textového řetězce, je určena pro jazyky používající dvoubajtovou znakovou sadu (DBCS) - japonština, čínština a korejština"}, "SUBSTITUTE": {"a": "(text; starý; nový; [instance])", "d": "Nahradí existující text novým textem v textovém řetězci."}, "T": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota text. <PERSON><PERSON><PERSON><PERSON><PERSON> ano, v<PERSON><PERSON><PERSON><PERSON> text, jest<PERSON><PERSON><PERSON> ne, vr<PERSON><PERSON><PERSON> uvo<PERSON> (pr<PERSON><PERSON><PERSON><PERSON> text)."}, "TEXT": {"a": "(hodnota; formát)", "d": "Převede hodnotu na text v určitém formátu."}, "TEXTJOIN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; ignorovat_prázdné; text1; ...)", "d": "Zřetězí seznam nebo oblast textových řetězců pomoc<PERSON>."}, "TRIM": {"a": "(text)", "d": "Odstraní všechny mezery z textového řetězce kromě jednotlivých mezer mezi slovy."}, "UNICHAR": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí znak Unicode vztažený k dané č<PERSON>elné hodnotě"}, "UNICODE": {"a": "(text)", "d": "Vrá<PERSON><PERSON> (bod kódu) odpovídající prvnímu znaku textu"}, "UPPER": {"a": "(text)", "d": "Převede všechna písmena textového řetězce na velká."}, "VALUE": {"a": "(text)", "d": "Převede textový řetězec představující číslo na číslo."}, "AVEDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí průměrnou hodnotu absolutních odchylek datových bodů od jejich střední hodnoty. Argumenty mohou být čís<PERSON>, matice nebo odkazy obsahující č<PERSON>."}, "AVERAGE": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí prů<PERSON><PERSON><PERSON><PERSON> hodnot<PERSON> (aritmetický průměr) argumentů. Argumenty mohou být č<PERSON>, matice ne<PERSON>, k<PERSON><PERSON> o<PERSON>ahu<PERSON>."}, "AVERAGEA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí prů<PERSON>ě<PERSON><PERSON> hodnotu (aritmetický průměr) argumentů. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1. <PERSON><PERSON><PERSON><PERSON><PERSON> mohou b<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, matice nebo o<PERSON>."}, "AVERAGEIF": {"a": "(oblast; kritérium; [oblast_pro_průměr])", "d": "Zjistí prů<PERSON><PERSON><PERSON><PERSON> hodn<PERSON> (aritmetický průměr) buněk určených danou podmínkou nebo kritériem."}, "AVERAGEIFS": {"a": "(oblast_pro_průměr; oblast_kritérií; kritérium; ...)", "d": "Zjistí prů<PERSON><PERSON><PERSON><PERSON> ho<PERSON> (aritmetický průměr) buněk určených danou sadou podmínek nebo kritérií."}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Vrátí hodnotu kumulativní funkce hustoty pravděpodobnosti beta rozdělení."}, "BETAINV": {"a": "(pravdě<PERSON>dobnost; alfa; beta; [A]; [B])", "d": "Vrátí inverzní hodnotu kumulativní funkce hustoty pravděpodobnosti beta rozdělení (inverzní funkce k funkci BETADIST)."}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativní; [A]; [B])", "d": "Vrátí funkci rozdělení pravděpodobnosti beta."}, "BETA.INV": {"a": "(pravdě<PERSON>dobnost; alfa; beta; [A]; [B])", "d": "Vrátí inverzní hodnotu kumulativní funkce hustoty pravděpodobnosti beta rozdělení (BETA.DIST)."}, "BINOMDIST": {"a": "(poč<PERSON>_úspěchů; pokusy; pravděpodobnost_úspěchu; kumulativní)", "d": "Vrátí hodnotu binomického rozdělení pravděpodobnosti jednotlivých veli<PERSON>."}, "BINOM.DIST": {"a": "(poč<PERSON>_úspěchů; pokusy; pravděpodobnost_úspěchu; kumulativní)", "d": "Vrátí hodnotu binomického rozdělení pravděpodobnosti jednotlivých veli<PERSON>."}, "BINOM.DIST.RANGE": {"a": "(poku<PERSON>; pravděpodobnost_úspěchu; počet_úspěchů; [počet_úspěchů2])", "d": "Vrátí pravděpodobnost výsledku pokusu pomocí binomického rozdělení"}, "BINOM.INV": {"a": "(pokusy; pravděpodobnost_úspěchu; alfa)", "d": "Vrátí nej<PERSON> hodnot<PERSON>, pro kterou má kumulativní binomické rozdělení hodnotu větší nebo rovnu hodnotě kritéria."}, "CHIDIST": {"a": "(x; volnost)", "d": "Vrátí pravděpodobnost (pravý chvost) chí-kvadrát rozdělení."}, "CHIINV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí inverzní hodnotu pravděpodobnosti (pravý chvost) chí-kvadrát rozdělení."}, "CHITEST": {"a": "(akt<PERSON><PERSON><PERSON><PERSON>; očekávané)", "d": "Vrátí test nezávislosti: hodnota chí-kvadr<PERSON>t rozdělení pro statistické jednotky a příslušné stupně volnosti."}, "CHISQ.DIST": {"a": "(x; volnost; kumulativní)", "d": "Vrátí le<PERSON>trann<PERSON> pravděpodobnost rozdělení chí-kvadrát."}, "CHISQ.DIST.RT": {"a": "(x; volnost)", "d": "Vrátí pravostrannou pravděpodobnost rozdělení chí-kvadrát."}, "CHISQ.INV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí hodnotu funkce inverzní k distribuční funkci levostranné pravděpodobnosti rozdělení chí-kvadrát."}, "CHISQ.INV.RT": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí hodnotu funkce inverzní k distribuční funkci pravostranné pravděpodobnosti rozdělení chí-kvadrát."}, "CHISQ.TEST": {"a": "(akt<PERSON><PERSON><PERSON><PERSON>; očekávané)", "d": "Vrátí test nezávislosti: hodnota ze statistického rozdělení chí-kvadrát a příslušné stupně volnosti."}, "CONFIDENCE": {"a": "(alfa; sm_odch; velikost)", "d": "Vrátí interval spolehlivosti pro střední hodnotu základního souboru pomocí normálního rozdělení."}, "CONFIDENCE.NORM": {"a": "(alfa; sm_odch; velikost)", "d": "Vrátí interval spolehlivosti pro střední hodnotu základního souboru pomocí normálního rozdělení."}, "CONFIDENCE.T": {"a": "(alfa; sm_odch; velikost)", "d": "Vrátí interval spolehlivosti pro střední hodnotu základního souboru pomocí <PERSON> t-rozdělení."}, "CORREL": {"a": "(matice1; matice2)", "d": "Vrátí k<PERSON>lační koeficient mezi dvěma množinami dat."}, "COUNT": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí počet buněk v oblasti obsahujících čísla."}, "COUNTA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí počet buněk v oblasti, které nejsou prázdné."}, "COUNTBLANK": {"a": "(oblast)", "d": "Vrátí počet prázdných buněk v zadané oblasti buněk."}, "COUNTIF": {"a": "(oblast; kritérium)", "d": "Vrátí počet buněk v zadané oblasti, které splňují požadované kritérium."}, "COUNTIFS": {"a": "(oblast_kritérií; kritérium; ...)", "d": "Určí počet buněk na základě dané sady podmínek nebo kritérií."}, "COVAR": {"a": "(matice1; matice2)", "d": "<PERSON>r<PERSON><PERSON><PERSON> hodnotu k<PERSON>, pr<PERSON><PERSON><PERSON><PERSON><PERSON> hodnotu součinů odchylek pro každou dvojici datových bodů ve dvou množinách dat."}, "COVARIANCE.P": {"a": "(matice1; matice2)", "d": "Vrá<PERSON>í hodnotu kovariance základ<PERSON><PERSON>, pr<PERSON><PERSON><PERSON><PERSON>u hodnotu součinů odchylek pro každou dvojici datových bodů ve dvou množinách dat."}, "COVARIANCE.S": {"a": "(matice1; matice2)", "d": "Vrátí hodnotu kovariance výběru, pr<PERSON><PERSON><PERSON><PERSON>u hodnotu součinů odchylek pro každou dvojici datových bodů ve dvou množinách dat."}, "CRITBINOM": {"a": "(pokusy; pravděpodobnost_úspěchu; alfa)", "d": "Vrátí nej<PERSON> hodnot<PERSON>, pro kterou má kumulativní binomické rozdělení hodnotu větší nebo rovnu hodnotě kritéria."}, "DEVSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí součet druhých mocnin odchylek datových bodů od jejich střední hodnoty výběru."}, "EXPONDIST": {"a": "(x; lambda; kumulativní)", "d": "Vrátí hodnotu exponenciálního rozdělení."}, "EXPON.DIST": {"a": "(x; lambda; kumulativní)", "d": "Vrátí hodnotu exponenciálního rozdělení."}, "FDIST": {"a": "(x; volnost1; volnost2)", "d": "Vrátí hodnotu F rozdělení (stupeň nonekvivalence) pravděpodobnosti (pravý chvost) pro dvě množiny dat."}, "FINV": {"a": "(pravděpodobnost; volnost1; volnost2)", "d": "Vrátí hodnotu inverzní funkce k funkci F rozdělení pravděpodobnosti (pravý chvost): Jestliže p = FDIST(x,...), pak FINV(p,...) = x."}, "FTEST": {"a": "(matice1; matice2)", "d": "Vrátí výsledek F-testu, tj. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (dva chvosty), že se rozptyly v argumentech matice1 a matice2 v<PERSON><PERSON>n<PERSON> neliš<PERSON>."}, "F.DIST": {"a": "(x; volnost1; volnost2; kumulativní)", "d": "Vrá<PERSON>í ho<PERSON> (levostranného) rozdělení pravděpodobnosti F (stupeň nonekvivalence) pro dvě množiny dat."}, "F.DIST.RT": {"a": "(x; volnost1; volnost2)", "d": "Vrá<PERSON>í ho<PERSON> (pravostranného) rozdělení pravděpodobnosti F (stupeň nonekvivalence) pro dvě množiny dat."}, "F.INV": {"a": "(pravděpodobnost; volnost1; volnost2)", "d": "Vrátí hodnotu inverzní funkce k distribuční funkci (levostranného) rozdělení pravděpodobnosti F: jestliže p = F.DIST(x,...), F.INV(p,...) = x."}, "F.INV.RT": {"a": "(pravděpodobnost; volnost1; volnost2)", "d": "Vrátí hodnotu inverzní funkce k distribuční funkci (pravostranného) rozdělení pravděpodobnosti F: jestliže p = F.DIST.RT(x,...), F.INV.RT(p,...) = x."}, "F.TEST": {"a": "(matice1; matice2)", "d": "Vrátí výsledek F-testu, tj. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (dva chvosty), že se rozptyly v argumentech matice1 a matice2 v<PERSON><PERSON>n<PERSON> neliš<PERSON>."}, "FISHER": {"a": "(x)", "d": "Vrátí hodnotu Fisherovy transformace."}, "FISHERINV": {"a": "(y)", "d": "Vrátí hodnotu inverzní funkce k Fisherově transformaci: jestliže y = FISHER(x), FISHERINV(y) = x."}, "FORECAST": {"a": "(x; pole_y; pole_x)", "d": "Vy<PERSON>čte (předpoví) budoucí hodnotu lineárního trendu pomocí existují<PERSON><PERSON>ch hodnot."}, "FORECAST.ETS": {"a": "(c<PERSON><PERSON><PERSON>_datum; hodnoty; čas<PERSON>_osa; [sez<PERSON>nost]; [dokončení_dat]; [agregace])", "d": "Vrátí prognózu hodnoty k zadanému budoucímu cílovému datu pomocí metody exponenciálního vyhlazování."}, "FORECAST.ETS.CONFINT": {"a": "(c<PERSON><PERSON><PERSON>_datum; hodnoty; č<PERSON><PERSON>_osa; [úrove<PERSON>_spolehlivosti]; [sezónnost]; [dokončení_dat]; [agregace])", "d": "Vrátí interval spolehlivosti pro prognózu hodnoty k zadanému cílovému datu."}, "FORECAST.ETS.SEASONALITY": {"a": "(hodnoty; čas<PERSON>_osa; [dokončen<PERSON>_dat]; [agregace])", "d": "Vrátí délku opakujícího se vzorku, který aplikace detekuje u zadané časové řady."}, "FORECAST.ETS.STAT": {"a": "(hodnoty; čas<PERSON>_osa; typ_statistiky; [sez<PERSON>nost]; [dokon<PERSON>en<PERSON>_dat]; [agregace])", "d": "Vrátí statistická data prognózy."}, "FORECAST.LINEAR": {"a": "(x; pole_y; pole_x)", "d": "Vy<PERSON>čte (předpoví) budoucí hodnotu lineárního trendu pomocí existují<PERSON><PERSON>ch hodnot."}, "FREQUENCY": {"a": "(data; hodnoty)", "d": "Vypočte počet výskytů hodnot v oblasti hodnot a vrátí vertikální matici čísel, která má o jeden prvek více než argument Hodnoty."}, "GAMMA": {"a": "(x)", "d": "Vrátí hodnotu funkce gama"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativní)", "d": "Vrátí hodnotu gama rozdělení."}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativní)", "d": "Vrátí hodnotu gama rozdělení."}, "GAMMAINV": {"a": "(pravdě<PERSON>dobnost; alfa; beta)", "d": "Vrátí hodnotu inverzní funkce ke kumulativnímu gama rozdělení: Jestliže p = GAMMADIST(x,...), pak GAMMAINV(p,...) = x."}, "GAMMA.INV": {"a": "(pravdě<PERSON>dobnost; alfa; beta)", "d": "Vrátí hodnotu inverzní funkce k distribuční funkci kumulativního rozdělení gama: jestliže p = GAMMA.DIST(x,...), potom GAMMA.INV(p,...) = x."}, "GAMMALN": {"a": "(x)", "d": "Vrátí přirozený logaritmus funkce gama."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Vrátí přirozený logaritmus funkce gama."}, "GAUSS": {"a": "(x)", "d": "Vrátí hodnotu 0,5 menší než u standardního normálního součtového rozdělení"}, "GEOMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí <PERSON>ý průměr matice nebo oblasti kladných číselných dat."}, "GROWTH": {"a": "(pole_y; [pole_x]; [nová_x]; [b])", "d": "Vrátí hodnoty trendu exponenciálního růstu odpovídajícího známým datovým bodům."}, "HARMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí harmonic<PERSON>ý průměr množiny kladných čísel: reciproční hodnota aritmetického průměru recipročn<PERSON><PERSON> čísel."}, "HYPGEOM.DIST": {"a": "(úspěch; celkem; zák<PERSON>_úspěch; základ_celkem; kumulativní)", "d": "Vrátí hodnotu hypergeometrického rozdělení."}, "HYPGEOMDIST": {"a": "(ú<PERSON><PERSON><PERSON>; celkem; zák<PERSON>_úspěch; základ_celkem)", "d": "Vrátí hodnotu hypergeometrického rozdělení."}, "INTERCEPT": {"a": "(pole_y; pole_x)", "d": "Vypočte souřadnice bodu, ve kterém čára protne osu y, pomocí proložení nejlepší regresní čáry známými hodnotami x a y."}, "KURT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí hodnotu excesu množiny dat."}, "LARGE": {"a": "(pole; k)", "d": "Vrátí k-tou největší hodnotu množ<PERSON>y dat, nap<PERSON><PERSON><PERSON> pá<PERSON> největ<PERSON><PERSON>."}, "LINEST": {"a": "(pole_y; [pole_x]; [b]; [stat])", "d": "Vrátí statistiku popisující lineární trend odpovídající známým datovým bodům proložením přímky vypočtené metodou nejmenš<PERSON>ch <PERSON>t<PERSON>ů."}, "LOGEST": {"a": "(pole_y; [pole_x]; [b]; [stat])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> statist<PERSON>, která popisuje exponenciální křivku odpovídající známým datovým bodům."}, "LOGINV": {"a": "(pravdě<PERSON>dobnost; stř_hodn; sm_odch)", "d": "Vrátí inverzní funkci ke kumulativní distribuční funkci logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry stř_hodn a sm_odch."}, "LOGNORM.DIST": {"a": "(x; středn<PERSON>; sm_odchy<PERSON>a; kumulativní)", "d": "Vrátí hodnotu logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry Střední a Sm_odch."}, "LOGNORM.INV": {"a": "(pravdě<PERSON>dobnost; stř_hodn; sm_odch)", "d": "Vrátí inverzní funkci ke kumulativní distribuční funkci logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry Stř_hodn a Sm_odch."}, "LOGNORMDIST": {"a": "(x; střední; sm_odch)", "d": "Vrátí hodnotu kumulativního logaritmicko-normálního rozdělení hodnot x, kde funkce ln(x) má normální rozdělení s parametry střední a sm_odch."}, "MAX": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí maximální hodnotu množiny hodnot. Přeskočí logické hodnoty a text."}, "MAXA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí maximální hodnotu v množině hodnot. Nepřeskočí logické hodnoty a text."}, "MAXIFS": {"a": "(max_oblast; kritéria_oblast; kritéria; ...)", "d": "Vrátí maximální hodnotu z buněk určených sadou podmínek nebo kritérií."}, "MEDIAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, st<PERSON>ed<PERSON><PERSON> hodnotu množiny zadaných <PERSON>."}, "MIN": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí minimální hodnotu množiny hodnot. Přeskočí logické hodnoty a text."}, "MINA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vrátí minimální hodnotu v množině hodnot. Nepřeskočí logické hodnoty a text."}, "MINIFS": {"a": "(min_oblast; kritéria_oblast; kritéria; ...)", "d": "Vrátí minimální hodnotu z buněk určených sadou podmínek nebo kritérií."}, "MODE": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí hodnotu, která se v matici nebo v oblasti dat vyskytuje nejčastěji."}, "MODE.MULT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí vertikální matici nejčastěji se vyskytujících (opakovaných) hodnot v matici nebo oblasti dat. Chcete-li získat horizontální matici, použijte vzorec =TRANSPOZICE(MODE.MULT(číslo1;číslo2;...))"}, "MODE.SNGL": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí hodnotu, která se v matici nebo v oblasti dat vyskytuje nejčastěji."}, "NEGBINOM.DIST": {"a": "(počet_neúspěchů; počet_úspěchů; pravděpodobnost_úspěchu; kumulativní)", "d": "Vrátí hodnotu negativního binomick<PERSON>ho rozd<PERSON>lení, tj. <PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že neúspěchy argumentu počet_neúspěchů nastanou dříve než úspěch argumentu počet_úspěchů s pravděpodobností určenou argumentem pravděpodobnost_úspěchu."}, "NEGBINOMDIST": {"a": "(počet_neúspěchů; počet_úspěchů; pravděpodobnost_úspěchu)", "d": "Vrátí hodnotu negativního binomick<PERSON>ho rozdělení, tj. <PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že počet neúspěchů určený argumentem počet_neúspěchů nastane dříve než počet úspěchů určených argumentem počet_úspěchů s pravděpodobností určenou argumentem pravděpodobnost_úspěchu."}, "NORM.DIST": {"a": "(x; střed_hodn; sm_odch; kumulativní)", "d": "Vrátí hodnotu normálního rozdělení pro zadanou střední hodnotu a směrodatnou odchylku."}, "NORMDIST": {"a": "(x; střed_hodn; sm_odch; kumulativní)", "d": "Vrátí hodnotu normálního kumulativního rozdělení pro zadanou střední hodnotu a směrodatnou odchylku."}, "NORM.INV": {"a": "(pravdě<PERSON>dobnost; střední; sm_odch)", "d": "Vrátí inverzní funkci k distribuční funkci normálního kumulativního rozdělení pro zadanou střední hodnotu a směrodatnou odchylku."}, "NORMINV": {"a": "(pravdě<PERSON>dobnost; střední; sm_odch)", "d": "Vrátí inverzní funkci k normálnímu kumulativnímu rozdělení pro zadanou střední hodnotu a směrodatnou odchylku."}, "NORM.S.DIST": {"a": "(z; kumulativní)", "d": "Vrátí standardní normá<PERSON>í rozd<PERSON> (má střední hodnotu nula a směrodatnou odchylku jedna)."}, "NORMSDIST": {"a": "(z)", "d": "Vrátí hodnotu standardního normálního kumulativního rozdělení. (Střední hodnota daného rozdělení je rovna 0 a jeho směrodatná odchylka je rovna 1.)"}, "NORM.S.INV": {"a": "(pravděpodobnost)", "d": "Vrátí inverzní funkci k distribuční funkci standardního normálního kumulativního rozdělení (které má střední hodnotu rovnou 0 a směrodatnou odchylku 1)."}, "NORMSINV": {"a": "(pravděpodobnost)", "d": "Vrátí inverzní funkci ke standardnímu normálnímu kumulativnímu rozdělení. (Střední hodnota daného rozdělení je rovna 0 a jeho směrodatná odchylka je rovna 1)."}, "PEARSON": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>v výsledný momentový korelační koe<PERSON> r."}, "PERCENTILE": {"a": "(matice; k)", "d": "Vrátí hodnotu k-tého percentilu hodnot v oblasti."}, "PERCENTILE.EXC": {"a": "(matice; k)", "d": "Vrátí hodnotu k-tého percentilu hodnot v oblasti, kde hodnota k spadá do oblasti 0..1 (s vyloučením hodnot 0 a 1)."}, "PERCENTILE.INC": {"a": "(matice; k)", "d": "Vrátí hodnotu k-tého percentilu hodnot v oblasti, kde hodnota k spadá do oblasti 0..1 (včetně)."}, "PERCENTRANK": {"a": "(matice; x; [významnost])", "d": "Vrátí pořadí hodnoty v množině dat vyjádřené procentuální částí množiny dat."}, "PERCENTRANK.EXC": {"a": "(matice; x; [významnost])", "d": "Vrátí pořadí hodnoty v množině dat vyjádřené procentuální částí (0..1, s vyloučením hodnot 0 a 1) množiny dat."}, "PERCENTRANK.INC": {"a": "(matice; x; [významnost])", "d": "Vrátí pořadí hodnoty v množině dat vyjádřené procentuální částí (0..1, v<PERSON><PERSON><PERSON>ě) množiny dat."}, "PERMUT": {"a": "(počet; permutace)", "d": "Vrátí počet permutací pro zadaný počet objektů, které lze vybrat z celkového počtu objektů."}, "PERMUTATIONA": {"a": "(počet; permutace)", "d": "Vrátí počet permutací pro zadaný počet objektů (s opakováním) které je možné vybrat z celkového počtu objektů"}, "PHI": {"a": "(x)", "d": "Vrátí hodnotu funkce hustoty pro standardní normální rozdělení"}, "POISSON": {"a": "(x; středn<PERSON>; kumulativní)", "d": "Vrátí hodnotu Poissonova rozdělení."}, "POISSON.DIST": {"a": "(x; středn<PERSON>; kumulativní)", "d": "Vrátí hodnotu Poissonova rozdělení."}, "PROB": {"a": "(x_oblast; prst_oblast; dolní_limit; [horní_limit])", "d": "Vrátí pravděpodobnost toho, že hodnoty v oblasti leží mezi dvěma mezemi nebo jsou rovny dolní mezi."}, "QUARTILE": {"a": "(matice; kvar<PERSON>)", "d": "Vrátí hodnotu kvartilu množiny dat."}, "QUARTILE.INC": {"a": "(matice; kvar<PERSON>)", "d": "Vrátí hodnotu kvartilu množiny dat na základě hodnot percentilu z oblasti 0..1 (vč<PERSON><PERSON>ě)."}, "QUARTILE.EXC": {"a": "(matice; kvar<PERSON>)", "d": "Vrátí hodnotu kvartilu množiny dat na základě hodnot percentilu z oblasti 0..1 (s vyloučením hodnot 0 a 1)."}, "RANK": {"a": "(č<PERSON>lo; odkaz; [pořad<PERSON>])", "d": "Vrátí pořadí čísla v seznamu čísel: jeho relativní velikost vzhledem k hodnotám v seznamu."}, "RANK.AVG": {"a": "(č<PERSON>lo; odkaz; [pořad<PERSON>])", "d": "Vrátí pořadí čísla v seznamu čísel: jeho relativní velikost vzhledem k ostatním hodnotám v seznamu. Má-li stejné pořadí více než jedna hodnota, bude vráceno průměrné pořadí."}, "RANK.EQ": {"a": "(č<PERSON>lo; odkaz; [pořad<PERSON>])", "d": "Vrátí pořadí čísla v seznamu čísel: jeho relativní velikost vzhledem k ostatním hodnotám v seznamu. Má-li stejné pořadí více než jedna hodnota, bude vráceno nejvyšší pořadí dané množiny hodnot."}, "RSQ": {"a": "(pole_y; pole_x)", "d": "Vrá<PERSON><PERSON> dru<PERSON> mocninu <PERSON> výsledného momentového korelačního koeficientu pomocí zadan<PERSON>ch da<PERSON>ch bod<PERSON>."}, "SKEW": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí zešikmení rozdělení: charakteristika stupně asymetrie rozdělení kolem jeho střední hodnoty."}, "SKEW.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí zešikmení rozdělení založené na základním souboru: charakteristika stupně asymetrie rozdělení kolem jeho střední hodnoty"}, "SLOPE": {"a": "(pole_y; pole_x)", "d": "Vrátí směrnici lineární regresní čáry pro<PERSON>é zadanými datovými body."}, "SMALL": {"a": "(pole; k)", "d": "Vrátí k-tou nejmenší hodnotu v množině dat, například páté nejmenší číslo."}, "STANDARDIZE": {"a": "(x; střed_hodn; sm_odch)", "d": "Vrátí normalizovanou hodnotu z rozdělení určeného střední hodnotou a směrodatnou odchylkou."}, "STDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne směrodatnou odchylku výběru (přeskočí logické hodnoty a text ve výběru)."}, "STDEV.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte směrodatnou odchylku základ<PERSON>, k<PERSON><PERSON> byl zadán jako argument (přeskočí logické hodnoty a text)."}, "STDEV.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne směrodatnou odchylku výběru (přeskočí logické hodnoty a text ve výběru)."}, "STDEVA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne směrodatnou odchylku výběru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1."}, "STDEVP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte směrodatnou odchylku základ<PERSON>, k<PERSON><PERSON> byl zadán jako argument (přeskočí logické hodnoty a text)."}, "STDEVPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočte směrodatnou odchylku základního souboru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1."}, "STEYX": {"a": "(pole_y; pole_x)", "d": "Vrátí standardní chybu předpovězené hodnoty y pro každou hodnotu x v regresi."}, "TDIST": {"a": "(x; volnost; chvosty)", "d": "Vrá<PERSON>í hodnotu Studentova t-rozdělení."}, "TINV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí in<PERSON><PERSON><PERSON><PERSON> (dva chvosty) ke Studentovu t-rozdělení."}, "T.DIST": {"a": "(x; volnost; kumulativní)", "d": "Vrá<PERSON><PERSON> hodnotu levostranného <PERSON>ova t-rozdělení."}, "T.DIST.2T": {"a": "(x; volnost)", "d": "Vrátí hodnotu oboustranného <PERSON>ova t-rozdělení."}, "T.DIST.RT": {"a": "(x; volnost)", "d": "Vrátí hodnotu pravostranného Studentova t-rozdělení."}, "T.INV": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí le<PERSON>trannou inverzní funkci k distribuční funkci Studentova t-rozdělení."}, "T.INV.2T": {"a": "(pravděpodobnost; volnost)", "d": "Vrátí oboustrannou inverzní funkci k distribuční funkci Studentova t-rozdělení."}, "T.TEST": {"a": "(matice1; matice2; chvosty; typ)", "d": "Vrátí pravděpodobnost odpovídající Studentovu t-testu."}, "TREND": {"a": "(pole_y; [pole_x]; [nová_x]; [b])", "d": "Vrátí hodnoty lineárního trendu odpovídajícího známým datovým bodům pomocí metody nejmenších čtver<PERSON>ů."}, "TRIMMEAN": {"a": "(pole; procenta)", "d": "Vrátí průměrnou hodnotu vnitřní části množiny datových hodnot."}, "TTEST": {"a": "(matice1; matice2; chvosty; typ)", "d": "Vrátí pravděpodobnost odpovídající Studentovu t-testu."}, "VAR": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl výběru (přeskočí logické hodnoty a text ve výběru)."}, "VAR.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte rozptyl celého základního souboru (přeskočí logické hodnoty a text v základním souboru)."}, "VAR.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl výběru (přeskočí logické hodnoty a text ve výběru)."}, "VARA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne rozptyl výběru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1."}, "VARP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočte rozptyl základního souboru (přeskočí logické hodnoty a text v základním souboru)."}, "VARPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočte rozptyl základního souboru. Nepřeskočí logické hodnoty a text. Text a logická hodnota NEPRAVDA mají hodnotu 0, logická hodnota PRAVDA má hodnotu 1."}, "WEIBULL": {"a": "(x; alfa; beta; kumulativní)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnotu <PERSON>ova rozdělení."}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativní)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnotu <PERSON>ova rozdělení."}, "Z.TEST": {"a": "(matice; x; [sigma])", "d": "Vrátí P<PERSON>hodnotu (jeden ch<PERSON>t) z-testu."}, "ZTEST": {"a": "(matice; x; [sigma])", "d": "Vrátí P<PERSON>hodnotu (jeden ch<PERSON>t) z-testu."}, "ACCRINT": {"a": "(emise; prvn<PERSON>_<PERSON>; vypořádání; sazba; nom_hodnota; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON>na]; [metoda_výpočtu])", "d": "Vrátí nahromaděný úrok z cenného papíru, ze kterého je úrok placen pravidelně."}, "ACCRINTM": {"a": "(emise; vypořádání; sazba; nom_hodnota; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí nahromaděný úrok z cenného papíru, ze kterého je úrok placen k datu splatnosti."}, "AMORDEGRC": {"a": "(nák<PERSON>; nákup; prvn<PERSON>_období; z<PERSON><PERSON><PERSON>; obdo<PERSON><PERSON>; sazba; [zák<PERSON>na])", "d": "Vrátí kalk<PERSON>čně rozvržený lineární odpis aktiva pro každé účetní období."}, "AMORLINC": {"a": "(nák<PERSON>; nákup; prvn<PERSON>_období; z<PERSON><PERSON><PERSON>; obdo<PERSON><PERSON>; sazba; [zák<PERSON>na])", "d": "Vrátí kalk<PERSON>čně rozvržený lineární odpis aktiva pro každé účetní období."}, "COUPDAYBS": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet dnů od začátku období placení kupónů do data splatnosti."}, "COUPDAYS": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet dnů v období placení kupónů, které obsahuje den zúčtování."}, "COUPDAYSNC": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet dnů od data zúčtování do následujícího data placení kupónu."}, "COUPNCD": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí následující datum placení kupónu po datu zúčtování."}, "COUPNUM": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí počet kupónů splatných mezi datem zúčtování a datem splatnosti."}, "COUPPCD": {"a": "(vypořádání; splatnost; po<PERSON>et_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí předchozí datum placení kupónu před datem zúčtování."}, "CUMIPMT": {"a": "(<PERSON><PERSON>; obdob<PERSON>; p<PERSON>jč<PERSON>; začátek; konec; typ)", "d": "Vrátí kumulativní úrok splacený mezi dvěma obdobími."}, "CUMPRINC": {"a": "(<PERSON><PERSON>; obdob<PERSON>; p<PERSON>jč<PERSON>; začátek; konec; typ)", "d": "Vrátí kumulativní jistinu splacenou mezi dvěma období<PERSON>."}, "DB": {"a": "(n<PERSON><PERSON><PERSON>; zůstatek; životnost; období; [mě<PERSON><PERSON><PERSON>])", "d": "Vypočítá odpis aktiva za určité období pomocí degresivní metody odpisu s pevným zůstatkem."}, "DDB": {"a": "(n<PERSON><PERSON><PERSON>; zůstatek; životnost; období; [faktor])", "d": "Vypočítá odpis aktiva za určité období pomocí dvojité degresivní metody odpisu nebo jiné metody, k<PERSON><PERSON>."}, "DISC": {"a": "(vypoř<PERSON>d<PERSON><PERSON>; splatnost; cena; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí diskontní sazbu cenného papíru."}, "DOLLARDE": {"a": "(zlomková_koruna; zlomek)", "d": "Převede částku v korunách vyjádřenou zlomkem na částku v korunách vyjádřenou desetinným číslem."}, "DOLLARFR": {"a": "(desetinná_koruna; zlomek)", "d": "Převede částku v korunách vyjádřenou desetinným číslem na částku v korunách vyjádřenou zlomkem."}, "DURATION": {"a": "(vypořádání; splatnost; kupón; výnos; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON>na])", "d": "Vrátí roční dobu cenného papíru s pravidelnými úrokovými sazbami."}, "EFFECT": {"a": "(<PERSON><PERSON>; období)", "d": "Vrátí efektivní roční úrokovou sazbu."}, "FV": {"a": "(sazba; pper; splátka; [souč_hod]; [typ])", "d": "Vrátí budoucí hodnotu investice vypočtenou na základě pravidelných konstantních splátek a konstantní úrokové sazby."}, "FVSCHEDULE": {"a": "(hodnota; sazby)", "d": "Vrátí budo<PERSON>í hodnotu počáteční jistiny po použití série sazeb složitého <PERSON>."}, "INTRATE": {"a": "(vypořádání; splatnost; investice; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí <PERSON> sazbu plně investovaného cenného papíru."}, "IPMT": {"a": "(sazba; za; pper; souč_hod; [bud_hod]; [typ])", "d": "Vrátí výšku úroku v určitém úrokovém období vypočtenou na základě pravidelných konstantních splátek a konstantní úrokové sazby."}, "IRR": {"a": "(hodnoty; [odhad])", "d": "Vrátí vnitřní výnosové procento série peněžních to<PERSON>ů."}, "ISPMT": {"a": "(sazba; za; pper; souč)", "d": "Vrátí výšku úroku zaplaceného za určité období investice."}, "MDURATION": {"a": "(vypořádání; splatnost; kupón; výnos; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON>na])", "d": "Vrátí <PERSON>ho modifikovanou dobu cenného papíru o nominální hodnotě 100 Kč."}, "MIRR": {"a": "(hodnoty; finance; investice)", "d": "Vrátí vnitřní sazbu výnosu pravidelných peněžních příjmů. Zohledňuje jak náklady na investice, tak úrok z reinvestic získaných peněžních prostředků."}, "NOMINAL": {"a": "(<PERSON><PERSON>; období)", "d": "Vrátí nominální roční úrokovou sazbu."}, "NPER": {"a": "(sazba; splátka; souč_hod; [bud_hod]; [typ])", "d": "Vrátí počet období pro investici vypočítaný na základě pravidelných konstantních splátek a konstantní úrokové sazby. Chcete-li například zadat čtvrtletní splátky realizované 6. <PERSON><PERSON>, pou<PERSON><PERSON><PERSON><PERSON> 6%/4."}, "NPV": {"a": "(sazba; hodnota1; [hodnota2]; ...)", "d": "Vrátí čistou současnou hodnotu investice vypočítanou na základě diskontní sazby a série budouc<PERSON>ch plateb (záporné hodnoty) a příjmů (kladné hodnoty)."}, "ODDFPRICE": {"a": "(vypoř<PERSON><PERSON><PERSON><PERSON>; splatnost; emise; prvn<PERSON>_<PERSON>rok; sazba; výnos; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč s odlišným prvním obdobím."}, "ODDFYIELD": {"a": "(vypoř<PERSON><PERSON><PERSON><PERSON>; splatnost; emise; prvn<PERSON>_úrok; sazba; cena; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí výnos cenného papíru s odlišným prvním obdobím."}, "ODDLPRICE": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; posledn<PERSON>_úrok; sazba; výnos; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč s odlišným posledním obdobím."}, "ODDLYIELD": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; posledn<PERSON>_úrok; sazba; cena; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí výnos cenného papíru s odlišným posledním obdobím."}, "PDURATION": {"a": "(sazba; souč_hod; bud_hod)", "d": "Vrátí počet období požadovaných investicí k tomu, aby <PERSON><PERSON><PERSON>a zadané hodnoty"}, "PMT": {"a": "(sazba; pper; souč_hod; [bud_hod]; [typ])", "d": "Vypočte splátku půjčky na základě konstantních splátek a konstantní úrokové sazby."}, "PPMT": {"a": "(sazba; za; pper; souč_hod; [bud_hod]; [typ])", "d": "Vrátí hodnotu splátky jistiny pro zadanou investici vypočtenou na základě pravidelných konstantních splátek a konstantní úrokové sazby."}, "PRICE": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; sazba; výnos; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč, ze kterého je úrok placen v pravidelných termínech."}, "PRICEDISC": {"a": "(vypoř<PERSON>dán<PERSON>; splatnost; diskont_sazba; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí cenu diskontního cenného papíru o nominální hodnotě 100 Kč."}, "PRICEMAT": {"a": "(vypoř<PERSON><PERSON><PERSON><PERSON>; splatnost; emise; sazba; výnos; [z<PERSON><PERSON>na])", "d": "Vrátí cenu cenného papíru o nominální hodnotě 100 Kč, ze kterého je úrok placen k datu splatnosti."}, "PV": {"a": "(sazba; pper; spl<PERSON>tka; [bud_hod]; [typ])", "d": "Vrátí současnou hodnotu investice: celkovou hodnotu série budoucích plateb."}, "RATE": {"a": "(pper; spl<PERSON>tka; souč_hod; [bud_hod]; [typ]; [odhad])", "d": "Vrátí úrokovou sazbu vztaženou na období půjčky nebo investice. Chcete-li například zadat čtvrtletní splátky realizované 6. <PERSON><PERSON>, pou<PERSON><PERSON><PERSON><PERSON> 6%/4."}, "RECEIVED": {"a": "(vypořádání; splatnost; investice; diskont_sazba; [z<PERSON><PERSON>na])", "d": "Vrátí částku obdrženou k datu splatnosti plně investovaného cenného papíru."}, "RRI": {"a": "(nper; souč_hod; bud_hod)", "d": "Vrátí odpovídající úrokovou sazbu pro růst investic"}, "SLN": {"a": "(náklady; zůstatek; životnost)", "d": "Vrátí přímé odpisy aktiva pro jedno období."}, "SYD": {"a": "(náklady; zůstatek; životnost; za)", "d": "Vrátí směrné číslo ročních odpisů aktiva pro zadané období."}, "TBILLEQ": {"a": "(vypořádání; splatnost; diskont_sazba)", "d": "Vrátí výnos směnky státní pokladny ekvivalentní výnosu obligace."}, "TBILLPRICE": {"a": "(vypořádání; splatnost; diskont_sazba)", "d": "Vrátí cenu směnky státní pokladny o nominální hodnotě 100 Kč."}, "TBILLYIELD": {"a": "(vypořádání; splatnost; cena)", "d": "Vrátí výnos směnky státní pokladny."}, "VDB": {"a": "(cena; zůstatek; životnost; začátek; konec; [faktor]; [nep<PERSON><PERSON><PERSON><PERSON>])", "d": "Vypočte odpisy aktiva pro každé zadané obdo<PERSON>í, vč<PERSON>ně neukončených období, pomocí dvojité degresivní metody odpisu nebo jiné metody, k<PERSON><PERSON>."}, "XIRR": {"a": "(hodnoty; data; [odhad])", "d": "Vrátí vnitřní výnosnost pro harmonogram pen<PERSON>ž<PERSON><PERSON><PERSON> tok<PERSON>."}, "XNPV": {"a": "(sazba; hodnoty; data)", "d": "Vrátí <PERSON> současnou hodnotu pro harmonogram pen<PERSON><PERSON><PERSON><PERSON><PERSON> tok<PERSON>."}, "YIELD": {"a": "(vypořádání; splatnost; sazba; cena; zaru<PERSON>_cena; po<PERSON><PERSON>_plateb; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí výnos cenného papíru, ze kterého je úrok placen v pravidelných termínech."}, "YIELDDISC": {"a": "(vypoř<PERSON>d<PERSON><PERSON>; splatnost; cena; zaru<PERSON>_cena; [z<PERSON><PERSON><PERSON>])", "d": "Vrátí roční výnos diskontního cenného p<PERSON>, například směnky státní pokladny."}, "YIELDMAT": {"a": "(vypoř<PERSON>dání; splatnost; emise; sazba; cena; [z<PERSON><PERSON>na])", "d": "Vrátí roční výnos cenného <PERSON>í<PERSON>, ze kterého je úrok placen k datu splatnosti."}, "ABS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí absolutní hodnotu čísla. Výsledek je číslo bez znaménka."}, "ACOS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hodnotu arkuskosinu čísla. Výsledek je v radiánech v rozsahu 0 až pí. Arkuskosinus je úhel, jeh<PERSON><PERSON> kosinus je argument Číslo."}, "ACOSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hodnotu hyperbolického arkuskosinu čísla."}, "ACOT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí arkuskotangens čísla. Výsledek je v radiánech v rozsahu 0 až pí."}, "ACOTH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí inverzní hyperbolický kotangens čísla"}, "AGGREGATE": {"a": "(<PERSON><PERSON>; mož<PERSON>ti; odkaz1; ...)", "d": "Vrátí agregaci v seznamu nebo databázi."}, "ARABIC": {"a": "(text)", "d": "Převede římskou číslici na arabskou."}, "ASC": {"a": "(text)", "d": "U jazyků využívajících dvoubajtové znakové sady (DBCS) změní znaky s plnou šířkou (dvoubajtové) na znaky s poloviční šířkou (jednobajtové)"}, "ASIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí arkussinus čísla. Výsledek je v radiánech v rozsahu -pí/2 až pí/2."}, "ASINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický arkussinus čísla."}, "ATAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí arkustangens čísla. Výsledek je v radiánech v rozsahu -pí/2 až pí/2."}, "ATAN2": {"a": "(x_č<PERSON>lo; y_č<PERSON>lo)", "d": "Vrátí arkustangens zadané x-ové a y-ové souřadnice. Výsledek je v radiánech v rozsahu -pí až pí kromě hodnoty -pí."}, "ATANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický arkustangens čísla."}, "BASE": {"a": "(č<PERSON>lo; radix; [min_length])", "d": "Převede číslo na textové vyjádření s danou číselnou základnou."}, "CEILING": {"a": "(číslo; významnost)", "d": "Zaokrouhlí číslo nahoru na nejbližší násobek zadané hodnoty významnosti."}, "CEILING.MATH": {"a": "(č<PERSON>lo; [významnost]; [rež<PERSON>])", "d": "Zaokrouhlí číslo nahoru na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty významnosti"}, "CEILING.PRECISE": {"a": "(č<PERSON>lo; [významnost])", "d": "Vrátí číslo zaokrouhlené nahoru na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty"}, "COMBIN": {"a": "(počet; kombinace)", "d": "Vrátí počet kombinací pro zadaný počet položek."}, "COMBINA": {"a": "(počet; kombinace)", "d": "Vrátí počet kombinací s opakováním pro daný počet položek"}, "COS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrá<PERSON><PERSON> k<PERSON>."}, "COSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosinus <PERSON>."}, "COT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí kotangens úhlu"}, "COTH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kotangens čísla"}, "CSC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON>rá<PERSON><PERSON> k<PERSON>"}, "CSCH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický kosekans úhlu"}, "DECIMAL": {"a": "(<PERSON><PERSON><PERSON>; zák<PERSON>)", "d": "Převede textové vyjádření čísla v daném základu na desítkové číslo"}, "DEGREES": {"a": "(<PERSON><PERSON>)", "d": "Převede radiány na stupně."}, "ECMA.CEILING": {"a": "(číslo; významnost)", "d": "Zaokrouhlí číslo nahoru na nejbližší násobek zadané hodnoty významnosti"}, "EVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrouhlí kladné číslo nahoru a záporné číslo dolů na nejbližší sudé celé číslo."}, "EXP": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí základ přirozeného logaritmu umocněný na zadané číslo."}, "FACT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí faktoriál čísla. Výsledek se rovná hodnotě 1*2*3*...*Číslo."}, "FACTDOUBLE": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí d<PERSON>ý faktor<PERSON>."}, "FLOOR": {"a": "(číslo; významnost)", "d": "Zaokrouhlí číslo dolů na nejbližší násobek zadané hodnoty významnosti."}, "FLOOR.PRECISE": {"a": "(č<PERSON>lo; [významnost])", "d": "Vrátí číslo zaokrouhlené dolů na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty"}, "FLOOR.MATH": {"a": "(č<PERSON>lo; [významnost]; [rež<PERSON>])", "d": "Zaokrouhlí číslo dolů na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty významnosti"}, "GCD": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí nejvě<PERSON>ší s<PERSON>čný dě<PERSON>l."}, "INT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrouhlí číslo dolů na nejbližší celé číslo."}, "ISO.CEILING": {"a": "(č<PERSON>lo; [významnost])", "d": "Vrátí číslo zaokrouhlené nahoru na nejbližší celé číslo nebo na nejbližší násobek zadané hodnoty. <PERSON><PERSON>lo bude zaokrouhleno nahoru bez ohledu na jeho znaménko. Pokud je však zadáno číslo nula nebo násobek nuly, bude vrácena nula."}, "LCM": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí nejmenší společný násobek."}, "LN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí přirozený logaritmus čísla."}, "LOG": {"a": "(<PERSON><PERSON><PERSON>; [z<PERSON><PERSON>])", "d": "Vrátí logaritmus čísla při zadaném základu."}, "LOG10": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí de<PERSON>dický logaritmus čísla."}, "MDETERM": {"a": "(pole)", "d": "Vrátí determinant matice."}, "MINVERSE": {"a": "(pole)", "d": "Vrátí inverzní matici k matici, která je uložena v oblasti definované jako matice."}, "MMULT": {"a": "(pole1; pole2)", "d": "Vrátí součin dvou matic, matici se stejným počtem řádků jako matice argumentu Pole1 a stejným počtem sloupců jako matice argumentu Pole2."}, "MOD": {"a": "(<PERSON><PERSON><PERSON>; dělitel)", "d": "Vrátí zbytek po dělení čísla."}, "MROUND": {"a": "(číslo; násobek)", "d": "Vrátí číslo zaokrouhlené na požadovaný násobek."}, "MULTINOMIAL": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí mnohočlen sady čísel."}, "MUNIT": {"a": "(dimenze)", "d": "Vrátí matici jednotek pro zadanou dimenzi"}, "ODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrouhlí kladné číslo nahoru a záporné číslo dolů na nejbližší liché celé číslo."}, "PI": {"a": "()", "d": "Vrátí hodnotu čísla pí s přesností na 15 číslic. Výsledek je hodnota 3.14159265358979."}, "POWER": {"a": "(č<PERSON><PERSON>; exponent)", "d": "Umocní číslo na zadaný exponent."}, "PRODUCT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vynásobí všechna čísla zadaná jako argumenty."}, "QUOTIENT": {"a": "(numer<PERSON>tor; denominátor)", "d": "Vrá<PERSON><PERSON> celou <PERSON> d<PERSON>."}, "RADIANS": {"a": "(<PERSON><PERSON>)", "d": "Převede stupně na radiány."}, "RAND": {"a": "()", "d": "Vrátí náhodné číslo větší nebo rovné 0 a menší než 1 určené na základě spojité distribuční funkce (změní se při každém přepočítání listu)."}, "RANDARRAY": {"a": "([<PERSON><PERSON><PERSON><PERSON><PERSON>]; [sloup<PERSON><PERSON>]; [min]; [max]; [celé_č<PERSON>lo])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "RANDBETWEEN": {"a": "(dolní; horní)", "d": "Vrátí náhodné číslo mezi zadanými č<PERSON>."}, "ROMAN": {"a": "(číslo; [forma])", "d": "Převede číslo nap<PERSON>é pomocí arabských číslic na římské číslice ve formátu textu."}, "ROUND": {"a": "(číslo; číslice)", "d": "Zaokrouhlí číslo na zadaný počet číslic."}, "ROUNDDOWN": {"a": "(číslo; číslice)", "d": "Zaokrouhlí číslo dolů směrem k nule."}, "ROUNDUP": {"a": "(číslo; číslice)", "d": "Zaokrouhlí č<PERSON>lo nahoru směrem od nuly."}, "SEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>"}, "SECH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sekans <PERSON>"}, "SERIESSUM": {"a": "(x; n; m; koeficient)", "d": "Vrátí součet mocninné řady určené podle vzorce."}, "SIGN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí znaménko čísla: číslo 1 pro kladné číslo, 0 pro nulu nebo -1 pro záporné číslo."}, "SIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí sinus úhlu."}, "SINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický sinus čísla."}, "SQRT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí d<PERSON>hou od<PERSON>cninu čísla."}, "SQRTPI": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrá<PERSON><PERSON> d<PERSON>hou od<PERSON> výrazu (číslo * pí)."}, "SUBTOTAL": {"a": "(funkce; odkaz1; ...)", "d": "Vrátí souhrn na listu nebo v databázi."}, "SUM": {"a": "(číslo1; [číslo2]; ...)", "d": "Sečte všechna čísla v oblasti buněk."}, "SUMIF": {"a": "(oblast; kritéria; [součet])", "d": "Sečte buňky vybrané podle zadaných kritérií."}, "SUMIFS": {"a": "(oblast_součtu; oblast_kritérií; kritérium; ...)", "d": "Sečte buňky určené danou sadou podmínek nebo kritérií."}, "SUMPRODUCT": {"a": "(pole1; [pole2]; [pole3]; ...)", "d": "Vrátí součet součinů odpovídajících oblastí nebo matic."}, "SUMSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "Vrátí součet druhých mocnin argumentů. Argumenty mohou představovat čísla, matice, názvy nebo odkazy na buňky obsahující čísla."}, "SUMX2MY2": {"a": "(pole_x; pole_y)", "d": "Vypočte součet rozdílů čtverců dvou odpovídajících oblastí nebo polí."}, "SUMX2PY2": {"a": "(pole_x; pole_y)", "d": "Vrátí celkový součet součtů čtverců čísel ve dvou odpovídajících oblastech nebo maticích."}, "SUMXMY2": {"a": "(pole_x; pole_y)", "d": "Vypočte součet čtverců rozdílů dvou odpovídajících oblastí nebo matic."}, "TAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrá<PERSON>í tangens úhlu."}, "TANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí hyperbolický tangens čísla."}, "TRUNC": {"a": "(č<PERSON>lo; [desetiny])", "d": "Zkrátí číslo na celé číslo odstraněním desetinné nebo zlomkové části čísla."}, "ADDRESS": {"a": "(<PERSON><PERSON><PERSON>; sloupec; [typ]; [a1]; [list])", "d": "Vytvoří textový odkaz na buňku po zadání čísla řádku a sloupce."}, "CHOOSE": {"a": "(index; hodnota1; [hodnota2]; ...)", "d": "Zvolí hodnotu nebo a<PERSON>, k<PERSON><PERSON> má být <PERSON>, ze seznamu hodnot na základě zadaného argumentu Index."}, "COLUMN": {"a": "([odkaz])", "d": "Vrátí č<PERSON>lo s<PERSON>ce odkazu."}, "COLUMNS": {"a": "(pole)", "d": "Vrátí počet sloupců v matici nebo odkazu."}, "FORMULATEXT": {"a": "(odkaz)", "d": "Vrátí vzorec jako ř<PERSON>"}, "HLOOKUP": {"a": "(hledat; tabulka; <PERSON><PERSON><PERSON>; [typ])", "d": "Prohledá horní řádek tabulky nebo matice hodnot a vrátí hodnotu ze zadaného řádku obsaženou ve stejném sloupci."}, "HYPERLINK": {"a": "(umístění; [název])", "d": "Vytvoří zástupce nebo odkaz, kter<PERSON> otevře dokument uložený na pevném disku, síťovém serveru nebo na síti Internet."}, "INDEX": {"a": "(pole; <PERSON><PERSON><PERSON>; [sloupec]!od<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [sloupec]; [oblast])", "d": "Vrátí hodnotu nebo odkaz na buňku v určitém řádku a sloupci v dané oblasti."}, "INDIRECT": {"a": "(odkaz; [a1])", "d": "Vrátí odkaz určený textovým řetězcem."}, "LOOKUP": {"a": "(co; hledat; [v<PERSON><PERSON><PERSON>]!co; pole)", "d": "Vyhledá požadovanou hodnotu v matici nebo v oblasti obsahující jeden řádek nebo jeden sloupec. Funkce je poskytnuta k zajištění zpětné kompatibility"}, "MATCH": {"a": "(co; prohledat; [shoda])", "d": "Vrátí relativní polohu položky matice, která odpovídá určené hodnotě v určeném pořadí."}, "OFFSET": {"a": "(odkaz; řádky; sloupce; [výška]; [ší<PERSON>ka])", "d": "Vrátí odkaz na oblast, která představuje daný počet řádků a sloupců z daného odkazu."}, "ROW": {"a": "([odkaz])", "d": "Vrátí č<PERSON>lo <PERSON>d<PERSON> od<PERSON>zu."}, "ROWS": {"a": "(pole)", "d": "Vrátí počet řádků v odkazu nebo matici."}, "TRANSPOSE": {"a": "(pole)", "d": "Převede vodorovnou oblast buněk na svislou nebo naopak."}, "UNIQUE": {"a": "(pole; [sloup<PERSON>]; [pr<PERSON><PERSON><PERSON>_jed<PERSON><PERSON>])", "d": "Vrátí jedinečné hodnoty z rozsahu nebo pole."}, "VLOOKUP": {"a": "(hledat; tabulka; sloupec; [typ])", "d": "Vyhledá hodnotu v krajním levém sloupci tabulky a vrátí hodnotu ze zadaného sloupce ve stejném řádku. Tabulka musí být standardně seřazena vzestupně."}, "XLOOKUP": {"a": "(co; prohledat; vr<PERSON><PERSON>t; [pokud_ne<PERSON><PERSON>o]; [rež<PERSON>_shody]; [režim_vyhled<PERSON>í])", "d": "Vyhledá požadovanou hodnotu v oblasti nebo poli a vrátí odpovídající položku z jiné oblasti nebo pole. Ve výchozím nastavení se vyžaduje přesná shoda."}, "CELL": {"a": "(informace; [odkaz])", "d": "Vrátí informace o formátování, umístění nebo obsahu bu<PERSON>"}, "ERROR.TYPE": {"a": "(chyba)", "d": "Vrátí číslo odpovídající chybové hodnotě."}, "ISBLANK": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda odkaz v argumentu Hodnota odkazuje na prázdnou buňku a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISERR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jest<PERSON> argument Hodnota představuje jinou chybu než #NENÍ_K_DISPOZICI, a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISERROR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jest<PERSON> <PERSON> Hodnota představuje chybu, a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISEVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí logickou hodnotu PRAVDA, pokud je číslo sudé."}, "ISFORMULA": {"a": "(odkaz)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jestli odkaz odkazuje na buňku obsahující vzorec, a vrátí hodnotu PRAVDA nebo NEPRAVDA"}, "ISLOGICAL": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda argument Hodnota je logická hodnota (PRAVDA nebo NEPRAVDA) a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISNA": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota chybová hodnota #NENÍ_K_DISPOZICI, a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISNONTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda argument Hodnota není text (prázdné buňky nejsou text) a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISNUMBER": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota číslo a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vrátí logickou hodnotu PRAVDA, pokud je číslo lich<PERSON>."}, "ISREF": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota odkaz a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "ISTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je argument Hodnota text a vrátí hodnotu PRAVDA nebo NEPRAVDA."}, "N": {"a": "(hodnota)", "d": "Převede nečíselnou hodnotu na číslo, kalendářní data na pořadová čísla, hodnotu PRAVDA na číslo 1 a všechny ostatní výrazy na číslo 0 (nula)."}, "NA": {"a": "()", "d": "Vrátí chybovou hodnotu #NENÍ_K_DISPOZICI (hodnota nedostupná)."}, "SHEET": {"a": "([hodnota])", "d": "Vrátí číslo listu odkazovaného listu"}, "SHEETS": {"a": "([odkaz])", "d": "Vrátí počet listů v odkazu"}, "TYPE": {"a": "(hodnota)", "d": "Vrátí celé číslo představující datový typ hodnoty: č<PERSON>lo = 1, text = 2, <PERSON><PERSON><PERSON> hodnota = 4, chybo<PERSON><PERSON> hodnota = 16, matice = 64, slo<PERSON><PERSON><PERSON> datový typ = 128."}, "AND": {"a": "(logická1; [logická2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda mají všechny argumenty hodnotu PRAVDA, a v takovém případě vrátí hodnotu PRAVDA."}, "FALSE": {"a": "()", "d": "Vrátí logickou hodnotu NEPRAVDA."}, "IF": {"a": "(podm<PERSON>ka; [ano]; [ne])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je podmínka splněna, a v<PERSON>á<PERSON><PERSON> jednu hodnotu, jest<PERSON><PERSON>e je výsledkem hodnota PRAVDA, a jinou hodnotu, pokud je výsledkem hodnota NEPRAVDA."}, "IFS": {"a": "(logický_test; podmínka; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jest<PERSON> je splněná jedna nebo ví<PERSON>, a vrátí hodnotu odpovídající první pravdivé podmínce."}, "IFERROR": {"a": "(hodnota; hodnota_v_případě_chyby)", "d": "Pokud je výraz chy<PERSON>ný, vr<PERSON>t<PERSON> hodnotu hodnota_v_případě_chyby. V opačném případě vrátí vlastní hodnotu výrazu."}, "IFNA": {"a": "(hodnota; hodnota_pokud_na)", "d": "Vrátí zada<PERSON>u hodnotu, pokud je výsledkem výrazu hodnota #N/A, v opačném případě vrátí výsledek výrazu"}, "NOT": {"a": "(loghod)", "d": "Změní hodnotu NEPRAVDA na PRAVDA nebo naopak."}, "OR": {"a": "(logická1; [logická2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zda je nejméně jeden argument roven hodnotě PRAVDA, a vrátí hodnotu PRAVDA nebo NEPRAVDA. Vrátí hodnotu NEPRAVDA pouze v případě, že všechny argumenty jsou rovny hodnotě NEPRAVDA."}, "SWITCH": {"a": "(výraz; hodnota1; výsledek1; [vý<PERSON><PERSON><PERSON>_nebo_hodnota2]; [výsledek2]; ...)", "d": "Vyhodnocuje výraz oproti seznamu hodnot a vrací výsledek odpovídající první shodné hodnotě. Pokud se neshoduje žádn<PERSON> hodnota, vrátí volitelnou výchozí hodnotu."}, "TRUE": {"a": "()", "d": "Vrátí logickou hodnotu PRAVDA."}, "XOR": {"a": "(logická1; [logická2]; ...)", "d": "Vrátí logickou hodnotu Výhradní nebo všech argumentů"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Vrát<PERSON> text, kter<PERSON> je před oddělov<PERSON>č<PERSON> znaků."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Vrátí text, který je po oddělovači znaků."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Rozdělí text na řádky nebo sloupce pomocí <PERSON>."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Zalomí vektor řádku nebo sloupce po zadaném po<PERSON><PERSON> hodnot."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": " <PERSON><PERSON><PERSON><PERSON><PERSON> pole do j<PERSON><PERSON><PERSON> pole."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON> pole do jednoho pole."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Vrátí <PERSON>ádky z pole nebo odkazu."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Vrátí sloupce z pole nebo odkazu."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON>rá<PERSON><PERSON> pole jako jeden sloupec."}, "TOROW": {"a": "(pole, [ignorovat], [pro<PERSON><PERSON><PERSON><PERSON>_podle_sloupce])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole jako jede<PERSON>. "}, "WRAPCOLS": {"a": "(vektor, wrap_count, [pad_with])", "d": " Zabalí vektor řádku nebo sloupce po zadaném po<PERSON><PERSON> hodnot."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Vrátí řádky nebo sloupce ze začátku nebo konce pole."}, "DROP": {"a": "(array, rows, [columns])", "d": "Přemístí řádky nebo sloupce ze začátku nebo konce pole."}}