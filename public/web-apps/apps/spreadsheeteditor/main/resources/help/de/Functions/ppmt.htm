﻿<!DOCTYPE html>
<html>
	<head>
		<title>KAPZ-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>KAPZ-Funktion</h1>
			<p>Die Funktion <b>KAPZ</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie gibt die Kapitalrückzahlung einer Investition für eine angegebene Periode zurück. Es werden konstante periodische Zahlungen und ein konstanter Zinssatz vorausgesetzt.</p>
			<p>Die Formelsyntax der Funktion <b>KAPZ</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>KAPZ(Zins;Zr;Zzr;Bw;[Zw];[F])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zins</em></b> ist die Effektivverzinsung für die Investition.</p> 
                <p style="text-indent: 50px;"><b><em>Zr</em></b> ist der Zeitraum, für den Sie die Tilgungszahlung berechnen möchten. Der Wert muss zwischen <b><em>1</em></b> und <b><em>Zzr</em></b> liegen.</p> 
				<p style="text-indent: 50px;"><b><em>Zzr</em></b> ist die Gesamtanzahl der Zahlungszeiträume für die Investition.</p> 
				<p style="text-indent: 50px;"><b><em>Bw</em></b> ist der Barwert oder der heutige Gesamtwert einer Reihe zukünftiger Zahlungen.</p>
                <p style="text-indent: 50px;"><b><em>Zw</em></b> ist ein zukünftiger Wert (d. h. ein Restguthaben, das übrigbleibt, nachdem die letzte Zahlung getätigt wurde). Dieses Argument ist optional. Fehlt das Argument <b><em>Zw</em></b>, wird es als 0 (Null) angenommen.</p>
				<p style="text-indent: 50px;"><b><em>F</em></b> gibt an, wann die Zahlungen fällig sind. Dieses Argument ist optional. Fehlt das Argument oder ist auf 0 festgelegt, nimmt die Funktion an, dass die Zahlungen am Ende der Periode fällig sind. Ist <b><em>F</em></b> mit 1 angegeben, sind die Zahlungen zum Anfang der Periode fällig.</p>
			<p class="note"><b>Hinweis:</b> Für alle Argumente gilt, dass Geldbeträge, die Sie auszahlen (zum Beispiel Spareinlagen), durch negative Zahlen und Geldbeträge, die Sie einnehmen (zum Beispiel Dividenden), durch positive Zahlen dargestellt werden. Sie sollten unbedingt darauf achten, dass Sie für <em>Zins</em> und <em>Zzr</em> zueinander passende Zeiteinheiten verwenden. Verwenden Sie beispielsweise bei monatliche Zahlungen N%/12 für <em>Zins</em> und N*12 für <em>Zzr</em>, für vierteljährliche Zahlungen N%/4 für <em>Zins</em> and N*4 für <em>Zzr</em> und für jährliche Zahlungen N% für <em>Zins</em> und N für <em>Zzr</em>.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>KAPZ</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>KAPZ</b>:</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="KAPZ-Funktion" src="../images/ppmt.png" /></p>
		</div>
	</body>
</html>