{"Common.Controllers.Chat.notcriticalErrorTitle": "경고", "Common.Controllers.Chat.textEnterMessage": "여기에 메시지를 입력하십시오", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "익명", "Common.Controllers.ExternalDiagramEditor.textClose": "닫기", "Common.Controllers.ExternalDiagramEditor.warningText": "다른 사용자가 편집 중이므로 개체를 사용할 수 없습니다.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "경고", "Common.define.chartData.textArea": "영역", "Common.define.chartData.textAreaStacked": "누적 영역형", "Common.define.chartData.textAreaStackedPer": "100% 누적 영역형", "Common.define.chartData.textBar": "막대", "Common.define.chartData.textBarNormal": "묶은 세로 막대형", "Common.define.chartData.textBarNormal3d": "3차원 묶은 세로 막대", "Common.define.chartData.textBarNormal3dPerspective": "3차원 세로 막대", "Common.define.chartData.textBarStacked": "누적 세로 막대형", "Common.define.chartData.textBarStacked3d": "3차원 누적 세로 막대형", "Common.define.chartData.textBarStackedPer": "100% 누적 세로 막대형", "Common.define.chartData.textBarStackedPer3d": "3차원 100 % 누적 세로 막 대형", "Common.define.chartData.textCharts": "차트", "Common.define.chartData.textColumn": "열", "Common.define.chartData.textCombo": "콤보", "Common.define.chartData.textComboAreaBar": "누적 영역형 - 묶은 세로 막대형", "Common.define.chartData.textComboBarLine": "묶은 세로 막대형 - 꺾은선형", "Common.define.chartData.textComboBarLineSecondary": "묶은 세로 막대형 - 꺾은선형,보조 축", "Common.define.chartData.textComboCustom": "맞춤 조합", "Common.define.chartData.textDoughnut": "도넛", "Common.define.chartData.textHBarNormal": "묶은 가로 막대형", "Common.define.chartData.textHBarNormal3d": "3차원 집합 막대", "Common.define.chartData.textHBarStacked": "누적 가로 막대형", "Common.define.chartData.textHBarStacked3d": "3차원 누적 가로 막대형", "Common.define.chartData.textHBarStackedPer": "100％ 누적 막대형", "Common.define.chartData.textHBarStackedPer3d": "3차원 100 % 기준 누적 가로 막 대형", "Common.define.chartData.textLine": "선", "Common.define.chartData.textLine3d": "3차원 꺾은 선형", "Common.define.chartData.textLineMarker": "마커 라인", "Common.define.chartData.textLineStacked": "누적 꺾은 선형", "Common.define.chartData.textLineStackedMarker": "표식이 있는 누적 꺾은 선형", "Common.define.chartData.textLineStackedPer": "100 % 기준 누적 꺾은 선형", "Common.define.chartData.textLineStackedPerMarker": "표식이 있는 100 % 기준 누적 꺾은 선형", "Common.define.chartData.textPie": "부분 원형", "Common.define.chartData.textPie3d": "3차원 원형", "Common.define.chartData.textPoint": "XY (분산형)", "Common.define.chartData.textScatter": "분산형", "Common.define.chartData.textScatterLine": "직선이 있는 분산형", "Common.define.chartData.textScatterLineMarker": "직선 및 표식이 있는 분산형", "Common.define.chartData.textScatterSmooth": "곡선이 있는 분산형", "Common.define.chartData.textScatterSmoothMarker": "곡선 및 표식이 있는 분산형", "Common.define.chartData.textStock": "주식형", "Common.define.chartData.textSurface": "표면", "Common.define.effectData.textAcross": "어크로스", "Common.define.effectData.textAppear": "나타내기", "Common.define.effectData.textArcDown": "아래쪽 타원", "Common.define.effectData.textArcLeft": "왼쪽 타원", "Common.define.effectData.textArcRight": "오른쪽 타원", "Common.define.effectData.textArcs": "타원", "Common.define.effectData.textArcUp": "위쪽 타원", "Common.define.effectData.textBasic": "심플 테마", "Common.define.effectData.textBasicSwivel": "기본 회전", "Common.define.effectData.textBasicZoom": "기본 확대", "Common.define.effectData.textBean": "콩", "Common.define.effectData.textBlinds": "블라인드", "Common.define.effectData.textBlink": "블링크", "Common.define.effectData.textBoldFlash": "굵게 번쩍하기", "Common.define.effectData.textBoldReveal": "굵게 나타내기", "Common.define.effectData.textBoomerang": "부메랑", "Common.define.effectData.textBounce": "바운드", "Common.define.effectData.textBounceLeft": "왼쪽 바운드", "Common.define.effectData.textBounceRight": "오른쪽 바운드", "Common.define.effectData.textBox": "상자", "Common.define.effectData.textBrushColor": "색 채우기", "Common.define.effectData.textCenterRevolve": "중심 회전", "Common.define.effectData.textCheckerboard": "바둑판 무늬", "Common.define.effectData.textCircle": "원", "Common.define.effectData.textCollapse": "축소", "Common.define.effectData.textColorPulse": "색 파동", "Common.define.effectData.textComplementaryColor": "보색", "Common.define.effectData.textComplementaryColor2": "보색 2", "Common.define.effectData.textCompress": "압축", "Common.define.effectData.textContrast": "대비", "Common.define.effectData.textContrastingColor": "대비 색상", "Common.define.effectData.textCredits": "크레딧", "Common.define.effectData.textCrescentMoon": "초승달", "Common.define.effectData.textCurveDown": "커브 다운", "Common.define.effectData.textCurvedSquare": "곡선 사각형", "Common.define.effectData.textCurvedX": "곡선 X", "Common.define.effectData.textCurvyLeft": "곡선 왼쪽", "Common.define.effectData.textCurvyRight": "곡선 오른쪽", "Common.define.effectData.textCurvyStar": "곡선 별", "Common.define.effectData.textCustomPath": "사용자 지정 경로", "Common.define.effectData.textCuverUp": "커브 업", "Common.define.effectData.textDarken": "어둡게 만들기", "Common.define.effectData.textDecayingWave": "소멸하는 물결", "Common.define.effectData.textDesaturate": "흐리기", "Common.define.effectData.textDiagonalDownRight": "오른쪽 아래로 대각선", "Common.define.effectData.textDiagonalUpRight": "오른쪽 위로 대각선", "Common.define.effectData.textDiamond": "다이아몬드", "Common.define.effectData.textDisappear": "사라지기", "Common.define.effectData.textDissolveIn": "디졸브 인", "Common.define.effectData.textDissolveOut": "디졸브 아웃", "Common.define.effectData.textDown": "아래로", "Common.define.effectData.textDrop": "드롭", "Common.define.effectData.textEmphasis": "강조 효과", "Common.define.effectData.textEntrance": "나타내기 효과", "Common.define.effectData.textEqualTriangle": "등삼각형", "Common.define.effectData.textExciting": "화려한 효과", "Common.define.effectData.textExit": "끝내기 효과", "Common.define.effectData.textExpand": "확장", "Common.define.effectData.textFade": "페이드", "Common.define.effectData.textFigureFour": "피겨 8 포", "Common.define.effectData.textFillColor": "채우기 색", "Common.define.effectData.textFlip": "대칭", "Common.define.effectData.textFloat": "플로트", "Common.define.effectData.textFloatDown": "하강", "Common.define.effectData.textFloatIn": "하강", "Common.define.effectData.textFloatOut": "가라앉기", "Common.define.effectData.textFloatUp": "상승", "Common.define.effectData.textFlyIn": "날아오기", "Common.define.effectData.textFlyOut": "날아가기", "Common.define.effectData.textFontColor": "글꼴 색", "Common.define.effectData.textFootball": "미식축구 공", "Common.define.effectData.textFromBottom": "아래로 부터", "Common.define.effectData.textFromBottomLeft": "왼쪽 아래에서", "Common.define.effectData.textFromBottomRight": "오른쪽 아래에서", "Common.define.effectData.textFromLeft": "왼쪽에서", "Common.define.effectData.textFromRight": "오른쪽에서", "Common.define.effectData.textFromTop": "위에서 부터", "Common.define.effectData.textFromTopLeft": "왼쪽 위에서", "Common.define.effectData.textFromTopRight": "오른쪽 위에서", "Common.define.effectData.textFunnel": "깔때기", "Common.define.effectData.textGrowShrink": "확대/축소", "Common.define.effectData.textGrowTurn": "확대 & 회전", "Common.define.effectData.textGrowWithColor": "색 채우며 확대", "Common.define.effectData.textHeart": "하트모양", "Common.define.effectData.textHeartbeat": "하트", "Common.define.effectData.textHexagon": "육각형", "Common.define.effectData.textHorizontal": "수평", "Common.define.effectData.textHorizontalFigure": "호리즌탈 피겨 8", "Common.define.effectData.textHorizontalIn": "수평 입력", "Common.define.effectData.textHorizontalOut": "수평 출력", "Common.define.effectData.textIn": "에", "Common.define.effectData.textInFromScreenCenter": "화면 중앙에서", "Common.define.effectData.textInSlightly": "약간", "Common.define.effectData.textInToScreenBottom": "화면 하단으로", "Common.define.effectData.textInvertedSquare": "역사각형", "Common.define.effectData.textInvertedTriangle": "역삼각형", "Common.define.effectData.textLeft": "왼쪽", "Common.define.effectData.textLeftDown": "왼쪽 아래로", "Common.define.effectData.textLeftUp": "왼쪽 위로", "Common.define.effectData.textLighten": "밝게 만들기", "Common.define.effectData.textLineColor": "선 색", "Common.define.effectData.textLines": "선", "Common.define.effectData.textLinesCurves": "선 곡선", "Common.define.effectData.textLoopDeLoop": "루프 드 루프", "Common.define.effectData.textLoops": "반복", "Common.define.effectData.textModerate": "보통", "Common.define.effectData.textNeutron": "뉴트론", "Common.define.effectData.textObjectCenter": "개체 중심", "Common.define.effectData.textObjectColor": "개체 색", "Common.define.effectData.textOctagon": "팔각형", "Common.define.effectData.textOut": "바깥쪽", "Common.define.effectData.textOutFromScreenBottom": "화면 아래에서 이동", "Common.define.effectData.textOutSlightly": "살짝 이동", "Common.define.effectData.textOutToScreenCenter": "화면 중앙으로 이동", "Common.define.effectData.textParallelogram": "평행 사변형", "Common.define.effectData.textPath": "이동 경로", "Common.define.effectData.textPeanut": "땅콩", "Common.define.effectData.textPeekIn": "피크 인", "Common.define.effectData.textPeekOut": "피크 아웃", "Common.define.effectData.textPentagon": "오각형", "Common.define.effectData.textPinwheel": "바람개비", "Common.define.effectData.textPlus": "덧셈", "Common.define.effectData.textPointStar": "꼭지점 별", "Common.define.effectData.textPointStar4": "4 꼭지점 별", "Common.define.effectData.textPointStar5": "5 꼭지점 별", "Common.define.effectData.textPointStar6": "6 꼭지점 별", "Common.define.effectData.textPointStar8": "8 꼭지점 별", "Common.define.effectData.textPulse": "펄스", "Common.define.effectData.textRandomBars": "실선 무늬", "Common.define.effectData.textRight": "오른쪽", "Common.define.effectData.textRightDown": "오른쪽 아래로", "Common.define.effectData.textRightTriangle": "직각 삼각형", "Common.define.effectData.textRightUp": "오른쪽 위로", "Common.define.effectData.textRiseUp": "떠오르기", "Common.define.effectData.textSCurve1": "S 커브 1", "Common.define.effectData.textSCurve2": "S 커브 2", "Common.define.effectData.textShape": "쉐이프", "Common.define.effectData.textShapes": "도형", "Common.define.effectData.textShimmer": "은은하게 빛내기", "Common.define.effectData.textShrinkTurn": "축소하면서", "Common.define.effectData.textSineWave": "사인파", "Common.define.effectData.textSinkDown": "가라앉기", "Common.define.effectData.textSlideCenter": "슬라이드 센터", "Common.define.effectData.textSpecial": "특별한", "Common.define.effectData.textSpin": "회전", "Common.define.effectData.textSpinner": "돌기", "Common.define.effectData.textSpiralIn": "안쪽 소용돌이", "Common.define.effectData.textSpiralLeft": "왼쪽 소용돌이", "Common.define.effectData.textSpiralOut": "바깥쪽 소용돌이", "Common.define.effectData.textSpiralRight": "오른쪽 소용돌이", "Common.define.effectData.textSplit": "분할", "Common.define.effectData.textSpoke1": "1 스포크", "Common.define.effectData.textSpoke2": "2 스포크", "Common.define.effectData.textSpoke3": "3 스포크", "Common.define.effectData.textSpoke4": "4 스포크", "Common.define.effectData.textSpoke8": "8 스포크", "Common.define.effectData.textSpring": "스프링", "Common.define.effectData.textSquare": "사각형", "Common.define.effectData.textStairsDown": "아래쪽 계단", "Common.define.effectData.textStretch": "늘이기", "Common.define.effectData.textStrips": "스트립", "Common.define.effectData.textSubtle": "은은한 효과", "Common.define.effectData.textSwivel": "회전", "Common.define.effectData.textSwoosh": "스우시", "Common.define.effectData.textTeardrop": "눈물 방울", "Common.define.effectData.textTeeter": "흔들기", "Common.define.effectData.textToBottom": "아래로", "Common.define.effectData.textToBottomLeft": "왼쪽 아래로", "Common.define.effectData.textToBottomRight": "오른쪽 아래로", "Common.define.effectData.textToLeft": "왼쪽으로", "Common.define.effectData.textToRight": "오른쪽으로", "Common.define.effectData.textToTop": "위로", "Common.define.effectData.textToTopLeft": "왼쪽 위로", "Common.define.effectData.textToTopRight": "오른쪽 위로", "Common.define.effectData.textTransparency": "투명", "Common.define.effectData.textTrapezoid": "사다리꼴", "Common.define.effectData.textTurnDown": "아래로 회전", "Common.define.effectData.textTurnDownRight": "오른쪽 아래로 회전", "Common.define.effectData.textTurns": "회전", "Common.define.effectData.textTurnUp": "위로 회전", "Common.define.effectData.textTurnUpRight": "오른쪽 위로 회전", "Common.define.effectData.textUnderline": "밑줄", "Common.define.effectData.textUp": "최대", "Common.define.effectData.textVertical": "세로", "Common.define.effectData.textVerticalFigure": "버티칼 피겨 8", "Common.define.effectData.textVerticalIn": "수직", "Common.define.effectData.textVerticalOut": "수직 출력", "Common.define.effectData.textWave": "물결", "Common.define.effectData.textWedge": "쇄기꼴", "Common.define.effectData.textWheel": "시계 방향", "Common.define.effectData.textWhip": "채찍", "Common.define.effectData.textWipe": "지우기", "Common.define.effectData.textZigzag": "지그재그", "Common.define.effectData.textZoom": "확대 / 축소", "Common.Translation.warnFileLocked": "파일이 다른 응용 프로그램에서 편집 중입니다. 편집을 계속하고 사본으로 저장할 수 있습니다.", "Common.Translation.warnFileLockedBtnEdit": "복사본 만들기", "Common.Translation.warnFileLockedBtnView": "미리보기", "Common.UI.ButtonColored.textAutoColor": "자동", "Common.UI.ButtonColored.textNewColor": "사용자 정의 색상 추가", "Common.UI.ComboBorderSize.txtNoBorders": "테두리 없음", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "테두리 없음", "Common.UI.ComboDataView.emptyComboText": "스타일 없음", "Common.UI.ExtendedColorDialog.addButtonText": "Add", "Common.UI.ExtendedColorDialog.textCurrent": "현재", "Common.UI.ExtendedColorDialog.textHexErr": "입력 한 값이 잘못되었습니다. <br> 000000에서 FFFFFF 사이의 값을 입력하십시오.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "입력 한 값이 잘못되었습니다. <br> 0에서 255 사이의 숫자 값을 입력하십시오.", "Common.UI.HSBColorPicker.textNoColor": "색상 없음", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "비밀번호 숨기기", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "비밀번호 표시", "Common.UI.SearchDialog.textHighlight": "결과 강조 표시", "Common.UI.SearchDialog.textMatchCase": "대소 문자를 구분합니다", "Common.UI.SearchDialog.textReplaceDef": "대체 텍스트 입력", "Common.UI.SearchDialog.textSearchStart": "여기에 텍스트를 입력하십시오", "Common.UI.SearchDialog.textTitle": "찾기 및 바꾸기", "Common.UI.SearchDialog.textTitle2": "찾기", "Common.UI.SearchDialog.textWholeWords": "전체 단어 만", "Common.UI.SearchDialog.txtBtnHideReplace": "바꾸기 숨기기", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "모두 바꾸기", "Common.UI.SynchronizeTip.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.UI.SynchronizeTip.textSynchronize": "다른 사용자가 문서를 변경했습니다. <br> 클릭하여 변경 사항을 저장하고 업데이트를 다시로드하십시오.", "Common.UI.ThemeColorPalette.textStandartColors": "표준 색상", "Common.UI.ThemeColorPalette.textThemeColors": "테마 색", "Common.UI.Themes.txtThemeClassicLight": "전통적인 밝은 색상", "Common.UI.Themes.txtThemeDark": "어두운", "Common.UI.Themes.txtThemeLight": "밝은", "Common.UI.Window.cancelButtonText": "취소", "Common.UI.Window.closeButtonText": "닫기", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "확인", "Common.UI.Window.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.UI.Window.textError": "오류", "Common.UI.Window.textInformation": "정보", "Common.UI.Window.textWarning": "경고", "Common.UI.Window.yesButtonText": "예", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "주소 :", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email :", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel .:", "Common.Views.About.txtVersion": "버전", "Common.Views.AutoCorrectDialog.textAdd": "추가", "Common.Views.AutoCorrectDialog.textApplyText": "입력과 동시에 적용", "Common.Views.AutoCorrectDialog.textAutoCorrect": "자동 고침", "Common.Views.AutoCorrectDialog.textAutoFormat": "입력 할 때 자동 서식", "Common.Views.AutoCorrectDialog.textBulleted": "자동 글머리 기호 목록", "Common.Views.AutoCorrectDialog.textBy": "작성", "Common.Views.AutoCorrectDialog.textDelete": "삭제", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "더블 스페이스로 마침표 추가", "Common.Views.AutoCorrectDialog.textFLCells": "표 셀의 첫 글자를 대문자로", "Common.Views.AutoCorrectDialog.textFLSentence": "영어 문장의 첫 글자를 대문자로", "Common.Views.AutoCorrectDialog.textHyperlink": "네트워크 경로 하이퍼링크", "Common.Views.AutoCorrectDialog.textHyphens": "하이픈(--)과 대시(—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "수식 자동 고침", "Common.Views.AutoCorrectDialog.textNumbered": "자동 번호 매기기 목록", "Common.Views.AutoCorrectDialog.textQuotes": "\"직선 따옴표\" 및 \"곱게 따옴표\"", "Common.Views.AutoCorrectDialog.textRecognized": "인식된 함수", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "다음 표현식은 인식 된 수식입니다. 자동으로 이탤릭체로 될 수는 없습니다.", "Common.Views.AutoCorrectDialog.textReplace": "바꾸기", "Common.Views.AutoCorrectDialog.textReplaceText": "입력시 바꿈", "Common.Views.AutoCorrectDialog.textReplaceType": "입력시 텍스트 바꿈", "Common.Views.AutoCorrectDialog.textReset": "재설정", "Common.Views.AutoCorrectDialog.textResetAll": "기본값으로 재설정", "Common.Views.AutoCorrectDialog.textRestore": "복구", "Common.Views.AutoCorrectDialog.textTitle": "자동 고침", "Common.Views.AutoCorrectDialog.textWarnAddRec": "인식되는 함수는 대소 A ~ Z까지의 문자만을 포함해야합니다.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "추가한 모든 표현식이 삭제되고 삭제된 표현식이 복원됩니다. 계속하시겠습니까?", "Common.Views.AutoCorrectDialog.warnReplace": "%1에 대한 자동 고침 항목이 이미 있습니다. 교체하시겠습니까?", "Common.Views.AutoCorrectDialog.warnReset": "추가한 모든 자동 고침이 삭제되고 변경된 자동 수정이 원래 값으로 복원됩니다. 계속하시겠습니까?", "Common.Views.AutoCorrectDialog.warnRestore": "%1의 자동 고침 항목이 원래 값으로 재설정됩니다. 계속하시겠습니까?", "Common.Views.Chat.textSend": "보내기", "Common.Views.Comments.mniAuthorAsc": "A에서 Z까지 작성자", "Common.Views.Comments.mniAuthorDesc": "Z에서 A까지 작성자", "Common.Views.Comments.mniDateAsc": "가장 오래된", "Common.Views.Comments.mniDateDesc": "최신", "Common.Views.Comments.mniFilterGroups": "그룹별 필터링", "Common.Views.Comments.mniPositionAsc": "위에서 부터", "Common.Views.Comments.mniPositionDesc": "아래로 부터", "Common.Views.Comments.textAdd": "추가", "Common.Views.Comments.textAddComment": "덧글 추가", "Common.Views.Comments.textAddCommentToDoc": "문서에 설명 추가", "Common.Views.Comments.textAddReply": "답장 추가", "Common.Views.Comments.textAll": "모두", "Common.Views.Comments.textAnonym": "손님", "Common.Views.Comments.textCancel": "취소", "Common.Views.Comments.textClose": "닫기", "Common.Views.Comments.textClosePanel": "코멘트 닫기", "Common.Views.Comments.textComments": "Comments", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "여기에 의견을 입력하십시오", "Common.Views.Comments.textHintAddComment": "덧글 추가", "Common.Views.Comments.textOpenAgain": "다시 열기", "Common.Views.Comments.textReply": "Reply", "Common.Views.Comments.textResolve": "해결", "Common.Views.Comments.textResolved": "해결됨", "Common.Views.Comments.textSort": "코멘트 분류", "Common.Views.Comments.textViewResolved": "코멘트를 다시 열 수 있는 권한이 없습니다", "Common.Views.Comments.txtEmpty": "문서에 코멘트가 없습니다", "Common.Views.CopyWarningDialog.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.Views.CopyWarningDialog.textMsg": "편집기 도구 모음 단추 및 컨텍스트 메뉴 작업을 사용하여 복사, 잘라 내기 및 붙여 넣기 작업은이 편집기 탭 내에서만 수행됩니다. <br> <br> 외부 응용 프로그램으로 복사하거나 붙여 넣으려면 편집기 탭은 다음과 같은 키보드 조합을 사용합니다 : ", "Common.Views.CopyWarningDialog.textTitle": "작업 복사, 잘라 내기 및 붙여 넣기", "Common.Views.CopyWarningDialog.textToCopy": "복사", "Common.Views.CopyWarningDialog.textToCut": "잘라 내기", "Common.Views.CopyWarningDialog.textToPaste": "붙여 넣기", "Common.Views.DocumentAccessDialog.textLoading": "로드 중 ...", "Common.Views.DocumentAccessDialog.textTitle": "공유 설정", "Common.Views.ExternalDiagramEditor.textTitle": "차트 편집기", "Common.Views.Header.labelCoUsersDescr": "파일을 편집 중인 사용자:", "Common.Views.Header.textAddFavorite": "즐겨찾기에 추가", "Common.Views.Header.textAdvSettings": "고급 설정", "Common.Views.Header.textBack": "파일 위치 열기", "Common.Views.Header.textCompactView": "보기 컴팩트 도구 모음", "Common.Views.Header.textHideLines": "눈금자 숨기기", "Common.Views.Header.textHideNotes": "메모 숨기기", "Common.Views.Header.textHideStatusBar": "상태 표시 줄 숨기기", "Common.Views.Header.textRemoveFavorite": "즐겨찾기에서 제거", "Common.Views.Header.textSaveBegin": "저장 중 ...", "Common.Views.Header.textSaveChanged": "수정된", "Common.Views.Header.textSaveEnd": "모든 변경 사항이 저장되었습니다", "Common.Views.Header.textSaveExpander": "모든 변경 사항이 저장되었습니다", "Common.Views.Header.textZoom": "확대/축소", "Common.Views.Header.tipAccessRights": "문서 액세스 권한 관리", "Common.Views.Header.tipDownload": "파일을 다운로드", "Common.Views.Header.tipGoEdit": "현재 파일 편집", "Common.Views.Header.tipPrint": "파일 출력", "Common.Views.Header.tipRedo": "다시 실행", "Common.Views.Header.tipSave": "저장", "Common.Views.Header.tipUndo": "실행 취소", "Common.Views.Header.tipUndock": "별도 창으로 이동", "Common.Views.Header.tipViewSettings": "보기 설정", "Common.Views.Header.tipViewUsers": "사용자보기 및 문서 액세스 권한 관리", "Common.Views.Header.txtAccessRights": "액세스 권한 변경", "Common.Views.Header.txtRename": "이름 바꾸기", "Common.Views.History.textCloseHistory": "버전기록 닫기", "Common.Views.History.textHide": "축소", "Common.Views.History.textHideAll": "자세한 변경 사항 숨기기", "Common.Views.History.textRestore": "복구", "Common.Views.History.textShow": "확장", "Common.Views.History.textShowAll": "자세한 변경 사항 표시", "Common.Views.History.textVer": "ver. ", "Common.Views.ImageFromUrlDialog.textUrl": "이미지 URL 붙여 넣기 :", "Common.Views.ImageFromUrlDialog.txtEmpty": "이 입력란은 필수 항목", "Common.Views.ImageFromUrlDialog.txtNotUrl": "이 필드는 \"http://www.example.com\"형식의 URL이어야합니다.", "Common.Views.InsertTableDialog.textInvalidRowsCols": "유효한 행과 열 번호를 지정해야합니다.", "Common.Views.InsertTableDialog.txtColumns": "열 수", "Common.Views.InsertTableDialog.txtMaxText": "이 필드의 최대 값은 {0}입니다.", "Common.Views.InsertTableDialog.txtMinText": "이 필드의 최소값은 {0}입니다.", "Common.Views.InsertTableDialog.txtRows": "행 수", "Common.Views.InsertTableDialog.txtTitle": "표 크기", "Common.Views.InsertTableDialog.txtTitleSplit": "셀 분할", "Common.Views.LanguageDialog.labelSelect": "문서 언어 선택", "Common.Views.ListSettingsDialog.textBulleted": "글머리 기호", "Common.Views.ListSettingsDialog.textNumbering": "매겨진 번호", "Common.Views.ListSettingsDialog.tipChange": "글 머리 기호 변경", "Common.Views.ListSettingsDialog.txtBullet": "글머리 기호", "Common.Views.ListSettingsDialog.txtColor": "색상", "Common.Views.ListSettingsDialog.txtNewBullet": "새로운 글머리 기호", "Common.Views.ListSettingsDialog.txtNone": "없음", "Common.Views.ListSettingsDialog.txtOfText": "전체의 %", "Common.Views.ListSettingsDialog.txtSize": "크기", "Common.Views.ListSettingsDialog.txtStart": "시작", "Common.Views.ListSettingsDialog.txtSymbol": "기호", "Common.Views.ListSettingsDialog.txtTitle": "목록설정", "Common.Views.ListSettingsDialog.txtType": "유형", "Common.Views.OpenDialog.closeButtonText": "파일 닫기", "Common.Views.OpenDialog.txtEncoding": "인코딩", "Common.Views.OpenDialog.txtIncorrectPwd": "비밀번호가 맞지 않음", "Common.Views.OpenDialog.txtOpenFile": "파일을 열려면 암호를 입력하십시오.", "Common.Views.OpenDialog.txtPassword": "비밀번호", "Common.Views.OpenDialog.txtProtected": "암호를 입력하고 파일을 열면 파일의 현재 암호가 재설정됩니다.", "Common.Views.OpenDialog.txtTitle": "% 1 옵션 선택", "Common.Views.OpenDialog.txtTitleProtected": "보호 된 파일", "Common.Views.PasswordDialog.txtDescription": "문서 보호용 비밀번호를 세팅하세요", "Common.Views.PasswordDialog.txtIncorrectPwd": "확인 비밀번호가 같지 않음", "Common.Views.PasswordDialog.txtPassword": "암호", "Common.Views.PasswordDialog.txtRepeat": "비밀번호 반복", "Common.Views.PasswordDialog.txtTitle": "비밀번호 설정", "Common.Views.PasswordDialog.txtWarning": "주의: 암호를 잊으면 복구할 수 없습니다. 암호는 대/소문자를 구분합니다. 이 코드를 안전한 곳에 보관하세요.", "Common.Views.PluginDlg.textLoading": "로드 중", "Common.Views.Plugins.groupCaption": "플러그인", "Common.Views.Plugins.strPlugins": "플러그인", "Common.Views.Plugins.textLoading": "로드 중", "Common.Views.Plugins.textStart": "시작", "Common.Views.Plugins.textStop": "정지", "Common.Views.Protection.hintAddPwd": "비밀번호로 암호화", "Common.Views.Protection.hintPwd": "비밀번호 변경 또는 삭제", "Common.Views.Protection.hintSignature": "디지털 서명 또는 서명 라인을 추가 ", "Common.Views.Protection.txtAddPwd": "비밀번호 추가", "Common.Views.Protection.txtChangePwd": "비밀번호를 변경", "Common.Views.Protection.txtDeletePwd": "비밀번호 삭제", "Common.Views.Protection.txtEncrypt": "암호화", "Common.Views.Protection.txtInvisibleSignature": "디지털 서명을 추가", "Common.Views.Protection.txtSignature": "서명", "Common.Views.Protection.txtSignatureLine": "서명란 추가", "Common.Views.RenameDialog.textName": "파일 이름", "Common.Views.RenameDialog.txtInvalidName": "파일 이름에 다음 문자를 포함 할 수 없습니다 :", "Common.Views.ReviewChanges.hintNext": "다음 변경 사항", "Common.Views.ReviewChanges.hintPrev": "이전 변경으로", "Common.Views.ReviewChanges.strFast": "빠르게", "Common.Views.ReviewChanges.strFastDesc": "실시간 협력 편집. 모든 변경사항들은 자동적으로 저장됨.", "Common.Views.ReviewChanges.strStrict": "엄격한", "Common.Views.ReviewChanges.strStrictDesc": "귀하와 다른 사람이 변경사항을 동기화 하려면 '저장'버튼을 사용하세요.", "Common.Views.ReviewChanges.tipAcceptCurrent": "현재 변경 내용 적용", "Common.Views.ReviewChanges.tipCoAuthMode": "협력 편집 모드 세팅", "Common.Views.ReviewChanges.tipCommentRem": "코멘트 삭제", "Common.Views.ReviewChanges.tipCommentRemCurrent": "현재 코멘트 삭제", "Common.Views.ReviewChanges.tipCommentResolve": "코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "현 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.tipHistory": "버전 표시", "Common.Views.ReviewChanges.tipRejectCurrent": "현재 변경 거부", "Common.Views.ReviewChanges.tipReview": "변경 내역 추적", "Common.Views.ReviewChanges.tipReviewView": "변경사항이 표시될 모드 선택", "Common.Views.ReviewChanges.tipSetDocLang": "문서 언어 설정", "Common.Views.ReviewChanges.tipSetSpelling": "맞춤법 검사", "Common.Views.ReviewChanges.tipSharing": "문서 액세스 권한 관리", "Common.Views.ReviewChanges.txtAccept": "수락", "Common.Views.ReviewChanges.txtAcceptAll": "모든 변경 내용 적용", "Common.Views.ReviewChanges.txtAcceptChanges": "변경 접수", "Common.Views.ReviewChanges.txtAcceptCurrent": "현재 변경 내용 적용", "Common.Views.ReviewChanges.txtChat": "채팅", "Common.Views.ReviewChanges.txtClose": "완료", "Common.Views.ReviewChanges.txtCoAuthMode": "공동 편집 모드", "Common.Views.ReviewChanges.txtCommentRemAll": "모든 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemCurrent": "현재 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemMy": "내 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "내 현재 댓글 삭제", "Common.Views.ReviewChanges.txtCommentRemove": "삭제", "Common.Views.ReviewChanges.txtCommentResolve": "해결", "Common.Views.ReviewChanges.txtCommentResolveAll": "모든 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "현 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtCommentResolveMy": "내 코멘트를 해결된 것을 표시", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "내 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtDocLang": "언어", "Common.Views.ReviewChanges.txtFinal": "모든 변경 접수됨 (미리보기)", "Common.Views.ReviewChanges.txtFinalCap": "최종", "Common.Views.ReviewChanges.txtHistory": "버전 기록", "Common.Views.ReviewChanges.txtMarkup": "모든 변경 (편집)", "Common.Views.ReviewChanges.txtMarkupCap": "마크업", "Common.Views.ReviewChanges.txtNext": "다음", "Common.Views.ReviewChanges.txtOriginal": "모든 변경 거부됨 (미리보기)", "Common.Views.ReviewChanges.txtOriginalCap": "오리지널", "Common.Views.ReviewChanges.txtPrev": "이전", "Common.Views.ReviewChanges.txtReject": "거부", "Common.Views.ReviewChanges.txtRejectAll": "모든 변경 사항 거부", "Common.Views.ReviewChanges.txtRejectChanges": "변경 거부", "Common.Views.ReviewChanges.txtRejectCurrent": "현재 변경 거부", "Common.Views.ReviewChanges.txtSharing": "공유", "Common.Views.ReviewChanges.txtSpelling": "맞춤법 검사", "Common.Views.ReviewChanges.txtTurnon": "변경 내역 추적", "Common.Views.ReviewChanges.txtView": "디스플레이 모드", "Common.Views.ReviewPopover.textAdd": "추가", "Common.Views.ReviewPopover.textAddReply": "댓글추가", "Common.Views.ReviewPopover.textCancel": "취소", "Common.Views.ReviewPopover.textClose": "닫기", "Common.Views.ReviewPopover.textEdit": "확인", "Common.Views.ReviewPopover.textMention": "+이 내용은 이 문서에 접근시 이메일을 통해 전달됩니다.", "Common.Views.ReviewPopover.textMentionNotify": "+이 내용은 사용자에게 이메일을 통해서 알려집니다.", "Common.Views.ReviewPopover.textOpenAgain": "다시 열기", "Common.Views.ReviewPopover.textReply": "댓글", "Common.Views.ReviewPopover.textResolve": "해결", "Common.Views.ReviewPopover.textViewResolved": "코멘트를 다시 열 수 있는 권한이 없습니다", "Common.Views.ReviewPopover.txtDeleteTip": "삭제", "Common.Views.ReviewPopover.txtEditTip": "편집", "Common.Views.SaveAsDlg.textLoading": "로드 중", "Common.Views.SaveAsDlg.textTitle": "저장 폴더", "Common.Views.SelectFileDlg.textLoading": "로드 중", "Common.Views.SelectFileDlg.textTitle": "데이터 소스 선택", "Common.Views.SignDialog.textBold": "볼드체", "Common.Views.SignDialog.textCertificate": "인증", "Common.Views.SignDialog.textChange": "변경", "Common.Views.SignDialog.textInputName": "서명자 성함을 입력하세요", "Common.Views.SignDialog.textItalic": "이탤릭", "Common.Views.SignDialog.textNameError": "서명자의 이름은 비워둘 수 없습니다.", "Common.Views.SignDialog.textPurpose": "이 문서에 서명하는 목적", "Common.Views.SignDialog.textSelect": "선택", "Common.Views.SignDialog.textSelectImage": "이미지 선택", "Common.Views.SignDialog.textSignature": "서명은 처럼 보임", "Common.Views.SignDialog.textTitle": "서명문서", "Common.Views.SignDialog.textUseImage": "또는 서명으로 그림을 사용하려면 '이미지 선택'을 클릭", "Common.Views.SignDialog.textValid": "%1에서 %2까지 유효", "Common.Views.SignDialog.tipFontName": "폰트명", "Common.Views.SignDialog.tipFontSize": "글꼴 크기", "Common.Views.SignSettingsDialog.textAllowComment": "서명 대화창에 서명자의 코멘트 추가 허용", "Common.Views.SignSettingsDialog.textInfoEmail": "이메일", "Common.Views.SignSettingsDialog.textInfoName": "이름", "Common.Views.SignSettingsDialog.textInfoTitle": "서명자 타이틀", "Common.Views.SignSettingsDialog.textInstructions": "서명자용 지침", "Common.Views.SignSettingsDialog.textShowDate": "서명라인에 서명 날짜를 보여주세요", "Common.Views.SignSettingsDialog.textTitle": "서명 셋업", "Common.Views.SignSettingsDialog.txtEmpty": "이 입력란은 필수 항목", "Common.Views.SymbolTableDialog.textCharacter": "문자", "Common.Views.SymbolTableDialog.textCode": "유니코드 HEX 값", "Common.Views.SymbolTableDialog.textCopyright": "저작권 표시", "Common.Views.SymbolTableDialog.textDCQuote": "큰 따옴표 닫기", "Common.Views.SymbolTableDialog.textDOQuote": "二重の引用符(左）", "Common.Views.SymbolTableDialog.textEllipsis": "말줄임표", "Common.Views.SymbolTableDialog.textEmDash": "Em 대시", "Common.Views.SymbolTableDialog.textEmSpace": "Em 공백", "Common.Views.SymbolTableDialog.textEnDash": "En 대시", "Common.Views.SymbolTableDialog.textEnSpace": "En 공백", "Common.Views.SymbolTableDialog.textFont": "글꼴", "Common.Views.SymbolTableDialog.textNBHyphen": "줄 바꿈없는 하이픈", "Common.Views.SymbolTableDialog.textNBSpace": "줄 바꿈 없는 공백", "Common.Views.SymbolTableDialog.textPilcrow": "단락기호", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 칸", "Common.Views.SymbolTableDialog.textRange": "범위", "Common.Views.SymbolTableDialog.textRecent": "최근 사용한 기호", "Common.Views.SymbolTableDialog.textRegistered": "등록된 서명", "Common.Views.SymbolTableDialog.textSCQuote": "작은 따옴표 닫기", "Common.Views.SymbolTableDialog.textSection": "섹션 기호", "Common.Views.SymbolTableDialog.textShortcut": "단축키", "Common.Views.SymbolTableDialog.textSHyphen": "소프트 하이픈", "Common.Views.SymbolTableDialog.textSOQuote": "작은 따옴표 (왼쪽)", "Common.Views.SymbolTableDialog.textSpecial": "특수 문자", "Common.Views.SymbolTableDialog.textSymbols": "기호", "Common.Views.SymbolTableDialog.textTitle": "기호", "Common.Views.SymbolTableDialog.textTradeMark": "로고기호", "Common.Views.UserNameDialog.textDontShow": "다시 표시하지 않음", "Common.Views.UserNameDialog.textLabel": "라벨:", "Common.Views.UserNameDialog.textLabelError": "라벨은 비워 둘 수 없습니다.", "PE.Controllers.LeftMenu.leavePageText": "이 문서에 저장되지 않은 모든 변경 사항이 손실됩니다. <br>\"취소\"를 클릭한 다음 \"저장\"을 클릭하여 저장하십시오. 저장되지 않은 모든 변경 사항을 취소하려면 \"확인\"을 클릭하십시오.", "PE.Controllers.LeftMenu.newDocumentTitle": "명명되지 않은 프레젠테이션", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "경고", "PE.Controllers.LeftMenu.requestEditRightsText": "편집 권한 요청 중 ...", "PE.Controllers.LeftMenu.textLoadHistory": "버전 기록로드 중 ...", "PE.Controllers.LeftMenu.textNoTextFound": "검색 한 데이터를 찾을 수 없습니다. 검색 옵션을 조정하십시오.", "PE.Controllers.LeftMenu.textReplaceSkipped": "대체가 이루어졌습니다. {0} 건은 건너 뛰었습니다.", "PE.Controllers.LeftMenu.textReplaceSuccess": "검색이 완료되었습니다. {0} 횟수가 대체되었습니다. ", "PE.Controllers.LeftMenu.txtUntitled": "제목없음", "PE.Controllers.Main.applyChangesTextText": "데이터로드 중 ...", "PE.Controllers.Main.applyChangesTitleText": "데이터로드 중", "PE.Controllers.Main.convertationTimeoutText": "전환 시간 초과를 초과했습니다.", "PE.Controllers.Main.criticalErrorExtText": "문서 목록으로 돌아가려면 \"OK\"를 누르십시오.", "PE.Controllers.Main.criticalErrorTitle": "오류", "PE.Controllers.Main.downloadErrorText": "다운로드하지 못했습니다.", "PE.Controllers.Main.downloadTextText": "프리젠 테이션 다운로드 중 ...", "PE.Controllers.Main.downloadTitleText": "프레젠테이션 다운로드", "PE.Controllers.Main.errorAccessDeny": "권한이없는 작업을 수행하려고합니다. <br> Document Server 관리자에게 문의하십시오.", "PE.Controllers.Main.errorBadImageUrl": "이미지 URL이 잘못되었습니다.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "서버 연결이 끊어졌습니다. 지금 문서를 편집 할 수 없습니다.", "PE.Controllers.Main.errorComboSeries": "혼합형 차트를 만들려면 최소 2 개의 데이터를 선택합니다.", "PE.Controllers.Main.errorConnectToServer": "문서를 저장할 수 없습니다. 연결 설정을 확인하거나 관리자에게 문의하십시오.<br>'확인'버튼을 클릭하면 문서를 다운로드하라는 메시지가 나타납니다.", "PE.Controllers.Main.errorDatabaseConnection": "외부 오류입니다. <br> 데이터베이스 연결 오류입니다. 오류가 계속 발생하면 지원 담당자에게 문의하십시오.", "PE.Controllers.Main.errorDataEncrypted": "암호화 변경 사항이 수신되었으며 해독할 수 없습니다.", "PE.Controllers.Main.errorDataRange": "잘못된 참조 대상 입니다.", "PE.Controllers.Main.errorDefaultMessage": "오류 코드 : % 1", "PE.Controllers.Main.errorEditingDownloadas": "  문서 작업 중에 알수 없는 장애가 발생했습니다.<br>  \"다른 이름으로 다운로드\"를 선택하여 파일을 현재 사용 중인 컴퓨터 하드 디스크에 저장하시기 바랍니다.", "PE.Controllers.Main.errorEditingSaveas": "  문서 작업 중에 알수 없는 장애가 발생하였습니다.<br>  \"다른 이름으로 저장...\"을 선택하여 현재 사용 중인 컴퓨터의 하드 디스크에 저장하시기 바랍니다.", "PE.Controllers.Main.errorEmailClient": "이메일 클라이언트를 찾을 수 없습니다.", "PE.Controllers.Main.errorFilePassProtect": "문서가 암호로 보호되어 있습니다.", "PE.Controllers.Main.errorFileSizeExceed": "파일의 크기가 서버에서 정해진 범위를 초과 했습니다. 문서 서버 관리자에게 해당 내용에 대한 자세한 안내를 받아 보시기 바랍니다.", "PE.Controllers.Main.errorForceSave": "파일 저장중 문제 발생됨. 컴퓨터 하드 드라이브에 파일을 저장하려면 '로 다운로드' 옵션을 사용 또는 나중에 다시 시도하세요.", "PE.Controllers.Main.errorKeyEncrypt": "알 수없는 키 설명자", "PE.Controllers.Main.errorKeyExpire": "키 설명자가 만료되었습니다", "PE.Controllers.Main.errorLoadingFont": "글꼴이 로드되지 않았습니다. <br>문서 관리 관리자에게 문의하십시오.", "PE.Controllers.Main.errorProcessSaveResult": "저장하지 못했습니다.", "PE.Controllers.Main.errorServerVersion": "에디터 버전이 업데이트되었습니다. 페이지가 다시로드되어 변경 사항이 적용됩니다.", "PE.Controllers.Main.errorSessionAbsolute": "문서 편집 세션이 만료되었습니다. 페이지를 새로 고침하십시오.", "PE.Controllers.Main.errorSessionIdle": "문서가 오랫동안 편집되지 않았습니다. 페이지를 새로 고침하십시오.", "PE.Controllers.Main.errorSessionToken": "서버 연결이 중단되었습니다. 페이지를 새로 고침하십시오.", "PE.Controllers.Main.errorSetPassword": "비밀번호를 재설정할 수 없습니다.", "PE.Controllers.Main.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 시트에 데이터를 다음과 같은 순서로 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "PE.Controllers.Main.errorToken": "문서 보안 토큰이 올바르게 구성되지 않았습니다. <br> Document Server 관리자에게 문의하십시오.", "PE.Controllers.Main.errorTokenExpire": "문서 보안 토큰이 만료되었습니다. <br> Document Server 관리자에게 문의하십시오.", "PE.Controllers.Main.errorUpdateVersion": "파일 버전이 변경되었습니다. 페이지가 다시로드됩니다.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "네트워크 연결이 복원되었으며 파일 버전이 변경되었습니다. <br>계속 작업하기 전에 데이터 손실을 방지하기 위해 파일을 다운로드하거나 내용을 복사한 다음 이 페이지를 새로 고쳐야 합니다.", "PE.Controllers.Main.errorUserDrop": "파일에 지금 액세스 할 수 없습니다.", "PE.Controllers.Main.errorUsersExceed": "가격 책정 계획에서 허용 한 사용자 수가 초과되었습니다.", "PE.Controllers.Main.errorViewerDisconnect": "연결이 끊어졌습니다. 문서를 볼 수는 <br> <br>하지만 연결이 복원 될 때까지 다운로드하거나 인쇄 할 수 없습니다.", "PE.Controllers.Main.leavePageText": "이 프레젠테이션에서 변경 사항을 저장하지 않았습니다.이 페이지에 머물러서 \"저장 \"을 클릭하여 저장하십시오. \"이 페이지를 남겨두기 \"를 클릭하여 모두 버리십시오. 저장되지 않은 변경 사항. ", "PE.Controllers.Main.leavePageTextOnClose": "이 문서에 저장되지 않은 모든 변경 사항이 손실됩니다. <br>\"취소\"를 클릭한 다음 \"저장\"을 클릭하여 저장하십시오. 저장되지 않은 모든 변경 사항을 취소하려면 \"확인\"을 클릭하십시오.", "PE.Controllers.Main.loadFontsTextText": "데이터로드 중 ...", "PE.Controllers.Main.loadFontsTitleText": "데이터로드 중", "PE.Controllers.Main.loadFontTextText": "데이터로드 중 ...", "PE.Controllers.Main.loadFontTitleText": "데이터로드 중", "PE.Controllers.Main.loadImagesTextText": "이미지로드 중 ...", "PE.Controllers.Main.loadImagesTitleText": "이미지로드 중", "PE.Controllers.Main.loadImageTextText": "이미지로드 중 ...", "PE.Controllers.Main.loadImageTitleText": "이미지로드 중", "PE.Controllers.Main.loadingDocumentTextText": "프레젠테이션로드 중 ...", "PE.Controllers.Main.loadingDocumentTitleText": "프레젠테이션로드 중", "PE.Controllers.Main.loadThemeTextText": "테마로드 중 ...", "PE.Controllers.Main.loadThemeTitleText": "테마로드 중", "PE.Controllers.Main.notcriticalErrorTitle": "경고", "PE.Controllers.Main.openErrorText": "파일을 여는 동안 오류가 발생했습니다", "PE.Controllers.Main.openTextText": "프레젠테이션 열기 ...", "PE.Controllers.Main.openTitleText": "프레젠테이션 열기", "PE.Controllers.Main.printTextText": "프레젠테이션 인쇄 중 ...", "PE.Controllers.Main.printTitleText": "프레젠테이션 인쇄", "PE.Controllers.Main.reloadButtonText": "페이지 새로 고침", "PE.Controllers.Main.requestEditFailedMessageText": "누군가이 프레젠테이션을 지금 편집 중입니다. 나중에 다시 시도하십시오.", "PE.Controllers.Main.requestEditFailedTitleText": "액세스가 거부되었습니다", "PE.Controllers.Main.saveErrorText": "파일을 저장하는 동안 오류가 발생했습니다.", "PE.Controllers.Main.saveErrorTextDesktop": "이 파일을 저장하거나 생성할 수 없습니다. <br>가능한 이유는 다음과 같습니다. <br> 1. 파일이 읽기 전용입니다. <br> 2. 다른 사용자가 파일을 편집 중입니다. <br> 3. 디스크가 가득 찼거나 손상되었습니다.", "PE.Controllers.Main.saveTextText": "프레젠테이션 저장 중 ...", "PE.Controllers.Main.saveTitleText": "프리젠 테이션 저장 중", "PE.Controllers.Main.scriptLoadError": "연결 속도가 느려, 일부 요소들이 로드되지 않았습니다. 페이지를 다시 새로 고침해주세요.", "PE.Controllers.Main.splitDividerErrorText": "행 수는 % 1의 제수이어야합니다.", "PE.Controllers.Main.splitMaxColsErrorText": "열 수가 % 1보다 작아야합니다.", "PE.Controllers.Main.splitMaxRowsErrorText": "행 수는 % 1보다 적어야합니다.", "PE.Controllers.Main.textAnonymous": "익명", "PE.Controllers.Main.textApplyAll": "모든 방정식에 적용", "PE.Controllers.Main.textBuyNow": "웹 사이트 방문", "PE.Controllers.Main.textChangesSaved": "모든 변경 사항이 저장되었습니다", "PE.Controllers.Main.textClose": "닫기", "PE.Controllers.Main.textCloseTip": "도움말을 닫으려면 클릭하십시오", "PE.Controllers.Main.textContactUs": "영업 담당자에게 문의", "PE.Controllers.Main.textConvertEquation": "방정식은 더 이상 지원되지 않는 이전 버전의 방정식 편집기를 사용하여 생성되었습니다. 편집하려면 수식을 Office Math ML 형식으로 변환하세요. <br>지금 변환하시겠습니까?", "PE.Controllers.Main.textCustomLoader": "라이센스 조건에 따라 교체할 권한이 없습니다. <br>견적은 당사 영업부에 문의해 주십시오.", "PE.Controllers.Main.textDisconnect": "네트워크 연결 끊김", "PE.Controllers.Main.textGuest": "게스트", "PE.Controllers.Main.textHasMacros": "파일에 자동 매크로가 포함되어 있습니다. <br> 매크로를 실행 하시겠습니까?", "PE.Controllers.Main.textLearnMore": "자세히", "PE.Controllers.Main.textLoadingDocument": "프레젠테이션로드 중", "PE.Controllers.Main.textLongName": "128자 미만의 이름을 입력하세요.", "PE.Controllers.Main.textNoLicenseTitle": "라이센스 수를 제한했습니다.", "PE.Controllers.Main.textPaidFeature": "유료기능", "PE.Controllers.Main.textReconnect": "연결이 복원되었습니다", "PE.Controllers.Main.textRemember": "모든 파일에 대한 선택 사항을 기억하기", "PE.Controllers.Main.textRenameError": "사용자 이름은 비워둘 수 없습니다.", "PE.Controllers.Main.textRenameLabel": "협업에 사용할 이름을 입력합니다", "PE.Controllers.Main.textShape": "도형", "PE.Controllers.Main.textStrict": "엄격 모드", "PE.Controllers.Main.textText": "본문", "PE.Controllers.Main.textTryUndoRedo": "빠른 편집 편집 모드에서는 실행 취소 / 다시 실행 기능이 비활성화됩니다. <br>\"엄격 모드 \"버튼을 클릭하면 Strict 동시 편집 모드로 전환되어 파일을 편집 할 수 있습니다. 다른 사용자가 방해를해서 저장 한 후에 만 ​​변경 사항을 보내십시오. 편집자 고급 설정을 사용하여 공동 편집 모드간에 전환 할 수 있습니다. ", "PE.Controllers.Main.textTryUndoRedoWarn": "빠른 공동 편집 모드에서 실행 취소 / 다시 실행 기능을 사용할 수 없습니다.", "PE.Controllers.Main.titleLicenseExp": "라이센스 만료", "PE.Controllers.Main.titleServerVersion": "편집기가 업데이트되었습니다", "PE.Controllers.Main.txtAddFirstSlide": "첫번째 슬라이드를 추가하려면 클릭", "PE.Controllers.Main.txtAddNotes": "노트를 추가하려면 클릭", "PE.Controllers.Main.txtArt": "여기에 귀하의 텍스트", "PE.Controllers.Main.txtBasicShapes": "기본 도형", "PE.Controllers.Main.txtButtons": "버튼", "PE.Controllers.Main.txtCallouts": "설명선", "PE.Controllers.Main.txtCharts": "차트", "PE.Controllers.Main.txtClipArt": "클립 아트", "PE.Controllers.Main.txtDateTime": "날짜 및 시간", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "차트 제목", "PE.Controllers.Main.txtEditingMode": "편집 모드 설정 ...", "PE.Controllers.Main.txtErrorLoadHistory": "이력을 로드하지 못했습니다.", "PE.Controllers.Main.txtFiguredArrows": "블록 화살표", "PE.Controllers.Main.txtFooter": "Footer", "PE.Controllers.Main.txtHeader": "머리글", "PE.Controllers.Main.txtImage": "이미지", "PE.Controllers.Main.txtLines": "선형 테마", "PE.Controllers.Main.txtLoading": "로딩중...", "PE.Controllers.Main.txtMath": "수학", "PE.Controllers.Main.txtMedia": "Media", "PE.Controllers.Main.txtNeedSynchronize": "업데이트가 있습니다.", "PE.Controllers.Main.txtNone": "없음", "PE.Controllers.Main.txtPicture": "그림", "PE.Controllers.Main.txtRectangles": "직사각형", "PE.Controllers.Main.txtSeries": "Series", "PE.Controllers.Main.txtShape_accentBorderCallout1": "설명선 1 (테두리 강조)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "설명선 2 (테두리 강조)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "설명선 3 (테두리 강조)", "PE.Controllers.Main.txtShape_accentCallout1": "설명선 1 (강조선)", "PE.Controllers.Main.txtShape_accentCallout2": "설명선 2 (강조선)", "PE.Controllers.Main.txtShape_accentCallout3": "설명선 3 (강조선)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "되돌리기 또는 이전 버튼", "PE.Controllers.Main.txtShape_actionButtonBeginning": "시작 버튼", "PE.Controllers.Main.txtShape_actionButtonBlank": "공백 버튼", "PE.Controllers.Main.txtShape_actionButtonDocument": "문서버튼", "PE.Controllers.Main.txtShape_actionButtonEnd": "종료버튼", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "다음 버튼", "PE.Controllers.Main.txtShape_actionButtonHelp": "도움말 버튼", "PE.Controllers.Main.txtShape_actionButtonHome": "홈 버튼", "PE.Controllers.Main.txtShape_actionButtonInformation": "상세정보 버튼", "PE.Controllers.Main.txtShape_actionButtonMovie": "동영상 버튼", "PE.Controllers.Main.txtShape_actionButtonReturn": "뒤로가기 버튼", "PE.Controllers.Main.txtShape_actionButtonSound": "소리 버튼", "PE.Controllers.Main.txtShape_arc": "원호", "PE.Controllers.Main.txtShape_bentArrow": "화살표: 굽음", "PE.Controllers.Main.txtShape_bentConnector5": "연결선: 꺾임", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "연결선: 꺾인 화살표", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "연결선: 꺾인 양쪽 화살표", "PE.Controllers.Main.txtShape_bentUpArrow": "화살표: 위로 굽음", "PE.Controllers.Main.txtShape_bevel": "액자", "PE.Controllers.Main.txtShape_blockArc": "막힌 원호", "PE.Controllers.Main.txtShape_borderCallout1": "설명선 1", "PE.Controllers.Main.txtShape_borderCallout2": "설명선 2", "PE.Controllers.Main.txtShape_borderCallout3": "설명선 3", "PE.Controllers.Main.txtShape_bracePair": "양쪽 중괄호", "PE.Controllers.Main.txtShape_callout1": "설명선 1 (테두리없음)", "PE.Controllers.Main.txtShape_callout2": "설명선 2 (테두리없음)", "PE.Controllers.Main.txtShape_callout3": "설명선 3 (테두리없음)", "PE.Controllers.Main.txtShape_can": "원통형", "PE.Controllers.Main.txtShape_chevron": "쉐브론", "PE.Controllers.Main.txtShape_chord": "현", "PE.Controllers.Main.txtShape_circularArrow": "화살표: 원형", "PE.Controllers.Main.txtShape_cloud": "클라우드", "PE.Controllers.Main.txtShape_cloudCallout": "생각풍선: 구름 모양", "PE.Controllers.Main.txtShape_corner": "L도형", "PE.Controllers.Main.txtShape_cube": "정육면체", "PE.Controllers.Main.txtShape_curvedConnector3": "연결선: 구부러짐", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "연결선: 구부러진 화살표", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "연결선: 구부러진 양쪽 화살표", "PE.Controllers.Main.txtShape_curvedDownArrow": "화살표: 아래로 구불어 짐", "PE.Controllers.Main.txtShape_curvedLeftArrow": "화살표: 왼쪽으로 구불어 짐", "PE.Controllers.Main.txtShape_curvedRightArrow": "화살표: 오른쪽으로 구불어 짐", "PE.Controllers.Main.txtShape_curvedUpArrow": "화살표: 위로 구불어 짐", "PE.Controllers.Main.txtShape_decagon": "십각형", "PE.Controllers.Main.txtShape_diagStripe": "대각선 줄무늬", "PE.Controllers.Main.txtShape_diamond": "다이아몬드", "PE.Controllers.Main.txtShape_dodecagon": "십이각형", "PE.Controllers.Main.txtShape_donut": "도넛", "PE.Controllers.Main.txtShape_doubleWave": "이중 물결", "PE.Controllers.Main.txtShape_downArrow": "화살표: 아래쪽", "PE.Controllers.Main.txtShape_downArrowCallout": "설명선: 아래쪽 화살표", "PE.Controllers.Main.txtShape_ellipse": "타원형", "PE.Controllers.Main.txtShape_ellipseRibbon": "리본: 아래로 구불어지고 기울어짐 ", "PE.Controllers.Main.txtShape_ellipseRibbon2": "리본: 위로 구불어지고 기울어짐 ", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "순서도: 대체 프로세스", "PE.Controllers.Main.txtShape_flowChartCollate": "순서도: 일치", "PE.Controllers.Main.txtShape_flowChartConnector": "순서도: 연결 연산자", "PE.Controllers.Main.txtShape_flowChartDecision": "순서도: 결정", "PE.Controllers.Main.txtShape_flowChartDelay": "순서도: 지연", "PE.Controllers.Main.txtShape_flowChartDisplay": "순서도: 표시", "PE.Controllers.Main.txtShape_flowChartDocument": "순서도: 문서", "PE.Controllers.Main.txtShape_flowChartExtract": "순서도: 추출", "PE.Controllers.Main.txtShape_flowChartInputOutput": "순서도: 데이터", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "순서도: 내부 스토리지", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "순서도: 디스크", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "순서도: 스토리지에 직접 접근", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "순서도: 순차 접근 스토리지", "PE.Controllers.Main.txtShape_flowChartManualInput": "순서도: 수동 입력", "PE.Controllers.Main.txtShape_flowChartManualOperation": "순서도: 수동조작", "PE.Controllers.Main.txtShape_flowChartMerge": "순서도: 병합", "PE.Controllers.Main.txtShape_flowChartMultidocument": "순서도: 다중문서", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "순서도: 페이지 외부 커넥터", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "순서도: 저장된 데이터", "PE.Controllers.Main.txtShape_flowChartOr": "순서도: 또는", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "순서도: 미리 정의된 흐름", "PE.Controllers.Main.txtShape_flowChartPreparation": "순서도: 준비", "PE.Controllers.Main.txtShape_flowChartProcess": "순서도: 프로세스", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "순서도: 카드", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "순서도: 천공된 종이 테이프", "PE.Controllers.Main.txtShape_flowChartSort": "순서도: 정렬", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "순서도: 합계 노드", "PE.Controllers.Main.txtShape_flowChartTerminator": "순서도: 종료", "PE.Controllers.Main.txtShape_foldedCorner": "접힌 모서리", "PE.Controllers.Main.txtShape_frame": "프레임", "PE.Controllers.Main.txtShape_halfFrame": "1/2 액자", "PE.Controllers.Main.txtShape_heart": "하트모양", "PE.Controllers.Main.txtShape_heptagon": "칠각형", "PE.Controllers.Main.txtShape_hexagon": "육각형", "PE.Controllers.Main.txtShape_homePlate": "오각형", "PE.Controllers.Main.txtShape_horizontalScroll": "두루마리 모양: 가로로 말림", "PE.Controllers.Main.txtShape_irregularSeal1": "폭발: 8pt", "PE.Controllers.Main.txtShape_irregularSeal2": "폭발: 14pt", "PE.Controllers.Main.txtShape_leftArrow": "화살표: 왼쪽", "PE.Controllers.Main.txtShape_leftArrowCallout": "설명선: 왼쪽 화살표", "PE.Controllers.Main.txtShape_leftBrace": "왼쪽 중괄호", "PE.Controllers.Main.txtShape_leftBracket": "왼쪽 대괄호", "PE.Controllers.Main.txtShape_leftRightArrow": "선 화살표 : 양방향", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "설명선: 왼쪽 및 오른쪽 화살표", "PE.Controllers.Main.txtShape_leftRightUpArrow": "화살표: 왼쪽/위쪽", "PE.Controllers.Main.txtShape_leftUpArrow": "화살표: 왼쪽", "PE.Controllers.Main.txtShape_lightningBolt": "번개", "PE.Controllers.Main.txtShape_line": "선", "PE.Controllers.Main.txtShape_lineWithArrow": "화살표", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "선 화살표: 양방향", "PE.Controllers.Main.txtShape_mathDivide": "분할", "PE.Controllers.Main.txtShape_mathEqual": "등호", "PE.Controllers.Main.txtShape_mathMinus": "마이너스", "PE.Controllers.Main.txtShape_mathMultiply": "곱셈", "PE.Controllers.Main.txtShape_mathNotEqual": "부등호", "PE.Controllers.Main.txtShape_mathPlus": "덧셈", "PE.Controllers.Main.txtShape_moon": "달모양", "PE.Controllers.Main.txtShape_noSmoking": "\"없음\" 기호", "PE.Controllers.Main.txtShape_notchedRightArrow": "화살표: 오른쪽 톱니 모양", "PE.Controllers.Main.txtShape_octagon": "팔각형", "PE.Controllers.Main.txtShape_parallelogram": "평행 사변형", "PE.Controllers.Main.txtShape_pentagon": "오각형", "PE.Controllers.Main.txtShape_pie": "부분 원형", "PE.Controllers.Main.txtShape_plaque": "배지", "PE.Controllers.Main.txtShape_plus": "덧셈", "PE.Controllers.Main.txtShape_polyline1": "자유형: 자유 곡선", "PE.Controllers.Main.txtShape_polyline2": "자유형: 도형", "PE.Controllers.Main.txtShape_quadArrow": "화살표: 왼쪽/오른쪽/위쪽/아래쪽", "PE.Controllers.Main.txtShape_quadArrowCallout": "설명선: 왼쪽/오른쪽/위쪽/아래쪽", "PE.Controllers.Main.txtShape_rect": "사각형", "PE.Controllers.Main.txtShape_ribbon": "리본: 아래로 기울어짐", "PE.Controllers.Main.txtShape_ribbon2": "리본: 위로 구불어짐", "PE.Controllers.Main.txtShape_rightArrow": "화살표: 오른쪽", "PE.Controllers.Main.txtShape_rightArrowCallout": "설명선: 오른쪽 화살표", "PE.Controllers.Main.txtShape_rightBrace": "오른쪽 중괄호", "PE.Controllers.Main.txtShape_rightBracket": "오른쪽 대괄호", "PE.Controllers.Main.txtShape_round1Rect": "사각형: 둥근 한쪽 모서리", "PE.Controllers.Main.txtShape_round2DiagRect": "사각형: 둥근 대각선 방향 모서리", "PE.Controllers.Main.txtShape_round2SameRect": "사각형: 둥근 위쪽 모서리", "PE.Controllers.Main.txtShape_roundRect": "사각형: 둥근 모서리", "PE.Controllers.Main.txtShape_rtTriangle": "직각 삼각형", "PE.Controllers.Main.txtShape_smileyFace": "웃는 얼굴", "PE.Controllers.Main.txtShape_snip1Rect": "사각형: 잘린 한쪽 모서리", "PE.Controllers.Main.txtShape_snip2DiagRect": "사각형: 잘린 대각선 방향 모서리", "PE.Controllers.Main.txtShape_snip2SameRect": "사각형: 잘린 양쪽 모서리", "PE.Controllers.Main.txtShape_snipRoundRect": "사각형: 한쪽은 둥글고 한쪽은 짤린 모서리", "PE.Controllers.Main.txtShape_spline": "곡선", "PE.Controllers.Main.txtShape_star10": "별: 꼭짓점 10개", "PE.Controllers.Main.txtShape_star12": "별: 꼭짓점 12개", "PE.Controllers.Main.txtShape_star16": "별: 꼭짓점 16개", "PE.Controllers.Main.txtShape_star24": "별: 꼭짓점 24개", "PE.Controllers.Main.txtShape_star32": "별: 꼭짓점 32개", "PE.Controllers.Main.txtShape_star4": "별: 꼭짓점 4개", "PE.Controllers.Main.txtShape_star5": "별: 꼭짓점 5개", "PE.Controllers.Main.txtShape_star6": "별: 꼭짓점 6개", "PE.Controllers.Main.txtShape_star7": "별: 꼭짓점 7개", "PE.Controllers.Main.txtShape_star8": "별: 꼭짓점 8개", "PE.Controllers.Main.txtShape_stripedRightArrow": "줄무늬 오른쪽 화살표", "PE.Controllers.Main.txtShape_sun": "해모양", "PE.Controllers.Main.txtShape_teardrop": "눈물 방울", "PE.Controllers.Main.txtShape_textRect": "텍스트 상자", "PE.Controllers.Main.txtShape_trapezoid": "사다리꼴", "PE.Controllers.Main.txtShape_triangle": "삼각형", "PE.Controllers.Main.txtShape_upArrow": "화살표: 위쪽", "PE.Controllers.Main.txtShape_upArrowCallout": "설명선: 위쪽 화살표", "PE.Controllers.Main.txtShape_upDownArrow": "화살표: 위쪽/아래쪽", "PE.Controllers.Main.txtShape_uturnArrow": "화살표: U자형", "PE.Controllers.Main.txtShape_verticalScroll": "두루마리 모양: 세로로 말림", "PE.Controllers.Main.txtShape_wave": "물결", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "말풍선: 타원형", "PE.Controllers.Main.txtShape_wedgeRectCallout": "말풍선: 사각형", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "말풍선: 모서리가 둥근 사각형", "PE.Controllers.Main.txtSldLtTBlank": "공백", "PE.Controllers.Main.txtSldLtTChart": "차트", "PE.Controllers.Main.txtSldLtTChartAndTx": "차트 및 텍스트", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "클립 아트 및 텍스트", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "클립 아트 및 세로 텍스트", "PE.Controllers.Main.txtSldLtTCust": "사용자 지정", "PE.Controllers.Main.txtSldLtTDgm": "다이어그램", "PE.Controllers.Main.txtSldLtTFourObj": "네 개의 개체", "PE.Controllers.Main.txtSldLtTMediaAndTx": "미디어 및 텍스트", "PE.Controllers.Main.txtSldLtTObj": "제목 및 개체", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "개체 및 두 개체", "PE.Controllers.Main.txtSldLtTObjAndTx": "개체 및 텍스트", "PE.Controllers.Main.txtSldLtTObjOnly": "개체", "PE.Controllers.Main.txtSldLtTObjOverTx": "텍스트 위에 개체", "PE.Controllers.Main.txtSldLtTObjTx": "제목, 개체 및 캡션", "PE.Controllers.Main.txtSldLtTPicTx": "그림 및 캡션", "PE.Controllers.Main.txtSldLtTSecHead": "구역 헤더", "PE.Controllers.Main.txtSldLtTTbl": "테이블", "PE.Controllers.Main.txtSldLtTTitle": "제목", "PE.Controllers.Main.txtSldLtTTitleOnly": "Title Only", "PE.Controllers.Main.txtSldLtTTwoColTx": "두 개의 열 텍스트", "PE.Controllers.Main.txtSldLtTTwoObj": "두 개체", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "두 개체 및 개체", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "두 개체 및 텍스트", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "텍스트 위에 두 개체", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "두 텍스트 및 두 개체", "PE.Controllers.Main.txtSldLtTTx": "텍스트", "PE.Controllers.Main.txtSldLtTTxAndChart": "텍스트 및 차트", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "텍스트 및 클립 아트", "PE.Controllers.Main.txtSldLtTTxAndMedia": "텍스트 및 미디어", "PE.Controllers.Main.txtSldLtTTxAndObj": "텍스트 및 개체", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "텍스트 및 두 개의 개체", "PE.Controllers.Main.txtSldLtTTxOverObj": "텍스트 위에 개체", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "세로 제목 및 텍스트", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "차트의 세로 제목 및 텍스트", "PE.Controllers.Main.txtSldLtTVertTx": "세로 텍스트", "PE.Controllers.Main.txtSlideNumber": "슬라이드 번호", "PE.Controllers.Main.txtSlideSubtitle": "슬라이드 부제목", "PE.Controllers.Main.txtSlideText": "슬라이드 텍스트", "PE.Controllers.Main.txtSlideTitle": "슬라이드 제목", "PE.Controllers.Main.txtStarsRibbons": "별 및 현수막", "PE.Controllers.Main.txtTheme_basic": "심플 테마", "PE.Controllers.Main.txtTheme_blank": "일반", "PE.Controllers.Main.txtTheme_classic": "클래식 테마", "PE.Controllers.Main.txtTheme_corner": "L형 테마", "PE.Controllers.Main.txtTheme_dotted": "점형 테마", "PE.Controllers.Main.txtTheme_green": "녹색", "PE.Controllers.Main.txtTheme_green_leaf": "녹차 테마", "PE.Controllers.Main.txtTheme_lines": "선형 테마", "PE.Controllers.Main.txtTheme_office": "사무실 테마", "PE.Controllers.Main.txtTheme_office_theme": "Office 테마", "PE.Controllers.Main.txtTheme_official": "업무적 테마", "PE.Controllers.Main.txtTheme_pixel": "픽셀", "PE.Controllers.Main.txtTheme_safari": "사파리 테마", "PE.Controllers.Main.txtTheme_turtle": "거북이 테마", "PE.Controllers.Main.txtXAxis": "X 축", "PE.Controllers.Main.txtYAxis": "Y 축", "PE.Controllers.Main.unknownErrorText": "알 수없는 오류.", "PE.Controllers.Main.unsupportedBrowserErrorText": "사용중인 브라우저가 지원되지 않습니다.", "PE.Controllers.Main.uploadImageExtMessage": "알 수없는 이미지 형식입니다.", "PE.Controllers.Main.uploadImageFileCountMessage": "이미지가 업로드되지 않았습니다.", "PE.Controllers.Main.uploadImageSizeMessage": "이미지 크기 제한을 초과했습니다.", "PE.Controllers.Main.uploadImageTextText": "이미지 업로드 중 ...", "PE.Controllers.Main.uploadImageTitleText": "이미지 업로드 중", "PE.Controllers.Main.waitText": "잠시만 기다려주세요...", "PE.Controllers.Main.warnBrowserIE9": "응용 프로그램의 기능이 IE9에서 부족합니다. IE10 이상을 사용하십시오.", "PE.Controllers.Main.warnBrowserZoom": "브라우저의 현재 확대/축소 설정이 완전히 지원되지 않습니다. Ctrl + 0을 눌러 기본 확대 / 축소로 재설정하십시오.", "PE.Controllers.Main.warnLicenseExceeded": "귀하의 시스템은 동시에 연결을 편집하는 %1명의 편집자에게 도달했습니다. 이 문서는 보기 모드에서만 열 수 있습니다. <br> 자세한 내용은 관리자에게 문의하십시오.", "PE.Controllers.Main.warnLicenseExp": "귀하의 라이센스가 만료되었습니다. <br> 라이센스를 업데이트하고 페이지를 새로 고침하십시오.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "라이센스가 만료되었습니다.<br>더 이상 파일을 수정할 수 있는 권한이 없습니다.<br> 관리자에게 문의하세요.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "라이센스를 갱신해야합니다. <br> 문서 편집 기능에 대한 액세스가 제한되어 있습니다. <br> 전체 액세스 권한을 얻으려면 관리자에게 문의하십시오", "PE.Controllers.Main.warnLicenseUsersExceeded": "편집자 사용자 한도인 %1명에 도달했습니다. 자세한 내용은 관리자에게 문의하십시오.", "PE.Controllers.Main.warnNoLicense": "이 버전의 %1 편집자는 문서 서버에 대한 동시 연결에 특정 제한 사항이 있습니다. <br> 더 많은 정보가 필요하면 현재 라이센스를 업그레이드하거나 상업용 라이센스를 구입하십시오.", "PE.Controllers.Main.warnNoLicenseUsers": "편집자 사용자 한도인 %1명에 도달했습니다. 개인 업그레이드 조건은 %1 영업 팀에 문의하십시오.", "PE.Controllers.Main.warnProcessRightsChange": "파일 편집 권한이 거부되었습니다.", "PE.Controllers.Statusbar.textDisconnect": "<b>연결이 끊어졌습니다</b><br>연결을 시도하는 중입니다.", "PE.Controllers.Statusbar.zoomText": "확대/축소 {0} %", "PE.Controllers.Toolbar.confirmAddFontName": "저장하려는 글꼴을 현재 장치에서 사용할 수 없습니다. <br> 시스템 글꼴 중 하나를 사용하여 텍스트 스타일을 표시하고 저장된 글꼴을 사용하면 글꼴이 사용됩니다 사용할 수 있습니다. <br> 계속 하시겠습니까? ", "PE.Controllers.Toolbar.textAccent": "Accents", "PE.Controllers.Toolbar.textBracket": "대괄호", "PE.Controllers.Toolbar.textEmptyImgUrl": "이미지 URL을 지정해야합니다.", "PE.Controllers.Toolbar.textFontSizeErr": "입력 한 값이 잘못되었습니다. <br> 1에서 300 사이의 숫자 값을 입력하십시오.", "PE.Controllers.Toolbar.textFraction": "Fractions", "PE.Controllers.Toolbar.textFunction": "Functions", "PE.Controllers.Toolbar.textInsert": "삽입", "PE.Controllers.Toolbar.textIntegral": "Integrals", "PE.Controllers.Toolbar.textLargeOperator": "Large Operators", "PE.Controllers.Toolbar.textLimitAndLog": "한계 및 로그 수", "PE.Controllers.Toolbar.textMatrix": "Matrices", "PE.Controllers.Toolbar.textOperator": "연산자", "PE.Controllers.Toolbar.textRadical": "Radicals", "PE.Controllers.Toolbar.textScript": "스크립트", "PE.Controllers.Toolbar.textSymbols": "심볼", "PE.Controllers.Toolbar.textWarning": "경고", "PE.Controllers.Toolbar.txtAccent_Accent": "급성", "PE.Controllers.Toolbar.txtAccent_ArrowD": "오른쪽 위 왼쪽 화살표", "PE.Controllers.Toolbar.txtAccent_ArrowL": "왼쪽 위 화살표", "PE.Controllers.Toolbar.txtAccent_ArrowR": "오른쪽 위 화살표 위", "PE.Controllers.Toolbar.txtAccent_Bar": "Bar", "PE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "PE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "PE.Controllers.Toolbar.txtAccent_BorderBox": "상자가있는 수식 (자리 표시 자 포함)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "상자화 된 수식 (예)", "PE.Controllers.Toolbar.txtAccent_Check": "확인", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "아래쪽 중괄호", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "위쪽 중괄호", "PE.Controllers.Toolbar.txtAccent_Custom_1": "벡터 A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC with Overbar", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Overbar", "PE.Controllers.Toolbar.txtAccent_DDDot": "트리플 도트", "PE.Controllers.Toolbar.txtAccent_DDot": "Double Dot", "PE.Controllers.Toolbar.txtAccent_Dot": "Dot", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Double Overbar", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "아래의 문자 그룹화", "PE.Controllers.Toolbar.txtAccent_GroupTop": "문자 위에 그룹화", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards Harpoon Above", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards Harpoon Above", "PE.Controllers.Toolbar.txtAccent_Hat": "Hat", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "물결표", "PE.Controllers.Toolbar.txtBracket_Angle": "대괄호", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "구분 기호가있는 대괄호", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "구분 기호가있는 대괄호", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Curve": "대괄호", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "구분 기호가있는 대괄호", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Custom_1": "사례 (두 조건)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "사례 (세 조건)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Stack Object", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Stack Object", "PE.Controllers.Toolbar.txtBracket_Custom_5": "사례 사례", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial Coefficient", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial Coefficient", "PE.Controllers.Toolbar.txtBracket_Line": "대괄호", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_LineDouble": "대괄호", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_LowLim": "대괄호", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "단일 브래킷", "PE.Controllers.Toolbar.txtBracket_Round": "대괄호", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "구분 기호가있는 대괄호", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Square": "대괄호", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "대괄호", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "대괄호", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "대괄호", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "대괄호", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_UppLim": "대괄호", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "단일 대괄호", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "단일 대괄호", "PE.Controllers.Toolbar.txtFractionDiagonal": "비뚤어진 부분", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Differential", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Differential", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Differential", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Differential", "PE.Controllers.Toolbar.txtFractionHorizontal": "선형 분수", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi Over 2", "PE.Controllers.Toolbar.txtFractionSmall": "Small Fraction", "PE.Controllers.Toolbar.txtFractionVertical": "Stacked Fraction", "PE.Controllers.Toolbar.txtFunction_1_Cos": "역 코사인 함수", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "쌍곡선 역 코사인 함수", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Inverse Cotangent Function", "PE.Controllers.Toolbar.txtFunction_1_Coth": "쌍곡선 역 코탄젠트 함수", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse Cosecant Function", "PE.Controllers.Toolbar.txtFunction_1_Csch": "쌍곡선 반전 보조 함수", "PE.Controllers.Toolbar.txtFunction_1_Sec": "역 분개 함수", "PE.Controllers.Toolbar.txtFunction_1_Sech": "쌍곡선 반전 시컨트 함수", "PE.Controllers.Toolbar.txtFunction_1_Sin": "역 사인 함수", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "쌍곡선 역 사인 함수", "PE.Controllers.Toolbar.txtFunction_1_Tan": "역 탄젠트 함수", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "쌍곡선 역 탄젠트 함수", "PE.Controllers.Toolbar.txtFunction_Cos": "코사인 함수", "PE.Controllers.Toolbar.txtFunction_Cosh": "쌍곡선 코사인 함수", "PE.Controllers.Toolbar.txtFunction_Cot": "코탄 센트 함수", "PE.Controllers.Toolbar.txtFunction_Coth": "쌍곡선 코탄 센트 함수", "PE.Controllers.Toolbar.txtFunction_Csc": "Cosecant 함수", "PE.Controllers.Toolbar.txtFunction_Csch": "쌍곡선 보조 함수", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "PE.Controllers.Toolbar.txtFunction_Sec": "Secant 함수", "PE.Controllers.Toolbar.txtFunction_Sech": "쌍곡선 시컨트 함수", "PE.Controllers.Toolbar.txtFunction_Sin": "사인 함수", "PE.Controllers.Toolbar.txtFunction_Sinh": "쌍곡선 사인 함수", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangent Function", "PE.Controllers.Toolbar.txtFunction_Tanh": "쌍곡선 탄젠트 함수", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "PE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "PE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralDouble": "Double Integral", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Double Integral", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "이중 정수", "PE.Controllers.Toolbar.txtIntegralOriented": "윤곽선 적분", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "윤곽선 적분", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "표면 적분", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "표면 적분", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "표면 적분", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "윤곽선 적분", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "볼륨 정수", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "볼륨 정수", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "볼륨 정수", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralTriple": "Triple Integral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple Integral", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple Integral", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-Product", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-Product", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-Product", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-Product", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-Product", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "교차점", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "교차점", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "교차점", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "교차점", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "교차점", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit Example", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "최대 예", "PE.Controllers.Toolbar.txtLimitLog_Lim": "제한", "PE.Controllers.Toolbar.txtLimitLog_Ln": "자연 로그", "PE.Controllers.Toolbar.txtLimitLog_Log": "로그", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "로그", "PE.Controllers.Toolbar.txtLimitLog_Max": "최대", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Empty Matrix", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Empty Matrix", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "대괄호가있는 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "괄호가있는 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "괄호가있는 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "괄호가있는 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 빈 행렬", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Empty Matrix", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "기준점", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline Dots", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "대각선 점", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "수직 점", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "스파 스 매트릭스", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "스파 스 매트릭스", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identity Matrix", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Identity Matrix", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identity Matrix", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Identity Matrix", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "오른쪽 아래 화살표", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "오른쪽 위 왼쪽 화살표", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "왼쪽 아래쪽 화살표", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "왼쪽 위 화살표", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "오른쪽 아래 화살표", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "오른쪽 위 화살표 위", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "콜론 균등", "PE.Controllers.Toolbar.txtOperator_Custom_1": "수익률", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta Yields", "PE.Controllers.Toolbar.txtOperator_Definition": "정의에 의해 동일", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Equal To", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "오른쪽 아래 화살표", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "오른쪽 위 왼쪽 화살표 위", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "왼쪽 아래쪽 화살표", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "왼쪽 위 화살표", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "아래 오른쪽 화살표", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "오른쪽 위 화살표 위", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal Equal", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Equal", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "측정 기준", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "PE.Controllers.Toolbar.txtRadicalRoot_2": "학위가있는 제곱근", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Cubic Root", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Degree와 함께 급진적", "PE.Controllers.Toolbar.txtRadicalSqrt": "Square Root", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "아래 첨자", "PE.Controllers.Toolbar.txtScriptSubSup": "Subscript-Superscript", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "LeftSubscript-Superscript", "PE.Controllers.Toolbar.txtScriptSup": "Superscript", "PE.Controllers.Toolbar.txtSymbol_about": "대략", "PE.Controllers.Toolbar.txtSymbol_additional": "Complement", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "PE.Controllers.Toolbar.txtSymbol_approx": "거의 동일", "PE.Controllers.Toolbar.txtSymbol_ast": "별표 연산자", "PE.Controllers.Toolbar.txtSymbol_beta": "베타", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "글 머리 기호 연산자", "PE.Controllers.Toolbar.txtSymbol_cap": "교차점", "PE.Controllers.Toolbar.txtSymbol_cbrt": "큐브 루트", "PE.Controllers.Toolbar.txtSymbol_cdots": "중간 말줄임표", "PE.Controllers.Toolbar.txtSymbol_celsius": "섭씨도", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "대략 같음", "PE.Controllers.Toolbar.txtSymbol_cup": "Union", "PE.Controllers.Toolbar.txtSymbol_ddots": "오른쪽 아래 대각선 줄임표", "PE.Controllers.Toolbar.txtSymbol_degree": "도", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Division Sign", "PE.Controllers.Toolbar.txtSymbol_downarrow": "화살표: 아래쪽", "PE.Controllers.Toolbar.txtSymbol_emptyset": "빈 세트", "PE.Controllers.Toolbar.txtSymbol_epsilon": "엡실론", "PE.Controllers.Toolbar.txtSymbol_equals": "Equal", "PE.Controllers.Toolbar.txtSymbol_equiv": "동일 함", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "존재 함", "PE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "화씨", "PE.Controllers.Toolbar.txtSymbol_forall": "모두에게", "PE.Controllers.Toolbar.txtSymbol_gamma": "감마", "PE.Controllers.Toolbar.txtSymbol_geq": "크거나 같음", "PE.Controllers.Toolbar.txtSymbol_gg": "훨씬 더 큼", "PE.Controllers.Toolbar.txtSymbol_greater": "Greater Than", "PE.Controllers.Toolbar.txtSymbol_in": "요소 중", "PE.Controllers.Toolbar.txtSymbol_inc": "증가", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infinity", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "화살표: 왼쪽", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "왼쪽 / 오른쪽 화살표", "PE.Controllers.Toolbar.txtSymbol_leq": "보다 작거나 같음", "PE.Controllers.Toolbar.txtSymbol_less": "Less Than", "PE.Controllers.Toolbar.txtSymbol_ll": "훨씬 적습니다", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "같지 않음", "PE.Controllers.Toolbar.txtSymbol_ni": "회원으로 포함", "PE.Controllers.Toolbar.txtSymbol_not": "Not Sign", "PE.Controllers.Toolbar.txtSymbol_notexists": "존재하지 않습니다", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "부분 미분", "PE.Controllers.Toolbar.txtSymbol_percent": "백분율", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Proportional To", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "네 번째 루트", "PE.Controllers.Toolbar.txtSymbol_qed": "End of Proof", "PE.Controllers.Toolbar.txtSymbol_rddots": "오른쪽 위 대각선 줄임표", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "화살표: 오른쪽", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Radical Sign", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "그러므로", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "곱셈 기호", "PE.Controllers.Toolbar.txtSymbol_uparrow": "화살표: 위쪽", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon Variant", "PE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varsigma": "시그마 변형", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vdots": "세로 줄임표", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "슬라이드에 맞추기", "PE.Controllers.Viewport.textFitWidth": "너비에 맞춤", "PE.Views.Animation.str0_5": "0.5초(매우 빠름)", "PE.Views.Animation.str1": "1초(빠름)", "PE.Views.Animation.str2": "2초(보통)", "PE.Views.Animation.str20": "20초(극도로 느림)", "PE.Views.Animation.str3": "3초(느림)", "PE.Views.Animation.str5": "5초(매우 느림)", "PE.Views.Animation.strDelay": "지연", "PE.Views.Animation.strDuration": "재생 시간", "PE.Views.Animation.strRepeat": "반복", "PE.Views.Animation.strRewind": "되감기", "PE.Views.Animation.strStart": "시작", "PE.Views.Animation.strTrigger": "트리거", "PE.Views.Animation.textAutoPreview": "자동 미리보기", "PE.Views.Animation.textMoreEffects": "더 많은 효과 표시", "PE.Views.Animation.textMoveEarlier": "더 일찍 이동", "PE.Views.Animation.textMoveLater": "나중에 이동", "PE.Views.Animation.textMultiple": "배수", "PE.Views.Animation.textNone": "없음", "PE.Views.Animation.textNoRepeat": "(없음)", "PE.Views.Animation.textOnClickOf": "클릭 시", "PE.Views.Animation.textOnClickSequence": "시퀀스 클릭 시", "PE.Views.Animation.textStartAfterPrevious": "이전 이후", "PE.Views.Animation.textStartOnClick": "클릭 시", "PE.Views.Animation.textStartWithPrevious": "이전과 함께", "PE.Views.Animation.textUntilEndOfSlide": "슬라이드 끝까지", "PE.Views.Animation.textUntilNextClick": "다음 클릭까지", "PE.Views.Animation.txtAddEffect": "애니메이션 추가", "PE.Views.Animation.txtAnimationPane": "애니메이션 팬", "PE.Views.Animation.txtParameters": "매개 변수", "PE.Views.Animation.txtPreview": "미리보기", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "효과 미리보기", "PE.Views.AnimationDialog.textTitle": "효과 더 보기", "PE.Views.ChartSettings.textAdvanced": "고급 설정 표시", "PE.Views.ChartSettings.textChartType": "차트 유형 변경", "PE.Views.ChartSettings.textEditData": "데이터 편집", "PE.Views.ChartSettings.textHeight": "높이", "PE.Views.ChartSettings.textKeepRatio": "일정 비율", "PE.Views.ChartSettings.textSize": "크기", "PE.Views.ChartSettings.textStyle": "스타일", "PE.Views.ChartSettings.textWidth": "너비", "PE.Views.ChartSettingsAdvanced.textAlt": "대체 텍스트", "PE.Views.ChartSettingsAdvanced.textAltDescription": "설명", "PE.Views.ChartSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "제목", "PE.Views.ChartSettingsAdvanced.textTitle": "차트 - 고급 설정", "PE.Views.DateTimeDialog.confirmDefault": "{0} 기본 형식을 설정 : \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "기본값으로 설정", "PE.Views.DateTimeDialog.textFormat": "형식", "PE.Views.DateTimeDialog.textLang": "언어", "PE.Views.DateTimeDialog.textUpdate": "자동 업데이트", "PE.Views.DateTimeDialog.txtTitle": "날짜 및 시간", "PE.Views.DocumentHolder.aboveText": "위", "PE.Views.DocumentHolder.addCommentText": "주석 추가", "PE.Views.DocumentHolder.addToLayoutText": "레이아웃추가", "PE.Views.DocumentHolder.advancedImageText": "이미지 고급 설정", "PE.Views.DocumentHolder.advancedParagraphText": "단락 고급 설정", "PE.Views.DocumentHolder.advancedShapeText": "모양 고급 설정", "PE.Views.DocumentHolder.advancedTableText": "표 고급 설정", "PE.Views.DocumentHolder.alignmentText": "정렬", "PE.Views.DocumentHolder.belowText": "아래", "PE.Views.DocumentHolder.cellAlignText": "셀 세로 맞춤", "PE.Views.DocumentHolder.cellText": "셀", "PE.Views.DocumentHolder.centerText": "Center", "PE.Views.DocumentHolder.columnText": "Column", "PE.Views.DocumentHolder.deleteColumnText": "열 삭제", "PE.Views.DocumentHolder.deleteRowText": "Delete Row", "PE.Views.DocumentHolder.deleteTableText": "테이블 삭제", "PE.Views.DocumentHolder.deleteText": "Delete", "PE.Views.DocumentHolder.direct270Text": "텍스트 회전", "PE.Views.DocumentHolder.direct90Text": "텍스트 아래로 회전", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "텍스트 방향", "PE.Views.DocumentHolder.editChartText": "데이터 편집", "PE.Views.DocumentHolder.editHyperlinkText": "하이퍼 링크 편집", "PE.Views.DocumentHolder.hyperlinkText": "하이퍼 링크", "PE.Views.DocumentHolder.ignoreAllSpellText": "모두 무시", "PE.Views.DocumentHolder.ignoreSpellText": "무시", "PE.Views.DocumentHolder.insertColumnLeftText": "왼쪽 열", "PE.Views.DocumentHolder.insertColumnRightText": "오른쪽 열", "PE.Views.DocumentHolder.insertColumnText": "열 삽입", "PE.Views.DocumentHolder.insertRowAboveText": "위의 행", "PE.Views.DocumentHolder.insertRowBelowText": "아래 행", "PE.Views.DocumentHolder.insertRowText": "행 삽입", "PE.Views.DocumentHolder.insertText": "삽입", "PE.Views.DocumentHolder.langText": "언어 선택", "PE.Views.DocumentHolder.leftText": "왼쪽", "PE.Views.DocumentHolder.loadSpellText": "로드 변형 ...", "PE.Views.DocumentHolder.mergeCellsText": "셀 병합", "PE.Views.DocumentHolder.mniCustomTable": "사용자 정의 테이블 삽입", "PE.Views.DocumentHolder.moreText": "다양한 변형 ...", "PE.Views.DocumentHolder.noSpellVariantsText": "변형 없음", "PE.Views.DocumentHolder.originalSizeText": "실제 크기", "PE.Views.DocumentHolder.removeHyperlinkText": "하이퍼 링크 제거", "PE.Views.DocumentHolder.rightText": "오른쪽", "PE.Views.DocumentHolder.rowText": "Row", "PE.Views.DocumentHolder.selectText": "선택", "PE.Views.DocumentHolder.spellcheckText": "맞춤법 검사", "PE.Views.DocumentHolder.splitCellsText": "셀 분할 ...", "PE.Views.DocumentHolder.splitCellTitleText": "셀 분할", "PE.Views.DocumentHolder.tableText": "테이블", "PE.Views.DocumentHolder.textArrangeBack": "맨 뒤로 보내기", "PE.Views.DocumentHolder.textArrangeBackward": "뒤로 이동", "PE.Views.DocumentHolder.textArrangeForward": "앞으로 이동", "PE.Views.DocumentHolder.textArrangeFront": "전경으로 가져 오기", "PE.Views.DocumentHolder.textCopy": "복사", "PE.Views.DocumentHolder.textCrop": "자르기", "PE.Views.DocumentHolder.textCropFill": "채우기", "PE.Views.DocumentHolder.textCropFit": "맞춤", "PE.Views.DocumentHolder.textCut": "잘라 내기", "PE.Views.DocumentHolder.textDistributeCols": "컬럼 배포", "PE.Views.DocumentHolder.textDistributeRows": "행 배포", "PE.Views.DocumentHolder.textEditPoints": "꼭지점 수정", "PE.Views.DocumentHolder.textFlipH": "좌우대칭", "PE.Views.DocumentHolder.textFlipV": "상하대칭", "PE.Views.DocumentHolder.textFromFile": "파일로부터", "PE.Views.DocumentHolder.textFromStorage": "스토리지로 부터", "PE.Views.DocumentHolder.textFromUrl": "URL로부터", "PE.Views.DocumentHolder.textNextPage": "다음 슬라이드", "PE.Views.DocumentHolder.textPaste": "붙여 넣기", "PE.Views.DocumentHolder.textPrevPage": "이전 슬라이드", "PE.Views.DocumentHolder.textReplace": "이미지 바꾸기", "PE.Views.DocumentHolder.textRotate": "회전", "PE.Views.DocumentHolder.textRotate270": "왼쪽으로 90도 회전", "PE.Views.DocumentHolder.textRotate90": "오른쪽으로 90도 회전", "PE.Views.DocumentHolder.textShapeAlignBottom": "아래쪽 정렬", "PE.Views.DocumentHolder.textShapeAlignCenter": "가운데 맞춤", "PE.Views.DocumentHolder.textShapeAlignLeft": "왼쪽 정렬", "PE.Views.DocumentHolder.textShapeAlignMiddle": "가운데 정렬", "PE.Views.DocumentHolder.textShapeAlignRight": "오른쪽 정렬", "PE.Views.DocumentHolder.textShapeAlignTop": "정렬 위쪽", "PE.Views.DocumentHolder.textSlideSettings": "슬라이드 설정", "PE.Views.DocumentHolder.textUndo": "실행 취소", "PE.Views.DocumentHolder.tipIsLocked": "이 요소는 현재 다른 사용자가 편집하고 있습니다.", "PE.Views.DocumentHolder.toDictionaryText": "사용자 정의 사전에 추가", "PE.Views.DocumentHolder.txtAddBottom": "아래쪽 테두리 추가", "PE.Views.DocumentHolder.txtAddFractionBar": "분수 막대 추가", "PE.Views.DocumentHolder.txtAddHor": "가로선 추가", "PE.Views.DocumentHolder.txtAddLB": "왼쪽 하단 추가", "PE.Views.DocumentHolder.txtAddLeft": "왼쪽 테두리 추가", "PE.Views.DocumentHolder.txtAddLT": "왼쪽 상단 줄 추가", "PE.Views.DocumentHolder.txtAddRight": "오른쪽 테두리 추가", "PE.Views.DocumentHolder.txtAddTop": "위쪽 테두리 추가", "PE.Views.DocumentHolder.txtAddVer": "세로선 추가", "PE.Views.DocumentHolder.txtAlign": "정렬", "PE.Views.DocumentHolder.txtAlignToChar": "문자에 정렬", "PE.Views.DocumentHolder.txtArrange": "정렬", "PE.Views.DocumentHolder.txtBackground": "배경", "PE.Views.DocumentHolder.txtBorderProps": "테두리 속성", "PE.Views.DocumentHolder.txtBottom": "Bottom", "PE.Views.DocumentHolder.txtChangeLayout": "레이아웃 변경", "PE.Views.DocumentHolder.txtChangeTheme": "테마 변경", "PE.Views.DocumentHolder.txtColumnAlign": "열 정렬", "PE.Views.DocumentHolder.txtDecreaseArg": "인수 크기 감소", "PE.Views.DocumentHolder.txtDeleteArg": "인수 삭제", "PE.Views.DocumentHolder.txtDeleteBreak": "수동 브레이크 삭제", "PE.Views.DocumentHolder.txtDeleteChars": "둘러싸인 문자 삭제", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "둘러싸는 문자 및 구분 기호 삭제", "PE.Views.DocumentHolder.txtDeleteEq": "방정식 삭제", "PE.Views.DocumentHolder.txtDeleteGroupChar": "문자 삭제", "PE.Views.DocumentHolder.txtDeleteRadical": "급진파 삭제", "PE.Views.DocumentHolder.txtDeleteSlide": "슬라이드 삭제", "PE.Views.DocumentHolder.txtDistribHor": "가로로 분포", "PE.Views.DocumentHolder.txtDistribVert": "수직 배분", "PE.Views.DocumentHolder.txtDuplicateSlide": "중복 슬라이드", "PE.Views.DocumentHolder.txtFractionLinear": "선형 분수로 변경", "PE.Views.DocumentHolder.txtFractionSkewed": "기울어 진 분수로 변경", "PE.Views.DocumentHolder.txtFractionStacked": "누적 분율로 변경", "PE.Views.DocumentHolder.txtGroup": "그룹", "PE.Views.DocumentHolder.txtGroupCharOver": "텍스트를 덮은 문자", "PE.Views.DocumentHolder.txtGroupCharUnder": "텍스트 아래의 문자", "PE.Views.DocumentHolder.txtHideBottom": "아래쪽 경계선 숨기기", "PE.Views.DocumentHolder.txtHideBottomLimit": "하단 제한 숨기기", "PE.Views.DocumentHolder.txtHideCloseBracket": "닫는 대괄호 숨기기", "PE.Views.DocumentHolder.txtHideDegree": "학위 숨기기", "PE.Views.DocumentHolder.txtHideHor": "가로 선 숨기기", "PE.Views.DocumentHolder.txtHideLB": "왼쪽 하단 줄 숨기기", "PE.Views.DocumentHolder.txtHideLeft": "왼쪽 테두리 숨기기", "PE.Views.DocumentHolder.txtHideLT": "왼쪽 상단 줄 숨기기", "PE.Views.DocumentHolder.txtHideOpenBracket": "여는 대괄호 숨기기", "PE.Views.DocumentHolder.txtHidePlaceholder": "자리 표시 자 숨기기", "PE.Views.DocumentHolder.txtHideRight": "오른쪽 테두리 숨기기", "PE.Views.DocumentHolder.txtHideTop": "위쪽 테두리 숨기기", "PE.Views.DocumentHolder.txtHideTopLimit": "상한 숨기기", "PE.Views.DocumentHolder.txtHideVer": "수직선 숨기기", "PE.Views.DocumentHolder.txtIncreaseArg": "인수 크기 늘리기", "PE.Views.DocumentHolder.txtInsertArgAfter": "뒤에 인수를 삽입하십시오.", "PE.Views.DocumentHolder.txtInsertArgBefore": "이전에 인수를 삽입하십시오", "PE.Views.DocumentHolder.txtInsertBreak": "수동 중단 삽입", "PE.Views.DocumentHolder.txtInsertEqAfter": "방정식 삽입하기", "PE.Views.DocumentHolder.txtInsertEqBefore": "이전에 수식 삽입", "PE.Views.DocumentHolder.txtKeepTextOnly": "텍스트 만 유지", "PE.Views.DocumentHolder.txtLimitChange": "제한 위치 변경", "PE.Views.DocumentHolder.txtLimitOver": "텍스트 제한", "PE.Views.DocumentHolder.txtLimitUnder": "텍스트 아래에서 제한", "PE.Views.DocumentHolder.txtMatchBrackets": "인수 높이에 대괄호 일치", "PE.Views.DocumentHolder.txtMatrixAlign": "매트릭스 정렬", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "끝으로 슬라이드 이동", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "처음으로 슬라이드 이동", "PE.Views.DocumentHolder.txtNewSlide": "새 슬라이드", "PE.Views.DocumentHolder.txtOverbar": "텍스트 위에 가로 막기", "PE.Views.DocumentHolder.txtPasteDestFormat": "목적 테마를 사용하기", "PE.Views.DocumentHolder.txtPastePicture": "그림", "PE.Views.DocumentHolder.txtPasteSourceFormat": "소스 포맷을 유지하세요", "PE.Views.DocumentHolder.txtPressLink": "{0} 키를 누르고 링크를 클릭합니다.", "PE.Views.DocumentHolder.txtPreview": "슬라이드 쇼 시작", "PE.Views.DocumentHolder.txtPrintSelection": "선택 항목 인쇄", "PE.Views.DocumentHolder.txtRemFractionBar": "분수 막대 제거", "PE.Views.DocumentHolder.txtRemLimit": "제한 제거", "PE.Views.DocumentHolder.txtRemoveAccentChar": "액센트 문자 제거", "PE.Views.DocumentHolder.txtRemoveBar": "막대 제거", "PE.Views.DocumentHolder.txtRemScripts": "스크립트 제거", "PE.Views.DocumentHolder.txtRemSubscript": "아래 첨자 제거", "PE.Views.DocumentHolder.txtRemSuperscript": "위 첨자 제거", "PE.Views.DocumentHolder.txtResetLayout": "슬라이드 재설정", "PE.Views.DocumentHolder.txtScriptsAfter": "텍스트 뒤의 스크립트", "PE.Views.DocumentHolder.txtScriptsBefore": "텍스트 앞의 스크립트", "PE.Views.DocumentHolder.txtSelectAll": "모두 선택", "PE.Views.DocumentHolder.txtShowBottomLimit": "하단 제한 표시", "PE.Views.DocumentHolder.txtShowCloseBracket": "닫는 괄호 표시", "PE.Views.DocumentHolder.txtShowDegree": "학위 표시", "PE.Views.DocumentHolder.txtShowOpenBracket": "여는 대괄호 표시", "PE.Views.DocumentHolder.txtShowPlaceholder": "자리 표시 자 표시", "PE.Views.DocumentHolder.txtShowTopLimit": "상한 표시", "PE.Views.DocumentHolder.txtSlide": "슬라이드", "PE.Views.DocumentHolder.txtSlideHide": "슬라이드 감추기", "PE.Views.DocumentHolder.txtStretchBrackets": "스트레치 괄호", "PE.Views.DocumentHolder.txtTop": "Top", "PE.Views.DocumentHolder.txtUnderbar": "텍스트 아래에 바", "PE.Views.DocumentHolder.txtUngroup": "그룹 해제", "PE.Views.DocumentHolder.txtWarnUrl": "이 링크는 장치와 데이터에 손상을 줄 수 있습니다. <br> 계속하시겠습니까?", "PE.Views.DocumentHolder.vertAlignText": "세로 맞춤", "PE.Views.DocumentPreview.goToSlideText": "슬라이드로 이동", "PE.Views.DocumentPreview.slideIndexText": "{1} 중 {0} 슬라이드", "PE.Views.DocumentPreview.txtClose": "슬라이드 쇼 닫기", "PE.Views.DocumentPreview.txtEndSlideshow": "슬라이드쇼 끝", "PE.Views.DocumentPreview.txtExitFullScreen": "전체 화면 나가기", "PE.Views.DocumentPreview.txtFinalMessage": "슬라이드 미리보기의 끝입니다. 끝내려면 클릭하십시오.", "PE.Views.DocumentPreview.txtFullScreen": "전체 화면", "PE.Views.DocumentPreview.txtNext": "다음 슬라이드", "PE.Views.DocumentPreview.txtPageNumInvalid": "잘못된 슬라이드 번호", "PE.Views.DocumentPreview.txtPause": "프레젠테이션 일시 중지", "PE.Views.DocumentPreview.txtPlay": "프레젠테이션 시작", "PE.Views.DocumentPreview.txtPrev": "이전 슬라이드", "PE.Views.DocumentPreview.txtReset": "재설정", "PE.Views.FileMenu.btnAboutCaption": "정보", "PE.Views.FileMenu.btnBackCaption": "파일 위치 열기", "PE.Views.FileMenu.btnCloseMenuCaption": "메뉴 닫기", "PE.Views.FileMenu.btnCreateNewCaption": "새로 만들기", "PE.Views.FileMenu.btnDownloadCaption": "다운로드 방법", "PE.Views.FileMenu.btnExitCaption": "완료", "PE.Views.FileMenu.btnFileOpenCaption": "열기", "PE.Views.FileMenu.btnHelpCaption": "Help", "PE.Views.FileMenu.btnHistoryCaption": "버전 기록", "PE.Views.FileMenu.btnInfoCaption": "프레젠테이션 정보", "PE.Views.FileMenu.btnPrintCaption": "인쇄", "PE.Views.FileMenu.btnProtectCaption": "보호", "PE.Views.FileMenu.btnRecentFilesCaption": "최근 열기", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnReturnCaption": "프레젠테이션으로 돌아 가기", "PE.Views.FileMenu.btnRightsCaption": "액세스 권한", "PE.Views.FileMenu.btnSaveAsCaption": "다른 이름으로 저장", "PE.Views.FileMenu.btnSaveCaption": "저장", "PE.Views.FileMenu.btnSaveCopyAsCaption": "다른 이름으로 저장", "PE.Views.FileMenu.btnSettingsCaption": "고급 설정", "PE.Views.FileMenu.btnToEditCaption": "프리젠 테이션 편집", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "새 프리젠테이션", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "새로 만들기", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "적용", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "작성자추가", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "텍스트추가", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "어플리케이션", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "작성자", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "액세스 권한 변경", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "코멘트", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "생성되었습니다", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "최종 편집자", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "최종 편집", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "소유자", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "위치", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "권한이있는 사람", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "제목", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "프레젠테이션 제목", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "업로드 되었습니다", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "액세스 권한 변경", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "권한이있는 사람", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "경고", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "비밀번호로", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "프리젠테이션 보호", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "서명으로", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "프리젠 테이션 편집", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "수정하면 프레젠테이션의 서명이 삭제됩니다. <br>계속하시겠습니까?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "이 프리젠테이션은 비밀번호로 보호되었슴.", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "유효 서명자가 프리젠테이션에 추가되었슴. 이 프리젠테이션은 편집할 수 없도록 보호됨.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "프리젠테이션내 몇가지 디지털 서명이 유효하지 않거나 확인되지 않음. 이 프리젠테이션은 편집할 수 없도록 보호됨.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "서명 보기", "PE.Views.FileMenuPanels.Settings.okButtonText": "적용", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "공동 편집 모드", "PE.Views.FileMenuPanels.Settings.strFast": "Fast", "PE.Views.FileMenuPanels.Settings.strFontRender": "글꼴 힌트", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "매크로 설정", "PE.Views.FileMenuPanels.Settings.strPasteButton": "내용을 붙여넣을 때 \"붙여넣기 옵션\" 표시", "PE.Views.FileMenuPanels.Settings.strStrict": "Strict", "PE.Views.FileMenuPanels.Settings.strTheme": "인터페이스 테마", "PE.Views.FileMenuPanels.Settings.strUnit": "측정 단위", "PE.Views.FileMenuPanels.Settings.strZoom": "기본 확대/축소 값", "PE.Views.FileMenuPanels.Settings.text10Minutes": "매 10 분마다", "PE.Views.FileMenuPanels.Settings.text30Minutes": "매 30 분마다", "PE.Views.FileMenuPanels.Settings.text5Minutes": "매 5 분마다", "PE.Views.FileMenuPanels.Settings.text60Minutes": "매시간", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "정렬 가이드", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "자동 복구", "PE.Views.FileMenuPanels.Settings.textAutoSave": "자동 저장", "PE.Views.FileMenuPanels.Settings.textDisabled": "사용 안 함", "PE.Views.FileMenuPanels.Settings.textForceSave": "모든 기록 버전을 서버에 저장", "PE.Views.FileMenuPanels.Settings.textMinute": "Every Minute", "PE.Views.FileMenuPanels.Settings.txtAll": "모두보기", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "자동 고침 옵션...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "사전 설정 캐시 모드", "PE.Views.FileMenuPanels.Settings.txtCm": "센티미터", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Fit to Slide", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "너비에 맞춤", "PE.Views.FileMenuPanels.Settings.txtInch": "인치", "PE.Views.FileMenuPanels.Settings.txtLast": "마지막보기", "PE.Views.FileMenuPanels.Settings.txtMac": "OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "기본", "PE.Views.FileMenuPanels.Settings.txtProofing": "보정", "PE.Views.FileMenuPanels.Settings.txtPt": "Point", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "모두 활성화", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "알림 없이 모든 매크로 활성화", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "맞춤법 검사", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "모두 비활성화", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "알림없이 모든 매크로를 비활성화", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "알림 표시", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "모든 매크로를 비활성화로 알림", "PE.Views.FileMenuPanels.Settings.txtWin": "Windows", "PE.Views.HeaderFooterDialog.applyAllText": "모두에 적용", "PE.Views.HeaderFooterDialog.applyText": "적용", "PE.Views.HeaderFooterDialog.diffLanguage": "슬라이드 마스터와 다른 날짜 형식은 사용할 수 없습니다. <br>마스터를 변경하려면 \"적용\" 대신 \"모두 적용\"을 클릭하십시오.", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "경고", "PE.Views.HeaderFooterDialog.textDateTime": "날짜 및 시간", "PE.Views.HeaderFooterDialog.textFixed": "고정", "PE.Views.HeaderFooterDialog.textFooter": "바닥글 텍스트", "PE.Views.HeaderFooterDialog.textFormat": "형식", "PE.Views.HeaderFooterDialog.textLang": "언어", "PE.Views.HeaderFooterDialog.textNotTitle": "제목 슬라이드에 표시되지 않음", "PE.Views.HeaderFooterDialog.textPreview": "미리보기", "PE.Views.HeaderFooterDialog.textSlideNum": "슬라이드 번호", "PE.Views.HeaderFooterDialog.textTitle": "꼬리말 설정", "PE.Views.HeaderFooterDialog.textUpdate": "자동 업데이트", "PE.Views.HyperlinkSettingsDialog.strDisplay": "표시", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "링크 대상", "PE.Views.HyperlinkSettingsDialog.textDefault": "선택한 텍스트 조각", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "여기에 캡션 입력", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "여기에 링크를 입력하십시오", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "여기에 툴팁 입력", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "외부 링크", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "이 프리젠 테이션에서 슬라이드", "PE.Views.HyperlinkSettingsDialog.textSlides": "슬라이드", "PE.Views.HyperlinkSettingsDialog.textTipText": "스크린 팁 텍스트", "PE.Views.HyperlinkSettingsDialog.textTitle": "하이퍼 링크 설정", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "이 입력란은 필수 항목", "PE.Views.HyperlinkSettingsDialog.txtFirst": "First Slide", "PE.Views.HyperlinkSettingsDialog.txtLast": "마지막 슬라이드", "PE.Views.HyperlinkSettingsDialog.txtNext": "다음 슬라이드", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "이 필드는 \"http://www.example.com\"형식의 URL이어야합니다.", "PE.Views.HyperlinkSettingsDialog.txtPrev": "이전 슬라이드", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "이 필드는 2083 자로 제한되어 있습니다", "PE.Views.HyperlinkSettingsDialog.txtSlide": "슬라이드", "PE.Views.ImageSettings.textAdvanced": "고급 설정 표시", "PE.Views.ImageSettings.textCrop": "자르기", "PE.Views.ImageSettings.textCropFill": "채우기", "PE.Views.ImageSettings.textCropFit": "맞춤", "PE.Views.ImageSettings.textCropToShape": "도형에 맞게 자르기", "PE.Views.ImageSettings.textEdit": "편집", "PE.Views.ImageSettings.textEditObject": "개체 편집", "PE.Views.ImageSettings.textFitSlide": "슬라이드에 맞추기", "PE.Views.ImageSettings.textFlip": "대칭", "PE.Views.ImageSettings.textFromFile": "파일로부터", "PE.Views.ImageSettings.textFromStorage": "스토리지로 부터", "PE.Views.ImageSettings.textFromUrl": "URL로부터", "PE.Views.ImageSettings.textHeight": "높이", "PE.Views.ImageSettings.textHint270": "왼쪽으로 90도 회전", "PE.Views.ImageSettings.textHint90": "오른쪽으로 90도 회전", "PE.Views.ImageSettings.textHintFlipH": "좌우대칭", "PE.Views.ImageSettings.textHintFlipV": "상하대칭", "PE.Views.ImageSettings.textInsert": "이미지 바꾸기", "PE.Views.ImageSettings.textOriginalSize": "실제 크기", "PE.Views.ImageSettings.textRecentlyUsed": "최근 사용된", "PE.Views.ImageSettings.textRotate90": "90도 회전", "PE.Views.ImageSettings.textRotation": "회전", "PE.Views.ImageSettings.textSize": "크기", "PE.Views.ImageSettings.textWidth": "너비", "PE.Views.ImageSettingsAdvanced.textAlt": "대체 텍스트", "PE.Views.ImageSettingsAdvanced.textAltDescription": "설명", "PE.Views.ImageSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "PE.Views.ImageSettingsAdvanced.textAngle": "각도", "PE.Views.ImageSettingsAdvanced.textFlipped": "대칭됨", "PE.Views.ImageSettingsAdvanced.textHeight": "높이", "PE.Views.ImageSettingsAdvanced.textHorizontally": "수평", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "일정 비율", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "실제 크기", "PE.Views.ImageSettingsAdvanced.textPlacement": "게재 위치", "PE.Views.ImageSettingsAdvanced.textPosition": "위치", "PE.Views.ImageSettingsAdvanced.textRotation": "회전", "PE.Views.ImageSettingsAdvanced.textSize": "크기", "PE.Views.ImageSettingsAdvanced.textTitle": "이미지 - 고급 설정", "PE.Views.ImageSettingsAdvanced.textVertically": "세로", "PE.Views.ImageSettingsAdvanced.textWidth": "너비", "PE.Views.LeftMenu.tipAbout": "정보", "PE.Views.LeftMenu.tipChat": "채팅", "PE.Views.LeftMenu.tipComments": "Comments", "PE.Views.LeftMenu.tipPlugins": "플러그인", "PE.Views.LeftMenu.tipSearch": "검색", "PE.Views.LeftMenu.tipSlides": "슬라이드", "PE.Views.LeftMenu.tipSupport": "피드백 및 지원", "PE.Views.LeftMenu.tipTitles": "제목", "PE.Views.LeftMenu.txtDeveloper": "개발자 모드", "PE.Views.LeftMenu.txtLimit": "접근 제한", "PE.Views.LeftMenu.txtTrial": "시험 모드", "PE.Views.LeftMenu.txtTrialDev": "개발자 모드 시도", "PE.Views.ParagraphSettings.strLineHeight": "줄 간격", "PE.Views.ParagraphSettings.strParagraphSpacing": "단락 간격", "PE.Views.ParagraphSettings.strSpacingAfter": "이후", "PE.Views.ParagraphSettings.strSpacingBefore": "이전", "PE.Views.ParagraphSettings.textAdvanced": "고급 설정 표시", "PE.Views.ParagraphSettings.textAt": "At", "PE.Views.ParagraphSettings.textAtLeast": "적어도", "PE.Views.ParagraphSettings.textAuto": "배수", "PE.Views.ParagraphSettings.textExact": "고정", "PE.Views.ParagraphSettings.txtAutoText": "자동", "PE.Views.ParagraphSettingsAdvanced.noTabs": "지정된 탭이이 필드에 나타납니다", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "모든 대문자", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "이중 취소 선", "PE.Views.ParagraphSettingsAdvanced.strIndent": "들여쓰기", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "줄 간격", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Right", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "후", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "이전", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "특별한", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "글꼴", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "들여쓰기 및 간격", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "작은 대문자", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "간격", "PE.Views.ParagraphSettingsAdvanced.strStrike": "취소선", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "PE.Views.ParagraphSettingsAdvanced.strTabs": "탭", "PE.Views.ParagraphSettingsAdvanced.textAlign": "정렬", "PE.Views.ParagraphSettingsAdvanced.textAuto": "배수", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "문자 간격", "PE.Views.ParagraphSettingsAdvanced.textDefault": "기본 탭", "PE.Views.ParagraphSettingsAdvanced.textEffects": "효과", "PE.Views.ParagraphSettingsAdvanced.textExact": "고정", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "머리글 행", "PE.Views.ParagraphSettingsAdvanced.textHanging": "둘째 줄 이하", "PE.Views.ParagraphSettingsAdvanced.textJustified": "균등분할", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(없음)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "삭제", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "모두 제거", "PE.Views.ParagraphSettingsAdvanced.textSet": "지정", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "왼쪽", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "탭 위치", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "오른쪽", "PE.Views.ParagraphSettingsAdvanced.textTitle": "단락 - 고급 설정", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "자동", "PE.Views.RightMenu.txtChartSettings": "차트 설정", "PE.Views.RightMenu.txtImageSettings": "이미지 설정", "PE.Views.RightMenu.txtParagraphSettings": "단락 설정", "PE.Views.RightMenu.txtShapeSettings": "도형 설정", "PE.Views.RightMenu.txtSignatureSettings": "서명 세팅", "PE.Views.RightMenu.txtSlideSettings": "슬라이드 설정", "PE.Views.RightMenu.txtTableSettings": "표 설정", "PE.Views.RightMenu.txtTextArtSettings": "텍스트 아트 설정", "PE.Views.ShapeSettings.strBackground": "배경색", "PE.Views.ShapeSettings.strChange": "도형 변경", "PE.Views.ShapeSettings.strColor": "Color", "PE.Views.ShapeSettings.strFill": "채우기", "PE.Views.ShapeSettings.strForeground": "전경색", "PE.Views.ShapeSettings.strPattern": "패턴", "PE.Views.ShapeSettings.strShadow": "음영 표시", "PE.Views.ShapeSettings.strSize": "크기", "PE.Views.ShapeSettings.strStroke": "선", "PE.Views.ShapeSettings.strTransparency": "투명도", "PE.Views.ShapeSettings.strType": "유형", "PE.Views.ShapeSettings.textAdvanced": "고급 설정 표시", "PE.Views.ShapeSettings.textAngle": "각도", "PE.Views.ShapeSettings.textBorderSizeErr": "입력 한 값이 잘못되었습니다. <br> 0 ~ 1584 포인트 사이의 값을 입력하십시오.", "PE.Views.ShapeSettings.textColor": "색상 채우기", "PE.Views.ShapeSettings.textDirection": "Direction", "PE.Views.ShapeSettings.textEmptyPattern": "패턴 없음", "PE.Views.ShapeSettings.textFlip": "대칭", "PE.Views.ShapeSettings.textFromFile": "파일에서", "PE.Views.ShapeSettings.textFromStorage": "스토리지로 부터", "PE.Views.ShapeSettings.textFromUrl": "URL로부터", "PE.Views.ShapeSettings.textGradient": "그라데이션 포인트", "PE.Views.ShapeSettings.textGradientFill": "그라데이션 채우기", "PE.Views.ShapeSettings.textHint270": "왼쪽으로 90도 회전", "PE.Views.ShapeSettings.textHint90": "오른쪽으로 90도 회전", "PE.Views.ShapeSettings.textHintFlipH": "좌우대칭", "PE.Views.ShapeSettings.textHintFlipV": "상하대칭", "PE.Views.ShapeSettings.textImageTexture": "그림 또는 질감", "PE.Views.ShapeSettings.textLinear": "선형", "PE.Views.ShapeSettings.textNoFill": "채우기 없음", "PE.Views.ShapeSettings.textPatternFill": "패턴", "PE.Views.ShapeSettings.textPosition": "위치", "PE.Views.ShapeSettings.textRadial": "방사형", "PE.Views.ShapeSettings.textRecentlyUsed": "최근 사용된", "PE.Views.ShapeSettings.textRotate90": "90도 회전", "PE.Views.ShapeSettings.textRotation": "회전", "PE.Views.ShapeSettings.textSelectImage": "그림선택", "PE.Views.ShapeSettings.textSelectTexture": "선택", "PE.Views.ShapeSettings.textStretch": "늘이기", "PE.Views.ShapeSettings.textStyle": "스타일", "PE.Views.ShapeSettings.textTexture": "텍스처에서", "PE.Views.ShapeSettings.textTile": "타일", "PE.Views.ShapeSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "PE.Views.ShapeSettings.txtBrownPaper": "갈색 종이", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "PE.Views.ShapeSettings.txtGrain": "Grain", "PE.Views.ShapeSettings.txtGranite": "Granite", "PE.Views.ShapeSettings.txtGreyPaper": "회색 용지", "PE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "가죽", "PE.Views.ShapeSettings.txtNoBorders": "No Line", "PE.Views.ShapeSettings.txtPapyrus": "파피루스", "PE.Views.ShapeSettings.txtWood": "우드", "PE.Views.ShapeSettingsAdvanced.strColumns": "Columns", "PE.Views.ShapeSettingsAdvanced.strMargins": "텍스트 채우기", "PE.Views.ShapeSettingsAdvanced.textAlt": "대체 텍스트", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "설명", "PE.Views.ShapeSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "제목", "PE.Views.ShapeSettingsAdvanced.textAngle": "각도", "PE.Views.ShapeSettingsAdvanced.textArrows": "화살표", "PE.Views.ShapeSettingsAdvanced.textAutofit": "자동조정", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "크기 시작", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "스타일 시작", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Bottom", "PE.Views.ShapeSettingsAdvanced.textCapType": "모자 유형", "PE.Views.ShapeSettingsAdvanced.textColNumber": "열 수", "PE.Views.ShapeSettingsAdvanced.textEndSize": "끝 크기", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "끝 스타일", "PE.Views.ShapeSettingsAdvanced.textFlat": "Flat", "PE.Views.ShapeSettingsAdvanced.textFlipped": "대칭됨", "PE.Views.ShapeSettingsAdvanced.textHeight": "높이", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "수평", "PE.Views.ShapeSettingsAdvanced.textJoinType": "조인 유형", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "일정 비율", "PE.Views.ShapeSettingsAdvanced.textLeft": "왼쪽", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "선 스타일", "PE.Views.ShapeSettingsAdvanced.textMiter": "연귀", "PE.Views.ShapeSettingsAdvanced.textNofit": "자동으로 조정하지 않음", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "텍스트에 맞게 모양 조정", "PE.Views.ShapeSettingsAdvanced.textRight": "오른쪽", "PE.Views.ShapeSettingsAdvanced.textRotation": "회전", "PE.Views.ShapeSettingsAdvanced.textRound": "Round", "PE.Views.ShapeSettingsAdvanced.textShrink": "텍스트 초과시 자동 조정", "PE.Views.ShapeSettingsAdvanced.textSize": "크기", "PE.Views.ShapeSettingsAdvanced.textSpacing": "열 사이의 간격", "PE.Views.ShapeSettingsAdvanced.textSquare": "Square", "PE.Views.ShapeSettingsAdvanced.textTextBox": "텍스트 상자", "PE.Views.ShapeSettingsAdvanced.textTitle": "도형 - 고급 설정", "PE.Views.ShapeSettingsAdvanced.textTop": "Top", "PE.Views.ShapeSettingsAdvanced.textVertically": "세로", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "가중치 및 화살표", "PE.Views.ShapeSettingsAdvanced.textWidth": "너비", "PE.Views.ShapeSettingsAdvanced.txtNone": "없음", "PE.Views.SignatureSettings.notcriticalErrorTitle": "경고", "PE.Views.SignatureSettings.strDelete": "서명 삭제", "PE.Views.SignatureSettings.strDetails": "서명 상세", "PE.Views.SignatureSettings.strInvalid": "잘못된 서명", "PE.Views.SignatureSettings.strSign": "서명", "PE.Views.SignatureSettings.strSignature": "서명", "PE.Views.SignatureSettings.strValid": "유효 서명", "PE.Views.SignatureSettings.txtContinueEditing": "무조건 편집", "PE.Views.SignatureSettings.txtEditWarning": "수정하면 프레젠테이션의 서명이 삭제됩니다. <br>계속하시겠습니까?", "PE.Views.SignatureSettings.txtRemoveWarning": "이 서명을 삭제하시겠습니까?<br>이 작업은 취소할 수 없습니다.", "PE.Views.SignatureSettings.txtSigned": "유효 서명자가 프리젠테이션에 추가되었슴. 이 프리젠테이션은 편집할 수 없도록 보호됨.", "PE.Views.SignatureSettings.txtSignedInvalid": "프리젠테이션내 몇가지 디지털 서명이 유효하지 않거나 확인되지 않음. 이 프리젠테이션은 편집할 수 없도록 보호됨.", "PE.Views.SlideSettings.strBackground": "배경색", "PE.Views.SlideSettings.strColor": "Color", "PE.Views.SlideSettings.strDateTime": "날짜 및 시간 표시", "PE.Views.SlideSettings.strFill": "배경", "PE.Views.SlideSettings.strForeground": "전경색", "PE.Views.SlideSettings.strPattern": "패턴", "PE.Views.SlideSettings.strSlideNum": "슬라이드 번호 표시", "PE.Views.SlideSettings.strTransparency": "투명도", "PE.Views.SlideSettings.textAdvanced": "고급 설정 표시", "PE.Views.SlideSettings.textAngle": "각도", "PE.Views.SlideSettings.textColor": "색상 채우기", "PE.Views.SlideSettings.textDirection": "Direction", "PE.Views.SlideSettings.textEmptyPattern": "패턴 없음", "PE.Views.SlideSettings.textFromFile": "파일로부터", "PE.Views.SlideSettings.textFromStorage": "스토리지로 부터", "PE.Views.SlideSettings.textFromUrl": "URL로부터", "PE.Views.SlideSettings.textGradient": "그라데이션 포인트", "PE.Views.SlideSettings.textGradientFill": "그라데이션 채우기", "PE.Views.SlideSettings.textImageTexture": "그림 또는 질감", "PE.Views.SlideSettings.textLinear": "선형", "PE.Views.SlideSettings.textNoFill": "채우기 없음", "PE.Views.SlideSettings.textPatternFill": "패턴", "PE.Views.SlideSettings.textPosition": "위치", "PE.Views.SlideSettings.textRadial": "방사형", "PE.Views.SlideSettings.textReset": "변경 사항 재설정", "PE.Views.SlideSettings.textSelectImage": "그림선택", "PE.Views.SlideSettings.textSelectTexture": "선택", "PE.Views.SlideSettings.textStretch": "늘이기", "PE.Views.SlideSettings.textStyle": "스타일", "PE.Views.SlideSettings.textTexture": "텍스처에서", "PE.Views.SlideSettings.textTile": "타일", "PE.Views.SlideSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "PE.Views.SlideSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "PE.Views.SlideSettings.txtBrownPaper": "갈색 종이", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "PE.Views.SlideSettings.txtGrain": "Grain", "PE.Views.SlideSettings.txtGranite": "Granite", "PE.Views.SlideSettings.txtGreyPaper": "회색 용지", "PE.Views.SlideSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "가죽", "PE.Views.SlideSettings.txtPapyrus": "파피루스", "PE.Views.SlideSettings.txtWood": "<PERSON>", "PE.Views.SlideshowSettings.textLoop": " 'Esc'를 누를 때까지 계속 반복합니다.", "PE.Views.SlideshowSettings.textTitle": "설정 표시", "PE.Views.SlideSizeSettings.strLandscape": "Landscape", "PE.Views.SlideSizeSettings.strPortrait": "Portrait", "PE.Views.SlideSizeSettings.textHeight": "높이", "PE.Views.SlideSizeSettings.textSlideOrientation": "슬라이드 방향", "PE.Views.SlideSizeSettings.textSlideSize": "슬라이드 크기", "PE.Views.SlideSizeSettings.textTitle": "슬라이드 크기 설정", "PE.Views.SlideSizeSettings.textWidth": "너비", "PE.Views.SlideSizeSettings.txt35": "35 mm 슬라이드", "PE.Views.SlideSizeSettings.txtA3": "A3 용지 (297x420mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 용지 (210x297mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) 용지 (250x353mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) 용지 (176x250mm)", "PE.Views.SlideSizeSettings.txtBanner": "배너", "PE.Views.SlideSizeSettings.txtCustom": "사용자 지정", "PE.Views.SlideSizeSettings.txtLedger": "원장 용지 (11x17 인치)", "PE.Views.SlideSizeSettings.txtLetter": "레터 용지 (8.5x11 인치)", "PE.Views.SlideSizeSettings.txtOverhead": "오버 헤드", "PE.Views.SlideSizeSettings.txtStandard": "표준 (4 : 3)", "PE.Views.SlideSizeSettings.txtWidescreen": "와이드스크린(16:9)", "PE.Views.Statusbar.goToPageText": "슬라이드로 이동", "PE.Views.Statusbar.pageIndexText": "{1} 중 {0} 슬라이드", "PE.Views.Statusbar.textShowBegin": "처음부터 보여주기", "PE.Views.Statusbar.textShowCurrent": "현재 슬라이드에서 보기", "PE.Views.Statusbar.textShowPresenterView": "프리젠터뷰 표시", "PE.Views.Statusbar.tipAccessRights": "문서 액세스 권한 관리", "PE.Views.Statusbar.tipFitPage": "슬라이드에 맞추기", "PE.Views.Statusbar.tipFitWidth": "너비에 맞춤", "PE.Views.Statusbar.tipPreview": "슬라이드 쇼 시작", "PE.Views.Statusbar.tipSetLang": "텍스트 언어 설정", "PE.Views.Statusbar.tipZoomFactor": "확대/축소", "PE.Views.Statusbar.tipZoomIn": "확대", "PE.Views.Statusbar.tipZoomOut": "축소", "PE.Views.Statusbar.txtPageNumInvalid": "유효하지 않은 슬라이드 번호", "PE.Views.TableSettings.deleteColumnText": "열 삭제", "PE.Views.TableSettings.deleteRowText": "행 삭제", "PE.Views.TableSettings.deleteTableText": "테이블 삭제", "PE.Views.TableSettings.insertColumnLeftText": "왼쪽에 열 삽입", "PE.Views.TableSettings.insertColumnRightText": "오른쪽 열 삽입", "PE.Views.TableSettings.insertRowAboveText": "Insert Row Above", "PE.Views.TableSettings.insertRowBelowText": "아래에 행 삽입", "PE.Views.TableSettings.mergeCellsText": "셀 병합", "PE.Views.TableSettings.selectCellText": "셀 선택", "PE.Views.TableSettings.selectColumnText": "열 선택", "PE.Views.TableSettings.selectRowText": "행 선택", "PE.Views.TableSettings.selectTableText": "표 선택", "PE.Views.TableSettings.splitCellsText": "셀 분할 ...", "PE.Views.TableSettings.splitCellTitleText": "셀 분할", "PE.Views.TableSettings.textAdvanced": "고급 설정 표시", "PE.Views.TableSettings.textBackColor": "배경색", "PE.Views.TableSettings.textBanded": "줄무늬", "PE.Views.TableSettings.textBorderColor": "Color", "PE.Views.TableSettings.textBorders": "테두리 스타일", "PE.Views.TableSettings.textCellSize": "셀 크기", "PE.Views.TableSettings.textColumns": "열", "PE.Views.TableSettings.textDistributeCols": "컬럼 배포", "PE.Views.TableSettings.textDistributeRows": "행 배포", "PE.Views.TableSettings.textEdit": "행 및 열", "PE.Views.TableSettings.textEmptyTemplate": "템플릿 없음", "PE.Views.TableSettings.textFirst": "First", "PE.Views.TableSettings.textHeader": "머리글", "PE.Views.TableSettings.textHeight": "높이", "PE.Views.TableSettings.textLast": "마지막", "PE.Views.TableSettings.textRows": "행", "PE.Views.TableSettings.textSelectBorders": "위에서 선택한 스타일 적용을 변경하려는 테두리 선택", "PE.Views.TableSettings.textTemplate": "템플릿에서 선택", "PE.Views.TableSettings.textTotal": "요약 행", "PE.Views.TableSettings.textWidth": "너비", "PE.Views.TableSettings.tipAll": "바깥쪽 테두리 및 안쪽 테두리", "PE.Views.TableSettings.tipBottom": "바깥 아래쪽 테두리", "PE.Views.TableSettings.tipInner": "내부 라인 만 설정", "PE.Views.TableSettings.tipInnerHor": "안쪽 가로 테두리", "PE.Views.TableSettings.tipInnerVert": "세로 내부 선만 설정", "PE.Views.TableSettings.tipLeft": "바깥 왼쪽 테두리", "PE.Views.TableSettings.tipNone": "테두리 없음 설정", "PE.Views.TableSettings.tipOuter": "바깥쪽 테두리", "PE.Views.TableSettings.tipRight": "바깥 오른쪽 테두리", "PE.Views.TableSettings.tipTop": "바깥 위쪽 테두리", "PE.Views.TableSettings.txtNoBorders": "테두리 없음", "PE.Views.TableSettings.txtTable_Accent": "강조", "PE.Views.TableSettings.txtTable_DarkStyle": "어두운 스타일", "PE.Views.TableSettings.txtTable_LightStyle": "밝은 스타일", "PE.Views.TableSettings.txtTable_MediumStyle": "보통 스타일", "PE.Views.TableSettings.txtTable_NoGrid": "그리드 없음", "PE.Views.TableSettings.txtTable_NoStyle": "스타일 없음", "PE.Views.TableSettings.txtTable_TableGrid": "테이블 그리드", "PE.Views.TableSettings.txtTable_ThemedStyle": "테마 스타일", "PE.Views.TableSettingsAdvanced.textAlt": "대체 텍스트", "PE.Views.TableSettingsAdvanced.textAltDescription": "설명", "PE.Views.TableSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "PE.Views.TableSettingsAdvanced.textAltTitle": "제목", "PE.Views.TableSettingsAdvanced.textBottom": "Bottom", "PE.Views.TableSettingsAdvanced.textCheckMargins": "기본 여백 사용", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "기본 여백", "PE.Views.TableSettingsAdvanced.textLeft": "왼쪽", "PE.Views.TableSettingsAdvanced.textMargins": "셀 여백", "PE.Views.TableSettingsAdvanced.textRight": "오른쪽", "PE.Views.TableSettingsAdvanced.textTitle": "표 - 고급 설정", "PE.Views.TableSettingsAdvanced.textTop": "Top", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "여백", "PE.Views.TextArtSettings.strBackground": "배경색", "PE.Views.TextArtSettings.strColor": "Color", "PE.Views.TextArtSettings.strFill": "채우기", "PE.Views.TextArtSettings.strForeground": "전경색", "PE.Views.TextArtSettings.strPattern": "패턴", "PE.Views.TextArtSettings.strSize": "크기", "PE.Views.TextArtSettings.strStroke": "선", "PE.Views.TextArtSettings.strTransparency": "투명도", "PE.Views.TextArtSettings.strType": "유형", "PE.Views.TextArtSettings.textAngle": "각도", "PE.Views.TextArtSettings.textBorderSizeErr": "입력 한 값이 잘못되었습니다. <br> 0pt ~ 1584pt 사이의 값을 입력하십시오.", "PE.Views.TextArtSettings.textColor": "색 채우기", "PE.Views.TextArtSettings.textDirection": "Direction", "PE.Views.TextArtSettings.textEmptyPattern": "패턴 없음", "PE.Views.TextArtSettings.textFromFile": "파일에서", "PE.Views.TextArtSettings.textFromUrl": "URL로부터", "PE.Views.TextArtSettings.textGradient": "그라데이션 포인트", "PE.Views.TextArtSettings.textGradientFill": "그라데이션 채우기", "PE.Views.TextArtSettings.textImageTexture": "그림 또는 질감", "PE.Views.TextArtSettings.textLinear": "선형", "PE.Views.TextArtSettings.textNoFill": "채우기 없음", "PE.Views.TextArtSettings.textPatternFill": "패턴", "PE.Views.TextArtSettings.textPosition": "위치", "PE.Views.TextArtSettings.textRadial": "방사형", "PE.Views.TextArtSettings.textSelectTexture": "선택", "PE.Views.TextArtSettings.textStretch": "늘이기", "PE.Views.TextArtSettings.textStyle": "스타일", "PE.Views.TextArtSettings.textTemplate": "템플릿", "PE.Views.TextArtSettings.textTexture": "텍스처에서", "PE.Views.TextArtSettings.textTile": "타일", "PE.Views.TextArtSettings.textTransform": "변형", "PE.Views.TextArtSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "PE.Views.TextArtSettings.txtBrownPaper": "갈색 종이", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "PE.Views.TextArtSettings.txtGrain": "Grain", "PE.Views.TextArtSettings.txtGranite": "Granite", "PE.Views.TextArtSettings.txtGreyPaper": "회색 용지", "PE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "가죽", "PE.Views.TextArtSettings.txtNoBorders": "No Line", "PE.Views.TextArtSettings.txtPapyrus": "파피루스", "PE.Views.TextArtSettings.txtWood": "<PERSON>", "PE.Views.Toolbar.capAddSlide": "슬라이드 추가", "PE.Views.Toolbar.capBtnAddComment": "코멘트 달기", "PE.Views.Toolbar.capBtnComment": "댓글", "PE.Views.Toolbar.capBtnDateTime": "날짜 및 시간", "PE.Views.Toolbar.capBtnInsHeader": "꼬리말", "PE.Views.Toolbar.capBtnInsSymbol": "기호", "PE.Views.Toolbar.capBtnSlideNum": "슬라이드 번호", "PE.Views.Toolbar.capInsertAudio": "소리", "PE.Views.Toolbar.capInsertChart": "차트", "PE.Views.Toolbar.capInsertEquation": "수식", "PE.Views.Toolbar.capInsertHyperlink": "하이퍼 링크", "PE.Views.Toolbar.capInsertImage": "그림", "PE.Views.Toolbar.capInsertShape": "쉐이프", "PE.Views.Toolbar.capInsertTable": "테이블", "PE.Views.Toolbar.capInsertText": "텍스트 상자", "PE.Views.Toolbar.capInsertVideo": "동영상", "PE.Views.Toolbar.capTabFile": "파일", "PE.Views.Toolbar.capTabHome": "집", "PE.Views.Toolbar.capTabInsert": "삽입", "PE.Views.Toolbar.mniCapitalizeWords": "각 단어의 첫글자를 대문자로", "PE.Views.Toolbar.mniCustomTable": "사용자 정의 테이블 삽입", "PE.Views.Toolbar.mniImageFromFile": "그림 파일에서", "PE.Views.Toolbar.mniImageFromStorage": "스토리지에서 불러오기", "PE.Views.Toolbar.mniImageFromUrl": "URL에서 그림", "PE.Views.Toolbar.mniLowerCase": "소문자", "PE.Views.Toolbar.mniSentenceCase": "문장의 첫 글자를 대문자로", "PE.Views.Toolbar.mniSlideAdvanced": "고급 설정", "PE.Views.Toolbar.mniSlideStandard": "표준 (4 : 3)", "PE.Views.Toolbar.mniSlideWide": "와이드 스크린 (16 : 9)", "PE.Views.Toolbar.mniToggleCase": "대/소문자 전환", "PE.Views.Toolbar.mniUpperCase": "대문자", "PE.Views.Toolbar.strMenuNoFill": "채우기 없음", "PE.Views.Toolbar.textAlignBottom": "텍스트를 하단에 정렬", "PE.Views.Toolbar.textAlignCenter": "센터 텍스트", "PE.Views.Toolbar.textAlignJust": "Justify", "PE.Views.Toolbar.textAlignLeft": "왼쪽 정렬", "PE.Views.Toolbar.textAlignMiddle": "중간에 텍스트 정렬", "PE.Views.Toolbar.textAlignRight": "텍스트 정렬", "PE.Views.Toolbar.textAlignTop": "텍스트를 상단에 정렬", "PE.Views.Toolbar.textArrangeBack": "맨 뒤로 보내기", "PE.Views.Toolbar.textArrangeBackward": "뒤로 이동", "PE.Views.Toolbar.textArrangeForward": "앞으로 이동", "PE.Views.Toolbar.textArrangeFront": "전경으로 가져 오기", "PE.Views.Toolbar.textBold": "Bold", "PE.Views.Toolbar.textColumnsCustom": "사용자 정의 열", "PE.Views.Toolbar.textColumnsOne": "1열", "PE.Views.Toolbar.textColumnsThree": "3열", "PE.Views.Toolbar.textColumnsTwo": "2열", "PE.Views.Toolbar.textItalic": "Italic", "PE.Views.Toolbar.textListSettings": "목록설정", "PE.Views.Toolbar.textRecentlyUsed": "최근 사용된", "PE.Views.Toolbar.textShapeAlignBottom": "아래쪽 정렬", "PE.Views.Toolbar.textShapeAlignCenter": "정렬 중심", "PE.Views.Toolbar.textShapeAlignLeft": "왼쪽 정렬", "PE.Views.Toolbar.textShapeAlignMiddle": "가운데 정렬", "PE.Views.Toolbar.textShapeAlignRight": "오른쪽 정렬", "PE.Views.Toolbar.textShapeAlignTop": "정렬", "PE.Views.Toolbar.textShowBegin": "처음부터 보여주기", "PE.Views.Toolbar.textShowCurrent": "현재 슬라이드에서보기", "PE.Views.Toolbar.textShowPresenterView": "프리젠터뷰를 보기", "PE.Views.Toolbar.textShowSettings": "설정 표시", "PE.Views.Toolbar.textStrikeout": "취소선", "PE.Views.Toolbar.textSubscript": "아래 첨자", "PE.Views.Toolbar.textSuperscript": "위첨자", "PE.Views.Toolbar.textTabAnimation": "애니메이션", "PE.Views.Toolbar.textTabCollaboration": "협업", "PE.Views.Toolbar.textTabFile": "파일", "PE.Views.Toolbar.textTabHome": "홈", "PE.Views.Toolbar.textTabInsert": "삽입", "PE.Views.Toolbar.textTabProtect": "보호", "PE.Views.Toolbar.textTabTransitions": "전환", "PE.Views.Toolbar.textTabView": "뷰", "PE.Views.Toolbar.textTitleError": "오류", "PE.Views.Toolbar.textUnderline": "밑줄", "PE.Views.Toolbar.tipAddSlide": "슬라이드 추가", "PE.Views.Toolbar.tipBack": "뒤로", "PE.Views.Toolbar.tipChangeCase": "대소문자 변경", "PE.Views.Toolbar.tipChangeChart": "차트 유형 변경", "PE.Views.Toolbar.tipChangeSlide": "슬라이드 레이아웃 변경", "PE.Views.Toolbar.tipClearStyle": "스타일 지우기", "PE.Views.Toolbar.tipColorSchemas": "Change Color Scheme", "PE.Views.Toolbar.tipColumns": "열 삽입", "PE.Views.Toolbar.tipCopy": "복사", "PE.Views.Toolbar.tipCopyStyle": "스타일 복사", "PE.Views.Toolbar.tipDateTime": "현재 날짜 시간 삽입", "PE.Views.Toolbar.tipDecFont": "글꼴 크기 작게", "PE.Views.Toolbar.tipDecPrLeft": "들여 쓰기 감소", "PE.Views.Toolbar.tipEditHeader": "바닥 글 편집", "PE.Views.Toolbar.tipFontColor": "글꼴 색", "PE.Views.Toolbar.tipFontName": "글꼴", "PE.Views.Toolbar.tipFontSize": "글꼴 크기", "PE.Views.Toolbar.tipHAligh": "수평 정렬", "PE.Views.Toolbar.tipHighlightColor": "텍스트 강조 색", "PE.Views.Toolbar.tipIncFont": "글꼴 크기 증가", "PE.Views.Toolbar.tipIncPrLeft": "들여 쓰기", "PE.Views.Toolbar.tipInsertAudio": "소리 삽입", "PE.Views.Toolbar.tipInsertChart": "차트 삽입", "PE.Views.Toolbar.tipInsertEquation": "수식 삽입", "PE.Views.Toolbar.tipInsertHyperlink": "하이퍼 링크 추가", "PE.Views.Toolbar.tipInsertImage": "그림 삽입", "PE.Views.Toolbar.tipInsertShape": "도형 삽입", "PE.Views.Toolbar.tipInsertSymbol": "기호 삽입", "PE.Views.Toolbar.tipInsertTable": "표 삽입", "PE.Views.Toolbar.tipInsertText": "텍스트 상자 삽입", "PE.Views.Toolbar.tipInsertTextArt": "텍스트 아트 삽입", "PE.Views.Toolbar.tipInsertVideo": "동영상 삽입", "PE.Views.Toolbar.tipLineSpace": "줄 간격", "PE.Views.Toolbar.tipMarkers": "글 머리 기호", "PE.Views.Toolbar.tipMarkersArrow": "화살 글머리 기호", "PE.Views.Toolbar.tipMarkersCheckmark": "체크 표시 글머리 기호", "PE.Views.Toolbar.tipMarkersDash": "대시 글머리 기호", "PE.Views.Toolbar.tipMarkersFRhombus": "채워진 마름모 글머리 기호", "PE.Views.Toolbar.tipMarkersFRound": "채워진 원형 글머리 기호", "PE.Views.Toolbar.tipMarkersFSquare": "채워진 사각형 글머리 기호", "PE.Views.Toolbar.tipMarkersHRound": "빈 원형 글머리 기호", "PE.Views.Toolbar.tipMarkersStar": "별 글머리 기호", "PE.Views.Toolbar.tipNone": "없음", "PE.Views.Toolbar.tipNumbers": "번호 매기기", "PE.Views.Toolbar.tipPaste": "붙여 넣기", "PE.Views.Toolbar.tipPreview": "슬라이드 쇼 시작", "PE.Views.Toolbar.tipPrint": "인쇄", "PE.Views.Toolbar.tipRedo": "다시 실행", "PE.Views.Toolbar.tipSave": "저장", "PE.Views.Toolbar.tipSaveCoauth": "다른 사용자가 볼 수 있도록 변경 사항을 저장하십시오.", "PE.Views.Toolbar.tipShapeAlign": "도형 정렬", "PE.Views.Toolbar.tipShapeArrange": "도형 배열", "PE.Views.Toolbar.tipSlideNum": "슬라이드 번호 삽입", "PE.Views.Toolbar.tipSlideSize": "슬라이드 크기 선택", "PE.Views.Toolbar.tipSlideTheme": "슬라이드 테마", "PE.Views.Toolbar.tipUndo": "실행 취소", "PE.Views.Toolbar.tipVAligh": "수직 정렬", "PE.Views.Toolbar.tipViewSettings": "설정보기", "PE.Views.Toolbar.txtDistribHor": "가로로 배포", "PE.Views.Toolbar.txtDistribVert": "수직 분배", "PE.Views.Toolbar.txtDuplicateSlide": "중복 슬라이드", "PE.Views.Toolbar.txtGroup": "그룹", "PE.Views.Toolbar.txtObjectsAlign": "선택한 개체 정렬", "PE.Views.Toolbar.txtScheme1": "사무실 테마", "PE.Views.Toolbar.txtScheme10": "중앙값", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "모듈", "PE.Views.Toolbar.txtScheme13": "호화 로움", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Origin", "PE.Views.Toolbar.txtScheme16": "종이", "PE.Views.Toolbar.txtScheme17": "지점", "PE.Views.Toolbar.txtScheme18": "기술", "PE.Views.Toolbar.txtScheme19": "트레킹", "PE.Views.Toolbar.txtScheme2": "Grayscale", "PE.Views.Toolbar.txtScheme20": "도시", "PE.Views.Toolbar.txtScheme21": "Verve", "PE.Views.Toolbar.txtScheme22": "신규 오피스", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Aspect", "PE.Views.Toolbar.txtScheme5": "Civic", "PE.Views.Toolbar.txtScheme6": "콩 코스", "PE.Views.Toolbar.txtScheme7": "주식", "PE.Views.Toolbar.txtScheme8": "흐름", "PE.Views.Toolbar.txtScheme9": "Foundry", "PE.Views.Toolbar.txtSlideAlign": "슬라이드 정렬", "PE.Views.Toolbar.txtUngroup": "그룹 해제", "PE.Views.Transitions.strDelay": "지연", "PE.Views.Transitions.strDuration": "재생 시간", "PE.Views.Transitions.strStartOnClick": "시작시 클릭", "PE.Views.Transitions.textBlack": "검은 색을 사용", "PE.Views.Transitions.textBottom": "바닥", "PE.Views.Transitions.textBottomLeft": "왼쪽 하단", "PE.Views.Transitions.textBottomRight": "오른쪽-하단", "PE.Views.Transitions.textClock": "시계", "PE.Views.Transitions.textClockwise": "시계 방향", "PE.Views.Transitions.textCounterclockwise": "반 시계 방향", "PE.Views.Transitions.textCover": "덮기", "PE.Views.Transitions.textFade": "밝기변화", "PE.Views.Transitions.textHorizontalIn": "수평 입력", "PE.Views.Transitions.textHorizontalOut": "수평 출력", "PE.Views.Transitions.textLeft": "왼쪽", "PE.Views.Transitions.textNone": "없음", "PE.Views.Transitions.textPush": "밀어내기", "PE.Views.Transitions.textRight": "오른쪽", "PE.Views.Transitions.textSmoothly": "부드럽게", "PE.Views.Transitions.textSplit": "나누기", "PE.Views.Transitions.textTop": "상위", "PE.Views.Transitions.textTopLeft": "왼쪽 위", "PE.Views.Transitions.textTopRight": "오른쪽 상단", "PE.Views.Transitions.textUnCover": "당기기", "PE.Views.Transitions.textVerticalIn": "수직 인치", "PE.Views.Transitions.textVerticalOut": "수직 출력", "PE.Views.Transitions.textWedge": "쇄기꼴", "PE.Views.Transitions.textWipe": "닦아내기", "PE.Views.Transitions.textZoom": "확대/축소", "PE.Views.Transitions.textZoomIn": "확대", "PE.Views.Transitions.textZoomOut": "축소", "PE.Views.Transitions.textZoomRotate": "확대/축소 및 회전", "PE.Views.Transitions.txtApplyToAll": "모든 슬라이드에 적용", "PE.Views.Transitions.txtParameters": "매개 변수", "PE.Views.Transitions.txtPreview": "미리보기", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textAlwaysShowToolbar": "항상 도구 모음 표시", "PE.Views.ViewTab.textFitToSlide": "슬라이드에 맞추기", "PE.Views.ViewTab.textFitToWidth": "너비에 맞춤", "PE.Views.ViewTab.textInterfaceTheme": "인터페이스 테마", "PE.Views.ViewTab.textNotes": "메모", "PE.Views.ViewTab.textRulers": "자", "PE.Views.ViewTab.textStatusBar": "상태 바", "PE.Views.ViewTab.textZoom": "확대 / 축소"}