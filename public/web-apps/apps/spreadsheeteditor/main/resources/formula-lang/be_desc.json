{"DATE": {"a": "(год;месяц;день)", "d": "Функция даты и времени, используется для добавления дат в стандартном формате дд.ММ.гггг"}, "DATEDIF": {"a": "(нач_дата;кон_дата;единица)", "d": "Функция даты и времени, возвращает разницу между двумя датами (начальной и конечной) согласно заданному интервалу (единице)"}, "DATEVALUE": {"a": "(дата_как_текст)", "d": "Функция даты и времени, возвращает порядковый номер заданной даты"}, "DAY": {"a": "(дата_в_числовом_формате)", "d": "Функция даты и времени, возвращает день (число от 1 до 31), соответствующий дате, заданной в числовом формате (дд.ММ.гггг по умолчанию)"}, "DAYS": {"a": "(кон_дата;нач_дата)", "d": "Функция даты и времени, возвращает количество дней между двумя датами"}, "DAYS360": {"a": "(нач_дата;кон_дата;[метод])", "d": "Функция даты и времени, возвращает количество дней между двумя датами (начальной и конечной) на основе 360-дневного года с использованием одного из методов вычислений (американского или европейского)"}, "EDATE": {"a": "(нач_дата;число_месяцев)", "d": "Функция даты и времени, возвращает порядковый номер даты, которая идет на заданное число месяцев (число_месяцев) до или после заданной даты (нач_дата)"}, "EOMONTH": {"a": "(нач_дата;число_месяцев)", "d": "Функция даты и времени, возвращает порядковый номер последнего дня месяца, который идет на заданное число месяцев до или после заданной начальной даты"}, "HOUR": {"a": "(время_в_числовом_формате)", "d": "Функция даты и времени, возвращает количество часов (число от 0 до 23), соответствующее заданному значению времени"}, "ISOWEEKNUM": {"a": "(дата)", "d": "Функция даты и времени, возвращает номер недели в году для определенной даты в соответствии со стандартами ISO"}, "MINUTE": {"a": "(время_в_числовом_формате)", "d": "Функция даты и времени, возвращает количество минут (число от 0 до 59), соответствующее заданному значению времени"}, "MONTH": {"a": "(дата_в_числовом_формате)", "d": "Функция даты и времени, возвращает месяц (число от 1 до 12), соответствующий дате, заданной в числовом формате (дд.ММ.гггг по умолчанию)"}, "NETWORKDAYS": {"a": "(нач_дата;кон_дата;[праздники])", "d": "Функция даты и времени, возвращает количество рабочих дней между двумя датами (начальной и конечной). Выходные и праздничные дни в это число не включаются"}, "NETWORKDAYS.INTL": {"a": "(нач_дата;кон_дата;[выходной];[праздники])", "d": "Функция даты и времени, возвращает количество рабочих дней между двумя датами с использованием параметров, определяющих, сколько в неделе выходных и какие дни являются выходными"}, "NOW": {"a": "()", "d": "Функция даты и времени, возвращает текущую дату и время в числовом формате; если до ввода этой функции для ячейки был задан формат Общий, он будет изменен на формат даты и времени, соответствующий региональным параметрам"}, "SECOND": {"a": "(время_в_числовом_формате)", "d": "Функция даты и времени, возвращает количество секунд (число от 0 до 59), соответствующее заданному значению времени"}, "TIME": {"a": "(часы;минуты;секунды)", "d": "Функция даты и времени, используется для добавления определенного времени в выбранном формате (по умолчанию чч:мм tt (указатель половины дня a.m./p.m.))"}, "TIMEVALUE": {"a": "(время_как_текст)", "d": "Функция даты и времени, возвращает порядковый номер, соответствующий заданному времени"}, "TODAY": {"a": "()", "d": "Функция даты и времени, используется для добавления текущей даты в следующем формате: дд.ММ.гггг. Данная функция не требует аргумента"}, "WEEKDAY": {"a": "(дата_в_числовом_формате;[тип])", "d": "Функция даты и времени, определяет, какой день недели соответствует заданной дате"}, "WEEKNUM": {"a": "(дата_в_числовом_формате;[тип])", "d": "Функция даты и времени, возвращает порядковый номер той недели в течение года, на которую приходится заданная дата"}, "WORKDAY": {"a": "(нач_дата;количество_дней;[праздники])", "d": "Функция даты и времени, возвращает дату, которая идет на заданное число дней (количество_дней) до или после заданной начальной даты, без учета выходных и праздничных дней"}, "WORKDAY.INTL": {"a": "(нач_дата;количество_дней;[выходной];[праздники])", "d": "Функция даты и времени, возвращает порядковый номер даты, отстоящей вперед или назад на заданное количество рабочих дней, с указанием настраиваемых параметров выходных, определяющих, сколько в неделе выходных дней и какие дни являются выходными"}, "YEAR": {"a": "(дата_в_числовом_формате)", "d": "Функция даты и времени, возвращает год (число от 1900 до 9999), соответствующий дате, заданной в числовом формате (дд.ММ.гггг по умолчанию)"}, "YEARFRAC": {"a": "(нач_дата;кон_дата;[базис])", "d": "Функция даты и времени, возвращает долю года, представленную числом целых дней между начальной и конечной датами, вычисляемую заданным способом"}, "BESSELI": {"a": "(X;N)", "d": "Инженерная функция, возвращает модифицированную функцию Бесселя, что эквивалентно вычислению функции Бесселя для чисто мнимого аргумента"}, "BESSELJ": {"a": "(X;N)", "d": "Инженерная функция, возвращает функцию Бесселя"}, "BESSELK": {"a": "(X;N)", "d": "Инженерная функция, возвращает модифицированную функцию Бесселя, что эквивалентно вычислению функции Бесселя для чисто мнимого аргумента"}, "BESSELY": {"a": "(X;N)", "d": "Инженерная функция, возвращает функцию Бесселя, также называемую функцией Вебера или функцией Неймана"}, "BIN2DEC": {"a": "(число)", "d": "Инженерная функция, преобразует двоичное число в десятичное"}, "BIN2HEX": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует двоичное число в шестнадцатеричное"}, "BIN2OCT": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует двоичное число в восьмеричное"}, "BITAND": {"a": "(число1;число2)", "d": "Инженерная функция, возвращает результат операции поразрядного И для двух чисел"}, "BITLSHIFT": {"a": "(число;число_бит)", "d": "Инженерная функция, возвращает число со сдвигом влево на указанное число бит"}, "BITOR": {"a": "(число1;число2)", "d": "Инженерная функция, возвращает результат операции поразрядного ИЛИ для двух чисел"}, "BITRSHIFT": {"a": "(число;число_бит)", "d": "Инженерная функция, возвращает число со сдвигом вправо на указанное число бит"}, "BITXOR": {"a": "(число1;число2)", "d": "Инженерная функция, возвращает результат операции поразрядного исключающего ИЛИ для двух чисел"}, "COMPLEX": {"a": "(действительная_часть;мнимая_часть;[мнимая_единица])", "d": "Инженерная функция, используется для преобразования действительной и мнимой части в комплексное число, выраженное в формате a + bi или a + bj"}, "CONVERT": {"a": "(число;исх_ед_изм;кон_ед_изм)", "d": "Инженерная функция, преобразует число из одной системы мер в другую; например, с помощью функции ПРЕОБР можно перевести таблицу расстояний в милях в таблицу расстояний в километрах"}, "DEC2BIN": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует десятичное число в двоичное"}, "DEC2HEX": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует десятичное число в шестнадцатеричное"}, "DEC2OCT": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует десятичное число в восьмеричное"}, "DELTA": {"a": "(число1;[число2])", "d": "Инженерная функция, используется для проверки равенства двух чисел. Функция возвращает 1, если числа равны, в противном случае возвращает 0"}, "ERF": {"a": "(нижний_предел;[верхний_предел])", "d": "Инженерная функция, используется для расчета значения функции ошибки, проинтегрированного в интервале от заданного нижнего до заданного верхнего предела"}, "ERF.PRECISE": {"a": "(x)", "d": "Инженерная функция, возвращает функцию ошибки"}, "ERFC": {"a": "(нижний_предел)", "d": "Инженерная функция, используется для расчета значения дополнительной функции ошибки, проинтегрированного в интервале от заданного нижнего предела до бесконечности"}, "ERFC.PRECISE": {"a": "(x)", "d": "Инженерная функция, возвращает дополнительную функцию ошибки, проинтегрированную в пределах от x до бесконечности"}, "GESTEP": {"a": "(число;[порог])", "d": "Инженерная функция, используется для проверки того, превышает ли какое-то число пороговое значение. Функция возвращает 1, если число больше или равно пороговому значению, в противном случае возвращает 0"}, "HEX2BIN": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует шестнадцатеричное число в двоичное"}, "HEX2DEC": {"a": "(число)", "d": "Инженерная функция, преобразует шестнадцатеричное число в десятичное"}, "HEX2OCT": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует шестнадцатеричное число в восьмеричное"}, "IMABS": {"a": "(компл_число)", "d": "Инженерная функция, возвращает абсолютное значение комплексного числа"}, "IMAGINARY": {"a": "(компл_число)", "d": "Инженерная функция, возвращает мнимую часть заданного комплексного числа"}, "IMARGUMENT": {"a": "(компл_число)", "d": "Инженерная функция, возвращает значение аргумента Тета, то есть угол в радианах"}, "IMCONJUGATE": {"a": "(компл_число)", "d": "Инженерная функция, возвращает комплексно-сопряженное значение комплексного числа"}, "IMCOS": {"a": "(компл_число)", "d": "Инженерная функция, возвращает косинус комплексного числа, представленного в текстовом формате a + bi или a + bj"}, "IMCOSH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический косинус комплексного числа в текстовом формате a + bi или a + bj"}, "IMCOT": {"a": "(компл_число)", "d": "Инженерная функция, возвращает котангенс комплексного числа в текстовом формате a + bi или a + bj"}, "IMCSC": {"a": "(компл_число)", "d": "Инженерная функция, возвращает косеканс комплексного числа в текстовом формате a + bi или a + bj"}, "IMCSCH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический косеканс комплексного числа в текстовом формате a + bi или a + bj"}, "IMDIV": {"a": "(компл_число1;компл_число2)", "d": "Инженерная функция, возвращает частное от деления двух комплексных чисел, представленных в формате a + bi или a + bj"}, "IMEXP": {"a": "(компл_число)", "d": "Инженерная функция, возвращает экспоненту комплексного числа (значение константы e, возведенной в степень, заданную комплексным числом). Константа e равна 2,71828182845904"}, "IMLN": {"a": "(компл_число)", "d": "Инженерная функция, возвращает натуральный логарифм комплексного числа"}, "IMLOG10": {"a": "(компл_число)", "d": "Инженерная функция, возвращает двоичный логарифм комплексного числа"}, "IMLOG2": {"a": "(компл_число)", "d": "Инженерная функция, возвращает десятичный логарифм комплексного числа"}, "IMPOWER": {"a": "(компл_число;число)", "d": "Инженерная функция, возвращает комплексное число, возведенное в заданную степень"}, "IMPRODUCT": {"a": "(список_аргументов)", "d": "Инженерная функция, возвращает произведение указанных комплексных чисел"}, "IMREAL": {"a": "(компл_число)", "d": "Инженерная функция, возвращает действительную часть комплексного числа"}, "IMSEC": {"a": "(компл_число)", "d": "Инженерная функция, возвращает секанс комплексного числа в текстовом формате a + bi или a + bj"}, "IMSECH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический секанс комплексного числа в текстовом формате a + bi или a + bj"}, "IMSIN": {"a": "(компл_число)", "d": "Инженерная функция, возвращает синус комплексного числа a + bi или a + bj"}, "IMSINH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический синус комплексного числа в текстовом формате a + bi или a + bj"}, "IMSQRT": {"a": "(компл_число)", "d": "Инженерная функция, возвращает значение квадратного корня из комплексного числа"}, "IMSUB": {"a": "(компл_число1;компл_число2)", "d": "Инженерная функция, возвращает разность двух комплексных чисел, представленных в формате a + bi или a + bj"}, "IMSUM": {"a": "(список_аргументов)", "d": "Инженерная функция, возвращает сумму двух комплексных чисел, представленных в формате a + bi или a + bj"}, "IMTAN": {"a": "(компл_число)", "d": "Инженерная функция, тангенс комплексного числа в текстовом формате a + bi или a + bj"}, "OCT2BIN": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует восьмеричное число в двоичное"}, "OCT2DEC": {"a": "(число)", "d": "Инженерная функция, преобразует восьмеричное число в десятичное"}, "OCT2HEX": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует восьмеричное число в шестнадцатеричное"}, "DAVERAGE": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, усредняет значения в поле (столбце) записей списка или базы данных, удовлетворяющие заданным условиям"}, "DCOUNT": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, подсчитывает количество ячеек в поле (столбце) записей списка или базы данных, которые содержат числа, удовлетворяющие заданным условиям"}, "DCOUNTA": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, подсчитывает непустые ячейки в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям"}, "DGET": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, извлекает из столбца списка или базы данных одно значение, удовлетворяющее заданным условиям"}, "DMAX": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, возвращает наибольшее число в поле (столбце) записей списка или базы данных, которое удовлетворяет заданным условиям"}, "DMIN": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, возвращает наименьшее число в поле (столбце) записей списка или базы данных, которое удовлетворяет заданным условиям"}, "DPRODUCT": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, перемножает значения в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям"}, "DSTDEV": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, оценивает стандартное отклонение на основе выборки из генеральной совокупности, используя числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям"}, "DSTDEVP": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, вычисляет стандартное отклонение генеральной совокупности, используя числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям"}, "DSUM": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, cуммирует числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям"}, "DVAR": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, оценивает дисперсию генеральной совокупности по выборке, используя отвечающие соответствующие заданным условиям числа в поле (столбце) записей списка или базы данных"}, "DVARP": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, вычисляет дисперсию генеральной совокупности, используя числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям"}, "CHAR": {"a": "(число)", "d": "Функция для работы с текстом и данными, возвращает символ ASCII, соответствующий заданному числовому коду"}, "CLEAN": {"a": "(текст)", "d": "Функция для работы с текстом и данными, используется для удаления всех непечатаемых символов из выбранной строки"}, "CODE": {"a": "(текст)", "d": "Функция для работы с текстом и данными, возвращает числовой код ASCII, соответствующий заданному символу или первому символу в ячейке"}, "CONCATENATE": {"a": "(текст1;текст2; ...)", "d": "Функция для работы с текстом и данными, используется для объединения данных из двух или более ячеек в одну"}, "CONCAT": {"a": "(текст1;текст2; ...)", "d": "Функция для работы с текстом и данными, используется для объединения данных из двух или более ячеек в одну. Эта функция заменяет функцию СЦЕПИТЬ"}, "DOLLAR": {"a": "(число;[число_знаков])", "d": "Функция для работы с текстом и данными, преобразует число в текст, используя денежный формат $#.##"}, "EXACT": {"a": "(текст1;текст2)", "d": "Функция для работы с текстом и данными, используется для сравнения данных в двух ячейках. Функция возвращает значение ИСТИНА, если данные совпадают, и ЛОЖЬ, если нет"}, "FIND": {"a": "(искомый_текст;просматриваемый_текст;[нач_позиция])", "d": "Функция для работы с текстом и данными, используется для поиска заданной подстроки (искомый_текст) внутри строки (просматриваемый_текст), предназначена для языков, использующих однобайтовую кодировку (SBCS)"}, "FINDB": {"a": "(искомый_текст;просматриваемый_текст;[нач_позиция])", "d": "Функция для работы с текстом и данными, используется для поиска заданной подстроки (искомый_текст) внутри строки (просматриваемый_текст), предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "FIXED": {"a": "(число;[чис<PERSON><PERSON>_знаков];[без_разделителей])", "d": "Функция для работы с текстом и данными, возвращает текстовое представление числа, округленного до заданного количества десятичных знаков"}, "LEFT": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с левого символа, предназначена для языков, использующих однобайтовую кодировку (SBCS)"}, "LEFTB": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с левого символа, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "LEN": {"a": "(текст)", "d": "Функция для работы с текстом и данными, анализирует заданную строку и возвращает количество символов, которые она содержит, предназначена для языков, использующих однобайтовую кодировку (SBCS)"}, "LENB": {"a": "(текст)", "d": "Функция для работы с текстом и данными, анализирует заданную строку и возвращает количество символов, которые она содержит, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "LOWER": {"a": "(текст)", "d": "Функция для работы с текстом и данными, используется для преобразования букв в выбранной ячейке из верхнего регистра в нижний"}, "MID": {"a": "(текст;начальная_позиция;число_знаков)", "d": "Функция для работы с текстом и данными, извлекает символы из заданной строки, начиная с любого места, предназначена для языков, использующих однобайтовую кодировку (SBCS)"}, "MIDB": {"a": "(текст;начальная_позиция;число_знаков)", "d": "Функция для работы с текстом и данными, извлекает символы из заданной строки, начиная с любого места, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "NUMBERVALUE": {"a": "(текст;[десятичный_разделитель];[разделитель_групп])", "d": "Функция для работы с текстом и данными, преобразует текст в числовое значение независимым от локали способом"}, "PROPER": {"a": "(текст)", "d": "Функция для работы с текстом и данными, преобразует первую букву каждого слова в прописную (верхний регистр), а все остальные буквы - в строчные (нижний регистр)"}, "REPLACE": {"a": "(стар_текст;начальная_позиция;число_знаков;нов_текст)", "d": "Функция для работы с текстом и данными, заменяет ряд символов на новый, с учетом заданного количества символов и начальной позиции, предназначена для языков, использующих однобайтовую кодировку (SBCS)"}, "REPLACEB": {"a": "(стар_текст;начальная_позиция;число_знаков;нов_текст)", "d": "Функция для работы с текстом и данными, заменяет ряд символов на новый, с учетом заданного количества символов и начальной позиции, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "REPT": {"a": "(текст;число_повторений)", "d": "Функция для работы с текстом и данными, используется для повторения данных в выбранной ячейке заданное количество раз"}, "RIGHT": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с крайнего правого символа, согласно заданному количеству символов, предназначена для языков, использующих однобайтовую кодировку (SBCS)"}, "RIGHTB": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с крайнего правого символа, согласно заданному количеству символов, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "SEARCH": {"a": "(искомый_текст;просматриваемый_текст;[начальная_позиция])", "d": "Функция для работы с текстом и данными, возвращает местоположение заданной подстроки в строке, предназначена для языков, использующих однобайтовую кодировку (SBCS)"}, "SEARCHB": {"a": "(искомый_текст;просматриваемый_текст;[начальная_позиция])", "d": "Функция для работы с текстом и данными, возвращает местоположение заданной подстроки в строке, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "SUBSTITUTE": {"a": "(текст;стар_текст;нов_текст;[номер_вхождения])", "d": "Функция для работы с текстом и данными, заменяет ряд символов на новый"}, "T": {"a": "(значение)", "d": "Функция для работы с текстом и данными, используется для проверки, является ли значение в ячейке (или используемое как аргумент) текстом или нет. Если это не текст, функция возвращает пустой результат. Если значение/аргумент является текстом, функция возвращает это же текстовое значение"}, "TEXT": {"a": "(значение;формат)", "d": "Функция для работы с текстом и данными, преобразует числовое значение в текст в заданном формате"}, "TEXTJOIN": {"a": "(разделитель;игнорировать_пустые;текст1;[текст2];… )", "d": "Функция для работы с текстом и данными, объединяет текст из нескольких диапазонов и (или) строк, вставляя между текстовыми значениями указанный разделитель; если в качестве разделителя используется пустая текстовая строка, функция эффективно объединит диапазоны"}, "TRIM": {"a": "(текст)", "d": "Функция для работы с текстом и данными, удаляет пробелы из начала и конца строки"}, "UNICHAR": {"a": "(число)", "d": "Функция для работы с текстом и данными, возвращает число (кодовую страницу), которая соответствует первому символу текста"}, "UNICODE": {"a": "(текст)", "d": "Функция для работы с текстом и данными, возвращает число (кодовую страницу), которая соответствует первому символу текста"}, "UPPER": {"a": "(текст)", "d": "Функция для работы с текстом и данными, используется для преобразования букв в выбранной ячейке из нижнего регистра в верхний"}, "VALUE": {"a": "(текст)", "d": "Функция для работы с текстом и данными, преобразует текстовое значение, представляющее число, в числовое значение. Если преобразуемый текст не является числом, функция возвращает ошибку #ЗНАЧ!"}, "AVEDEV": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает среднее абсолютных значений отклонений чисел от их среднего значения"}, "AVERAGE": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и вычисляет среднее значение"}, "AVERAGEA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных, включая текстовые и логические значения, и вычисляет среднее значение. Функция СРЗНАЧА интерпретирует текст и логическое значение ЛОЖЬ как числовое значение 0, а логическое значение ИСТИНА как числовое значение 1"}, "AVERAGEIF": {"a": "(диапазон;условия;[диапазон_усреднения])", "d": "Статистическая функция, анализирует диапазон данных и вычисляет среднее значение всех чисел в диапазоне ячеек, которые соответствуют заданному условию"}, "AVERAGEIFS": {"a": "(диапазон_усреднения;диапазон_условий1;условие1;[диапазон_условий2;условие2]; ... )", "d": "Статистическая функция, анализирует диапазон данных и вычисляет среднее значение всех чисел в диапазоне ячеек, которые соответствуют нескольким заданным условиям"}, "BETADIST": {"a": " (x;альфа;бета;[A];[B]) ", "d": "Статистическая функция, возвращает интегральную функцию плотности бета-вероятности"}, "BETAINV": {"a": " (вероятность;альфа;бета;[A];[B]) ", "d": "Статистическая функция, возвращает интегральную функцию плотности бета-вероятности"}, "BETA.DIST": {"a": " (x;альфа;бета;интегральная;[A];[B]) ", "d": "Статистическая функция, возвращает функцию бета-распределения"}, "BETA.INV": {"a": " (вероятность;альфа;бета;[A];[B]) ", "d": "Статистическая функция, возвращает обратную функцию к интегральной функции плотности бета-распределения вероятности "}, "BINOMDIST": {"a": "(число_успехов;число_испытаний;вероятность_успеха;интегральная)", "d": "Статистическая функция, возвращает отдельное значение вероятности биномиального распределения"}, "BINOM.DIST": {"a": "(число_успехов;число_испытаний;вероятность_успеха;интегральная)", "d": "Статистическая функция, возвращает отдельное значение биномиального распределения"}, "BINOM.DIST.RANGE": {"a": "(число_испытаний;вероятность_успеха;число_успехов;[число_успехов2])", "d": "Статистическая функция, возвращает вероятность результата испытаний при помощи биномиального распределения"}, "BINOM.INV": {"a": "(число_испытаний;вероятность_успеха;альфа)", "d": "Статистическая функция, возвращает наименьшее значение, для которого интегральное биномиальное распределение больше заданного значения критерия или равно ему"}, "CHIDIST": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает правостороннюю вероятность распределения хи-квадрат"}, "CHIINV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает значение, обратное правосторонней вероятности распределения хи-квадрат."}, "CHITEST": {"a": "(фактический_интервал;ожидаемый_интервал)", "d": "Статистическая функция, возвращает критерий независимости - значение статистики для распределения хи-квадрат (χ2) и соответствующее число степеней свободы"}, "CHISQ.DIST": {"a": "(x;степени_свободы;интегральная)", "d": "Статистическая функция, возвращает распределение хи-квадрат"}, "CHISQ.DIST.RT": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает правостороннюю вероятность распределения хи-квадрат"}, "CHISQ.INV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает значение, обратное левосторонней вероятности распределения хи-квадрат"}, "CHISQ.INV.RT": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает значение, обратное левосторонней вероятности распределения хи-квадрат"}, "CHISQ.TEST": {"a": "(фактический_интервал;ожидаемый_интервал)", "d": "Статистическая функция, возвращает критерий независимости - значение статистики для распределения хи-квадрат (χ2) и соответствующее число степеней свободы"}, "CONFIDENCE": {"a": "(альфа;стандартное_откл;размер)", "d": "Статистическая функция, возвращает доверительный интервал"}, "CONFIDENCE.NORM": {"a": "(альфа;стандартное_откл;размер)", "d": "Статистическая функция, возвращает доверительный интервал для среднего генеральной совокупности с нормальным распределением."}, "CONFIDENCE.T": {"a": "(альфа;стандартное_откл;размер)", "d": "Статистическая функция, возвращает доверительный интервал для среднего генеральной совокупности, используя распределение Стьюдента"}, "CORREL": {"a": "(массив1;массив2)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает коэффициент корреляции между двумя диапазонами ячеек"}, "COUNT": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для подсчета количества ячеек в выбранном диапазоне, содержащих числа, без учета пустых или содержащих текст ячеек"}, "COUNTA": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона ячеек и подсчета количества непустых ячеек"}, "COUNTBLANK": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона ячеек и возвращает количество пустых ячеек"}, "COUNTIFS": {"a": "(диапазон_условия1;условие1;[диапазон_условия2;условие2]; ... )", "d": "Статистическая функция, используется для подсчета количества ячеек выделенного диапазона, соответствующих нескольким заданным условиям"}, "COUNTIF": {"a": "(диапазон_ячеек;условие)", "d": "Статистическая функция, используется для подсчета количества ячеек выделенного диапазона, соответствующих заданному условию"}, "COVAR": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает ковариацию в двух диапазонах данных"}, "COVARIANCE.P": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает ковариацию совокупности, т. е. среднее произведений отклонений для каждой пары точек в двух наборах данных; ковариация используется для определения связи между двумя наборами данных"}, "COVARIANCE.S": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает ковариацию выборки, т. е. среднее произведений отклонений для каждой пары точек в двух наборах данных"}, "CRITBINOM": {"a": "(число_испытаний;вероятность_успеха;альфа)", "d": "Статистическая функция, возвращает наименьшее значение, для которого интегральное биномиальное распределение больше или равно заданному условию"}, "DEVSQ": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона ячеек и возвращает сумму квадратов отклонений чисел от их среднего значения"}, "EXPONDIST": {"a": "(x;лямбда;интегральная)", "d": "Статистическая функция, возвращает экспоненциальное распределение"}, "EXPON.DIST": {"a": "(x;лямбда;интегральная)", "d": "Статистическая функция, возвращает экспоненциальное распределение"}, "FDIST": {"a": "(x;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает правый хвост F-распределения вероятности для двух наборов данных. Эта функция позволяет определить, имеют ли два множества данных различные степени разброса результатов"}, "FINV": {"a": "(вероятность;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает значение, обратное (правостороннему) F-распределению вероятностей; F-распределение может использоваться в F-тесте, который сравнивает степени разброса двух множеств данных"}, "FTEST": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает результат F-теста; F-тест возвращает двустороннюю вероятность того, что разница между дисперсиями аргументов массив1 и массив2 несущественна; эта функция позволяет определить, имеют ли две выборки различные дисперсии"}, "F.DIST": {"a": "(x;степени_свободы1;степени_свободы2;интегральная)", "d": "Статистическая функция, возвращает F-распределение вероятности; эта функция позволяет определить, имеют ли два множества данных различные степени разброса результатов"}, "F.DIST.RT": {"a": "(x;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает правый хвост F-распределения вероятности для двух наборов данных; эта функция позволяет определить, имеют ли два множества данных различные степени разброса результатов"}, "F.INV": {"a": "(вероятность;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает значение, обратное F-распределению вероятности; F-распределение может использоваться в F-тесте, который сравнивает степени разброса двух наборов данных"}, "F.INV.RT": {"a": "(вероятность;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает значение, обратное F-распределению вероятности; F-распределение может использоваться в F-тесте, который сравнивает степени разброса двух наборов данных"}, "F.TEST": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает результат F-теста, двустороннюю вероятность того, что разница между дисперсиями аргументов массив1 и массив2 несущественна; эта функция позволяет определить, имеют ли две выборки различные дисперсии"}, "FISHER": {"a": "(число)", "d": "Статистическая функция, возвращает преобразование Фишера для числа"}, "FISHERINV": {"a": "(число)", "d": "Статистическая функция, выполняет обратное преобразование Фишера"}, "FORECAST": {"a": "(x;массив-1;массив-2)", "d": "Статистическая функция, предсказывает будущее значение на основе существующих значений"}, "FORECAST.ETS": {"a": "(целевая_дата;значения;временная_шкала;[сезонность];[заполнение_данных];[агрегирование])", "d": "Статистическая функция, рассчитывает или прогнозирует будущее значение на основе существующих (ретроспективных) данных с использованием версии AAA алгоритма экспоненциального сглаживания (ETS)"}, "FORECAST.ETS.CONFINT": {"a": "(целевая_дата;значения;временная_шкала;[вероятность];[сезонность];[заполнение_данных];[агрегирование])", "d": "Статистическая функция, возвращает доверительный интервал для прогнозной величины на указанную дату"}, "FORECAST.ETS.SEASONALITY": {"a": "(значения;временная_шкала;[заполнение_данных];[агрегирование])", "d": "Статистическая функция, возвращает длину повторяющегося фрагмента, обнаруженного приложением в заданном временном ряду"}, "FORECAST.ETS.STAT": {"a": "(значения;временная_шкала;тип_статистики;[сезонность];[заполнение_данных];[агрегирование])", "d": "Статистическая функция, возвращает статистическое значение, являющееся результатом прогнозирования временного ряда; тип статистики определяет, какая именно статистика используется этой функцией"}, "FORECAST.LINEAR": {"a": "(x;известные_значения_y;известные_значения_x)", "d": "Статистическая функция, вычисляет или предсказывает будущее значение по существующим значениям; предсказываемое значение — это значение y, соответствующее заданному значению x; значения x и y известны; новое значение предсказывается с использованием линейной регрессии"}, "FREQUENCY": {"a": "(массив_данных;массив_интервалов)", "d": "Статистическая функция, вычисляет частоту появления значений в выбранном диапазоне ячеек и отображает первое значение возвращаемого вертикального массива чисел"}, "GAMMA": {"a": "(число)", "d": "Статистическая функция, возвращает значение гамма-функции"}, "GAMMADIST": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает гамма-распределение"}, "GAMMA.DIST": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает гамма-распределение"}, "GAMMAINV": {"a": "(вероятность;альфа;бета)", "d": "Статистическая функция, возвращает значение, обратное гамма-распределению"}, "GAMMA.INV": {"a": "(вероятность;альфа;бета)", "d": "Статистическая функция, возвращает значение, обратное гамма-распределению"}, "GAMMALN": {"a": "(число)", "d": "Статистическая функция, возвращает натуральный логарифм гамма-функции"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Статистическая функция, возвращает натуральный логарифм гамма-функции"}, "GAUSS": {"a": "(z)", "d": "Статистическая функция, рассчитывает вероятность, с которой элемент стандартной нормальной совокупности находится в интервале между средним и стандартным отклонением z от среднего"}, "GEOMEAN": {"a": "(список_аргументов)", "d": "Статистическая функция, вычисляет среднее геометрическое для списка значений"}, "GROWTH": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Статистическая функция, рассчитывает прогнозируемый экспоненциальный рост на основе имеющихся данных; возвращает значения y для последовательности новых значений x, задаваемых с помощью существующих значений x и y"}, "HARMEAN": {"a": "(список_аргументов)", "d": "Статистическая функция, вычисляет среднее гармоническое для списка значений"}, "HYPGEOM.DIST": {"a": "(число_успехов_в_выборке;размер_выборки;число_успехов_в_совокупности;размер_совокупности;интегральная)", "d": "Статистическая функция, возвращает гипергеометрическое распределение, вероятность заданного количества успехов в выборке, если заданы размер выборки, количество успехов в генеральной совокупности и размер генеральной совокупности"}, "HYPGEOMDIST": {"a": "(число_успехов_в_выборке;размер_выборки;число_успехов_в_совокупности;размер_совокупности)", "d": "Статистическая функция, возвращает гипергеометрическое распределение, вероятность заданного количества успехов в выборке, если заданы размер выборки, количество успехов в генеральной совокупности и размер генеральной совокупности"}, "INTERCEPT": {"a": "(массив1;массив2)", "d": "Статистическая функция, анализирует значения первого и второго массивов для вычисления точки пересечения"}, "KURT": {"a": "(список_аргументов)", "d": "Статистическая функция, возвращает эксцесс списка значений"}, "LARGE": {"a": "(массив;k)", "d": "Статистическая функция, анализирует диапазон ячеек и возвращает k-ое по величине значение"}, "LINEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Статистическая функция, рассчитывает статистику для ряда с применением метода наименьших квадратов, чтобы вычислить прямую линию, которая наилучшим образом аппроксимирует имеющиеся данные и затем возвращает массив, который описывает полученную прямую; поскольку возвращается массив значений, функция должна задаваться в виде формулы массива"}, "LOGEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Статистическая функция, регрессионном анализе вычисляет экспоненциальную кривую, подходящую для данных и возвращает массив значений, описывающих кривую; поскольку данная функция возвращает массив значений, она должна вводиться как формула массива"}, "LOGINV": {"a": "(x;среднее;стандартное_отклонение)", "d": "Статистическая функция, возвращает обратное логарифмическое нормальное распределение для заданного значения x с указанными параметрами"}, "LOGNORM.DIST": {"a": "(x;среднее;стандартное_откл;интегральная)", "d": "Статистическая функция, возвращает логнормальное распределение для x, где ln(x) является нормально распределенным с параметрами Среднее и Стандартное отклонение; эта функция используется для анализа данных, которые были логарифмически преобразованы"}, "LOGNORM.INV": {"a": "(вероятность;среднее;станд_откл)", "d": "Статистическая функция, возвращает обратную функцию интегрального логнормального распределения x, где ln(x) имеет нормальное распределение с параметрами Среднее и Стандартное отклонение; логнормальное распределение применяется для анализа логарифмически преобразованных данных"}, "LOGNORMDIST": {"a": "(x;среднее;стандартное_откл)", "d": "Статистическая функция, анализирует логарифмически преобразованные данные и возвращает логарифмическое нормальное распределение для заданного значения x с указанными параметрами"}, "MAX": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наибольшего числа"}, "MAXA": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наибольшего значения"}, "MAXIFS": {"a": "(макс_диапазон;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ...)", "d": "Статистическая функция, возвращает максимальное значение из заданных определенными условиями или критериями ячеек."}, "MEDIAN": {"a": "(список_аргументов)", "d": "Статистическая функция, вычисляет медиану для списка значений"}, "MIN": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наименьшего числа"}, "MINA": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наименьшего значения"}, "MINIFS": {"a": "(мин_диапазон;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ...)", "d": "Статистическая функция, возвращает минимальное значение из заданных определенными условиями или критериями ячеек"}, "MODE": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает наиболее часто встречающееся значение"}, "MODE.MULT": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, возвращает вертикальный массив из наиболее часто встречающихся (повторяющихся) значений в массиве или диапазоне данных"}, "MODE.SNGL": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, возвращает наиболее часто встречающееся или повторяющееся значение в массиве или интервале данных"}, "NEGBINOM.DIST": {"a": "(число_неудач;число_успехов;вероятность_успеха;интегральная)", "d": "Статистическая функция, возвращает отрицательное биномиальное распределение — вероятность возникновения определенного числа неудач до указанного количества успехов при заданной вероятности успеха"}, "NEGBINOMDIST": {"a": "(число_неудач;число_успехов;вероятность_успеха)", "d": "Статистическая функция, возвращает отрицательное биномиальное распределение"}, "NORM.DIST": {"a": "(x;среднее;стандартное_откл;интегральная)", "d": "Статистическая функция, возвращает нормальную функцию распределения для указанного среднего и стандартного отклонения"}, "NORMDIST": {"a": "(x;среднее;стандартное_откл;интегральная)", "d": "Статистическая функция, возвращает нормальную функцию распределения для указанного среднего значения и стандартного отклонения"}, "NORM.INV": {"a": "(вероятность;среднее;стандартное_откл;)", "d": "Статистическая функция, возвращает обратное нормальное распределение для указанного среднего и стандартного отклонения"}, "NORMINV": {"a": "(x;среднее;стандартное_откл)", "d": "Статистическая функция, возвращает обратное нормальное распределение для указанного среднего значения и стандартного отклонения"}, "NORM.S.DIST": {"a": "(z;интегральная)", "d": "Статистическая функция, возвращает стандартное нормальное интегральное распределение; это распределение имеет среднее, равное нулю, и стандартное отклонение, равное единице."}, "NORMSDIST": {"a": "(число)", "d": "Статистическая функция, возвращает стандартное нормальное интегральное распределение"}, "NORM.S.INV": {"a": "(вероятность)", "d": "Статистическая функция, возвращает обратное значение стандартного нормального распределения; это распределение имеет среднее, равное нулю, и стандартное отклонение, равное единице"}, "NORMSINV": {"a": "(вероятность)", "d": "Статистическая функция, возвращает обратное значение стандартного нормального распределения"}, "PEARSON": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает коэффициент корреляции Пирсона"}, "PERCENTILE": {"a": "(массив;k)", "d": "Статистическая функция, анализирует диапазон данных и возвращает k-ю персентиль"}, "PERCENTILE.EXC": {"a": "(массив;k)", "d": "Статистическая функция, возвращает k-ю процентиль для значений диапазона, где k — число от 0 и 1 (не включая эти числа)"}, "PERCENTILE.INC": {"a": "(массив;k)", "d": "Статистическая функция, возвращает k-ю процентиль для значений диапазона, где k — число от 0 и 1 (включая эти числа)"}, "PERCENTRANK": {"a": "(массив;x;[разрядность])", "d": "Статистическая функция, возвращает категорию значения в наборе данных как процентное содержание в наборе данных"}, "PERCENTRANK.EXC": {"a": "(массив;x;[разрядность])", "d": "Статистическая функция, возвращает ранг значения в наборе данных как процентное содержание в наборе данных (от 0 до 1, не включая эти числа)"}, "PERCENTRANK.INC": {"a": "(массив;x;[разрядность])", "d": "Статистическая функция, возвращает ранг значения в наборе данных как процентное содержание в наборе данных (от 0 до 1, включая эти числа)"}, "PERMUT": {"a": "(число;число_выбранных)", "d": "Статистическая функция, возвращает количество перестановок для заданного числа элементов"}, "PERMUTATIONA": {"a": "(число;число_выбранных)", "d": "Статистическая функция, возвращает количество перестановок для заданного числа объектов (с повторами), которые можно выбрать из общего числа объектов"}, "PHI": {"a": "(x)", "d": "Статистическая функция, возвращает значение функции плотности для стандартного нормального распределения"}, "POISSON": {"a": "(x;среднее;интегральная)", "d": "Статистическая функция, возвращает распределение Пуассона"}, "POISSON.DIST": {"a": "(x;среднее;интегральная)", "d": "Статистическая функция, возвращает распределение Пуассона; обычное применение распределения Пуассона состоит в предсказании количества событий, происходящих за определенное время, например количества машин, появляющихся на площади за одну минуту"}, "PROB": {"a": "(x_интервал;интервал_вероятностей;[нижний_предел];[верхний_предел])", "d": "Статистическая функция, возвращает вероятность того, что значения из интервала находятся внутри нижнего и верхнего пределов"}, "QUARTILE": {"a": "(массив;часть)", "d": "Статистическая функция, анализирует диапазон данных и возвращает квартиль"}, "QUARTILE.INC": {"a": "(массив;часть)", "d": "Статистическая функция, возвращает квартиль набора данных на основе значений процентили от 0 до 1 (включительно)"}, "QUARTILE.EXC": {"a": "(массив;часть)", "d": "Статистическая функция, возвращает квартиль набора данных на основе значений процентили от 0 до 1, исключая эти числа"}, "RANK": {"a": "(число;ссылка;[порядок])", "d": "Статистическая функция, возвращает ранг числа в списке чисел; ранг числа — это его величина относительно других значений в списке; если отсортировать список, то ранг числа будет его позицией.)"}, "RANK.AVG": {"a": "(число;ссылка;[порядок])", "d": "Статистическая функция, возвращает ранг числа в списке чисел, то есть его величину относительно других значений в списке; если несколько значений имеют одинаковый ранг, возвращается среднее."}, "RANK.EQ": {"a": "(число;ссылка;[порядок])", "d": "Статистическая функция, возвращает ранг числа в списке чисел, то есть его величину относительно других значений в списке"}, "RSQ": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает квадрат коэффициента корреляции Пирсона"}, "SKEW": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает асимметрию распределения для списка значений"}, "SKEW.P": {"a": "(число 1;[число 2]; … )", "d": "Статистическая функция, возвращает асимметрию распределения на основе заполнения: характеристика степени асимметрии распределения относительно его среднего"}, "SLOPE": {"a": "(массив-1;массив-2)", "d": "Статистическая функция, возвращает наклон линии линейной регрессии для данных в двух массивах"}, "SMALL": {"a": "(массив;k)", "d": "Статистическая функция, анализирует диапазон данных и находит k-ое наименьшее значение"}, "STANDARDIZE": {"a": "(x;среднее;стандартное_откл)", "d": "Статистическая функция, возвращает нормализованное значение для распределения, характеризуемого заданными параметрами"}, "STDEV": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает стандартное отклонение по выборке, содержащей числа"}, "STDEV.P": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, вычисляет стандартное отклонение по генеральной совокупности, заданной аргументами. При этом логические значения и текст игнорируются"}, "STDEV.S": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, оценивает стандартное отклонение по выборке, логические значения и текст игнорируются"}, "STDEVA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает стандартное отклонение по выборке, содержащей числа, текст и логические значения (ИСТИНА или ЛОЖЬ). Текст и логические значения ЛОЖЬ интерпретируются как 0, а логические значения ИСТИНА - как 1"}, "STDEVP": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает стандартное отклонение по всей совокупности значений"}, "STDEVPA": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает стандартное отклонение по всей совокупности значений"}, "STEYX": {"a": "(известные_значения_y;известные_значения_x)", "d": "Статистическая функция, возвращает стандартную ошибку предсказанных значений Y для каждого значения X по регрессивной шкале"}, "TDIST": {"a": "(x;степени_свободы;хвосты)", "d": "Статистическая функция, возвращает процентные точки (вероятность) для t-распределения Стьюдента, где числовое значение (x) — вычисляемое значение t, для которого должны быть вычислены вероятности; T-распределение используется для проверки гипотез при малом объеме выборки"}, "TINV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает двустороннее обратное t-распределения Стьюдента"}, "T.DIST": {"a": "(x;степени_свободы;интегральная)", "d": "Статистическая функция, возвращает левостороннее t-распределение Стьюдента. T-распределение используется для проверки гипотез при малом объеме выборки. Данную функцию можно использовать вместо таблицы критических значений t-распределения"}, "T.DIST.2T": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает двустороннее t-распределение Стьюдента.T-распределение Стьюдента используется для проверки гипотез при малом объеме выборки. Данную функцию можно использовать вместо таблицы критических значений t-распределения"}, "T.DIST.RT": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает правостороннее t-распределение Стьюдента. T-распределение используется для проверки гипотез при малом объеме выборки. Данную функцию можно применять вместо таблицы критических значений t-распределения"}, "T.INV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает левостороннее обратное t-распределение Стьюдента."}, "T.INV.2T": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает двустороннее обратное t-распределение Стьюдента"}, "T.TEST": {"a": "(массив1;массив2;хвосты;тип)", "d": "Статистическая функция, возвращает вероятность, соответствующую t-тесту Стьюдента; функция СТЬЮДЕНТ.ТЕСТ позволяет определить вероятность того, что две выборки взяты из генеральных совокупностей, которые имеют одно и то же среднее"}, "TREND": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Статистическая функция, возвращает значения вдоль линейного тренда; он подмещается к прямой линии (с использованием метода наименьших квадратов) в known_y массива и known_x"}, "TRIMMEAN": {"a": "(массив;доля)", "d": "Статистическая функция, возвращает среднее внутренности множества данных. УРЕЗСРЕДНЕЕ вычисляет среднее, отбрасывания заданный процент данных с экстремальными значениями; можно использовать эту функцию, чтобы исключить из анализа выбросы"}, "TTEST": {"a": "(массив1;массив2;хвосты;тип)", "d": "Статистическая функция, возвращает вероятность, соответствующую критерию Стьюдента; функция ТТЕСТ позволяет определить, вероятность того, что две выборки взяты из генеральных совокупностей, которые имеют одно и то же среднее"}, "VAR": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по выборке, содержащей числа"}, "VAR.P": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, вычисляет дисперсию для генеральной совокупности. Логические значения и текст игнорируются"}, "VAR.S": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, оценивает дисперсию по выборке; логические значения и текст игнорируются"}, "VARA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по выборке"}, "VARP": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по всей совокупности значений"}, "VARPA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по всей совокупности значений"}, "WEIBULL": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает распределение Вейбулла; это распределение используется при анализе надежности, например для вычисления среднего времени наработки на отказ какого-либо устройства"}, "WEIBULL.DIST": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает распределение Вейбулла; это распределение используется при анализе надежности, например для вычисления среднего времени наработки на отказ какого-либо устройства"}, "Z.TEST": {"a": "(массив;x;[сигма])", "d": "Статистическая функция, возвращает одностороннее P-значение z-теста; для заданного гипотетического среднего генеральной совокупности функция Z.TEСT возвращает вероятность того, что среднее по выборке будет больше среднего значения набора рассмотренных данных (массива), то есть среднего значения наблюдаемой выборки"}, "ZTEST": {"a": "(массив;x;[сигма])", "d": "Статистическая функция, возвращает одностороннее значение вероятности z-теста; для заданного гипотетического среднего генеральной совокупности (μ0) возвращает вероятность того, что выборочное среднее будет больше среднего значения множества рассмотренных данных (массива), называемого также средним значением наблюдаемой выборки"}, "ACCRINT": {"a": "(дата_выпуска;первый_доход;дата_согл;ставка;[номинал];частота;[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценным бумагам с периодической выплатой процентов"}, "ACCRINTM": {"a": "(дата_выпуска;дата_согл;ставка;[номинал];[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценным бумагам, процент по которым уплачивается при наступлении срока погашения"}, "AMORDEGRC": {"a": "(стоимость;дата_приобр;первый_период;остаточная_стоимость;период;ставка;[базис])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества по каждому отчетному периоду методом дегрессивной амортизации"}, "AMORLINC": {"a": "(стоимость;дата_приобр;первый_период;остаточная_стоимость;период;ставка;[базис])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества по каждому отчетному периоду методом линейной амортизации"}, "COUPDAYBS": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества дней от начала действия купона до даты покупки ценной бумаги"}, "COUPDAYS": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества дней в периоде купона, содержащем дату покупки ценной бумаги"}, "COUPDAYSNC": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества дней от даты покупки ценной бумаги до следующей выплаты по купону"}, "COUPNCD": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления даты следующей выплаты по купону после даты покупки ценной бумаги"}, "COUPNUM": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества выплат процентов между датой покупки ценной бумаги и датой погашения"}, "COUPPCD": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления даты выплаты процентов, предшествующей дате покупки ценной бумаги"}, "CUMIPMT": {"a": "(ставка;кол_пер;нз;нач_период;кон_период;тип)", "d": "Финансовая функция, используется для вычисления общего размера процентых выплат по инвестиции между двумя периодами времени исходя из указанной процентной ставки и постоянной периодичности платежей"}, "CUMPRINC": {"a": "(ставка;кол_пер;нз;нач_период;кон_период;тип)", "d": "Финансовая функция, используется для вычисления общей суммы, выплачиваемой в погашение основного долга по инвестиции между двумя периодами времени исходя из указанной процентной ставки и постоянной периодичности платежей"}, "DB": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;период;[месяцы])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период методом фиксированного убывающего остатка"}, "DDB": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;период;[коэффициент])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период методом двойного убывающего остатка"}, "DISC": {"a": "(дата_согл;дата_вступл_в_силу;цена;погашение;[базис])", "d": "Финансовая функция, используется для вычисления ставки дисконтирования по ценной бумаге"}, "DOLLARDE": {"a": "(дроб_руб;дроб)", "d": "Финансовая функция, преобразует цену в рублях, представленную в виде дроби, в цену в рублях, выраженную десятичным числом"}, "DOLLARFR": {"a": "(дес_руб;дроб)", "d": "Финансовая функция, преобразует цену в рублях, представленную десятичным числом, в цену в рублях, выраженную в виде дроби"}, "DURATION": {"a": "(дата_согл;дата_вступл_в_силу;купон;доход;частота;[базис])", "d": "Финансовая функция, используется для вычисления продолжительности Маколея (взвешенного среднего срока погашения) для ценной бумаги с предполагаемой номинальной стоимостью 100 рублей"}, "EFFECT": {"a": "(номинальная_ставка;кол_пер)", "d": "Финансовая функция, используется для вычисления эффективной (фактической) годовой процентной ставки по ценной бумаге исходя из указанной номинальной годовой процентной ставки и количества периодов в году, за которые начисляются сложные проценты"}, "FV": {"a": "(ставка;кпер;плт;[пс];[тип])", "d": "Финансовая функция, вычисляет будущую стоимость инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей"}, "FVSCHEDULE": {"a": "(первичное;план)", "d": "Финансовая функция, используется для вычисления будущей стоимости инвестиций на основании ряда непостоянных процентных ставок"}, "INTRATE": {"a": "(дата_согл;дата_вступл_в_силу;инвестиция;погашение;[базис])", "d": "Финансовая функция, используется для вычисления ставки доходности по полностью обеспеченной ценной бумаге, проценты по которой уплачиваются только при наступлении срока погашения"}, "IPMT": {"a": "(ставка;период;кпер;пс;[бс];[тип])", "d": "Финансовая функция, используется для вычисления суммы платежей по процентам для инвестиции исходя из указанной процентной ставки и постоянной периодичности платежей"}, "IRR": {"a": "(значения;[предположения])", "d": "Финансовая функция, используется для вычисления внутренней ставки доходности по ряду периодических потоков денежных средств"}, "ISPMT": {"a": "(ставка;период;кпер;пс)", "d": "Финансовая функция, используется для вычисления процентов, выплачиваемых за определенный инвестиционный период, исходя из постоянной периодичности платежей"}, "MDURATION": {"a": "(дата_согл;дата_вступл_в_силу;купон;доход;частота;[базис])", "d": "Финансовая функция, используется для вычисления модифицированной продолжительности Маколея (взвешенного среднего срока погашения) для ценной бумаги с предполагаемой номинальной стоимостью 100 рублей"}, "MIRR": {"a": "(значения;ставка_финанс;ставка_реинвест)", "d": "Финансовая функция, используется для вычисления модифицированной внутренней ставки доходности по ряду периодических денежных потоков"}, "NOMINAL": {"a": "(эффект_ставка;кол_пер)", "d": "Финансовая функция, используется для вычисления номинальной годовой процентной ставки по ценной бумаге исходя из указанной эффективной (фактической) годовой процентной ставки и количества периодов в году, за которые начисляются сложные проценты"}, "NPER": {"a": "(ставка;плт;пс;[бс];[тип])", "d": "Финансовая функция, вычисляет количество периодов выплаты для инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей"}, "NPV": {"a": "(ставка;список аргументов)", "d": "Финансовая функция, вычисляет величину чистой приведенной стоимости инвестиции на основе заданной ставки дисконтирования"}, "ODDFPRICE": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;первый_купон;ставка;доход;погашение,частота;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги с периодической выплатой процентов в случае нерегулярной продолжительности первого периода выплаты процентов (больше или меньше остальных периодов)"}, "ODDFYIELD": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;первый_купон;ставка;цена;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценной бумаге с периодической выплатой процентов в случае нерегулярной продолжительности первого периода выплаты процентов (больше или меньше остальных периодов)"}, "ODDLPRICE": {"a": "(дата_согл;дата_вступл_в_силу;последняя_выплата;ставка;доход;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги с периодической выплатой процентов в случае нерегулярной продолжительности последнего периода выплаты процентов (больше или меньше остальных периодов)"}, "ODDLYIELD": {"a": "(дата_согл;дата_вступл_в_силу;последняя_выплата;ставка;цена;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценной бумаге с периодической выплатой процентов в случае нерегулярной продолжительности последнего периода выплаты процентов (больше или меньше остальных периодов)"}, "PDURATION": {"a": "(ставка;пс;бс)", "d": "Финансовая функция, возвращает количество периодов, которые необходимы инвестиции для достижения заданного значения"}, "PMT": {"a": "(ставка;кпер;пс;[бс];[тип])", "d": "Финансовая функция, вычисляет размер периодического платежа по ссуде исходя из заданной процентной ставки и постоянной периодичности платежей"}, "PPMT": {"a": "(ставка;период;кпер;пс;[бс];[тип])", "d": "Финансовая функция, используется для вычисления размера платежа в счет погашения основного долга по инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей"}, "PRICE": {"a": "(дата_согл;дата_вступл_в_силу;ставка;доход;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги с периодической выплатой процентов"}, "PRICEDISC": {"a": "(дата_согл;дата_вступл_в_силу;скидка;погашение;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги, на которую сделана скидка"}, "PRICEMAT": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;ставка;доход;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги, процент по которой уплачивается при наступлении срока погашения"}, "PV": {"a": "(ставка;кпер;плт;[бс];[тип])", "d": "Финансовая функция, вычисляет текущую стоимость инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей"}, "RATE": {"a": "(кпер;плт;пс;[бс];[тип];[прогноз])", "d": "Финансовая функция, используется для вычисления размера процентной ставки по инвестиции исходя из постоянной периодичности платежей"}, "RECEIVED": {"a": "(дата_согл;дата_вступл_в_силу;инвестиция;скидка;[базис])", "d": "Финансовая функция, используется для вычисления суммы, полученной за полностью обеспеченную ценную бумагу при наступлении срока погашения"}, "RRI": {"a": "(кпер;пс;бс)", "d": "Финансовая функция, возвращает эквивалентную процентную ставку для роста инвестиции"}, "SLN": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации)", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за один отчетный период линейным методом амортизационных отчислений"}, "SYD": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;период)", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период методом \"суммы годовых цифр\""}, "TBILLEQ": {"a": "(дата_согл;дата_вступл_в_силу;скидка)", "d": "Финансовая функция, используется для вычисления эквивалентной доходности по казначейскому векселю"}, "TBILLPRICE": {"a": "(дата_согл;дата_вступл_в_силу;скидка)", "d": "Финансовая функция, используется для вычисления цены на 100 рублей номинальной стоимости для казначейского векселя"}, "TBILLYIELD": {"a": "(дата_согл;дата_вступл_в_силу;цена)", "d": "Финансовая функция, используется для вычисления доходности по казначейскому векселю"}, "VDB": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;нач_период;кон_период;[коэффициент];[без_переключения])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период или его часть методом двойного уменьшения остатка или иным указанным методом"}, "XIRR": {"a": "(значения;даты[;предположение])", "d": "Финансовая функция, используется для вычисления внутренней ставки доходности по ряду нерегулярных денежных потоков"}, "XNPV": {"a": "(ставка;значения;даты)", "d": "Финансовая функция, используется для вычисления чистой приведенной стоимости инвестиции исходя из указанной процентной ставки и нерегулярных выплат"}, "YIELD": {"a": "(дата_согл;дата_вступл_в_силу;ставка;цена;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления доходности по ценной бумаге с периодической выплатой процентов"}, "YIELDDISC": {"a": "(дата_согл;дата_вступл_в_силу;цена;погашение;[базис])", "d": "Финансовая функция, используется для вычисления годовой доходности по ценной бумаге, на которую дается скидка"}, "YIELDMAT": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;ставка;цена;[базис])", "d": "Финансовая функция, используется для вычисления годовой доходности по ценным бумагам, процент по которым уплачивается при наступлении срока погашения"}, "ABS": {"a": "(x)", "d": "Математическая и тригонометрическая функция, используется для нахождения модуля (абсолютной величины) числа"}, "ACOS": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает арккосинус числа"}, "ACOSH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арккосинус числа"}, "ACOT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает главное значение арккотангенса, или обратного котангенса, числа"}, "ACOTH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арккотангенс числа"}, "AGGREGATE": {"a": "(номер_функции;параметры;ссылка1;[ссылка2];… )", "d": "Математическая и тригонометрическая функция, возвращает агрегатный результат вычислений по списку или базе данных; с помощью этой функции можно применять различные агрегатные функции к списку или базе данных с возможностью пропускать скрытые строки и значения ошибок"}, "ARABIC": {"a": "(x)", "d": "Математическая и тригонометрическая функция, преобразует римское число в арабское"}, "ASC": {"a": "(текст)", "d": "Текстовая функция, для языков с двухбайтовой кодировкой (DBCS) преобразует полноширинные (двухбайтовые) знаки в полуширинные (однобайтовые)"}, "ASIN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает арксинус числа"}, "ASINH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арксинус числа"}, "ATAN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает арктангенс числа"}, "ATAN2": {"a": "(x;y)", "d": "Математическая и тригонометрическая функция, возвращает арктангенс координат x и y"}, "ATANH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арктангенс числа"}, "BASE": {"a": "(число;основание;[минимальная_длина])", "d": "Преобразует число в текстовое представление с указанным основанием системы счисления"}, "CEILING": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число в большую сторону до ближайшего числа, кратного заданной значимости"}, "CEILING.MATH": {"a": "(x;[точность];[мода])", "d": "Математическая и тригонометрическая функция, округляет число до ближайшего целого или до ближайшего кратного заданной значимости"}, "CEILING.PRECISE": {"a": "(x;[точность])", "d": "Математическая и тригонометрическая функция, округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению"}, "COMBIN": {"a": "(число;число_выбранных)", "d": "Математическая и тригонометрическая функция, возвращает количество комбинаций для заданного числа элементов"}, "COMBINA": {"a": "(число;число_выбранных)", "d": "Математическая и тригонометрическая функция, возвращает количество комбинаций (с повторениями) для заданного числа элементов"}, "COS": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает косинус угла"}, "COSH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический косинус числа"}, "COT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает значение котангенса заданного угла в радианах"}, "COTH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический котангенс числа"}, "CSC": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает косеканс угла."}, "CSCH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический косеканс угла"}, "DECIMAL": {"a": "(текст;основание)", "d": "Преобразует текстовое представление числа с указанным основанием в десятичное число"}, "DEGREES": {"a": "(угол)", "d": "Математическая и тригонометрическая функция, преобразует радианы в градусы"}, "ECMA.CEILING": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число в большую сторону до ближайшего числа, кратного заданной значимости"}, "EVEN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число до ближайшего четного целого числа"}, "EXP": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает значение константы e, возведенной в заданную степень. Константа e равна 2,71828182845904"}, "FACT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает факториал числа"}, "FACTDOUBLE": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает двойной факториал числа"}, "FLOOR": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число в меньшую сторону до ближайшего числа, кратного заданной значимости"}, "FLOOR.PRECISE": {"a": "(x;[точность])", "d": "Математическая и тригонометрическая функция, возвращает число, округленное с недостатком до ближайшего целого или до ближайшего кратного разрядности"}, "FLOOR.MATH": {"a": "(x;[точность];[мода])", "d": "Математическая и тригонометрическая функция, округляет число в меньшую сторону до ближайшего целого или до ближайшего кратного указанному значению"}, "GCD": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает наибольший общий делитель для двух и более чисел"}, "INT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, анализирует и возвращает целую часть заданного числа"}, "ISO.CEILING": {"a": "(число;[точность])", "d": "Округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению вне зависимости от его знака; если в качестве точности указан нуль, возвращается нуль"}, "LCM": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает наименьшее общее кратное для одного или более чисел"}, "LN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает натуральный логарифм числа"}, "LOG": {"a": "(x;[основание])", "d": "Математическая и тригонометрическая функция, возвращает логарифм числа по заданному основанию"}, "LOG10": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает логарифм числа по основанию 10"}, "MDETERM": {"a": "(массив)", "d": "Математическая и тригонометрическая функция, возвращает определитель матрицы (матрица хранится в массиве)"}, "MINVERSE": {"a": "(массив)", "d": "Математическая и тригонометрическая функция, возвращает обратную матрицу для заданной матрицы и отображает первое значение возвращаемого массива чисел"}, "MMULT": {"a": "(массив1;массив2)", "d": "Математическая и тригонометрическая функция, возвращает матричное произведение двух массивов и отображает первое значение из возвращаемого массива чисел"}, "MOD": {"a": "(x;y)", "d": "Математическая и тригонометрическая функция, возвращает остаток от деления числа на заданный делитель"}, "MROUND": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число до кратного заданной значимости"}, "MULTINOMIAL": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает отношение факториала суммы значений к произведению факториалов"}, "MUNIT": {"a": "(размерность)", "d": "Математическая и тригонометрическая функция, возвращает матрицу единиц для указанного измерения"}, "ODD": {"a": "(x)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число до ближайшего нечетного целого числа"}, "PI": {"a": "()", "d": "Математическая и тригонометрическая функция, возвращает математическую константу пи, равную 3,14159265358979. Функция не требует аргумента"}, "POWER": {"a": "(x;y)", "d": "Математическая и тригонометрическая функция, возвращает результат возведения числа в заданную степень"}, "PRODUCT": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, перемножает все числа в заданном диапазоне ячеек и возвращает произведение"}, "QUOTIENT": {"a": "(числитель;знаменатель)", "d": "Математическая и тригонометрическая функция, возвращает целую часть результата деления с остатком"}, "RADIANS": {"a": "(угол)", "d": "Математическая и тригонометрическая функция, преобразует градусы в радианы"}, "RAND": {"a": "()", "d": "Математическая и тригонометрическая функция, возвращает случайное число, которое больше или равно 0 и меньше 1. Функция не требует аргумента"}, "RANDARRAY": {"a": "([строки];[столбцы];[минимум];[максимум];[целое_число])", "d": "Математическая и тригонометрическая функция, возвращает массив случайных чисел"}, "RANDBETWEEN": {"a": "(нижн_граница;верхн_граница)", "d": "Математическая и тригонометрическая функция, возвращает случайное число, большее или равное значению аргумента нижн_граница и меньшее или равное значению аргумента верхн_граница"}, "ROMAN": {"a": "(число;[форма])", "d": "Математическая и тригонометрическая функция, преобразует число в римское"}, "ROUND": {"a": "(x;число_разрядов)", "d": "Математическая и тригонометрическая функция, округляет число до заданного количества десятичных разрядов"}, "ROUNDDOWN": {"a": "(x;число_разрядов)", "d": "Математическая и тригонометрическая функция, округляет число в меньшую сторону до заданного количества десятичных разрядов"}, "ROUNDUP": {"a": "(x;число_разрядов)", "d": "Математическая и тригонометрическая функция, округляет число в большую сторону до заданного количества десятичных разрядов"}, "SEC": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает секанс угла"}, "SECH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический секанс угла"}, "SERIESSUM": {"a": "(переменная;показатель_степени;шаг;коэффициенты)", "d": "Математическая и тригонометрическая функция, возвращает сумму степенного ряда"}, "SIGN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, определяет знак числа. Если число положительное, функция возвращает значение 1. Если число отрицательное, функция возвращает значение -1. Если число равно 0, функция возвращает значение 0"}, "SIN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает синус угла"}, "SINH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический синус числа"}, "SQRT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает квадратный корень числа"}, "SQRTPI": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает квадратный корень от результата умножения константы пи (3,14159265358979) на заданное число"}, "SUBTOTAL": {"a": "(номер_функции;список_аргументов)", "d": "Возвращает промежуточный итог в список или базу данных"}, "SUM": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает результат сложения всех чисел в выбранном диапазоне ячеек"}, "SUMIF": {"a": "(диапазон;условие;[диапазон_суммирования])", "d": "Математическая и тригонометрическая функция, суммирует все числа в выбранном диапазоне ячеек в соответствии с заданным условием и возвращает результат"}, "SUMIFS": {"a": "(диапазон_суммирования;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ... )", "d": "Математическая и тригонометрическая функция, суммирует все числа в выбранном диапазоне ячеек в соответствии с несколькими условиями и возвращает результат"}, "SUMPRODUCT": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, перемножает соответствующие элементы заданных диапазонов ячеек или массивов и возвращает сумму произведений"}, "SUMSQ": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, вычисляет сумму квадратов чисел и возвращает результат"}, "SUMX2MY2": {"a": "(массив-1;массив-2)", "d": "Математическая и тригонометрическая функция, вычисляет сумму разностей квадратов соответствующих элементов в двух массивах"}, "SUMX2PY2": {"a": "(массив-1;массив-2)", "d": "Математическая и тригонометрическая функция, вычисляет суммы квадратов соответствующих элементов в двух массивах и возвращает сумму полученных результатов"}, "SUMXMY2": {"a": "(массив-1;массив-2)", "d": "Математическая и тригонометрическая функция, возвращает сумму квадратов разностей соответствующих элементов в двух массивах"}, "TAN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает тангенс угла"}, "TANH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический тангенс числа"}, "TRUNC": {"a": "(x;[число_разрядов])", "d": "Математическая и тригонометрическая функция, возвращает число, усеченное до заданного количества десятичных разрядов"}, "ADDRESS": {"a": "(номер_строки;номер_столбца;[тип_ссылки];[a1];[имя_листа])", "d": "Поисковая функция, возвращает адрес ячейки, представленный в виде текста"}, "CHOOSE": {"a": "(номер_индекса;список_аргументов)", "d": "Поисковая функция, возвращает значение из списка значений по заданному индексу (позиции)"}, "COLUMN": {"a": "([ссылка])", "d": "Поисковая функция, возвращает номер столбца ячейки"}, "COLUMNS": {"a": "(массив)", "d": "Поисковая функция, возвращает количество столбцов в ссылке на ячейки"}, "FORMULATEXT": {"a": "(ссылка)", "d": "Поисковая функция, возвращает формулу в виде строки"}, "HLOOKUP": {"a": "(искомое_значение;таблица;номер_строки;[интервальный_просмотр])", "d": "Поисковая функция, используется для выполнения горизонтального поиска значения в верхней строке таблицы или массива и возвращает значение, которое находится в том же самом столбце в строке с заданным номером"}, "HYPERLINK": {"a": "(адрес;[имя])", "d": "Поисковая функция, создает ярлык, который позволяет перейти к другому месту в текущей книге или открыть документ, расположенный на сетевом сервере, в интрасети или в Интернете"}, "INDEX": {"a": "(массив;номер_строки;[номер_столбца]) ИНДЕКС(ссылка;номер_строки;[номер_столбца];[номер_области])", "d": "Поисковая функция, возвращает значение в диапазоне ячеек на основании заданных номера строки и номера столбца. Существуют две формы функции ИНДЕКС"}, "INDIRECT": {"a": "(ссылка_на_текст;[a1])", "d": "Поисковая функция, возвращает ссылку на ячейку, указанную с помощью текстовой строки"}, "LOOKUP": {"a": "(искомое_значение;просматриваемый_вектор;[вектор_результатов])", "d": "Поисковая функция, возвращает значение из выбранного диапазона (строки или столбца с данными, отсортированными в порядке возрастания)"}, "MATCH": {"a": "(искомое_значение;просматриваемый_массив;[тип_сопоставления])", "d": "Поисковая функция, возвращает относительное положение заданного элемента в диапазоне ячеек"}, "OFFSET": {"a": "(ссылка;смещ_по_строкам;смещ_по_столбцам;[высота];[ширина])", "d": "Поисковая функция, возвращает ссылку на ячейку, отстоящую от заданной ячейки (или верхней левой ячейки в диапазоне ячеек) на определенное число строк и столбцов"}, "ROW": {"a": "([ссылка])", "d": "Поисковая функция, возвращает номер строки для ссылки на ячейку"}, "ROWS": {"a": "(массив)", "d": "Поисковая функция, возвращает количество строк в ссылке на ячейки"}, "TRANSPOSE": {"a": "(массив)", "d": "Поисковая функция, возвращает первый элемент массива"}, "UNIQUE": {"a": "(массив; [by_col]; [exactly_once])", "d": "Поисковая функция, возвращает список уникальных значений в списке или диапазоне"}, "VLOOKUP": {"a": "(искомое_значение;таблица;номер_столбца;[интервальный_просмотр])", "d": "Поисковая функция, используется для выполнения вертикального поиска значения в крайнем левом столбце таблицы или массива и возвращает значение, которое находится в той же самой строке в столбце с заданным номером"}, "XLOOKUP": {"a": "(искомое_значение; просматриваемый_массив; возращаемый_массив; [если_ничего_не_найдено]; [режим_сопоставления]; [режим_поиска])", "d": "Поисковая функция, выполняет поиск в диапазоне или массиве и возвращает элемент, соответствующий первому совпадению. Если совпадения не существуют, то она может вернуть наиболее близкое (приблизительное) совпадение"}, "CELL": {"a": "(info_type, [reference])", "d": "Информационная функция, возвращает сведения о форматировании, расположении или содержимом ячейки"}, "ERROR.TYPE": {"a": "(значение)", "d": "Информационная функция, возвращает числовое представление одной из существующих ошибок"}, "ISBLANK": {"a": "(значение)", "d": "Информационная функция, проверяет, является ли ячейка пустой. Если ячейка пуста, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "ISERR": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие значения ошибки. Если ячейка содержит значение ошибки (кроме #Н/Д), функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "ISERROR": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие значения ошибки. Если ячейка содержит одно из следующих значений ошибки: #Н/Д, #ЗНАЧ!, #ССЫЛКА!, #ДЕЛ/0!, #ЧИСЛО!, #ИМЯ? или #ПУСТО!, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "ISEVEN": {"a": "(число)", "d": "Информационная функция, используется для проверки на наличие четного числа. Если ячейка содержит четное число, функция возвращает значение ИСТИНА. Если число является нечетным, она возвращает значение ЛОЖЬ"}, "ISFORMULA": {"a": "(значение)", "d": "Информационная функция, проверяет, имеется ли ссылка на ячейку с формулой, и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISLOGICAL": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие логического значения (ИСТИНА или ЛОЖЬ). Если ячейка содержит логическое значение, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "ISNA": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие ошибки #Н/Д. Если ячейка содержит значение ошибки #Н/Д, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "ISNONTEXT": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие значения, которое не является текстом. Если ячейка не содержит текстового значения, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "ISNUMBER": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие числового значения. Если ячейка содержит числовое значение, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "ISODD": {"a": "(число)", "d": "Информационная функция, используется для проверки на наличие нечетного числа. Если ячейка содержит нечетное число, функция возвращает значение ИСТИНА. Если число является четным, она возвращает значение ЛОЖЬ"}, "ISREF": {"a": "(значение)", "d": "Информационная функция, используется для проверки, является ли значение допустимой ссылкой на другую ячейку"}, "ISTEXT": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие текстового значения. Если ячейка содержит текстовое значение, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ"}, "N": {"a": "(значение)", "d": "Информационная функция, преобразует значение в число"}, "NA": {"a": "()", "d": "Информационная функция, возвращает значение ошибки #Н/Д. Эта функция не требует аргумента"}, "SHEET": {"a": "(значение)", "d": "Информационная функция, возвращает номер листа, на который имеется ссылка"}, "SHEETS": {"a": "(ссылка)", "d": "Информационная функция, возвращает количество листов в ссылке"}, "TYPE": {"a": "(значение)", "d": "Информационная функция, используется для определения типа результирующего или отображаемого значения"}, "AND": {"a": "(логическое_значение1;[логическое_значение2]; ...)", "d": "Логическая функция, используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ИСТИНА, если все аргументы имеют значение ИСТИНА"}, "FALSE": {"a": "()", "d": "Логическая функция, возвращает значение ЛОЖЬ и не требует аргумента"}, "IF": {"a": "(лог_выражение;значение_если_истина;[значение_если_ложь])", "d": "Логическая функция, используется для проверки логического выражения и возвращает одно значение, если проверяемое условие имеет значение ИСТИНА, и другое, если оно имеет значение ЛОЖЬ"}, "IFS": {"a": "(условие1;значение1;[условие2;значение2]; … )", "d": "Логическая функция, проверяет соответствие одному или нескольким условиям и возвращает значение для первого условия, принимающего значение ИСТИНА"}, "IFERROR": {"a": "(значение;значение_если_ошибка)", "d": "Логическая функция, используется для проверки формулы на наличие ошибок в первом аргументе. Функция возвращает результат формулы, если ошибки нет, или определенное значение, если она есть"}, "IFNA": {"a": "(значение;значение_при_ошибке)", "d": "Логическая функция, возвращает указанное вами значение, если формула возвращает значение ошибки #Н/Д; в ином случае возвращает результат формулы."}, "NOT": {"a": "(логическое_значение)", "d": "Логическая функция, используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ИСТИНА, если аргумент имеет значение ЛОЖЬ, и ЛОЖЬ, если аргумент имеет значение ИСТИНА"}, "OR": {"a": "(логическое_значение1;[логическое значение2]; ... )", "d": "Логическая функция, используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ЛОЖЬ, если все аргументы имеют значение ЛОЖЬ"}, "SWITCH": {"a": "(выражение;значение1;результат1;[по_умолчанию или значение2;результат2];…[по_умолчанию или значение3;результат3])", "d": "Логическая функция, вычисляет значение (которое называют выражением) на основе списка значений и возвращает результат, соответствующий первому совпадающему значению; если совпадения не обнаружены, может быть возвращено необязательное стандартное значение"}, "TRUE": {"a": "()", "d": "Логическая функция, возвращает значение ИСТИНА и не требует аргумента"}, "XOR": {"a": "(логическое_значение1;[логическое значение2]; ... )", "d": "Логическая функция, возвращает логическое исключающее ИЛИ всех аргументов"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст перед символами-разделителями."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст после символов-разделителей."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Разбивает текст на строки или столбцы с помощью разделителей."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Вертикально собирает массивы в один массив."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Горизонтально собирает массивы в один массив."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Возвращает строки из массива или ссылки."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Возвращает столбцы из массива или ссылки."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Возвращает массив в виде одного столбца."}, "TOROW": {"a": "(мас<PERSON>ив, [игнорировать], [сканировать_по_столбцам])", "d": "Возвращает массив в виде одной строки."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Возвращает строки или столбцы из начала или конца массива."}, "DROP": {"a": "(array, rows, [columns])", "d": "Удаляет строки или столбцы из начала или конца массива."}}