﻿<!DOCTYPE html>
<html>
	<head>
		<title>Manipulate objects on a slide</title>
		<meta charset="utf-8" />
		<meta name="description" content="Move, rotate, resize and reshape autoshapes, images, charts." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Manipulate objects on a slide</h1>
			<p>You can resize, move, rotate different objects on a slide manually using the special handles. You can also specify the dimensions and position of some objects exactly using the right sidebar or <b>Advanced Settings</b> window.</p>			
            <p class="note">
                <b>Note</b>: the list of keyboard shortcuts that can be used when working with objects is available <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">here</a>.
            </p>
            <h3>Resize objects</h3>
			<p>To change the <b>autoshape/image/chart/table/text box</b> size, drag small squares <span class="icon icon-resize_square"></span> situated on the object edges. To maintain the original proportions of the selected object while resizing, hold down the <b>Shift</b> key and drag one of the corner icons.</p>
			<p><span class="big big-maintain_proportions"></span></p>
			<p>To specify the precise width and height of a <b>chart</b>, select it on a slide and use the <b>Size</b> section of the right sidebar that will be activated.</p>
			<p>To specify the precise dimensions of an <b>image</b> or <b>autoshape</b>, right-click the necessary object on the slide and select the <b>Image/Shape Advanced Settings</b> option from the menu. Specify necessary values on the <b>Size</b> tab of the <b>Advanced Settings</b> window and press <b>OK</b>.</p>
			<h3>Reshape autoshapes</h3>
			<p>When modifying some shapes, for example Figured arrows or Callouts, the yellow diamond-shaped <span class="icon icon-yellowdiamond"></span> icon is also available. It allows to adjust some aspects of the shape, for example, the length of the head of an arrow.</p>
			<p><span class="big big-reshaping"></span></p>
			<h3>Move objects</h3>
			<p>To alter the <b>autoshape/image/chart/table/text box</b> position, use the <span class="icon icon-arrow"></span> icon that appears after hovering your mouse cursor over the object. Drag the object to the necessary position without releasing the mouse button. 
			To move the object by the one-pixel increments, hold down the <b>Ctrl</b> key and use the keybord arrows. 
			To move the object strictly horizontally/vertically and prevent it from moving in a perpendicular direction, hold down the <b>Shift</b> key when dragging.</p>
			<p>To specify the precise position of an <b>image</b>, right-click it on a slide and select the <b>Image Advanced Settings</b> option from the menu. Specify necessary values in the <b>Position</b> section of the <b>Advanced Settings</b> window and press <b>OK</b>.</p>
			<h3>Rotate objects</h3>
			<p>To manually rotate an <b>autoshape/image/text box</b>, hover the mouse cursor over the rotation handle <span class="icon icon-greencircle"></span> and drag it clockwise or counterclockwise. To constrain the rotation angle to 15 degree increments, hold down the <b>Shift</b> key while rotating.</p>
            <p>To rotate the object by 90 degrees counterclockwise/clockwise or flip the object horizontally/vertically you can use the <b>Rotation</b> section of the right sidebar that will be activated once you select the necessary object. To open it, click the <b>Shape settings</b> <span class="icon icon-shape_settings_icon"></span>  or the <b>Image settings</b> <span class="icon icon-image_settings_icon"></span> icon to the right. Click one of the buttons:</p>
            <ul>
                <li><div class = "icon icon-rotatecounterclockwise"></div> to rotate the object by 90 degrees counterclockwise</li>
                <li><div class = "icon icon-rotateclockwise"></div> to rotate the object by 90 degrees clockwise</li>
                <li><div class = "icon icon-fliplefttoright"></div> to flip the object horizontally (left to right)</li>
                <li><div class = "icon icon-flipupsidedown"></div> to flip the object vertically (upside down)</li>
            </ul>
            <p>It's also possible to right-click the object, choose the <b>Rotate</b> option from the contextual menu and then use one of the available rotation options.</p>
            <p>To rotate the object by an exactly specified angle, click the <b>Show advanced settings</b> link at the right sidebar and use the <b>Rotation</b> tab of the <b>Advanced Settings</b> window. Specify the necessary value measured in degrees in the <b>Angle</b> field and click <b>OK</b>.</p>
			
		</div>
	</body>
</html>