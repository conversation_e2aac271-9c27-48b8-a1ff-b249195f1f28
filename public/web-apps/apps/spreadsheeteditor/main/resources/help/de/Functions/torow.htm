﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZUZEILE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>ZUZEILE-Funktion</h1>
			<p>Die <b>ZUZEILE</b>-Funktion ist eine der Suchen- und Bezügefunktionen. Sie wird verwendet, um das Array als eine Zeile zurückzugeben.</p>
			<p class="note">Bitte beachten Sie, dass dies eine Matrixformel ist. Um mehr zu erfahren, lesen Sie bitte den Artikel <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Matrixformeln einfügen</a>.</p>
			<p>Die Formelsyntax der Funktion <b>ZUZEILE</b> ist:</p>
			<p style="text-indent: 150px;"><b><em>ZUZEILE (array, [ignore], [scan_by_column])</em></b></p>
			<p><em>dabei gilt</em></p>
			<p style="text-indent: 50px;"><b><em>array</em></b> ist das Array oder die Referenz, die als Zeile zurückgegeben werden soll.</p>
			<p style="text-indent: 50px;"><b><em>ignore</em></b> wird verwendet, um festzulegen, ob bestimmte Arten von Werten ignoriert werden sollen. Standardmäßig werden keine Werte ignoriert. Die folgenden Werte werden verwendet: <b>0</b>, um alle Werte beizubehalten (Standard); <b>1</b>, um Leerzeichen zu ignorieren; <b>2</b>, um Fehler zu ignorieren; <b>3</b>, um Leerzeichen und Fehler zu ignorieren.</p>
			<p style="text-indent: 50px;"><b><em>scan_by_column</em></b> wird verwendet, um das Array nach Spalte zu scannen. Standardmäßig wird das Array-Scannen zeilenweise durchgeführt. Das Scannen bestimmt, ob die Werte in Zeilen oder Spalten angeordnet sind.</p>
			<p>Anwendung der Funktion <b>ZUZEILE</b>:</p>
			<ol>
				<li>Wählen Sie die Zelle aus, in der Sie das Ergebnis anzeigen möchten,</li>
				<li>
					Klicken Sie auf das Symbol <b>Funktion</b> <div class="icon icon-insertfunction"></div> in der oberen Symbolleiste
					<br />oder klicken Sie mit der rechten Maustaste in eine ausgewählte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü
					<br />oder klicken Sie auf das Symbol <div class="icon icon-function"></div> in der Bearbeitungsleiste.
				</li>
				<li>Wählen Sie aus der Liste die Funktionsgruppe <b>Suchen und Bezüge</b>.</li>
				<li>Klicken Sie auf die Funktion <b>ZUZEILE</b>.</li>
				<li>Geben Sie die erforderlichen Argumente ein, indem Sie sie durch Kommas trennen.</li>
				<li>Drücken Sie die <b>Eingabe</b>-Taste</li>
			</ol>
			<p>Das Ergebnis wird in der ausgewählten Zelle angezeigt.</p>
			<!--<p style="text-indent: 150px;"><img alt="TOROW Function" src="../images/torow.png" /></p>-->
		</div>
	</body>
</html>