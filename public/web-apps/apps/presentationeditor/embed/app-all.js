/*!
 * Copyright (c) Ascensio System SIA 2025. All rights reserved
 *
 * http://www.onlyoffice.com 
 *
 * Version: 4.3.0 (build:791)
 */

if(void 0===Common)var Common={};if(Common.Locale=new function(){"use strict";var l10n=null,loadcallback,apply=!1,defLang="{{DEFAULT_LANG}}",currentLang=defLang,_4letterLangs=["pt-pt","zh-tw"],_applyLocalization=function(e){try{if(e&&(loadcallback=e),l10n){for(var t in l10n){var o=t.split(".");if(o&&o.length>2){for(var n=window,r=0;r<o.length-1;++r)void 0===n[o[r]]&&(n[o[r]]=new Object),n=n[o[r]];n&&(n[o[o.length-1]]=l10n[t])}}loadcallback&&loadcallback()}else apply=!0}catch(e){}},_get=function(prop,scope){var res="";return l10n&&scope&&scope.name&&(res=l10n[scope.name+"."+prop],!res&&scope.default&&(res=scope.default)),res||(scope?eval(scope.name).prototype[prop]:"")},_getCurrentLanguage=function(){return currentLang},_getDefaultLanguage=function(){return defLang},_getLoadedLanguage=function(){return loadedLang},_getUrlParameterByName=function(e){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(location.search);return null==t?"":decodeURIComponent(t[1].replace(/\+/g," "))},_requireLang=function(e){"string"!=typeof e&&(e=null);var t=e||_getUrlParameterByName("lang")||defLang,o=_4letterLangs.indexOf(t.replace("_","-").toLowerCase());t=o<0?t.split(/[\-_]/)[0]:_4letterLangs[o],currentLang=t,fetch("locale/"+t+".json").then((function(e){if(!e.ok){if(o>=0)throw new Error("4letters error");if(currentLang=defLang,t!=defLang)return fetch("locale/"+defLang+".json");throw new Error("server error")}return e.json()})).then((function(e){if(e.json){if(!e.ok)throw new Error("server error");return e.json()}throw l10n=e,new Error("loaded")})).then((function(e){l10n=e||{},apply&&_applyLocalization()})).catch((function(e){return/4letters/.test(e)?setTimeout((function(){_requireLang(t.split(/[\-_]/)[0])}),0):!/loaded/.test(e)&&currentLang!=defLang&&defLang&&defLang.length<3?setTimeout((function(){_requireLang(defLang)}),0):(l10n=l10n||{},apply&&_applyLocalization(),void("loaded"==e.message||(currentLang=null,console.log("fetch error: "+e))))}))};if(window.fetch)_requireLang();else{var polyfills=["../vendor/fetch/fetch.umd"];window.Promise?require(polyfills,_requireLang):require(["../vendor/es6-promise/es6-promise.auto.min"],(function(){require(polyfills,_requireLang)}))}return{apply:_applyLocalization,get:_get,getCurrentLanguage:_getCurrentLanguage,getDefaultLanguage:_getDefaultLanguage}},void 0===window.Common&&(window.Common={}),Common.Gateway=new function(){var e=this,t=$(e),o={init:function(e){t.trigger("init",e)},openDocument:function(e){t.trigger("opendocument",e)},showMessage:function(e){t.trigger("showmessage",e)},applyEditRights:function(e){t.trigger("applyeditrights",e)},processSaveResult:function(e){t.trigger("processsaveresult",e)},processRightsChange:function(e){t.trigger("processrightschange",e)},refreshHistory:function(e){t.trigger("refreshhistory",e)},setHistoryData:function(e){t.trigger("sethistorydata",e)},setEmailAddresses:function(e){t.trigger("setemailaddresses",e)},setActionLink:function(e){t.trigger("setactionlink",e.url)},processMailMerge:function(e){t.trigger("processmailmerge",e)},downloadAs:function(e){t.trigger("downloadas",e)},processMouse:function(e){t.trigger("processmouse",e)},internalCommand:function(e){t.trigger("internalcommand",e)},resetFocus:function(e){t.trigger("resetfocus",e)},setUsers:function(e){t.trigger("setusers",e)},showSharingSettings:function(e){t.trigger("showsharingsettings",e)},setSharingSettings:function(e){t.trigger("setsharingsettings",e)},insertImage:function(e){t.trigger("insertimage",e)},setMailMergeRecipients:function(e){t.trigger("setmailmergerecipients",e)},setRevisedFile:function(e){t.trigger("setrevisedfile",e)},setFavorite:function(e){t.trigger("setfavorite",e)},requestClose:function(e){t.trigger("requestclose",e)},blurFocus:function(e){t.trigger("blurfocus",e)},grabFocus:function(e){t.trigger("grabfocus",e)},setReferenceData:function(e){t.trigger("setreferencedata",e)}},n=function(e){window.parent&&window.JSON&&(e.frameEditorId=window.frameEditorId,window.parent.postMessage(window.JSON.stringify(e),"*"))},r=function(e){!function(e){if(e.origin===window.parentOrigin||e.origin===window.location.origin||"null"===e.origin&&("file://"===window.parentOrigin||"file://"===window.location.origin)){var t=e.data;if("[object String]"===Object.prototype.toString.apply(t)&&window.JSON){var n,r;try{n=window.JSON.parse(t)}catch(e){n=""}n&&(r=o[n.command])&&r.call(this,n.data)}}}(e)};return window.attachEvent?window.attachEvent("onmessage",r):window.addEventListener("message",r,!1),{appReady:function(){n({event:"onAppReady"})},requestEditRights:function(){n({event:"onRequestEditRights"})},requestHistory:function(){n({event:"onRequestHistory"})},requestHistoryData:function(e){n({event:"onRequestHistoryData",data:e})},requestRestore:function(e,t,o){n({event:"onRequestRestore",data:{version:e,url:t,fileType:o}})},requestEmailAddresses:function(){n({event:"onRequestEmailAddresses"})},requestStartMailMerge:function(){n({event:"onRequestStartMailMerge"})},requestHistoryClose:function(e){n({event:"onRequestHistoryClose"})},reportError:function(e,t){n({event:"onError",data:{errorCode:e,errorDescription:t}})},reportWarning:function(e,t){n({event:"onWarning",data:{warningCode:e,warningDescription:t}})},sendInfo:function(e){n({event:"onInfo",data:e})},setDocumentModified:function(e){n({event:"onDocumentStateChange",data:e})},internalMessage:function(e,t){n({event:"onInternalMessage",data:{type:e,data:t}})},updateVersion:function(){n({event:"onOutdatedVersion"})},downloadAs:function(e,t){n({event:"onDownloadAs",data:{url:e,fileType:t}})},requestSaveAs:function(e,t,o){n({event:"onRequestSaveAs",data:{url:e,title:t,fileType:o}})},collaborativeChanges:function(){n({event:"onCollaborativeChanges"})},requestRename:function(e){n({event:"onRequestRename",data:e})},metaChange:function(e){n({event:"onMetaChange",data:e})},documentReady:function(){n({event:"onDocumentReady"})},requestClose:function(){n({event:"onRequestClose"})},requestMakeActionLink:function(e){n({event:"onMakeActionLink",data:e})},requestUsers:function(){n({event:"onRequestUsers"})},requestSendNotify:function(e){n({event:"onRequestSendNotify",data:e})},requestInsertImage:function(e){n({event:"onRequestInsertImage",data:{c:e}})},requestMailMergeRecipients:function(){n({event:"onRequestMailMergeRecipients"})},requestCompareFile:function(){n({event:"onRequestCompareFile"})},requestSharingSettings:function(){n({event:"onRequestSharingSettings"})},requestCreateNew:function(){n({event:"onRequestCreateNew"})},requestReferenceData:function(e){n({event:"onRequestReferenceData",data:e})},pluginsReady:function(){n({event:"onPluginsReady"})},on:function(o,n){t.on(o,(function(t,o){n.call(e,o)}))}}},void 0===window.Common&&(window.Common={}),Common.component=Common.component||{},Common.Analytics=Common.component.Analytics=new function(){var e;return{initialize:function(t,o){if(void 0===t)throw"Analytics: invalid id.";if(void 0===o||"[object String]"!==Object.prototype.toString.apply(o))throw"Analytics: invalid category type.";e=o,$("head").append('<script type="text/javascript">var _gaq = _gaq || [];_gaq.push(["_setAccount", "'+t+'"]);_gaq.push(["_trackPageview"]);(function() {var ga = document.createElement("script"); ga.type = "text/javascript"; ga.async = true;ga.src = ("https:" == document.location.protocol ? "https://ssl" : "http://www") + ".google-analytics.com/ga.js";var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(ga, s);})();<\/script>')},trackEvent:function(t,o,n){if(void 0!==t&&"[object String]"!==Object.prototype.toString.apply(t))throw"Analytics: invalid action type.";if(void 0!==o&&"[object String]"!==Object.prototype.toString.apply(o))throw"Analytics: invalid label type.";if(void 0!==n&&("[object Number]"!==Object.prototype.toString.apply(n)||!isFinite(n)))throw"Analytics: invalid value type.";if("undefined"!=typeof _gaq){if("undefined"===e)throw"Analytics is not initialized.";_gaq.push(["_trackEvent",e,t,o,n])}}}},!window.common&&(window.common={}),common.localStorage=new function(){var e,t,o={};Common.Gateway.on("internalcommand",(function(e){"localstorage"==e.type&&(o=e.keys)}));var n=function(e,t,n){if(a)try{localStorage.setItem(e,t)}catch(e){}else o[e]=t,!0===n&&Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:{name:t}})},r=function(e){return a?localStorage.getItem(e):void 0===o[e]?null:o[e]};try{var a=!!window.localStorage}catch(e){a=!1}return{getId:function(){return e},setId:function(t){e=t},getItem:r,getBool:function(e,t){var o=r(e);return t=t||!1,null!==o?0!=parseInt(o):t},setBool:function(e,t,o){n(e,t?1:0,o)},setItem:n,removeItem:function(e){a?localStorage.removeItem(e):delete o[e]},setKeysFilter:function(e){t=e},getKeysFilter:function(){return t},itemExists:function(e){return null!==r(e)},sync:function(){a||Common.Gateway.internalMessage("localstorage",{cmd:"get",keys:t})},save:function(){a||Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:o})}}},!window.common&&(window.common={}),!common.utils&&(common.utils={}),common.utils=new function(){var e=navigator.userAgent.toLowerCase();return{openLink:function(e){if(e){var t=window.open(e,"_blank");t&&t.focus()}},dialogPrint:function(e,t){if($("#id-print-frame").remove(),e){var o=document.createElement("iframe");o.id="id-print-frame",o.style.display="none",o.style.visibility="hidden",o.style.position="fixed",o.style.right="0",o.style.bottom="0",document.body.appendChild(o),o.onload=function(){try{o.contentWindow.focus(),o.contentWindow.print(),o.contentWindow.blur(),window.focus()}catch(e){t.asc_DownloadAs(new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.PDF))}},o.src=e}},htmlEncode:function(e){return $("<div/>").text(e).html()},fillUserInfo:function(e,t,o,n){var r=e||{};return r.anonymous=!r.id,!r.id&&(r.id=n),r.fullname=r.name?r.name:o,r.group&&(r.fullname=r.group.toString()+AscCommon.UserInfoParser.getSeparator()+r.fullname),r.guest=!r.name,r},fixedDigits:function(e,t,o){void 0===o&&(o="0");for(var n="",r=e.toString(),a=r.length;a<t;a++)n+=o;return n+r},getKeyByValue:function(e,t){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t)return o},isMac:/macintosh|mac os x/.test(e)}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.LoadMask=function(e){var t,o,n=e||$(document.body),r="",a=0,i=!1;return{show:function(){t&&o||(t=$('<div class="asc-loadmask-body" role="presentation" tabindex="-1"><i id="loadmask-spinner" class="asc-loadmask-image"></i><div class="asc-loadmask-title"></div></div>'),o=$('<div class="asc-loadmask"></div>')),$(".asc-loadmask-title",t).html(r),i||(i=!0,a=setTimeout((function(){n.append(o),n.append(t),t.css("min-width",$(".asc-loadmask-title",t).width()+105)}),500))},hide:function(){a&&(clearTimeout(a),a=0),o&&o.remove(),t&&t.remove(),o=t=null,i=!1},setTitle:function(e){if(r=e,n&&t){var o=$(".asc-loadmask-title",t);o.html(r),t.css("min-width",o.width()+105)}}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.modals=new function(){var e='<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="idm-title" aria-hidden="true"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button><h4 id="idm-title" class="modal-title">{title}</h4></div><div class="modal-body">{body}</div><div class="modal-footer">{footer}</div></div></div></div>',t='<div class="share-link"><input id="id-short-url" class="form-control" type="text" readonly/></div><div class="share-buttons"><span class="svg big-facebook" data-name="facebook"></span><span class="svg big-twitter" data-name="twitter"></span><span class="svg big-email" data-name="email"></span><div class="autotest" id="email" style="display: none"></div></div>';return{create:function(o,n){var r;if(!n&&(n="body"),"share"==o){if(window.config&&window.config.btnsShare){let e=[];for(const t of Object.keys(config.btnsShare))e.push(`<span class="svg big-${t}" data-name="${t}"></span>`);if(e){let o=$(t);o.find(".autotest").prevAll().remove(),o.eq(1).prepend(e.join("")),t=$("<div>").append(o).html()}}r=$(e.replace(/\{title}/,this.txtShare).replace(/\{body}/,t).replace(/\{footer}/,'<button id="btn-copyshort" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(n).attr("id","dlg-share")}else"embed"==o&&(r=$(e.replace(/\{title}/,this.txtEmbed).replace(/\{body}/,'<div class="size-manual"><span class="caption">{width}:</span><input id="txt-embed-width" class="form-control input-xs" type="text" value="400px"><input id="txt-embed-height" class="form-control input-xs right" type="text" value="600px"><span class="right caption">{height}:</span></div><textarea id="txt-embed-url" rows="4" class="form-control" readonly></textarea>').replace(/\{width}/,this.txtWidth).replace(/\{height}/,this.txtHeight).replace(/\{footer}/,'<button id="btn-copyembed" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(n).attr("id","dlg-embed"));return r},txtWidth:"Width",txtHeight:"Height",txtShare:"Share Link",txtCopy:"Copy to clipboard",txtEmbed:"Embed"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.modals=new function(){var e,t,o,n='<iframe allowtransparency="true" frameborder="0" scrolling="no" src="{embed-url}" width="{width}" height="{height}"></iframe>';function r(e,t){e.select(),document.execCommand("copy")||window.alert("Browser's error! Use keyboard shortcut [Ctrl] + [C]")}var a=function(){e=common.view.modals.create("share");var t=encodeURIComponent(o.shareUrl),n="mailto:?subject=I have shared a document with you: "+o.docTitle+"&body=I have shared a document with you: "+t;e.find("#btn-copyshort").on("click",r.bind(this,e.find("#id-short-url"))),e.find(".share-buttons > span").on("click",(function(e){if(window.config){const t=$(e.target).attr("data-name"),n=config.btnsShare[t];if(n&&n.getUrl)return void window.open(n.getUrl(o.shareUrl,o.docTitle),n.target||"",n.features||"menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600")}var r;switch($(e.target).attr("data-name")){case"facebook":r="https://www.facebook.com/sharer/sharer.php?u="+o.shareUrl+"&t="+encodeURI(o.docTitle),window.open(r,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"twitter":r="https://twitter.com/share?url="+t,o.docTitle&&(r+=encodeURIComponent("&text="+o.docTitle)),window.open(r,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"email":window.open(n,"_self")}})),e.find("#id-short-url").val(o.shareUrl),e.find(".share-buttons > #email.autotest").attr("data-test",n)};function i(){var e=t.find("#txt-embed-width"),r=t.find("#txt-embed-height"),a=parseInt(e.val()),i=parseInt(r.val());a<400&&(a=400),i<600&&(i=600),t.find("#txt-embed-url").text(n.replace("{embed-url}",o.embedUrl).replace("{width}",a).replace("{height}",i)),e.val(a+"px"),r.val(i+"px")}return{init:function(e){o=e},attach:function(s){s.share&&o.shareUrl&&(e||a(),$(s.share).on("click",(function(t){e.modal("show")}))),s.embed&&o.embedUrl&&(t||function(){var e=(t=common.view.modals.create("embed")).find("#txt-embed-url");e.text(n.replace("{embed-url}",o.embedUrl).replace("{width}",400).replace("{height}",600)),t.find("#btn-copyembed").on("click",r.bind(this,e)),t.find("#txt-embed-width, #txt-embed-height").on({keypress:function(e){13==e.keyCode&&i()},focusout:function(e){i()}})}(),$(s.embed).on("click",(function(e){t.modal("show")})))}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.SearchBar=new function(){var e='<div class="asc-window search-window" style="display: none;"><div class="body">{body}</div></div>';return{create:function(t){return!t&&(t="body"),$(e.replace(/\{body}/,'<input type="text" id="search-bar-text" placeholder="{textFind}" autocomplete="off"><div class="tools"><button id="search-bar-back" class="svg-icon search-arrow-up"></button><button id="search-bar-next" class="svg-icon search-arrow-down"></button><button id="search-bar-close" class="svg-icon search-close"></button></div>').replace(/\{textFind}/,this.textFind)).appendTo(t).attr("id","dlg-search")},disableNavButtons:function(e,t){var o=""===$("#search-bar-text").val()||!t;$("#search-bar-back").attr({disabled:o}),$("#search-bar-next").attr({disabled:o})},textFind:"Find"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.SearchBar=new function(){var e,t,o,n,r,a,i={searchText:""},s=function(e){(e&&i.searchText!==e||!e&&i.newSearchText)&&(i.newSearchText=e,r=new Date,void 0===a&&(a=setInterval((function(){new Date-r<400||(i.searchText=i.newSearchText,""!==i.newSearchText?c():n.asc_endFindText(),clearInterval(a),a=void 0)}),10)))},c=function(e,t){var o=new AscCommon.CSearchSettings;return o.put_Text(i.searchText),o.put_MatchCase(!1),o.put_WholeWords(!1),!!n.asc_findText(o,"back"!=e)||(common.view.SearchBar.disableNavButtons(),!1)},l=function(e,t,o){t&&t.length>0&&("keydown"===e&&13===o.keyCode||"keydown"!==e)&&(i.searchText=t,c(e)&&a&&(clearInterval(a),a=void 0))},d=function(e,t){common.view.SearchBar.disableNavButtons(e,t)};return{init:function(e){o=e},setApi:function(e){(n=e)&&n.asc_registerCallback("asc_onSetSearchCurrent",d)},show:function(){if(e||(e=common.view.SearchBar.create(),"bottom"===o.toolbarDocked?e.css({right:"45px",bottom:"31px"}):e.css({right:"45px",top:"31px"}),(t=e.find("#search-bar-text")).on("input",(function(e){common.view.SearchBar.disableNavButtons(),s(t.val())})).on("keydown",(function(e){l("keydown",t.val(),e)})),e.find("#search-bar-back").on("click",(function(e){l("back",t.val())})),e.find("#search-bar-next").on("click",(function(e){l("next",t.val())})),e.find("#search-bar-close").on("click",(function(t){e.hide()})),common.view.SearchBar.disableNavButtons()),!e.is(":visible")){var r=n&&n.asc_GetSelectedText()||i.searchText;t.val(r),r.length>0&&s(r),e.show(),setTimeout((function(){t.focus(),t.select()}),10)}}}},void 0===PE)var PE={};PE.ApplicationView=new function(){var e;return{create:function(){(e=$("#box-tools button")).addClass("dropdown-toggle").attr("data-toggle","dropdown").attr("aria-expanded","true"),e.parent().append('<ul class="dropdown-menu pull-right"><li><a id="idt-download"><span class="mi-icon svg-icon download"></span>'+this.txtDownload+'</a></li><li><a id="idt-print"><span class="mi-icon svg-icon print"></span>'+this.txtPrint+'</a></li><li class="divider"></li><li><a id="idt-search"><span class="mi-icon svg-icon search"></span>'+this.txtSearch+'</a></li><li class="divider"></li><li><a id="idt-share" data-toggle="modal"><span class="mi-icon svg-icon share"></span>'+this.txtShare+'</a></li><li><a id="idt-close" data-toggle="modal"><span class="mi-icon svg-icon go-to-location"></span>'+this.txtFileLocation+'</a></li><li class="divider"></li><li><a id="idt-embed" data-toggle="modal"><span class="mi-icon svg-icon embed"></span>'+this.txtEmbed+'</a></li><li><a id="idt-fullscreen"><span class="mi-icon svg-icon fullscr"></span>'+this.txtFullScreen+"</a></li></ul>")},tools:{get:function(t){return e.parent().find(t)}},txtDownload:"Download",txtPrint:"Print",txtShare:"Share",txtEmbed:"Embed",txtFullScreen:"Full Screen",txtFileLocation:"Open file location",txtSearch:"Search"}},PE.ApplicationController=new function(){var e,t,o,n,r,a,i={},s={},c={},l={},d={},u=0,m=!1,p=0,g=[5,-10],f=-256;if("undefined"==typeof isBrowserSupported||isBrowserSupported())return common.localStorage.setId("text"),common.localStorage.setKeysFilter("pe-,asc.presentation"),common.localStorage.sync(),{create:function(){return m||(e=this,m=!0,$(window).resize((function(){t&&t.Resize()})),window.onbeforeunload=j,(t=new Asc.asc_docs_api({"id-view":"editor_sdk",embedded:!0}))&&(t.SetThemesPath("../../../../sdkjs/slide/themes/"),t.asc_registerCallback("asc_onError",R),t.asc_registerCallback("asc_onDocumentContentReady",D),t.asc_registerCallback("asc_onOpenDocumentProgress",L),t.asc_registerCallback("asc_onCountPages",v),t.asc_registerCallback("asc_onCurrentPage",b),Common.Gateway.on("init",h),Common.Gateway.on("opendocument",w),Common.Gateway.on("showmessage",M),Common.Gateway.appReady(),common.controller.SearchBar.setApi(t))),e},errorDefaultMessage:"Error code: %1",unknownErrorText:"Unknown error.",convertationTimeoutText:"Conversion timeout exceeded.",convertationErrorText:"Conversion failed.",downloadErrorText:"Download failed.",criticalErrorTitle:"Error",notcriticalErrorTitle:"Warning",scriptLoadError:"The connection is too slow, some of the components could not be loaded. Please reload the page.",errorFilePassProtect:"The file is password protected and cannot be opened.",errorAccessDeny:"You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.",errorUserDrop:"The file cannot be accessed right now.",unsupportedBrowserErrorText:"Your browser is not supported.",textOf:"of",downloadTextText:"Downloading presentation...",waitText:"Please, wait...",textLoadingDocument:"Loading presentation",txtClose:"Close",errorFileSizeExceed:"The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.",errorUpdateVersionOnDisconnect:"Internet connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.",textGuest:"Guest",textAnonymous:"Anonymous",errorForceSave:"An error occurred while saving the file. Please use the 'Download as' option to save the file to your computer hard drive or try again later.",errorLoadingFont:"Fonts are not loaded.<br>Please contact your Document Server administrator.",errorTokenExpire:"The document security token has expired.<br>Please contact your Document Server administrator.",openErrorText:"An error has occurred while opening the file",errorInconsistentExtDocx:"An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.",errorInconsistentExtXlsx:"An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.",errorInconsistentExtPptx:"An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.",errorInconsistentExtPdf:"An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.",errorInconsistentExt:"An error has occurred while opening the file.<br>The file content does not match the file extension."};function h(e){i=$.extend(i,e.config),c=$.extend(c,e.config.embedded),common.controller.modals.init(c),common.controller.SearchBar.init(c),"bottom"===c.toolbarDocked?($("#toolbar").addClass("bottom"),$("#editor_sdk").addClass("bottom"),$("#box-preview").addClass("bottom"),$("#box-tools").removeClass("dropdown").addClass("dropup"),g[1]=-40):($("#toolbar").addClass("top"),$("#editor_sdk").addClass("top"),$("#box-preview").addClass("top")),i.canBackToFolder=!1!==i.canBackToFolder&&i.customization&&i.customization.goback&&(i.customization.goback.url||i.customization.goback.requestClose&&i.canRequestClose)}function w(n){if(s=n.doc){l=$.extend(l,s.permissions);var r=new Asc.asc_CDocInfo,a=new Asc.asc_CUserInfo,d=!("object"==typeof i.customization&&"object"==typeof i.customization.anonymous&&!1===i.customization.anonymous.request),u="object"==typeof i.customization&&"object"==typeof i.customization.anonymous&&"string"==typeof i.customization.anonymous.label&&""!==i.customization.anonymous.label.trim()?common.utils.htmlEncode(i.customization.anonymous.label):e.textGuest,m=d?common.localStorage.getItem("guest-username"):null,p=common.utils.fillUserInfo(i.user,i.lang,m?m+" ("+u+")":e.textAnonymous,common.localStorage.getItem("guest-id")||"uid-"+Date.now());p.anonymous&&common.localStorage.setItem("guest-id",p.id),a.put_Id(p.id),a.put_FullName(p.fullname),a.put_IsAnonymousUser(p.anonymous),r.put_Id(s.key),r.put_Url(s.url),r.put_DirectUrl(s.directUrl),r.put_Title(s.title),r.put_Format(s.fileType),r.put_VKey(s.vkey),r.put_UserInfo(a),r.put_CallbackUrl(i.callbackUrl),r.put_Token(s.token),r.put_Permissions(s.permissions),r.put_EncryptedInfo(i.encryptionKeys),r.put_Lang(i.lang),r.put_Mode(i.mode);var g=!i.customization||!1!==i.customization.macros;r.asc_putIsEnabledMacroses(!!g),g=!i.customization||!1!==i.customization.plugins,r.asc_putIsEnabledPlugins(!!g),t&&(t.asc_registerCallback("asc_onGetEditorPermissions",I),t.asc_registerCallback("asc_onRunAutostartMacroses",O),t.asc_setDocInfo(r),t.asc_getEditorPermissions(i.licenseUrl,i.customerId),t.asc_enableKeyEvents(!0),Common.Analytics.trackEvent("Load","Start")),c.docTitle=s.title,(o=$("#title-doc-name")).text(c.docTitle||"")}}function v(t){u=t,$("#pages").text(e.textOf+" "+t)}function b(e){$("#page-number").val(e+1),p=e}function y(t,o){var n="";switch(o){case Asc.c_oAscAsyncAction.Print:n=e.downloadTextText;break;case f:n=e.textLoadingDocument+"           ";break;default:n=e.waitText}t==Asc.c_oAscAsyncActionType.BlockInteraction&&(e.loadMask||(e.loadMask=new common.view.LoadMask),e.loadMask.setTitle(n),e.loadMask.show())}function x(){e.loadMask&&e.loadMask.hide()}function k(){e.isHideBodyTip=!0}function _(){e.isHideBodyTip&&r&&(r.tooltip("hide"),r=!1)}function C(t){if(t&&1==t.get_Type())if(e.isHideBodyTip=!1,n||((n=$(".hyperlink-tooltip")).tooltip({container:"body",trigger:"manual"}),n.on("shown.bs.tooltip",(function(e){r=n.data("bs.tooltip").tip();var t=n.ttpos[1]-r.height()+g[1];t<0&&(t=0),r.css({left:n.ttpos[0]+g[0],top:t}),r.find(".tooltip-arrow").css({left:10})}))),r){var o=n.ttpos[1]-r.height()+g[1];o<0&&(o=0),r.css({left:t.get_X()+g[0],top:o})}else n.ttpos=[t.get_X(),t.get_Y()],n.tooltip("show")}function A(e,t){Common.Gateway.downloadAs(e,t)}function E(){!1!==l.print&&t.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86))}function T(e){common.utils.dialogPrint(e,t)}function S(){$("#loading-mask").fadeOut("slow")}function D(){t.ShowThumbnails(!1),t.asc_DeleteVerticalScroll(),c.autostart&&"player"!=c.autostart||(t.SetDemonstrationModeOnly(),P()),S(),x(Asc.c_oAscAsyncActionType.BlockInteraction);var e=i.customization&&i.customization.zoom?parseInt(i.customization.zoom):-1;-1==e?t.zoomFitToPage():-2==e?t.zoomFitToWidth():t.zoom(e>0?e:100);var o=$("#box-tools .divider"),n=$("#box-tools a").length;$("#idt-search").hide(),n--,!1===l.print&&($("#idt-print").hide(),n--),c.saveUrl&&!1!==l.download||($("#idt-download").hide(),n--),c.shareUrl||($("#idt-share").hide(),n--),i.canBackToFolder||($("#idt-close").hide(),n--),n<7&&($(o[0]).hide(),$(o[1]).hide()),c.embedUrl||($("#idt-embed").hide(),n--),c.fullscreenUrl||($("#idt-fullscreen").hide(),n--),n<1?$("#box-tools").addClass("hidden"):c.embedUrl||c.fullscreenUrl||$(o[2]).hide(),common.controller.modals.attach({share:"#idt-share",embed:"#idt-embed"}),t.asc_registerCallback("asc_onMouseMoveStart",k),t.asc_registerCallback("asc_onMouseMoveEnd",_),t.asc_registerCallback("asc_onMouseMove",C),t.asc_registerCallback("asc_onDownloadUrl",A),t.asc_registerCallback("asc_onPrint",E),t.asc_registerCallback("asc_onPrintUrl",T),t.asc_registerCallback("asc_onHyperlinkClick",common.utils.openLink),t.asc_registerCallback("asc_onStartAction",y),t.asc_registerCallback("asc_onEndAction",x),t.asc_registerCallback("asc_onEndDemonstration",q),t.asc_registerCallback("asc_onDemonstrationSlideChanged",z),Common.Gateway.on("processmouse",U),Common.Gateway.on("downloadas",B),Common.Gateway.on("requestclose",F),PE.ApplicationView.tools.get("#idt-fullscreen").on("click",(function(){common.utils.openLink(c.fullscreenUrl)})),PE.ApplicationView.tools.get("#idt-download").on("click",(function(){c.saveUrl&&!1!==l.download&&common.utils.openLink(c.saveUrl),Common.Analytics.trackEvent("Save")})),PE.ApplicationView.tools.get("#idt-print").on("click",(function(){t.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86)),Common.Analytics.trackEvent("Print")})),PE.ApplicationView.tools.get("#idt-close").on("click",(function(){i.customization&&i.customization.goback&&(i.customization.goback.requestClose&&i.canRequestClose?Common.Gateway.requestClose():i.customization.goback.url&&(!1!==i.customization.goback.blank?window.open(i.customization.goback.url,"_blank"):window.parent.location.href=i.customization.goback.url))})),PE.ApplicationView.tools.get("#idt-search").on("click",(function(){common.controller.SearchBar.show()}));var r,s=$("#page-number");s.on({keyup:function(e){if(13==e.keyCode){var o=parseInt($("#page-number").val());isNaN(o)?$("#page-number").val(p+1):(o>u?o=u:o<2&&(o=1),o==p+1?$("#page-number").val(o):a?(p=o-1,t.DemonstrationGoToSlide(o-1)):t.goToPage(o-1)),s.blur()}},focusin:function(e){s.removeClass("masked")},focusout:function(e){!s.hasClass("masked")&&s.addClass("masked")}}),$("#pages").on("click",(function(e){s.focus()})),$("#btn-left").on("click",(function(){a?t.DemonstrationPrevSlide():p>0&&t.goToPage(p-1)})),$("#btn-right").on("click",(function(){a?t.DemonstrationNextSlide():p<u-1&&t.goToPage(p+1)}));var d=!1;$(document).on({click:function(e){clearTimeout(r),r=void 0},mousemove:function(e){$("#btn-left").fadeIn(),$("#btn-right").fadeIn(),d=!0,r||(r=setInterval((function(){d||(clearInterval(r),r=void 0),d=!1}),2e3))}});var m=!1;$(document.body).on("show.bs.modal",".modal",(function(e){m=!0,t.asc_enableKeyEvents(!1)})).on("hidden.bs.modal",".modal",(function(e){m=!1,t.asc_enableKeyEvents(!0)})).on("hidden.bs.dropdown",".dropdown",(function(e){m||t.asc_enableKeyEvents(!0)})).on("blur","input, textarea",(function(e){m||/area_id/.test(e.target.id)||t.asc_enableKeyEvents(!0)})),$("#editor_sdk").on("click",(function(e){"canvas"==e.target.localName&&e.currentTarget.focus()})),$("#btn-play").on("click",P),Common.Gateway.documentReady(),Common.Analytics.trackEvent("Load","Complete")}function I(e){d.canBranding=e.asc_getCustomization(),d.canBranding&&function(e){if(e&&e.logo){var t=$("#header-logo");(e.logo.image||e.logo.imageEmbedded)&&(t.html('<img src="'+(e.logo.image||e.logo.imageEmbedded)+'" style="max-width:100px; max-height:20px;"/>'),t.css({"background-image":"none",width:"auto",height:"auto"}),e.logo.imageEmbedded&&console.log("Obsolete: The 'imageEmbedded' parameter of the 'customization.logo' section is deprecated. Please use 'image' parameter instead.")),e.logo.url?t.attr("href",e.logo.url):void 0!==e.logo.url&&(t.removeAttr("href"),t.removeAttr("target"))}}(i.customization);var n=o.parent(),r=n.position().left,a=n.next().outerWidth();r<a?n.css("padding-left",a-r):n.css("padding-right",r-a),y(Asc.c_oAscAsyncActionType.BlockInteraction,f),t.asc_setViewMode(!0),t.asc_LoadDocument(),t.Resize()}function L(t){var o=(t.asc_getCurrentFont()+t.asc_getCurrentImage())/(t.asc_getFontsCount()+t.asc_getImagesCount());e.loadMask&&e.loadMask.setTitle(e.textLoadingDocument+": "+common.utils.fixedDigits(Math.min(Math.round(100*o),100),3,"  ")+"%")}function P(e){a?"play"==a?t.DemonstrationPause():t.DemonstrationPlay():($("#box-preview").show(),t.StartDemonstration("id-preview",p)),"play"!=a?($("#btn-play button").addClass("pause"),a="play"):($("#btn-play button").removeClass("pause"),a="pause")}function q(){a=void 0,$("#page-number").val(p+1),$("#btn-play button").removeClass("pause"),$("#box-preview").hide()}function z(e){e++<u&&$("#page-number").val(e)}function R(t,o,n){if(t==Asc.c_oAscError.ID.LoadingScriptError)return $("#id-critical-error-title").text(e.criticalErrorTitle),$("#id-critical-error-message").text(e.scriptLoadError),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){window.location.reload()})),void $("#id-critical-error-dialog").css("z-index",20002).modal("show");var r;switch(S(),x(Asc.c_oAscAsyncActionType.BlockInteraction),t){case Asc.c_oAscError.ID.Unknown:r=e.unknownErrorText;break;case Asc.c_oAscError.ID.ConvertationTimeout:r=e.convertationTimeoutText;break;case Asc.c_oAscError.ID.ConvertationError:r=e.convertationErrorText;break;case Asc.c_oAscError.ID.ConvertationOpenError:r=e.openErrorText;break;case Asc.c_oAscError.ID.DownloadError:r=e.downloadErrorText;break;case Asc.c_oAscError.ID.ConvertationPassword:r=e.errorFilePassProtect;break;case Asc.c_oAscError.ID.UserDrop:r=e.errorUserDrop;break;case Asc.c_oAscError.ID.ConvertationOpenLimitError:r=e.errorFileSizeExceed;break;case Asc.c_oAscError.ID.UpdateVersion:r=e.errorUpdateVersionOnDisconnect;break;case Asc.c_oAscError.ID.AccessDeny:r=e.errorAccessDeny;break;case Asc.c_oAscError.ID.ForceSaveButton:case Asc.c_oAscError.ID.ForceSaveTimeout:r=e.errorForceSave;break;case Asc.c_oAscError.ID.LoadingFontError:r=e.errorLoadingFont;break;case Asc.c_oAscError.ID.KeyExpire:r=e.errorTokenExpire;break;case Asc.c_oAscError.ID.ConvertationOpenFormat:r="pdf"===n?e.errorInconsistentExtPdf.replace("%1",s.fileType||""):"docx"===n?e.errorInconsistentExtDocx.replace("%1",s.fileType||""):"xlsx"===n?e.errorInconsistentExtXlsx.replace("%1",s.fileType||""):"pptx"===n?e.errorInconsistentExtPptx.replace("%1",s.fileType||""):e.errorInconsistentExt;break;default:r=e.errorDefaultMessage.replace("%1",t)}o==Asc.c_oAscError.Level.Critical?(Common.Gateway.reportError(t,r),$("#id-critical-error-title").text(e.criticalErrorTitle),$("#id-critical-error-message").html(r),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){window.location.reload()}))):(Common.Gateway.reportWarning(t,r),$("#id-critical-error-title").text(e.notcriticalErrorTitle),$("#id-critical-error-message").html(r),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){$("#id-critical-error-dialog").modal("hide")}))),$("#id-critical-error-dialog").modal("show"),Common.Analytics.trackEvent("Internal Error",t.toString())}function M(t){t&&(S(),$("#id-error-mask-title").text(e.criticalErrorTitle),$("#id-error-mask-text").text(t.msg),$("#id-error-mask").css("display","block"),Common.Analytics.trackEvent("External Error"))}function U(e){if("mouseup"==e.type){var o=document.getElementById("editor_sdk");if(o){var n=o.getBoundingClientRect();t.OnMouseUp(e.x-n.left,e.y-n.top)}}}function F(){Common.Gateway.requestClose()}function B(){!1!==l.download?t&&t.asc_DownloadAs(new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.PPTX,!0)):Common.Gateway.reportError(Asc.c_oAscError.ID.AccessDeny,e.errorAccessDeny)}function O(){i.customization&&!1===i.customization.macros||t&&t.asc_runAutostartMacroses()}function j(){common.localStorage.save()}Common.Gateway.reportError(void 0,this.unsupportedBrowserErrorText)},window.jQuery,Common.Locale.apply((function(){PE.ApplicationView.create(),PE.ApplicationController.create()}));