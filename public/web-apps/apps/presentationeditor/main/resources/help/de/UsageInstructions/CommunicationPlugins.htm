﻿<!DOCTYPE html>
<html>
<head>
    <title>Kommunikation während der Bearbeitung</title>
    <meta charset="utf-8" />
    <meta name="description" content="Im ONLYOFFICE Präsentationseditor können Sie immer mit Kollegen in Kontakt bleiben und beliebte Online-Messenger wie Telegram und Rainbow nutzen" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Kommunikation während der Bearbeitung</h1>
        <p>Im ONLYOFFICE <a href="https://www.onlyoffice.com/de/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> können Sie immer mit Kollegen in Kontakt bleiben und beliebte Online-Messenger wie Telegram und Rainbow nutzen.</p>
        <div class="note">Telegram- und Rainbow-Plugins werden standardmäßig nicht installiert. Informationen zur Installation finden Sie im entsprechenden Artikel: <span class="desktopDocumentFeatures"><a target="_blank" href="https://helpcenter.onlyoffice.com/installation/desktop-getting-started.aspx#AddingPlugins_block" onclick="onhyperlinkclick(this)">Hinzufügen von Plugins zu den ONLYOFFICE Desktop Editoren</a></span><span class="onlineDocumentFeatures"> <a target="_blank" href="https://api.onlyoffice.com/plugin/installation/cloud" onclick="onhyperlinkclick(this)">Hinzufügen von Plugins zu ONLYOFFICE Cloud</a> oder <a target="_blank" href="https://helpcenter.onlyoffice.com/installation/docs-add-plugin.aspx" onclick="onhyperlinkclick(this)">Hinzufügen neuer Plugins zu Server-Editoren</a></span>.</div>
        <h2>Telegram</h2>
        <p>Um mit dem Chatten im Telegram-Plugin zu beginnen:</p>
        <ul>
            <li>wechseln Sie zum Tab <b>Plugins</b> und klicken Sie auf <div class = "icon icon-telegram_icon"></div> <b>Telegram</b>,</li>
            <li>geben Sie Ihre Telefonnummer in das entsprechende Feld ein,</li>
            <li>aktivieren Sie das Kontrollkästchen <b>Angemeldet bleiben</b>, wenn Sie die Anmeldeinformationen für die aktuelle Sitzung speichern möchten, und klicken Sie auf die Schaltfläche <b>Weiter</b>,</li>
            <li>
                geben Sie den Code, den Sie erhalten haben, in Ihre Telegram-App ein,
                <p>oder</p>
            </li>
            <li>loggen Sie mit dem <b>QR-Code</b> ein,</li>
            <li>öffnen Sie die Telegram-App auf Ihrem Telefon,</li>
            <li>gehen Sie zu Einstellungen > Geräte > QR scannen,</li>
            <li>scannen Sie das Bild, um sich anzumelden.</li>
        </ul>
        <p>Jetzt können Sie Telegram für Instant Messaging innerhalb der Editor-Oberfläche von ONLYOFFICE verwenden.</p>
        <img alt="Telegram gif" src="../../images/telegram.gif" />
        <h2>Rainbow</h2>
        <p>Um mit dem Chatten im Rainbow-Plugin zu beginnen:</p>
        <ol>
            <li>wechseln Sie zum Tab <b>Plugins</b> und klicken Sie auf <div class = "icon icon-rainbow_icon"></div> <b>Rainbow</b>,</li>
            <li>registrieren Sie ein neues Konto, indem Sie auf die Schaltfläche <b>Anmelden</b> klicken, oder melden Sie sich bei einem bereits erstellten Konto an. Geben Sie dazu Ihre E-Mail in das entsprechende Feld ein und klicken Sie auf <b>Weiter</b>,</li>
            <li>geben Sie dann Ihr Kontopasswort ein,</li>
            <li>aktivieren Sie das Kontrollkästchen <b>Meine Sitzung am Leben erhalten</b>, wenn Sie die Anmeldeinformationen für die aktuelle Sitzung speichern möchten, und klicken Sie auf die Schaltfläche <b>Verbinden</b>.</li>
        </ol>
        <p>Jetzt sind Sie fertig und können gleichzeitig in Rainbow chatten und in der ONLYOFFICE-Editor-Oberfläche arbeiten.</p>
        <img alt="Rainbow gif" src="../../images/rainbow.gif" />
    </div>
</body>
</html>