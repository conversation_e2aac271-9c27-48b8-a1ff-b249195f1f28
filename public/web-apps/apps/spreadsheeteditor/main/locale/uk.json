{"cancelButtonText": "Скасувати", "Common.Controllers.Chat.notcriticalErrorTitle": "Застереження", "Common.Controllers.Chat.textEnterMessage": "ВВедіть своє повідомлення тут", "Common.Controllers.History.notcriticalErrorTitle": "Увага", "Common.define.chartData.textArea": "Площа", "Common.define.chartData.textAreaStacked": "Діаграма з областями із накопиченням", "Common.define.chartData.textAreaStackedPer": "Нормована з областями та накопиченням", "Common.define.chartData.textBar": "Вставити", "Common.define.chartData.textBarNormal": "Гістограма з групуванням", "Common.define.chartData.textBarNormal3d": "Тривимірна гістограма з групуванням", "Common.define.chartData.textBarNormal3dPerspective": "Тривимірна гістограма", "Common.define.chartData.textBarStacked": "Гістограма з накопиченням", "Common.define.chartData.textBarStacked3d": "Тривимірна гістограма з накопиченням", "Common.define.chartData.textBarStackedPer": "Нормована гістограма з накопиченням", "Common.define.chartData.textBarStackedPer3d": "Тривимірна нормована гістограма з накопиченням", "Common.define.chartData.textCharts": "Діаграми", "Common.define.chartData.textColumn": "Колона", "Common.define.chartData.textColumnSpark": "Колона", "Common.define.chartData.textCombo": "Комбінування", "Common.define.chartData.textComboAreaBar": "Діаграма з областями із накопиченням та гістограма з групуванням", "Common.define.chartData.textComboBarLine": "Гістограма з групуванням та графік", "Common.define.chartData.textComboBarLineSecondary": "Гістограма з групуванням та графік на допоміжній осі", "Common.define.chartData.textComboCustom": "Користувацька комбінація", "Common.define.chartData.textDoughnut": "Кільцева діаграма", "Common.define.chartData.textHBarNormal": "Лінійчата з групуванням", "Common.define.chartData.textHBarNormal3d": "Тривим<PERSON>рна лінійчата з групуванням", "Common.define.chartData.textHBarStacked": "Лінійчаста з накопиченням", "Common.define.chartData.textHBarStacked3d": "Тривимірна лінійчата з накопиченням", "Common.define.chartData.textHBarStackedPer": "Нормована лінійчата з накопиченням", "Common.define.chartData.textHBarStackedPer3d": "Тривимірна нормована лінійчата з накопиченням", "Common.define.chartData.textLine": "Лінія", "Common.define.chartData.textLine3d": "Тривимірний графік", "Common.define.chartData.textLineMarker": "Гра<PERSON><PERSON><PERSON> з маркерами", "Common.define.chartData.textLineSpark": "Лінія", "Common.define.chartData.textLineStacked": "Графік з накопиченням", "Common.define.chartData.textLineStackedMarker": "Графік з накопиченням і маркерами", "Common.define.chartData.textLineStackedPer": "Нормований графік з накопиченням", "Common.define.chartData.textLineStackedPerMarker": "Нормований графік з маркерами та накопиченням", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textPie3d": "Тривимірна кругова діаграма", "Common.define.chartData.textPoint": "XY (розсіювання)", "Common.define.chartData.textScatter": "Точкова діаграма", "Common.define.chartData.textScatterLine": "Точкова з прямими відрізками", "Common.define.chartData.textScatterLineMarker": "Точкова з прямими відрізками та маркерами", "Common.define.chartData.textScatterSmooth": "Точкова з гладкими кривими", "Common.define.chartData.textScatterSmoothMarker": "Точкова з гладкими кривими та маркерами", "Common.define.chartData.textSparks": "Міні-діграми", "Common.define.chartData.textStock": "За<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "Поверхня", "Common.define.chartData.textWinLossSpark": "Win / Loss", "Common.define.conditionalData.exampleText": "АаВвБбЯя", "Common.define.conditionalData.noFormatText": "Формат не заданий", "Common.define.conditionalData.text1Above": "На 1 стандартне відхилення вище", "Common.define.conditionalData.text1Below": "На 1 стандартне відхилення нижче", "Common.define.conditionalData.text2Above": "На 2 стандартні відхилення вище", "Common.define.conditionalData.text2Below": "На 2 стандартні відхилення нижче", "Common.define.conditionalData.text3Above": "На 3 стандартні відхилення вище", "Common.define.conditionalData.text3Below": "На 3 стандартні відхилення нижче", "Common.define.conditionalData.textAbove": "Вище", "Common.define.conditionalData.textAverage": "Середнє", "Common.define.conditionalData.textBegins": "Починається з", "Common.define.conditionalData.textBelow": "Нижче", "Common.define.conditionalData.textBetween": "Між", "Common.define.conditionalData.textBlank": "Пуста клітинка", "Common.define.conditionalData.textBlanks": "Містить пусті клітинки", "Common.define.conditionalData.textBottom": "Найменше", "Common.define.conditionalData.textContains": "Містить", "Common.define.conditionalData.textDataBar": "Гістограма", "Common.define.conditionalData.textDate": "Дата", "Common.define.conditionalData.textDuplicate": "Дублювати", "Common.define.conditionalData.textEnds": "Закінчується на ", "Common.define.conditionalData.textEqAbove": "Дорівнює або більше", "Common.define.conditionalData.textEqBelow": "Дорівнює або менше", "Common.define.conditionalData.textEqual": "Дорівнює", "Common.define.conditionalData.textError": "Помилка", "Common.define.conditionalData.textErrors": "Містить помилки", "Common.define.conditionalData.textFormula": "Формула", "Common.define.conditionalData.textGreater": "Більше", "Common.define.conditionalData.textGreaterEq": "Більше або дорівнює", "Common.define.conditionalData.textIconSets": "Набори іконок", "Common.define.conditionalData.textLast7days": "За останні 7 днів", "Common.define.conditionalData.textLastMonth": "<PERSON>и<PERSON><PERSON><PERSON><PERSON> місяць", "Common.define.conditionalData.textLastWeek": "Мин<PERSON><PERSON>ий тиждень", "Common.define.conditionalData.textLess": "Мен<PERSON>е ніж", "Common.define.conditionalData.textLessEq": "Менше або дорівнює", "Common.define.conditionalData.textNextMonth": "Наступний місяць", "Common.define.conditionalData.textNextWeek": "Наступний тиждень", "Common.define.conditionalData.textNotBetween": "не між", "Common.define.conditionalData.textNotBlanks": "Не містить пустих клітинок", "Common.define.conditionalData.textNotContains": "Не містить", "Common.define.conditionalData.textNotEqual": "Не дорівнює", "Common.define.conditionalData.textNotErrors": "Не містить помилок", "Common.define.conditionalData.textText": "Текст", "Common.define.conditionalData.textThisMonth": "Цей місяць", "Common.define.conditionalData.textThisWeek": "Цей тиждень", "Common.define.conditionalData.textToday": "Сьогодні", "Common.define.conditionalData.textTomorrow": "Завтра", "Common.define.conditionalData.textTop": "Найбільше", "Common.define.conditionalData.textUnique": "Унікальне", "Common.define.conditionalData.textValue": "Значення дорівнює", "Common.define.conditionalData.textYesterday": "Вчора", "Common.Translation.textMoreButton": "Більше", "Common.Translation.warnFileLocked": "Файл редагується в іншій програмі. Ви можете продовжити редагування та зберегти його як копію.", "Common.Translation.warnFileLockedBtnEdit": "Створити копію", "Common.Translation.warnFileLockedBtnView": "Відкрити на перегляд", "Common.UI.ButtonColored.textAutoColor": "Автоматичний", "Common.UI.ButtonColored.textNewColor": "Новий спеціальний колір", "Common.UI.ComboBorderSize.txtNoBorders": "Немає кордонів", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Немає кордонів", "Common.UI.ComboDataView.emptyComboText": "Немає стилів", "Common.UI.ExtendedColorDialog.addButtonText": "Додати", "Common.UI.ExtendedColorDialog.textCurrent": "В данний час", "Common.UI.ExtendedColorDialog.textHexErr": "Введене значення невірно. <br> Будь ласка, введіть значення між 000000 та FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Новий", "Common.UI.ExtendedColorDialog.textRGBErr": "Введене значення невірно. <br> Будь ласка, введіть числове значення від 0 до 255.", "Common.UI.HSBColorPicker.textNoColor": "Немає кольору", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Сховати пароль", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Показати пароль", "Common.UI.SearchDialog.textHighlight": "Виділіть результати", "Common.UI.SearchDialog.textMatchCase": "Чутливість до регістору символів", "Common.UI.SearchDialog.textReplaceDef": "Введіть текст заміни", "Common.UI.SearchDialog.textSearchStart": "Введіть свій текст тут", "Common.UI.SearchDialog.textTitle": "Знайти та перемістити", "Common.UI.SearchDialog.textTitle2": "Знайти", "Common.UI.SearchDialog.textWholeWords": "Тільки цілі слова", "Common.UI.SearchDialog.txtBtnHideReplace": "Сховати заміни", "Common.UI.SearchDialog.txtBtnReplace": "Замінити", "Common.UI.SearchDialog.txtBtnReplaceAll": "Замінити усе", "Common.UI.SynchronizeTip.textDontShow": "Не показувати це повідомлення знову", "Common.UI.SynchronizeTip.textSynchronize": "Документ був змінений іншим користувачем. <br> Будь ласка, натисніть, щоб зберегти зміни та завантажити оновлення.", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартні кольори", "Common.UI.ThemeColorPalette.textThemeColors": "Кольорові теми", "Common.UI.Themes.txtThemeClassicLight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на світла", "Common.UI.Themes.txtThemeDark": "Темна", "Common.UI.Themes.txtThemeLight": "Світла", "Common.UI.Window.cancelButtonText": "Скасувати", "Common.UI.Window.closeButtonText": "Закрити", "Common.UI.Window.noButtonText": "Немає", "Common.UI.Window.okButtonText": "OК", "Common.UI.Window.textConfirmation": "Підтвердження", "Common.UI.Window.textDontShow": "Не показувати це повідомлення знову", "Common.UI.Window.textError": "Помилка", "Common.UI.Window.textInformation": "Інформація", "Common.UI.Window.textWarning": "Застереження", "Common.UI.Window.yesButtonText": "Так", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "Пт", "Common.Views.About.txtAddress": "адреса:", "Common.Views.About.txtLicensee": "ЛІЦЕНЗІЯ", "Common.Views.About.txtLicensor": "Ліцензіар", "Common.Views.About.txtMail": "пошта:", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON>д керуванн<PERSON>м", "Common.Views.About.txtTel": "Тел.:", "Common.Views.About.txtVersion": "Версія", "Common.Views.AutoCorrectDialog.textAdd": "Додати", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Виконувати під час роботи", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Автозаміна", "Common.Views.AutoCorrectDialog.textAutoFormat": "Автоформат під час введення", "Common.Views.AutoCorrectDialog.textBy": "На", "Common.Views.AutoCorrectDialog.textDelete": "Видалити", "Common.Views.AutoCorrectDialog.textHyperlink": "Адреси в Інтернеті та мережеві шляхи гіперпосиланнями", "Common.Views.AutoCorrectDialog.textMathCorrect": "Автозаміна математичними символами", "Common.Views.AutoCorrectDialog.textNewRowCol": "Включати в таблицю нові рядки та стовпці", "Common.Views.AutoCorrectDialog.textRecognized": "Розпізнані функції", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Наступні вирази є розпізнаними математичними функціями. Вони не будуть автоматично виділятися курсивом.", "Common.Views.AutoCorrectDialog.textReplace": "Замінити", "Common.Views.AutoCorrectDialog.textReplaceText": "Заміняти при вводі", "Common.Views.AutoCorrectDialog.textReplaceType": "Заміняти текст при вводі", "Common.Views.AutoCorrectDialog.textReset": "Скинути", "Common.Views.AutoCorrectDialog.textResetAll": "Скинути налаштування", "Common.Views.AutoCorrectDialog.textRestore": "Відновити", "Common.Views.AutoCorrectDialog.textTitle": "Автозаміна", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Розпізнані функції повинні містити тільки прописні букви від А до Я.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Всі додані вирази будуть видалені, а видалені - відновлені. Ви хочете продовжити?", "Common.Views.AutoCorrectDialog.warnReplace": "Елемент автозаміни для %1 вже існує. Ви хочете його замінити?", "Common.Views.AutoCorrectDialog.warnReset": "Будь-які додані вами автозаміни будуть видалені, а змінені будуть відновлені до початкових. Бажаєте продовжити?", "Common.Views.AutoCorrectDialog.warnRestore": "Елемент автозаміни для %1 буде скинутий на початкове значення. Ви хочете продовжити?", "Common.Views.Chat.textSend": "Надіслати", "Common.Views.Comments.mniAuthorAsc": "За автором від А до Я", "Common.Views.Comments.mniAuthorDesc": "За автором від Я до А", "Common.Views.Comments.mniDateAsc": "Від старих до нових", "Common.Views.Comments.mniDateDesc": "Від нових до старих", "Common.Views.Comments.mniFilterGroups": "Фільтрувати за групою", "Common.Views.Comments.mniPositionAsc": "Зверху вниз", "Common.Views.Comments.mniPositionDesc": "Знизу вверх", "Common.Views.Comments.textAdd": "Додати", "Common.Views.Comments.textAddComment": "Добавити коментар", "Common.Views.Comments.textAddCommentToDoc": "Додати коментар до документа", "Common.Views.Comments.textAddReply": "Додати відповідь", "Common.Views.Comments.textAll": "Всі", "Common.Views.Comments.textAnonym": "Гість", "Common.Views.Comments.textCancel": "Скасувати", "Common.Views.Comments.textClose": "Закрити", "Common.Views.Comments.textClosePanel": "Закрити коментарі", "Common.Views.Comments.textComments": "Коментарі", "Common.Views.Comments.textEdit": "OК", "Common.Views.Comments.textEnterCommentHint": "Введіть свій коментар тут", "Common.Views.Comments.textHintAddComment": "Додати коментар", "Common.Views.Comments.textOpenAgain": "Відкрити знову", "Common.Views.Comments.textReply": "Відповісти", "Common.Views.Comments.textResolve": "Вирі<PERSON>ити", "Common.Views.Comments.textResolved": "Вирі<PERSON>ено", "Common.Views.Comments.textSort": "Сортувати коментарі", "Common.Views.Comments.textViewResolved": "У вас немає прав для повторного відкриття коментарю", "Common.Views.CopyWarningDialog.textDontShow": "Не показувати це повідомлення знову", "Common.Views.CopyWarningDialog.textMsg": "Копіювання, вирізання та вставлення дій за допомогою кнопок панелі інструментів редактора та дій контекстного меню буде виконуватися тільки на цій вкладці редактора. <br> <br> Щоб скопіювати або вставити до або з додатків за межами вкладки редактора, використовуйте такі комбінації клавіш:", "Common.Views.CopyWarningDialog.textTitle": "Копіювати, вирізати та вставити дії", "Common.Views.CopyWarningDialog.textToCopy": "Для копії", "Common.Views.CopyWarningDialog.textToCut": "для вирізання", "Common.Views.CopyWarningDialog.textToPaste": "Для вставлення", "Common.Views.DocumentAccessDialog.textLoading": "Завантаження...", "Common.Views.DocumentAccessDialog.textTitle": "Налаштування спільного доступу", "Common.Views.EditNameDialog.textLabel": "Підпис:", "Common.Views.EditNameDialog.textLabelError": "Підпис не повинен бути пустий", "Common.Views.Header.labelCoUsersDescr": "Користувач<PERSON>, що редагують документ:", "Common.Views.Header.textAddFavorite": "Додати в обране", "Common.Views.Header.textAdvSettings": "Додаткові параметри", "Common.Views.Header.textBack": "Відкрити розташування файлу", "Common.Views.Header.textCompactView": "Сховати панель інструментів", "Common.Views.Header.textHideLines": "Сховати лінійки", "Common.Views.Header.textHideStatusBar": "Об'єднати рядки листів та стану", "Common.Views.Header.textRemoveFavorite": "Видалити з вибраного", "Common.Views.Header.textSaveBegin": "Збереження ...", "Common.Views.Header.textSaveChanged": "Модифікований", "Common.Views.Header.textSaveEnd": "Усі зміни збережено", "Common.Views.Header.textSaveExpander": "Усі зміни збережено", "Common.Views.Header.textZoom": "Масш<PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Управління правами доступу до документів", "Common.Views.Header.tipDownload": "Завантажити файл", "Common.Views.Header.tipGoEdit": "Редагувати поточний файл", "Common.Views.Header.tipPrint": "Роздрукувати файл", "Common.Views.Header.tipRedo": "Повторити", "Common.Views.Header.tipSave": "Зберегти", "Common.Views.Header.tipUndo": "Скасувати", "Common.Views.Header.tipUndock": "Відкріпити в окремому вікні", "Common.Views.Header.tipViewSettings": "Налаштування перегляду", "Common.Views.Header.tipViewUsers": "Переглядайте користувачів та керуйте правами доступу до документів", "Common.Views.Header.txtAccessRights": "Змінити права доступу", "Common.Views.Header.txtRename": "Перейменування", "Common.Views.History.textCloseHistory": "Закрити історію", "Common.Views.History.textHide": "Згорнути", "Common.Views.History.textHideAll": "Сховати детальні зміни", "Common.Views.History.textRestore": "Відновити", "Common.Views.History.textShow": "Розгорнути", "Common.Views.History.textShowAll": "Показати детальні зміни", "Common.Views.History.textVer": "вер.", "Common.Views.ImageFromUrlDialog.textUrl": "Вставити URL зображення:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Це поле є обов'язковим", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Це поле має бути URL-адресою у форматі \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Маркований", "Common.Views.ListSettingsDialog.textNumbering": "Нумерований", "Common.Views.ListSettingsDialog.tipChange": "Змінити маркер", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON>овий маркер", "Common.Views.ListSettingsDialog.txtNone": "Немає", "Common.Views.ListSettingsDialog.txtOfText": "% тексту", "Common.Views.ListSettingsDialog.txtSize": "Розмір", "Common.Views.ListSettingsDialog.txtStart": "Розпочати з", "Common.Views.ListSettingsDialog.txtSymbol": "Символ", "Common.Views.ListSettingsDialog.txtTitle": "Налаштування списку", "Common.Views.ListSettingsDialog.txtType": "Тип", "Common.Views.OpenDialog.closeButtonText": "Закрити файл", "Common.Views.OpenDialog.textInvalidRange": "Неприпустимий діапазон клітинок", "Common.Views.OpenDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "Common.Views.OpenDialog.txtAdvanced": "Додатково", "Common.Views.OpenDialog.txtColon": "Двокрапка", "Common.Views.OpenDialog.txtComma": "Кома", "Common.Views.OpenDialog.txtDelimiter": "Розділ<PERSON><PERSON><PERSON>ч", "Common.Views.OpenDialog.txtDestData": "Виберіть де помістити дані", "Common.Views.OpenDialog.txtEmpty": "Це поле необхідно заповнити", "Common.Views.OpenDialog.txtEncoding": "Кодування", "Common.Views.OpenDialog.txtIncorrectPwd": "Введено хибний пароль.", "Common.Views.OpenDialog.txtOpenFile": "Введіть пароль для відкриття файлу", "Common.Views.OpenDialog.txtOther": "Ін<PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "Пароль", "Common.Views.OpenDialog.txtPreview": "Перегляд", "Common.Views.OpenDialog.txtProtected": "Після введення пароля та відкриття файла поточний пароль до нього буде скинутий.", "Common.Views.OpenDialog.txtSemicolon": "Крапка з комою", "Common.Views.OpenDialog.txtSpace": "Пробіл", "Common.Views.OpenDialog.txtTab": "Вкладка", "Common.Views.OpenDialog.txtTitle": "Виберіть параметри% 1", "Common.Views.OpenDialog.txtTitleProtected": "Захищений файл", "Common.Views.PasswordDialog.txtDescription": "Встановіть пароль для захисту цього документу", "Common.Views.PasswordDialog.txtIncorrectPwd": "Пароль та його підтвердження не збігаються", "Common.Views.PasswordDialog.txtPassword": "Пароль", "Common.Views.PasswordDialog.txtRepeat": "Повторити пароль", "Common.Views.PasswordDialog.txtTitle": "Встановлення паролю", "Common.Views.PasswordDialog.txtWarning": "Увага! Якщо ви втратили або не можете пригадати пароль, відновити його неможливо. Зберігайте його в надійному місці.", "Common.Views.PluginDlg.textLoading": "Завантаження", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "Common.Views.Plugins.textLoading": "Завантаження", "Common.Views.Plugins.textStart": "Початок", "Common.Views.Plugins.textStop": "Зупинити<PERSON>ь", "Common.Views.Protection.hintAddPwd": "Зашифрувати за допомогою пароля", "Common.Views.Protection.hintPwd": "Змінити чи видалити пароль", "Common.Views.Protection.hintSignature": "Додати цифровий підпис або рядок підпису", "Common.Views.Protection.txtAddPwd": "Додати пароль", "Common.Views.Protection.txtChangePwd": "Змінити пароль", "Common.Views.Protection.txtDeletePwd": "Видалити пароль", "Common.Views.Protection.txtEncrypt": "Шифрувати", "Common.Views.Protection.txtInvisibleSignature": "Додати цифровий підпис", "Common.Views.Protection.txtSignature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "Додати рядок підпису", "Common.Views.RenameDialog.textName": "Ім'я файлу", "Common.Views.RenameDialog.txtInvalidName": "Ім'я файлу не може містити жодного з наступних символів:", "Common.Views.ReviewChanges.hintNext": "До наступної зміни", "Common.Views.ReviewChanges.hintPrev": "До попередньої зміни", "Common.Views.ReviewChanges.strFast": "Швидкий", "Common.Views.ReviewChanges.strFastDesc": "Спільне редагування в режимі реального часу. Всі зміни зберігаються автоматично.", "Common.Views.ReviewChanges.strStrict": "Строгий", "Common.Views.ReviewChanges.strStrictDesc": "Використовуйте кнопку 'Зберегти' для синхронізації змін, які вносите ви та інші користувачі.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Прийняти поточні зміни", "Common.Views.ReviewChanges.tipCoAuthMode": "Встановити режим спільного редагування", "Common.Views.ReviewChanges.tipCommentRem": "Вилучити коментарі", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Вилучити поточні коментарі", "Common.Views.ReviewChanges.tipCommentResolve": "Вирішити коментарі", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Вирішити поточні коментарі", "Common.Views.ReviewChanges.tipHistory": "Показати історію версій", "Common.Views.ReviewChanges.tipRejectCurrent": "Відхилити поточну зміну", "Common.Views.ReviewChanges.tipReview": "Відстежувати зміни", "Common.Views.ReviewChanges.tipReviewView": "Виберіть режим, у якому ви бажаєте показувати зміни", "Common.Views.ReviewChanges.tipSetDocLang": "Задати мову документу", "Common.Views.ReviewChanges.tipSetSpelling": "Перевірка орфографії", "Common.Views.ReviewChanges.tipSharing": "Керування правами доступу до документів", "Common.Views.ReviewChanges.txtAccept": "Прийняти", "Common.Views.ReviewChanges.txtAcceptAll": "Прийняти усі зміни", "Common.Views.ReviewChanges.txtAcceptChanges": "Прийняти зміни", "Common.Views.ReviewChanges.txtAcceptCurrent": "Прийняти поточні зміни", "Common.Views.ReviewChanges.txtChat": "Чат", "Common.Views.ReviewChanges.txtClose": "Закрити", "Common.Views.ReviewChanges.txtCoAuthMode": "Режим спільного редагування", "Common.Views.ReviewChanges.txtCommentRemAll": "Вилучити усі коментарі", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Вилучити поточні коментарі", "Common.Views.ReviewChanges.txtCommentRemMy": "Вилучити мої коментарі", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Вилучити мій поточний коментар", "Common.Views.ReviewChanges.txtCommentRemove": "Видалити", "Common.Views.ReviewChanges.txtCommentResolve": "Вирі<PERSON>ити", "Common.Views.ReviewChanges.txtCommentResolveAll": "Вирішити всі коментарі", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Вирішити поточні коментарі", "Common.Views.ReviewChanges.txtCommentResolveMy": "Вирішити мої коментарі", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Вирішити мої поточні коментарі", "Common.Views.ReviewChanges.txtDocLang": "Мова", "Common.Views.ReviewChanges.txtFinal": "Усі зміни прийняті (попередній перегляд)", "Common.Views.ReviewChanges.txtFinalCap": "Фінальний", "Common.Views.ReviewChanges.txtHistory": "Історія версій", "Common.Views.ReviewChanges.txtMarkup": "Усі зміни (редагування)", "Common.Views.ReviewChanges.txtMarkupCap": "Зм<PERSON><PERSON>и", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Усі зміни відхилено (попередній перегляд)", "Common.Views.ReviewChanges.txtOriginalCap": "Початковий", "Common.Views.ReviewChanges.txtPrev": "До попереднього", "Common.Views.ReviewChanges.txtReject": "Від<PERSON><PERSON><PERSON>ити", "Common.Views.ReviewChanges.txtRejectAll": "Відхилити усі зміни", "Common.Views.ReviewChanges.txtRejectChanges": "Відхилити зміни", "Common.Views.ReviewChanges.txtRejectCurrent": "Відхилити поточну зміну", "Common.Views.ReviewChanges.txtSharing": "Спільний доступ", "Common.Views.ReviewChanges.txtSpelling": "Перевірка орфографії", "Common.Views.ReviewChanges.txtTurnon": "Відстежування змін", "Common.Views.ReviewChanges.txtView": "Пок<PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "Додати", "Common.Views.ReviewPopover.textAddReply": "Додати відповідь", "Common.Views.ReviewPopover.textCancel": "Відміна", "Common.Views.ReviewPopover.textClose": "Закрити", "Common.Views.ReviewPopover.textEdit": "Ок", "Common.Views.ReviewPopover.textMention": "+згадка надасть доступ до документа і відправить сповіщення поштою", "Common.Views.ReviewPopover.textMentionNotify": "+згадка відправить користувачу сповіщення поштою", "Common.Views.ReviewPopover.textOpenAgain": "Відкрити знову", "Common.Views.ReviewPopover.textReply": "Відповісти", "Common.Views.ReviewPopover.textResolve": "Вирі<PERSON>ити", "Common.Views.ReviewPopover.textViewResolved": "У вас немає прав для повторного відкриття коментарю", "Common.Views.ReviewPopover.txtDeleteTip": "Видалити", "Common.Views.ReviewPopover.txtEditTip": "Редагувати", "Common.Views.SaveAsDlg.textLoading": "Завантаження", "Common.Views.SaveAsDlg.textTitle": "Папка для збереження", "Common.Views.SelectFileDlg.textLoading": "Завантаження", "Common.Views.SelectFileDlg.textTitle": "Вибрати джерело даних", "Common.Views.SignDialog.textBold": "Напівжирний", "Common.Views.SignDialog.textCertificate": "Серти<PERSON><PERSON><PERSON><PERSON>т", "Common.Views.SignDialog.textChange": "Змінити", "Common.Views.SignDialog.textInputName": "Введіть ім'я підписанта", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Ім'я підписанта не має бути пустим.", "Common.Views.SignDialog.textPurpose": "Ціль підписання документу", "Common.Views.SignDialog.textSelect": "Вибрати", "Common.Views.SignDialog.textSelectImage": "Вибрати зображення", "Common.Views.SignDialog.textSignature": "Вигляд підпису:", "Common.Views.SignDialog.textTitle": "Підписання документу", "Common.Views.SignDialog.textUseImage": "або натисніть 'Обрати зображення', щоб використовувати зображення як підпис", "Common.Views.SignDialog.textValid": "Дійсний з %1 по %2", "Common.Views.SignDialog.tipFontName": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "Розмір шрифту", "Common.Views.SignSettingsDialog.textAllowComment": "Дозволити підписанту додавати коментар у вікні підпису", "Common.Views.SignSettingsDialog.textInfoEmail": "Адреса електронної пошти", "Common.Views.SignSettingsDialog.textInfoName": "Ім'я", "Common.Views.SignSettingsDialog.textInfoTitle": "Посада підписанта", "Common.Views.SignSettingsDialog.textInstructions": "Інструкції для підписанта", "Common.Views.SignSettingsDialog.textShowDate": "Показувати дату підпису в рядку підпису", "Common.Views.SignSettingsDialog.textTitle": "Налаштування підпису", "Common.Views.SignSettingsDialog.txtEmpty": "Це поле є обов'язковим", "Common.Views.SymbolTableDialog.textCharacter": "Символ", "Common.Views.SymbolTableDialog.textCode": "Код знака з Юнікод (шістн.)", "Common.Views.SymbolTableDialog.textCopyright": "Знак авторського права", "Common.Views.SymbolTableDialog.textDCQuote": "Закриваючі подвійні лапки", "Common.Views.SymbolTableDialog.textDOQuote": "Відкриваючі подвійні лапки", "Common.Views.SymbolTableDialog.textEllipsis": "Горизонтальна трикрапка", "Common.Views.SymbolTableDialog.textEmDash": "Довге тире", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON><PERSON> пробіл", "Common.Views.SymbolTableDialog.textEnDash": "Коротке тире", "Common.Views.SymbolTableDialog.textEnSpace": "Короткий пробіл", "Common.Views.SymbolTableDialog.textFont": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Нерозривний дефіс", "Common.Views.SymbolTableDialog.textNBSpace": "Нерозривний пробіл", "Common.Views.SymbolTableDialog.textPilcrow": "Знак абзацу", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 пробілу", "Common.Views.SymbolTableDialog.textRange": "Діапазон", "Common.Views.SymbolTableDialog.textRecent": "Раніше вживані символи", "Common.Views.SymbolTableDialog.textRegistered": "Зареєстрований товарний знак", "Common.Views.SymbolTableDialog.textSCQuote": "Закриваючі лапки", "Common.Views.SymbolTableDialog.textSection": "Знак розділу", "Common.Views.SymbolTableDialog.textShortcut": "Поєднання клавіш", "Common.Views.SymbolTableDialog.textSHyphen": "М'який дефіс", "Common.Views.SymbolTableDialog.textSOQuote": "Відкриваючі лапки", "Common.Views.SymbolTableDialog.textSpecial": "Спеціальні символи", "Common.Views.SymbolTableDialog.textSymbols": "Символи", "Common.Views.SymbolTableDialog.textTitle": "Символ", "Common.Views.SymbolTableDialog.textTradeMark": "Символ товарного знаку", "Common.Views.UserNameDialog.textDontShow": "Більше не запитувати", "Common.Views.UserNameDialog.textLabel": "Підпис:", "Common.Views.UserNameDialog.textLabelError": "Підпис не повинен бути пустий", "SSE.Controllers.DataTab.textColumns": "Стовпчики", "SSE.Controllers.DataTab.textEmptyUrl": "Необхідно вказати URL.", "SSE.Controllers.DataTab.textRows": "Рядки", "SSE.Controllers.DataTab.textWizard": "Текст по стовпчиках", "SSE.Controllers.DataTab.txtDataValidation": "Перевірка даних", "SSE.Controllers.DataTab.txtExpand": "Розгорнути", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Дані поруч із виділеним діапазоном не будуть видалені. Ви хочете розширити виділений діапазон, щоб включити дані із суміжних клітинок, або продовжити тільки з виділеним діапазоном?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Виділена область містить клітинки без умов для значень. Ви хочете поширити умови на ці клітинки?", "SSE.Controllers.DataTab.txtImportWizard": "Майстер імпорту тексту", "SSE.Controllers.DataTab.txtRemDuplicates": "Видалити дублікати", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Виділена область містить більше однієї умови.<br>Видалити поточні параметри та продовжити?", "SSE.Controllers.DataTab.txtRemSelected": "Видалити у виділеному діапазоні", "SSE.Controllers.DataTab.txtUrlTitle": "Вставте URL-адресу даних", "SSE.Controllers.DocumentHolder.alignmentText": "Вирівнювання", "SSE.Controllers.DocumentHolder.centerText": "Центр", "SSE.Controllers.DocumentHolder.deleteColumnText": "Видалити колону", "SSE.Controllers.DocumentHolder.deleteRowText": "Видалити рядок", "SSE.Controllers.DocumentHolder.deleteText": "Видалити", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Посилання не існує. Будь ласка, виправте посилання або видаліть його.", "SSE.Controllers.DocumentHolder.guestText": "Гість", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Колонка ліворуч", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Колонка праворуч", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Рядок вгорі", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Рядок внизу", "SSE.Controllers.DocumentHolder.insertText": "Вставити", "SSE.Controllers.DocumentHolder.leftText": "Лі<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Застереження", "SSE.Controllers.DocumentHolder.rightText": "Право", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Параметри автозаміни", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON>и<PERSON><PERSON>на стовпчика {0} символів ({1} пікселів)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Висота рядка {0} балів ({1} пікселів)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Клацніть на посилання, щоб перейти за ним, або клацніть та утримайте кнопку миші для вибору комірки.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Додати зліва", "SSE.Controllers.DocumentHolder.textInsertTop": "Додати вище", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Спеціа<PERSON>ьна вставка", "SSE.Controllers.DocumentHolder.textStopExpand": "Не розгортати таблиці автоматично", "SSE.Controllers.DocumentHolder.textSym": "Символ", "SSE.Controllers.DocumentHolder.tipIsLocked": "Цей елемент редагує інший користувач.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Вище середнього", "SSE.Controllers.DocumentHolder.txtAddBottom": "Додати нижню межу", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Додати фракційну панель", "SSE.Controllers.DocumentHolder.txtAddHor": "Додати горизонтальну лінію", "SSE.Controllers.DocumentHolder.txtAddLB": "Додати ліву лінію межі", "SSE.Controllers.DocumentHolder.txtAddLeft": "Додати ліву межу", "SSE.Controllers.DocumentHolder.txtAddLT": "Додати  верхню лінію ліворуч", "SSE.Controllers.DocumentHolder.txtAddRight": "Додати правий кордон", "SSE.Controllers.DocumentHolder.txtAddTop": "Додати верхню межу", "SSE.Controllers.DocumentHolder.txtAddVer": "Додати вертикальну лінію", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Вирівняти до символу", "SSE.Controllers.DocumentHolder.txtAll": "(Всі)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Повертає весь вміст таблиці або зазначені стовпчики таблиці, включаючи заголовки стовпчиків, дані та рядки підсумків", "SSE.Controllers.DocumentHolder.txtAnd": "і", "SSE.Controllers.DocumentHolder.txtBegins": "Починається з", "SSE.Controllers.DocumentHolder.txtBelowAve": "Нижче середнього", "SSE.Controllers.DocumentHolder.txtBlanks": "(Пусті)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Прикордонні властивості", "SSE.Controllers.DocumentHolder.txtBottom": "Внизу", "SSE.Controllers.DocumentHolder.txtColumn": "Стовпчик", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Вирівнювання колонки", "SSE.Controllers.DocumentHolder.txtContains": "Містить", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Повертає клітинки даних із таблиці або вказаних стовпчиків таблиці", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Зменшити розмір документу", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Видалити аргумент", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "видалити розрив", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Видалити супутні символи", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Видалити окремі символи та розділювачі", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Видалити рівняння", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Видалити символ", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Видалити радикал", "SSE.Controllers.DocumentHolder.txtEnds": "Закінчується на ", "SSE.Controllers.DocumentHolder.txtEquals": "Дорівнює", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Дорівнює кольору клітинки", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Дорівнює кольору шрифту", "SSE.Controllers.DocumentHolder.txtExpand": "Розгорнути та сортувати", "SSE.Controllers.DocumentHolder.txtExpandSort": "Дані після позначеного діапазону не буде впорядковано. Розширити вибір, щоб включити сусідні дані або продовжити впорядковування тільки щойно вибраних комірок?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Найменші", "SSE.Controllers.DocumentHolder.txtFilterTop": "Найбільші", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Зміна до лінійної фракції", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Зміна перекошеної фракції", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Зміна складеної фракції", "SSE.Controllers.DocumentHolder.txtGreater": "Більше", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Більше або дорівнює", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Прибрати над текстом", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Прибрати під текстом", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Повертає заголовки стовпчиків із таблиці або зазначених стовпчиків таблиці", "SSE.Controllers.DocumentHolder.txtHeight": "Висота", "SSE.Controllers.DocumentHolder.txtHideBottom": "Сховати нижню межу", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Сховати нижню межу", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Сховати закриваючу дужку", "SSE.Controllers.DocumentHolder.txtHideDegree": "Сховати ступінь", "SSE.Controllers.DocumentHolder.txtHideHor": "Сховати горизонтальну лінію", "SSE.Controllers.DocumentHolder.txtHideLB": "Сховати лівий нижній рядок", "SSE.Controllers.DocumentHolder.txtHideLeft": "Сховати лівий край", "SSE.Controllers.DocumentHolder.txtHideLT": "Сховати лівий верхній рядок", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Сховати відкрті дужки", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Сховати заповнювач", "SSE.Controllers.DocumentHolder.txtHideRight": "Приховати праву межу", "SSE.Controllers.DocumentHolder.txtHideTop": "Приховати верхню межу", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Сховати верхню межу", "SSE.Controllers.DocumentHolder.txtHideVer": "Сховати вертикальну лінію", "SSE.Controllers.DocumentHolder.txtImportWizard": "Майстер імпорту тексту", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Збільшити розмір аргументів", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Вставити аргумент після", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Вставити аргумент перед", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Вставити ручне переривання", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Вставити рівняння після", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Вставити рівняння перед", "SSE.Controllers.DocumentHolder.txtItems": "елементів", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Зберегти тільки текст", "SSE.Controllers.DocumentHolder.txtLess": "Мен<PERSON>е ніж", "SSE.Controllers.DocumentHolder.txtLessEquals": "Менше або дорівнює", "SSE.Controllers.DocumentHolder.txtLimitChange": "Зміна меж розташування", "SSE.Controllers.DocumentHolder.txtLimitOver": "Обмеження над текстом", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Обмеження під текстом", "SSE.Controllers.DocumentHolder.txtLockSort": "Виявлено дані поруч із виділеним діапазоном, але у вас недостатньо прав для зміни цих клітинок.<br>Ви бажаєте продовжити роботу з виділеним діапазоном?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Відповідність дужок до висоти аргументів", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Вирівнювання матриці", "SSE.Controllers.DocumentHolder.txtNoChoices": "Відсутній вибір для заповнення комірки.<br>Тільки текстові значення зі стовпця можна вибрати для заміни.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Не починається з", "SSE.Controllers.DocumentHolder.txtNotContains": "Не містить", "SSE.Controllers.DocumentHolder.txtNotEnds": "Не закінчується на", "SSE.Controllers.DocumentHolder.txtNotEquals": "Не рівно", "SSE.Controllers.DocumentHolder.txtOr": "або", "SSE.Controllers.DocumentHolder.txtOverbar": "Риска над текстом", "SSE.Controllers.DocumentHolder.txtPaste": "Вставити", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Формула без кордонів", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Формула + ширина колонки", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Форматування призначення", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Вставити лише форматування", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Формула + формат номера", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Вставити лише формулу", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Формула + все форматування", "SSE.Controllers.DocumentHolder.txtPasteLink": "Вставити посилання", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Пов'язана картина", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Злиття умовного форматування", "SSE.Controllers.DocumentHolder.txtPastePicture": "Картинка", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Формати вихідного коду", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Переміщувати", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Значення + все форматування", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Значення + формат номера", "SSE.Controllers.DocumentHolder.txtPasteValues": "Вставити  лише значення", "SSE.Controllers.DocumentHolder.txtPercent": "відсотків", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Повторити авторозгортання таблиці", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Видалити фракційну стрічку", "SSE.Controllers.DocumentHolder.txtRemLimit": "Видалити ліміт", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "видалити наголоси", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Видалити панель", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Ви хочете видалити цей підпис?<br>Цю дію не можна скасувати.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Видалити скрипти", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Видалити підписку", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Видалити верхній індекс", "SSE.Controllers.DocumentHolder.txtRowHeight": "Висота рядка", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Рукописи після тексту", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Рукописи перед текстом", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Показати нижню межу", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Показати закриваючу дужку", "SSE.Controllers.DocumentHolder.txtShowDegree": "Показати ступінь", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Показати відкриваючу дужку", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Показати заповнювач", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Показати верхню межу", "SSE.Controllers.DocumentHolder.txtSorting": "Сортування", "SSE.Controllers.DocumentHolder.txtSortSelected": "Сортувати вибрано", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Розтягнути дужки", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Вибрати тільки цей рядок вказаного стовпця", "SSE.Controllers.DocumentHolder.txtTop": "Верх", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Повертає рядки підсумків таблиці або зазначених стовпчиків таблиці", "SSE.Controllers.DocumentHolder.txtUnderbar": "Риска після тексту", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Скасувати авторозгортання таблиці", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Використовувати майстер імпорту тексту", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Перехід за цим посиланням може нашкодити вашому пристрою та даним.<br>Ви дійсно бажаєте продовжити?", "SSE.Controllers.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "Всі", "SSE.Controllers.FormulaDialog.sCategoryCube": "А<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Бази даних", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Дата і час", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Інженерія", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Фінансові", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Інформаційні", "SSE.Controllers.FormulaDialog.sCategoryLast10": "Останні 10 використаних", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Логічні", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Пошук та довідка", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Математичні", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Статистичний", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Текст і дата", "SSE.Controllers.LeftMenu.newDocumentTitle": "Електронна таблиця без нзви", "SSE.Controllers.LeftMenu.textByColumns": "За колонками", "SSE.Controllers.LeftMenu.textByRows": "За рядками", "SSE.Controllers.LeftMenu.textFormulas": "Формули", "SSE.Controllers.LeftMenu.textItemEntireCell": "Весь вміст комірки", "SSE.Controllers.LeftMenu.textLoadHistory": "Завантаження історії версій...", "SSE.Controllers.LeftMenu.textLookin": "Погляньте на", "SSE.Controllers.LeftMenu.textNoTextFound": "Не вдалося знайти дані, які ви шукали. Будь ласка, налаштуйте параметри пошуку.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "За<PERSON><PERSON>на була зроблена. {0} угоди були пропущені.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Пошук виконано. Угоди замінено: {0}", "SSE.Controllers.LeftMenu.textSearch": "По<PERSON><PERSON>к", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Значення", "SSE.Controllers.LeftMenu.textWarning": "Застереження", "SSE.Controllers.LeftMenu.textWithin": "Всередині", "SSE.Controllers.LeftMenu.textWorkbook": "Робоча книга", "SSE.Controllers.LeftMenu.txtUntitled": "Без назви", "SSE.Controllers.LeftMenu.warnDownloadAs": "Якщо ви продовжите збереження в цьому форматі, всі функції, окрім тексту, буде втрачено. <br> Ви впевнені, що хочете продовжити?", "SSE.Controllers.Main.confirmMoveCellRange": "Діапазон комірок призначення може містити дані. Продовжити операцію?", "SSE.Controllers.Main.confirmPutMergeRange": "Джерело даних містило об'єднані комірки.<br>Об'єднання було скасовано перед вставкою до таблиці.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Формули в рядку заголовка будуть видалені та перетворені на статичний текст.<br>Ви хочете продовжити?", "SSE.Controllers.Main.convertationTimeoutText": "Термін переходу перевищено.", "SSE.Controllers.Main.criticalErrorExtText": "Натисніть \"OK\", щоб повернутися до списку документів.", "SSE.Controllers.Main.criticalErrorTitle": "Помилка", "SSE.Controllers.Main.downloadErrorText": "Завантаження не вдалося", "SSE.Controllers.Main.downloadTextText": "Завантаження електронної таблиці...", "SSE.Controllers.Main.downloadTitleText": "Завантаження електронної таблиці", "SSE.Controllers.Main.errNoDuplicates": "Значення, що повторюються, не знайдені.", "SSE.Controllers.Main.errorAccessDeny": "Ви намагаєтеся виконати дію, на яку у вас немає прав. <br> Будь ласка, зв'яжіться з адміністратором вашого Сервера документів.", "SSE.Controllers.Main.errorArgsRange": "Помилка введеної формули. Використовується неправильний діапазон аргументів.", "SSE.Controllers.Main.errorAutoFilterChange": "Недозволена дія, оскільки ви намагаєтесь пересунути комірки до таблиці вашого робочого аркушу.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Неможливо виконати дію для вибраних комірок, оскільки ви не можете перемістити частину таблиці.<br>Виберіть інший діапазон даних, щоб перемістити всю таблицю та повторіть спробу.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Неможливо виконати дію для вибраного діапазону комірок.<br>Виберіть єдиний діапазон даних, відмінний від існуючого, та спробуйте ще раз.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Неможливо виконати дію, оскільки область містить відфільтровані комірки.<br>Будь ласка, зробіть елементи фільтрування видимими та повторіть спробу.", "SSE.Controllers.Main.errorBadImageUrl": "URL-адреса зображення невірна", "SSE.Controllers.Main.errorCannotUngroup": "Неможливо розгрупувати. Щоб створити структуру документу, виділіть стовпчики або рядки та згрупуйте їх.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Цю команду не можна використовувати на захищеному листі. Необхідно спочатку зняти захист листа. Можливо, потрібно буде ввести пароль.", "SSE.Controllers.Main.errorChangeArray": "Не можна змінити частину масиву.", "SSE.Controllers.Main.errorChangeFilteredRange": "Це призведе до зміни відфільтрованого діапазону листа.<br>Щоб виконати це завдання, необхідно видалити автофільтри.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Клітинка або діаграма, яку ви намагаєтеся змінити, знаходиться на захищеному листі.<br>Щоб внести зміни, зніміть захист листа. Можливо, потрібно буде ввести пароль.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "З'єднання з сервером втрачено. Документ не можна редагувати прямо зараз.", "SSE.Controllers.Main.errorConnectToServer": "Не вдалося зберегти документ. Будь ласка, перевірте налаштування з'єднання або зверніться до свого адміністратора.<br>Після натискання кнопки «ОК» вам буде запропоновано завантажити документ.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Ця команда не може бути використана з декількома вибору. <br> Виберіть один діапазон і спробуйте ще раз.", "SSE.Controllers.Main.errorCountArg": "Помилка введеної формули. <br> Використовується невірне число аргументів.", "SSE.Controllers.Main.errorCountArgExceed": "Помилка введеної формули. <br> Кількість аргументів перевищено.", "SSE.Controllers.Main.errorCreateDefName": "Існуючі названі діапазони не можна редагувати, а нові не можна створити на даний момент, оскільки деякі з них редагуються.", "SSE.Controllers.Main.errorDatabaseConnection": "Зовнішня помилка. <br> Помилка підключення до бази даних. Будь ласка, зв'яжіться зі службою підтримки, якщо помилка не зникне.", "SSE.Controllers.Main.errorDataEncrypted": "Отримані зашифровані зміни, їх неможливо розшифрувати.", "SSE.Controllers.Main.errorDataRange": "Неправильний діапазон даних.", "SSE.Controllers.Main.errorDataValidate": "Введене значення неприпустиме.<br>Значення, які можна ввести в цю клітинку, обмежені.", "SSE.Controllers.Main.errorDefaultMessage": "Код помилки: %1 ", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Ви намагаєтеся видалити стовпчик із заблокованою клітинкою. Заблоковані клітинки не можна видаляти, якщо лист захищений.<br>Щоб видалити заблоковану клітинку, зніміть захист листа. Можливо, потрібно буде ввести пароль.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Ви намагаєтеся видалити рядок із заблокованою клітинкою. Заблоковані клітинки не можна видаляти, якщо лист захищений.<br>Щоб видалити заблоковану клітинку, зніміть захист листа. Можливо, потрібно буде ввести пароль.", "SSE.Controllers.Main.errorEditingDownloadas": "Під час роботи з документом сталася помилка.<br>Використовуйте опцію 'Завантажити як', щоб зберегти резервну копію файлу на жорсткий диск комп'ютера.", "SSE.Controllers.Main.errorEditingSaveas": "Під час роботи з документом сталася помилка.<br>Використовуйте опцію 'Зберегти як...', щоб зберегти резервну копію файлу на жорсткий диск комп'ютера.", "SSE.Controllers.Main.errorEditView": "Зараз не можна відредагувати існуючий вигляд листа і не можна створювати новий, оскільки деякі з них редагуються.", "SSE.Controllers.Main.errorEmailClient": "Не знайдений поштовий клієнт", "SSE.Controllers.Main.errorFilePassProtect": "Файл захищений паролем і його неможливо відкрити.", "SSE.Controllers.Main.errorFileRequest": "Зовнішня помилка. <br> Помилка запиту файлу. Будь ласка, зв'яжіться зі службою підтримки, якщо помилка не зникне.", "SSE.Controllers.Main.errorFileSizeExceed": "Розмір файлу перевищує обмеження, встановлені для вашого сервера. <br> Для детальної інформації зверніться до адміністратора сервера документів.", "SSE.Controllers.Main.errorFileVKey": "Зовнішня помилка. <br> Невірний ключ безпеки. Будь ласка, зв'яжіться зі службою підтримки, якщо помилка не зникне.", "SSE.Controllers.Main.errorFillRange": "Не вдалося заповнити вибраний діапазон комірок.<br>Усі об'єднані комірки повинні мати однаковий розмір.", "SSE.Controllers.Main.errorForceSave": "Під час збереження файлу сталася помилка.<br>Використовуйте опцію 'Завантажити як...', щоб зберегти файл на жорсткий диск комп'ютера або спробуйте пізніше.", "SSE.Controllers.Main.errorFormulaName": "Помилка введеної формули. <br> Використовується невірна назва формули.", "SSE.Controllers.Main.errorFormulaParsing": "Внутрішня помилка при аналізі формули.", "SSE.Controllers.Main.errorFrmlMaxLength": "Довжина формули перевищує обмеження в 8192 символи. Відредагуйте її і повторіть спробу.", "SSE.Controllers.Main.errorFrmlMaxReference": "Не можна ввести цю формулу, оскільки вона містить занадто багато значень, посилань на клітинки та/або назв.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Довжина текстових значень у формулах не може перевищувати 255 символів.<br>Використовуйте функцію ЗЧЕПИТИ або оператор зчеплення (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Ця функція стосується аркуша, який не існує. <br> Будь ласка, перевірте дані та повторіть спробу.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Неможливо виконати дію для вибраного діапазону комірок.<br>Виберіть діапазон таким чином, щоб перший рядок таблиці містився в одному рядку<br>,а таблиця результату перекрила поточну.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Неможливо виконати дію для вибраного діапазону клітинок.<br>Виберіть діапазон, який не включає інші таблиці.", "SSE.Controllers.Main.errorInvalidRef": "Введіть правильне ім'я для вибору або дійсної посилання для переходу.", "SSE.Controllers.Main.errorKeyEncrypt": "Невідомий дескриптор ключа", "SSE.Controllers.Main.errorKeyExpire": "Ключовий дескриптор закінчився", "SSE.Controllers.Main.errorLabledColumnsPivot": "Щоб створити зведену таблицю, використовуйте дані, організовані у вигляді списку із заголовками стовпчиків.", "SSE.Controllers.Main.errorLoadingFont": "Шрифти не завантажені.<br>Будь ласка, зверніться до адміністратора Сервера документів.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Недійсне посилання на розташування або діапазон даних.", "SSE.Controllers.Main.errorLockedAll": "Операція не може бути виконана, оскільки цей аркуш заблоковано іншим користувачем.", "SSE.Controllers.Main.errorLockedCellPivot": "Ви не можете змінювати дані всередині таблиці.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Лист не можна перейменувати на даний момент, оскільки його перейменує інший користувач", "SSE.Controllers.Main.errorMaxPoints": "Макси<PERSON><PERSON><PERSON><PERSON>на кількість точок у серії для діаграми становить 4096.", "SSE.Controllers.Main.errorMoveRange": "Неможливо змінити частину об'єднаної комірки", "SSE.Controllers.Main.errorMoveSlicerError": "Зрізи таблиць не можна копіювати з однієї робочої книги в іншу. Спробуйте ще раз, виділивши всю таблицю та зрізи.", "SSE.Controllers.Main.errorMultiCellFormula": "Формули масиву з кількома клітинками забороняються в таблицях.", "SSE.Controllers.Main.errorNoDataToParse": "Не виділено дані для аналізу.", "SSE.Controllers.Main.errorOpenWarning": "Одна з формул у файлі перевищує обмеження в 8192 символи.<br>Формула була видалена.", "SSE.Controllers.Main.errorOperandExpected": "Синтак<PERSON>и<PERSON> введеної функції невірний. Будь ласка, перевірте, чи відсутня одна з дужок - '(' або ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Неправильний пароль.<br>Переконайтеся, що вимкнено клавішу CAPS LOCK і використовується правильний регістр.", "SSE.Controllers.Main.errorPasteMaxRange": "Область копіювання та вставки не збігається. <br> Будь ласка, виберіть область з таким самим розміром або натисніть першу комірку у рядку, щоб вставити скопійовані комірки.", "SSE.Controllers.Main.errorPasteMultiSelect": "Ця дія не застосовується до кількох виділених діапазонів.<br>Виділіть один діапазон і повторіть спробу.", "SSE.Controllers.Main.errorPasteSlicerError": "Зрізи таблиць не можна копіювати з однієї робочої книжки до іншої.", "SSE.Controllers.Main.errorPivotGroup": "Виділені об'єкти не можна об'єднати у групу.", "SSE.Controllers.Main.errorPivotOverlap": "Не допускається перекриття звіту зведеної таблиці та таблиці.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Звіт зведеної таблиці було збережено без даних.<br>Для оновлення звіту використовуйте кнопку 'Оновити'.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "На жаль, неможливо одночасно надрукувати більше 1500 сторінок у поточній версії програми. <br> Це обмеження буде видалено в майбутніх випусках.", "SSE.Controllers.Main.errorProcessSaveResult": "Помилка збереження", "SSE.Controllers.Main.errorServerVersion": "Версія редактора була оновлена. Сторінка буде перезавантажена, щоб застосувати зміни.", "SSE.Controllers.Main.errorSessionAbsolute": "Сесія редагування документа закінчилася. Будь ласка, перезавантажте сторінку.", "SSE.Controllers.Main.errorSessionIdle": "Цей документ не редагувався протягом тривалого часу. Перезавантажте сторінку.", "SSE.Controllers.Main.errorSessionToken": "З'єднання з сервером було перервано. Перезавантажте сторінку", "SSE.Controllers.Main.errorSetPassword": "Не вдалось задати пароль.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Посилання на розташування неприпустиме, тому що клітинки знаходяться в різних стовпчиках або рядках. Виділіть клітинки, розташовані в одному стовпчику або одному рядку.", "SSE.Controllers.Main.errorStockChart": "Невірний порядок рядків. Щоб побудувати фондову діаграму, помістіть дані на аркуші в наступному порядку: ціна відкриття, максимальна ціна, мінімальна ціна, ціна закриття.", "SSE.Controllers.Main.errorToken": "Токен безпеки документа не правильно сформовано. <br> Будь ласка, зв'яжіться з адміністратором вашого Сервера документів.", "SSE.Controllers.Main.errorTokenExpire": "Термін дії токену безпеки документа закінчився. <br> Будь ласка, зв'яжіться зі своїм адміністратором сервера документів.", "SSE.Controllers.Main.errorUnexpectedGuid": "Зовнішня помилка. <br> Неочікуваний GUID. Будь ласка, зв'яжіться зі службою підтримки, якщо помилка не зникне.", "SSE.Controllers.Main.errorUpdateVersion": "Версія файлу була змінена. Сторінка буде перезавантажена.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Підключення до Інтернету було відновлено, а версія файлу змінена. <br> Перш ніж продовжувати роботу, потрібно завантажити файл або скопіювати його вміст, щоб переконатися, що нічого не втрачено, а потім перезавантажити цю сторінку.", "SSE.Controllers.Main.errorUserDrop": "На даний момент файл не доступний.", "SSE.Controllers.Main.errorUsersExceed": "Перевищено кількість користувачів, дозволених планом ціноутворення", "SSE.Controllers.Main.errorViewerDisconnect": "Підключення втрачено. Ви, як і раніше, можете переглядати документ, але не зможете звантажити або надрукувати його до відновлення підключення та оновлення сторінки.", "SSE.Controllers.Main.errorWrongBracketsCount": "Помилка введеної формули. <br>Використовується неправильне число дужок .", "SSE.Controllers.Main.errorWrongOperator": "Помилка введеної формули. Використовується неправильний оператор. <br> Будь ласка, виправте помилку.", "SSE.Controllers.Main.errorWrongPassword": "Неправильний пароль", "SSE.Controllers.Main.errRemDuplicates": "Знайдено та видалено повторюваних значень: {0}, залишилося унікальних значень: {1}.", "SSE.Controllers.Main.leavePageText": "У вас є незбережені зміни в цій таблиці. Натисніть \"Залишитися на цій сторінці\", потім \"Зберегти\", щоб зберегти їх. Натисніть \"Покинути цю сторінку\", щоб відхилити всі незбережені зміни.", "SSE.Controllers.Main.leavePageTextOnClose": "Всі незбережені зміни в цій електронній таблиці будуть втрачені.<br> Натисніть кнопку \"Скасувати\", а потім натисніть кнопку \"Зберегти\", щоб зберегти їх. Натисніть кнопку \"OK\", щоб скинути всі незбережені зміни.", "SSE.Controllers.Main.loadFontsTextText": "Завантаження дати...", "SSE.Controllers.Main.loadFontsTitleText": "Дата завантаження", "SSE.Controllers.Main.loadFontTextText": "Завантаження дати...", "SSE.Controllers.Main.loadFontTitleText": "Дата завантаження", "SSE.Controllers.Main.loadImagesTextText": "Завантаження зображень...", "SSE.Controllers.Main.loadImagesTitleText": "Завантаження зображень", "SSE.Controllers.Main.loadImageTextText": "Завантаження зображення...", "SSE.Controllers.Main.loadImageTitleText": "Завантаження зображення", "SSE.Controllers.Main.loadingDocumentTitleText": "Завантаження електронної таблиці", "SSE.Controllers.Main.notcriticalErrorTitle": "Застереження", "SSE.Controllers.Main.openErrorText": "Під час відкриття файлу сталася помилка.", "SSE.Controllers.Main.openTextText": "Відкриття електронної таблиці...", "SSE.Controllers.Main.openTitleText": "Відкриття електронної таблиці", "SSE.Controllers.Main.pastInMergeAreaError": "Неможливо змінити частину об'єднаної комірки", "SSE.Controllers.Main.printTextText": "Друк електронної таблиці...", "SSE.Controllers.Main.printTitleText": "Друк електронної таблиці", "SSE.Controllers.Main.reloadButtonText": "Перезавантажити сторінку", "SSE.Controllers.Main.requestEditFailedMessageText": "Хтось редагує цей документ прямо зараз. Будь-ласка спробуйте пізніше.", "SSE.Controllers.Main.requestEditFailedTitleText": "Доступ заборонено", "SSE.Controllers.Main.saveErrorText": "Під час збереження файлу сталася помилка.", "SSE.Controllers.Main.saveErrorTextDesktop": "Неможливо зберегти або створити цей файл.<br>Можливі причини: <br>1. Файл доступний лише для читання. <br>2. Файл редагується іншими користувачами. <br>3. Диск заповнено або пошкоджено.", "SSE.Controllers.Main.saveTextText": "Збереження електронної таблиці...", "SSE.Controllers.Main.saveTitleText": "Збереження електронної таблиці", "SSE.Controllers.Main.scriptLoadError": "З'єднання занадто повільне, деякі компоненти не вдалося завантажити. Перезавантажте сторінку.", "SSE.Controllers.Main.textAnonymous": "Гість", "SSE.Controllers.Main.textApplyAll": "Застосувати до всіх рівнянь", "SSE.Controllers.Main.textBuyNow": "Відвідати сайт", "SSE.Controllers.Main.textChangesSaved": "Усі зміни збережено", "SSE.Controllers.Main.textClose": "Закрити", "SSE.Controllers.Main.textCloseTip": "Натисніть, щоб закрити підказку", "SSE.Controllers.Main.textConfirm": "Підтвердження", "SSE.Controllers.Main.textContactUs": "Зв'язатися з відділом продажів", "SSE.Controllers.Main.textConvertEquation": "Це рівняння створено у старій версії редактора рівнянь, яка більше не підтримується. Щоб змінити це рівняння, його необхідно перетворити на формат Office Math ML.<br>Перетворити зараз?", "SSE.Controllers.Main.textCustomLoader": "Зверніть увагу, що згідно з умовами ліцензії ви не маєте права змінювати завантажувач.<br>Будь ласка, зв'яжіться з нашим відділом продажів, щоб виконати запит.", "SSE.Controllers.Main.textDisconnect": "З'єднання втрачено", "SSE.Controllers.Main.textFillOtherRows": "Заповнити інші рядки", "SSE.Controllers.Main.textFormulaFilledAllRows": "Формула заповнила {0} рядків з даними. Заповнення інших пустих рядків може забрати кілька хвилин.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Формула заповнила перші {0} рядків. Заповнення інших пустих рядків може забрати кілька хвилин.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Формула заповнила лише перші {0} рядків з даними в цілях економії пам’яті. На цьому листі є ще {1} рядків з даними. Ви можете заповнити їх вручну.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Формула заповнила лише перші {0} рядків в цілях економії пам’яті. Інші рядки на цьому листі не містять даних.", "SSE.Controllers.Main.textGuest": "Гість", "SSE.Controllers.Main.textHasMacros": "Файл містить автоматичні макроси.<br>Запустити макроси?", "SSE.Controllers.Main.textLearnMore": "Детальніше", "SSE.Controllers.Main.textLoadingDocument": "Завантаження електронної таблиці", "SSE.Controllers.Main.textLongName": "Введіть ім'я не довше ніж 128 символів.", "SSE.Controllers.Main.textNeedSynchronize": "Є оновлення", "SSE.Controllers.Main.textNo": "Немає", "SSE.Controllers.Main.textNoLicenseTitle": "Ліцензійне обмеження", "SSE.Controllers.Main.textPaidFeature": "Пла<PERSON>на функція", "SSE.Controllers.Main.textPleaseWait": "Операція може зайняти більше часу, ніж очікувалося. Будь ласка, зачекайте ...", "SSE.Controllers.Main.textReconnect": "З'єднання відновлено", "SSE.Controllers.Main.textRemember": "Запам’ятати мій вибір для всіх файлів", "SSE.Controllers.Main.textRenameError": "Ім'я користувача не повинно бути порожнім.", "SSE.Controllers.Main.textRenameLabel": "Введіть ім'я, що буде використовуватись для спільної роботи", "SSE.Controllers.Main.textShape": "Форма", "SSE.Controllers.Main.textStrict": "суворий режим", "SSE.Controllers.Main.textText": "Текст", "SSE.Controllers.Main.textTryUndoRedo": "Функції Undo / Redo відключені для режиму швидкого редагування. <br> Натисніть кнопку \"Суворий режим\", щоб перейти до режиму суворого редагування, щоб редагувати файл без втручання інших користувачів та відправляти зміни лише після збереження. Ви можете переключатися між режимами спільного редагування за допомогою редактора Додаткові параметри.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Функції скасування і повтору дій відключені для режиму швидкого спільного редагування.", "SSE.Controllers.Main.textYes": "Так", "SSE.Controllers.Main.titleLicenseExp": "Термін дії ліцензії закінчився", "SSE.Controllers.Main.titleServerVersion": "Редактор оновлено", "SSE.Controllers.Main.txtAccent": "Акцент", "SSE.Controllers.Main.txtAll": "(Всі)", "SSE.Controllers.Main.txtArt": "Ваш текст тут", "SSE.Controllers.Main.txtBasicShapes": "Основні форми", "SSE.Controllers.Main.txtBlank": "(пусто)", "SSE.Controllers.Main.txtButtons": "Кнопки", "SSE.Controllers.Main.txtByField": "%1 з %2", "SSE.Controllers.Main.txtCallouts": "Виноски", "SSE.Controllers.Main.txtCharts": "Діаграми", "SSE.Controllers.Main.txtClearFilter": "Очистити фільтр", "SSE.Controllers.Main.txtColLbls": "Назви стовпчиків", "SSE.Controllers.Main.txtColumn": "Стовпчик", "SSE.Controllers.Main.txtConfidential": "Конфіденційно", "SSE.Controllers.Main.txtDate": "Дата", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Назва діграми", "SSE.Controllers.Main.txtEditingMode": "Встановити режим редагування ...", "SSE.Controllers.Main.txtErrorLoadHistory": "Не вдалось завантажити історію", "SSE.Controllers.Main.txtFiguredArrows": "Фігурні стрілки", "SSE.Controllers.Main.txtFile": "<PERSON>а<PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Загальний підсумок", "SSE.Controllers.Main.txtGroup": "Гру<PERSON>а", "SSE.Controllers.Main.txtHours": "Годи<PERSON>и", "SSE.Controllers.Main.txtLines": "Рядки", "SSE.Controllers.Main.txtMath": "Математика", "SSE.Controllers.Main.txtMinutes": "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "Місяці", "SSE.Controllers.Main.txtMultiSelect": "Множинний вибір", "SSE.Controllers.Main.txtOr": "%1 чи %2", "SSE.Controllers.Main.txtPage": "Сторінка", "SSE.Controllers.Main.txtPageOf": "Сторінка %1 з %2", "SSE.Controllers.Main.txtPages": "Сторінок", "SSE.Controllers.Main.txtPreparedBy": "Підготував:", "SSE.Controllers.Main.txtPrintArea": "Область_друку", "SSE.Controllers.Main.txtQuarter": "К-сть", "SSE.Controllers.Main.txtQuarters": "Квартали", "SSE.Controllers.Main.txtRectangles": "Прямокутники", "SSE.Controllers.Main.txtRow": "Рядок", "SSE.Controllers.Main.txtRowLbls": "Назви рядків", "SSE.Controllers.Main.txtSeconds": "Секунди", "SSE.Controllers.Main.txtSeries": "Серії", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Виноска 1 (межа і лінія)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Виноска 2 (межа і лінія)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Виноска 3 (межа і лінія)", "SSE.Controllers.Main.txtShape_accentCallout1": "Виноска 1 (лінія)", "SSE.Controllers.Main.txtShape_accentCallout2": "Виноска 2 (лінія)", "SSE.Controllers.Main.txtShape_accentCallout3": "Виноска 3 (лінія)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Кнопка \"Назад\"", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Кнопка \"На початок\"", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Пуста кнопка", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Кнопка документу", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Кнопка \"В кінець\"", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Кнопка \"Вперед\"", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Кнопка \"Довідка\"", "SSE.Controllers.Main.txtShape_actionButtonHome": "Кнопка \"На головну\"", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Кнопка інформації", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Кнопка відео", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Кнопка повернення", "SSE.Controllers.Main.txtShape_actionButtonSound": "Кнопка звуку", "SSE.Controllers.Main.txtShape_arc": "Дуга", "SSE.Controllers.Main.txtShape_bentArrow": "Зігнута стрілка", "SSE.Controllers.Main.txtShape_bentConnector5": "Колінчате з'єднання", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Колінчате з'єднання зі стрілкою", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Колінчате з'єднання з двома стрілками", "SSE.Controllers.Main.txtShape_bentUpArrow": "Загнута стрілка вгору", "SSE.Controllers.Main.txtShape_bevel": "Багетна рамка", "SSE.Controllers.Main.txtShape_blockArc": "Арка", "SSE.Controllers.Main.txtShape_borderCallout1": "Виноска 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Виноска 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Виноска 3", "SSE.Controllers.Main.txtShape_bracePair": "Подвійні фігурні дужки", "SSE.Controllers.Main.txtShape_callout1": "Виноска 1 (без межі)", "SSE.Controllers.Main.txtShape_callout2": "Виноска 2(без межі)", "SSE.Controllers.Main.txtShape_callout3": "Виноска 3 (без межі)", "SSE.Controllers.Main.txtShape_can": "Циліндр", "SSE.Controllers.Main.txtShape_chevron": "Шевр<PERSON>н", "SSE.Controllers.Main.txtShape_chord": "Хорда", "SSE.Controllers.Main.txtShape_circularArrow": "Кругова стрілка", "SSE.Controllers.Main.txtShape_cloud": "Хмара", "SSE.Controllers.Main.txtShape_cloudCallout": "Виноска хмарка", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>т", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Округлена сполучна лінія", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Округлена лінія зі стрілкою", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Округлена лінія з двома стрілками", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Вигнута вверх стрілка", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Вигнута праворуч стрілка", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Вигнута ліворуч стрілка", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Вигнута вниз стрілка", "SSE.Controllers.Main.txtShape_decagon": "Десятикутник", "SSE.Controllers.Main.txtShape_diagStripe": "Діагональна смуга", "SSE.Controllers.Main.txtShape_diamond": "Ромб", "SSE.Controllers.Main.txtShape_dodecagon": "Дванадцятикутник", "SSE.Controllers.Main.txtShape_donut": "Кільце", "SSE.Controllers.Main.txtShape_doubleWave": "Подв<PERSON>йна хвиля", "SSE.Controllers.Main.txtShape_downArrow": "Стрілка вниз", "SSE.Controllers.Main.txtShape_downArrowCallout": "Виноска зі стрілкою вниз", "SSE.Controllers.Main.txtShape_ellipse": "<PERSON>л<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Вигнута вниз стрічка", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Вигнута вверх стрічка", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: альтернативний процес", "SSE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: зіставлення", "SSE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: з'єднувач", "SSE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: рішення", "SSE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: затримка", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: дисплей", "SSE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: документ", "SSE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: витяг", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: дані", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: внутрішня пам'ять", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: магнітний диск", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: пам'ять з прямим доступом", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: пам'ять з послідовним доступом", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: ручне введення", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: ручне керування", "SSE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: об'єднання", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: декілька документів", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: посилання на іншу сторінку", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: збережені дані", "SSE.Controllers.Main.txtShape_flowChartOr": "Блок-схема: АБО", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: типовий процес", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: підготовка", "SSE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: процес", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: картка", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: перфострічка", "SSE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: сортування", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: вузол суми", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: знак закінчення", "SSE.Controllers.Main.txtShape_foldedCorner": "Загнутий кут", "SSE.Controllers.Main.txtShape_frame": "Рамка", "SSE.Controllers.Main.txtShape_halfFrame": "Половина рамки", "SSE.Controllers.Main.txtShape_heart": "Серце", "SSE.Controllers.Main.txtShape_heptagon": "Семикутник", "SSE.Controllers.Main.txtShape_hexagon": "Шестикутник", "SSE.Controllers.Main.txtShape_homePlate": "П'ятикутник", "SSE.Controllers.Main.txtShape_horizontalScroll": "Горизонтальний сувій", "SSE.Controllers.Main.txtShape_irregularSeal1": "Спалах 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Спалах 2", "SSE.Controllers.Main.txtShape_leftArrow": "Стрілка ліворуч", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Виноска зі стрілкою ліворуч", "SSE.Controllers.Main.txtShape_leftBrace": "Ліва фігурна дужка", "SSE.Controllers.Main.txtShape_leftBracket": "Ліва кругла дужка", "SSE.Controllers.Main.txtShape_leftRightArrow": "Подвійна стрілка ліворуч-праворуч", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Виноска зі стрілками ліворуч-праворуч", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Потр<PERSON>йна стрілка ліворуч-праворуч-вверх", "SSE.Controllers.Main.txtShape_leftUpArrow": "Подвійна стрілка ліворуч-вверх", "SSE.Controllers.Main.txtShape_lightningBolt": "Блискавка", "SSE.Controllers.Main.txtShape_line": "Лінія", "SSE.Controllers.Main.txtShape_lineWithArrow": "Стрілка", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Под<PERSON><PERSON><PERSON>на стрілка", "SSE.Controllers.Main.txtShape_mathDivide": "Ділення", "SSE.Controllers.Main.txtShape_mathEqual": "Дорівнює", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Множення", "SSE.Controllers.Main.txtShape_mathNotEqual": "Не дорівнює", "SSE.Controllers.Main.txtShape_mathPlus": "Плюс", "SSE.Controllers.Main.txtShape_moon": "Місяць", "SSE.Controllers.Main.txtShape_noSmoking": "Заборонено", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Стрілка праворуч з вирізом", "SSE.Controllers.Main.txtShape_octagon": "Восьмикутник", "SSE.Controllers.Main.txtShape_parallelogram": "Паралелограм", "SSE.Controllers.Main.txtShape_pentagon": "П'ятикутник", "SSE.Controllers.Main.txtShape_pie": "Сектор круга", "SSE.Controllers.Main.txtShape_plaque": "Підписати", "SSE.Controllers.Main.txtShape_plus": "Плюс", "SSE.Controllers.Main.txtShape_polyline1": "Крива", "SSE.Controllers.Main.txtShape_polyline2": "Довільна форма", "SSE.Controllers.Main.txtShape_quadArrow": "Зчетверена стрілка", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Виноска з чотирма стрілками", "SSE.Controllers.Main.txtShape_rect": "Прямокутник", "SSE.Controllers.Main.txtShape_ribbon": "Стрічка вниз", "SSE.Controllers.Main.txtShape_ribbon2": "Стрічка вверх", "SSE.Controllers.Main.txtShape_rightArrow": "Стрілка праворуч", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Виноска зі стрілкою праворуч", "SSE.Controllers.Main.txtShape_rightBrace": "Права фігурна дужка", "SSE.Controllers.Main.txtShape_rightBracket": "Права дужка", "SSE.Controllers.Main.txtShape_round1Rect": "Прямокутник з одним закругленим кутом", "SSE.Controllers.Main.txtShape_round2DiagRect": "Прямокутник із двома закругленими протилежними кутами", "SSE.Controllers.Main.txtShape_round2SameRect": "Прямокутник із двома закругленими сусідніми кутами", "SSE.Controllers.Main.txtShape_roundRect": "Прямокутник з закругленими кутами", "SSE.Controllers.Main.txtShape_rtTriangle": "Прямокутний трикутник", "SSE.Controllers.Main.txtShape_smileyFace": "Усміхнене обличчя", "SSE.Controllers.Main.txtShape_snip1Rect": "Прямокутник з одним вирізаним кутом", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Прямокутник з двома вирізаними протилежними кутами", "SSE.Controllers.Main.txtShape_snip2SameRect": "Прямокутник з двома вирізаними сусідніми кутами", "SSE.Controllers.Main.txtShape_snipRoundRect": "Прямокутник з одним вирізаним закругленим кутом", "SSE.Controllers.Main.txtShape_spline": "Крива", "SSE.Controllers.Main.txtShape_star10": "10-кінцева зірка", "SSE.Controllers.Main.txtShape_star12": "12-кінцева зірка", "SSE.Controllers.Main.txtShape_star16": "16-кінцева зірка", "SSE.Controllers.Main.txtShape_star24": "24-кінцева зірка", "SSE.Controllers.Main.txtShape_star32": "32-кінцева зірка", "SSE.Controllers.Main.txtShape_star4": "4-кінцева зірка", "SSE.Controllers.Main.txtShape_star5": "5-кінцева зірка", "SSE.Controllers.Main.txtShape_star6": "6-кінцева зірка", "SSE.Controllers.Main.txtShape_star7": "7-кінцева зірка", "SSE.Controllers.Main.txtShape_star8": "8-кінцева зірка", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Штрихпунктирна стрілка праворуч", "SSE.Controllers.Main.txtShape_sun": "Сонце", "SSE.Controllers.Main.txtShape_teardrop": "Кра<PERSON>ля", "SSE.Controllers.Main.txtShape_textRect": "Напис", "SSE.Controllers.Main.txtShape_trapezoid": "Трапеція", "SSE.Controllers.Main.txtShape_triangle": "Трикутник", "SSE.Controllers.Main.txtShape_upArrow": "Стрілка вгору", "SSE.Controllers.Main.txtShape_upArrowCallout": "Виноска зі стрілкою вверх", "SSE.Controllers.Main.txtShape_upDownArrow": "Подвійна стрілка вверх-вниз", "SSE.Controllers.Main.txtShape_uturnArrow": "Розвернута стрілка", "SSE.Controllers.Main.txtShape_verticalScroll": "Вертикальний сувій", "SSE.Controllers.Main.txtShape_wave": "Хв<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Овальна виноска", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Прямокутна виноска", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Закруглена прямокутна виноска", "SSE.Controllers.Main.txtStarsRibbons": "Зірки та стрічки", "SSE.Controllers.Main.txtStyle_Bad": "Поганий", "SSE.Controllers.Main.txtStyle_Calculation": "Розрахунок", "SSE.Controllers.Main.txtStyle_Check_Cell": "Перевірити комірку", "SSE.Controllers.Main.txtStyle_Comma": "Кома", "SSE.Controllers.Main.txtStyle_Currency": "Валюта", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Пояснювальний текст", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Заголовок 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Заголовок 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Заголовок 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Заголовок 4", "SSE.Controllers.Main.txtStyle_Input": "Вхідний", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Пов'язана комірки", "SSE.Controllers.Main.txtStyle_Neutral": "Нейтральний", "SSE.Controllers.Main.txtStyle_Normal": "Нормальний", "SSE.Controllers.Main.txtStyle_Note": "Примітка", "SSE.Controllers.Main.txtStyle_Output": "Вихідні дані", "SSE.Controllers.Main.txtStyle_Percent": "Відсоток", "SSE.Controllers.Main.txtStyle_Title": "Назва", "SSE.Controllers.Main.txtStyle_Total": "Загалом", "SSE.Controllers.Main.txtStyle_Warning_Text": "Текст попередження", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "Таблиця", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "Розблокувати", "SSE.Controllers.Main.txtUnlockRange": "Розблокувати діапазон", "SSE.Controllers.Main.txtUnlockRangeDescription": "Введіть пароль, щоб змінити цей діапазон:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Діа<PERSON><PERSON><PERSON><PERSON><PERSON>, який ви намагаєтесь змінити, захищений за допомогою пароля.", "SSE.Controllers.Main.txtValues": "Значення", "SSE.Controllers.Main.txtXAxis": "X Ось", "SSE.Controllers.Main.txtYAxis": "Y ось", "SSE.Controllers.Main.txtYears": "Роки", "SSE.Controllers.Main.unknownErrorText": "Невідома помилка.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Ваш браузер не підтримується", "SSE.Controllers.Main.uploadDocExtMessage": "Невідомий формат документу.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Жодного документу не завантажено.", "SSE.Controllers.Main.uploadDocSizeMessage": "Перевищений максимальний розмір документу", "SSE.Controllers.Main.uploadImageExtMessage": "Невідомий формат зображення.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Не завантажено жодного зображення.", "SSE.Controllers.Main.uploadImageSizeMessage": "Занадто велике зображення. Максимальний розмір – 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Завантаження зображення...", "SSE.Controllers.Main.uploadImageTitleText": "Завантаження зображення", "SSE.Controllers.Main.waitText": "Будь ласка, зачекайте...", "SSE.Controllers.Main.warnBrowserIE9": "Програма має низькі можливості для IE9. Використовувати IE10 або вище", "SSE.Controllers.Main.warnBrowserZoom": "Налаштування масштабу вашого браузера не підтримується повністю. Змініть стандартний масштаб, натиснувши Ctrl + 0.", "SSE.Controllers.Main.warnLicenseExceeded": "Ви досягли ліміту одночасного підключення до редакторів %1. Цей документ буде відкритий лише для перегляду.<br>Щоб дізнатися більше, зв’яжіться з адміністратором.", "SSE.Controllers.Main.warnLicenseExp": "Термін дії вашої ліцензії минув. <br> Будь ласка, оновіть свою ліцензію та оновіть сторінку.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Термін дії ліцензії закінчився.<br>Немає доступ до функцій редагування документів.<br>Будь ласка, зверніться до адміністратора.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Потрібно поновити ліцензію.<br>У вас обмежений доступ до функцій редагування документів.<br>Будь ласка, зверніться до свого адміністратора, щоб отримати повний доступ", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Ви досягли ліміту на кількість користувачів редакторів %1.<br>Зв'яжіться з адміністратором, щоб дізнатися більше.", "SSE.Controllers.Main.warnNoLicense": "Ви досягли ліміту на одночасне підключення до редакторів %1. Цей документ буде відкрито для перегляду.<br>Напишіть у відділ продажу %1, щоб обговорити індивідуальні умови ліцензування.", "SSE.Controllers.Main.warnNoLicenseUsers": "Ви досягли ліміту на одночасне підключення до редакторів %1.<br>Напишіть у відділ продаж %1, для обговорення індивідуальних умов ліцензування.", "SSE.Controllers.Main.warnProcessRightsChange": "Вам відмовлено у наданні права редагувати файл.", "SSE.Controllers.Print.strAllSheets": "Всі аркуші", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON><PERSON><PERSON> стовпчик", "SSE.Controllers.Print.textFirstRow": "Перший рядок", "SSE.Controllers.Print.textFrozenCols": "Закріплені стовпчики", "SSE.Controllers.Print.textFrozenRows": "Закріплені рядки", "SSE.Controllers.Print.textInvalidRange": "ПОМИЛКА! Неприпустимий діапазон клітинок", "SSE.Controllers.Print.textNoRepeat": "Не повторювати", "SSE.Controllers.Print.textRepeat": "Повторювати...", "SSE.Controllers.Print.textSelectRange": "Вибір діапазону", "SSE.Controllers.Print.textWarning": "Застереження", "SSE.Controllers.Print.txtCustom": "Користувальницький", "SSE.Controllers.Print.warnCheckMargings": "Поля неправильні", "SSE.Controllers.Statusbar.errorLastSheet": "Робоча книга повинна мати щонайменше один видимий аркуш.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Неможливо видалити робочий аркуш.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>З'єднання втрачено</b><br>Спроба підключення. Перевірте налаштування підключення.", "SSE.Controllers.Statusbar.textSheetViewTip": "Ви перебуваєте в режимі перегляду листа. Фільтри та сортування видно тільки вам і тим, хто також знаходиться в цьому перегляді.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Ви перебуваєте в режимі перегляду листа. Фільтри видно тільки вам і тим, хто також знаходиться в цьому перегляді.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Вибраний робочий лист може містити дані. Ви дійсно хочете продовжити?", "SSE.Controllers.Statusbar.zoomText": "Зб<PERSON>л<PERSON><PERSON>и<PERSON><PERSON> {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Шри<PERSON><PERSON>, який ви збираєтеся зберегти, недоступний на поточному пристрої. <br> Текстовий стиль відображатиметься за допомогою одного з системних шрифтів, збережений шрифт буде використовуватися, коли він буде доступний. <br> Ви хочете продовжити ?", "SSE.Controllers.Toolbar.errorComboSeries": "Для створення комбінованої діаграми виберіть щонайменше два ряди даних.", "SSE.Controllers.Toolbar.errorMaxRows": "ПОМИЛКА! Максимальна кількість даних на кожну діаграму становить 255", "SSE.Controllers.Toolbar.errorStockChart": "Невірний порядок рядків. Щоб побудувати фондову діаграму, помістіть дані на аркуші в наступному порядку: ціна відкриття, максимальна ціна, мінімальна ціна, ціна закриття.", "SSE.Controllers.Toolbar.textAccent": "Акценти", "SSE.Controllers.Toolbar.textBracket": "дужки", "SSE.Controllers.Toolbar.textDirectional": "Напрямок", "SSE.Controllers.Toolbar.textFontSizeErr": "Введене значення невірно. <br> Будь ласка, введіть числове значення від 1 до 409", "SSE.Controllers.Toolbar.textFraction": "Дроби", "SSE.Controllers.Toolbar.textFunction": "Функції", "SSE.Controllers.Toolbar.textIndicator": "Індикатори", "SSE.Controllers.Toolbar.textInsert": "Вставити", "SSE.Controllers.Toolbar.textIntegral": "Інтеграли", "SSE.Controllers.Toolbar.textLargeOperator": "Великі оператори", "SSE.Controllers.Toolbar.textLimitAndLog": "Межі та логарифми", "SSE.Controllers.Toolbar.textLongOperation": "Довга операція", "SSE.Controllers.Toolbar.textMatrix": "Ма<PERSON>р<PERSON>ц<PERSON>", "SSE.Controllers.Toolbar.textOperator": "Оператори", "SSE.Controllers.Toolbar.textPivot": "Зведена таблиця", "SSE.Controllers.Toolbar.textRadical": "Радикали", "SSE.Controllers.Toolbar.textRating": "Оцінки", "SSE.Controllers.Toolbar.textRecentlyUsed": "Останні використані", "SSE.Controllers.Toolbar.textScript": "Рукописи", "SSE.Controllers.Toolbar.textShapes": "Фігури", "SSE.Controllers.Toolbar.textSymbols": "Символи", "SSE.Controllers.Toolbar.textWarning": "Застереження", "SSE.Controllers.Toolbar.txtAccent_Accent": "Гострий", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Стрілка праворуч вліво вище", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Стрілка вліво вгору", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Стрілка праворуч вгорі", "SSE.Controllers.Toolbar.txtAccent_Bar": "Вставити", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Підкреслення", "SSE.Controllers.Toolbar.txtAccent_BarTop": "риса", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Формула (з місцем розташування)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Формула(приклад)", "SSE.Controllers.Toolbar.txtAccent_Check": "Перевірити", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON>ід дужкою", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Перевершити", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Вектор А", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC з надбавкою", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "X XOR у з рискою", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Три крапки", "SSE.Controllers.Toolbar.txtAccent_DDot": "Двокрапка", "SSE.Controllers.Toolbar.txtAccent_Dot": "Крапка", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Подвійна риска", "SSE.Controllers.Toolbar.txtAccent_Grave": "Грав<PERSON>ювати", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Згрупувати символи нижче", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Згрупувати символи вище", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Гар<PERSON>ун Вище Ліворуч", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Правор<PERSON><PERSON> Гарпун зверху", "SSE.Controllers.Toolbar.txtAccent_Hat": "Капелюх", "SSE.Controllers.Toolbar.txtAccent_Smile": "Короткий", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Тільда", "SSE.Controllers.Toolbar.txtBracket_Angle": "дужки", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Дужки з роздільниками", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Дужки з роздільниками", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Curve": "дужки", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Дужки з роздільниками", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Регістор символів (дві обставини)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Регістор символів (Три обставини)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Стек об'єкта", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Стек об'єкта", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Приклад регістру символів", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Биномиальный коефіцієнт", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Биномиальный коефіцієнт", "SSE.Controllers.Toolbar.txtBracket_Line": "дужки", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "дужки", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_LowLim": "дужки", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Round": "дужки", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Дужки з роздільниками", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Square": "дужки", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "дужки", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "дужки", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "дужки", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "дужки", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_UppLim": "дужки", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Єдина дужка", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Єдина дужка", "SSE.Controllers.Toolbar.txtDeleteCells": "Видалити клітинки", "SSE.Controllers.Toolbar.txtExpand": "Розгорнути та сортувати", "SSE.Controllers.Toolbar.txtExpandSort": "Дані після позначеного діапазону не буде впорядковано. Розширити вибір, щоб включити сусідні дані або продовжити впорядковування тільки щойно вибраних комірок?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Обмотана фракція", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Диференціальний", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Диференціальний", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Диференціальний", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Диференціальний", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Лін<PERSON>йна фракція", "SSE.Controllers.Toolbar.txtFractionPi_2": "Пі більше 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Мала частка", "SSE.Controllers.Toolbar.txtFractionVertical": "Укладена фракція", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Зворотна косинальна функція", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Гіперболічна зворотна функція косинуса", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Зворотна котангентна функція", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Гіперболічна зворотна котангенсована функція", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Зворотна косекантна функція", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Гіперболічна зворотна функція косеканта", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Зворотна секретна функція", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Гіпербол<PERSON>чна зворотна відсіюча функція", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Інверсна функція синуса", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Гіперболічна інверсна функція синуса", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Інверсна функція тангенса", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Гіперболічна інверсна функція тангента", "SSE.Controllers.Toolbar.txtFunction_Cos": "Функція косинуса", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Гіпербол<PERSON>чна косінусна функція", "SSE.Controllers.Toolbar.txtFunction_Cot": "Функція котангенса", "SSE.Controllers.Toolbar.txtFunction_Coth": "Гіперболічна функція котангенса", "SSE.Controllers.Toolbar.txtFunction_Csc": "Косекантна функція", "SSE.Controllers.Toolbar.txtFunction_Csch": "Гіперболічна косекантна функція", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Сине тета", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Формула тангенсу", "SSE.Controllers.Toolbar.txtFunction_Sec": "Секретна функція", "SSE.Controllers.Toolbar.txtFunction_Sech": "Гіперболічна секретна функція", "SSE.Controllers.Toolbar.txtFunction_Sin": "Функція синуса", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Гіперболічна синусація", "SSE.Controllers.Toolbar.txtFunction_Tan": "Функція тангенсу", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Гіперболічна дотична функція", "SSE.Controllers.Toolbar.txtInsertCells": "Вставити клітини", "SSE.Controllers.Toolbar.txtIntegral": "Інтеграл", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Диференціальна тета", "SSE.Controllers.Toolbar.txtIntegral_dx": "Диференціальний  x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Диференціальний y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Інтеграл", "SSE.Controllers.Toolbar.txtIntegralDouble": "Подвійний інтеграл", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Подвійний інтеграл", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Подвійний інтеграл", "SSE.Controllers.Toolbar.txtIntegralOriented": "Контурний інтеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурний інтеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Поверхневий інтеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Поверхневий інтеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Поверхневий інтеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурний інтеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Обсяг інтегралу", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Обсяг інтегралу", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Обсяг інтегралу", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Інтеграл", "SSE.Controllers.Toolbar.txtIntegralTriple": "Потрійний інтеграл", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Потрійний інтеграл", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Потрійний інтеграл", "SSE.Controllers.Toolbar.txtInvalidRange": "ПОМИЛКА! Недійсний діапазон комірок", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Трикутна призма", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Трикутна призма", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Трикутна призма", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Трикутна призма", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Трикутна призма", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Супутній продукт", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Супутній продукт", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Супутній продукт", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Супутній продукт", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Супутній продукт", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Продукт", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Об'єднання", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Пере<PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Пере<PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Пере<PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Пере<PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Пере<PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Продукт", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Продукт", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Продукт", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Продукт", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Продукт", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Підсумки", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Об'єднання", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Об'єднання", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Об'єднання", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Об'єднання", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Об'єднання", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Прик<PERSON>ад ліміту", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Максимальний приклад", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Природний логарифм", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Лог<PERSON><PERSON><PERSON><PERSON>м", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Лог<PERSON><PERSON><PERSON><PERSON>м", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Максимум", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Мін<PERSON><PERSON>ум", "SSE.Controllers.Toolbar.txtLockSort": "Виявлено дані поруч із виділеним діапазоном, але у вас недостатньо прав для зміни цих клітинок.<br>Ви бажаєте продовжити роботу з виділеним діапазоном?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Пуста матриця з дужками", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Пуста матриця з дужками", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Пуста матриця з дужками", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Пуста матриця з дужками", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Порожня матриця", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Базові точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Точки середньої лінії", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Діагональні точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Вертикальні крапки", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Розріджена матриця", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Розріджена матриця", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Індетифікація матриці", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 матриця ідентичності", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 матриця ідентичності", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 матриця ідентичності", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Стрілка праворуч вліво нижче", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрілка праворуч вліво вище", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрілка вліво внизу", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрілка вліво вгору", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Стрілка праворуч внизу", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрілка праворуч вгорі", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Колона рівна", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON>и<PERSON><PERSON>д", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Дельта врожайності", "SSE.Controllers.Toolbar.txtOperator_Definition": "Рівність за визначенням", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Дельта дорівнює до", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Стрілка праворуч вліво нижче", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Стрілка праворуч вліво вище", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрілка вліво внизу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрілка вліво вгору", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Стрілка праворуч внизу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрілка праворуч вгорі", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON> Рівний", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Мінус рівний", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс Рівний", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Вимірюється по", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Радикальний", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Радикальний", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Квадратне коріння зі ступенем", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Куб<PERSON>чний корінь", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Радикальна зі ступенем", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Квадратне коріння", "SSE.Controllers.Toolbar.txtScriptCustom_1": "Рукопис", "SSE.Controllers.Toolbar.txtScriptCustom_2": "Рукопис", "SSE.Controllers.Toolbar.txtScriptCustom_3": "Рукопис", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Рукопис", "SSE.Controllers.Toolbar.txtScriptSub": "Підрядковий", "SSE.Controllers.Toolbar.txtScriptSubSup": "Підрядковий індекс - верхній індекс", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Ліворуч - підрядковий індекс", "SSE.Controllers.Toolbar.txtScriptSup": "Надрядковий", "SSE.Controllers.Toolbar.txtSorting": "Сортування", "SSE.Controllers.Toolbar.txtSortSelected": "Сортувати вибрано", "SSE.Controllers.Toolbar.txtSymbol_about": "Приблизно", "SSE.Controllers.Toolbar.txtSymbol_additional": "Доповлення", "SSE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Альфа", "SSE.Controllers.Toolbar.txtSymbol_approx": "Майже рівний", "SSE.Controllers.Toolbar.txtSymbol_ast": "Оператор зірочка", "SSE.Controllers.Toolbar.txtSymbol_beta": "Бета", "SSE.Controllers.Toolbar.txtSymbol_beth": "Закладати", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Опера<PERSON><PERSON><PERSON> Bullet ", "SSE.Controllers.Toolbar.txtSymbol_cap": "Пере<PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Коріння Куба", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Горизонтальний Еліпсіс середньої лінії", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Градуси Цельсія", "SSE.Controllers.Toolbar.txtSymbol_chi": "Чи", "SSE.Controllers.Toolbar.txtSymbol_cong": "Приблизно рівний до", "SSE.Controllers.Toolbar.txtSymbol_cup": "Об'єднання", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Нижня права діагональ еліпсісу", "SSE.Controllers.Toolbar.txtSymbol_degree": "Гра<PERSON>у<PERSON>и", "SSE.Controllers.Toolbar.txtSymbol_delta": "Дельта", "SSE.Controllers.Toolbar.txtSymbol_div": "Знак поділу", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Стрілка вниз", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Порожній набір", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Епс<PERSON>лон", "SSE.Controllers.Toolbar.txtSymbol_equals": "Р<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Ідентична до", "SSE.Controllers.Toolbar.txtSymbol_eta": "Ета", "SSE.Controllers.Toolbar.txtSymbol_exists": "Тут існує", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Факторіал", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градуси за Фаренгейтом", "SSE.Controllers.Toolbar.txtSymbol_forall": "Для всього", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Гамма", "SSE.Controllers.Toolbar.txtSymbol_geq": "Більше ніж або дорівнює", "SSE.Controllers.Toolbar.txtSymbol_gg": "Набагато більше, ніж", "SSE.Controllers.Toolbar.txtSymbol_greater": "Більше ніж ", "SSE.Controllers.Toolbar.txtSymbol_in": "Елемент", "SSE.Controllers.Toolbar.txtSymbol_inc": "Збільшення", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Нескінченність", "SSE.Controllers.Toolbar.txtSymbol_iota": "Йота", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Каппа", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Лямбда", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Стрілка ліворуч", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрілка зліва направо", "SSE.Controllers.Toolbar.txtSymbol_leq": "Менше або дорівнює", "SSE.Controllers.Toolbar.txtSymbol_less": "Мен<PERSON>е ніж", "SSE.Controllers.Toolbar.txtSymbol_ll": "Значно менше, ніж", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON><PERSON><PERSON> Плюс", "SSE.Controllers.Toolbar.txtSymbol_mu": "Мю", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "SSE.Controllers.Toolbar.txtSymbol_neq": "Не відповідний до", "SSE.Controllers.Toolbar.txtSymbol_ni": "Містить в якості учасника", "SSE.Controllers.Toolbar.txtSymbol_not": "Не підписаний", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Там не існує", "SSE.Controllers.Toolbar.txtSymbol_nu": "ню", "SSE.Controllers.Toolbar.txtSymbol_o": "Омікрон", "SSE.Controllers.Toolbar.txtSymbol_omega": "Омега", "SSE.Controllers.Toolbar.txtSymbol_partial": "Частковий диференціал", "SSE.Controllers.Toolbar.txtSymbol_percent": "відсотковий вміст", "SSE.Controllers.Toolbar.txtSymbol_phi": "фі", "SSE.Controllers.Toolbar.txtSymbol_pi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "SSE.Controllers.Toolbar.txtSymbol_pm": "Плюс мінус", "SSE.Controllers.Toolbar.txtSymbol_propto": "Пропорційно до", "SSE.Controllers.Toolbar.txtSymbol_psi": "Пс<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Четвертий корінь", "SSE.Controllers.Toolbar.txtSymbol_qed": "Кінець показника", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Вгору справа Діагональ Еліпсіс", "SSE.Controllers.Toolbar.txtSymbol_rho": "ро", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Права стрілочка", "SSE.Controllers.Toolbar.txtSymbol_sigma": "сігма", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Радикал<PERSON><PERSON><PERSON> знак", "SSE.Controllers.Toolbar.txtSymbol_tau": "тау", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Тому", "SSE.Controllers.Toolbar.txtSymbol_theta": "тета", "SSE.Controllers.Toolbar.txtSymbol_times": "Знак множення", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Стрілочка вгору", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "іпсилон", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Е<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Варіант", "SSE.Controllers.Toolbar.txtSymbol_varphi": "варіант фі", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Пі варіант", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ро варіант", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "сігма варіант", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "тета варіант", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Вертикальний Еліпсіс", "SSE.Controllers.Toolbar.txtSymbol_xsi": "ксі", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Зета", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Стиль таблиці: темний", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Стиль таблиці: світлий", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Стиль таблиці: середній", "SSE.Controllers.Toolbar.warnLongOperation": "Операція, яку ви збираєтеся виконати, може зайняти досить багато часу. <br> Ви впевнені, що хочете продовжити?", "SSE.Controllers.Toolbar.warnMergeLostData": "Дані лише верхньої лівої комірки залишаться в об'єднаній комірці.<br>Продовжити?", "SSE.Controllers.Viewport.textFreezePanes": "Закріпити області", "SSE.Controllers.Viewport.textFreezePanesShadow": "Показувати тінь для закріплених областей", "SSE.Controllers.Viewport.textHideFBar": "Приховати рядок формул", "SSE.Controllers.Viewport.textHideGridlines": "Сховати лінії сітки", "SSE.Controllers.Viewport.textHideHeadings": "Сховати заголовки", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Десятковий роздільник", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Роздільник тисяч", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Налаштування визначення числових даних", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Кла<PERSON>и<PERSON>ікатор тексту", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Додаткові параметри", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(немає)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Спеціальний фільтр", "SSE.Views.AutoFilterDialog.textAddSelection": "Додати поточний вибір для фільтрації", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Виділити все", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Вибрати Усі результати пошуку", "SSE.Views.AutoFilterDialog.textWarning": "Застереження", "SSE.Views.AutoFilterDialog.txtAboveAve": "Вище середнього", "SSE.Views.AutoFilterDialog.txtBegins": "почати з...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Нижче середнього", "SSE.Views.AutoFilterDialog.txtBetween": "Між...", "SSE.Views.AutoFilterDialog.txtClear": "Очистити", "SSE.Views.AutoFilterDialog.txtContains": "Містить...", "SSE.Views.AutoFilterDialog.txtEmpty": "Встановіть фільтр комірки", "SSE.Views.AutoFilterDialog.txtEnds": "Закінчується з...", "SSE.Views.AutoFilterDialog.txtEquals": "Дорівнює...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Фільтр за кольором комірки", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Фільтр за кольором шрифту", "SSE.Views.AutoFilterDialog.txtGreater": "Більше ніж...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Більше або дорівнює ...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Фільтр підписів", "SSE.Views.AutoFilterDialog.txtLess": "менше ніж...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Менше або дорівнює...", "SSE.Views.AutoFilterDialog.txtNotBegins": "не починайте з...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Не між...", "SSE.Views.AutoFilterDialog.txtNotContains": "Не містить...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Не закінчується з...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Не рівний...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Номер фільтру", "SSE.Views.AutoFilterDialog.txtReapply": "Повторно застосуйте", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Впорядкувати за кольором комірок", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Сортувати за кольором шрифту", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Сортувати від найвищого до найнижчого", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Сортувати від найнижчого до найвищого", "SSE.Views.AutoFilterDialog.txtSortOption": "Додаткові параметри сортування...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Текстовий фільтр", "SSE.Views.AutoFilterDialog.txtTitle": "Фільтр", "SSE.Views.AutoFilterDialog.txtTop10": "Топ-10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Фільтр значень", "SSE.Views.AutoFilterDialog.warnFilterError": "Щоб застосувати фільтр за значенням, область значень повинна мати хоча б одне поле.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Ви повинні вибрати принаймні одне значення", "SSE.Views.CellEditor.textManager": "Менеджер імен", "SSE.Views.CellEditor.tipFormula": "Вставити функцію", "SSE.Views.CellRangeDialog.errorMaxRows": "ПОМИЛКА! Максимальна кількість даних на кожну діаграму становить 255", "SSE.Views.CellRangeDialog.errorStockChart": "Невірний порядок рядків. Щоб побудувати фондову діаграму, помістіть дані на аркуші в наступному порядку: ціна відкриття, максимальна ціна, мінімальна ціна, ціна закриття.", "SSE.Views.CellRangeDialog.txtEmpty": "Це поле є обов'язковим", "SSE.Views.CellRangeDialog.txtInvalidRange": "ПОМИЛКА! Недійсний діапазон комірок", "SSE.Views.CellRangeDialog.txtTitle": "Виберати діапазон даних", "SSE.Views.CellSettings.strShrink": "Авто<PERSON>ідбір ширини", "SSE.Views.CellSettings.strWrap": "Перенесення тексту", "SSE.Views.CellSettings.textAngle": "<PERSON>а<PERSON><PERSON>л", "SSE.Views.CellSettings.textBackColor": "Кол<PERSON>р фону", "SSE.Views.CellSettings.textBackground": "Кол<PERSON>р фону", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Стиль меж", "SSE.Views.CellSettings.textClearRule": "Видалити правила", "SSE.Views.CellSettings.textColor": "Заливка кольором", "SSE.Views.CellSettings.textColorScales": "Шкали кольору", "SSE.Views.CellSettings.textCondFormat": "Умовне форматування", "SSE.Views.CellSettings.textControl": "Керування текстами", "SSE.Views.CellSettings.textDataBars": "Гістограми", "SSE.Views.CellSettings.textDirection": "Напрямок", "SSE.Views.CellSettings.textFill": "Заливка", "SSE.Views.CellSettings.textForeground": "Колір переднього плану", "SSE.Views.CellSettings.textGradient": "Градієнти", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Градієнтна заливка", "SSE.Views.CellSettings.textIndent": "Відступ", "SSE.Views.CellSettings.textItems": "Елементи", "SSE.Views.CellSettings.textLinear": "Л<PERSON>н<PERSON><PERSON>ний", "SSE.Views.CellSettings.textManageRule": "Керування правилами", "SSE.Views.CellSettings.textNewRule": "Нове правило", "SSE.Views.CellSettings.textNoFill": "Без заливки", "SSE.Views.CellSettings.textOrientation": "Орієнтація тексту", "SSE.Views.CellSettings.textPattern": "Візерунок", "SSE.Views.CellSettings.textPatternFill": "Візерунок", "SSE.Views.CellSettings.textPosition": "Положення", "SSE.Views.CellSettings.textRadial": "Раді<PERSON>л<PERSON>ний", "SSE.Views.CellSettings.textSelectBorders": "Виберіть межі, до яких потрібно застосувати вибраний стиль", "SSE.Views.CellSettings.textSelection": "З поточного виділеного фрагмента", "SSE.Views.CellSettings.textThisPivot": "З цієї зведеної таблиці", "SSE.Views.CellSettings.textThisSheet": "З цього листа", "SSE.Views.CellSettings.textThisTable": "З цієї таблиці", "SSE.Views.CellSettings.tipAddGradientPoint": "Додати точку градієнта", "SSE.Views.CellSettings.tipAll": "Встановити зовнішню межу та всі внутрішні лінії", "SSE.Views.CellSettings.tipBottom": "Встановити лише зовнішню нижню межу", "SSE.Views.CellSettings.tipDiagD": "Задати діагональну межу зверху донизу", "SSE.Views.CellSettings.tipDiagU": "Задати діагональну межу знизу нагору", "SSE.Views.CellSettings.tipInner": "Встановити лише внутрішні лінії", "SSE.Views.CellSettings.tipInnerHor": "Встановити лише горизонтальні внутрішні лінії", "SSE.Views.CellSettings.tipInnerVert": "Встановити лише вертикальні внутрішні лінії", "SSE.Views.CellSettings.tipLeft": "Встановити лише зовнішню ліву межу", "SSE.Views.CellSettings.tipNone": "Встановити без меж", "SSE.Views.CellSettings.tipOuter": "Встановити лише зовнішню межу", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Видалити точку градієнта", "SSE.Views.CellSettings.tipRight": "Встановити лише зовнішню праву межу", "SSE.Views.CellSettings.tipTop": "Встановити лише зовнішню верхню межу", "SSE.Views.ChartDataDialog.errorInFormula": "Помилка у введеній формулі.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Неприпустиме посилання. Посилання має вказувати на відкритий лист.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Макси<PERSON><PERSON><PERSON><PERSON>на кількість точок у серії для діаграми становить 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Макси<PERSON><PERSON><PERSON><PERSON>на кількість даних для однієї діаграми: 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Неприпустиме посилання. Посилання для назв, значень, розмірів або міток даних має вказувати на одну клітинку, рядок або стовпчик.", "SSE.Views.ChartDataDialog.errorNoValues": "Для створення діаграми необхідно, щоб ряд містив хоча б одне значення.", "SSE.Views.ChartDataDialog.errorStockChart": "Неправильний порядок рядків. Щоб створити біржову діаграму, розташуйте дані на листі в наступному порядку: ціна відкриття, максимальна ціна, мінімальна ціна, ціна закриття.", "SSE.Views.ChartDataDialog.textAdd": "Додати", "SSE.Views.ChartDataDialog.textCategory": "Підписи горизонтальної осі (категорії)", "SSE.Views.ChartDataDialog.textData": "Діапазон даних для діаграми", "SSE.Views.ChartDataDialog.textDelete": "Видалити", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "Редагувати", "SSE.Views.ChartDataDialog.textInvalidRange": "Неприпустимий діапазон клітинок", "SSE.Views.ChartDataDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.ChartDataDialog.textSeries": "Елементи легенди (рядки)", "SSE.Views.ChartDataDialog.textSwitch": "Перемикнути рядок/стовпчик", "SSE.Views.ChartDataDialog.textTitle": "Дані діаграми", "SSE.Views.ChartDataDialog.textUp": "Ввер<PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Помилка у введеній формулі.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Неприпустиме посилання. Посилання має вказувати на відкритий лист.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Макси<PERSON><PERSON><PERSON><PERSON>на кількість точок у серії для діаграми становить 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Макси<PERSON><PERSON><PERSON><PERSON>на кількість даних для однієї діаграми: 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Неприпустиме посилання. Посилання для назв, значень, розмірів або міток даних має вказувати на одну клітинку, рядок або стовпчик.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Для створення діаграми необхідно, щоб ряд містив хоча б одне значення.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Неправильний порядок рядків. Щоб створити біржову діаграму, розташуйте дані на листі в наступному порядку: ціна відкриття, максимальна ціна, мінімальна ціна, ціна закриття.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Неприпустимий діапазон клітинок", "SSE.Views.ChartDataRangeDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Діапазон підписів осі", "SSE.Views.ChartDataRangeDialog.txtChoose": "Виберіть діапазон", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Назва серії", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Підписи осі", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Змінити ряд", "SSE.Views.ChartDataRangeDialog.txtValues": "Значення", "SSE.Views.ChartDataRangeDialog.txtXValues": "Значення Х", "SSE.Views.ChartDataRangeDialog.txtYValues": "Значення Y", "SSE.Views.ChartSettings.strLineWeight": "Line Weight", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Шабл<PERSON>н", "SSE.Views.ChartSettings.textAdvanced": "Показати додаткові налаштування", "SSE.Views.ChartSettings.textBorderSizeErr": "Введене значення невірно. <br> Будь ласка, введіть значення від 0 pt до 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Змінити тип", "SSE.Views.ChartSettings.textChartType": "Змінити тип діаграми", "SSE.Views.ChartSettings.textEditData": "Редагувати дату і місцезнаходження", "SSE.Views.ChartSettings.textFirstPoint": "Перша точка", "SSE.Views.ChartSettings.textHeight": "Висота", "SSE.Views.ChartSettings.textHighPoint": "Висока точка", "SSE.Views.ChartSettings.textKeepRatio": "Сталі пропорції", "SSE.Views.ChartSettings.textLastPoint": "остан<PERSON><PERSON>й пункт", "SSE.Views.ChartSettings.textLowPoint": "Низька точка", "SSE.Views.ChartSettings.textMarkers": "Маркери", "SSE.Views.ChartSettings.textNegativePoint": "Негативний момент", "SSE.Views.ChartSettings.textRanges": "Діапазон даних", "SSE.Views.ChartSettings.textSelectData": "Вибрати дату", "SSE.Views.ChartSettings.textShow": "Відобразити", "SSE.Views.ChartSettings.textSize": "Розмір", "SSE.Views.ChartSettings.textStyle": "Стиль", "SSE.Views.ChartSettings.textType": "Тип", "SSE.Views.ChartSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ПОМИЛКА! Максимальна кількість точок у серії для діаграми становить 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ПОМИЛКА! Максимальна кількість даних на кожну діаграму становить 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Невірний порядок рядків. Щоб побудувати фондову діаграму, помістіть дані на аркуші в наступному порядку: ціна відкриття, максимальна ціна, мінімальна ціна, ціна закриття.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Не переміщувати та не змінювати розміри разом з клітинками", "SSE.Views.ChartSettingsDlg.textAlt": "Альтернативний текст", "SSE.Views.ChartSettingsDlg.textAltDescription": "Опис угоди", "SSE.Views.ChartSettingsDlg.textAltTip": "Альтернативне текстове представлення інформації про візуальний об'єкт, яке може бути прочитано людям із порушеннями зору або когнітивними дисфункціями, щоб вони могли краще зрозуміти, яка інформація міститься в зображенні, автофігурі, діаграмі або таблиці.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Назва", "SSE.Views.ChartSettingsDlg.textAuto": "Авто", "SSE.Views.ChartSettingsDlg.textAutoEach": "Авто для кожного", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Осі Хрести", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Параметри осей", "SSE.Views.ChartSettingsDlg.textAxisPos": "Позиція осі", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Налаштування осі", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Заголовок", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Між відмітками", "SSE.Views.ChartSettingsDlg.textBillions": "Мільяр<PERSON>и", "SSE.Views.ChartSettingsDlg.textBottom": "Внизу", "SSE.Views.ChartSettingsDlg.textCategoryName": "Назва категорії", "SSE.Views.ChartSettingsDlg.textCenter": "Центр", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Графічні елементи та <br> Гра<PERSON><PERSON><PERSON><PERSON> легенда", "SSE.Views.ChartSettingsDlg.textChartTitle": "Назва діграми", "SSE.Views.ChartSettingsDlg.textCross": "Перетина<PERSON>и", "SSE.Views.ChartSettingsDlg.textCustom": "Користувальницький", "SSE.Views.ChartSettingsDlg.textDataColumns": "в колонках", "SSE.Views.ChartSettingsDlg.textDataLabels": "Підписи даних", "SSE.Views.ChartSettingsDlg.textDataRows": "в рядках", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Показати легенду", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Приховані та порожні комірки", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Поєднання точок даних з лінією", "SSE.Views.ChartSettingsDlg.textFit": "Придатний до ширини", "SSE.Views.ChartSettingsDlg.textFixed": "Зафіксований", "SSE.Views.ChartSettingsDlg.textFormat": "Формат підпису", "SSE.Views.ChartSettingsDlg.textGaps": "Прогалини", "SSE.Views.ChartSettingsDlg.textGridLines": "Сітки ліній", "SSE.Views.ChartSettingsDlg.textGroup": "Група Міні-діаграми", "SSE.Views.ChartSettingsDlg.textHide": "Приховати", "SSE.Views.ChartSettingsDlg.textHideAxis": "Приховати вісь", "SSE.Views.ChartSettingsDlg.textHigh": "Високий", "SSE.Views.ChartSettingsDlg.textHorAxis": "Горизонтальна вісь", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Допоміжна горизонтальна вісь", "SSE.Views.ChartSettingsDlg.textHorizontal": "Горізонтальний", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Сотні", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "в", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Внутрішнє Нижнє", "SSE.Views.ChartSettingsDlg.textInnerTop": "Внутр<PERSON><PERSON><PERSON><PERSON>й Топ", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ПОМИЛКА! Недійсний діапазон комірок", "SSE.Views.ChartSettingsDlg.textLabelDist": "Відстань мітки осі", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Інтервал між мітками", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Варіанти маркування", "SSE.Views.ChartSettingsDlg.textLabelPos": "Позиції маркування", "SSE.Views.ChartSettingsDlg.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeft": "Лі<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Ліве накладення", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Внизу", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Лі<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendRight": "Право", "SSE.Views.ChartSettingsDlg.textLegendTop": "Верх", "SSE.Views.ChartSettingsDlg.textLines": "Рядки", "SSE.Views.ChartSettingsDlg.textLocationRange": "Діапазон місць", "SSE.Views.ChartSettingsDlg.textLow": "Низький", "SSE.Views.ChartSettingsDlg.textMajor": "Мажор", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Мажор та мінор", "SSE.Views.ChartSettingsDlg.textMajorType": "Значний тип", "SSE.Views.ChartSettingsDlg.textManual": "Вруч<PERSON>у", "SSE.Views.ChartSettingsDlg.textMarkers": "Маркери", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Інтервал між знаками", "SSE.Views.ChartSettingsDlg.textMaxValue": "Максимальне значення", "SSE.Views.ChartSettingsDlg.textMillions": "Мільйони", "SSE.Views.ChartSettingsDlg.textMinor": "М<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "Маленький тип", "SSE.Views.ChartSettingsDlg.textMinValue": "Мінімальне значення", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Біля вісі", "SSE.Views.ChartSettingsDlg.textNone": "Ж<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Немає накладання", "SSE.Views.ChartSettingsDlg.textOneCell": "Переміщати, але не змінювати розміри разом з клітинками", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "На квитках марок", "SSE.Views.ChartSettingsDlg.textOut": "з", "SSE.Views.ChartSettingsDlg.textOuterTop": "Зовн<PERSON><PERSON>ній зверху", "SSE.Views.ChartSettingsDlg.textOverlay": "Накладання", "SSE.Views.ChartSettingsDlg.textReverse": "Значення у зворотному порядку", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Зворотний порядок", "SSE.Views.ChartSettingsDlg.textRight": "Право", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Праве накладення", "SSE.Views.ChartSettingsDlg.textRotated": "Поворот", "SSE.Views.ChartSettingsDlg.textSameAll": "Той же для всіх", "SSE.Views.ChartSettingsDlg.textSelectData": "Вибрати дату", "SSE.Views.ChartSettingsDlg.textSeparator": "Роздільник підписів даних", "SSE.Views.ChartSettingsDlg.textSeriesName": "Назви серій", "SSE.Views.ChartSettingsDlg.textShow": "Показати", "SSE.Views.ChartSettingsDlg.textShowBorders": "Відображення діаграми кордонів", "SSE.Views.ChartSettingsDlg.textShowData": "Показати дані в прихованих рядках і стовпцях", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Показати порожні комірки як", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Показати вісі", "SSE.Views.ChartSettingsDlg.textShowValues": "Значення відображуваної діаграми", "SSE.Views.ChartSettingsDlg.textSingle": "Єдина міні-діаграма", "SSE.Views.ChartSettingsDlg.textSmooth": "Глад<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Прив'язка до клітинки", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Діапазони міні-діаграм", "SSE.Views.ChartSettingsDlg.textStraight": "Строгий", "SSE.Views.ChartSettingsDlg.textStyle": "Стиль", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Перевірте варіанти", "SSE.Views.ChartSettingsDlg.textTitle": "Діаграма - Розширені налаштування", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Міні-діаграми - розширені налаштування", "SSE.Views.ChartSettingsDlg.textTop": "Верх", "SSE.Views.ChartSettingsDlg.textTrillions": "Трильйони", "SSE.Views.ChartSettingsDlg.textTwoCell": "Переміщувати та змінювати розміри разом з клітинками", "SSE.Views.ChartSettingsDlg.textType": "Тип", "SSE.Views.ChartSettingsDlg.textTypeData": "Тип і дата", "SSE.Views.ChartSettingsDlg.textUnits": "Елементи відображення", "SSE.Views.ChartSettingsDlg.textValue": "Значення", "SSE.Views.ChartSettingsDlg.textVertAxis": "Вертикальні вісі", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Допоміжна вертикальна вісь", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Назва осі X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Назва осі Y", "SSE.Views.ChartSettingsDlg.textZero": "Нуль", "SSE.Views.ChartSettingsDlg.txtEmpty": "Це поле є обов'язковим", "SSE.Views.ChartTypeDialog.errorComboSeries": "Для створення комбінованої діаграми виберіть щонайменше два ряди даних.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Для вибраного типу діаграми потрібна допоміжна вісь, яка використовується в діаграмі. Виберіть інший тип діаграми.", "SSE.Views.ChartTypeDialog.textSecondary": "Допоміжна вісь", "SSE.Views.ChartTypeDialog.textSeries": "Серія", "SSE.Views.ChartTypeDialog.textStyle": "Стиль", "SSE.Views.ChartTypeDialog.textTitle": "Тип діаграми", "SSE.Views.ChartTypeDialog.textType": "Тип", "SSE.Views.CreatePivotDialog.textDataRange": "Діапазон вихідних даних", "SSE.Views.CreatePivotDialog.textDestination": "Виберіть, де розмістити таблицю", "SSE.Views.CreatePivotDialog.textExist": "Існуючий лист", "SSE.Views.CreatePivotDialog.textInvalidRange": "Недійсний діапазон комірок", "SSE.Views.CreatePivotDialog.textNew": "<PERSON>овий лист", "SSE.Views.CreatePivotDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.CreatePivotDialog.textTitle": "Створити зведену таблицю", "SSE.Views.CreatePivotDialog.txtEmpty": "Це поле є обов'язковим", "SSE.Views.CreateSparklineDialog.textDataRange": "Діапазон вихідних даних", "SSE.Views.CreateSparklineDialog.textDestination": "Виберіть, де помістити спарклайни", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Неприпустимий діапазон клітинок", "SSE.Views.CreateSparklineDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.CreateSparklineDialog.textTitle": "Створення спарклайнів", "SSE.Views.CreateSparklineDialog.txtEmpty": "Це поле необхідно заповнити", "SSE.Views.DataTab.capBtnGroup": "Згрупувати", "SSE.Views.DataTab.capBtnTextCustomSort": "Сортування, що налаштовується", "SSE.Views.DataTab.capBtnTextDataValidation": "Перевірка даних", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Видалити дублікати", "SSE.Views.DataTab.capBtnTextToCol": "Текст по стовпчиках", "SSE.Views.DataTab.capBtnUngroup": "Розгрупувати", "SSE.Views.DataTab.capDataFromText": "Отримати дані", "SSE.Views.DataTab.mniFromFile": "З локального TXT/CSV файлу", "SSE.Views.DataTab.mniFromUrl": "URL TXT/CSV файлу", "SSE.Views.DataTab.textBelow": "Підсумки у рядках під даними", "SSE.Views.DataTab.textClear": "Видалити структуру", "SSE.Views.DataTab.textColumns": "Розгрупувати стовпчики", "SSE.Views.DataTab.textGroupColumns": "Згрупувати стовпчики", "SSE.Views.DataTab.textGroupRows": "Згрупувати рядки", "SSE.Views.DataTab.textRightOf": "Підсумки в стовпчиках праворуч від даних", "SSE.Views.DataTab.textRows": "Розгрупувати рядки", "SSE.Views.DataTab.tipCustomSort": "Сортування, що налаштовується", "SSE.Views.DataTab.tipDataFromText": "Отримати дані з текстового/CSV-файлу", "SSE.Views.DataTab.tipDataValidation": "Перевірка даних", "SSE.Views.DataTab.tipGroup": "Згрупувати діапазон клітинок", "SSE.Views.DataTab.tipRemDuplicates": "Видалити рядки, що повторюються, з листа", "SSE.Views.DataTab.tipToColumns": "Розділити текст клітинки по стовпчиках", "SSE.Views.DataTab.tipUngroup": "Зняти групування діапазону комірок", "SSE.Views.DataValidationDialog.errorFormula": "Під час обчислення значення виникає помилка. Ви хочете продовжити?", "SSE.Views.DataValidationDialog.errorInvalid": "У полі \"{0}\" введено неприпустиме значення.", "SSE.Views.DataValidationDialog.errorInvalidDate": "У полі \"{0}\" введено неприпустиму дату.", "SSE.Views.DataValidationDialog.errorInvalidList": "Джерело списку має бути списком з розділювачами або посиланням на один рядок або стовпчик.", "SSE.Views.DataValidationDialog.errorInvalidTime": "У полі \"{0}\" введено неприпустимий час.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Значення поля \"{1}\" має бути більшим або рівним значенню поля \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Необхідно ввести значення і в полі \"{0}\", і в полі \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "У полі \"{0}\" необхідно ввести значення.", "SSE.Views.DataValidationDialog.errorNamedRange": "Зазначений іменований діапазон не знайдено.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "В умовах {0} не можна використовувати негативні значення.", "SSE.Views.DataValidationDialog.errorNotNumeric": "Поле \"{0}\" має містити числове значення, чисельний вираз або посилання на клітинку з числовим значенням.", "SSE.Views.DataValidationDialog.strError": "Повідомлення про помилку", "SSE.Views.DataValidationDialog.strInput": "Підказка щодо введення", "SSE.Views.DataValidationDialog.strSettings": "Налаштування", "SSE.Views.DataValidationDialog.textAlert": "Сповіщення", "SSE.Views.DataValidationDialog.textAllow": "Дозволити", "SSE.Views.DataValidationDialog.textApply": "Поширити зміни на всі інші клітинки з тією ж умовою", "SSE.Views.DataValidationDialog.textCellSelected": "При виборі клітинки відображатиметься наступна підказка", "SSE.Views.DataValidationDialog.textCompare": "Порівняти з", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Дата завершення", "SSE.Views.DataValidationDialog.textEndTime": "<PERSON>а<PERSON> закінчення", "SSE.Views.DataValidationDialog.textError": "Повідомлення про помилку", "SSE.Views.DataValidationDialog.textFormula": "Формула", "SSE.Views.DataValidationDialog.textIgnore": "Ігнорувати пусті клітинки", "SSE.Views.DataValidationDialog.textInput": "Підказка щодо введення", "SSE.Views.DataValidationDialog.textMax": "Максимум", "SSE.Views.DataValidationDialog.textMessage": "Повідомлення", "SSE.Views.DataValidationDialog.textMin": "Мін<PERSON><PERSON>ум", "SSE.Views.DataValidationDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.DataValidationDialog.textShowDropDown": "Показувати список, що розкривається, в клітинці", "SSE.Views.DataValidationDialog.textShowError": "Виводити повідомлення про помилку", "SSE.Views.DataValidationDialog.textShowInput": "Показувати підказку, якщо це поточна клітинка", "SSE.Views.DataValidationDialog.textSource": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Дата початку", "SSE.Views.DataValidationDialog.textStartTime": "Час початку", "SSE.Views.DataValidationDialog.textStop": "Стоп", "SSE.Views.DataValidationDialog.textStyle": "Стиль", "SSE.Views.DataValidationDialog.textTitle": "Заголовок", "SSE.Views.DataValidationDialog.textUserEnters": "При спробі вводу неприпустимих даних показувати повідомлення", "SSE.Views.DataValidationDialog.txtAny": "Будь-яке значення", "SSE.Views.DataValidationDialog.txtBetween": "між", "SSE.Views.DataValidationDialog.txtDate": "Дата", "SSE.Views.DataValidationDialog.txtDecimal": "Десяткове число", "SSE.Views.DataValidationDialog.txtElTime": "Прошло часу", "SSE.Views.DataValidationDialog.txtEndDate": "Дата завершення", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON>а<PERSON> закінчення", "SSE.Views.DataValidationDialog.txtEqual": "дорівнює", "SSE.Views.DataValidationDialog.txtGreaterThan": "більше", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "більше або дорівнює", "SSE.Views.DataValidationDialog.txtLength": "Довжина", "SSE.Views.DataValidationDialog.txtLessThan": "менше ніж", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "менше або дорівнює", "SSE.Views.DataValidationDialog.txtList": "Список", "SSE.Views.DataValidationDialog.txtNotBetween": "не між", "SSE.Views.DataValidationDialog.txtNotEqual": "не рівно", "SSE.Views.DataValidationDialog.txtOther": "Інше", "SSE.Views.DataValidationDialog.txtStartDate": "Дата початку", "SSE.Views.DataValidationDialog.txtStartTime": "Час початку", "SSE.Views.DataValidationDialog.txtTextLength": "Д<PERSON><PERSON><PERSON>на тексту", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "Ціле число", "SSE.Views.DigitalFilterDialog.capAnd": "і", "SSE.Views.DigitalFilterDialog.capCondition1": "Дорівнює", "SSE.Views.DigitalFilterDialog.capCondition10": "не закінчується з", "SSE.Views.DigitalFilterDialog.capCondition11": "Містить", "SSE.Views.DigitalFilterDialog.capCondition12": "не містить", "SSE.Views.DigitalFilterDialog.capCondition2": "не рівний", "SSE.Views.DigitalFilterDialog.capCondition3": "Більше, ніж", "SSE.Views.DigitalFilterDialog.capCondition4": "більше або дорівнює", "SSE.Views.DigitalFilterDialog.capCondition5": "менше ніж", "SSE.Views.DigitalFilterDialog.capCondition6": "менше ніж або дорівнює", "SSE.Views.DigitalFilterDialog.capCondition7": "почати з", "SSE.Views.DigitalFilterDialog.capCondition8": "не починайте з", "SSE.Views.DigitalFilterDialog.capCondition9": "закінчується з ", "SSE.Views.DigitalFilterDialog.capOr": "або", "SSE.Views.DigitalFilterDialog.textNoFilter": "немає фільтру", "SSE.Views.DigitalFilterDialog.textShowRows": "Показати рядки де", "SSE.Views.DigitalFilterDialog.textUse1": "Використовуйте ? щоб представити будь-якого окремого персонажа", "SSE.Views.DigitalFilterDialog.textUse2": "Використовуйте * для подання будь-якої серії символів", "SSE.Views.DigitalFilterDialog.txtTitle": "Спеціальний фільтр", "SSE.Views.DocumentHolder.advancedImgText": "Зображення розширені налаштування", "SSE.Views.DocumentHolder.advancedShapeText": "Форма розширені налаштування", "SSE.Views.DocumentHolder.advancedSlicerText": "Додаткові параметри зрізу", "SSE.Views.DocumentHolder.bottomCellText": "Вирівняти знизу", "SSE.Views.DocumentHolder.bulletsText": "Кулі та нумерація", "SSE.Views.DocumentHolder.centerCellText": "Вирівняти посередині", "SSE.Views.DocumentHolder.chartText": "Діаграма Розширені налаштування", "SSE.Views.DocumentHolder.deleteColumnText": "Колона", "SSE.Views.DocumentHolder.deleteRowText": "Рядок", "SSE.Views.DocumentHolder.deleteTableText": "Таблиця", "SSE.Views.DocumentHolder.direct270Text": "Повернути текст вгору", "SSE.Views.DocumentHolder.direct90Text": "Повернути текст вниз", "SSE.Views.DocumentHolder.directHText": "Горізонтальний", "SSE.Views.DocumentHolder.directionText": "Текстовий напрямок", "SSE.Views.DocumentHolder.editChartText": "Редагувати дату", "SSE.Views.DocumentHolder.editHyperlinkText": "Редагувати гіперпосилання", "SSE.Views.DocumentHolder.insertColumnLeftText": "Колонка ліворуч", "SSE.Views.DocumentHolder.insertColumnRightText": "Колонка праворуч", "SSE.Views.DocumentHolder.insertRowAboveText": "Рядок вгорі", "SSE.Views.DocumentHolder.insertRowBelowText": "Рядок внизу", "SSE.Views.DocumentHolder.originalSizeText": "Реальний розмір", "SSE.Views.DocumentHolder.removeHyperlinkText": "Видалити гіперпосилання", "SSE.Views.DocumentHolder.selectColumnText": "Загальна колонка", "SSE.Views.DocumentHolder.selectDataText": "Колонка даних", "SSE.Views.DocumentHolder.selectRowText": "Рядок", "SSE.Views.DocumentHolder.selectTableText": "Таблиця", "SSE.Views.DocumentHolder.strDelete": "Вилучити підпис", "SSE.Views.DocumentHolder.strDetails": "Склад підпису", "SSE.Views.DocumentHolder.strSetup": "Налаштування підпису", "SSE.Views.DocumentHolder.strSign": "Підписати", "SSE.Views.DocumentHolder.textAlign": "Вирівнювання", "SSE.Views.DocumentHolder.textArrange": "Порядок", "SSE.Views.DocumentHolder.textArrangeBack": "Надіслати до фону", "SSE.Views.DocumentHolder.textArrangeBackward": "Відправити назад", "SSE.Views.DocumentHolder.textArrangeForward": "Висувати", "SSE.Views.DocumentHolder.textArrangeFront": "Перенести на передній план", "SSE.Views.DocumentHolder.textAverage": "Середнє", "SSE.Views.DocumentHolder.textBullets": "Маркери", "SSE.Views.DocumentHolder.textCount": "Кількість", "SSE.Views.DocumentHolder.textCrop": "Обрізати", "SSE.Views.DocumentHolder.textCropFill": "Заливка", "SSE.Views.DocumentHolder.textCropFit": "Вмістити", "SSE.Views.DocumentHolder.textEditPoints": "Змінити точки", "SSE.Views.DocumentHolder.textEntriesList": "Виберати зі спадного списку", "SSE.Views.DocumentHolder.textFlipH": "Перевернути зліва направо", "SSE.Views.DocumentHolder.textFlipV": "Перевернути зверху вниз", "SSE.Views.DocumentHolder.textFreezePanes": "Заморозити панелі", "SSE.Views.DocumentHolder.textFromFile": "З файлу", "SSE.Views.DocumentHolder.textFromStorage": "Зі сховища", "SSE.Views.DocumentHolder.textFromUrl": "З URL", "SSE.Views.DocumentHolder.textListSettings": "Налаштування списку", "SSE.Views.DocumentHolder.textMacro": "Призначити макрос", "SSE.Views.DocumentHolder.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMore": "Інші функції", "SSE.Views.DocumentHolder.textMoreFormats": "Більше форматів", "SSE.Views.DocumentHolder.textNone": "Ж<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Нумерація", "SSE.Views.DocumentHolder.textReplace": "Замінити зображення", "SSE.Views.DocumentHolder.textRotate": "Поворот", "SSE.Views.DocumentHolder.textRotate270": "Повернути на 90° проти годинникової стрілки", "SSE.Views.DocumentHolder.textRotate90": "Повернути на 90° за годинниковою стрілкою", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Вирівняти по нижньому краю", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Вирівняти по центру", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Вирівняти по лівому краю", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Вирівняти посередині", "SSE.Views.DocumentHolder.textShapeAlignRight": "Вирівняти по правому краю", "SSE.Views.DocumentHolder.textShapeAlignTop": "Вирівняти по верхньому краю", "SSE.Views.DocumentHolder.textStdDev": "Станд.відхилення", "SSE.Views.DocumentHolder.textSum": "Сума", "SSE.Views.DocumentHolder.textUndo": "Скасувати", "SSE.Views.DocumentHolder.textUnFreezePanes": "Розморозити грані", "SSE.Views.DocumentHolder.textVar": "Ди<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersArrow": "Маркери-стрілки", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Маркери-галочки", "SSE.Views.DocumentHolder.tipMarkersDash": "Маркери-тире", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Заповнені ромбоподібні маркери", "SSE.Views.DocumentHolder.tipMarkersFRound": "Заповнені круглі маркери", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Заповнені квадратні маркери", "SSE.Views.DocumentHolder.tipMarkersHRound": "Пусті круглі маркери", "SSE.Views.DocumentHolder.tipMarkersStar": "Маркери-зірочки", "SSE.Views.DocumentHolder.topCellText": "Вирівняти догори", "SSE.Views.DocumentHolder.txtAccounting": "Фінансовий", "SSE.Views.DocumentHolder.txtAddComment": "Додати коментар", "SSE.Views.DocumentHolder.txtAddNamedRange": "Визначте ім'я", "SSE.Views.DocumentHolder.txtArrange": "Організувати", "SSE.Views.DocumentHolder.txtAscending": "Висхідний", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Автоматична ширина колонки", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Автоматично встановити висоту рядка", "SSE.Views.DocumentHolder.txtClear": "Очистити", "SSE.Views.DocumentHolder.txtClearAll": "Всі", "SSE.Views.DocumentHolder.txtClearComments": "Коментарі", "SSE.Views.DocumentHolder.txtClearFormat": "Формат", "SSE.Views.DocumentHolder.txtClearHyper": "Гіперсилки", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Очистити вибрані групи Sparkline", "SSE.Views.DocumentHolder.txtClearSparklines": "Очистити вибрані спаркліни", "SSE.Views.DocumentHolder.txtClearText": "Текст", "SSE.Views.DocumentHolder.txtColumn": "Загальна колонка", "SSE.Views.DocumentHolder.txtColumnWidth": "Встановити ширину стовпця", "SSE.Views.DocumentHolder.txtCondFormat": "Умовне форматування", "SSE.Views.DocumentHolder.txtCopy": "Копіювати", "SSE.Views.DocumentHolder.txtCurrency": "Г<PERSON><PERSON><PERSON>овий", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Спеціальна ширина стовпця", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Спеціальна висота рядка", "SSE.Views.DocumentHolder.txtCustomSort": "Сортування, що налаштовується", "SSE.Views.DocumentHolder.txtCut": "Вирізати", "SSE.Views.DocumentHolder.txtDate": "Дата", "SSE.Views.DocumentHolder.txtDelete": "Видалити", "SSE.Views.DocumentHolder.txtDescending": "Спускається", "SSE.Views.DocumentHolder.txtDistribHor": "Розподілити горизонтально", "SSE.Views.DocumentHolder.txtDistribVert": "Розподілити вертикально", "SSE.Views.DocumentHolder.txtEditComment": "Редагувати коментар", "SSE.Views.DocumentHolder.txtFilter": "Фільтр", "SSE.Views.DocumentHolder.txtFilterCellColor": "Фільтр за кольором комірки", "SSE.Views.DocumentHolder.txtFilterFontColor": "Фільтр за кольором шрифту", "SSE.Views.DocumentHolder.txtFilterValue": "Фільтр за значенням вибраної комірки", "SSE.Views.DocumentHolder.txtFormula": "Вставити функцію", "SSE.Views.DocumentHolder.txtFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGeneral": "Загальний", "SSE.Views.DocumentHolder.txtGroup": "Гру<PERSON>а", "SSE.Views.DocumentHolder.txtHide": "Приховати", "SSE.Views.DocumentHolder.txtInsert": "Вставити", "SSE.Views.DocumentHolder.txtInsHyperlink": "Гіперсилка", "SSE.Views.DocumentHolder.txtNumber": "Числовий", "SSE.Views.DocumentHolder.txtNumFormat": "Числовий формат", "SSE.Views.DocumentHolder.txtPaste": "Вставити", "SSE.Views.DocumentHolder.txtPercentage": "Процентний", "SSE.Views.DocumentHolder.txtReapply": "Повторно застосуйте", "SSE.Views.DocumentHolder.txtRow": "Загальний ряд", "SSE.Views.DocumentHolder.txtRowHeight": "Встановити висоту рядка", "SSE.Views.DocumentHolder.txtScientific": "Науковий", "SSE.Views.DocumentHolder.txtSelect": "Обрати", "SSE.Views.DocumentHolder.txtShiftDown": "Пересунути комірки донизу", "SSE.Views.DocumentHolder.txtShiftLeft": "Пересунути комірки ліворуч", "SSE.Views.DocumentHolder.txtShiftRight": "Пересунути комірки праворуч", "SSE.Views.DocumentHolder.txtShiftUp": "Пересунути комірки догори", "SSE.Views.DocumentHolder.txtShow": "Показати", "SSE.Views.DocumentHolder.txtShowComment": "Показати коментар", "SSE.Views.DocumentHolder.txtSort": "Сортувати", "SSE.Views.DocumentHolder.txtSortCellColor": "Вибраний колір комірки згори", "SSE.Views.DocumentHolder.txtSortFontColor": "Вибраний колір шрифту зверху", "SSE.Views.DocumentHolder.txtSparklines": "Міні-діграми", "SSE.Views.DocumentHolder.txtText": "Текстовий", "SSE.Views.DocumentHolder.txtTextAdvanced": "Додаткові параметри абзацу", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtUngroup": "Розпакувати", "SSE.Views.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.vertAlignText": "Вертикальне вирівнювання", "SSE.Views.FieldSettingsDialog.strLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strSubtotals": "Проміжні підсумки", "SSE.Views.FieldSettingsDialog.textReport": "Форма звіту", "SSE.Views.FieldSettingsDialog.textTitle": "Параметри полів", "SSE.Views.FieldSettingsDialog.txtAverage": "Середнє", "SSE.Views.FieldSettingsDialog.txtBlank": "Додати пустий рядок після кожного запису", "SSE.Views.FieldSettingsDialog.txtBottom": "Показувати у нижній частині групи", "SSE.Views.FieldSettingsDialog.txtCompact": "Компактна", "SSE.Views.FieldSettingsDialog.txtCount": "Кількість", "SSE.Views.FieldSettingsDialog.txtCountNums": "Кількість чисел", "SSE.Views.FieldSettingsDialog.txtCustomName": "Ім'я користувача", "SSE.Views.FieldSettingsDialog.txtEmpty": "Показувати елементи без даних", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtOutline": "Структура", "SSE.Views.FieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "Повторювати позначки елементів у кожному рядку", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Показувати проміжні підсумки", "SSE.Views.FieldSettingsDialog.txtSourceName": "Ім'я джерела:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Станд.відхилення", "SSE.Views.FieldSettingsDialog.txtSum": "Сума", "SSE.Views.FieldSettingsDialog.txtSummarize": "Функції для проміжних підсумків", "SSE.Views.FieldSettingsDialog.txtTabular": "У вигляді таблиці", "SSE.Views.FieldSettingsDialog.txtTop": "Показувати у заголовку групи", "SSE.Views.FieldSettingsDialog.txtVar": "Ди<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtVarp": "Дис<PERSON>р", "SSE.Views.FileMenu.btnBackCaption": "Відкрити розташування файлу", "SSE.Views.FileMenu.btnCloseMenuCaption": "Закрити меню", "SSE.Views.FileMenu.btnCreateNewCaption": "Створити новий", "SSE.Views.FileMenu.btnDownloadCaption": "Завантажити як", "SSE.Views.FileMenu.btnExitCaption": "Вийти", "SSE.Views.FileMenu.btnFileOpenCaption": "Відкрити", "SSE.Views.FileMenu.btnHelpCaption": "Допомога", "SSE.Views.FileMenu.btnHistoryCaption": "Історія версій", "SSE.Views.FileMenu.btnInfoCaption": "Інформація про електронну таблицю", "SSE.Views.FileMenu.btnPrintCaption": "Роздрукувати", "SSE.Views.FileMenu.btnProtectCaption": "Захистити", "SSE.Views.FileMenu.btnRecentFilesCaption": "Відкрити останні", "SSE.Views.FileMenu.btnRenameCaption": "Перейменувати", "SSE.Views.FileMenu.btnReturnCaption": "Повернутися до електронної таблиці", "SSE.Views.FileMenu.btnRightsCaption": "Права доступу", "SSE.Views.FileMenu.btnSaveAsCaption": "Зберегти як", "SSE.Views.FileMenu.btnSaveCaption": "Зберегти", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Зберегти копію як", "SSE.Views.FileMenu.btnSettingsCaption": "Розширені налаштування", "SSE.Views.FileMenu.btnToEditCaption": "Редагувати електронну таблицю", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Пуста таблиця", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Створити нову", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Застосувати", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Додати автора", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Додати текст", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Додаток", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Змінити права доступу", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Коментар", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Створена", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Автор останньої зміни", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Остання зміна", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Власник", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Місцезнаходження", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Ос<PERSON><PERSON><PERSON>, які мають права", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Тема", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Назва", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Завантаж<PERSON>на", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Змінити права доступу", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Ос<PERSON><PERSON><PERSON>, які мають права", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Застосувати", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Режим спільного редагування", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Десятковий роздільник", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Швидко", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Підказки шрифта", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Мова формули", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Приклад: SUM; ХВ; MAX; РАХУВАТИ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Налаштування макросів", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Показувати кнопку Налаштування вставки при вставці вмісту", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Регіональні налаштування", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Приклад:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Суворий", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Тема інтерфейсу", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Роздільник тисяч", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Одиниця виміру", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Використовувати роздільники на базі регіональних налаштувань", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Зменшити значення за замовчуванням", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Кожні 10 хвилин", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Кожні 30 хвилин", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Кожні 5 хвилин", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Кожну годину", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Автовідновлення", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Автозбереження", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Заблокований", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Збереження проміжних версій", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Кожну хвилину", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Стиль посилань", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Білоруська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Болгарська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Каталонська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Режим кешування за замовчуванням", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Сантиметр", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Чеський", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Данський", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Німецький", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Грецька", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Англійська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Іспанська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Фінський", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Французький", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Угорська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Індонезійська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Італійська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Японська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Корейська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Лаоська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Латиська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "як OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Р<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Норвежська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Голландський", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Польський", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Визначити", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Португальська (Бразилія)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Португальська (Португалія)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Румунська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Російський", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Увімкнути все", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Увімкнути всі макроси без сповіщення", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Словацька", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Словенська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Вимкнути все", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Вимкнути всі макроси без сповіщення", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Шведська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Турецька", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Українська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "В'єтнамська", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Показувати сповіщення", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Вимкнути всі макроси зі сповіщенням", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "як  Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Китайська", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Увага", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "За допомогою паролю", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Захистити електронну таблицю", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "За допомогою підпису", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Редагувати таблицю", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Під час редагування з електронної таблиці буде видалено підписи.<br>Продовжити?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Ця електронна таблиця захищена паролем", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Цю таблицю потрібно підписати.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "До електронної таблиці додано дійсні підписи. Таблиця захищена від редагування.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Деякі цифрові підписи в електронній таблиці є недійсними або їх не можна перевірити. Таблиця захищена від редагування.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Перегляд підписів", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON>о<PERSON><PERSON><PERSON> заливки", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Увага", "SSE.Views.FormatRulesEditDlg.text2Scales": "Двоколірна шкала", "SSE.Views.FormatRulesEditDlg.text3Scales": "Триколірна шкала", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Всі кордони", "SSE.Views.FormatRulesEditDlg.textAppearance": "Зовнішній вигляд стовпчика", "SSE.Views.FormatRulesEditDlg.textApply": "Застосувати до діапазону", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Автоматично", "SSE.Views.FormatRulesEditDlg.textAxis": "Осі", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Напрям стовпчика", "SSE.Views.FormatRulesEditDlg.textBold": "Напівжирний", "SSE.Views.FormatRulesEditDlg.textBorder": "Межа", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Кол<PERSON>р меж", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Стиль меж", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Нижні межі", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Неможливо додати умовне форматування.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Середина клітинки", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Внутрішні вертикальні межі", "SSE.Views.FormatRulesEditDlg.textClear": "Очистити", "SSE.Views.FormatRulesEditDlg.textColor": "<PERSON>о<PERSON><PERSON>р тексту", "SSE.Views.FormatRulesEditDlg.textContext": "Контекст", "SSE.Views.FormatRulesEditDlg.textCustom": "Особливий", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Діагональна межа зверху вниз", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Діагональна межа знизу нагору", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Введіть допустиму формулу.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Значення введеної формули не є числом, датою, часом чи рядком.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Введіть значення.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Введене значення не є допустимим числом, датою, часом або рядком.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Значення для {0} має бути більшим, ніж значення для {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Введіть число від {0} до {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Заливка", "SSE.Views.FormatRulesEditDlg.textFormat": "Формат", "SSE.Views.FormatRulesEditDlg.textFormula": "Формула", "SSE.Views.FormatRulesEditDlg.textGradient": "Градієнт", "SSE.Views.FormatRulesEditDlg.textIconLabel": "когда {0} {1} і", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "коли {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "коли значення дорівнює", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Один або кілька діапазонів даних значків перекриваються.<br>Скоригуйте значення діапазонів даних значків так, щоб діапазони не перекривалися.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Стиль іконки", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Внутрішні межі", "SSE.Views.FormatRulesEditDlg.textInvalid": "Неприпустимий діапазон даних", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ПОМИЛКА! Неприпустимий діапазон клітинок", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Елемент", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Зліва направо", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Ліві кордони", "SSE.Views.FormatRulesEditDlg.textLongBar": "Найдовший стовпчик", "SSE.Views.FormatRulesEditDlg.textMaximum": "Максимум", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Макс.точка", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Внутрішні горизонтальні межі", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Середня точка", "SSE.Views.FormatRulesEditDlg.textMinimum": "Мін<PERSON><PERSON>ум", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Мін.точка", "SSE.Views.FormatRulesEditDlg.textNegative": "Негативне", "SSE.Views.FormatRulesEditDlg.textNewColor": "Новий спеціальний колір", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Без кордонів", "SSE.Views.FormatRulesEditDlg.textNone": "Немає", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Принаймні одне із зазначених значень не є допустимим відсотком.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Вказане значення {0} не є допустимим відсотком.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Принаймні одне із зазначених значень не є допустимим відсотком.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Вказане значення {0} не є допустимим відсотком.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Зовнішні кордони", "SSE.Views.FormatRulesEditDlg.textPercent": "Процентний", "SSE.Views.FormatRulesEditDlg.textPercentile": "Процентиль", "SSE.Views.FormatRulesEditDlg.textPosition": "Положення", "SSE.Views.FormatRulesEditDlg.textPositive": "Позитивне", "SSE.Views.FormatRulesEditDlg.textPresets": "Передустановки", "SSE.Views.FormatRulesEditDlg.textPreview": "Перегляд", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "За умов умовного форматування не можна використовувати відносні посилання для шкал кольорів, гістограм та наборів значків.", "SSE.Views.FormatRulesEditDlg.textReverse": "Значки у зворотному порядку", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Справа наліво", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Праві межі", "SSE.Views.FormatRulesEditDlg.textRule": "Правило", "SSE.Views.FormatRulesEditDlg.textSameAs": "Як додатне", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.FormatRulesEditDlg.textShortBar": "найкорот<PERSON>ий стовпчик", "SSE.Views.FormatRulesEditDlg.textShowBar": "Показувати лише стовпчик", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Показати тільки значок", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Такий тип посилання не можна використовувати у формулі умовного форматування.<br>Змініть посилання так, щоб воно вказувало на одну клітинку, або помістіть посилання у функцію. Наприклад: = СУМ(A1: B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Суцільний", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Закреслений", "SSE.Views.FormatRulesEditDlg.textSubscript": "Підрядні знаки", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Надрядкові знаки", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Верхні межі", "SSE.Views.FormatRulesEditDlg.textUnderline": "Підкреслений", "SSE.Views.FormatRulesEditDlg.tipBorders": "Межі", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Числовий формат", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Фінансовий", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Г<PERSON><PERSON><PERSON>овий", "SSE.Views.FormatRulesEditDlg.txtDate": "Дата", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Це поле необхідно заповнити", "SSE.Views.FormatRulesEditDlg.txtFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Загальний", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Без значка", "SSE.Views.FormatRulesEditDlg.txtNumber": "Числовий", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Відсоток", "SSE.Views.FormatRulesEditDlg.txtScientific": "Науковий", "SSE.Views.FormatRulesEditDlg.txtText": "Текст", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Зміна правил форматування", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Нове правило форматування", "SSE.Views.FormatRulesManagerDlg.guestText": "Гість", "SSE.Views.FormatRulesManagerDlg.lockText": "Заблокований", "SSE.Views.FormatRulesManagerDlg.text1Above": "На 1 стандартне відхилення вище за середнє", "SSE.Views.FormatRulesManagerDlg.text1Below": "На 1 стандартне відхилення нижче за середнє", "SSE.Views.FormatRulesManagerDlg.text2Above": "На 2 стандартні відхилення вище середнього", "SSE.Views.FormatRulesManagerDlg.text2Below": "На 2 стандартні відхилення нижче середнього", "SSE.Views.FormatRulesManagerDlg.text3Above": "На 3 стандартні відхилення вище середнього", "SSE.Views.FormatRulesManagerDlg.text3Below": "На 3 стандартні відхилення нижче середнього", "SSE.Views.FormatRulesManagerDlg.textAbove": "Вище середнього", "SSE.Views.FormatRulesManagerDlg.textApply": "Застосувати до", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Значення клітинки починається з", "SSE.Views.FormatRulesManagerDlg.textBelow": "Нижче середнього", "SSE.Views.FormatRulesManagerDlg.textBetween": "знаходиться між {0} та {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Значення клітинки", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Шкала кольор<PERSON>в", "SSE.Views.FormatRulesManagerDlg.textContains": "Значення клітинки містить", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Клітинка містить порожнє значення", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Клітинка містить помилку", "SSE.Views.FormatRulesManagerDlg.textDelete": "Видалити", "SSE.Views.FormatRulesManagerDlg.textDown": "Перемістити правило вниз", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Значення, що повторюються", "SSE.Views.FormatRulesManagerDlg.textEdit": "Змінити", "SSE.Views.FormatRulesManagerDlg.textEnds": "Значення клітинки закінчується на", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Дорівнює або вище середнього", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Дорівнює або менше середнього", "SSE.Views.FormatRulesManagerDlg.textFormat": "Формат", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Набір іконок", "SSE.Views.FormatRulesManagerDlg.textNew": "Нове", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "не знаходиться між {0} та {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Значення клітинки не містить", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Клітинка не містить пусте значення", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Клітинка не містить помилки", "SSE.Views.FormatRulesManagerDlg.textRules": "Правила", "SSE.Views.FormatRulesManagerDlg.textScope": "Показати правила форматування для", "SSE.Views.FormatRulesManagerDlg.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.FormatRulesManagerDlg.textSelection": "Поточний виділений фрагмент", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Це зведена таблиця", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Цей лист", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Ця таблиця", "SSE.Views.FormatRulesManagerDlg.textUnique": "Унікальні значення", "SSE.Views.FormatRulesManagerDlg.textUp": "Перемістити правило вверх", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Цей елемент редагує інший користувач.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Умовне форматування", "SSE.Views.FormatSettingsDialog.textCategory": "Категорія", "SSE.Views.FormatSettingsDialog.textDecimal": "десятковий дріб", "SSE.Views.FormatSettingsDialog.textFormat": "Формат", "SSE.Views.FormatSettingsDialog.textLinked": "Зв'язок з джерелом", "SSE.Views.FormatSettingsDialog.textSeparator": "Використовуйте 1000 роздільник", "SSE.Views.FormatSettingsDialog.textSymbols": "Символи", "SSE.Views.FormatSettingsDialog.textTitle": "Формат числа", "SSE.Views.FormatSettingsDialog.txtAccounting": "Бухгалтерський облік", "SSE.Views.FormatSettingsDialog.txtAs10": "Як десяті (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Як сотих (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON>к шістнадця<PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Як половинки (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Як четверті (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Як восьмих (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Валюта", "SSE.Views.FormatSettingsDialog.txtCustom": "Користувальницький", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Вводьте числовий формат уважно. Редактор електронних таблиць не перевіряє формати користувача на помилки, що може вплинути на файл xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Дата", "SSE.Views.FormatSettingsDialog.txtFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtGeneral": "Загальні", "SSE.Views.FormatSettingsDialog.txtNone": "Немає", "SSE.Views.FormatSettingsDialog.txtNumber": "Номер", "SSE.Views.FormatSettingsDialog.txtPercentage": "відсотковий вміст", "SSE.Views.FormatSettingsDialog.txtSample": "Приклад:", "SSE.Views.FormatSettingsDialog.txtScientific": "Науковий", "SSE.Views.FormatSettingsDialog.txtText": "Текст", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "До однієї цифри (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "До двох цифр (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "До трьох цифр (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Виберіть функціональну групу", "SSE.Views.FormulaDialog.textListDescription": "Вибрати функцію", "SSE.Views.FormulaDialog.txtRecommended": "Рекомендовані", "SSE.Views.FormulaDialog.txtSearch": "По<PERSON><PERSON>к", "SSE.Views.FormulaDialog.txtTitle": "Вставити функцію", "SSE.Views.FormulaTab.textAutomatic": "Автоматично", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Перерахунок поточного листа", "SSE.Views.FormulaTab.textCalculateWorkbook": "Перерахунок книги", "SSE.Views.FormulaTab.textManual": "Вруч<PERSON>у", "SSE.Views.FormulaTab.tipCalculate": "Перер<PERSON><PERSON><PERSON>нок", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Перерахунок усієї книги", "SSE.Views.FormulaTab.txtAdditional": "Вставити функцію", "SSE.Views.FormulaTab.txtAutosum": "Автосума", "SSE.Views.FormulaTab.txtAutosumTip": "Сума", "SSE.Views.FormulaTab.txtCalculation": "Перер<PERSON><PERSON><PERSON>нок", "SSE.Views.FormulaTab.txtFormula": "Функція", "SSE.Views.FormulaTab.txtFormulaTip": "Вставити функцію", "SSE.Views.FormulaTab.txtMore": "Інші функції", "SSE.Views.FormulaTab.txtRecent": "Останні використані", "SSE.Views.FormulaWizard.textAny": "будь-який", "SSE.Views.FormulaWizard.textArgument": "Аргумент", "SSE.Views.FormulaWizard.textFunction": "Функція", "SSE.Views.FormulaWizard.textFunctionRes": "Результат функції", "SSE.Views.FormulaWizard.textHelp": "Довідка по цій функції", "SSE.Views.FormulaWizard.textLogical": "логічне значення", "SSE.Views.FormulaWizard.textNoArgs": "Ця функція не має аргументів", "SSE.Views.FormulaWizard.textNumber": "число", "SSE.Views.FormulaWizard.textRef": "посилання", "SSE.Views.FormulaWizard.textText": "текст", "SSE.Views.FormulaWizard.textTitle": "Аргументи функції", "SSE.Views.FormulaWizard.textValue": "Значення", "SSE.Views.HeaderFooterDialog.textAlign": "Вирівняти відносно полів сторінки", "SSE.Views.HeaderFooterDialog.textAll": "Усі сторінки", "SSE.Views.HeaderFooterDialog.textBold": "Напівжирний", "SSE.Views.HeaderFooterDialog.textCenter": "В центрі", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON>о<PERSON><PERSON>р тексту", "SSE.Views.HeaderFooterDialog.textDate": "Дата", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Особливий для першої сторінки", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Різні для парних та непарних", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON>арна сторінка", "SSE.Views.HeaderFooterDialog.textFileName": "Ім'я файлу", "SSE.Views.HeaderFooterDialog.textFirst": "Перша сторінка", "SSE.Views.HeaderFooterDialog.textFooter": "Нижн<PERSON>й колонтитул", "SSE.Views.HeaderFooterDialog.textHeader": "Верхн<PERSON>й колонтитул", "SSE.Views.HeaderFooterDialog.textInsert": "Вставити", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "Введено занадто довгий текстовий рядок. Зменште кількість символів.", "SSE.Views.HeaderFooterDialog.textNewColor": "Новий спеціальний колір", "SSE.Views.HeaderFooterDialog.textOdd": "Непарна сторінка", "SSE.Views.HeaderFooterDialog.textPageCount": "Кількість сторінок", "SSE.Views.HeaderFooterDialog.textPageNum": "Номер сторінки", "SSE.Views.HeaderFooterDialog.textPresets": "Передустановки", "SSE.Views.HeaderFooterDialog.textRight": "Правор<PERSON>ч", "SSE.Views.HeaderFooterDialog.textScale": "Змінювати масштаб разом із документом", "SSE.Views.HeaderFooterDialog.textSheet": "Назва листа", "SSE.Views.HeaderFooterDialog.textStrikeout": "Викреслений", "SSE.Views.HeaderFooterDialog.textSubscript": "Підрядковий", "SSE.Views.HeaderFooterDialog.textSuperscript": "Надрядковий", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Параметри верхнього та нижнього колонтитулів", "SSE.Views.HeaderFooterDialog.textUnderline": "Підкреслений", "SSE.Views.HeaderFooterDialog.tipFontName": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontSize": "Розмір шрифту", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Дісплей", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "З'єднатися з", "SSE.Views.HyperlinkSettingsDialog.strRange": "Діапазон", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Копіювати", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Вибраний діапазон", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Введіть підпис тут", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Введіть посилання тут", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Введіть підказку тут", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Зовнішнє посилання", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Отримати посилання", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Внутрішній діапазон даних", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ПОМИЛКА! Недійсний діапазон комірок", "SSE.Views.HyperlinkSettingsDialog.textNames": "Визначені імена", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON>исти", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Текст ScreenTip", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Налаштування гіперсилки", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Це поле є обов'язковим", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Це поле має бути URL-адресою у форматі \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Це поле може містити не більше 2083 символів", "SSE.Views.ImageSettings.textAdvanced": "Показати додаткові налаштування", "SSE.Views.ImageSettings.textCrop": "Обрізати", "SSE.Views.ImageSettings.textCropFill": "Заливка", "SSE.Views.ImageSettings.textCropFit": "Вмістити", "SSE.Views.ImageSettings.textCropToShape": "Обрізати по фігурі", "SSE.Views.ImageSettings.textEdit": "Редагувати", "SSE.Views.ImageSettings.textEditObject": "Редагувати об'єкт", "SSE.Views.ImageSettings.textFlip": "Перевернути", "SSE.Views.ImageSettings.textFromFile": "З файлу", "SSE.Views.ImageSettings.textFromStorage": "Зі сховища", "SSE.Views.ImageSettings.textFromUrl": "З URL", "SSE.Views.ImageSettings.textHeight": "Висота", "SSE.Views.ImageSettings.textHint270": "Повернути на 90° проти годинникової стрілки", "SSE.Views.ImageSettings.textHint90": "Повернути на 90° за годинниковою стрілкою", "SSE.Views.ImageSettings.textHintFlipH": "Перевернути зліва направо", "SSE.Views.ImageSettings.textHintFlipV": "Перевернути зверху вниз", "SSE.Views.ImageSettings.textInsert": "Замінити зображення", "SSE.Views.ImageSettings.textKeepRatio": "Сталі пропорції", "SSE.Views.ImageSettings.textOriginalSize": "Реальний розмір", "SSE.Views.ImageSettings.textRecentlyUsed": "Останні використані", "SSE.Views.ImageSettings.textRotate90": "Повернути на 90°", "SSE.Views.ImageSettings.textRotation": "Поворот", "SSE.Views.ImageSettings.textSize": "Розмір", "SSE.Views.ImageSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Не переміщувати та не змінювати розміри разом з клітинками", "SSE.Views.ImageSettingsAdvanced.textAlt": "Альтернативний текст", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Альтернативне текстове представлення інформації про візуальний об'єкт, яке може бути прочитано людям із порушеннями зору або когнітивними дисфункціями, щоб вони могли краще зрозуміти, яка інформація міститься в зображенні, автофігурі, діаграмі або таблиці.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Назва", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON>а<PERSON><PERSON>л", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Віддзеркалено", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "По горизонталі", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Переміщати, але не змінювати розміри разом з клітинками", "SSE.Views.ImageSettingsAdvanced.textRotation": "Поворот", "SSE.Views.ImageSettingsAdvanced.textSnap": "Прив'язка до клітинки", "SSE.Views.ImageSettingsAdvanced.textTitle": "Зображення - розширені налаштування", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Переміщувати та змінювати розміри разом з клітинками", "SSE.Views.ImageSettingsAdvanced.textVertically": "По вертикалі", "SSE.Views.LeftMenu.tipAbout": "Про", "SSE.Views.LeftMenu.tipChat": "Чат", "SSE.Views.LeftMenu.tipComments": "Коментарі", "SSE.Views.LeftMenu.tipFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "SSE.Views.LeftMenu.tipSearch": "По<PERSON><PERSON>к", "SSE.Views.LeftMenu.tipSpellcheck": "Перевірка орфографії", "SSE.Views.LeftMenu.tipSupport": "Відгуки і підтримка", "SSE.Views.LeftMenu.txtDeveloper": "Режим розробника", "SSE.Views.LeftMenu.txtLimit": "Обмежений доступ", "SSE.Views.LeftMenu.txtTrial": "ПРОБНИЙ РЕЖИМ", "SSE.Views.LeftMenu.txtTrialDev": "Пробний режим розробника", "SSE.Views.MacroDialog.textMacro": "Ім'я макроса", "SSE.Views.MacroDialog.textTitle": "Призначити макрос", "SSE.Views.MainSettingsPrint.okButtonText": "Зберегти", "SSE.Views.MainSettingsPrint.strBottom": "Внизу", "SSE.Views.MainSettingsPrint.strLandscape": "лан<PERSON><PERSON><PERSON><PERSON>т", "SSE.Views.MainSettingsPrint.strLeft": "Лі<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "Поля", "SSE.Views.MainSettingsPrint.strPortrait": "Портрет", "SSE.Views.MainSettingsPrint.strPrint": "Роздрукувати", "SSE.Views.MainSettingsPrint.strPrintTitles": "Друкувати заголовки", "SSE.Views.MainSettingsPrint.strRight": "Право", "SSE.Views.MainSettingsPrint.strTop": "Верх", "SSE.Views.MainSettingsPrint.textActualSize": "Реальний розмір", "SSE.Views.MainSettingsPrint.textCustom": "Особливий", "SSE.Views.MainSettingsPrint.textCustomOptions": "Параметри, що налаштовуються", "SSE.Views.MainSettingsPrint.textFitCols": "Підібрати всі стовпці на одній сторінці", "SSE.Views.MainSettingsPrint.textFitPage": "Підібрати листки на одній сторінці", "SSE.Views.MainSettingsPrint.textFitRows": "Підібрати всі рядки на одній сторінці", "SSE.Views.MainSettingsPrint.textPageOrientation": "Орієнтація сторінки", "SSE.Views.MainSettingsPrint.textPageScaling": "Масштабування", "SSE.Views.MainSettingsPrint.textPageSize": "Розмір сторінки", "SSE.Views.MainSettingsPrint.textPrintGrid": "Друк мережних ліній", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Друк рядків і колонок заголовків", "SSE.Views.MainSettingsPrint.textRepeat": "Повторювати...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Повторювати стовпчики зліва", "SSE.Views.MainSettingsPrint.textRepeatTop": "Повторювати рядки зверху", "SSE.Views.MainSettingsPrint.textSettings": "Налаштування для", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Існуючі названі діапазони не можна редагувати, а нові не можна створити на даний момент, оскільки деякі з них редагуються.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Визначене ім'я", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Застереження", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Робоча книга", "SSE.Views.NamedRangeEditDlg.textDataRange": "Діапазон даних", "SSE.Views.NamedRangeEditDlg.textExistName": "ПОМИЛКА! Діапазон з такою назвою вже існує", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Назва повинна починатися з літери або підкреслення та не може містити недійсних символів.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ПОМИЛКА! Недійсний діапазон комірок", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ПОМИЛКА! Цей елемент редагує інший користувач.", "SSE.Views.NamedRangeEditDlg.textName": "Назва", "SSE.Views.NamedRangeEditDlg.textReservedName": "Ім'я, яке ви намагаєтесь використовувати, вже посилається на формули, що містяться у комірці. Будь ласка, використайте інше ім'я.", "SSE.Views.NamedRangeEditDlg.textScope": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Вибрати дату", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Це поле є обов'язковим", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Редагувати назву", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Нове ім'я", "SSE.Views.NamedRangePasteDlg.textNames": "Названі діапазони", "SSE.Views.NamedRangePasteDlg.txtTitle": "Вставити назву", "SSE.Views.NameManagerDlg.closeButtonText": "Закрити", "SSE.Views.NameManagerDlg.guestText": "Гість", "SSE.Views.NameManagerDlg.lockText": "Заблокований", "SSE.Views.NameManagerDlg.textDataRange": "Діапазон даних", "SSE.Views.NameManagerDlg.textDelete": "Видалити", "SSE.Views.NameManagerDlg.textEdit": "Редагувати", "SSE.Views.NameManagerDlg.textEmpty": "Названих діапазонів ще не створено. <br> Створіть принаймні один названий діапазон і він з'явиться в цьому полі.", "SSE.Views.NameManagerDlg.textFilter": "Фільтр", "SSE.Views.NameManagerDlg.textFilterAll": "Всі", "SSE.Views.NameManagerDlg.textFilterDefNames": "Визначені імена", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON><PERSON><PERSON>, наведені в аркуші", "SSE.Views.NameManagerDlg.textFilterTableNames": "Назви таблиці", "SSE.Views.NameManagerDlg.textFilterWorkbook": "<PERSON><PERSON><PERSON><PERSON>, наведені в робочій книзі", "SSE.Views.NameManagerDlg.textNew": "Новий", "SSE.Views.NameManagerDlg.textnoNames": "Названих діапазонів, що відповідають вашому фільтру, не знайдено.", "SSE.Views.NameManagerDlg.textRanges": "Названі діапазони", "SSE.Views.NameManagerDlg.textScope": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Робоча книга", "SSE.Views.NameManagerDlg.tipIsLocked": "Цей елемент редагує інший користувач.", "SSE.Views.NameManagerDlg.txtTitle": "Менеджер імен", "SSE.Views.NameManagerDlg.warnDelete": "Ви дійсно хочете видалити ім'я {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Нижнє", "SSE.Views.PageMarginsDialog.textLeft": "Лі<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "Праве", "SSE.Views.PageMarginsDialog.textTitle": "Поля", "SSE.Views.PageMarginsDialog.textTop": "Верхнє", "SSE.Views.ParagraphSettings.strLineHeight": "Лінія інтервалу", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Параметр інтервалу", "SSE.Views.ParagraphSettings.strSpacingAfter": "після", "SSE.Views.ParagraphSettings.strSpacingBefore": "Перед", "SSE.Views.ParagraphSettings.textAdvanced": "Показати додаткові налаштування", "SSE.Views.ParagraphSettings.textAt": "в", "SSE.Views.ParagraphSettings.textAtLeast": "принаймні", "SSE.Views.ParagraphSettings.textAuto": "Багаторазовий", "SSE.Views.ParagraphSettings.textExact": "Точно", "SSE.Views.ParagraphSettings.txtAutoText": "Авто", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Вказані вкладки з'являться в цьому полі", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Усі великі", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Подвійне перекреслення", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Відступи", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Лі<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Міжрядковий інтервал", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Право", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Після", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Перед", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Перший рядок", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "На", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Відступи та інтервали", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Зменшені великі", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Інтервал між абзацами", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Перекреслення", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Підрядковий", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Надрядковий", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Вкладка", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Вирівнювання", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Множник", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Пробіл між символами", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Вкладка за умовчанням", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Ефекти", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Точно", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Відступ", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Виступ", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "По ширині", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(немає)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Видалити", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Видалити усе", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Вказати", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Центр", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Лі<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Положення вкладки", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Право", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Параграф - розширені налаштування", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Авто", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "дорівнює", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "не закінчується на", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "Містить", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "не містить", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "між", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "не між", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "не рівно", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "Більше, ніж", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "більше або дорівнює", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "менше ніж", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "менше або дорівнює", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "починається з", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "не починається з", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "закінчується на ", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Показувати елементи, які мають підпис:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Показувати елементи, у яких:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Використовуйте знак ? замість будь-якого окремого символу", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Використовуйте * замість будь-якої послідовності символів", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "і", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Фільтр підписів", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Фільтр значень", "SSE.Views.PivotGroupDialog.textAuto": "Авто", "SSE.Views.PivotGroupDialog.textBy": "По", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "Закінчується в", "SSE.Views.PivotGroupDialog.textError": "Це поле має містити числове значення", "SSE.Views.PivotGroupDialog.textGreaterError": "Кінцеве число має бути більшим за початкове.", "SSE.Views.PivotGroupDialog.textHour": "Годи<PERSON>и", "SSE.Views.PivotGroupDialog.textMin": "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "Місяці", "SSE.Views.PivotGroupDialog.textNumDays": "Кількість днів", "SSE.Views.PivotGroupDialog.textQuart": "Квартали", "SSE.Views.PivotGroupDialog.textSec": "Секунди", "SSE.Views.PivotGroupDialog.textStart": "Починаючи з:", "SSE.Views.PivotGroupDialog.textYear": "Роки", "SSE.Views.PivotGroupDialog.txtTitle": "Групування", "SSE.Views.PivotSettings.textAdvanced": "Додаткові параметри", "SSE.Views.PivotSettings.textColumns": "Стовпчики", "SSE.Views.PivotSettings.textFields": "Вибрати поля", "SSE.Views.PivotSettings.textFilters": "Фільтри", "SSE.Views.PivotSettings.textRows": "Рядки", "SSE.Views.PivotSettings.textValues": "Значення", "SSE.Views.PivotSettings.txtAddColumn": "Додати до стовпчиків", "SSE.Views.PivotSettings.txtAddFilter": "Додати до фільтрів", "SSE.Views.PivotSettings.txtAddRow": "Додати до рядків", "SSE.Views.PivotSettings.txtAddValues": "Додати до значень", "SSE.Views.PivotSettings.txtFieldSettings": "Параметри полів", "SSE.Views.PivotSettings.txtMoveBegin": "Перемістити на початок", "SSE.Views.PivotSettings.txtMoveColumn": "Перемістити в стовпчики", "SSE.Views.PivotSettings.txtMoveDown": "Перенести вниз", "SSE.Views.PivotSettings.txtMoveEnd": "Перемістити до кінця", "SSE.Views.PivotSettings.txtMoveFilter": "Перемістити у фільтри", "SSE.Views.PivotSettings.txtMoveRow": "Перемістити до рядків", "SSE.Views.PivotSettings.txtMoveUp": "Перенести вверх", "SSE.Views.PivotSettings.txtMoveValues": "Перемістити до значення", "SSE.Views.PivotSettings.txtRemove": "Видалити поле", "SSE.Views.PivotSettingsAdvanced.strLayout": "Назва та макет", "SSE.Views.PivotSettingsAdvanced.textAlt": "Альтернативний текст", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Альтернативне текстове представлення інформації про візуальний об'єкт, яке може бути прочитано людям із порушеннями зору або когнітивними дисфункціями, щоб вони могли краще зрозуміти, яка інформація знаходиться в зображенні, автошопі, діаграмі або таблиці.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Заголовок", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Діапазон даних", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Дже<PERSON>ело даних", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Показувати поля в області фільтра звіту", "SSE.Views.PivotSettingsAdvanced.textDown": "Вниз, потім вправо", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Загальні підсумки", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Заголовки полів", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ПОМИЛКА! Неприпустимий діапазон клітинок", "SSE.Views.PivotSettingsAdvanced.textOver": "Праворуч, потім вниз", "SSE.Views.PivotSettingsAdvanced.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Показувати для стовпчиків", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Показувати заголовки полів для рядків та стовпчиків", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Показувати для рядків", "SSE.Views.PivotSettingsAdvanced.textTitle": "Зведена таблиця — додаткові параметри", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Число полів фільтра звіту для стовпчиків", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Число полів фільтра звіту для рядку", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Це поле є обов'язковим", "SSE.Views.PivotSettingsAdvanced.txtName": "Назва", "SSE.Views.PivotTable.capBlankRows": "Пусті рядки", "SSE.Views.PivotTable.capGrandTotals": "Загальні підсумки", "SSE.Views.PivotTable.capLayout": "Макет звіту", "SSE.Views.PivotTable.capSubtotals": "Проміжні підсумки", "SSE.Views.PivotTable.mniBottomSubtotals": "Показувати усі проміжні підсумки у нижній частині групи", "SSE.Views.PivotTable.mniInsertBlankLine": "Вставляти пустий рядок після кожного елемента", "SSE.Views.PivotTable.mniLayoutCompact": "Показати у стислій формі", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Не повторювати всі мітки елементів", "SSE.Views.PivotTable.mniLayoutOutline": "Показати у формі структури", "SSE.Views.PivotTable.mniLayoutRepeat": "Повторювати всі мітки елементів", "SSE.Views.PivotTable.mniLayoutTabular": "Показати у табличній формі", "SSE.Views.PivotTable.mniNoSubtotals": "Не показувати проміжні підсумки", "SSE.Views.PivotTable.mniOffTotals": "Відключити для рядків і стовпчиків", "SSE.Views.PivotTable.mniOnColumnsTotals": "Увімкнути тільки для стовпчиків", "SSE.Views.PivotTable.mniOnRowsTotals": "Увімкнути тільки для рядків", "SSE.Views.PivotTable.mniOnTotals": "Увімкнути тільки для рядків і стовпчиків", "SSE.Views.PivotTable.mniRemoveBlankLine": "Видалити порожній рядок після кожного елемента", "SSE.Views.PivotTable.mniTopSubtotals": "Показувати усі проміжні підсумки у верхній частині групи", "SSE.Views.PivotTable.textColBanded": "Чергувати стовпчики", "SSE.Views.PivotTable.textColHeader": "Заголовки стовпців", "SSE.Views.PivotTable.textRowBanded": "Чергувати рядки", "SSE.Views.PivotTable.textRowHeader": "Заголовки рядків", "SSE.Views.PivotTable.tipCreatePivot": "Вставити зведену таблицю", "SSE.Views.PivotTable.tipGrandTotals": "Показати або приховати загальні підсумки", "SSE.Views.PivotTable.tipRefresh": "Оновити інформацію з джерела даних", "SSE.Views.PivotTable.tipSelect": "Виділити всю зведену таблицю", "SSE.Views.PivotTable.tipSubtotals": "Показати або приховати проміжні підсумки", "SSE.Views.PivotTable.txtCreate": "Вставити таблицю", "SSE.Views.PivotTable.txtPivotTable": "Зведена таблиця", "SSE.Views.PivotTable.txtRefresh": "Оновити", "SSE.Views.PivotTable.txtSelect": "Виділити", "SSE.Views.PrintSettings.btnDownload": "Зберегти та завантажити", "SSE.Views.PrintSettings.btnPrint": "Зберегти і роздрукувати", "SSE.Views.PrintSettings.strBottom": "Внизу", "SSE.Views.PrintSettings.strLandscape": "лан<PERSON><PERSON><PERSON><PERSON>т", "SSE.Views.PrintSettings.strLeft": "Лі<PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "Поля", "SSE.Views.PrintSettings.strPortrait": "Портрет", "SSE.Views.PrintSettings.strPrint": "Роздрукувати", "SSE.Views.PrintSettings.strPrintTitles": "Друкувати заголовки", "SSE.Views.PrintSettings.strRight": "Право", "SSE.Views.PrintSettings.strShow": "Показати", "SSE.Views.PrintSettings.strTop": "Верх", "SSE.Views.PrintSettings.textActualSize": "Реальний розмір", "SSE.Views.PrintSettings.textAllSheets": "Всі аркуші", "SSE.Views.PrintSettings.textCurrentSheet": "Поточний аркуш", "SSE.Views.PrintSettings.textCustom": "Особливий", "SSE.Views.PrintSettings.textCustomOptions": "Параметри, що налаштовуються", "SSE.Views.PrintSettings.textFitCols": "Підібрати всі стовпці на одній сторінці", "SSE.Views.PrintSettings.textFitPage": "Підібрати листки на одній сторінці", "SSE.Views.PrintSettings.textFitRows": "Підібрати всі рядки на одній сторінці", "SSE.Views.PrintSettings.textHideDetails": "Приховати деталі", "SSE.Views.PrintSettings.textIgnore": "Ігнорувати область друку", "SSE.Views.PrintSettings.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageOrientation": "Орієнтація сторінки", "SSE.Views.PrintSettings.textPageScaling": "Масштабування", "SSE.Views.PrintSettings.textPageSize": "Розмір сторінки", "SSE.Views.PrintSettings.textPrintGrid": "Друк мережних ліній", "SSE.Views.PrintSettings.textPrintHeadings": "Друк рядків і колонок заголовків", "SSE.Views.PrintSettings.textPrintRange": "Роздрукувати діапазон", "SSE.Views.PrintSettings.textRange": "Діапазон", "SSE.Views.PrintSettings.textRepeat": "Повторювати...", "SSE.Views.PrintSettings.textRepeatLeft": "Повторювати стовпчики зліва", "SSE.Views.PrintSettings.textRepeatTop": "Повторювати рядки зверху", "SSE.Views.PrintSettings.textSelection": "Від<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Налаштування листа", "SSE.Views.PrintSettings.textShowDetails": "Показати деталі", "SSE.Views.PrintSettings.textShowGrid": "Показати лінії сітки", "SSE.Views.PrintSettings.textShowHeadings": "Показати заголовки рядків та стовпчиків", "SSE.Views.PrintSettings.textTitle": "Налаштування друку", "SSE.Views.PrintSettings.textTitlePDF": "Параметри PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON><PERSON><PERSON> стовпчик", "SSE.Views.PrintTitlesDialog.textFirstRow": "Перший рядок", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Закріплені стовпчики", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Закріплені рядки", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ПОМИЛКА! Неприпустимий діапазон клітинок", "SSE.Views.PrintTitlesDialog.textLeft": "Повторювати стовпчики зліва", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Не повторювати", "SSE.Views.PrintTitlesDialog.textRepeat": "Повторювати...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Вибір діапазону", "SSE.Views.PrintTitlesDialog.textTitle": "Друкувати заголовки", "SSE.Views.PrintTitlesDialog.textTop": "Повторювати рядки зверху", "SSE.Views.PrintWithPreview.txtActualSize": "Реальний розмір", "SSE.Views.PrintWithPreview.txtAllSheets": "Всі аркуші", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Застосувати до всіх листів", "SSE.Views.PrintWithPreview.txtBottom": "Нижнє", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Поточний лист", "SSE.Views.PrintWithPreview.txtCustom": "Особливий", "SSE.Views.PrintWithPreview.txtCustomOptions": "Параметри, що налаштовуються", "SSE.Views.PrintWithPreview.txtFitCols": "Вписати всі стовпчики на одну сторінку", "SSE.Views.PrintWithPreview.txtFitPage": "Вписати всі листи на одну сторінку", "SSE.Views.PrintWithPreview.txtFitRows": "Вписати всі рядки на одну сторінку", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Лінії сітки та заголовки", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Параметри верхнього та нижнього колонтитулів", "SSE.Views.PrintWithPreview.txtIgnore": "Ігнорувати область друку", "SSE.Views.PrintWithPreview.txtLandscape": "Альбомна", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "Поля", "SSE.Views.PrintWithPreview.txtOf": "з {0}", "SSE.Views.PrintWithPreview.txtPage": "Сторінка", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Номер сторінки недійсний", "SSE.Views.PrintWithPreview.txtPageOrientation": "Орієнтація сторінки", "SSE.Views.PrintWithPreview.txtPageSize": "Розмір сторінки", "SSE.Views.PrintWithPreview.txtPortrait": "Книжна", "SSE.Views.PrintWithPreview.txtPrint": "Друк", "SSE.Views.PrintWithPreview.txtPrintGrid": "Друк сітки", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Друк рядків і стовпчиків заголовків", "SSE.Views.PrintWithPreview.txtPrintRange": "Діа<PERSON>азон друку", "SSE.Views.PrintWithPreview.txtPrintTitles": "Друкувати заголовки", "SSE.Views.PrintWithPreview.txtRepeat": "Повторювати...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Повторювати стовпчики зліва", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Повторювати рядки зверху", "SSE.Views.PrintWithPreview.txtRight": "Праве", "SSE.Views.PrintWithPreview.txtSave": "Зберегти", "SSE.Views.PrintWithPreview.txtScaling": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Виділений фрагмент", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Параметри листа", "SSE.Views.PrintWithPreview.txtSheet": "Лист: {0}", "SSE.Views.PrintWithPreview.txtTop": "Верхнє", "SSE.Views.ProtectDialog.textExistName": "ПОМИЛКА! Діапазон із такою назвою вже існує", "SSE.Views.ProtectDialog.textInvalidName": "Назва діапазону повинна починатися з літери та може містити лише літери, цифри та пробіли.", "SSE.Views.ProtectDialog.textInvalidRange": "ПОМИЛКА! Неприпустимий діапазон клітинок", "SSE.Views.ProtectDialog.textSelectData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.ProtectDialog.txtAllow": "Дозволити всім користувачам цього аркуша", "SSE.Views.ProtectDialog.txtAutofilter": "Використовувати автофільтр", "SSE.Views.ProtectDialog.txtDelCols": "Видалити стовпчики", "SSE.Views.ProtectDialog.txtDelRows": "Видалити рядки", "SSE.Views.ProtectDialog.txtEmpty": "Це поле необхідно заповнити", "SSE.Views.ProtectDialog.txtFormatCells": "Форматування клітинки", "SSE.Views.ProtectDialog.txtFormatCols": "Форматування стовпчиків", "SSE.Views.ProtectDialog.txtFormatRows": "Форматування рядків", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Пароль та його підтвердження не збігаються", "SSE.Views.ProtectDialog.txtInsCols": "Вставляти стовпчики", "SSE.Views.ProtectDialog.txtInsHyper": "Вставити гіперпосилання", "SSE.Views.ProtectDialog.txtInsRows": "Вставляти рядки", "SSE.Views.ProtectDialog.txtObjs": "Редагувати об'єкти", "SSE.Views.ProtectDialog.txtOptional": "необов'язково", "SSE.Views.ProtectDialog.txtPassword": "Пароль", "SSE.Views.ProtectDialog.txtPivot": "Використовувати зведену таблицю та зведену діаграму", "SSE.Views.ProtectDialog.txtProtect": "Захистити", "SSE.Views.ProtectDialog.txtRange": "Діапазон", "SSE.Views.ProtectDialog.txtRangeName": "Назва", "SSE.Views.ProtectDialog.txtRepeat": "Повторити пароль", "SSE.Views.ProtectDialog.txtScen": "Редагувати сценарії", "SSE.Views.ProtectDialog.txtSelLocked": "Виділяти заблоковані клітинки", "SSE.Views.ProtectDialog.txtSelUnLocked": "Виділяти розблоковані клітинки", "SSE.Views.ProtectDialog.txtSheetDescription": "Забороніть внесення небажаних змін іншими користувачами шляхом обмеження їхнього права на редагування.", "SSE.Views.ProtectDialog.txtSheetTitle": "Захистити лист", "SSE.Views.ProtectDialog.txtSort": "Сортувати", "SSE.Views.ProtectDialog.txtWarning": "Увага: Як<PERSON>о пароль забуто або втрачено, його не можна відновити. Зберігайте його у надійному місці.", "SSE.Views.ProtectDialog.txtWBDescription": "Щоб заборонити іншим користувачам перегляд прихованих листів, додавання, переміщення, видалення або приховування листів та перейменування листів, ви можете захистити структуру книги за допомогою пароля.", "SSE.Views.ProtectDialog.txtWBTitle": "Захистити структуру книги", "SSE.Views.ProtectRangesDlg.guestText": "Гість", "SSE.Views.ProtectRangesDlg.lockText": "Заблокований", "SSE.Views.ProtectRangesDlg.textDelete": "Видалити", "SSE.Views.ProtectRangesDlg.textEdit": "Редагувати", "SSE.Views.ProtectRangesDlg.textEmpty": "Немає діапазонів, дозволених для редагування.", "SSE.Views.ProtectRangesDlg.textNew": "Новий", "SSE.Views.ProtectRangesDlg.textProtect": "Захистити лист", "SSE.Views.ProtectRangesDlg.textPwd": "Пароль", "SSE.Views.ProtectRangesDlg.textRange": "Діапазон", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Діапазони захищеного листа, що розблоковуються паролем (тільки для заблокованих клітинок)", "SSE.Views.ProtectRangesDlg.textTitle": "Назва", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Цей елемент редагує інший користувач.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Редагувати діапазон", "SSE.Views.ProtectRangesDlg.txtNewRange": "Новий діапазон", "SSE.Views.ProtectRangesDlg.txtNo": "Ні", "SSE.Views.ProtectRangesDlg.txtTitle": "Дозволити користувачам редагувати діапазони", "SSE.Views.ProtectRangesDlg.txtYes": "Так", "SSE.Views.ProtectRangesDlg.warnDelete": "Ви дійсно хочете видалити ім'я {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Стовпчики", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Щоб видалити значення, що повторюються, виділіть один або кілька стовпчиків, що містять їх.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Мої дані містять заголовки", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Вибрати все", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Видалити дублікати", "SSE.Views.RightMenu.txtCellSettings": "Налаштування комірки", "SSE.Views.RightMenu.txtChartSettings": "Налаштування діаграми", "SSE.Views.RightMenu.txtImageSettings": "Налаштування зображення", "SSE.Views.RightMenu.txtParagraphSettings": "Параметри абзацу", "SSE.Views.RightMenu.txtPivotSettings": "Налаштування зведеної таблиці", "SSE.Views.RightMenu.txtSettings": "Загальні налаштування", "SSE.Views.RightMenu.txtShapeSettings": "Параметри форми", "SSE.Views.RightMenu.txtSignatureSettings": "Налаштування підпису", "SSE.Views.RightMenu.txtSlicerSettings": "Параметри зрізу", "SSE.Views.RightMenu.txtSparklineSettings": "Налаштування міні-діаграм", "SSE.Views.RightMenu.txtTableSettings": "Налаштування таблиці", "SSE.Views.RightMenu.txtTextArtSettings": "Налаштування текст Art", "SSE.Views.ScaleDialog.textAuto": "Авто", "SSE.Views.ScaleDialog.textError": "Введено неправильне значення.", "SSE.Views.ScaleDialog.textFewPages": "сторінки", "SSE.Views.ScaleDialog.textFitTo": "Розмістити не більш ніж на", "SSE.Views.ScaleDialog.textHeight": "Висота", "SSE.Views.ScaleDialog.textManyPages": "сторінки", "SSE.Views.ScaleDialog.textOnePage": "сторінка", "SSE.Views.ScaleDialog.textScaleTo": "Встановити", "SSE.Views.ScaleDialog.textTitle": "Налаштування масштабу", "SSE.Views.ScaleDialog.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "Максимальне значення для цього поля: {0}", "SSE.Views.SetValueDialog.txtMinText": "Мінімальне значення для цього поля: {0}", "SSE.Views.ShapeSettings.strBackground": "Кол<PERSON>р фону", "SSE.Views.ShapeSettings.strChange": "Змінити автофігуру", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Заповнити", "SSE.Views.ShapeSettings.strForeground": "Колір переднього плану", "SSE.Views.ShapeSettings.strPattern": "Візерунок", "SSE.Views.ShapeSettings.strShadow": "Зобра<PERSON>а<PERSON>и тінь", "SSE.Views.ShapeSettings.strSize": "Розмір", "SSE.Views.ShapeSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Непрозорість", "SSE.Views.ShapeSettings.strType": "Тип", "SSE.Views.ShapeSettings.textAdvanced": "Показати додаткові налаштування", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>т", "SSE.Views.ShapeSettings.textBorderSizeErr": "Введене значення невірно. <br> Будь ласка, введіть значення від 0 pt до 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Заповнити колір", "SSE.Views.ShapeSettings.textDirection": "Напрямок", "SSE.Views.ShapeSettings.textEmptyPattern": "Немає шаблону", "SSE.Views.ShapeSettings.textFlip": "Перевернути", "SSE.Views.ShapeSettings.textFromFile": "З файлу", "SSE.Views.ShapeSettings.textFromStorage": "Зі сховища", "SSE.Views.ShapeSettings.textFromUrl": "З URL", "SSE.Views.ShapeSettings.textGradient": "Градієнти", "SSE.Views.ShapeSettings.textGradientFill": "Заповнити градієнт", "SSE.Views.ShapeSettings.textHint270": "Повернути на 90° проти годинникової стрілки", "SSE.Views.ShapeSettings.textHint90": "Повернути на 90° за годинниковою стрілкою", "SSE.Views.ShapeSettings.textHintFlipH": "Перевернути зліва направо", "SSE.Views.ShapeSettings.textHintFlipV": "Перевернути зверху вниз", "SSE.Views.ShapeSettings.textImageTexture": "Зображення або текстура", "SSE.Views.ShapeSettings.textLinear": "Л<PERSON>н<PERSON><PERSON>ний", "SSE.Views.ShapeSettings.textNoFill": "Немає заповнення", "SSE.Views.ShapeSettings.textOriginalSize": "Оригінальнйи розмір", "SSE.Views.ShapeSettings.textPatternFill": "Візерунок", "SSE.Views.ShapeSettings.textPosition": "Положення", "SSE.Views.ShapeSettings.textRadial": "Раді<PERSON>л<PERSON>ний", "SSE.Views.ShapeSettings.textRecentlyUsed": "Останні використані", "SSE.Views.ShapeSettings.textRotate90": "Повернути на 90°", "SSE.Views.ShapeSettings.textRotation": "Поворот", "SSE.Views.ShapeSettings.textSelectImage": "Вибрати зображення", "SSE.Views.ShapeSettings.textSelectTexture": "Обрати", "SSE.Views.ShapeSettings.textStretch": "Розтягнути", "SSE.Views.ShapeSettings.textStyle": "Стиль", "SSE.Views.ShapeSettings.textTexture": "З текстури", "SSE.Views.ShapeSettings.textTile": "Забеспечити таємність", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Додати точку градієнта", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Видалити точку градієнта", "SSE.Views.ShapeSettings.txtBrownPaper": "Коричневий папір", "SSE.Views.ShapeSettings.txtCanvas": "Полотно", "SSE.Views.ShapeSettings.txtCarton": "Картинка", "SSE.Views.ShapeSettings.txtDarkFabric": "Темна тканина", "SSE.Views.ShapeSettings.txtGrain": "Крупинка", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> папер", "SSE.Views.ShapeSettings.txtKnit": "В'язати", "SSE.Views.ShapeSettings.txtLeather": "Шкіра", "SSE.Views.ShapeSettings.txtNoBorders": "Немає лінії", "SSE.Views.ShapeSettings.txtPapyrus": "Па<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Дерево", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Колонки", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Текстове накладення тексту", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Не переміщувати та не змінювати розміри разом з клітинками", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Альтернативний текст", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Альтернативне текстове представлення інформації про візуальний об'єкт, яке може бути прочитано людям із порушеннями зору або когнітивними дисфункціями, щоб вони могли краще зрозуміти, яка інформація міститься в зображенні, автофігурі, діаграмі або таблиці.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Назва", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON>а<PERSON><PERSON>л", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Стрілки", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Автоп<PERSON>дб<PERSON>р", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Початковий розмір", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Початок стилю", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Скіс", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Внизу", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Тип великої літери", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Номер стовпчиків", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Кінець розміру", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Кін<PERSON><PERSON>ь стилю", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Площина", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Віддзеркалено", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Висота", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "По горизонталі", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Приєднати тип", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Сталі пропорції", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Лі<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Стиль лінії", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Переміщати, але не змінювати розміри разом з клітинками", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Дозволити переповнення фігури текстом", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Вмістити текст у фігурі", "SSE.Views.ShapeSettingsAdvanced.textRight": "Право", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Поворот", "SSE.Views.ShapeSettingsAdvanced.textRound": "Кру<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Розмір", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Прив'язка до клітинки", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Розміщення між стовпцями", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Площа", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Текстове поле", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Форма - розширені налаштування", "SSE.Views.ShapeSettingsAdvanced.textTop": "Верх", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Переміщувати та змінювати розміри разом з клітинками", "SSE.Views.ShapeSettingsAdvanced.textVertically": "По вертикалі", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Ваги та стрілки", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Увага", "SSE.Views.SignatureSettings.strDelete": "Вилучити підпис", "SSE.Views.SignatureSettings.strDetails": "Склад підпису", "SSE.Views.SignatureSettings.strInvalid": "Недійсні підписи", "SSE.Views.SignatureSettings.strRequested": "Запрошені підписи", "SSE.Views.SignatureSettings.strSetup": "Налаштування підпису", "SSE.Views.SignatureSettings.strSign": "Підписати", "SSE.Views.SignatureSettings.strSignature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSigner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strValid": "Дійсні підписи", "SSE.Views.SignatureSettings.txtContinueEditing": "Все одно редагувати", "SSE.Views.SignatureSettings.txtEditWarning": "Під час редагування з електронної таблиці буде видалено підписи.<br>Продовжити?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Ви хочете видалити цей підпис?<br>Цю дію не можна скасувати.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Цю таблицю потрібно підписати.", "SSE.Views.SignatureSettings.txtSigned": "До електронної таблиці додано дійсні підписи. Таблиця захищена від редагування.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Деякі цифрові підписи в електронній таблиці є недійсними або їх не можна перевірити. Таблиця захищена від редагування.", "SSE.Views.SlicerAddDialog.textColumns": "Стовпчики", "SSE.Views.SlicerAddDialog.txtTitle": "Вставка зрізів", "SSE.Views.SlicerSettings.strHideNoData": "Сховати елементи без даних", "SSE.Views.SlicerSettings.strIndNoData": "Візуально виділяти порожні елементи", "SSE.Views.SlicerSettings.strShowDel": "Показувати елементи, видалені з джерела даних", "SSE.Views.SlicerSettings.strShowNoData": "Показувати порожні елементи останніми", "SSE.Views.SlicerSettings.strSorting": "Сортування та фільтрація", "SSE.Views.SlicerSettings.textAdvanced": "Додаткові параметри", "SSE.Views.SlicerSettings.textAsc": "За зростанням", "SSE.Views.SlicerSettings.textAZ": "Від А до Я", "SSE.Views.SlicerSettings.textButtons": "Кнопки", "SSE.Views.SlicerSettings.textColumns": "Стовпчики", "SSE.Views.SlicerSettings.textDesc": "По спаданню", "SSE.Views.SlicerSettings.textHeight": "Висота", "SSE.Views.SlicerSettings.textHor": "По горизонталі", "SSE.Views.SlicerSettings.textKeepRatio": "Зберігати пропорції", "SSE.Views.SlicerSettings.textLargeSmall": "від більшого до меншого", "SSE.Views.SlicerSettings.textLock": "Вимкнути зміну розміру або переміщення", "SSE.Views.SlicerSettings.textNewOld": "від нових до старих", "SSE.Views.SlicerSettings.textOldNew": "від старих до нових", "SSE.Views.SlicerSettings.textPosition": "Положення", "SSE.Views.SlicerSettings.textSize": "Розмір", "SSE.Views.SlicerSettings.textSmallLarge": "від меншого до більшого", "SSE.Views.SlicerSettings.textStyle": "Стиль", "SSE.Views.SlicerSettings.textVert": "По вертикалі", "SSE.Views.SlicerSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Від Я до А", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Кнопки", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Стовпчики", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Висота", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Сховати елементи без даних", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Візуально виділяти порожні елементи", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Посилання", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Показувати елементи, видалені з джерела даних", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Показувати заголовок", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Показувати порожні елементи останніми", "SSE.Views.SlicerSettingsAdvanced.strSize": "Розмір", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Сортування та фільтрація", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Стиль", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Стиль та розмір", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Не переміщувати та не змінювати розміри разом з клітинками", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Альтернативний текст", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Альтернативне текстове представлення інформації про візуальний об'єкт, яке може бути прочитано людям із порушеннями зору або когнітивними дисфункціями, щоб вони могли краще зрозуміти, яка інформація знаходиться в зображенні, автошопі, діаграмі або таблиці.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Назва", "SSE.Views.SlicerSettingsAdvanced.textAsc": "За зростанням", "SSE.Views.SlicerSettingsAdvanced.textAZ": "Від А до Я", "SSE.Views.SlicerSettingsAdvanced.textDesc": "По спаданню", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Ім'я для використання у формулах", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Заголовок", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Зберігати пропорції", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "від більшого до меншого", "SSE.Views.SlicerSettingsAdvanced.textName": "Ім'я", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "від нових до старих", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "від старих до нових", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Переміщати, але не змінювати розміри разом з клітинками", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "від меншого до більшого", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Прив'язка до клітинки", "SSE.Views.SlicerSettingsAdvanced.textSort": "Сортувати", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Ім'я джерела", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Зріз – додаткові параметри", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Переміщувати та змінювати розміри разом з клітинками", "SSE.Views.SlicerSettingsAdvanced.textZA": "Від Я до А", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Це поле є обов'язковим", "SSE.Views.SortDialog.errorEmpty": "Для кожної умови сортування має бути зазначений стовпчик або рядок.", "SSE.Views.SortDialog.errorMoreOneCol": "Виділено кілька стовпчиків.", "SSE.Views.SortDialog.errorMoreOneRow": "Виділено кілька рядків.", "SSE.Views.SortDialog.errorNotOriginalCol": "Вибраний стовпчик не належить до виділеного діапазону.", "SSE.Views.SortDialog.errorNotOriginalRow": "Вибраний рядок не належить до початкового виділеного діапазону.", "SSE.Views.SortDialog.errorSameColumnColor": "Сортування рядка або стовпця %1 за одним і тим же кольором виконується більше одного разу.<br>Видаліть повторні умови сортування та повторіть спробу.", "SSE.Views.SortDialog.errorSameColumnValue": "Сортування рядка або стовпця %1 за значеннями виконується більше одного разу.<br>Видаліть повторні умови сортування та повторіть спробу.", "SSE.Views.SortDialog.textAdd": "Додати рівень", "SSE.Views.SortDialog.textAsc": "За зростанням", "SSE.Views.SortDialog.textAuto": "Автоматично", "SSE.Views.SortDialog.textAZ": "Від А до Я", "SSE.Views.SortDialog.textBelow": "Знизу", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> комірки", "SSE.Views.SortDialog.textColumn": "Стовпчик", "SSE.Views.SortDialog.textCopy": "Копіювати рівень", "SSE.Views.SortDialog.textDelete": "Видалити рівень", "SSE.Views.SortDialog.textDesc": "По спаданню", "SSE.Views.SortDialog.textDown": "Перемістити рівень вниз", "SSE.Views.SortDialog.textFontColor": "Кол<PERSON>р шри<PERSON>ту", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(Інші стовпчики...)", "SSE.Views.SortDialog.textMoreRows": "(Інші рядки...)", "SSE.Views.SortDialog.textNone": "Немає", "SSE.Views.SortDialog.textOptions": "Параметри", "SSE.Views.SortDialog.textOrder": "Порядок", "SSE.Views.SortDialog.textRight": "Правор<PERSON>ч", "SSE.Views.SortDialog.textRow": "Рядок", "SSE.Views.SortDialog.textSort": "Сортування", "SSE.Views.SortDialog.textSortBy": "Сортування по", "SSE.Views.SortDialog.textThenBy": "Потім по", "SSE.Views.SortDialog.textTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textUp": "Перемістити рівень вверх", "SSE.Views.SortDialog.textValues": "Значення", "SSE.Views.SortDialog.textZA": "Від Я до А", "SSE.Views.SortDialog.txtInvalidRange": "Недійсний діапазон комірок.", "SSE.Views.SortDialog.txtTitle": "Сортування", "SSE.Views.SortFilterDialog.textAsc": "За зростанням (від А до Я)", "SSE.Views.SortFilterDialog.textDesc": "За спаданням (від Я до А)", "SSE.Views.SortFilterDialog.txtTitle": "Сортувати", "SSE.Views.SortOptionsDialog.textCase": "З урахуванням регістру", "SSE.Views.SortOptionsDialog.textHeaders": "Мої дані містять заголовки", "SSE.Views.SortOptionsDialog.textLeftRight": "Сортувати зліва направо", "SSE.Views.SortOptionsDialog.textOrientation": "Орієнтація", "SSE.Views.SortOptionsDialog.textTitle": "Параметри сортування", "SSE.Views.SortOptionsDialog.textTopBottom": "Сортувати зверху донизу", "SSE.Views.SpecialPasteDialog.textAdd": "Додавання", "SSE.Views.SpecialPasteDialog.textAll": "Всі", "SSE.Views.SpecialPasteDialog.textBlanks": "Пропускати пусті клітинки", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> стовпчиків", "SSE.Views.SpecialPasteDialog.textComments": "Коментарі", "SSE.Views.SpecialPasteDialog.textDiv": "Ділення", "SSE.Views.SpecialPasteDialog.textFFormat": "Формули та форматування", "SSE.Views.SpecialPasteDialog.textFNFormat": "Формули та формати чисел", "SSE.Views.SpecialPasteDialog.textFormats": "Формати", "SSE.Views.SpecialPasteDialog.textFormulas": "Формули", "SSE.Views.SpecialPasteDialog.textFWidth": "Формули та ширина стовпчиків", "SSE.Views.SpecialPasteDialog.textMult": "Множення", "SSE.Views.SpecialPasteDialog.textNone": "Немає", "SSE.Views.SpecialPasteDialog.textOperation": "Операція", "SSE.Views.SpecialPasteDialog.textPaste": "Вставити", "SSE.Views.SpecialPasteDialog.textSub": "Віднімання", "SSE.Views.SpecialPasteDialog.textTitle": "Спеціа<PERSON>ьна вставка", "SSE.Views.SpecialPasteDialog.textTranspose": "Транспонувати", "SSE.Views.SpecialPasteDialog.textValues": "Значення", "SSE.Views.SpecialPasteDialog.textVFormat": "Значення та форматування", "SSE.Views.SpecialPasteDialog.textVNFormat": "Значення та формати чисел", "SSE.Views.SpecialPasteDialog.textWBorders": "Без рамки", "SSE.Views.Spellcheck.noSuggestions": "Варіантів не знайдено", "SSE.Views.Spellcheck.textChange": "Замінити", "SSE.Views.Spellcheck.textChangeAll": "Замінити все", "SSE.Views.Spellcheck.textIgnore": "Пропустити", "SSE.Views.Spellcheck.textIgnoreAll": "Пропустити всі", "SSE.Views.Spellcheck.txtAddToDictionary": "Додати в словник", "SSE.Views.Spellcheck.txtComplete": "Перевірку орфографії закінчено", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Мова словника", "SSE.Views.Spellcheck.txtNextTip": "Перейти до наступного слова", "SSE.Views.Spellcheck.txtSpelling": "Орфографія", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Копіювати до кінця)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Перемістити до кінця)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Вставити перед листом", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Перемістити перед листом", "SSE.Views.Statusbar.filteredRecordsText": "{0} з {1} записів відфільтровано", "SSE.Views.Statusbar.filteredText": "Режим фільтрації", "SSE.Views.Statusbar.itemAverage": "Середнє", "SSE.Views.Statusbar.itemCopy": "Копіювати", "SSE.Views.Statusbar.itemCount": "Кількість", "SSE.Views.Statusbar.itemDelete": "Видалити", "SSE.Views.Statusbar.itemHidden": "Приховано", "SSE.Views.Statusbar.itemHide": "Приховати", "SSE.Views.Statusbar.itemInsert": "Вставити", "SSE.Views.Statusbar.itemMaximum": "Максимум", "SSE.Views.Statusbar.itemMinimum": "Мін<PERSON><PERSON>ум", "SSE.Views.Statusbar.itemMove": "Переміщення", "SSE.Views.Statusbar.itemProtect": "Захистити", "SSE.Views.Statusbar.itemRename": "Перейменування", "SSE.Views.Statusbar.itemStatus": "Статус збереження", "SSE.Views.Statusbar.itemSum": "Сума", "SSE.Views.Statusbar.itemTabColor": "<PERSON>о<PERSON><PERSON><PERSON> вкладки", "SSE.Views.Statusbar.itemUnProtect": "Зняти захист", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Робоча таблиця з такою назвою вже існує.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Ім'я аркуша не може містити такі символи: \\ / *? []:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Назва листа", "SSE.Views.Statusbar.selectAllSheets": "Вибрати всі листи", "SSE.Views.Statusbar.sheetIndexText": "Лист {0} з {1}", "SSE.Views.Statusbar.textAverage": "Середнє", "SSE.Views.Statusbar.textCount": "Кількість", "SSE.Views.Statusbar.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textNewColor": "Новий спеціальний колір", "SSE.Views.Statusbar.textNoColor": "Немає кольору", "SSE.Views.Statusbar.textSum": "Сума", "SSE.Views.Statusbar.tipAddTab": "Додати робочу таблицю", "SSE.Views.Statusbar.tipFirst": "Перейдіть до Першого аркушу", "SSE.Views.Statusbar.tipLast": "Перейдіть до Останнього аркушу", "SSE.Views.Statusbar.tipListOfSheets": "Список листів", "SSE.Views.Statusbar.tipNext": "Список переміщень праворуч", "SSE.Views.Statusbar.tipPrev": "Список переміщень ліворуч", "SSE.Views.Statusbar.tipZoomFactor": "Зб<PERSON>льшити", "SSE.Views.Statusbar.tipZoomIn": "Збільшити зображення", "SSE.Views.Statusbar.tipZoomOut": "Зменшити зображення", "SSE.Views.Statusbar.ungroupSheets": "Розгрупувати листи", "SSE.Views.Statusbar.zoomText": "Зб<PERSON>л<PERSON><PERSON>и<PERSON><PERSON> {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Неможливо виконати дію для вибраного діапазону комірок.<br>Виберіть єдиний діапазон даних, відмінний від існуючого, та спробуйте ще раз.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Неможливо виконати дію для вибраного діапазону комірок.<br>Виберіть діапазон таким чином, щоб перший рядок таблиці містився в одному рядку<br>,а таблиця результату перекрила поточну.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Неможливо виконати дію для вибраного діапазону комірок.<br>Виберіть діапазон, який не включає інші таблиці.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Формули масиву з кількома клітинками забороняються в таблицях.", "SSE.Views.TableOptionsDialog.txtEmpty": "Це поле є обов'язковим", "SSE.Views.TableOptionsDialog.txtFormat": "Створити таблицю", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ПОМИЛКА! Недійсний діапазон комірок", "SSE.Views.TableOptionsDialog.txtNote": "Заголовки повинні залишатися у тому ж рядку, а результуючий діапазон таблиці - частково перекриватися з вихідним діапазоном.", "SSE.Views.TableOptionsDialog.txtTitle": "Назва", "SSE.Views.TableSettings.deleteColumnText": "Видалити колону", "SSE.Views.TableSettings.deleteRowText": "Видалити рядок", "SSE.Views.TableSettings.deleteTableText": "Видалити таблицю", "SSE.Views.TableSettings.insertColumnLeftText": "Вставити стопчик зліва", "SSE.Views.TableSettings.insertColumnRightText": "Вставити стопчик справа", "SSE.Views.TableSettings.insertRowAboveText": "Вставити рядок вище", "SSE.Views.TableSettings.insertRowBelowText": "Вставити рядок нижче", "SSE.Views.TableSettings.notcriticalErrorTitle": "Застереження", "SSE.Views.TableSettings.selectColumnText": "Виберати цілі стовпці", "SSE.Views.TableSettings.selectDataText": "Виберіть дані стовпця", "SSE.Views.TableSettings.selectRowText": "Виберіть рядок", "SSE.Views.TableSettings.selectTableText": "Виберіть таблицю", "SSE.Views.TableSettings.textActions": "Дії над таблицями", "SSE.Views.TableSettings.textAdvanced": "Показати додаткові налаштування", "SSE.Views.TableSettings.textBanded": "У смужку", "SSE.Views.TableSettings.textColumns": "Колонки", "SSE.Views.TableSettings.textConvertRange": "Перетворити в діапазон", "SSE.Views.TableSettings.textEdit": "Рядки і колони", "SSE.Views.TableSettings.textEmptyTemplate": "Немає шаблонів", "SSE.Views.TableSettings.textExistName": "ПОМИЛКА! Діапазон з такою назвою вже існує", "SSE.Views.TableSettings.textFilter": "Кнопка фільтра", "SSE.Views.TableSettings.textFirst": "пер<PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "Заголовок", "SSE.Views.TableSettings.textInvalidName": "ERROR! Invalid table name", "SSE.Views.TableSettings.textIsLocked": "Цей елемент редагує інший користувач.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "Довга операція", "SSE.Views.TableSettings.textPivot": "Вставити зведену таблицю", "SSE.Views.TableSettings.textRemDuplicates": "Видалити дублікати", "SSE.Views.TableSettings.textReservedName": "Ім'я, яке ви намагаєтесь використовувати, вже посилається на формули, що містяться у комірці. Будь ласка, використайте інше ім'я.", "SSE.Views.TableSettings.textResize": "Змінити розмір таблиці", "SSE.Views.TableSettings.textRows": "Рядки", "SSE.Views.TableSettings.textSelectData": "Вибрати дату", "SSE.Views.TableSettings.textSlicer": "Вставити зріз", "SSE.Views.TableSettings.textTableName": "Назва таблиці", "SSE.Views.TableSettings.textTemplate": "Виберіть з шаблону", "SSE.Views.TableSettings.textTotal": "Загалом", "SSE.Views.TableSettings.warnLongOperation": "Операція, яку ви збираєтеся виконати, може зайняти досить багато часу. <br> Ви впевнені, що хочете продовжити?", "SSE.Views.TableSettingsAdvanced.textAlt": "Альтернативний текст", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Альтернативне текстове представлення інформації про візуальний об'єкт, яке може бути прочитано людям із порушеннями зору або когнітивними дисфункціями, щоб вони могли краще зрозуміти, яка інформація знаходиться в зображенні, автошопі, діаграмі або таблиці.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Назва", "SSE.Views.TableSettingsAdvanced.textTitle": "Таблиця - розширені налаштування", "SSE.Views.TextArtSettings.strBackground": "Кол<PERSON>р фону", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Заповнити", "SSE.Views.TextArtSettings.strForeground": "Колір переднього плану", "SSE.Views.TextArtSettings.strPattern": "Візерунок", "SSE.Views.TextArtSettings.strSize": "Розмір", "SSE.Views.TextArtSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Непрозорість", "SSE.Views.TextArtSettings.strType": "Тип", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>т", "SSE.Views.TextArtSettings.textBorderSizeErr": "Введене значення невірно. <br> Будь ласка, введіть значення від 0 pt до 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Заповнити колір", "SSE.Views.TextArtSettings.textDirection": "Напрямок", "SSE.Views.TextArtSettings.textEmptyPattern": "Немає шаблону", "SSE.Views.TextArtSettings.textFromFile": "З файлу", "SSE.Views.TextArtSettings.textFromUrl": "З URL", "SSE.Views.TextArtSettings.textGradient": "Градієнти", "SSE.Views.TextArtSettings.textGradientFill": "Заповнити градієнт", "SSE.Views.TextArtSettings.textImageTexture": "Зображення або текстура", "SSE.Views.TextArtSettings.textLinear": "Л<PERSON>н<PERSON><PERSON>ний", "SSE.Views.TextArtSettings.textNoFill": "Немає заповнення", "SSE.Views.TextArtSettings.textPatternFill": "Візерунок", "SSE.Views.TextArtSettings.textPosition": "Положення", "SSE.Views.TextArtSettings.textRadial": "Раді<PERSON>л<PERSON>ний", "SSE.Views.TextArtSettings.textSelectTexture": "Обрати", "SSE.Views.TextArtSettings.textStretch": "Розтягнути", "SSE.Views.TextArtSettings.textStyle": "Стиль", "SSE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "SSE.Views.TextArtSettings.textTexture": "З текстури", "SSE.Views.TextArtSettings.textTile": "Забеспечити таємність", "SSE.Views.TextArtSettings.textTransform": "Перетворення", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Додати точку градієнта", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Видалити точку градієнта", "SSE.Views.TextArtSettings.txtBrownPaper": "Коричневий папір", "SSE.Views.TextArtSettings.txtCanvas": "Полотно", "SSE.Views.TextArtSettings.txtCarton": "Картинка", "SSE.Views.TextArtSettings.txtDarkFabric": "Темна тканина", "SSE.Views.TextArtSettings.txtGrain": "Крупинка", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> папер", "SSE.Views.TextArtSettings.txtKnit": "В'язати", "SSE.Views.TextArtSettings.txtLeather": "Шкіра", "SSE.Views.TextArtSettings.txtNoBorders": "Немає лінії", "SSE.Views.TextArtSettings.txtPapyrus": "Па<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Дерево", "SSE.Views.Toolbar.capBtnAddComment": "Додати коментар", "SSE.Views.Toolbar.capBtnColorSchemas": "Схема кольорів", "SSE.Views.Toolbar.capBtnComment": "Коментар", "SSE.Views.Toolbar.capBtnInsHeader": "Колонтитули", "SSE.Views.Toolbar.capBtnInsSlicer": "Зр<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsSymbol": "Символ", "SSE.Views.Toolbar.capBtnMargins": "Поля", "SSE.Views.Toolbar.capBtnPageOrient": "Орієнтація", "SSE.Views.Toolbar.capBtnPageSize": "Розмір", "SSE.Views.Toolbar.capBtnPrintArea": "Область друку", "SSE.Views.Toolbar.capBtnPrintTitles": "Друкувати заголовки", "SSE.Views.Toolbar.capBtnScale": "Вписати", "SSE.Views.Toolbar.capImgAlign": "Вирівнювання", "SSE.Views.Toolbar.capImgBackward": "Перенести назад", "SSE.Views.Toolbar.capImgForward": "Перенести вперед", "SSE.Views.Toolbar.capImgGroup": "Групування", "SSE.Views.Toolbar.capInsertChart": "Діаграма", "SSE.Views.Toolbar.capInsertEquation": "Рівняння", "SSE.Views.Toolbar.capInsertHyperlink": "Гіперсилка", "SSE.Views.Toolbar.capInsertImage": "Картинка", "SSE.Views.Toolbar.capInsertShape": "Форма", "SSE.Views.Toolbar.capInsertSpark": "Спарклайн", "SSE.Views.Toolbar.capInsertTable": "Таблиця", "SSE.Views.Toolbar.capInsertText": "Текстове вікно", "SSE.Views.Toolbar.mniImageFromFile": "Картинка з файлу", "SSE.Views.Toolbar.mniImageFromStorage": "Зображення зі сховища", "SSE.Views.Toolbar.mniImageFromUrl": "Зображення з URL", "SSE.Views.Toolbar.textAddPrintArea": "Додати до області друку", "SSE.Views.Toolbar.textAlignBottom": "Вирівняти знизу", "SSE.Views.Toolbar.textAlignCenter": "Вирівняти центр", "SSE.Views.Toolbar.textAlignJust": "Обгрунтовано", "SSE.Views.Toolbar.textAlignLeft": "Вирівняти зліва", "SSE.Views.Toolbar.textAlignMiddle": "Вирівняти посередині", "SSE.Views.Toolbar.textAlignRight": "Вирівняти справа", "SSE.Views.Toolbar.textAlignTop": "Вирівняти догори", "SSE.Views.Toolbar.textAllBorders": "Всі кордони", "SSE.Views.Toolbar.textAuto": "Авто", "SSE.Views.Toolbar.textAutoColor": "Автоматичний", "SSE.Views.Toolbar.textBold": "Ж<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> кордону", "SSE.Views.Toolbar.textBordersStyle": "Стиль межі", "SSE.Views.Toolbar.textBottom": "Нижнє: ", "SSE.Views.Toolbar.textBottomBorders": "Нижні межі", "SSE.Views.Toolbar.textCenterBorders": "Всередині вертикальних кордонів", "SSE.Views.Toolbar.textClearPrintArea": "Очистити область друку", "SSE.Views.Toolbar.textClearRule": "Видалити правила", "SSE.Views.Toolbar.textClockwise": "Кут за годинниковою стрілкою", "SSE.Views.Toolbar.textColorScales": "Шкали кольору", "SSE.Views.Toolbar.textCounterCw": "Кут за годинниковою стрілкою", "SSE.Views.Toolbar.textDataBars": "Гістограми", "SSE.Views.Toolbar.textDelLeft": "Пересунути комірки ліворуч", "SSE.Views.Toolbar.textDelUp": "Пересунути комірки догори", "SSE.Views.Toolbar.textDiagDownBorder": "Діагональ внизу межі", "SSE.Views.Toolbar.textDiagUpBorder": "Діагональ вгорі межі", "SSE.Views.Toolbar.textEntireCol": "Загальна колонка", "SSE.Views.Toolbar.textEntireRow": "Загальний ряд", "SSE.Views.Toolbar.textFewPages": "сторінки", "SSE.Views.Toolbar.textHeight": "Висота", "SSE.Views.Toolbar.textHorizontal": "Горизонтальний текст", "SSE.Views.Toolbar.textInsDown": "Пересунути комірки донизу", "SSE.Views.Toolbar.textInsideBorders": "Всередині кордонів", "SSE.Views.Toolbar.textInsRight": "Пересунути комірки праворуч", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Елементи", "SSE.Views.Toolbar.textLandscape": "Альбомна", "SSE.Views.Toolbar.textLeft": "Вліво:", "SSE.Views.Toolbar.textLeftBorders": "Ліві кордони", "SSE.Views.Toolbar.textManageRule": "Керування правилами", "SSE.Views.Toolbar.textManyPages": "сторінок", "SSE.Views.Toolbar.textMarginsLast": "Останні налаштування", "SSE.Views.Toolbar.textMarginsNarrow": "Вузький", "SSE.Views.Toolbar.textMarginsNormal": "Звичайні", "SSE.Views.Toolbar.textMarginsWide": "Широ<PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Внутрішні горизонтальні межі", "SSE.Views.Toolbar.textMoreFormats": "Більше форматів", "SSE.Views.Toolbar.textMorePages": "<PERSON>н<PERSON>і сторінки", "SSE.Views.Toolbar.textNewColor": "Новий спеціальний колір", "SSE.Views.Toolbar.textNewRule": "Нове правило", "SSE.Views.Toolbar.textNoBorders": "Немає кордонів", "SSE.Views.Toolbar.textOnePage": "сторінка", "SSE.Views.Toolbar.textOutBorders": "За межами кордонів", "SSE.Views.Toolbar.textPageMarginsCustom": "Користувацькі поля", "SSE.Views.Toolbar.textPortrait": "Книжна", "SSE.Views.Toolbar.textPrint": "Роздрукувати", "SSE.Views.Toolbar.textPrintGridlines": "Друк сітки", "SSE.Views.Toolbar.textPrintHeadings": "Друк заголовків", "SSE.Views.Toolbar.textPrintOptions": "Налаштування друку", "SSE.Views.Toolbar.textRight": "Праве:", "SSE.Views.Toolbar.textRightBorders": "Праві кордони", "SSE.Views.Toolbar.textRotateDown": "Повернути текст вниз", "SSE.Views.Toolbar.textRotateUp": "Повернути текст вгору", "SSE.Views.Toolbar.textScale": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Особливий", "SSE.Views.Toolbar.textSelection": "З поточного виділеного фрагмента", "SSE.Views.Toolbar.textSetPrintArea": "Задати область друку", "SSE.Views.Toolbar.textStrikeout": "Викреслений", "SSE.Views.Toolbar.textSubscript": "Підрядні знаки", "SSE.Views.Toolbar.textSubSuperscript": "Підрядні/надрядкові знаки", "SSE.Views.Toolbar.textSuperscript": "Надрядкові знаки", "SSE.Views.Toolbar.textTabCollaboration": "Співпраця", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Формули", "SSE.Views.Toolbar.textTabHome": "Головна", "SSE.Views.Toolbar.textTabInsert": "Вставити", "SSE.Views.Toolbar.textTabLayout": "Розмітка", "SSE.Views.Toolbar.textTabProtect": "За<PERSON>ист", "SSE.Views.Toolbar.textTabView": "Вигляд", "SSE.Views.Toolbar.textThisPivot": "З цієї зведеної таблиці", "SSE.Views.Toolbar.textThisSheet": "З цього листа", "SSE.Views.Toolbar.textThisTable": "З цієї таблиці", "SSE.Views.Toolbar.textTop": "Верхнє:", "SSE.Views.Toolbar.textTopBorders": "Межі угорі", "SSE.Views.Toolbar.textUnderline": "Підкреслений", "SSE.Views.Toolbar.textVertical": "Вертикальний текст", "SSE.Views.Toolbar.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Зб<PERSON>льшити", "SSE.Views.Toolbar.tipAlignBottom": "Вирівняти знизу", "SSE.Views.Toolbar.tipAlignCenter": "Вирівняти центр", "SSE.Views.Toolbar.tipAlignJust": "Обгрунтовано", "SSE.Views.Toolbar.tipAlignLeft": "Вирівняти зліва", "SSE.Views.Toolbar.tipAlignMiddle": "Вирівняти посередині", "SSE.Views.Toolbar.tipAlignRight": "Вирівняти справа", "SSE.Views.Toolbar.tipAlignTop": "Вирівняти догори", "SSE.Views.Toolbar.tipAutofilter": "Сортувати і отфільтрувати", "SSE.Views.Toolbar.tipBack": "Назад", "SSE.Views.Toolbar.tipBorders": "Межі", "SSE.Views.Toolbar.tipCellStyle": "Стиль комірки", "SSE.Views.Toolbar.tipChangeChart": "Змінити тип діаграми", "SSE.Views.Toolbar.tipClearStyle": "Очистити", "SSE.Views.Toolbar.tipColorSchemas": "Змінити кольорову схему", "SSE.Views.Toolbar.tipCondFormat": "Умовне форматування", "SSE.Views.Toolbar.tipCopy": "Копіювати", "SSE.Views.Toolbar.tipCopyStyle": "Копіювати стиль", "SSE.Views.Toolbar.tipDecDecimal": "Зменшити десяткове значення", "SSE.Views.Toolbar.tipDecFont": "Зменшення розміру шрифту", "SSE.Views.Toolbar.tipDeleteOpt": "Вилучити комірки", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON> стилів", "SSE.Views.Toolbar.tipDigStyleCurrency": "Валютний стиль", "SSE.Views.Toolbar.tipDigStylePercent": "Процентний стиль", "SSE.Views.Toolbar.tipEditChart": "Редагувати діаграму", "SSE.Views.Toolbar.tipEditChartData": "<PERSON>и<PERSON><PERSON><PERSON> даних", "SSE.Views.Toolbar.tipEditChartType": "Змінити тип діаграми", "SSE.Views.Toolbar.tipEditHeader": "Змінити колонтитул", "SSE.Views.Toolbar.tipFontColor": "Кол<PERSON>р шри<PERSON>ту", "SSE.Views.Toolbar.tipFontName": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontSize": "Розмір шрифта", "SSE.Views.Toolbar.tipImgAlign": "Вирівняти об'єкти", "SSE.Views.Toolbar.tipImgGroup": "Згрупувати об'єкти", "SSE.Views.Toolbar.tipIncDecimal": "Збільшити десяткове число", "SSE.Views.Toolbar.tipIncFont": "Збільшення розміру шрифту", "SSE.Views.Toolbar.tipInsertChart": "Вставити діаграму", "SSE.Views.Toolbar.tipInsertChartSpark": "Вставити діаграму", "SSE.Views.Toolbar.tipInsertEquation": "Вставити рівняння", "SSE.Views.Toolbar.tipInsertHyperlink": "Додати гіперсилку", "SSE.Views.Toolbar.tipInsertImage": "Вставити зображення", "SSE.Views.Toolbar.tipInsertOpt": "Вставити комірки", "SSE.Views.Toolbar.tipInsertShape": "вставити автофігури", "SSE.Views.Toolbar.tipInsertSlicer": "Вставити зріз", "SSE.Views.Toolbar.tipInsertSpark": "Вставити спарклайн", "SSE.Views.Toolbar.tipInsertSymbol": "Вставити символ", "SSE.Views.Toolbar.tipInsertTable": "Вставити таблицю", "SSE.Views.Toolbar.tipInsertText": "Вставити напис", "SSE.Views.Toolbar.tipInsertTextart": "Вставити текст Art", "SSE.Views.Toolbar.tipMerge": "Об'єднати та помістити в центрі", "SSE.Views.Toolbar.tipNone": "Немає", "SSE.Views.Toolbar.tipNumFormat": "Номер формату", "SSE.Views.Toolbar.tipPageMargins": "Поля сторінки", "SSE.Views.Toolbar.tipPageOrient": "Орієнтація сторінки", "SSE.Views.Toolbar.tipPageSize": "Розмір сторінки", "SSE.Views.Toolbar.tipPaste": "Вставити", "SSE.Views.Toolbar.tipPrColor": "<PERSON>о<PERSON><PERSON><PERSON> заливки", "SSE.Views.Toolbar.tipPrint": "Роздрукувати", "SSE.Views.Toolbar.tipPrintArea": "Область друку", "SSE.Views.Toolbar.tipPrintTitles": "Друкувати заголовки", "SSE.Views.Toolbar.tipRedo": "Переробити", "SSE.Views.Toolbar.tipSave": "Зберегти", "SSE.Views.Toolbar.tipSaveCoauth": "Збережіть свої зміни, щоб інші користувачі могли їх переглянути.", "SSE.Views.Toolbar.tipScale": "Вписати", "SSE.Views.Toolbar.tipSendBackward": "Перенести назад", "SSE.Views.Toolbar.tipSendForward": "Перенести вперед", "SSE.Views.Toolbar.tipSynchronize": "Документ був змінений іншим користувачем. Будь ласка, натисніть, щоб зберегти зміни та перезавантажити оновлення.", "SSE.Views.Toolbar.tipTextOrientation": "Орієнтація", "SSE.Views.Toolbar.tipUndo": "Скасувати", "SSE.Views.Toolbar.tipWrap": "Обернути текст", "SSE.Views.Toolbar.txtAccounting": "Бухгалтерський облік", "SSE.Views.Toolbar.txtAdditional": "Додатковий", "SSE.Views.Toolbar.txtAscending": "Висхідний", "SSE.Views.Toolbar.txtAutosumTip": "Сума", "SSE.Views.Toolbar.txtClearAll": "Всі", "SSE.Views.Toolbar.txtClearComments": "Коментарі", "SSE.Views.Toolbar.txtClearFilter": "Очистити фільтр", "SSE.Views.Toolbar.txtClearFormat": "Формат", "SSE.Views.Toolbar.txtClearFormula": "Функція", "SSE.Views.Toolbar.txtClearHyper": "Гіперсилки", "SSE.Views.Toolbar.txtClearText": "Текст", "SSE.Views.Toolbar.txtCurrency": "Валюта", "SSE.Views.Toolbar.txtCustom": "Користувальницький", "SSE.Views.Toolbar.txtDate": "Дата", "SSE.Views.Toolbar.txtDateTime": "Дата і час", "SSE.Views.Toolbar.txtDescending": "Спускається", "SSE.Views.Toolbar.txtDollar": "$ Долар", "SSE.Views.Toolbar.txtEuro": "€ Евро", "SSE.Views.Toolbar.txtExp": "Експоненціальний", "SSE.Views.Toolbar.txtFilter": "Фільтр", "SSE.Views.Toolbar.txtFormula": "Вставити функцію", "SSE.Views.Toolbar.txtFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFranc": "CHF Швейцарський франк", "SSE.Views.Toolbar.txtGeneral": "Загальні", "SSE.Views.Toolbar.txtInteger": "Ціле число", "SSE.Views.Toolbar.txtManageRange": "Диспетчер назв", "SSE.Views.Toolbar.txtMergeAcross": "Злиття навколо", "SSE.Views.Toolbar.txtMergeCells": "Об'єднати комірки", "SSE.Views.Toolbar.txtMergeCenter": "Поля і центр", "SSE.Views.Toolbar.txtNamedRange": "Названі діапазони", "SSE.Views.Toolbar.txtNewRange": "Визначте ім'я", "SSE.Views.Toolbar.txtNoBorders": "Немає кордонів", "SSE.Views.Toolbar.txtNumber": "Номер", "SSE.Views.Toolbar.txtPasteRange": "Вставити назву", "SSE.Views.Toolbar.txtPercentage": "відсотковий вміст", "SSE.Views.Toolbar.txtPound": "£ Фунт", "SSE.Views.Toolbar.txtRouble": "₽ Рубль", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "Медіана", "SSE.Views.Toolbar.txtScheme11": "Метро", "SSE.Views.Toolbar.txtScheme12": "Модуль", "SSE.Views.Toolbar.txtScheme13": "Баг<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme14": "Іріель", "SSE.Views.Toolbar.txtScheme15": "Походження", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Солнцестояння", "SSE.Views.Toolbar.txtScheme18": "Технічний", "SSE.Views.Toolbar.txtScheme19": "Трек", "SSE.Views.Toolbar.txtScheme2": "Градація сірого", "SSE.Views.Toolbar.txtScheme20": "Міський", "SSE.Views.Toolbar.txtScheme21": "Здібність", "SSE.Views.Toolbar.txtScheme22": "Нова офісна", "SSE.Views.Toolbar.txtScheme3": "Верх", "SSE.Views.Toolbar.txtScheme4": "Аспект", "SSE.Views.Toolbar.txtScheme5": "Громадянський", "SSE.Views.Toolbar.txtScheme6": "Скупчення", "SSE.Views.Toolbar.txtScheme7": "Власний", "SSE.Views.Toolbar.txtScheme8": "Розпливатися", "SSE.Views.Toolbar.txtScheme9": "Ливарня", "SSE.Views.Toolbar.txtScientific": "Науковий", "SSE.Views.Toolbar.txtSearch": "По<PERSON><PERSON>к", "SSE.Views.Toolbar.txtSort": "Сортувати", "SSE.Views.Toolbar.txtSortAZ": "Сортувати за зростанням", "SSE.Views.Toolbar.txtSortZA": "Сортувати за зменшенням", "SSE.Views.Toolbar.txtSpecial": "Спеціальний", "SSE.Views.Toolbar.txtTableTemplate": "Формат як шаблон таблиці", "SSE.Views.Toolbar.txtText": "Текст", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Скасувати об'єднання комірок", "SSE.Views.Toolbar.txtYen": "¥ Йен", "SSE.Views.Top10FilterDialog.textType": "Показати", "SSE.Views.Top10FilterDialog.txtBottom": "Внизу", "SSE.Views.Top10FilterDialog.txtBy": "по", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON>у<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "Відсоток", "SSE.Views.Top10FilterDialog.txtSum": "Сума", "SSE.Views.Top10FilterDialog.txtTitle": "Топ 10 Автофільтрів", "SSE.Views.Top10FilterDialog.txtTop": "Верх", "SSE.Views.Top10FilterDialog.txtValueTitle": "Фільтр \"Перші 10\"", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Параметри поля значень", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Середнє", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Базове поле", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Базовий елемент", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 з %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Кількість", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Кількість чисел", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Ім'я користувача", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Від<PERSON><PERSON>нн<PERSON>сть", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Індекс", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Без обчислень", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Відсоток", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Приведена відмінність", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Відсоток від стовпчика", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Відсоток від підсумкового значення", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Відсоток від рядка", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "З наростаючим підсумком у полі", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Додаткові обчислення", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Ім'я джерела:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Станд.відхилення", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Сума", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Операція", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Ди<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "Дис<PERSON>р", "SSE.Views.ViewManagerDlg.closeButtonText": "Закрити", "SSE.Views.ViewManagerDlg.guestText": "Гість", "SSE.Views.ViewManagerDlg.lockText": "Заблокований", "SSE.Views.ViewManagerDlg.textDelete": "Видалити", "SSE.Views.ViewManagerDlg.textDuplicate": "Дублювати", "SSE.Views.ViewManagerDlg.textEmpty": "Подання ще не створені.", "SSE.Views.ViewManagerDlg.textGoTo": "Перейти до перегляду", "SSE.Views.ViewManagerDlg.textLongName": "Введіть ім'я не довше ніж 128 символів.", "SSE.Views.ViewManagerDlg.textNew": "Нове", "SSE.Views.ViewManagerDlg.textRename": "Перейменувати", "SSE.Views.ViewManagerDlg.textRenameError": "Назва вигляду не повинна бути порожньою.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Перейменувати назву мініатюри", "SSE.Views.ViewManagerDlg.textViews": "Вигляди листа", "SSE.Views.ViewManagerDlg.tipIsLocked": "Цей елемент редагує інший користувач.", "SSE.Views.ViewManagerDlg.txtTitle": "Диспетчер виглядів листа", "SSE.Views.ViewManagerDlg.warnDeleteView": "Ви намагаєтеся видалити активований в цей момент вигляд '%1'.<br>Закрити цей вигляд та видалити його?", "SSE.Views.ViewTab.capBtnFreeze": "Закріпити області", "SSE.Views.ViewTab.capBtnSheetView": "Вигляд листа", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Завжди показувати панель інструментів", "SSE.Views.ViewTab.textClose": "Закрити", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Об'єднати рядки листів та стану", "SSE.Views.ViewTab.textCreate": "Нове", "SSE.Views.ViewTab.textDefault": "За замовчуванням", "SSE.Views.ViewTab.textFormula": "Рядок формул", "SSE.Views.ViewTab.textFreezeCol": "Закріпити перший стовпчик", "SSE.Views.ViewTab.textFreezeRow": "Закріпити верхній рядок", "SSE.Views.ViewTab.textGridlines": "Лінії сітки", "SSE.Views.ViewTab.textHeadings": "Заголовки", "SSE.Views.ViewTab.textInterfaceTheme": "Тема інтерфейсу", "SSE.Views.ViewTab.textManager": "Диспетчер представлень", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Показувати тінь для закріплених областей", "SSE.Views.ViewTab.textUnFreeze": "Зняти закріплення областей", "SSE.Views.ViewTab.textZeros": "Показувати нулі", "SSE.Views.ViewTab.textZoom": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "Закрити перегляд листа", "SSE.Views.ViewTab.tipCreate": "Створити вигляд листа", "SSE.Views.ViewTab.tipFreeze": "Закріпити області", "SSE.Views.ViewTab.tipSheetView": "Вигляд листа", "SSE.Views.WBProtection.hintAllowRanges": "Дозволити редагувати діапазони", "SSE.Views.WBProtection.hintProtectSheet": "Захистити лист", "SSE.Views.WBProtection.hintProtectWB": "Захистити книгу", "SSE.Views.WBProtection.txtAllowRanges": "Дозволити редагувати діапазони", "SSE.Views.WBProtection.txtHiddenFormula": "Приховані формули", "SSE.Views.WBProtection.txtLockedCell": "Заблокована клітинка", "SSE.Views.WBProtection.txtLockedShape": "Заблокована фігура", "SSE.Views.WBProtection.txtLockedText": "Заблокувати текст", "SSE.Views.WBProtection.txtProtectSheet": "Захистити лист", "SSE.Views.WBProtection.txtProtectWB": "Захистити книгу", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Введіть пароль для вимкнення захисту листа", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Зняти захист листа", "SSE.Views.WBProtection.txtWBUnlockDescription": "Введіть пароль для вимкнення захисту книги", "SSE.Views.WBProtection.txtWBUnlockTitle": "Зняти захист книги"}