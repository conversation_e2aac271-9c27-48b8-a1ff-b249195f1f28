﻿<!DOCTYPE html>
<html>
	<head>
		<title>FORMELTEXT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>FORMELTEXT-Funktion</h1>
			<p>Die Funktion <b>FORMELTEXT</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Sie gibt eine Formel als eine Zeichenfolge zurück (die Textzeichenfolge, die in der Bearbeitungsleiste angezeigt wird, wenn Sie die Zelle mit der Formel auswählen).</p>
			<p>Die Formelsyntax der Funktion <b>FORMELTEXT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>FORMELTEXT(Bezug)</em></b></p> 
			<p><b><em>Bezug</em></b> ist ein Bezug auf eine Zelle oder einen Zellbereich.</p>
            <p>Enthält der Zellenbereich auf den Bezug genommen wird mehr als eine Formel, gibt die Formel <b>FORMELTEXT</b> den Wert in der linken oberen Zelle der Zeile, der Spalte oder des Bereichs zurück. Enthält die Zelle auf die Bezug genommen wird keine Formel, gibt die Formel <b>FORMELTEXT</b> den Fehlerwert <b>#NV</b> zurück.</p>
			<p>Anwendung der Funktion <b>FORMELTEXT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>FORMELTEXT</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="FORMELTEXT-Funktion" src="../images/formulatext.png" /></p>
		</div>
	</body>
</html>