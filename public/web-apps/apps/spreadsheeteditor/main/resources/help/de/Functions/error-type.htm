﻿<!DOCTYPE html>
<html>
	<head>
		<title>FEHLER.TYP-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>FEHLER.TYP-Funktion</h1>
			<p>Die Funktion <b>FEHLER.TYP</b> gehört zur Gruppe der Informationsfunktionen. Sie gibt die numerische Darstellung von einem der vorhandenen Fehler zurück.</p>
			<p>Die Formelsyntax der Funktion <b>FEHLER.TYP</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>FEHLER.TYP(Fehlerwert)</em></b></p> 
			<p><b><em>Fehlerwert</em></b> ist der Fehlerwert dessen Kennnummer Sie finden möchten. Ein nummerischer Wert, der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen. Mögliche Fehlerwerte:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Fehlerwert</b></td>
					<td><b>Nummerische Darstellung</b></td>
				</tr>
				<tr>
					<td>#NULL!</td>
					<td>1</td>
				</tr>
				<tr>
					<td>#DIV/0!</td>
					<td>2</td>
				</tr>
				<tr>
					<td>#WERT!</td>
					<td>3</td>
				</tr>
				<tr>
					<td>#BEZUG!</td>
					<td>4</td>
				</tr>
				<tr>
					<td>#NAME?</td>
					<td>5</td>
				</tr>
				<tr>
					<td>#NUM!</td>
					<td>6</td>
				</tr>
				<tr>
					<td>#NV</td>
					<td>7</td>
				</tr>
				<tr>
					<td>#DATEN_ABRUFEN</td>
					<td>8</td>
				</tr>
				<tr>
					<td>Sonstige</td>
					<td>#NV</td>
				</tr>
			</table>
			<p>Anwendung der Funktion <b>FEHLER.TYP</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>FEHLER.TYP</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="FEHLER.TYP-Funktion" src="../images/error.type.png" /></p>
		</div>
	</body>
</html>