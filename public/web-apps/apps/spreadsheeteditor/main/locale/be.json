{"cancelButtonText": "Скасаваць", "Common.Controllers.Chat.notcriticalErrorTitle": "Увага", "Common.Controllers.Chat.textEnterMessage": "Увядзіце сюды сваё паведамленне", "Common.define.chartData.textArea": "Вобласць", "Common.define.chartData.textBar": "Лінія", "Common.define.chartData.textBarNormal3d": "Трохвымерная гістаграма з групаваннем", "Common.define.chartData.textBarNormal3dPerspective": "Трохвымерная гістаграма", "Common.define.chartData.textBarStacked3d": "Трохвымерная састаўная гістаграма", "Common.define.chartData.textBarStackedPer3d": "Трохвымерная састаўная гістаграма 100%", "Common.define.chartData.textCharts": "Дыяграмы", "Common.define.chartData.textColumn": "Гістаграма", "Common.define.chartData.textColumnSpark": "Гістаграма", "Common.define.chartData.textHBarNormal3d": "Трохвымерная лінейная з групаваннем", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textPie": "Па крузе", "Common.define.chartData.textPoint": "XY (рассеяная)", "Common.define.chartData.textSparks": "Спарклайны", "Common.define.chartData.textStock": "Біржа", "Common.define.chartData.textSurface": "Паверхня", "Common.define.chartData.textWinLossSpark": "Выйгрыш/параза", "Common.define.conditionalData.textAbove": "вышэй", "Common.define.conditionalData.textAverage": "Сярэдняе", "Common.define.conditionalData.textBegins": "Пачынаецца з", "Common.define.conditionalData.textBelow": "ніж<PERSON>й", "Common.define.conditionalData.textBetween": "паміж", "Common.define.conditionalData.textBlank": "Пусты", "Common.define.conditionalData.textBottom": "Ніжняе", "Common.define.conditionalData.textContains": "Змяшчае", "Common.define.conditionalData.textEnds": "Заканчваецца на", "Common.define.conditionalData.textError": "Памылка", "Common.define.conditionalData.textFormula": "Формула", "Common.define.conditionalData.textGreater": "<PERSON>о<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreaterEq": "Больш альбо роўна", "Common.define.conditionalData.textLessEq": "Мен<PERSON> альбо роўна", "Common.Translation.warnFileLocked": "Дакумент выкарыстоўваецца іншай праграмай. Вы можаце працягнуць рэдагаванне і захаваць яго як копію.", "Common.UI.ButtonColored.textAutoColor": "Аўтаматычна", "Common.UI.ButtonColored.textNewColor": "Адвольны колер", "Common.UI.ComboBorderSize.txtNoBorders": "Без межаў", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Без межаў", "Common.UI.ComboDataView.emptyComboText": "Без стыляў", "Common.UI.ExtendedColorDialog.addButtonText": "Дада<PERSON>ь", "Common.UI.ExtendedColorDialog.textCurrent": "Бягучы", "Common.UI.ExtendedColorDialog.textHexErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 000000 да FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Новы", "Common.UI.ExtendedColorDialog.textRGBErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце лік ад 0 да 255.", "Common.UI.HSBColorPicker.textNoColor": "Без колеру", "Common.UI.SearchDialog.textHighlight": "Падсвятліць вынікі", "Common.UI.SearchDialog.textMatchCase": "Улічваць рэгістр", "Common.UI.SearchDialog.textReplaceDef": "Увядзіце тэкст для замены", "Common.UI.SearchDialog.textSearchStart": "Увядзіце сюды тэкст", "Common.UI.SearchDialog.textTitle": "Пошук і замена", "Common.UI.SearchDialog.textTitle2": "По<PERSON><PERSON>к", "Common.UI.SearchDialog.textWholeWords": "Толькі слова цалкам", "Common.UI.SearchDialog.txtBtnHideReplace": "Схаваць замену", "Common.UI.SearchDialog.txtBtnReplace": "Замяніць", "Common.UI.SearchDialog.txtBtnReplaceAll": "Замяніць усе", "Common.UI.SynchronizeTip.textDontShow": "Больш не паказваць гэтае паведамленне", "Common.UI.SynchronizeTip.textSynchronize": "Дакумент быў зменены іншым карыстальнікам.<br>Націсніце, каб захаваць свае змены і загрузіць абнаўленні.", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартныя колеры", "Common.UI.ThemeColorPalette.textThemeColors": "Колеры тэмы", "Common.UI.Themes.txtThemeDark": "Цёмная", "Common.UI.Themes.txtThemeLight": "Светлая", "Common.UI.Window.cancelButtonText": "Скасаваць", "Common.UI.Window.closeButtonText": "Закрыць", "Common.UI.Window.noButtonText": "Не", "Common.UI.Window.okButtonText": "Добра", "Common.UI.Window.textConfirmation": "Пацвярджэнне", "Common.UI.Window.textDontShow": "Больш не паказваць гэтае паведамленне", "Common.UI.Window.textError": "Памылка", "Common.UI.Window.textInformation": "Інфармацыя", "Common.UI.Window.textWarning": "Увага", "Common.UI.Window.yesButtonText": "Так", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "пт", "Common.Views.About.txtAddress": "адрас:", "Common.Views.About.txtLicensee": "ЛІЦЭНЗІЯТ", "Common.Views.About.txtLicensor": "ЛІЦЭНЗІЯР", "Common.Views.About.txtMail": "адрас электроннай пошты", "Common.Views.About.txtPoweredBy": "Распрацавана", "Common.Views.About.txtTel": "тэл.:", "Common.Views.About.txtVersion": "Версія", "Common.Views.AutoCorrectDialog.textAdd": "Дада<PERSON>ь", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Ужываць падчас працы", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Аўтазамена", "Common.Views.AutoCorrectDialog.textAutoFormat": "Аўтафарматаванне падчас уводу", "Common.Views.AutoCorrectDialog.textBy": "На", "Common.Views.AutoCorrectDialog.textDelete": "Выдаліць", "Common.Views.AutoCorrectDialog.textMathCorrect": "Аўтазамена матэматычнымі сімваламі", "Common.Views.AutoCorrectDialog.textNewRowCol": "Уключаць у табліцу новыя радкі і слупкі", "Common.Views.AutoCorrectDialog.textRecognized": "Распазнаныя формулы", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Гэтыя выразы распазнаныя як матэматычныя. Яны не будуць пазначацца курсівам.", "Common.Views.AutoCorrectDialog.textReplace": "Замяніць", "Common.Views.AutoCorrectDialog.textReplaceType": "Замяніць тэкст падчас уводу", "Common.Views.AutoCorrectDialog.textReset": "Скінуць", "Common.Views.AutoCorrectDialog.textResetAll": "Скінуць да прадвызначанага", "Common.Views.AutoCorrectDialog.textRestore": "Аднавіць", "Common.Views.AutoCorrectDialog.textTitle": "Аўтазамена", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Распазнаныя формулы могуць змяшчаць толькі вялікія і малыя літары ад А да Я.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Любы дададзены вамі выраз будзе выдалены, а значэнні вернуцца да прадвызначаных. Хочаце працягнуць?", "Common.Views.AutoCorrectDialog.warnReplace": "Аўтазамена для %1 ужо існуе. Хочаце замяніць яе?", "Common.Views.AutoCorrectDialog.warnReset": "Любая дададзеная вамі аўтазамена будзе выдаленая, а значэнні вернуцца да прадвызначаных. Хочаце працягнуць?", "Common.Views.AutoCorrectDialog.warnRestore": "Аўтазамена для %1 скінутая да прадвызначанага значэння. Хочаце працягнуць?", "Common.Views.Chat.textSend": "Адправіць", "Common.Views.Comments.textAdd": "Дада<PERSON>ь", "Common.Views.Comments.textAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "Common.Views.Comments.textAddCommentToDoc": "Дада<PERSON>ь каментар да дакумента", "Common.Views.Comments.textAddReply": "Дада<PERSON>ь адказ", "Common.Views.Comments.textAll": "Усе", "Common.Views.Comments.textAnonym": "Госць", "Common.Views.Comments.textCancel": "Скасаваць", "Common.Views.Comments.textClose": "Закрыць", "Common.Views.Comments.textComments": "Каментары", "Common.Views.Comments.textEdit": "Добра", "Common.Views.Comments.textEnterCommentHint": "Увядзіце сюды свой каментар", "Common.Views.Comments.textHintAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "Common.Views.Comments.textOpenAgain": "Адкрыць зноў", "Common.Views.Comments.textReply": "Адказаць", "Common.Views.Comments.textResolve": "Вырашыць", "Common.Views.Comments.textResolved": "Вырашана", "Common.Views.CopyWarningDialog.textDontShow": "Больш не паказваць гэтае паведамленне", "Common.Views.CopyWarningDialog.textMsg": "Аперацыі капіявання, выразання і ўстаўкі можна выканаць пры дапамозе кнопак на панэлі інструментаў і загадаў кантэкстнага меню толькі ў гэтай укладцы рэдактара.<br><br>Для ўзаемадзеяння з іншымі праграмамі выкарыстоўвайце наступныя спалучэнні клавіш:", "Common.Views.CopyWarningDialog.textTitle": "Скапіяваць, выразаць, уставіць", "Common.Views.CopyWarningDialog.textToCopy": "для капіявання", "Common.Views.CopyWarningDialog.textToCut": "для выразання", "Common.Views.CopyWarningDialog.textToPaste": "для ўстаўкі", "Common.Views.DocumentAccessDialog.textLoading": "Загрузка…", "Common.Views.DocumentAccessDialog.textTitle": "Налады супольнага доступу", "Common.Views.EditNameDialog.textLabel": "Адмеціна:", "Common.Views.EditNameDialog.textLabelError": "Адмеціна не можа быць пустой", "Common.Views.Header.labelCoUsersDescr": "Дакумент рэдагуецца карыстальнікамі:", "Common.Views.Header.textAdvSettings": "Дадатковыя налады", "Common.Views.Header.textBack": "Перайсці да дакументаў", "Common.Views.Header.textCompactView": "Схаваць панэль прылад", "Common.Views.Header.textHideLines": "Схаваць лінейкі", "Common.Views.Header.textHideStatusBar": "Аб'яднаць панэлі радкоў і стану", "Common.Views.Header.textSaveBegin": "Захаванне…", "Common.Views.Header.textSaveChanged": "Зменена", "Common.Views.Header.textSaveEnd": "Усе змены захаваныя", "Common.Views.Header.textSaveExpander": "Усе змены захаваныя", "Common.Views.Header.textZoom": "Ма<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Кіраванне правамі на доступ да дакумента", "Common.Views.Header.tipDownload": "Спампаваць файл", "Common.Views.Header.tipGoEdit": "Рэдагаваць бягучы файл", "Common.Views.Header.tipPrint": "Друкаваць файл", "Common.Views.Header.tipRedo": "Паўтарыць", "Common.Views.Header.tipSave": "Захаваць", "Common.Views.Header.tipUndo": "Адрабіць", "Common.Views.Header.tipUndock": "Адмацаваць у асобнае акно", "Common.Views.Header.tipViewSettings": "Налады прагляду", "Common.Views.Header.tipViewUsers": "Прагляд карыстальнікаў і кіраванне правамі на доступ да дакумента", "Common.Views.Header.txtAccessRights": "Змяніць правы на доступ", "Common.Views.Header.txtRename": "Змяніць назву", "Common.Views.History.textCloseHistory": "Закрыць гісторыю", "Common.Views.History.textHideAll": "Схаваць падрабязныя змены", "Common.Views.History.textShow": "Пашырыць", "Common.Views.ImageFromUrlDialog.textUrl": "Устаўце URL выявы:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Гэтае поле павінна быць URL-адрасам фармату \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "З адзнакамі", "Common.Views.ListSettingsDialog.textNumbering": "Нумараваны", "Common.Views.ListSettingsDialog.tipChange": "Змяніць адзнаку", "Common.Views.ListSettingsDialog.txtBullet": "Адзнака", "Common.Views.ListSettingsDialog.txtColor": "<PERSON>о<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "Новая адзнака", "Common.Views.ListSettingsDialog.txtNone": "Няма", "Common.Views.ListSettingsDialog.txtOfText": "% тэксту", "Common.Views.ListSettingsDialog.txtSize": "<PERSON>а<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Пачаць з", "Common.Views.ListSettingsDialog.txtSymbol": "Сімвал", "Common.Views.ListSettingsDialog.txtTitle": "Налады спіса", "Common.Views.ListSettingsDialog.txtType": "Тып", "Common.Views.OpenDialog.closeButtonText": "Закрыць файл", "Common.Views.OpenDialog.textInvalidRange": "Хібны дыяпазон ячэек", "Common.Views.OpenDialog.txtAdvanced": "Дадаткова", "Common.Views.OpenDialog.txtColon": "Двукроп’е", "Common.Views.OpenDialog.txtComma": "Коска", "Common.Views.OpenDialog.txtDelimiter": "Падзяляльнік", "Common.Views.OpenDialog.txtEncoding": "Кадаванне", "Common.Views.OpenDialog.txtIncorrectPwd": "Уведзены хібны пароль.", "Common.Views.OpenDialog.txtOpenFile": "Каб адкрыць файл, увядзіце пароль", "Common.Views.OpenDialog.txtOther": "Іншае", "Common.Views.OpenDialog.txtPassword": "Пароль", "Common.Views.OpenDialog.txtPreview": "Прагляд", "Common.Views.OpenDialog.txtProtected": "Калі вы ўвядзеце пароль і адкрыеце файл бягучы пароль да файла скінецца.", "Common.Views.OpenDialog.txtSemicolon": "Коска з кропкай", "Common.Views.OpenDialog.txtSpace": "Прагал", "Common.Views.OpenDialog.txtTab": "Табуляцыя", "Common.Views.OpenDialog.txtTitle": "Абраць параметры %1", "Common.Views.OpenDialog.txtTitleProtected": "Абаронены файл", "Common.Views.PasswordDialog.txtDescription": "Прызначце пароль, каб абараніць гэты дакумент", "Common.Views.PasswordDialog.txtIncorrectPwd": "Паролі адрозніваюцца", "Common.Views.PasswordDialog.txtPassword": "Пароль", "Common.Views.PasswordDialog.txtRepeat": "Паўтарыць пароль", "Common.Views.PasswordDialog.txtTitle": "Прызначыць пароль", "Common.Views.PasswordDialog.txtWarning": "Увага: Страчаны або забыты пароль аднавіць немагчыма. Захоўвайце яго ў надзейным месцы.", "Common.Views.PluginDlg.textLoading": "Загрузка", "Common.Views.Plugins.groupCaption": "Убудовы", "Common.Views.Plugins.strPlugins": "Убудовы", "Common.Views.Plugins.textLoading": "Загрузка", "Common.Views.Plugins.textStart": "Запуск", "Common.Views.Plugins.textStop": "Спыніць", "Common.Views.Protection.hintAddPwd": "Зашыфраваць пры дапамозе пароля", "Common.Views.Protection.hintPwd": "Змяніць альбо выдаліць пароль", "Common.Views.Protection.hintSignature": "Дадаць лічбавы подпіс альбо радок подпісу", "Common.Views.Protection.txtAddPwd": "Дадаць пароль", "Common.Views.Protection.txtChangePwd": "Змяніць пароль", "Common.Views.Protection.txtDeletePwd": "Выдаліць пароль", "Common.Views.Protection.txtEncrypt": "Шыфраванне", "Common.Views.Protection.txtInvisibleSignature": "Дадаць лічбавы подпіс", "Common.Views.Protection.txtSignature": "Под<PERSON><PERSON>с", "Common.Views.Protection.txtSignatureLine": "Дадаць радок подпісу", "Common.Views.RenameDialog.textName": "Назва файла", "Common.Views.RenameDialog.txtInvalidName": "Назва файла не павінна змяшчаць наступныя сімвалы:", "Common.Views.ReviewChanges.hintNext": "Да наступнай змены", "Common.Views.ReviewChanges.hintPrev": "Да папярэдняй змены", "Common.Views.ReviewChanges.strFast": "Хутк<PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Сумеснае рэдагаванне ў рэжыме рэальнага часу. Ўсе змены захоўваюцца аўтаматычна.", "Common.Views.ReviewChanges.strStrict": "Строгі", "Common.Views.ReviewChanges.strStrictDesc": "Выкарыстоўвайце кнопку \"Захаваць\" для сінхранізацыі вашых змен і змен іншых карыстальнікаў.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Ухваліць бягучыя змены", "Common.Views.ReviewChanges.tipCoAuthMode": "Уключыць рэжым сумеснага рэдагавання", "Common.Views.ReviewChanges.tipCommentRem": "Выдаліць каментары", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Выдаліць бягучыя каментары", "Common.Views.ReviewChanges.tipHistory": "Паказваць гісторыю версій", "Common.Views.ReviewChanges.tipRejectCurrent": "Адхіліць бягучыя змены", "Common.Views.ReviewChanges.tipReview": "Адсочваць змены", "Common.Views.ReviewChanges.tipReviewView": "Абярыце рэжым, у якім неабходна адлюстроўваць змены", "Common.Views.ReviewChanges.tipSetDocLang": "Вызначыць мову дакумента ", "Common.Views.ReviewChanges.tipSetSpelling": "Праверка правапісу", "Common.Views.ReviewChanges.tipSharing": "Кіраванне правамі на доступ да дакумента", "Common.Views.ReviewChanges.txtAccept": "Ухваліць", "Common.Views.ReviewChanges.txtAcceptAll": "Ухваліць усе змены", "Common.Views.ReviewChanges.txtAcceptChanges": "Ухваліць змены", "Common.Views.ReviewChanges.txtAcceptCurrent": "Ухваліць бягучыя змены", "Common.Views.ReviewChanges.txtChat": "Размова", "Common.Views.ReviewChanges.txtClose": "Закрыць", "Common.Views.ReviewChanges.txtCoAuthMode": "Рэжым сумеснага рэдагавання", "Common.Views.ReviewChanges.txtCommentRemAll": "Выдаліць усе каментары", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Выдаліць бягучыя каментары", "Common.Views.ReviewChanges.txtCommentRemMy": "Выдаліць мае каментары", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Выдаліць мае бягучыя каментары", "Common.Views.ReviewChanges.txtCommentRemove": "Выдаліць", "Common.Views.ReviewChanges.txtCommentResolve": "Вырашыць", "Common.Views.ReviewChanges.txtDocLang": "Мова", "Common.Views.ReviewChanges.txtFinal": "Усе змены ўхваленыя (папярэдні прагляд)", "Common.Views.ReviewChanges.txtFinalCap": "Выніковы дакумент", "Common.Views.ReviewChanges.txtHistory": "Гісторыя версій", "Common.Views.ReviewChanges.txtMarkup": "Усе змены (рэдагаванне)", "Common.Views.ReviewChanges.txtMarkupCap": "Змены", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Усе змены адкінутыя (папярэдні прагляд)", "Common.Views.ReviewChanges.txtOriginalCap": "Зыходны дакумент", "Common.Views.ReviewChanges.txtPrev": "Папярэдняе", "Common.Views.ReviewChanges.txtReject": "Адх<PERSON><PERSON><PERSON>ць", "Common.Views.ReviewChanges.txtRejectAll": "Адхіліць усе змены", "Common.Views.ReviewChanges.txtRejectChanges": "Адх<PERSON><PERSON><PERSON><PERSON>ь змены", "Common.Views.ReviewChanges.txtRejectCurrent": "Адхіліць бягучыя змены", "Common.Views.ReviewChanges.txtSharing": "Супольны доступ", "Common.Views.ReviewChanges.txtSpelling": "Праверка правапісу", "Common.Views.ReviewChanges.txtTurnon": "Адсочванне змен", "Common.Views.ReviewChanges.txtView": "Адлюстраванне", "Common.Views.ReviewPopover.textAdd": "Дада<PERSON>ь", "Common.Views.ReviewPopover.textAddReply": "Дада<PERSON>ь адказ", "Common.Views.ReviewPopover.textCancel": "Скасаваць", "Common.Views.ReviewPopover.textClose": "Закрыць", "Common.Views.ReviewPopover.textEdit": "Добра", "Common.Views.ReviewPopover.textMention": "+апавяшчэнне дасць доступ да дакумента і адправіць апавяшчэнне па электроннай пошце", "Common.Views.ReviewPopover.textMentionNotify": "+апавяшчэнне адправіць карыстальніку апавяшчэнне па электроннай пошце", "Common.Views.ReviewPopover.textOpenAgain": "Адкрыць зноў", "Common.Views.ReviewPopover.textReply": "Адказаць", "Common.Views.ReviewPopover.textResolve": "Вырашыць", "Common.Views.ReviewPopover.txtDeleteTip": "Выдаліць", "Common.Views.ReviewPopover.txtEditTip": "Рэдагаваць", "Common.Views.SaveAsDlg.textLoading": "Загрузка", "Common.Views.SaveAsDlg.textTitle": "Каталог для захавання", "Common.Views.SearchPanel.textSearchAgain": "{0}Выканаць новы пошук{1}, каб атрымаць дакладныя вынікі.", "Common.Views.SelectFileDlg.textLoading": "Загрузка", "Common.Views.SelectFileDlg.textTitle": "Абраць крыніцу даных", "Common.Views.SignDialog.textBold": "Тоўсты", "Common.Views.SignDialog.textCertificate": "Сертыфікат", "Common.Views.SignDialog.textChange": "Змяніць", "Common.Views.SignDialog.textInputName": "Увядзіце імя падпісальніка", "Common.Views.SignDialog.textItalic": "Курсіў", "Common.Views.SignDialog.textPurpose": "Мэта падпісання дакумента", "Common.Views.SignDialog.textSelect": "Абраць", "Common.Views.SignDialog.textSelectImage": "Абраць выяву", "Common.Views.SignDialog.textSignature": "Выгляд подпісу:", "Common.Views.SignDialog.textTitle": "Падпісаць дакумент", "Common.Views.SignDialog.textUseImage": "альбо націсніце \"Абраць выяву\", каб выкарыстаць выяву як подпіс", "Common.Views.SignDialog.textValid": "Сапраўдны з %1 па %2", "Common.Views.SignDialog.tipFontName": "Назва шрыфту", "Common.Views.SignDialog.tipFontSize": "Памер шрыфту", "Common.Views.SignSettingsDialog.textAllowComment": "Дазволіць падпісальніку дадаваць каментары ў дыялогу подпісу", "Common.Views.SignSettingsDialog.textInfoEmail": "Адрас электроннай пошты", "Common.Views.SignSettingsDialog.textInfoName": "Імя", "Common.Views.SignSettingsDialog.textInfoTitle": "Пасада падпісальніка", "Common.Views.SignSettingsDialog.textInstructions": "Інструкцыі для падпісальніка", "Common.Views.SignSettingsDialog.textShowDate": "Паказваць дату подпісу ў радку подпісу", "Common.Views.SignSettingsDialog.textTitle": "Наладжванне подпісу", "Common.Views.SignSettingsDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "Common.Views.SymbolTableDialog.textCharacter": "Знак", "Common.Views.SymbolTableDialog.textCode": "Шаснаццатковы код Юнікод", "Common.Views.SymbolTableDialog.textCopyright": "Знак аўтарскіх правоў", "Common.Views.SymbolTableDialog.textDCQuote": "Закрывальнае двукоссе", "Common.Views.SymbolTableDialog.textDOQuote": "Адкрывальнае двукоссе", "Common.Views.SymbolTableDialog.textEllipsis": "Гарызантальнае шматкроп’е", "Common.Views.SymbolTableDialog.textEmDash": "Доўгі злучок", "Common.Views.SymbolTableDialog.textEmSpace": "Доўгі прагал", "Common.Views.SymbolTableDialog.textEnDash": "Кароткі злучок", "Common.Views.SymbolTableDialog.textEnSpace": "Кароткі прагал", "Common.Views.SymbolTableDialog.textFont": "Шры<PERSON>т", "Common.Views.SymbolTableDialog.textNBHyphen": "Неразрыўны праця<PERSON>нік", "Common.Views.SymbolTableDialog.textNBSpace": "Неразрыўны прагал", "Common.Views.SymbolTableDialog.textPilcrow": "Знак абзаца", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 прагала", "Common.Views.SymbolTableDialog.textRange": "Дыяпазон", "Common.Views.SymbolTableDialog.textRecent": "Раней выкарыстаныя сімвалы", "Common.Views.SymbolTableDialog.textRegistered": "Зарэгістраваны знак", "Common.Views.SymbolTableDialog.textSCQuote": "Закрывальная коска", "Common.Views.SymbolTableDialog.textSection": "Знак раздзела", "Common.Views.SymbolTableDialog.textShortcut": "Спалучэнне клавіш", "Common.Views.SymbolTableDialog.textSHyphen": "Мяккі працяжнік", "Common.Views.SymbolTableDialog.textSOQuote": "Адкрывальная коска", "Common.Views.SymbolTableDialog.textSpecial": "Адмысловыя сімвалы", "Common.Views.SymbolTableDialog.textSymbols": "Сімвалы", "Common.Views.SymbolTableDialog.textTitle": "Сімвал", "Common.Views.SymbolTableDialog.textTradeMark": "Сімвал таварнага знака", "Common.Views.UserNameDialog.textLabel": "Адмеціна:", "Common.Views.UserNameDialog.textLabelError": "Адмеціна не можа быць пустой", "SSE.Controllers.DataTab.textColumns": "Слупкі", "SSE.Controllers.DataTab.textWizard": "Тэкст па слупках", "SSE.Controllers.DataTab.txtExpand": "Пашырыць", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Даныя побач з абраным дыяпазонам не выдаляцца. Хочаце пашырыць абраны дыяпазон, каб уключыць даныя з вакольных ячэек, альбо працягнуць толькі з абраным дыяпазонам? ", "SSE.Controllers.DataTab.txtRemDuplicates": "Выдаліць паўторы", "SSE.Controllers.DataTab.txtRemSelected": "Выдаліць у вылучаным", "SSE.Controllers.DocumentHolder.alignmentText": "Выраўноўванне", "SSE.Controllers.DocumentHolder.centerText": "Па цэнтры", "SSE.Controllers.DocumentHolder.deleteColumnText": "Выдаліць слупок", "SSE.Controllers.DocumentHolder.deleteRowText": "Выдаліць радок", "SSE.Controllers.DocumentHolder.deleteText": "Выдаліць", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Спасылка паказвае на ячэйку, якая не існуе. Выпраўце ці выдаліце спасылку.", "SSE.Controllers.DocumentHolder.guestText": "Госць", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Слупок злева", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Слупок справа", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Радок вышэй", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Радок ніжэй", "SSE.Controllers.DocumentHolder.insertText": "Уставіць", "SSE.Controllers.DocumentHolder.leftText": "Па леваму краю", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Увага", "SSE.Controllers.DocumentHolder.rightText": "Па праваму краю", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Шырыня слупка {0} сімвалаў ({1} пікселяў)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Вышыня радка {0} кропак ({1} пікселяў)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Пстрыкніце па спасылцы, каб яе адкрыць, альбо пстрыкніце і ўтрымлівайце, каб вылучыць ячэйку.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Уставіць злева", "SSE.Controllers.DocumentHolder.textInsertTop": "Уставіць зверху", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Адмысловая ўстаўка", "SSE.Controllers.DocumentHolder.textSym": "сімв", "SSE.Controllers.DocumentHolder.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Вышэй за сярэдняе", "SSE.Controllers.DocumentHolder.txtAddBottom": "Дадаць ніжнюю мяжу", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Дадаць рыску дробу", "SSE.Controllers.DocumentHolder.txtAddHor": "Дада<PERSON>ь гарызантальную лінію", "SSE.Controllers.DocumentHolder.txtAddLB": "Дада<PERSON>ь лінію з левага ніжняга кута", "SSE.Controllers.DocumentHolder.txtAddLeft": "Дада<PERSON>ь левую мяжу", "SSE.Controllers.DocumentHolder.txtAddLT": "Дада<PERSON>ь лінію з левага верхняга кута", "SSE.Controllers.DocumentHolder.txtAddRight": "Дадаць правую мяжу", "SSE.Controllers.DocumentHolder.txtAddTop": "Дадаць верхнюю мяжу", "SSE.Controllers.DocumentHolder.txtAddVer": "Дадаць вертыкальную лінію", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Выраўноўванне па сімвале", "SSE.Controllers.DocumentHolder.txtAll": "(Усе)", "SSE.Controllers.DocumentHolder.txtAnd": "і", "SSE.Controllers.DocumentHolder.txtBegins": "Пачынаецца з", "SSE.Controllers.DocumentHolder.txtBelowAve": "Ніжэй за сярэдняе", "SSE.Controllers.DocumentHolder.txtBlanks": "(Пустыя)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Уласцівасці межаў", "SSE.Controllers.DocumentHolder.txtBottom": "Па ніжняму краю", "SSE.Controllers.DocumentHolder.txtColumn": "Слупок", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Выраўноўванне слупка", "SSE.Controllers.DocumentHolder.txtContains": "Змяшчае", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Паменшыць памер аргумента", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Выдаліць аргумент", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Выдаліць уласнаручны разрыў", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Выдаліць укладзеныя сімвалы", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Выдаліць укладзеныя сімвалы і падзяляльнікі", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Выдаліць раўнанне", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Выдаліць сімвал", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Выдаліць радыкал", "SSE.Controllers.DocumentHolder.txtEnds": "Заканчваецца на", "SSE.Controllers.DocumentHolder.txtEquals": "Роўна", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Адпавядае колеру ячэйкі", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Адпавядае колеру шрыфту", "SSE.Controllers.DocumentHolder.txtExpand": "Пашырыць і ўпарадкаваць", "SSE.Controllers.DocumentHolder.txtExpandSort": "Даныя побач з абраным дыяпазонам не будуць сартавацца. Хочаце пашырыць абраны дыяпазон, каб уключыць даныя з супольных ячэек, альбо працягнуць сартаванне толькі абранага дыяпазону?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Знізу", "SSE.Controllers.DocumentHolder.txtFilterTop": "Найбольшыя", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Змяніць на лінейны дроб", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Змяніць на дыяганальны дроб", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Змяніць на вертыкальны дроб", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Больш альбо роўна", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Сімвал па-над тэкстам", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Сімвал пад тэкстам", "SSE.Controllers.DocumentHolder.txtHeight": "Вышыня", "SSE.Controllers.DocumentHolder.txtHideBottom": "Схаваць ніжнюю мяжу", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Схаваць ніжні ліміт", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Схаваць закрывальную дужку", "SSE.Controllers.DocumentHolder.txtHideDegree": "Схаваць ступень", "SSE.Controllers.DocumentHolder.txtHideHor": "Схаваць гарызантальную лінію", "SSE.Controllers.DocumentHolder.txtHideLB": "Схаваць лінію з левага ніжняга кута ", "SSE.Controllers.DocumentHolder.txtHideLeft": "Схаваць левую мяжу", "SSE.Controllers.DocumentHolder.txtHideLT": "Схаваць лінію з верхняга левага кут", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Схаваць адкрывальную дужку", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Схаваць палі для запаўнення", "SSE.Controllers.DocumentHolder.txtHideRight": "Схаваць правую мяжу", "SSE.Controllers.DocumentHolder.txtHideTop": "Схаваць верхнюю мяжу", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Схаваць верхні ліміт", "SSE.Controllers.DocumentHolder.txtHideVer": "Схаваць вертыкальную лінію", "SSE.Controllers.DocumentHolder.txtImportWizard": "Памагаты імпарту тэксту", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Павялічыць памер аргумента", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Уставіць аргумент пасля", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Уставіць аргумент перад", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Уставіць уласнаручны разрыў", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Уставіць раўнанне пасля", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Уставіць раўнанне перад", "SSE.Controllers.DocumentHolder.txtItems": "элементаў", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Пакінуць толькі тэкст", "SSE.Controllers.DocumentHolder.txtLess": "Ме<PERSON><PERSON> за", "SSE.Controllers.DocumentHolder.txtLessEquals": "Мен<PERSON> альбо роўна", "SSE.Controllers.DocumentHolder.txtLimitChange": "Змяніць размяшчэнне лімітаў", "SSE.Controllers.DocumentHolder.txtLimitOver": "Ліміт па-над тэкстам", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Ліміт пад тэкстам", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Змяніць памер дужак адпаведна вышыні аргумента", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Выраўноўванне матрыц", "SSE.Controllers.DocumentHolder.txtNoChoices": "Няма варыянтаў для запаўнення ячэйкі.<br>Для замены можна абраць толькі тэкставыя значэнні са слупка.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Не пачынаецца з", "SSE.Controllers.DocumentHolder.txtNotContains": "Не змяшчае", "SSE.Controllers.DocumentHolder.txtNotEnds": "Не заканчваецца на", "SSE.Controllers.DocumentHolder.txtNotEquals": "Не роўна", "SSE.Controllers.DocumentHolder.txtOr": "альбо", "SSE.Controllers.DocumentHolder.txtOverbar": "Лінія па-над тэкстам", "SSE.Controllers.DocumentHolder.txtPaste": "Уставіць", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Формула без межаў", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Формула + шырыня слупка", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Фарматаванне канцавых ячэек", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Уставіць толькі фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Формула + фармат лічбаў", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Уставіць толькі формулу", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Формула + усё фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteLink": "Уставіць спасылку", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Звязаны малюнак", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Аб’яднаць умоўнае фарматаванне", "SSE.Controllers.DocumentHolder.txtPastePicture": "Малюнак", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Зыходнае фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Пераправіць", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Значэнне + усё фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Значэнне + фармат лікаў", "SSE.Controllers.DocumentHolder.txtPasteValues": "Уставіць толькі значэнне", "SSE.Controllers.DocumentHolder.txtPercent": "адсоткаў", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Паўтарыць аўтаразгортванне табліцы", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Выдаліць рыску дробу", "SSE.Controllers.DocumentHolder.txtRemLimit": "Выдаліць ліміт", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Выдаліць дыякрытычны знак", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Выдаліць лінію", "SSE.Controllers.DocumentHolder.txtRemScripts": "Выдаліць індэксы", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Выдаліць ніжні індэкс", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Выдаліць верхні індэкс", "SSE.Controllers.DocumentHolder.txtRowHeight": "Вышыня радка", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Індэксы пасля тэксту", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Індэксы перад тэкстам", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Паказаць ніжні ліміт", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Паказаць закрывальную дужку", "SSE.Controllers.DocumentHolder.txtShowDegree": "Паказаць ступень", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Паказаць адкрывальную дужку", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Паказаць поле для запаўнення", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Паказаць верхні ліміт", "SSE.Controllers.DocumentHolder.txtSorting": "Парадкаванне", "SSE.Controllers.DocumentHolder.txtSortSelected": "Парадкаваць абранае", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Расцягнуць дужкі", "SSE.Controllers.DocumentHolder.txtTop": "Па верхняму краю", "SSE.Controllers.DocumentHolder.txtUnderbar": "Лінія пад тэкстам", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Скасаваць аўтаразгортванне табліцы", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Выкарыстоўваць майстра імпарту тэксту", "SSE.Controllers.DocumentHolder.txtWidth": "Шырыня", "SSE.Controllers.FormulaDialog.sCategoryAll": "Усе", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Базы даных", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Дата і час", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Інжынерныя", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Фінансавыя", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Інфармацыйныя", "SSE.Controllers.FormulaDialog.sCategoryLast10": "Апошнія 10 выкарыстаных", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Лагічныя", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Агляд і спасылкі", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Матэматычныя", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Статыстычныя", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Тэкст і даныя", "SSE.Controllers.LeftMenu.newDocumentTitle": "Табліца без назвы", "SSE.Controllers.LeftMenu.textByColumns": "Па слупках", "SSE.Controllers.LeftMenu.textByRows": "Па радках", "SSE.Controllers.LeftMenu.textFormulas": "Формулы", "SSE.Controllers.LeftMenu.textItemEntireCell": "Усё змесціва ячэек", "SSE.Controllers.LeftMenu.textLoadHistory": "Загрузка гісторыі версій…", "SSE.Controllers.LeftMenu.textLookin": "Шукаць у", "SSE.Controllers.LeftMenu.textNoTextFound": "Шукаемых даных не знойдзена. Калі ласка, змяніце параметры пошуку.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Заменена. Мінута ўваходжанняў - {0}.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Пошук завершаны. Заменена ўваходжанняў: {0}", "SSE.Controllers.LeftMenu.textSearch": "По<PERSON><PERSON>к", "SSE.Controllers.LeftMenu.textSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Значэнні", "SSE.Controllers.LeftMenu.textWarning": "Увага", "SSE.Controllers.LeftMenu.textWithin": "Шукаць", "SSE.Controllers.LeftMenu.textWorkbook": "Кніга", "SSE.Controllers.LeftMenu.txtUntitled": "Без назвы", "SSE.Controllers.LeftMenu.warnDownloadAs": "Калі вы працягнеце захаванне ў гэты фармат, усе функцыі, апроч тэксту страцяцца.<br>Сапраўды хочаце працягнуць?", "SSE.Controllers.Main.confirmMoveCellRange": "Канцавы дыяпазон ячэек можа змяшчаць даныя. Працягнуць аперацыю?", "SSE.Controllers.Main.confirmPutMergeRange": "Зыходныя даныя змяшчалі аб’яднаныя ячэйкі .<br>Перад устаўкай ячэек у табліцу аб’яднанне было скасаванае.", "SSE.Controllers.Main.convertationTimeoutText": "Час чакання пераўтварэння сышоў.", "SSE.Controllers.Main.criticalErrorExtText": "Націсніце \"Добра\", каб вярнуцца да спіса дакументаў.", "SSE.Controllers.Main.criticalErrorTitle": "Памылка", "SSE.Controllers.Main.downloadErrorText": "Не атрымалася спампаваць.", "SSE.Controllers.Main.downloadTextText": "Спампоўванне табліцы…", "SSE.Controllers.Main.downloadTitleText": "Спампоўванне табліцы", "SSE.Controllers.Main.errNoDuplicates": "Паўторных значэнняў не знойдзена.", "SSE.Controllers.Main.errorAccessDeny": "Вы спрабуеце выканаць дзеянне, на якое не маеце правоў.<br>Калі ласка, звярніцеся да адміністратара сервера дакументаў.", "SSE.Controllers.Main.errorArgsRange": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстаны хібны дыяпазон аргументаў.", "SSE.Controllers.Main.errorAutoFilterChange": "Аперацыя не дазволеная, бо адбываецца спроба ссунуць ячэйкі табліцы на аркушы.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Гэтую аперацыю немагчыма выканаць для абраных ячэек, бо нельга перамясціць частку табліцы.<br>Абярыце іншы дыяпазон даных, каб перамяшчалася ўся табліца, і паўтарыце зноў.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Гэтую аперацыю немагчыма выканаць для абранага дыяпазону ячэек.<br>Абярыце іншы дыяпазон даных, адрозны ад існага, і паўтарыце зноў.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Аперацыя не можа быць выкананая, бо ў вобласці змяшчаюцца адфільтраваныя ячэйкі.<br>Выведзіце на экран схаваныя элементы і паспрабуйце зноў.", "SSE.Controllers.Main.errorBadImageUrl": "Хібны URL-адрас выявы", "SSE.Controllers.Main.errorCannotUngroup": "Немагчыма разгрупаваць. Каб стварыць структуру дакумента, абярыце слупкі альбо радкі і згрупуйце іх.", "SSE.Controllers.Main.errorChangeArray": "Немагчыма змяніць частку масіву.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Злучэнне з серверам страчана. На дадзены момант рэдагаваць дакумент немагчыма.", "SSE.Controllers.Main.errorConnectToServer": "Не атрымалася захаваць дакумент. Праверце налады злучэння альбо звярніцеся да вашага адміністратара.<br>Калі вы націснеце кнопку \"Добра\", вам прапануецца спампаваць дакумент.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Гэты загад немагчыма ўжыць да незвязаных дыяпазонаў.<br>Абярыце адзін дыяпазон і паўтарыце спробу.", "SSE.Controllers.Main.errorCountArg": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстана хібная колькасць аргументаў.", "SSE.Controllers.Main.errorCountArgExceed": "Ва ўведзенай формуле ёсць памылка.<br>Перасягнута колькасць аргументаў.", "SSE.Controllers.Main.errorCreateDefName": "На дадзены момант немагчыма рэдагаваць існыя названыя дыяпазоны і ствараць новыя,<br>бо некаторыя з іх рэдагуюцца.", "SSE.Controllers.Main.errorDatabaseConnection": "Вонкавая памылка.<br>Памылка злучэння з базай даных. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorDataEncrypted": "Атрыманы зашыфраваныя змены, іх немагчыма расшыфраваць.", "SSE.Controllers.Main.errorDataRange": "Хібны дыяпазон даных.", "SSE.Controllers.Main.errorDataValidate": "Уведзена непрыдатнае значэнне.<br>З<PERSON><PERSON><PERSON>нні, якія можна ўвесці ў гэтую ячэйку, абмежаваныя.", "SSE.Controllers.Main.errorDefaultMessage": "Код памылкі: %1", "SSE.Controllers.Main.errorEditingDownloadas": "Падчас працы з дакументам адбылася памылка.<br>Выкарыстайце параметр \"Спампаваць як\", каб захаваць рэзервовую копію файла на цвёрды дыск камп’ютара.", "SSE.Controllers.Main.errorEditingSaveas": "Падчас працы з дакументам адбылася памылка.<br>Выкарыстайце параметр \"Захаваць як…\", каб захаваць рэзервовую копію файла на цвёрды дыск камп’ютара.", "SSE.Controllers.Main.errorEditView": "Немагчыма рэдагаваць існыя мініяцюры аркушаў і ствараць новыя, бо некаторыя з іх ужо рэдагуюцца.", "SSE.Controllers.Main.errorEmailClient": "Не знойдзена паштовага кліента.", "SSE.Controllers.Main.errorFilePassProtect": "Файл абаронены паролем, яго немагчыма адкрыць.", "SSE.Controllers.Main.errorFileRequest": "Вонкавая памылка.<br>Памылка запыту файла. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorFileSizeExceed": "Памер файла перавышае ліміт, вызначаны для вашага сервера.<br>Звярніцеся да адміністратара сервера дакументаў за дадатковымі звесткамі.", "SSE.Controllers.Main.errorFileVKey": "Вонкавая памылка.<br><PERSON><PERSON><PERSON><PERSON><PERSON> ключ бяспекі. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorFillRange": "Немагчыма запоўніць абраны дыяпазон ячэек.<br>Усе аб’яднаныя ячэйкі мусяць быць аднаго памеру.", "SSE.Controllers.Main.errorForceSave": "Падчас захавання файла адбылася памылка. Калі ласка, выкарыстайце параметр \"Спампаваць як\", каб захаваць файл на цвёрдым дыску камп’ютара, альбо паспрабуйце яшчэ раз пазней.", "SSE.Controllers.Main.errorFormulaName": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстана хібная назва формулы.", "SSE.Controllers.Main.errorFormulaParsing": "Падчас разбору формулы адбылася ўнутраная памылка.", "SSE.Controllers.Main.errorFrmlMaxLength": "Даўжыня формулы перавышае абмежаванне ў 8192 сімвалы.<br>Адрэдагуйце яе і паўтарыце спробу.", "SSE.Controllers.Main.errorFrmlMaxReference": "Немагчыма увесці гэтую формулу, бо яна змяшчае занадта шмат значэнняў,<br>спасылак на ячэйкі альбо назваў.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Даўжыня тэкставых значэнняў у формулах не можа перавышаць 255 сімвалаў.<br>Выкарыстайце функцыю \"ЗЛУЧЫЦЬ\" альбо аператар злучэння (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Функцыя спасылаецца на аркуш, які не існуе.<br>Калі ласка, праверце даныя і паўтарыце спробу.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Не атрымалася выканаць аперацыю для абранага дыяпазону ячэек.<br>Вылучыце дыяпазон так, каб першы радок знаходзіўся на тым жа радку,<br>а выніковая табліца перакрывала бягучую.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Не атрымалася выканаць аперацыю для абранага дыяпазону ячэек.<br> Абярыце дыяпазон, які не змяшчае іншых табліц.", "SSE.Controllers.Main.errorInvalidRef": "Увядзіце прыдатную назву для дыяпазону альбо прыдатную спасылку для пераходу.", "SSE.Controllers.Main.errorKeyEncrypt": "Невядомы дэскрыптар ключа", "SSE.Controllers.Main.errorKeyExpire": "Тэрмін дзеяння ключа дэскрыптара сышоў", "SSE.Controllers.Main.errorLabledColumnsPivot": "Каб стварыць зводную табліцу, выкарыстайце даныя, пададзеныя спісам з загалоўкамі слупкоў.", "SSE.Controllers.Main.errorLockedAll": "Аперацыя не можа быць выкананая, бо аркуш заблакаваны іншым карыстальнікам.", "SSE.Controllers.Main.errorLockedCellPivot": "Нельга змяніць даныя ў зводнай табліцы.", "SSE.Controllers.Main.errorLockedWorksheetRename": "На дадзены момант немагчыма змяніць назву аркуша, бо яе змяняе іншы  карыстальнік", "SSE.Controllers.Main.errorMaxPoints": "Максімальная колькасць кропак у шэрагу для дыяграмы - 4096.", "SSE.Controllers.Main.errorMoveRange": "Немагчыма змяніць частку аб’яднанай ячэйкі", "SSE.Controllers.Main.errorMoveSlicerError": "Зводкі табліц немагчыма капіяваць з адной працоўнай кнігі ў іншую.<br>Паспрабуйце яшчэ раз, вылучыўшы ўсю табліцу і зводкі.", "SSE.Controllers.Main.errorMultiCellFormula": "У табліцах забаронена выкарыстанне формул масіву з некалькімі ячэйкамі.", "SSE.Controllers.Main.errorNoDataToParse": "Даных для разбору не вылучана.", "SSE.Controllers.Main.errorOpenWarning": "Даўжыня адной з формул у файле перасягнула 8192 сімвалы.<br>Формула была выдаленая.", "SSE.Controllers.Main.errorOperandExpected": "Сінтаксіс уведзенай функцыі хібны. Праверце, ці не мінутая адна з дужак.", "SSE.Controllers.Main.errorPasteMaxRange": "Вобласці капіявання і ўстаўкі адрозніваюцца.<br>Каб уставіць скапіяваныя ячэйкі вылучыце вобласць таго ж памеру альбо пстрыкніце па першай ячэйцы ў радку.", "SSE.Controllers.Main.errorPasteSlicerError": "Зводкі табліц немагчыма капіяваць з адной працоўнай кнігі ў іншую.", "SSE.Controllers.Main.errorPivotOverlap": "Забараняецца перакрыцце справаздачы зводнай табліцы і табліцы.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "На жаль, у бягучай версіі праграмы немагчыма надрукаваць больш за 1500 старонак запар.<br>Гэтае абмежаванне будзе прыбрана ў наступных версіях.", "SSE.Controllers.Main.errorProcessSaveResult": "Не атрымалася захаваць", "SSE.Controllers.Main.errorServerVersion": "Рэдактар быў абноўлены. Старонка перазагрузіцца, каб ужыць змены.", "SSE.Controllers.Main.errorSessionAbsolute": "Час сеанса рэдагавання дакумента сышоў. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.errorSessionIdle": "Дакумент працяглы час не рэдагаваўся. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.errorSessionToken": "Злучэнне з серверам перарванае. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Controllers.Main.errorToken": "Токен бяспекі дакумента мае хібны фармат.<br>Калі ласка, звярніцеся да адміністатара сервера дакументаў.", "SSE.Controllers.Main.errorTokenExpire": "Тэрмін дзеяння токена бяспекі дакумента сышоў.<br>Калі ласка, звярніцеся да адміністратара сервера дакументаў.", "SSE.Controllers.Main.errorUnexpectedGuid": "Вонкавая памылка.<br>Нечаканы ідэнтыфікатар GUID. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorUpdateVersion": "Версія файла была змененая. Старонка будзе перазагружаная.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Злучэнне з інтэрнэтам было адноўлена, і версія файла змянілася.<br>Перш чым працягнуць працу, неабходна спампаваць файл альбо скапіяваць яго змесціва, каб захаваць даныя, а пасля перазагрузіць старонку.", "SSE.Controllers.Main.errorUserDrop": "На дадзены момант файл недаступны.", "SSE.Controllers.Main.errorUsersExceed": "Перасягнута колькасць карыстальнікаў, дазволеных згодна тарыфу", "SSE.Controllers.Main.errorViewerDisconnect": "Злучэнне страчана. Вы зможаце праглядаць дакумент,<br>але не зможаце спампаваць альбо надрукаваць яго да аднаўлення злучэння і перазагрузкі старонкі.", "SSE.Controllers.Main.errorWrongBracketsCount": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстана хібная колькасць дужак.", "SSE.Controllers.Main.errorWrongOperator": "Ва ўведзенай формуле ёсць памылка. Выкарыстаны хібны аператар.<br>Калі ласка, выпраўце памылку.", "SSE.Controllers.Main.errRemDuplicates": "Знойдзена і выдалена паўторных значэнняў: {0}, засталося непаўторных значэнняў: {1}.", "SSE.Controllers.Main.leavePageText": "У электроннай табліцы ёсць незахаваныя змены. Каб захаваць іх, націсніце \"Застацца на гэтай старонцы\", пасля \"Захаваць\". Націсніце \"Пакінуць старонку\", каб скінуць усе незахаваныя змены.", "SSE.Controllers.Main.loadFontsTextText": "Загрузка даных…", "SSE.Controllers.Main.loadFontsTitleText": "Загрузка даных", "SSE.Controllers.Main.loadFontTextText": "Загрузка даных…", "SSE.Controllers.Main.loadFontTitleText": "Загрузка даных", "SSE.Controllers.Main.loadImagesTextText": "Загрузка выяў…", "SSE.Controllers.Main.loadImagesTitleText": "Загрузка выяў", "SSE.Controllers.Main.loadImageTextText": "Загрузка выявы…", "SSE.Controllers.Main.loadImageTitleText": "Загрузка выявы", "SSE.Controllers.Main.loadingDocumentTitleText": "Загрузка табліцы", "SSE.Controllers.Main.notcriticalErrorTitle": "Увага", "SSE.Controllers.Main.openErrorText": "Падчас адкрыцця файла адбылася памылка.", "SSE.Controllers.Main.openTextText": "Адкрыццё табліцы…", "SSE.Controllers.Main.openTitleText": "Адкрыццё табліцы", "SSE.Controllers.Main.pastInMergeAreaError": "Немагчыма змяніць частку аб’яднанай ячэйкі", "SSE.Controllers.Main.printTextText": "Друкаванне табліцы…", "SSE.Controllers.Main.printTitleText": "Друкаванне табліцы", "SSE.Controllers.Main.reloadButtonText": "Абнав<PERSON>ць старонку", "SSE.Controllers.Main.requestEditFailedMessageText": "Дакумент зараз рэдагуецца. Калі ласка, паспрабуйце пазней.", "SSE.Controllers.Main.requestEditFailedTitleText": "Доступ забаронены", "SSE.Controllers.Main.saveErrorText": "Падчас захавання файла адбылася памылка.", "SSE.Controllers.Main.saveTextText": "Захаванне табліцы…", "SSE.Controllers.Main.saveTitleText": "Захаванне табліцы", "SSE.Controllers.Main.scriptLoadError": "Занадта павольнае злучэнне, не ўсе кампаненты атрымалася загрузіць. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.textAnonymous": "Ананімны карыстальнік", "SSE.Controllers.Main.textApplyAll": "Ужыць да ўсіх раўнанняў", "SSE.Controllers.Main.textBuyNow": "Наведаць сайт", "SSE.Controllers.Main.textChangesSaved": "Усе змены захаваныя", "SSE.Controllers.Main.textClose": "Закрыць", "SSE.Controllers.Main.textCloseTip": "Пстрыкніце, каб закрыць гэтую падказку", "SSE.Controllers.Main.textConfirm": "Пацвярджэнне", "SSE.Controllers.Main.textContactUs": "Звязацца з аддзелам продажу", "SSE.Controllers.Main.textCustomLoader": "Звярніце ўвагу, што па ўмовах ліцэнзіі вы не можаце змяняць экран загрузкі.<br>Калі ласка, звярніцеся ў аддзел продажу.", "SSE.Controllers.Main.textDisconnect": "Злучэнне страчана", "SSE.Controllers.Main.textGuest": "Госць", "SSE.Controllers.Main.textHasMacros": "Файл змяшчае макрасы з аўтазапускам.<br>Хочаце запусціць макрасы?", "SSE.Controllers.Main.textLearnMore": "Падрабязней", "SSE.Controllers.Main.textLoadingDocument": "Загрузка табліцы", "SSE.Controllers.Main.textNo": "Не", "SSE.Controllers.Main.textNoLicenseTitle": "Ліцэнзійнае абмежаванне", "SSE.Controllers.Main.textPaidFeature": "Платная функцыя", "SSE.Controllers.Main.textPleaseWait": "Аперацыя можа заняць больш часу. Калі ласка, пачакайце… ", "SSE.Controllers.Main.textRemember": "Запомніць мой выбар для ўсіх файлаў", "SSE.Controllers.Main.textRequestMacros": "Макрас робіць запыт да URL. Хочаце дазволіць запыт да %1?", "SSE.Controllers.Main.textShape": "Фігура", "SSE.Controllers.Main.textStrict": "Строгі рэжым", "SSE.Controllers.Main.textText": "Тэкст", "SSE.Controllers.Main.textTryUndoRedo": "Функцыі скасавання і паўтору дзеянняў выключаныя ў хуткім рэжыме сумеснага рэдагавання.<br>Націсніце кнопку \"Строгі рэжым\", каб пераключыцца ў строгі рэжым сумеснага рэдагавання, каб рэдагаваць файл без іншых карыстальнікаў і адпраўляць змены толькі пасля іх захавання. Пераключацца паміж рэжымамі сумеснага рэдагавання можна з дапамогай дадатковых налад рэдактара.", "SSE.Controllers.Main.textYes": "Так", "SSE.Controllers.Main.titleLicenseExp": "Тэрмін дзеяння ліцэнзіі сышоў", "SSE.Controllers.Main.titleServerVersion": "Рэдактар абноўлены", "SSE.Controllers.Main.txtAccent": "Акцэнт", "SSE.Controllers.Main.txtAll": "(Усе)", "SSE.Controllers.Main.txtArt": "Увядзіце ваш тэкст", "SSE.Controllers.Main.txtBasicShapes": "Асноўныя фігуры", "SSE.Controllers.Main.txtBlank": "(пуста)", "SSE.Controllers.Main.txtButtons": "Кнопкі", "SSE.Controllers.Main.txtByField": "%1 з %2", "SSE.Controllers.Main.txtCallouts": "Удакладненні", "SSE.Controllers.Main.txtCharts": "Схемы", "SSE.Controllers.Main.txtClearFilter": "Ачысціць фільтр", "SSE.Controllers.Main.txtColLbls": "Назвы слупкоў", "SSE.Controllers.Main.txtColumn": "Слупок", "SSE.Controllers.Main.txtConfidential": "Канфідэнцыйна", "SSE.Controllers.Main.txtDate": "Дата", "SSE.Controllers.Main.txtDiagramTitle": "Загаловак дыяграмы", "SSE.Controllers.Main.txtEditingMode": "Актывацыя рэжыму рэдагавання…", "SSE.Controllers.Main.txtErrorLoadHistory": "Не атрымалася загрузіць гісторыю", "SSE.Controllers.Main.txtFiguredArrows": "Фігурныя стрэлкі", "SSE.Controllers.Main.txtFile": "<PERSON>а<PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Агульны вынік", "SSE.Controllers.Main.txtGroup": "Згрупаваць", "SSE.Controllers.Main.txtLines": "Лініі", "SSE.Controllers.Main.txtMath": "Матэматычныя знакі", "SSE.Controllers.Main.txtMonths": "Месяцы", "SSE.Controllers.Main.txtMultiSelect": "Масавы выбар", "SSE.Controllers.Main.txtOr": "%1 або %2", "SSE.Controllers.Main.txtPage": "Старонка", "SSE.Controllers.Main.txtPageOf": "Старонка %1 з %2", "SSE.Controllers.Main.txtPages": "Старонкі", "SSE.Controllers.Main.txtPreparedBy": "Падрыхтаваў", "SSE.Controllers.Main.txtPrintArea": "Вобласць_друкавання", "SSE.Controllers.Main.txtRectangles": "Прамавугольнікі", "SSE.Controllers.Main.txtRow": "Радок", "SSE.Controllers.Main.txtRowLbls": "Адмеціны радкоў", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Зноска 1 (мяжа і лінія)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Зноска 2 (мяжа і лінія)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Зноска 3 (мяжа і лінія)", "SSE.Controllers.Main.txtShape_accentCallout1": "Зноска 2 (лінія)", "SSE.Controllers.Main.txtShape_accentCallout2": "Зноска 2 (лінія)", "SSE.Controllers.Main.txtShape_accentCallout3": "Зноска 3 (лінія)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Кнопка \"Назад\"", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Кнопка \"У пачатак\"", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Пустая кнопка", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Кнопка дакумента", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Кнопка \"У канец\"", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Кнопка \"Уперад\"", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Кнопка \"Даведка\"", "SSE.Controllers.Main.txtShape_actionButtonHome": "Кнопка \"На хатнюю\"", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Кнопка \"Інфармацыя\"", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Кнопка відэа", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Кнопка вяртання", "SSE.Controllers.Main.txtShape_actionButtonSound": "Кнопка гуку", "SSE.Controllers.Main.txtShape_arc": "Дуга", "SSE.Controllers.Main.txtShape_bentArrow": "Загнутая стрэлка", "SSE.Controllers.Main.txtShape_bentConnector5": "Злучальнік водступам", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Водступ са стрэлкай", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Водступ з дзвюма стрэлкамі", "SSE.Controllers.Main.txtShape_bentUpArrow": "Загнутая стрэлка ўверх", "SSE.Controllers.Main.txtShape_bevel": "Нахілены", "SSE.Controllers.Main.txtShape_blockArc": "Арка", "SSE.Controllers.Main.txtShape_borderCallout1": "Зноска 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Зноска 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Зноска 3", "SSE.Controllers.Main.txtShape_bracePair": "Падвойныя фігурныя дужкі", "SSE.Controllers.Main.txtShape_callout1": "Зноска 1 (без мяжы)", "SSE.Controllers.Main.txtShape_callout2": "Зноска 2 (без мяжы)", "SSE.Controllers.Main.txtShape_callout3": "Зноска 3 (без мяжы)", "SSE.Controllers.Main.txtShape_can": "Цыліндр", "SSE.Controllers.Main.txtShape_chevron": "Сімвал", "SSE.Controllers.Main.txtShape_chord": "Хорда", "SSE.Controllers.Main.txtShape_circularArrow": "Кругавая стрэлка", "SSE.Controllers.Main.txtShape_cloud": "Воблака", "SSE.Controllers.Main.txtShape_cloudCallout": "Зноска-воблака", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>т", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Крывая злучальная лінія", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Крывая злучальная стрэлка", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Крывая лінія з дзвюма стрэлкамі", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Загнутая ўверх стрэлка", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Загнутая ўправа стрэлка", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Загнутая ўлева стрэлка", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Загнутая ўніз стрэлка", "SSE.Controllers.Main.txtShape_decagon": "Дзесяцівугольнік", "SSE.Controllers.Main.txtShape_diagStripe": "Дыяганальная рыска", "SSE.Controllers.Main.txtShape_diamond": "Ромб", "SSE.Controllers.Main.txtShape_dodecagon": "Дванаццацівугольнік", "SSE.Controllers.Main.txtShape_donut": "Кола", "SSE.Controllers.Main.txtShape_doubleWave": "Падвойная хваля", "SSE.Controllers.Main.txtShape_downArrow": "Стрэлка ўніз", "SSE.Controllers.Main.txtShape_downArrowCallout": "Зноска са стрэлкай уніз", "SSE.Controllers.Main.txtShape_ellipse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Загнутая ўніз стужка", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Закручаная ўверх стужка", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: альтэрнатыўны працэс", "SSE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: супастаўленне", "SSE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: злучальн<PERSON>к", "SSE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: рашэнне", "SSE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: затрымка", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: дысплэй", "SSE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: дакумент", "SSE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: выманне", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: даныя", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: унутраная памяць", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: магнітны дыск", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: памяць з непасрэдным доступам", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: сховішча з паслядоўным доступам", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: уласнаручны ўвод", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: уласнаручнае кіраванне", "SSE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: аб’яднанне", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: множны дакумент", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: спасылка на іншую старонку", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: захаваныя даныя", "SSE.Controllers.Main.txtShape_flowChartOr": "Блок-схема:альбо", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: прадвызначаны працэс", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: падрыхтоўка", "SSE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: працэс", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: картка", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: перфараваная стужка", "SSE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: сартаванне", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: вузел сумы", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: знак завяршэння", "SSE.Controllers.Main.txtShape_foldedCorner": "Сагнуты кут", "SSE.Controllers.Main.txtShape_frame": "Рамка", "SSE.Controllers.Main.txtShape_halfFrame": "Палова рамкі", "SSE.Controllers.Main.txtShape_heart": "Сэрца", "SSE.Controllers.Main.txtShape_heptagon": "Сямівугольнік", "SSE.Controllers.Main.txtShape_hexagon": "Шасцівугольнік", "SSE.Controllers.Main.txtShape_homePlate": "Пяцівугольнік", "SSE.Controllers.Main.txtShape_horizontalScroll": "Гарызантальны скрутак", "SSE.Controllers.Main.txtShape_irregularSeal1": "Выбліск 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Выбліск 2", "SSE.Controllers.Main.txtShape_leftArrow": "Стрэлка ўлева", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Зноска са стрэлкай улева", "SSE.Controllers.Main.txtShape_leftBrace": "Левая фігурная дужка", "SSE.Controllers.Main.txtShape_leftBracket": "Левая дужка", "SSE.Controllers.Main.txtShape_leftRightArrow": "Стрэлкі ўлева-ўправа", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Зноска са стрэлкамі ўлева-ўправа", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Стрэлкі ўлева-ўправа-ўверх", "SSE.Controllers.Main.txtShape_leftUpArrow": "Стрэлкі ўлева-ўверх", "SSE.Controllers.Main.txtShape_lightningBolt": "Маланка", "SSE.Controllers.Main.txtShape_line": "Лінія", "SSE.Controllers.Main.txtShape_lineWithArrow": "Стрэлка", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Падвойная стрэлка", "SSE.Controllers.Main.txtShape_mathDivide": "Дзяленне", "SSE.Controllers.Main.txtShape_mathEqual": "Роўна", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Множанне", "SSE.Controllers.Main.txtShape_mathNotEqual": "Не роўна", "SSE.Controllers.Main.txtShape_mathPlus": "Плюс", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Забаронена", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Стрэлка ўправа з выразам", "SSE.Controllers.Main.txtShape_octagon": "Васьмівугольнік", "SSE.Controllers.Main.txtShape_parallelogram": "Парал<PERSON><PERSON><PERSON><PERSON><PERSON>ам", "SSE.Controllers.Main.txtShape_pentagon": "Пяцівугольнік", "SSE.Controllers.Main.txtShape_pie": "Па крузе", "SSE.Controllers.Main.txtShape_plaque": "Под<PERSON><PERSON>с", "SSE.Controllers.Main.txtShape_plus": "Плюс", "SSE.Controllers.Main.txtShape_polyline1": "Крывая", "SSE.Controllers.Main.txtShape_polyline2": "Адвольная форма", "SSE.Controllers.Main.txtShape_quadArrow": "Чацвярная стрэлка", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Зноска з чацвярной стрэлкай", "SSE.Controllers.Main.txtShape_rect": "Прамавугольнік", "SSE.Controllers.Main.txtShape_ribbon": "Стужка ўніз", "SSE.Controllers.Main.txtShape_ribbon2": "Стужка ўверх", "SSE.Controllers.Main.txtShape_rightArrow": "Стрэлка ўправа", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Зноска са стрэлкай управа", "SSE.Controllers.Main.txtShape_rightBrace": "Правая фігурная дужка", "SSE.Controllers.Main.txtShape_rightBracket": "Правая дужка", "SSE.Controllers.Main.txtShape_round1Rect": "Прамавугольнік з адным скругленым вуглом", "SSE.Controllers.Main.txtShape_round2DiagRect": "Прамавугольнік з двума скругленымі супрацьлеглымі вугламі", "SSE.Controllers.Main.txtShape_round2SameRect": "Прамавугольнік з двума скругленымі суседнімі вугламі", "SSE.Controllers.Main.txtShape_roundRect": "Прамавугольнік са скругленымі вугламі", "SSE.Controllers.Main.txtShape_rtTriangle": "Прамавугольны трохвугольнік", "SSE.Controllers.Main.txtShape_smileyFace": "Твар з усмешкай", "SSE.Controllers.Main.txtShape_snip1Rect": "Прамавугольнік з адным абрэзаным вуглом", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Прамавугольнік з двума абрэзанымі супрацьлеглымі вугламі", "SSE.Controllers.Main.txtShape_snip2SameRect": "Прамавугольнік з двума абрэзанымі суседнімі вугламі", "SSE.Controllers.Main.txtShape_snipRoundRect": "Прамавугольнік з абрэзаным скругленым вуглом", "SSE.Controllers.Main.txtShape_spline": "Крывая", "SSE.Controllers.Main.txtShape_star10": "10-канцовая зорка", "SSE.Controllers.Main.txtShape_star12": "12-канцовая зорка", "SSE.Controllers.Main.txtShape_star16": "16-канцовая зорка", "SSE.Controllers.Main.txtShape_star24": "24-канцовая зорка", "SSE.Controllers.Main.txtShape_star32": "32-канцовая зорка", "SSE.Controllers.Main.txtShape_star4": "4-канцовая зорка", "SSE.Controllers.Main.txtShape_star5": "5-канцовая зорка", "SSE.Controllers.Main.txtShape_star6": "6-канцовая зорка", "SSE.Controllers.Main.txtShape_star7": "7-канцовая зорка", "SSE.Controllers.Main.txtShape_star8": "8-канцовая зорка", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Рыскавая стрэлка ўправа", "SSE.Controllers.Main.txtShape_sun": "Сонца", "SSE.Controllers.Main.txtShape_teardrop": "Кропля", "SSE.Controllers.Main.txtShape_textRect": "Над<PERSON><PERSON>с", "SSE.Controllers.Main.txtShape_trapezoid": "Трапецыя", "SSE.Controllers.Main.txtShape_triangle": "Трохвугольнік", "SSE.Controllers.Main.txtShape_upArrow": "Стрэлка ўверх", "SSE.Controllers.Main.txtShape_upArrowCallout": "Зноска са стрэлкай уверх", "SSE.Controllers.Main.txtShape_upDownArrow": "Стрэлкі ўверх-уніз", "SSE.Controllers.Main.txtShape_uturnArrow": "Разгорнутая стрэлка", "SSE.Controllers.Main.txtShape_verticalScroll": "Вертыкальны скрутак", "SSE.Controllers.Main.txtShape_wave": "Х<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Авальная зноска", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Прамавугольная зноска", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Скругленая прамавугольная зноска", "SSE.Controllers.Main.txtStarsRibbons": "Зоркі і стужкі", "SSE.Controllers.Main.txtStyle_Bad": "Дрэнны", "SSE.Controllers.Main.txtStyle_Calculation": "Вылічэнне", "SSE.Controllers.Main.txtStyle_Check_Cell": "Праверачная ячэйка", "SSE.Controllers.Main.txtStyle_Comma": "Фінансавы", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Удакладненне", "SSE.Controllers.Main.txtStyle_Good": "До<PERSON><PERSON>ы", "SSE.Controllers.Main.txtStyle_Heading_1": "Загаловак 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Загаловак 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Загаловак 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Загаловак 4", "SSE.Controllers.Main.txtStyle_Input": "Увод", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Звязаная ячэйка", "SSE.Controllers.Main.txtStyle_Neutral": "Нейтральны", "SSE.Controllers.Main.txtStyle_Normal": "Звычайны", "SSE.Controllers.Main.txtStyle_Note": "Нататка", "SSE.Controllers.Main.txtStyle_Output": "Вывад", "SSE.Controllers.Main.txtStyle_Percent": "Адсотак", "SSE.Controllers.Main.txtStyle_Title": "Назва", "SSE.Controllers.Main.txtStyle_Total": "<PERSON>г<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Warning_Text": "Тэкст папярэджання", "SSE.Controllers.Main.txtTab": "Укладка", "SSE.Controllers.Main.txtTable": "Табліца", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtValues": "Значэнні", "SSE.Controllers.Main.txtXAxis": "Вось Х", "SSE.Controllers.Main.txtYAxis": "Вось Y", "SSE.Controllers.Main.unknownErrorText": "Невядомая памылка.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Ваш браўзер не падтрымліваецца.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Ніводнага дакумента не загружана. ", "SSE.Controllers.Main.uploadDocSizeMessage": "Перасягнуты максімальны памер дакумента.", "SSE.Controllers.Main.uploadImageExtMessage": "Невядомы фармат выявы.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Выяў не запампавана.", "SSE.Controllers.Main.uploadImageSizeMessage": "Перасягнуты максімальны памер выявы.", "SSE.Controllers.Main.uploadImageTextText": "Запампоўванне выявы…", "SSE.Controllers.Main.uploadImageTitleText": "Запампоўванне выявы", "SSE.Controllers.Main.waitText": "Калі ласка, пачакайце...", "SSE.Controllers.Main.warnBrowserIE9": "У IE9 праграма працуе вельмі павольна. Выкарыстоўвайце IE10 альбо пазнейшыя версіі.", "SSE.Controllers.Main.warnBrowserZoom": "Бягучыя налады маштабу старонкі падтрымліваюцца браўзерам толькі часткова. Калі ласка, скінце да прадвызначанага значэння націснуўшы Ctrl+О.", "SSE.Controllers.Main.warnLicenseExceeded": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.Гэты дакумент будзе адкрыты для прагляду.<br>Звяжыцеся з адміністратарам, каб даведацца больш.", "SSE.Controllers.Main.warnLicenseExp": "Тэрмін дзеяння ліцэнзіі сышоў.<br>Калі ласка, абнавіце ліцэнзію, пасля абнавіце старонку.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Тэрмін дзеяння ліцэнзіі сышоў.<br>У вас няма доступу да функцый рэдагавання дакументаў.<br>Калі ласка, звярніцеся да адміністратара.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Патрэбна абнавіць ліцэнзію.<br>У вас абмежаваны доступ да функцый рэдагавання дакументаў.<br>Каб атрымаць поўны доступ, звярніцеся да адміністратара", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.<br>Звяжыцеся з адміністратарам, каб даведацца больш.", "SSE.Controllers.Main.warnNoLicense": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.Гэты дакумент будзе адкрыты для прагляду.<br>Напішыце ў аддзел продажу %1,каб абмеркаваць асабістыя ўмовы ліцэнзіявання.", "SSE.Controllers.Main.warnNoLicenseUsers": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.<br>Напішыце ў аддзел продажу %1,каб абмеркаваць асабістыя ўмовы ліцэнзіявання.", "SSE.Controllers.Main.warnProcessRightsChange": "Вам адмоўлена ў правах на рэдагаванне гэтага файла.", "SSE.Controllers.Print.strAllSheets": "Усе аркушы", "SSE.Controllers.Print.textFirstCol": "Першы слупок", "SSE.Controllers.Print.textFirstRow": "Першы радок", "SSE.Controllers.Print.textFrozenCols": "Замацаваныя слупкі", "SSE.Controllers.Print.textFrozenRows": "Замацаваныя радкі", "SSE.Controllers.Print.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Controllers.Print.textNoRepeat": "Не паўтараць", "SSE.Controllers.Print.textRepeat": "Паўтараць…", "SSE.Controllers.Print.textSelectRange": "Абраць дыяпазон", "SSE.Controllers.Print.textWarning": "Увага", "SSE.Controllers.Print.txtCustom": "Адвольны", "SSE.Controllers.Print.warnCheckMargings": "Хібныя палі", "SSE.Controllers.Statusbar.errorLastSheet": "Працоўная кніга мусіць утрымліваць не менш за адзін бачны аркуш.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Не атрымалася выдаліць аркуш.", "SSE.Controllers.Statusbar.strSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Злучэнне страчана</b><br>Выконваецца спроба падлучэння. Праверце налады.", "SSE.Controllers.Statusbar.textSheetViewTip": "Вы знаходзіцеся ў рэжыме мініяцюры аркуша. Фільтры і сартаванне бачныя толькі вам і тым, хто працуе ў гэтым рэжыме.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Аркуш можа змяшчаць даныя. Працягнуць аперацыю?", "SSE.Controllers.Statusbar.zoomText": "Ма<PERSON><PERSON><PERSON>б {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Шрыфт, які вы хочаце захаваць, недаступны на гэтай прыладзе.<br>Стыль тэксту будзе паказвацца пры дапамозе аднаго з сістэмных шрыфтоў. Захаваны шрыфт будзе выкарыстоўвацца, калі ён стане даступным.<br>Працягнуць?", "SSE.Controllers.Toolbar.errorMaxRows": "ПАМЫЛКА! максімальная колькасць шэрагаў даных у адной дыяграме - 255", "SSE.Controllers.Toolbar.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Controllers.Toolbar.textAccent": "Дыякрытычныя знакі", "SSE.Controllers.Toolbar.textBracket": "Дужкі", "SSE.Controllers.Toolbar.textFontSizeErr": "Уведзена хібнае значэнне.<br>Увядзіце лік ад 1 да 409", "SSE.Controllers.Toolbar.textFraction": "Дробы", "SSE.Controllers.Toolbar.textFunction": "Функцыі", "SSE.Controllers.Toolbar.textInsert": "Уставіць", "SSE.Controllers.Toolbar.textIntegral": "Інтэгралы", "SSE.Controllers.Toolbar.textLargeOperator": "Буйныя аператары", "SSE.Controllers.Toolbar.textLimitAndLog": "Ліміты і лагарыфмы", "SSE.Controllers.Toolbar.textLongOperation": "Працяглая аперацыя", "SSE.Controllers.Toolbar.textMatrix": "Матрыцы", "SSE.Controllers.Toolbar.textOperator": "Аперата<PERSON>ы", "SSE.Controllers.Toolbar.textPivot": "Зводная табліца", "SSE.Controllers.Toolbar.textRadical": "Радыкалы", "SSE.Controllers.Toolbar.textScript": "Індэксы", "SSE.Controllers.Toolbar.textSymbols": "Сімвалы", "SSE.Controllers.Toolbar.textWarning": "Увага", "SSE.Controllers.Toolbar.txtAccent_Accent": "Націск", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Стрэлка ўправа-ўлева зверху", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Стрэлка ўлева зверху", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Стрэлка ўправа зверху", "SSE.Controllers.Toolbar.txtAccent_Bar": "Лінія", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Лінія знізу", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Рыска зверху", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Формула ў рамцы (з запаўненнем)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Формула ў рамцы (прыклад)", "SSE.Controllers.Toolbar.txtAccent_Check": "Сцяг", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Фігурная дужка знізу", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Фігурная дужка зверху", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Вектар А", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC з радком уверсе", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y з лініяй зверху", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Тры кропкі", "SSE.Controllers.Toolbar.txtAccent_DDot": "Дзве кропкі", "SSE.Controllers.Toolbar.txtAccent_Dot": "Кропка", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Падвойная рыска зверху", "SSE.Controllers.Toolbar.txtAccent_Grave": "Націск", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Сімвал групавання знізу", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Сімвал групавання зверху", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Гар<PERSON>ун улева зверху", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Гарпун управа зверху", "SSE.Controllers.Toolbar.txtAccent_Hat": "Вечка", "SSE.Controllers.Toolbar.txtAccent_Smile": "Скарочанасць", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Тыльда", "SSE.Controllers.Toolbar.txtBracket_Angle": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Дужкі і падзяляльнікі", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Дужкі і падзяляльнікі", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Curve": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Дужкі і падзяляльнікі", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Наборы (дзве ўмовы)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Наборы (тры ўмовы)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Набор аб’ектаў", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Набор аб’ектаў", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Наборы (прыклад)", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Бінамінальны каэфіцыент", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Бінамінальны каэфіцыент", "SSE.Controllers.Toolbar.txtBracket_Line": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Round": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Дужкі і падзяляльнікі", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Square": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Асобная дужка", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Асобная дужка", "SSE.Controllers.Toolbar.txtDeleteCells": "Выдаліць ячэйкі", "SSE.Controllers.Toolbar.txtExpand": "Пашырыць і ўпарадкаваць", "SSE.Controllers.Toolbar.txtExpandSort": "Даныя побач з абраным дыяпазонам не будуць сартавацца. Хочаце пашырыць абраны дыяпазон, каб уключыць даныя з супольных ячэек, альбо працягнуць сартаванне толькі абранага дыяпазону?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Дыяганальны дроб", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Дыферэнцыял", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Дыферэнцыял", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Дыферэнцыял", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Дыферэнцыял", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Лінейны дроб", "SSE.Controllers.Toolbar.txtFractionPi_2": "Пі падзяліць на два", "SSE.Controllers.Toolbar.txtFractionSmall": "Маленькі дроб", "SSE.Controllers.Toolbar.txtFractionVertical": "Вертыкальны дроб", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Арккосінус", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Гіпербалічны арккосінус", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Арккат<PERSON><PERSON><PERSON><PERSON>нс", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Гіпербалічны арккатангенс", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Арккасеканс", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Гіпербалічны арккасеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Арксеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Гіпербалічны арксеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Аркс<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Гіпербалічны арксінус", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Арк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Гіпербалічны арктангенс", "SSE.Controllers.Toolbar.txtFunction_Cos": "Ко<PERSON><PERSON><PERSON><PERSON>с", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Гіпербалічны косінус", "SSE.Controllers.Toolbar.txtFunction_Cot": "Ка<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Coth": "Гіпербалічны катангенс", "SSE.Controllers.Toolbar.txtFunction_Csc": "Касеканс", "SSE.Controllers.Toolbar.txtFunction_Csch": "Гіпербалічны касеканс", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Формула тангенса", "SSE.Controllers.Toolbar.txtFunction_Sec": "Секанс", "SSE.Controllers.Toolbar.txtFunction_Sech": "Гіпербалічны секанс", "SSE.Controllers.Toolbar.txtFunction_Sin": "Сінус", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Гіпербалічны сінус", "SSE.Controllers.Toolbar.txtFunction_Tan": "Та<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Гіпербалічны тангенс", "SSE.Controllers.Toolbar.txtInsertCells": "Уставіць ячэйкі", "SSE.Controllers.Toolbar.txtIntegral": "Інтэграл", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Дыферэнцыял dθ", "SSE.Controllers.Toolbar.txtIntegral_dx": "Дыферэнцыял dx", "SSE.Controllers.Toolbar.txtIntegral_dy": "Дыферэнцыял dy", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Інтэграл", "SSE.Controllers.Toolbar.txtIntegralDouble": "Падвойны інтэграл", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Падвойны інтэграл", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Падвойны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOriented": "Контурны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Павярхоўны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Павярхоўны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Павярхоўны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Аб’ёмны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Аб’ёмны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Аб’ёмны інтэграл", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Інтэграл", "SSE.Controllers.Toolbar.txtIntegralTriple": "Патройны інтэграл", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Патройны інтэграл", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Патройны інтэграл", "SSE.Controllers.Toolbar.txtInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Сумесны твор", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Сумесны твор", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Сумесны твор", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Сумесны твор", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Сумесны твор", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Твор", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Аб’яднанне", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Твор", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Твор", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Твор", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Твор", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Твор", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Аб’яднанне", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Аб’яднанне", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Аб’яднанне", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Аб’яднанне", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Аб’яднанне", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Прыклад ліміту", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Прыклад максімуму", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Натуральны лагарыфм", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Лагарыфм", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Лагарыфм", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Максімум", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Мін<PERSON><PERSON>ум", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Пустая матрыца 1х2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Пустая матрыца 1х3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Пустая матрыца 2х1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Пустая матрыца 2х2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Пустая матрыца з дужкамі", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Пустая матрыца з дужкамі", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Пустая матрыца з дужкамі", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Пустая матрыца з дужкамі", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Пустая матрыца 2х3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Пустая матрыца 3х1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Пустая матрыца 3х2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Пустая матрыца 3х3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Кропкі базавай лініі", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Кропкі пасярэдзіне", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Дыяганальныя кропкі", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Вертыкальныя кропкі", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Разрэджаная матрыца", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Разрэджаная матрыца", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Адзінкавая матрыца 2х2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Адзінкавая матрыца 3х3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Адзінкавая матрыца 3х3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Адзінкавая матрыца 3х3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Стрэлка ўправа-ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрэлка ўправа-ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрэлка ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрэлка ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Стрэлка ўправа знізу", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрэлка ўправа зверху", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Двукроп’е роўна", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Выхад", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Дэльта выхаду", "SSE.Controllers.Toolbar.txtOperator_Definition": "Роўна па вызначэнні", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Дэльта роўная", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Стрэлка ўправа-ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Стрэлка ўправа-ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрэлка ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрэлка ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Стрэлка ўправа знізу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрэлка ўправа зверху", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Роўна роўна", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Мінус роўна", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс роўна", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Адзінкі вымярэння", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Радыкал", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Радыкал", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Квадратны корань са ступенню", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Кубічны корань", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Радыкал са ступенню", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Квадратны корань", "SSE.Controllers.Toolbar.txtScriptCustom_1": "Індэкс", "SSE.Controllers.Toolbar.txtScriptCustom_2": "Індэкс", "SSE.Controllers.Toolbar.txtScriptCustom_3": "Індэкс", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Індэкс", "SSE.Controllers.Toolbar.txtScriptSub": "Ніжні індэкс", "SSE.Controllers.Toolbar.txtScriptSubSup": "Ніжні і верхні індэксы", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Ніжні і верхні індэксы злева", "SSE.Controllers.Toolbar.txtScriptSup": "Верхні індэкс", "SSE.Controllers.Toolbar.txtSorting": "Парадкаванне", "SSE.Controllers.Toolbar.txtSortSelected": "Парадкаваць абранае", "SSE.Controllers.Toolbar.txtSymbol_about": "Прыблізна", "SSE.Controllers.Toolbar.txtSymbol_additional": "Дапаўненне", "SSE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Альфа", "SSE.Controllers.Toolbar.txtSymbol_approx": "Амаль роўна", "SSE.Controllers.Toolbar.txtSymbol_ast": "Аперат<PERSON><PERSON> зорачка", "SSE.Controllers.Toolbar.txtSymbol_beta": "Бэта", "SSE.Controllers.Toolbar.txtSymbol_beth": "Бэт", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Апера<PERSON><PERSON><PERSON> адзнак", "SSE.Controllers.Toolbar.txtSymbol_cap": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Кубічны корань", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Гарызантальнае шматкроп’е пасярэдзіне", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Градусы Цэльсія", "SSE.Controllers.Toolbar.txtSymbol_chi": "Хі", "SSE.Controllers.Toolbar.txtSymbol_cong": "Прыблізна роўна", "SSE.Controllers.Toolbar.txtSymbol_cup": "Аб’яднанне", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Дыяганальнае шматкроп’е ўніз управа", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>ра<PERSON><PERSON><PERSON>ы", "SSE.Controllers.Toolbar.txtSymbol_delta": "Дэльта", "SSE.Controllers.Toolbar.txtSymbol_div": "Знак дзялення", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Стрэлка ўніз", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Пусты набор", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Эп<PERSON><PERSON>лон", "SSE.Controllers.Toolbar.txtSymbol_equals": "Роўна", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Адпавядае", "SSE.Controllers.Toolbar.txtSymbol_eta": "Эта", "SSE.Controllers.Toolbar.txtSymbol_exists": "Існуе", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Фактарыял", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градусы Фарэнгейта", "SSE.Controllers.Toolbar.txtSymbol_forall": "Для ўсіх", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Гама", "SSE.Controllers.Toolbar.txtSymbol_geq": "Больш альбо роўна", "SSE.Controllers.Toolbar.txtSymbol_gg": "Значна больш", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_in": "З’яўляецца элементам", "SSE.Controllers.Toolbar.txtSymbol_inc": "Інкрэмент", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Бясконцасць", "SSE.Controllers.Toolbar.txtSymbol_iota": "Іота", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Капа", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Лямбда", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Стрэлка ўлева", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрэлкі ўлева і ўправа", "SSE.Controllers.Toolbar.txtSymbol_leq": "Мен<PERSON> альбо роўна", "SSE.Controllers.Toolbar.txtSymbol_less": "Ме<PERSON><PERSON> за", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON>начна менш", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Мін<PERSON>с плюс", "SSE.Controllers.Toolbar.txtSymbol_mu": "Мю", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "SSE.Controllers.Toolbar.txtSymbol_neq": "Не роўна", "SSE.Controllers.Toolbar.txtSymbol_ni": "Утрымлівае як член", "SSE.Controllers.Toolbar.txtSymbol_not": "Знак адмаўлення", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Не існуе", "SSE.Controllers.Toolbar.txtSymbol_nu": "Ню", "SSE.Controllers.Toolbar.txtSymbol_o": "Амік<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_omega": "Амега", "SSE.Controllers.Toolbar.txtSymbol_partial": "Часны дыферэнцыял", "SSE.Controllers.Toolbar.txtSymbol_percent": "У адсотках", "SSE.Controllers.Toolbar.txtSymbol_phi": "Фі", "SSE.Controllers.Toolbar.txtSymbol_pi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "SSE.Controllers.Toolbar.txtSymbol_pm": "Плюс мінус", "SSE.Controllers.Toolbar.txtSymbol_propto": "Прапар<PERSON>ыйна", "SSE.Controllers.Toolbar.txtSymbol_psi": "Пс<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Корань чацвёртай ступені", "SSE.Controllers.Toolbar.txtSymbol_qed": "Канец доказу", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Дыяганальнае шматкроп’е ўверх управа", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ро", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Стрэлка ўправа", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Сігма", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Знак радыкала", "SSE.Controllers.Toolbar.txtSymbol_tau": "Тау", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Такім чынам", "SSE.Controllers.Toolbar.txtSymbol_theta": "Тэта", "SSE.Controllers.Toolbar.txtSymbol_times": "Знак множання", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Стрэлка ўверх", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Іп<PERSON><PERSON>лон", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Эпсілон (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Фі (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Пі (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ро (варыянт) ", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Сігма (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Тэта (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Вертыкальнае шматкроп’е", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ксі", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Дзэта", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Цёмны стыль табліцы", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Светлы стыль табліцы", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Сярэдні стыль табліцы", "SSE.Controllers.Toolbar.warnLongOperation": "Для завяршэння аперацыі, якую вы хочаце выканаць, можа спатрэбіцца шмат часу.<br>Сапраўды хочаце працягнуць?", "SSE.Controllers.Toolbar.warnMergeLostData": "У аб’яднанай ячэйцы застануцца толькі даныя з левай верхняй ячэйкі.<br>Сапраўды хочаце працягнуць?", "SSE.Controllers.Viewport.textFreezePanes": "Замацаваць вобласці", "SSE.Controllers.Viewport.textFreezePanesShadow": "Паказваць цень для замацаваных панэляў", "SSE.Controllers.Viewport.textHideFBar": "Схаваць панэль формул", "SSE.Controllers.Viewport.textHideGridlines": "Схаваць лініі сеткі", "SSE.Controllers.Viewport.textHideHeadings": "Схаваць загалоўкі", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Дзесятковы падзяляльнік", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Падзяляльнік разрадаў тысяч", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Налады вызначэння лічбавых даных", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Дадатковыя налады", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(няма)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Адвольны фільтр", "SSE.Views.AutoFilterDialog.textAddSelection": "Дадаць вылучанае ў фільтр", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Пустыя}", "SSE.Views.AutoFilterDialog.textSelectAll": "Вылучыць усе", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Абраць усе вынікі пошуку", "SSE.Views.AutoFilterDialog.textWarning": "Увага", "SSE.Views.AutoFilterDialog.txtAboveAve": "Вышэй за сярэдняе", "SSE.Views.AutoFilterDialog.txtBegins": "Пачынаецца з…", "SSE.Views.AutoFilterDialog.txtBelowAve": "Ніжэй за сярэдняе", "SSE.Views.AutoFilterDialog.txtBetween": "<PERSON>а<PERSON><PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtClear": "Ачысціць", "SSE.Views.AutoFilterDialog.txtContains": "Змяшчае…", "SSE.Views.AutoFilterDialog.txtEmpty": "Увядзіце значэнне для фільтрацыі", "SSE.Views.AutoFilterDialog.txtEnds": "Заканчваецца на…", "SSE.Views.AutoFilterDialog.txtEquals": "Роўна…", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Фільтр па колеру ячэек", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Фільтр па колеру шрыфту ", "SSE.Views.AutoFilterDialog.txtGreater": "Больш за…", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Больш альбо роўна…", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Фільтр адмецін", "SSE.Views.AutoFilterDialog.txtLess": "Ме<PERSON><PERSON> за…", "SSE.Views.AutoFilterDialog.txtLessEquals": "Мен<PERSON> альбо роўна…", "SSE.Views.AutoFilterDialog.txtNotBegins": "Не пачынаецца з…", "SSE.Views.AutoFilterDialog.txtNotBetween": "Не паміж…", "SSE.Views.AutoFilterDialog.txtNotContains": "Не змяшчае…", "SSE.Views.AutoFilterDialog.txtNotEnds": "Не заканчваецца на…", "SSE.Views.AutoFilterDialog.txtNotEquals": "Не роўна…", "SSE.Views.AutoFilterDialog.txtNumFilter": "Лічбавы фільтр", "SSE.Views.AutoFilterDialog.txtReapply": "Ужыць паўторна", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Сартаванне па колеры ячэек", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Сартаванне па колеры шрыфту", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Сартаванне па ўбыванні", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Сартаванне па ўзрастанні", "SSE.Views.AutoFilterDialog.txtSortOption": "Дадатковыя параметры сартавання… ", "SSE.Views.AutoFilterDialog.txtTextFilter": "Тэкставы фільтр", "SSE.Views.AutoFilterDialog.txtTitle": "Фільтр", "SSE.Views.AutoFilterDialog.txtTop10": "Першыя 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Фільтр значэнняў", "SSE.Views.AutoFilterDialog.warnFilterError": "Каб узяць фільтр значэння, вобласць значэнняў мусіць змяшчаць хоць адно поле.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Неабходна абраць прынамсі адно значэнне", "SSE.Views.CellEditor.textManager": "Кіраўнік назваў", "SSE.Views.CellEditor.tipFormula": "Уставіць функцыю", "SSE.Views.CellRangeDialog.errorMaxRows": "ПАМЫЛКА! максімальная колькасць шэрагаў даных у адной дыяграме - 255", "SSE.Views.CellRangeDialog.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.CellRangeDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.CellRangeDialog.txtInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.CellRangeDialog.txtTitle": "Абраць дыяпазон даных", "SSE.Views.CellSettings.strShrink": "Запаўняць па шырыні", "SSE.Views.CellSettings.strWrap": "Перанос тэксту", "SSE.Views.CellSettings.textAngle": "Вугал", "SSE.Views.CellSettings.textBackColor": "Колер фону", "SSE.Views.CellSettings.textBackground": "Колер фону", "SSE.Views.CellSettings.textBorderColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Стыль межаў", "SSE.Views.CellSettings.textColor": "Заліўка колерам", "SSE.Views.CellSettings.textControl": "Кіраванне тэкстам", "SSE.Views.CellSettings.textDirection": "Напрамак", "SSE.Views.CellSettings.textFill": "Заліўка", "SSE.Views.CellSettings.textForeground": "Колер пярэдняга плану", "SSE.Views.CellSettings.textGradient": "Град<PERSON><PERSON><PERSON>т", "SSE.Views.CellSettings.textGradientColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Градыентная заліўка", "SSE.Views.CellSettings.textItems": "элементаў", "SSE.Views.CellSettings.textLinear": "Лінейны", "SSE.Views.CellSettings.textNoFill": "Без заліўкі", "SSE.Views.CellSettings.textOrientation": "Арыентацыя тэксту", "SSE.Views.CellSettings.textPattern": "Узор", "SSE.Views.CellSettings.textPatternFill": "Узор", "SSE.Views.CellSettings.textPosition": "Пасада", "SSE.Views.CellSettings.textRadial": "Радыяльны", "SSE.Views.CellSettings.textSelectBorders": "Абярыце межы, да якіх патрэбна ўжыць абраны стыль", "SSE.Views.CellSettings.tipAddGradientPoint": "Дадаць кропку градыента", "SSE.Views.CellSettings.tipAll": "Вызначыць вонкавую мяжу і ўсе ўнутраныя лініі", "SSE.Views.CellSettings.tipBottom": "Вызначыць толькі вонкавую ніжнюю мяжу", "SSE.Views.CellSettings.tipDiagD": "Прызначыць дыяганальную мяжу зверху ўніз", "SSE.Views.CellSettings.tipDiagU": "Прызначыць дыяганальную мяжу знізу ўверх", "SSE.Views.CellSettings.tipInner": "Вызначыць толькі ўнутраныя лініі", "SSE.Views.CellSettings.tipInnerHor": "Вызначыць толькі гарызантальныя ўнутраныя лініі", "SSE.Views.CellSettings.tipInnerVert": "Вызначыць толькі вертыкальныя ўнутраныя лініі", "SSE.Views.CellSettings.tipLeft": "Вызначыць толькі вонкавую левую мяжу", "SSE.Views.CellSettings.tipNone": "Не вызначаць межаў", "SSE.Views.CellSettings.tipOuter": "Вызначыць толькі вонкавую мяжу", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Выдаліць кропку градыента", "SSE.Views.CellSettings.tipRight": "Вызначыць толькі вонкавую правую мяжу", "SSE.Views.CellSettings.tipTop": "Вызначыць толькі вонкавую верхнюю мяжу", "SSE.Views.ChartDataDialog.errorInFormula": "Ва ўведзенай формуле ёсць памылка.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Непрыдатная спасылка. Спасылка мусіць паказваць на адкрыты аркуш.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Максімальная колькасць кропак у шэрагу для дыяграмы - 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Максімальная колькасць шэрагаў даных дыяграмы - 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Непрыдатная спасылка. Спасылка для загалоўкаў, значэнняў памераў альбо адмецін мусіць паказваць на адну ячэйку, радок ці слупок.", "SSE.Views.ChartDataDialog.errorNoValues": "Для стварэння дыяграмы неабходна, каб шэраг змяшчаў хоць адно значэнне.", "SSE.Views.ChartDataDialog.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.ChartDataDialog.textAdd": "Дада<PERSON>ь", "SSE.Views.ChartDataDialog.textCategory": "Адмеціны гарызантальнай восі (катэгорыі)", "SSE.Views.ChartDataDialog.textData": "Дыяпазон даных для дыяграмы", "SSE.Views.ChartDataDialog.textDelete": "Выдаліць", "SSE.Views.ChartDataDialog.textDown": "Уніз", "SSE.Views.ChartDataDialog.textEdit": "Рэдагаваць", "SSE.Views.ChartDataDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.ChartDataDialog.textSelectData": "Абраць даныя", "SSE.Views.ChartDataDialog.textSeries": "Элементы легенды (шэрагі)", "SSE.Views.ChartDataDialog.textSwitch": "Пераключыць радок / слупок", "SSE.Views.ChartDataDialog.textTitle": "Даныя дыяграмы", "SSE.Views.ChartDataDialog.textUp": "Уверх", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Ва ўведзенай формуле ёсць памылка.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Непрыдатная спасылка. Спасылка мусіць паказваць на адкрыты аркуш.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Максімальная колькасць кропак у шэрагу для дыяграмы - 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Максімальная колькасць шэрагаў даных дыяграмы - 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Непрыдатная спасылка. Спасылка для загалоўкаў, значэнняў памераў альбо адмецін мусіць паказваць на адну ячэйку, радок ці слупок.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Для стварэння дыяграмы неабходна, каб шэраг змяшчаў хоць адно значэнне.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.ChartDataRangeDialog.textSelectData": "Абраць даныя", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Дыяпазон адмецін восяў", "SSE.Views.ChartDataRangeDialog.txtChoose": "Абярыце дыяпазон", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Назва шэрагу", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Адмеціны восяў", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Змян<PERSON>ць шэраг", "SSE.Views.ChartDataRangeDialog.txtValues": "Значэнні", "SSE.Views.ChartDataRangeDialog.txtXValues": "Значэнні Х", "SSE.Views.ChartDataRangeDialog.txtYValues": "Значэнні Y", "SSE.Views.ChartSettings.strLineWeight": "Таўшчыня лініі", "SSE.Views.ChartSettings.strSparkColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Шабл<PERSON>н", "SSE.Views.ChartSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ChartSettings.textBorderSizeErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 0 да 1584 пунктаў.", "SSE.Views.ChartSettings.textChangeType": "Змяніць тып", "SSE.Views.ChartSettings.textChartType": "Змяніць тып дыяграмы", "SSE.Views.ChartSettings.textEditData": "Рэдагаваць даныя і месца", "SSE.Views.ChartSettings.textFirstPoint": "Першая кропка", "SSE.Views.ChartSettings.textHeight": "Вышыня", "SSE.Views.ChartSettings.textHighPoint": "Максімальная кропка", "SSE.Views.ChartSettings.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.ChartSettings.textLastPoint": "Апошняя кропка", "SSE.Views.ChartSettings.textLowPoint": "Мінімальная кропка", "SSE.Views.ChartSettings.textMarkers": "Адзнакі", "SSE.Views.ChartSettings.textNegativePoint": "Адмоўная кропка", "SSE.Views.ChartSettings.textRanges": "Дыяпазон даных", "SSE.Views.ChartSettings.textSelectData": "Абраць даныя", "SSE.Views.ChartSettings.textShow": "Паказаць", "SSE.Views.ChartSettings.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "Стыль", "SSE.Views.ChartSettings.textType": "Тып", "SSE.Views.ChartSettings.textWidth": "Шырыня", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ПАМЫЛКА! Максімальная колькасць кропак у шэрагу дыяграмы - 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ПАМЫЛКА! максімальная колькасць шэрагаў даных у адной дыяграме - 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.ChartSettingsDlg.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.ChartSettingsDlg.textAltDescription": "Апісанне", "SSE.Views.ChartSettingsDlg.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Загаловак", "SSE.Views.ChartSettingsDlg.textAuto": "Аўта", "SSE.Views.ChartSettingsDlg.textAutoEach": "Аўтаматычна для кожнага", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Перасячэнне з воссю", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Параметры восі", "SSE.Views.ChartSettingsDlg.textAxisPos": "Пазіцыя восі", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Налады восі", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Паміж падзеламі", "SSE.Views.ChartSettingsDlg.textBillions": "Мільярды", "SSE.Views.ChartSettingsDlg.textBottom": "Знізу", "SSE.Views.ChartSettingsDlg.textCategoryName": "Назва катэгорыі", "SSE.Views.ChartSettingsDlg.textCenter": "Па цэнтры", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Элементы дыяграмы і<br>легенда дыяграмы", "SSE.Views.ChartSettingsDlg.textChartTitle": "Загаловак дыяграмы", "SSE.Views.ChartSettingsDlg.textCross": "На скрыжаванні", "SSE.Views.ChartSettingsDlg.textCustom": "Адвольны", "SSE.Views.ChartSettingsDlg.textDataColumns": "у слупках", "SSE.Views.ChartSettingsDlg.textDataLabels": "Адмеціны даных", "SSE.Views.ChartSettingsDlg.textDataRows": "у радках", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Паказваць легенду", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Схаваныя і пустыя ячэйкі", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Злучаць кропкі даных лініямі", "SSE.Views.ChartSettingsDlg.textFit": "Па шырыні", "SSE.Views.ChartSettingsDlg.textFixed": "Фіксаванае", "SSE.Views.ChartSettingsDlg.textGaps": "Пуста", "SSE.Views.ChartSettingsDlg.textGridLines": "Лініі сеткі", "SSE.Views.ChartSettingsDlg.textGroup": "Група спарклайнаў", "SSE.Views.ChartSettingsDlg.textHide": "Схаваць", "SSE.Views.ChartSettingsDlg.textHigh": "Выш<PERSON>й", "SSE.Views.ChartSettingsDlg.textHorAxis": "Гарызантальная вось", "SSE.Views.ChartSettingsDlg.textHorizontal": "Гарызантальна", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Сотні", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Унутры", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Унутры ўнізе", "SSE.Views.ChartSettingsDlg.textInnerTop": "Унутры зверху", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.ChartSettingsDlg.textLabelDist": "Адлегласць да адмеціны", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Прамежак паміж адмецінамі", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Параметры адмеціны", "SSE.Views.ChartSettingsDlg.textLabelPos": "Пазіцыя адмеціны", "SSE.Views.ChartSettingsDlg.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeft": "Злева", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Накладанне злева", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Знізу", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Злева", "SSE.Views.ChartSettingsDlg.textLegendPos": "Легенда", "SSE.Views.ChartSettingsDlg.textLegendRight": "Справа", "SSE.Views.ChartSettingsDlg.textLegendTop": "Уверсе", "SSE.Views.ChartSettingsDlg.textLines": "Лініі", "SSE.Views.ChartSettingsDlg.textLocationRange": "Дыяпазон размяшчэння:", "SSE.Views.ChartSettingsDlg.textLow": "Ніжэ<PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Асноўныя", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Асноўныя і дадатковыя", "SSE.Views.ChartSettingsDlg.textMajorType": "Асноўны тып", "SSE.Views.ChartSettingsDlg.textManual": "Уласнаручна", "SSE.Views.ChartSettingsDlg.textMarkers": "Адзнакі", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Прамежак паміж падзеламі", "SSE.Views.ChartSettingsDlg.textMaxValue": "Максімальнае значэнне", "SSE.Views.ChartSettingsDlg.textMillions": "Мільёны", "SSE.Views.ChartSettingsDlg.textMinor": "Дадатковыя", "SSE.Views.ChartSettingsDlg.textMinorType": "Дадатковы тып", "SSE.Views.ChartSettingsDlg.textMinValue": "Мінімальнае значэнне", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Побач з воссю", "SSE.Views.ChartSettingsDlg.textNone": "Няма", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Без накладання", "SSE.Views.ChartSettingsDlg.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Падзелы", "SSE.Views.ChartSettingsDlg.textOut": "Звонку", "SSE.Views.ChartSettingsDlg.textOuterTop": "Звонку зверху", "SSE.Views.ChartSettingsDlg.textOverlay": "Накладанне", "SSE.Views.ChartSettingsDlg.textReverse": " Значэнні ў адваротным парадку", "SSE.Views.ChartSettingsDlg.textReverseOrder": "У адваротным парадку", "SSE.Views.ChartSettingsDlg.textRight": "Справа", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Накладанне справа", "SSE.Views.ChartSettingsDlg.textRotated": "Павернута", "SSE.Views.ChartSettingsDlg.textSameAll": "Аднолькава для ўсіх", "SSE.Views.ChartSettingsDlg.textSelectData": "Абраць даныя", "SSE.Views.ChartSettingsDlg.textSeparator": "Падзяляльнік адмецін", "SSE.Views.ChartSettingsDlg.textSeriesName": "Назва шэрагу", "SSE.Views.ChartSettingsDlg.textShow": "Паказаць", "SSE.Views.ChartSettingsDlg.textShowBorders": "Паказваць межы дыяграмы", "SSE.Views.ChartSettingsDlg.textShowData": "Паказваць даныя ў схаваных радках і слупках", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Паказваць пустыя ячэйкі як", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Паказваць вось", "SSE.Views.ChartSettingsDlg.textShowValues": "Паказваць значэнні дыяграмы", "SSE.Views.ChartSettingsDlg.textSingle": "Асобны спарклайн", "SSE.Views.ChartSettingsDlg.textSmooth": "Згладжаныя", "SSE.Views.ChartSettingsDlg.textSnap": "Далучэнне да ячэйкі", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Дыяпазоны спарклайнаў", "SSE.Views.ChartSettingsDlg.textStraight": "Непасрэдныя", "SSE.Views.ChartSettingsDlg.textStyle": "Стыль", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Тысячы", "SSE.Views.ChartSettingsDlg.textTickOptions": "Параметры падзелаў", "SSE.Views.ChartSettingsDlg.textTitle": "Дыяграма - дадатковыя налады", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Спарклайн - дадатковыя налады", "SSE.Views.ChartSettingsDlg.textTop": "Уверсе", "SSE.Views.ChartSettingsDlg.textTrillions": "Трыльёны", "SSE.Views.ChartSettingsDlg.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.ChartSettingsDlg.textType": "Тып", "SSE.Views.ChartSettingsDlg.textTypeData": "Тып і даныя", "SSE.Views.ChartSettingsDlg.textUnits": "Адзінкі адлюстравання", "SSE.Views.ChartSettingsDlg.textValue": "Значэнне", "SSE.Views.ChartSettingsDlg.textVertAxis": "Вертыкальная вось", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Назва восі Х", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Назва восі Y", "SSE.Views.ChartSettingsDlg.textZero": "Нуль", "SSE.Views.ChartSettingsDlg.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.CreatePivotDialog.textDataRange": "Дыяпазон зыходных даных", "SSE.Views.CreatePivotDialog.textDestination": "Абраць месца размяшчэння табліцы", "SSE.Views.CreatePivotDialog.textExist": "Існы аркуш", "SSE.Views.CreatePivotDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.CreatePivotDialog.textNew": "Новы аркуш", "SSE.Views.CreatePivotDialog.textSelectData": "Абраць даныя", "SSE.Views.CreatePivotDialog.textTitle": "Стварыць зводную табліцу", "SSE.Views.CreatePivotDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.DataTab.capBtnGroup": "Згрупаваць", "SSE.Views.DataTab.capBtnTextCustomSort": "Адвольнае сартаванне", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Выдаліць паўторы", "SSE.Views.DataTab.capBtnTextToCol": "Тэкст па слупках", "SSE.Views.DataTab.capBtnUngroup": "Разгрупаваць", "SSE.Views.DataTab.textBelow": "Вынікі ў радках пад данымі", "SSE.Views.DataTab.textClear": "Выдаліць структуру", "SSE.Views.DataTab.textColumns": "Разгрупаваць слупкі", "SSE.Views.DataTab.textGroupColumns": "Згрупаваць слупкі", "SSE.Views.DataTab.textGroupRows": "Згрупаваць радкі", "SSE.Views.DataTab.textRightOf": "Вынікі ў слупках справа ад даных", "SSE.Views.DataTab.textRows": "Разгрупаваць радкі", "SSE.Views.DataTab.tipCustomSort": "Адвольнае сартаванне", "SSE.Views.DataTab.tipGroup": "Згрупаваць дыяпазон ячэек", "SSE.Views.DataTab.tipRemDuplicates": "Выдаліць з аркуша паўторныя радкі", "SSE.Views.DataTab.tipToColumns": "Падзяліць тэкст ячэйкі па слупках", "SSE.Views.DataTab.tipUngroup": "Разгрупаваць дыяпазон ячэек", "SSE.Views.DataValidationDialog.textAllow": "Дазволіць", "SSE.Views.DataValidationDialog.textData": "Даныя", "SSE.Views.DataValidationDialog.textFormula": "Формула", "SSE.Views.DataValidationDialog.textMax": "Максімум", "SSE.Views.DataValidationDialog.textMessage": "Паведамленне", "SSE.Views.DataValidationDialog.textMin": "Мін<PERSON><PERSON>ум", "SSE.Views.DataValidationDialog.txtBetween": "паміж", "SSE.Views.DataValidationDialog.txtDate": "Дата", "SSE.Views.DataValidationDialog.txtDecimal": "Дзесятковыя знакі", "SSE.Views.DataValidationDialog.txtEqual": "Роўна", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Больш альбо роўна", "SSE.Views.DataValidationDialog.txtLessThan": "Ме<PERSON><PERSON> за", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Мен<PERSON> альбо роўна", "SSE.Views.DataValidationDialog.txtNotEqual": "Не роўна", "SSE.Views.DigitalFilterDialog.capAnd": "і", "SSE.Views.DigitalFilterDialog.capCondition1": "роўна", "SSE.Views.DigitalFilterDialog.capCondition10": "не заканчваецца на", "SSE.Views.DigitalFilterDialog.capCondition11": "змяшчае", "SSE.Views.DigitalFilterDialog.capCondition12": "не змяшчае", "SSE.Views.DigitalFilterDialog.capCondition2": "не роўна", "SSE.Views.DigitalFilterDialog.capCondition3": "больш за", "SSE.Views.DigitalFilterDialog.capCondition4": "больш альбо роўна", "SSE.Views.DigitalFilterDialog.capCondition5": "менш за", "SSE.Views.DigitalFilterDialog.capCondition6": "менш альбо роўна", "SSE.Views.DigitalFilterDialog.capCondition7": "пачынаецца з", "SSE.Views.DigitalFilterDialog.capCondition8": "не пачынаецца з", "SSE.Views.DigitalFilterDialog.capCondition9": "заканчваецца на", "SSE.Views.DigitalFilterDialog.capOr": "Альбо", "SSE.Views.DigitalFilterDialog.textNoFilter": "без фільтра", "SSE.Views.DigitalFilterDialog.textShowRows": "Паказаць радкі, у якіх", "SSE.Views.DigitalFilterDialog.textUse1": "Выкарыстоўвайце знак ? замест любога асобнага сімвала", "SSE.Views.DigitalFilterDialog.textUse2": "Выкарыстоўвайце знак * замест любой паслядоўнасці сімвалаў", "SSE.Views.DigitalFilterDialog.txtTitle": "Адвольны фільтр", "SSE.Views.DocumentHolder.advancedImgText": "Дадатковыя налады выявы", "SSE.Views.DocumentHolder.advancedShapeText": "Дадатковыя налады фігуры", "SSE.Views.DocumentHolder.advancedSlicerText": "Дадатковыя параметры зводкі", "SSE.Views.DocumentHolder.bottomCellText": "Выраўнаваць па ніжняму краю", "SSE.Views.DocumentHolder.bulletsText": "Адзнакі і нумарацыя", "SSE.Views.DocumentHolder.centerCellText": "Выраўнаваць па сярэдзіне", "SSE.Views.DocumentHolder.chartText": "Дадатковыя налады дыяграмы", "SSE.Views.DocumentHolder.deleteColumnText": "Слупок", "SSE.Views.DocumentHolder.deleteRowText": "Радок", "SSE.Views.DocumentHolder.deleteTableText": "Табліца", "SSE.Views.DocumentHolder.direct270Text": "Павярнуць тэкст уверх", "SSE.Views.DocumentHolder.direct90Text": "Павярнуць тэкст уніз", "SSE.Views.DocumentHolder.directHText": "Гарызантальна", "SSE.Views.DocumentHolder.directionText": "Напрамак тэксту", "SSE.Views.DocumentHolder.editChartText": "Рэдагаваць даныя", "SSE.Views.DocumentHolder.editHyperlinkText": "Рэдагаваць гіперспасылку", "SSE.Views.DocumentHolder.insertColumnLeftText": "Слупок злева", "SSE.Views.DocumentHolder.insertColumnRightText": "Слупок справа", "SSE.Views.DocumentHolder.insertRowAboveText": "Радок вышэй", "SSE.Views.DocumentHolder.insertRowBelowText": "Радок ніжэй", "SSE.Views.DocumentHolder.originalSizeText": "Актуальны памер", "SSE.Views.DocumentHolder.removeHyperlinkText": "Выдаліць гіперспасылку", "SSE.Views.DocumentHolder.selectColumnText": "Слупок", "SSE.Views.DocumentHolder.selectDataText": "Даныя слупкоў", "SSE.Views.DocumentHolder.selectRowText": "Радок", "SSE.Views.DocumentHolder.selectTableText": "Табліца", "SSE.Views.DocumentHolder.strDelete": "Выдаліць подпіс", "SSE.Views.DocumentHolder.strDetails": "Падрабязнасці подпісу", "SSE.Views.DocumentHolder.strSetup": "Наладжванне подпісу", "SSE.Views.DocumentHolder.strSign": "Падпісаць", "SSE.Views.DocumentHolder.textAlign": "Выраўноўванне", "SSE.Views.DocumentHolder.textArrange": "Пар<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Перамясціць у фон", "SSE.Views.DocumentHolder.textArrangeBackward": "Адправіць назад", "SSE.Views.DocumentHolder.textArrangeForward": "Перамясціць уперад", "SSE.Views.DocumentHolder.textArrangeFront": "Перанесці на пярэдні план", "SSE.Views.DocumentHolder.textAverage": "Сярэдняе", "SSE.Views.DocumentHolder.textBullets": "Адзнакі", "SSE.Views.DocumentHolder.textCount": "Колькасць", "SSE.Views.DocumentHolder.textCrop": "Абрэзаць", "SSE.Views.DocumentHolder.textCropFill": "Заліўка", "SSE.Views.DocumentHolder.textCropFit": "Умясціць", "SSE.Views.DocumentHolder.textEntriesList": "Абраць са спіса", "SSE.Views.DocumentHolder.textFlipH": "Адлюстраваць па гарызанталі", "SSE.Views.DocumentHolder.textFlipV": "Адлюстраваць па вертыкалі", "SSE.Views.DocumentHolder.textFreezePanes": "Замацаваць вобласці", "SSE.Views.DocumentHolder.textFromFile": "З файла", "SSE.Views.DocumentHolder.textFromStorage": "Са сховішча", "SSE.Views.DocumentHolder.textFromUrl": "Па URL", "SSE.Views.DocumentHolder.textListSettings": "Налады спіса", "SSE.Views.DocumentHolder.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMore": "Іншыя функцыі", "SSE.Views.DocumentHolder.textMoreFormats": "Іншыя фарматы", "SSE.Views.DocumentHolder.textNone": "Няма", "SSE.Views.DocumentHolder.textReplace": "Замяніць выяву", "SSE.Views.DocumentHolder.textRotate": "Паварочванне", "SSE.Views.DocumentHolder.textRotate270": "Павярнуць улева на 90°", "SSE.Views.DocumentHolder.textRotate90": "Павярнуць управа на 90°", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Выраўнаваць па ніжняму краю", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Выраўнаваць па цэнтры", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Выраўнаваць па леваму краю", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Выраўнаваць па сярэдзіне", "SSE.Views.DocumentHolder.textShapeAlignRight": "Выраўнаваць па праваму краю", "SSE.Views.DocumentHolder.textShapeAlignTop": "Выраўнаваць па верхняму краю", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Сума", "SSE.Views.DocumentHolder.textUndo": "Адрабіць", "SSE.Views.DocumentHolder.textUnFreezePanes": "Адмацаваць вобласці", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Маркеры-стрэлкі", "SSE.Views.DocumentHolder.topCellText": "Выраўнаваць па верхняму краю", "SSE.Views.DocumentHolder.txtAccounting": "Фінансавы", "SSE.Views.DocumentHolder.txtAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "SSE.Views.DocumentHolder.txtAddNamedRange": "Прызначыць назву", "SSE.Views.DocumentHolder.txtArrange": "Пар<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Па ўзрастанні", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Аўтавызначэнне шырыні слупка", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Аўтавызначэнне шырыні радка", "SSE.Views.DocumentHolder.txtClear": "Ачысціць", "SSE.Views.DocumentHolder.txtClearAll": "Усе", "SSE.Views.DocumentHolder.txtClearComments": "Каментары", "SSE.Views.DocumentHolder.txtClearFormat": "Фарматаванне", "SSE.Views.DocumentHolder.txtClearHyper": "Гіперспасылкі", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Ачысціць абраныя групы спарклайнаў", "SSE.Views.DocumentHolder.txtClearSparklines": "Ачысціць абраныя спарклайны", "SSE.Views.DocumentHolder.txtClearText": "Тэкст", "SSE.Views.DocumentHolder.txtColumn": "Слупок", "SSE.Views.DocumentHolder.txtColumnWidth": "Прызначыць шырыню слупка", "SSE.Views.DocumentHolder.txtCopy": "Капіяваць", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Адвольная шырыня слупка", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Адвольная вышыня радка", "SSE.Views.DocumentHolder.txtCustomSort": "Адвольнае сартаванне", "SSE.Views.DocumentHolder.txtCut": "Выразаць", "SSE.Views.DocumentHolder.txtDate": "Дата", "SSE.Views.DocumentHolder.txtDelete": "Выдаліць", "SSE.Views.DocumentHolder.txtDescending": "Па памяншэнні", "SSE.Views.DocumentHolder.txtDistribHor": "Размеркаваць па гарызанталі", "SSE.Views.DocumentHolder.txtDistribVert": "Размеркаваць па вертыкалі", "SSE.Views.DocumentHolder.txtEditComment": "Рэдагава<PERSON>ь каментар", "SSE.Views.DocumentHolder.txtFilter": "Фільтр", "SSE.Views.DocumentHolder.txtFilterCellColor": "Фільтр па колеру ячэек", "SSE.Views.DocumentHolder.txtFilterFontColor": "Фільтр па колеру шрыфту ", "SSE.Views.DocumentHolder.txtFilterValue": "Фільтр па значэнні абранай ячэйкі", "SSE.Views.DocumentHolder.txtFormula": "Уставіць функцыю", "SSE.Views.DocumentHolder.txtFraction": "Дроб", "SSE.Views.DocumentHolder.txtGeneral": "Агульны", "SSE.Views.DocumentHolder.txtGroup": "Згрупаваць", "SSE.Views.DocumentHolder.txtHide": "Схаваць", "SSE.Views.DocumentHolder.txtInsert": "Уставіць", "SSE.Views.DocumentHolder.txtInsHyperlink": "Гіперспасылка", "SSE.Views.DocumentHolder.txtNumber": "Лічбавы", "SSE.Views.DocumentHolder.txtNumFormat": "Фармат нумара", "SSE.Views.DocumentHolder.txtPaste": "Уставіць", "SSE.Views.DocumentHolder.txtPercentage": "Адсотак", "SSE.Views.DocumentHolder.txtReapply": "Ужыць паўторна", "SSE.Views.DocumentHolder.txtRow": "Радок", "SSE.Views.DocumentHolder.txtRowHeight": "Вызначыць вышыню радка", "SSE.Views.DocumentHolder.txtScientific": "Навуковы", "SSE.Views.DocumentHolder.txtSelect": "Абраць", "SSE.Views.DocumentHolder.txtShiftDown": "Ячэйкі са зрухам уніз", "SSE.Views.DocumentHolder.txtShiftLeft": "Ячэйкі са зрухам улева", "SSE.Views.DocumentHolder.txtShiftRight": "Ячэйкі са зрухам управа", "SSE.Views.DocumentHolder.txtShiftUp": "Ячэйкі са зрухам уверх", "SSE.Views.DocumentHolder.txtShow": "Паказаць", "SSE.Views.DocumentHolder.txtShowComment": "Паказаць каментар", "SSE.Views.DocumentHolder.txtSort": "Сартаванне", "SSE.Views.DocumentHolder.txtSortCellColor": "Спачатку ячэйкі з вылучаным колерам", "SSE.Views.DocumentHolder.txtSortFontColor": "Спачатку ячэйкі з вылучаным шрыфтам", "SSE.Views.DocumentHolder.txtSparklines": "Спарклайны", "SSE.Views.DocumentHolder.txtText": "Тэкст", "SSE.Views.DocumentHolder.txtTextAdvanced": "Дадатковыя налады абзаца", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtUngroup": "Разгрупаваць", "SSE.Views.DocumentHolder.txtWidth": "Шырыня", "SSE.Views.DocumentHolder.vertAlignText": "Вертыкальнае выраўноўванне", "SSE.Views.FieldSettingsDialog.strLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strSubtotals": "Прамежкавыя вынікі", "SSE.Views.FieldSettingsDialog.textReport": "Форма справаздачы", "SSE.Views.FieldSettingsDialog.textTitle": "Налады палёў", "SSE.Views.FieldSettingsDialog.txtAverage": "Сярэдняе", "SSE.Views.FieldSettingsDialog.txtBlank": "Устаўляць пусты радок пасля кожнага запісу", "SSE.Views.FieldSettingsDialog.txtBottom": "Паказваць у ніжняй частцы групы", "SSE.Views.FieldSettingsDialog.txtCompact": "Кампактная", "SSE.Views.FieldSettingsDialog.txtCount": "Колькасць", "SSE.Views.FieldSettingsDialog.txtCountNums": "Колькасць лікаў", "SSE.Views.FieldSettingsDialog.txtCustomName": "Адвольная назва", "SSE.Views.FieldSettingsDialog.txtEmpty": "Паказваць элементы без даных", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtOutline": "Структура", "SSE.Views.FieldSettingsDialog.txtProduct": "Твор", "SSE.Views.FieldSettingsDialog.txtRepeat": "Паўтараць адмеціны элементаў у кожным радку", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Паказваць прамежкавыя вынікі", "SSE.Views.FieldSettingsDialog.txtSourceName": "Назва крыніцы:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Сума", "SSE.Views.FieldSettingsDialog.txtSummarize": "Функцыі для прамежкавых вынікаў", "SSE.Views.FieldSettingsDialog.txtTabular": "Як табліца", "SSE.Views.FieldSettingsDialog.txtTop": "Паказваць у загалоўку групы", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Перайсці да дакументаў", "SSE.Views.FileMenu.btnCloseMenuCaption": "Закрыць меню", "SSE.Views.FileMenu.btnCreateNewCaption": "Стварыць новую", "SSE.Views.FileMenu.btnDownloadCaption": "Спампаваць як", "SSE.Views.FileMenu.btnHelpCaption": "Даведка", "SSE.Views.FileMenu.btnInfoCaption": "Інфармацыя аб табліцы", "SSE.Views.FileMenu.btnPrintCaption": "Друкаванне", "SSE.Views.FileMenu.btnProtectCaption": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ь", "SSE.Views.FileMenu.btnRecentFilesCaption": "Адкрыць апошнія", "SSE.Views.FileMenu.btnRenameCaption": "Змяніць назву", "SSE.Views.FileMenu.btnReturnCaption": "Назад да табліцы", "SSE.Views.FileMenu.btnRightsCaption": "Правы на доступ", "SSE.Views.FileMenu.btnSaveAsCaption": "Захаваць як", "SSE.Views.FileMenu.btnSaveCaption": "Захаваць", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Захаваць копію як", "SSE.Views.FileMenu.btnSettingsCaption": "Дадатковыя налады", "SSE.Views.FileMenu.btnToEditCaption": "Рэдагаваць табліцу", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Стварыць", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Ужыць", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Дадаць аўтара", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Дадаць тэкст", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Аўтар", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Змяніць правы на доступ", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Ка<PERSON><PERSON>н<PERSON>ар", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Створана", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Аўтар апошняй змены", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Апошняя змена", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Уладальнік", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Размяшчэнне", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Асобы, што маюць правы", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Тэма", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Назва", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Запампавана", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Змяніць правы на доступ", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Асобы, што маюць правы", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Ужыць", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Рэжым сумеснага рэдагавання", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Дзесятковы падзяляльнік", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Хутк<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Хінтынг шрыфтоў", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Мова формул", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Прыклад: СУМ; МІН; МАКС; ПАДЛІК", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Налады макрасаў", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Паказваць кнопку параметраў устаўкі падчас устаўкі", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Рэгіянальныя налады", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Прыклад:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Строгі", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Тэма інтэрфейсу", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Падзяляльнік разрадаў тысяч", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Адзінкі вымярэння", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Выкарыстоўваць падзяляльнікі на базе рэгіянальных налад", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Прадвызначанае значэнне маштабу", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Кожныя 10 хвілін", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Кожныя 30 хвілін", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Кожныя 5 хвілін", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON>т<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Аўтаматычнае аднаўленне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Аўтазахаванне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Выключана", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Захаванне прамежкавых версій", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Кожн<PERSON>ю хвіліну", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Стыль спасылак", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Беларуская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Прадвызначаны рэжым кэшу", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Сантыметр", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Нямецкая", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Англійская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Іспанская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Французская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Цаля", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Італьянская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "як OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Уласны", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Польская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON>у<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Расійская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Уключыць усе", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Уключыць усе макрасы без апавяшчэння", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Адключыць усе", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Адключыць усе макрасы без апавяшчэння", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Паказваць апавяшчэнне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Адключыць усе макрасы з апавяшчэннем", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "як Windows", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Увага", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Пры дапамозе пароля", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> табліцу", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Пры дапамозе подпісу", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Рэдагаваць табліцу", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Пры рэдагаванні табліцы выдаляцца подпісы.<br>Працягнуць?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Гэтая электронная табліца абароненая паролем", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Гэтую табліцу неабходна падпісаць.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "У табліцу былі дададзеныя дзейныя подпісы. Табліца абароненая ад рэдагавання.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Некаторыя з лічбавых подпісаў электроннай табліцы хібныя альбо іх немагчыма праверыць. Табліца абароненая ад рэдагавання.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Прагляд подпісаў", "SSE.Views.FormatRulesEditDlg.fillColor": "Колер запаўнення", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Усе межы", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Аўтаматычна", "SSE.Views.FormatRulesEditDlg.textAxis": "Восі", "SSE.Views.FormatRulesEditDlg.textBold": "Тоўсты", "SSE.Views.FormatRulesEditDlg.textBorder": "Мяжа", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Стыль межаў", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Ніжнія межы", "SSE.Views.FormatRulesEditDlg.textClear": "Ачысціць", "SSE.Views.FormatRulesEditDlg.textCustom": "Адвольны", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Дыяганальная мяжа зверху ўніз", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Дыяганальная мяжа знізу ўверх", "SSE.Views.FormatRulesEditDlg.textFill": "Заліўка", "SSE.Views.FormatRulesEditDlg.textFormat": "Фарматаванне", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.FormatRulesEditDlg.textItalic": "Курсіў", "SSE.Views.FormatRulesEditDlg.textItem": "Элемент", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Левыя межы", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Унутраныя гарызантальныя межы", "SSE.Views.FormatRulesEditDlg.textNewColor": "Адвольны колер", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Без межаў", "SSE.Views.FormatRulesEditDlg.textNone": "Няма", "SSE.Views.FormatRulesEditDlg.tipBorders": "Межы", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Фінансавы", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Дата", "SSE.Views.FormatRulesEditDlg.txtFraction": "Дроб", "SSE.Views.FormatRulesManagerDlg.guestText": "Госць", "SSE.Views.FormatRulesManagerDlg.textAbove": "Вышэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.textBelow": "Ніжэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.textDelete": "Выдаліць", "SSE.Views.FormatRulesManagerDlg.textEdit": "Рэдагаваць", "SSE.Views.FormatRulesManagerDlg.textFormat": "Фарматаванне", "SSE.Views.FormatRulesManagerDlg.textNew": "Новы", "SSE.Views.FormatSettingsDialog.textCategory": "Катэгорыя", "SSE.Views.FormatSettingsDialog.textDecimal": "Дзесятковыя знакі", "SSE.Views.FormatSettingsDialog.textFormat": "Фармат", "SSE.Views.FormatSettingsDialog.textSeparator": "Выкарыстоўваць падзяляльнік разрадаў", "SSE.Views.FormatSettingsDialog.textSymbols": "Сімвалы", "SSE.Views.FormatSettingsDialog.textTitle": "Фармат нумара", "SSE.Views.FormatSettingsDialog.txtAccounting": "Фінансавы", "SSE.Views.FormatSettingsDialog.txtAs10": "Дзясятыя часткі (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Сотыя часткі (50/100) ", "SSE.Views.FormatSettingsDialog.txtAs16": "Шаснаццатыя часткі (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Паловы (1/2) ", "SSE.Views.FormatSettingsDialog.txtAs4": "Чацвёртыя часткі (2/4) ", "SSE.Views.FormatSettingsDialog.txtAs8": "Восьмыя часткі (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Адвольны", "SSE.Views.FormatSettingsDialog.txtDate": "Дата", "SSE.Views.FormatSettingsDialog.txtFraction": "Дроб", "SSE.Views.FormatSettingsDialog.txtGeneral": "Агульны", "SSE.Views.FormatSettingsDialog.txtNone": "Няма", "SSE.Views.FormatSettingsDialog.txtNumber": "Лічбавы", "SSE.Views.FormatSettingsDialog.txtPercentage": "У адсотках", "SSE.Views.FormatSettingsDialog.txtSample": "Прыклад:", "SSE.Views.FormatSettingsDialog.txtScientific": "Навуковы", "SSE.Views.FormatSettingsDialog.txtText": "Тэкст", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Да адной лічбы (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Да двух лічбаў (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Да трох лічбаў (131/135)", "SSE.Views.FormulaDialog.sDescription": "Апісанне", "SSE.Views.FormulaDialog.textGroupDescription": "Абраць групу функцый", "SSE.Views.FormulaDialog.textListDescription": "Абраць функцыю", "SSE.Views.FormulaDialog.txtRecommended": "Рэкамендаваныя", "SSE.Views.FormulaDialog.txtSearch": "По<PERSON><PERSON>к", "SSE.Views.FormulaDialog.txtTitle": "Уставіць функцыю", "SSE.Views.FormulaTab.textAutomatic": "Аўтаматычна", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Падлік бягучага аркуша", "SSE.Views.FormulaTab.textCalculateWorkbook": "Пад<PERSON><PERSON><PERSON> кнігі", "SSE.Views.FormulaTab.textManual": "Уласнаручна", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Падлік усёй кнігі", "SSE.Views.FormulaTab.txtAdditional": "Дадаткова", "SSE.Views.FormulaTab.txtAutosum": "Аўтасума", "SSE.Views.FormulaTab.txtAutosumTip": "Сума", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Функцыя", "SSE.Views.FormulaTab.txtFormulaTip": "Уставіць функцыю", "SSE.Views.FormulaTab.txtMore": "Іншыя функцыі", "SSE.Views.FormulaTab.txtRecent": "Апошнія выкарыстаныя", "SSE.Views.FormulaWizard.textAny": "любы", "SSE.Views.FormulaWizard.textArgument": "Аргумент", "SSE.Views.FormulaWizard.textFunction": "Функцыя", "SSE.Views.FormulaWizard.textFunctionRes": "Вынік функцыі", "SSE.Views.FormulaWizard.textHelp": "Даведка па гэтай функцыі", "SSE.Views.FormulaWizard.textLogical": "лагічныя", "SSE.Views.FormulaWizard.textNoArgs": "У гэтай функцыі няма аргументаў", "SSE.Views.FormulaWizard.textNumber": "лічбавы", "SSE.Views.FormulaWizard.textRef": "спасылка", "SSE.Views.FormulaWizard.textText": "Тэкст", "SSE.Views.FormulaWizard.textTitle": "Аргументы функцыі", "SSE.Views.FormulaWizard.textValue": "Вынік формулы", "SSE.Views.HeaderFooterDialog.textAlign": "Выраўнаваць адносна палёў старонкі", "SSE.Views.HeaderFooterDialog.textAll": "Усе старонкі", "SSE.Views.HeaderFooterDialog.textBold": "Тоўсты", "SSE.Views.HeaderFooterDialog.textCenter": "Па цэнтры", "SSE.Views.HeaderFooterDialog.textColor": "Колер тэксту", "SSE.Views.HeaderFooterDialog.textDate": "Дата", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Асобны для першай старонкі", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Асобныя для цотных і няцотных", "SSE.Views.HeaderFooterDialog.textEven": "Цотная старонка", "SSE.Views.HeaderFooterDialog.textFileName": "Назва файла", "SSE.Views.HeaderFooterDialog.textFirst": "Першая старонка", "SSE.Views.HeaderFooterDialog.textFooter": "Ніжні калонтытул", "SSE.Views.HeaderFooterDialog.textHeader": "Верхні калонтытул", "SSE.Views.HeaderFooterDialog.textInsert": "Уставіць", "SSE.Views.HeaderFooterDialog.textItalic": "Курсіў", "SSE.Views.HeaderFooterDialog.textLeft": "Злева", "SSE.Views.HeaderFooterDialog.textMaxError": "Уведзены занадта доўгі тэкставы радок. Паменшыце колькасць знакаў.", "SSE.Views.HeaderFooterDialog.textNewColor": "Адвольны колер", "SSE.Views.HeaderFooterDialog.textOdd": "Няцотная старонка", "SSE.Views.HeaderFooterDialog.textPageCount": "Колькасць старонак", "SSE.Views.HeaderFooterDialog.textPageNum": "Нумар старон<PERSON>і", "SSE.Views.HeaderFooterDialog.textPresets": "Перадналады", "SSE.Views.HeaderFooterDialog.textRight": "Справа", "SSE.Views.HeaderFooterDialog.textScale": "Змяняць маштаб разам з дакументам", "SSE.Views.HeaderFooterDialog.textSheet": "Назва аркуша", "SSE.Views.HeaderFooterDialog.textStrikeout": "Закрэслены", "SSE.Views.HeaderFooterDialog.textSubscript": "Падрадковыя", "SSE.Views.HeaderFooterDialog.textSuperscript": "Надрадковыя", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Налады калонтытулаў", "SSE.Views.HeaderFooterDialog.textUnderline": "Падкрэслены", "SSE.Views.HeaderFooterDialog.tipFontName": "Шры<PERSON>т", "SSE.Views.HeaderFooterDialog.tipFontSize": "Памер шрыфту", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Паказваць", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Звязаць з", "SSE.Views.HyperlinkSettingsDialog.strRange": "Дыяпазон", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Капіяваць", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Абраны дыяпазон", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Увядзіце сюды подпіс", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Увядзіце сюды спасылку", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Увядзіце сюды падказку", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Вонкавая спасылка", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Атрымаць спасылку", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Унутраны дыяпазон даных", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.HyperlinkSettingsDialog.textNames": "Пэўныя назвы", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Абраць даныя", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Арк<PERSON><PERSON>ы", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Тэкст падказкі", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Налады гіперспасылкі", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Гэтае поле павінна быць URL-адрасам фармату \"http://www.example.com\"", "SSE.Views.ImageSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ImageSettings.textCrop": "Абрэзаць", "SSE.Views.ImageSettings.textCropFill": "Заліўка", "SSE.Views.ImageSettings.textCropFit": "Умясціць", "SSE.Views.ImageSettings.textEdit": "Рэдагаваць", "SSE.Views.ImageSettings.textEditObject": "Рэдагаваць аб’ект", "SSE.Views.ImageSettings.textFlip": "Пераварочванне", "SSE.Views.ImageSettings.textFromFile": "З файла", "SSE.Views.ImageSettings.textFromStorage": "Са сховішча", "SSE.Views.ImageSettings.textFromUrl": "Па URL", "SSE.Views.ImageSettings.textHeight": "Вышыня", "SSE.Views.ImageSettings.textHint270": "Павярнуць улева на 90°", "SSE.Views.ImageSettings.textHint90": "Павярнуць управа на 90°", "SSE.Views.ImageSettings.textHintFlipH": "Адлюстраваць па гарызанталі", "SSE.Views.ImageSettings.textHintFlipV": "Адлюстраваць па вертыкалі", "SSE.Views.ImageSettings.textInsert": "Замяніць выяву", "SSE.Views.ImageSettings.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.ImageSettings.textOriginalSize": "Актуальны памер", "SSE.Views.ImageSettings.textRotate90": "Павярнуць на 90°", "SSE.Views.ImageSettings.textRotation": "Паварочванне", "SSE.Views.ImageSettings.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "Шырыня", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.ImageSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.ImageSettingsAdvanced.textAngle": "Вугал", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Перавернута", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Па гарызанталі", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.ImageSettingsAdvanced.textRotation": "Паварочванне", "SSE.Views.ImageSettingsAdvanced.textSnap": "Далучэнне да ячэйкі", "SSE.Views.ImageSettingsAdvanced.textTitle": "Выява - дадатковыя налады", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.ImageSettingsAdvanced.textVertically": "Па вертыкалі", "SSE.Views.LeftMenu.tipAbout": "Пра праграму", "SSE.Views.LeftMenu.tipChat": "Размова", "SSE.Views.LeftMenu.tipComments": "Каментары", "SSE.Views.LeftMenu.tipFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Убудовы", "SSE.Views.LeftMenu.tipSearch": "По<PERSON><PERSON>к", "SSE.Views.LeftMenu.tipSpellcheck": "Праверка правапісу", "SSE.Views.LeftMenu.tipSupport": "Зваротная сувязь і падтрымка", "SSE.Views.LeftMenu.txtDeveloper": "РЭЖЫМ РАСПРАЦОЎШЧЫКА", "SSE.Views.LeftMenu.txtTrial": "ПРОБНЫ РЭЖЫМ", "SSE.Views.MainSettingsPrint.okButtonText": "Захаваць", "SSE.Views.MainSettingsPrint.strBottom": "Знізу", "SSE.Views.MainSettingsPrint.strLandscape": "Альбомная", "SSE.Views.MainSettingsPrint.strLeft": "Злева", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Кніжная", "SSE.Views.MainSettingsPrint.strPrint": "Друкаванне", "SSE.Views.MainSettingsPrint.strPrintTitles": "Друкаваць загалоўкі", "SSE.Views.MainSettingsPrint.strRight": "Справа", "SSE.Views.MainSettingsPrint.strTop": "Уверсе", "SSE.Views.MainSettingsPrint.textActualSize": "Актуальны памер", "SSE.Views.MainSettingsPrint.textCustom": "Адвольны", "SSE.Views.MainSettingsPrint.textCustomOptions": "Адвольныя параметры", "SSE.Views.MainSettingsPrint.textFitCols": "Умясціць усе слупкі на адной старонцы", "SSE.Views.MainSettingsPrint.textFitPage": "Умясціць аркуш на адной старонцы", "SSE.Views.MainSettingsPrint.textFitRows": "Умясціць усе радкі на адной старонцы", "SSE.Views.MainSettingsPrint.textPageOrientation": "Арыентацыя старонкі", "SSE.Views.MainSettingsPrint.textPageScaling": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Памер старон<PERSON>і", "SSE.Views.MainSettingsPrint.textPrintGrid": "Друкаваць сетку", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Друкаваць загалоўкі радкоў і слупкоў", "SSE.Views.MainSettingsPrint.textRepeat": "Паўтараць…", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Паўтараць слупкі злева", "SSE.Views.MainSettingsPrint.textRepeatTop": "Паўтараць радкі зверху", "SSE.Views.MainSettingsPrint.textSettings": "Налады для", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "На дадзены момант немагчыма рэдагаваць існыя названыя дыяпазоны і ствараць новыя,<br>бо некаторыя з іх рэдагуюцца.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Пэўная назва", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Увага", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Кніга", "SSE.Views.NamedRangeEditDlg.textDataRange": "Дыяпазон даных", "SSE.Views.NamedRangeEditDlg.textExistName": "ПАМЫЛКА! Дыяпазон з такой назвай ужо існуе", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Назва мусіць пачынацца з літары альбо ніжняга падкрэслівання і не павінна змяшчаць непрыдатных сімвалаў.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ПАМЫЛКА! Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.NamedRangeEditDlg.textName": "Назва", "SSE.Views.NamedRangeEditDlg.textReservedName": "Формулы ў ячэйках ужо змяшчаюць назву, якую вы спрабуеце выкарыстоўваць. Выкарыстайце іншую назву.", "SSE.Views.NamedRangeEditDlg.textScope": "Вобласць", "SSE.Views.NamedRangeEditDlg.textSelectData": "Абраць даныя", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Рэдагаваць назву", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Новая назва", "SSE.Views.NamedRangePasteDlg.textNames": "Названыя дыяпазоны", "SSE.Views.NamedRangePasteDlg.txtTitle": "Уставіць назву", "SSE.Views.NameManagerDlg.closeButtonText": "Закрыць", "SSE.Views.NameManagerDlg.guestText": "Госць", "SSE.Views.NameManagerDlg.textDataRange": "Дыяпазон даных", "SSE.Views.NameManagerDlg.textDelete": "Выдаліць", "SSE.Views.NameManagerDlg.textEdit": "Рэдагаваць", "SSE.Views.NameManagerDlg.textEmpty": "Яшчэ не створана ніводнага названага дыяпазону.<br>Стварыце хоць адзін названы дыяпазон, і ён з’явіцца ў гэтым полі.", "SSE.Views.NameManagerDlg.textFilter": "Фільтр", "SSE.Views.NameManagerDlg.textFilterAll": "Усе", "SSE.Views.NameManagerDlg.textFilterDefNames": "Пэўныя назвы", "SSE.Views.NameManagerDlg.textFilterSheet": "Назвы на аркушы", "SSE.Views.NameManagerDlg.textFilterTableNames": "Назвы табліц", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Назвы ў кнізе", "SSE.Views.NameManagerDlg.textNew": "Новы", "SSE.Views.NameManagerDlg.textnoNames": "Не знойдзена названых дыяпазонаў, якія адпавядаюць фільтру. ", "SSE.Views.NameManagerDlg.textRanges": "Названыя дыяпазоны", "SSE.Views.NameManagerDlg.textScope": "Вобласць", "SSE.Views.NameManagerDlg.textWorkbook": "Кніга", "SSE.Views.NameManagerDlg.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.NameManagerDlg.txtTitle": "Кіраўнік назваў", "SSE.Views.PageMarginsDialog.textBottom": "Ніжняе", "SSE.Views.PageMarginsDialog.textLeft": "Левае", "SSE.Views.PageMarginsDialog.textRight": "Правае", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON>а<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Верхняе", "SSE.Views.ParagraphSettings.strLineHeight": "Прамежак паміж радкамі", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Прамежак паміж абзацамі", "SSE.Views.ParagraphSettings.strSpacingAfter": "Пасля", "SSE.Views.ParagraphSettings.strSpacingBefore": "Пер<PERSON>д", "SSE.Views.ParagraphSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ParagraphSettings.textAt": "Значэнне", "SSE.Views.ParagraphSettings.textAtLeast": "Мін<PERSON><PERSON>ум", "SSE.Views.ParagraphSettings.textAuto": "Множнік", "SSE.Views.ParagraphSettings.textExact": "Дак<PERSON><PERSON>дна", "SSE.Views.ParagraphSettings.txtAutoText": "Аўта", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Вызначаныя табуляцыі з’явяцца ў гэтым полі", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Усе ў верхнім рэгістры", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Падвойнае закрэсліванне", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Водступы", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Злева", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Прамежак паміж радкамі", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Справа", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Пасля", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Пер<PERSON>д", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Адмысловы", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "На", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Шры<PERSON>т", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Водступы і інтэрвалы", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Малыя прапісныя", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Прамежак", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Закрэсліванне", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Падрадковыя", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Надрадковыя", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Табуляцыя", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Выраўноўванне", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Множнік", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Прамежак паміж знакамі", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Прадвызначана", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Эфекты", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Дак<PERSON><PERSON>дна", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Першы радок", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Выступ", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Па шырыні", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(няма)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Выдаліць", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Выдаліць усе", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Вызначыць", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Па цэнтры", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Па леваму краю", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Пазіцыя", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Па праваму краю", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Абзац - дадатковыя налады", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Аўта", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "не заканчваецца на", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "Змяшчае", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "не змяшчае", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "паміж", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "не паміж", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "не роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "больш за", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "больш альбо роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "менш за", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "менш альбо роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "Пачынаецца з", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "не пачынаецца з", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "заканчваецца на", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Паказваць элементы з адмецінамі:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Паказваць элементы, у якіх:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Выкарыстоўвайце знак ? замест любога асобнага сімвала", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Выкарыстоўвайце знак * замест любой паслядоўнасці сімвалаў", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "і", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Фільтр адмецін", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Фільтр значэнняў", "SSE.Views.PivotGroupDialog.textBy": "На", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.PivotSettings.textColumns": "Слупкі", "SSE.Views.PivotSettings.textFields": "Абра<PERSON>ь палі", "SSE.Views.PivotSettings.textFilters": "Фільтры", "SSE.Views.PivotSettings.textRows": "Радкі", "SSE.Views.PivotSettings.textValues": "Значэнні", "SSE.Views.PivotSettings.txtAddColumn": "Дада<PERSON>ь у слупкі", "SSE.Views.PivotSettings.txtAddFilter": "Дадаць у фільтры", "SSE.Views.PivotSettings.txtAddRow": "Дада<PERSON>ь у радкі", "SSE.Views.PivotSettings.txtAddValues": "Дада<PERSON>ь у значэнні", "SSE.Views.PivotSettings.txtFieldSettings": "Налады палёў", "SSE.Views.PivotSettings.txtMoveBegin": "Перамясціць у пачатак", "SSE.Views.PivotSettings.txtMoveColumn": "Перамясціць у слупкі", "SSE.Views.PivotSettings.txtMoveDown": "Перамясціць уніз", "SSE.Views.PivotSettings.txtMoveEnd": "Перамясціць у канец", "SSE.Views.PivotSettings.txtMoveFilter": "Перамясціць у фільтры", "SSE.Views.PivotSettings.txtMoveRow": "Перамясціць у радкі", "SSE.Views.PivotSettings.txtMoveUp": "Перамясціць уверх", "SSE.Views.PivotSettings.txtMoveValues": "Перамясціць у значэнні", "SSE.Views.PivotSettings.txtRemove": "Выдаліць поле", "SSE.Views.PivotSettingsAdvanced.strLayout": "Назва і макет", "SSE.Views.PivotSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Дыяпазон даных", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Крыніца даных", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Паказваць палі ў вобласці фільтра справаздачы", "SSE.Views.PivotSettingsAdvanced.textDown": "Уніз, пасля ўправа", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Агульныя вынікі", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Загалоўкі палёў", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.PivotSettingsAdvanced.textOver": "Управа, пасля ўніз", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Абраць даныя", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Паказваць для слупкоў", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Паказваць загалоўкі палёў для радкоў і слупкоў", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Паказваць для радкоў", "SSE.Views.PivotSettingsAdvanced.textTitle": "Зводная табліца - дадатковыя налады", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Колькасць палёў фільтра справаздачы ў слупку", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Колькасць палёў фільтра справаздачы ў радку", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.PivotSettingsAdvanced.txtName": "Назва", "SSE.Views.PivotTable.capBlankRows": "Пустыя радкі", "SSE.Views.PivotTable.capGrandTotals": "Агульныя вынікі", "SSE.Views.PivotTable.capLayout": "Макет справаздачы", "SSE.Views.PivotTable.capSubtotals": "Прамежкавыя вынікі", "SSE.Views.PivotTable.mniBottomSubtotals": "Паказваць усе прамежкавыя вынікі ўнізе групы", "SSE.Views.PivotTable.mniInsertBlankLine": "Устаўляць пусты радок пасля кожнага элемента", "SSE.Views.PivotTable.mniLayoutCompact": "Паказваць у сціслай форме", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Не паўтараць усе адмеціны элементаў", "SSE.Views.PivotTable.mniLayoutOutline": "Паказваць у форме структуры", "SSE.Views.PivotTable.mniLayoutRepeat": "Паўтараць усе адмеціны элементаў", "SSE.Views.PivotTable.mniLayoutTabular": "Паказваць у форме табліцы", "SSE.Views.PivotTable.mniNoSubtotals": "Не паказваць прамежкавыя вынікі", "SSE.Views.PivotTable.mniOffTotals": "Выключыць для радкоў і слупкоў", "SSE.Views.PivotTable.mniOnColumnsTotals": "Уключыць толькі для слупкоў", "SSE.Views.PivotTable.mniOnRowsTotals": "Уключыць толькі для радкоў", "SSE.Views.PivotTable.mniOnTotals": "Уключыць для слупкоў і радкоў", "SSE.Views.PivotTable.mniRemoveBlankLine": "Выдаліць пусты радок пасля кожнага элемента", "SSE.Views.PivotTable.mniTopSubtotals": "Паказваць усе прамежкавыя вынікі ўверсе групы", "SSE.Views.PivotTable.textColBanded": "Чаргаваць слупкі", "SSE.Views.PivotTable.textColHeader": "Загалоўкі слупкоў", "SSE.Views.PivotTable.textRowBanded": "Чаргаваць радкі", "SSE.Views.PivotTable.textRowHeader": "Загалоўкі радкоў", "SSE.Views.PivotTable.tipCreatePivot": "Уставіць зводную табліцу", "SSE.Views.PivotTable.tipGrandTotals": "Паказаць альбо схаваць агульныя вынікі", "SSE.Views.PivotTable.tipRefresh": "Абнавіць інфармацыю з крыніцы даных", "SSE.Views.PivotTable.tipSelect": "Абраць усю зводную табліцу", "SSE.Views.PivotTable.tipSubtotals": "Паказаць альбо схаваць прамежкавыя вынікі", "SSE.Views.PivotTable.txtCreate": "Уставіць табліцу", "SSE.Views.PivotTable.txtPivotTable": "Зводная табліца", "SSE.Views.PivotTable.txtRefresh": "Абнавіць", "SSE.Views.PivotTable.txtSelect": "Абраць", "SSE.Views.PrintSettings.btnDownload": "Захаваць і спампаваць", "SSE.Views.PrintSettings.btnPrint": "Захаваць і друкаваць", "SSE.Views.PrintSettings.strBottom": "Знізу", "SSE.Views.PrintSettings.strLandscape": "Альбомная", "SSE.Views.PrintSettings.strLeft": "Злева", "SSE.Views.PrintSettings.strMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Кніжная", "SSE.Views.PrintSettings.strPrint": "Друкаванне", "SSE.Views.PrintSettings.strPrintTitles": "Друкаваць загалоўкі", "SSE.Views.PrintSettings.strRight": "Справа", "SSE.Views.PrintSettings.strShow": "Паказаць", "SSE.Views.PrintSettings.strTop": "Уверсе", "SSE.Views.PrintSettings.textActualSize": "Актуальны памер", "SSE.Views.PrintSettings.textAllSheets": "Усе аркушы", "SSE.Views.PrintSettings.textCurrentSheet": "Бягучы аркуш", "SSE.Views.PrintSettings.textCustom": "Адвольны", "SSE.Views.PrintSettings.textCustomOptions": "Адвольныя параметры", "SSE.Views.PrintSettings.textFitCols": "Умясціць усе слупкі на адной старонцы", "SSE.Views.PrintSettings.textFitPage": "Умясціць аркуш на адной старонцы", "SSE.Views.PrintSettings.textFitRows": "Умясціць усе радкі на адной старонцы", "SSE.Views.PrintSettings.textHideDetails": "Схаваць падрабязнасці", "SSE.Views.PrintSettings.textIgnore": "Ігнараваць вобласць друкавання", "SSE.Views.PrintSettings.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageOrientation": "Арыентацыя старонкі", "SSE.Views.PrintSettings.textPageScaling": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Памер старон<PERSON>і", "SSE.Views.PrintSettings.textPrintGrid": "Друкаваць сетку", "SSE.Views.PrintSettings.textPrintHeadings": "Друкаваць загалоўкі радкоў і слупкоў", "SSE.Views.PrintSettings.textPrintRange": "Дыяпазон друкавання", "SSE.Views.PrintSettings.textRange": "Дыяпазон", "SSE.Views.PrintSettings.textRepeat": "Паўтараць…", "SSE.Views.PrintSettings.textRepeatLeft": "Паўтараць слупкі злева", "SSE.Views.PrintSettings.textRepeatTop": "Паўтараць радкі зверху", "SSE.Views.PrintSettings.textSelection": "Вылучанае", "SSE.Views.PrintSettings.textSettings": "Налады аркуша", "SSE.Views.PrintSettings.textShowDetails": "Паказаць падрабязнасці", "SSE.Views.PrintSettings.textShowGrid": "Паказваць лініі сеткі", "SSE.Views.PrintSettings.textShowHeadings": "Паказаць загалоўкі радкоў і слупкоў", "SSE.Views.PrintSettings.textTitle": "Налады друку", "SSE.Views.PrintSettings.textTitlePDF": "Налады PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "Першы слупок", "SSE.Views.PrintTitlesDialog.textFirstRow": "Першы радок", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Замацаваныя слупкі", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Замацаваныя радкі", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.PrintTitlesDialog.textLeft": "Паўтараць слупкі злева", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Не паўтараць", "SSE.Views.PrintTitlesDialog.textRepeat": "Паўтараць…", "SSE.Views.PrintTitlesDialog.textSelectRange": "Абраць дыяпазон", "SSE.Views.PrintTitlesDialog.textTitle": "Друкаваць загалоўкі", "SSE.Views.PrintTitlesDialog.textTop": "Паўтараць радкі зверху", "SSE.Views.PrintWithPreview.txtActualSize": "Актуальны памер", "SSE.Views.PrintWithPreview.txtAllSheets": "Усе аркушы", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Бягучы аркуш", "SSE.Views.PrintWithPreview.txtCustom": "Адвольны", "SSE.Views.PrintWithPreview.txtCustomOptions": "Адвольныя параметры", "SSE.Views.PrintWithPreview.txtFitCols": "Умясціць усе слупкі на адной старонцы", "SSE.Views.PrintWithPreview.txtFitPage": "Умясціць аркуш на адной старонцы", "SSE.Views.PrintWithPreview.txtFitRows": "Умясціць усе радкі на адной старонцы", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Налады калонтытулаў", "SSE.Views.PrintWithPreview.txtIgnore": "Ігнараваць вобласць друкавання", "SSE.Views.PrintWithPreview.txtLandscape": "Альбомная", "SSE.Views.PrintWithPreview.txtLeft": "Злева", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.ProtectDialog.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Паролі адрозніваюцца", "SSE.Views.ProtectDialog.txtInsCols": "Уставіць слупкі", "SSE.Views.ProtectRangesDlg.guestText": "Госць", "SSE.Views.ProtectRangesDlg.textDelete": "Выдаліць", "SSE.Views.ProtectRangesDlg.textEdit": "Рэдагаваць", "SSE.Views.ProtectRangesDlg.textNew": "Новы", "SSE.Views.ProtectRangesDlg.txtNo": "Не", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Слупкі", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Каб выдаліць паўторныя значэнні, абярыце адзін альбо некалькі слупкоў, якія іх змяшчаюць.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Мае даныя змяшчаюць загалоўкі", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Вылучыць усе", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Выдаліць паўторы", "SSE.Views.RightMenu.txtCellSettings": "Налады ячэйкі", "SSE.Views.RightMenu.txtChartSettings": "Налады дыяграмы", "SSE.Views.RightMenu.txtImageSettings": "Налады выявы", "SSE.Views.RightMenu.txtParagraphSettings": "Налады абзаца", "SSE.Views.RightMenu.txtPivotSettings": "Налады зводнай табліцы", "SSE.Views.RightMenu.txtSettings": "Асноўныя налады", "SSE.Views.RightMenu.txtShapeSettings": "Налады фігуры", "SSE.Views.RightMenu.txtSignatureSettings": "Налады подпісу", "SSE.Views.RightMenu.txtSlicerSettings": "Налады зводкі", "SSE.Views.RightMenu.txtSparklineSettings": "Налады спарклайна", "SSE.Views.RightMenu.txtTableSettings": "Налады табліцы", "SSE.Views.RightMenu.txtTextArtSettings": "Налады Text Art", "SSE.Views.ScaleDialog.textAuto": "Аўта", "SSE.Views.ScaleDialog.textError": "Уведзена няправільнае значэнне.", "SSE.Views.ScaleDialog.textFewPages": "Старонкі", "SSE.Views.ScaleDialog.textFitTo": "Запоўніць не больш чым", "SSE.Views.ScaleDialog.textHeight": "Вышыня", "SSE.Views.ScaleDialog.textManyPages": "старонкі", "SSE.Views.ScaleDialog.textOnePage": "Старонка", "SSE.Views.ScaleDialog.textScaleTo": "Вызначыць", "SSE.Views.ScaleDialog.textTitle": "Налады маштабу", "SSE.Views.ScaleDialog.textWidth": "Шырыня", "SSE.Views.SetValueDialog.txtMaxText": "Максімальнае значэнне для гэтага поля - {0}", "SSE.Views.SetValueDialog.txtMinText": "Мінімальнае значэнне для гэтага поля - {0}", "SSE.Views.ShapeSettings.strBackground": "Колер фону", "SSE.Views.ShapeSettings.strChange": "Змяніць аўтафігуру", "SSE.Views.ShapeSettings.strColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Заліўка", "SSE.Views.ShapeSettings.strForeground": "Колер пярэдняга плану", "SSE.Views.ShapeSettings.strPattern": "Узор", "SSE.Views.ShapeSettings.strShadow": "Паказваць цень", "SSE.Views.ShapeSettings.strSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "Абвядзенне", "SSE.Views.ShapeSettings.strTransparency": "Непразрыстасць", "SSE.Views.ShapeSettings.strType": "Тып", "SSE.Views.ShapeSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ShapeSettings.textAngle": "Вугал", "SSE.Views.ShapeSettings.textBorderSizeErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 0 да 1584 пунктаў.", "SSE.Views.ShapeSettings.textColor": "Заліўка колерам", "SSE.Views.ShapeSettings.textDirection": "Напрамак", "SSE.Views.ShapeSettings.textEmptyPattern": "Без узору", "SSE.Views.ShapeSettings.textFlip": "Пераварочванне", "SSE.Views.ShapeSettings.textFromFile": "З файла", "SSE.Views.ShapeSettings.textFromStorage": "Са сховішча", "SSE.Views.ShapeSettings.textFromUrl": "Па URL", "SSE.Views.ShapeSettings.textGradient": "Град<PERSON><PERSON><PERSON>т", "SSE.Views.ShapeSettings.textGradientFill": "Градыентная заліўка", "SSE.Views.ShapeSettings.textHint270": "Павярнуць улева на 90°", "SSE.Views.ShapeSettings.textHint90": "Павярнуць управа на 90°", "SSE.Views.ShapeSettings.textHintFlipH": "Адлюстраваць па гарызанталі", "SSE.Views.ShapeSettings.textHintFlipV": "Адлюстраваць па вертыкалі", "SSE.Views.ShapeSettings.textImageTexture": "Малюнак альбо тэкстура", "SSE.Views.ShapeSettings.textLinear": "Лінейны", "SSE.Views.ShapeSettings.textNoFill": "Без заліўкі", "SSE.Views.ShapeSettings.textOriginalSize": "Зыходны памер", "SSE.Views.ShapeSettings.textPatternFill": "Узор", "SSE.Views.ShapeSettings.textPosition": "Пасада", "SSE.Views.ShapeSettings.textRadial": "Радыяльны", "SSE.Views.ShapeSettings.textRotate90": "Павярнуць на 90°", "SSE.Views.ShapeSettings.textRotation": "Паварочванне", "SSE.Views.ShapeSettings.textSelectImage": "Абраць выяву", "SSE.Views.ShapeSettings.textSelectTexture": "Абраць", "SSE.Views.ShapeSettings.textStretch": "Расцягванне", "SSE.Views.ShapeSettings.textStyle": "Стыль", "SSE.Views.ShapeSettings.textTexture": "З тэкстуры", "SSE.Views.ShapeSettings.textTile": "Плітка", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Дадаць кропку градыента", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Выдаліць кропку градыента", "SSE.Views.ShapeSettings.txtBrownPaper": "Карычневая папера", "SSE.Views.ShapeSettings.txtCanvas": "Палатно", "SSE.Views.ShapeSettings.txtCarton": "Карт<PERSON>н", "SSE.Views.ShapeSettings.txtDarkFabric": "Цёмная тканіна", "SSE.Views.ShapeSettings.txtGrain": "Пясок", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Шэрая папера", "SSE.Views.ShapeSettings.txtKnit": "Вязанне", "SSE.Views.ShapeSettings.txtLeather": "Скура", "SSE.Views.ShapeSettings.txtNoBorders": "Без абвядзення", "SSE.Views.ShapeSettings.txtPapyrus": "Па<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Дрэва", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Слупкі", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Поле вакол тэксту", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Вугал", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Стрэлкі", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Аўтазапаўненне", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Першапачатковы памер", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Першапачатковы стыль", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Нахілены", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Знізу", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Тып канчатка", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Колькасць слупкоў", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Канцавы памер", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Канцавы стыль", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Плоскі", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Перавернута", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Вышыня", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Па гарызанталі", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Тып аб’яднання", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Злева", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Стыль лініі", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Непасрэдны", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Дазволіць перапаўненне фігуры тэкстам", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Падладжваць памер фігуры пад тэкст", "SSE.Views.ShapeSettingsAdvanced.textRight": "Справа", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Паварочванне", "SSE.Views.ShapeSettingsAdvanced.textRound": "Скруглены", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Далучэнне да ячэйкі", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Прамежак паміж слупкамі", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Квадра<PERSON>ны", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Над<PERSON><PERSON>с", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Фігура - дадатковыя налады", "SSE.Views.ShapeSettingsAdvanced.textTop": "Уверсе", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Па вертыкалі", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Лініі і стрэлкі", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Шырыня", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Увага", "SSE.Views.SignatureSettings.strDelete": "Выдаліць подпіс", "SSE.Views.SignatureSettings.strDetails": "Падрабязнасці подпісу", "SSE.Views.SignatureSettings.strInvalid": "Хібныя подпісы", "SSE.Views.SignatureSettings.strRequested": "Запытаныя подпісы", "SSE.Views.SignatureSettings.strSetup": "Наладжванне подпісу", "SSE.Views.SignatureSettings.strSign": "Падпісаць", "SSE.Views.SignatureSettings.strSignature": "Под<PERSON><PERSON>с", "SSE.Views.SignatureSettings.strSigner": "Падпіс<PERSON>л<PERSON>н<PERSON>к", "SSE.Views.SignatureSettings.strValid": "Дзейныя подпісы", "SSE.Views.SignatureSettings.txtContinueEditing": "Рэдагаваць усё роўна", "SSE.Views.SignatureSettings.txtEditWarning": "Пры рэдагаванні табліцы выдаляцца подпісы.<br>Працягнуць?", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Гэтую табліцу неабходна падпісаць.", "SSE.Views.SignatureSettings.txtSigned": "У табліцу былі дададзеныя дзейныя подпісы. Табліца абароненая ад рэдагавання.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Некаторыя з лічбавых подпісаў электроннай табліцы хібныя альбо іх немагчыма праверыць. Табліца абароненая ад рэдагавання.", "SSE.Views.SlicerAddDialog.textColumns": "Слупкі", "SSE.Views.SlicerAddDialog.txtTitle": "Устаўка зводак", "SSE.Views.SlicerSettings.strHideNoData": "Схаваць элементы без даных", "SSE.Views.SlicerSettings.strIndNoData": "Адзначаць пустыя элементы", "SSE.Views.SlicerSettings.strShowDel": "Паказваць элементы, якія былі выдаленыя з крыніцы даных", "SSE.Views.SlicerSettings.strShowNoData": "Паказваць пустыя элементы апошнімі", "SSE.Views.SlicerSettings.strSorting": "Сартаванне і фільтрацыя", "SSE.Views.SlicerSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.SlicerSettings.textAsc": "Па ўзрастанні", "SSE.Views.SlicerSettings.textAZ": "Ад А да Я", "SSE.Views.SlicerSettings.textButtons": "Кнопкі", "SSE.Views.SlicerSettings.textColumns": "Слупкі", "SSE.Views.SlicerSettings.textDesc": "Па памяншэнні", "SSE.Views.SlicerSettings.textHeight": "Вышыня", "SSE.Views.SlicerSettings.textHor": "Гарызантальна", "SSE.Views.SlicerSettings.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.SlicerSettings.textLargeSmall": "ад большага да меншага", "SSE.Views.SlicerSettings.textLock": "Адключыць змену памеру альбо перамяшчэнне", "SSE.Views.SlicerSettings.textNewOld": "ад новых да старых", "SSE.Views.SlicerSettings.textOldNew": "ад старых да новых", "SSE.Views.SlicerSettings.textPosition": "Пасада", "SSE.Views.SlicerSettings.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "ад малога да вялікага", "SSE.Views.SlicerSettings.textStyle": "Стыль", "SSE.Views.SlicerSettings.textVert": "Вертыкальна", "SSE.Views.SlicerSettings.textWidth": "Шырыня", "SSE.Views.SlicerSettings.textZA": "Ад Я да А", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Кнопкі", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Слупкі", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Вышыня", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Схаваць элементы без даных", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Адзначаць пустыя элементы", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Спасылкі", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Паказваць элементы, якія былі выдаленыя з крыніцы даных", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Паказваць загаловак", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Паказваць пустыя элементы апошнімі", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Сартаванне і фільтрацыя", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Стыль", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Стыль і памер", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Шырыня", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Назва", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Па ўзрастанні", "SSE.Views.SlicerSettingsAdvanced.textAZ": "Ад А да Я", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Па памяншэнні", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Назва для ўжывання ў формулах", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Загаловак", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "ад большага да меншага", "SSE.Views.SlicerSettingsAdvanced.textName": "Назва", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "ад новых да старых", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "ад старых да новых", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "ад малога да вялікага", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Далучэнне да ячэйкі", "SSE.Views.SlicerSettingsAdvanced.textSort": "Сартаванне", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Назва крыніцы", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Зводка - дадатковыя параметры", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.SlicerSettingsAdvanced.textZA": "Ад Я да А", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.SortDialog.errorEmpty": "Для кожнай умовы сартавання мусіць быць вызначаны слупок і радок.", "SSE.Views.SortDialog.errorMoreOneCol": "Вылучана некалькі слупкоў.", "SSE.Views.SortDialog.errorMoreOneRow": "Вылучана некалькі радкоў.", "SSE.Views.SortDialog.errorNotOriginalCol": "Абраны слупок не ўваходзіць у папярэдне абраны дыяпазон.", "SSE.Views.SortDialog.errorNotOriginalRow": "Абраны радок не належыць папярэдне абранаму дыяпазону.", "SSE.Views.SortDialog.errorSameColumnColor": "Сартаванне радка альбо слупка %1 па аднаму і таму ж колеру выконваецца больш за адзін раз.<br>Выдаліце паўторныя ўмовы сартавання і паўтарыце спробу.", "SSE.Views.SortDialog.errorSameColumnValue": "Сартаванне радка альбо слупка %1 па значэнням выконваецца больш за адзін раз.<br>Выдаліце паўторныя ўмовы сартавання і паўтарыце спробу.", "SSE.Views.SortDialog.textAdd": "Дада<PERSON>ь узровень", "SSE.Views.SortDialog.textAsc": "Па ўзрастанні", "SSE.Views.SortDialog.textAuto": "Аўтаматычна", "SSE.Views.SortDialog.textAZ": "Ад А да Я", "SSE.Views.SortDialog.textBelow": "Унізе", "SSE.Views.SortDialog.textCellColor": "Ко<PERSON>ер яч<PERSON><PERSON>кі", "SSE.Views.SortDialog.textColumn": "Слупок", "SSE.Views.SortDialog.textCopy": "Скапіяваць узровень", "SSE.Views.SortDialog.textDelete": "Выдаліць узровень", "SSE.Views.SortDialog.textDesc": "Па памяншэнні", "SSE.Views.SortDialog.textDown": "Перамясціць узровень ніжэй", "SSE.Views.SortDialog.textFontColor": "Колер шрыфту", "SSE.Views.SortDialog.textLeft": "Злева", "SSE.Views.SortDialog.textMoreCols": "(Яшчэ слупкі…)", "SSE.Views.SortDialog.textMoreRows": "(Яшчэ радкі…)", "SSE.Views.SortDialog.textNone": "Няма", "SSE.Views.SortDialog.textOptions": "Параметры", "SSE.Views.SortDialog.textOrder": "Пар<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRight": "Справа", "SSE.Views.SortDialog.textRow": "Радок", "SSE.Views.SortDialog.textSort": "Сартаванне", "SSE.Views.SortDialog.textSortBy": "Парадкаваць па", "SSE.Views.SortDialog.textThenBy": "Пасля па", "SSE.Views.SortDialog.textTop": "Уверсе", "SSE.Views.SortDialog.textUp": "Перамясціць узровень вышэй", "SSE.Views.SortDialog.textValues": "Значэнні", "SSE.Views.SortDialog.textZA": "Ад Я да А", "SSE.Views.SortDialog.txtInvalidRange": "Хібны дыяпазон ячэек.", "SSE.Views.SortDialog.txtTitle": "Сартаванне", "SSE.Views.SortFilterDialog.textAsc": "Па ўзрастанні (ад А да Я)", "SSE.Views.SortFilterDialog.textDesc": "Па ўбыванні (ад Я да А)", "SSE.Views.SortFilterDialog.txtTitle": "Сартаванне", "SSE.Views.SortOptionsDialog.textCase": "Улічваць рэгістр", "SSE.Views.SortOptionsDialog.textHeaders": "Мае даныя змяшчаюць загалоўкі", "SSE.Views.SortOptionsDialog.textLeftRight": "Сартаванне злева направа", "SSE.Views.SortOptionsDialog.textOrientation": "Арыентацыя", "SSE.Views.SortOptionsDialog.textTitle": "Параметры сартавання", "SSE.Views.SortOptionsDialog.textTopBottom": "Сартаванне зверху ўніз", "SSE.Views.SpecialPasteDialog.textAdd": "Дада<PERSON>ь", "SSE.Views.SpecialPasteDialog.textAll": "Усе", "SSE.Views.SpecialPasteDialog.textBlanks": "Мінаць пустыя ячэйкі", "SSE.Views.SpecialPasteDialog.textColWidth": "Шырыня слупкоў", "SSE.Views.SpecialPasteDialog.textComments": "Каментары", "SSE.Views.SpecialPasteDialog.textDiv": "Дзяленне", "SSE.Views.SpecialPasteDialog.textFFormat": "Формулы і фарматаванне", "SSE.Views.SpecialPasteDialog.textFNFormat": "Формулы і фарматы лікаў", "SSE.Views.SpecialPasteDialog.textFormats": "Фарматы", "SSE.Views.SpecialPasteDialog.textFormulas": "Формулы", "SSE.Views.SpecialPasteDialog.textFWidth": "Формулы і шырыня слупкоў", "SSE.Views.SpecialPasteDialog.textMult": "Множанне", "SSE.Views.SpecialPasteDialog.textNone": "Няма", "SSE.Views.SpecialPasteDialog.textOperation": "Аперацыя", "SSE.Views.SpecialPasteDialog.textPaste": "Уставіць", "SSE.Views.SpecialPasteDialog.textSub": "Адніманне", "SSE.Views.SpecialPasteDialog.textTitle": "Адмысловая ўстаўка", "SSE.Views.SpecialPasteDialog.textTranspose": "Пераправіць", "SSE.Views.SpecialPasteDialog.textValues": "Значэнні", "SSE.Views.SpecialPasteDialog.textVFormat": "Значэнні і фарматаванне", "SSE.Views.SpecialPasteDialog.textVNFormat": "Значэнні і фарматы лікаў", "SSE.Views.SpecialPasteDialog.textWBorders": "Без рамкі", "SSE.Views.Spellcheck.noSuggestions": "Варыянтаў не знойдзена", "SSE.Views.Spellcheck.textChange": "Змяніць", "SSE.Views.Spellcheck.textChangeAll": "Змяніць усе", "SSE.Views.Spellcheck.textIgnore": "Мінуць", "SSE.Views.Spellcheck.textIgnoreAll": "Мінуць усе", "SSE.Views.Spellcheck.txtAddToDictionary": "Дадаць у слоўнік", "SSE.Views.Spellcheck.txtComplete": "Правапіс правераны", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Мова слоўніка", "SSE.Views.Spellcheck.txtNextTip": "Перайсці да наступнага слова", "SSE.Views.Spellcheck.txtSpelling": "Права<PERSON><PERSON>с", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Скапіяваць у канец)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Перамясціць у канец)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Уставіць перад аркушам", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Перамясціць перад аркушам", "SSE.Views.Statusbar.filteredRecordsText": "Адфільтра<PERSON><PERSON><PERSON> {0} з {1} запісаў", "SSE.Views.Statusbar.filteredText": "Рэжым фільтрацыі", "SSE.Views.Statusbar.itemAverage": "Сярэдняе", "SSE.Views.Statusbar.itemCopy": "Капіяваць", "SSE.Views.Statusbar.itemCount": "Колькасць", "SSE.Views.Statusbar.itemDelete": "Выдаліць", "SSE.Views.Statusbar.itemHidden": "Схавана", "SSE.Views.Statusbar.itemHide": "Схаваць", "SSE.Views.Statusbar.itemInsert": "Уставіць", "SSE.Views.Statusbar.itemMaximum": "Максімум", "SSE.Views.Statusbar.itemMinimum": "Мін<PERSON><PERSON>ум", "SSE.Views.Statusbar.itemMove": "Перамясціць", "SSE.Views.Statusbar.itemRename": "Змяніць назву", "SSE.Views.Statusbar.itemSum": "Сума", "SSE.Views.Statusbar.itemTabColor": "Колер укладкі", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Аркуш з такой назвай ужо існуе", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Назва аркуша не можа змяшчаць наступныя сімвалы: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Назва аркуша", "SSE.Views.Statusbar.selectAllSheets": "Абраць усе аркушы", "SSE.Views.Statusbar.textAverage": "Сярэдняе", "SSE.Views.Statusbar.textCount": "Колькасць", "SSE.Views.Statusbar.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textNewColor": "Адвольны колер", "SSE.Views.Statusbar.textNoColor": "Без колеру", "SSE.Views.Statusbar.textSum": "Сума", "SSE.Views.Statusbar.tipAddTab": "Да<PERSON><PERSON><PERSON>ь аркуш", "SSE.Views.Statusbar.tipFirst": "Прагарнуць да першага аркуша", "SSE.Views.Statusbar.tipLast": "Прагарнуць да апошняга аркуша", "SSE.Views.Statusbar.tipNext": "Прагарнуць спіс аркушаў управа", "SSE.Views.Statusbar.tipPrev": "Прагарну<PERSON>ь спіс аркушаў улева", "SSE.Views.Statusbar.tipZoomFactor": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "Павелічэнне", "SSE.Views.Statusbar.tipZoomOut": "Памяншэнне", "SSE.Views.Statusbar.ungroupSheets": "Разгрупаваць аркушы", "SSE.Views.Statusbar.zoomText": "Ма<PERSON><PERSON><PERSON>б {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Гэтую аперацыю немагчыма выканаць для абранага дыяпазону ячэек.<br>Абярыце іншы дыяпазон даных, адрозны ад існага, і паўтарыце зноў.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Не атрымалася выканаць аперацыю для абранага дыяпазону ячэек.<br>Вылучыце дыяпазон так, каб першы радок знаходзіўся на тым жа радку,<br>а выніковая табліца перакрывала бягучую.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Не атрымалася выканаць аперацыю для абранага дыяпазону ячэек.<br> Абярыце дыяпазон, які не змяшчае іншых табліц.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "У табліцах забаронена выкарыстанне формул масіву з некалькімі ячэйкамі.", "SSE.Views.TableOptionsDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.TableOptionsDialog.txtFormat": "Стварыць табліцу", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.TableOptionsDialog.txtNote": "Загалоўкі мусяць заставацца ў тым самым радку, а выніковы дыяпазон табліцы часткова супадаць з зыходным дыяпазонам.", "SSE.Views.TableOptionsDialog.txtTitle": "Загаловак", "SSE.Views.TableSettings.deleteColumnText": "Выдаліць слупок", "SSE.Views.TableSettings.deleteRowText": "Выдаліць радок", "SSE.Views.TableSettings.deleteTableText": "Выдаліць табліцу", "SSE.Views.TableSettings.insertColumnLeftText": "Уставіць слупок злева", "SSE.Views.TableSettings.insertColumnRightText": "Уставіць слупок справа", "SSE.Views.TableSettings.insertRowAboveText": "Уставіць радок вышэй", "SSE.Views.TableSettings.insertRowBelowText": "Уставіць радок ніжэй", "SSE.Views.TableSettings.notcriticalErrorTitle": "Увага", "SSE.Views.TableSettings.selectColumnText": "Абраць увесь слупок", "SSE.Views.TableSettings.selectDataText": "Вылучыць даныя слупкоў", "SSE.Views.TableSettings.selectRowText": "Абраць радок", "SSE.Views.TableSettings.selectTableText": "Абра<PERSON>ь табліцу", "SSE.Views.TableSettings.textActions": "Дзеянні з табліцамі", "SSE.Views.TableSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.TableSettings.textBanded": "Чаргаваць", "SSE.Views.TableSettings.textColumns": "Слупкі", "SSE.Views.TableSettings.textConvertRange": "Пераўтварыць у дыяпазон", "SSE.Views.TableSettings.textEdit": "Радкі і слупкі", "SSE.Views.TableSettings.textEmptyTemplate": "Без шаблонаў", "SSE.Views.TableSettings.textExistName": "ПАМЫЛКА! Дыяпазон з такой назвай ужо існуе", "SSE.Views.TableSettings.textFilter": "Кнопка фільтра", "SSE.Views.TableSettings.textFirst": "Пер<PERSON>ы", "SSE.Views.TableSettings.textHeader": "Загаловак", "SSE.Views.TableSettings.textInvalidName": "ПАМЫЛКА! Хібная назва табліцы", "SSE.Views.TableSettings.textIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.TableSettings.textLast": "Апошні", "SSE.Views.TableSettings.textLongOperation": "Працяглая аперацыя", "SSE.Views.TableSettings.textPivot": "Уставіць зводную табліцу", "SSE.Views.TableSettings.textRemDuplicates": "Выдаліць паўторы", "SSE.Views.TableSettings.textReservedName": "Формулы ў ячэйках ужо змяшчаюць назву, якую вы спрабуеце выкарыстоўваць. Выкарыстайце іншую назву.", "SSE.Views.TableSettings.textResize": "Змяніць памер табліцы", "SSE.Views.TableSettings.textRows": "Радкі", "SSE.Views.TableSettings.textSelectData": "Абраць даныя", "SSE.Views.TableSettings.textSlicer": "Уставіць зводку", "SSE.Views.TableSettings.textTableName": "Назва табліцы", "SSE.Views.TableSettings.textTemplate": "Абраць шаблон", "SSE.Views.TableSettings.textTotal": "<PERSON>г<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.warnLongOperation": "Для завяршэння аперацыі, якую вы хочаце выканаць, можа спатрэбіцца шмат часу.<br>Сапраўды хочаце працягнуць?", "SSE.Views.TableSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.TableSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.TableSettingsAdvanced.textTitle": "Табліца - дадатковыя налады", "SSE.Views.TextArtSettings.strBackground": "Колер фону", "SSE.Views.TextArtSettings.strColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Заліўка", "SSE.Views.TextArtSettings.strForeground": "Колер пярэдняга плану", "SSE.Views.TextArtSettings.strPattern": "Узор", "SSE.Views.TextArtSettings.strSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "Абвядзенне", "SSE.Views.TextArtSettings.strTransparency": "Непразрыстасць", "SSE.Views.TextArtSettings.strType": "Тып", "SSE.Views.TextArtSettings.textAngle": "Вугал", "SSE.Views.TextArtSettings.textBorderSizeErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 0 да 1584 пунктаў.", "SSE.Views.TextArtSettings.textColor": "Заліўка колерам", "SSE.Views.TextArtSettings.textDirection": "Напрамак", "SSE.Views.TextArtSettings.textEmptyPattern": "Без узору", "SSE.Views.TextArtSettings.textFromFile": "З файла", "SSE.Views.TextArtSettings.textFromUrl": "Па URL", "SSE.Views.TextArtSettings.textGradient": "Град<PERSON><PERSON><PERSON>т", "SSE.Views.TextArtSettings.textGradientFill": "Градыентная заліўка", "SSE.Views.TextArtSettings.textImageTexture": "Малюнак альбо тэкстура", "SSE.Views.TextArtSettings.textLinear": "Лінейны", "SSE.Views.TextArtSettings.textNoFill": "Без заліўкі", "SSE.Views.TextArtSettings.textPatternFill": "Узор", "SSE.Views.TextArtSettings.textPosition": "Пасада", "SSE.Views.TextArtSettings.textRadial": "Радыяльны", "SSE.Views.TextArtSettings.textSelectTexture": "Абраць", "SSE.Views.TextArtSettings.textStretch": "Расцягванне", "SSE.Views.TextArtSettings.textStyle": "Стыль", "SSE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "SSE.Views.TextArtSettings.textTexture": "З тэкстуры", "SSE.Views.TextArtSettings.textTile": "Плітка", "SSE.Views.TextArtSettings.textTransform": "Трансфармацыя", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Дадаць кропку градыента", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Выдаліць кропку градыента", "SSE.Views.TextArtSettings.txtBrownPaper": "Карычневая папера", "SSE.Views.TextArtSettings.txtCanvas": "Палатно", "SSE.Views.TextArtSettings.txtCarton": "Карт<PERSON>н", "SSE.Views.TextArtSettings.txtDarkFabric": "Цёмная тканіна", "SSE.Views.TextArtSettings.txtGrain": "Пясок", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Шэрая папера", "SSE.Views.TextArtSettings.txtKnit": "Вязанне", "SSE.Views.TextArtSettings.txtLeather": "Скура", "SSE.Views.TextArtSettings.txtNoBorders": "Без абвядзення", "SSE.Views.TextArtSettings.txtPapyrus": "Па<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Дрэва", "SSE.Views.Toolbar.capBtnAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "SSE.Views.Toolbar.capBtnColorSchemas": "Каляровая схема", "SSE.Views.Toolbar.capBtnComment": "Ка<PERSON><PERSON>н<PERSON>ар", "SSE.Views.Toolbar.capBtnInsHeader": "Калонтытулы", "SSE.Views.Toolbar.capBtnInsSlicer": "Зводка", "SSE.Views.Toolbar.capBtnInsSymbol": "Сімвал", "SSE.Views.Toolbar.capBtnMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Арыентацыя", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Вобласць друкавання", "SSE.Views.Toolbar.capBtnPrintTitles": "Друкаваць загалоўкі", "SSE.Views.Toolbar.capBtnScale": "Умясціць", "SSE.Views.Toolbar.capImgAlign": "Выраўноўванне", "SSE.Views.Toolbar.capImgBackward": "Адправіць назад", "SSE.Views.Toolbar.capImgForward": "Перамясціць уперад", "SSE.Views.Toolbar.capImgGroup": "Групаванне", "SSE.Views.Toolbar.capInsertChart": "Дыяграма", "SSE.Views.Toolbar.capInsertEquation": "Раўнанне", "SSE.Views.Toolbar.capInsertHyperlink": "Гіперспасылка", "SSE.Views.Toolbar.capInsertImage": "Выява", "SSE.Views.Toolbar.capInsertShape": "Фігура", "SSE.Views.Toolbar.capInsertTable": "Табліца", "SSE.Views.Toolbar.capInsertText": "Над<PERSON><PERSON>с", "SSE.Views.Toolbar.mniImageFromFile": "Выява з файла", "SSE.Views.Toolbar.mniImageFromStorage": "Выява са сховішча", "SSE.Views.Toolbar.mniImageFromUrl": "Выява па URL", "SSE.Views.Toolbar.textAddPrintArea": "Дадаць у вобласць друкавання", "SSE.Views.Toolbar.textAlignBottom": "Выраўнаваць па ніжняму краю", "SSE.Views.Toolbar.textAlignCenter": "Выраўнаваць па цэнтры", "SSE.Views.Toolbar.textAlignJust": "Па шырыні", "SSE.Views.Toolbar.textAlignLeft": "Выраўнаваць па леваму краю", "SSE.Views.Toolbar.textAlignMiddle": "Выраўнаваць па сярэдзіне", "SSE.Views.Toolbar.textAlignRight": "Выраўнаваць па праваму краю", "SSE.Views.Toolbar.textAlignTop": "Выраўнаваць па верхняму краю", "SSE.Views.Toolbar.textAllBorders": "Усе межы", "SSE.Views.Toolbar.textAuto": "Аўта", "SSE.Views.Toolbar.textAutoColor": "Аўтаматычна", "SSE.Views.Toolbar.textBold": "Тоўсты", "SSE.Views.Toolbar.textBordersColor": "Колер межаў", "SSE.Views.Toolbar.textBordersStyle": "Стыль межаў", "SSE.Views.Toolbar.textBottom": "Ніжняе:", "SSE.Views.Toolbar.textBottomBorders": "Ніжнія межы", "SSE.Views.Toolbar.textCenterBorders": "Унутраныя вертыкальныя межы", "SSE.Views.Toolbar.textClearPrintArea": "Ачысціць вобласць друкавання", "SSE.Views.Toolbar.textClockwise": "Тэкст па гадзіннікавай стрэлцы", "SSE.Views.Toolbar.textCounterCw": "Тэкст супраць гадзіннікавай стрэлкі", "SSE.Views.Toolbar.textDelLeft": "Ячэйкі са зрухам улева", "SSE.Views.Toolbar.textDelUp": "Ячэйкі са зрухам уверх", "SSE.Views.Toolbar.textDiagDownBorder": "Дыяганальная мяжа зверху ўніз", "SSE.Views.Toolbar.textDiagUpBorder": "Дыяганальная мяжа знізу ўверх", "SSE.Views.Toolbar.textEntireCol": "Слупок", "SSE.Views.Toolbar.textEntireRow": "Радок", "SSE.Views.Toolbar.textFewPages": "Старонак", "SSE.Views.Toolbar.textHeight": "Вышыня", "SSE.Views.Toolbar.textHorizontal": "Гарызантальны тэкст", "SSE.Views.Toolbar.textInsDown": "Ячэйкі са зрухам уніз", "SSE.Views.Toolbar.textInsideBorders": "Унутраныя межы", "SSE.Views.Toolbar.textInsRight": "Ячэйкі са зрухам управа", "SSE.Views.Toolbar.textItalic": "Курсіў", "SSE.Views.Toolbar.textItems": "элементаў", "SSE.Views.Toolbar.textLandscape": "Альбомная", "SSE.Views.Toolbar.textLeft": "Левае:", "SSE.Views.Toolbar.textLeftBorders": "Левыя межы", "SSE.Views.Toolbar.textManyPages": "стар<PERSON><PERSON>к", "SSE.Views.Toolbar.textMarginsLast": "Апошнія адвольныя", "SSE.Views.Toolbar.textMarginsNarrow": "Вузкія", "SSE.Views.Toolbar.textMarginsNormal": "Звычайныя", "SSE.Views.Toolbar.textMarginsWide": "Шырокія", "SSE.Views.Toolbar.textMiddleBorders": "Унутраныя гарызантальныя межы", "SSE.Views.Toolbar.textMoreFormats": "Іншыя фарматы", "SSE.Views.Toolbar.textMorePages": "Іншыя старонкі", "SSE.Views.Toolbar.textNewColor": "Адвольны колер", "SSE.Views.Toolbar.textNoBorders": "Без межаў", "SSE.Views.Toolbar.textOnePage": "Старонка", "SSE.Views.Toolbar.textOutBorders": "Вонкавыя межы", "SSE.Views.Toolbar.textPageMarginsCustom": "Адвольныя палі", "SSE.Views.Toolbar.textPortrait": "Кніжная", "SSE.Views.Toolbar.textPrint": "Друкаванне", "SSE.Views.Toolbar.textPrintHeadings": "Друкаванне загалоўкаў", "SSE.Views.Toolbar.textPrintOptions": "Налады друку", "SSE.Views.Toolbar.textRight": "Правае:", "SSE.Views.Toolbar.textRightBorders": "Правыя межы", "SSE.Views.Toolbar.textRotateDown": "Павярнуць тэкст уніз", "SSE.Views.Toolbar.textRotateUp": "Павярнуць тэкст уверх", "SSE.Views.Toolbar.textScale": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Адвольны", "SSE.Views.Toolbar.textSetPrintArea": "Вызначыць вобласць друкавання", "SSE.Views.Toolbar.textStrikeout": "Закрэслены", "SSE.Views.Toolbar.textSubscript": "Падрадковыя", "SSE.Views.Toolbar.textSubSuperscript": "Падрадковыя / Надрадковыя", "SSE.Views.Toolbar.textSuperscript": "Надрадковыя", "SSE.Views.Toolbar.textTabCollaboration": "Сумесная праца", "SSE.Views.Toolbar.textTabData": "Даныя", "SSE.Views.Toolbar.textTabFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Формула", "SSE.Views.Toolbar.textTabHome": "Асноўныя функцыі", "SSE.Views.Toolbar.textTabInsert": "Устаўка", "SSE.Views.Toolbar.textTabLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabProtect": "Абарона", "SSE.Views.Toolbar.textTabView": "Мініяцюра", "SSE.Views.Toolbar.textTop": "Верх:", "SSE.Views.Toolbar.textTopBorders": "Верхнія межы", "SSE.Views.Toolbar.textUnderline": "Падкрэслены", "SSE.Views.Toolbar.textVertical": "Вертыкальны тэкст ", "SSE.Views.Toolbar.textWidth": "Шырыня", "SSE.Views.Toolbar.textZoom": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "Выраўнаваць па ніжняму краю", "SSE.Views.Toolbar.tipAlignCenter": "Выраўнаваць па цэнтры", "SSE.Views.Toolbar.tipAlignJust": "Па шырыні", "SSE.Views.Toolbar.tipAlignLeft": "Выраўнаваць па леваму краю", "SSE.Views.Toolbar.tipAlignMiddle": "Выраўнаваць па сярэдзіне", "SSE.Views.Toolbar.tipAlignRight": "Выраўнаваць па праваму краю", "SSE.Views.Toolbar.tipAlignTop": "Выраўнаваць па верхняму краю", "SSE.Views.Toolbar.tipAutofilter": "Парадкаванне і фільтрацыя", "SSE.Views.Toolbar.tipBack": "Назад", "SSE.Views.Toolbar.tipBorders": "Межы", "SSE.Views.Toolbar.tipCellStyle": "Стыль ячэйкі", "SSE.Views.Toolbar.tipChangeChart": "Змяніць тып дыяграмы", "SSE.Views.Toolbar.tipClearStyle": "Ачысціць", "SSE.Views.Toolbar.tipColorSchemas": "Змяніць каляровую схему", "SSE.Views.Toolbar.tipCopy": "Капіяваць", "SSE.Views.Toolbar.tipCopyStyle": "Скапіяваць стыль", "SSE.Views.Toolbar.tipDecDecimal": "Паменшыць разрад", "SSE.Views.Toolbar.tipDecFont": "Паменшыць памер шрыфту", "SSE.Views.Toolbar.tipDeleteOpt": "Выдаліць ячэйкі", "SSE.Views.Toolbar.tipDigStyleAccounting": "Фінансавы стыль", "SSE.Views.Toolbar.tipDigStyleCurrency": "Грашовы фармат", "SSE.Views.Toolbar.tipDigStylePercent": "Стыль адсоткаў", "SSE.Views.Toolbar.tipEditChart": "Рэдагаваць дыяграму", "SSE.Views.Toolbar.tipEditChartData": "Абраць даныя", "SSE.Views.Toolbar.tipEditChartType": "Змяніць тып дыяграмы", "SSE.Views.Toolbar.tipEditHeader": "Рэдагаваць калонтытулы", "SSE.Views.Toolbar.tipFontColor": "Колер шрыфту", "SSE.Views.Toolbar.tipFontName": "Шры<PERSON>т", "SSE.Views.Toolbar.tipFontSize": "Памер шрыфту", "SSE.Views.Toolbar.tipImgAlign": "Выраўнаваць аб’екты", "SSE.Views.Toolbar.tipImgGroup": "Згрупаваць аб’екты", "SSE.Views.Toolbar.tipIncDecimal": "Павялічыць разрад", "SSE.Views.Toolbar.tipIncFont": "Павялічыць памер шрыфту", "SSE.Views.Toolbar.tipInsertChart": "Уставіць дыяграму", "SSE.Views.Toolbar.tipInsertChartSpark": "Уставіць дыяграму", "SSE.Views.Toolbar.tipInsertEquation": "Уставіць раўнанне", "SSE.Views.Toolbar.tipInsertHyperlink": "Дада<PERSON>ь гіперспасылку", "SSE.Views.Toolbar.tipInsertImage": "Уставіць выяву", "SSE.Views.Toolbar.tipInsertOpt": "Уставіць ячэйкі", "SSE.Views.Toolbar.tipInsertShape": "Уставіць аўтафігуру", "SSE.Views.Toolbar.tipInsertSlicer": "Уставіць зводку", "SSE.Views.Toolbar.tipInsertSymbol": "Уставіць сімвал", "SSE.Views.Toolbar.tipInsertTable": "Уставіць табліцу", "SSE.Views.Toolbar.tipInsertText": "Уставіць надпіс", "SSE.Views.Toolbar.tipInsertTextart": "Уставіць Text Art", "SSE.Views.Toolbar.tipMerge": "Аб’яднаць і памясціць у цэнтры", "SSE.Views.Toolbar.tipNumFormat": "Фармат нумара", "SSE.Views.Toolbar.tipPageMargins": "Пал<PERSON> старонак", "SSE.Views.Toolbar.tipPageOrient": "Арыентацыя старонкі", "SSE.Views.Toolbar.tipPageSize": "Памер старон<PERSON>і", "SSE.Views.Toolbar.tipPaste": "Уставіць", "SSE.Views.Toolbar.tipPrColor": "Колер запаўнення", "SSE.Views.Toolbar.tipPrint": "Друкаванне", "SSE.Views.Toolbar.tipPrintArea": "Вобласць друкавання", "SSE.Views.Toolbar.tipPrintTitles": "Друкаваць загалоўкі", "SSE.Views.Toolbar.tipRedo": "Паўтарыць", "SSE.Views.Toolbar.tipSave": "Захаваць", "SSE.Views.Toolbar.tipSaveCoauth": "Захаваць свае змены, каб іншыя карыстальнікі іх убачылі.", "SSE.Views.Toolbar.tipScale": "Умясціць", "SSE.Views.Toolbar.tipSendBackward": "Адправіць назад", "SSE.Views.Toolbar.tipSendForward": "Перамясціць уперад", "SSE.Views.Toolbar.tipSynchronize": "Дакумент быў зменены іншым карыстальнікам. Націсніце, каб захаваць свае змены і загрузіць абнаўленні.", "SSE.Views.Toolbar.tipTextOrientation": "Арыентацыя", "SSE.Views.Toolbar.tipUndo": "Адрабіць", "SSE.Views.Toolbar.tipWrap": "Перанос тэксту", "SSE.Views.Toolbar.txtAccounting": "Фінансавы", "SSE.Views.Toolbar.txtAdditional": "Дадаткова", "SSE.Views.Toolbar.txtAscending": "Па ўзрастанні", "SSE.Views.Toolbar.txtAutosumTip": "Сума", "SSE.Views.Toolbar.txtClearAll": "Усе", "SSE.Views.Toolbar.txtClearComments": "Каментары", "SSE.Views.Toolbar.txtClearFilter": "Ачысціць фільтр", "SSE.Views.Toolbar.txtClearFormat": "Фарматаванне", "SSE.Views.Toolbar.txtClearFormula": "Функцыя", "SSE.Views.Toolbar.txtClearHyper": "Гіперспасылкі", "SSE.Views.Toolbar.txtClearText": "Тэкст", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Адвольны", "SSE.Views.Toolbar.txtDate": "Дата", "SSE.Views.Toolbar.txtDateTime": "Дата і час", "SSE.Views.Toolbar.txtDescending": "Па памяншэнні", "SSE.Views.Toolbar.txtDollar": "<PERSON><PERSON><PERSON><PERSON><PERSON> ЗША", "SSE.Views.Toolbar.txtEuro": "Еўра", "SSE.Views.Toolbar.txtExp": "Экспаненцыйны", "SSE.Views.Toolbar.txtFilter": "Фільтр", "SSE.Views.Toolbar.txtFormula": "Уставіць функцыю", "SSE.Views.Toolbar.txtFraction": "Дроб", "SSE.Views.Toolbar.txtFranc": "CHF Франк", "SSE.Views.Toolbar.txtGeneral": "Агульны", "SSE.Views.Toolbar.txtInteger": "Цэлы лік", "SSE.Views.Toolbar.txtManageRange": "Кіраўнік назваў", "SSE.Views.Toolbar.txtMergeAcross": "Аб’яднаць па радках", "SSE.Views.Toolbar.txtMergeCells": "Аб’яднаць ячэйкі", "SSE.Views.Toolbar.txtMergeCenter": "Аб’яднаць і размясціць у цэнтры", "SSE.Views.Toolbar.txtNamedRange": "Названыя дыяпазоны", "SSE.Views.Toolbar.txtNewRange": "Прызначыць назву", "SSE.Views.Toolbar.txtNoBorders": "Без межаў", "SSE.Views.Toolbar.txtNumber": "Лічбавы", "SSE.Views.Toolbar.txtPasteRange": "Уставіць назву", "SSE.Views.Toolbar.txtPercentage": "Адсотак", "SSE.Views.Toolbar.txtPound": "Фунт", "SSE.Views.Toolbar.txtRouble": "Расійскі рубель", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "Звычайная", "SSE.Views.Toolbar.txtScheme11": "Метро", "SSE.Views.Toolbar.txtScheme12": "Модульная", "SSE.Views.Toolbar.txtScheme13": "Прывабная", "SSE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme15": "Зыходная", "SSE.Views.Toolbar.txtScheme16": "Папяровая", "SSE.Views.Toolbar.txtScheme17": "Сонцаварот", "SSE.Views.Toolbar.txtScheme18": "Тэхнічная", "SSE.Views.Toolbar.txtScheme19": "Трэк", "SSE.Views.Toolbar.txtScheme2": "Адценні шэрага", "SSE.Views.Toolbar.txtScheme20": "Гарадская", "SSE.Views.Toolbar.txtScheme21": "Яркая", "SSE.Views.Toolbar.txtScheme3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme4": "Аспект", "SSE.Views.Toolbar.txtScheme5": "Аф<PERSON><PERSON><PERSON><PERSON>ная", "SSE.Views.Toolbar.txtScheme6": "Адкрытая", "SSE.Views.Toolbar.txtScheme7": "Справядлівасць", "SSE.Views.Toolbar.txtScheme8": "Плаваючая", "SSE.Views.Toolbar.txtScheme9": "Ліцейня", "SSE.Views.Toolbar.txtScientific": "Навуковы", "SSE.Views.Toolbar.txtSearch": "По<PERSON><PERSON>к", "SSE.Views.Toolbar.txtSort": "Сартаванне", "SSE.Views.Toolbar.txtSortAZ": "Сартаванне па ўзрастанні", "SSE.Views.Toolbar.txtSortZA": "Сартаванне па ўбыванні", "SSE.Views.Toolbar.txtSpecial": "Адмысловы", "SSE.Views.Toolbar.txtTableTemplate": "Фарматаваць як шаблон табліцы", "SSE.Views.Toolbar.txtText": "Тэкст", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Скасаваць аб’яднанне ячэек", "SSE.Views.Toolbar.txtYen": "Ена", "SSE.Views.Top10FilterDialog.textType": "Паказаць", "SSE.Views.Top10FilterDialog.txtBottom": "Знізу", "SSE.Views.Top10FilterDialog.txtBy": "па", "SSE.Views.Top10FilterDialog.txtItems": "Элемент", "SSE.Views.Top10FilterDialog.txtPercent": "Адсотак", "SSE.Views.Top10FilterDialog.txtSum": "Сума", "SSE.Views.Top10FilterDialog.txtTitle": "Аўтафільтр першых 10", "SSE.Views.Top10FilterDialog.txtTop": "Найбольшыя", "SSE.Views.Top10FilterDialog.txtValueTitle": "Фільтр першых 10", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Налады поля значэнняў", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Сярэдняе", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Базавае поле", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Базавы элемент", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 з %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Колькасць", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Колькасць лікаў", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Адвольная назва", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Адрозненне", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Індэкс", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Без падліку", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Адсотак", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Адрозненне ў адсотках", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Адсотак ад слупка", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Адсотак ад выніку", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Адсотак ад радка", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Твор", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "З вынікам", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Паказваць значэнні як", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Назва крыніцы:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Сума", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Аперацыя", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Закрыць", "SSE.Views.ViewManagerDlg.guestText": "Госць", "SSE.Views.ViewManagerDlg.textDelete": "Выдаліць", "SSE.Views.ViewManagerDlg.textDuplicate": "Дубляваць", "SSE.Views.ViewManagerDlg.textEmpty": "Мініяцюры яшчэ не створаныя.", "SSE.Views.ViewManagerDlg.textGoTo": "Перайсці да мініяцюры", "SSE.Views.ViewManagerDlg.textNew": "Новы", "SSE.Views.ViewManagerDlg.textRename": "Змяніць назву", "SSE.Views.ViewManagerDlg.textRenameError": "Назва выгляду не можа быць пустой.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Змяніць назву мініяцюры", "SSE.Views.ViewManagerDlg.textViews": "Мініяцюры аркушаў", "SSE.Views.ViewManagerDlg.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.ViewManagerDlg.txtTitle": "Кіраўнік мініяцюр аркуша", "SSE.Views.ViewManagerDlg.warnDeleteView": "Вы спрабуеце выдаліць актыўную мініяцюру \"%1\".<br>Закрыць і выдаліць?", "SSE.Views.ViewTab.capBtnFreeze": "Замацаваць вобласці", "SSE.Views.ViewTab.capBtnSheetView": "Мініяцюра аркуша", "SSE.Views.ViewTab.textClose": "Закрыць", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Схаваць панэль стану", "SSE.Views.ViewTab.textCreate": "Новы", "SSE.Views.ViewTab.textDefault": "Прадвызначана", "SSE.Views.ViewTab.textFormula": "Панэль формул", "SSE.Views.ViewTab.textGridlines": "Лініі сеткі", "SSE.Views.ViewTab.textHeadings": "Загалоўкі", "SSE.Views.ViewTab.textInterfaceTheme": "Тэма інтэрфейсу", "SSE.Views.ViewTab.textManager": "Кіраўнік мініяцюр", "SSE.Views.ViewTab.textZoom": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "Закрыць мініяцюру аркуша", "SSE.Views.ViewTab.tipCreate": "Стварыць мініяцюру аркуша", "SSE.Views.ViewTab.tipFreeze": "Замацаваць вобласці", "SSE.Views.ViewTab.tipInterfaceTheme": "Тэма інтэрфейсу", "SSE.Views.ViewTab.tipSheetView": "Мініяцюра аркуша"}