﻿<!DOCTYPE html>
<html>
	<head>
		<title>Registerkarte Startseite</title>
		<meta charset="utf-8" />
        <meta name="description" content="Benutzeroberfläche - Registerkarte Startseite - Präsentationseditor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Registerkarte Startseite</h1>
            <p>Die Registerkarte <b>Startseite</b> im <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> wird standardmäßig geöffnet, wenn Sie eine beliebige Präsentation öffnen. Hier können Sie allgemeine Folienparameter festlegen, Text formatieren und Objekte einfügen und diese ausrichten und anordnen.</p>
            <div class="onlineDocumentFeatures">
                <p>Dialogbox Online-Präsentationseditor:</p>
                <p><img alt="Registerkarte Startseite" src="../images/interface/hometab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Dialogbox Desktop-Präsentationseditor:</p>
                <p><img alt="Registerkarte Startseite" src="../images/interface/desktop_hometab.png" /></p>
            </div>
            <p>Sie können:</p>
            <ul>
                <li><a href="../UsageInstructions/ManageSlides.htm" onclick="onhyperlinkclick(this)">Folien</a> verwalten und eine <a href="../UsageInstructions/PreviewPresentation.htm" onclick="onhyperlinkclick(this)">Bildschirmpräsentation</a> starten,</li>
                <li><a href="../UsageInstructions/InsertText.htm#formattext" onclick="onhyperlinkclick(this)">Text</a> in einem Textfeld formatieren,</li>
                <li><a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">Textfelder</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">Bilder</a> und <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">Formen</a> einfügen,</li>
                <li>Objekte auf einer Folie <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">anordnen und ausrichten</a>,</li>
                <li>die Textformatierung <a href="../UsageInstructions/CopyClearFormatting.htm" onclick="onhyperlinkclick(this)">kopieren/entfernen</a>,</li>
                <li>ein <a href="../UsageInstructions/SetSlideParameters.htm" onclick="onhyperlinkclick(this)">Thema, Farbschema oder die Foliengröße</a> ändern.</li>
            </ul>
		</div>
	</body>
</html>