{"cancelButtonText": "取消", "Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Chat.textEnterMessage": "在這裡輸入您的信息", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.define.chartData.textArea": "區域", "Common.define.chartData.textAreaStacked": "堆叠面積", "Common.define.chartData.textAreaStackedPer": "100% 堆疊面積圖", "Common.define.chartData.textBar": "槓", "Common.define.chartData.textBarNormal": "劇集柱形", "Common.define.chartData.textBarNormal3d": "3-D 簇狀直式長條圖", "Common.define.chartData.textBarNormal3dPerspective": "3-D 直式長條圖", "Common.define.chartData.textBarStacked": "堆叠柱形", "Common.define.chartData.textBarStacked3d": "3-D 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer": "100% 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer3d": "3-D 100% 堆疊直式長條圖", "Common.define.chartData.textCharts": "圖表", "Common.define.chartData.textColumn": "欄", "Common.define.chartData.textColumnSpark": "欄", "Common.define.chartData.textCombo": "組合", "Common.define.chartData.textComboAreaBar": "堆叠面積 - 劇集柱形", "Common.define.chartData.textComboBarLine": "劇集柱形 - 綫", "Common.define.chartData.textComboBarLineSecondary": "劇集柱形 - 副軸綫", "Common.define.chartData.textComboCustom": "客制組合", "Common.define.chartData.textDoughnut": "甜甜圈圖", "Common.define.chartData.textHBarNormal": "劇集條形", "Common.define.chartData.textHBarNormal3d": "3-D 簇狀橫式長條圖", "Common.define.chartData.textHBarStacked": "堆叠條形", "Common.define.chartData.textHBarStacked3d": "3-D 堆疊橫式長條圖", "Common.define.chartData.textHBarStackedPer": "100% 堆疊橫式長條圖", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% 堆疊橫式長條圖", "Common.define.chartData.textLine": "線", "Common.define.chartData.textLine3d": "3-D 直線圖", "Common.define.chartData.textLineMarker": "直線加標記", "Common.define.chartData.textLineSpark": "線", "Common.define.chartData.textLineStacked": "堆叠綫", "Common.define.chartData.textLineStackedMarker": "堆積綫及標記", "Common.define.chartData.textLineStackedPer": "100% 堆疊直線圖", "Common.define.chartData.textLineStackedPerMarker": "100% 堆疊直線圖加標記", "Common.define.chartData.textPie": "餅", "Common.define.chartData.textPie3d": "3-D 圓餅圖", "Common.define.chartData.textPoint": "XY（散點圖）", "Common.define.chartData.textScatter": "散佈圖", "Common.define.chartData.textScatterLine": "散佈圖同直線", "Common.define.chartData.textScatterLineMarker": "散佈圖同直線及標記", "Common.define.chartData.textScatterSmooth": "散佈圖同平滑線", "Common.define.chartData.textScatterSmoothMarker": "散佈圖同平滑線及標記", "Common.define.chartData.textSparks": "走勢圖", "Common.define.chartData.textStock": "庫存", "Common.define.chartData.textSurface": "表面", "Common.define.chartData.textWinLossSpark": "贏/輸", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "無設定格式", "Common.define.conditionalData.text1Above": "高於1個標準差", "Common.define.conditionalData.text1Below": "低於1個標準差", "Common.define.conditionalData.text2Above": "高於2個標準差", "Common.define.conditionalData.text2Below": "低於2個標準差", "Common.define.conditionalData.text3Above": "高於3個標準差", "Common.define.conditionalData.text3Below": "低於3個標準差", "Common.define.conditionalData.textAbove": "以上", "Common.define.conditionalData.textAverage": " 平均", "Common.define.conditionalData.textBegins": "開始於", "Common.define.conditionalData.textBelow": "之下", "Common.define.conditionalData.textBetween": "之間", "Common.define.conditionalData.textBlank": "空白", "Common.define.conditionalData.textBlanks": "包含空白", "Common.define.conditionalData.textBottom": "底部", "Common.define.conditionalData.textContains": "包含", "Common.define.conditionalData.textDataBar": "數據欄", "Common.define.conditionalData.textDate": "日期", "Common.define.conditionalData.textDuplicate": "複製", "Common.define.conditionalData.textEnds": "結尾為", "Common.define.conditionalData.textEqAbove": "等於或高於", "Common.define.conditionalData.textEqBelow": "等於或低於", "Common.define.conditionalData.textEqual": "等於", "Common.define.conditionalData.textError": "錯誤", "Common.define.conditionalData.textErrors": "包含錯誤", "Common.define.conditionalData.textFormula": "公式", "Common.define.conditionalData.textGreater": "大於", "Common.define.conditionalData.textGreaterEq": "大於或等於", "Common.define.conditionalData.textIconSets": "圖標集", "Common.define.conditionalData.textLast7days": "在過去7天內", "Common.define.conditionalData.textLastMonth": "上個月", "Common.define.conditionalData.textLastWeek": "上個星期", "Common.define.conditionalData.textLess": "少於", "Common.define.conditionalData.textLessEq": "小於或等於", "Common.define.conditionalData.textNextMonth": "下個月", "Common.define.conditionalData.textNextWeek": "下個星期", "Common.define.conditionalData.textNotBetween": "不介於", "Common.define.conditionalData.textNotBlanks": "不含空白", "Common.define.conditionalData.textNotContains": "不含", "Common.define.conditionalData.textNotEqual": "不等於", "Common.define.conditionalData.textNotErrors": "不含錯誤", "Common.define.conditionalData.textText": "文字", "Common.define.conditionalData.textThisMonth": "這個月", "Common.define.conditionalData.textThisWeek": "此星期", "Common.define.conditionalData.textToday": "今天", "Common.define.conditionalData.textTomorrow": "明天", "Common.define.conditionalData.textTop": "上方", "Common.define.conditionalData.textUnique": "獨特", "Common.define.conditionalData.textValue": "此值是", "Common.define.conditionalData.textYesterday": "昨天", "Common.Translation.textMoreButton": "更多", "Common.Translation.warnFileLocked": "該文件正在另一個應用程序中進行編輯。您可以繼續編輯並將其另存為副本。", "Common.Translation.warnFileLockedBtnEdit": "建立副本", "Common.Translation.warnFileLockedBtnView": "打開查看", "Common.UI.ButtonColored.textAutoColor": "自動", "Common.UI.ButtonColored.textNewColor": "新增自訂顏色", "Common.UI.ComboBorderSize.txtNoBorders": "無邊框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "無邊框", "Common.UI.ComboDataView.emptyComboText": "無樣式", "Common.UI.ExtendedColorDialog.addButtonText": "新增", "Common.UI.ExtendedColorDialog.textCurrent": "當前", "Common.UI.ExtendedColorDialog.textHexErr": "輸入的值不正確。<br>請輸入一個介於000000和FFFFFF之間的值。", "Common.UI.ExtendedColorDialog.textNew": "新", "Common.UI.ExtendedColorDialog.textRGBErr": "輸入的值不正確。<br>請輸入0到255之間的數字。", "Common.UI.HSBColorPicker.textNoColor": "無顏色", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "不顯示密碼", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "顯示密碼", "Common.UI.SearchBar.textFind": "尋找", "Common.UI.SearchBar.tipCloseSearch": "關閉搜索", "Common.UI.SearchBar.tipNextResult": "下一個結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "開啟進階設置", "Common.UI.SearchBar.tipPreviousResult": "上一個結果", "Common.UI.SearchDialog.textHighlight": "強調結果", "Common.UI.SearchDialog.textMatchCase": "區分大小寫", "Common.UI.SearchDialog.textReplaceDef": "輸入替換文字", "Common.UI.SearchDialog.textSearchStart": "在這裡輸入您的文字", "Common.UI.SearchDialog.textTitle": "尋找與取代", "Common.UI.SearchDialog.textTitle2": "尋找", "Common.UI.SearchDialog.textWholeWords": "僅全字", "Common.UI.SearchDialog.txtBtnHideReplace": "隱藏替換", "Common.UI.SearchDialog.txtBtnReplace": "取代", "Common.UI.SearchDialog.txtBtnReplaceAll": "取代全部", "Common.UI.SynchronizeTip.textDontShow": "不再顯示此消息", "Common.UI.SynchronizeTip.textSynchronize": "該文檔已被其他用戶更改。<br>請單擊以保存更改並重新加載更新。", "Common.UI.ThemeColorPalette.textRecentColors": "近期顏色", "Common.UI.ThemeColorPalette.textStandartColors": "標準顏色", "Common.UI.ThemeColorPalette.textThemeColors": "主題顏色", "Common.UI.Themes.txtThemeClassicLight": "傳統亮色", "Common.UI.Themes.txtThemeContrastDark": "暗色對比", "Common.UI.Themes.txtThemeDark": "暗黑", "Common.UI.Themes.txtThemeLight": " 淺色主題", "Common.UI.Themes.txtThemeSystem": "和系統一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "關閉", "Common.UI.Window.noButtonText": "沒有", "Common.UI.Window.okButtonText": "確定", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "不再顯示此消息", "Common.UI.Window.textError": "錯誤", "Common.UI.Window.textInformation": "資訊", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "公分", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "地址:", "Common.Views.About.txtLicensee": "被許可人", "Common.Views.About.txtLicensor": "許可人", "Common.Views.About.txtMail": "電子郵件：", "Common.Views.About.txtPoweredBy": "於支援", "Common.Views.About.txtTel": "電話: ", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "新增", "Common.Views.AutoCorrectDialog.textApplyAsWork": "工作時套用", "Common.Views.AutoCorrectDialog.textAutoCorrect": " 自動更正", "Common.Views.AutoCorrectDialog.textAutoFormat": "鍵入時自動調整規格", "Common.Views.AutoCorrectDialog.textBy": "通過", "Common.Views.AutoCorrectDialog.textDelete": "刪除", "Common.Views.AutoCorrectDialog.textHyperlink": "網絡路徑超連結", "Common.Views.AutoCorrectDialog.textMathCorrect": "數學自動更正", "Common.Views.AutoCorrectDialog.textNewRowCol": "在表格中包括新的行和列", "Common.Views.AutoCorrectDialog.textRecognized": "公認的功能", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表達式是公認的數學表達式。它們不會自動斜體顯示。", "Common.Views.AutoCorrectDialog.textReplace": "取代", "Common.Views.AutoCorrectDialog.textReplaceText": "鍵入時替換", "Common.Views.AutoCorrectDialog.textReplaceType": "鍵入時替換文字", "Common.Views.AutoCorrectDialog.textReset": "重設", "Common.Views.AutoCorrectDialog.textResetAll": "重置為預設", "Common.Views.AutoCorrectDialog.textRestore": "恢復", "Common.Views.AutoCorrectDialog.textTitle": "自動更正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "公認的函數只能包含字母A到Z，大寫或小寫。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "您添加的所有表達式都將被刪除，被刪除的表達式將被恢復。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnReplace": "％1的自動更正條目已存在。您要更換嗎？", "Common.Views.AutoCorrectDialog.warnReset": "您添加的所有自動更正將被刪除，更改後的自動更正將恢復為其原始值。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnRestore": "％1的自動更正條目將被重置為其原始值。你想繼續嗎？", "Common.Views.Chat.textSend": "傳送", "Common.Views.Comments.mniAuthorAsc": "作者排行A到Z", "Common.Views.Comments.mniAuthorDesc": "作者排行Z到A", "Common.Views.Comments.mniDateAsc": "從最老的", "Common.Views.Comments.mniDateDesc": "從最新的", "Common.Views.Comments.mniFilterGroups": "依群組篩選", "Common.Views.Comments.mniPositionAsc": "從上到下", "Common.Views.Comments.mniPositionDesc": "自下而上", "Common.Views.Comments.textAdd": "新增", "Common.Views.Comments.textAddComment": "增加留言", "Common.Views.Comments.textAddCommentToDoc": "在文檔中添加評論", "Common.Views.Comments.textAddReply": "加入回覆", "Common.Views.Comments.textAll": "全部", "Common.Views.Comments.textAnonym": "來賓帳戶", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "關閉", "Common.Views.Comments.textClosePanel": "關註釋", "Common.Views.Comments.textComments": "評論", "Common.Views.Comments.textEdit": "確定", "Common.Views.Comments.textEnterCommentHint": "在這裡輸入您的評論", "Common.Views.Comments.textHintAddComment": "增加留言", "Common.Views.Comments.textOpenAgain": "重新打開", "Common.Views.Comments.textReply": "回覆", "Common.Views.Comments.textResolve": "解決", "Common.Views.Comments.textResolved": "已解決", "Common.Views.Comments.textSort": "註釋分類", "Common.Views.Comments.textViewResolved": "您沒有權限來重新開啟這個註解", "Common.Views.Comments.txtEmpty": "工作表裡沒有任何註解。", "Common.Views.CopyWarningDialog.textDontShow": "不再顯示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用編輯器工具欄按鈕進行複制，剪切和粘貼操作以及上下文選單操作僅在此編輯器選項卡中執行。<br> <br>要在“編輯器”選項卡之外的應用程序之間進行複製或粘貼，請使用以下鍵盤組合：", "Common.Views.CopyWarningDialog.textTitle": "複製, 剪下, 與貼上之動作", "Common.Views.CopyWarningDialog.textToCopy": "複印", "Common.Views.CopyWarningDialog.textToCut": "切", "Common.Views.CopyWarningDialog.textToPaste": "粘貼", "Common.Views.DocumentAccessDialog.textLoading": "載入中...", "Common.Views.DocumentAccessDialog.textTitle": "分享設定", "Common.Views.EditNameDialog.textLabel": "標籤：", "Common.Views.EditNameDialog.textLabelError": "標籤不能為空。", "Common.Views.Header.labelCoUsersDescr": "正在編輯文件的用戶：", "Common.Views.Header.textAddFavorite": "標記為最愛收藏", "Common.Views.Header.textAdvSettings": "進階設定", "Common.Views.Header.textBack": "打開文件所在位置", "Common.Views.Header.textCompactView": "隱藏工具欄", "Common.Views.Header.textHideLines": "隱藏標尺", "Common.Views.Header.textHideStatusBar": "隱藏狀態欄", "Common.Views.Header.textRemoveFavorite": "\n從最愛收藏夾中刪除", "Common.Views.Header.textSaveBegin": "存檔中...", "Common.Views.Header.textSaveChanged": "已更改", "Common.Views.Header.textSaveEnd": "所有更改已保存", "Common.Views.Header.textSaveExpander": "所有更改已保存", "Common.Views.Header.textShare": "分享", "Common.Views.Header.textZoom": "放大", "Common.Views.Header.tipAccessRights": "管理文檔存取權限", "Common.Views.Header.tipDownload": "下載文件", "Common.Views.Header.tipGoEdit": "編輯當前文件", "Common.Views.Header.tipPrint": "列印文件", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipSave": "儲存", "Common.Views.Header.tipSearch": "搜尋", "Common.Views.Header.tipUndo": "復原", "Common.Views.Header.tipUndock": "移至單獨的視窗", "Common.Views.Header.tipUsers": "查看用戶", "Common.Views.Header.tipViewSettings": "查看設定", "Common.Views.Header.tipViewUsers": "查看用戶並管理文檔存取權限", "Common.Views.Header.txtAccessRights": "更改存取權限", "Common.Views.Header.txtRename": "重新命名", "Common.Views.History.textCloseHistory": "關閉歷史紀錄", "Common.Views.History.textHide": "塌陷", "Common.Views.History.textHideAll": "隱藏詳細的更改", "Common.Views.History.textRestore": "恢復", "Common.Views.History.textShow": "擴大", "Common.Views.History.textShowAll": "顯示詳細的更改歷史", "Common.Views.History.textVer": "版本", "Common.Views.ImageFromUrlDialog.textUrl": "粘貼圖片網址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "這是必填欄", "Common.Views.ImageFromUrlDialog.txtNotUrl": "此字段應為“ http://www.example.com”格式的網址", "Common.Views.ListSettingsDialog.textBulleted": "已加入項目點", "Common.Views.ListSettingsDialog.textFromStorage": "從存儲", "Common.Views.ListSettingsDialog.textFromUrl": "從 URL", "Common.Views.ListSettingsDialog.textNumbering": "已編號", "Common.Views.ListSettingsDialog.tipChange": "更改項目點", "Common.Views.ListSettingsDialog.txtBullet": "項目點", "Common.Views.ListSettingsDialog.txtColor": "顏色", "Common.Views.ListSettingsDialog.txtImage": "圖像", "Common.Views.ListSettingsDialog.txtImport": "匯入", "Common.Views.ListSettingsDialog.txtNewBullet": "新項目點", "Common.Views.ListSettingsDialog.txtNone": "無", "Common.Views.ListSettingsDialog.txtOfText": "文字百分比", "Common.Views.ListSettingsDialog.txtSize": "大小", "Common.Views.ListSettingsDialog.txtStart": "開始", "Common.Views.ListSettingsDialog.txtSymbol": "符號", "Common.Views.ListSettingsDialog.txtTitle": "清單設定", "Common.Views.ListSettingsDialog.txtType": "類型", "Common.Views.OpenDialog.closeButtonText": "關閉檔案", "Common.Views.OpenDialog.textInvalidRange": "無效的儲存格範圍", "Common.Views.OpenDialog.textSelectData": "選擇數據", "Common.Views.OpenDialog.txtAdvanced": "進階", "Common.Views.OpenDialog.txtColon": "冒號", "Common.Views.OpenDialog.txtComma": "逗號", "Common.Views.OpenDialog.txtDelimiter": "分隔符號", "Common.Views.OpenDialog.txtDestData": "選擇數據擺位", "Common.Views.OpenDialog.txtEmpty": "這是必填欄", "Common.Views.OpenDialog.txtEncoding": "編碼", "Common.Views.OpenDialog.txtIncorrectPwd": "密碼錯誤。", "Common.Views.OpenDialog.txtOpenFile": "輸入檔案密碼", "Common.Views.OpenDialog.txtOther": "其它", "Common.Views.OpenDialog.txtPassword": "密碼", "Common.Views.OpenDialog.txtPreview": "預覽", "Common.Views.OpenDialog.txtProtected": "輸入密碼並打開文件後，該文件的當前密碼將被重置。", "Common.Views.OpenDialog.txtSemicolon": "分號", "Common.Views.OpenDialog.txtSpace": "空間", "Common.Views.OpenDialog.txtTab": "標籤", "Common.Views.OpenDialog.txtTitle": "選擇％1個選項", "Common.Views.OpenDialog.txtTitleProtected": "受保護的文件", "Common.Views.PasswordDialog.txtDescription": "設置密碼以保護此文檔", "Common.Views.PasswordDialog.txtIncorrectPwd": "確認密碼不相同", "Common.Views.PasswordDialog.txtPassword": "密碼", "Common.Views.PasswordDialog.txtRepeat": "重複輸入密碼", "Common.Views.PasswordDialog.txtTitle": "設置密碼", "Common.Views.PasswordDialog.txtWarning": "警告：如果失去密碼，將無法取回。請妥善保存。", "Common.Views.PluginDlg.textLoading": "載入中", "Common.Views.Plugins.groupCaption": "外掛程式", "Common.Views.Plugins.strPlugins": "外掛程式", "Common.Views.Plugins.textClosePanel": "關閉插件", "Common.Views.Plugins.textLoading": "載入中", "Common.Views.Plugins.textStart": "開始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Protection.hintAddPwd": "用密碼加密", "Common.Views.Protection.hintPwd": "更改或刪除密碼", "Common.Views.Protection.hintSignature": "添加數字簽名或簽名行", "Common.Views.Protection.txtAddPwd": "新增密碼", "Common.Views.Protection.txtChangePwd": "變更密碼", "Common.Views.Protection.txtDeletePwd": "刪除密碼", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "添加數字簽名", "Common.Views.Protection.txtSignature": "簽名", "Common.Views.Protection.txtSignatureLine": "添加簽名行", "Common.Views.RenameDialog.textName": "檔案名稱", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "到下一個變化", "Common.Views.ReviewChanges.hintPrev": "到之前的變化", "Common.Views.ReviewChanges.strFast": "快", "Common.Views.ReviewChanges.strFastDesc": "實時共同編輯。所有更改將自動保存。", "Common.Views.ReviewChanges.strStrict": "嚴格", "Common.Views.ReviewChanges.strStrictDesc": "使用“保存”按鈕同步您和其他人所做的更改。", "Common.Views.ReviewChanges.tipAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.tipCoAuthMode": "設定共同編輯模式", "Common.Views.ReviewChanges.tipCommentRem": "刪除評論", "Common.Views.ReviewChanges.tipCommentRemCurrent": "刪除當前評論", "Common.Views.ReviewChanges.tipCommentResolve": "標記註解為已解決", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "將註解標記為已解決", "Common.Views.ReviewChanges.tipHistory": "顯示版本歷史", "Common.Views.ReviewChanges.tipRejectCurrent": "拒絕當前變化", "Common.Views.ReviewChanges.tipReview": "跟蹤變化", "Common.Views.ReviewChanges.tipReviewView": "選擇您要顯示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "設定文件語言", "Common.Views.ReviewChanges.tipSetSpelling": "拼字檢查", "Common.Views.ReviewChanges.tipSharing": "管理文檔存取權限", "Common.Views.ReviewChanges.txtAccept": "同意", "Common.Views.ReviewChanges.txtAcceptAll": "接受全部的更改", "Common.Views.ReviewChanges.txtAcceptChanges": "同意更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "關閉", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編輯模式", "Common.Views.ReviewChanges.txtCommentRemAll": "刪除所有評論", "Common.Views.ReviewChanges.txtCommentRemCurrent": "刪除當前評論", "Common.Views.ReviewChanges.txtCommentRemMy": "刪除我的評論", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "刪除我當前的評論", "Common.Views.ReviewChanges.txtCommentRemove": "移除", "Common.Views.ReviewChanges.txtCommentResolve": "解決", "Common.Views.ReviewChanges.txtCommentResolveAll": "將所有註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "將註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMy": "將自己所有的註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "將自己的註解標記為已解決", "Common.Views.ReviewChanges.txtDocLang": "語言", "Common.Views.ReviewChanges.txtFinal": "更改已全部接受（預覽）", "Common.Views.ReviewChanges.txtFinalCap": "最後", "Common.Views.ReviewChanges.txtHistory": "版本歷史", "Common.Views.ReviewChanges.txtMarkup": "全部的更改(編輯中)", "Common.Views.ReviewChanges.txtMarkupCap": "標記", "Common.Views.ReviewChanges.txtNext": "下一個", "Common.Views.ReviewChanges.txtOriginal": "全部更改被拒絕（預覽）", "Common.Views.ReviewChanges.txtOriginalCap": "原始", "Common.Views.ReviewChanges.txtPrev": "前一個", "Common.Views.ReviewChanges.txtReject": "拒絕", "Common.Views.ReviewChanges.txtRejectAll": "拒絕所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "拒絕更改", "Common.Views.ReviewChanges.txtRejectCurrent": "拒絕當前變化", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼字檢查", "Common.Views.ReviewChanges.txtTurnon": "跟蹤變化", "Common.Views.ReviewChanges.txtView": "顯示模式", "Common.Views.ReviewPopover.textAdd": "新增", "Common.Views.ReviewPopover.textAddReply": "加入回覆", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "關閉", "Common.Views.ReviewPopover.textEdit": "確定", "Common.Views.ReviewPopover.textMention": "+提及將提供對文檔的存取權限並發送電子郵件", "Common.Views.ReviewPopover.textMentionNotify": "+提及將通過電子郵件通知用戶", "Common.Views.ReviewPopover.textOpenAgain": "重新打開", "Common.Views.ReviewPopover.textReply": "回覆", "Common.Views.ReviewPopover.textResolve": "解決", "Common.Views.ReviewPopover.textViewResolved": "您沒有權限來重新開啟這個註解", "Common.Views.ReviewPopover.txtDeleteTip": "刪除", "Common.Views.ReviewPopover.txtEditTip": "編輯", "Common.Views.SaveAsDlg.textLoading": "載入中", "Common.Views.SaveAsDlg.textTitle": "保存文件夾", "Common.Views.SearchPanel.textByColumns": "按列", "Common.Views.SearchPanel.textByRows": "按行", "Common.Views.SearchPanel.textCaseSensitive": "區分大小寫", "Common.Views.SearchPanel.textCell": "單元格", "Common.Views.SearchPanel.textCloseSearch": "關閉搜索", "Common.Views.SearchPanel.textFind": "尋找", "Common.Views.SearchPanel.textFindAndReplace": "尋找與取代", "Common.Views.SearchPanel.textFormula": "配方", "Common.Views.SearchPanel.textFormulas": "公式", "Common.Views.SearchPanel.textItemEntireCell": "整個單元格內容", "Common.Views.SearchPanel.textLookIn": "探望", "Common.Views.SearchPanel.textMatchUsingRegExp": "用正規表達式進行匹配", "Common.Views.SearchPanel.textName": "名稱", "Common.Views.SearchPanel.textNoMatches": "無匹配", "Common.Views.SearchPanel.textNoSearchResults": "查無搜索结果", "Common.Views.SearchPanel.textReplace": "取代", "Common.Views.SearchPanel.textReplaceAll": "全部替換", "Common.Views.SearchPanel.textReplaceWith": "替換為", "Common.Views.SearchPanel.textSearch": "搜尋", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchOptions": "搜索選項", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "選擇數據范圍", "Common.Views.SearchPanel.textSheet": "表格", "Common.Views.SearchPanel.textSpecificRange": "特定範圍", "Common.Views.SearchPanel.textTooManyResults": "因數量過多而無法顯示部分結果", "Common.Views.SearchPanel.textValue": "值", "Common.Views.SearchPanel.textValues": "值", "Common.Views.SearchPanel.textWholeWords": "僅全字", "Common.Views.SearchPanel.textWithin": "內", "Common.Views.SearchPanel.textWorkbook": "工作簿", "Common.Views.SearchPanel.tipNextResult": "下一個結果", "Common.Views.SearchPanel.tipPreviousResult": "上一個結果", "Common.Views.SelectFileDlg.textLoading": "載入中", "Common.Views.SelectFileDlg.textTitle": "選擇資料來源", "Common.Views.SignDialog.textBold": "粗體", "Common.Views.SignDialog.textCertificate": "證書", "Common.Views.SignDialog.textChange": "變更", "Common.Views.SignDialog.textInputName": "輸入簽名者名稱", "Common.Views.SignDialog.textItalic": "斜體", "Common.Views.SignDialog.textNameError": "簽名人姓名不能留空。", "Common.Views.SignDialog.textPurpose": "簽署本文件的目的", "Common.Views.SignDialog.textSelect": "選擇", "Common.Views.SignDialog.textSelectImage": "選擇圖片", "Common.Views.SignDialog.textSignature": "簽名看起來像", "Common.Views.SignDialog.textTitle": "簽署文件", "Common.Views.SignDialog.textUseImage": "或單擊“選擇圖像”以使用圖片作為簽名", "Common.Views.SignDialog.textValid": "從％1到％2有效", "Common.Views.SignDialog.tipFontName": "字體名稱", "Common.Views.SignDialog.tipFontSize": "字體大小", "Common.Views.SignSettingsDialog.textAllowComment": "允許簽名者在簽名對話框中添加註釋", "Common.Views.SignSettingsDialog.textInfoEmail": "電子郵件", "Common.Views.SignSettingsDialog.textInfoName": "名稱", "Common.Views.SignSettingsDialog.textInfoTitle": "簽名人稱號", "Common.Views.SignSettingsDialog.textInstructions": "簽名者說明", "Common.Views.SignSettingsDialog.textShowDate": "在簽名行中顯示簽名日期", "Common.Views.SignSettingsDialog.textTitle": "簽名設置", "Common.Views.SignSettingsDialog.txtEmpty": "這是必填欄", "Common.Views.SymbolTableDialog.textCharacter": "字符", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX 值", "Common.Views.SymbolTableDialog.textCopyright": "版權標誌", "Common.Views.SymbolTableDialog.textDCQuote": "結束雙引號", "Common.Views.SymbolTableDialog.textDOQuote": "開頭雙引號", "Common.Views.SymbolTableDialog.textEllipsis": "水平橢圓", "Common.Views.SymbolTableDialog.textEmDash": "空槓", "Common.Views.SymbolTableDialog.textEmSpace": "空白空間", "Common.Views.SymbolTableDialog.textEnDash": "En 橫槓", "Common.Views.SymbolTableDialog.textEnSpace": "En 空白", "Common.Views.SymbolTableDialog.textFont": "字體", "Common.Views.SymbolTableDialog.textNBHyphen": "不間斷連字符", "Common.Views.SymbolTableDialog.textNBSpace": "不間斷空間", "Common.Views.SymbolTableDialog.textPilcrow": "稻草人標誌", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 空白空間", "Common.Views.SymbolTableDialog.textRange": "範圍", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符號", "Common.Views.SymbolTableDialog.textRegistered": "註冊標誌", "Common.Views.SymbolTableDialog.textSCQuote": "結束單引號", "Common.Views.SymbolTableDialog.textSection": "分區標誌", "Common.Views.SymbolTableDialog.textShortcut": "快捷鍵", "Common.Views.SymbolTableDialog.textSHyphen": "軟連字符", "Common.Views.SymbolTableDialog.textSOQuote": "開單報價", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符號", "Common.Views.SymbolTableDialog.textTitle": "符號", "Common.Views.SymbolTableDialog.textTradeMark": "商標符號", "Common.Views.UserNameDialog.textDontShow": "不要再顯示", "Common.Views.UserNameDialog.textLabel": "標記：", "Common.Views.UserNameDialog.textLabelError": "標籤不能為空。", "SSE.Controllers.DataTab.textColumns": "欄", "SSE.Controllers.DataTab.textEmptyUrl": "你必須指定URL", "SSE.Controllers.DataTab.textRows": "行列", "SSE.Controllers.DataTab.textWizard": "文字轉欄", "SSE.Controllers.DataTab.txtDataValidation": "資料驗證", "SSE.Controllers.DataTab.txtExpand": "擴大", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "所選內容旁邊的數據將不會被刪除。您要擴展選擇範圍以包括相鄰數據還是僅繼續使用當前選定的單元格？", "SSE.Controllers.DataTab.txtExtendDataValidation": "該選擇包含一些沒有數據驗證設置的單元。<br>是否要將數據驗證擴展到這些單元？", "SSE.Controllers.DataTab.txtImportWizard": "文字彙入精靈", "SSE.Controllers.DataTab.txtRemDuplicates": "刪除重複項", "SSE.Controllers.DataTab.txtRemoveDataValidation": "該選擇包含多種驗證類型。<br>清除當前設置並繼續嗎？", "SSE.Controllers.DataTab.txtRemSelected": "在選定的位置刪除", "SSE.Controllers.DataTab.txtUrlTitle": "粘貼數據 URL", "SSE.Controllers.DocumentHolder.alignmentText": "對齊", "SSE.Controllers.DocumentHolder.centerText": "中心", "SSE.Controllers.DocumentHolder.deleteColumnText": "刪除欄位", "SSE.Controllers.DocumentHolder.deleteRowText": "刪除行列", "SSE.Controllers.DocumentHolder.deleteText": "刪除", "SSE.Controllers.DocumentHolder.errorInvalidLink": "連結引用不存在。請更正連結或將其刪除。", "SSE.Controllers.DocumentHolder.guestText": "來賓帳戶", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "欄位以左", "SSE.Controllers.DocumentHolder.insertColumnRightText": "欄位以右", "SSE.Controllers.DocumentHolder.insertRowAboveText": "上行", "SSE.Controllers.DocumentHolder.insertRowBelowText": "下行", "SSE.Controllers.DocumentHolder.insertText": "插入", "SSE.Controllers.DocumentHolder.leftText": "左", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "警告", "SSE.Controllers.DocumentHolder.rightText": "右", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "自動更正選項", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "列寬{0}個符號（{1}個像素）", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "行高{0}點（{1}像素）", "SSE.Controllers.DocumentHolder.textCtrlClick": "單擊鏈接將其打開，或單擊並按住鼠標按鈕以選擇該單元格。", "SSE.Controllers.DocumentHolder.textInsertLeft": "向左插入", "SSE.Controllers.DocumentHolder.textInsertTop": "插入頂部", "SSE.Controllers.DocumentHolder.textPasteSpecial": "特別貼黏", "SSE.Controllers.DocumentHolder.textStopExpand": "停止自動展開表格", "SSE.Controllers.DocumentHolder.textSym": "象徵", "SSE.Controllers.DocumentHolder.tipIsLocked": "該元素正在由另一個用戶編輯。", "SSE.Controllers.DocumentHolder.txtAboveAve": "高於平均", "SSE.Controllers.DocumentHolder.txtAddBottom": "添加底部邊框", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "添加分數欄", "SSE.Controllers.DocumentHolder.txtAddHor": "添加水平線", "SSE.Controllers.DocumentHolder.txtAddLB": "添加左底邊框", "SSE.Controllers.DocumentHolder.txtAddLeft": "添加左邊框", "SSE.Controllers.DocumentHolder.txtAddLT": "添加左上頂行", "SSE.Controllers.DocumentHolder.txtAddRight": "加入右邊框", "SSE.Controllers.DocumentHolder.txtAddTop": "加入上邊框", "SSE.Controllers.DocumentHolder.txtAddVer": "加入垂直線", "SSE.Controllers.DocumentHolder.txtAlignToChar": "與字符對齊", "SSE.Controllers.DocumentHolder.txtAll": "（所有）", "SSE.Controllers.DocumentHolder.txtAllTableHint": "回傳表格或指定表格列的全部內容包括列標題，資料和總行術", "SSE.Controllers.DocumentHolder.txtAnd": "和", "SSE.Controllers.DocumentHolder.txtBegins": "開始於", "SSE.Controllers.DocumentHolder.txtBelowAve": "低於平均值", "SSE.Controllers.DocumentHolder.txtBlanks": "（空白）", "SSE.Controllers.DocumentHolder.txtBorderProps": "邊框屬性", "SSE.Controllers.DocumentHolder.txtBottom": "底部", "SSE.Controllers.DocumentHolder.txtColumn": "欄", "SSE.Controllers.DocumentHolder.txtColumnAlign": "欄位對準", "SSE.Controllers.DocumentHolder.txtContains": "包含", "SSE.Controllers.DocumentHolder.txtCopySuccess": "連結已複製到剪貼板", "SSE.Controllers.DocumentHolder.txtDataTableHint": "回傳表格儲存格，或指定的表格儲存格", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "減小參數大小", "SSE.Controllers.DocumentHolder.txtDeleteArg": "刪除參數", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "刪除手動休息", "SSE.Controllers.DocumentHolder.txtDeleteChars": "刪除封閉字符", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "刪除括起來的字符和分隔符", "SSE.Controllers.DocumentHolder.txtDeleteEq": "刪除方程式", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "刪除字元", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "刪除部首", "SSE.Controllers.DocumentHolder.txtEnds": "以。。結束", "SSE.Controllers.DocumentHolder.txtEquals": "等於", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "等於單元格顏色", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "等於字體顏色", "SSE.Controllers.DocumentHolder.txtExpand": "展開和排序", "SSE.Controllers.DocumentHolder.txtExpandSort": "選擇項旁邊的數據將不會排序。您是否要擴展選擇範圍以包括相鄰數據，還是僅對當前選定的單元格進行排序？", "SSE.Controllers.DocumentHolder.txtFilterBottom": "底部", "SSE.Controllers.DocumentHolder.txtFilterTop": "上方", "SSE.Controllers.DocumentHolder.txtFractionLinear": "更改為線性分數", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "更改為傾斜分數", "SSE.Controllers.DocumentHolder.txtFractionStacked": "更改為堆積分數", "SSE.Controllers.DocumentHolder.txtGreater": "更佳", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "大於或等於", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "字符至文字的上方", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "字符至文字的下方", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "回傳表格列標題，或指定的表格列標題", "SSE.Controllers.DocumentHolder.txtHeight": "\n高度", "SSE.Controllers.DocumentHolder.txtHideBottom": "隱藏底部邊框", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "隱藏下限", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "隱藏右括號", "SSE.Controllers.DocumentHolder.txtHideDegree": "隱藏度", "SSE.Controllers.DocumentHolder.txtHideHor": "隱藏水平線", "SSE.Controllers.DocumentHolder.txtHideLB": "隱藏左底線", "SSE.Controllers.DocumentHolder.txtHideLeft": "隱藏左邊框", "SSE.Controllers.DocumentHolder.txtHideLT": "隱藏左頂行", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "隱藏開口支架", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "隱藏佔位符", "SSE.Controllers.DocumentHolder.txtHideRight": "隱藏右邊框", "SSE.Controllers.DocumentHolder.txtHideTop": "隱藏頂部邊框", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "隱藏最高限額", "SSE.Controllers.DocumentHolder.txtHideVer": "隱藏垂直線", "SSE.Controllers.DocumentHolder.txtImportWizard": "文字彙入精靈", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "增加參數大小", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "在後面插入參數", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "在前面插入參數", "SSE.Controllers.DocumentHolder.txtInsertBreak": "插入手動中斷", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "在後面插入方程式", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "在前面插入方程式", "SSE.Controllers.DocumentHolder.txtItems": "項目", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "僅保留文字", "SSE.Controllers.DocumentHolder.txtLess": "少於", "SSE.Controllers.DocumentHolder.txtLessEquals": "小於或等於", "SSE.Controllers.DocumentHolder.txtLimitChange": "更改限制位置", "SSE.Controllers.DocumentHolder.txtLimitOver": "文字限制", "SSE.Controllers.DocumentHolder.txtLimitUnder": "文字下的限制", "SSE.Controllers.DocumentHolder.txtLockSort": "您選材的比鄰有數據，可您更改權限不足。<br>是否繼續當前選材？", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "將括號匹配到參數高度", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "矩陣對齊", "SSE.Controllers.DocumentHolder.txtNoChoices": "無法選擇要填充單元格的內容。<br>只能選擇列中的文本值進行替換。", "SSE.Controllers.DocumentHolder.txtNotBegins": "不以", "SSE.Controllers.DocumentHolder.txtNotContains": "不含", "SSE.Controllers.DocumentHolder.txtNotEnds": "不以結束", "SSE.Controllers.DocumentHolder.txtNotEquals": "不等於", "SSE.Controllers.DocumentHolder.txtOr": "或", "SSE.Controllers.DocumentHolder.txtOverbar": "槓覆蓋文字", "SSE.Controllers.DocumentHolder.txtPaste": "貼上", "SSE.Controllers.DocumentHolder.txtPasteBorders": "無框公式", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "公式+欄寬", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "目標格式", "SSE.Controllers.DocumentHolder.txtPasteFormat": "僅粘貼格式", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "公式+數字格式", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "僅粘貼公式", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "公式+所有格式", "SSE.Controllers.DocumentHolder.txtPasteLink": "貼上連結", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "已連接的圖片", "SSE.Controllers.DocumentHolder.txtPasteMerge": "合併條件格式", "SSE.Controllers.DocumentHolder.txtPastePicture": "圖片", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "源格式", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "轉置", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "值+所有格式", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "值+數字格式", "SSE.Controllers.DocumentHolder.txtPasteValues": "僅粘貼值", "SSE.Controllers.DocumentHolder.txtPercent": "百分比", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "重做表自動擴展", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "刪除分數欄", "SSE.Controllers.DocumentHolder.txtRemLimit": "取消限制", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "刪除強調字符", "SSE.Controllers.DocumentHolder.txtRemoveBar": "移除欄", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "確定移除此簽名？<br>這動作無法復原。", "SSE.Controllers.DocumentHolder.txtRemScripts": "刪除腳本", "SSE.Controllers.DocumentHolder.txtRemSubscript": "刪除下標", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "刪除上標", "SSE.Controllers.DocumentHolder.txtRowHeight": "行高", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "文字後的腳本", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "文字前的腳本", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "顯示底限", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "顯示結束括號", "SSE.Controllers.DocumentHolder.txtShowDegree": "顯示程度", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "顯示開口支架", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "顯示佔位符", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "顯示最高限額", "SSE.Controllers.DocumentHolder.txtSorting": "排序", "SSE.Controllers.DocumentHolder.txtSortSelected": "排序已選擇項目", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "延伸括號", "SSE.Controllers.DocumentHolder.txtThisRowHint": "在選定的列裡選擇這行", "SSE.Controllers.DocumentHolder.txtTop": "上方", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "回傳表格或指定表格列的總行數", "SSE.Controllers.DocumentHolder.txtUnderbar": "槓至文字底下", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "撤消表自動擴展", "SSE.Controllers.DocumentHolder.txtUseTextImport": "使用文本導入嚮導", "SSE.Controllers.DocumentHolder.txtWarnUrl": "這鏈接有可能對您的設備和數據造成損害。<br> 您確定要繼續嗎？", "SSE.Controllers.DocumentHolder.txtWidth": "寬度", "SSE.Controllers.FormulaDialog.sCategoryAll": "全部", "SSE.Controllers.FormulaDialog.sCategoryCube": "　立方體", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "數據庫", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "日期和時間", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "工程", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "金融", "SSE.Controllers.FormulaDialog.sCategoryInformation": "資訊", "SSE.Controllers.FormulaDialog.sCategoryLast10": "最後使用10", "SSE.Controllers.FormulaDialog.sCategoryLogical": "合邏輯", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "查找和參考", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "數學和三角學", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "統計", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "文字和數據", "SSE.Controllers.LeftMenu.newDocumentTitle": "未命名電子表格", "SSE.Controllers.LeftMenu.textByColumns": "按列", "SSE.Controllers.LeftMenu.textByRows": "按行", "SSE.Controllers.LeftMenu.textFormulas": "公式", "SSE.Controllers.LeftMenu.textItemEntireCell": "整個單元格內容", "SSE.Controllers.LeftMenu.textLoadHistory": "正在載入版本歷史記錄...", "SSE.Controllers.LeftMenu.textLookin": "探望", "SSE.Controllers.LeftMenu.textNoTextFound": "找不到您一直在搜索的數據。請調整您的搜索選項。", "SSE.Controllers.LeftMenu.textReplaceSkipped": "替換已完成。 {0}個事件被跳過。", "SSE.Controllers.LeftMenu.textReplaceSuccess": "搜索已完成。發生的事件已替換：{0}", "SSE.Controllers.LeftMenu.textSearch": "搜尋", "SSE.Controllers.LeftMenu.textSheet": "表格", "SSE.Controllers.LeftMenu.textValues": "值", "SSE.Controllers.LeftMenu.textWarning": "警告", "SSE.Controllers.LeftMenu.textWithin": "內", "SSE.Controllers.LeftMenu.textWorkbook": "工作簿", "SSE.Controllers.LeftMenu.txtUntitled": "無標題", "SSE.Controllers.LeftMenu.warnDownloadAs": "如果繼續以這種格式保存，則除文本外的所有功能都將丟失。<br>確定要繼續嗎？", "SSE.Controllers.Main.confirmMoveCellRange": "目標單元格範圍可以包含數據。繼續操作嗎？", "SSE.Controllers.Main.confirmPutMergeRange": "源數據包含合併的單元格。<br>在將它們粘貼到表中之前，它們已被合併。", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "標題行中的公式將被刪除並轉換為靜態文本。<br>是否繼續？", "SSE.Controllers.Main.convertationTimeoutText": "轉換逾時。", "SSE.Controllers.Main.criticalErrorExtText": "按“確定”返回文檔列表。", "SSE.Controllers.Main.criticalErrorTitle": "錯誤", "SSE.Controllers.Main.downloadErrorText": "下載失敗", "SSE.Controllers.Main.downloadTextText": "下載電子表格中...", "SSE.Controllers.Main.downloadTitleText": "下載電子表格中", "SSE.Controllers.Main.errNoDuplicates": "找不到重複的值。", "SSE.Controllers.Main.errorAccessDeny": "您嘗試進行未被授權的動作。<br> 請聯繫您的文件伺服器主機的管理者。", "SSE.Controllers.Main.errorArgsRange": "輸入的公式中有錯誤。<br>使用了錯誤的參數範圍。", "SSE.Controllers.Main.errorAutoFilterChange": "不允許執行此操作，因為它試圖移動工作表中表格中的單元格。", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "無法移動表格的一部分，因此無法對所選單元格執行該操作。<br>選擇另一個數據范圍，以使整個表格移動，然後重試。", "SSE.Controllers.Main.errorAutoFilterDataRange": "無法對所選單元格範圍執行此操作。<br>選擇與現有單元格範圍不同的統一數據范圍，然後重試。", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "由於該區域包含已過濾的單元格，因此無法執行該操作。<br>請取消隱藏已過濾的元素，然後重試。", "SSE.Controllers.Main.errorBadImageUrl": "不正確的圖像 URL", "SSE.Controllers.Main.errorCannotUngroup": "無法取消分組。要開始大綱，請選擇詳細信息行或列並將其分組。", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "您無法在受保護的工作表使用這個指令，若要使用這個指令，請取消保護該工作表。<br>您可能需要輸入密碼。", "SSE.Controllers.Main.errorChangeArray": "您不能更改數組的一部分。", "SSE.Controllers.Main.errorChangeFilteredRange": "這將更改工作表上的篩選範圍。<br>要完成此任務，請刪除“自動篩選”。", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "嘗試更改的單元格或圖表位於受保護的工作表上。<br>如要更改請把工作表解鎖。會有可能要修您輸入簿密碼。", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "服務器連接丟失。該文檔目前無法編輯。", "SSE.Controllers.Main.errorConnectToServer": "此文件無法儲存。請檢查連線設定或聯絡您的管理者 <br> 當您點選'OK'按鈕, 您將會被提示來進行此文件的下載。", "SSE.Controllers.Main.errorCopyMultiselectArea": "此命令不能用於多個選擇。<br>選擇單個範圍，然後重試。", "SSE.Controllers.Main.errorCountArg": "輸入的公式中有錯誤。<br>使用了錯誤的參數數量。", "SSE.Controllers.Main.errorCountArgExceed": "輸入的公式中有錯誤。<br>參數數超出。", "SSE.Controllers.Main.errorCreateDefName": "由於其中一些正在被編輯，因此目前無法編輯現有命名範圍，也無法創建新的命名範圍。", "SSE.Controllers.Main.errorDatabaseConnection": "外部錯誤。<br>數據庫連接錯誤。如果錯誤仍然存在，請聯繫支持。", "SSE.Controllers.Main.errorDataEncrypted": "已收到加密的更改，無法解密。", "SSE.Controllers.Main.errorDataRange": "不正確的資料範圍", "SSE.Controllers.Main.errorDataValidate": "您輸入的值無效。<br>用戶具有可以在此單元格中輸入的限制值。", "SSE.Controllers.Main.errorDefaultMessage": "錯誤編號：%1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "您正嘗試刪除包含鎖定單元格的欄。在工作表受保護的狀態下，含鎖定單元格是不能被刪除的。<br>如要刪除含鎖定單元格請把工作表解鎖。會有可能要修您輸入簿密碼。", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "您正嘗試刪除包含鎖定單元格的行。在工作表受保護的狀態下，含鎖定單元格是不能被刪除的。<br>如要刪除含鎖定單元格請把工作表解鎖。會有可能要修您輸入簿密碼。", "SSE.Controllers.Main.errorEditingDownloadas": "在處理文檔期間發生錯誤。<br>使用“下載為”選項將文件備份副本保存到計算機硬碟驅動器中。", "SSE.Controllers.Main.errorEditingSaveas": "使用文檔期間發生錯誤。<br>使用“另存為...”選項將文件備份副本保存到計算機硬碟驅動器中。", "SSE.Controllers.Main.errorEditView": "由於其中一些正在被編輯，因此目前無法編輯現有圖紙視圖，也無法創建新圖紙視圖。", "SSE.Controllers.Main.errorEmailClient": "找不到電子郵件客戶端。", "SSE.Controllers.Main.errorFilePassProtect": "該文件受密碼保護，無法打開。", "SSE.Controllers.Main.errorFileRequest": "外部錯誤。<br>文件請求錯誤。如果錯誤仍然存在，請聯繫支持。", "SSE.Controllers.Main.errorFileSizeExceed": "此檔案超過這一主機限制的大小<br> 進一步資訊，請聯絡您的文件服務主機的管理者。", "SSE.Controllers.Main.errorFileVKey": "外部錯誤。<br>安全密鑰不正確。如果錯誤仍然存在，請聯繫支持。", "SSE.Controllers.Main.errorFillRange": "無法填充所選的單元格範圍。<br>所有合併的單元格必須具有相同的大小。", "SSE.Controllers.Main.errorForceSave": "保存文件時發生錯誤。請使用“下載為”選項將文件保存到電腦機硬碟中，或稍後再試。", "SSE.Controllers.Main.errorFormulaName": "輸入的公式錯誤。<br>使用了錯誤的公式名稱。", "SSE.Controllers.Main.errorFormulaParsing": "解析公式時發生內部錯誤。", "SSE.Controllers.Main.errorFrmlMaxLength": "公式的長度超過了8192個字符的限制。<br>請對其進行編輯，然後重試。", "SSE.Controllers.Main.errorFrmlMaxReference": "您無法輸入此公式，因為它具有太多的值，<br>單元格引用和/或名稱。", "SSE.Controllers.Main.errorFrmlMaxTextLength": "公式中的文本值限制為255個字符。<br>使用CONCATENATE函數或串聯運算符（＆）。", "SSE.Controllers.Main.errorFrmlWrongReferences": "該功能引用的表格不存在。<br>請檢查數據，然後重試。", "SSE.Controllers.Main.errorFTChangeTableRangeError": "無法完成所選單元格範圍的操作。<br>選擇一個範圍，以使第一表行位於同一行上，並且結果表與當前表重疊。", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "無法完成所選單元格範圍的操作。<br>選擇一個不包含其他表的範圍。", "SSE.Controllers.Main.errorInvalidRef": "輸入正確的選擇名稱或有效參考。", "SSE.Controllers.Main.errorKeyEncrypt": "未知密鑰描述符", "SSE.Controllers.Main.errorKeyExpire": "密鑰描述符已過期", "SSE.Controllers.Main.errorLabledColumnsPivot": "若要創建數據透視表，請使用組織為帶有標籤列的列表的數據。", "SSE.Controllers.Main.errorLoadingFont": "字體未加載。<br>請聯繫文件服務器管理員。", "SSE.Controllers.Main.errorLocationOrDataRangeError": "位置或數據范圍的引用無效。", "SSE.Controllers.Main.errorLockedAll": "該工作表已被另一位用戶鎖定，因此無法完成該操作。", "SSE.Controllers.Main.errorLockedCellPivot": "您不能在數據透視表中更改數據。", "SSE.Controllers.Main.errorLockedWorksheetRename": "該工作表目前無法重命名，因為它正在被其他用戶重命名", "SSE.Controllers.Main.errorMaxPoints": "每個圖表的最大串聯點數為4096。", "SSE.Controllers.Main.errorMoveRange": "無法更改合併單元格的一部分", "SSE.Controllers.Main.errorMoveSlicerError": "無法將表切片器從一個工作簿複製到另一工作簿。<br>通過選擇整個表和切片器，再試一次。", "SSE.Controllers.Main.errorMultiCellFormula": "表中不允許使用多單元格數組公式。", "SSE.Controllers.Main.errorNoDataToParse": "沒有選擇要解析的數據。", "SSE.Controllers.Main.errorOpenWarning": "其中一個文件公式超過了8192個字符的限制。<br>該公式已被刪除。", "SSE.Controllers.Main.errorOperandExpected": "輸入的函數語法不正確。請檢查您是否缺少括號之一-'（'或'）'。", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "密碼錯誤。<br>核實蓋帽封鎖鍵關閉並且是肯定使用正確資本化。", "SSE.Controllers.Main.errorPasteMaxRange": "複製和貼上區域不匹配。<br>請選擇一個大小相同的區域，或單擊行中的第一個單元格以粘貼複製的單元格。", "SSE.Controllers.Main.errorPasteMultiSelect": "無法對多範圍選擇執行此操作。<br>請選但範圍重新审理 。", "SSE.Controllers.Main.errorPasteSlicerError": "表切片器無法從一個工作簿複製到另一工作簿。", "SSE.Controllers.Main.errorPivotGroup": "不能進行分組", "SSE.Controllers.Main.errorPivotOverlap": "數據透視表報表不能與表重疊。", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "数据透视表未使用基本数据保存。<br>請使用《更新》按鍵更新報表。", "SSE.Controllers.Main.errorPrintMaxPagesCount": "在此程式的版本中，一次最多不能列印超過1500頁。<br>此限制將在以後的版本中刪除。", "SSE.Controllers.Main.errorProcessSaveResult": "保存失敗", "SSE.Controllers.Main.errorServerVersion": "編輯器版本已更新。該頁面將被重新加載以應用更改。", "SSE.Controllers.Main.errorSessionAbsolute": "此文件編輯的會期已經過時。請重新載入此頁面。", "SSE.Controllers.Main.errorSessionIdle": "此文件已經在編輯狀態有很長時間, 請重新載入此頁面。", "SSE.Controllers.Main.errorSessionToken": "與服務器的連接已中斷。請重新加載頁面。", "SSE.Controllers.Main.errorSetPassword": "無法設定密碼。", "SSE.Controllers.Main.errorSingleColumnOrRowError": "位置引用無效，因單元格並非都在同一列或行中。<br>請選同列或行中的單元格。", "SSE.Controllers.Main.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "SSE.Controllers.Main.errorToken": "文檔安全令牌的格式不正確。<br>請與您的Document Server管理員聯繫。", "SSE.Controllers.Main.errorTokenExpire": "文檔安全令牌已過期。<br>請與您的Document Server管理員聯繫。", "SSE.Controllers.Main.errorUnexpectedGuid": "外部錯誤。<br>意外的GUID。如果錯誤仍然存在，請聯繫支持。", "SSE.Controllers.Main.errorUpdateVersion": "文件版本已更改。該頁面將重新加載。", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internet連接已恢復，文件版本已更改。<br>在繼續工作之前，您需要下載文件或複制其內容以確保沒有丟失，然後重新加載此頁面。", "SSE.Controllers.Main.errorUserDrop": "目前無法存取該文件。", "SSE.Controllers.Main.errorUsersExceed": "超出了定價計劃所允許的用戶數量", "SSE.Controllers.Main.errorViewerDisconnect": "連線失敗。您仍然可以查看該檔案，<br>但在恢復連接並重新加載頁面之前將無法下載或列印該檔案。", "SSE.Controllers.Main.errorWrongBracketsCount": "輸入的公式錯誤。<br>使用了錯誤的括號。", "SSE.Controllers.Main.errorWrongOperator": "輸入的公式中有錯誤。使用了錯誤的運算符。<br>請更正錯誤。", "SSE.Controllers.Main.errorWrongPassword": "密碼錯誤", "SSE.Controllers.Main.errRemDuplicates": "找到和刪除的重複值：{0}，剩餘的唯一值：{1}。", "SSE.Controllers.Main.leavePageText": "您尚未在此電子表格中保存更改。點擊“停留在此頁面上”，然後點擊“保存”以保存它們。點擊“離開此頁面”以放棄所有未保存的更改。", "SSE.Controllers.Main.leavePageTextOnClose": "該文檔中所有未保存的更改都將丟失。<br>單擊“取消”，然後單擊“保存”以保存它們。單擊“確定”，放棄所有未保存的更改。", "SSE.Controllers.Main.loadFontsTextText": "加載數據中...", "SSE.Controllers.Main.loadFontsTitleText": "加載數據中", "SSE.Controllers.Main.loadFontTextText": "加載數據中...", "SSE.Controllers.Main.loadFontTitleText": "加載數據中", "SSE.Controllers.Main.loadImagesTextText": "正在載入圖片...", "SSE.Controllers.Main.loadImagesTitleText": "正在載入圖片", "SSE.Controllers.Main.loadImageTextText": "正在載入圖片...", "SSE.Controllers.Main.loadImageTitleText": "正在載入圖片", "SSE.Controllers.Main.loadingDocumentTitleText": "加載電子表格", "SSE.Controllers.Main.notcriticalErrorTitle": "警告", "SSE.Controllers.Main.openErrorText": "開啟檔案時發生錯誤", "SSE.Controllers.Main.openTextText": "打開電子表格...", "SSE.Controllers.Main.openTitleText": "打開電子表格", "SSE.Controllers.Main.pastInMergeAreaError": "無法更改合併單元格的一部分", "SSE.Controllers.Main.printTextText": "列印電子表格...", "SSE.Controllers.Main.printTitleText": "列印電子表格", "SSE.Controllers.Main.reloadButtonText": "重新載入頁面", "SSE.Controllers.Main.requestEditFailedMessageText": "有人正在編輯此文檔。請稍後再試。", "SSE.Controllers.Main.requestEditFailedTitleText": "存取被拒", "SSE.Controllers.Main.saveErrorText": "儲存檔案時發生錯誤", "SSE.Controllers.Main.saveErrorTextDesktop": "無法保存或創建此文件。<br>可能的原因是：<br> 1。該文件是只讀的。 <br> 2。該文件正在由其他用戶編輯。 <br> 3。磁碟已滿或損壞。", "SSE.Controllers.Main.saveTextText": "保存電子表格...", "SSE.Controllers.Main.saveTitleText": "保存電子表格", "SSE.Controllers.Main.scriptLoadError": "連接速度太慢，某些組件無法加載。請重新加載頁面。", "SSE.Controllers.Main.textAnonymous": "匿名", "SSE.Controllers.Main.textApplyAll": "適用於所有方程式", "SSE.Controllers.Main.textBuyNow": "訪問網站", "SSE.Controllers.Main.textChangesSaved": "所有更改已保存", "SSE.Controllers.Main.textClose": "關閉", "SSE.Controllers.Main.textCloseTip": "點擊關閉提示", "SSE.Controllers.Main.textConfirm": "確認", "SSE.Controllers.Main.textContactUs": "聯絡銷售人員", "SSE.Controllers.Main.textConvertEquation": "該方程式是使用不再受支持的方程式編輯器的舊版本創建的。要對其進行編輯，請將等式轉換為Office Math ML格式。<br>立即轉換？", "SSE.Controllers.Main.textCustomLoader": "請注意，根據許可條款，您無權更換裝載機。<br>請聯繫我們的銷售部門以獲取報價。", "SSE.Controllers.Main.textDisconnect": "失去網絡連接", "SSE.Controllers.Main.textFillOtherRows": "填滿其他行", "SSE.Controllers.Main.textFormulaFilledAllRows": "函數已填寫 {0} 有資料的行。正在填寫其他空白行，請稍待。", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "函數已填寫了前 {0} 行。正在填寫其它行數，請稍待。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "為了節省系統記憶體，函數只填寫了前 {0} 個有資料的行。此工作表裡還有 {1} 個有資料的行。您可以手動填寫。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "為了節省系統記憶體，函數只填寫了前 {0} 行。此工作表裡的其他行並無資料。", "SSE.Controllers.Main.textGuest": "來賓帳戶", "SSE.Controllers.Main.textHasMacros": "此檔案包含自動macros。<br>是否要運行macros？", "SSE.Controllers.Main.textLearnMore": "了解更多", "SSE.Controllers.Main.textLoadingDocument": "加載電子表格", "SSE.Controllers.Main.textLongName": "輸入少於128個字符的名稱。", "SSE.Controllers.Main.textNeedSynchronize": "您有更新", "SSE.Controllers.Main.textNo": "沒有", "SSE.Controllers.Main.textNoLicenseTitle": "達到許可限制", "SSE.Controllers.Main.textPaidFeature": "付費功能", "SSE.Controllers.Main.textPleaseWait": "該操作可能花費比預期更多的時間。請耐心等待...", "SSE.Controllers.Main.textReconnect": "連線恢復", "SSE.Controllers.Main.textRemember": "記住我對所有文件的選擇", "SSE.Controllers.Main.textRememberMacros": "記住我所有巨集的選擇", "SSE.Controllers.Main.textRenameError": "使用者名稱無法留空。", "SSE.Controllers.Main.textRenameLabel": "輸入合作名稱", "SSE.Controllers.Main.textRequestMacros": "有一個巨集指令要求連結至URL。是否允許該要求至%1?", "SSE.Controllers.Main.textShape": "形狀", "SSE.Controllers.Main.textStrict": "嚴格模式", "SSE.Controllers.Main.textText": "文字", "SSE.Controllers.Main.textTryUndoRedo": "快速共同編輯模式禁用了“撤消/重做”功能。<br>單擊“嚴格模式”按鈕切換到“嚴格共同編輯”模式以編輯文件而不會受到其他用戶的干擾，並且僅在保存後發送更改他們。您可以使用編輯器的“進階”設置在共同編輯模式之間切換。", "SSE.Controllers.Main.textTryUndoRedoWarn": "在快速共同編輯模式下，撤消/重做功能被禁用。", "SSE.Controllers.Main.textYes": "是", "SSE.Controllers.Main.titleLicenseExp": "證件過期", "SSE.Controllers.Main.titleServerVersion": "編輯器已更新", "SSE.Controllers.Main.txtAccent": "強調", "SSE.Controllers.Main.txtAll": "（所有）", "SSE.Controllers.Main.txtArt": "在這輸入文字", "SSE.Controllers.Main.txtBasicShapes": "基本形狀", "SSE.Controllers.Main.txtBlank": "（空白）", "SSE.Controllers.Main.txtButtons": "按鈕", "SSE.Controllers.Main.txtByField": "第％1個，共％2個", "SSE.Controllers.Main.txtCallouts": "標註", "SSE.Controllers.Main.txtCharts": "圖表", "SSE.Controllers.Main.txtClearFilter": "清除過濾器", "SSE.Controllers.Main.txtColLbls": "列標籤", "SSE.Controllers.Main.txtColumn": "欄", "SSE.Controllers.Main.txtConfidential": "機密", "SSE.Controllers.Main.txtDate": "日期", "SSE.Controllers.Main.txtDays": "天", "SSE.Controllers.Main.txtDiagramTitle": "圖表標題", "SSE.Controllers.Main.txtEditingMode": "設定編輯模式...", "SSE.Controllers.Main.txtErrorLoadHistory": "歷史記錄加載失敗", "SSE.Controllers.Main.txtFiguredArrows": "圖箭", "SSE.Controllers.Main.txtFile": "檔案", "SSE.Controllers.Main.txtGrandTotal": "累計", "SSE.Controllers.Main.txtGroup": "進行分組", "SSE.Controllers.Main.txtHours": "小時", "SSE.Controllers.Main.txtLines": "線", "SSE.Controllers.Main.txtMath": "數學", "SSE.Controllers.Main.txtMinutes": "分鐘", "SSE.Controllers.Main.txtMonths": "月", "SSE.Controllers.Main.txtMultiSelect": "多選", "SSE.Controllers.Main.txtOr": "1%或2%", "SSE.Controllers.Main.txtPage": "頁面", "SSE.Controllers.Main.txtPageOf": "第％1頁，共％2頁", "SSE.Controllers.Main.txtPages": "頁", "SSE.Controllers.Main.txtPreparedBy": "編制", "SSE.Controllers.Main.txtPrintArea": "列印區域", "SSE.Controllers.Main.txtQuarter": "季度", "SSE.Controllers.Main.txtQuarters": "季度", "SSE.Controllers.Main.txtRectangles": "長方形", "SSE.Controllers.Main.txtRow": "行", "SSE.Controllers.Main.txtRowLbls": "行標籤", "SSE.Controllers.Main.txtSeconds": "秒數", "SSE.Controllers.Main.txtSeries": "系列", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "線路標註1（邊框和強調欄）", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "線路標註2（邊框和強調欄）", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "線路標註3（邊框和強調欄）", "SSE.Controllers.Main.txtShape_accentCallout1": "線路標註1（強調欄）", "SSE.Controllers.Main.txtShape_accentCallout2": "線路標註2（強調欄）", "SSE.Controllers.Main.txtShape_accentCallout3": "線路標註3（強調欄）", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "後退或上一步按鈕", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "開始按鈕", "SSE.Controllers.Main.txtShape_actionButtonBlank": "空白按鈕", "SSE.Controllers.Main.txtShape_actionButtonDocument": "文件按鈕", "SSE.Controllers.Main.txtShape_actionButtonEnd": "結束按鈕", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "前進或後退按鈕", "SSE.Controllers.Main.txtShape_actionButtonHelp": "幫助按鈕", "SSE.Controllers.Main.txtShape_actionButtonHome": "首頁按鈕", "SSE.Controllers.Main.txtShape_actionButtonInformation": "信息按鈕", "SSE.Controllers.Main.txtShape_actionButtonMovie": "電影按鈕", "SSE.Controllers.Main.txtShape_actionButtonReturn": "返回按鈕", "SSE.Controllers.Main.txtShape_actionButtonSound": "聲音按鈕", "SSE.Controllers.Main.txtShape_arc": "弧", "SSE.Controllers.Main.txtShape_bentArrow": "彎曲箭頭", "SSE.Controllers.Main.txtShape_bentConnector5": "彎頭接頭", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "彎頭箭頭連接器", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "彎頭雙箭頭連接器", "SSE.Controllers.Main.txtShape_bentUpArrow": "向上彎曲箭頭", "SSE.Controllers.Main.txtShape_bevel": "斜角", "SSE.Controllers.Main.txtShape_blockArc": "圓弧", "SSE.Controllers.Main.txtShape_borderCallout1": "線路標註1", "SSE.Controllers.Main.txtShape_borderCallout2": "線路標註2", "SSE.Controllers.Main.txtShape_borderCallout3": "線路標註3", "SSE.Controllers.Main.txtShape_bracePair": "雙括號", "SSE.Controllers.Main.txtShape_callout1": "線路標註1（無邊框）", "SSE.Controllers.Main.txtShape_callout2": "線路標註2（無邊框）", "SSE.Controllers.Main.txtShape_callout3": "線路標註3（無邊框）", "SSE.Controllers.Main.txtShape_can": "能夠", "SSE.Controllers.Main.txtShape_chevron": "雪佛龍", "SSE.Controllers.Main.txtShape_chord": "弦", "SSE.Controllers.Main.txtShape_circularArrow": "圓形箭頭", "SSE.Controllers.Main.txtShape_cloud": "雲端", "SSE.Controllers.Main.txtShape_cloudCallout": "雲標註", "SSE.Controllers.Main.txtShape_corner": "角", "SSE.Controllers.Main.txtShape_cube": "　立方體", "SSE.Controllers.Main.txtShape_curvedConnector3": "彎曲連接器", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "彎曲箭頭連接器", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "彎曲雙箭頭連接器", "SSE.Controllers.Main.txtShape_curvedDownArrow": "彎曲的向下箭頭", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "彎曲的左箭頭", "SSE.Controllers.Main.txtShape_curvedRightArrow": "彎曲的右箭頭", "SSE.Controllers.Main.txtShape_curvedUpArrow": "彎曲的向上箭頭", "SSE.Controllers.Main.txtShape_decagon": "十邊形", "SSE.Controllers.Main.txtShape_diagStripe": "斜條紋", "SSE.Controllers.Main.txtShape_diamond": "鑽石", "SSE.Controllers.Main.txtShape_dodecagon": "十二邊形", "SSE.Controllers.Main.txtShape_donut": "甜甜圈", "SSE.Controllers.Main.txtShape_doubleWave": "雙波", "SSE.Controllers.Main.txtShape_downArrow": "下箭頭", "SSE.Controllers.Main.txtShape_downArrowCallout": "向下箭頭標註", "SSE.Controllers.Main.txtShape_ellipse": "橢圓", "SSE.Controllers.Main.txtShape_ellipseRibbon": "彎下絲帶", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "向上彎曲絲帶", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程圖：替代過程", "SSE.Controllers.Main.txtShape_flowChartCollate": "流程圖：整理", "SSE.Controllers.Main.txtShape_flowChartConnector": "流程圖：連接器", "SSE.Controllers.Main.txtShape_flowChartDecision": "流程圖：決策", "SSE.Controllers.Main.txtShape_flowChartDelay": "流程圖：延遲", "SSE.Controllers.Main.txtShape_flowChartDisplay": "流程圖：顯示", "SSE.Controllers.Main.txtShape_flowChartDocument": "流程圖：文件", "SSE.Controllers.Main.txtShape_flowChartExtract": "流程圖：提取", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "流程圖：數據", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "流程圖：內部存儲", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程圖：磁碟", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程圖：直接存取存儲", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "流程圖：順序存取存儲", "SSE.Controllers.Main.txtShape_flowChartManualInput": "流程圖：手動輸入", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "流程圖：手動操作", "SSE.Controllers.Main.txtShape_flowChartMerge": "流程圖：合併", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "流程圖：多文檔", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程圖：頁外連接器", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程圖：存儲的數據", "SSE.Controllers.Main.txtShape_flowChartOr": "流程圖：或", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程圖：預定義流程", "SSE.Controllers.Main.txtShape_flowChartPreparation": "流程圖：準備", "SSE.Controllers.Main.txtShape_flowChartProcess": "流程圖：流程", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "流程圖：卡", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "流程圖：穿孔紙帶", "SSE.Controllers.Main.txtShape_flowChartSort": "流程圖：排序", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "流程圖：求和結點", "SSE.Controllers.Main.txtShape_flowChartTerminator": "流程圖：終結者", "SSE.Controllers.Main.txtShape_foldedCorner": "折角", "SSE.Controllers.Main.txtShape_frame": "框", "SSE.Controllers.Main.txtShape_halfFrame": "半框", "SSE.Controllers.Main.txtShape_heart": "心", "SSE.Controllers.Main.txtShape_heptagon": "七邊形", "SSE.Controllers.Main.txtShape_hexagon": "六邊形", "SSE.Controllers.Main.txtShape_homePlate": "五角形", "SSE.Controllers.Main.txtShape_horizontalScroll": "水平滾動", "SSE.Controllers.Main.txtShape_irregularSeal1": "爆炸1", "SSE.Controllers.Main.txtShape_irregularSeal2": "爆炸2", "SSE.Controllers.Main.txtShape_leftArrow": "左箭頭", "SSE.Controllers.Main.txtShape_leftArrowCallout": "向左箭頭標註", "SSE.Controllers.Main.txtShape_leftBrace": "左括號", "SSE.Controllers.Main.txtShape_leftBracket": "左括號", "SSE.Controllers.Main.txtShape_leftRightArrow": "左右箭頭", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭頭標註", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "左右上箭頭", "SSE.Controllers.Main.txtShape_leftUpArrow": "左上箭頭", "SSE.Controllers.Main.txtShape_lightningBolt": "閃電", "SSE.Controllers.Main.txtShape_line": "線", "SSE.Controllers.Main.txtShape_lineWithArrow": "箭頭", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "雙箭頭", "SSE.Controllers.Main.txtShape_mathDivide": "分裂", "SSE.Controllers.Main.txtShape_mathEqual": "等於", "SSE.Controllers.Main.txtShape_mathMinus": "減去", "SSE.Controllers.Main.txtShape_mathMultiply": "乘", "SSE.Controllers.Main.txtShape_mathNotEqual": "不平等", "SSE.Controllers.Main.txtShape_mathPlus": "加", "SSE.Controllers.Main.txtShape_moon": "月亮", "SSE.Controllers.Main.txtShape_noSmoking": "“否”符號", "SSE.Controllers.Main.txtShape_notchedRightArrow": "缺口右箭頭", "SSE.Controllers.Main.txtShape_octagon": "八邊形", "SSE.Controllers.Main.txtShape_parallelogram": "平行四邊形", "SSE.Controllers.Main.txtShape_pentagon": "五角形", "SSE.Controllers.Main.txtShape_pie": "餅", "SSE.Controllers.Main.txtShape_plaque": "簽名", "SSE.Controllers.Main.txtShape_plus": "加", "SSE.Controllers.Main.txtShape_polyline1": "塗", "SSE.Controllers.Main.txtShape_polyline2": "自由形式", "SSE.Controllers.Main.txtShape_quadArrow": "四箭頭", "SSE.Controllers.Main.txtShape_quadArrowCallout": "四箭頭標註", "SSE.Controllers.Main.txtShape_rect": "長方形", "SSE.Controllers.Main.txtShape_ribbon": "下絨帶", "SSE.Controllers.Main.txtShape_ribbon2": "上色帶", "SSE.Controllers.Main.txtShape_rightArrow": "右箭頭", "SSE.Controllers.Main.txtShape_rightArrowCallout": "右箭頭標註", "SSE.Controllers.Main.txtShape_rightBrace": "右括號", "SSE.Controllers.Main.txtShape_rightBracket": "右括號", "SSE.Controllers.Main.txtShape_round1Rect": "圓形單角矩形", "SSE.Controllers.Main.txtShape_round2DiagRect": "圓斜角矩形", "SSE.Controllers.Main.txtShape_round2SameRect": "圓同一邊角矩形", "SSE.Controllers.Main.txtShape_roundRect": "圓角矩形", "SSE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "SSE.Controllers.Main.txtShape_smileyFace": "笑臉", "SSE.Controllers.Main.txtShape_snip1Rect": "剪斷單角矩形", "SSE.Controllers.Main.txtShape_snip2DiagRect": "剪裁對角線矩形", "SSE.Controllers.Main.txtShape_snip2SameRect": "剪斷同一邊角矩形", "SSE.Controllers.Main.txtShape_snipRoundRect": "剪斷和圓形單角矩形", "SSE.Controllers.Main.txtShape_spline": "曲線", "SSE.Controllers.Main.txtShape_star10": "十點星", "SSE.Controllers.Main.txtShape_star12": "十二點星", "SSE.Controllers.Main.txtShape_star16": "十六點星", "SSE.Controllers.Main.txtShape_star24": "24點星", "SSE.Controllers.Main.txtShape_star32": "32點星", "SSE.Controllers.Main.txtShape_star4": "4點星", "SSE.Controllers.Main.txtShape_star5": "5點星", "SSE.Controllers.Main.txtShape_star6": "6點星", "SSE.Controllers.Main.txtShape_star7": "7點星", "SSE.Controllers.Main.txtShape_star8": "8點星", "SSE.Controllers.Main.txtShape_stripedRightArrow": "條紋右箭頭", "SSE.Controllers.Main.txtShape_sun": "太陽", "SSE.Controllers.Main.txtShape_teardrop": "淚珠", "SSE.Controllers.Main.txtShape_textRect": "文字框", "SSE.Controllers.Main.txtShape_trapezoid": "梯形", "SSE.Controllers.Main.txtShape_triangle": "三角形", "SSE.Controllers.Main.txtShape_upArrow": "向上箭頭", "SSE.Controllers.Main.txtShape_upArrowCallout": "向上箭頭標註", "SSE.Controllers.Main.txtShape_upDownArrow": "上下箭頭", "SSE.Controllers.Main.txtShape_uturnArrow": "掉頭箭頭", "SSE.Controllers.Main.txtShape_verticalScroll": "垂直滾動", "SSE.Controllers.Main.txtShape_wave": "波", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "橢圓形標註", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "矩形標註", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圓角矩形標註", "SSE.Controllers.Main.txtStarsRibbons": "星星和絲帶", "SSE.Controllers.Main.txtStyle_Bad": "壞", "SSE.Controllers.Main.txtStyle_Calculation": "計算", "SSE.Controllers.Main.txtStyle_Check_Cell": "檢查單元格", "SSE.Controllers.Main.txtStyle_Comma": "逗號", "SSE.Controllers.Main.txtStyle_Currency": "幣別", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "解釋性文字", "SSE.Controllers.Main.txtStyle_Good": "好", "SSE.Controllers.Main.txtStyle_Heading_1": "標題 1", "SSE.Controllers.Main.txtStyle_Heading_2": "標題 2", "SSE.Controllers.Main.txtStyle_Heading_3": "標題 3", "SSE.Controllers.Main.txtStyle_Heading_4": "標題 4", "SSE.Controllers.Main.txtStyle_Input": "輸入", "SSE.Controllers.Main.txtStyle_Linked_Cell": "已連接的單元格", "SSE.Controllers.Main.txtStyle_Neutral": "中立", "SSE.Controllers.Main.txtStyle_Normal": "標準", "SSE.Controllers.Main.txtStyle_Note": "備註", "SSE.Controllers.Main.txtStyle_Output": "輸出量", "SSE.Controllers.Main.txtStyle_Percent": "百分比", "SSE.Controllers.Main.txtStyle_Title": "標題", "SSE.Controllers.Main.txtStyle_Total": "總計", "SSE.Controllers.Main.txtStyle_Warning_Text": "警告文字", "SSE.Controllers.Main.txtTab": "標籤", "SSE.Controllers.Main.txtTable": "表格", "SSE.Controllers.Main.txtTime": "時間", "SSE.Controllers.Main.txtUnlock": "開鎖", "SSE.Controllers.Main.txtUnlockRange": "範圍解鎖", "SSE.Controllers.Main.txtUnlockRangeDescription": "如要改變範圍，請輸入密碼：", "SSE.Controllers.Main.txtUnlockRangeWarning": "試圖改變的範圍受密碼保護。", "SSE.Controllers.Main.txtValues": "值", "SSE.Controllers.Main.txtXAxis": "X軸", "SSE.Controllers.Main.txtYAxis": "Y軸", "SSE.Controllers.Main.txtYears": "年", "SSE.Controllers.Main.unknownErrorText": "未知錯誤。", "SSE.Controllers.Main.unsupportedBrowserErrorText": "不支援您的瀏覽器", "SSE.Controllers.Main.uploadDocExtMessage": "未知的文件格式。", "SSE.Controllers.Main.uploadDocFileCountMessage": "沒有文件上傳。", "SSE.Controllers.Main.uploadDocSizeMessage": "超出最大文檔大小限制。", "SSE.Controllers.Main.uploadImageExtMessage": "圖片格式未知。", "SSE.Controllers.Main.uploadImageFileCountMessage": "沒有上傳圖片。", "SSE.Controllers.Main.uploadImageSizeMessage": "圖像超出最大大小限制。最大大小為25MB。", "SSE.Controllers.Main.uploadImageTextText": "正在上傳圖片...", "SSE.Controllers.Main.uploadImageTitleText": "上載圖片", "SSE.Controllers.Main.waitText": "請耐心等待...", "SSE.Controllers.Main.warnBrowserIE9": "該應用程序在IE9上具有較低的功能。使用IE10或更高版本", "SSE.Controllers.Main.warnBrowserZoom": "瀏覽器當前的縮放設置不受完全支持。請按Ctrl + 0重置為預設縮放。", "SSE.Controllers.Main.warnLicenseExceeded": "您的系統已經達到同時編輯連線的 %1 編輯者。只能以檢視模式開啟此文件。<br> 進一步訊息, 請聯繫您的管理者。", "SSE.Controllers.Main.warnLicenseExp": "您的授權證已過期.<br>請更新您的授權證並重新整理頁面。", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "授權過期<br>您已沒有編輯文件功能的授權<br> 請與您的管理者聯繫。", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "授權證書需要更新<br> 您只有部分的文件編輯功能的存取權限<br>請與您的管理者聯繫來取得完整的存取權限。", "SSE.Controllers.Main.warnLicenseUsersExceeded": "您已達到％1個編輯器的用戶限制。請與您的管理員聯繫以了解更多信息。", "SSE.Controllers.Main.warnNoLicense": "您的系統已經達到同時編輯連線的 %1 編輯者。只能以檢視模式開啟此文件。<br> 請聯繫 %1 銷售團隊來取得個人升級的需求。", "SSE.Controllers.Main.warnNoLicenseUsers": "您已達到％1個編輯器的用戶限制。與％1銷售團隊聯繫以了解個人升級條款。", "SSE.Controllers.Main.warnProcessRightsChange": "您被拒絕編輯文件的權利。", "SSE.Controllers.Print.strAllSheets": "所有工作表", "SSE.Controllers.Print.textFirstCol": "第一欄", "SSE.Controllers.Print.textFirstRow": "第一排", "SSE.Controllers.Print.textFrozenCols": "固定列", "SSE.Controllers.Print.textFrozenRows": "固定行", "SSE.Controllers.Print.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Controllers.Print.textNoRepeat": "不要重複", "SSE.Controllers.Print.textRepeat": "重複...", "SSE.Controllers.Print.textSelectRange": "選擇範圍", "SSE.Controllers.Print.textWarning": "警告", "SSE.Controllers.Print.txtCustom": "自訂", "SSE.Controllers.Print.warnCheckMargings": "邊界錯誤", "SSE.Controllers.Search.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Controllers.Search.textNoTextFound": "找不到您一直在搜索的數據。請調整您的搜索選項。", "SSE.Controllers.Search.textReplaceSkipped": "替換已完成。 {0}個事件被跳過。", "SSE.Controllers.Search.textReplaceSuccess": "搜尋完成。 {0}個符合結果已被取代", "SSE.Controllers.Statusbar.errorLastSheet": "工作簿必須至少具有一個可見的工作表。", "SSE.Controllers.Statusbar.errorRemoveSheet": "無法刪除工作表。", "SSE.Controllers.Statusbar.strSheet": "表格", "SSE.Controllers.Statusbar.textDisconnect": "<b>連線失敗</b><br>正在嘗試連線。請檢查網路連線設定。", "SSE.Controllers.Statusbar.textSheetViewTip": "您處於“圖紙視圖”模式。過濾器和排序僅對您和仍處於此視圖的用戶可見。", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "您處於“圖紙視圖”模式。過濾器僅對您和仍處於此視圖的用戶可見。", "SSE.Controllers.Statusbar.warnDeleteSheet": "所選的工作表可能包含數據。您確定要繼續嗎？", "SSE.Controllers.Statusbar.zoomText": "放大{0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "您要保存的字體在當前設備上不可用。<br>文本樣式將使用一種系統字體顯示，保存的字體將在可用時使用。<br>要繼續嗎？ ？", "SSE.Controllers.Toolbar.errorComboSeries": "新增組合圖表需選兩個以上的數據系列。", "SSE.Controllers.Toolbar.errorMaxRows": "錯誤！每個圖表的最大數據序列數為255", "SSE.Controllers.Toolbar.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "SSE.Controllers.Toolbar.textAccent": "強調", "SSE.Controllers.Toolbar.textBracket": "括號", "SSE.Controllers.Toolbar.textDirectional": "定向", "SSE.Controllers.Toolbar.textFontSizeErr": "輸入的值不正確。<br>請輸入1到409之間的數字值", "SSE.Controllers.Toolbar.textFraction": "分數", "SSE.Controllers.Toolbar.textFunction": "功能", "SSE.Controllers.Toolbar.textIndicator": "指标", "SSE.Controllers.Toolbar.textInsert": "插入", "SSE.Controllers.Toolbar.textIntegral": "積分", "SSE.Controllers.Toolbar.textLargeOperator": "大型運營商", "SSE.Controllers.Toolbar.textLimitAndLog": "極限和對數", "SSE.Controllers.Toolbar.textLongOperation": "運行時間長", "SSE.Controllers.Toolbar.textMatrix": "矩陣", "SSE.Controllers.Toolbar.textOperator": "經營者", "SSE.Controllers.Toolbar.textPivot": "數據透視表", "SSE.Controllers.Toolbar.textRadical": "激進單數", "SSE.Controllers.Toolbar.textRating": "收視率", "SSE.Controllers.Toolbar.textRecentlyUsed": "最近使用", "SSE.Controllers.Toolbar.textScript": "腳本", "SSE.Controllers.Toolbar.textShapes": "形狀", "SSE.Controllers.Toolbar.textSymbols": "符號", "SSE.Controllers.Toolbar.textWarning": "警告", "SSE.Controllers.Toolbar.txtAccent_Accent": "尖銳", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "上方的左右箭頭", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "上方的向左箭頭", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "上方向右箭頭", "SSE.Controllers.Toolbar.txtAccent_Bar": "槓", "SSE.Controllers.Toolbar.txtAccent_BarBot": "底橫槓", "SSE.Controllers.Toolbar.txtAccent_BarTop": "橫槓", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "盒裝公式（帶佔位符）", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "盒裝公式（示例）", "SSE.Controllers.Toolbar.txtAccent_Check": "檢查", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "底括號", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "大括號", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "向量A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "帶橫線的ABC", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x X或y的橫槓", "SSE.Controllers.Toolbar.txtAccent_DDDot": "三點", "SSE.Controllers.Toolbar.txtAccent_DDot": "雙點", "SSE.Controllers.Toolbar.txtAccent_Dot": "點", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "雙橫槓", "SSE.Controllers.Toolbar.txtAccent_Grave": "墓", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "下面的分組字符", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "上面的分組字符", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "上方的向左魚叉", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "右上方的魚叉", "SSE.Controllers.Toolbar.txtAccent_Hat": "帽子", "SSE.Controllers.Toolbar.txtAccent_Smile": "布雷夫", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "括號", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "帶分隔符的括號", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "帶分隔符的括號", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_Curve": "括號", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "帶分隔符的括號", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "案件（兩件條件）", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "案件（三件條件）", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "堆疊物件", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "堆疊物件", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "案件例子", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "二項式係數", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "二項式係數", "SSE.Controllers.Toolbar.txtBracket_Line": "括號", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "括號", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_LowLim": "括號", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_Round": "括號", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "帶分隔符的括號", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_Square": "括號", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括號", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括號", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括號", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "括號", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtBracket_UppLim": "括號", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "單括號", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "單括號", "SSE.Controllers.Toolbar.txtDeleteCells": "刪除單元格", "SSE.Controllers.Toolbar.txtExpand": "展開和排序", "SSE.Controllers.Toolbar.txtExpandSort": "選擇項旁邊的數據將不會排序。您是否要擴展選擇範圍以包括相鄰數據，還是僅對當前選定的單元格進行排序？", "SSE.Controllers.Toolbar.txtFractionDiagonal": "偏斜分數", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "SSE.Controllers.Toolbar.txtFractionHorizontal": "線性分數", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi超過2", "SSE.Controllers.Toolbar.txtFractionSmall": "小分數", "SSE.Controllers.Toolbar.txtFractionVertical": "堆積分數", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "反餘弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "雙曲餘弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "反正切函數", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "雙曲反正切函數", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "餘割函數反", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "雙曲反餘割函數", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "反割線功能", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "雙曲反正割函數", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "雙曲反正弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "反正切函數", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "雙曲反正切函數", "SSE.Controllers.Toolbar.txtFunction_Cos": "Cosine 函數", "SSE.Controllers.Toolbar.txtFunction_Cosh": "雙曲餘弦函數", "SSE.Controllers.Toolbar.txtFunction_Cot": "Cotangent 函數", "SSE.Controllers.Toolbar.txtFunction_Coth": "雙曲餘切函數", "SSE.Controllers.Toolbar.txtFunction_Csc": "餘割函數", "SSE.Controllers.Toolbar.txtFunction_Csch": "雙曲餘割函數", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "正弦波", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "切線公式", "SSE.Controllers.Toolbar.txtFunction_Sec": "正割功能", "SSE.Controllers.Toolbar.txtFunction_Sech": "雙曲正割函數", "SSE.Controllers.Toolbar.txtFunction_Sin": "正弦函數", "SSE.Controllers.Toolbar.txtFunction_Sinh": "雙曲正弦函數", "SSE.Controllers.Toolbar.txtFunction_Tan": "切線公式", "SSE.Controllers.Toolbar.txtFunction_Tanh": "雙曲正切函數", "SSE.Controllers.Toolbar.txtInsertCells": "插入單元格", "SSE.Controllers.Toolbar.txtIntegral": "積分", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "微分塞塔", "SSE.Controllers.Toolbar.txtIntegral_dx": "差分　x", "SSE.Controllers.Toolbar.txtIntegral_dy": "差分　y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "積分", "SSE.Controllers.Toolbar.txtIntegralDouble": "雙積分", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "雙積分", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "雙積分", "SSE.Controllers.Toolbar.txtIntegralOriented": "輪廓積分", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "輪廓積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "表面積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "表面積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "表面積分", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "輪廓積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "體積積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "體積積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "體積積分", "SSE.Controllers.Toolbar.txtIntegralSubSup": "積分", "SSE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重積分", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重積分", "SSE.Controllers.Toolbar.txtInvalidRange": "錯誤！無效的像元範圍", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "楔", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "聯產品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "聯產品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "聯產品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "聯產品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "聯產品", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "產品", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "聯合", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "交叉點", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交叉點", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交叉點", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交叉點", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交叉點", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "產品", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "產品", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "產品", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "產品", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "產品", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "聯合", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "聯合", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "聯合", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "聯合", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "聯合", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "限制例子", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大例子", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "限制", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "自然對數", "SSE.Controllers.Toolbar.txtLimitLog_Log": "對數", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "對數", "SSE.Controllers.Toolbar.txtLimitLog_Max": "最大", "SSE.Controllers.Toolbar.txtLimitLog_Min": "最低", "SSE.Controllers.Toolbar.txtLockSort": "您選材的比鄰有數據，可您更改權限不足。<br>是否繼續當前選材？", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "帶括號的空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "帶括號的空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "帶括號的空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "帶括號的空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基準點", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "中線點", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "對角點", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直點", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "稀疏矩陣", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "稀疏矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 單位矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 單位矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 單位矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 單位矩陣", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "下方的左右箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "上方的左右箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "下方的向左箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "上方的向左箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "上方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "冒號相等", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "產量", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta 收益", "SSE.Controllers.Toolbar.txtOperator_Definition": "等同於定義", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta 等於", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "下方的左右箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "上方的左右箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "下方的向左箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "上方的向左箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "上方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "等於 等於", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "負等於", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "加等於", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "測量者", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "激進", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "激進", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "平方根與度數", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "自由基度", "SSE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "SSE.Controllers.Toolbar.txtScriptCustom_1": "腳本", "SSE.Controllers.Toolbar.txtScriptCustom_2": "腳本", "SSE.Controllers.Toolbar.txtScriptCustom_3": "腳本", "SSE.Controllers.Toolbar.txtScriptCustom_4": "腳本", "SSE.Controllers.Toolbar.txtScriptSub": "下標", "SSE.Controllers.Toolbar.txtScriptSubSup": "下標-上標", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "左下標-上標", "SSE.Controllers.Toolbar.txtScriptSup": "上標", "SSE.Controllers.Toolbar.txtSorting": "排序", "SSE.Controllers.Toolbar.txtSortSelected": "排序已選擇項目", "SSE.Controllers.Toolbar.txtSymbol_about": "大約", "SSE.Controllers.Toolbar.txtSymbol_additional": "補充", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Ａ", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Α", "SSE.Controllers.Toolbar.txtSymbol_approx": "幾乎等於", "SSE.Controllers.Toolbar.txtSymbol_ast": "星號運算符", "SSE.Controllers.Toolbar.txtSymbol_beta": "貝塔", "SSE.Controllers.Toolbar.txtSymbol_beth": "賭注", "SSE.Controllers.Toolbar.txtSymbol_bullet": "項目點操作者", "SSE.Controllers.Toolbar.txtSymbol_cap": "交叉點", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "SSE.Controllers.Toolbar.txtSymbol_cdots": "中線水平省略號", "SSE.Controllers.Toolbar.txtSymbol_celsius": "攝氏度", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "大約等於", "SSE.Controllers.Toolbar.txtSymbol_cup": "聯合", "SSE.Controllers.Toolbar.txtSymbol_ddots": "右下斜省略號", "SSE.Controllers.Toolbar.txtSymbol_degree": "度", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "分裂標誌", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "下箭頭", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "空組集", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "等於", "SSE.Controllers.Toolbar.txtSymbol_equiv": "相同", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "存在", "SSE.Controllers.Toolbar.txtSymbol_factorial": "階乘", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏度", "SSE.Controllers.Toolbar.txtSymbol_forall": "對所有人", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "大於或等於", "SSE.Controllers.Toolbar.txtSymbol_gg": "比大得多", "SSE.Controllers.Toolbar.txtSymbol_greater": "更佳", "SSE.Controllers.Toolbar.txtSymbol_in": "元素", "SSE.Controllers.Toolbar.txtSymbol_inc": "增量", "SSE.Controllers.Toolbar.txtSymbol_infinity": "無限", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "拉姆達", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭頭", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭頭", "SSE.Controllers.Toolbar.txtSymbol_leq": "小於或等於", "SSE.Controllers.Toolbar.txtSymbol_less": "少於", "SSE.Controllers.Toolbar.txtSymbol_ll": "遠遠少於", "SSE.Controllers.Toolbar.txtSymbol_minus": "減去", "SSE.Controllers.Toolbar.txtSymbol_mp": "減加", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "不等於", "SSE.Controllers.Toolbar.txtSymbol_ni": "包含為成員", "SSE.Controllers.Toolbar.txtSymbol_not": "不簽名", "SSE.Controllers.Toolbar.txtSymbol_notexists": "不存在", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "SSE.Controllers.Toolbar.txtSymbol_percent": "百分比", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "加", "SSE.Controllers.Toolbar.txtSymbol_pm": "加減", "SSE.Controllers.Toolbar.txtSymbol_propto": "成比例", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "第四根", "SSE.Controllers.Toolbar.txtSymbol_qed": "證明結束", "SSE.Controllers.Toolbar.txtSymbol_rddots": "右上斜省略號", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭頭", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "激進標誌", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "因此", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "乘法符號", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭頭", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon 變體", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi 變體", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi變體", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho變體", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma 變體", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta變體", "SSE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略號", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "表格風格深色", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "表格風格淺色", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "屌格風中階", "SSE.Controllers.Toolbar.warnLongOperation": "您將要執行的操作可能需要花費大量時間才能完成。<br>您確定要繼續嗎？", "SSE.Controllers.Toolbar.warnMergeLostData": "只有左上角單元格中的數據會保留。<br>繼續嗎？", "SSE.Controllers.Viewport.textFreezePanes": "凍結窗格", "SSE.Controllers.Viewport.textFreezePanesShadow": "顯示固定面版視窗的陰影", "SSE.Controllers.Viewport.textHideFBar": "隱藏公式欄", "SSE.Controllers.Viewport.textHideGridlines": "隱藏網格線", "SSE.Controllers.Viewport.textHideHeadings": "隱藏標題", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "小數點分隔符", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "千位分隔符", "SSE.Views.AdvancedSeparatorDialog.textLabel": "用於識別數字數據的設置", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "文字識別符號", "SSE.Views.AdvancedSeparatorDialog.textTitle": "進階設定", "SSE.Views.AdvancedSeparatorDialog.txtNone": "（空）", "SSE.Views.AutoFilterDialog.btnCustomFilter": "自訂過濾", "SSE.Views.AutoFilterDialog.textAddSelection": "將當前選擇添加到過濾器", "SSE.Views.AutoFilterDialog.textEmptyItem": "{空白}", "SSE.Views.AutoFilterDialog.textSelectAll": "全選", "SSE.Views.AutoFilterDialog.textSelectAllResults": "選擇所有搜索結果", "SSE.Views.AutoFilterDialog.textWarning": "警告", "SSE.Views.AutoFilterDialog.txtAboveAve": "高於平均", "SSE.Views.AutoFilterDialog.txtBegins": "開始於...", "SSE.Views.AutoFilterDialog.txtBelowAve": "低於平均值", "SSE.Views.AutoFilterDialog.txtBetween": "之間...", "SSE.Views.AutoFilterDialog.txtClear": "清除", "SSE.Views.AutoFilterDialog.txtContains": "包含...", "SSE.Views.AutoFilterDialog.txtEmpty": "輸入單元格過濾器", "SSE.Views.AutoFilterDialog.txtEnds": "以。。結束", "SSE.Views.AutoFilterDialog.txtEquals": "等於...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "按單元格顏色過濾", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "按字體顏色過濾", "SSE.Views.AutoFilterDialog.txtGreater": "比...更大", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "大於或等於...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "標籤過濾器", "SSE.Views.AutoFilterDialog.txtLess": "小於...", "SSE.Views.AutoFilterDialog.txtLessEquals": "小於或等於...", "SSE.Views.AutoFilterDialog.txtNotBegins": "不以...", "SSE.Views.AutoFilterDialog.txtNotBetween": "不介於...", "SSE.Views.AutoFilterDialog.txtNotContains": "不含...", "SSE.Views.AutoFilterDialog.txtNotEnds": "不以結束...", "SSE.Views.AutoFilterDialog.txtNotEquals": "不等於...", "SSE.Views.AutoFilterDialog.txtNumFilter": "號碼過濾器", "SSE.Views.AutoFilterDialog.txtReapply": "重新申請", "SSE.Views.AutoFilterDialog.txtSortCellColor": "按單元格顏色排序", "SSE.Views.AutoFilterDialog.txtSortFontColor": "按字體顏色排序", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "最高到最低排序", "SSE.Views.AutoFilterDialog.txtSortLow2High": "從最低到最高排序", "SSE.Views.AutoFilterDialog.txtSortOption": "更多排序選項...", "SSE.Views.AutoFilterDialog.txtTextFilter": "文字過濾器", "SSE.Views.AutoFilterDialog.txtTitle": "過濾器", "SSE.Views.AutoFilterDialog.txtTop10": "前10名", "SSE.Views.AutoFilterDialog.txtValueFilter": "值過濾器", "SSE.Views.AutoFilterDialog.warnFilterError": "您必須在“值”區域中至少有一個字段才能應用值過濾器。", "SSE.Views.AutoFilterDialog.warnNoSelected": "您必須選擇至少一個值", "SSE.Views.CellEditor.textManager": "名稱管理員", "SSE.Views.CellEditor.tipFormula": "插入功能", "SSE.Views.CellRangeDialog.errorMaxRows": "錯誤！每個圖表的最大數據序列數為255", "SSE.Views.CellRangeDialog.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "SSE.Views.CellRangeDialog.txtEmpty": "這是必填欄", "SSE.Views.CellRangeDialog.txtInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.CellRangeDialog.txtTitle": "選擇數據范圍", "SSE.Views.CellSettings.strShrink": "縮小以適合", "SSE.Views.CellSettings.strWrap": "包覆文字", "SSE.Views.CellSettings.textAngle": "角度", "SSE.Views.CellSettings.textBackColor": "背景顏色", "SSE.Views.CellSettings.textBackground": "背景顏色", "SSE.Views.CellSettings.textBorderColor": "顏色", "SSE.Views.CellSettings.textBorders": "邊框樣式", "SSE.Views.CellSettings.textClearRule": "清除規則", "SSE.Views.CellSettings.textColor": "填充顏色", "SSE.Views.CellSettings.textColorScales": "色標", "SSE.Views.CellSettings.textCondFormat": "條件格式", "SSE.Views.CellSettings.textControl": "文字控制", "SSE.Views.CellSettings.textDataBars": "數據欄", "SSE.Views.CellSettings.textDirection": "方向", "SSE.Views.CellSettings.textFill": "填入", "SSE.Views.CellSettings.textForeground": "前景色", "SSE.Views.CellSettings.textGradient": "漸變點", "SSE.Views.CellSettings.textGradientColor": "顏色", "SSE.Views.CellSettings.textGradientFill": "漸層填充", "SSE.Views.CellSettings.textIndent": "縮排", "SSE.Views.CellSettings.textItems": "項目", "SSE.Views.CellSettings.textLinear": "線性的", "SSE.Views.CellSettings.textManageRule": "管理規則", "SSE.Views.CellSettings.textNewRule": "新槼則", "SSE.Views.CellSettings.textNoFill": "沒有填充", "SSE.Views.CellSettings.textOrientation": "文字方向", "SSE.Views.CellSettings.textPattern": "模式", "SSE.Views.CellSettings.textPatternFill": "模式", "SSE.Views.CellSettings.textPosition": "位置", "SSE.Views.CellSettings.textRadial": "徑向的", "SSE.Views.CellSettings.textSelectBorders": "選擇您要更改上面選擇的應用樣式的邊框", "SSE.Views.CellSettings.textSelection": "凍結當前選擇", "SSE.Views.CellSettings.textThisPivot": "由此數據透視表", "SSE.Views.CellSettings.textThisSheet": "由此工作表", "SSE.Views.CellSettings.textThisTable": "由此表", "SSE.Views.CellSettings.tipAddGradientPoint": "添加漸變點", "SSE.Views.CellSettings.tipAll": "設置外邊界和所有內線", "SSE.Views.CellSettings.tipBottom": "僅設置外底邊框", "SSE.Views.CellSettings.tipDiagD": "設置對角向下邊框", "SSE.Views.CellSettings.tipDiagU": "設置對角上邊界", "SSE.Views.CellSettings.tipInner": "僅設置內線", "SSE.Views.CellSettings.tipInnerHor": "僅設置水平內線", "SSE.Views.CellSettings.tipInnerVert": "僅設置垂直內線", "SSE.Views.CellSettings.tipLeft": "僅設置左外邊框", "SSE.Views.CellSettings.tipNone": "設置無邊界", "SSE.Views.CellSettings.tipOuter": "僅設置外部框線", "SSE.Views.CellSettings.tipRemoveGradientPoint": "刪除漸變點", "SSE.Views.CellSettings.tipRight": "僅設置右外框", "SSE.Views.CellSettings.tipTop": "僅設置外部頂部邊框", "SSE.Views.ChartDataDialog.errorInFormula": "您輸入的公式有誤。", "SSE.Views.ChartDataDialog.errorInvalidReference": "該引用無效。引用必須是一個打開的工作表。", "SSE.Views.ChartDataDialog.errorMaxPoints": "每個圖表的最大串聯點數為4096。", "SSE.Views.ChartDataDialog.errorMaxRows": "每個圖表的最大數據系列數為255。", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "該引用無效。標題，值，大小或數據標籤的引用必須是單個單元格，行或列。", "SSE.Views.ChartDataDialog.errorNoValues": "要創建圖表，該系列必須至少包含一個值。", "SSE.Views.ChartDataDialog.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "SSE.Views.ChartDataDialog.textAdd": "新增", "SSE.Views.ChartDataDialog.textCategory": "水平（類別）軸標籤", "SSE.Views.ChartDataDialog.textData": "圖表數據範圍", "SSE.Views.ChartDataDialog.textDelete": "移除", "SSE.Views.ChartDataDialog.textDown": "下", "SSE.Views.ChartDataDialog.textEdit": "編輯", "SSE.Views.ChartDataDialog.textInvalidRange": "無效的單元格範圍", "SSE.Views.ChartDataDialog.textSelectData": "選擇數據", "SSE.Views.ChartDataDialog.textSeries": "圖例條目（系列）", "SSE.Views.ChartDataDialog.textSwitch": "切換行/列", "SSE.Views.ChartDataDialog.textTitle": "圖表數據", "SSE.Views.ChartDataDialog.textUp": "上", "SSE.Views.ChartDataRangeDialog.errorInFormula": "您輸入的公式有誤。", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "該引用無效。引用必須是一個打開的工作表。", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "每個圖表的最大串聯點數為4096。", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "每個圖表的最大數據系列數為255。", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "該引用無效。標題，值，大小或數據標籤的引用必須是單個單元格，行或列。", "SSE.Views.ChartDataRangeDialog.errorNoValues": "要創建圖表，該系列必須至少包含一個值。", "SSE.Views.ChartDataRangeDialog.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "無效的單元格範圍", "SSE.Views.ChartDataRangeDialog.textSelectData": "選擇數據", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "軸標範圍", "SSE.Views.ChartDataRangeDialog.txtChoose": "選擇範圍", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "系列名稱", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "軸標籤", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "編輯系列", "SSE.Views.ChartDataRangeDialog.txtValues": "值", "SSE.Views.ChartDataRangeDialog.txtXValues": "X值", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y值", "SSE.Views.ChartSettings.errorMaxRows": "每個圖表的最大數據系列數為255。", "SSE.Views.ChartSettings.strLineWeight": "線寬", "SSE.Views.ChartSettings.strSparkColor": "顏色", "SSE.Views.ChartSettings.strTemplate": "樣板", "SSE.Views.ChartSettings.textAdvanced": "顯示進階設定", "SSE.Views.ChartSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入0 pt至1584 pt之間的值。", "SSE.Views.ChartSettings.textChangeType": "變更類型", "SSE.Views.ChartSettings.textChartType": "更改圖表類型", "SSE.Views.ChartSettings.textEditData": "編輯數據和位置", "SSE.Views.ChartSettings.textFirstPoint": "第一點", "SSE.Views.ChartSettings.textHeight": "高度", "SSE.Views.ChartSettings.textHighPoint": "高點", "SSE.Views.ChartSettings.textKeepRatio": "比例不變", "SSE.Views.ChartSettings.textLastPoint": "最後一點", "SSE.Views.ChartSettings.textLowPoint": "低點", "SSE.Views.ChartSettings.textMarkers": "標記物", "SSE.Views.ChartSettings.textNegativePoint": "負點", "SSE.Views.ChartSettings.textRanges": "數據範圍", "SSE.Views.ChartSettings.textSelectData": "選擇數據", "SSE.Views.ChartSettings.textShow": "顯示", "SSE.Views.ChartSettings.textSize": "大小", "SSE.Views.ChartSettings.textStyle": "樣式", "SSE.Views.ChartSettings.textSwitch": "切換行/列", "SSE.Views.ChartSettings.textType": "類型", "SSE.Views.ChartSettings.textWidth": "寬度", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "錯誤！每個圖表的最大串聯點數為4096。", "SSE.Views.ChartSettingsDlg.errorMaxRows": "錯誤！每個圖表的最大數據序列數為255", "SSE.Views.ChartSettingsDlg.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "SSE.Views.ChartSettingsDlg.textAbsolute": "不要移動或調整單元格的大小", "SSE.Views.ChartSettingsDlg.textAlt": "替代文字", "SSE.Views.ChartSettingsDlg.textAltDescription": "描述", "SSE.Views.ChartSettingsDlg.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "SSE.Views.ChartSettingsDlg.textAltTitle": "標題", "SSE.Views.ChartSettingsDlg.textAuto": "自動", "SSE.Views.ChartSettingsDlg.textAutoEach": "每個自動", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "軸十字", "SSE.Views.ChartSettingsDlg.textAxisOptions": "軸選項", "SSE.Views.ChartSettingsDlg.textAxisPos": "軸位置", "SSE.Views.ChartSettingsDlg.textAxisSettings": "軸設定", "SSE.Views.ChartSettingsDlg.textAxisTitle": "標題", "SSE.Views.ChartSettingsDlg.textBase": "基礎", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "勾線之間", "SSE.Views.ChartSettingsDlg.textBillions": "億", "SSE.Views.ChartSettingsDlg.textBottom": "底部", "SSE.Views.ChartSettingsDlg.textCategoryName": "分類名稱", "SSE.Views.ChartSettingsDlg.textCenter": "中心", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "圖表元素和<br>圖例", "SSE.Views.ChartSettingsDlg.textChartTitle": "圖表標題", "SSE.Views.ChartSettingsDlg.textCross": "交叉", "SSE.Views.ChartSettingsDlg.textCustom": "自訂", "SSE.Views.ChartSettingsDlg.textDataColumns": "在列中", "SSE.Views.ChartSettingsDlg.textDataLabels": "數據標籤", "SSE.Views.ChartSettingsDlg.textDataRows": "成排", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "顯示圖例", "SSE.Views.ChartSettingsDlg.textEmptyCells": "隱藏和清空單元格", "SSE.Views.ChartSettingsDlg.textEmptyLine": "用線連接數據點", "SSE.Views.ChartSettingsDlg.textFit": "切合至寬度", "SSE.Views.ChartSettingsDlg.textFixed": "固定", "SSE.Views.ChartSettingsDlg.textFormat": "標記格式", "SSE.Views.ChartSettingsDlg.textGaps": "縫隙", "SSE.Views.ChartSettingsDlg.textGridLines": "網格線", "SSE.Views.ChartSettingsDlg.textGroup": "組走勢圖", "SSE.Views.ChartSettingsDlg.textHide": "隱藏", "SSE.Views.ChartSettingsDlg.textHideAxis": "隱藏軸", "SSE.Views.ChartSettingsDlg.textHigh": "高", "SSE.Views.ChartSettingsDlg.textHorAxis": "橫軸", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "副横轴", "SSE.Views.ChartSettingsDlg.textHorizontal": "水平", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "幾百個", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "在", "SSE.Views.ChartSettingsDlg.textInnerBottom": "內底", "SSE.Views.ChartSettingsDlg.textInnerTop": "內頂", "SSE.Views.ChartSettingsDlg.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.ChartSettingsDlg.textLabelDist": "軸標距離", "SSE.Views.ChartSettingsDlg.textLabelInterval": "標籤之間的間隔", "SSE.Views.ChartSettingsDlg.textLabelOptions": "標籤選項", "SSE.Views.ChartSettingsDlg.textLabelPos": "標籤位置", "SSE.Views.ChartSettingsDlg.textLayout": "佈局", "SSE.Views.ChartSettingsDlg.textLeft": "左", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "左側覆蓋", "SSE.Views.ChartSettingsDlg.textLegendBottom": "底部", "SSE.Views.ChartSettingsDlg.textLegendLeft": "左", "SSE.Views.ChartSettingsDlg.textLegendPos": "說明", "SSE.Views.ChartSettingsDlg.textLegendRight": "右", "SSE.Views.ChartSettingsDlg.textLegendTop": "上方", "SSE.Views.ChartSettingsDlg.textLines": "線", "SSE.Views.ChartSettingsDlg.textLocationRange": "位置範圍", "SSE.Views.ChartSettingsDlg.textLogScale": "對數尺度", "SSE.Views.ChartSettingsDlg.textLow": "低", "SSE.Views.ChartSettingsDlg.textMajor": "重大", "SSE.Views.ChartSettingsDlg.textMajorMinor": "主要和次要", "SSE.Views.ChartSettingsDlg.textMajorType": "主要類型", "SSE.Views.ChartSettingsDlg.textManual": "手動", "SSE.Views.ChartSettingsDlg.textMarkers": "標記物", "SSE.Views.ChartSettingsDlg.textMarksInterval": "標記之間的間隔", "SSE.Views.ChartSettingsDlg.textMaxValue": "最大值", "SSE.Views.ChartSettingsDlg.textMillions": "百萬", "SSE.Views.ChartSettingsDlg.textMinor": "次要", "SSE.Views.ChartSettingsDlg.textMinorType": "次要類", "SSE.Views.ChartSettingsDlg.textMinValue": "最低值", "SSE.Views.ChartSettingsDlg.textNextToAxis": "軸旁", "SSE.Views.ChartSettingsDlg.textNone": "無", "SSE.Views.ChartSettingsDlg.textNoOverlay": "無覆蓋", "SSE.Views.ChartSettingsDlg.textOneCell": "移動但不調整單元格的大小", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "在勾標上", "SSE.Views.ChartSettingsDlg.textOut": "外", "SSE.Views.ChartSettingsDlg.textOuterTop": "外上", "SSE.Views.ChartSettingsDlg.textOverlay": "覆蓋", "SSE.Views.ChartSettingsDlg.textReverse": "值倒序", "SSE.Views.ChartSettingsDlg.textReverseOrder": "相反的順序", "SSE.Views.ChartSettingsDlg.textRight": "右", "SSE.Views.ChartSettingsDlg.textRightOverlay": "右側覆蓋", "SSE.Views.ChartSettingsDlg.textRotated": "已旋轉", "SSE.Views.ChartSettingsDlg.textSameAll": "所有都一樣", "SSE.Views.ChartSettingsDlg.textSelectData": "選擇數據", "SSE.Views.ChartSettingsDlg.textSeparator": "數據標籤分隔符", "SSE.Views.ChartSettingsDlg.textSeriesName": "系列名稱", "SSE.Views.ChartSettingsDlg.textShow": "顯示", "SSE.Views.ChartSettingsDlg.textShowBorders": "顯示圖表邊框", "SSE.Views.ChartSettingsDlg.textShowData": "在隱藏的行和列中顯示數據", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "將空單元格顯示為", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "顯示軸", "SSE.Views.ChartSettingsDlg.textShowValues": "顯示圖表值", "SSE.Views.ChartSettingsDlg.textSingle": "單一走勢圖", "SSE.Views.ChartSettingsDlg.textSmooth": "光滑", "SSE.Views.ChartSettingsDlg.textSnap": "單元捕捉", "SSE.Views.ChartSettingsDlg.textSparkRanges": "走勢圖範圍", "SSE.Views.ChartSettingsDlg.textStraight": "直行", "SSE.Views.ChartSettingsDlg.textStyle": "樣式", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "千", "SSE.Views.ChartSettingsDlg.textTickOptions": "勾號選項", "SSE.Views.ChartSettingsDlg.textTitle": "圖表-進階設置", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "走勢圖-進階設置", "SSE.Views.ChartSettingsDlg.textTop": "上方", "SSE.Views.ChartSettingsDlg.textTrillions": "兆", "SSE.Views.ChartSettingsDlg.textTwoCell": "移動並調整單元格大小", "SSE.Views.ChartSettingsDlg.textType": "類型", "SSE.Views.ChartSettingsDlg.textTypeData": "類型和數據", "SSE.Views.ChartSettingsDlg.textUnits": "顯示單位", "SSE.Views.ChartSettingsDlg.textValue": "值", "SSE.Views.ChartSettingsDlg.textVertAxis": "垂直軸", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "副纵轴", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X軸標題", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y軸標題", "SSE.Views.ChartSettingsDlg.textZero": "零", "SSE.Views.ChartSettingsDlg.txtEmpty": "這是必填欄", "SSE.Views.ChartTypeDialog.errorComboSeries": "新增組合圖表需選兩個以上的數據系列。", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "所選圖表類型需要現有圖表使用的輔助軸。 請選擇其他圖表類型。", "SSE.Views.ChartTypeDialog.textSecondary": "副轴", "SSE.Views.ChartTypeDialog.textSeries": "系列", "SSE.Views.ChartTypeDialog.textStyle": "樣式", "SSE.Views.ChartTypeDialog.textTitle": "圖表類型", "SSE.Views.ChartTypeDialog.textType": "類型", "SSE.Views.CreatePivotDialog.textDataRange": "源數據范圍", "SSE.Views.CreatePivotDialog.textDestination": "選擇放置桌子的位置", "SSE.Views.CreatePivotDialog.textExist": "現有工作表", "SSE.Views.CreatePivotDialog.textInvalidRange": "無效的單元格範圍", "SSE.Views.CreatePivotDialog.textNew": "新工作表", "SSE.Views.CreatePivotDialog.textSelectData": "選擇數據", "SSE.Views.CreatePivotDialog.textTitle": "創建數據透視表", "SSE.Views.CreatePivotDialog.txtEmpty": "這是必填欄", "SSE.Views.CreateSparklineDialog.textDataRange": "源數據范圍", "SSE.Views.CreateSparklineDialog.textDestination": "選擇走勢圖掰位", "SSE.Views.CreateSparklineDialog.textInvalidRange": "無效的儲存格範圍", "SSE.Views.CreateSparklineDialog.textSelectData": "選擇數據", "SSE.Views.CreateSparklineDialog.textTitle": "創建走勢圖", "SSE.Views.CreateSparklineDialog.txtEmpty": "這是必填欄", "SSE.Views.DataTab.capBtnGroup": "群組", "SSE.Views.DataTab.capBtnTextCustomSort": "自定義排序", "SSE.Views.DataTab.capBtnTextDataValidation": "資料驗證", "SSE.Views.DataTab.capBtnTextRemDuplicates": "刪除重複項", "SSE.Views.DataTab.capBtnTextToCol": "文字轉欄", "SSE.Views.DataTab.capBtnUngroup": "解開組合", "SSE.Views.DataTab.capDataFromText": "獲取數據", "SSE.Views.DataTab.mniFromFile": "從本機TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "從TXT/CSV網址", "SSE.Views.DataTab.textBelow": "詳細信息下方的摘要行", "SSE.Views.DataTab.textClear": "輪廓清晰", "SSE.Views.DataTab.textColumns": "取消分組列", "SSE.Views.DataTab.textGroupColumns": "組列", "SSE.Views.DataTab.textGroupRows": "組行", "SSE.Views.DataTab.textRightOf": "詳細信息右側的摘要列", "SSE.Views.DataTab.textRows": "取消分組行", "SSE.Views.DataTab.tipCustomSort": "自定義排序", "SSE.Views.DataTab.tipDataFromText": "從TXT/CSV檔獲取數據", "SSE.Views.DataTab.tipDataValidation": "資料驗證", "SSE.Views.DataTab.tipGroup": "單元格範圍", "SSE.Views.DataTab.tipRemDuplicates": "從工作表中刪除重複的行", "SSE.Views.DataTab.tipToColumns": "將單元格文本分成列", "SSE.Views.DataTab.tipUngroup": "取消單元格範圍", "SSE.Views.DataValidationDialog.errorFormula": "該值當前評估為錯誤。你要繼續嗎？", "SSE.Views.DataValidationDialog.errorInvalid": "您為字段“ {0}”輸入的值無效。", "SSE.Views.DataValidationDialog.errorInvalidDate": "您為字段“ {0}”輸入的日期無效。", "SSE.Views.DataValidationDialog.errorInvalidList": "列表源必須是定界列表，或者是對單行或單列的引用。", "SSE.Views.DataValidationDialog.errorInvalidTime": "您為字段“ {0}”輸入的時間無效。", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "“ {1}”字段必須大於或等於“ {0}”字段。", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "您必須在字段“ {0}”和字段“ {1}”中都輸入一個值。", "SSE.Views.DataValidationDialog.errorMustEnterValue": "您必須在字段“ {0}”中輸入一個值。", "SSE.Views.DataValidationDialog.errorNamedRange": "找不到您指定的命名範圍。", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "在條件“ {0}”中不能使用負值。", "SSE.Views.DataValidationDialog.errorNotNumeric": "字段“ {0}”必須是數字值，數字表達式或引用包含數字值的單元格。", "SSE.Views.DataValidationDialog.strError": "錯誤提示", "SSE.Views.DataValidationDialog.strInput": "輸入信息", "SSE.Views.DataValidationDialog.strSettings": "設定", "SSE.Views.DataValidationDialog.textAlert": "警示", "SSE.Views.DataValidationDialog.textAllow": "允許", "SSE.Views.DataValidationDialog.textApply": "將這些更改應用於具有相同設置的所有其他單元格", "SSE.Views.DataValidationDialog.textCellSelected": "選擇單元格後，顯示此輸入消息", "SSE.Views.DataValidationDialog.textCompare": "相比於", "SSE.Views.DataValidationDialog.textData": "數據", "SSE.Views.DataValidationDialog.textEndDate": "結束日期", "SSE.Views.DataValidationDialog.textEndTime": "時間結束", "SSE.Views.DataValidationDialog.textError": "錯誤信息", "SSE.Views.DataValidationDialog.textFormula": "配方", "SSE.Views.DataValidationDialog.textIgnore": "忽略空白", "SSE.Views.DataValidationDialog.textInput": "輸入信息", "SSE.Views.DataValidationDialog.textMax": "最大", "SSE.Views.DataValidationDialog.textMessage": "信息", "SSE.Views.DataValidationDialog.textMin": "最低", "SSE.Views.DataValidationDialog.textSelectData": "選擇數據", "SSE.Views.DataValidationDialog.textShowDropDown": "在單元格中顯示下拉列表", "SSE.Views.DataValidationDialog.textShowError": "輸入無效數據後顯示錯誤警報", "SSE.Views.DataValidationDialog.textShowInput": "選擇單元格時顯示輸入消息", "SSE.Views.DataValidationDialog.textSource": "來源", "SSE.Views.DataValidationDialog.textStartDate": "開始日期", "SSE.Views.DataValidationDialog.textStartTime": "開始時間", "SSE.Views.DataValidationDialog.textStop": "停止", "SSE.Views.DataValidationDialog.textStyle": "樣式", "SSE.Views.DataValidationDialog.textTitle": "標題", "SSE.Views.DataValidationDialog.textUserEnters": "當用戶輸入無效數據時，顯示此錯誤警報", "SSE.Views.DataValidationDialog.txtAny": "任何值", "SSE.Views.DataValidationDialog.txtBetween": "之間", "SSE.Views.DataValidationDialog.txtDate": "日期", "SSE.Views.DataValidationDialog.txtDecimal": "小數", "SSE.Views.DataValidationDialog.txtElTime": "以經過時間", "SSE.Views.DataValidationDialog.txtEndDate": "結束日期", "SSE.Views.DataValidationDialog.txtEndTime": "時間結束", "SSE.Views.DataValidationDialog.txtEqual": "等於", "SSE.Views.DataValidationDialog.txtGreaterThan": "更佳", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "大於或等於", "SSE.Views.DataValidationDialog.txtLength": "長度", "SSE.Views.DataValidationDialog.txtLessThan": "少於", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "小於或等於", "SSE.Views.DataValidationDialog.txtList": "清單", "SSE.Views.DataValidationDialog.txtNotBetween": "不介於", "SSE.Views.DataValidationDialog.txtNotEqual": "不等於", "SSE.Views.DataValidationDialog.txtOther": "其它", "SSE.Views.DataValidationDialog.txtStartDate": "開始日期", "SSE.Views.DataValidationDialog.txtStartTime": "開始時間", "SSE.Views.DataValidationDialog.txtTextLength": "文字長度", "SSE.Views.DataValidationDialog.txtTime": "時間", "SSE.Views.DataValidationDialog.txtWhole": "完整的號碼", "SSE.Views.DigitalFilterDialog.capAnd": "和", "SSE.Views.DigitalFilterDialog.capCondition1": "等於", "SSE.Views.DigitalFilterDialog.capCondition10": "不以結束", "SSE.Views.DigitalFilterDialog.capCondition11": "包含", "SSE.Views.DigitalFilterDialog.capCondition12": "不含", "SSE.Views.DigitalFilterDialog.capCondition2": "不等於", "SSE.Views.DigitalFilterDialog.capCondition3": "大於", "SSE.Views.DigitalFilterDialog.capCondition4": "大於或等於", "SSE.Views.DigitalFilterDialog.capCondition5": "小於", "SSE.Views.DigitalFilterDialog.capCondition6": "小於或等於", "SSE.Views.DigitalFilterDialog.capCondition7": "開始於", "SSE.Views.DigitalFilterDialog.capCondition8": "不以", "SSE.Views.DigitalFilterDialog.capCondition9": "以。。結束", "SSE.Views.DigitalFilterDialog.capOr": "或", "SSE.Views.DigitalFilterDialog.textNoFilter": "沒有過濾器", "SSE.Views.DigitalFilterDialog.textShowRows": "顯示行", "SSE.Views.DigitalFilterDialog.textUse1": "採用 ？呈現任何單個字符", "SSE.Views.DigitalFilterDialog.textUse2": "使用*表示任何系列字符", "SSE.Views.DigitalFilterDialog.txtTitle": "自訂過濾器", "SSE.Views.DocumentHolder.advancedImgText": "圖像進階設置", "SSE.Views.DocumentHolder.advancedShapeText": "形狀進階設定", "SSE.Views.DocumentHolder.advancedSlicerText": "切片器進階設置", "SSE.Views.DocumentHolder.bottomCellText": "底部對齊", "SSE.Views.DocumentHolder.bulletsText": "項目符和編號", "SSE.Views.DocumentHolder.centerCellText": "中央對齊", "SSE.Views.DocumentHolder.chartDataText": "選擇圖表資料", "SSE.Views.DocumentHolder.chartText": "圖表進階設置", "SSE.Views.DocumentHolder.chartTypeText": "變更圖表類型", "SSE.Views.DocumentHolder.deleteColumnText": "欄", "SSE.Views.DocumentHolder.deleteRowText": "行", "SSE.Views.DocumentHolder.deleteTableText": "表格", "SSE.Views.DocumentHolder.direct270Text": "向上旋轉文字", "SSE.Views.DocumentHolder.direct90Text": "向下旋轉文字", "SSE.Views.DocumentHolder.directHText": "水平", "SSE.Views.DocumentHolder.directionText": "文字方向", "SSE.Views.DocumentHolder.editChartText": "編輯資料", "SSE.Views.DocumentHolder.editHyperlinkText": "編輯超連結", "SSE.Views.DocumentHolder.insertColumnLeftText": "欄位以左", "SSE.Views.DocumentHolder.insertColumnRightText": "欄位以右", "SSE.Views.DocumentHolder.insertRowAboveText": "上行", "SSE.Views.DocumentHolder.insertRowBelowText": "下行", "SSE.Views.DocumentHolder.originalSizeText": "實際大小", "SSE.Views.DocumentHolder.removeHyperlinkText": "刪除超連結", "SSE.Views.DocumentHolder.selectColumnText": "整列", "SSE.Views.DocumentHolder.selectDataText": "列數據", "SSE.Views.DocumentHolder.selectRowText": "行", "SSE.Views.DocumentHolder.selectTableText": "表格", "SSE.Views.DocumentHolder.strDelete": "刪除簽名", "SSE.Views.DocumentHolder.strDetails": "簽名細節", "SSE.Views.DocumentHolder.strSetup": "簽名設置", "SSE.Views.DocumentHolder.strSign": "簽名", "SSE.Views.DocumentHolder.textAlign": "對齊", "SSE.Views.DocumentHolder.textArrange": "安排", "SSE.Views.DocumentHolder.textArrangeBack": "傳送到背景", "SSE.Views.DocumentHolder.textArrangeBackward": "向後發送", "SSE.Views.DocumentHolder.textArrangeForward": "向前帶進", "SSE.Views.DocumentHolder.textArrangeFront": "移到前景", "SSE.Views.DocumentHolder.textAverage": "平均", "SSE.Views.DocumentHolder.textBullets": "項目符號", "SSE.Views.DocumentHolder.textCount": "計數", "SSE.Views.DocumentHolder.textCrop": "修剪", "SSE.Views.DocumentHolder.textCropFill": "填入", "SSE.Views.DocumentHolder.textCropFit": "切合", "SSE.Views.DocumentHolder.textEditPoints": "編輯點", "SSE.Views.DocumentHolder.textEntriesList": "從下拉列表中選擇", "SSE.Views.DocumentHolder.textFlipH": "水平翻轉", "SSE.Views.DocumentHolder.textFlipV": "垂直翻轉", "SSE.Views.DocumentHolder.textFreezePanes": "凍結窗格", "SSE.Views.DocumentHolder.textFromFile": "從檔案", "SSE.Views.DocumentHolder.textFromStorage": "從存儲", "SSE.Views.DocumentHolder.textFromUrl": "從 URL", "SSE.Views.DocumentHolder.textListSettings": "清單設定", "SSE.Views.DocumentHolder.textMacro": "指定巨集", "SSE.Views.DocumentHolder.textMax": "最高", "SSE.Views.DocumentHolder.textMin": "最低", "SSE.Views.DocumentHolder.textMore": "更多功能", "SSE.Views.DocumentHolder.textMoreFormats": "更多格式", "SSE.Views.DocumentHolder.textNone": "無", "SSE.Views.DocumentHolder.textNumbering": "編號", "SSE.Views.DocumentHolder.textReplace": "取代圖片", "SSE.Views.DocumentHolder.textRotate": "旋轉", "SSE.Views.DocumentHolder.textRotate270": "逆時針旋轉90°", "SSE.Views.DocumentHolder.textRotate90": "順時針旋轉90°", "SSE.Views.DocumentHolder.textShapeAlignBottom": "底部對齊", "SSE.Views.DocumentHolder.textShapeAlignCenter": "居中對齊", "SSE.Views.DocumentHolder.textShapeAlignLeft": "對齊左側", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "中央對齊", "SSE.Views.DocumentHolder.textShapeAlignRight": "對齊右側", "SSE.Views.DocumentHolder.textShapeAlignTop": "上方對齊", "SSE.Views.DocumentHolder.textStdDev": "標準差", "SSE.Views.DocumentHolder.textSum": "總和", "SSE.Views.DocumentHolder.textUndo": "復原", "SSE.Views.DocumentHolder.textUnFreezePanes": "取消凍結窗格", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "箭頭項目符號", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "核取記號項目符號", "SSE.Views.DocumentHolder.tipMarkersDash": "連字號項目符號", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "實心菱形項目符號", "SSE.Views.DocumentHolder.tipMarkersFRound": "實心圓項目符號", "SSE.Views.DocumentHolder.tipMarkersFSquare": "實心方形項目符號", "SSE.Views.DocumentHolder.tipMarkersHRound": "空心圓項目符號", "SSE.Views.DocumentHolder.tipMarkersStar": "星星項目符號", "SSE.Views.DocumentHolder.topCellText": "上方對齊", "SSE.Views.DocumentHolder.txtAccounting": "會計", "SSE.Views.DocumentHolder.txtAddComment": "增加留言", "SSE.Views.DocumentHolder.txtAddNamedRange": "定義名稱", "SSE.Views.DocumentHolder.txtArrange": "安排", "SSE.Views.DocumentHolder.txtAscending": "上升", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "自動調整列寬", "SSE.Views.DocumentHolder.txtAutoRowHeight": "自動調整行高", "SSE.Views.DocumentHolder.txtClear": "清除", "SSE.Views.DocumentHolder.txtClearAll": "全部", "SSE.Views.DocumentHolder.txtClearComments": "評論", "SSE.Views.DocumentHolder.txtClearFormat": "格式", "SSE.Views.DocumentHolder.txtClearHyper": "超連結", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "清除選定的迷你圖組", "SSE.Views.DocumentHolder.txtClearSparklines": "清除選定的迷你圖", "SSE.Views.DocumentHolder.txtClearText": "文字", "SSE.Views.DocumentHolder.txtColumn": "整列", "SSE.Views.DocumentHolder.txtColumnWidth": "設置列寬", "SSE.Views.DocumentHolder.txtCondFormat": "條件格式", "SSE.Views.DocumentHolder.txtCopy": "複製", "SSE.Views.DocumentHolder.txtCurrency": "幣別", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "自定列寬", "SSE.Views.DocumentHolder.txtCustomRowHeight": "自定義行高", "SSE.Views.DocumentHolder.txtCustomSort": "自定義排序", "SSE.Views.DocumentHolder.txtCut": "剪下", "SSE.Views.DocumentHolder.txtDate": "日期", "SSE.Views.DocumentHolder.txtDelete": "刪除", "SSE.Views.DocumentHolder.txtDescending": "降序", "SSE.Views.DocumentHolder.txtDistribHor": "水平分佈", "SSE.Views.DocumentHolder.txtDistribVert": "垂直分佈", "SSE.Views.DocumentHolder.txtEditComment": "編輯評論", "SSE.Views.DocumentHolder.txtFilter": "過濾器", "SSE.Views.DocumentHolder.txtFilterCellColor": "按單元格顏色過濾", "SSE.Views.DocumentHolder.txtFilterFontColor": "按字體顏色過濾", "SSE.Views.DocumentHolder.txtFilterValue": "按選定單元格的值過濾", "SSE.Views.DocumentHolder.txtFormula": "插入功能", "SSE.Views.DocumentHolder.txtFraction": "分數", "SSE.Views.DocumentHolder.txtGeneral": "一般", "SSE.Views.DocumentHolder.txtGetLink": "獲取此範圍的連結", "SSE.Views.DocumentHolder.txtGroup": "群組", "SSE.Views.DocumentHolder.txtHide": "隱藏", "SSE.Views.DocumentHolder.txtInsert": "插入", "SSE.Views.DocumentHolder.txtInsHyperlink": "超連結", "SSE.Views.DocumentHolder.txtNumber": "數字", "SSE.Views.DocumentHolder.txtNumFormat": "數字格式", "SSE.Views.DocumentHolder.txtPaste": "貼上", "SSE.Views.DocumentHolder.txtPercentage": "百分比", "SSE.Views.DocumentHolder.txtReapply": "重新申請", "SSE.Views.DocumentHolder.txtRow": "整行", "SSE.Views.DocumentHolder.txtRowHeight": "設置行高", "SSE.Views.DocumentHolder.txtScientific": "科學的", "SSE.Views.DocumentHolder.txtSelect": "選擇", "SSE.Views.DocumentHolder.txtShiftDown": "下移單元格", "SSE.Views.DocumentHolder.txtShiftLeft": "左移單元格", "SSE.Views.DocumentHolder.txtShiftRight": "右移單元格", "SSE.Views.DocumentHolder.txtShiftUp": "上移單元格", "SSE.Views.DocumentHolder.txtShow": "顯示", "SSE.Views.DocumentHolder.txtShowComment": "顯示評論", "SSE.Views.DocumentHolder.txtSort": "分類", "SSE.Views.DocumentHolder.txtSortCellColor": "所選單元格顏色在頂部", "SSE.Views.DocumentHolder.txtSortFontColor": "所選字體顏色在頂部", "SSE.Views.DocumentHolder.txtSparklines": "走勢圖", "SSE.Views.DocumentHolder.txtText": "文字", "SSE.Views.DocumentHolder.txtTextAdvanced": "段落進階設置", "SSE.Views.DocumentHolder.txtTime": "時間", "SSE.Views.DocumentHolder.txtUngroup": "解開組合", "SSE.Views.DocumentHolder.txtWidth": "寬度", "SSE.Views.DocumentHolder.vertAlignText": "垂直對齊", "SSE.Views.FieldSettingsDialog.strLayout": "佈局", "SSE.Views.FieldSettingsDialog.strSubtotals": "小計", "SSE.Views.FieldSettingsDialog.textReport": "報表", "SSE.Views.FieldSettingsDialog.textTitle": "欄位設定", "SSE.Views.FieldSettingsDialog.txtAverage": "平均", "SSE.Views.FieldSettingsDialog.txtBlank": "在每個項目之後插入空白行", "SSE.Views.FieldSettingsDialog.txtBottom": "顯示在組的底部", "SSE.Views.FieldSettingsDialog.txtCompact": "緊湊", "SSE.Views.FieldSettingsDialog.txtCount": "計數", "SSE.Views.FieldSettingsDialog.txtCountNums": "數數", "SSE.Views.FieldSettingsDialog.txtCustomName": "自定義名稱", "SSE.Views.FieldSettingsDialog.txtEmpty": "顯示沒有數據的項目", "SSE.Views.FieldSettingsDialog.txtMax": "最高", "SSE.Views.FieldSettingsDialog.txtMin": "最低", "SSE.Views.FieldSettingsDialog.txtOutline": "大綱", "SSE.Views.FieldSettingsDialog.txtProduct": "產品", "SSE.Views.FieldSettingsDialog.txtRepeat": "在每一行重複項目標籤", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "顯示小計", "SSE.Views.FieldSettingsDialog.txtSourceName": "來源名稱:", "SSE.Views.FieldSettingsDialog.txtStdDev": "標準差", "SSE.Views.FieldSettingsDialog.txtStdDevp": "標準差p", "SSE.Views.FieldSettingsDialog.txtSum": "總和", "SSE.Views.FieldSettingsDialog.txtSummarize": "小計的功能", "SSE.Views.FieldSettingsDialog.txtTabular": "表格式的", "SSE.Views.FieldSettingsDialog.txtTop": "顯示在群組頂部", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "打開文件所在位置", "SSE.Views.FileMenu.btnCloseMenuCaption": "關閉選單", "SSE.Views.FileMenu.btnCreateNewCaption": "創建新的", "SSE.Views.FileMenu.btnDownloadCaption": "下載為", "SSE.Views.FileMenu.btnExitCaption": "關閉", "SSE.Views.FileMenu.btnFileOpenCaption": "開啟", "SSE.Views.FileMenu.btnHelpCaption": "幫助", "SSE.Views.FileMenu.btnHistoryCaption": "版本歷史", "SSE.Views.FileMenu.btnInfoCaption": "電子表格信息", "SSE.Views.FileMenu.btnPrintCaption": "列印", "SSE.Views.FileMenu.btnProtectCaption": "保護", "SSE.Views.FileMenu.btnRecentFilesCaption": "打開最近", "SSE.Views.FileMenu.btnRenameCaption": "改名", "SSE.Views.FileMenu.btnReturnCaption": "返回至試算表", "SSE.Views.FileMenu.btnRightsCaption": "存取權限", "SSE.Views.FileMenu.btnSaveAsCaption": "另存為", "SSE.Views.FileMenu.btnSaveCaption": "儲存", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "另存為", "SSE.Views.FileMenu.btnSettingsCaption": "進階設定", "SSE.Views.FileMenu.btnToEditCaption": "編輯電子表格", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "空白表格", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "創建新的", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "套用", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "添加作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "添加文字", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "應用程式", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "更改存取權限", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "評論", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已建立", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最後修改者", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上一次更改", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "擁有者", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "有權利的人", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主旨", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "標題", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "\n已上傳", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "更改存取權限", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "有權利的人", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "套用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "共同編輯模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "使用1904日期系統", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "小數點分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "字典語言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "快", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "字體提示", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "公式語言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "示例：SUM； MIN; MAX;COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "忽略大寫單詞", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "忽略帶數字的單詞", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "巨集設定", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "粘貼內容時顯示“粘貼選項”按鈕", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1參考樣式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "區域設置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "例：", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "在工作表中顯示注釋", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "顯示其他用戶的更改", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "顯示已解決的註釋", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "嚴格", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "介面主題", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "千位分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "測量單位", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "根據區域設置使用分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "預設縮放值", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "每10分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "每30分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "每5分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "每一小時", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "自動恢復", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "自動保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "已停用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "儲存所有歷史版本到伺服器", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "每一分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "參考風格", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "自動更正選項...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "白俄羅斯語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "保加利亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "加泰羅尼亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "預設緩存模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "計算", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "公分", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "協作", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "捷克語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "丹麥語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "德語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "編輯並儲存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "希腊语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "英語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "西班牙文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "實時共同編輯，所有變更將自動儲存。", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "芬蘭語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "法文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "匈牙利語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "印度尼西亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "吋", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "義大利文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "日文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "韓文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "老撾語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "拉脫維亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "作為OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "本機", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "挪威語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "荷蘭語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "拋光", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "打樣", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "點", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "葡萄牙語（巴西）", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "葡萄牙語（葡萄牙）", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "區域", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "羅馬尼亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "俄語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "全部啟用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "不用提示啟用全部巨集", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "斯洛伐克語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "斯洛文尼亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "全部停用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "不用提示停用全部巨集", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "使用儲存鍵來同步你和其他用戶的變更", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "瑞典語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "土耳其文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "烏克蘭文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "使用Alt鍵來操控用戶介面", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "使用Option鍵來操控用戶介面", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "越南語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "顯示通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "以提示停用全部巨集", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "作為Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "工作空間", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "中文", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "帶密碼", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "保護電子表格", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "帶簽名", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "編輯電子表格", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編輯將刪除電子表格中的簽名。<br>確定要繼續嗎？", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此電子表格已受密碼保護", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "該電子表格需要簽名。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效簽名已添加到電子表格中。電子表格受到保護，無法編輯。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "電子表格中的某些數字簽名無效或無法驗證。電子表格受到保護，無法編輯。", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "查看簽名", "SSE.Views.FormatRulesEditDlg.fillColor": "填色", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.FormatRulesEditDlg.text2Scales": "2色標", "SSE.Views.FormatRulesEditDlg.text3Scales": "3色標", "SSE.Views.FormatRulesEditDlg.textAllBorders": "所有邊界", "SSE.Views.FormatRulesEditDlg.textAppearance": "條綫外貌", "SSE.Views.FormatRulesEditDlg.textApply": "套用至範圍", "SSE.Views.FormatRulesEditDlg.textAutomatic": "自動", "SSE.Views.FormatRulesEditDlg.textAxis": "軸", "SSE.Views.FormatRulesEditDlg.textBarDirection": "條綫方向", "SSE.Views.FormatRulesEditDlg.textBold": "粗體", "SSE.Views.FormatRulesEditDlg.textBorder": "邊框", "SSE.Views.FormatRulesEditDlg.textBordersColor": "邊界顔色", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "邊框樣式", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "底部邊框", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "無法設定條件格式。", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "儲存格中點", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "內部垂直邊框", "SSE.Views.FormatRulesEditDlg.textClear": "清除", "SSE.Views.FormatRulesEditDlg.textColor": "文字顏色", "SSE.Views.FormatRulesEditDlg.textContext": "語境", "SSE.Views.FormatRulesEditDlg.textCustom": "客制", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "對角向下邊界", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "對角向上邊界", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "輸入有效公式。", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "您輸入的公式未計算為數字、日期、時間或字符串。", "SSE.Views.FormatRulesEditDlg.textEmptyText": "輸入定值。", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "输入值未是數字、日期、時間或字符串。", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "{0}定值必須大於{1}定值。", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "輸入{0}于{1}之間的數字。", "SSE.Views.FormatRulesEditDlg.textFill": "填入", "SSE.Views.FormatRulesEditDlg.textFormat": "格式", "SSE.Views.FormatRulesEditDlg.textFormula": "公式", "SSE.Views.FormatRulesEditDlg.textGradient": "漸層", "SSE.Views.FormatRulesEditDlg.textIconLabel": "{0}{1}時而", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "{0}{1}時", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "當定值是", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "一或多個圖標範圍重叠。<br>調整圖標數據范圍值，使范圍不重疊。", "SSE.Views.FormatRulesEditDlg.textIconStyle": "圖標樣式", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "內部邊界", "SSE.Views.FormatRulesEditDlg.textInvalid": "無效數據範圍", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.FormatRulesEditDlg.textItalic": "斜體", "SSE.Views.FormatRulesEditDlg.textItem": "項目", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "左到右", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "左邊框", "SSE.Views.FormatRulesEditDlg.textLongBar": "最長的槓", "SSE.Views.FormatRulesEditDlg.textMaximum": "最大", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "最大點", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "內部水平邊框", "SSE.Views.FormatRulesEditDlg.textMidpoint": "中點", "SSE.Views.FormatRulesEditDlg.textMinimum": "最小", "SSE.Views.FormatRulesEditDlg.textMinpoint": "最低點", "SSE.Views.FormatRulesEditDlg.textNegative": "負數", "SSE.Views.FormatRulesEditDlg.textNewColor": "新增自訂顏色", "SSE.Views.FormatRulesEditDlg.textNoBorders": "無邊框", "SSE.Views.FormatRulesEditDlg.textNone": "無", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "一或多個指定定值不是有效百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "{0}指定值不是有效百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "一或多個指定定值不是有效百分位。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "{0}指定值不是有效百分位。", "SSE.Views.FormatRulesEditDlg.textOutBorders": "境外", "SSE.Views.FormatRulesEditDlg.textPercent": "百分比", "SSE.Views.FormatRulesEditDlg.textPercentile": "百分位數", "SSE.Views.FormatRulesEditDlg.textPosition": "位置", "SSE.Views.FormatRulesEditDlg.textPositive": "正數", "SSE.Views.FormatRulesEditDlg.textPresets": "預設值", "SSE.Views.FormatRulesEditDlg.textPreview": "預覽", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "無法使用相對引用設定色標、數據欄、圖標集的條件格式標準。", "SSE.Views.FormatRulesEditDlg.textReverse": "顛倒圖標順序", "SSE.Views.FormatRulesEditDlg.textRight2Left": "右到左", "SSE.Views.FormatRulesEditDlg.textRightBorders": "右邊界", "SSE.Views.FormatRulesEditDlg.textRule": "規則", "SSE.Views.FormatRulesEditDlg.textSameAs": "于正相等", "SSE.Views.FormatRulesEditDlg.textSelectData": "選擇數據", "SSE.Views.FormatRulesEditDlg.textShortBar": "最短的槓", "SSE.Views.FormatRulesEditDlg.textShowBar": "只顯示槓", "SSE.Views.FormatRulesEditDlg.textShowIcon": "只顯示圖標", "SSE.Views.FormatRulesEditDlg.textSingleRef": "這種類型的引用不能用於條件格式公式。<br>請改引用為單单元格，或者設爲工作表公式如 =SUM(A1:B5)。", "SSE.Views.FormatRulesEditDlg.textSolid": "固體", "SSE.Views.FormatRulesEditDlg.textStrikeout": "淘汰", "SSE.Views.FormatRulesEditDlg.textSubscript": "下標", "SSE.Views.FormatRulesEditDlg.textSuperscript": "上標", "SSE.Views.FormatRulesEditDlg.textTopBorders": "上邊界", "SSE.Views.FormatRulesEditDlg.textUnderline": "底線", "SSE.Views.FormatRulesEditDlg.tipBorders": "邊框", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "數字格式", "SSE.Views.FormatRulesEditDlg.txtAccounting": "會計", "SSE.Views.FormatRulesEditDlg.txtCurrency": "貨幣", "SSE.Views.FormatRulesEditDlg.txtDate": "日期", "SSE.Views.FormatRulesEditDlg.txtEmpty": "這是必填欄", "SSE.Views.FormatRulesEditDlg.txtFraction": "分數", "SSE.Views.FormatRulesEditDlg.txtGeneral": "一般", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "沒有圖標", "SSE.Views.FormatRulesEditDlg.txtNumber": "數字", "SSE.Views.FormatRulesEditDlg.txtPercentage": "百分比", "SSE.Views.FormatRulesEditDlg.txtScientific": "科學的", "SSE.Views.FormatRulesEditDlg.txtText": "文字", "SSE.Views.FormatRulesEditDlg.txtTime": "時間", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "編輯格式規則", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "新的格式規則", "SSE.Views.FormatRulesManagerDlg.guestText": "來賓帳戶", "SSE.Views.FormatRulesManagerDlg.lockText": "已鎖定", "SSE.Views.FormatRulesManagerDlg.text1Above": "高於平均值1個標準差", "SSE.Views.FormatRulesManagerDlg.text1Below": "低於平均值1個標準差", "SSE.Views.FormatRulesManagerDlg.text2Above": "高於平均值2個標準差", "SSE.Views.FormatRulesManagerDlg.text2Below": "低於平均值2個標準差", "SSE.Views.FormatRulesManagerDlg.text3Above": "高於平均值3個標準差", "SSE.Views.FormatRulesManagerDlg.text3Below": "低於平均值3個標準差", "SSE.Views.FormatRulesManagerDlg.textAbove": "高於平均", "SSE.Views.FormatRulesManagerDlg.textApply": "套用至", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "儲存格定值開始於", "SSE.Views.FormatRulesManagerDlg.textBelow": "低於平均值", "SSE.Views.FormatRulesManagerDlg.textBetween": "在{0}與{1}之間", "SSE.Views.FormatRulesManagerDlg.textCellValue": "儲存格的值", "SSE.Views.FormatRulesManagerDlg.textColorScale": "分級色標", "SSE.Views.FormatRulesManagerDlg.textContains": "儲存格定值包含", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "儲存格的值是空白的", "SSE.Views.FormatRulesManagerDlg.textContainsError": "儲存格中有錯誤", "SSE.Views.FormatRulesManagerDlg.textDelete": "刪除", "SSE.Views.FormatRulesManagerDlg.textDown": "規則往下移", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "重复值", "SSE.Views.FormatRulesManagerDlg.textEdit": "編輯", "SSE.Views.FormatRulesManagerDlg.textEnds": "儲存格定值結尾為", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "等於或高於平均", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "等於或低於平均", "SSE.Views.FormatRulesManagerDlg.textFormat": "格式", "SSE.Views.FormatRulesManagerDlg.textIconSet": "圖標集", "SSE.Views.FormatRulesManagerDlg.textNew": "新", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "不在{0}與{1}之間", "SSE.Views.FormatRulesManagerDlg.textNotContains": "儲存格定值無包含", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "儲存格的值不是空白的", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "儲存格沒有錯誤", "SSE.Views.FormatRulesManagerDlg.textRules": "規則", "SSE.Views.FormatRulesManagerDlg.textScope": "顯示格式規則", "SSE.Views.FormatRulesManagerDlg.textSelectData": "選擇數據", "SSE.Views.FormatRulesManagerDlg.textSelection": "目前所選", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "這數據透視", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "此工作表", "SSE.Views.FormatRulesManagerDlg.textThisTable": "這個表格", "SSE.Views.FormatRulesManagerDlg.textUnique": "獨特值", "SSE.Views.FormatRulesManagerDlg.textUp": "規則網上移", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "該元素正在由另一個用戶編輯。", "SSE.Views.FormatRulesManagerDlg.txtTitle": "條件格式", "SSE.Views.FormatSettingsDialog.textCategory": "分類", "SSE.Views.FormatSettingsDialog.textDecimal": "小數", "SSE.Views.FormatSettingsDialog.textFormat": "格式", "SSE.Views.FormatSettingsDialog.textLinked": "鏈接到來源", "SSE.Views.FormatSettingsDialog.textSeparator": "使用1000個分隔符", "SSE.Views.FormatSettingsDialog.textSymbols": "符號", "SSE.Views.FormatSettingsDialog.textTitle": "數字格式", "SSE.Views.FormatSettingsDialog.txtAccounting": "會計", "SSE.Views.FormatSettingsDialog.txtAs10": "作為十分之一（5/10）", "SSE.Views.FormatSettingsDialog.txtAs100": "作為百分之一(50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "作為十六分之一（8/16）", "SSE.Views.FormatSettingsDialog.txtAs2": "作為一半（1/2）", "SSE.Views.FormatSettingsDialog.txtAs4": "作為四分之一（2/4）", "SSE.Views.FormatSettingsDialog.txtAs8": "八分之一（4/8）", "SSE.Views.FormatSettingsDialog.txtCurrency": "幣別", "SSE.Views.FormatSettingsDialog.txtCustom": "自訂", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "請仔細輸入自定義數字格式。電子表格編輯器不會檢查自定義格式中是否存在可能影響xlsx文件的錯誤。", "SSE.Views.FormatSettingsDialog.txtDate": "日期", "SSE.Views.FormatSettingsDialog.txtFraction": "分數", "SSE.Views.FormatSettingsDialog.txtGeneral": "一般", "SSE.Views.FormatSettingsDialog.txtNone": "無", "SSE.Views.FormatSettingsDialog.txtNumber": "數字", "SSE.Views.FormatSettingsDialog.txtPercentage": "百分比", "SSE.Views.FormatSettingsDialog.txtSample": "樣品：", "SSE.Views.FormatSettingsDialog.txtScientific": "科學的", "SSE.Views.FormatSettingsDialog.txtText": "文字", "SSE.Views.FormatSettingsDialog.txtTime": "時間", "SSE.Views.FormatSettingsDialog.txtUpto1": "最多一位（1/3）", "SSE.Views.FormatSettingsDialog.txtUpto2": "最多兩位數（12/25）", "SSE.Views.FormatSettingsDialog.txtUpto3": "最多三位數（131/135）", "SSE.Views.FormulaDialog.sDescription": "描述", "SSE.Views.FormulaDialog.textGroupDescription": "選擇功能組", "SSE.Views.FormulaDialog.textListDescription": "選擇功能", "SSE.Views.FormulaDialog.txtRecommended": "推薦的", "SSE.Views.FormulaDialog.txtSearch": "搜尋", "SSE.Views.FormulaDialog.txtTitle": "插入功能", "SSE.Views.FormulaTab.textAutomatic": "自動", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "計算當前工作表", "SSE.Views.FormulaTab.textCalculateWorkbook": "計算工作簿", "SSE.Views.FormulaTab.textManual": "手動", "SSE.Views.FormulaTab.tipCalculate": "計算", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "計算整個工作簿", "SSE.Views.FormulaTab.txtAdditional": "額外", "SSE.Views.FormulaTab.txtAutosum": "自動求和", "SSE.Views.FormulaTab.txtAutosumTip": "求和", "SSE.Views.FormulaTab.txtCalculation": "計算", "SSE.Views.FormulaTab.txtFormula": "功能", "SSE.Views.FormulaTab.txtFormulaTip": "插入功能", "SSE.Views.FormulaTab.txtMore": "更多功能", "SSE.Views.FormulaTab.txtRecent": "最近使用", "SSE.Views.FormulaWizard.textAny": "任何", "SSE.Views.FormulaWizard.textArgument": "論據", "SSE.Views.FormulaWizard.textFunction": "功能", "SSE.Views.FormulaWizard.textFunctionRes": "功能結果", "SSE.Views.FormulaWizard.textHelp": "有關此功能的幫助", "SSE.Views.FormulaWizard.textLogical": "合邏輯", "SSE.Views.FormulaWizard.textNoArgs": "該函數沒有參數", "SSE.Views.FormulaWizard.textNumber": "數字", "SSE.Views.FormulaWizard.textRef": "參考", "SSE.Views.FormulaWizard.textText": "文字", "SSE.Views.FormulaWizard.textTitle": "功能參數", "SSE.Views.FormulaWizard.textValue": "公式結果", "SSE.Views.HeaderFooterDialog.textAlign": "與頁面邊界對齊", "SSE.Views.HeaderFooterDialog.textAll": "所有頁面", "SSE.Views.HeaderFooterDialog.textBold": "粗體", "SSE.Views.HeaderFooterDialog.textCenter": "中心", "SSE.Views.HeaderFooterDialog.textColor": "文字顏色", "SSE.Views.HeaderFooterDialog.textDate": "日期", "SSE.Views.HeaderFooterDialog.textDiffFirst": "首頁不同", "SSE.Views.HeaderFooterDialog.textDiffOdd": "單/雙數頁不同", "SSE.Views.HeaderFooterDialog.textEven": "雙數頁", "SSE.Views.HeaderFooterDialog.textFileName": "檔案名稱", "SSE.Views.HeaderFooterDialog.textFirst": "第一頁", "SSE.Views.HeaderFooterDialog.textFooter": "頁尾", "SSE.Views.HeaderFooterDialog.textHeader": "標頭", "SSE.Views.HeaderFooterDialog.textInsert": "插入", "SSE.Views.HeaderFooterDialog.textItalic": "斜體", "SSE.Views.HeaderFooterDialog.textLeft": "左", "SSE.Views.HeaderFooterDialog.textMaxError": "您輸入的文本字符串太長。減少使用的字符數。", "SSE.Views.HeaderFooterDialog.textNewColor": "新增自訂顏色", "SSE.Views.HeaderFooterDialog.textOdd": "奇數頁", "SSE.Views.HeaderFooterDialog.textPageCount": "頁數", "SSE.Views.HeaderFooterDialog.textPageNum": "頁碼", "SSE.Views.HeaderFooterDialog.textPresets": "預設值", "SSE.Views.HeaderFooterDialog.textRight": "右", "SSE.Views.HeaderFooterDialog.textScale": "隨文件縮放", "SSE.Views.HeaderFooterDialog.textSheet": "表格名稱", "SSE.Views.HeaderFooterDialog.textStrikeout": "淘汰", "SSE.Views.HeaderFooterDialog.textSubscript": "下標", "SSE.Views.HeaderFooterDialog.textSuperscript": "上標", "SSE.Views.HeaderFooterDialog.textTime": "時間", "SSE.Views.HeaderFooterDialog.textTitle": "頁眉/頁腳設置", "SSE.Views.HeaderFooterDialog.textUnderline": "底線", "SSE.Views.HeaderFooterDialog.tipFontName": "字體", "SSE.Views.HeaderFooterDialog.tipFontSize": "字體大小", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "顯示", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "連結至", "SSE.Views.HyperlinkSettingsDialog.strRange": "範圍", "SSE.Views.HyperlinkSettingsDialog.strSheet": "表格", "SSE.Views.HyperlinkSettingsDialog.textCopy": "複製", "SSE.Views.HyperlinkSettingsDialog.textDefault": "選擇範圍", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "在此處輸入標題", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "在此處輸入連結", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "在此處輸入工具提示", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "外部連結", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "獲取連結", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "內部數據範圍", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.HyperlinkSettingsDialog.textNames": "定義名稱", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "選擇數據", "SSE.Views.HyperlinkSettingsDialog.textSheets": "工作表", "SSE.Views.HyperlinkSettingsDialog.textTipText": "屏幕提示文字", "SSE.Views.HyperlinkSettingsDialog.textTitle": "超連結設置", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "這是必填欄", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "此字段應為“ http://www.example.com”格式的網址", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此欄位限2083字符", "SSE.Views.ImageSettings.textAdvanced": "顯示進階設定", "SSE.Views.ImageSettings.textCrop": "修剪", "SSE.Views.ImageSettings.textCropFill": "填入", "SSE.Views.ImageSettings.textCropFit": "切合", "SSE.Views.ImageSettings.textCropToShape": "剪裁成圖形", "SSE.Views.ImageSettings.textEdit": "編輯", "SSE.Views.ImageSettings.textEditObject": "編輯物件", "SSE.Views.ImageSettings.textFlip": "翻轉", "SSE.Views.ImageSettings.textFromFile": "從檔案", "SSE.Views.ImageSettings.textFromStorage": "從存儲", "SSE.Views.ImageSettings.textFromUrl": "從 URL", "SSE.Views.ImageSettings.textHeight": "高度", "SSE.Views.ImageSettings.textHint270": "逆時針旋轉90°", "SSE.Views.ImageSettings.textHint90": "順時針旋轉90°", "SSE.Views.ImageSettings.textHintFlipH": "水平翻轉", "SSE.Views.ImageSettings.textHintFlipV": "垂直翻轉", "SSE.Views.ImageSettings.textInsert": "取代圖片", "SSE.Views.ImageSettings.textKeepRatio": "比例不變", "SSE.Views.ImageSettings.textOriginalSize": "實際大小", "SSE.Views.ImageSettings.textRecentlyUsed": "最近使用", "SSE.Views.ImageSettings.textRotate90": "旋轉90°", "SSE.Views.ImageSettings.textRotation": "旋轉", "SSE.Views.ImageSettings.textSize": "大小", "SSE.Views.ImageSettings.textWidth": "寬度", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "不要移動或調整單元格的大小", "SSE.Views.ImageSettingsAdvanced.textAlt": "替代文字", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ImageSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "標題", "SSE.Views.ImageSettingsAdvanced.textAngle": "角度", "SSE.Views.ImageSettingsAdvanced.textFlipped": "已翻轉", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "水平地", "SSE.Views.ImageSettingsAdvanced.textOneCell": "移動但不調整單元格的大小", "SSE.Views.ImageSettingsAdvanced.textRotation": "旋轉", "SSE.Views.ImageSettingsAdvanced.textSnap": "單元捕捉", "SSE.Views.ImageSettingsAdvanced.textTitle": "圖像-進階設置", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "移動並調整單元格大小", "SSE.Views.ImageSettingsAdvanced.textVertically": "垂直", "SSE.Views.LeftMenu.tipAbout": "關於", "SSE.Views.LeftMenu.tipChat": "聊天", "SSE.Views.LeftMenu.tipComments": "評論", "SSE.Views.LeftMenu.tipFile": "檔案", "SSE.Views.LeftMenu.tipPlugins": "外掛程式", "SSE.Views.LeftMenu.tipSearch": "搜尋", "SSE.Views.LeftMenu.tipSpellcheck": "拼字檢查", "SSE.Views.LeftMenu.tipSupport": "反饋與支持", "SSE.Views.LeftMenu.txtDeveloper": "開發者模式", "SSE.Views.LeftMenu.txtEditor": "試算表編輯器", "SSE.Views.LeftMenu.txtLimit": "限制存取", "SSE.Views.LeftMenu.txtTrial": "試用模式", "SSE.Views.LeftMenu.txtTrialDev": "試用開發人員模式", "SSE.Views.MacroDialog.textMacro": "宏名稱", "SSE.Views.MacroDialog.textTitle": "指定巨集", "SSE.Views.MainSettingsPrint.okButtonText": "儲存", "SSE.Views.MainSettingsPrint.strBottom": "底部", "SSE.Views.MainSettingsPrint.strLandscape": "橫向方向", "SSE.Views.MainSettingsPrint.strLeft": "左", "SSE.Views.MainSettingsPrint.strMargins": "邊界", "SSE.Views.MainSettingsPrint.strPortrait": "直向方向", "SSE.Views.MainSettingsPrint.strPrint": "列印", "SSE.Views.MainSettingsPrint.strPrintTitles": "列印標題", "SSE.Views.MainSettingsPrint.strRight": "右", "SSE.Views.MainSettingsPrint.strTop": "上方", "SSE.Views.MainSettingsPrint.textActualSize": "實際大小", "SSE.Views.MainSettingsPrint.textCustom": "自訂", "SSE.Views.MainSettingsPrint.textCustomOptions": "自訂選項", "SSE.Views.MainSettingsPrint.textFitCols": "將所有列放在一頁上", "SSE.Views.MainSettingsPrint.textFitPage": "一頁上適合紙張", "SSE.Views.MainSettingsPrint.textFitRows": "將所有行放在一頁上", "SSE.Views.MainSettingsPrint.textPageOrientation": "頁面方向", "SSE.Views.MainSettingsPrint.textPageScaling": "縮放比例", "SSE.Views.MainSettingsPrint.textPageSize": "頁面大小", "SSE.Views.MainSettingsPrint.textPrintGrid": "列印網格線", "SSE.Views.MainSettingsPrint.textPrintHeadings": "列印行和列標題", "SSE.Views.MainSettingsPrint.textRepeat": "重複...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "在左側重複列", "SSE.Views.MainSettingsPrint.textRepeatTop": "在頂部重複行", "SSE.Views.MainSettingsPrint.textSettings": "的設定", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "由於其中一些正在被編輯，因此目前無法編輯現有命名範圍，也無法創建新的命名範圍。", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "定義名稱", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.NamedRangeEditDlg.strWorkbook": "工作簿", "SSE.Views.NamedRangeEditDlg.textDataRange": "數據範圍", "SSE.Views.NamedRangeEditDlg.textExistName": "錯誤！具有這樣名稱的範圍已經存在", "SSE.Views.NamedRangeEditDlg.textInvalidName": "名稱必須以字母或下劃線開頭，並且不得包含無效字符。", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "錯誤！無效的像元範圍", "SSE.Views.NamedRangeEditDlg.textIsLocked": "錯誤！該元素正在由另一個用戶編輯。", "SSE.Views.NamedRangeEditDlg.textName": "名稱", "SSE.Views.NamedRangeEditDlg.textReservedName": "單元格公式中已經引用了您要使用的名稱。請使用其他名稱。", "SSE.Views.NamedRangeEditDlg.textScope": "範圍", "SSE.Views.NamedRangeEditDlg.textSelectData": "選擇數據", "SSE.Views.NamedRangeEditDlg.txtEmpty": "這是必填欄", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "編輯名稱", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "新名字", "SSE.Views.NamedRangePasteDlg.textNames": "命名範圍", "SSE.Views.NamedRangePasteDlg.txtTitle": "粘貼名稱", "SSE.Views.NameManagerDlg.closeButtonText": "關閉", "SSE.Views.NameManagerDlg.guestText": "來賓帳戶", "SSE.Views.NameManagerDlg.lockText": "已鎖定", "SSE.Views.NameManagerDlg.textDataRange": "數據範圍", "SSE.Views.NameManagerDlg.textDelete": "刪除", "SSE.Views.NameManagerDlg.textEdit": "編輯", "SSE.Views.NameManagerDlg.textEmpty": "尚未創建命名範圍。<br>創建至少一個命名範圍，它將出現在此字段中。", "SSE.Views.NameManagerDlg.textFilter": "過濾器", "SSE.Views.NameManagerDlg.textFilterAll": "全部", "SSE.Views.NameManagerDlg.textFilterDefNames": "定義名稱", "SSE.Views.NameManagerDlg.textFilterSheet": "範圍內的名稱", "SSE.Views.NameManagerDlg.textFilterTableNames": "表名", "SSE.Views.NameManagerDlg.textFilterWorkbook": "名稱適用於工作簿", "SSE.Views.NameManagerDlg.textNew": "新", "SSE.Views.NameManagerDlg.textnoNames": "找不到與您的過濾器匹配的命名範圍。", "SSE.Views.NameManagerDlg.textRanges": "命名範圍", "SSE.Views.NameManagerDlg.textScope": "範圍", "SSE.Views.NameManagerDlg.textWorkbook": "工作簿", "SSE.Views.NameManagerDlg.tipIsLocked": "該元素正在由另一個用戶編輯。", "SSE.Views.NameManagerDlg.txtTitle": "名稱管理員", "SSE.Views.NameManagerDlg.warnDelete": "確定要刪除此姓名: {0}?", "SSE.Views.PageMarginsDialog.textBottom": "底部", "SSE.Views.PageMarginsDialog.textLeft": "左", "SSE.Views.PageMarginsDialog.textRight": "右", "SSE.Views.PageMarginsDialog.textTitle": "邊界", "SSE.Views.PageMarginsDialog.textTop": "上方", "SSE.Views.ParagraphSettings.strLineHeight": "行間距", "SSE.Views.ParagraphSettings.strParagraphSpacing": "段落間距", "SSE.Views.ParagraphSettings.strSpacingAfter": "之後", "SSE.Views.ParagraphSettings.strSpacingBefore": "之前", "SSE.Views.ParagraphSettings.textAdvanced": "顯示進階設定", "SSE.Views.ParagraphSettings.textAt": "在", "SSE.Views.ParagraphSettings.textAtLeast": "至少", "SSE.Views.ParagraphSettings.textAuto": "多項", "SSE.Views.ParagraphSettings.textExact": "準確", "SSE.Views.ParagraphSettings.txtAutoText": "自動", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "指定的標籤將出現在此字段中", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大寫", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "雙刪除線", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "縮進", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行間距", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "之後", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "之前", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "通過", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字體", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "縮進和間距", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小大寫", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "間距", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "刪除線", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "下標", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "上標", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "標籤", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "對齊", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "多項", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字符間距", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "預設分頁", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "SSE.Views.ParagraphSettingsAdvanced.textExact": "準確", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "第一行", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "懸掛式", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "合理的", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（空）", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "移除", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "移除所有", "SSE.Views.ParagraphSettingsAdvanced.textSet": "指定", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "標籤位置", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "段落-進階設置", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "自動", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "等於", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "不以結束", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "包含", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "不含", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "之間", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "不介於", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "不等於", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "大於", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "大於或等於", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "小於", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "小於或等於", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "開始於", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "不以", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "以。。結束", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "顯示標籤如下的項目：", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "顯示以下項目：", "SSE.Views.PivotDigitalFilterDialog.textUse1": "採用 ？呈現任何單個字符", "SSE.Views.PivotDigitalFilterDialog.textUse2": "使用*表示任何系列字符", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "和", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "標籤過濾器", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "值過濾器", "SSE.Views.PivotGroupDialog.textAuto": "自動", "SSE.Views.PivotGroupDialog.textBy": "依照", "SSE.Views.PivotGroupDialog.textDays": "天", "SSE.Views.PivotGroupDialog.textEnd": "結束於", "SSE.Views.PivotGroupDialog.textError": "欄位限數值。", "SSE.Views.PivotGroupDialog.textGreaterError": "結尾編號必須大於起頭編號。", "SSE.Views.PivotGroupDialog.textHour": "小時", "SSE.Views.PivotGroupDialog.textMin": "分鐘", "SSE.Views.PivotGroupDialog.textMonth": "月", "SSE.Views.PivotGroupDialog.textNumDays": "天數", "SSE.Views.PivotGroupDialog.textQuart": "季度", "SSE.Views.PivotGroupDialog.textSec": "秒數", "SSE.Views.PivotGroupDialog.textStart": "開始於", "SSE.Views.PivotGroupDialog.textYear": "年", "SSE.Views.PivotGroupDialog.txtTitle": "組合", "SSE.Views.PivotSettings.textAdvanced": "顯示進階設定", "SSE.Views.PivotSettings.textColumns": "欄", "SSE.Views.PivotSettings.textFields": "選擇字段", "SSE.Views.PivotSettings.textFilters": "過濾器", "SSE.Views.PivotSettings.textRows": "行列", "SSE.Views.PivotSettings.textValues": "值", "SSE.Views.PivotSettings.txtAddColumn": "添加到列", "SSE.Views.PivotSettings.txtAddFilter": "添加到過濾器", "SSE.Views.PivotSettings.txtAddRow": "添加到行", "SSE.Views.PivotSettings.txtAddValues": "增到值數", "SSE.Views.PivotSettings.txtFieldSettings": "欄位設定", "SSE.Views.PivotSettings.txtMoveBegin": "移至開頭", "SSE.Views.PivotSettings.txtMoveColumn": "移至欄位", "SSE.Views.PivotSettings.txtMoveDown": "下移", "SSE.Views.PivotSettings.txtMoveEnd": "移至結尾", "SSE.Views.PivotSettings.txtMoveFilter": "移至過濾器", "SSE.Views.PivotSettings.txtMoveRow": "移至行列", "SSE.Views.PivotSettings.txtMoveUp": "上移", "SSE.Views.PivotSettings.txtMoveValues": "轉變為直", "SSE.Views.PivotSettings.txtRemove": "移除欄位", "SSE.Views.PivotSettingsAdvanced.strLayout": "名稱和佈局", "SSE.Views.PivotSettingsAdvanced.textAlt": "替代文字", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "描述", "SSE.Views.PivotSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "標題", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "更新時自動調整列寬", "SSE.Views.PivotSettingsAdvanced.textDataRange": "數據範圍", "SSE.Views.PivotSettingsAdvanced.textDataSource": "數據源", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "在報告過濾器區域中顯示字段", "SSE.Views.PivotSettingsAdvanced.textDown": "下來，然後結束", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "累計", "SSE.Views.PivotSettingsAdvanced.textHeaders": "字段標題", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.PivotSettingsAdvanced.textOver": "結束，然後向下", "SSE.Views.PivotSettingsAdvanced.textSelectData": "選擇數據", "SSE.Views.PivotSettingsAdvanced.textShowCols": "顯示列", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "顯示行和列的字段標題", "SSE.Views.PivotSettingsAdvanced.textShowRows": "顯示行", "SSE.Views.PivotSettingsAdvanced.textTitle": "數據透視表-進階設置", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "按列報告過濾器字段", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "每行報告過濾器字段", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "這是必填欄", "SSE.Views.PivotSettingsAdvanced.txtName": "名稱", "SSE.Views.PivotTable.capBlankRows": "空白行", "SSE.Views.PivotTable.capGrandTotals": "累計", "SSE.Views.PivotTable.capLayout": "報告格式", "SSE.Views.PivotTable.capSubtotals": "小計", "SSE.Views.PivotTable.mniBottomSubtotals": "在組底部顯示所有小計", "SSE.Views.PivotTable.mniInsertBlankLine": "在每個項目之後插入空白行", "SSE.Views.PivotTable.mniLayoutCompact": "以緊湊形式顯示", "SSE.Views.PivotTable.mniLayoutNoRepeat": "不要重複所有商品標籤", "SSE.Views.PivotTable.mniLayoutOutline": "以大綱形式顯示", "SSE.Views.PivotTable.mniLayoutRepeat": "重複所有商品標籤", "SSE.Views.PivotTable.mniLayoutTabular": "以表格形式顯示", "SSE.Views.PivotTable.mniNoSubtotals": "不顯示小計", "SSE.Views.PivotTable.mniOffTotals": "為行和列關閉", "SSE.Views.PivotTable.mniOnColumnsTotals": "開啟僅適用於列", "SSE.Views.PivotTable.mniOnRowsTotals": "僅限行", "SSE.Views.PivotTable.mniOnTotals": "在行和列上", "SSE.Views.PivotTable.mniRemoveBlankLine": "刪除每個項目後的空白行", "SSE.Views.PivotTable.mniTopSubtotals": "在組頂部顯示所有小計", "SSE.Views.PivotTable.textColBanded": "帶狀柱", "SSE.Views.PivotTable.textColHeader": "列標題", "SSE.Views.PivotTable.textRowBanded": "帶狀的行", "SSE.Views.PivotTable.textRowHeader": "行標題", "SSE.Views.PivotTable.tipCreatePivot": "插入樞紐分析表", "SSE.Views.PivotTable.tipGrandTotals": "顯示或隱藏總計", "SSE.Views.PivotTable.tipRefresh": "更新數據源中的信息", "SSE.Views.PivotTable.tipSelect": "選擇整個數據透視表", "SSE.Views.PivotTable.tipSubtotals": "顯示或隱藏小計", "SSE.Views.PivotTable.txtCreate": "插入表格", "SSE.Views.PivotTable.txtPivotTable": "數據透視表", "SSE.Views.PivotTable.txtRefresh": "刷新", "SSE.Views.PivotTable.txtSelect": "選擇", "SSE.Views.PrintSettings.btnDownload": "保存並下載", "SSE.Views.PrintSettings.btnPrint": "保存並列印", "SSE.Views.PrintSettings.strBottom": "底部", "SSE.Views.PrintSettings.strLandscape": "橫向方向", "SSE.Views.PrintSettings.strLeft": "左", "SSE.Views.PrintSettings.strMargins": "邊界", "SSE.Views.PrintSettings.strPortrait": "直向方向", "SSE.Views.PrintSettings.strPrint": "列印", "SSE.Views.PrintSettings.strPrintTitles": "列印標題", "SSE.Views.PrintSettings.strRight": "右", "SSE.Views.PrintSettings.strShow": "顯示", "SSE.Views.PrintSettings.strTop": "上方", "SSE.Views.PrintSettings.textActualSize": "實際大小", "SSE.Views.PrintSettings.textAllSheets": "所有工作表", "SSE.Views.PrintSettings.textCurrentSheet": "當前工作表", "SSE.Views.PrintSettings.textCustom": "自訂", "SSE.Views.PrintSettings.textCustomOptions": "自訂選項", "SSE.Views.PrintSettings.textFitCols": "將所有列放在一頁上", "SSE.Views.PrintSettings.textFitPage": "一頁上適合紙張", "SSE.Views.PrintSettings.textFitRows": "將所有行放在一頁上", "SSE.Views.PrintSettings.textHideDetails": "隱藏細節", "SSE.Views.PrintSettings.textIgnore": "忽略列印​​區域", "SSE.Views.PrintSettings.textLayout": "佈局", "SSE.Views.PrintSettings.textPageOrientation": "頁面方向", "SSE.Views.PrintSettings.textPageScaling": "縮放比例", "SSE.Views.PrintSettings.textPageSize": "頁面大小", "SSE.Views.PrintSettings.textPrintGrid": "列印網格線", "SSE.Views.PrintSettings.textPrintHeadings": "列印行和列標題", "SSE.Views.PrintSettings.textPrintRange": "列印範圍", "SSE.Views.PrintSettings.textRange": "範圍", "SSE.Views.PrintSettings.textRepeat": "重複...", "SSE.Views.PrintSettings.textRepeatLeft": "在左側重複列", "SSE.Views.PrintSettings.textRepeatTop": "在頂部重複行", "SSE.Views.PrintSettings.textSelection": "選拔", "SSE.Views.PrintSettings.textSettings": "圖紙設置", "SSE.Views.PrintSettings.textShowDetails": "顯示詳細資料", "SSE.Views.PrintSettings.textShowGrid": "顯示網格線", "SSE.Views.PrintSettings.textShowHeadings": "顯示行和列標題", "SSE.Views.PrintSettings.textTitle": "列印設定", "SSE.Views.PrintSettings.textTitlePDF": "PDF設置", "SSE.Views.PrintTitlesDialog.textFirstCol": "第一欄", "SSE.Views.PrintTitlesDialog.textFirstRow": "第一排", "SSE.Views.PrintTitlesDialog.textFrozenCols": "固定列", "SSE.Views.PrintTitlesDialog.textFrozenRows": "固定行", "SSE.Views.PrintTitlesDialog.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.PrintTitlesDialog.textLeft": "在左側重複列", "SSE.Views.PrintTitlesDialog.textNoRepeat": "不要重複", "SSE.Views.PrintTitlesDialog.textRepeat": "重複...", "SSE.Views.PrintTitlesDialog.textSelectRange": "選擇範圍", "SSE.Views.PrintTitlesDialog.textTitle": "列印標題", "SSE.Views.PrintTitlesDialog.textTop": "在頂部重複行", "SSE.Views.PrintWithPreview.txtActualSize": "實際大小", "SSE.Views.PrintWithPreview.txtAllSheets": "所有工作表", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "應用在所有工作表", "SSE.Views.PrintWithPreview.txtBottom": "底部", "SSE.Views.PrintWithPreview.txtCurrentSheet": "當前工作表", "SSE.Views.PrintWithPreview.txtCustom": "自訂", "SSE.Views.PrintWithPreview.txtCustomOptions": "自訂選項", "SSE.Views.PrintWithPreview.txtEmptyTable": "表格是空白的，無法列印。", "SSE.Views.PrintWithPreview.txtFitCols": "將所有列放在一頁上", "SSE.Views.PrintWithPreview.txtFitPage": "一頁上適合紙張", "SSE.Views.PrintWithPreview.txtFitRows": "將所有行放在一頁上", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "網格線與標題", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "頁頭/頁尾設定", "SSE.Views.PrintWithPreview.txtIgnore": "忽略列印​​區域", "SSE.Views.PrintWithPreview.txtLandscape": "橫向方向", "SSE.Views.PrintWithPreview.txtLeft": "左", "SSE.Views.PrintWithPreview.txtMargins": "邊界", "SSE.Views.PrintWithPreview.txtOf": "之 {0}", "SSE.Views.PrintWithPreview.txtPage": "頁面", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "頁碼無效", "SSE.Views.PrintWithPreview.txtPageOrientation": "頁面方向", "SSE.Views.PrintWithPreview.txtPageSize": "頁面大小", "SSE.Views.PrintWithPreview.txtPortrait": "直向方向", "SSE.Views.PrintWithPreview.txtPrint": "列印", "SSE.Views.PrintWithPreview.txtPrintGrid": "列印網格線", "SSE.Views.PrintWithPreview.txtPrintHeadings": "列印行和列標題", "SSE.Views.PrintWithPreview.txtPrintRange": "列印範圍", "SSE.Views.PrintWithPreview.txtPrintTitles": "列印標題", "SSE.Views.PrintWithPreview.txtRepeat": "重複...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "在左側重複列", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "在頂部重複行", "SSE.Views.PrintWithPreview.txtRight": "右", "SSE.Views.PrintWithPreview.txtSave": "存檔", "SSE.Views.PrintWithPreview.txtScaling": "縮放比例", "SSE.Views.PrintWithPreview.txtSelection": "選項", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "工作表設定", "SSE.Views.PrintWithPreview.txtSheet": "工作表：{0}", "SSE.Views.PrintWithPreview.txtTop": "上方", "SSE.Views.ProtectDialog.textExistName": "錯誤！有標題的範圍已存在", "SSE.Views.ProtectDialog.textInvalidName": "範圍標準必須為字母起頭而只能包含數字、字母和空格。", "SSE.Views.ProtectDialog.textInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.ProtectDialog.textSelectData": "選擇數據", "SSE.Views.ProtectDialog.txtAllow": "允許所有用戶:", "SSE.Views.ProtectDialog.txtAutofilter": "使用自動分類", "SSE.Views.ProtectDialog.txtDelCols": "列刪除", "SSE.Views.ProtectDialog.txtDelRows": "行刪除", "SSE.Views.ProtectDialog.txtEmpty": "這是必填欄", "SSE.Views.ProtectDialog.txtFormatCells": "儲存格格式化", "SSE.Views.ProtectDialog.txtFormatCols": "格式化柱列", "SSE.Views.ProtectDialog.txtFormatRows": "格式化行列", "SSE.Views.ProtectDialog.txtIncorrectPwd": "確認密碼不相同", "SSE.Views.ProtectDialog.txtInsCols": "插入欄列", "SSE.Views.ProtectDialog.txtInsHyper": "插入超鏈接", "SSE.Views.ProtectDialog.txtInsRows": "插入行列", "SSE.Views.ProtectDialog.txtObjs": "編輯物件", "SSE.Views.ProtectDialog.txtOptional": "可選的", "SSE.Views.ProtectDialog.txtPassword": "密碼", "SSE.Views.ProtectDialog.txtPivot": "使用PivotTable和PivotChart", "SSE.Views.ProtectDialog.txtProtect": "保護", "SSE.Views.ProtectDialog.txtRange": "範圍", "SSE.Views.ProtectDialog.txtRangeName": "標題", "SSE.Views.ProtectDialog.txtRepeat": "重複輸入密碼", "SSE.Views.ProtectDialog.txtScen": "編輯場景", "SSE.Views.ProtectDialog.txtSelLocked": "選鎖定儲存格", "SSE.Views.ProtectDialog.txtSelUnLocked": "選無鎖定儲存格", "SSE.Views.ProtectDialog.txtSheetDescription": "防止其他用戶編改，可限制其他用戶的編輯權限。", "SSE.Views.ProtectDialog.txtSheetTitle": "保護工作表", "SSE.Views.ProtectDialog.txtSort": "分類", "SSE.Views.ProtectDialog.txtWarning": "警告：如果失去密碼，將無法取回。請妥善保存。", "SSE.Views.ProtectDialog.txtWBDescription": "為防止其他用戶查看隱藏的工作表，添加、移動、刪除或隱藏工作表和重命名工作表，您可以設定密碼保護工作表架構。", "SSE.Views.ProtectDialog.txtWBTitle": "保護工作簿結構", "SSE.Views.ProtectRangesDlg.guestText": "來賓帳戶", "SSE.Views.ProtectRangesDlg.lockText": "已鎖定", "SSE.Views.ProtectRangesDlg.textDelete": "刪除", "SSE.Views.ProtectRangesDlg.textEdit": "編輯", "SSE.Views.ProtectRangesDlg.textEmpty": "無範圍可編輯", "SSE.Views.ProtectRangesDlg.textNew": "新", "SSE.Views.ProtectRangesDlg.textProtect": "保護工作表", "SSE.Views.ProtectRangesDlg.textPwd": "密碼", "SSE.Views.ProtectRangesDlg.textRange": "範圍", "SSE.Views.ProtectRangesDlg.textRangesDesc": "工作表受保護時，範圍為密碼解鎖。", "SSE.Views.ProtectRangesDlg.textTitle": "標題", "SSE.Views.ProtectRangesDlg.tipIsLocked": "該元素正在由另一個用戶編輯。", "SSE.Views.ProtectRangesDlg.txtEditRange": "編輯範圍", "SSE.Views.ProtectRangesDlg.txtNewRange": "新範圍", "SSE.Views.ProtectRangesDlg.txtNo": "沒有", "SSE.Views.ProtectRangesDlg.txtTitle": "允許用戶範圍編輯", "SSE.Views.ProtectRangesDlg.txtYes": "是", "SSE.Views.ProtectRangesDlg.warnDelete": "確定要刪除此姓名: {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "欄", "SSE.Views.RemoveDuplicatesDialog.textDescription": "要刪除重複的值，請選擇一個或多個包含重複的列。", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "我的數據有標題", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "全選", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "刪除重複項", "SSE.Views.RightMenu.txtCellSettings": "單元格設置", "SSE.Views.RightMenu.txtChartSettings": "圖表設定", "SSE.Views.RightMenu.txtImageSettings": "影像設定", "SSE.Views.RightMenu.txtParagraphSettings": "段落設定", "SSE.Views.RightMenu.txtPivotSettings": "數據透視表設置", "SSE.Views.RightMenu.txtSettings": "一般設定", "SSE.Views.RightMenu.txtShapeSettings": "形狀設定", "SSE.Views.RightMenu.txtSignatureSettings": "簽名設置", "SSE.Views.RightMenu.txtSlicerSettings": "切片器設置", "SSE.Views.RightMenu.txtSparklineSettings": "走勢圖設置", "SSE.Views.RightMenu.txtTableSettings": "表格設定", "SSE.Views.RightMenu.txtTextArtSettings": "文字藝術設定", "SSE.Views.ScaleDialog.textAuto": "自動", "SSE.Views.ScaleDialog.textError": "輸入的值不正確。", "SSE.Views.ScaleDialog.textFewPages": "頁", "SSE.Views.ScaleDialog.textFitTo": "適合", "SSE.Views.ScaleDialog.textHeight": "高度", "SSE.Views.ScaleDialog.textManyPages": "頁", "SSE.Views.ScaleDialog.textOnePage": "頁面", "SSE.Views.ScaleDialog.textScaleTo": "縮放到", "SSE.Views.ScaleDialog.textTitle": "比例設置", "SSE.Views.ScaleDialog.textWidth": "寬度", "SSE.Views.SetValueDialog.txtMaxText": "此字段的最大值為{0}", "SSE.Views.SetValueDialog.txtMinText": "此字段的最小值為{0}", "SSE.Views.ShapeSettings.strBackground": "背景顏色", "SSE.Views.ShapeSettings.strChange": "更改自動形狀", "SSE.Views.ShapeSettings.strColor": "顏色", "SSE.Views.ShapeSettings.strFill": "填入", "SSE.Views.ShapeSettings.strForeground": "前景色", "SSE.Views.ShapeSettings.strPattern": "模式", "SSE.Views.ShapeSettings.strShadow": "顯示陰影", "SSE.Views.ShapeSettings.strSize": "大小", "SSE.Views.ShapeSettings.strStroke": "筆鋒", "SSE.Views.ShapeSettings.strTransparency": "透明度", "SSE.Views.ShapeSettings.strType": "類型", "SSE.Views.ShapeSettings.textAdvanced": "顯示進階設定", "SSE.Views.ShapeSettings.textAngle": "角度", "SSE.Views.ShapeSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入0 pt至1584 pt之間的值。", "SSE.Views.ShapeSettings.textColor": "填充顏色", "SSE.Views.ShapeSettings.textDirection": "方向", "SSE.Views.ShapeSettings.textEmptyPattern": "無模式", "SSE.Views.ShapeSettings.textFlip": "翻轉", "SSE.Views.ShapeSettings.textFromFile": "從檔案", "SSE.Views.ShapeSettings.textFromStorage": "從存儲", "SSE.Views.ShapeSettings.textFromUrl": "從 URL", "SSE.Views.ShapeSettings.textGradient": "漸變點", "SSE.Views.ShapeSettings.textGradientFill": "漸層填充", "SSE.Views.ShapeSettings.textHint270": "逆時針旋轉90°", "SSE.Views.ShapeSettings.textHint90": "順時針旋轉90°", "SSE.Views.ShapeSettings.textHintFlipH": "水平翻轉", "SSE.Views.ShapeSettings.textHintFlipV": "垂直翻轉", "SSE.Views.ShapeSettings.textImageTexture": "圖片或紋理", "SSE.Views.ShapeSettings.textLinear": "線性的", "SSE.Views.ShapeSettings.textNoFill": "沒有填充", "SSE.Views.ShapeSettings.textOriginalSize": "原始尺寸", "SSE.Views.ShapeSettings.textPatternFill": "模式", "SSE.Views.ShapeSettings.textPosition": "位置", "SSE.Views.ShapeSettings.textRadial": "徑向的", "SSE.Views.ShapeSettings.textRecentlyUsed": "最近使用", "SSE.Views.ShapeSettings.textRotate90": "旋轉90°", "SSE.Views.ShapeSettings.textRotation": "旋轉", "SSE.Views.ShapeSettings.textSelectImage": "選擇圖片", "SSE.Views.ShapeSettings.textSelectTexture": "選擇", "SSE.Views.ShapeSettings.textStretch": "延伸", "SSE.Views.ShapeSettings.textStyle": "樣式", "SSE.Views.ShapeSettings.textTexture": "從紋理", "SSE.Views.ShapeSettings.textTile": "磚瓦", "SSE.Views.ShapeSettings.tipAddGradientPoint": "添加漸變點", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "刪除漸變點", "SSE.Views.ShapeSettings.txtBrownPaper": "牛皮紙", "SSE.Views.ShapeSettings.txtCanvas": "帆布", "SSE.Views.ShapeSettings.txtCarton": "紙箱", "SSE.Views.ShapeSettings.txtDarkFabric": "深色面料", "SSE.Views.ShapeSettings.txtGrain": "紋", "SSE.Views.ShapeSettings.txtGranite": "花崗岩", "SSE.Views.ShapeSettings.txtGreyPaper": "灰紙", "SSE.Views.ShapeSettings.txtKnit": "編織", "SSE.Views.ShapeSettings.txtLeather": "皮革", "SSE.Views.ShapeSettings.txtNoBorders": "無線條", "SSE.Views.ShapeSettings.txtPapyrus": "紙莎草紙", "SSE.Views.ShapeSettings.txtWood": "木頭", "SSE.Views.ShapeSettingsAdvanced.strColumns": "欄", "SSE.Views.ShapeSettingsAdvanced.strMargins": "文字填充", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "不要移動或調整單元格的大小", "SSE.Views.ShapeSettingsAdvanced.textAlt": "替代文字", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "標題", "SSE.Views.ShapeSettingsAdvanced.textAngle": "角度", "SSE.Views.ShapeSettingsAdvanced.textArrows": "箭頭", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "自動調整", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "開始大小", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "開始樣式", "SSE.Views.ShapeSettingsAdvanced.textBevel": "斜角", "SSE.Views.ShapeSettingsAdvanced.textBottom": "底部", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Cap 類型", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "列數", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "端部尺寸", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "結束樣式", "SSE.Views.ShapeSettingsAdvanced.textFlat": "平面", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "已翻轉", "SSE.Views.ShapeSettingsAdvanced.textHeight": "高度", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "水平地", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "加入類型", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "比例不變", "SSE.Views.ShapeSettingsAdvanced.textLeft": "左", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "線型", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "移動但不調整單元格的大小", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "允許文字溢出形狀", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "調整形狀以適合文本", "SSE.Views.ShapeSettingsAdvanced.textRight": "右", "SSE.Views.ShapeSettingsAdvanced.textRotation": "旋轉", "SSE.Views.ShapeSettingsAdvanced.textRound": "圓", "SSE.Views.ShapeSettingsAdvanced.textSize": "大小", "SSE.Views.ShapeSettingsAdvanced.textSnap": "單元捕捉", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "欄之前的距離", "SSE.Views.ShapeSettingsAdvanced.textSquare": "正方形", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "文字框", "SSE.Views.ShapeSettingsAdvanced.textTitle": "形狀 - 進階設定", "SSE.Views.ShapeSettingsAdvanced.textTop": "上方", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "移動並調整單元格大小", "SSE.Views.ShapeSettingsAdvanced.textVertically": "垂直", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "重量和箭頭", "SSE.Views.ShapeSettingsAdvanced.textWidth": "寬度", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "SSE.Views.SignatureSettings.strDelete": "刪除簽名", "SSE.Views.SignatureSettings.strDetails": "簽名細節", "SSE.Views.SignatureSettings.strInvalid": "無效的簽名", "SSE.Views.SignatureSettings.strRequested": "要求的簽名", "SSE.Views.SignatureSettings.strSetup": "簽名設置", "SSE.Views.SignatureSettings.strSign": "簽名", "SSE.Views.SignatureSettings.strSignature": "簽名", "SSE.Views.SignatureSettings.strSigner": "簽名者", "SSE.Views.SignatureSettings.strValid": "有效簽名", "SSE.Views.SignatureSettings.txtContinueEditing": "仍要編輯", "SSE.Views.SignatureSettings.txtEditWarning": "編輯將刪除電子表格中的簽名。<br>確定要繼續嗎？", "SSE.Views.SignatureSettings.txtRemoveWarning": "確定移除此簽名?<br>這動作無法復原.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "該電子表格需要簽名。", "SSE.Views.SignatureSettings.txtSigned": "有效簽名已添加到電子表格中。電子表格受到保護，無法編輯。", "SSE.Views.SignatureSettings.txtSignedInvalid": "電子表格中的某些數字簽名無效或無法驗證。電子表格受到保護，無法編輯。", "SSE.Views.SlicerAddDialog.textColumns": "欄", "SSE.Views.SlicerAddDialog.txtTitle": "插入切片機", "SSE.Views.SlicerSettings.strHideNoData": "隱藏沒有數據的項目", "SSE.Views.SlicerSettings.strIndNoData": "直觀地指示沒有數據的項目", "SSE.Views.SlicerSettings.strShowDel": "顯示從數據源中刪除的項目", "SSE.Views.SlicerSettings.strShowNoData": "顯示最後沒有數據的項目", "SSE.Views.SlicerSettings.strSorting": "排序與過濾", "SSE.Views.SlicerSettings.textAdvanced": "顯示進階設定", "SSE.Views.SlicerSettings.textAsc": "上升", "SSE.Views.SlicerSettings.textAZ": "從A到Z", "SSE.Views.SlicerSettings.textButtons": "按鈕", "SSE.Views.SlicerSettings.textColumns": "欄", "SSE.Views.SlicerSettings.textDesc": "降序", "SSE.Views.SlicerSettings.textHeight": "高度", "SSE.Views.SlicerSettings.textHor": "水平", "SSE.Views.SlicerSettings.textKeepRatio": "比例不變", "SSE.Views.SlicerSettings.textLargeSmall": "最大到最小", "SSE.Views.SlicerSettings.textLock": "禁用調整大小或移動", "SSE.Views.SlicerSettings.textNewOld": "最新到最舊", "SSE.Views.SlicerSettings.textOldNew": "最舊到最新", "SSE.Views.SlicerSettings.textPosition": "位置", "SSE.Views.SlicerSettings.textSize": "大小", "SSE.Views.SlicerSettings.textSmallLarge": "最小到最大", "SSE.Views.SlicerSettings.textStyle": "樣式", "SSE.Views.SlicerSettings.textVert": "垂直", "SSE.Views.SlicerSettings.textWidth": "寬度", "SSE.Views.SlicerSettings.textZA": "從Z到A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "按鈕", "SSE.Views.SlicerSettingsAdvanced.strColumns": "欄", "SSE.Views.SlicerSettingsAdvanced.strHeight": "高度", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "隱藏沒有數據的項目", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "直觀地指示沒有數據的項目", "SSE.Views.SlicerSettingsAdvanced.strReferences": "參考文獻", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "顯示從數據源中刪除的項目", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "顯示標題", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "顯示最後沒有數據的項目", "SSE.Views.SlicerSettingsAdvanced.strSize": "大小", "SSE.Views.SlicerSettingsAdvanced.strSorting": "排序與過濾", "SSE.Views.SlicerSettingsAdvanced.strStyle": "樣式", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "樣式和尺寸", "SSE.Views.SlicerSettingsAdvanced.strWidth": "寬度", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "不要移動或調整單元格的大小", "SSE.Views.SlicerSettingsAdvanced.textAlt": "替代文字", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "描述", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "標題", "SSE.Views.SlicerSettingsAdvanced.textAsc": "上升", "SSE.Views.SlicerSettingsAdvanced.textAZ": "從A到Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "降序", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "公式中使用的名稱", "SSE.Views.SlicerSettingsAdvanced.textHeader": "標頭", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "比例不變", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "最大到最小", "SSE.Views.SlicerSettingsAdvanced.textName": "名稱", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "最新到最舊", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "最舊到最新", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "移動但不調整單元格的大小", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "最小到最大", "SSE.Views.SlicerSettingsAdvanced.textSnap": "單元捕捉", "SSE.Views.SlicerSettingsAdvanced.textSort": "分類", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "來源名稱", "SSE.Views.SlicerSettingsAdvanced.textTitle": "切片器-進階設置", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "移動並調整單元格大小", "SSE.Views.SlicerSettingsAdvanced.textZA": "從Z到A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "這是必填欄", "SSE.Views.SortDialog.errorEmpty": "所有排序條件必須指定一個列或行。", "SSE.Views.SortDialog.errorMoreOneCol": "選擇了多個列。", "SSE.Views.SortDialog.errorMoreOneRow": "選擇了多個行。", "SSE.Views.SortDialog.errorNotOriginalCol": "您選擇的列不在原始選定範圍內。", "SSE.Views.SortDialog.errorNotOriginalRow": "您選擇的行不在原始選定範圍內。", "SSE.Views.SortDialog.errorSameColumnColor": "％1被多次用相同的顏色排序。<br>刪除重複的排序條件，然後重試。", "SSE.Views.SortDialog.errorSameColumnValue": "％1多次按值排序。<br>刪除重複的排序條件，然後重試。", "SSE.Views.SortDialog.textAdd": "添加等級", "SSE.Views.SortDialog.textAsc": "上升", "SSE.Views.SortDialog.textAuto": "自動", "SSE.Views.SortDialog.textAZ": "從A到Z", "SSE.Views.SortDialog.textBelow": "之下", "SSE.Views.SortDialog.textCellColor": "單元格顏色", "SSE.Views.SortDialog.textColumn": "欄", "SSE.Views.SortDialog.textCopy": "複製等級", "SSE.Views.SortDialog.textDelete": "刪除等級", "SSE.Views.SortDialog.textDesc": "降序", "SSE.Views.SortDialog.textDown": "向下移動級別", "SSE.Views.SortDialog.textFontColor": "字體顏色", "SSE.Views.SortDialog.textLeft": "左", "SSE.Views.SortDialog.textMoreCols": "（更多列...）", "SSE.Views.SortDialog.textMoreRows": "（更多行...）", "SSE.Views.SortDialog.textNone": "無", "SSE.Views.SortDialog.textOptions": "選項", "SSE.Views.SortDialog.textOrder": "排序", "SSE.Views.SortDialog.textRight": "右", "SSE.Views.SortDialog.textRow": "行", "SSE.Views.SortDialog.textSort": "排序", "SSE.Views.SortDialog.textSortBy": "排序方式", "SSE.Views.SortDialog.textThenBy": "然後", "SSE.Views.SortDialog.textTop": "上方", "SSE.Views.SortDialog.textUp": "向上移動", "SSE.Views.SortDialog.textValues": "值", "SSE.Views.SortDialog.textZA": "從Z到A", "SSE.Views.SortDialog.txtInvalidRange": "無效的單元格範圍。", "SSE.Views.SortDialog.txtTitle": "分類", "SSE.Views.SortFilterDialog.textAsc": "由（A到Z）升序", "SSE.Views.SortFilterDialog.textDesc": "降序（Z到A）", "SSE.Views.SortFilterDialog.txtTitle": "分類", "SSE.Views.SortOptionsDialog.textCase": "區分大小寫", "SSE.Views.SortOptionsDialog.textHeaders": "我的數據有標題", "SSE.Views.SortOptionsDialog.textLeftRight": "從左到右排序", "SSE.Views.SortOptionsDialog.textOrientation": "方向", "SSE.Views.SortOptionsDialog.textTitle": "排序選項", "SSE.Views.SortOptionsDialog.textTopBottom": "從上到下排序", "SSE.Views.SpecialPasteDialog.textAdd": "新增", "SSE.Views.SpecialPasteDialog.textAll": "全部", "SSE.Views.SpecialPasteDialog.textBlanks": "跳過空白", "SSE.Views.SpecialPasteDialog.textColWidth": "列寬", "SSE.Views.SpecialPasteDialog.textComments": "評論", "SSE.Views.SpecialPasteDialog.textDiv": "劃分", "SSE.Views.SpecialPasteDialog.textFFormat": "公式和格式", "SSE.Views.SpecialPasteDialog.textFNFormat": "公式和數字格式", "SSE.Views.SpecialPasteDialog.textFormats": "格式", "SSE.Views.SpecialPasteDialog.textFormulas": "公式", "SSE.Views.SpecialPasteDialog.textFWidth": "公式和列寬", "SSE.Views.SpecialPasteDialog.textMult": "乘", "SSE.Views.SpecialPasteDialog.textNone": "無", "SSE.Views.SpecialPasteDialog.textOperation": "操作", "SSE.Views.SpecialPasteDialog.textPaste": "貼上", "SSE.Views.SpecialPasteDialog.textSub": "減去", "SSE.Views.SpecialPasteDialog.textTitle": "特別貼黏", "SSE.Views.SpecialPasteDialog.textTranspose": "轉置", "SSE.Views.SpecialPasteDialog.textValues": "值", "SSE.Views.SpecialPasteDialog.textVFormat": "值和格式", "SSE.Views.SpecialPasteDialog.textVNFormat": "值和數字格式", "SSE.Views.SpecialPasteDialog.textWBorders": "所有,除邊界", "SSE.Views.Spellcheck.noSuggestions": "沒有拼寫建議", "SSE.Views.Spellcheck.textChange": "變更", "SSE.Views.Spellcheck.textChangeAll": "全部更改", "SSE.Views.Spellcheck.textIgnore": "忽視", "SSE.Views.Spellcheck.textIgnoreAll": "忽略所有", "SSE.Views.Spellcheck.txtAddToDictionary": "添加到字典", "SSE.Views.Spellcheck.txtClosePanel": "密集拼寫檢查", "SSE.Views.Spellcheck.txtComplete": "拼寫檢查已完成", "SSE.Views.Spellcheck.txtDictionaryLanguage": "字典語言", "SSE.Views.Spellcheck.txtNextTip": "轉到下一個單詞", "SSE.Views.Spellcheck.txtSpelling": "拼寫", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "（複製到結尾）", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "（移至結尾）", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "複製工作表之前", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "在工作表前移動", "SSE.Views.Statusbar.filteredRecordsText": "已過濾{1}個記錄中的{0}個", "SSE.Views.Statusbar.filteredText": "過濾模式", "SSE.Views.Statusbar.itemAverage": "平均", "SSE.Views.Statusbar.itemCopy": "複製", "SSE.Views.Statusbar.itemCount": "計數", "SSE.Views.Statusbar.itemDelete": "刪除", "SSE.Views.Statusbar.itemHidden": "隱長", "SSE.Views.Statusbar.itemHide": "隱藏", "SSE.Views.Statusbar.itemInsert": "插入", "SSE.Views.Statusbar.itemMaximum": "最大", "SSE.Views.Statusbar.itemMinimum": "最低", "SSE.Views.Statusbar.itemMove": "搬移", "SSE.Views.Statusbar.itemProtect": "保護", "SSE.Views.Statusbar.itemRename": "重新命名", "SSE.Views.Statusbar.itemStatus": "保存狀態", "SSE.Views.Statusbar.itemSum": "總和", "SSE.Views.Statusbar.itemTabColor": "標籤顏色", "SSE.Views.Statusbar.itemUnProtect": "解鎖", "SSE.Views.Statusbar.RenameDialog.errNameExists": "具有這樣名稱的工作表已經存在。", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "工作表名稱不能包含以下字符：\\ / *？[]：", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "表格名稱", "SSE.Views.Statusbar.selectAllSheets": "選擇所有工作表", "SSE.Views.Statusbar.sheetIndexText": "工作表{0}共{1}", "SSE.Views.Statusbar.textAverage": "平均", "SSE.Views.Statusbar.textCount": "計數", "SSE.Views.Statusbar.textMax": "最高", "SSE.Views.Statusbar.textMin": "最低", "SSE.Views.Statusbar.textNewColor": "新增自訂顏色", "SSE.Views.Statusbar.textNoColor": "無顏色", "SSE.Views.Statusbar.textSum": "總和", "SSE.Views.Statusbar.tipAddTab": "添加工作表", "SSE.Views.Statusbar.tipFirst": "滾動到第一張紙", "SSE.Views.Statusbar.tipLast": "滾動到最後張紙", "SSE.Views.Statusbar.tipListOfSheets": "工作表列表", "SSE.Views.Statusbar.tipNext": "向右滾動工作表列表", "SSE.Views.Statusbar.tipPrev": "向左滾動工作表列表", "SSE.Views.Statusbar.tipZoomFactor": "放大", "SSE.Views.Statusbar.tipZoomIn": "放大", "SSE.Views.Statusbar.tipZoomOut": "縮小", "SSE.Views.Statusbar.ungroupSheets": "取消工作表分組", "SSE.Views.Statusbar.zoomText": "放大{0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "無法對所選單元格範圍執行此操作。<br>選擇與現有單元格範圍不同的統一數據范圍，然後重試。", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "無法完成所選單元格範圍的操作。<br>選擇一個範圍，以使第一表行位於同一行上，並且結果表與當前表重疊。", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "無法完成所選單元格範圍的操作。<br>選擇一個不包含其他表的範圍。", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "表中不允許使用多單元格數組公式。", "SSE.Views.TableOptionsDialog.txtEmpty": "這是必填欄", "SSE.Views.TableOptionsDialog.txtFormat": "建立表格", "SSE.Views.TableOptionsDialog.txtInvalidRange": "錯誤！無效的單元格範圍", "SSE.Views.TableOptionsDialog.txtNote": "標頭必須保留在同一行中，並且結果表範圍必須與原始表範圍重疊。", "SSE.Views.TableOptionsDialog.txtTitle": "標題", "SSE.Views.TableSettings.deleteColumnText": "刪除欄位", "SSE.Views.TableSettings.deleteRowText": "刪除行列", "SSE.Views.TableSettings.deleteTableText": "刪除表格", "SSE.Views.TableSettings.insertColumnLeftText": "向左插入列", "SSE.Views.TableSettings.insertColumnRightText": "向右插入列", "SSE.Views.TableSettings.insertRowAboveText": "在上方插入行", "SSE.Views.TableSettings.insertRowBelowText": "在下方插入行", "SSE.Views.TableSettings.notcriticalErrorTitle": "警告", "SSE.Views.TableSettings.selectColumnText": "選擇整個列", "SSE.Views.TableSettings.selectDataText": "選擇列數據", "SSE.Views.TableSettings.selectRowText": "選擇列", "SSE.Views.TableSettings.selectTableText": "選擇表格", "SSE.Views.TableSettings.textActions": "表動作", "SSE.Views.TableSettings.textAdvanced": "顯示進階設定", "SSE.Views.TableSettings.textBanded": "帶狀", "SSE.Views.TableSettings.textColumns": "欄", "SSE.Views.TableSettings.textConvertRange": "轉換為範圍", "SSE.Views.TableSettings.textEdit": "行和列", "SSE.Views.TableSettings.textEmptyTemplate": "\n沒有模板", "SSE.Views.TableSettings.textExistName": "錯誤！具有這樣名稱的範圍已經存在", "SSE.Views.TableSettings.textFilter": "篩選按鈕", "SSE.Views.TableSettings.textFirst": "第一", "SSE.Views.TableSettings.textHeader": "標頭", "SSE.Views.TableSettings.textInvalidName": "錯誤！無效的表名", "SSE.Views.TableSettings.textIsLocked": "該元素正在由另一個用戶編輯。", "SSE.Views.TableSettings.textLast": "最後", "SSE.Views.TableSettings.textLongOperation": "運行時間長", "SSE.Views.TableSettings.textPivot": "插入樞紐分析表", "SSE.Views.TableSettings.textRemDuplicates": "刪除重複項", "SSE.Views.TableSettings.textReservedName": "單元格公式中已經引用了您要使用的名稱。請使用其他名稱。", "SSE.Views.TableSettings.textResize": "調整表格", "SSE.Views.TableSettings.textRows": "行列", "SSE.Views.TableSettings.textSelectData": "選擇數據", "SSE.Views.TableSettings.textSlicer": "插入切片器", "SSE.Views.TableSettings.textTableName": "表名", "SSE.Views.TableSettings.textTemplate": "從範本中選擇", "SSE.Views.TableSettings.textTotal": "總計", "SSE.Views.TableSettings.warnLongOperation": "您將要執行的操作可能需要花費大量時間才能完成。<br>您確定要繼續嗎？", "SSE.Views.TableSettingsAdvanced.textAlt": "替代文字", "SSE.Views.TableSettingsAdvanced.textAltDescription": "描述", "SSE.Views.TableSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "SSE.Views.TableSettingsAdvanced.textAltTitle": "標題", "SSE.Views.TableSettingsAdvanced.textTitle": "表格 - 進階設定", "SSE.Views.TextArtSettings.strBackground": "背景顏色", "SSE.Views.TextArtSettings.strColor": "顏色", "SSE.Views.TextArtSettings.strFill": "填入", "SSE.Views.TextArtSettings.strForeground": "前景色", "SSE.Views.TextArtSettings.strPattern": "模式", "SSE.Views.TextArtSettings.strSize": "大小", "SSE.Views.TextArtSettings.strStroke": "筆鋒", "SSE.Views.TextArtSettings.strTransparency": "透明度", "SSE.Views.TextArtSettings.strType": "類型", "SSE.Views.TextArtSettings.textAngle": "角度", "SSE.Views.TextArtSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入0 pt至1584 pt之間的值。", "SSE.Views.TextArtSettings.textColor": "填充顏色", "SSE.Views.TextArtSettings.textDirection": "方向", "SSE.Views.TextArtSettings.textEmptyPattern": "無模式", "SSE.Views.TextArtSettings.textFromFile": "從檔案", "SSE.Views.TextArtSettings.textFromUrl": "從 URL", "SSE.Views.TextArtSettings.textGradient": "漸變點", "SSE.Views.TextArtSettings.textGradientFill": "漸層填充", "SSE.Views.TextArtSettings.textImageTexture": "圖片或紋理", "SSE.Views.TextArtSettings.textLinear": "線性的", "SSE.Views.TextArtSettings.textNoFill": "沒有填充", "SSE.Views.TextArtSettings.textPatternFill": "模式", "SSE.Views.TextArtSettings.textPosition": "位置", "SSE.Views.TextArtSettings.textRadial": "徑向的", "SSE.Views.TextArtSettings.textSelectTexture": "選擇", "SSE.Views.TextArtSettings.textStretch": "延伸", "SSE.Views.TextArtSettings.textStyle": "樣式", "SSE.Views.TextArtSettings.textTemplate": "樣板", "SSE.Views.TextArtSettings.textTexture": "從紋理", "SSE.Views.TextArtSettings.textTile": "磚瓦", "SSE.Views.TextArtSettings.textTransform": "轉變", "SSE.Views.TextArtSettings.tipAddGradientPoint": "添加漸變點", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "刪除漸變點", "SSE.Views.TextArtSettings.txtBrownPaper": "牛皮紙", "SSE.Views.TextArtSettings.txtCanvas": "帆布", "SSE.Views.TextArtSettings.txtCarton": "紙箱", "SSE.Views.TextArtSettings.txtDarkFabric": "深色面料", "SSE.Views.TextArtSettings.txtGrain": "紋", "SSE.Views.TextArtSettings.txtGranite": "花崗岩", "SSE.Views.TextArtSettings.txtGreyPaper": "灰紙", "SSE.Views.TextArtSettings.txtKnit": "編織", "SSE.Views.TextArtSettings.txtLeather": "皮革", "SSE.Views.TextArtSettings.txtNoBorders": "無線條", "SSE.Views.TextArtSettings.txtPapyrus": "紙莎草紙", "SSE.Views.TextArtSettings.txtWood": "木頭", "SSE.Views.Toolbar.capBtnAddComment": "增加留言", "SSE.Views.Toolbar.capBtnColorSchemas": "配色", "SSE.Views.Toolbar.capBtnComment": "評論", "SSE.Views.Toolbar.capBtnInsHeader": "頁首/頁尾", "SSE.Views.Toolbar.capBtnInsSlicer": "切片機", "SSE.Views.Toolbar.capBtnInsSymbol": "符號", "SSE.Views.Toolbar.capBtnMargins": "邊界", "SSE.Views.Toolbar.capBtnPageOrient": "方向", "SSE.Views.Toolbar.capBtnPageSize": "大小", "SSE.Views.Toolbar.capBtnPrintArea": "列印區", "SSE.Views.Toolbar.capBtnPrintTitles": "列印標題", "SSE.Views.Toolbar.capBtnScale": "縮放以適合", "SSE.Views.Toolbar.capImgAlign": "對齊", "SSE.Views.Toolbar.capImgBackward": "向後發送", "SSE.Views.Toolbar.capImgForward": "向前帶進", "SSE.Views.Toolbar.capImgGroup": "群組", "SSE.Views.Toolbar.capInsertChart": "圖表", "SSE.Views.Toolbar.capInsertEquation": "公式", "SSE.Views.Toolbar.capInsertHyperlink": "超連結", "SSE.Views.Toolbar.capInsertImage": "圖像", "SSE.Views.Toolbar.capInsertShape": "形狀", "SSE.Views.Toolbar.capInsertSpark": "走勢圖", "SSE.Views.Toolbar.capInsertTable": "表格", "SSE.Views.Toolbar.capInsertText": "文字框", "SSE.Views.Toolbar.capInsertTextart": "文字藝術", "SSE.Views.Toolbar.mniImageFromFile": "圖片來自文件", "SSE.Views.Toolbar.mniImageFromStorage": "來自存儲的圖像", "SSE.Views.Toolbar.mniImageFromUrl": "來自網址的圖片", "SSE.Views.Toolbar.textAddPrintArea": "添加到列印區", "SSE.Views.Toolbar.textAlignBottom": "底部對齊", "SSE.Views.Toolbar.textAlignCenter": "居中對齊", "SSE.Views.Toolbar.textAlignJust": "合理的", "SSE.Views.Toolbar.textAlignLeft": "對齊左側", "SSE.Views.Toolbar.textAlignMiddle": "中央對齊", "SSE.Views.Toolbar.textAlignRight": "對齊右側", "SSE.Views.Toolbar.textAlignTop": "上方對齊", "SSE.Views.Toolbar.textAllBorders": "所有邊界", "SSE.Views.Toolbar.textAuto": "自動", "SSE.Views.Toolbar.textAutoColor": "自動", "SSE.Views.Toolbar.textBold": "粗體", "SSE.Views.Toolbar.textBordersColor": "邊框顏色", "SSE.Views.Toolbar.textBordersStyle": "邊框樣式", "SSE.Views.Toolbar.textBottom": "底部：", "SSE.Views.Toolbar.textBottomBorders": "底部邊框", "SSE.Views.Toolbar.textCenterBorders": "內部垂直邊框", "SSE.Views.Toolbar.textClearPrintArea": "清除列印區域", "SSE.Views.Toolbar.textClearRule": "清除規則", "SSE.Views.Toolbar.textClockwise": "順時針旋轉角度", "SSE.Views.Toolbar.textColorScales": "色標", "SSE.Views.Toolbar.textCounterCw": "逆時針旋轉角度", "SSE.Views.Toolbar.textDataBars": "數據欄", "SSE.Views.Toolbar.textDelLeft": "左移單元格", "SSE.Views.Toolbar.textDelUp": "上移單元格", "SSE.Views.Toolbar.textDiagDownBorder": "對角向下邊界", "SSE.Views.Toolbar.textDiagUpBorder": "對角上邊界", "SSE.Views.Toolbar.textDone": "已完成", "SSE.Views.Toolbar.textEditVA": "編輯可見區域", "SSE.Views.Toolbar.textEntireCol": "整列", "SSE.Views.Toolbar.textEntireRow": "整行", "SSE.Views.Toolbar.textFewPages": "頁", "SSE.Views.Toolbar.textHeight": "高度", "SSE.Views.Toolbar.textHideVA": "隱藏可見區域", "SSE.Views.Toolbar.textHorizontal": "橫軸上的文字", "SSE.Views.Toolbar.textInsDown": "下移單元格", "SSE.Views.Toolbar.textInsideBorders": "內部邊界", "SSE.Views.Toolbar.textInsRight": "右移單元格", "SSE.Views.Toolbar.textItalic": "斜體", "SSE.Views.Toolbar.textItems": "項目", "SSE.Views.Toolbar.textLandscape": "橫向方向", "SSE.Views.Toolbar.textLeft": "左：", "SSE.Views.Toolbar.textLeftBorders": "左邊框", "SSE.Views.Toolbar.textManageRule": "管理規則", "SSE.Views.Toolbar.textManyPages": "頁", "SSE.Views.Toolbar.textMarginsLast": "最後自訂", "SSE.Views.Toolbar.textMarginsNarrow": "狹窄", "SSE.Views.Toolbar.textMarginsNormal": "標準", "SSE.Views.Toolbar.textMarginsWide": "寬", "SSE.Views.Toolbar.textMiddleBorders": "內部水平邊框", "SSE.Views.Toolbar.textMoreFormats": "更多格式", "SSE.Views.Toolbar.textMorePages": "更多頁面", "SSE.Views.Toolbar.textNewColor": "新增自訂顏色", "SSE.Views.Toolbar.textNewRule": "新槼則", "SSE.Views.Toolbar.textNoBorders": "無邊框", "SSE.Views.Toolbar.textOnePage": "頁面", "SSE.Views.Toolbar.textOutBorders": "境外", "SSE.Views.Toolbar.textPageMarginsCustom": "自定邊界", "SSE.Views.Toolbar.textPortrait": "直向方向", "SSE.Views.Toolbar.textPrint": "列印", "SSE.Views.Toolbar.textPrintGridlines": "列印網格線", "SSE.Views.Toolbar.textPrintHeadings": "列印標題", "SSE.Views.Toolbar.textPrintOptions": "列印設定", "SSE.Views.Toolbar.textRight": "右: ", "SSE.Views.Toolbar.textRightBorders": "右邊界", "SSE.Views.Toolbar.textRotateDown": "向下旋轉文字", "SSE.Views.Toolbar.textRotateUp": "向上旋轉文字", "SSE.Views.Toolbar.textScale": "尺度", "SSE.Views.Toolbar.textScaleCustom": "自訂", "SSE.Views.Toolbar.textSelection": "凍結當前選擇", "SSE.Views.Toolbar.textSetPrintArea": "設置列印區域", "SSE.Views.Toolbar.textShowVA": "顯示可見區域", "SSE.Views.Toolbar.textStrikeout": "淘汰", "SSE.Views.Toolbar.textSubscript": "下標", "SSE.Views.Toolbar.textSubSuperscript": "下標/上標", "SSE.Views.Toolbar.textSuperscript": "上標", "SSE.Views.Toolbar.textTabCollaboration": "協作", "SSE.Views.Toolbar.textTabData": "數據", "SSE.Views.Toolbar.textTabFile": "檔案", "SSE.Views.Toolbar.textTabFormula": "配方", "SSE.Views.Toolbar.textTabHome": "首頁", "SSE.Views.Toolbar.textTabInsert": "插入", "SSE.Views.Toolbar.textTabLayout": "佈局", "SSE.Views.Toolbar.textTabProtect": "保護", "SSE.Views.Toolbar.textTabView": "檢視", "SSE.Views.Toolbar.textThisPivot": "由此數據透視表", "SSE.Views.Toolbar.textThisSheet": "由此工作表", "SSE.Views.Toolbar.textThisTable": "由此表", "SSE.Views.Toolbar.textTop": "頂部: ", "SSE.Views.Toolbar.textTopBorders": "上邊界", "SSE.Views.Toolbar.textUnderline": "底線", "SSE.Views.Toolbar.textVertical": "垂直文字", "SSE.Views.Toolbar.textWidth": "寬度", "SSE.Views.Toolbar.textZoom": "放大", "SSE.Views.Toolbar.tipAlignBottom": "底部對齊", "SSE.Views.Toolbar.tipAlignCenter": "居中對齊", "SSE.Views.Toolbar.tipAlignJust": "合理的", "SSE.Views.Toolbar.tipAlignLeft": "對齊左側", "SSE.Views.Toolbar.tipAlignMiddle": "中央對齊", "SSE.Views.Toolbar.tipAlignRight": "對齊右側", "SSE.Views.Toolbar.tipAlignTop": "上方對齊", "SSE.Views.Toolbar.tipAutofilter": "排序和過濾", "SSE.Views.Toolbar.tipBack": "返回", "SSE.Views.Toolbar.tipBorders": "邊框", "SSE.Views.Toolbar.tipCellStyle": "單元格樣式", "SSE.Views.Toolbar.tipChangeChart": "更改圖表類型", "SSE.Views.Toolbar.tipClearStyle": "清除", "SSE.Views.Toolbar.tipColorSchemas": "更改配色方案", "SSE.Views.Toolbar.tipCondFormat": "條件格式", "SSE.Views.Toolbar.tipCopy": "複製", "SSE.Views.Toolbar.tipCopyStyle": "複製樣式", "SSE.Views.Toolbar.tipCut": "剪下", "SSE.Views.Toolbar.tipDecDecimal": "減少小數", "SSE.Views.Toolbar.tipDecFont": "減少字體大小", "SSE.Views.Toolbar.tipDeleteOpt": "刪除單元格", "SSE.Views.Toolbar.tipDigStyleAccounting": "會計風格", "SSE.Views.Toolbar.tipDigStyleCurrency": "貨幣風格", "SSE.Views.Toolbar.tipDigStylePercent": "百分比樣式", "SSE.Views.Toolbar.tipEditChart": "編輯圖表", "SSE.Views.Toolbar.tipEditChartData": "選擇數據", "SSE.Views.Toolbar.tipEditChartType": "更改圖表類型", "SSE.Views.Toolbar.tipEditHeader": "編輯頁眉或頁腳", "SSE.Views.Toolbar.tipFontColor": "字體顏色", "SSE.Views.Toolbar.tipFontName": "字體", "SSE.Views.Toolbar.tipFontSize": "字體大小", "SSE.Views.Toolbar.tipHAlighOle": "水平對齊", "SSE.Views.Toolbar.tipImgAlign": "對齊物件", "SSE.Views.Toolbar.tipImgGroup": "組物件", "SSE.Views.Toolbar.tipIncDecimal": "增加小數", "SSE.Views.Toolbar.tipIncFont": "增量字體大小", "SSE.Views.Toolbar.tipInsertChart": "插入圖表", "SSE.Views.Toolbar.tipInsertChartSpark": "插入圖表", "SSE.Views.Toolbar.tipInsertEquation": "插入方程式", "SSE.Views.Toolbar.tipInsertHyperlink": "新增超連結", "SSE.Views.Toolbar.tipInsertImage": "插入圖片", "SSE.Views.Toolbar.tipInsertOpt": "插入單元格", "SSE.Views.Toolbar.tipInsertShape": "插入自動形狀", "SSE.Views.Toolbar.tipInsertSlicer": "插入切片器", "SSE.Views.Toolbar.tipInsertSpark": "插入走勢圖", "SSE.Views.Toolbar.tipInsertSymbol": "插入符號", "SSE.Views.Toolbar.tipInsertTable": "插入表格", "SSE.Views.Toolbar.tipInsertText": "插入文字框", "SSE.Views.Toolbar.tipInsertTextart": "插入文字藝術", "SSE.Views.Toolbar.tipMerge": "合併和居中", "SSE.Views.Toolbar.tipNone": "無", "SSE.Views.Toolbar.tipNumFormat": "數字格式", "SSE.Views.Toolbar.tipPageMargins": "頁面邊界", "SSE.Views.Toolbar.tipPageOrient": "頁面方向", "SSE.Views.Toolbar.tipPageSize": "頁面大小", "SSE.Views.Toolbar.tipPaste": "貼上", "SSE.Views.Toolbar.tipPrColor": "填色", "SSE.Views.Toolbar.tipPrint": "列印", "SSE.Views.Toolbar.tipPrintArea": "列印區", "SSE.Views.Toolbar.tipPrintTitles": "列印標題", "SSE.Views.Toolbar.tipRedo": "重做", "SSE.Views.Toolbar.tipSave": "儲存", "SSE.Views.Toolbar.tipSaveCoauth": "保存您的更改，以供其他用戶查看。", "SSE.Views.Toolbar.tipScale": "縮放以適合", "SSE.Views.Toolbar.tipSelectAll": "全選", "SSE.Views.Toolbar.tipSendBackward": "向後發送", "SSE.Views.Toolbar.tipSendForward": "向前帶進", "SSE.Views.Toolbar.tipSynchronize": "該文檔已被其他用戶更改。請單擊以保存您的更改並重新加載更新。", "SSE.Views.Toolbar.tipTextFormatting": "更多文字格式化工具", "SSE.Views.Toolbar.tipTextOrientation": "方向", "SSE.Views.Toolbar.tipUndo": "復原", "SSE.Views.Toolbar.tipVAlighOle": "垂直對齊", "SSE.Views.Toolbar.tipVisibleArea": "可見區域", "SSE.Views.Toolbar.tipWrap": "包覆文字", "SSE.Views.Toolbar.txtAccounting": "會計", "SSE.Views.Toolbar.txtAdditional": "額外功能", "SSE.Views.Toolbar.txtAscending": "上升", "SSE.Views.Toolbar.txtAutosumTip": "求和", "SSE.Views.Toolbar.txtClearAll": "全部", "SSE.Views.Toolbar.txtClearComments": "評論", "SSE.Views.Toolbar.txtClearFilter": "清空過濾器", "SSE.Views.Toolbar.txtClearFormat": "格式", "SSE.Views.Toolbar.txtClearFormula": "功能", "SSE.Views.Toolbar.txtClearHyper": "超連結", "SSE.Views.Toolbar.txtClearText": "文字", "SSE.Views.Toolbar.txtCurrency": "幣別", "SSE.Views.Toolbar.txtCustom": "自訂", "SSE.Views.Toolbar.txtDate": "日期", "SSE.Views.Toolbar.txtDateTime": "日期和時間", "SSE.Views.Toolbar.txtDescending": "降序", "SSE.Views.Toolbar.txtDollar": "$美元", "SSE.Views.Toolbar.txtEuro": "\n€歐元", "SSE.Views.Toolbar.txtExp": "指數的", "SSE.Views.Toolbar.txtFilter": "過濾器", "SSE.Views.Toolbar.txtFormula": "插入功能", "SSE.Views.Toolbar.txtFraction": "分數", "SSE.Views.Toolbar.txtFranc": "CHF 瑞士法郎", "SSE.Views.Toolbar.txtGeneral": "一般", "SSE.Views.Toolbar.txtInteger": "整數", "SSE.Views.Toolbar.txtManageRange": "名稱管理員", "SSE.Views.Toolbar.txtMergeAcross": "合併", "SSE.Views.Toolbar.txtMergeCells": "合併單元格", "SSE.Views.Toolbar.txtMergeCenter": "合併與中心", "SSE.Views.Toolbar.txtNamedRange": "命名範圍", "SSE.Views.Toolbar.txtNewRange": "定義名稱", "SSE.Views.Toolbar.txtNoBorders": "無邊框", "SSE.Views.Toolbar.txtNumber": "數字", "SSE.Views.Toolbar.txtPasteRange": "粘貼名稱", "SSE.Views.Toolbar.txtPercentage": "百分比", "SSE.Views.Toolbar.txtPound": "£英鎊", "SSE.Views.Toolbar.txtRouble": "₽盧布", "SSE.Views.Toolbar.txtScheme1": "辦公室", "SSE.Views.Toolbar.txtScheme10": "中位數", "SSE.Views.Toolbar.txtScheme11": " 地鐵", "SSE.Views.Toolbar.txtScheme12": "模組", "SSE.Views.Toolbar.txtScheme13": "豐富的", "SSE.Views.Toolbar.txtScheme14": "Oriel", "SSE.Views.Toolbar.txtScheme15": "起源", "SSE.Views.Toolbar.txtScheme16": "紙", "SSE.Views.Toolbar.txtScheme17": "冬至", "SSE.Views.Toolbar.txtScheme18": "技術", "SSE.Views.Toolbar.txtScheme19": "跋涉", "SSE.Views.Toolbar.txtScheme2": "灰階", "SSE.Views.Toolbar.txtScheme20": "市區", "SSE.Views.Toolbar.txtScheme21": "感染力", "SSE.Views.Toolbar.txtScheme22": "新的Office", "SSE.Views.Toolbar.txtScheme3": "頂尖", "SSE.Views.Toolbar.txtScheme4": "方面", "SSE.Views.Toolbar.txtScheme5": "思域", "SSE.Views.Toolbar.txtScheme6": "大堂", "SSE.Views.Toolbar.txtScheme7": "產權", "SSE.Views.Toolbar.txtScheme8": "流程", "SSE.Views.Toolbar.txtScheme9": "鑄造廠", "SSE.Views.Toolbar.txtScientific": "科學的", "SSE.Views.Toolbar.txtSearch": "搜尋", "SSE.Views.Toolbar.txtSort": "分類", "SSE.Views.Toolbar.txtSortAZ": "升序", "SSE.Views.Toolbar.txtSortZA": "降序排列", "SSE.Views.Toolbar.txtSpecial": "特殊", "SSE.Views.Toolbar.txtTableTemplate": "格式化為表格模板", "SSE.Views.Toolbar.txtText": "文字", "SSE.Views.Toolbar.txtTime": "時間", "SSE.Views.Toolbar.txtUnmerge": "解除單元格", "SSE.Views.Toolbar.txtYen": "¥日元", "SSE.Views.Top10FilterDialog.textType": "顯示", "SSE.Views.Top10FilterDialog.txtBottom": "底部", "SSE.Views.Top10FilterDialog.txtBy": "通過", "SSE.Views.Top10FilterDialog.txtItems": "項目", "SSE.Views.Top10FilterDialog.txtPercent": "百分比", "SSE.Views.Top10FilterDialog.txtSum": "總和", "SSE.Views.Top10FilterDialog.txtTitle": "十大自動篩選", "SSE.Views.Top10FilterDialog.txtTop": "上方", "SSE.Views.Top10FilterDialog.txtValueTitle": "十大過濾器", "SSE.Views.ValueFieldSettingsDialog.textTitle": "值字段設置", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "平均", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "基礎領域", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "基礎項目", "SSE.Views.ValueFieldSettingsDialog.txtByField": "第％1個，共％2個", "SSE.Views.ValueFieldSettingsDialog.txtCount": "計數", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "數數", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "自定義名稱", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "與...的區別", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "索引", "SSE.Views.ValueFieldSettingsDialog.txtMax": "最高", "SSE.Views.ValueFieldSettingsDialog.txtMin": "最低", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "沒有計算", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "與的差異百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "列百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "佔總數的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "行的百分比", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "產品", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "總計運行", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "顯示值為", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "來源名稱:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "標準差", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "標準差p", "SSE.Views.ValueFieldSettingsDialog.txtSum": "總和", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "匯總值字段", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "關閉", "SSE.Views.ViewManagerDlg.guestText": "來賓帳戶", "SSE.Views.ViewManagerDlg.lockText": "已鎖定", "SSE.Views.ViewManagerDlg.textDelete": "刪除", "SSE.Views.ViewManagerDlg.textDuplicate": "重複複製", "SSE.Views.ViewManagerDlg.textEmpty": "尚未創建任何視圖。", "SSE.Views.ViewManagerDlg.textGoTo": "去查看", "SSE.Views.ViewManagerDlg.textLongName": "輸入少於128個字符的名稱。", "SSE.Views.ViewManagerDlg.textNew": "新", "SSE.Views.ViewManagerDlg.textRename": "重新命名", "SSE.Views.ViewManagerDlg.textRenameError": "視圖名稱不能為空。", "SSE.Views.ViewManagerDlg.textRenameLabel": "重命名視圖", "SSE.Views.ViewManagerDlg.textViews": "工作表視圖", "SSE.Views.ViewManagerDlg.tipIsLocked": "該元素正在由另一個用戶編輯。", "SSE.Views.ViewManagerDlg.txtTitle": "圖紙視圖管理器", "SSE.Views.ViewManagerDlg.warnDeleteView": "您正在嘗試刪除當前啟用的視圖'％1'。<br>關閉此視圖並刪除它嗎？", "SSE.Views.ViewTab.capBtnFreeze": "凍結窗格", "SSE.Views.ViewTab.capBtnSheetView": "圖紙視圖", "SSE.Views.ViewTab.textAlwaysShowToolbar": "永遠顯示工具欄", "SSE.Views.ViewTab.textClose": "關閉", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "隱藏狀態欄", "SSE.Views.ViewTab.textCreate": "新", "SSE.Views.ViewTab.textDefault": "預設", "SSE.Views.ViewTab.textFormula": "公式欄", "SSE.Views.ViewTab.textFreezeCol": "凍結第一列", "SSE.Views.ViewTab.textFreezeRow": "凍結第一排", "SSE.Views.ViewTab.textGridlines": "網格線", "SSE.Views.ViewTab.textHeadings": "標題", "SSE.Views.ViewTab.textInterfaceTheme": "介面主題", "SSE.Views.ViewTab.textManager": "查看管理員", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "顯示固定面版視窗的陰影", "SSE.Views.ViewTab.textUnFreeze": "取消凍結窗格", "SSE.Views.ViewTab.textZeros": "顯示零", "SSE.Views.ViewTab.textZoom": "放大", "SSE.Views.ViewTab.tipClose": "關閉工作表視圖", "SSE.Views.ViewTab.tipCreate": "創建圖紙視圖", "SSE.Views.ViewTab.tipFreeze": "凍結窗格", "SSE.Views.ViewTab.tipInterfaceTheme": "介面主題", "SSE.Views.ViewTab.tipSheetView": "圖紙視圖", "SSE.Views.WBProtection.hintAllowRanges": "允許範圍編輯", "SSE.Views.WBProtection.hintProtectSheet": "保護工作表", "SSE.Views.WBProtection.hintProtectWB": "保護工作簿", "SSE.Views.WBProtection.txtAllowRanges": "允許範圍編輯", "SSE.Views.WBProtection.txtHiddenFormula": "隱藏公式", "SSE.Views.WBProtection.txtLockedCell": "儲存格鎖定", "SSE.Views.WBProtection.txtLockedShape": "形狀鎖定", "SSE.Views.WBProtection.txtLockedText": "鎖定文字", "SSE.Views.WBProtection.txtProtectSheet": "保護工作表", "SSE.Views.WBProtection.txtProtectWB": "保護工作簿", "SSE.Views.WBProtection.txtSheetUnlockDescription": "如解除表格保護，請輸入密碼", "SSE.Views.WBProtection.txtSheetUnlockTitle": "工作表解鎖", "SSE.Views.WBProtection.txtWBUnlockDescription": "如解除工作表保護，請輸入密碼", "SSE.Views.WBProtection.txtWBUnlockTitle": "工作簿解鎖"}