{"DATE": {"a": "(year; month; day)", "d": "返回在日期时间代码中代表日期的数字"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "计算两个日期之间相隔的天数、月数或年数。"}, "DATEVALUE": {"a": "(date_text)", "d": "将日期值从字符串转化为序列数，表示日期-时间代码的日期"}, "DAY": {"a": "(serial_number)", "d": "返回一个月中的第几天的数值，介于 1 到 31 之间。"}, "DAYS": {"a": "(end_date; start_date)", "d": "返回两个日期之间的天数。"}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "按每年 360 天返回两个日期间相差的天数(每月 30 天)"}, "EDATE": {"a": "(start_date; months)", "d": "返回一串日期，指示起始日期之前/之后的月数"}, "EOMONTH": {"a": "(start_date; months)", "d": "返回一串日期，表示指定月数之前或之后的月份的最后一天"}, "HOUR": {"a": "(serial_number)", "d": "返回小时数值，是一个 0 (12:00 A.M.) 到 23 (11:00 P.M.) 之间的整数。"}, "ISOWEEKNUM": {"a": "(date)", "d": "返回给定日期所在年份的 ISO 周数目"}, "MINUTE": {"a": "(serial_number)", "d": "返回分钟数值，是一个 0 到 59 之间的整数。"}, "MONTH": {"a": "(serial_number)", "d": "返回月份值，是一个 1 (一月)到 12 (十二月)之间的数字。"}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "返回两个日期之间的完整工作日数"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "使用自定义周末参数返回两个日期之间的完整工作日数"}, "NOW": {"a": "()", "d": "返回日期时间格式的当前日期和时间。"}, "SECOND": {"a": "(serial_number)", "d": "返回秒数值，是一个 0 到 59 之间的整数。"}, "TIME": {"a": "(hour; minute; second)", "d": "返回特定时间的序列数"}, "TIMEVALUE": {"a": "(time_text)", "d": "将文本形式表示的时间转换成序列数，是一个从 0 (12:00:00 AM) 到 0.999988426 (11:59:59 PM) 的数。在输入公式后将数字设置为时间格式"}, "TODAY": {"a": "()", "d": "返回日期格式的的当前日期。"}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "返回代表一周中的第几天的数值，是一个 1 到 7 之间的整数。"}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "返回一年中的周数"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "返回在指定的若干个工作日之前/之后的日期(一串数字)"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "使用自定义周末参数返回在指定的若干个工作日之前/之后的日期(一串数字)"}, "YEAR": {"a": "(serial_number)", "d": "返回日期的年份值，一个 1900-9999 之间的数字。"}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "返回一个年分数，表示 start_date 和 end_date 之间的整天天数"}, "BESSELI": {"a": "(x; n)", "d": "返回修正的贝赛耳函数 In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "返回贝赛耳函数 Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "返回修正的贝赛耳函数 Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "返回贝赛耳函数 Yn(x)"}, "BIN2DEC": {"a": "(number)", "d": "将二进制数转换为十进制"}, "BIN2HEX": {"a": "(number; [places])", "d": "将二进制数转换为十六进制"}, "BIN2OCT": {"a": "(number; [places])", "d": "将二进制数转换为八进制"}, "BITAND": {"a": "(number1; number2)", "d": "返回两个数字的按位\"与\"值"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "返回按 shift_amount 位左移的值数字"}, "BITOR": {"a": "(number1; number2)", "d": "返回两个数字的按位“或”值"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "返回按 shift_amount 位右移的值数字"}, "BITXOR": {"a": "(number1; number2)", "d": "返回两个数字的按位“异或”值"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "将实部系数和虚部系数转换为复数"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "将数字从一种度量体系转换为另一种度量体系"}, "DEC2BIN": {"a": "(number; [places])", "d": "将十进制数转换为二进制"}, "DEC2HEX": {"a": "(number; [places])", "d": "将十进制数转换为十六进制"}, "DEC2OCT": {"a": "(number; [places])", "d": "将十进制数转换为八进制"}, "DELTA": {"a": "(number1; [number2])", "d": "测试两个数字是否相等"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "返回误差函数"}, "ERF.PRECISE": {"a": "(X)", "d": "返回误差函数"}, "ERFC": {"a": "(x)", "d": "返回补余误差函数"}, "ERFC.PRECISE": {"a": "(X)", "d": "返回补余误差函数"}, "GESTEP": {"a": "(number; [step])", "d": "测试某个数字是否大于阈值"}, "HEX2BIN": {"a": "(number; [places])", "d": "将十六进制数转换为二进制"}, "HEX2DEC": {"a": "(number)", "d": "将十六进制数转换为十进制"}, "HEX2OCT": {"a": "(number; [places])", "d": "将十六进制数转换为八进制"}, "IMABS": {"a": "(inumber)", "d": "返回复数的绝对值(模数)"}, "IMAGINARY": {"a": "(inumber)", "d": "返回复数的虚部系数"}, "IMARGUMENT": {"a": "(inumber)", "d": "返回辐角 q (以弧度表示的角度)"}, "IMCONJUGATE": {"a": "(inumber)", "d": "返回复数的共轭复数"}, "IMCOS": {"a": "(inumber)", "d": "返回复数的余弦值"}, "IMCOSH": {"a": "(inumber)", "d": "返回复数的双曲余弦值"}, "IMCOT": {"a": "(inumber)", "d": "返回复数的余切值"}, "IMCSC": {"a": "(inumber)", "d": "返回复数的余割值"}, "IMCSCH": {"a": "(inumber)", "d": "返回复数的双曲余割值"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "返回两个复数之商"}, "IMEXP": {"a": "(inumber)", "d": "返回复数的指数值"}, "IMLN": {"a": "(inumber)", "d": "返回复数的自然对数"}, "IMLOG10": {"a": "(inumber)", "d": "返回以 10 为底的复数的对数"}, "IMLOG2": {"a": "(inumber)", "d": "返回以 2 为底的复数的对数"}, "IMPOWER": {"a": "(inumber; number)", "d": "返回复数的整数幂"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "返回 1 到 255 个复数的乘积"}, "IMREAL": {"a": "(inumber)", "d": "返回复数的实部系数"}, "IMSEC": {"a": "(inumber)", "d": "返回复数的正割值"}, "IMSECH": {"a": "(inumber)", "d": "返回复数的双曲正割值"}, "IMSIN": {"a": "(inumber)", "d": "返回复数的正弦值"}, "IMSINH": {"a": "(inumber)", "d": " 返回复数的双曲正弦值"}, "IMSQRT": {"a": "(inumber)", "d": "返回复数的平方根"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "返回两个复数的差值"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "返回复数的和"}, "IMTAN": {"a": "(inumber)", "d": "返回复数的正切值"}, "OCT2BIN": {"a": "(number; [places])", "d": "将八进制数转换为二进制"}, "OCT2DEC": {"a": "(number)", "d": "将八进制数转换为十进制"}, "OCT2HEX": {"a": "(number; [places])", "d": "将八进制数转换为十六进制"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "计算满足给定条件的列表或数据库的列中数值的平均值。请查看“帮助”"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "从满足给定条件的数据库记录的字段(列)中，计算数值单元格数目"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "对满足指定条件的数据库中记录字段(列)的非空单元格进行记数"}, "DGET": {"a": "(database; field; criteria)", "d": "从数据库中提取符合指定条件且唯一存在的记录"}, "DMAX": {"a": "(database; field; criteria)", "d": "返回满足给定条件的数据库中记录的字段(列)中数据的最大值"}, "DMIN": {"a": "(database; field; criteria)", "d": "返回满足给定条件的数据库中记录的字段(列)中数据的最小值"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "与满足指定条件的数据库中记录字段(列)的值相乘"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "根据所选数据库条目中的样本估算数据的标准偏差"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "以数据库选定项作为样本总体，计算数据的标准偏差"}, "DSUM": {"a": "(database; field; criteria)", "d": "求满足给定条件的数据库中记录的字段(列)数据的和"}, "DVAR": {"a": "(database; field; criteria)", "d": "根据所选数据库条目中的样本估算数据的方差"}, "DVARP": {"a": "(database; field; criteria)", "d": "以数据库选定项作为样本总体，计算数据的总体方差"}, "CHAR": {"a": "(number)", "d": "根据本机中的字符集，返回由代码数字指定的字符"}, "CLEAN": {"a": "(text)", "d": "删除文本中的所有非打印字符"}, "CODE": {"a": "(text)", "d": "返回文本字符串第一个字符在本机所用字符集中的数字代码"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "将多个文本字符串合并成一个"}, "CONCAT": {"a": "(text1; ...)", "d": "连接列表或文本字符串区域"}, "DOLLAR": {"a": "(number; [decimals])", "d": "用货币格式将数值转换成文本字符"}, "EXACT": {"a": "(text1; text2)", "d": "比较两个字符串是否完全相同(区分大小写)。返回 TRUE 或 FALSE"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "返回一个字符串在另一个字符串中出现的起始位置(区分大小写)"}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "用于在第二个文本串中定位第一个文本串，并返回第一个文本串的起始位置的值，该值从第二个文本串的第一个字符算起。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "用定点小数格式将数值舍入成特定位数并返回带或不带逗号的文本"}, "LEFT": {"a": "(text; [num_chars])", "d": "从一个文本字符串的第一个字符开始返回指定个数的字符"}, "LEFTB": {"a": "(text; [num_chars])", "d": "基于所指定的字节数返回文本字符串中的第一个或前几个字符。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。"}, "LEN": {"a": "(text)", "d": "返回文本字符串中的字符个数"}, "LENB": {"a": "(text)", "d": "返回文本字符串中用于代表字符的字节数。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。"}, "LOWER": {"a": "(text)", "d": "将一个文本字符串的所有字母转换为小写形式"}, "MID": {"a": "(text; start_num; num_chars)", "d": "从文本字符串中指定的起始位置起返回指定长度的字符"}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "根据您指定的字节数，返回文本字符串中从指定位置开始的特定数目的字符。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "按独立于区域设置的方式将文本转换为数字"}, "PROPER": {"a": "(text)", "d": "将一个文本字符串中各英文单词的第一个字母转换成大写，将其他字符转换成小写"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "将一个字符串中的部分字符用另一个字符串替换"}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "使用其他文本字符串并根据所指定的字节数替换某文本字符串中的部分文本。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。"}, "REPT": {"a": "(text; number_times)", "d": "根据指定次数重复文本。可用 RPET 在一个单元格中重复填写一个文本字符串"}, "RIGHT": {"a": "(text; [num_chars])", "d": "从一个文本字符串的最后一个字符开始返回指定个数的字符"}, "RIGHTB": {"a": "(text; [num_chars])", "d": "根据所指定的字节数返回文本字符串中最后一个或多个字符。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "返回一个指定字符或文本字符串在字符串中第一次出现的位置，从左到右查找(忽略大小写)"}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "函数可在第二个文本字符串中查找第一个文本字符串，并返回第一个文本字符串的起始位置的编号，该编号从第二个文本字符串的第一个字符算起。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "将字符串中的部分字符串以新字符串替换"}, "T": {"a": "(value)", "d": "检测给定值是否为文本，如果是文本按原样返回，如果不是文本则返回双引号(空文本)"}, "TEXT": {"a": "(value; format_text)", "d": "根据指定的数字格式将数值转成文本"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "使用分隔符连接列表或文本字符串区域"}, "TRIM": {"a": "(text)", "d": "删除字符串中多余的空格，但会在英文字符串中保留一个作为词与词之间分隔的空格"}, "UNICHAR": {"a": "(number)", "d": "返回由给定数值引用的 Unicode 字符"}, "UNICODE": {"a": "(text)", "d": "返回对应于文本的第一个字符的数字(代码点)"}, "UPPER": {"a": "(text)", "d": "将文本字符串转换成字母全部大写形式"}, "VALUE": {"a": "(text)", "d": "将一个代表数值的文本字符串转换成数值"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "返回一组数据点到其算术平均值的绝对偏差的平均值。参数可以是数字、名称、数组或包含数字的引用"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "返回其参数的算术平均值；参数可以是数值或包含数值的名称、数组或引用"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "返回所有参数的算术平均值。字符串和 FALSE 相当于 0；TRUE 相当于 1。参数可以是数值、名称、数组或引用"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "查找给定条件指定的单元格的平均值(算术平均值)"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "查找一组给定条件指定的单元格的平均值(算术平均值)"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "返回累积 beta 分布的概率密度函数"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "返回累积 beta 分布的概率密度函数区间点 (BETADIST)"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "返回 beta 概率分布函数"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "返回具有给定概率的累积 beta 分布的区间点"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "返回一元二项式分布的概率"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "返回一元二项式分布的概率"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "使用二项式分布返回试验结果的概率"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "返回一个数值，它是使得累积二项式分布的函数值大于或等于临界值 α 的最小整数"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "返回 χ2 分布的右尾概率"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "返回具有给定概率的右尾 χ2 分布的区间点"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "返回独立性检验的结果: 针对统计和相应的自由度返回卡方分布值"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "返回 χ2 分布的左尾概率"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "返回 χ2 分布的右尾概率"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "返回具有给定概率的左尾 χ2 分布的区间点"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "返回具有给定概率的右尾 χ2 分布的区间点"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "返回独立性检验的结果: 针对统计和相应的自由度返回卡方分布值"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "使用正态分布，返回总体平均值的置信区间"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "使用正态分布，返回总体平均值的置信区间"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "使用学生 T 分布，返回总体平均值的置信区间"}, "CORREL": {"a": "(array1; array2)", "d": "返回两组数值的相关系数"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "计算区域中包含数字的单元格的个数"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "计算区域中非空单元格的个数"}, "COUNTBLANK": {"a": "(range)", "d": "计算某个区域中空单元格的数目"}, "COUNTIF": {"a": "(range; criteria)", "d": "计算某个区域中满足给定条件的单元格数目"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "统计一组给定条件所指定的单元格数"}, "COVAR": {"a": "(array1; array2)", "d": "返回协方差，即每对变量的偏差乘积的均值"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "返回总体协方差，即两组数值中每对变量的偏差乘积的平均值"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "返回样本协方差，即两组数值中每对变量的偏差乘积的平均值"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "返回一个数值，它是使得累积二项式分布的函数值大于等于临界值 α 的最小整数"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "返回各数据点与数据均值点之差(数据偏差)的平方和"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "返回指数分布"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "返回指数分布"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "返回两组数据的(右尾) F 概率分布(散布程度)"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "返回(右尾) F 概率分布的逆函数值，如果 p = FDIST(x,...)，那么 FINV(p,...) = x"}, "FTEST": {"a": "(array1; array2)", "d": "返回 F 检验的结果，F 检验返回的是当 Array1 和 Array2 的方差无明显差异时的双尾概率"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "返回两组数据的(左尾) F 概率分布"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "返回两组数据的(右尾) F 概率分布"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "返回(左尾) F 概率分布的逆函数值，如果 p = F.DIST(x,...)，那么 FINV(p,...) = x"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "返回(右尾) F 概率分布的逆函数值，如果 p = F.DIST.RT(x,...)，那么 F.INV.RT(p,...) = x"}, "F.TEST": {"a": "(array1; array2)", "d": "返回 F 检验的结果，F 检验返回的是当 Array1 和 Array2 的方差无明显差异时的双尾概率"}, "FISHER": {"a": "(x)", "d": "返回 Fisher 变换值"}, "FISHERINV": {"a": "(y)", "d": "返回 Fisher 逆变换值，如果 y = FISHER(x)，那么 FISHERINV(y) = x"}, "FORECAST": {"a": "(x; known_y's; known_x's)", "d": "根据现有的值所产生出的等差序列来计算或预测未来值"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "使用指数平滑方法返回特定未来目标日期的预测值。"}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "返回指定目标日期预测值的置信区间。"}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "返回应用针对指定时间序列检测到的重复模式的长度。"}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "对预测返回请求的统计信息。"}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "根据现有的值所产生出的等差序列来计算或预测未来值"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "以一列垂直数组返回一组数据的频率分布"}, "GAMMA": {"a": "(x)", "d": "返回伽玛函数值"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "返回 γ 分布函数"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "返回 γ 分布函数"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "返回具有给定概率的 γ 累积分布的区间点: 如果 p = GAMMADIST(x,...) 则 GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "返回具有给定概率的 γ 累积分布的区间点"}, "GAMMALN": {"a": "(x)", "d": "返回 γ 函数的自然对数"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "返回 γ 函数的自然对数"}, "GAUSS": {"a": "(x)", "d": "返回比标准正态累积分布小 0.5 的值"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "返回一正数数组或数值区域的几何平均数"}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "返回指数回归拟合曲线的一组纵坐标值(y 值)"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "返回一组正数的调和平均数: 所有参数倒数平均值的倒数"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "返回超几何分布"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "返回超几何分布"}, "INTERCEPT": {"a": "(known_y's; known_x's)", "d": "根据已知的 x 值及 y 值所绘制出来的最佳回归线，计算出直线将于 y 轴交汇的点"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "返回一组数据的峰值"}, "LARGE": {"a": "(array; k)", "d": "返回数据组中第 k 个最大值。例如，第五个最大值"}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "返回线性回归方程的参数"}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "返回指数回归拟合曲线方程的参数"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "返回 x 的对数正态累积分布函数的区间点，其中 ln(x) 是平均数和标准方差参数的正态分布"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "返回对数正态分布"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "返回具有给定概率的对数正态分布函数的区间点"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "返回 x 的累积正态分布，其中 ln(x) 是平均数和标准方差参数的正态分布"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "返回一组数值中的最大值，忽略逻辑值及文本"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "返回一组参数中的最大值(不忽略逻辑值和字符串)"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "返回一组给定条件所指定的单元格的最大值"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "返回一组数的中值"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "返回一组数值中的最小值，忽略逻辑值及文本"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "返回一组参数中的最小值(不忽略逻辑值和字符串)"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "返回一组给定条件所指定的单元格的最小值"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "返回一组数据或数据区域中的众数(出现频率最高的数)"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "返回一组数据或数据区域中出现频率最高或重复出现的数值的垂直数组。对于水平数组，可使用 =TRANSPOSE(MODE.MULT(number1,number2,...))"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "返回一组数据或数据区域中出现频率最高或重复出现数值"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "返回负二项式分布函数值"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "返回负二项式分布函数，第 Number_s 次成功之前将有 Number_f 次失败的概率，具有 Probability_s 成功概率"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "返回正态分布函数值"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "返回指定平均值和标准方差的正态累积分布函数值"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "返回具有给定概率正态分布的区间点"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "返回指定平均值和标准方差的正态累积分布函数的区间点"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "返回标准正态分布函数值"}, "NORMSDIST": {"a": "(z)", "d": "返回标准正态累积分布函数值(具有零平均值和一标准方差)"}, "NORM.S.INV": {"a": "(probability)", "d": "返回标准正态分布的区间点"}, "NORMSINV": {"a": "(probability)", "d": "返回标准正态累积分布的区间点(具有零平均值和一标准方差)"}, "PEARSON": {"a": "(array1; array2)", "d": "求皮尔生(Pearson)积矩法的相关系数 r"}, "PERCENTILE": {"a": "(array; k)", "d": "返回数组的 K 百分点值"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "返回数组的 K 百分点值，K 介于 0 与 1 之间，不含 0 与 1"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "返回数组的 K 百分点值，K 介于 0 与 1 之间，含 0 与 1"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "返回特定数值在一组数中的百分比排名"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "返回特定数值在一组数中的百分比排名(介于 0 与 1 之间，不含 0 与 1)"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "返回特定数值在一组数中的百分比排名(介于 0 与 1 之间，含 0 与 1)"}, "PERMUT": {"a": "(number; number_chosen)", "d": "返回从给定元素数目的集合中选取若干元素的排列数"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "返回可以从对象总数中选取的给定数目对象(包含重复项)的排列数"}, "PHI": {"a": "(x)", "d": "返回标准正态分布的密度函数值"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "返回泊松(POISSON)分布"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "返回泊松(POISSON)分布"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "返回一概率事件组中符合指定条件的事件集所对应的概率之和"}, "QUARTILE": {"a": "(array; quart)", "d": "返回一组数据的四分位点"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "基于从 0 到 1 之间(含 0 与 1)的百分点值，返回一组数据的四分位点"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "基于从 0 到 1 之间(不含 0 与 1)的百分点值，返回一组数据的四分位点"}, "RANK": {"a": "(number; ref; [order])", "d": "返回某数字在一列数字中相对于其他数值的大小排名"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "返回某数字在一列数字中相对于其他数值的大小排名；如果多个数值排名相同，则返回平均值排名"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "返回某数字在一列数字中相对于其他数值的大小排名；如果多个数值排名相同，则返回该组数值的最佳排名"}, "RSQ": {"a": "(known_y's; known_x's)", "d": "返回给定数据点的 Pearson 积矩法相关系数的平方"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "返回一个分布的不对称度: 用来体现某一分布相对其平均值的不对称程度"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "基于总体返回分布的不对称度: 用来体现某一分布相对其平均值的不对称程度"}, "SLOPE": {"a": "(known_y's; known_x's)", "d": "返回经过给定数据点的线性回归拟合线方程的斜率"}, "SMALL": {"a": "(array; k)", "d": "返回数据组中第 k 个最小值"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "通过平均值和标准方差返回正态分布概率值"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "估算基于给定样本的标准偏差(忽略样本中的逻辑值及文本)"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "计算基于给定的样本总体的标准偏差(忽略逻辑值及文本)"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "估算基于给定样本的标准偏差(忽略样本中的逻辑值及文本)"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "估算基于给定样本(包括逻辑值和字符串)的标准偏差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "计算基于给定的样本总体的标准偏差(忽略逻辑值及文本)"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "计算样本(包括逻辑值和字符串)总体的标准偏差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1"}, "STEYX": {"a": "(known_y's; known_x's)", "d": "返回通过线性回归法计算纵坐标预测值所产生的标准误差"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "返回学生 t-分布"}, "TINV": {"a": "(probability; deg_freedom)", "d": "返回学生 t-分布的双尾区间点"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "返回左尾学生 t-分布"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "返回双尾学生 t-分布"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "返回右尾学生 t-分布"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "返回学生 t-分布的左尾区间点"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "返回学生 t-分布的双尾区间点"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "返回学生 t-检验的概率值"}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "返回线性回归拟合线的一组纵坐标值(y 值)"}, "TRIMMEAN": {"a": "(array; percent)", "d": "返回一组数据的修剪平均值"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "返回学生 t-检验的概率值"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "估算基于给定样本的方差(忽略样本中的逻辑值及文本)"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "计算基于给定的样本总体的方差(忽略样本中的逻辑值及文本)"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "估算基于给定样本的方差(忽略样本中的逻辑值及文本)"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "估算基于给定样本(包括逻辑值和字符串)的方差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "计算基于给定的样本总体的方差(忽略样本总体中的逻辑值及文本)"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "计算样本(包括逻辑值和字符串)总体的方差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "返回 Weibull 分布(概率密度)"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "返回 Weibull 分布(概率密度)"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": " 返回 z 测试的单尾 P 值。"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "返回 z 测试的单尾 P 值"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "返回定期支付利息的债券的应计利息"}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "返回在到期日支付利息的债券的应计利息"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "返回每个记帐期内资产分配的线性折旧。"}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "返回每个记帐期内资产分配的线性折旧。"}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "返回从票息期开始到结算日之间的天数"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "返回包含结算日的票息期的天数"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "返回从结算日到下一票息支付日之间的天数"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "返回结算日后的下一票息支付日"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "返回结算日与到期日之间可支付的票息数"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "返回结算日前的上一票息支付日"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "返回两个付款期之间为贷款累积支付的利息"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "返回两个付款期之间为贷款累积支付的本金"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "用固定余额递减法，返回指定期间内某项固定资产的折旧值"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "用双倍余额递减法或其他指定方法，返回指定期间内某项固定资产的折旧值"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "返回债券的贴现率"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "将以分数表示的货币值转换为以小数表示的货币值"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "将以小数表示的货币值转换为以分数表示的货币值"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "返回定期支付利息的债券的年持续时间"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "返回年有效利率"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "基于固定利率和等额分期付款方式，返回某项投资的未来值"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "返回在应用一系列复利后，初始本金的终值"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "返回完全投资型债券的利率"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "返回在定期偿还、固定利率条件下给定期次内某项投资回报(或贷款偿还)的利息部分"}, "IRR": {"a": "(values; [guess])", "d": "返回一系列现金流的内部报酬率"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "返回普通(无担保)贷款的利息偿还"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "为假定票面值为 100 元的债券返回麦考利修正持续时间"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "返回在考虑投资成本以及现金再投资利率下一系列分期现金流的内部报酬率"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "返回年度的单利"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "基于固定利率和等额分期付款方式，返回某项投资或贷款的期数"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "基于一系列将来的收(正值)支(负值)现金流和一贴现率，返回一项投资的净现值"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "返回每张票面为 100 元且第一期为奇数的债券的现价"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "返回第一期为奇数的债券的收益"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "返回每张票面为 100 元且最后一期为奇数的债券的现价"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "返回最后一期为奇数的债券的收益"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "返回投资达到指定的值所需的期数"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "计算在固定利率下，贷款的等额分期偿还额"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "返回在定期偿还、固定利率条件下给定期次内某项投资回报(或贷款偿还)的本金部分"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "返回每张票面为 100 元且定期支付利息的债券的现价"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "返回每张票面为 100 元的已贴现债券的现价"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "返回每张票面为 100 元且在到期日支付利息的债券的现价"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "返回某项投资的一系列将来偿还额的当前总值(或一次性偿还额的现值)"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "返回投资或贷款的每期实际利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "返回完全投资型债券在到期日收回的金额"}, "RRI": {"a": "(nper; pv; fv)", "d": "返回某项投资增长的等效利率"}, "SLN": {"a": "(cost; salvage; life)", "d": "返回固定资产的每期线性折旧费"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "返回某项固定资产按年限总和折旧法计算的每期折旧金额"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "返回短期国库券的等价债券收益"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "返回每张票面为 100 元的短期国库券的现价"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "返回短期国库券的收益"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "返回某项固定资产用余额递减法或其他指定方法计算的特定或部分时期的折旧额"}, "XIRR": {"a": "(values; dates; [guess])", "d": "返回现金流计划的内部回报率"}, "XNPV": {"a": "(rate; values; dates)", "d": "返回现金流计划的净现值"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "返回定期支付利息的债券的收益"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "返回已贴现债券的年收益，如短期国库券"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "返回在到期日支付利息的债券的年收益"}, "ABS": {"a": "(number)", "d": "返回给定数值的绝对值，即不带符号的数值"}, "ACOS": {"a": "(number)", "d": "返回一个弧度的反余弦。弧度值在 0 到 Pi 之间。反余弦值是指余弦值为 Number 的角度"}, "ACOSH": {"a": "(number)", "d": "返回反双曲余弦值"}, "ACOT": {"a": "(number)", "d": "返回一个数字的反余切值。弧度值在 0 到 Pi 之间"}, "ACOTH": {"a": "(number)", "d": "返回一个数字的反双曲余切值"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "返回一个数据列表或数据库的合计"}, "ARABIC": {"a": "(text)", "d": "将罗马数字转换为阿拉伯数字"}, "ASC": {"a": "(text)", "d": "对于双字节字符集 (DBCS) 语言，该函数将全角（双字节）字符转换成半角（单字节）字符。"}, "ASIN": {"a": "(number)", "d": "返回一个弧度的反正弦。弧度值在 -Pi/2 到 Pi/2 之间"}, "ASINH": {"a": "(number)", "d": "返回反双曲正弦值"}, "ATAN": {"a": "(number)", "d": "返回反正切值。以弧度表示，大小在 -Pi/2 到 Pi/2 之间"}, "ATAN2": {"a": "(x_num; y_num)", "d": "根据给定的 X 轴及 Y 轴坐标值，返回反正切值。返回值在 -Pi 到 Pi 之间(不包括 -Pi)"}, "ATANH": {"a": "(number)", "d": "返回反双曲正切值"}, "BASE": {"a": "(number; radix; [min_length])", "d": "将数字转换成具有给定基数的文本表示形式"}, "CEILING": {"a": "(number; significance)", "d": "将参数向上舍入为最接近的指定基数的倍数"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "将数字向上舍入到最接近的整数或最接近的指定基数的倍数"}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "返回一个数字，该数字向上舍入为最接近的整数或最接近的有效位的倍数。"}, "COMBIN": {"a": "(number; number_chosen)", "d": "返回从给定元素数目的集合中提取若干元素的组合数"}, "COMBINA": {"a": "(number; number_chosen)", "d": "返回给定数目的项目的组合数(包含重复项)"}, "COS": {"a": "(number)", "d": "返回给定角度的余弦值"}, "COSH": {"a": "(number)", "d": "返回双曲余弦值"}, "COT": {"a": "(number)", "d": "返回一个角度的余切值"}, "COTH": {"a": "(number)", "d": "返回一个数字的双曲余切值"}, "CSC": {"a": "(number)", "d": "返回一个角度的余割值"}, "CSCH": {"a": "(number)", "d": "返回一个角度的双曲余割值"}, "DECIMAL": {"a": "(number; radix)", "d": "按给定基数将数字的文本表示形式转换成十进制数"}, "DEGREES": {"a": "(angle)", "d": "将弧度转换成角度"}, "ECMA.CEILING": {"a": "(number; significance)", "d": "将参数向上舍入为最接近的指定基数的倍数"}, "EVEN": {"a": "(number)", "d": "将正数向上舍入到最近的偶数，负数向下舍入到最近的偶数"}, "EXP": {"a": "(number)", "d": "返回 e 的 n 次方"}, "FACT": {"a": "(number)", "d": "返回某数的阶乘，等于 1*2*...*Number"}, "FACTDOUBLE": {"a": "(number)", "d": "返回数字的双阶乘"}, "FLOOR": {"a": "(number; significance)", "d": "将参数向下舍入为最接近的指定基数的倍数"}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "返回一个数字，该数字向下舍入为最接近的整数或最接近的 significance 的倍数。"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "将数字向下舍入到最接近的整数或最接近的指定基数的倍数"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "返回最大公约数"}, "INT": {"a": "(number)", "d": "将数值向下取整为最接近的整数"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "返回一个数字，该数字向上舍入为最接近的整数或最接近的有效位的倍数。 无论该数字的符号如何，该数字都向上舍入。 但是，如果该数字或有效位为 0，则返回 0。"}, "LCM": {"a": "(number1; [number2]; ...)", "d": "返回最小公倍数"}, "LN": {"a": "(number)", "d": "返回给定数值的自然对数"}, "LOG": {"a": "(number; [base])", "d": "根据给定底数返回数字的对数"}, "LOG10": {"a": "(number)", "d": "返回给定数值以 10 为底的对数"}, "MDETERM": {"a": "(array)", "d": "返回一数组所代表的矩阵行列式的值"}, "MINVERSE": {"a": "(array)", "d": "返回一数组所代表的矩阵的逆矩阵"}, "MMULT": {"a": "(array1; array2)", "d": "返回两数组的矩阵积，结果矩阵的行数与 array1 相等，列数与 array2 相等"}, "MOD": {"a": "(number; divisor)", "d": "返回两数相除的余数"}, "MROUND": {"a": "(number; multiple)", "d": "返回一个舍入到所需倍数的数字"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "返回一组数字的多项式"}, "MUNIT": {"a": "(dimension)", "d": "返回指定维度的单位矩阵"}, "ODD": {"a": "(number)", "d": "将正(负)数向上(下)舍入到最接近的奇数"}, "PI": {"a": "()", "d": "返回圆周率 Pi 的值，3.14159265358979，精确到 15 位"}, "POWER": {"a": "(number; power)", "d": "返回某数的乘幂"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "计算所有参数的乘积"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "返回除法的整数部分"}, "RADIANS": {"a": "(angle)", "d": "将角度转为弧度"}, "RAND": {"a": "()", "d": "返回大于或等于 0 且小于 1 的平均分布随机数(依重新计算而变)"}, "RANDARRAY": {"a": "([行]; [列]; [最小值]; [最大值]; [整数])", "d": "返回随机数数组"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "返回一个介于指定的数字之间的随机数"}, "ROMAN": {"a": "(number; [form])", "d": "将阿拉伯数字转换成文本式罗马数字"}, "ROUND": {"a": "(number; num_digits)", "d": "按指定的位数对数值进行四舍五入"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "向下舍入数字"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "向上舍入数字"}, "SEC": {"a": "(number)", "d": "返回角度的正割值"}, "SECH": {"a": "(number)", "d": "返回角度的双曲正割值"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "返回基于公式的幂级数的和"}, "SIGN": {"a": "(number)", "d": "返回数字的正负号: 为正时，返回 1；为零时，返回 0；为负时，返回 -1"}, "SIN": {"a": "(number)", "d": "返回给定角度的正弦值"}, "SINH": {"a": "(number)", "d": "返回双曲正弦值"}, "SQRT": {"a": "(number)", "d": "返回数值的平方根"}, "SQRTPI": {"a": "(number)", "d": "返回(数字 * Pi)的平方根"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "返回一个数据列表或数据库的分类汇总"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "计算单元格区域中所有数值的和"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "对满足条件的单元格求和"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "对一组给定条件指定的单元格求和"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "返回相应的数组或区域乘积的和"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "返回所有参数的平方和。参数可以是数值、数组、名称，或者是对数值单元格的引用"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "计算两数组中对应数值平方差的和"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "计算两数组中对应数值平方和的和"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "求两数组中对应数值差的平方和"}, "TAN": {"a": "(number)", "d": "返回给定角度的正切值"}, "TANH": {"a": "(number)", "d": "返回双曲正切值"}, "TRUNC": {"a": "(number; [num_digits])", "d": "将数字截为整数或保留指定位数的小数"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "创建一个以文本方式对工作簿中某一单元格的引用"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "根据给定的索引值，从参数串中选出相应值或操作"}, "COLUMN": {"a": "([reference])", "d": "返回一引用的列号"}, "COLUMNS": {"a": "(array)", "d": "返回某一引用或数组的列数"}, "FORMULATEXT": {"a": "(reference)", "d": "作为字符串返回公式"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "搜索数组区域首行满足条件的元素，确定待检索单元格在区域中的列序号，再进一步返回选定单元格的值"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "创建一个快捷方式或链接，以便打开一个存储在硬盘、网络服务器或  Internet 上的文档"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "在给定的单元格区域中，返回特定行列交叉处单元格的值或引用"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "返回文本字符串所指定的引用"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "从单行或单列或从数组中查找一个值。条件是向后兼容性"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "返回符合特定值特定顺序的项在数组中的相对位置"}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "以指定的引用为参照系，通过给定偏移量返回新的引用"}, "ROW": {"a": "([reference])", "d": "返回一个引用的行号"}, "ROWS": {"a": "(array)", "d": "返回某一引用或数组的行数"}, "TRANSPOSE": {"a": "(array)", "d": "转置单元格区域"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "从一个范围或数组返回唯一值。"}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "搜索表区域首列满足条件的元素，确定待检索单元格在区域中的行序号，再进一步返回选定单元格的值。默认情况下，表是以升序排序的"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "在某个范围或数组中搜索匹配项，并通过第二个范围或数组返回相应的项。默认情况下使用精确匹配"}, "CELL": {"a": "(info_type; [reference])", "d": "返回有关单元格的格式、位置或内容的信息。"}, "ERROR.TYPE": {"a": "(error_val)", "d": "返回与错误值对应的数字。"}, "ISBLANK": {"a": "(value)", "d": "检查是否引用了空单元格，返回 TRUE 或 FALSE"}, "ISERR": {"a": "(value)", "d": "检测一个值是否为 #N/A 以外的错误，返回 TRUE 或 FALSE"}, "ISERROR": {"a": "(value)", "d": "检测一个值是否为错误，返回 TRUE 或 FALSE"}, "ISEVEN": {"a": "(number)", "d": "如果数字为偶数则返回 TRUE"}, "ISFORMULA": {"a": "(reference)", "d": "检查引用是否指向包含公式的单元格，并返回 TRUE 或 FALSE"}, "ISLOGICAL": {"a": "(value)", "d": "检测一个值是否是逻辑值(TRUE 或 FALSE)，返回 TRUE 或 FALSE"}, "ISNA": {"a": "(value)", "d": "检测一个值是否为 #N/A，返回 TRUE 或 FALSE"}, "ISNONTEXT": {"a": "(value)", "d": "检测一个值是否不是文本(空单元格不是文本)，返回 TRUE 或 FALSE"}, "ISNUMBER": {"a": "(value)", "d": "检测一个值是否是数值，返回 TRUE 或 FALSE"}, "ISODD": {"a": "(number)", "d": "如果数字为奇数则返回 TRUE"}, "ISREF": {"a": "(value)", "d": "检测一个值是否为引用，返回 TRUE 或 FALSE"}, "ISTEXT": {"a": "(value)", "d": "检测一个值是否为文本，返回 TRUE 或 FALSE"}, "N": {"a": "(value)", "d": "将不是数值形式的值转换为数值形式。日期转换成序列值，TRUE 转换成 1，其他值转换成 0"}, "NA": {"a": "()", "d": "返回错误值 #N/A (无法计算出数值)"}, "SHEET": {"a": "([value])", "d": "返回引用的工作表的工作表编号"}, "SHEETS": {"a": "([reference])", "d": "返回引用中的工作表数目"}, "TYPE": {"a": "(value)", "d": "以整数形式返回值的数据类型: 数值 = 1；文字 = 2；逻辑值 = 4；错误值 = 16；数组 = 64；复合数据 = 128"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "检查是否所有参数均为 TRUE，如果所有参数值均为 TRUE，则返回 TRUE"}, "FALSE": {"a": "()", "d": "返回逻辑值 FALSE"}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "判断是否满足某个条件，如果满足返回一个值，如果不满足则返回另一个值。"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": " 检查是否满足一个或多个条件并返回与第一个 TRUE 条件对应的值"}, "IFERROR": {"a": "(value; value_if_error)", "d": "如果表达式是一个错误，则返回 value_if_error，否则返回表达式自身的值"}, "IFNA": {"a": "(value; value_if_na)", "d": "如果表达式解析为 #N/A，则返回您指定的值,否则返回表达式的结果"}, "NOT": {"a": "(logical)", "d": "对参数的逻辑值求反: 参数为 TRUE 时返回 FALSE；参数为 FALSE 时返回TRUE"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "如果任一参数值为 TRUE，即返回 TRUE；只有当所有参数值均为 FALSE 时才返回 FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "根据值列表计算表达式并返回与第一个匹配值对应的结果。如果没有匹配项，则返回可选默认值"}, "TRUE": {"a": "()", "d": "返回逻辑值 TRUE"}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "返回所有参数的逻辑“异或”值"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " 返回分隔字符之前的文本。"}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " 返回分隔字符之后的文本。"}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": " 使用分隔符将文本拆分为行或列。"}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "在指定数目的值后将行或列向量换行。"}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "将数组垂直堆叠到一个数组中。"}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "将数组水平堆叠到一个数组中。"}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "返回数组或引用中的行。"}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "返回数组或引用中的列。"}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "以一列形式返回数组。"}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "以一行形式返回数组。"}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "在指定数目的值后将行或列向量换行。"}, "TAKE": {"a": "(array, rows, [columns])", "d": "从数组开头或结尾返回行或列。"}, "DROP": {"a": "(array, rows, [columns])", "d": "从数组开头或结尾删除行或列。"}}