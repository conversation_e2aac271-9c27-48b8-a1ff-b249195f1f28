﻿<!DOCTYPE html>
<html>
	<head>
		<title>File tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - File tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>File tab</h1>
            <p>The <b>File</b> tab in the <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> allows performing some basic file operations.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Presentation Editor:</p>
                <p><img alt="File tab" src="../images/interface/filetab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Presentation Editor:</p>
                <p><img alt="File tab" src="../images/interface/desktop_filetab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li>
                    <span class="onlineDocumentFeatures">in the <em>online version</em>, <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">save</a> the current file (in case the <b>Autosave</b> option is disabled), <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">download as</a> (save the document in the selected format to the hard disk drive of your computer), <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">save copy as</a> (save a copy of the document in the selected format to the portal documents), <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">print</a> or <a href="../UsageInstructions/ViewDocInfo.htm" onclick="onhyperlinkclick(this)">rename</a> it,</span>
                    <span class="desktopDocumentFeatures">in the <em>desktop version</em>, <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">save</a> the current file keeping the current format and location using the <b>Save</b> option or save the current file under a different name and change its location or format using the <b>Save as</b> option, <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">print</a> the file.</span>
                </li>
                <li>protect the file using a <a href="../HelpfulHints/Password.htm" onclick="onhyperlinkclick(this)">password</a>, change or remove the password;</li>
                <li class="desktopDocumentFeatures">protect the file using a digital signature (available in the <em>desktop version</em> only);</li>
                <li class="onlineDocumentFeatures"><a href="../UsageInstructions/OpenCreateNew.htm" onclick="onhyperlinkclick(this)">create</a> a new presentation or open a recently edited one (available in the <em>online version</em> only),</li>
                <li>view <a href="../UsageInstructions/ViewPresentationInfo.htm" onclick="onhyperlinkclick(this)">general information</a> about the presentation or change some file properties,</li>
                <li class="onlineDocumentFeatures">manage <a href="../UsageInstructions/ViewPresentationInfo.htm" onclick="onhyperlinkclick(this)">access rights</a> (available in the <em>online version</em> only),</li>
                <li>access the <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Advanced Settings</a> of the editor,</li>
                <li><span class="desktopDocumentFeatures">in the <em>desktop version</em>, open the folder, where the file is stored, in the <b>File Explorer</b> window.</span><span class="onlineDocumentFeatures"> In the <em>online version</em>, open the folder of the <b>Documents</b> module, where the file is stored, in a new browser tab.</span></li>
            </ul>
		</div>
	</body>
</html>