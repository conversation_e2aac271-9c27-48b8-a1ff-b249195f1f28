﻿<!DOCTYPE html>
<html>
	<head>
		<title>ISOKALENDERWOCHE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ISOKALENDERWOCHE-Funktion</h1>
			<p>Die Funktion <b>ISOKALENDERWOCHE</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie gibt die Zahl der ISO-Kalenderwoche des Jahres für ein angegebenes Datum zurück. Die Funktion gibt eine zahl zwischen 1 und 54 zurück.</p>
			<p>Die Formelsyntax der Funktion <b>ISOKALENDERWOCHE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ISOKALENDERWOCHE(Datum)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Datum</em></b> das Datum für das Sie die ISO-Kalenderwoche finden wollen. Dabei kann es sich um einen Bezug zu einer Zelle mit einem Datum handeln oder ein <a href="Date.htm" onclick="onhyperlinkclick(this)">Datum</a>, dass durch die Funktion Datum oder eine andere Daten- und Zeitfunktion zurückgegeben wird.</p>
			<p>Anwendung der Funktion <b>ISOKALENDERWOCHE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ISOKALENDERWOCHE</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ISOKALENDERWOCHE-Funktion" src="../images/isoweeknum.png" /></p>
		</div>
	</body>
</html>