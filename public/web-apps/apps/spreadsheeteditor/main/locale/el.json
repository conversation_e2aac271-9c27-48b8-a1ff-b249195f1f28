{"cancelButtonText": "Ακύρωση", "Common.Controllers.Chat.notcriticalErrorTitle": "Προειδοποίηση", "Common.Controllers.Chat.textEnterMessage": "Εισάγετε το μήνυμά σας εδώ", "Common.Controllers.History.notcriticalErrorTitle": "Προειδοποίηση", "Common.define.chartData.textArea": "Περιοχή", "Common.define.chartData.textAreaStacked": "Σωρευμένη περιοχή", "Common.define.chartData.textAreaStackedPer": "100% Σωρευμένη περιοχή", "Common.define.chartData.textBar": "Μπάρα", "Common.define.chartData.textBarNormal": "Ομαδοποιημένη στήλη", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Ομαδοποιημένη στήλη", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON> στήλη", "Common.define.chartData.textBarStacked": "Σωρευμένη στήλη", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Σωρευμένη στήλη", "Common.define.chartData.textBarStackedPer": "100% Σωρευμένη στήλη", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Σωρευμένη στήλη", "Common.define.chartData.textCharts": "Γραφήματα", "Common.define.chartData.textColumn": "Στήλη", "Common.define.chartData.textColumnSpark": "Στήλη", "Common.define.chartData.textCombo": "Συνδυασμ<PERSON>ς", "Common.define.chartData.textComboAreaBar": "Σωρευμένη στήλη ομαδοποιημένη ανά περιοχή", "Common.define.chartData.textComboBarLine": "Ομαδοποιημένη γραμμή - στήλης", "Common.define.chartData.textComboBarLineSecondary": "Ομαδοποιημένη γραμμή - στήλη σε δευτερεύοντα άξονα", "Common.define.chartData.textComboCustom": "Προσαρμοσμένος συνδυασμός", "Common.define.chartData.textDoughnut": "Ντ<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Ομαδοποιημένη μπάρα", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> Ομαδοποιημένη μπάρα", "Common.define.chartData.textHBarStacked": "Σωρευμένη μπάρα", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Σωρευμένη μπάρα", "Common.define.chartData.textHBarStackedPer": "100% Σωρευμένη μπάρα", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Σωρευμένη μπάρα", "Common.define.chartData.textLine": "Γραμμή", "Common.define.chartData.textLine3d": "3-D γραμμή", "Common.define.chartData.textLineMarker": "Γραμμή με δείκτες", "Common.define.chartData.textLineSpark": "Γραμμή", "Common.define.chartData.textLineStacked": "Σωρευμένη γραμμή", "Common.define.chartData.textLineStackedMarker": "Σωρευμένη γραμμή με δείκτες", "Common.define.chartData.textLineStackedPer": "100% Σωρευμένη γραμμή", "Common.define.chartData.textLineStackedPerMarker": "100% Σωρευμένη γραμμή με δείκτες", "Common.define.chartData.textPie": "Πίτα", "Common.define.chartData.textPie3d": "3-<PERSON> πίτα", "Common.define.chartData.textPoint": "ΧΥ (Διασπορά)", "Common.define.chartData.textScatter": "Διασπορά", "Common.define.chartData.textScatterLine": "Διασπορά με ευθείες γραμμές", "Common.define.chartData.textScatterLineMarker": "Διασπορά με ευθείες γραμμές και δείκτες", "Common.define.chartData.textScatterSmooth": "Διασπορά με ομαλές γραμμές", "Common.define.chartData.textScatterSmoothMarker": "Διασπορά με ομαλές γραμμές και δείκτες", "Common.define.chartData.textSparks": "Μικρογραφήματα", "Common.define.chartData.textStock": "Μετοχή", "Common.define.chartData.textSurface": "Επιφάνεια", "Common.define.chartData.textWinLossSpark": "Νίκες/Ήττες", "Common.define.conditionalData.exampleText": "ΑαΒβΓγΨψΩω", "Common.define.conditionalData.noFormatText": "Δεν ορίστηκε μορφή", "Common.define.conditionalData.text1Above": "1 τυπική απόκλιση πάνω", "Common.define.conditionalData.text1Below": "1 τυπική απόκλιση κάτω", "Common.define.conditionalData.text2Above": "2 τυπική απόκλιση πάνω", "Common.define.conditionalData.text2Below": "2 τυπική απόκλιση κάτω", "Common.define.conditionalData.text3Above": "3 τυπική απόκλιση πάνω", "Common.define.conditionalData.text3Below": "3 τυπική απόκλιση κάτω", "Common.define.conditionalData.textAbove": "Πάνω από", "Common.define.conditionalData.textAverage": "Μέ<PERSON><PERSON> Όρος", "Common.define.conditionalData.textBegins": "Αρχίζει με", "Common.define.conditionalData.textBelow": "Κάτω από", "Common.define.conditionalData.textBetween": "Μεταξύ", "Common.define.conditionalData.textBlank": "Κενό", "Common.define.conditionalData.textBlanks": "Περιέχει κενά", "Common.define.conditionalData.textBottom": "Κάτω", "Common.define.conditionalData.textContains": "Περιέχει", "Common.define.conditionalData.textDataBar": "Μπάρα δεδομένων", "Common.define.conditionalData.textDate": "Ημερομηνία", "Common.define.conditionalData.textDuplicate": "Δημιουργ<PERSON>α Διπλότυπου", "Common.define.conditionalData.textEnds": "Τελειώνει με", "Common.define.conditionalData.textEqAbove": "Ίσο με ή μεγαλύτερο", "Common.define.conditionalData.textEqBelow": "Ίσο με ή μικρότερο", "Common.define.conditionalData.textEqual": "Ίσο με", "Common.define.conditionalData.textError": "Σφάλμα", "Common.define.conditionalData.textErrors": "Περιέχει σφάλματα", "Common.define.conditionalData.textFormula": "Τύπος", "Common.define.conditionalData.textGreater": "Μεγαλύτερο από", "Common.define.conditionalData.textGreaterEq": "Μεγαλύτερο από ή ίσο με", "Common.define.conditionalData.textIconSets": "Σύνολα εικονιδίων", "Common.define.conditionalData.textLast7days": "Τις τελευταίες 7 ημέρες", "Common.define.conditionalData.textLastMonth": "Τον περασμένο μήνα", "Common.define.conditionalData.textLastWeek": "Την περασμένη εβδομάδα", "Common.define.conditionalData.textLess": "Μικρότερο από", "Common.define.conditionalData.textLessEq": "Μικρότερο από ή ίσο με", "Common.define.conditionalData.textNextMonth": "Τον επόμενο μήνα", "Common.define.conditionalData.textNextWeek": "Την επόμενη εβδομάδα", "Common.define.conditionalData.textNotBetween": "Όχι ανάμεσα", "Common.define.conditionalData.textNotBlanks": "Δεν περιέχει κενά", "Common.define.conditionalData.textNotContains": "Δεν περιέχει", "Common.define.conditionalData.textNotEqual": "Διάφορο από", "Common.define.conditionalData.textNotErrors": "Δεν περιέχει σφάλματα", "Common.define.conditionalData.textText": "Κείμενο", "Common.define.conditionalData.textThisMonth": "Αυτό το μήνα", "Common.define.conditionalData.textThisWeek": "Αυτή την εβδομάδα", "Common.define.conditionalData.textToday": "Σήμερα", "Common.define.conditionalData.textTomorrow": "Αύριο", "Common.define.conditionalData.textTop": "Επάνω", "Common.define.conditionalData.textUnique": "Μοναδικό", "Common.define.conditionalData.textValue": "Η τιμή είναι ", "Common.define.conditionalData.textYesterday": "Χθ<PERSON><PERSON>", "Common.Translation.warnFileLocked": "Το αρχείο τελεί υπό επεξεργασία σε άλλη εφαρμογή. Μπορείτε να συνεχίσετε την επεξεργασία και να το αποθηκεύσετε ως αντίγραφo.", "Common.Translation.warnFileLockedBtnEdit": "Δημιουρ<PERSON><PERSON><PERSON> αντιγράφου", "Common.Translation.warnFileLockedBtnView": "Άνοιγμα για προβολή", "Common.UI.ButtonColored.textAutoColor": "Αυτόματα", "Common.UI.ButtonColored.textNewColor": "Νέου Προσαρμοσμένου Χρώματος", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON><PERSON> τεχνοτροπίες", "Common.UI.ExtendedColorDialog.addButtonText": "Προσθήκη", "Common.UI.ExtendedColorDialog.textCurrent": "Τρέχουσα", "Common.UI.ExtendedColorDialog.textHexErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια τιμή μεταξύ 000000 και FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Νέο", "Common.UI.ExtendedColorDialog.textRGBErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 και 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Απόκρυψη συνθηματικού", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Εμφάνιση συνθηματικού", "Common.UI.SearchBar.textFind": "Εύρεση", "Common.UI.SearchBar.tipCloseSearch": "Κλείσι<PERSON>ο αναζήτησης", "Common.UI.SearchBar.tipNextResult": "Επόμενο αποτέλεσμα", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Άνοιγμα σύνθετων ρυθμίσεων", "Common.UI.SearchBar.tipPreviousResult": "Προηγούμενο αποτέλεσμα", "Common.UI.SearchDialog.textHighlight": "Επισήμανση αποτελεσμάτων", "Common.UI.SearchDialog.textMatchCase": "Με διάκριση πεζών - κε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν γραμμάτων", "Common.UI.SearchDialog.textReplaceDef": "Εισάγετε το κείμενο αντικατάστασης", "Common.UI.SearchDialog.textSearchStart": "Εισάγετε το κείμενό σας εδώ", "Common.UI.SearchDialog.textTitle": "Εύρεση και Αντικατάσταση", "Common.UI.SearchDialog.textTitle2": "Εύρεση", "Common.UI.SearchDialog.textWholeWords": "Ολόκληρες λέξεις μόνο", "Common.UI.SearchDialog.txtBtnHideReplace": "Απόκρυψη Αντικατάστασης", "Common.UI.SearchDialog.txtBtnReplace": "Αντικατάσταση", "Common.UI.SearchDialog.txtBtnReplaceAll": "Αντικατάσταση Όλων", "Common.UI.SynchronizeTip.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.UI.SynchronizeTip.textSynchronize": "Το έγγραφο έχει αλλάξει από άλλο χρήστη.<br>Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>με κάντε κλικ για να αποθηκεύσετε τις αλλαγές σας και να φορτώσετε ξανά τις ενημερώσεις.", "Common.UI.ThemeColorPalette.textRecentColors": "Πρόσφατα Χρώματα", "Common.UI.ThemeColorPalette.textStandartColors": "Τυπικά Χρώματα", "Common.UI.ThemeColorPalette.textThemeColors": "Χρώμα<PERSON><PERSON>έμ<PERSON>τος", "Common.UI.Themes.txtThemeClassicLight": "Κλασικό Ανοιχτό", "Common.UI.Themes.txtThemeDark": "Σκούρο", "Common.UI.Themes.txtThemeLight": "Ανοιχτό", "Common.UI.Themes.txtThemeSystem": "Ίδιο με το σύστημα", "Common.UI.Window.cancelButtonText": "Ακύρωση", "Common.UI.Window.closeButtonText": "Κλείσιμο", "Common.UI.Window.noButtonText": "Όχι", "Common.UI.Window.okButtonText": "Εντάξει", "Common.UI.Window.textConfirmation": "Επιβεβαίωση", "Common.UI.Window.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.UI.Window.textError": "Σφάλμα", "Common.UI.Window.textInformation": "Πληροφορία", "Common.UI.Window.textWarning": "Προειδοποίηση", "Common.UI.Window.yesButtonText": "Ναι", "Common.Utils.Metric.txtCm": "εκ", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "Διεύθυνση: ", "Common.Views.About.txtLicensee": "ΑΔΕΙΟΔΕΚΤΗΣ", "Common.Views.About.txtLicensor": "ΑΔΕΙΟΔΟΤΗΣ", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Υποστηρίζεται από", "Common.Views.About.txtTel": "Tηλ.: ", "Common.Views.About.txtVersion": "Έκδοση ", "Common.Views.AutoCorrectDialog.textAdd": "Προσθήκη", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Εφαρμογή κατά την εργασία", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Αυτόματη Διόρθωση", "Common.Views.AutoCorrectDialog.textAutoFormat": "Αυτόματη Μορφοποίηση Κατά Την Πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textBy": "Από", "Common.Views.AutoCorrectDialog.textDelete": "Διαγραφή", "Common.Views.AutoCorrectDialog.textHyperlink": "Μονοπάτια δικτύου και διαδικτύου με υπερσυνδέσμους", "Common.Views.AutoCorrectDialog.textMathCorrect": "Αυτόματη Διόρθωση Μαθηματικών", "Common.Views.AutoCorrectDialog.textNewRowCol": "Συμπερίληψη νέων γραμμών και στηλών στον πίνακα", "Common.Views.AutoCorrectDialog.textRecognized": "Αναγνωρισμένες Συναρτήσεις", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Οι ακόλουθες εκφράσεις αναγνωρίζονται ως μαθηματικές. Δεν θα μορφοποιηθούν αυτόματα με πλάγια γράμματα.", "Common.Views.AutoCorrectDialog.textReplace": "Αντικατάσταση", "Common.Views.AutoCorrectDialog.textReplaceText": "Αντικ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Κατά Την Πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textReplaceType": "Αντικα<PERSON><PERSON><PERSON>τα<PERSON>η κειμένου κατά την πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textReset": "Αρχικοποίηση", "Common.Views.AutoCorrectDialog.textResetAll": "Αρχικοποίηση στην προεπιλογή", "Common.Views.AutoCorrectDialog.textRestore": "Επαναφορά", "Common.Views.AutoCorrectDialog.textTitle": "Αυτόματη Διόρθωση", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Οι αναγνωρισμένες συναρτήσεις πρέπει να περιέχουν μόνο τα γράμματα A <PERSON>ω<PERSON> Z, κεφαλα<PERSON>α ή μικρά.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Κάθε έκφραση που προσθέσατε θα αφαιρεθεί και ό,τι αφαιρέθηκε θα αποκατασταθεί. Θέλετε να συνεχίσετε;", "Common.Views.AutoCorrectDialog.warnReplace": "Η καταχώρηση αυτόματης διόρθωσης για %1 υπάρχει ήδη. Θέλετε να την αντικαταστήσετε;", "Common.Views.AutoCorrectDialog.warnReset": "Κάθε αυτόματη διόρθωση που προσθέσατε θα αφαιρεθεί και ό,τι τροποποιήθηκε θα αποκατασταθεί στην αρχική του τιμή. Θέλετε να συνεχίσετε;", "Common.Views.AutoCorrectDialog.warnRestore": "Η καταχώρηση αυτόματης διόρθωσης για %1 θα τεθεί στην αρχική τιμή της. Θέλετε να συνεχίσετε;", "Common.Views.Chat.textSend": "Αποστολή", "Common.Views.Comments.mniAuthorAsc": "Συγγραφ<PERSON>ας Α έως Ω", "Common.Views.Comments.mniAuthorDesc": "Συγγραφ<PERSON>ας Ω έως Α", "Common.Views.Comments.mniDateAsc": "Παλαιότερο", "Common.Views.Comments.mniDateDesc": "Νεότερο", "Common.Views.Comments.mniFilterGroups": "Φιλτράρισμα κατά Ομάδα", "Common.Views.Comments.mniPositionAsc": "Από πάνω", "Common.Views.Comments.mniPositionDesc": "Από κάτω", "Common.Views.Comments.textAdd": "Προσθήκη", "Common.Views.Comments.textAddComment": "Προσθήκη Σχολίου", "Common.Views.Comments.textAddCommentToDoc": "Προσθήκη Σχολίου στο Έγγραφο", "Common.Views.Comments.textAddReply": "Προσθήκη Απάντησης", "Common.Views.Comments.textAll": "Όλα", "Common.Views.Comments.textAnonym": "Επισκέπτης", "Common.Views.Comments.textCancel": "Ακύρωση", "Common.Views.Comments.textClose": "Κλείσιμο", "Common.Views.Comments.textClosePanel": "Κλείσιμο σχολίων", "Common.Views.Comments.textComments": "Σχόλια", "Common.Views.Comments.textEdit": "Εντάξει", "Common.Views.Comments.textEnterCommentHint": "Εισάγετε το σχόλιό σας εδώ", "Common.Views.Comments.textHintAddComment": "Προσθήκη σχολίου", "Common.Views.Comments.textOpenAgain": "Άνοιγμα Ξανά", "Common.Views.Comments.textReply": "Απάντηση", "Common.Views.Comments.textResolve": "Επίλυση", "Common.Views.Comments.textResolved": "Επιλύθηκε", "Common.Views.Comments.textSort": "Ταξινόμηση σχολίων", "Common.Views.Comments.textViewResolved": "Δεν έχετε άδεια να ανοίξετε ξανά το σχόλιο", "Common.Views.Comments.txtEmpty": "Δεν υπάρχουν σχόλια στο φύλλο.", "Common.Views.CopyWarningDialog.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.Views.CopyWarningDialog.textMsg": "Η αντιγραφή, η αποκοπή και η επικόλληση μέσω των κουμπιών της εργαλειοθήκης του συντάκτη καθώς και οι ενέργειες του μενού συμφραζομένων εφαρμόζονται μόνο εντός αυτής της καρτέλας.<br><br>Γ<PERSON>α αντιγραφή ή επικόλληση από ή προς εφαρμογές εκτός της καρτέλας χρησιμοποιήστε τους ακόλουθους συνδυασμούς πλήκτρων:", "Common.Views.CopyWarningDialog.textTitle": "Ενέργειες Αντιγ<PERSON>α<PERSON><PERSON><PERSON>, Α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και Επικόλλησης", "Common.Views.CopyWarningDialog.textToCopy": "για Αντιγραφή", "Common.Views.CopyWarningDialog.textToCut": "για Αποκοπή", "Common.Views.CopyWarningDialog.textToPaste": "για Επικόλληση", "Common.Views.DocumentAccessDialog.textLoading": "Φόρτωση...", "Common.Views.DocumentAccessDialog.textTitle": "Ρυθμίσεις Διαμοιρασμού", "Common.Views.EditNameDialog.textLabel": "Ετικέτα:", "Common.Views.EditNameDialog.textLabelError": "Η ετικέτα δεν μπορεί να είναι κενή.", "Common.Views.Header.labelCoUsersDescr": "Οι χρήστες που επεξεργ<PERSON><PERSON>ονται το αρχείο:", "Common.Views.Header.textAddFavorite": "Σημείωση ως αγαπημένο", "Common.Views.Header.textAdvSettings": "Προηγμένες ρυθμίσεις", "Common.Views.Header.textBack": "Άνοιγμα τοποθεσίας αρχείου", "Common.Views.Header.textCompactView": "Απόκρυψη Γραμμής Εργαλείων", "Common.Views.Header.textHideLines": "Απόκρυψη Χαράκων", "Common.Views.Header.textHideStatusBar": "Συνδυ<PERSON><PERSON><PERSON><PERSON>ς μπάρας φύλλου και κατάστασης", "Common.Views.Header.textRemoveFavorite": "Αφαίρεση από τα Αγαπημένα", "Common.Views.Header.textSaveBegin": "Αποθήκευση...", "Common.Views.Header.textSaveChanged": "Τροποποιημένο", "Common.Views.Header.textSaveEnd": "Όλες οι αλλαγές αποθηκεύτηκαν", "Common.Views.Header.textSaveExpander": "Όλες οι αλλαγές αποθηκεύτηκαν", "Common.Views.Header.textShare": "Διαμοιρασμός", "Common.Views.Header.textZoom": "Εστίαση", "Common.Views.Header.tipAccessRights": "Διαχείριση δικαιωμάτων πρόσβασης εγγράφου", "Common.Views.Header.tipDownload": "Λήψη αρχείου", "Common.Views.Header.tipGoEdit": "Επεξεργασία τρέχοντος αρχείου", "Common.Views.Header.tipPrint": "Εκτύπωση αρχείου", "Common.Views.Header.tipRedo": "Επανάληψη", "Common.Views.Header.tipSave": "Αποθήκευση", "Common.Views.Header.tipSearch": "Αναζήτηση", "Common.Views.Header.tipUndo": "Αναίρεση", "Common.Views.Header.tipUndock": "Απαγ<PERSON>ίστρωση σε ξεχωριστό παράθυρο", "Common.Views.Header.tipUsers": "Προβολή χρηστών", "Common.Views.Header.tipViewSettings": "Προβολή ρυθμίσεων", "Common.Views.Header.tipViewUsers": "Προβολή χρηστών και διαχείριση δικαιωμάτων πρόσβασης σε έγγραφα", "Common.Views.Header.txtAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "Common.Views.Header.txtRename": "Μετονομασία", "Common.Views.History.textCloseHistory": "Κλείσι<PERSON><PERSON> Ιστορικού", "Common.Views.History.textHide": "Κλείσιμο", "Common.Views.History.textHideAll": "Απόκρυψη λεπτομερών αλλαγών", "Common.Views.History.textRestore": "Επαναφορά", "Common.Views.History.textShow": "Επέκταση", "Common.Views.History.textShowAll": "Εμφάνιση λεπτομερών αλλαγών", "Common.Views.History.textVer": "εκδ.", "Common.Views.ImageFromUrlDialog.textUrl": "Επικόλληση URL εικόνας:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Αυτό το πεδίο πρέπει να είναι διεύθυνση URL με τη μορφή «http://www.example.com»", "Common.Views.ListSettingsDialog.textBulleted": "Με κουκίδες", "Common.Views.ListSettingsDialog.textNumbering": "Αριθμημένο", "Common.Views.ListSettingsDialog.tipChange": "Αλλαγ<PERSON> κουκίδων", "Common.Views.ListSettingsDialog.txtBullet": "Κουκκίδα", "Common.Views.ListSettingsDialog.txtColor": "Χρώμα", "Common.Views.ListSettingsDialog.txtNewBullet": "Νέα κουκίδα", "Common.Views.ListSettingsDialog.txtNone": "Κανένα", "Common.Views.ListSettingsDialog.txtOfText": "% του κειμένου", "Common.Views.ListSettingsDialog.txtSize": "Μέγεθος", "Common.Views.ListSettingsDialog.txtStart": "Έναρξη από", "Common.Views.ListSettingsDialog.txtSymbol": "Σύμβολο", "Common.Views.ListSettingsDialog.txtTitle": "Ρυθμίσεις Λίστας", "Common.Views.ListSettingsDialog.txtType": "Τύπος", "Common.Views.OpenDialog.closeButtonText": "Κλείσιμο Αρχείου", "Common.Views.OpenDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "Common.Views.OpenDialog.textSelectData": "Επιλογή δεδομένων", "Common.Views.OpenDialog.txtAdvanced": "Για προχωρημένους", "Common.Views.OpenDialog.txtColon": "Άνω κάτω τελεία", "Common.Views.OpenDialog.txtComma": "Κόμμα", "Common.Views.OpenDialog.txtDelimiter": "Διαχωριστικό", "Common.Views.OpenDialog.txtDestData": "Επιλογή θέσης για τα δεδομένα", "Common.Views.OpenDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.OpenDialog.txtEncoding": "Κωδικοποίηση", "Common.Views.OpenDialog.txtIncorrectPwd": "Το συνθηματικό είναι εσφαλμένο.", "Common.Views.OpenDialog.txtOpenFile": "Εισάγετε συνθηματικό για να ανοίξετε το αρχείο", "Common.Views.OpenDialog.txtOther": "Άλλο", "Common.Views.OpenDialog.txtPassword": "Συνθηματικό", "Common.Views.OpenDialog.txtPreview": "Προεπισκόπηση", "Common.Views.OpenDialog.txtProtected": "Μό<PERSON><PERSON>ς βάλετε το συνθηματι<PERSON><PERSON> και ανοίξετε το αρχείο, θα γίνει επαναφορά του τρέχοντος συνθηματικού.", "Common.Views.OpenDialog.txtSemicolon": "Άνω τελεία", "Common.Views.OpenDialog.txtSpace": "Κενό διάστημα", "Common.Views.OpenDialog.txtTab": "Καρτ<PERSON><PERSON>α", "Common.Views.OpenDialog.txtTitle": "Διαλέξτε %1 επιλογές", "Common.Views.OpenDialog.txtTitleProtected": "Προστατευμένο Αρχείο", "Common.Views.PasswordDialog.txtDescription": "Ορίστε ένα συνθηματικό για την προστασία αυτού του εγγράφου", "Common.Views.PasswordDialog.txtIncorrectPwd": "Το συνθηματικ<PERSON> επιβεβαίωσης δεν είναι πανομοιότυπο", "Common.Views.PasswordDialog.txtPassword": "Συνθηματικό", "Common.Views.PasswordDialog.txtRepeat": "Επανάληψη συνθηματικού", "Common.Views.PasswordDialog.txtTitle": "Ορισμός Συνθηματικού", "Common.Views.PasswordDialog.txtWarning": "Προσοχή: <PERSON><PERSON><PERSON> χ<PERSON>τε ή ξεχάσετε το συνθηματικό, δεν είναι δυνατή η ανάκτησή του. Παρακαλούμε διατηρήστε το σε ασφαλές μέρος.", "Common.Views.PluginDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.Plugins.groupCaption": "Πρόσθετα", "Common.Views.Plugins.strPlugins": "Πρόσθετα", "Common.Views.Plugins.textClosePanel": "Κλείσιμο πρόσθετου", "Common.Views.Plugins.textLoading": "Γίνεται φόρτωση", "Common.Views.Plugins.textStart": "Εκκίνηση", "Common.Views.Plugins.textStop": "Διακοπή", "Common.Views.Protection.hintAddPwd": "Κρυπτογράφηση με συνθηματικό", "Common.Views.Protection.hintPwd": "Αλλα<PERSON><PERSON> <PERSON> διαγραφή συνθηματικού", "Common.Views.Protection.hintSignature": "Προσθήκη ψηφιακής υπογραφής ή γραμμής υπογραφής", "Common.Views.Protection.txtAddPwd": "Προσθήκη συνθηματικού", "Common.Views.Protection.txtChangePwd": "Αλλαγή συνθηματικού", "Common.Views.Protection.txtDeletePwd": "Διαγραφή συνθηματικού", "Common.Views.Protection.txtEncrypt": "Κρυπτογράφηση", "Common.Views.Protection.txtInvisibleSignature": "Προσθήκη ψηφιακής υπογραφής", "Common.Views.Protection.txtSignature": "Υπογραφή", "Common.Views.Protection.txtSignatureLine": "Προσθήκη γραμμής υπογραφής", "Common.Views.RenameDialog.textName": "Όνομα αρχείου", "Common.Views.RenameDialog.txtInvalidName": "Το όνομα αρχείου δεν μπορεί να περιέχει κανέναν από τους ακόλουθους χαρακτήρες:", "Common.Views.ReviewChanges.hintNext": "Στην επόμενη αλλαγή", "Common.Views.ReviewChanges.hintPrev": "Στην προηγούμενη αλλαγή", "Common.Views.ReviewChanges.strFast": "Γρήγ<PERSON><PERSON>η", "Common.Views.ReviewChanges.strFastDesc": "Συν-επεξεργασία πραγματικού χρόνου. Όλες οι αλλαγές αποθηκεύονται αυτόματα.", "Common.Views.ReviewChanges.strStrict": "Αυστηρή", "Common.Views.ReviewChanges.strStrictDesc": "Χρησιμοποιήστε το κουμπί 'Αποθήκευση' για να συγχρονίσετε τις αλλαγές που κάνετε εσείς και οι άλλοι.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Αποδοχή τρέχουσας αλλαγής", "Common.Views.ReviewChanges.tipCoAuthMode": "Ορισμ<PERSON><PERSON> κατάστασης συν-επεξεργασίας", "Common.Views.ReviewChanges.tipCommentRem": "Αφαίρεση σχολίων", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Αφαίρεση υφιστάμενων σχολίων", "Common.Views.ReviewChanges.tipCommentResolve": "Επίλυση σχολίων", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Επίλυση των τρεχόντων σχολίων", "Common.Views.ReviewChanges.tipHistory": "Εμφάνιση ιστορικού εκδόσεων", "Common.Views.ReviewChanges.tipRejectCurrent": "Απόρριψη τρέχουσας αλλαγής", "Common.Views.ReviewChanges.tipReview": "Παρα<PERSON><PERSON>λούθηση αλλαγών", "Common.Views.ReviewChanges.tipReviewView": "Επιλέξτε τον τρόπο προβολής των αλλαγών", "Common.Views.ReviewChanges.tipSetDocLang": "Ορισμ<PERSON><PERSON> γλώσσας εγγράφου", "Common.Views.ReviewChanges.tipSetSpelling": "Έλεγχος ορθογραφίας", "Common.Views.ReviewChanges.tipSharing": "Διαχείριση δικαιωμάτων πρόσβασης εγγράφου", "Common.Views.ReviewChanges.txtAccept": "Αποδοχή", "Common.Views.ReviewChanges.txtAcceptAll": "Αποδοχή Όλων των Αλλαγών", "Common.Views.ReviewChanges.txtAcceptChanges": "Αποδοχ<PERSON> αλλαγών", "Common.Views.ReviewChanges.txtAcceptCurrent": "Αποδοχή Τρέχουσας Αλλαγής", "Common.Views.ReviewChanges.txtChat": "Συνομιλία", "Common.Views.ReviewChanges.txtClose": "Κλείσιμο", "Common.Views.ReviewChanges.txtCoAuthMode": "Κατάσταση Συν-επεξεργασίας", "Common.Views.ReviewChanges.txtCommentRemAll": "Αφαίρεση Όλων των Σχολίων", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Αφαίρεση Υφιστάμενων Σχολίων", "Common.Views.ReviewChanges.txtCommentRemMy": "Αφαίρεση των Σχολίων Μου", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Αφαίρεση Πρόσφατων Σχολίων Μου", "Common.Views.ReviewChanges.txtCommentRemove": "Αφαίρεση", "Common.Views.ReviewChanges.txtCommentResolve": "Επίλυση", "Common.Views.ReviewChanges.txtCommentResolveAll": "Επίλυση Όλων των Σχολίων", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Επίλυση των Τρεχόντων Σχολίων", "Common.Views.ReviewChanges.txtCommentResolveMy": "Επίλυση των Σχολίων Μου", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Επίλυση των Τρεχόντων Σχολίων Μου", "Common.Views.ReviewChanges.txtDocLang": "Γλώσσα", "Common.Views.ReviewChanges.txtFinal": "Όλες οι αλλαγές έγιναν αποδεκτές (Προεπισκόπηση)", "Common.Views.ReviewChanges.txtFinalCap": "Τελικ<PERSON>ς", "Common.Views.ReviewChanges.txtHistory": "Ιστορικ<PERSON> Εκδόσεων", "Common.Views.ReviewChanges.txtMarkup": "Όλες οι αλλαγές (Επεξεργασία)", "Common.Views.ReviewChanges.txtMarkupCap": "Σήμανση", "Common.Views.ReviewChanges.txtNext": "Επόμενο", "Common.Views.ReviewChanges.txtOriginal": "Όλες οι αλλαγές απορρίφθηκαν (Προεπισκόπηση)", "Common.Views.ReviewChanges.txtOriginalCap": "Πρωτότυπο", "Common.Views.ReviewChanges.txtPrev": "Προηγούμενο", "Common.Views.ReviewChanges.txtReject": "Απόρριψη", "Common.Views.ReviewChanges.txtRejectAll": "Απόρριψη Όλων των Αλλαγών", "Common.Views.ReviewChanges.txtRejectChanges": "Απόρριψη αλλαγών", "Common.Views.ReviewChanges.txtRejectCurrent": "Απόρριψη Τρέχουσας Αλλαγής", "Common.Views.ReviewChanges.txtSharing": "Διαμοιρασμός", "Common.Views.ReviewChanges.txtSpelling": "Έλεγχος Ορθογραφίας", "Common.Views.ReviewChanges.txtTurnon": "Παρακ<PERSON>λούθηση Αλλαγών", "Common.Views.ReviewChanges.txtView": "Κατάσταση Προβολής", "Common.Views.ReviewPopover.textAdd": "Προσθήκη", "Common.Views.ReviewPopover.textAddReply": "Προσθήκη Απάντησης", "Common.Views.ReviewPopover.textCancel": "Ακύρωση", "Common.Views.ReviewPopover.textClose": "Κλείσιμο", "Common.Views.ReviewPopover.textEdit": "Εντάξει", "Common.Views.ReviewPopover.textMention": "+mention θα δώσει πρόσβαση στο αρχείο και θα στείλει email", "Common.Views.ReviewPopover.textMentionNotify": "+mention θα ενημερώσει τον χρήστη με email", "Common.Views.ReviewPopover.textOpenAgain": "Άνοιγμα Ξανά", "Common.Views.ReviewPopover.textReply": "Απάντηση", "Common.Views.ReviewPopover.textResolve": "Επίλυση", "Common.Views.ReviewPopover.textViewResolved": "Δεν έχετε άδεια να ανοίξετε ξανά το σχόλιο", "Common.Views.ReviewPopover.txtDeleteTip": "Διαγραφή", "Common.Views.ReviewPopover.txtEditTip": "Επεξεργασία", "Common.Views.SaveAsDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.SaveAsDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> για αποθήκευση", "Common.Views.SearchPanel.textByColumns": "Κατά στήλες", "Common.Views.SearchPanel.textByRows": "Κατά γραμμές", "Common.Views.SearchPanel.textCaseSensitive": "Διάκριση Πεζών-Κεφαλαίων", "Common.Views.SearchPanel.textCell": "Κελί", "Common.Views.SearchPanel.textCloseSearch": "Κλείσι<PERSON>ο αναζήτησης", "Common.Views.SearchPanel.textFind": "Εύρεση", "Common.Views.SearchPanel.textFindAndReplace": "Εύρεση και αντικατάσταση", "Common.Views.SearchPanel.textFormula": "Τύπος", "Common.Views.SearchPanel.textFormulas": "Τύποι", "Common.Views.SearchPanel.textItemEntireCell": "Ολόκληρο το περιεχόμενου κελιού", "Common.Views.SearchPanel.textLookIn": "Αναζήτηση σε", "Common.Views.SearchPanel.textMatchUsingRegExp": "Ταίριασμα με χρήση κανονικ<PERSON>ν εκφράσεων", "Common.Views.SearchPanel.textName": "Όνομα", "Common.Views.SearchPanel.textNoMatches": "Χ<PERSON><PERSON><PERSON>ς ταίριασμα", "Common.Views.SearchPanel.textNoSearchResults": "Δεν υπάρχουν αποτελέσματα αναζήτησης", "Common.Views.SearchPanel.textReplace": "Αντικατάσταση", "Common.Views.SearchPanel.textReplaceAll": "Αντικατάσταση Όλων", "Common.Views.SearchPanel.textReplaceWith": "Αντικατάσταση με", "Common.Views.SearchPanel.textSearch": "Αναζήτηση", "Common.Views.SearchPanel.textSearchOptions": "Ε<PERSON>ι<PERSON><PERSON><PERSON><PERSON><PERSON> αναζήτησης", "Common.Views.SearchPanel.textSearchResults": "Αποτελέσματα αναζήτησης: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "Επιλογή Εύρους Δεδομένων", "Common.Views.SearchPanel.textSheet": "Φύλλο", "Common.Views.SearchPanel.textSpecificRange": "Συγκεκριμένο εύρος", "Common.Views.SearchPanel.textTooManyResults": "Υπάρχουν πάρα πολλά αποτελέσματα για εμφάνιση εδώ", "Common.Views.SearchPanel.textValue": "Τιμή", "Common.Views.SearchPanel.textValues": "Τιμές", "Common.Views.SearchPanel.textWholeWords": "Ολόκληρες λέξεις μόνο", "Common.Views.SearchPanel.textWithin": "Εντ<PERSON>ς", "Common.Views.SearchPanel.textWorkbook": "Βιβλίο Εργασίας", "Common.Views.SearchPanel.tipNextResult": "Επόμενο αποτέλεσμα", "Common.Views.SearchPanel.tipPreviousResult": "Προηγούμενο αποτέλεσμα", "Common.Views.SelectFileDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.SelectFileDlg.textTitle": "Επιλογή Πηγής Δεδομένων", "Common.Views.SignDialog.textBold": "Έντονα", "Common.Views.SignDialog.textCertificate": "Πιστοποιητικό", "Common.Views.SignDialog.textChange": "Αλλαγή", "Common.Views.SignDialog.textInputName": "Εισαγω<PERSON><PERSON> ονόμα<PERSON>ος υπογράφοντος", "Common.Views.SignDialog.textItalic": "Πλάγια", "Common.Views.SignDialog.textNameError": "Το όνομα υπογράφοντα δεν μπορεί να είναι κενό.", "Common.Views.SignDialog.textPurpose": "Ο σκοπός για υπογραφή αυτού του εγγράφου", "Common.Views.SignDialog.textSelect": "Επιλογή", "Common.Views.SignDialog.textSelectImage": "Επιλογή Εικόνας", "Common.Views.SignDialog.textSignature": "Η υπογραφή μοιάζει με", "Common.Views.SignDialog.textTitle": "Υπογράψτε το Έγγραφο", "Common.Views.SignDialog.textUseImage": "ή κάντε κλικ στο 'Επιλογή εικόνας' για να χρησιμοποιήσετε μια εικόνα ως υπογραφή", "Common.Views.SignDialog.textValid": "Έγκυρο από %1 έως %2", "Common.Views.SignDialog.tipFontName": "Όνομα Γραμματοσειράς", "Common.Views.SignDialog.tipFontSize": "Μέγεθος <PERSON>αμματοσειράς", "Common.Views.SignSettingsDialog.textAllowComment": "Να επιτρέπεται στον υπογράφοντα να προσθέτει σχόλιο στο διάλογο υπογραφής", "Common.Views.SignSettingsDialog.textInfoEmail": "Ηλεκτρονική Διεύθυνση", "Common.Views.SignSettingsDialog.textInfoName": "Όνομα", "Common.Views.SignSettingsDialog.textInfoTitle": "Τίτλος Υπογράφοντος", "Common.Views.SignSettingsDialog.textInstructions": "Οδηγίες προς Υπογράφοντα", "Common.Views.SignSettingsDialog.textShowDate": "Εμφάνιση ημερομηνίας υπογραφής στη γραμμή υπογράφοντος", "Common.Views.SignSettingsDialog.textTitle": "Ρύθμιση Υπογραφής", "Common.Views.SignSettingsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Δεκαεξαδική τιμή Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Σήμα Πνευματικών Δικαιωμάτων", "Common.Views.SymbolTableDialog.textDCQuote": "Κλείσιμο Διπλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textDOQuote": "Άνοιγμα Διπλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textEllipsis": "Οριζόντια Έλλειψη", "Common.Views.SymbolTableDialog.textEmDash": "Πλατιά Παύλα Em", "Common.Views.SymbolTableDialog.textEmSpace": "Διάστημα Em", "Common.Views.SymbolTableDialog.textEnDash": "Πλατιά Παύλα En", "Common.Views.SymbolTableDialog.textEnSpace": "Διάστημα En", "Common.Views.SymbolTableDialog.textFont": "Γραμματοσειρά", "Common.Views.SymbolTableDialog.textNBHyphen": "Προστατευμένη Παύλα", "Common.Views.SymbolTableDialog.textNBSpace": "Προστατευμένο Διάστημα", "Common.Views.SymbolTableDialog.textPilcrow": "Σύμβολο <PERSON>αραγράφου", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em Διάστημα", "Common.Views.SymbolTableDialog.textRange": "Εύρ<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Πρόσφατα χρησιμοποιημένα σύμβολα", "Common.Views.SymbolTableDialog.textRegistered": "Σύμβο<PERSON><PERSON>ταχωρημένου", "Common.Views.SymbolTableDialog.textSCQuote": "Κλείσιμο Απλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textSection": "Σύμβολο Τ<PERSON>ή<PERSON>ατος", "Common.Views.SymbolTableDialog.textShortcut": "Πλήκτρο συντόμευσης", "Common.Views.SymbolTableDialog.textSHyphen": "Απαλή Παύλα", "Common.Views.SymbolTableDialog.textSOQuote": "Άνοιγμα Απλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textSpecial": "Ειδικοί χαρακτήρες", "Common.Views.SymbolTableDialog.textSymbols": "Σύμβολα", "Common.Views.SymbolTableDialog.textTitle": "Σύμβολο", "Common.Views.SymbolTableDialog.textTradeMark": "Σύμβολο Εμπορικού Σήματος", "Common.Views.UserNameDialog.textDontShow": "Να μην ερωτηθώ ξανά", "Common.Views.UserNameDialog.textLabel": "Ετικέτα:", "Common.Views.UserNameDialog.textLabelError": "Η ετικέτα δεν μπορεί να είναι κενή.", "SSE.Controllers.DataTab.textColumns": "Στήλες", "SSE.Controllers.DataTab.textEmptyUrl": "Πρέπει να ορίσετε μια διεύθυνση URL.", "SSE.Controllers.DataTab.textRows": "Γραμμές", "SSE.Controllers.DataTab.textWizard": "Κείμενο σε Στήλες", "SSE.Controllers.DataTab.txtDataValidation": "Επικύρωση Δεδομένων", "SSE.Controllers.DataTab.txtExpand": "Επέκταση", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Τα δεδομένα δίπλα στην επιλογή δεν θα διαγραφούν. Θέλετε να επεκτείνετε την επιλογή ώστε να συμπεριλάβει τα παρακείμενα δεδομένα ή να συνεχίσετε μόνο με τα τρέχοντα επιλεγμένα κελιά;", "SSE.Controllers.DataTab.txtExtendDataValidation": "Η επιλογή περιέχει μερικά κελιά χωρίς ρυθμίσεις Επικύρωσης Δεδομένων.<br>Θέλετε να επεκτείνετε την Επικύρωση Δεδομένων και σε αυτά τα κελιά;", "SSE.Controllers.DataTab.txtImportWizard": "Οδηγ<PERSON><PERSON>σαγωγής <PERSON>νου", "SSE.Controllers.DataTab.txtRemDuplicates": "Αφαίρεση Διπλότυπων", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Η επιλογή περιέχει περισσότερους από έναν τύπους επικύρωσης.<br>Διαγραφή τρεχουσών ρυθμίσεων και συνέχεια;", "SSE.Controllers.DataTab.txtRemSelected": "Αφαίρεση στα επιλεγμένα", "SSE.Controllers.DataTab.txtUrlTitle": "Επικόλληση URL δεδομένων", "SSE.Controllers.DocumentHolder.alignmentText": "Στοίχιση", "SSE.Controllers.DocumentHolder.centerText": "Κέντρο", "SSE.Controllers.DocumentHolder.deleteColumnText": "Διαγρα<PERSON><PERSON> Στήλης", "SSE.Controllers.DocumentHolder.deleteRowText": "Διαγραφή Γραμμής", "SSE.Controllers.DocumentHolder.deleteText": "Διαγραφή", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Η παραπομπή του συνδέσμου δεν υπάρχει. Παρακαλούμε διορθώστε τον σύνδεσμο ή διαγράψτε τον.", "SSE.Controllers.DocumentHolder.guestText": "Επισκέπτης", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Στήλη Αριστερά", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Στήλη Δεξιά", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Γραμμή Από Πάνω", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Γραμμή Από <PERSON>ά<PERSON>ω", "SSE.Controllers.DocumentHolder.insertText": "Εισαγωγή", "SSE.Controllers.DocumentHolder.leftText": "Αριστερά", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Controllers.DocumentHolder.rightText": "Δεξιά", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Επιλ<PERSON><PERSON><PERSON><PERSON> αυτόματης διόρθωσης", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0} σύμβολα ({1} εικονοστοιχεία)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Ύψος Γραμμής {0} σημεία ({1} εικονοστοιχεία)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Κάντε κλικ στον σύνδεσμο για να ανοίξει ή κάντε κλικ και κρατήστε πατημένο το κουμπί του ποντικιού για να επιλέξετε το κελί.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Εισαγωγ<PERSON> Αριστερά", "SSE.Controllers.DocumentHolder.textInsertTop": "Εισαγωγ<PERSON>ν<PERSON>", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Ειδική επικόλληση", "SSE.Controllers.DocumentHolder.textStopExpand": "Διακο<PERSON>ή αυτόματης επέκτασης πινάκων", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Πάνω από τον μέσο όρο", "SSE.Controllers.DocumentHolder.txtAddBottom": "Προσθήκη κάτω περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Προσθήκη γραμμής κλάσματος", "SSE.Controllers.DocumentHolder.txtAddHor": "Προσθήκη οριζόντιας γραμμής", "SSE.Controllers.DocumentHolder.txtAddLB": "Προσθήκη αριστερής κάτω γραμμής", "SSE.Controllers.DocumentHolder.txtAddLeft": "Προσθήκη αριστερού περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddLT": "Προσθήκη αριστερής πάνω γραμμής", "SSE.Controllers.DocumentHolder.txtAddRight": "Προσθήκη δεξιού περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddTop": "Προσθήκη επάνω περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddVer": "Προσθήκη κατακόρυφης γραμμής", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Στοίχιση σε χαρακτήρα", "SSE.Controllers.DocumentHolder.txtAll": "(Όλα)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Επιστρέφει όλα τα περιεχόμενα από τον πίνακα ή από τις καθορισμένες στήλες του πίνακα συμπεριλαμβανομένων των επικεφαλίδων των στηλών, τα δεδομένα και τις συνολικές σειρές", "SSE.Controllers.DocumentHolder.txtAnd": "και", "SSE.Controllers.DocumentHolder.txtBegins": "Αρχίζει με", "SSE.Controllers.DocumentHolder.txtBelowAve": "Κάτω από τον μέσο όρο", "SSE.Controllers.DocumentHolder.txtBlanks": "(Κενά)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Ιδιότητες περιγράμματος", "SSE.Controllers.DocumentHolder.txtBottom": "Κάτω", "SSE.Controllers.DocumentHolder.txtColumn": "Στήλη", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Στοίχιση στήλης", "SSE.Controllers.DocumentHolder.txtContains": "Περιέχει", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Επιστρέφει τα κελιά δεδομένων από τον πίνακα ή τις καθορισμένες στήλες του πίνακα", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Μείωση μεγέθους ορίσματος", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Διαγραφή ορίσματος", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Διαγρα<PERSON>ή χειροκίνητης αλλαγής", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Διαγρα<PERSON>ή χαρακτήρων εγκλεισμού", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Διαγρα<PERSON>ή χαρακτήρων εγκλεισμού και διαχωριστών", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Διαγραφή εξίσωσης", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Διαγρα<PERSON><PERSON> χαρακτήρα", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Διαγραφή ρίζας", "SSE.Controllers.DocumentHolder.txtEnds": "Τελειώνει με", "SSE.Controllers.DocumentHolder.txtEquals": "Ισούται", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Ίσο με το χρώμα κελιού", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Ίσο με το χρώμα γραμματοσειράς", "SSE.Controllers.DocumentHolder.txtExpand": "Επέκταση και ταξινόμηση", "SSE.Controllers.DocumentHolder.txtExpandSort": "Τα δεδομένα δίπλα στην επιλογή δεν θα ταξινομηθούν. Θέλετε να επεκτείνετε την επιλογή ώστε να συμπεριλάβει τα παρακείμενα δεδομένα ή να συνεχίσετε με την ταξινόμηση μόνο των επιλεγμένων κελιών;", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Κάτω", "SSE.Controllers.DocumentHolder.txtFilterTop": "Επάνω", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Αλλαγή σε γραμμικό κλάσμα", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Αλλαγή σε πλάγιο κλάσμα", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Αλλαγή σε σύνθετο κλάσμα", "SSE.Controllers.DocumentHolder.txtGreater": "Μεγαλύτερο από", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Μεγαλύτερο ή ίσο με", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πάνω από το κείμενο", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κάτω από το κείμενο", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Επιστρέφει τις επικεφαλίδες των στηλών για τον πίνακα ή τις καθορισμένες στήλες του πίνακα", "SSE.Controllers.DocumentHolder.txtHeight": "Ύψος", "SSE.Controllers.DocumentHolder.txtHideBottom": "Απόκρυψη κάτω περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Απόκρυψη κάτω ορίου", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Απόκρυψη παρένθεσης που κλείνει", "SSE.Controllers.DocumentHolder.txtHideDegree": "Απόκρυψη πτυχίου", "SSE.Controllers.DocumentHolder.txtHideHor": "Απόκρυψη οριζόντιας γραμμής", "SSE.Controllers.DocumentHolder.txtHideLB": "Απόκρυψη αριστερής κάτω γραμμής", "SSE.Controllers.DocumentHolder.txtHideLeft": "Απόκρυψη αριστερού περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideLT": "Απόκρυψη αριστερής πάνω γραμμής", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Απόκρυψη παρένθεσης που ανοίγει", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Απόκρυψη δέσμευσης θέσης", "SSE.Controllers.DocumentHolder.txtHideRight": "Απόκρυψη δεξιού περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideTop": "Απόκρυψη πάνω περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Απόκρυψη άνω ορίου", "SSE.Controllers.DocumentHolder.txtHideVer": "Απόκρυψη κατακόρυφης γραμμής", "SSE.Controllers.DocumentHolder.txtImportWizard": "Οδηγ<PERSON><PERSON>σαγωγής <PERSON>νου", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Αύξηση μεγέθους ορίσματος", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Εισαγωγή ορίσματος μετά", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Εισαγωγή ορίσματος πριν", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Εισαγωγή χειροκίνητης αλλαγής", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Εισαγωγή εξίσωσης μετά", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Εισαγωγή εξίσωσης πριν", "SSE.Controllers.DocumentHolder.txtItems": "αντικείμενα", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Διατήρηση κειμένου μόνο", "SSE.Controllers.DocumentHolder.txtLess": "Μικρότερο από", "SSE.Controllers.DocumentHolder.txtLessEquals": "Μικρότερο από ή ίσο με", "SSE.Controllers.DocumentHolder.txtLimitChange": "Αλλαγή θέσης ορίων", "SSE.Controllers.DocumentHolder.txtLimitOver": "Όριο πάνω από το κείμενο", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Όριο κάτω από το κείμενο", "SSE.Controllers.DocumentHolder.txtLockSort": "Υπάρχουν δεδομένα δίπλα στην επιλογή σας, αλλ<PERSON> δεν έχετε επαρκή δικαιώματα τροποποίησης αυτών των κελιών.<br>Θέλετε να συνεχίσετε με την τρέχουσα επιλογή;", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Προσαρμογή παρενθέσεων στο ύψος των ορισμάτων", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Στοίχιση πίνακα", "SSE.Controllers.DocumentHolder.txtNoChoices": "Δεν υπάρχουν επιλογές για το γέμισμα του κελιού.<br><PERSON><PERSON><PERSON><PERSON> τιμές κειμένου από την στήλη μπορούν να επιλεγούν για αντικατάσταση.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Δεν ξεκινά με", "SSE.Controllers.DocumentHolder.txtNotContains": "Δεν περιέχει", "SSE.Controllers.DocumentHolder.txtNotEnds": "Δεν τελειώνει με", "SSE.Controllers.DocumentHolder.txtNotEquals": "Δεν είναι ίσο με", "SSE.Controllers.DocumentHolder.txtOr": "ή", "SSE.Controllers.DocumentHolder.txtOverbar": "Μπάρα πάνω από κείμενο", "SSE.Controllers.DocumentHolder.txtPaste": "Επικόλληση", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Μαθημα<PERSON>ι<PERSON><PERSON>ς τύπος χωρίς περιγράμματα", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Τύπος + πλάτος στήλης", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Μορφοποίηση προορισμού", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Επικόλληση μορφοποίησης μόνο", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Τύπος + μορφή αριθμού", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Επικόλληση τύπου μόνο", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Τύπος + όλες οι μορφοποιήσεις", "SSE.Controllers.DocumentHolder.txtPasteLink": "Επικόλληση συνδέσμου", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Συνδεδεμένη εικόνα", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Συγχώνευση μορφοποίησης υπό όρους", "SSE.Controllers.DocumentHolder.txtPastePicture": "Εικόνα", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Μορφοποίηση πηγής", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Μετατόπιση", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Τιμή + όλες οι μορφοποιήσεις", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Τιμή + μορφή αριθμού", "SSE.Controllers.DocumentHolder.txtPasteValues": "Επικόλληση τιμής μόνο", "SSE.Controllers.DocumentHolder.txtPercent": "ποσοστό", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Επανάληψη αυτόματης επέκτασης πίνακα", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Αφαίρεση γραμμής κλάσματος", "SSE.Controllers.DocumentHolder.txtRemLimit": "Αφαίρεση ορίου", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Αφαίρεση τονισμένου χαρακτήρα", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Αφαίρεση μπάρας", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Θέλετε να αφαιρέσετε αυτή την υπογραφή;<br>Δεν μπορεί να αναιρεθεί.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Αφαίρεση δεσμών ενεργειών", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Αφαίρεση δείκτη", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Αφαίρεση εκθέτη", "SSE.Controllers.DocumentHolder.txtRowHeight": "Ύψος Γραμμής", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Δέσμες ενεργειών μετά το κείμενο", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Δέσμες ενεργειών πριν το κείμενο", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Εμφάνιση κάτω ορίου", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Εμφάνιση δεξιάς παρένθεσης", "SSE.Controllers.DocumentHolder.txtShowDegree": "Εμφάνιση πτυχίου", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Εμφάνιση αριστερής παρένθεσης", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Εμφάνιση δεσμευμένης θέσης", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Εμφάνιση πάνω ορίου", "SSE.Controllers.DocumentHolder.txtSorting": "Ταξινόμηση", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ταξινόμηση επιλεγμένων", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Έκταση παρενθέσεων", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Επίλεξε μόνο αυτή τη σειρά της καθορισμένης στήλης", "SSE.Controllers.DocumentHolder.txtTop": "Επάνω", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Επιστρέφει τις συνολικές σειρές για τον πίνακα ή για τις καθορισμένες στήλες του πίνακα", "SSE.Controllers.DocumentHolder.txtUnderbar": "Μπά<PERSON><PERSON> κάτω από κείμενο", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Αναίρεση αυτόματης έκτασης πίνακα", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Χρήση οδηγού εισαγωγής κειμένου", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Η συσκευή και τα δεδομένα σας μπορεί να κινδυνεύσουν αν κάνετε κλικ σε αυτόν τον σύνδεσμο.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "Όλα", "SSE.Controllers.FormulaDialog.sCategoryCube": "Κύβος", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Βάση δεδομένων", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Ημερομηνία και ώρα", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Μηχανική", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Χρηματοοικονομικά", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Πληροφορία", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 τελευταία χρησιμοποιημένες", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Λογική", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Αναζήτηση και παραπομπή", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Μαθηματι<PERSON><PERSON> και τριγωνομετρία", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Στατιστική", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Κείμενο και δεδομένα", "SSE.Controllers.LeftMenu.newDocumentTitle": "Λογιστικ<PERSON> φύλλο χωρίς όνομα", "SSE.Controllers.LeftMenu.textByColumns": "Κατά στήλες", "SSE.Controllers.LeftMenu.textByRows": "Κατά γραμμές", "SSE.Controllers.LeftMenu.textFormulas": "Τύποι", "SSE.Controllers.LeftMenu.textItemEntireCell": "Ολόκληρο το περιεχόμενου κελιού", "SSE.Controllers.LeftMenu.textLoadHistory": "Φόρτωση ιστορικού εκδόσεων...", "SSE.Controllers.LeftMenu.textLookin": "Αναζήτηση σε", "SSE.Controllers.LeftMenu.textNoTextFound": "Τα δεδομένα που αναζητάτε δεν βρέθηκαν. Παρακαλούμε προσαρμόστε τις επιλογές αναζήτησης.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Η αντικατάσταση έγινε. {0} εμφανίσεις προσπεράστηκαν.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Η αναζήτηση ολοκληρώθηκε. Εμφανίσεις που αντικαταστάθηκαν: {0}", "SSE.Controllers.LeftMenu.textSearch": "Αναζήτηση", "SSE.Controllers.LeftMenu.textSheet": "Φύλλο", "SSE.Controllers.LeftMenu.textValues": "Τιμές", "SSE.Controllers.LeftMenu.textWarning": "Προειδοποίηση", "SSE.Controllers.LeftMenu.textWithin": "Εντ<PERSON>ς", "SSE.Controllers.LeftMenu.textWorkbook": "Βιβλίο Εργασίας", "SSE.Controllers.LeftMenu.txtUntitled": "Άτιτλο", "SSE.Controllers.LeftMenu.warnDownloadAs": "Αν προχωρήσετε με την αποθήκευση σε αυτή τη μορφή, όλα τα χαρακτηριστικά πλην του κειμένου θα χαθούν.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Controllers.Main.confirmMoveCellRange": "Το εύρος κελιών προορισμού περιέχει δεδομένα. Να συνεχιστεί η λειτουργία;", "SSE.Controllers.Main.confirmPutMergeRange": "Τα δεδομένα προέλευσης περιείχαν συγχωνευμένα κελιά.<br>Αυτά διαιρέθηκαν πριν την επικόλλησή τους στον πίνακα.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Οι τύποι στη γραμμή κεφαλίδας θα αφαιρεθούν και θα μετατραπούν σε στατικό κείμενο.<br>Θέλετε να συνεχίσετε;", "SSE.Controllers.Main.convertationTimeoutText": "Υπέρβαση χρονικού ορίου μετατροπής.", "SSE.Controllers.Main.criticalErrorExtText": "Πατήστ<PERSON> \"Εντάξει\" για να επιστρέψετε στη λίστα εγγράφων.", "SSE.Controllers.Main.criticalErrorTitle": "Σφάλμα", "SSE.Controllers.Main.downloadErrorText": "Αποτυχία λήψης.", "SSE.Controllers.Main.downloadTextText": "Γίνεται λήψη λογιστικού φύλλου...", "SSE.Controllers.Main.downloadTitleText": "<PERSON>ήψη λογιστικού φύλλου", "SSE.Controllers.Main.errNoDuplicates": "Δεν βρέθηκαν διπλότυπες τιμές.", "SSE.Controllers.Main.errorAccessDeny": "Προσπαθείτε να εκτελέσετε μια ενέργεια για την οποία δεν έχετε δικαιώματα.<br>Παρακαλούμε να επικοινωνήστε με τον διαχειριστή του διακομιστή εγγράφων.", "SSE.Controllers.Main.errorArgsRange": "Υπάρχει σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται εσφαλμένο εύρος ορίσματος.", "SSE.Controllers.Main.errorAutoFilterChange": "Η λειτουργία δεν επιτρέπεται, καθ<PERSON><PERSON> επιχειρεί να μετατοπίσει κελιά σε έναν πίνακα στο φύλλο εργασίας σας.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Δεν ήταν δυνατή η πραγματοποίηση της λειτουργίας για τα επιλεγμένα κελιά, καθώς δεν μπορείτε να μετακινήσετε ένα μέρος του πίνακα.<br>Επιλέξτε μια άλλη περιοχή δεδομένων, ώστε ολόκληρος ο πίνακας να μετατοπιστεί και να δοκιμάσετε ξανά.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Δεν ήταν δυνατή η εκτέλεση της επιλεγμένης περιοχής κελιών.<br>Επιλέξτε ένα ομοιόμορφο εύρος δεδομένων διαφορετικό από το υπάρχον και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Δεν είναι δυνατή η εκτέλεση της λειτουργίας, επειδή η περιοχή περιέχει φιλτραρισμένα κελιά.<br>Παρακαλούμε εμφανίστε τα φιλτραρισμένα στοιχεία και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorBadImageUrl": "Εσφαλμένη διεύθυνση URL εικόνας", "SSE.Controllers.Main.errorCannotUngroup": "Δεν είναι δυνατή η αφαίρεση ομαδοποίησης. Για να ξεκινήσετε μια ομάδα, επιλέξτε τις γραμμές ή στήλες λεπτομερειών και ομαδοποιήστε τες. ", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Δεν μπορείτε να χρησιμοποιήσετε αυτή την εντολή σε προστατευμένο φύλλο. Για να χρησιμοποιήσετε αυτή την εντολή, αφαιρέστε την προστασία.<br>Εν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> να σας ζητηθεί να εισάγετε συνθηματικό.", "SSE.Controllers.Main.errorChangeArray": "Δεν μπορείτε να αλλάξετε μέρος ενός πίνακα.", "SSE.Controllers.Main.errorChangeFilteredRange": "Αυτό θα αλλάξει ένα φιλτραρισμένο εύρος στο φύλλο εργασίας.<br>Γ<PERSON>α να ολοκληρώσετε αυτή την εργασία, παρακαλούμε αφαιρέστε τα Αυτόματα Φίλτρα.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Το κελί ή γράφημα που προσπαθείτε να αλλάξετε βρίσκεται σε προστατευμένο φύλλο.<br>Γ<PERSON><PERSON> να κάνετε αλλαγή, αφαιρέστε την προστασία. Ίσως σας ζητηθεί συνθηματικό.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Η σύνδεση διακομιστή χάθηκε. Δεν είναι δυνατή η επεξεργασία του εγγράφου αυτήν τη στιγμή.", "SSE.Controllers.Main.errorConnectToServer": "Δεν ήταν δυνατή η αποθήκευση του εγγράφου. Παρακαλούμε ελέγξτε τις ρυθμίσεις σύνδεσης ή επικοινωνήστε με τον διαχειριστή σας.<br>Όταν κάνετε κλικ στο κουμπί «OK», θα σας ζητηθεί να πραγματοποιήσετε λήψη του εγγράφου.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Αυτή η εντολή δεν μπορεί να χρησιμοποιηθεί με πολλές επιλογές.<br>Επιλέξτε ένα εύρος και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorCountArg": "Υπάρχει σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται εσφαλμένος αριθμός ορισμάτων.", "SSE.Controllers.Main.errorCountArgExceed": "Υπάρχει σφάλμα καταχωρημένο τύπο.<br>Έγινε υπέρβαση του αριθμού των ορισμάτων.", "SSE.Controllers.Main.errorCreateDefName": "Δεν είναι δυνατή η επεξεργασία των υφιστάμενων επώνυμων ευρών και δεν είναι δυνατή η δημιουργία νέων<br>αυτ<PERSON><PERSON> τη στιγμή, καθ<PERSON><PERSON> ορισμένα από αυτά τελούν υπό επεξεργασία.", "SSE.Controllers.Main.errorDatabaseConnection": "Εξωτερι<PERSON><PERSON> σφάλμα.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σύνδεσης βάσης δεδομένων. Παρακαλούμε επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorDataEncrypted": "Οι κρυπτογραφημένες αλλαγ<PERSON>ς έχουν ληφθεί, δεν μπορούν να αποκρυπτογραφηθούν.", "SSE.Controllers.Main.errorDataRange": "Εσφαλμένο εύρος δεδομένων.", "SSE.Controllers.Main.errorDataValidate": "Η τιμή που εισαγάγατε δεν είναι έγκυρη.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> χρήστης έχει περιορίσει τις δυνατές τιμές σε αυτό το κελί.", "SSE.Controllers.Main.errorDefaultMessage": "Κωδικός σφάλματος: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Προσπαθείτε να διαγράψετε μια στήλη που περιέχει κλειδωμένο κελί. Τα κλειδωμένα κελιά δεν μπορούν να διαγραφούν όσο το φύλλο εργασίας προστατεύεται.<br>Για να διαγράψετε ένα κλειδωμένο κελί, αφαιρέστε την προστασία φύλλου. Ίσως σας ζητηθεί συνθηματικό.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Προσπαθείτε να διαγράψετε μια γραμμή που περιέχει κλειδωμένο κελί. Τα κλειδωμένα κελιά δεν μπορούν να διαγραφούν όσο το φύλλο εργασίας προστατεύεται.<br>Για να διαγράψετε ένα κλειδωμένο κελί, αφαιρέστε την προστασία φύλλου. Ίσως σας ζητηθεί συνθηματικό.", "SSE.Controllers.Main.errorEditingDownloadas": "Παρουσ<PERSON>ά<PERSON>τηκε σφάλμα κατά την εργασία με το έγγραφο.<br>Χρησιμοποιήστε την επιλογή «Λήψη ως» για να αποθηκεύσετε το αντίγραφο ασφαλείας στον σκληρό δίσκο του υπολογιστή σας.", "SSE.Controllers.Main.errorEditingSaveas": "Παρουσιάστηκε σφάλμα κατά την εργασία με το έγγραφο.<br>Χρησιμοποιήστε την επιλογή «Αποθήκευση ως...» για να αποθηκεύσετε το αντίγραφο ασφαλείας στον σκληρό δίσκο του υπολογιστή σας.", "SSE.Controllers.Main.errorEditView": "Η υφιστάμενη όψη φύλλου δεν μπορεί να τροποποιηθεί και οι νέες δεν μπορούν να δημιουργηθούν αυτή τη στιγμή καθώς κάποιες από αυτές τελούν υπό επεξεργασία.", "SSE.Controllers.Main.errorEmailClient": "Δε βρέθηκε καμιά εφαρμογή ηλεκτρονικού ταχυδρομείου.", "SSE.Controllers.Main.errorFilePassProtect": "Το αρχεί<PERSON> προστατεύεται με συνθηματικό και δεν μπορεί να ανοίξει.", "SSE.Controllers.Main.errorFileRequest": "Εξωτερι<PERSON><PERSON> σφάλμα.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αιτήματος αρχείου. Επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorFileSizeExceed": "Το μέγεθος του αρχείου υπερβαίνει το όριο που έχει οριστεί για τον διακομιστή σας.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων για λεπτομέρειες.", "SSE.Controllers.Main.errorFileVKey": "Εξωτερικ<PERSON> σφάλμα.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νο κλειδί ασφαλείας. Παρακαλούμε επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorFillRange": "Δεν ήταν δυνατή η συμπλήρωση του επιλεγμένου εύρους κελιών.<br>Όλα τα συγχωνευμένα κελιά πρέπει να έχουν το ίδιο μέγεθος.", "SSE.Controllers.Main.errorForceSave": "Παρουσ<PERSON><PERSON><PERSON><PERSON>ηκε σφάλμα κατά την αποθήκευση του αρχείου. Χρησιμοποιήστε την επιλογή «Λήψη ως» για να αποθηκεύσετε το αρχείο στον σκληρό δίσκο του υπολογιστή σας ή δοκιμάστε ξανά αργότερα.", "SSE.Controllers.Main.errorFormulaName": "Υπάρχει σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται εσφαλμένο όνομα τύπου.", "SSE.Controllers.Main.errorFormulaParsing": "Εσωτερι<PERSON><PERSON> σφάλμα κατά την ανάλυση του τύπου.", "SSE.Controllers.Main.errorFrmlMaxLength": "Ο μαθηματικός τύπος ξεπερνά το όριο των 8192 χαρακτήρων.<br>Παρακαλούμε επεξεργαστείτε τον και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorFrmlMaxReference": "Δεν μπορείτε να εισαγάγετε αυτόν τον τύπο επειδή έχει πάρα πολλές τιμές,<br>αναφορές κελιού ή/και ονόματα.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Οι τιμές κειμένου στους τύπους περιορίζονται σε 255 χαρακτήρες.<br>Χρησιμοποιήστε τη συνάρτηση CONCATENATE ή τον τελεστή συνένωσης (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Η συνάρτηση αναφέρεται σε ένα φύλλο που δεν υπάρχει.<br>Παρακαλούμε ελέγξτε τα δεδομένα και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br>Ε<PERSON><PERSON>λ<PERSON>ξτε ένα εύρος ώστε η πρώτη γραμμή του πίνακα να είναι στην ίδια γραμμή<br>και ο παραγόμενος πίνακας να επικαλύπτει τον τρέχοντα.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ένα εύρος που δεν περιλαμβάνει άλλους πίνακες.", "SSE.Controllers.Main.errorInvalidRef": "Εισάγετε ένα σωστό όνομα για την επιλογή ή μια έγκυρη αναφορά για να μεταβείτε.", "SSE.Controllers.Main.errorKeyEncrypt": "Άγνωστος περιγραφέας κλειδιού", "SSE.Controllers.Main.errorKeyExpire": "Ο περιγραφέας κλειδιού έχει λήξει", "SSE.Controllers.Main.errorLabledColumnsPivot": "Για να δημιουργηθεί συγκεντρωτικ<PERSON>ς πίνακας, χρησιμοποιήστε δεδομένα οργανωμένα ως λίστα στηλών με ετικέτες.", "SSE.Controllers.Main.errorLoadingFont": "Οι γραμματοσειρές δεν έχουν φορτωθεί.<br>Παρα<PERSON><PERSON>λ<PERSON>ύμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων σας.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Η αναφορά προορισμού ή εύρους δεδομένων δεν είναι έγκυρη.", "SSE.Controllers.Main.errorLockedAll": "Η λειτουργία δεν μπόρεσε να γίνει καθώς το φύλλο έχει κλειδωθεί από άλλο χρήστη.", "SSE.Controllers.Main.errorLockedCellPivot": "Δεν είναι δυνατή η τροποποίηση δεδομένων εντός ενός συγκεντρωτικού πίνακα", "SSE.Controllers.Main.errorLockedWorksheetRename": "Το φύλλο δεν μπορεί να μετονομαστεί προς το παρόν καθώς μετονομάζεται από άλλο χρήστη", "SSE.Controllers.Main.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Controllers.Main.errorMoveRange": "Αδυναμία αλλαγής μέρους ενός συγχωνευμένου κελιού", "SSE.Controllers.Main.errorMoveSlicerError": "Οι αναλυτές πίνακα δεν μπορούν να αντιγραφούν από ένα βιβλίο εργασίας σε άλλο.<br>Προσπαθήστε ξανά επιλέγοντας ολόκληρο τον πίνακα και τους αναλυτές.", "SSE.Controllers.Main.errorMultiCellFormula": "Οι τύποι συστοιχιών πολλών κελιών δεν επιτρέπονται στους πίνακες.", "SSE.Controllers.Main.errorNoDataToParse": "Δεν επιλέχτηκαν δεδομένα για επεξεργασία.", "SSE.Controllers.Main.errorOpenWarning": "Το μήκος ενός από τους τύπους στο αρχείο ξεπέρασε<br>τον επιτρεπόμενο αριθμό 8192 χαρακτήρων και αφαιρέθηκε.", "SSE.Controllers.Main.errorOperandExpected": "Η σύνταξη της εισαγόμενης συνάρτησης δεν είναι σωστή. Παρακαλούμε ελέγξτε αν λείπει μία από τις παρενθέσεις - «(» ή «)».", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Το συνθηματικό που βάλατε δεν είναι σωστό.<br>Βεβαιωθείτε ότι το πλήκτρο CAPS LOCK είναι ανενεργό και ότι βάζετε κεφαλαία όπου χρειάζεται.", "SSE.Controllers.Main.errorPasteMaxRange": "Η περιοχή αντιγραφής και επικόλλησης δεν ταιριάζει.<br>Παρακαλούμε επιλέξτε μια περιοχή με το ίδιο μέγεθος ή κάντε κλικ στο πρώτο κελί της σειράς για να επικολλήσετε τα αντιγραμμένα κελιά.", "SSE.Controllers.Main.errorPasteMultiSelect": "Αυτή η ενέργεια δεν μπορεί να γίνει σε μια επιλογή πολλαπλών εύρους.<br>Επιλέξτε ένα μόνο εύρος και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorPasteSlicerError": "Οι αναλυτές πίνακα δεν μπορούν να αντιγραφούν από ένα βιβλίο εργασίας σε άλλο.", "SSE.Controllers.Main.errorPivotGroup": "Δεν είναι δυνατή η ομαδοποίηση αυτής της επιλογής.", "SSE.Controllers.Main.errorPivotOverlap": "Μια αναφορ<PERSON> συγκεντρωτικού πίνακα δεν μπορεί να επικαλύπτει έναν πίνακα.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Η αναφορά Συγκεντρωτικού Πίνακα αποθηκεύτηκε χωρίς τα υποκείμενα δεδομένα.<br>Χρησιμοποιήστε το κουμπί \"Ανανέωση\" για να ενημερώσετε την αναφορά.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Δυστυχ<PERSON><PERSON>, δεν είναι δυνατή η εκτύπωση περισσότερων από 1500 σελίδων ταυτόχρονα στην τρέχουσα έκδοση του προγράμματος.<br><PERSON><PERSON><PERSON><PERSON><PERSON> ο περιορισμός θα καταργηθεί στις επερχόμενες εκδόσεις.", "SSE.Controllers.Main.errorProcessSaveResult": "Αποτυχία αποθήκευσης", "SSE.Controllers.Main.errorServerVersion": "Αναβαθμίστηκε η έκδοση του συντάκτη. Η σελίδα θα φορτωθεί ξανά για να εφαρμοστούν οι αλλαγές.", "SSE.Controllers.Main.errorSessionAbsolute": "Η περίοδος επεξεργασίας εγγράφων έχει λήξει. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.errorSessionIdle": "Το έγγραφο δεν έχει επεξεργα<PERSON>τε<PERSON> εδώ και πολύ ώρα. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.errorSessionToken": "Η σύνδεση με το διακομιστή έχει διακοπεί. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.errorSetPassword": "Δεν ήταν δυνα<PERSON><PERSON>ς ο ορισμός του συνθηματικού.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Η αναφορά θέσης δεν είναι έγκυρη επειδή τα κελιά δε βρίσκονται όλα στην ίδια γραμμή ή στήλη.<br>Επιλέξτε κελιά που βρίσκονται όλα σε μια γραμμή ή στήλη.", "SSE.Controllers.Main.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Controllers.Main.errorToken": "Το κλειδί ασφαλείας του εγγράφου δεν είναι σωστά σχηματισμένο.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων.", "SSE.Controllers.Main.errorTokenExpire": "Το κλειδί ασφαλείας του εγγράφου έληξε.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων.", "SSE.Controllers.Main.errorUnexpectedGuid": "Εξωτερικ<PERSON> σφάλμα.<br>Μη αναμενόμενο GUID. Παρακαλούμε επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorUpdateVersion": "Η έκδοση του αρχείου έχει αλλάξει. Η σελίδα θα φορτωθεί ξανά.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Η σύνδεση στο Διαδίκτυο έχει αποκατασταθεί και η έκδοση του αρχείου έχει αλλάξει.<br>Προτ<PERSON>ύ συνεχίσετε να εργάζεστε, πρέπει να κατεβάσετε το αρχείο ή να αντιγράψετε το περιεχόμενό του για να βεβαιωθείτε ότι δεν έχει χαθεί τίποτα και, στη συνέχεια, φορτώστε ξανά αυτήν τη σελίδα.", "SSE.Controllers.Main.errorUserDrop": "Δεν είναι δυνατή η πρόσβαση στο αρχείο αυτή τη στιγμή.", "SSE.Controllers.Main.errorUsersExceed": "Υπέρβαση του αριθμού των χρηστών που επιτρέπονται από το πρόγραμμα τιμολόγησης", "SSE.Controllers.Main.errorViewerDisconnect": "Η σύνδεση χάθηκε. Μπορείτε να συνεχίσετε να βλέπετε το έγγραφο,<br>αλλά δεν θα μπορείτε να το λάβετε ή να το εκτυπώσετε έως ότου αποκατασταθεί η σύνδεση και ανανεωθεί η σελίδα.", "SSE.Controllers.Main.errorWrongBracketsCount": "Σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται λανθασμένος αριθμός αγκυλών.", "SSE.Controllers.Main.errorWrongOperator": "Υπάρχει σφάλμα στον καταχωρημένο τύπο. Χρησιμοποιείται λανθασμένος τελεστής.<br>Παρακαλούμε διορθώστε το σφάλμα.", "SSE.Controllers.Main.errorWrongPassword": "Το συνθηματικό που βάλατε δεν είναι σωστό.", "SSE.Controllers.Main.errRemDuplicates": "Διπλότυπες τιμές που βρέθηκαν και διαγράφηκαν: {0}, μοναδικές τιμές που απέμειναν: {1}.", "SSE.Controllers.Main.leavePageText": "Έχετε μη αποθηκευμένες αλλαγές στο λογιστικό φύλλο. Πατήστε 'Παραμονή στη Σελίδα' και μετά 'Αποθήκευση' για να τις αποθηκεύσετε. Πατήστε 'Έξοδος από τη Σελίδα' για να απορρίψετε όλες τις μη αποθηκευμένες αλλαγές.", "SSE.Controllers.Main.leavePageTextOnClose": "Όλες οι μη αποθηκευμένες αλλαγές στο λογιστικό φύλλο θα χαθούν.<br>Πατή<PERSON>τ<PERSON> \"Ακύρωση\" και μετά \"Αποθήκευση\" για να τις αποθηκεύσετε. Πατήστε \"Εντάξει\" για να τις απορρίψετε.", "SSE.Controllers.Main.loadFontsTextText": "Φόρτωση δεδομένων...", "SSE.Controllers.Main.loadFontsTitleText": "Φόρτωση Δεδομένων", "SSE.Controllers.Main.loadFontTextText": "Φόρτωση δεδομένων...", "SSE.Controllers.Main.loadFontTitleText": "Φόρτωση Δεδομένων", "SSE.Controllers.Main.loadImagesTextText": "Φόρτωση εικόνων...", "SSE.Controllers.Main.loadImagesTitleText": "Φόρτωση Εικόνων", "SSE.Controllers.Main.loadImageTextText": "Φόρτωση εικόνας...", "SSE.Controllers.Main.loadImageTitleText": "Φόρτωση Εικόνας", "SSE.Controllers.Main.loadingDocumentTitleText": "Φόρτωση λογιστικού φύλλου", "SSE.Controllers.Main.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Controllers.Main.openErrorText": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά το άνοιγμα του αρχείου.", "SSE.Controllers.Main.openTextText": "Άνοιγμα υπολογιστικού φύλλου...", "SSE.Controllers.Main.openTitleText": "Άνοιγμα Υπολογιστικού Φύλλου", "SSE.Controllers.Main.pastInMergeAreaError": "Αδυναμία αλλαγής μέρους ενός συγχωνευμένου κελιού", "SSE.Controllers.Main.printTextText": "Εκτύπωση υπολογιστικού φύλλου...", "SSE.Controllers.Main.printTitleText": "Εκτύπωση Υπολογιστικού Φύλλου", "SSE.Controllers.Main.reloadButtonText": "Επαναφόρτωση Σελίδας", "SSE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON><PERSON><PERSON><PERSON>ος επεξ<PERSON><PERSON>γ<PERSON>ζ<PERSON><PERSON>α<PERSON> αυτό το έγγραφο αυτήν τη στιγμή. Παρακαλούμε δοκιμάστε ξανά αργότερα.", "SSE.Controllers.Main.requestEditFailedTitleText": "Δεν επιτρέπεται η πρόσβαση", "SSE.Controllers.Main.saveErrorText": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON>ε σφάλμα κατά την αποθήκευση του αρχείου.", "SSE.Controllers.Main.saveErrorTextDesktop": "Δεν είναι δυνατή η αποθήκευση ή η δημιουργία αυτού του αρχείου.<br>Πιθανοί λόγοι είναι:<br>1. Το αρχείο είναι μόνο για ανάγνωση.<br>2. Το αρχείο τελεί υπό επεξεργασία από άλλους χρήστες.<br>3. Ο δίσκος είναι γεμάτος ή κατεστραμμένος.", "SSE.Controllers.Main.saveTextText": "Γίνεται αποθήκευση λογιστικού φύλλου...", "SSE.Controllers.Main.saveTitleText": "Αποθήκευση Λογιστικού Φύλλου", "SSE.Controllers.Main.scriptLoadError": "Η σύνδεση είναι πολύ αργή, δεν ήταν δυνατή η φόρτωση ορισμένων στοιχείων. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.textAnonymous": "Ανώνυμος", "SSE.Controllers.Main.textApplyAll": "Εφαρμογή σε όλες τις εξισώσεις", "SSE.Controllers.Main.textBuyNow": "Επισκεφθείτε την ιστοσελίδα", "SSE.Controllers.Main.textChangesSaved": "Όλες οι αλλαγές αποθηκεύτηκαν", "SSE.Controllers.Main.textClose": "Κλείσιμο", "SSE.Controllers.Main.textCloseTip": "Κάντε κλικ για να κλείσει η υπόδειξη", "SSE.Controllers.Main.textConfirm": "Επιβεβαίωση", "SSE.Controllers.Main.textContactUs": "Επικοινωνήστε με το τμήμα πωλήσεων", "SSE.Controllers.Main.textConvertEquation": "Η εξίσωση αυτή δημιουργήθηκε με παλαιότερη έκδοση του συντάκτη εξισώσεων που δεν υποστηρίζεται πια. Για να την επεξεργαστείτε, μετατρέψτε την σε μορφή Office Math ML.<br>Να μετατραπεί τώρα;", "SSE.Controllers.Main.textCustomLoader": "Παρακαλούμε λάβετε υπόψη ότι σύμφωνα με τους όρους της άδειας δεν δικαιούστε αλλαγή του φορτωτή.<br>Π<PERSON><PERSON><PERSON><PERSON><PERSON>λούμε επικοινωνήστε με το Τμήμα Πωλήσεων για να λάβετε μια προσφορά.", "SSE.Controllers.Main.textDisconnect": "Η σύνδεση χάθηκε", "SSE.Controllers.Main.textFillOtherRows": "Γέμισμα υπόλοιπων γραμμών", "SSE.Controllers.Main.textFormulaFilledAllRows": "Ο μαθηματικός τύπος γέμισε {0} γραμμές με δεδομένα. Το γέμισμα των υπόλοιπων γραμμών ίσως χρειαστεί μερικά λεπτά.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Ο μαθηματικός τύπος γέμισε τις {0} πρώτες γραμμές. Το γέμισμα των υπόλοιπων άδειων γραμμών ίσως χρειαστεί μερικά λεπτά.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Ο μαθηματικός τύπος γέμισε μόνο τις {0} πρώτες γραμμές με δεδομένα για λόγους εξοικονόμησης μνήμης. Υπάρχουν άλλες {1} γραμμές με δεδομένα. Μπορείτε να τις γεμίσετε χειρονακτικά.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Ο μαθηματικός τύπος γέμισε μόνο τις {0} πρώτες γραμμές για λόγους εξοικονόμησης μνήμης. Οι υπόλοιπες γραμμές αυτού του φύλλου δεν περιέχουν δεδομένα.", "SSE.Controllers.Main.textGuest": "Επισκέπτης", "SSE.Controllers.Main.textHasMacros": "Το αρχείο περιέχει αυτόματες μακροεντολές.<br>Θέλετε να εκτελέσετε μακροεντολές;", "SSE.Controllers.Main.textLearnMore": "Μάθετε Περισσότερα", "SSE.Controllers.Main.textLoadingDocument": "Φόρτωση λογιστικού φύλλου", "SSE.Controllers.Main.textLongName": "Εισάγετε ένα όνομα μικρότερο από 128 χαρακτήρες.", "SSE.Controllers.Main.textNeedSynchronize": "Έχετε ενημερώσεις", "SSE.Controllers.Main.textNo": "Όχι", "SSE.Controllers.Main.textNoLicenseTitle": "Το όριο της άδειας χρήσης έχει εξαντληθεί.", "SSE.Controllers.Main.textPaidFeature": "Δυνατότητα επί πληρωμή", "SSE.Controllers.Main.textPleaseWait": "Η λειτουργία ίσως χρειαστεί περισσότερο χρόνο από τον αναμενόμενο. Παρακαλούμε περιμένετε...", "SSE.Controllers.Main.textReconnect": "Η σύνδεση αποκαταστάθηκε", "SSE.Controllers.Main.textRemember": "Να θυμάσαι την επιλογή μου για όλα τα αρχεία", "SSE.Controllers.Main.textRememberMacros": "Απομνημόνευση της επιλογής μου για όλες τις μακροεντολές", "SSE.Controllers.Main.textRenameError": "Το όνομα χρήστη δεν μπορεί να είναι κενό.", "SSE.Controllers.Main.textRenameLabel": "Εισάγετε ένα όνομα για συνεργατική χρήση", "SSE.Controllers.Main.textRequestMacros": "Μια μακροεντολή κάνει ένα αίτημα σε διεύθυνση URL. Θέλετε να επιτρέψετε το αίτημα στο %1;", "SSE.Controllers.Main.textShape": "Σχήμα", "SSE.Controllers.Main.textStrict": "Αυστηρή κατάσταση", "SSE.Controllers.Main.textText": "Κείμενο", "SSE.Controllers.Main.textTryUndoRedo": "Οι λειτουργίες Αναίρεση/Επανάληψη είναι απενεργοποιημένες στην κατάσταση Γρήγορης συν-επεξεργασίας.<br>Κάντε κλικ στο κουμπί 'Αυστηρή κατάσταση' για να μεταβείτε στην Αυστηρή κατάσταση συν-επεξεργασίας όπου επεξεργάζεστε το αρχείο χωρίς παρέμβαση άλλων χρηστών και στέλνετε τις αλλαγές σας αφού τις αποθηκεύσετε. Η μετάβαση μεταξύ των δύο καταστάσεων γίνεται μέσω των Προηγμένων Ρυθμίσεων.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Οι λειτουργ<PERSON>ες Αναίρεση/Επανάληψη είναι απενεργοποιημένες στην κατάσταση Γρήγορης συν-επεξεργασίας.", "SSE.Controllers.Main.textYes": "Ναι", "SSE.Controllers.Main.titleLicenseExp": "Η άδεια έληξε", "SSE.Controllers.Main.titleServerVersion": "Ο επεξεργαστής ενημερώθηκε", "SSE.Controllers.Main.txtAccent": "Τόνος", "SSE.Controllers.Main.txtAll": "(Όλα)", "SSE.Controllers.Main.txtArt": "Το κείμενό σας εδώ", "SSE.Controllers.Main.txtBasicShapes": "Βασικά <PERSON>χήματα", "SSE.Controllers.Main.txtBlank": "(κενό)", "SSE.Controllers.Main.txtButtons": "Κουμπιά", "SSE.Controllers.Main.txtByField": "%1 από %2", "SSE.Controllers.Main.txtCallouts": "Επεξηγήσεις", "SSE.Controllers.Main.txtCharts": "Γραφήματα", "SSE.Controllers.Main.txtClearFilter": "Εκκαθάριση Φίλτρου", "SSE.Controllers.Main.txtColLbls": "Ετικέτε<PERSON>", "SSE.Controllers.Main.txtColumn": "Στήλη", "SSE.Controllers.Main.txtConfidential": "Εμπιστευτικό", "SSE.Controllers.Main.txtDate": "Ημερομηνία", "SSE.Controllers.Main.txtDays": "Ημέρες", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>τος", "SSE.Controllers.Main.txtEditingMode": "Ορισμός λειτουργίας επεξεργασίας...", "SSE.Controllers.Main.txtErrorLoadHistory": "Η φόρτωση ιστορικού απέτυχε", "SSE.Controllers.Main.txtFiguredArrows": "Σχηματικ<PERSON> Βέλη", "SSE.Controllers.Main.txtFile": "Αρχείο", "SSE.Controllers.Main.txtGrandTotal": "Τελικό Σύνολο", "SSE.Controllers.Main.txtGroup": "Ομάδα", "SSE.Controllers.Main.txtHours": "Ώρες", "SSE.Controllers.Main.txtLines": "Γραμμές", "SSE.Controllers.Main.txtMath": "Μαθηματικά", "SSE.Controllers.Main.txtMinutes": "Λεπτά", "SSE.Controllers.Main.txtMonths": "Μήνες", "SSE.Controllers.Main.txtMultiSelect": "Πολλαπλή Επιλογή", "SSE.Controllers.Main.txtOr": "%1 ή %2", "SSE.Controllers.Main.txtPage": "Σελίδα", "SSE.Controllers.Main.txtPageOf": "Σελίδα %1 από %2", "SSE.Controllers.Main.txtPages": "Σελίδες", "SSE.Controllers.Main.txtPreparedBy": "Ετοιμάστηκε από", "SSE.Controllers.Main.txtPrintArea": "Εκτυπώσιμη_Περιοχή", "SSE.Controllers.Main.txtQuarter": "Τέταρτο", "SSE.Controllers.Main.txtQuarters": "Τετράμηνα", "SSE.Controllers.Main.txtRectangles": "Ορθογώνια Παραλληλόγραμμα", "SSE.Controllers.Main.txtRow": "Γραμμή", "SSE.Controllers.Main.txtRowLbls": "Ετικέτες <PERSON>μ<PERSON>ν", "SSE.Controllers.Main.txtSeconds": "Δευτερόλεπτα", "SSE.Controllers.Main.txtSeries": "Σειρά", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Επεξήγηση με Γραμμή 1 (Περίγραμμα και Μπάρα)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Επεξήγηση με Γραμμή 2 (Περίγραμμα και Μπάρα)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Επεξήγηση με Γραμμή 3 (Περίγραμμα και Μπάρα)", "SSE.Controllers.Main.txtShape_accentCallout1": "Επεξήγηση με Γραμμή 1 (Μπάρα)", "SSE.Controllers.Main.txtShape_accentCallout2": "Επεξήγηση με Γραμμή 2 (Μπάρ<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "Επεξήγηση με Γραμμή 3 (Μπάρα)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Κουμπί Πίσω ή Προηγούμενο", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Κουμπί Αρχής", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Κενό Κουμπί", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Κουμπί Εγγράφου", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Κουμπί Τέλους", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Κουμπί Μπροστά ή Επόμενο", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Κουμπί Βοήθειας", "SSE.Controllers.Main.txtShape_actionButtonHome": "Κουμπί Αρχικής", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Κουμπί Πληροφορίας", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Κουμπί Ταινίας", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Κουμπί Επιστροφής", "SSE.Controllers.Main.txtShape_actionButtonSound": "Κουμπί Ήχου", "SSE.Controllers.Main.txtShape_arc": "Κυκλικό Τόξο", "SSE.Controllers.Main.txtShape_bentArrow": "Λυγισ<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Αρθρωτός <PERSON>ύνδεσμος", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Αρθρωτ<PERSON><PERSON>ύνδεσμος με Βέλος", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Αρθρωτ<PERSON><PERSON>ύνδεσμος με Διπλό Βέλος", "SSE.Controllers.Main.txtShape_bentUpArrow": "Λυγισμ<PERSON><PERSON><PERSON> Βέλος", "SSE.Controllers.Main.txtShape_bevel": "Πλάγια Τομή", "SSE.Controllers.Main.txtShape_blockArc": "Πλα<PERSON><PERSON><PERSON><PERSON>ου", "SSE.Controllers.Main.txtShape_borderCallout1": "Επεξήγηση με Γραμμή 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Επεξήγηση με Γραμμή 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Επεξήγηση με Γραμμή 3", "SSE.Controllers.Main.txtShape_bracePair": "Διπλό Άγκιστρο", "SSE.Controllers.Main.txtShape_callout1": "Επεξήγηση με Γραμμή 1 (<PERSON><PERSON><PERSON><PERSON><PERSON>ρίγραμμα)", "SSE.Controllers.Main.txtShape_callout2": "Επεξήγηση με Γραμμή 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>ίγραμμα)", "SSE.Controllers.Main.txtShape_callout3": "Επεξήγηση με Γραμμή 3 (<PERSON><PERSON><PERSON><PERSON><PERSON>ρίγραμμα)", "SSE.Controllers.Main.txtShape_can": "Κύλινδρος", "SSE.Controllers.Main.txtShape_chevron": "Σιρίτι", "SSE.Controllers.Main.txtShape_chord": "Χορ<PERSON><PERSON> Κύκλου", "SSE.Controllers.Main.txtShape_circularArrow": "Κυκλικ<PERSON>έλ<PERSON>", "SSE.Controllers.Main.txtShape_cloud": "Σύννεφο", "SSE.Controllers.Main.txtShape_cloudCallout": "Επεξήγηση σε Σύννεφο", "SSE.Controllers.Main.txtShape_corner": "Γωνία", "SSE.Controllers.Main.txtShape_cube": "Κύβος", "SSE.Controllers.Main.txtShape_curvedConnector3": "Καμπυλω<PERSON><PERSON>ς <PERSON>ύνδεσμος", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Καμπυ<PERSON>ω<PERSON><PERSON><PERSON> Σύνδεσμος με Βέλος", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Καμπυλω<PERSON><PERSON><PERSON> Σύνδεσμος με Διπλό Βέλος", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Καμπυλωτ<PERSON> Κάτω Βέλος", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Καμπυλωτό Αριστερό Βέλος", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Καμπυλωτό Δεξί Βέλος", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Καμπυλωτό Πάνω Βέλος", "SSE.Controllers.Main.txtShape_decagon": "Δεκάγωνο", "SSE.Controllers.Main.txtShape_diagStripe": "Διαγώνια Λωρίδα", "SSE.Controllers.Main.txtShape_diamond": "Ρόμβος", "SSE.Controllers.Main.txtShape_dodecagon": "Δωδεκάγωνο", "SSE.Controllers.Main.txtShape_donut": "Ντ<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "Διπλό Κύμα", "SSE.Controllers.Main.txtShape_downArrow": "Κάτω Βέλος", "SSE.Controllers.Main.txtShape_downArrowCallout": "Επεξήγηση με Κάτω Βέλος", "SSE.Controllers.Main.txtShape_ellipse": "Έλλειψη", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Καμπυλωτ<PERSON> Κ<PERSON>τω <PERSON>δ<PERSON>λα", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Καμπυλωτή Κορδέλα Πάνω", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Διάγραμμα Ροής: Εναλλακτική Διεργασία", "SSE.Controllers.Main.txtShape_flowChartCollate": "Διάγραμμα Ροής: Τοποθέτηση σε Σειρά", "SSE.Controllers.Main.txtShape_flowChartConnector": "Διάγραμμα Ροής: Σύνδεσμος", "SSE.Controllers.Main.txtShape_flowChartDecision": "Διάγραμμα Ροής: Απόφαση", "SSE.Controllers.Main.txtShape_flowChartDelay": "Διάγραμμα Ροής: Καθυστέρηση", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Διάγραμμα Ροής: Προβολή", "SSE.Controllers.Main.txtShape_flowChartDocument": "Διάγραμμα Ροής: Έγγραφο", "SSE.Controllers.Main.txtShape_flowChartExtract": "Διάγραμμα Ροής: Εξαγωγή", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Διάγραμμα Ροής: Δεδομένα", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Διάγραμμα Ροής: Εσωτε<PERSON>ικ<PERSON>ς Αποθηκευτικ<PERSON>ς Χώρος", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Διάγραμμα Ροής: Μαγνητικ<PERSON>ς Δίσκος", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Διάγραμμα Ροής: Αποθηκευτικό Μέσο Άμεσης Πρόσβασης", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Διάγραμμα Ροής: Αποθηκευτικό Μέσο Σειριακής Προσπέλασης", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Διάγραμμα Ροής: Χ<PERSON><PERSON>ροκίνητη Εισαγωγή", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Διάγραμμα Ροής: Χ<PERSON><PERSON>ροκίνητη Λειτουργία", "SSE.Controllers.Main.txtShape_flowChartMerge": "Διάγραμμα Ροής: Συγχώνευση", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Διάγραμμα Ροής: Πολλαπλό Έγγραφο", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Διάγραμμα Ροής: Σύνδεσμος Εκτός Σελίδας", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Διάγραμμα Ροής: Αποθηκευμένα Δεδομένα", "SSE.Controllers.Main.txtShape_flowChartOr": "Διάγραμμα Ροής: Ή", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Διάγραμμα Ροής: Προκαθορισμένη Διεργασία", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Διάγραμμα Ροής: Προετοιμασία", "SSE.Controllers.Main.txtShape_flowChartProcess": "Διάγραμμα Ροής: Διεργασία", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Διάγραμμα Ροής: Κάρτα", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Διάγραμμα Ροής: Διάτρητη Ταινία", "SSE.Controllers.Main.txtShape_flowChartSort": "Διάγραμμα Ροής: Ταξινόμηση", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Διάγραμμα Ροής: Συμβολή", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Διάγραμμα Ροής: Τερματισμός", "SSE.Controllers.Main.txtShape_foldedCorner": "Διπλωμένη Γωνία", "SSE.Controllers.Main.txtShape_frame": "Πλαίσιο", "SSE.Controllers.Main.txtShape_halfFrame": "Μισό Πλαίσιο", "SSE.Controllers.Main.txtShape_heart": "Καρδιά", "SSE.Controllers.Main.txtShape_heptagon": "Επτάγωνο", "SSE.Controllers.Main.txtShape_hexagon": "Εξάγωνο", "SSE.Controllers.Main.txtShape_homePlate": "Πεντάγωνο", "SSE.Controllers.Main.txtShape_horizontalScroll": "Οριζόντια Κύλιση", "SSE.Controllers.Main.txtShape_irregularSeal1": "Έκρηξη 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Έκρηξη 2", "SSE.Controllers.Main.txtShape_leftArrow": "Αριστερ<PERSON>έλ<PERSON>", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Επεξήγηση με Αριστερό Βέλος", "SSE.Controllers.Main.txtShape_leftBrace": "Αριστερό Άγκιστρο", "SSE.Controllers.Main.txtShape_leftBracket": "Αριστερή Παρένθεση", "SSE.Controllers.Main.txtShape_leftRightArrow": "Αριστερό Δεξιό Βέλος", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Επεξήγηση με Αριστερό Δεξιό Βέλος", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Αριστερό Δεξιό Πάνω Βέλος", "SSE.Controllers.Main.txtShape_leftUpArrow": "Αριστερ<PERSON> Πάνω Βέλος", "SSE.Controllers.Main.txtShape_lightningBolt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "SSE.Controllers.Main.txtShape_line": "Γραμμή", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Διπλ<PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "Δια", "SSE.Controllers.Main.txtShape_mathEqual": "Ίσον", "SSE.Controllers.Main.txtShape_mathMinus": "Πλην", "SSE.Controllers.Main.txtShape_mathMultiply": "Επί", "SSE.Controllers.Main.txtShape_mathNotEqual": "Διάφορο", "SSE.Controllers.Main.txtShape_mathPlus": "Συν", "SSE.Controllers.Main.txtShape_moon": "Φεγγάρι", "SSE.Controllers.Main.txtShape_noSmoking": "Σύμβολο \"Όχι\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Δεξί Βέλος με Εγκοπή", "SSE.Controllers.Main.txtShape_octagon": "Οκτάγωνο", "SSE.Controllers.Main.txtShape_parallelogram": "Παραλληλόγραμμο", "SSE.Controllers.Main.txtShape_pentagon": "Πεντάγωνο", "SSE.Controllers.Main.txtShape_pie": "Πίτα", "SSE.Controllers.Main.txtShape_plaque": "Σύμβολο", "SSE.Controllers.Main.txtShape_plus": "Συν", "SSE.Controllers.Main.txtShape_polyline1": "Μουντζού<PERSON>α", "SSE.Controllers.Main.txtShape_polyline2": "Ελεύθερο Σχέδιο", "SSE.Controllers.Main.txtShape_quadArrow": "Τετραπλ<PERSON> Βέλος", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Επεξήγηση Τετραπλού Βέλους", "SSE.Controllers.Main.txtShape_rect": "Ορθ<PERSON>γ<PERSON><PERSON><PERSON><PERSON>αλληλόγραμμο", "SSE.Controllers.Main.txtShape_ribbon": "Κάτ<PERSON>λα", "SSE.Controllers.Main.txtShape_ribbon2": "Πάνω <PERSON>δ<PERSON>λα", "SSE.Controllers.Main.txtShape_rightArrow": "Δεξιό Βέλος", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Επεξήγηση με Δεξιό Βέλος", "SSE.Controllers.Main.txtShape_rightBrace": "Δεξιό Άγκιστρο", "SSE.Controllers.Main.txtShape_rightBracket": "Δεξιά Παρένθεση", "SSE.Controllers.Main.txtShape_round1Rect": "Με Στρογγυλεμένη Γωνία", "SSE.Controllers.Main.txtShape_round2DiagRect": "Με Διαγώνιες Στρογγυλεμένες Γω<PERSON>ς", "SSE.Controllers.Main.txtShape_round2SameRect": "Με Στρογγυλεμένες Γωνίες στην Ίδια Πλευρά", "SSE.Controllers.Main.txtShape_roundRect": "Με Στρογγυλεμένες Γωνίες", "SSE.Controllers.Main.txtShape_rtTriangle": "Ορθογώνιο Τρίγωνο", "SSE.Controllers.Main.txtShape_smileyFace": "Χαμογ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Φατσούλα", "SSE.Controllers.Main.txtShape_snip1Rect": "Με Ψαλιδισμένη Γωνία", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Με Διαγώνιες Ψαλιδισμένες <PERSON>ω<PERSON>ς", "SSE.Controllers.Main.txtShape_snip2SameRect": "Με Ψαλιδισμένες Γωνίες στην Ίδια Πλευρά", "SSE.Controllers.Main.txtShape_snipRoundRect": "Με Στρογγυλεμένη και Ψαλιδισμένη Γωνία", "SSE.Controllers.Main.txtShape_spline": "Καμπύλη", "SSE.Controllers.Main.txtShape_star10": "Αστέρι 10 Σημείων", "SSE.Controllers.Main.txtShape_star12": "Αστέρι 12 Σημείων", "SSE.Controllers.Main.txtShape_star16": "Αστέρι 16 Σημείων", "SSE.Controllers.Main.txtShape_star24": "Αστέρι 24 Σημείων", "SSE.Controllers.Main.txtShape_star32": "Αστέρι 32 Σημείων", "SSE.Controllers.Main.txtShape_star4": "Αστέρι 4 Σημείων", "SSE.Controllers.Main.txtShape_star5": "Αστέρι 5 Σημείων", "SSE.Controllers.Main.txtShape_star6": "Αστέρι 6 Σημείων", "SSE.Controllers.Main.txtShape_star7": "Αστέρι 7 Σημείων", "SSE.Controllers.Main.txtShape_star8": "Αστέρι 8 Σημείων", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Ριγέ Δεξιό Βέλος", "SSE.Controllers.Main.txtShape_sun": "Ήλιος", "SSE.Controllers.Main.txtShape_teardrop": "Δά<PERSON>ρυ", "SSE.Controllers.Main.txtShape_textRect": "Πλ<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON>νου", "SSE.Controllers.Main.txtShape_trapezoid": "Τραπέζιο", "SSE.Controllers.Main.txtShape_triangle": "Τρίγωνο", "SSE.Controllers.Main.txtShape_upArrow": "Πάν<PERSON> Βέλ<PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "Επεξήγηση με Πάνω Βέλος", "SSE.Controllers.Main.txtShape_upDownArrow": "Πάνω Κάτω Β<PERSON>λος", "SSE.Controllers.Main.txtShape_uturnArrow": "<PERSON><PERSON><PERSON><PERSON>αστροφής", "SSE.Controllers.Main.txtShape_verticalScroll": "Κατακόρυφη Κύλιση", "SSE.Controllers.Main.txtShape_wave": "Κύμα", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Οβάλ Επεξήγηση", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Ορθογώνια Επεξήγηση", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Στρογγυλεμένη Ορθογώνια Επεξήγηση", "SSE.Controllers.Main.txtStarsRibbons": "Αστέρια & Κορδέλες", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "Υπολογισμός", "SSE.Controllers.Main.txtStyle_Check_Cell": "Έλεγχος Κελιού", "SSE.Controllers.Main.txtStyle_Comma": "Κόμμα", "SSE.Controllers.Main.txtStyle_Currency": "Νόμισμα", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Επεξηγηματικ<PERSON>είμενο", "SSE.Controllers.Main.txtStyle_Good": "Σωστό", "SSE.Controllers.Main.txtStyle_Heading_1": "Επικεφαλίδα 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Επικεφαλίδα 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Επικεφαλίδα 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Επικεφαλίδα 4", "SSE.Controllers.Main.txtStyle_Input": "Εισαγωγή", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Συνδεδεμέν<PERSON> Κελί", "SSE.Controllers.Main.txtStyle_Neutral": "Ουδέτερο", "SSE.Controllers.Main.txtStyle_Normal": "Κανονική", "SSE.Controllers.Main.txtStyle_Note": "Σημείωση", "SSE.Controllers.Main.txtStyle_Output": "Αποτέλεσμα", "SSE.Controllers.Main.txtStyle_Percent": "Ποσοστό", "SSE.Controllers.Main.txtStyle_Title": "Τίτλος", "SSE.Controllers.Main.txtStyle_Total": "Σύνολο", "SSE.Controllers.Main.txtStyle_Warning_Text": "Κείμενο Προειδοποίησης", "SSE.Controllers.Main.txtTab": "Καρτ<PERSON><PERSON>α", "SSE.Controllers.Main.txtTable": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Controllers.Main.txtTime": "Ώρα", "SSE.Controllers.Main.txtUnlock": "Ξεκλείδωμα", "SSE.Controllers.Main.txtUnlockRange": "Ξεκλείδωμα Εύρους", "SSE.Controllers.Main.txtUnlockRangeDescription": "Εισάγετε το συνθηματικό τροποποίησης αυτού του εύρους:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Ένα εύρος που προσπαθείτε να τροποποιήσετε προστατεύεται με συνθηματικό.", "SSE.Controllers.Main.txtValues": "Τιμές", "SSE.Controllers.Main.txtXAxis": "Άξονας Χ", "SSE.Controllers.Main.txtYAxis": "Άξονας Υ", "SSE.Controllers.Main.txtYears": "Έτη", "SSE.Controllers.Main.unknownErrorText": "Άγνωστο σφάλμα.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Ο περιηγητής σας δεν υποστηρίζεται.", "SSE.Controllers.Main.uploadDocExtMessage": "Άγνωστη μορφή εγγράφου.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Δεν μεταφορτώθηκαν έγγραφα.", "SSE.Controllers.Main.uploadDocSizeMessage": "Ξεπεράστηκε το μέγιστο μέγεθος εγγράφου.", "SSE.Controllers.Main.uploadImageExtMessage": "Άγνωστη μορφή εικόνας.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Δεν μεταφορτώθηκαν εικόνες.", "SSE.Controllers.Main.uploadImageSizeMessage": "Η εικόνα είναι πολύ μεγάλη. Το μέγιστο μέγεθος είναι 25MB.", "SSE.Controllers.Main.uploadImageTextText": "Μεταφόρτωση εικόνας...", "SSE.Controllers.Main.uploadImageTitleText": "Μεταφόρτωση Εικόνας", "SSE.Controllers.Main.waitText": "Παρακαλούμε, περιμένετε...", "SSE.Controllers.Main.warnBrowserIE9": "Η εφαρμογή έχει περιορισμένες δυνατότητες στον IE9. Δοκιμάστε IE10 ή νεώτερο.", "SSE.Controllers.Main.warnBrowserZoom": "Η τρέχουσα ρύθμιση εστίασης του περιηγητή σας δεν υποστηρίζεται πλήρως. Παρακαλούμε επιστρέψτε στην προεπιλεγμένη εστίαση πατώντας Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "Έχετε υπερβεί τον αριθμό των ταυτόχρονων συνδέσεων με τον διακομιστή εγγράφων και το έγγραφο θα ανοίξει μόνο για προβολή.<br>Πα<PERSON><PERSON><PERSON><PERSON>λούμε επικοινωνήστε με τον διαχειριστή σας για περισσότερες πληροφορίες.", "SSE.Controllers.Main.warnLicenseExp": "Η άδειά σας έχει λήξει.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>με ενημερώστε την άδεια χρήσης σας και ανανεώστε τη σελίδα.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Η άδεια έληξε.<br>Δεν έχετε πρόσβαση στη δυνατότητα επεξεργασίας εγγράφων.<br>Ε<PERSON><PERSON><PERSON><PERSON><PERSON>νωνήστε με τον διαχειριστή σας.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Η άδεια πρέπει να ανανεωθεί.<br>Έχετε περιορισμένη πρόσβαση στη λειτουργία επεξεργασίας εγγράφων.<br>Ε<PERSON><PERSON><PERSON><PERSON><PERSON>νωνήστε με τον διαχειριστή σας για πλήρη πρόσβαση", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Έχετε υπερβεί τον αριθμό των ταυτόχρονων χρηστών και το έγγραφο θα ανοίξει μόνο για προβολή.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή σας για περισσότερες πληροφορίες.", "SSE.Controllers.Main.warnNoLicense": "Αυτή η έκδοση των επεξεργαστών %1 έχει ορισμένους περιορισμούς για ταυτόχρονες συνδέσεις με το διακομιστή εγγράφων.<br><PERSON><PERSON><PERSON> χρειάζεστε περισσότερες, παρακαλούμε σκεφτείτε να αγοράσετε μια εμπορική άδεια.", "SSE.Controllers.Main.warnNoLicenseUsers": "Αυτή η έκδοση των επεξεργαστών %1 έχει ορισμένους περιορισμούς για τους ταυτόχρονους χρήστες.<br><PERSON><PERSON><PERSON> χρειάζεστε περισσότερους, παρακαλούμε σκεφτείτε να αγοράσετε μια εμπορική άδεια.", "SSE.Controllers.Main.warnProcessRightsChange": "Σας έχει απαγορευτεί το δικαίωμα επεξεργασίας του αρχείου.", "SSE.Controllers.Print.strAllSheets": "Όλα τα Φύλλα", "SSE.Controllers.Print.textFirstCol": "Πρώτη στήλη", "SSE.Controllers.Print.textFirstRow": "Πρώτη γραμμή", "SSE.Controllers.Print.textFrozenCols": "Παγωμένες στήλες", "SSE.Controllers.Print.textFrozenRows": "Παγωμένες γραμμές", "SSE.Controllers.Print.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Controllers.Print.textNoRepeat": "Να μην επαναλαμβάνεται", "SSE.Controllers.Print.textRepeat": "Επανάληψη...", "SSE.Controllers.Print.textSelectRange": "Επιλογή εύρους", "SSE.Controllers.Print.textWarning": "Προειδοποίηση", "SSE.Controllers.Print.txtCustom": "Προσαρμογή", "SSE.Controllers.Print.warnCheckMargings": "Τα περιθώρια είναι εσφαλμένα", "SSE.Controllers.Search.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Controllers.Search.textNoTextFound": "Τα δεδομένα που αναζητάτε δεν βρέθηκαν. Παρακαλούμε προσαρμόστε τις επιλογές αναζήτησης.", "SSE.Controllers.Search.textReplaceSkipped": "Η αντικατάσταση ολοκληρώθηκε. {0} εμφανίσεις προσπεράστηκαν.", "SSE.Controllers.Search.textReplaceSuccess": "Η αναζήτηση ολοκληρώθηκε. Αντικαταστάθηκαν {0} εμφανίσεις", "SSE.Controllers.Statusbar.errorLastSheet": "Το βιβλίο εργασίας πρέπει να έχει τουλάχιστον ένα ορατό φύλλο εργασίας.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Δεν είναι δυνατή η διαγραφή του φύλλου εργασίας.", "SSE.Controllers.Statusbar.strSheet": "Φύλλο", "SSE.Controllers.Statusbar.textDisconnect": "<b>Η σύνδεση χάθηκε</b><br>Απ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επανασύνδεσης. Παρακαλούμε ελέγξτε τις ρυθμίσεις σύνδεσης.", "SSE.Controllers.Statusbar.textSheetViewTip": "Βρίσκεστε σε κατάσταση Προβολής Φύλλου. Φίλτρα και ταξινομήσεις είναι ορατά μόνο σε εσάς και όσους είναι ακόμα σε αυτή την προβολή.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Βρίσκεστε ακόμα σε κατάσταση Προβολής Φύλλου. Φίλτρα είναι ορατά μόνο σε εσάς και όσους βρίσκονται ακόμα σε αυτή την προβολή.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Τα επιλεγμένα φύλλα εργασίας ενδέχεται να περιέχουν δεδομένα. Είστε βέβαιοι ότι θέλετε να συνεχίσετε;", "SSE.Controllers.Statusbar.zoomText": "Εστίαση {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Η γραμματοσειρά που επιχειρείτε να αποθηκεύσετε δεν είναι διαθέσιμη στην τρέχουσα συσκευή.<br>Η τεχνοτροπία κειμένου θα εμφανιστεί με μια από τις γραμματοσειρές συστήματος, η αποθηκευμένη γραμματοσειρά θα χρησιμοποιηθεί όταν γίνει διαθέσιμη.<br>Θέλετε να συνεχίσετε;", "SSE.Controllers.Toolbar.errorComboSeries": "Για να δημιουργήσετε συνδυαστικ<PERSON> γράφημα, επιλέξτε τουλάχιστον δύο σειρές δεδομένων.", "SSE.Controllers.Toolbar.errorMaxRows": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255", "SSE.Controllers.Toolbar.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Controllers.Toolbar.textAccent": "Τόνοι/Πνεύματα", "SSE.Controllers.Toolbar.textBracket": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.textDirectional": "Κατευθυντικό", "SSE.Controllers.Toolbar.textFontSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 1 και 409", "SSE.Controllers.Toolbar.textFraction": "Κλάσματα", "SSE.Controllers.Toolbar.textFunction": "Συναρτή<PERSON><PERSON>ις", "SSE.Controllers.Toolbar.textIndicator": "Δείκτες", "SSE.Controllers.Toolbar.textInsert": "Εισαγωγή", "SSE.Controllers.Toolbar.textIntegral": "Ολοκληρώματα", "SSE.Controllers.Toolbar.textLargeOperator": "Μεγάλοι Τελεστές", "SSE.Controllers.Toolbar.textLimitAndLog": "Όρια και Λογάριθμοι", "SSE.Controllers.Toolbar.textLongOperation": "Η λειτουργία είναι χρονοβόρα", "SSE.Controllers.Toolbar.textMatrix": "Πίνακ<PERSON>ς", "SSE.Controllers.Toolbar.textOperator": "Τελεστές", "SSE.Controllers.Toolbar.textPivot": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Controllers.Toolbar.textRadical": "Ρίζες", "SSE.Controllers.Toolbar.textRating": "Αξιολογήσεις", "SSE.Controllers.Toolbar.textRecentlyUsed": "Πρόσφατα Χρησιμοποιημένα", "SSE.Controllers.Toolbar.textScript": "Δέσμες ενεργειών", "SSE.Controllers.Toolbar.textShapes": "Σχήματα", "SSE.Controllers.Toolbar.textSymbols": "Σύμβολα", "SSE.Controllers.Toolbar.textWarning": "Προειδοποίηση", "SSE.Controllers.Toolbar.txtAccent_Accent": "Οξεία", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Από πάνω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Από πάνω βέλος προς αριστερά", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Από πάνω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtAccent_Bar": "Μπάρα", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Κάτω οριζόντια γραμμή", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Επάνω οριζόντια γραμμή", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Μαθηματική σχέση εντός πλαισίου (με δέσμευση θέσης)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Μαθηματική σχέση εντός πλαισίου (παράδειγμα)", "SSE.Controllers.Toolbar.txtAccent_Check": "Έλεγχος", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Κάτω αγκύλη έκτασης", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Πάνω αγκύλη έκτασης", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Διάνυσμα A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC με οριζόντια γραμμή από πάνω", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y με επάνω οριζόντια γραμμή", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Τριπλή τελεία", "SSE.Controllers.Toolbar.txtAccent_DDot": "Διπλή τελεία", "SSE.Controllers.Toolbar.txtAccent_Dot": "Τελεία", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Διπλή μπάρα από πάνω", "SSE.Controllers.Toolbar.txtAccent_Grave": "Βαρεία", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Ομαδοποίηση χαρακτήρα από κάτω", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Ομαδοποίηση χαρακτήρα από πάνω", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Από πάνω καμάκι προς τα αριστερά", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Από πάνω καμάκι προς τα δεξιά", "SSE.Controllers.Toolbar.txtAccent_Hat": "Σύμβολο (^)", "SSE.Controllers.Toolbar.txtAccent_Smile": "Σημεί<PERSON> βραχέος", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Περισπωμένη", "SSE.Controllers.Toolbar.txtBracket_Angle": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Αγκύλες με διαχωριστικά", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Αγκύλες με διαχωριστικά", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Curve": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Αγκύλες με διαχωριστικά", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Περιπ<PERSON><PERSON><PERSON><PERSON>ι<PERSON> (δύο συνθήκες)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Περιπτώσεις (τρεις συνθήκες)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Αντικείμενο στοίβας", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Αντικείμενο στοίβας", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Παράδειγμα περιπτώσεων", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Διωνυ<PERSON><PERSON><PERSON><PERSON>ς συντελεστής", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Διωνυ<PERSON><PERSON><PERSON><PERSON>ς συντελεστής", "SSE.Controllers.Toolbar.txtBracket_Line": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Round": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Αγκύλες με διαχωριστικά", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Square": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Μονή αγκύλη", "SSE.Controllers.Toolbar.txtDeleteCells": "Διαγρα<PERSON><PERSON> Κελιών", "SSE.Controllers.Toolbar.txtExpand": "Επέκταση και ταξινόμηση", "SSE.Controllers.Toolbar.txtExpandSort": "Τα δεδομένα δίπλα στην επιλογή δεν θα ταξινομηθούν. Θέλετε να επεκτείνετε την επιλογή ώστε να συμπεριλάβει τα παρακείμενα δεδομένα ή να συνεχίσετε με την ταξινόμηση μόνο των επιλεγμένων κελιών;", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Κεκλιμένο κλάσμα", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Διαφορικό", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Διαφορικό", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Διαφορικό", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Διαφορικό", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Γραμμικό κλάσμα", "SSE.Controllers.Toolbar.txtFractionPi_2": "π/2", "SSE.Controllers.Toolbar.txtFractionSmall": "Μικρό κλάσμα", "SSE.Controllers.Toolbar.txtFractionVertical": "Σύνθετο κλάσμα", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Συνάρτηση αντίστροφου συνημιτόνου", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Συνάρτηση αντίστροφου υπερβολικού συνημιτόνου", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Συνάρτηση αντίστροφης συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Συνάρτηση αντίστροφης υπερβολικής συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Συνάρτηση αντίστροφης συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Συνάρτηση αντίστροφης υπερβολικής συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Συνάρτηση αντίστροφης τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Συνάρτηση αντίστροφης υπερβολικής τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Συνάρτηση αντίστροφου ημίτονου", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Συνάρτηση αντίστροφου υπερβολικού ημίτονου", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Συνάρτηση αντίστροφης εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Συνάρτηση αντίστροφης υπερβολικής εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Cos": "Συνάρτηση συνημίτονου", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Συνάρτηση υπερβολικού συνημιτόνου", "SSE.Controllers.Toolbar.txtFunction_Cot": "Συνάρτηση συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Coth": "Συνάρτηση υπερβολικής συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Csc": "Συνάρτηση συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Csch": "Συνάρτηση υπερβολικής συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Ημίτονο γωνίας θήτα", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Συνημίτονο 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Τύ<PERSON><PERSON> εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Sec": "Συνάρτηση τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Sech": "Συνάρτηση υπερβολικής τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Sin": "Συνάρτηση ημίτονου", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Συνάρτηση υπερβολικού ημίτονου", "SSE.Controllers.Toolbar.txtFunction_Tan": "Συνάρτηση εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Συνάρτηση υπερβολικής εφαπτομένης", "SSE.Controllers.Toolbar.txtInsertCells": "Εισαγωγ<PERSON>ελιών", "SSE.Controllers.Toolbar.txtIntegral": "Ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Διαφορικ<PERSON> θήτα", "SSE.Controllers.Toolbar.txtIntegral_dx": "Διαφορικό x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Διαφορικό y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralDouble": "Διπλ<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Διπλ<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Διπλ<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOriented": "Επικαμ<PERSON>ύ<PERSON>ι<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Επικαμ<PERSON>ύ<PERSON>ι<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Επικαμ<PERSON>ύ<PERSON>ι<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Ολοκλήρω<PERSON>α όγκου", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Ολοκλήρω<PERSON>α όγκου", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Ολοκλήρω<PERSON>α όγκου", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralTriple": "Τριπλό ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Τριπλό ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Τριπλό ολοκλήρωμα", "SSE.Controllers.Toolbar.txtInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Σύζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Σύζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Σύζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Σύζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Σύζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Συν-γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Συν-γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Συν-γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Συν-γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Συν-γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Ένωση", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Διάζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Διάζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Διάζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Διάζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Διάζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Τομή", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Τομή", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Τομή", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Τομή", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Τομή", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Ένωση", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Ένωση", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Ένωση", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Ένωση", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Ένωση", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Παράδειγμα ορίου", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Παράδειγμα μέγιστου", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Όριο", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON>υ<PERSON>ι<PERSON><PERSON><PERSON> λογάριθμος", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Λογάριθμος", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Λογάριθμος", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Μέγιστο", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Ελάχιστο", "SSE.Controllers.Toolbar.txtLockSort": "Υπάρχουν δεδομένα δίπλα στην επιλογή σας, αλλ<PERSON> δεν έχετε επαρκή δικαιώματα τροποποίησης αυτών των κελιών.<br>Θέλετε να συνεχίσετε με την τρέχουσα επιλογή;", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με παρενθέσεις", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με παρενθέσεις", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με παρενθέσεις", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με παρενθέσεις", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Τελείες στο κάτω μέρος της γραμμής", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Τελείες στο μέσον της γραμμής", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Διαγώνιες τελείες", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Κατακόρυφες τελείες", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Αραι<PERSON>ς πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Αραι<PERSON>ς πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 μοναδι<PERSON><PERSON>ος πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 μοναδι<PERSON><PERSON>ος πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 μονα<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 μονα<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Από κάτω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Από πάνω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Από κάτω βέλος προς τα αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Από πάνω βέλος προς αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Από κάτω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Από πάνω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Άνω κάτω τελεία ίσον", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Δίνει", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Δέλτα δίνει", "SSE.Controllers.Toolbar.txtOperator_Definition": "Ίσον με εξ ορισμού", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Δέλτα ίσο με", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Από κάτω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Από πάνω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Από κάτω βέλος προς τα αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Από πάνω βέλος προς αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Από κάτω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Από πάνω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Ίσον ίσον", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Πλην ίσον", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Συν ίσον", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Μέτρηση με", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Ρίζα", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Ρίζα", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Τετραγωνική ρίζα με βαθμό", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Κυβική ρίζα", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Ρίζα με βαθμό", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Τετραγωνική ρίζα", "SSE.Controllers.Toolbar.txtScriptCustom_1": "Δέσμη ενεργειών", "SSE.Controllers.Toolbar.txtScriptCustom_2": "Δέσμη ενεργειών", "SSE.Controllers.Toolbar.txtScriptCustom_3": "Δέσμη ενεργειών", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Δέσμη ενεργειών", "SSE.Controllers.Toolbar.txtScriptSub": "Δείκτης", "SSE.Controllers.Toolbar.txtScriptSubSup": "Δείκτης-εκθέτης", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Αριστερ<PERSON>ς δείκτης-εκθέτης", "SSE.Controllers.Toolbar.txtScriptSup": "Εκθέτης", "SSE.Controllers.Toolbar.txtSorting": "Ταξινόμηση", "SSE.Controllers.Toolbar.txtSortSelected": "Ταξινόμηση επιλεγμένων", "SSE.Controllers.Toolbar.txtSymbol_about": "Περίπου", "SSE.Controllers.Toolbar.txtSymbol_additional": "Συμπλήρωμα", "SSE.Controllers.Toolbar.txtSymbol_aleph": "'Aλεφ", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Άλφα", "SSE.Controllers.Toolbar.txtSymbol_approx": "Σχεδόν ίσο με", "SSE.Controllers.Toolbar.txtSymbol_ast": "Τελεστής αστερίσκος", "SSE.Controllers.Toolbar.txtSymbol_beta": "Βήτα", "SSE.Controllers.Toolbar.txtSymbol_beth": "Στοίχημα", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Τελεστής κουκκίδα", "SSE.Controllers.Toolbar.txtSymbol_cap": "Τομή", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Κυβική ρίζα", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Οριζόντια έλλειψη στο μέσον της γραμμής", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON>αθ<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "Χι", "SSE.Controllers.Toolbar.txtSymbol_cong": "Περίπου ίσο με", "SSE.Controllers.Toolbar.txtSymbol_cup": "Ένωση", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Διαγώνια έλλειψη κάτω δεξιά", "SSE.Controllers.Toolbar.txtSymbol_degree": "Βαθμοί", "SSE.Controllers.Toolbar.txtSymbol_delta": "Δέλτα", "SSE.Controllers.Toolbar.txtSymbol_div": "Σύμβολο διαίρεσης", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Κάτω βέλος", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Κενό σύνολο", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Έψιλον", "SSE.Controllers.Toolbar.txtSymbol_equals": "Ίσον", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Πανομοιότυπο με", "SSE.Controllers.Toolbar.txtSymbol_eta": "Ήτα", "SSE.Controllers.Toolbar.txtSymbol_exists": "Υπάρχει", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Παραγοντικό", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Βαθμοί Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON>α όλα", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Γάμμα", "SSE.Controllers.Toolbar.txtSymbol_geq": "Μεγαλύτερο ή ίσο με", "SSE.Controllers.Toolbar.txtSymbol_gg": "Πολύ μεγαλύτερο από", "SSE.Controllers.Toolbar.txtSymbol_greater": "Μεγαλύτερο από", "SSE.Controllers.Toolbar.txtSymbol_in": "Στοιχείο του", "SSE.Controllers.Toolbar.txtSymbol_inc": "Αύξηση τιμής", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Άπειρο", "SSE.Controllers.Toolbar.txtSymbol_iota": "Γιώτα", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Κάπα", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Λάμδα", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Αριστερ<PERSON> βέλος", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Αριστερό-δεξιό βέλος", "SSE.Controllers.Toolbar.txtSymbol_leq": "Μικρότερο από ή ίσο με", "SSE.Controllers.Toolbar.txtSymbol_less": "Μικρότερο από", "SSE.Controllers.Toolbar.txtSymbol_ll": "Πολ<PERSON> μικρότερο από", "SSE.Controllers.Toolbar.txtSymbol_minus": "Πλην", "SSE.Controllers.Toolbar.txtSymbol_mp": "Πλην συν", "SSE.Controllers.Toolbar.txtSymbol_mu": "Μι", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Ανάδελτα", "SSE.Controllers.Toolbar.txtSymbol_neq": "Διάφορο από", "SSE.Controllers.Toolbar.txtSymbol_ni": "Περιέχει ως μέλος", "SSE.Controllers.Toolbar.txtSymbol_not": "Σύμβολο άρνησης", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Δεν υπάρχει", "SSE.Controllers.Toolbar.txtSymbol_nu": "Νι", "SSE.Controllers.Toolbar.txtSymbol_o": "Όμικρον", "SSE.Controllers.Toolbar.txtSymbol_omega": "Ωμέγα", "SSE.Controllers.Toolbar.txtSymbol_partial": "Μερικό διαφορικό", "SSE.Controllers.Toolbar.txtSymbol_percent": "Ποσοστό", "SSE.Controllers.Toolbar.txtSymbol_phi": "Φι", "SSE.Controllers.Toolbar.txtSymbol_pi": "Πι", "SSE.Controllers.Toolbar.txtSymbol_plus": "Συν", "SSE.Controllers.Toolbar.txtSymbol_pm": "Συν πλην", "SSE.Controllers.Toolbar.txtSymbol_propto": "Σε αναλογία με", "SSE.Controllers.Toolbar.txtSymbol_psi": "Ψι", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Τέταρτη ρίζα", "SSE.Controllers.Toolbar.txtSymbol_qed": "Τ<PERSON><PERSON><PERSON> απόδειξης", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Πάνω δεξιά διαγώνια έλλειψη", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ρο", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Δεξ<PERSON> βέλος", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Σίγμα", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Σύμβολο ρίζας", "SSE.Controllers.Toolbar.txtSymbol_tau": "Ταυ", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Επομένως", "SSE.Controllers.Toolbar.txtSymbol_theta": "Θήτα", "SSE.Controllers.Toolbar.txtSymbol_times": "Σύμβολο πολλαπλασιασμού", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Πάν<PERSON> βέλος", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ύψιλον", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Παραλλαγή του έψιλον", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Παραλλαγή του φι", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Παραλλαγή του πι", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Παραλλαγή του ρο", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Τελικό σίγμα", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Παραλλαγή του θήτα", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Κατακόρυφη έλλειψη", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ξι", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Ζήτα", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Σκούρα Τεχνοτροπία Πίνακα", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Φωτεινή Τεχνοτροπία Πίνακα", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Ενδιάμεση Τεχνοτροπία Πίνακα", "SSE.Controllers.Toolbar.warnLongOperation": "Η λειτουργία που πρόκειται να εκτελέσετε ίσως χρειαστεί πολύ χρόνο για να ολοκληρωθεί.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Controllers.Toolbar.warnMergeLostData": "Μόνο τα δεδομένα από το επάνω αριστερό κελί θα παραμείνουν στο συγχωνευμένο κελί.<br><PERSON><PERSON><PERSON><PERSON><PERSON> σίγουροι ότι θέλετε να συνεχίσετε;", "SSE.Controllers.Viewport.textFreezePanes": "Πάγωμα Παραθύρων", "SSE.Controllers.Viewport.textFreezePanesShadow": "Εμφάνιση Σκιάς Παγωμένων Παραθύρων", "SSE.Controllers.Viewport.textHideFBar": "Απόκρυψη Μπάρας Τύπων", "SSE.Controllers.Viewport.textHideGridlines": "Απόκρυψη Γραμμών Πλέγματος", "SSE.Controllers.Viewport.textHideHeadings": "Απόκρυψη Επικεφαλίδων", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Διαχωριστικ<PERSON> δεκαδικού", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Διαχωριστικ<PERSON> χιλιάδων", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Ρυθμίσεις αναγνώρισης αριθμητικών δεδομένων", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Προσδιοριστής κειμένου", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Προηγμένες Ρυθμίσεις", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(κανένα)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Προσαρμοσμένο Φίλτρο", "SSE.Views.AutoFilterDialog.textAddSelection": "Προσθήκη τρέχουσας επιλογής στο φίλτρο", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Κενά}", "SSE.Views.AutoFilterDialog.textSelectAll": "Επιλογή Όλων ", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Επιλογή Όλων των Αποτελεσμάτων Αναζήτησης", "SSE.Views.AutoFilterDialog.textWarning": "Προειδοποίηση", "SSE.Views.AutoFilterDialog.txtAboveAve": "Πάνω από τον μέσο όρο", "SSE.Views.AutoFilterDialog.txtBegins": "Αρχίζει με...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Κάτω από τον μέσο όρο", "SSE.Views.AutoFilterDialog.txtBetween": "Μεταξύ...", "SSE.Views.AutoFilterDialog.txtClear": "Εκκαθάριση", "SSE.Views.AutoFilterDialog.txtContains": "Περιέχει...", "SSE.Views.AutoFilterDialog.txtEmpty": "Εισάγετε φίλτρο κελιού", "SSE.Views.AutoFilterDialog.txtEnds": "Τελειώνει με...", "SSE.Views.AutoFilterDialog.txtEquals": "Ισούται...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Φιλτράρισμα με χρώμα κελιών", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Φιλτράρισμα με χρώμα γραμματοσειράς", "SSE.Views.AutoFilterDialog.txtGreater": "Μεγαλύτερο από...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Μεγαλύτερο από ή ίσο με...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Φίλτρο ετικέτας", "SSE.Views.AutoFilterDialog.txtLess": "Μικρότερο από...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Μικρότερο από ή ίσο με...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Δεν ξεκινά με...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Όχι ανάμεσα...", "SSE.Views.AutoFilterDialog.txtNotContains": "Δεν περιέχει...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Δεν τελειώνει με...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Δεν είναι ίσο με...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Φίλτρο αριθμού", "SSE.Views.AutoFilterDialog.txtReapply": "Εφαρμογή Ξανά", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Ταξινόμηση κατά το χρώμα κελιών", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Ταξινόμηση κατά το χρώμα γραμματοσειράς", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Ταξινόμηση από το Υψηλότερο στο Χαμηλότερο", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Ταξινόμηση από το Χαμηλότερο στο Υψηλότερο", "SSE.Views.AutoFilterDialog.txtSortOption": "Περισσότερες ρυθμίσεις ταξινόμησης...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Φίλτρ<PERSON> κειμένου", "SSE.Views.AutoFilterDialog.txtTitle": "Φίλτρο", "SSE.Views.AutoFilterDialog.txtTop10": "Κορυφαία 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Φίλ<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "Απαιτείτ<PERSON>ι τουλάχιστον ένα πεδίο στην περιοχή Τιμών για να εφαρμοστεί ένα φίλτρο τιμών.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Πρέπει να επιλέξετε τουλάχιστον μία τιμή", "SSE.Views.CellEditor.textManager": "Διαχειριστής Ονομάτων", "SSE.Views.CellEditor.tipFormula": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.CellRangeDialog.errorMaxRows": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255", "SSE.Views.CellRangeDialog.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.CellRangeDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.CellRangeDialog.txtInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.CellRangeDialog.txtTitle": "Επιλογή Εύρους Δεδομένων", "SSE.Views.CellSettings.strShrink": "Σμίκρυνση για ταίριασμα", "SSE.Views.CellSettings.strWrap": "Αναδίπλωση κειμένου", "SSE.Views.CellSettings.textAngle": "Γωνία", "SSE.Views.CellSettings.textBackColor": "Χρώμα Παρασ<PERSON>ηνίου", "SSE.Views.CellSettings.textBackground": "Χρώμα παρασκηνίου", "SSE.Views.CellSettings.textBorderColor": "Χρώμα", "SSE.Views.CellSettings.textBorders": "Τεχνοτροπία Περιγραμμάτων", "SSE.Views.CellSettings.textClearRule": "Εκκαθάριση Κανόνων", "SSE.Views.CellSettings.textColor": "Γέμισμα με Χρώμα", "SSE.Views.CellSettings.textColorScales": "Κλίμα<PERSON>ες <PERSON>ρωμάτων", "SSE.Views.CellSettings.textCondFormat": "Μορφοποίηση υπό όρους", "SSE.Views.CellSettings.textControl": "Έλεγχος Κειμένου", "SSE.Views.CellSettings.textDataBars": "Μπ<PERSON><PERSON><PERSON><PERSON> Δεδομένων", "SSE.Views.CellSettings.textDirection": "Κατεύθυνση", "SSE.Views.CellSettings.textFill": "Γέμισμα", "SSE.Views.CellSettings.textForeground": "Χρώμα προσκηνίου", "SSE.Views.CellSettings.textGradient": "Σημεία διαβάθμισης", "SSE.Views.CellSettings.textGradientColor": "Χρώμα", "SSE.Views.CellSettings.textGradientFill": "Βαθμωτό Γέμισμα", "SSE.Views.CellSettings.textIndent": "Εσοχή", "SSE.Views.CellSettings.textItems": "Αντικείμενα", "SSE.Views.CellSettings.textLinear": "Γραμμικός", "SSE.Views.CellSettings.textManageRule": "Διαχείρι<PERSON>η Κανόνων", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON>α", "SSE.Views.CellSettings.textOrientation": "Προσανα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς <PERSON>ειμένου", "SSE.Views.CellSettings.textPattern": "Μοτίβο", "SSE.Views.CellSettings.textPatternFill": "Μοτίβο", "SSE.Views.CellSettings.textPosition": "Θέση", "SSE.Views.CellSettings.textRadial": "Ακτινικ<PERSON>ς", "SSE.Views.CellSettings.textSelectBorders": "Επιλέξτε τα περιγράμματα που θέλετε να αλλάξετε εφαρμόζοντας την ανωτέρω επιλεγμένη τεχνοτροπία", "SSE.Views.CellSettings.textSelection": "Από την τρέχουσα επιλογή", "SSE.Views.CellSettings.textThisPivot": "Από αυτόν τον συγκεντρωτικό πίνακα", "SSE.Views.CellSettings.textThisSheet": "Από αυτό το φύλλο εργασίας", "SSE.Views.CellSettings.textThisTable": "Από αυτόν τον πίνακα", "SSE.Views.CellSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "SSE.Views.CellSettings.tipAll": "Ορισμ<PERSON>ς εξωτερικού περιγράμματος και όλων των εσωτερικών γραμμών", "SSE.Views.CellSettings.tipBottom": "Ορισμός μόνο εξωτερικού κάτω περιγράμματος", "SSE.Views.CellSettings.tipDiagD": "Ορισμ<PERSON>ς Δ<PERSON>αγώνιου Κάτω Περιγράμματος", "SSE.Views.CellSettings.tipDiagU": "Ορισμ<PERSON>ς Δ<PERSON>αγώνιου Πάνω Περιγράμματος", "SSE.Views.CellSettings.tipInner": "Ορισμός μόνο των εσωτερικών γραμμών", "SSE.Views.CellSettings.tipInnerHor": "Ορισμός μόνο των οριζόντιων εσωτερικών γραμμών", "SSE.Views.CellSettings.tipInnerVert": "Ορισμός μόνο των κατακόρυφων εσωτερικών γραμμών", "SSE.Views.CellSettings.tipLeft": "Ορισμός μόνο του εξωτερικού αριστερού περιγράμματος", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON><PERSON><PERSON><PERSON> κανένα περίγραμμα", "SSE.Views.CellSettings.tipOuter": "Ορισμός μόνο του εξωτερικού περιγράμματος", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "SSE.Views.CellSettings.tipRight": "Ορισμός μόνο του εξωτερικού δεξιού περιγράμματος", "SSE.Views.CellSettings.tipTop": "Ορισμός μόνο του εξωτερικού πάνω περιγράμματος", "SSE.Views.ChartDataDialog.errorInFormula": "Υπάρχει κάποιο σφάλμα στον τύπο που εισαγάγατε.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Η αναφορά δεν είναι έγκυρη. Η αναφορά πρέπει να δείχνει σε ένα ανοικτό φύλλο εργασιών.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Η αναφορά δεν είναι έγκυρη. Οι αναφορές σε τίτλους, τιμ<PERSON>ς, μεγέθη ή ετικέτες δεδομένων πρέπει να είναι ένα μονα<PERSON>ικ<PERSON> κελί, γρα<PERSON><PERSON><PERSON> <PERSON> στήλη.", "SSE.Views.ChartDataDialog.errorNoValues": "Για να δημιουργηθεί γράφημα, πρέπει η σειρά να περιέχει τουλάχιστον μία τιμή.", "SSE.Views.ChartDataDialog.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.ChartDataDialog.textAdd": "Προσθήκη", "SSE.Views.ChartDataDialog.textCategory": "Ετικέτες Οριζόντιου Άξονα (Κατηγορία)", "SSE.Views.ChartDataDialog.textData": "Εύρ<PERSON> δεδομένων γραφήματος", "SSE.Views.ChartDataDialog.textDelete": "Αφαίρεση", "SSE.Views.ChartDataDialog.textDown": "Κάτω", "SSE.Views.ChartDataDialog.textEdit": "Επεξεργασία", "SSE.Views.ChartDataDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.ChartDataDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ChartDataDialog.textSeries": "Καταχωρήσεις Υπομνήματος (Σειρές)", "SSE.Views.ChartDataDialog.textSwitch": "Εναλλαγή <PERSON>μής/Στήλης", "SSE.Views.ChartDataDialog.textTitle": "Δεδομένα Γραφήματος", "SSE.Views.ChartDataDialog.textUp": "Πάνω", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Υπάρχει κάποιο σφάλμα στον τύπο που εισαγάγατε.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Η αναφορά δεν είναι έγκυρη. Η αναφορά πρέπει να δείχνει σε ένα ανοικτό φύλλο εργασιών.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Η αναφορά δεν είναι έγκυρη. Οι αναφορές σε τίτλους, τιμ<PERSON>ς, μεγέθη ή ετικέτες δεδομένων πρέπει να είναι ένα μονα<PERSON>ικ<PERSON> κελί, γρα<PERSON><PERSON><PERSON> <PERSON> στήλη.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Για να δημιουργηθεί γράφημα, πρέπει η σειρά να περιέχει τουλάχιστον μία τιμή.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.ChartDataRangeDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Ε<PERSON><PERSON><PERSON> ετικέτας άξονα", "SSE.Views.ChartDataRangeDialog.txtChoose": "Επιλέξτε εύρος", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Όνομα σειράς", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Ετικέτες Άξονα", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Επεξεργασία Σειράς", "SSE.Views.ChartDataRangeDialog.txtValues": "Τιμές", "SSE.Views.ChartDataRangeDialog.txtXValues": "<PERSON>ι<PERSON><PERSON><PERSON> Χ", "SSE.Views.ChartDataRangeDialog.txtYValues": "Τιμές Υ", "SSE.Views.ChartSettings.errorMaxRows": "Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strSparkColor": "Χρώμα", "SSE.Views.ChartSettings.strTemplate": "Πρότυπο", "SSE.Views.ChartSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ChartSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 pt και 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Αλλαγή τύπου", "SSE.Views.ChartSettings.textChartType": "Αλλαγή Τύπου Γραφήματος", "SSE.Views.ChartSettings.textEditData": "Επεξεργασία Δεδομένων και Τοποθεσίας", "SSE.Views.ChartSettings.textFirstPoint": "Πρώτ<PERSON>", "SSE.Views.ChartSettings.textHeight": "Ύψος", "SSE.Views.ChartSettings.textHighPoint": "Μέγιστο Σημείο", "SSE.Views.ChartSettings.textKeepRatio": "Σταθερές αναλογίες", "SSE.Views.ChartSettings.textLastPoint": "Τελευτ<PERSON><PERSON><PERSON>είο", "SSE.Views.ChartSettings.textLowPoint": "Χαμηλ<PERSON>ημείο", "SSE.Views.ChartSettings.textMarkers": "Δείκτες", "SSE.Views.ChartSettings.textNegativePoint": "Αρνητικ<PERSON> Σημείο", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON><PERSON><PERSON>ν", "SSE.Views.ChartSettings.textSelectData": "Επιλογ<PERSON> Δεδομένων", "SSE.Views.ChartSettings.textShow": "Εμφάνιση", "SSE.Views.ChartSettings.textSize": "Μέγεθος", "SSE.Views.ChartSettings.textStyle": "Τεχνοτροπία", "SSE.Views.ChartSettings.textSwitch": "Εναλλαγή <PERSON>μής/Στήλης", "SSE.Views.ChartSettings.textType": "Τύπος", "SSE.Views.ChartSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.ChartSettingsDlg.textAlt": "Εναλλακτικό Κείμενο", "SSE.Views.ChartSettingsDlg.textAltDescription": "Περιγραφή", "SSE.Views.ChartSettingsDlg.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Τίτλος", "SSE.Views.ChartSettingsDlg.textAuto": "Αυτόματα", "SSE.Views.ChartSettingsDlg.textAutoEach": "Αυτόματο για <PERSON>θε", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Διασχίσεις Άξονα", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Επιλογές Άξονα", "SSE.Views.ChartSettingsDlg.textAxisPos": "Θέση Άξονα", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Ρυθμίσεις Άξονα", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Τίτλος", "SSE.Views.ChartSettingsDlg.textBase": "Βάση", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Μεταξύ Διαβαθμίσεων", "SSE.Views.ChartSettingsDlg.textBillions": "Δισεκατομμύρια", "SSE.Views.ChartSettingsDlg.textBottom": "Κάτω", "SSE.Views.ChartSettingsDlg.textCategoryName": "Όνομα Κατηγορίας", "SSE.Views.ChartSettingsDlg.textCenter": "Κέντρο", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Στοιχεία Γραφήματος &<br>Υπόμνημα Γραφήματος", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>τος", "SSE.Views.ChartSettingsDlg.textCross": "Διασταύρωση", "SSE.Views.ChartSettingsDlg.textCustom": "Προσαρμογή", "SSE.Views.ChartSettingsDlg.textDataColumns": "σε στήλες", "SSE.Views.ChartSettingsDlg.textDataLabels": "Ετικέτες Δεδομένων", "SSE.Views.ChartSettingsDlg.textDataRows": "σε γραμμές", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Εμφάνιση Υπομνήματος", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Κρυφά και Άδεια κελιά", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Σύνδεση σημείων δεδομένων με γραμμή", "SSE.Views.ChartSettingsDlg.textFit": "Προσαρμο<PERSON><PERSON> στο Πλάτος", "SSE.Views.ChartSettingsDlg.textFixed": "Σταθερό", "SSE.Views.ChartSettingsDlg.textFormat": "Μορφή ετικέτας", "SSE.Views.ChartSettingsDlg.textGaps": "Κενά", "SSE.Views.ChartSettingsDlg.textGridLines": "Γραμ<PERSON><PERSON>ς πλέγματος", "SSE.Views.ChartSettingsDlg.textGroup": "Ομαδοποίηση Μικρογραφήματος", "SSE.Views.ChartSettingsDlg.textHide": "Απόκρυψη", "SSE.Views.ChartSettingsDlg.textHideAxis": "Απόκρυψη άξονα", "SSE.Views.ChartSettingsDlg.textHigh": "Υψηλό", "SSE.Views.ChartSettingsDlg.textHorAxis": "Οριζόντιος Άξονας", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Δευτερεύων Οριζόντιος Άξονας", "SSE.Views.ChartSettingsDlg.textHorizontal": "Οριζόντια", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Εκατοντάδες", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Σε", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Εσωτερικ<PERSON>άτω Μέρος", "SSE.Views.ChartSettingsDlg.textInnerTop": "Εσωτερικ<PERSON>ω Μέρ<PERSON>", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.ChartSettingsDlg.textLabelDist": "Απόσταση Ετικέτας Άξονα", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Διάστημα μετα<PERSON>ύ Ετικετών", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Επιλογ<PERSON>ς <PERSON>τικέτας", "SSE.Views.ChartSettingsDlg.textLabelPos": "Θέση Ετικέτας", "SSE.Views.ChartSettingsDlg.textLayout": "Διάταξη", "SSE.Views.ChartSettingsDlg.textLeft": "Αριστερά", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Αριστερή Επικάλυψη", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Κάτω", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Αριστερά", "SSE.Views.ChartSettingsDlg.textLegendPos": "Υπόμνημα", "SSE.Views.ChartSettingsDlg.textLegendRight": "Δεξιά", "SSE.Views.ChartSettingsDlg.textLegendTop": "Επάνω", "SSE.Views.ChartSettingsDlg.textLines": "Γραμμές", "SSE.Views.ChartSettingsDlg.textLocationRange": "Εύρ<PERSON> Τοποθεσίας", "SSE.Views.ChartSettingsDlg.textLogScale": "Λογαριθμική Κλίμακα", "SSE.Views.ChartSettingsDlg.textLow": "Χαμηλό", "SSE.Views.ChartSettingsDlg.textMajor": "Βα<PERSON>ι<PERSON><PERSON>ς", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Βα<PERSON><PERSON><PERSON><PERSON><PERSON> και Δευτερεύουσες", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "Χε<PERSON>ρ<PERSON>κ<PERSON>νητα", "SSE.Views.ChartSettingsDlg.textMarkers": "Δείκτες", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Διάστημα μεταξύ Σημαδιών", "SSE.Views.ChartSettingsDlg.textMaxValue": "Μέγιστη Τιμή", "SSE.Views.ChartSettingsDlg.textMillions": "Εκατομμύρια", "SSE.Views.ChartSettingsDlg.textMinor": "Ελάσσον", "SSE.Views.ChartSettingsDlg.textMinorType": "Δευτερεύων Τύπος", "SSE.Views.ChartSettingsDlg.textMinValue": "Ελάχιστη Τιμή", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Δί<PERSON><PERSON>α στον άξονα", "SSE.Views.ChartSettingsDlg.textNone": "Κανένα", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON><PERSON><PERSON><PERSON>υψη", "SSE.Views.ChartSettingsDlg.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Επί των Διαβαθμίσεων", "SSE.Views.ChartSettingsDlg.textOut": "Έξω", "SSE.Views.ChartSettingsDlg.textOuterTop": "Εξωτερικ<PERSON>", "SSE.Views.ChartSettingsDlg.textOverlay": "Επικάλυψη", "SSE.Views.ChartSettingsDlg.textReverse": "Τιμές σε αντίστροφη σειρά", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Αντίστροφη σειρά", "SSE.Views.ChartSettingsDlg.textRight": "Δεξιά", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Δεξιά Επικάλυψη", "SSE.Views.ChartSettingsDlg.textRotated": "Περιστραμμένος", "SSE.Views.ChartSettingsDlg.textSameAll": "Ίδιο για Όλα", "SSE.Views.ChartSettingsDlg.textSelectData": "Επιλογ<PERSON> Δεδομένων", "SSE.Views.ChartSettingsDlg.textSeparator": "Διαχω<PERSON>ιστ<PERSON><PERSON> Ετικετών Δεδομένων", "SSE.Views.ChartSettingsDlg.textSeriesName": "Όνομα Σειράς", "SSE.Views.ChartSettingsDlg.textShow": "Εμφάνιση", "SSE.Views.ChartSettingsDlg.textShowBorders": "Εμφάνιση περιγραμμάτων γραφήματος", "SSE.Views.ChartSettingsDlg.textShowData": "Εμφάνιση δεδομένων σε κρυμμένες γραμμές και στήλες", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Εμφάνιση κενών κελιών ως", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Εμφάνιση Άξονα", "SSE.Views.ChartSettingsDlg.textShowValues": "Εμφάνιση τιμών γραφήματος", "SSE.Views.ChartSettingsDlg.textSingle": "Μονό Μικρογράφημα", "SSE.Views.ChartSettingsDlg.textSmooth": "Ομαλ<PERSON>ς", "SSE.Views.ChartSettingsDlg.textSnap": "Ευθυγράμμιση σε Kελί", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Εύρη Μικρογραφήματος", "SSE.Views.ChartSettingsDlg.textStraight": "Ίσιες", "SSE.Views.ChartSettingsDlg.textStyle": "Τεχνοτροπία", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Χιλιάδες", "SSE.Views.ChartSettingsDlg.textTickOptions": "Επιλογές Διαβαθμίσεων", "SSE.Views.ChartSettingsDlg.textTitle": "Γράφημα - Προηγμένες Ρυθμίσεις", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Μικρογράφημα - Προηγμένες Ρυθμίσεις", "SSE.Views.ChartSettingsDlg.textTop": "Επάνω", "SSE.Views.ChartSettingsDlg.textTrillions": "Τρισεκατομμύρια", "SSE.Views.ChartSettingsDlg.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.ChartSettingsDlg.textType": "Τύπος", "SSE.Views.ChartSettingsDlg.textTypeData": "Τύπος & Δεδομένα", "SSE.Views.ChartSettingsDlg.textUnits": "Εμφάνιση Μονάδων Μέτρησης", "SSE.Views.ChartSettingsDlg.textValue": "Τιμή", "SSE.Views.ChartSettingsDlg.textVertAxis": "Κατακόρυφος Άξονας", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Δευτερε<PERSON><PERSON>ν Κατακόρυφος Άξονας", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Τίτλος Άξονα Χ", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Τίτλος Άξονα Υ", "SSE.Views.ChartSettingsDlg.textZero": "Μηδέν", "SSE.Views.ChartSettingsDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.ChartTypeDialog.errorComboSeries": "Για να δημιουργήσετε συνδυαστικ<PERSON> γράφημα, επιλέξτε τουλάχιστον δύο σειρές δεδομένων.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Ο επιλεγμένος τύπος γραφήματος απαιτεί τον δευτερεύοντα άξονα που χρησιμοποιείται ήδη από υφιστάμενο γράφημα. Επιλέξτε άλλο τύπο γραφήματος.", "SSE.Views.ChartTypeDialog.textSecondary": "Δευτερεύων Άξονας", "SSE.Views.ChartTypeDialog.textSeries": "Σειρά", "SSE.Views.ChartTypeDialog.textStyle": "Τεχνοτροπία", "SSE.Views.ChartTypeDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>α<PERSON>ήματος", "SSE.Views.ChartTypeDialog.textType": "Τύπος", "SSE.Views.CreatePivotDialog.textDataRange": "Εύ<PERSON><PERSON> δεδομένων πηγής", "SSE.Views.CreatePivotDialog.textDestination": "Επιλέξτε θέση πίνακα", "SSE.Views.CreatePivotDialog.textExist": "Υφιστάμενο φύλλο εργασίας", "SSE.Views.CreatePivotDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.CreatePivotDialog.textNew": "Νέο φύλλο εργασίας", "SSE.Views.CreatePivotDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.CreatePivotDialog.textTitle": "Δημιουργ<PERSON><PERSON> Συγκεντρωτικού Πίνακα", "SSE.Views.CreatePivotDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.CreateSparklineDialog.textDataRange": "Εύ<PERSON><PERSON> δεδομένων πηγής", "SSE.Views.CreateSparklineDialog.textDestination": "Επιλογή θέσης για τα μικρογραφήματα", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.CreateSparklineDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.CreateSparklineDialog.textTitle": "Δημιουργ<PERSON>α Μικρογραφημάτων", "SSE.Views.CreateSparklineDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.DataTab.capBtnGroup": "Ομάδα", "SSE.Views.DataTab.capBtnTextCustomSort": "Προσαρμοσμένη Ταξινόμηση", "SSE.Views.DataTab.capBtnTextDataValidation": "Επικύρωση Δεδομένων", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Αφαίρεση Διπλότυπων", "SSE.Views.DataTab.capBtnTextToCol": "Κείμενο σε Στήλες", "SSE.Views.DataTab.capBtnUngroup": "Κατάργηση ομαδοποίησης", "SSE.Views.DataTab.capDataFromText": "Λήψη δεδομένων", "SSE.Views.DataTab.mniFromFile": "Από τοπικό αρχείο TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Από διεύθυνση ιστού σε αρχείο TXT/CSV", "SSE.Views.DataTab.textBelow": "Περίληψη γραμμών κάτω από τις γραμμές λεπτομέρειας", "SSE.Views.DataTab.textClear": "Καθα<PERSON>ισμ<PERSON>ς ομάδας", "SSE.Views.DataTab.textColumns": "Κατάργηση ομαδοποίησης στηλών", "SSE.Views.DataTab.textGroupColumns": "Ομαδοποίη<PERSON>η στηλών", "SSE.Views.DataTab.textGroupRows": "Ομαδοποίηση γραμμών", "SSE.Views.DataTab.textRightOf": "Περίληψη στηλών στα δεξιά των στηλών λεπτομερειών", "SSE.Views.DataTab.textRows": "Αναίρεση ομαδοποίησης γραμμών", "SSE.Views.DataTab.tipCustomSort": "Προσαρμοσμένη ταξινόμηση", "SSE.Views.DataTab.tipDataFromText": "Λήψη δεδομένων από αρχείο TXT/CSV", "SSE.Views.DataTab.tipDataValidation": "Επικύρωση δεδομένων", "SSE.Views.DataTab.tipGroup": "Ομαδοποίηση εύρους κελιών", "SSE.Views.DataTab.tipRemDuplicates": "Αφαίρεση διπλότυπων γραμμών από ένα φύλλο", "SSE.Views.DataTab.tipToColumns": "Διαχω<PERSON>ισμ<PERSON>ς κειμένου κελιού σε στήλες", "SSE.Views.DataTab.tipUngroup": "Κατάργηση ομαδοποίησης εύρους κελιών", "SSE.Views.DataValidationDialog.errorFormula": "Η τιμή οδηγεί σε σφάλμα με τον τρέχοντα υπολογισμό. Θέλετε να συνεχίσετε;", "SSE.Views.DataValidationDialog.errorInvalid": "Η τιμή που εισαγάγατε στο πεδίο \"{0}\" δεν είναι έγκυρη.", "SSE.Views.DataValidationDialog.errorInvalidDate": "Η ημερομηνία για το πεδίο \"{0}\" δεν είναι έγκυρη.", "SSE.Views.DataValidationDialog.errorInvalidList": "Η πηγή της λίστας πρέπει να είναι μια λίστα στοιχείων χωρισμένων με ειδικό χαρακτήρα ή μια αναφορά σε μια γραμμή ή στήλη.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Ο χρόνος που εισαγάγατε στο πεδίο \"{0}\" δεν είναι έγκυρος.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Το πεδίο \"{1}\" πρέπει να είναι μεγαλύτερο από ή ίσο με το πεδίο \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Πρέπει να βάλετε μια τιμή και στο πεδίο \"{0}\" και στο πεδίο \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Πρέπει να βάλετε μια τιμή στο πεδίο \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Ένα επώνυμο εύρος που ορίσατε δεν μπορεί να βρεθεί.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Αρνητικές τιμές δεν μπορούν να χρησιμοποιηθούν σε συνθήκες \"{0}\". ", "SSE.Views.DataValidationDialog.errorNotNumeric": "Το πεδίο \"{0}\" πρέπει να έχει αριθμητική τιμή, αριθμητική έκφραση ή να αναφέρεται σε κελί με αριθμητική τιμή.", "SSE.Views.DataValidationDialog.strError": "Ειδοποίη<PERSON>η Σφάλματος", "SSE.Views.DataValidationDialog.strInput": "Εισαγωγ<PERSON>νύματος", "SSE.Views.DataValidationDialog.strSettings": "Ρυθμίσεις", "SSE.Views.DataValidationDialog.textAlert": "Συναγ<PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.DataValidationDialog.textAllow": "Επιτρέπεται", "SSE.Views.DataValidationDialog.textApply": "Εφαρμογ<PERSON> αυτών των αλλα<PERSON>ών σε όλα τα κελιά με τις ίδιες ρυθμίσεις", "SSE.Views.DataValidationDialog.textCellSelected": "Όταν επιλέγεται κελί να εμφανίζεται αυτό το μήνυμα εισόδου", "SSE.Views.DataValidationDialog.textCompare": "Σύγκριση με", "SSE.Views.DataValidationDialog.textData": "Δεδομένα", "SSE.Views.DataValidationDialog.textEndDate": "Ημερομηνία Τέλους", "SSE.Views.DataValidationDialog.textEndTime": "Ώρα Τέλους", "SSE.Views.DataValidationDialog.textError": "Μήνυμα Λάθους", "SSE.Views.DataValidationDialog.textFormula": "Τύπος", "SSE.Views.DataValidationDialog.textIgnore": "Αγνόηση κενών", "SSE.Views.DataValidationDialog.textInput": "Εισαγωγ<PERSON>νύματος", "SSE.Views.DataValidationDialog.textMax": "Μέγιστο", "SSE.Views.DataValidationDialog.textMessage": "Μήνυμα", "SSE.Views.DataValidationDialog.textMin": "Ελάχιστο", "SSE.Views.DataValidationDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.DataValidationDialog.textShowDropDown": "Εμφάνιση αναδυόμενης λίστας σε κελί", "SSE.Views.DataValidationDialog.textShowError": "Εμφάνιση προειδοποίησης σφάλματος μετά την εισαγωγή μη έγκυρων δεδομένων", "SSE.Views.DataValidationDialog.textShowInput": "Εμφάνιση μηνύ<PERSON>α<PERSON>ος εισόδου κατά την επιλογή κελιού", "SSE.Views.DataValidationDialog.textSource": "Πηγή", "SSE.Views.DataValidationDialog.textStartDate": "Ημερομηνία Έναρξης", "SSE.Views.DataValidationDialog.textStartTime": "Ώρα Έναρξης", "SSE.Views.DataValidationDialog.textStop": "Διακοπή", "SSE.Views.DataValidationDialog.textStyle": "Τεχνοτροπία", "SSE.Views.DataValidationDialog.textTitle": "Τίτλος", "SSE.Views.DataValidationDialog.textUserEnters": "Όταν ο χρήστης εισάγει μη έγκυρα δεδομένα να εμφανίζεται αυτή η προειδοποίηση σφάλματος", "SSE.Views.DataValidationDialog.txtAny": "Οποιαδήποτε τιμή", "SSE.Views.DataValidationDialog.txtBetween": "μεταξύ", "SSE.Views.DataValidationDialog.txtDate": "Ημερομηνία", "SSE.Views.DataValidationDialog.txtDecimal": "Δεκαδικ<PERSON>ς", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON>ρ<PERSON><PERSON><PERSON> που πέρασε", "SSE.Views.DataValidationDialog.txtEndDate": "Ημερομηνία τέλους", "SSE.Views.DataValidationDialog.txtEndTime": "Ώρα τέλους", "SSE.Views.DataValidationDialog.txtEqual": "ισούται", "SSE.Views.DataValidationDialog.txtGreaterThan": "μεγαλύτερο από", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "μεγαλύτερο από ή ίσο με", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "μικρότερο από", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "μικρότερο από ή ίσο με", "SSE.Views.DataValidationDialog.txtList": "Λίστα", "SSE.Views.DataValidationDialog.txtNotBetween": "όχι ανάμεσα", "SSE.Views.DataValidationDialog.txtNotEqual": "δεν είναι ίσο με", "SSE.Views.DataValidationDialog.txtOther": "Άλλο", "SSE.Views.DataValidationDialog.txtStartDate": "Ημερομηνία έναρξης", "SSE.Views.DataValidationDialog.txtStartTime": "Ώρα έναρξης", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON><PERSON><PERSON> κειμένου", "SSE.Views.DataValidationDialog.txtTime": "Ώρα", "SSE.Views.DataValidationDialog.txtWhole": "Ολόκληρος αριθμός", "SSE.Views.DigitalFilterDialog.capAnd": "Και", "SSE.Views.DigitalFilterDialog.capCondition1": "ισούται", "SSE.Views.DigitalFilterDialog.capCondition10": "δεν τελειώνει με", "SSE.Views.DigitalFilterDialog.capCondition11": "περιέχει", "SSE.Views.DigitalFilterDialog.capCondition12": "δεν περιέχει", "SSE.Views.DigitalFilterDialog.capCondition2": "δεν είναι ίσο με", "SSE.Views.DigitalFilterDialog.capCondition3": "είναι μεγαλύτερο από", "SSE.Views.DigitalFilterDialog.capCondition4": "είναι μεγαλύτερο από ή ίσο με", "SSE.Views.DigitalFilterDialog.capCondition5": "είναι μικρότερο από", "SSE.Views.DigitalFilterDialog.capCondition6": "είναι μικρότερο από ή ίσο με", "SSE.Views.DigitalFilterDialog.capCondition7": "αρχίζει με", "SSE.Views.DigitalFilterDialog.capCondition8": "δεν ξεκινά με", "SSE.Views.DigitalFilterDialog.capCondition9": "τελειώνει με", "SSE.Views.DigitalFilterDialog.capOr": "Ή", "SSE.Views.DigitalFilterDialog.textNoFilter": "χωρ<PERSON>ς φίλτρο", "SSE.Views.DigitalFilterDialog.textShowRows": "Εμφάνιση γραμμών όπου", "SSE.Views.DigitalFilterDialog.textUse1": "Χρήση του ? για αναπαράσταση οποιουδήποτε χαρακτήρα", "SSE.Views.DigitalFilterDialog.textUse2": "Χρήση του * για αναπαράσταση οποιασδήποτε συμβολοσειράς", "SSE.Views.DigitalFilterDialog.txtTitle": "Προσαρμοσμένο Φίλτρο", "SSE.Views.DocumentHolder.advancedImgText": "Προηγμένες Ρυθμίσεις Εικόνας", "SSE.Views.DocumentHolder.advancedShapeText": "Προηγμένες Ρυθμίσεις Σχήματος", "SSE.Views.DocumentHolder.advancedSlicerText": "Προηγμένες Ρυθμίσεις Αναλυτή", "SSE.Views.DocumentHolder.bottomCellText": "Στοίχιση Κάτω", "SSE.Views.DocumentHolder.bulletsText": "Κουκκίδες και Αρίθμηση", "SSE.Views.DocumentHolder.centerCellText": "Στοίχιση στη Μέση", "SSE.Views.DocumentHolder.chartDataText": "Επιλογή Δεδομένων Γραφήματος", "SSE.Views.DocumentHolder.chartText": "Προηγμένες Ρυθμίσεις Γραφήματος", "SSE.Views.DocumentHolder.chartTypeText": "Αλλαγή Τύπου Γραφήματος", "SSE.Views.DocumentHolder.deleteColumnText": "Στήλη", "SSE.Views.DocumentHolder.deleteRowText": "Γραμμή", "SSE.Views.DocumentHolder.deleteTableText": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.DocumentHolder.direct270Text": "Περιστροφή Κειμένου Πάνω", "SSE.Views.DocumentHolder.direct90Text": "Περιστροφή Κειμένου Κάτω", "SSE.Views.DocumentHolder.directHText": "Οριζόντια", "SSE.Views.DocumentHolder.directionText": "Κατεύθυνση Κειμένου", "SSE.Views.DocumentHolder.editChartText": "Επεξεργασία Δεδομένων", "SSE.Views.DocumentHolder.editHyperlinkText": "Επεξεργασία Υπερσυνδέσμου", "SSE.Views.DocumentHolder.insertColumnLeftText": "Στήλη Αριστερά", "SSE.Views.DocumentHolder.insertColumnRightText": "Στήλη Δεξιά", "SSE.Views.DocumentHolder.insertRowAboveText": "Γραμμή Από Πάνω", "SSE.Views.DocumentHolder.insertRowBelowText": "Γραμμή Από <PERSON>ά<PERSON>ω", "SSE.Views.DocumentHolder.originalSizeText": "Πραγματικ<PERSON> Μέγεθος", "SSE.Views.DocumentHolder.removeHyperlinkText": "Αφαίρεση Υπερσυνδέσμου", "SSE.Views.DocumentHolder.selectColumnText": "Ολόκληρη Στήλη", "SSE.Views.DocumentHolder.selectDataText": "Δεδομέ<PERSON><PERSON>ήλης", "SSE.Views.DocumentHolder.selectRowText": "Γραμμή", "SSE.Views.DocumentHolder.selectTableText": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.DocumentHolder.strDelete": "Αφαίρεση Υπογραφής", "SSE.Views.DocumentHolder.strDetails": "Λεπτομέρειες Υπογραφής", "SSE.Views.DocumentHolder.strSetup": "Ρύθμιση Υπογραφής", "SSE.Views.DocumentHolder.strSign": "Σύμβολο", "SSE.Views.DocumentHolder.textAlign": "Στοίχιση", "SSE.Views.DocumentHolder.textArrange": "Τακτοποίηση", "SSE.Views.DocumentHolder.textArrangeBack": "Μεταφ<PERSON><PERSON><PERSON> στο Παρασκήνιο", "SSE.Views.DocumentHolder.textArrangeBackward": "Μεταφορά Προς τα Πίσω", "SSE.Views.DocumentHolder.textArrangeForward": "Μεταφορά Προς τα Εμπρός", "SSE.Views.DocumentHolder.textArrangeFront": "Μεταφ<PERSON><PERSON><PERSON> στο Προσκήνιο", "SSE.Views.DocumentHolder.textAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.DocumentHolder.textBullets": "Κουκκίδες", "SSE.Views.DocumentHolder.textCount": "Μέτρηση", "SSE.Views.DocumentHolder.textCrop": "Περικοπή", "SSE.Views.DocumentHolder.textCropFill": "Γέμισμα", "SSE.Views.DocumentHolder.textCropFit": "Προσαρμογή", "SSE.Views.DocumentHolder.textEditPoints": "Επεξεργασία Σημείων", "SSE.Views.DocumentHolder.textEntriesList": "Επιλογή από αναδυόμενη λίστα", "SSE.Views.DocumentHolder.textFlipH": "Οριζόντια Περιστροφή", "SSE.Views.DocumentHolder.textFlipV": "Κατακόρυφη Περιστροφή", "SSE.Views.DocumentHolder.textFreezePanes": "Πάγωμα Παραθύρων", "SSE.Views.DocumentHolder.textFromFile": "Από Αρχείο", "SSE.Views.DocumentHolder.textFromStorage": "Από Αποθηκευτικ<PERSON>ο", "SSE.Views.DocumentHolder.textFromUrl": "Από διεύθυνση URL", "SSE.Views.DocumentHolder.textListSettings": "Ρυθμίσεις Λίστας", "SSE.Views.DocumentHolder.textMacro": "Ανάθεση Μακροεντολής", "SSE.Views.DocumentHolder.textMax": "Μέγιστο", "SSE.Views.DocumentHolder.textMin": "Ελάχιστο", "SSE.Views.DocumentHolder.textMore": "Περισσότερες συναρτήσεις", "SSE.Views.DocumentHolder.textMoreFormats": "Περισσότερες μορφές", "SSE.Views.DocumentHolder.textNone": "Κανένα", "SSE.Views.DocumentHolder.textNumbering": "Αρίθμηση", "SSE.Views.DocumentHolder.textReplace": "Αντικατάσταση εικόνας", "SSE.Views.DocumentHolder.textRotate": "Περιστροφή", "SSE.Views.DocumentHolder.textRotate270": "Περιστροφή 90° Αριστερόστροφα", "SSE.Views.DocumentHolder.textRotate90": "Περιστροφή 90° Δεξιόστροφα", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Στοίχιση Κάτω", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Στοίχιση στο Κέντρο", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Στοίχιση Αριστερά", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Στοίχιση στη Μέση", "SSE.Views.DocumentHolder.textShapeAlignRight": "Στοίχιση Δεξιά", "SSE.Views.DocumentHolder.textShapeAlignTop": "Στοίχιση Πάνω", "SSE.Views.DocumentHolder.textStdDev": "Τυπική Απόκλιση", "SSE.Views.DocumentHolder.textSum": "Άθροισμα", "SSE.Views.DocumentHolder.textUndo": "Αναίρεση", "SSE.Views.DocumentHolder.textUnFreezePanes": "Απελευθέρωση Παραθύρων", "SSE.Views.DocumentHolder.textVar": "Διαφορά", "SSE.Views.DocumentHolder.tipMarkersArrow": "Κου<PERSON><PERSON><PERSON><PERSON>ς βέλη", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Κουκίδες τσεκαρίσματος", "SSE.Views.DocumentHolder.tipMarkersDash": "Κουκ<PERSON>δες παύλας", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Κου<PERSON><PERSON>δες πλήρους ρόμβου", "SSE.Views.DocumentHolder.tipMarkersFRound": "Κουκίδες πλήρεις στρογγυλές", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Κουκίδες πλήρεις τετράγωνες", "SSE.Views.DocumentHolder.tipMarkersHRound": "Κουκ<PERSON>δες κούφιες στρογγυλές", "SSE.Views.DocumentHolder.tipMarkersStar": "Κουκ<PERSON>δες αστέρια", "SSE.Views.DocumentHolder.topCellText": "Στοίχιση Πάνω", "SSE.Views.DocumentHolder.txtAccounting": "Λογιστική", "SSE.Views.DocumentHolder.txtAddComment": "Προσθήκη Σχολίου", "SSE.Views.DocumentHolder.txtAddNamedRange": "Προσδιορισμ<PERSON>ς Ονόματος", "SSE.Views.DocumentHolder.txtArrange": "Τακτοποίηση", "SSE.Views.DocumentHolder.txtAscending": "Αύξουσα", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Αυτόματη Προσαρμογή Πλάτους <PERSON>ήλης", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Αυτόματη Προσαρμογή Ύψους Γραμμής", "SSE.Views.DocumentHolder.txtClear": "Εκκαθάριση", "SSE.Views.DocumentHolder.txtClearAll": "Όλα", "SSE.Views.DocumentHolder.txtClearComments": "Σχόλια", "SSE.Views.DocumentHolder.txtClearFormat": "Μορφή", "SSE.Views.DocumentHolder.txtClearHyper": "Υπερσύνδεσμοι", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Εκκαθάριση Επιλεγμένων Ομάδων Μικρών Γραφημάτων (sparklines)", "SSE.Views.DocumentHolder.txtClearSparklines": "Εκκαθάριση Επιλεγμένων Μικρών Γραφημάτων (sparklines)", "SSE.Views.DocumentHolder.txtClearText": "Κείμενο", "SSE.Views.DocumentHolder.txtColumn": "Ολόκληρη στήλη", "SSE.Views.DocumentHolder.txtColumnWidth": "Ορισ<PERSON><PERSON><PERSON>τους Στήλης", "SSE.Views.DocumentHolder.txtCondFormat": "Μορφοποίηση Υπό Όρους", "SSE.Views.DocumentHolder.txtCopy": "Αντιγραφή", "SSE.Views.DocumentHolder.txtCurrency": "Νόμισμα", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Προσαρ<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Προσαρμοσμένο Ύψος Γραμμής", "SSE.Views.DocumentHolder.txtCustomSort": "Προσαρμοσμένη ταξινόμηση", "SSE.Views.DocumentHolder.txtCut": "Αποκοπή", "SSE.Views.DocumentHolder.txtDate": "Ημερομηνία", "SSE.Views.DocumentHolder.txtDelete": "Διαγραφή", "SSE.Views.DocumentHolder.txtDescending": "Φθίνουσα", "SSE.Views.DocumentHolder.txtDistribHor": "Οριζόντια Κατανομή", "SSE.Views.DocumentHolder.txtDistribVert": "Κατακόρυφη Κατανομή", "SSE.Views.DocumentHolder.txtEditComment": "Επεξεργασία Σχολίου", "SSE.Views.DocumentHolder.txtFilter": "Φίλτρο", "SSE.Views.DocumentHolder.txtFilterCellColor": "Φιλτράρισμα με χρώμα κελιού", "SSE.Views.DocumentHolder.txtFilterFontColor": "Φιλτράρισμα με χρώμα γραμματοσειράς", "SSE.Views.DocumentHolder.txtFilterValue": "Φιλτράρισμα με την τιμή του Επιλεγμένου κελιού", "SSE.Views.DocumentHolder.txtFormula": "Εισαγωγ<PERSON>τησης", "SSE.Views.DocumentHolder.txtFraction": "Κλάσμα", "SSE.Views.DocumentHolder.txtGeneral": "Γενικά", "SSE.Views.DocumentHolder.txtGroup": "Ομάδα", "SSE.Views.DocumentHolder.txtHide": "Απόκρυψη", "SSE.Views.DocumentHolder.txtInsert": "Εισαγωγή", "SSE.Views.DocumentHolder.txtInsHyperlink": "Υπερσύνδεσμος", "SSE.Views.DocumentHolder.txtNumber": "Αριθμός", "SSE.Views.DocumentHolder.txtNumFormat": "Μορφή Αριθμού", "SSE.Views.DocumentHolder.txtPaste": "Επικόλληση", "SSE.Views.DocumentHolder.txtPercentage": "Ποσοστό", "SSE.Views.DocumentHolder.txtReapply": "Εφαρμογή Ξανά", "SSE.Views.DocumentHolder.txtRow": "Ολόκληρη γραμμή", "SSE.Views.DocumentHolder.txtRowHeight": "Ορισμός Ύψους Γραμμής", "SSE.Views.DocumentHolder.txtScientific": "Επιστημονική", "SSE.Views.DocumentHolder.txtSelect": "Επιλογή", "SSE.Views.DocumentHolder.txtShiftDown": "Ολίσθηση κελιών κάτω", "SSE.Views.DocumentHolder.txtShiftLeft": "Ολίσθηση κελιών αριστερά", "SSE.Views.DocumentHolder.txtShiftRight": "Ολίσθηση κελιών δεξιά", "SSE.Views.DocumentHolder.txtShiftUp": "Ολίσθηση κελιών πάνω", "SSE.Views.DocumentHolder.txtShow": "Εμφάνιση", "SSE.Views.DocumentHolder.txtShowComment": "Εμφάνιση Σχολίου", "SSE.Views.DocumentHolder.txtSort": "Ταξινόμηση", "SSE.Views.DocumentHolder.txtSortCellColor": "Επιλεγμένο Χρώμα Κελιού στην κορυφή", "SSE.Views.DocumentHolder.txtSortFontColor": "Επιλεγμένο Χρώμα Γραμματοσειράς στην κορυφή", "SSE.Views.DocumentHolder.txtSparklines": "Μικρογραφήματα", "SSE.Views.DocumentHolder.txtText": "Κείμενο", "SSE.Views.DocumentHolder.txtTextAdvanced": "Προηγμένες Ρυθμίσεις Παραγράφου", "SSE.Views.DocumentHolder.txtTime": "Ώρα", "SSE.Views.DocumentHolder.txtUngroup": "Κατάργηση ομαδοποίησης", "SSE.Views.DocumentHolder.txtWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.vertAlignText": "Κατακόρυφη Στοίχιση", "SSE.Views.FieldSettingsDialog.strLayout": "Διάταξη", "SSE.Views.FieldSettingsDialog.strSubtotals": "Μερικά σύνολα", "SSE.Views.FieldSettingsDialog.textReport": "Φόρμα Αναφοράς", "SSE.Views.FieldSettingsDialog.textTitle": "Ρυθμίσεις Πεδίων", "SSE.Views.FieldSettingsDialog.txtAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.FieldSettingsDialog.txtBlank": "Εισαγω<PERSON><PERSON> κενών γραμμών μετά από κάθε στοιχείο", "SSE.Views.FieldSettingsDialog.txtBottom": "Εμφάνισης στο κάτω μέρος της ομάδας", "SSE.Views.FieldSettingsDialog.txtCompact": "Συμπαγές", "SSE.Views.FieldSettingsDialog.txtCount": "Μέτρηση", "SSE.Views.FieldSettingsDialog.txtCountNums": "Μέτρηση Αριθμών", "SSE.Views.FieldSettingsDialog.txtCustomName": "Προσαρμοσμένο όνομα", "SSE.Views.FieldSettingsDialog.txtEmpty": "Εμφάνιση στοιχείων χωρίς καθόλου δεδομένα", "SSE.Views.FieldSettingsDialog.txtMax": "Μέγιστο", "SSE.Views.FieldSettingsDialog.txtMin": "Ελάχιστο", "SSE.Views.FieldSettingsDialog.txtOutline": "Περίγραμμα", "SSE.Views.FieldSettingsDialog.txtProduct": "Γινόμενο", "SSE.Views.FieldSettingsDialog.txtRepeat": "Επανάληψη ετικετών στοιχείων σε κάθε γραμμή", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Εμφάνιση μερικών συνόλων", "SSE.Views.FieldSettingsDialog.txtSourceName": "Όνομα πηγής:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Τυπική Απόκλιση", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Τυπική απόκλιση πληθυσμού", "SSE.Views.FieldSettingsDialog.txtSum": "Άθροισμα", "SSE.Views.FieldSettingsDialog.txtSummarize": "Συναρτήσεις για Μερικά Σύνολα", "SSE.Views.FieldSettingsDialog.txtTabular": "Σε Μορφή Πίνακα", "SSE.Views.FieldSettingsDialog.txtTop": "Εμφάνιση στο πάνω μέρος της ομάδας", "SSE.Views.FieldSettingsDialog.txtVar": "Διαφορά", "SSE.Views.FieldSettingsDialog.txtVarp": "Διακύμανση πληθυσμού", "SSE.Views.FileMenu.btnBackCaption": "Άνοιγμα τοποθεσίας αρχείου", "SSE.Views.FileMenu.btnCloseMenuCaption": "Κλείσιμο <PERSON>ενού", "SSE.Views.FileMenu.btnCreateNewCaption": "Δημιουργ<PERSON>α Νέου", "SSE.Views.FileMenu.btnDownloadCaption": "Λήψη ως", "SSE.Views.FileMenu.btnExitCaption": "Κλείσιμο", "SSE.Views.FileMenu.btnFileOpenCaption": "Άνοιγμα", "SSE.Views.FileMenu.btnHelpCaption": "Βοήθεια", "SSE.Views.FileMenu.btnHistoryCaption": "Ιστορικ<PERSON> Εκδόσεων", "SSE.Views.FileMenu.btnInfoCaption": "Πληροφορίες Υπολογιστικού Φύλλου", "SSE.Views.FileMenu.btnPrintCaption": "Εκτύπωση", "SSE.Views.FileMenu.btnProtectCaption": "Προστασία", "SSE.Views.FileMenu.btnRecentFilesCaption": "Άνοιγμα Πρόσφατου", "SSE.Views.FileMenu.btnRenameCaption": "Μετονομασία", "SSE.Views.FileMenu.btnReturnCaption": "Πίσ<PERSON> στο Λογιστικό Φύλλο", "SSE.Views.FileMenu.btnRightsCaption": "Δικαιώματα Πρόσβασης", "SSE.Views.FileMenu.btnSaveAsCaption": "Αποθήκευση ως", "SSE.Views.FileMenu.btnSaveCaption": "Αποθήκευση", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Αποθήκευση Αντιγράφου ως", "SSE.Views.FileMenu.btnSettingsCaption": "Προηγμένες Ρυθμίσεις", "SSE.Views.FileMenu.btnToEditCaption": "Επεξεργασ<PERSON><PERSON> Λογιστικού Φύλλου", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Κενό Φύλλο Εργασίας", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Δημιουργ<PERSON>α Νέου", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Εφαρμογή", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Προσθήκη Συγγραφέα", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Προσθήκη Κειμένου", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Εφαρμογή", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Συγγραφέας", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Σχόλιο", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Δημιουργήθηκε", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Τελευταία Τροποποίηση Από", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Τελευταία Τροποποίηση", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Ιδιοκτήτης", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Τοποθεσία", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Άτομα που έχουν δικαιώματα", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Θέμα", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Τίτλος", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Μεταφορτώθηκε", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Άτομα που έχουν δικαιώματα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Εφαρμογή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Κατάσταση Συν-επεξεργασίας", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Διαχωριστικ<PERSON> δεκαδικού", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Γλώσσ<PERSON> λεξικού", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Γρήγ<PERSON><PERSON>η", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Βελτιστοποίηση Γραμματοσειράς", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Γλώσσα Τύπων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Παράδειγμα: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Αγνόηση λέξεων με ΚΕΦΑΛΑΙΑ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Αγνόηση λέξεων με αριθμούς", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Ρυθμίσεις Mακροεντολών", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Εμφάνιση κουμπιού Επιλογών <PERSON>πικόλλησης κατά την επικόλληση περιεχομένου", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Τεχνοτροπία Παραπομπών R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Τοπικές Ρυθμίσεις", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Παράδειγμα:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Εμφάνιση σχολίων στο φύλλο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Εμφάνιση επιλυμένων σχολίων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Αυστηρή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Θέμα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Διαχωριστικ<PERSON> χιλιάδων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Μονάδα Μέτρησης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Χρήση διαχω<PERSON><PERSON><PERSON><PERSON>ικών από τις τοπικές ρυθμίσεις", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Προεπιλεγμένη Τιμή Εστίασης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Κάθε 10 Λεπτά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Κάθε 30 Λεπτά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Κάθε 5 Λεπτά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Κάθε Ώρα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Αυτόματη ανάκτηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Αυτόματη αποθήκευση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Απενεργοποιημένο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Αποθήκευση ενδιάμεσων εκδόσεων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Κάθε Λεπτό", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Τεχνοτροπία Παραπομπών", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Επιλογ<PERSON>ς αυτόματης διόρθωσης...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Λευκ<PERSON>ρωσικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Βουλγάρικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Καταλανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Προεπιλεγμένη κατάσταση λανθάνουσας μνήμης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Εκατοστό", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Συνεργασία", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Τσέχικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Δανέζικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Γερμανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Επεξεργασία και αποθήκευση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Ελληνικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Αγγλικ<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Ισπανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Συν-επεξεργασία σε πραγματικό χρόνο. Όλες οι αλλαγές αποθηκεύονται αυτόματα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Φινλανδικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Γαλλικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Ουγγρικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Ινδονησιακά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Ίντσα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Ιταλικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Ιαπωνικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Κορεάτικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Λαοϊκά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Λετονικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "ως OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Εγγεν<PERSON>ς", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Νορβηγικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Ολλανδικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Πολωνικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Διόρθωση Κειμένου", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Σημείο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Πορ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Βραζιλίας)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Πορτ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Πορτογαλίας)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Περιοχή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Ρουμάνικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Ρώσικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Ενεργοποίηση Όλων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Ενεργοποίηση όλων των μακροεντολών χωρίς ειδοποίηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Σλοβάκικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Σλοβένικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Απενεργοποίηση όλων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Απενεργοποίηση όλων των μακροεντολών χωρίς ειδοποίηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Χρησιμοποιήστε το κουμπί \"Αποθήκευση\" για να συγχρονίσετε τις αλλαγές που κάνετε εσείς και άλλοι", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Σουηδικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Τουρκικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ουκρανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Χρησιμοποιήστε το πλήκτρο Alt για πλοήγηση στη διεπαφή χρήστη χρησιμοποιώντας το πληκτρολόγιο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Χρησιμοποιήστε το πλήκτρο επιλογής για πλοήγηση στη διεπαφή χρήστη χρησιμοποιώντας το πληκτρολόγιο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Βιετναμέζικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Εμφάνιση Ειδοποίησης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Απενεργοποίηση όλων των μακροεντολών με μια ειδοποίηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "ως Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON><PERSON><PERSON> εργασ<PERSON>ας", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Κινέζικα", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Με συνθηματικό", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Προστασία Υπολογιστικού Φύλλου", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Με υπογραφή", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Επεξεργα<PERSON><PERSON><PERSON> λογιστικού φύλλου", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Η επεξεργασία θα αφαιρέσει τις υπογραφές από το υπολογιστικό φύλλο.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Αυτό το υπολογιστικό φύλλο προστατεύτηκε με συνθηματικό", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Αυτό το υπολογιστικό φύλλο πρέπει να υπογραφεί.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Προστέθηκαν έγκυρες υπογραφές στο λογιστικό φύλλο. Το φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Κάποιες από τις ψηφιακές υπογραφές στο υπολογιστικό φύλλο δεν είναι έγκυρες ή δεν ήταν δυνατό να επιβεβαιωθούν. Το υπολογιστικό φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Προβολή υπογραφών", "SSE.Views.FormatRulesEditDlg.fillColor": "Χρώμα γεμίσματος", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.FormatRulesEditDlg.text2Scales": "Δίχρωμη κλίμακα", "SSE.Views.FormatRulesEditDlg.text3Scales": "Τρίχρωμη κλίμακα", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Όλα τα περιγράμματα", "SSE.Views.FormatRulesEditDlg.textAppearance": "Μορφή Μπάρας", "SSE.Views.FormatRulesEditDlg.textApply": "Εφαρμογή σε Εύρος", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Αυτόματα", "SSE.Views.FormatRulesEditDlg.textAxis": "Άξονας", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Κατεύθυνση Μπάρας", "SSE.Views.FormatRulesEditDlg.textBold": "Έντονα", "SSE.Views.FormatRulesEditDlg.textBorder": "Περίγραμμα", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Χρώμα Περιγραμμάτων", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Τεχνοτροπία Περιγράμματος", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Κάτω Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Δεν είναι δυνατή η προσθήκη της μορφοποίησης υπό όρους.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Μέσον κελιού", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Εσωτερ<PERSON><PERSON><PERSON>ατακόρυφα Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textClear": "Εκκαθάριση", "SSE.Views.FormatRulesEditDlg.textColor": "Χρώμα κειμένου", "SSE.Views.FormatRulesEditDlg.textContext": "Συμφραζόμενα", "SSE.Views.FormatRulesEditDlg.textCustom": "Προσαρμογή", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Δ<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON>τω Περίγραμμα", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Διαγ<PERSON><PERSON><PERSON><PERSON>ω Περίγραμμα", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Εισάγετε έναν έγκυρο τύπο.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Ο τύπος που εισάγατε δεν έχει τιμή αριθμού, ημερομην<PERSON>ας, ώρας ή συμβολοσειράς.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Εισάγετε μια τιμή.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Η τιμή που βάλατε δεν είναι έγκυρος αριθμός, ημερομην<PERSON><PERSON>, ώρα ή συμβολοσειρά.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Η τιμή για το {0} πρέπει να είναι μεγαλύτερη της τιμής για το {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Εισάγετε έναν αριθμό μεταξύ {0} και {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Γέμισμα", "SSE.Views.FormatRulesEditDlg.textFormat": "Μορφή", "SSE.Views.FormatRulesEditDlg.textFormula": "Τύπος", "SSE.Views.FormatRulesEditDlg.textGradient": "Βαθμωτό", "SSE.Views.FormatRulesEditDlg.textIconLabel": "όταν {0} {1} και", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "όταν {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "όταν η τιμή είναι", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Ένα ή περισσότερα εύρη δεδομένων εικονιδίων επικαλύπτονται.<br>Προ<PERSON><PERSON>ρμόστε τις τιμές των ευρών δεδομένων εικονιδίων ώστε να μην επικαλύπτονται.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Τεχνοτροπία Εικονιδίου", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Εσωτερικά Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textInvalid": "Μη έγκυρο εύρος δεδομένων.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.FormatRulesEditDlg.textItalic": "Πλάγια", "SSE.Views.FormatRulesEditDlg.textItem": "Αντικείμενο", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Αριστερ<PERSON> προς δεξιά", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Αριστερ<PERSON> Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textLongBar": "μακρύτερη μπάρα", "SSE.Views.FormatRulesEditDlg.textMaximum": "Μέγιστο", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Μέγιστο σημείο", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Εσωτερικά Οριζόντια Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Με<PERSON><PERSON><PERSON><PERSON> σημείο", "SSE.Views.FormatRulesEditDlg.textMinimum": "Ελάχιστο", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Ελάχιστο σημείο", "SSE.Views.FormatRulesEditDlg.textNegative": "Αρνητική", "SSE.Views.FormatRulesEditDlg.textNewColor": "Νέου Προσαρμοσμένου Χρώματος", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>ριγράμματα", "SSE.Views.FormatRulesEditDlg.textNone": "Κανένα", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Μία ή περισσότερες από τις καθορισμένες τιμές δεν είναι έγκυρο ποσοστό.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Η καθορισμένη {0} τιμή δεν είναι έγκυρο ποσοστό επί τοις εκατό.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Μία ή περισσότερες από τις καθορισμένες τιμές δεν είναι έγκυρη εκατοστιαία τιμή.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Η καθορισμένη {0} τιμή δεν είναι έγκυρη εκατοστιαία τιμή.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Εξωτερικά Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textPercent": "Επί τοις εκατό", "SSE.Views.FormatRulesEditDlg.textPercentile": "Εκατοστιαία τιμή", "SSE.Views.FormatRulesEditDlg.textPosition": "Θέση", "SSE.Views.FormatRulesEditDlg.textPositive": "Θετικ<PERSON>ς", "SSE.Views.FormatRulesEditDlg.textPresets": "Προεπιλογές", "SSE.Views.FormatRulesEditDlg.textPreview": "Προεπισκόπηση", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Δεν μπορείτε να χρησιμοποιήσετε σχετικές αναφορές στα κριτήρια μορφοποίησης υπό όρους για χρωματικές κλίμακες, μπάρες δεδομένων και σύνολα εικονιδίων.", "SSE.Views.FormatRulesEditDlg.textReverse": "Αντιστροφή Διάταξης Εικονιδίων", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Δεξιά προς αριστερά", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Δεξιά Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textRule": "Κανόνας", "SSE.Views.FormatRulesEditDlg.textSameAs": "Ίδιο με το θετικό", "SSE.Views.FormatRulesEditDlg.textSelectData": "Επιλογ<PERSON> Δεδομένων", "SSE.Views.FormatRulesEditDlg.textShortBar": "κοντύτερη μπάρα", "SSE.Views.FormatRulesEditDlg.textShowBar": "Εμφάνιση μπάρας μόνο", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Εμφάνιση εικονιδίου μόνο", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Αυτός ο τύπος αναφοράς δεν μπορεί να χρησιμοποιηθεί σε τύπο προσαρμοσμένης μορφοποίησης.<br>Αλλάξτε την αναφορά σε μεμονωμένο κελί, ή χρησιμοποιήστε την αναφορά σε μια συνάρτηση, όπως =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Συμπαγές", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Διαγραφή", "SSE.Views.FormatRulesEditDlg.textSubscript": "Δείκτης", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Εκθέτης", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Πάνω Περιγράμματα", "SSE.Views.FormatRulesEditDlg.textUnderline": "Υπογράμμιση", "SSE.Views.FormatRulesEditDlg.tipBorders": "Περιγράμματα", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Μορφή Αριθμού", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Λογιστική", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Νόμισμα", "SSE.Views.FormatRulesEditDlg.txtDate": "Ημερομηνία", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.FormatRulesEditDlg.txtFraction": "Κλάσμα", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Γενικά", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON>δ<PERSON>ο", "SSE.Views.FormatRulesEditDlg.txtNumber": "Αριθμός", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Ποσοστό επί τοις εκατό", "SSE.Views.FormatRulesEditDlg.txtScientific": "Επιστημονική", "SSE.Views.FormatRulesEditDlg.txtText": "Κείμενο", "SSE.Views.FormatRulesEditDlg.txtTime": "Ώρα", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Επεξεργασία Κανόνα Μορφοποίησης", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON> Μορφοποίησης", "SSE.Views.FormatRulesManagerDlg.guestText": "Επισκέπτης", "SSE.Views.FormatRulesManagerDlg.lockText": "Κλειδωμένο", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 τυπική απόκλιση πάνω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 τυπική απόκλιση κάτω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 τυπική απόκλιση πάνω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 τυπική απόκλιση κάτω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 τυπική απόκλιση πάνω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 τυπική απόκλιση κάτω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.textAbove": "Πάνω από τον μέσο όρο", "SSE.Views.FormatRulesManagerDlg.textApply": "Εφαρμογή σε", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Η τιμή του κελιού ξεκινά με", "SSE.Views.FormatRulesManagerDlg.textBelow": "Κάτω από τον μέσο όρο", "SSE.Views.FormatRulesManagerDlg.textBetween": "είναι μεταξύ {0] και {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Τιμή κελιού", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Βαθμωτή χρωματική κλίμακα", "SSE.Views.FormatRulesManagerDlg.textContains": "Η τιμή του κελιού περιέχει", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Το κελί περιέχει μια κενή τιμή", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Το κελί περιέχει σφάλμα", "SSE.Views.FormatRulesManagerDlg.textDelete": "Διαγραφή", "SSE.Views.FormatRulesManagerDlg.textDown": "Μετακίνηση κανόνα κάτω", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Διπλότυπες τιμές", "SSE.Views.FormatRulesManagerDlg.textEdit": "Επεξεργασία", "SSE.Views.FormatRulesManagerDlg.textEnds": "Η τιμή του κελιού τελειώνει με", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Ίσο με ή μεγαλύτερο του μέσου όρου", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Ίσο με ή μικρότερο του μέσου όρου", "SSE.Views.FormatRulesManagerDlg.textFormat": "Μορφή", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Σύνολο εικονιδίων", "SSE.Views.FormatRulesManagerDlg.textNew": "Νέο", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "δεν είναι μεταξύ {0} και {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Η τιμή του κελιού δεν περιέχει", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Το κελί δεν περιέχει μια κενή τιμή", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Το κελί δεν περιέχει σφάλμα", "SSE.Views.FormatRulesManagerDlg.textRules": "Κανόνες", "SSE.Views.FormatRulesManagerDlg.textScope": "Εμφάνιση κανόνων μορφοποίησης για ", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Επιλογή δεδομένων", "SSE.Views.FormatRulesManagerDlg.textSelection": "Τρέχουσα επιλογή", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Αυτ<PERSON><PERSON> ο συγκεντρωτικ<PERSON>ς πίνακας", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Αυτό το φύλλο εργασίας", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Αυτ<PERSON><PERSON> ο πίνακας", "SSE.Views.FormatRulesManagerDlg.textUnique": "Μοναδικές τιμές", "SSE.Views.FormatRulesManagerDlg.textUp": "Μετακίνηση κανόνα πάνω", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Μορφοποίηση Υπό Όρους", "SSE.Views.FormatSettingsDialog.textCategory": "Κατηγορία", "SSE.Views.FormatSettingsDialog.textDecimal": "Δεκαδικ<PERSON>ς", "SSE.Views.FormatSettingsDialog.textFormat": "Μορφή", "SSE.Views.FormatSettingsDialog.textLinked": "Συνδεδεμένο με την προέλευση", "SSE.Views.FormatSettingsDialog.textSeparator": "Χρήση διαχωριστή 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Σύμβολα", "SSE.Views.FormatSettingsDialog.textTitle": "Μορφή Αριθμού", "SSE.Views.FormatSettingsDialog.txtAccounting": "Λογιστική", "SSE.Views.FormatSettingsDialog.txtAs10": "Ως δέκατα (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Ως εκατοστά (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Ως δέκατα έκτα (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Ως δεύτερα (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Ως τέταρτα (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Ως όγδοα (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Νόμισμα", "SSE.Views.FormatSettingsDialog.txtCustom": "Προσαρμογή", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Παρακαλούμε εισάγετε προσεκτικά την προσαρμοσμένη μορφή αριθμού. Ο Συντάκτης Λογιστικού Φύλλου δεν ελέγχει τις προκαθορισμένες μορφές για λάθη που μπορεί να επηρεάσουν το αρχείο xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Ημερομηνία", "SSE.Views.FormatSettingsDialog.txtFraction": "Κλάσμα", "SSE.Views.FormatSettingsDialog.txtGeneral": "Γενικά", "SSE.Views.FormatSettingsDialog.txtNone": "Κανένα", "SSE.Views.FormatSettingsDialog.txtNumber": "Αριθμός", "SSE.Views.FormatSettingsDialog.txtPercentage": "Ποσοστό", "SSE.Views.FormatSettingsDialog.txtSample": "Δείγμα:", "SSE.Views.FormatSettingsDialog.txtScientific": "Επιστημονική", "SSE.Views.FormatSettingsDialog.txtText": "Κείμενο", "SSE.Views.FormatSettingsDialog.txtTime": "Ώρα", "SSE.Views.FormatSettingsDialog.txtUpto1": "Έως ένα ψηφίο (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Έως δύο ψηφία (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Έως τρία ψηφία (131/135)", "SSE.Views.FormulaDialog.sDescription": "Περιγραφή", "SSE.Views.FormulaDialog.textGroupDescription": "Επιλογή Ομάδας <PERSON>υναρτήσεων", "SSE.Views.FormulaDialog.textListDescription": "Επιλογή <PERSON>ν<PERSON>τησης", "SSE.Views.FormulaDialog.txtRecommended": "Προτεινόμενα", "SSE.Views.FormulaDialog.txtSearch": "Αναζήτηση", "SSE.Views.FormulaDialog.txtTitle": "Εισαγωγ<PERSON>τησης", "SSE.Views.FormulaTab.textAutomatic": "Αυτόματα", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Υπολογισμός τρέχοντος φύλλου εργασίας", "SSE.Views.FormulaTab.textCalculateWorkbook": "Υπολογισμός βιβλίου εργασίας", "SSE.Views.FormulaTab.textManual": "Χε<PERSON>ρ<PERSON>κ<PERSON>νητα", "SSE.Views.FormulaTab.tipCalculate": "Υπολογισμός", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Υπολογισμός ολόκληρου του βιβλίου εργασίας", "SSE.Views.FormulaTab.txtAdditional": "Επιπρόσθετα", "SSE.Views.FormulaTab.txtAutosum": "Αυτόματο Άθροισμα", "SSE.Views.FormulaTab.txtAutosumTip": "Άθροιση", "SSE.Views.FormulaTab.txtCalculation": "Υπολογισμός", "SSE.Views.FormulaTab.txtFormula": "Συνάρτηση", "SSE.Views.FormulaTab.txtFormulaTip": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.FormulaTab.txtMore": "Περισσότερες συναρτήσεις", "SSE.Views.FormulaTab.txtRecent": "Πρόσφατα χρησιμοποιημένα", "SSE.Views.FormulaWizard.textAny": "οποιοδήποτε", "SSE.Views.FormulaWizard.textArgument": "Όρισμα", "SSE.Views.FormulaWizard.textFunction": "Συνάρτηση", "SSE.Views.FormulaWizard.textFunctionRes": "Αποτέλεσμα συνάρτησης", "SSE.Views.FormulaWizard.textHelp": "Βοήθεια για αυτή τη συνάρτηση", "SSE.Views.FormulaWizard.textLogical": "λογικό", "SSE.Views.FormulaWizard.textNoArgs": "Η συνάρτηση δεν έχει ορίσματα", "SSE.Views.FormulaWizard.textNumber": "αριθμός", "SSE.Views.FormulaWizard.textRef": "παραπομπή", "SSE.Views.FormulaWizard.textText": "κείμενο", "SSE.Views.FormulaWizard.textTitle": "Ορίσματα <PERSON>νάρτησης", "SSE.Views.FormulaWizard.textValue": "Αποτέλεσμα μαθηματικού τύπου", "SSE.Views.HeaderFooterDialog.textAlign": "Στοίχιση με τα περιθώρια σελίδας", "SSE.Views.HeaderFooterDialog.textAll": "Όλες οι σελίδες", "SSE.Views.HeaderFooterDialog.textBold": "Έντονα", "SSE.Views.HeaderFooterDialog.textCenter": "Κέντρο", "SSE.Views.HeaderFooterDialog.textColor": "Χρώμα κειμένου", "SSE.Views.HeaderFooterDialog.textDate": "Ημερομηνία", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Διαφορετική πρώτη σελίδα", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Διαφορετικές μονές και ζυγές σελίδες", "SSE.Views.HeaderFooterDialog.textEven": "Ζυγή σελίδα", "SSE.Views.HeaderFooterDialog.textFileName": "Όνομα αρχείου", "SSE.Views.HeaderFooterDialog.textFirst": "Πρώτη σελίδα", "SSE.Views.HeaderFooterDialog.textFooter": "Υποσέλιδο", "SSE.Views.HeaderFooterDialog.textHeader": "Κεφαλίδα", "SSE.Views.HeaderFooterDialog.textInsert": "Εισαγωγή", "SSE.Views.HeaderFooterDialog.textItalic": "Πλάγια", "SSE.Views.HeaderFooterDialog.textLeft": "Αριστερά", "SSE.Views.HeaderFooterDialog.textMaxError": "Η συμβολοσειρά που εισαγάγατε είναι πολύ μεγάλη. Μειώστε τον αριθμό των χαρακτήρων.", "SSE.Views.HeaderFooterDialog.textNewColor": "Νέου Προσαρμοσμένου Χρώματος", "SSE.Views.HeaderFooterDialog.textOdd": "Μονή σελίδα", "SSE.Views.HeaderFooterDialog.textPageCount": "Αρίθμηση σελίδων", "SSE.Views.HeaderFooterDialog.textPageNum": "Αριθμός σελίδας", "SSE.Views.HeaderFooterDialog.textPresets": "Προεπιλογές", "SSE.Views.HeaderFooterDialog.textRight": "Δεξιά", "SSE.Views.HeaderFooterDialog.textScale": "Κλιμάκωση με το έγγραφο", "SSE.Views.HeaderFooterDialog.textSheet": "Όνομα φύλλου", "SSE.Views.HeaderFooterDialog.textStrikeout": "Διαγραφή", "SSE.Views.HeaderFooterDialog.textSubscript": "Δείκτης", "SSE.Views.HeaderFooterDialog.textSuperscript": "Εκθέτης", "SSE.Views.HeaderFooterDialog.textTime": "Ώρα", "SSE.Views.HeaderFooterDialog.textTitle": "Ρυθμίσεις Κεφαλίδας/Υποσέλιδου", "SSE.Views.HeaderFooterDialog.textUnderline": "Υπογράμμιση", "SSE.Views.HeaderFooterDialog.tipFontName": "Γραμματοσειρά", "SSE.Views.HeaderFooterDialog.tipFontSize": "Μέγ<PERSON><PERSON>ος γραμματοσειράς", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Προβολή", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Σύνδεσμος σε", "SSE.Views.HyperlinkSettingsDialog.strRange": "Εύρ<PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Φύλλο", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Αντιγραφή", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Επιλεγμένο εύρος", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Εισάγετε λεζάντα εδώ", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Εισάγετε σύνδεσμο εδώ", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Εισάγετε συμβουλή εδώ", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Εξωτερι<PERSON><PERSON>ς <PERSON>ύνδεσμος", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Λήψη Συνδέσμου", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Εσωτερικ<PERSON>ύρ<PERSON> Δ<PERSON>δο<PERSON>ένων", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.HyperlinkSettingsDialog.textNames": "Προσδιορισμένα ονόματα", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Φύλλα", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Κείμενο Υπόδειξης", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Ρυθμίσεις Υπερσυνδέσμου", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Αυτό το πεδίο πρέπει να είναι διεύθυνση URL με τη μορφή «http://www.example.com»", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Το πεδίο αυτό χωράει 2083 χαρακτήρες", "SSE.Views.ImageSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ImageSettings.textCrop": "Περικοπή", "SSE.Views.ImageSettings.textCropFill": "Γέμισμα", "SSE.Views.ImageSettings.textCropFit": "Προσαρμογή", "SSE.Views.ImageSettings.textCropToShape": "Περικο<PERSON>ή στο σχήμα", "SSE.Views.ImageSettings.textEdit": "Επεξεργασία", "SSE.Views.ImageSettings.textEditObject": "Επεξεργασία Αντικειμένου", "SSE.Views.ImageSettings.textFlip": "Περιστροφή", "SSE.Views.ImageSettings.textFromFile": "Από Αρχείο", "SSE.Views.ImageSettings.textFromStorage": "Από Αποθηκευτικ<PERSON>ο", "SSE.Views.ImageSettings.textFromUrl": "Από διεύθυνση URL", "SSE.Views.ImageSettings.textHeight": "Ύψος", "SSE.Views.ImageSettings.textHint270": "Περιστροφή 90° Αριστερόστροφα", "SSE.Views.ImageSettings.textHint90": "Περιστροφή 90° Δεξιόστροφα", "SSE.Views.ImageSettings.textHintFlipH": "Οριζόντια Περιστροφή", "SSE.Views.ImageSettings.textHintFlipV": "Κατακόρυφη Περιστροφή", "SSE.Views.ImageSettings.textInsert": "Αντικατάσταση εικόνας", "SSE.Views.ImageSettings.textKeepRatio": "Σταθερές αναλογίες", "SSE.Views.ImageSettings.textOriginalSize": "Πραγματικ<PERSON> Μέγεθος", "SSE.Views.ImageSettings.textRecentlyUsed": "Πρόσφατα Χρησιμοποιημένα", "SSE.Views.ImageSettings.textRotate90": "Περιστροφή 90°", "SSE.Views.ImageSettings.textRotation": "Περιστροφή", "SSE.Views.ImageSettings.textSize": "Μέγεθος", "SSE.Views.ImageSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.ImageSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.ImageSettingsAdvanced.textAngle": "Γωνία", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Περιεστρεμμένο  ", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Οριζόντια", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.ImageSettingsAdvanced.textRotation": "Περιστροφή", "SSE.Views.ImageSettingsAdvanced.textSnap": "Ευθυγράμμιση σε Κελί", "SSE.Views.ImageSettingsAdvanced.textTitle": "Εικόνα - Προηγμένες Ρυθμίσεις", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.ImageSettingsAdvanced.textVertically": "Κατακόρυφα", "SSE.Views.LeftMenu.tipAbout": "Περί", "SSE.Views.LeftMenu.tipChat": "Συνομιλία", "SSE.Views.LeftMenu.tipComments": "Σχόλια", "SSE.Views.LeftMenu.tipFile": "Αρχείο", "SSE.Views.LeftMenu.tipPlugins": "Πρόσθετα", "SSE.Views.LeftMenu.tipSearch": "Αναζήτηση", "SSE.Views.LeftMenu.tipSpellcheck": "Έλεγχος ορθογραφίας", "SSE.Views.LeftMenu.tipSupport": "Ανατροφοδότηση & Υποστήριξη", "SSE.Views.LeftMenu.txtDeveloper": "ΛΕΙΤΟΥΡΓΙΑ ΓΙΑ ΠΡΟΓΡΑΜΜΑΤΙΣΤΕΣ", "SSE.Views.LeftMenu.txtEditor": "Συντάκ<PERSON>η<PERSON>ιστικού Φύλλου", "SSE.Views.LeftMenu.txtLimit": "Περιορισμ<PERSON>ς Πρόσβασης", "SSE.Views.LeftMenu.txtTrial": "ΚΑΤΑΣΤΑΣΗ ΔΟΚΙΜΑΣΤΙΚΗΣ ΛΕΙΤΟΥΡΓΙΑΣ", "SSE.Views.LeftMenu.txtTrialDev": "Δοκιμαστική Λειτουργία για Προγραμματιστές", "SSE.Views.MacroDialog.textMacro": "Όνομα μακροεντολής", "SSE.Views.MacroDialog.textTitle": "Ανάθεση Μακροεντολής", "SSE.Views.MainSettingsPrint.okButtonText": "Αποθήκευση", "SSE.Views.MainSettingsPrint.strBottom": "Κάτω", "SSE.Views.MainSettingsPrint.strLandscape": "Οριζόντιος", "SSE.Views.MainSettingsPrint.strLeft": "Αριστερά", "SSE.Views.MainSettingsPrint.strMargins": "Περιθώρια", "SSE.Views.MainSettingsPrint.strPortrait": "Κατακόρυφος", "SSE.Views.MainSettingsPrint.strPrint": "Εκτύπωση", "SSE.Views.MainSettingsPrint.strPrintTitles": "Εκτύπωση Τίτλων", "SSE.Views.MainSettingsPrint.strRight": "Δεξιά", "SSE.Views.MainSettingsPrint.strTop": "Επάνω", "SSE.Views.MainSettingsPrint.textActualSize": "Πραγματικ<PERSON> Μέγεθος", "SSE.Views.MainSettingsPrint.textCustom": "Προσαρμογή", "SSE.Views.MainSettingsPrint.textCustomOptions": "Προσαρμοσμένες Επιλογές", "SSE.Views.MainSettingsPrint.textFitCols": "Προσαρμογή Όλων των Στηλών σε Μια Σελίδα", "SSE.Views.MainSettingsPrint.textFitPage": "Προσαρ<PERSON>ο<PERSON><PERSON> Φύλλου σε Μια Σελίδα", "SSE.Views.MainSettingsPrint.textFitRows": "Προσαρμογή Όλων των Γραμμών σε Μια Σελίδα", "SSE.Views.MainSettingsPrint.textPageOrientation": "Προσαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς Σελίδας", "SSE.Views.MainSettingsPrint.textPageScaling": "Κλίμακα", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.MainSettingsPrint.textPrintGrid": "Εκτύπωση Γραμμών Πλέγματος", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Εκτύπωση Επικεφαλίδων Γραμμών και Στηλών", "SSE.Views.MainSettingsPrint.textRepeat": "Επανάληψη...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.MainSettingsPrint.textRepeatTop": "Επανάληψη γραμμών στην κορυφή", "SSE.Views.MainSettingsPrint.textSettings": "Ρυθμίσεις για", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Δεν είναι δυνατή η επεξεργασία των υφιστάμενων επώνυμων ευρών και δεν είναι δυνατή η δημιουργία νέων<br>αυτ<PERSON><PERSON> τη στιγμή, καθ<PERSON><PERSON> ορισμένα από αυτά τελούν υπό επεξεργασία.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Προσδιορισμένο όνομα", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Βιβλίο Εργασίας", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON>ν", "SSE.Views.NamedRangeEditDlg.textExistName": "ΣΦΑΛΜΑ! Υπάρχει ήδη εύρος με αυτό το όνομα", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Το όνομα πρέπει να ξεκινάει με γράμμα ή κάτω παύλα και δεν πρέπει να περιέχει μη έγκυρους χαρακτήρες.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ΣΦΑΛΜΑ! Το στοιχείο αυτό τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.NamedRangeEditDlg.textName": "Όνομα", "SSE.Views.NamedRangeEditDlg.textReservedName": "Το όνομα που προσπαθείτε να χρησιμοποιήσετε αναφέρεται ήδη σε τύπους κελιών. Παρακαλούμε χρησιμοποιήστε κάποιο άλλο όνομα.", "SSE.Views.NamedRangeEditDlg.textScope": "Εμβέλεια", "SSE.Views.NamedRangeEditDlg.textSelectData": "Επιλογ<PERSON> Δεδομένων", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Επεξεργασία Ονόματος", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Νέο Όνομα", "SSE.Views.NamedRangePasteDlg.textNames": "Επώνυμα Εύρη ", "SSE.Views.NamedRangePasteDlg.txtTitle": "Επικόλληση Ονόματος", "SSE.Views.NameManagerDlg.closeButtonText": "Κλείσιμο", "SSE.Views.NameManagerDlg.guestText": "Επισκέπτης", "SSE.Views.NameManagerDlg.lockText": "Κλειδωμένο", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON>ν", "SSE.Views.NameManagerDlg.textDelete": "Διαγραφή", "SSE.Views.NameManagerDlg.textEdit": "Επεξεργασία", "SSE.Views.NameManagerDlg.textEmpty": "Δεν δημιουργήθηκαν ακόμα επώνυμα εύρη.<br>Δημιουργήστε τουλάχιστον ένα επώνυμο εύρος και θα εμφανιστεί σε αυτό το πεδίο.", "SSE.Views.NameManagerDlg.textFilter": "Φίλτρο", "SSE.Views.NameManagerDlg.textFilterAll": "Όλα", "SSE.Views.NameManagerDlg.textFilterDefNames": "Προσδιορισμένα ονόματα", "SSE.Views.NameManagerDlg.textFilterSheet": "Ονόματα Εμβέλειας εντός Φύλλου", "SSE.Views.NameManagerDlg.textFilterTableNames": "Ονόματα πίνακα", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Ονόματα Εμβέλειας εντός Βιβλίου Εργασίας", "SSE.Views.NameManagerDlg.textNew": "Νέο", "SSE.Views.NameManagerDlg.textnoNames": "Δεν βρέθηκαν επώνυμα εύρη που να ταιριάζουν στο φίλτρο σας.", "SSE.Views.NameManagerDlg.textRanges": "Επώνυμα Εύρη ", "SSE.Views.NameManagerDlg.textScope": "Εμβέλεια", "SSE.Views.NameManagerDlg.textWorkbook": "Βιβλίο Εργασίας", "SSE.Views.NameManagerDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.NameManagerDlg.txtTitle": "Διαχειριστής Ονομάτων", "SSE.Views.NameManagerDlg.warnDelete": "Θέλετε σίγουρα να διαγράψετε το όνομα {0};", "SSE.Views.PageMarginsDialog.textBottom": "Κάτω", "SSE.Views.PageMarginsDialog.textLeft": "Αριστερά", "SSE.Views.PageMarginsDialog.textRight": "Δεξιά", "SSE.Views.PageMarginsDialog.textTitle": "Περιθώρια", "SSE.Views.PageMarginsDialog.textTop": "Επάνω", "SSE.Views.ParagraphSettings.strLineHeight": "Διάστιχο", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Απόσταση Παραγράφων", "SSE.Views.ParagraphSettings.strSpacingAfter": "Μετά", "SSE.Views.ParagraphSettings.strSpacingBefore": "Πριν", "SSE.Views.ParagraphSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ParagraphSettings.textAt": "Στο", "SSE.Views.ParagraphSettings.textAtLeast": "Τουλάχιστον", "SSE.Views.ParagraphSettings.textAuto": "Πολλαπλό", "SSE.Views.ParagraphSettings.textExact": "Α<PERSON><PERSON>ιβ<PERSON>ς", "SSE.Views.ParagraphSettings.txtAutoText": "Αυτόματα", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Οι καθορισμένοι στηλοθέτες θα εμφανίζονται σε αυτό το πεδίο", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Όλα κεφαλαία", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Διπλή διαγραφή", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Εσοχ<PERSON>ς", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Αριστερά", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Διάστιχο", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Δεξιά", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Μετά", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Πριν", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Ειδική", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Από", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Γραμματοσειρά", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Εσο<PERSON><PERSON>ς & Αποστάσεις", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Μικρά κεφαλαία", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Απόσταση", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Διακριτική διαγραφή", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Δείκτης", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Εκθέτης", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Στηλοθέτες", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Στοίχιση", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Πολλαπλό", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Απόστα<PERSON>η Χαρακτήρων", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Προεπι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ηλοθέτης", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Εφέ", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Α<PERSON><PERSON>ιβ<PERSON>ς", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Πρώτη γραμμή", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Αρνητική", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Πλήρης στοίχιση", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(κανένα)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Αφαίρεση", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Αφαίρεση Όλων", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Προσδιορισμός", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Κέντρο", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Αριστερά", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Θέση Στηλοθέτη", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Δεξιά", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Παράγραφος - Προηγμένες Ρυθμίσεις", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Αυτόματα", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "ισούται", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "δεν τελειώνει με", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "περιέχει", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "δεν περιέχει", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "μεταξύ", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "όχι ανάμεσα", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "δεν είναι ίσο με", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "είναι μεγαλύτερο από", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "είναι μεγαλύτερο από ή ίσο με", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "είναι μικρότερο από", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "είναι μικρότερο από ή ίσο με", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "αρχίζει με", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "δεν ξεκινά με", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "τελειώνει με", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Εμφάνιση στοιχείων για τα οποία η ετικέτα:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Εμφάνιση στοιχείων για τα οποία:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Χρήση του ? για αναπαράσταση οποιουδήποτε χαρακτήρα", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Χρήση του * για αναπαράσταση οποιασδήποτε συμβολοσειράς", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "και", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Φίλτρ<PERSON>ας", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Φίλ<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "Αυτόματα", "SSE.Views.PivotGroupDialog.textBy": "Κατά", "SSE.Views.PivotGroupDialog.textDays": "Ημέρες", "SSE.Views.PivotGroupDialog.textEnd": "Τελειώνει στο", "SSE.Views.PivotGroupDialog.textError": "Το πεδίο αυτό πρέπει να έχει αριθμητική τιμή", "SSE.Views.PivotGroupDialog.textGreaterError": "Ο αριθμός τέλους πρέπει να είναι μεγαλύτερος από τον αριθμό αρχής", "SSE.Views.PivotGroupDialog.textHour": "Ώρες", "SSE.Views.PivotGroupDialog.textMin": "Λεπτά", "SSE.Views.PivotGroupDialog.textMonth": "Μήνες", "SSE.Views.PivotGroupDialog.textNumDays": "Αριθμ<PERSON>ς ημερών", "SSE.Views.PivotGroupDialog.textQuart": "Τετράμηνα", "SSE.Views.PivotGroupDialog.textSec": "Δευτερόλεπτα", "SSE.Views.PivotGroupDialog.textStart": "Αρχίζει στο", "SSE.Views.PivotGroupDialog.textYear": "Έτη", "SSE.Views.PivotGroupDialog.txtTitle": "Ομαδοποίηση", "SSE.Views.PivotSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.PivotSettings.textColumns": "Στήλες", "SSE.Views.PivotSettings.textFields": "Επιλογ<PERSON> Πεδίω<PERSON>", "SSE.Views.PivotSettings.textFilters": "Φίλτρα", "SSE.Views.PivotSettings.textRows": "Γραμμές", "SSE.Views.PivotSettings.textValues": "Τιμές", "SSE.Views.PivotSettings.txtAddColumn": "Προσθήκη στις Στήλες", "SSE.Views.PivotSettings.txtAddFilter": "Προσθήκη στα Φίλτρα", "SSE.Views.PivotSettings.txtAddRow": "Προσθήκη στις Γραμμές", "SSE.Views.PivotSettings.txtAddValues": "Προσθήκη στις Τιμές", "SSE.Views.PivotSettings.txtFieldSettings": "Ρυθμίσεις Πεδίων", "SSE.Views.PivotSettings.txtMoveBegin": "Μετακίνηση στην Αρχή", "SSE.Views.PivotSettings.txtMoveColumn": "Μετακίνηση στις Στήλες", "SSE.Views.PivotSettings.txtMoveDown": "Μετακίνηση Κάτω", "SSE.Views.PivotSettings.txtMoveEnd": "Μετακίνηση στο Τέλος", "SSE.Views.PivotSettings.txtMoveFilter": "Μετακίνηση στα Φίλτρα", "SSE.Views.PivotSettings.txtMoveRow": "Μετακίνηση στις Γραμμές", "SSE.Views.PivotSettings.txtMoveUp": "Μετακίνηση Πάνω", "SSE.Views.PivotSettings.txtMoveValues": "Μετακίνηση στις Τιμές", "SSE.Views.PivotSettings.txtRemove": "Αφαίρεση Πεδίου", "SSE.Views.PivotSettingsAdvanced.strLayout": "Όνομα και Διάταξη", "SSE.Views.PivotSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON><PERSON><PERSON>ν", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Πηγή Δεδομένων", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Εμφάνιση πεδίων στην περιοχή φίλτρων της αναφοράς", "SSE.Views.PivotSettingsAdvanced.textDown": "Κ<PERSON>τ<PERSON>, μετά πάνω από", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Τελικά Σύνολα", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Κεφα<PERSON><PERSON><PERSON><PERSON><PERSON>ν", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON><PERSON><PERSON>, μετά κάτω", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Επιλογή δεδομένων", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Εμφάνιση για στήλες", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Εμφάνιση κεφαλίδων πεδίων για γραμμές και στήλες", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Εμφάνιση για γραμμές", "SSE.Views.PivotSettingsAdvanced.textTitle": "Συγκεντρωτι<PERSON><PERSON>ς <PERSON>ίνακας - Προηγμένες Ρυθμίσεις", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Πεδία φίλτρων ανα<PERSON><PERSON><PERSON><PERSON><PERSON> ανά στήλη", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Πεδία φίλτρων αναφ<PERSON><PERSON><PERSON>ς ανά γραμμή", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.PivotSettingsAdvanced.txtName": "Όνομα", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.PivotTable.capGrandTotals": "Τελικά Σύνολα", "SSE.Views.PivotTable.capLayout": "Διάταξη Αναφοράς", "SSE.Views.PivotTable.capSubtotals": "Μερικά σύνολα", "SSE.Views.PivotTable.mniBottomSubtotals": "Εμφάνιση όλων των Μερικών Συνόλων στο Κάτω Μέρος της Ομάδας", "SSE.Views.PivotTable.mniInsertBlankLine": "Εισαγωγ<PERSON> Κενής Γραμμής μετά από Κάθε Στοιχείο", "SSE.Views.PivotTable.mniLayoutCompact": "Εμφάνιση σε Συμπαγή Μορφή", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Να Μην Επαναλαμβάνεται Καμία Ετικέτα Στοιχείων", "SSE.Views.PivotTable.mniLayoutOutline": "Προβολή ως Φόρμα Διάρθρωσης", "SSE.Views.PivotTable.mniLayoutRepeat": "Επανάληψη Όλων των Ετικετών Στοιχείων", "SSE.Views.PivotTable.mniLayoutTabular": "Εμφάνιση σε Φόρμα Πίνακα", "SSE.Views.PivotTable.mniNoSubtotals": "Να Μην Εμφανίζονται Μερικά Σύνολα", "SSE.Views.PivotTable.mniOffTotals": "Απενεργοποίηση για Γραμμές και Στήλες", "SSE.Views.PivotTable.mniOnColumnsTotals": "Ενεργοποίηση Μόνο για Στήλες", "SSE.Views.PivotTable.mniOnRowsTotals": "Ενεργοποίηση Μόνο για Γραμμές", "SSE.Views.PivotTable.mniOnTotals": "Ενεργοποίηση για Γραμμές και Στήλες", "SSE.Views.PivotTable.mniRemoveBlankLine": "Αφαίρεση Κενής Γραμμής μετά από Κάθε Στοιχείο", "SSE.Views.PivotTable.mniTopSubtotals": "Εμφάνιση όλων των Μερικών Συνόλων στο Πάνω Μέρος της Ομάδας", "SSE.Views.PivotTable.textColBanded": "Στήλες με Εναλλαγή Σκίασης", "SSE.Views.PivotTable.textColHeader": "Κε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.PivotTable.textRowBanded": "Γρα<PERSON><PERSON><PERSON>ς με Εναλλαγή Σκίασης", "SSE.Views.PivotTable.textRowHeader": "Κεφαλ<PERSON>δ<PERSON><PERSON>ν", "SSE.Views.PivotTable.tipCreatePivot": "Εισαγωγ<PERSON>υγκεντρωτικού Πίνακα", "SSE.Views.PivotTable.tipGrandTotals": "Εμφάνιση ή απόκρυψη τελικών συνόλων", "SSE.Views.PivotTable.tipRefresh": "Ενημέρωση πληροφοριών από πηγή δεδομένων", "SSE.Views.PivotTable.tipSelect": "Επιλογή ολόκληρου συγκεντρωτικού πίνακα", "SSE.Views.PivotTable.tipSubtotals": "Εμφάνιση ή απόκρυψη μερικών συνόλων", "SSE.Views.PivotTable.txtCreate": "Εισαγωγή <PERSON>ίνακα", "SSE.Views.PivotTable.txtPivotTable": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.PivotTable.txtRefresh": "Ανανέωση", "SSE.Views.PivotTable.txtSelect": "Επιλογή", "SSE.Views.PrintSettings.btnDownload": "Αποθήκευση & Λήψη", "SSE.Views.PrintSettings.btnPrint": "Αποθήκευση & Εκτύπωση", "SSE.Views.PrintSettings.strBottom": "Κάτω", "SSE.Views.PrintSettings.strLandscape": "Οριζόντιος", "SSE.Views.PrintSettings.strLeft": "Αριστερά", "SSE.Views.PrintSettings.strMargins": "Περιθώρια", "SSE.Views.PrintSettings.strPortrait": "Κατακόρυφος", "SSE.Views.PrintSettings.strPrint": "Εκτύπωση", "SSE.Views.PrintSettings.strPrintTitles": "Εκτύπωση Τίτλων", "SSE.Views.PrintSettings.strRight": "Δεξιά", "SSE.Views.PrintSettings.strShow": "Εμφάνιση", "SSE.Views.PrintSettings.strTop": "Επάνω", "SSE.Views.PrintSettings.textActualSize": "Πραγματικ<PERSON> Μέγεθος", "SSE.Views.PrintSettings.textAllSheets": "Όλα τα Φύλλα", "SSE.Views.PrintSettings.textCurrentSheet": "Τρέχον Φύλλο", "SSE.Views.PrintSettings.textCustom": "Προσαρμογή", "SSE.Views.PrintSettings.textCustomOptions": "Προσαρμοσμένες Επιλογές", "SSE.Views.PrintSettings.textFitCols": "Προσαρμογή Όλων των Στηλών σε Μια Σελίδα", "SSE.Views.PrintSettings.textFitPage": "Προσαρ<PERSON>ο<PERSON><PERSON> Φύλλου σε Μια Σελίδα", "SSE.Views.PrintSettings.textFitRows": "Προσαρμογή Όλων των Γραμμών σε Μια Σελίδα", "SSE.Views.PrintSettings.textHideDetails": "Λιγότερα...", "SSE.Views.PrintSettings.textIgnore": "Αγνόηση Περιοχής Εκτύπωσης", "SSE.Views.PrintSettings.textLayout": "Διάταξη", "SSE.Views.PrintSettings.textPageOrientation": "Προσαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς Σελίδας", "SSE.Views.PrintSettings.textPageScaling": "Κλίμακα", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.PrintSettings.textPrintGrid": "Εκτύπωση Γραμμών Πλέγματος", "SSE.Views.PrintSettings.textPrintHeadings": "Εκτύπωση Επικεφαλίδων Γραμμών και Στηλών", "SSE.Views.PrintSettings.textPrintRange": "Εκτύπωση Εύρους", "SSE.Views.PrintSettings.textRange": "Εύρ<PERSON>", "SSE.Views.PrintSettings.textRepeat": "Επανάληψη...", "SSE.Views.PrintSettings.textRepeatLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.PrintSettings.textRepeatTop": "Επανάληψη γραμμών στην κορυφή", "SSE.Views.PrintSettings.textSelection": "Επιλογή", "SSE.Views.PrintSettings.textSettings": "Ρυθμίσεις Φύλλου", "SSE.Views.PrintSettings.textShowDetails": "Περισσότερα...", "SSE.Views.PrintSettings.textShowGrid": "Εμφάνιση Γραμμών Πλέγματος", "SSE.Views.PrintSettings.textShowHeadings": "Εμφάνιση Επικεφαλίδων Γραμμών και Στηλών", "SSE.Views.PrintSettings.textTitle": "Ρυθμίσεις Εκτύπωσης", "SSE.Views.PrintSettings.textTitlePDF": "Ρυθμίσεις PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "Πρώτη στήλη", "SSE.Views.PrintTitlesDialog.textFirstRow": "Πρώτη γραμμή", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Παγωμένες στήλες", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Παγωμένες γραμμές", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.PrintTitlesDialog.textLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Να μην επαναλαμβάνεται", "SSE.Views.PrintTitlesDialog.textRepeat": "Επανάληψη...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Επιλογή εύρους", "SSE.Views.PrintTitlesDialog.textTitle": "Εκτύπωση Τίτλων", "SSE.Views.PrintTitlesDialog.textTop": "Επανάληψη γραμμών στην κορυφή", "SSE.Views.PrintWithPreview.txtActualSize": "Πραγματικ<PERSON> Μέγεθος", "SSE.Views.PrintWithPreview.txtAllSheets": "Όλα τα Φύλλα", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Εφαρμογή σε όλα τα φύλλα", "SSE.Views.PrintWithPreview.txtBottom": "Κάτ<PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Τρέχον φύλλο", "SSE.Views.PrintWithPreview.txtCustom": "Προσαρμογή", "SSE.Views.PrintWithPreview.txtCustomOptions": "Επιλο<PERSON><PERSON><PERSON> Προσαρμογής", "SSE.Views.PrintWithPreview.txtEmptyTable": "Δεν υπάρχει κάτι για εκτύπωση επειδή ο πίνακας είναι άδειος", "SSE.Views.PrintWithPreview.txtFitCols": "Προσαρμογή Όλων των Στηλών σε Μια Σελίδα", "SSE.Views.PrintWithPreview.txtFitPage": "Προσαρ<PERSON>ο<PERSON><PERSON> Φύλλου σε Μία Σελίδα", "SSE.Views.PrintWithPreview.txtFitRows": "Προσαρμογή Όλων των Σειρών σε Μια Σελίδα", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Γραμ<PERSON><PERSON>ς πλέγματος και επικεφαλίδες", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Ρυθμίσεις Κεφαλίδας/Υποσέλιδου", "SSE.Views.PrintWithPreview.txtIgnore": "Αγνόηση περιοχής εκτύπωσης", "SSE.Views.PrintWithPreview.txtLandscape": "Οριζόντιος", "SSE.Views.PrintWithPreview.txtLeft": "Αριστερά", "SSE.Views.PrintWithPreview.txtMargins": "Περιθώρια", "SSE.Views.PrintWithPreview.txtOf": "του {0}", "SSE.Views.PrintWithPreview.txtPage": "Σελίδα", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Μη έγκυρος αριθμός σελίδας", "SSE.Views.PrintWithPreview.txtPageOrientation": "Προσανα<PERSON><PERSON><PERSON>ισμ<PERSON>ς σελίδας", "SSE.Views.PrintWithPreview.txtPageSize": "Μέγ<PERSON><PERSON>ος σελίδας", "SSE.Views.PrintWithPreview.txtPortrait": "Κατακόρυφος", "SSE.Views.PrintWithPreview.txtPrint": "Εκτύπωση", "SSE.Views.PrintWithPreview.txtPrintGrid": "Εκτύπωση γραμμών πλέγματος", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Εκτύπωση επικεφαλίδων σειρών και στηλών", "SSE.Views.PrintWithPreview.txtPrintRange": "Εκτύπωση εύρους", "SSE.Views.PrintWithPreview.txtPrintTitles": "Εκτύπωση τίτλων", "SSE.Views.PrintWithPreview.txtRepeat": "Επανάληψη...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Επανάληψη σειρών στην κορυφή", "SSE.Views.PrintWithPreview.txtRight": "Δεξιά", "SSE.Views.PrintWithPreview.txtSave": "Αποθήκευση", "SSE.Views.PrintWithPreview.txtScaling": "Κλίμακα", "SSE.Views.PrintWithPreview.txtSelection": "Επιλογή", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Ρυθμίσεις φύλλου", "SSE.Views.PrintWithPreview.txtSheet": "Φύλλο: {0}", "SSE.Views.PrintWithPreview.txtTop": "Κορυφή", "SSE.Views.ProtectDialog.textExistName": "ΣΦΑΛΜΑ! Υπάρχει ήδη εύρος με αυτόν τον τίτλο", "SSE.Views.ProtectDialog.textInvalidName": "Ο τίτλος εύρους πρέπει να ξεκινά με γράμμα και να περιέχει μόνο γράμματα, αριθμούς και διαστήματα.", "SSE.Views.ProtectDialog.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.ProtectDialog.textSelectData": "Επιλογ<PERSON> Δεδομένων", "SSE.Views.ProtectDialog.txtAllow": "Επιτρέπεται σε όλους τους χρήστες του φύλλου να", "SSE.Views.ProtectDialog.txtAutofilter": "Χρήση Αυτόματου Φίλτρου", "SSE.Views.ProtectDialog.txtDelCols": "Διαγρα<PERSON><PERSON> στηλών", "SSE.Views.ProtectDialog.txtDelRows": "Διαγραφή γραμμών", "SSE.Views.ProtectDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.ProtectDialog.txtFormatCells": "Μορφοποίηση κελιών", "SSE.Views.ProtectDialog.txtFormatCols": "Μορφοποίη<PERSON>η στηλών", "SSE.Views.ProtectDialog.txtFormatRows": "Μορφοποίηση γραμμών", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Το συνθηματικ<PERSON> επιβεβαίωσης δεν είναι πανομοιότυπο", "SSE.Views.ProtectDialog.txtInsCols": "Εισαγω<PERSON><PERSON> στηλών", "SSE.Views.ProtectDialog.txtInsHyper": "Εισαγωγή υπερυνδέσμου", "SSE.Views.ProtectDialog.txtInsRows": "Εισαγωγή γραμμών", "SSE.Views.ProtectDialog.txtObjs": "Επεξεργα<PERSON><PERSON>α αντικειμένων", "SSE.Views.ProtectDialog.txtOptional": "προαιρετικό", "SSE.Views.ProtectDialog.txtPassword": "Συνθηματικό", "SSE.Views.ProtectDialog.txtPivot": "Χρήση Συγκεντρωτικού Πίνακα και Συγκεντρωτικού Γραφήματος", "SSE.Views.ProtectDialog.txtProtect": "Προστασία", "SSE.Views.ProtectDialog.txtRange": "Εύρ<PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "Τίτλος", "SSE.Views.ProtectDialog.txtRepeat": "Επανάληψη συνθηματικού", "SSE.Views.ProtectDialog.txtScen": "Επεξεργα<PERSON><PERSON>α σεναρίων", "SSE.Views.ProtectDialog.txtSelLocked": "Επιλογή κλειδωμένων κελιών", "SSE.Views.ProtectDialog.txtSelUnLocked": "Επιλογή ξεκλείδωτων κελιών", "SSE.Views.ProtectDialog.txtSheetDescription": "Εμποδίστε ανεπιθύμητες αλλαγές από τρίτους περιορίζοντας τη δυνατότητα επεξεργασίας.", "SSE.Views.ProtectDialog.txtSheetTitle": "Προστα<PERSON><PERSON><PERSON> Φύλλου", "SSE.Views.ProtectDialog.txtSort": "Ταξινόμηση", "SSE.Views.ProtectDialog.txtWarning": "Προειδοποίηση: <PERSON><PERSON><PERSON> χάσετε ή ξεχάσετε το συνθηματικ<PERSON>, δεν είναι δυνατή η ανάκτησή του. Παρακαλούμε διατηρήστε το σε ασφαλές μέρος.", "SSE.Views.ProtectDialog.txtWBDescription": "Για να αποτρέψετε άλλους χρήστες από το να βλέπουν κρυφά φύλλα εργασίας, να προσθέτουν, μ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, δι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, κρύβ<PERSON>υν και μετονομάζουν φύλλα εργασίας, μπορείτε να προστατέψετε τη δομή του βιβλίου εργασίας σας με ένα συνθηματικό.", "SSE.Views.ProtectDialog.txtWBTitle": "Προστασί<PERSON> δομής Βιβλίου Εργασίας", "SSE.Views.ProtectRangesDlg.guestText": "Επισκέπτης", "SSE.Views.ProtectRangesDlg.lockText": "Κλειδωμένο", "SSE.Views.ProtectRangesDlg.textDelete": "Διαγραφή", "SSE.Views.ProtectRangesDlg.textEdit": "Επεξεργασία", "SSE.Views.ProtectRangesDlg.textEmpty": "Δεν επιτρέπεται η επεξεργασία κανενός εύρους.", "SSE.Views.ProtectRangesDlg.textNew": "Νέο", "SSE.Views.ProtectRangesDlg.textProtect": "Προστα<PERSON><PERSON><PERSON> Φύλλου", "SSE.Views.ProtectRangesDlg.textPwd": "Συνθηματικό", "SSE.Views.ProtectRangesDlg.textRange": "Εύρ<PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Εύρη που ξεκλειδώνουν με συνθηματικ<PERSON> όταν το φύλλο είναι προστατευμένο (δουλεύει μόνο για κλειδωμένα κελιά)", "SSE.Views.ProtectRangesDlg.textTitle": "Τίτλος", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Επεξεργασία Εύρους", "SSE.Views.ProtectRangesDlg.txtNewRange": "<PERSON>έ<PERSON>", "SSE.Views.ProtectRangesDlg.txtNo": "Όχι", "SSE.Views.ProtectRangesDlg.txtTitle": "Επιτρέπεται στους Χρήστες Επεξεργασία Ευρών", "SSE.Views.ProtectRangesDlg.txtYes": "Ναι", "SSE.Views.ProtectRangesDlg.warnDelete": "Θέλετε σίγουρα να διαγράψετε το όνομα {0};", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Στήλες", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Για να διαγράψετε διπλότυπες τιμές, επιλέξτε μία ή περισσότερες στήλες που περιέχουν διπλότυπα.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Τα δεδομένα μου έχουν κεφαλίδες", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Επιλογή Όλων ", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Αφαίρεση Διπλότυπων", "SSE.Views.RightMenu.txtCellSettings": "Ρυθμίσεις κελιού", "SSE.Views.RightMenu.txtChartSettings": "Ρυθμίσεις γραφήματος", "SSE.Views.RightMenu.txtImageSettings": "Ρυθμίσεις εικόνας", "SSE.Views.RightMenu.txtParagraphSettings": "Ρυθμίσεις παραγράφου", "SSE.Views.RightMenu.txtPivotSettings": "Ρυθμίσεις Συγκεντρωτικού Πίνακα", "SSE.Views.RightMenu.txtSettings": "Κοινές Ρυθμίσεις", "SSE.Views.RightMenu.txtShapeSettings": "Ρυθμίσεις σχήματος", "SSE.Views.RightMenu.txtSignatureSettings": "Ρυθμίσεις υπογραφής", "SSE.Views.RightMenu.txtSlicerSettings": "Ρυθμίσεις αναλυτή", "SSE.Views.RightMenu.txtSparklineSettings": "Ρυθμίσεις μικρογραφήματος", "SSE.Views.RightMenu.txtTableSettings": "Ρυθμίσεις πίνακα", "SSE.Views.RightMenu.txtTextArtSettings": "Ρυθμίσεις καλλιτεχνικού κειμένου", "SSE.Views.ScaleDialog.textAuto": "Αυτόματα", "SSE.Views.ScaleDialog.textError": "Η εισηγμένη τιμή είναι εσφαλμένη.", "SSE.Views.ScaleDialog.textFewPages": "σελίδες", "SSE.Views.ScaleDialog.textFitTo": "Προσαρμογή Σε", "SSE.Views.ScaleDialog.textHeight": "Ύψος", "SSE.Views.ScaleDialog.textManyPages": "σελίδες", "SSE.Views.ScaleDialog.textOnePage": "σελίδα", "SSE.Views.ScaleDialog.textScaleTo": "Κλιμάκωση Σε", "SSE.Views.ScaleDialog.textTitle": "Ρυθμίσεις Κλίμακας", "SSE.Views.ScaleDialog.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "Η μέγιστη τιμή για αυτό το πεδίο είναι {0}", "SSE.Views.SetValueDialog.txtMinText": "Η ελάχιστη τιμή για αυτό το πεδίο είναι {0}", "SSE.Views.ShapeSettings.strBackground": "Χρώμα παρασκηνίου", "SSE.Views.ShapeSettings.strChange": "Αλλαγή Αυτόματου Σχήματος", "SSE.Views.ShapeSettings.strColor": "Χρώμα", "SSE.Views.ShapeSettings.strFill": "Γέμισμα", "SSE.Views.ShapeSettings.strForeground": "Χρώμα προσκηνίου", "SSE.Views.ShapeSettings.strPattern": "Μοτίβο", "SSE.Views.ShapeSettings.strShadow": "Εμφάνιση σκιάς", "SSE.Views.ShapeSettings.strSize": "Μέγεθος", "SSE.Views.ShapeSettings.strStroke": "Πινελιά", "SSE.Views.ShapeSettings.strTransparency": "Αδιαφάνεια", "SSE.Views.ShapeSettings.strType": "Τύπος", "SSE.Views.ShapeSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ShapeSettings.textAngle": "Γωνία", "SSE.Views.ShapeSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 pt και 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Γέμισμα με Χρώμα", "SSE.Views.ShapeSettings.textDirection": "Κατεύθυνση", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "Περιστροφή", "SSE.Views.ShapeSettings.textFromFile": "Από Αρχείο", "SSE.Views.ShapeSettings.textFromStorage": "Από Αποθηκευτικ<PERSON>ο", "SSE.Views.ShapeSettings.textFromUrl": "Από διεύθυνση URL", "SSE.Views.ShapeSettings.textGradient": "Σημεία διαβάθμισης", "SSE.Views.ShapeSettings.textGradientFill": "Βαθμωτό Γέμισμα", "SSE.Views.ShapeSettings.textHint270": "Περιστροφή 90° Αριστερόστροφα", "SSE.Views.ShapeSettings.textHint90": "Περιστροφή 90° Δεξιόστροφα", "SSE.Views.ShapeSettings.textHintFlipH": "Οριζόντια Περιστροφή", "SSE.Views.ShapeSettings.textHintFlipV": "Κατακόρυφη Περιστροφή", "SSE.Views.ShapeSettings.textImageTexture": "Εικόνα ή Υφή", "SSE.Views.ShapeSettings.textLinear": "Γραμμικός", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON>α", "SSE.Views.ShapeSettings.textOriginalSize": "Αρχικό Μέγεθος", "SSE.Views.ShapeSettings.textPatternFill": "Μοτίβο", "SSE.Views.ShapeSettings.textPosition": "Θέση", "SSE.Views.ShapeSettings.textRadial": "Ακτινικ<PERSON>ς", "SSE.Views.ShapeSettings.textRecentlyUsed": "Πρόσφατα Χρησιμοποιημένα", "SSE.Views.ShapeSettings.textRotate90": "Περιστροφή 90°", "SSE.Views.ShapeSettings.textRotation": "Περιστροφή", "SSE.Views.ShapeSettings.textSelectImage": "Επιλογή Εικόνας", "SSE.Views.ShapeSettings.textSelectTexture": "Επιλογή", "SSE.Views.ShapeSettings.textStretch": "Έκταση", "SSE.Views.ShapeSettings.textStyle": "Τεχνοτροπία", "SSE.Views.ShapeSettings.textTexture": "Από Υφή", "SSE.Views.ShapeSettings.textTile": "Πλακίδιο", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "SSE.Views.ShapeSettings.txtBrownPaper": "Καφέ <PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "Καμβάς", "SSE.Views.ShapeSettings.txtCarton": "Χ<PERSON>ρτόνι", "SSE.Views.ShapeSettings.txtDarkFabric": "Σκούρο Ύφασμα", "SSE.Views.ShapeSettings.txtGrain": "Κόκκος", "SSE.Views.ShapeSettings.txtGranite": "Γραν<PERSON>της", "SSE.Views.ShapeSettings.txtGreyPaper": "Γκρι <PERSON>", "SSE.Views.ShapeSettings.txtKnit": "Πλέκω", "SSE.Views.ShapeSettings.txtLeather": "Δέρμα", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Ξύλο", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Στήλες", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Απόσταση Κειμένου", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Η εναλλακτική με βάση κείμενο αναπαράσταση των πληροφοριών οπτικού αντικειμένου, η οποία θα αναγνωσθε<PERSON> στα άτομα με προβλήματα όρασης ή γνωστικών προβλημάτων για να τους βοηθήσουν να κατανοήσουν καλύτερα ποιες πληροφορίες υπάρχουν στην εικόνα, σε αυτόματο σχήμα, στο διάγραμμα ή στον πίνακα.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Γωνία", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Βέλη", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Αυτόματο Ταίριασμα", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Μέγ<PERSON>θος <PERSON>ης", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Τεχνοτροπία Εκκίνησης", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Πλάγια Τομή", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Κάτω", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Αρχικό Γράμμα", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Αριθμ<PERSON><PERSON> στηλών", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Τέλους", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Τεχνοτροπία Τέλους", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Επίπεδο", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Περιεστρεμμένο", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Ύψος", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Οριζόντια", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Τύπος Ένωσης", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Σταθερές αναλογίες", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Αριστερά", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Τεχνοτροπία Γραμμής", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Μίτρα", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Να επιτρέπεται η επέκταση κειμένου εκτός σχήματος", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Προσαρμογή σχήματος για ταίριασμα με το κείμενο", "SSE.Views.ShapeSettingsAdvanced.textRight": "Δεξιά", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Περιστροφή", "SSE.Views.ShapeSettingsAdvanced.textRound": "Στρογγυλεμένο", "SSE.Views.ShapeSettingsAdvanced.textSize": "Μέγεθος", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Ευθυγράμμιση σε Κελί", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Απόσταση μεταξύ στηλών", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Τετράγωνο", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Πλ<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON>νου", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Σχήμα - Προηγμένες Ρυθμίσεις", "SSE.Views.ShapeSettingsAdvanced.textTop": "Επάνω", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Κατακόρυφα", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Πλάτη & Βέλη", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.SignatureSettings.strDelete": "Αφαίρεση Υπογραφής", "SSE.Views.SignatureSettings.strDetails": "Λεπτομέρειες Υπογραφής", "SSE.Views.SignatureSettings.strInvalid": "Μη έγκυρες υπογραφές", "SSE.Views.SignatureSettings.strRequested": "Αιτηθείσες υπογραφές", "SSE.Views.SignatureSettings.strSetup": "Ρύθμιση Υπογραφής", "SSE.Views.SignatureSettings.strSign": "Σύμβολο", "SSE.Views.SignatureSettings.strSignature": "Υπογραφή", "SSE.Views.SignatureSettings.strSigner": "Υπογράφων", "SSE.Views.SignatureSettings.strValid": "Έγκυρες υπογραφές", "SSE.Views.SignatureSettings.txtContinueEditing": "Επεξεργασ<PERSON>α ούτως ή άλλως", "SSE.Views.SignatureSettings.txtEditWarning": "Η επεξεργασία θα αφαιρέσει τις υπογραφές από το υπολογιστικό φύλλο.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Views.SignatureSettings.txtRemoveWarning": "Θέλετε να αφαιρέσετε αυτή την υπογραφή;<br>Δεν μπορεί να αναιρεθεί.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Αυτό το υπολογιστικό φύλλο πρέπει να υπογραφεί.", "SSE.Views.SignatureSettings.txtSigned": "Προστέθηκαν έγκυρες υπογραφές στο λογιστικό φύλλο. Το φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Κάποιες από τις ψηφιακές υπογραφές στο υπολογιστικό φύλλο δεν είναι έγκυρες ή δεν ήταν δυνατό να επιβεβαιωθούν. Το υπολογιστικό φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.SlicerAddDialog.textColumns": "Στήλες", "SSE.Views.SlicerAddDialog.txtTitle": "Εισαγωγ<PERSON>υτών", "SSE.Views.SlicerSettings.strHideNoData": "Απόκρυψη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettings.strIndNoData": "Οπτική κατάδειξη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettings.strShowDel": "Εμφάνιση διαγραμμένων στοιχείων από την πηγή δεδομένων", "SSE.Views.SlicerSettings.strShowNoData": "Εμφάνιση στοιχείων χωρίς καθόλου δεδομένα τελευταία", "SSE.Views.SlicerSettings.strSorting": "Ταξινόμηση και φιλτράρισμα", "SSE.Views.SlicerSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.SlicerSettings.textAsc": "Αύξουσα", "SSE.Views.SlicerSettings.textAZ": "A έως Ω", "SSE.Views.SlicerSettings.textButtons": "Κουμπιά", "SSE.Views.SlicerSettings.textColumns": "Στήλες", "SSE.Views.SlicerSettings.textDesc": "Φθίνουσα", "SSE.Views.SlicerSettings.textHeight": "Ύψος", "SSE.Views.SlicerSettings.textHor": "Οριζόντια", "SSE.Views.SlicerSettings.textKeepRatio": "Σταθερές Αναλογίες", "SSE.Views.SlicerSettings.textLargeSmall": "μεγαλύτερο προς μικρότερο", "SSE.Views.SlicerSettings.textLock": "Απενεργοποίηση αλλαγής μεγέθους και μετακίνησης", "SSE.Views.SlicerSettings.textNewOld": "από το νεότερο στο παλαιότερο", "SSE.Views.SlicerSettings.textOldNew": "από το παλαιότερο στο νεότερο", "SSE.Views.SlicerSettings.textPosition": "Θέση", "SSE.Views.SlicerSettings.textSize": "Μέγεθος", "SSE.Views.SlicerSettings.textSmallLarge": "από το μικρότερο στο μεγαλύτερο", "SSE.Views.SlicerSettings.textStyle": "Τεχνοτροπία", "SSE.Views.SlicerSettings.textVert": "Κατακόρυφος", "SSE.Views.SlicerSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Ω έως Α", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Κουμπιά", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Στήλες", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Ύψος", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Απόκρυψη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Οπτική κατάδειξη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Παραπομπ<PERSON>ς", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Εμφάνιση διαγραμμένων στοιχείων από την πηγή δεδομένων", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Εμφάνισης κεφαλίδας", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Εμφάνιση στοιχείων χωρίς καθόλου δεδομένα τελευταία", "SSE.Views.SlicerSettingsAdvanced.strSize": "Μέγεθος", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Ταξινόμηση & Φιλτράρισμα", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Τεχνοτροπία", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Τεχνοτροπία & Μέγεθος", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Αύξουσα", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A έως Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Φθίνουσα", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Όνομα που θα χρησιμοποιείται στους τύπους", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Κεφαλίδα", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Σταθερές Αναλογίες", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "μεγαλύτερο προς μικρότερο", "SSE.Views.SlicerSettingsAdvanced.textName": "Όνομα", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "από το νεότερο στο παλαιότερο", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "από το παλαιότερο στο νεότερο", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "από το μικρότερο στο μεγαλύτερο", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Ευθυγράμμιση σε Κελί", "SSE.Views.SlicerSettingsAdvanced.textSort": "Ταξινόμηση", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Όνομα πηγής", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Αναλυτής- Προηγμένες Ρυθμίσεις", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.SlicerSettingsAdvanced.textZA": "Ω έως Α", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.SortDialog.errorEmpty": "Όλα τα κριτήρια ταξινόμησης πρέπει να ορίζουν μια στήλη ή γραμμή", "SSE.Views.SortDialog.errorMoreOneCol": "Περισσότερες από μία στήλες είναι επιλεγμένες.", "SSE.Views.SortDialog.errorMoreOneRow": "Περισσότερες από μία γραμμή είναι επιλεγμένες", "SSE.Views.SortDialog.errorNotOriginalCol": "Η στήλη που επιλέξατε δεν περιλαμβάνεται στο αρχικά επιλεγμένο εύρος.", "SSE.Views.SortDialog.errorNotOriginalRow": "Η γραμμή που επιλέξατε δεν είναι στο αρχικό επιλεγμένο εύρος.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 ταξινομείται με το ίδιο χρώμα περισσότερες από μία φορές.<br>Διαγράψτε τα διπλότυπα κριτήρια ταξινόμησης και προσπαθήστε ξανά.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 ταξινομείται ως προς τις τιμές περισσότερες από μία φορές.<br>Διαγράψτε τα διπλότυπα κριτήρια ταξινόμησης και προσπαθήστε ξανά.", "SSE.Views.SortDialog.textAdd": "Προσθήκη επιπέδου", "SSE.Views.SortDialog.textAsc": "Αύξουσα", "SSE.Views.SortDialog.textAuto": "Αυτόματα", "SSE.Views.SortDialog.textAZ": "A έως Ω", "SSE.Views.SortDialog.textBelow": "Από κάτω", "SSE.Views.SortDialog.textCellColor": "Χρώμα κελιού", "SSE.Views.SortDialog.textColumn": "Στήλη", "SSE.Views.SortDialog.textCopy": "Αντιγραφ<PERSON> επιπέδου", "SSE.Views.SortDialog.textDelete": "Διαγρα<PERSON><PERSON> επιπέδου", "SSE.Views.SortDialog.textDesc": "Φθίνουσα", "SSE.Views.SortDialog.textDown": "Μετακίνηση επιπέδου κάτω", "SSE.Views.SortDialog.textFontColor": "Χρώμα γραμματοσειράς", "SSE.Views.SortDialog.textLeft": "Αριστερά", "SSE.Views.SortDialog.textMoreCols": "(Περισσότερες στήλες...)", "SSE.Views.SortDialog.textMoreRows": "(Περισσότερες γραμμές...)", "SSE.Views.SortDialog.textNone": "Κανένα", "SSE.Views.SortDialog.textOptions": "Επιλογές", "SSE.Views.SortDialog.textOrder": "Σειρά", "SSE.Views.SortDialog.textRight": "Δεξιά", "SSE.Views.SortDialog.textRow": "Γραμμή", "SSE.Views.SortDialog.textSort": "Ταξινόμηση σε", "SSE.Views.SortDialog.textSortBy": "Ταξινόμηση κατά", "SSE.Views.SortDialog.textThenBy": "Τότε από", "SSE.Views.SortDialog.textTop": "Επάνω", "SSE.Views.SortDialog.textUp": "Μετακίνηση επιπέδου πάνω", "SSE.Views.SortDialog.textValues": "Τιμές", "SSE.Views.SortDialog.textZA": "Ω έως Α", "SSE.Views.SortDialog.txtInvalidRange": "Μη έγκυρο εύρος κελιών.", "SSE.Views.SortDialog.txtTitle": "Ταξινόμηση", "SSE.Views.SortFilterDialog.textAsc": "Αύξουσα (A έως Ω) κατά", "SSE.Views.SortFilterDialog.textDesc": "Φθίνουσα (Ω έως Α) κατά", "SSE.Views.SortFilterDialog.txtTitle": "Ταξινόμηση", "SSE.Views.SortOptionsDialog.textCase": "Με διάκριση πεζών - κε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν γραμμάτων", "SSE.Views.SortOptionsDialog.textHeaders": "Τα δεδομένα μου έχουν κεφαλίδες", "SSE.Views.SortOptionsDialog.textLeftRight": "Ταξινόμηση από αριστερά προς τα δεξιά", "SSE.Views.SortOptionsDialog.textOrientation": "Προσανατολισμός", "SSE.Views.SortOptionsDialog.textTitle": "Επιλο<PERSON><PERSON><PERSON>ινόμησης", "SSE.Views.SortOptionsDialog.textTopBottom": "Ταξινόμηση από πάνω προς τα κάτω", "SSE.Views.SpecialPasteDialog.textAdd": "Προσθήκη", "SSE.Views.SpecialPasteDialog.textAll": "Όλα", "SSE.Views.SpecialPasteDialog.textBlanks": "Προσπέρα<PERSON>η κενών", "SSE.Views.SpecialPasteDialog.textColWidth": "Πλάτη στηλών", "SSE.Views.SpecialPasteDialog.textComments": "Σχόλια", "SSE.Views.SpecialPasteDialog.textDiv": "Διαίρεση", "SSE.Views.SpecialPasteDialog.textFFormat": "Τύποι & μορφοποίηση", "SSE.Views.SpecialPasteDialog.textFNFormat": "Τύποι & μορφές αριθμών", "SSE.Views.SpecialPasteDialog.textFormats": "Μορφοποιήσεις", "SSE.Views.SpecialPasteDialog.textFormulas": "Τύποι", "SSE.Views.SpecialPasteDialog.textFWidth": "Πλάτη μαθηματικών τύπων & στηλών", "SSE.Views.SpecialPasteDialog.textMult": "Επί", "SSE.Views.SpecialPasteDialog.textNone": "Κανένα", "SSE.Views.SpecialPasteDialog.textOperation": "Λειτουργία", "SSE.Views.SpecialPasteDialog.textPaste": "Επικόλληση", "SSE.Views.SpecialPasteDialog.textSub": "Αφαίρεση", "SSE.Views.SpecialPasteDialog.textTitle": "Ειδική Επικόλληση", "SSE.Views.SpecialPasteDialog.textTranspose": "Μετατόπιση", "SSE.Views.SpecialPasteDialog.textValues": "Τιμές", "SSE.Views.SpecialPasteDialog.textVFormat": "Τιμές & μορφοποίηση", "SSE.Views.SpecialPasteDialog.textVNFormat": "Τιμές & μορφές αριθμών", "SSE.Views.SpecialPasteDialog.textWBorders": "Όλα εκτός από τα περιγράμματα", "SSE.Views.Spellcheck.noSuggestions": "Δεν υπάρχουν προτάσεις ορθογραφίας", "SSE.Views.Spellcheck.textChange": "Αλλαγή", "SSE.Views.Spellcheck.textChangeAll": "Αλλαγή Όλων", "SSE.Views.Spellcheck.textIgnore": "Αγνόηση", "SSE.Views.Spellcheck.textIgnoreAll": "Αγνόηση Όλων", "SSE.Views.Spellcheck.txtAddToDictionary": "Προσθήκη στο Λεξικό", "SSE.Views.Spellcheck.txtClosePanel": "Κλείσιμο συλλαβισμού", "SSE.Views.Spellcheck.txtComplete": "Ο έλεγχος ορθογρα<PERSON><PERSON>ας ολοκληρώθηκε", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Γλώσσα Λεξικού", "SSE.Views.Spellcheck.txtNextTip": "Μετάβαση στην επόμενη λέξη", "SSE.Views.Spellcheck.txtSpelling": "Ορθογραφία", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Αντιγρα<PERSON><PERSON> στο τέλος)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Με<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>η στο τέλος)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Επικόλληση πριν το φύλλο", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Μετακίνηση πριν το φύλλο", "SSE.Views.Statusbar.filteredRecordsText": "{0} από {1} εγγραφ<PERSON>ς φιλτραρίστηκαν", "SSE.Views.Statusbar.filteredText": "Κατάσταση φιλτραρίσματος", "SSE.Views.Statusbar.itemAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.Statusbar.itemCopy": "Αντιγραφή", "SSE.Views.Statusbar.itemCount": "Μέτρηση", "SSE.Views.Statusbar.itemDelete": "Διαγραφή", "SSE.Views.Statusbar.itemHidden": "Κρυφό", "SSE.Views.Statusbar.itemHide": "Απόκρυψη", "SSE.Views.Statusbar.itemInsert": "Εισαγωγή", "SSE.Views.Statusbar.itemMaximum": "Μέγιστο", "SSE.Views.Statusbar.itemMinimum": "Ελάχιστο", "SSE.Views.Statusbar.itemMove": "Μετακίνηση", "SSE.Views.Statusbar.itemProtect": "Προστασία", "SSE.Views.Statusbar.itemRename": "Μετονομασία", "SSE.Views.Statusbar.itemStatus": "Κατάσταση αποθήκευσης", "SSE.Views.Statusbar.itemSum": "Άθροισμα", "SSE.Views.Statusbar.itemTabColor": "Χρώ<PERSON><PERSON> Στηλοθέτη", "SSE.Views.Statusbar.itemUnProtect": "Άρση Προστασίας", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Υπάρχει ήδη ένα φύλλο εργασίας με αυτό το όνομα", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Το όνομα ενός φύλλου εργασίας δεν μπορεί να περιέχει τους ακόλουθους χαρακτήρες: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Όνομα Φύλλου", "SSE.Views.Statusbar.selectAllSheets": "Επιλογή Όλων των Φύλλων", "SSE.Views.Statusbar.sheetIndexText": "Φύλλο {0} από {1}", "SSE.Views.Statusbar.textAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.Statusbar.textCount": "Μέτρηση", "SSE.Views.Statusbar.textMax": "Μέγιστο", "SSE.Views.Statusbar.textMin": "Ελάχιστο", "SSE.Views.Statusbar.textNewColor": "Νέου Προσαρμοσμένου Χρώματος", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textSum": "Άθροισμα", "SSE.Views.Statusbar.tipAddTab": "Προσθήκη φύλλου εργασίας", "SSE.Views.Statusbar.tipFirst": "Κύλιση στο πρώτο φύλλο", "SSE.Views.Statusbar.tipLast": "Κύλιση στο τελευτα<PERSON><PERSON> φύλλο", "SSE.Views.Statusbar.tipListOfSheets": "Λίστα Φύλλων", "SSE.Views.Statusbar.tipNext": "Κύλιση λίστας φύλλων δεξιά", "SSE.Views.Statusbar.tipPrev": "Κύλιση λίστας φύλλων αριστερά", "SSE.Views.Statusbar.tipZoomFactor": "Εστίαση", "SSE.Views.Statusbar.tipZoomIn": "Μεγέθυνση", "SSE.Views.Statusbar.tipZoomOut": "Σμίκρυνση", "SSE.Views.Statusbar.ungroupSheets": "Κατάργη<PERSON>η Ομαδοποίησης Φύλλων", "SSE.Views.Statusbar.zoomText": "Εστίαση {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Δεν ήταν δυνατή η εκτέλεση της επιλεγμένης περιοχής κελιών.<br>Επιλέξτε ένα ομοιόμορφο εύρος δεδομένων διαφορετικό από το υπάρχον και δοκιμάστε ξανά.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br>Ε<PERSON><PERSON>λ<PERSON>ξτε ένα εύρος ώστε η πρώτη γραμμή του πίνακα να είναι στην ίδια γραμμή<br>και ο παραγόμενος πίνακας να επικαλύπτει τον τρέχοντα.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ένα εύρος που δεν περιλαμβάνει άλλους πίνακες.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Οι τύποι συστοιχιών πολλών κελιών δεν επιτρέπονται στους πίνακες.", "SSE.Views.TableOptionsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.TableOptionsDialog.txtFormat": "Δημιουργ<PERSON>α Πίνακα", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.TableOptionsDialog.txtNote": "Οι κεφαλίδες πρέπει να παραμείνουν στην ίδια γραμμή και το παραγόμενο εύρος πίνακα να επικαλύπτει το αρχικό εύρος πίνακα.", "SSE.Views.TableOptionsDialog.txtTitle": "Τίτλος", "SSE.Views.TableSettings.deleteColumnText": "Διαγρα<PERSON><PERSON> Στήλης", "SSE.Views.TableSettings.deleteRowText": "Διαγραφή Γραμμής", "SSE.Views.TableSettings.deleteTableText": "Διαγρα<PERSON>ή Πίνακα", "SSE.Views.TableSettings.insertColumnLeftText": "Εισαγω<PERSON><PERSON>ήλης Αριστερά", "SSE.Views.TableSettings.insertColumnRightText": "Εισαγω<PERSON><PERSON> Στήλης Δεξιά", "SSE.Views.TableSettings.insertRowAboveText": "Εισαγωγή Γραμμής Από Πάνω", "SSE.Views.TableSettings.insertRowBelowText": "Εισαγωγή Γραμμής Απ<PERSON> Κάτω", "SSE.Views.TableSettings.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.TableSettings.selectColumnText": "Επιλογή Ολόκληρης Στήλης", "SSE.Views.TableSettings.selectDataText": "Επιλογή Δεδομένων Στήλης", "SSE.Views.TableSettings.selectRowText": "Επιλογή Γραμμής", "SSE.Views.TableSettings.selectTableText": "Επιλογή Πίνακα", "SSE.Views.TableSettings.textActions": "Ενέργειες Πίνακα", "SSE.Views.TableSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.TableSettings.textBanded": "Με Εναλλαγή Σκίασης", "SSE.Views.TableSettings.textColumns": "Στήλες", "SSE.Views.TableSettings.textConvertRange": "Μετατροπή σε εύρος", "SSE.Views.TableSettings.textEdit": "Γραμμές & Στήλες", "SSE.Views.TableSettings.textEmptyTemplate": "Κανένα πρότυπο", "SSE.Views.TableSettings.textExistName": "ΣΦΑΛΜΑ! Υπάρχει ήδη εύρος με αυτό το όνομα", "SSE.Views.TableSettings.textFilter": "Κουμπί φίλτρου", "SSE.Views.TableSettings.textFirst": "Πρώτη", "SSE.Views.TableSettings.textHeader": "Κεφαλίδα", "SSE.Views.TableSettings.textInvalidName": "ΣΦΑΛΜΑ! Μη έγκυρο όνομα πίνακα", "SSE.Views.TableSettings.textIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.TableSettings.textLast": "Τελευταία", "SSE.Views.TableSettings.textLongOperation": "Η λειτουργία είναι χρονοβόρα", "SSE.Views.TableSettings.textPivot": "Εισαγωγ<PERSON> συγκεντρωτικού πίνακα", "SSE.Views.TableSettings.textRemDuplicates": "Αφαίρεση διπλότυπων", "SSE.Views.TableSettings.textReservedName": "Το όνομα που προσπαθείτε να χρησιμοποιήσετε αναφέρεται ήδη σε τύπους κελιών. Παρακαλούμε χρησιμοποιήστε κάποιο άλλο όνομα.", "SSE.Views.TableSettings.textResize": "Αλλαγή μεγέθους πίνακα", "SSE.Views.TableSettings.textRows": "Γραμμές", "SSE.Views.TableSettings.textSelectData": "Επιλογ<PERSON> Δεδομένων", "SSE.Views.TableSettings.textSlicer": "Εισαγωγ<PERSON> αναλυτή", "SSE.Views.TableSettings.textTableName": "Όνομα Πίνακα", "SSE.Views.TableSettings.textTemplate": "Επιλογή Από Πρότυπο", "SSE.Views.TableSettings.textTotal": "Σύνολο", "SSE.Views.TableSettings.warnLongOperation": "Η λειτουργία που πρόκειται να εκτελέσετε ίσως χρειαστεί πολύ χρόνο για να ολοκληρωθεί.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Views.TableSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.TableSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.TableSettingsAdvanced.textTitle": "Πίνακας - Προηγμένες Ρυθμίσεις", "SSE.Views.TextArtSettings.strBackground": "Χρώμα παρασκηνίου", "SSE.Views.TextArtSettings.strColor": "Χρώμα", "SSE.Views.TextArtSettings.strFill": "Γέμισμα", "SSE.Views.TextArtSettings.strForeground": "Χρώμα προσκηνίου", "SSE.Views.TextArtSettings.strPattern": "Μοτίβο", "SSE.Views.TextArtSettings.strSize": "Μέγεθος", "SSE.Views.TextArtSettings.strStroke": "Πινελιά", "SSE.Views.TextArtSettings.strTransparency": "Αδιαφάνεια", "SSE.Views.TextArtSettings.strType": "Τύπος", "SSE.Views.TextArtSettings.textAngle": "Γωνία", "SSE.Views.TextArtSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 pt και 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Γέμισμα με Χρώμα", "SSE.Views.TextArtSettings.textDirection": "Κατεύθυνση", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromFile": "Από Αρχείο", "SSE.Views.TextArtSettings.textFromUrl": "Από διεύθυνση URL", "SSE.Views.TextArtSettings.textGradient": "Σημεία διαβάθμισης", "SSE.Views.TextArtSettings.textGradientFill": "Βαθμωτό Γέμισμα", "SSE.Views.TextArtSettings.textImageTexture": "Εικόνα ή Υφή", "SSE.Views.TextArtSettings.textLinear": "Γραμμικός", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON>α", "SSE.Views.TextArtSettings.textPatternFill": "Μοτίβο", "SSE.Views.TextArtSettings.textPosition": "Θέση", "SSE.Views.TextArtSettings.textRadial": "Ακτινικ<PERSON>ς", "SSE.Views.TextArtSettings.textSelectTexture": "Επιλογή", "SSE.Views.TextArtSettings.textStretch": "Έκταση", "SSE.Views.TextArtSettings.textStyle": "Τεχνοτροπία", "SSE.Views.TextArtSettings.textTemplate": "Πρότυπο", "SSE.Views.TextArtSettings.textTexture": "Από Υφή", "SSE.Views.TextArtSettings.textTile": "Πλακίδιο", "SSE.Views.TextArtSettings.textTransform": "Μετασχηματισμός", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "SSE.Views.TextArtSettings.txtBrownPaper": "Καφέ <PERSON>", "SSE.Views.TextArtSettings.txtCanvas": "Καμβάς", "SSE.Views.TextArtSettings.txtCarton": "Χ<PERSON>ρτόνι", "SSE.Views.TextArtSettings.txtDarkFabric": "Σκούρο Ύφασμα", "SSE.Views.TextArtSettings.txtGrain": "Κόκκος", "SSE.Views.TextArtSettings.txtGranite": "Γραν<PERSON>της", "SSE.Views.TextArtSettings.txtGreyPaper": "Γκρι <PERSON>", "SSE.Views.TextArtSettings.txtKnit": "Πλέκω", "SSE.Views.TextArtSettings.txtLeather": "Δέρμα", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Ξύλο", "SSE.Views.Toolbar.capBtnAddComment": "Προσθήκη Σχολίου", "SSE.Views.Toolbar.capBtnColorSchemas": "Σύστημα Χρωμάτων", "SSE.Views.Toolbar.capBtnComment": "Σχόλιο", "SSE.Views.Toolbar.capBtnInsHeader": "Κεφαλίδα & Υποσέλιδο", "SSE.Views.Toolbar.capBtnInsSlicer": "Αναλυτής", "SSE.Views.Toolbar.capBtnInsSymbol": "Σύμβολο", "SSE.Views.Toolbar.capBtnMargins": "Περιθώρια", "SSE.Views.Toolbar.capBtnPageOrient": "Προσανατολισμός", "SSE.Views.Toolbar.capBtnPageSize": "Μέγεθος", "SSE.Views.Toolbar.capBtnPrintArea": "Περιοχή Εκτύπωσης", "SSE.Views.Toolbar.capBtnPrintTitles": "Εκτύπωση Τίτλων", "SSE.Views.Toolbar.capBtnScale": "Κλιμάκωση για Ταίριασμα", "SSE.Views.Toolbar.capImgAlign": "Στοίχιση", "SSE.Views.Toolbar.capImgBackward": "Μεταφορά Προς τα Πίσω", "SSE.Views.Toolbar.capImgForward": "Μεταφορά Προς τα Εμπρός", "SSE.Views.Toolbar.capImgGroup": "Ομάδα", "SSE.Views.Toolbar.capInsertChart": "Γράφημα", "SSE.Views.Toolbar.capInsertEquation": "Εξίσωση", "SSE.Views.Toolbar.capInsertHyperlink": "Υπερσύνδεσμος", "SSE.Views.Toolbar.capInsertImage": "Εικόνα", "SSE.Views.Toolbar.capInsertShape": "Σχήμα", "SSE.Views.Toolbar.capInsertSpark": "Μικρογράφημα", "SSE.Views.Toolbar.capInsertTable": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.Toolbar.capInsertText": "Πλ<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON>νου", "SSE.Views.Toolbar.mniImageFromFile": "Εικόνα από Αρχείο", "SSE.Views.Toolbar.mniImageFromStorage": "Εικόνα από Αποθηκευτικ<PERSON>ο", "SSE.Views.Toolbar.mniImageFromUrl": "Εικόνα από διεύθυνση URL", "SSE.Views.Toolbar.textAddPrintArea": "Προσθήκη στην Περιοχή Εκτύπωσης", "SSE.Views.Toolbar.textAlignBottom": "Στοίχιση Κάτω", "SSE.Views.Toolbar.textAlignCenter": "Στοίχιση στο Κέντρο", "SSE.Views.Toolbar.textAlignJust": "Πλήρης στοίχιση", "SSE.Views.Toolbar.textAlignLeft": "Στοίχιση Αριστερά", "SSE.Views.Toolbar.textAlignMiddle": "Στοίχιση στη Μέση", "SSE.Views.Toolbar.textAlignRight": "Στοίχιση Δεξιά", "SSE.Views.Toolbar.textAlignTop": "Στοίχιση Πάνω", "SSE.Views.Toolbar.textAllBorders": "Όλα Τα Περιγράμματα", "SSE.Views.Toolbar.textAuto": "Αυτόματα", "SSE.Views.Toolbar.textAutoColor": "Αυτόματα", "SSE.Views.Toolbar.textBold": "Έντονα", "SSE.Views.Toolbar.textBordersColor": "Χρώμα Περιγράμματος", "SSE.Views.Toolbar.textBordersStyle": "Τεχνοτροπία Περιγράμματος", "SSE.Views.Toolbar.textBottom": "Κάτω Μέρος:", "SSE.Views.Toolbar.textBottomBorders": "Κάτω Περιγράμματα", "SSE.Views.Toolbar.textCenterBorders": "Εσωτερ<PERSON><PERSON><PERSON>ατακόρυφα Περιγράμματα", "SSE.Views.Toolbar.textClearPrintArea": "Εκκαθάριση Περιοχής Εκτύπωσης", "SSE.Views.Toolbar.textClearRule": "Εκκαθάριση Κανόνων", "SSE.Views.Toolbar.textClockwise": "Γωνία Δεξιόστροφα", "SSE.Views.Toolbar.textColorScales": "Κλίμα<PERSON>ες <PERSON>ρωμάτων", "SSE.Views.Toolbar.textCounterCw": "Γωνία Αριστερόστροφα", "SSE.Views.Toolbar.textDataBars": "Μπ<PERSON><PERSON><PERSON><PERSON> Δεδομένων", "SSE.Views.Toolbar.textDelLeft": "Ολίσθηση Κελιών Αριστερά", "SSE.Views.Toolbar.textDelUp": "Ολίσθηση Κελιών Πάνω", "SSE.Views.Toolbar.textDiagDownBorder": "Δ<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON>τω Περίγραμμα", "SSE.Views.Toolbar.textDiagUpBorder": "Διαγ<PERSON><PERSON><PERSON><PERSON>ω Περίγραμμα", "SSE.Views.Toolbar.textDone": "Ολοκληρώθηκε", "SSE.Views.Toolbar.textEditVA": "Επεξεργασία Ορατής Περιοχής", "SSE.Views.Toolbar.textEntireCol": "Ολόκληρη Στήλη", "SSE.Views.Toolbar.textEntireRow": "Ολόκληρη Γραμμή", "SSE.Views.Toolbar.textFewPages": "σελίδες", "SSE.Views.Toolbar.textHeight": "Ύψος", "SSE.Views.Toolbar.textHideVA": "Απόκρυψη Ορατής Περιοχής", "SSE.Views.Toolbar.textHorizontal": "Οριζόν<PERSON>ι<PERSON>είμενο", "SSE.Views.Toolbar.textInsDown": "Ολίσθηση Κελιών <PERSON>ω", "SSE.Views.Toolbar.textInsideBorders": "Εσωτερικά Περιγράμματα", "SSE.Views.Toolbar.textInsRight": "Ολίσθηση Κελιών Δεξιά", "SSE.Views.Toolbar.textItalic": "Πλάγια", "SSE.Views.Toolbar.textItems": "Αντικείμενα", "SSE.Views.Toolbar.textLandscape": "Οριζόντιος", "SSE.Views.Toolbar.textLeft": "Αριστερά:", "SSE.Views.Toolbar.textLeftBorders": "Αριστερ<PERSON> Περιγράμματα", "SSE.Views.Toolbar.textManageRule": "Διαχείρι<PERSON>η Κανόνων", "SSE.Views.Toolbar.textManyPages": "σελίδες", "SSE.Views.Toolbar.textMarginsLast": "Τελευτ<PERSON><PERSON><PERSON> Προσαρμοσμένο", "SSE.Views.Toolbar.textMarginsNarrow": "Στενό", "SSE.Views.Toolbar.textMarginsNormal": "Κανονική", "SSE.Views.Toolbar.textMarginsWide": "<PERSON>λα<PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Εσωτερικά Οριζόντια Περιγράμματα", "SSE.Views.Toolbar.textMoreFormats": "Περισσότερες μορφές", "SSE.Views.Toolbar.textMorePages": "Περισσότερες σελίδες", "SSE.Views.Toolbar.textNewColor": "Νέου Προσαρμοσμένου Χρώματος", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>ριγράμματα", "SSE.Views.Toolbar.textOnePage": "σελίδα", "SSE.Views.Toolbar.textOutBorders": "Εξωτερικά Περιγράμματα", "SSE.Views.Toolbar.textPageMarginsCustom": "Προσαρμοσμένα περιθώρια", "SSE.Views.Toolbar.textPortrait": "Κατακόρυφος", "SSE.Views.Toolbar.textPrint": "Εκτύπωση", "SSE.Views.Toolbar.textPrintGridlines": "Εκτύπωση γραμμών πλέγματος", "SSE.Views.Toolbar.textPrintHeadings": "Εκτύπωση επικεφαλίδων", "SSE.Views.Toolbar.textPrintOptions": "Ρυθμίσεις Εκτύπωσης", "SSE.Views.Toolbar.textRight": "Δεξιά:", "SSE.Views.Toolbar.textRightBorders": "Δεξιά Περιγράμματα", "SSE.Views.Toolbar.textRotateDown": "Περιστροφή Κειμένου Κάτω", "SSE.Views.Toolbar.textRotateUp": "Περιστροφή Κειμένου Πάνω", "SSE.Views.Toolbar.textScale": "Κλίμακα", "SSE.Views.Toolbar.textScaleCustom": "Προσαρμογή", "SSE.Views.Toolbar.textSelection": "Από την τρέχουσα επιλογή", "SSE.Views.Toolbar.textSetPrintArea": "Ορισμ<PERSON>ς <PERSON>τυπώσιμης <PERSON>εριοχής", "SSE.Views.Toolbar.textStrikeout": "Διαγραφή", "SSE.Views.Toolbar.textSubscript": "Δείκτης", "SSE.Views.Toolbar.textSubSuperscript": "Δείκτης/Εκθέτης", "SSE.Views.Toolbar.textSuperscript": "Εκθέτης", "SSE.Views.Toolbar.textTabCollaboration": "Συνεργασία", "SSE.Views.Toolbar.textTabData": "Δεδομένα", "SSE.Views.Toolbar.textTabFile": "Αρχείο", "SSE.Views.Toolbar.textTabFormula": "Τύπος", "SSE.Views.Toolbar.textTabHome": "Αρχική", "SSE.Views.Toolbar.textTabInsert": "Εισαγωγή", "SSE.Views.Toolbar.textTabLayout": "Διάταξη", "SSE.Views.Toolbar.textTabProtect": "Προστασία", "SSE.Views.Toolbar.textTabView": "Προβολή", "SSE.Views.Toolbar.textThisPivot": "Από αυτόν τον συγκεντρωτικό πίνακα", "SSE.Views.Toolbar.textThisSheet": "Από αυτό το φύλλο εργασίας", "SSE.Views.Toolbar.textThisTable": "Από αυτόν τον πίνακα", "SSE.Views.Toolbar.textTop": "Πάνω:", "SSE.Views.Toolbar.textTopBorders": "Πάνω Περιγράμματα", "SSE.Views.Toolbar.textUnderline": "Υπογράμμιση", "SSE.Views.Toolbar.textVertical": "Κατακόρυ<PERSON><PERSON> Κείμενο", "SSE.Views.Toolbar.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Εστίαση", "SSE.Views.Toolbar.tipAlignBottom": "Στοίχιση κάτω", "SSE.Views.Toolbar.tipAlignCenter": "Στοίχιση στο κέντρο", "SSE.Views.Toolbar.tipAlignJust": "Πλήρης στοίχιση", "SSE.Views.Toolbar.tipAlignLeft": "Στοίχιση αριστερά", "SSE.Views.Toolbar.tipAlignMiddle": "Στοίχιση στη μέση", "SSE.Views.Toolbar.tipAlignRight": "Στοίχιση δεξιά", "SSE.Views.Toolbar.tipAlignTop": "Στοίχιση πάνω", "SSE.Views.Toolbar.tipAutofilter": "Ταξινόμηση και Φίλτρο", "SSE.Views.Toolbar.tipBack": "Πίσω", "SSE.Views.Toolbar.tipBorders": "Περιγράμματα", "SSE.Views.Toolbar.tipCellStyle": "Τεχνοτροπία Κελιού", "SSE.Views.Toolbar.tipChangeChart": "Αλλαγή τύπου γραφήματος", "SSE.Views.Toolbar.tipClearStyle": "Εκκαθάριση", "SSE.Views.Toolbar.tipColorSchemas": "Αλλαγή χρωματικού σχεδίου", "SSE.Views.Toolbar.tipCondFormat": "Μορφοποίηση υπό όρους", "SSE.Views.Toolbar.tipCopy": "Αντιγραφή", "SSE.Views.Toolbar.tipCopyStyle": "Αντιγραφή τεχνοτροπίας", "SSE.Views.Toolbar.tipDecDecimal": "Μείωση δεκαδικού", "SSE.Views.Toolbar.tipDecFont": "Μείωση μεγέθους γραμματοσειράς", "SSE.Views.Toolbar.tipDeleteOpt": "Διαγρα<PERSON><PERSON> κελιών", "SSE.Views.Toolbar.tipDigStyleAccounting": "Τεχνοτροπία λογιστικής", "SSE.Views.Toolbar.tipDigStyleCurrency": "Νομισματική Τεχνοτροπία", "SSE.Views.Toolbar.tipDigStylePercent": "Τεχνοτροπία ποσοστού", "SSE.Views.Toolbar.tipEditChart": "Επεξεργασία Γραφήματος", "SSE.Views.Toolbar.tipEditChartData": "Επιλογ<PERSON> Δεδομένων", "SSE.Views.Toolbar.tipEditChartType": "Αλλαγή Τύπου Γραφήματος", "SSE.Views.Toolbar.tipEditHeader": "Επεξεργασ<PERSON><PERSON> κεφαλίδας ή υποσέλιδου", "SSE.Views.Toolbar.tipFontColor": "Χρώμα γραμματοσειράς", "SSE.Views.Toolbar.tipFontName": "Γραμματοσειρά", "SSE.Views.Toolbar.tipFontSize": "Μέγ<PERSON><PERSON>ος γραμματοσειράς", "SSE.Views.Toolbar.tipImgAlign": "Στοίχιση αντικειμένων", "SSE.Views.Toolbar.tipImgGroup": "Ομαδοποίηση αντικειμένων", "SSE.Views.Toolbar.tipIncDecimal": "Αύξηση δεκαδικού", "SSE.Views.Toolbar.tipIncFont": "Αύξηση μεγέθους γραμματοσειράς", "SSE.Views.Toolbar.tipInsertChart": "Εισαγωγή γραφήματος", "SSE.Views.Toolbar.tipInsertChartSpark": "Εισαγωγή γραφήματος", "SSE.Views.Toolbar.tipInsertEquation": "Εισαγωγή εξίσωσης", "SSE.Views.Toolbar.tipInsertHyperlink": "Προσθήκη υπερσυνδέσμου", "SSE.Views.Toolbar.tipInsertImage": "Εισαγωγή εικόνας", "SSE.Views.Toolbar.tipInsertOpt": "Εισαγωγ<PERSON> κελιών", "SSE.Views.Toolbar.tipInsertShape": "Εισαγωγ<PERSON> αυτόματου σχήματος", "SSE.Views.Toolbar.tipInsertSlicer": "Εισαγωγ<PERSON> αναλυτή", "SSE.Views.Toolbar.tipInsertSpark": "Εισαγωγή μικρογραφήματος", "SSE.Views.Toolbar.tipInsertSymbol": "Εισαγωγή συμβόλου", "SSE.Views.Toolbar.tipInsertTable": "Εισαγωγή πίνακα", "SSE.Views.Toolbar.tipInsertText": "Εισαγωγή πλαισίου κειμένου", "SSE.Views.Toolbar.tipInsertTextart": "Εισαγωγή Τεχνοκειμένου", "SSE.Views.Toolbar.tipMerge": "Συγχώνευση και κεντράρισμα", "SSE.Views.Toolbar.tipNone": "Κανένα", "SSE.Views.Toolbar.tipNumFormat": "Μορφή αριθμού", "SSE.Views.Toolbar.tipPageMargins": "Περιθώρια σελίδας", "SSE.Views.Toolbar.tipPageOrient": "Προσανα<PERSON><PERSON><PERSON>ισμ<PERSON>ς σελίδας", "SSE.Views.Toolbar.tipPageSize": "Μέγ<PERSON><PERSON>ος σελίδας", "SSE.Views.Toolbar.tipPaste": "Επικόλληση", "SSE.Views.Toolbar.tipPrColor": "Χρώμα γεμίσματος", "SSE.Views.Toolbar.tipPrint": "Εκτύπωση", "SSE.Views.Toolbar.tipPrintArea": "Περιοχή εκτύπωσης", "SSE.Views.Toolbar.tipPrintTitles": "Εκτύπωση τίτλων", "SSE.Views.Toolbar.tipRedo": "Επανάληψη", "SSE.Views.Toolbar.tipSave": "Αποθήκευση", "SSE.Views.Toolbar.tipSaveCoauth": "Αποθηκεύστε τις αλλαγές σας για να τις δουν οι άλλοι χρήστες.", "SSE.Views.Toolbar.tipScale": "Κλιμάκωση για Ταίριασμα", "SSE.Views.Toolbar.tipSendBackward": "Μεταφορά προς τα πίσω", "SSE.Views.Toolbar.tipSendForward": "Μεταφορά προς τα εμπρός", "SSE.Views.Toolbar.tipSynchronize": "Το έγγραφο τροποποιήθηκε από άλλο χρήστη. Κάντε κλικ για να αποθηκεύσετε τις αλλαγές σας και να επαναφορτώσετε τις ενημερώσεις.", "SSE.Views.Toolbar.tipTextFormatting": "Περισσότερα εργαλεία μορφοποίησης κειμένου ", "SSE.Views.Toolbar.tipTextOrientation": "Προσανατολισμός", "SSE.Views.Toolbar.tipUndo": "Αναίρεση", "SSE.Views.Toolbar.tipVisibleArea": "Ορατή περιοχή", "SSE.Views.Toolbar.tipWrap": "Αναδίπλωση κειμένου", "SSE.Views.Toolbar.txtAccounting": "Λογιστική", "SSE.Views.Toolbar.txtAdditional": "Επιπρόσθετα", "SSE.Views.Toolbar.txtAscending": "Αύξουσα", "SSE.Views.Toolbar.txtAutosumTip": "Άθροιση", "SSE.Views.Toolbar.txtClearAll": "Όλα", "SSE.Views.Toolbar.txtClearComments": "Σχόλια", "SSE.Views.Toolbar.txtClearFilter": "Εκκαθάριση φίλτρου", "SSE.Views.Toolbar.txtClearFormat": "Μορφή", "SSE.Views.Toolbar.txtClearFormula": "Συνάρτηση", "SSE.Views.Toolbar.txtClearHyper": "Υπερσύνδεσμοι", "SSE.Views.Toolbar.txtClearText": "Κείμενο", "SSE.Views.Toolbar.txtCurrency": "Νόμισμα", "SSE.Views.Toolbar.txtCustom": "Προσαρμογή", "SSE.Views.Toolbar.txtDate": "Ημερομηνία", "SSE.Views.Toolbar.txtDateTime": "Ημερομηνία & Ώρα", "SSE.Views.Toolbar.txtDescending": "Φθίνουσα", "SSE.Views.Toolbar.txtDollar": "$ Δολάριο", "SSE.Views.Toolbar.txtEuro": "€ Ευρώ", "SSE.Views.Toolbar.txtExp": "Εκθετικό", "SSE.Views.Toolbar.txtFilter": "Φίλτρο", "SSE.Views.Toolbar.txtFormula": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.Toolbar.txtFraction": "Κλάσμα", "SSE.Views.Toolbar.txtFranc": "CHF Ελβετικά φράγκα", "SSE.Views.Toolbar.txtGeneral": "Γενικά", "SSE.Views.Toolbar.txtInteger": "Ακ<PERSON><PERSON><PERSON><PERSON><PERSON> αριθμός", "SSE.Views.Toolbar.txtManageRange": "Διαχειριστής Ονομάτων", "SSE.Views.Toolbar.txtMergeAcross": "Συγχώνευση", "SSE.Views.Toolbar.txtMergeCells": "Συγχώνευση Κελιών", "SSE.Views.Toolbar.txtMergeCenter": "Συγχώνευση & Κεντράρισμα", "SSE.Views.Toolbar.txtNamedRange": "Επώνυμα εύρη ", "SSE.Views.Toolbar.txtNewRange": "Προσδιορισμ<PERSON>ς Ονόματος", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "SSE.Views.Toolbar.txtNumber": "Αριθμός", "SSE.Views.Toolbar.txtPasteRange": "Επικόλληση Ονόματος", "SSE.Views.Toolbar.txtPercentage": "Ποσοστό", "SSE.Views.Toolbar.txtPound": "£ Λίρα", "SSE.Views.Toolbar.txtRouble": "₽ Ρούβλι", "SSE.Views.Toolbar.txtScheme1": "Γραφείο", "SSE.Views.Toolbar.txtScheme10": "Διάμεσο", "SSE.Views.Toolbar.txtScheme11": "Μετρό", "SSE.Views.Toolbar.txtScheme12": "Άρθρωμα", "SSE.Views.Toolbar.txtScheme13": "Άφθονο", "SSE.Views.Toolbar.txtScheme14": "Προεξέχον παράθυρο", "SSE.Views.Toolbar.txtScheme15": "Προέλευση", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Ηλιοστάσιο", "SSE.Views.Toolbar.txtScheme18": "Τεχνικό", "SSE.Views.Toolbar.txtScheme19": "Ταξίδι", "SSE.Views.Toolbar.txtScheme2": "Αποχρώσεις του γκρι", "SSE.Views.Toolbar.txtScheme20": "Αστικό", "SSE.Views.Toolbar.txtScheme21": "Οίστρος", "SSE.Views.Toolbar.txtScheme22": "Νέο <PERSON>αφείο", "SSE.Views.Toolbar.txtScheme3": "Άκρο", "SSE.Views.Toolbar.txtScheme4": "Άποψη", "SSE.Views.Toolbar.txtScheme5": "Κυβικό", "SSE.Views.Toolbar.txtScheme6": "Συνάθροιση", "SSE.Views.Toolbar.txtScheme7": "Μετοχή", "SSE.Views.Toolbar.txtScheme8": "Ροή", "SSE.Views.Toolbar.txtScheme9": "<PERSON>υτ<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "Επιστημονική", "SSE.Views.Toolbar.txtSearch": "Αναζήτηση", "SSE.Views.Toolbar.txtSort": "Ταξινόμηση", "SSE.Views.Toolbar.txtSortAZ": "Ταξινόμηση με αύξουσα σειρά", "SSE.Views.Toolbar.txtSortZA": "Ταξινόμηση σε φθίνουσα σειρά", "SSE.Views.Toolbar.txtSpecial": "Ειδική", "SSE.Views.Toolbar.txtTableTemplate": "Μορφοποίηση σύμφωνα με το πρότυπο πίνακα", "SSE.Views.Toolbar.txtText": "Κείμενο", "SSE.Views.Toolbar.txtTime": "Ώρα", "SSE.Views.Toolbar.txtUnmerge": "Κατάργη<PERSON>η Συγχών<PERSON>υ<PERSON>η<PERSON>ν", "SSE.Views.Toolbar.txtYen": "¥ Γιέν", "SSE.Views.Top10FilterDialog.textType": "Εμφάνιση", "SSE.Views.Top10FilterDialog.txtBottom": "Κάτω", "SSE.Views.Top10FilterDialog.txtBy": "από", "SSE.Views.Top10FilterDialog.txtItems": "Αντικείμενο", "SSE.Views.Top10FilterDialog.txtPercent": "Ποσοστό", "SSE.Views.Top10FilterDialog.txtSum": "Άθροισμα", "SSE.Views.Top10FilterDialog.txtTitle": "Κορυφαία 10 Αυτόματα Φίλτρα", "SSE.Views.Top10FilterDialog.txtTop": "Επάνω", "SSE.Views.Top10FilterDialog.txtValueTitle": "Κορυφαία 10 Φίλτρα", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Ρυθμίσεις Πεδίου Τιμών", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Βασικ<PERSON> πεδίο", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Βασικ<PERSON> πεδίο", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 από %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Μέτρηση", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Μέτρηση Αριθμών", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Προσαρμοσμένο όνομα", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Η Διαφορά Από", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Ευρετήριο", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Μέγιστο", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Ελάχιστο", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON><PERSON><PERSON><PERSON> Υπολογισμό", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Ποσοστό του", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Ποσοστό Διαφοράς Από", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Ποσοστό της Στήλης", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Ποσοστό του Συνόλου", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Ποσοστό της Γ<PERSON>αμμής", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Γινόμενο", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Τρέχον Σύνολο Σε", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Εμφάνιση τιμών ως", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Όνομα πηγής:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Τυπική Απόκλιση", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Τυπική απόκλιση πληθυσμού", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Άθροισμα", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "\nΣυνοψίστε το πεδίο τιμών κατά", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Διαφορά", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "Διακύμανση πληθυσμού", "SSE.Views.ViewManagerDlg.closeButtonText": "Κλείσιμο", "SSE.Views.ViewManagerDlg.guestText": "Επισκέπτης", "SSE.Views.ViewManagerDlg.lockText": "Κλειδωμένο", "SSE.Views.ViewManagerDlg.textDelete": "Διαγραφή", "SSE.Views.ViewManagerDlg.textDuplicate": "Αντίγραφο", "SSE.Views.ViewManagerDlg.textEmpty": "Δεν δημιουργήθηκαν ακόμα όψεις.", "SSE.Views.ViewManagerDlg.textGoTo": "Μετάβαση σε προβολή", "SSE.Views.ViewManagerDlg.textLongName": "Εισάγετε ένα όνομα μικρότερο από 128 χαρακτήρες.", "SSE.Views.ViewManagerDlg.textNew": "Νέο", "SSE.Views.ViewManagerDlg.textRename": "Μετονομασία", "SSE.Views.ViewManagerDlg.textRenameError": "Το όνομα όψης δεν μπορεί να είναι κενό.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Μετονομασία όψης", "SSE.Views.ViewManagerDlg.textViews": "Όψεις φύλλων", "SSE.Views.ViewManagerDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.ViewManagerDlg.txtTitle": "Διαχειριστής Όψης Φύλλου", "SSE.Views.ViewManagerDlg.warnDeleteView": "Προσπαθείτε να διαγράψετε την τρέχουσα επιλεγμένη όψη '%1'.<br>Κλ<PERSON><PERSON><PERSON><PERSON>μο αυτής της όψης και διαγραφή της;", "SSE.Views.ViewTab.capBtnFreeze": "Πάγωμα Παραθύρων", "SSE.Views.ViewTab.capBtnSheetView": "Όψη Φύλλου", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Να εμφανίζεται πάντα η γραμμή εργαλείων", "SSE.Views.ViewTab.textClose": "Κλείσιμο", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Συνδυ<PERSON><PERSON><PERSON><PERSON>ς γραμμών φύλλου και κατάστασης", "SSE.Views.ViewTab.textCreate": "Νέο", "SSE.Views.ViewTab.textDefault": "Προεπιλογή", "SSE.Views.ViewTab.textFormula": "Μπά<PERSON>α μαθηματικών τύπων", "SSE.Views.ViewTab.textFreezeCol": "Πάγωμα Πρώτης <PERSON>ήλης", "SSE.Views.ViewTab.textFreezeRow": "Πάγωμα Πάνω Γραμμής", "SSE.Views.ViewTab.textGridlines": "Γραμ<PERSON><PERSON>ς πλέγματος", "SSE.Views.ViewTab.textHeadings": "Επικεφαλίδες", "SSE.Views.ViewTab.textInterfaceTheme": "Θέμα διεπαφής", "SSE.Views.ViewTab.textManager": "Διαχειριστής προβολής", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Εμφάνιση σκιάς παγωμένων παραθύρων", "SSE.Views.ViewTab.textUnFreeze": "Απελευθέρωση Παραθύρων", "SSE.Views.ViewTab.textZeros": "Εμφάνιση μηδενικών", "SSE.Views.ViewTab.textZoom": "Εστίαση", "SSE.Views.ViewTab.tipClose": "Κλείσιμο προβολής φύλλου εργασίας", "SSE.Views.ViewTab.tipCreate": "Δημιουρ<PERSON><PERSON><PERSON> όψης φύλλου", "SSE.Views.ViewTab.tipFreeze": "Πάγωμα παραθύρων", "SSE.Views.ViewTab.tipSheetView": "Όψη φύλλου", "SSE.Views.WBProtection.hintAllowRanges": "Επιτρέπεται η επεξεργασία ευρών", "SSE.Views.WBProtection.hintProtectSheet": "Προστα<PERSON><PERSON><PERSON> φύλλου", "SSE.Views.WBProtection.hintProtectWB": "Προστασία βιβλίου εργασίας", "SSE.Views.WBProtection.txtAllowRanges": "Επιτρέπεται Επεξεργασία Ευρών", "SSE.Views.WBProtection.txtHiddenFormula": "Κρυφοί Τύποι", "SSE.Views.WBProtection.txtLockedCell": "Κλειδωμένο Κελί", "SSE.Views.WBProtection.txtLockedShape": "Σχήμα Κλειδωμένο", "SSE.Views.WBProtection.txtLockedText": "Κλείδωμα Κειμένου", "SSE.Views.WBProtection.txtProtectSheet": "Προστα<PERSON><PERSON><PERSON> Φύλλου", "SSE.Views.WBProtection.txtProtectWB": "Προστασία Βιβλίου Εργασίας", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Εισάγετε συνθηματικό για άρση προστασίας φύλλου", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Άρση Προστασίας Φύλλου", "SSE.Views.WBProtection.txtWBUnlockDescription": "Εισάγετε συνθηματικό για άρση προστασίας βιβλίου εργασίας", "SSE.Views.WBProtection.txtWBUnlockTitle": "Άρση Προστασίας Βιβλίου Εργασίας"}