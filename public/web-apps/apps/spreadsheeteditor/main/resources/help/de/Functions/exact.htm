﻿<!DOCTYPE html>
<html>
	<head>
		<title>IDENTISCH-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>IDENTISCH-Funktion</h1>
			<p>Die Funktion <b>IDENTISCH</b> gehört zur Gruppe der Text- und Datenfunktionen. Mit der Funktion lassen sich Daten in zwei Zellen vergleichen. Sind die Daten identisch, gibt die Funktion gibt den Wert WAHR zurück, anderfalls gibt die Funktion den Wert FALSCH zurück.</p>
			<p>Die Formelsyntax der Funktion <b>IDENTISCH</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>IDENTISCH(Text1;Text2)</em></b></p> 
			<p><b><em>Text1(2)…</em></b> ist die zu analysierende Zeichenfolge, manuell eingegeben oder in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>IDENTISCH</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>IDENTISCH</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.<p class="note"><b>Hinweis</b>: die Funktionen IDENTISCH <b>unterscheidet zwischen Groß- und Kleinbuchstaben</b>.</p>
			</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p><em>Beispiel:</em></p>
			<p>Es gibt 2 Argumente: <em>Text1</em> = <b>A1</b>; <em>Text2</em> = <b>B1</b>; <b>A1</b> ist <b>MeinPasswort</b>, <b>B1</b> ist <b>meinpasswort</b>. In diesem Fall gibt die Funktion den Wert <b>FALSCH</b> zurück.</p>
			<p style="text-indent: 150px;"><img alt="IDENTISCH-Funktion FALSCH" src="../images/exactfalse.png" /></p>
			<p>Ändert man nun alle Zeichen in <b>A1</b> von Groß- auf Kleinbuchstaben, gibt die Funktion den Wert <b>WAHR</b> zurück:</p>
			<p style="text-indent: 150px;"><img alt="IDENTISCH-Funktion WAHR" src="../images/exacttrue.png" /></p>
		</div>
	</body>
</html>