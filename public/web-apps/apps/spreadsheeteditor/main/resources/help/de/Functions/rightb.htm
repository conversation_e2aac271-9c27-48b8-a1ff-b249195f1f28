﻿<!DOCTYPE html>
<html>
	<head>
		<title>RECHTS/RECHTSB-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>RECHTS/RECHTSB-Funktion</h1>
			<p>Die Funktionen <b>RECHTS, RECHTSB</b> gehören zur Gruppe der Text- und Datenfunktionen. Sie extrahieren eine Teilzeichenfolge aus einer Zeichenfolge, beginnend mit dem Zeichen ganz rechts, basierend auf der angegebenen Anzahl von Zeichen. Die Funktion <b>RECHTS</b> ist für Sprachen gedacht, die den Single-Byte-Zeichensatz (SBCS) verwenden, während <b>RECHTSB</b> für Sprachen verwendet wird, die den Doppelbyte-Zeichensatz (DBCS) verwenden, wie Japanisch, Chinesisch, Koreanisch usw.</p>
			<p>Die Formelsyntax der Funktionen <b>RECHTS, RECHTSB</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>RECHTS(Text;[Anzahl_Zeichen])</em></b></p>
			 <p style="text-indent: 150px;"><b><em>RECHTSB(Text;[Anzahl_Zeichen])</em></b></p>
			<p><em>Dabei gilt:</em></p>
				<p style="text-indent: 50px;"><b><em>Text</em></b> ist die Zeichenfolge mit den Zeichen, die Sie extrahieren möchten.</p> 
				<p style="text-indent: 50px;"><b><em>Anzahl_Zeichen</em></b> gibt die Anzahl der Zeichen an, die von der Funktion extrahiert werden sollen. Dieses Argument ist optional. Wird an dieser Stelle kein Argument eingegeben, wird es als 1 angenommen.</p>
			<p>Die Daten können manuell eingegeben werden oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktionen <b>RECHTS, RECHTSB</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die gewünschte Funktion <b>RECHTS</b> oder <b>RECHTSB</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
            <p style="text-indent: 150px;"><img alt="RECHTS/RECHTSB-Funktion" src="../images/right.png" /></p>
		</div>
	</body>
</html>