﻿<!DOCTYPE html>
<html>
	<head>
		<title>DATUM-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>DATUM-Funktion</h1>
			<p>Die Funktion <b>DATUM</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie dient dazu eine fortlaufende Zahl zurückzugeben, die ein bestimmtes Datum darstellt, im Format <em>MM/TT/JJ</em>.</p>
			<p>Die Formelsyntax der Funktion <b>DATUM</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>DATUM(Jahr;Monat;Tag)</em></b></p> 
			<p>Die Werte für <b><em>Jahr</em></b>, <b><em>Monat</em></b> und <b><em>Tag</em></b> werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwenden der Funktion <b>DATUM</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>DATUM</b>.</li>
			<li>Geben Sie die gewünschten Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="DATUM-Funktion" src="../images/date.png" /></p>
		</div>
	</body>
</html>