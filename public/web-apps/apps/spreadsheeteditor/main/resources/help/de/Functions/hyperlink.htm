﻿<!DOCTYPE html>
<html>
	<head>
		<title>HYPERLINK-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>HYPERLINK-Funktion</h1>
			<p>Die Funktion <b>HYPERLINK</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Mit dieser Funktion lässt sich eine Verknüpfung erstellen, die zu einem anderen Speicherort in der aktuellen Arbeitsmappe wechselt oder ein Dokument öffnet, das auf einem Netzwerkserver, im Intranet oder im Internet gespeichert ist.</p>
			<p>Die Formelsyntax der Funktion <b>HYPERLINK</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>HYPERLINK(Hyperlink_Adresse, [Anzeigename])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Hyperlink_Adresse</em></b> bezeichnet Pfad und Dateiname des zu öffnenden Dokuments. <span class="onlineDocumentFeatures">In der <em>Online-Version</em> darf der Pfad ausschließlich eine URL-Adresse sein.</span> <b><em>Hyperlink_Adresse</em></b> kann sich auch auf eine bestimmte Stelle in der aktuellen Arbeitsmappe beziehen, z. B. auf eine bestimmte Zelle oder einen benannten Bereich. Der Wert kann als Textzeichenfolge in Anführungszeichen oder als Verweis auf eine Zelle, die den Link als Textzeichenfolge enthält, angegeben werden.</p> 
				<p style="text-indent: 50px;"><b><em>Anzeigename</em></b> ist ein Text, der in der Zelle angezeigt wird. Dieser Wert ist optional. Bleibt die Angabe aus, wird der Wert aus <b><em>Hyperlink_Adresse</em></b> in der Zelle angezeigt.</p>
				
			<p>Anwendung der Funktion <b>HYPERLINK</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>HYPERLINK</b>.</li>
			<li>Geben Sie die gewünschten Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
            <p>Klicken Sie den Link zum Öffnen einfach an. Um eine Zelle auszuwählen die einen Link enthält, ohne den Link zu öffnen, klicken Sie diese an und halten Sie die Maustaste gedrückt.</p>
			<p style="text-indent: 150px;"><img alt="HYPERLINK-Funktion" src="../images/hyperlinkfunction.png" /></p>
		</div>
	</body>
</html>