﻿<!DOCTYPE html>
<html>
	<head>
		<title>INDIREKT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>INDIREKT-Funktion</h1>
			<p>Die Funktion <b>INDIREKT</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Sie gibt den Bezug eines Textwerts zurück.</p>
			<p>Die Formelsyntax der Funktion <b>INDIREKT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>INDIREKT(Bezug;[A1])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Bezug</em></b> ist eine Textdarstellung einer Zelle.</p> 
				<p style="text-indent: 50px;"><b><em>A1</em></b> ist die Schreibweise. Dieser Wahrheitswert ist optional. WAHR oder FALSCH. Ist das A1-Argument mit WAHR belegt oder nicht angegeben, liegt der von der Funktion ADRESSE gelieferte <b><em>Bezug</em></b> in A1-Schreibweise vor. Ist das A1-Argument mit FALSCH belegt, liegt der von der Funktion gelieferte <b><em>Bezug</em></b> in der Z1S1-Schreibweise vor.</p>
				
			<p>Anwendung der Funktion <b>INDIREKT</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>INDIREKT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="INDIREKT-Funktion" src="../images/indirect.png" /></p>
		</div>
	</body>
</html>