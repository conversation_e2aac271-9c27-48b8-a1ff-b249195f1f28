﻿<!DOCTYPE html>
<html>
	<head>
		<title>ISTNV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ISTNV-Funktion</h1>
			<p>Die Funktion <b>ISTNV</b> gehört zur Gruppe der Informationsfunktionen. Sie gibt den Fehlerwert #NV zurück Wenn die Zelle einen #N/V-Fehlerwert enthält, gibt die Funktion WAHR zurück, andernfalls gibt die Funktion FALSCH zurück.</p>
			<p>Die Formelsyntax der Funktion <b>ISTNV</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ISTNV(Wert)</em></b></p> 
			<p><b><em>Wert</em></b> gibt den Wert wieder der geprüft werden soll. Das Argument für den Wert kann eine leere Zelle, ein Fehlerwert, ein Wahrheitswert, Text, eine Zahl, ein Bezugswert oder ein Name sein, der sich auf eine dieser Möglichkeiten bezieht.</p>
			<p><b>ISTNV</b>-Funktion anwenden:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ISTNV</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Enter</b>-Taste.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ISTNV-Funktion" src="../images/isna.png" /></p>
		</div>
	</body>
</html>