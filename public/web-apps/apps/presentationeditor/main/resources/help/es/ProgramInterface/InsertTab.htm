﻿<!DOCTYPE html>
<html>
	<head>
		<title>Pestaña Insertar</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - Insert tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Pestaña Insertar</h1>
            <p>La pestaña <b>Insertar</b> permite añadir objetos visuales y comentarios a su presentación.</p>
            <div class="onlineDocumentFeatures">
                <p>Ventana del editor de presentaciones en línea:</p>
                <p><img alt="Pestaña Insertar" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Ventana del editor de presentaciones de escritorio:</p>
                <p><img alt="Pestaña Insertar" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>Al usar esta pestaña podrás:</p>
            <ul>
                <li>insertar <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tablas</a>,</li>
                <li>Insertar <a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">cuadros de texto y objetos Text Art</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">imágenes</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">formas</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">gráficos</a>,</li>
                <li>insertar <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">comentarios</a> y <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">hiperenlaces</a>,</li>
                <li>insertar <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">ecuaciones</a>.</li>
            </ul>
		</div>
	</body>
</html>