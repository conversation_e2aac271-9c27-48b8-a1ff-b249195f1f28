{"cancelButtonText": "Storno", "Common.Controllers.Chat.notcriticalErrorTitle": "Varování", "Common.Controllers.Chat.textEnterMessage": "Zde napište svou zprávu", "Common.Controllers.History.notcriticalErrorTitle": "Varování", "Common.define.chartData.textArea": "Plošný graf", "Common.define.chartData.textAreaStacked": "Skládaný plošný", "Common.define.chartData.textAreaStackedPer": "100% skládaný plošný", "Common.define.chartData.textBar": "Pruhov<PERSON> graf", "Common.define.chartData.textBarNormal": "Skupinový sloupcový", "Common.define.chartData.textBarNormal3d": "3D skupinový sloupcový", "Common.define.chartData.textBarNormal3dPerspective": "3D sloupcový", "Common.define.chartData.textBarStacked": "Skl<PERSON>dan<PERSON> slou<PERSON>vý", "Common.define.chartData.textBarStacked3d": "3D skládaný sloupcový", "Common.define.chartData.textBarStackedPer": "100% sk<PERSON><PERSON><PERSON><PERSON> sloupcový", "Common.define.chartData.textBarStackedPer3d": "3D 100% skl<PERSON>daný sloupcový", "Common.define.chartData.textCharts": "<PERSON><PERSON>", "Common.define.chartData.textColumn": "Sloupcový graf", "Common.define.chartData.textColumnSpark": "Sloupec", "Common.define.chartData.textCombo": "<PERSON><PERSON><PERSON><PERSON>ý", "Common.define.chartData.textComboAreaBar": "Plošný - skupinový sloupcový", "Common.define.chartData.textComboBarLine": "Skupinový sloupcový - spojnice", "Common.define.chartData.textComboBarLineSecondary": "Skupinový sloupcový - spojnice na sekundární ose", "Common.define.chartData.textComboCustom": "Uživatelsky určená kombinace", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Skupinový pruhový", "Common.define.chartData.textHBarNormal3d": "3D Skupinový pruhový", "Common.define.chartData.textHBarStacked": "Skl<PERSON>daný", "Common.define.chartData.textHBarStacked3d": "3D skládaný sloupcový", "Common.define.chartData.textHBarStackedPer": "100% sk<PERSON><PERSON><PERSON><PERSON> sloupcový", "Common.define.chartData.textHBarStackedPer3d": "3D 100% skl<PERSON>daný sloupcový", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON> graf", "Common.define.chartData.textLine3d": "3D spojnicový", "Common.define.chartData.textLineMarker": "Spojnicový se značkami", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Skládaný spojnicový", "Common.define.chartData.textLineStackedMarker": "Skládaný spojnicový se značkami", "Common.define.chartData.textLineStackedPer": "100% skl<PERSON>daný spojnicový", "Common.define.chartData.textLineStackedPerMarker": "100% skládaný spojnicový se značkami", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON><PERSON> diagram", "Common.define.chartData.textPie3d": "3D výsečový", "Common.define.chartData.textPoint": "Bodov<PERSON> graf", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Bodový s rovnými spojnicemi", "Common.define.chartData.textScatterLineMarker": "Bodový s vyhlazenými spojnicemi a značkami", "Common.define.chartData.textScatterSmooth": "Bodový s vyhlazenými spojnicemi", "Common.define.chartData.textScatterSmoothMarker": "Bodový s vyhlazenými spojnicemi a značkami", "Common.define.chartData.textSparks": "Mikrografy", "Common.define.chartData.textStock": "Burzovní graf", "Common.define.chartData.textSurface": "Povrch", "Common.define.chartData.textWinLossSpark": "Zisk/<PERSON>tr<PERSON> ", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Nenastaven žádný <PERSON>", "Common.define.conditionalData.text1Above": "Na 1 standardní odchylka výše", "Common.define.conditionalData.text1Below": "Na 1 standardní odchylka níže", "Common.define.conditionalData.text2Above": "2 Směrodatná odchylka nad", "Common.define.conditionalData.text2Below": "2 Směrodatná odchylka pod", "Common.define.conditionalData.text3Above": "3 Směrodatná odchylka nad", "Common.define.conditionalData.text3Below": "3 Směrodatná odchylka pod", "Common.define.conditionalData.textAbove": "Nad", "Common.define.conditionalData.textAverage": "Průměrné", "Common.define.conditionalData.textBegins": "Začíná na", "Common.define.conditionalData.textBelow": "Pod", "Common.define.conditionalData.textBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textBlank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlanks": "Obsahuje prázdno", "Common.define.conditionalData.textBottom": "<PERSON><PERSON>", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Datový pruh", "Common.define.conditionalData.textDate": "Datum", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Končí na", "Common.define.conditionalData.textEqAbove": "Větší nebo rovno", "Common.define.conditionalData.textEqBelow": "Menší nebo rovno", "Common.define.conditionalData.textEqual": "Rovná se", "Common.define.conditionalData.textError": "Chyba", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textFormula": "Vzorec", "Common.define.conditionalData.textGreater": "větš<PERSON> než", "Common.define.conditionalData.textGreaterEq": "Větší nebo rovno", "Common.define.conditionalData.textIconSets": "<PERSON><PERSON> <PERSON>", "Common.define.conditionalData.textLast7days": "v uplynulých 7 dnech", "Common.define.conditionalData.textLastMonth": "Poslední <PERSON>", "Common.define.conditionalData.textLastWeek": "Poslední týden", "Common.define.conditionalData.textLess": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLessEq": "Méně než nebo rovno", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotBetween": "Ne mezi", "Common.define.conditionalData.textNotBlanks": "Neobsahuje prázdné", "Common.define.conditionalData.textNotContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotEqual": "Nerov<PERSON> se", "Common.define.conditionalData.textNotErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON>", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON>", "Common.define.conditionalData.textToday": "Dnes", "Common.define.conditionalData.textTomorrow": "<PERSON>í<PERSON>", "Common.define.conditionalData.textTop": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textUnique": "Neopakující se", "Common.define.conditionalData.textValue": "Hodnota je", "Common.define.conditionalData.textYesterday": "Včera", "Common.Translation.textMoreButton": "V<PERSON>ce", "Common.Translation.warnFileLocked": "Soubor je upravován v jiné aplikaci. Můžete pokračovat v úpravách a uložit ho jako kopii.", "Common.Translation.warnFileLockedBtnEdit": "Vytvořit kopii", "Common.Translation.warnFileLockedBtnView": "Otevřít pro náhled", "Common.UI.ButtonColored.textAutoColor": "Automatické", "Common.UI.ButtonColored.textNewColor": "Přidat novou vlastní barvu", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON>z oh<PERSON>í", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON>z oh<PERSON>í", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON> styly", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Stávající", "Common.UI.ExtendedColorDialog.textHexErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu z rozmezí 000000 až FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nová", "Common.UI.ExtendedColorDialog.textRGBErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu z rozmezí 0 až 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> barvy", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Zobrazit heslo", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Následující", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Otevřít pokročilé nastavení", "Common.UI.SearchBar.tipPreviousResult": "Předchozí", "Common.UI.SearchDialog.textHighlight": "Zvýraznit výsledky", "Common.UI.SearchDialog.textMatchCase": "Roz<PERSON>šovat malá a velká písmena", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> text, k<PERSON><PERSON>m nahradit", "Common.UI.SearchDialog.textSearchStart": "Sem zadejte text k vyhledání", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> a na<PERSON>", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON>uze celá slova", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Nahradit", "Common.UI.SearchDialog.txtBtnReplaceAll": "Nahradit vše", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON> už nezobrazovat", "Common.UI.SynchronizeTip.textSynchronize": "Dokument byl mezitím změněn jiným uživatelem.<br>Kliknutím uložte změny provedené vámi a načtěte ty od ostatních.", "Common.UI.ThemeColorPalette.textRecentColors": "Aktuální barvy", "Common.UI.ThemeColorPalette.textStandartColors": "Standardní barvy", "Common.UI.ThemeColorPalette.textThemeColors": "Barvy motivu vzhledu", "Common.UI.Themes.txtThemeClassicLight": "Standartní světlost", "Common.UI.Themes.txtThemeDark": "Tmavé", "Common.UI.Themes.txtThemeLight": "Světl<PERSON>", "Common.UI.Themes.txtThemeSystem": "<PERSON><PERSON><PERSON><PERSON> jako <PERSON>", "Common.UI.Window.cancelButtonText": "Storno", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Ne", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Potvrzení", "Common.UI.Window.textDontShow": "<PERSON><PERSON> už nezobrazovat", "Common.UI.Window.textError": "Chyba", "Common.UI.Window.textInformation": "Informace", "Common.UI.Window.textWarning": "Varování", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "adresa:", "Common.Views.About.txtLicensee": "DRŽITEL LICENCE", "Common.Views.About.txtLicensor": "UDĚLOVATEL LICENCE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Poháněno", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Verze", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Aplikovat během práce", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorekce", "Common.Views.AutoCorrectDialog.textAutoFormat": "Automaticky formátovat během psaní", "Common.Views.AutoCorrectDialog.textBy": "Od", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textHyperlink": "Internetové a síťové přístupy s hypertextovými odkazy", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autokorekce pro matematiku", "Common.Views.AutoCorrectDialog.textNewRowCol": "Zahrnout nové řádky a sloupce v tabulce", "Common.Views.AutoCorrectDialog.textRecognized": "Rozpozná<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Následující výrazy jsou rozpoznány jako matematic<PERSON>. Nebudou aplikována pravidla týkajících se velkých a malých písmen.", "Common.Views.AutoCorrectDialog.textReplace": "Nahradit", "Common.Views.AutoCorrectDialog.textReplaceText": "Nahraz<PERSON>t b<PERSON><PERSON> psaní", "Common.Views.AutoCorrectDialog.textReplaceType": "Nahrazovat text během psaní", "Common.Views.AutoCorrectDialog.textReset": "Obnovit", "Common.Views.AutoCorrectDialog.textResetAll": "Vrátit zpět do výchozích hodnot", "Common.Views.AutoCorrectDialog.textRestore": "Obnovit", "Common.Views.AutoCorrectDialog.textTitle": "Autokorekce", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON> třeba, aby r<PERSON><PERSON><PERSON><PERSON><PERSON> obsahovaly pouze písmena A až Z (velká či malá).", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON> z<PERSON> výrazy budou odstraněny a odstraněné budou obnoveny. Opravdu chcete pokračovat? ", "Common.Views.AutoCorrectDialog.warnReplace": "Hodnota pro automatické opravy %1 již existuje. Opravdu ji chcete nahradit?", "Common.Views.AutoCorrectDialog.warnReset": "Vámi přidané autokorekce budou odstraněny a změněné budou obnoveny do výchozích hodnot. Opravdu chcete pokračovat? ", "Common.Views.AutoCorrectDialog.warnRestore": "Hodnota pro automatické opravy %1 bude nastavena na původní hodnotu. Opravdu chcete pokračovat?", "Common.Views.Chat.textSend": "Poslat", "Common.Views.Comments.mniAuthorAsc": "Autor A až Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z až A", "Common.Views.Comments.mniDateAsc": "Nejstarší", "Common.Views.Comments.mniDateDesc": "Nejnovější", "Common.Views.Comments.mniFilterGroups": "Filtrovat podle skupiny", "Common.Views.Comments.mniPositionAsc": "Shora", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Přidat komentář k dokumentu", "Common.Views.Comments.textAddReply": "Přidat odpověď", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Návštěvník", "Common.Views.Comments.textCancel": "Storno", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Zavřít komentář<PERSON>", "Common.Views.Comments.textComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Sem napište svůj komentář", "Common.Views.Comments.textHintAddComment": "Přidat komentář", "Common.Views.Comments.textOpenAgain": "Znovu otevřít", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Vyřešit", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Řadit koment<PERSON><PERSON>e", "Common.Views.Comments.textViewResolved": "Nemáte oprávnění pro opětovné otevír<PERSON><PERSON> k<PERSON>", "Common.Views.Comments.txtEmpty": "Tento list neobs<PERSON><PERSON> k<PERSON>.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON> už nezobrazovat", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON> k<PERSON>, vyjmout a vložit použitím lišty nástrojů editoru a kontextové nabídky budou prováděny pouze v tomto okně editoru.<br><br>Pro kopírování do nebo vkládání z aplikací mimo okno editoru použijte následující klávesové zkratky:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON> k<PERSON>, vyjmout a vložit", "Common.Views.CopyWarningDialog.textToCopy": "pro zkopírování", "Common.Views.CopyWarningDialog.textToCut": "pro vyjmutí", "Common.Views.CopyWarningDialog.textToPaste": "pro vložení", "Common.Views.DocumentAccessDialog.textLoading": "Načít<PERSON><PERSON>…", "Common.Views.DocumentAccessDialog.textTitle": "Nastavení sdílení", "Common.Views.EditNameDialog.textLabel": "Štítek:", "Common.Views.EditNameDialog.textLabelError": "Štítek je nutné vyplnit.", "Common.Views.Header.labelCoUsersDescr": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> soubor právě upravují:", "Common.Views.Header.textAddFavorite": "Označit jako ob<PERSON>", "Common.Views.Header.textAdvSettings": "Pokročilá nastavení", "Common.Views.Header.textBack": "Otev<PERSON><PERSON><PERSON>ě<PERSON>í so<PERSON>u", "Common.Views.Header.textCompactView": "Skrýt panel nástrojů", "Common.Views.Header.textHideLines": "Skrýt pravítka", "Common.Views.Header.textHideStatusBar": "Zkombinovat listy a datové pruhy", "Common.Views.Header.textRemoveFavorite": "Odebrat z oblíbených", "Common.Views.Header.textSaveBegin": "Ukládání…", "Common.Views.Header.textSaveChanged": "Změněno", "Common.Views.Header.textSaveEnd": "Všechny změny uloženy", "Common.Views.Header.textSaveExpander": "Všechny změny uloženy", "Common.Views.Header.textShare": "Sdílet", "Common.Views.Header.textZoom": "Přiblížení", "Common.Views.Header.tipAccessRights": "Spravovat přístupová práva k dokumentům", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Upravit stávající soubor", "Common.Views.Header.tipPrint": "Vytisknout soubor", "Common.Views.Header.tipRedo": "Znovu", "Common.Views.Header.tipSave": "Uložit", "Common.Views.Header.tipSearch": "Hledat", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Oddělit do zvlášť okna", "Common.Views.Header.tipUsers": "Zobrazit uživatele", "Common.Views.Header.tipViewSettings": "Zobrazit nastavení", "Common.Views.Header.tipViewUsers": "Zobrazte uživatele a spravujte přístupová práva k dokumentům", "Common.Views.Header.txtAccessRights": "Změnit přístupová práva", "Common.Views.Header.txtRename": "Př<PERSON>menovat", "Common.Views.History.textCloseHistory": "Zavřít historii", "Common.Views.History.textHide": "Sbal<PERSON>", "Common.Views.History.textHideAll": "Skrýt podrobné změny", "Common.Views.History.textRestore": "Obnovit", "Common.Views.History.textShow": "Rozbalit", "Common.Views.History.textShowAll": "Zobrazit změny podrobně", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Vložte URL adresu obrázku:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> kolo<PERSON> by <PERSON><PERSON><PERSON> b<PERSON>t URL adresa ve formátu „http://www.example.com“", "Common.Views.ListSettingsDialog.textBulleted": "S odrážkami", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON><PERSON> souboru", "Common.Views.ListSettingsDialog.textFromStorage": "Z úložiště", "Common.Views.ListSettingsDialog.textNumbering": "Číslovaný", "Common.Views.ListSettingsDialog.tipChange": "Změnit odrážku", "Common.Views.ListSettingsDialog.txtBullet": "Odr<PERSON>ž<PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "Nová odrážka", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% textu", "Common.Views.ListSettingsDialog.txtSize": "Velikost", "Common.Views.ListSettingsDialog.txtStart": "Začít na", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "Nastavení seznamu", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Neplatný rozsah buněk", "Common.Views.OpenDialog.textSelectData": "Vybrat data", "Common.Views.OpenDialog.txtAdvanced": "Pokroč<PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Dvojtečka", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Odd<PERSON><PERSON><PERSON>č", "Common.Views.OpenDialog.txtDestData": "Zvolte kam umístit data", "Common.Views.OpenDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "Common.Views.OpenDialog.txtEncoding": "Kódován<PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> není správn<PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Zadejte heslo pro otevření souboru", "Common.Views.OpenDialog.txtOther": "Ostatní", "Common.Views.OpenDialog.txtPassword": "He<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON><PERSON> heslo a soubor otevř<PERSON>, stávající heslo k souboru bude reset<PERSON>no.", "Common.Views.OpenDialog.txtSemicolon": "Středník", "Common.Views.OpenDialog.txtSpace": "Mezera", "Common.Views.OpenDialog.txtTab": "Tabulátory", "Common.Views.OpenDialog.txtTitle": "Vyberte %1 možností", "Common.Views.OpenDialog.txtTitleProtected": "Zabezpečený soubor", "Common.Views.PasswordDialog.txtDescription": "Nastavit heslo pro zabezpečení tohoto dokumentu", "Common.Views.PasswordDialog.txtIncorrectPwd": "Zadání hesla a jeho potvrzení se neshodují", "Common.Views.PasswordDialog.txtPassword": "He<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Varování: Ztracené nebo zapomenuté he<PERSON>lo nelze obnovit. Uložte ji na bezpečném místě.", "Common.Views.PluginDlg.textLoading": "Načítá se", "Common.Views.Plugins.groupCaption": "Z<PERSON>uv<PERSON><PERSON> moduly", "Common.Views.Plugins.strPlugins": "Z<PERSON>uv<PERSON><PERSON> moduly", "Common.Views.Plugins.textClosePanel": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>uvné moduly", "Common.Views.Plugins.textLoading": "Načítá se", "Common.Views.Plugins.textStart": "Začátek", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Protection.hintAddPwd": "Šifrovat heslem", "Common.Views.Protection.hintPwd": "Změnit nebo odebrat he<PERSON>lo", "Common.Views.Protection.hintSignature": "Přidat digitální podpis nebo", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "Odstranit heslo", "Common.Views.Protection.txtEncrypt": "Šifrovat", "Common.Views.Protection.txtInvisibleSignature": "Přidat digit<PERSON>lní <PERSON>", "Common.Views.Protection.txtSignature": "Podpis", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "Název souboru nemůže obsahovat žádný z následujících znaků:", "Common.Views.ReviewChanges.hintNext": "K další změně", "Common.Views.ReviewChanges.hintPrev": "K předchozí změně", "Common.Views.ReviewChanges.strFast": "Automatický", "Common.Views.ReviewChanges.strFastDesc": "Společně prováděná úprava v reálném čase. Veškeré změny jsou ukládány automaticky.", "Common.Views.ReviewChanges.strStrict": "Statický", "Common.Views.ReviewChanges.strStrictDesc": "Pro synchronizaci změn, k<PERSON><PERSON> jste udělali vy a ostatní, použijte tlačítko „Uložit“.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, která právě proběhla", "Common.Views.ReviewChanges.tipCoAuthMode": "Nastavit režim spolupráce na úpravách", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Odebrat právě zobrazovaný komentář", "Common.Views.ReviewChanges.tipCommentResolve": "Vyřešit komentáře", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Vyřešit stávající komentáře", "Common.Views.ReviewChanges.tipHistory": "Zobrazit historii verzí", "Common.Views.ReviewChanges.tipRejectCurrent": "Odmí<PERSON><PERSON><PERSON> z<PERSON>nu, která právě proběhla", "Common.Views.ReviewChanges.tipReview": "Sledovat změny", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON><PERSON>, ve kterém chcete zobrazit změny", "Common.Views.ReviewChanges.tipSetDocLang": "Nastavit jazyk dokumentu", "Common.Views.ReviewChanges.tipSetSpelling": "Kontrola pra<PERSON>pisu", "Common.Views.ReviewChanges.tipSharing": "Spravovat přístupová práva k dokumentům", "Common.Views.ReviewChanges.txtAccept": "Př<PERSON><PERSON>mout", "Common.Views.ReviewChanges.txtAcceptAll": "Přijmout všechny změny", "Common.Views.ReviewChanges.txtAcceptChanges": "Přijmout změny", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, která právě proběhla", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON><PERSON> s<PERSON>upráce na úpravách", "Common.Views.ReviewChanges.txtCommentRemAll": "Odstranit všechny komentáře", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Odebrat právě zobrazovaný komentář", "Common.Views.ReviewChanges.txtCommentRemMy": "Odebrat mé komentáře", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Odebrat mé stávající komentáře", "Common.Views.ReviewChanges.txtCommentRemove": "Odstranit", "Common.Views.ReviewChanges.txtCommentResolve": "Vyřešit", "Common.Views.ReviewChanges.txtCommentResolveAll": "Vyřešit všechny komentáře", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Vyřešit stávající komentáře", "Common.Views.ReviewChanges.txtCommentResolveMy": "Vyřešit moje komentáře", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Vyřešit moje aktuální komentáře", "Common.Views.ReviewChanges.txtDocLang": "Jazyk", "Common.Views.ReviewChanges.txtFinal": "Všechny změny přijaty (náhled)", "Common.Views.ReviewChanges.txtFinalCap": "Konečné", "Common.Views.ReviewChanges.txtHistory": "Historie verzí", "Common.Views.ReviewChanges.txtMarkup": "Všechny změny (upravení)", "Common.Views.ReviewChanges.txtMarkupCap": "Značka", "Common.Views.ReviewChanges.txtNext": "Dalš<PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Všechny změny by<PERSON> (náhled)", "Common.Views.ReviewChanges.txtOriginalCap": "Původní", "Common.Views.ReviewChanges.txtPrev": "Předchozí", "Common.Views.ReviewChanges.txtReject": "Odmítnout", "Common.Views.ReviewChanges.txtRejectAll": "Odmítnout všechny změny", "Common.Views.ReviewChanges.txtRejectChanges": "Odmítnout změny", "Common.Views.ReviewChanges.txtRejectCurrent": "Odmí<PERSON><PERSON><PERSON> z<PERSON>nu, která právě proběhla", "Common.Views.ReviewChanges.txtSharing": "Sdílení", "Common.Views.ReviewChanges.txtSpelling": "Kontrola pra<PERSON>pisu", "Common.Views.ReviewChanges.txtTurnon": "Sledovat změny", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "Přidat odpověď", "Common.Views.ReviewPopover.textCancel": "Storno", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+zmínění poskytne přístup k dokumentu a pošle e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+zmínění upozorní uživatele e-mailem", "Common.Views.ReviewPopover.textOpenAgain": "Znovu otevřít", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Vyřešit", "Common.Views.ReviewPopover.textViewResolved": "Nemáte oprávnění pro opětovné otevír<PERSON><PERSON> k<PERSON>", "Common.Views.ReviewPopover.txtDeleteTip": "Odstranit", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Načítá se", "Common.Views.SaveAsDlg.textTitle": "Složka do které uložit", "Common.Views.SearchPanel.textByColumns": "<PERSON> slou<PERSON>", "Common.Views.SearchPanel.textByRows": "<PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "Roz<PERSON>šovat malá a velká písmena", "Common.Views.SearchPanel.textCell": "Buňka", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON><PERSON><PERSON> a na<PERSON>", "Common.Views.SearchPanel.textFormula": "Vzorec", "Common.Views.SearchPanel.textFormulas": "Vzorce", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON><PERSON> o<PERSON><PERSON> b<PERSON>", "Common.Views.SearchPanel.textLookIn": "Hledat v", "Common.Views.SearchPanel.textMatchUsingRegExp": "Argument používá regulární výraz", "Common.Views.SearchPanel.textName": "Jméno", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON><PERSON> nebyla na<PERSON>a", "Common.Views.SearchPanel.textNoSearchResults": "Žádné výsledky vyhledávání", "Common.Views.SearchPanel.textReplace": "Nahradit", "Common.Views.SearchPanel.textReplaceAll": "Nahradit vše", "Common.Views.SearchPanel.textReplaceWith": "Nahradit pomocí", "Common.Views.SearchPanel.textSearch": "Hledat", "Common.Views.SearchPanel.textSearchOptions": "Možnosti hledání", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON><PERSON>dek hledání: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON><PERSON><PERSON><PERSON> rozsah dat", "Common.Views.SearchPanel.textSheet": "List", "Common.Views.SearchPanel.textSpecificRange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textTooManyResults": "Př<PERSON><PERSON>š mnoho výsledků pro zobrazení", "Common.Views.SearchPanel.textValue": "Hodnota", "Common.Views.SearchPanel.textValues": "Hodnoty", "Common.Views.SearchPanel.textWholeWords": "<PERSON>uze celá slova", "Common.Views.SearchPanel.textWithin": "V rámci", "Common.Views.SearchPanel.textWorkbook": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.tipNextResult": "Následující", "Common.Views.SearchPanel.tipPreviousResult": "Předchozí", "Common.Views.SelectFileDlg.textLoading": "Načítá se", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> zdroj dat", "Common.Views.SignDialog.textBold": "Tučn<PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Změnit", "Common.Views.SignDialog.textInputName": "Zadat j<PERSON> podep<PERSON>uj<PERSON>", "Common.Views.SignDialog.textItalic": "Skloněné", "Common.Views.SignDialog.textNameError": "Je třeba vyplnit jméno podepisujícího.", "Common.Views.SignDialog.textPurpose": "Účel podepsání tohoto dokumentu", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON>y<PERSON><PERSON> o<PERSON>", "Common.Views.SignDialog.textSignature": "Podpis vypadá jako", "Common.Views.SignDialog.textTitle": "Podepsat dokument", "Common.Views.SignDialog.textUseImage": "Nebo klikněte „Vybrat obrázek“ a použijte ho jako podpis", "Common.Views.SignDialog.textValid": "Platné od %1 do %2", "Common.Views.SignDialog.tipFontName": "Název písma", "Common.Views.SignDialog.tipFontSize": "Velikost písma", "Common.Views.SignSettingsDialog.textAllowComment": "Umožnit podepisujícímu přidat komentář", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON><PERSON> pod<PERSON>", "Common.Views.SignSettingsDialog.textInstructions": "Pokyny pro podepisujícího", "Common.Views.SignSettingsDialog.textShowDate": "Na řádku s podpisem zobrazit datum podpisu", "Common.Views.SignSettingsDialog.textTitle": "Nastavení podpisu", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "Common.Views.SymbolTableDialog.textCharacter": "Znak", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX hodnota", "Common.Views.SymbolTableDialog.textCopyright": "Znak autorských práv", "Common.Views.SymbolTableDialog.textDCQuote": "Uzavírací dvojitá uvozovka", "Common.Views.SymbolTableDialog.textDOQuote": "Otevírací dvojitá uvozovka", "Common.Views.SymbolTableDialog.textEllipsis": "Vodorovná výpustka", "Common.Views.SymbolTableDialog.textEmDash": "Dlouhá pomlčka", "Common.Views.SymbolTableDialog.textEmSpace": "Dlouhá mezera", "Common.Views.SymbolTableDialog.textEnDash": "Krátká pomlčka", "Common.Views.SymbolTableDialog.textEnSpace": "Krátká mezera", "Common.Views.SymbolTableDialog.textFont": "Písmo", "Common.Views.SymbolTableDialog.textNBHyphen": "pevná pomlčka", "Common.Views.SymbolTableDialog.textNBSpace": "Nezlomitelná mezera", "Common.Views.SymbolTableDialog.textPilcrow": "Značka odstavce", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 em mezera", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Nedávno použité symboly", "Common.Views.SymbolTableDialog.textRegistered": "Symbol registrované ochranné z<PERSON>ky", "Common.Views.SymbolTableDialog.textSCQuote": "Uzavírací jednoduchá uvozovka", "Common.Views.SymbolTableDialog.textSection": "Paragra<PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON>ová zkratka", "Common.Views.SymbolTableDialog.textSHyphen": "Měkká pomlčka", "Common.Views.SymbolTableDialog.textSOQuote": "Otevírací jednoduchá uvozovka", "Common.Views.SymbolTableDialog.textSpecial": "Speciální znaky", "Common.Views.SymbolTableDialog.textSymbols": "Symboly", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Symbol užívané obchodní značky", "Common.Views.UserNameDialog.textDontShow": "<PERSON>novu se neptat", "Common.Views.UserNameDialog.textLabel": "Štítek:", "Common.Views.UserNameDialog.textLabelError": "Štítek je nutné vyplnit.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "Je třeba zadat URL adresu.", "SSE.Controllers.DataTab.textRows": "Řádky", "SSE.Controllers.DataTab.textWizard": "Text na sloupce", "SSE.Controllers.DataTab.txtDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat", "SSE.Controllers.DataTab.txtExpand": "Rozbalit", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Data v okolí výběru nebudou odstraněny. Chcete rozšířit výběr, aby zahrnoval sousední data, nebo pokračovat pouze s aktuálně vybranými buňkami?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Výb<PERSON>r obsahuje některé buňky bez nastaveného ověření dat.<br> <PERSON>cete rozšířit ověřování dat na tyto buňky?", "SSE.Controllers.DataTab.txtImportWizard": "Průvodce importem textu", "SSE.Controllers.DataTab.txtRemDuplicates": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Výběr obsahuje více než jeden typ ověření.<br>Vymazat aktuální nastavení a pokračovat?", "SSE.Controllers.DataTab.txtRemSelected": "Vyjmout vybrané", "SSE.Controllers.DataTab.txtUrlTitle": "Zadejte URL, na které se nacházejí data", "SSE.Controllers.DocumentHolder.alignmentText": "Zarovnání", "SSE.Controllers.DocumentHolder.centerText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "Odstranit", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Odkaz neexistuje. Prosím, opravte odkaz nebo ho odstraňte.", "SSE.Controllers.DocumentHolder.guestText": "Návštěvník", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Sloupec vlevo", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Sloupec vpravo", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Řádek nad", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Řádek pod", "SSE.Controllers.DocumentHolder.insertText": "Vložit", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Varování", "SSE.Controllers.DocumentHolder.rightText": "Vpravo", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Autokorekce možnosti", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON><PERSON> sloupce {0} symboly ({1} pixelů)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Výška řádku {0} bod<PERSON> ({1} pixelů)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Buď klikněte a otevřete odkaz nebo klikněte a podržte a pro označení buňky.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Vložit vlevo", "SSE.Controllers.DocumentHolder.textInsertTop": "Vložit nahoru", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Vložit jinak", "SSE.Controllers.DocumentHolder.textStopExpand": "Zastavit <PERSON> r<PERSON>řování tabulek", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Nadprůměrné", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Přidat zlomkovou čáru", "SSE.Controllers.DocumentHolder.txtAddHor": "Přidat vodorovnou čáru", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON><PERSON><PERSON> vlevo <PERSON>", "SSE.Controllers.DocumentHolder.txtAddLeft": "Přidat ohraničení vlevo", "SSE.Controllers.DocumentHolder.txtAddLT": "Přidat čáru vlevo nahoře", "SSE.Controllers.DocumentHolder.txtAddRight": "Přidat ohraničení vpravo", "SSE.Controllers.DocumentHolder.txtAddTop": "Přidat ohraničení nahoře", "SSE.Controllers.DocumentHolder.txtAddVer": "Přidat svislou čáru", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Zarovnat vůči znaku", "SSE.Controllers.DocumentHolder.txtAll": "(vše)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON> obs<PERSON> tabul<PERSON>, nebo def<PERSON><PERSON><PERSON><PERSON> sloup<PERSON> tabulky tj. <PERSON><PERSON><PERSON><PERSON><PERSON>, data a celkový počet řádků", "SSE.Controllers.DocumentHolder.txtAnd": "a", "SSE.Controllers.DocumentHolder.txtBegins": "Začíná na", "SSE.Controllers.DocumentHolder.txtBelowAve": "Podprůměrné", "SSE.Controllers.DocumentHolder.txtBlanks": "(prázdné)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Vlastnosti ohraničení", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumn": "Sloupec", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Zarovnání sloupce", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Odkaz zkopírován do schránky", "SSE.Controllers.DocumentHolder.txtDataTableHint": "<PERSON><PERSON><PERSON><PERSON> hodnotu buněk tabulky, nebo def<PERSON><PERSON><PERSON><PERSON> slou<PERSON><PERSON> tabulky", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Snížit velikost argumentu", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Odstranit argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Odstranit ruční zalomení", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Odstranit vložené znaky ", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Odstranit vložené znaky a oddělovače", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Odstranit rovnici", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Odstranit znak", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Odstranit základ", "SSE.Controllers.DocumentHolder.txtEnds": "Končí na", "SSE.Controllers.DocumentHolder.txtEquals": "Rovná se", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Shodné s barvou buňky", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Shodné s barvou písma", "SSE.Controllers.DocumentHolder.txtExpand": "Rozbalit a seřadit", "SSE.Controllers.DocumentHolder.txtExpandSort": "Data vedle výběru nebudou seřazena. Chcete rozšířit výběr tak, aby zahrnoval sousední data, nebo pokračovat v seřazení pouze vybraných buněk?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Nahoře", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Změnit na lineární zlomek", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Změnit na zkosený zlomek", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Změnit na složený zlomek", "SSE.Controllers.DocumentHolder.txtGreater": "Větš<PERSON> než", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Větší nebo rovno", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Znak nad textem", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Znak pod textem", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Získá hlavičky v tabulce, nebo hlavičky definovaných sloupců tabulky", "SSE.Controllers.DocumentHolder.txtHeight": "Výška", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Skrýt dolní limit", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Skrýt uzavírací závorku", "SSE.Controllers.DocumentHolder.txtHideDegree": "Skrýt stupeň", "SSE.Controllers.DocumentHolder.txtHideHor": "Skrýt vodorovnou čáru", "SSE.Controllers.DocumentHolder.txtHideLB": "Skrýt levou dolní čáru", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON>k<PERSON><PERSON><PERSON> vlevo", "SSE.Controllers.DocumentHolder.txtHideLT": "Skrýt levou horní čáru", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Skrýt otevírací závorku", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> zás<PERSON>ný symbol", "SSE.Controllers.DocumentHolder.txtHideRight": "Skrýt ohraničení vpravo", "SSE.Controllers.DocumentHolder.txtHideTop": "Skr<PERSON>t ohraničení nahoře", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Skrýt horní limit", "SSE.Controllers.DocumentHolder.txtHideVer": "Skr<PERSON>t s<PERSON> č<PERSON>", "SSE.Controllers.DocumentHolder.txtImportWizard": "Průvodce importem textu", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Zvětšit velikost argumentu", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Vložit argument za", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Vložit argument před", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Vložit ruční zalomení", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Vložit rovnici za", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Vložit rovnici před", "SSE.Controllers.DocumentHolder.txtItems": "Polož<PERSON>", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Ponechat pouze text", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtLessEquals": "Méně než nebo rovná se", "SSE.Controllers.DocumentHolder.txtLimitChange": "Změnit umístění limitu", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limita nad textem", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON>ita pod textem", "SSE.Controllers.DocumentHolder.txtLockSort": "Poblíž Vašeho výběru existují data, nemáte však dostatečná oprávnění k úpravě těchto buněk.<br>Chcete pokračovat s aktuálním výběrem?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Přizpůsobit závorky výšce argumentu", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Zarovnání matice", "SSE.Controllers.DocumentHolder.txtNoChoices": "Neexistují <PERSON><PERSON>é možnosti pro vyplnění buňky.<br><PERSON><PERSON><PERSON> n<PERSON> je možné vybrat pouze textové hodnoty ze sloupce.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Nezačíná na", "SSE.Controllers.DocumentHolder.txtNotContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNotEnds": "Nekončí na", "SSE.Controllers.DocumentHolder.txtNotEquals": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.DocumentHolder.txtOr": "nebo", "SSE.Controllers.DocumentHolder.txtOverbar": "Čárka nad textem", "SSE.Controllers.DocumentHolder.txtPaste": "Vložit", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Vzorec bez ohraničení", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Vzorec + ší<PERSON><PERSON> sloupce", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formátov<PERSON><PERSON> c<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Vložit pouze formátování", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Vzorec + form<PERSON>t <PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Vložit pouze vzorec", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Vzorec + všechna formátování", "SSE.Controllers.DocumentHolder.txtPasteLink": "Vložit odkaz", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Připojený obrázek", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Sloučit podmíněné <PERSON>", "SSE.Controllers.DocumentHolder.txtPastePicture": "Obrázek", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formátování zdroje", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Přemístit", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Hodnota + všechna formátování", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Hodnota + formátování č<PERSON>el", "SSE.Controllers.DocumentHolder.txtPasteValues": "Vložit pouze hodnotu", "SSE.Controllers.DocumentHolder.txtPercent": "procento", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Znovu provést automatické zvětšení tabulky", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Odstranit zlomkovou čáru", "SSE.Controllers.DocumentHolder.txtRemLimit": "Odebrat limit", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Odstranit znak akcentu", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Odstranit vodorovný pruh", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Chcete tento podpis odstranit?<br><PERSON>to krok je nevrat<PERSON>. ", "SSE.Controllers.DocumentHolder.txtRemScripts": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Odebrat dolní index", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Odebrat horní index", "SSE.Controllers.DocumentHolder.txtRowHeight": "Výška řádku", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skripty po textu", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "<PERSON><PERSON><PERSON><PERSON> před textem", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Zobrazit dolní limit", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Zobrazit uzavírací závorku", "SSE.Controllers.DocumentHolder.txtShowDegree": "Zobrazit stupeň", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Zobrazit otevírací závorku", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Zobrazit zástupný symbol", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Zobrazit horní limit", "SSE.Controllers.DocumentHolder.txtSorting": "Řazení", "SSE.Controllers.DocumentHolder.txtSortSelected": "Se<PERSON><PERSON><PERSON> v<PERSON>né", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Roztáhn<PERSON> z<PERSON>ky", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Zvolte pouze tento řádek zadaného slou<PERSON>ce", "SSE.Controllers.DocumentHolder.txtTop": "Nahoře", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Získá hodnotu celkového p<PERSON><PERSON>, nebo def<PERSON><PERSON><PERSON><PERSON> sloup<PERSON><PERSON> tabulky", "SSE.Controllers.DocumentHolder.txtUnderbar": "Čárka pod textem", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Vzít zpět automatické rozšíření tabulky", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Použít průvodce importem textu", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Kliknutí na tento odkaz může být škodlivé pro Vaše zařízení a Vaše data.<br>Jste si jistí, že chcete pok<PERSON>č<PERSON>t?", "SSE.Controllers.DocumentHolder.txtWidth": "Šířka", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Datum a čas", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Technické", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finanční", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informace", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 pos<PERSON><PERSON><PERSON> použ<PERSON>ých", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logické", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Vyhledávání a odkazování", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematika a trigonometrie", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistické", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text a data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Nepojmenovan<PERSON> sešit", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON> slou<PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "Vzorce", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON> o<PERSON><PERSON> b<PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Načítání historie verzí…", "SSE.Controllers.LeftMenu.textLookin": "Hledat v", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON>, <PERSON><PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON>. Upravte parametry vyhledávání.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Nahrazení bylo <PERSON>. {0} v<PERSON>skyt<PERSON> bylo p<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON>led<PERSON><PERSON> bylo <PERSON>. Nahrazeno výskytů: {0}", "SSE.Controllers.LeftMenu.textSearch": "Hledat", "SSE.Controllers.LeftMenu.textSheet": "List", "SSE.Controllers.LeftMenu.textValues": "Hodnoty", "SSE.Controllers.LeftMenu.textWarning": "Varování", "SSE.Controllers.LeftMenu.textWithin": "V rámci", "SSE.Controllers.LeftMenu.textWorkbook": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.txtUntitled": "Bez názvu", "SSE.Controllers.LeftMenu.warnDownloadAs": "Pokud budete pokračovat v ukládání v tomto formátu, vše kromě textu bude ztraceno.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Main.confirmMoveCellRange": "Rozsah cílových buněk může obsahovat data. Pokračovat v operaci?", "SSE.Controllers.Main.confirmPutMergeRange": "Zdrojová data obsahovala sloučené buňky.<br><PERSON><PERSON> roz<PERSON>ny již před tím, než byly vloženy do tabulky.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Vzorce v záhlaví budou odstraněny a převedeny na statický text.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Main.convertationTimeoutText": "Překročen časový limit pro provedení převodu.", "SSE.Controllers.Main.criticalErrorExtText": "Pro návrat na seznam dokumentů klikněte na „OK“.", "SSE.Controllers.Main.criticalErrorTitle": "Chyba", "SSE.Controllers.Main.downloadErrorText": "Stahování se nezdařilo.", "SSE.Controllers.Main.downloadTextText": "Stahování sešitu…", "SSE.Controllers.Main.downloadTitleText": "Stahování sešitu", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnoty nebyly na<PERSON>.", "SSE.Controllers.Main.errorAccessDeny": "Pokoušíte se provést a<PERSON>, na kterou nemáte oprávnění.<br>Obraťte se na správce vámi využívaného dokumentového serveru.", "SSE.Controllers.Main.errorArgsRange": "Chyba v zadaném vzorci.<br>Použit nesprávný rozsah argumentu.", "SSE.Controllers.Main.errorAutoFilterChange": "Operace není p<PERSON>, protože se pokouší posunout buňky v tabulce na listu.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operaci nelze provést pro vybrané <PERSON>, proto<PERSON><PERSON> nelze přesunout část tabulky.<br>Vyberte jinou oblast dat tak, aby byla celá tabulka byla posunuta a zkuste to znovu.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operaci nelze pro zvolený rozsah buněk provést.<br>Vyberte jednotnou oblast dat odlišnou od už existující a zkuste to znovu.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operaci nelze <PERSON>, protože oblast obsahuje filtrované buňky.<br>Odkryjte filtrované prvky a zkuste to znovu.", "SSE.Controllers.Main.errorBadImageUrl": "URL adresa obrázku není správně", "SSE.Controllers.Main.errorCannotUngroup": "Seskupení není mož<PERSON>é z<PERSON>. Pro zahájení o<PERSON>, vyberte podrobnost řádek nebo sloupců a seskupte je.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Tento příkaz nelze použít na zabezpečený list. Pro použití příkazu, zrušte zabezpečení listu.<br><PERSON><PERSON><PERSON><PERSON> bý<PERSON> vyžadováno zadání hesla.", "SSE.Controllers.Main.errorChangeArray": "Nemů<PERSON><PERSON> mě<PERSON>t <PERSON> pole.", "SSE.Controllers.Main.errorChangeFilteredRange": "Dojde ke změně rozsahu filtrů v listě.<br>Pro provedení je nutné vypnout automatické filtry.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Buňka nebo graf, který se pokoušíte změnit je na zabezpečeném listu. Pro provedení změny, vypněte zabezpečení listu. Může být vyžadováno zadání hesla.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Spojení se serverem ztraceno. Dokument v tuto chvíli nelze upravovat.", "SSE.Controllers.Main.errorConnectToServer": "Dokument se nedaří uložit. Zkontrolujte nastavení vašeho připojení nebo se obraťte na svého správce.<br> <PERSON><PERSON><PERSON>net<PERSON> na „OK“ budete vyzváni k tomu, abyste si dokument stáhli.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Tento příkaz nelze použít s více výběry.<br><PERSON><PERSON><PERSON><PERSON> jeden z rozsahů a zkuste to znovu.", "SSE.Controllers.Main.errorCountArg": "Chyba v zadaném vzorci.<br>Použit nesprávný počet argumentů.", "SSE.Controllers.Main.errorCountArgExceed": "Chyba v zadaném vzorci.<br>Překročen počet argumentů.", "SSE.Controllers.Main.errorCreateDefName": "Stávající pojmenované rozsahy nelze upravovat a nové nyní nelze vytvořit<br>proto<PERSON><PERSON> některé z nich jsou právě upravovány.", "SSE.Controllers.Main.errorDatabaseConnection": "Externí chyba.<br>Chyba spojení s databází. Pokud chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> – bez hesla je není možné zobrazit.", "SSE.Controllers.Main.errorDataRange": "Nesprávný datový rozsah.", "SSE.Controllers.Main.errorDataValidate": "Zadaná hodnota není platná.<br><PERSON><PERSON><PERSON><PERSON> má omezeny hodnoty, které je možné zadat do této buňky.", "SSE.Controllers.Main.errorDefaultMessage": "<PERSON><PERSON><PERSON>: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Pokoušíte se smazat sloupec, který obsahuje uzamčenou buňku. Uzamčené buňky nemohou být smazány dokud list je zabezpečen.<br>Pro odstranění uzamčené buňky, vypněte zabezpečení listu. Může být vyžadováno zadání hesla.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Pokoušíte se s<PERSON><PERSON><PERSON>, který obsahuje uzamčené buňky. Uzamčené buňky nemohou být smazány dokud je list zabezpečen.<br>Pro odstranění uzamčené buňky, vypněte zabezpečení listu. Může být vyžadováno zadání hesla.", "SSE.Controllers.Main.errorEditingDownloadas": "Při práci s dokumentem došlo k chybě.<br>Použijte volbu „Stáhnout jako“ a uložte si do souboru jako záložní kopii na svůj počítač.", "SSE.Controllers.Main.errorEditingSaveas": "Při práci s dokumentem došlo k chybě.<br>Použijte volbu „Uložit jako...“ a uložte si do souboru jako záložní kopii na svůj počítač.", "SSE.Controllers.Main.errorEditView": "Během úpravy některého se zobrazení se<PERSON>, není možné upravovat existující zobrazení sešitů a vytvářet nové.", "SSE.Controllers.Main.errorEmailClient": "Nenalezen žádný e-mailový klient.", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON>or je z<PERSON>en hesle<PERSON>, bez kterého ho nelze otevřít.", "SSE.Controllers.Main.errorFileRequest": "Externí chyba.<br>Chyba požadavku na soubor. Pokud chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorFileSizeExceed": "Velikost souboru překračuje omezení nastavená na serveru, který využíváte.<br>Ohledně podrobností se obraťte na správce dokumentového serveru.", "SSE.Controllers.Main.errorFileVKey": "Externí chyba.<br>Nesprávný klíč <PERSON>zpečení. V případě, že chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorFillRange": "Nelze vyplnit vybranou oblast buněk.<br>Všechny sloučené buňky musí být stejně velké.", "SSE.Controllers.Main.errorForceSave": "Došlo k chybě při ukládání souboru. Použijte volbu „Stáhnout jako“ a uložte si do souboru na svůj počítač nebo to zkuste později znovu.", "SSE.Controllers.Main.errorFormulaName": "Chyba v zadaném vzorci.<br>Použit nesprávný název vzorce.", "SSE.Controllers.Main.errorFormulaParsing": "Interní chyba při analýze vzorce.", "SSE.Controllers.Main.errorFrmlMaxLength": "Délka vzorce je delší než limit 8192 znaků.<br>Prosím upravte vzorec a akci opakujte.", "SSE.Controllers.Main.errorFrmlMaxReference": "Takový vzorec není m<PERSON>, proto<PERSON>e obsahuje pří<PERSON>š mnoho hodnot,<br> od<PERSON><PERSON><PERSON> na tabulky nebo jmen.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "<PERSON><PERSON><PERSON><PERSON> textové hodnoty ve vzorcích je omezena na 255 znaků.<br>Po<PERSON><PERSON><PERSON>jte funkci SPOJIT nebo spojovací operátor (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funkce odkazuje na list, který neexistuje.<br>Zkontrolujte data a zkuste to znovu.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON> rozsah tak, aby první řádek tabulky byl na stejném řádku<br>a výsledná tabulka překrývala tu stávající.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> neobsahuje jin<PERSON>.", "SSE.Controllers.Main.errorInvalidRef": "Zadejte správný název pro výběr nebo platnou referenci.", "SSE.Controllers.Main.errorKeyEncrypt": "Neznámý popisovač klíče", "SSE.Controllers.Main.errorKeyExpire": "Platnost popisovače klíče skončila", "SSE.Controllers.Main.errorLabledColumnsPivot": "Pro vytvoření kontingenční ta<PERSON>, použijte data uspořádaná jako seznam s označenými sloupci.", "SSE.Controllers.Main.errorLoadingFont": "Písma nejsou načtená.<br>Obraťte se na správce vámi využívaného dokumentového serveru.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Odkaz na umístnění nebo rozsah dat je neplatný. ", "SSE.Controllers.Main.errorLockedAll": "Operace nemůže být <PERSON>, protože list byl uzamčen jiným uživatelem.", "SSE.Controllers.Main.errorLockedCellPivot": "Nemůžete měnit data uvnitř kontingenční tabulky.", "SSE.Controllers.Main.errorLockedWorksheetRename": "V tuto chvíli list nelze př<PERSON>, proto<PERSON>e je přejmenováván jiným uživatelem", "SSE.Controllers.Main.errorMaxPoints": "Nejvyšší možný počet bodů v sérii na graf je 4096.", "SSE.Controllers.Main.errorMoveRange": "Nelze změnit část sloučené buňky", "SSE.Controllers.Main.errorMoveSlicerError": "Průřezy tabulky nemůžou být zkopírovány z jednoho sešitu do druhého.<br>Akci opakujte vybráním celé tabulky včetně průřezů.", "SSE.Controllers.Main.errorMultiCellFormula": "V tabulkách nejsou dovoleny vzorce pro pole s vícero buňkami.", "SSE.Controllers.Main.errorNoDataToParse": "Nebyla vybrána žádná data pro zpracování.", "SSE.Controllers.Main.errorOpenWarning": "Délka jednoho ze vzorců v souboru překročila<br>povolený počet znaků, proto byl vzorec odstraněn.", "SSE.Controllers.Main.errorOperandExpected": "Form<PERSON> (syntaxe) funkce není správná. Zkontrolujte zda nechybí jedna ze závorek – „(“ nebo „)“.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "<PERSON><PERSON><PERSON><PERSON>.<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že máte vypnutý CAPS LOCK a správnou velikosti písmen.", "SSE.Controllers.Main.errorPasteMaxRange": "Kopírovaná oblast a oblast pro vložení se neshodují.<br>Vyberte oblast se stejnou velikosti nebo klikněte na první buňku v řádku a vložte zkopírované buňky.", "SSE.Controllers.Main.errorPasteMultiSelect": "Tuto akci nelze provést v případě výběru více rozsahů.<br><PERSON><PERSON><PERSON><PERSON> jeden rozsah a akci opakujte.", "SSE.Controllers.Main.errorPasteSlicerError": "Průřezy tabulky nemůžou být zkopírovány z jednoho sešitu do druhého.", "SSE.Controllers.Main.errorPivotGroup": "Vybrané objekty nelze sloučit.", "SSE.Controllers.Main.errorPivotOverlap": "Výstup z kontingenční tabulky nesmí překrývat tabulku.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Výkaz z kontingenční tabulky byl uložen bez podkladových dat.<br>Pro aktualizaci klikněte na tlačítko 'Obnovit'.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Je ná<PERSON> líto, ale ve stávajícíí verzi programu není možné vytisknout více jak 1500 stránek najedn<PERSON>.<br><PERSON><PERSON> omezení bude nadcházejících vydáních odstraněno.", "SSE.Controllers.Main.errorProcessSaveResult": "Ukládání se nezdařilo", "SSE.Controllers.Main.errorServerVersion": "Verze editoru byla aktualizována. Stránka bude znovu načtena, aby se změny uplatnily.", "SSE.Controllers.Main.errorSessionAbsolute": "Platnost relace upravování dokumentu skončila. Načtete stránku znovu.", "SSE.Controllers.Main.errorSessionIdle": "Po dost dlouhou dobu jste s otevřeným dokumentem nepracovali. Načtete stránku znovu.", "SSE.Controllers.Main.errorSessionToken": "Spojení se <PERSON>em bylo <PERSON>. Načtěte stránku znovu.", "SSE.Controllers.Main.errorSetPassword": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Umístnění odkazu je chybné, proto<PERSON>e buňky nejsou na stejném řádku nebo sloupci.<br>Vyberte buňky na stejném řádku nebo sloupci.", "SSE.Controllers.Main.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Controllers.Main.errorToken": "Token zabezpečení dokumentu nemá správný formát.<br> Obraťte se na Vašeho správce dokumentového serveru.", "SSE.Controllers.Main.errorTokenExpire": "Platnost tokenu zabezpečení dokumentu skončila.<br>Obraťte se na správce vámi využívaného dokumentového serveru.", "SSE.Controllers.Main.errorUnexpectedGuid": "Externí chyba.<br>Neočekávané GUID. V případě, že chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorUpdateVersion": "Verze souboru byla změněna. Stránka bude znovu načtena.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Připojení k Internetu bylo obnoveno a verze souboru byla změněna.<br><PERSON><PERSON>ž budete moci pokračovat v práci, bude třeba si soubor stáhnout nebo si zkopírovat jeho obsah, abyste si zaj<PERSON>, že se nic neztratí a až poté tuto stránku znovu načtěte.", "SSE.Controllers.Main.errorUserDrop": "Tento soubor v tuto chvíli není přístupný.", "SSE.Controllers.Main.errorUsersExceed": "Počet uživatelů pro daný tarif byl překročen", "SSE.Controllers.Main.errorViewerDisconnect": "Spojení bylo z<PERSON>. Dokument zůstává zobrazen,<br>ale do obnovení spojení (a znovunačtení stránky) ho není možné si stáhnout a ani vytisknout.", "SSE.Controllers.Main.errorWrongBracketsCount": "Chyba v zadaném vzorci.<br>Použit nesprávný počet závorek.", "SSE.Controllers.Main.errorWrongOperator": "Chyba v zadaném vzorci.Použit nesprávný operátor.<br>Prosím, opravte chybu.", "SSE.Controllers.Main.errorWrongPassword": "<PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errRemDuplicates": "<PERSON><PERSON> na<PERSON> a smazány duplicitní hodnoty: {0}, zbývá neopakujících se hodnot: {1}.", "SSE.Controllers.Main.leavePageText": "V tomto sešitu máte neuložené změny. Pokud o ně nechcete přijít, klikněte na „Zůstat na této stránce“, poté na „Uložit“. V opačném případě klikněte na „Opustit tuto stránku“ a všechny neuložené změny budou zahozeny.", "SSE.Controllers.Main.leavePageTextOnClose": "Veškeré neuložené změny v tomto sešitu budou ztraceny.<br> Pokud je chcete uložit, klikněte na „Storno“ a poté na „Uložit“. Pokud chcete veškeré neuložené změny zahodit, klikněte na „OK“.", "SSE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat…", "SSE.Controllers.Main.loadFontsTitleText": "Nač<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Controllers.Main.loadFontTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat…", "SSE.Controllers.Main.loadFontTitleText": "Nač<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Controllers.Main.loadImagesTextText": "Načítání obr<PERSON>…", "SSE.Controllers.Main.loadImagesTitleText": "Načítání obr<PERSON>ů", "SSE.Controllers.Main.loadImageTextText": "Načítání obrázku…", "SSE.Controllers.Main.loadImageTitleText": "Načítání obrázku", "SSE.Controllers.Main.loadingDocumentTitleText": "Načítán<PERSON> se<PERSON>itu", "SSE.Controllers.Main.notcriticalErrorTitle": "Varování", "SSE.Controllers.Main.openErrorText": "Při otevírání souboru došlo k chybě.", "SSE.Controllers.Main.openTextText": "Otev<PERSON><PERSON><PERSON><PERSON> se<PERSON>…", "SSE.Controllers.Main.openTitleText": "Oteví<PERSON><PERSON><PERSON> se<PERSON>", "SSE.Controllers.Main.pastInMergeAreaError": "Nelze změnit část sloučené buňky", "SSE.Controllers.Main.printTextText": "Tisk sešitu…", "SSE.Controllers.Main.printTitleText": "Tisk sešitu", "SSE.Controllers.Main.reloadButtonText": "Načíst stránku znovu", "SSE.Controllers.Main.requestEditFailedMessageText": "Tento dokument měkdo právě upravuje. Prosím zkuste to znovu později.", "SSE.Controllers.Main.requestEditFailedTitleText": "Přístup odepřen", "SSE.Controllers.Main.saveErrorText": "Při ukládání souboru došlo k chybě.", "SSE.Controllers.Main.saveErrorTextDesktop": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON> být uložen nebo vytvořen.<br><PERSON><PERSON><PERSON><PERSON>: <br>1. <PERSON><PERSON><PERSON> je pouze pro čtení. <br>2. <PERSON><PERSON>or je editován jinými u<PERSON>. <br>3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> je plně zaplněno nebo poš<PERSON>o.", "SSE.Controllers.Main.saveTextText": "Uklá<PERSON><PERSON><PERSON> se<PERSON>itu…", "SSE.Controllers.Main.saveTitleText": "Ukládán<PERSON> sešitu", "SSE.Controllers.Main.scriptLoadError": "Připojení je p<PERSON><PERSON>, některé součás<PERSON> se nepodařilo na<PERSON>. Načtěte stránku znovu.", "SSE.Controllers.Main.textAnonymous": "Anonymní", "SSE.Controllers.Main.textApplyAll": "Uplatnit na všechny rovnice", "SSE.Controllers.Main.textBuyNow": "Navštívit webovou stránku", "SSE.Controllers.Main.textChangesSaved": "Všechny změny uloženy", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Tip zavřete kliknutím", "SSE.Controllers.Main.textConfirm": "Potvrzení", "SSE.Controllers.Main.textContactUs": "Obraťte se na obchodní oddělení", "SSE.Controllers.Main.textConvertEquation": "Tato rovnice byla vytvořena starou verzí editoru rovnic, k<PERSON><PERSON> už není podporovaná. Pro jej<PERSON> upraven<PERSON>, převeďte rovnici do formátu Office Math ML.<br>Převést nyní?", "SSE.Controllers.Main.textCustomLoader": "Mějte na paměti, že dle podmínek licence nejste oprávněni měnit zavaděč.<br>Pro získání nabídky se obraťte na naše obchodní oddělení.", "SSE.Controllers.Main.textDisconnect": "Spojení je ztraceno", "SSE.Controllers.Main.textFillOtherRows": "Vyplnit ostatní <PERSON>", "SSE.Controllers.Main.textFormulaFilledAllRows": "Vzorec zadaný v {0} řádcích obsahuje data. Doplnění dat do ostatních řád<PERSON>ů, může trvat několik minut.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Vzorec je zadán zadaný prvních {0} řádcích. Doplnění dat do ostatních <PERSON>, může trvat několik minut.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Vzorec je zadaný pouze v prvních {0} ř<PERSON>dcích, z důvodu ukládání do paměti. Dalších {1} řádků v tomto listu neobsahuje data. Data můžete zapsat manuálně.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Vzorec je zadaný pouze v prvních {0} řádcích, z důvodu ukládání do paměti. Ostatní řádky v tomto listu neobsahují data.", "SSE.Controllers.Main.textGuest": "Návštěvník", "SSE.Controllers.Main.textHasMacros": "<PERSON><PERSON><PERSON> obsahu<PERSON> makra.<br>Opravdu chcete makra spustit?", "SSE.Controllers.Main.textLearnMore": "Více informací", "SSE.Controllers.Main.textLoadingDocument": "Načítán<PERSON> se<PERSON>itu", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> má méně než 128 znaků.", "SSE.Controllers.Main.textNeedSynchronize": "Jsou k dispozici aktualizace", "SSE.Controllers.Main.textNo": "Ne", "SSE.Controllers.Main.textNoLicenseTitle": "Došlo k dosažení limitu licence", "SSE.Controllers.Main.textPaidFeature": "Placená funkce", "SSE.Controllers.Main.textPleaseWait": "Operace může trvat déle, než se předpokládalo. Prosím čekejte…", "SSE.Controllers.Main.textReconnect": "Spojení je obnovené", "SSE.Controllers.Main.textRemember": "Zapamatovat si mou volbu pro všechny soubory", "SSE.Controllers.Main.textRememberMacros": "Zapamatovat moji volbu pro všechna makra", "SSE.Controllers.Main.textRenameError": "Jméno uživatele nesmí být prázdné. ", "SSE.Controllers.Main.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> bude použ<PERSON> pro spolupráci", "SSE.Controllers.Main.textRequestMacros": "Makro vytváří požadavek na URL. Chcete povolit požadavek k přístupu na %1?", "SSE.Controllers.Main.textShape": "O<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textStrict": "Statický režim", "SSE.Controllers.Main.textText": "Text", "SSE.Controllers.Main.textTryUndoRedo": "Funkce zpět/znovu nejsou v režimu rychlé spolupráce na úpravách k dispozici.<br>Kliknutím na tlačítko „Striktní režim“ přejdete do striktního režimu spolupráce na úpravách, ve kterém soubor upravujte bez vyrušování ostatními uživateli a vámi provedené změny odesíláte pouze po jejich uložení. Mezi oběma režimy spolupráce na úpravách je možné přepínat v pokročilých nastaveních editoru.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Funkce Zpět/Znovu jsou vypnuty pro rychlý rež<PERSON> s<PERSON>.", "SSE.Controllers.Main.textYes": "<PERSON><PERSON>", "SSE.Controllers.Main.titleLicenseExp": "Platnost licence skončila", "SSE.Controllers.Main.titleServerVersion": "Editor by<PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAccent": "Zvýraznění", "SSE.Controllers.Main.txtAll": "(vše)", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON> nap<PERSON> text", "SSE.Controllers.Main.txtBasicShapes": "Základní obrazce", "SSE.Controllers.Main.txtBlank": "(p<PERSON><PERSON><PERSON><PERSON>ý)", "SSE.Controllers.Main.txtButtons": "Tlačítka", "SSE.Controllers.Main.txtByField": "\n%1 z %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Vyčistit filtr", "SSE.Controllers.Main.txtColLbls": "Štítky buňky", "SSE.Controllers.Main.txtColumn": "Sloupec", "SSE.Controllers.Main.txtConfidential": "Důvěrné", "SSE.Controllers.Main.txtDate": "Datum", "SSE.Controllers.Main.txtDays": "Dny", "SSE.Controllers.Main.txtDiagramTitle": "Nad<PERSON> grafu", "SSE.Controllers.Main.txtEditingMode": "Na<PERSON><PERSON><PERSON>…", "SSE.Controllers.Main.txtErrorLoadHistory": "Načítání historie se nezdařilo", "SSE.Controllers.Main.txtFiguredArrows": "Orientač<PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Celkový součet", "SSE.Controllers.Main.txtGroup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematika", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Vícenásobný výběr", "SSE.Controllers.Main.txtOr": "%1 nebo %2", "SSE.Controllers.Main.txtPage": "Strán<PERSON>", "SSE.Controllers.Main.txtPageOf": "Stránka %1 z %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "Připravil(a)", "SSE.Controllers.Main.txtPrintArea": "Oblast_tisku", "SSE.Controllers.Main.txtQuarter": "Čtvrtiny", "SSE.Controllers.Main.txtQuarters": "Čtvrtiny", "SSE.Controllers.Main.txtRectangles": "Obdélníky", "SSE.Controllers.Main.txtRow": "Řádek", "SSE.Controllers.Main.txtRowLbls": "Štíky řádku", "SSE.Controllers.Main.txtSeconds": "Sekundy", "SSE.Controllers.Main.txtSeries": "Řady", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Bublina s čárou 1 (se zdůrazňujícím pruhem a orámováním)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Bublina s čárou 2 (se zdůrazňujícím pruhem a orámováním)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Bublina s čárou 3 (se zdůrazňujícím pruhem a orámováním)", "SSE.Controllers.Main.txtShape_accentCallout1": "Bublina s čárou 1 (se zdůrazňujícím pruhem)", "SSE.Controllers.Main.txtShape_accentCallout2": "Bublina s čárou 2 (se zdůrazňujícím pruhem)", "SSE.Controllers.Main.txtShape_accentCallout3": "Bublina s čárou 3 (se zdůrazňujícím pruhem)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tlačí<PERSON><PERSON>t nebo Předchozí", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Tlačítko začátku", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Tlačítko dokumentu", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Tlačítko konec", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "T<PERSON>č<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Tlačítko informace", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Tlačítko videa", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Tlačítko <PERSON>v<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "Tlačítko zvuku", "SSE.Controllers.Main.txtShape_arc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "<PERSON><PERSON><PERSON> propo<PERSON>a se <PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "<PERSON><PERSON><PERSON> propojka se dv<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentUpArrow": "Šipka ohnut<PERSON> nahoru", "SSE.Controllers.Main.txtShape_bevel": "Zkosení", "SSE.Controllers.Main.txtShape_blockArc": "Část kruhu", "SSE.Controllers.Main.txtShape_borderCallout1": "Bublina s čárou 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Bublina s čárou 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Bublina s čárou 3", "SSE.Controllers.Main.txtShape_bracePair": "D<PERSON><PERSON><PERSON> z<PERSON>vorka", "SSE.Controllers.Main.txtShape_callout1": "Bublina s čárou 1 (bez orámování)", "SSE.Controllers.Main.txtShape_callout2": "Bublina s čárou 2 (<PERSON>z orámování)", "SSE.Controllers.Main.txtShape_callout3": "Bublina s čárou 3 (bez orámování)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chord": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloud": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloudCallout": "Bublina-mrak", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Zakřivená propojka", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Zakřivená propojka se š<PERSON>u", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Zakřivená propojka se dv<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Zakřivená šipka dolů", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Zakřivená šipka vlevo", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Zakřivená šipka vpravo", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Zakřivená šipka nahoru", "SSE.Controllers.Main.txtShape_decagon": "Desetiúhelník", "SSE.Controllers.Main.txtShape_diagStripe": "Proužek po úhlopříčce", "SSE.Controllers.Main.txtShape_diamond": "Kosodélník", "SSE.Controllers.Main.txtShape_dodecagon": "Dvanáctiúhelník", "SSE.Controllers.Main.txtShape_donut": "Kruh", "SSE.Controllers.Main.txtShape_doubleWave": "Dvojitá vlnovka", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrowCallout": "<PERSON><PERSON><PERSON> se <PERSON> do<PERSON>", "SSE.Controllers.Main.txtShape_ellipse": "Elipsa", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Zakřivený proužek dolů", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Pro<PERSON>žek zakřivený nahoru", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagram: alternativní proces", "SSE.Controllers.Main.txtShape_flowChartCollate": "Diagram: kompletovat", "SSE.Controllers.Main.txtShape_flowChartConnector": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Diagram: roz<PERSON>dnutí", "SSE.Controllers.Main.txtShape_flowChartDelay": "Diagram: pro<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Diagram: o<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Diagram: dokument", "SSE.Controllers.Main.txtShape_flowChartExtract": "Diagram: r<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Diagram: data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagram: magnetický disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagram: <PERSON><PERSON><PERSON><PERSON>št<PERSON> s přímým přístupem", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagram: <PERSON><PERSON><PERSON><PERSON>št<PERSON> se sekvenčním přístupem", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Diagram: ru<PERSON><PERSON><PERSON> vstup", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Diagram: ruční operace", "SSE.Controllers.Main.txtShape_flowChartMerge": "Diagram: s<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Diagram: v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagram: prop<PERSON><PERSON>a mimo str<PERSON>", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagram: uložená data", "SSE.Controllers.Main.txtShape_flowChartOr": "Diagram: nebo", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> proces", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartProcess": "Diagram: proces", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagram: karta", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagram: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Diagram: řazení", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagram: křižovatka sčítání", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Diagram: ukončení", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON><PERSON> roh", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Sed<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Pě<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_irregularSeal1": "Výbuch 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Výbuch 2", "SSE.Controllers.Main.txtShape_leftArrow": "Šipka vlevo", "SSE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON> se <PERSON> vlevo", "SSE.Controllers.Main.txtShape_leftBrace": "Složená závorka vlevo", "SSE.Controllers.Main.txtShape_leftBracket": "Závorka vlevo", "SSE.Controllers.Main.txtShape_leftRightArrow": "Šipka vlevo a vpravo", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "<PERSON><PERSON><PERSON> se šipkou vpravo a vlevo", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Šip<PERSON> vlevo, vpravo a nahoru", "SSE.Controllers.Main.txtShape_leftUpArrow": "Šipka vlevo a nahoru", "SSE.Controllers.Main.txtShape_lightningBolt": "Blesk", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Šipka", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "Rozdělení", "SSE.Controllers.Main.txtShape_mathEqual": "Rovná se", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Vícenásobné", "SSE.Controllers.Main.txtShape_mathNotEqual": "Nerov<PERSON> se", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Symbol „Ne“", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Šipka vpravo se zářezem", "SSE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_parallelogram": "Rovnoběžník", "SSE.Controllers.Main.txtShape_pentagon": "Pě<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_pie": "Kruhová výseč", "SSE.Controllers.Main.txtShape_plaque": "Podpis", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON> ruky", "SSE.Controllers.Main.txtShape_polyline2": "Volná forma", "SSE.Controllers.Main.txtShape_quadArrow": "Šipka do čtyř stran", "SSE.Controllers.Main.txtShape_quadArrowCallout": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rect": "Obdélník", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon2": "<PERSON><PERSON><PERSON><PERSON>hor<PERSON>", "SSE.Controllers.Main.txtShape_rightArrow": "Šipka vpravo", "SSE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON><PERSON> se šip<PERSON>u vpravo", "SSE.Controllers.Main.txtShape_rightBrace": "Složená závorka vpravo", "SSE.Controllers.Main.txtShape_rightBracket": "Závorka vpravo", "SSE.Controllers.Main.txtShape_round1Rect": "Obdélník s jedn<PERSON><PERSON> zaobleným rohem", "SSE.Controllers.Main.txtShape_round2DiagRect": "Obdélník se dvě<PERSON> protilehlými zaoblenými rohy", "SSE.Controllers.Main.txtShape_round2SameRect": "Obdélník se dvěma zaoblenými rohy na stejné straně", "SSE.Controllers.Main.txtShape_roundRect": "Obdélník se zaoblenými rohy", "SSE.Controllers.Main.txtShape_rtTriangle": "Pravoúhlý trojúhelník", "SSE.Controllers.Main.txtShape_smileyFace": "Usměvavý obličej", "SSE.Controllers.Main.txtShape_snip1Rect": "Obdélník s jedním ustřiženým rohem", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Obdélník se dvěma protilehlými ustřiženými rohy", "SSE.Controllers.Main.txtShape_snip2SameRect": "Obdélník se dvěma ustřiženými rohy na stejné straně", "SSE.Controllers.Main.txtShape_snipRoundRect": "Obdélník s jedním zaobleným a jedním ustřiženým rohem na stejné straně", "SSE.Controllers.Main.txtShape_spline": "Křivka", "SSE.Controllers.Main.txtShape_star10": "Hvězda s 10 paprsky", "SSE.Controllers.Main.txtShape_star12": "Hvězda s 12 paprsky", "SSE.Controllers.Main.txtShape_star16": "Hvězda s 16 paprsky", "SSE.Controllers.Main.txtShape_star24": "Hvězda se 24 paprsky", "SSE.Controllers.Main.txtShape_star32": "Hvězda se 32 paprsky", "SSE.Controllers.Main.txtShape_star4": "Hvězda se 4 paprsky", "SSE.Controllers.Main.txtShape_star5": "Hvězda s 5 paprsky", "SSE.Controllers.Main.txtShape_star6": "Hvězda se 6 paprsky", "SSE.Controllers.Main.txtShape_star7": "Hvězda se 7 paprsky", "SSE.Controllers.Main.txtShape_star8": "Hvězda s 8 paprsky", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Proužkovaná šipka vpravo", "SSE.Controllers.Main.txtShape_sun": "Slunce", "SSE.Controllers.Main.txtShape_teardrop": "Slza", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON><PERSON> pole", "SSE.Controllers.Main.txtShape_trapezoid": "Lichoběžník", "SSE.Controllers.Main.txtShape_triangle": "Trojúhelník", "SSE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON> se <PERSON> na<PERSON>u", "SSE.Controllers.Main.txtShape_upDownArrow": "Šipka nahoru a dolů", "SSE.Controllers.Main.txtShape_uturnArrow": "Šipka s otočkou", "SSE.Controllers.Main.txtShape_verticalScroll": "Svislé posouvání", "SSE.Controllers.Main.txtShape_wave": "Vlnka", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON>v<PERSON><PERSON><PERSON> bublina", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bublina", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON><PERSON> ve tvaru obdélníku se zaoblenými rohy", "SSE.Controllers.Main.txtStarsRibbons": "Hvě<PERSON><PERSON> a stuhy", "SSE.Controllers.Main.txtStyle_Bad": "Chybné", "SSE.Controllers.Main.txtStyle_Calculation": "Výpočet", "SSE.Controllers.Main.txtStyle_Check_Cell": "Zkontrolovat buňku", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Měna", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Vysvětlující text", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Nadpis 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Nadpis 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Nadpis 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Nadpis 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Spojená buňka", "SSE.Controllers.Main.txtStyle_Neutral": "Neutrální", "SSE.Controllers.Main.txtStyle_Normal": "Normální", "SSE.Controllers.Main.txtStyle_Note": "Poznámka", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Procento", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Warning_Text": "Varovný text", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "Tabulka", "SSE.Controllers.Main.txtTime": "Čas", "SSE.Controllers.Main.txtUnlock": "Odemknout", "SSE.Controllers.Main.txtUnlockRange": "Odemknout rozsah", "SSE.Controllers.Main.txtUnlockRangeDescription": "Pokud chcete tento rozsah změnit, zadej<PERSON> he<PERSON>:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Oblast, kterou se pokoušíte upravit je zabezpečena heslem.", "SSE.Controllers.Main.txtValues": "Hodnoty", "SSE.Controllers.Main.txtXAxis": "Osa X", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "SSE.Controllers.Main.txtYears": "Roky", "SSE.Controllers.Main.unknownErrorText": "Neznámá chyba.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON>ý webový prohlížeč není podporován.", "SSE.Controllers.Main.uploadDocExtMessage": "Neznámý formát dokumentu.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nebyly nahrány dokumenty.", "SSE.Controllers.Main.uploadDocSizeMessage": "Překročena maximální velikost dokumentu.", "SSE.Controllers.Main.uploadImageExtMessage": "Neznámý formát obrázku.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Nenahr<PERSON><PERSON> o<PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "Obrázek je p<PERSON><PERSON><PERSON><PERSON> velk<PERSON>. Maximální velikost je 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Nahrávání obrázku…", "SSE.Controllers.Main.uploadImageTitleText": "Nahrávání obrázku", "SSE.Controllers.Main.waitText": "Čekejte prosím…", "SSE.Controllers.Main.warnBrowserIE9": "Aplikace nemůže v IE9 fungovat správně. Použijte IE10 a novější", "SSE.Controllers.Main.warnBrowserZoom": "Stávající nastavení velikosti zobrazení vámi využívaného prohlížeče není zcela podporováno. Stisknutím CTRL+0 vraťte velikost zobrazení na výchozí hodnotu.", "SSE.Controllers.Main.warnLicenseExceeded": "Došlo k dosažení limitu počtu souběžných spojení %1 editor<PERSON><PERSON> Dokument bude otevřen pouze pro náhled.<br>Pro více podrobností kontaktujte svého správce.", "SSE.Controllers.Main.warnLicenseExp": "Platnost vaší licence skončila.<br>Obnovte si svou licenci a načtěte stránku znovu.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Platnost vaší licence skončila.<br>Nemáte přístup k upravování dokumentů.<br>Obraťte se na svého správce.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Vaši licenci je nutné obnovit.<br>Přístup k možnostem editace dokumentu je omezen.<br>Pro získání plného přístupu prosím kontaktujte svého administrátora.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Došlo dosažení limitu %1 editorů v režimu spolupráce na úpravách. Ohledně podrobností se obraťte na svého správce.", "SSE.Controllers.Main.warnNoLicense": "<PERSON><PERSON><PERSON>žení limitu souběžných připojení %1 editor<PERSON>. Dokument bude otevřen pouze pro náhled.<br>Pro rozšíření funkcí kontaktujte %1 obchodní oddělení.", "SSE.Controllers.Main.warnNoLicenseUsers": "Došlo k dosažení limitu %1 editor<PERSON>. Pro rozšíření funkcí kontaktujte %1 obchodní oddělení.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON><PERSON> vám odepřeno oprávnění soubor upravovat.", "SSE.Controllers.Print.strAllSheets": "Všechny listy", "SSE.Controllers.Print.textFirstCol": "První sloupec", "SSE.Controllers.Print.textFirstRow": "Prvn<PERSON>", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Controllers.Print.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textRepeat": "Opakovat...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textWarning": "Varování", "SSE.Controllers.Print.txtCustom": "Uživatelsky určené", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON><PERSON> ne<PERSON> správn<PERSON>", "SSE.Controllers.Search.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Controllers.Search.textNoTextFound": "<PERSON>, <PERSON><PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON>. Upravte parametry vyhledávání.", "SSE.Controllers.Search.textReplaceSkipped": "Nahrazení bylo <PERSON>. {0} v<PERSON>skyt<PERSON> bylo p<PERSON>.", "SSE.Controllers.Search.textReplaceSuccess": "Hledání bylo <PERSON>eno. <PERSON><PERSON><PERSON> {0} nahrazení", "SSE.Controllers.Statusbar.errorLastSheet": "Sešit musí mít alespoň jeden viditelný list", "SSE.Controllers.Statusbar.errorRemoveSheet": "List se neda<PERSON><PERSON> s<PERSON>t.", "SSE.Controllers.Statusbar.strSheet": "List", "SSE.Controllers.Statusbar.textDisconnect": "<b>Spojení je z<PERSON></b><br>Pokus o opětovné připojení. Zkontrolujte nastavení připojení.", "SSE.Controllers.Statusbar.textSheetViewTip": "Jste v režimu náhledu listu. Filtry a řazení je viditelné pouze pro Vás a uživatele v tomto náhledu. ", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Jste v režimu náhledu listu. Filtry jsou viditelné pouze pro Vás a uživatele v tomto náhledu. ", "SSE.Controllers.Statusbar.warnDeleteSheet": "List může obsahovat data. Opravdu chcete pokračovat?", "SSE.Controllers.Statusbar.zoomText": "Přiblížení {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Písmo (font) ve kterém se chyst<PERSON>te ulož<PERSON>, není na tomto zařízení k dispozici.<br>Text bude zobrazen pomocí některého ze systémových písem s tím, že uložené písmo bude použito, k<PERSON><PERSON> bude dostupné.<br>Chcete pokračovat?", "SSE.Controllers.Toolbar.errorComboSeries": "Pro vytvoření kombino<PERSON>ho grafu, zvolte alespoň dvě skupiny dat. ", "SSE.Controllers.Toolbar.errorMaxRows": "CHYBA! Nejvyšší možný počet datových řad v každém grafu je 255", "SSE.Controllers.Toolbar.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Controllers.Toolbar.textAccent": "Zvýraznění", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Směrové", "SSE.Controllers.Toolbar.textFontSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu z rozmezí 1 až 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Funkce", "SSE.Controllers.Toolbar.textIndicator": "Indikátory", "SSE.Controllers.Toolbar.textInsert": "Vložit", "SSE.Controllers.Toolbar.textIntegral": "Integ<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Velké operátory", "SSE.Controllers.Toolbar.textLimitAndLog": "Limity a logaritmy", "SSE.Controllers.Toolbar.textLongOperation": "Dlouhý provoz", "SSE.Controllers.Toolbar.textMatrix": "Mat<PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operátory", "SSE.Controllers.Toolbar.textPivot": "Kontingenční tabulka", "SSE.Controllers.Toolbar.textRadical": "Odmo<PERSON>niny", "SSE.Controllers.Toolbar.textRating": "Hodnocení", "SSE.Controllers.Toolbar.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Controllers.Toolbar.textScript": "Skripty", "SSE.Controllers.Toolbar.textShapes": "Obrazce", "SSE.Controllers.Toolbar.textSymbols": "Symboly", "SSE.Controllers.Toolbar.textWarning": "Varování", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Šipka vlevo-vpravo nad", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Šipka vlevo nad", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Šipka vpravo nad", "SSE.Controllers.Toolbar.txtAccent_Bar": "Vodorovná čárka", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Čára pod", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Čára nad", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Vzorec v rámečku (s volným místem)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Vzorec v rámečku (ukázka)", "SSE.Controllers.Toolbar.txtAccent_Check": "Kontrola", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Závorka pod", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Závorka nad", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC s čárou nad", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y s čárou nad", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Dot": "Tečka", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Dvojitá vodorovná čárka", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Znak seskupení pod", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Znak seskupení nad", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpuna vlevo nad", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpuna vpravo nad", "SSE.Controllers.Toolbar.txtAccent_Hat": "Stříška", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Tilda", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Závorky s oddělovači", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Závorky s oddělovači", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Závorky s oddělovači", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (dv<PERSON>ínky)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (t<PERSON>i <PERSON>ínky)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Složený objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Složený objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Ukázka případů", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Kombinační <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Kombinační <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Závorky s oddělovači", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Jednoduchá závorka", "SSE.Controllers.Toolbar.txtDeleteCells": "Odstranit buňky", "SSE.Controllers.Toolbar.txtExpand": "Rozbalit a seřadit", "SSE.Controllers.Toolbar.txtExpandSort": "Data vedle výběru nebudou seřazena. Chcete rozšířit výběr tak, aby zahrnoval sousední data, nebo pokračovat v seřazení pouze vybraných buněk?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Zkosený zlomek", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Line<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionPi_2": "<PERSON><PERSON> lomeno dv<PERSON>ma", "SSE.Controllers.Toolbar.txtFractionSmall": "Malý zlo<PERSON>k", "SSE.Controllers.Toolbar.txtFractionVertical": "Složený zlomek", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverzní funk<PERSON> kos<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Inverzní funkce hyperbolický kosinus", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverzní funkce kotangens", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Inverzní funkce hyperbolický kotangens", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverzní funkce kosekans", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Inverzní funkce hyperbolický kosekans", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverzní <PERSON>ce sekans", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Inverzní funkce hyperbolický sekans", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Inverzní funkce sinus", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Inverzní funkce hyperbolický sinus", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverzní <PERSON>ce tangens", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Inverzní funkce hyperbolický tangens", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON> cosinus", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Funkce hyperbolický kosinus", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON>ce kotangens", "SSE.Controllers.Toolbar.txtFunction_Coth": "Funkce Hyperbolický kotangens", "SSE.Controllers.Toolbar.txtFunction_Csc": "Funkce kosekans", "SSE.Controllers.Toolbar.txtFunction_Csch": "Funkce hyperbolický kosekans", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> théta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Vzorec tangens", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON> se<PERSON>s", "SSE.Controllers.Toolbar.txtFunction_Sech": "Funkce hyperbolický sekans", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON>ce sinus", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Funkce hyperbolický sinus", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON>ce tangens", "SSE.Controllers.Toolbar.txtFunction_Tanh": "<PERSON>ce hyperbolický tangens", "SSE.Controllers.Toolbar.txtInsertCells": "Vložit buňky", "SSE.Controllers.Toolbar.txtIntegral": "Integrál", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferenci<PERSON>l theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferenciál x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferenci<PERSON><PERSON> y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrál", "SSE.Controllers.Toolbar.txtIntegralDouble": "Dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralOriented": "Křivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Křivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Křivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Prostorový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Prostorový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Prostorový integrál", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integrál", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON>jn<PERSON> integrál", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON>jn<PERSON> integrál", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON>jn<PERSON> integrál", "SSE.Controllers.Toolbar.txtInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Konjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Konjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Konjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Konjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Konjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Sjednocení", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Disjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Disjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Disjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Disjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Disjunkce", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Průnik", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Průnik", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Průnik", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Průnik", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Průnik", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Sjednocení", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Sjednocení", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Sjednocení", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Sjednocení", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Sjednocení", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Příklad limity", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Příklad maxima", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Přirozený logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Poblíž Vašeho výběru existují data, nemáte však dostatečná oprávnění k úpravě těchto buněk.<br>Chcete pokračovat s aktuálním výběrem?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Prázdná matice se závorkami", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Prázdná matice se závorkami", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Prázdná matice se závorkami", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Prázdná matice se závorkami", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Tečky na řádku", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Tečky v řádku", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Úhlop<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Svislé tečky", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Řídká matice", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Řídká matice", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Jednotková matice", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Jednotková matice", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Jednotková matice", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Jednotková matice", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Šipka vlevo-vpravo pod", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Šipka vlevo-vpravo nad", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Šipka vlevo pod", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Šipka vlevo nad", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Šipka vpravo pod", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Šipka vpravo nad", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dvojtečka rovná se", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Vzniká", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta vzniká", "SSE.Controllers.Toolbar.txtOperator_Definition": "Rovná se podle definice", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta rovná se", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Šipka vlevo-vpravo pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Šipka vlevo-vpravo nad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Šipka vlevo pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Šipka vlevo nad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Šipka vpravo pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Šipka vpravo nad", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON><PERSON> rov<PERSON> se", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON> r<PERSON> se", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus rovná se", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "M<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Odmo<PERSON>niny", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Odmo<PERSON>niny", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Odmocnina se stupněm", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Třetí odmocnina", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "n-tá odmocnina", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> od<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Dolní index", "SSE.Controllers.Toolbar.txtScriptSubSup": "Dolní-horní index", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Levý horní-dolní index", "SSE.Controllers.Toolbar.txtScriptSup": "Horní index", "SSE.Controllers.Toolbar.txtSorting": "Řazení", "SSE.Controllers.Toolbar.txtSortSelected": "Se<PERSON><PERSON><PERSON> v<PERSON>né", "SSE.Controllers.Toolbar.txtSymbol_about": "Přibližně", "SSE.Controllers.Toolbar.txtSymbol_additional": "Doplněk", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Téměř se rovná", "SSE.Controllers.Toolbar.txtSymbol_ast": "<PERSON>er<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cap": "Průnik", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Třetí odmocnina", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Výpustka v ose řádku", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Stupně Celsia", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Přibližně se rovná", "SSE.Controllers.Toolbar.txtSymbol_cup": "Sjednocení", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Výpustka úhlopříčně dolů", "SSE.Controllers.Toolbar.txtSymbol_degree": "Stupně", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Znak <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Prázdná množina", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Rovná se", "SSE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON> s", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON>ist<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktor<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Stupně Fahrenheita", "SSE.Controllers.Toolbar.txtSymbol_forall": "Pro každé", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "Větší nebo rovná se", "SSE.Controllers.Toolbar.txtSymbol_gg": "Mnohem větší než", "SSE.Controllers.Toolbar.txtSymbol_greater": "Větš<PERSON> než", "SSE.Controllers.Toolbar.txtSymbol_in": "Prvek náleží", "SSE.Controllers.Toolbar.txtSymbol_inc": "Přír<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Nekonečno", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Šipka vlevo", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Šipka vlevo a vpravo", "SSE.Controllers.Toolbar.txtSymbol_leq": "Méně než nebo rovná se", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ll": "Mnohem menší než", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON> plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Nerov<PERSON> se", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON> jako p<PERSON>k", "SSE.Controllers.Toolbar.txtSymbol_not": "Znak negace", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Neexistuje", "SSE.Controllers.Toolbar.txtSymbol_nu": "Ný", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Parciální diferenciál", "SSE.Controllers.Toolbar.txtSymbol_percent": "Procento", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fí", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pí", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus mínus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Úměrný k", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psí", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Čtvrtá odmocnina", "SSE.Controllers.Toolbar.txtSymbol_qed": "Konec dů<PERSON>zu", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Výpustka úhlopříčně nahoru", "SSE.Controllers.Toolbar.txtSymbol_rho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Šipka vpravo", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Znak odmocniny", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "A proto", "SSE.Controllers.Toolbar.txtSymbol_theta": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_times": "Znak násobení", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Varianta epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Varianta Fí", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Varianta <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Varianta Sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Svislá výpustka", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ksí", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Styl tabulky tmavý", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Styl tabulky světlý", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Styl tabulky střední", "SSE.Controllers.Toolbar.warnLongOperation": "Dokončení operace, kterou se chyst<PERSON>te prov<PERSON>t, by m<PERSON><PERSON> trvat opravdu dlouho.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Toolbar.warnMergeLostData": "Ve sloučené buňce budou zachována pouze data z původní levé horn<PERSON> buňky.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Viewport.textFreezePanes": "Ukotvit <PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "Zobrazit stín ukotvených příček", "SSE.Controllers.Viewport.textHideFBar": "Skrý<PERSON> ř<PERSON>dek v<PERSON>ů", "SSE.Controllers.Viewport.textHideGridlines": "Skrýt mřížku", "SSE.Controllers.Viewport.textHideHeadings": "Skr<PERSON><PERSON> z<PERSON>av<PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Odd<PERSON>lov<PERSON><PERSON> desetinný<PERSON> m<PERSON>t", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Nastavení použitá pro rozpoznání číselných dat", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Textový kvalifikátor", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Pokročilé nastavení", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(ž<PERSON><PERSON><PERSON>)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Uživatelsky určený filtr", "SSE.Views.AutoFilterDialog.textAddSelection": "Přidat aktuální výběr k filtrování", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Vybrat vše", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Vybrat všechny výsledky hledání", "SSE.Views.AutoFilterDialog.textWarning": "Varování", "SSE.Views.AutoFilterDialog.txtAboveAve": "Nadprůměrný", "SSE.Views.AutoFilterDialog.txtBegins": "Začíná na…", "SSE.Views.AutoFilterDialog.txtBelowAve": "Podprůměrný", "SSE.Views.AutoFilterDialog.txtBetween": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtClear": "Vyčistit", "SSE.Views.AutoFilterDialog.txtContains": "<PERSON><PERSON><PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtEmpty": "Zadejte filtr buněk", "SSE.Views.AutoFilterDialog.txtEnds": "Končí na…", "SSE.Views.AutoFilterDialog.txtEquals": "Rovná se…", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrovat podle barvy buněk", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrovat podle barvy písma", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON>…", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Větší než nebo rovná se…", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtrování štítků", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON><PERSON>ě než nebo rovná se…", "SSE.Views.AutoFilterDialog.txtNotBegins": "Nezačíná na…", "SSE.Views.AutoFilterDialog.txtNotBetween": "Ne mezi...", "SSE.Views.AutoFilterDialog.txtNotContains": "<PERSON><PERSON><PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtNotEnds": "Nekončí na…", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON><PERSON><PERSON> se…", "SSE.Views.AutoFilterDialog.txtNumFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtReapply": "Použ<PERSON>t znov<PERSON>", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON><PERSON><PERSON> podle barvy buněk", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON><PERSON><PERSON> podle barvy p<PERSON>", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Seřadit od nejvyššího po nejnižší", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Seřadit od nejnižšího po nejvyšší", "SSE.Views.AutoFilterDialog.txtSortOption": "<PERSON><PERSON><PERSON> možnosti řazení…", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtr textu", "SSE.Views.AutoFilterDialog.txtTitle": "Filtr", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtr hodnot", "SSE.Views.AutoFilterDialog.warnFilterError": "Aby bylo mož<PERSON>é p<PERSON>t filtr hodnot, je třeba, aby v oblasti Hodnoty byla alespoň jedna hodnota.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Je třeba zvolit alespoň jednu hodnotu", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> názvů", "SSE.Views.CellEditor.tipFormula": "Vlož<PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "CHYBA! Nejvyšší možný počet datových řad v každém grafu je 255", "SSE.Views.CellRangeDialog.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.CellRangeDialog.txtInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> rozsah dat", "SSE.Views.CellSettings.strShrink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby se vešlo", "SSE.Views.CellSettings.strWrap": "Zalomit text", "SSE.Views.CellSettings.textAngle": "Úhel", "SSE.Views.CellSettings.textBackColor": "<PERSON>va p<PERSON>adí", "SSE.Views.CellSettings.textBackground": "<PERSON>va p<PERSON>adí", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "SSE.Views.CellSettings.textColor": "Vyplnit barvou", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textCondFormat": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textControl": "Kontrola textu", "SSE.Views.CellSettings.textDataBars": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textDirection": "Směr", "SSE.Views.CellSettings.textFill": "Výplň", "SSE.Views.CellSettings.textForeground": "<PERSON>va popředí", "SSE.Views.CellSettings.textGradient": "Stínování", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Výplň přechodem", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "Polož<PERSON>", "SSE.Views.CellSettings.textLinear": "Lineární", "SSE.Views.CellSettings.textManageRule": "Spravovat pravidla", "SSE.Views.CellSettings.textNewRule": "Nové pravidlo", "SSE.Views.CellSettings.textNoFill": "Bez výplně", "SSE.Views.CellSettings.textOrientation": "Orientace textu", "SSE.Views.CellSettings.textPattern": "Vzor", "SSE.Views.CellSettings.textPatternFill": "Vzor", "SSE.Views.CellSettings.textPosition": "Pozice", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON>, na které chcete p<PERSON>žít výše vybraný styl.", "SSE.Views.CellSettings.textSelection": "<PERSON>d st<PERSON>jícího výběru", "SSE.Views.CellSettings.textThisPivot": "Z této kontingenční tabulky", "SSE.Views.CellSettings.textThisSheet": "Z tohoto listu", "SSE.Views.CellSettings.textThisTable": "Z této tabulky", "SSE.Views.CellSettings.tipAddGradientPoint": "P<PERSON><PERSON>t stínování", "SSE.Views.CellSettings.tipAll": "Nastavit vnější ohraničení a všechny vnitřní čáry", "SSE.Views.CellSettings.tipBottom": "Nastavit pouze vnější oh<PERSON>í dole", "SSE.Views.CellSettings.tipDiagD": "Nastavit <PERSON>říčný okraj dolů", "SSE.Views.CellSettings.tipDiagU": "Nastavit <PERSON>říčný okraj nahoru", "SSE.Views.CellSettings.tipInner": "Nastavit pouze vnitřní <PERSON>", "SSE.Views.CellSettings.tipInnerHor": "Nastavit pouze vodorovné vnitřní <PERSON>", "SSE.Views.CellSettings.tipInnerVert": "Nastavit pouze svislé vnitřní <PERSON>", "SSE.Views.CellSettings.tipLeft": "Nastavit pouze vnější ohraničení vlevo", "SSE.Views.CellSettings.tipNone": "Nastavit bez ohraničení", "SSE.Views.CellSettings.tipOuter": "Nastavit pouze vnější <PERSON>í", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Odstranit stínování", "SSE.Views.CellSettings.tipRight": "Nastavit pouze vnější ohraničení vpravo", "SSE.Views.CellSettings.tipTop": "Nastavit pouze vnější horní ohraničení", "SSE.Views.ChartDataDialog.errorInFormula": "V zadaném vzorci je chyba.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Odkaz není platný. Musí odkazovat na otevřený list.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Nejvyšší možný počet bodů v sérii na graf je 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Maximální počet datových řad na graf je 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Odkaz je neplatný. Odkazy na názvy, hodnoty, velikosti, štítky dat musí  být samost<PERSON> buň<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo s<PERSON>. ", "SSE.Views.ChartDataDialog.errorNoValues": "Pokud ch<PERSON>te v<PERSON> gra<PERSON>, je t<PERSON><PERSON><PERSON>, aby pos<PERSON><PERSON><PERSON>t obsahovala alespoň jednu hodnotu.", "SSE.Views.ChartDataDialog.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Kategorie) štítků osy", "SSE.Views.ChartDataDialog.textData": "<PERSON><PERSON><PERSON><PERSON> dat grafu", "SSE.Views.ChartDataDialog.textDelete": "Odstranit", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.ChartDataDialog.textSelectData": "Vybrat data", "SSE.Views.ChartDataDialog.textSeries": "Záz<PERSON>y <PERSON>y (Série)", "SSE.Views.ChartDataDialog.textSwitch": "Pohodit Řádky/Sloupce", "SSE.Views.ChartDataDialog.textTitle": "Data grafu", "SSE.Views.ChartDataDialog.textUp": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "V zadaném vzorci je chyba.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Odkaz není platný. Musí odkazovat na otevřený list.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Nejvyšší možný počet bodů v sérii na graf je 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Maximální počet datových řad na graf je 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Odkaz je neplatný. Odkazy na názvy, hodnoty, velikosti, štítky dat musí  být samost<PERSON> buň<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo s<PERSON>. ", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Pokud ch<PERSON>te v<PERSON> gra<PERSON>, je t<PERSON><PERSON><PERSON>, aby pos<PERSON><PERSON><PERSON>t obsahovala alespoň jednu hodnotu.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.ChartDataRangeDialog.textSelectData": "Vybrat data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Rozsah štítků osy", "SSE.Views.ChartDataRangeDialog.txtChoose": "Zvolte rozsah", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON>v řady", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Štítky osy", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Upravit série", "SSE.Views.ChartDataRangeDialog.txtValues": "Hodnoty", "SSE.Views.ChartDataRangeDialog.txtXValues": "Hodnoty na vodorovné ose", "SSE.Views.ChartDataRangeDialog.txtYValues": "Hodnoty na svislé ose", "SSE.Views.ChartSettings.errorMaxRows": "Maximální počet datových řad na graf je 255.", "SSE.Views.ChartSettings.strLineWeight": "Tloušťka čáry", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Šablona", "SSE.Views.ChartSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ChartSettings.textBorderSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu z rozmezí 0 až 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Změnit typ", "SSE.Views.ChartSettings.textChartType": "Změnit typ grafu", "SSE.Views.ChartSettings.textEditData": "Upravit data a umístění", "SSE.Views.ChartSettings.textFirstPoint": "Prvn<PERSON> bod", "SSE.Views.ChartSettings.textHeight": "Výška", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textKeepRatio": "Konst<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Poslední bod", "SSE.Views.ChartSettings.textLowPoint": "Nízký bod", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.ChartSettings.textSelectData": "Vybrat data", "SSE.Views.ChartSettings.textShow": "Zobrazit", "SSE.Views.ChartSettings.textSize": "Velikost", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Pohodit Řádky/Sloupce", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textWidth": "Šířka", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "CHYBA! Nejvyšší možný počet bodů za sebou v jednom grafu je 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "CHYBA! Nejvyšší možný počet datových řad pro jeden graf je 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.ChartSettingsDlg.textAlt": "Alternativní text", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, grafu, obrazci nebo v tabulce.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "<PERSON>ky", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automaticky pro každý", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Křížení os", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Možnosti osy", "SSE.Views.ChartSettingsDlg.textAxisPos": "Umístěn<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Nastavení osy", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "funkce Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Mezi značkami zaškrtnutí", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON>v kategorie", "SSE.Views.ChartSettingsDlg.textCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Prvky grafu a<br>Legenda grafu", "SSE.Views.ChartSettingsDlg.textChartTitle": "Nad<PERSON> grafu", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Uživatelsky určené", "SSE.Views.ChartSettingsDlg.textDataColumns": "ve slou<PERSON><PERSON><PERSON>ch", "SSE.Views.ChartSettingsDlg.textDataLabels": "Š<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.ChartSettingsDlg.textDataRows": "v řádcích", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Zobrazit legendu", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Skryté a prázdné buňky", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Připojit datové body k řádku", "SSE.Views.ChartSettingsDlg.textFit": "Přizpůsobit šířce", "SSE.Views.ChartSettingsDlg.textFixed": "Napevno", "SSE.Views.ChartSettingsDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGaps": "Mezery", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Seskupit mikrografy", "SSE.Views.ChartSettingsDlg.textHide": "Skr<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "Skr<PERSON><PERSON> o<PERSON>", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "Vodorovná osa", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Pomocná vodorovná osa", "SSE.Views.ChartSettingsDlg.textHorizontal": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "V rámci", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Uvni<PERSON><PERSON> dole", "SSE.Views.ChartSettingsDlg.textInnerTop": "Uvnitř nahoře", "SSE.Views.ChartSettingsDlg.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.ChartSettingsDlg.textLabelDist": "Vzdálenost štítku osy", "SSE.Views.ChartSettingsDlg.textLabelInterval": "<PERSON>val <PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Možnosti štítku", "SSE.Views.ChartSettingsDlg.textLabelPos": "Pozice štítku", "SSE.Views.ChartSettingsDlg.textLayout": "Rozvržení", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Překrytí vlevo", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "Vpravo", "SSE.Views.ChartSettingsDlg.textLegendTop": "Nahoře", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLogScale": "Logaritmické <PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON>z<PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Hlavní", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Hlavní a vedlejší", "SSE.Views.ChartSettingsDlg.textMajorType": "Hlavní typ", "SSE.Views.ChartSettingsDlg.textManual": "R<PERSON>č<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Interval mezi značkami", "SSE.Views.ChartSettingsDlg.textMaxValue": "Nejvyš<PERSON><PERSON> ho<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Vedlejší", "SSE.Views.ChartSettingsDlg.textMinorType": "Vedlejší typ", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimální hodnota", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Bez překrytí", "SSE.Views.ChartSettingsDlg.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Na značkách zaškrtnutí", "SSE.Views.ChartSettingsDlg.textOut": "Vně", "SSE.Views.ChartSettingsDlg.textOuterTop": "Vnějš<PERSON>", "SSE.Views.ChartSettingsDlg.textOverlay": "Překrytí", "SSE.Views.ChartSettingsDlg.textReverse": "Hodnoty v opačném pořadí", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Obrácené p<PERSON>ř<PERSON>í", "SSE.Views.ChartSettingsDlg.textRight": "Vpravo", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Překrytí vpravo", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "Stejné pro všechny", "SSE.Views.ChartSettingsDlg.textSelectData": "Vybrat data", "SSE.Views.ChartSettingsDlg.textSeparator": "Oddělovače štítků", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON>v řady", "SSE.Views.ChartSettingsDlg.textShow": "Zobrazit", "SSE.Views.ChartSettingsDlg.textShowBorders": "Zobrazit ohraničení grafu", "SSE.Views.ChartSettingsDlg.textShowData": "Zobrazit data v uzavřených řádcích a sloupcích", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Zobrazit prázdné buňky jako", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Zobrazit osu", "SSE.Views.ChartSettingsDlg.textShowValues": "Zobrazit hodnoty grafu", "SSE.Views.ChartSettingsDlg.textSingle": "Jednoduchý mikrograf", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Přichytá<PERSON><PERSON> b<PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStraight": "Rov<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Možnosti zaškrtávání", "SSE.Views.ChartSettingsDlg.textTitle": "<PERSON> <PERSON> pokročilá nastavení", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Mikrograf – pokročilá nastavení", "SSE.Views.ChartSettingsDlg.textTop": "Nahoře", "SSE.Views.ChartSettingsDlg.textTrillions": "Biliony", "SSE.Views.ChartSettingsDlg.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Typy a data", "SSE.Views.ChartSettingsDlg.textUnits": "Zob<PERSON><PERSON> j<PERSON>", "SSE.Views.ChartSettingsDlg.textValue": "Hodnota", "SSE.Views.ChartSettingsDlg.textVertAxis": "Svislá osa", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Pomocná svislá osa", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Titulek osy x", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Titulek osy y", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.ChartTypeDialog.errorComboSeries": "Pro vytvoření kombino<PERSON>ho grafu, zvolte alespoň dvě skupiny dat. ", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Vybraný graf v<PERSON><PERSON>, která je v použita ve vybraném grafu. Vyberte jiný typ grafu.", "SSE.Views.ChartTypeDialog.textSecondary": "Pomocná osa", "SSE.Views.ChartTypeDialog.textSeries": "Série", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Typ grafu", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON> zdrojov<PERSON>ch dat", "SSE.Views.CreatePivotDialog.textDestination": "Zvolte kam umístit tabulku", "SSE.Views.CreatePivotDialog.textExist": "Existující list", "SSE.Views.CreatePivotDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.CreatePivotDialog.textNew": "Nový list", "SSE.Views.CreatePivotDialog.textSelectData": "Vybrat data", "SSE.Views.CreatePivotDialog.textTitle": "Vytvořit kontingenční tabulku", "SSE.Views.CreatePivotDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.CreateSparklineDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON> zdrojov<PERSON>ch dat", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, kde umístit mikrograf", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.CreateSparklineDialog.textSelectData": "Vybrat data", "SSE.Views.CreateSparklineDialog.textTitle": "Vytvořit mikrograf", "SSE.Views.CreateSparklineDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.DataTab.capBtnGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextCustomSort": "Uživatelsky určené řazení", "SSE.Views.DataTab.capBtnTextDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.DataTab.capBtnTextRemDuplicates": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Views.DataTab.capBtnTextToCol": "Text na sloupce", "SSE.Views.DataTab.capBtnUngroup": "Zrušit seskupení", "SSE.Views.DataTab.capDataFromText": "Získat data", "SSE.Views.DataTab.mniFromFile": "Z místního TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Z webové adresy TXT/CSV", "SSE.Views.DataTab.textBelow": "Řádky souhrnu pod podrobnost", "SSE.Views.DataTab.textClear": "Vyčistit ob<PERSON>s", "SSE.Views.DataTab.textColumns": "Zrušit seskupení sloupců", "SSE.Views.DataTab.textGroupColumns": "Seskupit sloupce", "SSE.Views.DataTab.textGroupRows": "Seskupit <PERSON>ky", "SSE.Views.DataTab.textRightOf": "S<PERSON><PERSON>ce souhrnů vpravo od podrobnosti", "SSE.Views.DataTab.textRows": "Zrušit seskupení řádků", "SSE.Views.DataTab.tipCustomSort": "Uživatelsky určené řazení", "SSE.Views.DataTab.tipDataFromText": "Získat data z textového/CSV souboru", "SSE.Views.DataTab.tipDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.DataTab.tipGroup": "Seskupit rozsah buněk", "SSE.Views.DataTab.tipRemDuplicates": "Odebrat z listu duplicitní <PERSON>", "SSE.Views.DataTab.tipToColumns": "Rozdělit text buňky do sloupců", "SSE.Views.DataTab.tipUngroup": "Zrušit seskupení rozsahu buněk", "SSE.Views.DataValidationDialog.errorFormula": "Hodnota je aktuálně vyhodnocena jako chy<PERSON>ná. Opravdu chcete pokračovat?", "SSE.Views.DataValidationDialog.errorInvalid": "Zadaná hodnota pole \"{0}\" je chyb<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidDate": "<PERSON><PERSON><PERSON> datum \"{0}\" nen<PERSON> platn<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidList": "Zdrojový seznam musí obsahovat oddělovače, nebo odkazovat na jeden řádek či sloupec.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Zadaný čas do pole \"{0}\" je chyb<PERSON><PERSON>.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Hodnota pole \"{1}\" mus<PERSON> být vět<PERSON><PERSON> nebo rovna hodnotě pole \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "<PERSON><PERSON><PERSON><PERSON> zadat hodnotu do pole \"{0}\" and pole \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "<PERSON> kolonky „{0}“ je třeba zadat hodnotu.", "SSE.Views.DataValidationDialog.errorNamedRange": "<PERSON>j<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, se ne<PERSON><PERSON><PERSON>.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "V podmínce „{0}“ není možné p<PERSON>žít záporné hodnoty.", "SSE.Views.DataValidationDialog.errorNotNumeric": "Pole \"{0}\" m<PERSON><PERSON> <PERSON><PERSON><PERSON>, mate<PERSON><PERSON><PERSON> v<PERSON>, nebo odkazovat na buňku obsahující číselnou hodnotu. ", "SSE.Views.DataValidationDialog.strError": "Chybová hláška", "SSE.Views.DataValidationDialog.strInput": "Zpráva při zadávání", "SSE.Views.DataValidationDialog.strSettings": "Nastavení", "SSE.Views.DataValidationDialog.textAlert": "Upozornění", "SSE.Views.DataValidationDialog.textAllow": "Povolit", "SSE.Views.DataValidationDialog.textApply": "Uplatnit tyto změny na veškeré ostatní buňky, kter<PERSON> mají stejn<PERSON> nastavení", "SSE.Views.DataValidationDialog.textCellSelected": "Zprávu při zadávání zobrazit při výběru buňky", "SSE.Views.DataValidationDialog.textCompare": "Porovnat s", "SSE.Views.DataValidationDialog.textData": "Údaje", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> datum", "SSE.Views.DataValidationDialog.textEndTime": "Konečný čas", "SSE.Views.DataValidationDialog.textError": "Chybové hlášení", "SSE.Views.DataValidationDialog.textFormula": "Vzorec", "SSE.Views.DataValidationDialog.textIgnore": "Ignorovat prázdné buňky", "SSE.Views.DataValidationDialog.textInput": "Zpráva při zadávání", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Zpráva", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Vybrat data", "SSE.Views.DataValidationDialog.textShowDropDown": "Zobrazit rozbalovací seznam v buňce", "SSE.Views.DataValidationDialog.textShowError": "Zobrazovat chybovou hlášku po zadání chybných dat", "SSE.Views.DataValidationDialog.textShowInput": "Zprávu při zadávání zobrazit při výběru buňky", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Datum z<PERSON>čá<PERSON>", "SSE.Views.DataValidationDialog.textStartTime": "Čas začátku", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Pokud uživatel zadá nepovolená data, zobrazit tuto zprávu", "SSE.Views.DataValidationDialog.txtAny": "Libovolná hodnota", "SSE.Views.DataValidationDialog.txtBetween": "mezi", "SSE.Views.DataValidationDialog.txtDate": "Datum", "SSE.Views.DataValidationDialog.txtDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtElTime": "Uplynulý čas", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> datum", "SSE.Views.DataValidationDialog.txtEndTime": "Konečný čas", "SSE.Views.DataValidationDialog.txtEqual": "rovná se", "SSE.Views.DataValidationDialog.txtGreaterThan": "větš<PERSON> než", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "větší nebo rovno", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Méně než nebo rovno", "SSE.Views.DataValidationDialog.txtList": "Seznam", "SSE.Views.DataValidationDialog.txtNotBetween": "ne mezi", "SSE.Views.DataValidationDialog.txtNotEqual": "nerovná se", "SSE.Views.DataValidationDialog.txtOther": "Ostatní", "SSE.Views.DataValidationDialog.txtStartDate": "Datum z<PERSON>čá<PERSON>", "SSE.Views.DataValidationDialog.txtStartTime": "Čas začátku", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON><PERSON><PERSON> textu", "SSE.Views.DataValidationDialog.txtTime": "Čas", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "A", "SSE.Views.DigitalFilterDialog.capCondition1": "rovná se", "SSE.Views.DigitalFilterDialog.capCondition10": "nekončí na", "SSE.Views.DigitalFilterDialog.capCondition11": "obs<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition2": "nerovná se", "SSE.Views.DigitalFilterDialog.capCondition3": "je v<PERSON><PERSON><PERSON><PERSON> než", "SSE.Views.DigitalFilterDialog.capCondition4": "je vet<PERSON><PERSON> než nebo rovno", "SSE.Views.DigitalFilterDialog.capCondition5": "je men<PERSON><PERSON> ne<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition6": "je men<PERSON><PERSON> než nebo rovno", "SSE.Views.DigitalFilterDialog.capCondition7": "začíná", "SSE.Views.DigitalFilterDialog.capCondition8": "nezačíná na", "SSE.Views.DigitalFilterDialog.capCondition9": "končí na", "SSE.Views.DigitalFilterDialog.capOr": "Nebo", "SSE.Views.DigitalFilterDialog.textNoFilter": "ž<PERSON><PERSON><PERSON> filtr", "SSE.Views.DigitalFilterDialog.textShowRows": "Zobrazit řádky kde", "SSE.Views.DigitalFilterDialog.textUse1": "Použijte ? pro zastoupení libovolného znaku", "SSE.Views.DigitalFilterDialog.textUse2": "Použijte * pro zastoupení libovolné řady znaků", "SSE.Views.DigitalFilterDialog.txtTitle": "Uživatelsky určený filtr", "SSE.Views.DocumentHolder.advancedImgText": "Pokročilá nastavení obrázku", "SSE.Views.DocumentHolder.advancedShapeText": "Pokročilá nastavení obrazců", "SSE.Views.DocumentHolder.advancedSlicerText": "Pokročilé nastavení průřezu", "SSE.Views.DocumentHolder.bottomCellText": "Zarovnat dolů", "SSE.Views.DocumentHolder.bulletsText": "Odrážky a číslování", "SSE.Views.DocumentHolder.centerCellText": "Zarovnat na střed", "SSE.Views.DocumentHolder.chartDataText": "Vybrat data grafu", "SSE.Views.DocumentHolder.chartText": "Pokročilá nastavení grafu", "SSE.Views.DocumentHolder.chartTypeText": "Změnit typ grafu", "SSE.Views.DocumentHolder.deleteColumnText": "Sloupec", "SSE.Views.DocumentHolder.deleteRowText": "Řádek", "SSE.Views.DocumentHolder.deleteTableText": "Tabulka", "SSE.Views.DocumentHolder.direct270Text": "Otočit text nahoru", "SSE.Views.DocumentHolder.direct90Text": "Otočit text dolů", "SSE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON><PERSON>u", "SSE.Views.DocumentHolder.editChartText": "Upravit data", "SSE.Views.DocumentHolder.editHyperlinkText": "Upravit hypertextový odkaz", "SSE.Views.DocumentHolder.insertColumnLeftText": "Sloupec vlevo", "SSE.Views.DocumentHolder.insertColumnRightText": "Sloupec vpravo", "SSE.Views.DocumentHolder.insertRowAboveText": "Řádek nad", "SSE.Views.DocumentHolder.insertRowBelowText": "Řádek pod", "SSE.Views.DocumentHolder.originalSizeText": "Skutečná velikost", "SSE.Views.DocumentHolder.removeHyperlinkText": "Odebrat hypertextový odkaz", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.selectDataText": "Data sloupce", "SSE.Views.DocumentHolder.selectRowText": "Řádek", "SSE.Views.DocumentHolder.selectTableText": "Tabulka", "SSE.Views.DocumentHolder.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strDetails": "Podrobnosti podpisu", "SSE.Views.DocumentHolder.strSetup": "Nastavení podpisu", "SSE.Views.DocumentHolder.strSign": "Podepsat", "SSE.Views.DocumentHolder.textAlign": "Zarovnání", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Přesunout do pozadí", "SSE.Views.DocumentHolder.textArrangeBackward": "Přesunout o vrstvu níž", "SSE.Views.DocumentHolder.textArrangeForward": "Přenést do popředí", "SSE.Views.DocumentHolder.textArrangeFront": "Přenést do popředí", "SSE.Views.DocumentHolder.textAverage": "Průměrné", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "Počet", "SSE.Views.DocumentHolder.textCrop": "Oříznout", "SSE.Views.DocumentHolder.textCropFill": "Výplň", "SSE.Views.DocumentHolder.textCropFit": "Přizpůsobit", "SSE.Views.DocumentHolder.textEditPoints": "Upravit body", "SSE.Views.DocumentHolder.textEntriesList": "Vybrat z rozbalovacího seznamu", "SSE.Views.DocumentHolder.textFlipH": "Převrátit vodorovně", "SSE.Views.DocumentHolder.textFlipV": "Př<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.textFreezePanes": "Ukotvit <PERSON>", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.DocumentHolder.textFromStorage": "Z úložiště", "SSE.Views.DocumentHolder.textFromUrl": "Z URL adresy", "SSE.Views.DocumentHolder.textListSettings": "Nastavení seznamu", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>ro", "SSE.Views.DocumentHolder.textMax": "Maximum", "SSE.Views.DocumentHolder.textMin": "Minimum", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Číslování", "SSE.Views.DocumentHolder.textReplace": "Nahradit obrázek", "SSE.Views.DocumentHolder.textRotate": "O<PERSON>č<PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Otočit o 90° doleva", "SSE.Views.DocumentHolder.textRotate90": "Otočit o 90° doprava", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Zarovnat dolů", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Zarovnat na střed", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Zarovnat vlevo", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Zarovnat na střed", "SSE.Views.DocumentHolder.textShapeAlignRight": "Zarovnat vpravo", "SSE.Views.DocumentHolder.textShapeAlignTop": "Zarovnat nahoru", "SSE.Views.DocumentHolder.textStdDev": "Směrodatná odchylka", "SSE.Views.DocumentHolder.textSum": "SUMA", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Zrušit ukotvení příček", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Zatržítkové o<PERSON>", "SSE.Views.DocumentHolder.tipMarkersDash": "Pomlčkové o<PERSON>žky", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Kosočtvercové odrážky s výplní", "SSE.Views.DocumentHolder.tipMarkersFRound": "Vyplně<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Plné čtvercové <PERSON>", "SSE.Views.DocumentHolder.tipMarkersHRound": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersStar": "Hvězdičkové odrážky", "SSE.Views.DocumentHolder.topCellText": "Zarovnat nahoru", "SSE.Views.DocumentHolder.txtAccounting": "Účetnictví", "SSE.Views.DocumentHolder.txtAddComment": "Přidat komentář", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>zev", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Vzestupně", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automatické nastavení <PERSON> slou<PERSON>ce", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automatické nastavení výšky řádku", "SSE.Views.DocumentHolder.txtClear": "Vymazat", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearHyper": "Hypertextové odkazy", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Vyčistit vybrané skupiny mikrografů", "SSE.Views.DocumentHolder.txtClearSparklines": "Vyčistit vybrané mikrografy", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "Nastavit šířku slou<PERSON>ce", "SSE.Views.DocumentHolder.txtCondFormat": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCopy": "Zkopírovat", "SSE.Views.DocumentHolder.txtCurrency": "Měna", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Uživatelsky určená šířka sloupce", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Uživatelsky určená výška řádku", "SSE.Views.DocumentHolder.txtCustomSort": "Vlastní <PERSON>", "SSE.Views.DocumentHolder.txtCut": "Vyjmout", "SSE.Views.DocumentHolder.txtDate": "Datum", "SSE.Views.DocumentHolder.txtDelete": "Vymazat", "SSE.Views.DocumentHolder.txtDescending": "Sestupně", "SSE.Views.DocumentHolder.txtDistribHor": "Rozmístit vodorovně", "SSE.Views.DocumentHolder.txtDistribVert": "Rozmístit svisle", "SSE.Views.DocumentHolder.txtEditComment": "Upravit komentář", "SSE.Views.DocumentHolder.txtFilter": "Filtr", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrovat podle barvy buňky", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrovat podle barvy písma", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrovat podle hodnoty vybrané buňky", "SSE.Views.DocumentHolder.txtFormula": "Vlož<PERSON>", "SSE.Views.DocumentHolder.txtFraction": "Zlomek", "SSE.Views.DocumentHolder.txtGeneral": "Obecné", "SSE.Views.DocumentHolder.txtGetLink": "Získat odkaz pro tento rozsah", "SSE.Views.DocumentHolder.txtGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtHide": "Skr<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsert": "Vložit", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hypertextový odkaz", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPaste": "Vložit", "SSE.Views.DocumentHolder.txtPercentage": "Procento", "SSE.Views.DocumentHolder.txtReapply": "Použ<PERSON>t znov<PERSON>", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "Nastavit výšku řádku", "SSE.Views.DocumentHolder.txtScientific": "Vědecké", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "Posunout buňky do<PERSON>ů", "SSE.Views.DocumentHolder.txtShiftLeft": "Posunout buňky vlevo", "SSE.Views.DocumentHolder.txtShiftRight": "Posunout buňky vpravo", "SSE.Views.DocumentHolder.txtShiftUp": "Posunout buňky nahoru", "SSE.Views.DocumentHolder.txtShow": "Zobrazit", "SSE.Views.DocumentHolder.txtShowComment": "Zobrazit komentář", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Vybraná barva buňky v horní části", "SSE.Views.DocumentHolder.txtSortFontColor": "Vybraná barva písma v horní části", "SSE.Views.DocumentHolder.txtSparklines": "Mikrografy", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Pokročilé nastavení odstavce", "SSE.Views.DocumentHolder.txtTime": "Čas", "SSE.Views.DocumentHolder.txtUngroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtWidth": "Šířka", "SSE.Views.DocumentHolder.vertAlignText": "Svislé zarovnání", "SSE.Views.FieldSettingsDialog.strLayout": "Rozvržení", "SSE.Views.FieldSettingsDialog.strSubtotals": "Dílčí souč<PERSON>", "SSE.Views.FieldSettingsDialog.textReport": "Formulář výkazu", "SSE.Views.FieldSettingsDialog.textTitle": "Nastavení kolonky", "SSE.Views.FieldSettingsDialog.txtAverage": "Průměrné", "SSE.Views.FieldSettingsDialog.txtBlank": "Vložit prázdné řádky za každou položku", "SSE.Views.FieldSettingsDialog.txtBottom": "Zobrazit zápatí skupiny", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompaktní", "SSE.Views.FieldSettingsDialog.txtCount": "Počet", "SSE.Views.FieldSettingsDialog.txtCountNums": "Spočítat buňky s čísly", "SSE.Views.FieldSettingsDialog.txtCustomName": "Vlast<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtEmpty": "Zobrazit položky bez dat", "SSE.Views.FieldSettingsDialog.txtMax": "Maximum", "SSE.Views.FieldSettingsDialog.txtMin": "Minimum", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "Opakovat štítky položek na každém řádku", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Zobrazit dílčí celkové součty", "SSE.Views.FieldSettingsDialog.txtSourceName": "Název zdroje:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Směrodatná odchylka", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Populační směrodatná odchylka", "SSE.Views.FieldSettingsDialog.txtSum": "SUMA", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funkce pro dílčí součty", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabelární", "SSE.Views.FieldSettingsDialog.txtTop": "Zobrazit v záhlaví skupiny.", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Otev<PERSON><PERSON><PERSON>ě<PERSON>í so<PERSON>u", "SSE.Views.FileMenu.btnCloseMenuCaption": "Zavřít nabídku", "SSE.Views.FileMenu.btnCreateNewCaption": "Vytvořit nový", "SSE.Views.FileMenu.btnDownloadCaption": "St<PERSON><PERSON><PERSON> jako", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "Nápověda", "SSE.Views.FileMenu.btnHistoryCaption": "Historie verzí", "SSE.Views.FileMenu.btnInfoCaption": "Informace o sešitu", "SSE.Views.FileMenu.btnPrintCaption": "Tisk", "SSE.Views.FileMenu.btnProtectCaption": "Zabezpečení", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "Př<PERSON>menovat", "SSE.Views.FileMenu.btnReturnCaption": "Zpátky do sešitu", "SSE.Views.FileMenu.btnRightsCaption": "Přístupové práva", "SSE.Views.FileMenu.btnSaveAsCaption": "Uložit jako", "SSE.Views.FileMenu.btnSaveCaption": "Uložit", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Uložit kopii jako", "SSE.Views.FileMenu.btnSettingsCaption": "Pokročilá nastavení", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Prázdný sešit", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Vytvořit nový", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Přidat autora", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Přidat text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikace", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Změnit přístupová práva", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Vytvořeno", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Naposledy upravil(a)", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON><PERSON> up<PERSON>no", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Vlastník", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Umístění", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> mají <PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Změnit přístupová práva", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> mají <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON><PERSON> s<PERSON>upráce na úpravách", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Odd<PERSON>lov<PERSON><PERSON> desetinný<PERSON> m<PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Jazyk slovníku", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Automatický", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Vyhlazování hran znaků", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Jazyk vzorce", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Příklad: SUMA; MIN; MAX; POČET", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorovat slova psaná pouze VELKÝMI PÍSMENY", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignorovat slova obsahující čísla", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Nastavení maker", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Při vkládání obsahu zobrazit tlačítko možností vložení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Styl reference R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Místní nastavení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Příklad:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Zobrazit komentáře v listu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Zobrazit vyřešené komentáře", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Statický", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Vzhled uživatelského rozhraní", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Měřit v jednotkách", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Použ<PERSON>t oddělovače podle místních nastavení (locale)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Výchozí měřítko zobrazení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Každých 10 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Každých 30 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Každých 5 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "<PERSON><PERSON><PERSON> obnova", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Automatic<PERSON>é <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Vypnuto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Uložit dočasnou verzi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON><PERSON> minutu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "<PERSON><PERSON> od<PERSON>zu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Autokorekce možnosti...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Katalánský", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Výchozí re<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimetry", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "čeština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "němčina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Úprava a uložení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "řečtina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "anglič<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "š<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Režim spolupráce v reálném čase. Veškeré změny jsou ukládány automaticky", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "ma<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "indonézština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Palec", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "<PERSON><PERSON>š<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "korejš<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "jako mac<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativní", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Kontrola pra<PERSON>pisu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Body", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON>š<PERSON> (Brazílie)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON>š<PERSON> (Portugalsko)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Místní", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Zapnout vše", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Zapnout všechna makra bez oznámení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "slovenština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Vypnout vše", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Vypnout všechna makra bez oznámení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Pro synchronizaci provedených změn klikněte na tlačítko \"Uložit\"", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Použít klávesu Alt pro navigaci uživatelským rozhraním za použití klávesnice", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Použít klávesu Option pro navigaci uživatelským rozhraním za použití klávesnice", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "vietnamština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Zobrazit oznámení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Vypnout všechna makra s oznámením", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "jako <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Pracovní prostředí", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Varování", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Heslem", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Zabezpečit sešit", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Podpisem", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Upravením budou ze sešitu odebrány podpisy.<br>Opravdu chcete pokračovat?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Tento list je zabezpečen heslem", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Tento list je třeba podepsat.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Do sešitu byly přidány platné podpisy. List je zabezpečen před <PERSON>.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Některé z digitálních podpisů v listu nejsou platné nebo je není možné ověřit. List je zabezpečen před úpravami.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Zobrazit podpisy", "SSE.Views.FormatRulesEditDlg.fillColor": "Barva výplně", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Varování", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 Barevná škála", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 Barevná škála", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Všechna ohraničení", "SSE.Views.FormatRulesEditDlg.textAppearance": "<PERSON><PERSON><PERSON>ed pruhu", "SSE.Views.FormatRulesEditDlg.textApply": "Uplatnit na rozsah", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatické", "SSE.Views.FormatRulesEditDlg.textAxis": "O<PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBold": "Tučn<PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "Ohraničení", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Ohraničen<PERSON> dole", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Není možno přidat podmíněné <PERSON>", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Vnitřní svislé ohraničení", "SSE.Views.FormatRulesEditDlg.textClear": "Vymazat", "SSE.Views.FormatRulesEditDlg.textColor": "<PERSON>va textu", "SSE.Views.FormatRulesEditDlg.textContext": "Kontext", "SSE.Views.FormatRulesEditDlg.textCustom": "Uživatelsky určené", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Ohraničení úhlopříčně dolů", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Ohraničení <PERSON>hlopříčně nahoru", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Zadejte platný vzorec.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Zadaný vzorec neodpovídá formátu <PERSON>, data, času nebo řetězci.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON>j<PERSON> hodnotu.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Zadaná hodnota neodpovídá formátu <PERSON>, data, času nebo řetězci.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Hodnota pro {0} musí výt větší než hodnota pro {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "<PERSON>ade<PERSON><PERSON> {0} a {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Vyplnit", "SSE.Views.FormatRulesEditDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormula": "Vzorec", "SSE.Views.FormatRulesEditDlg.textGradient": "Př<PERSON>od", "SSE.Views.FormatRulesEditDlg.textIconLabel": "když {0} {1} a", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "k<PERSON><PERSON> {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "k<PERSON><PERSON> je hodnota", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Dochází k překrytí jednoho nebo více rozsahů dat ikon.<br>Upravte hodnoty rozsahů dat ikon, aby nedocházelo k překrytí. ", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Vnitřní ok<PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Neplatný rozsah dat.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.FormatRulesEditDlg.textItalic": "Skloněné", "SSE.Views.FormatRulesEditDlg.textItem": "Položka", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Ohraničení vlevo", "SSE.Views.FormatRulesEditDlg.textLongBar": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Vnitřní vodorovné <PERSON>í", "SSE.Views.FormatRulesEditDlg.textMidpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textNegative": "Záporné", "SSE.Views.FormatRulesEditDlg.textNewColor": "Přidat novou vlastní barvu", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON>z oh<PERSON>í", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Jedna nebo více hodnot není platným procentuálním podílem.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Hodnota {0} není platný procentuální podíl.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Jedna nebo více hodnot není platným percentilem.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Hodnota {0} není platný percentil.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Vn<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "Procento", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Pozice", "SSE.Views.FormatRulesEditDlg.textPositive": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Není možné použít relativní odkaz při aplikovaném podmíněném formátování bare<PERSON>, da<PERSON><PERSON>ch pruhů a sad ikon.", "SSE.Views.FormatRulesEditDlg.textReverse": "Obrácené pořadí ikon", "SSE.Views.FormatRulesEditDlg.textRight2Left": "<PERSON><PERSON><PERSON> doleva", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Ohraničení vpravo", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "<PERSON><PERSON><PERSON><PERSON> jako k<PERSON>", "SSE.Views.FormatRulesEditDlg.textSelectData": "Vybrat data", "SSE.Views.FormatRulesEditDlg.textShortBar": "nejk<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.FormatRulesEditDlg.textShowBar": "Zobrazit pouze sloupce", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Zobrazit pouze ikony", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Tento odkaz nemůže být použit ve vzorci s podmíněným formátováním.<br>Změňte odkaz tak, aby odkazoval na jednu buňku, nebo vložte odkaz do funkce. Například: =СУММ(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Přeškrnuté", "SSE.Views.FormatRulesEditDlg.textSubscript": "Dolní index", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Horní index", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textUnderline": "Podtržené", "SSE.Views.FormatRulesEditDlg.tipBorders": "Ohraničení", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Účetnictví", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Měna", "SSE.Views.FormatRulesEditDlg.txtDate": "Datum", "SSE.Views.FormatRulesEditDlg.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.FormatRulesEditDlg.txtFraction": "Zlomek", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Obecné", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Žádná ikona", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Procento", "SSE.Views.FormatRulesEditDlg.txtScientific": "Vědecké", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Čas", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Upravit formá<PERSON><PERSON> pra<PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "<PERSON><PERSON> formátovací pra<PERSON>", "SSE.Views.FormatRulesManagerDlg.guestText": "Návštěvník", "SSE.Views.FormatRulesManagerDlg.lockText": "Uzamčeno", "SSE.Views.FormatRulesManagerDlg.text1Above": "Na 1 standardní odchylka výše průměru", "SSE.Views.FormatRulesManagerDlg.text1Below": "Na 1 standardní odchylka níže průměru", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 Směrodatná odchylka nad průměrem", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 Směrodatná odchylka pod průměrem", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 Směrodatná odchylka nad průměrem", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 Směrodatná odchylka pod průměrem", "SSE.Views.FormatRulesManagerDlg.textAbove": "Nadprůměrný", "SSE.Views.FormatRulesManagerDlg.textApply": "Uplatnit na", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Hodnota buňky začíná na", "SSE.Views.FormatRulesManagerDlg.textBelow": "Podprůměrné", "SSE.Views.FormatRulesManagerDlg.textBetween": "je z rozmezí {0} až {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Hodnota v buňce", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Gradační barevná škála", "SSE.Views.FormatRulesManagerDlg.textContains": "Hodnota buňky obsahuje", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Buňka obsahuje prázdnou hodnotu", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Chybný obsah buňky", "SSE.Views.FormatRulesManagerDlg.textDelete": "Odstranit", "SSE.Views.FormatRulesManagerDlg.textDown": "Přesunout pravidlo do<PERSON>ů", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplikovat hodnoty", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Hodnota buňky končí na", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Větší nebo rovno průměru", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Menší nebo rovno průměru", "SSE.Views.FormatRulesManagerDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textIconSet": "<PERSON><PERSON> <PERSON>kon", "SSE.Views.FormatRulesManagerDlg.textNew": "Nové", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nen<PERSON> z rozmezí {0} až {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Hodnota buňky neobsahuje", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Buňka neobsahuje prázdnou hodnotu", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Buňka neobsahuje chybu.", "SSE.Views.FormatRulesManagerDlg.textRules": "Pravid<PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Zobrazit pravidla formátování pro", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Vybrat data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Stávající výběr", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON> tabulka", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Tento list", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> ta<PERSON>", "SSE.Views.FormatRulesManagerDlg.textUnique": "Neopakující se hodnoty", "SSE.Views.FormatRulesManagerDlg.textUp": "Přesunout pravidlo nahoru", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Desetinn<PERSON>ch m<PERSON>t", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textLinked": "Odkázat na zdroj", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Symboly", "SSE.Views.FormatSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAccounting": "Účetní", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON> deseti<PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Měna", "SSE.Views.FormatSettingsDialog.txtCustom": "Uživatelsky určené", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Zadávejte prosím nestandartní číselný formát obezřetně. Tabulkový editor nekontroluje součtové hodnoty formátu na chyby, kter<PERSON> mohou ovlivnit soubor xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Datum", "SSE.Views.FormatSettingsDialog.txtFraction": "Zlomek", "SSE.Views.FormatSettingsDialog.txtGeneral": "Obecný", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Procento", "SSE.Views.FormatSettingsDialog.txtSample": "Ukázka:", "SSE.Views.FormatSettingsDialog.txtScientific": "Vědecké", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Čas", "SSE.Views.FormatSettingsDialog.txtUpto1": "Až na jednu číslici (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON>ž na dvě číslice (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON>ž na tři číslice (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "<PERSON><PERSON><PERSON><PERSON> sku<PERSON>u funkcí", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Doporučeno", "SSE.Views.FormulaDialog.txtSearch": "Hledat", "SSE.Views.FormulaDialog.txtTitle": "Vlož<PERSON>", "SSE.Views.FormulaTab.textAutomatic": "Automatické", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Spočítat nyní otevřený list", "SSE.Views.FormulaTab.textCalculateWorkbook": "Propočítat sešit", "SSE.Views.FormulaTab.textManual": "R<PERSON>č<PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "Spočítat", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Propočítat celý sešit", "SSE.Views.FormulaTab.txtAdditional": "Dalš<PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Automatický součet", "SSE.Views.FormulaTab.txtAutosumTip": "Su<PERSON>ce", "SSE.Views.FormulaTab.txtCalculation": "Výpočet", "SSE.Views.FormulaTab.txtFormula": "Funkce", "SSE.Views.FormulaTab.txtFormulaTip": "Vlož<PERSON>", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtRecent": "Nedávno p<PERSON>žité", "SSE.Views.FormulaWizard.textAny": "libovolné", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Funkce", "SSE.Views.FormulaWizard.textFunctionRes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textHelp": "Nápověda k této funkci", "SSE.Views.FormulaWizard.textLogical": "Logické", "SSE.Views.FormulaWizard.textNoArgs": "<PERSON><PERSON> neo<PERSON> argumenty", "SSE.Views.FormulaWizard.textNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textRef": "odkaz", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Argumenty funkce", "SSE.Views.FormulaWizard.textValue": "Výsledek vzorce", "SSE.Views.HeaderFooterDialog.textAlign": "Zarovnat vůči okrajům stránky", "SSE.Views.HeaderFooterDialog.textAll": "Všechny stránky", "SSE.Views.HeaderFooterDialog.textBold": "Tučn<PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON>va textu", "SSE.Views.HeaderFooterDialog.textDate": "Datum", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Odlišná první stránka", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Rozdílné liché a sudé str<PERSON>ky", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFirst": "První strán<PERSON>", "SSE.Views.HeaderFooterDialog.textFooter": "Zápatí", "SSE.Views.HeaderFooterDialog.textHeader": "Záhlaví", "SSE.Views.HeaderFooterDialog.textInsert": "Vložit", "SSE.Views.HeaderFooterDialog.textItalic": "Skloněné", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "Textov<PERSON>, k<PERSON><PERSON>, je p<PERSON><PERSON><PERSON>. Snižte počet použitých znaků.", "SSE.Views.HeaderFooterDialog.textNewColor": "Přidat novou vlastní barvu", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "Vpravo", "SSE.Views.HeaderFooterDialog.textScale": "Měnit velikost podle dokumentu", "SSE.Views.HeaderFooterDialog.textSheet": "Název listu", "SSE.Views.HeaderFooterDialog.textStrikeout": "Př<PERSON>š<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSubscript": "Dolní index", "SSE.Views.HeaderFooterDialog.textSuperscript": "Horní index", "SSE.Views.HeaderFooterDialog.textTime": "Čas", "SSE.Views.HeaderFooterDialog.textTitle": "Nastavení záhlaví/zápatí", "SSE.Views.HeaderFooterDialog.textUnderline": "Podtržené", "SSE.Views.HeaderFooterDialog.tipFontName": "Písmo", "SSE.Views.HeaderFooterDialog.tipFontSize": "Velikost písma", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Zobrazit", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Odkaz na", "SSE.Views.HyperlinkSettingsDialog.strRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "List", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Kopírovat", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Vybraný rozsah", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON> zadejte titulek", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Sem zadejte odkaz", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON> zadej<PERSON> popisek", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Externí odkaz", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Získat odkaz", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Vnitřní roz<PERSON> dat", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Vybrat data", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Listy", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Text rady", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Nastavení hypertextového odkazu", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> kolo<PERSON> by <PERSON><PERSON><PERSON> b<PERSON>t URL adresa ve formátu „http://www.example.com“", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Toto pole je omezeno na 2083 znaků", "SSE.Views.ImageSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ImageSettings.textCrop": "Oříznout", "SSE.Views.ImageSettings.textCropFill": "Výplň", "SSE.Views.ImageSettings.textCropFit": "Přizpůsobit", "SSE.Views.ImageSettings.textCropToShape": "Oříz<PERSON>ut podle tvaru", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Upravit objekt", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.ImageSettings.textFromStorage": "Z úložiště", "SSE.Views.ImageSettings.textFromUrl": "Z URL adresy", "SSE.Views.ImageSettings.textHeight": "Výška", "SSE.Views.ImageSettings.textHint270": "Otočit o 90° doleva", "SSE.Views.ImageSettings.textHint90": "Otočit o 90° doprava", "SSE.Views.ImageSettings.textHintFlipH": "Převrátit vodorovně", "SSE.Views.ImageSettings.textHintFlipV": "Př<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ImageSettings.textInsert": "Nahradit obrázek", "SSE.Views.ImageSettings.textKeepRatio": "Konst<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "Skutečná velikost", "SSE.Views.ImageSettings.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Views.ImageSettings.textRotate90": "Otočit o 90°", "SSE.Views.ImageSettings.textRotation": "Otočení", "SSE.Views.ImageSettings.textSize": "Velikost", "SSE.Views.ImageSettings.textWidth": "Šířka", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, grafu, obrazci nebo v tabulce.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "Úhel", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Převrá<PERSON>né", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Vodorovně", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.ImageSettingsAdvanced.textRotation": "Otočení", "SSE.Views.ImageSettingsAdvanced.textSnap": "Přichytá<PERSON><PERSON> b<PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Obrázek – pokročilá nastavení", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipAbout": "O aplikaci", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Z<PERSON>uv<PERSON><PERSON> moduly", "SSE.Views.LeftMenu.tipSearch": "Hledat", "SSE.Views.LeftMenu.tipSpellcheck": "Kontrola pra<PERSON>pisu", "SSE.Views.LeftMenu.tipSupport": "Zpětná vazba a technická podpora", "SSE.Views.LeftMenu.txtDeveloper": "REŽIM PRO VÝVOJÁŘE", "SSE.Views.LeftMenu.txtEditor": "Tabul<PERSON>ý editor", "SSE.Views.LeftMenu.txtLimit": "Omezit přístup", "SSE.Views.LeftMenu.txtTrial": "ZKUŠEBNÍ REŽIM", "SSE.Views.LeftMenu.txtTrialDev": "Zkušební vývojářský režim", "SSE.Views.MacroDialog.textMacro": "Název makra", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>ro", "SSE.Views.MainSettingsPrint.okButtonText": "Uložit", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Na šířku", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Na výšku", "SSE.Views.MainSettingsPrint.strPrint": "Tisk", "SSE.Views.MainSettingsPrint.strPrintTitles": "Tisk názvů", "SSE.Views.MainSettingsPrint.strRight": "Vpravo", "SSE.Views.MainSettingsPrint.strTop": "Nahoře", "SSE.Views.MainSettingsPrint.textActualSize": "Skutečná velikost", "SSE.Views.MainSettingsPrint.textCustom": "Uživatelsky určené", "SSE.Views.MainSettingsPrint.textCustomOptions": "Uživatelsky určené předvolby", "SSE.Views.MainSettingsPrint.textFitCols": "Přizpůsobit všechny sloupce na jedné stránce", "SSE.Views.MainSettingsPrint.textFitPage": "Přizpůsobit list jed<PERSON><PERSON> s<PERSON>", "SSE.Views.MainSettingsPrint.textFitRows": "Přizpůsobit všechny řádky na jedné stránce", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON> s<PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "Škálování", "SSE.Views.MainSettingsPrint.textPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.MainSettingsPrint.textPrintGrid": "Vytisknout mřížku", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Tisknout nadpisy řádků a buněk", "SSE.Views.MainSettingsPrint.textRepeat": "Opakovat...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.MainSettingsPrint.textRepeatTop": "Nahoře opak<PERSON>", "SSE.Views.MainSettingsPrint.textSettings": "Nastavení pro", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Stávající pojmenované rozsahy nelze upravovat a nové nyní nelze vytvořit<br>proto<PERSON><PERSON> některé z nich jsou právě upravovány.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Varování", "SSE.Views.NamedRangeEditDlg.strWorkbook": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.NamedRangeEditDlg.textExistName": "CHYBA! Rozsah se stejným názvem už existuje", "SSE.Views.NamedRangeEditDlg.textInvalidName": "<PERSON> třeba, aby název začínal písmenem nebo podtržítkem a neobsahoval neplatné zna<PERSON>.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.NamedRangeEditDlg.textIsLocked": "CHYBA! Prvek je upravován jiným uživatelem", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se p<PERSON><PERSON><PERSON><PERSON><PERSON>, je už uveden ve vzorcích buněk. Použijte nějaký jiný název.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Vybrat data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Upravi<PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nový název", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "Vložit název", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Návštěvník", "SSE.Views.NameManagerDlg.lockText": "Uzamčeno", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.NameManagerDlg.textDelete": "Vymazat", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Doposud nebyly vytvořeny žádné pojmenované roz<PERSON>hy.<br>Vytvořte alespoň jeden takový a objeví se v této kolonce.", "SSE.Views.NameManagerDlg.textFilter": "Filtr", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterSheet": "Rozsah názvů do listu", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Rozsah názvů do sešitu", "SSE.Views.NameManagerDlg.textNew": "Nový", "SSE.Views.NameManagerDlg.textnoNames": "Pro daný filtr se nepodařilo nalézt žádné pojmenované roz<PERSON>.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> názvů", "SSE.Views.NameManagerDlg.warnDelete": "Opravdu chcete název {0} smazat?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "Vpravo", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Nahoře", "SSE.Views.ParagraphSettings.strLineHeight": "Řádkování", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Rozestup odstavců", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON>a", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ParagraphSettings.textAt": "Výška", "SSE.Views.ParagraphSettings.textAtLeast": "Alespoň", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Přesně", "SSE.Views.ParagraphSettings.txtAutoText": "<PERSON>ky", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Zadané panely se objeví v této kolonce", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Všechno velkými", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Dvojité <PERSON>š<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Odsazení", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Řádkování", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Vpravo", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Po", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciální", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Od", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Písmo", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Odsazení a mezery", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Malá písmena", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Mezery", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Přeškrtnutí", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Dolní index", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Horní index", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulátory", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Zarovnání", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Vícero", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Mezery mezi písmeny", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Výchozí tabulátor", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efekty", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Přesně", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prvn<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Navazující", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Do bloku", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ž<PERSON><PERSON><PERSON>)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Odstranit", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Odstranit vše", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Vpravo", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Odstavec – pokročilá nastavení", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON>ky", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "rovná se", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "Nekončí na", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "obs<PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "mezi", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "ne mezi", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "nerovná se", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "je v<PERSON><PERSON><PERSON><PERSON> než", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "je vet<PERSON><PERSON> než nebo rovno", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "je men<PERSON><PERSON> ne<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "je men<PERSON><PERSON> než nebo rovno", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "začíná na", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "nezačíná na", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "končí na", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Zobrazit položky se štítkem:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Zobrazit položky pro které:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Použijte ? pro zastoupení libovolného znaku", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Použijte * pro zastoupení libovolné řady znaků", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "a", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtrování štítků", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtr hodnot", "SSE.Views.PivotGroupDialog.textAuto": "<PERSON>ky", "SSE.Views.PivotGroupDialog.textBy": "Od", "SSE.Views.PivotGroupDialog.textDays": "Dny", "SSE.Views.PivotGroupDialog.textEnd": "Končí na", "SSE.Views.PivotGroupDialog.textError": "<PERSON> třeba, aby obsahem této kolonky byla č<PERSON> hodnota", "SSE.Views.PivotGroupDialog.textGreaterError": "Číslo na konci musí být větší než číslo na začátku", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "Počet dnů", "SSE.Views.PivotGroupDialog.textQuart": "Čtvrtiny", "SSE.Views.PivotGroupDialog.textSec": "Sekundy", "SSE.Views.PivotGroupDialog.textStart": "Začínající v", "SSE.Views.PivotGroupDialog.textYear": "Roky", "SSE.Views.PivotGroupDialog.txtTitle": "Seskupování", "SSE.Views.PivotSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.PivotSettings.textFilters": "Filtry", "SSE.Views.PivotSettings.textRows": "Řádky", "SSE.Views.PivotSettings.textValues": "Hodnoty", "SSE.Views.PivotSettings.txtAddColumn": "Přidat do sloupců", "SSE.Views.PivotSettings.txtAddFilter": "Přidat do filtrů", "SSE.Views.PivotSettings.txtAddRow": "Přidat do řádků", "SSE.Views.PivotSettings.txtAddValues": "Přidat do hodnot", "SSE.Views.PivotSettings.txtFieldSettings": "Nastavení kolonky", "SSE.Views.PivotSettings.txtMoveBegin": "Přesunout na začátek", "SSE.Views.PivotSettings.txtMoveColumn": "Přesunout do sloupců", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveEnd": "Přesunout na konec", "SSE.Views.PivotSettings.txtMoveFilter": "Přesunout do filtrů", "SSE.Views.PivotSettings.txtMoveRow": "Přesunout do řádků", "SSE.Views.PivotSettings.txtMoveUp": "Př<PERSON>unout nahoru", "SSE.Views.PivotSettings.txtMoveValues": "Přesunout do hodnot", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.PivotSettingsAdvanced.strLayout": "Název a rozvržení", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, obrazci, grafu nebo v tabulce.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Zobrazit pole ve oblasti pro výkaz filtru", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON><PERSON>, pak příčně", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Celkov<PERSON> souč<PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Záhlaví kolonek", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pak dolů", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Vybrat data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Zobrazit pro sloupce", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Zobrazit záhlaví pro řádky a sloupce", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Zobrazit pro řádky", "SSE.Views.PivotSettingsAdvanced.textTitle": "Kontingenční tabul<PERSON> – pokročilé", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Počet polí filtru výkazu na sloupec ", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Počet polí filtru výkazu na řádek", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.PivotSettingsAdvanced.txtName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "Celkov<PERSON> souč<PERSON>", "SSE.Views.PivotTable.capLayout": "Rozvržení výkazu", "SSE.Views.PivotTable.capSubtotals": "Dílčí souč<PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Zobrazit pod každou ze skupin všechny dílčí součty", "SSE.Views.PivotTable.mniInsertBlankLine": "Za každou z položek vložit prázdný řádek", "SSE.Views.PivotTable.mniLayoutCompact": "Zobrazit ve zhuštěné podobě", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Neopakovat štítky všech položek", "SSE.Views.PivotTable.mniLayoutOutline": "Zobrazit jako obrys", "SSE.Views.PivotTable.mniLayoutRepeat": "Zopakovat všechny štítky položky", "SSE.Views.PivotTable.mniLayoutTabular": "Zobrazit v podobě tabulky", "SSE.Views.PivotTable.mniNoSubtotals": "Nezobrazovat dílčí součty", "SSE.Views.PivotTable.mniOffTotals": "Vypnuto <PERSON>dky a sloupce", "SSE.Views.PivotTable.mniOnColumnsTotals": "Zapnuto pouze pro sloupce", "SSE.Views.PivotTable.mniOnRowsTotals": "<PERSON>ap<PERSON><PERSON> p<PERSON>", "SSE.Views.PivotTable.mniOnTotals": "Zapnuto pro řádky a sloupce", "SSE.Views.PivotTable.mniRemoveBlankLine": "Odebrat prázdný řádek za každou z položek", "SSE.Views.PivotTable.mniTopSubtotals": "Zobrazit všechny dílčí součty nad skupinou", "SSE.Views.PivotTable.textColBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PivotTable.textColHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.tipCreatePivot": "vložit kontingenční tabulku", "SSE.Views.PivotTable.tipGrandTotals": "Zobrazit nebo skrýt celkové součty", "SSE.Views.PivotTable.tipRefresh": "Aktualizovat informace z datového zdroje", "SSE.Views.PivotTable.tipSelect": "Vybrat celou kontingenční tabulku", "SSE.Views.PivotTable.tipSubtotals": "Zobrazit nebo skrýt dílčí součty", "SSE.Views.PivotTable.txtCreate": "Vložit tabulku", "SSE.Views.PivotTable.txtPivotTable": "Kontingenční tabulka", "SSE.Views.PivotTable.txtRefresh": "Nač<PERSON><PERSON> znovu", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.btnDownload": "Uložit a stáhnout si", "SSE.Views.PrintSettings.btnPrint": "Uložit a vytisknout", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "Na šířku", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Na výšku", "SSE.Views.PrintSettings.strPrint": "Tisk", "SSE.Views.PrintSettings.strPrintTitles": "Tisk názvů", "SSE.Views.PrintSettings.strRight": "Vpravo", "SSE.Views.PrintSettings.strShow": "Zobrazit", "SSE.Views.PrintSettings.strTop": "Nahoře", "SSE.Views.PrintSettings.textActualSize": "Skutečná velikost", "SSE.Views.PrintSettings.textAllSheets": "Všechny listy", "SSE.Views.PrintSettings.textCurrentSheet": "Stávající list", "SSE.Views.PrintSettings.textCustom": "Uživatelsky určené", "SSE.Views.PrintSettings.textCustomOptions": "Uživatelsky určené předvolby", "SSE.Views.PrintSettings.textFitCols": "Přizpůsobit všechny sloupce na jedné stránce", "SSE.Views.PrintSettings.textFitPage": "Přizpůsobit list jed<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintSettings.textFitRows": "Přizpůsobit všechny řádky na jedné stránce", "SSE.Views.PrintSettings.textHideDetails": "<PERSON>k<PERSON><PERSON><PERSON> podro<PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorovat oblast tisku", "SSE.Views.PrintSettings.textLayout": "Rozvržení", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintSettings.textPageScaling": "Škálování", "SSE.Views.PrintSettings.textPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.PrintSettings.textPrintGrid": "Vytisknout mřížku", "SSE.Views.PrintSettings.textPrintHeadings": "Tisknout nadpisy řádků a buněk", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON><PERSON> tisku", "SSE.Views.PrintSettings.textRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRepeat": "Opakovat...", "SSE.Views.PrintSettings.textRepeatLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintSettings.textRepeatTop": "Nahoře opak<PERSON>", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Nastavení listu", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "Zobrazit č<PERSON>", "SSE.Views.PrintSettings.textShowHeadings": "Zobrazit záhlaví řádků a sloupců", "SSE.Views.PrintSettings.textTitle": "Nastavení tisku", "SSE.Views.PrintSettings.textTitlePDF": "Nastavení pro PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "První sloupec", "SSE.Views.PrintTitlesDialog.textFirstRow": "Prvn<PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.PrintTitlesDialog.textLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textRepeat": "Opakovat...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "Tisk názvů", "SSE.Views.PrintTitlesDialog.textTop": "Nahoře opak<PERSON>", "SSE.Views.PrintWithPreview.txtActualSize": "Skutečná velikost", "SSE.Views.PrintWithPreview.txtAllSheets": "Všechny listy", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Uplatnit na všechny listy", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Stávající list", "SSE.Views.PrintWithPreview.txtCustom": "Uživatelsky určené", "SSE.Views.PrintWithPreview.txtCustomOptions": "Uživatelsky určené předvolby", "SSE.Views.PrintWithPreview.txtEmptyTable": "Není co vytisknout, protože tabulka je prázdná.", "SSE.Views.PrintWithPreview.txtFitCols": "Přizpůsobit všechny sloupce na jedné stránce", "SSE.Views.PrintWithPreview.txtFitPage": "Přizpůsobit list jed<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintWithPreview.txtFitRows": "Přizpůsobit všechny řádky na jedné stránce", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Mřížky a nadpisy", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Nastavení záhlaví/zápatí", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorovat oblast tisku", "SSE.Views.PrintWithPreview.txtLandscape": "Na šířku", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "z {0}", "SSE.Views.PrintWithPreview.txtPage": "Strán<PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Neplat<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageOrientation": "<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintWithPreview.txtPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.PrintWithPreview.txtPortrait": "Na výšku", "SSE.Views.PrintWithPreview.txtPrint": "Tisk", "SSE.Views.PrintWithPreview.txtPrintGrid": "Vytisknout mřížku", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Vytisknout záhlaví řádků a sloupců", "SSE.Views.PrintWithPreview.txtPrintRange": "<PERSON><PERSON><PERSON><PERSON> tisku", "SSE.Views.PrintWithPreview.txtPrintTitles": "Tisk názvů", "SSE.Views.PrintWithPreview.txtRepeat": "<PERSON><PERSON><PERSON><PERSON>…", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Nahoře opak<PERSON>", "SSE.Views.PrintWithPreview.txtRight": "Vpravo", "SSE.Views.PrintWithPreview.txtSave": "Uložit", "SSE.Views.PrintWithPreview.txtScaling": "Změna <PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Nastavení listu", "SSE.Views.PrintWithPreview.txtSheet": "List: {0}", "SSE.Views.PrintWithPreview.txtTop": "Nahoře", "SSE.Views.ProtectDialog.textExistName": "CHYBA! Rozsah s takovým názvem už existuje", "SSE.Views.ProtectDialog.textInvalidName": "Název musí začínat písmenem a musí obsahovat pouze písmena, čísla nebo mezery.", "SSE.Views.ProtectDialog.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.ProtectDialog.textSelectData": "Vybrat data", "SSE.Views.ProtectDialog.txtAllow": "Umožnit všem uživatelům tohoto listu", "SSE.Views.ProtectDialog.txtAutofilter": "Použít <PERSON>", "SSE.Views.ProtectDialog.txtDelCols": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.ProtectDialog.txtFormatCells": "Formátovat buňky", "SSE.Views.ProtectDialog.txtFormatCols": "Formátovat sloupce", "SSE.Views.ProtectDialog.txtFormatRows": "Formátovat <PERSON>dky", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Zadání hesla a jeho potvrzení se neshodují", "SSE.Views.ProtectDialog.txtInsCols": "Vlož<PERSON> s<PERSON>", "SSE.Views.ProtectDialog.txtInsHyper": "Vložit hypertextový odkaz", "SSE.Views.ProtectDialog.txtInsRows": "Vlož<PERSON>", "SSE.Views.ProtectDialog.txtObjs": "Upravit objekty", "SSE.Views.ProtectDialog.txtOptional": "volitelné", "SSE.Views.ProtectDialog.txtPassword": "He<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Použít kontingenční tabulku a kontingenční graf", "SSE.Views.ProtectDialog.txtProtect": "Zabezpečení", "SSE.Views.ProtectDialog.txtRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "SSE.Views.ProtectDialog.txtScen": "Upravi<PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.ProtectDialog.txtSelUnLocked": "<PERSON>ybrat odemč<PERSON>é b<PERSON>", "SSE.Views.ProtectDialog.txtSheetDescription": "Pokud chcete zabránit nechtěným změnám ostatními, omezte jejich možnost upravovat.", "SSE.Views.ProtectDialog.txtSheetTitle": "Zabezpečit list", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Varování: Ztracené nebo zapomenuté heslo nelze obnovit. Uložte si ho na bezpečném místě.", "SSE.Views.ProtectDialog.txtWBDescription": "Můžete p<PERSON>žít he<PERSON>lo k zabránění zobrazení, <PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>, odstranění, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo přejmenování souboru jinými <PERSON>. ", "SSE.Views.ProtectDialog.txtWBTitle": "Zabezpečit strukturu sešitu", "SSE.Views.ProtectRangesDlg.guestText": "Návštěvník", "SSE.Views.ProtectRangesDlg.lockText": "Uzamčeno", "SSE.Views.ProtectRangesDlg.textDelete": "Odstranit", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Nejsou povoleny žádné rozsahy pro úpravy.", "SSE.Views.ProtectRangesDlg.textNew": "Nové", "SSE.Views.ProtectRangesDlg.textProtect": "Zabezpečit list", "SSE.Views.ProtectRangesDlg.textPwd": "He<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Rozsahy odemčení heslem když je list zabezpečen(toto funguje pouze pro uzamčené buňky)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nový rozsah", "SSE.Views.ProtectRangesDlg.txtNo": "Ne", "SSE.Views.ProtectRangesDlg.txtTitle": "Umožnit uživatelům upravovat rozsahy", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.warnDelete": "Opravdu chcete název {0} smazat?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Pro smazání duplicitn<PERSON>ch hodnot <PERSON> j<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>, k<PERSON><PERSON> obsahují duplicitní hodnoty.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Data mají <PERSON>", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Vybrat vše", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Views.RightMenu.txtCellSettings": "Nastavení b<PERSON>ň<PERSON>", "SSE.Views.RightMenu.txtChartSettings": "Nastavení grafu", "SSE.Views.RightMenu.txtImageSettings": "Nastavení obrázku", "SSE.Views.RightMenu.txtParagraphSettings": "Nastavení odstavce", "SSE.Views.RightMenu.txtPivotSettings": "Nastavení kontingenční ta<PERSON>", "SSE.Views.RightMenu.txtSettings": "Obecná nastavení", "SSE.Views.RightMenu.txtShapeSettings": "Nastavení o<PERSON>z<PERSON>ů", "SSE.Views.RightMenu.txtSignatureSettings": "Nastavení podpisu", "SSE.Views.RightMenu.txtSlicerSettings": "Nastavení průřezu", "SSE.Views.RightMenu.txtSparklineSettings": "Nastavení mikrografu", "SSE.Views.RightMenu.txtTableSettings": "Nastavení ta<PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Nastavení Text Art", "SSE.Views.ScaleDialog.textAuto": "<PERSON>ky", "SSE.Views.ScaleDialog.textError": "Zadaná hodnota není platná.", "SSE.Views.ScaleDialog.textFewPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Přizpůsobit vůči", "SSE.Views.ScaleDialog.textHeight": "Výška", "SSE.Views.ScaleDialog.textManyPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "Strán<PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Změnit měřítko na", "SSE.Views.ScaleDialog.textTitle": "Nastavení změny měřítka", "SSE.Views.ScaleDialog.textWidth": "Šířka", "SSE.Views.SetValueDialog.txtMaxText": "Nejvyšší možná hodnota v této kolonce je {0}", "SSE.Views.SetValueDialog.txtMinText": "Nejnižší možná hodnota v této kolonce je {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON>va p<PERSON>adí", "SSE.Views.ShapeSettings.strChange": "Zm<PERSON><PERSON><PERSON> o<PERSON>", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Výplň", "SSE.Views.ShapeSettings.strForeground": "<PERSON>va popředí", "SSE.Views.ShapeSettings.strPattern": "Vzor", "SSE.Views.ShapeSettings.strShadow": "Zobrazit stín", "SSE.Views.ShapeSettings.strSize": "Velikost", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Průhlednost", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ShapeSettings.textAngle": "Úhel", "SSE.Views.ShapeSettings.textBorderSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu z rozmezí 0 až 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Vyplnit barvou", "SSE.Views.ShapeSettings.textDirection": "Směr", "SSE.Views.ShapeSettings.textEmptyPattern": "Bez vzoru", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.ShapeSettings.textFromStorage": "Z úložiště", "SSE.Views.ShapeSettings.textFromUrl": "Z URL adresy", "SSE.Views.ShapeSettings.textGradient": "Stínování", "SSE.Views.ShapeSettings.textGradientFill": "Výplň přechodem", "SSE.Views.ShapeSettings.textHint270": "Otočit o 90° doleva", "SSE.Views.ShapeSettings.textHint90": "Otočit o 90° doprava", "SSE.Views.ShapeSettings.textHintFlipH": "Převrátit vodorovně", "SSE.Views.ShapeSettings.textHintFlipV": "Př<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ShapeSettings.textImageTexture": "Obrázek nebo textura", "SSE.Views.ShapeSettings.textLinear": "Lineární", "SSE.Views.ShapeSettings.textNoFill": "Bez výplně", "SSE.Views.ShapeSettings.textOriginalSize": "Původní velikost", "SSE.Views.ShapeSettings.textPatternFill": "Vzor", "SSE.Views.ShapeSettings.textPosition": "Pozice", "SSE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Views.ShapeSettings.textRotate90": "Otočit o 90°", "SSE.Views.ShapeSettings.textRotation": "Otočení", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON>y<PERSON><PERSON> o<PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Z textury", "SSE.Views.ShapeSettings.textTile": "D<PERSON>ž<PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "P<PERSON><PERSON>t stínování", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Odstranit stínování", "SSE.Views.ShapeSettings.txtBrownPaper": "Hnědý papír", "SSE.Views.ShapeSettings.txtCanvas": "Pl<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Tmavá tkanina", "SSE.Views.ShapeSettings.txtGrain": "Zrnitost", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Šedý papír", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Kůže", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "Dřevo", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Vnitřní odsazení textu", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, grafu, obrazci nebo v tabulce.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Úhel", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Velikost začátku", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Styl začátku", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Zkosení", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Typ zakončení", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Velikost konce", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON> konce", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>ý", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Převrá<PERSON>né", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Výška", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Vodorovně", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON>p spoje", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Konst<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Povolit přetékání textu přes obrazec", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Upravit velikost podle textu", "SSE.Views.ShapeSettingsAdvanced.textRight": "Vpravo", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Otočení", "SSE.Views.ShapeSettingsAdvanced.textRound": "Z<PERSON><PERSON>n<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Velikost", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Přichytá<PERSON><PERSON> b<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Vzdálenost mezi sloupci", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Čtverec", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON><PERSON> pole", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Obrazce – pokročilá nastavení", "SSE.Views.ShapeSettingsAdvanced.textTop": "Nahoře", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Tloušťka a šipky", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Šířka", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Varování", "SSE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDetails": "Podrobnosti podpisu", "SSE.Views.SignatureSettings.strInvalid": "Neplat<PERSON><PERSON>", "SSE.Views.SignatureSettings.strRequested": "Požadované pod<PERSON>", "SSE.Views.SignatureSettings.strSetup": "Nastavení podpisu", "SSE.Views.SignatureSettings.strSign": "Podpis", "SSE.Views.SignatureSettings.strSignature": "Podpis", "SSE.Views.SignatureSettings.strSigner": "Podepsal", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "Upravit i tak", "SSE.Views.SignatureSettings.txtEditWarning": "Upravením budou ze sešitu odebrány podpisy.<br>Opravdu chcete pokračovat?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Chcete tento podpis odstranit?<br><PERSON>to krok je nevrat<PERSON>. ", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Tento list je třeba podepsat.", "SSE.Views.SignatureSettings.txtSigned": "Do sešitu byly přidány platné podpisy. List je zabezpečen před <PERSON>.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Některé z digitálních podpisů v listu nejsou platné nebo je není možné ověřit. List je zabezpečen před úpravami.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Vložit průřezy", "SSE.Views.SlicerSettings.strHideNoData": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> data", "SSE.Views.SlicerSettings.strIndNoData": "Vizuáln<PERSON>, k<PERSON><PERSON> data", "SSE.Views.SlicerSettings.strShowDel": "Zobrazit smazané položky ze zdroje dat", "SSE.Views.SlicerSettings.strShowNoData": "jako poslední zobrazit položky bez dat ", "SSE.Views.SlicerSettings.strSorting": "Řazení a filtrování", "SSE.Views.SlicerSettings.textAdvanced": "Zobrazit pokročilé nastavení", "SSE.Views.SlicerSettings.textAsc": "Vzestupně", "SSE.Views.SlicerSettings.textAZ": "A po Z", "SSE.Views.SlicerSettings.textButtons": "Tlačítka", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "Sestupně", "SSE.Views.SlicerSettings.textHeight": "Výška", "SSE.Views.SlicerSettings.textHor": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON><PERSON> pom<PERSON> stran", "SSE.Views.SlicerSettings.textLargeSmall": "od nejv<PERSON><PERSON>š<PERSON>ch po nejmenší", "SSE.Views.SlicerSettings.textLock": "Vypnout možnost změny velikosti a přesunu", "SSE.Views.SlicerSettings.textNewOld": "od nejnovějš<PERSON>ch po nejstarší", "SSE.Views.SlicerSettings.textOldNew": "od nejstaršího po nejnovější", "SSE.Views.SlicerSettings.textPosition": "Pozice", "SSE.Views.SlicerSettings.textSize": "Velikost", "SSE.Views.SlicerSettings.textSmallLarge": "od nej<PERSON>š<PERSON>ch po největší", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "Svislé", "SSE.Views.SlicerSettings.textWidth": "Šířka", "SSE.Views.SlicerSettings.textZA": "Z po A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Tlačítka", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Výška", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Vizuáln<PERSON>, k<PERSON><PERSON> data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Od<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Zobrazit smazané položky ze zdroje dat", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Zobrazit záhlaví", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Jako poslední zobrazit položky bez dat ", "SSE.Views.SlicerSettingsAdvanced.strSize": "Velikost", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Řazení a filtrování", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Styl a velikost", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Šířka", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, obrazci, grafu nebo v tabulce.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Vzestupně", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A po Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Sestupně", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Název který použít ve vzorcích", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Záhlaví", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON> pom<PERSON> stran", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "od nejv<PERSON><PERSON>š<PERSON>ch po nejmenší", "SSE.Views.SlicerSettingsAdvanced.textName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "od nejnovějš<PERSON>ch po nejstarší", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "od nejstaršího po nejnovější", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "od nej<PERSON>š<PERSON>ch po největší", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Přichytá<PERSON><PERSON> b<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Název zdroje", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Průřez - Pokročilé nastavení", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z po A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.SortDialog.errorEmpty": "Je třeba, aby u všech kritérií řazení byl uveden sloupec nebo řádek.", "SSE.Views.SortDialog.errorMoreOneCol": "Je vybrán více než jeden sloupec.", "SSE.Views.SortDialog.errorMoreOneRow": "Je vybrán více než jeden <PERSON>.", "SSE.Views.SortDialog.errorNotOriginalCol": "Sloupec který jste vybrali se nenachází v původně vybraném rozsahu.", "SSE.Views.SortDialog.errorNotOriginalRow": "Řádek, který jste vybrali se nenachází v původně vybraném rozsahu.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 je řazeno dle stejné barvy více než jednou.<br>Smažte duplicitní kritérium řazení a zkuste to znovu.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 je řazeno dle hodnot více než jednou.<br>Smažte duplicitní kritérium řazení a zkuste to znovu.", "SSE.Views.SortDialog.textAdd": "Přida<PERSON>", "SSE.Views.SortDialog.textAsc": "Vzestupně", "SSE.Views.SortDialog.textAuto": "Automatické", "SSE.Views.SortDialog.textAZ": "A po Z", "SSE.Views.SortDialog.textBelow": "Pod", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> b<PERSON>", "SSE.Views.SortDialog.textColumn": "Sloupec", "SSE.Views.SortDialog.textCopy": "Zkopírovat úroveň", "SSE.Views.SortDialog.textDelete": "Smaza<PERSON>", "SSE.Views.SortDialog.textDesc": "Sestupně", "SSE.Views.SortDialog.textDown": "Přesunout o stupeň dolů", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON>", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON><PERSON> s<PERSON>)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON><PERSON>)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "Pořadí", "SSE.Views.SortDialog.textRight": "Vpravo", "SSE.Views.SortDialog.textRow": "Řádek", "SSE.Views.SortDialog.textSort": "Seřadit na", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textThenBy": "Následn<PERSON> podle", "SSE.Views.SortDialog.textTop": "Nahoře", "SSE.Views.SortDialog.textUp": "Přesunout o stupeň nahoru", "SSE.Views.SortDialog.textValues": "Hodnoty", "SSE.Views.SortDialog.textZA": "Z po A", "SSE.Views.SortDialog.txtInvalidRange": "Neplatný rozsah buněk.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "Vzestupně (A do Z) od", "SSE.Views.SortFilterDialog.textDesc": "Sestupně (od Z do A) dle", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortOptionsDialog.textCase": "Roz<PERSON>šovat malá a velká písmena", "SSE.Views.SortOptionsDialog.textHeaders": "Data mají <PERSON>", "SSE.Views.SortOptionsDialog.textLeftRight": "<PERSON><PERSON><PERSON><PERSON> zleva doprava", "SSE.Views.SortOptionsDialog.textOrientation": "Orientace", "SSE.Views.SortOptionsDialog.textTitle": "Předvolby řazení", "SSE.Views.SortOptionsDialog.textTopBottom": "Seřadit odshora dolů", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "Přeskočit prázdné", "SSE.Views.SpecialPasteDialog.textColWidth": "Šíř<PERSON> slou<PERSON>", "SSE.Views.SpecialPasteDialog.textComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Vzorce a formátování", "SSE.Views.SpecialPasteDialog.textFNFormat": "Vzorce a formáty čísla", "SSE.Views.SpecialPasteDialog.textFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFormulas": "Vzorce", "SSE.Views.SpecialPasteDialog.textFWidth": "Vzorce & šířka slou<PERSON>ců", "SSE.Views.SpecialPasteDialog.textMult": "Násobit", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operace", "SSE.Views.SpecialPasteDialog.textPaste": "Vložit", "SSE.Views.SpecialPasteDialog.textSub": "O<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "Vložit jinak", "SSE.Views.SpecialPasteDialog.textTranspose": "Přemístit", "SSE.Views.SpecialPasteDialog.textValues": "Hodnoty", "SSE.Views.SpecialPasteDialog.textVFormat": "Hodnoty a formátování", "SSE.Views.SpecialPasteDialog.textVNFormat": "<PERSON><PERSON><PERSON> hodnot a čísel", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.noSuggestions": "Žádná doporučení ohledně pravopisu", "SSE.Views.Spellcheck.textChange": "Změnit", "SSE.Views.Spellcheck.textChangeAll": "Změnit vše", "SSE.Views.Spellcheck.textIgnore": "Ignorovat", "SSE.Views.Spellcheck.textIgnoreAll": "Ignorovat vše", "SSE.Views.Spellcheck.txtAddToDictionary": "Přidat do slovníku", "SSE.Views.Spellcheck.txtClosePanel": "Zav<PERSON><PERSON><PERSON> h<PERSON>kování", "SSE.Views.Spellcheck.txtComplete": "Kontrola pravopisu dokončena", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Jazyk slovníku", "SSE.Views.Spellcheck.txtNextTip": "Jít na další slovo", "SSE.Views.Spellcheck.txtSpelling": "Hláskování", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(zkopírovat na konec)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(přesunout na konec)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Zkopírovat před list", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON><PERSON><PERSON> před list", "SSE.Views.Statusbar.filteredRecordsText": "<PERSON>lt<PERSON><PERSON><PERSON>: {0} z {1}", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON><PERSON> filt<PERSON>", "SSE.Views.Statusbar.itemAverage": "Průměrné", "SSE.Views.Statusbar.itemCopy": "Zkopírovat", "SSE.Views.Statusbar.itemCount": "Počet", "SSE.Views.Statusbar.itemDelete": "Vymazat", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "Skr<PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Vložit", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMove": "Př<PERSON>un", "SSE.Views.Statusbar.itemProtect": "Zabezpečení", "SSE.Views.Statusbar.itemRename": "Př<PERSON>menovat", "SSE.Views.Statusbar.itemStatus": "Status ukládání", "SSE.Views.Statusbar.itemSum": "SUMA", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Zrušit zabezpečení", "SSE.Views.Statusbar.RenameDialog.errNameExists": "List se stejným názvem již existuje", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Název listu nemůže obsahovat následující znaky: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Název listu", "SSE.Views.Statusbar.selectAllSheets": "Vybrat všechny listy", "SSE.Views.Statusbar.sheetIndexText": "List {0} z {1}", "SSE.Views.Statusbar.textAverage": "Průměrné", "SSE.Views.Statusbar.textCount": "Počet", "SSE.Views.Statusbar.textMax": "Maximum", "SSE.Views.Statusbar.textMin": "<PERSON>ejmé<PERSON>", "SSE.Views.Statusbar.textNewColor": "Přidat novou vlastní barvu", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON> barvy", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Přidat list", "SSE.Views.Statusbar.tipFirst": "Přejít na první list", "SSE.Views.Statusbar.tipLast": "Přejít na poslední list", "SSE.Views.Statusbar.tipListOfSheets": "Seznam listů", "SSE.Views.Statusbar.tipNext": "Posunout seznam list<PERSON> doprava", "SSE.Views.Statusbar.tipPrev": "Posunout seznam list<PERSON> doleva", "SSE.Views.Statusbar.tipZoomFactor": "Měří<PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "Přiblížit", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Zrušit seskupení listů", "SSE.Views.Statusbar.zoomText": "Přiblížení {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operaci nelze provést pro zvolený rozsah buněk.<br>Vyberte jednotnou oblast dat odlišnou od již existující a zkuste to znovu.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON> rozsah tak, aby první řádek tabulky byl na stejném řádku<br>a výsledná tabulka překrývala tu stávající.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> neobsahuje jin<PERSON>.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "V tabulkách nejsou dovoleny vzorce pro pole s vícero buňkami.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON> kolonk<PERSON> je třeba vyplnit", "SSE.Views.TableOptionsDialog.txtFormat": "Vytvořit tabulku", "SSE.Views.TableOptionsDialog.txtInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.TableOptionsDialog.txtNote": "Záhlaví musí zůstat na stejném řádku a výsledný rozsah tabulky musí překrývat původní rozsah.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteTableText": "Odstranit tabulku", "SSE.Views.TableSettings.insertColumnLeftText": "Vložit sloupec nalevo", "SSE.Views.TableSettings.insertColumnRightText": "Vložit sloupec napravo", "SSE.Views.TableSettings.insertRowAboveText": "Vložit řádek nad", "SSE.Views.TableSettings.insertRowBelowText": "Vložit ř<PERSON>dek pod", "SSE.Views.TableSettings.notcriticalErrorTitle": "Varování", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> cel<PERSON> sloupec", "SSE.Views.TableSettings.selectDataText": "Vybrat data sloupců", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON>ybrat tabulku", "SSE.Views.TableSettings.textActions": "Ak<PERSON> s tabulkou", "SSE.Views.TableSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Převést na rozsah", "SSE.Views.TableSettings.textEdit": "Řádky a sloupce", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textExistName": "CHYBA! Rozsah s takovým názvem už existuje", "SSE.Views.TableSettings.textFilter": "Tlačí<PERSON><PERSON> filt<PERSON>", "SSE.Views.TableSettings.textFirst": "První", "SSE.Views.TableSettings.textHeader": "Záhlaví", "SSE.Views.TableSettings.textInvalidName": "CHYBA! Neplatný název tabulky", "SSE.Views.TableSettings.textIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.TableSettings.textLast": "Poslední", "SSE.Views.TableSettings.textLongOperation": "Dlouhý provoz", "SSE.Views.TableSettings.textPivot": "vložit kontingenční tabulku", "SSE.Views.TableSettings.textRemDuplicates": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se p<PERSON><PERSON><PERSON><PERSON><PERSON>, je už uveden ve vzorcích buněk. Použijte nějaký jiný název.", "SSE.Views.TableSettings.textResize": "Velikost tabulky", "SSE.Views.TableSettings.textRows": "Řádky", "SSE.Views.TableSettings.textSelectData": "Vybrat data", "SSE.Views.TableSettings.textSlicer": "Vložit průřez", "SSE.Views.TableSettings.textTableName": "N<PERSON>zev tabulky", "SSE.Views.TableSettings.textTemplate": "Vybrat ze šablony", "SSE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.warnLongOperation": "Dokončení operace, kterou se chyst<PERSON>te prov<PERSON>t, by m<PERSON><PERSON> trvat opravdu dlouho.<br>Opravdu chcete pokračovat?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, obrazci, grafu nebo v tabulce.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabulka – pokročilá nastavení", "SSE.Views.TextArtSettings.strBackground": "<PERSON>va p<PERSON>adí", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Výplň", "SSE.Views.TextArtSettings.strForeground": "<PERSON>va popředí", "SSE.Views.TextArtSettings.strPattern": "Vzor", "SSE.Views.TextArtSettings.strSize": "Velikost", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Průhlednost", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "Úhel", "SSE.Views.TextArtSettings.textBorderSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu z rozmezí 0 až 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Vyplnit barvou", "SSE.Views.TextArtSettings.textDirection": "Směr", "SSE.Views.TextArtSettings.textEmptyPattern": "Bez vzoru", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.TextArtSettings.textFromUrl": "Z URL adresy", "SSE.Views.TextArtSettings.textGradient": "Stínování", "SSE.Views.TextArtSettings.textGradientFill": "Výplň přechodem", "SSE.Views.TextArtSettings.textImageTexture": "Obrázek nebo textura", "SSE.Views.TextArtSettings.textLinear": "Lineární", "SSE.Views.TextArtSettings.textNoFill": "Bez výplně", "SSE.Views.TextArtSettings.textPatternFill": "Vzor", "SSE.Views.TextArtSettings.textPosition": "Pozice", "SSE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Šablona", "SSE.Views.TextArtSettings.textTexture": "Z textury", "SSE.Views.TextArtSettings.textTile": "D<PERSON>ž<PERSON>", "SSE.Views.TextArtSettings.textTransform": "Transformovat", "SSE.Views.TextArtSettings.tipAddGradientPoint": "P<PERSON><PERSON>t stínování", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Odstranit stínování", "SSE.Views.TextArtSettings.txtBrownPaper": "Hnědý papír", "SSE.Views.TextArtSettings.txtCanvas": "Pl<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Tmavá tkanina", "SSE.Views.TextArtSettings.txtGrain": "Zrnitost", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Šedý papír", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Kůže", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "Dřevo", "SSE.Views.Toolbar.capBtnAddComment": "Přidat komentář", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Záhlaví a zápatí", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientace", "SSE.Views.Toolbar.capBtnPageSize": "Velikost", "SSE.Views.Toolbar.capBtnPrintArea": "Oblast tisku", "SSE.Views.Toolbar.capBtnPrintTitles": "Tisk názvů", "SSE.Views.Toolbar.capBtnScale": "Přizpůsobit měřítko, aby se vešlo", "SSE.Views.Toolbar.capImgAlign": "Zarovnání", "SSE.Views.Toolbar.capImgBackward": "Přesunout o vrstvu níž", "SSE.Views.Toolbar.capImgForward": "Přenést o vrstvu výš", "SSE.Views.Toolbar.capImgGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChart": "<PERSON>", "SSE.Views.Toolbar.capInsertEquation": "Rovnice", "SSE.Views.Toolbar.capInsertHyperlink": "Hypertextový odkaz", "SSE.Views.Toolbar.capInsertImage": "Obrázek", "SSE.Views.Toolbar.capInsertShape": "O<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertSpark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertTable": "Tabulka", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON> pole", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.mniImageFromFile": "Obrázek ze souboru", "SSE.Views.Toolbar.mniImageFromStorage": "Obrázek z úložiště", "SSE.Views.Toolbar.mniImageFromUrl": "Obrázek z URL adresy", "SSE.Views.Toolbar.textAddPrintArea": "Přidat do tisknutelné oblasti", "SSE.Views.Toolbar.textAlignBottom": "Zarovnat dolů", "SSE.Views.Toolbar.textAlignCenter": "Zarovnat na střed", "SSE.Views.Toolbar.textAlignJust": "Do bloku", "SSE.Views.Toolbar.textAlignLeft": "Zarovnat vlevo", "SSE.Views.Toolbar.textAlignMiddle": "Zarovnat na střed", "SSE.Views.Toolbar.textAlignRight": "Zarovnat vpravo", "SSE.Views.Toolbar.textAlignTop": "Zarovnat nahoru", "SSE.Views.Toolbar.textAllBorders": "Všechna ohraničení", "SSE.Views.Toolbar.textAuto": "<PERSON>ky", "SSE.Views.Toolbar.textAutoColor": "Automatické", "SSE.Views.Toolbar.textBold": "Tučně", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "<PERSON><PERSON>:", "SSE.Views.Toolbar.textBottomBorders": "Ohraničen<PERSON> dole", "SSE.Views.Toolbar.textCenterBorders": "Vnitřní svislé ohraničení", "SSE.Views.Toolbar.textClearPrintArea": "Vyčistit oblast tisku", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "SSE.Views.Toolbar.textClockwise": "Naklopit vpravo", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCounterCw": "Naklopit vlevo", "SSE.Views.Toolbar.textDataBars": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDelLeft": "Posunout buňky vlevo", "SSE.Views.Toolbar.textDelUp": "Posunout buňky nahoru", "SSE.Views.Toolbar.textDiagDownBorder": "Ohraničení úhlopříčně dolů", "SSE.Views.Toolbar.textDiagUpBorder": "Ohraničení <PERSON>hlopříčně nahoru", "SSE.Views.Toolbar.textDone": "Hotovo", "SSE.Views.Toolbar.textEditVA": "Upravit viditelnou oblast", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFewPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHeight": "Výška", "SSE.Views.Toolbar.textHideVA": "Skrýt viditelnou oblast", "SSE.Views.Toolbar.textHorizontal": "Vodorovný text", "SSE.Views.Toolbar.textInsDown": "Posunout buňky do<PERSON>ů", "SSE.Views.Toolbar.textInsideBorders": "Vložit ohraničení", "SSE.Views.Toolbar.textInsRight": "Posunout buňky vpravo", "SSE.Views.Toolbar.textItalic": "Skloněné", "SSE.Views.Toolbar.textItems": "Polož<PERSON>", "SSE.Views.Toolbar.textLandscape": "Na šířku", "SSE.Views.Toolbar.textLeft": "Vlevo:", "SSE.Views.Toolbar.textLeftBorders": "Ohraničení vlevo", "SSE.Views.Toolbar.textManageRule": "Spravovat pravidla", "SSE.Views.Toolbar.textManyPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Poslední uživatelsky určené", "SSE.Views.Toolbar.textMarginsNarrow": "Úzké", "SSE.Views.Toolbar.textMarginsNormal": "Normální", "SSE.Views.Toolbar.textMarginsWide": "Š<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Vnitřní vodorovné <PERSON>í", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNewColor": "Přidat novou vlastní barvu", "SSE.Views.Toolbar.textNewRule": "Nové pravidlo", "SSE.Views.Toolbar.textNoBorders": "<PERSON>z oh<PERSON>í", "SSE.Views.Toolbar.textOnePage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textOutBorders": "Vn<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "Uživatelsky určené okraje", "SSE.Views.Toolbar.textPortrait": "Na výšku", "SSE.Views.Toolbar.textPrint": "Tisk", "SSE.Views.Toolbar.textPrintGridlines": "Vytisknout mřížku", "SSE.Views.Toolbar.textPrintHeadings": "Tisk záhlaví", "SSE.Views.Toolbar.textPrintOptions": "Nastavení tisku", "SSE.Views.Toolbar.textRight": "Vpravo:", "SSE.Views.Toolbar.textRightBorders": "Ohraničení vpravo", "SSE.Views.Toolbar.textRotateDown": "Otočit text dolů", "SSE.Views.Toolbar.textRotateUp": "Otočit text nahoru", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Uživatelsky určené", "SSE.Views.Toolbar.textSelection": "<PERSON>d st<PERSON>jícího výběru", "SSE.Views.Toolbar.textSetPrintArea": "Nastavit oblast tisku", "SSE.Views.Toolbar.textShowVA": "Zobrazit viditelnou oblast", "SSE.Views.Toolbar.textStrikeout": "Př<PERSON>š<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubscript": "Dolní index", "SSE.Views.Toolbar.textSubSuperscript": "Dolní/horní index", "SSE.Views.Toolbar.textSuperscript": "Horní index", "SSE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabData": "Údaje", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Vzorec", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "Vložit", "SSE.Views.Toolbar.textTabLayout": "Rozvržení", "SSE.Views.Toolbar.textTabProtect": "Zabezpečení", "SSE.Views.Toolbar.textTabView": "Zobrazit", "SSE.Views.Toolbar.textThisPivot": "Z této kontingenční tabulky", "SSE.Views.Toolbar.textThisSheet": "Z tohoto listu", "SSE.Views.Toolbar.textThisTable": "Z této tabulky", "SSE.Views.Toolbar.textTop": "Nahoře:", "SSE.Views.Toolbar.textTopBorders": "Ohraničení nahoře", "SSE.Views.Toolbar.textUnderline": "Podtržení", "SSE.Views.Toolbar.textVertical": "Svislý text", "SSE.Views.Toolbar.textWidth": "Šířka", "SSE.Views.Toolbar.textZoom": "Přiblížit", "SSE.Views.Toolbar.tipAlignBottom": "Zarovnat dolů", "SSE.Views.Toolbar.tipAlignCenter": "Zarovnat na střed", "SSE.Views.Toolbar.tipAlignJust": "Do bloku", "SSE.Views.Toolbar.tipAlignLeft": "Zarovnat vlevo", "SSE.Views.Toolbar.tipAlignMiddle": "Zarovnat na střed", "SSE.Views.Toolbar.tipAlignRight": "Zarovnat vpravo", "SSE.Views.Toolbar.tipAlignTop": "Zarovnat nahoru", "SSE.Views.Toolbar.tipAutofilter": "Seřadit a filtrovat", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "Ohraničení", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeChart": "Změnit typ grafu", "SSE.Views.Toolbar.tipClearStyle": "Vymazat", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCondFormat": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopy": "Zkopírovat", "SSE.Views.Toolbar.tipCopyStyle": "Zkopírovat styl", "SSE.Views.Toolbar.tipCut": "Vyjmout", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON>de<PERSON><PERSON> desetinn<PERSON> m<PERSON>to", "SSE.Views.Toolbar.tipDecFont": "Zmenšit velikost písma", "SSE.Views.Toolbar.tipDeleteOpt": "Odstranit buňky", "SSE.Views.Toolbar.tipDigStyleAccounting": "Účetnický formát", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON><PERSON><PERSON> graf", "SSE.Views.Toolbar.tipEditChartData": "Vybrat data", "SSE.Views.Toolbar.tipEditChartType": "Změnit typ grafu", "SSE.Views.Toolbar.tipEditHeader": "Upravit záhlaví nebo zápatí", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "Font", "SSE.Views.Toolbar.tipFontSize": "Velikost písma", "SSE.Views.Toolbar.tipImgAlign": "Zarovnat objekty", "SSE.Views.Toolbar.tipImgGroup": "Seskupit objekty", "SSE.Views.Toolbar.tipIncDecimal": "Přidat desetinné m<PERSON>", "SSE.Views.Toolbar.tipIncFont": "Zvětšit velikost písma", "SSE.Views.Toolbar.tipInsertChart": "Vložit graf", "SSE.Views.Toolbar.tipInsertChartSpark": "Vložit graf", "SSE.Views.Toolbar.tipInsertEquation": "Vložit rovnici", "SSE.Views.Toolbar.tipInsertHyperlink": "Přidat hypertextový odkaz", "SSE.Views.Toolbar.tipInsertImage": "Vložit obrázek", "SSE.Views.Toolbar.tipInsertOpt": "Vložit buňky", "SSE.Views.Toolbar.tipInsertShape": "Vložit obrazec", "SSE.Views.Toolbar.tipInsertSlicer": "Vložit průřez", "SSE.Views.Toolbar.tipInsertSpark": "Vložit mikrograf", "SSE.Views.Toolbar.tipInsertSymbol": "Vložit symbol", "SSE.Views.Toolbar.tipInsertTable": "Vložit tabulku", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertTextart": "Vložit Text art", "SSE.Views.Toolbar.tipMerge": "Sloučit a vystředit", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.tipPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.Toolbar.tipPaste": "Vložit", "SSE.Views.Toolbar.tipPrColor": "Barva výplně", "SSE.Views.Toolbar.tipPrint": "Tisk", "SSE.Views.Toolbar.tipPrintArea": "Oblast tisku", "SSE.Views.Toolbar.tipPrintTitles": "Tisk názvů", "SSE.Views.Toolbar.tipRedo": "Znovu", "SSE.Views.Toolbar.tipSave": "Uložit", "SSE.Views.Toolbar.tipSaveCoauth": "Uložte změny, aby je viděli i ostatní uživatelé.", "SSE.Views.Toolbar.tipScale": "Přizpůsobit měřítko, aby se vešlo", "SSE.Views.Toolbar.tipSelectAll": "Vybrat vše", "SSE.Views.Toolbar.tipSendBackward": "Přesunout o vrstvu níž", "SSE.Views.Toolbar.tipSendForward": "Přenést o vrstvu výš", "SSE.Views.Toolbar.tipSynchronize": "Dokument byl mezitím pozměněn jiným uživatelem. Klikněte pro uložení vašich změn a načtení úprav.", "SSE.Views.Toolbar.tipTextFormatting": "<PERSON><PERSON><PERSON> n<PERSON>ů pro formátování textu", "SSE.Views.Toolbar.tipTextOrientation": "Orientace", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Svislé zarovnání", "SSE.Views.Toolbar.tipVisibleArea": "Viditelná oblast", "SSE.Views.Toolbar.tipWrap": "Zalamovat text", "SSE.Views.Toolbar.txtAccounting": "Účetnictví", "SSE.Views.Toolbar.txtAdditional": "Dalš<PERSON>", "SSE.Views.Toolbar.txtAscending": "Vzestupně", "SSE.Views.Toolbar.txtAutosumTip": "Su<PERSON>ce", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Vymazat filtr", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "Funkce", "SSE.Views.Toolbar.txtClearHyper": "Hypertextové odkazy", "SSE.Views.Toolbar.txtClearText": "Text", "SSE.Views.Toolbar.txtCurrency": "Měna", "SSE.Views.Toolbar.txtCustom": "Uživatelsky určené", "SSE.Views.Toolbar.txtDate": "Datum", "SSE.Views.Toolbar.txtDateTime": "Datum a čas", "SSE.Views.Toolbar.txtDescending": "Sestupně", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponenciální", "SSE.Views.Toolbar.txtFilter": "Filtr", "SSE.Views.Toolbar.txtFormula": "Vlož<PERSON>", "SSE.Views.Toolbar.txtFraction": "Zlomek", "SSE.Views.Toolbar.txtFranc": "CHF Švýcarský frank", "SSE.Views.Toolbar.txtGeneral": "Obecný", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> názvů", "SSE.Views.Toolbar.txtMergeAcross": "Sloučit v řádcích", "SSE.Views.Toolbar.txtMergeCells": "Sloučit buňky", "SSE.Views.Toolbar.txtMergeCenter": "Sloučit a vystředit", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>zev", "SSE.Views.Toolbar.txtNoBorders": "<PERSON>z oh<PERSON>í", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Vložit název", "SSE.Views.Toolbar.txtPercentage": "Procento", "SSE.Views.Toolbar.txtPound": "£ Libra", "SSE.Views.Toolbar.txtRouble": "₽ Rubl", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "Medián", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme15": "Původ", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Slunovrat", "SSE.Views.Toolbar.txtScheme18": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme19": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme20": "Městský", "SSE.Views.Toolbar.txtScheme21": "Elán", "SSE.Views.Toolbar.txtScheme22": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme3": "Vrchol", "SSE.Views.Toolbar.txtScheme4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme5": "Občanský", "SSE.Views.Toolbar.txtScheme6": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme7": "Rovnost", "SSE.Views.Toolbar.txtScheme8": "Tok", "SSE.Views.Toolbar.txtScheme9": "Slévárna", "SSE.Views.Toolbar.txtScientific": "Vědecké", "SSE.Views.Toolbar.txtSearch": "Hledat", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Seřadit vzestupně", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSpecial": "Speciální", "SSE.Views.Toolbar.txtTableTemplate": "Formátovat podle šablony tabulky", "SSE.Views.Toolbar.txtText": "Text", "SSE.Views.Toolbar.txtTime": "Čas", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtYen": "¥ Jen", "SSE.Views.Top10FilterDialog.textType": "Zobrazit", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "Od", "SSE.Views.Top10FilterDialog.txtItems": "Položka", "SSE.Views.Top10FilterDialog.txtPercent": "Procento", "SSE.Views.Top10FilterDialog.txtSum": "SUMA", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automatického filtru", "SSE.Views.Top10FilterDialog.txtTop": "Nahoře", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtry \"Top 10\"", "SSE.Views.ValueFieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> hodnot pole", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Průměrné", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Základní <PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 z %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Počet", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Spočítat buňky s čísly", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Vlast<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Rozdílné <PERSON>i", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Maximum", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Minimum", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Bez výpočtu", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Procento z", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Procenta rozdílná od", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Procento z celku", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Procento <PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Průběžný součet v", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Zobrazit hodnoty jako", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Název zdroje:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Směrodatná odchylka", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Populační směrodatná odchylka", "SSE.Views.ValueFieldSettingsDialog.txtSum": "SUMA", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnotu pole dle", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Návštěvník", "SSE.Views.ViewManagerDlg.lockText": "Uzamčeno", "SSE.Views.ViewManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "Žádné zobrazení nebyly prozatím vytvořeny.", "SSE.Views.ViewManagerDlg.textGoTo": "Přejít na zobrazení", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> má méně než 128 znaků.", "SSE.Views.ViewManagerDlg.textNew": "Nový", "SSE.Views.ViewManagerDlg.textRename": "Př<PERSON>menovat", "SSE.Views.ViewManagerDlg.textRenameError": "Je třeba vyplnit název zobrazení.", "SSE.Views.ViewManagerDlg.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pohled", "SSE.Views.ViewManagerDlg.textViews": "Zobrazení sešitu", "SSE.Views.ViewManagerDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.ViewManagerDlg.txtTitle": "Správce zobrazení listů", "SSE.Views.ViewManagerDlg.warnDeleteView": "Pokoušíte se smazat aktuálně zapnuté zobrazení'%1'.<br>Opravdu chcete toto zobrazení zavřít a smazat?", "SSE.Views.ViewTab.capBtnFreeze": "Ukotvit <PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "Zobrazení sešitu", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Vždy zobrazovat panel nástrojů", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Zkombinovat lišty listů a stavu", "SSE.Views.ViewTab.textCreate": "Nový", "SSE.Views.ViewTab.textDefault": "Výchozí", "SSE.Views.ViewTab.textFormula": "Lišta vzorce", "SSE.Views.ViewTab.textFreezeCol": "Ukotvit první sloupec", "SSE.Views.ViewTab.textFreezeRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "Nadpisy", "SSE.Views.ViewTab.textInterfaceTheme": "Vzhled uživatelského rozhraní", "SSE.Views.ViewTab.textManager": "Správce zobrazení", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Zobrazit stín ukotvených příček", "SSE.Views.ViewTab.textUnFreeze": "Zrušit ukotvení příček", "SSE.Views.ViewTab.textZeros": "Zobrazit nuly", "SSE.Views.ViewTab.textZoom": "Přiblížit", "SSE.Views.ViewTab.tipClose": "<PERSON>av<PERSON><PERSON><PERSON> n<PERSON> se<PERSON>itu", "SSE.Views.ViewTab.tipCreate": "Vytvořit nové zobrazení sešitu", "SSE.Views.ViewTab.tipFreeze": "Ukotvit <PERSON>", "SSE.Views.ViewTab.tipSheetView": "Zobrazení sešitu", "SSE.Views.WBProtection.hintAllowRanges": "Umožnit upravovat rozsahy", "SSE.Views.WBProtection.hintProtectSheet": "Zabezpečit list", "SSE.Views.WBProtection.hintProtectWB": "Zabezpečit sešit", "SSE.Views.WBProtection.txtAllowRanges": "Umožnit upravovat rozsahy", "SSE.Views.WBProtection.txtHiddenFormula": "Skryté vzorce", "SSE.Views.WBProtection.txtLockedCell": "Uzamčená buňka", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedText": "Uzamknout text", "SSE.Views.WBProtection.txtProtectSheet": "Zabezpečit list", "SSE.Views.WBProtection.txtProtectWB": "Zabezpečit sešit", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Zadejte heslo pro deaktivaci zabezpečení listu", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Zrušit zabezpečení listu", "SSE.Views.WBProtection.txtWBUnlockDescription": "Vložte heslo pro přístup k sešitu", "SSE.Views.WBProtection.txtWBUnlockTitle": "Zrušit zabezpečení sešitu"}