{"DATE": "DATA", "DATEDIF": "DATEDIF", "DATEVALUE": "DATA.VALOR", "DAY": "DIA", "DAYS": "DIAS", "DAYS360": "DIAS360", "EDATE": "DATAM", "EOMONTH": "FIMMÊS", "HOUR": "HORA", "ISOWEEKNUM": "NÚMSEMANAISO", "MINUTE": "MINUTO", "MONTH": "MÊS", "NETWORKDAYS": "DIATRABALHOTOTAL", "NETWORKDAYS.INTL": "DIATRABALHOTOTAL.INTL", "NOW": "AGORA", "SECOND": "SEGUNDO", "TIME": "TEMPO", "TIMEVALUE": "VALOR.TEMPO", "TODAY": "HOJE", "WEEKDAY": "DIA.DA.SEMANA", "WEEKNUM": "NÚMSEMANA", "WORKDAY": "DIATRABALHO", "WORKDAY.INTL": "DIATRABALHO.INTL", "YEAR": "ANO", "YEARFRAC": "FRAÇÃOANO", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BINADEC", "BIN2HEX": "BINAHEX", "BIN2OCT": "BINAOCT", "BITAND": "BITAND", "BITLSHIFT": "DESLOCESQBIT", "BITOR": "BITOR", "BITRSHIFT": "DESLOCDIRBIT", "BITXOR": "BITXOR", "COMPLEX": "COMPLEXO", "CONVERT": "CONVERTER", "DEC2BIN": "DECABIN", "DEC2HEX": "DECAHEX", "DEC2OCT": "DECAOCT", "DELTA": "DELTA", "ERF": "FUNERRO", "ERF.PRECISE": "FUNERRO.PRECISO", "ERFC": "FUNERROCOMPL", "ERFC.PRECISE": "FUNERROCOMPL.PRECISO", "GESTEP": "DEGRAU", "HEX2BIN": "HEXABIN", "HEX2DEC": "HEXADEC", "HEX2OCT": "HEXAOCT", "IMABS": "IMABS", "IMAGINARY": "IMAGINÁRIO", "IMARGUMENT": "IMARG", "IMCONJUGATE": "IMCONJ", "IMCOS": "IMCOS", "IMCOSH": "IMCOSH", "IMCOT": "IMCOT", "IMCSC": "IMCOSEC", "IMCSCH": "IMCOSECH", "IMDIV": "IMDIV", "IMEXP": "IMEXP", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMPOT", "IMPRODUCT": "IMPROD", "IMREAL": "IMREAL", "IMSEC": "IMSEC", "IMSECH": "IMSECH", "IMSIN": "IMSENO", "IMSINH": "IMSENH", "IMSQRT": "IMRAIZ", "IMSUB": "IMSUBTR", "IMSUM": "IMSOMA", "IMTAN": "IMTAN", "OCT2BIN": "OCTABIN", "OCT2DEC": "OCTADEC", "OCT2HEX": "OCTAHEX", "DAVERAGE": "BDMÉDIA", "DCOUNT": "BDCONTAR", "DCOUNTA": "BDCONTARA", "DGET": "BDEXTRAIR", "DMAX": "BDMÁX", "DMIN": "BDMÍN", "DPRODUCT": "BDMULTIPL", "DSTDEV": "BDEST", "DSTDEVP": "BDDESVPA", "DSUM": "BDSOMA", "DVAR": "BDVAREST", "DVARP": "BDVARP", "CHAR": "CARACT", "CLEAN": "TIRAR", "CODE": "CÓDIGO", "CONCATENATE": "CONCATENAR", "CONCAT": "CONCAT", "DOLLAR": "MOEDA", "EXACT": "EXATO", "FIND": "PROCURAR", "FINDB": "FINDB", "FIXED": "DEF.NÚM.DEC", "LEFT": "ESQUERDA", "LEFTB": "LEFTB", "LEN": "NÚM.CARACT", "LENB": "LENB", "LOWER": "MINÚSCULA", "MID": "EXT.TEXTO", "MIDB": "MIDB", "NUMBERVALUE": "VALORNUMÉRICO", "PROPER": "PRI.MAIÚSCULA", "REPLACE": "MUDAR", "REPLACEB": "REPLACEB", "REPT": "REPT", "RIGHT": "DIREITA", "RIGHTB": "RIGHTB", "SEARCH": "LOCALIZAR", "SEARCHB": "SEARCHB", "SUBSTITUTE": "SUBSTITUIR", "T": "T", "T.TEST": "TESTE.T", "TEXT": "TEXTO", "TEXTJOIN": "UNIRTEXTO", "TREND": "TENDÊNCIA", "TRIM": "ARRUMAR", "TRIMMEAN": "MÉDIA.INTERNA", "TTEST": "TESTET", "UNICHAR": "CARACTUNICODE", "UNICODE": "UNICODE", "UPPER": "MAIÚSCULA", "VALUE": "VALOR", "AVEDEV": "DESV.MÉDIO", "AVERAGE": "MÉDIA", "AVERAGEA": "MÉDIAA", "AVERAGEIF": "MÉDIASE", "AVERAGEIFS": "MÉDIASES", "BETADIST": "DISTBETA", "BETAINV": "BETA.ACUM.INV", "BETA.DIST": "DIST.BETA", "BETA.INV": "INV.BETA", "BINOMDIST": "DISTRBINOM", "BINOM.DIST": "DISTR.BINOM", "BINOM.DIST.RANGE": "INTERV.DISTR.BINOM", "BINOM.INV": "INV.BINOM", "CHIDIST": "DIST.QUI", "CHIINV": "INV.QUI", "CHITEST": "TESTE.QUI", "CHISQ.DIST": "DIST.QUIQUA", "CHISQ.DIST.RT": "DIST.QUIQUA.CD", "CHISQ.INV": "INV.QUIQUA", "CHISQ.INV.RT": "INV.QUIQUA.CD", "CHISQ.TEST": "TESTE.QUIQUA", "CONFIDENCE": "INT.CONFIANÇA", "CONFIDENCE.NORM": "INT.CONFIANÇA.NORM", "CONFIDENCE.T": "INT.CONFIANÇA.T", "CORREL": "CORREL", "COUNT": "CONT.NÚM", "COUNTA": "CONT.VALORES", "COUNTBLANK": "CONTAR.VAZIO", "COUNTIF": "CONT.SE", "COUNTIFS": "CONT.SES", "COVAR": "COVAR", "COVARIANCE.P": "COVARIAÇÃO.P", "COVARIANCE.S": "COVARIAÇÃO.S", "CRITBINOM": "CRIT.BINOM", "DEVSQ": "DESVQ", "EXPON.DIST": "DISTR.EXPON", "EXPONDIST": "DISTEXPON", "FDIST": "DISTF", "FINV": "INVF", "FTEST": "TESTEF", "F.DIST": "DIST.F", "F.DIST.RT": "DIST.F.CD", "F.INV": "INV.F", "F.INV.RT": "INV.F.CD", "F.TEST": "TESTE.F", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "PREVISÃO", "FORECAST.ETS": "PREVISÃO.ETS", "FORECAST.ETS.CONFINT": "PREVISÃO.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "PREVISÃO.ETS.SAZONALIDADE", "FORECAST.ETS.STAT": "PREVISÃO.ETS.STAT", "FORECAST.LINEAR": "PREVISÃO.LINEAR", "FREQUENCY": "FREQÜÊNCIA", "GAMMA": "GAMA", "GAMMADIST": "DISTGAMA", "GAMMA.DIST": "DIST.GAMA", "GAMMAINV": "INVGAMA", "GAMMA.INV": "INV.GAMA", "GAMMALN": "LNGAMA", "GAMMALN.PRECISE": "LNGAMA.PRECISO", "GAUSS": "GAUSS", "GEOMEAN": "MÉDIA.GEOMÉTRICA", "GROWTH": "CRESCIMENTO", "HARMEAN": "MÉDIA.HARMÔNICA", "HYPGEOM.DIST": "DIST.HIPERGEOM.N", "HYPGEOMDIST": "DIST.HIPERGEOM", "INTERCEPT": "INTERCEPÇÃO", "KURT": "CURT", "LARGE": "MAIOR", "LINEST": "PROJ.LIN", "LOGEST": "PROJ.LOG", "LOGINV": "INVLOG", "LOGNORM.DIST": "DIST.LOGNORMAL.N", "LOGNORM.INV": "INV.LOGNORMAL", "LOGNORMDIST": "DIST.LOGNORMAL", "MAX": "MÁXIMO", "MAXA": "MÁXIMOA", "MAXIFS": "MÁXIMOSES", "MEDIAN": "MED", "MIN": "MÍNIMO", "MINA": "MÍNIMOA", "MINIFS": "MÍNIMOSES", "MODE": "MODO", "MODE.MULT": "MODO.MULT", "MODE.SNGL": "MODO.ÚNICO", "NEGBINOM.DIST": "DIST.BIN.NEG.N", "NEGBINOMDIST": "DIST.BIN.NEG", "NORM.DIST": "DIST.NORM.N", "NORM.INV": "INV.NORM.N", "NORM.S.DIST": "DIST.NORMP.N", "NORM.S.INV": "INV.NORMP.N", "NORMDIST": "DISTNORM", "NORMINV": "INV.NORM", "NORMSDIST": "DISTNORMP", "NORMSINV": "INV.NORMP", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTIL", "PERCENTILE.EXC": "PERCENTIL.EXC", "PERCENTILE.INC": "PERCENTIL.INC", "PERCENTRANK": "ORDEM.PORCENTUAL", "PERCENTRANK.EXC": "ORDEM.PORCENTUAL.EXC", "PERCENTRANK.INC": "ORDEM.PORCENTUAL.INC", "PERMUT": "PERMUT", "PERMUTATIONA": "PERMUTAS", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "DIST.POISSON", "PROB": "PROB", "QUARTILE": "QUARTIL", "QUARTILE.INC": "QUARTIL.INC", "QUARTILE.EXC": "QUARTIL.EXC", "RANK.AVG": "ORDEM.MÉD", "RANK.EQ": "ORDEM.EQ", "RANK": "ORDEM", "RSQ": "RQUAD", "SKEW": "DISTORÇÃO", "SKEW.P": "DISTORÇÃO.P", "SLOPE": "INCLINAÇÃO", "SMALL": "MENOR", "STANDARDIZE": "PADRONIZAR", "STDEV": "DESVPAD", "STDEV.P": "DESVPAD.P", "STDEV.S": "DESVPAD.A", "STDEVA": "DESVPADA", "STDEVP": "DESVPADP", "STDEVPA": "DESVPADPA", "STEYX": "EPADYX", "TDIST": "DISTT", "TINV": "INVT", "T.DIST": "DIST.T", "T.DIST.2T": "DIST.T.BC", "T.DIST.RT": "DIST.T.CD", "T.INV": "INV.T", "T.INV.2T": "INV.T.BC", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.A", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "DIST.WEIBULL", "Z.TEST": "TESTE.Z", "ZTEST": "TESTEZ", "ACCRINT": "JUROSACUM", "ACCRINTM": "JUROSACUMV", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "CUPDIASINLIQ", "COUPDAYS": "CUPDIAS", "COUPDAYSNC": "CUPDIASPRÓX", "COUPNCD": "CUPDATAPRÓX", "COUPNUM": "CUPNÚM", "COUPPCD": "CUPDATAANT", "CUMIPMT": "PGTOJURACUM", "CUMPRINC": "PGTOCAPACUM", "DB": "BD", "DDB": "BDD", "DISC": "DESC", "DOLLARDE": "MOEDADEC", "DOLLARFR": "MOEDAFRA", "DURATION": "DURAÇÃO", "EFFECT": "EFETIVA", "FV": "VF", "FVSCHEDULE": "VFPLANO", "INTRATE": "TAXAJUROS", "IPMT": "IPGTO", "IRR": "TIR", "ISPMT": "ÉPGTO", "MDURATION": "MDURAÇÃO", "MIRR": "MTIR", "NOMINAL": "NOMINAL", "NPER": "NPER", "NPV": "VPL", "ODDFPRICE": "PREÇOPRIMINC", "ODDFYIELD": "LUCROPRIMINC", "ODDLPRICE": "PREÇOÚLTINC", "ODDLYIELD": "LUCROÚLTINC", "PDURATION": "DURAÇÃOP", "PMT": "PGTO", "PPMT": "PPGTO", "PRICE": "PREÇO", "PRICEDISC": "PREÇODESC", "PRICEMAT": "PREÇOVENC", "PV": "VP", "RATE": "TAXA", "RECEIVED": "RECEBER", "RRI": "TAXAJURO", "SLN": "DPD", "SYD": "SDA", "TBILLEQ": "OTN", "TBILLPRICE": "OTNVALOR", "TBILLYIELD": "OTNLUCRO", "VDB": "BDV", "XIRR": "XTIR", "XNPV": "XVPL", "YIELD": "LUCRO", "YIELDDISC": "LUCRODESC", "YIELDMAT": "LUCROVENC", "ABS": "ABS", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "AGREGAR", "ARABIC": "ARÁBICO", "ASC": "ASC", "ASIN": "ASEN", "ASINH": "ASENH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "BASE", "CEILING": "TETO", "CEILING.MATH": "TETO.MAT", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "COMBIN", "COMBINA": "COMBINA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "COSEC", "CSCH": "COSECH", "DECIMAL": "DECIMAL", "DEGREES": "GRAUS", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "PAR", "EXP": "EXP", "FACT": "FATORIAL", "FACTDOUBLE": "FATDUPLO", "FLOOR": "ARREDMULTB", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "ARREDMULTB.MAT", "GCD": "MDC", "INT": "INT", "ISO.CEILING": "ISO.CEILING", "LCM": "MMC", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MATRIZ.DETERM", "MINVERSE": "MATRIZ.INVERSO", "MMULT": "MATRIZ.MULT", "MOD": "MOD", "MROUND": "MARRED", "MULTINOMIAL": "MULTINOMIAL", "MUNIT": "MUNIT", "ODD": "ÍMPAR", "PI": "PI", "POWER": "POTÊNCIA", "PRODUCT": "MULT", "QUOTIENT": "QUOCIENTE", "RADIANS": "RADIANOS", "RAND": "ALEATÓRIO", "RANDARRAY": "MATRIZALEATÓRIA", "RANDBETWEEN": "ALEATÓRIOENTRE", "ROMAN": "ROMANO", "ROUND": "ARRED", "ROUNDDOWN": "ARREDONDAR.PARA.BAIXO", "ROUNDUP": "ARREDONDAR.PARA.CIMA", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SOMASEQÜÊNCIA", "SIGN": "SINAL", "SIN": "SEN", "SINH": "SENH", "SQRT": "RAIZ", "SQRTPI": "RAIZPI", "SUBTOTAL": "SUBTOTAL", "SUM": "SOMA", "SUMIF": "SOMASE", "SUMIFS": "SOMASES", "SUMPRODUCT": "SOMARPRODUTO", "SUMSQ": "SOMAQUAD", "SUMX2MY2": "SOMAX2DY2", "SUMX2PY2": "SOMAX2SY2", "SUMXMY2": "SOMAXMY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "TRUNCAR", "ADDRESS": "ENDEREÇO", "CHOOSE": "ESCOLHER", "COLUMN": "COL", "COLUMNS": "COLS", "FORMULATEXT": "FÓRMULATEXTO", "HLOOKUP": "PROCH", "HYPERLINK": "HIPERLINK", "INDEX": "ÍNDICE", "INDIRECT": "INDIRETO", "LOOKUP": "PROC", "MATCH": "CORRESP", "OFFSET": "DESLOC", "ROW": "LIN", "ROWS": "LINS", "TRANSPOSE": "TRANSPOR", "UNIQUE": "ÚNICO", "VLOOKUP": "PROCV", "XLOOKUP": "PROCX", "CELL": "CELL", "ERROR.TYPE": "TIPO.ERRO", "ISBLANK": "ÉCÉL.VAZIA", "ISERR": "ÉERRO", "ISERROR": "ÉERROS", "ISEVEN": "ÉPAR", "ISFORMULA": "ÉFÓRMULA", "ISLOGICAL": "ÉLÓGICO", "ISNA": "É.NÃO.DISP", "ISNONTEXT": "É.NÃO.TEXTO", "ISNUMBER": "ÉNÚM", "ISODD": "ÉIMPAR", "ISREF": "ÉREF", "ISTEXT": "ÉTEXTO", "N": "N", "NA": "NÃO.DISP", "SHEET": "PLAN", "SHEETS": "PLANS", "TYPE": "TIPO", "AND": "E", "FALSE": "FALSO", "IF": "SE", "IFS": "SES", "IFERROR": "SEERRO", "IFNA": "SENÃODISP", "NOT": "NÃO", "OR": "OU", "SWITCH": "PARÂMETRO", "TRUE": "VERDADEIRO", "XOR": "XOR", "TEXTBEFORE": "TEXTOANTES", "TEXTAFTER": "TEXTODEPOIS", "TEXTSPLIT": "DIVIDIRTEXTO", "WRAPROWS": "QUEBRARLINS", "VSTACK": "EMPILHARV", "HSTACK": "HSTACK", "CHOOSEROWS": "ESCOLHERLINS", "CHOOSECOLS": "ESCOLHERCOLS", "TOCOL": "PARACOL", "TOROW": "PARALIN", "WRAPCOLS": "QUEBRARCOLS", "TAKE": "PEGAR", "DROP": "DESCARTAR", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}