﻿<!DOCTYPE html>
<html>
	<head>
		<title>MAXA-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>MAXA-Funktion</h1>
			<p>Die Funktion <b>MAXA</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den größten Wert in einer Liste mit Argumenten zurück. Dazu zählen Zahlen, Text und Wahrheitswerte</p>
			<p>Die Formelsyntax der Funktion <b>MAXA</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>MAXA(Zahl1;[Zahl2];...)</em></b></p> 
			<p>Die Argumenteliste <b><em>Zahl1;[Zahl2];...</em></b> kann Zahlen, Text und Wahrheitswerte enthalten, die manuell eingegeben werden oder in den Zellbereich eingeschlossen sind, auf den Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>MAXA</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>MAXA</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="MAXA-Funktion" src="../images/maxa.png" /></p>
		</div>
	</body>
</html>