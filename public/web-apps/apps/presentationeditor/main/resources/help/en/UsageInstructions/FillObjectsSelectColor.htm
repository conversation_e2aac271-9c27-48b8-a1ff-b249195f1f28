﻿<!DOCTYPE html>
<html>
	<head>
		<title>Fill objects and select colors</title>
		<meta charset="utf-8" />
		<meta name="description" content="Fill the added objects with color, picture or texture, select colors for the slide background, autoshape fill and stroke, font." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Fill objects and select colors</h1>
			<p>In the <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a>, you can apply different fills for the slide, autoshape and Text Art font background.</p>
			<ol>
			<li>Select an object
                <ul>
                    <li>To change the slide background fill, select the necessary slides in the slide list. The <div class = "icon icon-slide_settings_icon"></div> <b>Slide settings</b> tab will be activated on the right sidebar.</li>
                    <li>To change the autoshape fill, left-click the necessary autoshape. The <div class = "icon icon-shape_settings_icon"></div> <b>Shape settings</b> tab will be activated on the right sidebar.</li>
                    <li>To change the Text Art font fill, left-click the necessary text object. The <div class = "icon icon-textart_settings_icon"></div> <b>Text Art settings</b> tab will be activated on the right sidebar.</li>
                </ul>
			</li>
			<li>Set the necessary fill type</li>
			<li>Adjust the selected fill properties (see the detailed description below for each fill type)
			<p class="note">For the autoshapes and Text Art font, regardless of the selected fill type, you can also set an <b>Opacity</b> level by dragging the slider or entering the percent value manually. The default value is <b>100%</b>. It corresponds to the full opacity. The <b>0%</b> value corresponds to the full transparency.</p>
			</li>
			</ol>			
			<p><b>The following fill types are available:</b></p>
			<ul>
				<li><b>Color Fill</b> - select this option to specify the solid color to fill the inner space of the selected shape/slide.
				<p><img alt="Color Fill" src="../images/fill_color.png" /></p>
				<p>Click on the colored box below and select the necessary color from the available color sets or specify any color you like:</p>
			        <p><img alt="Palettes" src="../images/palettes.png" /></p>
			        <ul>
			        <li><b>Theme Colors</b> - the colors that correspond to the selected theme/color scheme of the presentation. Once you apply a different theme or color scheme, the <b>Theme Colors</b> set will change.</li>
			        <li><b>Standard Colors</b> - the default colors set.</li>
			        <li><b>Custom Color</b> - click on this caption if there is no needed color in the available palettes. Select the necessary color range by moving the vertical color slider and set the specific color by dragging the color picker within the large square color field. Once you select a color with the color picker, the appropriate RGB and sRGB color values will be displayed in the fields on the right. You can also specify a color on the base of the RGB color model by entering the necessary numeric values into the <b>R</b>, <b>G</b>, <b>B</b> (Red, Green, Blue) fields or enter the sRGB hexadecimal code into the field marked with the <b>#</b> sign. The selected color will appear in the <b>New</b> preview box. If the object was previously filled with any custom color, this color is displayed in the <b>Current</b> box so you can compare the original and modified colors. When the color is specified, click the <b>Add</b> button:
			        <p><img alt="Palette - Custom Color" src="../../../../../../common/main/resources/help/en/images/palette_custom.png" /></p>
			        <p>The custom color will be applied to your object and added to the <b>Custom color</b> palette of the menu.</p>			        
			        </li>			        
			        </ul>
			    <p class="note">You can use the same color types when selecting the <b>color of the autoshape stroke</b>, adjusting the <b>font color</b>, or changing the <b>table background or border color</b>.</p>
				</li>
			</ul>
				<hr />
			<ul>				
				<li>
					<b>Gradient Fill</b> - select this option to fill the slide/shape with two colors which smoothly change from one to another. Click the <div class = "icon icon-shape_settings_icon"></div> <b>Shape settings</b> icon to open the <b>Fill</b> menu:
					<p><img class="floatleft" alt="Gradient Fill" src="../images/fill_gradient.png" /></p>
					<ul style="margin-left: 280px;">
						<li><b>Style</b> - choose one of the available options: <b>Linear</b> (colors change in a straight line i.e. along a horizontal/vertical axis or diagonally at a 45 degree angle) or <b>Radial</b> (colors change in a circular path from the center to the edges).</li>
						<li><b>Direction</b> - the direction preview window displays the selected gradient color, click the arrow to choose a template from the menu. If the <b>Linear</b> gradient is selected, the following directions are available : top-left to bottom-right, top to bottom, top-right to bottom-left, right to left, bottom-right to top-left, bottom to top, bottom-left to top-right, left to right. If the <b>Radial</b> gradient is selected, only one template is available.</li>
						<li><b>Angle</b> - set the numeric value for a precise color transition angle.</li>
						<li><b>Gradient Points</b> are specific points of color transition.
						<ol>
							<li>Use the <b>Add Gradient Point</b> button or a slider bar to add a gradient point, and the <b>Remove Gradient Point</b> button to delete one. You can add up to 10 gradient points. Each of the following gradient points added does not affect the current gradient appearance.</li>
							<li>Use the slider bar to change the location of the gradient point or specify the <b>Position</b> in percentage for a precise location.</li>
							<li>To apply a color to the gradient point, click on the required point on the slider bar, and then click <b>Color</b> to choose the color you want.</li>
							</ol>
						</li>
					</ul>
				</li>
			</ul>
				<hr />
			<ul>
				<li><b>Picture or Texture</b> - select this option to use an image or a predefined texture as the shape/slide background.
				<p><img class="floatleft"alt="Picture or Texture Fill" src="../images/fill_picture.png" /></p>
				    <ul style="margin-left: 280px;">
						<li>
							If you wish to use an image as a background for the shape/slide, click the <b>Select Picture</b> button and add an image <b>From File</b> by selecting it on your computer hard disc drive, <b>From URL</b> by inserting the appropriate URL address into the opened window, or <b>From Storage</b> by selecting the required image stored on your portal.
						</li>
				    <li>If you wish to use a texture as a background for the shape/slide, drop-down the <b>From Texture</b> menu and select the necessary texture preset.
				    <p>Currently, the following textures are available: Canvas, Carton, Dark Fabric, Grain, Granite, Grey Paper, Knit, Leather, Brown Paper, Papyrus, Wood.</p>
				    </li>
				    </ul>
			        <ul style="margin-left: 280px;">
			        <li>In case the selected <b>Picture</b> has less or more dimensions than the autoshape or slide has, you can choose the <b>Stretch</b> or <b>Tile</b> setting from the drop-down list.
			        <p>The <b>Stretch</b> option allows you to adjust the image size to fit the slide or autoshape size so that it could fill the space completely.</p>
			        <p>The <b>Tile</b> option allows you to display only a part of the bigger image keeping its original dimensions, or repeat the smaller image keeping its original dimensions over the slide or autoshape surface so that it could fill the space completely.</p>
			        <p class="note">Any selected <b>Texture</b> preset fills the space completely, but you can apply the <b>Stretch</b> effect if necessary.</p>
			        </li>
			        </ul>			
				</li>
			</ul>
				<hr />
			<ul>
				<li><b>Pattern</b> - select this option to fill the slide/shape with a two-colored design composed of regularly repeated elements.
				<p><img class="floatleft"alt="Pattern Fill" src="../images/fill_pattern.png" /></p>
				    <ul style="margin-left: 280px;">
				        <li><b>Pattern</b> - select one of the predefined designs from the menu.</li>
				        <li><b>Foreground color</b> - click this color box to change the color of the pattern elements.</li>
				        <li><b>Background color</b> - click this color box to change the color of the pattern background.</li>
				    </ul>			
				</li>
			</ul>
				<hr />
			<ul>
				<li><b>No Fill</b> - select this option if you don't want to use any fill.</li>
			</ul>
		</div>
	</body>
</html>