{"About": {"textAbout": "About", "textAddress": "Address", "textBack": "Back", "textEmail": "Email", "textPoweredBy": "Powered By", "textTel": "Tel", "textVersion": "Version", "textEditor": "Presentation Editor"}, "Common": {"Collaboration": {"notcriticalErrorTitle": "Warning", "textAddComment": "Add Comment", "textAddReply": "Add Reply", "textBack": "Back", "textCancel": "Cancel", "textCollaboration": "Collaboration", "textComments": "Comments", "textDeleteComment": "Delete Comment", "textDeleteReply": "Delete Reply", "textDone": "Done", "textEdit": "Edit", "textEditComment": "Edit Comment", "textEditReply": "Edit Reply", "textEditUser": "Users who are editing the file:", "textMessageDeleteComment": "Do you really want to delete this comment?", "textMessageDeleteReply": "Do you really want to delete this reply?", "textNoComments": "This document doesn't contain comments", "textReopen": "Reopen", "textResolve": "Resolve", "textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "textUsers": "Users", "textOk": "Ok", "textSharingSettings": "Sharing Settings"}, "ThemeColorPalette": {"textCustomColors": "Custom Colors", "textStandartColors": "Standard Colors", "textThemeColors": "Theme Colors"}, "HighlightColorPalette": {"textNoFill": "No Fill"}}, "ContextMenu": {"errorCopyCutPaste": "Copy, cut and paste actions using the context menu will be performed within the current file only.", "menuAddComment": "Add Comment", "menuAddLink": "Add Link", "menuCancel": "Cancel", "menuDelete": "Delete", "menuDeleteTable": "Delete Table", "menuEdit": "Edit", "menuMerge": "<PERSON><PERSON>", "menuMore": "More", "menuOpenLink": "Open Link", "menuSplit": "Split", "menuViewComment": "View Comment", "textColumns": "Columns", "textCopyCutPasteActions": "Copy, Cut and Paste Actions", "textDoNotShowAgain": "Don't show again", "textRows": "Rows", "textOk": "Ok", "txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "menuEditLink": "Edit Link"}, "Controller": {"Main": {"advDRMOptions": "Protected File", "advDRMPassword": "Password", "closeButtonText": "Close File", "criticalErrorTitle": "Error", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your admin.", "errorOpensource": "Using the free Community version, you can open documents for viewing only. To access mobile web editors, a commercial license is required.", "errorProcessSaveResult": "Saving failed.", "errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "leavePageText": "You have unsaved changes in this document. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "notcriticalErrorTitle": "Warning", "SDK": {"Chart": "Chart", "Click to add first slide": "Click to add first slide", "Click to add notes": "Click to add notes", "ClipArt": "<PERSON><PERSON>", "Date and time": "Date and time", "Diagram": "Diagram", "Diagram Title": "Chart Title", "Footer": "Footer", "Header": "Header", "Image": "Image", "Loading": "Loading", "Media": "Media", "None": "None", "Picture": "Picture", "Series": "Series", "Slide number": "Slide number", "Slide subtitle": "Slide subtitle", "Slide text": "Slide text", "Slide title": "Slide title", "Table": "Table", "X Axis": "X Axis XAS", "Y Axis": "Y Axis", "Your text here": "Your text here"}, "textAnonymous": "Anonymous", "textBuyNow": "Visit website", "textClose": "Close", "textContactUs": "Contact sales", "textCustomLoader": "Sorry, you are not entitled to change the loader. Please, contact our sales department to get a quote.", "textGuest": "Guest", "textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "textNo": "No", "textNoLicenseTitle": "License limit reached", "textOpenFile": "Enter a password to open the file", "textPaidFeature": "Paid feature", "textRemember": "Remember my choice", "textYes": "Yes", "titleLicenseExp": "License expired", "titleServerVersion": "Editor updated", "titleUpdateVersion": "Version changed", "txtIncorrectPwd": "Password is incorrect", "txtProtected": "Once you enter the password and open the file, the current password to the file will be reset", "warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact your administrator to learn more.", "warnLicenseExp": "Your license has expired. Please, update it and refresh the page.", "warnLicenseLimitedNoAccess": "License expired. You have no access to document editing functionality. Please, contact your administrator.", "warnLicenseLimitedRenewed": "The license needs to be renewed. You have limited access to document editing functionality.<br>Please contact your administrator to get full access", "warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact %1 sales team for personal upgrade terms.", "warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "warnProcessRightsChange": "You don't have permission to edit the file.", "textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "textNoTextFound": "Text not found", "textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?"}}, "Error": {"convertationTimeoutText": "Conversion timeout exceeded.", "criticalErrorExtText": "Press 'OK' to go back to the document list.", "criticalErrorTitle": "Error", "downloadErrorText": "Download failed.", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your admin.", "errorBadImageUrl": "Image url is incorrect", "errorConnectToServer": "Can't save this doc. Check your connection settings or contact your admin.<br>When you click the 'OK' button, you will be prompted to download the document.", "errorDatabaseConnection": "External error.<br>Database connection error. Please, contact support.", "errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "errorDataRange": "Incorrect data range.", "errorDefaultMessage": "Error code: %1", "errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download' option to save the file backup copy locally.", "errorFilePassProtect": "The file is password protected and could not be opened.", "errorFileSizeExceed": "The file size exceeds your server limitation.<br>Please, contact your admin.", "errorKeyEncrypt": "Unknown key descriptor", "errorKeyExpire": "Key descriptor expired", "errorSessionAbsolute": "The document editing session has expired. Please, reload the page.", "errorSessionIdle": "The document has not been edited for quite a long time. Please, reload the page.", "errorSessionToken": "The connection to the server has been interrupted. Please, reload the page.", "errorStockChart": "Incorrect row order. To build a stock chart, place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "errorUpdateVersionOnDisconnect": "Internet connection has been restored, and the file version has been changed.<br>Before you can continue working, download the file or copy its content to make sure nothing is lost, and then reload this page.", "errorUserDrop": "The file cannot be accessed right now.", "errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but you won't be able to download it until the connection is restored and the page is reloaded.", "notcriticalErrorTitle": "Warning", "openErrorText": "An error has occurred while opening the file", "saveErrorText": "An error has occurred while saving the file", "scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please, reload the page.", "splitDividerErrorText": "The number of rows must be a divisor of %1", "splitMaxColsErrorText": "The number of columns must be less than %1", "splitMaxRowsErrorText": "The number of rows must be less than %1", "unknownErrorText": "Unknown error.", "uploadImageExtMessage": "Unknown image format.", "uploadImageFileCountMessage": "No images uploaded.", "uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator."}, "LongActions": {"applyChangesTextText": "Loading data...", "applyChangesTitleText": "Loading Data", "downloadTextText": "Downloading document...", "downloadTitleText": "Downloading Document", "loadFontsTextText": "Loading data...", "loadFontsTitleText": "Loading Data", "loadFontTextText": "Loading data...", "loadFontTitleText": "Loading Data", "loadImagesTextText": "Loading images...", "loadImagesTitleText": "Loading Images", "loadImageTextText": "Loading image...", "loadImageTitleText": "Loading Image", "loadingDocumentTextText": "Loading document...", "loadingDocumentTitleText": "Loading document", "loadThemeTextText": "Loading theme...", "loadThemeTitleText": "Loading Theme", "openTextText": "Opening document...", "openTitleText": "Opening Document", "printTextText": "Printing document...", "printTitleText": "Printing Document", "savePreparingText": "Preparing to save", "savePreparingTitle": "Preparing to save. Please wait...", "saveTextText": "Saving document...", "saveTitleText": "Saving Document", "textLoadingDocument": "Loading document", "txtEditingMode": "Set editing mode...", "uploadImageTextText": "Uploading image...", "uploadImageTitleText": "Uploading Image", "waitText": "Please, wait...", "confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "textUndo": "Undo", "textContinue": "Continue"}, "Toolbar": {"dlgLeaveMsgText": "You have unsaved changes in this document. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "dlgLeaveTitleText": "You leave the application", "leaveButtonText": "Leave this page", "stayButtonText": "Stay on this Page"}, "View": {"Add": {"notcriticalErrorTitle": "Warning", "textAddLink": "Add Link", "textAddress": "Address", "textBack": "Back", "textCancel": "Cancel", "textColumns": "Columns", "textComment": "Comment", "textDefault": "Selected text", "textDisplay": "Display", "textEmptyImgUrl": "You need to specify the image URL.", "textExternalLink": "External Link", "textFirstSlide": "First Slide", "textImage": "Image", "textImageURL": "Image URL", "textInsert": "Insert", "textInsertImage": "Insert Image", "textLastSlide": "Last Slide", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkTo": "Link to", "textLinkType": "Link Type", "textNextSlide": "Next Slide", "textOther": "Other", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPreviousSlide": "Previous Slide", "textRows": "Rows", "textScreenTip": "Screen Tip", "textShape": "<PERSON><PERSON><PERSON>", "textSlide": "Slide", "textSlideInThisPresentation": "Slide in this Presentation", "textSlideNumber": "Slide Number", "textTable": "Table", "textTableSize": "Table Size", "txtNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "textOk": "Ok", "textDone": "Done", "textRecommended": "Recommended", "textRequired": "Required"}, "Edit": {"notcriticalErrorTitle": "Warning", "textActualSize": "Actual Size", "textAddCustomColor": "Add Custom Color", "textAdditional": "Additional", "textAdditionalFormatting": "Additional Formatting", "textAddress": "Address", "textAfter": "After", "textAlign": "Align", "textAlignBottom": "Align Bottom", "textAlignCenter": "Align Center", "textAlignLeft": "Align Left", "textAlignMiddle": "Align Middle", "textAlignRight": "Align Right", "textAlignTop": "Align Top", "textAllCaps": "All Caps", "textApplyAll": "Apply to All Slides", "textAuto": "Auto", "textBack": "Back", "textBandedColumn": "Banded Column", "textBandedRow": "Banded Row", "textBefore": "Before", "textBlack": "Through Black", "textBorder": "Border", "textBottom": "Bottom", "textBottomLeft": "Bottom-Left", "textBottomRight": "Bottom-Right", "textBringToForeground": "Bring to Foreground", "textBullets": "Bullets", "textBulletsAndNumbers": "Bullets & Numbers", "textCaseSensitive": "Case Sensitive", "textCellMargins": "<PERSON>", "textChart": "Chart", "textClock": "Clock", "textClockwise": "Clockwise", "textColor": "Color", "textCounterclockwise": "Counterclockwise", "textCover": "Cover", "textCustomColor": "Custom Color", "textDefault": "Selected text", "textDelay": "Delay", "textDeleteSlide": "Delete Slide", "textDisplay": "Display", "textDistanceFromText": "Distance From Text", "textDistributeHorizontally": "Distribute Horizontally", "textDistributeVertically": "Distribute Vertically", "textDone": "Done", "textDoubleStrikethrough": "Double Strikethrough", "textDuplicateSlide": "Duplicate Slide", "textDuration": "Duration", "textEditLink": "Edit Link", "textEffect": "Effect", "textEffects": "Effects", "textEmptyImgUrl": "You need to specify the image URL.", "textExternalLink": "External Link", "textFade": "Fade", "textFill": "Fill", "textFinalMessage": "The end of slide preview. <PERSON>lick to exit.", "textFind": "Find", "textFindAndReplace": "Find and Replace", "textFirstColumn": "First Column", "textFirstSlide": "First Slide", "textFontColor": "Font Color", "textFontColors": "Font Colors", "textFonts": "Fonts", "textFromLibrary": "Picture from Library", "textFromURL": "Picture from URL", "textHeaderRow": "Header Row", "textHighlight": "Highlight Results", "textHighlightColor": "Highlight Color", "textHorizontalIn": "Horizontal In", "textHorizontalOut": "Horizontal Out", "textHyperlink": "Hyperlink", "textImage": "Image", "textImageURL": "Image URL", "textLastColumn": "Last Column", "textLastSlide": "Last Slide", "textLayout": "Layout", "textLeft": "Left", "textLetterSpacing": "Letter Spacing", "textLineSpacing": "Line Spacing", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkTo": "Link to", "textLinkType": "Link Type", "textMoveBackward": "Move Backward", "textMoveForward": "Move Forward", "textNextSlide": "Next Slide", "textNone": "None", "textNoStyles": "No styles for this type of chart.", "textNoTextFound": "Text not found", "textNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "textNumbers": "Numbers", "textOpacity": "Opacity", "textOptions": "Options", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPreviousSlide": "Previous Slide", "textPt": "pt", "textPush": "<PERSON><PERSON>", "textRemoveChart": "Remove Chart", "textRemoveImage": "Remove Image", "textRemoveLink": "Remove Link", "textRemoveShape": "Remove <PERSON>", "textRemoveTable": "Remove Table", "textReorder": "Reorder", "textReplace": "Replace", "textReplaceAll": "Replace All", "textReplaceImage": "Replace Image", "textRight": "Right", "textScreenTip": "Screen Tip", "textSearch": "Search", "textSec": "s", "textSelectObjectToEdit": "Select object to edit", "textSendToBackground": "Send to Background", "textShape": "<PERSON><PERSON><PERSON>", "textSize": "Size", "textSlide": "Slide", "textSlideInThisPresentation": "Slide in this Presentation", "textSlideNumber": "Slide Number", "textSmallCaps": "Small Caps", "textSmoothly": "Smooth<PERSON>", "textSplit": "Split", "textStartOnClick": "Start On Click", "textStrikethrough": "Strikethrough", "textStyle": "Style", "textStyleOptions": "Style Options", "textSubscript": "Subscript", "textSuperscript": "Superscript", "textTable": "Table", "textText": "Text", "textTheme": "Theme", "textTop": "Top", "textTopLeft": "Top-Left", "textTopRight": "Top-Right", "textTotalRow": "Total Row", "textTransition": "Transition", "textType": "Type", "textUnCover": "UnCover", "textVerticalIn": "Vertical In", "textVerticalOut": "Vertical Out", "textWedge": "Wedge", "textWipe": "Wipe", "textZoom": "Zoom", "textZoomIn": "Zoom In", "textZoomOut": "Zoom Out", "textZoomRotate": "Zoom and Rotate", "textTransitions": "Transitions", "textDesign": "Design", "textAutomatic": "Automatic", "textOk": "Ok", "textArrange": "<PERSON><PERSON><PERSON>", "textCancel": "Cancel", "textChangeShape": "Change Shape", "textDeleteImage": "Delete Image", "textDeleteLink": "Delete Link", "textInsertImage": "Insert Image", "textRecommended": "Recommended", "textRequired": "Required"}, "Settings": {"mniSlideStandard": "Standard (4:3)", "mniSlideWide": "Widescreen (16:9)", "textAbout": "About", "textAddress": "address:", "textApplication": "Application", "textApplicationSettings": "Application Settings", "textAuthor": "Author", "textBack": "Back", "textCaseSensitive": "Case Sensitive", "textCentimeter": "Centimeter", "textCollaboration": "Collaboration", "textColorSchemes": "Color Schemes", "textComment": "Comment", "textCreated": "Created", "textDisableAll": "Disable All", "textDisableAllMacrosWithNotification": "Disable all macros with notification", "textDisableAllMacrosWithoutNotification": "Disable all macros without notification", "textDone": "Done", "textDownload": "Download", "textDownloadAs": "Download As...", "textEmail": "email:", "textEnableAll": "Enable All", "textEnableAllMacrosWithoutNotification": "Enable all macros without notification", "textFind": "Find", "textFindAndReplace": "Find and Replace", "textFindAndReplaceAll": "Find and Replace All", "textHelp": "Help", "textHighlight": "Highlight Results", "textInch": "Inch", "textLastModified": "Last Modified", "textLastModifiedBy": "Last Modified By", "textLoading": "Loading...", "textLocation": "Location", "textMacrosSettings": "<PERSON><PERSON>", "textNoTextFound": "Text not found", "textOwner": "Owner", "textPoint": "Point", "textPoweredBy": "Powered By", "textPresentationInfo": "Presentation Info", "textPresentationSettings": "Presentation Settings", "textPresentationTitle": "Presentation Title", "textPrint": "Print", "textReplace": "Replace", "textReplaceAll": "Replace All", "textSearch": "Search", "textSettings": "Settings", "textShowNotification": "Show Notification", "textSlideSize": "Slide Size", "textSpellcheck": "Spell Checking", "textSubject": "Subject", "textTel": "tel:", "textTitle": "Title", "textUnitOfMeasurement": "Unit Of Measurement", "textUploaded": "Uploaded", "textVersion": "Version", "txtScheme1": "Office", "txtScheme10": "Median", "txtScheme11": "Metro", "txtScheme12": "<PERSON><PERSON><PERSON>", "txtScheme13": "Opulent", "txtScheme14": "Oriel", "txtScheme15": "Origin", "txtScheme16": "Paper", "txtScheme17": "Solstice", "txtScheme18": "Technic", "txtScheme19": "Trek", "txtScheme2": "Grayscale", "txtScheme20": "Urban", "txtScheme21": "Verve", "txtScheme22": "New Office", "txtScheme3": "Apex", "txtScheme4": "Aspect", "txtScheme5": "Civic", "txtScheme6": "Concourse", "txtScheme7": "Equity", "txtScheme8": "Flow", "txtScheme9": "Foundry", "textDarkTheme": "Dark Theme", "textFeedback": "Feedback & Support", "textOk": "Ok", "textRTL": "RTL", "textRestartApplication": "Please restart the application for the changes to take effect", "notcriticalErrorTitle": "Warning"}}}