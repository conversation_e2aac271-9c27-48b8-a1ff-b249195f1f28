﻿<!DOCTYPE html>
<html>
	<head>
		<title>SUMMEWENNS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="Die Funktion SUMMEWENNS gehört zur Gruppe der mathematischen Funktionen. Erfahren Sie, wie Sie die SUMMEWENNS-Formel in Tabellen und kompatiblen Dateien nutzen." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>SUMMEWENNS-Funktion</h1>
			<p>Die Funktion <b>SUMMEWENNS</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um alle Zahlen in gewählten Zellenbereich anhand vom angegebenen Kriterium zu addieren und das Ergebnis zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>SUMMEWENNS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>SUMMEWENNS(Summe_Bereich; Kriterien_Bereich1; Kriterien1; [Kriterien_Bereich2; Kriterien2]; ...)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
            <p style="text-indent: 50px;"><b><em>Summe_Bereich</em></b> ist der Zellenbereich, der addiert werden soll.</p>
			<p style="text-indent: 50px;"><b><em>Kriterien_Bereich1</em></b> ist der erste gewählte Zellenbereich, der auf <em>Kriterien1</em> getestet wird.</p>
			<p style="text-indent: 50px;"><b><em>Kriterien1</em></b> sind die Kriterien, die definieren, welche Zellen in Kriterien_Bereich1 addiert werden. Sie werden auf <em>Kriterien_Bereich1</em> angewendet und bestimmen den Mittelwert der Zellen <em>Summe_Bereich</em>. Der Wert kann manuell eingegeben werden oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p style="text-indent: 50px;"><b><em>Kriterien_Bereich2; Kriterien2, ...</em></b> sind zusätzliche Zellenbereiche und ihre jeweiligen Kriterien. Diese Argumente sind optional.</p>
            <p class="note">Sie können Platzhalterzeichen verwenden, wenn Sie Kriterien angeben. Das Fragezeichen &quot;?&quot; kann ein beliebiges einzelnes Zeichen ersetzen und der Stern &quot;*&quot; kann anstelle einer beliebigen Anzahl von Zeichen verwendet werden.</p>
			<h2>Wie funktioniert SUMMEWENNS</h2>
			<p>Anwendung der Funktion <b>SUMMEWENNS</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>SUMMEWENNS</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="SUMMEWENNS-Funktion" src="../images/sumifs.png" /></p>
		</div>
	</body>
</html>