﻿<!DOCTYPE html>
<html>
	<head>
		<title>View presentation information</title>
		<meta charset="utf-8" />
		<meta name="description" content="View presentation title, author, location, creation date, persons with the rights to view or edit the presentation" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>View presentation information</h1>
			<p>To access the detailed information about the currently edited presentation, click the <b>File</b> tab of the top toolbar and select the <b>Presentation Info</b> option.</p>
            <h3>General Information</h3>
            <p>The spreadsheet information includes a number of the file properties which describe the spreadsheet. Some of these properties are updated automatically, and some of them can be edited.</p>
            <ul>
                <li class="onlineDocumentFeatures"><b>Location</b> - the folder in the <b>Documents</b> module where the file is stored. <b>Owner</b> - the name of the user who have created the file. <b>Uploaded</b> - the date and time when the file has been created. These properties are available in the <em>online version</em> only.</li>
                <li><b>Title</b>, <b>Subject</b>, <b>Comment</b> - these properties allow to simplify your documents classification. You can specify the necessary text in the properties fields.</li>
                <li><b>Last Modified</b> - the date and time when the file was last modified.</li>
                <li><b>Last Modified By</b> - the name of the user who have made the latest change in the presentation if it has been shared and it can be edited by several users. </li>
                <li><b>Application</b> - the application the presentation was created with.</li>
                <li><b>Author</b> - the person who have created the file. You can enter the necessary name in this field. Press <em>Enter</em> to add a new field that allows to specify one more author.</li>
            </ul>
            <p>If you changed the file properties, click the <b>Apply</b> button to apply the changes.</p>
            <div class="onlineDocumentFeatures">
                <p class="note"><b>Note</b>: Online Editors allow you to change the presentation title directly from the editor interface. To do that, click the <b>File</b> tab of the top toolbar and select the <b>Rename...</b> option, then enter the necessary <b>File name</b> in a new window that opens and click <b>OK</b>.</p>
            </div>
            <div class="onlineDocumentFeatures">
            <h3>Permission Information</h3>
            <p>In the <em>online version</em>, you can view the information about permissions to the files stored in the cloud.</p>
            <p class="note"><b>Note</b>: this option is not available for users with the <b>Read Only</b> permissions.</p>
            <p>To find out, who have rights to view or edit the presentation, select the <b>Access Rights...</b> option at the left sidebar.</p>
			<p>You can also change currently selected access rights by pressing the <b>Change access rights</b> button in the <b>Persons who have rights</b> section.</p>
            </div>
			<p>To close the <b>File</b> pane and return to presentation editing, select the <b>Close Menu</b> option.</p>
		</div>
	</body>
</html>