﻿<!DOCTYPE html>
<html>
	<head>
		<title>RANG.GLEICH-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>RANG.GLEICH-Funktion</h1>
			<p>Die Funktion <b>RANG.GLEICH</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den Rang zurück, den eine Zahl innerhalb einer Liste von Zahlen einnimmt. Als Rang einer Zahl wird deren Größe, bezogen auf die anderen Werte der jeweiligen Liste, bezeichnet. (Wenn Sie die Liste sortieren würden, würde die Rangzahl die Position der Zahl angeben.) Wenn mehrere Werte die gleiche Rangzahl aufweisen, wird der oberste Rang dieser Gruppe von Werten zurückgegeben.</p>
			<p>Die Formelsyntax der Funktion <b>RANG.GLEICH</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>RANG.GLEICH(Zahl;Bezug;[Reihenfolge])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist die Zahl, für die der Rang ermittelt werden soll.</p>
				<p style="text-indent: 50px;"><b><em>Bezug</em></b> ist eine Matrix oder ein Bezug auf eine Liste von Zahlen, in der die angegebene <b><em>Zahl</em></b> enthalten ist.</p>
				<p style="text-indent: 50px;"><b><em>Reihenfolge</em></b> ist der Zahlenwert der angibt, wie <b><em>Bezug</em></b> geordnet werden soll. Dieses Argument ist optional. Ist Reihenfolge mit 0 (Null) belegt oder nicht angegeben, bestimmt die Funktion den Rang von <b><em>Zahl</em></b> so, als wäre <b><em>Bezug</em></b> eine in absteigender Reihenfolge sortierte Liste. Ist Reihenfolge mit einem Wert ungleich 0 belegt, bestimmt die Funktion den Rang von <b><em>Zahl</em></b> so, als wäre <b><em>Bezug</em></b> eine in aufsteigender Reihenfolge sortierte Liste.</p>
			<p>Anwendung der Funktion <b>RANG.GLEICH</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>RANG.GLEICH</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="RANG.GLEICH-Funktion" src="../images/rank-eq.png" /></p>
		</div>
	</body>
</html>