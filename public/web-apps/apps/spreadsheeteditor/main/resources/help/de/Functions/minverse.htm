﻿<!DOCTYPE html>
<html>
	<head>
		<title>MINV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>MINV-Funktion</h1>
			<p>Die Funktion <b>MINV</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um die zu einer Matrix gehörende Kehrmatrix zurückzugeben und den ersten Wert des zurückgegebenen Arrays der Zahlen anzuzeigen.</p>
			<p>Die Formelsyntax der Funktion <b>MINV</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>MINV(Matrix)</em></b></p> 
			<p><b><em>Matrix</em></b> ist eine Anordnung von Zahlen.</p>
			<p class="note"><b>Hinweis</b>: Wenn die Zellen in der Matrix leer sind oder Text enthalten, gibt die Funktion den Fehlerwert <b>#NV</b> zurück. Wenn die Anzahl der Zeilen und Spalten in Matrix nicht gleich ist, gibt die Funktion den Fehlerwert <b>#WERT!</b> zurück.</p>
			<p>Anwendung der Funktion <b>MINV</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie die Funktion <b>MINV</b>.</li>
			<li>Wählen Sie den Zellenbereich mit der Maus oder geben Sie das gewünschte Argument manuell als A1:B2 ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="MINV-Funktion" src="../images/minverse.png" /></p>
		</div>
	</body>
</html>