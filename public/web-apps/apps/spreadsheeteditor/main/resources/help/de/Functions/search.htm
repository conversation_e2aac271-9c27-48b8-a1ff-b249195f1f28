﻿<!DOCTYPE html>
<html>
	<head>
		<title>SUCHEN/SUCHENB-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>SUCHEN/SUCHENB-Funktion</h1>
			<p>Die Funktionen <b>SUCHEN/SUCHENB</b> gehören zur Gruppe der Text- und Datenfunktionen. Sie werden verwendet, um einen in einem anderen Textwert enthaltenen Textwert (Groß-/Kleinschreibung wird nicht beachtet) zu suchen. Die Funktion <b>SUCHEN</b> ist für Sprachen gedacht, die den Single-Byte-Zeichensatz (SBCS) verwenden, während <b>SUCHENB</b> für Sprachen verwendet wird, die den Doppelbyte-Zeichensatz (DBCS) verwenden, wie Japanisch, Chinesisch, Koreanisch usw.</p>
			<p>Die Formelsyntax der Funktion <b>SUCHEN/SUCHENB</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>SUCHEN(Suchtext;Text;[Erstes_Zeichen])</em></b></p> 
			<p style="text-indent: 150px;"><b><em>SUCHENB(Suchtext;Text;[Erstes_Zeichen])</em></b></p>
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Suchtext</em></b> ist der gesuchte Text.</p>
			<p style="text-indent: 50px;"><b><em>Text</em></b> ist der Text indem nach dem Argument Suchtext gesucht werden soll.</p>
			<p style="text-indent: 50px;"><b><em>Erstes Zeichen</em></b> ist die Nummer des Zeichens im Argument Text, ab der die Suche durchgeführt werde soll. Dieses Argument ist optional. Fehlt das Argument Erstes_Zeichen, wird es als 1 angenommen und die Funktion startet die Suche am Anfang von <b><em>Text</em></b>.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p class="note"><b>Hinweis</b>: Wird der in Suchtext angegebene Wert nicht gefunden, gibt die Funktion den Fehlerwert <b>#WERT!</b> zurück.</p>
			<p>Anwendung der Funktion <b>SUCHEN/SUCHENB</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>SUCHEN/SUCHENB</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.<p class="note"><b>Hinweis</b>: die Funktionen SUCHEN und SUCHENB unterscheiden <b>NICHT</b> zwischen Groß- und Kleinschreibung.</p>
			</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="SUCHEN-Funktion" src="../images/search.png" /></p>
		</div>
	</body>
</html>