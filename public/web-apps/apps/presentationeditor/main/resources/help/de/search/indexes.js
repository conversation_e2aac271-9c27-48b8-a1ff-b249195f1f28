var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "Über den Präsentationseditor", 
        "body": "Der Präsentationseditor ist eine Online- Anwendung, mit der Sie Ihre Dokumente direkt in Ihrem Browser betrachten und bearbeiten können. Mit dem Präsentationseditor können Sie verschiedene Editiervorgänge durchführen wie bei einem beliebigen Desktopeditor, editierte Präsentationen unter Beibehaltung aller Formatierungsdetails drucken oder sie auf der Festplatte Ihres Rechners als PPTX-, PDF-. ODP-, POTX-, PDF/A- oder OTP-Dateien speichern. Wenn Sie in der Online-Version mehr über die aktuelle Softwareversion, das Build und den Lizenzgeber erfahren möchten, klicken Sie auf das Symbol in der linken Seitenleiste. Wenn Sie in der Desktop-Version für Windows mehr über die aktuelle Softwareversion und den Lizenzgeber erfahren möchten, wählen Sie das Menü Über in der linken Seitenleiste des Hauptfensters. Öffnen Sie in der Desktop-Version für Mac OS das Menü ONLYOFFICE oben auf dem Bildschirm und wählen Sie den Menüpunkt Über ONLYOFFICE."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Erweiterte Einstellungen des Präsentationseditors", 
        "body": "Der Präsentationseditor ermöglicht es Ihnen, die erweiterten Einstellungen zu ändern. Um darauf zuzugreifen, öffnen Sie die Registerkarte Datei in der oberen Symbolleiste und wählen Sie die Option Erweiterte Einstellungen. Die erweiterten Einstellungen sind wie folgt gruppiert: Bearbeitung und Speicherung Die Option Automatisch speichern wird in der Online-Version verwendet, um das automatische Speichern von Änderungen, die Sie während der Bearbeitung vornehmen, ein-/auszuschalten. Die Option Wiederherstellen wird in der Desktop-Version verwendet, um die Option ein-/auszuschalten, die die automatische Wiederherstellung von Präsentationen ermöglicht, falls das Programm unerwartet geschlossen wird. Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen. Das entsprechende Symbol wird angezeigt, wenn Sie Inhalte in die Präsentation einfügen. Zusammenarbeit Im Unterabschnitt Modus \"Gemeinsame Bearbeitung\" können Sie den bevorzugten Modus zum Anzeigen von Änderungen an der Präsentation festlegen, wenn Sie gemeinsam arbeiten. Schnell (standardmäßig). Die Benutzer, die an der gemeinsamen Bearbeitung von Präsentationen teilnehmen, sehen die Änderungen in Echtzeit, sobald sie von anderen Benutzern vorgenommen wurden. Formal. Alle von Mitbearbeitern vorgenommenen Änderungen werden erst angezeigt, nachdem Sie auf das Symbol Speichern geklickt haben, das Sie über neue Änderungen informiert. Rechtschreibprüfung Die Option Rechtschreibprüfung wird verwendet, um die Rechtschreibprüfung ein-/auszuschalten. Wörter in GROSSBUCHSTABEN ignorieren. In Großbuchstaben eingegebene Wörter werden bei der Rechtschreibprüfung ignoriert. Wörter mit Zahlen ignorieren. Wörter mit Zahlen werden bei der Rechtschreibprüfung ignoriert. Über das Menü Automatische Korrekturoptionen können Sie auf die Autokorrektur-Einstellungen zugreifen, z. B. Text während der Eingabe ersetzen, Funktionen erkennen, automatische Formatierung usw. Arbeitsbereich Die Option Ausrichtungslinien wird zum Ein-/Ausschalten der Ausrichtungshilfslinien verwendet, die beim Verschieben von Objekten angezeigt werden. Sie ermöglicht eine präzisere Objektpositionierung auf der Seite. Die Option Hieroglyphen wird verwendet, um die Anzeige von Hieroglyphen ein-/auszuschalten. Die Option Verwenden Sie die Alt-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren wird verwendet, um die Verwendung der Alt-Taste in Tastaturkürzeln zu aktivieren. Die Option Thema der Benutzeroberfläche wird verwendet, um das Farbschema der Benutzeroberfläche des Editors zu ändern. Die Option Wie im System sorgt dafür, dass der Editor dem Oberflächendesign Ihres Systems folgt. Das Farbschema Hell umfasst die Standardfarben Blau, Weiß und Hellgrau mit weniger Kontrast in UI-Elementen, die für die Arbeit tagsüber geeignet sind. Das Farbschema Klassisch Hell umfasst die Standardfarben Blau, Weiß und Hellgrau. Das Farbschema Dunkel umfasst schwarze, dunkelgraue und hellgraue Farben, die für Arbeiten bei Nacht geeignet sind. Das Farbschema Dunkler Kontrast umfasst schwarze, dunkelgraue und weiße Farben mit mehr Kontrast in UI-Elementen, die den Arbeitsbereich der Datei hervorheben. Abgesehen von den verfügbaren Benutzeroberflächendesigns Hell, Klassisch Hell, Dunkel und Dunkler Kontrast können jetzt ONLYOFFICE-Editoren mit Ihrem eigenen Farbschema angepasst werden. Bitte befolgen Sie diese Anleitung, um zu erfahren, wie Sie das tun können. Die Option Maßeinheit wird verwendet, um anzugeben, welche Einheiten auf den Linealen und in Eigenschaften von Objekten verwendet werden, wenn Parameter wie Breite, Höhe, Abstand, Ränder usw. eingestellt werden. Die verfügbaren Einheiten sind Zentimeter, Punkt und Zoll. Die Option Standard-Zoom-Wert wird verwendet, um den Standard-Zoom-Wert festzulegen, indem Sie ihn in der Liste der verfügbaren Optionen zwischen 50 % und 500 % auswählen. Sie können auch die Option Folie anpassen oder Breite anpassen auswählen. Die Option Schriftglättung wird verwendet, um auszuwählen, wie Schriftarten im Präsentationseditor angezeigt werden. Wählen Sie Wie Windows, wenn Ihnen die Art gefällt, wie die Schriftarten unter Windows gewöhnlich angezeigt werden, d.h. mit Windows-artigen Hints. Wählen Sie Wie OS X, wenn Ihnen die Art gefällt, wie die Schriftarten auf einem Mac gewöhnlich angezeigt werden, d.h. ohne Hints. Wählen Sie Einheitlich, wenn Sie möchten, dass Ihr Text mit den Hints angezeigt wird, die in Schriftartdateien eingebettet sind. Standard-Cache-Modus wird verwendet, um den Cache-Modus für die Schriftzeichen auszuwählen. Es wird nicht empfohlen, es ohne Gründe zu wechseln. Es kann nur in manchen Fällen hilfreich sein, beispielsweise wenn der Google Chrome-Browser Probleme mit der aktivierten Hardwarebeschleunigung hat. Der Präsentationseditor verfügt über zwei Cache-Modi: Im ersten Cache-Modus wird jeder Buchstabe als separates Bild gecachet. Im zweiten Cache-Modus wird ein Bild einer bestimmten Größe ausgewählt, in dem Buchstaben dynamisch platziert werden, und ein Mechanismus zum Zuweisen/Entfernen von Speicher in diesem Bild wird ebenfalls implementiert. Wenn nicht genügend Speicher vorhanden ist, wird ein zweites Bild erstellt usw. Die Einstellung Standard-Cache-Modus wendet zwei der oben genannten Cache-Modi separat für verschiedene Browser an: Wenn die Einstellung Standard-Cache-Modus aktiviert ist, verwendet Internet Explorer (v. 9, 10, 11) den zweiten Cache-Modus, andere Browser verwenden den ersten Cache-Modus . Wenn die Einstellung Standard-Cache-Modus deaktiviert ist, verwendet Internet Explorer (v. 9, 10, 11) den ersten Cache-Modus, andere Browser verwenden den zweiten Cache-Modus . Die Option Einstellungen von Makros wird verwendet, um die Anzeige von Makros mit einer Benachrichtigung einzustellen. Wählen Sie Alle deaktivieren, um alle Makros in der Präsentation zu deaktivieren. Wählen Sie Benachrichtigung anzeigen, um Benachrichtigungen über Makros in der Präsentation zu erhalten. Wählen Sie Alle aktivieren, um automatisch alle Makros in der Präsentation auszuführen. Um die vorgenommenen Änderungen zu speichern, klicken Sie auf Anwenden."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Gemeinsame Bearbeitung von Präsentationen in Echtzeit", 
        "body": "Der Präsentationseditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beibehalten: Sie können die Dateien und Ordner freigeben; an Präsentationen in Echtzeit zusammenarbeiten; direkt im Editor kommunizieren; bestimmte Teile Ihrer Präsentationen, die zusätzliche Eingaben Dritter erfordern, kommentieren; die Versionen von Präsentationen für die zukünftige Verwendung speichern. Im Präsentationseditor können Sie in Echtzeit an Präsentationen mit zwei Modi zusammenarbeiten: Schnell oder Formal. Die Modi können in den erweiterten Einstellungen ausgewählt werden. Es ist auch möglich, den erforderlichen Modus über das Symbol Modus \"Gemeinsame Bearbeitung\" auf der Registerkarte Zusammenarbeit in der oberen Symbolleiste auswählen: Die Anzahl der Benutzer, die in der aktuellen Präsentation arbeiten, wird in der linken unteren Ecke auf der Statusleiste angegeben - . Wenn Sie sehen möchten, wer genau die Datei gerade bearbeitet, können Sie auf dieses Symbol klicken oder das Chat-Bedienfeld mit der vollständigen Liste der Benutzer öffnen. Modus \"Schnell\" Der Modus Schnell wird standardmäßig verwendet und zeigt die von anderen Benutzern vorgenommenen Änderungen in Echtzeit an. Wenn Sie eine Präsentation in diesem Modus gemeinsam bearbeiten, ist die Möglichkeit zum Wiederholen des letzten rückgängig gemachten Vorgangs nicht verfügbar. In diesem Modus werden die Aktionen und die Namen der Mitbearbeiter angezeigt. Wenn eine Präsentation in diesem Modus von mehreren Benutzern gleichzeitig bearbeitet wird, werden die bearbeiteten Objekte mit gestrichelten Linien in unterschiedlichen Farben gekennzeichnet. Wenn Sie den Mauszeiger über eine der bearbeiteten Passagen bewegen, wird der Name des Benutzers angezeigt, der sie gerade bearbeitet. Modus \"Formal\" Der Modus Formal wird ausgewählt, um von anderen Benutzern vorgenommene Änderungen auszublenden, bis Sie auf das Symbol Speichern  klicken, um Ihre Änderungen zu speichern und die von Co-Autoren vorgenommenen Änderungen anzunehmen. Wenn eine Präsentation von mehreren Benutzern gleichzeitig im Modus Formal bearbeitet wird, werden die bearbeiteten Objekte (AutoFormen, Textobjekte, Tabellen, Bilder, Diagramme) mit gestrichelten Linien unterschiedlicher Farbe markiert. Das Objekt, das Sie bearbeiten, ist von der grün gestrichelten Linie umgeben. Rote gestrichelte Linien zeigen an, dass Objekte von anderen Benutzern bearbeitet werden. Sobald einer der Benutzer seine Änderungen durch Klicken auf das Symbol speichert, sehen die anderen einen Hinweis in der Statusleiste, der darauf informiert, dass es Aktualisierungen gibt. Um die von Ihnen vorgenommenen Änderungen zu speichern, damit andere Benutzer sie sehen, und die von Ihren Mitbearbeitern gespeicherten Aktualisierungen abzurufen, klicken Sie auf das Symbol in der linken oberen Ecke der oberen Symbolleiste. Die Aktualisierungen werden hervorgehoben, damit Sie überprüfen können, was genau geändert wurde. Modus \"Live Viewer\" Der Modus Live Viewer wird verwendet, um die von anderen Benutzern vorgenommenen Änderungen in Echtzeit anzuzeigen, wenn die Präsentation von einem Benutzer mit den Zugriffsrechten Schreibgeschützt geöffnet wird. Damit der Modus richtig funktioniert, stellen Sie sicher, dass das Kontrollkästchen Änderungen von anderen Benutzer anzeigen in den Erweiterten Einstellungen des Editors aktiviert ist. Anonym Portalbenutzer, die nicht registriert sind und kein Profil haben, gelten als anonym, können jedoch weiterhin an Dokumenten zusammenarbeiten. Um ihnen einen Namen zuzuweisen, muss der anonyme Benutzer beim ersten Öffnen des Dokuments einen Namen in das entsprechende Feld in der rechten oberen Ecke des Bildschirms eingeben. Aktivieren Sie das Kontrollkästchen \"Nicht mehr anzeigen\", um den Namen beizubehalten."
    },
   {
        "id": "HelpfulHints/Commenting.htm", 
        "title": "Präsentationen kommentieren", 
        "body": "Der Präsentationseditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beibehalten: Sie können die Dateien und Ordner freigeben; an Präsentationen in Echtzeit zusammenarbeiten; direkt im Editor kommunizieren; die Versionen von Präsentationen für die zukünftige Verwendung speichern. Im Präsentationseditor können Sie Kommentare zum Inhalt von Präsentationen hinterlassen, ohne ihn tatsächlich zu bearbeiten. Im Gegensatz zu Chat-Nachrichten bleiben die Kommentare, bis sie gelöscht werden. Kommentare hinterlassen und darauf antworten Um einen Kommentar zu einem bestimmten Objekt (Textfeld, Form usw.) zu hinterlassen: Wählen Sie ein Objekt aus, bei dem Ihrer Meinung nach ein Fehler oder Problem vorliegt. Wechseln Sie zur Registerkarte Einfügen oder Zusammenarbeit der oberen Symbolleiste und klicken Sie auf die Schaltfläche Kommentar, oder Klicken Sie mit der rechten Maustaste auf das ausgewählte Objekt und wählen Sie im Menü die Option Kommentar hinzufügen. Geben Sie den erforderlichen Text ein. Klicken Sie auf die Schaltfläche Kommentar hinzufügen/Hinzufügen. Das von Ihnen kommentierte Objekt wird mit dem Symbol gekennzeichnet. Um den Kommentar anzuzeigen, klicken Sie einfach auf dieses Symbol. Um einen Kommentar zu einer bestimmten Folie hinzuzufügen, wählen Sie die Folie aus und verwenden Sie die Schaltfläche Kommentar auf der Registerkarte Einfügen oder Zusammenarbeit in der oberen Symbolleiste. Der hinzugefügte Kommentar wird in der oberen linken Ecke der Folie angezeigt. Um einen Kommentar auf Präsentationsebene zu erstellen, der sich nicht auf ein bestimmtes Objekt oder eine bestimmte Folie bezieht, klicken Sie auf das Symbol in der linken Seitenleiste, um das Bedienfield Kommentare zu öffnen, und verwenden Sie den Link Kommentar zum Dokument hinzufügen. Die Kommentare auf Präsentationsebene können im Bereich Kommentare angezeigt werden. Hier sind auch Kommentare zu Objekten und Folien verfügbar. Jeder andere Benutzer kann auf den hinzugefügten Kommentar antworten, indem er Fragen stellt oder über seine Arbeit berichtet. Klicken Sie dazu auf den Link Antwort hinzufügen unterhalb des Kommentars, geben Sie Ihren Antworttext in das Eingabefeld ein und klicken Sie auf die Schaltfläche Antworten. Wenn Sie den Co-Bearbeitungsmodus Formal verwenden, werden neue Kommentare, die von anderen Benutzern hinzugefügt wurden, erst sichtbar, nachdem Sie auf das Symbol in der linken oberen Ecke der oberen Symbolleiste geklickt haben. Kommentare verwalten Sie können die hinzugefügten Kommentare mit den Symbolen in der Kommentarsprechblase oder im Bereich Kommentare auf der linken Seite verwalten: Sortieren Sie die hinzugefügten Kommentare, indem Sie auf das Symbol klicken: nach Datum: Neueste zuerst oder Älteste zuerste. nach Verfasser: Verfasser (A-Z) oder Verfasser (Z-A). nach Gruppe: Alle oder wählen Sie eine bestimmte Gruppe aus der Liste aus. Diese Sortieroption ist verfügbar, wenn Sie eine Version ausführen, die diese Funktionalität enthält. Bearbeiten Sie den aktuell ausgewählten Kommentar, indem Sie auf das Symbol klicken. Löschen Sie den aktuell ausgewählten Kommentar, indem Sie auf das Symbol klicken. Schließen Sie die aktuell ausgewählte Diskussion, indem Sie auf das Symbol klicken, wenn die von Ihnen in Ihrem Kommentar angegebene Aufgabe oder das Problem gelöst wurde, danach die von Ihnen geöffnete Diskussion mit Ihrem Kommentar erhält den gelösten Status. Klicken Sie auf das Symbol , um die Diskussion neu zu öffnen. Wenn Sie mehrere Kommentare verwalten möchten, öffnen Sie das Drop-Down-Menü Lösen auf der Registerkarte Zusammenarbeit. Wählen Sie eine der Optionen zum Auflösen von Kommentaren aus: Gültige Kommentare lösen, Meine Kommentare lösen oder Alle Kommentare lösen. Erwähnungen hinzufügen Sie können Erwähnungen nur zu den Kommentaren zum Inhalt der Präsentation hinzufügen, nicht zur Präsentation selbst. Bei der Eingabe von Kommentaren können Sie die Funktion Erwähnungen verwenden, mit der Sie die Aufmerksamkeit von jemandem auf den Kommentar lenken und eine Benachrichtigung per E-Mail und Chat an den erwähnten Benutzer senden können. Um eine Erwähnung hinzuzufügen: Geben Sie das Zeichen \"+\" oder \"@\" an einer beliebigen Stelle im Kommentartext ein - eine Liste der Portalbenutzer wird geöffnet. Um den Suchvorgang zu vereinfachen, können Sie im Kommentarfeld mit der Eingabe eines Namens beginnen - die Benutzerliste ändert sich während der Eingabe. Wählen Sie die erforderliche Person aus der Liste aus. Wenn die Datei noch nicht für den genannten Benutzer freigegeben wurde, wird das Fenster Freigabeeinstellungen geöffnet. Der Zugriffstyp Schreibgeschützt ist standardmäßig ausgewählt. Ändern Sie es bei Bedarf. Klicken Sie auf OK. Der erwähnte Benutzer erhält eine E-Mail-Benachrichtigung, dass er in einem Kommentar erwähnt wurde. Wurde die Datei freigegeben, erhält der Benutzer auch eine entsprechende Benachrichtigung. Kommentare entfernen Um Kommentare zu entfernen: Klicken Sie auf die Schaltfläche Entfernen auf der Registerkarte Zusammenarbeit in der oberen Symbolleiste. Wählen Sie die erforderliche Option aus dem Menü: Aktuelle Kommentare entfernen, um den aktuell ausgewählten Kommentar zu entfernen. Wenn dem Kommentar einige Antworten hinzugefügt wurden, werden alle seine Antworten ebenfalls entfernt. Meine Kommentare entfernen, um von Ihnen hinzugefügte Kommentare zu entfernen, ohne von anderen Benutzern hinzugefügte Kommentare zu entfernen. Wenn Ihrem Kommentar einige Antworten hinzugefügt wurden, werden auch alle Antworten entfernt. Alle Kommentare entfernen, um alle Kommentare in der Präsentation zu entfernen, die Sie und andere Benutzer hinzugefügt haben. Um die Leiste mit den Kommentaren zu schließen, klicken Sie in der linken Seitenleiste erneut auf das Symbol ."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Tastenkombinationen", 
        "body": "Tastenkombinationen für Key-Tipps Verwenden Sie Tastenkombinationen für einen schnelleren und einfacheren Zugriff auf die Funktionen des Präsentationseditors ohne eine Maus zu verwenden. Drücken Sie die Alt-Taste, um alle wichtigen Tipps für die Kopfzeile des Editors, die obere Symbolleiste, die rechte und linke Seitenleiste und die Statusleiste einzuschalten. Drücken Sie den Buchstaben, der dem Element entspricht, das Sie verwenden möchten. Die zusätzlichen Tastentipps können je nach gedrückter Taste angezeigt werden. Die ersten Tastentipps werden ausgeblendet, wenn zusätzliche Tastentipps angezeigt werden. Um beispielsweise auf die Registerkarte Einfügen zuzugreifen, drücken Sie Alt, um alle Tipps zu den Primärtasten anzuzeigen. Drücken Sie den Buchstaben I, um auf die Registerkarte Einfügen zuzugreifen, und Sie sehen alle verfügbaren Verknüpfungen für diese Registerkarte. Drücken Sie dann den Buchstaben, der dem zu konfigurierenden Element entspricht. Drücken Sie Alt, um alle Tastentipps auszublenden, oder drücken Sie Escape, um zur vorherigen Gruppe von Tastentipps zurückzukehren. In der folgenden Liste finden Sie die gängigsten Tastenkombinationen: Windows/Linux Mac OS Eine Präsentation bearbeiten Dateimenü öffnen ALT+F ^ STRG+⌥ Option+F Über das Dateimenü können Sie die aktuelle Präsentation speichern, drucken, herunterladen, Informationen einsehen, eine neue Präsentation erstellen oder eine vorhandene öffnen, auf die Hilfefunktion zugreifen oder die erweiterten Einstellungen öffnen. Dialogfeld „Suchen“ öffnen STRG+F ^ STRG+F, &#8984; Cmd+F Über das Dialogfeld Suchen können Sie in der aktuellen Präsentation nach Zeichen/Wörtern/Phrasen suchen. Kommentarleiste öffnen STRG+⇧ UMSCHALT+H ^ STRG+⇧ UMSCHALT+H, &#8984; Cmd+⇧ UMSCHALT+H Über die Kommentarleiste können Sie Kommentare hinzufügen oder auf bestehende Kommentare antworten. Kommentarfeld öffnen ALT+H &#8984; Cmd+⌥ Option+A Ein Textfeld zum Eingeben eines Kommentars öffnen. Chatleiste öffnen ALT+Q ^ STRG+⌥ Option+Q Chatleiste öffnen, um eine Nachricht zu senden. Präsentation speichern STRG+S ^ STRG+S, &#8984; Cmd+S Alle Änderungen in der aktuellen Präsentation werden gespeichert. Die aktive Datei wird mit dem aktuellen Dateinamen, Speicherort und Dateiformat gespeichert. Präsentation drucken STRG+P ^ STRG+P, &#8984; Cmd+P Ausdrucken mit einem verfügbaren Drucker oder speichern als Datei. Herunterladen als... STRG+⇧ UMSCHALT+S ^ STRG+⇧ UMSCHALT+S, &#8984; Cmd+⇧ UMSCHALT+S Öffnen Sie das Menü Herunterladen als..., um die aktuell bearbeitete Präsentation in einem der unterstützten Dateiformate auf der Festplatte speichern: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. Vollbild F11 Der Präsentationseditor wird an Ihren Bildschirm angepasst und im Vollbildmodus ausgeführt. Hilfemenü F1 F1 Das Hilfemenü wird geöffnet. Vorhandene Datei öffnen (Desktop-Editoren) STRG+O Auf der Registerkarte Lokale Datei öffnen unter Desktop-Editoren wird das Standarddialogfeld geöffnet, in dem Sie eine vorhandene Datei auswählen können. Datei schließen (Desktop-Editoren) STRG+W, STRG+F4 ^ STRG+W, &#8984; Cmd+W Die aktuelle Präsentation in Desktop-Editoren schließen. Element-Kontextmenü ⇧ UMSCHALT+F10 ⇧ UMSCHALT+F10 Öffnen des ausgewählten Element-Kontextmenüs. Parameter „Zoom“ zurücksetzen STRG+0 ^ STRG+0 oder &#8984; Cmd+0 Setzen Sie den Parameter „Zoom“ der aktuellen Präsentation auf den Standardwert „An Folie anpassen“ zurück. Navigation Erste Folie POS1 POS1, Fn+← Auf die erste Folie der aktuellen Präsentation wechseln. Letzte Folie ENDE ENDE, Fn+→ Auf die letzte Folie der aktuellen Präsentation wechseln. Nächste Folie BILD auf BILD auf, Fn+↓ Auf die nächste Folie der aktuellen Präsentation wechseln. Vorherige Folie BILD ab BILD ab, Fn+↑ Auf die vorherige Folie der aktuellen Präsentation wechseln. Vergrößern STRG++ ^ STRG+=, &#8984; Cmd+= Die Ansicht der aktuellen Präsentation vergrößern. Verkleinern STRG+- ^ STRG+-, &#8984; Cmd+- Die Ansicht der aktuellen Präsentation verkleinern. Zwischen Steuerelementen in modalen Dialogen navigieren ↹ Tab/⇧ UMSCHALT+↹ Tab ↹ Tab/⇧ UMSCHALT+↹ Tab Navigieren Sie zwischen Steuerelementen, um den Fokus auf das nächste oder vorherige Steuerelement in modalen Dialogen zu legen. Folien bearbeiten Neue Folie STRG+M ^ STRG+M, &#8984; Cmd+M Eine neue Folie erstellen und nach der ausgewählten Folie in der Liste einfügen. Folie duplizieren STRG+D &#8984; Cmd+D Die ausgewählte Folie wird dupliziert. Folie nach oben verschieben STRG+↑ &#8984; Cmd+↑ Die ausgewählte Folie in der Liste hinter die vorherige Folie verschieben. Folie nach unten verschieben STRG+↓ &#8984; Cmd+↓ Die ausgewählte Folie in der Liste hinter die nachfolgende Folie verschieben. Folie zum Anfang verschieben STRG+⇧ UMSCHALT+↑ &#8984; Cmd+⇧ UMSCHALT+↑ Verschiebt die gewählte Folie an die erste Position in der Liste. Folie zum Ende verschieben STRG+⇧ UMSCHALT+↓ &#8984; Cmd+⇧ UMSCHALT+↓ Verschiebt die gewählte Folie an die letzte Position in der Liste. Objekte bearbeiten Kopie erstellen STRG + ziehen, STRG+D ^ STRG + ziehen, &#8984; Cmd + ziehen, ^ STRG+D, &#8984; Cmd+D Halten Sie die Taste STRG beim Ziehen des gewählten Objekts gedrückt oder drücken Sie STRG+D (&#8984; Cmd+D für Mac), um die Kopie zu erstellen. Gruppieren STRG+G &#8984; Cmd+G Die ausgewählten Objekte gruppieren. Gruppierung aufheben STRG+⇧ UMSCHALT+G &#8984; Cmd+⇧ UMSCHALT+G Die Gruppierung der gewählten Objekte wird aufgehoben. Nächstes Objekt auswählen ↹ Tab ↹ Tab Das nächste Objekt nach dem aktuellen auswählen. Vorheriges Objekt wählen ⇧ UMSCHALT+↹ Tab ⇧ UMSCHALT+↹ Tab Das vorherige Objekt vor dem aktuellen auswählen. Gerade Linie oder Pfeil zeichnen ⇧ UMSCHALT + ziehen (beim Ziehen von Linien/Pfeilen) ⇧ UMSCHALT + ziehen (beim Ziehen von Linien/Pfeilen) Zeichnen einer geraden vertikalen/horizontalen/45-Grad Linie oder eines solchen Pfeils. Objekte ändern Verschiebung begrenzen ⇧ UMSCHALT + ziehen ⇧ UMSCHALT + ziehen Die Verschiebung des gewählten Objekts wird horizontal oder vertikal begrenzt. 15-Grad-Drehung einschalten ⇧ UMSCHALT + ziehen (beim Drehen) ⇧ UMSCHALT + ziehen (beim Drehen) Die Drehung wird auf 15-Grad-Stufen begrenzt. Seitenverhältnis sperren ⇧ UMSCHALT + ziehen (beim Ändern der Größe) ⇧ UMSCHALT + ziehen (beim Ändern der Größe) Das Seitenverhältnis des gewählten Objekts wird bei der Größenänderung beibehalten. Bewegung Pixel für Pixel STRG+← → ↑ ↓ &#8984; Cmd+← → ↑ ↓ Halten Sie die Taste STRG (&#8984; Cmd bei Mac) gedrückt und nutzen Sie die Pfeile auf der Tastatur, um das gewählte Objekt jeweils um ein Pixel zu verschieben. Tabellen bearbeiten Zur nächsten Zelle in einer Zeile übergeghen ↹ Tab ↹ Tab Zur nächsten Zelle in einer Zeile wechseln. Zur nächsten Zelle in einer Tabellenzeile wechseln. ⇧ UMSCHALT+↹ Tab ⇧ UMSCHALT+↹ Tab Zur vorherigen Zelle in einer Zeile wechseln. Zur nächsten Zeile wechseln ↓ ↓ Zur nächsten Zeile in einer Tabelle wechseln. Zur vorherigen Zeile wechseln ↑ ↑ Zur vorherigen Zeile in einer Tabelle wechseln. Neuen Abstz beginnen ↵ Eingabetaste ↵ Zurück Einen neuen Absatz in einer Zelle beginnen. Neue Zeile einfügen ↹ Tab in der unteren rechten Tabellenzelle. ↹ Tab in der unteren rechten Tabellenzelle. Eine neue Zeile am Ende der Tabelle einfügen. Vorschau der Präsentation Vorschau von Beginn an starten STRG+F5 ^ STRG+F5 Die Vorschau wird von Beginn an Präsentation gestartet Vorwärts navigieren ↵ Eingabetaste, BILD auf, →, ↓, ␣ Leertaste ↵ Zurück, BILD auf, →, ↓, ␣ Leertaste Startet die Vorschau der nächsten Animation oder geht zur nächsten Folie über. Rückwärts navigieren BILD ab, ←, ↑ BILD ab, ←, ↑ Vorschau der vorherigen Animation oder zur vorherigen Folie übergehen. Vorschau beenden ESC ESC Die Vorschau wird beendet. Rückgängig machen und Wiederholen Rückgängig machen STRG+Z ^ STRG+Z, &#8984; Cmd+Z Die zuletzt durchgeführte Aktion wird rückgängig gemacht. Wiederholen STRG+J ^ STRG+J, &#8984; Cmd+J Die zuletzt durchgeführte Aktion wird wiederholt. Ausschneiden, Kopieren, Einfügen Ausschneiden STRG+X, ⇧ UMSCHALT+ENTF &#8984; Cmd+X Das gewählte Objekt wird ausgeschnitten und in der Zwischenablage des Rechners abgelegt. Das kopierte Objekt kann später an einer anderen Stelle in derselben Präsentation, in eine andere Präsentation oder in ein anderes Programm eingefügt werden. Kopieren STRG+C, STRG+EINFG &#8984; Cmd+C Das gewählte Objekt wird in der Zwischenablage des Rechners abgelegt. Das kopierte Objekt kann später an einer anderen Stelle in derselben Präsentation eingefügt werden. Einfügen STRG+V, ⇧ UMSCHALT+EINFG &#8984; Cmd+V Das vorher kopierte Objekt wird aus der Zwischenablage des Rechners an der aktuellen Cursorposition eingefügt. Das Objekt kann vorher aus derselben Präsentation kopiert werden oder auch aus einem anderen Dokument oder Programm oder von einer Webseite. Hyperlink einfügen STRG+K ^ STRG+K, &#8984; Cmd+K Einen Hyperlink einfügen der an eine Webadresse oder eine bestimmte Stelle in der Präsentation weiterleitet. Format übertragen STRG+⇧ UMSCHALT+C ^ STRG+⇧ UMSCHALT+C, &#8984; Cmd+⇧ UMSCHALT+C Die Formatierung des gewählten Textabschnitts wird kopiert. Die kopierte Formatierung kann später auf einen anderen Textabschnitt in derselben Präsentation angewendet werden. Format übertragen STRG+⇧ UMSCHALT+V ^ STRG+⇧ UMSCHALT+V, &#8984; Cmd+⇧ UMSCHALT+V Wendet die vorher kopierte Formatierung auf den Text in der aktuellen Präsentation an. Auswahl mit der Maus Zum gewählten Abschnitt hinzufügen ⇧ UMSCHALT ⇧ UMSCHALT Starten Sie die Auswahl, halten Sie die Taste ⇧ UMSCHALT gedrückt und klicken Sie an der Stelle, wo Sie die Auswahl beenden möchten. Auswahl mithilfe der Tastatur Alles auswählen STRG+A ^ STRG+A, &#8984; Cmd+A Alle Folien (in der Folienliste) auswählen oder alle Objekte auf einer Folie (im Bereich für die Folienbearbeitung) oder den ganzen Text (im Textblock) - abhängig von der Cursorposition. Textabschnitt auswählen ⇧ UMSCHALT+→ ← ⇧ UMSCHALT+→ ← Den Text Zeichen für Zeichen auswählen. Text von der aktuellen Cursorposition bis zum Zeilenanfang auswählen ⇧ UMSCHALT+POS1 Einen Textabschnitt von der aktuellen Cursorposition bis zum Anfang der aktuellen Zeile auswählen. Text ab der Cursorposition bis Ende der Zeile auswählen ⇧ UMSCHALT+ENDE Einen Textabschnitt von der aktuellen Cursorposition bis zum Ende der aktuellen Zeile auswählen. Ein Zeichen nach rechts auswählen ⇧ UMSCHALT+→ ⇧ UMSCHALT+→ Das Zeichen rechts neben dem Mauszeiger wird ausgewählt. Ein Zeichen nach links auswählen ⇧ UMSCHALT+← ⇧ UMSCHALT+← Das Zeichen links neben dem Mauszeiger wird ausgewählt. Bis zum Wortende auswählen STRG+⇧ UMSCHALT+→ Einen Textfragment vom Cursor bis zum Ende eines Wortes wird ausgewählt. Bis zum Wortanfang auswählen STRG+⇧ UMSCHALT+← Einen Textfragment vom Cursor bis zum Anfang eines Wortes wird ausgewählt. Eine Reihe nach oben auswählen ⇧ UMSCHALT+↑ ⇧ UMSCHALT+↑ Eine Reihe nach oben auswählen (mit dem Cursor am Zeilenanfang). Eine Reihe nach unten auswählen ⇧ UMSCHALT+↓ ⇧ UMSCHALT+↓ Eine Reihe nach unten auswählen (mit dem Cursor am Zeilenanfang). Textformatierung Fett STRG+B ^ STRG+B, &#8984; Cmd+B Zuweisung der Formatierung Fett im gewählten Textabschnitt. Kursiv STRG+I ^ STRG+I, &#8984; Cmd+I Zuweisung der Formatierung Kursiv im gewählten Textabschnitt. Unterstrichen STRG+U ^ STRG+U, &#8984; Cmd+U Der gewählten Textabschnitt wird mit einer Linie unterstrichen. Durchgestrichen STRG+5 ^ STRG+5, &#8984; Cmd+5 Der gewählte Textabschnitt wird durchgestrichen. Tiefgestellt STRG+⇧ UMSCHALT+&gt; &#8984; Cmd+⇧ UMSCHALT+&gt; Der gewählte Textabschnitt wird verkleinert und tiefgestellt. Hochgestellt STRG+⇧ UMSCHALT+&lt; &#8984; Cmd+⇧ UMSCHALT+&lt; Der gewählte Textabschnitt wird verkleinert und hochgestellt wie z. B. in Bruchzahlen. Aufzählungsliste STRG+⇧ UMSCHALT+L ^ STRG+⇧ UMSCHALT+L, &#8984; Cmd+⇧ UMSCHALT+L Baiserend auf dem gewählten Textabschnitt wird eine Aufzählungsliste erstellt oder eine neue Liste begonnen. Formatierung entfernen STRG+␣ Leertaste Entfernt die Formatierung im gewählten Textabschnitt. Schrift vergrößern STRG+] ^ STRG+], &#8984; Cmd+] Vergrößert die Schrift des gewählten Textabschnitts um 1 Punkt. Schrift verkleinern STRG+[ ^ STRG+[, &#8984; Cmd+[ Verkleinert die Schrift des gewählten Textabschnitts um 1 Punkt. Zentriert ausrichten STRG+E Text zwischen dem linken und dem rechten Rand zentrieren. Blocksatz STRG+J Der Text im Absatz wird im Blocksatz ausgerichtet. Dabei wird zwischen den Wörtern ein zusätzlicher Abstand eingefügt, sodass der linke und der rechte Textrand an den Absatzrändern ausgerichtet sind. Rechtsbündig ausrichten STRG+R Der rechte Textrand verläuft parallel zum rechten Seitenrand, der linke Textrand bleibt unausgerichtet. Linksbündig ausrichten STRG+L Der linke Textrand verläuft parallel zum linken Seitenrand, der rechte Textrand bleibt unausgerichtet. Linken Einzug vergrößern STRG+M ^ STRG+M, &#8984; Cmd+M Der linke Seiteneinzug wird um einen Tabstopp vergrößert. Linken Einzug vergrößern STRG+⇧ UMSCHALT+M ^ STRG+⇧ UMSCHALT+M, &#8984; Cmd+⇧ UMSCHALT+M Der linke Seiteneinzug wird um einen Tabstopp verkleinert. Ein Zeichen nach links löschen ← Rücktaste ← Rücktaste Das Zeichen links neben dem Mauszeiger wird gelöscht. Ein Zeichen nach rechts löschen ENTF Fn+ENTF Das Zeichen rechts neben dem Mauszeiger wird gelöscht. Im Text navigieren Ein Zeichen nach links bewegen ← ← Der Mauszeiger bewegt sich ein Zeichen nach links. Ein Zeichen nach rechts bewegen → → Der Mauszeiger bewegt sich ein Zeichen nach rechts. Eine Reihe nach oben ↑ ↑ Der Mauszeiger wird eine Reihe nach oben verschoben. Eine Reihe nach unten ↓ ↓ Der Mauszeiger wird eine Reihe nach unten verschoben. Zum Anfang eines Wortes oder ein Wort nach links bewegen STRG+← &#8984; Cmd+← Der Mauszeiger wird zum Anfang eines Wortes oder ein Wort nach links verschoben. Ein Wort nach rechts bewegen STRG+→ &#8984; Cmd+→ Der Mauszeiger bewegt sich ein Wort nach rechts. Zum nächsten Platzhalter wechseln STRG+↵ Eingabetaste ^ STRG+↵ Zurück, &#8984; Cmd+↵ Zurück Zum nächsten Titel oder zum nächsten Textplatzhalter wechseln Handelt es sich um den letzten Platzhalter auf einer Folie, wird eine neue Folie mit demselben Folienlayout wie die ursprüngliche Folie eingefügt Zum Anfang einer Zeile springen POS1 POS1 Der Cursor wird an den Anfang der aktuellen Zeile verschoben. Zum Ende der Zeile springen ENDE ENDE Der Cursor wird an das Ende der aktuellen Zeile verschoben. Zum Anfang des Textfelds springen STRG+POS1 Der Cursor wird an den Anfang des aktuell bearbeiteten Textfelds verschoben. Zum Ende des Textfelds springen STRG+ENDE Der Cursor wird an das Ende des aktuell bearbeiteten Textfelds verschoben."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "Ansichtseinstellungen und Navigationswerkzeuge", 
        "body": "Der Präsentationseditor bietet mehrere Werkzeuge, um Ihnen die Ansicht und Navigation in Ihrer Präsentation zu erleichtern: Zoom, vorherige/nächste Folie, Anzeige der Foliennummer. Anzeigeeinstellungen anpassen Um die Standardansichtseinstellungen anzupassen und den bequemsten Modus für die Arbeit mit der Präsentation festzulegen, gehen Sie zur Registerkarte Ansicht und wählen Sie aus, welche Elemente der Benutzeroberfläche ausgeblendet oder angezeigt werden sollen. Auf der Registerkarte Ansicht können Sie die folgenden Optionen auswählen: Zoom, um den erforderlichen Zoomwert von 50 % bis 500 % aus der Drop-Down-Liste einzustellen. An Folie anpassen, um die gesamte Folie an den sichtbaren Teil des Arbeitsbereichs anzupassen. An Breite anpassen, um die Folienbreite an den sichtbaren Teil des Arbeitsbereichs anzupassen. Thema der Benutzeroberfläche - wählen Sie eines der verfügbaren Oberflächenthemen aus dem Drop-Down-Menü: Wie im System, Hell, Klassisch Hell, Dunkel, Dunkler Kontrast. Notizen - wenn deaktiviert, wird der Notizenbereich unter der Folie ausgeblendet. Dieser Abschnitt kann auch durch Ziehen mit dem Mauszeiger ein-/ausgeblendet werden. Lineale - wenn deaktiviert, werden Lineale ausgeblendet, die zum Einrichten von Tabstopps und Absatzeinzügen in den Textfeldern verwendet werden. Um die ausgeblendeten Lineale anzuzeigen, klicken Sie erneut auf diese Option. Führungslinien – wählen Sie den bevorzugten Führungstyp, um Objekte richtig auf der Folie zu positionieren. Die verfügbaren Optionen sind vertikal, horizontal und Smart-Führungslinien für eine bessere Positionierung. Gitternetzlinien – wählen Sie die bevorzugte Gittergröße aus verfügbaren Vorlagen oder legen Sie eine benutzerdefinierte Größe fest, und ob Sie Objekte am Gitter ausrichten möchten oder nicht, um die Objektpositionierung zu verbessern. Symbolleiste immer anzeigen - wenn diese Option deaktiviert ist, wird die obere Symbolleiste, die Befehle enthält, ausgeblendet, während die Registerkartennamen sichtbar bleiben. Sie können auch einfach auf eine beliebige Registerkarte doppelklicken, um die obere Symbolleiste auszublenden oder wieder anzuzeigen. Statusleiste - wenn deaktiviert, wird die unterste Leiste ausgeblendet, in der sich die Schaltflächen Folienzahlanzeige und Zoom befinden. Um die ausgeblendete Statusleiste anzuzeigen, klicken Sie erneut auf diese Option. Linkes Bedienfeld - wenn diese Option deaktiviert ist, wird der linke Bereich ausgeblendet, in dem sich die Schaltflächen Suchen, Kommentare usw. befinden. Aktivieren Sie dieses Kontrollkästchen, um das linke Bedienfeld anzuzeigen. Rechtes Bedienungsfeld - wenn diese Option deaktiviert ist, wird das rechte Bedienfeld ausgeblendet, in dem sich Einstellungen befinden. Aktivieren Sie dieses Kontrollkästchen, um das rechte Bedienfeld anzuzeigen. Die rechte Seitenleiste ist standartmäßig minimiert. Um sie zu erweitern, wählen Sie ein beliebiges Objekt/Folie aus und klicken Sie auf das Symbol des aktuell aktivierten Tabs auf der rechten Seite. Um die Seitenleiste wieder zu minimieren, klicken Sie erneut auf das Symbol. Die Breite der linken Randleiste wurd durch Ziehen und Loslassen mit dem Mauszeiger angepasst: Bewegen Sie den Mauszeiger über den Rand der linken Seitenleiste, so dass dieser sich in einen bidirektionalen Pfeil verwandelt und ziehen Sie den Rand nach links, um die Seitenleiste zu verkleinern und nach rechs um sie zu erweitern. Verwendung der Navigationswerkzeuge Mithilfe der folgenden Werkzeuge können Sie durch Ihre Präsentation navigieren: Die Zoom-Funktion befindet sich in der rechten unteren Ecke und dient zum Vergrößern und Verkleinern der aktuellen Präsentation. Um den in Prozent angezeigten aktuellen Zoomwert zu ändern, klicken Sie darauf und wählen Sie eine der verfügbaren Zoomoptionen (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) aus der Liste oder klicken Sie auf Vergrößern oder Verkleinern . Klicken Sie auf das Symbol Breite anpassen , um die Folienbreite der Präsentation an den sichtbaren Teil des Arbeitsbereichs anzupassen. Um die ganze Folie der Präsentation an den sichtbaren Teil des Arbeitsbereichs anzupassen, klicken Sie auf das Symbol Folie anpassen . Zoomeinstellungen sind auch auf der Registerkarte Ansicht verfügbar. Sie können einen Standard-Zoomwert festlegen. Wechseln Sie in der oberen Symbolleiste in die Registerkarte Datei, wählen Sie die Option Erweiterte Einstellungen..., wählen Sie den gewünschten Zoom-Standard-Wert aus der Liste aus und klicken sie auf Anwenden. Um während der Bearbeitung der Präsentation zur nächsten Folie zu wechseln oder zur vorherigen Folie zurückzukehren, können Sie über die Schaltflächen und oben und unten in der vertikalen Bildlaufleiste am rechten Folienrand für die Navigation verwenden. Die Folienzahlanzeige stellt die aktuelle Folie als Teil aller Folien in der aktuellen Präsentation dar (Folie „n“ von „nn“). Wenn Sie auf die Anzeige der Foliennummer klicken, öffnet sich ein Fenster und Sie können eine gewünschte Foliennummer angeben, um direkt auf diese Folie zu wechseln. Wenn Sie die Statusleiste ausgeblendet haben, ist dieser Schnellzugriff nicht zugänglich."
    },
   {
        "id": "HelpfulHints/Password.htm", 
        "title": "Präsentationen mit einem Kennwort schützen", 
        "body": "Sie können Ihre Präsentationen mit einem Kennwort schützen, das Ihre Mitautoren benötigen, um zum Bearbeitungsmodus zu wechseln. Das Kennwort kann später geändert oder entfernt werden. Wenn Sie das Kennwort verlieren oder vergessen, lässt es sich nicht mehr wiederherstellen. Bewahren Sie es an einem sicheren Ort auf. Kennwort erstellen öffnen Sie die Registerkarte Datei in der oberen Symbolleiste, wählen Sie die Option Schützen aus, klicken Sie auf die Schaltfläche Kennwort hinzufügen, geben Sie das Kennwort im Feld Kennwort ein und wiederholen Sie es im Feld Kennwort wiederholen nach unten, dann klicken Sie auf OK. Klicken Sie auf , um die Kennwortzeichen bei der Eingabe anzuzeigen oder auszublenden. Kennwort ändern öffnen Sie die Registerkarte Datei in der oberen Symbolleiste, wählen Sie die Option Schützen aus, klicken Sie auf die Schaltfläche Kennwort ändern, geben Sie das neue Kennwort im Feld Kennwort ein und wiederholen Sie es im Feld Kennwort wiederholen nach unten, dann klicken Sie auf OK. Kennwort löschen öffnen Sie die Registerkarte Datei in der oberen Symbolleiste, wählen Sie die Option Schützen aus, klicken Sie auf die Schaltfläche Kennwort löschen."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Suchen und Ersetzen", 
        "body": "Um im Präsentationseditor nach Zeichen, Wörtern oder Phrasen zu suchen, klicken Sie auf das Symbol in der linken Seitenleiste, das Symbol in der oberen rechten Ecke, oder verwenden Sie die Tastenkombination Strg+F (Command+F für MacOS), um das kleine Suchfeld zu öffnen, oder die Tastenkombination Strg+H, um das vollständige Suchfenster zu öffnen. Ein kleiner Suchen-Bereich öffnet sich in der oberen rechten Ecke des Arbeitsbereichs. Um auf die erweiterten Einstellungen zuzugreifen, klicken Sie auf das Symbol oder verwenden Sie die Tastenkombination Strg+H. Das Fenster Suchen und ersetzen wird geöffnet: Geben Sie Ihre Anfrage in das entsprechende Dateneingabefeld Suchen ein. Wenn Sie ein oder mehrere Vorkommen der gefundenen Zeichen ersetzen müssen, geben Sie den Ersetzungstext in das entsprechende Dateneingabefeld Ersetzen durch ein oder verwenden Sie die Tastenkombination Strg+H. Sie können wählen, ob Sie ein einzelnes derzeit markiertes Vorkommen oder alle Vorkommen ersetzen möchten, indem Sie auf die entsprechenden Schaltflächen Ersetzen und Alle ersetzen klicken. Um zwischen den gefundenen Vorkommen zu navigieren, klicken Sie auf eine der Pfeilschaltflächen. Die Suche wird entweder am Anfang der Präsentation (wenn Sie auf die Schaltfläche klicken) oder am Ende der Präsentation (wenn Sie auf die Schaltfläche klicken) von der aktuellen Position aus durchgeführt. Geben Sie Suchparameter an, indem Sie die erforderlichen Optionen unter den Eingabefeldern aktivieren: Die Option Groß-/Kleinschreibung beachten wird verwendet, um nur die Vorkommen zu finden, die in der gleichen Groß-/Kleinschreibung wie Ihre Anfrage eingegeben wurden (z. B. wenn Ihre Anfrage „Editor“ lautet und diese Option ausgewählt ist, werden Wörter wie „Editor“ oder „EDITOR“ usw. nicht gefunden). Die Option Nur ganze Wörter wird verwendet, um nur ganze Wörter hervorzuheben. Die erste Folie in der gewählten Richtung, die den Suchbegriff enthält, wird in der Folienliste hervorgehoben und im Arbeitsbereich angezeigt. Wenn diese Folie nicht das gewünschte Ergebnisse enthält, klicken Sie erneut auf den Pfeil, um die nächste Folie mit dem gewünschten Suchbegriff zu finden."
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Rechtschreibprüfung", 
        "body": "Der Präsentationseditor bietet Ihnen die Möglichkeit, die Rechtschreibung Ihres Textes in einer bestimmten Sprache zu überprüfen und Fehler während der Bearbeitung zu korrigieren. Ab Version 6.3 unterstützen die ONLYOFFICE-Editoren das SharedWorker Interface für einen besseren Betrieb ohne großen Speicherverbrauch. Wenn Ihr Browser SharedWorker nicht unterstützt, ist nur Worker aktiv. Weitere Informationen zu SharedWorker finden Sie in diesem Artikel. Wählen Sie zunächst die Sprache für Ihre Präsentation aus. Klicken Sie auf der rechten Seite der Statusleiste auf . Wählen Sie nun im angezeigten Fenster die gewünschte Sprache und klicken Sie auf OK. Die ausgewählte Sprache wird auf die gesamte Präsentation angewandt. Um für einen beliebigen Textabschnitt in der Präsentation eine andere Sprache auszuwählen, markieren Sie den entsprechenden Abschnitt mit der Maus und klicken Sie anschließend auf das Menü in der Statusleiste. Rechtschreibprüfung aktivieren: klicken Sie in der Statusleiste auf das Symbol Rechtschreibprüfung oder öffnen Sie in der oberen Symbolleiste die Registerkarte Datei und wählen Sie die Option Erweiterte Einstellungen, setzen Sie das Häkchen in der Box Rechtschreibprüfung aktivieren und klicken Sie auf Übernehmen. Falsch geschriebene Wörter werden mit einer roten Linie unterstrichen. Klicken Sie mit der rechten Maustaste auf das entsprechende Wort, um das Kontextmenü zu aktivieren, und: wählen Sie eine der verfügbaren Varianten aus, um das falsch geschriebene Wort durch die korrekte Rechtschreibung zu ersetzen. Wenn zu viel Möglichkeiten vorliegen, wird die Option Weitere... im Menü angezeigt; wählen Sie die Option Ignorieren, um ein Wort zu überspringen und die rote Linie auszublenden oder Alle ignorieren, um ein bestimmtes Fehlerergebnis für den gesamten Text zu überspringen; wenn das aktuelle Wort im Wörterbuch fehlt, können Sie es dem benutzerdefinierten Wörterbuch hinzufügen. Dieses Wort wird beim nächsten Mal nicht als Fehler behandelt. Diese Option ist in der Desktop-Version verfügbar; wählen Sie für dieses Wort eine andere Sprache. Rechtschreibprüfung deaktivieren: klicken Sie in der Statusleiste auf das Symbol Rechtschreibprüfung oder öffnen Sie in der oberen Symbolleiste die Registerkarte Datei und wählen Sie die Option Erweiterte Einstellungen, entfernen Sie das Häkchen in der Box Rechtschreibprüfung aktivieren und klicken Sie auf Übernehmen."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Unterstützte Formate elektronischer Präsentationen", 
        "body": "Eine Präsentation besteht aus einer Reihe von Folien, die verschiedene Arten von Inhalten enthalten können z. B. Bilder, Mediendateien, Text, Effekte usw. Der Präsentationseditor unterstützt die folgenden Formate: Beim Hochladen oder Öffnen der Datei für die Bearbeitung wird sie ins Office-Open-XML-Format (PPTX) konvertiert. Dies wird gemacht, um die Dateibearbeitung zu beschleunigen und die Interfunktionsfähigkeit zu erhöhen. Die folgende Tabelle enthält die Formate, die zum Anzeigen und/oder zur Bearbeitung geöffnet werden können. Formate Beschreibung Nativ anzeigen Anzeigen nach Konvertierung in OOXML Nativ bearbeiten Bearbeitung ach Konvertierung in OOXML ODP OpenDocument Presentation Dateiformat, das mit der Anwendung Impress erstellte Präsentationen darstellt; diese Anwendung ist ein Bestandteil des OpenOffice-Pakets + + OTP OpenDocument-Präsentationsvorlage OpenDocument-Dateiformat für Präsentationsvorlagen. Eine OTP-Vorlage enthält Formatierungseinstellungen, Stile usw. und kann zum Erstellen mehrerer Präsentationen mit derselben Formatierung verwendet werden. + + POTX PowerPoint Office Open XML Dokumenten-Vorlage Gezipptes, XML-basiertes, von Microsoft für Präsentationsvorlagen entwickeltes Dateiformat. Eine POTX-Vorlage enthält Formatierungseinstellungen, Stile usw. und kann zum Erstellen mehrerer Präsentationen mit derselben Formatierung verwendet werden. + + PPSX Microsoft PowerPoint Slide Show Präsentationsdateiformat, das für die Wiedergabe von Slideshows verwendet wird + + PPT Dateiformat, das in Microsoft PowerPoint verwendet wird + + PPTX Office Open XML Presentation Gezipptes, XML-basiertes, von Microsoft entwickeltes Dateiformat zur Präsentation von Kalkulationstabellen, Diagrammen, Präsentationen und Textverarbeitungsdokumenten + + Die folgende Tabelle enthält die Formate, in denen Sie eine Präsentation über das Menü Datei -> Herunterladen als herunterladen können. Eingabeformat Kann heruntergeladen werden als ODP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX OTP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX POTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPSX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPT JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX Sie können sich auch auf die Conversion-Matrix auf api.onlyoffice.com beziehen, um die Möglichkeiten zu sehen, Ihre Präsentationen in die bekanntesten Dateiformate zu konvertieren."
    },
   {
        "id": "HelpfulHints/UsingChat.htm", 
        "title": "Kommunikation in Echtzeit", 
        "body": "Der Präsentationseditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beibehalten: Sie können die Dateien und Ordner freigeben; an Präsentationen in Echtzeit zusammenarbeiten; bestimmte Teile Ihrer Präsentationen, die zusätzliche Eingaben Dritter erfordern, kommentieren; die Versionen von Präsentationen für die zukünftige Verwendung speichern. Im Präsentationseditor können Sie mit Ihren Mitbearbeitern in Echtzeit kommunizieren, indem Sie das integrierte Chat-Tool sowie eine Reihe nützlicher Plugins verwenden, z. B. Telegram oder Rainbow. Um auf das Chat-Tool zuzugreifen und eine Nachricht für andere Benutzer zu hinterlassen: Klicken Sie auf das Symbol in der linken Symbolleiste. Geben Sie Ihren Text in das entsprechende Feld unten ein. Klicken Sie die Schaltfläche Senden. Die Chat-Nachrichten werden nur während einer Sitzung gespeichert. Um den Inhalt der Präsentation zu diskutieren, verwenden Sie besser Kommentare, die bis zu ihrer Löschung gespeichert werden. Alle von Benutzern hinterlassenen Nachrichten werden auf der linken Seite angezeigt. Wenn es neue Nachrichten gibt, die Sie noch nicht gelesen haben, sieht das Chat-Symbol so aus - . Um das Fenster mit Chat-Nachrichten zu schließen, klicken Sie erneut auf das Symbol ."
    },
   {
        "id": "HelpfulHints/VersionHistory.htm", 
        "title": "Versionshistorie", 
        "body": "Der Präsentationseditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beibehalten: Sie können die Dateien und Ordner freigeben; an Präsentationen in Echtzeit zusammenarbeiten; direkt im Editor kommunizieren; bestimmte Teile Ihrer Präsentationen, die zusätzliche Eingaben Dritter erfordern, kommentieren. Im Präsentationseditor können Sie den Versionsverlauf der Präsentation anzeigen, an der Sie mitarbeiten. Versionshistorie anzeigen: Um alle an der Präsentation vorgenommenen Änderungen anzuzeigen: Gehen Sie zur Registerkarte Datei. Wählen Sie in der linken Seitenleiste die Option Versionshistorie aus oder Gehen Sie zur Registerkarte Zusammenarbeit. Öffnen Sie die Versionshistorie mithilfe des Symbols  Versionshistorie in der oberen Symbolleiste. Sie sehen die Liste der Präsentationsversionen und -Revisionen mit Angabe des Autors jeder Version/Revision sowie Erstellungsdatum und -zeit. Bei Präsentationsversionen wird auch die Versionsnummer angegeben (z. B. ver. 2). Versionen anzeigen: Um genau zu wissen, welche Änderungen in jeder einzelnen Version/Revision vorgenommen wurden, können Sie die gewünschte Version anzeigen, indem Sie in der linken Seitenleiste darauf klicken. Die vom Autor der Version/Revision vorgenommenen Änderungen werden mit der Farbe gekennzeichnet, die neben dem Namen des Autors in der linken Seitenleiste angezeigt wird. Um zur aktuellen Version der Präsentation zurückzukehren, verwenden Sie die Option Historie schließen oben in der Versionsliste. Versionen wiederherstellen: Wenn Sie zu einer der vorherigen Versionen der Präsentation zurückkehren müssen, klicken Sie auf den Link Wiederherstellen unter der ausgewählten Version/Revision. Um mehr über das Verwalten von Versionen und Zwischenrevisionen sowie das Wiederherstellen früherer Versionen zu erfahren, lesen Sie bitte diesen Artikel."
    },
   {
        "id": "ProgramInterface/AnimationTab.htm", 
        "title": "Registerkarte Animation", 
        "body": "Die Registerkarte Animation im Präsentationseditor ermöglicht Ihnen die Verwaltung von Animationseffekten. Sie können Animationseffekte hinzufügen, bestimmen, wie sich die Animationseffekte bewegen, und andere Parameter für Animationseffekte konfigurieren, um Ihre Präsentation anzupassen. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Sie können: Animationseffekte auswählen, für jeden Animationseffekt die geeigneten Bewegungsparameter festlegen, mehrere Animationen hinzufügen, die Reihenfolge der Animationseffekte mit den Optionen Aufwärts schweben oder Abwärts schweben ändern, Vorschau des Animationseffekts aktivieren, die Timing-Optionen wie Dauer, Verzögern und Wiederholen für Animationseffekte festlegen, die Option Zurückspulen aktivieren und deaktivieren."
    },
   {
        "id": "ProgramInterface/CollaborationTab.htm", 
        "title": "Registerkarte Zusammenarbeit", 
        "body": "Unter der Registerkarte Zusammenarbeit im Präsentationseditor können Sie die Zusammenarbeit in der Präsentation organisieren. In der Online-Version können Sie die Datei mit jemanden teilen, einen gleichzeitigen Bearbeitungsmodus auswählen, Kommentare verwalten. In der Desktop-Version können Sie Kommentare verwalten. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Sie können: Freigabeeinstellungen festlegen (nur in der Online-Version verfügbar), zwischen den Modi für die gemeinsame Bearbeitung Schnell und Formal wechseln (nur in der Online-Version verfügbar), Kommentare in die Präsentation einfügen oder löschen, den Chat öffnen (nur in der Online-Version verfügbar), Versionsverläufe nachverfolgen (nur in der Online-Version verfügbar)."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "Registerkarte Datei", 
        "body": "Über die Registerkarte Datei im Präsentationseditor können Sie einige grundlegende Vorgänge in der aktuellen Datei durchführen. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Sie können: in der Online-Version: Die aktuelle Datei speichern (falls die Option Automatisch speichern deaktiviert ist), herunterladen als (Speichern des Dokuments im ausgewählten Format auf der Festplatte des Computers), eine Kopie speichern als (Speichern einer Kopie des Dokuments im Portal im ausgewählten Format), drucken oder umbenennen, in der Desktop-Version: Die aktuelle Datei mit der Option Speichern unter Beibehaltung des aktuellen Dateiformats und Speicherorts speichern oder die aktuelle Datei unter einem anderen Namen, Speicherort oder Format speichern. Nutzen Sie dazu die Option Speichern als. Weiter haben Sie die Möglichkeit, die Datei zu drucken. die Datei mit einem Kennwort schützen, das Kennwort ändern oder löschen, die Datei mit einer digitalen Signatur schützen (nur in der Desktop-Version verfügbar), erstellen Sie eine neue Präsentation oder öffnen Sie eine kürzlich bearbeitete Präsentation (nur in der Online-Version verfügbar), allgemeine Informationen über die Präsentation einsehen oder die Dateieinstellungen konfigurieren, Zugriffsrechte verwalten (nur in der Online-Version verfügbar), auf die Erweiterten Einstellungen des Editors zugreifen, in der Desktop-Version: Den Ordner öffnen, wo die Datei gespeichert ist; nutzen Sie dazu das Fenster Datei-Explorer. In der Online-Version: haben Sie außerdem die Möglichkeit, den Ordner des Moduls Dokumente, in dem die Datei gespeichert ist, in einem neuen Browser-Fenster zu öffnen."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Registerkarte Startseite", 
        "body": "Die Registerkarte Startseite im Präsentationseditor wird standardmäßig geöffnet, wenn Sie eine beliebige Präsentation öffnen. Hier können Sie allgemeine Folienparameter festlegen, Text formatieren und Objekte einfügen und diese ausrichten und anordnen. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Sie können: Folien verwalten und eine Bildschirmpräsentation starten, Text in einem Textfeld formatieren, Textfelder, Bilder und Formen einfügen, Objekte auf einer Folie anordnen und ausrichten, die Textformatierung kopieren/entfernen, ein Thema, Farbschema oder die Foliengröße ändern."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Registerkarte Einfügen", 
        "body": "Über die Registerkarte Einfügen im Präsentationseditor können Sie visuelle Objekte und Kommentare zu Ihrer Präsentation hinzufügen. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Sie können: Tabellen einfügen, Textfelder und TextArt-Objekte, Bilder, Formen und Diagramme einfügen, Kommentare und Hyperlinks einfügen, Fußzeile, Datum und Uhrzeit, Foliennummer einfügen, Gleichungen und Symbole einfügen, auf Ihrer Festplatte Audio- und Videodateien einfügen (nur im Desktop-Version verfügbar, für Mac OS nicht verfügbar) Um eine Videodatei abzuspielen, sollen Sie die Codecs installieren, z.B. K-Lite."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Registerkarte Plugins", 
        "body": "Die Registerkarte Plugins im Präsentationseditor ermöglicht den Zugriff auf erweiterte Bearbeitungsfunktionen mit verfügbaren Komponenten von Drittanbietern. Unter dieser Registerkarte können Sie auch Makros festlegen, um Routinevorgänge zu vereinfachen. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Durch Anklicken der Schaltfläche Einstellungen öffnet sich das Fenster, in dem Sie alle installierten Plugins anzeigen und verwalten sowie eigene Plugins hinzufügen können. Durch Anklicken der Schaltfläche Makros öffnet sich das Fenster, in dem Sie Ihre eigenen Makros erstellen und ausführen können. Um mehr über Makros zu erfahren, lesen Sie bitte unsere API-Dokumentation. Derzeit stehen folgende Plugins zur Verfügung: Senden ermöglicht das Senden der Präsentation per E-Mail mit dem Standard-Desktop-Mail-Client (nur in der Desktop-Version verfügbar), Code hervorheben - Hervorhebung der Syntax des Codes durch Auswahl der erforderlichen Sprache, des Stils, der Hintergrundfarbe, Foto-Editor - Bearbeitung von Bildern: schneiden, spiegeln, drehen, Linien und Formen zeichnen, Symbole und Texte einfügen, Maske laden und die Filter verwenden, z.B. Graustufe, invertieren, Sepia, Blur, schärfen, Emboss usw., Thesaurus - mit diesem Plugin können Synonyme und Antonyme eines Wortes gesucht und durch das ausgewählte Wort ersetzt werden, Übersetzer - Übersetzen von ausgewählten Textabschnitten in andere Sprachen, Dieses Plugin funktioniert nicht im Internet Explorer. YouTube - Einbetten von YouTube-Videos in der Präsentation. Um mehr über Plugins zu erfahren, lesen Sie bitte unsere API-Dokumentation. Alle derzeit als Open-Source verfügbaren Plugin-Beispiele sind auf GitHub verfügbar."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Einführung in die Benutzeroberfläche des Präsentationseditors", 
        "body": "Einführung in die Benutzeroberfläche des Präsentationseditor Der Präsentationseditor verfügt über eine Benutzeroberfläche mit Registerkarten, in der Bearbeitungsbefehle nach Funktionalität in Registerkarten gruppiert sind. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Die Oberfläche des Editors besteht aus folgenden Hauptelementen: In der Kopfzeile des Editors werden das Logo, geöffnete Dokumente , der Name der Präsentation sowie die Menü-Registerkarten angezeigt. Im linken Bereich der Kopfzeile des Editors finden Sie die Schaltflächen Speichern, Datei drucken, Rückgängig machen und Wiederholen. Im rechten Bereich der Kopfzeile des Editors werden der Benutzername und die folgenden Symbole angezeigt: Dateispeicherort öffnen - in der Desktop-Version können Sie den Ordner öffnen, in dem die Datei gespeichert ist; nutzen Sie dazu das Fenster Datei-Explorer. In der Online-Version haben Sie außerdem die Möglichkeit den Ordner des Moduls Dokumente, in dem die Datei gespeichert ist, in einem neuen Browser-Fenster zu öffnen. Freigeben (nur in der Online-Version verfügbar) - hier können Sie Zugriffsrechte für die in der Cloud gespeicherten Dokumente festlegen. Als Favorit kennzeichnen - klicken Sie auf den Stern, um eine Datei zu den Favoriten hinzuzufügen, damit Sie sie leichter finden können. Die hinzugefügte Datei ist nur eine Verknüpfung, sodass die Datei selbst am ursprünglichen Speicherort gespeichert bleibt. Durch das Löschen einer Datei aus den Favoriten wird die Datei nicht an ihrem ursprünglichen Speicherort entfernt. Suchen - ermöglicht das Durchsuchen der Präsentation nach einem bestimmten Wort oder Symbol usw. Abhängig von der ausgewählten Registerkarte werden in der oberen Symbolleiste eine Reihe von Bearbeitungsbefehlen angezeigt. Aktuell stehen die folgenden Registerkarten zur Verfügung: Datei, Startseite, Einfügen, Übergänge, Animation, Zusammenarbeit, Ansicht, Schützen und Plugins. Die Befehle Kopieren, Einfügen, Ausschneiden und Alles auswählen stehen unabhängig von der ausgewählten Registerkarte jederzeit im linken Teil der oberen Symbolleiste zur Verfügung. In der Statusleiste am unteren Rand des Editorfensters finden Sie das Symbol für den Beginn der Bildschirmpräsentation sowie diverse Navigationswerkzeuge: z. B. Foliennummer und Zoom. Außerdem werden in der Statusleiste Benachrichtigungen vom System angezeigt (wie beispielsweise „Alle Änderungen wurden gespeichert“, „Verbindung verloren“, wenn keine Verbindung besteht und der Editor versucht, die Verbindung wiederherzustellen usw.) und Sie haben die Möglichkeit, die Textsprache festzulegen und die Rechtschreibprüfung zu aktivieren. Die Symbole in der linken Seitenleiste: mithilfe der Funktion können Sie den Text suchen und ersetzen, - ermöglicht das Anzeigen von Folien und das Navigieren, öffnet die Kommentarfunktion, - (nur in der Online-Version verfügbar) hier können Sie das Chatfenster öffnen, - (nur in der Online-Version verfügbar) kontaktieren Sie under Support-Team, - (nur in der Online-Version verfügbar) sehen Sie sich die Programminformationen an. Über die rechte Randleiste können zusätzliche Parameter von verschiedenen Objekten angepasst werden. Wenn Sie ein bestimmtes Objekt auf der Folie auswählen, wird die entsprechende Schaltfläche auf der rechten Randleiste aktiviert. Um die Randleiste zu erweitern, klicken Sie diese Schaltfläche an. Die horizontale und vertikale Lineale: Sie können dabei Objekte auf einer Folie präzis positionieren und Tabulatoren und Absätze in Textfeldern festlegen. Über den Arbeitsbereich enthält man den Präsentationsinhalt; hier können Sie die Daten eingeben und bearbeiten. Mithilfe der Bildaufleiste rechts können Sie in der Präsentation hoch und runter navigieren. Zur Vereinfachung können Sie bestimmte Komponenten verbergen und bei Bedarf erneut anzeigen. Weitere Informationen zum Anpassen der Ansichtseinstellungen finden Sie auf dieser Seite."
    },
   {
        "id": "ProgramInterface/TransitionsTab.htm", 
        "title": "Registerkarte Übergänge", 
        "body": "Auf der Registerkarte Übergänge im Präsentationseditor können Sie Folienübergänge verwalten. Sie können Übergangseffekte hinzufügen, die Übergangsgeschwindigkeit einstellen und andere Folienübergangsparameter konfigurieren, um Ihre Präsentation anzupassen. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Sie können: einen Übergangseffekt wählen, für jeden Übergangseffekt geeignete Parameter festlegen, Übergangsdauer festlegen, einen Übergang in der Vorschau anzeigen, angeben, wie lange die Folie angezeigt werden soll, indem Sie die Optionen Bei Klicken beginnen und Verzögern aktivieren, den Übergang auf alle Folien anwenden, indem Sie auf die Schaltfläche Auf alle Folien anwenden klicken."
    },
   {
        "id": "ProgramInterface/ViewTab.htm", 
        "title": "Registerkarte Ansicht", 
        "body": "Auf der Registerkarte Ansicht im Präsentationseditor können Sie verwalten, wie Ihr Dokument aussieht, während Sie daran arbeiten. Dialogbox Online-Präsentationseditor: Dialogbox Desktop-Präsentationseditor: Auf dieser Registerkarte sind die folgenden Funktionen verfügbar: Zoom ermöglicht das Vergrößern und Verkleinern Ihres Dokuments. Folie anpassen ermöglicht es, die Größe der Folie so zu ändern, dass der Bildschirm die gesamte Folie anzeigt. Breite anpassen ermöglicht es, die Folie so zu skalieren, dass sie an die Breite des Bildschirms angepasst wird. Thema der Benutzeroberfläche ermöglicht es, das Design der Benutzeroberfläche zu ändern, indem Sie eine der Optionen auswählen: Wie im System, Hell, Klassisch Hell, Dunkel, Dunkler Kontrast. Mit den folgenden Optionen können Sie die anzuzeigenden oder auszublendenden Elemente konfigurieren. Aktivieren Sie die Elemente, um sie sichtbar zu machen: Notizen, um das Notizenfeld immer sichtbar zu machen. Lineale, um Lineale immer sichtbar zu machen. Führungslinien und Gitternetzlinien zum richtigen Positionieren von Objekten auf der Folie. Symbolleiste immer anzeigen, um die obere Symbolleiste immer sichtbar zu machen. Statusleiste, um die Statusleiste immer sichtbar zu machen. Linkes Bedienfeld, um das linke Bedienfeld immer sichtbar zu machen. Rechtes Bedienungsfeld, um das rechte Bedienfeld immer sichtbar zu machen."
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Hyperlink einfügen", 
        "body": "Einfügen eines Hyperlinks im Präsentationseditor Positionieren Sie Ihren Mauszeiger an der Stelle im Textfeld, die Sie in den Link integrieren möchten. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie auf das Symbol Hyperlink in der oberen Symbolleiste. Sie können nun Fenster Einstellungen Hyperlink, die Parameter für den Hyperlink festlegen: wählen Sie den Linktyp, den Sie einfügen möchten: Verwenden Sie die Option Externer Link und geben Sie eine URL im Format http://www.example.com in das Feld Verknüpfen mit unten ein, wenn Sie möchten einen Hyperlink hinzufügen, der zu einer externen Website führt. Wenn Sie einen Hyperlink zu einer lokalen Datei hinzufügen müssen, geben Sie die URL in den Datei://Pfad/Präsentation.pptx (für Windows) oder Datei:///Pfad/Präsentation.pptx (für MacOS und Linux) Format in das Feld Verknüpfen mit unten ein. Der Hyperlink Datei://Pfad/Präsentation.pptx oder Datei:///Pfad/Präsentation.pptx kann nur in der Desktop-Version des Editors geöffnet werden. Im Web-Editor können Sie den Link nur hinzufügen, ohne ihn öffnen zu können. Wenn Sie einen Hyperlink hinzufügen möchten, der zu einer bestimmten Folie in der aktuellen Präsentation führt, wählen Sie die Option Folie aus dieser Präsentation und geben Sie die gewünschte Folie an. Die folgenden Optionen stehen Ihnen zur Verfügung: Nächste Folie, Vorherige Folie, Erste Folie, Letze Folie, Folie Angezeigter Text - geben Sie einen Text ein, der klickbar wird und zu der im oberen Feld angegebenen Webadresse/Folie führt. QuickInfo - geben Sie einen Text ein, der in einem kleinen Dialogfenster angezeigt wird und den Nutzer über den Inhalt des Verweises informiert. Klicken Sie auf OK. Um einen Hyperlink hinzuzufügen und die Einstellungen zu öffnen, können Sie auch mit der rechten Maustaste an die gewünschte Stelle klicken und die Option Hyperlink im Kontextmenü auswählen oder Sie positionieren den Cursor an der gewünschten Position und drücken die Tastenkombination STRG+K. Es ist auch möglich, ein Zeichen, Wort oder eine Wortverbindung mit der Maus oder über die Tastatur auszuwählen. Klicken Sie anschließend in der Registerkarte Einfügen auf Hyperlink oder klicken Sie mit der rechten Maustaste auf die Auswahl und wählen Sie im Kontextmenü die Option Hyperlink aus. Danach öffnet sich das oben dargestellte Fenster und im Feld Angezeigter Text erscheint der ausgewählte Textabschnitt. Wenn Sie den Mauszeiger über den eingefügten Hyperlink bewegen, wird der von Ihnen im Feld QuickInfo eingebene Text angezeigt. Sie können dem Link folgen, indem Sie die Taste STRG drücken und dann auf den Link in Ihrer Präsentation klicken. Um den hinzugefügten Hyperlink zu bearbeiten oder zu entfernen, klicken Sie diesen mit der rechten Maustaste an, wählen Sie die Option Hyperlink im Kontextmenü und wählen Sie anschließend den gewünschten Vorgang aus - Hyperlink bearbeiten oder Hyperlink entfernen."
    },
   {
        "id": "UsageInstructions/AddingAnimations.htm", 
        "title": "Animationen hinzufügen", 
        "body": "Animation ist ein visueller Effekt, mit dem Sie Text, Objekte und Grafiken animieren können, um Ihre Präsentation dynamischer zu gestalten und wichtige Informationen hervorzuheben. Sie können die Bewegung, Farbe und Größe von Text, Objekten und Grafiken verwalten. Animationseffekt anwenden Wechseln Sie in der oberen Symbolleiste zur Registerkarte Animation. Wählen Sie einen Text, ein Objekt oder ein Grafikelement aus, um den Animationseffekt darauf anzuwenden. Wählen Sie einen Animationseffekt aus der Animationsgalerie aus. Wählen Sie die Bewegungsrichtung des Animationseffekts aus, indem Sie neben der Animationsgalerie auf Parameter klicken. Die Parameter in der Liste hängen vom angewendeten Effekt ab. Sie können Animationseffekte auf der aktuellen Folie in der Vorschau anzeigen. Standardmäßig werden Animationseffekte automatisch abgespielt, wenn Sie sie zu einer Folie hinzufügen, aber Sie können sie deaktivieren. Klicken Sie auf der Registerkarte Animation auf das Drop-Down-Menü Vorschau und wählen Sie einen Vorschaumodus aus: Vorschau, um eine Vorschau anzuzeigen, wenn Sie auf die Schaltfläche Vorschau klicken. AutoVorschau, um automatisch eine Vorschau anzuzeigen, wenn Sie einer Folie eine Animation hinzufügen. Typen von Animationen Alle Animationseffekte sind in der Animationsgalerie aufgelistet. Klicken Sie auf den Drop-Down-Pfeil, um sie zu öffnen. Jeder Animationseffekt wird durch ein sternförmiges Symbol dargestellt. Die Animationen sind nach dem Zeitpunkt ihres Auftretens gruppiert: Eingangseffekte bestimmen, wie Objekte auf einer Folie erscheinen, und werden in der Galerie grün gefärbt. Hervorhebungseffekte ändern die Größe oder Farbe des Objekts, um ein Objekt hervorzuheben und die Aufmerksamkeit des Publikums auf sich zu ziehen, und sind in der Galerie gelb oder zweifarbig gefärbt. Ausgangseffekte bestimmen, wie Objekte von einer Folie verschwinden, und werden in der Galerie rot eingefärbt. Animationspfad bestimmt die Bewegung eines Objekts und den Pfad, dem es folgt. Die Symbole in der Galerie stellen den vorgeschlagenen Pfad dar. Die Option Benutzerdefinierter Pfad ist ebenfalls verfügbar. Um mehr zu erfahren, lesen Sie bitte den folgenden Artikel. Scrollen Sie in der Animationsgalerie nach unten, um alle in der Galerie enthaltenen Effekte anzuzeigen. Wenn Sie die benötigte Animation nicht in der Galerie sehen, klicken Sie unten in der Galerie auf die Option Mehr Effekte anzeigen. Hier finden Sie die vollständige Liste der Animationseffekte. Effekte werden zusätzlich nach der visuellen Wirkung gruppiert, die sie auf das Publikum haben. Die Eingangs-, Hervorhebungs- und Ausgangseffekte sind gruppiert: Grundlegende, Dezent, Mittelmäßig und Spektakulär. Animationspfad-Effekte sind gruppiert: Grundlegende, Dezent und Mittelmäßig. Anwenden mehrerer Animationen Sie können demselben Objekt mehr als einen Animationseffekt hinzufügen. Um eine weitere Animation hinzuzufügen, Klicken Sie auf der Registerkarte Animation auf die Schaltfläche Animation hinzufügen. Die Liste der Animationseffekte wird geöffnet. Wiederholen Sie die Schritte 3 und 4 oben, um eine Animation hinzuzufügen. Wenn Sie die Animationsgalerie und nicht die Schaltfläche Animation hinzufügen verwenden, wird der erste Animationseffekt durch einen neuen Effekt ersetzt. Ein kleines Quadrat neben dem Objekt zeigt die Sequenznummern der angewendeten Effekte. Sobald Sie einem Objekt mehrere Effekte hinzufügen, erscheint das Symbol Mehrfach Animationen in der Animationsgalerie. Ändern der Reihenfolge der Animationseffekte auf einer Folie Klicken Sie auf das Animationsquadrat. Klicken Sie auf die Pfeile oder auf der Registerkarte Animation, um die Reihenfolge der Darstellung von Objekten auf der Folie zu ändern. Einstellen des Animationstimings Verwenden Sie die Timing-Optionen auf der Registerkarte Animation, um die Optionen Start, Dauer, Verzögern, Wiederholen und Zurückspulen für Animationen auf einer Folie festzulegen. Startoptionen für Animationen Beim Klicken: Die Animation beginnt, wenn Sie auf die Folie klicken. Dies ist die Standardoption. Mit vorheriger: Die Animation beginnt, wenn der vorherige Animationseffekt beginnt und die Effekte gleichzeitig erscheinen. Nach vorheriger: Die Animation beginnt direkt nach dem vorherigen Animationseffekt. Animationseffekte werden automatisch auf einer Folie nummeriert. Alle Animationen, die auf Mit vorheriger und Nach vorheriger eingestellt sind, nehmen die Nummer der Animation, mit der sie verbunden sind, da sie automatisch erscheinen. Optionen für Animation-Trigger Klicken Sie auf die Schaltfläche Trigger und wählen Sie eine der entsprechenden Optionen aus: Bei Reihenfolge von Klicken: Jedes Mal, wenn Sie irgendwo auf die Folie klicken, wird die nächste Animation in Folge gestartet. Dies ist die Standardoption. Beim Klicken auf: Um die Animation zu starten, wenn Sie auf das Objekt klicken, das Sie aus der Drop-Down-Liste auswählen. Andere Timing-Optionen Dauer: Verwenden Sie diese Option, um festzulegen, wie lange eine Animation angezeigt werden soll. Wählen Sie eine der verfügbaren Optionen aus dem Menü oder geben Sie den erforderlichen Zeitwert ein. Verzögern: Verwenden Sie diese Option, wenn Sie möchten, dass die ausgewählte Animation innerhalb eines bestimmten Zeitraums angezeigt wird, oder wenn Sie eine Pause zwischen den Effekten benötigen. Verwenden Sie die Pfeile, um den erforderlichen Zeitwert auszuwählen, oder geben Sie den erforderlichen Wert in Sekunden ein. Wiederholen: Verwenden Sie diese Option, wenn Sie eine Animation mehr als einmal anzeigen möchten. Klicken Sie auf das Feld Wiederholen und wählen Sie eine der verfügbaren Optionen aus oder geben Sie Ihren Wert ein. Zurückspulen: Aktivieren Sie dieses Kontrollkästchen, wenn Sie das Objekt in seinen ursprünglichen Zustand zurückspulen möchten, wenn die Animation endet."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Objekte auf einer Folie anordnen und ausrichten", 
        "body": "Im Präsentationseditor die hinzugefügten Textbereiche, AutoFormen, Diagramme und Bilder können auf der Folie ausgerichtet, gruppiert, angeordnet und horizontal oder vertikal verteilt werden. Um einen dieser Vorgänge auszuführen, wählen Sie zuerst ein einzelnes Objekt oder mehrere Objekte auf der Folie aus. Um mehrere Objekte zu wählen, halten Sie die Taste STRG gedrückt und klicken Sie auf die gewünschten Objekte. Um ein Textfeld auszuwählen, klicken Sie auf den Rahmen und nicht auf den darin befindlichen Text. Danach können Sie entweder über die nachstehend beschriebenden Symbole in der Registerkarte Layout navigieren oder Sie nutzen die entsprechenden Optionen aus dem Rechtsklickmenü. Objekte ausrichten Ausrichten von zwei oder mehr ausgewählten Objekten: Klicken Sie auf das Symbol Form ausrichten auf der oberen Symbolleiste in der Registerkarte Startseite und wählen Sie eine der verfügbaren Optionen: An Folie ausrichten, um Objekte relativ zu den Rändern der Folie auszurichten. Ausgewählte Objekte ausrichten (diese Option ist standardmäßig ausgewählt), um Objekte im Verhältnis zueinander auszurichten. Klicken Sie erneut auf das Symbol Form ausrichten und wählen Sie den gewünschten Ausrichtungstyp aus der Liste aus: Linksbündig ausrichten - um die Objekte horizontal am linken Folienrand ausrichten. Mittig ausrichten - um die Objekte horizontal mittig auf der Folie auszurichten. Rechtsbündig ausrichten - um die Objekte horizontal am rechten Folienrand auszurichten. Oben ausrichten - um die Objekte vertikal am oberen Folienrand auszurichten. Mittig ausrichten - um die Objekte vertikal nach ihrer Mitte und der Mitte des Slides auszurichten. Unten ausrichten - um die Objekte vertikal am unteren Folienrand auszurichten. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Ausrichten aus und nutzen Sie dann eine der verfügbaren Ausrichtungsoptionen. Wenn Sie ein einzelnes Objekt ausrichten möchten, kann es relativ zu den Kanten der Folie ausgerichtet werden. Standardmäßig ist in diesem Fall die Option An der Folie ausrichten ausgewählt. Objekte verteilen Drei oder mehr ausgewählte Objekte horizontal oder vertikal so verteilen, dass der gleiche Abstand zwischen ihnen angezeigt wird: Klicken Sie auf das Symbol Form ausrichten auf der oberen Symbolleiste in der Registerkarte Startseite und wählen Sie eine der verfügbaren Optionen: An Folie ausrichten, um Objekte zwischen den Rändern der Folie zu verteilen. Ausgewählte Objekte ausrichten (diese Option ist standardmäßig ausgewählt), um Objekte zwischen zwei ausgewählten äußersten Objekten zu verteilen. Klicken Sie erneut auf das Symbol Form ausrichten und wählen Sie den gewünschten Verteilungstyp aus der Liste aus: Horizontal verteilen - um Objekte gleichmäßig zwischen den am weitesten links und rechts liegenden ausgewählten Objekten / dem linken und rechten Rand der Folie zu verteilen. Vertikal verteilen - um Objekte gleichmäßig zwischen den am weitesten oben und unten liegenden ausgewählten Objekten / dem oberen und unteren Rand der Folie zu verteilen. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Ausrichten aus und nutzen Sie dann eine der verfügbaren Verteilungsoptionen. Die Verteilungsoptionen sind deaktiviert, wenn Sie weniger als drei Objekte auswählen. Objekte gruppieren Um zwei oder mehr ausgewählte Objekte zu gruppieren oder die Gruppierung aufzuheben, klicken Sie auf das Symbol Gruppieren in der Registerkarte Startseite und wählen Sie die gewünschte Option aus der Liste aus: Gruppieren - um mehrere Objekte zu einer Gruppe zusammenzufügen, so dass sie gleichzeitig gedreht, verschoben, skaliert, ausgerichtet, angeordnet, kopiert, eingefügt und formatiert werden können, wie ein einzelnes Objekt. Gruppierung aufheben - um die ausgewählte Gruppe der zuvor gruppierten Objekte aufzulösen. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Anordnung aus und nutzen Sie dann die Optionen Gruppieren oder Gruppierung aufheben. Die Option Gruppieren ist deaktiviert, wenn Sie weniger als zwei Objekte auswählen. Die Option Gruppierung aufheben ist nur verfügbar, wenn Sie eine zuvor gruppierte Objektgruppe auswählen. Objekte anordnen Um die gewählten Objekte anzuordnen (d.h. ihre Reihenfolge bei der Überlappung zu ändern), klicken Sie auf das Symbol Form anordnen in der Registerkarte Startseite und wählen Sie die gewünschte Anordnung aus der Liste. In den Vordergrund - Objekt(e) in den Vordergrund bringen. Eine Ebene nach vorne - um ausgewählte Objekte eine Ebene nach vorne zu schieben. In den Hintergrund - um ein Objekt/Objekte in den Hintergrund zu schieben. Eine Ebene nach hinten - um ausgewählte Objekte nur eine Ebene nach hinten zu schieben. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Anordnen aus und nutzen Sie dann eine der verfügbaren Optionen."
    },
   {
        "id": "UsageInstructions/ApplyTransitions.htm", 
        "title": "Übergänge hinzufügen", 
        "body": "Ein Übergang ist ein Effekt, der angezeigt wird, wenn während der Präsentation eine Folie zur nächsten weitergeht. Im Präsentationseditor können Sie auf alle Folien denselben Übergang oder auf jede einzelne Folie unterschiedliche Übergänge anwenden und die Übergangsparameter anpassen. Um einen Übergang auf eine einzelne Folie oder mehrere ausgewählte Folien anzuwenden: Wechseln Sie zur Registerkarte Übergänge in der oberen Symbolleiste.</p> Wählen Sie eine Folie (oder mehrere Folien in der Folienliste) aus, auf die Sie einen Übergang anwenden möchten. Wählen Sie einen der verfügbaren Übergangseffekte auf der Registerkarte Übergänge aus: Kein(e), Einblendung, Schieben, Wischblende, Aufteilen, Aufdecken, Bedecken, Uhr, Vergrößern. Klicken Sie auf die Schaltfläche Parameter, um eine der verfügbaren Effektoptionen auszuwählen, die genau definieren, wie der Effekt angezeigt wird. Die verfügbaren Optionen für den Vergrößern-Effekt sind beispielsweise Vergrößern, Verkleinern und Vergrößern und drehen. Geben Sie die Dauer des gewünschten Übergangs an. Wählen Sie im Feld Dauer die gewünschte Dauer aus oder geben Sie einen Wert in das dafür vorgesehene Feld ein (die Dauer wird in Sekunden gemessen). Klicken Sie auf die Schaltfläche Vorschau, um den ausgewählten Übergang im Folienbearbeitungsbereich abzuspielen. Geben Sie an, wie lange die Folie angezeigt werden soll, bis der Übergang in die nächste Folie erfolgt: Bei Klicken beginnen – aktivieren Sie dieses Kontrollkästchen, wenn Sie keine Beschränkung für die Anzeigedauer einer Folie wünschen. Der Wechseln in eine neue Folie erfolgt erst bei Mausklick Verzögern – nutzen Sie diese Option, wenn die gewählte Folie für eine bestimmte Zeit angezeigt werden soll, bis der Übergang in eine andere Folie erfolgt. Aktivieren Sie dieses Kontrollkästchen und wählen Sie die Dauer in Sekunden aus oder geben Sie den Wert in das dafür vorgesehene Kästchen ein. Wenn Sie nur das Kontrollkästchen Verzögern aktivieren, erfolgt der Übergang automatisch, in einem festgelegten Zeitintervall. Wenn Sie die Kontrollkästchen Bei Klicken beginnen und Verzögern gleichzeitig aktivieren und den Wert für die Dauer der Verzögerung festlegen, erfolgt der Übergang ebenfalls automatisch, sie haben zusätzlich jedoch auch die Möglichkeit, ein Slide mit der Maus anzuklicken, um den Übergang in die nächste Folie einzuleiten. Um einen Übergang auf alle Folien in Ihrer Präsentation anzuwenden, klicken Sie auf der Registerkarte Übergänge auf die Schaltfläche Auf alle Folien anwenden. Um einen Übergang zu löschen, wählen Sie die erforderliche Folie aus und wählen Sie Kein(e) unter den Übergangseffektoptionen auf der Registerkarte Übergänge. Um alle Übergänge zu löschen, wählen Sie eine beliebige Folie aus, wählen Sie Kein(e) unter den Übergangseffektoptionen und klicken Sie auf die Schaltfläche Auf alle Folien anwenden auf der Registerkarte Übergänge."
    },
   {
        "id": "UsageInstructions/CommunicationPlugins.htm", 
        "title": "Kommunikation während der Bearbeitung", 
        "body": "Im ONLYOFFICE Präsentationseditor können Sie immer mit Kollegen in Kontakt bleiben und beliebte Online-Messenger wie Telegram und Rainbow nutzen. Telegram- und Rainbow-Plugins werden standardmäßig nicht installiert. Informationen zur Installation finden Sie im entsprechenden Artikel: Hinzufügen von Plugins zu den ONLYOFFICE Desktop Editoren Hinzufügen von Plugins zu ONLYOFFICE Cloud oder Hinzufügen neuer Plugins zu Server-Editoren . Telegram Um mit dem Chatten im Telegram-Plugin zu beginnen: wechseln Sie zum Tab Plugins und klicken Sie auf Telegram, geben Sie Ihre Telefonnummer in das entsprechende Feld ein, aktivieren Sie das Kontrollkästchen Angemeldet bleiben, wenn Sie die Anmeldeinformationen für die aktuelle Sitzung speichern möchten, und klicken Sie auf die Schaltfläche Weiter, geben Sie den Code, den Sie erhalten haben, in Ihre Telegram-App ein, oder loggen Sie mit dem QR-Code ein, öffnen Sie die Telegram-App auf Ihrem Telefon, gehen Sie zu Einstellungen > Geräte > QR scannen, scannen Sie das Bild, um sich anzumelden. Jetzt können Sie Telegram für Instant Messaging innerhalb der Editor-Oberfläche von ONLYOFFICE verwenden. Rainbow Um mit dem Chatten im Rainbow-Plugin zu beginnen: wechseln Sie zum Tab Plugins und klicken Sie auf Rainbow, registrieren Sie ein neues Konto, indem Sie auf die Schaltfläche Anmelden klicken, oder melden Sie sich bei einem bereits erstellten Konto an. Geben Sie dazu Ihre E-Mail in das entsprechende Feld ein und klicken Sie auf Weiter, geben Sie dann Ihr Kontopasswort ein, aktivieren Sie das Kontrollkästchen Meine Sitzung am Leben erhalten, wenn Sie die Anmeldeinformationen für die aktuelle Sitzung speichern möchten, und klicken Sie auf die Schaltfläche Verbinden. Jetzt sind Sie fertig und können gleichzeitig in Rainbow chatten und in der ONLYOFFICE-Editor-Oberfläche arbeiten."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Formatierung übernehmen/entfernen", 
        "body": "Kopieren einer bestimmte Textformatierung im Präsentationseditor: Wählen Sie mit der Maus oder mithilfe der Tastatur den Textabschnitt aus, dessen Formatierung Sie kopieren möchten. Klicken Sie in der oberen Symbolleiste unter der Registerkarte Start auf das Symbol Format übertragen (der Mauszeiger ändert sich wie folgt ). Wählen Sie einen Textabschnitt, auf den Sie die Formatierung übertragen möchten. Übertragung der Formatierung auf mehrere Textabschnitte. Wählen Sie mit der Maus oder mithilfe der Tastatur den Textabschnitt aus, dessen Formatierung Sie kopieren möchten. Führen Sie in der oberen Symbolleiste unter der Registerkarte Start einen Doppelklick auf das Symbol Format übertragen aus (der Mauszeiger ändert sich wie folgt und das Symbol Format übertragen bleibt ausgewählt: ), Markieren Sie die gewünschten Textabschnitte Schritt für Schritt, um die Formatierung zu übertragen. Wenn Sie den Modus beenden möchten, klicken Sie erneut auf das Symbol Format übertragen oder drücken Sie die ESC-Taste auf Ihrer Tastatur. Um die angewandte Formatierung in einem Textabschnitt zu entfernen, führen Sie die folgenden Schritte aus: markieren Sie den entsprechenden Textabschnitt und klicken Sie auf das Symbol Formatierung löschen auf der oberen Symbolleiste, in der Registerkarte Start."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Daten kopieren/einfügen, Aktionen rückgängig machen/wiederholen", 
        "body": "Zwischenablage verwenden Um gewählte Objekte (Folien, Textabschnitte, AutoFormen) im Präsentationseditor auszuschneiden, zu kopieren, einzufügen oder Aktionen rückgängig zu machen bzw. zu wiederholen, nutzen Sie die Optionen aus dem Rechtsklickmenü oder die entsprechenden Tastenkombinationen oder die Symbole die auf jeder beliebigen Registerkarte in der oberen Symbolleiste verfügbar sind: Ausschneiden - wählen Sie ein Objekt aus und nutzen Sie die Option Ausschneiden im Rechtsklickmenü oder das Symbol Ausschneiden in der oberen Symbolleiste, um die Auswahl zu löschen und in der Zwischenablage des Rechners zu speichern. Die ausgeschnittenen Daten können später an einer anderen Stelle in derselben Präsentation wieder eingefügt werden. Kopieren – wählen Sie ein Objekt aus und klicken Sie im Rechtsklickmenü auf Kopieren oder klicken Sie in der oberen Symbolleiste auf das Symbol Kopieren , um die Auswahl in die Zwischenablage Ihres Computers zu kopieren. Das kopierte Objekt kann später an einer anderen Stelle in derselben Präsentation eingefügt werden. Einfügen – platzieren Sie den Cursor an der Stelle in Ihrer Präsentation, an der Sie das zuvor kopierte Objekt einfügen möchten und wählen Sie im Rechtsklickmenü die Option Einfügen oder klicken Sie in der oberen Symbolleiste auf Einfügen . Das Objekt wird an der aktuellen Cursorposition eingefügt. Das Objekt kann vorher aus derselben Präsentation kopiert werden oder auch aus einem anderen Dokument oder Programm oder von einer Webseite. In der Online-Version können nur die folgenden Tastenkombinationen zum Kopieren oder Einfügen von Daten aus/in eine andere Präsentation oder ein anderes Programm verwendet werden. In der Desktop-Version können sowohl die entsprechenden Schaltflächen/Menüoptionen als auch Tastenkombinationen für alle Kopier-/Einfügevorgänge verwendet werden: STRG+C - Kopieren; STRG+V - Einfügen; STRG+X - Ausschneiden; Inhalte einfügen mit Optionen Für die gemeinsame Bearbeitung ist die Option Spezielles Einfügen ist nur im Co-Editing-Modus Formal verfügbar. Nachdem der kopierte Text eingefügt wurde, erscheint neben der eingefügten Textpassage oder dem eingefügten Objekt das Menü Einfügeoptionen . Klicken Sie auf diese Schaltfläche, um die gewünschte Einfügeoption auszuwählen. Für das Einfügen von Textpassagen stehen folgende Auswahlmöglichkeiten zur Verfügung: An Zielformatierung anpassen (Strg+H) - die Formatierung der aktuellen Präsentation wird auf den eingefügten Text angewendet. Diese Option ist standardmäßig ausgewählt. Ursprüngliche Formatierung beibehalten (Strg+K) - die ursprüngliche Formatierung des kopierten Textabschnitts wird in die Präsentation eingefügt. Bild (Strg+U) - der Text wird als Bild eingefügt und kann nicht bearbeitet werden. Nur den Text übernehmen (Strg+T) - der kopierte Text wird in an die vorhandene Formatierung angepasst. Für das Einfügen von Objekten (AutoFormen, Diagramme, Tabellen) stehen folgende Auswahlmöglichkeiten zur Verfügung: An Zielformatierung anpassen (Strg+H) - die Formatierung der aktuellen Präsentation wird auf den eingefügten Text angewendet. Diese Option ist standardmäßig ausgewählt. Bild (Strg+U) - das Objekt wird als Bild eingefügt und kann nicht bearbeitet werden. Um die automatische Anzeige der Schaltfläche Spezielles Einfügen nach dem Einfügen zu aktivieren/deaktivieren, gehen Sie zur Registerkarte Datei > Erweiterte Einstellungen und aktivieren/deaktivieren Sie das Kontrollkästchen Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen. Vorgänge rückgängig machen/wiederholen Verwenden Sie die entsprechenden Symbole im linken Bereich der Kopfzeile des Editors, um Vorgänge rückgängig zu machen/zu wiederholen oder nutzen Sie die entsprechenden Tastenkombinationen: Rückgängig machen – klicken Sie auf das Symbol Rückgängig machen , um den zuletzt durchgeführten Vorgang rückgängig zu machen. Wiederholen – klicken Sie auf das Symbol Wiederholen , um den zuletzt rückgängig gemachten Vorgang zu wiederholen. Alternativ können Sie Vorgänge auch mit der Tastenkombination STRG+Z rückgängig machen bzw. mit STRG+Y wiederholen. Wenn Sie eine Präsentation im Modus Schnell gemeinsam bearbeiten, ist die Option letzten rückgängig gemachten Vorgang wiederherstellen nicht verfügbar."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Listen erstellen", 
        "body": "Um eine Liste im Präsentationseditor zu erstellen: Positionieren Sie den Cursor dort, wo eine Liste beginnen soll (dies kann eine neue Zeile oder der bereits eingegebene Text sein). Wechseln Sie zur Registerkarte Startseite der oberen Symbolleiste. Wählen Sie den Listentyp aus, den Sie starten möchten: Ungeordnete Liste mit Markierungen wird mit dem Symbol Aufzählung erstellt, das sich in der oberen Symbollesite befindet Eine geordnete Liste mit Ziffern oder Buchstaben wird mithilfe des Symbols Nummerierung in der oberen Symbolleiste erstellt. Klicken Sie auf den Abwärtspfeil neben dem Symbol Aufzählung oder Nummerierung, um auszuwählen, wie die Liste aussehen soll. Jetzt erscheint jedes Mal, wenn Sie die Eingabetaste am Ende der Zeile drücken, ein neues geordnetes oder ungeordnetes Listenelement. Um dies zu beenden, drücken Sie die Rücktaste und fahren Sie mit dem allgemeinen Textabsatz fort. Sie können auch den Texteinzug in den Listen und ihre Verschachtelung mit den Schaltflächen Einzug verkleinern und Einzug vergrößern auf der Registerkarte Startseite in der oberen Symbolleiste ändern. Die zusätzlichen Einzugs- und Abstandsparameter können in der rechten Seitenleiste und im erweiterten Einstellungsfenster geändert werden. Um mehr darüber zu erfahren, lesen Sie den Abschnitt Text einfügen und formatieren. Listeneinstellungen ändern Um die Einstellungen für Aufzählungszeichen oder nummerierte Listen, z. B. Art, Größe und Farbe der Aufzählungszeichen zu ändern: Klicken Sie auf ein vorhandenes Listenelement oder wählen Sie den Text aus, den Sie als Liste formatieren möchten. Klicken Sie auf das Symbol Aufzählung oder Nummerierung auf der Registerkarte Startseite in der oberen Symbolleiste. Wählen Sie die Option Listeneinstellungen. Das Fenster Listeneinstellungen wird geöffnet. Das Einstellungsfenster der Liste mit Aufzählungszeichen sieht folgendermaßen aus: Typ - ermöglicht Ihnen die Auswahl des erforderlichen Zeichens für die Liste. Wenn Sie auf die Option Neues Aufzählungszeichen klicken, öffnet sich das Fenster Symbol und Sie können eines der verfügbaren Zeichen auswählen. Sie können auch ein neues Symbol hinzufügen. Weitere Informationen zum Arbeiten mit Symbolen finden Sie in diesem Artikel. Wenn Sie auf die Option Neues Bild klicken, erscheint ein neues Feld Importieren, in dem Sie neue Bilder für Aufzählungszeichen Aus Datei, Aus URL oder Aus dem Speicher. Größe - ermöglicht es Ihnen, die erforderliche Aufzählungszeichengröße abhängig von der aktuellen Textgröße auszuwählen. Der Wert kann zwischen 25% und 400% liegen. Farbe - ermöglicht Ihnen die Auswahl der erforderlichen Aufzählungszeichenfarbe. Sie können eine der Designfarben oder Standardfarben aus der Palette auswählen oder eine benutzerdefinierte Farbe angeben. Das Einstellungsfenster der nummerierten Liste sieht folgendermaßen aus: Typ - können Sie das für die Liste verwendete Zahlenformat auswählen. Größe - ermöglicht Ihnen die Auswahl der erforderlichen Zahlengröße in Abhängigkeit von der aktuellen Textgröße. Der Wert kann zwischen 25% und 400% liegen. Beginnen mit - ermöglicht Ihnen die Auswahl der erforderlichen Sequenznummer, ab der eine nummerierte Liste beginnt. Farbe - ermöglicht Ihnen die Auswahl der erforderlichen Zahlenfarbe. Sie können eine der Designfarben oder Standardfarben aus der Palette auswählen oder eine benutzerdefinierte Farbe angeben. Klicken Sie auf OK, um die Änderungen zu übernehmen und das Einstellungsfenster zu schließen."
    },
   {
        "id": "UsageInstructions/FillObjectsSelectColor.htm", 
        "title": "Objekte ausfüllen und Farben auswählen", 
        "body": "Im Präsentationseditor sie haben die Möglichkeit für Folien, AutoFormen und DekoSchriften verschiedene Füllfarben und Hintergründe zu verwenden. Wählen Sie ein Objekt Um die Hintergrundfarbe einer Folie zu ändern, wählen Sie die gewünschte Folie in der Folienliste aus. Die Registerkarte Folieneinstellungen wird in der rechten Menüleiste aktiviert. Um die Füllung einer AutoForm zu ändern, klicken Sie mit der linken Maustaste auf die gewünschte AutoForm. Die Registerkarte Folieneinstellungen wird in der rechten Menüleiste aktiviert. Um die Füllung einer DekoSchrift zu ändern, klicken Sie mit der linken Maustaste auf das gewünschte Textfeld. Die Registerkarte Folieneinstellungen wird in der rechten Menüleiste aktiviert. Gewünschte Füllung festlegen Passen Sie die Eigenschaften der gewählten Füllung an (eine detaillierte Beschreibung für jeden Füllungstyp finden Sie weiter in dieser Anleitung). Für AutoFormen und TextArt können Sie unabhängig vom gewählten Füllungstyp die gewünschte Transparenz festlegen, schieben Sie den Schieberegler in die gewünschte Position oder geben Sie manuelle den Prozentwert ein. Der Standardwert ist 100%. 100% entspricht dabei völliger Undurchsichtigkeit. Der Wert 0% entspricht der vollen Transparenz. Die folgenden Füllungstypen sind verfügbar: Einfarbige Füllung - wählen Sie diese Option, um die Volltonfarbe festzulegen, mit der Sie die innere Fläche der ausgewählten Folie/Form ausfüllen möchten. Klicken Sie auf das Farbfeld unten und wählen Sie die gewünschte Farbe aus den verfügbaren Farbpaletten aus oder legen Sie eine beliebige Farbe fest: Designfarben - die Farben, die dem gewählten Farbschema der Präsentation entsprechen. Sobald Sie ein anderes Thema oder ein anderes Farbschema anwenden, ändert sich die Einstellung für die Designfarben. Standardfarben - die festgelegten Standardfarben. Benutzerdefinierte Farbe - klicken Sie auf diese Option, wenn Ihre gewünschte Farbe nicht in der Palette mit verfügbaren Farben enthalten ist. Wählen Sie den gewünschten Farbbereich mit dem vertikalen Schieberegler aus und legen Sie dann die gewünschte Farbe fest, indem Sie den Farbwähler innerhalb des großen quadratischen Farbfelds an die gewünschte Position ziehen. Sobald Sie eine Farbe mit dem Farbwähler bestimmt haben, werden die entsprechenden RGB- und sRGB-Farbwerte in den Feldern auf der rechten Seite angezeigt. Sie können eine Farbe auch anhand des RGB-Farbmodells bestimmen, indem Sie die gewünschten nummerischen Werte in den Feldern R, G, B (Rot, Grün, Blau) festlegen oder den sRGB-Hexadezimalcode in das Feld mit dem #-Zeichen eingeben. Die gewählte Farbe erscheint im Vorschaufeld Neu. Wenn das Objekt vorher mit einer benutzerdefinierten Farbe gefüllt war, wird diese Farbe im Feld Aktuell angezeigt, so dass Sie die Originalfarbe und die Zielfarbe vergleichen könnten. Wenn Sie die Farbe festgelegt haben, klicken Sie auf Hinzufügen. Die benutzerdefinierte Farbe wird auf das Objekt angewandt und in die Palette Benutzerdefinierte Farbe hinzugefügt. Es sind die gleichen Farbtypen, die Ihnen bei der Auswahl der Strichfarbe für AutoFormen, Schriftfarbe oder bei der Farbänderung des Tabellenhintergrunds und -rahmens zur Verfügung stehen. Füllung mit Farbverlauf - wählen Sie diese Option, um die Form mit zwei Farben zu füllen, die sanft ineinander übergehen. Stil - wählen Sie eine der verfügbaren Optionen: Linear (Farben ändern sich linear, d.h. entlang der horizontalen/vertikalen Achse oder diagonal in einem 45-Grad Winkel) oder Radial (Farben ändern sich kreisförmig vom Zentrum zu den Kanten). Richtung - das Richtungsvorschaufenster zeigt die ausgewählte Verlaufsfarbe an. Klicken Sie auf den Pfeil, um eine Vorlage aus dem Menü auszuwählen. Wenn der Farbverlauf Linear ausgewählt ist, sind die folgenden Richtungen verfügbar: von oben links nach unten rechts, von oben nach unten, von oben rechts nach unten links, von rechts nach links, von unten rechts nach oben links, von unten nach oben, von unten links nach oben rechts, von links nach rechts. Wenn der Farbverlauf Radial ausgewählt ist, steht nur eine Vorlage zur Verfügung. Winkel - stellen Sie den numerischen Wert für einen genauen Farbübergangswinkel ein. Punkt des Farbverlaufs ist ein bestimmter Punkt für den Verlauf von einer Farbe zur anderen. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs einfügen oder den Schieberegler, um einen Punkt des Verlaufs einzufügen. Sie können bis zu 10 Punkte einfügen. Jeder nächste eingefügte Punkt des Farbverlaufs beeinflusst in keiner Weise die aktuelle Darstellung der Farbverlaufsfüllung. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs entfernen, um den bestimmten Punkt zu löschen. Verwenden Sie den Schieberegler, um die Position des Farbverlaufspunkts zu ändern, oder geben Sie Position in Prozent an, um eine genaue Position zu erhalten. Um eine Farbe auf einen Verlaufspunkt anzuwenden, klicken Sie auf einen Punkt im Schieberegler und dann auf Farbe, um die gewünschte Farbe auszuwählen. Bild- oder Texturfüllung - wählen Sie diese Option, um ein Bild oder eine vorgegebene Textur als Hintergrund für eine Form/Folie zu benutzen. Wenn Sie ein Bild als Hintergrund für eine Form/Folie verwenden möchten, können Sie das Bild Aus Datei einfügen, geben Sie dazu in dem geöffneten Fenster den Speicherort auf Ihrem Computer an, oder Aus URL, geben Sie die entsprechende Webadresse in das geöffnete Fenster ein. Wenn Sie eine Textur als Hintergrund für eine Form bzw. Folie verwenden möchten, öffnen Sie die Liste Aus Textur und wählen Sie die gewünschte Texturvoreinstellung. Aktuell stehen die folgenden Texturen zur Verfügung: Canvas, Carton, Dark Fabric, Grain, Granite, Grey Paper, Knit, Leather, Brown Paper, Papyrus, Wood. Wenn das gewählte Bild kleiner oder größer als die AutoForm oder Folie ist, können Sie die Option Strecken oder Kacheln aus dem Listenmenü auswählen. Die Option Strecken ermöglicht Ihnen die Größe des Bildes so anzupassen, dass es die Folie oder AutoForm vollständig ausfüllen kann. Die Option Kacheln ermöglicht Ihnen nur einen Teil des größeren Bildes zu verwenden und die Originalgröße für diesen Teil beizubehalten oder ein kleineres Bild unter Beibehaltung der Originalgröße zu wiederholen und durch diese Wiederholungen die gesamte Fläche der Folie oder AutoForm auszufüllen. Jede Voreinstellung für Texturfüllungen ist dahingehend festgelegt, den gesamten Bereich auszufüllen, aber Sie können nach Bedarf auch den Effekt Strecken anwenden. Muster - wählen Sie diese Option, um die Form/Folie mit einem zweifarbigen Design zu füllen, dass aus regelmäßig wiederholten Elementen besteht. Muster - wählen Sie eine der Designvorgaben aus dem Menü aus. Vordergrundfarbe - klicken Sie auf dieses Farbfeld, um die Farbe der Musterelemente zu ändern. Hintergrundfarbe - klicken Sie auf dieses Farbfeld, um die Farbe des Hintergrundmusters zu ändern. Keine Füllung - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten."
    },
   {
        "id": "UsageInstructions/HighlightedCode.htm", 
        "title": "Hervorgehobenen Code einfügen", 
        "body": "Im Präsentationseditor können Sie hervorgehobenen Code mit dem schon angepassten Stil entsprechend der Programmiersprache und dem Farbstil des von Ihnen ausgewählten Programms einfügen. Gehen Sie zu Ihrer Präsentation und platzieren Sie den Cursor an der Stelle, an der Sie den Code einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Code hervorheben aus. Geben Sie die Programmiersprache an. Wählen Sie einen Code-Stil aus, der so aussieht, als wäre er in diesem Programm geöffnet. Geben Sie an, ob Sie Tabulatoren durch Leerzeichen ersetzen möchten. Wählen Sie Hintergrundfarbe. Bewegen Sie dazu den Cursor manuell über die Palette oder fügen Sie den RGB/HSL/HEX-Wert ein. Klicken Sie auf OK, um den Code einzufügen."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "AutoFormen einfügen und formatieren", 
        "body": "AutoForm einfügen Um eine AutoForm in eine Folie im Präsentationseditor einzufügen: Wählen Sie in der Folienliste links die Folie aus, der Sie eine AutoForm hinzufügen wollen. Klicken Sie auf das Symbol Form auf der Registerkarte Startseite oder klicken Sie auf die Formengalerie auf der Registerkarte Einfügen in der oberen Symbolleiste. Wählen Sie eine der verfügbaren Gruppen von AutoFormen aus der Formengalerie aus: Zuletzt verwendet, Standardformen, Geformte Pfeile, Mathematik, Diagramme, Sterne &amp; Bänder, Legenden, Schaltflächen, Rechtecke, Linien. Klicken Sie in der gewählten Gruppe auf die gewünschte AutoForm. Positionieren Sie den Mauszeiger an der Stelle, an der Sie eine Form hinzufügen möchten. Sie können klicken und ziehen, um die Form auszudehnen. Sobald die AutoForm hinzugefügt wurde, können Sie Größe, Position und Eigenschaften ändern. Um eine Bildunterschrift innerhalb der AutoForm hinzuzufügen, wählen Sie die Form auf der Folie aus und geben Sie den Text ein. Ein solcher Text wird Bestandteil der AutoForm (wenn Sie die AutoForm verschieben oder drehen, wird der Text ebenfalls verschoben oder gedreht). Es ist auch möglich, einem Folienlayout eine AutoForm hinzuzufügen. Weitere Informationen finden Sie in dieser Artikel. Einstellungen der AutoForm anpassen Einige Eigenschaften der AutoFormen können in der Registerkarte Formeinstellungen in der rechten Seitenleiste geändert werden. Klicken Sie dazu auf die AutoForm und wählen Sie das Symbol Formeinstellungen in der rechten Seitenleiste aus. Hier können die folgenden Eigenschaften geändert werden: Füllung - zum Ändern der Füllung einer AutoForm. Folgende Optionen stehen Ihnen zur Verfügung: Farbfüllung - um die homogene Farbe zu bestimmen, mit der Sie die gewählte Form füllen wollen. Füllung mit Farbverlauf - um die Form mit einem sanften Übergang von einer Farbe zu einer anderen zu füllen. Bild oder Textur - um ein Bild oder eine vorgegebene Textur als Hintergrund der Form zu nutzen. Muster - um die Form mit einem zweifarbigen Design zu füllen, das aus regelmäßig wiederholten Elementen besteht. Keine Füllung - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten. Weitere Informationen zu diesen Optionen finden Sie im Abschnitt Objekte ausfüllen und Farben auswählen. Strich - in dieser Gruppe können Sie Strichbreite und -farbe der AutoForm ändern. Um die Breite der Striche zu ändern, wählen Sie eine der verfügbaren Optionen im Listenmenü Größe aus. Die folgenden Optionen stehen Ihnen zur Verfügung: 0,5 Pt., 1 Pt., 1,5 Pt., 2,25 Pt., 3 Pt., 4,5 Pt., 6 Pt. Alternativ können Sie die Option Keine Linie auswählen, wenn Sie keine Umrandung wünschen. Um die Farbe der Striche zu ändern, klicken Sie auf das farbige Feld und wählen Sie die gewünschte Farbe aus. Sie können die gewählte Designfarbe, eine Standardfarbe oder eine benutzerdefinierte Farbe auswählen. Um den Typ der Striche zu ändern, wählen Sie die gewünschte Option aus der entsprechenden Drop-Down-Liste aus (standardmäßig wird eine durchgezogene Linie verwendet, diese können Sie in eine der verfügbaren gestrichelten Linien ändern). Drehen dient dazu die Form um 90 Grad im oder gegen den Uhrzeigersinn zu drehen oder die Form horizontal oder vertikal zu spiegeln. Wählen Sie eine der folgenden Optionen: um die Form um 90 Grad gegen den Uhrzeigersinn zu drehen um die Form um 90 Grad im Uhrzeigersinn zu drehen um die Form horizontal zu spiegeln (von links nach rechts) um die Form vertikal zu spiegeln (von oben nach unten) AutoForm ändern - verwenden Sie diesen Abschnitt, um die aktuelle AutoForm durch eine andere Form aus der Drop-Down-Liste zu ersetzen. Schatten anzeigen - aktivieren Sie diese Option, um die Form mit Schatten anzuzeigen. Um die erweiterte Einstellungen der AutoForm zu ändern, klicken Sie mit der rechten Maustaste auf die Form und wählen Sie die Option Form - erweiterte Einstellungen im Menü aus, oder klicken Sie in der rechten Seitenleiste auf die Verknüpfung Erweiterte Einstellungen anzeigen. Das Fenster mit den Formeigenschaften wird geöffnet: Auf der Registerkarte Positionierung können Sie die Breite und/oder Höhe der AutoForm ändern. Wenn Sie auf die Schaltfläche Seitenverhältnis beibehalten klicken (in diesem Fall sieht es so aus ), werden Breite und Höhe zusammen geändert, wobei das ursprüngliche Seitenverhältnis der automatischen Form beibehalten wird. Sie können die genaue Position auch mit den Feldern Horizontal und Vertikal sowie dem Feld Ab festlegen, wo Sie auf Einstellungen wie Obere linke Ecke und Zentriert zugreifen können. Die Registerkarte Rotation umfasst die folgenden Parameter: Winkel - mit dieser Option lässt sich die Form in einem genau festgelegten Winkel drehen. Geben Sie den erforderlichen Wert in Grad in das Feld ein oder stellen Sie diesen mit den Pfeilen rechts ein. Gekippt - aktivieren Sie das Kontrollkästchen Horizontal, um die Form horizontal zu spiegeln (von links nach rechts), oder aktivieren Sie das Kontrollkästchen Vertikal, um die Form vertikal zu spiegeln (von oben nach unten). Die Registerkarte Stärken &amp; Pfeile enthält folgende Parameter: Linienart - in dieser Gruppe können Sie die folgenden Parameter bestimmen: Abschlusstyp - legen Sie den Stil für den Abschluss der Linie fest, diese Option besteht nur bei Formen mit offener Kontur wie Linien, Polylinien usw.: Flach - für flache Endpunkte. Rund - für runde Endpunkte. Eckig - quadratisches Linienende. Verknüpfungstyp - legen Sie die Art der Verknüpfung von zwei Linien fest, z.B. kann diese Option auf Polylinien oder die Ecken von Dreiecken bzw. Vierecken angewendet werden: Rund - die Ecke wird abgerundet. Schräge Kante - die Ecke wird schräg abgeschnitten. Winkel - spitze Ecke. Dieser Typ passt gut bei AutoFormen mit spitzen Winkeln. Der Effekt wird auffälliger, wenn Sie eine hohe Konturbreite verwenden. Pfeile - diese Option ist verfügbar, wenn eine Form aus der Gruppe Linien ausgewählt ist. Hier können Sie die Start- und Endlinienart sowie die Start- und Endgröße des Pfeils festlegen, indem Sie die entsprechende Option aus den Drop-Down-Listen auswählen. Die Registerkarte Textfeld enthält die folgenden Parameter: Automatisch anpassen, um die Art und Weise zu ändern, wie Text innerhalb der Form angezeigt wird: Ohne automatische Anpassung, Text bei Überlauf verkleinern, Die Form am Text anpassen. Ränder um den Text, um die inneren Ränder der automatischen Form wie Oben, Unten, Links und Rechts zu ändern (d. h. den Abstand zwischen dem Text innerhalb die Form und die Grenzen der AutoForm). Diese Registerkarte ist nur verfügbar, wenn der AutoForm ein Text hinzugefügt wurde, ansonsten wird die Registerkarte ausgeblendet. Über die Registerkarte Spalten ist es möglich, der AutoForm Textspalten hinzuzufügen und die gewünschte Anzahl von Spalten (bis zu 16) und den Abstand zwischen Spalten festzulegen. Wenn Sie auf OK klicken, erscheint der bereits vorhandene Text, oder jeder beliebige Text den Sie in die AutoForm eingeben, in den Spalten und geht flüssig von einer Spalte in die nächste über. Die Registerkarte Alternativer Text ermöglicht die Eingabe eines Titels und einer Beschreibung, die Personen mit Sehbehinderungen oder kognitiven Beeinträchtigungen vorgelesen werden kann, damit sie besser verstehen können, welche Informationen in der Form enthalten sind. Um die hinzugefügte AutoForm zu ersetzen, klicken Sie diese mit der linken Maustaste an, wechseln Sie in die Registerkarte Formeinstellungen in der rechten Seitenleiste und wählen Sie unter AutoForm ändern in der Liste die gewünschte Form aus. Um die hinzugefügte AutoForm zu löschen, klicken Sie dieses mit der linken Maustaste an und drücken Sie die Taste Entfernen auf Ihrer Tastatur. Um mehr über die Ausrichtung einer AuftoForm auf einer Folie zu erfahren oder mehrere AutoFormen anzuordnen, lesen Sie den Abschnitt Objekte auf einer Folie ausrichten und anordnen. AutoFormen mithilfe von Verbindungen anbinden Sie können Autoformen mithilfe von Linien mit Verbindungspunkten verbinden, um Abhängigkeiten zwischen Objekten zu demonstrieren (z.B. wenn Sie ein Flussdiagramm erstellen wollen). Gehen Sie dazu vor wie folgt: Klicken Sie in der oberen Symbolleiste in den Registerkarten Start oder Einfügen auf Form. Wählen Sie die Gruppe Linien im Menü aus. Klicken Sie auf die gewünschte Form in der ausgewählten Gruppe (mit Ausnahme der letzten drei Formen, bei denen es sich nicht um Konnektoren handelt: Kurve, Skizze und Freihand). Bewegen Sie den Mauszeiger über die erste AutoForm und klicken Sie auf einen der Verbindungspunkte , die auf dem Umriss der Form zu sehen sind. Bewegen Sie den Mauszeiger in Richtung der zweiten AutoForm und klicken Sie auf den gewünschten Verbindungspunkt auf dem Umriss der Form. Wenn Sie die verbundenen AutoFormen verschieben, bleiben die Verbindungen an die Form gebunden und bewegen sich mit den Formen zusammen. Alternativ können Sie die Verbindungen auch von den Formen lösen und an andere Verbindungspunkte anbinden."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Diagramme einfügen und bearbeiten", 
        "body": "Diagramm einfügen Ein Diagramm einfügen im Präsentationseditor: Positionieren Sie den Cursor an der Stelle, an der Sie ein Diagramm einfügen möchten. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie in der oberen Symbolleiste auf das Symbol Diagramm. Wählen Sie den gewünschten Diagrammtyp aus der Liste der verfügbaren Typen aus: Spalte Gruppierte Säule Gestapelte Säulen 100% Gestapelte Säule Gruppierte 3D-Säule Gestapelte 3D-Säule 3-D 100% Gestapelte Säule 3D-Säule Linie Linie Gestapelte Linie 100% Gestapelte Linie Linie mit Datenpunkten Gestapelte Linie mit Datenpunkten 100% Gestapelte Linie mit Datenpunkten 3D-Linie Kreis Kreis Ring 3D-Kreis Balken Gruppierte Balken Gestapelte Balken 100% Gestapelte Balken Gruppierte 3D-Balken Gestapelte 3D-Balken 3-D 100% Gestapelte Balken Fläche Fläche Gestapelte Fläche 100% Gestapelte Fläche Kurs Punkte (XY) Punkte Gestapelte Balken Punkte mit interpolierten Linien und Datenpunkten Punkte mit interpolierten Linien Punkte mit geraden Linien und Datenpunkten Punkte mit geraden Linien Verbund Gruppierte Säulen - Linie Gruppierte Säulen / Linien auf der Sekundärachse Gestapelte Flächen / Gruppierte Säulen Benutzerdefinierte Kombination ONLYOFFICE Präsentationseditor unterstützt die folgenden Arten von Diagrammen, die mit Editoren von Drittanbietern erstellt wurden: Pyramide, Balken (Pyramide), horizontale/vertikale Zylinder, horizontale/vertikale Kegel. Sie können die Datei, die ein solches Diagramm enthält, öffnen und sie mit den verfügbaren Diagrammbearbeitungswerkzeugen ändern. Wenn Sie Ihre Auswahl getroffen haben, öffnet sich das Fenster Diagramm bearbeiten und Sie können die gewünschten Daten mithilfe der folgenden Steuerelemente in die Zellen eingeben: und - Kopieren und Einfügen der kopierten Daten. und - Vorgänge Rückgängig machen und Wiederholen. - Einfügen einer Funktion. und - Löschen und Hinzufügen von Dezimalstellen. - Zahlenformat ändern, d.h. das Format in dem die eingegebenen Zahlen in den Zellen dargestellt werden. zur Auswahl eines anderen Diagrammtyps. Klicken Sie auf die Schaltfläche Daten auswählen im Fenster Diagramm bearbeiten. Das Fenster Diagrammdaten wird geöffnet. Verwenden Sie das Dialogfeld Diagrammdaten, um den Diagrammdatenbereich, Legendeneinträge (Reihen), Horizontale Achsenbeschriftungen (Rubrik) zu verwalten und Zeile/Spalte ändern. Diagrammdatenbereich - wählen Sie Daten für Ihr Diagramm aus. Klicken SIe auf das Symbol rechts neben dem Feld Diagrammdatenbereich, um den Datenbereicht auszuwählen. Legendeneinträge (Reihen) - Hinzufügen, Bearbeiten oder Entfernen von Legendeneinträgen. Geben Sie den Reihennamen für Legendeneinträge ein oder wählen Sie ihn aus. Im Feld Legendeneinträge (Reihen) klicken Sie auf die Schaltfläche Hinzufügen. Im Fenster Datenreihe bearbeiten geben Sie einen neuen Legendeneintrag ein oder klicken Sie auf das Symbol rechts neben dem Feld Reihenname. Horizontale Achsenbeschriftungen (Rubrik) - den Text für Achsenbeschriftungen ändern. Im Feld Horizontale Achsenbeschriftungen (Rubrik) klicken Sie auf Bearbeiten. Im Feld Der Bereich von Achsenbeschriftungen geben Sie die gewünschten Achsenbeschriftungen ein oder klicken Sie auf das Symbol rechts neben dem Feld Der Bereich von Achsenbeschriftungen, um den Datenbereich auszuwählen. Zeile/Spalte ändern - ordnen Sie die im Diagramm konfigurierten Arbeitsblattdaten so an, wie Sie sie möchten. Wechseln Sie zu Spalten, um Daten auf einer anderen Achse anzuzeigen. Klicken Sie auf die Schaltfläche OK, um die Änderungen anzuwenden und das Fenster schließen. Klicken Sie auf die Schaltfläche Diagramm bearbeiten im Fenster Diagrammeditor, um den Diagrammtyp und -stil auszuwählen. Wählen Sie den Diagrammtyp aus der Liste der verfügbaren Typen aus: Spalte, Linie, Kreis, Balken, Fläche, Kurs, Punkte (XY) oder Verbund. Wenn Sie den Typ Verbund auswählen, listet das Fenster Diagrammtyp die Diagrammreihen auf und ermöglicht die Auswahl der zu kombinierenden Diagrammtypen und die Auswahl von Datenreihen, die auf einer Sekundärachse platziert werden sollen. Die Diagrammeinstellungen ändern Sie durch Anklicken der Schaltfläche Diagramm bearbeiten im Fenster Diagramme. Das Fenster Diagramme - Erweiterte Einstellungen wird geöffnet. Auf der Registerkarte Layout können Sie das Layout von Diagrammelementen ändern. Wählen Sie die gewünschte Position der Diagrammbezeichnung aus der Dropdown-Liste aus: Keine - es wird keine Diagrammbezeichnung angezeigt. Überlagerung - der Titel wird zentriert und im Diagrammbereich angezeigt. Keine Überlagerung - der Titel wird über dem Diagramm angezeigt. Wählen Sie die gewünschte Position der Legende aus der Menüliste aus: Keine - es wird keine Legende angezeigt Unten - die Legende wird unterhalb des Diagramms angezeigt Oben - die Legende wird oberhalb des Diagramms angezeigt Rechts - die Legende wird rechts vom Diagramm angezeigt Links - die Legende wird links vom Diagramm angezeigt Überlappung links - die Legende wird im linken Diagrammbereich mittig dargestellt Überlappung rechts - die Legende wird im rechten Diagrammbereich mittig dargestellt Legen Sie Datenbeschriftungen fest (Titel für genaue Werte von Datenpunkten): Wählen Sie die gewünschte Position der Datenbeschriftungen aus der Menüliste aus: Die verfügbaren Optionen variieren je nach Diagrammtyp. Für Säulen-/Balkendiagramme haben Sie die folgenden Optionen: Keine, zentriert, unterer Innenbereich, oberer Innenbereich, oberer Außenbereich. Für Linien-/Punktdiagramme (XY)/Strichdarstellungen haben Sie die folgenden Optionen: Keine, zentriert, links, rechts, oben, unten. Für Kreisdiagramme stehen Ihnen folgende Optionen zur Verfügung: Keine, zentriert, an Breite anpassen, oberer Innenbereich, oberer Außenbereich. Für Flächendiagramme sowie für 3D-Diagramme, Säulen- Linien- und Balkendiagramme, stehen Ihnen folgende Optionen zur Verfügung: Keine, zentriert. Wählen Sie die Daten aus, für die Sie eine Bezeichnung erstellen möchten, indem Sie die entsprechenden Felder markieren: Reihenname, Kategorienname, Wert. Geben Sie das Zeichen (Komma, Semikolon etc.) in das Feld Trennzeichen Datenbeschriftung ein, dass Sie zum Trennen der Beschriftungen verwenden möchten. Linien - Einstellen der Linienart für Liniendiagramme/Punktdiagramme (XY). Die folgenden Optionen stehen Ihnen zur Verfügung: Gerade, um gerade Linien zwischen Datenpunkten zu verwenden, glatt, um glatte Kurven zwischen Datenpunkten zu verwenden oder keine, um keine Linien anzuzeigen. Markierungen - über diese Funktion können Sie festlegen, ob die Marker für Liniendiagramme/ Punktdiagramme (XY) angezeigt werden sollen (Kontrollkästchen aktiviert) oder nicht (Kontrollkästchen deaktiviert). Die Optionen Linien und Marker stehen nur für Liniendiagramme und Punktdiagramme (XY) zur Verfügung. Auf der Registerkarte Vertikale Achse können Sie die Parameter der vertikalen Achse ändern, die auch als Werteachse oder y-Achse bezeichnet wird und numerische Werte anzeigt. Beachten Sie, dass die vertikale Achse die Kategorieachse ist, auf der Textbeschriftungen für die Balkendiagramme angezeigt werden. In diesem Fall entsprechen die Optionen der Registerkarte Vertikale Achse den Optionen, die im nächsten Abschnitt beschrieben werden. Für die Punkte (XY)-Diagramme sind beide Achsen Wertachsen. Die Abschnitte Achseneinstellungen und Gitterlinien werden für Kreisdiagramme deaktiviert, da Diagramme dieses Typs keine Achsen und Gitterlinien haben. Wählen Sie Ausblenden, um die vertikale Achse im Diagramm auszublenden, und lassen Sie das Kontrollkästchen deaktiviert, damit die vertikale Achse angezeigt wird. Geben Sie die Ausrichtung des Titels an und wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Keine, um keinen vertikalen Achsentitel anzuzeigen, Gedreht, um den Titel von unten nach oben links von der vertikalen Achse anzuzeigen. Horizontal, um den Titel horizontal links von der vertikalen Achse anzuzeigen. Die Option Minimalwert wird verwendet, um den niedrigsten Wert anzugeben, der beim Start der vertikalen Achse angezeigt wird. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Mindestwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Fixiert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben. Die Option Maximalwert wird verwendet, um den höchsten Wert anzugeben, der am Ende der vertikalen Achse angezeigt wird. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Maximalwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Fixiert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben. Die Option Schnittpunkt mit der Achse wird verwendet, um einen Punkt auf der vertikalen Achse anzugeben, an dem die horizontale Achse ihn kreuzen soll. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Achsenschnittpunktwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Wert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben oder den Achsenschnittpunkt auf den Minimal-/Maximalwert auf der vertikalen Achse setzen. Die Option Anzeigeeinheiten wird verwendet, um die Darstellung der numerischen Werte entlang der vertikalen Achse zu bestimmen. Diese Option kann nützlich sein, wenn Sie mit großen Zahlen arbeiten und möchten, dass die Werte auf der Achse kompakter und lesbarer angezeigt werden (z.B. können Sie 50.000 als 50 darstellen, indem Sie die Option Tausende verwenden). Wählen Sie die gewünschten Einheiten aus der Dropdown-Liste aus: Hunderte, Tausende, 10 000, 100 000, Millionen, 10 000 000, 100 000 000, Milliarden, Billionen oder wählen Sie die Option Kein, um zu den Standardeinheiten zurückzukehren. Die Option Werte in umgekehrter Reihenfolge wird verwendet, um Werte in die entgegengesetzte Richtung anzuzeigen. Wenn das Kontrollkästchen deaktiviert ist, befindet sich der niedrigste Wert unten und der höchste Wert oben auf der Achse. Wenn das Kontrollkästchen aktiviert ist, werden die Werte von oben nach unten sortiert. Im Abschnitt Parameter der Teilstriche können Sie das Erscheinungsbild von Häkchen auf der vertikalen Skala anpassen. Hauptmarkierungen sind die größeren Teilungen, bei denen Beschriftungen numerische Werte anzeigen können. Kleinere Häkchen sind die Skalenunterteilungen, die zwischen den großen Häkchen platziert werden und keine Beschriftungen haben. Häkchen definieren auch, wo Gitterlinien angezeigt werden können, wenn die entsprechende Option auf der Registerkarte Layout festgelegt ist. Die Dropdown-Listen Primärer / Sekundärer Typ enthalten die folgenden Platzierungsoptionen: Kein, um keine Haupt- / Nebenmarkierungen anzuzeigen, Schnittpunkt, um auf beiden Seiten der Achse Haupt- / Nebenmarkierungen anzuzeigen. In, um Haupt- / Nebenmarkierungen innerhalb der Achse anzuzeigen, Außen, um Haupt- / Nebenmarkierungen außerhalb der Achse anzuzeigen. Im Abschnitt Beschriftungsoptionen können Sie das Erscheinungsbild der wichtigsten Häkchenbeschriftungen anpassen, auf denen Werte angezeigt werden. Um eine Beschriftungsposition in Bezug auf die vertikale Achse festzulegen, wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Keine, um keine Häkchenbeschriftungen anzuzeigen, Niedrig, um Markierungsbeschriftungen links vom Plotbereich anzuzeigen. Hoch, um Markierungsbeschriftungen rechts vom Plotbereich anzuzeigen. Neben der Achse, um Markierungsbezeichnungen neben der Achse anzuzeigen. Um das Bezeichnungsformat anzupassen, klicken Sie auf die Schaltfläche Bezeichnungsformat und wählen Sie den gewünschten Typ aus. Verfügbare Bezeichnungsformate: Allgemein Nummer Wissenschaftlich Rechnungswesen Währung Datum Zeit Prozentsatz Bruch Text Benutzerdefiniert Die Optionen für das Bezeichnungsformat variieren je nach ausgewähltem Typ. Weitere Informationen zum Ändern des Zahlenformats finden Sie auf dieser Seite. Aktivieren Sie das Kästchen Mit Quelle verknüpft, um die Formatierung der Zahlen aus der Datenquelle im Diagramm beizubehalten. Sekundärachsen werden nur in den Verbund-Diagrammen verfügbar. Sekundärachsen sind in Verbund-Diagrammen nützlich, wenn Datenreihen erheblich variieren oder gemischte Datentypen zum Zeichnen eines Diagramms verwendet werden. Sekundärachsen erleichtern das Lesen und Verstehen eines Verbund-Diagramms. Die Registerkarte Vertikale/horizontale Sekundärachse wird angezeigt, wenn Sie eine geeignete Datenreihe für ein Verbund-Diagramm auswählen. Alle Einstellungen und Optionen auf der Registerkarte Vertikale/horizontale Sekundärachse stimmen mit den Einstellungen auf der vertikalen/horizontalen Achse überein. Eine detaillierte Beschreibung der Optionen Vertikale/Horizontale Achse finden Sie in der Beschreibung oben/unten. Auf der Registerkarte Horizontale Achse können Sie die Parameter der horizontalen Achse ändern, die auch als Kategorieachse oder x-Achse bezeichnet wird und Textbeschriftungen anzeigt. Beachten Sie, dass die horizontale Achse die Werteachse ist, auf der numerische Werte für die Balkendiagramme angezeigt werden. In diesem Fall entsprechen die Optionen der Registerkarte Horizontale Achse den Optionen im vorherigen Abschnitt. Für die Punkte (XY)-Diagramme sind beide Achsen Wertachsen. Wählen Sie Ausblenden, um die horizontale Achse im Diagramm auszublenden, und lassen Sie das Kontrollkästchen deaktiviert, damit die horizontale Achse angezeigt wird. Geben Sie die Ausrichtung des Titels an und wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Kein, um keinen horizontalen Achsentitel anzuzeigen, Ohne Überlagerung, um den Titel unterhalb der horizontalen Achse anzuzeigen, Die Option Gitternetzlinien wird verwendet, um die anzuzeigenden horizontalen Gitternetzlinien anzugeben, indem die erforderliche Option aus der Dropdown-Liste ausgewählt wird: Kein, Primäre, Sekundär oder Primäre und Sekundäre. Die Option Schnittpunkt mit der Achse wird verwendet, um einen Punkt auf der horizontalen Achse anzugeben, an dem die vertikale Achse ihn kreuzen soll. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Achsenschnittpunktwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Wert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben oder den Achsenschnittpunkt auf den Minimal-/Maximalwert auf der horizontalen Achse setzen. Die Option Position der Achse wird verwendet, um anzugeben, wo die Achsentextbeschriftungen platziert werden sollen: Teilstriche oder Zwischen den Teilstrichen. Die Option Werte in umgekehrter Reihenfolge wird verwendet, um Werte in die entgegengesetzte Richtung anzuzeigen. Wenn das Kontrollkästchen deaktiviert ist, befindet sich der niedrigste Wert unten und der höchste Wert oben auf der Achse. Wenn das Kontrollkästchen aktiviert ist, werden die Werte von oben nach unten sortiert. Im Abschnitt Parameter der Teilstriche können Sie das Erscheinungsbild von Häkchen auf der horizontalen Skala anpassen. Hauptmarkierungen sind die größeren Teilungen, bei denen Beschriftungen numerische Werte anzeigen können. Kleinere Häkchen sind die Skalenunterteilungen, die zwischen den großen Häkchen platziert werden und keine Beschriftungen haben. Häkchen definieren auch, wo Gitterlinien angezeigt werden können, wenn die entsprechende Option auf der Registerkarte Layout festgelegt ist. Die Dropdown-Listen Primärer / Sekundärer Typ enthalten die folgenden Platzierungsoptionen: Die Option Primärer/Sekundärer Typ wird verwendet, um die folgenden Platzierungsoptionen anzugeben: Kein, um keine primäre/sekundäre Teilstriche anzuzeigen, Schnittpunkt, um primäre/sekundäre Teilstriche auf beiden Seiten der Achse anzuzeigen, In, um primäre/sekundäre Teilstriche innerhalb der Achse anzuzeigen, Außen, um primäre/sekundäre Teilstriche außerhalb der Achse anzuzeigen. Die Option Abstand zwischen Teilstrichen wird verwendet, um anzugeben, wie viele Kategorien zwischen zwei benachbarten Teilstrichen angezeigt werden sollen. Im Abschnitt Beschriftungsoptionen können Sie das Erscheinungsbild der wichtigsten Häkchenbeschriftungen anpassen, auf denen Werte angezeigt werden. Die Option Beschriftungsposition wird verwendet, um anzugeben, wo die Beschriftungen in Bezug auf die horizontale Achse platziert werden sollen. Wählen Sie die gewünschte Option aus der Dropdown-Liste: Kein, um die Beschriftungen nicht anzuzeigen, Niedrig, um Beschriftungen am unteren Rand anzuzeigen, Hoch, um Beschriftungen oben anzuzeigen, Neben der Achse, um Beschriftungen neben der Achse anzuzeigen. Die Option Abstand bis zur Beschriftung wird verwendet, um anzugeben, wie eng die Beschriftungen an der Achse platziert werden sollen. Sie können den erforderlichen Wert im Eingabefeld angeben. Je mehr Wert Sie einstellen, desto größer ist der Abstand zwischen Achse und Beschriftung. Die Option Abstand zwischen Teilstrichen wird verwendet, um anzugeben, wie oft die Beschriftungen angezeigt werden sollen. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall werden Beschriftungen für jede Kategorie angezeigt. Sie können die Option Manuell aus der Dropdown-Liste auswählen und den erforderlichen Wert im Eingabefeld rechts angeben. Geben Sie beispielsweise 2 ein, um Beschriftungen für jede zweite Kategorie usw. anzuzeigen. Um das Bezeichnungsformat anzupassen, klicken Sie auf die Schaltfläche Bezeichnungsformat und wählen Sie den gewünschten Typ aus. Verfügbare Bezeichnungsformate: Allgemein Nummer Wissenschaftlich Rechnungswesen Währung Datum Zeit Prozentsatz Bruch Text Benutzerdefiniert Die Optionen für das Bezeichnungsformat variieren je nach ausgewähltem Typ. Weitere Informationen zum Ändern des Zahlenformats finden Sie auf dieser Seite. Aktivieren Sie das Kästchen Mit Quelle verknüpft, um die Formatierung der Zahlen aus der Datenquelle im Diagramm beizubehalten. Im Abschnitt Andocken an die Zelle sind die folgenden Parameter verfügbar: Verschieben und Ändern der Größe mit Zellen - mit dieser Option können Sie das Diagramm an der Zelle dahinter ausrichten. Wenn sich die Zelle verschiebt (z.B. wenn Sie einige Zeilen/Spalten einfügen oder löschen), wird das Diagramm zusammen mit der Zelle verschoben. Wenn Sie die Breite oder Höhe der Zelle erhöhen oder verringern, ändert das Diagramm auch seine Größe. Verschieben, aber die Größe nicht ändern mit Zellen - mit dieser Option können Sie das Diagramm in der Zelle dahinter fixieren, um zu verhindern, dass die Größe des Diagramms geändert wird. Wenn sich die Zelle verschiebt, wird das Diagramm zusammen mit der Zelle verschoben. Wenn Sie jedoch die Zellengröße ändern, bleiben die Diagrammabmessungen unverändert. Kein Verschieben oder Ändern der Größe mit Zellen - mit dieser Option können Sie es verhindern, dass das Diagramm verschoben oder in der Größe geändert wird, wenn die Zellenposition oder -größe geändert wurde. Im Abschnitt Der alternative Text können Sie einen Titel und eine Beschreibung angeben, die Personen mit Seh- oder kognitiven Beeinträchtigungen vorgelesen werden, damit sie besser verstehen, welche Informationen das Diagramm enthält. Wenn das Diagramm eingefügt ist, können Sie die Größe und Position ändern. Sie können die Position des Diagramm auf der Folie angeben, indem Sie das Diagram vertikal oder horizontal ziehen. Sie können einem Textplatzhalter auch ein Diagramm hinzufügen, indem Sie auf das Symbol Diagramm innen drücken und den gewünschten Diagrammtyp auswählen: Es ist auch möglich, einem Folienlayout ein Diagramm hinzuzufügen. Weitere Informationen finden Sie hier. Diagrammelemente bearbeiten Um den Diagrammtitel zu bearbeiten, wählen Sie den Standardtext mit der Maus aus und geben Sie stattdessen Ihren eigenen Text ein. Um die Schriftformatierung innerhalb von Textelementen, wie beispielsweise Diagrammtitel, Achsentitel, Legendeneinträge, Datenbeschriftungen usw. zu ändern, wählen Sie das gewünschte Textelement durch Klicken mit der linken Maustaste aus. Wechseln Sie in die Registerkarte Startseite und nutzen Sie die in der Menüleiste angezeigten Symbole, um Schriftart, Schriftform, Schriftgröße oder Schriftfarbe zu bearbeiten. Wenn das Diagramm ausgewählt ist, ist das Symbol Formeinstellungen auch auf der rechten Seite verfügbar, da die Form als Hintergrund für das Diagramm verwendet wird. Sie können auf dieses Symbol klicken, um die Registerkarte Formeinstellungen in der rechten Seitenleiste zu öffnen und die Formeinstellungen anzupassen: Füllung, Strich und Textumbruch. Beachten Sie, dass Sie den Formtyp nicht ändern können. Mit der Registerkarte Formeinstellungen auf der rechten Seite können Sie nicht nur den Diagrammbereich selbst anpassen, sondern auch die Diagrammelemente wie Zeichnungsfläche, Datenreihe, Diagrammtitel, Legende usw. und wenden Sie verschiedene Füllungstypen darauf an. Wählen Sie das Diagrammelement aus, indem Sie es mit der linken Maustaste anklicken, und wählen Sie den bevorzugten Füllungstyp aus: Farbfüllung, Füllung mit Farbverlauf, Bild oder Textur, Muster. Geben Sie die Füllparameter an und stellen Sie bei Bedarf die Undurchsichtigkeit ein. Wenn Sie eine vertikale oder horizontale Achse oder Gitternetzlinien auswählen, sind die Stricheinstellungen nur auf der Registerkarte Formeinstellungen verfügbar: Farbe, Größe und Typ. Weitere Einzelheiten zum Arbeiten mit Formfarben, Füllungen und Striche finden Sie auf dieser Seite. Die Option Schatten anzeigen ist auch auf der Registerkarte Formeinstellungen verfügbar, sie ist jedoch für Diagrammelemente deaktiviert. Wenn Sie die Größe von Diagrammelementen ändern müssen, klicken Sie mit der linken Maustaste, um das gewünschte Element auszuwählen, und ziehen Sie eines der 8 weißen Quadrate entlang des Umfangs von das Element. Um die Position des Elements zu ändern, klicken Sie mit der linken Maustaste darauf, vergewissern Sie sich, dass sich Ihr Cursor in geändert hat, halten Sie die linke Maustaste gedrückt und ziehen Sie die Element in die benötigte Position. Um ein Diagrammelement zu löschen, wählen Sie es mit der linken Maustaste aus und drücken Sie die Taste Entfernen auf Ihrer Tastatur. Sie haben die Möglichkeit, 3D-Diagramme mithilfe der Maus zu drehen. Klicken Sie mit der linken Maustaste in den Diagrammbereich und halten Sie die Maustaste gedrückt. Um die 3D-Diagrammausrichtung zu ändern, ziehen Sie den Mauszeiger in die gewünschte Richtung ohne die Maustaste loszulassen. Diagrammeinstellungen anpassen Diagrammgröße, -typ und -stil sowie die zur Erstellung des Diagramms verwendeten Daten, können in der rechten Seitenleiste geändert werden. Um das Menü zu aktivieren, klicken Sie auf das Diagramm und wählen Sie rechts das Symbol Diagrammeinstellungen aus. Im Abschnitt Größe können Sie Breite und Höhe des aktuellen Diagramms ändern. Wenn Sie die Funktion Seitenverhältnis sperren aktivieren (in diesem Fall sieht das Symbol so aus ), werden Breite und Höhe gleichmäßig geändert und das ursprüngliche Seitenverhältnis des Diagramms wird beibehalten. Im Abschnitt Diagrammtyp ändern können Sie den gewählten Diagrammtyp und -stil über die entsprechende Auswahlliste ändern. Um den gewünschten Diagrammstil auszuwählen, verwenden Sie im Abschnitt Diagrammtyp ändern die zweite Auswahlliste. Über die Schaltfläche Daten ändern können Sie das Fenster Diagrammtools öffnen und die Daten ändern (wie oben beschrieben). Wenn Sie einen Doppelklick auf einem in Ihrer Präsentation enthaltenen Diagramm ausführen, öffnet sich das Fenster „Diagrammtools“. Zusätzlich sind Einstellungen für die 3D-Drehung für 3D-Diagramme verfügbar: X-Rotation - stellen Sie den gewünschten Wert für die Drehung der X-Achse mit der Tastatur oder über die Pfeile Links und Rechts nach rechts ein. Y-Rotation - stellen Sie den gewünschten Wert für die Drehung der Y-Achse mit der Tastatur oder über die Pfeile Aufwärts und Unten nach rechts ein. Perspektive - stellen Sie den gewünschten Wert für die Tiefenrotation mit der Tastatur oder über die Pfeile Blickfeld verengen und Blickfeld verbreitern nach rechts ein. Rechtwinklige Achsen - wird verwendet, um die rechtwinklige Achsenansicht einzustellen. Autoskalierung - aktivieren Sie dieses Kontrollkästchen, um die Tiefen- und Höhenwerte des Diagramms automatisch zu skalieren, oder deaktivieren Sie dieses Kontrollkästchen, um die Tiefen- und Höhenwerte manuell festzulegen. Tiefe (% der Basis) - stellen Sie den gewünschten Tiefenwert mit der Tastatur oder über die Pfeile ein. Höhe (% der Basis) - stellen Sie den gewünschten Höhenwert über die Tastatur oder über die Pfeile ein. Standardmäßige Drehung - setzen Sie die 3D-Parameter auf ihre Standardwerte. Bitte beachten Sie, dass Sie nicht jedes Element des Diagramms bearbeiten können. Die Einstellungen werden auf das Diagramm als Ganzes angewendet. Mit der Option Erweiterte Einstellungen anzeigen in der rechten Seitenleiste können Sie das Fenster Diagramm - Erweiterte Einstellungen öffnen, in dem Sie die folgenden Parameter anpassen können: Auf der Registerkarte Positionierung können Sie die folgenden Bildeigenschaften festlegen: Größe - verwenden Sie diese Option, um die Diagrammbreite und/oder -höhe zu ändern. Wenn Sie auf die Schaltfläche Seitenverhältnis beibehalten klicken (in diesem Fall sieht es so aus ), werden Breite und Höhe zusammen geändert, wobei das ursprüngliche Seitenverhältnis beibehalten wird. Position - stellen Sie die genaue Position mit den Feldern Horizontal und Vertikal sowie dem Feld Ab ein, wo Sie auf Einstellungen wie Obere linke Ecke und Zentriert zugreifen können. Die Registerkarte Alternativer Text ermöglicht die Eingabe eines Titels und einer Beschreibung, die Personen mit Sehbehinderungen oder kognitiven Beeinträchtigungen vorgelesen werden kann, damit sie besser verstehen können, welche Informationen darin enthalten sind. Wenn das Diagramm ausgewählt ist, ist rechts auch das Symbol Formeinstellungen verfügbar, da eine Form als Hintergrund für das Diagramm verwendet wird. Klicken Sie auf dieses Symbol, um die Registerkarte Formeinstellungen in der rechten Seitenleiste zu öffnen und passen Sie Füllung und Linienstärke der Form an. Beachten Sie, dass Sie den Formtyp nicht ändern können. Um das hinzugefügte Diagramm zu löschen, wählen Sie es mit der linken Maustaste aus und drücken Sie die Taste ENTF auf Ihrer Tastatur. Informationen zum Ausrichten eines Diagramms auf der Folie oder zum Anordnen mehrerer Objekte, finden Sie im Abschnitt Objekte auf einer Folie ausrichten und anordnen."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Formeln einfügen", 
        "body": "Mit dem Präsentationseditor können Sie Formeln mithilfe der integrierten Vorlagen erstellen, sie bearbeiten, Sonderzeichen einfügen (einschließlich mathematischer Operatoren, griechischer Buchstaben, Akzente usw.). Eine neue Formel einfügen Eine Formel aus den Vorlagen einfügen: Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie auf den Pfeil neben dem Symbol Gleichung. Wählen Sie die gewünschte Gleichungskategorie in der Symbolleiste über der eingefügten Gleichung aus oder Wählen Sie in der geöffneten Dropdown-Liste die gewünschte Gleichungskategorie aus. Derzeit sind die folgenden Kategorien verfügbar: Symbole, Brüche, Skripte, Wurzeln, Integrale, Große Operatoren, Klammern, Funktionen, Akzente, Grenzwerte und Logarithmen, Operators, Matrizen. Klicken Sie auf das Symbol Gleichungseinstellungen in der Symbolleiste über der eingefügten Gleichung, um auf weitere Einstellungen zuzugreifen, z. B. Unicode oder LaTeX, Aktuell - Professionell oder Aktuell - Linear und Zum Inline wechseln. Klicken Sie im entsprechenden Vorlagensatz auf das gewünschte Symbol/die gewünschte Gleichung. Das ausgewählte Symbol/die ausgewählte Formel wird an der aktuellen Cursorposition eingefügt. Wenn der Rahmen des Formelfelds nicht angezeigt wird, klicken Sie auf eine beliebige Stelle innerhalb der Formel - der Rahmen wird als gestrichelte Linie dargestellt. Das Formelfeld kann auf der Folie beliebig verschoben, in der Größe verändert oder gedreht werden. Klicken Sie dazu auf den Rahmen des Formelfelds (der Rahmen wird als durchgezogene Linie dargestellt) und nutzen Sie die entsprechenden Bedienelemente. Jede Gleichungsvorlage repräsentiert eine Reihe von Slots. Ein Slot ist eine Position für jedes Element, aus dem die Gleichung besteht. Ein leerer Platz (auch als Platzhalter bezeichnet) hat einen gepunkteten Umriss . Sie müssen alle Platzhalter mit den erforderlichen Werten ausfüllen. Werte eingeben Der Einfügepunkt gibt an, wo das nächste von Ihnen eingegebene Zeichen erscheinen wird. Um die Einfügemarke genau zu positionieren, klicken Sie in einen Platzhalter und bewegen Sie die Einfügemarke mit den Pfeiltasten der Tastatur, um ein Zeichen nach links/rechts zu bewegen. Wenn Sie den Einfügepunkt positioniert haben, können Sie die Werte in den Platzhaltern einfügen: Geben Sie den gewünschten numerischen/literalen Wert über die Tastatur ein. Wechseln Sie zum Einfügen von Sonderzeichen in die Registerkarte Einfügen und wählen Sie im Menü Formel das gewünschte Zeichen aus der Palette mit den Symbolen aus (sehen Sie die Beschreibung der Option AutoKorrekturfunktionen). Fügen Sie eine weitere Vorlage aus der Palette hinzu, um eine komplexe verschachtelte Gleichung zu erstellen. Die Größe der primären Formel wird automatisch an den Inhalt angepasst. Die Größe der verschachtelten Gleichungselemente hängt von der Platzhaltergröße der primären Gleichung ab, sie darf jedoch nicht kleiner sein, als die Vorlage für tiefgestellte Zeichen. </p> Alternativ können Sie auch über das Rechtsklickmenü neue Elemente in Ihre Formel einfügen: Um ein neues Argument vor oder nach einem vorhandenen Argument einzufügen, das in Klammern steht, klicken Sie mit der rechten Maustaste auf das vorhandene Argument und wählen Sie die Option Argument vorher/nachher einfügen. Um in Fällen mit mehreren Bedingungen eine neue Formel aus der Gruppe Klammern hinzuzufügen, klicken Sie mit der rechten Maustaste auf einen leeren Platzhalter oder eine im Platzhalter eingegebene Gleichung und wählen Sie Formel vorher/nachher einfügen aus dem Menü aus. Um in einer Matrix eine neue Zeile oder Spalte einzugeben, wählen Sie die Option Einfügen aus dem Menü, und klicken Sie dann auf Zeile oberhalb/unterhalb oder Spalte links/rechts. Derzeit können Gleichungen nicht im linearen Format eingegeben werden, d. h. \\sqrt(4&x^3). Wenn Sie die Werte der mathematischen Ausdrücke eingeben, ist es nicht notwendig die Leertaste zu verwenden, da die Leerzeichen zwischen den Zeichen und Werten automatisch gesetzt werden. Wenn die Formel zu lang ist und nicht in eine einzelnen Zeile passt, wird während der Eingabe automatisch ein Zeilenumbruch ausgeführt. Bei Bedarf können Sie auch manuell einen Zeilenumbruch an einer bestimmten Position einfügen. Klicken sie dazu mit der rechten Maustaste auf einen der Platzhalter und wählen Sie im Menü die Option manuellen Umbruch einfügen aus. Der ausgewählte Platzhalter wird in die nächste Zeile verschoben. Um einen manuell hinzugefügten Zeilenumbruch zu entfernen, klicken Sie mit der rechten Maustaste auf den Platzhalter, der die neue Zeile einleitet, und wählen Sie die Option manuellen Umbruch entfernen. Formeln formatieren Standardmäßig wird die Formel innerhalb des Textfelds horizontal zentriert und vertikal am oberen Rand des Textfelds ausgerichtet. Um die horizontale/vertikale Ausrichtung zu ändern, platzieren Sie den Cursor im Formelfeld (die Rahmen des Textfelds werden als gestrichelte Linien angezeigt) und verwenden Sie die entsprechenden Symbole auf der Registerkarte Startseite in der oberen Symbolleiste. Um die Schriftgröße der Formel zu verkleinern oder zu vergrößern, klicken Sie an eine beliebige Stelle im Formelfeld und wählen Sie in der Registerkarte Startseite die gewünschte Schriftgröße aus der Liste aus. Alle Elemente in der Formel werden entsprechend angepasst. Die Buchstaben innerhalb der Formel werden standardmäßig kursiv gestellt. Bei Bedarf können Sie Schriftart (fett, kursiv, durchgestrichen) oder Schriftfarbe für die gesamte Formel oder Teile davon ändern. Unterstreichen ist nur für die gesamte Formel möglich und nicht für einzelne Zeichen. Wählen Sie den gewünschten Teil der Formel durch anklicken und ziehen aus. Der ausgewählte Teil wird blau markiert. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Startseite, um die Auswahl zu formatieren. Sie können zum Beispiel das Kursivformat für gewöhnliche Wörter entfernen, die keine Variablen oder Konstanten darstellen. Einige Elemente aus der Formel lassen sich auch über das Rechtsklickmenü ändern: Um das Format von Brüchen zu ändern, klicken Sie mit der rechten Maustaste auf einen Bruch und wählen Sie im Menü die Option in schrägen/linearen/gestapelten Bruch ändern (die verfügbaren Optionen hängen vom ausgewählten Bruchtyp ab). Um die Position der Skripte in Bezug auf Text zu ändern, klicken Sie mit der rechten Maustaste auf die Formel, die Skripte enthält und wählen Sie die Option Skripte vor/nach Text aus dem Menü aus. Um die Größe der Argumente für Skripte, Wurzeln, Integrale, Große Operatoren, Grenzwerte und Logarithmen und Operatoren sowie für über- und untergeordnete Klammern und Vorlagen mit Gruppierungszeichen aus der Gruppe Akzente zu ändern, klicken Sie mit der rechten Maustaste auf das Argument, das Sie ändern wollen, und wählen Sie die Option Argumentgröße vergrößern/verkleinern aus dem Menü aus. Um festzulegen, ob ein leerer Grad-Platzhalter für eine Wurzel angezeigt werden soll oder nicht, klicken Sie mit der rechten Maustaste auf die Wurzel und wählen Sie die Option Grad anzeigen/ausblenden aus dem Menü aus. Um festzulegen, ob ein leerer Grenzwert-Platzhalter für ein Integral oder für Große Operatoren angezeigt werden soll oder nicht, klicken Sie mit der rechten Maustaste auf die Gleichung und wählen Sie im Menü die Option oberen/unteren Grenzwert anzeigen/ausblenden aus. Um die Position der Grenzwerte in Bezug auf das Integral oder einen Operator für Integrale oder einen großen Operator zu ändern, klicken Sie mit der rechten Maustaste auf die Formel und wählen Sie die Option Position des Grenzwertes ändern aus dem Menü aus. Die Grenzwerte können rechts neben dem Operatorzeichen (als tiefgestellte und hochgestellte Zeichen) oder direkt über und unter dem Operatorzeichen angezeigt werden. Um die Positionen der Grenzwerte für Grenzwerte und Logarithmen und Vorlagen mit Gruppierungszeichen aus der Gruppe Akzente zu ändern, klicken Sie mit der rechten Maustaste auf die Formel und wählen Sie die Option Grenzwert über/unter Text aus dem Menü aus. Um festzulegen, welche Klammern angezeigt werden sollen, klicken Sie mit der rechten Maustaste auf den darin enthaltenen Ausdruck und wählen Sie die Option öffnende/schließende Klammer anzeigen/verbergen aus dem Menü aus. Um die Größe der Klammern zu ändern, klicken Sie mit der rechten Maustaste auf den darin enthaltenen Ausdruck. Standardmäßig ist die Option Klammern ausdehnen aktiviert, so dass die Klammern an den eingegebenen Ausdruck angepasst werden. Sie können diese Option jedoch deaktivieren und die Klammern werden nicht mehr ausgedehnt. Wenn die Option aktiviert ist, können Sie auch die Option Klammern an Argumenthöhe anpassen verwenden. Um die Position der Zeichen, in Bezug auf Text für Klammern (über dem Text/unter dem Text) oder Überstriche/Unterstriche aus der Gruppe Akzente, zu ändern, klicken Sie mit der rechten Maustaste auf die Vorlage und wählen Sie die Option Überstrich/Unterstrich über/unter Text aus dem Menü aus. Um festzulegen, welche Rahmen aus der Gruppe Akzente für die umrandete Formel angezeigt werden sollen, klicken Sie mit der rechten Maustaste auf die Formel, klicken Sie im Menü auf die Option Umrandungen und legen Sie die Parameter Einblenden/Ausblenden oberer/unterer/rechter/linker Rand oder Hinzufügen/Verbergen horizontale/vertikale/diagonale Grenzlinie fest. Um festzulegen, ob ein leerer Platzhalter für eine Matrix angezeigt werden soll oder nicht, klicken Sie mit der rechten Maustaste darauf und wählen Sie die Option Platzhalter einblenden/ausblenden aus dem Menü aus. Einige Elemente aus der Formel lassen sich auch über das Rechtsklickmenü ausrichten: Um Formeln in Fällen mit mehreren Bedingungen aus der Gruppe Klammern auszurichten (oder beliebige andere Formeln, wenn Sie zuvor über die Eingabetaste einen neuen Platzhalter eingefügt haben), klicken Sie mit der rechten Maustaste auf eine Formel, wählen Sie die Option Ausrichten im Menü aus und legen Sie den Ausrichtungstyp fest: Oben, Zentriert oder Unten. Um eine Matrix vertikal auszurichten, klicken Sie mit der rechten Maustaste auf die Matrix, wählen Sie die Option Matrixausrichtung aus dem Menü aus und legen Sie den Ausrichtungstyp fest: Oben, Zentriert oder Unten. Um Elemente in einer Matrix-Spalte vertikal auszurichten, klicken Sie mit der rechten Maustaste auf einen Platzhalter in der Spalte, wählen Sie die Option Spaltenausrichtung aus dem Menü aus und legen Sie den Ausrichtungstyp fest: Links, Zentriert oder Rechts. Formelelemente löschen Um Teile einer Formel zu löschen, wählen Sie den Teil den Sie löschen wollen mit der Maus aus oder halten Sie die Umschalttaste gedrückt, und drücken sie dann auf Ihrer Tastatur auf ENTF. Ein Slot kann nur zusammen mit der zugehörigen Vorlage gelöscht werden. Um die gesamte Formel zu löschen, klicken Sie auf die Umrandung der Formel (der Rahmen wird als durchgezogene Linie dargestellt) und drücken Sie dann auf Ihrer Tastatur auf ENTF. Einige Elemente aus der Formel lassen sich auch über das Rechtsklickmenü löschen: Um eine Wurzel zu löschen, klicken Sie diese mit der rechten Maustaste an und wählen Sie die Option Wurzel löschen im Menü aus. Um ein tiefgestelltes Zeichen bzw. ein hochgestelltes Zeichen zu löschen, klicken sie mit der rechten Maustaste auf das entsprechende Element und wählen Sie die Option hochgestelltes/tiefgestelltes Zeichen entfernen im Menü aus. Wenn der Ausdruck Skripte mit Vorrang vor dem Text enthält, ist die Option Skripte entfernen verfügbar. Um Klammern zu entfernen, klicken Sie mit der rechten Maustaste auf den darin enthaltenen Ausdruck und wählen Sie die Option Umschließende Zeichen entfernen oder die Option Umschließende Zeichen und Trennzeichen entfernen im Menü aus. Wenn ein Ausdruck in Klammern mehr als ein Argument enthält, klicken Sie mit der rechten Maustaste auf das Argument das Sie löschen wollen und wählen Sie die Option Argument löschen im Menü aus. Wenn Klammern mehr als eine Formel umschließen (in Fällen mit mehreren Bedingungen), klicken Sie mit der rechten Maustaste auf die Formel die Sie löschen wollen und wählen Sie die Option Formel löschen im Menü aus. Um einen Grenzwert zu löschen, klicken Sie diesen mit der rechten Maustaste an und wählen Sie die Option Grenzwert entfernen im Menü aus. Um einen Akzent zu löschen, klicken Sie diesen mit der rechten Maustaste an und wählen Sie im Menü die Option Akzentzeichen entfernen, Überstrich entfernen oder Unterstrich entfernen (die verfügbaren Optionen hängen vom ausgewählten Akzent ab). Um eine Zeile bzw. Spalte in einer Matrix zu löschen, klicken Sie mit der rechten Maustaste auf den Platzhalter in der entsprechenden Zeile/Spalte, wählen Sie im Menü die Option Entfernen und wählen Sie dann Zeile/Spalte entfernen. Gleichungen konvertieren Wenn Sie ein vorhandenes Dokument öffnen, das Formeln enthält, die mit einer alten Version des Formeleditors erstellt wurden (z. B. mit MS Office-Versionen vor 2007), müssen Sie diese Formeln in das Office Math ML-Format konvertieren, um sie bearbeiten zu können. Um eine Gleichung zu konvertieren, doppelklicken Sie darauf. Das Warnfenster wird angezeigt: Um nur die ausgewählte Gleichung zu konvertieren, klicken Sie im Warnfenster auf die Schaltfläche Ja. Um alle Gleichungen in diesem Dokument zu konvertieren, aktivieren Sie das Kontrollkästchen Auf alle Gleichungen anwenden und klicken Sie auf Ja. Nachdem die Gleichung konvertiert wurde, können Sie sie bearbeiten."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Fußzeilen einfügen", 
        "body": "Die Fußzeilen fügen weitere Information auf den ausgegebenen Folien hin, z.B. Datum und Uhrzeit, Foliennummer oder Text. Um eine Fußzeile einzufügen im Präsentationseditor: öffnen Sie die Registerkarte Einfügen, klicken Sie die Schaltfläche Fußzeile bearbeiten in der oberen Symbolleiste an, das Fenster Fußzeileneinstellungen wird geöffnet. Markieren Sie die Kästchen mit den Daten, die Sie in die Fußzeile einfügen möchten. Die Änderungen werden im Vorschaufenster rechts angezeigt. markieren Sie das Kästchen Datum und Uhrzeit, um das Datum und die Uhrzeit im ausgewählten Format einzufügen. Das Datum wird links in der Fußzeile eingefügt. Wählen Sie das gewünschte Datumsformat aus: Automatisch aktualisieren - markieren Sie das Optionsfeld, um das Datum und die Uhrzeit automatisch entsprechend dem aktuellen Datum und der aktuellen Uhrzeit zu aktualisieren. Wählen Sie das gewünschte Datums- und Uhrzeitformat und die Sprache aus den Listen aus. Fixiert - markieren Sie das Optionsfeld, um das Datum und die Uhrzeit automatisch nicht zu aktualisieren. Markieren Sie das Kästchen Foliennummer, um die aktive Foliennummer einzufügen. Die Foliennummer wird rechts in der Fußzeile eingefügt. Markieren Sie das Kästchen Text in der Fußzeile, um einen Text einzufügen. Geben Sie den gewünschten Text im Feld nach unten ein. Der Text wird in der Fußzeile zentriert. Markieren Sie gegebenenfalls das Kästchen Nicht auf der Titelfolie anzeigen, Klicken Sie die Schaltfläche Auf alle anwenden an, um die Änderungen auf alle Folien anzuwenden, oder die Schaltfläche Anwenden, um die Änderungen nur für die aktive Folie anzunehmen. Um das Datum und die Uhrzeit oder die Foliennummer schnell in der Fußzeile der aktiven Folie einzufügen, verwenden Sie die Optionen Foliennummer anzeigen und Datum und Uhrzeit anzeigen im Abschnitt Folieneinstellungen in der rechten Randleiste. Die aktivierten Einstellungen werden nur für die aktive Folie angenommen. Das eingefügte Datum und die Uhrzeit oder die Foliennummer können Sie später im Fenster Fußzeileneinstellungen konfigurieren. Um die eingefügte Fußzeile zu bearbeiten, klicken Sie die Schaltfläche Fußzeile bearbeiten in der oberen Symbolleiste an, konfigurieren Sie die gewünschte Einstellungen im Fenster Fußzeileneinstellungen und klicken Sie auf Anwenden oder Auf alle anwenden, um die Änderungen zu speichern. Das Datum und die Uhrzeit oder die Foliennummer im Textfeld einfügen Sie können das Datum und die Uhrzeit oder die Foliennummer im aktiven Textfeld durch die entsprechenden Schaltflächen auf der Registerkarte Einfügen in der oberen Symbolleiste einfügen. Das Datum und die Uhrzeit einfügen Positionieren Sie den Mauscursor im Textfeld, wo Sie das Datum und die Uhrzeit einfügen möchten, klicken Sie die Schaltfläche Datum und Uhrzeit auf der Registerkarte Einfügen in der oberen Symbolleiste an, wählen Sie die gewünschte Sprache aus der Liste aus und bestimmen Sie das gewünschte Format für das Datum und die Uhrzeit im Fenster Datum und Uhrzeit, markieren Sie gegebenenfalls die Kästchen Automatisch aktualisieren oder Als Standard setzen, um das Datums- und Uhrzeitformat entsprechend der ausgewählten Sprache zu konfigurieren, klicken Sie auf OK, um die Änderungen anzunehmen. Das Datum und die Uhrzeit oder die Foliennummer werden entsprechend der Cursorposition eingefügt. Um das Datum und die Uhrzeit zu bearbeiten, wählen Sie das eingefügte Datum und die Uhrzeit im Textfeld aus, klicken Sie die Schaltfläche Datum und Uhrzeit auf der Registerkarte Einfügen in der oberen Symbolleiste an, wählen Sie das gewünschte Format im Fenster Datum und Uhrzeit, klicken Sie auf OK. Die Foliennummer einfügen Positionieren Sie den Mauscursor im Textfeld, wo Sie die Foliennummer einfügen möchten. Klicken Sie die Schaltfläche Foliennummer auf der Registerkarte Einfügen in der oberen Symbolleiste an. Markieren Sie das Kästchen Foliennummer im Fenster Fußzeileneinstellungen. Klicken Sie auf OK, um die Änderungen anzunehmen. Die Foliennummer wird entsprechend der Cursorposition eingefügt."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Bilder einfügen und anpassen", 
        "body": "Bild einfügen Im Präsentationseditor können Sie Bilder in den gängigen Formaten in Ihre Präsentation einfügen. Die folgenden Formate werden unterstützt: BMP, GIF, JPEG, JPG, PNG. Um ein Bild in eine Folie einzufügen: Markieren Sie mit dem Cursor die Folie in der Folienliste links, in die Sie ein Bild einfügen möchten. Klicken Sie auf das Symbol Bild auf der Registerkarte Startseite oder Einfügen in der oberen Symbolleiste, Wählen Sie eine der folgenden Optionen, um das Bild hochzuladen: Mit der Option Bild aus Datei öffnen Sie das Standarddialogfenster zur Dateiauswahl. Durchsuchen Sie die Festplatte Ihres Computers nach der gewünschten Bilddatei und klicken Sie auf Öffnen. Im Online-Editor können Sie mehrere Bilder gleichzeitig auswählen. Mit der Option Bild aus URL öffnen Sie das Fenster zum Eingeben der erforderlichen Webadresse, wenn Sie die Adresse eingegeben haben, klicken Sie auf OK. Die Option Bild aus Speicher öffnet das Fenster Datenquelle auswählen. Wählen Sie ein in Ihrem Portal gespeichertes Bild aus und klicken Sie auf die Schaltfläche OK. Wenn Sie das Bild hinzugefügt haben, können Sie Größe und Position ändern. Sie können auch ein Bild in einen Textplatzhalter einfügen, indem Sie das Bild aus Datei darin klicken und das erforderliche Bild auswählen, das auf Ihrem PC gespeichert ist, oder verwenden Sie die Schaltfläche Bild aus URL und geben Sie die URL-Adresse des Bildes: Es ist auch möglich, ein Bild zu einem Folienlayout hinzuzufügen. Weitere Informationen finden Sie in dieser Artikel. Bildeinstellungen anpassen Klicken Sie mit der linken Maustaste ein Bild an und wählen Sie rechts das Symbol Bildeinstellungen aus, um die rechte Seitenleiste zu aktivieren. Hier finden Sie die folgenden Abschnitte:Größe - um Breite und Höhe des aktuellen Bildes einzusehen oder bei Bedarf die Standardgröße des Bildes wiederherzustellen. Mit der Schaltfläche Zuschneiden können Sie das Bild zuschneiden. Klicken Sie auf die Schaltfläche Zuschneiden, um die Ziehpunkte zu aktivieren, die an den Bildecken und in der Mitte der Bildseiten angezeigt werden. Ziehen Sie die Ziehpunkte manuell, um den Zuschneidebereich festzulegen. Wenn Sie den Mauszeiger über den Zuschneidebereich bewegen, ändert sich der Zeiger in das Symbol und Sie können die Auswahl in die gewünschte Position ziehen. Um eine einzelne Seite zuzuschneiden, ziehen Sie den Ziehpunkt in der Mitte dieser Seite. Um zwei benachbarte Seiten gleichzeitig zuzuschneiden, ziehen Sie einen der Ziehpunkte in den Ecken. Um zwei gegenüberliegende Seiten des Bildes gleichermaßen zuzuschneiden, halten Sie die Strg-Taste gedrückt, während Sie den Ziehpunkt in der Mitte einer dieser Seiten ziehen. Um alle Seiten des Bildes gleichermaßen zuzuschneiden, halten Sie die Strg-Taste gedrückt und ziehen Sie gleichzeitig einen der Ziehpunkt in den Ecken. Wenn der Zuschneidebereich festgelegt ist, klicken Sie erneut auf die Schaltfläche Zuschneiden oder drücken Sie die Taste Esc oder klicken Sie auf eine beliebige Stelle außerhalb des Zuschneidebereichs, um die Änderungen zu übernehmen. Nachdem der Zuschneidebereich ausgewählt wurde, können Sie auch die Optionen Auf Form zuschneiden, Füllen und Anpassen verwenden, die im Drop-Down-Menü Zuschneiden verfügbar sind. Klicken Sie erneut auf die Schaltfläche Zuschneiden und wählen Sie die gewünschte Option aus: Wenn Sie die Option Auf Form zuschneiden auswählen, füllt das Bild eine bestimmte Form aus. Sie können eine Form aus der Galerie auswählen, die geöffnet wird, wenn Sie Ihren Mauszeiger über die Option Auf Form zuschneiden bewegen. Sie können weiterhin die Optionen Ausfüllen und Anpassen verwenden, um auszuwählen, wie Ihr Bild an die Form angepasst wird. Wenn Sie die Option Ausfüllen auswählen, wird der zentrale Teil des Originalbilds beibehalten und zum Ausfüllen des ausgewählten Zuschneidebereichs verwendet, während andere Teile des Bildes entfernt werden. Wenn Sie die Option Anpassen auswählen, wird die Bildgröße so angepasst, dass sie der Höhe oder Breite des Zuschneidebereichs entspricht. Es werden keine Teile des Originalbilds entfernt, es können jedoch leere Bereiche innerhalb des ausgewählten Zuschneidebereichs erscheinen. Bild ersetzen - das aktuelle Bild durch ein anderes Bild ersetzen und die entsprechende Quelle auswählen. Die folgenden Optionen stehen Ihnen zur Verfügung: Aus Datei oder Aus URL. Die Option Bild ersetzen ist auch im Rechtsklickmenü verfügbar. Drehen dient dazu das Bild um 90 Grad im oder gegen den Uhrzeigersinn zu drehen und die Form horizontal oder vertikal zu spiegeln. Wählen Sie eine der folgenden Optionen: um das Bild um 90 Grad gegen den Uhrzeigersinn zu drehen um das Bild um 90 Grad im Uhrzeigersinn zu drehen um das Bild horizontal zu spiegeln (von links nach rechts) um das Bild vertikal zu spiegeln (von oben nach unten) Wenn Sie das Bild ausgewählt haben, ist rechts auch das Symbol Formeinstellungen verfügbar. Klicken Sie auf dieses Symbol, um die Registerkarte Formeinstellungen in der rechten Seitenleiste zu öffnen und die Form anzupassen, d.h. den Linientyp, Größe und Farbe auswählen oder die Form ändern und im Menü AutoForm ändern eine neue Form auswählen. Die Form des Bildes ändert sich entsprechend Ihrer Auswahl. Auf der Registerkarte Formeinstellungen können Sie auch die Option Schatten anzeigen verwenden, um dem Bild einen Schatten hinzuzufügen. Um die erweiterten Einstellungen des Bildes zu ändern, klicken Sie mit der rechten Maustaste auf das Bild und wählen Sie die Option Bild - erweiterte Einstellungen im Menü aus, oder klicken Sie in der rechten Seitenleiste auf die Verknüpfung Erweiterte Einstellungen anzeigen. Das Fenster mit den Bildeigenschaften wird geöffnet: In der Registerkarte Positionierung können Sie die folgenden Bildeigenschaften festlegen: Größe - mit diesen Optionen können Sie die Breite bzw. Höhe des Bildes ändern. Wenn Sie die Funktion Seitenverhältnis beibehalten aktivieren (in diesem Fall sieht das Symbol so aus ), werden Breite und Höhe gleichmäßig geändert und das ursprüngliche Bildseitenverhältnis wird beibehalten. Um die Standardgröße des hinzugefügten Bildes wiederherzustellen, klicken Sie auf Tatsächliche Größe. Position - stellen Sie die genaue Position mit den Feldern Horizontal und Vertikal sowie dem Feld Ab ein, wo Sie auf Einstellungen wie Obere linke Ecke und Zentriert zugreifen können. Die Registerkarte Rotation umfasst die folgenden Parameter: Winkel - mit dieser Option lässt sich das Bild in einem genau festgelegten Winkel drehen. Geben Sie den erforderlichen Wert in Grad in das Feld ein oder stellen Sie diesen mit den Pfeilen rechts ein. Gekippt - Aktivieren Sie das Kontrollkästchen Horizontal, um das Bild horizontal zu spiegeln (von links nach rechts), oder aktivieren Sie das Kontrollkästchen Vertikal, um das Bild vertikal zu spiegeln (von oben nach unten). Die Registerkarte Alternativer Text ermöglicht die Eingabe eines Titels und einer Beschreibung, die Personen mit Sehbehinderungen oder kognitiven Beeinträchtigungen vorgelesen werden kann, damit sie besser verstehen können, welche Informationen im Bild enthalten sind. Um das eingefügte Bild zu löschen, wählen Sie es mit der linken Maustaste aus und drücken Sie die Taste ENTF auf Ihrer Tastatur. Informationen zum Ausrichten eines Bildes auf der Folie oder zum Anordnen mehrerer Bilder finden Sie im Abschnitt Objekte auf einer Folie ausrichten und anordnen."
    },
   {
        "id": "UsageInstructions/InsertSmartArt.htm", 
        "title": "SmartArt-Objekte einfügen", 
        "body": "SmartArt-Grafiken werden verwendet, um eine visuelle Darstellung einer hierarchischen Struktur zu erstellen, indem ein Layout ausgewählt wird, das am besten passt. Fügen Sie SmartArt-Objekte ein oder bearbeiten Sie die in Editoren von Drittanbietern hinzugefügten Objekte. Um ein SmartArt-Objekt einzufügen, Gehen Sie zur Registerkarte Einfügen. Klicken Sie auf die Schaltfläche SmartArt. Bewegen Sie den Mauszeiger über einen der verfügbaren Layoutstile, z. B. Liste oder Prozess. Wählen Sie einen der verfügbaren Layouttypen aus der Liste, die rechts neben dem hervorgehobenen Menüelement angezeigt wird. Sie können die SmartArt-Einstellungen im rechten Bereich anpassen: Bitte beachten Sie, dass Farb-, Stil- und Formtypeinstellungen individuell angepasst werden können. Füllen - Verwenden Sie diesen Abschnitt, um die automatische SmartArt-Objekt-Füllung auszuwählen. Sie können folgende Optionen auswählen: Farbfüllung - Wählen Sie diese Option aus, um die Volltonfarbe anzugeben, mit der Sie den Innenraum des ausgewählten SmartArt-Objektes füllen möchten. Füllung mit Farbverlauf - wählen Sie diese Option, um die Form mit einem sanften Übergang von einer Farbe zu einer anderen zu füllen. Die verfügbaren Menüoptionen: Stil - wählen Sie Linear oder Radial aus: Linear wird verwendet, wenn Ihre Farben von links nach rechts, von oben nach unten oder in einem beliebigen Winkel in eine Richtung fließen sollen. Das Vorschaufenster Richtung zeigt die ausgewählte Verlaufsfarbe an. Klicken Sie auf den Pfeil, um eine voreingestellte Verlaufsrichtung auszuwählen. Verwenden Sie Winkel-Einstellungen für einen präzisen Verlaufswinkel. Radial wird verwendet, um sich von der Mitte zu bewegen, da die Farbe an einem einzelnen Punkt beginnt und nach außen ausstrahlt. Punkt des Farbverlaufs ist ein bestimmter Punkt für den Verlauf von einer Farbe zur anderen. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs einfügen oder den Schieberegler, um einen Punkt des Verlaufs einzufügen. Sie können bis zu 10 Punkte einfügen. Jeder nächste eingefügte Punkt des Farbverlaufs beeinflusst in keiner Weise die aktuelle Darstellung der Farbverlaufsfüllung. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs entfernen, um den bestimmten Punkt zu löschen. Verwenden Sie den Schieberegler, um die Position des Farbverlaufspunkts zu ändern, oder geben Sie Position in Prozent an, um eine genaue Position zu erhalten. Um eine Farbe auf einen Verlaufspunkt anzuwenden, klicken Sie auf einen Punkt im Schieberegler und dann auf Farbe, um die gewünschte Farbe auszuwählen. Bild oder Textur - Wählen Sie diese Option, um ein Bild oder eine vordefinierte Textur als Formhintergrund zu verwenden. Wenn Sie ein Bild als Hintergrund für die Form verwenden möchten, können Sie ein Bild aus der Datei hinzufügen, indem Sie es auf der Festplatte Ihres Computers auswählen, oder aus der URL, indem Sie die entsprechende URL-Adresse in das geöffnete Fenster einfügen. Wenn Sie eine Textur als Hintergrund für die Form verwenden möchten, öffnen Sie das Menü Von Textur und wählen Sie die gewünschte Texturvoreinstellung aus. Derzeit sind folgende Texturen verfügbar: Leinwand, Karton, dunkler Stoff, Maserung, Granit, graues Papier, Strick, Leder, braunes Papier, Papyrus, Holz. Falls das ausgewählte Bild weniger oder mehr Abmessungen als die automatische Form hat, können Sie die Einstellung Dehnen oder Kacheln aus der Dropdown-Liste auswählen.</p> Mit der Option Dehnen können Sie die Bildgröße an die Größe der automatischen Form anpassen, sodass sie den Raum vollständig ausfüllen kann. Mit der Option Kacheln können Sie nur einen Teil des größeren Bilds anzeigen, wobei die ursprünglichen Abmessungen beibehalten werden, oder das kleinere Bild wiederholen, wobei die ursprünglichen Abmessungen über der Oberfläche der automatischen Form beibehalten werden, sodass der Raum vollständig ausgefüllt werden kann. Jede ausgewählte Texturvoreinstellung füllt den Raum vollständig aus. Sie können jedoch bei Bedarf den Dehnungseffekt anwenden. Muster - Wählen Sie diese Option, um die Form mit einem zweifarbigen Design zu füllen, das aus regelmäßig wiederholten Elementen besteht. Muster - Wählen Sie eines der vordefinierten Designs aus dem Menü. Vordergrundfarbe - Klicken Sie auf dieses Farbfeld, um die Farbe der Musterelemente zu ändern. Hintergrundfarbe - Klicken Sie auf dieses Farbfeld, um die Farbe des Musterhintergrunds zu ändern. Keine Füllung - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten. Strich - Verwenden Sie diesen Abschnitt, um die Breite, Farbe oder den Typ des Strichs für das SmartArt-Objekt zu ändern. Um die Strichbreite zu ändern, wählen Sie eine der verfügbaren Optionen aus der Dropdown-Liste Größe. Die verfügbaren Optionen sind: 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt. Alternativ können Sie die Option Keine Linie auswählen, wenn Sie keinen Strich verwenden möchten. Um die Strichfarbe zu ändern, klicken Sie auf das farbige Feld unten und wählen Sie die gewünschte Farbe aus. Um den Strich-Typ zu ändern, wählen Sie die erforderliche Option aus der entsprechenden Dropdown-Liste aus (standardmäßig wird eine durchgezogene Linie angewendet, Sie können sie in eine der verfügbaren gestrichelten Linien ändern). Schatten anzeigen - Aktivieren Sie diese Option, um die Form mit Schatten anzuzeigen. Klicken Sie auf den Link Erweiterte Einstellungen anzeigen, um die erweiterten Einstellungen zu öffnen."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Symbole und Sonderzeichen einfügen", 
        "body": "Während des Arbeitsprozesses im Präsentationseditor wollen Sie ein Symbol einfügen, das sich nicht auf der Tastatur befindet. Um solche Symbole einzufügen, verwenden Sie die Option Symbol einfügen: positionieren Sie den Textcursor an der Stelle für das Sonderzeichen, öffnen Sie die Registerkarte Einfügen, klicken Sie Symbol an, Das Dialogfeld Symbol wird angezeigt, in dem Sie das gewünschte Symbol auswählen können, öffnen Sie das Dropdown-Menü Bereich, um ein Symbol schnell zu finden. Alle Symbole sind in Gruppen unterteilt, wie z.B. “Währungssymbole” für Währungszeichen. Falls Sie das gewünschte Symbol nicht finden können, wählen Sie eine andere Schriftart aus. Viele von ihnen haben auch die Sonderzeichen, die es nicht in den Standartsatz gibt. Sie können auch das Unicode HEX Wert-Feld verwenden, um den Code einzugeben. Die Codes können Sie in der Zeichentabelle finden. Verwenden Sie auch die Registerkarte Sonderzeichen, um ein Sonderzeichen auszuwählen. Die Symbole, die zuletzt verwendet wurden, befinden sich im Feld Kürzlich verwendete Symbole, klicken Sie Einfügen an. Das ausgewählte Symbol wird eingefügt. ASCII-Symbole einfügen Man kann auch die ASCII-Tabelle verwenden, um die Zeichen und Symbole einzufügen. Drücken und halten Sie die ALT-Taste und verwenden Sie den Ziffernblock, um einen Zeichencode einzugeben. Verwenden Sie nur den Ziffernblock. Um den Ziffernblock zu aktivieren, drücken Sie die NumLock-Taste. Z.B., um das Paragraphenzeichen (§) einzufügen, drücken und halten Sie die ALT-Taste und geben Sie 789 ein, dann lassen Sie die ALT-Taste los. Symbole per Unicode-Tabelle einfügen Sonstige Symbole und Zeichen befinden sich auch in der Windows-Symboltabelle. Um diese Tabelle zu öffnen: geben Sie “Zeichentabelle” in dem Suchfeld ein, drücken Sie die Windows-Taste+R und geben Sie charmap.exe in dem Suchfeld ein, dann klicken Sie OK. Wählen Sie die Zeichensätze, Gruppen und Schriftarten aus. Klicken Sie die gewünschte Zeichen an, dann kopieren und fügen an der gewünschten Stelle ein."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Tabellen einfügen und formatieren", 
        "body": "Eine Tabelle einfügen Eine Tabelle in eine Folie einfügen im Präsentationseditor: Wählen Sie die gewünschte Folie aus, in die Sie eine Tabelle einfügen möchten. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie auf das Symbol Tabelle. Wählen Sie die gewünschte Option für die Erstellung einer Tabelle: Tabelle mit einer vordefinierten Zellenanzahl (maximal 10 x 8 Zellen) Wenn Sie schnell eine Tabelle erstellen möchten, wählen Sie einfach die Anzahl der Zeilen (maximal 8) und Spalten (maximal 10) aus. Eine benutzerdefinierte Tabelle Wenn Sie eine Tabelle mit mehr als 10 x 8 Zellen benötigen, wählen Sie die Option Benutzerdefinierte Tabelle einfügen. Geben Sie nun im geöffneten Fenster die gewünschte Anzahl der Zeilen und Spalten an und klicken Sie anschließend auf OK. Wenn Sie eine Tabelle als OLE-Objekt einfügen möchten: Wählen Sie die Option Tabelle einfügen im Menü Tabelle auf der Registerkarte Einfügen. Es erscheint das entsprechende Fenster, in dem Sie die erforderlichen Daten eingeben und mit den Formatierungswerkzeugen der Tabellenkalkulation wie Auswahl von Schriftart, Typ und Stil, Zahlenformat einstellen, Funktionen einfügen, Tabellen formatieren usw. sie formatieren. Die Kopfzeile enthält die Schaltfläche Sichtbarer Bereich in der oberen rechten Ecke des Fensters. Wählen Sie die Option Sichtbaren Bereich bearbeiten, um den Bereich auszuwählen, der angezeigt wird, wenn das Objekt in die Präsentation eingefügt wird; andere Daten gehen nicht verloren, sie werden nur ausgeblendet. Klicken Sie auf Fertig, wenn Sie fertig sind. Klicken Sie auf die Schaltfläche Sichtbaren Bereich anzeigen, um den ausgewählten Bereich mit einem blauen Rand anzuzeigen. Wenn Sie fertig sind, klicken Sie auf die Schaltfläche Speichern und beenden. Wenn Sie eine Tabelle eingefügt haben, können Sie Eigenschaften und Position verändern. Sie können auch eine Tabelle in einen Textplatzhalter einfügen, indem Sie auf das Symbol Tabelle darin drücken und die gewünschte Anzahl der Zellen auswählen oder die Option Benutzerdefinierte Tabelle einfügen verwenden: Um die Größe einer Tabelle zu ändern, ziehen Sie die Griffe an ihren Rändern, bis die Tabelle die erforderliche Größe erreicht. Sie können die Breite einer bestimmten Spalte oder die Höhe einer Zeile auch manuell ändern. Bewegen Sie den Mauszeiger über den rechten Rand der Spalte, sodass sich der Mauszeiger in den bidirektionalen Pfeil verwandelt , und ziehen Sie den Rand nach links oder rechts, um die erforderliche Breite einzustellen. Um die Höhe einer einzelnen Zeile manuell zu ändern, bewegen Sie den Mauszeiger über den unteren Rand der Zeile, bis sich der Cursor in den bidirektionalen Pfeil verwandelt , und ziehen Sie den Rand nach oben oder unten. Sie können die Tabellenposition auf der Folie festlegen, indem Sie sie vertikal oder horizontal ziehen. Um sich in einer Tabelle zu bewegen, können Sie Tastaturkürzel verwenden. Es ist auch möglich, einem Folienlayout eine Tabelle hinzuzufügen. Weitere Informationen finden Sie in diesem Artikel. Tabelleneinstellungen anpassen Die meisten Tabelleneigenschaften sowie die Struktur, können Sie in der rechten Seitenleiste ändern. Um diese zu aktivieren, klicken Sie auf die Tabelle und wählen Sie rechts das Symbol Tabelleneinstellungen aus. In den Abschnitten Zeilen und Spalten, haben Sie die Möglichkeit, bestimmte Zeilen/Spalten hervorzuheben, eine bestimmte Formatierung anzuwenden oder die Zeilen/Spalten in den verschiedenen Hintergrundfarben einzufärben, um sie klar zu unterscheiden. Folgende Auswahlmöglichkeiten stehen zur Verfügung: Kopfzeile - die erste Zeile der Tabelle wird durch eine bestimmte Formatierung hervorgehoben. Ergebniszeile - die letzte Zeile der Tabelle wird durch eine bestimmte Formatierung hervorgehoben. Gebänderte Zeilen - gerade und ungerade Zeilen werden unterschiedlich formatiert. Erste Spalte - die erste Spalte der Tabelle wird durch eine bestimmte Formatierung hervorgehoben. Letzte Spalte - die letzte Spalte der Tabelle wird durch eine bestimmte Formatierung hervorgehoben. Gebänderte Spalten - gerade und ungerade Spalten werden unterschiedlich formatiert. Im Abschnitt Aus Vorlage wählen können Sie einen vordefinierten Tabellenstil auswählen. Jede Vorlage kombiniert bestimmte Formatierungsparameter, wie Hintergrundfarbe, Rahmenstil, Zellen-/Spaltenformat usw. Abhängig von den in den Abschnitten Zeilen oder Spalten ausgewählten Optionen, werden die Vorlagen unterschiedlich dargestellt. Wenn Sie zum Beispiel die Option Kopfzeile im Abschnitt Zeilen und die Option Gebänderte Spalten im Abschnitt Spalten aktiviert haben, enthält die angezeigte Vorlagenliste nur Vorlagen mit Kopfzeile und gebänderten Spalten: Im Abschnitt Rahmenstil können Sie die angewandte Formatierung ändern, die der gewählten Vorlage entspricht. Sie können die ganze Tabelle auswählen oder einen bestimmten Zellenbereich, dessen Formatierung Sie ändern möchten, und alle Parameter manuell bestimmen. Rahmen - legen Sie die Stärke des Rahmens in der Liste fest (oder wählen Sie die Option Kein Rahmen) sowie den gewünschten Rahmenstil und wählen Sie die Rahmenfarbe aus den verfügbaren Paletten aus: Hintergrundfarbe - wählen Sie eine Farbe für den Hintergrund innerhalb der ausgewählten Zellen. Im Abschnitt Zeilen &amp; Spalten können Sie folgende Vorgänge durchzuführen: Auswählen - eine Zeile, Spalte, Zelle (abhängig von der Cursorposition) oder die ganze Tabelle auswählen. Einfügen eine neue Zeile unter oder über der ausgewählten Zeile bzw. eine neue Spalte links oder rechts von der ausgewählten Spalte einfügen. Löschen eine Zeile, Spalte, Zelle (abhängig von der Cursorposition) oder die ganze Tabelle löschen. Zellen verbinden - die ausgewählten Zellen in einer Zelle zusammenführen. Zelle teilen... - die ausgewählte Zelle in mehrere Zellen oder Spalten teilen. Diese Option öffnet das folgende Fenster: Geben Sie die gewünschte Spaltenanzahl und Zeilenanzahl für die Teilung der ausgewählten Zelle an und klicken Sie auf OK. Die Optionen im Abschnitt Zeilen &amp; Spalten sind auch über das Rechtsklickmenü zugänglich. Der Abschnitt Zellengröße wird verwendet, um die Breite und Höhe der aktuell ausgewählten Zelle anzupassen. In diesem Abschnitt können Sie auch Zeilen verteilen, sodass alle ausgewählten Zellen die gleiche Höhe haben, oder Spalten verteilen, sodass alle ausgewählten Zellen die gleiche Breite haben. Die Optionen Zeilen/Spalten verteilen sind auch über das Rechtsklickmenü zugänglich. Die erweiterten Einstellungen der Tabelle anpassen Um die erweiterten Tabelleneinstellungen zu ändern, klicken Sie mit der rechten Maustaste auf die Tabelle und wählen Sie die Option Erweiterte Tabelleneinstellungen aus dem Rechtsklickmenü oder klicken Sie auf den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Das Tabelleneigenschaften-Fenster wird geöffnet: In der Registerkarte Positionierung können Sie die folgenden Tabelleneigenschaften festlegen: Größe - mit diesen Optionen können Sie die Breite bzw. Höhe der Tabelle ändern. Wenn Sie die Funktion Seitenverhältnis beibehalten aktivieren (in diesem Fall sieht das Symbol so aus ), werden Breite und Höhe gleichmäßig geändert und das ursprüngliche Bildseitenverhältnis wird beibehalten. Position - stellen Sie die genaue Position mit den Feldern Horizontal und Vertikal sowie dem Feld Ab ein, wo Sie auf Einstellungen wie Obere linke Ecke und Zentriert zugreifen können. Auf der Registerkarte Ränder können Sie den Abstand zwischen dem Text innerhalb der Zellen und dem Zellenrand festlegen: geben Sie die erforderlichen Werte für die Zellenränder manuell ein, oder aktivieren Sie das Kontrollkästchen Standardränder nutzen, um die vordefinierten Werte zu übernehmen (falls erforderlich, können sie auch angepasst werden). Die Registerkarte Alternativer Text ermöglicht die Eingabe eines Titels und einer Beschreibung, die Personen mit Sehbehinderungen oder kognitiven Beeinträchtigungen vorgelesen werden kann, damit sie besser verstehen können, welche Informationen in der Tabelle enthalten sind. Um den eingegebenen Text in den Zellen zu formatieren, nutzen Sie die Symbole in der Registerkarte Start in der oberen Symbolleiste. Im Rechtsklickmenü stehen Ihnen zwei zusätzliche Optionen zur Verfügung: Textausrichtung in Zellen - ermöglicht Ihnen den gewünschten Typ der vertikalen Textausrichtung in den gewählten Zellen festzulegen: Oben ausrichten, Vertikal zentrieren oder Unten ausrichten. Hyperlink - einen Hyperlink in die ausgewählte Zelle einfügen."
    },
   {
        "id": "UsageInstructions/InsertText.htm", 
        "title": "Text einfügen und formatieren", 
        "body": "Text in der Präsentation einfügen Im Präsentationseditor für die Eingabe von neuem Text stehen Ihnen drei Möglichkeiten zur Auswahl: Textabschnitt in den entsprechenden Textplatzhalter auf der Folie einfügen. Platzieren Sie dazu den Cursor im Platzhalter und geben Sie Ihren Text ein oder fügen Sie diesen mithilfe der Tastenkombination STRG+V ein. Textabschnitt an beliebiger Stelle auf einer Folie einfügen. Fügen Sie ein Textfeld (rechteckiger Rahmen, in den ein Text eingegeben werden kann) oder ein TextArtfeld (Textfeld mit einer vordefinierten Schriftart und Farbe, das die Anwendung von Texteffekten ermöglicht) in die Folie ein. Abhängig vom ausgewählten Textobjekt haben Sie folgende Möglichkeiten: Um ein Textfeld hinzuzufügen, klicken Sie in der oberen Symbolleiste in den Registerkarten Startseite oder Einfügen auf das Symbol Textfeld, wählen Sie eine der folgenden Optionen: Horizontales Textfeld einfügen oder Vertikales Textfeld einfügen, und dann klicken Sie auf die Stelle, an der Sie das Textfeld einfügen möchten. Halten Sie die Maustaste gedrückt, und ziehen Sie den Rahmen des Textfelds in die gewünschte Größe. Wenn Sie die Maustaste loslassen, erscheint die Einfügemarke im hinzugefügten Textfeld und Sie können Ihren Text eingeben. Alternativ können Sie ein Textfeld einfügen, indem Sie in der oberen Symbolleiste auf Form klicken und das Symbol aus der Gruppe Standardformen auswählen. Um ein TextArt-Objekt einzufügen, klicken Sie auf das Symbol TextArt in der Registerkarte Einfügen und klicken Sie dann auf die gewünschte Stilvorlage - das TextArt-Objekt wird in der Mitte der Folie eingefügt. Markieren Sie den Standardtext innerhalb des Textfelds mit der Maus und ersetzen Sie diesen durch Ihren eigenen Text. Einen Textabschnitt in eine AutoForm einfügen. Wählen Sie eine Form aus und geben Sie Ihren Text ein. Klicken Sie in einen Bereich außerhalb des Textobjekts, um die Änderungen anzuwenden und zur Folie zurückzukehren. Der Text innerhalb des Textfelds ist Bestandteil der AutoForm (wenn Sie die AutoForm verschieben oder drehen, wird der Text mit ihr verschoben oder gedreht). Da ein eingefügtes Textobjekt von einem rechteckigen Rahmen umgeben ist (TextArt-Objekte haben standardmäßig unsichtbare Rahmen) und dieser Rahmen eine allgemeine AutoForm ist, können Sie sowohl die Form als auch die Texteigenschaften ändern. Um das hinzugefügte Textobjekt zu löschen, klicken Sie auf den Rand des Textfelds und drücken Sie die Taste ENTF auf der Tastatur. Dadurch wird auch der Text im Textfeld gelöscht. Textfeld formatieren Wählen Sie das entsprechende Textfeld durch Anklicken der Rahmenlinien aus, um die Eigenschaften zu verändern. Wenn das Textfeld markiert ist, werden alle Rahmenlinien als durchgezogene Linien (nicht gestrichelt) angezeigt. Sie können das Textfeld mithilfe der speziellen Ziehpunkte an den Ecken der Form verschieben, drehen und dessen Größe ändern. Um das Textfeld zu bearbeiten, mit einer Füllung zu versehen, Rahmenlinien zu ändern, das rechteckige Feld mit einer anderen Form zu ersetzen oder auf Formen - erweiterte Einstellungen zuzugreifen, klicken Sie in der rechten Seitenleiste auf Formeinstellungen und nutzen Sie die entsprechenden Optionen. Um ein Textfeld auf einer Folie auszurichten, zu drehen oder zu spiegeln oder Textfelder mit anderen Objekten zu verknüpfen, klicken Sie mit der rechten Maustaste auf den Feldrand und nutzen Sie die entsprechende Option im geöffneten Kontextmenü. Um Textspalten in einem Textfeld zu erzeugen, klicken Sie auf das entsprechende Symbol in der Symbolleiste für Textformatierung und wählen Sie die gewünschte Option aus, oder klicken Sie mit der rechten Maustaste auf den Feldrand, klicken Sie auf die Option Form - Erweiterte Einstellungen und wechseln Sie im Fenster Form - Erweiterte Einstellungen in die Registerkarte Spalten. Text im Textfeld formatieren Markieren Sie den Text im Textfeld, um die Eigenschaften zu verändern. Wenn der Text markiert ist, werden alle Rahmenlinien als gestrichelte Linien angezeigt. Es ist auch möglich die Textformatierung zu ändern, wenn das Textfeld (nicht der Text selbst) ausgewählt ist. In einem solchen Fall werden alle Änderungen auf den gesamten Text im Textfeld angewandt. Einige Schriftformatierungsoptionen (Schriftart, -größe, -farbe und -stile) können separat auf einen zuvor ausgewählten Teil des Textes angewendet werden. Text im Textfeld ausrichten Der Text kann auf vier Arten horizontal ausgerichtet werden: linksbündig, rechtsbündig, zentriert oder im Blocksatz. Textobjekt einfügen: Bewegen Sie den Cursor an die Stelle, an der Sie den Text ausrichten möchten (dabei kann es sich um eine neue Zeile oder um bereits eingegebenen Text handeln). Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Startseite und klicken Sie auf Horizontale Ausrichtung , um die Auswahlliste zu öffnen. Wählen Sie den gewünschten Ausrichtungstyp: Die Option Text linksbündig ausrichten lässt den linken Textrand parallel zum linken Rand des Textbereichs verlaufen (der rechte Textrand bleibt unausgerichtet). Durch die Option Text zentrieren wird der Text im Textbereich zentriert ausgerichtet (die rechte und linke Seite bleiben unausgerichtet). Die Option Text rechtsbündig ausrichten lässt den rechten Textrand parallel zum rechten Rand des Textbereichs verlaufen (der linke Textrand bleibt unausgerichtet). Die Option Im Blocksatz ausrichten lässt den Text parallel zum linken und rechten Rand des Textbereichs verlaufen (zusätzlicher Abstand wird hinzugefügt, wo es notwendig ist, um die Ausrichtung beizubehalten). Diese Parameter finden Sie auch im Fenster Absatz - Erweiterte Einstellungen. Der Text kann vertikal auf drei Arten ausgerichtet werden: oben, mittig oder unten. Textobjekt einfügen: Bewegen Sie den Cursor an die Stelle, an der Sie den Text ausrichten möchten (dabei kann es sich um eine neue Zeile oder um bereits eingegebenen Text handeln). Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Startseite und klicken Sie auf Vertikale Ausrichtung , um die Auswahlliste zu öffnen. Wählen Sie den gewünschten Ausrichtungstyp: Über die Option Text am oberen Rand ausrichten richten Sie den Text am oberen Rand des Textfelds aus. Über die Option Text mittig ausrichten richten Sie den Text im Textfeld mittig aus. Über die Option Text am unteren Rand ausrichten richten Sie den Text am unteren Rand des Textfelds aus. Textrichtung ändern Um den Text innerhalb des Textfeldes zu drehen, klicken Sie mit der rechten Maustaste auf den Text, klicken Sie auf Textausrichtung und wählen Sie eine der verfügbaren Optionen: Horizontal (Standardeinstellung), Text um 180° drehen (vertikale Ausrichtung von oben nach unten) oder Text um 270° drehen (vertikale Ausrichtung von unten nach oben). Schriftart, -größe und -farbe anpassen und DekoSchriften anwenden In der Registerkarte Start können Sie über die Symbole in der oberen Symbolleiste Schriftart, -größe und -Farbe festelgen und verschiedene DekoSchriften anwenden. Wenn Sie die Formatierung auf Text anwenden wollen, der bereits in der Präsentation vorhanden ist, wählen Sie diesen mit der Maus oder mithilfe der Tastatur aus und legen Sie die gewünschte Formatierung fest. Sie können den Mauszeiger auch innerhalb des erforderlichen Wortes platzieren, um die Formatierung nur auf dieses Wort anzuwenden. Schriftart Wird verwendet, um eine Schriftart aus der Liste mit den verfügbaren Schriftarten zu wählen. Wenn eine gewünschte Schriftart nicht in der Liste verfügbar ist, können Sie diese runterladen und in Ihrem Betriebssystem speichern. Anschließend steht Ihnen diese Schrift in der Desktop-Version zur Nutzung zur Verfügung. Schriftgröße Über diese Option kann der gewünschte Wert für die Schriftgröße aus der Dropdown-List ausgewählt werden (die Standardwerte sind: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 und 96). Sie können auch manuell einen Wert in das Feld für die Schriftgröße eingeben und anschließend die Eingabetaste drücken. Schriftgrad vergrößern Wird verwendet, um die Schriftgröße zu ändern, sodass sie bei jedem Drücken der Taste um einen Punkt größer wird. Schriftgrad verkleinern Wird verwendet, um die Schriftgröße zu ändern, sodass sie bei jedem Drücken der Taste um einen Punkt kleiner wird. Groß-/Kleinschreibung Wird verwendet, um die Groß- und Kleinschreibung zu ändern. Ersten Buchstaben im Satz großschreiben - die Buchstaben werden wie in einem Satz geschrieben. Kleinbuchstaben - alle Buchstaben sind klein. GROSSBUCHSTABEN - alle Buchstaben sind groß. Ersten Buchstaben im Wort großschreiben - jedes Wort beginnt mit einem Großbuchstaben. gROSS-/kLEINSCHREIBUNG - kehrt die Groß- und Kleinschreibung des ausgewählten Textes oder des Wortes um, an dem sich der Mauszeiger befindet. Texthervorhebungsfarbe Wird verwendet, um den Hintergrund für einzelne Sätze, Phrasen, Wörter oder sogar Zeichen durch Hinzufügen eines Farbbandes zu markieren, das den Effekt eines Textmarkers um den Text herum imitiert. Wählen Sie dazu den gewünschten Text aus und klicken Sie anschließend auf den Abwärtspfeil neben dem Symbol, um eine Farbe auf der Palette auszuwählen (diese Farbeinstellung ist unabhängig vom ausgewählten Farbschema und enthält 16 Farben) - die Farbe wird auf den ausgewählten Text angewendet. Alternativ können Sie zuerst eine Hervorhebungsfarbe auswählen und dann den Text mit der Maus auswählen - der Mauszeiger sieht in diesem Fall so aus und Sie können mehrere verschiedene Teile Ihres Textes nacheinander markieren. Um die Hervorhebung zu beenden, klicken Sie einfach erneut auf das Symbol. Um die Markierungsfarbe zu löschen, klicken Sie auf die Option Keine Füllung. Schriftfarbe Wird verwendet, um die Farbe der Buchstaben/Zeichen im Text zu ändern. Klicken Sie auf den Abwärtspfeil neben dem Symbol, um eine Farbe auszuwählen. Fett Der gewählte Textabschnitt wird durch fette Schrift hervorgehoben. Kursiv Der gewählte Textabschnitt wird durch die Schrägstellung der Zeichen hervorgehoben. Unterstrichen Der gewählten Textabschnitt wird mit einer Linie unterstrichen. Durchgestrichen Der gewählten Textabschnitt wird mit einer Linie durchgestrichen. Hochgestellt Der gewählte Textabschnitt wird verkleinert und im oberen Teil der Textzeile platziert z.B. in Bruchzahlen. Tiefgestellt Der gewählte Textabschnitt wird verkleinert und im unteren Teil der Textzeile platziert z.B. in chemischen Formeln. Bestimmen Sie den Zeilenabstand und ändern Sie die Absatzeinzüge Im Präsentationseditor können Sie die Zeilenhöhe für den Text innerhalb der Absätze sowie den Abstand zwischen dem aktuellen Absatz und dem vorherigen oder nächsten Absatz festlegen. Gehen Sie dazu vor wie folgt: Positionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus. Nutzen Sie die entsprechenden Felder der Registerkarte Texteinstellungen in der rechten Seitenleiste, um die gewünschten Ergebnisse zu erzielen: Zeilenabstand - Zeilenhöhe für die Textzeilen im Absatz festlegen. Sie können unter zwei Optionen wählen: mehrfach (mithilfe dieser Option wird ein Zeilenabstand festgelegt, der ausgehend vom einfachen Zeilenabstand vergrößert wird (Größer als 1)), genau (mithilfe dieser Option wird ein fester Zeilenabstand festgelegt). Sie können den gewünschten Wert im Feld rechts angeben. Absatzabstand - Auswählen wie groß die Absätze sind, die zwischen Textzeilen und Abständen angezeigt werden. Vor - Abstand vor dem Absatz festlegen. Nach - Abstand nach dem Absatz festlegen. Diese Parameter finden Sie auch im Fenster Absatz - Erweiterte Einstellungen. Um den aktuellen Zeilenabstand zu ändern, können Sie auch auf der Registerkarte Startseite das Symbol Zeilenabstand anklicken und den gewünschten Wert aus der Liste auswählen: 1,0; 1,15; 1,5; 2,0; 2,5; oder 3,0 Zeilen. Um den Absatzversatz von der linken Seite des Textfelds zu ändern, positionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus und klicken Sie auf das entsprechende Symbole auf der Registerkarte Startseite in der oberen Symbolleiste: Einzug verkleinern und Einzug vergrößern . Die erweiterten Absatzeinstellungen anpassen Um das Fenster Absatz – Erweiterte Einstellungen zu öffnen, klicken Sie mit der rechten Maustaste auf den Text und wählen Sie die Option Erweiterte Absatzeinstellungen aus dem Menü. Es ist auch möglich, den Cursor innerhalb des gewünschten Absatzes zu platzieren - die Registerkarte Absatzeinstellungen wird in der rechten Seitenleiste aktiviert. Klicken Sie auf den Link Erweiterte Einstellungen anzeigen. Das Absatzeinstellungen-Fenster wird geöffnet: In der Registerkarte Einzüge &amp; Position können Sie: die Ausrichtung für den Absatztext ändern, die Absatz-Einzüge in Bezug auf innere Ränder des Textfelds ändern, Links - stellen Sie den Abstand des Absatzes vom linken inneren Rand des Textfelds ein, indem Sie den erforderlichen numerischen Wert angeben, Rechts - setzen Sie den Abstand des Absatzes vom rechten inneren Rand des Textfelds, indem Sie den erforderlichen numerischen Wert angeben, Speziell - setzen Sie den Einzug für die erste Zeile des Absatzes: Wählen Sie den entsprechenden Menüpunkt ((Kein), Erste Zeile, Hängend) und ändern Sie den standardmäßigen numerischen Wert, der für Erste Zeile oder Hängend angegeben ist, den Zeilenabstand des Absatzes ändern. Sie können auch das horizontale Lineal verwenden, um Einzüge festzulegen. Wählen Sie die erforderlichen Absätze aus und ziehen Sie die Einzugsmarkierungen entlang des Lineals. Markierung für den Einzug der ersten Zeile wird verwendet, um den Abstand vom linken inneren Rand des Textfelds für die erste Zeile des Absatzes festzulegen. Markierung für hängenden Einzug wird verwendet, um den Abstand vom linken inneren Rand des Textfelds für die zweite und alle folgenden Zeilen des Absatzes festzulegen. Markierung für linken Einzug wird verwendet, um den gesamten Absatzversatz vom linken inneren Rand des Textfelds festzulegen. Markierung für rechten Einzug wird verwendet, um den Abstand des Absatzes vom rechten inneren Rand des Textfelds festzulegen. Wenn Sie die Lineale nicht sehen, wechseln Sie zur Registerkarte Startseite in der oberen Symbolleiste und klicken Sie auf Ansichts-Einstellungen in der oberen rechten Ecke und deaktivieren Sie die Option Lineale verbergen, um sie anzuzeigen. Die Registerkarte Schriftart enthält die folgenden Parameter: Durchgestrichen - durchstreichen einer Textstelle mithilfe einer Linie. Doppelt durchgestrichen - durchstreichen einer Textstelle mithilfe einer doppelten Linie. Hochgestellt - Textstellen verkleinern und hochstellen, wie beispielsweise in Brüchen. Tiefgestellt - Textstellen verkleinern und tiefstellen, wie beispielsweise in chemischen Formeln. Kapitälchen - erzeugt Großbuchstaben in Höhe von Kleinbuchstaben. Großbuchstaben - alle Buchstaben als Großbuchstaben schreiben. Zeichenabstand - Abstand zwischen den einzelnen Zeichen festlegen. Erhöhen Sie den Standardwert für den Abstand Erweitert oder verringern Sie den Standardwert für den Abstand Verkürzt. Nutzen Sie die Pfeiltasten oder geben Sie den erforderlichen Wert in das dafür vorgesehene Feld ein. Alle Änderungen werden im Feld Vorschau unten angezeigt. Die Registerkarte Tabulator ermöglicht die Änderung der Tabstopps zu ändern, d.h. die Position des Mauszeigers rückt vor, wenn Sie die Tabulatortaste auf der Tastatur drücken. Tabulatorposition - Festlegen von benutzerdefinierten Tabstopps. Geben Sie den gewünschten Wert in das angezeigte Feld ein, über die Pfeiltasten können Sie den Wert präzise anpassen, klicken Sie anschließend auf Festlegen. Ihre benutzerdefinierte Tabulatorposition wird der Liste im unteren Feld hinzugefügt. Die Standardeinstellung für Tabulatoren ist auf 1,25 cm festgelegt. Sie können den Wert verkleinern oder vergrößern, nutzen Sie dafür die Pfeiltasten oder geben Sie den gewünschten Wert in das dafür vorgesehene Feld ein. Ausrichtung - legt den gewünschten Ausrichtungstyp für jede der Tabulatorpositionen in der obigen Liste fest. Wählen Sie die gewünschte Tabulatorposition in der Liste aus, Ihnen stehen die Optionen Linksbündig, Zentriert oder Rechtsbündig zur Verfügung, klicken sie anschließend auf Festlegen. Linksbündig - der Text wird ab der Position des Tabstopps linksbündig ausgerichtet; d.h. der Text verschiebt sich bei der Eingabe nach rechts. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung angezeigt. Zentriert - der Text wird an der Tabstoppposition zentriert. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung angezeigt. Rechtsbündig - der Text wird ab der Position des Tabstopps rechtsbündig ausgerichtet; d.h. der Text verschiebt sich bei der Eingabe nach links. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung angezeigt. Um Tabstopps aus der Liste zu löschen, wählen Sie einen Tabstopp und drücken Sie Entfernen oder Alle entfernen. Alternativ können Sie die Tabstopps auch mithilfe des horizontalen Lineals festlegen. Klicken Sie zum Auswählen des gewünschten Tabstopps auf das Symbol in der oberen linken Ecke des Arbeitsbereichs, um den gewünschten Tabstopp auszuwählen: Links , Zentriert oder Rechts . Klicken Sie an der unteren Kante des Lineals auf die Position, an der Sie einen Tabstopp setzen möchten. Ziehen Sie die Markierung nach links oder rechts, um die Position zu ändern. Um den hinzugefügten Tabstopp zu entfernen, ziehen Sie die Markierung aus dem Lineal. Wenn die Lineale nicht angezeigt werden, wechseln Sie in die Registerkarte Startseite, klicken Sie in der oberen rechten Ecke auf das Symbol Ansichtseinstellungen und deaktivieren Sie die Option Lineale verbergen. TextArt-Stil bearbeiten Wählen Sie ein Textobjekt aus und klicken Sie in der rechten Seitenleiste auf das Symbol TextArt-Einstellungen . Ändern Sie den angewandten Textstil, indem Sie eine neue Vorlage aus der Galerie auswählen. Sie können den Grundstil außerdem ändern, indem Sie eine andere Schriftart, -größe usw. auswählen. Füllung und Umrandung der Schriftart ändern. Die verfügbaren Optionen sind die gleichen wie für AutoFormen. Wenden Sie einen Texteffekt an, indem Sie aus der Galerie mit den verfügbaren Vorlagen die gewünschte Formatierung auswählen. Sie können den Grad der Textverzerrung anpassen, indem Sie den rosafarbenen, rautenförmigen Ziehpunkt in die gewünschte Position ziehen."
    },
   {
        "id": "UsageInstructions/ManageSlides.htm", 
        "title": "Folien verwalten", 
        "body": "Standardmäßig besteht eine neu erstellte Präsentation aus einer leeren Titelfolie. Im Präsentationseditor sie haben nun die Möglichkeit neue Folien zu erstellen, eine Folie zu kopieren, um sie an anderer Stelle in der Folienliste einzufügen, Folien zu duplizieren, Folien mit dem Cursor in eine andere Position zu ziehen, um die Reihenfolge zu verändern, überflüssige Folien zu löschen, ausgewählte Folien auszublenden. Um eine neue Folie mit Titel und Inhalt zu erstellen: Klicken Sie in der oberen Symbolleiste in den Registerkarten Start oder Einfügen auf das Symbol Folie hinzufügen oder klicken Sie mit der rechten Maustaste auf eine beliebige Folie und wählen Sie die Option Neue Folie im Kontextmenü aus oder nutzen Sie die Tastenkombination STRG+M. Um eine neue Folie mit einem anderen Layout zu erstellen: Klicken Sie in der oberen Symbolleiste auf den Registerkarten Startseite oder Einfügen auf den Pfeil neben dem Symbol Folie hinzufügen. Wählen Sie eine Folie mit dem gewünschten Layout im Menü aus. Sie können das Layout der hinzugefügten Folie jederzeit ändern. Weitere Informationen finden Sie im Abschnitt Folienparameter festlegen. Die neue Folie wird vor der aktuell mit dem Cursor markierten Folie eingefügt. Um die Folien zu duplizieren: Wählen Sie eine Folie oder mehrere Folien in der Liste der vorhandenen Folien auf der linken Seite aus. Klicken Sie mit der rechten Maustaste und wählen Sie die Option Folie duplizieren aus dem Kontextmenü, oder gehen Sie zur Registerkarte Startseite oder Einfügen, klicken Sie auf die Folie hinzufügen und wählen Sie die Menüoption Folie duplizieren. Die duplizierte Folie wird nach der gewählten Folie in die Folienliste eingefügt. Um die Folien zu kopieren: Wählen Sie eine Folie oder mehrere Folien in der Folienliste aus. Drücken Sie die Tasten STRG+C. Wählen Sie in der Folienliste die Folie aus, hinter der die kopierte Folie eingefügt werden soll. Drücken Sie die Tasten STRG+V. Nachdem die kopierte Folie eingefügt wurde, wird neben der eingefügten Folie die Schaltfläche Spezielles Einfügen angezeigt. Klicken Sie auf diese Schaltfläche, um die erforderliche Einfügeoption auszuwählen, oder verwenden Sie die Strg-Taste in Kombination mit der Buchstabentaste, die in den Klammern neben der Option angegeben ist. Die folgenden Spezielles Einfügen-Optionen sind verfügbar: Zieldesign verwenden (Strg+H) - ermöglicht die Anwendung der durch das Thema der aktuellen Präsentation festgelegten Formatierung. Diese Option wird standardmäßig verwendet. Ursprüngliche Formatierung beibehalten (Strg+K) - ermöglicht das Beibehalten der Quellformatierung der kopierten Folie. Bild (Ctrl+U) - ermöglicht das Einfügen der Folie als Bild, sodass sie nicht bearbeitet werden kann. Um die vorhandenen Folien zu verschieben: Klicken Sie mit der linken Maustaste auf eine Folie oder mehrere Folien. Ziehen Sie die Folie nun mit dem Mauszeiger an die gewünschte Stelle, ohne die Maustaste loszulassen (eine horizontale Linie zeigt eine neue Position an). Um die Folien zu löschen: Klicken Sie in der Liste mit den vorhandenen Folien mit der rechten Maustaste auf eine Folie oder mehrere Folien, die Sie löschen möchten. Wählen Sie die Option Folie löschen im Kontextmenü. Um die Folien auszublenden: Klicken Sie in der Liste mit den vorhandenen Folien mit der rechten Maustaste auf eine Folie oder mehrere Folien, die Sie ausblenden möchten. Wählen Sie die Option Folie ausblenden im Kontextmenü. Die Foliennummer, die der entsprechenden Folie entspricht, wird durchgestrichen. Um die ausgeblendete Folie wieder normal darzustellen, klicken Sie erneut auf die Option Folie ausblenden. Nutzen Sie diese Option, wenn Sie Ihrer Zielgruppe bestimmte Folien nicht grundsätzlich vorführen wollen, aber diese bei Bedarf dennoch einbinden können. Wenn Sie die Präsentation in der Referentenansicht starten, werden alle vorhandenen Folien in der Liste angezeigt, wobei die Foliennummern der ausgeblendeten Folien durchgestrichen sind. Wenn Sie anderen eine ausgeblendete Folie zeigen möchten, klicken Sie in der Folienliste mit der Maus auf die Folie - die Folie wird angezeigt. Um alle vorhandenen Folien auszuwählen: Klicken Sie mit der rechten Maustaste auf eine beliebige Folie in der Folienliste. Wählen Sie die Option Alle wählen im Kontextmenü. Um mehrere Folien auszuwählen: Drücken Sie die Taste STRG und halten Sie sie gedrückt. Wählen Sie die gewünschten Folien in der Folienliste aus. Alle Tastenkombinationen zum navigieren und einrichten von Folien verwendet werden können, finden Sie auf der Seite Tastenkombinationen."
    },
   {
        "id": "UsageInstructions/ManipulateObjects.htm", 
        "title": "Objekte formatieren", 
        "body": "Im Präsentationseditor sie können die verschiedene Objekte auf einer Folie mithilfe der speziellen Ziehpunkte manuell verschieben, drehen und ihre Größe ändern. Alternativ können Sie über die rechte Seitenleiste oder das Fenster Erweiterte Einstellungen genaue Werte für die Abmessungen und die Position von Objekten festlegen. Hier finden Sie eine Übersicht über die gängigen Tastenkombinationen für die Arbeit mit Objekten. Größe von Objekten ändern Um die Größe von AutoFormen, Bildern, Diagrammen, Tabellen oder Textboxen zu ändern, ziehen Sie mit der Maus an den kleinen Quadraten an den Rändern des entsprechenden Objekts. Um das ursprünglichen Seitenverhältnis der ausgewählten Objekte während der Größenänderung beizubehalten, halten Sie Taste UMSCHALT gedrückt und ziehen Sie an einem der Ecksymbole. Um die präzise Breite und Höhe eines Diagramms festzulegen, wählen Sie es auf der Folie aus und navigieren Sie über den Bereich Größe, der in der rechten Seitenleiste aktiviert wird. Um die präzise Abmessungen eines Bildes oder einer AutoForm festzulegen, klicken Sie mit der rechten Maustaste auf das gewünschte Objekt und wählen Sie die Option Bild/AutoForm - Erweiterte Einstellungen aus dem Menü aus. Legen Sie benötigte Werte in die Registerkarte Größe im Fenster Erweiterte Einstellungen fest und drücken Sie auf OK. Die Form einer AutoForm ändern Bei der Änderung einiger Formen, z.B. geformte Pfeile oder Legenden, ist auch dieses gelbe diamantförmige Symbol verfügbar. Über dieses Symbol können verschiedene Komponenten einer Form geändert werden, z.B. die Länge des Pfeilkopfes. Um eine AutoForm umzuformen, können Sie auch die Option Punkte bearbeiten aus dem Kontextmenü verwenden. Die Option Punkte bearbeiten wird verwendet, um die Krümmung Ihrer Form anzupassen oder zu ändern. Um die bearbeitbaren Ankerpunkte einer Form zu aktivieren, klicken Sie mit der rechten Maustaste auf die Form und wählen Sie im Menü die Option Punkte bearbeiten aus. Die schwarzen Quadrate, die aktiv werden, sind die Punkte, an denen sich zwei Linien treffen, und die rote Linie umreißt die Form. Klicken Sie darauf und ziehen Sie, um den Punkt neu zu positionieren und den Umriss der Form zu ändern. Sobald Sie auf den Ankerpunkt klicken, werden zwei blaue Linien mit weißen Quadraten an den Enden angezeigt. Dies sind Bezier-Ziehpunkte, mit denen Sie eine Kurve erstellen und die Glätte einer Kurve ändern können. Solange die Ankerpunkte aktiv sind, können Sie sie hinzufügen und löschen: Um einer Form einen Punkt hinzuzufügen, halten Sie die Strg-Taste gedrückt und klicken Sie auf die Position, an der Sie einen Ankerpunkt hinzufügen möchten. Um einen Punkt zu löschen, halten Sie die Strg-Taste gedrückt und klicken Sie auf den unnötigen Punkt. Objekte verschieben Um die Position von AutoFormen, Bildern, Diagrammen, Tabellen und Textfeldern zu ändern, nutzen Sie das Symbol , das eingeblendet wird, wenn Sie den Mauszeiger über die AutoForm bewegen. Ziehen Sie das Objekt in die gewünschten Position, ohne die Maustaste loszulassen. Um ein Objekt in 1-Pixel-Stufen zu verschieben, halten Sie die Taste STRG gedrückt und verwenden Sie die Pfeile auf der Tastatur. Um ein Objekt strikt horizontal/vertikal zu bewegen und zu verhindern, dass es sich perpendikular bewegt, halten Sie die UMSCHALT-Taste beim Ziehen gedrückt. Um die exakte Position eines Bildes festzulegen, klicken Sie mit der rechten Maustaste auf das Bild und wählen Sie die Option Bild - Erweiterte Einstellungen aus dem Menü aus. Legen Sie gewünschten Werte im Bereich Position im Fenster Erweiterte Einstellungen fest und drücken Sie auf OK. Objekte drehen Um AutoFormen, Bilder und Textfelder manuell zu drehen, positionieren Sie den Cursor auf dem Drehpunkt und ziehen Sie das Objekt im Uhrzeigersinn oder gegen Uhrzeigersinn in die gewünschte Position. Um ein Objekt in 15-Grad-Stufen zu drehen, halten Sie die UMSCHALT-Taste bei der Drehung gedrückt. Sobald Sie das gewünschte Objekt ausgewählt haben, wird der Abschnitt Drehen in der rechten Seitenleiste aktiviert. Hier haben Sie die Möglichkeit das Objekt um 90 Grad im/gegen den Uhrzeigersinn zu drehen oder das Objekt horizontal/vertikal zu drehen. Um die Funktion zu öffnen, klicken Sie rechts auf das Symbol Formeinstellungen oder das Symbol Bildeinstellungen . Wählen Sie eine der folgenden Optionen: - die Form um 90 Grad gegen den Uhrzeigersinn drehen - die Form um 90 Grad im Uhrzeigersinn drehen - die Form horizontal spiegeln (von links nach rechts) - die Form vertikal spiegeln (von oben nach unten) Alternativ können Sie mit der rechten Maustaste auf das ausgewählte Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Drehen aus und nutzen Sie dann eine der verfügbaren Optionen zum Drehen. Um den Text in einem genau festgelegten Winkel zu drehen, klicken Sie auf das Symbol Erweiterte Einstellungen anzeigen in der rechten Seitenleiste und wählen Sie dann die Option Drehen im Fenster Erweiterte Einstellungen. Geben Sie den erforderlichen Wert in Grad in das Feld Winkel ein und klicken Sie dann auf OK."
    },
   {
        "id": "UsageInstructions/MathAutoCorrect.htm", 
        "title": "AutoKorrekturfunktionen", 
        "body": "Die Autokorrekturfunktionen im Präsentationseditor werden verwendet, um Text automatisch zu formatieren, wenn sie erkannt werden, oder um spezielle mathematische Symbole einzufügen, indem bestimmte Zeichen verwendet werden. Die verfügbaren AutoKorrekturoptionen werden im entsprechenden Dialogfeld aufgelistet. Um darauf zuzugreifen, öffnen Sie die Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von AutoKorrektur. Das Dialogfeld Autokorrektur besteht aus vier Registerkarten: Mathematische Autokorrektur, Erkannte Funktionen, AutoFormat während der Eingabe und Autokorrektur für Text. Math. AutoKorrektur Sie können manuell die Symbole, Akzente und mathematische Symbole für die Gleichungen mit der Tastatur statt der Galerie eingeben. Positionieren Sie die Einfügemarke am Platzhalter im Formel-Editor, geben Sie den mathematischen AutoKorrektur-Code ein, drücken Sie die Leertaste. Für die Codes muss die Groß-/Kleinschreibung beachtet werden. Sie können Autokorrektur-Einträge zur Autokorrektur-Liste hinzufügen, ändern, wiederherstellen und entfernen. Wechseln Sie zur Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von AutoKorrektur -> Mathematische Autokorrektur. Einträge zur Autokorrekturliste hinzufügen Geben Sie den Autokorrekturcode, den Sie verwenden möchten, in das Feld Ersetzen ein. Geben Sie das Symbol ein, das dem früher eingegebenen Code zugewiesen werden soll, in das Feld Nach ein. Klicken Sie auf die Schaltfläche Hinzufügen. Einträge in der Autokorrekturliste bearbeiten Wählen Sie den Eintrag, den Sie bearbeiten möchten. Sie können die Informationen in beiden Feldern ändern: den Code im Feld Ersetzen oder das Symbol im Feld Nach. Klicken Sie auf die Schaltfläche Ersetzen. Einträge aus der Autokorrekturliste entfernen Wählen Sie den Eintrag, den Sie entfernen möchten. Klicken Sie auf die Schaltfläche Löschen. Um die zuvor gelöschten Einträge wiederherzustellen, wählen Sie den wiederherzustellenden Eintrag aus der Liste aus und klicken Sie auf die Schaltfläche Wiederherstellen. Verwenden Sie die Schaltfläche Zurücksetzen auf die Standardeinstellungen, um die Standardeinstellungen wiederherzustellen. Alle von Ihnen hinzugefügten Autokorrektur-Einträge werden entfernt und die geänderten werden auf ihre ursprünglichen Werte zurückgesetzt. Deaktivieren Sie das Kontrollkästchen Text bei der Eingabe ersetzen, um Math. AutoKorrektur zu deaktivieren und automatische Änderungen und Ersetzungen zu verbieten. Die folgende Tabelle enthält alle derzeit unterstützten Codes, die im Präsentationseditor verfügbar sind. Die vollständige Liste der unterstützten Codes finden Sie auch auf der Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von AutoKorrektur -> Mathematische Autokorrektur. Die unterstützte Codes Code Symbol Bereich !! Symbole ... Punkte :: Operatoren := Operatoren /< Vergleichsoperatoren /> Vergleichsoperatoren /= Vergleichsoperatoren \\above Hochgestellte/Tiefgestellte Skripts \\acute Akzente \\aleph Hebräische Buchstaben \\alpha Griechische Buchstaben \\Alpha Griechische Buchstaben \\amalg Binäre Operatoren \\angle Geometrische Notation \\aoint Integrale \\approx Vergleichsoperatoren \\asmash Pfeile \\ast Binäre Operatoren \\asymp Vergleichsoperatoren \\atop Operatoren \\bar Über-/Unterstrich \\Bar Akzente \\because Vergleichsoperatoren \\begin Trennzeichen \\below Above/Below Skripts \\bet Hebräische Buchstaben \\beta Griechische Buchstaben \\Beta Griechische Buchstaben \\beth Hebräische Buchstaben \\bigcap Große Operatoren \\bigcup Große Operatoren \\bigodot Große Operatoren \\bigoplus Große Operatoren \\bigotimes Große Operatoren \\bigsqcup Große Operatoren \\biguplus Große Operatoren \\bigvee Große Operatoren \\bigwedge Große Operatoren \\binomial Gleichungen \\bot Logische Notation \\bowtie Vergleichsoperatoren \\box Symbole \\boxdot Binäre Operatoren \\boxminus Binäre Operatoren \\boxplus Binäre Operatoren \\bra Trennzeichen \\break Symbole \\breve Akzente \\bullet Binäre Operatoren \\cap Binäre Operatoren \\cbrt Wurzeln \\cases Symbole \\cdot Binäre Operatoren \\cdots Punkte \\check Akzente \\chi Griechische Buchstaben \\Chi Griechische Buchstaben \\circ Binäre Operatoren \\close Trennzeichen \\clubsuit Symbole \\coint Integrale \\cong Vergleichsoperatoren \\coprod Mathematische Operatoren \\cup Binäre Operatoren \\dalet Hebräische Buchstaben \\daleth Hebräische Buchstaben \\dashv Vergleichsoperatoren \\dd Buchstaben mit Doppelstrich \\Dd Buchstaben mit Doppelstrich \\ddddot Akzente \\dddot Akzente \\ddot Akzente \\ddots Punkte \\defeq Vergleichsoperatoren \\degc Symbole \\degf Symbole \\degree Symbole \\delta Griechische Buchstaben \\Delta Griechische Buchstaben \\Deltaeq Operatoren \\diamond Binäre Operatoren \\diamondsuit Symbole \\div Binäre Operatoren \\dot Akzente \\doteq Vergleichsoperatoren \\dots Punkte \\doublea Buchstaben mit Doppelstrich \\doubleA Buchstaben mit Doppelstrich \\doubleb Buchstaben mit Doppelstrich \\doubleB Buchstaben mit Doppelstrich \\doublec Buchstaben mit Doppelstrich \\doubleC Buchstaben mit Doppelstrich \\doubled Buchstaben mit Doppelstrich \\doubleD Buchstaben mit Doppelstrich \\doublee Buchstaben mit Doppelstrich \\doubleE Buchstaben mit Doppelstrich \\doublef Buchstaben mit Doppelstrich \\doubleF Buchstaben mit Doppelstrich \\doubleg Buchstaben mit Doppelstrich \\doubleG Buchstaben mit Doppelstrich \\doubleh Buchstaben mit Doppelstrich \\doubleH Buchstaben mit Doppelstrich \\doublei Buchstaben mit Doppelstrich \\doubleI Buchstaben mit Doppelstrich \\doublej Buchstaben mit Doppelstrich \\doubleJ Buchstaben mit Doppelstrich \\doublek Buchstaben mit Doppelstrich \\doubleK Buchstaben mit Doppelstrich \\doublel Buchstaben mit Doppelstrich \\doubleL Buchstaben mit Doppelstrich \\doublem Buchstaben mit Doppelstrich \\doubleM Buchstaben mit Doppelstrich \\doublen Buchstaben mit Doppelstrich \\doubleN Buchstaben mit Doppelstrich \\doubleo Buchstaben mit Doppelstrich \\doubleO Buchstaben mit Doppelstrich \\doublep Buchstaben mit Doppelstrich \\doubleP Buchstaben mit Doppelstrich \\doubleq Buchstaben mit Doppelstrich \\doubleQ Buchstaben mit Doppelstrich \\doubler Buchstaben mit Doppelstrich \\doubleR Buchstaben mit Doppelstrich \\doubles Buchstaben mit Doppelstrich \\doubleS Buchstaben mit Doppelstrich \\doublet Buchstaben mit Doppelstrich \\doubleT Buchstaben mit Doppelstrich \\doubleu Buchstaben mit Doppelstrich \\doubleU Buchstaben mit Doppelstrich \\doublev Buchstaben mit Doppelstrich \\doubleV Buchstaben mit Doppelstrich \\doublew Buchstaben mit Doppelstrich \\doubleW Buchstaben mit Doppelstrich \\doublex Buchstaben mit Doppelstrich \\doubleX Buchstaben mit Doppelstrich \\doubley Buchstaben mit Doppelstrich \\doubleY Buchstaben mit Doppelstrich \\doublez Buchstaben mit Doppelstrich \\doubleZ Buchstaben mit Doppelstrich \\downarrow Pfeile \\Downarrow Pfeile \\dsmash Pfeile \\ee Buchstaben mit Doppelstrich \\ell Symbole \\emptyset Notationen von Mengen \\emsp Leerzeichen \\end Trennzeichen \\ensp Leerzeichen \\epsilon Griechische Buchstaben \\Epsilon Griechische Buchstaben \\eqarray Symbole \\equiv Vergleichsoperatoren \\eta Griechische Buchstaben \\Eta Griechische Buchstaben \\exists Logische Notationen \\forall Logische Notationen \\fraktura Fraktur \\frakturA Fraktur \\frakturb Fraktur \\frakturB Fraktur \\frakturc Fraktur \\frakturC Fraktur \\frakturd Fraktur \\frakturD Fraktur \\frakture Fraktur \\frakturE Fraktur \\frakturf Fraktur \\frakturF Fraktur \\frakturg Fraktur \\frakturG Fraktur \\frakturh Fraktur \\frakturH Fraktur \\frakturi Fraktur \\frakturI Fraktur \\frakturk Fraktur \\frakturK Fraktur \\frakturl Fraktur \\frakturL Fraktur \\frakturm Fraktur \\frakturM Fraktur \\frakturn Fraktur \\frakturN Fraktur \\frakturo Fraktur \\frakturO Fraktur \\frakturp Fraktur \\frakturP Fraktur \\frakturq Fraktur \\frakturQ Fraktur \\frakturr Fraktur \\frakturR Fraktur \\frakturs Fraktur \\frakturS Fraktur \\frakturt Fraktur \\frakturT Fraktur \\frakturu Fraktur \\frakturU Fraktur \\frakturv Fraktur \\frakturV Fraktur \\frakturw Fraktur \\frakturW Fraktur \\frakturx Fraktur \\frakturX Fraktur \\fraktury Fraktur \\frakturY Fraktur \\frakturz Fraktur \\frakturZ Fraktur \\frown Vergleichsoperatoren \\funcapply Binäre Operatoren \\G Griechische Buchstaben \\gamma Griechische Buchstaben \\Gamma Griechische Buchstaben \\ge Vergleichsoperatoren \\geq Vergleichsoperatoren \\gets Pfeile \\gg Vergleichsoperatoren \\gimel Hebräische Buchstaben \\grave Akzente \\hairsp Leerzeichen \\hat Akzente \\hbar Symbole \\heartsuit Symbole \\hookleftarrow Pfeile \\hookrightarrow Pfeile \\hphantom Pfeile \\hsmash Pfeile \\hvec Akzente \\identitymatrix Matrizen \\ii Buchstaben mit Doppelstrich \\iiint Integrale \\iint Integrale \\iiiint Integrale \\Im Symbole \\imath Symbole \\in Vergleichsoperatoren \\inc Symbole \\infty Symbole \\int Integrale \\integral Integrale \\iota Griechische Buchstaben \\Iota Griechische Buchstaben \\itimes Mathematische Operatoren \\j Symbole \\jj Buchstaben mit Doppelstrich \\jmath Symbole \\kappa Griechische Buchstaben \\Kappa Griechische Buchstaben \\ket Trennzeichen \\lambda Griechische Buchstaben \\Lambda Griechische Buchstaben \\langle Trennzeichen \\lbbrack Trennzeichen \\lbrace Trennzeichen \\lbrack Trennzeichen \\lceil Trennzeichen \\ldiv Bruchteile \\ldivide Bruchteile \\ldots Punkte \\le Vergleichsoperatoren \\left Trennzeichen \\leftarrow Pfeile \\Leftarrow Pfeile \\leftharpoondown Pfeile \\leftharpoonup Pfeile \\leftrightarrow Pfeile \\Leftrightarrow Pfeile \\leq Vergleichsoperatoren \\lfloor Trennzeichen \\lhvec Akzente \\limit Grenzwerte \\ll Vergleichsoperatoren \\lmoust Trennzeichen \\Longleftarrow Pfeile \\Longleftrightarrow Pfeile \\Longrightarrow Pfeile \\lrhar Pfeile \\lvec Akzente \\mapsto Pfeile \\matrix Matrizen \\medsp Leerzeichen \\mid Vergleichsoperatoren \\middle Symbole \\models Vergleichsoperatoren \\mp Binäre Operatoren \\mu Griechische Buchstaben \\Mu Griechische Buchstaben \\nabla Symbole \\naryand Operatoren \\nbsp Leerzeichen \\ne Vergleichsoperatoren \\nearrow Pfeile \\neq Vergleichsoperatoren \\ni Vergleichsoperatoren \\norm Trennzeichen \\notcontain Vergleichsoperatoren \\notelement Vergleichsoperatoren \\notin Vergleichsoperatoren \\nu Griechische Buchstaben \\Nu Griechische Buchstaben \\nwarrow Pfeile \\o Griechische Buchstaben \\O Griechische Buchstaben \\odot Binäre Operatoren \\of Operatoren \\oiiint Integrale \\oiint Integrale \\oint Integrale \\omega Griechische Buchstaben \\Omega Griechische Buchstaben \\ominus Binäre Operatoren \\open Trennzeichen \\oplus Binäre Operatoren \\otimes Binäre Operatoren \\over Trennzeichen \\overbar Akzente \\overbrace Akzente \\overbracket Akzente \\overline Akzente \\overparen Akzente \\overshell Akzente \\parallel Geometrische Notation \\partial Symbole \\pmatrix Matrizen \\perp Geometrische Notation \\phantom Symbole \\phi Griechische Buchstaben \\Phi Griechische Buchstaben \\pi Griechische Buchstaben \\Pi Griechische Buchstaben \\pm Binäre Operatoren \\pppprime Prime-Zeichen \\ppprime Prime-Zeichen \\pprime Prime-Zeichen \\prec Vergleichsoperatoren \\preceq Vergleichsoperatoren \\prime Prime-Zeichen \\prod Mathematische Operatoren \\propto Vergleichsoperatoren \\psi Griechische Buchstaben \\Psi Griechische Buchstaben \\qdrt Wurzeln \\quadratic Wurzeln \\rangle Trennzeichen \\Rangle Trennzeichen \\ratio Vergleichsoperatoren \\rbrace Trennzeichen \\rbrack Trennzeichen \\Rbrack Trennzeichen \\rceil Trennzeichen \\rddots Punkte \\Re Symbole \\rect Symbole \\rfloor Trennzeichen \\rho Griechische Buchstaben \\Rho Griechische Buchstaben \\rhvec Akzente \\right Trennzeichen \\rightarrow Pfeile \\Rightarrow Pfeile \\rightharpoondown Pfeile \\rightharpoonup Pfeile \\rmoust Trennzeichen \\root Symbole \\scripta Skripts \\scriptA Skripts \\scriptb Skripts \\scriptB Skripts \\scriptc Skripts \\scriptC Skripts \\scriptd Skripts \\scriptD Skripts \\scripte Skripts \\scriptE Skripts \\scriptf Skripts \\scriptF Skripts \\scriptg Skripts \\scriptG Skripts \\scripth Skripts \\scriptH Skripts \\scripti Skripts \\scriptI Skripts \\scriptk Skripts \\scriptK Skripts \\scriptl Skripts \\scriptL Skripts \\scriptm Skripts \\scriptM Skripts \\scriptn Skripts \\scriptN Skripts \\scripto Skripts \\scriptO Skripts \\scriptp Skripts \\scriptP Skripts \\scriptq Skripts \\scriptQ Skripts \\scriptr Skripts \\scriptR Skripts \\scripts Skripts \\scriptS Skripts \\scriptt Skripts \\scriptT Skripts \\scriptu Skripts \\scriptU Skripts \\scriptv Skripts \\scriptV Skripts \\scriptw Skripts \\scriptW Skripts \\scriptx Skripts \\scriptX Skripts \\scripty Skripts \\scriptY Skripts \\scriptz Skripts \\scriptZ Skripts \\sdiv Bruchteile \\sdivide Bruchteile \\searrow Pfeile \\setminus Binäre Operatoren \\sigma Griechische Buchstaben \\Sigma Griechische Buchstaben \\sim Vergleichsoperatoren \\simeq Vergleichsoperatoren \\smash Pfeile \\smile Vergleichsoperatoren \\spadesuit Symbole \\sqcap Binäre Operatoren \\sqcup Binäre Operatoren \\sqrt Wurzeln \\sqsubseteq Notation von Mengen \\sqsuperseteq Notation von Mengen \\star Binäre Operatoren \\subset Notation von Mengen \\subseteq Notation von Mengen \\succ Vergleichsoperatoren \\succeq Vergleichsoperatoren \\sum Mathematische Operatoren \\superset Notation von Mengen \\superseteq Notation von Mengen \\swarrow Pfeile \\tau Griechische Buchstaben \\Tau Griechische Buchstaben \\therefore Vergleichsoperatoren \\theta Griechische Buchstaben \\Theta Griechische Buchstaben \\thicksp Leerzeichen \\thinsp Leerzeichen \\tilde Akzente \\times Binäre Operatoren \\to Pfeile \\top Logische Notationen \\tvec Pfeile \\ubar Akzente \\Ubar Akzente \\underbar Akzente \\underbrace Akzente \\underbracket Akzente \\underline Akzente \\underparen Akzente \\uparrow Pfeile \\Uparrow Pfeile \\updownarrow Pfeile \\Updownarrow Pfeile \\uplus Binäre Operatoren \\upsilon Griechische Buchstaben \\Upsilon Griechische Buchstaben \\varepsilon Griechische Buchstaben \\varphi Griechische Buchstaben \\varpi Griechische Buchstaben \\varrho Griechische Buchstaben \\varsigma Griechische Buchstaben \\vartheta Griechische Buchstaben \\vbar Trennzeichen \\vdash Vergleichsoperatoren \\vdots Punkte \\vec Akzente \\vee Binäre Operatoren \\vert Trennzeichen \\Vert Trennzeichen \\Vmatrix Matrizen \\vphantom Pfeile \\vthicksp Leerzeichen \\wedge Binäre Operatoren \\wp Symbole \\wr Binäre Operatoren \\xi Griechische Buchstaben \\Xi Griechische Buchstaben \\zeta Griechische Buchstaben \\Zeta Griechische Buchstaben \\zwnj Leerzeichen \\zwsp Leerzeichen ~= Vergleichsoperatoren -+ Binäre Operatoren +- Binäre Operatoren << Vergleichsoperatoren <= Vergleichsoperatoren -> Pfeile >= Vergleichsoperatoren >> Vergleichsoperatoren Erkannte Funktionen Auf dieser Registerkarte finden Sie die Liste der mathematischen Ausdrücke, die vom Gleichungseditor als Funktionen erkannt und daher nicht automatisch kursiv dargestellt werden. Die Liste der erkannten Funktionen finden Sie auf der Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von Autokorrektur -> Erkannte Funktionen. Um der Liste der erkannten Funktionen einen Eintrag hinzuzufügen, geben Sie die Funktion in das leere Feld ein und klicken Sie auf die Schaltfläche Hinzufügen. Um einen Eintrag aus der Liste der erkannten Funktionen zu entfernen, wählen Sie die gewünschte Funktion aus und klicken Sie auf die Schaltfläche Löschen. Um die zuvor gelöschten Einträge wiederherzustellen, wählen Sie den gewünschten Eintrag aus der Liste aus und klicken Sie auf die Schaltfläche Wiederherstellen. Verwenden Sie die Schaltfläche Zurücksetzen auf die Standardeinstellungen, um die Standardeinstellungen wiederherzustellen. Alle von Ihnen hinzugefügten Funktionen werden entfernt und die entfernten Funktionen werden wiederhergestellt. AutoKorrektur während der Eingabe Standardmäßig formatiert der Editor den Text während der Eingabe gemäß den Voreinstellungen für die automatische Formatierung: Ersetzt Anführungszeichen, konvertiert Bindestriche in Gedankenstriche, ersetzt Internet- oder Netzwerkpfade durch Hyperlinks, startet eine Aufzählungsliste oder eine nummerierte Liste, wenn eine Liste wird erkannt. Mit der Option Punkt mit doppeltem Leerzeichen hinzufügen können Sie einen Punkt hinzufügen, wenn Sie zweimal die Leertaste drücken. Aktivieren oder deaktivieren Sie sie nach Bedarf. Standardmäßig ist diese Option für Linux und Windows deaktiviert und für macOS aktiviert. Um die Voreinstellungen für die automatische Formatierung zu aktivieren oder zu deaktivieren, öffnen Sie die Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von Autokorrektur -> Text bei der Eingabe ersetzen. Autokorrektur für Text Sie können den Editor so einstellen, dass das erste Wort jedes Satzes automatisch groß geschrieben wird. Die Option ist standardmäßig aktiviert. Um diese Option zu deaktivieren, gehen Sie zur Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von Autokorrektur -> Autokorrektur für Text und deaktivieren Sie die Option Jeden Satz mit einem Großbuchstaben beginnen."
    },
   {
        "id": "UsageInstructions/MotionPath.htm", 
        "title": "Animationspfad erstellen", 
        "body": "Animationspfad ist ein Teil der Effekte der Animationsgalerie, der die Bewegung eines Objekts und den ihm folgenden Pfad bestimmt. Die Symbole in der Galerie stellen den vorgeschlagenen Pfad dar. Die Animationsgalerie ist auf der Registerkarte Animation in der oberen Symbolleiste verfügbar. Anwenden eines Effekts für Animationspfade Wechseln Sie in der oberen Symbolleiste zur Registerkarte Animation. Wählen Sie einen Text, ein Objekt oder ein Grafikelement aus, auf das Sie den Animationseffekt anwenden möchten. Wählen Sie eines der vorgefertigten Bewegungspfadmuster aus dem Abschnitt Animationspfade in der Animationsgalerie (Zeilen, Bögen usw.) oder wählen Sie die Option Benutzerdefinierter Pfad, wenn Sie einen eigenen Pfad erstellen möchten. Sie können Animationseffekte auf der aktuellen Folie in der Vorschau anzeigen. Standardmäßig werden Animationseffekte automatisch abgespielt, wenn Sie sie zu einer Folie hinzufügen, aber Sie können sie deaktivieren. Klicken Sie auf der Registerkarte Animation auf das Dropdown-Menü Vorschau und wählen Sie einen Vorschaumodus aus: Vorschau, um eine Vorschau anzuzeigen, wenn Sie auf die Schaltfläche Vorschau klicken. AutoVorschau, um automatisch eine Vorschau anzuzeigen, wenn Sie einer Folie eine Animation hinzufügen oder eine vorhandene ersetzen. Hinzufügen eines benutzerdefinierten Pfadanimationseffekts Um einen benutzerdefinierten Pfad zu zeichnen, Klicken Sie auf das Objekt, dem Sie eine benutzerdefinierte Pfadanimation geben möchten. Markieren Sie die Wegpunkte mit der linken Maustaste. Ein Klick mit der linken Maustaste zeichnet eine Linie, während Sie mit gedrückter linker Maustaste jede gewünschte Kurve zeichnen können. Der Startpunkt des Weges wird mit einem grünen Richtungspfeil markiert, der Endpunkt mit einem roten. Wenn Sie fertig sind, klicken Sie zweimal mit der linken Maustaste oder drücken Sie die Esc-Taste, um das Zeichnen Ihres Pfads zu beenden. Bearbeiten von Animationspfadpunkten Um die Animationspfadpunkte zu bearbeiten, wählen Sie das Pfadobjekt aus, klicken Sie mit der rechten Maustaste, um das Kontextmenü zu öffnen, und wählen Sie die Option Punkte bearbeiten. Ziehen Sie die schwarzen Quadrate, um die Position der Knoten der Pfadpunkte anzupassen; Ziehen Sie die weißen Quadrate, um die Richtung an den Ein- und Austrittspunkten des Knotens anzupassen. Drücken Sie Esc oder irgendwo außerhalb des Pfadobjekts, um den Bearbeitungsmodus zu verlassen. Sie können den Animationspfad skalieren, indem Sie darauf klicken und die quadratischen Punkte an den Rändern des Objekts ziehen."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Eine neue Präsentation erstellen oder eine vorhandene öffnen", 
        "body": "Im Präsentationseditor können Sie eine kürzlich bearbeitete Präsentation öffnen, die Präsentation umbenennen, eine neue Präsentation erstellen oder zur Liste der vorhandenen Präsentationen zurückkehren. Eine neue Präsentation erstellen Online-Editor Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Neu erstellen. Desktop-Editor Wählen Sie im Hauptfenster des Programms das Menü Präsentation im Abschnitt Neu erstellen der linken Seitenleiste aus - eine neue Datei wird in einer neuen Registerkarte geöffnet. Wenn Sie alle gewünschten Änderungen durchgeführt haben, klicken Sie auf das Symbol Speichern in der oberen linken Ecke oder wechseln Sie in die Registerkarte Datei und wählen Sie das Menü Speichern als aus. Wählen Sie im Fenster Dateiverwaltung den Speicherort, legen Sie den Namen fest, wählen Sie das gewünschte Format (PPTX, Presentation template (POTX), ODP, OTP, PDF or PDFA) und klicken Sie auf die Schaltfläche Speichern. Eine vorhandenes Präsentation öffnen Desktop-Editor Wählen Sie in der linken Seitenleiste im Hauptfenster des Programms den Menüpunkt Lokale Datei öffnen. Wählen Sie im Fenster Dateiverwaltung die gewünschte Präsentation aus und klicken Sie auf die Schaltfläche Öffnen. Sie können auch im Fenster Dateiverwaltung mit der rechten Maustaste auf die gewünschte Präsentation klicken, die Option Öffnen mit auswählen und die gewünschte Anwendung aus dem Menü auswählen. Wenn die Office-Dokumentdateien mit der Anwendung verknüpft sind, können Sie Präsentationen auch öffnen, indem Sie im Fenster Datei-Explorer auf den Dateinamen doppelklicken. Alle Verzeichnisse, auf die Sie mit dem Desktop-Editor zugegriffen haben, werden in der Liste Aktuelle Ordner angezeigt, um Ihnen einen schnellen Zugriff zu ermöglichen. Klicken Sie auf den gewünschten Ordner, um eine der darin gespeicherten Dateien auszuwählen. Öffnen einer kürzlich bearbeiteten Präsentation: Online-Editor Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Zuletzt benutztes Dokument öffnen. Wählen Sie die gewünschte Präsentation aus der Liste mit den zuletzt bearbeiteten Dokumenten aus. Desktop-Editor Wählen Sie in der linken Seitenleiste im Hauptfenster des Programms den Menüpunkt Aktuelle Dateien. Wählen Sie die gewünschte Präsentation aus der Liste mit den zuletzt bearbeiteten Dokumenten aus. Eine geöffnete Präsentation umbenennen: Online-Editor Klicken Sie oben auf der Seite auf den Präsentationsnamen. Geben Sie einen neuen Präsentationsnamen ein. Drücken Sie Enter, um die Änderungen zu akzeptieren. Um den Ordner in dem die Datei gespeichert ist in der Online-Version in einem neuen Browser-Tab oder in der Desktop-Version im Fenster Datei-Explorer zu öffnen, klicken Sie auf der rechten Seite des Editor-Hauptmenüs auf das Symbol Dateispeicherort öffnen. Alternativ können Sie in der oberen Menüleiste auf die Registerkarte Datei wechseln und die Option Dateispeicherort öffnen auswählen."
    },
   {
        "id": "UsageInstructions/PhotoEditor.htm", 
        "title": "Bild bearbeiten", 
        "body": "ONLYOFFICE Präsentationseditor hat einen sehr effektiven Fotoeditor, mit dem Sie das Bild mit Filtern anpassen und alle Arten von Anmerkungen einfügen können. Wählen Sie das Bild in Ihrer Präsentation aus. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Foto-Editor aus. Sie befinden sich jetzt im Bearbeitungsmodus. Unter dem Bild finden Sie die folgenden Kontrollkästchen und Schieberegler für Filter: Graustufe, Sepia, Sepia 2, Blur, Emboss, Invertieren, Schärfen; Weiß entfernen (Grenzwert, Abstand), Farbtransparenz, Helligkeit, Rauschen, Verpixelt, Farbfilter; Farbton, Multiplizieren, Mix. Links neben den Filtern finden Sie die folgenden Schaltflächen Rückgängig machen, Wiederholen und Zurücksetzen; Löschen, Alles löschen; Menge (Benutzerdefiniert, Quadrat, 3:2, 4:3, 5:4, 7:5, 16:9); Kippen (Kippen X, Kippen Y, Zurücksetzen); Drehen (30 Grad., -30 Grad.,Schieberegler für manuelle Drehung); Zeichnen (Frei, Gerade, Farbe, Schieberegler für Größe); Form (Rechteck, Kreis, Dreieck, Ausfüllen, Strich, Strichgröße); Symbol (Pfeile, Sterne, Polygon, Speicherort, Herz, Blase, Benutzerdefiniertes Symbol, Farbe); Text (Fett, Kursiv, Unterstrichen, Links, Zentriert, Rechts, Farbe, Textgröße); Schablone. Sie können alle diese Einstellungen probieren. Sie können immer Ihre Aktionen rückgängig machen. Wenn Sie bereit sind, klicken Sie auf die Schaltfläche OK. Das bearbeitete Bild ist jetzt in der Präsentation enthalten."
    },
   {
        "id": "UsageInstructions/PreviewPresentation.htm", 
        "title": "Vorschau einer Präsentation", 
        "body": "Vorschau beginnen Wenn Sie eine Präsentation herunterladen, die mit einer Drittanbieteranwendung erstellt wurde, können Sie ggf. eine Vorschau der Animationseffekte anzeigen. Bildschirmpräsentation der aktuellen Präsentation im Präsentationseditor: klicken Sie in der Registerkarte Startseite oder links in der Statusleiste auf das Symbol Bildschirmpräsentation oder wählen Sie in der Folienliste links eine bestimmte Folie aus, klicken Sie diese mit der rechten Maustaste an und wählen Sie die Option Bildschirmpräsentation starten im Kontextmenü aus. Die Bildschirmpräsentation wird ab der aktuellen Folie gestartet. Klicken Sie alternativ in der Registerkarte Startseite auf das Symbol Bildschirmpräsentation und wählen Sie eine der verfügbaren Optionen: Von Beginn an - die Bildschirmpräsentation aber der ersten Folie starten Ab aktueller Folie - die Bildschirmpräsentation beginnt bei der aktuellen Folie Referentenansicht - die Präsentation wird in der Referentenansicht gestartet, die Zielgruppe sieht die Präsentation auf einem Bildschirm im Vollbildmodus und auf dem anderen Bildschirm wird die „Sprecheransicht“ mit den Notizen angezeigt. Einstellungen anzeigen - ein Einstellungsfenster wird geöffnet, in dem sich eine Sonderoption einstellen lässt: Dauerschleife, bis zum Drücken der Taste „ESC“. Aktivieren Sie diese Option bei Bedarf und klicken Sie auf OK. Wenn Sie diese Option aktivieren, wird die Präsentation angezeigt, bis Sie die Taste Escape auf Ihrer Tastatur drücken, d.h., wenn die letzte Folie der Präsentation erreicht ist, beginnt die Bildschirmpräsentation wieder bei der ersten Folie usw. Wenn Sie diese Option deaktivieren, erscheint nach der letzten Folie ein schwarzer Bildschirm, der Sie darüber informiert, dass die Präsentation beendet ist und Sie die Vorschau verlassen können. Vorschaumodus Im Vorschaumodus stehen Ihnen folgende Steuerelemente in der unteren linken Ecke zur Verfügung: Vorherige Folie - zur vorherigen Folie zurückkehren. Bildschirmpräsentation pausieren - die Vorschau wird angehalten. Wird diese Schaltfläche angeklickt, wandelt sich der Cursor zu . Bildschirmpräsentation fortsetzen - die Vorschau wird fortgesetzt. Wird diese Schaltfläche angeklickt, wandelt sich der Cursor zu . Nächste Folie - wechseln Sie in die nächste Folie. Foliennummer - Anzeige der aktuellen Foliennummer sowie der Gesamtzahl von Folien in der Präsentation. Um im Vorschaumodus zu einer bestimmten Folie überzugehen, klicken Sie auf die angezeigte Foliennummer, geben Sie die gewünschte Foliennummer in das geöffnete Fenster ein und drücken Sie die Eingabetaste. Über die Schaltfläche Vollbildmodus können Sie in die Vollbildansicht wechseln. Über die Schaltfläche Vollbildmodus verlassen können Sie die Vollbildansicht verlassen. Über die Schaltfläche Bildschirmpräsentation beenden können Sie den Präsentationsmodus verlassen. Alternativ können Sie im Präsentationsmodus auch mit den Tastenkombinationen zwischen den Folien wechseln. Referentenansicht In der Desktop-Version kann der Referentenansichtsmodus nur aktiviert werden, wenn der zweite Monitor angeschlossen ist. Referentenansicht - die Präsentation wird auf einem Bildschirm im Vollbildmodus angezeigt und auf dem anderen Bildschirm wird die „Sprecheransicht“ mit den Notizen wiedergegeben. Die Notizen für jede Folie werden unter dem Folienvorschaubereich angezeigt. Nutzen Sie die Tasten und oder klicken Sie im linken Seitenbereich auf die entsprechende Folie in der Liste. Foliennummer von ausgeblendeten Folien sind in der Liste durchgestrichen. Wenn Sie anderen eine ausgeblendete Folie zeigen möchten, klicken Sie in der Folienliste mit der Maus auf die Folie - die Folie wird angezeigt. Die folgenden Steuerelemente stehen Ihnen unterhalb des Folienvorschaubereichs zur Verfügung: Timer - zeigt die seit Beginn der Präsentation vergangene Zeit im Format hh.mm.ss an. Bildschirmpräsentation pausieren - die Vorschau wird angehalten. Wird diese Schaltfläche angeklickt, wandelt sich der Cursor zu . Bildschirmpräsentation fortsetzen - die Vorschau wird fortgesetzt. Wird diese Schaltfläche angeklickt, wandelt sich der Cursor zu . Zurücksetzen - Timer auf Null zurücksetzen. Vorherige Folie - zur vorherigen Folie zurückkehren. Nächste Folie - wechseln Sie in die nächste Folie. Foliennummer - Anzeige der aktuellen Foliennummer sowie der Gesamtzahl von Folien in der Präsentation. Pointer - Ausgewählte Elemente während der Präsentation hervorheben. Wenn diese Option aktiviert ist, sieht der Cursor aus wie folgt: . Um auf bestimmte Objekte zu zeigen, bewegen Sie den Mauszeiger über den Vorschaubereich der Folie und bewegen Sie den Pointer über die Folie. Der Pointer wird wie folgt angezeigt: . Um diese Option zu deaktivieren, klicken Sie erneut auf . Bildschirmpräsentation beenden - Präsentationsmodus verlassen."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Präsentation speichern/drucken/herunterladen", 
        "body": "Speichern Standardmäßig speichert der Online-Präsentationseditor Ihre Datei während der Bearbeitung automatisch alle 2 Sekunden, um Datenverluste im Falle eines unerwarteten Progammabsturzes zu verhindern. Wenn Sie die Datei im Schnellmodus co-editieren, fordert der Timer 25 Mal pro Sekunde Aktualisierungen an und speichert vorgenommene Änderungen. Wenn Sie die Datei im Modus Strikt co-editieren, werden Änderungen automatisch alle 10 Minuten gespeichert. Sie können den bevorzugten Co-Modus nach Belieben auswählen oder die Funktion AutoSpeichern auf der Seite Erweiterte Einstellungen deaktivieren. Aktuelle Präsentation manuell im aktuellen Format im aktuellen Verzeichnis speichern: verwenden Sie das Symbol Speichern im linken Bereich der Kopfzeile des Editors oder drücken Sie die Tasten STRG+S oder wechseln Sie in der oberen Menüleiste in die Registerkarte Datei und wählen Sie die Option Speichern. Um Datenverluste durch ein unerwartetes Schließen des Programms zu verhindern, können Sie in der Desktop-Version die Option AutoWiederherstellen auf der Seite Erweiterte Einstellungen aktivieren. In der Desktop-Version können Sie die Präsentation unter einem anderen Namen, an einem neuen Speicherort oder in einem anderen Format speichern. Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Speichern als. Wählen Sie das gewünschte Format aus: PPTX, ODP, PDF, PDF/A, PNG, JPG. Sie können auch die Option Präsentationsvorlage (POTX oder OTP) auswählen. Herunterladen In der Online-Version können Sie die daraus resultierende Präsentation auf der Festplatte Ihres Computers speichern. Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Herunterladen als. Wählen Sie das gewünschte Format aus: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. Kopie speichern In der Online-Version können Sie die eine Kopie der Datei in Ihrem Portal speichern. Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Kopie speichern als. Wählen Sie das gewünschte Format aus: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. Wählen Sie den gewünschten Speicherort auf dem Portal aus und klicken Sie Speichern. Drucken Aktuelle Präsentation drucken: Klicken Sie auf das Symbol Drucken im linken Bereich der Kopfzeile des Editors oder nutzen Sie die Tastenkombination STRG+P oder wechseln Sie in der oberen Menüleiste in die Registerkarte Datei und wählen Sie die Option Drucken. Der Firefox-Browser ermöglicht das Drucken, ohne das Dokument zuerst als PDF-Datei herunterzuladen. Es ist auch möglich, die ausgewählten Folien mit der Option Auswahl drucken aus dem Kontextmenü sowohl im Modus Bearbeiten als auch im Modus Anzeigen drucken (klicken Sie mit der rechten Maustaste auf die ausgewählten Folien und wählen Sie die Option Auswahl drucken). In der Desktop-Version wird die Datei direkt gedruckt. In der Online-Version wird basierend auf der Präsentation eine PDF-Datei erstellt. Diese können Sie öffnen und drucken oder auf der Festplatte des Computers oder einem Wechseldatenträger speichern und später drucken. Einige Browser (z. B. Chrome und Opera) unterstützen Direktdruck."
    },
   {
        "id": "UsageInstructions/SetSlideParameters.htm", 
        "title": "Folienparameter festlegen", 
        "body": "Um Ihre Präsentation zu personalisieren im Präsentationseditor, können Sie ein Thema und ein Farbschema sowie die Foliengröße und -ausrichtung für die gesamte Präsentation auswählen, Hintergrundfarbe oder Folienlayout für jede einzelne Folie ändern und Übergänge zwischen den Folien hinzufügen. Es ist außerdem möglich Notizen zu jeder Folie einzufüllen, die hilfreich sein können, wenn Sie die Präsentation in der Referentenansicht wiedergeben. Mit Themen können Sie das Präsentationsdesign schnell ändern, insbesondere das Aussehen des Folienhintergrunds, vordefinierte Schriftarten für Titel und Texte und das Farbschema, das für die Präsentationselemente verwendet wird. Um ein Thema für die Präsentation auszuwählen, klicken Sie auf das erforderliche vordefinierte Thema aus der Themengalerie rechts in der oberen Symbolleiste auf der Registerkarte Startseite. Das ausgewählte Thema wird auf alle Folien angewendet, wenn Sie nicht zuvor bestimmte Folien zum Anwenden des Themas ausgewählt haben. Um das ausgewählte Thema für eine oder mehrere Folien zu ändern, können Sie mit der rechten Maustaste auf die ausgewählten Folien in der Liste links klicken (oder mit der rechten Maustaste auf eine Folie im Bearbeitungsbereich klicken), die Option Thema ändern auswählen aus dem Kontextmenü und wählen Sie das gewünschte Thema. Farbschema wirkt sich auf die vordefinierten Farben der Präsentationselemente (Schriften, Linien, Füllungen usw.) aus und ermöglichen Ihnen, die Farbkonsistenz während der gesamten Präsentation beizubehalten. Um ein Farbschema zu ändern, klicken Sie auf das Symbol Farbschema ändern auf der Registerkarte Startseite der oberen Symbolleiste und wählen Sie das erforderliche Schema aus der Drop-Down-Liste aus. Das ausgewählte Farbschema wird in der Liste hervorgehoben und auf alle Folien angewendet. Um die Größe aller Folien in der Präsentation zu ändern, klicken Sie auf Foliegröße wählen auf der Registerkarte Startseite der oberen Symbolleiste und wählen Sie die erforderliche Option aus der Drop-Down-Liste aus. Sie können wählen: eine der beiden Schnellzugriffs-Voreinstellungen - Standard (4:3) oder Breitbildschirm (16:9), die Option Erweiterte Einstellungen, die das Fenster Einstellungen der Foliengröße öffnet, in dem Sie eine der verfügbaren Voreinstellungen auswählen oder eine benutzerdefinierte-Größe (Breite und Höhe) festlegen können, die verfügbaren Voreinstellungen sind: Standard (4:3), Breitbild (16:9), Breitbild (16:10), Letter Paper (8.5x11 in), Ledger Blatt (11x17 in), A3 Blatt (297x420 mm), A4 Blatt (210x297 mm), B4 (ICO) Blatt (250x353 mm), B5 (ICO) Blatt (176x250 mm), 35 mm Folien, Overheadfolien, Banner, das Menü Folienausrichtung ermöglicht das Ändern des aktuell ausgewählten Ausrichtungstyps. Der Standardausrichtungstyp ist Querformat, der auf Hochformat umgestellt werden kann. Um eine Hintergrundfüllung zu ändern, wählen Sie in der Folienliste auf der linken Seite die Folien aus, auf die Sie die Füllung anwenden möchten. Oder klicken Sie auf eine beliebige Leerstelle innerhalb der aktuell bearbeiteten Folie im Folienbearbeitungsbereich, um den Fülltyp für diese separate Folie zu ändern. Wählen Sie auf der Registerkarte Folien-Einstellungen der rechten Seitenleiste die erforderliche Option aus: Farbfüllung - wählen Sie diese Option, um die Farbe festzulegen, die Sie auf die ausgwählten Folien anwenden wollen. Füllung mit Farbverlauf - wählen Sie diese Option, um die Folie in zwei Farben zu gestalten, die sanft ineinander übergehen. Bild oder Textur - wählen Sie diese Option, um ein Bild oder eine vorgegebene Textur als Folienhintergrund festzulegen. Muster - wählen Sie diese Option, um die Folie mit dem zweifarbigen Design, das aus regelmässig wiederholten Elementen besteht, zu füllen. Keine Füllung - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten. Undurchsichtigkeit - ziehen Sie den Schieberegler oder geben Sie den Prozentwert manuell ein. Der Standardwert ist 100%. Er entspricht der vollen Undurchsichtigkeit. Der Wert 0% entspricht der vollen Transparenz. Weitere Informationen zu diesen Optionen finden Sie im Abschnitt Objekte ausfüllen und Farben auswählen. Die Übergänge helfen dabei, Ihre Präsentation dynamischer zu gestalten und die Aufmerksamkeit Ihres Publikums zu erhalten. Um einen Übergang anzuwenden, wählen Sie in der Folienliste auf der linken Seite die Folien aus, auf die Sie einen Übergang anwenden möchten, wählen Sie in der Drop-Down-Liste Effekt auf der Registerkarte Folien-Einstellungen einen Übergang aus, Um die Registerkarte Folien-Einstellungen zu öffnen, klicken Sie im Folienbearbeitungsbereich auf das Symbol Folien-Einstellungen oder öffnen Sie das Rechtsklickmenü und wählen Sie Folien-Einstellungen aus dem Kontextmenü aus. passen Sie die Übergangseigenschaften an: Wählen Sie einen Übergang, Dauer und die Art und Weise, wie die Folien vorgerückt werden, klicken Sie auf die Schaltfläche Auf alle Folien anwenden, um den gleichen Übergang auf alle Folien in der Präsentation anzuwenden. Weitere Informationen über diese Optionen finden Sie im Abschnitt Übergänge anwenden. Um Folienlayout zu ändern, wählen Sie in der Folienliste auf der linken Seite die Folien aus, auf die Sie ein neues Layout anwenden möchten, klicken Sie auf das Symbol Folienlayout ändern auf der Registerkarte Startseite der obere Symbolleiste, wählen Sie das gewünschte Layout aus dem Menü aus. Sie können auch mit der rechten Maustaste auf die gewünschte Folie in der Liste links klicken, die Option Layout ändern im Kontextmenü auswählen und das gewünschte Layout bestimmen. Derzeit sind die folgenden Layouts verfügbar: Title Slide, Title and Content, Section Header, Two Content, Comparison, Title Only, Blank, Content with Caption, Picture with Caption, Title and Vertical Text, Vertical Title and Text. Um einem Folienlayout Objekte hinzuzufügen, Klicken Sie auf das Symbol Folienlayout ändern und wählen Sie ein Layout aus, zu dem Sie ein Objekt hinzufügen möchten, verwenden Sie die Registerkarte Einfügen der oberen Symbolleiste, fügen Sie das erforderliche Objekt zur Folie hinzu (Bild, Tabelle, Diagramm , Form), dann mit der rechten Maustaste auf dieses Objekt klicken und die Option Zum Layout hinzufügen auswählen, klicken Sie auf der Registerkarte Startseite auf Folienlayout ändern und wenden Sie die geänderte Anordnung an. Die ausgewählten Objekte werden dem Layout des aktuellen Themas hinzugefügt. Objekte, die auf diese Weise auf einer Folie platziert wurden, können nicht ausgewählt, in der Größe geändert oder verschoben werden. Um das Folienlayout in den ursprünglichen Zustand zurückzusetzen, wählen Sie in der Folienliste auf der linken Seite die Folien aus, die Sie in den Standardzustand zurücksetzen möchten. Halten Sie die Strg-Taste gedrückt und wählen Sie jeweils eine Folie aus, um mehrere Folien gleichzeitig auszuwählen, oder halten Sie die Umschalttaste gedrückt, um alle Folien von der aktuellen bis zur ausgewählten auszuwählen. klicken Sie mit der rechten Maustaste auf eine der Folien und wählen Sie im Kontextmenü die Option Folie zurücksetzen, Alle auf Folien befindlichen Textrahmen und Objekte werden zurückgesetzt und entsprechend dem Folienlayout angeordnet. Um einer Folie Notizen hinzuzufügen, wählen Sie in der Folienliste auf der linken Seite die Folie aus, zu der Sie eine Notiz hinzufügen möchten, klicken Sie unterhalb des Folienbearbeitungsbereichs auf die Beschriftung Klicken Sie, um Notizen hinzuzufügen, geben Sie den Text Ihrer Notiz ein. Sie können den Text mithilfe der Symbole auf der Registerkarte Startseite der oberen Symbolleiste formatieren. Wenn Sie die Präsentation in der Referentenansicht starten, werden Ihnen alle vorhandenen Notizen unterhalb der Vorschauansicht angezeigt."
    },
   {
        "id": "UsageInstructions/SupportSmartArt.htm", 
        "title": "Unterstützung von SmartArt im ONLYOFFICE-Präsentationseditor", 
        "body": "SmartArt-Grafiken werden verwendet, um eine visuelle Darstellung einer hierarchischen Struktur zu erstellen, indem ein Layout ausgewählt wird, das am besten passt. ONLYOFFICE Präsentationseditor unterstützt SmartArt-Grafiken, die mit Editoren von Drittanbietern eingefügt wurden. Sie können eine Datei öffnen, die SmartArt enthält, und sie mit den verfügbaren Bearbeitungswerkzeugen als Grafikobjekt bearbeiten. Sobald Sie auf den SmartArt-Grafikrahmen oder den Rahmen seines Elements klicken, werden die folgenden Registerkarten in der rechten Seitenleiste aktiv, um ein Layout anzupassen: Folien-Einstellungen zum Ändern der Hintergrundfüllung und Undurchsichtigkeit der Folie und zum Ein- oder Ausblenden von Foliennummer, Datum und Uhrzeit. Lesen Sie Folienparameter festlegen und Fußzeilen einfügen für Details. Form-Einstellungen zum Konfigurieren der in einem Layout verwendeten Formen. Sie können Formen ändern, die Füllung, die Striche, die Größe, den Umbruchstil, die Position, die Stärke und Pfeile, das Textfeld und den alternativen Text bearbeiten. Absatzeinstellungen zum Konfigurieren von Einzügen und Abständen, Zeilen- und Seitenumbrüchen, Rahmen und Füllungen, Schriftarten, Tabulatoren und Auffüllungen. Sehen Sie den Abschnitt Absatzformatierung für eine detaillierte Beschreibung jeder Option. Diese Registerkarte wird nur für SmartArt-Elemente aktiv. TextArt-Einstellungen, um den Textartstil zu konfigurieren, der in einer SmartArt-Grafik verwendet wird, um den Text hervorzuheben. Sie können die TextArt-Vorlage, den Fülltyp, die Farbe und die Undirchsichtigkeit, die Strichgröße, die -Farbe und den -Typ ändern. Diese Registerkarte wird nur für SmartArt-Elemente aktiv. Klicken Sie mit der rechten Maustaste auf die SmartArt-Grafik oder ihren Elementrahmen, um auf die folgenden Formatierungsoptionen zuzugreifen: Anordnen, um die Objekte mit den folgenden Optionen anzuordnen: In den Vordergrund bringen, In den Hintergrund, Eine Ebene nach vorne, Eine Ebene nach hinten, Gruppieren und Gruppierung aufheben. Die Anordnungsmöglichkeiten hängen davon ab, ob die SmartArt-Elemente gruppiert sind oder nicht. Ausrichtung, um die Grafik oder die Objekte mit den folgenden Optionen auszurichten: Links ausrichten, Zentriert ausrichten, Rechts ausrichten, Oben ausrichten, Mittig ausrichten, Unten ausrichten, Horizontal verteilen und Vertikal verteilen. Drehen, um die Drehrichtung für das ausgewählte Element auf einer SmartArt-Grafik auszuwählen: 90° im UZS drehen, Um 90° gegen den Uhrzeigersinn drehen, Horizontal kippen, Vertikal kippen. Diese Option wird nur für SmartArt-Elemente aktiv. Erweiterte Einstellungen der Form, um auf zusätzliche Formformatierungsoptionen zuzugreifen. Kommentar hinzufügen, um einen Kommentar zu einer bestimmten SmartArt-Grafik oder ihrem Element zu hinterlassen. Zum Layout hinzufügen, um die SmartArt-Grafik zum Folienlayout hinzuzufügen. Klicken Sie mit der rechten Maustaste auf ein SmartArt-Grafikelement, um auf die folgenden Textformatierungsoptionen zuzugreifen: Vertikale Ausrichtung, um die Textausrichtung innerhalb des ausgewählten SmartArt-Elements zu wählen: Oben ausrichten, Mittig ausrichten, Unten ausrichten. Textausrichtung, um die Textausrichtung innerhalb des ausgewählten SmartArt-Elements auszuwählen: Horizontal, Text nach unten drehen, Text nach oben drehen. Erweiterte Text-Einstellungen, um auf zusätzliche Absatzformatierungsoptionen zuzugreifen. Kommentar hinzufügen, um einen Kommentar zu einer bestimmten SmartArt-Grafik oder ihrem Element zu hinterlassen. Hyperlink, um einen Hyperlink zum SmartArt-Element hinzuzufügen."
    },
   {
        "id": "UsageInstructions/Thesaurus.htm", 
        "title": "Wort durch Synonym ersetzen", 
        "body": "Wenn Sie dasselbe Wort mehrmals verwenden oder ein Wort nicht ganz das richtige Wort ist, können Sie im ONLYOFFICE Präsentationseditor nach Synonymen suchen. Die Antonyme werden auch angezeigt. Wählen Sie das Wort in Ihrer Präsentation aus. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Thesaurus aus. Die Synonyme und Antonyme werden in der linken Seitenleiste angezeigt. Klicken Sie auf ein Wort, um das Wort in Ihrer Präsentation zu ersetzen."
    },
   {
        "id": "UsageInstructions/Translator.htm", 
        "title": "Text übersetzen", 
        "body": "Sie können Ihre Präsentation im Präsentationseditor in zahlreiche Sprachen übersetzen. Wählen Sie den Text aus, den Sie übersetzen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Übersetzer aus. Der Übersetzer wird in einer Seitenleiste links angezeigt. Klicken Sie auf das Drop-Down-Menü und wählen Sie die bevorzugte Sprache aus. Der Text wird in die gewünschte Sprache übersetzt. Die Sprache des Ergebnisses ändern: Klicken Sie auf das Drop-Down-Menü und wählen Sie die bevorzugte Sprache aus. Die Übersetzung wird sofort geändert."
    },
   {
        "id": "UsageInstructions/ViewPresentationInfo.htm", 
        "title": "Präsentationseigenschaften anzeigen", 
        "body": "Um detaillierte Informationen über die aktuelle Präsentation im Präsentationseditor einzusehen, wechseln Sie in die Registerkarte Datei und wählen Sie die Option Information zur Präsentation. Allgemeine Eigenschaften Die Präsentationsinformationen enthalten eine Reihe von Dateieigenschaften, die die Präsentation beschreiben. Einige dieser Eigenschaften werden automatisch aktualisiert, andere können bearbeitet werden. Speicherort - den Ordner im Modul Dokumente, in dem die Datei gespeichert ist. Besitzer – der Name des Benutzers, der die Datei erstellt hat. Hochgeladen – das Datum und die Uhrzeit, wann die Datei erstellt wurde. Diese Eigenschaften sind nur in der Online-Version verfügbar. Titel, Thema, Kommentar - mit diesen Eigenschaften können Sie die Klassifizierung Ihrer Präsentationen vereinfachen. Sie können den erforderlichen Text in den Eigenschaftsfeldern angeben. Zuletzt geändert - Datum und Uhrzeit der letzten Änderung der Datei. Zuletzt geändert von - der Name des Benutzers, der die letzte Änderung an der Präsentation vorgenommen hat, wenn die Präsentation freigegeben wurde und von mehreren Benutzern bearbeitet werden kann. Anwendung - die Anwendung, mit der die Präsentation erstellt wurde. Autor - die Person, die die Datei erstellt hat. In diesem Feld können Sie den erforderlichen Namen eingeben. Drücken Sie die Eingabetaste, um ein neues Feld hinzuzufügen, in dem Sie einen weiteren Autor angeben können. Wenn Sie die Dateieigenschaften geändert haben, klicken Sie auf die Schaltfläche Anwenden, um die Änderungen zu übernehmen. Mit Online-Editoren können Sie den Präsentationstitel direkt über die Editor-Oberfläche ändern. Klicken Sie dazu auf die Registerkarte Datei in der oberen Symbolleiste und wählen Sie die Option Umbenennen. Geben Sie dann den erforderlichen Dateinamen in ein neues Fenster, das sich öffnet, und klicken Sie auf OK. Zugriffsrechte In der Online-Version können Sie die Informationen zu Berechtigungen für die in der Cloud gespeicherten Dateien einsehen. Diese Option steht im schreibgeschützten Modus nicht zur Verfügung. Um einzusehen, wer zur Ansicht oder Bearbeitung der Präsentation berechtigt ist, wählen Sie die Option Zugriffsrechte in der linken Seitenleiste. Sie können die aktuell ausgewählten Zugriffsrechte auch ändern, klicken Sie dazu im Abschnitt Personen mit Berechtigungen auf die Schaltfläche Zugriffsrechte ändern. Versionsverlauf In der Online-Version können Sie den Versionsverlauf für die in der Cloud gespeicherten Dateien einsehen. Diese Option steht im schreibgeschützten Modus nicht zur Verfügung. Um alle Änderungen an dieser Präsentation anzuzeigen, wählen Sie die Option Versionsverlauf in der linken Seitenleiste. Sie können den Versionsverlauf auch über das Symbol Versionsverlauf in der Registerkarte Zusammenarbeit öffnen. Sie sehen eine Liste mit allen Versionen der Präsentation (Hauptänderungen) und Revisionen (geringfügige Änderungen) unter Angabe aller jeweiligen Autoren sowie Erstellungsdatum und -zeit. Für Versionen der Präsentation wird auch die Versionsnummer angegeben (z.B. Ver. 2). Für eine detaillierte Anzeige der jeweiligen Änderungen in jeder einzelnen Version/Revision können Sie die gewünschte Version anzeigen, indem Sie in der linken Seitenleiste darauf klicken. Die vom Autor der Version/Revision vorgenommenen Änderungen sind mit der Farbe markiert, die neben dem Autorennamen in der linken Seitenleiste angezeigt wird. Über den unterhalb der gewählten Version/Revision angezeigten Link Wiederherstellen gelangen Sie in die jeweilige Version. Um zur aktuellen Version der Präsentation zurückzukehren, klicken Sie oben in der Liste mit Versionen auf Verlauf schließen. Um das Fenster Datei zu schließen und in den Bearbeitungsmodus zurückzukehren, klicken sie auf Menü schließen."
    },
   {
        "id": "UsageInstructions/YouTube.htm", 
        "title": "Video einfügen", 
        "body": "Sie können ein Video im Präsentationseditor in Ihre Präsentation einfügen. Es wird als Bild angezeigt. Durch Doppelklick auf das Bild wird der Videodialog geöffnet. Hier können Sie das Video starten. Kopieren Sie die URL des Videos, das Sie einbetten möchten. (die vollständige Adresse wird in der Adresszeile Ihres Browsers angezeigt) Gehen Sie zu Ihrer Präsentation und platzieren Sie den Cursor an der Stelle, an der Sie das Video einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt YouTube aus. Fügen Sie die URL ein und klicken Sie auf OK. Überprüfen Sie, ob Sie das richtige Video eingefügt haben, und klicken Sie auf die Schaltfläche OK unter dem Video. Das Video ist jetzt in Ihrer Präsentation eingefügt."
    }
]