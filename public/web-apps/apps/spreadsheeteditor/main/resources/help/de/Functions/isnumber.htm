﻿<!DOCTYPE html>
<html>
	<head>
		<title>ISTZAHL-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ISTZAHL-Funktion</h1>
			<p>Die Funktion <b>ISTZAHL</b> gehört zur Gruppe der Informationsfunktionen. Mit der Funktion wird der ausgewählte Bereich auf Zahlen überprüft. Ist eine Zahl vorhanden gibt die Funktion den Wert WAHR wieder, ansonsten FALSCH.</p>
			<p>Die Formelsyntax der Funktion <b>ISTZAHL</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ISTZAHL(Wert)</em></b></p> 
			<p><b><em>Wert</em></b> gibt den Wert wieder der geprüft werden soll.</p>
			<p>Anwendung der Funktion <b>ISTZAHL</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ISTZAHL</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ISTZAHL-Funktion" src="../images/isnumber.png" /></p>
		</div>
	</body>
</html>