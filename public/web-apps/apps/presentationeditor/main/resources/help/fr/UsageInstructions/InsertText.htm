﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insérer et mettre en forme du texte</title>
		<meta charset="utf-8" />
		<meta name="description" content="L'essentiel du texte dans les présentations PowerPoint : ajouter et mettre en forme, modifier la direction et configurer les paramètres avancés de paragraphe." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insérer et mettre en forme du texte</h1>
			<h2>Insérer une zone de texte dans votre présentation</h2>
			<p>Dans <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)">Presentation Editor</a>, vous pouvez ajouter un nouveau texte de trois manières différentes:</p>
			    <ul>
			        <li>Ajoutez un passage de texte dans l'espace réservé de texte correspondant inclus dans la présentation de diapositive. Pour ce faire, placez simplement le curseur dans l'espace réservé et tapez votre texte ou collez-le en utilisant la combinaison de touches <b>Ctrl+V</b> à la place du texte par défaut correspondant.</li>
			        <li>Ajoutez un passage de texte n'importe où sur une diapositive. Vous pouvez insérer une zone de texte (un cadre rectangulaire qui permet de saisir du texte) ou un objet Text Art (une zone de texte avec un style de police et une couleur prédéfinis permettant d'appliquer certains effets de texte). Selon le type d'objet textuel voulu, vous pouvez effectuer les opérations suivantes:
			                    <ul>
                                    <li>
                                        pour ajouter une zone de texte, cliquez sur  <div class = "icon icon-inserttexticon"></div> l'icône <b>Zone de texte</b> sous l'onglet <b>Accueil</b> ou <b>Insertion</b> de la barre d'outils supérieure, sélectionnez l'une des options suivantes: <b>Insérer une zone de texte horizontale</b> ou <b>Insérer une zone de texte verticale</b>, ensuite cliquez sur l'emplacement où vous souhaitez insérer la zone de texte, maintenez le bouton de la souris enfoncé et faites glisser la bordure pour définir sa taille. Lorsque vous relâchez le bouton de la souris, le point d'insertion apparaîtra dans la zone de texte ajoutée, vous permettant d'entrer votre texte.
                                        <p class="note">Il est également possible d'insérer une zone de texte en cliquant sur l'icône <b>Forme</b> <span class="icon icon-insertautoshape"></span> dans la barre d'outils supérieure et en sélectionnant la forme <span class="icon icon-text_autoshape"></span> du groupe <b>Formes de base</b>.</p>
                                    </li>
                                    <li>pour ajouter un objet Text Art, cliquez sur <div class = "icon icon-inserttextarticon"></div> l'icône <b>Text Art</b> sous l'onglet <b>Insertion</b> dans la barre d'outils supérieure, ensuite cliquez sur le modèle de style souhaité - l'objet Text Art sera ajouté au centre de la diapositive. Sélectionnez le texte par défaut dans la zone de texte avec la souris et remplacez-le par votre propre texte.</li>
                                </ul>
			        </li>
                    <li>Ajouter un passage de texte dans une forme automatique. Sélectionnez une forme et commencez à taper votre texte.</li>
			    </ul>
            <p>Cliquez en dehors de l'objet texte pour appliquer les modifications et revenir à la diapositive.</p>
            <p>Le texte dans l'objet textuel fait partie de celui ci (ainsi si vous déplacez ou faites pivoter l'objet textuel, le texte change de position lui aussi).</p>
            <p>Comme un objet texte inséré représente un cadre rectangulaire (avec des bordures de zone de texte invisibles par défaut) avec du texte à l'intérieur et que ce cadre est une forme automatique commune, vous pouvez modifier aussi bien les propriétés de forme que de texte.</p>
            <p>Pour supprimer l'objet textuel ajouté, cliquez sur la bordure de la zone de texte et appuyez sur la touche <b>Suppr</b> du clavier. Le texte dans la zone de texte sera également supprimé.</p>
            <h2>Mettre en forme une zone de texte</h2>
            <p>Sélectionnez la zone de texte en cliquant sur sa bordure pour pouvoir modifier ses propriétés. Lorsque la zone de texte est sélectionnée, ses bordures sont affichées en tant que lignes pleines (non pointillées).</p>
            <p><img alt="Zone de texte sélectionnée" src="../images/textbox_boxselected.png" /></p>
			<ul>
                <li>Pour <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">redimensionner, déplacer, faire pivoter</a> la zone de texte, utilisez les poignées spéciales sur les bords de la forme.</li>
                <li>pour modifier <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">le remplissage</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">le contour</a> ou <b>remplacer</b> la boîte rectangulaire par une forme différente, cliquez sur l'icône <a href="InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">Paramètres avancés de forme</a>, cliquez sur l'icône <b>Paramètres de forme</b> <div class = "icon icon-shape_settings_icon"></div> dans la barre latérale sur la droite et utilisez les options correspondantes.</li>
                <li>pour <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">aligner une zone de texte sur la diapositive, la faire pivoter ou la retourner, organiser des zones de texte</a> par rapport à d'autres objets, cliquez avec le bouton droit sur la bordure de la zone de texte et utilisez les options de menu contextuel.</li>
				<li>pour créer des <b>colonnes de texte</b> dans la zone de texte, cliquez sur l'icône appropriée <div class = "icon icon-insert_columns"></div> de la barre de mise en forme du texte et choisissez l'option appropriée, ou cliquez avec le bouton droit sur la bordure de la zone de texte, cliquez sur <b>Paramètres avancés de forme</b> et passez à l'onglet <a href="../UsageInstructions/InsertAutoshapes.htm#columns" onclick="onhyperlinkclick(this)"><b>Colonnes</b></a> dans la fenêtre <b>Forme - Paramètres avancés</b>.</li>
            </ul>
            <h2 id="formattext">Mettre en forme le texte dans la zone de texte</h2>
            <p>Cliquez sur le texte dans la zone de texte pour pouvoir modifier ses propriétés. Lorsque le texte est sélectionné, les bordures de la zone de texte sont affichées en lignes pointillées.</p>
            <p><img alt="Texte sélectionné" src="../images/textbox_textselected.png" /></p>
            <p class="note"><b>Remarque</b>: il est également possible de modifier le formatage du texte lorsque la zone de texte (et non le texte lui-même) est sélectionnée. Dans ce cas, toutes les modifications seront appliquées à tout le texte dans la zone de texte. Certaines options de mise en forme de police (type de police, taille, couleur et styles de décoration) peuvent être appliquées séparément à une partie du texte précédemment sélectionnée.</p>
            <p><b>Aligner le texte dans la zone de texte</b></p>
			<p>Le texte peut être aligné horizontalement de quatre façons : aligné à gauche, centré, aligné à droite et justifié. Pour ce faire:</p>
			<ol>
				<li>placez le curseur à la position où vous voulez appliquer l'alignement (une nouvelle ligne ou le texte déjà saisi),</li>
				<li>faites dérouler la liste <b>Alignement horizontal</b> <div class = "icon icon-horizontalalign"></div>sous l'onglet <b>Accueil</b> de la barre d'outils supérieure,</li>
				<li>sélectionnez le type d'alignement que vous allez appliquer:
					<ul>
						<li>l'option <b>Aligner le texte à gauche</b> <div class = "icon icon-alignleft"></div> permet d'aligner votre texte sur le côté gauche de la zone de texte (le côté droit reste non aligné).</li>
						<li>l'option <b>Aligner le texte au centre</b> <div class = "icon icon-aligncenter"></div> permet d'aligner votre texte au centre de la zone de texte (les côtés droit et gauche ne sont pas alignés).</li>
						<li>l'option <b>Aligner le texte à droite</b> <div class = "icon icon-alignright"></div> permet d'aligner votre texte sur le côté droit de la zone de texte (le côté gauche reste non aligné).</li>
						<li>l'option <b>Justifier</b> <div class = "icon icon-justify"></div> permet d'aligner votre texte par les côtés gauche et droit de la zone de texte (l'espacement supplémentaire est ajouté si nécessaire pour garder l'alignement).</li>
					</ul>
				</li>
			</ol>
            <p class="note"><b>Remarque</b>: on peut configurer les mêmes paramètres dans la fenêtre <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Paragraphe - Paramètres avancés</b></a> .</p>
			<p>Le texte peut être aligné verticalement de trois façons: haut, milieu ou bas. Pour ce faire:</p>
			<ol>
				<li>placez le curseur à la position où vous voulez appliquer l'alignement (une nouvelle ligne ou le texte déjà saisi),</li>
				<li>faites dérouler la liste <b>Alignement vertical</b> <div class = "icon icon-verticalalign"></div>sous l'onglet <b>Accueil</b> de la barre d'outils supérieure,</li>
				<li>sélectionnez le type d'alignement que vous allez appliquer:
					<ul>
						<li>l'option <b>Aligner le texte en haut</b> <div class = "icon icon-aligntop"></div> permet d'aligner votre texte en haut de la zone de texte.</li>
						<li>l'option <b>Aligner le texte au milieu</b> <div class = "icon icon-alignmiddle"></div> permet d'aligner votre texte au centre de la zone de texte.</li>
						<li>l'option <b>Aligner le texte en bas</b> <div class = "icon icon-alignbottom"></div> permet d'aligner votre texte en bas de la zone de texte.</li>
					</ul>
				</li>
			</ol>
            <hr />
            <p><b>Changer la direction du texte</b></p>
            <p>Pour <b>Faire pivoter</b> le texte dans la zone de texte, cliquez avec le bouton droit sur le texte, sélectionnez l'option <b>Direction du texte</b>, puis choisissez l'une des options disponibles: <b>Horizontal</b> (sélectionné par défaut), <b>Rotation du texte vers le bas</b> (définit une direction verticale, de haut en bas) ou <b>Rotation du texte vers le haut</b> (définit une direction verticale, de bas en haut).</p>
			<hr />
			<p id="formatfont"><b>Ajuster le type de police, la taille, la couleur et appliquer les styles de décoration</b></p>
			<p>Vous pouvez sélectionner le type, la taille et la couleur de police et appliquer l'un des styles de décoration en utilisant les icônes correspondantes situées dans l'onglet <b>Accueil</b> de la barre d'outils supérieure.</p>
			<p class="note"><b>Remarque</b>: si vous voulez appliquer la mise en forme au texte déjà saisi, sélectionnez-le avec la souris ou <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">en utilisant le clavier</a>et appliquez la mise en forme. Vous pouvez aussi positionner le curseur de la souris sur le mot à mettre en forme.</p>
			<table>
				<tr>
					<td width="10%">Police</td>
					<td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Sert à sélectionner l'une des polices disponibles dans la liste. <span class="desktopDocumentFeatures">Si une police requise n'est pas disponible dans la liste, vous pouvez la télécharger et l'installer sur votre système d'exploitation, après quoi la police sera disponible pour utilisation dans la <em>version de bureau</em>.</span></td>
				</tr>
				<tr>
					<td>Taille de la police</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Sert à sélectionner la taille de la police parmi les valeurs disponibles dans la liste déroulante, les valeurs par défaut sont: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 et 96).  Il est également possible d'entrer manuellement une valeur personnalisée dans le champ de taille de police jusqu'à 300 pt. Appuyer sur la touche Entrée pour confirmer</td>
				</tr>
				<tr>
					<td>Augmenter la taille de la police</td>
					<td><div class = "icon icon-larger"></div></td>
					<td>Sert à modifier la taille de la police en la rendant plus grande à un point chaque fois que vous appuyez sur le bouton.</td>
				</tr>
				<tr>
					<td>Diminuer la taille de la police</td>
					<td><div class = "icon icon-smaller"></div></td>
					<td>Sert à modifier la taille de la police en la rendant plus petite à un point chaque fois que vous appuyez sur le bouton.</td>
				</tr>
				<tr>
					<td>Modifier la casse</td>
					<td><div class = "icon icon-change_case"></div></td>
					<td>Sert à modifier la casse du texte. <em>Majuscule en début de phrase</em> - la casse à correspondre la casse de la proposition ordinaire. minuscule - mettre en minuscule toutes les lettres <em>MAJUSCULES</em> - mettre en majuscule toutes les lettres <em>Mettre en majuscule chaque mot</em> - mettre en majuscule la première lettre de chaque mot <em>Inverser la casse</em> - basculer entre d'affichages de la casse du texte.</td>
				</tr>
				<tr>
					<td>Couleur de surlignage</td>
					<td><div class = "icon icon-highlightcolor"></div></td>
					<td>Est utilisé pour marquer des phrases, des fragments, des mots ou même des caractères séparés en ajoutant une bande de couleur qui imite l'effet du surligneur sur le texte. Vous pouvez sélectionner la partie voulue du texte, puis cliquer sur la flèche vers le bas à côté de l'icône pour sélectionner une couleur dans la palette (cet ensemble de couleurs ne dépend pas du <b>Jeux de couleurs</b> sélectionné et comprend 16 couleurs).
La couleur sera appliquée à la sélection. Alternativement, vous pouvez tout d'abord choisir une couleur de surbrillance et ensuite commencer à sélectionner le texte avec la souris - le pointeur de la souris ressemblera à ceci <div class = "icon icon-highlight_color_mouse_pointer"></div> et vous serez en mesure de surligner plusieurs parties différentes de votre texte de manière séquentielle. Pour enlever la mise en surbrillance, cliquez à nouveau sur l'icône. Pour effacer la couleur de surbrillance, choisissez l'option <b>Pas de remplissage</b>.</td>
				</tr>
				<tr>
					<td>Couleur de police</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Sert à changer la couleur des lettres /caractères dans le texte. Cliquez sur la flèche vers le bas à côté de l'icône pour <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">sélectionner la couleur</a>.</td>
				</tr>
				<tr>
					<td>Gras</td>
					<td><div class = "icon icon-bold"></div></td>
					<td>Sert à mettre la police en gras pour lui donner plus de poids.</td>
				</tr>
				<tr>
					<td>Italique</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Sert à mettre la police en italique pour lui donner une certaine inclinaison à droite.</td>
				</tr>
				<tr>
					<td>Souligné</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Sert à souligner le texte avec la ligne qui passe sous les lettres.</td>
				</tr>
				<tr>
					<td>Barré</td>
					<td><div class = "icon icon-strike"></div></td>
					<td>Sert à barrer le texte par la ligne passant par les lettres.</td>
				</tr>
				<tr>
					<td>Exposant</td>
					<td><div class = "icon icon-sup"></div></td>
					<td>Sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions.</td>
				</tr>
				<tr>
					<td>Indice</td>
					<td><div class = "icon icon-sub"></div></td>
					<td>Sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques.</td>
				</tr>
			</table>
			<p><b>Définir l'interligne et modifier les retraits de paragraphe</b></p>
			<p>Vous pouvez définir l'interligne pour les lignes de texte dans le paragraphe ainsi que les marges entre le paragraphe courant et le précédent ou le suivant.</p>
			<p><img class="floatleft" alt="Onglet Paramètres de paragraphe" src="../images/textsettingstab.png" /></p>
			<p>Pour ce faire,</p>
			<ol style="margin-left: 280px;">
				<li>placez le curseur dans le paragraphe de votre choix ou sélectionnez plusieurs paragraphes avec la souris,</li>
				<li>utilisez les champs correspondants de l'onglet <div class = "icon icon-text_settings_icon"></div> <b>Paramètres de texte</b> dans la barre latérale sur la droite pour obtenir les résultats nécessaires:
					<ul>
						<li><b>Interligne</b> - réglez la hauteur de la ligne pour les lignes de texte dans le paragraphe. Vous pouvez choisir parmi deux options: <b>multiple</b> (sert à régler l'interligne exprimée en nombre supérieur à 1), <b>exactement</b> (sert à définir l'interligne fixe). Spécifiez la valeur nécessaire dans le champ situé à droite.</li>
						<li><b>Espacement de paragraphe</b> - définissez l'espace entre les paragraphes.
                        <ul>
                            <li><b>Avant</b> - réglez la taille de l'espace avant le paragraphe.</li>
                            <li><b>Après</b> - réglez la taille de l'espace après le paragraphe.</li>
                        </ul>
                        </li>                        
					</ul>
				</li>
			</ol>
            <p class="note"><b>Remarque</b>: on peut configurer les mêmes paramètres dans la fenêtre <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Paragraphe - Paramètres avancés</b></a> .</p>
			<p>Pour modifier rapidement l'interligne du paragraphe actuel, vous pouvez aussi cliquer sur l'icône <b>Interligne</b> <span class="icon icon-linespacing"></span> sous l'onglet <b>Accueil</b> de la barre d'outils supérieure et sélectionnez la valeur nécessaire dans la liste: 1.0, 1.15, 1.5, 2.0, 2.5, ou 3.0 lignes.</p>
		    <p>Pour modifier le décalage de paragraphe du côté gauche de la zone de texte, placez le curseur dans le paragraphe de votre choix ou sélectionnez plusieurs paragraphes à l'aide de la souris et utilisez les icônes correspondantes dans l'onglet <b>Accueil</b> de la barre d'outils supérieure: <b>Réduire le retrait</b> <span class="icon icon-decreaseindent"></span> et <b>Augmenter le retrait</b> <span class="icon icon-increaseindent"></span>.</p>
            <h2 id="textadvancedsettings">Configurer les paramètres avancés du paragraphe</h2>
		    <p>Pour ouvrir la fenêtre<b> Paragraphe - Paramètres avancés</b>, faites un clic droit sur le texte et sélectionnez l'option <b>Paramètres avancés du texte</b> dans le menu. Il est également possible de placer le curseur dans le paragraphe de votre choix - l'onglet <b>Paramètres du paragraphe</b> devient actif sur la barre latérale droite. Appuyez sur le lien <b>Afficher les paramètres avancés</b>. La fenêtre paramètres du paragraphe s'ouvre:</p>
			<img alt="Paramètres du paragraphe - Retraits et espacement" src="../images/textadvancedsettings1.png" />
			<p>L'onglet<b> Retrait et emplacement</b> permet de:</p>
            <ul>
                <li>modifier le type d'alignement du paragraphe,</li>
                <li>modifier les <b>retraits</b> du paragraphe par rapport aux <a href="../UsageInstructions/InsertAutoshapes.htm#internalmargins" onclick="onhyperlinkclick(this)">marges internes</a> de la zone de texte,
                    <ul>
                        <li><b>A gauche</b> - spécifiez le décalage du paragraphe de la marge interne <b>gauche</b> de la zone de texte et saisissez la valeur numérique appropriée,</li>
                        <li><b>A </b><b>droite</b> - spécifiez le décalage du paragraphe de la marge interne <b>droite</b> de la zone de texte et saisissez la valeur numérique appropriée,</li>
                        <li><b>Spécial</b> - spécifier le retrait de la <b>première ligne</b> du paragraphe: sélectionnez l'élément approprié du menu (<b>(aucun)</b>, <b>Première ligne</b>, <b>Suspend</b><b>u</b>) et modifiez la valeur numérique par défaut pour les options <b>Première ligne</b> ou <b>Suspendu</b>,</li>
                    </ul>
                </li>
                <li>modifiez l'<b>interligne</b> du paragraphe.</li>
            </ul>
            <p>Vous pouvez également utilisez la <b>règle</b> horizontale pour changer les retraits.</p>
            <div class = "big big-indents_ruler"></div>
            <p>Sélectionnez le(s) paragraphe(s) et faites glisser les marqueurs tout au long de la règle</p>
            <ul>
                <li>Le marqueur <b>Retrait de première ligne</b> <div class = "icon icon-firstline_indent"></div> sert à définir le décalage de la marge interne gauche de la zone de texte pour la première ligne du paragraphe.</li>
                <li>Le marqueur <b>Retrait suspendu</b> <div class = "icon icon-hanging"></div> sert à définir le décalage de la marge interne gauche de la zone de texte pour la deuxième ligne et toutes les lignes suivantes du paragraphe.</li>
                <li>Le marqueur <b>Retrait de gauche</b> <div class = "icon icon-leftindent"></div> sert à définir le décalage du paragraphe de la marge interne gauche de la zone de texte.</li>
                <li>Le marqueur <b>Retrait de droite</b> <div class = "icon icon-right_indent"></div> sert à définir le décalage du paragraphe de la marge interne gauche de la zone de texte.</li>
            </ul>
            <p class="note"><b>Remarque</b>: si vous ne voyez pas les règles, passez à l'onglet <b>Accueil</b> de la barre d'outils supérieure, cliquez sur l'icône <b>Paramètres d'affichage</b> <span class="icon icon-viewsettingsicon"></span> dans le coin supérieur droit et décochez l'option <b>Masquer les règles</b> pour les afficher.</p>
            <img alt="Paramètres du paragraphe - l'onglet Police" src="../images/textadvancedsettings2.png" />
			<p>L'onglet <b>Police</b> comporte les paramètres suivants:</p>
			<ul>
				<li><b>Barré</b> sert à barrer le texte par la ligne passant par les lettres.</li>
				<li><b>Barré double</b> sert à barrer le texte par la ligne double passant par les lettres.</li>
				<li><b>Exposant</b> sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions.</li>
				<li><b>Indice</b> sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques.</li>
				<li><b>Petites majuscules</b> sert à mettre toutes les lettres en petite majuscule.</li>
				<li><b>Majuscules</b> sert à mettre toutes les lettres en majuscule.</li>
				<li><b>Espacement des caractères </b>sert à définir l'espace entre les caractères. Augmentez la valeur par défaut pour appliquer l'espacement <b>Étendu</b>, ou diminuez la valeur par défaut pour appliquer l'espacement <b>Condensé</b>. Utilisez les touches fléchées ou entrez la valeur voulue dans la case.
                    <p>Tous les changements seront affichés dans le champ de prévisualisation ci-dessous.</p>
                </li>
			</ul>
			<img alt="Paramètres du paragraphe - l'onglet Tabulation" src="../images/textadvancedsettings3.png" />
			<p>L'onglet <b>Tabulation</b> vous permet de changer des taquets de tabulation c'est-à-dire l'emplacement où le curseur s'arrête quand vous appuyez sur la touche <b>Tab</b> du clavier.</p>
			<ul>
                <li>La <b>Tabulation par défaut</b> est 2.54 cm. Vous pouvez augmenter ou diminuer cette valeur en utilisant les boutons à flèche ou en saisissant la valeur nécessaire dans la zone.</li>
                <li><b>Position</b> sert à personnaliser les taquets de tabulation. Saisissez la valeur nécessaire dans ce champ, réglez-la en utilisant les boutons à flèche et cliquez sur le bouton <b>Spécifier</b>. La position du taquet de tabulation personnalisée sera ajoutée à la liste dans le champ au-dessous.</li>
			    <li><b>Alignement</b> sert à définir le type d'alignement pour chaque taquet de tabulation de la liste. Sélectionnez le taquet nécessaire dans la liste, choisissez l'option <b>De gauche</b>, <b>De centre</b> ou <b>De droite</b> dans la liste déroulante <b>Alignement</b> et cliquez sur le bouton <b>Spécifier</b>.
			        <ul>
			            <li><b>De gauche</b> sert à aligner le texte sur le côté gauche du taquet de tabulation; le texte se déplace à droite du taquet de tabulation quand vous saisissez le texte. Le taquet de tabulation sera indiqué sur la règle horizontale par le <div class = "icon icon-tabstopleft_marker"></div> marqueur.</li>
			            <li><b>Du centre</b> - sert à centrer le texte à l'emplacement du taquet de tabulation. Le taquet de tabulation sera indiqué sur la règle horizontale par le <div class = "icon icon-tabstopcenter_marker"></div> marqueur.</li>
			            <li><b>De droite</b> - sert à aligner le texte sur le côté droit du taquet de tabulation; le texte se déplace à gauche du taquet de tabulation quand vous saisissez le texte. Le taquet de tabulation sera indiqué sur la règle horizontale par le <div class = "icon icon-tabstopright_marker"></div> marqueur.</li>
			        </ul>
			        <p>Pour supprimer un taquet de tabulation de la liste sélectionnez-le et cliquez sur le bouton <b>Supprimer</b> ou utilisez le bouton <b>Supprimer tout</b> pour vider la liste.</p>
			    </li>
			</ul>
            <p>Pour définir les taquets de tabulation vous pouvez utiliser la règle horizontale:</p>
            <ol>
                <li>Cliquez sur le bouton de sélection de tabulation <div class = "icon icon-tabstopleft"></div> dans le coin supérieur gauche de la zone de travail pour choisir le type d'arrêt de tabulation requis: <b>À gauche,</b> <div class = "icon icon-tabstopleft"></div><b>Au centre,</b> <div class = "icon icon-tabstopcenter"></div><b>À droite</b> <div class = "icon icon-tabstopright"></div>.</li>
                <li>Cliquez sur le bord inférieur de la règle là où vous voulez positionner le taquet de tabulation. Faites-le glisser tout au long de la règle pour changer son emplacement. Pour supprimer le taquet de tabulation ajouté faites-le glisser en dehors de la règle.
                    <p><div class = "big big-tabstops_ruler"></div></p>
                    <p class="note"><b>Remarque</b>: si vous ne voyez pas les règles, passez à l'onglet <b>Accueil</b> de la barre d'outils supérieure, cliquez sur l'icône <b>Paramètres d'affichage</b> <span class="icon icon-viewsettingsicon"></span> dans le coin supérieur droit et décochez l'option <b>Masquer les règles</b> pour les afficher.</p>
                </li>
            </ol>
            <h2>Modifier un style Text Art</h2>
            <p>Sélectionnez un objet texte et cliquez sur l'icône des <b>Paramètres de Text Art</b> <span class="icon icon-textart_settings_icon"></span> dans la barre latérale sur la droite.</p>
            <p><img alt="Onglet Paramètres de Text Art" src="../images/right_textart.png" /></p>
            <ul>
                <li>Modifiez le style de texte appliqué en sélectionnant un nouveau <b>Modèle</b> dans la galerie. Vous pouvez également modifier le style de base en sélectionnant un type de police différent, une autre taille, etc.</li>
                <li>Changez <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">le remplissage</a> et <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">le contour</a>de police. Les options disponibles sont les mêmes que pour les formes automatiques.</li>
                <li>Appliquez un effet de texte en sélectionnant le type de transformation de texte voulu dans la galerie <b>Transformation</b>. Vous pouvez ajuster le degré de distorsion du texte en faisant glisser la poignée en forme de diamant rose.</li>
            </ul>
            <p><img alt="Transformation de Text Art" src="../images/textart_transformation.png" /></p>
		</div>
	</body>
</html>