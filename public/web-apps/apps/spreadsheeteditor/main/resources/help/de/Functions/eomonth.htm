﻿<!DOCTYPE html>
<html>
	<head>
		<title>MONATSENDE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>MONATSENDE-Funktion</h1>
			<p>Die Funktion <b>MONATSENDE</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie gibt die fortlaufende Zahl des letzten Tages des Monats zurück, der eine bestimmte Anzahl von Monaten vor bzw. nach dem Ausgangsdatum liegt</p>
			<p>Die Formelsyntax der Funktion <b>MONATSENDE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>MONATSENDE(Ausgangsdatum;Monate)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Ausgangsdatum</em></b> ist eine Zahl, die das erste Datum des Zeitraums darstellt, das mit der Funktion <a href="Date.htm" onclick="onhyperlinkclick(this)">Datum</a> oder einer anderen Datums- und Uhrzeitfunktion eingegeben wurde.</p>
				<p style="text-indent: 50px;"><b><em>Monate</em></b> ist die Anzahl der Monate vor oder nach <b><em>Ausgangsdatum</em></b>. Hat <b><em>Monate</em></b> ein negatives Vorzeichen, gibt die Funktion die Seriennummer des Datums zurück, das vor dem angegebenen <b>Ausgangsdatum</b> liegt. Hat  ein positives Vorzeichen, gibt die Funktion die Seriennummer des Datums zurück, das nach dem angegebenen  folgt.</p>
			<p>Anwendung der Funktion <b>MONATSENDE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>MONATSENDE</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="MONATSENDE-Funktion" src="../images/eomonth.png" /></p>
		</div>
	</body>
</html>