﻿<!DOCTYPE html>
<html>
	<head>
		<title>IKV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>IKV-Funktion</h1>
			<p>Die Funktion <b>IKV</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie gibt den internen Zinsfuß einer Investition ohne Finanzierungskosten oder Reinvestitionsgewinne zurück.</p>
			<p>Die Formelsyntax der Funktion <b>IKV</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>IKV(Werte;[Schätzwert])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Werte</em></b> ist eine Matrix oder ein Bezug auf Zellen, in denen die Zahlen stehen, für die Sie den internen Zinsfuß berechnen möchten. Mindestens einer der Werte muss negativ und mindestens einer muss positiv sein.</p> 
				<p style="text-indent: 50px;"><b><em>Schätzwert</em></b> ist eine Zahl, von der Sie annehmen, dass sie dem Ergebnis der Funktion XINTZINSFUSS nahe kommt. Dieses Argument ist optional. Wird es ausgelassen, geht die Funktion vom <b><em>Schätzwert</em></b> 10 % aus.</p> 			
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>IKV</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>IKV</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="IKV-Funktion" src="../images/irr.png" /></p>
		</div>
	</body>
</html>