<!DOCTYPE html>
<html>
	<head>
		<title>Как вставить текст в презентацию PowerPoint - ONLYOFFICE</title>
		<meta charset="utf-8" />
        <meta name="description" content="Работа с текстом в презентациях PowerPoint: узнайте, как добавлять и форматировать текст, менять его стиль и направление, задавать параметры абзаца." />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <meta name="keywords" content="текст в презентации, как в powerpoint добавить текст, как вставить текст в powerpoint, как добавить текст в презентацию, как написать текст на слайде в powerpoint, как вставить текст в презентацию, как вставить текст в презентацию powerpoint, как вставить текст в слайд презентации, как добавить текст в powerpoint, работа с текстом в powerpoint">
        <link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка и форматирование текста презентации</h1>
			<h2>Вставка текста</h2>
			<p>Новый текст можно добавить тремя разными способами:</p>
			    <ul>
			        <li>Добавить фрагмент текста внутри соответствующей текстовой рамки, предусмотренной на макете слайда. Для этого установите курсор внутри текстовой рамки и напишите свой текст или вставьте его, используя сочетание клавиш <b>Ctrl+V</b>, вместо соответствующего текста по умолчанию.</li>
			        <li>Добавить фрагмент текста в любом месте на слайде. Можно вставить надпись (прямоугольную рамку, внутри которой вводится текст) или объект Text Art (текстовое поле с предварительно заданным стилем и цветом шрифта, позволяющее применять текстовые эффекты). В зависимости от требуемого типа текстового объекта можно сделать следующее:
			                    <ul>
                                    <li>
                                        чтобы добавить текстовое поле, щелкните по значку <div class = "icon icon-inserttexticon"></div> <b>Надпись</b> на вкладке <b>Главная</b> или <b>Вставка</b> верхней панели инструментов, выберите одну из следующих опций: <b>Вставить горизонтальную надпись</b> или <b>Вставить вертикальную надпись</b>, затем щелкните там, где требуется поместить надпись, удерживайте кнопку мыши и перетаскивайте границу текстового поля, чтобы задать его размер. Когда вы отпустите кнопку мыши, в добавленном текстовом поле появится курсор, и вы сможете ввести свой текст.
                                        <p class="note"><b>Примечание</b>: надпись можно также вставить, если щелкнуть по значку <span class="icon icon-insertautoshape"></span> <b>Фигура</b> на верхней панели инструментов и выбрать фигуру <span class="icon icon-text_autoshape"></span> из группы <b>Основные фигуры</b>.</p>
                                    </li>
                                    <li>чтобы добавить объект Text Art, щелкните по значку <div class = "icon icon-inserttextarticon"></div> <b>Text Art</b> на вкладке <b>Вставка</b> верхней панели инструментов, затем щелкните по нужному шаблону стиля – объект Text Art будет добавлен в центре слайда. Выделите мышью стандартный текст внутри текстового поля и напишите вместо него свой текст.</li>
                                </ul>
			        </li>
			        <li>Добавить фрагмент текста внутри автофигуры. Выделите фигуру и начинайте печатать текст.</li>
			    </ul>
            <p>Щелкните за пределами текстового объекта, чтобы применить изменения и вернуться к слайду.</p>
            <p>Текст внутри текстового объекта является его частью (при перемещении или повороте текстового объекта текст будет перемещаться или поворачиваться вместе с ним).</p>
            <p>Поскольку вставленный текстовый объект представляет собой прямоугольную рамку (у нее по умолчанию невидимые границы) с текстом внутри, а эта рамка является обычной автофигурой, можно изменять свойства и фигуры, и текста.</p>
            <p>Чтобы удалить добавленный текстовый объект, щелкните по краю текстового поля и нажмите клавишу <b>Delete</b> на клавиатуре. Текст внутри текстового поля тоже будет удален.</p>
            <h2>Форматирование текстовых полей презентации</h2>
            <p>Выделите текстовое поле, щелкнув по его границе, чтобы можно было изменить его свойства. Когда текстовое поле выделено, его границы отображаются как сплошные, а не пунктирные линии.</p>
            <p><img alt="Выделенное текстовое поле" src="../images/textbox_boxselected.png" /></p>
			<ul>
                <li>чтобы <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">изменить размер текстового поля, переместить, повернуть</a> его, используйте специальные маркеры по краям фигуры.</li>
                <li>чтобы изменить <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">заливку</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">контур</a> текстового поля, <b>заменить</b> прямоугольное поле на какую-то другую фигуру или открыть <a href="InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">дополнительные параметры фигуры</a>, щелкните по значку <b>Параметры фигуры</b> <div class = "icon icon-shape_settings_icon"></div> на правой боковой панели и используйте соответствующие опции.</li>
                <li>чтобы <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">выровнять текстовое поле на слайде, повернуть или отразить поле, расположить</a> текстовые поля в определенном порядке относительно других объектов, щелкните правой кнопкой мыши по границе текстового поля и используйте опции контекстного меню.</li>
                <li>чтобы создать <b>колонки текста</b> внутри текстового поля, щелкните правой кнопкой мыши по границе текстового поля, нажмите на пункт меню <b>Дополнительные параметры фигуры</b> и перейдите на вкладку <a href="../UsageInstructions/InsertAutoshapes.htm#columns" onclick="onhyperlinkclick(this)"><b>Колонки</b></a> в окне <b>Фигура - дополнительные параметры</b>.</li>
            </ul>
            <h2 id="formattext">Форматирование текста внутри текстового поля</h2>
            <p>Щелкните по тексту внутри текстового поля, чтобы можно было изменить его свойства. Когда текст выделен, границы текстового поля отображаются как пунктирные линии.</p>
            <p><img alt="Выделенный текст" src="../images/textbox_textselected.png" /></p>
            <p class="note"><b>Примечание</b>: форматирование текста можно изменить и в том случае, если выделено текстовое поле, а не сам текст. В этом случае любые изменения будут применены ко всему тексту в текстовом поле. Некоторые параметры форматирования шрифта (тип, размер, цвет и стили оформления шрифта) можно отдельно применить к предварительно выделенному фрагменту текста.</p>
			<p><b>Выравнивание текста внутри текстового поля</b></p>
			<p>Горизонтально текст выравнивается четырьмя способами: по левому краю, по правому краю, по центру или по ширине. Для этого:</p>
			<ol>
				<li>установите курсор в том месте, где требуется применить выравнивание (это может быть новая строка или уже введенный текст),</li>
				<li>разверните список <b>Горизонтальное выравнивание</b> <div class = "icon icon-horizontalalign"></div> на вкладке <b>Главная</b> верхней панели инструментов,</li>
				<li>выберите тип выравнивания, который вы хотите применить:
					<ul>
						<li>опция <b>Выравнивание текста по левому краю</b> <div class = "icon icon-alignleft"></div> позволяет выровнять текст по левому краю текстового поля (правый край остается невыровненным).</li>
						<li>опция <b>Выравнивание текста по центру</b> <div class = "icon icon-aligncenter"></div> позволяет выровнять текст по центру текстового поля (правый и левый края остаются невыровненными).</li>
						<li>опция <b>Выравнивание текста по правому краю</b> <div class = "icon icon-alignright"></div> позволяет выровнять текст по правому краю текстового поля (левый край остается невыровненным).</li>
						<li>опция <b>Выравнивание текста по ширине</b> <div class = "icon icon-justify"></div> позволяет выровнять текст как по левому, так и по правому краю текстового поля (выравнивание осуществляется за счет добавления дополнительных интервалов там, где это необходимо).</li>
					</ul>
				</li>
			</ol>
            <p class="note"><b>Примечание</b>: эти параметры также можно найти в окне <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Абзац - Дополнительные параметры</b></a>.</p>
			<p>Вертикально текст выравнивается тремя способами: по верхнему краю, по середине или по нижнему краю. Для этого:</p>
			<ol>
				<li>установите курсор в том месте, где требуется применить выравнивание (это может быть новая строка или уже введенный текст),</li>
				<li>разверните список <b>Вертикальное выравнивание</b> <div class = "icon icon-verticalalign"></div> на вкладке <b>Главная</b> верхней панели инструментов,</li>
				<li>выберите тип выравнивания, который вы хотите применить:
					<ul>
						<li>опция <b>Выравнивание текста по верхнему краю</b> <div class = "icon icon-aligntop"></div> позволяет выровнять текст по верхнему краю текстового поля.</li>
						<li>опция <b>Выравнивание текста по середине</b> <div class = "icon icon-alignmiddle"></div> позволяет выровнять текст по центру текстового поля.</li>
						<li>опция <b>Выравнивание текста по нижнему краю</b> <div class = "icon icon-alignbottom"></div> позволяет выровнять текст по нижнему краю текстового поля.</li>
					</ul>
				</li>
			</ol>
			<hr />
            <p><b>Изменение направления текста</b></p>
            <p>Чтобы <b>повернуть</b> текст внутри текстового поля, щелкните по тексту правой кнопкой мыши, выберите опцию <b>Направление текста</b>, а затем выберите один из доступных вариантов: <b>Горизонтальное</b> (выбран по умолчанию), <b>Повернуть текст вниз</b> (задает вертикальное направление, сверху вниз) или <b>Повернуть текст вверх</b> (задает вертикальное направление, снизу вверх).</p>
            <hr />
            <p id="formatfont"><b>Настройка типа, размера, цвета шрифта и применение стилей оформления</b></p>
			<p>Можно выбрать тип, размер и цвет шрифта, а также применить различные стили оформления шрифта, используя соответствующие значки, расположенные на вкладке <b>Главная</b> верхней панели инструментов.</p>
			<p class="note"><b>Примечание</b>: если необходимо применить форматирование к тексту, который уже есть в презентации, выделите его мышью или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">с помощью клавиатуры</a>, а затем примените форматирование. Также можно поместить курсор мыши в нужное слово, чтобы применить форматирование только к этому слову.</p>
			<table>
				<tr>
					<td width="10%">Шрифт</td>
					<td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Используется для выбора шрифта из списка доступных. <span class="desktopDocumentFeatures">Если требуемый шрифт отсутствует в списке, его можно скачать и установить в вашей операционной системе, после чего он будет доступен для использования в <em>десктопной версии</em>.</span></td>
				</tr>
				<tr>
					<td>Размер шрифта</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Используется для выбора предустановленного значения размера шрифта из выпадающего списка (доступны следующие стандартные значения: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 и 96). Также можно вручную ввести произвольное значение до 300 пунктов в поле ввода и нажать клавишу <em>Enter</em>.</td>
				</tr>
				<tr>
					<td>Увеличить размер шрифта</td>
					<td><div class = "icon icon-larger"></div></td>
					<td>Используется для изменения размера шрифта, делая его на один пункт крупнее при каждом нажатии на кнопку.</td>
				</tr>
				<tr>
					<td>Уменьшить размер шрифта</td>
					<td><div class = "icon icon-smaller"></div></td>
					<td>Используется для изменения размера шрифта, делая его на один пункт мельче при каждом нажатии на кнопку.</td>
				</tr>
				<tr>
					<td>Изменить регистр</td>
					<td><div class = "icon icon-change_case"></div></td>
					<td>Используется для изменения регистра шрифта. <em>Как в предложениях.</em> - регистр совпадает с обычным предложением. <em>нижнеий регистр</em> - все буквы маленькие. <em>ВЕРХНИЙ РЕГИСТР</em> - все буквы прописные. <em>Каждое Слово С Прописной</em> - каждое слово начинается с заглавной буквы. <em>иЗМЕНИТЬ рЕГИСТР</em> - поменять регистр выделенного текста или слова, в котором находится курсор мыши.</td>
				</tr>
				<tr>
					<td>Цвет выделения</td>
					<td><div class = "icon icon-highlightcolor"></div></td>
					<td>
						Используется для выделения отдельных предложений, фраз, слов или даже символов путем добавления цветовой полосы, имитирующей отчеркивание текста
						маркером. Можно выделить нужную часть текста, а потом нажать направленную вниз стрелку рядом с этим значком, чтобы выбрать цвет на палитре (этот набор
						цветов не зависит от выбранной <b>Цветовой схемы</b> и включает в себя 16 цветов), и этот цвет будет применен к выбранному тексту. Или же можно сначала выбрать
						цвет выделения, а потом начать выделять текст мышью - указатель мыши будет выглядеть так: <div class = "icon icon-highlight_color_mouse_pointer"></div> - и появится возможность выделить несколько разных частей
						текста одну за другой. Чтобы остановить выделение текста, просто еще раз щелкните по значку. Для очистки цвета выделения воспользуйтесь опцией <b>Без заливки</b>.
					</td>
				<tr>
				<tr>
					<td>Цвет шрифта</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Используется для изменения цвета букв/символов в тексте. <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">Для выбора цвета</a> нажмите направленную вниз стрелку рядом со значком.</td>
				</tr>
				<tr>
					<td>Полужирный</td>
					<td><div class = "icon icon-bold"></div></td>
					<td>Используется для придания шрифту большей насыщенности.</td>
				</tr>
				<tr>
					<td>Курсив</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Используется для придания шрифту наклона вправо.</td>
				</tr>
				<tr>
					<td>Подчеркнутый</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Используется для подчеркивания текста чертой, проведенной под буквами.</td>
				</tr>
				<tr>
					<td>Зачеркнутый</td>
					<td><div class = "icon icon-strike"></div></td>
					<td>Используется для зачеркивания текста чертой, проведенной по буквам.</td>
				</tr>
				<tr>
					<td>Надстрочные знаки</td>
					<td><div class = "icon icon-sup"></div></td>
					<td>Используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях.</td>
				</tr>
				<tr>
					<td>Подстрочные знаки</td>
					<td><div class = "icon icon-sub"></div></td>
					<td>Используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах.</td>
				</tr>
			</table>
			<p><b>Задание междустрочного интервала и изменение отступов абзацев</b></p>
			<p>Можно задать высоту строки для строк текста в абзаце, а также поля между текущим и предыдущим или последующим абзацем.</p>
			<p><img class="floatleft"alt="Вкладка Параметры текста" src="../images/textsettingstab.png" /></p>
			<p>Для этого:</p>
			<ol style="margin-left: 280px;">
				<li>установите курсор в пределах нужного абзаца, или выделите мышью несколько абзацев,</li>
				<li>используйте соответствующие поля вкладки <div class = "icon icon-text_settings_icon"></div> <b>Параметры текста</b> на правой боковой панели, чтобы добиться нужного результата:
					<ul>
						<li><b>Междустрочный интервал</b> - задайте высоту строки для строк текста в абзаце. Вы можете выбрать одну из двух опций: <b>множитель</b> (устанавливает междустрочный интервал, который может быть выражен в числах больше 1), <b>точно</b> (устанавливает фиксированный междустрочный интервал). Необходимое значение можно указать в поле справа.</li>
						<li><b>Интервал между абзацами</b> - задайте величину свободного пространства между абзацами.
                            <ul>
                                <li><b>Перед</b> - задайте величину свободного пространства перед абзацем.</li>
                                <li><b>После</b> - задайте величину свободного пространства после абзаца.</li>
                            </ul>
                        </li>                        
					</ul>
				</li>
			</ol>
            <p class="note"><b>Примечание</b>: эти параметры также можно найти в окне <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Абзац - Дополнительные параметры</b></a>.</p>
            <p>Чтобы быстро изменить междустрочный интервал в текущем абзаце, можно также использовать значок <b>Междустрочный интервал</b> <span class="icon icon-linespacing"></span> на вкладке <b>Главная</b> верхней панели инструментов, выбрав нужное значение из списка: 1.0, 1.15, 1.5, 2.0, 2.5, или 3.0 строки.</p>
		    <p>Чтобы изменить смещение абзаца от левого края текстового поля, установите курсор в пределах нужного абзаца или выделите мышью несколько абзацев и используйте соответствующие значки на вкладке <b>Главная</b> верхней панели инструментов: <b>Уменьшить отступ</b> <span class="icon icon-decreaseindent"></span> и <b>Увеличить отступ</b> <span class="icon icon-increaseindent"></span>.</p>
            <h2 id="textadvancedsettings">Изменение дополнительных параметров абзаца</h2>
		    <p>Чтобы открыть окно <b>Абзац - Дополнительные параметры</b>, щелкните по тексту правой кнопкой мыши и выберите в контекстном меню пункт <b>Дополнительные параметры текста</b>. Также можно установить курсор в пределах нужного абзаца - на правой боковой панели будет активирована вкладка <span class="icon icon-text_settings_icon"></span> <b>Параметры текста</b>. Нажмите на ссылку <b>Дополнительные параметры</b>. Откроется окно свойств абзаца:</p>
			<img alt="Свойства абзаца - вкладка Отступы и интервалы" src="../images/textadvancedsettings1.png" />
			<p>На вкладке <b>Отступы и интервалы</b> можно выполнить следующие действия:</p>
            <ul>
                <li>изменить тип <b>выравнивания</b> текста внутри абзаца,</li>
                <li>изменить <b>отступы</b> абзаца от <a href="../UsageInstructions/InsertAutoshapes.htm#internalmargins" onclick="onhyperlinkclick(this)">внутренних полей</a> текстового объекта,
                    <ul>
                        <li><b>Слева</b> - задайте смещение всего абзаца от <b>левого</b> внутреннего поля текстового блока, указав нужное числовое значение,</li>
                        <li><b>Справа</b> - задайте смещение всего абзаца от <b>правого</b> внутреннего поля текстового блока, указав нужное числовое значение,</li>
                        <li><b>Первая строка</b> - задайте отступ для <b>первой строки</b> абзаца, выбрав соответствующий пункт меню (<b>(нет)</b>, <b>Отступ</b>, <b>Выступ</b>) и изменив числовое значение для <b>Отступа</b> или <b>Выступа</b>, заданное по умолчанию,</li>
                    </ul>
                </li>
                <li>изменить <b>междустрочный интервал</b> внутри абзаца.</li>
            </ul>
            <p>
        Чтобы задать отступы, можно также использовать горизонтальную <b>линейку</b>.
      </p>
      <div class = "big big-indents_ruler"></div>
      <p>Выделите нужный абзац или абзацы и перетащите маркеры отступов по линейке.</p>
      <ul>
        <li>
          Маркер <b>отступа первой строки</b> <div class = "icon icon-firstline_indent"></div> используется, чтобы задать смещение от левого внутреннего поля текстового объекта для первой строки абзаца.
        </li>
        <li>
          Маркер <b>выступа</b> <div class = "icon icon-hanging"></div> используется, чтобы задать смещение от левого внутреннего поля текстового объекта для второй и всех последующих строк абзаца.
        </li>
        <li>Маркер <b>отступа слева</b> <div class = "icon icon-leftindent"></div> используется, чтобы задать смещение от левого внутреннего поля текстового объекта для всего абзаца.</li>
        <li>
          Маркер <b>отступа справа</b> <div class = "icon icon-right_indent"></div> используется, чтобы задать смещение абзаца от правого внутреннего поля текстового объекта.
        </li>
      </ul>
      <p class="note">
        <b>Примечание</b>: если вы не видите линеек, перейдите на вкладку <b>Главная</b> верхней панели инструментов, щелкните по значку <b>Параметры представления</b> <span class="icon icon-viewsettingsicon"></span> в правом верхнем углу и снимите отметку с опции <b>Скрыть линейки</b>, чтобы отобразить их.</p>
      <img alt="Свойства абзаца - вкладка Шрифт" src="../images/textadvancedsettings2.png" />
			<p>Вкладка <b>Шрифт</b> содержит следующие параметры:</p>
			<ul>
				<li><b>Зачёркивание</b> - используется для зачеркивания текста чертой, проведенной по буквам.</li>
				<li><b>Двойное зачёркивание</b> - используется для зачеркивания текста двойной чертой, проведенной по буквам.</li>
				<li><b>Надстрочные</b> - используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях.</li>
				<li><b>Подстрочные</b> - используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах.</li>
				<li><b>Малые прописные</b> - используется, чтобы сделать все буквы строчными.</li>
				<li><b>Все прописные</b> - используется, чтобы сделать все буквы прописными.</li>
                <li><b>Межзнаковый интервал</b> - используется, чтобы задать расстояние между символами. Увеличьте значение, заданное по умолчанию, чтобы применить <b>Разреженный</b> интервал, или уменьшите значение, заданное по умолчанию, чтобы применить <b>Уплотненный</b> интервал. Используйте кнопки со стрелками или введите нужное значение в поле ввода.
                    <p>Все изменения будут отображены в расположенном ниже поле предварительного просмотра.</p>
                </li>
			</ul>
			<img alt="Свойства абзаца - вкладка Табуляция" src="../images/textadvancedsettings3.png" />
			<p>На вкладке <b>Табуляция</b> можно изменить позиции табуляции, то есть те позиции, куда переходит курсор при нажатии клавиши <b>Tab</b> на клавиатуре.</p>
			<ul>
                <li>Позиция табуляции <b>По умолчанию</b> имеет значение 2.54 см. Это значение можно уменьшить или увеличить, используя кнопки со стрелками или введя в поле нужное значение.</li>
			    <li><b>Позиция</b> - используется, чтобы задать пользовательские позиции табуляции. Введите в этом поле нужное значение, настройте его более точно, используя кнопки со стрелками, и нажмите на кнопку <b>Задать</b>. Пользовательская позиция табуляции будет добавлена в список в расположенном ниже поле.</li>
			    <li><b>Выравнивание</b> - используется, чтобы задать нужный тип выравнивания для каждой из позиций табуляции в расположенном выше списке. Выделите нужную позицию табуляции в списке, выберите в выпадающем списке <b>Выравнивание</b> опцию <b>По левому краю</b>, <b>По центру</b> или <b>По правому краю</b> и нажмите на кнопку <b>Задать</b>.
			        <ul>
			            <li><b>По левому краю</b> - выравнивает текст по левому краю относительно позиции табуляции; при наборе текст движется вправо от позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером <div class = "icon icon-tabstopleft_marker"></div>.</li>
			            <li><b>По центру</b> - центрирует текст относительно позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером <div class = "icon icon-tabstopcenter_marker"></div>.</li>
			            <li><b>По правому краю</b> - выравнивает текст по правому краю относительно позиции табуляции; при наборе текст движется влево от позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером <div class = "icon icon-tabstopright_marker"></div>.</li>
			        </ul>
			        <p>Для удаления позиций табуляции из списка выделите позицию табуляции и нажмите кнопку <b>Удалить</b> или <b>Удалить все</b>.</p>
			    </li>
			</ul>
      <p>Для установки позиций табуляции можно также использовать горизонтальную линейку:</p>
      <ol>
        <li>
          Выберите нужный тип позиции табуляции, нажав на кнопку <div class = "icon icon-tabstopleft"></div> в левом верхнем углу рабочей области:          
              <b>По левому краю</b> <div class = "icon icon-tabstopleft"></div>,
              <b>По центру</b> <div class = "icon icon-tabstopcenter"></div>,
              <b>По правому краю</b> <div class = "icon icon-tabstopright"></div>.
            </li>
          <li>
          Щелкните по нижнему краю линейки в том месте, где требуется установить позицию табуляции. Для изменения местоположения позиции табуляции перетащите ее по линейке. Для удаления добавленной позиции табуляции перетащите ее за пределы линейки.
          <p>
            <div class = "big big-tabstops_ruler"></div>
          </p>
              <p class="note"><b>Примечание</b>: если вы не видите линеек, перейдите на вкладку <b>Главная</b> верхней панели инструментов, щелкните по значку <b>Параметры представления</b> <span class="icon icon-viewsettingsicon"></span> в правом верхнем углу и снимите отметку с опции <b>Скрыть линейки</b>, чтобы отобразить их.</p>
        </li>
      </ol>      
            <h2>Изменение стиля объекта Text Art</h2>
            <p>Выделите текстовый объект и щелкните по значку <b>Параметры объектов Text Art</b> <span class="icon icon-textart_settings_icon"></span> на правой боковой панели.</p>
            <p><img alt="Вкладка Параметры объектов Text Art" src="../images/right_textart.png" /></p>
            <ul>
                <li>Измените примененный стиль текста, выбрав из галереи новый <b>Шаблон</b>. Можно также дополнительно изменить этот базовый стиль, выбрав другой тип, размер шрифта и т.д.</li>
                <li>Измените <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">заливку</a> и <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">контур</a> шрифта. Доступны точно такие же опции, как и для автофигур.</li>
                <li>Примените текстовый эффект, выбрав нужный тип трансформации текста из галереи <b>Трансформация</b>. Можно скорректировать степень искривления текста, перетаскивая розовый маркер в форме ромба.</li>
            </ul>
            <p><img alt="Трансформация объекта Text Art" src="../images/textart_transformation.png" /></p>
    </div>
	</body>
</html>