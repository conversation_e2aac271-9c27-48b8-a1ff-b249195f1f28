﻿<!DOCTYPE html>
<html>
	<head>
		<title>T.TEST-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>T.TEST-Funktion</h1>
			<p>Die Funktion <b>T.TEST</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt die Teststatistik eines Student&apos;schen t-Tests zurück. Mithilf<PERSON> von <b>T.TEST</b> können Sie testen, ob zwei Stichproben aus zwei Grundgesamtheiten mit demselben Mittelwert stammen.</p>
			<p>Die Formelsyntax der Funktion <b>T.TEST</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>T.TEST(Matrix1;Matrix2;Seiten;Typ)</em></b></p> 
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Matrix1</em></b> ist der erste Datensatz.</p>
            <p style="text-indent: 50px;"><b><em>Matrix2</em></b> ist der zweite Datensatz.</p>
            <p style="text-indent: 50px;"><b><em>Seiten</em></b> bestimmt die Anzahl der Endflächen. Ist Seiten 1, verwendet die Funktion die einseitige Verteilung. Ist Seiten 2, verwendet die Funktion die zweiseitige Verteilung.</p>
            <p style="text-indent: 50px;"><b><em>Typ</em></b> ist ein nummerischer Wert, der den Typ des durchzuführenden t-Tests bestimmt. Die folgenden Parameter stehen zur Auswahl:</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Typ</b></td>
                    <td><b>Ausgeführter t-Test</b></td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>Gepaart</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Zwei Stichproben, gleiche Varianz (homoskedastisch)</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Zwei Stichproben, ungleiche Varianz (heteroskedastisch)</td>
                </tr>
            </table>
            <p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>T.TEST</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>T.TEST</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="T.TEST-Funktion" src="../images/t-test.png" /></p>
		</div>
	</body>
</html>