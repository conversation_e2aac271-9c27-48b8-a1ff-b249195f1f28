{"DATE": {"a": "(year; month; day)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ໃນລະຫັດ​ວັນ​ທີ-​ເວລາ"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "ຄືນ ຜົນລົບ ຈາກສອງຄ່າ ວັນທີ (ວັນທີເລີ່ມ ແລະ ວັນທີສິ້ນສຸດ) ໂດຍອີງໃສ່ ຫົວໜ່ວຍ (unit) ທີໄດ້ລະບຸໄວ້"}, "DATEVALUE": {"a": "(date_text)", "d": "ປ່ຽນ​ວັນ​ທີ​ໃນ​ຮູບ​ແບບ​ຂໍ້ຄວາມ​ເປັນ​ຕົວ​ເລກ​ທີ່​ສະ​ແດງ​ເຖິງ​ວັນ​ທີ​ນັ້ນ​ໃນລະຫັດ​ວັນ​ທີ-​ເວລາ"}, "DAY": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າວັນ​ຂອງ​ເດືອນ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ແຕ່ 1 ຫາ 31."}, "DAYS": {"a": "(end_date; start_date)", "d": "ສົ່ງ​ຄືນ​ຈຳນວນ​ມື້ລະ​ຫວ່າງ​ສອງ​ວັນ​ທີ."}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນວັນລະ​ຫວ່າງ​ສອງວັນ​ທີ​ຕາມປີ 360 ວັນ (ເດືອນ 30 ວັນ​ສິບ​ສອງ​ເດືອນ)"}, "EDATE": {"a": "(start_date; months)", "d": "ສະ​ແດງ​ຜົນ​ຕົວ​ເລກ​ລຳ​ດັບ​ຂອງວັນ​ທີ​ທີ່​ເປັນ​ຕົວ​ເລກ​ຊີ້ບອກ​ໄວ້​ຂອງເດືອນກ່ອນ ຫຼື​ຫຼັງ​ຈາກວັນ​ທີ​ເລີ່ມ​ຕົ້ນ"}, "EOMONTH": {"a": "(start_date; months)", "d": "​ສະ​ແດງ​ຜົນ​ຕົວ​ເລກ​ລຳ​ດັບ​ຂອງວັນ​ສຸດ​ທ້າຍ​ຂອງ​ເດືອນ​ກ່ອນ ຫຼື​ຫຼັງ​ຈາກຈຳ​ນວນ​ເດືອນລະ​ບຸ​ໄວ້"}, "HOUR": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຊົ່ວ​ໂມງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 0 (12:00 A.M.) ຫາ 23 (11:00 P.M.) "}, "ISOWEEKNUM": {"a": "(date)", "d": "ສົ່ງ​ຄືນ​ຈຳນວນ​ອາທິດ​ ISO ​ໃນ​ປີ​ສໍາລັບ​ວັນ​ທີ່​ທີ່​​ໃຫ້"}, "MINUTE": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ນາທີ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກຕັ້ງ​ແຕ່ 0 ຫາ 59 "}, "MONTH": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເດືອນ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 1 (ມັງກອນ) ຫາ 12 (ທັນວາ)."}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "ສະ​ແດງ​ຜົນ​ວັນ​ເຮັດ​ວຽກ​ທັງ​ໝົດ​ລະ​ຫວ່າງສອງວັນ​ທີ"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "ສົ່ງ​ກັບ​ຈໍານວນ​ວັນ​ເຮັດ​ວຽກ​ເຕັມ​ວັນ​ທີ່ຢູ່​ລະຫວ່າງ​ສອງ​ວັນ​ທີ ​ໂດຍ​ມີ​ພາຣາມິເຕີ​ວັນ​ທ້າຍ​ອາທິດ​ແບບ​ກໍານົດ​ເອງ"}, "NOW": {"a": "()", "d": "ສົ່ງຄືນຄ່າວັນທີ ແລະ ເວລາປະຈຸບັນທີ່​ມີຮູບແບບ​ເປັນວັນທີ ແລະເວລາ."}, "SECOND": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ວິນາທີ ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 0 ຫາ 59."}, "TIME": {"a": "(hour; minute; second)", "d": "ປ່ຽນແປງຄ່າຕົວເລກຊົ່ວໂມງ, ນາທີ, ແລະວິນາທີທີ່ລະບຸ, ໃຫ້ເປັນຕົວເລກລະຫັດຂອງ, ເຊິ່ງຈະຖືກຈັດຮູບແບບດ້ວຍຮູບແບບເວລາ"}, "TIMEVALUE": {"a": "(time_text)", "d": "ປ່ຽນ​ເວລາ​ຂໍ້ຄວາມ​ເປັນ​ເລກ​ດໍາ​ລັບ​ຕໍ່​ເນື່ອງ​ຂອງສໍາລັບ​ເວລາ​​ໃດ​ໜຶ່ງ, ​ເຊິ່ງ​ເປັນ​ຕົວ​ເລກ​ຕັ້ງ​ແຕ່ 0 (12:00:00 AM) ຫາ 0.999988426 (11:59:59 PM). ຈັດ​ຮູບ​ແບບ​ຕົວ​ເລກ​ທີ່​ມີ​ຮູບ​ແບບ​ເວລາ​ຫຼັງຈາກ​ການ​ປ້ອນ​ສູດ​ເຂົ້າ"}, "TODAY": {"a": "()", "d": "ສົ່ງຄືນຄ່າວັນທີປະຈຸບັນທີ່ມີຮູບແບບເປັນວັນທີ."}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "ສົ່ງຄືນຄ່າຕົວເລກຕັ້ງແຕ່ 1 ຫາ 7 ເຊິ່ງຈະລະບຸວັນໃນອາທິດຂອງວັນທີ."}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "ສົ່ງ​ຄືນ​ຈຳນວນ​ອາທິດ​ໃນ​ປີ"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "ສະ​ແດງ​ຜົນ​ຕົວ​ເລກ​ລຳ​ດັບ​ຂອງວັນ​ທີ ກ່ອນ ຫຼື​ຫຼັງ​ຈຳ​ນວນ​ວັນ​ສະ​ເພາະ"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "ສົ່ງ​ຄືນ​ເລກ​ດໍາ​ລັບ​ຂອງ​ວັນ​ທີ​ກ່ອນ ຫຼືຫຼັງຈໍານວນ​ວັນເຮັດ​ວຽກ​ທີ່​ລະບຸ ​ໂດຍ​ມີ​ພາຣາມິເຕີ​ວັນ​ທ້າຍ​ອາທິດ​ທີ່​ກຳນົດ​ເອງ"}, "YEAR": {"a": "(serial_number)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປີ​ຂອງ​ວັນ​ທີ ​ເຊິ່ງ​ເປັນ​ຈໍານວນ​ຖ້ວນ​ໃນ​ຂອບ​ເຂດ 1900 - 9999."}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "ສະ​ແດງ​ຜົນ​ເສດ​ສ່ວນ​ປີ​ທີ່​ແທນ​ໃຫ້​ຈຳ​ນວນ​ວັນ​ທັງ​ໝົດ​ລະ​ຫວ່າງ​ວັນ​ທີ_ເລີ່ມ​ຕົ້ນ ແລະ ​ວັນ​ທີ_ສິ້ນ​ສຸດ"}, "BESSELI": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel Yn(x)"}, "BESSELJ": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel ດັດ​ແປງ​ແລ້ວ ln(x)"}, "BESSELY": {"a": "(x; n)", "d": "ສະ​ແດງ​ຜົນຟັງ​ຄ໌​ຊັນ Bessel Jn(x)"}, "BIN2DEC": {"a": "(number)", "d": "ປ່ຽນເລກຖານສອງເປັນເລກຖານສິບ"}, "BIN2HEX": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສອງເປັນເລກຖານສິບ ຫົກ"}, "BIN2OCT": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສອງເປັນເລກຖານແປດ"}, "BITAND": {"a": "(number1; number2)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຂອງ​ຄ່າ​ຄວາມຈິງ 'AND' ຂອງ​ສອງ​ຕົວ​ເລກ​ໃນ​ຮູບ​ຂອງ​ບິດ"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ​ທີ່​ຍ້າຍ​ໄປ​ທາງ​ຊ້າຍ​ຈໍານວນ shift_amount ບິດ"}, "BITOR": {"a": "(number1; number2)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຂອງ​ຄ່າ​ຄວາມ​ຈິງ 'Or' ຂອງ​ສອງ​ຕົວ​ເລກ​ໃນ​ຮູບ​ຂອງ​ບິດ"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ​ທີ່​ຍ້າຍ​ໄປ​ທາງ​ຂວາຈໍານວນ shift_amount ບິດ"}, "BITXOR": {"a": "(number1; number2)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຂອງ​ຄ່າ​ຄວາມ​ຈິງ 'Exclusive Or' ຂອງ​ສອງ​ຕົວ​ເລກ​ໃນ​ຮູບ​ຂອງ​ບິດ"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "ແປງສຳປະສິດຂອງສ່ວນຈິງ ແລະ ສ່ວນຈິນຕະນາການເປັນຈຳນວນຊັບຊ້ອນ"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "ປ່ຽນຈຳນວນຈາກລະບົບວັດ​ແທກໜຶ່ງເປັນອີກລະບົບ​ວັດ​ແທກ​ອື່ນໜຶ່ງ"}, "DEC2BIN": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບເປັນເລກຖານສອງ"}, "DEC2HEX": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບເປັນເລກຖານສິບຫົກ"}, "DEC2OCT": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບເປັນເລກຖານແປດ"}, "DELTA": {"a": "(number1; [number2])", "d": "ທົດສອບວ່າຈຳນວນສອງຈຳນວນມີຄ່າເທົ່າກັນຫຼືບໍ່"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "ສົ່ງຄືນຟັງຊັນຂໍ້ຜິດພາດ"}, "ERF.PRECISE": {"a": "(X)", "d": "ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ຄ່າ​ຜິດພາດ"}, "ERFC": {"a": "(x)", "d": "ສະ​ແດງ​ຜົນ​ຟັງ​ຄ໌​ຊັນ​ຜິດ​ພາດ​ເຕີມ​ເຕັມ"}, "ERFC.PRECISE": {"a": "(X)", "d": "ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ຄ່າ​ຜິດພາດ​ເສີມ​ເຕີມ"}, "GESTEP": {"a": "(number; [step])", "d": "ທົດສອບວ່າຈຳນວນທີ່ລະບຸມີຄ່າຫຼາຍກ່ວາຄ່າທີ່ໃຊ້ເປັນຕົວທຽບຫຼືບໍ່"}, "HEX2BIN": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບຫົກເປັນເລກຖານສອງ"}, "HEX2DEC": {"a": "(number)", "d": "ປ່ຽນເລກຖານສິບຫົກເປັນເລກຖານສິບ"}, "HEX2OCT": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານສິບຫົກເປັນເລກຖານແປດ"}, "IMABS": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າສົມບູນ (ໂມດູນ) ຂອງຈຳນວນຊັບຊ້ອນ"}, "IMAGINARY": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດຂອງສ່ວນຈິນຕະນາການຂອງຈຳນວນຊັບຊ້ອນ"}, "IMARGUMENT": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າຂໍ້ພິສູດ q ເຊິ່ງເປັນຄ່າມູມໃນໜ່ວຍຣາດຽນ"}, "IMCONJUGATE": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າກະຈາຍ​ຮູບ​ຂອງຈຳນວນຊັບຊ້ອນ"}, "IMCOS": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າໂກຊິນຂອງຈຳນວນຊັບຊ້ອນ"}, "IMCOSH": {"a": "(inumber)", "d": "ຕອບໄຮເປີບໍລິກໂກຊິນຂອງຈຳນວນຊັບຊ້ອນ"}, "IMCOT": {"a": "(inumber)", "d": "ຕອບໂຄແທນເຈນຂອງຈຳນວນຊັບຊ້ອນ"}, "IMCSC": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໂຄ​ຊີ​ແຄນ​ທ (cosecant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ"}, "IMCSCH": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກໂຄ​ຊີ​ແຄນ​ທ (hyperbolic cosecant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "ສົ່ງຄືນຜົນຫານລະຫວ່າງຈຳນວນຊັບຊ້ອນສອງຈຳນວນ"}, "IMEXP": {"a": "(inumber)", "d": "ເລກກຳລັງຂອງຈຳນວນຊັບຊ້ອນ"}, "IMLN": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າໂລກາລິດທຳມະຊາດຂອງຈຳນວນຊັບຊ້ອນ"}, "IMLOG10": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າໂລກາລິດພື້ນສິບ ຂອງຈຳນນວນຊັບຊ້ອນ"}, "IMLOG2": {"a": "(inumber)", "d": "ສົ່ງຄ່າໂລກາລິດພື້ນສອງ ຂອງຈຳນວນ ຊັບຊ້ອນ"}, "IMPOWER": {"a": "(inumber; number)", "d": "ສົ່ງຄືນຈຳນວນຊັບຊ້ອນຂຶ້ນກຳລັງທີ່ມີເລກຂຶ້ນກຳລັງເປັນຈຳນວນເຕັມ"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "ສົ່ງຄືນຜົນຄູນຂອງຈຳນວນຊັບຊ້ອນຕັ້ງແຕ່ 1 ຫາ 255 ຈຳນວນ"}, "IMREAL": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດຂອງສ່ວນຈິງຂອງຈຳນວນຊັບຊ້ອນ"}, "IMSEC": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ຊີ​ແຄນ​ທ (secant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ"}, "IMSECH": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກຊີ​ແຄນ​ທ (hyperbolic secant) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ"}, "IMSIN": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າຊິນຂອງຈຳນວນຊັບຊ້ອນ"}, "IMSINH": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກຊີນ (hyperbolic sine) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ"}, "IMSQRT": {"a": "(inumber)", "d": "ສົ່ງຄືນຄ່າຮາກຂັ້ນສອງຂອງຈຳນວນຊັບຊ້ອນ"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "ສົ່ງຄືນຄ່າແຕກຕ່າງລະຫວ່າງຈຳນວນຊັບຊ້ອນສອງຈຳນວນ"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "ສົ່ງຄືນຜົນລວມຂອງຈຳນວນຊັບຊ້ອນ"}, "IMTAN": {"a": "(inumber)", "d": "ສົ່ງ​ຄືນ​ແທນ​ເຈນ (tangent) ຂອງ​ຈຳນວນ​ຊັບຊ້ອນ"}, "OCT2BIN": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານແປດເປັນເລກຖານສອງ"}, "OCT2DEC": {"a": "(number)", "d": "ປ່ຽນ​ເລກຖານແປດເປັນເລກຖານສິບ"}, "OCT2HEX": {"a": "(number; [places])", "d": "ປ່ຽນ​ເລກຖານແປດເປັນເລກຖານສິບຫົກ"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "ຄ່າສະເລ່ຍໃນຖັນທີ່ຢູ່ໃນລາຍການ ຫຼືຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "ນັບຈຳນວນຫ້ອງທີ່ບັນຈຸຕົວເລກໃນຂົງເຂດ (ຖັນ) ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ສົມທຽບກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "ນັບຫ້ອງທີ່ບໍ່ວ່າງໃນຂົງເຂດ (ຖັນ)ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ"}, "DGET": {"a": "(database; field; criteria)", "d": "ແຍກການບັນທຶກອັນດຽວອອກທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸຈາກຖານຂໍ້ມູນ"}, "DMAX": {"a": "(database; field; criteria)", "d": "ສົ່ງຄືນຄ່າຕົວເລກທີ່ມີຄ່າໃຫຍ່ທີ່ສຸດໃນຂົງເຂດ (ຖັນ) ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ"}, "DMIN": {"a": "(database; field; criteria)", "d": "ສົ່ງຄືນຄ່າຕົວເລກທີ່ມີຄ່ານ້ອຍທີ່ສຸດໃນຂົງເຂດ (ຖັນ) ຂອງການບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "ຄູນຄ່າໃນຂົງເຂດ (ຖັນ) ຂອງລະບຽບໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸ"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "ຄາດຄະເນການ​ຜິດ​ບ່ຽງມາດຕະຖານໂດຍໃຊ້ຄ່າຕົວຢ່າງຈາກການເຂົ້າເຖິງຖານຂໍ້ມູນທີ່ເລືອກ"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "ຄຳນວນຫາການ​ຜິດ​ບ່ຽງມາດຕະຖານໂດຍໃຊ້ປະຊາກອນທັງໝົດ ເຊິ່ງເປັນລາຍການທີ່ຖືກເລືອກມາຈາກຖານຂໍ້ມູນ (ໂດຍໃຊ້ເງື່ອນໄຂ)"}, "DSUM": {"a": "(database; field; criteria)", "d": "ເພີ່ມຈຳນວນໃນຂົງເຂດ (ຖັນ) ຂອງການເຮັດບັນທຶກໃນຖານຂໍ້ມູນທີ່ກົງກັບເງື່ອນໄຂທີ່ທ່ານລະບຸໄວ້"}, "DVAR": {"a": "(database; field; criteria)", "d": "ຄາດຄະເນຖາານຕ່າງໆ ໃນຕົວຢ່າງຈາກການເລືອກການເຂົ້າເຖິງຖານຂໍ້ມູນ"}, "DVARP": {"a": "(database; field; criteria)", "d": "ຄຳນວນຫາຄ່າຕ່າງໆ ໂດຍໃຊ້ປະຊາກອນທັງໝົດ ເຊິ່ງເປັນລາຍການທີ່ຖືກເລືອກມາຈາກຖານຂໍ້ມູນ (ໂດຍໃຊ້ເງື່ອນໄຂ)"}, "CHAR": {"a": "(number)", "d": "ສົ່ງຄືນອັກຂະ​ລະເຊິ່ງຈະຖືກລະບຸ ໂດຍໝາຍເລກໂຄດຈາກຊຸດອັກຂະ​ລະສຳລັບເຄື່ອງຄອມພິວເຕີຂອງທ່ານ"}, "CLEAN": {"a": "(text)", "d": "ເອົາຕົວ​ອັກ​ຂະ​ລະທີ່​ບໍ່​ສາ​ມາດ​ພິມ​ໄດ້​ທັງ​ໝົດ​ອອກ​ໄປ​ຈາກ​ຂໍ້​ຄວາມ"}, "CODE": {"a": "(text)", "d": "ສະ​ແດງ​ຜົນ​ລະ​ຫັດ​ຕົວ​ເລກ​ສຳ​ລັບ​ຕົວ​ອັກ​ຂະ​ລະ​ທຳ​ອິດ​ຢູ່​ໃນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ, ຢູ່​ໃນ​ຊຸດ​ຕົວ​ອັກ​ຂະ​​ລະ​ທີ່​​ຄອມ​ພິວ​ເຕີ​ຂອງ​ທ່ານ​ນຳ​ໃຊ້"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "ເອົາ​ຫຼາຍ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ເຂົ້າ​ເປັນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ດຽວ"}, "CONCAT": {"a": "(text1; ...)", "d": "ເຊື່ອມຕໍ່ລາຍຊື່ ຫຼື ໄລຍະຂອງສະຕຣິງຂໍ້ຄວາມ"}, "DOLLAR": {"a": "(number; [decimals])", "d": "ປ່ຽນຕົວເລກຫາຂໍ້ຄວາມ, ໂດຍໃຊ້ຮູບແບບສະກຸນເງິນ"}, "EXACT": {"a": "(text1; text2)", "d": "ກວ​ດ​ເບິ່ງວ່າ ສອງ​​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ຄື​ກັນ​ແທ້​ບໍ່, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False. TRUE​ແມ່ນ​ຢຳ ນັ້ນແມ່ນໂຕພິມໃຫຍ່ນ້ອຍມີຜົນຕ່າງກັນ"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "ສົ່ງຄືນຄ່າຕຳແໜ່ງເລີ່ມຕົ້ນຂອງສາຍຂໍ້ຄວາມທີ່ຢູ່ພາຍໃນຕົວສາຍຂໍ້ຄວາມອີກອັນໜຶ່ງ. ເຊິ່ງຟັງຊັນ FIND ​ນັ້ນແມ່ນໂຕພິມໃຫຍ່ນ້ອຍມີຜົນຕ່າງກັນ"}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "ຊອກຫາ ຊຸດຕົວອັກສອນທີໄດ້ລະບຸໄວ້ ໃນຊຸດຕົວອັກສອນ ແລະ ສໍາລັບຕົວອັກສອນອ double-byte character set (DBCS) ເຊັ່ນພາສາຍີ່ປຸ່ນ, ຈີນ, ເກົາຫຼີແລະອື່ນໆ."}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "ປັດຈຳນວນເລກເສດຕາມຕຳແໜ່ງທົດສະນິຍົມທີ່ລະບຸ ແລະ ສົ່ງຄ່າຜົນຮັບກັບຄືນເປັນຂໍ້ຄວາມທີ່ມີ ຫຼືບໍ່ມີເຄື່ອງໝາຍຈຸດ"}, "LEFT": {"a": "(text; [num_chars])", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນ​ລະ​ບຸ​ໄວ້​ຂອງ​ຕົວ​ອັກ​ຂະ​ລະ​ຈາກເລີ່ມ​ຕົ້ນ​ຂອງ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ"}, "LEFTB": {"a": "(text; [num_chars])", "d": "ສະກັດຊຸດຕົວອັກສອນ ຍ່ອຍຈາກ ຊຸດຕົວອັກາອນ ທີ່ລະບຸໄວ້ເລີ່ມຕົ້ນຈາກຕົວອັກສອນຊ້າຍສຸດ ແລະມີຈຸດປະສົງ ສຳ ລັບພາສາຕ່າງໆທີ່ໃຊ້ຊຸດຕົວອັກສອນ double-byte character set (DBCS) ເຊັ່ນພາສາຍີ່ປຸ່ນ, ຈີນ, ເກົາຫຼີແລະອື່ນໆ."}, "LEN": {"a": "(text)", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນຕົວ​ອັກ​ຂະ​ລະຢູ່​ໃນ​ສະ​​ຕ​ຣິງ​ຂໍ້​ຄວາມ"}, "LENB": {"a": "(text)", "d": "ປະເມີນຊຸດຕົວອັກສອນ ທີໄດ້ລະບຸໄວ້ ແລະ ຄືນ ຕົວອັກສອນ ທີມີພາສາ double-byte character set (DBCS) like ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ."}, "LOWER": {"a": "(text)", "d": "ປ່ຽນ​ທຸກ​ຕົວ​ອັກ​ສອນ​ຢູ່​ໃນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ເປັນ​ຕົວ​ພິມ​ນ້ອຍ"}, "MID": {"a": "(text; start_num; num_chars)", "d": "ສະ​ແດງ​ຜົນຕົວ​ອັກ​ຂະ​ລະຈາກ​ໃຈ​ກາງ​ຂອງ​ສະ​ຕ​ຣິງ, ໂດຍ​ທີ່​ມີ​ຕຳ​ແໜ່ງ​ເລີ່ມ​ຕົ້ນ ແລະ​ຄວາມ​ຍາວ"}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "ດືງເອົາຕົວອັກສອນທີໄດ້ລະບຸໄວ້ ທີເລີ່ມຈາກ ຈຸດໃດໜື່ງ ແລະ ເພື່ອນໍາໃຊ້ ຊຸດພາສາ double-byte (DBCS) ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ."}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "ປ່ຽນ​ຂໍ້ຄວາມ​ເປັນ​ຕົວ​ເລກ​ໃນ​ຮູບ​ແບບ​ທີ່​ເປັນ​ອິດສະຫຼະຈາກ​ລັກສະນະ​ທ້ອງ​ຖິ່ນ"}, "PROPER": {"a": "(text)", "d": "ປ່ຽນ​ສະຕຣິງຂໍ້ຄວາມ​ເປັນ​ຕົວ​ພິມ​ທີ່​ເໝາະ​ສົມ; ຕົວ​ອັກສອນ​ທຳ​ອິດ​ໃນ​ແຕ່ລະ​ຄໍາ​ສັບ​ເປັນ​ຕົວ​ພິມ​ໃຫຍ່ ​ແລະ​ຕົວ​ອັກສອນ​ອື່ນ​ທັງ​ໝົດ​ເປັນ​ຕົວ​ພິມ​ນ້ອຍ"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "ແທນທີ່ບາງສ່ວນຂອງສາຍຂໍ້ຄວາມດ້ວຍສາຍຂໍ້ຄວາມອື່ນ"}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "ປ່ຽນແທນ ຊຸດຕົວອັກສອນ, ທີໄດ້ລະບຸ ຈໍານວນຕົວອັກສອນ ແລະ ຈຸດເລີ່ມຕົ້ນ, ປ່ຽນເປັນ ຊຸດຕົວອັກສອນໃໝ່ ເພື່ອນໍາໃຊ້ ຊຸດພາສາ double-byte (DBCS) ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ."}, "REPT": {"a": "(text; number_times)", "d": "​ຊ້ຳ​ຄືນ​ຂໍ້​ຄວາມ​ຈຳ​ນວນ​ຄັ້ງ​ທີ່​ໄດ້​ໃຫ້. ໃຊ້​ຊ້ຳ​ຄືນ​ເພື່ອ​ຕື່ມ​ເຊວ​ດ້ວຍ​ຈຳ​ນວນ​ຕົວ​ຢ່າງ​ຂອງ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ"}, "RIGHT": {"a": "(text; [num_chars])", "d": "ສະ​ແດງ​ຜົນ​ຈຳ​ນວນ​ລະ​ບຸ​ໄວ້​ຂອງ​ຕົວ​ອັກ​ຂະ​ລະ​ຈາກ​ທ້າຍ​ຂອງ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ"}, "RIGHTB": {"a": "(text; [num_chars])", "d": "ດືງເອົາຕົວອັກສອນທີໄດ້ລະບຸໄວ້ ທີເລີ່ມຈາກ ຂວາສຸດ ແລະ ເພື່ອນໍາໃຊ້ ຊຸດພາສາ double-byte (DBCS) ເປັນຕົ້ນ ຈີນ, ຢີປຸ່ນ ເກົາຫຼີ ແລະ ອື່ນໆ."}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "ສົ່ງຄືນໝາຍ​ເລກ​ອັກຂະ​ລະທີ່​ອັກ​ຂະ​ລະຖືກຄົ້ນພົບເປັນອັນດັບທຳອິດໃນສາຍອັກຂະ​ລະຫຼືຂໍ້ຄວາມສະ​ເພາະ, ການຄົ້ນຫາຈະຖືກເຮັດຈາກຊ້າຍໄປຂວາ (ໂຕພິມໃຫຍ່ນ້ອຍບໍ່ມີຜົນຕ່າງກັນ)"}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "ຄືນ ສະຖານທີ ຂອງຊຸດອັກສອນຍ່ອຍ ທີໄດ້ລະບຸໄວ້ ຈາກຊຸດຕົວອກາອນຫຼັກ. ແລະ ສໍາລັບຕົວອັກສອນທີເປັນ double-byte character set (DBCS) ເຊັ່ນພາສາຍີ່ປຸ່ນ, ຈີນ, ເກົາຫຼີແລະອື່ນໆ."}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "ປ່ຽນ​ແທນ​ຂໍ້​ຄວາມ​ທີ່​ມີ​ຢູ່​ດ້ວຍ​ຂໍ້​ຄວາມ​ໃໝ່​ຢູ່​ໃນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ"}, "T": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ຄ່າ​ແມ່ນ​ຂໍ້​ຄວາມ​ບໍ, ແລະ​ສະ​ແດງ​ຜົນ​ຂໍ້​ຄວາມ​ຖ້າ​ມັນ​ແມ່ນ, ຫຼື​ສະ​ແດງ​ຜົນ​ວົງ​ຢືມ​ຄູ່ (ຂໍ້​ຄວາມ​ຫວ່າງ​ເປົ່າ) ຖ້າ​ມັນ​ບໍ່​ແມ່ນ"}, "TEXT": {"a": "(value; format_text)", "d": "ປ່ຽນຄ່າເປັນຂໍ້ຄວາມຢູ່ໃນຮູບແບບຕົວເລກສະເພາະ"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "ເຊື່ອມຕໍ່ລາຍຊື່ ຫຼື ໄລຍະຂອງສະຕຣິງຂໍ້ຄວາມໂດຍໃຊ້ເຄື່ອງໝາຍຂັ້ນ"}, "TRIM": {"a": "(text)", "d": "ເອົາ​ທຸກ​ບ່ອນ​ຍະ​ຫວ່າງ​ອອກ​ໄປ​ຈາກ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ ຍົກ​ເວັ້ນ​ສຳ​ລັບ​ບ່ອນ​ຍະ​ຫວ່າງ​ລະ​ຫວ່າງ​ຄຳ​ເວົ້າ"}, "UNICHAR": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ອັກຂະ​ລະ Unicode ທີ່​ອ້າງ​ອີງ​​ໂດຍ​ຄ່າຕົວ​ເລກ​ທີ່​ໃຫ້​ໄວ້"}, "UNICODE": {"a": "(text)", "d": "ສົ່ງ​ຄືນ​ຕົວ​ເລກ (ຈຸດ​ລະຫັດ) ທີ່​ກົງ​ກັບ​ອັກຂະ​ລະ​ທຳ​ອິດ​ຂອງ​ຂໍ້ຄວາມ"}, "UPPER": {"a": "(text)", "d": "ປ່ຽນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ເປັນ​ຕົວ​ພິມ​ໃຫຍ່​ທັງ​ໝົດ"}, "VALUE": {"a": "(text)", "d": "ປ່ຽນ​ສະ​ຕ​ຣິງ​ຂໍ້​ຄວາມ​ທີ່​ແທນ​ຕົວ​ເລກ​ເປັນ​ຕົວ​ເລກ"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍຂອງການຜິດ​ບ່ຽງສົມບູນຂອງຈຸດຂໍ້ມູນຈາກຄ່າສະເລ່ຍຂໍ້ມູນທັງໝົດ. ຂໍ້ພິສູດທີ່ລະບຸສາມາດເປັນໄດ້ທັງຕົວເລກ ຫຼື ຊື່, ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຕົວເລກ"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍ (ຄ່າສະເລ່ຍເລກຄະນິດ) ຂອງຂໍ້ພິສູດທັງໝົດ, ເຊິ່ງສາມາດເປັນຕົວເລກ ຫຼື ຊື່, ອາ​ເຣຍ໌, ຫຼືການອ້າງອີງທີ່ມີຕົວ​ເລກຢູ່ນຳ"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "ສະ​ແດງ​ຜົນສະ​ເລ່ຍ (ຄ່າ​ສະ​ເລ່ຍເລກ​ຄະ​ນິດ) ຂອງ​​ຂໍ້​ພິ​ສູດ, ຂໍ້​ຄວາມ​ການ​ປະ​ເມີນ ແລະ False ຢູ່​ໃນ​​ຂໍ້​ພິ​ສູດ​ເປັນ 0; ການ​ປະ​ເມີນ​ TRUE ເປັນ 1. ​ຂໍ້​ພິ​ສູດສາ​ມາດ​ເປັນ​ຕົວ​ເລກ, ຊື່, ແຖວ​ລຳ​ດັບ, ຫຼື​ການ​ອ້າງ​ອີງ"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "ຫາຄ່າສະເລ່ຍ (ເລຂາຄະນິດ) ສຳລັບຫ້ອງທີ່ຖືກລະບຸ ໂດຍຊຸດຂອງເງື່ອນໄຂ ຫຼືເກນການຕັດສິນທີ່ກຳນົດໃຫ້"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": " ຫາຄ່າສະເລ່ຍ (ເລຂາຄະນິດ) ສຳລັບຫ້ອງທີ່ຖືກລະບຸ ໂດຍຊຸດຂອງເງື່ອນໄຂ ຫຼືເກນການຕັດສິນທີ່ກໍານົດໃຫ້"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນຂອງ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ​ເບ​ຕ້າ​ສະ​ສົມ"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ຟັງ​ຊັນ​ຄວວາ​ມໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​​ໄປ​ໄດ້​ແບບ​ເບ​ຕ້າ​ສະ​ສົມ (BETA.DIST)"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຟັງ​ຊັນ​ການ​​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​​ໄດ້​ແບບ​ເບ​ຕ້າ"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ຟັງ​ຊັນ​ຄວວາ​ມໜາ​ແໜ້ນ​ຂອງ​ຄວາມ​ເປັນ​​ໄປ​ໄດ້​ແບບ​ເບ​ຕ້າ​ສະ​ສົມ (BETA.DIST)"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "ສົ່ງ​ຄືນຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ທະວີ​ນາມ​ສໍາລັບ​ແຕ່​ລະ​ຊຸດ​ຂອງ​ຜົນ​ການ​ທົດ​ລອງ"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "ສົ່ງຄືນຄ່າຄວາມເປັນໄປໄດ້ ຂອງການແຈກຢາຍທາງຄະນິດສາດສໍາລັບແຕ່ລະຊຸດຂອງຜົນການທົດລອງ"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ຂອງ​ຜົນ​ການ​ທົດ​ລອງ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ​ທະວີ​ນາມ"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "ສະ​ແດງ​ຜົນ​ຄ່າ​ນ້ອຍ​ສຸດ​ສຳ​ລັບ​ອັນ​ທີ່​ການ​ກະ​ຈາຍ​ໄບ​ນໍ​ມຽວ​ສະ​ນົມ​ໃຫຍ່ກວ່າ ຫຼື​ເທົ່າ​ກັບ​ຄ່າ​ມາດ​ຖານ"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້​​ດ້ານ​ຂວາ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຄ-ສະ​ແຄຣ"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "nສົ່ງ​ຄືນຄ່າປິ້ນຄືນຂອງຄວາມເປັນໄປໄດ້​ເບື້ອງຂວາຂອງການແຈກຢາຍແບບໄຄ-ສະແຄຣ"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "nສົ່ງຄືນຄ່າການທົດສອບຄວາມເປັນອິດສະຫຼະ: ຄ່າຄວາມເປັນໄປໄດ້ຈາກການແຈກຢາຍແບບໄຊ-ສະແຄ (chi-squared) ຂອງສະຖີຕິໄຊ-ສະແຄ ແລະອົງສາຄວາມເປັນອິດສະຫຼະທີ່ເໝາະສົມ "}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້​​ດ້ານຊ້າຍ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຄ-ສະ​ແຄຣ"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາ​ມ​ເປັນ​ໄປ​ໄດ້​​ດ້ານ​ຂວາ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຄ-ສະ​ແຄຣ"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນຄ່າປື້ນຄືນຂອງຄວາມເປັນໄປໄດ້​ເບື້ອງຊ້າຍຂອງການແຈກຢາຍແບບໄຄ-ສະແຄຣ"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນຄ່າປິ້ນຄືນຂອງຄວາມເປັນໄປໄດ້​ເບື້ອງຂວາຂອງການແຈກຢາຍແບບໄຄ-ສະແຄຣ"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "ສົ່ງຄືນຄ່າການທົດສອບຄວາມເປັນອິດສະຫຼະ: ຄ່າຄວາມເປັນໄປໄດ້ຈາກການແຈກຢາຍແບບໄຊ-ສະແຄ (chi-squared) ຂອງສະຖີຕິໄຊ-ສະແຄ ແລະອົງສາຄວາມເປັນອິດສະຫຼະທີ່ເໝາະສົມ "}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "ສົ່ງ​ຄືນຄ່າ​ຊ່ວງ​​ຄວາມ​ເຊື່ອ​ໝັ້ນຂອງ​ຄ່າ​ສະ​ເລ່ຍປະຊາກອນ ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "ສົ່ງ​ຄືນຄ່າ​ຊ່ວງ​​ຄວາມ​ເຊື່ອ​ໝັ້ນຂອງ​ຄ່າ​ສະ​ເລ່ຍປະຊາກອນ ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "ສົ່ງ​ຄືນຄ່າ​ຊ່ວງ​​ຄວາມ​ເຊື່ອ​ໝັ້ນຂອງ​ຄ່າ​ສະ​ເລ່ຍປະຊາກອນ ​ໂດຍ​ໃຊ້​ການ​ແຈກ​ຢາຍ T ຂອງ​ນັກຮຽນ"}, "CORREL": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດສຳພັນລະຫວ່າງຊຸດຂໍ້ມູນ 2 ຊຸດຂໍ້ມູນ"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "ນັບ​ຈຳ​ນວນ​ຂອງ​ເຊວ​ທີ່​ຢູ່​ໃນ​ຂອບ​ເຂດ​ທີ່​ມີ​ຕົວ​ເລກ"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "ນັບ​ຈຳ​ນວນ​ຂອງ​ເຊວ​ຢູ່​ໃນ​ຂອບ​ເຂດ​ທີ່​ບໍ່​ຫວ່າງ​ເປົ່າ"}, "COUNTBLANK": {"a": "(range)", "d": "ນັບຈຳນວນຫ້ອງທີ່ຫວ່າງໃນຊ່ວງຂອງຫ້ອງທີ່ລະບຸ"}, "COUNTIF": {"a": "(range; criteria)", "d": "ນັບຈຳນວນຂອງຫ້ອງພາຍໃນຊ່ວງທີ່ກົງຕາມເງື່ອນໄຂທີ່ທ່ານລະບຸ"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "ນັບຈຳນວນຂອງຫ້ອງທີ່ຖືກລະບຸໂດຍຊຸດຂອງເງື່ອນໄຂ ຫຼືເກນຕັດສິນທີ່ກຳນົດໃຫ້"}, "COVAR": {"a": "(array1; array2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງຮ່ວມ​ ​ເຊິ່ງ​ເປັນ​ຄ່າ​ສະ​ເລ່ຍຂອງ​ຜົນ​ຄູນ​ຂອງ​ສ່ວນ​ຜິດ​ບ່ຽງສໍາລັບຄູ່​ຈຸດ​ຂໍ້​ມູນ​ແຕ່​ລະ​ຄູ່​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ສອງ​ຊຸດ"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງຮ່ວມ​ຂອງ​ປະຊາກອນ ​ເຊິ່ງ​ເປັນ​ຄ່າ​ສະ​ເລ່ຍຂອງ​ຜົນ​ຄູນ​ຂອງ​ສ່ວນ​ຜິດ​ບ່ຽງສໍາລັບຄູ່​ຈຸດ​ຂໍ້​ມູນ​ແຕ່​ລະ​ຄູ່​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ສອງ​ຊຸດ"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງຮ່ວມ​ຂອງ​ຕົວຢ່າງ ​ເຊິ່ງ​ເປັນ​ຄ່າ​ສະ​ເລ່ຍຂອງ​ຜົນ​ຄູນ​ຂອງ​ສ່ວນ​ຜິດ​ບ່ຽງສໍາລັບຄູ່​ຈຸດ​ຂໍ້​ມູນ​ແຕ່​ລະ​ຄູ່​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ສອງ​ຊຸດ"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ນ້ອຍ​ສຸດ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ​ທະ​ວີນາ​ມສະ​ສົມທີ່​ມີຄ່າ​ຫຼາຍກວ່າ ຫຼື​ເທົ່າ​ກັບ​ຄ່າ​ເກນ​ເງື່ອນ​ໄຂ"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າຜົນລວມຂອງສ່ວນຜິດ​ບ່ຽງຂອງຈຸດຂໍ້ມູນຈາກຄ່າສະເລ່ຍຕົວຢ່າງຂຶ້ນກຳລັງສອງ"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ exponential"}, "EXPON.DIST": {"a": "(x; known_y's; known_x's)", "d": "ຄິດ​ໄລ່, ຫຼື​ເດົາ, ຄ່າອະ​ນາ​ຄົດ​ຕາມ​ແນວ​ໂນ້ມ​ຕາມ​ເສັ້ນ​ໂດຍ​ການ​ໃຊ້​ຄ່າ​ທີ່​ມີ​ຢູ່"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (ດ້ານ​ຂວາ) (ລະດັບ​ຄວາມ​ໜາ​ແໜ້ນ) ສໍາລັບ​ສອງ​ຊຸດ​ຂໍ້​ມູນ"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (​ເບື້ອງ​ຂວາ): ຖ້າ p = F.DIST(x,...) ​ແລ້ວ F.INV(p,...) = x"}, "FTEST": {"a": "(array1; array2)", "d": "ຕອບຜົນຂອງ F-test ເຊິ່ງເປັນຄ່າຄວາມເປັນໄປໄດ້ແບບສອງທາງ ທີ່ຄວາມແປຜັນໃນ Array1 ແລະ Array2 ບໍ່ແຕກຕ່າງກັນຫຼາຍ"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (ດ້ານ​ຂວາ) (ລະດັບ​ຄວາມ​ໜາ​ແໜ້ນ) ສໍາລັບ​ສອງ​ຊຸດ​ຂໍ້​ມູນ"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (ດ້ານ​ຂວາ) (ລະດັບ​ຄວາມ​ໜາ​ແໜ້ນ) ສໍາລັບ​ສອງ​ຊຸດ​ຂໍ້​ມູນ"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (​ເບື້ອງ​ຊ້າຍ): ຖ້າ p = F.DIST(x,...) ​ແລ້ວ F.INV(p,...) = x"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ແບບ F (​ເບື້ອງ​ຂວາ): ຖ້າ p = F.DIST(x,...) ​ແລ້ວ F.INV(p,...) = x"}, "F.TEST": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຄ່າຜົນຮັບຂອງ F-test,ເຊິ່ງເປັນຄ່າຄວາມເປັນໄປໄດ້ແບບສອງທາງ (two-tailed) ເຊິ່ງເປັນຜົນມາຈາກການທົດລອງທີ່ມີຄວາມຜັນແປ ຂອງອາ​ເຣຍ໌1 ແລະອາ​ເຣຍ໌2 ບໍ່ແຕກຕ່າງກັນຫຼາຍ"}, "FISHER": {"a": "(x)", "d": "ສະ​ແດງ​ຜົນການ​ແປງ​ຄ່າ Fisher"}, "FISHERINV": {"a": "(y)", "d": "ສະ​ແດງ​ຜົນປີ້ນ​ກັບ​ຂອງ​ການ​ແປງ Fisher: ຖ້າ y = FISHER(x), ຈາກນັ້ນ FISHERINV(y) = x"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "ຄໍານວນ, ຫຼືຄາດ​ເດົາ, ຄ່າ​ໃນ​ອະນາຄົດ​ຕາມ​ແນວ​ໂນ້ມ​​ເສັ້ນຊື່ ​ໂດຍ​ການ​ໃຊ້​ຄ່າ​ທີ່ມີ​ຢູ່"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຄາດ​ການ​ໄວ້​ສຳ​ລັບ​ວັນ​ທີ​ເປົ້າ​ໝາຍ​ອະ​ນາ​ຄົດ​ສະ​ເພາະ​ດ້ວຍ​ການ​ໃຊ້​ວິ​ທີ​ການ​ເຮັດ​ໃຫ້​ລະ​ອຽດ​ເອັກ​ໂປ​ເນັນ​ຊຽວ."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "ຜົນ​ໄດ້​ຮັບ​ຊ່ວງ​ຄວາມ​ເຊື່ອ​ໝັ້ນ​ສໍາລັບຄ່າ​ທີ່​ຄາດ​ໄວ້​ລ່ວງ​ໜ້າ​ໃນ​ວັນ​ທີ​ເປົ້າ​ໝາຍ​ທີ່​ລະບຸ​ໄວ້."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "ສົ່ງ​​ຄືນ​ຄວາມ​ຍາວ​ຂອງ​ຮູບ​ແບບ​ຊ້ຳກວດ​ຫາ​ຊຸດ​ເວ​ລາ​ສະ​ເພາະ."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "ສົ່ງ​ຄືນ​ສະ​ຖິ​ຕິ​ສະ​ເໜີ​ສຳ​ລັບ​ການ​ຄາດ​ການ."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "ຄໍານວນ, ຫຼືຄາດ​ເດົາ, ຄ່າ​ໃນ​ອະນາຄົດ​ຕາມ​ແນວ​ໂນ້ມ​​ເສັ້ນຊື່ ​ໂດຍ​ການ​ໃຊ້​ຄ່າ​ທີ່ມີ​ຢູ່"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "ຄຳນວນຫາຈຳນວນເທື່ອທີ່ເກີດຂຶ້ນຂອງຄ່າພາຍໃນຊ່ວງຂອງຄ່າທີ່ລະບຸ ຈາກນັ້ນສົ່ງຄືນອະເຣໃນແນວຕັ້ງ ເຊິ່ງມີຈຳນວນຂໍ້ມູນຫຼາຍກວ່າ ຈຳນວນຂໍ້ມູນຂອງ Bins_array ຢູ່ 1"}, "GAMMA": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າຟັງ​ຊັນ​ແມ​ມາ"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແກມ​ມາ"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແກມ​ມາ"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາສະ​ສົມ: ຖ້າ​ຄ່າ p = GAMMA.DIST(x,...) แล้วค่า GAMMA.INV(p,...) ຈະ = x"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ແກມ​ມາສະ​ສົມ: ຖ້າ​ຄ່າ p = GAMMA.DIST(x,...) แล้วค่า GAMMA.INV(p,...) ຈະ = x"}, "GAMMALN": {"a": "(x)", "d": "ສະ​ແດງ​ຜົນໂລ​ກາ​ລິດ​ທຳ​ມະ​ຊາດ​ຂອງ​ຟັງ​ຄ໌​ຊັນ​ແກມ​ມາ"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ໂລກາ​ລິດ​ທໍາ​ມະ​ຊາດ​ຂອງ​ຟັງ​ຊັນ​ແກມມາ"}, "GAUSS": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ທີ່​ນ້ອຍ​ກວ່າ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມ​ປົກກະຕິ​ມາດຕະຖານ 0.5"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍເລຂາຄະນິດຂອງອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຂໍ້ມູນທີ່ເປັນຕົວເລກບວກ"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "ຜົນ​ໄດ້​ຮັບຕົວເລກທີ່ປະກອບຢູ່ໃນແນວໂນ້ມການເຕີບໂຕທາງເລກກຳລັງທີ່ກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍຮາໂມນິກ (harmonic - H.M.)ຂອງຊຸດຂໍ້ມູນຈຳນວນບວກ:ສ່ວນກັບຂອງຄ່າສະເລ່ຍເລກຄະນິດທີ່ມີສູດເປັນສ່ວນກັບ"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຮ​ເປີ​ຢີ​ໂອ​ເມ​ຕຣິກ"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ​ໄຮ​ເປີ​ຢີ​ໂອ​ເມ​ຕຣິກ"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "ຄຳນວນຫາຈຸດທີ່ເສັ້ນຈະຕັດແກນ y ໂດຍການໃຊ້ເສັ້ນຖົດ​ຖອຍ​ທີ່​ພໍ​ດີ​ທີ່​ສຸດ​ທີ່​ໄດ້​ແຕ້ມ​ລົງຜ່ານຄ່າ x ແລະ y ທີ່ຮູ້ຢູ່ແລ້ວ"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າເຄີໂຕສີດສຂອງຊຸດຂໍ້ມູນ"}, "LARGE": {"a": "(array; k)", "d": "ສົ່ງຄືນຄ່າຫຼາຍທີ່ສຸດໃນລຳດັບທີ K ຂອງຊຸດຂໍ້ມູນ. ຕົວຢ່າງເຊັ່ນ, ຕົວເລກທີ່ຫຼາຍທີ່ສຸດ​ໃນອັນດັບທີ 5 ຂອງຊຸດຕົວເລກ"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "ຜົນ​ໄດ້​ຮັບຄ່າສະຖິຕິເຊິ່ງຈະບອກລັກສະນະແນວໂນ້ມເສັ້ນຊື່ທີ່ກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ,ໂດຍການປັບໃຫ້ເປັນເສັ້ນຊື່ພໍດີຈາກການນຳໃຊ້ວິທີກຳລັງສອງນ້ອຍສຸດ"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "ຜົນ​ໄດ້​ຮັບຄ່າສະຖິຕິເຊິ່ງຈະບອກລັກສະນະເສັ້ນ​ໂຄ້ງ​ເອັກ​ໂປ​ເນັນ​ຊຽ​ລທີ່ກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ຟັງ​ຊັນ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມ​ແບບ lognormal ຂອງ x ​ໂດຍ​ທີ່ ln(x) ​ເປັນ​ການ​ແຈ​ກຢາຍ​ແບບ​ປົກກະຕິ​ທີ່​ມີ​ພາຣາມິເຕີ Mean และ Standard_dev"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈ​ກຢາຍ​ແບບ lognormal ສະ​ສົມ​ຂອງ x ​ໂດຍ​ທີ່ ln(x) ​ແມ່ນ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ ທີ່​ໃຊ້​ຄ່າ​ພາຣາມິເຕີ Mean ​ແລະ Standard_dev"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງຄືນຄ່າປີ້ນຄືນຂອງຟັງຊັນການແຈກຢາຍສະສົມແບບລ໋ອກນໍມັນຂອງ x, ໂດຍທີ່ ln(x) ເປັນການແຈກຢາຍແບບປົກກະຕິທີ່ໃຊ້ພາຣາມິເຕີເປັນຕົວກາງ ແລະ Standard_dev"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈ​ກຢາຍ​ແບບ lognormal ສະ​ສົມ​ຂອງ x ​ໂດຍ​ທີ່ ln(x) ​ແມ່ນ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ ທີ່​ໃຊ້​ຄ່າ​ພາຣາມິເຕີ Mean ​ແລະ Standard_dev"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າທີ່ຫຼາຍທີ່ສຸດໃນຊຸດຂອງຄ່າທີ່ລະບຸ. ຍົກເວັ້ນຄ່າຄວາມ​ຈິງ ແລະ ຂໍ້ຄວາມ"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "ສະ​ແດງ​ຜົນຄ່າໃຫຍ່​ສຸດ​ຢູ່​ໃນ​ກຸ່ມ​ຂອງຄ່າ. ບໍ່​ປະ​ຕິ​ເສດຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ ແລະ​ຂໍ້​ຄວາມ"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "ຕອບຄ່າສູງສຸດໃນຈຳນວນຕາລາງທີ່ລະບຸໂດຍຈຳນວນເງື່ອນໄຂ ຫຼືເກນກຳນົດທີ່ເຈາະຈົງໃຫ້"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າກາງ, ຫຼືຈຳນວນທີ່ຢູ່ເຄິງກາງຂອງຊຸດຈຳນວນທີ່ລະບຸ"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຈຳນວນທີ່ນ້ອຍທີ່ສຸດໃນຊຸດຂອງຄ່າທີ່ລະບຸ. ຍົກເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມ"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "ສະ​ແດງ​ຜົນຄ່ານ້ອຍ​ສຸດ​ຢູ່​ໃນ​ກຸ່ມ​ຂອງຄ່າ. ບໍ່​ປະ​ຕິ​ເສດຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ ແລະ​ຂໍ້​ຄວາມ"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "ຕອບຄ່າຕໍ່າສຸດໃນຈຳນວນຕາລາງທີ່ລະບຸໂດຍຈຳນວນເງື່ອນໄຂ ຫຼືເກນກຳນົດທີ່ເຈາະຈົງໃຫ້"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ທີ່​ເກີດ​ຂຶ້ນ ຫຼືຊໍ້າ​ກັນ​ເລື້ອຍໆ​ທີ່​ສຸດ​ໃນ​ອາ​ເຣຍ໌ ຫຼືຊ່ອງ​ຂອງ​ຂໍ້​ມູນ"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງ​ຄືນ​ອາ​ເຣຍ໌​ໃນ​ແນວ​ຕັ້ງ​ຂອງ​ຄ່າ​ໃນ​ອາ​ເຣຍ໌ ຫຼື​ໃນ​ຊ່ວງ​ຂອງ​ຂໍ້​ມູນ​ທີ່​ເກີດ​ຂຶ້ນ ຫຼືຊໍ້າ​ກັນ​ເລື້ອຍໆ​ທີ່​ສຸດ.  ສໍາລັບ​ອາ​​ເຣຍ໌​ແນວ​ນອນ ​ໃຫ້​ໃຊ້ =TRANSPOSE(MODE.MULT(number1,number2,...))"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າທີ່ເກີດຂຶ້ນເລື້ອຍໆ ຫຼື ເຮັດຊໍ້າຫຼາຍທີ່ສຸດໃນອາ​ເຣຍ໌ ຫຼືຊ່ວງຂອງຂໍ້ມູນ"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ​ທະວີ​ນາມ​ລົບ (negative binomial distribution) ​ເຊິ່ງ​ເປັນ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈະມີ​ຄວາມ​ລົ້ມ​ເຫຼວ Number_f ຄັ້ງ ​ເກີດ​ຂຶ້ນ​ກ່ອນ​ທີ່​ຈະ​ເກີດ​ຄວາມ​ສໍາ​ເລັດ​ຄັ້ງ​ທີ Number_s, ​ໂດຍ​ຄ່າ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ຈະ​ສໍາ​ເລັດ​ໃນ​ແຕ່​ລະກາ​ນທົດລອງ​ເທົ່າ​ກັບ Probability_s "}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "ຕອບຄ່າການແຈກຢາຍແບບທະວິນາມລົບເຊິ່ງເປັນຄ່າຄວາມເປັນໄປໄດ້ທີ່ຈະມີຄວາມລົ້ມເຫຼວ Number_f ຄັ້ງ ເກີດຂຶ້ນກ່ອນທີ່ຈະເກີດຄວາມສໍາເລັດຄັ້ງທີ Number_s, ໂດຍຄ່າຄວາມເປັນໄປໄດ້ທີ່ຈະສໍາເລັດໃນແຕ່ລະການທົດລອງເທົ່າກັບ Probability_s "}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ​ສໍາລັບ​ສ່ວນ​ຜິດ​ບ່ຽງ​ສະ​ເລ່ຍ ​ແລະ​ມາດຕະຖານ"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​ສະ​ສົມປົກກະຕິ​ສໍາລັບ​ສ່ວນ​ຜິດ​ບ່ຽງ​ສະ​ເລ່ຍ ​ແລະ​ມາດຕະຖານ"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງຄືນຄ່າການປີ້ນຄືນຂອງການແຈກຢາຍສະ​ສົມແບບປົກກະຕິ (normal cumulative distribution) ສຳລັບຄ່າສະເລ່ຍ ແລະ ສ່ວນຫາຄ່າມາດຕະຖານທີ່ລະບຸ"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "ສົ່ງ​ກັບ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ​ສໍາລັບ​ຄ່າ​ສະ​ເລ່ຍ ​ແລະ​ສ່ວນ​ຜິ​ດບ່ຽງ​ມາດຕະຖານ​ທີ່​ລະ​ບຸ"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ​ປົກກະຕິ (ມີຄ່າ​ສະ​ເລ່ຍ​ເທົ່າ​ສູນ​ ​ແລະຄ່າ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ເທົ່າ​ໜຶ່ງ)"}, "NORMSDIST": {"a": "(z)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ​ສະ​ສົມ​ປົກກະຕິ (ມີຄ່າ​ສະ​ເລ່ຍ​ເທົ່າ​ສູນ​ ​ແລະຄ່າ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ເທົ່າ​ໜຶ່ງ)"}, "NORM.S.INV": {"a": "(probability)", "d": "ສົ່ງຄືນຄ່າປີ້ນຄືນຂອງການແຈກຢາຍສະ​ສົມແບບປົກກະຕິມາດຕະຖານ (ມີຄ່າສະເລ່ຍເທົ່າກັບສູນ ແລະຄ່າ​ຜິດ​ບ່ຽງມາດຕະຖານເທົ່າກັບໜຶ່ງ)"}, "NORMSINV": {"a": "(probability)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ​ປົກກະຕິ​ມາດຕະຖານ​ສະ​ສົມ (ມີຄ່າ​ສ​ະ​ເລ່ຍ​ເທົ່າ​ສູນ ​ແລະ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ເທົ່າ​ກັບ​ໜຶ່ງ)"}, "PEARSON": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຄ່າສຳປະສິດສຳພັນຂອງຜະລິດຕະພັນເພຍສັນ (Pearson product moment correlation) ຫຼື ຄ່າ r"}, "PERCENTILE": {"a": "(array; k)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເປີ​ຊັນ​ອັນ​ດັບ​ທີ k ​ໃນ​ຊ່ວງ​ໃດ​ໜຶ່ງ"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເປີ​ຊັນ​ອັນ​ດັບ​ທີ k ​ໃນ​ຊ່ວງ ບ່ອນ​ທີ່ k ​ແມ່ນ​ຢູ່​ຊ່ວງ 0..1, ສະ​ເພາະ​ເທົ່າ​ນັ້ນ"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ເປີ​ຊັນ​ອັນ​ດັບ​ທີ k ​ໃນ​ຊ່ວງ ບ່ອນ​ທີ່ k ​ແມ່ນ​ຢູ່​ຊ່ວງ 0..1, ສະ​ເພາະ​ເທົ່າ​ນັ້ນ"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​ເຊັນ"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​ເຊັນ​ຂອງຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​​ເຊັນ (0..1, ສ​ະ​ເພາະ)ຂອງ​ຊຸດ​ຂໍ້​ມູນ​ນັ້ນ"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ໃນ​ຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​ເຊັນ​ຂອງຊຸດ​ຂໍ້​ມູນ​ເປັນ​ເປີ​​ເຊັນ (0..1, ສ​ະ​ເພາະ)ຂອງ​ຊຸດ​ຂໍ້​ມູນ​ນັ້ນ"}, "PERMUT": {"a": "(number; number_chosen)", "d": "ສົ່ງຄືນຈຳນວນວິທີການປ່ຽນ​ລໍາດັບທີ່ເປັນໄປໄດ້ທັງໝົດ ສຳລັບຈຳນວນວັດຖຸທີ່ກຳນົດໃຫ້ເລືອກຈາກວັດຖຸທັງໝົດ"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​ການ​ລຽງສັບ​ປ່ຽນ​ທີ່​ເປັນ​ໄປ​ໄດ້​ທັງ​ໝົດ​ສໍາລັບ​ຈຳນວນ​ວັດຖຸ​ທີ່​ກຳນົດ​ໄວ້ (ທີ່​ມີ​ການ​ຊໍ້າ​ກັນ) ທີ່​ເລືອກ​ຈາກ​ວັດຖຸທັງ​ໝົດ"}, "PHI": {"a": "(x)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ຟັງ​ຊັນ​ຄວາມ​ໜາ​ແໜ້ນ​ສໍາລັບ​ການ​ແຈກ​ຢາຍ​ປົກກະຕິ​ມາດຕະຖານ"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ Poisson"}, "POISSON.DIST": {"a": "(x; lambda; cumulative)", "d": "ສະ​ແດງ​ຜົນການ​ກະ​ຈາຍເອັກ​ຊ໌​ໂປ​ເນັນ​ຊຽວ"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "ສະ​ແດງ​ຜົນໂອ​ກາດ​ທີ່​ຄ່າ​ຢູ່​ໃນ​ຂອບ​ເຂດ​ຢູ່​ລະ​ຫວ່າງ​ສອງ​ຂີດ​ຈຳ​ກັດ ຫຼື​ເທົ່າ​ກັບ​ຂີດ​ຕ່ຳ"}, "QUARTILE": {"a": "(array; quart)", "d": "ຜົນໄດ້ຮັບຄ່າໜຶ່ງສ່ວນສີ່ຂອງຊຸດຂໍ້ມູນ"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "ສົ່ງຄືນ​ຄ່າໜຶ່ງ​​ສ່ວນສີ່​ຂອງ​ຊຸດ​ຂໍ້​ມູນ ​ໂດຍ​ອີງ​ຕາມ​ຄ່າ​ເປີ​ເຊັນ​ຈາກ 0..1"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "ສົ່ງຄືນ​ຄ່າໜຶ່ງ​​ສ່ວນສີ່​ຂອງ​ຊຸດ​ຂໍ້​ມູນ ​ໂດຍ​ອີງ​ຕາມ​ຄ່າ​ເປີ​ເຊັນ​ຈາກ 0..1"}, "RANK": {"a": "(number; ref; [order])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ຂອງ​ຕົວ​ເລກ​ທີ່​​ຢູ່​ໃນ​ລາຍການ​ຂອງ​ຕົວ​ເລກ: ຂະໜາດ​ຂອງ​ມັນ​ກ່ຽວ​ພັນ​ກັບ​ຄ່າ​ອື່ນ​ໃນ​ລາຍການ"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ຂອງ​ຕົວ​ເລກ​ທີ່​ລະບຸ​ຊື່​ຢູ່​ໃນ​ລາຍການ​ຂອງ​ຕົວ​ເລກ: ຂະໜາດ​ຂອງ​ມັນ​ກ່ຽວ​ພັນ​ກັບ​ຄ່າ​ອື່ນ​ໃນ​ລາຍການ; ຖ້າ​ຫຼາຍກວ່າ​ໜຶ່ງ​ຄ່າ​ມີ​ອັນ​ດັບ​ຄື​ກັນ, ອັນ​ດັບ​ສະ​ເລ່ຍຖືກ​ສົ່ງ​ຄືນ"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັນ​ດັບ​ຂອງ​ຕົວ​ເລກ​ທີ່​ລະບຸ​ຊື່​ຢູ່​ໃນ​ລາຍການ​ຂອງ​ຕົວ​ເລກ: ຂະໜາດ​ຂອງ​ມັນ​ກ່ຽວ​ພັນ​ກັບ​ຄ່າ​ອື່ນ​ໃນ​ລາຍການ; ຖ້າ​ຫຼາຍກວ່າ​ໜຶ່ງ​ຄ່າ​ມີ​ອັນ​ດັບ​ຄື​ກັນ, ອັນ​ດັບ​ສູງ​ສຸດ​ຂອງ​ຊຸດ​ຄ່າ​ນັ້ນ​ຈະຖືກ​ສົ່ງ​ຄືນ"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "ຜົນ​ໄດ້​ຮັບຄ່າກຳ​ລັງ​ສອງ​ຂອງ​ສຳ​ປາ​ສິດ​ຄວາມ​ສຳ​ພັນ​ຊ່ວງ​ເວ​ລາ​ຜະ​ລິດ​ຕະ​ພັນ​ເພຍ​ສັນຜ່ານຈຸດຂໍ້ມູນທີ່ໃຫ້ໄວ້"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຄ່າຄວາມອ່ຽງຂອງການແຈກຢາຍ: ເປັນຄ່າທີ່ສະແດງລັກສະນະລະດັບຂອງຄວາມບໍ່ສົມສ່ວນຂອງການແຈກຢາຍອ້ອມຮອບ"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງ​ກັບ​ຄ່າ​ຄວາມ​ອ່ຽງຂອງ​ການ​ແຈກ​ຢາຍ​ໃນ​ກຸ່ມ​ປະຊາກອນ​ໜຶ່ງ: ​ເປັນ​ຄ່າ​ທີ່​ສະ​ແດງລັກສະນະ​ລະດັບ​ຄວາມ​ບໍ່ົມສ່ວນ​ຂອງ​ການ​ແຈກ​ຢາຍ​ອ້ອມ​ຮອບ​ຄ່າ​ສະ​ເລ່ຍຂອງມັນ"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "ຜົນ​ໄດ້​ຮັບຄ່າຄວາມຊັນຂອງການຖົດຖອຍເປັນ​​ເສັ້ນ​ຊື່ຜ່ານຈຸດຂໍ້ມູນທີ່ໃຫ້ໄວ້"}, "SMALL": {"a": "(array; k)", "d": "ສົ່ງຄືນຄ່າທີ່ນ້ອຍທີ່ສຸດໃນລຳດັບທີ K ຂອງຊຸດຂໍ້ມູນ. ຕົວຢ່າງ, ຕົວເລກທີ່ນ້ອຍສຸດເປັນອັນດັບ 5 ຂອງຊຸດຕົວເລກ"}, "STANDARDIZE": {"a": "(x; mean; cumulative)", "d": "ສະ​ແດງ​ຜົນການ​ກະ​ຈາຍ Poisson"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "ຄາດ​ຄະ​ເນ​ສ່ວນ​ຜິດ​ບ່ຽງ​ມາດຕະຖານ​ຈາກ​ຕົວຢ່າງ (ລ​ະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ​ແລະ​ຂໍ້ຄວາມ​ໃນ​ຕົວຢ່າງ)"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "ຄຳນວນຫາການ​ຜິດ​ບ່ຽງມາດຕະຖານຈາກທັງກຸ່ມປະຊາກອນທັງໝົດທີ່ເປັນຂໍ້ພິສູດ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະ ຂໍ້ຄວາມ)"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "ປະເມີນການຄາດເຄືອນມາດຖານຈາກຕົວຢ່າງ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມທີ່ຢູ່ໃນຕົວຢ່າງ)"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "ປະ​ເມີນ​ຄວາມ​ບ່ຽງ​ເບນ​ມາດ​ຕະ​ຖານ​ອີງ​ຕາມຕົວ​ຢ່າງ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "ຄໍານວນ​ນຫາ​ສ່ວນ​ຜິດ​ບ່ຽງມາດຕະຖານ​ຈາ​ກຸ່ມ​ປະຊາກອນ​ທັງ​ໝົດ​ທີ່​ເປັນ​ຂໍ້​ພິສູດ (ລະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ​ແລະ​ຂໍ້ຄວາມ)"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "ຄິດ​ໄລ່​ຄວາມ​ບ່ຽງ​ເບນ​ມາດ​ຕະ​ຖານ​ອີງ​ຕາມ​ປະ​ຊາ​ກອນ​ທັງ​ໝົດ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "ຜົນ​ໄດ້​ຮັບຄວາມ​ຜິດ​ພາດ​ມາດ​ຕະ​ຖານ​ຂອງ​ຄ່າ y ທີ່​ຄາດ​ໄວ້​ສຳ​​ລັບ​ແຕ່​ລະ​ຕົວ x ຢູ່​ໃນ​ການ​ຖົດ​ຖອຍ"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "ສົ່ງ​ຄືນຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ"}, "TINV": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ທັງ​ສອງ​ດ້ານ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "ສົ່ງ​ຄືນຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນດ້ານ​ຊ້າຍ"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ການ​ແຈກ​ຢາຍ​​ແບບ t ຂອງ​ນັກຮຽນ​ທັງ​ສອງ​ດ້ານ"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "ສົ່ງ​ຄືນຄ່າ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນດ້ານ​ຂວາ"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ທັງ​ສອງ​ດ້ານ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ປິ້ນຄືນ​ທັງ​ສອງ​ດ້ານ​ຂອງ​ການ​ແຈກ​ຢາຍ​ແບບ t ຂອງ​ນັກຮຽນ"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "ສົ່ງຄືນຄ່າຄວາມເປັນໄປໄດ້ທີ່ໄດ້ຈາກການເຮັດ t-Test ຂອງ​ນັກຮຽນ"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "ຜົນ​ໄດ້​ຮັບຄ່າຕົວເລກຢູ່ໃນແນວໂນ້ມເສັ້ນ ເຊິ່ງກົງກັບຈຸດຂໍ້ມູນທີ່ຮູ້ຄ່າ, ນຳໃຊ້ວິທີກຳລັງສອງນ້ອຍທີ່ສຸດ"}, "TRIMMEAN": {"a": "(array; percent)", "d": "ສົ່ງຄືນຄ່າສະເລ່ຍຂອງຊຸດຂໍ້ມູນທີ່ເຫຼືອຫຼັງຈາກເຮັດການຕັດບາງສ່ວນຂອງຊຸດຂໍ້ມູນອອກໄປ"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "ສົ່ງ​ຄືນ​ຄວາມ​ເປັນ​ໄປ​ໄດ້​ທີ່​ກ່ຽວ​ພັນ​ກັບ Student's t-Test"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "ຄາດ​ຄະ​ເນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງ​ຈາກ​ຕົວຢ່າງ (ລະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ​ແລະ​ຂໍ້ຄວາມ​ໃນ​ຕົວຢ່າງ)"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "ຄຳນວນຫາຄ່າຕ່າງໆຈາກປະຊາກອນທັງໝົດ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມຂອງປະຊາກອນ)"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "ຄາດຄະເນຖານຕ່າງໆຈາກຕົວຢ່າງ (ລະເວັ້ນຄ່າຄວາມ​ຈິງ ແລະຂໍ້ຄວາມໃນຕົວຢ່າງ)"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "ປະ​ເມີນ​​​ການຜັນ​ປ່ຽນ​ອີງ​ຕາມຕົວ​ຢ່າງ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "ຄໍານວນ​ຄ່າ​ຄວາມ​ແຕກ​ຕ່າງ​ຈາກ​ປະຊາກອນ​ທັງ​ໝົດ (ລ​ະ​ເວັ້ນຄ່າ​ຄວາມ​ຈິງ ລ​ແລະ​ຂໍ້ຄວວາ​ມ​ໃນ​ປະຊາກອນ)"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "ປະ​ເມີນ​​​ການຜັນ​ປ່ຽນ​ອີງ​ຕາມຕົວ​ຢ່າງ, ລວມ​ທັງ ຄ່າ ແລະ​ຂໍ້​ຄວາມຕັກ​ກະ​ວິ​ທະ​ຍາ. ຂໍ້​ຄວາມ ແລະຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ False ມີ​ຄ່າ 0; the ຄ່າ​ຕັກ​ກະ​ວິ​ທະ​ຍາ TRUE ມີ 1"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງ​ຄືນ​ການ​ແຈກ​ຢາຍ​ແບບ <PERSON><PERSON>"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "ສົ່ງກັບຄ່າການແຈກຢາຍ ແບບ Weibull"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "ສົ່ງຄືນຄ່າ P ສອງດ້ານຂອງການທົດສອບ z-test"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "ສົ່ງ​ຄືນ​ຄ່າ P ​ແບບ​ດ້ານ​ດຽວ​ຂອງ​ກາ​ນທົດ​ສອບ z-test"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "ສົ່ງຄືນດອກເບ້ຍຄ້າງຮັບສຳລັບຫຼັກຊັບທີ່ຈ່າຍດອກເບ້ຍເປັນງວດ"}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "ສົ່ງຄືນດອກເບ້ຍຄ້າງຮັບສຳລັບຫຼັກຊັບທີ່ຈ່າຍດອກເບ້ຍໃນວັນຄົບກຳນົດຖອນ"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາແບບເສັ້ນຊື່ທີ່ແບ່ງຕາມສ່ວນ ຂອງຊັບ​ສິນສຳລັບຊ່ວງເວລາທາງບັນຊີແຕ່ລະຊ່ວງ"}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາແບບເສັ້ນຊື່ທີ່ແບ່ງຕາມສ່ວນ (prorated) ຂອງຊັບ​ສິນສຳລັບຊ່ວງເວລາທາງບັນຊີແຕ່ລະຊ່ວງ"}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນວັນຕັ້ງແຕ່ວັນທີເລີ່ມຕົ້ນງວດການຊຳລະຄ່າກາສານເຖິງວັນທີຊຳລະຄ່າຊື້ຂາຍ"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນວັນໃນງວດຂອງການຈ່າຍດອກເບ້ຍ ເຊິ່ງລວມວັນທີຊຳລະຄ່າຊື້ຂາຍ"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນວັນຕັ້ງແຕ່ວັນທີຊຳລະຄ່າຊື້ຂາຍເຖິງວັນທີຈ່າຍດອກເບ້ຍງວດຖັດໄປ"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນວັນຈ່າຍດອກເບ້ຍງວດຖັດໄປຫຼັງຈາກວັນທີຊຳລະຄ່າຊື້ຂາຍ"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນຈຳນວນການຈ່າຍດອກເບ້ຍລະຫວ່າງວັນທີຊຳລະຄ່າຊື້ຂາຍ ແລະວັນຄົບກຳນົດຖອນ"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "ສົ່ງຄືນວັນຈ່າຍດອກເບ້ຍງວດທີ່ຜ່ານມາກ່ອນວັນທີຄ່າຊຳລະຄ່າຊື້ຂາຍ"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "ສົ່ງຄືນດອກເບ້ຍສະສົມທີ່ຊຳລະລະຫວ່າງສອງງວດຄືອັດຕາດອກເບ້ຍ"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "ສົ່ງຄືນຄ່າເງິນຕົ້ນສະສົມທີ່ໄດ້ຊຳລະໃຫ້ກັບເງິນກູ້ໃນ ລະຫວ່າງສອງງວດ"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາຂອງຊັບສິນສຳລັບຊ່ວງເວລາທີ່ລະບຸ ໂດຍໃຊ້ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ຄົງ​ທີ່"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "ສົ່ງຄືນຄ່າເສື່​ອມລາຄາຂອງຊັບ​ສິນສຳລັບຊ່ວງເວລາທີ່ລະບຸ ໂດຍໃຊ້ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ສອງ​ເທົ່າ ຫຼືວິທີການອື່ນທີ່ທ່ານລະບຸ"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "ສະ​ແດງ​ຜົນ​ອັດ​ຕາ​ຫຼຸດ​ລາ​ຄາ​ສຳ​ລັບ​ຫຼັກ​ຊັບ"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "ປ່ຽນລາຄາໂດລາໃນ​ຮູບ​ແບບເລກເສດສ່ວນເປັນລາຄາໂດຍລາໃນຮູບແບບເລກທົດສະນິຍົມ"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "ປ່ຽນລາຄາໂດລາ,ໃນຮູບແບບເລກທົດສະນິຍົມ,ເປັນລາຄາໂດລາ,ໃນຮູບແບບເລກເສດສ່ວນ"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "ສົ່ງຄືນຊ່ວງເວລາຕໍ່ປີຂອງຫຼັກຊັບທີ່ຈ່າຍດອກເບ້ຍເປັນງວດ"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "ສົ່ງຄືນອັດຕາດອກເບ້ຍທີ່ແທ້ຈິງຕໍ່ປີ"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "ສົ່ງຄືນຄ່າ FV ມູນຄ່າໃນອະນາຄົດ ຂອງການລົງທຶນ ຄ່ານີ້ຖືກຄຳນວນ ໂດຍມີພື້ນຖານຢູ່ໃນການຈ່າຍເງິນເປັນງວໂດຍຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາທີ່ຄົງທີ່"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "ສົ່ງຄືນມູນຄ່າໃນອະນາຄົດ (Future Value) ຂອງເງິນຕົ້ນຫຼັງຈາກນຳຊຸດຂໍ້ມູນຂອງອັດຕາດອກເບ້ຍທົບຕົ້ນມາໃຊ້"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "ສົ່ງຄືນຄ່າອັດຕາດອກເບ້ຍຂອງຫຼັກຊັບທີ່ລົງທຶນທັງ​ໝົດ"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າດອກເບ້ຍທີ່ຕ້ອງຈ່າຍສຳລັບຊ່ວງເວລາທີ່ລະບຸສຳລັບການລົງທືນ, ຄ່າທີ່ຄຳນວນໄດ້ມີພື້ນຖານຢູ່ເທິງການຈ່າຍເງິນເປັນງວດ, ຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍທີ່ຄົງທີ່"}, "IRR": {"a": "(values; [guess])", "d": "ສະ​ແດງ​ຜົນອັດ​ຕາ​ພາຍ​ໃນ​ຂອງ​ຜົນ​ໄດ້​ຮັບ​ສຳ​ລັບ​ຊຸດ​ການ​ໄຫຼວຽນ​ເງິນ​ສົດ"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "ສົ່ງຄືນຄ່າດອກເບ້ຍທີ່ຕ້ອງຊຳລະໃນ ລະຫວ່າງເວລາໜຶ່ງຂອງການລົງທຶນ"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "ສົ່ງຄືນຊ່ວງເວລາທີ່​ດັດ​ແປງ​ຂອງແມັກຄໍລີ (Macauley Modified Duration ) ຂອງຫຼັກຊັບທີ່ສົມມຸດໃຫ້ມູນຄ່າທີ່ຕັ້ງໄວ້ເປັນ 100 ໂດຍລາ"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "ສະ​ແດງ​ຜົນ​ອັດ​ຕາພາຍ​ໃນ​ຂອງ​ຜົນ​ຕອບ​ແທນ​ສຳ​ລັບ​ຊຸດ​ຂອງ​ການ​ໄຫຼວຽນ​ເງິນ​ສົດ, ພິ​ຈາ​ລະ​ນາ​ທັງມູນ​ຄ່າ​ການ​ລົງ​ທຶນ ແລະ​ດອກ​ເບ້ຍ​ຢູ່​ໃນ​ການ​ລົງ​ທຶນ​ເງິນ​ສົດ​ໃໝ່"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "ສົ່ງຄືນອັດຕາດອກເບ້ຍທີ່ລະບຸຕໍ່ປີ"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າຈຳນວນຊ່ວງເວລາທັງໝົດໃນການຈ່າຍເງິນ ສຳລັບການລົງທຶນຈຳນວນດັ່ງ ກ່າວຖືກຄຳນວນ ໂດຍມີພື້ນຖານຢູ່ເທິງການຈ່າຍເງິນ ເປັນງວໂດຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍທີ່ຄົງທີ່"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "ສົ່ງຄືນມູນຄ່າປະຈຸບັນ ຂອງເງິນລົງທຶນເຊິ່ງຄຳນວນຈາກອັດຕາດອກເບ້ຍ (ຄ່າລົບ) ແລະ ລາຍໄດ້ (ຄ່າບວກ)ໃນອະນາຄົດ"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "ສົ່ງຄືນລາຄາຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາຂອງຫຼັກຊັບທີ່ມີຊ່ວງເວລາທຳອິດສັ້ນ ຫຼືຍາວກວ່າມາດຕະຖານ"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "ສົ່ງຄືນອັດຕາຜົນຕອບແທນຂອງຫຼັກຊັບທີ່ມີຊ່ວງເວລາທຳອິດສັ້ນ ຫຼືຍາວກ່ວາມາດຕະຖານ"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ຜົນ​ລ​າ​ຄາ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100 ຂອງ​ຫຼັກ​ຊັບ​ທີ່​ມີ​ໄລ​ຍະ​ສຸດ​ທ້າຍ​ຄີກ"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ຂອງ​ການ​ຄ້ຳ​ປະ​ກັນ​ກັບ​ໄລ​ຍະ​ສຸດ​ທ້າຍ​ຄີກ"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​​ໄລຍະ​ເວລາ​ທີ່​ຕ້ອງ​ໃຊ້​​ໂດຍ​ການ​ລົງທຶນ​ເພື່ອ​ໃຫ້​ໄປ​ຮອດ​ຄ່າ​ທີ່​ລະບຸ​ໄວ້"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "ຄຳນວນຫາຍອດການຈ່າຍເງິນສຳລັບເງິນກູ້ທີ່​ອີງ​ໃສ່ຍອດການຈ່າຍເງິນທີ່ຄົງທີ່"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າເງິນຕົ້ນທີ່ຕ້ອງຈ່າຍສຳລັບການລົງທຶນການຄຳນວນໄດ້ມີພື້ນຖານຢູ່ເທິງ ການຈ່າຍເງິນເປັນງວດ, ຍອດການຈ່າຍເງິນທີ່ຄົງທີ່ ແລະ ອັດຕາດອກເບ້ຍທີ່ຄົງທີ່"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ລາ​ຄາ​ຕໍ່​ມູນ​ຄ່າ​ດ້ານ​ໜ້າ $100 ຂອງ​ຫຼັກ​ຊັບ​ທີ່​ຈ່າຍດອກ​ເບ້ຍ​ເປັນ​ໄລ​ຍະ"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "ສົ່ງຄືນລາຄ່າຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາຂອງຫຼັກຊັບທີ່ມີອັດຕາສ່ວນຫຼຸດ"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "​ສະ​ແດງ​ຜົນ​​ລາ​ຄາ​ຕໍ່​ມູນ​ຄ່າດ້ານ​ໜ້າ $100 ຂອ​ງ​​ຫຼັກ​ຊັບ​ທີ່​ຈ່າຍດອກ​ເບ້ຍ​ໃນ​ເວ​ລາ​ຄົບ​ກຳ​ນົດ"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "ສົ່ງຄືນຄ່າ PV (present value-ມູນຄ່າປະຈຸບັນ) ຂອງການລົງທຶນ: ເຊິ່ງກໍ່ແມ່ນມູນຄ່າລວມທັງໝົດໃນເວລາປະຈຸບັນຂອງຈຳນວນເງິນໃນທຸກງວດທີ່ຕ້ອງຈ່າຍໃນອະນາຄົດ"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "ສົ່ງຄືນອັດຕາດອກເບ້ຍຕໍ່ຊ່ວງເວລາຂອງເງິນກູ້ ຫຼືການລົງທຶນ, ຕົວຢ່າງເຊັ່ນ, ໃຫ້ໃຊ້ 6%/4 ສຳລັບການຈ່າຍເງິນທີ່ແບ່ງຈ່າຍທຸກໆ 3 ເດືອນລວມເປັນ 4 ງວດທີ່ອັດຕາດອກເບ້ຍ 6% ຕໍ່ປີ"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "ສົ່ງຄືນມູນຄ່າທີ່ຈະໄດ້ຮັບໃນວັນຄົບກຳນົດຖອນຂອງຫຼັກຊັບທີ່ໃຊ້ຊັບ​ສິນທັງໝົດຂອງການລົງທຶນ"}, "RRI": {"a": "(nper; pv; fv)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ອັດຕາ​ດອກ​ເບ້ຍ​ທີ່​ທຽບ​ເທົ່າ​ກັບ​ການ​ເຕີບ​ໂຕ​ຂອງ​ການ​ລົງທຶນ​ອັນ​ໜຶ່ງ"}, "SLN": {"a": "(cost; salvage; life)", "d": "ສົ່ງຄືນຄ່າເສື່ອມລາຄາແບບເສັ້ນຊື່ຂອງຊັບສິນສຳລັບໜຶ່ງຊ່ວງເວລາ"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "ສົ່ງຄືນຄ່າເສື່ອມລາຄາແບບ sum-of-years' digits ຂອງຊັບ​ສິນ ສໍາລັບຊ່ວງເວລາທີ່ລະບຸ"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "ສົ່ງຄືນຄ່າອັດຕາຜົນຕອບແທນທຽບເທົ່າພັນນະບັດໃບເງິນຄັງ"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "ສົ່ງຄືນລາຄາຕໍ່ມູນຄ່າຕາມຕາສານ 100 ໂດລາຂອງໃບ​ເງິນຄັງ"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "​ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ສຳ​ລັບ​ໃບ​ບິນ​ຄັງ​ເງິນ"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "ສົ່ງຄືນຄ່າ​ເສື່ອ​ມລາຄາຂອງຊັບສິນສຳລັບຊ່ວງເວລາໃດໆກໍ່ຕາມທີ່ທ່ານລະບຸ,ຫຼືບາງສ່ວນຂອງຊ່ວງເວລາໃດໆກໍ່ຕາມທີ່ທ່ານລະບຸ, ການຄຳນວນຈະໃຊ້ວິທີການຍອດ​ເຫຼືອຫຼຸດລົງ​ສອງ​ເທົ່າ ຫຼືວິທີອື່ນໆທີ່ທ່ານລະບຸ"}, "XIRR": {"a": "(values; dates; [guess])", "d": "ສະ​ແດງ​ຜົນ​ອັດ​ຕາພາຍ​ໃນ​ຂອງ​ຜົນ​ຕອບ​ແທນ​ສຳ​ລັບກຳ​ນົດ​ເວ​ລາ​ການ​ໄຫຼວ​ວຽນ​ເງິນ​ສົດ"}, "XNPV": {"a": "(rate; values; dates)", "d": "ສົ່ງຄືນມູນຄ່າປະຈຸບັນສຸດທິ (Net Present Value) ສຳລັບກະແສເງິນສົດໃນຊ່ວງເວລາໜຶ່ງ"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ຢູ່​ໃນ​ຫຼັກ​ຊັບ​ທີ່​ຈ່າຍ​ດອກ​ເບ້ຍ​ເປັນ​ໄລ​ຍະ"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "​ສະ​ແດງ​ຜົນ​ໄດ້​ຮັບ​ປະ​ຈຳ​ປີ​ສຳ​ລັບ​​ຫຼັກ​ຊັບ​ຫຼຸດ​ລາ​ຄາ. ຕົວ​ຢ່າງ, ​ໃບ​ບິນ​ຄັງ​ເງິນ"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "ສົ່ງຄືນຜົນຕອບແທນລາຍປີ ຂອງຫຼັກຊັບທີ່ຊຳລະດອກເບ້ຍໃນວັນຄົບກຳນົດຖອນ"}, "ABS": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າທີ່ສົມບູນຂອງເລກ, ເລກທີ່ບໍ່ມີເຄື່ອງໝາຍ"}, "ACOS": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າອາກໂກຊິນຂອງຕົວເລກທີ່ລະບຸ, ຄ່າທີ່ສົ່ງຄືນຈະຢູ່ໃນແບບຣາດຽນທີ່ຢູ່ໃນຊ່ວງ 0 ຫາ Pi. ໝາຍເຫດ ຖ້າອາກໂກຊິນເປັນຄ່າມູມທີ່ໂກຊິນໃຊ້ຫາຄ່າ"}, "ACOSH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກໂກຊິນ (hyperbolic cosine) ກັບກັນຂອງຈຳນວນທີ່ລະບຸ"}, "ACOT": {"a": "(number)", "d": "ສົ່ງ​ກັບ​ອາກ​ໂຄ​ແທນ​ເຈນ (arccotangent) ຂອງ​ຕົວ​ເລກ​ໃນ​ໜ່ວຍຣາດຽນ​ໃນ​ຊ່ວງ 0 ຫາ Pi"}, "ACOTH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​ແທນ​ເຈນ (hyperbolic cotangent) ​ແບບປິ້ນຄືນ​ຂອງ​ຕົວ​​ເລກ"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "ສົ່ງ​ຄືນ​ການ​ລວມ​ໃນ​ລາຍການ ຫຼືຖານ​ຂໍ້​ມູນ"}, "ARABIC": {"a": "(text)", "d": "ປ່ຽນ​ຕົວ​ເລກ​ໂຣມັນ​ເປັນ​ອາຣາບິ​ກ"}, "ASC": {"a": "(text)", "d": "ສໍາລັບພາສາ ຕົວອັກສອນເປັນຊຸດ Double-byte (DBCS), ໜ້າທີ ປ່ຽນ ຈະປ່ຽນ ຕົວອັກສອນຈາກ ເຕັມໜ້າ (double-byte) ເປັນ ຕົວອັກສອນ ເຄີ່ງໜ້າ (single-byte)"}, "ASIN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າອາກຊິນຂອງຕົວເລກທີ່ລະບຸ,ຄ່າທີ່ສົ່ງຄືນຈະຢູ່ໃນຮູບແບບຣາດຽນ ທີ່ຢູ່ໃນຊ່ວງ -Pi/2 ຫາ Pi/2"}, "ASINH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກຊິນ (hyperbolic sine) ກັບກັນ ຂອງຈຳນວນທີ່ລະບຸ"}, "ATAN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ​ອາກ​ແທນ​ເຈັນຂອງ​ຕົວ​ເລກ ​ໃນ​​ຮູບແບບຣາດຽນ ທີ່ຢູ່ໃນຊ່ວງຫ່າງ -Pi/2 ຫາ Pi/2"}, "ATAN2": {"a": "(x_num; y_num)", "d": "ສະ​ແດງ​ຜົນອາກ​ຕັງ​ຂອງຕົວ​ພິ​ກັດ x- ແລະ y-, ເປັນ​ຣາ​ດຽງ​​ລະ​ຫວ່າງ -Pi ຫາ Pi, ບໍ່​ລວມ -Pi"}, "ATANH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກ​ແທນ​ເຈັນ (hyperbolic tangent) ກັບກັນຂອງຈຳນວນທີ່ລະບຸ"}, "BASE": {"a": "(number; radix; [min_length])", "d": "ປ່ຽນ​ຕົວ​ເລກ​ໃຫ້​ເປັນ​ຂໍ້ຄວາມ​ທີ່​ໃຊ້​ແທນ​ຕົວ​ເລກ​ນັ້ນ​ໃນ​ຖານ Radix ທີ່​ກຳນົດ​ໃຫ້"}, "CEILING": {"a": "(number; significance)", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ຂຶ້ນ​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ໃກ້​ທີ່​ສຸດຂອງ​ຄວາ​ມສຳຄັນ"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ຂຶ້ນໃຫ້​ເປັນ​ຈໍານວນ​ເຕັມທີ່​ໃກ້​ທີ່​ສຸດ ຫຼື​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ໃກ້​ທີ່​ສຸດ​ຂອງ​ຄວາມສຳຄັນ"}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "ຄືນ ຕົວເລກທີປັດເສດຂື້ນເປັນຈໍານວນເຕັມທີໄກ້ຄຽງທີສຸດ ຫຼື ໄກ້ຄຽງກັບຕົວເລກທີໃຫ່ຍທີສຸດ, ແລະບໍ່ຂື້ນກັບ ເຄື່ອງໝາຍຂອງຕົວເລກ."}, "COMBIN": {"a": "(number; number_chosen)", "d": "ສົ່ງຄືນຈຳນວນວິທີຈັດລວມທີ່ເປັນໄປໄດ້ສຳລັບ ຈໍານວນລາຍການທີລະບຸ"}, "COMBINA": {"a": "(number; number_chosen)", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​ການ​ປະສົມ​ເຂົ້າກັນ​ທີ່​ມີ​ການ​ຊໍ້າ​ກັນ​ສໍາລັບ​ຈໍານວນ​ລາຍການ​ທີ່​ລະບຸ"}, "COS": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໂກຊິນຂອງມູມ"}, "COSH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກໂກຊິນ (hyperbolic cosine) ຂອງຈຳນວນທີ່ລະບຸ"}, "COT": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໂຄ​ແທນ​ເຈນ (cotangent) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ"}, "COTH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​​ແທນ​ເຈນ (hyperbolic cotangent) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ"}, "CSC": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໂຄ​ຊີ​ແຄນ​ທ (cosecant) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ"}, "CSCH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ໂຄ​ຊີ​ແຄນ​ທ (hyperbolic cosecant) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ"}, "DECIMAL": {"a": "(number; radix)", "d": "ປ່ຽນ​ຂໍ້ຄວາມ​ທີ່​ສະ​ແດງ​ແທນ​ຕົວ​ເລກ​​ໃນ​ຖານ​ທີ່​ກໍານົດ​ໃຫ້​ເປັນ​ເລກ​ຖານ​ສິບ"}, "DEGREES": {"a": "(angle)", "d": "ປ່ຽນມູມຣາດຽນ (radians)​ໃຫ້​ເປັນມູມອົງສາ"}, "ECMA.CEILING": {"a": "(number; significance)", "d": "ປັດຕົວເລກຂື້ນ ຫາຈຸດທີໄກ້ຄຽງ ທີໄດ້ກໍານົດໄວ້"}, "EVEN": {"a": "(number)", "d": "ປັດຄ່າຂຶ້ນສຳລັບຈຳນວນບວກ ແລະ ປັດຄ່າລົງສຳລັບຈຳນວນລົບ ໂດຍຈະປັດຄ່າໃຫ້ເທົ່າກັບຈຳນວນເຕັມທີ່ເປັນເລກຄູ່ທີ່ໃກ້ຄຽງທີ່ສຸດ"}, "EXP": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ e ຂອງຈຳນວນທີ່ຍົກກຳລັງ"}, "FACT": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າແຟັກເຕີຣຽລຂອງຕົວ​ເລກທີ່ລະບຸ, (ເທົ່າກັບ 1*2*3*...* ຕົວເລກ)"}, "FACTDOUBLE": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າສອງແຟັກເຕີ​​ຣຽລຂອງຕົວ​ເລກທີ່ລະບຸ"}, "FLOOR": {"a": "(number; significance)", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ລົງ​ໃຫ້​ເປັນ​ຈໍານວນ​ທີ່​ໃຫ້​ທີ່​ສຸດ​ຂອງ​ຄວາມສຳຄັນ"}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "ຄືນ ຕົວເລກທີປັດເສດລົງເປັນຈໍານວນເຕັມທີໄກ້ຄຽງທີສຸດ ຫຼື ໄກ້ຄຽງກັບຕົວເລກທີນ້ອຍທີສຸດ, ແລະບໍ່ຂື້ນກັບ ເຄື່ອງໝາຍຂອງຕົວເລກ."}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "ປັດ​ເສດ​ຕົວ​ເລກ​ລົງ​​ໃຫ້​ເປັນ​ຈໍານວນ​ເຕັມທີ່​ໃກ້​ທີ່​ສຸດ ຫຼື​ເປັນ​ຈໍານວນ​ເທົ່າ​ທີ່​ໃກ້​ທີ່​ສຸດ​ຂອງ​ຄວາມສຳຄັນ"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຕົວຫານຮ່ວມໃຫຍ່ສຸດ"}, "INT": {"a": "(number)", "d": "ປ່ຽນຈຳນວນເສດໃຫ້ເປັນຈຳນວນຖ້ວນທີ່ໃກ້ຄຽງທີ່ສຸດ"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "ຄືນ ຕົວເລກທີປັດເສດຂື້ນເປັນຈໍານວນເຕັມທີໄກ້ຄຽງທີສຸດ ຫຼື ໄກ້ຄຽງກັບຕົວເລກທີໃຫ່ຍທີສຸດ, ແລະບໍ່ຂື້ນກັບ ເຄື່ອງໝາຍຂອງຕົວເລກ. ແຕ່ວ່າ,ຖ້າຫາກ ຕົວຄູນ ແມ່ນເລກ ສູນ. ຈະຕ້ອງຄືນ ສູນ."}, "LCM": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຕົວຄູນຮ່ວມນ້ອຍສຸດ"}, "LN": {"a": "(number)", "d": "ສົ່ງຄືນຈຳນວນຂອງໂລກາລິກທຳມະຊາດ"}, "LOG": {"a": "(number; [base])", "d": "ສົ່ງຄືນຄ່າໂລກາລິກຂອງຕົວເລກ ໂດຍໃຊ້ຖານໂລກາລິກທີ່ທ່ານລະບຸ"}, "LOG10": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໂລກາລິກຖານ-10 ຂອງ​ຈໍານວນ​ທີ່​ລະບຸ"}, "MDETERM": {"a": "(array)", "d": "ສົ່ງຄືນຄ່າຕົວ​ກຳນົດແມດທຣິກ (matrix determinant) ຂອງອາ​ເຣຍ໌"}, "MINVERSE": {"a": "(array)", "d": "ສົ່ງຄືນຄ່າແມດທຣິກປິ້ນກັນ (inverse matrix) ສຳລັ ແມດທຣິກທີ່ເກັບໃນຮູບ​ແບບອາ​ເຣຍ໌"}, "MMULT": {"a": "(array1; array2)", "d": "ສົ່ງຄືນຜົນຄູນຂອງ ແມດທຣິກ 2 ຄ່າ ຮອບວຽນ, ອາ​ເຣຍ໌ທີ່ມີຈຳນວນແຖວເທົ່າກັບອາ​ເຣຍ໌ 1 ແລະ ຈຳນວນຖັນເທົ່າກັບອາ​ເຣຍ໌ 2"}, "MOD": {"a": "(number; divisor)", "d": "ສົ່ງຄືນຄ່າເສດທີ່ເຫຼືອຫຼັງຈາກທີ່ຈຳນວນຖືກຫານໂດຍໂຕຫານແລ້ວ"}, "MROUND": {"a": "(number; multiple)", "d": "ສົ່ງຄືນຈຳນວນທີ່ທີ່ປັດເປັນຈຳນວນ ທະວີຄູນທີ່ທ່ານຕ້ອງການ "}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນພະຫຸພົດຂອງຊຸດຂໍ້ມູນຕົວເລກ"}, "MUNIT": {"a": "(dimension)", "d": "ສົ່ງ​ຄືນ​ແມັດທຣິກຫົວໜ່ວຍ​ສຳລັບ​ມິ​ຕິ​ທີ່​ລະບຸ​ໄວ້"}, "ODD": {"a": "(number)", "d": "ປັດຄ່າຂຶ້ນສຳລັບຈຳນວນບວກ ແລະ ປັດຄ່າລົງສຳລັບຈຳນວນລົບ ໂດຍຈະປັດຄ່າໃຫ້ເທົ່າກັບຈຳນວນເຕັມທີ່ເປັນເລກຄີກທີ່ໃກ້ຄຽງທີ່ສຸດ"}, "PI": {"a": "()", "d": "ສົ່ງຄືນຄ່າ Pi, ເຊິ່ງເທົ່າກັບ 3.14159265358979, ຄວາມລະອຽດ 15 ຫຼັກ"}, "POWER": {"a": "(number; power)", "d": "ສົ່ງຄືນຄ່າຜົນລັບຂອງເລກຂຶ້ນກຳລັງ"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "ຫານຜົນຄູນຂອງຕົວເລກທີ່ເປັນຂໍ້ພິສູດທັງໝົດ"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "ສົ່ງຄືນຈຳນວນເຕັມຂອງຜົນຫານ"}, "RADIANS": {"a": "(angle)", "d": "ປ່ຽນມູມອົງສາເປັນມູມຣາດຽນ (radians)"}, "RAND": {"a": "()", "d": "ສົ່ງຄືນຕົວເລກສຸ່ມທີ່ມີຄ່າຫຼາຍກວ່າ ຫຼືເທົ່າກັບ 0 ແຕ່ນ້ອຍກວ່າ 1, ທີ່ແຈກຢາຍຢ່າງເທົ່າໆກັນ (ຄ່າຈະຖືກປ່ຽນແປງເມື່ອເຮັດການຄຳນວນໃໝ່)"}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "ຕອບອະເຣຂອງຕົວເລກສຸ່ມ"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "ສົ່ງຄືນຕົວເລກສຸ່ມທີ່ມີຄ່າລະຫວ່າງຈຳນວນທີ່ທ່ານລະບຸ"}, "ROMAN": {"a": "(number; [form])", "d": "ປ່ຽນຕົວເລກອາລັບບິກໃຫ້ເປັນເລກໂລມັນ, ໃນຮູບແບບຂໍ້ຄວາມ"}, "ROUND": {"a": "(number; num_digits)", "d": "ປ່ຽນຈຳນວນເສດໃຫ້ເປັນຈຳນວນທີ່ມີຕຳແໜ່ງທົດສະນິຍົມຕາມທີ່ລະບຸ"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "ປັດເສດຈຳນວນລົງ"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "ປັດເສດຈຳນວນຂຶ້ນ"}, "SEC": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ຊີ​ແຄນ​ທ (secant) ຂອງ​ມູມ"}, "SECH": {"a": "(number)", "d": "ສົ່ງ​ຄືນ​ໄຮ​ເປີ​ບໍລິກ​ຊີ​​ແຄນ​ທ (hyperbolic secant) ຂອງ​ມູມ​ໃດ​ໜຶ່ງ"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "ສະ​ແດງ​ຜົນການ​ລວມ​ກັນ​ຂອງ​ຊຸດ​ກຳ​ລັງ​ຕ​າມ​ສູດ"}, "SIGN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າເຄື່ອງໝາຍຂອງຈຳນວນ: ເທົ່າກັບ1 ຖ້າຈຳນວນເປັນບວກ, ເທົ່າກັບ0 ຖ້າຈຳນວນເປັນສູນ, ຫຼື -1 ຖ້າຈຳນວນເປັນລົບ"}, "SIN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ ຊິນ"}, "SINH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກຊິນ (hyperbolic sine) ຂອງຈຳນວນທີ່ລະບຸ"}, "SQRT": {"a": "(number)", "d": "ສົ່ງຄືນຈຳນວນຂອງຮາກຂັ້ນສອງຕົວເລກທີ່ລະບຸ"}, "SQRTPI": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າຮາກຂັ້ນສອງຂອງ (number * pi)"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "ສົ່ງຄືນຄ່າຜົນລວມຍ່ອຍໃນລາຍການ ຫຼືຖານຂໍ້ມູນ"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "ບວກຈຳນວນທັງໝົດໃນຊ່ວງຂອງຫ້ອງ"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "ເຮັດການບວກຫ້ອງທີ່ຖືກກັບເງື່ອນໄຂ ຫຼືເກນທີ່ລະບຸ"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "ເພີ່ມ​ເຊວ​ລະ​ບຸ​ໄວ້​ໂດຍ​ຊຸດ​ເງື່ອ​ໄຂ ຫຼື​ມາດ​ຖານ​ທີ່​ໃຫ້​ໄວ້"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "ສົ່ງຄືນຜົນລວມຂອງຄ່າຈາກຜົນຄູນ ໂດຍແຕ່ລະຜົນຄູນເກີດຈາກການຄູນກັນລະຫວ່າງຕົວເລກທີ່ມີຕຳແໜ່ງສອດຄ່ອງກັນທີ່ຢູ່ຄົນລະຊ່ວງ ຫຼືຄົນລະອາ​ເຣຍ໌"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "ສົ່ງຄືນຜົນລວມຂອງຂໍ້ພິສູດຂຶ້ນກຳລັງສອງ. ຂໍ້​ພິສູດດັ່ງກ່າວເປັນໄດ້ທັງຕົວເລກ, ອາ​ເຣຍ໌, ຊື່, ຫຼືການອ້າງອີງຫາຫ້ອງທີ່ມີຕົວເລກ"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "ຫາຜົນລວມຂອງຄ່າຜົນຕ່າງກັນ ເຊິ່ງແຕ່ລະຄ່າເປັນຜົນຕ່າງກັນລະຫວ່າງຄ່າຂຶ້ນກຳລັງສອງຂອງ​ສອງຊ່ວງ ຫຼືສອງອາ​ເຣຍ໌"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "ສົ່ງຄືນຜົນລວມທັງໝົດຂອງຄ່າຜົນບວກ ເຊິ່ງແຕ່ລະຄ່າເປັນຜົນບວກລະຫວ່າງຄ່າຂຶ້ນກຳລັງສອງຂອງຕົວເລກທີ່ມີຕຳແໜ່ງສອດຄ່ອງກັນໃນຊ່ວງສອງຊ່ວງ ຫຼືສ​ອງອາ​ເຣຍ໌"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "ຫາຜົນລວມຂອງຄ່າຂຶ້ນກຳລັງສອງຂອງຜົນຕ່າງລະຫວ່າງຕົວເລກທີ່ມີຕຳແໜ່ງສອດຄ່ອງກັນໃນສອງຊ່ວງ ຫຼືສອງອາ​ເຣຍ໌"}, "TAN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ​ແທນ​ເຈັນ"}, "TANH": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າໄຮເປີໂບລິກ​ແທນ​ເຈັນ (hyperbolic tangent) ຂອງຈຳນວນທີ່ລະບຸ"}, "TRUNC": {"a": "(number; [num_digits])", "d": "ປັດເສດຕົວເລກຖີ້ມໃຫ້ເປັນຈຳນວນຖ້ວນ ໂດຍການເອົາທົດສະນິຍົມ ຫຼືສ່ວນເສດອອກ"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "ສ້າງການອ້າງອີງຫ້ອງໃນຮູບແບບຂໍ້ຄວາມ, ໂດຍໃຊ້ການລະບຸໝາຍເລກແຖວ ແລະ ໝາຍເລກຖັນ"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "ເລືອກຄ່າ ຫຼືຄຳສັ່ງທີ່ຈະດຳເນີນການຈາກລາຍການຂອງຄ່າ, ຕາມຕົວເລກດັດ​ສະ​ນີ"}, "COLUMN": {"a": "([reference])", "d": "ສົ່ງຄືນໝາຍເລກຖັນຂອງການອ້າງອີງ"}, "COLUMNS": {"a": "(array)", "d": "ສົ່ງຄືນຈຳນວນຂອງຖັນ​ໃນ​​ອາ​ເຣຍ໌ ຫຼືການອ້າງອີງທີ່ລະບຸ"}, "FORMULATEXT": {"a": "(reference)", "d": "ສົ່ງ​ຄືນ​ສູດ​ເປັນ​ສະຕຣິງ"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "ຊອກຫາຄ່າໃນແຖວເທິງສຸດຂອງຕາຕະລາງ ຫຼືອາ​ເຣຍ໌ຂອງຄ່າ ແລະສົ່ງຄືນຄ່າໃນຖັນ ດຽວກັນຈາກແຖວທີ່ທ່ານລະບຸ"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "ສ້າງທາງລັດເພື່ອເປີດເອກະສານທີ່ເກັບຢູ່ໃນຮາດ​​ດຣາຍຂອງທ່ານ,ໃນເຊີບເວີເຄື່ອຂ່າຍ, ຫຼືໃນອິນເຕີເນັດ"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "ສົ່ງຄືນຄ່າ ຫຼືການອ້າງອີງຂອງຫ້ອງຢູ່ຈຸດຕັດກັນລະຫວ່າງແຖວ ແລະຖັນ, ​ໃນ​ຊ່ວງທີ່ກໍານົດ​ໃຫ້"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "ສົ່ງຄືນຄ່າການອ້າງອີງທີ່ຖືກລະບຸຢູ່ໃນສາຍຂໍ້ຄວາມ"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "ຊອກຫາອາດຖຶກຄ່າຈາກຊ່ວງໜຶ່ງ-ແຖວ ຫຼື ໜຶ່ງ-ຖັນ ຫຼືຈາກອາ​ເຣຍ໌. ສະໜອງ ສຳລັບຄວາມສາມາດເຂົ້າກັນໄດ້ລະຫວ່າງເກົ່າ ແລະ ໃໝ່"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "ສົ່ງຄືນຄ່າທີ່ກ່ຽວຂ້ອງກັບຕຳແໜ່ງຂອງລາຍການໃນອາ​ເຣຍ໌ທີ່ກົງກັບຄ່າທີ່ລະບຸເຊິ່ງຢູ່ໃນລຳດັບການຈັດ ລຽງທີ່ລະບຸ"}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "ສົ່ງຄືນຄ່າທີ່ເປັນຜົນຈາກການອ້າງອີງຫາຊ່ວງທີ່ທີ່ຖືກລະບຸ ໂດຍຈຳນວນຂອງແຖວ ແລະຈຳນວນຂອງຖັນ ເຊິ່ງຖືກນັບຈາກການອ້າງອີງ"}, "ROW": {"a": "([reference])", "d": "ສົ່ງຄືນໝາຍເລກແຖວ ຂອງການອ້າງອີງ"}, "ROWS": {"a": "(array)", "d": "ສົ່ງຄືນຈຳນວນຂອງແຖວ​ໃນການອ້າງອີງ ຫຼືອາ​ເຣຍ໌"}, "TRANSPOSE": {"a": "(array)", "d": "ປ່ຽນຄ່າໃນຊ່ວງແນວຕັ້ງຂອງຫ້ອງໄປເປັນຄ່າໃນຊ່ວງແນວນອນ, ຫຼືປິ້ນກັນ"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "ຕອບຄ່າທີ່ບໍ່ຊ້ຳກັນຈາກໄລຍະ ຫຼື ອະເຣໃດໜຶ່ງ."}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "ຊອກຫາຄ່າໃນຖັນຊ້າຍສຸດຂອງຕາຕະລາງ, ແລ້ວ ສົ່ງກັບຄືນຄ່າໃນແຖວດຽວກັນຈາກຖັນທີ່ທ່ານລະບຸ. ຕາມຄ່າ​ເລີ່​ມຕົ້ນ, ຕາຕະລາງຕ້ອງຖືກລຽງລຳດັບຈາກນອ້ຍໄປຫາໃຫຍ່"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "ຊອກຫາໄລຍະ ຫຼື ອະເຣໃດໜຶ່ງແລ້ວຕອບລາຍການທີ່ກົງກັນຈາກໄລຍະ ຫຼື ອະເຣທີສອງ. ຕາມຄ່າເລີ່ມຕົ້ນ, ຄ່າທີ່ແນ່ນອນຈະຖືກໃຊ້"}, "CELL": {"a": "(info_type; [reference])", "d": "ຄືນຂໍ້ມູນກ່ຽວກັບການຈັດຮູບແບບ, ສະຖານທີ່, ຫຼື ເນື້ອໃນຂອງ ​ເຊ​ລ"}, "ERROR.TYPE": {"a": "(error_val)", "d": "ສົ່ງຄືນຄ່າຕົວເລກທີ່ກົງກັນກັບຄ່າ​ຜິດພາດ."}, "ISBLANK": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ການ​ອ້າງ​ອີງ​ແມ່ນ​ໄປ​ໃສ່​ເຊວ​ເປົ່າ​ບໍ, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False "}, "ISERR": {"a": "(value)", "d": "ກວດສອບເບິ່ງວ່າຄ່ານັ້ນເປັນຂໍ້ຜິດພາດນອກເໜືອໄປຈາກ #N/A ບໍ່ ແລະສົ່ງກັບຄ່າ TRUE ຫຼື FALSE"}, "ISERROR": {"a": "(value)", "d": "ກວດສອບເບິ່ງວ່າຄ່ານັ້ນເປັນຂໍ້ຜິດພາດຫຼືບໍ່ ແລະ ຕອບກັບ TRUE ຫຼື FALSE"}, "ISEVEN": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ TRUE ຖ້າຈຳນວນທີ່ລະບຸເປັນເລກຄູ່"}, "ISFORMULA": {"a": "(reference)", "d": "ກວດ​ເບິ່ງ​ວ່າການ​ອ້າງ​ອີງ​​ແມ່ນ​ການ​ອ້າງ​ອີງ​ໄປ​ຫາ​ຫ້ອງ​ທີ່​ມີ​ສຸດ​ຢູ່​ຫຼືບໍ່ ​ແລະ​ສົ່ງ​ຄືນ​ຄ່າ TRUE ຫຼື FALSE"}, "ISLOGICAL": {"a": "(value)", "d": "ກວດສອບຄ່າ ວ່າເປັນຄ່າທີ່ມີເຫດຜົນ (TRUE ຫຼື FALSE) ຫຼືບໍ່ ແລະ ສົ່ງກັບຄືນຄ່າ TRUE ຫຼື FALSE"}, "ISNA": {"a": "(value)", "d": "ກວດສອບເບິ່ງວ່າເປັນຄ່າ #N/A, ແລະສົ່ງກັບຄ່າ TRUE ຫຼື FALSE"}, "ISNONTEXT": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ຄ່າ​ບໍ່​ຢູ່​ໃນ​ຂໍ້​ຄວາມ (ເຊວ​ເປົ່າບໍ່​ແມ່ນ​ຂໍ້​ຄວາມ) ບໍ່, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False"}, "ISNUMBER": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ​ຄ່າ​ແມ່ນຕົວ​ເລກບໍ, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False "}, "ISODD": {"a": "(number)", "d": "ສົ່ງຄືນຄ່າ TRUE ຖ້າຈຳນວນທີ່ລະບຸເປັນເລກຄີກ "}, "ISREF": {"a": "(value)", "d": "ກວດສອບຫາຄ່າວ່າເປັນການອ້າງອີງຫຼືບໍ່, ແລ້ວ ສົ່ງກັບຄືນຄ່າ TRUE ຫຼື FALSE"}, "ISTEXT": {"a": "(value)", "d": "ກວດ​ເບິ່ງວ່າ ​ຄ່າ​ແມ່ນຂໍ​ຄວາມບໍ, ແລະ​ສະ​ແດງ​ຜົນ TRUE ຫຼື False "}, "N": {"a": "(value)", "d": "ປ່ຽນ​ຄ່າ​ບໍ່​ແມ່ນ​ຕົວ​ເລກ​ເປັນ​ຕົວ​ເລກ, ວັນ​ທີ​ເປັນ​ຕົວ​ເລກ​ລຳ​ດັບ, TRUE ເປັນ 1, ອັນ​ອື່ນໆ​ເປັນ 0 (ສູນ)"}, "NA": {"a": "()", "d": "ສົ່ງຄືນຄ່າຄວາມຜິດພາດ #N/A (ຄ່າທີ່ໃຊ້ບໍ່ໄດ້)"}, "SHEET": {"a": "([value])", "d": "ສົ່ງ​ຄືນ​ເລກທີ​ຂອງ​ແຜ່ນ​ວຽກ​ທີ່​ອ້າງ​ອີງ"}, "SHEETS": {"a": "([reference])", "d": "ສົ່ງ​ຄືນ​ຈໍານວນ​ຂອງ​ແຜ່ນ​ວຽກ​ໃນ​ການ​ອ້າງ​ອີງ"}, "TYPE": {"a": "(value)", "d": "ສະແດງຜົນຕົວເລກຖ້ວນແທນໃຫ້ກັບປະເພດຂໍ້ມູນຂອງຄ່າ: ຕົວເລກ = 1; ຂໍ້ຄວາມ = 2; ຄ່າໂລຈິກ = 4; ຄ່າຄວາມຜິດພາດ = 16; ອະເຣ  = 64; ຂໍ້ມູນປະສົມ = 128"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "ກວດກາເບິ່ງວ່າທຸກຂໍ້ພິສູດແມ່ນ TRUE, ແລະ ສົ່ງຄືນ TRUE ຖ້າທຸກຂໍ້ພິສູດແມ່ນ TRUE"}, "FALSE": {"a": "()", "d": "ສົ່ງກຄືນຄ່າຄວາມ​ຈິງ FALSE"}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "ກວດສອບວ່າເງື່ອນໄຂຖືກ​ຕອບ​ສະ​ໜອງ​ໄດ້ຫຼືບໍ່, ແລະສົ່ງຄືນຄ່າໜຶ່ງຖ້າເງື່ອນໄຂເປັນ TRUE ແລະສົ່ງຄືນອີກຄ່າໜຶ່ງ ຖ້າເງື່ອນໄຂເປັນ FALSE"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "ກວດສອບວ່າໜຶ່ງ ຫຼືຫຼາຍກວ່າໜຶ່ງເງື່ອນໄຂແມ່ນກົງກັນ ແລະຕອບຄ່າທີ່ສອດຄ່ອງກັບເງື່ອນໄຂ TRUE ທຳອິດ"}, "IFERROR": {"a": "(value; value_if_error)", "d": "ສະ​ແດງ​ຜົນ ຄ່າ_ຖ້າ_ຜິດ​ພາດ ຖ້າ​ສຳ​ນວນ​ແມ່ນ​ຄວາມ​ຜິດ​ພາດ ແລະ​ຄ່າ​ຂອງ​ສຳ​ນວນ​ມັນ​ເອງ"}, "IFNA": {"a": "(value; value_if_na)", "d": "ສົ່ງ​ຄືນ​ຄ່າ​ທີ່​ທ່ານ​ລະບຸ ຖ້າສໍານວນ​ໃຫ້​ຜົນ​ເປັນ#N/A, ບໍ່​ດັ່ງ​ນັ້ນ​ສົ່ງ​ຄືນ​ຜົນ​ຮັບ​ຂອງ​ສຳນວນ"}, "NOT": {"a": "(logical)", "d": "ປ່ຽນຄ່າຈາກ FALSE ໄປເປັນ TRUE, ຫຼື TRUE ໄປເປັນ FALSE"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "ກວດກາເບິ່ງວ່າມີທຸກຂໍ້ພິສູດແມ່ນ TRUE, ແລະສົ່ງຄືນ TRUE ຫຼື FALSE. ສົ່ງຄືນ ຄືນ FALSE ຖ້າຂໍ້ພິສູດທັງໝົດແມ່ນ FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "ປະ​ເມີນ​ຜົນ​ການ​ສະ​ແດງ​ອອກ​ຕໍ່​ລາຍ​ການ​ຂອງ​ຄ່າ ແລະ ​ໃຫ້​ຜົນ​ສອດ​ຄ້ອງ​ກັບ​ຄ່າ​ຖືກ​ກັນ​ທຳ​ອິດ. ຖ້າ​ມີ​ອັນ​ບໍ່​ກົງ​ກັນ, ຄ່າ​ມາດ​ຕະ​ຖານ​ທາງ​ເລືອກ​ຖືກ​ກັບ​ຄືນ​ມາ"}, "TRUE": {"a": "()", "d": "ສົ່ງຄືນຄ່າຄວາມ​ຈິງ TRUE"}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "ສົ່ງ​ຄືນ​ຜົນ​ຄ່າ​ຄວາມ​ຈິງ 'Exclusive OR' ຂອງ​ຂໍ້​ພິສູດ​ທັງ​ໝົດ"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "ສົ່ງຄືນຂໍ້ຄວາມທີ່ຢູ່ກ່ອນການຈຳກັດຕົວອັກສອນ."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "ສົ່ງຄືນຂໍ້ຄວາມທີ່ຢູ່ຫຼັງຈາກການຈຳກັດຕົວອັກສອນ."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "ແຍກຂໍ້ຄວາມອອກເປັນແຖວ ຫຼື ຖັນໂດຍໃຊ້ຕົວຂັ້ນ."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "ຕັດແຖວ ຫຼື ເວັກເຕີຖັນ ຫຼັງຈໍານວນທີ່ກໍານົດໄວ້ຂອງຄ່າທີ່ລະບຸ."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "ອະເຣແບບຊ້ອນກັນໃນແນວຕັ້ງເປັນອະເຣດຽວ."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "ອະເຣແບບຊ້ອນກັນໃນແນວນອນເປັນອະເຣດຽວ."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "ສົ່ງຄືນແຖວຈາກອະເຣ ຫຼືການອ້າງອີງ."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "ສົ່ງຄືນຖັນຈາກອະເຣ ຫຼືການອ້າງອີງ."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "ສົ່ງຄືນອະເຣເປັນຖັນດຽວ."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "ສົ່ງຄືນອະເຣເປັນແຖວດຽວ."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "ຕັດແຖວ ຫຼື ເວັກເຕີຖັນ ຫຼັງຈໍານວນທີ່ກໍານົດໄວ້ຂອງຄ່າທີ່ລະບຸ."}, "TAKE": {"a": "(array, rows, [columns])", "d": "ສົ່ງຄືນແຖວ ຫຼືຖັນຈາກອະເຣເລີ່ມຕົ້ນ ຫຼືສິ້ນສຸດ."}, "DROP": {"a": "(array, rows, [columns])", "d": "ວາງແຖວ ຫຼືຖັນຈາກອະເຣເລີ່ມຕົ້ນ ຫຼືສິ້ນສຸດ."}}