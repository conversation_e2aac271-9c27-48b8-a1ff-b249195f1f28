{"DATE": {"a": "(vuosi; kuukausi; pä<PERSON><PERSON>)", "d": "Palauttaa annetun päivämäärän järjestysnumeron päivämäärä-aika-koodissa"}, "DATEDIF": {"a": "(aloituspäivä; lopetuspäivä; yksikkö)", "d": "Laskee kahden päivämäärän välillä olevien päivien, kuukausien tai vuosien määrän"}, "DATEVALUE": {"a": "(päivämäärä_teksti)", "d": "Muuntaa päivämäärän tekstistä järjestysnumeroksi, joka vastaa päivämäärää päivämäärä-aika-koodauksessa"}, "DAY": {"a": "(järjestysnro)", "d": "<PERSON><PERSON><PERSON><PERSON> kuukauden pä<PERSON>än, luvun väliltä 1–31."}, "DAYS": {"a": "(lopetuspäivä; aloituspäivä)", "d": "Palauttaa päivän luvun kahden päivämäärän väliltä."}, "DAYS360": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [menetelmä])", "d": "Palauttaa kahden päivämäärän välisen päivien lukumäärän käyttämällä 360-päiväistä vuotta (12 kuukautta, joissa on 30 päivää)"}, "EDATE": {"a": "(aloituspäivä; ku<PERSON><PERSON>t)", "d": "Palauttaa sen päivän järjestysnumeron, joka on määrätyn kuukausimäärän verran ennen tai jälkeen alkupäivää."}, "EOMONTH": {"a": "(aloituspäivä; ku<PERSON><PERSON>t)", "d": "Palauttaa kuukauden viimeisen päivän järjestysnumeron, joka on määrätyn kuukausimäärän päässä ennen tai jälkeen aloituspäivämäärästä laskien."}, "HOUR": {"a": "(järjestysnro)", "d": "Palauttaa tunnin kokonaislukumuodossa. Arvo on välillä 0 (0:00)–23 (23:00)."}, "ISOWEEKNUM": {"a": "(päivämäärä)", "d": "Palauttaa annetun päivämäärän mukaisen vuoden ISO-standardin mukaisen viikon numeron"}, "MINUTE": {"a": "(järjestysnro)", "d": "Palauttaa minuutin lukuna väliltä 0–59."}, "MONTH": {"a": "(järjestysnro)", "d": "Palauttaa kuukauden järjestysnumeron 1 (tammikuu)–12 (jou<PERSON><PERSON><PERSON>)."}, "NETWORKDAYS": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [loma])", "d": "Palauttaa työpäivien lukumäärän kahden päivämäärän väliltä."}, "NETWORKDAYS.INTL": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [viikon<PERSON><PERSON>]; [loma])", "d": "Palauttaa työpäivien lukumäärän kahden päivämäärän väliltä mukautettava viikonloppuparametri huomioiden"}, "NOW": {"a": "()", "d": "Palau<PERSON><PERSON> n<PERSON>sen päivän ja ajan päivämäärän ja ajan muodos<PERSON>."}, "SECOND": {"a": "(järjestysnro)", "d": "Palauttaa sekunnin lukuna väliltä 0–59."}, "TIME": {"a": "(tunnit; minuutit; sekunnit)", "d": "<PERSON><PERSON><PERSON> lukuina annetut tunnit, minuutit ja sekunnit aikamuotoilluksi järjestysnumeroksi"}, "TIMEVALUE": {"a": "(a<PERSON>_teksti)", "d": "Muuntaa tekstimuotoisen ajan aikaa ilmaisevaksi järjestysnumeroksi. Numero 0 (0:00:00) muunnetaan muotoon 0,999988426 (23:59:59). Muotoile numero aikamuotoon kaavan kirjoittamisen jälkeen"}, "TODAY": {"a": "()", "d": "Palauttaa nykyisen päivämäärän päivämäärämuodossa."}, "WEEKDAY": {"a": "(järjestysn<PERSON>; [palauta_tyyppi])", "d": "Palauttaa viikonpäivän määrittävän numeron välillä 1 - 7."}, "WEEKNUM": {"a": "(järjestysn<PERSON>; [palauta_tyyppi])", "d": "Palauttaa viikon numeron."}, "WORKDAY": {"a": "(aloitusp<PERSON><PERSON><PERSON>; päiv<PERSON>; [loma])", "d": "Palauttaa sen päivän järjestysnumeron, joka määritettyjen työpäivien päässä aloituspäivästä."}, "WORKDAY.INTL": {"a": "(al<PERSON>usp<PERSON>iv<PERSON>; päiv<PERSON>; [viikon<PERSON><PERSON>]; [loma])", "d": "Palauttaa sen päivän järjestysnumeron, joka on määritettyjen työpäivien päässä aloituspäivästä, mukautettava viikonloppuparametri huomioiden"}, "YEAR": {"a": "(järjestysnro)", "d": "Palauttaa vuoden kokonaislukuna välillä 1900–9999."}, "YEARFRAC": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON>, joka ilmoittaa kuinka suuri osa vuodesta kuuluu aloituspäivän ja lopetuspäivän väliseen a<PERSON>an."}, "BESSELI": {"a": "(x; n)", "d": "Palauttaa muutetun Besselin funktion ln(x)."}, "BESSELJ": {"a": "(x; n)", "d": "Palauttaa Besselin funktion Jn(x)."}, "BESSELK": {"a": "(x; n)", "d": "Palauttaa muutetun Besselin funktion Kn(x)."}, "BESSELY": {"a": "(x; n)", "d": "Palauttaa Besselin funktion Yn(x)."}, "BIN2DEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON> des<PERSON>."}, "BIN2HEX": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>."}, "BIN2OCT": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON>."}, "BITAND": {"a": "(luku1; luku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden luvun bitti<PERSON>on 'Ja'"}, "BITLSHIFT": {"a": "(luku; siirrettävä_määrä)", "d": "<PERSON><PERSON><PERSON><PERSON> siirrettävän_määrän bittimäärän verran vasemmalle siirretyn luvun"}, "BITOR": {"a": "(luku1; luku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden luvun bittitason 'Tai'"}, "BITRSHIFT": {"a": "(luku; siirrettävä_määrä)", "d": "Palauttaa siirrettävän_määrän bittimäärän verran oikealle siirretyn luvun"}, "BITXOR": {"a": "(luku1; luku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden luvun bittitason 'Poissulkeva Tai'"}, "COMPLEX": {"a": "(re<PERSON><PERSON><PERSON>; imag_osa; [suffiksi])", "d": "<PERSON><PERSON><PERSON>- ja imaginaari<PERSON><PERSON><PERSON>t kompleksiluvuksi."}, "CONVERT": {"a": "(luku; yks<PERSON><PERSON><PERSON><PERSON>; yks<PERSON><PERSON><PERSON><PERSON>)", "d": "Muuttaa luvun toiseen järjestelmään."}, "DEC2BIN": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> k<PERSON>rjestelmän luvun binaariluvuksi."}, "DEC2HEX": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> k<PERSON>rjestelmän luvun heksades<PERSON>luvuks<PERSON>."}, "DEC2OCT": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> k<PERSON>j<PERSON>rjestelmän luvun oktaaliluvuksi."}, "DELTA": {"a": "(luku1; [luku2])", "d": "Testaa ovatko kaksi lukua y<PERSON>t."}, "ERF": {"a": "(alaraja; [yl<PERSON>raja])", "d": "Palauttaa virhefunktion."}, "ERF.PRECISE": {"a": "(X)", "d": "Palauttaa virhefunktion"}, "ERFC": {"a": "(x)", "d": "Palauttaa virhefunktion komplementin."}, "ERFC.PRECISE": {"a": "(X)", "d": "Palauttaa virhefunktion komplementin"}, "GESTEP": {"a": "(luku; [raja_arvo])", "d": "Testaa onko luku suurempi kuin raja-arvo."}, "HEX2BIN": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> bin<PERSON>."}, "HEX2DEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON> he<PERSON> desima<PERSON>luvu<PERSON>i."}, "HEX2OCT": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> ok<PERSON>lu<PERSON>."}, "IMABS": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun itseisarvon."}, "IMAGINARY": {"a": "(kompleksiluku)", "d": "<PERSON><PERSON><PERSON>a kompleksiluvun imaginaariosan kertoimen."}, "IMARGUMENT": {"a": "(kompleksiluku)", "d": "<PERSON><PERSON><PERSON><PERSON> argumentin q, joka on kulma radiaaneina."}, "IMCONJUGATE": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun konjugaattiluvun."}, "IMCOS": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun kosinin."}, "IMCOSH": {"a": "(luku)", "d": "Palauttaa kompleksiluvun hyperbolisen kosinin"}, "IMCOT": {"a": "(luku)", "d": "Palauttaa kompleksiluvun kotangentin"}, "IMCSC": {"a": "(luku)", "d": "Pa<PERSON><PERSON>a kompleksiluvun kosekantin"}, "IMCSCH": {"a": "(luku)", "d": "Palauttaa kompleksiluvun hyperbolisen kosekantin"}, "IMDIV": {"a": "(kompleksiluku1; kompleksiluku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden kompleksiluvun osamäärän."}, "IMEXP": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun eksponentin."}, "IMLN": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun luonnollisen logaritmin."}, "IMLOG10": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun kymmenkantaisen logaritmin."}, "IMLOG2": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun kaksikantaisen logaritmin."}, "IMPOWER": {"a": "(kompleksiluku; luku)", "d": "Palauttaa kompleksiluvun korotettuna kokonaislukupotenssiin."}, "IMPRODUCT": {"a": "(iluku1; [iluku2]; ...)", "d": "Palauttaa 1 - 255 kompleksiluvun tulon"}, "IMREAL": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun reaaliosan kertoimen."}, "IMSEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kompleksilu<PERSON>n se<PERSON>tin"}, "IMSECH": {"a": "(luku)", "d": "Palauttaa kompleksiluvun hyperbolisen sekantin"}, "IMSIN": {"a": "(kompleksiluku)", "d": "<PERSON><PERSON><PERSON><PERSON> kompleks<PERSON>vun sinin."}, "IMSINH": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON>a kompleksiluvun hyperbolisen sinin"}, "IMSQRT": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun neliöjuuren."}, "IMSUB": {"a": "(kompleksiluku1; kompleksiluku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden kompleksiluvun erotuksen."}, "IMSUM": {"a": "(iluku1; [iluku2]; ...)", "d": "Pa<PERSON><PERSON>a kompleksil<PERSON><PERSON>n summan"}, "IMTAN": {"a": "(iluku)", "d": "Pa<PERSON><PERSON>a kompleksiluvun tangentin"}, "OCT2BIN": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON>."}, "OCT2DEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON> desima<PERSON>."}, "OCT2HEX": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> he<PERSON>."}, "DAVERAGE": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Palauttaa valittujen tietok<PERSON>akenttien arvojen kes<PERSON>von"}, "DCOUNT": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "<PERSON><PERSON><PERSON>, mon<PERSON><PERSON>o annetun tietokannan solussa on ehdot täyttävä luku"}, "DCOUNTA": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "<PERSON><PERSON><PERSON>, moniko tietokannan tietoja sisältävä solu vastaa määrittämiäsi ehtoja"}, "DGET": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Poimii yksittäisiä ehdot täyttäviä tietueita tietokannasta"}, "DMAX": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "<PERSON><PERSON><PERSON><PERSON> valittujen tie<PERSON>n kent<PERSON>, määritetty<PERSON><PERSON> <PERSON><PERSON> arvon"}, "DMIN": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Palauttaa valittujen tietokannan kent<PERSON>, määritetyt ehdot täyttävän arvon"}, "DPRODUCT": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee määritetyt ehdot täyttävien tietokantakenttien tulon"}, "DSTDEV": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee populaation keskipoikkeaman otoksen perusteella käyttäen ehdon täyttävissä tietokantakentissä olevia arvoja"}, "DSTDEVP": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee keskihajonnan koko populaatiosta käyttäen ehdon täyttävissä tietokantakentissä olevia arvoja"}, "DSUM": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee ehdon tä<PERSON>tävissä tietokantakentissä olevien arvojen summan"}, "DVAR": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee populaation varianssin otoksen perusteella käyttäen ehdot täyttävissä tietokantakentissä olevia arvoja"}, "DVARP": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee populaation varianssin koko populaation perusteella käyttäen ehdot täyttävissä tietokantakentissä olevia arvoja"}, "CHAR": {"a": "(luku)", "d": "Palauttaa tietokoneen merkistössä annettua lukua vastaavan merkin"}, "CLEAN": {"a": "(teks<PERSON>)", "d": "Poistaa tekstistä kaikki merkit, jotka eivät tulostu"}, "CODE": {"a": "(teks<PERSON>)", "d": "Palauttaa tekstijonon ensimmäisen merkin numerokoodin tietokoneen käyttämässä merkistössä"}, "CONCATENATE": {"a": "(teksti1; [teksti2]; ...)", "d": "Yhdistää erilliset merkkijonot yhdeksi merkkijonoksi"}, "CONCAT": {"a": "(teksti1; ...)", "d": "Yhdistää luettelon tai alueen tekstimerkkijonot"}, "DOLLAR": {"a": "(luku; [desimaalit])", "d": "<PERSON><PERSON><PERSON> luvun valuutt<PERSON>uo<PERSON>iseksi tekstiksi"}, "EXACT": {"a": "(teksti1; teksti2)", "d": "<PERSON><PERSON><PERSON><PERSON>, o<PERSON><PERSON> ka<PERSON>i <PERSON>, ja palauttaa arvon TOSI tai EPÄTOSI. VERTAA-funktio ottaa huo<PERSON> kir<PERSON>"}, "FIND": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, josta toisen merkkijonon sisällä oleva merkkijono alkaa. FIND-arvo ottaa huomioon kirjainkoon"}, "FINDB": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "Etsivät merkkijonon toisen merkkijonon sisältä ja palauttavat luvun, joka ilmaisee etsittävän merkkijonon ensimmäisen merkin sijainnin toisen merkkijonon sisällä, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kun kirjoituskielen merkistön merkeissä on kaksi tavu<PERSON> (DBCS) - japani, kiina ja korea."}, "FIXED": {"a": "(luku; [desimaalit]; [ei_erotinta])", "d": "Muotoilee luvun tekstiksi, jossa on kiinteä määrä desimaaleja ja palauttaa tuloksen tekstinä"}, "LEFT": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa määritetyn määrän merkkejä tekstimerkkijonon alusta lukien"}, "LEFTB": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa merkkijonon ensimmäisen merkin tai ensimmäiset merkit määritetyn tavumäärän per<PERSON>, k<PERSON>ytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea"}, "LEN": {"a": "(teks<PERSON>)", "d": "Palauttaa tekstimerkkijonon merkkien määrän"}, "LENB": {"a": "(teks<PERSON>)", "d": "Palauttaa tekstimerkkijonossa olevien tavujen määrän, k<PERSON>ytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea"}, "LOWER": {"a": "(teks<PERSON>)", "d": "Muuntaa kaikki tekstissä olevat isot kirjaimet pieniksi"}, "MID": {"a": "(teksti; aloitusnro; merkit_luku)", "d": "Palauttaa tekstin keskeltä määritetyn määrän merkkejä aloittaen määrittämästäsi kohdasta"}, "MIDB": {"a": "(teksti; aloitusnro; merkit_luku)", "d": "Poimii merkkijonosta määrittämääsi tavumäärään perustuvan määrän merkkejä alkaen määrittämästäsi paikasta, käytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea"}, "NUMBERVALUE": {"a": "(teksti; [desima<PERSON><PERSON>tin]; [ry<PERSON><PERSON><PERSON>tin])", "d": "<PERSON><PERSON><PERSON> tekstin luvuksi maa-asetuksen itsenäisellä tavalla"}, "PROPER": {"a": "(teks<PERSON>)", "d": "Muuntaa jokaisen tekstimuotoisen sanan ensimmäisen kirjaimen isoksi kirjaimeksi ja kaikki muut kirjaimet pieniksi"}, "REPLACE": {"a": "(van<PERSON>_teksti; aloit<PERSON><PERSON><PERSON>; merkit_luku; uusi_teksti)", "d": "Korvaa merkkejä tekstissä"}, "REPLACEB": {"a": "(van<PERSON>_teksti; aloit<PERSON><PERSON><PERSON>; merkit_luku; uusi_teksti)", "d": "<PERSON>rvaa tekstimerkkijonon osan toisella tekstimerkkijonolla määritettyjen merkkien tavujen määrän perust<PERSON>, with a new set of characters, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea"}, "REPT": {"a": "(teksti; kerrat_luku)", "d": "Toistaa tekstin antamasi määrän kertoja. Voit käyttää funktiota saman tekstin kirjoittamiseen soluun useita kertoja"}, "RIGHT": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa määritetyn määrän merkkejä tekstimerkkijonon lopusta lukien"}, "RIGHTB": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa merkkijonon viimeisen merkin tai viimeiset merkit annetun merkkien tavumäärän perust<PERSON>, k<PERSON>ytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea"}, "SEARCH": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "Palauttaa sen merkin numeron, jossa etsittävä merkki tai merkkijono esiintyy ensimmäisen kerran. Merkkiä tai merkkijonoa etsitään vasemmalta o<PERSON>alle, eik<PERSON> kir<PERSON>a oteta huomioon"}, "SEARCHB": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "Paikantavat yhden merkkijonon toisen merkkijonon sisältä ja ne palauttavat luvun, joka vastaa ensimmäisen merkkijonon aloituskohtaa toisen merkkijojon ensimmäisestä kirjaimesta laskettuna, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea"}, "SUBSTITUTE": {"a": "(teksti; vanha_teksti; uusi_teksti; [esiintym<PERSON>_nro])", "d": "<PERSON><PERSON><PERSON> tekstissä olevan vanhan tekstin uudella tekstillä"}, "T": {"a": "(arvo)", "d": "Tark<PERSON>a, onko arvo tekstiä. Jos arvo on tekstiä, funktio palauttaa tekstin. Jos arvo ei ole tekstiä, funktio palauttaa tyhjät lainausmerkit"}, "TEXT": {"a": "(arvo; muoto_teksti)", "d": "Muotoilee luvun ja muuntaa sen tekstiksi"}, "TEXTJOIN": {"a": "(erotin; ohita_tyhjä; teksti1; ...)", "d": "Yhdistää luettelon tai alueen tekstimerkkijonot erottimella"}, "TRIM": {"a": "(teks<PERSON>)", "d": "Poistaa välit tekstimerkkijonosta paitsi yksittäiset sanojen välissä olevat välit"}, "UNICHAR": {"a": "(luku)", "d": "Palauttaa Unicode-<PERSON><PERSON>, johon annettu luku<PERSON> viittaa"}, "UNICODE": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> (koodipiste), joka vastaa tekstin ensimmäistä merk<PERSON>"}, "UPPER": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON> te<PERSON><PERSON> isoin kir<PERSON><PERSON><PERSON> kir<PERSON>i"}, "VALUE": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON> luku<PERSON>a kuvaavan merkkijonon luvuksi"}, "AVEDEV": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa hajontojen itseisarvojen keskiarvon. Argumentit voivat olla lukuja, ni<PERSON><PERSON>, matri<PERSON>ja tai viitta<PERSON>sia lukuihin"}, "AVERAGE": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa argumenttien <PERSON><PERSON><PERSON> k<PERSON>. Argumentit voivat olla lukuja, <PERSON><PERSON><PERSON>, matri<PERSON><PERSON> tai vii<PERSON><PERSON><PERSON>, jotka kohdistuvat lukuihin"}, "AVERAGEA": {"a": "(arvo1; [arvo2]; ...)", "d": "Palauttaa argumenttien (<PERSON><PERSON><PERSON><PERSON><PERSON>) keskiarvon. Argumentin teksti ja arvo EPÄTOSI lasketaan arvona 0. Arvo TOSI lasketaan arvona 1. Argumentit voivat olla lukuja, ni<PERSON><PERSON>, matriiseja tai viittauksia"}, "AVERAGEIF": {"a": "(alue; ehdot; [keski<PERSON>vo<PERSON>ue])", "d": "Määrittää tiettyjen ehtojen määrittämille soluille aritmeettisen keskiarvon"}, "AVERAGEIFS": {"a": "(keskiar<PERSON>alue; ehtoalue; ehdot; ...)", "d": "Määrittää tiettyjen ehtojen määrittämille soluille aritmeettisen keskiarvon"}, "BETADIST": {"a": "(x; alfa; beeta; [A]; [B])", "d": "Palauttaa kumulatiivisen beeta-todennäköisyystiheysfunktion arvon"}, "BETAINV": {"a": "(todennäköisyys; alfa; beeta; [A]; [B])", "d": "Palauttaa kumulatiivisen beeta-todennäköisyystiheysfunktion (BEETAJAKAUMA) käänteisarvon"}, "BETA.DIST": {"a": "(x; alfa; beeta; kertymä; [A]; [B])", "d": "Palauttaa beeta-todennäköisyysjakaumafunktion arvon"}, "BETA.INV": {"a": "(todennäköisyys; alfa; beeta; [A]; [B])", "d": "Palauttaa kumulatiivisen beeta-todennäköisyystiheysfunktion (BEETA.JAKAUMA) käänteisarvon"}, "BINOMDIST": {"a": "(luku_tot; yritykset; todennäköisyys_tot; kumulatiivinen)", "d": "Palauttaa yksittäisen termin binomijakauman todennäköisyyden"}, "BINOM.DIST": {"a": "(luku_tot; yritykset; todennäköisyys_tot; kumulatiivinen)", "d": "Palauttaa yksittäisen termin binomijakauman todennäköisyyden"}, "BINOM.DIST.RANGE": {"a": "(kokeet; todennäköisyys_s; luku_s; [luku_s2])", "d": "Palauttaa kokeen tuloksen todennäköisyyden binomijakaumaa käyttämällä"}, "BINOM.INV": {"a": "(yritykset; todennäköisyys_tot; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON>, jossa binomijak<PERSON><PERSON> kertymäfunktion arvo on pienempi tai yhtä suuri kuin eh<PERSON>vo"}, "CHIDIST": {"a": "(x; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> chi-ne<PERSON><PERSON>n jakauman to<PERSON>äköisyyden"}, "CHIINV": {"a": "(todennäköisyys; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-<PERSON><PERSON><PERSON>n o<PERSON>ole<PERSON>n jakauman k<PERSON>isa<PERSON>von"}, "CHITEST": {"a": "(todellinen_alue; odotettu_alue)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli chi-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> arvon ja vapausasteiden määrän"}, "CHISQ.DIST": {"a": "(x; vapausa<PERSON>et; kertym<PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-ne<PERSON><PERSON>n vasenhäntä<PERSON>n jakauman todennäköisyyden"}, "CHISQ.DIST.RT": {"a": "(x; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>ntäisen chi-ne<PERSON><PERSON>n jakauman todennäköisyyden"}, "CHISQ.INV": {"a": "(todennäköisyys; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-<PERSON><PERSON><PERSON>n vasenhäntäisen jakauman käänteisarvon"}, "CHISQ.INV.RT": {"a": "(todennäköisyys; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-<PERSON><PERSON><PERSON>n vasenhäntäisen jakauman käänteisarvon"}, "CHISQ.TEST": {"a": "(todellinen_alue; odotettu_alue)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli chi-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> arvo ja vapausasteiden määrän"}, "CONFIDENCE": {"a": "(alfa; keskihajonta; koko)", "d": "Palauttaa populaation keskiarvon luottamusvälin normaalijakaumaa käyttäen"}, "CONFIDENCE.NORM": {"a": "(alfa; keskihajonta; koko)", "d": "Palauttaa populaation keskiarvon luottamusvälin normaalijakaumaa käyttäen"}, "CONFIDENCE.T": {"a": "(alfa; keskihajonta; koko)", "d": "Palauttaa populaation keskiarvon luottamusvälin Studentin T-jakaumaa käyttäen"}, "CORREL": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa kahden tietoalueen välisen korrelaatiokertoimen"}, "COUNT": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee alueen lukuja sisältävien solujen määrän"}, "COUNTA": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee alueen tietoja sisältävien solujen määrän"}, "COUNTBLANK": {"a": "(alue)", "d": "Las<PERSON><PERSON> alueella olevien tyhjien solujen määrän"}, "COUNTIF": {"a": "(alue; ehdot)", "d": "Las<PERSON><PERSON> annetun alueen solut, jotka täyttävät annetut ehdot"}, "COUNTIFS": {"a": "(eh<PERSON><PERSON><PERSON>; ehdot; ...)", "d": "Laskee määritettyjen ehtojen palauttamien solujen määrän"}, "COVAR": {"a": "(matriisi1; matriisi2)", "d": "<PERSON><PERSON><PERSON><PERSON>, eli kahden arvojoukon kaikkien arvopisteparien hajontojen tulojen keski<PERSON>von"}, "COVARIANCE.P": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa popula<PERSON> k<PERSON><PERSON><PERSON>, eli kahden arvojoukon kaikkien arvopisteparien hajontojen tulojen keskiarvon"}, "COVARIANCE.S": {"a": "(matriisi1; matriisi2)", "d": "<PERSON><PERSON><PERSON><PERSON>, eli kahden arvojoukon kaikkien arvopisteparien hajontojen tulojen keskiarvon"}, "CRITBINOM": {"a": "(yritykset; todennäköisyys_tot; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON>, jossa binomijak<PERSON><PERSON> kertymäfunktion arvo on pienempi tai yhtä suuri kuin eh<PERSON>vo"}, "DEVSQ": {"a": "(luku1; [luku2]; ...)", "d": "Pa<PERSON>ttaa keskipoikkeamien neliösumman"}, "EXPONDIST": {"a": "(x; lambda; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa eksponent<PERSON>"}, "EXPON.DIST": {"a": "(x; lambda; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa eksponent<PERSON>"}, "FDIST": {"a": "(x; vapausaste1; vapausaste2)", "d": "Palauttaa F-todennäkö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (hajonnan aste) kahdesta tietosarjasta"}, "FINV": {"a": "(todennäköisyys; vapausaste1; vapausaste2)", "d": "Palauttaa k<PERSON>änteisen (oikeanpuoleisen) F-todennäköisyysjakauman. Jos p = FJAKAUMA(x,...), niin <PERSON>.KÄÄNT(p,...) = x"}, "FTEST": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa F-testin tuloksen eli kaksisuuntaisen todennäköisyyden sille, että matriisien 1 ja 2 varianssit eivät ole merkittävästi erilaisia"}, "F.DIST": {"a": "(x; vapausaste1; vapausaste2; kertymäfunktio)", "d": "Pa<PERSON><PERSON>a (vasenhäntäisen) F-todennäköisyysjakauman (hajonnan aste) kahdesta tietosarjasta"}, "F.DIST.RT": {"a": "(x; vapausaste1; vapausaste2)", "d": "Palauttaa (oikeahäntäisen) F-todennäköisyysjakauman (hajonnan aste) kahdesta tietosarjasta"}, "F.INV": {"a": "(todennäköisyys; vapausaste1; vapausaste2)", "d": "Palauttaa k<PERSON>änteisen (vasenhäntäisen) F-todennäköisyysjakauman. Jos p = F.JAKAUMA(x,...), niin <PERSON>(p,...) = x"}, "F.INV.RT": {"a": "(todennäköisyys; vapausaste1; vapausaste2)", "d": "Palauttaa käänteisen (oikeahäntäisen) F-todennäköisyysjakauman. Jos p = F.JAKAUMA.OH(x,...), niin F.KÄÄNT.OH(p,...) = x"}, "F.TEST": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa F-testin tuloksen eli kaksisuuntaisen todennäköisyyden sille, että matriisien 1 ja 2 varianssit eivät ole merkittävästi erilaisia"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>"}, "FISHERINV": {"a": "(y)", "d": "Palauttaa käänteisen Fisherin muunnoksen. Jos y = FISHER(x), niin FISHER.KÄÄNT(y) = x"}, "FORECAST": {"a": "(x; tunnettu_y; tunnettu_x)", "d": "Laskee tai ennustaa arvojen lineaarisen trendin aiempien arvojen perusteella"}, "FORECAST.ETS": {"a": "(mä<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>; arvo<PERSON>; aika<PERSON>; [kausivaihtelu]; [tieto<PERSON><PERSON>_viimeistely]; [koon<PERSON>])", "d": "Palauttaa tietyn tulevan määräpäivän ennustearvon eksponentiaalisella tasoitusmenetelmällä."}, "FORECAST.ETS.CONFINT": {"a": "(määr<PERSON><PERSON><PERSON><PERSON><PERSON>; arvo<PERSON>; aika<PERSON>; [luotta<PERSON><PERSON><PERSON><PERSON>]; [kausivaihtelu]; [tieto<PERSON><PERSON>_viimeistely]; [koon<PERSON>])", "d": "Palauttaa tietyn tulevan määräpäivän ennustearvon luottamusvälin."}, "FORECAST.ETS.SEASONALITY": {"a": "(arvot; aikajana; [tie<PERSON><PERSON>n_viimeistely]; [koon<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> mallin <PERSON>, jonka sovellus tunnistaa annet<PERSON>a a<PERSON>."}, "FORECAST.ETS.STAT": {"a": "(arvo<PERSON>; aikajana; tilastotyyppi; [kausivaihtelu]; [tie<PERSON><PERSON><PERSON>_viimeistely]; [koon<PERSON>])", "d": "<PERSON><PERSON><PERSON>a ennusteen pyydetyn tilastotiedon."}, "FORECAST.LINEAR": {"a": "(x; tunnettu_y; tunnettu_x)", "d": "Laskee tai ennustaa arvojen lineaarisen trendin aiempien arvojen perusteella"}, "FREQUENCY": {"a": "(tieto_matriisi; lohko_matriisi)", "d": "<PERSON><PERSON><PERSON>, kuinka usein arvot esiintyvät arvoalueessa ja palauttaa pystymatriisin, jossa on yksi elementti enemmän kuin Bins_matriisissa"}, "GAMMA": {"a": "(x)", "d": "Palauttaa Gamma-funktion arvon"}, "GAMMADIST": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa gamma-jakauman"}, "GAMMA.DIST": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa gamma-jakauman"}, "GAMMAINV": {"a": "(todennäköisyys; alfa; beeta)", "d": "Palauttaa käänteisen gamma-jakauman kertymäfunktion: jos p = GAMMAJAKAUMA(x,...), niin GAMMAJAKAUMA.KÄÄNT(p,...) = x"}, "GAMMA.INV": {"a": "(todennäköisyys; alfa; beeta)", "d": "Palauttaa käänteisen gamma-jakauman kertymäfunktion: jos p = GAMMA.JAKAUMA(x,...), niin GAMMA.JAKAUMA.KÄÄNT(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Palauttaa gamma-funktion luonnollisen logaritmin"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Palauttaa gamma-funktion luonnollisen logaritmin"}, "GAUSS": {"a": "(x)", "d": "Palauttaa 0,5 vähemmän kuin normaali kumulatiivinen vakiojakauma"}, "GEOMEAN": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa matriisin tai positiivisten lukuarvojen geometrisen keskiarvon"}, "GROWTH": {"a": "(tunnettu_y; [tunnettu_x]; [uusi_x]; [vakio])", "d": "Palauttaa eksponentiaalisen kasvutrendin luvut, jotka vastaavat tunnettuja tietopisteitä"}, "HARMEAN": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> harmonisen keskiarvon positiivisesta lukujou<PERSON>ta"}, "HYPGEOM.DIST": {"a": "(otos_tot; luku_otos; populaatio_tot; populaatio_luku; kertymä)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperge<PERSON><PERSON> j<PERSON>"}, "HYPGEOMDIST": {"a": "(otos_tot; luku_otos; populaatio_tot; populaatio_luku)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperge<PERSON><PERSON> j<PERSON>"}, "INTERCEPT": {"a": "(tunnettu_y; tunnettu_x)", "d": "Palauttaa lineaarisen regressiosuoran ja  y-a<PERSON><PERSON>. Regressiosuora piirretään tunnettujen x-arvojen ja y-arvojen avulla"}, "KURT": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa tietojoukon kurtosis-arvon"}, "LARGE": {"a": "(matriisi; k)", "d": "Palauttaa tietoalueen k:nneksi suurimman arvon. Esimerkiksi viidenneksi suurimman arvon"}, "LINEST": {"a": "(tunnettu_y; [tunnettu_x]; [vakio]; [tilasto])", "d": "<PERSON><PERSON><PERSON><PERSON>, jotka kuva<PERSON>t tietopisteisiin sovitettua lineaarista trendiä. <PERSON><PERSON> on laskettu neliösummamenetelmällä"}, "LOGEST": {"a": "(tunnettu_y; [tunnettu_x]; [vakio]; [tilasto])", "d": "<PERSON><PERSON><PERSON><PERSON> an<PERSON><PERSON><PERSON> tietopisteisiin sovitetun eksponentiaalisen käyrän tilastotiedot"}, "LOGINV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa x:n käänteisjakauman kumulatiivisesta ja logaritmisesta normaalijakaumasta, jossa ln(x) jakautuu normaalijakauman mukaisesti parametrien Keskiarvo ja Keskipoikkeama osoittamalla tavalla"}, "LOGNORM.DIST": {"a": "(x; keski<PERSON><PERSON>; keski<PERSON><PERSON><PERSON>; kertym<PERSON>)", "d": "Palauttaa x:n logaritmisen norm<PERSON><PERSON><PERSON><PERSON>, jossa ln(x) jakautuu normaal<PERSON> mukaisesti parametrien Ke<PERSON>arvo ja Keskihajonta osoittama<PERSON> ta<PERSON>la"}, "LOGNORM.INV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa x:n käänteisjakauman kumulatiivisesta ja logaritmisesta normaalijakaumasta, jossa ln(x) jakautuu normaalijakauman mukaisesti parametrien Keskiarvo ja Keskihajonta osoittamalla tavalla"}, "LOGNORMDIST": {"a": "(x; keski<PERSON><PERSON>; keskihajon<PERSON>)", "d": "Palauttaa x:n kumulatiivisen ja logaritmisen normaal<PERSON>, jossa ln(x) jakautuu normaal<PERSON> mukaisesti parametrien Keskiarvo ja Keskipoikkeama osoittamalla tavalla"}, "MAX": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa suurimman luvun arvojoukosta. Totuusarvot ja merkkijonot jätetään huomioimatta"}, "MAXA": {"a": "(arvo1; [arvo2]; ...)", "d": "Palauttaa arvojoukon suurimman arvon. Funktio ottaa my<PERSON> huo<PERSON>on loogiset arvot ja tekstin"}, "MAXIFS": {"a": "(ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; krite<PERSON>; ...)", "d": "Palauttaa suurimman arvon annetut ehdot täyttävistä soluista"}, "MEDIAN": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> annettujen lukujen mediaanin eli annetun lukujoukon keskimmäisen arvon"}, "MIN": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa pienimmän luvun arvojoukosta. Jättää  totuusarvot ja tekstin huomiotta"}, "MINA": {"a": "(arvo1; [arvo2]; ...)", "d": "Palauttaa arvojoukon pienimmän arvon. Funktio ottaa my<PERSON> huo<PERSON>on loogiset arvot ja tekstin"}, "MINIFS": {"a": "(v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; eh<PERSON><PERSON><PERSON>; ehdo<PERSON>; ...)", "d": "Palauttaa pienimmän arvon annetut ehdot täyttävistä soluista"}, "MODE": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa matriisissa tai tietoalueella useimmin tai toistu<PERSON>ti esiint<PERSON>vän arvon"}, "MODE.MULT": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa pystymatriisin matriisissa tai tietoalueella useimmin tai toistuvasti esiintyvistä arvoista. Jo<PERSON> haluat vaakamatriisin, käytä =TRANSPONOI(MOODI.USEA(luku1,luku2),...)-funktiota"}, "MODE.SNGL": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa matriisissa tai tietoalueella useimmin tai toistu<PERSON>ti esiint<PERSON>vän arvon"}, "NEGBINOM.DIST": {"a": "(luku_epäon; luku_tot; todennäköisyys_tot; kertymä)", "d": "Palauttaa negatiivisen binomijakauman eli todennäköisyyden, että ennen luku_tot:ttä onnistumista tapahtuu luku_epäon epäonnistumista onnistumistodennäköisyydellä todennäköisyys_tot"}, "NEGBINOMDIST": {"a": "(luku_epäon; luku_tot; todennäköisyys_tot)", "d": "Palauttaa negatiivisen binomijakauman eli todennäköisyyden, että ennen luku_tot:ttä onnistumista tapahtuu luku_epäon epäonnistumista onnistumistodennäköisyydellä todennäköisyys_tot"}, "NORM.DIST": {"a": "(x; kes<PERSON><PERSON><PERSON>; kes<PERSON><PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> määritetylle keskiarvolle ja -hajonnalle"}, "NORMDIST": {"a": "(x; kes<PERSON><PERSON><PERSON>; kes<PERSON><PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Pa<PERSON><PERSON><PERSON> kertymäfunktion määritetylle keskiarvolle ja -hajonnalle"}, "NORM.INV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa käänteisen normaalijakauman kertymäfunktion määritetylle keskiarvolle ja -hajonnalle"}, "NORMINV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa käänteisen normaalijakauman kertymäfunktion määritetylle keskiarvolle ja -hajonnalle"}, "NORM.S.DIST": {"a": "(z; kertymä)", "d": "Palauttaa normitetun normaalija<PERSON>uman (keskiarvo 0 ja keskihajonta 1)"}, "NORMSDIST": {"a": "(z)", "d": "Palauttaa normitetun normaal<PERSON>uman kertymäfunktion, jonka keskiarvo on 0 ja keskihajonta 1"}, "NORM.S.INV": {"a": "(todennäköisyys)", "d": "Palauttaa käänteisen normitetun normaalijakauman kertymäfunktion, jonka keskiar<PERSON> on nolla ja keskihajonta 1"}, "NORMSINV": {"a": "(todennäköisyys)", "d": "Palauttaa käänteisen normitetun normaalijakauman kertymäfunktion, jonka keskiar<PERSON> on nolla ja keskihajonta 1"}, "PEARSON": {"a": "(matriisi1; matriisi2)", "d": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>aati<PERSON>n"}, "PERCENTILE": {"a": "(matriisi; k)", "d": "Palauttaa k:nnen prosentti<PERSON>uuden alueen arvoista"}, "PERCENTILE.EXC": {"a": "(matriisi; k)", "d": "Palauttaa k:nnen prosentti<PERSON>uden alueen arvoista, jossa k on välillä 0 - 1 päätepisteet pois lukien"}, "PERCENTILE.INC": {"a": "(matriisi; k)", "d": "Palauttaa k:nnen prosentti<PERSON>uden alueen arvoista, jossa k on välillä 0 - 1 päätepisteet mukaan lukien"}, "PERCENTRANK": {"a": "(matriisi; x; [tark<PERSON>us])", "d": "Palautta<PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PERCENTRANK.EXC": {"a": "(matriisi; x; [tark<PERSON>us])", "d": "Palauttaa arvon prosenttijärjestyksen tietojoukossa prosenttijärjestyksenä (0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pois lukien) tietojoukossa"}, "PERCENTRANK.INC": {"a": "(matriisi; x; [tark<PERSON>us])", "d": "Palauttaa arvon prosenttijärjestyksen tietojoukossa prosenttijärjestyksenä (0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mukaan lukien) tietojoukossa"}, "PERMUT": {"a": "(luku; valittu_luku)", "d": "Palauttaa permutaatioiden määrän kaikista valittavissa olevista objekteista valituille objekteille"}, "PERMUTATIONA": {"a": "(luku; valittu_luku)", "d": "Palauttaa permutaatioiden määrän kaikista valittavissa olevista objekteista valituille objekteille (toistojen kanssa)"}, "PHI": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>funktion arvon norm<PERSON> v<PERSON>"}, "POISSON": {"a": "(x; kes<PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "POISSON.DIST": {"a": "(x; kes<PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "PROB": {"a": "(x_alue; todnäk_alue; alaraja; [yl<PERSON><PERSON><PERSON>])", "d": "Palauttaa todennäköisyyden sille, että alueen arvot ovat kahden rajan välissä tai yhtä suuria kuin alaraja"}, "QUARTILE": {"a": "(matriisi; pal_nelj<PERSON><PERSON>)", "d": "Palauttaa tietoalueen neljänneksen"}, "QUARTILE.INC": {"a": "(matriisi; pal_nelj<PERSON><PERSON>)", "d": "Palauttaa tietoalueen neljänneksen perustuen prosenttiarvoihin välillä 0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pois lukien"}, "QUARTILE.EXC": {"a": "(matriisi; pal_nelj<PERSON><PERSON>)", "d": "Palauttaa tietoalueen neljänneksen perustuen prosenttiarvoihin välillä 0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pois lukien"}, "RANK": {"a": "(luku; viittaus; [järje<PERSON><PERSON>])", "d": "Palau<PERSON>a luvun sija<PERSON>in lukuarvoluettelossa. <PERSON><PERSON><PERSON> arvon su<PERSON><PERSON><PERSON> muihin luette<PERSON> lukuihin"}, "RANK.AVG": {"a": "(luku; viittaus; [järje<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> luvun sijainnin luku<PERSON><PERSON><PERSON><PERSON><PERSON>, eli sen arvon su<PERSON><PERSON><PERSON> muihin luette<PERSON> lukuihin. <PERSON><PERSON> use<PERSON><PERSON><PERSON> kuin yhdellä arvolla on sama sijainti, funktio palauttaa keskimääräisen sijainnin"}, "RANK.EQ": {"a": "(luku; viittaus; [järje<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> luvun sijainnin luku<PERSON><PERSON><PERSON><PERSON>, eli sen arvon su<PERSON><PERSON><PERSON> muihin luette<PERSON> lukuihin. <PERSON><PERSON> use<PERSON><PERSON><PERSON> kuin yhdell<PERSON> arvolla on sama sijainti, funktio palauttaa arvojoukon arvojen korke<PERSON> sijainnin"}, "RSQ": {"a": "(tunnettu_y; tunnettu_x)", "d": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>relaatiokerto<PERSON>n ne<PERSON>ö<PERSON>, joka on laskettu annettujen arvopisteiden pohjalta"}, "SKEW": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa jakauman vinouden. <PERSON><PERSON>vo kuvaa jakauman keskittymistä keskiarvon ympärille"}, "SKEW.P": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa jakauman vinouden populaatioon perustuen: arvo kuvaa jakauman keskittymistä keskiarvon ympärille"}, "SLOPE": {"a": "(tunnettu_y; tunnettu_x)", "d": "Palauttaa lineaarisen regressiosuoran kulmakertoimen, joka on laskettu annettujen arvopisteiden pohjalta"}, "SMALL": {"a": "(matriisi; k)", "d": "Palauttaa k:nneksi pienimmän arvon tie<PERSON><PERSON>ue<PERSON>. Esimerkiksi viidenneksi pienimmän arvon"}, "STANDARDIZE": {"a": "(x; keski<PERSON><PERSON>; keskihajon<PERSON>)", "d": "Palauttaa normitetun arvon kes<PERSON>von ja -hajonnan määrittämästä jakaumasta"}, "STDEV": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation keskihajonnan otoksen perusteella (funktio ei huomioi näytteessä olevia totuusarvoja tai tekstiä)"}, "STDEV.P": {"a": "(luku1; [luku2]; ...)", "d": "Laskee populaation keskihajonnan koko argumentteina annetun populaation perusteella (funktio ei huomioi totuusarvoja tai tekstiä)"}, "STDEV.S": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation keskihajonnan otoksen perusteella (funktio ei huomioi näytteessä olevia totuusarvoja tai tekstiä)"}, "STDEVA": {"a": "(arvo1; [arvo2]; ...)", "d": "Arvioi keskipoikkeamaa näytteen pohjalta. Funktio huomioi myös totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1"}, "STDEVP": {"a": "(luku1; [luku2]; ...)", "d": "Laskee populaation keskihajonnan koko populaation perusteella (funktio ei huomioi totuusarvoja tai tekstiä)"}, "STDEVPA": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee koko populaation keskipoikkeaman. Funktio ottaa myö<PERSON> huo<PERSON>on totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1"}, "STEYX": {"a": "(tunnettu_y; tunnettu_x)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> x-ar<PERSON>a vast<PERSON> ennus<PERSON> y-arvon k<PERSON>n"}, "TDIST": {"a": "(x; vapausa<PERSON><PERSON>; suunta)", "d": "<PERSON><PERSON><PERSON><PERSON> t-jaka<PERSON>"}, "TINV": {"a": "(todennäköisyys; vapausasteet)", "d": "Palauttaa käänteisen t-jakauman"}, "T.DIST": {"a": "(x; vapausa<PERSON>et; kertym<PERSON>)", "d": "Palauttaa vasenhäntäisen Studentin t-jakauman"}, "T.DIST.2T": {"a": "(x; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON><PERSON> t-jakauman"}, "T.DIST.RT": {"a": "(x; vapausasteet)", "d": "Pa<PERSON>ttaa oikeahäntäisen Studentin t-jakauman"}, "T.INV": {"a": "(todennäköisyys; vapausasteet)", "d": "Palauttaa käänteisen vasenhäntäisen t-jakauman"}, "T.INV.2T": {"a": "(todennäköisyys; vapausasteet)", "d": "Palauttaa käänteisen kaksisuuntaisen t-jakauman"}, "T.TEST": {"a": "(matriisi1; matriisi2; suunta; laji)", "d": "Palauttaa t-testiin liittyvän todennäköisyyden"}, "TREND": {"a": "(tunnettu_y; [tunnettu_x]; [uusi_x]; [vakio])", "d": "Palauttaa lineaarisen trendin numerot sovittamalla tunnetut tietopisteet ja käyttäen pienemmän neliön menetelmää"}, "TRIMMEAN": {"a": "(matri<PERSON>; prosentti)", "d": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>un keski<PERSON>"}, "TTEST": {"a": "(matriisi1; matriisi2; suunta; laji)", "d": "Palauttaa t-testiin liittyvän todennäköisyyden"}, "VAR": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation varians<PERSON> o<PERSON>sen perusteella (jättää huomiotta otoksessa olevat totuusarvot ja tekstit)"}, "VAR.P": {"a": "(luku1; [luku2]; ...)", "d": "Laskee varianssin koko populaation perusteella (jättää huomiotta populaatiossa olevat totuusarvot ja tekstit)"}, "VAR.S": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation varians<PERSON> o<PERSON>sen perusteella (jättää huomiotta otoksessa olevat totuusarvot ja tekstit)"}, "VARA": {"a": "(arvo1; [arvo2]; ...)", "d": "Arvioi varianssia näytteen pohjalta. Funktio ottaa myö<PERSON> huomioon  totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1"}, "VARP": {"a": "(luku1; [luku2]; ...)", "d": "Laskee varianssin koko populaation perusteella (jättää huomiotta populaatiossa olevat totuusarvot ja tekstit)"}, "VARPA": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee koko populaation varianssin. Funktio ottaa myös huo<PERSON>on totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1"}, "WEIBULL": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "WEIBULL.DIST": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "Z.TEST": {"a": "(matriisi; x; [sigma])", "d": "Palauttaa yksisuuntaisen z-testin P-arvon"}, "ZTEST": {"a": "(matriisi; x; [sigma])", "d": "Palauttaa yksisuuntaisen z-testin P-arvon"}, "ACCRINT": {"a": "(asettamispvm; alkukorko; tilityspvm; korko; nimellisarvo; korkojakso; [peruste]; [laskentamenetelmä])", "d": "<PERSON><PERSON><PERSON><PERSON> kausitt<PERSON>ta korkoa maksavalle arvo<PERSON> kertyneen koron."}, "ACCRINTM": {"a": "(asettamispvm; tilityspvm; korko; nimellisarvo; [peruste])", "d": "Palauttaa eräpäivänä korkoa maksavalle arvo<PERSON> kertyneen koron."}, "AMORDEGRC": {"a": "(kustannus; ostopäivämäär<PERSON>; ensimmäinen_kausi; loppuarvo; kausi; korko; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kunkin til<PERSON>n valmiskorkoisen suoran poiston."}, "AMORLINC": {"a": "(kustannus; ostopäivämäär<PERSON>; ensimmäinen_kausi; loppuarvo; kausi; korko; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kunkin til<PERSON>n valmiskorkoisen suoran poiston."}, "COUPDAYBS": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa koronmaksupäivien lukumäärän korkokauden alusta tilityspäivämäärään asti."}, "COUPDAYS": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palau<PERSON>a koronmaksukauden päivien lukumäärän, joka sisältää tilityspäivän."}, "COUPDAYSNC": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa tilityspäivän ja se<PERSON>avan koronmaksupäivän välisen a<PERSON>jakson päivien lukumäärän."}, "COUPNCD": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa tilityspäivämäärää seuraavan koronmaksupäivän."}, "COUPNUM": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa maksettavien korkosuoritusten lukumäärän tilitys- ja erääntymispäivämäärän välillä."}, "COUPPCD": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa tilityspäivää edeltävän korkopäivän."}, "CUMIPMT": {"a": "(korko; kaudet_yht; nykyarvo; ens_kausi; viim_kausi; laji)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden kauden välillä maksetun kumulatiivisen koron."}, "CUMPRINC": {"a": "(korko; kaudet_yht; nykyarvo; ens_kausi; viim_kausi; laji)", "d": "Palauttaa kumulatiivisen lyhennyksen kahden jakson v<PERSON>llä."}, "DB": {"a": "(kustannus; loppuarvo; aika; kausi; [kuukausi])", "d": "<PERSON><PERSON><PERSON><PERSON> kauden kirjan<PERSON>ollisen poiston am<PERSON><PERSON><PERSON><PERSON>-men<PERSON>lm<PERSON>n (Fixed-declining balance) mukaan"}, "DDB": {"a": "(kustannus; loppuarvo; aika; kausi; [kerroin])", "d": "<PERSON><PERSON><PERSON><PERSON> kauden kirjanpidollisen poiston amerikka<PERSON>sen DDB-menetemän (Double-Declining Balance) tai jonkun muun määrittämäsi menetelmän mukaan"}, "DISC": {"a": "(tilityspvm; erääntymispvm; hinta; lunastushinta; [peruste])", "d": "Palauttaa arvopaperin diskonttokoron."}, "DOLLARDE": {"a": "(valuutta_murtoluku; nimitt<PERSON>j<PERSON>)", "d": "<PERSON><PERSON><PERSON> murtolukuna esitetyn luvun kymmenjärjestelmän luvuksi."}, "DOLLARFR": {"a": "(valuutta_des; nimitt<PERSON>j<PERSON>)", "d": "<PERSON><PERSON><PERSON> kym<PERSON>rjestelmän luvun murtoluvuksi."}, "DURATION": {"a": "(tilityspvm; erääntymispvm; korko; tuotto; korko<PERSON><PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kausitt<PERSON>ta korkoa maksavan arvo<PERSON>in keston vuosina."}, "EFFECT": {"a": "(nimelliskorko; korkojaksot)", "d": "Palauttaa efektiivisen vuosikorkokannan."}, "FV": {"a": "(korko; kaudet_yht; erä; [nykyarvo]; [laji])", "d": "Palauttaa tasavälisiin vakiomaksueriin ja kiinteään korkoon perustuvan lainan tai sijoituksen tulevan arvon"}, "FVSCHEDULE": {"a": "(nykyarvo; korot)", "d": "Pa<PERSON><PERSON><PERSON> tulevan arvon, joka on saatu käyttämällä erilaisia korkokantoja."}, "INTRATE": {"a": "(tilityspvm; erääntymispvm; sijoitus; lunastushinta; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON>."}, "IPMT": {"a": "(korko; kausi; kaudet_yht; nykyarvo; [ta]; [laji])", "d": "Palauttaa sijoitukselle tiettynä ajan<PERSON>a k<PERSON>vän koron, joka pohjautuu säännöllisiin vakioeriin ja kiinteään korkoprosenttiin"}, "IRR": {"a": "(arvot; [arvaus])", "d": "Palauttaa sisäisen korkokannan toistuvista kassavirroista muodostuvalle sar<PERSON>"}, "ISPMT": {"a": "(korko; kausi; kaudet_yht; nykyar<PERSON>)", "d": "Palauttaa tietyn sijoituskauden lainanmaksukoron"}, "MDURATION": {"a": "(tilityspvm; erääntymispvm; korko; tuotto; korko<PERSON><PERSON>; [peruste])", "d": "Palauttaa muunnetun Macauleyn keston arvopaperille (100 euron nimellisarvo)."}, "MIRR": {"a": "(arvot; pääoma_korko; uudinvest_korko)", "d": "Palauttaa sisäisen korkokannan sarjalle jat<PERSON><PERSON> ka<PERSON>, j<PERSON><PERSON> huo<PERSON> my<PERSON> sijo<PERSON> arvo ja uudelleen sijoittamisen korko"}, "NOMINAL": {"a": "(tod_korko; korkojaksot)", "d": "Palauttaa vuosittaisen nimellisk<PERSON>n."}, "NPER": {"a": "(korko; erä; nykyarvo; [ta]; [laji])", "d": "<PERSON><PERSON><PERSON><PERSON> kausien mä<PERSON><PERSON><PERSON><PERSON>, joka perustuu ta<PERSON>, kii<PERSON><PERSON><PERSON> maksuihin ja kiinte<PERSON>än kor<PERSON>p<PERSON>in"}, "NPV": {"a": "(korko; arvo1; [arvo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka perustuu toistuvista kassavirroista muodos<PERSON><PERSON> sar<PERSON> (ma<PERSON><PERSON><PERSON> ja tulo<PERSON>)  ja kor<PERSON>"}, "ODDFPRICE": {"a": "(tilityspvm; erääntymispvm; asettamispvm; ens_korko; korko; tuotto; lunastushinta; korkojak<PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON> (100 euron nimellisarvo) <PERSON><PERSON><PERSON><PERSON>, j<PERSON> en<PERSON><PERSON><PERSON><PERSON> kaus<PERSON> on normaalista poikkeava."}, "ODDFYIELD": {"a": "(tilityspvm; erääntymispvm; asettamispvm; ens_korko; korko; hinta; lunastushinta; korkojakso; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON> tuoton <PERSON>, jossa en<PERSON><PERSON><PERSON><PERSON> kaus<PERSON> on normaalista poikkeava."}, "ODDLPRICE": {"a": "(tilityspvm; erääntymispvm; viim_korko; korko; tuotto; lunast<PERSON><PERSON>a; kor<PERSON><PERSON><PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> arvo<PERSON><PERSON> hi<PERSON> (100 euron nimellisarvo) <PERSON><PERSON><PERSON><PERSON>, jossa vii<PERSON><PERSON> kausi on normaalista poikkeava."}, "ODDLYIELD": {"a": "(tilityspvm; erääntymispvm; viim_korko; korko; hinta; lunastushinta; korkojakso; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> ar<PERSON><PERSON><PERSON> tuoton til<PERSON>, jossa vii<PERSON><PERSON> ka<PERSON> on normaalista poikkeava."}, "PDURATION": {"a": "(korko; nykyarvo; tuleva_arvo)", "d": "<PERSON><PERSON><PERSON>a sijoituk<PERSON>sa tarvittavien jaksojen mä<PERSON><PERSON><PERSON>, jotta määritetty arvo <PERSON> saavuttaa"}, "PMT": {"a": "(korko; kaudet_yht; nykyarvo; [ta]; [laji])", "d": "Palau<PERSON><PERSON> lainan kausittaisen maksun. <PERSON>na perustuu tasa<PERSON>in ja kiinte<PERSON>än korkoon"}, "PPMT": {"a": "(korko; kausi; kaudet_yht; nykyarvo; [ta]; [laji])", "d": "Palauttaa pääoman lyhennyksen annetulla kaude<PERSON>, kun käytetään tasaeriä ja kiinteää korkoa"}, "PRICE": {"a": "(tilityspvm; erä<PERSON>ntymispvm; korko; tuotto; luna<PERSON><PERSON><PERSON>a; kor<PERSON><PERSON><PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kausitt<PERSON>ta korkoa maksavan a<PERSON>vo<PERSON> hinnan (100 euron nimellisarvo)"}, "PRICEDISC": {"a": "(tilityspvm; erääntymispvm; diskonttokorko; lunast<PERSON>inta; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> arvo<PERSON><PERSON> hinnan (100 euron nimellisarvo)"}, "PRICEMAT": {"a": "(tilityspvm; erääntymispvm; asettamispvm; korko; tuotto; [peruste])", "d": "Palauttaa erääntymispäivänä korkoa maksavan arvo<PERSON> hinnan (100 euron nimellisarvo)"}, "PV": {"a": "(korko; kaudet_yht; erä; [ta]; [laji])", "d": "<PERSON><PERSON><PERSON><PERSON>"}, "RATE": {"a": "(kaudet_yht; erä; nykyarvo; [ta]; [laji]; [arvaus])", "d": "Palauttaa sijoituksen tai lainan kausittaisen korkokannan. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuosik<PERSON><PERSON> on 6 %"}, "RECEIVED": {"a": "(tilityspvm; erääntymispvm; sijoitus; disk<PERSON><PERSON><PERSON><PERSON>; [peruste])", "d": "Palauttaa arvopaperista erääntymispäivänä saatavan rahasumman."}, "RRI": {"a": "(nper; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON> si<PERSON> kasvun vast<PERSON> k<PERSON>"}, "SLN": {"a": "(kustannus; loppuarvo; aika)", "d": "<PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON> tasapoiston yhdeltä kaudelta"}, "SYD": {"a": "(kustannus; loppuarvo; aika; kausi)", "d": "<PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON> vuosipoiston annettuna kautena käyttäen amerikkalaista SYD-menetelmää (Sum-of-Year's Digits)"}, "TBILLEQ": {"a": "(tilityspvm; erääntymispvm; diskonttokorko)", "d": "Palauttaa obligaation tuoton."}, "TBILLPRICE": {"a": "(tilityspvm; erääntymispvm; diskonttokorko)", "d": "Palauttaa obligaation hinnan (100 euron nimellisarvo)"}, "TBILLYIELD": {"a": "(tilityspvm; erääntymispvm; hinta)", "d": "Palauttaa obligaation tuoton."}, "VDB": {"a": "(kustannus; loppuarvo; aika; ens_kausi; viim_kausi; [kerroin]; [ei_siirtoa])", "d": "<PERSON><PERSON><PERSON><PERSON> sijoit<PERSON>sen kaksinkertaisen kirjanpidon tai muun määritetyn menetelmän mukaisen poiston millä hyvänsä annet<PERSON>a kaudella, muka<PERSON><PERSON><PERSON> osittaiset kaudet"}, "XIRR": {"a": "(arvot; päivät; [arvaus])", "d": "Pa<PERSON>ttaa rahasuoritusten sarjan si<PERSON>n kor<PERSON>kan<PERSON>."}, "XNPV": {"a": "(korko; arvot; päivät)", "d": "<PERSON><PERSON><PERSON><PERSON> maksus<PERSON><PERSON><PERSON>en sar<PERSON>."}, "YIELD": {"a": "(tilityspvm; erääntymispvm; korko; hinta; lunastushinta; korkojakso; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> jaksott<PERSON>ta korkoa maksavan arvo<PERSON>in tuoton."}, "YIELDDISC": {"a": "(tilityspvm; erääntymispvm; hinta; lunastushinta; [peruste])", "d": "Palauttaa vuosittaisen tuoton diskontatulle arvo<PERSON>ille."}, "YIELDMAT": {"a": "(tilityspvm; erääntymispvm; asettamispvm; korko; hinta; [peruste])", "d": "<PERSON><PERSON><PERSON>a vuosittaisen tuo<PERSON><PERSON>, joka maksaa korkoa erääntymispäivänä."}, "ABS": {"a": "(luku)", "d": "Palauttaa luvun itseisarvon eli luvun ilman etumerkkiä."}, "ACOS": {"a": "(luku)", "d": "Palauttaa luvun arcuskosinin radiaaneina väliltä 0 - pii. <PERSON><PERSON><PERSON><PERSON> on kulma, jonka kosini on luku"}, "ACOSH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen kosinin käänteisfunktion arvon"}, "ACOT": {"a": "(luku)", "d": "Palauttaa luvun arkuskotangentin radiaaneina 0 - pii."}, "ACOTH": {"a": "(luku)", "d": "Palauttaa luvun käänteisen hyperbolisen kotangentin"}, "AGGREGATE": {"a": "(funktion_nro; asetukset; viittaus1; ...)", "d": "Palauttaa koosteen luettelona tai tietok<PERSON>ana"}, "ARABIC": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON>alaiset numerot arabialaisiksi numeroiksi"}, "ASC": {"a": "(teks<PERSON>)", "d": "Funktio muuttaa DBCS (Double-byte character set) -merkit SBCS (Single-byte character set) -merkist<PERSON>n merkeiksi DBCS-merkistöä edellyttäviä kieliä käytettäessä"}, "ASIN": {"a": "(luku)", "d": "Palauttaa luvun arcussinin radiaaneina välillä -pii/2 - pii/2"}, "ASINH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen sinin käänteisfunktion arvon"}, "ATAN": {"a": "(luku)", "d": "Palauttaa luvun arcustangentin radiaaneina. <PERSON><PERSON> on välillä -pii/2 - pii/2"}, "ATAN2": {"a": "(x_luku; y_luku)", "d": "<PERSON><PERSON><PERSON><PERSON> pisteen (x,y) arcustangentin radiaaneina. <PERSON><PERSON> on välillä -pii - pii, pois<PERSON>ien -pii"}, "ATANH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen tangentin käänteisfunktion arvon"}, "BASE": {"a": "(luku; kantaluku; [vähimmäispituus])", "d": "<PERSON><PERSON><PERSON> luvun teksti<PERSON><PERSON><PERSON> annetulla kantal<PERSON> (kanta)"}, "CEILING": {"a": "(luku; tarkkuus)", "d": "Pyöristää luvun ylöspäin lähimpään tarkkuuden kerrannaiseen"}, "CEILING.MATH": {"a": "(luku; [tarkkuus]; [tila])", "d": "Pyöristää luvun ylöspäin seuraavaan kokonaislukuun tai seuraavaan tarkkuuden <PERSON>taan"}, "CEILING.PRECISE": {"a": "(luku; [tarkkuus])", "d": "Palauttaa luvun pyöristettynä lähimpään kokonaislukuun tai tarkkuuden kerrannaiseen"}, "COMBIN": {"a": "(luku; valittu_luku)", "d": "<PERSON><PERSON><PERSON><PERSON> annettujen objektien kombinaatioiden määrän"}, "COMBINA": {"a": "(luku; valittu_luku)", "d": "Palauttaa yhdistelmien määrän toistoineen tietylle määrälle kohteita"}, "COS": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> annetun kulman kosinin"}, "COSH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen kosinin"}, "COT": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman kotangentin"}, "COTH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen kotangentin"}, "CSC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman k<PERSON>"}, "CSCH": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman hyperbolisen kose<PERSON>tin"}, "DECIMAL": {"a": "(luku; kantaluku)", "d": "<PERSON><PERSON><PERSON> annetun kannan luvun tekstimuodon desima<PERSON>luvuksi"}, "DEGREES": {"a": "(kulma)", "d": "<PERSON><PERSON><PERSON> r<PERSON><PERSON>"}, "ECMA.CEILING": {"a": "(luku; tarkkuus)", "d": "Pyöristää luvun ylöspäin lähimpään tarkkuuden kerrannaiseen"}, "EVEN": {"a": "(luku)", "d": "Pyöristää positiivisen luvun ylöspäin ja negatiivisen luvun alaspäin lähimpään parilliseen kokonaislukuun"}, "EXP": {"a": "(luku)", "d": "Palauttaa e:n korotettuna annettuun potenssiin"}, "FACT": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun k<PERSON>, eli 1*2*3*...*luku"}, "FACTDOUBLE": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun o<PERSON>."}, "FLOOR": {"a": "(luku; tarkkuus)", "d": "Pyöristää luvun alaspäin lähimpään tarkkuuden kerrannaiseen"}, "FLOOR.PRECISE": {"a": "(luku; [tarkkuus])", "d": "Palauttaa luvun pyöristettynä alaspäin lähimpään kokonaislukuun tai tarkkuuden kerrannaiseen"}, "FLOOR.MATH": {"a": "(luku; [tarkkuus]; [tila])", "d": "Pyöristää luvun alaspäin seuraavaan kokonaislukuun tai seuraavaan tarkkuuden monikertaan"}, "GCD": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> suurimman y<PERSON><PERSON><PERSON> j<PERSON>"}, "INT": {"a": "(luku)", "d": "Pyöristää luvun alaspäin lähimpään kokonai<PERSON>lukuun"}, "ISO.CEILING": {"a": "(luku; [tarkkuus])", "d": "Palauttaa luvun pyöristettynä lähimpään kokonaislukuun tai tarkkuuden kerrannaiseen. Luvun etumerkistä huolimatta arvo pyöristetään aina ylöspäin. <PERSON><PERSON> luku tai tarkkuus on pariton kokonaisluku, sitä ei pyöristetä."}, "LCM": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> pie<PERSON>än yhtei<PERSON> kertojan"}, "LN": {"a": "(luku)", "d": "Palauttaa luvun luonnollisen logaritmin"}, "LOG": {"a": "(luku; [kanta])", "d": "Palauttaa luvun logaritmin käyttämällä annettua kantal<PERSON>a"}, "LOG10": {"a": "(luku)", "d": "<PERSON>lautta<PERSON> luvun 10-kanta<PERSON>n logaritmin"}, "MDETERM": {"a": "(matriisi)", "d": "Palauttaa matriisin matriisideterminantin"}, "MINVERSE": {"a": "(matriisi)", "d": "Palauttaa matriisin käänteismatriisin"}, "MMULT": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa kahden matriisin tulon. <PERSON><PERSON><PERSON><PERSON>a on yhtä monta riviä kuin matriisissa 1 ja yhtä monta saraketta kuin matriisissa 2"}, "MOD": {"a": "(luku; jakaja)", "d": "Palauttaa jakoj<PERSON><PERSON><PERSON><PERSON><PERSON>en"}, "MROUND": {"a": "(luku; kerran<PERSON><PERSON>)", "d": "Palauttaa luvun pyöristettynä haluttuun kerrannaiseen."}, "MULTINOMIAL": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON>a lukujoukon multinomin"}, "MUNIT": {"a": "(ulottuvuus)", "d": "<PERSON><PERSON><PERSON><PERSON> valitun ul<PERSON>n yksikön matriisin"}, "ODD": {"a": "(luku)", "d": "Pyöristää positiivisen luvun ylöspäin ja negatiivisen luvun alaspäin lähimpään parittomaan kokonai<PERSON>uun"}, "PI": {"a": "()", "d": "Palauttaa piin likiarvon 15 numeron tarkkuudella (3,14159265358979)"}, "POWER": {"a": "(luku; potenssi)", "d": "Palauttaa luvun korotettuna haluttuun potenssiin"}, "PRODUCT": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> tulon"}, "QUOTIENT": {"a": "(oso<PERSON><PERSON>; nimittäjä)", "d": "<PERSON>lau<PERSON><PERSON> o<PERSON>ää<PERSON>ä<PERSON>."}, "RADIANS": {"a": "(kulma)", "d": "<PERSON><PERSON><PERSON> asteet r<PERSON><PERSON><PERSON>"}, "RAND": {"a": "()", "d": "Palauttaa tasaisesti jakautuneen satunnaisluvun, joka on yhtä suuri tai suurempi kuin 0 ja pienempi kuin 1"}, "RANDARRAY": {"a": "([rivit]; [sarak<PERSON>et]; [minimi]; [maksimi]; [koko<PERSON><PERSON><PERSON>u])", "d": "<PERSON><PERSON><PERSON><PERSON> satun<PERSON><PERSON><PERSON>n matri<PERSON>n"}, "RANDBETWEEN": {"a": "(ala; ylä)", "d": "Palauttaa satunnaisluvun määritettyjen arvojen väliltä."}, "ROMAN": {"a": "(luku; [muoto])", "d": "Muuntaa arabialaiset numerot roomalaisiksi numeroiksi"}, "ROUND": {"a": "(luku; numerot)", "d": "Pyöristää luvun annettuun määrään desimaaleja"}, "ROUNDDOWN": {"a": "(luku; numerot)", "d": "Pyöristä<PERSON> luvun alaspäin (no<PERSON><PERSON> kohti)"}, "ROUNDUP": {"a": "(luku; numerot)", "d": "Pyöristää luvun y<PERSON> (nollasta pois<PERSON>)"}, "SEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman se<PERSON>"}, "SECH": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman hyperbolisen sekantin"}, "SERIESSUM": {"a": "(x; n; m; kertoimet)", "d": "<PERSON><PERSON><PERSON><PERSON> summan."}, "SIGN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun etumerkin. <PERSON><PERSON> luku on positiivinen, arvo on 1. <PERSON><PERSON> luku on nolla, funktio palauttaa arvon 0. <PERSON><PERSON> luku on negatii<PERSON><PERSON>, funktio palauttaa arvon -1."}, "SIN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> annetun kulman sinin"}, "SINH": {"a": "(luku)", "d": "<PERSON>lauttaa luvun hyperbolisen sinin"}, "SQRT": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun ne<PERSON>"}, "SQRTPI": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> (luku*pii) neliöjuuren"}, "SUBTOTAL": {"a": "(funktio_nro; viittaus1; ...)", "d": "Laskee välisumman luettelosta tai tietokannasta"}, "SUM": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON> solualueessa olevien lukujen summan"}, "SUMIF": {"a": "(alue; ehdot; [summa_alue])", "d": "Laskee ehdot täyttävien solujen summan"}, "SUMIFS": {"a": "(summa-alue; eh<PERSON><PERSON>ue; ehdot; ...)", "d": "Lisää tiettyjen ehtojen määrittämät solut"}, "SUMPRODUCT": {"a": "(matriisi1; [matriisi2]; [matriisi3]; ...)", "d": "Palauttaa toisiaan vastaavien alueiden tai matriisin osien tulojen summan"}, "SUMSQ": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> argumenttien ne<PERSON>. Arvot voivat olla lukuja, <PERSON><PERSON><PERSON>, matri<PERSON><PERSON> tai vii<PERSON><PERSON><PERSON> lukuihin"}, "SUMX2MY2": {"a": "(matriisi_x; matriisi_y)", "d": "<PERSON><PERSON><PERSON> kahden alueen tai matriisin toisiaan vastaavien arvojen neliösummien erotuksen"}, "SUMX2PY2": {"a": "(matriisi_x; matriisi_y)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden alueen tai matriisin toisiaan vastaavien arvojen neliösummien summan"}, "SUMXMY2": {"a": "(matriisi_x; matriisi_y)", "d": "<PERSON><PERSON><PERSON> kahden alueen tai matriisin toisiaan vastaavien arvojen erotusten neliösumman"}, "TAN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman tangentin"}, "TANH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen tangentin"}, "TRUNC": {"a": "(luku; [numerot])", "d": "Katkaisee luvun kokonai<PERSON>luvuksi poistamalla desimaali- tai murto-osan"}, "ADDRESS": {"a": "(rivi_nro; sarake_nro; [vii<PERSON><PERSON><PERSON><PERSON>]; [a1]; [tauluk<PERSON>_teksti])", "d": "<PERSON><PERSON><PERSON><PERSON> solu<PERSON><PERSON><PERSON><PERSON> te<PERSON>, kun <PERSON><PERSON>a annetaan rivien ja sarakkeiden numerot"}, "CHOOSE": {"a": "(indeksi_luku; arvo1; [arvo2]; ...)", "d": "Valitsee arvon tai suoritettavan toimen indeksiluetteloon perustuvasta arvoluettelosta"}, "COLUMN": {"a": "([viittaus])", "d": "Palauttaa annettua viittausta vastaavan sarakkeen numeron"}, "COLUMNS": {"a": "(matriisi)", "d": "Palauttaa viittauksessa tai matriisissa olevien sarakkeiden määrän"}, "FORMULATEXT": {"a": "(viittaus)", "d": "Pa<PERSON><PERSON><PERSON> ka<PERSON> me<PERSON>jon<PERSON>"}, "HLOOKUP": {"a": "(haku<PERSON><PERSON>; tauluk<PERSON>_matriisi; rivi_indeksi_nro; [alue_haku])", "d": "Hakee annettua arvoa matriisin tai taulukon ylimmältä riviltä ja palauttaa samassa sarakkeessa ja määrittämälläsi rivillä olevan arvon"}, "HYPERLINK": {"a": "(<PERSON>in_kuvaus; [<PERSON><PERSON>_tiedot])", "d": "<PERSON><PERSON>, joka a<PERSON><PERSON> k<PERSON>, verk<PERSON>palvelimella tai Internetissä olevan asiaki<PERSON>"}, "INDEX": {"a": "(matriisi; rivi_nro; [sarake_nro]!viittaus; rivi_nro; [sarake_nro]; [alue_nro])", "d": "Palauttaa solun arvon tai viitta<PERSON>sen tietyn alueen rivin ja sarakkeen yhtymäkohdasta"}, "INDIRECT": {"a": "(viittaus_teksti; [a1])", "d": "Palauttaa merkkijonon määrittämän viittauksen"}, "LOOKUP": {"a": "(hakuarvo; hakuvektori; [tulosvektori]!hakuarvo; matriisi)", "d": "Etsii arvoja yhden rivin tai sarakkeen kokoisesta alueesta tai matriisista"}, "MATCH": {"a": "(hakuarvo; haku_matriisi; [vastine_laji])", "d": "<PERSON><PERSON><PERSON><PERSON> sen matriisin osan <PERSON> si<PERSON>, joka vastaa määritettyj<PERSON>"}, "OFFSET": {"a": "(viittaus; rivit; sarakkeet; [korkeus]; [leveys])", "d": "Palautta<PERSON> vii<PERSON><PERSON><PERSON>, joka on annetun etäisyyden (sarakkeina ja riveinä) päässä annetusta viittauksesta"}, "ROW": {"a": "([viittaus])", "d": "Palauttaa viittauksen rivinumeron"}, "ROWS": {"a": "(matriisi)", "d": "Palauttaa viittauksessa tai matriisissa olevien rivien määrän"}, "TRANSPOSE": {"a": "(matriisi)", "d": "<PERSON>untaa vertikaalisen solualueen horisontaaliseksi ja päinvas<PERSON>in"}, "UNIQUE": {"a": "(matriisi; [by_col]; [exactly_once])", "d": " Palauttaa alueen tai matriisin ainutkertaiset arvot."}, "VLOOKUP": {"a": "(haku<PERSON><PERSON>; taulukko_matriisi; sar_indeksi_nro; [alue_haku])", "d": "Hakee solun arvoa taulukon vasemmanpuoleisimmasta sarakkeesta ja palauttaa arvon samalla rivillä määritetystä sarakkeesta. Oletusarvoisesti taulukon tulee olla lajiteltu nousevassa järjestyksessä"}, "XLOOKUP": {"a": "(haku<PERSON><PERSON>; hakumatri<PERSON>; pala<PERSON><PERSON><PERSON><PERSON><PERSON>; [jos_ei_lö<PERSON><PERSON>]; [vastaa<PERSON>ust<PERSON>]; [haku<PERSON>a])", "d": "<PERSON><PERSON><PERSON> vastaavaa aluetta tai matriisia ja palauttaa vastaavan kohteen toisesta alueesta tai matriisista. Oletusarvoisesti tarkkaa vastaavuutta k<PERSON>ytetään"}, "CELL": {"a": "(kuvaus_laji; [viittaus])", "d": "Palauttaa tietoja solun muoto<PERSON>, si<PERSON><PERSON><PERSON> ja sis<PERSON>llöst<PERSON>"}, "ERROR.TYPE": {"a": "(virhearvo)", "d": "<PERSON><PERSON><PERSON><PERSON> vir<PERSON><PERSON><PERSON>a vastaavan luvun."}, "ISBLANK": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko vii<PERSON> kohteena tyhjä solu, ja palauttaa arvon TOSI tai EPÄTOSI"}, "ISERR": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo virhe, joka on muu kuin #PUUTTUU, ja palauttaa TOSI- tai EPÄTOSI-arvon"}, "ISERROR": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo virhe, ja palauttaa TOSI- tai EPÄTOSI-arvon"}, "ISEVEN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>von <PERSON>, jos luku on parill<PERSON>"}, "ISFORMULA": {"a": "(viittaus)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko viittaus kaavan sisältävä solu, ja palauttaa arvon TOSI tai EPÄTOSI"}, "ISLOGICAL": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo totuusarvo (TOSI tai EPÄTOSI), ja palauttaa arvon TOSI tai EPÄTOSI"}, "ISNA": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo #PUUTTUU, ja palauttaa TOSI- tai EPÄTOSI-arvon"}, "ISNONTEXT": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo muuta kuin tekstiä (tyhjät solut eivät ole tekstiä), ja palauttaa arvon TOSI tai EPÄTOSI"}, "ISNUMBER": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo luku, ja palauttaa arvon TOSI tai EPÄTOSI"}, "ISODD": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> arvon <PERSON>, jos luku on pariton"}, "ISREF": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo viittaus, ja palauttaa arvon <PERSON>SI tai EPÄTOSI"}, "ISTEXT": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo te<PERSON>, ja palauttaa arvon <PERSON>SI tai EPÄTOSI"}, "N": {"a": "(arvo)", "d": "Muuntaa muun kuin numeroarvon numeroksi, päivämäärät järjestysnumeroksi, TOSI-arvon arvoksi 1 ja kaikki muut arvot arvoksi 0 (nolla)"}, "NA": {"a": "()", "d": "Palauttaa virhearvon #PUUTTUU (arvo ei ole käytettävissä)"}, "SHEET": {"a": "([arvo])", "d": "Palauttaa viitattavan taulukon numeron"}, "SHEETS": {"a": "([viittaus])", "d": "Palauttaa viittauksessa olevien taulukoiden määrän"}, "TYPE": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, joka vastaa arvon tie<PERSON>: numero = 1, teksti = 2, totuusarvo = 4, virhearvo = 16, matri<PERSON> = 64; yhdistelmätiedot = 128"}, "AND": {"a": "(totuus1; [totuus2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko kaikkien argumenttien totuusarvo TOSI, ja palauttaa totuusarvon TOSI, jos n<PERSON>in on"}, "FALSE": {"a": "()", "d": "Palauttaa totuusarvon EPÄTOSI"}, "IF": {"a": "(logiikka_testi; [arvo_jos_tosi]; [arvo_jos_epätosi])", "d": "Ta<PERSON><PERSON>a, täyttyykö määrittämäsi ehto. Palauttaa y<PERSON><PERSON> arvon, jos ehto on TOSI ja toisen arvon, jos ehto on EPÄTOSI"}, "IFS": {"a": "(logiikka_testi; arvo_jos_tosi; ...)", "d": "Tarkistaa, täyttyykö vähintään yksi eh<PERSON>, ja palauttaa ensimmäistä TOSI-ehtoa vastaavan arvon"}, "IFERROR": {"a": "(arvo; arvo_jos_virhe)", "d": "<PERSON><PERSON><PERSON><PERSON> arvo_jos_virheen, jos lauseke on virhe ja lausekkeen arvo jokin muu"}, "IFNA": {"a": "(arvo; arvo_jos_ei_mit<PERSON>än)", "d": "Pa<PERSON>ttaa mää<PERSON>, jos lauseke antaa ratkaisuksi #Ei mitään, muutoin se palauttaa lausekkeen tuloksen"}, "NOT": {"a": "(totuus)", "d": "Kääntää EPÄTOSI-arvon TOSI-arvoksi tai TOSI-arvon EPÄTOSI-arvoksi"}, "OR": {"a": "(totuus1; [totuus2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko <PERSON>n argumentin totuusarvo TOSI, ja palauttaa TOSI- tai EPÄTOSI-arvon. <PERSON><PERSON> kaikkien argumenttien arvo on EPÄTOSI, funktio palauttaa arvon EPÄTOSI"}, "SWITCH": {"a": "(la<PERSON><PERSON>; arvo1; tulos1; [oletus_tai_arvo2]; [tulos2]; ...)", "d": "<PERSON><PERSON><PERSON> lausekkeen arvon luettelon arvojen per<PERSON> ja palauttaa tuloksena ensimmäisen vastaavan arvon. <PERSON><PERSON> vastaavu<PERSON> ei ole, palauttaa valinnaisen oletusarvon"}, "TRUE": {"a": "()", "d": "Palauttaa totuusarvon TOSI"}, "XOR": {"a": "(totuus1; [totuus2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> to<PERSON>uden 'Poissulkeva Tai'"}, "TEXTBEFORE": {"a": "(te<PERSON><PERSON>, erotin, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON>, joka on ennen erotinmerk<PERSON>j<PERSON>."}, "TEXTAFTER": {"a": "(te<PERSON><PERSON>, erotin, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON> on erotinmerkkien jälkeen."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "<PERSON><PERSON><PERSON> te<PERSON><PERSON> rive<PERSON> tai sarak<PERSON>si erott<PERSON> avulla."}, "WRAPROWS": {"a": "(vektori, wrap_count, [pad_with])", "d": " Rivittää rivi- tai sarakevektorin määritetyn arvomäärän jälk<PERSON>."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Pinoaa taulukot pystysuunnassa yhteen matriisiin."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Pinoaa taulukot vaakasuunnassa yhteen matriisiin."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Palauttaa matriisista tai viittauksesta vain määritetyt rivit"}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Palauttaa matriisista tai viittauksesta vain mä<PERSON>tyt sarak<PERSON>et."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Palauttaa matriisin yhtenä sarakkeena."}, "TOROW": {"a": "(matriisi, [ignore], [scan_by_column])", "d": "Palauttaa matriisin yhtenä rivinä. "}, "WRAPCOLS": {"a": "(vektori, wrap_count, [pad_with])", "d": " Rivittää rivi- tai sarakevektorin määritetyn arvomäärän jälk<PERSON>."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Palauttaa rivit tai sarakkeet matriisin alusta tai lopusta."}, "DROP": {"a": "(array, rows, [columns])", "d": "Poistaa rivit tai sarakkeet matriisin alusta tai lopusta."}}