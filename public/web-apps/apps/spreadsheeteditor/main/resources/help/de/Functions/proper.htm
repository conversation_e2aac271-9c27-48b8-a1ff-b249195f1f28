﻿<!DOCTYPE html>
<html>
	<head>
		<title>GROSS2-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>GROSS2-Funktion</h1>
			<p>Die Funktion <b>GROSS2</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie wandelt den ersten Buchstaben aller Wörter einer Zeichenfolge in Großbuchstaben um und alle anderen Buchstaben in Kleinbuchstaben.</p>
			<p>Die Formelsyntax der Funktion <b>GROSS2</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>GROSS2(Text)</em></b></p> 
			<p>Dabei ist <b><em>Text</em></b> ein in Anführungszeichen eingeschlossener Text, eine Formel, die Text zurückgibt, oder ein Bezug auf eine Zelle, die den Text enthält, auf den Sie die Formel anwenden wollen.</p>
			<p>Anwenden der Funktion <b>GROSS2</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>GROSS2</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="GROSS2-Funktion" src="../images/proper.png" /></p>
		</div>
	</body>
</html>