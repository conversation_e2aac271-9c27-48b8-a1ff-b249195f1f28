﻿<!DOCTYPE html>
<html>
	<head>
		<title>NEGBINOMVERT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>NEGBINOMVERT-Funktion</h1>
			<p>Die Funktion <b>NEGBINOMVERT</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt Wahrscheinlichkeiten einer negativbinomialverteilten Zufallsvariablen zurück.</p>
			<p>Doe Formelsyntax der Funktion <b>NEGBINOMVERT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>NEGBINOMVERT(Zahl_Mißerfolge;Zahl_Erfolge;Erfolgswahrsch)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
				<p style="text-indent: 50px;"><b><em>Zahl_Misserfolge</em></b> ist die Anzahl der Misserfolge in einer Versuchsreihe. Ein nummerischer Wert, der größer oder gleich 0 ist.</p>
				<p style="text-indent: 50px;"><b><em>Zahl_Erfolge</em></b> ist die Anzahl der Erfolge in einer Versuchsreihe. Ein nummerischer Wert, der größer oder gleich 0 ist.</p>
				<p style="text-indent: 50px;"><b><em>Erfolgswahrsch</em></b> ist die Wahrscheinlichkeit eines Erfolgs für jeden Versuch. Ein nummerischer Wert, größer als 0 und kleiner als 1.</p>
				<p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>NEGBINOMVERT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Anwendung der Funktion <b>NEGBINOMVERT</b>:</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="NEGBINOMVERT-Funktion" src="../images/negbinomdist.png" /></p>
		</div>
	</body>
</html>