{"DATE": {"a": "(ano; mês; dia)", "d": "Retorna o número que representa a data no código data-hora"}, "DATEDIF": {"a": "(data_inicial; data_final; unidade)", "d": "Calcula o número de dias, meses ou anos entre duas datas"}, "DATEVALUE": {"a": "(texto_data)", "d": "Converte uma data com formato de texto em um número que representa a data no código data-hora"}, "DAY": {"a": "(núm_série)", "d": "Retorna o dia do mês, um número de 1 a 31."}, "DAYS": {"a": "(data_final; data_inicial)", "d": "Retorna o número de dias entre duas datas."}, "DAYS360": {"a": "(data_inicial; data_final; [método])", "d": "Retorna o número de dias entre duas datas com base em um ano de 360 dias (doze meses de 30 dias)"}, "EDATE": {"a": "(data_inicial; meses)", "d": "Retorna o número de série da data que é o número indicado de meses antes ou depois da data inicial"}, "EOMONTH": {"a": "(data_inicial; meses)", "d": "Retorna o número serial do último dia do mês antes ou depois de um dado número de meses"}, "HOUR": {"a": "(núm_série)", "d": "Retorna a hora como um número de 0 (12:00 AM) a 23 (11:00 PM)."}, "ISOWEEKNUM": {"a": "(data)", "d": "Retorna o número da semana ISO do ano de uma determinada data"}, "MINUTE": {"a": "(núm_série)", "d": "Retorna o minuto, um número de 0 a 59."}, "MONTH": {"a": "(núm_série)", "d": "<PERSON><PERSON><PERSON> o mês, um número entre 1 (janeiro) e 12 (de<PERSON><PERSON><PERSON>)."}, "NETWORKDAYS": {"a": "(data_inicial; data_final; [feriados])", "d": "Retorna o número de dias úteis entre duas datas"}, "NETWORKDAYS.INTL": {"a": "(data_inicial; data_final; [fimdesemana]; [feriados])", "d": "Retorna o número de dias úteis entre duas datas com parâmetros de fim de semana personalizados"}, "NOW": {"a": "()", "d": "Retorna a data e a hora atuais formatadas como data e hora."}, "SECOND": {"a": "(núm_série)", "d": "Retorna o segundo, um número de 0 a 59."}, "TIME": {"a": "(hora; minuto; segundo)", "d": "Converte horas, minutos e segundos fornecidos como números em um número de série, formatado com formato de hora"}, "TIMEVALUE": {"a": "(texto_hora)", "d": "Converte uma hora de texto em um número de série que represente hora, um número de 0 (12:00:00 AM) a 0.999988426 (11:59:59 PM). Formate o número com um formato de hora após inserir a fórmula"}, "TODAY": {"a": "()", "d": "Retorna a data de hoje formatada como uma data."}, "WEEKDAY": {"a": "(núm_série; [retornar_tipo])", "d": "Retorna um número entre 1 e 7 identificando o dia da semana,"}, "WEEKNUM": {"a": "(núm_série; [tipo_retorno])", "d": "Retorna o número da semana no ano"}, "WORKDAY": {"a": "(data_inicial; dias; [feriados])", "d": "Retorna o número de série da data antes ou depois de um número especificado de dias úteis"}, "WORKDAY.INTL": {"a": "(data_inicial; dias; [fimdesemana]; [feriados])", "d": "Retorna o número de série de datas anteriores ou posteriores a um número especificado de dias úteis com parâmetros de fim de semana personalizados"}, "YEAR": {"a": "(núm_série)", "d": "Retorna o ano de uma data, um número inteiro no intervalo de 1900 a 9999."}, "YEARFRAC": {"a": "(data_inicial; data_final; [base])", "d": "Retorna a fração do ano que representa o número de dias inteiros entre data_inicial e data_final"}, "BESSELI": {"a": "(x; n)", "d": "Retorna a função Bessel modificada ln(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Retorna a função Bessel Jn(x)."}, "BESSELK": {"a": "(x; n)", "d": "Retorna a função Bessel modificada Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Retorna a função Bessel Yn(x)"}, "BIN2DEC": {"a": "(núm)", "d": "Converte um número binário em decimal"}, "BIN2HEX": {"a": "(núm; [casas])", "d": "Converte um número binário em hexadecimal"}, "BIN2OCT": {"a": "(núm; [casas])", "d": "Converte um número binário em octal"}, "BITAND": {"a": "(número1; número2)", "d": "Retorna um bit 'E' de dois números"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Retorna um número deslocado à esquerda por shift_amount bits"}, "BITOR": {"a": "(número1; número2)", "d": "Retorna um bit 'Ou' de dois números"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Retorna um número deslocados à direita por shift_amount bits"}, "BITXOR": {"a": "(número1; número2)", "d": "Retorna um bit 'Exclusivo Ou' de dois números"}, "COMPLEX": {"a": "(núm_real; i_núm; [sufixo])", "d": "Converte coeficientes reais e imaginários em um número complexo"}, "CONVERT": {"a": "(núm; de_unidade; para_unidade)", "d": "Converte um número de um sistema de medidas para outro"}, "DEC2BIN": {"a": "(núm; [casas])", "d": "Converte um número decimal em binário"}, "DEC2HEX": {"a": "(núm; [casas])", "d": "Converte um número decimal em hexadecimal"}, "DEC2OCT": {"a": "(núm; [casas])", "d": "Converte um número decimal em octal"}, "DELTA": {"a": "(núm1; [núm2])", "d": "Testa se dois números são iguais"}, "ERF": {"a": "(limite_inferior; [limite_superior])", "d": "Retorna a função de erro"}, "ERF.PRECISE": {"a": "(X)", "d": "Retorna a função de erro"}, "ERFC": {"a": "(x)", "d": "Retorna a função de erro complementar"}, "ERFC.PRECISE": {"a": "(x)", "d": "Retorna a função de erro complementar"}, "GESTEP": {"a": "(núm; [passo])", "d": "Testa se um número é maior que um valor limite"}, "HEX2BIN": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em binário"}, "HEX2DEC": {"a": "(núm)", "d": "Converte um número hexadecimal em decimal"}, "HEX2OCT": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em octal"}, "IMABS": {"a": "(inúm)", "d": "Retorna o valor absoluto (módulo) de um número complexo"}, "IMAGINARY": {"a": "(inúm)", "d": "Retorna o coeficiente imaginário de um número complexo"}, "IMARGUMENT": {"a": "(inúm)", "d": "Retorna o argumento q, um ângulo expresso em radianos"}, "IMCONJUGATE": {"a": "(inúm)", "d": "Retorna o conjugado complexo de um número complexo"}, "IMCOS": {"a": "(inúm)", "d": "Retorna o cosseno de um número complexo"}, "IMCOSH": {"a": "(inúmero)", "d": "Retorna o cosseno hiperbólico de um número complexo"}, "IMCOT": {"a": "(inúmero)", "d": "Retorna a cotangente de um número complexo"}, "IMCSC": {"a": "(inúmero)", "d": "Retorna a cossecante de um número complexo"}, "IMCSCH": {"a": "(inúmero)", "d": "Retorna a hiperbólica da cossecante de um número complexo"}, "IMDIV": {"a": "(inúm1; inúm2)", "d": "Retorna o quociente de dois números complexos"}, "IMEXP": {"a": "(inúm)", "d": "Retorna o exponencial de um número complexo"}, "IMLN": {"a": "(inúm)", "d": "Retorna o logaritmo natural de um número complexo"}, "IMLOG10": {"a": "(inúm)", "d": "Retorna o logaritmo de base-10 de um número complexo"}, "IMLOG2": {"a": "(inúm)", "d": "Retorna o logaritmo de base-2 de um número complexo"}, "IMPOWER": {"a": "(inúm; núm)", "d": "Retorna um número complexo elevado a uma potência inteira"}, "IMPRODUCT": {"a": "(inúm1; [inúm2]; ...)", "d": "Retorna o produto de 1 a 255 números complexos"}, "IMREAL": {"a": "(inúm)", "d": "Retorna o coeficiente real de um número complexo"}, "IMSEC": {"a": "(inúmero)", "d": "Retorna a secante de um número complexo"}, "IMSECH": {"a": "(inúmero)", "d": "Retorna a hiperbólica da secante de um número complexo"}, "IMSIN": {"a": "(inúm)", "d": "Retorna o seno de um número complexo"}, "IMSINH": {"a": "(inúmero)", "d": "Retorna o seno hiperbólico de um número complexo"}, "IMSQRT": {"a": "(inúm)", "d": "Retorna a raiz quadrada de um número complexo"}, "IMSUB": {"a": "(inúm1; inúm2)", "d": "Retorna a diferença de dois números complexos"}, "IMSUM": {"a": "(inúm1; [inúm2]; ...)", "d": "Retorna a soma dos números complexos"}, "IMTAN": {"a": "(inúmero)", "d": "Retorna a tangente de um número complexo"}, "OCT2BIN": {"a": "(núm; [casas])", "d": "Converte um número octal em binário"}, "OCT2DEC": {"a": "(núm)", "d": "Converte um número octal em decimal"}, "OCT2HEX": {"a": "(núm; [casas])", "d": "Converte um número octal em hexadecimal"}, "DAVERAGE": {"a": "(banco_dados; campo; critérios)", "d": "Calcula a média dos valores em uma coluna de uma lista ou um banco de dados que correspondam às condições especificadas"}, "DCOUNT": {"a": "(banco_dados; campo; critérios)", "d": "<PERSON>ta as c<PERSON><PERSON>las contendo números no campo (coluna) de registros no banco de dados que corresponde às condições especificadas"}, "DCOUNTA": {"a": "(banco_dados; campo; critérios)", "d": "Conta as células não vazias no campo (coluna) de registros do banco de dados que atendam às condições especificadas"}, "DGET": {"a": "(banco_dados; campo; critérios)", "d": "Extrai de um banco de dados um único registro que corresponde às condições especificadas"}, "DMAX": {"a": "(banco_dados; campo; critérios)", "d": "Retorna o maior número no campo (coluna) de registros do banco de dados que atendam às condições especificadas"}, "DMIN": {"a": "(banco_dados; campo; critérios)", "d": "Retorna o menor número no campo (coluna) de registros do banco de dados que atendam às condições especificadas"}, "DPRODUCT": {"a": "(banco_dados; campo; critérios)", "d": "Multiplica os valores no campo (coluna) de registros do banco de dados que atendam às condições especificadas"}, "DSTDEV": {"a": "(banco_dados; campo; critérios)", "d": "Estima o desvio padrão com base em uma amostra de entradas selecionadas do banco de dados"}, "DSTDEVP": {"a": "(banco_dados; campo; critérios)", "d": "Calcula o desvio padrão com base na população total de entradas selecionadas do banco de dados"}, "DSUM": {"a": "(banco_dados; campo; critérios)", "d": "Soma os números no campo (coluna) de registros no banco de dados que atendam às condições especificadas"}, "DVAR": {"a": "(banco_dados; campo; critérios)", "d": "Estima a variação com base em uma amostra das entradas selecionadas do banco de dados"}, "DVARP": {"a": "(banco_dados; campo; critérios)", "d": "Calcula a variação com base na população total de entradas selecionadas de banco de dados"}, "CHAR": {"a": "(núm)", "d": "Retorna o caractere especificado pelo código numérico do conjunto de caracteres de seu computador"}, "CLEAN": {"a": "(texto)", "d": "Remove do texto todos os caracteres não imprimíveis"}, "CODE": {"a": "(texto)", "d": "Retorna um código numérico para o primeiro caractere de uma cadeia de texto, no conjunto de caracteres usado por seu computador"}, "CONCATENATE": {"a": "(texto1; [texto2]; ...)", "d": "Agrupa várias cadeias de texto em uma única sequência de texto"}, "CONCAT": {"a": "(texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto"}, "DOLLAR": {"a": "(núm; [decimais])", "d": "Converte um número em texto, utilizando o formato de moeda"}, "EXACT": {"a": "(texto1; texto2)", "d": "Verifica se duas cadeias são exatamente iguais e retorna VERDADEIRO ou FALSO. EXATO diferencia maiúsculas de minúsculas"}, "FIND": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Retorna a posição inicial de uma cadeia de texto encontrada em outra cadeia de texto. PROCURAR diferencia maiúsculas de minúsculas"}, "FINDB": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto em uma segunda cadeia de texto e retorna o número da posição inicial da primeira cadeia de texto do primeiro caractere da segunda cadeia de texto, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano."}, "FIXED": {"a": "(núm; [decimais]; [sem_sep_milhar])", "d": "Arredonda um número para o número de casas decimais especificado e retorna o resultado como texto com ou sem separadores de milhares"}, "LEFT": {"a": "(texto; [núm_caract])", "d": "Retorna o número especificado de caracteres do início de uma cadeia de texto"}, "LEFTB": {"a": "(texto; [núm_caract])", "d": "Retorna o primeiro caractere ou os primeiros caracteres em uma cadeia de texto com base no número de bytes especificado por você, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano."}, "LEN": {"a": "(texto)", "d": "Retorna o número de caracteres em uma cadeia de texto"}, "LENB": {"a": "(texto)", "d": "Retorna o número de bytes usados para representar os caracteres de uma cadeia de texto, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano."}, "LOWER": {"a": "(texto)", "d": "Converte todas as letras em uma cadeia de texto em minúsculas"}, "MID": {"a": "(texto; núm_inicial; núm_caract)", "d": "Retorna os caracteres do meio de uma cadeia de texto, tendo a posição e o comprimento especificados"}, "MIDB": {"a": "(texto; núm_inicial; núm_caract)", "d": "Retorna um número específico de caracteres de uma cadeia de texto, começando na posição especificada, com base no número de bytes especificado, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano."}, "NUMBERVALUE": {"a": "(texto; [separador_decimal]; [separador_grupo])", "d": "Converte texto em número de maneira independente de localidade"}, "PROPER": {"a": "(texto)", "d": "Converte uma cadeia de texto no formato apropriado; a primeira letra de cada palavra em maiúsculas e as demais letras em minúsculas"}, "REPLACE": {"a": "(texto_antigo; núm_inicial; núm_caract; novo_texto)", "d": "Substitui parte de uma cadeia de texto por uma cadeia diferente"}, "REPLACEB": {"a": "(texto_antigo; núm_inicial; núm_caract; novo_texto)", "d": "Substitui parte de uma cadeia de texto, com base no número de bytes especificado, por uma cadeia de texto diferente, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano."}, "REPT": {"a": "(texto; número_vezes)", "d": "Repete o texto um determinado número de vezes. Utilize REPT para preencher uma célula com um número de repetições de uma cadeia"}, "RIGHT": {"a": "(texto; [núm_caract])", "d": "Retorna o número de caracteres especificado do final de uma cadeia de texto"}, "RIGHTB": {"a": "(texto; [núm_caract])", "d": "Retorna o último caractere ou os últimos caracteres em uma cadeia de texto, com base no número de bytes especificado, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano."}, "SEARCH": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Retorna o número do caractere no qual um caractere ou uma cadeia de texto foram localizados, sendo a leitura feita da esquerda para a direita (não distingue maiúsculas de minúsculas)"}, "SEARCHB": {"a": "(texto_procurado; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto em uma segunda cadeia de texto e retorna o número da posição inicial da primeira cadeia de texto do primeiro caractere da segunda cadeia de texto, é para ser usada com idiomas que utilizam o DBCS (conjunto de caracteres de dois bytes) - o japonês, o chinês e o coreano."}, "SUBSTITUTE": {"a": "(texto; texto_antigo; novo_texto; [núm_da_ocorrência])", "d": "Substitui um texto antigo por outro novo em uma cadeia de texto"}, "T": {"a": "(valor)", "d": "Verifica se um valor é texto e retorna o texto referido em caso afirmativo ou retorna aspas duplas (texto vazio) em caso negativo"}, "TEXT": {"a": "(valor; formato_texto)", "d": "Converte um valor em texto com um formato de número específico"}, "TEXTJOIN": {"a": "(delimitador; ignorar_vazio; texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto usando um delimitador"}, "TRIM": {"a": "(texto)", "d": "Remove os espaços de uma cadeia de texto, com exceção dos espaços simples entre palavras"}, "UNICHAR": {"a": "(número)", "d": "Retorna o caractere Unicode referenciado por um determinado valor numérico"}, "UNICODE": {"a": "(texto)", "d": "Retorna o número (ponto de código) correspondente ao primeiro caractere do texto"}, "UPPER": {"a": "(texto)", "d": "Converte a cadeia de texto em maiúsculas"}, "VALUE": {"a": "(texto)", "d": "Converte uma cadeia de texto que representa um número em um número"}, "AVEDEV": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média dos desvios absolutos dos pontos de dados a partir de sua média. Os argumentos podem ser números ou nomes, matrizes ou referências que contenham números"}, "AVERAGE": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média (aritmética) dos argumentos que podem ser números ou nomes, matrizes ou referências que contêm números"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna a média aritmética dos argumentos, avaliando texto e FALSO em argumentos como 0, VERDADEIRO é avaliado como 1. Os argumentos podem ser números, nomes, matrizes ou referências"}, "AVERAGEIF": {"a": "(intervalo; critérios; [intervalo_média])", "d": "Descobre a média aritmética das células especificadas por uma dada condição ou determinados critérios"}, "AVERAGEIFS": {"a": "(intervalo_média; intervalo_critérios; critérios; ...)", "d": "Descobre a média aritmética das células especificadas por um dado conjunto de condições ou critérios"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Retorna a função de densidade da probabilidade beta cumulativa"}, "BETAINV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Retorna o inverso da função de densidade da probabilidade beta cumulativa (DISTBETA)"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativo; [A]; [B])", "d": "Retorna a função de distribuição da probabilidade beta"}, "BETA.INV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Retorna o inverso da função de densidade da probabilidade beta cumulativa (DIST.BETA)"}, "BINOMDIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Retorna a probabilidade da distribuição binomial do termo individual"}, "BINOM.DIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Retorna a probabilidade da distribuição binomial do termo individual"}, "BINOM.DIST.RANGE": {"a": "(tentativas; probabilidade_s; número_s; [número_s2])", "d": "Retorna a probabilidade de um resultado de teste usando uma distribuição binomial"}, "BINOM.INV": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Retorna o menor valor para o qual a distribuição binomial cumulativa é maior ou igual ao valor padrão"}, "CHIDIST": {"a": "(x; graus_liberdade)", "d": "Retorna a probabilidade de cauda direita da distribuição qui-quadrada"}, "CHIINV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso da probabilidade de cauda direita da distribuição qui-quadrada"}, "CHITEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Retorna o teste para independência: o valor da distribuição qui-quadrada para a estatística e o grau apropriado de liberdade"}, "CHISQ.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Retorna a probabilidade de cauda esquerda da distribuição qui-quadrada"}, "CHISQ.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Retorna a probabilidade de cauda direita da distribuição qui-quadrada"}, "CHISQ.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso da probabilidade de cauda esquerda da distribuição qui-quadrada"}, "CHISQ.INV.RT": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso da probabilidade de cauda direita da distribuição qui-quadrada"}, "CHISQ.TEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Retorna o teste para independência: o valor da distribuição qui-quadrada para a estatística e o grau apropriado de liberdade"}, "CONFIDENCE": {"a": "(alfa; desv_padrão; tamanho)", "d": "Retorna o intervalo de confiança para uma média da população"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_padrão; tamanho)", "d": "Retorna o intervalo de confiança para uma média da população, usando uma distribuição normal"}, "CONFIDENCE.T": {"a": "(alfa; desv_padrão; tamanho)", "d": "Retorna o intervalo de confiança para uma média da população, usando uma distribuição T de Student"}, "CORREL": {"a": "(matriz1; matriz2)", "d": "Retorna o coeficiente de correlação entre dois conjuntos de dados"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o número de células em um intervalo que contém números"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o número de células em um intervalo que não estão vazias"}, "COUNTBLANK": {"a": "(intervalo)", "d": "Conta o número de células vazias em um intervalo de células especificado"}, "COUNTIF": {"a": "(intervalo; critérios)", "d": "Calcula o número de células não vazias em um intervalo que corresponde a uma determinada condição"}, "COUNTIFS": {"a": "(intervalo_critérios; critérios; ...)", "d": "Conta o número de células especificadas por um dado conjunto de condições ou critérios"}, "COVAR": {"a": "(matriz1; matriz2)", "d": "Retorna a covariação, a média dos produtos dos desvios de cada par de pontos de dados em dois conjuntos de dados"}, "COVARIANCE.P": {"a": "(matriz1; matriz2)", "d": "Retorna a covariação da população, a média dos produtos dos desvios para cada par de pontos de dados em dois conjuntos de dados"}, "COVARIANCE.S": {"a": "(matriz1; matriz2)", "d": "Retorna a covariação da amostra, a média dos produtos dos desvios de cada par de pontos de dados em dois conjuntos de dados"}, "CRITBINOM": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Retorna o menor valor para o qual a distribuição binomial cumulativa é maior ou igual ao valor padrão"}, "DEVSQ": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a soma dos quadrados dos desvios dos pontos de dados da média de suas amostras"}, "EXPONDIST": {"a": "(x; lambda; cumulativo)", "d": "Retorna a distribuição exponencial"}, "EXPON.DIST": {"a": "(x; lambda; cumulativo)", "d": "Retorna a distribuição exponencial"}, "FDIST": {"a": "(x; graus_liberdade1; graus_liberdade2)", "d": "Retorna a distribuição (grau de diversidade) de probabilidade F (cauda direita) para dois conjuntos de dados"}, "FINV": {"a": "(probabilidade; graus_liberdade1; graus_liberdade2)", "d": "Retorna o inverso da distribuição de probabilidades F (cauda direita): se p = DISTF(x,...), então INVF(p,...) = x"}, "FTEST": {"a": "(matriz1; matriz2)", "d": "Retorna o resultado de um teste F, a probabilidade bicaudal de que as variações em Matriz1 e Matriz2 não sejam significativamente diferentes"}, "F.DIST": {"a": "(x; graus_liberdade1; graus_liberdade2; cumulativo)", "d": "Retorna a distribuição (grau de diversidade) de probabilidade F (cauda esquerda) para dois conjuntos de dados"}, "F.DIST.RT": {"a": "(x; graus_liberdade1; graus_liberdade2)", "d": "Retorna a distribuição (grau de diversidade) de probabilidade F (cauda direita) para dois conjuntos de dados"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Retorna o inverso da distribuição de probabilidade F (cauda esquerda): se p = DISTF(x,...), então INV.F(p,...) = x"}, "F.INV.RT": {"a": "(probabilidade; graus_liberdade1; graus_liberdade2)", "d": "Retorna o inverso da distribuição de probabilidade F (cauda direita): se p = DIST.F.CD(x,...), então INV.F.CD(p,...) = x"}, "F.TEST": {"a": "(matriz1; matriz2)", "d": "Retorna o resultado de um teste F, a probabilidade bicaudal de que as variações em Matriz1 e Matriz2 não sejam significativamente diferentes"}, "FISHER": {"a": "(x)", "d": "Retorna a transformação Fisher"}, "FISHERINV": {"a": "(y)", "d": "Retorna o inverso da transformação Fisher: se y = FISHER(x), então FISHERINV(y) = x"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear usando valores existentes"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna o valor previsto para uma data de destino futura específica usando o método de suavização exponencial."}, "FORECAST.ETS.CONFINT": {"a": "(data_alvo; valores; linha_cronológica; [nível_de_confiança]; [sazonalidade]; [conclusão_dos_dados]; [agregação])", "d": "Retorna um intervalo de confiança para o valor de previsão na data de destino especificada."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Retorna o comprimento do padrão repetitivo que um aplicativo detecta para a série de tempo especificada."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna a estatística solicitada para a previsão."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear usando valores existentes"}, "FREQUENCY": {"a": "(matriz_dados; matriz_bin)", "d": "Calcula a frequência de ocorrência de valores em um intervalo de valores e retorna uma matriz vertical de números contendo um elemento a mais do que 'Matriz_bin'"}, "GAMMA": {"a": "(x)", "d": "Retorna o valor da função Gama"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição gama"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição gama"}, "GAMMAINV": {"a": "(probabilidade; alfa; beta)", "d": "Retorna o inverso da distribuição cumulativa gama: se p = DISTGAMA(x,...), então INVGAMA(p,...) = x"}, "GAMMA.INV": {"a": "(probabilidade; alfa; beta)", "d": "Retorna o inverso da distribuição cumulativa gama: se p = DIST.GAMA(x,...), então INV.GAMA(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Retorna o logaritmo natural da função gama"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Retorna o logaritmo natural da função gama"}, "GAUSS": {"a": "(x)", "d": "Retorna 0,5 menos que a distribuição cumulativa normal padrão"}, "GEOMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média geométrica de uma matriz ou um intervalo de dados numéricos positivos"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Retorna números em uma tendência de crescimento exponencial que corresponda a pontos de dados conhecidos"}, "HARMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a média harmônica de um conjunto de dados de números positivos: o recíproco da média aritmética de recíprocos"}, "HYPGEOM.DIST": {"a": "(exemplo_s; exemplo_núm; população_s; núm_população; cumulativo)", "d": "Retorna a distribuição hipergeométrica"}, "HYPGEOMDIST": {"a": "(exemplo_s; exemplo_núm; população_s; núm_população)", "d": "Retorna a distribuição hipergeométrica"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Calcula o ponto em que uma linha interceptará o eixo y usando uma linha de regressão de melhor ajuste plotada através dos valores de x e valores de y conhecidos"}, "KURT": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a curtose de um conjunto de dados"}, "LARGE": {"a": "(matriz; k)", "d": "Retorna o maior valor k-ésimo de um conjunto de dados. Por exemplo, o quinto maior número"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Retorna a estatística que descreve a tendência linear que corresponda aos pontos de dados, ajustando uma linha reta através do método de quadrados mínimos"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Retorna a estatística que descreve uma curva exponencial que corresponda a pontos de dados conhecidos"}, "LOGINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da função de distribuição log-normal cumulativa de x, onde ln(x) é normalmente distribuído com parâmetros Média e Dev_padrão"}, "LOGNORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Retorna a distribuição log-normal de x, onde ln(x) é normalmente distribuído com parâmetros Média e Desv_padrão"}, "LOGNORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da função de distribuição cumulativa log-normal de x, onde ln(x) é normalmente distribuído com parâmetros Média e Desv_padrão"}, "LOGNORMDIST": {"a": "(x; média; desv_padrão)", "d": "Retorna a distribuição log-normal cumulativa de x, onde ln(x) é normalmente distribuído com parâmetros Média e Desv_padrão"}, "MAX": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor máximo de um conjunto de argumentos. Valores lógicos e texto são ignorados"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna o valor máximo contido em um conjunto de valores. Não ignora valores lógicos e texto"}, "MAXIFS": {"a": "(intervalo_máximo; intervalo_critérios; critérios; ...)", "d": "Retorna o valor máximo entre células especificadas por um dado conjunto de condições ou critérios"}, "MEDIAN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a mediana, ou o número central de um determinado conjunto de números"}, "MIN": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor mínimo contido em um conjunto de valores. Texto e valores lógicos são ignorados"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna o valor mínimo contido em um conjunto de valores. Não ignora valores lógicos e texto"}, "MINIFS": {"a": "(intervalo_mínimo; critério_intervalo; critérios; ...)", "d": "Retorna o valor mínimo entre células especificadas por um dado conjunto de condições ou critérios"}, "MODE": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor mais repetitivo ou que ocorre com maior frequência, em uma matriz ou um intervalo de dados"}, "MODE.MULT": {"a": "(núm1; [núm2]; ...)", "d": "Retorna uma matriz vertical dos valores mais repetidos, ou que ocorrem com maior frequência, em uma matriz ou um intervalo de dados. Para a matriz horizontal, use =TRANSPOR(MODO.MULT(número1,número2,...))"}, "MODE.SNGL": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o valor mais repetido, ou que ocorre com maior frequência, em uma matriz ou um intervalo de dados"}, "NEGBINOM.DIST": {"a": "(núm_f; núm_s; probabilidade_s; cumulativo)", "d": "Retorna a distribuição binomial negativa, a probabilidade de que ocorrerá Núm_f de falhas antes de Núm_s de sucessos, com probabilidade Probabilidade_s de um sucesso"}, "NEGBINOMDIST": {"a": "(núm_f; núm_s; probabilidade_s)", "d": "Retorna a distribuição binomial negativa, a probabilidade de que ocorrerá Núm_f de falhas antes de Núm_s de sucessos, com probabilidade Probabilidade_s de um sucesso"}, "NORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Retorna a distribuição normal da média e do desvio padrão especificados"}, "NORMDIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Retorna a distribuição cumulativa normal para um desvio padrão e média especificada"}, "NORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da distribuição cumulativa normal para a média e o desvio padrão especificados"}, "NORMINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Retorna o inverso da distribuição cumulativa normal para a média e o desvio padrão especificados"}, "NORM.S.DIST": {"a": "(z; cumulativo)", "d": "Retorna a distribuição normal padrão (possui uma média zero e um desvio padrão 1)"}, "NORMSDIST": {"a": "(z)", "d": "Retorna a distribuição cumulativa normal padrão (possui uma média zero e um desvio padrão 1)"}, "NORM.S.INV": {"a": "(probabilidade)", "d": "Retorna o inverso da distribuição cumulativa normal padrão (possui uma média zero e um desvio padrão 1)"}, "NORMSINV": {"a": "(probabilidade)", "d": "Retorna o inverso da distribuição cumulativa normal padrão (possui uma média zero e um desvio padrão 1)"}, "PEARSON": {"a": "(matriz1; matriz2)", "d": "Retorna o coeficiente de correlação do momento do produto Pearson, r"}, "PERCENTILE": {"a": "(matriz; k)", "d": "Retorna o k-ésimo percentil de valores em um intervalo"}, "PERCENTILE.EXC": {"a": "(matriz; k)", "d": "Retorna o k-ésimo percentil de valores em um intervalo, em que k está no intervalo 0..1, exclusive"}, "PERCENTILE.INC": {"a": "(matriz; k)", "d": "Retorna o k-ésimo percentil de valores em um intervalo, em que k está no intervalo 0..1, inclusive"}, "PERCENTRANK": {"a": "(matriz; x; [significância])", "d": "Retorna a posição de um valor em um conjunto de dados na forma de uma porcentagem do conjunto de dados"}, "PERCENTRANK.EXC": {"a": "(matriz; x; [significância])", "d": "Retorna a posição de um valor em um conjunto de dados como uma porcentagem do conjunto de dados (0..1, exclusive)"}, "PERCENTRANK.INC": {"a": "(matriz; x; [significância])", "d": "Retorna a posição de um valor em um conjunto de dados como uma porcentagem do conjunto de dados (0..1, inclusive)"}, "PERMUT": {"a": "(núm; núm_escolhido)", "d": "Retorna o número de permutações para um dado número de objetos que podem ser selecionados do total de objetos"}, "PERMUTATIONA": {"a": "(número; núm_escolhido)", "d": "Retorna o número de permutações para um determinado número de objetos (com repetições) que podem ser selecionados do total de objetos"}, "PHI": {"a": "(x)", "d": "Retorna o valor da função de densidade para uma distribuição normal padrão"}, "POISSON": {"a": "(x; média; cumulativo)", "d": "Retorna a distribuição Poisson"}, "POISSON.DIST": {"a": "(x; média; cumulativo)", "d": "Retorna a distribuição Poisson"}, "PROB": {"a": "(intervalo_x; intervalo_prob; limite_inferior; [limite_superior])", "d": "Retorna a probabilidade de que valores em um intervalo estão entre dois limites ou são iguais ao limite inferior"}, "QUARTILE": {"a": "(matriz; quarto)", "d": "Retorna o quartil do conjunto de dados"}, "QUARTILE.INC": {"a": "(matriz; quarto)", "d": "Retorna o quartil do conjunto de dados, com base nos valores de percentil de 0..1, inclusive"}, "QUARTILE.EXC": {"a": "(matriz; quarto)", "d": "Retorna o quartil do conjunto de dados, com base nos valores de percentil de 0..1, exclusive"}, "RANK": {"a": "(núm; ref; [ordem])", "d": "Retorna a posição de um número em uma lista de números: o seu tamanho em relação a outros valores da lista"}, "RANK.AVG": {"a": "(núm; ref; [ordem])", "d": "Retorna a posição de um número em uma lista de números: o seu tamanho em relação a outros valores da lista; se mais de um valor tiver a mesma posição, será retornada uma posição média"}, "RANK.EQ": {"a": "(núm; ref; [ordem])", "d": "Retorna a posição de um número em uma lista de números: o seu tamanho em relação a outros valores da lista; se mais de um valor tiver a mesma posição, será retornada a posição mais elevada do conjunto de valores"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Retorna o quadrado do coeficiente de correlação do momento do produto de Pearson para os pontos de dados determinados"}, "SKEW": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a distorção de uma distribuição: uma caracterização do grau de assimetria da distribuição em relação à média"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Retorna a distorção de uma distribuição com base na população: uma caracterização do grau de assimetria da distribuição em torno da média"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Retorna a inclinação da reta de regressão linear para os pontos de dados determinados"}, "SMALL": {"a": "(matriz; k)", "d": "Retorna o menor valor k-ésimo do conjunto de dados. Por exemplo, o quinto menor número"}, "STANDARDIZE": {"a": "(x; média; desv_padrão)", "d": "Retorna um valor normalizado de uma distribuição caracterizada por uma média e um desvio padrão"}, "STDEV": {"a": "(núm1; [núm2]; ...)", "d": "Estima o desvio padrão com base em uma amostra (ignora os valores lógicos e texto da amostra)"}, "STDEV.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio padrão com base na população total determinada como argumento (ignora valores lógicos e texto)"}, "STDEV.S": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio padrão a partir de uma amostra (ignora os valores lógicos e o texto da amostra)"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Estima o desvio padrão com base em uma amostra, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1"}, "STDEVP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio padrão com base na população total determinada como argumento (ignora valores lógicos e texto)"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o desvio padrão com base na população total, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Retorna o erro padrão do valor de y previsto para cada x da regressão"}, "TDIST": {"a": "(x; graus_liberdade; caudas)", "d": "Retorna a distribuição t de Student"}, "TINV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso bicaudal da distribuição t de Student"}, "T.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Retorna a cauda esquerda da distribuição t de Student"}, "T.DIST.2T": {"a": "(x; graus_liberdade)", "d": "Retorna a distribuição t de Student bicaudal"}, "T.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Retorna a distribuição t de Student de cauda direita"}, "T.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso de cauda esquerda da distribuição t de Student"}, "T.INV.2T": {"a": "(probabilidade; graus_liberdade)", "d": "Retorna o inverso bicaudal da distribuição t de Student"}, "T.TEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Retorna a probabilidade associada ao teste t de Student"}, "TREND": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [novos_valores_x]; [constante])", "d": "Retorna números em uma tendência linear que corresponda a pontos de dados conhecidos através do método de quadrados mínimos"}, "TRIMMEAN": {"a": "(matriz; porcentagem)", "d": "Retorna a média da parte interior de um conjunto de valores de dados"}, "TTEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Retorna a probabilidade associada ao teste t de Student"}, "VAR": {"a": "(núm1; [núm2]; ...)", "d": "Estima a variação com base em uma amostra (ignora valores lógicos e texto da amostra)"}, "VAR.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variação com base na população total (ignora valores lógicos e texto da população)"}, "VAR.S": {"a": "(núm1; [núm2]; ...)", "d": "Estima a variação com base em uma amostra (ignora valores lógicos e texto na amostra)"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Estima a variação com base em uma amostra, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1"}, "VARP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variação com base na população total (ignora valores lógicos e texto da população)"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula a variação com base na população total, incluindo valores lógicos e texto. Texto e o valor lógico FALSO têm o valor 0, o valor lógico VERDADEIRO tem valor 1"}, "WEIBULL": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição Weibull"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Retorna a distribuição Weibull"}, "Z.TEST": {"a": "(matriz; x; [sigma])", "d": "Retorna o valor-P unicaudal do teste-z"}, "ZTEST": {"a": "(matriz; x; [sigma])", "d": "Retorna o valor-P unicaudal do teste z"}, "ACCRINT": {"a": "(emissão; primeiro_juro; liquidação; taxa; valor_nominal; frequência; [base]; [méto<PERSON>_cálculo])", "d": "Retorna os juros acumulados de um título que paga juros periódicos."}, "ACCRINTM": {"a": "(emissão; liquidação; taxa; valor_nominal; [base])", "d": "Retorna os juros acumulados de um título que paga juros no vencimento"}, "AMORDEGRC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Retorna a depreciação linear pro rata de um ativo para cada período contábil."}, "AMORLINC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Retorna a depreciação linear pro rata de um ativo para cada período contábil"}, "COUPDAYBS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de dias entre o início do período do cupom e a data de liquidação."}, "COUPDAYS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de dias no período do cupom que contém a data de liquidação."}, "COUPDAYSNC": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de dias entre a data de liquidação e a próxima data do cupom."}, "COUPNCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna a próxima data do cupom depois da data de liquidação."}, "COUPNUM": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna o número de cupons a serem pagos entre a data de liquidação e a data de vencimento"}, "COUPPCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Retorna a última data do cupom antes da data de liquidação."}, "CUMIPMT": {"a": "(taxa; nper; vp; início_período; final_período; tipo_pgto)", "d": "Retorna os juros cumulativos pagos entre dois períodos."}, "CUMPRINC": {"a": "(taxa; nper; vp; início_período; final_período; tipo_pgto)", "d": "Retorna o capital cumulativo pago em um empréstimo entre dois períodos."}, "DB": {"a": "(custo; recuperação; vida_útil; período; [mês])", "d": "Retorna a depreciação de um ativo para um determinado período utilizando o método de balanço de declínio fixo"}, "DDB": {"a": "(custo; recuperação; vida_útil; período; [fator])", "d": "Retorna a depreciação de um ativo para um determinado período utilizando o método do balanço de declínio duplo ou qualquer outro método especificado"}, "DISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Retorna a taxa de desconto de um título"}, "DOLLARDE": {"a": "(moeda_fracionária; fração)", "d": "Converte um preço em moeda, expresso como uma fração, em um preço em moeda, expresso como um número decimal"}, "DOLLARFR": {"a": "(moeda_decimal; fração)", "d": "Converte um preço em moeda, expresso como um número decimal, em um preço em moeda, expresso como uma fração"}, "DURATION": {"a": "(liquidação; vencimento; cupom; lcr; frequência; [base])", "d": "Retorna a duração anual de um título com pagamentos de juros periódicos."}, "EFFECT": {"a": "(taxa_nominal; núm_por_ano)", "d": "Retorna a taxa de juros efetiva anual"}, "FV": {"a": "(taxa; nper; pgto; [vp]; [tipo])", "d": "Retorna o valor futuro de um investimento com base em pagamentos constantes e periódicos e uma taxa de juros constante"}, "FVSCHEDULE": {"a": "(capital; plano)", "d": "Retorna o valor futuro de um capital inicial depois de ter sido aplicada uma série de taxas de juros compostos."}, "INTRATE": {"a": "(liquidação; vencimento; investimento; resgate; [base])", "d": "Retorna a taxa de juros de um título totalmente investido"}, "IPMT": {"a": "(taxa; período; nper; vp; [vf]; [tipo])", "d": "Retorna o pagamento dos juros de um investimento durante um determinado período, com base nos pagamentos constantes, periódicos e na taxa de juros constante"}, "IRR": {"a": "(valores; [estimativa])", "d": "Retorna a taxa interna de retorno de uma série de fluxos de caixa"}, "ISPMT": {"a": "(taxa; período; nper; vp)", "d": "Retorna os juros pagos durante um período específico do investimento"}, "MDURATION": {"a": "(liquidação; vencimento; cupom; lcr; frequência; [base])", "d": "Retorna a duração modificada Macauley de um título com um valor par presumido de R$ 100."}, "MIRR": {"a": "(valores; taxa_financ; taxa_reinvest)", "d": "Retorna a taxa interna de retorno para uma série de fluxos de caixa periódicos, considerando o custo de investimento e os juros de reinvestimento de caixa"}, "NOMINAL": {"a": "(taxa_efetiva; núm_por_ano)", "d": "Retorna a taxa de juros nominal anual"}, "NPER": {"a": "(taxa; pgto; vp; [vf]; [tipo])", "d": "Retorna o número de períodos de um investimento com base em pagamentos constantes periódicos e uma taxa de juros constante"}, "NPV": {"a": "(taxa; valor1; [valor2]; ...)", "d": "Retorna o valor líquido atual de um investimento, com base em uma taxa de desconto e uma série de pagamentos futuros (valores negativos) e renda (valores positivos)"}, "ODDFPRICE": {"a": "(liquidação; vencimento; emissão; prim_cupom; taxa; lcr; resgate; frequência; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título com um período inicial incompleto."}, "ODDFYIELD": {"a": "(liquidação; vencimento; emissão; prim_cupom; taxa; pr; resgate; frequência; [base])", "d": "Retorna o rendimento de um título com um período inicial incompleto."}, "ODDLPRICE": {"a": "(liquidação; vencimento; último_juros; taxa; lcr; resgate; frequência; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título com um período final incompleto"}, "ODDLYIELD": {"a": "(liquidação; vencimento; último_juros; taxa; pr; resgate; frequência; [base])", "d": "Retorna o rendimento de um título com um período final incompleto."}, "PDURATION": {"a": "(taxa; pv; fv)", "d": "Retorna o número de períodos exigido por um investimento para atingir um valor especificado"}, "PMT": {"a": "(taxa; nper; vp; [vf]; [tipo])", "d": "Calcula o pagamento de um empréstimo com base em pagamentos e em uma taxa de juros constantes"}, "PPMT": {"a": "(taxa; per; nper; vp; [vf]; [tipo])", "d": "Retorna o pagamento sobre o montante de um investimento com base em pagamentos e em uma taxa de juros constantes, periódicos"}, "PRICE": {"a": "(liquidação; vencimento; taxa; lcr; resgate; frequência; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título que paga juros periódicos."}, "PRICEDISC": {"a": "(liquidação; vencimento; desconto; resgate; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título com deságio"}, "PRICEMAT": {"a": "(liquidação; vencimento; emissão; taxa; lcr; [base])", "d": "Retorna o preço por R$100 do valor nominal de um título que paga juros no vencimento"}, "PV": {"a": "(taxa; per; pgto; [vf]; [tipo])", "d": "Retorna o valor presente de um investimento: a quantia total atual de uma série de pagamentos futuros"}, "RATE": {"a": "(nper; pgto; vp; [vf]; [tipo]; [estimativa])", "d": "Retorna a taxa de juros por período em um empréstimo ou investimento. Por exemplo, use 6%/4 para pagamentos trimestrais a uma taxa de 6% TPA"}, "RECEIVED": {"a": "(liquidação; vencimento; investimento; desconto; [base])", "d": "Retorna a quantia recebida no vencimento para um título totalmente investido."}, "RRI": {"a": "(nper; pv; fv)", "d": "Retorna uma taxa de juros equivalente para o crescimento de um investimento"}, "SLN": {"a": "(custo; recuperação; vida_útil)", "d": "Retorna a depreciação em linha reta de um ativo durante um período"}, "SYD": {"a": "(custo; recuperação; vida_útil; per)", "d": "Retorna a depreciação dos dígitos da soma dos anos de um ativo para um período especificado"}, "TBILLEQ": {"a": "(liquidação; vencimento; desconto)", "d": "Retorna o rendimento de uma letra do tesouro equivalente ao rendimento de um título"}, "TBILLPRICE": {"a": "(liquidação; vencimento; desconto)", "d": "Retorna o preço por R$100 do valor nominal de uma letra do tesouro"}, "TBILLYIELD": {"a": "(liquidação; vencimento; pr)", "d": "Retorna o rendimento de uma letra do tesouro"}, "VDB": {"a": "(custo; recuperação; vida_útil; início_período; final_período; [fator]; [sem_mudança])", "d": "Retorna a depreciação de um ativo para um período específico, incluindo o período parcial, utilizando o método de balanço decrescente duplo ou qualquer outro método especificado"}, "XIRR": {"a": "(valores; datas; [estimativa])", "d": "Retorna a taxa de retorno interna de um cronograma de fluxos de caixa"}, "XNPV": {"a": "(taxa; valores; datas)", "d": "Retorna o valor presente líquido de um cronograma de fluxos de caixa"}, "YIELD": {"a": "(liquidação; vencimento; taxa; pr; resgate; frequência; [base])", "d": "Retorna o rendimento de um título que paga juros periódicos"}, "YIELDDISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Retorna o rendimento anual de um título com deságio. Por exemplo, uma letra do tesouro"}, "YIELDMAT": {"a": "(liquidação; vencimento; emissão; taxa; pr; [base])", "d": "Retorna o rendimento anual de um título que paga juros no vencimento"}, "ABS": {"a": "(núm)", "d": "Retorna o valor absoluto de um número, um número sem sinal"}, "ACOS": {"a": "(núm)", "d": "Retorna o arco cosseno de um número, em radianos no intervalo de 0 a Pi. O arco cosseno é o ângulo cujo cosseno é número"}, "ACOSH": {"a": "(núm)", "d": "Retorna o cosseno hiperbólico inverso de um número"}, "ACOT": {"a": "(número)", "d": "Retorna o arco cotangente de um número, em radianos no intervalo de 0 a Pi."}, "ACOTH": {"a": "(número)", "d": "Retorna a hiperbólica inversa da cotangente de um número"}, "AGGREGATE": {"a": "(núm_função; opções; ref1; ...)", "d": "Retorna uma agregação em uma lista ou um banco de dados"}, "ARABIC": {"a": "(texto)", "d": "Converte um numeral romano em arábico"}, "ASC": {"a": "(texto)", "d": "Para idiomas do conjunto de caracteres de dois bytes (DBCS), a função altera os caracteres de largura total (byte duplo) para caracteres de meia largura (byte único)"}, "ASIN": {"a": "(núm)", "d": "Retorna o arco seno de um número em radianos, no intervalo entre -Pi/2 e Pi/2"}, "ASINH": {"a": "(núm)", "d": "Retorna o seno hiperbólico inverso de um número"}, "ATAN": {"a": "(núm)", "d": "Retorna o arco tangente de um número em radianos, no intervalo entre -Pi e p1/2"}, "ATAN2": {"a": "(núm_x; núm_y)", "d": "Retorna o arco tangente das coordenadas x e y especificadas, em radianos entre -Pi e Pi, excluindo -Pi"}, "ATANH": {"a": "(núm)", "d": "Retorna a tangente hiperbólica inversa de um número"}, "BASE": {"a": "(número; radix; [min_length])", "d": "Converte um número em uma representação de texto com determinado radix (base)"}, "CEILING": {"a": "(núm; significância)", "d": "Arredonda um número para cima, para o próximo múltiplo significativo"}, "CEILING.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número para cima, para o inteiro mais próximo ou para o próximo múltiplo significativo"}, "CEILING.PRECISE": {"a": "(número; [significância])", "d": "Retorna um número que é arredondado para o inteiro mais próximo ou para o múltiplo mais próximo de significância"}, "COMBIN": {"a": "(núm; núm_escolhido)", "d": "Retorna o número de combinações para um determinado número de itens"}, "COMBINA": {"a": "(número; núm_escolhido)", "d": "Retorna o número de combinações com repetições para um determinado número de itens"}, "COS": {"a": "(núm)", "d": "Retorna o cosseno de um ângulo"}, "COSH": {"a": "(núm)", "d": "Retorna o cosseno hiperbólico de um número"}, "COT": {"a": "(número)", "d": "Retorna a cotangente de um ângulo"}, "COTH": {"a": "(número)", "d": "Retorna a hiperbólica da cotangente de um número"}, "CSC": {"a": "(número)", "d": "Retorna a cossecante de um ângulo"}, "CSCH": {"a": "(número)", "d": "Retorna a hiperbólica da cossecante de um ângulo"}, "DECIMAL": {"a": "(número; radix)", "d": "Converte uma representação de texto de um número em uma determinada base em um número decimal"}, "DEGREES": {"a": "(â<PERSON><PERSON>)", "d": "Converte radianos em graus"}, "ECMA.CEILING": {"a": "(número; significância)", "d": "Arredonda um número para cima para o próximo múltiplo significativo"}, "EVEN": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo até o valor inteiro mais próximo"}, "EXP": {"a": "(núm)", "d": "Retorna 'e' elevado à potência de um determinado número"}, "FACT": {"a": "(núm)", "d": "Retorna o fatorial de um número, igual a 1*2*3*...*núm"}, "FACTDOUBLE": {"a": "(núm)", "d": "Retorna o fatorial duplo de um número"}, "FLOOR": {"a": "(núm; significância)", "d": "Arredonda um número para baixo até o múltiplo ou a significância mais próxima"}, "FLOOR.PRECISE": {"a": "(número; [significância])", "d": "Retorna um número que é arredondado para baixo para o inteiro mais próximo ou para o múltiplo mais próximo de significância"}, "FLOOR.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número para baixo, para o inteiro mais próximo ou para o próximo múltiplo significativo"}, "GCD": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o máximo divisor comum"}, "INT": {"a": "(núm)", "d": "Arredonda um número para baixo até o número inteiro mais próximo"}, "ISO.CEILING": {"a": "(número; [significância])", "d": "Retorna um número que é arredondado para o inteiro mais próximo ou para o múltiplo mais próximo de significância. Independentemente do sinal de núm, um valor será arredondado. No entanto, se núm ou significância for zero, zero será retornado."}, "LCM": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o mínimo múl<PERSON>lo comum"}, "LN": {"a": "(núm)", "d": "Retorna o logaritmo natural de um número"}, "LOG": {"a": "(núm; [base])", "d": "Retorna o logaritmo de um número em uma base especificada"}, "LOG10": {"a": "(núm)", "d": "Retorna o logaritmo de base 10 de um número"}, "MDETERM": {"a": "(matriz)", "d": "Retorna o determinante de uma matriz"}, "MINVERSE": {"a": "(matriz)", "d": "Retorna a matriz inversa de uma matriz"}, "MMULT": {"a": "(matriz1; matriz2)", "d": "Retorna a matriz produto de duas matrizes, uma matriz com o mesmo número de linhas que a matriz1 e de colunas que a matriz2"}, "MOD": {"a": "(núm; divisor)", "d": "Retorna o resto da divisão após um número ter sido dividido por um divisor"}, "MROUND": {"a": "(núm; múltiplo)", "d": "Retorna um número arredondado para o múltiplo desejado"}, "MULTINOMIAL": {"a": "(núm1; [núm2]; ...)", "d": "Retorna o multinômio de um conjunto de números"}, "MUNIT": {"a": "(dimensão)", "d": "Retorna a matriz de unidade para a dimensão especificada"}, "ODD": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo até o número ímpar inteiro mais próximo"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> o valor de Pi, 3,14159265358979, em 15 dígitos"}, "POWER": {"a": "(núm; potência)", "d": "Retorna o resultado de um número elevado a uma potência"}, "PRODUCT": {"a": "(núm1; [núm2]; ...)", "d": "Multiplica todos os números dados como argumentos"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Retorna a parte inteira de uma divisão"}, "RADIANS": {"a": "(â<PERSON><PERSON>)", "d": "Converte graus em radianos"}, "RAND": {"a": "()", "d": "Retorna um número aleatório maior ou igual a 0 e menor que 1 (modificado quando recalculado) distribuído uniformemente"}, "RANDARRAY": {"a": "([linhas]; [colunas]; [min]; [max]; [inteiro])", "d": "Retorna uma matriz de números aleatórios"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Retorna um número aleatório entre os números especificados"}, "ROMAN": {"a": "(núm; [forma])", "d": "Converte um algarismo arábico em romano, como texto"}, "ROUND": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número até uma quantidade especificada de dígitos"}, "ROUNDDOWN": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número para baixo até zero"}, "ROUNDUP": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número para cima afastando-o de zero"}, "SEC": {"a": "(número)", "d": "Retorna a secante de um ângulo"}, "SECH": {"a": "(número)", "d": "Retorna a hiperbólica da secante de um ângulo"}, "SERIESSUM": {"a": "(x; n; m; coeficientes)", "d": "Retorna a soma de uma série de potência baseada na fórmula"}, "SIGN": {"a": "(núm)", "d": "Retorna o sinal de um número: 1 se o número for positivo, 0 se o número for zero ou -1 se o número for negativo"}, "SIN": {"a": "(núm)", "d": "Retorna o seno de um ângulo"}, "SINH": {"a": "(núm)", "d": "Retorna o seno hiperbólico de um número"}, "SQRT": {"a": "(núm)", "d": "Retorna a raiz quadrada de um número"}, "SQRTPI": {"a": "(núm)", "d": "Retorna a raiz quadrada de (núm * Pi)"}, "SUBTOTAL": {"a": "(núm_função; ;ref1]; ...)", "d": "Retorna um subtotal em uma lista ou um banco de dados"}, "SUM": {"a": "(núm1; [núm2]; ...)", "d": "Soma todos os números em um intervalo de células"}, "SUMIF": {"a": "(intervalo; critérios; [intervalo_soma])", "d": "Adiciona as células especificadas por um determinado critério ou condição"}, "SUMIFS": {"a": "(intervalo_soma; intervalo_critérios; critérios; ...)", "d": "Adiciona as células especificadas por um dado conjunto de condições ou critérios"}, "SUMPRODUCT": {"a": "(matriz1; [matriz2]; [matriz3]; ...)", "d": "Retorna a soma dos produtos de intervalos ou matrizes correspondentes"}, "SUMSQ": {"a": "(núm1; [núm2]; ...)", "d": "Retorna a soma dos quadrados dos argumentos. Os argumentos podem ser números, matrizes, nomes ou referências a células que contenham números"}, "SUMX2MY2": {"a": "(matriz_x; matriz_y)", "d": "<PERSON><PERSON> as diferenças entre dois quadrados de dois intervalos ou matrizes correspondentes"}, "SUMX2PY2": {"a": "(matriz_x; matriz_y)", "d": "Retorna a soma total das somas dos quadrados dos números em dois intervalos ou matrizes correspondentes"}, "SUMXMY2": {"a": "(matriz_x; matriz_y)", "d": "Soma os quadrados das diferenças em dois intervalos ou matrizes correspondentes"}, "TAN": {"a": "(núm)", "d": "Retorna a tangente de um ângulo"}, "TANH": {"a": "(núm)", "d": "Retorna a tangente hiperbólica de um número"}, "TRUNC": {"a": "(núm; [núm_dígitos])", "d": "Trunca um número até um número inteiro removendo a parte decimal ou fracionária do número"}, "ADDRESS": {"a": "(núm_lin; núm_col; [núm_abs]; [a1]; [texto_planilha])", "d": "Cria uma referência de célula como texto, de acordo com números de linha e coluna específicos"}, "CHOOSE": {"a": "(núm_índice; valor1; [valor2]; ...)", "d": "Escolhe um valor a partir de uma lista de valores, com base em um número de índice"}, "COLUMN": {"a": "([ref])", "d": "Retorna o número da coluna de uma referência"}, "COLUMNS": {"a": "(matriz)", "d": "Retorna o número de colunas contido em uma matriz ou referência"}, "FORMULATEXT": {"a": "(reference)", "d": "Retorna uma fórmula como uma cadeia de caracteres"}, "HLOOKUP": {"a": "(valor_procurado; matriz_tabela; núm_índice_lin; [procurar_intervalo])", "d": "Pesquisa um valor na linha superior de uma tabela ou matriz de valores e retorna o valor na mesma coluna a partir de uma linha especificada"}, "HYPERLINK": {"a": "(local_vínculo; [nome_amigável])", "d": "Cria um atalho ou salto que abre um documento armazenado no disco rígido, em um servidor de rede ou na Internet"}, "INDEX": {"a": "(matriz; núm_linha; [núm_coluna]!ref; núm_linha; [núm_coluna]; [núm_<PERSON><PERSON>])", "d": "Retorna um valor ou a referência da célula na interseção de uma linha e coluna específica, em um dado intervalo"}, "INDIRECT": {"a": "(texto_ref; [a1])", "d": "Retorna uma referência indicada por um valor de texto"}, "LOOKUP": {"a": "(valor_procurado; vetor_proc; [vetor_result]!valor_procurado; matriz)", "d": "Procura um valor a partir de um intervalo de linha ou coluna ou de uma matriz. Fornecido para manter a compatibilidade com versões anteriores"}, "MATCH": {"a": "(valor_procurado; matriz_procurada; [tipo_correspondência])", "d": "Retorna a posição relativa de um item em uma matriz que corresponda a um valor específico em uma ordem específica"}, "OFFSET": {"a": "(ref; lins; cols; [altura]; [largura])", "d": "Retorna uma referência a um intervalo que possui um número específico de linhas e colunas com base em uma referência especificada"}, "ROW": {"a": "([ref])", "d": "Retorna o número da linha de uma referência"}, "ROWS": {"a": "(matriz)", "d": "Retorna o número de linhas em uma referência ou matriz"}, "TRANSPOSE": {"a": "(matriz)", "d": "Converte um intervalo de células vertical em um intervalo horizontal e vice-versa"}, "UNIQUE": {"a": "(matriz; [by_col]; [exactly_once])", "d": " Retorna os valores exclusivos de um intervalo ou matriz."}, "VLOOKUP": {"a": "(valor_procurado; matriz_tabela; núm_índice_coluna; [procurar_intervalo])", "d": "Procura um valor na primeira coluna à esquerda de uma tabela e retorna um valor na mesma linha de uma coluna especificada. Como padrão, a tabela deve estar classificada em ordem crescente"}, "XLOOKUP": {"a": "(pesquisa_valor; pesquisa_matriz; matriz_retorno; [se_não_encontrada]; [modo_correspondência]; [modo_pesquisa])", "d": "Procura uma correspondência em um intervalo ou matriz e retorna o item correspondente de uma segunda matriz ou intervalo. Por padr<PERSON>, é usada uma correspondência exata"}, "CELL": {"a": "(tipo_info; [referência])", "d": "Retorna informações sobre a formatação, o local ou o conteúdo de uma célula"}, "ERROR.TYPE": {"a": "(val_erro)", "d": "Retorna um número que corresponde a um valor de erro."}, "ISBLANK": {"a": "(valor)", "d": "Verifica se uma referência está sendo feita a uma célula vazia e retorna VERDADEIRO ou FALSO"}, "ISERR": {"a": "(valor)", "d": "Verifica se um valor é um erro diferente de #N/D e retorna VERDADEIRO ou FALSO"}, "ISERROR": {"a": "(valor)", "d": "Verifica se um valor é um erro e retorna VERDADEIRO ou FALSO"}, "ISEVEN": {"a": "(núm)", "d": "Retorna VERDADEIRO se o número for par"}, "ISFORMULA": {"a": "(referência)", "d": "Verifica se uma referência é uma célula que contém uma fórmula e retorna VERDADEIRO ou FALSO"}, "ISLOGICAL": {"a": "(valor)", "d": "Verifica se um valor é lógico (VERDADEIRO ou FALSO) e retorna VERDADEIRO ou FALSO"}, "ISNA": {"a": "(valor)", "d": "Verifica se um valor é #N/D e retorna VERDADEIRO ou FALSO"}, "ISNONTEXT": {"a": "(valor)", "d": "Verifica se um valor não é texto (células vazias não são texto) e retorna VERDADEIRO ou FALSO"}, "ISNUMBER": {"a": "(valor)", "d": "Verifica se um valor é um número e retorna VERDADEIRO ou FALSO"}, "ISODD": {"a": "(núm)", "d": "Retorna VERDADEIRO se o número for ímpar"}, "ISREF": {"a": "(valor)", "d": "Verifica se um valor é uma referência e retorna VERDADEIRO ou FALSO"}, "ISTEXT": {"a": "(valor)", "d": "Verifica se um valor é texto e retorna VERDADEIRO ou FALSO"}, "N": {"a": "(valor)", "d": "Converte um valor não numérico em um número, datas em números de série, VERDADEIRO em 1 e qualquer outro valor em 0 (zero)"}, "NA": {"a": "()", "d": "Retorna o valor de erro #N/D (valor não disponível)"}, "SHEET": {"a": "([valor])", "d": "Retorna o número da folha da folha de referência"}, "SHEETS": {"a": "([referência])", "d": "Retorna o número de planilhas em uma referência"}, "TYPE": {"a": "(valor)", "d": "Retorna um número inteiro que indica o tipo de dados de um valor: número = 1, texto = 2, valor lógico = 4, valor de erro = 16, matriz = 64;dados compostos = 128"}, "AND": {"a": "(lógico1; [lógico2]; ...)", "d": "Verifica se os argumentos são VERDADEIRO e retorna VERDADEIRO se todos os argumentos forem VERDADEIRO"}, "FALSE": {"a": "()", "d": "Retorna o valor lógico FALSO"}, "IF": {"a": "(teste_lógico; [valor_se_verdadeiro]; [valor_se_falso])", "d": "Verifica se uma condição foi satisfeita e retorna um valor se for VERDADEIRO e retorna um outro valor se for FALSO"}, "IFS": {"a": "(teste_lógico; valor_se_verdadeiro; ...)", "d": "Verifica se uma ou mais condições são atendidas e retorna um valor correspondente à primeira condição VERDADEIRA"}, "IFERROR": {"a": "(valor; valor_se_erro)", "d": "Retorna valor_se_erro se a expressão for um erro ; caso contr<PERSON>rio, retorna o valor da expressão"}, "IFNA": {"a": "(valor; valor_se_na)", "d": "Retorna o valor que você especificar se a expressão resolver para #N/A, caso contr<PERSON>rio, retorna o resultado da expressão"}, "NOT": {"a": "(lógico)", "d": "Inverte FALSO para VERDADEIRO, ou VERDADEIRO para FALSO"}, "OR": {"a": "(lógico1; [lógico2]; ...)", "d": "Verifica se algum argumento é VERDADEIRO e retorna VERDADEIRO ou FALSO. Retorna FALSO somente se todos os argumentos forem FALSO"}, "SWITCH": {"a": "(expressão; valor1; resultado1; [padrão_ou_valor2]; [resultado2]; ...)", "d": "Avalia uma expressão em uma lista de valores e retorna o resultado correspondente para o primeiro valor coincidente. Se não houver nenhuma correspondência, será retornado um valor padrão opcional"}, "TRUE": {"a": "()", "d": "Retorna o valor lógico VERDADEIRO"}, "XOR": {"a": "(lógico1; [lógico2]; ...)", "d": "Retorna uma lógica 'Exclusivo Ou' de todos os argumentos"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna o texto que está antes dos caracteres delimitadores."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna o texto que está depois dos caracteres delimitadores."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Divide o texto em linhas ou colunas usando delimitadores."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Encapsula um vetor de linha ou coluna após um número especificado de valores."}, "VSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha verticalmente matrizes em uma matriz."}, "HSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha horizontalmente matrizes em uma matriz."}, "CHOOSEROWS": {"a": "(matriz, row_num1, [row_num2], ...)", "d": "Retorna linhas de uma matriz ou referência."}, "CHOOSECOLS": {"a": "(matriz, col_num1, [col_num2], ...)", "d": "<PERSON><PERSON><PERSON> as colu<PERSON> de uma matriz ou referência"}, "TOCOL": {"a": "(matriz, [ignorar], [scan_by_column])", "d": "Retorna a matriz como uma coluna."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Retorna a matriz como uma linha."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Encapsula um vetor de linha ou coluna após um número especificado de valores."}, "TAKE": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "<PERSON><PERSON><PERSON> linhas ou colunas de início ou té<PERSON><PERSON> da matriz."}, "DROP": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "Remove linhas ou colunas de início ou té<PERSON><PERSON> da matriz."}}