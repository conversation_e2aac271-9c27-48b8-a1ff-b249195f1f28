{"DATE": "DÁTUM", "DATEDIF": "DATEDIF", "DATEVALUE": "DÁTUMÉRTÉK", "DAY": "NAP", "DAYS": "NAPOK", "DAYS360": "NAP360", "EDATE": "KALK.DÁTUM", "EOMONTH": "HÓNAP.UTOLSÓ.NAP", "HOUR": "ÓRA", "ISOWEEKNUM": "ISO.HÉT.SZÁMA", "MINUTE": "PERCEK", "MONTH": "HÓNAP", "NETWORKDAYS": "ÖSSZ.MUNKANAP", "NETWORKDAYS.INTL": "ÖSSZ.MUNKANAP.INTL", "NOW": "MOST", "SECOND": "MPERC", "TIME": "IDŐ", "TIMEVALUE": "IDŐÉRTÉK", "TODAY": "MA", "WEEKDAY": "HÉT.NAPJA", "WEEKNUM": "HÉT.SZÁMA", "WORKDAY": "KALK.MUNKANAP", "WORKDAY.INTL": "KALK.MUNKANAP.INTL", "YEAR": "ÉV", "YEARFRAC": "TÖRTÉV", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN.DEC", "BIN2HEX": "BIN.HEX", "BIN2OCT": "BIN.OKT", "BITAND": "BIT.ÉS", "BITLSHIFT": "BIT.BAL.ELTOL", "BITOR": "BIT.VAGY", "BITRSHIFT": "BIT.JOBB.ELTOL", "BITXOR": "BIT.XVAGY", "COMPLEX": "KOMPLEX", "CONVERT": "KONVERTÁLÁS", "DEC2BIN": "DEC.BIN", "DEC2HEX": "DEC.HEX", "DEC2OCT": "DEC.OKT", "DELTA": "DELTA", "ERF": "HIBAF", "ERF.PRECISE": "HIBAF.PONTOS", "ERFC": "HIBAF.KOMPLEMENTER", "ERFC.PRECISE": "HIBAFKOMPLEMENTER.PONTOS", "GESTEP": "KÜSZÖBNÉL.NAGYOBB", "HEX2BIN": "HEX.BIN", "HEX2DEC": "HEX.DEC", "HEX2OCT": "HEX.OKT", "IMABS": "KÉPZ.ABSZ", "IMAGINARY": "KÉPZETES", "IMARGUMENT": "KÉPZ.ARGUMENT", "IMCONJUGATE": "KÉPZ.KONJUGÁLT", "IMCOS": "KÉPZ.COS", "IMCOSH": "KÉPZ.COSH", "IMCOT": "KÉPZ.COT", "IMCSC": "KÉPZ.CSC", "IMCSCH": "KÉPZ.CSCH", "IMDIV": "KÉPZ.HÁNYAD", "IMEXP": "KÉPZ.EXP", "IMLN": "KÉPZ.LN", "IMLOG10": "KÉPZ.LOG10", "IMLOG2": "KÉPZ.LOG2", "IMPOWER": "KÉPZ.HATV", "IMPRODUCT": "KÉPZ.SZORZAT", "IMREAL": "KÉPZ.VALÓS", "IMSEC": "KÉPZ.SEC", "IMSECH": "KÉPZ.SECH", "IMSIN": "KÉPZ.SIN", "IMSINH": "KÉPZ.SINH", "IMSQRT": "KÉPZ.GYÖK", "IMSUB": "KÉPZ.KÜL", "IMSUM": "KÉPZ.ÖSSZEG", "IMTAN": "KÉPZ.TAN", "OCT2BIN": "OKT.BIN", "OCT2DEC": "OKT.DEC", "OCT2HEX": "OKT.HEX", "DAVERAGE": "AB.ÁTLAG", "DCOUNT": "AB.DARAB", "DCOUNTA": "AB.DARAB2", "DGET": "AB.MEZŐ", "DMAX": "AB.MAX", "DMIN": "AB.MIN", "DPRODUCT": "AB.SZORZAT", "DSTDEV": "AB.SZÓRÁS", "DSTDEVP": "AB.SZÓRÁS2", "DSUM": "AB.SZUM", "DVAR": "AB.VAR", "DVARP": "AB.VAR2", "CHAR": "KARAKTER", "CLEAN": "TISZTÍT", "CODE": "KÓD", "CONCATENATE": "ÖSSZEFŰZ", "CONCAT": "FŰZ", "DOLLAR": "FORINT", "EXACT": "AZONOS", "FIND": "SZÖVEG.TALÁL", "FINDB": "FINDB", "FIXED": "FIX", "LEFT": "BAL", "LEFTB": "LEFTB", "LEN": "HOSSZ", "LENB": "LENB", "LOWER": "KISBETŰ", "MID": "KÖZÉP", "MIDB": "MIDB", "NUMBERVALUE": "SZÁMÉRTÉK", "PROPER": "TNÉV", "REPLACE": "CSERE", "REPLACEB": "REPLACEB", "REPT": "SOKSZOR", "RIGHT": "JOBB", "RIGHTB": "RIGHTB", "SEARCH": "SZÖVEG.KERES", "SEARCHB": "SEARCHB", "SUBSTITUTE": "HELYETTE", "T": "T", "T.TEST": "T.PRÓB", "TEXT": "SZÖVEG", "TEXTJOIN": "SZÖVEGÖSSZEFŰZÉS", "TREND": "TREND", "TRIM": "KIMETSZ", "TRIMMEAN": "RÉSZÁTLAG", "TTEST": "T.PRÓBA", "UNICHAR": "UNIKARAKTER", "UNICODE": "UNICODE", "UPPER": "NAGYBETŰS", "VALUE": "ÉRTÉK", "AVEDEV": "ÁTL.ELTÉRÉS", "AVERAGE": "ÁTLAG", "AVERAGEA": "ÁTLAGA", "AVERAGEIF": "ÁTLAGHA", "AVERAGEIFS": "ÁTLAGHATÖBB", "BETADIST": "BÉTA.ELOSZLÁS", "BETAINV": "INVERZ.BÉTA", "BETA.DIST": "BÉTA.ELOSZL", "BETA.INV": "BÉTA.INVERZ", "BINOMDIST": "BINOM.ELOSZLÁS", "BINOM.DIST": "BINOM.ELOSZL", "BINOM.DIST.RANGE": "BINOM.ELOSZL.TART", "BINOM.INV": "BINOM.INVERZ", "CHIDIST": "KHI.ELOSZLÁS", "CHIINV": "INVERZ.KHI", "CHITEST": "KHI.PRÓBA", "CHISQ.DIST": "KHINÉGYZET.ELOSZLÁS", "CHISQ.DIST.RT": "KHINÉGYZET.ELOSZLÁS.JOBB", "CHISQ.INV": "KHINÉGYZET.INVERZ", "CHISQ.INV.RT": "KHINÉGYZET.INVERZ.JOBB", "CHISQ.TEST": "KHINÉGYZET.PRÓBA", "CONFIDENCE": "MEGBÍZHATÓSÁG", "CONFIDENCE.NORM": "MEGBÍZHATÓSÁG.NORM", "CONFIDENCE.T": "MEGBÍZHATÓSÁG.T", "CORREL": "KORREL", "COUNT": "DARAB", "COUNTA": "DARAB2", "COUNTBLANK": "DARABÜRES", "COUNTIF": "DARABTELI", "COUNTIFS": "DARABHATÖBB", "COVAR": "KOVAR", "COVARIANCE.P": "KOVARIANCIA.S", "COVARIANCE.S": "KOVARIANCIA.M", "CRITBINOM": "KRITBINOM", "DEVSQ": "SQ", "EXPON.DIST": "EXP.ELOSZL", "EXPONDIST": "EXP.ELOSZLÁS", "FDIST": "F.ELOSZLÁS", "FINV": "INVERZ.F", "FTEST": "F.PRÓBA", "F.DIST": "F.ELOSZL", "F.DIST.RT": "F.ELOSZLÁS.JOBB", "F.INV": "F.INVERZ", "F.INV.RT": "F.INVERZ.JOBB", "F.TEST": "F.PRÓB", "FISHER": "FISHER", "FISHERINV": "INVERZ.FISHER", "FORECAST": "ELŐREJELZÉS", "FORECAST.ETS": "ELŐREJELZÉS.ESIM", "FORECAST.ETS.CONFINT": "ELŐREJELZÉS.ESIM.KONFINT", "FORECAST.ETS.SEASONALITY": "ELŐREJELZÉS.ESIM.SZEZONALITÁS", "FORECAST.ETS.STAT": "ELŐREJELZÉS.ESIM.STAT", "FORECAST.LINEAR": "ELŐREJELZÉS.LINEÁRIS", "FREQUENCY": "GYAKORISÁG", "GAMMA": "GAMMA", "GAMMADIST": "GAMMA.ELOSZLÁS", "GAMMA.DIST": "GAMMA.ELOSZL", "GAMMAINV": "INVERZ.GAMMA", "GAMMA.INV": "GAMMA.INVERZ", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.PONTOS", "GAUSS": "GAUSS", "GEOMEAN": "MÉRTANI.KÖZÉP", "GROWTH": "NÖV", "HARMEAN": "HARM.KÖZÉP", "HYPGEOM.DIST": "HIPGEOM.ELOSZLÁS", "HYPGEOMDIST": "HIPERGEOM.ELOSZLÁS", "INTERCEPT": "METSZ", "KURT": "CSÚCSOSSÁG", "LARGE": "NAGY", "LINEST": "LIN.ILL", "LOGEST": "LOG.ILL", "LOGINV": "INVERZ.LOG.ELOSZLÁS", "LOGNORM.DIST": "LOGNORM.ELOSZLÁS", "LOGNORM.INV": "LOGNORM.INVERZ", "LOGNORMDIST": "LOG.ELOSZLÁS", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAXHA", "MEDIAN": "MEDIÁN", "MIN": "MIN", "MINA": "MIN2", "MINIFS": "MINHA", "MODE": "MÓDUSZ", "MODE.MULT": "MÓDUSZ.TÖBB", "MODE.SNGL": "MÓDUSZ.EGY", "NEGBINOM.DIST": "NEGBINOM.ELOSZLÁS", "NEGBINOMDIST": "NEGBINOM.ELOSZL", "NORM.DIST": "NORM.ELOSZLÁS", "NORM.INV": "NORM.INVERZ", "NORM.S.DIST": "NORM.S.ELOSZLÁS", "NORM.S.INV": "NORM.S.INVERZ", "NORMDIST": "NORM.ELOSZL", "NORMINV": "INVERZ.NORM", "NORMSDIST": "STNORMELOSZL", "NORMSINV": "INVERZ.STNORM", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTILIS", "PERCENTILE.EXC": "PERCENTILIS.KIZÁR", "PERCENTILE.INC": "PERCENTILIS.TARTALMAZ", "PERCENTRANK": "SZÁZALÉKRANG", "PERCENTRANK.EXC": "SZÁZALÉKRANG.KIZÁR", "PERCENTRANK.INC": "SZÁZALÉKRANG.TARTALMAZ", "PERMUT": "VARIÁCIÓK", "PERMUTATIONA": "VARIÁCIÓK.ISM", "PHI": "FI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.ELOSZLÁS", "PROB": "VALÓSZÍNŰSÉG", "QUARTILE": "KVARTILIS", "QUARTILE.INC": "KVARTILIS.TARTALMAZ", "QUARTILE.EXC": "KVARTILIS.KIZÁR", "RANK.AVG": "RANG.ÁTL", "RANK.EQ": "RANG.EGY", "RANK": "SORSZÁM", "RSQ": "RNÉGYZET", "SKEW": "FERDESÉG", "SKEW.P": "FERDESÉG.P", "SLOPE": "MEREDEKSÉG", "SMALL": "KICSI", "STANDARDIZE": "NORMALIZÁLÁS", "STDEV": "SZÓRÁS", "STDEV.P": "SZÓR.S", "STDEV.S": "SZÓR.M", "STDEVA": "SZÓRÁSA", "STDEVP": "SZÓRÁSP", "STDEVPA": "SZÓRÁSPA", "STEYX": "STHIBAYX", "TDIST": "T.ELOSZLÁS", "TINV": "INVERZ.T", "T.DIST": "T.ELOSZL", "T.DIST.2T": "T.ELOSZLÁS.2SZ", "T.DIST.RT": "T.ELOSZLÁS.JOBB", "T.INV": "T.INVERZ", "T.INV.2T": "T.INVERZ.2SZ", "VAR": "VAR", "VAR.P": "VAR.S", "VAR.S": "VAR.M", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.ELOSZLÁS", "Z.TEST": "Z.PRÓB", "ZTEST": "Z.PRÓBA", "ACCRINT": "IDŐSZAKI.KAMAT", "ACCRINTM": "LEJÁRATI.KAMAT", "AMORDEGRC": "ÉRTÉKCSÖKK.TÉNYEZŐVEL", "AMORLINC": "ÉRTÉKCSÖKK", "COUPDAYBS": "SZELVÉNYIDŐ.KEZDETTŐL", "COUPDAYS": "SZELVÉNYIDŐ", "COUPDAYSNC": "SZELVÉNYIDŐ.KIFIZETÉSTŐL", "COUPNCD": "ELSŐ.SZELVÉNYDÁTUM", "COUPNUM": "SZELVÉNYSZÁM", "COUPPCD": "UTOLSÓ.SZELVÉNYDÁTUM", "CUMIPMT": "ÖSSZES.KAMAT", "CUMPRINC": "ÖSSZES.TŐKERÉSZ", "DB": "KCS2", "DDB": "KCSA", "DISC": "LESZÁM", "DOLLARDE": "FORINT.DEC", "DOLLARFR": "FORINT.TÖRT", "DURATION": "KAMATÉRZ", "EFFECT": "TÉNYLEGES", "FV": "JBÉ", "FVSCHEDULE": "KJÉ", "INTRATE": "KAMATRÁTA", "IPMT": "RRÉSZLET", "IRR": "BMR", "ISPMT": "LRÉSZLETKAMAT", "MDURATION": "MKAMATÉRZ", "MIRR": "MEGTÉRÜLÉS", "NOMINAL": "NÉVLEGES", "NPER": "PER.SZÁM", "NPV": "NMÉ", "ODDFPRICE": "ELTÉRŐ.EÁR", "ODDFYIELD": "ELTÉRŐ.EHOZAM", "ODDLPRICE": "ELTÉRŐ.UÁR", "ODDLYIELD": "ELTÉRŐ.UHOZAM", "PDURATION": "KAMATÉRZ.PER", "PMT": "RÉSZLET", "PPMT": "PRÉSZLET", "PRICE": "ÁR", "PRICEDISC": "ÁR.LESZÁM", "PRICEMAT": "ÁR.LEJÁRAT", "PV": "MÉ", "RATE": "RÁTA", "RECEIVED": "KAPOTT", "RRI": "MR", "SLN": "LCSA", "SYD": "ÉSZÖ", "TBILLEQ": "KJEGY.EGYENÉRT", "TBILLPRICE": "KJEGY.ÁR", "TBILLYIELD": "KJEGY.HOZAM", "VDB": "ÉCSRI", "XIRR": "XBMR", "XNPV": "XNJÉ", "YIELD": "HOZAM", "YIELDDISC": "HOZAM.LESZÁM", "YIELDMAT": "HOZAM.LEJÁRAT", "ABS": "ABS", "ACOS": "ARCCOS", "ACOSH": "ACOSH", "ACOT": "ARCCOT", "ACOTH": "ARCCOTH", "AGGREGATE": "ÖSSZESÍT", "ARABIC": "ARAB", "ASC": "ASC", "ASIN": "ARCSIN", "ASINH": "ASINH", "ATAN": "ARCTAN", "ATAN2": "ARCTAN2", "ATANH": "ATANH", "BASE": "ALAP", "CEILING": "PLAFON", "CEILING.MATH": "PLAFON.MAT", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "KOMBINÁCIÓK", "COMBINA": "KOMBINÁCIÓK.ISM", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "TIZEDES", "DEGREES": "FOK", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "PÁROS", "EXP": "KITEVŐ", "FACT": "FAKT", "FACTDOUBLE": "FAKTDUPLA", "FLOOR": "PADLÓ", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "PADLÓ.MAT", "GCD": "LKO", "INT": "INT", "ISO.CEILING": "ISO.CEILING", "LCM": "LKT", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "INVERZ.MÁTRIX", "MMULT": "MSZORZAT", "MOD": "MARADÉK", "MROUND": "TÖBBSZ.KEREKÍT", "MULTINOMIAL": "SZORHÁNYFAKT", "MUNIT": "MMÁTRIX", "ODD": "PÁRATLAN", "PI": "PI", "POWER": "HATVÁNY", "PRODUCT": "SZORZAT", "QUOTIENT": "KVÓCIENS", "RADIANS": "RADIÁN", "RAND": "VÉL", "RANDARRAY": "VÉLETLENTÖMB", "RANDBETWEEN": "VÉLETLEN.KÖZÖTT", "ROMAN": "RÓMAI", "ROUND": "KEREKÍTÉS", "ROUNDDOWN": "KEREK.LE", "ROUNDUP": "KEREK.FEL", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SORÖSSZEG", "SIGN": "ELŐJEL", "SIN": "SIN", "SINH": "SINH", "SQRT": "GYÖK", "SQRTPI": "GYÖKPI", "SUBTOTAL": "RÉSZÖSSZEG", "SUM": "SZUM", "SUMIF": "SZUMHA", "SUMIFS": "SZUMHATÖBB", "SUMPRODUCT": "SZORZATÖSSZEG", "SUMSQ": "NÉGYZETÖSSZEG", "SUMX2MY2": "SZUMX2BŐLY2", "SUMX2PY2": "SZUMX2MEGY2", "SUMXMY2": "SZUMXBŐLY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "CSONK", "ADDRESS": "CÍM", "CHOOSE": "VÁLASZT", "COLUMN": "OSZLOP", "COLUMNS": "OSZLOPOK", "FORMULATEXT": "KÉPLETSZÖVEG", "HLOOKUP": "VKERES", "HYPERLINK": "HIPERHIVATKOZÁS", "INDEX": "INDEX", "INDIRECT": "INDIREKT", "LOOKUP": "KERES", "MATCH": "HOL.VAN", "OFFSET": "ELTOLÁS", "ROW": "SOR", "ROWS": "SOROK", "TRANSPOSE": "TRANSZPONÁLÁS", "UNIQUE": "EGYEDI", "VLOOKUP": "FKERES", "XLOOKUP": "XKERES", "CELL": "CELL", "ERROR.TYPE": "HIBA.TÍPUS", "ISBLANK": "ÜRES", "ISERR": "HIBA.E", "ISERROR": "HIBÁS", "ISEVEN": "PÁROSE", "ISFORMULA": "KÉPLET", "ISLOGICAL": "LOGIKAI", "ISNA": "NINCS", "ISNONTEXT": "NEM.SZÖVEG", "ISNUMBER": "SZÁM", "ISODD": "PÁRATLANE", "ISREF": "HIVATKOZÁS", "ISTEXT": "SZÖVEG.E", "N": "S", "NA": "HIÁNYZIK", "SHEET": "LAP", "SHEETS": "LAPOK", "TYPE": "TÍPUS", "AND": "ÉS", "FALSE": "HAMIS", "IF": "HA", "IFS": "HAELSŐIGAZ", "IFERROR": "HAHIBA", "IFNA": "HAHIÁNYZIK", "NOT": "NEM", "OR": "VAGY", "SWITCH": "ÁTVÁLT", "TRUE": "IGAZ", "XOR": "XVAGY", "TEXTBEFORE": "SZÖVEGELŐTTE", "TEXTAFTER": "SZÖVEGUTÁNA", "TEXTSPLIT": "SZÖVEGFELOSZTÁS", "WRAPROWS": "SORTÖRDELÉS", "VSTACK": "FÜGG.HALMOZÁS", "HSTACK": "VÍZSZ.HALMOZÁS", "CHOOSEROWS": "SORVÁLASZTÁS", "CHOOSECOLS": "OSZLOPVÁLASZTÁS", "TOCOL": "OSZLOPHOZ", "TOROW": "SORHOZ", "WRAPCOLS": "OSZLOPTÖRDELÉS", "TAKE": "ÁTHELYEZ", "DROP": "ELTÁVOLÍT", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}