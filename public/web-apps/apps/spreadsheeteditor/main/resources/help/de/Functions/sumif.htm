﻿<!DOCTYPE html>
<html>
	<head>
		<title>SUMMEWENN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="<PERSON>rf<PERSON><PERSON>, wie Sie die SUMMEWENN-Formel in Excel-Tabellen und kompatiblen Dateien in ONLYOFFICE verwenden." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>SUMMEWENN-Funktion</h1>
			<p>Die Funktion <b>SUMMEWENN</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um alle Zahlen in gewählten Zellenbereich anhand vom angegebenen Kriterium zu addieren und das Ergebnis zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>SUMMEWENN</b> ist:</p>
			<p style="text-indent: 150px;"><b><em>SUMMEWENN(Bereich;Suchkriterien;[Summe_Bereich])</em></b></p>
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Bereich</em></b> ist der Zellbereich, den Sie nach Kriterien auswerten möchten.</p>
			<p style="text-indent: 50px;"><b><em>Suchkriterien</em></b> sind die Suchkriterien mit denen definiert wird, welche Zellen addiert werden sollen, diese können manuell eingegeben werden oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p style="text-indent: 50px;"><b><em>Summe_Bereich</em></b> ist der Zellenbereich, der addiert werden soll. Dieses Argument ist optional. Wird es ausgelassen, addiert die Funktion die Zahlen von <b><em>Bereich</em></b>.</p>
			<p class="note">Sie können Platzhalterzeichen verwenden, wenn Sie Kriterien angeben. Das Fragezeichen &quot;?&quot; kann ein beliebiges einzelnes Zeichen ersetzen und der Stern &quot;*&quot; kann anstelle einer beliebigen Anzahl von Zeichen verwendet werden.</p>
			<h2>Wie funktioniert SUMMEWENN</h2>
			<p>Anwendung der Funktion <b>SUMMEWENN</b>:</p>
			<ol>
				<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
				<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
				<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
				<li>Klicken Sie auf die Funktion <b>SUMMEWENN</b>.</li>
				<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
				<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<img class="gif" alt="SUMMEWENN-Funktion gif" src="../images/sumif_function.gif" />
		</div>
	</body>
</html>