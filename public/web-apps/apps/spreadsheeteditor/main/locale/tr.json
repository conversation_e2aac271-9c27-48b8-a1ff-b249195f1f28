{"cancelButtonText": "İptal Et", "Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.textEnterMessage": "Mesajınızı buraya giriniz", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alan", "Common.define.chartData.textAreaStackedPer": "%100 Yığılmış alan", "Common.define.chartData.textBar": "Çubuk grafik", "Common.define.chartData.textBarNormal": "kümelenmiş sütun", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Kümelenmiş sütun", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON>", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sütun", "Common.define.chartData.textBarStacked3d": "3-D Yığılmış sütun ", "Common.define.chartData.textBarStackedPer": "%100 Yığılmış sütun", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Yığılmış sütun", "Common.define.chartData.textCharts": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON> grafik", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "kombo", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alan - kümelenmiş sütun", "Common.define.chartData.textComboBarLine": "Kümelenmiş sütun - satır", "Common.define.chartData.textComboBarLineSecondary": "Kümelenmiş sütun - ikincil eksendeki çizgi", "Common.define.chartData.textComboCustom": "<PERSON><PERSON> k<PERSON>", "Common.define.chartData.textDoughnut": "Hal<PERSON>", "Common.define.chartData.textHBarNormal": "kümelen<PERSON>ş <PERSON>", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> Kümelenmiş çubuk", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Yığılmış çubuk ", "Common.define.chartData.textHBarStackedPer": "%100 Yığılmış çubuk", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Yığılmış çubuk", "Common.define.chartData.textLine": "Çizgi grafiği", "Common.define.chartData.textLine3d": "3-<PERSON>", "Common.define.chartData.textLineMarker": "İşaretli çizgi ", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş çizgi", "Common.define.chartData.textLineStackedMarker": "İşaretleyicilerle yığılmış çizgi", "Common.define.chartData.textLineStackedPer": "%100 Yığılmış çizgi", "Common.define.chartData.textLineStackedPerMarker": "İşaretli %100 Yığılmış çizgi ", "Common.define.chartData.textPie": "<PERSON><PERSON>", "Common.define.chartData.textPie3d": "3-D Dilim grafik", "Common.define.chartData.textPoint": "Nokta grafiği", "Common.define.chartData.textScatter": "Dağılım", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON>le dağılım", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON>z çizgiler ve işaretleyicilerle dağılım", "Common.define.chartData.textScatterSmooth": "Yumuşak çizgilerle dağılım", "Common.define.chartData.textScatterSmoothMarker": "Yumuşak çizgiler ve işaretleyicilerle dağılım", "Common.define.chartData.textSparks": "Mini grafikler", "Common.define.chartData.textStock": "Stok Grafiği", "Common.define.chartData.textSurface": "Yüzey", "Common.define.chartData.textWinLossSpark": "Kazanç/Kayıp", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.text1Above": "1. standart <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>", "Common.define.conditionalData.text1Below": "1. standart <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.text2Above": "2 standart gel<PERSON><PERSON><PERSON><PERSON><PERSON> yuka<PERSON>", "Common.define.conditionalData.text2Below": "2 standart gel<PERSON><PERSON><PERSON><PERSON><PERSON>ıda", "Common.define.conditionalData.text3Above": "3 standart gel<PERSON><PERSON><PERSON><PERSON><PERSON> yuka<PERSON>da", "Common.define.conditionalData.text3Below": "3 standart geli<PERSON><PERSON><PERSON><PERSON>ağıda", "Common.define.conditionalData.textAbove": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textAverage": "ORTALAMA", "Common.define.conditionalData.textBegins": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>ın:", "Common.define.conditionalData.textBelow": "Altında", "Common.define.conditionalData.textBetween": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Boş", "Common.define.conditionalData.textBlanks": "boşluklar içerir", "Common.define.conditionalData.textBottom": "Alt", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "<PERSON><PERSON>", "Common.define.conditionalData.textDate": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDuplicate": "Çoğalt", "Common.define.conditionalData.textEnds": "<PERSON><PERSON><PERSON><PERSON> biten:", "Common.define.conditionalData.textEqAbove": "Eşit veya üstü", "Common.define.conditionalData.textEqBelow": "Eşit veya altı", "Common.define.conditionalData.textEqual": "Eş<PERSON><PERSON>", "Common.define.conditionalData.textError": "<PERSON><PERSON>", "Common.define.conditionalData.textErrors": "<PERSON><PERSON>r içeriyor", "Common.define.conditionalData.textFormula": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreater": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreaterEq": "büyük veya eşit", "Common.define.conditionalData.textIconSets": "<PERSON><PERSON><PERSON> setleri", "Common.define.conditionalData.textLast7days": "Son 7 gün i<PERSON><PERSON>e", "Common.define.conditionalData.textLastMonth": "Geçen ay", "Common.define.conditionalData.textLastWeek": "geçen hafta", "Common.define.conditionalData.textLess": "Küçüktür", "Common.define.conditionalData.textLessEq": "Küçük eşittir", "Common.define.conditionalData.textNextMonth": "Sonraki ay", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON><PERSON> hafta", "Common.define.conditionalData.textNotBetween": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotBlanks": "Boşluk içermez", "Common.define.conditionalData.textNotContains": "ş<PERSON>u içermeyen:", "Common.define.conditionalData.textNotEqual": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotErrors": "Hata içermez", "Common.define.conditionalData.textText": "<PERSON><PERSON>", "Common.define.conditionalData.textThisMonth": "<PERSON>u ay", "Common.define.conditionalData.textThisWeek": "Bu hafta", "Common.define.conditionalData.textToday": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTop": "Üst", "Common.define.conditionalData.textUnique": "Benzersiz", "Common.define.conditionalData.textValue": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON><PERSON>", "Common.Translation.textMoreButton": "<PERSON><PERSON> fazla", "Common.Translation.warnFileLocked": "Do<PERSON>a başka bir uygulamada düzenleniyor. Düzenlemeye devam edebilir ve kopya olarak kaydedebilirsiniz.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON>", "Common.Translation.warnFileLockedBtnView": "Görüntülemek için aç", "Common.UI.ButtonColored.textAutoColor": "Otomatik", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Sınır yok", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Sınır yok", "Common.UI.ComboDataView.emptyComboText": "Stil yok", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Mevcut", "Common.UI.ExtendedColorDialog.textHexErr": "G<PERSON><PERSON> değer yanlış. <br> Lütfen 000000 ile FFFFFF arasında değer giriniz.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Girilen değer yanlış. <br> Lütfen 0 ile 255 arasında sayısal değer giriniz.", "Common.UI.HSBColorPicker.textNoColor": "Renk yok", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Parolayı gizle", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Parolayı göster", "Common.UI.SearchDialog.textHighlight": "Vurgu sonuçları", "Common.UI.SearchDialog.textMatchCase": "Büyük küçük harfe duyarlı", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON> g<PERSON> metini giriniz", "Common.UI.SearchDialog.textSearchStart": "Metninizi buraya giri<PERSON>z", "Common.UI.SearchDialog.textTitle": "Bul ve Değiştir", "Common.UI.SearchDialog.textTitle2": "Bul", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON> tam keli<PERSON>er", "Common.UI.SearchDialog.txtBtnHideReplace": "Değiştirmeyi Gizle", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON>", "Common.UI.SynchronizeTip.textDontShow": "Bu mesajı bir daha gö<PERSON>me", "Common.UI.SynchronizeTip.textSynchronize": "Döküman başka bir kullanıcı tarafından değiştirildi.<br> Lütfen değişikleri kaydetmek için tıklayın ve güncellemeleri yenileyin.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasik Aydınlık", "Common.UI.Themes.txtThemeDark": "Karanlık", "Common.UI.Themes.txtThemeLight": "Aydınlık", "Common.UI.Window.cancelButtonText": "İptal Et", "Common.UI.Window.closeButtonText": "Ka<PERSON><PERSON>", "Common.UI.Window.noButtonText": "Hay<PERSON><PERSON>", "Common.UI.Window.okButtonText": "<PERSON><PERSON>", "Common.UI.Window.textConfirmation": "Konfirmasyon", "Common.UI.Window.textDontShow": "Bu mesajı bir daha gö<PERSON>me", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON>", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "adres:", "Common.Views.About.txtLicensee": "LİSANS SAHİBİ", "Common.Views.About.txtLicensor": "LİSANS VEREN", "Common.Views.About.txtMail": "Eposta:", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel:", "Common.Views.About.txtVersion": "Versiyon", "Common.Views.AutoCorrectDialog.textAdd": "ekle", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Çalışırken uygula", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Otomatik Düzeltme", "Common.Views.AutoCorrectDialog.textAutoFormat": "Yazarken Otomatik Biçimlendir", "Common.Views.AutoCorrectDialog.textBy": "Tara<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDelete": "Sil", "Common.Views.AutoCorrectDialog.textHyperlink": "Köprülü internet ve ağ yolları", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematik Otomatik Düzeltme ", "Common.Views.AutoCorrectDialog.textNewRowCol": "Tabloya yeni satırlar ve sütunlar ekle", "Common.Views.AutoCorrectDialog.textRecognized": "Bilinen fonksiyonlar", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Aşağıdaki ifadeler tanınan matematik ifadeleridir. Otomatik olarak eğik yazılmazlar.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON> ", "Common.Views.AutoCorrectDialog.textReplaceType": "<PERSON><PERSON>ğiştirin", "Common.Views.AutoCorrectDialog.textReset": "Sıfırla", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Otomatik Düzeltme", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Bilinen fonksiyonlar yalnızca A'dan Z'ye kadar olan harfler<PERSON>, büyük veya küçük harfleri içermelidir.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Eklediğiniz tüm ifadeler kaldırılacak ve kaldırılanlar geri yüklenecektir. Devam etmek istiyor musun?", "Common.Views.AutoCorrectDialog.warnReplace": "%1 için otomatik düzeltme girişi zaten var. Değiştirmek istiyor musun?", "Common.Views.AutoCorrectDialog.warnReset": "Eklediğiniz tüm otomatik düzeltmeler kaldırılacak ve değiştirilenler orijinal değerlerine geri dönecektir. Devam etmek istiyor musun?", "Common.Views.AutoCorrectDialog.warnRestore": "%1 için otomatik düzeltme girişi orijinal değerine sıfırlanacak. <PERSON><PERSON> etmek istiyor musun?", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "<PERSON><PERSON>", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON>", "Common.Views.Comments.mniDateAsc": "En eski", "Common.Views.Comments.mniDateDesc": "En yeni", "Common.Views.Comments.mniFilterGroups": "Gruba göre Filtrele", "Common.Views.Comments.mniPositionAsc": "üstten", "Common.Views.Comments.mniPositionDesc": "Alttan", "Common.Views.Comments.textAdd": "<PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Dökümana yorum ekle", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON> e<PERSON>", "Common.Views.Comments.textAll": "Tümü", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "İptal Et", "Common.Views.Comments.textClose": "Ka<PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Yorumları kapat", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON><PERSON> buraya giriniz", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textReply": "Yanıtla", "Common.Views.Comments.textResolve": "Çöz", "Common.Views.Comments.textResolved": "Çözüldü", "Common.Views.Comments.textSort": "Yorumları sırala", "Common.Views.CopyWarningDialog.textDontShow": "Bu mesajı bir daha gö<PERSON>me", "Common.Views.CopyWarningDialog.textMsg": "Editör araç çubuğu tuşlarını kullanarak eylemleri kopyala,kes ve yapıştır ve içerik menüsü eylemleri sadece bu editör sekmesiyle yapılabilir. <br><br>Editör sekmesi dışındaki uygulamalara/dan kopyalamak yada yapıştırmak için şu klavye kombinasyonlarını kullanınız:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON> <PERSON>", "Common.Views.CopyWarningDialog.textToCopy": "Kopyalamak için", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "Yapıştırmak için", "Common.Views.DocumentAccessDialog.textLoading": "Yükleniyor...", "Common.Views.DocumentAccessDialog.textTitle": "Paylaşım Ayarları", "Common.Views.EditNameDialog.textLabel": "Etiket:", "Common.Views.EditNameDialog.textLabelError": "Etiket boş olamaz.", "Common.Views.Header.labelCoUsersDescr": "Belgeyi düzenleyen kullanıcılar:", "Common.Views.Header.textAddFavorite": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textAdvSettings": "Gelişmiş <PERSON>", "Common.Views.Header.textBack": "<PERSON><PERSON><PERSON> kon<PERSON>u aç", "Common.Views.Header.textCompactView": "<PERSON><PERSON> Gizle", "Common.Views.Header.textHideLines": "Cetvelleri Gizle", "Common.Views.Header.textHideStatusBar": "Sayfa ve durum çubuklarını birleştirin", "Common.Views.Header.textRemoveFavorite": "<PERSON>av<PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveBegin": "Kay<PERSON>ili<PERSON>r...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON>iklikler kaydedildi", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON>iklikler kaydedildi", "Common.Views.Header.textZoom": "Yakınlaştırma", "Common.Views.Header.tipAccessRights": "Belge eri<PERSON><PERSON> ha<PERSON> yönet", "Common.Views.Header.tipDownload": "Dosyayı indir", "Common.Views.Header.tipGoEdit": "<PERSON><PERSON><PERSON> be<PERSON> d<PERSON>", "Common.Views.Header.tipPrint": "Belgeyi yazdır", "Common.Views.Header.tipRedo": "<PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON>", "Common.Views.Header.tipUndock": "Ayrı pencereye çıkarın", "Common.Views.Header.tipViewSettings": "G<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewUsers": "Kullanıcıları görüntüle ve belge erişim haklarını yönet", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON><PERSON> değiştir", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON>ı<PERSON>", "Common.Views.History.textCloseHistory": "Geçmişi kapat", "Common.Views.History.textHide": "Dar<PERSON><PERSON>", "Common.Views.History.textHideAll": "Detaylı değişiklikleri sakla", "Common.Views.History.textRestore": "<PERSON><PERSON>", "Common.Views.History.textShow": "Genişlet", "Common.Views.History.textShowAll": "Ayrıntılı değişiklikleri göster", "Common.Views.History.textVer": "sür.", "Common.Views.ImageFromUrlDialog.textUrl": "Resim URL'sini yapıştır:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON>u alan \"http://www.example.com\" formatında URL olmalıdır", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON> ", "Common.Views.ListSettingsDialog.textNumbering": "Numaralı", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON> işaret<PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Renk", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON><PERSON> madde", "Common.Views.ListSettingsDialog.txtNone": "Hiç<PERSON>i", "Common.Views.ListSettingsDialog.txtOfText": "Metnin %'si", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Başlangıç", "Common.Views.ListSettingsDialog.txtSymbol": "<PERSON>m<PERSON>", "Common.Views.ListSettingsDialog.txtTitle": "Liste Ayarları", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "Dosyayı Kapat", "Common.Views.OpenDialog.textInvalidRange": "Geçersiz hücre aralığı", "Common.Views.OpenDialog.textSelectData": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtAdvanced": "Gelişmiş", "Common.Views.OpenDialog.txtColon": "Kolon", "Common.Views.OpenDialog.txtComma": "Virg<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Sınırlayıcı", "Common.Views.OpenDialog.txtDestData": "Verileri nereye koyacağınızı seçin", "Common.Views.OpenDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "Common.Views.OpenDialog.txtEncoding": "Kodlama", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Dosyayı Açmak için Parola Girin", "Common.Views.OpenDialog.txtOther": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "Şifre", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Şifreyi girip <PERSON> açtığınızda, dosyanın mevcut şifresi sıfırlanacaktır.", "Common.Views.OpenDialog.txtSemicolon": "Noktalı virgül", "Common.Views.OpenDialog.txtSpace": "Boşluk", "Common.Views.OpenDialog.txtTab": "Sekme", "Common.Views.OpenDialog.txtTitle": "%1 seçenekleri seçin", "Common.Views.OpenDialog.txtTitleProtected": "Korumalı Dosya", "Common.Views.PasswordDialog.txtDescription": "<PERSON>u be<PERSON><PERSON> korumak için bir <PERSON><PERSON><PERSON> beli<PERSON>", "Common.Views.PasswordDialog.txtIncorrectPwd": "Onay şifresi aynı değil", "Common.Views.PasswordDialog.txtPassword": "Şifre", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON> tekrar girin", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> beli<PERSON>", "Common.Views.PasswordDialog.txtWarning": "Dikkat: Parol<PERSON>ı ka<PERSON>beder veya unutursanız, kurtarılamaz. Lütfen parolayı güvenli bir yerde saklayın.", "Common.Views.PluginDlg.textLoading": "Yükleniyor", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "Yükleniyor", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Şifre ile şifrele", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON><PERSON> veya sil", "Common.Views.Protection.hintSignature": "Dijital imza veya imza satırı ekleyin", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON><PERSON> sil", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON> imza ekle", "Common.Views.Protection.txtSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON>za <PERSON>ı<PERSON> e<PERSON>", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON> adı", "Common.Views.RenameDialog.txtInvalidName": "Dosya adı aşağıdaki karakterlerden herhangi birini içeremez:", "Common.Views.ReviewChanges.hintNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.hintPrev": "Önceki değişikliğe", "Common.Views.ReviewChanges.strFast": "Hızlı", "Common.Views.ReviewChanges.strFastDesc": "Gerçek zamanlı ortak çalışma. Bütün değişiklikler otomatik olarak kaydedilir.", "Common.Views.ReviewChanges.strStrictDesc": "Değişiklikleri kaydetmek için 'Kaydet' butonunu kullanın.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Mevcut değişiklikleri kabul et", "Common.Views.ReviewChanges.tipCoAuthMode": "Ortak çalışma modu", "Common.Views.ReviewChanges.tipCommentRem": "Yorumları kaldır", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Mevcut yorumları kaldır", "Common.Views.ReviewChanges.tipCommentResolve": "Yorumları çöz", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Mevcut yorumları çözümle", "Common.Views.ReviewChanges.tipHistory": "Versiyon geçmişini göster", "Common.Views.ReviewChanges.tipRejectCurrent": "Mevcut Değişiklikleri Reddet", "Common.Views.ReviewChanges.tipReview": "Değişiklikleri İzle", "Common.Views.ReviewChanges.tipReviewView": "Değişikliklerin gösterilmesini istediğiniz modu seçin", "Common.Views.ReviewChanges.tipSetDocLang": "<PERSON>ge di<PERSON> belirle", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON><PERSON> deneti<PERSON>", "Common.Views.ReviewChanges.tipSharing": "Belge eri<PERSON><PERSON> ha<PERSON> yönet", "Common.Views.ReviewChanges.txtAccept": "Kabul Et", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON>m Değişiklikleri Kabul Et", "Common.Views.ReviewChanges.txtAcceptChanges": "Değişiklikleri Kabul Et", "Common.Views.ReviewChanges.txtAcceptCurrent": "Mevcut değişiklikleri kabul et", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Ka<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Ortak Düzenleme Modu", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON><PERSON> yo<PERSON>ları kaldır", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Mevcut yorumları kaldır", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON>ımı kaldır", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON><PERSON> mevcut yorumlarımı kaldır", "Common.Views.ReviewChanges.txtCommentRemove": "Kaldır", "Common.Views.ReviewChanges.txtCommentResolve": "Çöz", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON>m Yo<PERSON>ları Çöz", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Mevcut yorumları çözümle", "Common.Views.ReviewChanges.txtCommentResolveMy": "Yorumlarımı Çöz", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Mevcut Yorumlarımı Çöz", "Common.Views.ReviewChanges.txtDocLang": "Dil", "Common.Views.ReviewChanges.txtFinal": "T<PERSON>m değişiklikler onaylandı (Önizleme)", "Common.Views.ReviewChanges.txtFinalCap": "Son", "Common.Views.ReviewChanges.txtHistory": " Versiyon Geçmişi", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> (Düzenleme)", "Common.Views.ReviewChanges.txtMarkupCap": "İşaretleme", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> reddedildi (Önizleme)", "Common.Views.ReviewChanges.txtOriginalCap": "Orjinal", "Common.Views.ReviewChanges.txtPrev": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON>m Değişiklikleri Reddet", "Common.Views.ReviewChanges.txtRejectChanges": "Değişiklikleri Reddet", "Common.Views.ReviewChanges.txtRejectCurrent": "Mevcut Değişiklikleri Reddet", "Common.Views.ReviewChanges.txtSharing": "Paylaşım", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON> deneti<PERSON>", "Common.Views.ReviewChanges.txtTurnon": "Değişiklikleri İzle", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "ekle", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON> e<PERSON>", "Common.Views.ReviewPopover.textCancel": "İptal", "Common.Views.ReviewPopover.textClose": "Ka<PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textMention": "+bah<PERSON>me belgeye eri<PERSON>im sağlayacak ve bir e-posta gönderecek", "Common.Views.ReviewPopover.textMentionNotify": "+mention kullanıcıyı e-posta yoluyla bilgilendirecek", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "Yanıtla", "Common.Views.ReviewPopover.textResolve": "Çöz", "Common.Views.ReviewPopover.txtDeleteTip": "Sil", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Yükleniyor", "Common.Views.SaveAsDlg.textTitle": "Kaydetmek için <PERSON>", "Common.Views.SelectFileDlg.textLoading": "Yükleniyor", "Common.Views.SelectFileDlg.textTitle": "Veri Kaynağını Seçin", "Common.Views.SignDialog.textBold": "Kalı<PERSON>", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "İmzalayan adını girin", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "İmzalayan adı boş bırakılmamalıdır.", "Common.Views.SignDialog.textPurpose": "<PERSON>u belgeyi im<PERSON>ın amacı", "Common.Views.SignDialog.textSelect": "Seç", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "<PERSON><PERSON><PERSON>ü<PERSON>ü<PERSON>r", "Common.Views.SignDialog.textTitle": "Belgeyi <PERSON>a", "Common.Views.SignDialog.textUseImage": "veya bir resmi imza olarak kullanmak için '<PERSON><PERSON><PERSON>i tıklayın", "Common.Views.SignDialog.textValid": "%1 ile %2 arasında geçerlidir", "Common.Views.SignDialog.tipFontName": "Yazı Tipi", "Common.Views.SignDialog.tipFontSize": "Yazı Boyutu", "Common.Views.SignSettingsDialog.textAllowComment": "İmzalayanın imza iletişim kutusuna yorum eklemesine izin ver", "Common.Views.SignSettingsDialog.textInfoEmail": "E-posta", "Common.Views.SignSettingsDialog.textInfoName": "Ad", "Common.Views.SignSettingsDialog.textInfoTitle": "İmzalayan Ünvanı", "Common.Views.SignSettingsDialog.textInstructions": "İmzalayan <PERSON><PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON>ında imzalama ta<PERSON>hini g<PERSON>", "Common.Views.SignSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "<PERSON><PERSON><PERSON> kodu", "Common.Views.SymbolTableDialog.textCopyright": "Telif <PERSON>", "Common.Views.SymbolTableDialog.textDCQuote": "Çift Tırnak (Kapanış)", "Common.Views.SymbolTableDialog.textDOQuote": "Çift Tırnak (Açılış)", "Common.Views.SymbolTableDialog.textEllipsis": "Elips", "Common.Views.SymbolTableDialog.textEmDash": "Uzun Tire", "Common.Views.SymbolTableDialog.textEmSpace": "Uzun Boşluk", "Common.Views.SymbolTableDialog.textEnDash": "Kısa Tire", "Common.Views.SymbolTableDialog.textEnSpace": "Boşluk", "Common.Views.SymbolTableDialog.textFont": "Yazı Tipi", "Common.Views.SymbolTableDialog.textNBHyphen": "Bölünemez kısa <PERSON>", "Common.Views.SymbolTableDialog.textNBSpace": "Bölünemez Boşluk", "Common.Views.SymbolTableDialog.textPilcrow": "Paragra<PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Uzun boşluk", "Common.Views.SymbolTableDialog.textRange": "Aralık", "Common.Views.SymbolTableDialog.textRecent": "<PERSON><PERSON>k k<PERSON>anılan simgeler", "Common.Views.SymbolTableDialog.textRegistered": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSCQuote": "Tek Tırnak (Kapanış)", "Common.Views.SymbolTableDialog.textSection": "Bölüm", "Common.Views.SymbolTableDialog.textShortcut": "Kısayol tuşu", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Tek Tırnak (Açılış)", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON> karak<PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "<PERSON>m<PERSON>", "Common.Views.SymbolTableDialog.textTradeMark": "Telif <PERSON>", "Common.Views.UserNameDialog.textDontShow": "bana bir daha sorma", "Common.Views.UserNameDialog.textLabel": "Etiket:", "Common.Views.UserNameDialog.textLabelError": "Etiket boş olamaz.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "URL belirtmeniz gerekiyor.", "SSE.Controllers.DataTab.textRows": "Satırlar", "SSE.Controllers.DataTab.textWizard": "<PERSON><PERSON>", "SSE.Controllers.DataTab.txtDataValidation": "<PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpand": "Genişlet", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Seç<PERSON>in yanındaki veriler kaldırılmayacaktır. Seçimi bitişik verileri içerecek şekilde genişletmek mi yoksa yalnızca seçili hücrelerle devam etmek mi istiyorsunuz?", "SSE.Controllers.DataTab.txtExtendDataValidation": "<PERSON><PERSON><PERSON>, Veri Doğrulama ayarları olmayan bazı hücreler içeriyor.<br>Veri Doğrulamayı bu hücrelere genişletmek istiyor musunuz?", "SSE.Controllers.DataTab.txtImportWizard": "Metin İçe Aktarma Sihirbazı", "SSE.Controllers.DataTab.txtRemDuplicates": "Yinelenenleri Kaldır", "SSE.Controllers.DataTab.txtRemoveDataValidation": "<PERSON><PERSON><PERSON>, birden fazla doğrulama türü içeriyor.<br><PERSON><PERSON>ç<PERSON>li ayarlar silinsin ve devam edilsin mi?", "SSE.Controllers.DataTab.txtRemSelected": "Seçilenden kaldır", "SSE.Controllers.DataTab.txtUrlTitle": "Bir veri URL'si yapıştırın", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Orta", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "Satırı Sil", "SSE.Controllers.DocumentHolder.deleteText": "Sil", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Link referansı mevcut değil. Lütfen linki düzeltin veya silin.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON>", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Yukarı Satır", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Aşağı Satır", "SSE.Controllers.DocumentHolder.insertText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.leftText": "Sol", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Uyarı", "SSE.Controllers.DocumentHolder.rightText": "Sağ", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Otomatik Düzeltme Seçenekleri", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON>ği {0} sembol  ({1} piksel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON>r <PERSON>i {0} puan ({1} piksel)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Açmak için bağlantıya tıklayın veya hücreyi seçmek için fare düğmesini tıklayın ve basılı tutun.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Insert Left", "SSE.Controllers.DocumentHolder.textInsertTop": "Insert Top", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON>ı<PERSON>", "SSE.Controllers.DocumentHolder.textStopExpand": "Tabloları otomatik olarak genişletmeyi durdur", "SSE.Controllers.DocumentHolder.textSym": "sem", "SSE.Controllers.DocumentHolder.tipIsLocked": "<PERSON>u öğe başka bir kullanıcı tarafından düzenleniyor.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Ortalama üstü", "SSE.Controllers.DocumentHolder.txtAddBottom": "Alt sınır ekle", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddLB": "Sol alt çizgi ekle", "SSE.Controllers.DocumentHolder.txtAddLeft": "Sol sınır ekle", "SSE.Controllers.DocumentHolder.txtAddLT": "Sol üst çizgi ekle", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON> sınır e<PERSON>", "SSE.Controllers.DocumentHolder.txtAddTop": "Üst sınır ekle", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON> e<PERSON>", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAll": "(Tümü)", "SSE.Controllers.DocumentHolder.txtAnd": "Ve", "SSE.Controllers.DocumentHolder.txtBegins": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>ın:", "SSE.Controllers.DocumentHolder.txtBelowAve": "Ortalama altı", "SSE.Controllers.DocumentHolder.txtBlanks": "(Bo<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Sınır özellikleri", "SSE.Controllers.DocumentHolder.txtBottom": "Alt", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Argümanı sil", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> sonu sil", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Çevreleyen karakterleri sil", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Çevreleyen karakterleri sil", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON> sil", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON> sil", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Kökü sil", "SSE.Controllers.DocumentHolder.txtEnds": "<PERSON><PERSON><PERSON><PERSON> biten:", "SSE.Controllers.DocumentHolder.txtEquals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "<PERSON><PERSON>cre reng<PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Yazı tipi rengine eşit", "SSE.Controllers.DocumentHolder.txtExpand": "Genişlet ve sırala", "SSE.Controllers.DocumentHolder.txtExpandSort": "Seç<PERSON>in yanındaki veri sıralanmayacaktır. Seçimi genişleterek bitişik verilerin dahil edilmesini istiyor musunuz veya mevcut seçim ile devam etmek mi istiyorsunuz?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Alt", "SSE.Controllers.DocumentHolder.txtFilterTop": "Üst", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Lineer <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON><PERSON> frak<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Yığılı fraksiyona değiştir", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "büyük veya eşit", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON> üstünde char", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON> altında char", "SSE.Controllers.DocumentHolder.txtHeight": "Yükseklik", "SSE.Controllers.DocumentHolder.txtHideBottom": "Alt sınırı gizle", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Alt limiti gizle", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON><PERSON> parantezini gizle", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON> gizle", "SSE.Controllers.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON>izle", "SSE.Controllers.DocumentHolder.txtHideLB": "Sol alt çizgiyi gizle", "SSE.Controllers.DocumentHolder.txtHideLeft": "Sol sınırı gizle", "SSE.Controllers.DocumentHolder.txtHideLT": "Sol üst çizgiyi gizle", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Açma parantezini gizle", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Yer tutuc<PERSON>u gizle", "SSE.Controllers.DocumentHolder.txtHideRight": "Sağ sınırı gizle", "SSE.Controllers.DocumentHolder.txtHideTop": "Üst sınırı gizle", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Üst limiti gizle", "SSE.Controllers.DocumentHolder.txtHideVer": "<PERSON><PERSON> gizle", "SSE.Controllers.DocumentHolder.txtImportWizard": "Metin İçe Aktarma Sihirbazı", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON><PERSON>ır", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "<PERSON><PERSON> argüman <PERSON>kle", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtInsertBreak": "<PERSON><PERSON><PERSON> son ekle", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON> denklem ekle", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "<PERSON><PERSON><PERSON><PERSON> denklem ekle", "SSE.Controllers.DocumentHolder.txtItems": "<PERSON><PERSON><PERSON>r", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Yalnızca metni tut", "SSE.Controllers.DocumentHolder.txtLess": "Küçüktür", "SSE.Controllers.DocumentHolder.txtLessEquals": "Küçük eşittir", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON><PERSON>r konum<PERSON>u <PERSON>r", "SSE.Controllers.DocumentHolder.txtLimitOver": "Metin ü<PERSON>ü<PERSON> limit", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Metin altına limit", "SSE.Controllers.DocumentHolder.txtLockSort": "Seçiminizin yanında veriler bulunur, ancak bu hücreleri değiştirmek için yeterli izniniz yok.<br>Geçerli seçimle devam etmek istiyor musunuz?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>luğuyla parantezleri eşle", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "Hücreyi doldurmak için seçenek bulunmuyor.<br>Yalnızca sütundaki metin değerleri ile değiştirilebilir.", "SSE.Controllers.DocumentHolder.txtNotBegins": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>aya<PERSON>:", "SSE.Controllers.DocumentHolder.txtNotContains": "ş<PERSON>u içermeyen:", "SSE.Controllers.DocumentHolder.txtNotEnds": "<PERSON><PERSON><PERSON><PERSON> bitmeyen:", "SSE.Controllers.DocumentHolder.txtNotEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtOr": "veya", "SSE.Controllers.DocumentHolder.txtOverbar": "Metin üstünde bar", "SSE.Controllers.DocumentHolder.txtPaste": "Yapıştır", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Sınırs<PERSON><PERSON> ", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formül + sütun g<PERSON>i", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Hedef formatı", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Sadece formatla yapıştır", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formül + sayı formatı", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formül + tüm bi<PERSON>e", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON>ı<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Bağlı resim", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Koşullu formatı birleştir", "SSE.Controllers.DocumentHolder.txtPastePicture": "Resim", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Kaynak <PERSON>", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpoz", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "<PERSON>ğer + tüm biçim", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Değer + sayı biçimi", "SSE.Controllers.DocumentHolder.txtPasteValues": "<PERSON><PERSON><PERSON>ı<PERSON>tı<PERSON>", "SSE.Controllers.DocumentHolder.txtPercent": "<PERSON><PERSON>z<PERSON>", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Tablo otomatik genişletmeyi yinele", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Kesir barını kaldır", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON><PERSON> kaldır", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON> ka<PERSON> kaldır", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Barı kaldır", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Bu imzayı kaldırmak istiyor musunuz?<br><PERSON><PERSON>.", "SSE.Controllers.DocumentHolder.txtRemScripts": "<PERSON><PERSON>t kaldır", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Altsimge kaldır", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Üstsimge kaldır", "SSE.Controllers.DocumentHolder.txtRowHeight": "Sa<PERSON>ır <PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Metinden sonra simge", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Metinden önce simge", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Alt sınırı göster", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON> parantezini <PERSON>", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Açma parantezini g<PERSON>", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON> tut<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Üst sınırı göster", "SSE.Controllers.DocumentHolder.txtSorting": "Sıralama", "SSE.Controllers.DocumentHolder.txtSortSelected": "Seçili olanları sırala", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Parantezleri g<PERSON>", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Belirtilen sütunun yalnızca bu satırını seçin", "SSE.Controllers.DocumentHolder.txtTop": "Üst", "SSE.Controllers.DocumentHolder.txtUnderbar": "<PERSON><PERSON> altında bar", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Tablo otomatik genişletmesini geri al", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Metin içe aktarma sihirbazını kullan", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Bu bağlantıyı tıklamak cihazınıza ve verilerinize zarar verebilir.<br><PERSON><PERSON> etmek istediğinizden emin misiniz?", "SSE.Controllers.DocumentHolder.txtWidth": "Genişlik", "SSE.Controllers.FormulaDialog.sCategoryAll": "Tümü", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "<PERSON><PERSON> tabanı", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Ta<PERSON>h ve saat", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Mühendislik", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finansal", "SSE.Controllers.FormulaDialog.sCategoryInformation": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 son <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Mantıksal", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematik ve trigonometri", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "İstatistiksel", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Metin ve veri", "SSE.Controllers.LeftMenu.newDocumentTitle": "İsimlendirilmemiş spreadsheet", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textByRows": "Satırlarla", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Versiyon geçmişi yükleniyor...", "SSE.Controllers.LeftMenu.textLookin": "şurada bak:", "SSE.Controllers.LeftMenu.textNoTextFound": "Aradığınız veri bulunamadı. Lütfen arama seçeneklerinizi ayarlayınız.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Değiştirme yapıldı. {0} olay at<PERSON><PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> ya<PERSON>. {0} <PERSON><PERSON>.", "SSE.Controllers.LeftMenu.textSearch": "Ara", "SSE.Controllers.LeftMenu.textSheet": "Say<PERSON>", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "Not defteri", "SSE.Controllers.LeftMenu.txtUntitled": "Başlıksız", "SSE.Controllers.LeftMenu.warnDownloadAs": "Kaydetmeye bu formatta devam ederseniz metin dışında tüm özellikler kaybolacak.<br> <PERSON><PERSON> etmek istediğinizden emin misiniz?", "SSE.Controllers.Main.confirmMoveCellRange": "<PERSON><PERSON><PERSON> hü<PERSON> aralığı veri içerebilir. Operasyona devam etmek istiyor musunuz?", "SSE.Controllers.Main.confirmPutMergeRange": "Kaynak veri birleştirilmiş hücreler içeriyor.<br>Tabloya yapıştırılmadan önce birleştirmeleri kaldırılmıştır.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Başlık satırındaki formüller kaldırılacak ve statik metne dönüştürülecek.<br><PERSON><PERSON> etmek istiyor musunuz?", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON><PERSON><PERSON> süresi aşıldı.", "SSE.Controllers.Main.criticalErrorExtText": "Döküman listesine dönmek için \"Tamam\"'a tıklayın", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "Yükleme başarısız oldu.", "SSE.Controllers.Main.downloadTextText": "E-Tablo indiriliyor...", "SSE.Controllers.Main.downloadTitleText": "E-Ta<PERSON>lo <PERSON>", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON>nen değer bulunamadı.", "SSE.Controllers.Main.errorAccessDeny": "Hakkınız olmayan bir eylem gerçekleştirmeye çalışıyorsunuz.<br>Lütfen Belge Sunucu yöneticinize başvurun.", "SSE.Controllers.Main.errorArgsRange": "<PERSON><PERSON><PERSON> formülde hata oluştu. <br> <PERSON><PERSON><PERSON><PERSON> değişken aralığı kullanıldı.", "SSE.Controllers.Main.errorAutoFilterChange": "Operasyon çalışma tablonuzdaki tablodaki hücreleri kaldırmaya çalıştığından izin verilmiyor.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operasyon çalışma tablosunda filtreli bir aralığı değiştirmeye çalışıyor ve tamamlanamadı.<br> Operasyonu tamamlamak için tablodan Otomatik Filtreleri kaldırın.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Seçilen hücre aralığı için operasyon tamamlanamadı.<br> Tablo içinde yada dışında tekdüze veri aralığı seçin ve tekrar deneyin.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "The operation cannot be performed because the area contains filtered cells.<br>Please unhide the filtered elements and try again.", "SSE.Controllers.Main.errorBadImageUrl": "Resim URL'si yanlış", "SSE.Controllers.Main.errorCannotUngroup": "Grup çözülemiyor. Bir anahat başlatmak için ayrıntı satırlarını veya sütunlarını seçin ve gruplayın.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Bu komutu korumalı bir sayfada kullanamazsınız. Bu komutu kullanmak için sayfanın korumasını kaldırın.<br>Bir parola girmeniz istenebilir.", "SSE.Controllers.Main.errorChangeArray": "<PERSON>ir dizinin bir b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ğiştiremezsiniz.", "SSE.Controllers.Main.errorChangeFilteredRange": "<PERSON><PERSON>, çalışma sayfanızdaki filtrelenmiş bir aralığı değiştirecektir.<br>Bu görevi tamamlamak için lütfen Otomatik Filtreleri kaldırın.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Değiştirmeye çalıştığınız hücre veya grafik korumalı bir sayfada.<br>Değişiklik yapmak için sayfanın korumasını kaldırın. Bir şifre girmeniz istenebilir.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "<PERSON><PERSON><PERSON> bağlantısı kesildi. Döküman şu an düzenlenemez.", "SSE.Controllers.Main.errorConnectToServer": "Belge kaydedilemedi. Lütfen internet bağlantınızı kontrol edin veya yöneticiniz ile iletişime geçin. <br>'Tamam' tuşuna tıkladığınızda belgeyi indirebileceksiniz.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Bu komut çoklu seçimlerle kullanılamaz.<br><PERSON><PERSON> bir aralık seçin ve tekrar deneyin.", "SSE.Controllers.Main.errorCountArg": "<PERSON><PERSON><PERSON> formülde hata oluştu. <br> <PERSON><PERSON><PERSON><PERSON> değişken sayısı kullanıldı.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON><PERSON><PERSON> formülde hata oluştu. <br> Değişken sayısı aşıldı.", "SSE.Controllers.Main.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON> hata. <br><PERSON><PERSON> tabanı bağlantı hatası. Hata devam ederse lütfen destek ile iletişime geçin.", "SSE.Controllers.Main.errorDataEncrypted": "Şifreli <PERSON>ğişiklikler algılandı, çözülemiyor.", "SSE.Controllers.Main.errorDataRange": "Yanlış veri aralığı.", "SSE.Controllers.Main.errorDataValidate": "Girdiğiniz değer geçerli değil.<br>B<PERSON> kullanıcı bu hücreye girilebilecek kısıtlı değerlere sahip.", "SSE.Controllers.Main.errorDefaultMessage": "Hata kodu: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Kilitli bir hücre içeren bir sütunu silmeye çalışıyorsunuz. Çalışma sayfası korunurken kilitli hücreler silinemez.<br>Kilitli bir hücreyi silmek için sayfanın korumasını kaldırın. Bir şifre girmeniz istenebilir.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Kilitli bir hücre içeren bir satırı silmeye çalışıyorsunuz. Çalışma sayfası korunurken kilitli hücreler silinemez.<br>Kilitli bir hücreyi silmek için sayfanın korumasını kaldırın. Bir şifre girmeniz istenebilir.", "SSE.Controllers.Main.errorEditingDownloadas": "<PERSON><PERSON><PERSON><PERSON>ışırken hata meydana geldi. 'Farklı Kaydet' seçeneğini kullanarak dosyayı bilgisayarınıza yedekleyin.", "SSE.Controllers.Main.errorEditingSaveas": "Belgeyle <PERSON>ışma sırasında bir hata oluştu.<br><PERSON><PERSON><PERSON> <PERSON><PERSON> kopyasını bilgisayarınızın sabit diskine kaydetmek için 'Farklı kaydet...' seçeneğini kullanın.", "SSE.Controllers.Main.errorEditView": "Mevcut sayfa görünümü düzenlenemez ve bazıları düzenlenmekte olduğu için yenileri şu anda oluşturulamaz.", "SSE.Controllers.Main.errorEmailClient": "E-posta istemcisi bulunamadı.", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON>a parola k<PERSON>alıdır ve açılamaz.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON> hata.<br> <PERSON><PERSON><PERSON> istek hatası. <PERSON><PERSON> devam ederse lütfen destekle iletişime geçiniz.", "SSE.Controllers.Main.errorFileSizeExceed": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> i<PERSON>in belirlenen limiti aşıyor.<br>Ayr<PERSON>nt<PERSON>lar için lütfen Doküman Sunucusu yöneticinizle iletişime geçin.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON> hata.<br> <PERSON><PERSON><PERSON><PERSON> güvenlik anahtarı. Problem devam ederse lütfen destekle iletişime geçin.", "SSE.Controllers.Main.errorFillRange": "Seçilen hücre aralığı doldurulamıyor. <br>Birleştirilen tüm hücrelerin aynı boyutta olması gerekir.", "SSE.Controllers.Main.errorForceSave": "Dosya kaydedilirken bir hata oluştu. Dosyayı bilgisayarınızın sabit diskine kaydetmek için lütfen 'Farklı indir' seçeneğini kullanın veya daha sonra tekrar deneyin.", "SSE.Controllers.Main.errorFormulaName": "<PERSON><PERSON><PERSON> formülde hata oluştu. <br> <PERSON><PERSON><PERSON><PERSON> formül ismi kullanıld<PERSON>.", "SSE.Controllers.Main.errorFormulaParsing": "<PERSON><PERSON><PERSON> ögelerine ayrılırken dahili hata", "SSE.Controllers.Main.errorFrmlMaxLength": "Formülünüzün uzunluğu 8192 karakter sınırını aşıyor.<br>Lütfen düzenleyin ve tekrar deneyin.", "SSE.Controllers.Main.errorFrmlMaxReference": "Bu formülü giremezsiniz çünkü çok fazla değeri,<br>hücre referansı ve/veya adı vardır.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Formüllerdeki metin değ<PERSON>leri 255 karakterle sınırlıdır.<br>BİRLEŞTİR fonksiyonunu veya birleştirme operatörünü (&) kullanın.", "SSE.Controllers.Main.errorFrmlWrongReferences": "Fonksiyon olmayan bir iş tablosuna bağlı.<br>Lütfen veriyi kontrol edin ve tekrar deneyin.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Seçili hücre aralığı için işlem yapılamıyor.<br>İlk tablo satırı aynı satırda olan bir aralık seçilir<br>ve sonuç tablosu mevcut olanın üstüne biner.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Seçili hücre aralığı için işlem yapılamıyor.<br><PERSON><PERSON>er tabloları içeren bir aralık seçemezsiniz.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Dosya açılırken bir hata oluştu.<br>Dosya içeriği metin be<PERSON> (örn. docx) karşılık geliyor, ancak dosyanın uzantısı tutarsız: %1.", "SSE.Controllers.Main.errorInvalidRef": "Enter a correct name for the selection or a valid reference to go to.", "SSE.Controllers.Main.errorKeyEncrypt": "Bilinmeyen anahtar tanımlayıcı", "SSE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON>ın süresi doldu", "SSE.Controllers.Main.errorLabledColumnsPivot": "Bir pivot tablo oluşturmak için, et<PERSON><PERSON>i s<PERSON>tunlarla bir liste olarak düzenlenen verileri kullanın.", "SSE.Controllers.Main.errorLoadingFont": "Yazı tipleri yüklenmedi.<br>Lütfen Doküman Sunucusu yöneticinize başvurun.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Konum veya veri aralığı için referans geçerli değil.", "SSE.Controllers.Main.errorLockedAll": "The operation could not be done as the sheet has been locked by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "Pivot tablodaki veriyi değiştiremezsiniz.", "SSE.Controllers.Main.errorLockedWorksheetRename": "The sheet cannot be renamed at the moment as it is being renamed by another user", "SSE.Controllers.Main.errorMaxPoints": "Grafik başına serideki maksimum nokta sayısı 4096'dır.", "SSE.Controllers.Main.errorMoveRange": "Birleştirilmiş hücrenin parçası değiştirilemez", "SSE.Controllers.Main.errorMoveSlicerError": "Tablo dilimleyiciler bir çalışma kitabından diğerine kopyalanamaz.<br>Tablonun tamamını ve dilimleyicileri seçerek tekrar deneyin.", "SSE.Controllers.Main.errorMultiCellFormula": "Tablolarda çok hücreli dizi formüllerine izin verilmez.", "SSE.Controllers.Main.errorNoDataToParse": "Ayrıştırmak için hiçbir veri seçilmedi.", "SSE.Controllers.Main.errorOpenWarning": "Belgedeki formüllerin birinin uzunluğu<br>izin verilen karakter sayısını aştı ve kaldırıldı.", "SSE.Controllers.Main.errorOperandExpected": "İşlemci beklentili", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Verdiğiniz parola doğru değil.<br>CAPS LOCK anahtarının kapalı olduğunu doğrulayın ve doğru büyük/küçük harf kullandığınızdan emin olun.", "SSE.Controllers.Main.errorPasteMaxRange": "The copy and paste area does not match.<br>Please select an area with the same size or click the first cell in a row to paste the copied cells.", "SSE.Controllers.Main.errorPasteMultiSelect": "<PERSON><PERSON> <PERSON><PERSON>, çoklu aralık seçiminde yapılamaz.<br><PERSON>k bir aralık seçin ve tekrar deneyin.", "SSE.Controllers.Main.errorPasteSlicerError": "Tablo dilimleyiciler bir çalışma kitabından diğerine kopyalanamaz.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON><PERSON> se<PERSON><PERSON> g<PERSON>mıyor.", "SSE.Controllers.Main.errorPivotOverlap": "Bir pivot tablo raporu, bir tab<PERSON>la çakışamaz.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "<PERSON><PERSON><PERSON>lo <PERSON>, temel alınan veriler olmadan kaydedildi.<br><PERSON><PERSON><PERSON> güncellemek için 'Yenile' düğ<PERSON><PERSON> kullanın.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Maalesef 1500 sayfadan fazla yazdırmak mevcut program sürümü ile mümkün değildir.<br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, önümüzdeki güncellemeler ile kaldırılacaktır.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON> başarıs<PERSON>z oldu", "SSE.Controllers.Main.errorServerVersion": "Editör versiyonu gü<PERSON>di. <PERSON><PERSON>ğişiklikler uygulanacaktır.", "SSE.Controllers.Main.errorSessionAbsolute": "Belge düzenleme oturumu sona erdi. Lütfen sayfayı yeniden yükleyin.", "SSE.Controllers.Main.errorSessionIdle": "Belge oldukça uzun süredir düzenlenmedi. Lütfen sayfayı yeniden yükleyin.", "SSE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON> bağlantısı yarıda kesildi. Lütfen sayfayı yeniden yükleyin.", "SSE.Controllers.Main.errorSetPassword": "<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Hücrelerin tümü aynı sütun veya satırda olmadığı için konum başvurusu geçerli değil.<br>Tümü tek bir sütun veya satırda olan hücreleri seçin.", "SSE.Controllers.Main.errorStockChart": "Yanlış dizi sırası. Stok grafiği oluşturma için tablodaki verileri şu sırada yerleştirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> fiyatı, maks<PERSON><PERSON> fiyat, minimum fiyat, kapan<PERSON>ş fiyatı. ", "SSE.Controllers.Main.errorToken": "Belge güvenlik belirteci doğru şekilde oluşturulmamış.<br>Lütfen Belge Sunucu yöneticinize başvurun.", "SSE.Controllers.Main.errorTokenExpire": "Belge güvenlik belirteci süresi doldu.<br>Lütfen Belge Sunucusu yöneticinize başvurun.", "SSE.Controllers.Main.errorUnexpectedGuid": "<PERSON><PERSON> hata.<br> Beklenmeyen GUID. Hata devam ederse lütfen destekle iletişime geçiniz.", "SSE.Controllers.Main.errorUpdateVersion": "Dosya versi<PERSON>. Sayfa yenilenecektir.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "İnternet bağlantısı geri yüklendi ve dosya sürümü değiştirildi.<br>Çalışmaya devam etmeden önce, hiçbir şeyin kaybolmadığından emin olmak için dosyayı indirmeniz veya içeriğini kopyalamanız ve ardından bu sayfayı yeniden yüklemeniz gerekir.", "SSE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "SSE.Controllers.Main.errorUsersExceed": "Fiyat planının izin verdiği kullanıcı sayısı aşıldı", "SSE.Controllers.Main.errorViewerDisconnect": "Bağlantı kesildi. Belgeyi yine de görüntüleyebilirsiniz,<br>an<PERSON><PERSON> bağlantı yeniden kurulana ve sayfa yeniden yüklenene kadar indiremez veya yazdıramazsınız.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON><PERSON> formü<PERSON> hata oluştu. <br> <PERSON><PERSON><PERSON><PERSON> sayıda köşeli parantez kullanıldı.", "SSE.Controllers.Main.errorWrongOperator": "<PERSON><PERSON><PERSON> formü<PERSON> hata oluştu. <br> Yanlış operatör kullanıldı.", "SSE.Controllers.Main.errorWrongPassword": "Verdiğiniz şifre doğru <PERSON>.", "SSE.Controllers.Main.errRemDuplicates": "<PERSON><PERSON><PERSON> değerler bulundu ve silindi: {0}, <PERSON><PERSON><PERSON> kaldı: {1}.", "SSE.Controllers.Main.leavePageText": "Spreadsheet'te kaydedilmemiş değişiklikler var. Kaydetmek için 'Bu Sayfada Kal'a daha sonra da 'Kaydet'e tıklayınız.Kaydedilmemiş tüm değişiklikleri göz ardı etmek için 'Bu Sayfadan Ayrıl'a tıklayın.", "SSE.Controllers.Main.leavePageTextOnClose": "Bu e-tablodaki kaydedilmemiş tüm değişiklikler kaybolacak.<br> Bunları kaydetmek için \"İptal\"i ve ardından \"Kaydet\"i tıklayın. Kaydedilmemiş tüm değişiklikleri atmak için \"Tamam\" ı tıklayın.", "SSE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON>...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON>", "SSE.Controllers.Main.loadFontTextText": "<PERSON><PERSON>...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON>", "SSE.Controllers.Main.loadImagesTextText": "Resimler yükleniyor...", "SSE.Controllers.Main.loadImagesTitleText": "Resimler yükleniyor", "SSE.Controllers.Main.loadImageTextText": "<PERSON>si<PERSON>...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "E-tablo yükle<PERSON>yor", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON>a a<PERSON>ılırken bir hata oluş<PERSON>", "SSE.Controllers.Main.openTextText": "E-tablo açılıyor...", "SSE.Controllers.Main.openTitleText": "E-Tablo Açılıyor", "SSE.Controllers.Main.pastInMergeAreaError": "Birleştirilmiş hücrenin parçası değiştirilemez", "SSE.Controllers.Main.printTextText": "Spreadsheet yazdırılıyor...", "SSE.Controllers.Main.printTitleText": "Spreadsheet Yazdırılıyor", "SSE.Controllers.Main.reloadButtonText": "Sayfayı Yenile", "SSE.Controllers.Main.requestEditFailedMessageText": "<PERSON>u bu dök<PERSON><PERSON> biri tarafından düzenleniyor. Lütfen daha sonra tekrar deneyin.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> ka<PERSON> bir hata <PERSON>", "SSE.Controllers.Main.saveErrorTextDesktop": "Bu dosya kaydedilemez veya oluşturulamaz.<br><PERSON><PERSON><PERSON> nedenler şunlardır: <br>1. <PERSON><PERSON><PERSON> salt okunurdur. <br>2. <PERSON><PERSON><PERSON> kullanıcılar tarafından düzenleniyor. <br>3. Disk dolu veya bozuk. ", "SSE.Controllers.Main.saveTextText": "Spreadsheet kaydediliyor...", "SSE.Controllers.Main.saveTitleText": "E-tablo <PERSON>", "SSE.Controllers.Main.scriptLoadError": "Bağlantı çok yavaş, bileş<PERSON><PERSON>in bazıları yüklenemedi. Lütfen sayfayı yenileyin.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textBuyNow": "Websitesini ziyaret edin", "SSE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON>iklikler kaydedildi", "SSE.Controllers.Main.textClose": "Ka<PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "<PERSON>cu kapamak için tıklayın", "SSE.Controllers.Main.textConfirm": "Konfirmasyon", "SSE.Controllers.Main.textContactUs": "Satış departmanı ile iletişime geçin", "SSE.Controllers.Main.textConvertEquation": "<PERSON><PERSON> den<PERSON><PERSON>, denkle<PERSON> düzenleyicinin artık desteklenmeyen eski bir sürümüyle oluşturulmuştur. Düzenlemek için denklemi Office Math ML formatına dönüştürün.<br>Şimdi dönüştürülsün mü?", "SSE.Controllers.Main.textCustomLoader": "Lütfen lisans şartlarına göre yükleyiciyi değiştirme hakkınız olmadığını unutmayın.<br>Fiyat teklifi almak için lütfen Satış Departmanımızla iletişime geçin.", "SSE.Controllers.Main.textDisconnect": "bağlantı kesildi", "SSE.Controllers.Main.textFillOtherRows": "<PERSON><PERSON><PERSON> satı<PERSON>arı doldur", "SSE.Controllers.Main.textFormulaFilledAllRows": "<PERSON><PERSON>lle doldurulmuş {0} satırda veri bulunuyor. <PERSON><PERSON><PERSON> boş satırların doldurulması birkaç dakika sürebilir.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formül ilk {0} satırı doldurdu. <PERSON><PERSON>er boş satırların doldurulması birkaç dakika sürebilir.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "<PERSON><PERSON><PERSON>, yalnızca ilk {0} satırı bellek kaydetme nedenine göre doldurdu. Bu sayfadaki diğer satırlarda veri yok.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "Dosya otomatik makrolar içeriyor.<br>Makroları çalıştırmak istiyor musunuz?", "SSE.Controllers.Main.textLearnMore": "<PERSON>ha fazlası", "SSE.Controllers.Main.textLoadingDocument": "E-tablo yükle<PERSON>yor", "SSE.Controllers.Main.textLongName": "128 karakterden kısa bir ad girin.", "SSE.Controllers.Main.textNeedSynchronize": "G<PERSON><PERSON><PERSON><PERSON> var", "SSE.Controllers.Main.textNo": "Hay<PERSON><PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "Lisans limitine ulaşıldı.", "SSE.Controllers.Main.textPaidFeature": "Ücretli Ö<PERSON>lik", "SSE.Controllers.Main.textPleaseWait": "Operasyon beklenenden daha çok vakit alabilir. Lütfen bekleyin...", "SSE.Controllers.Main.textReconnect": "Yeniden bağlanıldı", "SSE.Controllers.Main.textRemember": "Seçimimi tüm dosyalar için hatırla", "SSE.Controllers.Main.textRenameError": "Kullanıcı adı boş bırakılmamalıdır.", "SSE.Controllers.Main.textRenameLabel": "İşbirliği için kullanılacak bir ad girin", "SSE.Controllers.Main.textShape": "Şekil", "SSE.Controllers.Main.textStrict": "Strict mode", "SSE.Controllers.Main.textText": "<PERSON><PERSON>", "SSE.Controllers.Main.textTryUndoRedo": "Geri al/Yinele fonksiyonları hızlı ortak çalışma modunda devre dışı kalır.<br>'Katı mod' tuşuna tıklayarak Katı ortak düzenleme moduna geçebilir ve diğer kullanıcıların müdehalesi olmadan, yalnızca siz belgeyi kaydettikten sonra değişiklik yapılmasını sağlayabilirsiniz. Ortak çalışma moduna tekrar dönmek için Gelişmiş ayarları kullanabilirsiniz.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Hızlı ortak düzenleme modunda geri al/yinele fonksiyonları devre dışıdır.", "SSE.Controllers.Main.textYes": "<PERSON><PERSON>", "SSE.Controllers.Main.titleLicenseExp": "Lisans süresi doldu", "SSE.Controllers.Main.titleServerVersion": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(Tümü)", "SSE.Controllers.Main.txtArt": "Your text here", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON>", "SSE.Controllers.Main.txtBlank": "(boşluk)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 / %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtColLbls": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtConfidential": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Diagram Başlığı", "SSE.Controllers.Main.txtEditingMode": "<PERSON><PERSON><PERSON>leme modunu belirle...", "SSE.Controllers.Main.txtErrorLoadHistory": "Geçmiş yüklenemedi", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "<PERSON><PERSON>", "SSE.Controllers.Main.txtGroup": "Grup", "SSE.Controllers.Main.txtHours": "Saat", "SSE.Controllers.Main.txtLines": "Satırlar", "SSE.Controllers.Main.txtMath": "Matematik", "SSE.Controllers.Main.txtMinutes": "Dakika", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Çoklu Seçim", "SSE.Controllers.Main.txtOr": "%1 ya da %2", "SSE.Controllers.Main.txtPage": "Say<PERSON>", "SSE.Controllers.Main.txtPageOf": "Sayfa %1 / %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "Tarafından hazırlandı", "SSE.Controllers.Main.txtPrintArea": "Alanı yazdır", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtQuarters": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "Dikdörtgen<PERSON>", "SSE.Controllers.Main.txtRow": "Satır", "SSE.Controllers.Main.txtRowLbls": "Satır <PERSON>", "SSE.Controllers.Main.txtSeconds": "saniye", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Satır Belirtme Çizgisi 1 (Kenarlık ve Vurgu Çubuğu)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Satır Belirtme Çizgisi 2 (Kenarlık ve Vurgu Çubuğu)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Satır Belirtme Çizgisi 3 (Kenarlık ve Vurgu Çubuğu)", "SSE.Controllers.Main.txtShape_accentCallout1": "Satır Belirtme Çizgisi 1 (Vurgu Çubuğu)", "SSE.Controllers.Main.txtShape_accentCallout2": "Satır Belirtme Çizgisi 2 (Vurgu Çubuğu)", "SSE.Controllers.Main.txtShape_accentCallout3": "Satır Belirtme Çizgisi 3 (Vurgu Çubuğu)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON> <PERSON>ya Ö<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Başlangıç <PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>ğ<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Belge Düğmesi", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Bitir <PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "İleri veya İleri Düğmesi", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Film Düğmesi", "SSE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_arc": "yay", "SSE.Controllers.Main.txtShape_bentArrow": "Bükülmüş Ok", "SSE.Controllers.Main.txtShape_bentConnector5": "Dirsek Konnektörü", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Dirsek Ok Konnektörü", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Dirsek Çift Ok Konnektörü", "SSE.Controllers.Main.txtShape_bentUpArrow": "Dışa Bükülmüş Ok", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Blok yay", "SSE.Controllers.Main.txtShape_borderCallout1": "Satır Belirtme Çizgisi 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Satır Belirtme Çizgisi 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Satır Belirtme Çizgisi 3", "SSE.Controllers.Main.txtShape_bracePair": "Çift Braket", "SSE.Controllers.Main.txtShape_callout1": "Satır Belirtme Çizgisi 1 (Kenarlık Yok)", "SSE.Controllers.Main.txtShape_callout2": "Satır Belirtme Çizgisi 2 (Kenarlık Yok)", "SSE.Controllers.Main.txtShape_callout3": "Satır Belirtme Çizgisi 3 (Kenarlık Yok)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "şerit", "SSE.Controllers.Main.txtShape_chord": "akor", "SSE.Controllers.Main.txtShape_circularArrow": "da<PERSON><PERSON> ok", "SSE.Controllers.Main.txtShape_cloud": "Bulut", "SSE.Controllers.Main.txtShape_cloudCallout": "Bulut <PERSON>irt<PERSON>", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Kavisli Bağlayıcı ", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Kavisli Ok Bağlayıcı ", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Kavisli Çift Ok Bağlayıcı", "SSE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Kavisli Yukarı OK", "SSE.Controllers.Main.txtShape_decagon": "Dekagon", "SSE.Controllers.Main.txtShape_diagStripe": "Çapraz Çizgi", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Onikigen", "SSE.Controllers.Main.txtShape_donut": "Hal<PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "Çift Dalga", "SSE.Controllers.Main.txtShape_downArrow": "Aşağı <PERSON>", "SSE.Controllers.Main.txtShape_downArrowCallout": "Aşağı Ok Belirtme Çizgisi", "SSE.Controllers.Main.txtShape_ellipse": "Elips", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Kavisli Aşağı Ribbon", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Ka<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Akış çizelgesi: Alternatif <PERSON>üreç", "SSE.Controllers.Main.txtShape_flowChartCollate": "Akış çizelgesi: <PERSON><PERSON>la", "SSE.Controllers.Main.txtShape_flowChartConnector": "Akış çizelgesi: Bağlayıcı", "SSE.Controllers.Main.txtShape_flowChartDecision": "Akış çizelgesi: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDelay": "Akış çizelgesi: Gecikme", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Akış çizelgesi: Ekran", "SSE.Controllers.Main.txtShape_flowChartDocument": "Akış çizelgesi: Belge", "SSE.Controllers.Main.txtShape_flowChartExtract": "Akış çizelgesi: Özü", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Akış çizelgesi: Veri", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Akış çizelgesi: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Akış çizelgesi: Manyetik Disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Akış çizelgesi: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Akış çizelgesi: Sıralı Erişimli <PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Akış çizelgesi: <PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Akış çizelgesi: <PERSON>", "SSE.Controllers.Main.txtShape_flowChartMerge": "Akış çizelgesi: Birleştirme", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Akış çizelgesi: Çoklu belge", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Akış çizelgesi: Sayfa Dışı Bağlayıcı", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Akış çizelgesi: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartOr": "Akış şeması: Veya", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Akış çizelgesi: Önceden Tanımlanmış Süreç", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Akış şeması: Hazırlık", "SSE.Controllers.Main.txtShape_flowChartProcess": "Akış çizelgesi: S<PERSON>reç", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Akış çizelgesi: Kart", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Akış çizelgesi: Delikli Bant", "SSE.Controllers.Main.txtShape_flowChartSort": "Akış çizelgesi: Sıralama", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Akış çizelgesi: Toplama Kavşağı", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Akış çizelgesi: Sonlandırıcı", "SSE.Controllers.Main.txtShape_foldedCorner": "Katlanmış Köşe", "SSE.Controllers.Main.txtShape_frame": "Ç<PERSON><PERSON>eve", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Yedigen", "SSE.Controllers.Main.txtShape_hexagon": "Altıgen", "SSE.Controllers.Main.txtShape_homePlate": "Beşgen", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_irregularSeal1": "Patlama 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Patlama 2", "SSE.Controllers.Main.txtShape_leftArrow": "Sol Ok", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Sol Ok Belirtme <PERSON>i", "SSE.Controllers.Main.txtShape_leftBrace": "Sol Ayraç", "SSE.Controllers.Main.txtShape_leftBracket": "Sol Köşeli Ayraç", "SSE.Controllers.Main.txtShape_leftRightArrow": "Sol Sağ Ok", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Sol Sağ Ok Belirtme Çizgisi ", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Sol Sağ Yukarı Ok", "SSE.Controllers.Main.txtShape_leftUpArrow": "Sol Yukarı Ok", "SSE.Controllers.Main.txtShape_lightningBolt": "Ş<PERSON>şek", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Ok", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Çift Ok", "SSE.Controllers.Main.txtShape_mathDivide": "Bölüm", "SSE.Controllers.Main.txtShape_mathEqual": "Eş<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Çoğalt", "SSE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathPlus": "Artı", "SSE.Controllers.Main.txtShape_moon": "Ay", "SSE.Controllers.Main.txtShape_noSmoking": "<PERSON><PERSON><PERSON> \"Yok\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Çentikli Sağ Ok ", "SSE.Controllers.Main.txtShape_octagon": "Sekizgen", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelkenar", "SSE.Controllers.Main.txtShape_pentagon": "Beşgen", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "İmzala", "SSE.Controllers.Main.txtShape_plus": "Artı", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline2": "Serbest çalışma", "SSE.Controllers.Main.txtShape_quadArrow": "Dörtlü ok", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Dörtlü Ok Belirtme Çizgisi", "SSE.Controllers.Main.txtShape_rect": "Dikdörtgen", "SSE.Controllers.Main.txtShape_ribbon": "Aşağı Ribbon", "SSE.Controllers.Main.txtShape_ribbon2": "Yukarı Ribbon", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Sağ Ok Belirtme Çizgisi ", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBracket": "Sağ Köşeli Ayraç", "SSE.Controllers.Main.txtShape_round1Rect": "Yuvarlak Tek Köşe Dikdörtgen", "SSE.Controllers.Main.txtShape_round2DiagRect": "Yuvarlak Çapraz Köşe Dikdörtgen", "SSE.Controllers.Main.txtShape_round2SameRect": "Yuvarlak Aynı Kenar Köşe Dikdörtgen", "SSE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON> Köşe Dikdörtgen", "SSE.Controllers.Main.txtShape_rtTriangle": "Sağ Üçgen", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Tek Köşeli Dikdörtgen Kes", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Çapraz Köşe Dikdörtgen Kes", "SSE.Controllers.Main.txtShape_snip2SameRect": "Aynı Yan Köşe Dikdörtgeni Kes", "SSE.Controllers.Main.txtShape_snipRoundRect": "Yuvarlak Tek Köşe Dikdörtgen Kes", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "10 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star12": "12 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star16": "16 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star24": "24 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star32": "32 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star4": "4 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star5": "5 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star6": "6 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star7": "7 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_star8": "8 Köşeli Yıldız", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Çizgili Sağ Ok", "SSE.Controllers.Main.txtShape_sun": "<PERSON><PERSON><PERSON>ş", "SSE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Üçgen", "SSE.Controllers.Main.txtShape_upArrow": "Yu<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "Yukarı Ok Belirtme Çizgisi", "SSE.Controllers.Main.txtShape_upDownArrow": "Yukarı Aşağı Ok", "SSE.Controllers.Main.txtShape_uturnArrow": "U-Dön<PERSON>ş <PERSON>", "SSE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wave": "Dalga", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Belirtme <PERSON>i", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Dikdörtgen Belirtme Çizgisi", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON><PERSON> Dikdörtgen Belirtme ", "SSE.Controllers.Main.txtStarsRibbons": "Yıldızlar & Kurdeleler", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Hücreyi İşaretle", "SSE.Controllers.Main.txtStyle_Comma": "Virg<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Para Birimi", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Açıklayıcı Metin", "SSE.Controllers.Main.txtStyle_Good": "İyi", "SSE.Controllers.Main.txtStyle_Heading_1": "Başlık 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Başlık 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Başlık 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Başlık 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Bağlı Hücre", "SSE.Controllers.Main.txtStyle_Neutral": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "Not", "SSE.Controllers.Main.txtStyle_Output": "Çıktı", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON><PERSON>z<PERSON>", "SSE.Controllers.Main.txtStyle_Title": "Başlık", "SSE.Controllers.Main.txtStyle_Total": "Toplam", "SSE.Controllers.Main.txtStyle_Warning_Text": "Uyarı Metni", "SSE.Controllers.Main.txtTab": "Sekme", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Zaman", "SSE.Controllers.Main.txtUnlock": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Aralık Kilidini Aç", "SSE.Controllers.Main.txtUnlockRangeDescription": "Bu aralığı değiştirmek için şifreyi girin:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Değiştirmeye çalıştığınız bir aralık parola korumalıdır.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtXAxis": "X Ekseni", "SSE.Controllers.Main.txtYAxis": "Y Ekseni", "SSE.Controllers.Main.txtYears": "yıl", "SSE.Controllers.Main.unknownErrorText": "Bilinmeyen hata.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Tarayıcınız desteklenmiyor.", "SSE.Controllers.Main.uploadDocExtMessage": "Bilinmeyen belge biçimi.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Yüklenen belge yok.", "SSE.Controllers.Main.uploadDocSizeMessage": "<PERSON><PERSON><PERSON><PERSON> belge boyutu sınırı aşıldı.", "SSE.Controllers.Main.uploadImageExtMessage": "Bilinmeyen resim formatı", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "Görüntü çok büyük. Ma<PERSON><PERSON><PERSON> boyut 25 MB'dir.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON>si<PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "SSE.Controllers.Main.warnBrowserIE9": "Uygulama IE9'da düşük yeteneklere sahip. IE10 yada daha yükseğini kullanınız", "SSE.Controllers.Main.warnBrowserZoom": "Tarayıcınızın mevcut yakınlaştırma ayarı tam olarak desteklenmiyor. Ctrl+0'a basarak varsayılan yakınlaştırmayı sıfırlayınız.", "SSE.Controllers.Main.warnLicenseExceeded": "%1 düzenleyiciye eşzamanlı bağlantı sınırına ulaştınız. Bu doküman yalnızca görüntüleme için açılacaktır.<br>Daha fazla bilgi için yöneticinizle iletişime geçin.", "SSE.Controllers.Main.warnLicenseExp": "Lisansınızın süresi doldu.<br>Lütfen lisansınızı güncelleyin ve sayfayı yenileyin.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Lisansın süresi doldu.<br>Belge düzenleme işlevine erişiminiz yok.<br>Lütfen yöneticinizle iletişime geçin.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Lisansın yenilenmesi gerekiyor.<br>Belge düzenleme işlevine sınırlı erişiminiz var.<br>Tam erişim için lütfen yöneticinizle iletişime geçin.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "%1 düzenleyici için kullanıcı sınırına ulaştınız. Daha fazla bilgi edinmek için yöneticinizle iletişime geçin.", "SSE.Controllers.Main.warnNoLicense": "Düzenleyiciler %1 eşzamanlı bağlantı sınırına ulaştı. Bu belge yalnızca görüntüleme için açılacaktır.<br>Kişisel yükseltme koşulları için %1 satış ekibiyle iletişime geçin.", "SSE.Controllers.Main.warnNoLicenseUsers": "%1 düzenleyici için kullanıcı sınırına ulaştınız. Kişisel yükseltme koşulları için %1 satış ekibiyle iletişime geçin.", "SSE.Controllers.Main.warnProcessRightsChange": "Dosyayı düzenleme hakkınız reddedildi", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON>", "SSE.Controllers.Print.textFirstRow": "İlk sıra", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Controllers.Print.textNoRepeat": "tekrar etme", "SSE.Controllers.Print.textRepeat": "Tekra<PERSON>...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON>", "SSE.Controllers.Print.warnCheckMargings": "Kenar boşlukları yanlış", "SSE.Controllers.Statusbar.errorLastSheet": "Not defterinde en az bir görünür çalışma tablosu olmalıdır.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Çalışma tablosu silinemiyor.", "SSE.Controllers.Statusbar.strSheet": "Say<PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Bağlantı kesildi</b><br>Bağlanmayı deneyin. Lütfen bağlantı ayarlarını kontrol edin.", "SSE.Controllers.Statusbar.textSheetViewTip": "Sayfa Görünümü modundasınız. Filtreleri ve sıralamayı yalnızca siz ve hâlâ bu görünümde olanlar görebilir.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Sayfa Görünümü modundasınız. Filtreleri yalnızca siz ve hâlâ bu görünümde olanlar görebilir.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Seçilen çalışma sayfaları veri içerebilir. <PERSON>am etmek istediğinizden emin misiniz?", "SSE.Controllers.Statusbar.zoomText": "Ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rma {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the device fonts, the saved font will be used when it is available.<br>Do you want to continue?", "SSE.Controllers.Toolbar.errorComboSeries": "Bir kombinasyon grafiği oluşturmak için en az iki veri serisi seçin.", "SSE.Controllers.Toolbar.errorMaxRows": "HATA! Her grafik için maksimum veri serileri sayısı 255'tir", "SSE.Controllers.Toolbar.errorStockChart": "Yanlış dizi sırası. Stok grafiği oluşturma için tablodaki verileri şu sırada yerleştirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> fiyatı, maks<PERSON><PERSON> fiyat, minimum fiyat, kapan<PERSON>ş fiyatı. ", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Yönlü", "SSE.Controllers.Toolbar.textFontSizeErr": "Girilen değer yanlış. <br> Lütfen 1 ile 409 arasında sayısal değer giriniz.", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Fonksiyonlar", "SSE.Controllers.Toolbar.textIndicator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textInsert": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Büyük Operatörler", "SSE.Controllers.Toolbar.textLimitAndLog": "<PERSON>it ve Logaritma", "SSE.Controllers.Toolbar.textLongOperation": "Uzun işlem", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operatörler", "SSE.Controllers.Toolbar.textPivot": "Pivot Tablo", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Şekiller", "SSE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Yukarı Sağ-Sol Ok", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Sola Yukarı Ok", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Bar": "Çubuk grafik", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Altçizgi", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Üstçizgi", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> (Yer Tutu<PERSON>lu)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON> (örnek)", "SSE.Controllers.Toolbar.txtAccent_Check": "İşaretle", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Altparantez", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Üstparantez", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektör A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "Çizgili ABC", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Üstçizgili", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Üç Nokta", "SSE.Controllers.Toolbar.txtAccent_DDot": "İki Nokta", "SSE.Controllers.Toolbar.txtAccent_Dot": "Nokta", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Çift Üst Çizgi", "SSE.Controllers.Toolbar.txtAccent_Grave": "Yavaş", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Aşağı Gruplama Karakteri", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Yukarı Gruplama Karakteri", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Sola Yukarı Süslü Ok", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Sağa Yukarı Süslü Ok", "SSE.Controllers.Toolbar.txtAccent_Hat": "Şapka", "SSE.Controllers.Toolbar.txtAccent_Smile": "K<PERSON>sa", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Ayırıcılı Köşeli Ayraç", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Ayırıcılı Köşeli Ayraç", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Ayırıcılı Köşeli Ayraç", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON> (İki <PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON> (Üç <PERSON>ullu)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binom <PERSON>sayı", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binom <PERSON>sayı", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Ayırıcılı Köşeli Ayraç", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Tek Parantez", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Tek Parantez", "SSE.Controllers.Toolbar.txtDeleteCells": "Hücreleri Sil", "SSE.Controllers.Toolbar.txtExpand": "Genişlet ve sırala", "SSE.Controllers.Toolbar.txtExpandSort": "Seç<PERSON>in yanındaki veri sıralanmayacaktır. Seçimi genişleterek bitişik verilerin dahil edilmesini istiyor musunuz veya mevcut seçim ile devam etmek mi istiyorsunuz?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Diferansiyel", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Diferansiyel", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Diferansiyel", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Diferansiyel", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Lineer <PERSON>", "SSE.Controllers.Toolbar.txtFractionPi_2": "2 Üzeri Pi", "SSE.Controllers.Toolbar.txtFractionSmall": "Küçük Kesir", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperbolik Ters Kosinüs Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperbolik Ters Kotanjant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Ters Kosekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperbolik Ters Kosekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Ters Sekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperbolik Ters Sekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperbolik Ters Sinüs Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON> Tan<PERSON>t <PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperbolik Ters Tanjant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hiperbolik Kosinüs Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hiperbolik Kotanjant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Csc": "Kosekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hiperbolik Kosekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin teta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sec": "Sekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hiperbolik Sekant Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hiperbolik Sinüs Fonksiyonu", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hiperbolik Tanjant Fonksiyonu", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral": "İntegral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferansiyel teta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferansiyel x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferansiyel y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "İntegral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Çift İntegral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Çift İntegral", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Çift İntegral", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Yüzey İ<PERSON>grali", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Yüzey İ<PERSON>grali", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Yüzey İ<PERSON>grali", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralSubSup": "İntegral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Üçlü İntegral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Üçlü İntegral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Üçlü İntegral", "SSE.Controllers.Toolbar.txtInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wedge", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ortak İş", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ortak İş", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ortak İş", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ortak İş", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ortak İş", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Toplama", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON> logari<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarit<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarit<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Seçiminizin yanında veriler bulunur, ancak bu hücreleri değiştirmek için yeterli izniniz yok.<br>Geçerli seçimle devam etmek istiyor musunuz?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Boş Matris", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diyagonal Noktalar", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Biri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 <PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 <PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 <PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Aşağı Sağ-Sol Ok", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Yukarı Sağ-Sol Ok", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Sola Aşağı Ok", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Sola Yukarı Ok", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Sağa Aşağı Ok", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta Miktarları", "SSE.Controllers.Toolbar.txtOperator_Definition": "Tanımla eşittir", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Eşittir", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Aşağı Sağ-Sol Ok", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Yukarı Sağ-Sol Ok", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Sola Aşağı Ok", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Sola Yukarı Ok", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Sağa Aşağı Ok", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON>şit eşittir", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Artı Eşittir", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Ölçüm", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON>m<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON>m<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON>m<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON>m<PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Altsimge", "SSE.Controllers.Toolbar.txtScriptSubSup": "Altsimge-Üstsimge", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Sol altsimge-üstsimge", "SSE.Controllers.Toolbar.txtScriptSup": "Üstsimge", "SSE.Controllers.Toolbar.txtSorting": "Sıralama", "SSE.Controllers.Toolbar.txtSortSelected": "Seçili olanları sırala", "SSE.Controllers.Toolbar.txtSymbol_about": "Yaklaşık", "SSE.Controllers.Toolbar.txtSymbol_additional": "Tamamlayıcı", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Yaklaşık <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ast": "Asterisk Operatörü", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Madde Operatörü", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Yaklaşık", "SSE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Aşağı Sağ Diyagonal Elips", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Aşağı <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Eş<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Eş<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Mevcut", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktöriyel", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "Büyük Eşittir", "SSE.Controllers.Toolbar.txtSymbol_gg": "Çok büyüktür", "SSE.Controllers.Toolbar.txtSymbol_greater": "Büyüktür", "SSE.Controllers.Toolbar.txtSymbol_in": "Elemanı", "SSE.Controllers.Toolbar.txtSymbol_inc": "Orantısal", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Sonsuz", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Sol Ok", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Sol-sağ ok", "SSE.Controllers.Toolbar.txtSymbol_leq": "Küçük eşittir", "SSE.Controllers.Toolbar.txtSymbol_less": "Küçüktür", "SSE.Controllers.Toolbar.txtSymbol_ll": "Çok Küçüktür", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Eksi Artı", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ni": "Üye İçerir", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Parçalı Diferansiyel", "SSE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON>z<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Artı", "SSE.Controllers.Toolbar.txtSymbol_pm": "Artı Eksi", "SSE.Controllers.Toolbar.txtSymbol_propto": "Orantısal", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kök", "SSE.Controllers.Toolbar.txtSymbol_qed": "İspat Sonu", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Yukarı Sağ Diyagonal Elips", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Kök İşareti", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> ne<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_theta": "Teta", "SSE.Controllers.Toolbar.txtSymbol_times": "Çarpma İşareti", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Yu<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon varyantı", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi <PERSON>ı", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi <PERSON>ı", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho <PERSON>antı", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma varyantı", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Teta <PERSON>ı", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Tablo Tarzı Açık", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.warnLongOperation": "Yapmak istediğiniz işlemin tamamlanması uzun sürebilir.<br><PERSON><PERSON> etmek istediğinize emin misiniz?", "SSE.Controllers.Toolbar.warnMergeLostData": "Sadece üst sol hücredeki veri birleştirilmiş hücrede kalacaktır. <br><PERSON><PERSON> et<PERSON>k istediğinizden emin misiniz?", "SSE.Controllers.Viewport.textFreezePanes": "Parçaları Dondur", "SSE.Controllers.Viewport.textFreezePanesShadow": "<PERSON><PERSON><PERSON> Bölmeler Gölgesini Göster", "SSE.Controllers.Viewport.textHideFBar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textHideGridlines": "Kılavuz Çizgileri Gizle", "SSE.Controllers.Viewport.textHideHeadings": "Başlıkları Gizle", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "ondalık ayırıcı", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Binlik ayırıcı", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Sayısal verileri tanımak için kullanılan ayarlar", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Gelişmiş <PERSON>", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(hi<PERSON><PERSON><PERSON>)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON> Filtre", "SSE.Views.AutoFilterDialog.textAddSelection": "<PERSON><PERSON><PERSON><PERSON> filtreye ekle", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Boşluklar}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Tüm Arama Sonuçlarını Seç", "SSE.Views.AutoFilterDialog.textWarning": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAboveAve": "Ortalama üstü", "SSE.Views.AutoFilterDialog.txtBegins": "Başlangıç...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Ortalama altı", "SSE.Views.AutoFilterDialog.txtBetween": "Arasında...", "SSE.Views.AutoFilterDialog.txtClear": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "İçerir...", "SSE.Views.AutoFilterDialog.txtEmpty": "Hücre filtresi gir", "SSE.Views.AutoFilterDialog.txtEnds": "Biter...", "SSE.Views.AutoFilterDialog.txtEquals": "Eşittir...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "<PERSON><PERSON><PERSON> rengine göre filtrele", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "<PERSON><PERSON><PERSON> rengine göre filtrele", "SSE.Views.AutoFilterDialog.txtGreater": "Büyüktür...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Büyük eşittir...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Etiket filtresi", "SSE.Views.AutoFilterDialog.txtLess": "Küçüktür...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Küçük eşittir...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Başlamaz...", "SSE.Views.AutoFilterDialog.txtNotBetween": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>...", "SSE.Views.AutoFilterDialog.txtNotContains": "İçermez...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Bitmez...", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Sayı filtresi", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON><PERSON>den uygula", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON>cre rengine gö<PERSON> s<PERSON>", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON><PERSON> rengine göre sı<PERSON>", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Büyükten Küçüğe Sırala", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Küçükten Büyüğe Sırala", "SSE.Views.AutoFilterDialog.txtSortOption": "Daha fazla sıralama seçeneği...", "SSE.Views.AutoFilterDialog.txtTextFilter": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTitle": "Filtre", "SSE.Views.AutoFilterDialog.txtTop10": "İlk 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "Değer filtresi uygulamak için <PERSON> alanında en az bir alana ihtiyacınız vardır.", "SSE.Views.AutoFilterDialog.warnNoSelected": "En az bir değer seçmelisiniz", "SSE.Views.CellEditor.textManager": "Manager", "SSE.Views.CellEditor.tipFormula": "Fonksiyon <PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "HATA! Her grafik için maksimum veri serileri sayısı 255'tir", "SSE.Views.CellRangeDialog.errorStockChart": "Yanlış dizi sırası. Stok grafiği oluşturma için tablodaki verileri şu sırada yerleştirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> fiyatı, maks<PERSON><PERSON> fiyat, minimum fiyat, kapan<PERSON>ş fiyatı. ", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.CellRangeDialog.txtInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.CellRangeDialog.txtTitle": "Veri Aralığı Seç", "SSE.Views.CellSettings.strShrink": "Sığdırmak için küçült", "SSE.Views.CellSettings.strWrap": "<PERSON><PERSON>", "SSE.Views.CellSettings.textAngle": "Açı", "SSE.Views.CellSettings.textBackColor": "Arka plan rengi", "SSE.Views.CellSettings.textBackground": "Arka plan rengi", "SSE.Views.CellSettings.textBorderColor": "Renk", "SSE.Views.CellSettings.textBorders": "Sınır Stili", "SSE.Views.CellSettings.textClearRule": "Kuralları Temizle", "SSE.Views.CellSettings.textColor": "Renk Dolgusu", "SSE.Views.CellSettings.textColorScales": "Renk Ölçekleri", "SSE.Views.CellSettings.textCondFormat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textControl": "<PERSON><PERSON>", "SSE.Views.CellSettings.textDataBars": "Veri Çubukları", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Ön plan rengi", "SSE.Views.CellSettings.textGradient": "<PERSON>an no<PERSON>ı", "SSE.Views.CellSettings.textGradientColor": "Renk", "SSE.Views.CellSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "<PERSON><PERSON><PERSON>r", "SSE.Views.CellSettings.textLinear": "Doğrusal", "SSE.Views.CellSettings.textManageRule": "Kuralları Yönet", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textOrientation": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPattern": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Pozisyon", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "Yukarıda seçilen stili uygulayarak değiştirmek istediğiniz sınırları seçin", "SSE.Views.CellSettings.textSelection": "Geç<PERSON><PERSON> se<PERSON>", "SSE.Views.CellSettings.textThisPivot": "Bu pivottan", "SSE.Views.CellSettings.textThisSheet": "Bu çalışma <PERSON>fa<PERSON>ından", "SSE.Views.CellSettings.textThisTable": "bu tablodan", "SSE.Views.CellSettings.tipAddGradientPoint": "<PERSON><PERSON> nokt<PERSON> e<PERSON>", "SSE.Views.CellSettings.tipAll": "Dış Sınır ve Tüm İç Satırları Belirle", "SSE.Views.CellSettings.tipBottom": "Sadece Dış Alt Sınırı Belirle", "SSE.Views.CellSettings.tipDiagD": "Çapraz Aşağı Kenarlığı Ayarla", "SSE.Views.CellSettings.tipDiagU": "Çapraz Kenarlık Ayarla", "SSE.Views.CellSettings.tipInner": "Sadece İç Satırları Belirle", "SSE.Views.CellSettings.tipInnerHor": "Sadece Yatay İç Satırları Belirle", "SSE.Views.CellSettings.tipInnerVert": "Sad<PERSON>e Dikey İç Çizgileri Belirle", "SSE.Views.CellSettings.tipLeft": "Sadece Dış Sol Sınırı Belirle", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.CellSettings.tipOuter": "Sadece Dış Sınırı Belirle", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Gradyan noktasını kaldır", "SSE.Views.CellSettings.tipRight": "Sadece Dış Sağ Sınırı Belirle", "SSE.Views.CellSettings.tipTop": "Sadece Dış Üst Sınırı Belirle", "SSE.Views.ChartDataDialog.errorInFormula": "Girdiğiniz formülde bir hata var.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Referans geçerli değil. Referans, açık bir çalışma sayfasına yapılmalıdır.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Grafik başına serideki maksimum nokta sayısı 4096'dır.", "SSE.Views.ChartDataDialog.errorMaxRows": "Grafik başına maksimum veri serisi sayısı 255'tir.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Referans geç<PERSON><PERSON> değ<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, boyutlar veya veri etiketleri için referanslar tek bir hücre, satır veya sütun o<PERSON>lıdır.", "SSE.Views.ChartDataDialog.errorNoValues": "Bir grafik oluşturmak için serinin en az bir değer içermesi gerekir.", "SSE.Views.ChartDataDialog.errorStockChart": "Yanlış dizi sırası. Stok grafiği oluşturma için tablodaki verileri şu sırada yerleştirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> fiyatı, maks<PERSON><PERSON> fiyat, minimum fiyat, kapan<PERSON>ş fiyatı. ", "SSE.Views.ChartDataDialog.textAdd": "ekle", "SSE.Views.ChartDataDialog.textCategory": "<PERSON><PERSON><PERSON> (Kategori) Eksen <PERSON>i", "SSE.Views.ChartDataDialog.textData": "Grafik veri aralığı", "SSE.Views.ChartDataDialog.textDelete": "Kaldır", "SSE.Views.ChartDataDialog.textDown": "Aşağı", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Geçersiz hücre aralığı", "SSE.Views.ChartDataDialog.textSelectData": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textSeries": "Açıklama <PERSON>i (Seri)", "SSE.Views.ChartDataDialog.textSwitch": "Satır/<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textUp": "Yukarı", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Girdiğiniz formülde bir hata var.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Referans geçerli değil. Referans, açık bir çalışma sayfasına yapılmalıdır.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Grafik başına serideki maksimum nokta sayısı 4096'dır.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Grafik başına maksimum veri serisi sayısı 255'tir.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Referans geç<PERSON><PERSON> değ<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, boyutlar veya veri etiketleri için referanslar tek bir hücre, satır veya sütun o<PERSON>lıdır.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Bir grafik oluşturmak için serinin en az bir değer içermesi gerekir.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Yanlış dizi sırası. Stok grafiği oluşturma için tablodaki verileri şu sırada yerleştirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> fiyatı, maks<PERSON><PERSON> fiyat, minimum fiyat, kapan<PERSON>ş fiyatı. ", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Geçersiz hücre aralığı", "SSE.Views.ChartDataRangeDialog.textSelectData": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "<PERSON><PERSON><PERSON> etiket aralığı", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "X Değerleri", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y Değerleri", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON>ır <PERSON>", "SSE.Views.ChartSettings.strSparkColor": "Renk", "SSE.Views.ChartSettings.strTemplate": "Şablon", "SSE.Views.ChartSettings.textAdvanced": "Gelişmiş ayarları göster", "SSE.Views.ChartSettings.textBorderSizeErr": "Girilen değer yanlış.<br>Lütfen 0 ile 1584 pt arasında değer giriniz.", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textChartType": "Grafik Türünü Değiştir", "SSE.Views.ChartSettings.textEditData": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textFirstPoint": "İlk Nokta", "SSE.Views.ChartSettings.textHeight": "Yükseklik", "SSE.Views.ChartSettings.textHighPoint": "Yüksek Nokta", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Son Nokta", "SSE.Views.ChartSettings.textLowPoint": "Düşük Nokta", "SSE.Views.ChartSettings.textMarkers": "İşaretler", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON>egatif <PERSON>", "SSE.Views.ChartSettings.textRanges": "Veri Aralığı", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "Stil", "SSE.Views.ChartSettings.textType": "Tip", "SSE.Views.ChartSettings.textWidth": "Genişlik", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "HATA! Grafik başına serideki maksimum nokta sayısı 4096'dır.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "HATA! Her grafik için maksimum veri serileri sayısı 255'tir", "SSE.Views.ChartSettingsDlg.errorStockChart": "Yanlış dizi sırası. Stok grafiği oluşturma için tablodaki verileri şu sırada yerleştirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> fiyatı, maks<PERSON><PERSON> fiyat, minimum fiyat, kapan<PERSON>ş fiyatı. ", "SSE.Views.ChartSettingsDlg.textAbsolute": "Hücre<PERSON>le hareket etmeyin veya boyutlandırmayın", "SSE.Views.ChartSettingsDlg.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "G<PERSON><PERSON>l nesne bi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, otomatik şekil, çizelge veya tabloda hangi bilgilerin olduğunu daha iyi anlamalarına yardımcı olmak için görme veya bilişsel bozukluğu olan kişilere okunacak alternatif metin tabanlı temsili.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Başlık", "SSE.Views.ChartSettingsDlg.textAuto": "Otomatik", "SSE.Views.ChartSettingsDlg.textAutoEach": "Her Biri i<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Axis Settings", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Başlık", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Tik İşaretleri Arasında", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "Alt", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "Orta", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Grafik Elementleri &<br>Grafik Göstergesi", "SSE.Views.ChartSettingsDlg.textChartTitle": "Grafik başlığı", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataLabels": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataRows": "sat<PERSON>rlarda", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Göstergeyi görüntüle", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON><PERSON> <PERSON>ş hü<PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Veri noktalarını çizgi ile bağlayın", "SSE.Views.ChartSettingsDlg.textFit": "Genişliğe Sığdır", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Etiket formatı", "SSE.Views.ChartSettingsDlg.textGaps": "Aralıklar", "SSE.Views.ChartSettingsDlg.textGridLines": "Gridlines", "SSE.Views.ChartSettingsDlg.textGroup": "Grup Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON>de", "SSE.Views.ChartSettingsDlg.textHideAxis": "<PERSON><PERSON><PERSON> gizle", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "İçinde", "SSE.Views.ChartSettingsDlg.textInnerBottom": "İç Alt", "SSE.Views.ChartSettingsDlg.textInnerTop": "İç Ü<PERSON>", "SSE.Views.ChartSettingsDlg.textInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.ChartSettingsDlg.textLabelDist": "Eksen Etiket Mesafesi", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Etiketler arası interval", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Etiket Seçenekleri", "SSE.Views.ChartSettingsDlg.textLabelPos": "Etiket Pozisyonu", "SSE.Views.ChartSettingsDlg.textLayout": "Tasarım", "SSE.Views.ChartSettingsDlg.textLeft": "Sol", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Alt", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Sol", "SSE.Views.ChartSettingsDlg.textLegendPos": "Gösterge", "SSE.Views.ChartSettingsDlg.textLegendRight": "Sağ", "SSE.Views.ChartSettingsDlg.textLegendTop": "Üst", "SSE.Views.ChartSettingsDlg.textLines": "Satırlar", "SSE.Views.ChartSettingsDlg.textLocationRange": "Konum Aralığı", "SSE.Views.ChartSettingsDlg.textLow": "Düşük", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "İşaretler", "SSE.Views.ChartSettingsDlg.textMarksInterval": "İşaretler arası interval", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimum Değer", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON> ya<PERSON>", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Bindirme yok", "SSE.Views.ChartSettingsDlg.textOneCell": "Taş<PERSON> ama hücreleri boyutlandırma", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Tik İşaretlerinde", "SSE.Views.ChartSettingsDlg.textOut": "Dışarı", "SSE.Views.ChartSettingsDlg.textOuterTop": "Dış Üst", "SSE.Views.ChartSettingsDlg.textOverlay": "Bindirme", "SSE.Views.ChartSettingsDlg.textReverse": "<PERSON><PERSON> sıralanmış de<PERSON>", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Sırayı tersine çevir", "SSE.Views.ChartSettingsDlg.textRight": "Sağ", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSeparator": "Veri Etiketi Ayırıcı", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShow": "Show", "SSE.Views.ChartSettingsDlg.textShowBorders": "Grafik sınırlarını görüntüle", "SSE.Views.ChartSettingsDlg.textShowData": "<PERSON><PERSON><PERSON> satır ve sütunlardaki veriyi göster", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Boş hücreleri göster", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "Grafik değerlerini görüntüle", "SSE.Views.ChartSettingsDlg.textSingle": "Tek Sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "Düz", "SSE.Views.ChartSettingsDlg.textSnap": "Hücre <PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparkline Aralıkları", "SSE.Views.ChartSettingsDlg.textStraight": "Düz", "SSE.Views.ChartSettingsDlg.textStyle": "Stil", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Binlerce", "SSE.Views.ChartSettingsDlg.textTickOptions": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitle": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Gelişmiş Ayarlar", "SSE.Views.ChartSettingsDlg.textTop": "Üst", "SSE.Views.ChartSettingsDlg.textTrillions": "Trilyonlarca", "SSE.Views.ChartSettingsDlg.textTwoCell": "Taş<PERSON> ve hücreleri boyutlandır ", "SSE.Views.ChartSettingsDlg.textType": "Tip", "SSE.Views.ChartSettingsDlg.textTypeData": "Tip & Veri", "SSE.Views.ChartSettingsDlg.textUnits": "Birimleri görüntüle", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X Eksen Başlığı", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y eksen başlığı", "SSE.Views.ChartSettingsDlg.textZero": "Sıf<PERSON>r", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.ChartTypeDialog.errorComboSeries": "Bir kombinasyon grafiği oluşturmak için en az iki veri serisi seçin.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Seçilen grafik türü, mevcut bir grafiğin kullandığı ikincil ekseni gerektirir. Başka bir grafik türü seçin.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textStyle": "Stil", "SSE.Views.ChartTypeDialog.textTitle": "<PERSON><PERSON> tipi", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textDataRange": "Kaynak veri aralığı", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON> nereye <PERSON>receğinizi seçin", "SSE.Views.CreatePivotDialog.textExist": "Mevcut çalışma sayfası", "SSE.Views.CreatePivotDialog.textInvalidRange": "Geçersiz hücre aralığı", "SSE.Views.CreatePivotDialog.textNew": "Yeni <PERSON>ışma sayfası", "SSE.Views.CreatePivotDialog.textSelectData": "<PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textTitle": "Pivot Tablo Oluştur", "SSE.Views.CreatePivotDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.CreateSparklineDialog.textDataRange": "Kaynak veri aralığı", "SSE.Views.CreateSparklineDialog.textDestination": "Mini grafiklerin nereye yerleştirileceğini seçin", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Geçersiz hücre aralığı", "SSE.Views.CreateSparklineDialog.textSelectData": "<PERSON><PERSON>", "SSE.Views.CreateSparklineDialog.textTitle": "<PERSON> Grafik<PERSON>", "SSE.Views.CreateSparklineDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.DataTab.capBtnGroup": "Grup", "SSE.Views.DataTab.capBtnTextCustomSort": "<PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextDataValidation": "<PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Yinelenenleri Kaldır", "SSE.Views.DataTab.capBtnTextToCol": "<PERSON><PERSON>", "SSE.Views.DataTab.capBtnUngroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capDataFromText": "Veri al", "SSE.Views.DataTab.mniFromFile": "Yerel TXT/CSV'den", "SSE.Views.DataTab.mniFromUrl": "TXT/CSV web adresinden", "SSE.Views.DataTab.textBelow": "Ayrıntıların altındaki özet satırları", "SSE.Views.DataTab.textClear": "Anahattı temizle", "SSE.Views.DataTab.textColumns": "Sütunların grubu<PERSON>", "SSE.Views.DataTab.textGroupColumns": "Grup sütunları", "SSE.Views.DataTab.textGroupRows": "Grup satırları", "SSE.Views.DataTab.textRightOf": "Ayrıntıların sağındaki özet sütunları", "SSE.Views.DataTab.textRows": "Satırların grubunu çöz", "SSE.Views.DataTab.tipCustomSort": "<PERSON><PERSON>", "SSE.Views.DataTab.tipDataFromText": "Metin/CSV dosyasından veri alın", "SSE.Views.DataTab.tipDataValidation": "<PERSON><PERSON>", "SSE.Views.DataTab.tipGroup": "Grup hücre aralığı", "SSE.Views.DataTab.tipRemDuplicates": "Sayfadan yinelenen satırları kaldırın", "SSE.Views.DataTab.tipToColumns": "<PERSON><PERSON><PERSON> a<PERSON>ın", "SSE.Views.DataTab.tipUngroup": "Hücre aralığı grubunu çöz", "SSE.Views.DataValidationDialog.errorFormula": "<PERSON><PERSON><PERSON> anda bir hata olarak değerlendiriliyor. <PERSON><PERSON> etmek istiyor musun?", "SSE.Views.DataValidationDialog.errorInvalid": "\"{0}\" al<PERSON><PERSON> i<PERSON><PERSON> g<PERSON>di<PERSON>z değer geçersiz.", "SSE.Views.DataValidationDialog.errorInvalidDate": "\"{0}\" al<PERSON><PERSON> için girdiğiniz tarih geçersiz.", "SSE.Views.DataValidationDialog.errorInvalidList": "Liste kaynağı, sınırlandırılmış bir liste olmalı veya tek bir satır veya sütunu referans almalıdır.", "SSE.Views.DataValidationDialog.errorInvalidTime": "\"{0}\" al<PERSON><PERSON> için girdiğiniz saat geçersiz.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "\"{1}\" alan<PERSON>, \"{0}\" alan<PERSON><PERSON>n büyük veya ona eşit olmalıdır.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Hem \"{0}\" alanına hem de \"{1}\" alanına bir değer girmelisiniz.", "SSE.Views.DataValidationDialog.errorMustEnterValue": "\"{0}\" al<PERSON><PERSON>na bir değer girmelisiniz.", "SSE.Views.DataValidationDialog.errorNamedRange": "Belirttiğiniz adlandırılmış bir aralık bulunamıyor.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "<PERSON><PERSON><PERSON><PERSON> \"{0}\" koşullarında kullanılamaz.", "SSE.Views.DataValidationDialog.errorNotNumeric": "\"{0}\" alan<PERSON> sayısal bir değer, sayısal bir ifade veya sayısal bir değer içeren bir hücreye başvuruda bulunmalıdır.", "SSE.Views.DataValidationDialog.strError": "Hata Uyarısı", "SSE.Views.DataValidationDialog.strInput": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.strSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAlert": "Uyarı", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON> ver", "SSE.Views.DataValidationDialog.textApply": "Bu değişiklikleri aynı ayarlarla diğer tüm hücrelere uygula", "SSE.Views.DataValidationDialog.textCellSelected": "<PERSON><PERSON><PERSON>, bu giri<PERSON> mesa<PERSON>ı<PERSON><PERSON> g<PERSON>ster", "SSE.Views.DataValidationDialog.textCompare": "Karşılaştırmak", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Bitiş Tarihi", "SSE.Views.DataValidationDialog.textEndTime": "Bitiş zamanı", "SSE.Views.DataValidationDialog.textError": "<PERSON>a mesajı", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "Boşluğu yoksay", "SSE.Views.DataValidationDialog.textInput": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textShowDropDown": "Hücrede açılır listeyi gö<PERSON>", "SSE.Views.DataValidationDialog.textShowError": "Geçersiz veri girildikten sonra hata uyarısı göster", "SSE.Views.DataValidationDialog.textShowInput": "<PERSON><PERSON>cre seçildiğinde giriş mesajını göster", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Başlangıç <PERSON>", "SSE.Views.DataValidationDialog.textStartTime": "Başlangıç <PERSON>", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "Stil", "SSE.Views.DataValidationDialog.textTitle": "Başlık", "SSE.Views.DataValidationDialog.textUserEnters": "Kullanıcı geçersiz veri girdiğinde bu hata uyarısını göster", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON><PERSON> bi<PERSON>", "SSE.Views.DataValidationDialog.txtBetween": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "Ondalık", "SSE.Views.DataValidationDialog.txtElTime": "Ge<PERSON><PERSON> zaman", "SSE.Views.DataValidationDialog.txtEndDate": "Bitiş Tarihi", "SSE.Views.DataValidationDialog.txtEndTime": "Bitiş zamanı", "SSE.Views.DataValidationDialog.txtEqual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "büyük veya eşit", "SSE.Views.DataValidationDialog.txtLength": "Uzunluk", "SSE.Views.DataValidationDialog.txtLessThan": "Küçüktür", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Küçük eşittir", "SSE.Views.DataValidationDialog.txtList": "Liste", "SSE.Views.DataValidationDialog.txtNotBetween": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtNotEqual": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtOther": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "Başlangıç <PERSON>", "SSE.Views.DataValidationDialog.txtStartTime": "Başlangıç z<PERSON>ı", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtTime": "Zaman", "SSE.Views.DataValidationDialog.txtWhole": "Tamsayı", "SSE.Views.DigitalFilterDialog.capAnd": "Ve", "SSE.Views.DigitalFilterDialog.capCondition1": "eşit", "SSE.Views.DigitalFilterDialog.capCondition10": "<PERSON><PERSON><PERSON><PERSON> bitmeyen:", "SSE.Views.DigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "ş<PERSON>u içermeyen:", "SSE.Views.DigitalFilterDialog.capCondition2": "şuna eşit olmayan:", "SSE.Views.DigitalFilterDialog.capCondition3": "şundan büyük:", "SSE.Views.DigitalFilterDialog.capCondition4": "şundan büyük yada eşit:", "SSE.Views.DigitalFilterDialog.capCondition5": "şundan küçük:", "SSE.Views.DigitalFilterDialog.capCondition6": "şundan küçük yada eşit:", "SSE.Views.DigitalFilterDialog.capCondition7": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>ın:", "SSE.Views.DigitalFilterDialog.capCondition8": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>aya<PERSON>:", "SSE.Views.DigitalFilterDialog.capCondition9": "<PERSON><PERSON><PERSON><PERSON> biten:", "SSE.Views.DigitalFilterDialog.capOr": "yada", "SSE.Views.DigitalFilterDialog.textNoFilter": "filtre yok", "SSE.Views.DigitalFilterDialog.textShowRows": "Şuradaki satırları göster:", "SSE.Views.DigitalFilterDialog.textUse1": "Her<PERSON><PERSON> bir tek karakteri sunmak için ?'ini kullanın", "SSE.Views.DigitalFilterDialog.textUse2": "Herhangi bir karakter serisini sunmak için *'ı kullanın", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON> Filtre", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedSlicerText": "Dilimleyici Gelişmiş Ayarlar", "SSE.Views.DocumentHolder.bottomCellText": "Alta Hizala", "SSE.Views.DocumentHolder.bulletsText": "<PERSON><PERSON> <PERSON>", "SSE.Views.DocumentHolder.centerCellText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.chartText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "Satır", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Rotate at 270°", "SSE.Views.DocumentHolder.direct90Text": "Rotate at 90°", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Text Direction", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "Köprüyü <PERSON>ü<PERSON>le", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON>", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "Yukarı Satır", "SSE.Views.DocumentHolder.insertRowBelowText": "Aşağı Satır", "SSE.Views.DocumentHolder.originalSizeText": "G<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "Köprüyü Kaldır", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectRowText": "Satır", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strDelete": "İmzayı kaldır", "SSE.Views.DocumentHolder.strDetails": "İmza Ayrıntıları", "SSE.Views.DocumentHolder.strSetup": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strSign": "İmzala", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "İleri Taşı", "SSE.Views.DocumentHolder.textArrangeFront": "Önplana <PERSON>", "SSE.Views.DocumentHolder.textAverage": "ORTALAMA", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "Saymak", "SSE.Views.DocumentHolder.textCrop": "Kırpmak", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Sığdır", "SSE.Views.DocumentHolder.textEditPoints": "Noktaları Düzenle", "SSE.Views.DocumentHolder.textEntriesList": "Açılır <PERSON>en seç", "SSE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFreezePanes": "Parçaları Dondur", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromUrl": "URL'den", "SSE.Views.DocumentHolder.textListSettings": "Liste Ayarları", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMax": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON> fazla fonksiyon", "SSE.Views.DocumentHolder.textMoreFormats": "Daha fazla format", "SSE.Views.DocumentHolder.textNone": "Hiç<PERSON>i", "SSE.Views.DocumentHolder.textNumbering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Döndür 90° Saatyönütersi", "SSE.Views.DocumentHolder.textRotate90": "Döndür 90° Saatyönü", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Alta Hizala", "SSE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON> Hizala", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON> Hizala", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Toplam", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Unfreeze Panes", "SSE.Views.DocumentHolder.textVar": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersArrow": "Ok işaretleri", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersDash": "Çizgi işaretleri", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Dolu eşkenar dörtgen işaretler", "SSE.Views.DocumentHolder.tipMarkersFRound": "<PERSON><PERSON> yu<PERSON>lak işaretler", "SSE.Views.DocumentHolder.tipMarkersFSquare": "<PERSON><PERSON> i<PERSON>", "SSE.Views.DocumentHolder.tipMarkersHRound": "İçi boş daire işaretler", "SSE.Views.DocumentHolder.tipMarkersStar": "Yıldız işaretleri", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON> Hizala", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Define Name", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Satır Uzunluğuna Uyarla", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Seçili Sparkline Grupları Temizle", "SSE.Views.DocumentHolder.txtClearSparklines": "Seçili Sparklineları Temizle", "SSE.Views.DocumentHolder.txtClearText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCondFormat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCopy": "Kopyala", "SSE.Views.DocumentHolder.txtCurrency": "Para Birimi", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON>ş<PERSON>ği", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Özel Satır <PERSON>ği", "SSE.Views.DocumentHolder.txtCustomSort": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelete": "Sil", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON> o<PERSON>", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON> d<PERSON>", "SSE.Views.DocumentHolder.txtFilter": "Filtre", "SSE.Views.DocumentHolder.txtFilterCellColor": "<PERSON><PERSON><PERSON> rengine göre filtrele", "SSE.Views.DocumentHolder.txtFilterFontColor": "<PERSON><PERSON><PERSON> rengine göre filtrele", "SSE.Views.DocumentHolder.txtFilterValue": "<PERSON><PERSON><PERSON> hücre değ<PERSON>ne göre filtrele", "SSE.Views.DocumentHolder.txtFormula": "Fonksiyon <PERSON>", "SSE.Views.DocumentHolder.txtFraction": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGroup": "Grup", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsert": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "Köprü", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "Sayı Formatı", "SSE.Views.DocumentHolder.txtPaste": "Yapıştır", "SSE.Views.DocumentHolder.txtPercentage": "<PERSON><PERSON>z<PERSON>", "SSE.Views.DocumentHolder.txtReapply": "<PERSON><PERSON>den uygula", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "Sa<PERSON>ır <PERSON>", "SSE.Views.DocumentHolder.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSelect": "Seç", "SSE.Views.DocumentHolder.txtShiftDown": "Hücreleri aşağı kaydır", "SSE.Views.DocumentHolder.txtShiftLeft": "Hücreleri sola kaydır", "SSE.Views.DocumentHolder.txtShiftRight": "Hücreleri sağa kaydır", "SSE.Views.DocumentHolder.txtShiftUp": "Hücreleri yukarı kaydır", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSort": "S<PERSON>rala", "SSE.Views.DocumentHolder.txtSortCellColor": "Seçili Hücre Rengi üstte", "SSE.Views.DocumentHolder.txtSortFontColor": "Seçili Yazı Rengi üstte", "SSE.Views.DocumentHolder.txtSparklines": "Sparklinelar", "SSE.Views.DocumentHolder.txtText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTextAdvanced": "Gelişmiş Paragraf A<PERSON>ı", "SSE.Views.DocumentHolder.txtTime": "Zaman", "SSE.Views.DocumentHolder.txtUngroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtWidth": "Genişlik", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strLayout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strSubtotals": "<PERSON>", "SSE.Views.FieldSettingsDialog.textReport": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textTitle": "<PERSON>", "SSE.Views.FieldSettingsDialog.txtAverage": "ORTALAMA", "SSE.Views.FieldSettingsDialog.txtBlank": "Her öğeden sonra boş satır ekle", "SSE.Views.FieldSettingsDialog.txtBottom": "Grubun altında göster", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompakt", "SSE.Views.FieldSettingsDialog.txtCount": "Saymak", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON> say", "SSE.Views.FieldSettingsDialog.txtCustomName": "<PERSON><PERSON> ad", "SSE.Views.FieldSettingsDialog.txtEmpty": "Veri içermeyen öğeleri göster", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "<PERSON><PERSON><PERSON>ini her satırda tekrarlayın", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Ara <PERSON>lamları göster", "SSE.Views.FieldSettingsDialog.txtSourceName": "Kaynak adı:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Toplam", "SSE.Views.FieldSettingsDialog.txtSummarize": "<PERSON> i<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Grubun başında gö<PERSON>", "SSE.Views.FieldSettingsDialog.txtVar": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON><PERSON> kon<PERSON>u aç", "SSE.Views.FileMenu.btnCloseMenuCaption": "Men<PERSON><PERSON>ü <PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnDownloadCaption": "Farklı İndir", "SSE.Views.FileMenu.btnExitCaption": "Çıkış", "SSE.Views.FileMenu.btnFileOpenCaption": "Aç", "SSE.Views.FileMenu.btnHelpCaption": "Yardım", "SSE.Views.FileMenu.btnHistoryCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnInfoCaption": "E-<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnPrintCaption": "Yazdır", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "En sonunucuyu aç", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON>ı<PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "E-tabloya geri dön", "SSE.Views.FileMenu.btnRightsCaption": "Access Rights", "SSE.Views.FileMenu.btnSaveAsCaption": "Save as", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Kopyasını kaydet", "SSE.Views.FileMenu.btnSettingsCaption": "Gelişmiş <PERSON>", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Boş Elektronik Tablo", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Uygulama", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON> değiştir", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Sahip", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokasyon", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Hakkı olan k<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Başlık", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Yüklendi", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON> değiştir", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Hakkı olan k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Co-editing mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "ondalık ayırıcı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Fast", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Yazı Tipi İpucu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formula Language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "İçerik yapıştırıldığında Yapıştırma Seçenekleri düğmesini göster", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regional Settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Example: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Arayüz teması", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Binlik ayırıcı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "<PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>i", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Bölgesel ayarlara dayalı ayırıcılar kullanın", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Varsayılan Yakınlaştırma <PERSON>eri", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Her 10 dakika", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Her 30 dakika", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Her 5 Dakika", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Her Saat", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Otomatik <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Otomatik <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Devre Dışı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "<PERSON> s<PERSON> kaydet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Her Dakika", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "<PERSON>fer<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Otomatik Düzeltme Seçenekleri", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusça", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarca", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Katalanca", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Varsayılan önbellek modu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Santimetre", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Çek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danimarkalı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "De<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "English", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "İspanyolca", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Fince", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Fransızca", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "İnç", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "OS X olarak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norveççe", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Flemenkçe", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Parlat", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Nokta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portekizce (Brezilya)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portekizce (Portekiz)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Rumence", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Russian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Tüm makroları bildirimde bulunmadan etkinleştir", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovakça", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovence", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Tümünü Devre Dışı Bırak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Tüm makroları bildirimde bulunmadan devre dışı bırakın", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "İsveççe", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Türkçe", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukraynaca", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Viyetnam dili", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Bütün macroları uyarı vererek devre dışı bırak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "Windows olarak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Uyarı", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Parola ile", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Elektronik Tabloyu Koru", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON><PERSON> ile", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>, imzaları e-tablodan kaldıracak.<br><PERSON><PERSON> edilsin mi?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Bu tablo parola ile korunmuştur", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Bu elektronik tablonun imzalanması gerekiyor.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Elektronik tabloya geçerli imzalar eklendi. Elektronik tablo, düzenlemeye karşı korumalıdır.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Elektronik tablodaki bazı dijital imzalar geçersiz veya doğrulanamadı. Elektronik tablo, düzenlemeye karşı korumalıdır.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "İmzaları görüntüle", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 Renk skalası", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 Renk skalası", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textAppearance": "Çubuk Görünümü", "SSE.Views.FormatRulesEditDlg.textApply": "Aralığa Uygula", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Otomatik", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Çubuk <PERSON>", "SSE.Views.FormatRulesEditDlg.textBold": "Kalı<PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "Sınır", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Kenarlıklar <PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Sınır Stili", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Alt Sınırlar", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "<PERSON><PERSON><PERSON><PERSON> biçimlendirme eklenemiyor.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Hücre orta noktası", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Dikey İç Sınırlar", "SSE.Views.FormatRulesEditDlg.textClear": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textContext": "Bağlam", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Diyagonal Aşağı Sınır", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diyagonal Yukarı Sınır", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Geçerli bir formü<PERSON> girin.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Girdiğiniz <PERSON>ü<PERSON> bir sayı, tari<PERSON>, saat veya dize olarak değerlendirilmiyor.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON> <PERSON> girin.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Girdiğ<PERSON>z <PERSON> geçerli bir sayı, tarih, saat veya dize değil.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "{0} <PERSON><PERSON><PERSON>, {1} değerinden büyük olmalıdır.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "{0} ile {1} a<PERSON><PERSON>nda bir sayı girin.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabel": "{0} {1} <PERSON><PERSON><PERSON><PERSON> ve", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "{0} {1} <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Bir veya daha fazla simge veri aralığı çakışıyor.<br>Simge veri aralı<PERSON><PERSON><PERSON><PERSON>, aralıklar çakışmayacak şekilde ayarlayın.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Simge <PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "İç <PERSON>ınırlar", "SSE.Views.FormatRulesEditDlg.textInvalid": "Geçersiz veri aralığı.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Soldan sağa", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Sol Sınırlar", "SSE.Views.FormatRulesEditDlg.textLongBar": "en uzun çubuk", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "<PERSON><PERSON><PERSON><PERSON> nokta", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Ya<PERSON>y <PERSON>ınırlar", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Orta nokta", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Alt nokta", "SSE.Views.FormatRulesEditDlg.textNegative": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNewColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Sınır yok", "SSE.Views.FormatRulesEditDlg.textNone": "Hiç<PERSON>i", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Belirtilen değerlerden biri veya daha fazlası geçerli bir yüzde değil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Belirtilen {0} değeri geçerli bir yüzde değ<PERSON>.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Belirtilen değerlerden biri veya daha fazlası geçerli bir yüzdelik dilim değil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Belirtilen {0} değeri geçerli bir yüzdelik dilim değil.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Dış Sınırlar", "SSE.Views.FormatRulesEditDlg.textPercent": "<PERSON><PERSON>z<PERSON>", "SSE.Views.FormatRulesEditDlg.textPercentile": "Yüzdelik", "SSE.Views.FormatRulesEditDlg.textPosition": "Pozisyon", "SSE.Views.FormatRulesEditDlg.textPositive": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Renk ölçekleri, veri ç<PERSON> ve simge kümeleri için koşullu biçimlendirme ölçütlerinde göreli başvuruları kullanamazsınız.", "SSE.Views.FormatRulesEditDlg.textReverse": "Simgelerin Sırasını Ters Çevir", "SSE.Views.FormatRulesEditDlg.textRight2Left": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Sağ Sınırlar", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "Pozitif ile aynı", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textShortBar": "en kısa <PERSON>", "SSE.Views.FormatRulesEditDlg.textShowBar": "Yalnızca çubuğu göster", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Yalnızca simgeyi göster", "SSE.Views.FormatRulesEditDlg.textSingleRef": "<PERSON>u tür ba<PERSON><PERSON><PERSON>, koşullu biçimlendirme formülünde kullanılamaz.<br>Başvuruyu tek bir hücreyle değiştirin veya başvuruyu =SUM(A1:B5) gibi bir çalışma sayfası işleviyle kullanın.", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Üstü çizili", "SSE.Views.FormatRulesEditDlg.textSubscript": "Alt Simge", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Üst Simge", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Üst Sınırlar", "SSE.Views.FormatRulesEditDlg.textUnderline": "Altı çizili", "SSE.Views.FormatRulesEditDlg.tipBorders": "Sınırlar", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Sayı Formatı", "SSE.Views.FormatRulesEditDlg.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Para Birimi", "SSE.Views.FormatRulesEditDlg.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.FormatRulesEditDlg.txtFraction": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "<PERSON><PERSON>z<PERSON>", "SSE.Views.FormatRulesEditDlg.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtText": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTime": "Zaman", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Biçimlendirme Kuralını Düzenle", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Yeni Biçimlendirme Kuralı", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1. standart gel<PERSON><PERSON><PERSON><PERSON>i ortalamanın üzerinde", "SSE.Views.FormatRulesManagerDlg.text1Below": "1. standart gel<PERSON><PERSON><PERSON><PERSON><PERSON> ortalamanın altında", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 standart geliş<PERSON>rici ortalamanın üzerinde", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 standart geliştirme ortalamanın altında", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 standart geliştirici ortalamanın üzerinde", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 standart geliştirme ortalamanın altında", "SSE.Views.FormatRulesManagerDlg.textAbove": "Ortalama üstü", "SSE.Views.FormatRulesManagerDlg.textApply": "Başvurmak", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.FormatRulesManagerDlg.textBelow": "Ortalama altı", "SSE.Views.FormatRulesManagerDlg.textBetween": "{0} ile {1} a<PERSON><PERSON>nda", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Dereceli renk skalası", "SSE.Views.FormatRulesManagerDlg.textContains": "Hücre değeri şunları içerir:", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "<PERSON><PERSON><PERSON> bo<PERSON> bir değer içeriyor", "SSE.Views.FormatRulesManagerDlg.textContainsError": "<PERSON><PERSON><PERSON> bir hata içeriyor", "SSE.Views.FormatRulesManagerDlg.textDelete": "Sil", "SSE.Views.FormatRulesManagerDlg.textDown": "Kuralı aşağı taşı", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "<PERSON><PERSON><PERSON> biter:", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Ortalamaya eşit veya ortalamanın üzerinde", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Ortalamaya eşit veya ortalamanın altında", "SSE.Views.FormatRulesManagerDlg.textFormat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textIconSet": "<PERSON>m<PERSON> seti", "SSE.Views.FormatRulesManagerDlg.textNew": "yeni", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "{0} ile {1} <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotContains": "<PERSON><PERSON><PERSON>çermiyor", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "<PERSON><PERSON><PERSON> bo<PERSON> bir değer içermiyor", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "<PERSON><PERSON><PERSON> bir hata içermiyor", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textSelectData": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textSelection": "<PERSON><PERSON> an<PERSON> se<PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Bu pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Bu çalışma sayfası", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Bu tablo", "SSE.Views.FormatRulesManagerDlg.textUnique": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textUp": "Kuralı yukarı taşı", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "<PERSON>u öğe başka bir kullanıcı tarafından düzenleniyor.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Ondalık", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Kaynağa bağlı", "SSE.Views.FormatSettingsDialog.textSeparator": "1000 ayracı kullan", "SSE.Views.FormatSettingsDialog.textSymbols": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "Sayı Formatı", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs10": "Onluk (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON><PERSON><PERSON>k (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "On altılık (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Yarım (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "D<PERSON>rtlük (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Para Birimi", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Lütfen özel sayı biçimini dikkatli bir şekilde girin. Elektronik Tablo Düzenleyici, xlsx dosyasını etkileyebilecek hatalar için özel biçimleri kontrol etmez.", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "Hiç<PERSON>i", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "<PERSON><PERSON>z<PERSON>", "SSE.Views.FormatSettingsDialog.txtSample": "Örnek:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtTime": "Saat", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON><PERSON> ha<PERSON><PERSON> kadar (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON> ha<PERSON> ka<PERSON> (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON> (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Fonksiyon Grubu Seç", "SSE.Views.FormulaDialog.textListDescription": "Fonksiyon Seç", "SSE.Views.FormulaDialog.txtRecommended": "Tavsiye edilen", "SSE.Views.FormulaDialog.txtSearch": "Ara", "SSE.Views.FormulaDialog.txtTitle": "Fonksiyon <PERSON>", "SSE.Views.FormulaTab.textAutomatic": "Otomatik", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Geçerli sayfayı hesapla", "SSE.Views.FormulaTab.textCalculateWorkbook": "Çalışma kitabını hesapla", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "<PERSON><PERSON>m çalışma kitabını hesaplayın", "SSE.Views.FormulaTab.txtAdditional": "Ek", "SSE.Views.FormulaTab.txtAutosum": "otomatik toplam", "SSE.Views.FormulaTab.txtAutosumTip": "Toplama", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Fonksiyon", "SSE.Views.FormulaTab.txtFormulaTip": "Fonksiyon <PERSON>", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON> fazla fonksiyon", "SSE.Views.FormulaTab.txtRecent": "<PERSON>", "SSE.Views.FormulaWizard.textAny": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textArgument": "Değişken", "SSE.Views.FormulaWizard.textFunction": "Fonksiyon", "SSE.Views.FormulaWizard.textFunctionRes": "fon<PERSON><PERSON><PERSON>u", "SSE.Views.FormulaWizard.textHelp": "Bu fonksiyon hakkında yardım", "SSE.Views.FormulaWizard.textLogical": "Mantıksal", "SSE.Views.FormulaWizard.textNoArgs": "Bu i<PERSON><PERSON>in ba<PERSON><PERSON><PERSON><PERSON>z değişkeni yok", "SSE.Views.FormulaWizard.textNumber": "sayı", "SSE.Views.FormulaWizard.textRef": "referans", "SSE.Views.FormulaWizard.textText": "<PERSON><PERSON>", "SSE.Views.FormulaWizard.textTitle": "Fonksiyon Argümanları", "SSE.Views.FormulaWizard.textValue": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textAlign": "<PERSON><PERSON> kenar b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hizala", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textBold": "Kalı<PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "Orta", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDate": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Farklı ilk sayfa", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Farklı tek ve çift sayfalar", "SSE.Views.HeaderFooterDialog.textEven": "Çift Sayfa", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON><PERSON> adı", "SSE.Views.HeaderFooterDialog.textFirst": "İlk sayfa", "SSE.Views.HeaderFooterDialog.textFooter": "Altbilgi", "SSE.Views.HeaderFooterDialog.textHeader": "Başlık", "SSE.Views.HeaderFooterDialog.textInsert": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "Sol", "SSE.Views.HeaderFooterDialog.textMaxError": "Girdiğiniz metin dizisi çok uzun. Kullanılan karakter sayısını azaltın.", "SSE.Views.HeaderFooterDialog.textNewColor": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textOdd": "Tek <PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "Sayfa sayısı", "SSE.Views.HeaderFooterDialog.textPageNum": "Sayfa Numarası", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "Sağ", "SSE.Views.HeaderFooterDialog.textScale": "Belgeyle ö<PERSON>çeklendir", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textStrikeout": "Üstü çizili", "SSE.Views.HeaderFooterDialog.textSubscript": "Alt Simge", "SSE.Views.HeaderFooterDialog.textSuperscript": "Üst Simge", "SSE.Views.HeaderFooterDialog.textTime": "Zaman", "SSE.Views.HeaderFooterDialog.textTitle": "Üst/Alt Bilgi Ayarları", "SSE.Views.HeaderFooterDialog.textUnderline": "Altı çizili", "SSE.Views.HeaderFooterDialog.tipFontName": "Yazı Tipi", "SSE.Views.HeaderFooterDialog.tipFontSize": "Yazı Boyutu", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Şuna bağlantıla:", "SSE.Views.HyperlinkSettingsDialog.strRange": "Aralık", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Say<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Kopyala", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Selected range", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Başlığı buraya girin", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Bağlantıyı buraya girin", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON> bi<PERSON> buraya girin", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Harici Bağlantı", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Bağlantıyı al", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON><PERSON>lığı", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Ekran<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Köprü Ayarları", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON>u alan \"http://www.example.com\" formatında URL olmalıdır", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Bu alan 2083 karakterle sınırlıdır", "SSE.Views.ImageSettings.textAdvanced": "Gelişmiş Ayarları Göster", "SSE.Views.ImageSettings.textCrop": "Kırpmak", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Sığdır", "SSE.Views.ImageSettings.textCropToShape": "Şekillendirmek için kırp", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromUrl": "URL'den", "SSE.Views.ImageSettings.textHeight": "Yükseklik", "SSE.Views.ImageSettings.textHint270": "Döndür 90° Saatyönütersi", "SSE.Views.ImageSettings.textHint90": "Döndür 90° Saatyönü", "SSE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "G<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textRotate90": "Döndür 90°", "SSE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "Genişlik", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Hücre<PERSON>le hareket etmeyin veya boyutlandırmayın", "SSE.Views.ImageSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "G<PERSON><PERSON>l nesne bi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, otomatik şekil, çizelge veya tabloda hangi bilgilerin olduğunu daha iyi anlamalarına yardımcı olmak için görme veya bilişsel bozukluğu olan kişilere okunacak alternatif metin tabanlı temsili.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Başlık", "SSE.Views.ImageSettingsAdvanced.textAngle": "Açı", "SSE.Views.ImageSettingsAdvanced.textFlipped": "çevrilmiş", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Taş<PERSON> ama hücreleri boyutlandırma", "SSE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textSnap": "Hücre <PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Resim - <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Taş<PERSON> ve hücreleri boyutlandır ", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipAbout": "Hakkında", "SSE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "Ara", "SSE.Views.LeftMenu.tipSpellcheck": "<PERSON><PERSON><PERSON><PERSON> deneti<PERSON>", "SSE.Views.LeftMenu.tipSupport": "Geri Bildirim & Destek", "SSE.Views.LeftMenu.txtDeveloper": "GELİŞTİRİCİ MODU", "SSE.Views.LeftMenu.txtLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.txtTrial": "DENEME MODU", "SSE.Views.LeftMenu.txtTrialDev": "<PERSON><PERSON><PERSON>", "SSE.Views.MacroDialog.textMacro": "<PERSON><PERSON><PERSON> adı", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "Alt", "SSE.Views.MainSettingsPrint.strLandscape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "Sol", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Portre", "SSE.Views.MainSettingsPrint.strPrint": "Yazdır", "SSE.Views.MainSettingsPrint.strPrintTitles": "Başlıkları yazdır", "SSE.Views.MainSettingsPrint.strRight": "Sağ", "SSE.Views.MainSettingsPrint.strTop": "Üst", "SSE.Views.MainSettingsPrint.textActualSize": "G<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textFitCols": "Tüm Sütunları Bir Sayfaya Sığdır", "SSE.Views.MainSettingsPrint.textFitPage": "Yaprağı Bir Sayfaya Sığdır", "SSE.Views.MainSettingsPrint.textFitRows": "Tüm Satırları Bir Sayfaya Sığdır", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "Ölçeklendirme", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Kılavuz Çizgilerini Yazdır", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Satır ve Sütun Başlıklarını Yazdır", "SSE.Views.MainSettingsPrint.textRepeat": "Tekra<PERSON>...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Soldaki sütunları tekrarla", "SSE.Views.MainSettingsPrint.textRepeatTop": "Üstteki satırları tekrarlayın", "SSE.Views.MainSettingsPrint.textSettings": "<PERSON><PERSON><PERSON>:", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defined name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Data Range", "SSE.Views.NamedRangeEditDlg.textExistName": "ERROR! Range with such a name already exists", "SSE.Views.NamedRangeEditDlg.textInvalidName": "ERROR! Invalid range name", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERROR! Invalid cell range", "SSE.Views.NamedRangeEditDlg.textIsLocked": "HATA! Bu öğe başka bir kullanıcı tarafından düzenleniyor.", "SSE.Views.NamedRangeEditDlg.textName": "Ad", "SSE.Views.NamedRangeEditDlg.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Select Data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Edit Name", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "New Name", "SSE.Views.NamedRangePasteDlg.textNames": "Named Ranges", "SSE.Views.NamedRangePasteDlg.txtTitle": "Paste Name", "SSE.Views.NameManagerDlg.closeButtonText": "Close", "SSE.Views.NameManagerDlg.guestText": "Guest", "SSE.Views.NameManagerDlg.textDataRange": "Data Range", "SSE.Views.NameManagerDlg.textDelete": "Delete", "SSE.Views.NameManagerDlg.textEdit": "Edit", "SSE.Views.NameManagerDlg.textEmpty": "No named ranges have been created yet.<br>Create at least one named range and it will appear in this field.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "All", "SSE.Views.NameManagerDlg.textFilterDefNames": "Defined names", "SSE.Views.NameManagerDlg.textFilterSheet": "Names Scoped to Sheet", "SSE.Views.NameManagerDlg.textFilterTableNames": "Table names", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Names Scoped to Workbook", "SSE.Views.NameManagerDlg.textNew": "New", "SSE.Views.NameManagerDlg.textnoNames": "No named ranges matching your filter could be found.", "SSE.Views.NameManagerDlg.textRanges": "Named Ranges", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "<PERSON>u öğe başka bir kullanıcı tarafından düzenleniyor.", "SSE.Views.NameManagerDlg.txtTitle": "Name Manager", "SSE.Views.NameManagerDlg.warnDelete": "{0} adını silmek istediğinizden emin misiniz?", "SSE.Views.PageMarginsDialog.textBottom": "Alt", "SSE.Views.PageMarginsDialog.textLeft": "Sol", "SSE.Views.PageMarginsDialog.textRight": "Sağ", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Üst", "SSE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON>r <PERSON>", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Aralık", "SSE.Views.ParagraphSettings.strSpacingAfter": "Sonra", "SSE.Views.ParagraphSettings.strSpacingBefore": "Önce", "SSE.Views.ParagraphSettings.textAdvanced": "Gelişmiş ayarları göster", "SSE.Views.ParagraphSettings.textAt": "Şurada:", "SSE.Views.ParagraphSettings.textAtLeast": "En az", "SSE.Views.ParagraphSettings.textAuto": "Çoklu", "SSE.Views.ParagraphSettings.textExact": "<PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "Otomatik", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Belirtilen sekmeler bu alanda görünecektir", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Üstü çift çizili", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Sol", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "<PERSON><PERSON><PERSON>r <PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Sağ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "sonra", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Önce", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Tara<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Yazı Tipi", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Girinti & Boşluk", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Küçük büyük harfler", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Aralık", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Üstü çizili", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Altsimge", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Üstsimge", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Sekme", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Hi<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Çoklu", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Varsayılan Sekme", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "İlk Satır", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Asılı", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON> yana yaslı", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(hi<PERSON><PERSON><PERSON>)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Kaldır", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Belirt", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Orta", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Sol", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Sağ", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraf - <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Otomatik", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "<PERSON><PERSON><PERSON><PERSON> bitmeyen:", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "ş<PERSON>u içermeyen:", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "şundan büyük:", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "şundan büyük yada eşit:", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "şundan küçük:", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "şundan küçük yada eşit:", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>ın:", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>aya<PERSON>:", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "<PERSON><PERSON><PERSON><PERSON> biten:", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Show items for which the label:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Şunları gösteren öğeleri göster:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Her<PERSON><PERSON> bir tek karakteri sunmak için ?'ini kullanın", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Herhangi bir karakter serisini sunmak için *'ı kullanın", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "Ve", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Etiket Filtresi", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "Otomatik", "SSE.Views.PivotGroupDialog.textBy": "Tara<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "Bitiyor", "SSE.Views.PivotGroupDialog.textError": "<PERSON><PERSON> alan sayı<PERSON> bir değer olmalıdır", "SSE.Views.PivotGroupDialog.textGreaterError": "Bitiş sayısı ba<PERSON><PERSON><PERSON>ç sayısından büyük olmalıdır", "SSE.Views.PivotGroupDialog.textHour": "Saat", "SSE.Views.PivotGroupDialog.textMin": "Dakika", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textQuart": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "saniye", "SSE.Views.PivotGroupDialog.textStart": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textYear": "yıl", "SSE.Views.PivotGroupDialog.txtTitle": "Gruplama", "SSE.Views.PivotSettings.textAdvanced": "Gelişmiş Ayarları Göster", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "Alanları Seç", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "Satırlar", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddFilter": "Filtrelere Ekle", "SSE.Views.PivotSettings.txtAddRow": "Satırlara <PERSON>", "SSE.Views.PivotSettings.txtAddValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtFieldSettings": "<PERSON>", "SSE.Views.PivotSettings.txtMoveBegin": "Başa taşı", "SSE.Views.PivotSettings.txtMoveColumn": "Sütunlara <PERSON>şı", "SSE.Views.PivotSettings.txtMoveDown": "Aşağı in", "SSE.Views.PivotSettings.txtMoveEnd": "<PERSON><PERSON> ta<PERSON>ı", "SSE.Views.PivotSettings.txtMoveFilter": "Filtrelere taşı", "SSE.Views.PivotSettings.txtMoveRow": "Satırlara taşı", "SSE.Views.PivotSettings.txtMoveUp": "Yukarı çık", "SSE.Views.PivotSettings.txtMoveValues": "<PERSON><PERSON><PERSON><PERSON>e taşı", "SSE.Views.PivotSettings.txtRemove": "Alanı Kaldır", "SSE.Views.PivotSettingsAdvanced.strLayout": "<PERSON>", "SSE.Views.PivotSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>lama", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Görsel obje bilgilerinin alternatif metin tabanlı sunumu görsel veya bilinçsel açıdan problem yaşan kişilere okunarak resimdeki, <PERSON><PERSON><PERSON><PERSON>, grafikteki veya tablodaki bilgileri daha kolay anlamalarını sağlamayı amaçlar.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Başlık", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Veri Aralığı", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON>eri ka<PERSON>ğı", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "<PERSON>or filtre alanında alan<PERSON>ı g<PERSON>", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ra bitti", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "<PERSON>", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON>, sonra aşağı", "SSE.Views.PivotSettingsAdvanced.textSelectData": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Sütunları göster", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Satırlar ve sütunlar için alan başlıklarını göster", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Satırları göster", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot Tablo - Gelişmiş Ayarlar", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON>na rapor filtre al<PERSON>ı", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.PivotSettingsAdvanced.txtName": "Ad", "SSE.Views.PivotTable.capBlankRows": "Boş Satırlar", "SSE.Views.PivotTable.capGrandTotals": "<PERSON><PERSON>", "SSE.Views.PivotTable.capLayout": "<PERSON><PERSON>", "SSE.Views.PivotTable.capSubtotals": "<PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Tüm Alt Toplamları Grubun Altında Göster", "SSE.Views.PivotTable.mniInsertBlankLine": "Her Öğeden Sonra Boş Satır Ekle", "SSE.Views.PivotTable.mniLayoutCompact": "Kompakt Formda Göster", "SSE.Views.PivotTable.mniLayoutNoRepeat": "<PERSON><PERSON>m Öğe Etiketlerini Tekrar Etmeyin", "SSE.Views.PivotTable.mniLayoutOutline": "<PERSON><PERSON>", "SSE.Views.PivotTable.mniLayoutRepeat": "T<PERSON>m Öğe Etiketlerini Tekrarla", "SSE.Views.PivotTable.mniLayoutTabular": "Tabular Formunda Göster ", "SSE.Views.PivotTable.mniNoSubtotals": "Alt Toplamları Gösterme", "SSE.Views.PivotTable.mniOffTotals": "Satırlar ve Sütunlar iç<PERSON>", "SSE.Views.PivotTable.mniOnColumnsTotals": "Yalnızca Sütunlar İçin Açık", "SSE.Views.PivotTable.mniOnRowsTotals": "Yalnızca Satırlar için Açık", "SSE.Views.PivotTable.mniOnTotals": "Satırlar ve Sütunlar için Açık", "SSE.Views.PivotTable.mniRemoveBlankLine": "Her Öğeden Sonra Boş Satırı Kaldır", "SSE.Views.PivotTable.mniTopSubtotals": "Tüm Alt Toplamları Grubun Başında Göster", "SSE.Views.PivotTable.textColBanded": "Bantlı Sütunlar", "SSE.Views.PivotTable.textColHeader": "Sütun Başlıkları", "SSE.Views.PivotTable.textRowBanded": "Bantlı Satırlar", "SSE.Views.PivotTable.textRowHeader": "Satır Başlıkları", "SSE.Views.PivotTable.tipCreatePivot": "Pivot Tablo Ekle", "SSE.Views.PivotTable.tipGrandTotals": "Genel toplamları göster veya gizle", "SSE.Views.PivotTable.tipRefresh": "Veri kaynağından bilgileri güncelleyin", "SSE.Views.PivotTable.tipSelect": "Tüm pivot tabloyu seç", "SSE.Views.PivotTable.tipSubtotals": "Ara toplamları göster veya gizle", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.PivotTable.txtPivotTable": "Pivot Tablo", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtSelect": "Seç", "SSE.Views.PrintSettings.btnDownload": "<PERSON><PERSON>", "SSE.Views.PrintSettings.btnPrint": "Kaydet & Yazdır", "SSE.Views.PrintSettings.strBottom": "Alt", "SSE.Views.PrintSettings.strLandscape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "Sol", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Portre", "SSE.Views.PrintSettings.strPrint": "Yazdır", "SSE.Views.PrintSettings.strPrintTitles": "Başlıkları yazdır", "SSE.Views.PrintSettings.strRight": "Sağ", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "Üst", "SSE.Views.PrintSettings.textActualSize": "G<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textFitCols": "Tüm Sütunları Bir Sayfaya Sığdır", "SSE.Views.PrintSettings.textFitPage": "Yaprağı Bir Sayfaya Sığdır", "SSE.Views.PrintSettings.textFitRows": "Tüm Satırları Bir Sayfaya Sığdır", "SSE.Views.PrintSettings.textHideDetails": "Detayları Gizle", "SSE.Views.PrintSettings.textIgnore": "Yazdırma Alanını Yoksay", "SSE.Views.PrintSettings.textLayout": "Tasarım", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textPageScaling": "Ölçeklendirme", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintGrid": "Kılavuz Çizgilerini Yazdır", "SSE.Views.PrintSettings.textPrintHeadings": "Satır ve Sütun Başlıklarını Yazdır", "SSE.Views.PrintSettings.textPrintRange": "Yazdırma Aralığı", "SSE.Views.PrintSettings.textRange": "Aralık", "SSE.Views.PrintSettings.textRepeat": "Tekra<PERSON>...", "SSE.Views.PrintSettings.textRepeatLeft": "Soldaki sütunları tekrarla", "SSE.Views.PrintSettings.textRepeatTop": "Üstteki satırları tekrarlayın", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Yaprak <PERSON>", "SSE.Views.PrintSettings.textShowDetails": "Detayları Göster", "SSE.Views.PrintSettings.textShowGrid": "Kılavuz Çizgilerini Göster", "SSE.Views.PrintSettings.textShowHeadings": "Satır ve Sütun Başlıklarını Göster", "SSE.Views.PrintSettings.textTitle": "Yaz<PERSON><PERSON>rma <PERSON>", "SSE.Views.PrintSettings.textTitlePDF": "PDF Ayarları", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFirstRow": "İlk sıra", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.PrintTitlesDialog.textLeft": "Soldaki sütunları tekrarla", "SSE.Views.PrintTitlesDialog.textNoRepeat": "tekrar etme", "SSE.Views.PrintTitlesDialog.textRepeat": "Tekra<PERSON>...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "Başlıkları yazdır", "SSE.Views.PrintTitlesDialog.textTop": "Üstteki satırları tekrarlayın", "SSE.Views.PrintWithPreview.txtActualSize": "G<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON><PERSON> tablo<PERSON>", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtBottom": "Alt", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtFitCols": "Tüm Sütunları Bir Sayfaya Sığdır", "SSE.Views.PrintWithPreview.txtFitPage": "Yaprağı Bir Sayfaya Sığdır", "SSE.Views.PrintWithPreview.txtFitRows": "Tüm Satırları Bir Sayfaya Sığdır", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Üst/Alt bilgi ayarları", "SSE.Views.PrintWithPreview.txtPage": "Say<PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Sayfa numarası geçersiz", "SSE.Views.PrintWithPreview.txtPageOrientation": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.textExistName": "HATA! Böyle bir başlığa sahip aralık zaten var", "SSE.Views.ProtectDialog.textInvalidName": "Aralık baş<PERSON><PERSON><PERSON><PERSON> bir harfle başlamalı ve yalnızca harf, sayı ve boşluk içerebilir.", "SSE.Views.ProtectDialog.textInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.ProtectDialog.textSelectData": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtAllow": "<PERSON>u sayfanın tüm kullanıcılarının şun<PERSON>ı yapmasına izin ver:", "SSE.Views.ProtectDialog.txtAutofilter": "Otomatik Filtreyi <PERSON>", "SSE.Views.ProtectDialog.txtDelCols": "Sütunları sil", "SSE.Views.ProtectDialog.txtDelRows": "Satırları sil", "SSE.Views.ProtectDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.ProtectDialog.txtFormatCells": "Hücreleri biçimlendir", "SSE.Views.ProtectDialog.txtFormatCols": "Sütunları biçimlendir", "SSE.Views.ProtectDialog.txtFormatRows": "Satırları biçimlendir", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Onay şifresi aynı değil", "SSE.Views.ProtectDialog.txtInsCols": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtInsHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "SSE.Views.ProtectDialog.txtObjs": "Nesneleri <PERSON>", "SSE.Views.ProtectDialog.txtOptional": "Opsiyonel", "SSE.Views.ProtectDialog.txtPassword": "Şifre", "SSE.Views.ProtectDialog.txtPivot": "PivotTable ve PivotChart'ı kullanın", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Aralık", "SSE.Views.ProtectDialog.txtRangeName": "Başlık", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON> tekrar girin", "SSE.Views.ProtectDialog.txtScen": "Senaryoları düzenle", "SSE.Views.ProtectDialog.txtSelLocked": "<PERSON><PERSON><PERSON> hü<PERSON> se<PERSON>", "SSE.Views.ProtectDialog.txtSelUnLocked": "Kilidi açılmış hücreleri seçin", "SSE.Views.ProtectDialog.txtSheetDescription": "<PERSON><PERSON>zen<PERSON><PERSON> sınırlayarak başkalarının istenmeyen değişiklikler yapmasını önleyin.", "SSE.Views.ProtectDialog.txtSheetTitle": "Sayfayı Koruyun", "SSE.Views.ProtectDialog.txtSort": "S<PERSON>rala", "SSE.Views.ProtectDialog.txtWarning": "Dikkat: Parol<PERSON>ı ka<PERSON>beder veya unutursanız, kurtarılamaz. Lütfen parolayı güvenli bir yerde saklayın.", "SSE.Views.ProtectDialog.txtWBDescription": "<PERSON><PERSON>er kullanıcıların gizli çalışma sayfalarını görüntülemesini, çalışma sayfalarını eklemesini, ta<PERSON><PERSON>masını, silmesini veya gizlemesini ve çalışma sayfalarını yeniden adlandırmasını önlemek için çalışma kitabınızın yapısını bir parola ile koruyabilirsiniz.", "SSE.Views.ProtectDialog.txtWBTitle": "Çalışma Kitabı yapısını koruyun", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Sil", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Aralıkların düzenlenmesine izin verilmiyor.", "SSE.Views.ProtectRangesDlg.textNew": "yeni", "SSE.Views.ProtectRangesDlg.textProtect": "Sayfayı Koruyun", "SSE.Views.ProtectRangesDlg.textPwd": "Şifre", "SSE.Views.ProtectRangesDlg.textRange": "Aralık", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Sayfa korumalıyken parola ile açılan aralıklar (bu yalnızca kilitli hücreler için geçerlidir)", "SSE.Views.ProtectRangesDlg.textTitle": "Başlık", "SSE.Views.ProtectRangesDlg.tipIsLocked": "<PERSON>u öğe başka bir kullanıcı tarafından düzenleniyor.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Aralığı Düzenle", "SSE.Views.ProtectRangesDlg.txtNewRange": "<PERSON>ni <PERSON>", "SSE.Views.ProtectRangesDlg.txtNo": "Hay<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtTitle": "Kullanıcıların Aralıkları Düzenlemesine İzin Ver", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.warnDelete": "{0} adını silmek istediğinizden emin misiniz?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Yinelenen değerleri silmek için yinelenenleri içeren bir veya daha fazla sütun seçin.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Verilerimin başlıkları var", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Yinelenenleri Kaldır", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtParagraphSettings": "Paragraf <PERSON>", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Tablo ayarları", "SSE.Views.RightMenu.txtSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtShapeSettings": "Şekil <PERSON>", "SSE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON>za <PERSON>ı", "SSE.Views.RightMenu.txtSlicerSettings": "Dilimleyici ayarları", "SSE.Views.RightMenu.txtSparklineSettings": "Sparkline Ayarları", "SSE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Yazı Sanatı ayarları", "SSE.Views.ScaleDialog.textAuto": "Otomatik", "SSE.Views.ScaleDialog.textError": "<PERSON><PERSON><PERSON> ya<PERSON>lı<PERSON>.", "SSE.Views.ScaleDialog.textFewPages": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textHeight": "Yükseklik", "SSE.Views.ScaleDialog.textManyPages": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "Say<PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textTitle": "Ölçek Ayarları", "SSE.Views.ScaleDialog.textWidth": "Genişlik", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON> <PERSON><PERSON><PERSON> {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON> al<PERSON> i<PERSON> minimum değer {0}", "SSE.Views.ShapeSettings.strBackground": "Arka plan rengi", "SSE.Views.ShapeSettings.strChange": "Otomatik Şeklini Değiştir", "SSE.Views.ShapeSettings.strColor": "Renk", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Önplan rengi", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "Gölgeleri göster", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Opasite", "SSE.Views.ShapeSettings.strType": "Tip", "SSE.Views.ShapeSettings.textAdvanced": "Gelişmiş ayarları göster", "SSE.Views.ShapeSettings.textAngle": "Açı", "SSE.Views.ShapeSettings.textBorderSizeErr": "Girilen değer yanlış. <br> Lütfen 0 ile 1584 pt arasında değer giriniz.", "SSE.Views.ShapeSettings.textColor": "Renk Dolgusu", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromUrl": "URL'den", "SSE.Views.ShapeSettings.textGradient": "<PERSON>an no<PERSON>ı", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textHint270": "Döndür 90° Saatyönütersi", "SSE.Views.ShapeSettings.textHint90": "Döndür 90° Saatyönü", "SSE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON><PERSON> ya<PERSON>", "SSE.Views.ShapeSettings.textLinear": "Doğrusal", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Pozisyon", "SSE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRotate90": "Döndür 90°", "SSE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "Seç", "SSE.Views.ShapeSettings.textStretch": "Esnet", "SSE.Views.ShapeSettings.textStyle": "Stil", "SSE.Views.ShapeSettings.textTexture": "Doldurma deseninden", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "<PERSON><PERSON> nokt<PERSON> e<PERSON>", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Gradyan noktasını kaldır", "SSE.Views.ShapeSettings.txtBrownPaper": "Kahvereng<PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Koyu Örgü", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtKnit": "Birleştir", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "Çizgi yok", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Ahşap", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Hücre<PERSON>le hareket etmeyin veya boyutlandırmayın", "SSE.Views.ShapeSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "G<PERSON><PERSON>l nesne bi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, otomatik şekil, çizelge veya tabloda hangi bilgilerin olduğunu daha iyi anlamalarına yardımcı olmak için görme veya bilişsel bozukluğu olan kişilere okunacak alternatif metin tabanlı temsili.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Başlık", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Açı", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Otomatik Sığdır", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Başlama Boyutu", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Başlama Stili", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Alt", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Başlık Tipi", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Bitiş Stili", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Düz", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "çevrilmiş", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Yükseklik", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Katılım Tipi", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Sol", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON> stili", "SSE.Views.ShapeSettingsAdvanced.textMiter": "G<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Taş<PERSON> ama hücreleri boyutlandırma", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "<PERSON><PERSON> şekli taşmasına izin ver", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Metne sığdırmak için şekli yeniden boyutlandırın", "SSE.Views.ShapeSettingsAdvanced.textRight": "Sağ", "SSE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Hücre <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Sütunlar arasında boşluk", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Şekil - Gelişmiş Ayarlar", "SSE.Views.ShapeSettingsAdvanced.textTop": "Üst", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Taş<PERSON> ve hücreleri boyutlandır ", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Ağırlık & oklar", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Genişlik", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDelete": "İmzayı kaldır", "SSE.Views.SignatureSettings.strDetails": "İmza Ayrıntıları", "SSE.Views.SignatureSettings.strInvalid": "Geçersiz im<PERSON>ar", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSign": "İmzala", "SSE.Views.SignatureSettings.strSignature": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSigner": "İmzalayan", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.txtEditWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>, imzaları e-tablodan kaldıracak.<br><PERSON><PERSON> edilsin mi?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Bu imzayı kaldırmak istiyor musunuz?<br><PERSON><PERSON>.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Bu elektronik tablonun imzalanması gerekiyor.", "SSE.Views.SignatureSettings.txtSigned": "Elektronik tabloya geçerli imzalar eklendi. Elektronik tablo, düzenlemeye karşı korumalıdır.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Elektronik tablodaki bazı dijital imzalar geçersiz veya doğrulanamadı. Elektronik tablo, düzenlemeye karşı korumalıdır.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SlicerSettings.strHideNoData": "Veri içermeyen öğeleri gizle", "SSE.Views.SlicerSettings.strIndNoData": "Veri içermeyen öğeleri görsel olarak belirtin", "SSE.Views.SlicerSettings.strShowDel": "Veri kaynağından silinen öğeleri gö<PERSON>", "SSE.Views.SlicerSettings.strShowNoData": "Son veris<PERSON> o<PERSON>", "SSE.Views.SlicerSettings.strSorting": "Sıralama ve filtreleme", "SSE.Views.SlicerSettings.textAdvanced": "Gelişmiş Ayarları Göster", "SSE.Views.SlicerSettings.textAsc": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textAZ": "A'dan <PERSON>'ye", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "Yükseklik", "SSE.Views.SlicerSettings.textHor": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textLargeSmall": "en büyüğünden en küçüğüne", "SSE.Views.SlicerSettings.textLock": "Yeniden boyutlandırmayı veya taşımayı devre dışı bırak", "SSE.Views.SlicerSettings.textNewOld": "ye<PERSON><PERSON> eskiye", "SSE.Views.SlicerSettings.textOldNew": "eskiden yeniye", "SSE.Views.SlicerSettings.textPosition": "Pozisyon", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "küçükten büyüğe", "SSE.Views.SlicerSettings.textStyle": "Stil", "SSE.Views.SlicerSettings.textVert": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textWidth": "Genişlik", "SSE.Views.SlicerSettings.textZA": "<PERSON>'den A'ya", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Yükseklik", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Veri içermeyen öğeleri gizle", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Veri içermeyen öğeleri görsel olarak belirtin", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Kaynakça", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Veri kaynağından silinen öğeleri gö<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Ekran başlığı", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Son veris<PERSON> o<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sıralama ve Filtreleme", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stil", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "<PERSON><PERSON> ve <PERSON>", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Genişlik", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Hücre<PERSON>le hareket etmeyin veya boyutlandırmayın", "SSE.Views.SlicerSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>lama", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Görsel obje bilgilerinin alternatif metin tabanlı sunumu görsel veya bilinçsel açıdan problem yaşan kişilere okunarak resimdeki, <PERSON><PERSON><PERSON><PERSON>, grafikteki veya tablodaki bilgileri daha kolay anlamalarını sağlamayı amaçlar.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Başlık", "SSE.Views.SlicerSettingsAdvanced.textAsc": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A'dan <PERSON>'ye", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Formüllerde kullanılacak ad", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Başlık", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "en büyüğünden en küçüğüne", "SSE.Views.SlicerSettingsAdvanced.textName": "Ad", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "ye<PERSON><PERSON> eskiye", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "eskiden yeniye", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Taş<PERSON> ama hücreleri boyutlandırma", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "küçükten büyüğe", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Hücre <PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSort": "S<PERSON>rala", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Kaynak adı", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Dilimleyici - G<PERSON>ş<PERSON>ş <PERSON>", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Taş<PERSON> ve hücreleri boyutlandır ", "SSE.Views.SlicerSettingsAdvanced.textZA": "<PERSON>'den A'ya", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.SortDialog.errorEmpty": "Tüm sıralama ölçütlerinde bir sütun veya satır belirtilmelidir.", "SSE.Views.SortDialog.errorMoreOneCol": "<PERSON><PERSON> fazla s<PERSON>tun <PERSON>.", "SSE.Views.SortDialog.errorMoreOneRow": "<PERSON><PERSON> fazla satır <PERSON>.", "SSE.Views.SortDialog.errorNotOriginalCol": "Seçti<PERSON><PERSON><PERSON>, orijinal seçili aralıkta değil.", "SSE.Views.SortDialog.errorNotOriginalRow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, orijinal seçili aralıkta değil.", "SSE.Views.SortDialog.errorSameColumnColor": "%1, aynı renge göre birden fazla kez sıralanıyor.<br>Yinelenen sıralama ölçütlerini silin ve yeniden deneyin.", "SSE.Views.SortDialog.errorSameColumnValue": "%1, de<PERSON><PERSON><PERSON>e göre bir kereden fazla sıralanıyor.<br>Yinelenen sıralama ölçütlerini silin ve yeniden deneyin.", "SSE.Views.SortDialog.textAdd": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Views.SortDialog.textAsc": "<PERSON><PERSON>", "SSE.Views.SortDialog.textAuto": "Otomatik", "SSE.Views.SortDialog.textAZ": "A'dan <PERSON>'ye", "SSE.Views.SortDialog.textBelow": "Altında", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON><PERSON>i", "SSE.Views.SortDialog.textColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON> sil", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "Seviyeyi aşağı taşı", "SSE.Views.SortDialog.textFontColor": "Yazı Tipi Rengi", "SSE.Views.SortDialog.textLeft": "Sol", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON> fazla s<PERSON>...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON> fazla satır...)", "SSE.Views.SortDialog.textNone": "Hiç<PERSON>i", "SSE.Views.SortDialog.textOptions": "Seçenekler", "SSE.Views.SortDialog.textOrder": "Sipariş Et", "SSE.Views.SortDialog.textRight": "Sağ", "SSE.Views.SortDialog.textRow": "Satır", "SSE.Views.SortDialog.textSort": "S<PERSON>rala", "SSE.Views.SortDialog.textSortBy": "<PERSON>öyle sırala:", "SSE.Views.SortDialog.textThenBy": "<PERSON><PERSON> ta<PERSON>", "SSE.Views.SortDialog.textTop": "Üst", "SSE.Views.SortDialog.textUp": "Seviyeyi yukarı taşı", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "<PERSON>'den A'ya", "SSE.Views.SortDialog.txtInvalidRange": "Geçersiz hücre aralığı.", "SSE.Views.SortDialog.txtTitle": "S<PERSON>rala", "SSE.Views.SortFilterDialog.textAsc": "<PERSON><PERSON> (<PERSON>'da<PERSON>ye)", "SSE.Views.SortFilterDialog.textDesc": "<PERSON><PERSON><PERSON> (<PERSON>'den A'ya)", "SSE.Views.SortFilterDialog.txtTitle": "S<PERSON>rala", "SSE.Views.SortOptionsDialog.textCase": "Büyük küçük harfe duyarlı", "SSE.Views.SortOptionsDialog.textHeaders": "Verilerimin başlıkları var", "SSE.Views.SortOptionsDialog.textLeftRight": "Soldan sağa sırala", "SSE.Views.SortOptionsDialog.textOrientation": "Oryantasyon", "SSE.Views.SortOptionsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortOptionsDialog.textTopBottom": "Yu<PERSON><PERSON>dan a<PERSON>ağı<PERSON> sırala", "SSE.Views.SpecialPasteDialog.textAdd": "ekle", "SSE.Views.SpecialPasteDialog.textAll": "Tümü", "SSE.Views.SpecialPasteDialog.textBlanks": "Boşlukları atla", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON>tun genişlikleri", "SSE.Views.SpecialPasteDialog.textComments": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formüller ve biçimlendirme", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formüller ve sayı biçimleri", "SSE.Views.SpecialPasteDialog.textFormats": "Formatlar", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "Formüller ve sütun genişlikleri", "SSE.Views.SpecialPasteDialog.textMult": "Çoğalt", "SSE.Views.SpecialPasteDialog.textNone": "Hiç<PERSON>i", "SSE.Views.SpecialPasteDialog.textOperation": "İşlem", "SSE.Views.SpecialPasteDialog.textPaste": "Yapıştır", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON>ı<PERSON>", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpoz", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Değerler ve biçimlendirme", "SSE.Views.SpecialPasteDialog.textVNFormat": "Değ<PERSON>ler ve sayı biçimleri ", "SSE.Views.SpecialPasteDialog.textWBorders": "Kenarlıklar ha<PERSON>i", "SSE.Views.Spellcheck.noSuggestions": "Yazım önerisi yok", "SSE.Views.Spellcheck.textChange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON> yoksay", "SSE.Views.Spellcheck.txtAddToDictionary": "Sö<PERSON>l<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtComplete": "<PERSON><PERSON><PERSON>m denetimi tama<PERSON>landı", "SSE.Views.Spellcheck.txtDictionaryLanguage": "sözlük dili", "SSE.Views.Spellcheck.txtNextTip": "<PERSON><PERSON><PERSON> keli<PERSON>ye git", "SSE.Views.Spellcheck.txtSpelling": "Yazım", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON>)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON>)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Say<PERSON>dan <PERSON> kopyala", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ta<PERSON>ı", "SSE.Views.Statusbar.filteredRecordsText": "{0} / {1} kayıt filtrelendi", "SSE.Views.Statusbar.filteredText": "Filtre modu", "SSE.Views.Statusbar.itemAverage": "ORTALAMA", "SSE.Views.Statusbar.itemCopy": "Kopyala", "SSE.Views.Statusbar.itemCount": "Saymak", "SSE.Views.Statusbar.itemDelete": "Sil", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMove": "Taşı", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON>ı<PERSON>", "SSE.Views.Statusbar.itemStatus": "<PERSON><PERSON><PERSON> du<PERSON>u", "SSE.Views.Statusbar.itemSum": "Toplam", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Korumayı kaldır", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Bu isimde çalışma tablosu zaten var.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "<PERSON><PERSON><PERSON> ismi şu karakterleri içeremez: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON>", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON>m <PERSON>ı Seç", "SSE.Views.Statusbar.sheetIndexText": "Sayfa {0}/{1}", "SSE.Views.Statusbar.textAverage": "Ortalama", "SSE.Views.Statusbar.textCount": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textMax": "<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "<PERSON><PERSON>", "SSE.Views.Statusbar.textNoColor": "Renk yok", "SSE.Views.Statusbar.textSum": "Toplam", "SSE.Views.Statusbar.tipAddTab": "Çalışma tablosu ekle", "SSE.Views.Statusbar.tipFirst": "İlk Tabloya Kaydır", "SSE.Views.Statusbar.tipLast": "Son tabloya kaydır", "SSE.Views.Statusbar.tipNext": "Tablo Listesini Sağa Kaydır", "SSE.Views.Statusbar.tipPrev": "Tablo Listesini Sola Kaydır", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "Yakınlaştır", "SSE.Views.Statusbar.tipZoomOut": "Uzaklaştır", "SSE.Views.Statusbar.ungroupSheets": "Sayfaların Gru<PERSON>", "SSE.Views.Statusbar.zoomText": "Ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rma {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Seçilen hücre aralığı için operasyon tamamlanamadı.<br> Tablo içinde yada dışında tekdüze veri aralığı seçin ve tekrar deneyin.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Seçili hücre aralığı için işlem yapılamıyor.<br>İlk tablo satırı aynı satırda olan bir aralık seçilir<br>ve sonuç tablosu mevcut olanın üstüne biner.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Seçili hücre aralığı için işlem yapılamıyor.<br><PERSON><PERSON>er tabloları içeren bir aralık seçemezsiniz.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Tablolarda çok hücreli dizi formüllerine izin verilmez.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "HATA! Geçersiz hücre aralığı", "SSE.Views.TableOptionsDialog.txtNote": "Başlıklar aynı satırda kalmalı ve ortaya çıkan tablo aralığı, orijinal tablo aralığıyla örtüşmelidir.", "SSE.Views.TableOptionsDialog.txtTitle": "Başlık", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteRowText": "Satırı Sil", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertRowAboveText": "Yukarı Satır <PERSON>", "SSE.Views.TableSettings.insertRowBelowText": "Aşağı Satır E<PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "Uyarı", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textAdvanced": "Gelişmiş Ayarları Göster", "SSE.Views.TableSettings.textBanded": "Bağlı", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Aralığa dönüştür", "SSE.Views.TableSettings.textEdit": "Satırlar & Sütunlar", "SSE.Views.TableSettings.textEmptyTemplate": "Şablon yok", "SSE.Views.TableSettings.textExistName": "HATA! Böyle bir aralık zaten mevcut", "SSE.Views.TableSettings.textFilter": "Filtre tuşu", "SSE.Views.TableSettings.textFirst": "İlk", "SSE.Views.TableSettings.textHeader": "Başlık", "SSE.Views.TableSettings.textInvalidName": "HATA! Geçersiz tablo adı", "SSE.Views.TableSettings.textIsLocked": "<PERSON>u öğe başka bir kullanıcı tarafından düzenleniyor.", "SSE.Views.TableSettings.textLast": "Son", "SSE.Views.TableSettings.textLongOperation": "Uzun işlem", "SSE.Views.TableSettings.textPivot": "Pivot tablo ekle", "SSE.Views.TableSettings.textRemDuplicates": "Yinelenenleri kaldır", "SSE.Views.TableSettings.textReservedName": "Kullanmak istediğiniz ad zaten hücre formüllerinde referanslanmış. Lütfen başka bir ad kkullanın.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON><PERSON><PERSON> yeniden <PERSON>landır", "SSE.Views.TableSettings.textRows": "Satırlar", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSlicer": "<PERSON><PERSON><PERSON><PERSON> ekle ", "SSE.Views.TableSettings.textTableName": "Ta<PERSON><PERSON> Adı", "SSE.Views.TableSettings.textTemplate": "Şablondan Seç", "SSE.Views.TableSettings.textTotal": "Toplam", "SSE.Views.TableSettings.warnLongOperation": "Yapmak istediğiniz işlemin tamamlanması uzun sürebilir.<br><PERSON><PERSON> etmek istediğinize emin misiniz?", "SSE.Views.TableSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Görsel obje bilgilerinin alternatif metin tabanlı sunumu görsel veya bilinçsel açıdan problem yaşan kişilere okunarak resimdeki, <PERSON><PERSON><PERSON><PERSON>, grafikteki veya tablodaki bilgileri daha kolay anlamalarını sağlamayı amaçlar.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Başlık", "SSE.Views.TableSettingsAdvanced.textTitle": "Tablo - Gelişmiş Ayarlar", "SSE.Views.TextArtSettings.strBackground": "Background color", "SSE.Views.TextArtSettings.strColor": "Color", "SSE.Views.TextArtSettings.strFill": "Fill", "SSE.Views.TextArtSettings.strForeground": "Foreground color", "SSE.Views.TextArtSettings.strPattern": "Pattern", "SSE.Views.TextArtSettings.strSize": "Size", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Opacity", "SSE.Views.TextArtSettings.strType": "Tip", "SSE.Views.TextArtSettings.textAngle": "Açı", "SSE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Color Fill", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "No Pattern", "SSE.Views.TextArtSettings.textFromFile": "From File", "SSE.Views.TextArtSettings.textFromUrl": "From URL", "SSE.Views.TextArtSettings.textGradient": "<PERSON>an no<PERSON>ı", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "No Fill", "SSE.Views.TextArtSettings.textPatternFill": "Pattern", "SSE.Views.TextArtSettings.textPosition": "Pozisyon", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "Select", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "Style", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "From Texture", "SSE.Views.TextArtSettings.textTile": "Tile", "SSE.Views.TextArtSettings.textTransform": "Transform", "SSE.Views.TextArtSettings.tipAddGradientPoint": "<PERSON><PERSON> nokt<PERSON> e<PERSON>", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Gradyan noktasını kaldır", "SSE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granite", "SSE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "SSE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Leather", "SSE.Views.TextArtSettings.txtNoBorders": "No Line", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnColorSchemas": "Renk uyumu", "SSE.Views.Toolbar.capBtnComment": "<PERSON>rum yap", "SSE.Views.Toolbar.capBtnInsHeader": "Üst/Alt Bilgi", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsSymbol": "<PERSON>m<PERSON>", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Oryantasyon", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Yaz<PERSON><PERSON>rma Alanı", "SSE.Views.Toolbar.capBtnPrintTitles": "Başlıkları yazdır", "SSE.Views.Toolbar.capBtnScale": "Sığdırmak için <PERSON>", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "<PERSON><PERSON>", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON>", "SSE.Views.Toolbar.capImgGroup": "Grup", "SSE.Views.Toolbar.capInsertChart": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertEquation": "Denklem", "SSE.Views.Toolbar.capInsertHyperlink": "Köprü", "SSE.Views.Toolbar.capInsertImage": "Resim", "SSE.Views.Toolbar.capInsertShape": "Şekil", "SSE.Views.Toolbar.capInsertSpark": "Mini grafik", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON><PERSON> resim", "SSE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromUrl": "URL'den resim", "SSE.Views.Toolbar.textAddPrintArea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignBottom": "Alta Hizala", "SSE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON> yana yaslı", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON> Hizala", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON> Hizala", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAuto": "Otomatik", "SSE.Views.Toolbar.textAutoColor": "Otomatik", "SSE.Views.Toolbar.textBold": "Kalı<PERSON>", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "Sınır Stili", "SSE.Views.Toolbar.textBottom": "Alt:", "SSE.Views.Toolbar.textBottomBorders": "Alt Sınırlar", "SSE.Views.Toolbar.textCenterBorders": "Dikey İç Sınırlar", "SSE.Views.Toolbar.textClearPrintArea": "Yazdırma Alanını Temizle", "SSE.Views.Toolbar.textClearRule": "Kuralları Temizle", "SSE.Views.Toolbar.textClockwise": "Saat yönünde açı", "SSE.Views.Toolbar.textColorScales": "Renk Ölçekleri", "SSE.Views.Toolbar.textCounterCw": "Saat yönü tersi açı", "SSE.Views.Toolbar.textDataBars": "Veri Çubukları", "SSE.Views.Toolbar.textDelLeft": "Hücreleri sola kaydır", "SSE.Views.Toolbar.textDelUp": "Hücreleri yukarı kaydır", "SSE.Views.Toolbar.textDiagDownBorder": "Diagonal Down Border", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonal Up Border", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFewPages": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHeight": "Yükseklik", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInsDown": "Hücreleri aşağı kaydır", "SSE.Views.Toolbar.textInsideBorders": "İç <PERSON>ınırlar", "SSE.Views.Toolbar.textInsRight": "Hücreleri sağa kaydır", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "<PERSON><PERSON><PERSON>r", "SSE.Views.Toolbar.textLandscape": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeft": "Sol:", "SSE.Views.Toolbar.textLeftBorders": "Sol Sınırlar", "SSE.Views.Toolbar.textManageRule": "Kuralları Yönet", "SSE.Views.Toolbar.textManyPages": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "<PERSON>", "SSE.Views.Toolbar.textMarginsNarrow": "Dar", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Genişlik", "SSE.Views.Toolbar.textMiddleBorders": "Ya<PERSON>y <PERSON>ınırlar", "SSE.Views.Toolbar.textMoreFormats": "Daha fazla format", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON> fazla sayfa", "SSE.Views.Toolbar.textNewColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "Sınır yok", "SSE.Views.Toolbar.textOnePage": "Say<PERSON>", "SSE.Views.Toolbar.textOutBorders": "Dış Sınırlar", "SSE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON> kenar b<PERSON>", "SSE.Views.Toolbar.textPortrait": "<PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "Yazdır", "SSE.Views.Toolbar.textPrintOptions": "Yaz<PERSON><PERSON>rma <PERSON>", "SSE.Views.Toolbar.textRight": "Sağ:", "SSE.Views.Toolbar.textRightBorders": "Sağ Sınırlar", "SSE.Views.Toolbar.textRotateDown": "Metini Aşağı Döndür", "SSE.Views.Toolbar.textRotateUp": "<PERSON>ini <PERSON> Döndür", "SSE.Views.Toolbar.textScale": "Ölçek", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON>", "SSE.Views.Toolbar.textSelection": "Geç<PERSON><PERSON> se<PERSON>", "SSE.Views.Toolbar.textSetPrintArea": "Yazdırma Alanını Ayarla", "SSE.Views.Toolbar.textStrikeout": "Üstü çizili", "SSE.Views.Toolbar.textSubscript": "Alt Simge", "SSE.Views.Toolbar.textSubSuperscript": "Alt Simge/Üst Simge", "SSE.Views.Toolbar.textSuperscript": "Üst Simge", "SSE.Views.Toolbar.textTabCollaboration": "Ortak Çalışma", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "<PERSON>", "SSE.Views.Toolbar.textTabInsert": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabLayout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textThisPivot": "Bu pivottan", "SSE.Views.Toolbar.textThisSheet": "Bu çalışma <PERSON>fa<PERSON>ından", "SSE.Views.Toolbar.textThisTable": "bu tablodan", "SSE.Views.Toolbar.textTopBorders": "Üst Sınırlar", "SSE.Views.Toolbar.textUnderline": "Altı çizili", "SSE.Views.Toolbar.textVertical": "<PERSON><PERSON>", "SSE.Views.Toolbar.textWidth": "Genişlik", "SSE.Views.Toolbar.textZoom": "Büyütme", "SSE.Views.Toolbar.tipAlignBottom": "Alta Hizala", "SSE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON> yana yaslı", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON>a hizala", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON> Hizala", "SSE.Views.Toolbar.tipAutofilter": "Sırala ve Filtrele", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "Sınırlar", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Renk Şemasını Değiştir", "SSE.Views.Toolbar.tipCondFormat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopy": "Kopyala", "SSE.Views.Toolbar.tipCopyStyle": "St<PERSON>", "SSE.Views.Toolbar.tipDecDecimal": "Ondalık Azalt", "SSE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON> boy<PERSON><PERSON>u a<PERSON>", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleAccounting": "Accounting Style", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "Yüzde Stili", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartData": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartType": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipEditHeader": "Alt veya üst bilgiyi düzenle", "SSE.Views.Toolbar.tipFontColor": "Yazı Tipi Rengi", "SSE.Views.Toolbar.tipFontName": "Yazı Tipi", "SSE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> boyutu", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>a", "SSE.Views.Toolbar.tipImgGroup": "Grup objeleri", "SSE.Views.Toolbar.tipIncDecimal": "Ondalık Arttır", "SSE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON> boyutunu art<PERSON>ır", "SSE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertChartSpark": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertShape": "Otomatik Şekil ekle", "SSE.Views.Toolbar.tipInsertSlicer": "<PERSON><PERSON><PERSON><PERSON> ekle ", "SSE.Views.Toolbar.tipInsertSpark": "Mini grafik ekle", "SSE.Views.Toolbar.tipInsertSymbol": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON> kutusu e<PERSON>", "SSE.Views.Toolbar.tipInsertTextart": "Yazı Sanatı Ekle", "SSE.Views.Toolbar.tipMerge": "B<PERSON><PERSON>ş<PERSON>r ve ortala", "SSE.Views.Toolbar.tipNumFormat": "Sayı Formatı", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON> kenar b<PERSON>ı", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPaste": "Yapıştır", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrint": "Yazdır", "SSE.Views.Toolbar.tipPrintArea": "Yaz<PERSON><PERSON>rma Alanı", "SSE.Views.Toolbar.tipPrintTitles": "Başlıkları yazdır", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> k<PERSON>ıların görmesi için değişikliklerinizi kaydedin.", "SSE.Views.Toolbar.tipScale": "Sığdırmak için <PERSON>", "SSE.Views.Toolbar.tipSendBackward": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSynchronize": "Döküman başka bir kullanıcı tarafından değiştirildi. Lütfen değişikleri kaydetmek için tıklayın ve güncellemeleri yenileyin.", "SSE.Views.Toolbar.tipTextOrientation": "Oryantasyon", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "Ek", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "Toplama", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "Fonksiyon", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearText": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDateTime": "Tarih & Saat", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Üssel", "SSE.Views.Toolbar.txtFilter": "Filtre", "SSE.Views.Toolbar.txtFormula": "Fonksiyon <PERSON>", "SSE.Views.Toolbar.txtFraction": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtFranc": "CHF İsviçre frangı", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtInteger": "Tamsayı", "SSE.Views.Toolbar.txtManageRange": "<PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON> boya bi<PERSON>", "SSE.Views.Toolbar.txtMergeCells": "Hücreleri birleştir", "SSE.Views.Toolbar.txtMergeCenter": "Birleştir & Ortala", "SSE.Views.Toolbar.txtNamedRange": "Named Ranges", "SSE.Views.Toolbar.txtNewRange": "Define Name", "SSE.Views.Toolbar.txtNoBorders": "Sınır yok", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Adı k<PERSON>ala", "SSE.Views.Toolbar.txtPercentage": "<PERSON><PERSON>z<PERSON>", "SSE.Views.Toolbar.txtPound": "£ Sterlin", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON>le", "SSE.Views.Toolbar.txtScheme1": "<PERSON>is", "SSE.Views.Toolbar.txtScheme10": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme15": "Orjin", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme18": "Yöntem", "SSE.Views.Toolbar.txtScheme19": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme2": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme20": "Kentsel", "SSE.Views.Toolbar.txtScheme21": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme22": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme3": "Apex", "SSE.Views.Toolbar.txtScheme4": "Açı", "SSE.Views.Toolbar.txtScheme5": "Kentsel", "SSE.Views.Toolbar.txtScheme6": "Toplama", "SSE.Views.Toolbar.txtScheme7": "<PERSON>", "SSE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme9": "Döküm", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "Ara", "SSE.Views.Toolbar.txtSort": "S<PERSON>rala", "SSE.Views.Toolbar.txtSortAZ": "En düşükten en yükseğe sırala", "SSE.Views.Toolbar.txtSortZA": "En yüksekten en aza sırala", "SSE.Views.Toolbar.txtSpecial": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtTableTemplate": "<PERSON><PERSON><PERSON> Şablonu olarak biçim<PERSON>dir", "SSE.Views.Toolbar.txtText": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtTime": "Zaman", "SSE.Views.Toolbar.txtUnmerge": "Hücreleri Ayır", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "Alt", "SSE.Views.Top10FilterDialog.txtBy": "Tara<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON><PERSON>z<PERSON>", "SSE.Views.Top10FilterDialog.txtSum": "Toplam", "SSE.Views.Top10FilterDialog.txtTitle": "İlk 10 Otomatik Filtre", "SSE.Views.Top10FilterDialog.txtTop": "Üst", "SSE.Views.Top10FilterDialog.txtValueTitle": "En İyi 10 Filtre", "SSE.Views.ValueFieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>ları", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "ORTALAMA", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON> alan", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 / %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Saymak", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON> say", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "<PERSON><PERSON> ad", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Fark", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Yüzdesi", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Yüzde Farkı", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Toplamın Yüzdesi", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Satırın <PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Değerleri şu şek<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Kaynak adı:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Toplam", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON><PERSON>er alanını şuna göre özetle", "SSE.Views.ValueFieldSettingsDialog.txtVar": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Ka<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Sil", "SSE.Views.ViewManagerDlg.textDuplicate": "Çoğalt", "SSE.Views.ViewManagerDlg.textEmpty": "Henüz bir gö<PERSON><PERSON><PERSON><PERSON><PERSON> oluşturulmadı.", "SSE.Views.ViewManagerDlg.textGoTo": "Görüntülemeye git", "SSE.Views.ViewManagerDlg.textLongName": "128 karakterden kısa bir ad girin.", "SSE.Views.ViewManagerDlg.textNew": "yeni", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON><PERSON>ı<PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "Görün<PERSON>m adı boş bırakılmamalıdır.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Görünümü yeniden adlandır", "SSE.Views.ViewManagerDlg.textViews": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.tipIsLocked": "<PERSON>u öğe başka bir kullanıcı tarafından düzenleniyor.", "SSE.Views.ViewManagerDlg.txtTitle": "Sayfa Görünümü Yöneticisi", "SSE.Views.ViewManagerDlg.warnDeleteView": "<PERSON>u anda etkin olan '%1' görünümünü silmeye çalışıyorsunuz.<br><PERSON>u görünüm kapatılıp silinsin mi?", "SSE.Views.ViewTab.capBtnFreeze": "Parçaları Dondur", "SSE.Views.ViewTab.capBtnSheetView": "Sayfa Görünümü", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Her zaman araç <PERSON>", "SSE.Views.ViewTab.textClose": "Ka<PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Sayfa ve durum çubuklarını birleştirin", "SSE.Views.ViewTab.textCreate": "yeni", "SSE.Views.ViewTab.textDefault": "varsay<PERSON>lan", "SSE.Views.ViewTab.textFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFreezeCol": "<PERSON>lk Sütunu Dondur", "SSE.Views.ViewTab.textFreezeRow": "Üst Satırı Dondur", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "Başlıklar", "SSE.Views.ViewTab.textManager": "Yöneticiyi <PERSON>", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON>lmeleri <PERSON>", "SSE.Views.ViewTab.textZeros": "Sıfırları göster", "SSE.Views.ViewTab.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "<PERSON><PERSON> gö<PERSON>ü<PERSON>ümü<PERSON>ü ka<PERSON>t", "SSE.Views.ViewTab.tipCreate": "Sayfa görünümü oluştur", "SSE.Views.ViewTab.tipFreeze": "Parçaları Dondur", "SSE.Views.ViewTab.tipSheetView": "Sayfa Görünümü", "SSE.Views.WBProtection.hintAllowRanges": "Aralıkları düzenlemeye izin ver", "SSE.Views.WBProtection.hintProtectSheet": "Sayfayı koruyun", "SSE.Views.WBProtection.hintProtectWB": "Çalışma kitabını koru", "SSE.Views.WBProtection.txtAllowRanges": "Aralıkları düzenlemeye izin ver", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedText": "<PERSON><PERSON>", "SSE.Views.WBProtection.txtProtectSheet": "Sayfayı Koruyun", "SSE.Views.WBProtection.txtProtectWB": "Çalışma Kitabını Koru", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Say<PERSON>ın korumasını kaldırmak için bir şifre girin", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Korumasız Sayfa", "SSE.Views.WBProtection.txtWBUnlockDescription": "Çalışma kitabının korumasını kaldırmak için bir parola girin", "SSE.Views.WBProtection.txtWBUnlockTitle": "Korumasız Çalışma Sayfası"}