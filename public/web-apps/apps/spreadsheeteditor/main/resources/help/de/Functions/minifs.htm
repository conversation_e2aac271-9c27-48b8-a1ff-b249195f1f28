﻿<!DOCTYPE html>
<html>
	<head>
		<title>MINWENNS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>MINWENNS-Funktion</h1>
			<p>Die Funktion <b>MINWENNS</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den Minimalwert aus Zellen zurück, die mit einem bestimmten Satz Bedingungen oder Kriterien angegeben wurden.</p>
			<p>Die Formelsyntax der Funktion <b>MINWENNS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>MINWENNS(Min_Bereich; Kriterienbereich1; Kriterien1; [Kriterienbereich2; Kriterien2]; ...)</em></b></p> 
            <p style="text-indent: 50px;"><b><em>Min_Bereich</em></b> ist er tatsächliche Zellenbereich, in dem das Minimum ermittelt wird.</p>
            <p style="text-indent: 50px;"><b><em>Kriterienbereich1</em></b> ist der erste gewählte Zellenbereich, der auf <em>Kriterien1</em> getestet wird.</p>
            <p style="text-indent: 50px;"><b><em>Kriterien1</em></b> ist die erste zu erfüllende Bedingung. Sie wird auf <em>Kriterienbereich1</em> angewendet und bestimmt, welche Zellen im <em>Min-Bereich</em> als Minimum ausgewertet werden. Der Wert kann manuell eingegeben werden oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p style="text-indent: 50px;"><b><em>Kriterienbereich2; Kriterien2, ...</em></b> sind zusätzliche Zellenbereiche und ihre jeweiligen Kriterien. Diese Argumente sind optional.</p>
            <p class="note"><b>Hinweis:</b> Sie können Platzhalterzeichen verwenden, wenn Sie Kriterien angeben. Das Fragezeichen &quot;?&quot; kann ein beliebiges einzelnes Zeichen ersetzen und der Stern &quot;*&quot; kann anstelle einer beliebigen Anzahl von Zeichen verwendet werden.</p>
			<p>Anwendung der Funktion <b>MINWENNS</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>MINWENNS</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="MINWENNS-Funktion" src="../images/minifs.png" /></p>
		</div>
	</body>
</html>