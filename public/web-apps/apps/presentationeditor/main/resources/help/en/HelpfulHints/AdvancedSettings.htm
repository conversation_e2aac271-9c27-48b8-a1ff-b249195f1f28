﻿<!DOCTYPE html>
<html>
	<head>
		<title>Advanced Settings of the Presentation Editor</title>
		<meta charset="utf-8" />
		<meta name="description" content="The advanced settings of Presentation Editor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Advanced Settings of the Presentation Editor</h1>
			<p>The <a target="_blank" href="https://www.onlyoffice.com/en/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> allows you to change its advanced settings. To access them, open the <b>File</b> tab on the top toolbar and select the <b>Advanced Settings</b> option.</p>
            <p>The advanced settings are grouped as follows:</p>

            <h3>Editing and saving</h3>
            <ol>
                <li><span class="onlineDocumentFeatures"><b>Autosave</b> is used in the <em>online version</em> to turn on/off automatic saving of changes you make while editing.</span> </li>
                <li><span class="desktopDocumentFeatures"><b>Autorecover</b> is used in the <em>desktop version</em> to turn on/off the option that allows you to automatically recover presentations if the program closes unexpectedly.</span></li>
                <li><b>Show the Paste Options button when the content is pasted</b>. The corresponding icon will appear when you paste content in the presentation.</li>
            </ol>

            <h3>Collaboration</h3>
            <ol>
                <li class="onlineDocumentFeatures">
                    The <b>Co-editing mode</b> subsection allows you to set the preferable mode for seeing changes made to the presentation when working in collaboration.
                    <ul>
                        <li><b>Fast</b> (by default). The users who take part in the presentation co-editing will see the changes in real time once they are made by other users.</li>
                        <li><b>Strict</b>. All the changes made by co-editors will be shown only after you click the <b>Save</b> <div class="icon icon-saveupdate"></div> icon that will notify you about new changes.</li>
                    </ul>
                </li>
                <li><b>Show changes from other users</b>. This feature allows to see changes made by other users in the presentation opened for viewing only in the <b>Live Viewer</b> mode.</li>
            </ol>

            <h3>Proofing</h3>
            <ol>
                <li>The <b>Spell Checking</b> option is used to turn on/off the spell checking.</li>
                <li><b>Ignore words in UPPERCASE</b>. Words typed in capital letters are ignored during the spell checking.</li>
                <li><b>Ignore words with numbers</b>. Words with numbers in them are ignored during the spell checking.</li>
                <li>The <b>AutoCorrect options</b> menu allows you to access the <a href="../UsageInstructions/MathAutoCorrect.htm" onclick="onhyperlinkclick(this)">autocorrect settings</a> such as replacing text as you type, recognizing functions, automatic formatting etc.</li>
            </ol>

            <h3>Workspace</h3>
            <ol>
                <li>The <b>Alignment Guides</b> option is used to turn on/off alignment guides that appear when you move objects. It allows for a more precise object positioning on the slide.</li>
                <li>The <b>Hieroglyphs</b> option is used to turn on/off the display of hieroglyphs.</li>
                <li>The <b>Use Alt key to navigate the user interface using the keyboard</b> option is used to enable using the <em>Alt</em> / <em>Option</em>key in keyboard shortcuts.</li>
                <li>
                    The <b>Interface theme</b> option is used to change the color scheme of the editor’s interface.
                    <ul>
                        <li>The <b>Same as system</b> option makes the editor follow the interface theme of your system.</li>
                        <li>The <b>Light</b> color scheme incorporates standard blue, white, and light gray colors with less contrast in UI elements suitable for working during daytime.</li>
                        <li>The <b>Classic Light</b> color scheme incorporates standard blue, white, and light gray colors.</li>
                        <li>The <b>Dark</b> color scheme incorporates black, dark gray, and light gray colors suitable for working during nighttime.</li>
                        <li>
                            The <b>Contrast Dark</b> color scheme incorporates black, dark gray, and white colors with more contrast in UI elements highlighting the working area of the file.
                            <p class="note"><b>Note</b>: Apart from the available <b>Light</b>, <b>Classic Light</b>, <b>Dark</b>, and <b>Contrast Dark</b> interface themes, ONLYOFFICE editors can now be customized with your own color theme. Please follow <a target="_blank" href="https://helpcenter.onlyoffice.com/installation/docs-developer-change-theme.aspx" onclick="onhyperlinkclick(this)">these instructions</a> to learn how you can do that.</p>
                        </li>
                    </ul>
                </li>
                <li>The <b>Unit of Measurement</b> option is used to specify what units are used on the rulers and in properties of objects when setting such parameters as width, height, spacing, margins etc. The available units are <em>Centimeter</em>, <em>Point</em>, and <em>Inch</em>.</li>
                <li>The <b>Default Zoom Value</b> option is used to set the default zoom value, selecting it in the list of available options from 50% to 500%. You can also choose the <em>Fit to Slide</em> or <em>Fit to Width</em> option.</li>
                <li>
                    The <b>Font Hinting</b> option is used to select how fonts are displayed in the Presentation Editor.
                    <ul>
                        <li>Choose <b>As Windows</b> if you like the way fonts are usually displayed on Windows, i.e. using Windows font hinting.</li>
                        <li>Choose <b>As OS X</b> if you like the way fonts are usually displayed on a Mac, i.e. without any font hinting at all.</li>
                        <li>Choose <b>Native</b> if you want your text to be displayed with the hinting embedded into font files.</li>
                        <li>
                            <b>Default cache mode</b> - used to select the cache mode for the font characters. It’s not recommended to switch it without any reason. It can be helpful in some cases only, for example, when the Google Chrome browser has problems with the enabled hardware acceleration.
                            <p>The Presentation Editor has two cache modes:</p>
                            <ol>
                                <li>In the <b>first cache mode</b>, each letter is cached as a separate picture.</li>
                                <li>In the <b>second cache mode</b>, a picture of a certain size is selected where letters are placed dynamically and a mechanism of allocating/removing memory in this picture is also implemented. If there is not enough memory, a second picture is created, etc.</li>
                            </ol>
                            <p>The <b>Default cache mode</b> setting applies two above mentioned cache modes separately for different browsers:</p>
                            <ul>
                                <li>When the <b>Default cache mode</b> setting is enabled, Internet Explorer (v. 9, 10, 11) uses the <b>second cache mode</b>, other browsers use the <b>first cache mode</b>.</li>
                                <li>When the <b>Default cache mode</b> setting is disabled, Internet Explorer (v. 9, 10, 11) uses the <b>first cache mode</b>, other browsers use the <b>second cache mode</b>.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    The <b>Macros Settings</b> option is used to set macros display with a notification.
                    <ul>
                        <li>Choose <b>Disable All</b> to disable all macros within the presentation.</li>
                        <li>Choose <b>Show Notification</b> to receive notifications about macros within the presentation.</li>
                        <li>Choose <b>Enable All</b> to automatically run all macros within the presentation.</li>
                    </ul>
                </li>
            </ol>
			<p>To save the changes you made, click the <b>Apply</b> button.</p>
		</div>
	</body>
</html>