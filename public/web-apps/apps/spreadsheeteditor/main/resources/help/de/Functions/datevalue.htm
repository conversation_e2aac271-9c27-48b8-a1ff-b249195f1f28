﻿<!DOCTYPE html>
<html>
	<head>
		<title>DATWERT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>DATWERT-Funktion</h1>
			<p>Die Funktion <b>DATWERT</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Die Funktion wandelt eine als Text vorliegende Zeitangabe in eine fortlaufende Zahl um.</p>
			<p>Die Formelsyntax der Funktion <b>DATWERT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>DATWERT(Datumstext)</em></b></p> 
			<p>Dabei ist <b><em>Datumstext</em></b> ein Text, der ein Datum zwischen dem 1. Januar 1900 und dem 31.12 9999 darstellt einem Excel-Datumsformat darstellt und der manuelle eingegeben oder durch Bezugnahme in die Formel aufgenommen wird.</p>
			<p><b>DATWERT</b>-Funktion eingeben.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>DATWERT</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="DATWERT-Funktion" src="../images/datevalue.png" /></p>
		</div>
	</body>
</html>