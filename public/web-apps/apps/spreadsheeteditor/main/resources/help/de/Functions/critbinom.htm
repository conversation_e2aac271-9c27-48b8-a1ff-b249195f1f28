﻿<!DOCTYPE html>
<html>
	<head>
		<title>KRITBINOM-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>KRITBINOM-Funktion</h1>
			<p>Die Funktion <b>KRITBINOM</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den kleinsten Wert zurück, für den die kumulierten Wahrscheinlichkeiten der Binomialverteilung größer oder gleich dem angegebenen Alpha-Wert sind.</p>
			<p>Die Formelsyntax der Funktion <b>KRITBINOM</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>KRITBINOM(Versuche;Erfolgswahrsch;Alpha)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Versuche</em></b> ist die Anzahl der Bernoulliexperimente. Ein nummerischer Wert, der größer oder gleich 0 ist.</p>
				<p style="text-indent: 50px;"><b><em>Erfolgswahrsch</em></b> ist die Wahrscheinlichkeit eines Erfolgs für jeden Versuch. Ein nummerischer Wert, größer oder gleich 0 und kleiner oder gleich 1.</p>
				<p style="text-indent: 50px;"><b><em>Alpha</em></b> ist das Kriterium. Ein nummerischer Wert, der größer oder gleich 0 ist und kleiner oder gleich 1.</p>
				<p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>KRITBINOM</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>KRITBINOM</b>:</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="KRITBINOM-Funktion" src="../images/critbinom.png" /></p>
		</div>
	</body>
</html>