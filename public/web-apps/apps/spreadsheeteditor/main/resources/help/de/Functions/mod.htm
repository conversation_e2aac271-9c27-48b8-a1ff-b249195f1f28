﻿<!DOCTYPE html>
<html>
	<head>
		<title>REST-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>REST-Funktion</h1>
			<p>Die Funktion <b>REST</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie gibt den Rest einer Division zurück. Das Ergebnis hat dasselbe Vorzeichen wie Divisor.</p>
			<p>Die Formelsyntax der Funktion <b>REST</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>REST(Zahl;Divisor)</em></b></p> 
			<p>Dabei gilt:</p>
				<p style="text-indent: 50px;"><b><em>Zahl</em></b> die Zahl, für die der Rest einer Division gesucht wird.</p> 
				<p style="text-indent: 50px;"><b><em>Divisor</em></b> ist die Zahl, durch die dividiert wird.</p> 
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p class="note"><b>Hinweis</b>: Ist <b>Divisor</b> gleich <b>0</b>, gibt die Funktion den Fehlerwert <b>#DIV/0!</b> zurück.</p> 
			<p>Anwendung der Funktion <b>REST</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>REST</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="REST-Funktion" src="../images/mod.png" /></p>
		</div>
	</body>
</html>