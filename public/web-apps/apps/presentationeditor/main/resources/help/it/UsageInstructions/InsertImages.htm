﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert and adjust images</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add an image to your presentation and adjust its size and position." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert and adjust images</h1>
            <h3>Insert an image</h3>
			<p>In Presentation Editor, you can insert images in the most popular formats into your presentation. The following image formats are supported: <b>BMP</b>, <b>GIF</b>, <b>JPEG</b>, <b>JPG</b>, <b>PNG</b>.</p>
			<p>To <b>add</b> an image on a slide,</p>
			<ol>
				<li>in the slide list on the left, select the slide you want to add the image to,</li>
				<li>click the <div class = "icon icon-image"></div> <b>Image</b> icon at the <b>Home</b> or <b>Insert</b> tab of the top toolbar,</li>
				<li>select one of the following options to load the image:
					<ul>
						<li>the <b>Image from File</b> option will open the standard dialog window for file selection. Browse your computer hard disk drive for the necessary file and click the <b>Open</b> button</li>
						<li>the <b>Image from URL</b> option will open the window where you can enter the necessary image web address and click the <b>OK</b> button</li>
                        <li class="onlineDocumentFeatures"> the <b>Image from Storage</b> option will open the <b>Select data source</b> window. Select an image stored on your portal and click the <b>OK</b> button</li>
					</ul>
				</li>
				<li>once the image is added you can <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">change its size and position</a>.</li>
			</ol>
            <p>You can also add an image into a text placeholder pressing the <span class="icon icon-placeholder_imagefromfile"></span> <b>Image from file</b> in it and selecting the necessary image stored on your PC, or use the <span class="icon icon-placeholder_imagefromurl"></span> <b>Image from URL</b> button and specify the image URL address:</p>
            <p><img alt="Add image to placeholder" src="../images/placeholder_object.png" /></p>	
            <p>It's also possible to add an image to a slide layout. To learn more, please refer to this <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">article</a>.</p>
			<hr />
            <h3>Adjust image settings</h3>
			<p>The right sidebar is activated when you left-click an image and choose the <b>Image settings</b> <span class="icon icon-image_settings_icon"></span> icon on the right. It contains the following sections:</p>
            <img alt="Image Settings tab" src="../images/imagesettingstab.png" />
            <p><b>Size</b> - is used to view the current image <b>Width</b> and <b>Height</b> or restore the image <b>Actual Size</b> if necessary.</p>
			
            <p>The <b>Crop</b> button is used to crop the image. Click the <b>Crop</b> button to activate cropping handles which appear on the image corners and in the center of each its side. Manually drag the handles to set the cropping area. You can move the mouse cursor over the cropping area border so that it turns into the <span class="icon icon-arrow"></span> icon and drag the area. </p>
            <ul>
                <li>To crop a single side, drag the handle located in the center of this side.</li>
                <li>To simultaneously crop two adjacent sides, drag one of the corner handles.</li>
                <li>To equally crop two opposite sides of the image, hold down the <em>Ctrl</em> key when dragging the handle in the center of one of these sides. </li>
                <li>To equally crop all sides of the image, hold down the <em>Ctrl</em> key when dragging any of the corner handles.</li>
            </ul>
            <p>When the cropping area is specified, click the <b>Crop</b> button once again, or press the <em>Esc</em> key, or click anywhere outside of the cropping area to apply the changes.</p>
            <p>After the cropping area is selected, it's also possible to use the <b>Fill</b> and <b>Fit</b> options available from the <b>Crop</b> drop-down menu. Click the <b>Crop</b> button once again and select the option you need: </p>
            <ul>
                <li>If you select the <b>Fill</b> option, the central part of the original image will be preserved and used to fill the selected cropping area, while other parts of the image will be removed.</li>
                <li>If you select the <b>Fit</b> option, the image will be resized so that it fits the cropping area height or width. No parts of the original image will be removed, but empty spaces may appear within the selected cropping area.</li>
            </ul>
            <p><b>Replace Image</b> - is used to load another image instead of the current one selecting the desired source. You can select one of the options: <b>From File</b> or <b>From URL</b>. The <b>Replace image</b> option is also available in the right-click menu.</p>
            <p><b>Rotation</b> is used to rotate the image by 90 degrees clockwise or counterclockwise as well as to flip the image horizontally or vertically. Click one of the buttons:</p>
            <ul>
                <li><div class = "icon icon-rotatecounterclockwise"></div> to rotate the image by 90 degrees counterclockwise</li>
                <li><div class = "icon icon-rotateclockwise"></div> to rotate the image by 90 degrees clockwise</li>
                <li><div class = "icon icon-fliplefttoright"></div> to flip the image horizontally (left to right)</li>
                <li><div class = "icon icon-flipupsidedown"></div> to flip the image vertically (upside down)</li>
            </ul>
            <p>When the image is selected, the <b>Shape settings</b> <span class="icon icon-shape_settings_icon"></span> icon is also available on the right. You can click this icon to open the <b>Shape settings</b> tab at the right sidebar and adjust the shape <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)"><b>Stroke</b></a> type, size and color as well as change the shape type selecting another shape from the <b>Change Autoshape</b> menu. The shape of the image will change correspondingly.</p>
            <p>At the <b>Shape Settings</b> tab, you can also use the <b>Show shadow</b> option to add a shadow to the image.</p>
            <p><img alt="Shape Settings tab" src="../images/right_image_shape.png" /></p>
			<hr />
			<p>To change the <b>advanced settings</b> of the image, right-click the image and select the <b>Image Advanced Settings</b> option from the contextual menu or left-click the image and press the <b>Show advanced settings</b> link at the right sidebar. The image properties window will be opened:</p>
            <p><img alt="Image Properties" src="../images/image_properties.png" /></p>
			<p>The <b>Placement</b> tab allows you to set the following image properties:</p>
			<ul>
				<li><b>Size</b> - use this option to change the image width and/or height. If the <b>Constant proportions</b> <div class = "icon icon-constantproportions"></div> button is clicked (in this case it looks like this <div class = "icon icon-constantproportionsactivated"></div>), the width and height will be changed together preserving the original image aspect ratio. To restore the actual size of the added image, click the <b>Actual Size</b> button.</li>
				<li><b>Position</b> - use this option to change the image position on the slide (the position is calculated from the top and the left side of the slide).</li>
			</ul>
            <p><img alt="Image Properties: Rotation" src="../images/image_properties2.png" /></p>
            <p>The <b>Rotation</b> tab contains the following parameters:</p>
            <ul>
                <li><b>Angle</b> - use this option to rotate the image by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. </li>
                <li><b>Flipped</b> - check the <b>Horizontally</b> box to flip the image horizontally (left to right) or check the <b>Vertically</b> box to flip the image vertically (upside down).</li>
            </ul>
            <p><img alt="Image Properties" src="../images/image_properties1.png" /></p>
            <p>The <b>Alternative Text</b> tab allows to specify a <b>Title</b> and <b>Description</b> which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image.</p>			
			<hr />
			<p>To <b>delete</b> the inserted image, left-click it and press the <b>Delete</b> key on the keyboard.</p>
			<p>To learn how to <b>align</b> an image on the slide or <b>arrange</b> several images, refer to the <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">Align and arrange objects on a slide</a> section.</p>
		</div>
	</body>
</html>