﻿<!DOCTYPE html>
<html>
	<head>
		<title>VORZEICHEN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>VORZEICHEN-Funktion</h1>
			<p>Die Funktion <b>VORZEICHEN</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie gibt das Vorzeichen einer Zahl zurück. Ist die Zahl positiv, gibt die Funktion den Wert <b>1</b> zurück. Ist die Zahl negativ, gibt die Funktion den Wert <b>-1</b> zurück. Ist die Zahl <b>0</b>, gibt die Funktion den Wert <b>0</b> zurück.</p>
			<p>Die Formelsyntax der Funktion <b>VORZEICHEN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>VORZEICHEN(Zahl)</em></b></p> 
			<p>Dabei ist <b><em>Zahl</em></b> ein Zahlenwert der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>VORZEICHEN</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>VORZEICHEN</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p><em>Beispiel:</em></p>
			<p>Das gewünschte Argument ist <b>A1</b>, wobei <b>A1</b> gleich <b>12</b> ist. Die Zahl ist positiv, also gibt die Funktion den Wert <b>1</b> zurück.</p>
			<p style="text-indent: 150px;"><img alt="VORZEICHEN-Funktion: POSITIV" src="../images/signpositive.png" /></p>
			<p>Ändert man den Wert <b>A1</b> von <b>12</b> auf <b>-12</b>, gibt die Funktion den Wert <b>-1</b> zurück:</p>
			<p style="text-indent: 150px;"><img alt="VORZEICHEN-Funktion: NEGATIV" src="../images/signnegative.png" /></p>
		</div>
	</body>
</html>