﻿<!DOCTYPE html>
<html>
	<head>
		<title>QUARTILE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>QUARTILE-Funktion</h1>
			<p>Die Funktion <b>QUARTILE</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt die Quartile der Datengruppe zurück</p>
			<p>Die Formelsyntax der Funktion <b>QUARTILE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>QUARTILE(Matrix;Quartile)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Matrix</em></b> ist eine Matrix oder ein Zellbereich numerischer Werte, deren Quartile Sie bestimmen möchten.</p>
				<p style="text-indent: 50px;"><b><em>Quartile</em></b> gibt an, welcher Wert ausgegeben werden soll. Ein nummerischer wert, der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen. Mögliche Werte für Quartile:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Zahlenwert</b></td>
					<td><b>Rückgabe durch Quartile</b></td>
				</tr>
				<tr>
					<td>0</td>
					<td>Minimalwert</td>
				</tr>
				<tr>
					<td>1</td>
					<td>Das untere Quartil (25%-Quantil)</td>
				</tr>
				<tr>
					<td>2</td>
					<td>Den Median (50%-Quantil)</td>
				</tr>
				<tr>
					<td>3</td>
					<td>Das obere Quartil (75%-Quantil)</td>
				</tr>
				<tr>
					<td>4</td>
					<td>Maximalwert</td>
				</tr>
				
			</table>
			<p>Anwenden der Funktion <b>QUARTILE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>QUARTILE</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="QUARTILE-Funktion" src="../images/quartile.png" /></p>
		</div>
	</body>
</html>