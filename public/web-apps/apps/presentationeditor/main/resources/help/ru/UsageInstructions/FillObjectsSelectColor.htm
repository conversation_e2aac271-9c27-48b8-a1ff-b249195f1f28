<!DOCTYPE html>
<html>
	<head>
		<title>Заливка объектов и выбор цветов</title>
		<meta charset="utf-8" />
		<meta name="description" content="Заливайте добавленные объекты, используя цвет, изображение или текстуру, выбирайте цвета для фона слайда, заливки и обводки автофигуры, шрифта." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Заливка объектов и выбор цветов</h1>
			<p>Можно использовать различные заливки для фона слайда, автофигур и шрифта объектов Text Art.</p>
			<ol>
			<li>Выберите объект
                <ul>
                    <li>Чтобы изменить заливку фона слайда, выделите нужные слайды в списке слайдов. На правой боковой панели будет активирована вкладка <div class = "icon icon-slide_settings_icon"></div> <b>Параметры слайда</b>.</li>
                    <li>Чтобы изменить заливку автофигуры, щелкните по нужной автофигуре левой кнопкой мыши. На правой боковой панели будет активирована вкладка <div class = "icon icon-shape_settings_icon"></div> <b>Параметры фигуры</b>.</li>
                    <li>Чтобы изменить заливку шрифта объекта Text Art, выделите нужный текстовый объект. На правой боковой панели будет активирована вкладка <div class = "icon icon-textart_settings_icon"></div> <b>Параметры объектов Text Art</b>.</li>
                </ul>
			</li>
			<li>Определите нужный тип заливки</li>
			<li>Настройте свойства выбранной заливки (подробное описание для каждого типа заливки смотрите ниже)
			<p class="note"><b>Примечание</b>: для автофигур и шрифта объектов Text Art, независимо от выбранного типа заливки, можно также задать уровень <b>Непрозрачности</b>, перетаскивая ползунок или вручную вводя значение в процентах. Значение, заданное по умолчанию, составляет <b>100%</b>. Оно соответствует полной непрозрачности. Значение <b>0%</b> соответствует полной прозрачности.</p>
			</li>
			</ol>			
			<p><b>Доступны следующие типы заливки:</b></p>			
			<ul>
				<li><b>Заливка цветом</b> - выберите эту опцию, чтобы задать сплошной цвет, которым требуется заполнить внутреннее пространство выбранной фигуры или слайда.
				<p><img alt="Заливка цветом" src="../images/fill_color.png" /></p>
				    <p>Нажмите на цветной прямоугольник, расположенный ниже, и выберите нужный цвет из доступных наборов цветов или задайте любой цвет, который вам нравится:</p>
			        <p><img alt="Палитра" src="../images/palettes.png" /></p>
			        <ul>
			        <li><b>Цвета темы</b> - цвета, соответствующие выбранной теме/цветовой схеме презентации. Как только вы примените какую-то другую тему или цветовую схему, набор <b>Цвета темы</b> изменится.</li>
			        <li><b>Стандартные цвета</b> - набор стандартных цветов.</li>
			        <li><b>Пользовательский цвет</b> - щелкните по этой надписи, если в доступных палитрах нет нужного цвета. Выберите нужный цветовой диапазон, перемещая вертикальный ползунок цвета, и определите конкретный цвет, перетаскивая инструмент для выбора цвета внутри большого квадратного цветового поля. Как только Вы выберете какой-то цвет, в полях справа отобразятся соответствующие цветовые значения RGB и sRGB. Также можно задать цвет на базе цветовой модели RGB, введя нужные числовые значения в полях <b>R</b>, <b>G</b>, <b>B</b> (красный, зеленый, синий), или указать шестнадцатеричный код sRGB в поле, отмеченном знаком <b>#</b>. Выбранный цвет появится в окне предпросмотра <b>Новый</b>. Если к объекту был ранее применен какой-то пользовательский цвет, этот цвет отображается в окне <b>Текущий</b>, так что вы можете сравнить исходный и измененный цвета. Когда цвет будет задан, нажмите на кнопку <b>Добавить</b>:
			        <p><img alt="Палитра - Пользовательский цвет" src="../../../../../../common/main/resources/help/ru/images/palette_custom.png" /></p>	
			        <p>Пользовательский цвет будет применен к объекту и добавлен в палитру <b>Пользовательский цвет</b>.</p>		        
			        </li>			        
			        </ul>
			        <p class="note"><b>Примечание</b>: такие же типы цветов можно использовать при выборе <b>цвета обводки автофигуры</b>, настройке <b>цвета шрифта</b> или изменении <b>цвета фона или границ таблицы</b>.</p>
				</li>
			</ul>
				<hr />
			<ul>				
				<li><b>Градиентная заливка</b> - выберите эту опцию, чтобы залить слайд или фигуру двумя цветами, плавно переходящими друг в друга.
				<p><img class="floatleft"alt="Градиентная заливка" src="../images/fill_gradient.png" /></p>
				<ul style="margin-left: 280px;">
					<li><b>Стиль</b> - выберите один из доступных вариантов: <b>Линейный</b> (цвета изменяются по прямой, то есть по горизонтальной/вертикальной оси или по диагонали под углом 45 градусов) или <b>Радиальный</b> (цвета изменяются по кругу от центра к краям).</li>
					<li><b>Направление</b> - в окне предварительного просмотра направления отображается выбранный цвет градиента. Нажмите на стрелку, чтобы выбрать шаблон из меню. Если выбран <b>Линейный</b> градиент, доступны следующие направления : из левого верхнего угла в нижний правый, сверху вниз, из правого верхнего угла в нижний левый, справа налево, из правого нижнего угла в верхний левый, снизу вверх, из левого нижнего угла в верхний правый, слева направо. Если выбран <b>Радиальный</b> градиент, доступен только один шаблон.</li>
					<li><b>Угол</b> - установите числовое значение для точного угла перехода цвета.</li>
					<li>
						<b>Точка градиента</b> - это определенная точка перехода от одного цвета к другому.
						<ol>
							<li>Чтобы добавить точку градиента, Используйте кнопку <div class = "icon icon-addgradientpoint"></div> <b>Добавить точку градиента</b> или ползунок. Вы можете добавить до 10 точек градиента. Каждая следующая добавленная точка градиента никоим образом не повлияет на внешний вид текущей градиентной заливки. Чтобы удалить определенную точку градиента, используйте кнопку  <div class = "icon icon-removegradientpoint"></div> <b>Удалить точку градиента</b>.</li>
							<li>Чтобы изменить положение точки градиента, используйте ползунок или укажите <b>Положение</b> в процентах для точного местоположения.</li>
							<li>Чтобы применить цвет к точке градиента, щелкните точку на панели ползунка, а затем нажмите <b>Цвет</b>, чтобы выбрать нужный цвет.</li>
						</ol>
					</li>
				</ul>			
				</li>
			</ul>
				<hr />
			<ul>	
				<li><b>Изображение или текстура</b> - выберите эту опцию, чтобы использовать в качестве фона фигуры или слайда изображение или предустановленную текстуру.
				<p><img class="floatleft"alt="Заливка с помощью изображения или текстуры" src="../images/fill_picture.png" /></p>    
				    <ul style="margin-left: 280px;">
				    <li>Если Вы хотите использовать изображение в качестве фона фигуры или слайда, нажмите кнопку <b>Выбрать изображение</b> и добавьте изображение <b>Из файла</b>, выбрав его на жестком диске компьютера, или <b>По URL</b>, вставив в открывшемся окне соответствующий URL-адрес, или <b>Из хранилища</b>, выбрав нужное изображение, сохраненное на портале.
				    </li>
				    <li>Если Вы хотите использовать текстуру в качестве фона фигуры или слайда, разверните меню <b>Из текстуры</b> и выберите нужную предустановленную текстуру.
				    <p>В настоящее время доступны следующие текстуры: Холст, Картон, Темная ткань, Песок, Гранит, Серая бумага, Вязание, Кожа, Крафт-бумага, Папирус, Дерево.</p>
				    </li>
				    </ul>
			        <ul style="margin-left: 280px;">
			        <li>В том случае, если выбранное <b>изображение</b> имеет большие или меньшие размеры, чем автофигура или слайд, можно выбрать из выпадающего списка параметр <b>Растяжение</b> или <b>Плитка</b>.
			        <p>Опция <b>Растяжение</b> позволяет подогнать размер изображения под размер слайда или автофигуры, чтобы оно могло полностью заполнить пространство.</p>
			        <p>Опция <b>Плитка</b> позволяет отображать только часть большего изображения, сохраняя его исходные размеры, или повторять меньшее изображение, сохраняя его исходные размеры, по всей площади слайда или автофигуры, чтобы оно могло полностью заполнить пространство.</p>
			        <p class="note"><b>Примечание</b>: любая выбранная предустановленная <b>текстура</b> полностью заполняет пространство, но в случае необходимости можно применить эффект <b>Растяжение</b>.</p>
			        </li>
			        </ul>			
				</li>
			</ul>
				<hr />
			<ul>
				<li><b>Узор</b> - выберите эту опцию, чтобы залить слайд или фигуру с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами.
				<p><img class="floatleft"alt="Заливка с помощью узора" src="../images/fill_pattern.png" /></p>
				    <ul style="margin-left: 280px;">
				        <li><b>Узор</b> - выберите один из готовых рисунков в меню.</li>
				        <li><b>Цвет переднего плана</b> - нажмите на это цветовое поле, чтобы изменить цвет элементов узора.</li>
				        <li><b>Цвет фона</b> - нажмите на это цветовое поле, чтобы изменить цвет фона узора.</li>
				    </ul>			
				</li>
			</ul>
				<hr />
			<ul>
				<li><b>Без заливки</b> - выберите эту опцию, если Вы вообще не хотите использовать заливку.</li>
			</ul>
		</div>
	</body>
</html>