﻿<!DOCTYPE html>
<html>
	<head>
		<title>WIEDERHOLEN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WIEDERHOLEN-Funktion</h1>
			<p>Die Funktion <b>WIEDERHOLEN</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie wiederholt einen Text so oft wie angegeben.</p>
			<p>Die Formelsyntax der Funktion <b>WIEDERHOLEN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>WIEDERHOLEN(Text;Multiplikator)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Text</em></b> ist der Text, den Sie wiederholen möchten.</p> 
			<p style="text-indent: 50px;"><b><em>Multiplikator</em></b> ist eine positive Zahl, die angibt, wie oft die Wiederholung erfolgen soll.</p>
			<p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>WIEDERHOLEN</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WIEDERHOLEN</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="WIEDERHOLEN-Funktion" src="../images/rept.png" /></p>
		</div>
	</body>
</html>