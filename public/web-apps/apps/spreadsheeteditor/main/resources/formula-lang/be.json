{"DATE": "ДАТА", "DATEDIF": "РАЗНДАТ", "DATEVALUE": "ДАТАЗНАЧ", "DAY": "ДЕНЬ", "DAYS": "ДНИ", "DAYS360": "ДНЕЙ360", "EDATE": "ДАТАМЕС", "EOMONTH": "КОНМЕСЯЦА", "HOUR": "ЧАС", "ISOWEEKNUM": "НОМНЕДЕЛИ.ISO", "MINUTE": "МИНУТЫ", "MONTH": "МЕСЯЦ", "NETWORKDAYS": "ЧИСТРАБДНИ", "NETWORKDAYS.INTL": "ЧИСТРАБДНИ.МЕЖД", "NOW": "ТДАТА", "SECOND": "СЕКУНДЫ", "TIME": "ВРЕМЯ", "TIMEVALUE": "ВРЕМЗНАЧ", "TODAY": "СЕГОДНЯ", "WEEKDAY": "ДЕНЬНЕД", "WEEKNUM": "НОМНЕДЕЛИ", "WORKDAY": "РАБДЕНЬ", "WORKDAY.INTL": "РАБДЕНЬ.МЕЖД", "YEAR": "ГОД", "YEARFRAC": "ДОЛЯГОДА", "BESSELI": "БЕССЕЛЬ.I", "BESSELJ": "БЕССЕЛЬ.J", "BESSELK": "БЕССЕЛЬ.K", "BESSELY": "БЕССЕЛЬ.Y", "BIN2DEC": "ДВ.В.ДЕС", "BIN2HEX": "ДВ.В.ШЕСТН", "BIN2OCT": "ДВ.В.ВОСЬМ", "BITAND": "БИТ.И", "BITLSHIFT": "БИТ.СДВИГЛ", "BITOR": "БИТ.ИЛИ", "BITRSHIFT": "БИТ.СДВИГП", "BITXOR": "БИТ.ИСКЛИЛИ", "COMPLEX": "КОМПЛЕКСН", "CONVERT": "ПРЕОБР", "DEC2BIN": "ДЕС.В.ДВ", "DEC2HEX": "ДЕС.В.ШЕСТН", "DEC2OCT": "ДЕС.В.ВОСЬМ", "DELTA": "ДЕЛЬТА", "ERF": "ФОШ", "ERF.PRECISE": "ФОШ.ТОЧН", "ERFC": "ДФОШ", "ERFC.PRECISE": "ДФОШ.ТОЧН", "GESTEP": "ПОРОГ", "HEX2BIN": "ШЕСТН.В.ДВ", "HEX2DEC": "ШЕСТН.В.ДЕС", "HEX2OCT": "ШЕСТН.В.ВОСЬМ", "IMABS": "МНИМ.ABS", "IMAGINARY": "МНИМ.ЧАСТЬ", "IMARGUMENT": "МНИМ.АРГУМЕНТ", "IMCONJUGATE": "МНИМ.СОПРЯЖ", "IMCOS": "МНИМ.COS", "IMCOSH": "МНИМ.COSH", "IMCOT": "МНИМ.COT", "IMCSC": "МНИМ.CSC", "IMCSCH": "МНИМ.CSCH", "IMDIV": "МНИМ.ДЕЛ", "IMEXP": "МНИМ.EXP", "IMLN": "МНИМ.LN", "IMLOG10": "МНИМ.LOG10", "IMLOG2": "МНИМ.LOG2", "IMPOWER": "МНИМ.СТЕПЕНЬ", "IMPRODUCT": "МНИМ.ПРОИЗВЕД", "IMREAL": "МНИМ.ВЕЩ", "IMSEC": "МНИМ.SEC", "IMSECH": "МНИМ.SECH", "IMSIN": "МНИМ.SIN", "IMSINH": "МНИМ.SINH", "IMSQRT": "МНИМ.КОРЕНЬ", "IMSUB": "МНИМ.РАЗН", "IMSUM": "МНИМ.СУММ", "IMTAN": "МНИМ.TAN", "OCT2BIN": "ВОСЬМ.В.ДВ", "OCT2DEC": "ВОСЬМ.В.ДЕС", "OCT2HEX": "ВОСЬМ.В.ШЕСТН", "DAVERAGE": "ДСРЗНАЧ", "DCOUNT": "БСЧЁТ", "DCOUNTA": "БСЧЁТА", "DGET": "БИЗВЛЕЧЬ", "DMAX": "ДМАКС", "DMIN": "ДМИН", "DPRODUCT": "БДПРОИЗВЕД", "DSTDEV": "ДСТАНДОТКЛ", "DSTDEVP": "ДСТАНДОТКЛП", "DSUM": "БДСУММ", "DVAR": "БДДИСП", "DVARP": "БДДИСПП", "CHAR": "СИМВОЛ", "CLEAN": "ПЕЧСИМВ", "CODE": "КОДСИМВ", "CONCATENATE": "СЦЕПИТЬ", "CONCAT": "СЦЕП", "DOLLAR": "РУБЛЬ", "EXACT": "СОВПАД", "FIND": "НАЙТИ", "FINDB": "НАЙТИБ", "FIXED": "ФИКСИРОВАННЫЙ", "LEFT": "ЛЕВСИМВ", "LEFTB": "ЛЕВБ", "LEN": "ДЛСТР", "LENB": "ДЛИНБ", "LOWER": "СТРОЧН", "MID": "ПСТР", "MIDB": "ПСТРБ", "NUMBERVALUE": "ЧЗНАЧ", "PROPER": "ПРОПНАЧ", "REPLACE": "ЗАМЕНИТЬ", "REPLACEB": "ЗАМЕНИТЬБ", "REPT": "ПОВТОР", "RIGHT": "ПРАВСИМВ", "RIGHTB": "ПРАВБ", "SEARCH": "ПОИСК", "SEARCHB": "ПОИСКБ", "SUBSTITUTE": "ПОДСТАВИТЬ", "T": "Т", "TEXT": "ТЕКСТ", "TEXTJOIN": "ОБЪЕДИНИТЬ", "TREND": "ТЕНДЕНЦИЯ", "TRIM": "СЖПРОБЕЛЫ", "T.TEST": "СТЬЮДЕНТ.ТЕСТ", "TRIMMEAN": "УРЕЗСРЕДНЕЕ", "TTEST": "ТТЕСТ", "UNICHAR": "ЮНИСИМВ", "UNICODE": "UNICODE", "UPPER": "ПРОПИСН", "VALUE": "ЗНАЧЕН", "AVEDEV": "СРОТКЛ", "AVERAGE": "СРЗНАЧ", "AVERAGEA": "СРЗНАЧА", "AVERAGEIF": "СРЗНАЧЕСЛИ", "AVERAGEIFS": "СРЗНАЧЕСЛИМН", "BETADIST": "БЕТАРАСП", "BETAINV": "БЕТАОБР", "BETA.DIST": "БЕТА.РАСП", "BETA.INV": "БЕТА.ОБР", "BINOMDIST": "БИНОМРАСП", "BINOM.DIST": "БИНОМ.РАСП", "BINOM.DIST.RANGE": "БИНОМ.РАСП.ДИАП", "BINOM.INV": "БИНОМ.ОБР", "CHIDIST": "ХИ2РАСП", "CHIINV": "ХИ2ОБР", "CHITEST": "ХИ2ТЕСТ", "CHISQ.DIST": "ХИ2.РАСП", "CHISQ.DIST.RT": "ХИ2.РАСП.ПХ", "CHISQ.INV": "ХИ2.ОБР", "CHISQ.INV.RT": "ХИ2.ОБР.ПХ", "CHISQ.TEST": "ХИ2.ТЕСТ", "CONFIDENCE": "ДОВЕРИТ", "CONFIDENCE.NORM": "ДОВЕРИТ.НОРМ", "CONFIDENCE.T": "ДОВЕРИТ.СТЬЮДЕНТ", "CORREL": "КОРРЕЛ", "COUNT": "СЧЁТ", "COUNTA": "СЧЁТЗ", "COUNTBLANK": "СЧИТАТЬПУСТОТЫ", "COUNTIF": "СЧЁТЕСЛИ", "COUNTIFS": "СЧЁТЕСЛИМН", "COVAR": "КОВАР", "COVARIANCE.P": "КОВАРИАЦИЯ.Г", "COVARIANCE.S": "КОВАРИАЦИЯ.В", "CRITBINOM": "КРИТБИНОМ", "DEVSQ": "КВАДРОТКЛ", "EXPON.DIST": "ЭКСП.РАСП", "EXPONDIST": "ЭКСПРАСП", "FDIST": "FРАСП", "FINV": "FРАСПОБР", "FTEST": "ФТЕСТ", "F.DIST": "F.РАСП", "F.DIST.RT": "F.РАСП.ПХ", "F.INV": "F.ОБР", "F.INV.RT": "F.ОБР.ПХ", "F.TEST": "F.ТЕСТ", "FISHER": "ФИШЕР", "FISHERINV": "ФИШЕРОБР", "FORECAST": "ПРЕДСКАЗ", "FORECAST.ETS": "ПРЕДСКАЗ.ETS", "FORECAST.ETS.CONFINT": "ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ", "FORECAST.ETS.SEASONALITY": "ПРЕДСКАЗ.ETS.СЕЗОННОСТЬ", "FORECAST.ETS.STAT": "ПРЕДСКАЗ.ETS.СТАТ", "FORECAST.LINEAR": "ПРЕДСКАЗ.ЛИНЕЙН", "FREQUENCY": "ЧАСТОТА", "GAMMA": "ГАММА", "GAMMADIST": "ГАММАРАСП", "GAMMA.DIST": "ГАММА.РАСП", "GAMMAINV": "ГАММАОБР", "GAMMA.INV": "ГАММА.ОБР", "GAMMALN": "ГАММАНЛОГ", "GAMMALN.PRECISE": "ГАММАНЛОГ.ТОЧН", "GAUSS": "ГАУСС", "GEOMEAN": "СРГЕОМ", "GROWTH": "РОСТ", "HARMEAN": "СРГАРМ", "HYPGEOM.DIST": "ГИПЕРГЕОМ.РАСП", "HYPGEOMDIST": "ГИПЕРГЕОМЕТ", "INTERCEPT": "ОТРЕЗОК", "KURT": "ЭКСЦЕСС", "LARGE": "НАИБОЛЬШИЙ", "LINEST": "ЛИНЕЙН", "LOGEST": "ЛГРФПРИБЛ", "LOGINV": "ЛОГНОРМОБР", "LOGNORM.DIST": "ЛОГНОРМ.РАСП", "LOGNORM.INV": "ЛОГНОРМ.ОБР", "LOGNORMDIST": "ЛОГНОРМРАСП", "MAX": "МАКС", "MAXA": "МАКСА", "MAXIFS": "МАКСЕСЛИ", "MEDIAN": "МЕДИАНА", "MIN": "МИН", "MINA": "МИНА", "MINIFS": "МИНЕСЛИ", "MODE": "МОДА", "MODE.MULT": "МОДА.НСК", "MODE.SNGL": "МОДА.ОДН", "NEGBINOMDIST": "ОТРБИНОМРАСП", "NEGBINOM.DIST": "ОТРБИНОМ.РАСП", "NORM.DIST": "НОРМ.РАСП", "NORM.INV": "НОРМ.ОБР", "NORM.S.DIST": "НОРМ.СТ.РАСП", "NORM.S.INV": "НОРМ.СТ.ОБР", "NORMDIST": "НОРМРАСП", "NORMINV": "НОРМОБР", "NORMSDIST": "НОРМСТРАСП", "NORMSINV": "НОРМСТОБР", "PEARSON": "ПИРСОН", "PERCENTILE": "ПЕРСЕНТИЛЬ", "PERCENTILE.EXC": "ПРОЦЕНТИЛЬ.ИСКЛ", "PERCENTILE.INC": "ПРОЦЕНТИЛЬ.ВКЛ", "PERCENTRANK": "ПРОЦЕНТРАНГ", "PERCENTRANK.EXC": "ПРОЦЕНТРАНГ.ИСКЛ", "PERCENTRANK.INC": "ПРОЦЕНТРАНГ.ВКЛ", "PERMUT": "ПЕРЕСТ", "PERMUTATIONA": "ПЕРЕСТА", "PHI": "ФИ", "POISSON": "ПУАССОН", "POISSON.DIST": "ПУАССОН.РАСП", "PROB": "ВЕРОЯТНОСТЬ", "QUARTILE": "КВАРТИЛЬ", "QUARTILE.INC": "КВАРТИЛЬ.ВКЛ", "QUARTILE.EXC": "КВАРТИЛЬ.ИСКЛ", "RANK.AVG": "РАНГ.СР", "RANK.EQ": "РАНГ.РВ", "RANK": "РАНГ", "RSQ": "КВПИРСОН", "SKEW": "СКОС", "SKEW.P": "СКОС.Г", "SLOPE": "НАКЛОН", "SMALL": "НАИМЕНЬШИЙ", "STANDARDIZE": "НОРМАЛИЗАЦИЯ", "STDEV": "СТАНДОТКЛОН", "STDEV.P": "СТАНДОТКЛОН.Г", "STDEV.S": "СТАНДОТКЛОН.В", "STDEVA": "СТАНДОТКЛОНА", "STDEVP": "СТАНДОТКЛОНП", "STDEVPA": "СТАНДОТКЛОНПА", "STEYX": "СТОШYX", "TDIST": "СТЬЮДРАСП", "TINV": "СТЬЮДРАСПОБР", "T.DIST": "СТЬЮДЕНТ.РАСП", "T.DIST.2T": "СТЬЮДЕНТ.РАСП.2Х", "T.DIST.RT": "СТЬЮДЕНТ.РАСП.ПХ", "T.INV": "СТЬЮДЕНТ.ОБР", "T.INV.2T": "СТЬЮДЕНТ.ОБР.2Х", "VAR": "ДИСП", "VAR.P": "ДИСП.Г", "VAR.S": "ДИСП.В", "VARA": "ДИСПА", "VARP": "ДИСПР", "VARPA": "ДИСПРА", "WEIBULL": "ВЕЙБУЛЛ", "WEIBULL.DIST": "ВЕЙБУЛЛ.РАСП", "Z.TEST": "Z.ТЕСТ", "ZTEST": "ZТЕСТ", "ACCRINT": "НАКОПДОХОД", "ACCRINTM": "НАКОПДОХОДПОГАШ", "AMORDEGRC": "АМОРУМ", "AMORLINC": "АМОРУВ", "COUPDAYBS": "ДНЕЙКУПОНДО", "COUPDAYS": "ДНЕЙКУПОН", "COUPDAYSNC": "ДНЕЙКУПОНПОСЛЕ", "COUPNCD": "ДАТАКУПОНПОСЛЕ", "COUPNUM": "ЧИСЛКУПОН", "COUPPCD": "ДАТАКУПОНДО", "CUMIPMT": "ОБЩПЛАТ", "CUMPRINC": "ОБЩДОХОД", "DB": "ФУО", "DDB": "ДДОБ", "DISC": "СКИДКА", "DOLLARDE": "РУБЛЬ.ДЕС", "DOLLARFR": "РУБЛЬ.ДРОБЬ", "DURATION": "ДЛИТ", "EFFECT": "ЭФФЕКТ", "FV": "БС", "FVSCHEDULE": "БЗРАСПИС", "INTRATE": "ИНОРМА", "IPMT": "ПРПЛТ", "IRR": "ВСД", "ISPMT": "ПРОЦПЛАТ", "MDURATION": "МДЛИТ", "MIRR": "МВСД", "NOMINAL": "НОМИНАЛ", "NPER": "КПЕР", "NPV": "ЧПС", "ODDFPRICE": "ЦЕНАПЕРВНЕРЕГ", "ODDFYIELD": "ДОХОДПЕРВНЕРЕГ", "ODDLPRICE": "ЦЕНАПОСЛНЕРЕГ", "ODDLYIELD": "ДОХОДПОСЛНЕРЕГ", "PDURATION": "ПДЛИТ", "PMT": "ПЛТ", "PPMT": "ОСПЛТ", "PRICE": "ЦЕНА", "PRICEDISC": "ЦЕНАСКИДКА", "PRICEMAT": "ЦЕНАПОГАШ", "PV": "ПС", "RATE": "СТАВКА", "RECEIVED": "ПОЛУЧЕНО", "RRI": "ЭКВ.СТАВКА", "SLN": "АПЛ", "SYD": "АСЧ", "TBILLEQ": "РАВНОКЧЕК", "TBILLPRICE": "ЦЕНАКЧЕК", "TBILLYIELD": "ДОХОДКЧЕК", "VDB": "ПУО", "XIRR": "ЧИСТВНДОХ", "XNPV": "ЧИСТНЗ", "YIELD": "ДОХОД", "YIELDDISC": "ДОХОДСКИДКА", "YIELDMAT": "ДОХОДПОГАШ", "ABS": "ABS", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "АГРЕГАТ", "ARABIC": "АРАБСКОЕ", "ASC": "ASC", "ASIN": "ASIN", "ASINH": "ASINH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "ОСНОВАНИЕ", "CEILING": "ОКРВВЕРХ", "CEILING.MATH": "ОКРВВЕРХ.МАТ", "CEILING.PRECISE": "ОКРВВЕРХ.ТОЧН", "COMBIN": "ЧИСЛКОМБ", "COMBINA": "ЧИСЛКОМБА", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "ДЕС", "DEGREES": "ГРАДУСЫ", "ECMA.CEILING": "ECMA.ОКРВВЕРХ", "EVEN": "ЧЁТН", "EXP": "EXP", "FACT": "ФАКТР", "FACTDOUBLE": "ДВФАКТР", "FLOOR": "ОКРВНИЗ", "FLOOR.PRECISE": "ОКРВНИЗ.ТОЧН", "FLOOR.MATH": "ОКРВНИЗ.МАТ", "GCD": "НОД", "INT": "ЦЕЛОЕ", "ISO.CEILING": "ISO.ОКРВВЕРХ", "LCM": "НОК", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "МОПРЕД", "MINVERSE": "МОБР", "MMULT": "МУМНОЖ", "MOD": "ОСТАТ", "MROUND": "ОКРУГЛТ", "MULTINOMIAL": "МУЛЬТИНОМ", "MUNIT": "МЕДИН", "ODD": "НЕЧЁТ", "PI": "ПИ", "POWER": "СТЕПЕНЬ", "PRODUCT": "ПРОИЗВЕД", "QUOTIENT": "ЧАСТНОЕ", "RADIANS": "РАДИАНЫ", "RAND": "СЛЧИС", "RANDARRAY": "СЛУЧМАССИВ", "RANDBETWEEN": "СЛУЧМЕЖДУ", "ROMAN": "РИМСКОЕ", "ROUND": "ОКРУГЛ", "ROUNDDOWN": "ОКРУГЛВНИЗ", "ROUNDUP": "ОКРУГЛВВЕРХ", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "РЯД.СУММ", "SIGN": "ЗНАК", "SIN": "SIN", "SINH": "SINH", "SQRT": "КОРЕНЬ", "SQRTPI": "КОРЕНЬПИ", "SUBTOTAL": "ПРОМЕЖУТОЧНЫЕ.ИТОГИ", "SUM": "СУММ", "SUMIF": "СУММЕСЛИ", "SUMIFS": "СУММЕСЛИМН", "SUMPRODUCT": "СУММПРОИЗВ", "SUMSQ": "СУММКВ", "SUMX2MY2": "СУММРАЗНКВ", "SUMX2PY2": "СУММСУММКВ", "SUMXMY2": "СУММКВРАЗН", "TAN": "TAN", "TANH": "TANH", "TRUNC": "ОТБР", "ADDRESS": "АДРЕС", "CHOOSE": "ВЫБОР", "COLUMN": "СТОЛБЕЦ", "COLUMNS": "ЧИСЛСТОЛБ", "FORMULATEXT": "Ф.ТЕКСТ", "HLOOKUP": "ГПР", "HYPERLINK": "ГИПЕРССЫЛКА", "INDEX": "ИНДЕКС", "INDIRECT": "ДВССЫЛ", "LOOKUP": "ПРОСМОТР", "MATCH": "ПОИСКПОЗ", "OFFSET": "СМЕЩ", "ROW": "СТРОКА", "ROWS": "ЧСТРОК", "TRANSPOSE": "ТРАНСП", "UNIQUE": "УНИК", "VLOOKUP": "ВПР", "XLOOKUP": "ПРОСМОТРX", "CELL": "ЯЧЕЙКА", "ERROR.TYPE": "ТИП.ОШИБКИ", "ISBLANK": "ЕПУСТО", "ISERR": "ЕОШ", "ISERROR": "ЕОШИБКА", "ISEVEN": "ЕЧЁТН", "ISFORMULA": "ЕФОРМУЛА", "ISLOGICAL": "ЕЛОГИЧ", "ISNA": "ЕНД", "ISNONTEXT": "ЕНЕТЕКСТ", "ISNUMBER": "ЕЧИСЛО", "ISODD": "ЕНЕЧЁТ", "ISREF": "ЕССЫЛКА", "ISTEXT": "ЕТЕКСТ", "N": "Ч", "NA": "НД", "SHEET": "ЛИСТ", "SHEETS": "ЛИСТЫ", "TYPE": "ТИП", "AND": "И", "FALSE": "ЛОЖЬ", "IF": "ЕСЛИ", "IFS": "ЕСЛИМН", "IFERROR": "ЕСЛИОШИБКА", "IFNA": "ЕСНД", "NOT": "НЕ", "OR": "ИЛИ", "SWITCH": "ПЕРЕКЛЮЧ", "TRUE": "ИСТИНА", "XOR": "ИСКЛИЛИ", "TEXTBEFORE": "ТЕКСТДО", "TEXTAFTER": "ТЕКСТПОСЛЕ", "TEXTSPLIT": "ТЕКСТРАЗД", "WRAPROWS": "СВЕРНСТРОК", "VSTACK": "ВСТОЛБИК", "HSTACK": "ГСТОЛБИК", "CHOOSEROWS": "ВЫБОРСТРОК", "CHOOSECOLS": "ВЫБОРСТОЛБЦ", "TOCOL": "ПОСТОЛБЦ", "TOROW": "ПОСТРОК", "WRAPCOLS": "СВЕРНСТОЛБЦ", "TAKE": "ВЗЯТЬ", "DROP": "СБРОСИТЬ", "LocalFormulaOperands": {"StructureTables": {"h": "Заголовки", "d": "Данные", "a": "Все", "tr": "Эта строка", "t": "Итоги"}, "CONST_TRUE_FALSE": {"t": "ИСТИНА", "f": "ЛОЖЬ"}, "CONST_ERROR": {"nil": "#ПУСТО!", "div": "#ДЕЛ/0!", "value": "#ЗНАЧ!", "ref": "#ССЫЛКА!", "name": "#ИМЯ\\?", "num": "#ЧИСЛО!", "na": "#Н/Д", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}