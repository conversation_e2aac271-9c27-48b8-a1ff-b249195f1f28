{"cancelButtonText": "Abbrechen", "Common.Controllers.Chat.notcriticalErrorTitle": "Achtung", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht hier ein", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textArea": "Fläche", "Common.define.chartData.textAreaStacked": "Gestapelte Fläche", "Common.define.chartData.textAreaStackedPer": "100% Gestapelte Fläche", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Gruppierte Säule", "Common.define.chartData.textBarNormal3d": "Gruppierte 3D-Säule", "Common.define.chartData.textBarNormal3dPerspective": "3D-Säule", "Common.define.chartData.textBarStacked": "Gestapelte Säulen", "Common.define.chartData.textBarStacked3d": "Gestapelte 3D-Säule", "Common.define.chartData.textBarStackedPer": "100% Gestapelte Säule", "Common.define.chartData.textBarStackedPer3d": "3D 100% Gestapelte Säule", "Common.define.chartData.textCharts": "Diagramme", "Common.define.chartData.textColumn": "<PERSON>lt<PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON>lt<PERSON>", "Common.define.chartData.textCombo": "Verbund", "Common.define.chartData.textComboAreaBar": "Gestapelte Flächen/Gruppierte Säulen", "Common.define.chartData.textComboBarLine": "Gruppierte Säulen - Linie", "Common.define.chartData.textComboBarLineSecondary": "Gruppierte Säulen/Linien auf der Sekundärachse", "Common.define.chartData.textComboCustom": "Benutzerdefinierte Kombination", "Common.define.chartData.textDoughnut": "Ring", "Common.define.chartData.textHBarNormal": "Gruppierte Balken", "Common.define.chartData.textHBarNormal3d": "Gruppierte 3D-Balken", "Common.define.chartData.textHBarStacked": "Gestapelte Balken", "Common.define.chartData.textHBarStacked3d": "Gestapelte 3D-Balken", "Common.define.chartData.textHBarStackedPer": "100% Gestapelte Balken", "Common.define.chartData.textHBarStackedPer3d": "3D 100% Gestapelte Balken", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3D-<PERSON><PERSON>", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> mit Datenpunkten", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Gestapelte Linie", "Common.define.chartData.textLineStackedMarker": "Gestapelte Linie mit Datenpunkten", "Common.define.chartData.textLineStackedPer": "100% Gestapelte Linie", "Common.define.chartData.textLineStackedPerMarker": "100% Gestapelte Linie mit Datenpunkten", "Common.define.chartData.textPie": "Kreis", "Common.define.chartData.textPie3d": "3D-Kreis", "Common.define.chartData.textPoint": "<PERSON><PERSON> (XY)", "Common.define.chartData.textScatter": "Punkte", "Common.define.chartData.textScatterLine": "Punkte mit geraden Linien", "Common.define.chartData.textScatterLineMarker": "Punkte mit geraden Linien und Datenpunkten", "Common.define.chartData.textScatterSmooth": "Punkte mit interpolierten Linien", "Common.define.chartData.textScatterSmoothMarker": "Punkte mit interpolierten Linien und Datenpunkten", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "<PERSON><PERSON>", "Common.define.chartData.textSurface": "Oberfläche", "Common.define.chartData.textWinLossSpark": "Gewinn/Verlust", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Kein Format festgelegt", "Common.define.conditionalData.text1Above": "1 Std Abw über", "Common.define.conditionalData.text1Below": "1 s darunter", "Common.define.conditionalData.text2Above": "2 s dar<PERSON>ber", "Common.define.conditionalData.text2Below": "2 s darunter", "Common.define.conditionalData.text3Above": "3 s da<PERSON><PERSON>ber", "Common.define.conditionalData.text3Below": "3 s darunter", "Common.define.conditionalData.textAbove": "<PERSON><PERSON>", "Common.define.conditionalData.textAverage": "Durchschnitt", "Common.define.conditionalData.textBegins": "beginnt mit", "Common.define.conditionalData.textBelow": "Unten", "Common.define.conditionalData.textBetween": "zwischen", "Common.define.conditionalData.textBlank": "<PERSON><PERSON>", "Common.define.conditionalData.textBlanks": "enthält leere Werte", "Common.define.conditionalData.textBottom": "Unten", "Common.define.conditionalData.textContains": "<PERSON>th<PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Datenbalken", "Common.define.conditionalData.textDate": "Datum", "Common.define.conditionalData.textDuplicate": "Verdoppeln", "Common.define.conditionalData.textEnds": "endet mit", "Common.define.conditionalData.textEqAbove": "<PERSON><PERSON><PERSON> oder größer", "Common.define.conditionalData.textEqBelow": "<PERSON><PERSON><PERSON> oder weniger", "Common.define.conditionalData.textEqual": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textError": "<PERSON><PERSON>", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textFormula": "Formel", "Common.define.conditionalData.textGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "Common.define.conditionalData.textGreaterEq": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich wie ", "Common.define.conditionalData.textIconSets": "Symbolsätze", "Common.define.conditionalData.textLast7days": "In den letzten 7 Tagen", "Common.define.conditionalData.textLastMonth": "Letzter Monat", "Common.define.conditionalData.textLastWeek": "Letzte Woche", "Common.define.conditionalData.textLess": "<PERSON><PERSON>s", "Common.define.conditionalData.textLessEq": "<PERSON>er als oder gleich", "Common.define.conditionalData.textNextMonth": "Nächster Monat", "Common.define.conditionalData.textNextWeek": "Nächste Woche", "Common.define.conditionalData.textNotBetween": "nicht zwischen", "Common.define.conditionalData.textNotBlanks": "En<PERSON><PERSON>lt keine leeren Werte", "Common.define.conditionalData.textNotContains": "Enthält kein(e/en)", "Common.define.conditionalData.textNotEqual": "<PERSON><PERSON> gleich", "Common.define.conditionalData.textNotErrors": "<PERSON><PERSON><PERSON><PERSON> keine <PERSON>", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON>", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON>", "Common.define.conditionalData.textToday": "<PERSON><PERSON>", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON>", "Common.define.conditionalData.textTop": "<PERSON><PERSON>", "Common.define.conditionalData.textUnique": "Eindeutig", "Common.define.conditionalData.textValue": "Wert ist", "Common.define.conditionalData.textYesterday": "Gestern", "Common.define.smartArt.textAccentedPicture": "Bild mit Akzenten", "Common.define.smartArt.textAccentProcess": "Akzentprozess", "Common.define.smartArt.textAlternatingFlow": "Alternierender Fluss", "Common.define.smartArt.textAlternatingHexagons": "Alternierende Sechsecke", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternierende Bildblöcke", "Common.define.smartArt.textAlternatingPictureCircles": "Alternierende Bildblöcke", "Common.define.smartArt.textArchitectureLayout": "Architekturlayout", "Common.define.smartArt.textArrowRibbon": "Pfeilband", "Common.define.smartArt.textAscendingPictureAccentProcess": "Aufsteigender Prozess mit Bildakzenten", "Common.define.smartArt.textBalance": "Kontostand", "Common.define.smartArt.textBasicBendingProcess": "Einfacher umgebrochener Prozess", "Common.define.smartArt.textBasicBlockList": "Einfache Blockliste", "Common.define.smartArt.textBasicChevronProcess": "Einfacher Chevronprozess", "Common.define.smartArt.textBasicCycle": "Einfacher Kreis", "Common.define.smartArt.textBasicMatrix": "Einfache Matrix", "Common.define.smartArt.textBasicPie": "Einfaches Kreisdiagramm", "Common.define.smartArt.textBasicProcess": "Einfacher Prozess", "Common.define.smartArt.textBasicPyramid": "Einfache Pyramide", "Common.define.smartArt.textBasicRadial": "Einfaches Radial", "Common.define.smartArt.textBasicTarget": "Einfaches Ziel", "Common.define.smartArt.textBasicTimeline": "Einfache Zeitachse", "Common.define.smartArt.textBasicVenn": "Einfaches Venn", "Common.define.smartArt.textBendingPictureAccentList": "Umgebrochene Bildakzentliste", "Common.define.smartArt.textBendingPictureBlocks": "Umgebrochene Bildblöcke", "Common.define.smartArt.textBendingPictureCaption": "Umgebrochene Bildbeschriftung", "Common.define.smartArt.textBendingPictureCaptionList": "Umgebrochene Bildbeschriftungsliste", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Umgebrochener halbtransparenter Bildtext", "Common.define.smartArt.textBlockCycle": "Blockkreis", "Common.define.smartArt.textBubblePictureList": "Blasenbildliste", "Common.define.smartArt.textCaptionedPictures": "Bilder mit Beschriftungen", "Common.define.smartArt.textChevronAccentProcess": "Chevronakzentprozess", "Common.define.smartArt.textChevronList": "Chevronliste", "Common.define.smartArt.textCircleAccentTimeline": "Zeitachse mit Kreisakzent", "Common.define.smartArt.textCircleArrowProcess": "Kreisförmiger Pfeilprozess", "Common.define.smartArt.textCirclePictureHierarchy": "Bilderhierarchie mit Kreisakzent", "Common.define.smartArt.textCircleProcess": "Kreisprozess", "Common.define.smartArt.textCircleRelationship": "Kreisbeziehung", "Common.define.smartArt.textCircularBendingProcess": "Kreisförmiger umgebrochener Prozess", "Common.define.smartArt.textCircularPictureCallout": "Bildlegende mit Kreisakzent", "Common.define.smartArt.textClosedChevronProcess": "Geschlossener Chevronprozess", "Common.define.smartArt.textContinuousArrowProcess": "Fortlaufender Pfeilprozess", "Common.define.smartArt.textContinuousBlockProcess": "Fortlaufender Blockprozess", "Common.define.smartArt.textContinuousCycle": "Fortlaufender Kreis", "Common.define.smartArt.textContinuousPictureList": "Fortlaufende Bildliste", "Common.define.smartArt.textConvergingArrows": "Zusammenlaufende Pfeile", "Common.define.smartArt.textConvergingRadial": "Zusammenlaufendes Radial", "Common.define.smartArt.textConvergingText": "Zusammenlaufender Text", "Common.define.smartArt.textCounterbalanceArrows": "Gegengewichtspfeile", "Common.define.smartArt.textCycle": "Z<PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "Kreismatrix", "Common.define.smartArt.textDescendingBlockList": "Absteigende Blockliste", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> Prozess", "Common.define.smartArt.textDetailedProcess": "Detaillierter Prozess", "Common.define.smartArt.textDivergingArrows": "Auseinanderlaufende Pfeile", "Common.define.smartArt.textDivergingRadial": "Auseinanderlaufendes Radial", "Common.define.smartArt.textEquation": "Gleichung", "Common.define.smartArt.textFramedTextPicture": "Umrahmte Textgrafik", "Common.define.smartArt.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGear": "Zahnrad", "Common.define.smartArt.textGridMatrix": "Rastermatrix", "Common.define.smartArt.textGroupedList": "Gruppierte Liste", "Common.define.smartArt.textHalfCircleOrganizationChart": "Halbkreisorganigramm", "Common.define.smartArt.textHexagonCluster": "Sechseck-Cluster", "Common.define.smartArt.textHexagonRadial": "Sechseck Radial", "Common.define.smartArt.textHierarchy": "Hierarchie", "Common.define.smartArt.textHierarchyList": "Hierarchieliste", "Common.define.smartArt.textHorizontalBulletList": "Horizontale Aufzählungsliste", "Common.define.smartArt.textHorizontalHierarchy": "Horizontale Hierarchie", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal beschriftete Hierarchie", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontale Hierarchie mit mehreren Ebenen", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontales Organigramm", "Common.define.smartArt.textHorizontalPictureList": "Horizontale Bildliste", "Common.define.smartArt.textIncreasingArrowProcess": "Wachsender Pfeil-Prozess", "Common.define.smartArt.textIncreasingCircleProcess": "Wachsender Kreis-Prozess", "Common.define.smartArt.textInterconnectedBlockProcess": "Vernetzter Blockprozess", "Common.define.smartArt.textInterconnectedRings": "Verbundene Ringe", "Common.define.smartArt.textInvertedPyramid": "Umgekehrte Pyramide", "Common.define.smartArt.textLabeledHierarchy": "Beschriftete Hierarchie", "Common.define.smartArt.textLinearVenn": "Lineares Venn", "Common.define.smartArt.textLinedList": "Liste mit Linien", "Common.define.smartArt.textList": "Liste", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Kreis mit mehreren Richtungen", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigramm mit Name und Titel", "Common.define.smartArt.textNestedTarget": "Geschachteltes Ziel", "Common.define.smartArt.textNondirectionalCycle": "Richtungsloser Kreis", "Common.define.smartArt.textOpposingArrows": "Entgegengesetzte Pfeile", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Organigramm", "Common.define.smartArt.textOther": "Sonstiges", "Common.define.smartArt.textPhasedProcess": "Phasenprozess", "Common.define.smartArt.textPicture": "Bild", "Common.define.smartArt.textPictureAccentBlocks": "Bildakzentblöcke", "Common.define.smartArt.textPictureAccentList": "Bildakzentliste", "Common.define.smartArt.textPictureAccentProcess": "Bildakzentprozess", "Common.define.smartArt.textPictureCaptionList": "Bildbeschriftungsliste", "Common.define.smartArt.textPictureFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureGrid": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureLineup": "Bildanordnung", "Common.define.smartArt.textPictureOrganizationChart": "Bildorganigramm", "Common.define.smartArt.textPictureStrips": "Bildstreifen", "Common.define.smartArt.textPieProcess": "Kreisdiagrammprozess", "Common.define.smartArt.textPlusAndMinus": "Plus und Minus", "Common.define.smartArt.textProcess": "Prozess", "Common.define.smartArt.textProcessArrows": "Prozesspfeile", "Common.define.smartArt.textProcessList": "Prozessliste", "Common.define.smartArt.textPyramid": "Pyramide", "Common.define.smartArt.textPyramidList": "Pyramidenliste", "Common.define.smartArt.textRadialCluster": "Radialer Cluster", "Common.define.smartArt.textRadialCycle": "Radialkreis", "Common.define.smartArt.textRadialList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialPictureList": "Radiale Bildliste", "Common.define.smartArt.textRadialVenn": "Radialvenn", "Common.define.smartArt.textRandomToResultProcess": "Zufallsergebnisprozess", "Common.define.smartArt.textRelationship": "Beziehung", "Common.define.smartArt.textRepeatingBendingProcess": "Wiederholter umgebrochener Prozess", "Common.define.smartArt.textReverseList": "Umgekehrte Liste", "Common.define.smartArt.textSegmentedCycle": "Segmentierter Kreis", "Common.define.smartArt.textSegmentedProcess": "Segment<PERSON>ter Prozess", "Common.define.smartArt.textSegmentedPyramid": "Segmentierte Pyramide", "Common.define.smartArt.textSnapshotPictureList": "Momentaufnahme-Bildliste", "Common.define.smartArt.textSpiralPicture": "Spiralförmige Grafik", "Common.define.smartArt.textSquareAccentList": "Liste mit quadratischen Akzenten", "Common.define.smartArt.textStackedList": "Gestapelte Liste", "Common.define.smartArt.textStackedVenn": "Gestapeltes Venn", "Common.define.smartArt.textStaggeredProcess": "Gestaffelter Prozess", "Common.define.smartArt.textStepDownProcess": "Prozess mit absteigenden Schritten", "Common.define.smartArt.textStepUpProcess": "Prozess mit aufsteigenden Schritten", "Common.define.smartArt.textSubStepProcess": "Unterschrittprozess", "Common.define.smartArt.textTabbedArc": "Registerkartenbogen", "Common.define.smartArt.textTableHierarchy": "Tabellenhierarchie", "Common.define.smartArt.textTableList": "Tabellenliste", "Common.define.smartArt.textTabList": "Registerkartenliste", "Common.define.smartArt.textTargetList": "Zielliste", "Common.define.smartArt.textTextCycle": "Textkreis", "Common.define.smartArt.textThemePictureAccent": "Designbildakzent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Alternierender Designbildakzent", "Common.define.smartArt.textThemePictureGrid": "Designbildraster", "Common.define.smartArt.textTitledMatrix": "Betitelte Matrix", "Common.define.smartArt.textTitledPictureAccentList": "Bildakzentliste mit Titel", "Common.define.smartArt.textTitledPictureBlocks": "Titelbildblöcke", "Common.define.smartArt.textTitlePictureLineup": "Titelbildanordnung", "Common.define.smartArt.textTrapezoidList": "Trapezförmige Liste", "Common.define.smartArt.textUpwardArrow": "<PERSON><PERSON><PERSON> nach oben", "Common.define.smartArt.textVaryingWidthList": "Liste mit variabler Breite", "Common.define.smartArt.textVerticalAccentList": "Liste mit vertikalen Akzenten", "Common.define.smartArt.textVerticalArrowList": "Vertical Arrow List", "Common.define.smartArt.textVerticalBendingProcess": "Vertikaler umgebrochener Prozess", "Common.define.smartArt.textVerticalBlockList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBoxList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBracketList": "Liste mit vertikalen Klammerakzenten", "Common.define.smartArt.textVerticalBulletList": "Vert<PERSON><PERSON> Aufzählung", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalCircleList": "Liste mit vertikalen Kreisakzenten", "Common.define.smartArt.textVerticalCurvedList": "Liste mit vertikalen Kurven", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalPictureAccentList": "Vertikale Bildakzentliste", "Common.define.smartArt.textVerticalPictureList": "<PERSON><PERSON><PERSON><PERSON>ildl<PERSON>", "Common.define.smartArt.textVerticalProcess": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "Das Dokument ist für die Bearbeitung gesperrt. Sie können Änderungen vornehmen und es später als lokale Kopie speichern.", "Common.Translation.tipFileReadOnly": "Das Dokument ist schreibgeschützt und für die Bearbeitung gesperrt. Sie können Änderungen vornehmen und die lokale Kopie später speichern.", "Common.Translation.warnFileLocked": "Die Datei wird in einer anderen App bearbeitet. Si<PERSON> können die Bearbeitung fortsetzen und die Kopie dieser Datei speichern.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>", "Common.Translation.warnFileLockedBtnView": "Schreibgeschützt öffnen", "Common.UI.ButtonColored.textAutoColor": "Automatisch", "Common.UI.ButtonColored.textNewColor": "Benutzerdefinierte Farbe", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON>", "Common.UI.ComboDataView.emptyComboText": "Keine Formate", "Common.UI.ExtendedColorDialog.addButtonText": "Hinzufügen", "Common.UI.ExtendedColorDialog.textCurrent": "Aktuell", "Common.UI.ExtendedColorDialog.textHexErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 000000 und FFFFFF ein.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON>eu", "Common.UI.ExtendedColorDialog.textRGBErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen numerischen Wert zwischen 0 und 255 ein.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Passwort ausblenden", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Password anzeigen", "Common.UI.SearchBar.textFind": "<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON> sch<PERSON>ßen", "Common.UI.SearchBar.tipNextResult": "Nächstes Ergebnis", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Erweiterte Einstellungen öffnen", "Common.UI.SearchBar.tipPreviousResult": "Vorheriges Ergebnis", "Common.UI.SearchDialog.textHighlight": "Ergebnisse markieren", "Common.UI.SearchDialog.textMatchCase": "Groß-/Kleinschreibung beachten", "Common.UI.SearchDialog.textReplaceDef": "Geben Sie den Ersetzungstext ein", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON> den Text hier ein", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON> und ersetzen", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Nur ganze Wörter", "Common.UI.SearchDialog.txtBtnHideReplace": "Ersetzen verbergen", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON>e ersetzen", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.UI.SynchronizeTip.textSynchronize": "Das Dokument wurde von einem anderen Benutzer geändert.<br><PERSON><PERSON> klicken hier, um Ihre Änderungen zu speichern und die Aktualisierungen neu zu laden.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON><PERSON>lich verwendete Farben", "Common.UI.ThemeColorPalette.textStandartColors": "Standardfarben", "Common.UI.ThemeColorPalette.textThemeColors": "Designfarben", "Common.UI.Themes.txtThemeClassicLight": "Klassisch Hell", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON> Ko<PERSON>ras<PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Hell", "Common.UI.Themes.txtThemeSystem": "Wie im System", "Common.UI.Window.cancelButtonText": "Abbrechen", "Common.UI.Window.closeButtonText": "Schließen", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bestätigung", "Common.UI.Window.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Achtung", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Strg", "Common.Utils.String.textShift": "Umschalt", "Common.Views.About.txtAddress": "<PERSON><PERSON><PERSON>: ", "Common.Views.About.txtLicensee": "LIZENZNEHMER", "Common.Views.About.txtLicensor": "LIZENZGEBER", "Common.Views.About.txtMail": "E-Mail-Adresse: ", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "Common.Views.About.txtTel": "Tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Hinzufügen", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Bei der Arbeit anwenden", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorrektur", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat während der Eingabe", "Common.Views.AutoCorrectDialog.textBy": "Nach", "Common.Views.AutoCorrectDialog.textDelete": "Löschen", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet- und Netzwerkpfade durch Links", "Common.Views.AutoCorrectDialog.textMathCorrect": "Mathe Autokorrektur", "Common.Views.AutoCorrectDialog.textNewRowCol": "Neue Zeilen und Spalten in Tabellenkalkulation einschließen", "Common.Views.AutoCorrectDialog.textRecognized": "Erkannte Funktionen", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Die folgenden Ausdrücke sind erkannte mathematische Funktionen. Diese werden nicht automatisch kursiviert.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Bei der Eingabe ersetzen", "Common.Views.AutoCorrectDialog.textReplaceType": "Text bei der Eingabe ersetzen", "Common.Views.AutoCorrectDialog.textReset": "Z<PERSON>ücksetzen", "Common.Views.AutoCorrectDialog.textResetAll": "Zurücksetzen auf die Standardeinstellungen", "Common.Views.AutoCorrectDialog.textRestore": "Wiederherstellen", "Common.Views.AutoCorrectDialog.textTitle": "Autokorrektur", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Erkannte Funktionen sollen nur groß- oder kleingeschriebene Buchstaben von A bis Z beinhalten.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Von Ihnen hinzugefügte Ausdrücke werden entfernt und von Ihnen gelöschte Ausdrücke werden wiederhergestellt. Möchten Sie fortfahren?", "Common.Views.AutoCorrectDialog.warnReplace": "Es gibt schon einen Autokorrektur-Eintrag für %1. Möchten Sie dieses ersetzen?", "Common.Views.AutoCorrectDialog.warnReset": "Von Ihnen hinzugefügte Autokorrekturen werden entfernt und von Ihnen geänderte Autokorrekturen werden auf die Ursprungswerte zurückgesetzt. Möchten Sie fortfahren?", "Common.Views.AutoCorrectDialog.warnRestore": "Der Autokorrektur-Eintrag für %1 wird zurückgestellt. Möchten Sie fortsetzen?", "Common.Views.Chat.textSend": "Senden", "Common.Views.Comments.mniAuthorAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z)", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A)", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniFilterGroups": "Nach Gruppe filtern", "Common.Views.Comments.mniPositionAsc": "<PERSON>ben", "Common.Views.Comments.mniPositionDesc": "<PERSON> unten", "Common.Views.Comments.textAdd": "Hinzufügen", "Common.Views.Comments.textAddComment": "Hinzufügen", "Common.Views.Comments.textAddCommentToDoc": "Kommentar zum Dokument hinzufügen", "Common.Views.Comments.textAddReply": "Antwort hinzufügen", "Common.Views.Comments.textAll": "Alle", "Common.Views.Comments.textAnonym": "Gas<PERSON>", "Common.Views.Comments.textCancel": "Abbrechen", "Common.Views.Comments.textClose": "Schließen", "Common.Views.Comments.textClosePanel": "Kommentare schließen", "Common.Views.Comments.textComments": "Kommentare", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Kommentar hier ein", "Common.Views.Comments.textHintAddComment": "Kommentar hinzufügen", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textReply": "Antworten", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Kommentare sortieren", "Common.Views.Comments.textViewResolved": "<PERSON>e haben keine Berechtigung, den Kommentar erneut zu öffnen", "Common.Views.Comments.txtEmpty": "Das Blatt enthält keine Kommentare.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.Views.CopyWarningDialog.textMsg": "Die Funktionen \"<PERSON>pieren\", \"Ausschneiden\" und \"Einfügen\" können mithilfe den Schaltflächen in der Symbolleiste und Aktionen im Kontextmenü nur in dieser Editor-Registerkarte durchgeführt werden.<br><br> <PERSON><PERSON><PERSON> oder Einfügen in oder aus anderen Anwendungen nutzen Sie die folgenden Tastenkombinationen:", "Common.Views.CopyWarningDialog.textTitle": "Funktionen \"Kopieren\", \"Ausschneiden\" und \"Einfügen\"", "Common.Views.CopyWarningDialog.textToCopy": "zum Kopieren", "Common.Views.CopyWarningDialog.textToCut": "zum Ausschneiden", "Common.Views.CopyWarningDialog.textToPaste": "zum Einfügen", "Common.Views.DocumentAccessDialog.textLoading": "Ladevorgang...", "Common.Views.DocumentAccessDialog.textTitle": "Freigabeeinstellungen", "Common.Views.EditNameDialog.textLabel": "Bezeichnung:", "Common.Views.EditNameDialog.textLabelError": "<PERSON><PERSON><PERSON>nung darf nicht leer sein.", "Common.Views.Header.labelCoUsersDescr": "<PERSON><PERSON><PERSON>, die die Datei bearbeiten:", "Common.Views.Header.textAddFavorite": "Als Favorit kennzei<PERSON>nen", "Common.Views.Header.textAdvSettings": "Erweiterte Einstellungen", "Common.Views.Header.textBack": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textCompactView": "Symbolleiste ausblenden", "Common.Views.Header.textHideLines": "Lineale verbergen", "Common.Views.Header.textHideStatusBar": "Statusleiste verbergen", "Common.Views.Header.textRemoveFavorite": "Aus Favoriten entfernen", "Common.Views.Header.textSaveBegin": "Speicherung...", "Common.Views.Header.textSaveChanged": "Verändert", "Common.Views.Header.textSaveEnd": "Alle Änderungen wurden gespeichert", "Common.Views.Header.textSaveExpander": "Alle Änderungen wurden gespeichert", "Common.Views.Header.textShare": "Freigeben", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Zugriffsrechte für das Dokument verwalten", "Common.Views.Header.tipDownload": "<PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Aktuelle Datei bearbeiten", "Common.Views.Header.tipPrint": "<PERSON><PERSON> drucken", "Common.Views.Header.tipPrintQuick": "Schnelldruck", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Speichern", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "Common.Views.Header.tipUndock": "In einem separaten Fenster abdocken", "Common.Views.Header.tipUsers": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "Ansichts-Einstellungen", "Common.Views.Header.tipViewUsers": "Benutzer ansehen und Zugriffsrechte für das Dokument verwalten", "Common.Views.Header.txtAccessRights": "Zugriffsrechte ändern", "Common.Views.Header.txtRename": "Umbenennen", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHide": "Reduzieren", "Common.Views.History.textHideAll": "Wesentliche Änderungen verbergen", "Common.Views.History.textRestore": "Wiederherstellen", "Common.Views.History.textShow": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Wesentliche Änderungen anzeigen", "Common.Views.History.textVer": "Vers.", "Common.Views.ImageFromUrlDialog.textUrl": "Bild-URL einfügen:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON> muss eine URL im Format \"http://www.example.com\" enthalten", "Common.Views.ListSettingsDialog.textBulleted": "Aufzählung", "Common.Views.ListSettingsDialog.textFromFile": "Aus einer Datei", "Common.Views.ListSettingsDialog.textFromStorage": "Aus dem Speicher", "Common.Views.ListSettingsDialog.textFromUrl": "Aus einer URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "Common.Views.ListSettingsDialog.tipChange": "Aufzählungszeichen ändern", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Farbe", "Common.Views.ListSettingsDialog.txtImage": "Bild", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "Neues Aufzählungszeichen", "Common.Views.ListSettingsDialog.txtNewImage": "Neues Bild", "Common.Views.ListSettingsDialog.txtNone": "kein", "Common.Views.ListSettingsDialog.txtOfText": "% des Textes", "Common.Views.ListSettingsDialog.txtSize": "Größe", "Common.Views.ListSettingsDialog.txtStart": "Beginnen mit", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "Listeneinstellungen", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON> sch<PERSON>n", "Common.Views.OpenDialog.textInvalidRange": "Ungültiger Zellenbereich", "Common.Views.OpenDialog.textSelectData": "Daten auswählen", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Doppelpunkt", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Trennzeichen", "Common.Views.OpenDialog.txtDestData": "<PERSON><PERSON><PERSON><PERSON> aus, wo diese Daten abgelegt werden soll.", "Common.Views.OpenDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "Common.Views.OpenDialog.txtEncoding": "Zeichenkodierung", "Common.Views.OpenDialog.txtIncorrectPwd": "Kennwort ist falsch.", "Common.Views.OpenDialog.txtOpenFile": "Kennwort zum Öffnen der Datei eingeben", "Common.Views.OpenDialog.txtOther": "Sonstige", "Common.Views.OpenDialog.txtPassword": "Kennwort", "Common.Views.OpenDialog.txtPreview": "Vorschau", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON>d Sie das Passwort eingegeben und die Datei geöffnet haben, wird das aktuelle Passwort für die Datei zurückgesetzt.", "Common.Views.OpenDialog.txtSemicolon": "Semikolon", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtTab": "Tabulator", "Common.Views.OpenDialog.txtTitle": "%1-Optionen wählen", "Common.Views.OpenDialog.txtTitleProtected": "Geschützte Datei", "Common.Views.PasswordDialog.txtDescription": "Legen Sie ein Passwort fest, um dieses Dokument zu schützen", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bestätigungseingabe ist nicht identisch", "Common.Views.PasswordDialog.txtPassword": "Kennwort", "Common.Views.PasswordDialog.txtRepeat": "Kenn<PERSON><PERSON> wiederholen", "Common.Views.PasswordDialog.txtTitle": "Kennwort festlegen", "Common.Views.PasswordDialog.txtWarning": "Vorsicht: <PERSON>n Si<PERSON> das Kennwort verlieren oder vergessen, lässt es sich nicht mehr wiederherstellen. Bewahren Sie es an einem sicheren Ort auf.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "<PERSON><PERSON><PERSON> s<PERSON>n", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Starten", "Common.Views.Plugins.textStop": "<PERSON>den", "Common.Views.Protection.hintAddPwd": "Mit Kennwort verschlüsseln", "Common.Views.Protection.hintDelPwd": "Kennwort löschen", "Common.Views.Protection.hintPwd": "Das Kennwort ändern oder löschen", "Common.Views.Protection.hintSignature": "Fügen Sie eine digitale Signatur oder Unterschriftenzeile hinzu", "Common.Views.Protection.txtAddPwd": "Kennwort hinzufügen", "Common.Views.Protection.txtChangePwd": "Kennwort ändern", "Common.Views.Protection.txtDeletePwd": "Kennwort löschen", "Common.Views.Protection.txtEncrypt": "Verschlüsseln", "Common.Views.Protection.txtInvisibleSignature": "Fügen Sie eine digitale Signatur hinzu", "Common.Views.Protection.txtSignature": "Signatur", "Common.Views.Protection.txtSignatureLine": "Signaturzeile hinzufügen", "Common.Views.RenameDialog.textName": "Dateiname", "Common.Views.RenameDialog.txtInvalidName": "Dieser Dateiname darf keines der folgenden Zeichen enthalten:", "Common.Views.ReviewChanges.hintNext": "Zur nächsten Änderung", "Common.Views.ReviewChanges.hintPrev": "Zur vorherigen Änderung", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Echtzeit-Zusammenbearbeitung. Alle Änderungen werden automatisch gespeichert.", "Common.Views.ReviewChanges.strStrict": "Formal", "Common.Views.ReviewChanges.strStrictDesc": "Verwenden Sie die Schaltfläche \"Speichern\", um die von Ihnen und anderen vorgenommenen Änderungen zu synchronisieren.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aktuelle Änderungen annehmen", "Common.Views.ReviewChanges.tipCoAuthMode": "Zusammen-Bearbeitungsmodus einstellen", "Common.Views.ReviewChanges.tipCommentRem": "Kommentare entfernen", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Aktuelle Kommentare entfernen", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Gültige Kommentare lösen", "Common.Views.ReviewChanges.tipHistory": "Versionshistorie anzeigen", "Common.Views.ReviewChanges.tipRejectCurrent": "Aktuelle Änderungen ablehnen", "Common.Views.ReviewChanges.tipReview": "Nachverfo<PERSON><PERSON> von <PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Wählen Sie den Modus aus, in dem die Änderungen angezeigt werden sollen", "Common.Views.ReviewChanges.tipSetDocLang": "Sprache des Dokumentes festlegen", "Common.Views.ReviewChanges.tipSetSpelling": "Rechtschreibprüfung", "Common.Views.ReviewChanges.tipSharing": "Zugriffsrechte für das Dokument verwalten", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Alle Änderungen annehmen", "Common.Views.ReviewChanges.txtAcceptChanges": "Änderungen annehmen", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aktuelle Änderungen annehmen", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Schließen", "Common.Views.ReviewChanges.txtCoAuthMode": "Modus \"Gemeinsame Bearbeitung\"", "Common.Views.ReviewChanges.txtCommentRemAll": "Alle Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Aktuelle Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON>ne Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Meine aktuellen Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemove": "Entfernen", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Alle Kommentare lösen", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Gültige Kommentare lösen", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Meine gültige Kommentare lösen", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "Alle Änderungen werden übernommen (Vorschau)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "Versionshistorie", "Common.Views.ReviewChanges.txtMarkup": "Alle Änderungen (Bearbeitung)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Alle Änderungen werden abgelehnt (Vorschau)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Zurück", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Alle Änderungen ablehnen", "Common.Views.ReviewChanges.txtRejectChanges": "Änderungen ablehnen", "Common.Views.ReviewChanges.txtRejectCurrent": "Aktuelle Änderungen ablehnen", "Common.Views.ReviewChanges.txtSharing": "Freigabe", "Common.Views.ReviewChanges.txtSpelling": "Rechtschreibprüfung", "Common.Views.ReviewChanges.txtTurnon": "Nachverfo<PERSON><PERSON> von <PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON>ige<PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "Hinzufügen", "Common.Views.ReviewPopover.textAddReply": "Antwort hinzufügen", "Common.Views.ReviewPopover.textCancel": "Abbrechen", "Common.Views.ReviewPopover.textClose": "Schließen", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Kommentar hier ein", "Common.Views.ReviewPopover.textMention": "+Erwähnung ermöglicht den Zugriff auf das Dokument und das Senden einer E-Mail", "Common.Views.ReviewPopover.textMentionNotify": "+Erwähnung benachrichtigt den Benutzer per E-Mail", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "Antworten", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "<PERSON>e haben keine Berechtigung, den Kommentar erneut zu öffnen", "Common.Views.ReviewPopover.txtDeleteTip": "Löschen", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Ordner fürs Speichern", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON>", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "Groß-/Kleinschreibung beachten", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON> sch<PERSON>ßen", "Common.Views.SearchPanel.textContentChanged": "Dokument verändert.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON> und ersetzen", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formeln", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textLookIn": "<PERSON><PERSON> in", "Common.Views.SearchPanel.textMatchUsingRegExp": "Über reguläre Ausdrücke abgleichen", "Common.Views.SearchPanel.textName": "Name", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON><PERSON>", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON>e ersetzen", "Common.Views.SearchPanel.textReplaceWith": "Ersetzen durch", "Common.Views.SearchPanel.textSearch": "<PERSON><PERSON>", "Common.Views.SearchPanel.textSearchAgain": "{0}Neue Suche durchführen{1} für genaue Ergebnisse.", "Common.Views.SearchPanel.textSearchHasStopped": "Suche abgebrochen", "Common.Views.SearchPanel.textSearchOptions": "Suchoptionen", "Common.Views.SearchPanel.textSearchResults": "Suchergebnisse: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "Datenbereich auswählen", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Im <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Be<PERSON>ich", "Common.Views.SearchPanel.textTooManyResults": "<PERSON>s gibt zu viele Ergebnisse, um sie hier zu zeigen", "Common.Views.SearchPanel.textValue": "Wert", "Common.Views.SearchPanel.textValues": "<PERSON><PERSON>", "Common.Views.SearchPanel.textWholeWords": "Nur ganze Wörter", "Common.Views.SearchPanel.textWithin": "Innerhalb", "Common.Views.SearchPanel.textWorkbook": "Arbeitsmappe", "Common.Views.SearchPanel.tipNextResult": "Nächstes Ergebnis", "Common.Views.SearchPanel.tipPreviousResult": "Vorheriges Ergebnis", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>w<PERSON>", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ändern", "Common.Views.SignDialog.textInputName": "Name des Signaturgebers eingeben", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Der Name des Signaturgebers darf nicht leer sein.", "Common.Views.SignDialog.textPurpose": "Zweck der Signierung dieses Dokuments", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Bild auswählen", "Common.Views.SignDialog.textSignature": "Wie sieht Signatur aus:", "Common.Views.SignDialog.textTitle": "Dokument signieren", "Common.Views.SignDialog.textUseImage": "oder klicken Sie auf \"Bild auswählen\", um ein Bild als Unterschrift zu verwenden", "Common.Views.SignDialog.textValid": "Gültig von% 1 bis% 2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "Schriftgröße", "Common.Views.SignSettingsDialog.textAllowComment": "Signaturgeber verfügt über die Möglichkeit, einen Kommentar im Signaturdialog hinzuzufügen", "Common.Views.SignSettingsDialog.textDefInstruction": "Überprüfen Si<PERSON>, ob der signierte Inhalt stimmt, bevor <PERSON> dieses Dokument signieren.", "Common.Views.SignSettingsDialog.textInfoEmail": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoName": "Name", "Common.Views.SignSettingsDialog.textInfoTitle": "Titel des Signatureingebers", "Common.Views.SignSettingsDialog.textInstructions": "Anweisungen für Signaturgeber", "Common.Views.SignSettingsDialog.textShowDate": "Signaturdatum in der Signaturzeile anzeigen", "Common.Views.SignSettingsDialog.textTitle": "Signatureinstellungen", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX Wert", "Common.Views.SymbolTableDialog.textCopyright": "Copyright Zeichen", "Common.Views.SymbolTableDialog.textDCQuote": "Schließende doppelte Anführungsstriche", "Common.Views.SymbolTableDialog.textDOQuote": "Öffnende doppelte Anführungsstriche", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontale Ellipse", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Geviert-<PERSON>rz<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "Halbgeviertstrich", "Common.Views.SymbolTableDialog.textEnSpace": "Halbgeviert-Leerzeichen", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBSpace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textPilcrow": "Absatzzeichen", "Common.Views.SymbolTableDialog.textQEmSpace": "Viertelgeviert-Leerzeichen", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "K<PERSON><PERSON>lich verwendete Symbole", "Common.Views.SymbolTableDialog.textRegistered": "Registered Trade Mark", "Common.Views.SymbolTableDialog.textSCQuote": "Schließende einfache Anführungsstriche", "Common.Views.SymbolTableDialog.textSection": "Paragraphenzeichen", "Common.Views.SymbolTableDialog.textShortcut": "Tastenkombination", "Common.Views.SymbolTableDialog.textSHyphen": "Weicher Bindestrich", "Common.Views.SymbolTableDialog.textSOQuote": "Öffnende einfache  Anführungszeichen", "Common.Views.SymbolTableDialog.textSpecial": "Sonderzeichen", "Common.Views.SymbolTableDialog.textSymbols": "Symbole", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Registered Trade Mark", "Common.Views.UserNameDialog.textDontShow": "Nicht mehr anzeigen", "Common.Views.UserNameDialog.textLabel": "Bezeichnung:", "Common.Views.UserNameDialog.textLabelError": "<PERSON><PERSON><PERSON>nung darf nicht leer sein.", "SSE.Controllers.DataTab.textColumns": "Spalten", "SSE.Controllers.DataTab.textEmptyUrl": "Geben Sie URL ein.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Text in Spalten", "SSE.Controllers.DataTab.txtDataValidation": "Datenüberprüfung", "SSE.Controllers.DataTab.txtErrorExternalLink": "Fehler: Aktualisierung fehlgeschlagen", "SSE.Controllers.DataTab.txtExpand": "erweitern", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Die Daten neben der Auswahlliste werden nicht entfernt. Möchten Sie die Auswahlliste erweitern, um nebenstehende Angaben einzusch<PERSON>ßen oder nur mit ausgewählten Zellen fortsetzen?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Die Auswahl enthält einige Zellen ohne Einstellungen für die Datenüberprüfung.<br>Soll die Datenüberprüfung auf diese Zellen erweitert werden?", "SSE.Controllers.DataTab.txtImportWizard": "Textimport-Assistent", "SSE.Controllers.DataTab.txtRemDuplicates": "Entferne Duplikate", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Die Auswahl enthält mehr als eine Prüfungsart.<br>Sollen die aktuellen Einstellungen gelöscht und dann fortgefahren werden?", "SSE.Controllers.DataTab.txtRemSelected": "Aus dem ausgewählten Bereich entfernen", "SSE.Controllers.DataTab.txtUrlTitle": "In Daten-URL einfügen", "SSE.Controllers.DocumentHolder.alignmentText": "Ausrichtung", "SSE.Controllers.DocumentHolder.centerText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteColumnText": "Spalte löschen", "SSE.Controllers.DocumentHolder.deleteRowText": "Zeile löschen", "SSE.Controllers.DocumentHolder.deleteText": "Löschen", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Der Linkverweis existiert nicht. Bitte korrigieren Sie das Link oder löschen Sie es. ", "SSE.Controllers.DocumentHolder.guestText": "Gas<PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Spalte nach links", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Spalte nach rechts", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON> un<PERSON>", "SSE.Controllers.DocumentHolder.insertText": "Einfügen", "SSE.Controllers.DocumentHolder.leftText": "Links", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Autokorrektur-Optionen", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Spaltenbreite {0} Symbole ({1} Pixel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Zeilenhöhe {0} Punkte ({1} Pixel)", "SSE.Controllers.DocumentHolder.textCtrlClick": "<PERSON><PERSON><PERSON> Si<PERSON> auf den Link, um ihn zu ö<PERSON>nen, oder halten Sie die Maustaste gedrückt, um die Z<PERSON> auszuw<PERSON>hlen.", "SSE.Controllers.DocumentHolder.textInsertLeft": "<PERSON><PERSON> ein<PERSON>ügen", "SSE.Controllers.DocumentHolder.textInsertTop": "<PERSON><PERSON> e<PERSON>gen", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Spezielles Einfügen", "SSE.Controllers.DocumentHolder.textStopExpand": "Automatische Tabellenerweiterung anhalten", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Das Element wird gerade von einem anderen Benutzer bearbeitet.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Überdurch<PERSON><PERSON><PERSON>lich", "SSE.Controllers.DocumentHolder.txtAddBottom": "Unterer Rand hinzufügen", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Bruchstrich einfügen", "SSE.Controllers.DocumentHolder.txtAddHor": "Horizontale Linie einfügen", "SSE.Controllers.DocumentHolder.txtAddLB": "Linke untere Linie einfügen", "SSE.Controllers.DocumentHolder.txtAddLeft": "Linke Grenze hinzufügen", "SSE.Controllers.DocumentHolder.txtAddLT": "Linke obere Linie einfügen", "SSE.Controllers.DocumentHolder.txtAddRight": "Rechter Rand hinzufügen", "SSE.Controllers.DocumentHolder.txtAddTop": "Oberer Rand hinzufügen", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON><PERSON> Lin<PERSON>", "SSE.Controllers.DocumentHolder.txtAlignToChar": "An einem Zeichen ausrichten", "SSE.Controllers.DocumentHolder.txtAll": "(All<PERSON>)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Gibt den gesamten Inhalt der Tabelle oder der angegebenen Tabellenspalten zurück, einschließlich der Spaltenüberschriften, der Daten und der Gesamtergebnisse", "SSE.Controllers.DocumentHolder.txtAnd": "und", "SSE.Controllers.DocumentHolder.txtBegins": "beginnt mit", "SSE.Controllers.DocumentHolder.txtBelowAve": "Unterdu<PERSON><PERSON><PERSON><PERSON>lich", "SSE.Controllers.DocumentHolder.txtBlanks": "(<PERSON><PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Rahmeneigenschaften", "SSE.Controllers.DocumentHolder.txtBottom": "Unten", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON>lt<PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Spaltenausrichtung", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "<PERSON> wurde in die Zwischenablage kopiert", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Gibt die Datenzellen der Tabelle oder der angegebenen Tabellenspalten zurück", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Argumentgröße reduzieren", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Argument löschen", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON>len Umbruch löschen", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Einschlusszeichen löschen", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Einschlusszeichen und Trennzeichen löschen", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Formel löschen", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Zeichen löschen", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEnds": "<PERSON><PERSON> mit", "SSE.Controllers.DocumentHolder.txtEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Gleich der Zellfarbe", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Gleich der Schriftfarbe", "SSE.Controllers.DocumentHolder.txtExpand": "Erweitern und sortieren", "SSE.Controllers.DocumentHolder.txtExpandSort": "Die Daten neben der Auswahlliste werden nicht sortiert. Möchten Sie die Auswahlliste erweitern, um nebenstehende Angaben einzuschließen oder nur mit Sortieren derzeit ausgewählten Zellen fortzusetzen.", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Unten", "SSE.Controllers.DocumentHolder.txtFilterTop": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Zu linearer Bruchrechnung ändern", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> verzerrter Bruchrechnung ändern", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> verzerrter Bruchrechnung ändern", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich wie ", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Zeichen über dem Text ", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON> unter dem Text ", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Gibt die Spaltenüberschriften für die Tabelle oder die angegebenen Tabellenspalten zurück", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "Untere Rahmenlinie verbergen", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Untere Grenze verbergen", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Schließende Klammer verbergen", "SSE.Controllers.DocumentHolder.txtHideDegree": "Grad verbergen", "SSE.Controllers.DocumentHolder.txtHideHor": "Horizontale Linie verbergen", "SSE.Controllers.DocumentHolder.txtHideLB": "Linke untere Line verbergen", "SSE.Controllers.DocumentHolder.txtHideLeft": "Linker Rand verbergen", "SSE.Controllers.DocumentHolder.txtHideLT": "Linke obere Linie verbergen", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Öffnende Klammer verbergen", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Platzhalter verbergen", "SSE.Controllers.DocumentHolder.txtHideRight": "Rahmenlinie rechts verbergen", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> oben verbergen", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Obergrenze verbergen", "SSE.Controllers.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON> Lin<PERSON> verb<PERSON>gen", "SSE.Controllers.DocumentHolder.txtImportWizard": "Textimport-Assistent", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Argumentgröße erh<PERSON>hen", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Argument nachher einfügen", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Argument vorher einfügen", "SSE.Controllers.DocumentHolder.txtInsertBreak": "<PERSON><PERSON> Umbruch einfügen", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Formel nachher einfügen", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Formel vorher einfügen", "SSE.Controllers.DocumentHolder.txtItems": "Elemente", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Nur Text beibehalten", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON> als", "SSE.Controllers.DocumentHolder.txtLessEquals": "<PERSON><PERSON> als oder gleich wie", "SSE.Controllers.DocumentHolder.txtLimitChange": "Grenzwerten ändern ", "SSE.Controllers.DocumentHolder.txtLimitOver": "Grenzen über dem Text", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON> unter dem <PERSON>", "SSE.Controllers.DocumentHolder.txtLockSort": "Neben dem ausgewählten Bereich wurde Daten gefunden, aber Si<PERSON> haben keine Berechtigung, diese <PERSON><PERSON> zu verändern.<br><PERSON><PERSON><PERSON><PERSON> Sie mit dem ausgewählten Bereich weiter arbeiten?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON>ckige Klammern an Argumenthöhe anpassen", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Matrixausrichtung", "SSE.Controllers.DocumentHolder.txtNoChoices": "<PERSON>s gibt keine Möglichkeit zum Füllung der Zelle.<br><PERSON>ur die Textwerte aus der Spalte kann für den Ersatz gewählt werden. ", "SSE.Controllers.DocumentHolder.txtNotBegins": "Be<PERSON>nt nicht mit", "SSE.Controllers.DocumentHolder.txtNotContains": "Enthält kein(e/en)", "SSE.Controllers.DocumentHolder.txtNotEnds": "<PERSON>et nicht mit", "SSE.Controllers.DocumentHolder.txtNotEquals": "<PERSON>t nicht gleich", "SSE.Controllers.DocumentHolder.txtOr": "oder", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON> über dem <PERSON>", "SSE.Controllers.DocumentHolder.txtPaste": "Einfügen", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formel ohne Rahmenlinien", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formel + Spaltenbreite", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Zielformatierung", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Nur Formatierung einfügen", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formel + Zahlenformat", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Nur Formel einfügen", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formel + alle Formatierungen", "SSE.Controllers.DocumentHolder.txtPasteLink": "Hyperlink einfügen", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Verknüpfte Grafik", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Bedingte Formatierung beim Verbinden", "SSE.Controllers.DocumentHolder.txtPastePicture": "Bild", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Ursprüngliche Formatierung ", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Vert<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Wert + alle Formatierung", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Wert + Zahlenformat", "SSE.Controllers.DocumentHolder.txtPasteValues": "<PERSON><PERSON> die Werte einfügen", "SSE.Controllers.DocumentHolder.txtPercent": "Prozent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Automatische Tabellenerweiterung wiederholen", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Bruchstrich entfernen", "SSE.Controllers.DocumentHolder.txtRemLimit": "Grenzwert entfernen", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Akzentzeichen entfernen", "SSE.Controllers.DocumentHolder.txtRemoveBar": "<PERSON><PERSON><PERSON> en<PERSON>fernen", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Möchten Sie diese Signatur wirklich entfernen?<br>Dies kann nicht rückgängig gemacht werden.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Skripts entfernen", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Tiefstellung entfernen", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Hochstellung entfernen", "SSE.Controllers.DocumentHolder.txtRowHeight": "Zeilenhöhe", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "<PERSON><PERSON><PERSON> nach dem Text", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts vor dem Text", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Untere Grenze zeigen", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Schließende eckige Klammer anzeigen", "SSE.Controllers.DocumentHolder.txtShowDegree": "Grad anzeigen", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Öffnende eckige Klammer anzeigen", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Platzhaltertext anzeigen", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Höchstgrenze anzeigen", "SSE.Controllers.DocumentHolder.txtSorting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ausgewählte sortieren", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtThisRowHint": "<PERSON>ur diese Zeile der notwendigen Spalte auswählen", "SSE.Controllers.DocumentHolder.txtTop": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Gibt Gesamtergebnisse der Zeilen für die Tabelle oder die angegebenen Tabellenspalten zurück", "SSE.Controllers.DocumentHolder.txtUnderbar": "<PERSON><PERSON> unter dem <PERSON> ", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Automatische Erweiterung der Tabelle rückgängig machen", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Text Import-Assistenten verwenden", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Dieser Link kann für Ihr Gerät und Daten gefährlich sein.<br><PERSON><PERSON>cht<PERSON> Sie wirklich fortsetzen?", "SSE.Controllers.DocumentHolder.txtWidth": "Breite", "SSE.Controllers.FormulaDialog.sCategoryAll": "Alle", "SSE.Controllers.FormulaDialog.sCategoryCube": "C<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Datenbank", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Datum und Uhrzeit", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Konstruktion", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finanzmathematik", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Information", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 zuletzt verwendete", "SSE.Controllers.FormulaDialog.sCategoryLogical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Suchen und Bezüge", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Mathematik und Trigonometrie", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistik", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text und Daten", "SSE.Controllers.LeftMenu.newDocumentTitle": "Unbetitelte Kalkulationstabelle", "SSE.Controllers.LeftMenu.textByColumns": "Spaltenweise", "SSE.Controllers.LeftMenu.textByRows": "Zeilenweise", "SSE.Controllers.LeftMenu.textFormulas": "Formeln", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Versionshistorie wird geladen...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON> in", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON> Daten, nach denen <PERSON> gesucht haben, können nicht gefunden werden. Bitte ändern Sie die Suchparameter.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Der Ersatzvorgang wurde durchgeführt. {0} Vorkommen wurden ausgelassen.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Der Suchvorgang wurde durchgeführt. Vorkommen wurden ersetzt:{0}", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSheet": "Sheet", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "Achtung", "SSE.Controllers.LeftMenu.textWithin": "Innerhalb", "SSE.Controllers.LeftMenu.textWorkbook": "Arbeitsmappe", "SSE.Controllers.LeftMenu.txtUntitled": "Unbenannt", "SSE.Controllers.LeftMenu.warnDownloadAs": "Wenn Si<PERSON> mit dem Speichern in diesem Format fortsetzen, werden alle Objekte außer Text verloren gehen.<br>M<PERSON>chten Sie wirklich fortsetzen?", "SSE.Controllers.Main.confirmAddCellWatches": "Diese Aktion wird {0} Zell-Überwachungen hinzufügen.<br>Wollen Sie fortsetzen ?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Diese Aktion fügt nur {0} Zellüberwachungen hinzu, um den Speicher zu sparen.<br>Möchten Sie fortsetzen?", "SSE.Controllers.Main.confirmMaxChangesSize": "Die Anzahl der Aktionen überschreitet die für Ihren Server festgelegte Grenze.<br><PERSON><PERSON><PERSON> \"Rückgängig\", um Ihre letzte Aktion abzubrechen, oder drücken Sie \"Weiter\", um die Aktion lokal fortzusetzen (Sie müssen die Datei herunterladen oder ihren Inhalt kopieren, um sicherzustellen, dass nichts verloren geht).", "SSE.Controllers.Main.confirmMoveCellRange": "Der Zielzellenbereich kann Daten enthalten. Die Operation fortsetzen?", "SSE.Controllers.Main.confirmPutMergeRange": "Die Quelldaten enthielten verbundene Zellen. <br> <PERSON><PERSON> dem Einfügen dieser Zellen in die Tabelle, wurde die Zusammenführung aufgehoben. ", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formeln in der Kopfzeile werden entfernt und in statischen Text konvertiert.<br>M<PERSON>cht<PERSON> Sie den Vorgang fortsetzen?", "SSE.Controllers.Main.convertationTimeoutText": "Timeout für die Konvertierung wurde überschritten.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON> Sie auf 'OK', um zur Dokumentenliste zu übergehen.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "Herunterladen ist fehlgeschlagen.", "SSE.Controllers.Main.downloadTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wird heruntergeladen...", "SSE.Controllers.Main.downloadTitleText": "Herunterladen der Kalkulationstabelle", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON> doppelten Werte gefunden.", "SSE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON>, eine Aktion durchzuführen, für die Si<PERSON> keine Rechte haben.<br><PERSON>te wenden Sie sich an Ihren Document Serveradministrator.", "SSE.Controllers.Main.errorArgsRange": "Die eingegebene Formel enthält einen Fehler.<br>Es wird falschen Argumentbereich genutzt.", "SSE.Controllers.Main.errorAutoFilterChange": "Der Vorgang ist nicht zu<PERSON>g, denn es wurde versucht, <PERSON><PERSON><PERSON> in der Tabelle auf Ihrem Arbeitsblatt zu verschieben.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Dieser Vorgang kann für die gewählten Zellen nicht ausgeführt werden, weil Sie ein Teil der Tabelle nicht verschieben können.<br>W<PERSON>hlen Sie den anderen Datenbereich, so dass die ganze Tabelle verschoben wurde und versuchen Si<PERSON> noch einmal.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Der Vorgang kann für einen ausgewählten Zellbereich nicht ausgeführt werden.<br>Wählen Sie einen einheitlichen Datenbereich, der sich deutlich von dem bestehenden unterscheidet und versuchen Sie es erneut.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Die Operation kann nicht ausgeführt werden, weil der Bereich gefilterte Zellen enthält.<br><PERSON>te machen Sie die gefilterten Elemente sichtbar und versuchen Si<PERSON> es erneut.", "SSE.Controllers.Main.errorBadImageUrl": "URL des Bildes ist falsch", "SSE.Controllers.Main.errorCannotUngroup": "Gruppierung kann nicht aufgehoben werden. Um eine Gliederung zu erstellen, wählen Sie Zeilen oder Spalten aus und gruppieren Sie diese.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Dieser Befehl kann für ein geschütztes Blatt nicht verwendet werden. Sie müssen zu<PERSON>t den Schutz des Blatts aufheben, um diesen Befehl zu verwenden.<br>Sie werden möglicherweise aufgefordert, ein Kennwort einzugeben.", "SSE.Controllers.Main.errorChangeArray": "<PERSON>e können einen Teil eines Arrays nicht ändern.", "SSE.Controllers.Main.errorChangeFilteredRange": "Hierd<PERSON><PERSON> wird ein gefilterter Bereich in Ihrem Arbeitsblatt geändert.<br> Um diesen Vorgang abzuschließen, entfernen Si<PERSON> bitte die AutoFilter.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Die Zelle oder das Diagramm, die Si<PERSON> bearbeiten möchten, ist in der geschützten Liste.<br>Entschützen Sie die Liste, um Änderungen vorzunehmen. Das Passwort kann erforderlich sein.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Verbindung zum Server ist verloren gegangen. Das Dokument kann momentan nicht bearbeitet werden.", "SSE.Controllers.Main.errorConnectToServer": "Das Dokument konnte nicht gespeichert werden. Bitte überprüfen Sie die Verbindungseinstellungen oder wenden Si<PERSON> sich an Ihren Administrator.<br><PERSON><PERSON> <PERSON><PERSON> auf die Schaltfläche \"OK\" klicken, werden Sie aufgefordert das Dokument herunterzuladen.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Bei einer Markierung von nicht angrenzenden Zellen ist die Ausführung dieses Befehls nicht möglich.<br><PERSON><PERSON>hlen Sie nur einen einzelnen Bereich aus, und versuchen Sie es noch mal.", "SSE.Controllers.Main.errorCountArg": "Die eingegebene Formel enthält einen Fehler.<br>Falsche Anzahl an Argumenten wurde genutzt.", "SSE.Controllers.Main.errorCountArgExceed": "Die eingegebene Formel enthält einen Fehler.<br><PERSON><PERSON><PERSON> der Argumente wurde überschritten.", "SSE.Controllers.Main.errorCreateDefName": "Die bestehende benannte Bereiche können nicht bearbeitet werden und die neuen Bereiche können<br>im Moment nicht erstellt werden, weil einige von ihnen sind in Bearbeitung.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> Fehler.<br><PERSON><PERSON> beim Verbinden zur Datenbank. Bitte wenden Si<PERSON> sich an den Kundendienst, falls der Fehler bestehen bleibt.", "SSE.Controllers.Main.errorDataEncrypted": "Änderungen wurden verschlüsselt. Sie können nicht entschlüsselt werden.", "SSE.Controllers.Main.errorDataRange": "Falscher Datenbereich.", "SSE.Controllers.Main.errorDataValidate": "Der eingegebene Wert ist ungültig.<br><PERSON> Werte, die in diese Zelle eingegeben werden können, sind begrenzt.", "SSE.Controllers.Main.errorDefaultMessage": "Fehlercode: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "<PERSON><PERSON>, eine Spalte mit einer gesperrten Zelle zu löschen. Gesperrte Zellen können in geschützten Listen nicht gelöscht werden.<br>Um eine gesperrte Zelle zu löschen, entschützen Sie die Liste. Das Passwort kann erforderlich sein.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "<PERSON><PERSON>, eine Zeil<PERSON> mit einer gesperrten Zelle zu löschen. Gesperrte Zellen können in geschützten Listen nicht gelöscht werden.<br>Um eine gesperrte Zelle zu löschen, entschützen Sie die Liste. Das Passwort kann erforderlich sein.", "SSE.Controllers.Main.errorDirectUrl": "Bitte überprüfen Sie den Link zum Dokument.<br><PERSON>ser Link muss ein direkter Link zu der Datei zum Herunterladen sein.", "SSE.Controllers.Main.errorEditingDownloadas": "Bei der Arbeit mit dem Dokument ist ein Fehler aufgetreten. <br> Verwenden Sie die Option 'Herunterladen als', um die Sicherungskopie der Datei auf der Festplatte Ihres Computers zu speichern.", "SSE.Controllers.Main.errorEditingSaveas": "Bei der Arbeit mit dem Dokument ist ein Fehler aufgetreten. <br> Verwenden Sie die Option \"Speichern als ...\", um die Sicherungskopie der Datei auf der Festplatte Ihres Computers zu speichern.", "SSE.Controllers.Main.errorEditView": "Die aktuelle Tabellenansicht sind schreibgeschützt und die neuen können nicht erstellt werden, weil manche Ansichten bearbeitet werden.", "SSE.Controllers.Main.errorEmailClient": "<PERSON>s wurde kein E-Mail-Client gefunden.", "SSE.Controllers.Main.errorFilePassProtect": "Das Dokument ist kennwortgeschützt und kann nicht geöffnet werden.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON>.<br>Fehler bei der Dateianfrage. Bitte wenden Si<PERSON> sich an den Kundendienst, falls der Fehler bestehen bleibt.", "SSE.Controllers.Main.errorFileSizeExceed": "Die Dateigröße überschreitet die für Ihren Server festgelegte Einschränkung.<br>Weitere Informationen können Sie von Ihrem Document Server-Administrator er<PERSON><PERSON>.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON> Fehler.<br>Ungültiger Sicherheitsschlüssel. Bitte wenden Si<PERSON> sich an den Kundendienst, falls der Fehler bestehen bleibt.", "SSE.Controllers.Main.errorFillRange": "Der gewählte Zellbereich kann nicht ausgefüllt werden.<br>Alle verbundenen Zellen müssen die gleiche Größe haben.", "SSE.Controllers.Main.errorForceSave": "<PERSON>im Speichern der Datei ist ein Fehler aufgetreten. Verwenden Sie die Option \"Herunterladen als\", um die Datei auf Ihrer Computerfestplatte zu speichern oder versuchen Sie es später erneut.", "SSE.Controllers.Main.errorFormulaName": "Die eingegebene Formel enthält einen Fehler. <br>Falscher Name der Formel wurde genutzt.", "SSE.Controllers.Main.errorFormulaParsing": "Interner Fehler bei der Syntaxanalyse der Formel.", "SSE.Controllers.Main.errorFrmlMaxLength": "Ihre Formel ist länger als 8192 Symbole.<br>Bitte bearbeiten Sie diese und versuchen Sie neu.", "SSE.Controllers.Main.errorFrmlMaxReference": "<PERSON>e können solche Formel nicht e<PERSON>ben, denn <PERSON> zu viele Werte,<br>Zellbezüge und/oder Namen beinhaltet.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Textwerte in Formeln sind auf 255 Zeichen begrenzt.<br>Verwenden Sie die Funktion VERKETTEN oder den Verkettungsoperator (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Die Funktion bezieht sich auf ein <PERSON>, das nicht existiert.<br>Bitte überprüfen Sie die Daten und versuchen Si<PERSON> es erneut.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Die Operation konnte nicht", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Die Operation konnte nicht", "SSE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei stimmt nicht mit der Dateierweiterung überein.", "SSE.Controllers.Main.errorInconsistentExtDocx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Textdokumenten (z.B. docx), aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht einem der folgenden Formate: pdf/djvu/xps/oxps, aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Präsentationen (z.B. pptx), aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Tabellenkalkulationen (z.B. xlsx), aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.Controllers.Main.errorInvalidRef": "Geben Si<PERSON> einen korrekten Namen oder einen gültigen Webverweis ein.", "SSE.Controllers.Main.errorKeyEncrypt": "Unbekannter Schlüsseldeskriptor", "SSE.Controllers.Main.errorKeyExpire": "Der Schlüsseldeskriptor ist abgelaufen", "SSE.Controllers.Main.errorLabledColumnsPivot": "Um eine Pivot-<PERSON><PERSON><PERSON> zu erstellen, verwenden Si<PERSON> Daten, die in einer Liste mit Spaltenüberschriften organisiert sind.", "SSE.Controllers.Main.errorLoadingFont": "Schriftarten nicht hochgeladen.<br><PERSON><PERSON> wenden <PERSON> sich an <PERSON><PERSON> von Ihrem Document Server.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Die Referenz für den Standort oder den Datenbereich ist nicht gültig.", "SSE.Controllers.Main.errorLockedAll": "Die Operation kann nicht durchgeführt werden, weil das Blatt von e<PERSON>m anderen Benutzer gesperrt ist.", "SSE.Controllers.Main.errorLockedCellPivot": "Die Daten innerhalb einer Pivot-Tabelle können nicht geändert werden.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Umbenennen des Blattes ist derzeit nicht möglich, denn es wird gleichzeitig von einem anderen Benutzer umbenannt.", "SSE.Controllers.Main.errorMaxPoints": "Die maximale Punktzahl pro eine Tabelle beträgt 4096 Punkte.", "SSE.Controllers.Main.errorMoveRange": "Es ist unmöglich einen Teil der vereinigten Zelle zu ändern", "SSE.Controllers.Main.errorMoveSlicerError": "Datenschnitte können aus einer Arbeitsmappe in die andere nicht verschoben werden.<br>W<PERSON>hlen Sie die ganze Tabelle und Datenschnitte aus und versuchen Si<PERSON> erneut.", "SSE.Controllers.Main.errorMultiCellFormula": "Matrixformeln mit mehreren Zellen sind in Tabellen nicht zulässig.", "SSE.Controllers.Main.errorNoDataToParse": "<PERSON>s wurden keine Daten zur Analyse markiert.", "SSE.Controllers.Main.errorOpenWarning": "Die Länge einer der Formeln in der Datei hat<br>die zugelassene Anzahl von Zeichen überschritten und sie wurde entfernt.", "SSE.Controllers.Main.errorOperandExpected": "Die Syntax der eingegeben Funktion ist nicht korrekt. Bitte überprüfen Sie, ob eine der Klammern - '(' oder ')' fehlt.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Das eingegebene Kennwort ist ungültig.<br><PERSON><PERSON><PERSON>, dass die FESTSTELLTASTE nicht aktiviert ist und dass Sie die korrekte Groß-/Kleinschreibung verwenden.", "SSE.Controllers.Main.errorPasteMaxRange": "Zeilen Kopieren und Einfügen stimmen nicht überein.<br>Bitte wählen Si<PERSON> einen Bereich der gleichen Größe oder klicken auf die erste Zelle der Zeile, um die kopierten Zellen einzufügen.", "SSE.Controllers.Main.errorPasteMultiSelect": "Dies kann nicht mit einer Mehrfachauswahl ausgeführt werden.<br><PERSON><PERSON>hlen Sie nur einen einzelnen Bereich aus und versuchen Sie es noch mal.", "SSE.Controllers.Main.errorPasteSlicerError": "Datensch<PERSON>tte können aus einer Arbeitsmappe in die andere nicht verschoben werden.", "SSE.Controllers.Main.errorPivotGroup": "Die markierten Objekte können nicht gruppiert werden.", "SSE.Controllers.Main.errorPivotOverlap": "ein Pivot Tabellenbericht kann nicht", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Der Bericht einer Pivot-Tabelle wurde ohne die zugrunde liegenden Daten gespeichert.<br><PERSON><PERSON><PERSON> Sie auf 'Aktualisieren', um den Bericht zu aktualisieren.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON>ider kann man in der aktuellen Programmversion nicht mehr als 1500 Seiten gleichzeitig drucken.<br>Diese Einschränkung wird in den kommenden Versionen entfernt.", "SSE.Controllers.Main.errorProcessSaveResult": "Speichern ist fehlgeschlagen", "SSE.Controllers.Main.errorServerVersion": "Editor-Version wurde aktualisiert. Die Seite wird neu geladen, um die Änderungen zu übernehmen.", "SSE.Controllers.Main.errorSessionAbsolute": "Die Bearbeitungssitzung des Dokumentes ist abgelaufen. Laden Sie die Seite neu.", "SSE.Controllers.Main.errorSessionIdle": "Das Dokument wurde lange nicht bearbeitet. Laden Sie die Seite neu.", "SSE.Controllers.Main.errorSessionToken": "Die Verbindung zum Server wurde unterbrochen. Laden Sie die Seite neu.", "SSE.Controllers.Main.errorSetPassword": "Das Passwort konnte nicht festgelegt werden.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Der Ortsbezug ist nicht gültig, da sich die Zellen nicht alle in derselben Spalte oder Zeile befinden.<br><PERSON><PERSON><PERSON><PERSON> Sie Zellen aus, die sich alle in einer einzigen Spalte oder Zeile befinden.", "SSE.Controllers.Main.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "SSE.Controllers.Main.errorToken": "Sicherheitstoken des Dokuments ist nicht korrekt.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "SSE.Controllers.Main.errorTokenExpire": "Sicherheitstoken des Dokuments ist abgelaufen.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "SSE.Controllers.Main.errorUnexpectedGuid": "<PERSON><PERSON><PERSON> Fehler.<br>Unerwartete GUID. Bitte wenden Sie sich an den Kundendienst, falls der Fehler bestehen bleibt.", "SSE.Controllers.Main.errorUpdateVersion": "Die Dateiversion wurde geändert. Die Seite wird neu geladen.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Die Internetverbindung wurde wiederhergestellt und die Dateiversion wurde geändert.<br><PERSON><PERSON> weiterarbeiten können, müssen Sie die Datei herunterladen oder den Inhalt kopieren, um sicherzustellen, dass nichts verloren geht, und diese Seite anschließend neu laden.", "SSE.Controllers.Main.errorUserDrop": "<PERSON><PERSON> auf diese Datei ist möglich.", "SSE.Controllers.Main.errorUsersExceed": "Die nach dem Zahlungsplan erlaubte Benutzeranzahl ist überschritten", "SSE.Controllers.Main.errorViewerDisconnect": "Die Verbindung ist unterbrochen. Man kann das Dokument weiterhin anschauen.<br><PERSON><PERSON> ist aber momentan nicht möglich, es herunterzuladen oder zu drucken bis die Verbindung wiederhergestellt <br>und die Seite neu geladen wird.", "SSE.Controllers.Main.errorWrongBracketsCount": "Die eingegebene Formel enthält einen Fehler.<br>Falsche Anzahl an Klammern wurde genutzt.", "SSE.Controllers.Main.errorWrongOperator": "Die eingegebene Formel enthält einen Fehler. Falscher Operator wurde genutzt.<br>Bitte korrigieren Si<PERSON> den Fehler.", "SSE.Controllers.Main.errorWrongPassword": "Inkorrektes Passwort.", "SSE.Controllers.Main.errRemDuplicates": "Duplizierte Werte gefunden und gelöscht: {0}, Werte geblieben: {1}.", "SSE.Controllers.Main.leavePageText": "In dieser Kalkulationstabelle gibt es nicht gespeicherte Änderungen. Klicken Sie auf 'Auf dieser Seite bleiben' und dann auf 'Speichern', um sie zu speichern. Klicken Sie auf 'Diese Seite verlassen', um alle nicht gespeicherten Änderungen zu verwerfen.", "SSE.Controllers.Main.leavePageTextOnClose": "Alle ungespeicherten Änderungen in dieser Tabellenkalkulation werden verloren.<br> <PERSON><PERSON><PERSON> <PERSON><PERSON> auf \"Abbrechen\" und anschließend auf \"Speichern\", um die Änderungen zu speichern. Klicken Sie auf den Button \"OK\", so werden alle ungespeicherten Änderungen verloren gehen. ", "SSE.Controllers.Main.loadFontsTextText": "Daten werden geladen...", "SSE.Controllers.Main.loadFontsTitleText": "Daten werden geladen", "SSE.Controllers.Main.loadFontTextText": "Daten werden geladen...", "SSE.Controllers.Main.loadFontTitleText": "Daten werden geladen", "SSE.Controllers.Main.loadImagesTextText": "Bilder werden geladen...", "SSE.Controllers.Main.loadImagesTitleText": "Bilder werden geladen", "SSE.Controllers.Main.loadImageTextText": "Bild wird geladen...", "SSE.Controllers.Main.loadImageTitleText": "Bild wird geladen", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON>e wird geladen", "SSE.Controllers.Main.notcriticalErrorTitle": "Achtung", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON> dieser Datei ist ein Fehler aufgetreten.", "SSE.Controllers.Main.openTextText": "Kalkulationstabelle wird geöffnet...", "SSE.Controllers.Main.openTitleText": "Kalkulationstabelle wird ge<PERSON>ffnet", "SSE.Controllers.Main.pastInMergeAreaError": "<PERSON><PERSON> ist unmöglich, einen Teil der vereinigten Zelle zu ändern", "SSE.Controllers.Main.printTextText": "Kalkulation<PERSON>bell<PERSON> wird gedruckt...", "SSE.Controllers.Main.printTitleText": "Drucken der Kalkulationstabelle", "SSE.Controllers.Main.reloadButtonText": "Seite neu laden", "SSE.Controllers.Main.requestEditFailedMessageText": "Das Dokument wurde gerade von einem anderen Benutzer bearbeitet. Bitte versuchen Sie es später erneut.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> verweigert", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON> dieser Datei ist ein Fehler aufgetreten.", "SSE.Controllers.Main.saveErrorTextDesktop": "Diese Datei kann nicht erstellt oder gespeichert werden.<br>Dies ist möglicherweise davon verursacht: <br>1. Die Datei ist schreibgeschützt. <br>2. Die Datei wird von anderen Benutzern bearbeitet. <br>3. Die Festplatte ist voll oder beschädigt.", "SSE.Controllers.Main.saveTextText": "Kalkulationstabelle wird gespeichert...", "SSE.Controllers.Main.saveTitleText": "Speichern der Kalkulationstabelle", "SSE.Controllers.Main.scriptLoadError": "Die Verbindung ist zu langsam, einige der Komponenten konnten nicht geladen werden. Bitte laden Sie die Se<PERSON> erneut.", "SSE.Controllers.Main.textAnonymous": "Anonym", "SSE.Controllers.Main.textApplyAll": "<PERSON>ür alle Gleichungen verwenden", "SSE.Controllers.Main.textBuyNow": "Webseite besuchen", "SSE.Controllers.Main.textChangesSaved": "Alle Änderungen wurden gespeichert", "SSE.Controllers.Main.textClose": "Schließen", "SSE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON>, um den Tipp zu schließen", "SSE.Controllers.Main.textConfirm": "Bestätigung", "SSE.Controllers.Main.textContactUs": "Verkaufsteam kontaktieren", "SSE.Controllers.Main.textContinue": "Fortsetzen", "SSE.Controllers.Main.textConvertEquation": "Diese Gleichung wurde in einer alten Version des Gleichungseditors erstellt, die nicht mehr unterstützt wird. Um die Gleichung zu bearbeiten, konvertieren Sie diese ins Format Office Math ML. <br><PERSON>zt konvertieren?", "SSE.Controllers.Main.textCustomLoader": "<PERSON>te beachten Si<PERSON>, dass Si<PERSON> gemäß den Lizenzbedingungen nicht berechtigt sind, den Loader zu wechseln. <br> <PERSON><PERSON> sich an unseren Vertrieb, um ein Angebot zu erhalten.", "SSE.Controllers.Main.textDisconnect": "Verbindung wurde unterbrochen", "SSE.Controllers.Main.textFillOtherRows": "Andere Zeilen ausfüllen", "SSE.Controllers.Main.textFormulaFilledAllRows": "Die Formel hat {0} <PERSON>eil<PERSON> mit Daten ausgefüllt. Das Ausfüllen anderer leerer Zeilen kann einige Minuten dauern.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Die Formel hat die ersten {0} Zeilen mit Daten ausgefüllt. Das Ausfüllen anderer leerer Zeilen kann einige Minuten dauern.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Die Formel hat nur die ersten {0} Zeilen ausgefüllt, um Speicherplatz zu sparen. Es gibt weitere {1} Zeilen mit Daten in diesem Blatt. Sie können sie manuell ausfüllen.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Die Formel hat nur die ersten {0} Zeilen ausgefüllt, um Speicherplatz zu sparen. Die anderen Zeilen in diesem Blatt enthalten keine Daten.", "SSE.Controllers.Main.textGuest": "Gas<PERSON>", "SSE.Controllers.Main.textHasMacros": "Die Datei beinhaltet automatische Makros.<br><PERSON><PERSON><PERSON><PERSON> Sie Makros ausführen?", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON> er<PERSON>", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON>e wird geladen", "SSE.Controllers.Main.textLongName": "Der Name einer Tabellenansicht darf maximal 128 <PERSON><PERSON>chen lang sein.", "SSE.Controllers.Main.textNeedSynchronize": "Updates verf<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textNo": "<PERSON><PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "Lizenzlimit erreicht", "SSE.Controllers.Main.textPaidFeature": "Kostenpflichtige Funktion", "SSE.Controllers.Main.textPleaseWait": "Der Vorgang könnte mehr Zeit in Anspruch nehmen als erwartet. Bitte warten...", "SSE.Controllers.Main.textReconnect": "Verbindung wurde wiederhergestellt", "SSE.Controllers.Main.textRemember": "<PERSON><PERSON>rken", "SSE.Controllers.Main.textRememberMacros": "Auswahl für alle Makros speichern", "SSE.Controllers.Main.textRenameError": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein.", "SSE.Controllers.Main.textRenameLabel": "Geben Sie den Namen für Zusammenarbeit ein", "SSE.Controllers.Main.textRequestMacros": "Ein Makro stellt eine Anfrage an die URL. Möchten Sie die Anfrage an die %1 zulassen?", "SSE.Controllers.Main.textShape": "Form", "SSE.Controllers.Main.textStrict": "Formaler Modus", "SSE.Controllers.Main.textText": "Text", "SSE.Controllers.Main.textTryQuickPrint": "Sie haben Schnelldruck gewählt: Das gesamte Dokument wird auf dem zuletzt gewählten oder dem Standarddrucker gedruckt.<br><PERSON><PERSON> fort<PERSON>hren?", "SSE.Controllers.Main.textTryUndoRedo": "Undo/Redo Optionen sind  für den halbformalen Zusammenbearbeitungsmodus deaktiviert.<br><PERSON><PERSON><PERSON> <PERSON><PERSON> auf den Button \"Formaler Modus\", um den formalen Zusammenbearbeitungsmodus zu aktivieren, um die Datei, ohne Störungen anderer Benutzer zu bearbeiten und die Änderungen erst nachdem Sie sie gespeichert haben, zu senden. Sie können zwischen den Zusammenbearbeitungsmodi mit der Hilfe der erweiterten Einstellungen von Editor umschalten.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Die Optionen Rückgängig/Wiederholen sind für den halbformalen Zusammenbearbeitungsmodus deaktiviert.", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "SSE.Controllers.Main.textYes": "<PERSON>a", "SSE.Controllers.Main.titleLicenseExp": "Lizenz ist abgelaufen", "SSE.Controllers.Main.titleServerVersion": "Editor wurde aktualisi<PERSON>", "SSE.Controllers.Main.txtAccent": "Akzent", "SSE.Controllers.Main.txtAll": "(All<PERSON>)", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON> den <PERSON> e<PERSON>ben", "SSE.Controllers.Main.txtBasicShapes": "Standardformen", "SSE.Controllers.Main.txtBlank": "(leer)", "SSE.Controllers.Main.txtButtons": "Buttons", "SSE.Controllers.Main.txtByField": "%1 von %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "Diagramme", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtColLbls": "Spaltenbeschriftungen", "SSE.Controllers.Main.txtColumn": "<PERSON>lt<PERSON>", "SSE.Controllers.Main.txtConfidential": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDate": "Datum", "SSE.Controllers.Main.txtDays": "Tage", "SSE.Controllers.Main.txtDiagramTitle": "Diagrammtitel", "SSE.Controllers.Main.txtEditingMode": "Bearbeitungsmo<PERSON> e<PERSON>...", "SSE.Controllers.Main.txtErrorLoadHistory": "Laden der Historie ist fehlgeschlagen ", "SSE.Controllers.Main.txtFiguredArrows": "Geformte Pfeile", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Gesamtsumme", "SSE.Controllers.Main.txtGroup": "Gruppieren", "SSE.Controllers.Main.txtHours": "Stunden", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Mathematik", "SSE.Controllers.Main.txtMinutes": "Minuten", "SSE.Controllers.Main.txtMonths": "Monate", "SSE.Controllers.Main.txtMultiSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtOr": "%1 oder %2", "SSE.Controllers.Main.txtPage": "Seite", "SSE.Controllers.Main.txtPageOf": "Seite %1 von %2", "SSE.Controllers.Main.txtPages": "Seiten", "SSE.Controllers.Main.txtPreparedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "SSE.Controllers.Main.txtPrintArea": "Dr<PERSON>berei<PERSON>", "SSE.Controllers.Main.txtQuarter": "Qrtl", "SSE.Controllers.Main.txtQuarters": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "Zeilenbeschriftungen", "SSE.Controllers.Main.txtSeconds": "Sekunden", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Legende mit Linie 1 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Legende mit Linie 2 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Legende mit Linie 3 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "SSE.Controllers.Main.txtShape_accentCallout1": "Legende mit Linie 1 (Markierungsleiste)", "SSE.Controllers.Main.txtShape_accentCallout2": "Legende mit Linie 2 (Markierungsle<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "Legende mit Linie 3 (Markierungsle<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Schaltfläche \"Zurück\"", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON> \"Start\"", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Dokumentschaltfläche", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Schaltfläche „Beenden\"", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Schaltfläche 'Weiter'", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Schaltfläche \"Hilfe\"", "SSE.Controllers.Main.txtShape_actionButtonHome": "Schaltfläche \"Startseite\"", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Schaltfläche \"Informationen\"", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Schaltfläche \"Movie\"", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Schaltfläche „Zurück\"", "SSE.Controllers.Main.txtShape_actionButtonSound": "Schaltfläche \"Ton\"", "SSE.Controllers.Main.txtShape_arc": "Bogen", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Gewinkelte Verbindung", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Gewinkelte Verbindung mit Pfeil", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Gewinkelte Verbindung mit Doppelpfeil", "SSE.Controllers.Main.txtShape_bentUpArrow": "Nach oben gebogener Pfeil", "SSE.Controllers.Main.txtShape_bevel": "Schräge Kante", "SSE.Controllers.Main.txtShape_blockArc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_borderCallout1": "Legende mit Linie 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Legende mit Linie 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Legende mit Linie 3", "SSE.Controllers.Main.txtShape_bracePair": "Geschweifte Klammer links/rechts", "SSE.Controllers.Main.txtShape_callout1": "Legende mit Linie 1 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_callout2": "Legende mit Linie 2 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_callout3": "Legende mit Linie 3 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Akkord", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloud": "Cloud", "SSE.Controllers.Main.txtShape_cloudCallout": "Wolkenförmige Legende", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "C<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Gekrümmte Verbindung", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Gekrümmte Verbindung mit Pfeil", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Gekrümmte Verbindung mit Doppelpfeil", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Nach unten gekrümmter Pfeil", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Nach links gekrümmter Pfeil", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Nach rechts gekrümmter Pfeil", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Nach oben gekr<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_decagon": "Zeh<PERSON>", "SSE.Controllers.Main.txtShape_diagStripe": "Diagonaler Streifen", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "Doppelte Welle", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON> nach unten", "SSE.Controllers.Main.txtShape_downArrowCallout": "Legende mit Pfeil nach unten", "SSE.Controllers.Main.txtShape_ellipse": "Ellipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Nach unten gekrümmtes Band", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Nach oben gekrümmtes Band", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flussdiagramm: Alternativer Prozess", "SSE.Controllers.Main.txtShape_flowChartCollate": "Flussdiagramm: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartConnector": "Flussdiagramm: Verbindungsstelle", "SSE.Controllers.Main.txtShape_flowChartDecision": "Flussdiagramm: Verzweigung", "SSE.Controllers.Main.txtShape_flowChartDelay": "Flussdiagramm: Verzögerung", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Flussdiagramm: Anzeige", "SSE.Controllers.Main.txtShape_flowChartDocument": "Flussdiagramm: Dokument", "SSE.Controllers.Main.txtShape_flowChartExtract": "Flussdiagramm: Auszug", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Flussdiagramm: Daten", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Flussdiagramm: Zentralspeicher", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flussdiagramm: Magnetplattenspeicher", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flussdiagramm: Datenträger mit direktem Zugriff", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Flussdiagramm: Datenträger mit sequenziellem Zugriff", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Flussdiagramm: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Flussdiagramm: <PERSON><PERSON> Verarbeitung", "SSE.Controllers.Main.txtShape_flowChartMerge": "Flussdiagramm: Z<PERSON>mmenführen", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Flussdiagramm: <PERSON><PERSON>ere Dokumente", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flussdiagramm: Verbindungsstelle zu einer anderen Seite", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flussdiagramm: Gespeicherte Daten", "SSE.Controllers.Main.txtShape_flowChartOr": "Flussdiagramm", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flussdiagramm: Vordefinierter Prozess", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Flussdiagramm: Vorbereitung", "SSE.Controllers.Main.txtShape_flowChartProcess": "Flussdiagramm: Pro<PERSON>s", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Flussdiagramm: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Flussdiagramm: Lochstreifen", "SSE.Controllers.Main.txtShape_flowChartSort": "Flussdiagramm: Sortieren", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Flussdiagramm: Zusammenführung", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Flussdiagramm: Grenzstelle", "SSE.Controllers.Main.txtShape_foldedCorner": "Gefal<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "Sechseck", "SSE.Controllers.Main.txtShape_homePlate": "Richtungspfeil", "SSE.Controllers.Main.txtShape_horizontalScroll": "Horizontaler Bildlauf", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "SSE.Controllers.Main.txtShape_leftArrow": "Pfeil nach links", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Legende mit Pfeil nach links", "SSE.Controllers.Main.txtShape_leftBrace": "Geschweifte Klammer links", "SSE.Controllers.Main.txtShape_leftBracket": "Runde K<PERSON> links", "SSE.Controllers.Main.txtShape_leftRightArrow": "Pfeil nach links und rechts", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Legende mit Pfeil nach links und rechts", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Pfeil nach links, rechts und oben", "SSE.Controllers.Main.txtShape_leftUpArrow": "Pfeil nach links und oben", "SSE.Controllers.Main.txtShape_lightningBolt": "Gewitterblitz", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Pfeil", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Doppelpfeil", "SSE.Controllers.Main.txtShape_mathDivide": "Division", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiplizieren", "SSE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON> gleich", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "\"<PERSON><PERSON>\" Zeichen", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Eingekerbter Pfeil nach rechts", "SSE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogramm", "SSE.Controllers.Main.txtShape_pentagon": "Richtungspfeil", "SSE.Controllers.Main.txtShape_pie": "Kreis", "SSE.Controllers.Main.txtShape_plaque": "Signieren", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Skizze", "SSE.Controllers.Main.txtShape_polyline2": "Freihandform", "SSE.Controllers.Main.txtShape_quadArrow": "Pfeil in vier Richtungen", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Legende mit Pfeil in vier Richtungen", "SSE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "Band nach unten", "SSE.Controllers.Main.txtShape_ribbon2": "<PERSON> hoch", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> nach rechts", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Legende mit Pfeil nach rechts", "SSE.Controllers.Main.txtShape_rightBrace": "Geschweifte Klammer rechts", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON> re<PERSON>s", "SSE.Controllers.Main.txtShape_round1Rect": "Eine Ecke des Rechtecks abrunden", "SSE.Controllers.Main.txtShape_round2DiagRect": "Diagonal liegende Ecken des Rechtecks abrunden", "SSE.Controllers.Main.txtShape_round2SameRect": "Auf der gleichen Seite des Rechtecks liegende Ecken abrunden", "SSE.Controllers.Main.txtShape_roundRect": "Rechteck mit runden Ecken", "SSE.Controllers.Main.txtShape_rtTriangle": "Rechtwinkliges Dreieck", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Eine Ecke des Rechtecks schneiden", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Diagonal liegende Ecken des Rechtecks schneiden", "SSE.Controllers.Main.txtShape_snip2SameRect": "Ecken des Rechtecks auf der gleichen Seite schneiden", "SSE.Controllers.Main.txtShape_snipRoundRect": "Eine Ecke des Rechtecks schneiden und abrunden", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Gestreifter Pfeil nach rechts", "SSE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_teardrop": "Tropfenförmig", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "SSE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON> nach oben", "SSE.Controllers.Main.txtShape_upArrowCallout": "Legende mit Pfeil nach oben", "SSE.Controllers.Main.txtShape_upDownArrow": "<PERSON><PERSON><PERSON> nach unten", "SSE.Controllers.Main.txtShape_uturnArrow": "180-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON><PERSON> Bildlau<PERSON>", "SSE.Controllers.Main.txtShape_wave": "Welle", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON>e Legende", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Rechteckige Legende", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Abgerundete rechteckige Legende", "SSE.Controllers.Main.txtStarsRibbons": "Sterne & Bänder", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Zelle überprüfen", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Währung", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Erklärender Text", "SSE.Controllers.Main.txtStyle_Good": "Gut", "SSE.Controllers.Main.txtStyle_Heading_1": "Überschrift 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Überschrift 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Überschrift 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Überschrift 4", "SSE.Controllers.Main.txtStyle_Input": "Eingabe", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Verknüpfte Zelle", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "Ausgabe", "SSE.Controllers.Main.txtStyle_Percent": "Prozent", "SSE.Controllers.Main.txtStyle_Title": "Titel", "SSE.Controllers.Main.txtStyle_Total": "Insgesamt", "SSE.Controllers.Main.txtStyle_Warning_Text": "Warnungstext", "SSE.Controllers.Main.txtTab": "Registerkarte", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Uhrzeit", "SSE.Controllers.Main.txtUnlock": "Entsperren", "SSE.Controllers.Main.txtUnlockRange": "<PERSON><PERSON><PERSON> aufsperren", "SSE.Controllers.Main.txtUnlockRangeDescription": "<PERSON><PERSON> Bearbeitung dieses Bereichs bitte Kennwort eingeben:", "SSE.Controllers.Main.txtUnlockRangeWarning": "<PERSON> Be<PERSON>ich, den Si<PERSON> zu bearbeiten versuchen, ist durch ein Kennwort geschützt.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON>", "SSE.Controllers.Main.txtXAxis": "x-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtYAxis": "y-<PERSON><PERSON>e", "SSE.Controllers.Main.txtYears": "Jahre", "SSE.Controllers.Main.unknownErrorText": "Unbek<PERSON><PERSON> Fehler.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON> wird nicht unterstützt.", "SSE.Controllers.Main.uploadDocExtMessage": "Unbekanntes Dokumentformat.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Keine Dokumente hochgeladen.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maximale Dokumentgröße ist überschritten.", "SSE.Controllers.Main.uploadImageExtMessage": "Unbekanntes Bildformat.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON> ho<PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "Die maximal zulässige Bildgröße von 25 MB ist überschritten.", "SSE.Controllers.Main.uploadImageTextText": "Das Bild wird hochgeladen...", "SSE.Controllers.Main.uploadImageTitleText": "Bild wird hoch<PERSON>aden", "SSE.Controllers.Main.waitText": "Bitte warten...", "SSE.Controllers.Main.warnBrowserIE9": "Die Anwendung hat geringe Fähigkeiten in IE9. Nutzen Sie IE10 oder höher.", "SSE.Controllers.Main.warnBrowserZoom": "Die aktuelle Zoom-Einstellung Ihres Webbrowsers wird nicht völlig unterstützt. Bitte stellen Sie die Standardeinstellung mithilfe der Tastenkombination STRG+0 wieder her.", "SSE.Controllers.Main.warnLicenseExceeded": "Sie haben das Limit für gleichzeitige Verbindungen in %1-Editoren erreicht. Dieses Dokument wird nur zum Anzeigen geöffnet.<br><PERSON>te wenden Si<PERSON> sich an Ihren Administrator, um weitere Informationen zu erhalten.", "SSE.Controllers.Main.warnLicenseExp": "<PERSON>hre Lizenz ist abgelaufen.<br>Bitte aktualisieren Sie Ihre Lizenz und laden Si<PERSON> die Seite neu.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Die Lizenz ist abgelaufen.<br>Die Bearbeitungsfunktionen sind nicht verfügbar.<br><PERSON><PERSON> wenden <PERSON> sich an Ihrem Administrator.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Die Lizenz soll aktualisiert werden.<br>Die Bearbeitungsfunktionen sind eingeschränkt.<br><PERSON>te wenden Si<PERSON> sich an Ihrem Administrator für vollen Zugriff", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Sie haben das Benutzerlimit für %1-Editoren erreicht. Bitte wenden Si<PERSON> sich an Ihren Administrator, um weitere Informationen zu erhalten.", "SSE.Controllers.Main.warnNoLicense": "Sie haben das Limit für gleichzeitige Verbindungen in %1-Editoren erreicht. Dieses Dokument wird nur zum Anzeigen geöffnet.<br>Bitte kontaktieren Sie unser Verkaufsteam, um persönliche Upgrade-Bedingungen zu erhalten.", "SSE.Controllers.Main.warnNoLicenseUsers": "Sie haben das Benutzerlimit für %1-Editoren erreicht. Bitte kontaktieren Sie unser Verkaufsteam, um persönliche Upgrade-Bedingungen zu erhalten.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON>, die Datei zu bearbeiten, wurde Ihnen verweigert.", "SSE.Controllers.Print.strAllSheets": "Alle Blätter", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFrozenCols": "Eingefrorene Spalten", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Controllers.Print.textNoRepeat": "<PERSON>cht wiederholen", "SSE.Controllers.Print.textRepeat": "<PERSON><PERSON><PERSON><PERSON>...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON> auswählen", "SSE.Controllers.Print.textWarning": "Achtung", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON><PERSON> sind falsch", "SSE.Controllers.Search.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Controllers.Search.textNoTextFound": "<PERSON> Daten, nach denen <PERSON> gesucht haben, können nicht gefunden werden. Bitte ändern Sie die Suchparameter.", "SSE.Controllers.Search.textReplaceSkipped": "Der Ersatzvorgang wurde durchgeführt. {0} Vorkommen wurden ausgelassen.", "SSE.Controllers.Search.textReplaceSuccess": "Die Suche wurde durchgeführt. {0} Einträge wurden ersetzt", "SSE.Controllers.Statusbar.errorLastSheet": "Ein Arbeitsbuch muss mindestens ein sichtbares Arbeitsblatt enthalten.", "SSE.Controllers.Statusbar.errorRemoveSheet": "<PERSON>s ist nicht möglich, das Arbeitsblatt zu löschen.", "SSE.Controllers.Statusbar.strSheet": "Sheet", "SSE.Controllers.Statusbar.textDisconnect": "<b>Die Verbindung wurde unterbrochen</b><br>Verbindungsversuch... Bitte Verbindungseinstellungen überprüfen.", "SSE.Controllers.Statusbar.textSheetViewTip": "Sie befinden sich in einer Tabellenansicht. Filter und Sortierung sind nur für Sie und andere Benutzer/innen sichtbar, die in diesem Modus sind.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Sie befinden sich in einer Tabellenansicht. Filter sind nur für Sie und andere Benutzer/innen sichtbar, die in diesem Modus sind.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Die ausgewählten Arbeitsblätter könnten Daten enthalten. Fortsetzen?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "<PERSON> Schriftart, die Sie verwenden wollen, ist auf diesem Gerät nicht verfügbar.<br>Der Textstil wird mit einer der Systemschriften angezeigt, die gespeicherte Schriftart wird verwendet, wenn sie verfügbar ist.<br>Wollen Sie fortsetzen?", "SSE.Controllers.Toolbar.errorComboSeries": "<PERSON><PERSON>hlen Sie mindestens zwei Datenreihen aus, um ein Verbunddiagramm zu erstellen.", "SSE.Controllers.Toolbar.errorMaxRows": "FEHLER! Die maximale Anzahl der Datenreihen per Diagramm ist 255", "SSE.Controllers.Toolbar.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "SSE.Controllers.Toolbar.textAccent": "Akzente", "SSE.Controllers.Toolbar.textBracket": "Klammern", "SSE.Controllers.Toolbar.textDirectional": "Direktional", "SSE.Controllers.Toolbar.textFontSizeErr": "Der eingegebene Wert ist falsch.<br><PERSON><PERSON><PERSON> Sie bitte einen numerischen Wert zwischen 1 und 409 ein.", "SSE.Controllers.Toolbar.textFraction": "Bruchteile", "SSE.Controllers.Toolbar.textFunction": "Bruchteile", "SSE.Controllers.Toolbar.textIndicator": "Indikatoren", "SSE.Controllers.Toolbar.textInsert": "Einfügen", "SSE.Controllers.Toolbar.textIntegral": "Integrale", "SSE.Controllers.Toolbar.textLargeOperator": "Große Operatoren", "SSE.Controllers.Toolbar.textLimitAndLog": "Grenzwerte und Logarithmen", "SSE.Controllers.Toolbar.textLongOperation": "Operation ist zeitaufwendig", "SSE.Controllers.Toolbar.textMatrix": "Matrizen", "SSE.Controllers.Toolbar.textOperator": "Operatoren", "SSE.Controllers.Toolbar.textPivot": "Pivot-Tabelle", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Bewertungen", "SSE.Controllers.Toolbar.textRecentlyUsed": "Zuletzt verwendet", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Formen", "SSE.Controllers.Toolbar.textSymbols": "Symbole", "SSE.Controllers.Toolbar.textWarning": "Achtung", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Pfeil nach rechts und links oben", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Pfeil nach links oben", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> nach rechts oben", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Unterstreichung", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Überstreichung", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Geschachtelte Formel (mit Platzhalter)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Geschachtelte Formel (Beispiel)", "SSE.Controllers.Toolbar.txtAccent_Check": "Prüfen", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Horizontale geschweifte Klammer (unten)", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Horizontale geschweifte Klammer (oben)", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Mit Überstreichung", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Mit Überstreichung", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Dreifa<PERSON>", "SSE.Controllers.Toolbar.txtAccent_DDot": "Doppelpunkt", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Doppelte Überstreichung", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Gruppierungszeichen unten", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Gruppierungszeichen oben", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Ha<PERSON>une nach links oben", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Ha<PERSON><PERSON> nach rechts oben", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Klammern", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Klammern mit Trennlinien", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Klammern mit Trennlinien", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve": "Klammern", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Klammern mit Trennlinien", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Fälle (zwei Bedingungen)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON>ä<PERSON> (drei Bedingungen)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Stapelobjekt", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Stapelobjekt", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Fallbeispiele", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomialkoeffizient", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomialkoeffizient", "SSE.Controllers.Toolbar.txtBracket_Line": "Klammern", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Klammern", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Klammern", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round": "Klammern", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Klammern mit Trennlinien", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "Klammern", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Klammern", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Klammern", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Klammern", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Klammern", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Klammern", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtExpand": "Erweitern und sortieren", "SSE.Controllers.Toolbar.txtExpandSort": "Die Daten neben der Auswahlliste werden nicht sortiert. Möchten Sie die Auswahlliste erweitern, um nebenstehende Angaben einzuschließen oder nur mit Sortieren derzeit ausgewählten Zellen fortzusetzen.", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Versetzter Bruch mit schrägem Bruchstrich", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Differenzial", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Differenzial", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Differenzial", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Differenzial", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Bruch mit schrägem Bruchstrich", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi wird durch 2 dividiert", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> Bru<PERSON>hl", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON>ruch mit waagerechtem Bruchstrich", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Umgekehrte Kosinusfunktion", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolische umgekehrte Kosinus-Funktion", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Umgekehrte Kotangensfunktion", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolische umgekehrte Kotangens-Funktion", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Umgekehrte Kosekansfunktion", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolische umgekehrte Kosekans-Funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Umgekehrte Sekansfunktion", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolische umgekehrte Sekans-Funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Umgekehrte Sinusfunktion", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolische umgekehrte Sinus-Funktion", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Umgekehrte Tangensfunktion", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolische umgekehrte Tangens-Funktion", "SSE.Controllers.Toolbar.txtFunction_Cos": "Kosinusfunktion", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolische Kosinusfunktion", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangensfunktion", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolische Kotangensfunktion", "SSE.Controllers.Toolbar.txtFunction_Csc": "Kosekansfunktion", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolische Kosekansfunktion", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Kosinus 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensformel", "SSE.Controllers.Toolbar.txtFunction_Sec": "Sekansfunktion", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolische Sekans-Funktion", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sinusfunktion", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolische Sinus-Funktion", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangensformel", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolische Tangens-Funktion", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Einstellbar", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "<PERSON>n und Modell", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Gut, Schlecht und Neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Zahlenformat", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Zellenformate mit Designs", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titel und Überschriften", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Einstellbar", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Hell", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differenzial Theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differenzial x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differenzial y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Doppelintegral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Doppelintegral", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Doppelintegral", "SSE.Controllers.Toolbar.txtIntegralOriented": "Konturenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Konturenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Oberflächenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Oberflächenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Oberflächenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Konturenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volumenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volumenintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volumenintegral", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Dreifaches Integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Dreifaches Integral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Dreifaches Integral", "SSE.Controllers.Toolbar.txtInvalidRange": "FEHLER! Ungültiger Zellbereich", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Keil", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Keil", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Keil", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Keil", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Keil", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summierung", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summenbildung", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summenbildung", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Vereinigung", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-förmig", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-förmig", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-förmig", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-förmig", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-förmig", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Schnittmenge", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Schnittmenge", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Schnittmenge", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Schnittmenge", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Schnittmenge", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Summenbildung", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summenbildung", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summenbildung", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summenbildung", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summenbildung", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Vereinigung", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Vereinigung", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Vereinigung", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Vereinigung", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Vereinigung", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Beispiel für Grenzwert", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Beispiel für Maximum", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Grenzwert", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Natürlicher Logarithmus", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarith<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarith<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maximal", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimal", "SSE.Controllers.Toolbar.txtLockSort": "Neben dem ausgewählten Bereich wurde Daten gefunden, aber Si<PERSON> haben keine Berechtigung, diese <PERSON><PERSON> zu verändern.<br><PERSON><PERSON><PERSON><PERSON> Sie mit dem ausgewählten Bereich weiter arbeiten?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON>re Matrix mit Klammern", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON>re Matrix mit Klammern", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON>re Matrix mit Klammern", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON>re Matrix mit Klammern", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Leere Matrix", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Grundlinienpunkte", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Mittellinienpunkte", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonale Punkte", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Dünnbesetzte Matrix", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Dünnbesetzte Matrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identitätsmatrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Identitätsmatrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identitätsmatrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Identitätsmatrix", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Pfeil nach rechts und links unten", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Pfeil nach rechts und links oben", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "<PERSON><PERSON>il nach links unten", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Pfeil nach links oben", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> nach rechts unten", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> nach rechts oben", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Doppelpunkt gleich", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta ergibt", "SSE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON> gemäß Definition", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta gleich", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Pfeil nach rechts und links unten", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Pfeil nach rechts und links oben", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "<PERSON><PERSON>il nach links unten", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Pfeil nach links oben", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> nach rechts unten", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> nach rechts oben", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Gleich", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON> an", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Quadratwurzel mit Grad", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> mit Grad", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Tiefgestellt", "SSE.Controllers.Toolbar.txtScriptSubSup": "Tiefgestellt-Hochgestellt", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Hochgestellter/ tiefgestellter Index links", "SSE.Controllers.Toolbar.txtScriptSup": "Hochgestellt", "SSE.Controllers.Toolbar.txtSorting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSortSelected": "Ausgewählte sortieren", "SSE.Controllers.Toolbar.txtSymbol_about": "Circa", "SSE.Controllers.Toolbar.txtSymbol_additional": "Komplement", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "SSE.Controllers.Toolbar.txtSymbol_approx": "Fast gleich", "SSE.Controllers.Toolbar.txtSymbol_ast": "Stern-Operator", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Aufzählungsoperator", "SSE.Controllers.Toolbar.txtSymbol_cap": "Schnittmenge", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Horizontale Ellipse (Mittellinie)", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Etwa gleich ", "SSE.Controllers.Toolbar.txtSymbol_cup": "Vereinigung", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Diagonale Ellipse nach unten rechts", "SSE.Controllers.Toolbar.txtSymbol_degree": "Grad", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Divisionszeichen", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> nach unten", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identisch mit", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Vorhand<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktoriell", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grad Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON><PERSON> alle", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich wie ", "SSE.Controllers.Toolbar.txtSymbol_gg": "Viel g<PERSON>ößer als", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "SSE.Controllers.Toolbar.txtSymbol_in": "Element", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Pfeil nach links", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Pfeil nach rechts und links", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON>er als oder gleich", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>s", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> kleiner als", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON> gleich", "SSE.Controllers.Toolbar.txtSymbol_ni": "Enthält als Element", "SSE.Controllers.Toolbar.txtSymbol_not": "Negationszeichen", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Nicht vorhanden", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Partielles Differenzial", "SSE.Controllers.Toolbar.txtSymbol_percent": "Prozentsatz", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proportional zu", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qed": "Ende des Beweises", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Horizontale Ellipse nach oben rechts", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> nach rechts", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Wurzelzeichen", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Folglich", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "Multiplikationszeichen", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON> nach oben", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon Variant", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma Variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Tabellenformat - Dunkel", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Tabellenformat - Hell ", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Tabellenformat - Mittel", "SSE.Controllers.Toolbar.warnLongOperation": "Die Operation, die Sie durchführen möchten, kann viel Zeit in Anspruch nehmen. <br> <PERSON><PERSON> sie fortgesetzt werden?", "SSE.Controllers.Toolbar.warnMergeLostData": "Nur die Daten aus der oberen linken Zelle bleiben nach der Vereinigung.<br>M<PERSON>cht<PERSON> Sie wirklich fortsetzen?", "SSE.Controllers.Viewport.textFreezePanes": "<PERSON><PERSON> fixieren", "SSE.Controllers.Viewport.textFreezePanesShadow": "Schatten für fixierte Bereiche anzeigen", "SSE.Controllers.Viewport.textHideFBar": "Formelleiste verbergen", "SSE.Controllers.Viewport.textHideGridlines": "Gitternetzlinien verbergen", "SSE.Controllers.Viewport.textHideHeadings": "Überschriften verbergen", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Dezimaltrennzeichen", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Tausendertrennzeichen", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Einstellungen zum Erkennen numerischer Daten", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Textqualifizierer", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Erweiterte Einstellungen", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(kein)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Benutzerdefinierter Filter", "SSE.Views.AutoFilterDialog.textAddSelection": "Dem Filter die aktuelle Auswahl hinzufügen", "SSE.Views.AutoFilterDialog.textEmptyItem": "{<PERSON><PERSON><PERSON>}", "SSE.Views.AutoFilterDialog.textSelectAll": "Alle wählen", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Alle Suchergebnisse auswählen", "SSE.Views.AutoFilterDialog.textWarning": "Achtung", "SSE.Views.AutoFilterDialog.txtAboveAve": "Überdurch<PERSON><PERSON><PERSON>lich", "SSE.Views.AutoFilterDialog.txtBegins": "Beginnt mit...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Überdurch<PERSON><PERSON><PERSON>lich", "SSE.Views.AutoFilterDialog.txtBetween": "<PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtClear": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "Enthält...", "SSE.Views.AutoFilterDialog.txtEmpty": "<PERSON><PERSON><PERSON> Si<PERSON> den Zellenfilter ein", "SSE.Views.AutoFilterDialog.txtEnds": "Endet mit...", "SSE.Views.AutoFilterDialog.txtEquals": "Entspricht...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtern nach Zellenfarbe", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtern nach Schriftfarbe", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON> als...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Label Filter", "SSE.Views.AutoFilterDialog.txtLess": "Kleiner als...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Kleiner als oder gleich...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Beginnt nicht mit...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Nicht zwischen...", "SSE.Views.AutoFilterDialog.txtNotContains": "Enthält kein/keine...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Endet nicht mit...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Ist nicht gleich...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Nummernfilter", "SSE.Views.AutoFilterDialog.txtReapply": "Erneut übernehmen", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON> Z<PERSON>far<PERSON> sortieren", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Nach Schriftfarbe sortieren", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Absteigend sortieren", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Aufsteigend sortieren", "SSE.Views.AutoFilterDialog.txtSortOption": "Mehr Sortierungsoptionen", "SSE.Views.AutoFilterDialog.txtTextFilter": "Textfilter", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtTop10": "Erste 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "<PERSON>s wird mindestens ein <PERSON>", "SSE.Views.AutoFilterDialog.warnNoSelected": "<PERSON>e müssen zumindest einen Wert wählen", "SSE.Views.CellEditor.textManager": "Name-Manager", "SSE.Views.CellEditor.tipFormula": "Funktion einfügen", "SSE.Views.CellRangeDialog.errorMaxRows": "FEHLER! Die maximale Anzahl der Datenreihen per Diagramm ist 255", "SSE.Views.CellRangeDialog.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.CellRangeDialog.txtInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.CellRangeDialog.txtTitle": "Datenbereich auswählen", "SSE.Views.CellSettings.strShrink": "passend schrumpfen", "SSE.Views.CellSettings.strWrap": "Text umbrechen", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Hintergrundfarbe", "SSE.Views.CellSettings.textBackground": "Hintergrundfarbe", "SSE.Views.CellSettings.textBorderColor": "Farbe", "SSE.Views.CellSettings.textBorders": "<PERSON>il des Rahmens", "SSE.Views.CellSettings.textClearRule": "Regeln löschen", "SSE.Views.CellSettings.textColor": "Farbfüllung", "SSE.Views.CellSettings.textColorScales": "Farbskalen", "SSE.Views.CellSettings.textCondFormat": "Bedingte Formatierung", "SSE.Views.CellSettings.textControl": "Textsteuerung", "SSE.Views.CellSettings.textDataBars": "Datenbalken", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "Ausfüllen", "SSE.Views.CellSettings.textForeground": "Vordergrundfarbe", "SSE.Views.CellSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientColor": "Farbe", "SSE.Views.CellSettings.textGradientFill": "Füllung mit Farbverlauf", "SSE.Views.CellSettings.textIndent": "Einzug", "SSE.Views.CellSettings.textItems": "Elemente", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "Regeln verwalten", "SSE.Views.CellSettings.textNewRule": "Neue Regel", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textOrientation": "Textausrichtung", "SSE.Views.CellSettings.textPattern": "Muster", "SSE.Views.CellSettings.textPatternFill": "Muster", "SSE.Views.CellSettings.textPosition": "Stellung", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> Sie Rahmen, die Si<PERSON> ändern möchten, indem Sie den oben ausgewählten Stil anwenden", "SSE.Views.CellSettings.textSelection": "Aus der aktuellen Auswahl", "SSE.Views.CellSettings.textThisPivot": "<PERSON><PERSON> <PERSON>er Pi<PERSON>-<PERSON><PERSON>e", "SSE.Views.CellSettings.textThisSheet": "Aus diesem Arbeitsblatt", "SSE.Views.CellSettings.textThisTable": "<PERSON><PERSON> dies<PERSON>", "SSE.Views.CellSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "SSE.Views.CellSettings.tipAll": "Äußere Rahmenlinie und alle inneren Linien festlegen", "SSE.Views.CellSettings.tipBottom": "Nur äußere untere Rahmenlinie festlegen", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON><PERSON> den diagonalen unteren Rand ein", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON> den oberen Rand nach oben ein", "SSE.Views.CellSettings.tipInner": "Nur innere Linien festlegen", "SSE.Views.CellSettings.tipInnerHor": "Nur innere horizontale Linien festlegen", "SSE.Views.CellSettings.tipInnerVert": "Nur vertikale innere Linien festlegen", "SSE.Views.CellSettings.tipLeft": "Nur äußere linke Rahmenlinie festlegen", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON> festlegen", "SSE.Views.CellSettings.tipOuter": "Nur äußere Rahmenlinie festlegen", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "SSE.Views.CellSettings.tipRight": "Nur äußere rechte Rahmenlinie festlegen", "SSE.Views.CellSettings.tipTop": "Nur äußere obere Rahmenlinie festlegen", "SSE.Views.ChartDataDialog.errorInFormula": "Fehler bei der eingegebenen Formel.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Der Bezug ist ungültig. Dieser soll ein offener Arbeitsblatt sein.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Die maximale Punktzahl pro eine Tabelle in Reihen beträgt 4096 Punkte.", "SSE.Views.ChartDataDialog.errorMaxRows": "Die maximale Anzahl an Datenreihen pro Diagramm ist 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Der Bezug ist ungültig. Bezüge für Rubriken, Werte, Größen oder Datenbeschriftungen müssen aus einer e<PERSON>zelnen Zelle, Zeile oder Spalte bestehen.", "SSE.Views.ChartDataDialog.errorNoValues": "Die Datenreihe muss mindestens einen Wert enthalten, um ein Diagramm zu erstellen.", "SSE.Views.ChartDataDialog.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "SSE.Views.ChartDataDialog.textAdd": "Hinzufügen", "SSE.Views.ChartDataDialog.textCategory": "Horizontale Achsenbeschriftungen (Rubrik)", "SSE.Views.ChartDataDialog.textData": "Diagrammdatenbereich", "SSE.Views.ChartDataDialog.textDelete": "Entfernen", "SSE.Views.ChartDataDialog.textDown": "Nach unten", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Ungültiger Zellenbereich", "SSE.Views.ChartDataDialog.textSelectData": "Daten auswählen", "SSE.Views.ChartDataDialog.textSeries": "Legendeneinträge (Reihen)", "SSE.Views.ChartDataDialog.textSwitch": "<PERSON>eile/<PERSON>lte <PERSON>n", "SSE.Views.ChartDataDialog.textTitle": "Diagrammdaten", "SSE.Views.ChartDataDialog.textUp": "Nach oben", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Fehler bei der eingegebenen Formel.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Der Bezug ist ungültig. Dieser soll ein offener Arbeitsblatt sein.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Die maximale Punktzahl pro eine Tabelle in Reihen beträgt 4096 Punkte.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Die maximale Anzahl an Datenreihen pro Diagramm ist 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Der Bezug ist ungültig. Bezüge für Rubriken, Werte, Größen oder Datenbeschriftungen müssen aus einer e<PERSON>zelnen Zelle, Zeile oder Spalte bestehen.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Die Datenreihe muss mindestens einen Wert enthalten, um ein Diagramm zu erstellen.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Ungültiger Zellenbereich", "SSE.Views.ChartDataRangeDialog.textSelectData": "Daten auswählen", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Der Bereich von Achsenbeschriftungen", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON> auswählen", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Achsenbeschriftungen", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON> bearbeiten", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "X-Werte", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y-Werte", "SSE.Views.ChartSettings.errorMaxRows": "Die maximale Anzahl an Datenreihen pro Diagramm ist 255.", "SSE.Views.ChartSettings.strLineWeight": "Linienbreite", "SSE.Views.ChartSettings.strSparkColor": "Farbe", "SSE.Views.ChartSettings.strTemplate": "Vorlage", "SSE.Views.ChartSettings.text3dDepth": "Tiefe (% der Basis)", "SSE.Views.ChartSettings.text3dHeight": "Höhe (% der Basis)", "SSE.Views.ChartSettings.text3dRotation": "3D-Dr<PERSON>ung", "SSE.Views.ChartSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "SSE.Views.ChartSettings.textAutoscale": "Autoskalierung", "SSE.Views.ChartSettings.textBorderSizeErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 0 pt und 1584 pt ein.", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textChartType": "Diagrammtyp ändern", "SSE.Views.ChartSettings.textDefault": "Standardmäßige Drehung", "SSE.Views.ChartSettings.textDown": "Unten", "SSE.Views.ChartSettings.textEditData": "Daten und Standort ändern", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "Höchstpunkt", "SSE.Views.ChartSettings.textKeepRatio": "Seitenverhältnis beibehalten", "SSE.Views.ChartSettings.textLastPoint": "Letzter Punkt", "SSE.Views.ChartSettings.textLeft": "Links", "SSE.Views.ChartSettings.textLowPoint": "Tiefpunkt", "SSE.Views.ChartSettings.textMarkers": "Markierungen", "SSE.Views.ChartSettings.textNarrow": "Blickfeld verengen", "SSE.Views.ChartSettings.textNegativePoint": "Negativpunkt", "SSE.Views.ChartSettings.textPerspective": "Perspektive", "SSE.Views.ChartSettings.textRanges": "Datenbereich", "SSE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRightAngle": "Rechtwinklige Achsen", "SSE.Views.ChartSettings.textSelectData": "Daten auswählen", "SSE.Views.ChartSettings.textShow": "Anzeigen", "SSE.Views.ChartSettings.textSize": "Größe", "SSE.Views.ChartSettings.textStyle": "Stil", "SSE.Views.ChartSettings.textSwitch": "<PERSON>eile/<PERSON>lte <PERSON>n", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textUp": "Nach oben", "SSE.Views.ChartSettings.textWiden": "Blickfeld verbreitern", "SSE.Views.ChartSettings.textWidth": "Breite", "SSE.Views.ChartSettings.textX": "X-Rotation", "SSE.Views.ChartSettings.textY": "Y-Rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "FEHLER! Die maximale Punktzahl pro eine Tabelle beträgt 4096 Punkte.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "FEHLER! Die maximale Anzahl der Datenreihen per Diagramm ist 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "SSE.Views.ChartSettingsDlg.textAbsolute": "<PERSON><PERSON> Verschieben oder Ändern der Größe mit Zellen", "SSE.Views.ChartSettingsDlg.textAlt": "Der alternative Text", "SSE.Views.ChartSettingsDlg.textAltDescription": "Beschreibung", "SSE.Views.ChartSettingsDlg.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Titel", "SSE.Views.ChartSettingsDlg.textAuto": "Automatisch", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automatisch für jeden", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Schnittpunkt mit der Achse", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Parameter der Achse", "SSE.Views.ChartSettingsDlg.textAxisPos": "Position der Achse", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Achseneinstellungen", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Titel", "SSE.Views.ChartSettingsDlg.textBase": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Zwischen den Teilstrichen", "SSE.Views.ChartSettingsDlg.textBillions": "Milliarden", "SSE.Views.ChartSettingsDlg.textBottom": "Unten", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Diagrammelemente und <br> Diagrammlegende", "SSE.Views.ChartSettingsDlg.textChartTitle": "Diagrammtitel", "SSE.Views.ChartSettingsDlg.textCross": "Schnittpunkt", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "in Spalten", "SSE.Views.ChartSettingsDlg.textDataLabels": "Datenbeschriftungen", "SSE.Views.ChartSettingsDlg.textDataRows": "in Zeilen", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON> anzeigen", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Ausgeblendete und leere Zellen", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Datenpunkte mit Linie verbinden", "SSE.Views.ChartSettingsDlg.textFit": "<PERSON><PERSON><PERSON> an<PERSON>en", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Bezeichnungsformat", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Gitternetzlinien ", "SSE.Views.ChartSettingsDlg.textGroup": "Sparklinegruppen", "SSE.Views.ChartSettingsDlg.textHide": "Verbergen", "SSE.Views.ChartSettingsDlg.textHideAxis": "Achse ausblenden", "SSE.Views.ChartSettingsDlg.textHigh": "Hoch", "SSE.Views.ChartSettingsDlg.textHorAxis": "Horizontale Achse", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Horizontale Sekundärachse", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Hunderte", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "In", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Innere Untere", "SSE.Views.ChartSettingsDlg.textInnerTop": "Innen oben", "SSE.Views.ChartSettingsDlg.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.ChartSettingsDlg.textLabelDist": "Abstand bis zur Beschriftung", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Abstand zwischen Beschriftungen", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Beschriftungsoptionen", "SSE.Views.ChartSettingsDlg.textLabelPos": "Beschriftungsposition", "SSE.Views.ChartSettingsDlg.textLayout": "Layout", "SSE.Views.ChartSettingsDlg.textLeft": "Links", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Überlagerung links", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Unten", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Links", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Positionsbereich", "SSE.Views.ChartSettingsDlg.textLogScale": "Logarithmische Skalierung", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Primäre und sekundäre", "SSE.Views.ChartSettingsDlg.textMajorType": "Primärer <PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Abstand zwischen Teilstrichen", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maximalwert", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON>en", "SSE.Views.ChartSettingsDlg.textMinor": "Sekundär", "SSE.Views.ChartSettingsDlg.textMinorType": "Sekundärer Typ", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimalwert", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Neben der Achse", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Ohne Überlagerung", "SSE.Views.ChartSettingsDlg.textOneCell": "<PERSON><PERSON><PERSON>eb<PERSON>, aber die Größe nicht ändern mit Zellen", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOut": "Außen", "SSE.Views.ChartSettingsDlg.textOuterTop": "Außen oben", "SSE.Views.ChartSettingsDlg.textOverlay": "Überlagerung", "SSE.Views.ChartSettingsDlg.textReverse": "Werte in umgekehrter Reihenfolge", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Reihenfolge umkehren", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Überlagerung rechts", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "Das Gleiche für alle", "SSE.Views.ChartSettingsDlg.textSelectData": "Daten auswählen", "SSE.Views.ChartSettingsDlg.textSeparator": "Trennzeichen für Datenbeschriftungen", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShow": "Anzeigen", "SSE.Views.ChartSettingsDlg.textShowBorders": "Diagrammr<PERSON>nder anzeigen", "SSE.Views.ChartSettingsDlg.textShowData": "Daten in ausgeblendeten Zeilen und Spalten anzeigen", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON><PERSON> an<PERSON>igen", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Achse anzeigen", "SSE.Views.ChartSettingsDlg.textShowValues": "Diagrammwerte anzeigen", "SSE.Views.ChartSettingsDlg.textSingle": "Einzelne Sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Andocken an die Zelle", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparkline Bereiche", "SSE.Views.ChartSettingsDlg.textStraight": "Gerade", "SSE.Views.ChartSettingsDlg.textStyle": "Stil", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Tausende", "SSE.Views.ChartSettingsDlg.textTickOptions": "Parameter der Teilstriche", "SSE.Views.ChartSettingsDlg.textTitle": "Diagramm - Erweiterte Einstellungen", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Erweiterte Einstellungen", "SSE.Views.ChartSettingsDlg.textTop": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Verschieben und Ändern der Größe mit Zellen", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "<PERSON><PERSON> und Daten", "SSE.Views.ChartSettingsDlg.textUnits": "Anzeigeeinheiten", "SSE.Views.ChartSettingsDlg.textValue": "Wert", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Vertikale Sekundärac<PERSON>e", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X-Achsentitel", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y-Achsentitel", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.ChartTypeDialog.errorComboSeries": "<PERSON><PERSON>hlen Sie mindestens zwei Datenreihen aus, um ein Verbunddiagramm zu erstellen.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "<PERSON><PERSON>r den ausgewählten Diagrammtyp ist die Sekundärachse erforderlich, die ein vorhandenes Diagramm schon benutzt. Wählen Si<PERSON> einen anderen Typ aus.", "SSE.Views.ChartTypeDialog.textSecondary": "Sekundärachse", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textStyle": "Stil", "SSE.Views.ChartTypeDialog.textTitle": "Diagrammtyp", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textDataRange": "Der Bereich von ursprünglichen Daten", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, wo (...) platz<PERSON>t wird", "SSE.Views.CreatePivotDialog.textExist": "existierendes Arbeitsblatt", "SSE.Views.CreatePivotDialog.textInvalidRange": "Ungültiger Zellenbereich", "SSE.Views.CreatePivotDialog.textNew": "Neues Arbeitsblatt", "SSE.Views.CreatePivotDialog.textSelectData": "Daten auswählen", "SSE.Views.CreatePivotDialog.textTitle": "Pivot-<PERSON><PERSON><PERSON>", "SSE.Views.CreatePivotDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.CreateSparklineDialog.textDataRange": "Der Bereich von ursprünglichen Daten", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> aus, wo die Sparklines abgelegt werden soll.", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Ungültiger Zellenbereich", "SSE.Views.CreateSparklineDialog.textSelectData": "Daten auswählen", "SSE.Views.CreateSparklineDialog.textTitle": "Sparklines erstellen", "SSE.Views.CreateSparklineDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.DataTab.capBtnGroup": "Gruppieren", "SSE.Views.DataTab.capBtnTextCustomSort": "Benutzerdefinierte Sortierung", "SSE.Views.DataTab.capBtnTextDataValidation": "Datenüberprüfung", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Entferne Duplikate", "SSE.Views.DataTab.capBtnTextToCol": "Text in Spalten", "SSE.Views.DataTab.capBtnUngroup": "Gruppierung aufheben", "SSE.Views.DataTab.capDataExternalLinks": "Externe Links", "SSE.Views.DataTab.capDataFromText": "Aus Text/CSV", "SSE.Views.DataTab.mniFromFile": "Daten aus einer Datei erhalten", "SSE.Views.DataTab.mniFromUrl": "Daten aus einer URL erhalten", "SSE.Views.DataTab.textBelow": "Hauptzeilen unter Detaildaten", "SSE.Views.DataTab.textClear": "Gliederung entfernen", "SSE.Views.DataTab.textColumns": "Gruppierung von Spalten aufheben", "SSE.Views.DataTab.textGroupColumns": "Spalten gruppieren", "SSE.Views.DataTab.textGroupRows": "Zeilen gruppieren", "SSE.Views.DataTab.textRightOf": "Hauptspalten rechts von Detaildaten", "SSE.Views.DataTab.textRows": "Gruppierung von Zeilen aufheben", "SSE.Views.DataTab.tipCustomSort": "Benutzerdefinierte Sortierung", "SSE.Views.DataTab.tipDataFromText": "Daten aus einer Text-/CSV-Datei erhalten", "SSE.Views.DataTab.tipDataValidation": "Datenüberprüfung", "SSE.Views.DataTab.tipExternalLinks": "Andere Dateien anzeigen, mit denen diese Tabellenkalkulation verknüpft ist", "SSE.Views.DataTab.tipGroup": "Zellenbereich gruppieren", "SSE.Views.DataTab.tipRemDuplicates": "Duplikate von Reihen aus Tabellenkalkulation entfernen", "SSE.Views.DataTab.tipToColumns": "Zelltext in Spalten aufteilen", "SSE.Views.DataTab.tipUngroup": "Gruppierung von Zellenbereich aufheben", "SSE.Views.DataValidationDialog.errorFormula": "Dieser Wert wird zurzeit zu einem Fehler ausgewertet. Möchten Sie den Vorgang fortsetzen?", "SSE.Views.DataValidationDialog.errorInvalid": "Der im Feld \"{0}\" eingegebene Wert ist ungültig.", "SSE.Views.DataValidationDialog.errorInvalidDate": "Das im Feld \"{0}\" eingegebene Datum ist ungültig.", "SSE.Views.DataValidationDialog.errorInvalidList": "Die Quelle muss eine getrennte Liste oder ein Bezug auf eine einzelne Zeile oder Spalte sein.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Die im Feld \"{0}\" eingegebene Zeit ist ungültig.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "<PERSON> Feld \"{1}\" muss größer als oder gleich dem Feld \"{0}\" sein.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Sie müssen einen Wert in Felder \"{0}\" und \"{1}\" eingeben.", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Sie müssen einen Wert ins Feld \"{0}\" eingeben.", "SSE.Views.DataValidationDialog.errorNamedRange": "Der angegebene benannte Bereich wurde nicht gefunden.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negative Werte können nicht für die Bedingungen \"{0}\" verwendet werden.", "SSE.Views.DataValidationDialog.errorNotNumeric": "Das Feld \"{0}\" muss ein numerischer Wert, numerischer Ausdruck oder ein Bezug auf eine Zelle, die einen numerischen Wert enthält, sein.", "SSE.Views.DataValidationDialog.strError": "Fehlermeldung", "SSE.Views.DataValidationDialog.strInput": "Eingabemeldung", "SSE.Views.DataValidationDialog.strSettings": "Einstellungen", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Diese Änderungen auf alle Zellen mit denselben Einstellungen anwenden", "SSE.Views.DataValidationDialog.textCellSelected": "Diese Eingabemeldung anzeigen, wenn Zelle ausgewählt wird", "SSE.Views.DataValidationDialog.textCompare": "Vergleichen mit", "SSE.Views.DataValidationDialog.textData": "Daten", "SSE.Views.DataValidationDialog.textEndDate": "Enddatum", "SSE.Views.DataValidationDialog.textEndTime": "Endzeit", "SSE.Views.DataValidationDialog.textError": "Fehlermeldung", "SSE.Views.DataValidationDialog.textFormula": "Formel", "SSE.Views.DataValidationDialog.textIgnore": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textInput": "Eingabemeldung", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Nachricht", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Daten auswählen", "SSE.Views.DataValidationDialog.textShowDropDown": "Dropdownliste in der Zell anzeigen", "SSE.Views.DataValidationDialog.textShowError": "Fehlermeldung anzeigen, wenn ungültige Daten eingegeben werden", "SSE.Views.DataValidationDialog.textShowInput": "Eingabemeldung anzeigen, wenn Zelle ausgewählt wird", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Startdatum", "SSE.Views.DataValidationDialog.textStartTime": "Startzeit", "SSE.Views.DataValidationDialog.textStop": "<PERSON>den", "SSE.Views.DataValidationDialog.textStyle": "Stil", "SSE.Views.DataValidationDialog.textTitle": "Titel", "SSE.Views.DataValidationDialog.textUserEnters": "<PERSON>se <PERSON>dung anzeigen, wenn ungültige Daten eingegeben wurden", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtBetween": "zwischen", "SSE.Views.DataValidationDialog.txtDate": "Datum", "SSE.Views.DataValidationDialog.txtDecimal": "Dezimal", "SSE.Views.DataValidationDialog.txtElTime": "Verstrichene Zeit", "SSE.Views.DataValidationDialog.txtEndDate": "Enddatum", "SSE.Views.DataValidationDialog.txtEndTime": "Endzeit", "SSE.Views.DataValidationDialog.txtEqual": "ist gleich", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich wie ", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "<PERSON><PERSON>s", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "<PERSON>er als oder gleich wie", "SSE.Views.DataValidationDialog.txtList": "Liste", "SSE.Views.DataValidationDialog.txtNotBetween": "nicht zwischen", "SSE.Views.DataValidationDialog.txtNotEqual": "ist nicht gleich", "SSE.Views.DataValidationDialog.txtOther": "Sonstiges", "SSE.Views.DataValidationDialog.txtStartDate": "Startdatum", "SSE.Views.DataValidationDialog.txtStartTime": "Startzeit", "SSE.Views.DataValidationDialog.txtTextLength": "Textlänge", "SSE.Views.DataValidationDialog.txtTime": "Uhrzeit", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "Und", "SSE.Views.DigitalFilterDialog.capCondition1": "ist gleich", "SSE.Views.DigitalFilterDialog.capCondition10": "endet nicht mit", "SSE.Views.DigitalFilterDialog.capCondition11": "<PERSON>th<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "enthält nicht", "SSE.Views.DigitalFilterDialog.capCondition2": "ist nicht gleich", "SSE.Views.DigitalFilterDialog.capCondition3": "ist größer als", "SSE.Views.DigitalFilterDialog.capCondition4": "ist größer oder gleich", "SSE.Views.DigitalFilterDialog.capCondition5": "ist kleiner als", "SSE.Views.DigitalFilterDialog.capCondition6": "ist kleiner oder gleich", "SSE.Views.DigitalFilterDialog.capCondition7": "beginnt mit", "SSE.Views.DigitalFilterDialog.capCondition8": "beginnt nicht mit", "SSE.Views.DigitalFilterDialog.capCondition9": "endet mit", "SSE.Views.DigitalFilterDialog.capOr": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.textNoFilter": "ohne <PERSON>lter", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON> anzeigen, wo", "SSE.Views.DigitalFilterDialog.textUse1": "Nutzen Sie das Symbol ?, um ein einziges Zeichen darzustellen", "SSE.Views.DigitalFilterDialog.textUse2": "Nutzen Sie das Symbol *, um eine Reihe von Zeichen darzustellen", "SSE.Views.DigitalFilterDialog.txtTitle": "Benutzerdefinierter Filter", "SSE.Views.DocumentHolder.advancedEquationText": "Einstellungen der Gleichung", "SSE.Views.DocumentHolder.advancedImgText": "Erweiterte Einstellungen des Bildes", "SSE.Views.DocumentHolder.advancedShapeText": "Erweiterte Einstellungen der Form", "SSE.Views.DocumentHolder.advancedSlicerText": "Erweiterte Einstellungen vom Datenschnitt", "SSE.Views.DocumentHolder.allLinearText": "Alle – Linear", "SSE.Views.DocumentHolder.allProfText": "Alle – Professionelle", "SSE.Views.DocumentHolder.bottomCellText": "Unten ausrichten", "SSE.Views.DocumentHolder.bulletsText": "Nummerierung und Aufzählungszeichen", "SSE.Views.DocumentHolder.centerCellText": "<PERSON><PERSON> au<PERSON>", "SSE.Views.DocumentHolder.chartDataText": "Diagrammdaten auswählen", "SSE.Views.DocumentHolder.chartText": "Erweiterte Einstellungen des Diagramms", "SSE.Views.DocumentHolder.chartTypeText": "Diagrammtyp ändern", "SSE.Views.DocumentHolder.currLinearText": "Aktuell – Linear", "SSE.Views.DocumentHolder.currProfText": "Aktuell – Professionell", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON>lt<PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Text nach oben drehen", "SSE.Views.DocumentHolder.direct90Text": "Text nach unten drehen", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Textausrichtung", "SSE.Views.DocumentHolder.editChartText": "Daten ä<PERSON>n", "SSE.Views.DocumentHolder.editHyperlinkText": "Hyperlink bearbeiten", "SSE.Views.DocumentHolder.insertColumnLeftText": "Spalte nach links", "SSE.Views.DocumentHolder.insertColumnRightText": "Spalte nach rechts", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON> un<PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Tatsächliche Größe", "SSE.Views.DocumentHolder.removeHyperlinkText": "Hyperlink entfernen", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "Spaltendaten", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strDelete": "Signatur entfernen", "SSE.Views.DocumentHolder.strDetails": "Signaturdetails", "SSE.Views.DocumentHolder.strSetup": "Signatureinrichtung", "SSE.Views.DocumentHolder.strSign": "Signieren", "SSE.Views.DocumentHolder.textAlign": "Ausrichten", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "In den Hintergrund senden", "SSE.Views.DocumentHolder.textArrangeBackward": "Eine Ebene nach hinten", "SSE.Views.DocumentHolder.textArrangeForward": "Eine Ebene nach vorne", "SSE.Views.DocumentHolder.textArrangeFront": "In den Vordergrund bringen", "SSE.Views.DocumentHolder.textAverage": "MITTELWERT", "SSE.Views.DocumentHolder.textBullets": "Aufzählung", "SSE.Views.DocumentHolder.textCount": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "Ausfüllen", "SSE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON> bearbeiten", "SSE.Views.DocumentHolder.textEntriesList": "Aus der Dropdown-Liste wählen", "SSE.Views.DocumentHolder.textFlipH": "Horizontal kippen", "SSE.Views.DocumentHolder.textFlipV": "Vertikal kippen", "SSE.Views.DocumentHolder.textFreezePanes": "Fensterausschnitt fixieren", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON> Datei", "SSE.Views.DocumentHolder.textFromStorage": "vom Speicher", "SSE.Views.DocumentHolder.textFromUrl": "Aus URL", "SSE.Views.DocumentHolder.textListSettings": "Listeneinstellungen", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "Weitere Funktionen", "SSE.Views.DocumentHolder.textMoreFormats": "Weitere Formate", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Nummerierung", "SSE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Um 90 ° gegen den Uhrzeigersinn drehen", "SSE.Views.DocumentHolder.textRotate90": "90° im UZS drehen", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Unten ausrichten", "SSE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON> au<PERSON>", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON> au<PERSON>", "SSE.Views.DocumentHolder.textShapeAlignRight": "Rechts ausrichten", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON> aus<PERSON>", "SSE.Views.DocumentHolder.textStdDev": "STABW", "SSE.Views.DocumentHolder.textSum": "Summe", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "SSE.Views.DocumentHolder.textUnFreezePanes": "Fixierung aufheben", "SSE.Views.DocumentHolder.textVar": "VARIANZ", "SSE.Views.DocumentHolder.tipMarkersArrow": "Pfeilförmige Aufzählungszeichen", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Häkchenaufzählungszeichen", "SSE.Views.DocumentHolder.tipMarkersDash": "Aufzählungszeichen", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Ausgefüllte karoförmige Aufzählungszeichen", "SSE.Views.DocumentHolder.tipMarkersFRound": "Ausgefüllte runde Aufzählungszeichen", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Ausgefüllte quadratische Aufzählungszeichen", "SSE.Views.DocumentHolder.tipMarkersHRound": "<PERSON><PERSON> runde Aufzählungszeichen", "SSE.Views.DocumentHolder.tipMarkersStar": "Sternförmige Aufzählungszeichen", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON> aus<PERSON>", "SSE.Views.DocumentHolder.txtAccounting": "Rechnungswesen", "SSE.Views.DocumentHolder.txtAddComment": "Kommentar hinzufügen", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON>n definieren", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Aufsteigend", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Spaltenbreite autom. anpassen", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Zeilenhöhe autom. anpassen", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "Alle", "SSE.Views.DocumentHolder.txtClearComments": "Kommentare", "SSE.Views.DocumentHolder.txtClearFormat": "Formatierung", "SSE.Views.DocumentHolder.txtClearHyper": "Hyperlinks", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Ausgewählte Sparklinegruppen löschen", "SSE.Views.DocumentHolder.txtClearSparklines": "Ausgewählte Sparklines löschen", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "Spaltenbreite einstellen", "SSE.Views.DocumentHolder.txtCondFormat": "Bedingte Formatierung", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCurrency": "Währung", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Benutzerdefinierte Spaltenbreite", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Benutzerdefinierte Zeilenhöhe", "SSE.Views.DocumentHolder.txtCustomSort": "Benutzerdefinierte Sortierung", "SSE.Views.DocumentHolder.txtCut": "Ausschneiden", "SSE.Views.DocumentHolder.txtDate": "Datum", "SSE.Views.DocumentHolder.txtDelete": "Löschen", "SSE.Views.DocumentHolder.txtDescending": "Absteigend", "SSE.Views.DocumentHolder.txtDistribHor": "Horizontal verteilen", "SSE.Views.DocumentHolder.txtDistribVert": "Vertikal verteilen", "SSE.Views.DocumentHolder.txtEditComment": "Kommentar bearbeiten", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtern nach Zellenfarbe", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtern nach Schriftfarbe", "SSE.Views.DocumentHolder.txtFilterValue": "Nach dem Wert der ausgewählten Zelle filtern", "SSE.Views.DocumentHolder.txtFormula": "Funktion einfügen", "SSE.Views.DocumentHolder.txtFraction": "Bru<PERSON>", "SSE.Views.DocumentHolder.txtGeneral": "Allgemein", "SSE.Views.DocumentHolder.txtGetLink": "Link zum diesen Bereich erhalten", "SSE.Views.DocumentHolder.txtGroup": "Gruppieren", "SSE.Views.DocumentHolder.txtHide": "Verbergen", "SSE.Views.DocumentHolder.txtInsert": "Einfügen", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hyperlink", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "Zahlenformat", "SSE.Views.DocumentHolder.txtPaste": "Einfügen", "SSE.Views.DocumentHolder.txtPercentage": "Prozentsatz", "SSE.Views.DocumentHolder.txtReapply": "Erneut übernehmen", "SSE.Views.DocumentHolder.txtRefresh": "Aktualisieren", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "Zeilenhöhe einstellen", "SSE.Views.DocumentHolder.txtScientific": "Wissenschaftlich", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON> nach unten verschieben", "SSE.Views.DocumentHolder.txtShiftLeft": "Zellen nach links verschieben", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON>n nach rechts verschieben", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON> nach oben verschieben", "SSE.Views.DocumentHolder.txtShow": "Anzeigen", "SSE.Views.DocumentHolder.txtShowComment": "Kommentare anzeigen", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Ausgewählte Zellenfarbe nach oben", "SSE.Views.DocumentHolder.txtSortFontColor": "Ausgewählte Schriftfarbe nach oben", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Erweiterte Paragraf-Einstellungen", "SSE.Views.DocumentHolder.txtTime": "Zeit", "SSE.Views.DocumentHolder.txtUngroup": "Gruppierung aufheben", "SSE.Views.DocumentHolder.txtWidth": "Breite", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.closeButtonText": "Schließen", "SSE.Views.ExternalLinksDlg.textDelete": "Verknüpfungen löschen", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Alle Verknüpfungen aufheben", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textSource": "<PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unbekannt", "SSE.Views.ExternalLinksDlg.textUpdate": "Werte aktualisieren", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Alles aktualisieren", "SSE.Views.ExternalLinksDlg.textUpdating": "Wird aktualisiert...", "SSE.Views.ExternalLinksDlg.txtTitle": "Externe Links", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Zwischensumme", "SSE.Views.FieldSettingsDialog.textReport": "Berichtsformular", "SSE.Views.FieldSettingsDialog.textTitle": "Feldeinstellungen", "SSE.Views.FieldSettingsDialog.txtAverage": "MITTELWERT", "SSE.Views.FieldSettingsDialog.txtBlank": "leere Reihen einfügen nach jeder", "SSE.Views.FieldSettingsDialog.txtBottom": "Im unteren Teil der Gruppe anzeigen", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompakt", "SSE.Views.FieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Benutzerdefinierter Name", "SSE.Views.FieldSettingsDialog.txtEmpty": "Elemente ohne Daten anzeigen", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Gliederung", "SSE.Views.FieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.FieldSettingsDialog.txtRepeat": "Elementbeschriftungen in jeder Zeile wiederholen", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Teilergebnissen anzeigen", "SSE.Views.FieldSettingsDialog.txtSourceName": "Name der Quelle:", "SSE.Views.FieldSettingsDialog.txtStdDev": "STABW", "SSE.Views.FieldSettingsDialog.txtStdDevp": "STABW.N", "SSE.Views.FieldSettingsDialog.txtSum": "Summe", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funktion für Zwischensummen", "SSE.Views.FieldSettingsDialog.txtTabular": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtTop": "Im oberen Teil der Gruppe anzeigen", "SSE.Views.FieldSettingsDialog.txtVar": "VARIANZ", "SSE.Views.FieldSettingsDialog.txtVarp": "VARIANZEN", "SSE.Views.FileMenu.btnBackCaption": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON> sch<PERSON>ßen", "SSE.Views.FileMenu.btnCreateNewCaption": "Neue erstellen", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "SSE.Views.FileMenu.btnExitCaption": "Schließen", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Versionshistorie", "SSE.Views.FileMenu.btnInfoCaption": "Tabelleninfo", "SSE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "Zuletzt benutzte öffnen", "SSE.Views.FileMenu.btnRenameCaption": "Umbenennen", "SSE.Views.FileMenu.btnReturnCaption": "Zurück zur Tabelle", "SSE.Views.FileMenu.btnRightsCaption": "Zugriffsrechte", "SSE.Views.FileMenu.btnSaveAsCaption": "Speichern als", "SSE.Views.FileMenu.btnSaveCaption": "Speichern", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "<PERSON><PERSON> speichern als", "SSE.Views.FileMenu.btnSettingsCaption": "Erweiterte Einstellungen", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON> bearbeiten", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Text hinzufügen", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Zugriffsrechte ändern", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Zuletzt ge<PERSON><PERSON><PERSON> von", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Zuletzt geändert", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Speicherort", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personen mit Berechtigungen", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON>a", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titel", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Hochgeladen", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Zugriffsrechte ändern", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Personen mit Berechtigungen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Modus \"Gemeinsame Bearbeitung\"", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "1904-Datumswerte verwenden", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Dezimaltrennzeichen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Sprache des Wörterbuchs", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Schriftglättung", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formelsprache ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Beispiel: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Wörter in GROSSBUCHSTABEN ignorieren", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "W<PERSON>rter mit Zahlen ignorieren", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Makro-Einstellungen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Z1S1-<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regionale Einstellungen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Beispiel:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Kommentare im Blatt anzeigen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Änderungen von anderen Benutzern anzeigen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Gelöste Kommentare anzeigen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Formal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Thema der Benutzeroberfläche", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Tausendertrennzeichen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Maßeinheit   ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Trennzeichen basierend auf regionalen Einstellungen verwenden", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Standard-Zoom-Wert", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Alle 10 Minuten", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Alle 30 Minuten", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Alle 5 Minuten", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Jede Stunde", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Autowiederherstellen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Automatisch speichern", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Deaktiviert", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Referenzstil", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Optionen von Autokorrektur...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarussisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Katalanisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Standard-Cache-Modus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Zentimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Zusammenarbeit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Tschechisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "D<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "De<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Bearbeitung und Speicherung", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Griechisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Zusammenarbeit in Echtzeit. Alle Änderungen werden automatisch gespeichert", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finnisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Franzö<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Ungarisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italienisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Koreanisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "wie OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Native", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polnisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Rechtschreibprüfung", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portugiesisch (Brasilien)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Die Schaltfläche Schnelldruck in der Kopfzeile des Editors anzeigen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Das Dokument wird auf dem zuletzt ausgewählten oder dem standardmäßigen Drucker gedruckt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Rumänisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "alle aktivieren", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Aktivieren aller Makros ohne Benachrichtigung", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Alle deaktivieren.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Alle Makros ohne Benachrichtigung deaktivieren", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Verwenden Sie die Schaltfläche \"Speichern\", um die vorgenommenen Änderungen zu synchronisieren.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Schwedisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Türkisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "U<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Verwenden Sie die Alt-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Verwenden Sie die Option-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamesisch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Benachrichtigungen anzeigen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "<PERSON>e Makros mit einer Benachrichtigung deaktivieren", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "wie Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Arbeitsbereich", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Präsentation schützen", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON>t Signatur", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON> bearbeiten", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Die Bearbeitung entfernt Signaturen aus dieser Tabellenkalkulation.<br>M<PERSON>chten Sie trotzdem fortsetzen?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON><PERSON> wurde mit einem Passwort geschützt", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "<PERSON><PERSON> muss signiert sein.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Gültige Signaturen wurden der Tabelle hinzugefügt. Die Tabelle ist vor der Bearbeitung geschützt.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Einige der digitalen Signaturen in der Tabelle sind ungültig oder konnten nicht verifiziert werden. Die Tabelle ist vor der Bearbeitung geschützt.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Signaturen anzeigen", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.text2Scales": "2-Farben-Skala", "SSE.Views.FormatRulesEditDlg.text3Scales": "3-Farben-Skal<PERSON>", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON> Rahmen<PERSON>en", "SSE.Views.FormatRulesEditDlg.textAppearance": "Balkendarstellung", "SSE.Views.FormatRulesEditDlg.textApply": "<PERSON><PERSON><PERSON> diesen Bereich anwenden:", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatisch", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Balkenrichtung", "SSE.Views.FormatRulesEditDlg.textBold": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "<PERSON><PERSON>e Ränder", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Die bedingte Formatierung kann nicht hinzugefügt werden.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Zellmittelpunkt", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Innere vertikale <PERSON>en", "SSE.Views.FormatRulesEditDlg.textClear": "Löschen", "SSE.Views.FormatRulesEditDlg.textColor": "Textfarbe", "SSE.Views.FormatRulesEditDlg.textContext": "Kontext", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON> diagonal nach unten", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON> diagonal nach oben", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "<PERSON><PERSON>en Si<PERSON> eine gültige Formel ein.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Die eingegebene Formel wird nicht in eine Zahl, ein <PERSON>, eine Uhrzeit oder eine Zeichenfolge ausgewertet.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON><PERSON> einen Wert ein.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Der eingegebene Wert ist keine gültige Zahl bzw. kein gültiger Datums-, Zeit- oder Zeichenfolgenwert.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "<PERSON> Wert für {0} muss größer sein als {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "<PERSON><PERSON><PERSON> Sie eine Zahl zwischen {0} und {1} ein.", "SSE.Views.FormatRulesEditDlg.textFill": "Ausfüllen", "SSE.Views.FormatRulesEditDlg.textFormat": "Formatierung", "SSE.Views.FormatRulesEditDlg.textFormula": "Formel", "SSE.Views.FormatRulesEditDlg.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabel": "wann {0} {1} und", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "wann {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "wann Wert ist", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Mindestens ein Symboldatenbereich überlappt.<br>Passen Sie die Werte des Symboldatenbereichs an, sodass die Bereiche nicht überlappen.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> innen", "SSE.Views.FormatRulesEditDlg.textInvalid": "Ungültiger Datenbereich", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Element", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "<PERSON>s nach rechts", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Ra<PERSON><PERSON><PERSON><PERSON> links", "SSE.Views.FormatRulesEditDlg.textLongBar": "Längster Balken", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maximaler Wert", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Innere horizontale Rahmenlinien", "SSE.Views.FormatRulesEditDlg.textMidpoint": "<PERSON><PERSON>lwer<PERSON>", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minimaler Wert", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativ", "SSE.Views.FormatRulesEditDlg.textNewColor": "Benutzerdefinierte Farbe", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNone": "Kein(e)", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Mindestens einer der angegebenen Werte ist kein gültiger Prozentwert.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Der angegebene Wert {0} ist kein gültiger Prozentsatz.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Mindestens einer der angegebenen Werte ist kein gültiges Quantil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Der angegebene Wert {0} ist kein gültiges Quantil.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> außen", "SSE.Views.FormatRulesEditDlg.textPercent": "Prozent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Quantil", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "Positiv", "SSE.Views.FormatRulesEditDlg.textPresets": "Voreinstellungen", "SSE.Views.FormatRulesEditDlg.textPreview": "Vorschau", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "In Kriterien der bedingten Formatierung für Farbskalen, Datenbalken oder Symbolsätze können keine relativen Bezüge verwendet werden.", "SSE.Views.FormatRulesEditDlg.textReverse": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Symbolen umkehren", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Von rechts nach links", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> rechts", "SSE.Views.FormatRulesEditDlg.textRule": "Regel", "SSE.Views.FormatRulesEditDlg.textSameAs": "wie positive", "SSE.Views.FormatRulesEditDlg.textSelectData": "Daten auswählen", "SSE.Views.FormatRulesEditDlg.textShortBar": "<PERSON><PERSON>rz<PERSON> Balken", "SSE.Views.FormatRulesEditDlg.textShowBar": "Nur Balken anzeigen", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Nur Symbol anzeigen", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Dieser Verweistyp kann in einer Formel vom Typ 'Bedingte Formatierung' nicht verwendet werden.<br>Ändern Sie den Bezug auf eine einzelne Zelle oder verwenden Sie den Bezug mit einer Arbeitsmappenfunktion. Beispiel: =SUMME(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Einfarbig", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Durchgestrichen", "SSE.Views.FormatRulesEditDlg.textSubscript": "Tiefgestellt", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Hochgestellt", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> oben", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Zahlenformat", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Rechnungswesen", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Währung", "SSE.Views.FormatRulesEditDlg.txtDate": "Datum", "SSE.Views.FormatRulesEditDlg.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.FormatRulesEditDlg.txtFraction": "Bru<PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Allgemeines", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON>in <PERSON>", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Prozentsatz", "SSE.Views.FormatRulesEditDlg.txtScientific": "Wissenschaftlich", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Uhrzeit", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Formatierungsregel bearbeiten", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Neue Formatierungsregel", "SSE.Views.FormatRulesManagerDlg.guestText": "Gas<PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 s über dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 s unter dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 s über dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 s unter dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 s über dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 s unter dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.textAbove": "Über dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.textApply": "<PERSON><PERSON><PERSON> für", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Zellwert beginnt mit", "SSE.Views.FormatRulesManagerDlg.textBelow": "Unter dem Durchschnitt", "SSE.Views.FormatRulesManagerDlg.textBetween": "ist zwischen {0} und {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Zellenwert", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Abgestufte Farbskala", "SSE.Views.FormatRulesManagerDlg.textContains": "Zellwert beinhaltet", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Die Zelle enthält einen leeren Wert.", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Die Zelle enthält einen <PERSON>hler.", "SSE.Views.FormatRulesManagerDlg.textDelete": "Löschen", "SSE.Views.FormatRulesManagerDlg.textDown": "Regel nach unten verschieben", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Zellwert endet mit", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Gleich oder über Durchschnitt", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "<PERSON><PERSON><PERSON> oder unter Durchschnitt", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formatierung", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Symbolsatz", "SSE.Views.FormatRulesManagerDlg.textNew": "neu", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "ist nicht zwischen {0} und {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Zellwert beinhaltet keine", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Die Zelle enthält keine leeren Werte.", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Die Zelle enthält keinen Fehler.", "SSE.Views.FormatRulesManagerDlg.textRules": "Regel", "SSE.Views.FormatRulesManagerDlg.textScope": "Formatierungsregeln anzeigen für:", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Daten auswählen", "SSE.Views.FormatRulesManagerDlg.textSelection": "Aktuelle Auswahl", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Dieses Arbeitsblatt", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textUnique": "Eindeutige Werte", "SSE.Views.FormatRulesManagerDlg.textUp": "Regel nach oben verschieben", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Das Element wird gerade von einem anderen Benutzer bearbeitet.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Bedingte Formatierung", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Dezimal", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "<PERSON><PERSON> verknü<PERSON>", "SSE.Views.FormatSettingsDialog.textSeparator": "1000er-Trennzeichen verwenden", "SSE.Views.FormatSettingsDialog.textSymbols": "Symbole", "SSE.Views.FormatSettingsDialog.textTitle": "Zahlenformat", "SSE.Views.FormatSettingsDialog.txtAccounting": "Rechnungswesen", "SSE.Views.FormatSettingsDialog.txtAs10": "Als Zehntel", "SSE.Views.FormatSettingsDialog.txtAs100": "Als Hundertstel", "SSE.Views.FormatSettingsDialog.txtAs16": "Als Sechzehntel", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs4": "Als viertel", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCurrency": "Währung", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Bitte geben Sie das benutzerdefinierte Zahlenformat aufmerksam ein. Der Editor von Tabellenkalkulationen überprüft Fehler in benutzerdefinierten Zahlenformaten nicht und diese können in Ihrer XLSX-Datei bleiben.", "SSE.Views.FormatSettingsDialog.txtDate": "Datum", "SSE.Views.FormatSettingsDialog.txtFraction": "Bru<PERSON>", "SSE.Views.FormatSettingsDialog.txtGeneral": "Allgemein", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Prozentsatz", "SSE.Views.FormatSettingsDialog.txtSample": "Beispiel:", "SSE.Views.FormatSettingsDialog.txtScientific": "Wissenschaftlich", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Zeit", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON><PERSON> zu e<PERSON>(1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON><PERSON><PERSON><PERSON> (131/135)", "SSE.Views.FormulaDialog.sDescription": "Beschreibung", "SSE.Views.FormulaDialog.textGroupDescription": "Funktionsgruppe auswählen", "SSE.Views.FormulaDialog.textListDescription": "Funktion auswählen", "SSE.Views.FormulaDialog.txtRecommended": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtSearch": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "Funktion einfügen", "SSE.Views.FormulaTab.textAutomatic": "Automatisch", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Das aktuelle Blatt berechnen", "SSE.Views.FormulaTab.textCalculateWorkbook": "Arbeit<PERSON><PERSON> be<PERSON>", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Gesamte Arbeitsmappe berechnen", "SSE.Views.FormulaTab.tipWatch": "Zellen zur Liste \"Überwachungsfenster\" hinzufügen", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "AutoSumme", "SSE.Views.FormulaTab.txtAutosumTip": "Summenbildung", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Funktion", "SSE.Views.FormulaTab.txtFormulaTip": "Funktion einfügen", "SSE.Views.FormulaTab.txtMore": "Weitere Funktionen", "SSE.Views.FormulaTab.txtRecent": "Zuletzt verwendet", "SSE.Views.FormulaTab.txtWatch": "Überwachungsfenster", "SSE.Views.FormulaWizard.textAny": "Alle", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Funktion", "SSE.Views.FormulaWizard.textFunctionRes": "Funktionsergebnis", "SSE.Views.FormulaWizard.textHelp": "Hilfe für diese Funktion", "SSE.Views.FormulaWizard.textLogical": "logisch", "SSE.Views.FormulaWizard.textNoArgs": "Die Funktion hat keine Argumente", "SSE.Views.FormulaWizard.textNumber": "nummer", "SSE.Views.FormulaWizard.textRef": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textText": "Text", "SSE.Views.FormulaWizard.textTitle": "Funktionsparameter", "SSE.Views.FormulaWizard.textValue": "Formelergebnis", "SSE.Views.HeaderFooterDialog.textAlign": "An Seitenrändern ausrichten", "SSE.Views.HeaderFooterDialog.textAll": "Alle Seiten", "SSE.Views.HeaderFooterDialog.textBold": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textColor": "Textfarbe", "SSE.Views.HeaderFooterDialog.textDate": "Datum", "SSE.Views.HeaderFooterDialog.textDiffFirst": "<PERSON><PERSON><PERSON> Se<PERSON> anders", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Gerade und ungerade Seiten anders", "SSE.Views.HeaderFooterDialog.textEven": "Gerade Seite", "SSE.Views.HeaderFooterDialog.textFileName": "Dateiname", "SSE.Views.HeaderFooterDialog.textFirst": "Erste Seite", "SSE.Views.HeaderFooterDialog.textFooter": "Fußzeile", "SSE.Views.HeaderFooterDialog.textHeader": "Kopfzeile", "SSE.Views.HeaderFooterDialog.textInsert": "Einfügen", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "Links", "SSE.Views.HeaderFooterDialog.textMaxError": "Die eingegebene Textzeichenfolge ist zu lang. Verringern Sie die Anzahl der verwendeten Zeichen.", "SSE.Views.HeaderFooterDialog.textNewColor": "Benutzerdefinierte Farbe", "SSE.Views.HeaderFooterDialog.textOdd": "Ungerade Seite", "SSE.Views.HeaderFooterDialog.textPageCount": "Anzahl der Seiten", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "Voreinstellungen", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Mit Dokument skalieren", "SSE.Views.HeaderFooterDialog.textSheet": "Blattname", "SSE.Views.HeaderFooterDialog.textStrikeout": "Durchgestrichen", "SSE.Views.HeaderFooterDialog.textSubscript": "Tiefgestellt", "SSE.Views.HeaderFooterDialog.textSuperscript": "Hochgestellt", "SSE.Views.HeaderFooterDialog.textTime": "Uhrzeit", "SSE.Views.HeaderFooterDialog.textTitle": "Kopf- und Fußzeileneinstellungen", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontSize": "Schriftgrad", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Anzeigen", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Verknüpfen mit", "SSE.Views.HyperlinkSettingsDialog.strRange": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Sheet", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Gewählter Bereich", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Geben Sie die Überschrift hier ein", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON> den <PERSON> hier ein", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON>eb<PERSON> Sie den QuickInfo-Text hier ein", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Externer Link", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "<PERSON> a<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Interner Datenbereich", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.HyperlinkSettingsDialog.textNames": "Definierte Namen", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Daten auswählen", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Arbeitsblätter", "SSE.Views.HyperlinkSettingsDialog.textTipText": "QuickInfo-Text", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink-Einstellungen", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON> muss eine URL im Format \"http://www.example.com\" enthalten", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "<PERSON><PERSON> soll maximal 2083 Zeichen beinhalten", "SSE.Views.ImageSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "Ausfüllen", "SSE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropToShape": "Auf Form <PERSON>", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Objekt bearbeiten", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON> Datei", "SSE.Views.ImageSettings.textFromStorage": "vom Speicher", "SSE.Views.ImageSettings.textFromUrl": "Aus URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "Um 90 ° gegen den Uhrzeigersinn drehen", "SSE.Views.ImageSettings.textHint90": "90° im UZS drehen", "SSE.Views.ImageSettings.textHintFlipH": "Horizontal kippen", "SSE.Views.ImageSettings.textHintFlipV": "Vertikal kippen", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textKeepRatio": "Seitenverhältnis beibehalten", "SSE.Views.ImageSettings.textOriginalSize": "Tatsächliche Größe", "SSE.Views.ImageSettings.textRecentlyUsed": "Zuletzt verwendet", "SSE.Views.ImageSettings.textRotate90": "90 Grad drehen", "SSE.Views.ImageSettings.textRotation": "Rotation", "SSE.Views.ImageSettings.textSize": "Größe", "SSE.Views.ImageSettings.textWidth": "Breite", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "<PERSON><PERSON> Verschieben oder Ändern der Größe mit Zellen", "SSE.Views.ImageSettingsAdvanced.textAlt": "Der alternative Text", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Beschreibung", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Gekippt", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontal", "SSE.Views.ImageSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON>eb<PERSON>, aber die Größe nicht ändern mit Zellen", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ImageSettingsAdvanced.textSnap": "Andocken an die Zelle", "SSE.Views.ImageSettingsAdvanced.textTitle": "Bild - Erweiterte Einstellungen", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Verschieben und Ändern der Größe mit Zellen", "SSE.Views.ImageSettingsAdvanced.textVertically": "Vertikal", "SSE.Views.LeftMenu.tipAbout": "Über das Produkt", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Kommentare", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Plugins", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Rechtschreibprüfung", "SSE.Views.LeftMenu.tipSupport": "Feed<PERSON> und Support", "SSE.Views.LeftMenu.txtDeveloper": "ENTWICKLERMODUS", "SSE.Views.LeftMenu.txtEditor": "Editor der Tabellenkalkulationen", "SSE.Views.LeftMenu.txtLimit": "Zugriffseinschränkung", "SSE.Views.LeftMenu.txtTrial": "Trial-Modus", "SSE.Views.LeftMenu.txtTrialDev": "Testversion für Entwickler-Modus", "SSE.Views.MacroDialog.textMacro": "Ma<PERSON><PERSON><PERSON>", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.okButtonText": "Speichern", "SSE.Views.MainSettingsPrint.strBottom": "Unten", "SSE.Views.MainSettingsPrint.strLandscape": "Querformat", "SSE.Views.MainSettingsPrint.strLeft": "Links", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Hochformat", "SSE.Views.MainSettingsPrint.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textActualSize": "Tatsächliche Größe", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "Benutzerdefinierte Optionen", "SSE.Views.MainSettingsPrint.textFitCols": "Alle Spalten auf einer Se<PERSON> darstellen", "SSE.Views.MainSettingsPrint.textFitPage": "Blatt auf einer Se<PERSON> darstellen", "SSE.Views.MainSettingsPrint.textFitRows": "Alle Zeilen auf einer Se<PERSON> darstellen", "SSE.Views.MainSettingsPrint.textPageOrientation": "Seitenorientierung", "SSE.Views.MainSettingsPrint.textPageScaling": "Skalierung", "SSE.Views.MainSettingsPrint.textPageSize": "Seitenformat", "SSE.Views.MainSettingsPrint.textPrintGrid": "Gitternetzlinien drucken", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Zeilen- und Spaltenüberschriften drucken", "SSE.Views.MainSettingsPrint.textRepeat": "<PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Spalten links wiederholen", "SSE.Views.MainSettingsPrint.textRepeatTop": "wiederhole Zeilen am Anfang", "SSE.Views.MainSettingsPrint.textSettings": "Einstellungen für", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Die bestehende benannte Bereiche können nicht bearbeitet werden und die neuen Bereiche können<br>im Moment nicht erstellt werden, weil einige von ihnen sind in Bearbeitung.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Definierter Name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Achtung", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Arbeitsmappe", "SSE.Views.NamedRangeEditDlg.textDataRange": "Datenbereich", "SSE.Views.NamedRangeEditDlg.textExistName": "FEHLER! Bereich mit einem solchen Namen existiert bereits", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Der Name muss mit einem Buchstaben oder Unterstrich beginnen und keine ungültigen Zeichen enthalten.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "FEHLER! Ungültiger Zellbereich", "SSE.Views.NamedRangeEditDlg.textIsLocked": "FEHLER! Dieses Element wird gerade von einem anderen Benutzer bearbeitet.", "SSE.Views.NamedRangeEditDlg.textName": "Name", "SSE.Views.NamedRangeEditDlg.textReservedName": "Der Name, den Si<PERSON> verwenden möchten, wurde bereits in Zellformeln verwiesen. Bitte benutzen Si<PERSON> einen anderen Namen.", "SSE.Views.NamedRangeEditDlg.textScope": "Umfang", "SSE.Views.NamedRangeEditDlg.textSelectData": "Daten auswählen", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Name bearbeiten", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Neuer Name", "SSE.Views.NamedRangePasteDlg.textNames": "Benannte Bereiche", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON>n ein<PERSON>ügen", "SSE.Views.NameManagerDlg.closeButtonText": "Schließen", "SSE.Views.NameManagerDlg.guestText": "Gas<PERSON>", "SSE.Views.NameManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Datenbereich", "SSE.Views.NameManagerDlg.textDelete": "Löschen", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "<PERSON><PERSON> benannte Bereiche wurden noch erstellt.<br><PERSON><PERSON><PERSON><PERSON> <PERSON> mindestens einen benannten Bereich und es wird in diesem Feld angezeigt.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "Alle", "SSE.Views.NameManagerDlg.textFilterDefNames": "Definierte Namen", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON>n, verfügbat für einen Blatt", "SSE.Views.NameManagerDlg.textFilterTableNames": "Tabellennamen", "SSE.Views.NameManagerDlg.textFilterWorkbook": "<PERSON>n, verfügbar für eine Arbeitsmappe", "SSE.Views.NameManagerDlg.textNew": "<PERSON>eu", "SSE.Views.NameManagerDlg.textnoNames": "<PERSON><PERSON> benannte Bereiche konnte dem entsprechenden Filter gefunden werden.", "SSE.Views.NameManagerDlg.textRanges": "Benannte Bereiche", "SSE.Views.NameManagerDlg.textScope": "Umfang", "SSE.Views.NameManagerDlg.textWorkbook": "Arbeitsmappe", "SSE.Views.NameManagerDlg.tipIsLocked": "Das Element wird gerade von einem anderen Benutzer bearbeitet.", "SSE.Views.NameManagerDlg.txtTitle": "Name-Manager", "SSE.Views.NameManagerDlg.warnDelete": "Möchten Sie den Namen {0} wirklich löschen?", "SSE.Views.PageMarginsDialog.textBottom": "Unten", "SSE.Views.PageMarginsDialog.textLeft": "Links", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strLineHeight": "Zeilenabstand", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Absatzabstand ", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "Vor ", "SSE.Views.ParagraphSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "SSE.Views.ParagraphSettings.textAt": "Um", "SSE.Views.ParagraphSettings.textAtLeast": "Mindestens", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "Automatisch", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Die festgelegten Registerkarten werden in diesem Feld erscheinen", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Alle Großbuchstaben", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Doppeltes Durchstreichen", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Einzüge ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Links", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Zeilenabstand", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Nach", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Vor ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Nach", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Einzüge und Abstände", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Kapitälchen", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Abstand", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Durchgestrichen", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Tiefgestellt", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Hochgestellt", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulator", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Ausrichtung", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Zeichenabstand", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Standardregisterkarte", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Effekte", "SSE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Hängend", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Blocksatz", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(<PERSON><PERSON>)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Entfernen", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Alle entfernen", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Links", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tabulatorposition", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Absatz - Erweiterte Einstellungen", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "ist gleich", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "endet nicht mit", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON>th<PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "enthält kein(e/en)", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "zwischen", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "nicht zwischen", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "ist nicht gleich", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "ist größer als", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "ist größer oder gleich", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "ist kleiner als", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "ist kleiner oder gleich", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "beginnt mit", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "beginnt nicht mit", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "endet mit", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Elemente mit folgender Beschriftung anzeigen:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Elemente anzeigen, für die Folgendes gilt:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Nutzen Sie das Symbol ?, um ein einziges Zeichen darzustellen", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Nutzen Sie das Symbol *, um eine Reihe von Zeichen darzustellen", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "und", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Label Filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "nach", "SSE.Views.PivotGroupDialog.textDays": "Tage", "SSE.Views.PivotGroupDialog.textEnd": "Beenden:", "SSE.Views.PivotGroupDialog.textError": "<PERSON><PERSON> muss einen numerischen Wert enthalten", "SSE.Views.PivotGroupDialog.textGreaterError": "<PERSON> Endzahl muss größer als die Startzahl sein.", "SSE.Views.PivotGroupDialog.textHour": "Stunden", "SSE.Views.PivotGroupDialog.textMin": "Minuten", "SSE.Views.PivotGroupDialog.textMonth": "Monate", "SSE.Views.PivotGroupDialog.textNumDays": "Anzahl der Tage", "SSE.Views.PivotGroupDialog.textQuart": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "Sekunden", "SSE.Views.PivotGroupDialog.textStart": "Starten:", "SSE.Views.PivotGroupDialog.textYear": "Jahre", "SSE.Views.PivotGroupDialog.txtTitle": "Gruppierung", "SSE.Views.PivotSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "SSE.Views.PivotSettings.textColumns": "Spalten", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON> au<PERSON>w<PERSON>", "SSE.Views.PivotSettings.textFilters": "Filter", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "Zu Spalten hinzufügen", "SSE.Views.PivotSettings.txtAddFilter": "<PERSON><PERSON> hinzuf<PERSON>", "SSE.Views.PivotSettings.txtAddRow": "<PERSON><PERSON> hinzuf<PERSON>", "SSE.Views.PivotSettings.txtAddValues": "<PERSON><PERSON>rten hinzufügen", "SSE.Views.PivotSettings.txtFieldSettings": "Feldeinstellungen", "SSE.Views.PivotSettings.txtMoveBegin": "Zum Anfang bewegen", "SSE.Views.PivotSettings.txtMoveColumn": "<PERSON>u <PERSON> verschieben", "SSE.Views.PivotSettings.txtMoveDown": "Nach unten bewegen", "SSE.Views.PivotSettings.txtMoveEnd": "Zum Ende verschieben", "SSE.Views.PivotSettings.txtMoveFilter": "<PERSON><PERSON> bewegen", "SSE.Views.PivotSettings.txtMoveRow": "<PERSON><PERSON> verschieben", "SSE.Views.PivotSettings.txtMoveUp": "Nach oben bewegen", "SSE.Views.PivotSettings.txtMoveValues": "<PERSON><PERSON> wechseln", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.strLayout": "Name und Layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternativer Text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Beschreibung", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Automatische Anpassung der Spaltenbreiten bei der Aktualisierung", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Datenbereich", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "zeige Felder in Bericht Filter", "SSE.Views.PivotSettingsAdvanced.textDown": "runter, dann über", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "G<PERSON><PERSON>tsummen", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Feld Überschrift", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.PivotSettingsAdvanced.textOver": "über, dann runter", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Daten auswählen", "SSE.Views.PivotSettingsAdvanced.textShowCols": "<PERSON><PERSON><PERSON> anzeigen", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Die Feldkopfzeilen für Zeilen und Spalten anzeigen.", "SSE.Views.PivotSettingsAdvanced.textShowRows": "<PERSON><PERSON><PERSON> anzeigen", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot-Tabelle fortgeschritten", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Berichtsfilterfelder pro Spalte:", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Berichtsfilterfelder pro Zeile:", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.PivotSettingsAdvanced.txtName": "Name", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "Gesamtergebnisse", "SSE.Views.PivotTable.capLayout": "Berichtslayout", "SSE.Views.PivotTable.capSubtotals": "Teilergebnisse", "SSE.Views.PivotTable.mniBottomSubtotals": "Alle Teilergebnisse unten in der Gruppe anzeigen", "SSE.Views.PivotTable.mniInsertBlankLine": "Fügen Sie nach jedem Element eine Leerzeile ein", "SSE.Views.PivotTable.mniLayoutCompact": "In Kurzformat anzeigen", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Wiederholen Si<PERSON> nicht alle Elementnamen", "SSE.Views.PivotTable.mniLayoutOutline": "In Gliederungsformat anzeigen", "SSE.Views.PivotTable.mniLayoutRepeat": "Alle Elementnamen wiederholen", "SSE.Views.PivotTable.mniLayoutTabular": "In Tabellenformat anzeigen", "SSE.Views.PivotTable.mniNoSubtotals": "<PERSON><PERSON>n anzeigen", "SSE.Views.PivotTable.mniOffTotals": "<PERSON><PERSON>r <PERSON> und Spalten deaktiviert", "SSE.Views.PivotTable.mniOnColumnsTotals": "Nur für Spalten aktiviert", "SSE.Views.PivotTable.mniOnRowsTotals": "Nur für Zeilen aktiviert", "SSE.Views.PivotTable.mniOnTotals": "<PERSON><PERSON>r <PERSON> und Spalten aktiviert", "SSE.Views.PivotTable.mniRemoveBlankLine": "<PERSON><PERSON> nach jedem Element entfernen", "SSE.Views.PivotTable.mniTopSubtotals": "Alle Teilergebnisse oben in der Gruppe anzeigen", "SSE.Views.PivotTable.textColBanded": "Verbundene Spalten", "SSE.Views.PivotTable.textColHeader": "Spaltenüberschriften", "SSE.Views.PivotTable.textRowBanded": "Verbundene Zeilen", "SSE.Views.PivotTable.textRowHeader": "Zeilenüberschriften", "SSE.Views.PivotTable.tipCreatePivot": "Pivot-Tabelle einfügen", "SSE.Views.PivotTable.tipGrandTotals": "Gesamtergebnisse ein- oder ausblenden", "SSE.Views.PivotTable.tipRefresh": "Die Informationen aus der Datenquelle aktualisieren", "SSE.Views.PivotTable.tipRefreshCurrent": "Aktualisierung der Informationen aus der Datenquelle für die aktuelle Tabelle", "SSE.Views.PivotTable.tipSelect": "Die gesamte Pivot-Tabelle wählen", "SSE.Views.PivotTable.tipSubtotals": "Teilergebnisse ein- oder ausblenden", "SSE.Views.PivotTable.txtCreate": "<PERSON>bell<PERSON> e<PERSON>fügen", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Einstellbar", "SSE.Views.PivotTable.txtGroupPivot_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Light": "Hell", "SSE.Views.PivotTable.txtGroupPivot_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtPivotTable": "Pivot-Tabelle", "SSE.Views.PivotTable.txtRefresh": "Aktualisieren", "SSE.Views.PivotTable.txtRefreshAll": "Alles aktualisieren", "SSE.Views.PivotTable.txtSelect": "Auswählen", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot-Tabelle Stil Dunkel", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot-Tabelle Stil Hell", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot-Tabelle Stil Standard", "SSE.Views.PrintSettings.btnDownload": "Speichern & Herunterladen", "SSE.Views.PrintSettings.btnPrint": "Speichern & drucken", "SSE.Views.PrintSettings.strBottom": "Unten", "SSE.Views.PrintSettings.strLandscape": "Querformat", "SSE.Views.PrintSettings.strLeft": "Links", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Hochformat", "SSE.Views.PrintSettings.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Anzeigen", "SSE.Views.PrintSettings.strTop": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textActualSize": "Tatsächliche Größe", "SSE.Views.PrintSettings.textAllSheets": "Alle Blätter", "SSE.Views.PrintSettings.textCurrentSheet": "Aktuelles Blatt", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "Benutzerdefinierte Optionen", "SSE.Views.PrintSettings.textFitCols": "Alle Spalten auf einer Se<PERSON> darstellen", "SSE.Views.PrintSettings.textFitPage": "Blatt auf einer Se<PERSON> darstellen", "SSE.Views.PrintSettings.textFitRows": "Alle Zeilen auf einer Se<PERSON> darstellen", "SSE.Views.PrintSettings.textHideDetails": "Details verbergen", "SSE.Views.PrintSettings.textIgnore": "Druckauswahl ignorieren", "SSE.Views.PrintSettings.textLayout": "Layout", "SSE.Views.PrintSettings.textPageOrientation": "Seitenorientierung", "SSE.Views.PrintSettings.textPageScaling": "Skalierung", "SSE.Views.PrintSettings.textPageSize": "Seitenformat", "SSE.Views.PrintSettings.textPrintGrid": "Gitternetzlinien drucken", "SSE.Views.PrintSettings.textPrintHeadings": "Zeilen- und Spaltenüberschriften drucken", "SSE.Views.PrintSettings.textPrintRange": "Dr<PERSON>berei<PERSON>", "SSE.Views.PrintSettings.textRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRepeat": "<PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintSettings.textRepeatLeft": "Spalten links wiederholen", "SSE.Views.PrintSettings.textRepeatTop": "wiederhole Zeilen am Anfang", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Seiten-Einstellungen", "SSE.Views.PrintSettings.textShowDetails": "Details anzeigen", "SSE.Views.PrintSettings.textShowGrid": "Gitternetzlinien anzeigen", "SSE.Views.PrintSettings.textShowHeadings": "Zeilen und Spaltenüberschriften anzeigen", "SSE.Views.PrintSettings.textTitle": "Druck-Einstellungen", "SSE.Views.PrintSettings.textTitlePDF": "PDF Einstellungen", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Eingefrorene Spalten", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.PrintTitlesDialog.textLeft": "Spalten links wiederholen", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON>cht wiederholen", "SSE.Views.PrintTitlesDialog.textRepeat": "<PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON> auswählen", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTop": "wiederhole Zeilen am Anfang", "SSE.Views.PrintWithPreview.txtActualSize": "Tatsächliche Größe", "SSE.Views.PrintWithPreview.txtAllSheets": "Alle Blätter", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "In allen Blättern anwenden", "SSE.Views.PrintWithPreview.txtBottom": "Unten", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Aktuelles Blatt", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "Benutzerdefinierte Optionen", "SSE.Views.PrintWithPreview.txtEmptyTable": "<PERSON>s gibt nichts zu drucken", "SSE.Views.PrintWithPreview.txtFitCols": "Alle Spalten auf einer Se<PERSON> darstellen", "SSE.Views.PrintWithPreview.txtFitPage": "Blatt auf einer Se<PERSON> darstellen", "SSE.Views.PrintWithPreview.txtFitRows": "Alle Zeilen auf einer Se<PERSON> darstellen", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gitternetzlinien und Überschriften", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Kopf- und Fußzeileneinstellungen", "SSE.Views.PrintWithPreview.txtIgnore": "Druckauswahl ignorieren", "SSE.Views.PrintWithPreview.txtLandscape": "Querformat", "SSE.Views.PrintWithPreview.txtLeft": "Links", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "von {0}", "SSE.Views.PrintWithPreview.txtPage": "Seite", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Ungültige Seitennummer", "SSE.Views.PrintWithPreview.txtPageOrientation": "Seitenorientierung", "SSE.Views.PrintWithPreview.txtPageSize": "Seitenformat", "SSE.Views.PrintWithPreview.txtPortrait": "Hochformat", "SSE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintGrid": "Gitternetzlinien drucken", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Zeilen- und Spaltenüberschriften drucken", "SSE.Views.PrintWithPreview.txtPrintRange": "Dr<PERSON>berei<PERSON>", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Spalten links wiederholen", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Zeilen am Anfang wiederholen", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Speichern", "SSE.Views.PrintWithPreview.txtScaling": "Skalierung", "SSE.Views.PrintWithPreview.txtSelection": "Auswahl", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Blatteinstellungen", "SSE.Views.PrintWithPreview.txtSheet": "Blatt: {0}", "SSE.Views.PrintWithPreview.txtTop": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.textExistName": "FEHLER! Es gibt schon einen Bereich mit diesem Titel", "SSE.Views.ProtectDialog.textInvalidName": "Der Bereich soll mit einem Buchstaben anfangen und soll nur Buchstaben, Zahlen und Lücken beinhalten.", "SSE.Views.ProtectDialog.textInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.ProtectDialog.textSelectData": "Daten auswählen", "SSE.Views.ProtectDialog.txtAllow": "Alle Benutzer dieser Arbeitsblätter können", "SSE.Views.ProtectDialog.txtAutofilter": "AutoFilter verwenden", "SSE.Views.ProtectDialog.txtDelCols": "Spalten löschen", "SSE.Views.ProtectDialog.txtDelRows": "Zeilen löschen", "SSE.Views.ProtectDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.ProtectDialog.txtFormatCells": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtFormatCols": "Spalten formatieren", "SSE.Views.ProtectDialog.txtFormatRows": "Zeilen formatieren", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Bestätigungseingabe ist nicht identisch", "SSE.Views.ProtectDialog.txtInsCols": "Spalten einfügen", "SSE.Views.ProtectDialog.txtInsHyper": "Hyperlink einfügen", "SSE.Views.ProtectDialog.txtInsRows": "Zeilen einfügen", "SSE.Views.ProtectDialog.txtObjs": "Objekte bearbeiten", "SSE.Views.ProtectDialog.txtOptional": "optional", "SSE.Views.ProtectDialog.txtPassword": "Passwort", "SSE.Views.ProtectDialog.txtPivot": "PivotTable und PivotChart verwenden", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "Titel", "SSE.Views.ProtectDialog.txtRepeat": "Kenn<PERSON><PERSON> wiederholen", "SSE.Views.ProtectDialog.txtScen": "<PERSON><PERSON><PERSON><PERSON> bear<PERSON>ten", "SSE.Views.ProtectDialog.txtSelLocked": "Gesperrte Zellen auswählen", "SSE.Views.ProtectDialog.txtSelUnLocked": "Aufgesperrte Zellen auswählen", "SSE.Views.ProtectDialog.txtSheetDescription": "Unerwünschte Änderungen durch andere Personen verhindern, indem deren Bearbeitungsmöglichkeiten eingeschränkt werden.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Vorsicht: <PERSON>n Si<PERSON> das Kennwort verlieren oder vergessen, lässt es sich nicht mehr wiederherstellen. Bewahren Sie es an einem sicheren Ort auf.", "SSE.Views.ProtectDialog.txtWBDescription": "Um den Benutzern das Öffnen von ausgeblendeten Arbeitsblättern, Hinzufügen, Verschieben oder Ausblenden und Umbenennen von Arbeitsblättern zu verbieten, schützen Sie die Arbeitsmappenstruktur mit einem Passwort.", "SSE.Views.ProtectDialog.txtWBTitle": "Arbeitsmappenstruktur schützen", "SSE.Views.ProtectRangesDlg.guestText": "Gas<PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Löschen", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Keine Bereiche für Bearbeitung gefunden.", "SSE.Views.ProtectRangesDlg.textNew": "<PERSON>eu", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textPwd": "Passwort", "SSE.Views.ProtectRangesDlg.textRange": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "<PERSON><PERSON><PERSON>, für die ein Kennwort die Sperre aufhebt (nur für gesperrte Zellen)", "SSE.Views.ProtectRangesDlg.textTitle": "Titel", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Das Element wird gerade von einem anderen Benutzer bearbeitet.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON><PERSON> bear<PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtNo": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtTitle": "Den Benutzern Bearbeitung der Bereiche erlauben", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON>a", "SSE.Views.ProtectRangesDlg.warnDelete": "Möchten Sie den Namen {0} wirklich löschen?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Spalten", "SSE.Views.RemoveDuplicatesDialog.textDescription": "W<PERSON>hlen Sie zum Löschen doppelter Werte mindestens eine Spalte aus, die doppelte Werte enthält.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Meine Daten haben Kopfzeilen", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Alles auswählen", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Entferne Duplikate", "SSE.Views.RightMenu.txtCellSettings": "Zelleneinstellungen", "SSE.Views.RightMenu.txtChartSettings": "Diagrammeinstellungen", "SSE.Views.RightMenu.txtImageSettings": "Bild-Einstellungen", "SSE.Views.RightMenu.txtParagraphSettings": "Paragraf-Einstellungen", "SSE.Views.RightMenu.txtPivotSettings": "Einstellungen der Pivot-Tabelle", "SSE.Views.RightMenu.txtSettings": "Allgemeine Einstellungen", "SSE.Views.RightMenu.txtShapeSettings": "Form-Einstellungen", "SSE.Views.RightMenu.txtSignatureSettings": "Signatureinstellungen", "SSE.Views.RightMenu.txtSlicerSettings": "Einstellungen vom Datenschnitt", "SSE.Views.RightMenu.txtSparklineSettings": "Sparkline Einstellungen", "SSE.Views.RightMenu.txtTableSettings": "Tabellen-Einstellungen", "SSE.Views.RightMenu.txtTextArtSettings": "TextArt-Einstellungen", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "Der eingegebene Wert ist falsch.", "SSE.Views.ScaleDialog.textFewPages": "Seiten", "SSE.Views.ScaleDialog.textFitTo": "Anpassen an", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "Seiten", "SSE.Views.ScaleDialog.textOnePage": "Seite", "SSE.Views.ScaleDialog.textScaleTo": "Anpassen an", "SSE.Views.ScaleDialog.textTitle": "Maßstab-Einstellungen", "SSE.Views.ScaleDialog.textWidth": "Breite", "SSE.Views.SetValueDialog.txtMaxText": "Der maximale Wert für dieses Feld ist {0}", "SSE.Views.SetValueDialog.txtMinText": "Der minimale Wert für dieses Feld ist {0}", "SSE.Views.ShapeSettings.strBackground": "Hintergrundfarbe", "SSE.Views.ShapeSettings.strChange": "AutoForm ändern", "SSE.Views.ShapeSettings.strColor": "Farbe", "SSE.Views.ShapeSettings.strFill": "Füllung", "SSE.Views.ShapeSettings.strForeground": "Vordergrundfarbe", "SSE.Views.ShapeSettings.strPattern": "Muster", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON> anzeigen", "SSE.Views.ShapeSettings.strSize": "Größe", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Undurchsichtigkeit", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 0 pt und 1584 pt ein.", "SSE.Views.ShapeSettings.textColor": "Farbfüllung", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON> Datei", "SSE.Views.ShapeSettings.textFromStorage": "vom Speicher", "SSE.Views.ShapeSettings.textFromUrl": "Aus URL", "SSE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textGradientFill": "Füllung mit Farbverlauf", "SSE.Views.ShapeSettings.textHint270": "Um 90 ° gegen den Uhrzeigersinn drehen", "SSE.Views.ShapeSettings.textHint90": "90° im UZS drehen", "SSE.Views.ShapeSettings.textHintFlipH": "Horizontal kippen", "SSE.Views.ShapeSettings.textHintFlipV": "Vertikal kippen", "SSE.Views.ShapeSettings.textImageTexture": "Bild oder Textur", "SSE.Views.ShapeSettings.textLinear": "linear", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textOriginalSize": "Originalgröße", "SSE.Views.ShapeSettings.textPatternFill": "Muster", "SSE.Views.ShapeSettings.textPosition": "Stellung", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Zuletzt verwendet", "SSE.Views.ShapeSettings.textRotate90": "90 Grad drehen", "SSE.Views.ShapeSettings.textRotation": "Rotation", "SSE.Views.ShapeSettings.textSelectImage": "Bild auswählen", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "Ausdehnung", "SSE.Views.ShapeSettings.textStyle": "Stil", "SSE.Views.ShapeSettings.textTexture": "Aus Textur", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "SSE.Views.ShapeSettings.txtBrownPaper": "Kraftpapier", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON>nk<PERSON> Stoff", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>z", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Spalten", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON> um den Text", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "<PERSON><PERSON> Verschieben oder Ändern der Größe mit Zellen", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Der alternative Text", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Beschreibung", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Automatisch anpassen", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Startgröße", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Startlinienart", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Schräge Kante", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Unten", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Zierbuchstabe", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Endgröße", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Endlinienart", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Gekippt", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontal", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Verknüpfungstyp", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Seitenverhältnis beibehalten", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Links", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON>eb<PERSON>, aber die Größe nicht ändern mit Zellen", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "erlaube Text, über die Form zu fließen", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Die Form am Text anpassen", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ShapeSettingsAdvanced.textRound": "Rund", "SSE.Views.ShapeSettingsAdvanced.textSize": "Größe", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Andocken an die Zelle", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Abstand zwischen Spalten", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Form - Erweiterte Einstellungen", "SSE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Verschieben und Ändern der Größe mit Zellen", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Vertikal", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Stärken & Pfeile", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Breite", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDelete": "Signatur entfernen", "SSE.Views.SignatureSettings.strDetails": "Signaturdetails", "SSE.Views.SignatureSettings.strInvalid": "Ungültige Signaturen", "SSE.Views.SignatureSettings.strRequested": "Angeforderte Signaturen", "SSE.Views.SignatureSettings.strSetup": "Signatureinrichtung", "SSE.Views.SignatureSettings.strSign": "Signieren", "SSE.Views.SignatureSettings.strSignature": "Signatur", "SSE.Views.SignatureSettings.strSigner": "Signaturgeber", "SSE.Views.SignatureSettings.strValid": "Gültige Signaturen", "SSE.Views.SignatureSettings.txtContinueEditing": "Trotzdem bearbeiten", "SSE.Views.SignatureSettings.txtEditWarning": "Die Bearbeitung entfernt Signaturen aus dieser Tabellenkalkulation.<br>M<PERSON>chten Sie trotzdem fortsetzen?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Möchten Sie diese Signatur wirklich entfernen?<br>Dies kann nicht rückgängig gemacht werden.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "<PERSON><PERSON> muss signiert sein.", "SSE.Views.SignatureSettings.txtSigned": "Gültige Signaturen wurden der Tabelle hinzugefügt. Die Tabelle ist vor der Bearbeitung geschützt.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Einige der digitalen Signaturen in der Tabelle sind ungültig oder konnten nicht verifiziert werden. Die Tabelle ist vor der Bearbeitung geschützt.", "SSE.Views.SlicerAddDialog.textColumns": "Spalten", "SSE.Views.SlicerAddDialog.txtTitle": "Datenschnitte einfügen", "SSE.Views.SlicerSettings.strHideNoData": "verberge Element ohne Daten", "SSE.Views.SlicerSettings.strIndNoData": "Elemente ohne Daten visuell kennzeichnen", "SSE.Views.SlicerSettings.strShowDel": "Aus der Datenquelle entfernte Elemente anzeigen", "SSE.Views.SlicerSettings.strShowNoData": "Leere Elemente am Ende anzeigen", "SSE.Views.SlicerSettings.strSorting": "Sortierung und Filterung", "SSE.Views.SlicerSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "SSE.Views.SlicerSettings.textAsc": "Aufsteigend", "SSE.Views.SlicerSettings.textAZ": "A bis Z", "SSE.Views.SlicerSettings.textButtons": "Schaltflächen", "SSE.Views.SlicerSettings.textColumns": "Spalten", "SSE.Views.SlicerSettings.textDesc": "Absteigend", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "konstante Proportionen", "SSE.Views.SlicerSettings.textLargeSmall": "gr<PERSON><PERSON><PERSON> zu kleinste", "SSE.Views.SlicerSettings.textLock": "schalte Größe ändern und Bewegen aus", "SSE.Views.SlicerSettings.textNewOld": "neueste zu ältester", "SSE.Views.SlicerSettings.textOldNew": "älteste zu neueste", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "Größe", "SSE.Views.SlicerSettings.textSmallLarge": "kleinste zur größeste", "SSE.Views.SlicerSettings.textStyle": "Stil", "SSE.Views.SlicerSettings.textVert": "Vertikal", "SSE.Views.SlicerSettings.textWidth": "Breite", "SSE.Views.SlicerSettings.textZA": "Z bis A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Schaltflächen", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Spalten", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "verberge Element ohne Daten", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Elemente ohne Daten visuell kennzeichnen", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Verweise", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Aus der Datenquelle entfernte Elemente anzeigen", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Kopfzeile anzeigen", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Leere Elemente am Ende anzeigen", "SSE.Views.SlicerSettingsAdvanced.strSize": "Größe", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sortierung und Filterung", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stil", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Stil und Größe", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Breite", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "kein Bewegen oder Größenänderung mit", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternativer Text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Beschreibung", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Aufsteigend", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A bis Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Absteigend", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Name zur Nutzung in Formeln", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Kopfzeile", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "konstante Proportionen", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "gr<PERSON><PERSON><PERSON> zu kleinste", "SSE.Views.SlicerSettingsAdvanced.textName": "Name", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "neueste zu ältester", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "älteste zu neueste", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON>, aber nicht Größe ändern mit", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "kleinste zu größeste", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Andocken an die Zelle", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Name der <PERSON>lle", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Datenschnitt - Erweiterte Einstellungen", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Bewegen und Größenänderung mit Zellen", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z bis A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.SortDialog.errorEmpty": "<PERSON>ür alle Sortierkriterien muss eine Spalte oder Zeile angegeben sein.", "SSE.Views.SortDialog.errorMoreOneCol": "<PERSON>s ist mehr als eine Spalte ausgewählt.", "SSE.Views.SortDialog.errorMoreOneRow": "<PERSON>s ist mehr als eine Zeile ausgewählt.", "SSE.Views.SortDialog.errorNotOriginalCol": "Die ausgewählte Spalte befindet sich nicht im ursprünglich ausgewählten Bereich.", "SSE.Views.SortDialog.errorNotOriginalRow": "Die ausgewählte Zeile befindet sich nicht im ursprünglich ausgewählten Bereich.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 wird mehrmals nach derselben Farbe sortiert.<br>Löschen Sie die doppelten Sortierkriterien und versuchen Sie es erneut.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 wird mehrmals nach Werten sortiert.<br>Löschen Sie die doppelten Sortierkriterien und versuchen Sie es erneut.", "SSE.Views.SortDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textAsc": "Aufsteigend", "SSE.Views.SortDialog.textAuto": "Automatisch", "SSE.Views.SortDialog.textAZ": "A bis Z", "SSE.Views.SortDialog.textBelow": "Unten", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textColumn": "<PERSON>lt<PERSON>", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON><PERSON> kop<PERSON>en", "SSE.Views.SortDialog.textDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDesc": "Absteigend", "SSE.Views.SortDialog.textDown": "Ebene nach unten verschieben", "SSE.Views.SortDialog.textFontColor": "Schriftfarbe", "SSE.Views.SortDialog.textLeft": "Links", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON>...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON> Zeilen...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Optionen", "SSE.Views.SortDialog.textOrder": "Reihenfolge", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Sortierung", "SSE.Views.SortDialog.textSortBy": "Sortieren nach", "SSE.Views.SortDialog.textThenBy": "<PERSON>n nach", "SSE.Views.SortDialog.textTop": "<PERSON><PERSON>", "SSE.Views.SortDialog.textUp": "Ebene nach oben verschieben", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z bis A", "SSE.Views.SortDialog.txtInvalidRange": "Ungültiger Zellenbereich.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "aufsteigend (A bis Z) nach", "SSE.Views.SortFilterDialog.textDesc": "absteigend (Z bis A) nach", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortOptionsDialog.textCase": "Groß-/Kleinschreibung beachten", "SSE.Views.SortOptionsDialog.textHeaders": "Meine Daten enthalten Kopfzeilen", "SSE.Views.SortOptionsDialog.textLeftRight": "<PERSON> links nach rechts sortieren", "SSE.Views.SortOptionsDialog.textOrientation": "Ausrichtung", "SSE.Views.SortOptionsDialog.textTitle": "Sortieroptionen", "SSE.Views.SortOptionsDialog.textTopBottom": "Von oben nach unten sortieren", "SSE.Views.SpecialPasteDialog.textAdd": "Hinzufügen", "SSE.Views.SpecialPasteDialog.textAll": "Alle", "SSE.Views.SpecialPasteDialog.textBlanks": "überspringe leere", "SSE.Views.SpecialPasteDialog.textColWidth": "Spaltenbreite", "SSE.Views.SpecialPasteDialog.textComments": "Kommentare", "SSE.Views.SpecialPasteDialog.textDiv": "Un<PERSON>teile<PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formeln & Formatierung", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formeln & Zahlenformat", "SSE.Views.SpecialPasteDialog.textFormats": "Formate", "SSE.Views.SpecialPasteDialog.textFormulas": "Formeln", "SSE.Views.SpecialPasteDialog.textFWidth": "Formeln & Spaltenbreite", "SSE.Views.SpecialPasteDialog.textMult": "multiplizieren", "SSE.Views.SpecialPasteDialog.textNone": "kein", "SSE.Views.SpecialPasteDialog.textOperation": "Aktion", "SSE.Views.SpecialPasteDialog.textPaste": "Einfügen", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "Spezielles Einfügen", "SSE.Views.SpecialPasteDialog.textTranspose": "Vert<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Werte & Formatierung", "SSE.Views.SpecialPasteDialog.textVNFormat": "Werte & Zahlenformate", "SSE.Views.SpecialPasteDialog.textWBorders": "alles außer Grenzen", "SSE.Views.Spellcheck.noSuggestions": "<PERSON><PERSON>schlä<PERSON>", "SSE.Views.Spellcheck.textChange": "Ändern", "SSE.Views.Spellcheck.textChangeAll": "Alles ä<PERSON>n", "SSE.Views.Spellcheck.textIgnore": "Ignorieren", "SSE.Views.Spellcheck.textIgnoreAll": "Alles ignorieren", "SSE.Views.Spellcheck.txtAddToDictionary": "Zum Wörterbuch hinzufügen", "SSE.Views.Spellcheck.txtClosePanel": "Rechtschreibprüfung schließen", "SSE.Views.Spellcheck.txtComplete": "Die Rechtschreibprüfung wurde abgeschlossen", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Sprache des Wörterbuchs", "SSE.Views.Spellcheck.txtNextTip": "Zum nächsten Wort wechseln", "SSE.Views.Spellcheck.txtSpelling": "Rechtschreibung", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON> zum Ende kopieren)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON> zum Ende verschieben)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Vor dem Blatt kopieren lassen ", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON> versch<PERSON>", "SSE.Views.Statusbar.filteredRecordsText": "{0} von {1} Einträge sind gefiltert", "SSE.Views.Statusbar.filteredText": "Filter-Modus", "SSE.Views.Statusbar.itemAverage": "MITTELWERT", "SSE.Views.Statusbar.itemCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemCount": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemDelete": "Löschen", "SSE.Views.Statusbar.itemHidden": "Ausgeblendet", "SSE.Views.Statusbar.itemHide": "Verbergen", "SSE.Views.Statusbar.itemInsert": "Einfügen", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMove": "Verschieben", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "Umbenennen", "SSE.Views.Statusbar.itemStatus": "Status der Speicherung", "SSE.Views.Statusbar.itemSum": "Summe", "SSE.Views.Statusbar.itemTabColor": "Farbe des Tabulators", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Das Arbeitsblatt mit demselben Namen ist bereits vorhanden.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Der Blattname kann die folgenden Zeichen nicht enthalten: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Blattname", "SSE.Views.Statusbar.selectAllSheets": "Alle Blätter auswählen ", "SSE.Views.Statusbar.sheetIndexText": "Liste {0} von {1}", "SSE.Views.Statusbar.textAverage": "<PERSON><PERSON>lwer<PERSON>", "SSE.Views.Statusbar.textCount": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Benutzerdefinierte Farbe", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON>", "SSE.Views.Statusbar.textSum": "Summe", "SSE.Views.Statusbar.tipAddTab": "Arbeitsblatt hinzufügen", "SSE.Views.Statusbar.tipFirst": "Bis zum ersten Blatt blättern", "SSE.Views.Statusbar.tipLast": "Bis zum letzten Blatt blättern", "SSE.Views.Statusbar.tipListOfSheets": "<PERSON><PERSON> von Blättern", "SSE.Views.Statusbar.tipNext": "Blattliste nach rechts blättern", "SSE.Views.Statusbar.tipPrev": "Blattliste nach links blättern", "SSE.Views.Statusbar.tipZoomFactor": "Zoommodus", "SSE.Views.Statusbar.tipZoomIn": "Vergrößern", "SSE.Views.Statusbar.tipZoomOut": "Verkleinern", "SSE.Views.Statusbar.ungroupSheets": "Gruppierung von Arbeitsblättern aufheben", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Der Vorgang kann für einen ausgewählten Zellbereich nicht ausgeführt werden.<br>Wählen Sie einen einheitlichen Datenbereich, der sich deutlich von dem bestehenden unterscheidet und versuchen Sie es erneut.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Die Operation konnte nicht für den markierten Zellbereich abgeschlossen werden. <br> <PERSON><PERSON><PERSON><PERSON> Sie einen Bereich so aus, dass die erste Tabellenzeile in der gleichen Zeile war <br> und Ergebnistabelle die aktuelle überlappte. ", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Die Operation konnte nicht für den markierten Zellbereich abgeschlossen werden. <br> <PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen Bereich, der keine andere Tabellen einschließt.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Matrixformeln mit mehreren Zellen sind in Tabellen nicht zulässig.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "FEHLER! Ungültiger Zellenbereich", "SSE.Views.TableOptionsDialog.txtNote": "Die Kopfzeilen müssen in derselben Zeile verbleiben, und der resultierende Tabellenbereich muss den ursprünglichen Tabellenbereich überlappen.", "SSE.Views.TableOptionsDialog.txtTitle": "Titel", "SSE.Views.TableSettings.deleteColumnText": "Spalte löschen", "SSE.Views.TableSettings.deleteRowText": "Zeile löschen", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Views.TableSettings.insertColumnLeftText": "Spalte links einfügen", "SSE.Views.TableSettings.insertColumnRightText": "Spalte rechts einfügen", "SSE.Views.TableSettings.insertRowAboveText": "Zeile oberhalb einfügen", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> unterhalb einfügen", "SSE.Views.TableSettings.notcriticalErrorTitle": "Achtung", "SSE.Views.TableSettings.selectColumnText": "Ganze Spalte auswählen", "SSE.Views.TableSettings.selectDataText": "Spaltendaten auswählen", "SSE.Views.TableSettings.selectRowText": "Zeile auswählen", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> auswählen", "SSE.Views.TableSettings.textActions": "Vorgänge mit Tabellen", "SSE.Views.TableSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "SSE.Views.TableSettings.textBanded": "Gestreift", "SSE.Views.TableSettings.textColumns": "Spalten", "SSE.Views.TableSettings.textConvertRange": "In Zellenbereich konvertieren", "SSE.Views.TableSettings.textEdit": "Zeilen & Spalten", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON>", "SSE.Views.TableSettings.textExistName": "FEHLER! Der Bereich mit demselben Namen existiert bereits", "SSE.Views.TableSettings.textFilter": "<PERSON>", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "Kopfzeile", "SSE.Views.TableSettings.textInvalidName": "FEHLER! Ungültiger Tabellenname", "SSE.Views.TableSettings.textIsLocked": "Das Element wird gerade von einem anderen Benutzer bearbeitet.", "SSE.Views.TableSettings.textLast": "Letzte", "SSE.Views.TableSettings.textLongOperation": "Operation ist zeitaufwendig", "SSE.Views.TableSettings.textPivot": "Pivot-Tabelle einfügen", "SSE.Views.TableSettings.textRemDuplicates": "Entferne Duplikate", "SSE.Views.TableSettings.textReservedName": "Der Name, den Si<PERSON> verwenden möchten, wurde bereits in Zellformeln verwiesen. Bitte benutzen Si<PERSON> einen anderen Namen.", "SSE.Views.TableSettings.textResize": "Tabellengröße ändern", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Daten auswählen", "SSE.Views.TableSettings.textSlicer": "Datenschnitt einfügen", "SSE.Views.TableSettings.textTableName": "Tabellenname", "SSE.Views.TableSettings.textTemplate": "Vorlage auswählen", "SSE.Views.TableSettings.textTotal": "Insgesamt", "SSE.Views.TableSettings.warnLongOperation": "Die Operation, die Sie durchführen möchten, kann viel Zeit in Anspruch nehmen. <br> <PERSON><PERSON> sie fortgesetzt werden?", "SSE.Views.TableSettingsAdvanced.textAlt": "Der alternative Text", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Beschreibung", "SSE.Views.TableSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabelle - Erweiterte Einstellungen", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "Einstellbar", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "Hell", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "Tabellenformat - Dunkel", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "Tabellenformat - Hell ", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "Tabellenformat - Mittel", "SSE.Views.TextArtSettings.strBackground": "Hintergrundfarbe", "SSE.Views.TextArtSettings.strColor": "Farbe", "SSE.Views.TextArtSettings.strFill": "Füllung", "SSE.Views.TextArtSettings.strForeground": "Vordergrundfarbe", "SSE.Views.TextArtSettings.strPattern": "Muster", "SSE.Views.TextArtSettings.strSize": "Größe", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Undurchsichtigkeit", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 0 pt und 1584 pt ein.", "SSE.Views.TextArtSettings.textColor": "Farbfüllung", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON> Datei", "SSE.Views.TextArtSettings.textFromUrl": "Aus URL", "SSE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textGradientFill": "Füllung mit Farbverlauf", "SSE.Views.TextArtSettings.textImageTexture": "Bild oder Textur", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "Muster", "SSE.Views.TextArtSettings.textPosition": "Stellung", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "Ausdehnung", "SSE.Views.TextArtSettings.textStyle": "Stil", "SSE.Views.TextArtSettings.textTemplate": "Vorlage", "SSE.Views.TextArtSettings.textTexture": "Aus Textur", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "Transformieren", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "SSE.Views.TextArtSettings.txtBrownPaper": "Kraftpapier", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>z", "SSE.Views.Toolbar.capBtnAddComment": "Kommentar Hinzufügen", "SSE.Views.Toolbar.capBtnColorSchemas": "Farbschema", "SSE.Views.Toolbar.capBtnComment": "Kommentar", "SSE.Views.Toolbar.capBtnInsHeader": "Kopf- und Fußzeile", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientierung", "SSE.Views.Toolbar.capBtnPageSize": "Größe", "SSE.Views.Toolbar.capBtnPrintArea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnScale": "An Format anpassen", "SSE.Views.Toolbar.capImgAlign": "Ausrichtung", "SSE.Views.Toolbar.capImgBackward": "Eine Ebene nach hinten", "SSE.Views.Toolbar.capImgForward": "Eine Ebene nach vorne", "SSE.Views.Toolbar.capImgGroup": "Gruppe", "SSE.Views.Toolbar.capInsertChart": "Diagramm", "SSE.Views.Toolbar.capInsertEquation": "Gleichung", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "SSE.Views.Toolbar.capInsertImage": "Bild", "SSE.Views.Toolbar.capInsertShape": "Form", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.mniImageFromFile": "Bild aus Datei", "SSE.Views.Toolbar.mniImageFromStorage": "Bild aus dem Speicher", "SSE.Views.Toolbar.mniImageFromUrl": "Bild aus URL", "SSE.Views.Toolbar.textAddPrintArea": "Zur Druckauswahl hinzufügen", "SSE.Views.Toolbar.textAlignBottom": "Unten ausrichten", "SSE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignJust": "Blocksatz", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON> au<PERSON>", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON> au<PERSON>", "SSE.Views.Toolbar.textAlignRight": "Rechts ausrichten", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON> aus<PERSON>", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON> Rahmen<PERSON>en", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatisch", "SSE.Views.Toolbar.textBold": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "Unten: ", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON>e Ränder", "SSE.Views.Toolbar.textCenterBorders": "Innere vertikale <PERSON>en", "SSE.Views.Toolbar.textClearPrintArea": "Druckauswahl aufheben", "SSE.Views.Toolbar.textClearRule": "Regeln löschen", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON> d<PERSON>hen", "SSE.Views.Toolbar.textColorScales": "Farbskalen", "SSE.Views.Toolbar.textCounterCw": "Gegen den Uhrzeigersinn drehen", "SSE.Views.Toolbar.textCustom": "Einstellbar", "SSE.Views.Toolbar.textDataBars": "Datenbalken", "SSE.Views.Toolbar.textDelLeft": "Zellen nach links verschieben", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON> nach oben verschieben", "SSE.Views.Toolbar.textDiagDownBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON> diagonal nach unten", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON> diagonal nach oben", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "Sichtbaren Bereich bearbeiten", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFewPages": "Seiten", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHideVA": "Sichtbaren Bereich ausblenden", "SSE.Views.Toolbar.textHorizontal": "Horizontaler Text", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON> nach unten verschieben", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> innen", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON>n nach rechts verschieben", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Elemente", "SSE.Views.Toolbar.textLandscape": "Querformat", "SSE.Views.Toolbar.textLeft": "Links: ", "SSE.Views.Toolbar.textLeftBorders": "Ra<PERSON><PERSON><PERSON><PERSON> links", "SSE.Views.Toolbar.textManageRule": "Regeln verwalten", "SSE.Views.Toolbar.textManyPages": "Seiten", "SSE.Views.Toolbar.textMarginsLast": " Benutzerdefiniert als letzte", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Breit", "SSE.Views.Toolbar.textMiddleBorders": "Innere horizontale Rahmenlinien", "SSE.Views.Toolbar.textMoreFormats": "Weitere Formate", "SSE.Views.Toolbar.textMorePages": "Wei<PERSON>e Seiten", "SSE.Views.Toolbar.textNewColor": "Benutzerdefinierte Farbe", "SSE.Views.Toolbar.textNewRule": "Neue Regel", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textOnePage": "Seite", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> außen", "SSE.Views.Toolbar.textPageMarginsCustom": "Benutzerdefinierte Seitenränder", "SSE.Views.Toolbar.textPortrait": "Hochformat", "SSE.Views.Toolbar.textPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintGridlines": "Gitternetzlinien drucken", "SSE.Views.Toolbar.textPrintHeadings": "Überschriften drucken", "SSE.Views.Toolbar.textPrintOptions": "Druck-Einstellungen", "SSE.Views.Toolbar.textRight": "Rechts: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> rechts", "SSE.Views.Toolbar.textRotateDown": "Text nach unten drehen", "SSE.Views.Toolbar.textRotateUp": "Text nach oben drehen", "SSE.Views.Toolbar.textScale": "Maßstab", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSelection": "Aus der aktuellen Auswahl", "SSE.Views.Toolbar.textSetPrintArea": "Druckauswahl festlegen", "SSE.Views.Toolbar.textShowVA": "Sichtbaren Bereich anzeigen", "SSE.Views.Toolbar.textStrikeout": "Durchgestrichen", "SSE.Views.Toolbar.textSubscript": "Tiefgestellt", "SSE.Views.Toolbar.textSubSuperscript": "Tiefgestellt/hochgestellt", "SSE.Views.Toolbar.textSuperscript": "Hochgestellt", "SSE.Views.Toolbar.textTabCollaboration": "Zusammenarbeit", "SSE.Views.Toolbar.textTabData": "Daten", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Formel", "SSE.Views.Toolbar.textTabHome": "Startseite", "SSE.Views.Toolbar.textTabInsert": "Einfügen", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "Tabellenansicht", "SSE.Views.Toolbar.textThisPivot": "<PERSON><PERSON> <PERSON>er Pi<PERSON>-<PERSON><PERSON>e", "SSE.Views.Toolbar.textThisSheet": "Aus diesem Arbeitsblatt", "SSE.Views.Toolbar.textThisTable": "<PERSON><PERSON> dies<PERSON>", "SSE.Views.Toolbar.textTop": "Oben: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> oben", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textVertical": "Vertikaler Text", "SSE.Views.Toolbar.textWidth": "Breite", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Unten ausrichten", "SSE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignJust": "Blocksatz", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON> au<PERSON>", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON> au<PERSON>", "SSE.Views.Toolbar.tipAlignRight": "Rechts ausrichten", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON> aus<PERSON>", "SSE.Views.Toolbar.tipAutofilter": "Sortieren und Filtern", "SSE.Views.Toolbar.tipBack": "Zurück", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Zellenformatvorlage", "SSE.Views.Toolbar.tipChangeChart": "Diagrammtyp ändern", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Farbschema ändern", "SSE.Views.Toolbar.tipCondFormat": "Bedingte Formatierung", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "Format übertragen", "SSE.Views.Toolbar.tipCut": "Ausschneiden", "SSE.Views.Toolbar.tipDecDecimal": "Dezimalstelle löschen", "SSE.Views.Toolbar.tipDecFont": "Schriftart verkleinern", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleAccounting": "Buchhaltungsformat", "SSE.Views.Toolbar.tipDigStyleCurrency": "Währungsformat", "SSE.Views.Toolbar.tipDigStylePercent": "Prozentformat", "SSE.Views.Toolbar.tipEditChart": "Diagramm bearbeiten", "SSE.Views.Toolbar.tipEditChartData": "Daten auswählen", "SSE.Views.Toolbar.tipEditChartType": "Diagrammtyp ändern", "SSE.Views.Toolbar.tipEditHeader": "Kopf- oder Fußzeile bearbeiten", "SSE.Views.Toolbar.tipFontColor": "Schriftfarbe", "SSE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipFontSize": "Schriftgrad", "SSE.Views.Toolbar.tipHAlighOle": "Horizontale Ausrichtung", "SSE.Views.Toolbar.tipImgAlign": "Objekte ausrichten", "SSE.Views.Toolbar.tipImgGroup": "Objekte gruppieren", "SSE.Views.Toolbar.tipIncDecimal": "Dezimalstelle erhöhen", "SSE.Views.Toolbar.tipIncFont": "Schriftart vergrößern", "SSE.Views.Toolbar.tipInsertChart": "Diagramm einfügen", "SSE.Views.Toolbar.tipInsertChartSpark": "Diagramm einfügen", "SSE.Views.Toolbar.tipInsertEquation": "Formel einfügen", "SSE.Views.Toolbar.tipInsertHorizontalText": "Horizontales Textfeld einfügen", "SSE.Views.Toolbar.tipInsertHyperlink": "Hyperlink hinzufügen", "SSE.Views.Toolbar.tipInsertImage": "Bild einfügen", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON> e<PERSON>", "SSE.Views.Toolbar.tipInsertShape": "AutoForm einfügen", "SSE.Views.Toolbar.tipInsertSlicer": "Datenschnitt einfügen", "SSE.Views.Toolbar.tipInsertSmartArt": "SmartArt einfügen", "SSE.Views.Toolbar.tipInsertSpark": "Sparkline einfügen", "SSE.Views.Toolbar.tipInsertSymbol": "Symbol einfügen", "SSE.Views.Toolbar.tipInsertTable": "<PERSON>bell<PERSON> e<PERSON>fügen", "SSE.Views.Toolbar.tipInsertText": "Textfeld einfügen", "SSE.Views.Toolbar.tipInsertTextart": "TextArt einfügen", "SSE.Views.Toolbar.tipInsertVerticalText": "Vertikales Textfeld einfügen", "SSE.Views.Toolbar.tipMerge": "Verbinden und zentrieren", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "Zahlenformat", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageOrient": "Seitenausrichtung", "SSE.Views.Toolbar.tipPageSize": "Seitenformat", "SSE.Views.Toolbar.tipPaste": "Einfügen", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintArea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON>ucke titel", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "Speichern", "SSE.Views.Toolbar.tipSaveCoauth": "Speichern Sie die Änderungen, damit die anderen Benutzer sie sehen können.", "SSE.Views.Toolbar.tipScale": "An Format anpassen", "SSE.Views.Toolbar.tipSelectAll": "Alles auswählen", "SSE.Views.Toolbar.tipSendBackward": "Eine Ebene nach hinten", "SSE.Views.Toolbar.tipSendForward": "Eine Ebene nach vorne", "SSE.Views.Toolbar.tipSynchronize": "Das Dokument wurde von einem anderen Benutzer geändert. Bitte speichern Sie Ihre Änderungen und aktualisieren Sie Ihre Seite.", "SSE.Views.Toolbar.tipTextFormatting": "Mehr Formatierungstools", "SSE.Views.Toolbar.tipTextOrientation": "Orientierung", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "SSE.Views.Toolbar.tipVAlighOle": "Vertikal ausrichten", "SSE.Views.Toolbar.tipVisibleArea": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "Zeilenumbruch", "SSE.Views.Toolbar.txtAccounting": "Rechnungswesen", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "Aufsteigend", "SSE.Views.Toolbar.txtAutosumTip": "Summenbildung", "SSE.Views.Toolbar.txtCellStyle": "Zellenformatvorlage", "SSE.Views.Toolbar.txtClearAll": "Alle", "SSE.Views.Toolbar.txtClearComments": "Kommentare", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON> leeren", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Funktion", "SSE.Views.Toolbar.txtClearHyper": "Hyperlinks", "SSE.Views.Toolbar.txtClearText": "Text", "SSE.Views.Toolbar.txtCurrency": "Währung", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "Datum", "SSE.Views.Toolbar.txtDateTime": "Datum & Uhrzeit", "SSE.Views.Toolbar.txtDescending": "Absteigend", "SSE.Views.Toolbar.txtDollar": "$ Dollar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponentiell ", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Funktion einfügen", "SSE.Views.Toolbar.txtFraction": "Bru<PERSON>", "SSE.Views.Toolbar.txtFranc": "CHF Schweizer Franken", "SSE.Views.Toolbar.txtGeneral": "Allgemein", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Name-Manager", "SSE.Views.Toolbar.txtMergeAcross": "Alle Zellen in der Reihe verbinden", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON> verbinden", "SSE.Views.Toolbar.txtMergeCenter": "Verbinden und zentrieren", "SSE.Views.Toolbar.txtNamedRange": "Benannte Bereiche", "SSE.Views.Toolbar.txtNewRange": "<PERSON>n definieren", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "<PERSON>n ein<PERSON>ügen", "SSE.Views.Toolbar.txtPercentage": "Prozentsatz", "SSE.Views.Toolbar.txtPound": "£ Pfund", "SSE.Views.Toolbar.txtRouble": "₽ Rubel", "SSE.Views.Toolbar.txtScheme1": "Larissa", "SSE.Views.Toolbar.txtScheme10": "Median", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Lysithea", "SSE.Views.Toolbar.txtScheme14": "Nere<PERSON>", "SSE.Views.Toolbar.txtScheme15": "Okeanos", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Nyad", "SSE.Views.Toolbar.txtScheme18": "Ha<PERSON>era", "SSE.Views.Toolbar.txtScheme19": "Trek", "SSE.Views.Toolbar.txtScheme2": "Graustufe", "SSE.Views.Toolbar.txtScheme20": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme21": "Telesto", "SSE.Views.Toolbar.txtScheme22": "Neues Office", "SSE.Views.Toolbar.txtScheme3": "Apex", "SSE.Views.Toolbar.txtScheme4": "Aspekt ", "SSE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme6": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme7": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme8": "Fluss", "SSE.Views.Toolbar.txtScheme9": "Phoebe", "SSE.Views.Toolbar.txtScientific": "Wissenschaftlich", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Aufsteigend sortieren", "SSE.Views.Toolbar.txtSortZA": "Absteigend sortieren", "SSE.Views.Toolbar.txtSpecial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTableTemplate": "Wie Tabellenvorlage formatieren", "SSE.Views.Toolbar.txtText": "Text", "SSE.Views.Toolbar.txtTime": "Zeit", "SSE.Views.Toolbar.txtUnmerge": "Zellverbund aufheben", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "Anzeigen", "SSE.Views.Top10FilterDialog.txtBottom": "Unten", "SSE.Views.Top10FilterDialog.txtBy": "nach", "SSE.Views.Top10FilterDialog.txtItems": "Artikel", "SSE.Views.Top10FilterDialog.txtPercent": "Prozent", "SSE.Views.Top10FilterDialog.txtSum": "Summe", "SSE.Views.Top10FilterDialog.txtTitle": "Top-10-Auto<PERSON>ilter", "SSE.Views.Top10FilterDialog.txtTop": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 Filter", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Wertfeldeinstellungen", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "MITTELWERT", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 von %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Benutzerdefinierter Name", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "<PERSON><PERSON><PERSON><PERSON> zu", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "keine <PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "<PERSON><PERSON><PERSON> von", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Prozent der Spalte", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Prozent vom Ganzen", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "<PERSON><PERSON><PERSON> von <PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "<PERSON><PERSON><PERSON> Summe in", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Zeige Werte als", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Name der Quelle:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "STABW", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "STABW.N", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Summe", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON>rtfeld zusammenfassen nach", "SSE.Views.ValueFieldSettingsDialog.txtVar": "VARIANZ", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "VARIANZEN", "SSE.Views.ViewManagerDlg.closeButtonText": "Schließen", "SSE.Views.ViewManagerDlg.guestText": "Gas<PERSON>", "SSE.Views.ViewManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Löschen", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplizieren", "SSE.Views.ViewManagerDlg.textEmpty": "<PERSON><PERSON> An<PERSON>igen erstellt.", "SSE.Views.ViewManagerDlg.textGoTo": "Zum Anzeigen", "SSE.Views.ViewManagerDlg.textLongName": "Der Name einer Tabellenansicht darf maximal 128 <PERSON><PERSON>chen lang sein.", "SSE.Views.ViewManagerDlg.textNew": "<PERSON>eu", "SSE.Views.ViewManagerDlg.textRename": "Umbenennen", "SSE.Views.ViewManagerDlg.textRenameError": "Der Name von der Tabellenansicht kann nicht leer sein.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Ansicht umbenennen", "SSE.Views.ViewManagerDlg.textViews": "Tabellenansichten", "SSE.Views.ViewManagerDlg.tipIsLocked": "Das Element wird gerade von einem anderen Benutzer bearbeitet.", "SSE.Views.ViewManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>en-Manager", "SSE.Views.ViewManagerDlg.warnDeleteView": "Die aktuelle Tabellenansicht '%1' wird gelöscht.<br><PERSON><PERSON><PERSON><PERSON> Sie diese wirklich schließen und löschen?", "SSE.Views.ViewTab.capBtnFreeze": "Fensterausschnitt fixieren", "SSE.Views.ViewTab.capBtnSheetView": "Tabellenansicht", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Symbolleiste immer anzeigen", "SSE.Views.ViewTab.textClose": "Schließen", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Statusleiste verbergen", "SSE.Views.ViewTab.textCreate": "<PERSON>eu", "SSE.Views.ViewTab.textDefault": "Standard", "SSE.Views.ViewTab.textFormula": "Formelleiste", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON><PERSON> Spalte einfrieren", "SSE.Views.ViewTab.textFreezeRow": "Oberste Zeile einfrieren", "SSE.Views.ViewTab.textGridlines": "Gitternetzlinien ", "SSE.Views.ViewTab.textHeadings": "Überschriften", "SSE.Views.ViewTab.textInterfaceTheme": "Thema der Benutzeroberfläche", "SSE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "<PERSON><PERSON><PERSON><PERSON>-Manager", "SSE.Views.ViewTab.textRightMenu": "Rechtes Bedienungsfeld ", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Schatten für fixierte Bereiche anzeigen", "SSE.Views.ViewTab.textUnFreeze": "Fixierung aufheben", "SSE.Views.ViewTab.textZeros": "<PERSON><PERSON><PERSON> anzeigen", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Tabellenansicht schließen", "SSE.Views.ViewTab.tipCreate": "Tabellenansicht erstellen", "SSE.Views.ViewTab.tipFreeze": "Fensterausschnitt fixieren", "SSE.Views.ViewTab.tipInterfaceTheme": "Thema der Benutzeroberfläche", "SSE.Views.ViewTab.tipSheetView": "Tabellenansicht", "SSE.Views.WatchDialog.closeButtonText": "Schließen", "SSE.Views.WatchDialog.textAdd": "Überwachung hinzufügen", "SSE.Views.WatchDialog.textBook": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textCell": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textDelete": "Überwachung löschen", "SSE.Views.WatchDialog.textDeleteAll": "Alle löschen", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "Wert", "SSE.Views.WatchDialog.txtTitle": "Überwachungsfenster", "SSE.Views.WBProtection.hintAllowRanges": "Bearbeitung der Bereiche erlauben", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectWB": "Arb<PERSON><PERSON><PERSON> s<PERSON>tzen", "SSE.Views.WBProtection.txtAllowRanges": "Bearbeitung der Bereiche erlauben", "SSE.Views.WBProtection.txtHiddenFormula": "Ausgeblendete Formeln", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Gesperrte Form", "SSE.Views.WBProtection.txtLockedText": "Text sperren", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON><PERSON>", "SSE.Views.WBProtection.txtProtectWB": "Arb<PERSON><PERSON><PERSON> s<PERSON>tzen", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Zum Entschützen der Tabellenkalkulation bitte Kennwort eingeben", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Liste entschützen", "SSE.Views.WBProtection.txtWBUnlockDescription": "Zum Entschützen der Arbeitsmappe bitte Kennwort eingeben", "SSE.Views.WBProtection.txtWBUnlockTitle": "Arbeit<PERSON><PERSON>"}