﻿<!DOCTYPE html>
<html>
	<head>
		<title>Save/print/download your presentation</title>
		<meta charset="utf-8" />
		<meta name="description" content="Save, print and download your presentations in various formats" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
            <h1>Save/print/download your presentation</h1>
            <h3>Saving</h3>
            <p class="onlineDocumentFeatures">By default, the online <a target="_blank" href="https://www.onlyoffice.com/en/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> automatically saves your file every 2 seconds when you are working on it preventing your data loss if the program closes unexpectedly. If you co-edit the file in the <b>Fast</b> mode, the timer requests for updates 25 times a second and saves the changes if there are any. When the file is co-edited in the <b>Strict</b> mode, changes are automatically saved within 10-minute intervals. If you need, you can easily select the preferred co-editing mode or disable the <b>Autosave</b> feature on the <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Advanced Settings</a> page.</p>
            <p>To save your presentation manually in the current format and location,</p>
            <ul>
                <li>press the <b>Save</b> <div class = "icon icon-save"></div> icon on the left side of the editor header, or</li>
                <li>use the <b>Ctrl+S</b> key combination, or</li>
                <li>click the <b>File</b> tab of the top toolbar and select the <b>Save</b> option.</li>
            </ul>
            <p class="note desktopDocumentFeatures">In the <em>desktop version</em>, to prevent data loss if the program closes unexpectedly, you can turn on the <b>Autorecover</b> option on the <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Advanced Settings</a> page. </p>
            <div class="desktopDocumentFeatures">
                <p>In the <em>desktop version</em>, you can save the presentation under a different name, in a new location or format,</p>
                <ol>
                    <li>click the <b>File</b> tab of the top toolbar,</li>
                    <li>select the <b>Save as</b> option,</li>
                    <li>choose one of the available formats depending on your needs: PPTX, ODP, PDF, PDF/A, PNG, JPG. You can also choose the <b>Рresentation template</b> (POTX or OTP) option.</li>
                </ol>
            </div>
            <div class="onlineDocumentFeatures">
                <h3>Downloading</h3>
                <p>In the <em>online version</em>, you can download the resulting presentation onto the hard disk drive of your computer,</p>
                <ol>
                    <li>click the <b>File</b> tab of the top toolbar,</li>
                    <li>select the <b>Download as</b> option,</li>
                    <li>choose one of the available formats depending on your needs: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG.</li>
                </ol>
                <h3>Saving a copy</h3>
                <p>In the <em>online version</em>, you can save a copy of the file on your portal,</p>
                <ol>
                    <li>click the <b>File</b> tab of the top toolbar,</li>
                    <li>select the <b>Save Copy as</b> option,</li>
                    <li>choose one of the available formats depending on your needs: PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG.</li>
                    <li>select a location of the file on the portal and press <b>Save</b>.</li>
                </ol>
            </div>
            <h3 id="print">Printing</h3>
            <p>To print out the current presentation,</p>
            <ul>
                <li>click the <b>Print</b> <div class = "icon icon-print"></div> icon on the left side of the editor header, or</li>
                <li>use the <b>Ctrl+P</b> key combination, or</li>
                <li>click the <b>File</b> tab of the top toolbar and select the <b>Print</b> option.</li>
            </ul>
            <div class="note">
                The Firefox browser enables printing without downloading the document as a .pdf file first.
            </div>
            <p>It's also possible to print the selected slides using the <b>Print Selection</b> option from the contextual menu both in the <b>Edit</b> and <b>View</b> modes (<b>Right Mouse Button Click</b> on the selected slides and choose option <b>Print selection</b>).</p>
            <p><span class="desktopDocumentFeatures">In the <em>desktop version</em>, the file will be printed directly. </span><span class="onlineDocumentFeatures">In the <em>online version</em>, a PDF file based on your presentation will be generated. You can open and print it out, or save onto the hard disk drive of your computer or a removable medium to print it out later. Some browsers (e.g. Chrome and Opera) support direct printing.</span></p>
        </div>
	</body>
</html>