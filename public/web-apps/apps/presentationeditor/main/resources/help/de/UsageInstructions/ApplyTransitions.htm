﻿<!DOCTYPE html>
<html>
	<head>
		<title>Übergänge hinzufügen</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add animation effects between slides" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Übergänge hinzufügen</h1>
			<p>Ein <b>Übergang</b> ist ein Effekt, der angezeigt wird, wenn während der Präsentation eine Folie zur nächsten weitergeht. Im <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> können Sie auf alle Folien denselben Übergang oder auf jede einzelne Folie unterschiedliche Übergänge anwenden und die Übergangsparameter anpassen.</p>
			<p><b>Um einen Übergang auf eine einzelne Folie oder mehrere ausgewählte Folien anzuwenden</b>:</p>
			<ol>
				<li>Wechseln Sie zur Registerkarte <b>Übergänge</b> in der oberen Symbolleiste.</p>
					<p><img alt="Registerkarte Übergänge" src="../images/interface/transitionstab.png" /></p>
				</li>
				<li>Wählen Sie eine Folie (oder mehrere Folien in der Folienliste) aus, auf die Sie einen Übergang anwenden möchten.</li>
				<li>Wählen Sie einen der verfügbaren Übergangseffekte auf der Registerkarte <b>Übergänge</b> aus: <b>Kein(e)</b>, <b>Einblendung</b>, <b>Schieben</b>, <b>Wischblende</b>, <b>Aufteilen</b>, <b>Aufdecken</b>, <b>Bedecken</b>, <b>Uhr</b>, <b>Vergrößern</b>.</li>
				<li>Klicken Sie auf die Schaltfläche <b>Parameter</b>, um eine der verfügbaren Effektoptionen auszuwählen, die genau definieren, wie der Effekt angezeigt wird. Die verfügbaren Optionen für den <b>Vergrößern</b>-Effekt sind beispielsweise <b>Vergrößern</b>, <b>Verkleinern</b> und <b>Vergrößern und drehen</b>.</li>
				<li>Geben Sie die Dauer des gewünschten Übergangs an. Wählen Sie im Feld <b>Dauer</b> die gewünschte Dauer aus oder geben Sie einen Wert in das dafür vorgesehene Feld ein (die Dauer wird in Sekunden gemessen).</li>
				<li>Klicken Sie auf die Schaltfläche <b>Vorschau</b>, um den ausgewählten Übergang im Folienbearbeitungsbereich abzuspielen.</li>
				<li>Geben Sie an, wie lange die Folie angezeigt werden soll, bis der Übergang in die nächste Folie erfolgt:
				<ul>
					<li><b>Bei Klicken beginnen</b> – aktivieren Sie dieses Kontrollkästchen, wenn Sie keine Beschränkung für die Anzeigedauer einer Folie wünschen. Der Wechseln in eine neue Folie erfolgt erst bei Mausklick</li>
					<li><b>Verzögern</b> – nutzen Sie diese Option, wenn die gewählte Folie für eine bestimmte Zeit angezeigt werden soll, bis der Übergang in eine andere Folie erfolgt. Aktivieren Sie dieses Kontrollkästchen und wählen Sie die Dauer in Sekunden aus oder geben Sie den Wert in das dafür vorgesehene Kästchen ein.
					<p class="note">Wenn Sie nur das Kontrollkästchen <b>Verzögern</b> aktivieren, erfolgt der Übergang automatisch, in einem festgelegten Zeitintervall. Wenn Sie die Kontrollkästchen <b>Bei Klicken beginnen</b> und <b>Verzögern</b> gleichzeitig aktivieren und den Wert für die Dauer der Verzögerung festlegen, erfolgt der Übergang ebenfalls automatisch, sie haben zusätzlich jedoch auch die Möglichkeit, ein Slide mit der Maus anzuklicken, um den Übergang in die nächste Folie einzuleiten.</p></li>
				</ul>
				</li>
			</ol>
			<p><b>Um einen Übergang auf alle Folien</b> in Ihrer Präsentation anzuwenden, klicken Sie auf der Registerkarte <b>Übergänge</b> auf die Schaltfläche <b>Auf alle Folien anwenden</b>.</p>
			<p><b>Um einen Übergang zu löschen</b>, wählen Sie die erforderliche Folie aus und wählen Sie <b>Kein(e)</b> unter den Übergangseffektoptionen auf der Registerkarte <b>Übergänge</b>.</p>
			<p><b>Um alle Übergänge zu löschen</b>, wählen Sie eine beliebige Folie aus, wählen Sie <b>Kein(e)</b> unter den Übergangseffektoptionen und klicken Sie auf die Schaltfläche <b>Auf alle Folien anwenden</b> auf der Registerkarte <b>Übergänge</b>.</p>
		</div>
	</body>
</html>
