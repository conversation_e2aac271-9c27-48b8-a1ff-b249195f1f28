﻿<!DOCTYPE html>
<html>
	<head>
		<title>Paramètres d'affichage et outils de navigation</title>
		<meta charset="utf-8" />
		<meta name="description" content="Description des paramètres d'affichage et outils de navigation en tant que zoom, les boutons diapositive précédente/suivante" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Paramètres d'affichage et outils de navigation</h1>
			<p><a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> est doté de plusieurs outils qui vous aide à afficher et naviguer à travers votre présentation: le zoom, les boutons de diapositive précédente/suivante et l'indicateur du numéro de diapositive etc.</p>
			<h3>Régler les paramètres d'affichage</h3>
			<p>
				Pour régler les paramètres d'affichage par défaut et définir le mode le plus pratique pour travailler avec la présentation, passez à l'onglet <b><a href="../ProgramInterface/ViewTab.htm" onclick="onhyperlinkclick(this)">Affichage</a></b> . Les options disponibles sont les suivantes:
			</p>
			<ul>
				<li><b>Zoom</b> - sert à définir la valeur de zoom de 50% à 200% en sélectionnant de la liste des options disponibles.</li>
				<li><b>Ajuster à la diapositive</b> sert à adapter la diapositive entière à la partie visible de la zone de travail.</li>
				<li><b>Ajuster à la largeur</b> sert à adapter la largeur de la diapositive à la partie visible de la zone de travail.</li>
				<li><b>Thème d'interface</b> - sélectionnez l'une des thèmes d'interface disponibles dans la liste déroulante: <em>Identique à système</em>, <em>Claire</em>, <em>Classique claire</em>, <em>Sombre</em>, <em>Contraste élevé sombre</em>.</li>
				<li><b>Notes</b> - une fois désactivé, les notes au-dessous de la diapositive sont masquées. Il est également possible de masquer/afficher cette section en la faisant glisser avec la souris.</li>
				<li><b>Règles</b> une fois désactivé, masque les règles qui sont utilisées pour définir des tabulations et des retraits de paragraphe dans une zone de texte. Pour afficher les <b>Règles</b> masquées, cliquez sur cette option encore une fois.</li>
				<li><b>Repères</b> - sélectionnez le type de repères approprié pour positionner correctement des objets sur une diapositive. Les options disponibles pour un positionnement amélioré <em>vertical</em>, <em>horizontal</em> et <em>repères actifs</em>.</li>
				<li><b>Quadrillage</b> - sélectionnez la taille de la grille appropriée parmi les modèles disponibles ou indiquez la taille personnalisée et activez ou désactivez les options d'aligner sur la grille pour améliorer le positionnement des objets.</li>
				<li>
					<b>Toujours afficher la barre d'outils</b> - une fois désactivé, la barre d'outils supérieure comportant toutes les commandes sont masquée mais tous les onglets restent visibles.
					<p class="note">Vous pouvez également double-cliquer sur un onglet pour masquer la barre d'outils supérieure ou l'afficher à nouveau.</p>
				</li>
				<li><b>Barre d'état</b> - une fois désactivé, sert à masquer la barre qui se situe tout en bas avec les boutons <b>Affichage des numéros de diapositives</b> et <b>Zoom</b>. Pour afficher la <b>Barre d'état</b> masquée cliquez sur cette option encore une fois.</li>
				<li><b>Panneau gauche</b> - une fois désactivé, le panneau gauche comportant les onglets <b>Rechercher</b>, <b>Diapositives</b>, <b>Commentaires</b> sera masqué. Pour afficher le panneau gauche, cochez cette case.</li>
				<li><b>Panneau droit</b> - une fois désactivé, le panneau droit comportant les options de configurations des <b>Paramètres</b> sera masqué. Pour afficher le panneau droit, cochez cette case.</li>
			</ul>
			<p>La barre latérale sur la droite est réduite par défaut. Pour l'agrandir, sélectionnez un objet/diapositive et cliquez sur l'icône de l'onglet actuellement activé sur la droite. Pour réduire la barre latérale sur la droite, cliquez à nouveau sur l'icône. La largeur de la barre latérale gauche est ajustée par simple glisser-déposer: déplacez le curseur de la souris sur la bordure gauche pour qu'elle se transforme en flèche bidirectionnelle et déplacez la bordure vers la gauche pour réduire la largeur de la barre latérale ou vers la droite pour l'agrandir.</p>
			<h3>Utiliser les outils de navigation</h3>
			<p>Pour naviguer à travers votre présentation, utilisez les outils suivants:</p>
			<p>
				Les boutons <b>Zoom</b> sont situés en bas à droite et sont utilisés pour faire un zoom avant et arrière dans la présentation active. Pour modifier la valeur de zoom sélectionnée en pourcentage, cliquez dessus et sélectionnez l'une des options de zoom disponibles dans la liste (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) ou utilisez les boutons <b>Zoom avant</b> <span class="icon icon-zoomin"></span> ou <b>Zoom arrière</b> <span class="icon icon-zoomout"></span>. Cliquez sur l'icône <b>Ajuster à la largeur</b> <span class="icon icon-fitwidth"></span>  pour adapter la largeur de la diapositive à la partie visible de la zone de travail. Pour adapter la diapositive entière à la partie visible de la zone de travail, cliquez sur l'icône <b>Ajuster à la diapositive</b> <span class="icon icon-fitslide"></span>.  Les options de zoom sont également disponibles sous l'onglet <a href="../ProgramInterface/ViewTab.htm" onclick="onhyperlinkclick(this)">Affichage</a>.
			</p>
			<p class="note">Vous pouvez définir une valeur de zoom par défaut. Basculez vers l'onglet <b>Fichier</b> de la barre d'outils supérieure, allez à la section <b>Paramètres avancés...</b>, choisissez la <b>Valeur de zoom par défaut</b> nécessaire dans la liste et cliquez sur le bouton <b>Appliquer</b>.</p>
			<p>Pour accéder à la diapositive précédente ou suivante lors de la modification de la présentation, vous pouvez utiliser les boutons <span class="icon icon-previouspage"></span> et <span class="icon icon-nextpage"></span> en haut et en bas de la barre de défilement verticale sur le côté droit de la diapositive.</p>
			<p><b>Indicateur du numéro de diapositive</b> affiche la diapositive actuelle en tant que partie de toute la présentation actuelle (diapositive 'n' de 'nn'). Cliquez sur la légende pour ouvrir la fenêtre dans laquelle vous pouvez entrer le numéro de la diapositive et y accéder rapidement. Si la <b>Barre d'état</b> est masquée, cet outil reste inaccessible.</p>
			
			
		</div>
	</body>
</html>