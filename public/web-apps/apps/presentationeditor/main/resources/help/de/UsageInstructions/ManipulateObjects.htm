﻿<!DOCTYPE html>
<html>
	<head>
		<title>Objekte formatieren</title>
		<meta charset="utf-8" />
		<meta name="description" content="Move, rotate, resize and reshape autoshapes, images, charts." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Objekte formatieren</h1>
			<p>Im <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> sie können die verschiedene Objekte auf einer Folie mithilfe der speziellen Ziehpunkte manuell verschieben, drehen und ihre Größe ändern. Alternativ können Sie über die rechte Seitenleiste oder das Fenster <b>Erweiterte Einstellungen</b> genaue Werte für die Abmessungen und die Position von Objekten festlegen.</p>
			<p class="note">
				<a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">Hier</a> finden Sie eine Übersicht über die gängigen Tastenkombinationen für die Arbeit mit Objekten.
			</p>
			<h3>Größe von Objekten ändern</h3>
			<p>Um die Größe von <b>AutoFormen, Bildern, Diagrammen, Tabellen oder Textboxen</b> zu ändern, ziehen Sie mit der Maus an den kleinen Quadraten <span class="icon icon-resize_square"></span> an den Rändern des entsprechenden Objekts. Um das ursprünglichen Seitenverhältnis der ausgewählten Objekte während der Größenänderung beizubehalten, halten Sie Taste <b>UMSCHALT</b> gedrückt und ziehen Sie an einem der Ecksymbole.</p>
			<p><span class="big big-maintain_proportions"></span></p>
			<p>Um die präzise Breite und Höhe eines <b>Diagramms</b> festzulegen, wählen Sie es auf der Folie aus und navigieren Sie über den Bereich <b>Größe</b>, der in der rechten Seitenleiste aktiviert wird.</p>
			<p>Um die präzise Abmessungen eines <b>Bildes</b> oder einer <b>AutoForm</b> festzulegen, klicken Sie mit der rechten Maustaste auf das gewünschte Objekt und wählen Sie die Option <b>Bild/AutoForm - Erweiterte Einstellungen</b> aus dem Menü aus. Legen Sie benötigte Werte in die Registerkarte <b>Größe</b> im Fenster <b>Erweiterte Einstellungen</b> fest und drücken Sie auf <b>OK</b>.</p>
			<h3>Die Form einer AutoForm ändern</h3>
			<p>Bei der Änderung einiger Formen, z.B. geformte Pfeile oder Legenden, ist auch dieses gelbe diamantförmige Symbol <span class="icon icon-yellowdiamond"></span> verfügbar. Über dieses Symbol können verschiedene Komponenten einer Form geändert werden, z.B. die Länge des Pfeilkopfes.</p>
			<p><span class="big big-reshaping"></span></p>
			<p>Um eine AutoForm umzuformen, können Sie auch die Option <b>Punkte bearbeiten</b> aus dem <b>Kontextmenü</b> verwenden.</p>
			<p>Die Option <b>Punkte bearbeiten</b> wird verwendet, um die Krümmung Ihrer Form anzupassen oder zu ändern.</p>
			<ol>
				<li>
					Um die bearbeitbaren Ankerpunkte einer Form zu aktivieren, klicken Sie mit der rechten Maustaste auf die Form und wählen Sie im Menü die Option <b>Punkte bearbeiten</b> aus. Die schwarzen Quadrate, die aktiv werden, sind die Punkte, an denen sich zwei Linien treffen, und die rote Linie umreißt die Form. Klicken Sie darauf und ziehen Sie, um den Punkt neu zu positionieren und den Umriss der Form zu ändern.
					<p><img alt="Punkte bearbeiten Menü" src="../images/editpoints_rightclick.png" /></p>
				</li>
				<li>
					Sobald Sie auf den Ankerpunkt klicken, werden zwei blaue Linien mit weißen Quadraten an den Enden angezeigt. Dies sind Bezier-Ziehpunkte, mit denen Sie eine Kurve erstellen und die Glätte einer Kurve ändern können.
					<p><span class="big big-editpoints_example"></span></p>
				</li>
				<li>
					Solange die Ankerpunkte aktiv sind, können Sie sie hinzufügen und löschen:
					<ul>
						<li><b>Um einer Form einen Punkt hinzuzufügen</b>, halten Sie die <b>Strg</b>-Taste gedrückt und klicken Sie auf die Position, an der Sie einen Ankerpunkt hinzufügen möchten.</li>
						<li><b>Um einen Punkt zu löschen</b>, halten Sie die <b>Strg</b>-Taste gedrückt und klicken Sie auf den unnötigen Punkt.</li>
					</ul>
				</li>
			</ol>
			<h3>Objekte verschieben</h3>
			<p>
				Um die Position von <b>AutoFormen, Bildern, Diagrammen, Tabellen und Textfeldern</b> zu ändern, nutzen Sie das Symbol <span class="icon icon-arrow"></span>, das eingeblendet wird, wenn Sie den Mauszeiger über die AutoForm bewegen. Ziehen Sie das Objekt in die gewünschten Position, ohne die Maustaste loszulassen. 
				Um ein Objekt in 1-Pixel-Stufen zu verschieben, halten Sie die Taste <b>STRG</b> gedrückt und verwenden Sie die Pfeile auf der Tastatur. 
				Um ein Objekt strikt horizontal/vertikal zu bewegen und zu verhindern, dass es sich perpendikular bewegt, halten Sie die <b>UMSCHALT</b>-Taste beim Ziehen gedrückt.
			</p>
			<p>Um die exakte Position eines <b>Bildes</b> festzulegen, klicken Sie mit der rechten Maustaste auf das Bild und wählen Sie die Option <b>Bild - Erweiterte Einstellungen</b> aus dem Menü aus. Legen Sie gewünschten Werte im Bereich <b>Position</b> im Fenster <b>Erweiterte Einstellungen</b> fest und drücken Sie auf <b>OK</b>.</p>
			<h3>Objekte drehen</h3>
			<p>Um <b>AutoFormen, Bilder und Textfelder</b> manuell zu drehen, positionieren Sie den Cursor auf dem Drehpunkt <span class="icon icon-greencircle"></span> und ziehen Sie das Objekt im Uhrzeigersinn oder gegen Uhrzeigersinn in die gewünschte Position. Um ein Objekt in 15-Grad-Stufen zu drehen, halten Sie die <b>UMSCHALT</b>-Taste bei der Drehung gedrückt.</p>
			<p>Sobald Sie das gewünschte Objekt ausgewählt haben, wird der Abschnitt <b>Drehen</b> in der rechten Seitenleiste aktiviert. Hier haben Sie die Möglichkeit das Objekt um 90 Grad im/gegen den Uhrzeigersinn zu drehen oder das Objekt horizontal/vertikal zu drehen. Um die Funktion zu öffnen, klicken Sie rechts auf das Symbol <b>Formeinstellungen</b> <span class="icon icon-shape_settings_icon"></span> oder das Symbol <b>Bildeinstellungen</b> <span class="icon icon-image_settings_icon"></span>. Wählen Sie eine der folgenden Optionen:</p>
			<ul>
				<li><div class = "icon icon-rotatecounterclockwise"></div> - die Form um 90 Grad gegen den Uhrzeigersinn drehen</li>
				<li><div class = "icon icon-rotateclockwise"></div> - die Form um 90 Grad im Uhrzeigersinn drehen</li>
				<li><div class = "icon icon-fliplefttoright"></div> - die Form horizontal spiegeln (von links nach rechts)</li>
				<li><div class = "icon icon-flipupsidedown"></div> - die Form vertikal spiegeln (von oben nach unten)</li>
			</ul>
			<p>Alternativ können Sie mit der rechten Maustaste auf das ausgewählte Objekte klicken, wählen Sie anschließend im Kontextmenü die Option <b>Drehen</b> aus und nutzen Sie dann eine der verfügbaren Optionen zum Drehen.</p>
			<p>Um den Text in einem genau festgelegten Winkel zu drehen, klicken Sie auf das Symbol <b>Erweiterte Einstellungen anzeigen</b> in der rechten Seitenleiste und wählen Sie dann die Option <b>Drehen</b> im Fenster <b>Erweiterte Einstellungen</b>. Geben Sie den erforderlichen Wert in Grad in das Feld <b>Winkel</b> ein und klicken Sie dann auf <b>OK</b>.</p>

		</div>
	</body>
</html>