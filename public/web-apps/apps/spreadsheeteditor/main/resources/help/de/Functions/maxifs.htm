﻿<!DOCTYPE html>
<html>
	<head>
		<title>MAXWENNS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>MAXWENNS-Funktion</h1>
			<p>Die Funktion <b>MAXWENNS</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den Maximalwert aus Zellen zurück, die mit einem bestimmten Satz Bedingungen oder Kriterien angegeben wurden.</p>
			<p>Die Formelsyntax der Funktion <b>MAXWENNS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>MAXWENNS(Max_Bereich; Kriterienbereich1; Kriterien1; [Kriterienbereich2; Kriterien2]; ...)</em></b></p> 
            <p style="text-indent: 50px;"><b><em>Max_Bereich</em></b> ist tatsächliche Zellenbereich, in dem das Maximum ermittelt wird.</p>
            <p style="text-indent: 50px;"><b><em>Kriterienbereich1</em></b> ist der erste gewählte Zellenbereich, der auf <em>Kriterien1</em> getestet wird.</p>
            <p style="text-indent: 50px;"><b><em>Kriterien1</em></b> ist die erste zu erfüllende Bedingung. Sie wird auf <em>Kriterienbereich1</em> angewendet und bestimmt, welche Zellen im <em>Max-Bereich</em> als Maximum ausgewertet werden. Der Wert kann manuell eingegeben werden oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p style="text-indent: 50px;"><b><em>Kriterienbereich2; Kriterien2, ...</em></b> sind zusätzliche Zellenbereiche und ihre jeweiligen Kriterien. Diese Argumente sind optional.</p>
            <p class="note"><b>Hinweis:</b> Sie können Platzhalterzeichen verwenden, wenn Sie Kriterien angeben. Das Fragezeichen &quot;?&quot; kann ein beliebiges einzelnes Zeichen ersetzen und der Stern &quot;*&quot; kann anstelle einer beliebigen Anzahl von Zeichen verwendet werden.</p>
			<p>Anwendung der Funktion <b>MAXWENNS</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>MAXWENNS</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="MAXWENNS-Funktion" src="../images/maxifs.png" /></p>
		</div>
	</body>
</html>