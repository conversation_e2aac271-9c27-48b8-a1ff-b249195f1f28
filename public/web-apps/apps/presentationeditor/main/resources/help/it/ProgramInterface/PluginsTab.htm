﻿<!DOCTYPE html>
<html>
	<head>
		<title>Plugins tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - Plugins tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Plugins tab</h1>
            <p>The <b>Plugins</b> tab allows to access advanced editing features using available third-party components. Here you can also use macros to simplify routine operations.</p>
            <div class="onlineDocumentFeatures">
                <p>Online <a href="https://www.onlyoffice.com/it/presentation-editor.aspx">Presentation Editor</a> window:</p>
                <p><img alt="Plugins tab" src="../images/interface/pluginstab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Desktop Presentation Editor window:</p>
                <p><img alt="Plugins tab" src="../images/interface/desktop_pluginstab.png" /></p>
            </div>
            <p class="desktopDocumentFeatures">The <b>Settings</b> button allows to open the window where you can view and manage all installed plugins and add your own ones.</p>
            <p>The <b>Macros</b> button allows to open the window where you can create your own macros and run them. To learn more about macros you can refer to our <a target="_blank" href="https://api.onlyoffice.com/plugin/macros" onclick="onhyperlinkclick(this)">API Documentation</a>.</p>
            <p>Currently, the following plugins are available:</p>		
            <ul>
                <li class="desktopDocumentFeatures"><b>Send</b> allows to send the presentation via email using the default desktop mail client (available in the <em>desktop version</em> only),</li>
                <li class="desktopDocumentFeatures"><b>Audio</b> allows to insert audio records stored on the hard disk drive into your presentation (available in the <em>desktop version</em> only, not available for Mac OS),</li>
                <li class="desktopDocumentFeatures"><b>Video</b> allows to insert video records stored on the hard disk drive into your presentation (available in the <em>desktop version</em> only, not available for Mac OS),
                    <p class="note">
                        <b>Note</b>: to be able to playback video, you'll need to install codecs, for example, <b>K-Lite</b>.
                    </p>
                </li>
                <li><b>Highlight code</b> allows to highlight syntax of the code selecting the necessary language, style, background color,</li>
                <li><b>PhotoEditor</b> allows to edit images: crop, flip, rotate them, draw lines and shapes, add icons and text, load a mask and apply filters such as Grayscale, Invert, Sepia, Blur, Sharpen, Emboss, etc.,</li>
                <li><b>Thesaurus</b> allows to search for synonyms and antonyms of a word and replace it with the selected one,</li>
                <li><b>Translator</b> allows to translate the selected text into other languages,
                    <p class="note"><b>Note</b>: this plugin doesn't work in Internet Explorer.</p>
                </li>
                <li><b>YouTube</b> allows to embed YouTube videos into your presentation.</li>
            </ul>
            <p>To learn more about plugins please refer to our <a target="_blank" href="https://api.onlyoffice.com/plugin/basic" onclick="onhyperlinkclick(this)">API Documentation</a>. All the currently existing open source plugin examples are available on <a target="_blank" href="https://github.com/ONLYOFFICE/sdkjs-plugins" onclick="onhyperlinkclick(this)">GitHub</a>.</p>
		</div>
	</body>
</html>