{"Common.Controllers.Chat.notcriticalErrorTitle": "Προειδοποίηση", "Common.Controllers.Chat.textEnterMessage": "Εισάγετε το μήνυμά σας εδώ", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Ανώνυμος", "Common.Controllers.ExternalDiagramEditor.textClose": "Κλείσιμο", "Common.Controllers.ExternalDiagramEditor.warningText": "Το αντικείμενο είναι απενεργοποιημένο επειδή χρησιμοποιείται από άλλο χρήστη.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Προειδοποίηση", "Common.define.chartData.textArea": "Περιοχή", "Common.define.chartData.textAreaStacked": "Σωρευμένη περιοχή", "Common.define.chartData.textAreaStackedPer": "100% Σωρευμένη περιοχή", "Common.define.chartData.textBar": "Μπάρα", "Common.define.chartData.textBarNormal": "Ομαδοποιημένη στήλη", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Ομαδοποιημένη στήλη", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON> στήλη", "Common.define.chartData.textBarStacked": "Σωρευμένη στήλη", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Σωρευμένη στήλη", "Common.define.chartData.textBarStackedPer": "100% Σωρευμένη στήλη", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Σωρευμένη στήλη", "Common.define.chartData.textCharts": "Γραφήματα", "Common.define.chartData.textColumn": "Στήλη", "Common.define.chartData.textCombo": "Συνδυασμ<PERSON>ς", "Common.define.chartData.textComboAreaBar": "Σωρευμένη στήλη ομαδοποιημένη ανά περιοχή", "Common.define.chartData.textComboBarLine": "Ομαδοποιημένη γραμμή - στήλης", "Common.define.chartData.textComboBarLineSecondary": "Ομαδοποιημένη γραμμή - στήλης σε δευτερεύοντα άξονα", "Common.define.chartData.textComboCustom": "Προσαρμοσμένος συνδυασμός", "Common.define.chartData.textDoughnut": "Ντ<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Ομαδοποιημένη μπάρα", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> Ομαδοποιημένη μπάρα", "Common.define.chartData.textHBarStacked": "Σωρευμένη μπάρα", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Σωρευμένη μπάρα", "Common.define.chartData.textHBarStackedPer": "100% Σωρευμένη μπάρα", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Σωρευμένη μπάρα", "Common.define.chartData.textLine": "Γραμμή", "Common.define.chartData.textLine3d": "3-D γραμμή", "Common.define.chartData.textLineMarker": "Γραμμή με δείκτες", "Common.define.chartData.textLineStacked": "Σωρευμένη γραμμή", "Common.define.chartData.textLineStackedMarker": "Σωρευμένη γραμμή με δείκτες", "Common.define.chartData.textLineStackedPer": "100% Σωρευμένη γραμμή", "Common.define.chartData.textLineStackedPerMarker": "100% Σωρευμένη γραμμή με δείκτες", "Common.define.chartData.textPie": "Πίτα", "Common.define.chartData.textPie3d": "3-<PERSON> πίτα", "Common.define.chartData.textPoint": "ΧΥ (Διασπορά)", "Common.define.chartData.textScatter": "Διασπορά", "Common.define.chartData.textScatterLine": "Διασπορά με ευθείες γραμμές", "Common.define.chartData.textScatterLineMarker": "Διασπορά με ευθείες γραμμές και δείκτες", "Common.define.chartData.textScatterSmooth": "Διασπορά με ομαλές γραμμές", "Common.define.chartData.textScatterSmoothMarker": "Διασπορά με ομαλές γραμμές και δείκτες", "Common.define.chartData.textStock": "Μετοχή", "Common.define.chartData.textSurface": "Επιφάνεια", "Common.define.effectData.textAcross": "Απέναντι", "Common.define.effectData.textAppear": "Εμφάνιση", "Common.define.effectData.textArcDown": "Τόξο <PERSON>ω", "Common.define.effectData.textArcLeft": "Τόξο Αριστερά", "Common.define.effectData.textArcRight": "Τόξο Δεξιά", "Common.define.effectData.textArcs": "Κυκλικά τόξα", "Common.define.effectData.textArcUp": "Τόξο <PERSON>", "Common.define.effectData.textBasic": "Βασικό", "Common.define.effectData.textBasicSwivel": "Βασική Συστροφή", "Common.define.effectData.textBasicZoom": "Βασική Εστίαση", "Common.define.effectData.textBean": "Κόκκος", "Common.define.effectData.textBlinds": "Περσίδες", "Common.define.effectData.textBlink": "Βλεφάρισμα", "Common.define.effectData.textBoldFlash": "Έντονη Λάμψη", "Common.define.effectData.textBoldReveal": "Έντονη Απκάλυψη", "Common.define.effectData.textBoomerang": "Μπούμερανγκ", "Common.define.effectData.textBounce": "Αναπήδηση", "Common.define.effectData.textBounceLeft": "Αναπήδηση Αριστερά", "Common.define.effectData.textBounceRight": "Αναπήδηση Δεξιά", "Common.define.effectData.textBox": "Κουτί", "Common.define.effectData.textBrushColor": "Χρώμα Βούρτσας", "Common.define.effectData.textCenterRevolve": "Κεντρική Περιστροφή", "Common.define.effectData.textCheckerboard": "Σκακιέρα", "Common.define.effectData.textCircle": "<PERSON><PERSON><PERSON><PERSON>ος", "Common.define.effectData.textCollapse": "Κλείσιμο", "Common.define.effectData.textColorPulse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textComplementaryColor": "Συμπληρωματικό Χρώμα", "Common.define.effectData.textComplementaryColor2": "Συμπληρωματικό Χρώμα 2", "Common.define.effectData.textCompress": "Συμπίεση", "Common.define.effectData.textContrast": "Αντίθεση", "Common.define.effectData.textContrastingColor": "Χρώμα Αντίθεσης", "Common.define.effectData.textCredits": "Μνεία", "Common.define.effectData.textCrescentMoon": "Ημισέληνος", "Common.define.effectData.textCurveDown": "Καμπύλη Κάτω", "Common.define.effectData.textCurvedSquare": "Καμπυλωτό Τετράγωνο", "Common.define.effectData.textCurvedX": "Καμπυλωτό Χ", "Common.define.effectData.textCurvyLeft": "Καμπύλη Αριστερά", "Common.define.effectData.textCurvyRight": "Καμπύλη Δεξιά", "Common.define.effectData.textCurvyStar": "Καμπυλωτό Αστέρι", "Common.define.effectData.textCustomPath": "Προσαρμοσμέν<PERSON>ονοπάτι", "Common.define.effectData.textCuverUp": "Καμπύλη Πάνω", "Common.define.effectData.textDarken": "Σκίαση", "Common.define.effectData.textDecayingWave": "Υποχωρούμενο Κύμα", "Common.define.effectData.textDesaturate": "Αποκορεσμός", "Common.define.effectData.textDiagonalDownRight": "Δ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Δεξι<PERSON>", "Common.define.effectData.textDiagonalUpRight": "Δ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Δεξι<PERSON>", "Common.define.effectData.textDiamond": "Ρόμβος", "Common.define.effectData.textDisappear": "Εξαφάνιση", "Common.define.effectData.textDissolveIn": "Διάλυση Μέσα", "Common.define.effectData.textDissolveOut": "Διάλυση Έξω", "Common.define.effectData.textDown": "Κάτω", "Common.define.effectData.textDrop": "Πτώση", "Common.define.effectData.textEmphasis": "Εφέ Έμφασης", "Common.define.effectData.textEntrance": "Εφέ Εισόδου", "Common.define.effectData.textEqualTriangle": "Ισόπλευρο Τρίγωνο", "Common.define.effectData.textExciting": "Συν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ι<PERSON><PERSON>ς", "Common.define.effectData.textExit": "Εφέ <PERSON>δου", "Common.define.effectData.textExpand": "Επέκταση", "Common.define.effectData.textFade": "Ξεθώριασμα", "Common.define.effectData.textFigureFour": "Διάγραμμα 8 Τέσσερα", "Common.define.effectData.textFillColor": "Χρώμ<PERSON> Γεμίσματος", "Common.define.effectData.textFlip": "Αναστροφή", "Common.define.effectData.textFloat": "Επίπλευση", "Common.define.effectData.textFloatDown": "Επίπλευση Κάτω", "Common.define.effectData.textFloatIn": "Επίπλευση Μέσα", "Common.define.effectData.textFloatOut": "Επίπλευση Έξω", "Common.define.effectData.textFloatUp": "Επίπλευση Πάνω", "Common.define.effectData.textFlyIn": "Πέταγμα Μέσα", "Common.define.effectData.textFlyOut": "Πέταγμα Έξω", "Common.define.effectData.textFontColor": "Χρώμα Γραμματοσειράς", "Common.define.effectData.textFootball": "Μπάλ<PERSON>οδοσφαίρου", "Common.define.effectData.textFromBottom": "Από το Κάτω <PERSON>έρος", "Common.define.effectData.textFromBottomLeft": "Από το Κάτω Μ<PERSON><PERSON>ος Αριστερά", "Common.define.effectData.textFromBottomRight": "Από το Κάτω Μέρος <PERSON>", "Common.define.effectData.textFromLeft": "Από Αριστερά", "Common.define.effectData.textFromRight": "Από Δεξιά", "Common.define.effectData.textFromTop": "Από την Κορυφή", "Common.define.effectData.textFromTopLeft": "Από την Κορυφή Αριστερά", "Common.define.effectData.textFromTopRight": "Από την Κορυφή Δεξιά", "Common.define.effectData.textFunnel": "Χωνί", "Common.define.effectData.textGrowShrink": "Μεγέθυνση/Σμίκρυνση", "Common.define.effectData.textGrowTurn": "Μεγέθυνση & Στροφή", "Common.define.effectData.textGrowWithColor": "Μεγέθυνση Με Χρώμα", "Common.define.effectData.textHeart": "Καρδιά", "Common.define.effectData.textHeartbeat": "Σ<PERSON>υγ<PERSON><PERSON>ς", "Common.define.effectData.textHexagon": "Εξάγωνο", "Common.define.effectData.textHorizontal": "Οριζόντιος", "Common.define.effectData.textHorizontalFigure": "Οριζόντιο Διάγραμμα 8", "Common.define.effectData.textHorizontalIn": "Οριζόντια Μέσα", "Common.define.effectData.textHorizontalOut": "Οριζόντια Έξω", "Common.define.effectData.textIn": "Μέσα", "Common.define.effectData.textInFromScreenCenter": "Μέσα Από το Κέντρο της Οθόνης", "Common.define.effectData.textInSlightly": "Ε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textInToScreenBottom": "Μέσ<PERSON> Στο <PERSON>τω Μέρος Της <PERSON>ης", "Common.define.effectData.textInvertedSquare": "Ανεστραμμένο Τετράγωνο", "Common.define.effectData.textInvertedTriangle": "Ανεστραμμένο Τρίγωνο", "Common.define.effectData.textLeft": "Αριστερά", "Common.define.effectData.textLeftDown": "Αριστερ<PERSON> Κάτω", "Common.define.effectData.textLeftUp": "Αριστερ<PERSON> Πάνω", "Common.define.effectData.textLighten": "Φώτισμα", "Common.define.effectData.textLineColor": "Χρώμα Γραμμής", "Common.define.effectData.textLines": "Γραμμές", "Common.define.effectData.textLinesCurves": "Καμπύλες", "Common.define.effectData.textLoopDeLoop": "Κυκλική Περιστροφή", "Common.define.effectData.textLoops": "Βρόχοι", "Common.define.effectData.textModerate": "Μέτριο", "Common.define.effectData.textNeutron": "Νετρόνιο", "Common.define.effectData.textObjectCenter": "Κέντρο Αντικειμένου", "Common.define.effectData.textObjectColor": "Χρώμα Αντικειμένου", "Common.define.effectData.textOctagon": "Οκτάγωνο", "Common.define.effectData.textOut": "Έξω", "Common.define.effectData.textOutFromScreenBottom": "Έξω Από το Κάτω Μέρος της Οθόνης", "Common.define.effectData.textOutSlightly": "Ελαφρώς Έξω", "Common.define.effectData.textOutToScreenCenter": "Έξω Στο Κέντρο Της Οθόνης", "Common.define.effectData.textParallelogram": "Παραλληλόγραμμο", "Common.define.effectData.textPath": "Μονοπά<PERSON><PERSON><PERSON>ς", "Common.define.effectData.textPeanut": "Φυστίκι", "Common.define.effectData.textPeekIn": "Κρυφοκοίταγμα Μέσα", "Common.define.effectData.textPeekOut": "Κρυφοκοίταγμα Έξω", "Common.define.effectData.textPentagon": "Πεντάγωνο", "Common.define.effectData.textPinwheel": "Τροχ<PERSON>ς", "Common.define.effectData.textPlus": "Συν", "Common.define.effectData.textPointStar": "Αστέρι με Σημεία", "Common.define.effectData.textPointStar4": "Αστέρι 4 Σημείων", "Common.define.effectData.textPointStar5": "Αστέρι 5 Σημείων", "Common.define.effectData.textPointStar6": "Αστέρι 6 Σημείων", "Common.define.effectData.textPointStar8": "Αστέρι 8 Σημείων", "Common.define.effectData.textPulse": "Παλμός", "Common.define.effectData.textRandomBars": "Τυχαίες Ράβδοι", "Common.define.effectData.textRight": "Δεξιά", "Common.define.effectData.textRightDown": "Δεξιά Κάτω", "Common.define.effectData.textRightTriangle": "Ορθογώνιο Τρίγωνο", "Common.define.effectData.textRightUp": "Δεξιά Πάνω", "Common.define.effectData.textRiseUp": "Ανύψωση", "Common.define.effectData.textSCurve1": "S Καμπύλη 1", "Common.define.effectData.textSCurve2": "S Καμπύλη 2", "Common.define.effectData.textShape": "Σχήμα", "Common.define.effectData.textShapes": "Σχήματα", "Common.define.effectData.textShimmer": "Λαμπύρισμα", "Common.define.effectData.textShrinkTurn": "Σμίκρυνση και Στροφή", "Common.define.effectData.textSineWave": "Ημιτονοειδές Κύμα", "Common.define.effectData.textSinkDown": "Βύθιση Κάτω", "Common.define.effectData.textSlideCenter": "Κέντρο Διαφάνειας", "Common.define.effectData.textSpecial": "Ειδικ<PERSON>ς", "Common.define.effectData.textSpin": "Περιστροφή", "Common.define.effectData.textSpinner": "Στροβιλιστής", "Common.define.effectData.textSpiralIn": "Ελικοειδής Μέσα", "Common.define.effectData.textSpiralLeft": "Ελικοειδής Αριστερά", "Common.define.effectData.textSpiralOut": "Ελικοειδής Έξω", "Common.define.effectData.textSpiralRight": "Ελικοειδής Δεξιά", "Common.define.effectData.textSplit": "Διαίρεση", "Common.define.effectData.textSpoke1": "1 Ακτίνα", "Common.define.effectData.textSpoke2": "2 Ακτίνες", "Common.define.effectData.textSpoke3": "3 Ακτίνες", "Common.define.effectData.textSpoke4": "4 Ακτίνες", "Common.define.effectData.textSpoke8": "8 Ακτίνες", "Common.define.effectData.textSpring": "Ελατήριο", "Common.define.effectData.textSquare": "Τετράγωνο", "Common.define.effectData.textStairsDown": "Κλίμακα προς τα Κάτω", "Common.define.effectData.textStretch": "Επέκταση", "Common.define.effectData.textStrips": "Λ<PERSON>ρ<PERSON><PERSON><PERSON>ς", "Common.define.effectData.textSubtle": "Διακριτικ<PERSON>ς", "Common.define.effectData.textSwivel": "Στροφή", "Common.define.effectData.textSwoosh": "Ροή", "Common.define.effectData.textTeardrop": "Δά<PERSON>ρυ", "Common.define.effectData.textTeeter": "Τραμπάλα", "Common.define.effectData.textToBottom": "Προς το <PERSON>ος", "Common.define.effectData.textToBottomLeft": "Προς το <PERSON>ω Μ<PERSON><PERSON>ος <PERSON>ε<PERSON>", "Common.define.effectData.textToBottomRight": "Προς το <PERSON>ω Μ<PERSON>ρ<PERSON>", "Common.define.effectData.textToLeft": "Προς τα Αριστερά", "Common.define.effectData.textToRight": "Προς τα Δεξιά", "Common.define.effectData.textToTop": "Προς την <PERSON>υφ<PERSON>", "Common.define.effectData.textToTopLeft": "Προς την Κορυφή Αριστερά", "Common.define.effectData.textToTopRight": "Προς την Κορυφή Δεξιά", "Common.define.effectData.textTransparency": "Διαφάνεια", "Common.define.effectData.textTrapezoid": "Τραπέζιο", "Common.define.effectData.textTurnDown": "Στροφ<PERSON> Κάτω", "Common.define.effectData.textTurnDownRight": "Στροφή Κάτω Δεξιά", "Common.define.effectData.textTurns": "Στροφές", "Common.define.effectData.textTurnUp": "Στροφή Πάνω", "Common.define.effectData.textTurnUpRight": "Στροφή Πάνω Δεξιά", "Common.define.effectData.textUnderline": "Υπογράμμιση", "Common.define.effectData.textUp": "Πάνω", "Common.define.effectData.textVertical": "Κατακόρυφος", "Common.define.effectData.textVerticalFigure": "Κατακόρυφο Διάγραμμα 8", "Common.define.effectData.textVerticalIn": "Κατοκόρ<PERSON><PERSON><PERSON>", "Common.define.effectData.textVerticalOut": "Κατακόρυφος Έξω", "Common.define.effectData.textWave": "Κύμα", "Common.define.effectData.textWedge": "Σφήνα", "Common.define.effectData.textWheel": "Τροχ<PERSON>ς", "Common.define.effectData.textWhip": "Μαστίγιο", "Common.define.effectData.textWipe": "Εκκαθάριση", "Common.define.effectData.textZigzag": "Ζιγκζαγκ", "Common.define.effectData.textZoom": "Εστίαση", "Common.Translation.textMoreButton": "Περισσότερη", "Common.Translation.warnFileLocked": "Το αρχείο τελεί υπό επεξεργασία σε άλλη εφαρμογή. Μπορείτε να συνεχίσετε την επεξεργασία και να το αποθηκεύσετε ως αντίγραφο.", "Common.Translation.warnFileLockedBtnEdit": "Δημιουρ<PERSON><PERSON><PERSON> αντιγράφου", "Common.Translation.warnFileLockedBtnView": "Άνοιγμα για προβολή", "Common.UI.ButtonColored.textAutoColor": "Αυτόματα", "Common.UI.ButtonColored.textNewColor": "Νέου Προσαρμοσμένου Χρώματος", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON><PERSON> τεχνοτροπίες", "Common.UI.ExtendedColorDialog.addButtonText": "Προσθήκη", "Common.UI.ExtendedColorDialog.textCurrent": "Τρέχουσα", "Common.UI.ExtendedColorDialog.textHexErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια τιμή μεταξύ 000000 και FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Νέο", "Common.UI.ExtendedColorDialog.textRGBErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 και 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Απόκρυψη συνθηματικού", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Εμφάνιση συνθηματικού", "Common.UI.SearchBar.textFind": "Εύρεση", "Common.UI.SearchBar.tipCloseSearch": "Κλείσι<PERSON>ο αναζήτησης", "Common.UI.SearchBar.tipNextResult": "Επόμενο αποτέλεσμα", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Ανοίξτε τις σύνθετες ρυθμίσεις", "Common.UI.SearchBar.tipPreviousResult": "Προηγούμενο αποτέλεσμα", "Common.UI.SearchDialog.textHighlight": "Επισήμανση αποτελεσμάτων", "Common.UI.SearchDialog.textMatchCase": "Με διάκριση πεζών-κεφαλαίων", "Common.UI.SearchDialog.textReplaceDef": "Εισάγετε το κείμενο αντικατάστασης", "Common.UI.SearchDialog.textSearchStart": "Εισάγετε το κείμενό σας εδώ", "Common.UI.SearchDialog.textTitle": "Εύρεση και Αντικατάσταση", "Common.UI.SearchDialog.textTitle2": "Εύρεση", "Common.UI.SearchDialog.textWholeWords": "Ολόκληρες λέξεις μόνο", "Common.UI.SearchDialog.txtBtnHideReplace": "Απόκρυψη Αντικατάστασης", "Common.UI.SearchDialog.txtBtnReplace": "Αντικατάσταση", "Common.UI.SearchDialog.txtBtnReplaceAll": "Αντικατάσταση Όλων", "Common.UI.SynchronizeTip.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.UI.SynchronizeTip.textSynchronize": "Το έγγραφο έχει αλλάξει από άλλο χρήστη.<br>Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>με κάντε κλικ για να αποθηκεύσετε τις αλλαγές σας και να φορτώσετε ξανά τις ενημερώσεις.", "Common.UI.ThemeColorPalette.textRecentColors": "Πρόσφατα Χρώματα", "Common.UI.ThemeColorPalette.textStandartColors": "Τυπικά Χρώματα", "Common.UI.ThemeColorPalette.textThemeColors": "Χρώμα<PERSON><PERSON>έμ<PERSON>τος", "Common.UI.Themes.txtThemeClassicLight": "Κλασικό Ανοιχτό", "Common.UI.Themes.txtThemeDark": "Σκούρο", "Common.UI.Themes.txtThemeLight": "Ανοιχτό", "Common.UI.Themes.txtThemeSystem": "Ίδιο με το σύστημα", "Common.UI.Window.cancelButtonText": "Ακύρωση", "Common.UI.Window.closeButtonText": "Κλείσιμο", "Common.UI.Window.noButtonText": "Όχι", "Common.UI.Window.okButtonText": "Εντάξει", "Common.UI.Window.textConfirmation": "Επιβεβαίωση", "Common.UI.Window.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.UI.Window.textError": "Σφάλμα", "Common.UI.Window.textInformation": "Πληροφορίες", "Common.UI.Window.textWarning": "Προειδοποίηση", "Common.UI.Window.yesButtonText": "Ναι", "Common.Utils.Metric.txtCm": "εκ", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "Διεύθυνση: ", "Common.Views.About.txtLicensee": "ΑΔΕΙΟΔΕΚΤΗΣ", "Common.Views.About.txtLicensor": "ΑΔΕΙΟΔΟΤΗΣ", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Υποστηρίζεται από", "Common.Views.About.txtTel": "Tηλ.: ", "Common.Views.About.txtVersion": "Έκδοση ", "Common.Views.AutoCorrectDialog.textAdd": "Προσθήκη", "Common.Views.AutoCorrectDialog.textApplyText": "Εφαρμογή Κατά Την Πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Αυτόματη Διόρθωση", "Common.Views.AutoCorrectDialog.textAutoFormat": "Αυτόματη Μορφοποίηση Κατά Την Πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textBulleted": "Αυτόματες λίστες κουκκίδων", "Common.Views.AutoCorrectDialog.textBy": "Κατά", "Common.Views.AutoCorrectDialog.textDelete": "Διαγραφή", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Εισαγωγή διπλού διαστήματος", "Common.Views.AutoCorrectDialog.textFLCells": "Να γίνει κεφαλαίο το πρώτο γράμμα των κελιών του πίνακα", "Common.Views.AutoCorrectDialog.textFLSentence": "Κεφαλαιοποιήστε το πρώτο γράμμα των προτάσεων", "Common.Views.AutoCorrectDialog.textHyperlink": "Μονοπάτια δικτύου και διαδικτύου με υπερσυνδέσμους", "Common.Views.AutoCorrectDialog.textHyphens": "Παύλες (--) με πλατιά παύλα (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Αυτόματη Διόρθωση Μαθηματικών", "Common.Views.AutoCorrectDialog.textNumbered": "Αυτόματες αριθμημένες λίστες", "Common.Views.AutoCorrectDialog.textQuotes": "\"Ίσια εισαγωγικά\" με \"έξυπνα εισαγωγικά\"", "Common.Views.AutoCorrectDialog.textRecognized": "Αναγνωρισμένες Συναρτήσεις", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Οι ακόλουθες εκφράσεις αναγνωρίζονται ως μαθηματικές. Δεν θα μορφοποιηθούν αυτόματα με πλάγια γράμματα.", "Common.Views.AutoCorrectDialog.textReplace": "Αντικατάσταση", "Common.Views.AutoCorrectDialog.textReplaceText": "Αντικ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Κατά Την Πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textReplaceType": "Αντικα<PERSON><PERSON><PERSON>τα<PERSON>η κειμένου κατά την πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textReset": "Επαναφορά", "Common.Views.AutoCorrectDialog.textResetAll": "Αρχικοποίηση στην προεπιλογή", "Common.Views.AutoCorrectDialog.textRestore": "Επαναφορά", "Common.Views.AutoCorrectDialog.textTitle": "Αυτόματη Διόρθωση", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Οι αναγνωρισμένες συναρτήσεις πρέπει να περιέχουν μόνο τα γράμματα A <PERSON>ω<PERSON> Z, κεφαλα<PERSON>α ή μικρά.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Κάθε έκφραση που προσθέσατε θα αφαιρεθεί και ό,τι αφαιρέθηκε θα αποκατασταθεί. Θέλετε να συνεχίσετε;", "Common.Views.AutoCorrectDialog.warnReplace": "Η καταχώρηση αυτόματης διόρθωσης για %1 υπάρχει ήδη. Θέλετε να την αντικαταστήσετε;", "Common.Views.AutoCorrectDialog.warnReset": "Κάθε αυτόματη διόρθωση που προσθέσατε θα αφαιρεθεί και ό,τι τροποποιήθηκε θα αποκατασταθεί στην αρχική του τιμή. Θέλετε να συνεχίσετε;", "Common.Views.AutoCorrectDialog.warnRestore": "Η καταχώρηση αυτόματης διόρθωσης για %1 θα τεθεί στην αρχική τιμή της. Θέλετε να συνεχίσετε;", "Common.Views.Chat.textSend": "Αποστολή", "Common.Views.Comments.mniAuthorAsc": "Συγγραφ<PERSON>ας Α έως Ω", "Common.Views.Comments.mniAuthorDesc": "Συγγραφ<PERSON>ας Ω έως Α", "Common.Views.Comments.mniDateAsc": "Παλαιότερο", "Common.Views.Comments.mniDateDesc": "Νεότερο", "Common.Views.Comments.mniFilterGroups": "Φιλτράρισμα κατά Ομάδα", "Common.Views.Comments.mniPositionAsc": "Από πάνω", "Common.Views.Comments.mniPositionDesc": "Από κάτω", "Common.Views.Comments.textAdd": "Προσθήκη", "Common.Views.Comments.textAddComment": "Προσθήκη Σχολίου", "Common.Views.Comments.textAddCommentToDoc": "Προσθήκη Σχολίου στο Έγγραφο", "Common.Views.Comments.textAddReply": "Προσθήκη Απάντησης", "Common.Views.Comments.textAll": "Όλα", "Common.Views.Comments.textAnonym": "Επισκέπτης", "Common.Views.Comments.textCancel": "Ακύρωση", "Common.Views.Comments.textClose": "Κλείσιμο", "Common.Views.Comments.textClosePanel": "Κλείσιμο σχολίων", "Common.Views.Comments.textComments": "Σχόλια", "Common.Views.Comments.textEdit": "Εντάξει", "Common.Views.Comments.textEnterCommentHint": "Εισάγετε το σχόλιό σας εδώ", "Common.Views.Comments.textHintAddComment": "Προσθήκη σχολίου", "Common.Views.Comments.textOpenAgain": "Άνοιγμα Ξανά", "Common.Views.Comments.textReply": "Απάντηση", "Common.Views.Comments.textResolve": "Επίλυση", "Common.Views.Comments.textResolved": "Επιλύθηκε", "Common.Views.Comments.textSort": "Ταξινόμηση σχολίων", "Common.Views.Comments.textViewResolved": "Δεν έχετε άδεια να ανοίξετε ξανά το σχόλιο", "Common.Views.Comments.txtEmpty": "Δεν υπάρχουν σχόλια στο έγγραφο.", "Common.Views.CopyWarningDialog.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.Views.CopyWarningDialog.textMsg": "Η αντιγραφή, η αποκοπή και η επικόλληση μέσω των κουμπιών της εργαλειοθήκης του συντάκτη καθώς και οι ενέργειες του μενού συμφραζομένων εφαρμόζονται μόνο εντός αυτής της καρτέλας.<br><br>Γ<PERSON>α αντιγραφή ή επικόλληση από ή προς εφαρμογές εκτός της καρτέλας χρησιμοποιήστε τους ακόλουθους συνδυασμούς πλήκτρων:", "Common.Views.CopyWarningDialog.textTitle": "Ενέργειες Αντιγ<PERSON>α<PERSON><PERSON><PERSON>, Α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και Επικόλλησης", "Common.Views.CopyWarningDialog.textToCopy": "για Αντιγραφή", "Common.Views.CopyWarningDialog.textToCut": "για Αποκοπή", "Common.Views.CopyWarningDialog.textToPaste": "για Επικόλληση", "Common.Views.DocumentAccessDialog.textLoading": "Φόρτωση...", "Common.Views.DocumentAccessDialog.textTitle": "Ρυθμίσεις Διαμοιρασμού", "Common.Views.ExternalDiagramEditor.textTitle": "Συντάκτης <PERSON>α<PERSON>ήματος", "Common.Views.Header.labelCoUsersDescr": "Οι χρήστες που επεξεργ<PERSON><PERSON>ονται το αρχείο:", "Common.Views.Header.textAddFavorite": "Σημείωση ως αγαπημένο", "Common.Views.Header.textAdvSettings": "Προηγμένες ρυθμίσεις", "Common.Views.Header.textBack": "Άνοιγμα τοποθεσίας αρχείου", "Common.Views.Header.textCompactView": "Απόκρυψη Γραμμής Εργαλείων", "Common.Views.Header.textHideLines": "Απόκρυψη Χαράκων", "Common.Views.Header.textHideNotes": "Απόκρυψη Σημειώσεων", "Common.Views.Header.textHideStatusBar": "Απόκρυψη Γραμ<PERSON><PERSON>ς <PERSON>τάστασης", "Common.Views.Header.textRemoveFavorite": "Αφαίρεση από τα Αγαπημένα", "Common.Views.Header.textSaveBegin": "Αποθήκευση...", "Common.Views.Header.textSaveChanged": "Τροποποιημένο", "Common.Views.Header.textSaveEnd": "Όλες οι αλλαγές αποθηκεύτηκαν", "Common.Views.Header.textSaveExpander": "Όλες οι αλλαγές αποθηκεύτηκαν", "Common.Views.Header.textShare": "Διαμοιρασμός", "Common.Views.Header.textZoom": "Εστίαση", "Common.Views.Header.tipAccessRights": "Διαχείριση δικαιωμάτων πρόσβασης εγγράφου", "Common.Views.Header.tipDownload": "Λήψη αρχείου", "Common.Views.Header.tipGoEdit": "Επεξεργασία τρέχοντος αρχείου", "Common.Views.Header.tipPrint": "Εκτύπωση αρχείου", "Common.Views.Header.tipRedo": "Επανάληψη", "Common.Views.Header.tipSave": "Αποθήκευση", "Common.Views.Header.tipSearch": "Αναζήτηση", "Common.Views.Header.tipUndo": "Αναίρεση", "Common.Views.Header.tipUndock": "Απαγ<PERSON>ίστρωση σε ξεχωριστό παράθυρο", "Common.Views.Header.tipUsers": "Προβολή χρηστών", "Common.Views.Header.tipViewSettings": "Προβολή ρυθμίσεων", "Common.Views.Header.tipViewUsers": "Προβολή χρηστών και διαχείριση δικαιωμάτων πρόσβασης σε έγγραφα", "Common.Views.Header.txtAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "Common.Views.Header.txtRename": "Μετονομασία", "Common.Views.History.textCloseHistory": "Κλείσιμο ιστορικού", "Common.Views.History.textHide": "Κλείσιμο", "Common.Views.History.textHideAll": "Απόκρυψη λεπτομερών αλλαγών", "Common.Views.History.textRestore": "Επαναφορά", "Common.Views.History.textShow": "Επέκταση", "Common.Views.History.textShowAll": "Εμφάνιση λεπτομερών αλλαγών", "Common.Views.History.textVer": "εκδ.", "Common.Views.ImageFromUrlDialog.textUrl": "Επικόλληση URL εικόνας:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Αυτό το πεδίο πρέπει να είναι διεύθυνση URL με τη μορφή «http://www.example.com»", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Πρέπει να ορίσετε έγκυρο αριθμό γραμμών και στηλών.", "Common.Views.InsertTableDialog.txtColumns": "Αριθ<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "Η μέγιστη τιμή για αυτό το πεδίο είναι {0}.", "Common.Views.InsertTableDialog.txtMinText": "Η ελάχιστη τιμή για αυτό το πεδίο είναι {0}.", "Common.Views.InsertTableDialog.txtRows": "Αριθμός <PERSON>ν", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "Διαίρεση Κελιού", "Common.Views.LanguageDialog.labelSelect": "Επιλογή γλώσσας εγγράφου", "Common.Views.ListSettingsDialog.textBulleted": "Με κουκίδες", "Common.Views.ListSettingsDialog.textNumbering": "Αριθμημένο", "Common.Views.ListSettingsDialog.tipChange": "Αλλαγ<PERSON> κουκίδων", "Common.Views.ListSettingsDialog.txtBullet": "Κουκκίδα", "Common.Views.ListSettingsDialog.txtColor": "Χρώμα", "Common.Views.ListSettingsDialog.txtNewBullet": "Νέα κουκίδα", "Common.Views.ListSettingsDialog.txtNone": "Κανένα", "Common.Views.ListSettingsDialog.txtOfText": "% του κειμένου", "Common.Views.ListSettingsDialog.txtSize": "Μέγεθος", "Common.Views.ListSettingsDialog.txtStart": "Έναρξη από", "Common.Views.ListSettingsDialog.txtSymbol": "Σύμβολο", "Common.Views.ListSettingsDialog.txtTitle": "Ρυθμίσεις Λίστας", "Common.Views.ListSettingsDialog.txtType": "Τύπος", "Common.Views.OpenDialog.closeButtonText": "Κλείσιμο Αρχείου", "Common.Views.OpenDialog.txtEncoding": "Κωδικοποίηση", "Common.Views.OpenDialog.txtIncorrectPwd": "Το συνθηματικό είναι εσφαλμένο.", "Common.Views.OpenDialog.txtOpenFile": "Εισάγετε συνθηματικό για να ανοίξετε το αρχείο", "Common.Views.OpenDialog.txtPassword": "Συνθηματικό", "Common.Views.OpenDialog.txtProtected": "Μό<PERSON><PERSON>ς βάλετε το συνθηματικ<PERSON> και ανοίξετε το αρχείο, το τρέχον συνθηματικό αρχείου θα αρχικοποιηθεί.", "Common.Views.OpenDialog.txtTitle": "Διαλέξτε %1 επιλογές", "Common.Views.OpenDialog.txtTitleProtected": "Προστατευμένο Αρχείο", "Common.Views.PasswordDialog.txtDescription": "Ορίστε ένα συνθηματικό για την προστασία αυτού του εγγράφου", "Common.Views.PasswordDialog.txtIncorrectPwd": "Το συνθηματικ<PERSON> επιβεβαίωσης δεν είναι πανομοιότυπο", "Common.Views.PasswordDialog.txtPassword": "Συνθηματικό", "Common.Views.PasswordDialog.txtRepeat": "Επανάληψη συνθηματικού", "Common.Views.PasswordDialog.txtTitle": "Ορισμός Συνθηματικού", "Common.Views.PasswordDialog.txtWarning": "Προσοχή: <PERSON><PERSON><PERSON> χ<PERSON>τε ή ξεχάσετε το συνθηματικό, δεν είναι δυνατή η ανάκτησή του. Παρακαλούμε διατηρήστε το σε ασφαλές μέρος.", "Common.Views.PluginDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.Plugins.groupCaption": "Πρόσθετα", "Common.Views.Plugins.strPlugins": "Πρόσθετα", "Common.Views.Plugins.textClosePanel": "Κλείσιμο πρόσθετου", "Common.Views.Plugins.textLoading": "Γίνεται φόρτωση", "Common.Views.Plugins.textStart": "Εκκίνηση", "Common.Views.Plugins.textStop": "Διακοπή", "Common.Views.Protection.hintAddPwd": "Κρυπτογράφηση με συνθηματικό", "Common.Views.Protection.hintPwd": "Αλλα<PERSON><PERSON> <PERSON> διαγραφή συνθηματικού", "Common.Views.Protection.hintSignature": "Προσθήκη ψηφιακής υπογραφής ή γραμμής υπογραφής", "Common.Views.Protection.txtAddPwd": "Προσθήκη συνθηματικού", "Common.Views.Protection.txtChangePwd": "Αλλαγή συνθηματικού", "Common.Views.Protection.txtDeletePwd": "Διαγραφή συνθηματικού", "Common.Views.Protection.txtEncrypt": "Κρυπτογράφηση", "Common.Views.Protection.txtInvisibleSignature": "Προσθήκη ψηφιακής υπογραφής", "Common.Views.Protection.txtSignature": "Υπογραφή", "Common.Views.Protection.txtSignatureLine": "Προσθήκη γραμμής υπογραφής", "Common.Views.RenameDialog.textName": "Όνομα αρχείου", "Common.Views.RenameDialog.txtInvalidName": "Το όνομα αρχείου δεν μπορεί να περιέχει κανέναν από τους ακόλουθους χαρακτήρες:", "Common.Views.ReviewChanges.hintNext": "Στην επόμενη αλλαγή", "Common.Views.ReviewChanges.hintPrev": "Στην προηγούμενη αλλαγή", "Common.Views.ReviewChanges.strFast": "Γρήγ<PERSON><PERSON>η", "Common.Views.ReviewChanges.strFastDesc": "Συν-επεξεργασία πραγματικού χρόνου. Όλες οι αλλαγές αποθηκεύονται αυτόματα.", "Common.Views.ReviewChanges.strStrict": "Αυστηρή", "Common.Views.ReviewChanges.strStrictDesc": "Χρησιμοποιήστε το κουμπί 'Αποθήκευση' για να συγχρονίσετε τις αλλαγές που κάνετε εσείς και οι άλλοι.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Αποδοχή τρέχουσας αλλαγής", "Common.Views.ReviewChanges.tipCoAuthMode": "Ορισμ<PERSON><PERSON> κατάστασης συν-επεξεργασίας", "Common.Views.ReviewChanges.tipCommentRem": "Αφαίρεση σχολίων", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Αφαίρεση υφιστάμενων σχολίων", "Common.Views.ReviewChanges.tipCommentResolve": "Επίλυση σχολίων", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Επίλυση των τρεχόντων σχολίων", "Common.Views.ReviewChanges.tipHistory": "Εμφάνιση ιστορικού εκδόσεων", "Common.Views.ReviewChanges.tipRejectCurrent": "Απόρριψη τρέχουσας αλλαγής", "Common.Views.ReviewChanges.tipReview": "Παρα<PERSON><PERSON>λούθηση αλλαγών", "Common.Views.ReviewChanges.tipReviewView": "Επιλέξτε τον τρόπο προβολής των αλλαγών", "Common.Views.ReviewChanges.tipSetDocLang": "Ορισμ<PERSON><PERSON> γλώσσας εγγράφου", "Common.Views.ReviewChanges.tipSetSpelling": "Έλεγχος ορθογραφίας", "Common.Views.ReviewChanges.tipSharing": "Διαχείριση δικαιωμάτων πρόσβασης εγγράφου", "Common.Views.ReviewChanges.txtAccept": "Αποδοχή", "Common.Views.ReviewChanges.txtAcceptAll": "Αποδοχή Όλων των Αλλαγών", "Common.Views.ReviewChanges.txtAcceptChanges": "Αποδοχ<PERSON> αλλαγών", "Common.Views.ReviewChanges.txtAcceptCurrent": "Αποδοχή Τρέχουσας Αλλαγής", "Common.Views.ReviewChanges.txtChat": "Συνομιλία", "Common.Views.ReviewChanges.txtClose": "Κλείσιμο", "Common.Views.ReviewChanges.txtCoAuthMode": "Κατάσταση Συν-επεξεργασίας", "Common.Views.ReviewChanges.txtCommentRemAll": "Αφαίρεση Όλων των Σχολίων", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Αφαίρεση Υφιστάμενων Σχολίων", "Common.Views.ReviewChanges.txtCommentRemMy": "Αφαίρεση των Σχολίων Μου", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Διαγρα<PERSON><PERSON> Πρόσφατων Σχολίων Μου", "Common.Views.ReviewChanges.txtCommentRemove": "Αφαίρεση", "Common.Views.ReviewChanges.txtCommentResolve": "Επίλυση", "Common.Views.ReviewChanges.txtCommentResolveAll": "Επίλυση όλων των σχολίων", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Επίλυση των τρεχόντων σχολίων", "Common.Views.ReviewChanges.txtCommentResolveMy": "Επίλυση των σχολίων μου", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Επίλυση των τρεχόντων σχολίων μου", "Common.Views.ReviewChanges.txtDocLang": "Γλώσσα", "Common.Views.ReviewChanges.txtFinal": "Όλες οι αλλαγές έγιναν αποδεκτές (Προεπισκόπηση)", "Common.Views.ReviewChanges.txtFinalCap": "Τελικ<PERSON>ς", "Common.Views.ReviewChanges.txtHistory": "Ιστορικ<PERSON> εκδόσεων", "Common.Views.ReviewChanges.txtMarkup": "Όλες οι αλλαγές (Επεξεργασία)", "Common.Views.ReviewChanges.txtMarkupCap": "Σήμανση", "Common.Views.ReviewChanges.txtNext": "Επόμενο", "Common.Views.ReviewChanges.txtOriginal": "Όλες οι αλλαγές απορρίφθηκαν (Προεπισκόπηση)", "Common.Views.ReviewChanges.txtOriginalCap": "Πρωτότυπο", "Common.Views.ReviewChanges.txtPrev": "Προηγούμενο", "Common.Views.ReviewChanges.txtReject": "Απόρριψη", "Common.Views.ReviewChanges.txtRejectAll": "Απόρριψη Όλων των Αλλαγών", "Common.Views.ReviewChanges.txtRejectChanges": "Απόρριψη αλλαγών", "Common.Views.ReviewChanges.txtRejectCurrent": "Απόρριψη Τρέχουσας Αλλαγής", "Common.Views.ReviewChanges.txtSharing": "Διαμοιρασμός", "Common.Views.ReviewChanges.txtSpelling": "Έλεγχος Ορθογραφίας", "Common.Views.ReviewChanges.txtTurnon": "Παρακ<PERSON>λούθηση Αλλαγών", "Common.Views.ReviewChanges.txtView": "Κατάσταση Προβολής", "Common.Views.ReviewPopover.textAdd": "Προσθήκη", "Common.Views.ReviewPopover.textAddReply": "Προσθήκη Απάντησης", "Common.Views.ReviewPopover.textCancel": "Ακύρωση", "Common.Views.ReviewPopover.textClose": "Κλείσιμο", "Common.Views.ReviewPopover.textEdit": "Εντάξει", "Common.Views.ReviewPopover.textMention": "+mention θα δώσει πρόσβαση στο αρχείο και θα στείλει email", "Common.Views.ReviewPopover.textMentionNotify": "+mention θα ενημερώσει τον χρήστη με email", "Common.Views.ReviewPopover.textOpenAgain": "Άνοιγμα Ξανά", "Common.Views.ReviewPopover.textReply": "Απάντηση", "Common.Views.ReviewPopover.textResolve": "Επίλυση", "Common.Views.ReviewPopover.textViewResolved": "Δεν έχετε άδεια να ανοίξετε ξανά το σχόλιο", "Common.Views.ReviewPopover.txtDeleteTip": "Διαγραφή", "Common.Views.ReviewPopover.txtEditTip": "Επεξεργασία", "Common.Views.SaveAsDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.SaveAsDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> για αποθήκευση", "Common.Views.SearchPanel.textCaseSensitive": "Διάκριση Πεζών-Κεφαλαίων", "Common.Views.SearchPanel.textCloseSearch": "Κλείσι<PERSON>ο αναζήτησης", "Common.Views.SearchPanel.textFind": "Εύρεση", "Common.Views.SearchPanel.textFindAndReplace": "Εύρεση και Αντικατάσταση", "Common.Views.SearchPanel.textMatchUsingRegExp": "Ταίριασμα χρησιμοποιώντας κανονικές εκφράσεις (regexp)", "Common.Views.SearchPanel.textNoMatches": "Χ<PERSON><PERSON><PERSON>ς ταίριασμα", "Common.Views.SearchPanel.textNoSearchResults": "Δεν υπάρχουν αποτελέσματα αναζήτησης", "Common.Views.SearchPanel.textReplace": "Αντικατάσταση", "Common.Views.SearchPanel.textReplaceAll": "Αντικατάσταση Όλων", "Common.Views.SearchPanel.textReplaceWith": "Αντικατάσταση με", "Common.Views.SearchPanel.textSearchResults": "Αποτελέσματα αναζήτησης: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Υπάρχουν πάρα πολλά αποτελέσματα για εμφάνιση εδώ", "Common.Views.SearchPanel.textWholeWords": "Ολόκληρες λέξεις μόνο", "Common.Views.SearchPanel.tipNextResult": "Επόμενο αποτέλεσμα", "Common.Views.SearchPanel.tipPreviousResult": "Προηγούμενο αποτέλεσμα", "Common.Views.SelectFileDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.SelectFileDlg.textTitle": "Επιλογή Πηγής Δεδομένων", "Common.Views.SignDialog.textBold": "Έντονα", "Common.Views.SignDialog.textCertificate": "Πιστοποιητικό", "Common.Views.SignDialog.textChange": "Αλλαγή", "Common.Views.SignDialog.textInputName": "Εισαγω<PERSON><PERSON> ονόμα<PERSON>ος υπογράφοντος", "Common.Views.SignDialog.textItalic": "Πλάγια", "Common.Views.SignDialog.textNameError": "Το όνομα υπογράφοντα δεν μπορεί να είναι κενό.", "Common.Views.SignDialog.textPurpose": "Ο σκοπός για υπογραφή αυτού του εγγράφου", "Common.Views.SignDialog.textSelect": "Επιλογή", "Common.Views.SignDialog.textSelectImage": "Επιλογή Εικόνας", "Common.Views.SignDialog.textSignature": "Η υπογραφή μοιάζει με", "Common.Views.SignDialog.textTitle": "Υπογράψτε το Έγγραφο", "Common.Views.SignDialog.textUseImage": "ή κάντε κλικ στο 'Επιλογή Εικόνας' για να χρησιμοποιήσετε μια εικόνα ως υπογραφή", "Common.Views.SignDialog.textValid": "Έγκυρο από %1 έως %2", "Common.Views.SignDialog.tipFontName": "Όνομα Γραμματοσειράς", "Common.Views.SignDialog.tipFontSize": "Μέγεθος <PERSON>αμματοσειράς", "Common.Views.SignSettingsDialog.textAllowComment": "Να επιτρέπεται στον υπογράφοντα να προσθέτει σχόλιο στο διάλογο υπογραφής", "Common.Views.SignSettingsDialog.textInfoEmail": "Ηλεκτρονική Διεύθυνση", "Common.Views.SignSettingsDialog.textInfoName": "Όνομα", "Common.Views.SignSettingsDialog.textInfoTitle": "Τίτλος Υπογράφοντος", "Common.Views.SignSettingsDialog.textInstructions": "Οδηγίες προς Υπογράφοντα", "Common.Views.SignSettingsDialog.textShowDate": "Εμφάνιση ημερομηνίας υπογραφής στη γραμμή υπογράφοντος", "Common.Views.SignSettingsDialog.textTitle": "Ρύθμιση Υπογραφής", "Common.Views.SignSettingsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Δεκαεξαδική τιμή Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Σήμα Πνευματικών Δικαιωμάτων", "Common.Views.SymbolTableDialog.textDCQuote": "Κλείσιμο Διπλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textDOQuote": "Άνοιγμα Διπλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textEllipsis": "Οριζόντια Έλλειψη", "Common.Views.SymbolTableDialog.textEmDash": "Πλατιά Παύλα Em", "Common.Views.SymbolTableDialog.textEmSpace": "Διάστημα Em", "Common.Views.SymbolTableDialog.textEnDash": "Πλατιά Παύλα En", "Common.Views.SymbolTableDialog.textEnSpace": "Διάστημα En", "Common.Views.SymbolTableDialog.textFont": "Γραμματοσειρά", "Common.Views.SymbolTableDialog.textNBHyphen": "Προστατευμένη Παύλα", "Common.Views.SymbolTableDialog.textNBSpace": "Προστατευμένο Διάστημα", "Common.Views.SymbolTableDialog.textPilcrow": "Σύμβολο <PERSON>αραγράφου", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em Διάστημα", "Common.Views.SymbolTableDialog.textRange": "Εύρ<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Πρόσφατα χρησιμοποιημένα σύμβολα", "Common.Views.SymbolTableDialog.textRegistered": "Σύμβο<PERSON><PERSON>ταχωρημένου", "Common.Views.SymbolTableDialog.textSCQuote": "Κλείσιμο Απλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textSection": "Σύμβολο Τ<PERSON>ή<PERSON>ατος", "Common.Views.SymbolTableDialog.textShortcut": "Πλήκτρ<PERSON>τόμευσης", "Common.Views.SymbolTableDialog.textSHyphen": "Απαλή Παύλα", "Common.Views.SymbolTableDialog.textSOQuote": "Άνοιγμα Απλών Εισαγωγικών", "Common.Views.SymbolTableDialog.textSpecial": "Ειδικοί χαρακτήρες", "Common.Views.SymbolTableDialog.textSymbols": "Σύμβολα", "Common.Views.SymbolTableDialog.textTitle": "Σύμβολο", "Common.Views.SymbolTableDialog.textTradeMark": "Σύμβολο Εμπορικού Σήματος", "Common.Views.UserNameDialog.textDontShow": "Να μην ερωτηθώ ξανά", "Common.Views.UserNameDialog.textLabel": "Ετικέτα:", "Common.Views.UserNameDialog.textLabelError": "Η ετικέτα δεν μπορεί να είναι κενή.", "PE.Controllers.LeftMenu.leavePageText": "Όλες οι μη αποθηκευμένες αλλαγές σε αυτό το έγγραφο θα χαθούν.<br>Κ<PERSON><PERSON><PERSON><PERSON> κλικ στο «Ακύρωση» και στη συνέχεια στο «Αποθήκευση» για να τις αποθηκεύσετε. Κάντε κλικ στο «Εντάξει» για να απορρίψετε όλες τις μη αποθηκευμένες αλλαγές.", "PE.Controllers.LeftMenu.newDocumentTitle": "Παρουσία<PERSON>η χωρίς όνομα", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Προειδοποίηση", "PE.Controllers.LeftMenu.requestEditRightsText": "Αίτηση δικαιωμάτων επεξεργασίας...", "PE.Controllers.LeftMenu.textLoadHistory": "Φόρτωση ιστορικού εκδόσεων...", "PE.Controllers.LeftMenu.textNoTextFound": "Τα δεδομένα που αναζητάτε δεν βρέθηκαν. Παρακαλούμε προσαρμόστε τις επιλογές αναζήτησης.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Η αντικατάσταση έγινε. {0} εμφανίσεις προσπεράστηκαν.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Η αναζήτηση ολοκληρώθηκε. Εμφανίσεις που αντικαταστάθηκαν: {0}", "PE.Controllers.LeftMenu.txtUntitled": "Άτιτλο", "PE.Controllers.Main.applyChangesTextText": "Φόρτωση δεδομένων...", "PE.Controllers.Main.applyChangesTitleText": "Φόρτωση Δεδομένων", "PE.Controllers.Main.convertationTimeoutText": "Υπέρβαση χρονικού ορίου μετατροπής.", "PE.Controllers.Main.criticalErrorExtText": "Πατήστ<PERSON> \"Εντάξει\" για να επιστρέψετε στη λίστα εγγράφων.", "PE.Controllers.Main.criticalErrorTitle": "Σφάλμα", "PE.Controllers.Main.downloadErrorText": "Αποτυχία λήψης.", "PE.Controllers.Main.downloadTextText": "Γίνεται λήψη παρουσίασης...", "PE.Controllers.Main.downloadTitleText": "Λήψη Παρουσίασης", "PE.Controllers.Main.errorAccessDeny": "Προσπαθείτε να εκτελέσετε μια ενέργεια για την οποία δεν έχετε δικαιώματα.<br>Παρακαλούμε να επικοινωνήστε με τον διαχειριστή του διακομιστή εγγράφων.", "PE.Controllers.Main.errorBadImageUrl": "Εσφαλμένη διεύθυνση URL εικόνας", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Η σύνδεση διακομιστή χάθηκε. Δεν είναι δυνατή η επεξεργασία του εγγράφου αυτήν τη στιγμή.", "PE.Controllers.Main.errorComboSeries": "Για να δημιουργήσετε συνδυαστικ<PERSON> γράφημα, επιλέξτε τουλάχιστον δύο σειρές δεδομένων.", "PE.Controllers.Main.errorConnectToServer": "Δεν ήταν δυνατή η αποθήκευση του εγγράφου. Παρακαλούμε ελέγξτε τις ρυθμίσεις σύνδεσης ή επικοινωνήστε με τον διαχειριστή σας.<br>Όταν κάνετε κλικ στο κουμπί «OK», θα σας ζητηθεί να πραγματοποιήσετε λήψη του εγγράφου.", "PE.Controllers.Main.errorDatabaseConnection": "Εξωτερι<PERSON><PERSON> σφάλμα.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σύνδεσης βάσης δεδομένων. Παρακαλούμε επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "PE.Controllers.Main.errorDataEncrypted": "Οι κρυπτογραφημένες αλλαγ<PERSON>ς έχουν ληφθεί, δεν μπορούν να αποκρυπτογραφηθούν.", "PE.Controllers.Main.errorDataRange": "Εσφαλμένο εύρος δεδομένων.", "PE.Controllers.Main.errorDefaultMessage": "Κωδικός σφάλματος: %1", "PE.Controllers.Main.errorEditingDownloadas": "Παρουσ<PERSON>ά<PERSON>τηκε σφάλμα κατά την εργασία με το έγγραφο.<br>Χρησιμοποιήστε την επιλογή «Λήψη ως» για να αποθηκεύσετε το αντίγραφο ασφαλείας στον σκληρό δίσκο του υπολογιστή σας.", "PE.Controllers.Main.errorEditingSaveas": "Παρουσιάστηκε σφάλμα κατά την εργασία με το έγγραφο.<br>Χρησιμοποιήστε την επιλογή «Αποθήκευση ως...» για να αποθηκεύσετε το αντίγραφο ασφαλείας στον σκληρό δίσκο του υπολογιστή σας.", "PE.Controllers.Main.errorEmailClient": "Δε βρέθηκε καμιά εφαρμογή ηλεκτρονικού ταχυδρομείου.", "PE.Controllers.Main.errorFilePassProtect": "Το αρχεί<PERSON> προστατεύεται με συνθηματικό και δεν μπορεί να ανοίξει.", "PE.Controllers.Main.errorFileSizeExceed": "Το μέγεθος του αρχείου υπερβαίνει το όριο που έχει οριστεί για τον διακομιστή σας.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων για λεπτομέρειες.", "PE.Controllers.Main.errorForceSave": "Παρουσ<PERSON><PERSON><PERSON><PERSON>ηκε σφάλμα κατά την αποθήκευση του αρχείου. Χρησιμοποιήστε την επιλογή «Λήψη ως» για να αποθηκεύσετε το αρχείο στον σκληρό δίσκο του υπολογιστή σας ή δοκιμάστε ξανά αργότερα.", "PE.Controllers.Main.errorKeyEncrypt": "Άγνωστος περιγραφέας κλειδιού", "PE.Controllers.Main.errorKeyExpire": "Ο περιγραφέας κλειδιού έχει λήξει", "PE.Controllers.Main.errorLoadingFont": "Οι γραμματοσειρές δεν έχουν φορτωθεί.<br>Παρα<PERSON><PERSON>λ<PERSON>ύμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων σας.", "PE.Controllers.Main.errorProcessSaveResult": "Αποτυχία αποθήκευσης.", "PE.Controllers.Main.errorServerVersion": "Αναβαθμίστηκε η έκδοση του συντάκτη. Η σελίδα θα φορτωθεί ξανά για να εφαρμοστούν οι αλλαγές.", "PE.Controllers.Main.errorSessionAbsolute": "Η σύνοδος επεξεργασίας εγγράφου έχει λήξει. Παρακαλούμε ανανεώστε τη σελίδα.", "PE.Controllers.Main.errorSessionIdle": "Το έγγραφο δεν έχει επεξεργα<PERSON>τε<PERSON> εδώ και πολύ ώρα. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "PE.Controllers.Main.errorSessionToken": "Η σύνδεση με το διακομιστή έχει διακοπεί. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "PE.Controllers.Main.errorSetPassword": "Δεν ήταν δυνα<PERSON><PERSON>ς ο ορισμός του συνθηματικού.", "PE.Controllers.Main.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "PE.Controllers.Main.errorToken": "Το διακριτικ<PERSON> ασφαλείας εγγράφου δεν έχει σχηματιστεί σωστά.<br>Παρακαλούμε επικοινωνήστε με τον\n διαχειριστή του διακομιστή εγγράφων.", "PE.Controllers.Main.errorTokenExpire": "Το διακριτικ<PERSON> ασφαλείας εγγράφου έχει λήξει.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του διακομιστή εγγράφων.", "PE.Controllers.Main.errorUpdateVersion": "Η έκδοση του αρχείου έχει αλλάξει. Η σελίδα θα φορτωθεί ξανά.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Η σύνδεση στο Διαδίκτυο έχει αποκατασταθεί και η έκδοση του αρχείου έχει αλλάξει.<br>Προτ<PERSON><PERSON> συνεχίσετε να εργάζεστε, πρέπει να κατεβάσετε το αρχείο ή να αντιγράψετε το περιεχόμενό του για να βεβαιωθείτε ότι δεν έχει χαθεί τίποτα και στη συνέχεια φορτώστε ξανά αυτήν τη σελίδα.", "PE.Controllers.Main.errorUserDrop": "Δεν είναι δυνατή η πρόσβαση στο αρχείο αυτή τη στιγμή.", "PE.Controllers.Main.errorUsersExceed": "Υπέρβαση του αριθμού των χρηστών που επιτρέπονται από το πρόγραμμα τιμολόγησης", "PE.Controllers.Main.errorViewerDisconnect": "Η σύνδεση χάθηκε. Μπορείτε να συνεχίσετε να βλέπετε το έγγραφο,<br>αλλά δεν θα μπορείτε να το λάβετε ή να το εκτυπώσετε έως ότου αποκατασταθεί η σύνδεση και ανανεωθεί η σελίδα.", "PE.Controllers.Main.leavePageText": "Υπάρχουν αλλαγές που δεν έχουν αποθηκευτεί σε αυτή τη διαφάνεια. Κάντε κλικ στο \"Παραμονή στη Σελίδα\" και μετά \"Αποθήκευση\" για να τις αποθηκεύσετε. Κάντε κλικ στο \"Έξοδος από τη Σελίδα\" για να απορρίψετε όλες τις μη αποθηκευμένες αλλαγές.", "PE.Controllers.Main.leavePageTextOnClose": "Όλες οι μη αποθηκευμένες αλλαγές σε αυτή τη παρουσίαση θα χαθούν.<br><PERSON><PERSON><PERSON><PERSON><PERSON> κλικ στο «Ακύρωση» και στη συνέχεια στο «Αποθήκευση» για να τις αποθηκεύσετε. Κάντε κλικ στο «Εντάξει» για να απορρίψετε όλες τις μη αποθηκευμένες αλλαγές.", "PE.Controllers.Main.loadFontsTextText": "Φόρτωση δεδομένων...", "PE.Controllers.Main.loadFontsTitleText": "Φόρτωση Δεδομένων", "PE.Controllers.Main.loadFontTextText": "Φόρτωση δεδομένων...", "PE.Controllers.Main.loadFontTitleText": "Φόρτωση Δεδομένων", "PE.Controllers.Main.loadImagesTextText": "Φόρτωση εικόνων...", "PE.Controllers.Main.loadImagesTitleText": "Φόρτωση Εικόνων", "PE.Controllers.Main.loadImageTextText": "Φόρτωση εικόνας...", "PE.Controllers.Main.loadImageTitleText": "Φόρτωση Εικόνας", "PE.Controllers.Main.loadingDocumentTextText": "Φόρτωση παρουσίασης...", "PE.Controllers.Main.loadingDocumentTitleText": "Φόρτωση παρουσίασης", "PE.Controllers.Main.loadThemeTextText": "Φόρτωση θέματος...", "PE.Controllers.Main.loadThemeTitleText": "Φόρτωση Θέματος", "PE.Controllers.Main.notcriticalErrorTitle": "Προειδοποίηση", "PE.Controllers.Main.openErrorText": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά το άνοιγμα του αρχείου.", "PE.Controllers.Main.openTextText": "Άνοιγμα παρουσίασης...", "PE.Controllers.Main.openTitleText": "Άνοιγμα Παρουσίασης", "PE.Controllers.Main.printTextText": "Εκτύπωση παρουσίασης...", "PE.Controllers.Main.printTitleText": "Εκτύπωση Παρουσίασης", "PE.Controllers.Main.reloadButtonText": "Επαναφόρτωση Σελίδας", "PE.Controllers.Main.requestEditFailedMessageText": "Κ<PERSON><PERSON><PERSON><PERSON>ος επεξ<PERSON>ργ<PERSON>ζεται την παρουσίαση αυτή τη στιγμή. Παρακαλούμε δοκιμάστε ξανά αργότερα.", "PE.Controllers.Main.requestEditFailedTitleText": "Δεν επιτρέπεται η πρόσβαση", "PE.Controllers.Main.saveErrorText": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON>ε σφάλμα κατά την αποθήκευση του αρχείου.", "PE.Controllers.Main.saveErrorTextDesktop": "Δεν είναι δυνατή η αποθήκευση ή η δημιουργία αυτού του αρχείου.<br>Πιθανοί λόγοι είναι:<br>1. Το αρχείο είναι μόνο για ανάγνωση.<br>2. Το αρχείο τελεί υπό επεξεργασία από άλλους χρήστες.<br>3. Ο δίσκος είναι γεμάτος ή κατεστραμμένος.", "PE.Controllers.Main.saveTextText": "Αποθήκευση παρουσίασης...", "PE.Controllers.Main.saveTitleText": "Αποθήκευση Παρουσίασης", "PE.Controllers.Main.scriptLoadError": "Η σύνδεση είναι πολύ αργή, δεν ήταν δυνατή η φόρτωση ορισμένων στοιχείων. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "PE.Controllers.Main.splitDividerErrorText": "Ο αριθμός των γραμμών πρέπει να είναι διαιρέτης του %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Ο αριθμός των στηλών πρέπει να είναι μικρότερος από %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Ο αριθμός των γραμμών πρέπει να είναι μικρότερος από %1.", "PE.Controllers.Main.textAnonymous": "Ανώνυμος", "PE.Controllers.Main.textApplyAll": "Εφαρμογή σε όλες τις εξισώσεις", "PE.Controllers.Main.textBuyNow": "Επισκεφθείτε την ιστοσελίδα", "PE.Controllers.Main.textChangesSaved": "Όλες οι αλλαγές αποθηκεύτηκαν", "PE.Controllers.Main.textClose": "Κλείσιμο", "PE.Controllers.Main.textCloseTip": "Κάντε κλικ για να κλείσει η υπόδειξη", "PE.Controllers.Main.textContactUs": "Επικοινωνήστε με το τμήμα πωλήσεων", "PE.Controllers.Main.textConvertEquation": "Η εξίσωση αυτή δημιουργήθηκε με παλαιότερη έκδοση του συντάκτη εξισώσεων που δεν υποστηρίζεται πια. Για να την επεξεργαστείτε, μετατρέψτε την σε μορφή Office Math ML.<br>Να μετατραπεί τώρα;", "PE.Controllers.Main.textCustomLoader": "Παρακαλούμε λάβετε υπόψη ότι σύμφωνα με τους όρους της άδειας δεν δικαιούστε αλλαγή του φορτωτή.<br>Π<PERSON><PERSON><PERSON><PERSON><PERSON>λούμε επικοινωνήστε με το Τμήμα Πωλήσεων για να λάβετε μια προσφορά.", "PE.Controllers.Main.textDisconnect": "Η σύνδεση χάθηκε", "PE.Controllers.Main.textGuest": "Επισκέπτης", "PE.Controllers.Main.textHasMacros": "Το αρχείο περιέχει αυτόματες μακροεντολές.<br>Θέλετε να εκτελέσετε μακροεντολές;", "PE.Controllers.Main.textLearnMore": "Μάθετε περισσότερα", "PE.Controllers.Main.textLoadingDocument": "Φόρτωση παρουσίασης", "PE.Controllers.Main.textLongName": "Εισάγετε ένα όνομα μικρότερο από 128 χαρακτήρες.", "PE.Controllers.Main.textNoLicenseTitle": "Το όριο άδειας συμπληρώθηκε.", "PE.Controllers.Main.textPaidFeature": "Δυνατότητα επί πληρωμή", "PE.Controllers.Main.textReconnect": "Η σύνδεση αποκαταστάθηκε", "PE.Controllers.Main.textRemember": "Να θυμάσαι την επιλογή μου για όλα τα αρχεία", "PE.Controllers.Main.textRememberMacros": "Θυμηθείτε την επιλογή μου για όλες τις μακροεντολές", "PE.Controllers.Main.textRenameError": "Το όνομα χρήστη δεν μπορεί να είναι κενό.", "PE.Controllers.Main.textRenameLabel": "Εισάγετε ένα όνομα για συνεργατική χρήση", "PE.Controllers.Main.textRequestMacros": "Μια μακροεντολή κάνει ένα αίτημα στη διεύθυνση URL. Θέλετε να επιτρέψετε το αίτημα στο %1;", "PE.Controllers.Main.textShape": "Σχήμα", "PE.Controllers.Main.textStrict": "Αυστηρή κατάσταση", "PE.Controllers.Main.textText": "Κείμενο", "PE.Controllers.Main.textTryUndoRedo": "Οι λειτουργίες Αναίρεση/Επανάληψη είναι απενεργοποιημένες στην κατάσταση Γρήγορης συν-επεξεργασίας.<br>Κάντε κλικ στο κουμπί 'Αυστηρή κατάσταση' για να μεταβείτε στην Αυστηρή κατάσταση συν-επεξεργασίας όπου επεξεργάζεστε το αρχείο χωρίς παρέμβαση άλλων χρηστών και στέλνετε τις αλλαγές σας αφού τις αποθηκεύσετε. Η μετάβαση μεταξύ των δύο καταστάσεων γίνεται μέσω των Προηγμένων Ρυθμίσεων.", "PE.Controllers.Main.textTryUndoRedoWarn": "Οι λειτουργ<PERSON>ες Αναίρεση/Επανάληψη απενεργοποιούνται για τη λειτουργία γρήγορης συν-επεξεργασίας.", "PE.Controllers.Main.titleLicenseExp": "Η άδεια έληξε", "PE.Controllers.Main.titleServerVersion": "Ο συντάκτης ενημερώθηκε", "PE.Controllers.Main.txtAddFirstSlide": "Κάντε κλικ για να προσθέσετε την πρώτη διαφάνεια", "PE.Controllers.Main.txtAddNotes": "Κάντε κλικ για προσθήκη σημειώσεων", "PE.Controllers.Main.txtArt": "Το κείμενό σας εδώ", "PE.Controllers.Main.txtBasicShapes": "Βασικά <PERSON>χήματα", "PE.Controllers.Main.txtButtons": "Κουμπιά", "PE.Controllers.Main.txtCallouts": "Επεξηγήσεις", "PE.Controllers.Main.txtCharts": "Γραφήματα", "PE.Controllers.Main.txtClipArt": "Εικονίδιο", "PE.Controllers.Main.txtDateTime": "Ημερομηνία και ώρα", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>τος", "PE.Controllers.Main.txtEditingMode": "Ορισμ<PERSON>ς κατάστασης επεξεργασίας...", "PE.Controllers.Main.txtErrorLoadHistory": "Η φόρτωση ιστορικού απέτυχε", "PE.Controllers.Main.txtFiguredArrows": "Σχηματικ<PERSON> Βέλη", "PE.Controllers.Main.txtFooter": "Υποσέλιδο", "PE.Controllers.Main.txtHeader": "Κεφαλίδα", "PE.Controllers.Main.txtImage": "Εικόνα", "PE.Controllers.Main.txtLines": "Γραμμές", "PE.Controllers.Main.txtLoading": "Φόρτωση...", "PE.Controllers.Main.txtMath": "Μαθηματικά", "PE.Controllers.Main.txtMedia": "Μέσα", "PE.Controllers.Main.txtNeedSynchronize": "Έχετε ενημερώσεις", "PE.Controllers.Main.txtNone": "Κανένα", "PE.Controllers.Main.txtPicture": "Εικόνα", "PE.Controllers.Main.txtRectangles": "Ορθογώνια Παραλληλόγραμμα", "PE.Controllers.Main.txtSeries": "Σειρά", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Επεξήγηση με Γραμμή 1 (Περίγραμμα και Μπάρα)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Επεξήγηση με Γραμμή 2 (Περίγραμμα και Μπάρα)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Επεξήγηση με Γραμμή 3 (Περίγραμμα και Μπάρα)", "PE.Controllers.Main.txtShape_accentCallout1": "Επεξήγηση με Γραμμή 1 (Μπάρα)", "PE.Controllers.Main.txtShape_accentCallout2": "Επεξήγηση με Γραμμή 2 (Μπάρ<PERSON>)", "PE.Controllers.Main.txtShape_accentCallout3": "Επεξήγηση με Γραμμή 3 (Μπάρα)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Κουμπί Πίσω ή Προηγούμενο", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Κουμπί Αρχής", "PE.Controllers.Main.txtShape_actionButtonBlank": "Κενό Κουμπί", "PE.Controllers.Main.txtShape_actionButtonDocument": "Κουμπί Εγγράφου", "PE.Controllers.Main.txtShape_actionButtonEnd": "Κουμπί Τέλους", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Κουμπί Μπροστά ή Επόμενο", "PE.Controllers.Main.txtShape_actionButtonHelp": "Κουμπί Βοήθειας", "PE.Controllers.Main.txtShape_actionButtonHome": "Κουμπί Αρχικής", "PE.Controllers.Main.txtShape_actionButtonInformation": "Κουμπί Πληροφορίας", "PE.Controllers.Main.txtShape_actionButtonMovie": "Κουμπί Ταινίας", "PE.Controllers.Main.txtShape_actionButtonReturn": "Κουμπί Επιστροφής", "PE.Controllers.Main.txtShape_actionButtonSound": "Κουμπί Ήχου", "PE.Controllers.Main.txtShape_arc": "Κυκλικό Τόξο", "PE.Controllers.Main.txtShape_bentArrow": "Λυγισ<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Αρθρωτός <PERSON>ύνδεσμος", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Αρθρωτ<PERSON><PERSON>ύνδεσμος με Βέλος", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Αρθρωτ<PERSON><PERSON>ύνδεσμος με Διπλό Βέλος", "PE.Controllers.Main.txtShape_bentUpArrow": "Λυγισμ<PERSON><PERSON><PERSON> Βέλος", "PE.Controllers.Main.txtShape_bevel": "Πλάγια Τομή", "PE.Controllers.Main.txtShape_blockArc": "Πλα<PERSON><PERSON><PERSON><PERSON>ου", "PE.Controllers.Main.txtShape_borderCallout1": "Επεξήγηση με Γραμμή 1", "PE.Controllers.Main.txtShape_borderCallout2": "Επεξήγηση με Γραμμή 2", "PE.Controllers.Main.txtShape_borderCallout3": "Επεξήγηση με Γραμμή 3", "PE.Controllers.Main.txtShape_bracePair": "Διπλό Άγκιστρο", "PE.Controllers.Main.txtShape_callout1": "Επεξήγηση με Γραμμή 1 (<PERSON><PERSON><PERSON><PERSON><PERSON>ρίγραμμα)", "PE.Controllers.Main.txtShape_callout2": "Επεξήγηση με Γραμμή 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>ίγραμμα)", "PE.Controllers.Main.txtShape_callout3": "Επεξήγηση με Γραμμή 3 (<PERSON><PERSON><PERSON><PERSON><PERSON>ρίγραμμα)", "PE.Controllers.Main.txtShape_can": "Κύλινδρος", "PE.Controllers.Main.txtShape_chevron": "Σιρίτι", "PE.Controllers.Main.txtShape_chord": "Χορ<PERSON><PERSON> Κύκλου", "PE.Controllers.Main.txtShape_circularArrow": "Κυκλικ<PERSON>έλ<PERSON>", "PE.Controllers.Main.txtShape_cloud": "Σύννεφο", "PE.Controllers.Main.txtShape_cloudCallout": "Επεξήγηση σε Σύννεφο", "PE.Controllers.Main.txtShape_corner": "Γωνία", "PE.Controllers.Main.txtShape_cube": "Κύβος", "PE.Controllers.Main.txtShape_curvedConnector3": "Καμπυλω<PERSON><PERSON>ς <PERSON>ύνδεσμος", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Καμπυ<PERSON>ω<PERSON><PERSON><PERSON> Σύνδεσμος με Βέλος", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Καμπυλω<PERSON><PERSON><PERSON> Σύνδεσμος με Διπλό Βέλος", "PE.Controllers.Main.txtShape_curvedDownArrow": "Καμπυλωτ<PERSON> Κάτω Βέλος", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Καμπυλωτό Αριστερό Βέλος", "PE.Controllers.Main.txtShape_curvedRightArrow": "Καμπυλωτό Δεξί Βέλος", "PE.Controllers.Main.txtShape_curvedUpArrow": "Καμπυλωτό Πάνω Βέλος", "PE.Controllers.Main.txtShape_decagon": "Δεκάγωνο", "PE.Controllers.Main.txtShape_diagStripe": "Διαγώνια Λωρίδα", "PE.Controllers.Main.txtShape_diamond": "Ρόμβος", "PE.Controllers.Main.txtShape_dodecagon": "Δωδεκάγωνο", "PE.Controllers.Main.txtShape_donut": "Ντ<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "Διπλό Κύμα", "PE.Controllers.Main.txtShape_downArrow": "Κάτω Βέλος", "PE.Controllers.Main.txtShape_downArrowCallout": "Επεξήγηση με Κάτω Βέλος", "PE.Controllers.Main.txtShape_ellipse": "Έλλειψη", "PE.Controllers.Main.txtShape_ellipseRibbon": "Καμπυλωτ<PERSON> Κ<PERSON>τω <PERSON>δ<PERSON>λα", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Καμπυλωτή Πάνω Κ<PERSON>ρδ<PERSON>λα", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Διάγραμμα Ροής: Εναλλακτική Διεργασία", "PE.Controllers.Main.txtShape_flowChartCollate": "Διάγραμμα Ροής: Τοποθέτηση σε Σειρά", "PE.Controllers.Main.txtShape_flowChartConnector": "Διάγραμμα Ροής: Σύνδεσμος", "PE.Controllers.Main.txtShape_flowChartDecision": "Διάγραμμα Ροής: Απόφαση", "PE.Controllers.Main.txtShape_flowChartDelay": "Διάγραμμα Ροής: Καθυστέρηση", "PE.Controllers.Main.txtShape_flowChartDisplay": "Διάγραμμα Ροής: Προβολή", "PE.Controllers.Main.txtShape_flowChartDocument": "Διάγραμμα Ροής: Έγγραφο", "PE.Controllers.Main.txtShape_flowChartExtract": "Διάγραμμα Ροής: Εξαγωγή", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Διάγραμμα Ροής: Δεδομένα", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Διάγραμμα Ροής: Εσωτε<PERSON>ικ<PERSON>ς Αποθηκευτικ<PERSON>ς Χώρος", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Διάγραμμα Ροής: Μαγνητικ<PERSON>ς Δίσκος", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Διάγραμμα Ροής: Αποθηκευτικό Μέσο Άμεσης Πρόσβασης", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Διάγραμμα Ροής: Αποθηκευτικό Μέσο Σειριακής Προσπέλασης", "PE.Controllers.Main.txtShape_flowChartManualInput": "Διάγραμμα Ροής: Χ<PERSON><PERSON>ροκίνητη Εισαγωγή", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Διάγραμμα Ροής: Χ<PERSON><PERSON>ροκίνητη Λειτουργία", "PE.Controllers.Main.txtShape_flowChartMerge": "Διάγραμμα Ροής: Συγχώνευση", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Διάγραμμα Ροής: Πολλαπλό Έγγραφο", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Διάγραμμα Ροής: Σύνδεσμος Εκτός Σελίδας", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Διάγραμμα Ροής: Αποθηκευμένα Δεδομένα", "PE.Controllers.Main.txtShape_flowChartOr": "Διάγραμμα Ροής: Ή", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Διάγραμμα Ροής: Προκαθορισμένη Διεργασία", "PE.Controllers.Main.txtShape_flowChartPreparation": "Διάγραμμα Ροής: Προετοιμασία", "PE.Controllers.Main.txtShape_flowChartProcess": "Διάγραμμα Ροής: Διεργασία", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Διάγραμμα Ροής: Κάρτα", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Διάγραμμα Ροής: Διάτρητη Ταινία", "PE.Controllers.Main.txtShape_flowChartSort": "Διάγραμμα Ροής: Ταξινόμηση", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Διάγραμμα Ροής: Συμβολή", "PE.Controllers.Main.txtShape_flowChartTerminator": "Διάγραμμα Ροής: Τερματισμός", "PE.Controllers.Main.txtShape_foldedCorner": "Διπλωμένη Γωνία", "PE.Controllers.Main.txtShape_frame": "Πλαίσιο", "PE.Controllers.Main.txtShape_halfFrame": "Μισό Πλαίσιο", "PE.Controllers.Main.txtShape_heart": "Καρδιά", "PE.Controllers.Main.txtShape_heptagon": "Επτάγωνο", "PE.Controllers.Main.txtShape_hexagon": "Εξάγωνο", "PE.Controllers.Main.txtShape_homePlate": "Πεντάγωνο", "PE.Controllers.Main.txtShape_horizontalScroll": "Οριζόντια Κύλιση", "PE.Controllers.Main.txtShape_irregularSeal1": "Έκρηξη 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Έκρηξη 2", "PE.Controllers.Main.txtShape_leftArrow": "Αριστερ<PERSON>έλ<PERSON>", "PE.Controllers.Main.txtShape_leftArrowCallout": "Επεξήγηση με Αριστερό Βέλος", "PE.Controllers.Main.txtShape_leftBrace": "Αριστερό Άγκιστρο", "PE.Controllers.Main.txtShape_leftBracket": "Αριστερή Παρένθεση", "PE.Controllers.Main.txtShape_leftRightArrow": "Αριστερό Δεξιό Βέλος", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Επεξήγηση με Αριστερό Δεξιό Βέλος", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Αριστερό Δεξιό Πάνω Βέλος", "PE.Controllers.Main.txtShape_leftUpArrow": "Αριστερ<PERSON> Πάνω Βέλος", "PE.Controllers.Main.txtShape_lightningBolt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "PE.Controllers.Main.txtShape_line": "Γραμμή", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Διπλ<PERSON>", "PE.Controllers.Main.txtShape_mathDivide": "Δια", "PE.Controllers.Main.txtShape_mathEqual": "Ίσον", "PE.Controllers.Main.txtShape_mathMinus": "Πλην", "PE.Controllers.Main.txtShape_mathMultiply": "Επί", "PE.Controllers.Main.txtShape_mathNotEqual": "Διάφορο", "PE.Controllers.Main.txtShape_mathPlus": "Συν", "PE.Controllers.Main.txtShape_moon": "Φεγγάρι", "PE.Controllers.Main.txtShape_noSmoking": "Σύμβολο \"Όχι\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Δεξί Βέλος με Εγκοπή", "PE.Controllers.Main.txtShape_octagon": "Οκτάγωνο", "PE.Controllers.Main.txtShape_parallelogram": "Παραλληλόγραμμο", "PE.Controllers.Main.txtShape_pentagon": "Πεντάγωνο", "PE.Controllers.Main.txtShape_pie": "Πίτα", "PE.Controllers.Main.txtShape_plaque": "Σύμβολο", "PE.Controllers.Main.txtShape_plus": "Συν", "PE.Controllers.Main.txtShape_polyline1": "Μουντζού<PERSON>α", "PE.Controllers.Main.txtShape_polyline2": "Ελεύθερο Σχέδιο", "PE.Controllers.Main.txtShape_quadArrow": "Τετραπλ<PERSON> Βέλος", "PE.Controllers.Main.txtShape_quadArrowCallout": "Επεξήγηση Τετραπλού Βέλους", "PE.Controllers.Main.txtShape_rect": "Ορθ<PERSON>γ<PERSON><PERSON><PERSON><PERSON>αλληλόγραμμο", "PE.Controllers.Main.txtShape_ribbon": "Κάτ<PERSON>λα", "PE.Controllers.Main.txtShape_ribbon2": "Πάνω <PERSON>δ<PERSON>λα", "PE.Controllers.Main.txtShape_rightArrow": "Δεξιό Βέλος", "PE.Controllers.Main.txtShape_rightArrowCallout": "Επεξήγηση με Δεξιό Βέλος", "PE.Controllers.Main.txtShape_rightBrace": "Δεξιό Άγκιστρο", "PE.Controllers.Main.txtShape_rightBracket": "Δεξιά Παρένθεση", "PE.Controllers.Main.txtShape_round1Rect": "Με Στρογγυλεμένη Γωνία", "PE.Controllers.Main.txtShape_round2DiagRect": "Με Διαγώνιες Στρογγυλεμένες Γω<PERSON>ς", "PE.Controllers.Main.txtShape_round2SameRect": "Με Στρογγυλεμένες Γωνίες στην Ίδια Πλευρά", "PE.Controllers.Main.txtShape_roundRect": "Με Στρογγυλεμένες Γωνίες", "PE.Controllers.Main.txtShape_rtTriangle": "Ορθογώνιο Τρίγωνο", "PE.Controllers.Main.txtShape_smileyFace": "Χαμογ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Φατσούλα", "PE.Controllers.Main.txtShape_snip1Rect": "Με Ψαλιδισμένη Γωνία", "PE.Controllers.Main.txtShape_snip2DiagRect": "Με Διαγώνιες Ψαλιδισμένες <PERSON>ω<PERSON>ς", "PE.Controllers.Main.txtShape_snip2SameRect": "Με Ψαλιδισμένες Γωνίες στην Ίδια Πλευρά", "PE.Controllers.Main.txtShape_snipRoundRect": "Με Στρογγυλεμένη και Ψαλιδισμένη Γωνία", "PE.Controllers.Main.txtShape_spline": "Καμπύλη", "PE.Controllers.Main.txtShape_star10": "Αστέρι 10 Σημείων", "PE.Controllers.Main.txtShape_star12": "Αστέρι 12 Σημείων", "PE.Controllers.Main.txtShape_star16": "Αστέρι 16 Σημείων", "PE.Controllers.Main.txtShape_star24": "Αστέρι 24 Σημείων", "PE.Controllers.Main.txtShape_star32": "Αστέρι 32 Σημείων", "PE.Controllers.Main.txtShape_star4": "Αστέρι 4 Σημείων", "PE.Controllers.Main.txtShape_star5": "Αστέρι 5 Σημείων", "PE.Controllers.Main.txtShape_star6": "Αστέρι 6 Σημείων", "PE.Controllers.Main.txtShape_star7": "Αστέρι 7 Σημείων", "PE.Controllers.Main.txtShape_star8": "Αστέρι 8 Σημείων", "PE.Controllers.Main.txtShape_stripedRightArrow": "Ριγέ Δεξιό Βέλος", "PE.Controllers.Main.txtShape_sun": "Ήλιος", "PE.Controllers.Main.txtShape_teardrop": "Δά<PERSON>ρυ", "PE.Controllers.Main.txtShape_textRect": "Πλ<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON>νου", "PE.Controllers.Main.txtShape_trapezoid": "Τραπέζιο", "PE.Controllers.Main.txtShape_triangle": "Τρίγωνο", "PE.Controllers.Main.txtShape_upArrow": "Πάν<PERSON> Βέλ<PERSON>", "PE.Controllers.Main.txtShape_upArrowCallout": "Επεξήγηση με Πάνω Βέλος", "PE.Controllers.Main.txtShape_upDownArrow": "Πάνω Κάτω Β<PERSON>λος", "PE.Controllers.Main.txtShape_uturnArrow": "<PERSON><PERSON><PERSON><PERSON>αστροφής", "PE.Controllers.Main.txtShape_verticalScroll": "Κατακόρυφη Κύλιση", "PE.Controllers.Main.txtShape_wave": "Κύμα", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Οβάλ Επεξήγηση", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Ορθογώνια Επεξήγηση", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Στρογγυλεμένη Ορθογώνια Επεξήγηση", "PE.Controllers.Main.txtSldLtTBlank": "Κενό", "PE.Controllers.Main.txtSldLtTChart": "Γράφημα", "PE.Controllers.Main.txtSldLtTChartAndTx": "Γράφημα και Κείμενο", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Εικον<PERSON><PERSON>ι<PERSON> και Κείμενο", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Εικ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κα<PERSON> Κατακόρυφο Κείμενο", "PE.Controllers.Main.txtSldLtTCust": "Προσαρμοσμένο", "PE.Controllers.Main.txtSldLtTDgm": "Διάγραμμα", "PE.Controllers.Main.txtSldLtTFourObj": "Τέσσερα Αντικείμενα", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Μέσα και Κείμενο", "PE.Controllers.Main.txtSldLtTObj": "<PERSON><PERSON><PERSON><PERSON><PERSON> και Αντικείμενο", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Αντικείμενο και Δυο αντικείμενα", "PE.Controllers.Main.txtSldLtTObjAndTx": "Αντικείμενο και Κείμενο", "PE.Controllers.Main.txtSldLtTObjOnly": "Αντικείμενο", "PE.Controllers.Main.txtSldLtTObjOverTx": "Αντικείμενο πάνω από Κείμενο", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Αντικ<PERSON>ίμενο και Λεζάντα", "PE.Controllers.Main.txtSldLtTPicTx": "Εικόνα και Λεζάντα", "PE.Controllers.Main.txtSldLtTSecHead": "Κεφαλίδ<PERSON> Τμήματος", "PE.Controllers.Main.txtSldLtTTbl": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "PE.Controllers.Main.txtSldLtTTitle": "Τίτλος", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON>ν<PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Κείμενο <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObj": "Δύο Αντικείμενα", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Δυο Αντικείμενα και Αντικείμενο", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Δυο Αντικείμενα και Κείμενο", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Δυο Αντικείμενα πάνω από Κείμενο", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Δυ<PERSON>είμενα και Δυο Αντικείμενα", "PE.Controllers.Main.txtSldLtTTx": "Κείμενο", "PE.Controllers.Main.txtSldLtTTxAndChart": "Κείμενο και Γράφημα", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Κείμενο και Εικονίδιο", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Κείμενο και Πολυμέσα", "PE.Controllers.Main.txtSldLtTTxAndObj": "Κείμενο και Αντικείμενο", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Κείμενο και Δυο Αντικείμενα", "PE.Controllers.Main.txtSldLtTTxOverObj": "Κείμενο <PERSON>ω από Αντικείμενο", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Τ<PERSON><PERSON><PERSON>ος και Κείμενο", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Τ<PERSON><PERSON><PERSON>ος και Κείμενο <PERSON>νω από το Γράφημα", "PE.Controllers.Main.txtSldLtTVertTx": "Κατακόρυ<PERSON><PERSON> Κείμενο", "PE.Controllers.Main.txtSlideNumber": "Αριθμός διαφάνειας", "PE.Controllers.Main.txtSlideSubtitle": "Υπότιτλος διαφάνειας", "PE.Controllers.Main.txtSlideText": "Κείμενο διαφάνειας", "PE.Controllers.Main.txtSlideTitle": "Τίτλος διαφάνειας", "PE.Controllers.Main.txtStarsRibbons": "Αστέρια & Κορδέλες", "PE.Controllers.Main.txtTheme_basic": "Βασικό", "PE.Controllers.Main.txtTheme_blank": "Κενό", "PE.Controllers.Main.txtTheme_classic": "Κλασσικό", "PE.Controllers.Main.txtTheme_corner": "Γωνία", "PE.Controllers.Main.txtTheme_dotted": "Με τελείες", "PE.Controllers.Main.txtTheme_green": "Πράσινο", "PE.Controllers.Main.txtTheme_green_leaf": "Πράσιν<PERSON> φύλλο", "PE.Controllers.Main.txtTheme_lines": "Γραμμές", "PE.Controllers.Main.txtTheme_office": "Γραφείο", "PE.Controllers.Main.txtTheme_office_theme": "Θέμα Γραφείου", "PE.Controllers.Main.txtTheme_official": "Επίσημο", "PE.Controllers.Main.txtTheme_pixel": "Εικονοστοιχείο", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Χελώνα", "PE.Controllers.Main.txtXAxis": "Άξονας Χ", "PE.Controllers.Main.txtYAxis": "Άξονας Υ", "PE.Controllers.Main.unknownErrorText": "Άγνωστο σφάλμα.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Ο περιηγητής σας δεν υποστηρίζεται.", "PE.Controllers.Main.uploadImageExtMessage": "Άγνωστη μορφή εικόνας.", "PE.Controllers.Main.uploadImageFileCountMessage": "Δεν μεταφορτώθηκαν εικόνες.", "PE.Controllers.Main.uploadImageSizeMessage": "Η εικόνα είναι πολύ μεγάλη. Το μέγιστο μέγεθος είναι 25MB.", "PE.Controllers.Main.uploadImageTextText": "Μεταφόρτωση εικόνας...", "PE.Controllers.Main.uploadImageTitleText": "Μεταφόρτωση Εικόνας", "PE.Controllers.Main.waitText": "Παρακαλούμε, περιμένετε...", "PE.Controllers.Main.warnBrowserIE9": "Η εφαρμογή έχει πενιχρές δυνατότητες στον IE9. Δοκιμάστε IE10 ή νεώτερο.", "PE.Controllers.Main.warnBrowserZoom": "Η τρέχουσα ρύθμιση εστίασης του περιηγητή σας δεν υποστηρίζεται πλήρως. Παρακαλούμε επιστρέψτε στην προεπιλεγμένη εστίαση πατώντας Ctrl+0.", "PE.Controllers.Main.warnLicenseExceeded": "Έχετε φτάσει το όριο για ταυτόχρονες συνδέσεις με συντάκτες %1. Αυτό το έγγραφο θα ανοιχτεί μόνο για προβολή.​​<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή σας για περισσότερες πληροφορίες.", "PE.Controllers.Main.warnLicenseExp": "Η άδειά σας έχει λήξει.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>με ενημερώστε την άδεια χρήσης σας και ανανεώστε τη σελίδα.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Η άδεια έληξε.<br>Δεν έχετε πρόσβαση στη δυνατότητα επεξεργασίας εγγράφων.<br>Ε<PERSON><PERSON><PERSON><PERSON><PERSON>νωνήστε με τον διαχειριστή σας.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Η άδεια πρέπει να ανανεωθεί.<br>Έχετε περιορισμένη πρόσβαση στη λειτουργία επεξεργασίας εγγράφων.<br>Ε<PERSON><PERSON><PERSON><PERSON><PERSON>νωνήστε με τον διαχειριστή σας για πλήρη πρόσβαση", "PE.Controllers.Main.warnLicenseUsersExceeded": "Έχετε φτάσει το όριο χρήστη για συντάκτες %1.<br>Επ<PERSON>κ<PERSON><PERSON>νωνήστε με τον διαχειριστή σας για περισσότερες πληροφορίες.", "PE.Controllers.Main.warnNoLicense": "Έχετε φτάσει το όριο για ταυτόχρονες συνδέσεις με %1 επεξεργαστές. Αυτό το έγγραφο θα ανοίχτεί μόνο για προβολή.​​<br>Παρακαλούμε επικοινωνήστε με την ομάδα πωλήσεων %1 για προσωπικούς όρους αναβάθμισης.", "PE.Controllers.Main.warnNoLicenseUsers": "Έχετε φτάσει το όριο χρήστη για συντάκτες %1.<br>Ε<PERSON><PERSON><PERSON><PERSON><PERSON>νωνήστε με την ομάδα πωλήσεων %1 για προσωπικούς όρους αναβάθμισης.", "PE.Controllers.Main.warnProcessRightsChange": "Σας έχει απαγορευτεί το δικαίωμα επεξεργασίας του αρχείου.", "PE.Controllers.Search.notcriticalErrorTitle": "Προειδοποίηση", "PE.Controllers.Search.warnReplaceString": "{0} δεν είν<PERSON><PERSON> έ<PERSON>ας έγκυρος ειδικός χαρακτήρας για το πλαίσιο Αντικατάσταση με.", "PE.Controllers.Statusbar.textDisconnect": "<b>Η σύνδεση χάθηκε</b><br>Απ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επανασύνδεσης. Παρακαλούμε, ελέγξτε τις ρυθμίσεις σύνδεσης.", "PE.Controllers.Statusbar.zoomText": "Εστίαση {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Η γραμματοσειρά που επιχειρείτε να αποθηκεύσετε δεν είναι διαθέσιμη στην τρέχουσα συσκευή.<br>Η τεχνοτροπία κειμένου θα εμφανιστεί με μια από τις γραμματοσειρές συστήματος, η αποθηκευμένη γραμματοσειρά θα χρησιμοποιηθεί όταν γίνει διαθέσιμη.<br>Θέλετε να συνεχίσετε;", "PE.Controllers.Toolbar.textAccent": "Τόνοι/Πνεύματα", "PE.Controllers.Toolbar.textBracket": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.textEmptyImgUrl": "Πρέπει να καθορίσετε τη διεύθυνση URL της εικόνας.", "PE.Controllers.Toolbar.textFontSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 1 και 300", "PE.Controllers.Toolbar.textFraction": "Κλάσματα", "PE.Controllers.Toolbar.textFunction": "Λειτουργ<PERSON>ες", "PE.Controllers.Toolbar.textInsert": "Εισαγωγή", "PE.Controllers.Toolbar.textIntegral": "Ολοκληρώματα", "PE.Controllers.Toolbar.textLargeOperator": "Μεγάλοι Τελεστές", "PE.Controllers.Toolbar.textLimitAndLog": "Όρια και Λογάριθμοι", "PE.Controllers.Toolbar.textMatrix": "Πίνακ<PERSON>ς", "PE.Controllers.Toolbar.textOperator": "Τελεστές", "PE.Controllers.Toolbar.textRadical": "Ρίζες", "PE.Controllers.Toolbar.textScript": "Δέσμες ενεργειών", "PE.Controllers.Toolbar.textSymbols": "Σύμβολα", "PE.Controllers.Toolbar.textWarning": "Προειδοποίηση", "PE.Controllers.Toolbar.txtAccent_Accent": "Οξεία", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Από πάνω βέλος δεξιά-αριστερά", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Από πάνω βέλος προς αριστερά", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Από πάνω βέλος προς τα δεξιά", "PE.Controllers.Toolbar.txtAccent_Bar": "Μπάρα", "PE.Controllers.Toolbar.txtAccent_BarBot": "Κάτω οριζόντια γραμμή", "PE.Controllers.Toolbar.txtAccent_BarTop": "Επάνω οριζόντια γραμμή", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Μαθηματική σχέση εντός πλαισίου (με δέσμευση θέσης)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Μαθηματική σχέση εντός πλαισίου (παράδειγμα)", "PE.Controllers.Toolbar.txtAccent_Check": "Έλεγχος", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Κάτω αγκύλη έκτασης", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Πάνω αγκύλη έκτασης", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Διάνυσμα A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC με οριζόντια γραμμή από πάνω", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y με επάνω οριζόντια γραμμή", "PE.Controllers.Toolbar.txtAccent_DDDot": "Τριπλή τελεία", "PE.Controllers.Toolbar.txtAccent_DDot": "Διπλή τελεία", "PE.Controllers.Toolbar.txtAccent_Dot": "Τελεία", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Διπλή μπάρα από πάνω", "PE.Controllers.Toolbar.txtAccent_Grave": "Βαρεία", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Ομαδοποίηση χαρακτήρα από κάτω", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Ομαδοποίηση χαρακτήρα από πάνω", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Από πάνω καμάκι προς τα αριστερά", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Από πάνω καμάκι προς τα δεξιά", "PE.Controllers.Toolbar.txtAccent_Hat": "Σύμβολο (^)", "PE.Controllers.Toolbar.txtAccent_Smile": "Σημεί<PERSON> βραχέος", "PE.Controllers.Toolbar.txtAccent_Tilde": "Περισπωμένη", "PE.Controllers.Toolbar.txtBracket_Angle": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Αγκύλες με διαχωριστικά", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Αγκύλες με διαχωριστικά", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Curve": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Αγκύλες με διαχωριστικά", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Περιπ<PERSON><PERSON><PERSON><PERSON>ι<PERSON> (δύο συνθήκες)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Περιπτώσεις (τρεις συνθήκες)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Σύνθετο αντικείμενο", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Σύνθετο αντικείμενο", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Παράδειγμα περιπτώσεων", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Διωνυ<PERSON><PERSON><PERSON><PERSON>ς συντελεστής", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Διωνυ<PERSON><PERSON><PERSON><PERSON>ς συντελεστής", "PE.Controllers.Toolbar.txtBracket_Line": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_LineDouble": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_LowLim": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Round": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Αγκύλες με διαχωριστικά", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Square": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_UppLim": "Αγκ<PERSON>λες", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Μονή αγκύλη", "PE.Controllers.Toolbar.txtFractionDiagonal": "Κεκλιμένο κλάσμα", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Διαφορικό", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Διαφορικό", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Διαφορικό", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Διαφορικό", "PE.Controllers.Toolbar.txtFractionHorizontal": "Γραμμικό κλάσμα", "PE.Controllers.Toolbar.txtFractionPi_2": "π/2", "PE.Controllers.Toolbar.txtFractionSmall": "Μικρό κλάσμα", "PE.Controllers.Toolbar.txtFractionVertical": "Σύνθετο κλάσμα", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Συνάρτηση αντίστροφου συνημιτόνου", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Συνάρτηση αντίστροφου υπερβολικού συνημιτόνου", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Συνάρτηση αντίστροφης συνεφαπτομένης", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Συνάρτηση αντίστροφης υπερβολικής συνεφαπτομένης", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Συνάρτηση αντίστροφης συντέμνουσας", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Συνάρτηση αντίστροφης υπερβολικής συντέμνουσας", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Συνάρτηση αντίστροφης τέμνουσας", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Συνάρτηση αντίστροφης υπερβολικής τέμνουσας", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Συνάρτηση αντίστροφου ημίτονου", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Συνάρτηση αντίστροφου υπερβολικού ημίτονου", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Συνάρτηση αντίστροφης εφαπτομένης", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Συνάρτηση αντίστροφης υπερβολικής εφαπτομένης", "PE.Controllers.Toolbar.txtFunction_Cos": "Συνάρτηση συνημίτονου", "PE.Controllers.Toolbar.txtFunction_Cosh": "Συνάρτηση υπερβολικού συνημιτόνου", "PE.Controllers.Toolbar.txtFunction_Cot": "Συνάρτηση συνεφαπτομένης", "PE.Controllers.Toolbar.txtFunction_Coth": "Συνάρτηση υπερβολικής συνεφαπτομένης", "PE.Controllers.Toolbar.txtFunction_Csc": "Συνάρτηση συντέμνουσας", "PE.Controllers.Toolbar.txtFunction_Csch": "Συνάρτηση υπερβολικής συντέμνουσας", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Ημίτονο γωνίας θήτα", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Συνημίτονο 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Τύ<PERSON><PERSON> εφαπτομένης", "PE.Controllers.Toolbar.txtFunction_Sec": "Συνάρτηση τέμνουσας", "PE.Controllers.Toolbar.txtFunction_Sech": "Συνάρτηση υπερβολικής τέμνουσας", "PE.Controllers.Toolbar.txtFunction_Sin": "Συνάρτηση ημίτονου", "PE.Controllers.Toolbar.txtFunction_Sinh": "Συνάρτηση υπερβολικού ημίτονου", "PE.Controllers.Toolbar.txtFunction_Tan": "Συνάρτηση εφαπτομένης", "PE.Controllers.Toolbar.txtFunction_Tanh": "Συνάρτηση υπερβολικής εφαπτομένης", "PE.Controllers.Toolbar.txtIntegral": "Ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Διαφορικ<PERSON> θήτα", "PE.Controllers.Toolbar.txtIntegral_dx": "Διαφορικό x", "PE.Controllers.Toolbar.txtIntegral_dy": "Διαφορικό y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralDouble": "Διπλ<PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Διπλ<PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Διπλ<PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralOriented": "Επικαμ<PERSON>ύ<PERSON>ι<PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Επικαμ<PERSON>ύ<PERSON>ι<PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Επικαμ<PERSON>ύ<PERSON>ι<PERSON> ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Ολοκλήρω<PERSON>α όγκου", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Ολοκλήρω<PERSON>α όγκου", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Ολοκλήρω<PERSON>α όγκου", "PE.Controllers.Toolbar.txtIntegralSubSup": "Ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralTriple": "Τριπλό ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Τριπλό ολοκλήρωμα", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Τριπλό ολοκλήρωμα", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Σύζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Σύζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Σύζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Σύζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Σύζευξη", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Συν-γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Συν-γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Συν-γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Συν-γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Συν-γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Ένωση", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Διάζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Διάζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Διάζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Διάζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Διάζευξη", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Τομή", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Τομή", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Τομή", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Τομή", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Τομή", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Γινόμενο", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Άθροιση", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Ένωση", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Ένωση", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Ένωση", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Ένωση", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Ένωση", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Παράδειγμα ορίου", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Παράδειγμα μέγιστου", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Όριο", "PE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON>υ<PERSON><PERSON><PERSON><PERSON><PERSON>ιθμος", "PE.Controllers.Toolbar.txtLimitLog_Log": "Λογάριθμος", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Λογάριθμος", "PE.Controllers.Toolbar.txtLimitLog_Max": "Μέγιστο", "PE.Controllers.Toolbar.txtLimitLog_Min": "Ελάχιστο", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με αγκύλες", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με αγκύλες", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με αγκύλες", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας με αγκύλες", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 κενός πίνακας", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Τελείες στο κάτω μέρος της γραμμής", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Τελείες στο μέσον της γραμμής", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Διαγώνιες τελείες", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Κατακόρυφες τελείες", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Αραι<PERSON>ς πίνακας", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Αραι<PERSON>ς πίνακας", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 μοναδι<PERSON><PERSON>ος πίνακας", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 μοναδι<PERSON><PERSON>ος πίνακας", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 μονα<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 μονα<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Από κάτω βέλος δεξιά-αριστερά", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Από πάνω βέλος δεξιά-αριστερά", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Από κάτω βέλος προς τα αριστερά", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Από πάνω βέλος προς αριστερά", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Από κάτω βέλος προς τα δεξιά", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Από πάνω βέλος προς τα δεξιά", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Άνω κάτω τελεία ίσον", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Δίνει", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Δέλτα δίνει", "PE.Controllers.Toolbar.txtOperator_Definition": "Ίσον με εξ ορισμού", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Δέλτα ίσο με", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Από κάτω βέλος δεξιά-αριστερά", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Από πάνω βέλος δεξιά-αριστερά", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Από κάτω βέλος προς τα αριστερά", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Από πάνω βέλος προς αριστερά", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Από κάτω βέλος προς τα δεξιά", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Από πάνω βέλος προς τα δεξιά", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Ίσον ίσον", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Πλην ίσον", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Συν ίσον", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Μέτρηση με", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Ρίζα", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Ρίζα", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Τετραγωνική ρίζα με βαθμό", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Κυβική ρίζα", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Ρίζα με βαθμό", "PE.Controllers.Toolbar.txtRadicalSqrt": "Τετραγωνική ρίζα", "PE.Controllers.Toolbar.txtScriptCustom_1": "Δέσμη ενεργειών", "PE.Controllers.Toolbar.txtScriptCustom_2": "Δέσμη ενεργειών", "PE.Controllers.Toolbar.txtScriptCustom_3": "Δέσμη ενεργειών", "PE.Controllers.Toolbar.txtScriptCustom_4": "Δέσμη ενεργειών", "PE.Controllers.Toolbar.txtScriptSub": "Δείκτης", "PE.Controllers.Toolbar.txtScriptSubSup": "Δείκτης-εκθέτης", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Αριστερ<PERSON>ς δείκτης-εκθέτης", "PE.Controllers.Toolbar.txtScriptSup": "Εκθέτης", "PE.Controllers.Toolbar.txtSymbol_about": "Περίπου", "PE.Controllers.Toolbar.txtSymbol_additional": "Συμπλήρωμα", "PE.Controllers.Toolbar.txtSymbol_aleph": "'Aλεφ", "PE.Controllers.Toolbar.txtSymbol_alpha": "Άλφα", "PE.Controllers.Toolbar.txtSymbol_approx": "Σχεδόν ίσο με", "PE.Controllers.Toolbar.txtSymbol_ast": "Τελεστής αστερίσκος", "PE.Controllers.Toolbar.txtSymbol_beta": "Βήτα", "PE.Controllers.Toolbar.txtSymbol_beth": "Στοίχημα", "PE.Controllers.Toolbar.txtSymbol_bullet": "Τελεστής κουκκίδα", "PE.Controllers.Toolbar.txtSymbol_cap": "Τομή", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Κυβική ρίζα", "PE.Controllers.Toolbar.txtSymbol_cdots": "Οριζόντια έλλειψη στο μέσον της γραμμής", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON>αθ<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "Χι", "PE.Controllers.Toolbar.txtSymbol_cong": "Περίπου ίσο με", "PE.Controllers.Toolbar.txtSymbol_cup": "Ένωση", "PE.Controllers.Toolbar.txtSymbol_ddots": "Διαγώνια έλλειψη κάτω δεξιά", "PE.Controllers.Toolbar.txtSymbol_degree": "Βαθμοί", "PE.Controllers.Toolbar.txtSymbol_delta": "Δέλτα", "PE.Controllers.Toolbar.txtSymbol_div": "Σύμβολο διαίρεσης", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Κάτω βέλος", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Κενό σύνολο", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Έψιλον", "PE.Controllers.Toolbar.txtSymbol_equals": "Ίσον", "PE.Controllers.Toolbar.txtSymbol_equiv": "Πανομοιότυπο με", "PE.Controllers.Toolbar.txtSymbol_eta": "Ήτα", "PE.Controllers.Toolbar.txtSymbol_exists": "Υπάρχει", "PE.Controllers.Toolbar.txtSymbol_factorial": "Παραγοντικό", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Βαθμοί Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON>α όλα", "PE.Controllers.Toolbar.txtSymbol_gamma": "Γάμμα", "PE.Controllers.Toolbar.txtSymbol_geq": "Μεγαλύτερο ή ίσο με", "PE.Controllers.Toolbar.txtSymbol_gg": "Πολύ μεγαλύτερο από", "PE.Controllers.Toolbar.txtSymbol_greater": "Μεγαλύτερο από", "PE.Controllers.Toolbar.txtSymbol_in": "Στοιχείο του", "PE.Controllers.Toolbar.txtSymbol_inc": "Αύξηση τιμής", "PE.Controllers.Toolbar.txtSymbol_infinity": "Άπειρο", "PE.Controllers.Toolbar.txtSymbol_iota": "Γιώτα", "PE.Controllers.Toolbar.txtSymbol_kappa": "Κάπα", "PE.Controllers.Toolbar.txtSymbol_lambda": "Λάμδα", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Αριστερ<PERSON> βέλος", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Αριστερό-δεξιό βέλος", "PE.Controllers.Toolbar.txtSymbol_leq": "Μικρότερο ή ίσο με", "PE.Controllers.Toolbar.txtSymbol_less": "Λιγότερο από", "PE.Controllers.Toolbar.txtSymbol_ll": "Πολ<PERSON> μικρότερο από", "PE.Controllers.Toolbar.txtSymbol_minus": "Πλην", "PE.Controllers.Toolbar.txtSymbol_mp": "Πλην συν", "PE.Controllers.Toolbar.txtSymbol_mu": "Μι", "PE.Controllers.Toolbar.txtSymbol_nabla": "Ανάδελτα", "PE.Controllers.Toolbar.txtSymbol_neq": "Διάφορο από", "PE.Controllers.Toolbar.txtSymbol_ni": "Περιέχει ως μέλος", "PE.Controllers.Toolbar.txtSymbol_not": "Σύμβολο άρνησης", "PE.Controllers.Toolbar.txtSymbol_notexists": "Δεν υπάρχει", "PE.Controllers.Toolbar.txtSymbol_nu": "Νι", "PE.Controllers.Toolbar.txtSymbol_o": "Όμικρον", "PE.Controllers.Toolbar.txtSymbol_omega": "Ωμέγα", "PE.Controllers.Toolbar.txtSymbol_partial": "Μερικό διαφορικό", "PE.Controllers.Toolbar.txtSymbol_percent": "Ποσοστό", "PE.Controllers.Toolbar.txtSymbol_phi": "Φι", "PE.Controllers.Toolbar.txtSymbol_pi": "Πι", "PE.Controllers.Toolbar.txtSymbol_plus": "Συν", "PE.Controllers.Toolbar.txtSymbol_pm": "Συν πλην", "PE.Controllers.Toolbar.txtSymbol_propto": "Σε αναλογία με", "PE.Controllers.Toolbar.txtSymbol_psi": "Ψι", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Τέταρτη ρίζα", "PE.Controllers.Toolbar.txtSymbol_qed": "Τ<PERSON><PERSON><PERSON> απόδειξης", "PE.Controllers.Toolbar.txtSymbol_rddots": "Πάνω δεξιά διαγώνια έλλειψη", "PE.Controllers.Toolbar.txtSymbol_rho": "Ρο", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Δεξ<PERSON> βέλος", "PE.Controllers.Toolbar.txtSymbol_sigma": "Σίγμα", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Σύμβολο ρίζας", "PE.Controllers.Toolbar.txtSymbol_tau": "Ταυ", "PE.Controllers.Toolbar.txtSymbol_therefore": "Επομένως", "PE.Controllers.Toolbar.txtSymbol_theta": "Θήτα", "PE.Controllers.Toolbar.txtSymbol_times": "Σύμβολο Πολλαπλασιασμού", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Πάν<PERSON> βέλος", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ύψιλον", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Παραλλαγή του έψιλον", "PE.Controllers.Toolbar.txtSymbol_varphi": "Παραλλαγή του φι", "PE.Controllers.Toolbar.txtSymbol_varpi": "Παραλλαγή του πι", "PE.Controllers.Toolbar.txtSymbol_varrho": "Παραλλαγή του ρο", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Τελικό σίγμα", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Παραλλαγή του θήτα", "PE.Controllers.Toolbar.txtSymbol_vdots": "Κατακόρυφη έλλειψη", "PE.Controllers.Toolbar.txtSymbol_xsi": "Ξι", "PE.Controllers.Toolbar.txtSymbol_zeta": "Ζήτα", "PE.Controllers.Viewport.textFitPage": "Προσαρμογή στη Διαφάνεια", "PE.Controllers.Viewport.textFitWidth": "Προσαρμο<PERSON><PERSON> στο Πλάτος", "PE.Views.Animation.str0_5": "0.5 δευτ. (Πολύ γρήγορα)", "PE.Views.Animation.str1": "1 δευτ. (Γρήγ<PERSON>ρα)", "PE.Views.Animation.str2": "2 δευτ. (Μέτρια)", "PE.Views.Animation.str20": "20 δευτ. (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αργά)", "PE.Views.Animation.str3": "3 δευτ. (Αργά)", "PE.Views.Animation.str5": "5 δευτ. (<PERSON><PERSON>λ<PERSON>γ<PERSON>)", "PE.Views.Animation.strDelay": "Καθυστέρηση", "PE.Views.Animation.strDuration": "Διάρκεια", "PE.Views.Animation.strRepeat": "Επανάληψη", "PE.Views.Animation.strRewind": "Επανάληψη", "PE.Views.Animation.strStart": "Εκκίνηση", "PE.Views.Animation.strTrigger": "Ενεργοποίηση", "PE.Views.Animation.textAutoPreview": "Αυτόματη προεπισκόπηση", "PE.Views.Animation.textMoreEffects": "Εμφάνιση Περισσότερων Εφφέ", "PE.Views.Animation.textMoveEarlier": "Μετακίνησε Νωρίτερα", "PE.Views.Animation.textMoveLater": "Μετακίνησε Αργότερα", "PE.Views.Animation.textMultiple": "Πολλαπλ<PERSON>ς", "PE.Views.Animation.textNone": "Κανένα", "PE.Views.Animation.textNoRepeat": "(κανένα)", "PE.Views.Animation.textOnClickOf": "Με το Κλικ σε", "PE.Views.Animation.textOnClickSequence": "Με την Ακολουθία Κλικ", "PE.Views.Animation.textStartAfterPrevious": "Μετά το Προηγούμενο", "PE.Views.Animation.textStartOnClick": "Με το Κλικ", "PE.Views.Animation.textStartWithPrevious": "Με το Προηγούμενο", "PE.Views.Animation.textUntilEndOfSlide": "Μέχρι το Τέλος της Διαφάνειας", "PE.Views.Animation.textUntilNextClick": "Μέχρι το Επόμενο Κλικ", "PE.Views.Animation.txtAddEffect": "Προσθήκη animation", "PE.Views.Animation.txtAnimationPane": "Παράθυρο Animation", "PE.Views.Animation.txtParameters": "Παράμετροι", "PE.Views.Animation.txtPreview": "Προεπισκόπηση", "PE.Views.Animation.txtSec": "S", "PE.Views.AnimationDialog.textPreviewEffect": "Εφφέ Προεπισκόπησης", "PE.Views.AnimationDialog.textTitle": "Περισσότερα Εφφέ", "PE.Views.ChartSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "PE.Views.ChartSettings.textChartType": "Αλλαγή Τύπου Γραφήματος", "PE.Views.ChartSettings.textEditData": "Επεξεργασία Δεδομένων", "PE.Views.ChartSettings.textHeight": "Ύψος", "PE.Views.ChartSettings.textKeepRatio": "Σταθερές αναλογίες", "PE.Views.ChartSettings.textSize": "Μέγεθος", "PE.Views.ChartSettings.textStyle": "Τεχνοτροπία", "PE.Views.ChartSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Περιγραφή", "PE.Views.ChartSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Τίτλος", "PE.Views.ChartSettingsAdvanced.textCenter": "Κέντρο", "PE.Views.ChartSettingsAdvanced.textFrom": "Από", "PE.Views.ChartSettingsAdvanced.textHeight": "Ύψος", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Οριζόντιο", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Σταθερές αναλογίες", "PE.Views.ChartSettingsAdvanced.textPlacement": "Τοποθέτηση", "PE.Views.ChartSettingsAdvanced.textPosition": "Θέση", "PE.Views.ChartSettingsAdvanced.textSize": "Μέγεθος", "PE.Views.ChartSettingsAdvanced.textTitle": "Γράφημα - Προηγμένες Ρυθμίσεις", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Πάνω Αριστερή Γωνία", "PE.Views.ChartSettingsAdvanced.textVertical": "Κατακόρυφος", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Ορισμός προεπιλεγμένης μορφής για {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Ορισμός ως προεπιλογή", "PE.Views.DateTimeDialog.textFormat": "Μορφοποιήσεις", "PE.Views.DateTimeDialog.textLang": "Γλώσσα", "PE.Views.DateTimeDialog.textUpdate": "Αυτόματη ενημέρωση", "PE.Views.DateTimeDialog.txtTitle": "Ημερομηνία & Ώρα", "PE.Views.DocumentHolder.aboveText": "Από πάνω", "PE.Views.DocumentHolder.addCommentText": "Προσθήκη Σχολίου", "PE.Views.DocumentHolder.addToLayoutText": "Προσθήκη στη Διάταξη", "PE.Views.DocumentHolder.advancedImageText": "Προηγμένες Ρυθμίσεις Εικόνας", "PE.Views.DocumentHolder.advancedParagraphText": "Προηγμένες Ρυθμίσεις Παραγράφου", "PE.Views.DocumentHolder.advancedShapeText": "Προηγμένες Ρυθμίσεις Σχήματος", "PE.Views.DocumentHolder.advancedTableText": "Προηγμένες Ρυθμίσεις Πίνακα", "PE.Views.DocumentHolder.alignmentText": "Στοίχιση", "PE.Views.DocumentHolder.belowText": "Από κάτω", "PE.Views.DocumentHolder.cellAlignText": "Κατακόρυφη Στοίχιση Κελιού", "PE.Views.DocumentHolder.cellText": "Κελί", "PE.Views.DocumentHolder.centerText": "Κέντρο", "PE.Views.DocumentHolder.columnText": "Στήλη", "PE.Views.DocumentHolder.deleteColumnText": "Διαγρα<PERSON><PERSON> Στήλης", "PE.Views.DocumentHolder.deleteRowText": "Διαγραφή Γραμμής", "PE.Views.DocumentHolder.deleteTableText": "Διαγρα<PERSON>ή Πίνακα", "PE.Views.DocumentHolder.deleteText": "Διαγραφή", "PE.Views.DocumentHolder.direct270Text": "Περιστροφή Κειμένου Πάνω", "PE.Views.DocumentHolder.direct90Text": "Περιστροφή Κειμένου Κάτω", "PE.Views.DocumentHolder.directHText": "Οριζόντια", "PE.Views.DocumentHolder.directionText": "Κατεύθυνση Κειμένου", "PE.Views.DocumentHolder.editChartText": "Επεξεργασία Δεδομένων", "PE.Views.DocumentHolder.editHyperlinkText": "Επεξεργασία Υπερσυνδέσμου", "PE.Views.DocumentHolder.hyperlinkText": "Υπερσύνδεσμος", "PE.Views.DocumentHolder.ignoreAllSpellText": "Αγνόηση Όλων", "PE.Views.DocumentHolder.ignoreSpellText": "Αγνόηση", "PE.Views.DocumentHolder.insertColumnLeftText": "Στήλη Αριστερά", "PE.Views.DocumentHolder.insertColumnRightText": "Στήλη Δεξιά", "PE.Views.DocumentHolder.insertColumnText": "Εισαγω<PERSON><PERSON>ς", "PE.Views.DocumentHolder.insertRowAboveText": "Γραμμή Από Πάνω", "PE.Views.DocumentHolder.insertRowBelowText": "Γραμμή Από <PERSON>ά<PERSON>ω", "PE.Views.DocumentHolder.insertRowText": "Εισαγωγή Γραμμής", "PE.Views.DocumentHolder.insertText": "Εισαγωγή", "PE.Views.DocumentHolder.langText": "Επιλογ<PERSON>λ<PERSON>", "PE.Views.DocumentHolder.leftText": "Αριστερά", "PE.Views.DocumentHolder.loadSpellText": "Φόρτωση παραλλαγών...", "PE.Views.DocumentHolder.mergeCellsText": "Συγχώνευση Κελιών", "PE.Views.DocumentHolder.mniCustomTable": "Εισαγωγή Προσαρμοσμένου Πίνακα", "PE.Views.DocumentHolder.moreText": "Περισσότερες παραλλαγές...", "PE.Views.DocumentHolder.noSpellVariantsText": "<PERSON><PERSON><PERSON><PERSON><PERSON> παραλλαγές", "PE.Views.DocumentHolder.originalSizeText": "Πραγματικ<PERSON> Μέγεθος", "PE.Views.DocumentHolder.removeHyperlinkText": "Αφαίρεση Υπερσυνδέσμου", "PE.Views.DocumentHolder.rightText": "Δεξιά", "PE.Views.DocumentHolder.rowText": "Γραμμή", "PE.Views.DocumentHolder.selectText": "Επιλογή", "PE.Views.DocumentHolder.spellcheckText": "Ορθογ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς <PERSON>λεγχος", "PE.Views.DocumentHolder.splitCellsText": "Διαίρεση Κελιού...", "PE.Views.DocumentHolder.splitCellTitleText": "Διαίρεση Κελιού", "PE.Views.DocumentHolder.tableText": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "PE.Views.DocumentHolder.textArrangeBack": "Μεταφ<PERSON><PERSON><PERSON> στο Παρασκήνιο", "PE.Views.DocumentHolder.textArrangeBackward": "Μεταφορά Προς τα Πίσω", "PE.Views.DocumentHolder.textArrangeForward": "Μεταφορά Προς τα Εμπρός", "PE.Views.DocumentHolder.textArrangeFront": "Μεταφ<PERSON><PERSON><PERSON> στο Προσκήνιο", "PE.Views.DocumentHolder.textCopy": "Αντιγραφή", "PE.Views.DocumentHolder.textCrop": "Περικοπή", "PE.Views.DocumentHolder.textCropFill": "Γέμισμα", "PE.Views.DocumentHolder.textCropFit": "Προσαρμογή", "PE.Views.DocumentHolder.textCut": "Αποκοπή", "PE.Views.DocumentHolder.textDistributeCols": "Κατανο<PERSON><PERSON> στηλών", "PE.Views.DocumentHolder.textDistributeRows": "Κατανομή γραμμών", "PE.Views.DocumentHolder.textEditPoints": "Επεξεργασία Σημείων", "PE.Views.DocumentHolder.textFlipH": "Οριζόντια Περιστροφή", "PE.Views.DocumentHolder.textFlipV": "Κατακόρυφη Περιστροφή", "PE.Views.DocumentHolder.textFromFile": "Από Αρχείο", "PE.Views.DocumentHolder.textFromStorage": "Από Αποθηκευτικ<PERSON>ο", "PE.Views.DocumentHolder.textFromUrl": "Από διεύθυνση URL", "PE.Views.DocumentHolder.textNextPage": "Επόμενη Διαφάνεια", "PE.Views.DocumentHolder.textPaste": "Επικόλληση", "PE.Views.DocumentHolder.textPrevPage": "Προηγούμενη Διαφάνεια", "PE.Views.DocumentHolder.textReplace": "Αντικατάσταση εικόνας", "PE.Views.DocumentHolder.textRotate": "Περιστροφή", "PE.Views.DocumentHolder.textRotate270": "Περιστροφή 90° Αριστερόστροφα", "PE.Views.DocumentHolder.textRotate90": "Περιστροφή 90° Δεξιόστροφα", "PE.Views.DocumentHolder.textShapeAlignBottom": "Στοίχιση Κάτω", "PE.Views.DocumentHolder.textShapeAlignCenter": "Στοίχιση στο Κέντρο", "PE.Views.DocumentHolder.textShapeAlignLeft": "Στοίχιση Αριστερά", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Στοίχιση στη Μέση", "PE.Views.DocumentHolder.textShapeAlignRight": "Στοίχιση Δεξιά", "PE.Views.DocumentHolder.textShapeAlignTop": "Στοίχιση Πάνω", "PE.Views.DocumentHolder.textSlideSettings": "Ρυθμίσεις Διαφάνειας", "PE.Views.DocumentHolder.textUndo": "Αναίρεση", "PE.Views.DocumentHolder.tipIsLocked": "Αυτό το στοιχεί<PERSON> επεξεργάζ<PERSON><PERSON>αι αυτήν τη στιγμή από άλλο χρήστη.", "PE.Views.DocumentHolder.toDictionaryText": "Προσθήκη στο Λεξικό", "PE.Views.DocumentHolder.txtAddBottom": "Προσθήκη κάτω περιγράμματος", "PE.Views.DocumentHolder.txtAddFractionBar": "Προσθήκη γραμμής κλάσματος", "PE.Views.DocumentHolder.txtAddHor": "Προσθήκη οριζόντιας γραμμής", "PE.Views.DocumentHolder.txtAddLB": "Προσθήκη αριστερής κάτω γραμμής", "PE.Views.DocumentHolder.txtAddLeft": "Προσθήκη αριστερού περιγράμματος", "PE.Views.DocumentHolder.txtAddLT": "Προσθήκη αριστερής πάνω γραμμής", "PE.Views.DocumentHolder.txtAddRight": "Προσθήκη δεξιού περιγράμματος", "PE.Views.DocumentHolder.txtAddTop": "Προσθήκη επάνω περιγράμματος", "PE.Views.DocumentHolder.txtAddVer": "Προσθήκη κατακόρυφης γραμμής", "PE.Views.DocumentHolder.txtAlign": "Στοίχιση", "PE.Views.DocumentHolder.txtAlignToChar": "Στοίχιση σε χαρακτήρα", "PE.Views.DocumentHolder.txtArrange": "Τακτοποίηση", "PE.Views.DocumentHolder.txtBackground": "Παρασκήνιο", "PE.Views.DocumentHolder.txtBorderProps": "Ιδιότητες περιγράμματος", "PE.Views.DocumentHolder.txtBottom": "Κάτω", "PE.Views.DocumentHolder.txtChangeLayout": "Αλλαγ<PERSON> Διάταξης", "PE.Views.DocumentHolder.txtChangeTheme": "Αλλα<PERSON><PERSON>έμ<PERSON>", "PE.Views.DocumentHolder.txtColumnAlign": "Στοίχιση στήλης", "PE.Views.DocumentHolder.txtDecreaseArg": "Μείωση μεγέθους ορίσματος", "PE.Views.DocumentHolder.txtDeleteArg": "Διαγραφή ορίσματος", "PE.Views.DocumentHolder.txtDeleteBreak": "Διαγρα<PERSON>ή χειροκίνητης αλλαγής", "PE.Views.DocumentHolder.txtDeleteChars": "Διαγρα<PERSON>ή χαρακτήρων εγκλεισμού", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Διαγρα<PERSON>ή χαρακτήρων εγκλεισμού και διαχωριστών", "PE.Views.DocumentHolder.txtDeleteEq": "Διαγραφή εξίσωσης", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Διαγρα<PERSON><PERSON> χαρακτήρα", "PE.Views.DocumentHolder.txtDeleteRadical": "Διαγραφή ρίζας", "PE.Views.DocumentHolder.txtDeleteSlide": "Διαγραφή Διαφάνειας", "PE.Views.DocumentHolder.txtDistribHor": "Οριζόντια Κατανομή", "PE.Views.DocumentHolder.txtDistribVert": "Κατακόρυφη Κατανομή", "PE.Views.DocumentHolder.txtDuplicateSlide": "Αναπαραγωγή Διαφάνειας", "PE.Views.DocumentHolder.txtFractionLinear": "Αλλαγή σε γραμμικό κλάσμα", "PE.Views.DocumentHolder.txtFractionSkewed": "Αλλαγή σε πλάγιο κλάσμα", "PE.Views.DocumentHolder.txtFractionStacked": "Αλλαγή σε όρθιο κλάσμα", "PE.Views.DocumentHolder.txtGroup": "Ομάδα", "PE.Views.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πάνω από το κείμενο γραμμάτων", "PE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κάτω από το κείμενο", "PE.Views.DocumentHolder.txtHideBottom": "Απόκρυψη κάτω περιγράμματος", "PE.Views.DocumentHolder.txtHideBottomLimit": "Απόκρυψη κάτω ορίου", "PE.Views.DocumentHolder.txtHideCloseBracket": "Απόκρυψη παρένθεσης που κλείνει", "PE.Views.DocumentHolder.txtHideDegree": "Απόκρυψη πτυχίου", "PE.Views.DocumentHolder.txtHideHor": "Απόκρυψη οριζόντιας γραμμής", "PE.Views.DocumentHolder.txtHideLB": "Απόκρυψη αριστερής κάτω γραμμής", "PE.Views.DocumentHolder.txtHideLeft": "Απόκρυψη αριστερού περιγράμματος", "PE.Views.DocumentHolder.txtHideLT": "Απόκρυψη αριστερής πάνω γραμμής", "PE.Views.DocumentHolder.txtHideOpenBracket": "Απόκρυψη παρένθεσης που ανοίγει", "PE.Views.DocumentHolder.txtHidePlaceholder": "Απόκρυψη δέσμευσης θέσης", "PE.Views.DocumentHolder.txtHideRight": "Απόκρυψη δεξιού περιγράμματος", "PE.Views.DocumentHolder.txtHideTop": "Απόκρυψη πάνω περιγράμματος", "PE.Views.DocumentHolder.txtHideTopLimit": "Απόκρυψη άνω ορίου", "PE.Views.DocumentHolder.txtHideVer": "Απόκρυψη κατακόρυφης γραμμής", "PE.Views.DocumentHolder.txtIncreaseArg": "Αύξηση μεγέθους ορίσματος", "PE.Views.DocumentHolder.txtInsertArgAfter": "Εισαγωγή ορίσματος μετά", "PE.Views.DocumentHolder.txtInsertArgBefore": "Εισαγωγή ορίσματος πριν", "PE.Views.DocumentHolder.txtInsertBreak": "Εισαγωγή χειροκίνητης αλλαγής", "PE.Views.DocumentHolder.txtInsertEqAfter": "Εισαγωγή εξίσωσης μετά", "PE.Views.DocumentHolder.txtInsertEqBefore": "Εισαγωγή εξίσωσης πριν", "PE.Views.DocumentHolder.txtKeepTextOnly": "Διατήρηση κειμένου μόνο", "PE.Views.DocumentHolder.txtLimitChange": "Αλλαγή θέσης ορίων", "PE.Views.DocumentHolder.txtLimitOver": "Όριο πάνω από το κείμενο", "PE.Views.DocumentHolder.txtLimitUnder": "Όριο κάτω από το κείμενο", "PE.Views.DocumentHolder.txtMatchBrackets": "Προσαρμογή παρενθέσεων στο ύψος των ορισμάτων", "PE.Views.DocumentHolder.txtMatrixAlign": "Στοίχιση πίνακα", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Μετακίνησε τη Διαφάνεια στο Τέλος", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Μετακίνησε τη Διαφάνεια στην Αρχή", "PE.Views.DocumentHolder.txtNewSlide": "Νέα Διαφάνεια", "PE.Views.DocumentHolder.txtOverbar": "Μπάρα πάνω από κείμενο", "PE.Views.DocumentHolder.txtPasteDestFormat": "Χρήση θέματος προορισμού", "PE.Views.DocumentHolder.txtPastePicture": "Εικόνα", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Διατήρηση μορφοποίησης πηγής", "PE.Views.DocumentHolder.txtPressLink": "Πατήστε {0} και κάντε κλικ στο σύνδεσμο", "PE.Views.DocumentHolder.txtPreview": "Εκκίνηση παρουσίασης", "PE.Views.DocumentHolder.txtPrintSelection": "Εκτύπωση Επιλογής", "PE.Views.DocumentHolder.txtRemFractionBar": "Αφαίρεση γραμμής κλάσματος", "PE.Views.DocumentHolder.txtRemLimit": "Αφαίρεση ορίου", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Αφαίρεση τονισμένου χαρακτήρα", "PE.Views.DocumentHolder.txtRemoveBar": "Αφαίρεση μπάρας", "PE.Views.DocumentHolder.txtRemScripts": "Αφαίρεση δεσμών ενεργειών", "PE.Views.DocumentHolder.txtRemSubscript": "Αφαίρεση δείκτη", "PE.Views.DocumentHolder.txtRemSuperscript": "Αφαίρεση εκθέτη", "PE.Views.DocumentHolder.txtResetLayout": "Επαναφορά Διαφάνειας", "PE.Views.DocumentHolder.txtScriptsAfter": "Δέσμες ενεργειών μετά το κείμενο", "PE.Views.DocumentHolder.txtScriptsBefore": "Δέσμες ενεργειών πριν το κείμενο", "PE.Views.DocumentHolder.txtSelectAll": "Επιλογή Όλων ", "PE.Views.DocumentHolder.txtShowBottomLimit": "Εμφάνιση κάτω ορίου", "PE.Views.DocumentHolder.txtShowCloseBracket": "Εμφάνιση δεξιάς παρένθεσης", "PE.Views.DocumentHolder.txtShowDegree": "Εμφάνιση πτυχίου", "PE.Views.DocumentHolder.txtShowOpenBracket": "Εμφάνιση αριστερής παρένθεσης", "PE.Views.DocumentHolder.txtShowPlaceholder": "Εμφάνιση δεσμευμένης θέσης", "PE.Views.DocumentHolder.txtShowTopLimit": "Εμφάνιση πάνω ορίου", "PE.Views.DocumentHolder.txtSlide": "Διαφάνεια", "PE.Views.DocumentHolder.txtSlideHide": "Απόκρυψη Διαφάνειας", "PE.Views.DocumentHolder.txtStretchBrackets": "Έκταση παρενθέσεων", "PE.Views.DocumentHolder.txtTop": "Επάνω", "PE.Views.DocumentHolder.txtUnderbar": "Μπά<PERSON><PERSON> κάτω από κείμενο", "PE.Views.DocumentHolder.txtUngroup": "Κατάργηση ομαδοποίησης", "PE.Views.DocumentHolder.txtWarnUrl": "Η συσκευή και τα δεδομένα σας μπορεί να κινδυνεύσουν αν κάνετε κλικ σε αυτόν τον σύνδεσμο.<br>Θέλετε σίγουρα να συνεχίσετε;", "PE.Views.DocumentHolder.vertAlignText": "Κατακόρυφη Στοίχιση", "PE.Views.DocumentPreview.goToSlideText": "Μετάβαση στη Διαφάνεια", "PE.Views.DocumentPreview.slideIndexText": "Διαφάνεια {0} από {1}", "PE.Views.DocumentPreview.txtClose": "Κλείσιμο προβολής διαφανειών", "PE.Views.DocumentPreview.txtEndSlideshow": "Τέλος προβολής διαφανειών", "PE.Views.DocumentPreview.txtExitFullScreen": "Έξοδος από την πλήρη οθόνη", "PE.Views.DocumentPreview.txtFinalMessage": "Τέλος προεπισκόπησης διαφανειών. Κάντε κλικ για έξοδο.", "PE.Views.DocumentPreview.txtFullScreen": "Πλή<PERSON><PERSON><PERSON> οθόνη", "PE.Views.DocumentPreview.txtNext": "Επόμενη διαφάνεια", "PE.Views.DocumentPreview.txtPageNumInvalid": "Μη έγκυρος αριθμός διαφάνειας", "PE.Views.DocumentPreview.txtPause": "Παύση παρουσίασης", "PE.Views.DocumentPreview.txtPlay": "Έναρξη παρουσίασης", "PE.Views.DocumentPreview.txtPrev": "Προηγούμενη διαφάνεια", "PE.Views.DocumentPreview.txtReset": "Επαναφορά", "PE.Views.FileMenu.btnAboutCaption": "Περί", "PE.Views.FileMenu.btnBackCaption": "Άνοιγμα τοποθεσίας αρχείου", "PE.Views.FileMenu.btnCloseMenuCaption": "Κλείσιμο <PERSON>ενού", "PE.Views.FileMenu.btnCreateNewCaption": "Δημιουργ<PERSON>α Νέου", "PE.Views.FileMenu.btnDownloadCaption": "Λήψη ως", "PE.Views.FileMenu.btnExitCaption": "Κλείσιμο", "PE.Views.FileMenu.btnFileOpenCaption": "Άνοιγμα", "PE.Views.FileMenu.btnHelpCaption": "Βοήθεια", "PE.Views.FileMenu.btnHistoryCaption": "Ιστορικ<PERSON> εκδόσεων", "PE.Views.FileMenu.btnInfoCaption": "Πληροφορίες παρουσίασης", "PE.Views.FileMenu.btnPrintCaption": "Εκτύπωση", "PE.Views.FileMenu.btnProtectCaption": "Προστασία", "PE.Views.FileMenu.btnRecentFilesCaption": "Άνοιγμα Πρόσφατου", "PE.Views.FileMenu.btnRenameCaption": "Μετονομασία", "PE.Views.FileMenu.btnReturnCaption": "Πίσω στην Παρουσίαση", "PE.Views.FileMenu.btnRightsCaption": "Δικαιώματα Πρόσβασης", "PE.Views.FileMenu.btnSaveAsCaption": "Αποθήκευση ως", "PE.Views.FileMenu.btnSaveCaption": "Αποθήκευση", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Αποθήκευση Αντιγράφου ως", "PE.Views.FileMenu.btnSettingsCaption": "Προηγμένες Ρυθμίσεις", "PE.Views.FileMenu.btnToEditCaption": "Επεξεργασία Παρουσίασης", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Κενή Παρουσίαση", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Δημιουργ<PERSON>α Νέας", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Εφαρμογή", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Προσθήκη Συγγραφέα", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Προσθήκη Κειμένου", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Εφαρμογή", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Συγγραφέας", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Σχόλιο", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Δημιουργήθηκε", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Τελευταία Τροποποίηση Από", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Τελευτα<PERSON><PERSON> Τροποποιημένο", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Κάτ<PERSON><PERSON>ος", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Τοποθεσία", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Άτομα που έχουν δικαιώματα", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Θέμα", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Τίτλος", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Μεταφορτώθηκε", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Άτομα που έχουν δικαιώματα", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Προειδοποίηση", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Με συνθηματικό", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Προστα<PERSON><PERSON><PERSON> Παρουσίασης", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Με υπογραφή", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Επεξεργασία παρουσίασης", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Η επεξεργασία θα αφαιρέσει τις υπογραφές από την παρουσίαση.<br>Θέλετε σίγουρα να συνεχίσετε;", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Η παρουσίαση προστατεύτηκε με συνθηματικό", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Έγκυρες υπογραφές προστέθηκαν στην παρουσίαση. Η παρουσίαση προστατεύεται από επεξεργασία.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Κάποιες από τις ψηφιακ<PERSON>ς υπογραφές στην παρουσίαση είναι άκυρες ή δεν επαληθεύτηκαν. Η παρουσίαση έχει προστασία τροποποίησης.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Προβολή υπογραφών", "PE.Views.FileMenuPanels.Settings.okButtonText": "Εφαρμογή", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Κατάσταση Συν-επεξεργασίας", "PE.Views.FileMenuPanels.Settings.strFast": "Γρήγ<PERSON><PERSON>η", "PE.Views.FileMenuPanels.Settings.strFontRender": "Βελτιστοποίηση Γραμματοσειράς", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Αγνόηση λέξεων με ΚΕΦΑΛΑΙΑ", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Αγνόηση λέξεων με αριθμούς", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Ρυθμίσεις Mακροεντολών", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Εμφάνιση κουμπιού Επιλογών <PERSON>πικόλλησης κατά την επικόλληση περιεχομένου", "PE.Views.FileMenuPanels.Settings.strStrict": "Αυστηρή", "PE.Views.FileMenuPanels.Settings.strTheme": "Θέμα", "PE.Views.FileMenuPanels.Settings.strUnit": "Μονάδα Μέτρησης", "PE.Views.FileMenuPanels.Settings.strZoom": "Προεπιλεγμένη Τιμή Εστίασης", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Κάθε 10 Λεπτά", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Κάθε 30 Λεπτά", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Κάθε 5 Λεπτά", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Κάθε Ώρα", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Οδηγ<PERSON><PERSON> Στοίχισης", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Αυτόματη ανάκτηση", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Αυτόματη αποθήκευση", "PE.Views.FileMenuPanels.Settings.textDisabled": "Απενεργοποιημένο", "PE.Views.FileMenuPanels.Settings.textForceSave": "Αποθήκευση ενδιάμεσων εκδόσεων", "PE.Views.FileMenuPanels.Settings.textMinute": "Κάθε Λεπτό", "PE.Views.FileMenuPanels.Settings.txtAll": "Προβολή Όλων", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Επιλογής αυτόματης διόρθωσης...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Προεπιλεγμένη κατάσταση λανθάνουσας μνήμης", "PE.Views.FileMenuPanels.Settings.txtCm": "Εκατοστό", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Συνεργασία", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Επεξεργασία και αποθήκευση", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Συν-επεξεργασία σε πραγματικό χρόνο. Όλες οι αλλαγές αποθηκεύονται αυτόματα", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Προσαρμογή στη Διαφάνεια", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Προσαρμο<PERSON><PERSON> στο Πλάτος", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Ιερογλυφικά", "PE.Views.FileMenuPanels.Settings.txtInch": "Ίντσα", "PE.Views.FileMenuPanels.Settings.txtLast": "Προβολή Τελευταίου", "PE.Views.FileMenuPanels.Settings.txtMac": "ως OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Εγγεν<PERSON>ς", "PE.Views.FileMenuPanels.Settings.txtProofing": "Διόρθωση Κειμένου", "PE.Views.FileMenuPanels.Settings.txtPt": "Σημείο", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Ενεργοποίηση Όλων", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Ενεργοποίηση όλων των μακροεντολών χωρίς ειδοποίηση", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Έλεγχος Ορθογραφίας", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Απενεργοποίηση Όλων", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Απενεργοποίηση όλων των μακροεντολών χωρίς ειδοποίηση", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Χρησιμοποιήστε το κουμπί \"Αποθήκευση\" για να συγχρονίσετε τις αλλαγές που κάνετε εσείς και άλλοι", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Χρησιμοποιήστε το πλήκτρο Alt για πλοήγηση στη διεπαφή χρήστη χρησιμοποιώντας το πληκτρολόγιο", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Χρησιμοποιήστε το πλήκτρο επιλογής για πλοήγηση στη διεπαφή χρήστη χρησιμοποιώντας το πληκτρολόγιο", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Εμφάνιση Ειδοποίησης", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Απενεργοποίηση όλων των μακροεντολών με ειδοποίηση", "PE.Views.FileMenuPanels.Settings.txtWin": "ως Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON><PERSON><PERSON> εργασ<PERSON>ας", "PE.Views.HeaderFooterDialog.applyAllText": "Εφαρμογή σε όλες", "PE.Views.HeaderFooterDialog.applyText": "Εφαρμογή", "PE.Views.HeaderFooterDialog.diffLanguage": "Δεν μπορείτε να χρησιμοποιήσετε μορφή ημερομηνίας σε διαφορετική γλώσσα από την βασική διαφάνεια.<br><PERSON><PERSON><PERSON> να αλλάξετε τη βασική διαφάνεια, κάντε κλικ στο 'Εφαρμογή σε όλες' αντί για το 'Εφαρμογή'", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Προειδοποίηση", "PE.Views.HeaderFooterDialog.textDateTime": "Ημερομηνία και ώρα", "PE.Views.HeaderFooterDialog.textFixed": "Σταθερό", "PE.Views.HeaderFooterDialog.textFooter": "Κείμενο στο υποσέλιδο", "PE.Views.HeaderFooterDialog.textFormat": "Μορφοποιήσεις", "PE.Views.HeaderFooterDialog.textLang": "Γλώσσα", "PE.Views.HeaderFooterDialog.textNotTitle": "Να μην εμφανίζεται στη διαφάνεια τίτλου", "PE.Views.HeaderFooterDialog.textPreview": "Προεπισκόπηση", "PE.Views.HeaderFooterDialog.textSlideNum": "Αριθμός διαφάνειας", "PE.Views.HeaderFooterDialog.textTitle": "Ρυθμίσεις Υποσέλιδου", "PE.Views.HeaderFooterDialog.textUpdate": "Αυτόματη ενημέρωση", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Προβολή", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Σύνδεσ<PERSON>ος Σε", "PE.Views.HyperlinkSettingsDialog.textDefault": "Επιλεγμέν<PERSON> κομμάτι κειμένου", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Εισάγετε λεζάντα εδώ", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Εισάγετε σύνδεσμο εδώ", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Εισάγετε συμβουλή εδώ", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Εξωτερι<PERSON><PERSON>ς <PERSON>ύνδεσμος", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Διαφάνει<PERSON> Εντ<PERSON>ς Παρουσίασης", "PE.Views.HyperlinkSettingsDialog.textSlides": "Διαφάνειες", "PE.Views.HyperlinkSettingsDialog.textTipText": "Κείμενο Υπόδειξης", "PE.Views.HyperlinkSettingsDialog.textTitle": "Ρυθμίσεις Υπερσυνδέσμου", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Πρώτη Διαφάνεια", "PE.Views.HyperlinkSettingsDialog.txtLast": "Τελευταία Διαφάνεια", "PE.Views.HyperlinkSettingsDialog.txtNext": "Επόμενη Διαφάνεια", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Αυτό το πεδίο πρέπει να είναι διεύθυνση URL με τη μορφή «http://www.example.com»", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Προηγούμενη Διαφάνεια", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Το πεδίο αυτό χωράει 2083 χαρακτήρες", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Διαφάνεια", "PE.Views.ImageSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "PE.Views.ImageSettings.textCrop": "Περικοπή", "PE.Views.ImageSettings.textCropFill": "Γέμισμα", "PE.Views.ImageSettings.textCropFit": "Προσαρμογή", "PE.Views.ImageSettings.textCropToShape": "Περικο<PERSON>ή στο σχήμα", "PE.Views.ImageSettings.textEdit": "Επεξεργασία", "PE.Views.ImageSettings.textEditObject": "Επεξεργασία Αντικειμένου", "PE.Views.ImageSettings.textFitSlide": "Προσαρμογή στη Διαφάνεια", "PE.Views.ImageSettings.textFlip": "Περιστροφή", "PE.Views.ImageSettings.textFromFile": "Από Αρχείο", "PE.Views.ImageSettings.textFromStorage": "Από Αποθηκευτικ<PERSON>ο", "PE.Views.ImageSettings.textFromUrl": "Από διεύθυνση URL", "PE.Views.ImageSettings.textHeight": "Ύψος", "PE.Views.ImageSettings.textHint270": "Περιστροφή 90° Αριστερόστροφα", "PE.Views.ImageSettings.textHint90": "Περιστροφή 90° Δεξιόστροφα", "PE.Views.ImageSettings.textHintFlipH": "Οριζόντια Περιστροφή", "PE.Views.ImageSettings.textHintFlipV": "Κατακόρυφη Περιστροφή", "PE.Views.ImageSettings.textInsert": "Αντικατάσταση Εικόνας", "PE.Views.ImageSettings.textOriginalSize": "Πραγματικ<PERSON> Μέγεθος", "PE.Views.ImageSettings.textRecentlyUsed": "Πρόσφατα Χρησιμοποιημένα", "PE.Views.ImageSettings.textRotate90": "Περιστροφή 90°", "PE.Views.ImageSettings.textRotation": "Περιστροφή", "PE.Views.ImageSettings.textSize": "Μέγεθος", "PE.Views.ImageSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Περιγραφή", "PE.Views.ImageSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Τίτλος", "PE.Views.ImageSettingsAdvanced.textAngle": "Γωνία", "PE.Views.ImageSettingsAdvanced.textCenter": "Κέντρο", "PE.Views.ImageSettingsAdvanced.textFlipped": "Περιεστρεμμένο", "PE.Views.ImageSettingsAdvanced.textFrom": "Από", "PE.Views.ImageSettingsAdvanced.textHeight": "Ύψος", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Οριζόντια", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Οριζόντια", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Σταθερές αναλογίες", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Πραγματικ<PERSON> Μέγεθος", "PE.Views.ImageSettingsAdvanced.textPlacement": "Τοποθέτηση", "PE.Views.ImageSettingsAdvanced.textPosition": "Θέση", "PE.Views.ImageSettingsAdvanced.textRotation": "Περιστροφή", "PE.Views.ImageSettingsAdvanced.textSize": "Μέγεθος", "PE.Views.ImageSettingsAdvanced.textTitle": "Εικόνα - Προηγμένες Ρυθμίσεις", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Πάνω Αριστερή Γωνία", "PE.Views.ImageSettingsAdvanced.textVertical": "Κατακόρυφος", "PE.Views.ImageSettingsAdvanced.textVertically": "Κατακόρυφα", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "Περί", "PE.Views.LeftMenu.tipChat": "Συνομιλία", "PE.Views.LeftMenu.tipComments": "Σχόλια", "PE.Views.LeftMenu.tipPlugins": "Πρόσθετα", "PE.Views.LeftMenu.tipSearch": "Αναζήτηση", "PE.Views.LeftMenu.tipSlides": "Διαφάνειες", "PE.Views.LeftMenu.tipSupport": "Ανατροφοδότηση & Υποστήριξη", "PE.Views.LeftMenu.tipTitles": "Τίτλοι", "PE.Views.LeftMenu.txtDeveloper": "ΛΕΙΤΟΥΡΓΙΑ ΓΙΑ ΠΡΟΓΡΑΜΜΑΤΙΣΤΕΣ", "PE.Views.LeftMenu.txtEditor": "Συντάκτης Παρουσίασης", "PE.Views.LeftMenu.txtLimit": "Περιορισμ<PERSON>ς Πρόσβασης", "PE.Views.LeftMenu.txtTrial": "ΚΑΤΑΣΤΑΣΗ ΔΟΚΙΜΑΣΤΙΚΗΣ ΛΕΙΤΟΥΡΓΙΑΣ", "PE.Views.LeftMenu.txtTrialDev": "Δοκιμαστική Λειτουργία για Προγραμματιστές", "PE.Views.ParagraphSettings.strLineHeight": "Διάστιχο", "PE.Views.ParagraphSettings.strParagraphSpacing": "Απόσταση Παραγράφων", "PE.Views.ParagraphSettings.strSpacingAfter": "Μετά", "PE.Views.ParagraphSettings.strSpacingBefore": "Πριν", "PE.Views.ParagraphSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "PE.Views.ParagraphSettings.textAt": "Στο", "PE.Views.ParagraphSettings.textAtLeast": "Τουλάχιστον", "PE.Views.ParagraphSettings.textAuto": "Πολλαπλό", "PE.Views.ParagraphSettings.textExact": "Α<PERSON><PERSON>ιβ<PERSON>ς", "PE.Views.ParagraphSettings.txtAutoText": "Αυτόματα", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Οι καθορισμένοι στηλοθέτες θα εμφανίζονται σε αυτό το πεδίο", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Όλα κεφαλαία", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Διπλή Διαγραφή", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Εσοχ<PERSON>ς", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Αριστερά", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Διάστιχο", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Δεξιά", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Μετά", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Πριν", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Ειδική", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Γραμματοσειρά", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Εσο<PERSON><PERSON>ς & Αποστάσεις", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Μικρά κεφαλαία", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Απόσταση", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Διακριτική διαγραφή", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Δείκτης", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Εκθέτης", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Στηλοθέτες", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Στοίχιση", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Πολλαπλό", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Απόστα<PERSON>η Χαρακτήρων", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Προεπι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ηλοθέτης", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Εφέ", "PE.Views.ParagraphSettingsAdvanced.textExact": "Α<PERSON><PERSON>ιβ<PERSON>ς", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Πρώτη γραμμή", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Αρνητική", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Πλήρης στοίχιση", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(κανένα)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Αφαίρεση", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Αφαίρεση Όλων", "PE.Views.ParagraphSettingsAdvanced.textSet": "Προσδιορισμός", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Κέντρο", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Αριστερά", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Θέση Στηλοθέτη", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Δεξιά", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Παράγραφος - Προηγμένες Ρυθμίσεις", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Αυτόματα", "PE.Views.RightMenu.txtChartSettings": "Ρυθμίσεις γραφήματος", "PE.Views.RightMenu.txtImageSettings": "Ρυθμίσεις εικόνας", "PE.Views.RightMenu.txtParagraphSettings": "Ρυθμίσεις Παραγράφου", "PE.Views.RightMenu.txtShapeSettings": "Ρυθμίσεις σχήματος", "PE.Views.RightMenu.txtSignatureSettings": "Ρυθμίσεις υπογραφής", "PE.Views.RightMenu.txtSlideSettings": "Ρυθμίσεις διαφάνειας", "PE.Views.RightMenu.txtTableSettings": "Ρυθμίσεις πίνακα", "PE.Views.RightMenu.txtTextArtSettings": "Ρυθμίσεις Τεχνοκειμένου", "PE.Views.ShapeSettings.strBackground": "Χρώμα παρασκηνίου", "PE.Views.ShapeSettings.strChange": "Αλλαγή Αυτόματου Σχήματος", "PE.Views.ShapeSettings.strColor": "Χρώμα", "PE.Views.ShapeSettings.strFill": "Γέμισμα", "PE.Views.ShapeSettings.strForeground": "Χρώμα προσκηνίου", "PE.Views.ShapeSettings.strPattern": "Μοτίβο", "PE.Views.ShapeSettings.strShadow": "Εμφάνιση σκιάς", "PE.Views.ShapeSettings.strSize": "Μέγεθος", "PE.Views.ShapeSettings.strStroke": "Πινελιά", "PE.Views.ShapeSettings.strTransparency": "Αδιαφάνεια", "PE.Views.ShapeSettings.strType": "Τύπος", "PE.Views.ShapeSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "PE.Views.ShapeSettings.textAngle": "Γωνία", "PE.Views.ShapeSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0pt και 1584pt.", "PE.Views.ShapeSettings.textColor": "Γέμισμα με Χρώμα", "PE.Views.ShapeSettings.textDirection": "Κατεύθυνση", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFlip": "Περιστροφή", "PE.Views.ShapeSettings.textFromFile": "Από Αρχείο", "PE.Views.ShapeSettings.textFromStorage": "Από Αποθηκευτικ<PERSON>ο", "PE.Views.ShapeSettings.textFromUrl": "Από διεύθυνση URL", "PE.Views.ShapeSettings.textGradient": "Σημεία διαβάθμισης", "PE.Views.ShapeSettings.textGradientFill": "Βαθμωτό Γέμισμα", "PE.Views.ShapeSettings.textHint270": "Περιστροφή 90° Αριστερόστροφα", "PE.Views.ShapeSettings.textHint90": "Περιστροφή 90° Δεξιόστροφα", "PE.Views.ShapeSettings.textHintFlipH": "Οριζόντια Περιστροφή", "PE.Views.ShapeSettings.textHintFlipV": "Κατακόρυφη Περιστροφή", "PE.Views.ShapeSettings.textImageTexture": "Εικόνα ή Υφή", "PE.Views.ShapeSettings.textLinear": "Γραμμικός", "PE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON>α", "PE.Views.ShapeSettings.textPatternFill": "Μοτίβο", "PE.Views.ShapeSettings.textPosition": "Θέση", "PE.Views.ShapeSettings.textRadial": "Ακτινικ<PERSON>ς", "PE.Views.ShapeSettings.textRecentlyUsed": "Πρόσφατα Χρησιμοποιημένα", "PE.Views.ShapeSettings.textRotate90": "Περιστροφή 90°", "PE.Views.ShapeSettings.textRotation": "Περιστροφή", "PE.Views.ShapeSettings.textSelectImage": "Επιλογή Εικόνας", "PE.Views.ShapeSettings.textSelectTexture": "Επιλογή", "PE.Views.ShapeSettings.textStretch": "Έκταση", "PE.Views.ShapeSettings.textStyle": "Τεχνοτροπία", "PE.Views.ShapeSettings.textTexture": "Από Υφή", "PE.Views.ShapeSettings.textTile": "Πλακίδιο", "PE.Views.ShapeSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "PE.Views.ShapeSettings.txtBrownPaper": "Καφέ <PERSON>", "PE.Views.ShapeSettings.txtCanvas": "Καμβάς", "PE.Views.ShapeSettings.txtCarton": "Χ<PERSON>ρτόνι", "PE.Views.ShapeSettings.txtDarkFabric": "Σκούρο Ύφασμα", "PE.Views.ShapeSettings.txtGrain": "Κόκκος", "PE.Views.ShapeSettings.txtGranite": "Γραν<PERSON>της", "PE.Views.ShapeSettings.txtGreyPaper": "Γκρι <PERSON>", "PE.Views.ShapeSettings.txtKnit": "Πλέκω", "PE.Views.ShapeSettings.txtLeather": "Δέρμα", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "Ξύλο", "PE.Views.ShapeSettingsAdvanced.strColumns": "Στήλες", "PE.Views.ShapeSettingsAdvanced.strMargins": "Απόσταση Κειμένου", "PE.Views.ShapeSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Περιγραφή", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Τίτλος", "PE.Views.ShapeSettingsAdvanced.textAngle": "Γωνία", "PE.Views.ShapeSettingsAdvanced.textArrows": "Βέλη", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Αυτόματο Ταίριασμα", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Μέγ<PERSON>θος <PERSON>ης", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Τεχνοτροπία Εκκίνησης", "PE.Views.ShapeSettingsAdvanced.textBevel": "Πλάγια Τομή", "PE.Views.ShapeSettingsAdvanced.textBottom": "Κάτω", "PE.Views.ShapeSettingsAdvanced.textCapType": "Αρχικό Γράμμα", "PE.Views.ShapeSettingsAdvanced.textCenter": "Κέντρο", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Αριθμ<PERSON><PERSON> στηλών", "PE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Τέλους", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Τεχνοτροπία Τέλους", "PE.Views.ShapeSettingsAdvanced.textFlat": "Επίπεδο", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Περιεστρεμμένο", "PE.Views.ShapeSettingsAdvanced.textFrom": "Από", "PE.Views.ShapeSettingsAdvanced.textHeight": "Ύψος", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Οριζόντιο", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Οριζόντια", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Τύπος Ένωσης", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Σταθερές αναλογίες", "PE.Views.ShapeSettingsAdvanced.textLeft": "Αριστερά", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Τεχνοτροπία Γραμμής", "PE.Views.ShapeSettingsAdvanced.textMiter": "Μίτρα", "PE.Views.ShapeSettingsAdvanced.textNofit": "Όχι Αυτόματο Ταίριασμα", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Τοποθέτηση", "PE.Views.ShapeSettingsAdvanced.textPosition": "Θέση", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Προσαρμογή σχήματος για ταίριασμα με το κείμενο", "PE.Views.ShapeSettingsAdvanced.textRight": "Δεξιά", "PE.Views.ShapeSettingsAdvanced.textRotation": "Περιστροφή", "PE.Views.ShapeSettingsAdvanced.textRound": "Στρογγυλεμένο", "PE.Views.ShapeSettingsAdvanced.textShrink": "Σμίκρυνση κειμένου κατά την υπερχείλιση", "PE.Views.ShapeSettingsAdvanced.textSize": "Μέγεθος", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Απόσταση μεταξύ στηλών", "PE.Views.ShapeSettingsAdvanced.textSquare": "Τετράγωνο", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Πλ<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON>νου", "PE.Views.ShapeSettingsAdvanced.textTitle": "Σχήμα - Προηγμένες Ρυθμίσεις", "PE.Views.ShapeSettingsAdvanced.textTop": "Επάνω", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Πάνω Αριστερή Γωνία", "PE.Views.ShapeSettingsAdvanced.textVertical": "Κατακόρυφος", "PE.Views.ShapeSettingsAdvanced.textVertically": "Κατακόρυφα", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Πλάτη & Βέλη", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "Κανένα", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Προειδοποίηση", "PE.Views.SignatureSettings.strDelete": "Αφαίρεση Υπογραφής", "PE.Views.SignatureSettings.strDetails": "Λεπτομέρειες Υπογραφής", "PE.Views.SignatureSettings.strInvalid": "Μη έγκυρες υπογραφές", "PE.Views.SignatureSettings.strSign": "Σύμβολο", "PE.Views.SignatureSettings.strSignature": "Υπογραφή", "PE.Views.SignatureSettings.strValid": "Έγκυρες υπογραφές", "PE.Views.SignatureSettings.txtContinueEditing": "Επεξεργασ<PERSON>α ούτως ή άλλως", "PE.Views.SignatureSettings.txtEditWarning": "Η επεξεργασία θα αφαιρέσει τις υπογραφές από την παρουσίαση.<br>Θέλετε σίγουρα να συνεχίσετε;", "PE.Views.SignatureSettings.txtRemoveWarning": "Θέλετε να αφαιρέσετε αυτή την υπογραφή;<br>Δεν μπορεί να αναιρεθεί.", "PE.Views.SignatureSettings.txtSigned": "Έγκυρες υπογραφές προστέθηκαν στην παρουσίαση. Η παρουσίαση προστατεύεται από επεξεργασία.", "PE.Views.SignatureSettings.txtSignedInvalid": "Κάποιες από τις ψηφιακ<PERSON>ς υπογραφές στην παρουσίαση είναι άκυρες ή δεν επαληθεύτηκαν. Η παρουσίαση έχει προστασία τροποποίησης.", "PE.Views.SlideSettings.strBackground": "Χρώμα παρασκηνίου", "PE.Views.SlideSettings.strColor": "Χρώμα", "PE.Views.SlideSettings.strDateTime": "Εμφάνιση Ημερομηνίας και Ώρας", "PE.Views.SlideSettings.strFill": "Παρασκήνιο", "PE.Views.SlideSettings.strForeground": "Χρώμα προσκηνίου", "PE.Views.SlideSettings.strPattern": "Μοτίβο", "PE.Views.SlideSettings.strSlideNum": "Εμφάνιση Αριθμού Διαφάνειας", "PE.Views.SlideSettings.strTransparency": "Αδιαφάνεια", "PE.Views.SlideSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "PE.Views.SlideSettings.textAngle": "Γωνία", "PE.Views.SlideSettings.textColor": "Γέμισμα με Χρώμα", "PE.Views.SlideSettings.textDirection": "Κατεύθυνση", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textFromFile": "Από Αρχείο", "PE.Views.SlideSettings.textFromStorage": "Από Αποθηκευτικ<PERSON>ο", "PE.Views.SlideSettings.textFromUrl": "Από διεύθυνση URL", "PE.Views.SlideSettings.textGradient": "Σημεία διαβάθμισης", "PE.Views.SlideSettings.textGradientFill": "Βαθμωτό Γέμισμα", "PE.Views.SlideSettings.textImageTexture": "Εικόνα ή Υφή", "PE.Views.SlideSettings.textLinear": "Γραμμικός", "PE.Views.SlideSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON>α", "PE.Views.SlideSettings.textPatternFill": "Μοτίβο", "PE.Views.SlideSettings.textPosition": "Θέση", "PE.Views.SlideSettings.textRadial": "Ακτινικ<PERSON>ς", "PE.Views.SlideSettings.textReset": "Επαναφορά Αλλαγών", "PE.Views.SlideSettings.textSelectImage": "Επιλογή Εικόνας", "PE.Views.SlideSettings.textSelectTexture": "Επιλογή", "PE.Views.SlideSettings.textStretch": "Έκταση", "PE.Views.SlideSettings.textStyle": "Τεχνοτροπία", "PE.Views.SlideSettings.textTexture": "Από Υφή", "PE.Views.SlideSettings.textTile": "Πλακίδιο", "PE.Views.SlideSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "PE.Views.SlideSettings.txtBrownPaper": "Καφέ <PERSON>", "PE.Views.SlideSettings.txtCanvas": "Καμβάς", "PE.Views.SlideSettings.txtCarton": "Χ<PERSON>ρτόνι", "PE.Views.SlideSettings.txtDarkFabric": "Σκούρο Ύφασμα", "PE.Views.SlideSettings.txtGrain": "Κόκκος", "PE.Views.SlideSettings.txtGranite": "Γραν<PERSON>της", "PE.Views.SlideSettings.txtGreyPaper": "Γκρι <PERSON>", "PE.Views.SlideSettings.txtKnit": "Πλέκω", "PE.Views.SlideSettings.txtLeather": "Δέρμα", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "Ξύλο", "PE.Views.SlideshowSettings.textLoop": "Αέναη επανάληψη μέχρι να πατηθεί το πλήκτρο 'Esc'", "PE.Views.SlideshowSettings.textTitle": "Εμφάνιση Ρυθμίσεων", "PE.Views.SlideSizeSettings.strLandscape": "Οριζόντιος", "PE.Views.SlideSizeSettings.strPortrait": "Κατακόρυφος", "PE.Views.SlideSizeSettings.textHeight": "Ύψος", "PE.Views.SlideSizeSettings.textSlideOrientation": "Προσανατ<PERSON>λισμός Διαφάνειας", "PE.Views.SlideSizeSettings.textSlideSize": "Μέγεθος Διαφάνειας", "PE.Views.SlideSizeSettings.textTitle": "Ρυθμίσεις Μεγέθους Διαφάνειας", "PE.Views.SlideSizeSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Διαφάνειες 35mm", "PE.Views.SlideSizeSettings.txtA3": "Α3 Χαρτί (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Χαρτί (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Χαρ<PERSON><PERSON> B4 (ICO) (250x353 χιλ)", "PE.Views.SlideSizeSettings.txtB5": "Χαρ<PERSON><PERSON> B5 (ICO) (176x250 χιλ)", "PE.Views.SlideSizeSettings.txtBanner": "Πινακίδα", "PE.Views.SlideSizeSettings.txtCustom": "Προσαρμοσμένο", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON><PERSON><PERSON><PERSON> (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Χαρτί Letter (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Διαφάνεια", "PE.Views.SlideSizeSettings.txtStandard": "Τυπικ<PERSON> (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Ευρεία οθόνη", "PE.Views.Statusbar.goToPageText": "Μετάβαση στη Διαφάνεια", "PE.Views.Statusbar.pageIndexText": "Διαφάνεια {0} από {1}", "PE.Views.Statusbar.textShowBegin": "Προβολή από την Αρχή", "PE.Views.Statusbar.textShowCurrent": "Προβολή από την Τρέχουσα Διαφάνεια", "PE.Views.Statusbar.textShowPresenterView": "Προβολή Οπτικής <PERSON>ων<PERSON>ς Παρουσιαστή", "PE.Views.Statusbar.tipAccessRights": "Διαχείριση δικαιωμάτων πρόσβασης εγγράφου", "PE.Views.Statusbar.tipFitPage": "Προσαρμογή στη διαφάνεια", "PE.Views.Statusbar.tipFitWidth": "Προσαρμογή στο πλάτος", "PE.Views.Statusbar.tipPreview": "Εκκίνηση παρουσίασης", "PE.Views.Statusbar.tipSetLang": "Ορισμ<PERSON>ς γλώσσας κειμένου", "PE.Views.Statusbar.tipZoomFactor": "Εστίαση", "PE.Views.Statusbar.tipZoomIn": "Μεγέθυνση", "PE.Views.Statusbar.tipZoomOut": "Σμίκρυνση", "PE.Views.Statusbar.txtPageNumInvalid": "Μη έγκυρος αριθμός διαφάνειας", "PE.Views.TableSettings.deleteColumnText": "Διαγρα<PERSON><PERSON> Στήλης", "PE.Views.TableSettings.deleteRowText": "Διαγραφή Γραμμής", "PE.Views.TableSettings.deleteTableText": "Διαγρα<PERSON>ή Πίνακα", "PE.Views.TableSettings.insertColumnLeftText": "Εισαγω<PERSON><PERSON>ήλης Αριστερά", "PE.Views.TableSettings.insertColumnRightText": "Εισαγω<PERSON><PERSON> Στήλης Δεξιά", "PE.Views.TableSettings.insertRowAboveText": "Εισαγωγή Γραμμής Από Πάνω", "PE.Views.TableSettings.insertRowBelowText": "Εισαγωγή Γραμμής Απ<PERSON> Κάτω", "PE.Views.TableSettings.mergeCellsText": "Συγχώνευση Κελιών", "PE.Views.TableSettings.selectCellText": "Επιλογή Κελιού", "PE.Views.TableSettings.selectColumnText": "Επιλογ<PERSON>τ<PERSON>λης", "PE.Views.TableSettings.selectRowText": "Επιλογή Γραμμής", "PE.Views.TableSettings.selectTableText": "Επιλογή Πίνακα", "PE.Views.TableSettings.splitCellsText": "Διαίρεση Κελιού...", "PE.Views.TableSettings.splitCellTitleText": "Διαίρεση Κελιού", "PE.Views.TableSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "PE.Views.TableSettings.textBackColor": "Χρώμα παρασκηνίου", "PE.Views.TableSettings.textBanded": "Με Εναλλαγή Σκίασης", "PE.Views.TableSettings.textBorderColor": "Χρώμα", "PE.Views.TableSettings.textBorders": "Τεχνοτροπία Περιγραμμάτων", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "Στήλες", "PE.Views.TableSettings.textDistributeCols": "Κατανο<PERSON><PERSON> στηλών", "PE.Views.TableSettings.textDistributeRows": "Κατανομή γραμμών", "PE.Views.TableSettings.textEdit": "Γραμμές & Στήλες", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON><PERSON><PERSON> πρότυπα", "PE.Views.TableSettings.textFirst": "Πρώτη", "PE.Views.TableSettings.textHeader": "Κεφαλίδα", "PE.Views.TableSettings.textHeight": "Ύψος", "PE.Views.TableSettings.textLast": "Τελευταία", "PE.Views.TableSettings.textRows": "Γραμμές", "PE.Views.TableSettings.textSelectBorders": "Επιλέξτε τα περιγράμματα που θέλετε να αλλάξετε εφαρμόζοντας την ανωτέρω επιλεγμένη τεχνοτροπία", "PE.Views.TableSettings.textTemplate": "Επιλογή Από Πρότυπο", "PE.Views.TableSettings.textTotal": "Σύνολο", "PE.Views.TableSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Ορισμ<PERSON>ς εξωτερικού περιγράμματος και όλων των εσωτερικών γραμμών", "PE.Views.TableSettings.tipBottom": "Ορισμός μόνο εξωτερικού κάτω περιγράμματος", "PE.Views.TableSettings.tipInner": "Ορισμός μόνο των εσωτερικών γραμμών", "PE.Views.TableSettings.tipInnerHor": "Ορισμός μόνο των οριζόντιων εσωτερικών γραμμών", "PE.Views.TableSettings.tipInnerVert": "Ορισμός μόνο των κατακόρυφων εσωτερικών γραμμών", "PE.Views.TableSettings.tipLeft": "Ορισμός μόνο του εξωτερικού αριστερού περιγράμματος", "PE.Views.TableSettings.tipNone": "<PERSON><PERSON><PERSON><PERSON><PERSON> κανένα περίγραμμα", "PE.Views.TableSettings.tipOuter": "Ορισμός μόνο του εξωτερικού περιγράμματος", "PE.Views.TableSettings.tipRight": "Ορισμός μόνο του εξωτερικού δεξιού περιγράμματος", "PE.Views.TableSettings.tipTop": "Ορισμός μόνο του εξωτερικού πάνω περιγράμματος", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "PE.Views.TableSettings.txtTable_Accent": "Τόνος", "PE.Views.TableSettings.txtTable_DarkStyle": "Σκούρα Τεχνοτροπία", "PE.Views.TableSettings.txtTable_LightStyle": "Φωτεινή Τεχνοτροπία", "PE.Views.TableSettings.txtTable_MediumStyle": "Ενδιάμεση Τεχνοτροπία", "PE.Views.TableSettings.txtTable_NoGrid": "<PERSON><PERSON><PERSON><PERSON><PERSON>έγ<PERSON>", "PE.Views.TableSettings.txtTable_NoStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON>χνοτροπία", "PE.Views.TableSettings.txtTable_TableGrid": "Πλέγμα Πίνακα", "PE.Views.TableSettings.txtTable_ThemedStyle": "Θεματική Τεχνοτροπία", "PE.Views.TableSettingsAdvanced.textAlt": "Εναλλακτικό Κείμενο", "PE.Views.TableSettingsAdvanced.textAltDescription": "Περιγραφή", "PE.Views.TableSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, αυτό<PERSON>α<PERSON><PERSON> σχήμα, γράφημα ή πίνακα.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Τίτλος", "PE.Views.TableSettingsAdvanced.textBottom": "Κάτω", "PE.Views.TableSettingsAdvanced.textCenter": "Κέντρο", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Χρήση προεπιλεγμένων περιθωρίων", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Προεπιλεγμένα Περιθώρια", "PE.Views.TableSettingsAdvanced.textFrom": "Από", "PE.Views.TableSettingsAdvanced.textHeight": "Ύψος", "PE.Views.TableSettingsAdvanced.textHorizontal": "Οριζόντιος", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Σταθερές αναλογίες", "PE.Views.TableSettingsAdvanced.textLeft": "Αριστερά", "PE.Views.TableSettingsAdvanced.textMargins": "Περιθώρια Κελιού", "PE.Views.TableSettingsAdvanced.textPlacement": "Τοποθέτηση", "PE.Views.TableSettingsAdvanced.textPosition": "Θέση", "PE.Views.TableSettingsAdvanced.textRight": "Δεξιά", "PE.Views.TableSettingsAdvanced.textSize": "Μέγεθος", "PE.Views.TableSettingsAdvanced.textTitle": "Πίνακας - Προηγμένες Ρυθμίσεις", "PE.Views.TableSettingsAdvanced.textTop": "Επάνω", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Πάνω Αριστερή Γωνία", "PE.Views.TableSettingsAdvanced.textVertical": "Κατακόρυφος", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Περιθώρια", "PE.Views.TextArtSettings.strBackground": "Χρώμα παρασκηνίου", "PE.Views.TextArtSettings.strColor": "Χρώμα", "PE.Views.TextArtSettings.strFill": "Γέμισμα", "PE.Views.TextArtSettings.strForeground": "Χρώμα προσκηνίου", "PE.Views.TextArtSettings.strPattern": "Μοτίβο", "PE.Views.TextArtSettings.strSize": "Μέγεθος", "PE.Views.TextArtSettings.strStroke": "Πινελιά", "PE.Views.TextArtSettings.strTransparency": "Αδιαφάνεια", "PE.Views.TextArtSettings.strType": "Τύπος", "PE.Views.TextArtSettings.textAngle": "Γωνία", "PE.Views.TextArtSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0pt και 1584pt.", "PE.Views.TextArtSettings.textColor": "Γέμισμα με Χρώμα", "PE.Views.TextArtSettings.textDirection": "Κατεύθυνση", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textFromFile": "Από Αρχείο", "PE.Views.TextArtSettings.textFromUrl": "Από διεύθυνση URL", "PE.Views.TextArtSettings.textGradient": "Σημεία διαβάθμισης", "PE.Views.TextArtSettings.textGradientFill": "Βαθμωτό Γέμισμα", "PE.Views.TextArtSettings.textImageTexture": "Εικόνα ή Υφή", "PE.Views.TextArtSettings.textLinear": "Γραμμικός", "PE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON>α", "PE.Views.TextArtSettings.textPatternFill": "Μοτίβο", "PE.Views.TextArtSettings.textPosition": "Θέση", "PE.Views.TextArtSettings.textRadial": "Ακτινικ<PERSON>ς", "PE.Views.TextArtSettings.textSelectTexture": "Επιλογή", "PE.Views.TextArtSettings.textStretch": "Έκταση", "PE.Views.TextArtSettings.textStyle": "Τεχνοτροπία", "PE.Views.TextArtSettings.textTemplate": "Πρότυπο", "PE.Views.TextArtSettings.textTexture": "Από Υφή", "PE.Views.TextArtSettings.textTile": "Πλακίδιο", "PE.Views.TextArtSettings.textTransform": "Μεταμόρφωση", "PE.Views.TextArtSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "PE.Views.TextArtSettings.txtBrownPaper": "Καφέ <PERSON>", "PE.Views.TextArtSettings.txtCanvas": "Καμβάς", "PE.Views.TextArtSettings.txtCarton": "Χ<PERSON>ρτόνι", "PE.Views.TextArtSettings.txtDarkFabric": "Σκούρο Ύφασμα", "PE.Views.TextArtSettings.txtGrain": "Κόκκος", "PE.Views.TextArtSettings.txtGranite": "Γραν<PERSON>της", "PE.Views.TextArtSettings.txtGreyPaper": "Γκρι <PERSON>", "PE.Views.TextArtSettings.txtKnit": "Πλέκω", "PE.Views.TextArtSettings.txtLeather": "Δέρμα", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "Ξύλο", "PE.Views.Toolbar.capAddSlide": "Προσθήκη Διαφάνειας", "PE.Views.Toolbar.capBtnAddComment": "Προσθήκη Σχολίου", "PE.Views.Toolbar.capBtnComment": "Σχόλιο", "PE.Views.Toolbar.capBtnDateTime": "Ημερομηνία & Ώρα", "PE.Views.Toolbar.capBtnInsHeader": "Υποσέλιδο", "PE.Views.Toolbar.capBtnInsSymbol": "Σύμβολο", "PE.Views.Toolbar.capBtnSlideNum": "Αριθμός Διαφάνειας", "PE.Views.Toolbar.capInsertAudio": "Ήχος", "PE.Views.Toolbar.capInsertChart": "Γράφημα", "PE.Views.Toolbar.capInsertEquation": "Εξίσωση", "PE.Views.Toolbar.capInsertHyperlink": "Υπερσύνδεσμος", "PE.Views.Toolbar.capInsertImage": "Εικόνα", "PE.Views.Toolbar.capInsertShape": "Σχήμα", "PE.Views.Toolbar.capInsertTable": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "PE.Views.Toolbar.capInsertText": "Πλ<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON>νου", "PE.Views.Toolbar.capInsertVideo": "Βίντεο", "PE.Views.Toolbar.capTabFile": "Αρχείο", "PE.Views.Toolbar.capTabHome": "Αρχική", "PE.Views.Toolbar.capTabInsert": "Εισαγωγή", "PE.Views.Toolbar.mniCapitalizeWords": "Κεφαλαία Πρώτα Γράμματα", "PE.Views.Toolbar.mniCustomTable": "Εισαγωγή Προσαρμοσμένου Πίνακα", "PE.Views.Toolbar.mniImageFromFile": "Εικόνα από Αρχείο", "PE.Views.Toolbar.mniImageFromStorage": "Εικόνα από Αποθηκευτικ<PERSON>ο", "PE.Views.Toolbar.mniImageFromUrl": "Εικόνα από διεύθυνση URL", "PE.Views.Toolbar.mniInsertSSE": "Εισαγωγ<PERSON> Φύλλο Εργασίας", "PE.Views.Toolbar.mniLowerCase": "πεζά", "PE.Views.Toolbar.mniSentenceCase": "Πεζά-κεφα<PERSON><PERSON><PERSON><PERSON> πρότασης.", "PE.Views.Toolbar.mniSlideAdvanced": "Προηγμένες Ρυθμίσεις", "PE.Views.Toolbar.mniSlideStandard": "Τυπικ<PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "Ευρεία οθόνη (16:9)", "PE.Views.Toolbar.mniToggleCase": "εΝΑΛΛΑΓΗ πΕΖΩΝ-κΕΦΑΛΑΙΩΝ", "PE.Views.Toolbar.mniUpperCase": "ΚΕΦΑΛΑΙΑ", "PE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON>α", "PE.Views.Toolbar.textAlignBottom": "Στοίχιση κειμένου κάτω", "PE.Views.Toolbar.textAlignCenter": "Κεντράρισμα κειμένου", "PE.Views.Toolbar.textAlignJust": "Πλή<PERSON><PERSON><PERSON>η", "PE.Views.Toolbar.textAlignLeft": "Στοίχιση κειμένου αριστερά", "PE.Views.Toolbar.textAlignMiddle": "Στοίχιση κειμένου στη μέση", "PE.Views.Toolbar.textAlignRight": "Στοίχιση κειμένου δεξιά", "PE.Views.Toolbar.textAlignTop": "Στοίχιση κειμένου επάνω", "PE.Views.Toolbar.textArrangeBack": "Μεταφ<PERSON><PERSON><PERSON> στο Παρασκήνιο", "PE.Views.Toolbar.textArrangeBackward": "Μεταφορά Προς τα Πίσω", "PE.Views.Toolbar.textArrangeForward": "Μεταφορά Προς τα Εμπρός", "PE.Views.Toolbar.textArrangeFront": "Μεταφ<PERSON><PERSON><PERSON> στο Προσκήνιο", "PE.Views.Toolbar.textBold": "Έντονα", "PE.Views.Toolbar.textColumnsCustom": "Προσαρμοσμένες Στήλες", "PE.Views.Toolbar.textColumnsOne": "Μία Στήλη", "PE.Views.Toolbar.textColumnsThree": "Τρεις <PERSON>ήλες", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON><PERSON>ς", "PE.Views.Toolbar.textItalic": "Πλάγια", "PE.Views.Toolbar.textListSettings": "Ρυθμίσεις Λίστας", "PE.Views.Toolbar.textRecentlyUsed": "Πρόσφατα Χρησιμοποιημένα", "PE.Views.Toolbar.textShapeAlignBottom": "Στοίχιση Κάτω", "PE.Views.Toolbar.textShapeAlignCenter": "Στοίχιση στο Κέντρο", "PE.Views.Toolbar.textShapeAlignLeft": "Στοίχιση Αριστερά", "PE.Views.Toolbar.textShapeAlignMiddle": "Στοίχιση στη Μέση", "PE.Views.Toolbar.textShapeAlignRight": "Στοίχιση Δεξιά", "PE.Views.Toolbar.textShapeAlignTop": "Στοίχιση Πάνω", "PE.Views.Toolbar.textShowBegin": "Προβολή από την Αρχή", "PE.Views.Toolbar.textShowCurrent": "Προβολή από την Τρέχουσα Διαφάνεια", "PE.Views.Toolbar.textShowPresenterView": "Προβολή Οπτικής <PERSON>ων<PERSON>ς Παρουσιαστή", "PE.Views.Toolbar.textShowSettings": "Εμφάνιση Ρυθμίσεων", "PE.Views.Toolbar.textStrikeout": "Διακριτική διαγραφή", "PE.Views.Toolbar.textSubscript": "Δείκτης", "PE.Views.Toolbar.textSuperscript": "Εκθέτης", "PE.Views.Toolbar.textTabAnimation": "Animation", "PE.Views.Toolbar.textTabCollaboration": "Συνεργασία", "PE.Views.Toolbar.textTabFile": "Αρχείο", "PE.Views.Toolbar.textTabHome": "Αρχική", "PE.Views.Toolbar.textTabInsert": "Εισαγωγή", "PE.Views.Toolbar.textTabProtect": "Προστασία", "PE.Views.Toolbar.textTabTransitions": "Μεταβάσεις", "PE.Views.Toolbar.textTabView": "Προβολή", "PE.Views.Toolbar.textTitleError": "Σφάλμα", "PE.Views.Toolbar.textUnderline": "Υπογράμμιση", "PE.Views.Toolbar.tipAddSlide": "Προσθήκη διαφάνειας", "PE.Views.Toolbar.tipBack": "Πίσω", "PE.Views.Toolbar.tipChangeCase": "Αλλαγ<PERSON> πεζών-κεφαλαίων", "PE.Views.Toolbar.tipChangeChart": "Αλλαγή τύπου γραφήματος", "PE.Views.Toolbar.tipChangeSlide": "Αλλαγή διάταξης διαφάνειας", "PE.Views.Toolbar.tipClearStyle": "Εκκαθάριση τεχνοτροπίας", "PE.Views.Toolbar.tipColorSchemas": "Αλλαγή χρωματικού σχεδίου", "PE.Views.Toolbar.tipColumns": "Εισαγω<PERSON><PERSON> στηλών", "PE.Views.Toolbar.tipCopy": "Αντιγραφή", "PE.Views.Toolbar.tipCopyStyle": "Αντιγραφή τεχνοτροπίας", "PE.Views.Toolbar.tipDateTime": "Εισαγωγή τρέχουσας ημερομηνίας και ώρας", "PE.Views.Toolbar.tipDecFont": "Μείωση μεγέθους γραμματοσειράς", "PE.Views.Toolbar.tipDecPrLeft": "Μείωση εσοχής", "PE.Views.Toolbar.tipEditHeader": "Επεξεργασία υποσέλιδου", "PE.Views.Toolbar.tipFontColor": "Χρώμα γραμματοσειράς", "PE.Views.Toolbar.tipFontName": "Γραμματοσειρά", "PE.Views.Toolbar.tipFontSize": "Μέγ<PERSON><PERSON>ος γραμματοσειράς", "PE.Views.Toolbar.tipHAligh": "Οριζόντια στοίχιση", "PE.Views.Toolbar.tipHighlightColor": "Χρώμα επισήμανσης", "PE.Views.Toolbar.tipIncFont": "Αύξηση μεγέθους γραμματοσειράς", "PE.Views.Toolbar.tipIncPrLeft": "Αύξηση εσοχής", "PE.Views.Toolbar.tipInsertAudio": "Εισαγωγ<PERSON> ήχου", "PE.Views.Toolbar.tipInsertChart": "Εισαγωγή γραφήματος", "PE.Views.Toolbar.tipInsertEquation": "Εισαγωγή εξίσωσης", "PE.Views.Toolbar.tipInsertHyperlink": "Προσθήκη υπερσυνδέσμου", "PE.Views.Toolbar.tipInsertImage": "Εισαγωγή εικόνας", "PE.Views.Toolbar.tipInsertShape": "Εισαγωγ<PERSON> αυτόματου σχήματος", "PE.Views.Toolbar.tipInsertSymbol": "Εισαγωγή συμβόλου", "PE.Views.Toolbar.tipInsertTable": "Εισαγωγή πίνακα", "PE.Views.Toolbar.tipInsertText": "Εισαγωγή πλαισίου κειμένου", "PE.Views.Toolbar.tipInsertTextArt": "Εισαγωγή Τεχνοκειμένου", "PE.Views.Toolbar.tipInsertVideo": "Εισαγωγ<PERSON> βίντεο", "PE.Views.Toolbar.tipLineSpace": "Διάστιχο", "PE.Views.Toolbar.tipMarkers": "Κουκκίδες", "PE.Views.Toolbar.tipMarkersArrow": "Κου<PERSON><PERSON><PERSON><PERSON>ς βέλη", "PE.Views.Toolbar.tipMarkersCheckmark": "Κουκίδες τσεκαρίσματος", "PE.Views.Toolbar.tipMarkersDash": "Κουκ<PERSON>δες παύλας", "PE.Views.Toolbar.tipMarkersFRhombus": "Κου<PERSON><PERSON>δες πλήρους ρόμβου", "PE.Views.Toolbar.tipMarkersFRound": "Κουκίδες πλήρεις στρογγυλές", "PE.Views.Toolbar.tipMarkersFSquare": "Κουκίδες πλήρεις τετράγωνες", "PE.Views.Toolbar.tipMarkersHRound": "Κουκ<PERSON>δες κούφιες στρογγυλές", "PE.Views.Toolbar.tipMarkersStar": "Κουκ<PERSON>δες αστέρια", "PE.Views.Toolbar.tipNone": "Κανένα", "PE.Views.Toolbar.tipNumbers": "Αρίθμηση", "PE.Views.Toolbar.tipPaste": "Επικόλληση", "PE.Views.Toolbar.tipPreview": "Εκκίνηση παρουσίασης", "PE.Views.Toolbar.tipPrint": "Εκτύπωση", "PE.Views.Toolbar.tipRedo": "Επανάληψη", "PE.Views.Toolbar.tipSave": "Αποθήκευση", "PE.Views.Toolbar.tipSaveCoauth": "Αποθηκεύστε τις αλλαγές σας για να τις δουν οι άλλοι χρήστες.", "PE.Views.Toolbar.tipShapeAlign": "Στοίχιση σχήματος", "PE.Views.Toolbar.tipShapeArrange": "Τακτοποίηση σχήματος", "PE.Views.Toolbar.tipSlideNum": "Εισαγωγή αριθμού διαφάνειας", "PE.Views.Toolbar.tipSlideSize": "Επιλογή μεγέθους διαφάνειας", "PE.Views.Toolbar.tipSlideTheme": "Θέμα διαφάνειας", "PE.Views.Toolbar.tipUndo": "Αναίρεση", "PE.Views.Toolbar.tipVAligh": "Κατακόρυφη στοίχιση", "PE.Views.Toolbar.tipViewSettings": "Προβολή ρυθμίσεων", "PE.Views.Toolbar.txtDistribHor": "Οριζόντια Κατανομή", "PE.Views.Toolbar.txtDistribVert": "Κατακόρυφη Κατανομή", "PE.Views.Toolbar.txtDuplicateSlide": "Διπλότυπη Διαφάνεια", "PE.Views.Toolbar.txtGroup": "Ομάδα", "PE.Views.Toolbar.txtObjectsAlign": "Στοίχιση Επιλεγμένων Αντικειμένων", "PE.Views.Toolbar.txtScheme1": "Γραφείο", "PE.Views.Toolbar.txtScheme10": "Διάμεσο", "PE.Views.Toolbar.txtScheme11": "Μετρό", "PE.Views.Toolbar.txtScheme12": "Άρθρωμα", "PE.Views.Toolbar.txtScheme13": "Άφθονο", "PE.Views.Toolbar.txtScheme14": "Προεξέχον παράθυρο", "PE.Views.Toolbar.txtScheme15": "Προέλευση", "PE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme17": "Ηλιοστάσιο", "PE.Views.Toolbar.txtScheme18": "Τεχνικό", "PE.Views.Toolbar.txtScheme19": "Ταξίδι", "PE.Views.Toolbar.txtScheme2": "Αποχρώσεις του γκρι", "PE.Views.Toolbar.txtScheme20": "Αστικό", "PE.Views.Toolbar.txtScheme21": "Οίστρος", "PE.Views.Toolbar.txtScheme22": "Νέο <PERSON>αφείο", "PE.Views.Toolbar.txtScheme3": "Άκρο", "PE.Views.Toolbar.txtScheme4": "Άποψη", "PE.Views.Toolbar.txtScheme5": "Κυβικό", "PE.Views.Toolbar.txtScheme6": "Συνάθροιση", "PE.Views.Toolbar.txtScheme7": "Μετοχή", "PE.Views.Toolbar.txtScheme8": "Ροή", "PE.Views.Toolbar.txtScheme9": "<PERSON>υτ<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtSlideAlign": "Στοίχιση στη Διαφάνεια", "PE.Views.Toolbar.txtUngroup": "Κατάργηση ομαδοποίησης", "PE.Views.Transitions.strDelay": "Καθυστέρηση", "PE.Views.Transitions.strDuration": "Διάρκεια", "PE.Views.Transitions.strStartOnClick": "Έναρξη Με Κλικ", "PE.Views.Transitions.textBlack": "Μέσω του Μαύρου", "PE.Views.Transitions.textBottom": "Κάτω", "PE.Views.Transitions.textBottomLeft": "Κάτω-αριστερά", "PE.Views.Transitions.textBottomRight": "Κάτω-δεξιά", "PE.Views.Transitions.textClock": "Ρολόι", "PE.Views.Transitions.textClockwise": "Δεξιόστροφα", "PE.Views.Transitions.textCounterclockwise": "Αριστερόστροφα", "PE.Views.Transitions.textCover": "Εξώφυλλο", "PE.Views.Transitions.textFade": "Σβήσιμο", "PE.Views.Transitions.textHorizontalIn": "Οριζόντιο εσωτερικό", "PE.Views.Transitions.textHorizontalOut": "Οριζόντι<PERSON> εξωτερικό", "PE.Views.Transitions.textLeft": "Αριστερά", "PE.Views.Transitions.textNone": "Κανένα", "PE.Views.Transitions.textPush": "Ώθηση", "PE.Views.Transitions.textRight": "Δεξιά", "PE.Views.Transitions.textSmoothly": "Ομαλή μετάβαση", "PE.Views.Transitions.textSplit": "Διαίρεση", "PE.Views.Transitions.textTop": "Επάνω", "PE.Views.Transitions.textTopLeft": "Επάνω-αριστερά", "PE.Views.Transitions.textTopRight": "Επάνω-δεξιά", "PE.Views.Transitions.textUnCover": "Αποκάλυψη", "PE.Views.Transitions.textVerticalIn": "Κάθετο εσωτερικό", "PE.Views.Transitions.textVerticalOut": "Κάθετο εξωτερικό", "PE.Views.Transitions.textWedge": "Σύζευξη", "PE.Views.Transitions.textWipe": "Εκκαθάριση", "PE.Views.Transitions.textZoom": "Εστίαση", "PE.Views.Transitions.textZoomIn": "Μεγέθυνση", "PE.Views.Transitions.textZoomOut": "Σμίκρυνση", "PE.Views.Transitions.textZoomRotate": "Εστίαση και Περιστροφή", "PE.Views.Transitions.txtApplyToAll": "Εφαρμογή σε όλες τις διαφάνειες", "PE.Views.Transitions.txtParameters": "Παράμετροι", "PE.Views.Transitions.txtPreview": "Προεπισκόπηση", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.textAlwaysShowToolbar": "Να εμφανίζεται πάντα η γραμμή εργαλείων", "PE.Views.ViewTab.textFitToSlide": "Προσαρμογή Στη Διαφάνεια", "PE.Views.ViewTab.textFitToWidth": "Προσαρ<PERSON><PERSON><PERSON><PERSON> Σ<PERSON>ο <PERSON>", "PE.Views.ViewTab.textInterfaceTheme": "Θέμα διεπαφής", "PE.Views.ViewTab.textNotes": "Σημειώσεις", "PE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textStatusBar": "Γραμ<PERSON><PERSON> Κατάστασης", "PE.Views.ViewTab.textZoom": "Εστίαση"}