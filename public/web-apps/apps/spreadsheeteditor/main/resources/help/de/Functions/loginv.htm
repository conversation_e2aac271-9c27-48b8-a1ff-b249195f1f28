﻿<!DOCTYPE html>
<html>
	<head>
		<title>LOGINV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>LOGINV-Funktion</h1>
			<p>Die Funktion <b>LOGINV</b> gehört zur Gruppe der statistischen Funktionen. Sieg gibt Quantile der Lognormalverteilung von <b><em>Wahrsch</em></b> zurück, wobei ln(x) mit den Parametern Mittelwert und Standabwn normal verteilt ist.</p>
			<p>Die Formelsyntax der Funktion <b>LOGINV</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>LOGINV(Wahrsch;Mittelwert;Standabwn)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Wahrsch</em></b> ist die zur Lognormalverteilung gehörige Wahrscheinlichkeit. Ein nummerischer Wert größer als 0, aber kleiner als 1.</p>
				<p style="text-indent: 50px;"><b><em>Mittelwert</em></b> ist der Mittelwert von ln(<b><em>x</em></b>), ein nummerischer Wert.</p>
				<p style="text-indent: 50px;"><b><em>Standabwn</em></b> ist die Standardabweichung von ln(<b><em>x)</em></b>. Ein nummerischer Wert der größer ist als 0.</p>
				<p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>LOGINV</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>LOGINV</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="LOGINV-Funktion" src="../images/loginv.png" /></p>
		</div>
	</body>
</html>