﻿<!DOCTYPE html>
<html>
	<head>
		<title>Rechtschreibprüfung</title>
		<meta charset="utf-8" />
		<meta name="description" content="Spell check the text in your language while editing a presentation" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Rechtschreibprüfung</h1>
			<p>Der <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> bietet Ihnen die Möglichkeit, die Rechtschreibung Ihres Textes in einer bestimmten Sprache zu überprüfen und Fehler während der Bearbeitung zu korrigieren.</p>
			<p class="note">Ab <b>Version 6.3</b> unterstützen die ONLYOFFICE-Editoren das <b>SharedWorker Interface</b> für einen besseren Betrieb ohne großen Speicherverbrauch. Wenn Ihr Browser SharedWorker nicht unterstützt, ist nur <b>Worker</b> aktiv. Weitere Informationen zu SharedWorker finden Sie <a href="https://javascript.plainenglish.io/introduction-to-shared-workers-533d9abe9de3">in diesem Artikel</a>.</p>
			<p>Wählen Sie zunächst die <b>Sprache</b> für Ihre Präsentation aus. Klicken Sie auf der rechten Seite der Statusleiste auf <span class="icon icon-document_language"></span>. Wählen Sie nun im angezeigten Fenster die gewünschte Sprache und klicken Sie auf <b>OK</b>. Die ausgewählte Sprache wird auf die gesamte Präsentation angewandt.</p>
			<p><img alt="Fenster Sprache für die Präsentation festlegen" src="../../../../../../common/main/resources/help/de/images/document_language_window.png" /></p>
			<p>Um für einen beliebigen Textabschnitt in der Präsentation eine <b>andere Sprache auszuwählen</b>, markieren Sie den entsprechenden Abschnitt mit der Maus und klicken Sie anschließend auf das <img alt="Rechtschreibprüfung - Sprachauswahl" src="../images/spellchecking_language.png" /> Menü in der Statusleiste.</p>
            <p>Rechtschreibprüfung <b>aktivieren</b>:</p>
            <ul>
                <li>klicken Sie in der Statusleiste auf das Symbol <div class = "icon icon-spellcheckdeactivated"></div> <b>Rechtschreibprüfung</b> oder</li>
                <li>öffnen Sie in der oberen Symbolleiste die Registerkarte <b>Datei</b> und wählen Sie die Option <b>Erweiterte Einstellungen</b>, setzen Sie das Häkchen in der Box <b>Rechtschreibprüfung aktivieren</b> und klicken Sie auf <b>Übernehmen</b>.</li>
            </ul>
            <p>Falsch geschriebene Wörter werden mit einer roten Linie unterstrichen.</p>
			<p>Klicken Sie mit der rechten Maustaste auf das entsprechende Wort, um das Kontextmenü zu aktivieren, und:</p>
			<ul>
				<li>wählen Sie eine der verfügbaren Varianten aus, um das falsch geschriebene Wort durch die korrekte Rechtschreibung zu ersetzen. Wenn zu viel Möglichkeiten vorliegen, wird die Option <b>Weitere...</b> im Menü angezeigt;</li>
				<li>wählen Sie die Option <b>Ignorieren</b>, um ein Wort zu überspringen und die rote Linie auszublenden oder <b>Alle ignorieren</b>, um ein bestimmtes Fehlerergebnis für den gesamten Text zu überspringen;</li>
				<li>wenn das aktuelle Wort im Wörterbuch fehlt, können Sie es dem benutzerdefinierten Wörterbuch hinzufügen. Dieses Wort wird beim nächsten Mal nicht als Fehler behandelt. Diese Option ist in der <em>Desktop-Version</em> verfügbar;</li>
				<li>wählen Sie für dieses Wort eine andere Sprache.</li>
			</ul>  
            <p><img alt="Rechtschreibprüfung" src="../images/spellchecking_presentation.png" /></p>
            <p>Rechtschreibprüfung <b>deaktivieren</b>:</p>
            <ul>
                <li>klicken Sie in der Statusleiste auf das Symbol <div class = "icon icon-spellcheckactivated"></div> <b>Rechtschreibprüfung</b> oder</li>
                <li>öffnen Sie in der oberen Symbolleiste die Registerkarte <b>Datei</b> und wählen Sie die Option <b>Erweiterte Einstellungen</b>, entfernen Sie das Häkchen in der Box <b>Rechtschreibprüfung aktivieren</b> und klicken Sie auf <b>Übernehmen</b>.</li>
            </ul>
		</div>
	</body>
</html>