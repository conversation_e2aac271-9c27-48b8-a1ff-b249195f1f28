﻿<!DOCTYPE html>
<html>
	<head>
		<title>ISO.OBERGRENZE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ISO.OBERGRENZE-Funktion</h1>
			<p>Die Funktion <b>ISO.OBERGRENZE</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie gibt eine Zahl zurück, die auf die nächste Ganzzahl oder auf das kleinste Vielfache von „Schritt“ gerundet wurde. Die Zahl wird unabhängig von ihrem Vorzeichen immer aufgerundet.</p>
			<p>Die Formelsyntax der Funktion <b>ISO.OBERGRENZE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ISO.OBERGRENZE(Zahl;[Schritt])</em></b></p> 
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist der Wert, der aufgerundet werden soll.</p>
			<p style="text-indent: 50px;"><b><em>Schritt</em></b> ist das optionale Vielfache, auf das der Wert aufgerundet werden soll. Das Argument Schritt ist optional. Wird SCHRITT ausgelassen, beträgt der Standardwert 1. Bei der Festlegung Null, gibt die Funktion 0 wieder.</p>
            <p>Der nummerische Werte wird manuell eingegeben oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>ISO.OBERGRENZE</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken sie auf die Funktion <b>ISO.OBERGRENZE</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ISO.OBERGRENZE-Funktion" src="../images/isoceiling.png" /></p>
		</div>
	</body>
</html>