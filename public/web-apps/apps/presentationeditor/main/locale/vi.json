{"Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn của bạn ở đây", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Nặc danh", "Common.Controllers.ExternalDiagramEditor.textClose": "Đ<PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "<PERSON><PERSON>i tượng bị vô hiệu vì nó đang được chỉnh sửa bởi một người dùng khác.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBar": "Gạch", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLine": "Đường kẻ", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON> b<PERSON>", "Common.define.chartData.textPoint": "XY (Phân tán)", "Common.define.chartData.textStock": "<PERSON><PERSON> phi<PERSON>u", "Common.define.chartData.textSurface": "Bề mặt", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON> có kiểu", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON> t<PERSON>i", "Common.UI.ExtendedColorDialog.textHexErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị thuộc từ 000000 đến FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị số thuộc từ 0 đến 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON> màu", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON> sáng kết quả", "Common.UI.SearchDialog.textMatchCase": "<PERSON>ân biệt chữ hoa chữ thường", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> văn bản thay thế", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> từ khóa của bạn ở đây", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON> và Thay thế", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Chỉ toàn bộ từ", "Common.UI.SearchDialog.txtBtnHideReplace": "Ẩn Thay thế", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON>hay thế", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON> thế tất cả", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.SynchronizeTip.textSynchronize": "Tài liệu đã được thay đổi bởi người dùng khác.<br><PERSON><PERSON> lòng nhấp để lưu thay đổi của bạn và tải lại các cập nhật.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Màu theme", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Đ<PERSON><PERSON>", "Common.UI.Window.noButtonText": "K<PERSON>ô<PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.Window.textError": "Lỗi", "Common.UI.Window.textInformation": "Thông tin", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "địa chỉ:", "Common.Views.About.txtLicensee": "NGƯỜI ĐƯỢC CẤP GIẤY PHÉP", "Common.Views.About.txtLicensor": "NGƯỜI CẤP GIẤY PHÉP", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Được hỗ trợ bởi", "Common.Views.About.txtTel": "ĐT.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "Common.Views.Comments.textAddCommentToDoc": "<PERSON><PERSON><PERSON><PERSON> bình luận vào tài liệu", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Đ<PERSON><PERSON>", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> bình luận của bạn ở đây", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "Common.Views.Comments.textOpenAgain": "Mở lại", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON> lờ<PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON> gi<PERSON>i quy<PERSON>", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, cắt và dán bằng cách sử dụng các nút trên thanh công cụ của trình soạn thảo và các tác vụ trình đơn ngữ cảnh sẽ chỉ được thực hiện trong tab trình soạn thảo này.<br><br> Để sao chép hoặc dán vào hoặc từ các ứng dụng bên ngoài tab trình soạn thảo sử dụng các kết hợp bàn phím sau đây:", "Common.Views.CopyWarningDialog.textTitle": "Sao ché<PERSON>, Cắt và Dán", "Common.Views.CopyWarningDialog.textToCopy": "để sao chép", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "<PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "<PERSON><PERSON> tả<PERSON>...", "Common.Views.DocumentAccessDialog.textTitle": "Cài đặt chia sẻ", "Common.Views.ExternalDiagramEditor.textTitle": "Tr<PERSON><PERSON> chỉnh sửa biểu đồ", "Common.Views.Header.labelCoUsersDescr": "<PERSON>ài liệu hiện đang được chỉnh sửa bởi nhiều người dùng.", "Common.Views.Header.textBack": "<PERSON><PERSON> tới Tài liệu", "Common.Views.Header.textSaveBegin": "<PERSON><PERSON> l<PERSON>...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON> sửa đổi", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON> lưu mọi thay đổi", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON> lưu mọi thay đổi", "Common.Views.Header.textZoom": "<PERSON><PERSON> ph<PERSON>g", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON><PERSON><PERSON> lý quyền truy cập tài liệu", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Chỉnh sửa file hiện tại", "Common.Views.Header.tipPrint": "In file", "Common.Views.Header.tipViewUsers": "<PERSON>em người dùng và quản lý quyền truy cập tài liệu", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON> tên", "Common.Views.ImageFromUrlDialog.textUrl": "Dán URL hình ảnh:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "<PERSON>ạn cần x<PERSON>c đ<PERSON>nh số hàng và cột hợp lệ.", "Common.Views.InsertTableDialog.txtColumns": "Số cột", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON><PERSON> trị lớn nhất cho trường này là {0}.", "Common.Views.InsertTableDialog.txtMinText": "<PERSON><PERSON><PERSON> trị nhỏ nhất cho trường này là {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON> hàng", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON> b<PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "<PERSON><PERSON><PERSON> ngôn ngữ tài liệu", "Common.Views.OpenDialog.txtEncoding": "Mã hóa", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng.", "Common.Views.OpenDialog.txtOpenFile": "Nhập mật khẩu để mở tệp", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtTitle": "Chọn %1 lựa chọn", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON> vệ", "Common.Views.PasswordDialog.txtWarning": "Chú ý: Nếu bạn mất hoặc quên mật khẩu, bạn không thể khôi phục mật khẩu.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON> t<PERSON>", "Common.Views.Plugins.groupCaption": "Plugin", "Common.Views.Plugins.strPlugins": "Plugin", "Common.Views.Plugins.textLoading": "<PERSON><PERSON> t<PERSON>", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Common.Views.Plugins.textStop": "Dừng", "Common.Views.RenameDialog.textName": "Tên file", "Common.Views.RenameDialog.txtInvalidName": "Tên file không đư<PERSON><PERSON> chứa bất kỳ ký tự nào sau đây:", "PE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> trình chi<PERSON>u không tên", "PE.Controllers.LeftMenu.requestEditRightsText": "<PERSON><PERSON><PERSON> c<PERSON>u quyền chỉnh sửa...", "PE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy dữ liệu bạn đang tìm kiếm. Vui lòng điều chỉnh các tùy chọn tìm kiếm của bạn.", "PE.Controllers.Main.applyChangesTextText": "<PERSON><PERSON> tải dữ liệu...", "PE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON> t<PERSON> liệu", "PE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON> quá thời gian chờ chuyển đổi.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON> \"OK\" để trở lại danh sách tài liệu.", "PE.Controllers.Main.criticalErrorTitle": "Lỗi", "PE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> về không thành công.", "PE.Controllers.Main.downloadTextText": "<PERSON><PERSON> tải trình chi<PERSON>u...", "PE.Controllers.Main.downloadTitleText": "<PERSON><PERSON> tả<PERSON> ch<PERSON>", "PE.Controllers.Main.errorAccessDeny": "Bạn đang cố gắng thực hiện hành động mà bạn không có quyền.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> li<PERSON> củ<PERSON> bạn.", "PE.Controllers.Main.errorBadImageUrl": "URL hình ảnh không chính xác", "PE.Controllers.Main.errorCoAuthoringDisconnect": "<PERSON>ất kết nối server. K<PERSON>ông thể chỉnh sửa tài liệu ngay lúc này.", "PE.Controllers.Main.errorConnectToServer": "<PERSON><PERSON><PERSON><PERSON> thể lưu tài liệu. Vui lòng kiểm tra cài đặt kết nối hoặc liên hệ với quản trị viên của bạn.<br><PERSON><PERSON> bạn nhấp vào nút 'OK', bạn sẽ được nhắc tải xuống tài liệu.", "PE.Controllers.Main.errorDatabaseConnection": "Lỗi bên ngoài.<br>Lỗi kết nối cơ sở dữ liệu. <PERSON>ui lòng liên hệ bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "PE.Controllers.Main.errorDataRange": "Phạm vi dữ liệu không ch<PERSON>h xác.", "PE.Controllers.Main.errorDefaultMessage": "Mã lỗi: %1", "PE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON><PERSON> liệu được bảo vệ bằng mật khẩu và không thể mở được.", "PE.Controllers.Main.errorKeyEncrypt": "Key descriptor k<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "PE.Controllers.Main.errorKeyExpire": "Key của descriptor đ<PERSON> hết hạn", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON> không thành công.", "PE.Controllers.Main.errorServerVersion": "<PERSON><PERSON><PERSON> bản trình chỉnh sửa này đã được cập nhật. Trang sẽ được tải lại để áp dụng các thay đổi.", "PE.Controllers.Main.errorSessionAbsolute": "Phiên chỉnh sửa tài liệu đã hết hạn. <PERSON><PERSON> lòng tải lại trang.", "PE.Controllers.Main.errorSessionIdle": "Tài liệu đã không được chỉnh sửa trong một thời gian khá dài. <PERSON>ui lòng tải lại trang.", "PE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON> nối với server bị gi<PERSON> đ<PERSON>. <PERSON><PERSON> lòng tải lại trang.", "PE.Controllers.Main.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "PE.Controllers.Main.errorToken": "To<PERSON> bảo mật tài liệu không đư<PERSON><PERSON> tạo đúng.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "PE.Controllers.Main.errorTokenExpire": "To<PERSON> bảo mật tài liệu đã hết hạn.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "PE.Controllers.Main.errorUpdateVersion": "<PERSON><PERSON><PERSON> bản file này đã được thay đổi. Trang này sẽ được tải lại.", "PE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON><PERSON> thể truy cập file ngay lúc này.", "PE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON> vư<PERSON>t quá số người dùng đ<PERSON><PERSON><PERSON> phép của gói dịch vụ này", "PE.Controllers.Main.errorViewerDisconnect": "<PERSON><PERSON>t kết nối. Bạn vẫn có thể xem tài liệu,<br>nhưng không thể tải về hoặc in cho đến khi kết nối được khôi phục.", "PE.Controllers.Main.leavePageText": "Bạn có những thay đổi chưa lưu trong bản trình chiếu này. <PERSON><PERSON><PERSON><PERSON> vào \"Ở lại Trang này\", <PERSON><PERSON> <PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" để lưu chúng. <PERSON><PERSON><PERSON><PERSON> vào \"Rời trang này\" để bỏ tất cả các thay đổi chưa lưu.", "PE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON> tải dữ liệu...", "PE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON> t<PERSON> liệu", "PE.Controllers.Main.loadFontTextText": "<PERSON><PERSON> tải dữ liệu...", "PE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON> t<PERSON> liệu", "PE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "PE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "PE.Controllers.Main.loadImageTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "PE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "PE.Controllers.Main.loadingDocumentTextText": "<PERSON><PERSON> tải trình chi<PERSON>u...", "PE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON> tải trình chi<PERSON>u", "PE.Controllers.Main.loadThemeTextText": "<PERSON><PERSON> tả<PERSON> theme...", "PE.Controllers.Main.loadThemeTitleText": "<PERSON><PERSON> t<PERSON> Theme", "PE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "PE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi mở file", "PE.Controllers.Main.openTextText": "<PERSON><PERSON> mở trình chiếu...", "PE.Controllers.Main.openTitleText": "<PERSON><PERSON> mở Trình chi<PERSON>u", "PE.Controllers.Main.printTextText": "<PERSON><PERSON> in bản trình chiếu...", "PE.Controllers.Main.printTitleText": "<PERSON><PERSON> in bản trình chiếu", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON> l<PERSON>", "PE.Controllers.Main.requestEditFailedMessageText": "Hiện tại có ai đó đang chỉnh sửa bản trình chiếu này. <PERSON><PERSON> lòng thử lại sau.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> bị từ chối", "PE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi lưu file", "PE.Controllers.Main.saveTextText": "<PERSON><PERSON> l<PERSON>u bản trình chi<PERSON>...", "PE.Controllers.Main.saveTitleText": "<PERSON><PERSON> l<PERSON> bản trình chi<PERSON>u", "PE.Controllers.Main.splitDividerErrorText": "Số hàng phải là ước của %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Số cột phải nhỏ hơn %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "S<PERSON> hàng phải nhỏ hơn %1.", "PE.Controllers.Main.textAnonymous": "Nặc danh", "PE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON> c<PERSON>p trang web", "PE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> lưu mọi thay đổi", "PE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON><PERSON> để đóng phần đầu", "PE.Controllers.Main.textContactUs": "<PERSON><PERSON><PERSON> hệ bộ phận bán hàng", "PE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON> tải trình chi<PERSON>u", "PE.Controllers.Main.textNoLicenseTitle": "<PERSON><PERSON><PERSON> bản mã nguồn mở ONLYOFFICE", "PE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON> d<PERSON>", "PE.Controllers.Main.textStrict": "<PERSON><PERSON> độ nghiêm ngặt", "PE.Controllers.Main.textText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textTryUndoRedo": "<PERSON><PERSON><PERSON> chức năng Hoàn tác/<PERSON>à<PERSON> lại bị vô hiệu hóa cho chế độ đồng chỉnh sửa Nhanh.<br>Nhấp vào nút 'Chế độ nghiêm ngặt' để chuyển sang chế độ đồng chỉnh sửa Nghiêm ngặt để chỉnh sửa các file mà không có sự can thiệp của người dùng khác và gửi các thay đổi của bạn chỉ sau khi bạn đã lưu. Bạn có thể chuyển đổi giữa các chế độ đồng chỉnh sửa bằng cách sử dụng Cài đặt Nâng cao trình biên tập.", "PE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> hết hạn", "PE.Controllers.Main.titleServerVersion": "<PERSON><PERSON> cập nhật trình chỉnh sửa", "PE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON> bản của bạn ở đây", "PE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON> d<PERSON>ng c<PERSON> bản", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "Callout", "PE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON><PERSON> đồ", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Ngày và giờ", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Tiêu đề biểu đồ", "PE.Controllers.Main.txtEditingMode": "Đặt chế độ chỉnh sửa...", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON> tên có hình vẽ", "PE.Controllers.Main.txtFooter": "Footer", "PE.Controllers.Main.txtHeader": "Header", "PE.Controllers.Main.txtImage": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLines": "Đường kẻ", "PE.Controllers.Main.txtLoading": "<PERSON><PERSON> tả<PERSON>...", "PE.Controllers.Main.txtMath": "<PERSON><PERSON>", "PE.Controllers.Main.txtMedia": "<PERSON><PERSON><PERSON><PERSON> tiện truyền thông", "PE.Controllers.Main.txtNeedSynchronize": "<PERSON><PERSON><PERSON> c<PERSON> cập nh<PERSON>t", "PE.Controllers.Main.txtPicture": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON> chữ nhật", "PE.Controllers.Main.txtSeries": "Chuỗi", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON> b<PERSON>", "PE.Controllers.Main.txtSldLtTBlank": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChart": "<PERSON><PERSON><PERSON><PERSON> đồ", "PE.Controllers.Main.txtSldLtTChartAndTx": "<PERSON><PERSON><PERSON><PERSON> đồ và <PERSON>n bản", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art và V<PERSON>n b<PERSON>n", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art và Văn bản d<PERSON>c", "PE.Controllers.Main.txtSldLtTCust": "Tuỳ chỉnh", "PE.Controllers.Main.txtSldLtTDgm": "Sơ đồ", "PE.Controllers.Main.txtSldLtTFourObj": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON>", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Phương tiện truyền thông và Văn bản", "PE.Controllers.Main.txtSldLtTObj": "Ti<PERSON><PERSON> đề và Đối tượng", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "<PERSON><PERSON><PERSON> tượng và Hai đối tượng", "PE.Controllers.Main.txtSldLtTObjAndTx": "<PERSON><PERSON><PERSON> t<PERSON> và <PERSON> bản", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> trên <PERSON>n", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> tư<PERSON> và <PERSON> thích", "PE.Controllers.Main.txtSldLtTPicTx": "Hình <PERSON> và Chú thích", "PE.Controllers.Main.txtSldLtTSecHead": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PE.Controllers.Main.txtSldLtTTitleOnly": "Chỉ Tiêu đề", "PE.Controllers.Main.txtSldLtTTwoColTx": "<PERSON> cột v<PERSON><PERSON> bản", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON> đối tư<PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "<PERSON> đối tượng và Đối tượng", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Hai <PERSON><PERSON> tư<PERSON> và <PERSON>n bản", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "<PERSON><PERSON> tượng trên <PERSON> bản", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "<PERSON>ăn bản và hai Đố<PERSON> tượng", "PE.Controllers.Main.txtSldLtTTx": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTxAndChart": "<PERSON><PERSON><PERSON> bản và Biểu đồ", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "<PERSON><PERSON><PERSON> bản v<PERSON>lip <PERSON>", "PE.Controllers.Main.txtSldLtTTxAndMedia": "<PERSON><PERSON><PERSON> bản và <PERSON>ơng tiện truyền thông", "PE.Controllers.Main.txtSldLtTTxAndObj": "<PERSON><PERSON><PERSON> bản và <PERSON> tư<PERSON>", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "<PERSON><PERSON><PERSON> bản và <PERSON> đối tượng", "PE.Controllers.Main.txtSldLtTTxOverObj": "<PERSON><PERSON><PERSON> bản trên <PERSON> t<PERSON>", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "<PERSON>i<PERSON><PERSON> đề và Văn bản dọc", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Tiêu đề dọc và Văn bản bên trên Bi<PERSON>u đồ", "PE.Controllers.Main.txtSldLtTVertTx": "<PERSON><PERSON><PERSON> b<PERSON>", "PE.Controllers.Main.txtSlideNumber": "Số slide", "PE.Controllers.Main.txtSlideSubtitle": "<PERSON><PERSON> đề slide", "PE.Controllers.Main.txtSlideText": "Nội dung slide", "PE.Controllers.Main.txtSlideTitle": "Tiêu đề slide", "PE.Controllers.Main.txtStarsRibbons": "Sao & Ruy-băng", "PE.Controllers.Main.txtXAxis": "Trục X", "PE.Controllers.Main.txtYAxis": "<PERSON><PERSON><PERSON><PERSON> Y", "PE.Controllers.Main.unknownErrorText": "Lỗi không xác định.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Tr<PERSON><PERSON> du<PERSON>t của bạn không được hỗ trợ.", "PE.Controllers.Main.uploadImageExtMessage": "Định dạng hình <PERSON>nh không xác đ<PERSON>nh.", "PE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh đư<PERSON> tải lên.", "PE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON> vượt quá giới hạn kích thước tối đa của hình <PERSON>nh.", "PE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>...", "PE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>", "PE.Controllers.Main.warnBrowserIE9": "Ứng dụng vận hành kém trên IE9. Sử dụng IE10 hoặc cao hơn", "PE.Controllers.Main.warnBrowserZoom": "Hiện cài đặt thu phóng trình duyệt của bạn không được hỗ trợ đầy đủ. <PERSON>ui lòng thiết lập lại chế độ thu phóng mặc định bằng cách nhấn Ctrl+0.", "PE.Controllers.Main.warnLicenseExp": "G<PERSON><PERSON><PERSON> phép của bạn đã hết hạn.<br><PERSON><PERSON> lòng cập nhật gi<PERSON>y phép và làm mới trang.", "PE.Controllers.Main.warnNoLicense": "Bạn đang sử dụng phiên bản nguồn mở của %1. <PERSON><PERSON>n bản có giới hạn các kết nối đồng thời với server tà<PERSON> liệu (20 kết nối cùng một lúc).<br><PERSON><PERSON><PERSON> bạn cần thêm, h<PERSON><PERSON> cân nhắc mua giấy phép thương mại.", "PE.Controllers.Main.warnProcessRightsChange": "Bạn đã bị từ chối quyền chỉnh sửa file này.", "PE.Controllers.Statusbar.zoomText": "<PERSON>hu phóng {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Phông chữ bạn sẽ lưu không có sẵn trên thiết bị hiện tại.<br>Ki<PERSON><PERSON> văn bản sẽ được hiển thị bằng một trong các phông chữ hệ thống, phông chữ đã lưu sẽ được sử dụng khi có sẵn.<br>Bạn có muốn tiếp tục?", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON> phụ", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.textEmptyImgUrl": "Bạn cần chỉ định URL hình ảnh.", "PE.Controllers.Toolbar.textFontSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị số thuộc từ 1 đến 300", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON> số", "PE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON> phân", "PE.Controllers.Toolbar.textLargeOperator": "<PERSON><PERSON> tử lớn", "PE.Controllers.Toolbar.textLimitAndLog": "Giới hạn và Lô-ga", "PE.Controllers.Toolbar.textMatrix": "<PERSON> trận", "PE.Controllers.Toolbar.textOperator": "<PERSON><PERSON> tử", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "PE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "PE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> tên phải ở trên", "PE.Controllers.Toolbar.txtAccent_Bar": "Gạch", "PE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON><PERSON> trên", "PE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> thứ<PERSON> đ<PERSON> (Có Placeholder)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON> thức đ<PERSON>g khung (Ví dụ)", "PE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Ngoặc ôm ở dưới", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Ngoặc ôm ở trên", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC với Gạch trên", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Vớ<PERSON> trên", "PE.Controllers.Toolbar.txtAccent_DDDot": "Ba chấm", "PE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON> đ<PERSON>i", "PE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> trên k<PERSON>", "PE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupBot": "<PERSON><PERSON> nhóm ký tự ở dưới", "PE.Controllers.Toolbar.txtAccent_GroupTop": "<PERSON><PERSON> nhóm ký tự ở trên", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON> c<PERSON>u trên hư<PERSON> về trái", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON> móc phải ở trên", "PE.Controllers.Toolbar.txtAccent_Hat": "Mũ", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON><PERSON> ng<PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Dấu ngoặc với Dấu phân cách", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (hai đi<PERSON><PERSON> ki<PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ba điều kiện)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "PE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "PE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON> dụ cho tr<PERSON><PERSON><PERSON> hợp", "PE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON> số nhị thức", "PE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON> số nhị thức", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>u ngoặc", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Ngoặc đơn", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Ngoặc đơn", "PE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON> số vi<PERSON><PERSON> l<PERSON>ch", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Vi phân", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Vi phân", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Vi phân", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Vi phân", "PE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON> số viết ngang", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi hơn 2", "PE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> số nhỏ", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON> số xếp chồng", "PE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON> cos ngh<PERSON><PERSON> đ<PERSON>o", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "<PERSON><PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON><PERSON><PERSON> cotg ngh<PERSON><PERSON> đ<PERSON>o", "PE.Controllers.Toolbar.txtFunction_1_Coth": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON><PERSON> cosec ngh<PERSON><PERSON> đ<PERSON>o", "PE.Controllers.Toolbar.txtFunction_1_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>-péc-b<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sec ngh<PERSON><PERSON> đ<PERSON>o", "PE.Controllers.Toolbar.txtFunction_1_Sech": "<PERSON><PERSON><PERSON> sec <PERSON>y-p<PERSON><PERSON>-b<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON><PERSON> sin ngh<PERSON>ch đ<PERSON>o", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON><PERSON> tan ngh<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON>m cosin", "PE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON> cos <PERSON>-bôn", "PE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON> co<PERSON>g", "PE.Controllers.Toolbar.txtFunction_Coth": "<PERSON><PERSON><PERSON>b<PERSON>n", "PE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON> cos", "PE.Controllers.Toolbar.txtFunction_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>bôn", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sin theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON> thức tan", "PE.Controllers.Toolbar.txtFunction_Sec": "Hàm sec", "PE.Controllers.Toolbar.txtFunction_Sech": "<PERSON><PERSON><PERSON>n", "PE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON> sin", "PE.Controllers.Toolbar.txtFunction_Sinh": "<PERSON><PERSON><PERSON>n", "PE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON> tan", "PE.Controllers.Toolbar.txtFunction_Tanh": "<PERSON><PERSON><PERSON>n", "PE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON> phân", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Vi phân của theta", "PE.Controllers.Toolbar.txtIntegral_dx": "Vi phân của x", "PE.Controllers.Toolbar.txtIntegral_dy": "Vi phân của y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON> phân", "PE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> phân kép", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân kép", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON> phân kép", "PE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON> phân bề mặt", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "PE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON> phân", "PE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON> phân ba lớp", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON> p<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON> <PERSON>hau", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON> <PERSON>hau", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON> <PERSON>hau", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON> p<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON> p<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON> p<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON>í dụ giới hạn", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON> dụ Lớn nhất", "PE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON> hạn", "PE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON>-ga-r<PERSON>t tự nhiên", "PE.Controllers.Toolbar.txtLimitLog_Log": "Lô-ga-r<PERSON>t", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Lô-ga-r<PERSON>t", "PE.Controllers.Toolbar.txtLimitLog_Max": "Lớn n<PERSON>t", "PE.Controllers.Toolbar.txtLimitLog_Min": "Nhỏ nhất", "PE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON> trận rỗng 1x2", "PE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON> trận rỗng 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON> trận rỗng 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON> trận rỗng 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Ma trận rỗng với dấu ngoặc", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Ma trận rỗng với dấu ngoặc", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Ma trận rỗng với dấu ngoặc", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Ma trận rỗng với dấu ngoặc", "PE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON> trận rỗng 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON> trận rỗng 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON> trận rỗng 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON> trận rỗng 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> chấm câu", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON>m gi<PERSON>a dòng", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> chấm chéo", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON> trận thưa", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON> trận thưa", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON> trận Đơn vị 2x2", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "<PERSON> trận Đơn vị 3x3", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "<PERSON> trận Đơn vị 3x3", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "<PERSON> trận Đơn vị 3x3", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "<PERSON> chấm Bằng", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Lợi suất Delta", "PE.Controllers.Toolbar.txtOperator_Definition": "Bằng với theo <PERSON> ngh<PERSON>a", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta bằng với", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Bằng bằng", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Trừ Bằng", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Cộng Bằng", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON> đo bằng", "PE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> bậc hai có bậc", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "PE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> b<PERSON>", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hai", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Chỉ số dưới", "PE.Controllers.Toolbar.txtScriptSubSup": "Chỉ số dưới-Chỉ số trên", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Chỉ số dưới-Chỉ số trên bên trái", "PE.Controllers.Toolbar.txtScriptSup": "Chỉ số trên", "PE.Controllers.Toolbar.txtSymbol_about": "Xấp xỉ", "PE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON> sung", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "PE.Controllers.Toolbar.txtSymbol_approx": "Gần bằng với", "PE.Controllers.Toolbar.txtSymbol_ast": "<PERSON><PERSON> t<PERSON> *", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON> tử Dấu đầu dòng", "PE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON> <PERSON>hau", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "PE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON>m lửng nằm ngang giữa dòng", "PE.Controllers.Toolbar.txtSymbol_celsius": "Độ C", "PE.Controllers.Toolbar.txtSymbol_chi": "X", "PE.Controllers.Toolbar.txtSymbol_cong": "Xấp xỉ bằng với", "PE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON><PERSON> lửng chéo xuống bên ph<PERSON>i", "PE.Controllers.Toolbar.txtSymbol_degree": "Độ", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON> chia", "PE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> tên xu<PERSON>", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> hợp rỗng", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Bằng", "PE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON> tồn tại", "PE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON> th<PERSON>a", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Độ F", "PE.Controllers.Toolbar.txtSymbol_forall": "<PERSON> tất cả", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "Lớn hơn hoặc bằng", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> h<PERSON>", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_in": "<PERSON>ần tử của", "PE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON> h<PERSON>", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> tên trái", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON> tên <PERSON>-<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_leq": "Nhỏ hơn hoặc Bằng", "PE.Controllers.Toolbar.txtSymbol_less": "Nhỏ hơn", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_minus": "Trừ", "PE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Không bằng", "PE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON><PERSON> ký hiệu", "PE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON><PERSON> tồn tại", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "chữ 'o' ngắn", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Sai phân riêng", "PE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>ng", "PE.Controllers.Toolbar.txtSymbol_pm": "Cộng Trừ", "PE.Controllers.Toolbar.txtSymbol_propto": "Tỷ lệ", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON> thứ tư", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> chứ<PERSON>h", "PE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON> lửng chéo lên ph<PERSON>i", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> tên bên ph<PERSON>i", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> v<PERSON>y", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON> tên chỉ lên", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON><PERSON> thể Epsilon", "PE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON><PERSON> thể của <PERSON>ho", "PE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON><PERSON> thể theta", "PE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON> l<PERSON> d<PERSON>c", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Views.ChartSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "PE.Views.ChartSettings.textChartType": "Thay đổi Loại biểu đồ", "PE.Views.ChartSettings.textEditData": "Chỉnh sửa <PERSON> liệu", "PE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "PE.Views.ChartSettings.textKeepRatio": "Tỷ lệ không đổi", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "PE.Views.ChartSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PE.Views.ChartSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON><PERSON> đồ - <PERSON>ài đặt Nâng cao", "PE.Views.DocumentHolder.aboveText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "PE.Views.DocumentHolder.advancedImageText": "Cài đặt Hình <PERSON>nh <PERSON> cao", "PE.Views.DocumentHolder.advancedParagraphText": "Cài đặt Nâng cao <PERSON> bản", "PE.Views.DocumentHolder.advancedShapeText": "Cài đặt Nâng cao Hình dạng", "PE.Views.DocumentHolder.advancedTableText": "Cài đặt Nâng cao Bảng", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON>n chỉnh", "PE.Views.DocumentHolder.belowText": "Dư<PERSON><PERSON>", "PE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON><PERSON> chỉnh dọc ô", "PE.Views.DocumentHolder.cellText": "Ô", "PE.Views.DocumentHolder.centerText": "Trung tâm", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteText": "Xóa", "PE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản lên", "PE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản x<PERSON>", "PE.Views.DocumentHolder.directHText": "Nằm ngang", "PE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "PE.Views.DocumentHolder.editChartText": "Chỉnh sửa <PERSON> liệu", "PE.Views.DocumentHolder.editHyperlinkText": "Chỉnh sửa <PERSON><PERSON><PERSON> liên kết", "PE.Views.DocumentHolder.hyperlinkText": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "PE.Views.DocumentHolder.ignoreAllSpellText": "Bỏ qua tất cả", "PE.Views.DocumentHolder.ignoreSpellText": "Bỏ qua", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> trái", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> trên", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON> ngôn ngữ", "PE.Views.DocumentHolder.leftText": "Trái", "PE.Views.DocumentHolder.loadSpellText": "<PERSON><PERSON> tải các biến thể...", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON><PERSON> nhi<PERSON>u ô và không canh giữa", "PE.Views.DocumentHolder.moreText": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c biến thể...", "PE.Views.DocumentHolder.noSpellVariantsText": "<PERSON><PERSON><PERSON><PERSON> có biến thể", "PE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON> thước mặc định", "PE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON> si<PERSON>u liên k<PERSON>t", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON> tra ch<PERSON>h tả", "PE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON> ô...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> t<PERSON>", "PE.Views.DocumentHolder.textArrangeBackward": "Gửi về phía sau", "PE.Views.DocumentHolder.textArrangeForward": "<PERSON> chuyển tiến lên", "PE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON> lên <PERSON> cảnh", "PE.Views.DocumentHolder.textCopy": "Sao chép", "PE.Views.DocumentHolder.textCut": "Cắt", "PE.Views.DocumentHolder.textNextPage": "Slide kế tiếp", "PE.Views.DocumentHolder.textPaste": "Dán", "PE.Views.DocumentHolder.textPrevPage": "Slide tr<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON><PERSON> c<PERSON>ng", "PE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON> trung tâm", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "PE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "PE.Views.DocumentHolder.textSlideSettings": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> slide", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "<PERSON> tiết này hiện đang được chỉnh sửa bởi một người dùng khác.", "PE.Views.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON> đườ<PERSON> viền dưới cùng", "PE.Views.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON><PERSON> dấu phân số", "PE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> đường kẻ ngang", "PE.Views.DocumentHolder.txtAddLB": "Thê<PERSON> đường kẻ dưới cùng bên trái", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền trái", "PE.Views.DocumentHolder.txtAddLT": "Thê<PERSON> đường kẻ trên cùng bên trái", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền bên phải", "PE.Views.DocumentHolder.txtAddTop": "<PERSON>hê<PERSON> đường viền trên cùng", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON><PERSON> đường kẻ dọc", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON>n chỉnh", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON>n chỉnh theo ký tự", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>p", "PE.Views.DocumentHolder.txtBackground": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đư<PERSON> vền", "PE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "PE.Views.DocumentHolder.txtChangeLayout": "<PERSON><PERSON> đ<PERSON>i bố cục", "PE.Views.DocumentHolder.txtChangeTheme": "Thay đổi theme", "PE.Views.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> chỉnh cột", "PE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đ<PERSON>i số", "PE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> đ<PERSON>i số", "PE.Views.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "PE.Views.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON> các ký tự kèm theo", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON> các ký tự và dấu phân cách kèm theo", "PE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON> biểu đồ", "PE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> c<PERSON>c", "PE.Views.DocumentHolder.txtDeleteSlide": "Xóa slide", "PE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON> bổ theo chi<PERSON>u ngang", "PE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON> bổ theo chi<PERSON> dọc", "PE.Views.DocumentHolder.txtDuplicateSlide": "<PERSON><PERSON><PERSON> bản slide", "PE.Views.DocumentHolder.txtFractionLinear": "<PERSON><PERSON> sang phân số viết ngang", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> sang phân số viết lệch", "PE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> đ<PERSON> sang phân số viết đứng", "PE.Views.DocumentHolder.txtGroup": "Nhóm", "PE.Views.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON><PERSON> đồ trên văn bản", "PE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> dư<PERSON><PERSON> văn bản", "PE.Views.DocumentHolder.txtHideBottom": "Ẩn đường viền dưới cùng", "PE.Views.DocumentHolder.txtHideBottomLimit": "Ẩn giới hạn dưới", "PE.Views.DocumentHolder.txtHideCloseBracket": "Ẩn dấu ngoặc đóng", "PE.Views.DocumentHolder.txtHideDegree": "Ẩn cấp độ", "PE.Views.DocumentHolder.txtHideHor": "Ẩn đường kẻ ngang", "PE.Views.DocumentHolder.txtHideLB": "Ẩn đường kẻ dưới cùng bên trái", "PE.Views.DocumentHolder.txtHideLeft": "Ẩn đường viền trái", "PE.Views.DocumentHolder.txtHideLT": "Ẩn đường kẻ trên cùng bên trái", "PE.Views.DocumentHolder.txtHideOpenBracket": "Ẩn dấu ngoặc mở", "PE.Views.DocumentHolder.txtHidePlaceholder": "Ẩn placeholder", "PE.Views.DocumentHolder.txtHideRight": "Ẩn đường viền bên phải", "PE.Views.DocumentHolder.txtHideTop": "Ẩn đường viền trên cùng", "PE.Views.DocumentHolder.txtHideTopLimit": "Ẩn giới hạn trên cùng", "PE.Views.DocumentHolder.txtHideVer": "Ẩn đường kẻ dọc", "PE.Views.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đối số", "PE.Views.DocumentHolder.txtInsertArgAfter": "<PERSON><PERSON><PERSON> đ<PERSON>i số sau", "PE.Views.DocumentHolder.txtInsertArgBefore": "<PERSON><PERSON><PERSON> đ<PERSON>i số trước", "PE.Views.DocumentHolder.txtInsertBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "PE.Views.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON>ng trình sau", "PE.Views.DocumentHolder.txtInsertEqBefore": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình trước", "PE.Views.DocumentHolder.txtLimitChange": "<PERSON>hay đổi giới hạn địa điểm", "PE.Views.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON><PERSON> hạn trên văn bản", "PE.Views.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON><PERSON> hạn dưới văn bản", "PE.Views.DocumentHolder.txtMatchBrackets": "Chỉnh dấu ngoặc phù hợp với độ cao đối số", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON> chỉnh ma trận", "PE.Views.DocumentHolder.txtNewSlide": "Slide mới", "PE.Views.DocumentHolder.txtOverbar": "<PERSON><PERSON><PERSON> trên v<PERSON><PERSON> bản", "PE.Views.DocumentHolder.txtPressLink": "Ấn {0} và nhấp vào liên kết", "PE.Views.DocumentHolder.txtPreview": "<PERSON><PERSON><PERSON> đầu trình chiếu", "PE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON> d<PERSON>u phân số", "PE.Views.DocumentHolder.txtRemLimit": "<PERSON>óa gi<PERSON> hạn", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Xóa ký tự dấu phụ", "PE.Views.DocumentHolder.txtRemoveBar": "<PERSON>óa v<PERSON>", "PE.Views.DocumentHolder.txtRemScripts": "Xóa script", "PE.Views.DocumentHolder.txtRemSubscript": "Xóa chỉ số dưới", "PE.Views.DocumentHolder.txtRemSuperscript": "Xóa chỉ số trên", "PE.Views.DocumentHolder.txtScriptsAfter": "Các script sau văn bản", "PE.Views.DocumentHolder.txtScriptsBefore": "Các script trước văn bản", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON> tất cả", "PE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON> thị giới hạn dưới", "PE.Views.DocumentHolder.txtShowCloseBracket": "Hiển thị dấu ngoặc đóng", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> thị cấp độ", "PE.Views.DocumentHolder.txtShowOpenBracket": "Hiển thị dấu ngoặc mở", "PE.Views.DocumentHolder.txtShowPlaceholder": "Hiển thị placeholder", "PE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON> thị giới hạn trên", "PE.Views.DocumentHolder.txtSlide": "Slide", "PE.Views.DocumentHolder.txtSlideHide": "Ẩn slide", "PE.Views.DocumentHolder.txtStretchBrackets": "<PERSON>éo dài ngoặc", "PE.Views.DocumentHolder.txtTop": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtUnderbar": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON><PERSON> bản", "PE.Views.DocumentHolder.txtUngroup": "Bỏ nhóm", "PE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON> chỉnh dọc", "PE.Views.DocumentPreview.goToSlideText": "<PERSON><PERSON> tới Slide", "PE.Views.DocumentPreview.slideIndexText": "Slide {0} trên {1}", "PE.Views.DocumentPreview.txtClose": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtEndSlideshow": "<PERSON><PERSON><PERSON> th<PERSON>c trình ch<PERSON>u", "PE.Views.DocumentPreview.txtExitFullScreen": "<PERSON><PERSON><PERSON><PERSON> toàn màn hình", "PE.Views.DocumentPreview.txtFinalMessage": "<PERSON><PERSON><PERSON> thúc xem trước slide. <PERSON><PERSON><PERSON><PERSON> đ<PERSON> tho<PERSON>t.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON><PERSON><PERSON> màn hình", "PE.Views.DocumentPreview.txtNext": "Slide kế tiếp", "PE.Views.DocumentPreview.txtPageNumInvalid": "Số slide không hợp lệ", "PE.Views.DocumentPreview.txtPause": "<PERSON><PERSON><PERSON> trình ch<PERSON>u", "PE.Views.DocumentPreview.txtPlay": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> ch<PERSON>", "PE.Views.DocumentPreview.txtPrev": "Slide tr<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtReset": "Đặt lại", "PE.Views.FileMenu.btnAboutCaption": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "PE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON> tới Tài liệu", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON> mới", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> về dưới dạng", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON> g<PERSON>", "PE.Views.FileMenu.btnInfoCaption": "Th<PERSON><PERSON> tin Bản trình chiếu", "PE.Views.FileMenu.btnPrintCaption": "In", "PE.Views.FileMenu.btnRecentFilesCaption": "Mở gần đây", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON> tên", "PE.Views.FileMenu.btnReturnCaption": "Quay lại Trình chi<PERSON>u", "PE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON><PERSON> truy cập", "PE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON> dạng", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSettingsCaption": "Cài đặt nâng cao", "PE.Views.FileMenu.btnToEditCaption": "Chỉnh sửa bản trình chi<PERSON>u", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Tác g<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Ti<PERSON><PERSON> đề bản trình chiếu", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "PE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Chế độ đồng chỉnh sửa", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strStrict": "Nghiêm ngặt", "PE.Views.FileMenuPanels.Settings.strUnit": "Đơn vị đo lư<PERSON>", "PE.Views.FileMenuPanels.Settings.strZoom": "<PERSON><PERSON><PERSON> trị <PERSON> to Mặc định", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Mỗi 10 phút", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Mỗi 30 phút", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Mỗi 5 phút", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Mỗi giờ", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Hướng dẫn Căn chỉnh", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Tự động khôi phục", "PE.Views.FileMenuPanels.Settings.textAutoSave": "<PERSON><PERSON> động lưu", "PE.Views.FileMenuPanels.Settings.textDisabled": "Tắt", "PE.Views.FileMenuPanels.Settings.textForceSave": "Lưu vào Server", "PE.Views.FileMenuPanels.Settings.textMinute": "Mỗi phút", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON> t<PERSON>t cả", "PE.Views.FileMenuPanels.Settings.txtCm": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Vừa với Slide", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Vừa với <PERSON> rộng", "PE.Views.FileMenuPanels.Settings.txtInch": "Inch", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON> tra ch<PERSON>h tả", "PE.Views.HeaderFooterDialog.textPreview": "<PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON> thị", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON> kết t<PERSON>i", "PE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON><PERSON><PERSON> văn bản đã chọn", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> đầu đề ở đây", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON><PERSON> liên kết ở đây", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Nhập tooltip ở đây", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON> k<PERSON> ng<PERSON>i", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slide trong Bản trình chi<PERSON>u này", "PE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON><PERSON> b<PERSON>n <PERSON>ip", "PE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> đặt <PERSON><PERSON>u liên kết", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Slide đầu tiên", "PE.Views.HyperlinkSettingsDialog.txtLast": "Slide cuối cùng", "PE.Views.HyperlinkSettingsDialog.txtNext": "Slide kế tiếp", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Slide tr<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slide", "PE.Views.ImageSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "PE.Views.ImageSettings.textEdit": "Chỉnh sửa", "PE.Views.ImageSettings.textEditObject": "Chỉnh sửa <PERSON><PERSON> tư<PERSON>", "PE.Views.ImageSettings.textFromFile": "Từ file", "PE.Views.ImageSettings.textFromUrl": "Từ URL", "PE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "PE.Views.ImageSettings.textInsert": "<PERSON><PERSON> thế <PERSON>nh", "PE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON> thước mặc định", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "PE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON> cao", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Tỷ lệ không đổi", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON> thước mặc định", "PE.Views.ImageSettingsAdvanced.textPlacement": "<PERSON><PERSON>n chỉnh", "PE.Views.ImageSettingsAdvanced.textPosition": "<PERSON><PERSON> trí", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "Hình <PERSON>nh - Cài đặt Nâng cao", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "PE.Views.LeftMenu.tipPlugins": "Plugin", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "Slide", "PE.Views.LeftMenu.tipSupport": "<PERSON><PERSON><PERSON> & Hỗ trợ", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PE.Views.LeftMenu.txtDeveloper": "CHẾ ĐỘ NHÀ PHÁT TRIỂN", "PE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON><PERSON> cách dòng", "PE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch đo<PERSON>n", "PE.Views.ParagraphSettings.strSpacingAfter": "Sau", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "PE.Views.ParagraphSettings.textAt": "Tại", "PE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON> động", "PE.Views.ParagraphSettingsAdvanced.noTabs": "<PERSON><PERSON><PERSON> tab được chỉ định sẽ xuất hiện trong trường này", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Tất cả Drop cap", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON>ch đôi giữa chữ", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Trái", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Phông chữ", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Thụt lề & Căn chỉnh", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Drop cap nhỏ", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON> gi<PERSON>a chữ", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Chỉ số dưới", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Chỉ số trên", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "PE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON>n chỉnh", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON> c<PERSON>ch trắng", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Tab mặc định", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Xóa", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON> tất cả", "PE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Trung tâm", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Trái", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON> trí <PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON><PERSON> văn bản - <PERSON><PERSON>i đặt Nâng cao", "PE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON>", "PE.Views.RightMenu.txtImageSettings": "<PERSON>ài đặt hình ảnh", "PE.Views.RightMenu.txtParagraphSettings": "<PERSON>ài đặt văn bản", "PE.Views.RightMenu.txtShapeSettings": "<PERSON>ài đặt hình dạng", "PE.Views.RightMenu.txtSlideSettings": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> slide", "PE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON>i đặt bảng", "PE.Views.RightMenu.txtTextArtSettings": "Cài đặt chữ Nghệ thuật", "PE.Views.ShapeSettings.strBackground": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strChange": "Thay đ<PERSON>i Autoshape", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON> màu", "PE.Views.ShapeSettings.strForeground": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON> văn", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON> mờ", "PE.Views.ShapeSettings.strType": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "PE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "PE.Views.ShapeSettings.textColor": "<PERSON><PERSON> màu", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "PE.Views.ShapeSettings.textFromFile": "Từ file", "PE.Views.ShapeSettings.textFromUrl": "Từ URL", "PE.Views.ShapeSettings.textGradient": "Gradient", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "PE.Views.ShapeSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "PE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON> văn", "PE.Views.ShapeSettings.textRadial": "Tỏa tròn", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "Từ Texture", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "PE.Views.ShapeSettings.txtGrain": "Thớ gỗ", "PE.Views.ShapeSettings.txtGranite": "Đá granite", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "PE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON> xen", "PE.Views.ShapeSettings.txtLeather": "Da", "PE.Views.ShapeSettings.txtNoBorders": "Không đường kẻ", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "PE.Views.ShapeSettings.txtWood": "Gỗ", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON><PERSON> padding cho vă<PERSON> bản", "PE.Views.ShapeSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON> tên", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> thước khởi đầu", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON>u khởi đầu", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Số cột", "PE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON> kết th<PERSON>c", "PE.Views.ShapeSettingsAdvanced.textFlat": "Phẳng", "PE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON> cao", "PE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Tỷ lệ không đổi", "PE.Views.ShapeSettingsAdvanced.textLeft": "Trái", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON> đường kẻ", "PE.Views.ShapeSettingsAdvanced.textMiter": "Góc 45 độ", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRound": "Tròn", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch gi<PERSON><PERSON> các c<PERSON>t", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTitle": "<PERSON><PERSON>nh dạng - <PERSON>ài đặt Nâng cao", "PE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "<PERSON><PERSON> & <PERSON><PERSON><PERSON> tên", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "kh<PERSON>ng", "PE.Views.SlideSettings.strBackground": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strForeground": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON> văn", "PE.Views.SlideSettings.strTransparency": "<PERSON><PERSON> mờ", "PE.Views.SlideSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "PE.Views.SlideSettings.textColor": "<PERSON><PERSON> màu", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "PE.Views.SlideSettings.textFromFile": "Từ file", "PE.Views.SlideSettings.textFromUrl": "Từ URL", "PE.Views.SlideSettings.textGradient": "Gradient", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "PE.Views.SlideSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "PE.Views.SlideSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON> văn", "PE.Views.SlideSettings.textRadial": "Tỏa tròn", "PE.Views.SlideSettings.textReset": "<PERSON><PERSON><PERSON><PERSON> lập lại Thay đổi", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "Từ Texture", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "PE.Views.SlideSettings.txtGrain": "Thớ gỗ", "PE.Views.SlideSettings.txtGranite": "Đá granite", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "PE.Views.SlideSettings.txtKnit": "<PERSON><PERSON> xen", "PE.Views.SlideSettings.txtLeather": "Da", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "PE.Views.SlideSettings.txtWood": "Gỗ", "PE.Views.SlideshowSettings.textLoop": "Vòng lặp liên tục cho đến khi '<PERSON>s<PERSON>' đư<PERSON><PERSON> ấn", "PE.Views.SlideshowSettings.textTitle": "Hiển thị cài đặt", "PE.Views.SlideSizeSettings.strLandscape": "Nằm ngang", "PE.Views.SlideSizeSettings.strPortrait": "Thẳng đứng", "PE.Views.SlideSizeSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "PE.Views.SlideSizeSettings.textSlideOrientation": "Hướng slide", "PE.Views.SlideSizeSettings.textSlideSize": "<PERSON><PERSON><PERSON> slide", "PE.Views.SlideSizeSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> slide", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.SlideSizeSettings.txt35": "Slides 35 mm", "PE.Views.SlideSizeSettings.txtA3": "Giấy A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Giấy A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Giấy B4 (ICO) (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "Giấy B5 (ICO) (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Tuỳ chỉnh", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON><PERSON><PERSON> (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Giấy Letter (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtStandard": "<PERSON><PERSON><PERSON><PERSON> (4:3)", "PE.Views.Statusbar.goToPageText": "<PERSON><PERSON> tới Slide", "PE.Views.Statusbar.pageIndexText": "Slide {0} trên {1}", "PE.Views.Statusbar.tipAccessRights": "<PERSON><PERSON><PERSON><PERSON> lý quyền truy cập tài liệu", "PE.Views.Statusbar.tipFitPage": "Vừa với Slide", "PE.Views.Statusbar.tipFitWidth": "Vừa với <PERSON> rộng", "PE.Views.Statusbar.tipPreview": "<PERSON><PERSON><PERSON> đầu trình chiếu", "PE.Views.Statusbar.tipSetLang": "Đặt ngôn ngữ văn bản", "PE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON> ph<PERSON>g", "PE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON> to", "PE.Views.Statusbar.tipZoomOut": "<PERSON>hu nhỏ", "PE.Views.Statusbar.txtPageNumInvalid": "Số slide không hợp lệ", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON><PERSON> c<PERSON>t bên trái", "PE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON> c<PERSON>t bên ph<PERSON>i", "PE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> hàng bên trên", "PE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> hàng bên <PERSON>", "PE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON><PERSON> nhi<PERSON>u ô và không canh giữa", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> b<PERSON>", "PE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON> ô...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "PE.Views.TableSettings.textBackColor": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON> d<PERSON>i màu", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON> viền", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textEdit": "Hàng & Cột", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON>ng có template", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON> tiên", "PE.Views.TableSettings.textHeader": "Header", "PE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "PE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Chọn đường viền bạn muốn thay đổi áp dụng kiểu đã chọn ở trên", "PE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON> từ Template", "PE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON> cộng", "PE.Views.TableSettings.tipAll": "Đặt viền ngoài và tất cả đường kẻ bên trong", "PE.Views.TableSettings.tipBottom": "Chỉ đặt viền ngoài dưới cùng", "PE.Views.TableSettings.tipInner": "Chỉ đặt các đường kẻ bên trong", "PE.Views.TableSettings.tipInnerHor": "Chỉ đặt các đường ngang bên trong", "PE.Views.TableSettings.tipInnerVert": "Chỉ đặt đường kẻ dọc bên trong", "PE.Views.TableSettings.tipLeft": "Chỉ đặt viền ngoài bên trái", "PE.Views.TableSettings.tipNone": "K<PERSON>ông đặt viền", "PE.Views.TableSettings.tipOuter": "Chỉ đặt viền ngoài", "PE.Views.TableSettings.tipRight": "Chỉ đặt viền ngoài bên phải", "PE.Views.TableSettings.tipTop": "Chỉ đặt viền ngoài trên cùng", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "PE.Views.TableSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "PE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Sử dụng lề mặc định", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON> mặc định", "PE.Views.TableSettingsAdvanced.textLeft": "Trái", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON> của ô", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTitle": "Bảng - Cài đặt Nâng cao", "PE.Views.TableSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Lề", "PE.Views.TextArtSettings.strBackground": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON> màu", "PE.Views.TextArtSettings.strForeground": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON> văn", "PE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON> mờ", "PE.Views.TextArtSettings.strType": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "PE.Views.TextArtSettings.textColor": "<PERSON><PERSON> màu", "PE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "PE.Views.TextArtSettings.textFromFile": "Từ file", "PE.Views.TextArtSettings.textFromUrl": "Từ URL", "PE.Views.TextArtSettings.textGradient": "Gradient", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "PE.Views.TextArtSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "PE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON> văn", "PE.Views.TextArtSettings.textRadial": "Tỏa tròn", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "Template", "PE.Views.TextArtSettings.textTexture": "Từ Texture", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON><PERSON> đổi", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "PE.Views.TextArtSettings.txtGrain": "Thớ gỗ", "PE.Views.TextArtSettings.txtGranite": "Đá granite", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "PE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON> xen", "PE.Views.TextArtSettings.txtLeather": "Da", "PE.Views.TextArtSettings.txtNoBorders": "Không đường kẻ", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "PE.Views.TextArtSettings.txtWood": "Gỗ", "PE.Views.Toolbar.capAddSlide": "Thêm slide", "PE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON> lu<PERSON>", "PE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON><PERSON> đồ", "PE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON> trình", "PE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertShape": "<PERSON><PERSON><PERSON> d<PERSON>", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "PE.Views.Toolbar.capTabFile": "File", "PE.Views.Toolbar.capTabHome": "Trang chủ", "PE.Views.Toolbar.capTabInsert": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniCustomTable": "<PERSON><PERSON><PERSON> bảng tùy chỉnh", "PE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON> từ file", "PE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON> từ URL", "PE.Views.Toolbar.mniSlideAdvanced": "Cài đặt nâng cao", "PE.Views.Toolbar.mniSlideStandard": "<PERSON><PERSON><PERSON><PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON> r<PERSON> (16:9)", "PE.Views.Toolbar.textAlignBottom": "<PERSON>ăn chỉnh văn bản xuống dưới cùng", "PE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON><PERSON> bản trung tâm", "PE.Views.Toolbar.textAlignJust": "<PERSON><PERSON> đ<PERSON>u", "PE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> chỉnh văn bản sang trái", "PE.Views.Toolbar.textAlignMiddle": "Căn chỉnh văn bản vào giữa", "PE.Views.Toolbar.textAlignRight": "<PERSON><PERSON>n chỉnh văn bản sang phải", "PE.Views.Toolbar.textAlignTop": "<PERSON><PERSON>n chỉnh văn bản lên trên cùng", "PE.Views.Toolbar.textArrangeBack": "<PERSON><PERSON><PERSON> t<PERSON>", "PE.Views.Toolbar.textArrangeBackward": "Gửi về phía sau", "PE.Views.Toolbar.textArrangeForward": "<PERSON> chuyển tiến lên", "PE.Views.Toolbar.textArrangeFront": "<PERSON><PERSON><PERSON> lên <PERSON> cảnh", "PE.Views.Toolbar.textBold": "Đậm", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignBottom": "<PERSON><PERSON><PERSON> c<PERSON>ng", "PE.Views.Toolbar.textShapeAlignCenter": "<PERSON><PERSON><PERSON> trung tâm", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "PE.Views.Toolbar.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "PE.Views.Toolbar.textShowBegin": "<PERSON><PERSON><PERSON> thị từ đầu", "PE.Views.Toolbar.textShowCurrent": "<PERSON><PERSON><PERSON> thị từ slide <PERSON><PERSON><PERSON> tại", "PE.Views.Toolbar.textShowPresenterView": "Hi<PERSON><PERSON> thị presenter view", "PE.Views.Toolbar.textShowSettings": "Hiển thị cài đặt", "PE.Views.Toolbar.textStrikeout": "Gạch bỏ", "PE.Views.Toolbar.textSubscript": "Chỉ số dưới", "PE.Views.Toolbar.textSuperscript": "Chỉ số trên", "PE.Views.Toolbar.textTabFile": "File", "PE.Views.Toolbar.textTabHome": "Trang chủ", "PE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTitleError": "Lỗi", "PE.Views.Toolbar.textUnderline": "G<PERSON><PERSON> chân", "PE.Views.Toolbar.tipAddSlide": "Thêm slide", "PE.Views.Toolbar.tipBack": "Quay lại", "PE.Views.Toolbar.tipChangeChart": "Thay đổi Loại biểu đồ", "PE.Views.Toolbar.tipChangeSlide": "<PERSON><PERSON> đ<PERSON> cục slide", "PE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipColorSchemas": "<PERSON>hay đ<PERSON>i <PERSON> màu", "PE.Views.Toolbar.tipCopy": "Sao chép", "PE.Views.Toolbar.tipCopyStyle": "<PERSON>o ch<PERSON>p kiểu", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON> chữ", "PE.Views.Toolbar.tipFontName": "Phông chữ", "PE.Views.Toolbar.tipFontSize": "Cỡ chữ", "PE.Views.Toolbar.tipHAligh": "<PERSON><PERSON>n chỉnh ngang", "PE.Views.Toolbar.tipIncPrLeft": "<PERSON><PERSON><PERSON> th<PERSON>t l<PERSON>", "PE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> bi<PERSON>u đồ", "PE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "PE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON> siêu liên kết", "PE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertShape": "Chèn Autoshape", "PE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> b<PERSON>", "PE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "PE.Views.Toolbar.tipInsertTextArt": "<PERSON><PERSON><PERSON> chữ nghệ thuật", "PE.Views.Toolbar.tipLineSpace": "<PERSON><PERSON><PERSON><PERSON> cách dòng", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON> đầu dòng", "PE.Views.Toolbar.tipNumbers": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPaste": "Dán", "PE.Views.Toolbar.tipPreview": "<PERSON><PERSON><PERSON> đầu trình chiếu", "PE.Views.Toolbar.tipPrint": "In", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> thay đổi của bạn để những người dùng khác thấy chúng.", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON> chỉnh hình", "PE.Views.Toolbar.tipShapeArrange": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> hình dạng", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON> slide", "PE.Views.Toolbar.tipSlideTheme": "Theme của slide", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "<PERSON><PERSON><PERSON> chỉnh dọc", "PE.Views.Toolbar.tipViewSettings": "<PERSON><PERSON> đặt", "PE.Views.Toolbar.txtDistribHor": "<PERSON><PERSON> bổ theo chi<PERSON>u ngang", "PE.Views.Toolbar.txtDistribVert": "<PERSON><PERSON> bổ theo chi<PERSON> dọc", "PE.Views.Toolbar.txtGroup": "Nhóm", "PE.Views.Toolbar.txtScheme1": "Office", "PE.Views.Toolbar.txtScheme10": "Median", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "Opulent", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Origin", "PE.Views.Toolbar.txtScheme16": "Paper", "PE.Views.Toolbar.txtScheme17": "Solstice", "PE.Views.Toolbar.txtScheme18": "Technic", "PE.Views.Toolbar.txtScheme19": "Trek", "PE.Views.Toolbar.txtScheme2": "Grayscale", "PE.Views.Toolbar.txtScheme20": "Urban", "PE.Views.Toolbar.txtScheme21": "Verve", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Aspect", "PE.Views.Toolbar.txtScheme5": "Civic", "PE.Views.Toolbar.txtScheme6": "Concourse", "PE.Views.Toolbar.txtScheme7": "Equity", "PE.Views.Toolbar.txtScheme8": "Flow", "PE.Views.Toolbar.txtScheme9": "Foundry", "PE.Views.Toolbar.txtUngroup": "Bỏ nhóm", "PE.Views.Transitions.strDelay": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.Transitions.strStartOnClick": "<PERSON><PERSON><PERSON> đầu bằng <PERSON>h<PERSON><PERSON> chuột", "PE.Views.Transitions.textBlack": "<PERSON>ua màu đen", "PE.Views.Transitions.textClock": "<PERSON><PERSON><PERSON> hồ", "PE.Views.Transitions.textClockwise": "<PERSON> ch<PERSON>u kim đồng hồ", "PE.Views.Transitions.textCounterclockwise": "<PERSON><PERSON><PERSON><PERSON> chiều kim đồng hồ", "PE.Views.Transitions.textCover": "Slide mở đầu", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "<PERSON><PERSON> trong", "PE.Views.Transitions.textHorizontalOut": "<PERSON><PERSON> ng<PERSON>i", "PE.Views.Transitions.textLeft": "Trái", "PE.Views.Transitions.textPush": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "Nhẹ nhàng", "PE.Views.Transitions.textSplit": "<PERSON><PERSON> t<PERSON>", "PE.Views.Transitions.textUnCover": "Bỏ làm trang đầu", "PE.Views.Transitions.textWedge": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "<PERSON><PERSON> ph<PERSON>g", "PE.Views.Transitions.textZoomIn": "<PERSON><PERSON><PERSON> to", "PE.Views.Transitions.textZoomOut": "<PERSON>hu nhỏ", "PE.Views.Transitions.textZoomRotate": "Thu phóng và Xoay", "PE.Views.Transitions.txtApplyToAll": "<PERSON><PERSON> d<PERSON>ng cho tất cả slide", "PE.Views.Transitions.txtParameters": "<PERSON>h<PERSON><PERSON> số", "PE.Views.Transitions.txtPreview": "<PERSON><PERSON>", "PE.Views.Transitions.txtSec": "s"}