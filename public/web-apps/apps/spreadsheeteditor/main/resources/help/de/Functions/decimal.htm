﻿<!DOCTYPE html>
<html>
	<head>
		<title>DEZIMAL-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>DEZIMAL-Funktion</h1>
			<p>Die Funktion <b>DEZIMAL</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie konvertiert eine Textdarstellung einer Zahl mit einer angegebenen Basis in eine Dezimalzahl.</p>
			<p>Die Formelsyntax der Funktion <b>DEZIMAL</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>DEZIMAL(Text;Basis)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
            <p style="text-indent: 50px;"><b><em>Text</em></b> ist die Textdarstellung der Zahl, die Sie konvertieren möchten. Text darf maximal 255 Zeichen lang sein.</p>
            <p style="text-indent: 50px;"><b><em>Basis</em></b> ist die Basis der Nummer. Es muss eine ganze Zahl sein, die größer gleich 2 und kleiner gleich 36 ist.</p>
            <p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>DEZIMAL</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>DEZIMAL</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="DEZIMAL-Funktion" src="../images/decimal.png" /></p>
		</div>
	</body>
</html>