﻿<!DOCTYPE html>
<html>
	<head>
		<title>Cree listas</title>
		<meta charset="utf-8" />
		<meta name="description" content="Create bulleted and numbered lists in the presentation" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Cree listas</h1>
			<p>Para crear una lista en su presentación,</p>
			<ol>
				<li>coloque el cursor dentro del cuadro de texto donde desea iniciar una lista (puede ser una línea nueva o el texto ya introducido),</li>
                <li>cambie a la pestaña <b>Inicio</b> en la barra de herramientas superior,</li>
                <li>seleccione el tipo de lista que usted quiere crear:<ul>
						<li>La <b>Lista desordenada</b> con marcadores se puede crear usando el icono <div class = "icon icon-bullets"></div> <b>Puntos</b> situado en la barra de herramientas superior</li>
						<li>La <b>Lista ordenada</b> con números o letras se puede crear usando el icono <div class = "icon icon-numbering"></div> <b>Numeración</b> que se sitúa en la barra de herramientas superior<p class="note"><b>Nota</b>: haga clic en la flecha hacia abajo junto a icono <b>Puntos</b> o <b>Numeración</b> para seleccionar el aspecto de la lista.</p>
						</li>
					</ul>
				</li>
				<li>ahora cada vez que usted pulse la tecla <b>Enter</b> al final de la línea, un elemento nuevo de una lista ordenada o desordenada aparecerá. Para para esto, pulse la tecla <b>Backspace</b> y continúe con el texto de párrafo común.</li>
			</ol>
		</div>
	</body>
</html>