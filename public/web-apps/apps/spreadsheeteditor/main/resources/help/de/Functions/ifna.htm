﻿<!DOCTYPE html>
<html>
	<head>
		<title>WENNNV-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WENNNV-Funktion</h1>
			<p>Die Funktion <b>WENNNV</b> gehört zur Gruppe der logischen Funktionen. Sie wird verwendet, um zu überprüfen, ob im ersten Argument der Formel ein Fehler aufgetreten ist. Sie gibt den von Ihnen angegebenen Wert zurück, wenn die Formel den Fehlerwert #N/V liefert. Andernfalls wird das Ergebnis der Formel zurückgegeben.</p>
			<p>Die Formelsyntax der Funktion <b>WENNNV</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>WENNNV(Wert;Wert_bei_NV)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Wert</em></b> ist das Argument, das auf den Fehlerwert &quot;#N/V&quot; geprüft wird.</p>
            <p style="text-indent: 50px;"><b><em>Wert_bei_NV</em></b> ist der zurückzugebende Wert, wenn die Formel zum Fehlerwert &quot;#N/V&quot; ausgewertet wird.</p> 
            <p>Die Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p>Anwendung der Funktion <b>WENNNV</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Logisch</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WENNNV</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="WENNNV-Funktion" src="../images/ifna.png" /></p>
		</div>
	</body>
</html>