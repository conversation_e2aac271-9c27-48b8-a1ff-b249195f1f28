﻿<!DOCTYPE html>
<html>
	<head>
		<title>View tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - View tab" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>View tab</h1>
            <p>
                The <b>View</b> tab of the <a href="https://www.onlyoffice.com/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> allows you to manage how your presentation looks like while you are working on it.
            </p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Presentation Editor:</p>
                <p><img alt="View tab" src="../images/interface/viewtab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Presentation Editor:</p>
                <p><img alt="Home tab" src="../images/interface/desktop_viewtab.png" /></p>
            </div>
            <p>View options available on this tab:</p>
            <ul>
                <li><b>Zoom</b> allows to zoom in and zoom out your document.</li>
                <li><b>Fit to Slide</b> allows to resize the slide so that the screen displays the whole slide.</li>
                <li><b>Fit to Width</b> allows to resize the slide so that the slide scales to fit the width of the screen.</li>
                <li><b>Interface Theme</b> allows to change interface theme by choosing a <b>Same as system</b>, <b>Light</b>, <b>Classic Light</b>, <b>Dark</b> or <b>Contrast Dark</b> theme.</li>
            </ul>
            <p>The following options allow you to configure the elements to display or to hide. Check the elements to make them visible:</p>
            <ul>
                <li><b>Notes</b> to make the notes panel always visible.</li>
                <li><b>Rulers</b> to make rulers always visible.</li>
                <li><b>Guides</b> and <b>Gridlines</b> to properly position objects on the slide.</li>
                <li><b>Always Show Toolbar</b> to make the top toolbar always visible.</li>
                <li><b>Status Bar</b> to make the status bar always visible.</li>
                <li><b>Left Panel</b> to make the left panel visible.</li>
                <li><b>Right Panel</b> to make the right panel visible.</li>
            </ul>
        </div>
	</body>
</html>