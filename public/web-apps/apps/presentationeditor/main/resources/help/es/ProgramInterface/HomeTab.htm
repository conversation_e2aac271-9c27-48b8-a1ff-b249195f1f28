﻿<!DOCTYPE html>
<html>
	<head>
		<title>Pestaña de Inicio</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - Home tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Pestaña de Inicio</h1>
            <p>La pestaña de <b>Inicio</b> se abre por defecto cuando abre una presentación. Permite fijar parámetros generales con respecto a diapositivas, formato del texto, insertar, alinear y organizar varios objetos.</p>
            <div class="onlineDocumentFeatures">
                <p>Ventana del editor de presentaciones en línea:</p>
                <p><img alt="Pestaña de Inicio" src="../images/interface/hometab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Ventana del editor de presentaciones de escritorio:</p>
                <p><img alt="Pestaña de Inicio" src="../images/interface/desktop_hometab.png" /></p>
            </div>
            <p>Al usar esta pestaña podrás:</p>
            <ul>
                <li>organizar <a href="../UsageInstructions/ManageSlides.htm" onclick="onhyperlinkclick(this)">diapositivas</a> y <a href="../UsageInstructions/PreviewPresentation.htm" onclick="onhyperlinkclick(this)">empezar presentación</a>,</li>
                <li>formatear <a href="../UsageInstructions/InsertText.htm#formattext" onclick="onhyperlinkclick(this)">texto</a> que está dentro de un cuadro de texto,</li>
                <li>insertar <a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">cuadros de texto</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">imágenes</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">formas</a>,</li>
                <li><a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">alinear y arreglar objetos</a> en una diapositiva,</li>
                <li><a href="../UsageInstructions/CopyClearFormatting.htm" onclick="onhyperlinkclick(this)">copiar/borrar</a> el formato de un texto,</li>
                <li>cambiar un <a href="../UsageInstructions/SetSlideParameters.htm" onclick="onhyperlinkclick(this)">tema, esquema de color o tamaño de diapositiva</a>.</li>
            </ul>
		</div>
	</body>
</html>