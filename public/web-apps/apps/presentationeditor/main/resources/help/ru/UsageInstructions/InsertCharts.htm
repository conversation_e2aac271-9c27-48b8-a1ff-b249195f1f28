<!DOCTYPE html>
<html>
	<head>
		<title>Вставка и редактирование диаграмм</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в презентацию диаграмму и скорректируйте ее свойства" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
    <body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка и редактирование диаграмм</h1>
            <h3>Вставка диаграммы</h3>
            <p><b>Для вставки диаграммы</b> в презентацию,</p>
            <ol>
                <li>Установите курсор там, где требуется поместить диаграмму.</li>
                <li>Перейдите на вкладку <b>Вставка</b> верхней панели инструментов.</li>
                <li>Щелкните по значку <div class = "icon icon-insertchart"></div> <b>Диаграмма</b> на верхней панели инструментов.</li>
                <li>Выберите из доступных типов диаграммы:
                    <details class="details-example">
                        <summary>Гистограмма</summary>
                        <ul>
                            <li>Гистограмма с группировкой</li>
                            <li>Гистограмма с накоплением</li>
                            <li>Нормированная гистограмма с накоплением</li>
                            <li>Трехмерная гистограмма с группировкой</li>
                            <li>Трехмерная гистограмма с накоплением</li>
                            <li>Трехмерная нормированная гистограмма с накоплением</li>
                            <li>Трехмерная гистограмма</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>График</summary>
                        <ul>
                            <li>График</li>
                            <li>График с накоплением</li>
                            <li>Нормированный график с накоплением</li>
                            <li>График с маркерами</li>
                            <li>График с накоплениями с маркерами</li>
                            <li>Нормированный график с маркерами и накоплением</li>
                            <li>Трехмерный график</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Круговая</summary>
                        <ul>
                            <li>Круговая</li>
                            <li>Кольцевая диаграмма</li>
                            <li>Трехмерная круговая диаграмма</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Линейчатая</summary>
                        <ul>
                            <li>Линейчатая с группировкой</li>
                            <li>Линейчатая с накоплением</li>
                            <li>Нормированная линейчатая с накоплением</li>
                            <li>Трехмерная линейчатая с группировкой</li>
                            <li>Трехмерная линейчатая с накоплением</li>
                            <li>Трехмерная нормированная линейчатая с накоплением</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>С областями</summary>
                        <ul>
                            <li>С областями</li>
                            <li>Диаграмма с областями с накоплением</li>
                            <li>Нормированная с областями и накоплением</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Биржевая</summary>
                    </details>
                    <details class="details-example">
                        <summary>Точечная</summary>
                        <ul>
                            <li>Точечная диаграмма</li>
                            <li>Точечная с гладкими кривыми и маркерами</li>
                            <li>Точечная с гладкими кривыми</li>
                            <li>Точечная с прямыми отрезками и маркерами</li>
                            <li>Точечная с прямыми отрезками</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Комбинированные</summary>
                        <ul>
                            <li>Гистограмма с группировкой и график</li>
                            <li>Гистограмма с группировкой и график на вспомогательной оси</li>
                            <li>С областями с накоплением и гистограмма с группировкой</li>
                            <li>Пользовательская комбинация</li>
                        </ul>
                    </details>
                    <p class="note"><b>Примечание</b>: <b>Редактор презентаций ONLYOFFICE</b> поддерживает следующие типы диаграмм, созданных в сторонних редакторах: <b>Пирамида</b>, <b>Гистограмма (пирамида)</b>, <b>Горизонтальные/Вертикальные цилиндры</b>, <b>Горизонтальные/вертикальные конусы</b>. Вы можете открыть файл, содержащий такую диаграмму, и изменить его, используя доступные инструменты редактирования диаграмм.</p>
                </li>
                <li>После этого появится окно <b>Редактор диаграмм</b>, в котором можно ввести в ячейки необходимые данные при помощи следующих элементов управления:
                    <ul>
                        <li><div class = "icon icon-copy"></div> и <div class = "icon icon-paste"></div> для копирования и вставки скопированных данных</li>
                        <li><div class = "icon icon-undo1"></div> и <div class = "icon icon-redo1"></div> для отмены и повтора действий</li>
                        <li><div class = "icon icon-insertfunction"></div> для вставки функции</li>
                        <li><div class = "icon icon-decreasedec"></div> и <div class = "icon icon-increasedec"></div> для уменьшения и увеличения числа десятичных знаков</li>
                        <li><img alt="Формат чисел" src="../../../../../../common/main/resources/help/ru/images/numberformat.png" /> для изменения числового формата, то есть того, каким образом выглядят введенные числа в ячейках</li>
                        <li><img alt="Тип диаграммы" src="../../../../../../common/main/resources/help/ru/images/charttypebutton.png" /> для выбора диаграммы другого типа.</li>
                    </ul>
                    <p><img alt="Окно Редактор диаграмм" src="../../../../../../common/main/resources/help/ru/images/charteditor.png" /></p>
                </li>
                <li>
                    Нажмите кнопку <b>Выбрать данные</b>, расположенную в окне <b>Редактора диаграмм</b>. Откроется окно <b>Данные диаграммы</b>.
                    <ol>
                        <li>
                            Используйте диалоговое окно <b>Данные диаграммы</b> для управления <b>диапазоном данных диаграммы</b>, <b>элементами легенды (ряды)</b>, <b>подписями горизонтальной оси (категории)</b> и <b>переключением строк / столбцов</b>.
                            <p><img alt="Окно Диапазон данных" src="../../../../../../common/main/resources/help/ru/images/chartdata.png" /></p>
                            <ul>
                                <li>
                                    <b>Диапазон данных для диаграммы</b> - выберите данные для вашей диаграммы.
                                    <ul>
                                        <li>
                                            Щелкните значок <div class = "icon icon-changerange"></div> справа от поля <b>Диапазон данных для диаграммы</b>, чтобы выбрать диапазон ячеек.
                                            <p><img alt="Окно Диапазон данных для диаграммы" src="../../../../../../common/main/resources/help/ru/images/selectdata.png" /></p>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <b>Элементы легенды (ряды)</b> - добавляйте, редактируйте или удаляйте записи легенды. Введите или выберите ряд для записей легенды.
                                    <ul>
                                        <li>В Элементах легенды (ряды) нажмите кнопку <b>Добавить</b>.</li>
                                        <li>
                                            В диалоговом окне <b>Изменить ряд</b> выберите диапазон ячеек для легенды или нажмите на иконку <div class = "icon icon-changerange"></div> справа от поля <b>Имя ряда</b>.
                                            <p><img alt="Окно Изменить ряд" src="../../../../../../common/main/resources/help/ru/images/editseries.png" /></p>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <b>Подписи горизонтальной оси (категории)</b> - изменяйте текст подписи категории.
                                    <ul>
                                        <li>В <b>Подписях горизонтальной оси (категории)</b> нажмите <b>Редактировать</b>.</li>
                                        <li>
                                            В поле <b>Диапазон подписей оси</b> введите названия для категорий или нажмите на иконку <div class = "icon icon-changerange"></div>, чтобы выбрать диапазон ячеек.
                                            <p><img alt="Окно Подписи оси" src="../images/axislabels.png" /></p>
                                        </li>
                                    </ul>
                                </li>
                                <li><b>Переключить строку/столбец</b> - переставьте местами данные, которые расположены на диаграмме. Переключите строки на столбцы, чтобы данные отображались на другой оси.</li>
                            </ul>
                        </li>
                        <li>Нажмите кнопку <b>ОК</b>, чтобы применить изменения и закрыть окно.</li>
                    </ol>
                </li>
                <li>
                    Измените параметры диаграммы, нажав на кнопку <b>Изменить тип диаграммы</b> в окне <b>Редактор диаграмм</b>, чтобы выбрать тип и стиль диаграммы. Выберите диаграмму из доступных разделов: гистограмма, график, круговая, линейчатая, с областями, биржевая, точечная, комбинированные.
                    <p><img alt="Окно Тип диаграммы" src="../../../../../../common/main/resources/help/ru/images/charttype.png" /></p>
                    <p>Когда вы выбираете <b>Комбинированные диаграммы</b>, в окне <b>Тип диаграммы</b> расположены ряды диаграмм, для которых можно выбрать типы диаграмм и данные для размещения на вторичной оси.</p>
                    <p><img alt="Комбинированные диаграммы" src="../../../../../../common/main/resources/help/ru/images/charttype_combo.png" /></p>
                </li>
                <li>
                    Измените параметры диаграммы, нажав кнопку <b>Редактировать диаграмму</b> в окне <b>Редактор диаграмм</b>. Откроется окно <b>Диаграмма - Дополнительные параметры</b>.
                    <p><img alt="Окно Диаграмма - дополнительные параметры" src="../../../../../../common/main/resources/help/ru/images/chartsettings_layout.png" /></p>
                    <p>На вкладке <b>Макет</b> можно изменить расположение элементов диаграммы:</p>
                    <ul>
                        <li>
                            Укажите местоположение <b>Заголовка диаграммы</b> относительно диаграммы, выбрав нужную опцию из выпадающего списка:
                            <ul>
                                <li><b>Нет</b>, чтобы заголовок диаграммы не отображался,</li>
                                <li><b>Наложение</b>, чтобы наложить заголовок на область построения диаграммы и выровнять его по центру,</li>
                                <li><b>Без наложения</b>, чтобы показать заголовок над областью построения диаграммы.</li>
                            </ul>
                        </li>
                        <li>
                            Укажите местоположение <b>Условных обозначений</b> относительно диаграммы, выбрав нужную опцию из выпадающего списка:
                            <ul>
                                <li><b>Нет</b>, чтобы условные обозначения не отображались,</li>
                                <li><b>Снизу</b>, чтобы показать условные обозначения и расположить их в ряд под областью построения диаграммы,</li>
                                <li><b>Сверху</b>, чтобы показать условные обозначения и расположить их в ряд над областью построения диаграммы,</li>
                                <li><b>Справа</b>, чтобы показать условные обозначения и расположить их справа от области построения диаграммы,</li>
                                <li><b>Слева</b>, чтобы показать условные обозначения и расположить их слева от области построения диаграммы,</li>
                                <li><b>Наложение слева</b>, чтобы наложить условные обозначения на область построения диаграммы и выровнять их по центру слева,</li>
                                <li><b>Наложение справа</b>,  чтобы наложить условные обозначения на область построения диаграммы и выровнять их по центру справа.</li>
                            </ul>
                        </li>
                        <li>
                            Определите параметры <b>Подписей данных</b> (то есть текстовых подписей, показывающих точные значения элементов данных):<br />
                            <ul>
                                <li>Укажите местоположение <b>Подписей данных</b> относительно элементов данных, выбрав нужную опцию из выпадающего списка. Доступные варианты зависят от выбранного типа диаграммы.
                                    <ul>
                                        <li>Для <b>Гистограмм</b> и <b>Линейчатых</b> диаграмм можно выбрать следующие варианты: <b>Нет</b>, <b>По центру</b>, <b>Внутри снизу</b>, <b>Внутри сверху</b>, <b>Снаружи сверху</b>.</li>
                                        <li>Для <b>Графиков</b> и <b>Точечных</b> или <b>Биржевых</b> диаграмм можно выбрать следующие варианты: <b>Нет</b>, <b>По центру</b>, <b>Слева</b>, <b>Справа</b>, <b>Сверху</b>, <b>Снизу</b>.</li>
                                        <li>Для <b>Круговых</b> диаграмм можно выбрать следующие варианты: <b>Нет</b>, <b>По центру</b>, <b>По ширине</b>, <b>Внутри сверху</b>, <b>Снаружи сверху</b>.</li>
                                        <li>Для диаграмм <b>С областями</b>, а также для <b>Гистограмм</b>, <b>Графиков</b> и <b>Линейчатых</b> диаграмм в <b>формате 3D</b> можно выбрать следующие варианты: <b>Нет</b>, <b>По центру</b>.</li>
                                    </ul>
                                </li>
                                <li>Выберите данные, которые вы хотите включить в ваши подписи, поставив соответствующие флажки: <b>Имя ряда</b>, <b>Название категории</b>, <b>Значение</b>,</li>
                                <li>Введите символ (запятая, точка с запятой и т.д.), который вы хотите использовать для разделения нескольких подписей, в поле <b>Разделитель подписей данных</b>.</li>
                            </ul>
                        </li>
                        <li><b>Линии</b> - используется для выбора типа линий для <b>линейчатых/точечных диаграмм</b>. Можно выбрать одну из следующих опций: <b>Прямые</b> для использования прямых линий между элементами данных, <b>Сглаженные</b> для использования сглаженных кривых линий между элементами данных или <b>Нет</b> для того, чтобы линии не отображались.</li>
                        <li>
                            <b>Маркеры</b> - используется для указания того, нужно показывать маркеры (если флажок поставлен) или нет (если флажок снят) на <b>линейчатых/точечных диаграммах</b>.
                            <p class="note"><b>Примечание</b>: Опции <b>Линии</b> и <b>Маркеры</b> доступны только для <b>Линейчатых диаграмм</b> и <b>Точечных диаграмм</b>.</p>
                        </li>
                    </ul>
                    <p><img alt="Диаграмма - дополнительные параметры" src="../../../../../../common/main/resources/help/ru/images/chartsettings_verticalaxis.png" /></p>
                    <p>Вкладка <b>Вертикальная ось</b> позволяет изменять параметры вертикальной оси, также называемой осью значений или осью Y, которая отображает числовые значения. Обратите внимание, что вертикальная ось будет осью категорий, которая отображает подпись для <b>Гистограмм</b>, таким образом, параметры вкладки <b>Вертикальная ось</b> будут соответствовать параметрам, описанным в следующем разделе. Для <b>Точечных диаграмм</b> обе оси являются осями значений.</p>
                    <p class="note"><b>Примечание</b>: <b>Параметры оси</b> и <b>Линии сетки</b> недоступны для круговых диаграмм, так как у круговых диаграмм нет осей и линий сетки.</p>
                    <ul>
                        <li>Нажмите <b>Скрыть ось</b>, чтобы скрыть вертикальную ось на диаграмме.</li>
                        <li>
                            Укажите ориентацию <b>Заголовка</b>, выбрав нужный вариант из раскрывающегося списка:
                            <ul>
                                <li><b>Нет</b> - не отображать название вертикальной оси,</li>
                                <li><b>Повернутое</b> - показать название снизу вверх слева от вертикальной оси,</li>
                                <li><b>По горизонтали</b> - показать название по горизонтали слева от вертикальной оси.</li>
                            </ul>
                        </li>
                        <li>
                            <b>Минимум</b> - используется для указания наименьшего значения, которое отображается в начале вертикальной оси. По умолчанию выбрана опция <b>Авто</b>;
                            в этом случае минимальное значение высчитывается автоматически в зависимости от выбранного диапазона данных. Можно выбрать
                            из выпадающего списка опцию <b>Фиксированный</b> и указать в поле справа другое значение.
                        </li>
                        <li>
                            <b>Максимум</b> - используется для указания наибольшего значения, которое отображается в конце вертикальной оси. По умолчанию выбрана опция <b>Авто</b>;
                            в этом случае максимальное значение высчитывается автоматически в зависимости от выбранного диапазона данных. Можно выбрать
                            из выпадающего списка опцию <b>Фиксированный</b> и указать в поле справа другое значение.
                        </li>
                        <li>
                            <b>Пересечение с осью</b> - используется для указания точки на вертикальной оси, в которой она должна пересекаться с горизонтальной осью.
                            По умолчанию выбрана опция <b>Авто</b>; в этом случае точка пересечения осей определяется автоматически в зависимости от выбранного диапазона данных.
                            Можно выбрать из выпадающего списка опцию <b>Значение</b> и указать в поле справа другое значение или установить точку пересечения осей на <b>Минимум/Максимум</b> на вертикальной оси.
                        </li>
                        <li>
                            <b>Единицы отображения</b> - используется для определения порядка числовых значений на вертикальной оси. Эта опция
                            может пригодиться, если вы работаете с большими числами и хотите, чтобы отображение цифр на оси было более компактным и удобочитаемым
                            (например, можно сделать так, чтобы 50 000 показывалось как 50, воспользовавшись опцией <b>Тысячи</b>). Выберите желаемые единицы отображения
                            из выпадающего списка: <b>Сотни</b>, <b>Тысячи</b>, <b>10 000</b>, <b>100 000</b>, <b>Миллионы</b>, <b>10 000 000</b>, <b>100 000 000</b>,
                            <b>Миллиарды</b>, <b>Триллионы</b> или выберите опцию <b>Нет</b>, чтобы вернуться к единицам отображения по умолчанию.
                        </li>
                        <li>
                            <b>Значения в обратном порядке</b> - используется для отображения значений в обратном порядке. Когда этот флажок снят, наименьшее значение находится
                            внизу, а наибольшее - наверху. Когда этот флажок отмечен, значения располагаются сверху вниз.
                        </li>
                        <li>
                            Раздел <b>Параметры делений</b> позволяет настроить отображение делений на вертикальной шкале. Основной тип - это деления шкалы большего размера, на которых могут быть подписи с числовыми значениями. Дополнительный тип - это деления шкалы, которые помещаются между основными делениями и не имеют подписей. Отметки также определяют, где могут отображаться линии сетки, если соответствующий параметр установлен на вкладке <b>Макет</b>. В раскрывающихся списках <b>Основной/Дополнительный тип</b> содержатся следующие варианты размещения:
                            <ul>
                                <li><b>Нет</b> - не отображать основные/дополнительные деления,</li>
                                <li><b>На пересечении</b> - отображать основные/дополнительные деления по обе стороны от оси,</li>
                                <li><b>Внутри</b> - отображать основные/дополнительные деления внутри оси,</li>
                                <li><b>Снаружи</b> - отображать основные/дополнительные деления за пределами оси.</li>
                            </ul>
                        </li>
                        <li>
                            Раздел <b>Параметры подписи</b> позволяет определить положение подписей основных делений, отображающих значения. Для того, чтобы задать <b>Положение подписи</b> относительно вертикальной оси, выберите нужную опцию из выпадающего списка:
                            <ul>
                                <li><b>Нет</b> - не отображать подписи,</li>
                                <li><b>Ниже</b> - показывать подписи слева от области диаграммы,</li>
                                <li><b>Выше</b> - показывать подписи справа от области диаграммы,</li>
                                <li><b>Рядом с осью</b> - показывать подписи рядом с осью.</li>
                                <li>
                                    Чтобы указать <b>Формат подписи</b>, нажмите Формат подписи и в окне <b>Числовой формат</b> выберите подходящую категорию.
                                    <p>Доступные категории подписей:</p>
                                    <ul>
                                        <li>Общий</li>
                                        <li>Числовой</li>
                                        <li>Научный</li>
                                        <li>Финансовый</li>
                                        <li>Денежный</li>
                                        <li>Дата</li>
                                        <li>Время</li>
                                        <li>Процентный</li>
                                        <li>Дробный</li>
                                        <li>Текстовый</li>
                                        <li>Особый</li>
                                    </ul>
                                    <p>Параметры формата подписи различаются в зависимости от выбранной категории. Для получения дополнительной информации об изменении числового формата, пожалуйста, обратитесь к <a href="https://helpcenter.onlyoffice.com/ONLYOFFICE-Editors/ONLYOFFICE-Spreadsheet-Editor/UsageInstructions/ChangeNumberFormat.aspx">этой странице</a>.</p>
                                </li>
                                <li>Установите флажок напротив <b>Связать с источником</b>, чтобы сохранить форматирование чисел из источника данных в диаграмме.</li>
                            </ul>
                        </li>
                    </ul>

                    <p><img alt="Диаграмма - окно дополнительные параметры" src="../../../../../../common/main/resources/help/ru/images/chartsettings_secondaryaxis1.png" /></p>
                    <p class="note"><b>Примечание</b>: второстепенные оси поддерживаются только в <b>Комбинированных</b> диаграммах.</p>
                    <p><b>Второстепенные оси</b> полезны в комбинированных диаграммах, когда ряды данных значительно различаются или для построения диаграммы используются смешанные типы данных. Второстепенные оси упрощают чтение и понимание комбинированной диаграммы.</p>
                    <p>Вкладка <b>Вспомогательная вертикальная / горизонтальная ось</b> появляется, когда вы выбираете соответствующий ряд данных для комбинированной диаграммы. Все настройки и параметры на вкладке <b>Вспомогательная вертикальная/горизонтальная ось</b> такие же, как настройки на вертикальной / горизонтальной оси. Подробное описание параметров <b>Вертикальная / горизонтальная ось</b> смотрите выше / ниже.</p>
                    <p><img alt="Диаграмма - окно дополнительные параметры" src="../../../../../../common/main/resources/help/ru/images/chartsettings_horizontalaxis.png" /></p>
                    <p>Вкладка <b>Горизонтальная ось</b> позволяет изменять параметры горизонтальной оси, также называемой осью категорий или осью x, которая отображает текстовые метки. Обратите внимание, что горизонтальная ось будет осью значений, которая отображает числовые значения для <b>Гистограмм</b>, поэтому в этом случае параметры вкладки <b>Горизонтальная ось</b> будут соответствовать параметрам, описанным в предыдущем разделе. Для <b>Точечных диаграмм</b> обе оси являются осями значений.</p>
                    <ul>
                        <li>Нажмите <b>Скрыть ось</b>, чтобы скрыть горизонтальную ось на диаграмме.</li>
                        <li>
                            Укажите ориентацию <b>Заголовка</b>, выбрав нужный вариант из раскрывающегося списка:
                            <ul>
                                <li><b>Нет</b> - не отображать заголовок горизонтальной оси,</li>
                                <li><b>Без наложения</b> - отображать заголовок под горизонтальной осью,</li>
                            </ul>
                        </li>
                        <li><b>Линии сетки</b> используется для отображения <b>Горизонтальных линий сетки</b> путем выбора необходимого параметра в раскрывающемся списке: <b>Нет</b>, <b>Основные</b>, <b>Незначительное</b> или <b>Основные и Дополнительные</b>.</li>
                        <li><b>Пересечение с осью</b> - используется для указания точки на горизонтальной оси, в которой вертикальная ось должна пересекать ее. По умолчанию выбрана опция <b>Авто</b>, в этом случае точка пересечения осей определяется автоматически в зависимости от выбранного диапазона данных. Из выпадающего списка можно выбрать опцию <b>Значение</b> и указать в поле справа другое значение или установить точку пересечения осей на <b>Минимальном/Максимальном</b> значении на вертикальной оси.</li>
                        <li><b>Положение оси</b> - используется для указания места размещения подписей на оси: на <b>Делениях</b> или <b>Между делениями</b>.</li>
                        <li><b>Значения в обратном порядке</b> - используется для отображения категорий в обратном порядке. Когда этот флажок снят, категории располагаются слева направо. Когда этот флажок отмечен, категории располагаются справа налево.</li>
                        <li>
                            Раздел <b>Параметры делений</b> позволяет определять местоположение делений на горизонтальной шкале. Деления основного типа - это более крупные
                            деления шкалы, у которых могут быть подписи, отображающие значения категорий. Деления дополнительного типа - это более мелкие деления шкалы,
                            которые располагаются между делениями основного типа и у которых нет подписей. Кроме того, деления шкалы указывают, где могут отображаться
                            линии сетки, если на вкладке <b>Макет</b> выбрана соответствующая опция. Можно редактировать следующие параметры делений:
                            <ul>
                                <li><b>Основной/Дополнительный тип</b> - используется для указания следующих вариантов размещения: <b>Нет</b>, чтобы деления основного/дополнительного типа не отображались, <b>На пересечении</b>, чтобы отображать деления основного/дополнительного типа по обеим сторонам оси, <b>Внутри</b> чтобы отображать деления основного/дополнительного типа с внутренней стороны оси, <b>Снаружи</b>, чтобы отображать деления основного/дополнительного типа с наружной стороны оси.</li>
                                <li><b>Интервал между делениями</b> - используется для указания того, сколько категорий нужно показывать между двумя соседними делениями.</li>
                            </ul>
                        </li>
                        <li>
                            Раздел <b>Параметры подписи</b> позволяет настроить внешний вид меток, отображающих категории.
                            <ul>
                                <li><b>Положение подписи</b> - используется для указания того, где следует располагать подписи относительно горизонтальной оси. Выберите нужную опцию из выпадающего списка: <b>Нет</b>, чтобы подписи  категорий не отображались, <b>Ниже</b>, чтобы подписи категорий располагались снизу области диаграммы, <b>Выше</b>, чтобы подписи категорий располагались наверху области диаграммы, <b>Рядом с осью</b>, чтобы подписи категорий отображались рядом с осью.</li>
                                <li><b>Расстояние до подписи</b> - используется для указания того, насколько близко подписи должны располагаться от осей. Можно указать нужное значение в поле ввода. Чем это значение больше, тем дальше расположены подписи от осей.</li>
                                <li><b>Интервал между подписями</b> - используется для указания того, как часто нужно показывать подписи. По умолчанию выбрана опция <b>Авто</b>, в этом случае подписи отображаются для каждой категории. Можно выбрать опцию <b>Вручную</b> и указать нужное значение в поле справа. Например, введите 2, чтобы отображать подписи у каждой второй категории, и т.д.</li>
                                <li>
                                    Чтобы указать <b>Формат подписи</b>, нажмите Формат подписи и в окне <b>Числовой формат</b> выберите подходящую категорию.
                                    <p>Доступные категории подписей:</p>
                                    <ul>
                                        <li>Общий</li>
                                        <li>Числовой</li>
                                        <li>Научный</li>
                                        <li>Финансовый</li>
                                        <li>Денежный</li>
                                        <li>Дата</li>
                                        <li>Время</li>
                                        <li>Процентный</li>
                                        <li>Дробный</li>
                                        <li>Текстовый</li>
                                        <li>Особый</li>
                                    </ul>
                                    <p>Параметры формата подписи различаются в зависимости от выбранной категории. Для получения дополнительной информации об изменении числового формата, пожалуйста, обратитесь к <a href="https://helpcenter.onlyoffice.com/ONLYOFFICE-Editors/ONLYOFFICE-Spreadsheet-Editor/UsageInstructions/ChangeNumberFormat.aspx">этой странице</a>.</p>
                                </li>
                                <li>Установите флажок напротив <b>Связать с источником</b>, чтобы сохранить форматирование чисел из источника данных в диаграмме.</li>
                            </ul>
                        </li>
                    </ul>
                    <p><img alt="Диаграмма - окно дополнительные параметры: Привязка к ячейке" src="../../../../../../common/main/resources/help/ru/images/chartsettings_cellsnapping.png" /></p>
                    <p>Вкладка <b>Привязка к ячейке</b> содержит следующие параметры:</p>
                    <ul>
                        <li><b>Перемещать и изменять размеры вместе с ячейками</b> - эта опция позволяет привязать диаграмму к ячейке позади нее. Если ячейка перемещается (например, при вставке или удалении нескольких строк/столбцов), диаграмма будет перемещаться вместе с ячейкой. При увеличении или уменьшении ширины или высоты ячейки размер диаграммы также будет изменяться.</li>
                        <li><b>Перемещать, но не изменять размеры вместе с ячейками</b> - эта опция позволяет привязать диаграмму к ячейке позади нее, не допуская изменения размера диаграммы. Если ячейка перемещается, диаграмма будет перемещаться вместе с ячейкой, но при изменении размера ячейки размеры диаграммы останутся неизменными.</li>
                        <li><b>Не перемещать и не изменять размеры вместе с ячейками</b> - эта опция позволяет запретить перемещение или изменение размера диаграммы при изменении положения или размера ячейки.</li>
                    </ul>
                    <p><img alt="Диаграмма - дополнительные параметры" src="../../../../../../common/main/resources/help/ru/images/chartsettings_alternativetext.png" /></p>
                    <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит диаграмма.</p>
                </li>
                <li>
                    После того, как диаграмма будет добавлена, можно изменить ее <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">размер и положение</a>.
                    <p>Вы можете задать <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">положение диаграммы</a> на слайде, перетащив ее по вертикали или горизонтали.</p>
                </li>
             </ol>
            <p>Также можно добавить диаграмму в текстовый заполнитель, нажав на значок <span class="icon icon-placeholder_chart"></span> <b>Диаграмма</b> внутри него и выбрав нужный тип диаграммы:</p>
            <p><img alt="Добавление диаграммы в заполнитель" src="../images/placeholder_object.png" /></p>
            <p>Также можно добавить диаграмму в макет слайда. Для получения дополнительной информации вы можете обратиться к этой <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">статье</a>.</p>
            <hr />
            <h3>Редактирование элементов диаграммы</h3>
            <p>Чтобы изменить <b>Заголовок</b> диаграммы, выделите мышью стандартный текст и введите вместо него свой собственный.</p>
            <p>Чтобы изменить форматирование шрифта внутри текстовых элементов, таких как заголовок диаграммы, названия осей, элементы условных обозначений, подписи данных и так далее, выделите нужный текстовый элемент, щелкнув по нему левой кнопкой мыши. Затем используйте значки на вкладке <b>Главная</b> верхней панели инструментов, чтобы изменить <a href="../UsageInstructions/InsertText.htm#formatfont" onclick="onhyperlinkclick(this)">тип, стиль, размер или цвет</a> шрифта.</p>
            <p>
                При выборе диаграммы становится также активным значок <b>Параметры фигуры</b> <span class="icon icon-shape_settings_icon"></span>
                справа, так как фигура используется в качестве фона для диаграммы. Можно щелкнуть по этому значку, чтобы открыть вкладку
                <a href="InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">Параметры фигуры</a> на правой боковой панели инструментов и изменить параметры <b>Заливки</b> и <b>Обводки</b> фигуры.
                Обратите, пожалуйста, внимание, что вы не можете изменить тип фигуры.
            </p>
            <p>
                C помощью вкладки <b>Параметры фигуры</b> на правой боковой панели можно изменить не только саму область диаграммы, но и элементы диаграммы, такие как <em>область построения</em>, <em>ряды данных</em>, <em>заголовок диаграммы</em>, <em>легенда</em> и другие, и применить к ним различные типы заливки. Выберите элемент диаграммы, нажав на него левой кнопкой мыши, и выберите нужный тип заливки: <em>сплошной цвет</em>, <em>градиент</em>, <em>текстура</em> или <em>изображение</em>, <em>узор</em>. Настройте параметры заливки и при необходимости задайте уровень <em>прозрачности</em>.
                При выделении вертикальной или горизонтальной оси или линий сетки на вкладке <b>Параметры фигуры</b> будут доступны только параметры обводки: <em>цвет</em>, <em>толщина</em> и <em>тип</em> линии. Для получения дополнительной информации о работе с цветами, заливками и обводкой фигур можно обратиться к <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">этой странице</a>.
            </p>
            <p class="note"><b>Обратите внимание</b>: параметр <b>Отображать тень</b> также доступен на вкладке <b>Параметры фигуры</b>, но для элементов диаграммы он неактивен.</p>
            <p>Если вам нужно изменить размер элементов диаграммы, щелкните левой кнопкой мыши, чтобы выбрать нужный элемент, и перетащите один из 8 белых квадратов <span class="icon icon-resize_square"></span>, расположенных по периметру элемента.</p>
            <p><span class="big big-resizeelement"></span></p>
            <p>Чтобы изменить положение элемента, щелкните по нему левой кнопкой мыши, убедитесь, что ваш курсор изменился на <span class="icon icon-arrow"></span>, удерживайте левую кнопку мыши и перетащите элемент в нужное положение.</p>
            <p><span class="big big-moveelement"></span></p>
            <p>Чтобы удалить элемент диаграммы, выделите его, щелкнув левой кнопкой мыши, и нажмите клавишу <b>Delete</b> на клавиатуре.</p>
            <p>Можно также поворачивать 3D-диаграммы с помощью мыши. Щелкните левой кнопкой мыши внутри области построения диаграммы и удерживайте кнопку мыши. Не отпуская кнопку мыши, перетащите курсор, чтобы изменить ориентацию 3D-диаграммы.</p>
            <p><img alt="3D-диаграмма" src="../../../../../../common/main/resources/help/ru/images/3dchart.png" /></p>
            <hr />
            <h3>Изменение параметров диаграммы</h3>
            <img class="Chart tab" alt="Вкладка Параметры диаграммы" src="../images/charttab.png" />
            <p>Размер, тип и стиль диаграммы, а также данные, используемые для построения диаграммы, можно изменить с помощью правой боковой панели. Чтобы ее активировать, щелкните по диаграмме и выберите значок <b>Параметры диаграммы</b> <span class="icon icon-chart_settings_icon"></span> справа.</p>
            <p>Раздел <b>Размер</b> позволяет изменить ширину и/или высоту диаграммы. Если нажата кнопка <b>Сохранять пропорции</b> <span class="icon icon-constantproportions"></span> (в этом случае она выглядит так: <span class="icon icon-constantproportionsactivated"></span>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон диаграммы.</p>
            <p>Раздел <b>Изменить тип диаграммы</b> позволяет изменить выбранный тип и/или стиль диаграммы с помощью соответствующего выпадающего меню.</p>
            <p>Для выбора нужного <b>Стиля</b> диаграммы используйте второе выпадающее меню в разделе <b>Изменить тип диаграммы</b>.</p>
            <p>Кнопка <b>Изменить данные</b> позволяет вызвать окно <b>Редактор диаграмм</b> и начать редактирование данных, как описано выше.</p>
            <p class="note"><b>Примечание</b>: чтобы быстро вызвать окно <b>Редактор диаграмм</b>, можно также дважды щелкнуть мышью по диаграмме на слайде.</p>
            <p>Для 3D-диаграмм также доступны настройки <b>Трехмерного поворота</b>:</p>
            <p><img class="floatleft" alt="Вкладка Параметры диаграммы" src="../../../../../../common/main/resources/help/ru/images/right_chart_3d.png" /></p>
            <ul style="margin-left: 280px;">
                <li><b>По оси X</b> - задайте нужное значение для поворота по оси X с помощью клавиатуры или расположенных справа стрелок <em>Влево</em> и <em>Вправо</em>.</li>
                <li><b>По оси Y</b> - задайте нужное значение для поворота по оси Y с помощью клавиатуры или расположенных справа стрелок <em>Влево</em> и <em>Вправо</em>.</li>
                <li><b>Перспектива</b> - задайте нужное значение для поворота по глубине с помощью клавиатуры или расположенных справа стрелок <em>Сузить поле зрения</em> и <em>Расширить поле зрения</em>.</li>
                <li><b>Оси под прямым углом</b> - используется, чтобы установить вид оси под прямым углом.</li>
                <li><b>Автомасштабирование</b> - отметьте эту опцию, чтобы автоматически масштабировать значения глубины и высоты диаграммы, или снимите эту галочку, чтобы задать значения глубины и высоты вручную.</li>
                <li><b>Глубина (% от базовой)</b> - задайте нужное значение глубины с помощью клавиатуры или стрелок.</li>
                <li><b>Высота (% от базовой)</b> - задайте нужное значение высоты с помощью клавиатуры или стрелок.</li>
                <li>
                    <b>Поворот по умолчанию</b> - установите параметры трехмерного поворота по умолчанию.
                    <p class="note">Пожалуйста, обратите внимание, что нельзя отредактировать отдельные элементы диаграммы; настройки будут применены к диаграмме в целом.</p>
                </li>
            </ul>
            <p>Опция <b>Дополнительные параметры</b> на правой боковой панели позволяет открыть окно <b>Диаграмма - дополнительные параметры</b>, в котором можно настроить следующие параметры:</p>
            <p><img alt="Окно дополнительных параметров диаграммы" src="../images/chartsettings7.png" /></p>
            <p>На вкладке <b>Положение</b> можно задать следующие свойства:</p>
            <ul>
                <li><b>Размер</b> - используйте эту опцию, чтобы изменить ширину и/или высоту диаграммы. Если нажата кнопка <b>Сохранять пропорции</b> <span class="icon icon-constantproportions"></span> (в этом случае она выглядит так: <span class="icon icon-constantproportionsactivated"></span>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон диаграммы.</li>
                <li><b>Позиция</b> - задайте точную позицию, используя поля <b>По горизонтали</b> и <b>По вертикали</b>, а также поле <b>От</b>, где доступны параметры <b>Верхний левый угол</b> и <b>По центру</b>.</li>
            </ul>
            <p><img alt="Окно дополнительных параметров диаграммы" src="../images/chart_properties_alternative.png" /></p>
            <hr />
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит диаграмма.</p>
            <p>Чтобы <b>удалить</b> добавленную диаграмму, щелкните по ней левой кнопкой мыши и нажмите клавишу <b>Delete</b> на клавиатуре.</p>
            <p>Чтобы узнать, как <b>выровнять</b> диаграмму на слайде или <b>расположить в определенном порядке</b> несколько объектов, обратитесь к разделу <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">Выравнивание и упорядочивание объектов на слайде</a>.</p>
        </div>
    </body>
</html>