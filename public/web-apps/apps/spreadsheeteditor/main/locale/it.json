{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Avviso", "Common.Controllers.Chat.textEnterMessage": "Inserisci il tuo messaggio qui", "Common.Controllers.History.notcriticalErrorTitle": "Avvertimento", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Area impilata", "Common.define.chartData.textAreaStackedPer": "Area impilata al 100%", "Common.define.chartData.textBar": "A barre", "Common.define.chartData.textBarNormal": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal3d": "Colonna 3D raggruppata", "Common.define.chartData.textBarNormal3dPerspective": "Colonna in 3D", "Common.define.chartData.textBarStacked": "Colonna in pila", "Common.define.chartData.textBarStacked3d": "Colonna 3D impilata", "Common.define.chartData.textBarStackedPer": "Colonna in pila 100%", "Common.define.chartData.textBarStackedPer3d": "Colonna 3D al 100% con impilamento", "Common.define.chartData.textCharts": "<PERSON><PERSON>", "Common.define.chartData.textColumn": "<PERSON>onna", "Common.define.chartData.textColumnSpark": "<PERSON>onna", "Common.define.chartData.textCombo": "Combinato", "Common.define.chartData.textComboAreaBar": "Area in pila - colonna raggruppata", "Common.define.chartData.textComboBarLine": "<PERSON>onna rag<PERSON> - riga", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON>na rag<PERSON> - linea sull'asse secondario", "Common.define.chartData.textComboCustom": "Combinazione personalizzata", "Common.define.chartData.textDoughnut": "Grafico a doppia ciambella", "Common.define.chartData.textHBarNormal": "Barra raggruppata", "Common.define.chartData.textHBarNormal3d": "Barra 3D raggruppata", "Common.define.chartData.textHBarStacked": "Barra in pila", "Common.define.chartData.textHBarStacked3d": "Barra 3D impilata", "Common.define.chartData.textHBarStackedPer": "Barra in pila al 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D 100% impilata", "Common.define.chartData.textLine": "Linea", "Common.define.chartData.textLine3d": "Linea in 3D", "Common.define.chartData.textLineMarker": "Linea con pennarelli", "Common.define.chartData.textLineSpark": "Linea", "Common.define.chartData.textLineStacked": "Linea impilata", "Common.define.chartData.textLineStackedMarker": "Linea impilata con pennarelli", "Common.define.chartData.textLineStackedPer": "Linea impilata al 100%", "Common.define.chartData.textLineStackedPerMarker": "Linea impilata al 100% con pennarelli", "Common.define.chartData.textPie": "A torta", "Common.define.chartData.textPie3d": "Grafico a torta in 3D", "Common.define.chartData.textPoint": "XY (A dispersione)", "Common.define.chartData.textScatter": "Grafico a dispersione", "Common.define.chartData.textScatterLine": "Grafico a dispersione con linee rette", "Common.define.chartData.textScatterLineMarker": "Grafico a dispersione con linee rette e pennarelli", "Common.define.chartData.textScatterSmooth": "Grafico a dispersione con linee leggere", "Common.define.chartData.textScatterSmoothMarker": "Grafico a dispersione con linee e indicatori", "Common.define.chartData.textSparks": "Sparkline", "Common.define.chartData.textStock": "Azionario", "Common.define.chartData.textSurface": "Superficie", "Common.define.chartData.textWinLossSpark": "Vinci/Perdi", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Formato non impostato", "Common.define.conditionalData.text1Above": "1 deviazione standard sopra", "Common.define.conditionalData.text1Below": "1 deviazione standard sotto", "Common.define.conditionalData.text2Above": "2 deviazioni standard sopra", "Common.define.conditionalData.text2Below": "2 deviazioni standard sotto", "Common.define.conditionalData.text3Above": "3 deviazioni standard sopra", "Common.define.conditionalData.text3Below": "3 deviazioni standard sotto", "Common.define.conditionalData.textAbove": "Sopra", "Common.define.conditionalData.textAverage": "Media", "Common.define.conditionalData.textBegins": "Inizia con", "Common.define.conditionalData.textBelow": "<PERSON>tto", "Common.define.conditionalData.textBetween": "Tra", "Common.define.conditionalData.textBlank": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlanks": "contiene spazi vuoti", "Common.define.conditionalData.textBottom": "In basso", "Common.define.conditionalData.textContains": "contiene", "Common.define.conditionalData.textDataBar": "Barra di dati", "Common.define.conditionalData.textDate": "Data", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "finisce con", "Common.define.conditionalData.textEqAbove": "Uguale o superiore", "Common.define.conditionalData.textEqBelow": "Uguale o inferiore", "Common.define.conditionalData.textEqual": "Uguale a", "Common.define.conditionalData.textError": "Errore", "Common.define.conditionalData.textErrors": "contiene errori", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "Maggiore di", "Common.define.conditionalData.textGreaterEq": "Maggiore o uguale a", "Common.define.conditionalData.textIconSets": "Set di icone", "Common.define.conditionalData.textLast7days": "Negli ultimi 7 giorni", "Common.define.conditionalData.textLastMonth": "ultimo mese", "Common.define.conditionalData.textLastWeek": "ultima settimana", "Common.define.conditionalData.textLess": "Meno di", "Common.define.conditionalData.textLessEq": "Inferiore o uguale a", "Common.define.conditionalData.textNextMonth": "Mese prossimo", "Common.define.conditionalData.textNextWeek": "Settimana prossima", "Common.define.conditionalData.textNotBetween": "non tra", "Common.define.conditionalData.textNotBlanks": "non contiene spazi vuoti", "Common.define.conditionalData.textNotContains": "non contiene", "Common.define.conditionalData.textNotEqual": "Non uguale a", "Common.define.conditionalData.textNotErrors": "non contiene errori", "Common.define.conditionalData.textText": "<PERSON><PERSON>", "Common.define.conditionalData.textThisMonth": "<PERSON>o mese", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> setti<PERSON>", "Common.define.conditionalData.textToday": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON>", "Common.define.conditionalData.textTop": "In alto", "Common.define.conditionalData.textUnique": "Unico", "Common.define.conditionalData.textValue": "<PERSON><PERSON>", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON>", "Common.define.smartArt.textBalance": "Equilibri", "Common.define.smartArt.textEquation": "Equazione", "Common.define.smartArt.textFunnel": "Imbuto", "Common.define.smartArt.textList": "Elenco", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textOther": "Altro", "Common.define.smartArt.textPicture": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.textMoreButton": "più", "Common.Translation.warnFileLocked": "Il file è in fase di modifica in un'altra applicazione. Puoi continuare a modificarlo e salvarlo come copia.", "Common.Translation.warnFileLockedBtnEdit": "Crea una copia", "Common.Translation.warnFileLockedBtnView": "‎Aperto per la visualizzazione‎", "Common.UI.ButtonColored.textAutoColor": "Automatico", "Common.UI.ButtonColored.textNewColor": "Aggiungi Colore personalizzato", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON> stile", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Attuale", "Common.UI.ExtendedColorDialog.textHexErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nuovo", "Common.UI.ExtendedColorDialog.textRGBErr": "Il valore inserito non è corretto.<br>Inserisci un valore numerico tra 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Nessun colore", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Nascondi password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostra password", "Common.UI.SearchBar.textFind": "Ricerca", "Common.UI.SearchBar.tipCloseSearch": "Chiudere la ricerca", "Common.UI.SearchBar.tipNextResult": "Risultato successivo", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Aprire le impostazioni avanzate", "Common.UI.SearchBar.tipPreviousResult": "Risultato precedente", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON> ris<PERSON>ati", "Common.UI.SearchDialog.textMatchCase": "Sensibile al maiuscolo/minuscolo", "Common.UI.SearchDialog.textReplaceDef": "Inserisci testo sostitutivo", "Common.UI.SearchDialog.textSearchStart": "Inserisci il tuo testo qui", "Common.UI.SearchDialog.textTitle": "Trova e sostituisci", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Solo parole intere", "Common.UI.SearchDialog.txtBtnHideReplace": "Nascondi Sostituzione", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Sostit<PERSON><PERSON><PERSON> tutto", "Common.UI.SynchronizeTip.textDontShow": "Non mostrare più questo messaggio", "Common.UI.SynchronizeTip.textSynchronize": "Il documento è stato modificato da un altro utente.<br><PERSON><PERSON><PERSON> per salvare le modifiche e ricaricare gli aggiornamenti.", "Common.UI.ThemeColorPalette.textRecentColors": "Colori recenti", "Common.UI.ThemeColorPalette.textStandartColors": "Colori standard", "Common.UI.ThemeColorPalette.textThemeColors": "Colori del tema", "Common.UI.Themes.txtThemeClassicLight": "Luce chiara", "Common.UI.Themes.txtThemeContrastDark": "Contrasto scuro", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Chiaro", "Common.UI.Themes.txtThemeSystem": "Uguale al sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON>", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Conferma", "Common.UI.Window.textDontShow": "Non mostrare più questo messaggio", "Common.UI.Window.textError": "Errore", "Common.UI.Window.textInformation": "Informazioni", "Common.UI.Window.textWarning": "Avviso", "Common.UI.Window.yesButtonText": "Sì", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "indirizzo: ", "Common.Views.About.txtLicensee": "LICENZIATARIO", "Common.Views.About.txtLicensor": "CONCEDENTE", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Con tecnologia", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versione", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Applica mentre lavori", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Correzione automatica", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formattazione automatica durante la scrittura", "Common.Views.AutoCorrectDialog.textBy": "Di", "Common.Views.AutoCorrectDialog.textDelete": "Elimina", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e percorsi di rete con i collegamenti ipertestuali", "Common.Views.AutoCorrectDialog.textMathCorrect": "Correzione automatica matematica", "Common.Views.AutoCorrectDialog.textNewRowCol": "Aggiungi nuove righe e colonne nella tabella", "Common.Views.AutoCorrectDialog.textRecognized": "Funzioni riconosciute", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Le seguenti espressioni sono espressioni matematiche riconosciute. Non verranno automaticamente scritte in corsivo.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Sostituisci mentre digiti", "Common.Views.AutoCorrectDialog.textReplaceType": "Sostituisci testo durante la scrittura", "Common.Views.AutoCorrectDialog.textReset": "Reimposta", "Common.Views.AutoCorrectDialog.textResetAll": "Ripristina valori predefiniti", "Common.Views.AutoCorrectDialog.textRestore": "R<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Correzione automatica", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Le funzioni riconosciute possono contenere soltanto lettere da A a Z, maiuscole o minuscole.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "‎Qualsiasi espressione aggiunta verrà rimossa e quelle rimosse verranno ripristinate. Vuoi continuare?‎", "Common.Views.AutoCorrectDialog.warnReplace": "La voce di correzione automatica per %1 esiste già. Vuoi sostituirla?", "Common.Views.AutoCorrectDialog.warnReset": "Tutte le correzioni automatiche aggiunte verranno rimosse e quelle modificate verranno ripristinate ai valori originali. Vuoi continuare?‎", "Common.Views.AutoCorrectDialog.warnRestore": "La voce di correzione automatica per %1 verrà reimpostata al suo valore originale. Vuoi continuare?", "Common.Views.Chat.textSend": "Invia", "Common.Views.Comments.mniAuthorAsc": "Autore dalla A alla Z", "Common.Views.Comments.mniAuthorDesc": "Autore dalla Z alla A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> vecchio", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON> recente", "Common.Views.Comments.mniFilterGroups": "Filtra per gruppo", "Common.Views.Comments.mniPositionAsc": "Dall'alto", "Common.Views.Comments.mniPositionDesc": "<PERSON> basso", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "Aggiungi commento", "Common.Views.Comments.textAddCommentToDoc": "Aggiungi commento al documento", "Common.Views.Comments.textAddReply": "Aggiungi risposta", "Common.Views.Comments.textAll": "<PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Ospite", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> commenti", "Common.Views.Comments.textComments": "Commenti", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Inserisci il commento qui", "Common.Views.Comments.textHintAddComment": "Aggiungi commento", "Common.Views.Comments.textOpenAgain": "Apri di nuovo", "Common.Views.Comments.textReply": "Rispondi", "Common.Views.Comments.textResolve": "Risolvere", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Ordinare commenti", "Common.Views.Comments.textViewResolved": "Non sei autorizzato a riaprire il commento", "Common.Views.Comments.txtEmpty": "Non ci sono commenti nel foglio.", "Common.Views.CopyWarningDialog.textDontShow": "Non mostrare più questo messaggio", "Common.Views.CopyWarningDialog.textMsg": "Le azioni di copia, taglia e incolla utilizzando i pulsanti della barra degli strumenti dell'editor e le azioni del menu di scelta rapida verranno eseguite solo all'interno di questa scheda dell'editor. <br><br>Per copiare o incollare in o da applicazioni esterne alla scheda dell'editor, utilizzare le seguenti combinazioni di tasti:", "Common.Views.CopyWarningDialog.textTitle": "Funzioni copia/taglia/incolla", "Common.Views.CopyWarningDialog.textToCopy": "per copiare", "Common.Views.CopyWarningDialog.textToCut": "per tagliare", "Common.Views.CopyWarningDialog.textToPaste": "per incollare", "Common.Views.DocumentAccessDialog.textLoading": "Caricamento in corso...", "Common.Views.DocumentAccessDialog.textTitle": "Impostazioni di condivisione", "Common.Views.EditNameDialog.textLabel": "Etichetta:", "Common.Views.EditNameDialog.textLabelError": "L'etichetta non deve essere vuota.", "Common.Views.Header.labelCoUsersDescr": "Utenti che stanno modificando il file:", "Common.Views.Header.textAddFavorite": "Segna come preferito", "Common.Views.Header.textAdvSettings": "Impostazioni avanzate", "Common.Views.Header.textBack": "Apri percorso file", "Common.Views.Header.textCompactView": "Mostra barra degli strumenti compatta", "Common.Views.Header.textHideLines": "Nascondi righelli", "Common.Views.Header.textHideStatusBar": "Combinare le barre di foglio e di stato", "Common.Views.Header.textRemoveFavorite": "Rimuovi dai preferiti", "Common.Views.Header.textSaveBegin": "Salvataggio in corso...", "Common.Views.Header.textSaveChanged": "Modificato", "Common.Views.Header.textSaveEnd": "Tutte le modifiche sono state salvate", "Common.Views.Header.textSaveExpander": "Tutte le modifiche sono state salvate", "Common.Views.Header.textShare": "Condividere", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Gestisci i diritti di accesso al documento", "Common.Views.Header.tipDownload": "Scarica file", "Common.Views.Header.tipGoEdit": "Modifica il file corrente", "Common.Views.Header.tipPrint": "Stampa file", "Common.Views.Header.tipRedo": "R<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON>", "Common.Views.Header.tipSearch": "Ricerca", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Sgancia in una finestra separata", "Common.Views.Header.tipUsers": "Visualizzare gli utenti", "Common.Views.Header.tipViewSettings": "Mostra impostazioni", "Common.Views.Header.tipViewUsers": "Mostra gli utenti e gestisci i diritti di accesso al documento", "Common.Views.Header.txtAccessRights": "Modifica diritti di accesso", "Common.Views.Header.txtRename": "Rinomina", "Common.Views.History.textCloseHistory": "Chiudere cronologia", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Nascondere le modifiche dettagliate", "Common.Views.History.textRestore": "Ripristinare", "Common.Views.History.textShow": "Espandi", "Common.Views.History.textShowAll": "Mostrare cambiamenti de<PERSON>ti", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Incolla URL immagine:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Campo obbligatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Il formato URL richiesto è \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Elenco puntato", "Common.Views.ListSettingsDialog.textFromStorage": "Da spazio di archiviazione", "Common.Views.ListSettingsDialog.textFromUrl": "Da URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerato", "Common.Views.ListSettingsDialog.tipChange": "Modifica elenco puntato", "Common.Views.ListSettingsDialog.txtBullet": "Elenco puntato", "Common.Views.ListSettingsDialog.txtColor": "Colore", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "Importa", "Common.Views.ListSettingsDialog.txtNewBullet": "Nuovo elenco puntato", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% del testo", "Common.Views.ListSettingsDialog.txtSize": "Dimensione", "Common.Views.ListSettingsDialog.txtStart": "Inizia da", "Common.Views.ListSettingsDialog.txtSymbol": "Simbolo", "Common.Views.ListSettingsDialog.txtTitle": "Impostazioni elenco", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Intervallo di celle non valido", "Common.Views.OpenDialog.textSelectData": "Selezionare i dati", "Common.Views.OpenDialog.txtAdvanced": "Avanzate", "Common.Views.OpenDialog.txtColon": "<PERSON> punti", "Common.Views.OpenDialog.txtComma": "Virgola", "Common.Views.OpenDialog.txtDelimiter": "Delimitatore", "Common.Views.OpenDialog.txtDestData": "Scegli dove inserire i dati", "Common.Views.OpenDialog.txtEmpty": "Questo campo è obbligatorio", "Common.Views.OpenDialog.txtEncoding": "Codifica", "Common.Views.OpenDialog.txtIncorrectPwd": "Password errata", "Common.Views.OpenDialog.txtOpenFile": "Immettere la password per aprire il file", "Common.Views.OpenDialog.txtOther": "Altro", "Common.Views.OpenDialog.txtPassword": "Password", "Common.Views.OpenDialog.txtPreview": "Anteprima", "Common.Views.OpenDialog.txtProtected": "Una volta inserita la password e aperto il file, verrà ripristinata la password corrente sul file.", "Common.Views.OpenDialog.txtSemicolon": "Punto e virgola", "Common.Views.OpenDialog.txtSpace": "Spazio", "Common.Views.OpenDialog.txtTab": "Scheda", "Common.Views.OpenDialog.txtTitle": "Seleziona %1 opzioni", "Common.Views.OpenDialog.txtTitleProtected": "File protetto", "Common.Views.PasswordDialog.txtDescription": "Impostare una password per proteggere questo documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "La password di conferma non corrisponde", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Ripeti password", "Common.Views.PasswordDialog.txtTitle": "Imposta password", "Common.Views.PasswordDialog.txtWarning": "Importante: una volta persa o dimenticata, la password non potrà più essere recuperata. Conservalo in un luogo sicuro.", "Common.Views.PluginDlg.textLoading": "Caricamento", "Common.Views.Plugins.groupCaption": "Plugin", "Common.Views.Plugins.strPlugins": "Plugin", "Common.Views.Plugins.textClosePanel": "Chiudere plugin", "Common.Views.Plugins.textLoading": "Caricamento", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "Termina", "Common.Views.Protection.hintAddPwd": "Crittografa con password", "Common.Views.Protection.hintDelPwd": "Elimina password", "Common.Views.Protection.hintPwd": "Modifica o rimuovi password", "Common.Views.Protection.hintSignature": "Aggiungi firma digitale o riga di firma", "Common.Views.Protection.txtAddPwd": "Aggiungi password", "Common.Views.Protection.txtChangePwd": "Modifica password", "Common.Views.Protection.txtDeletePwd": "Elimina password", "Common.Views.Protection.txtEncrypt": "Crittografare", "Common.Views.Protection.txtInvisibleSignature": "Aggiungi firma digitale", "Common.Views.Protection.txtSignature": "Firma", "Common.Views.Protection.txtSignatureLine": "Aggiungi riga di firma", "Common.Views.RenameDialog.textName": "Nome file", "Common.Views.RenameDialog.txtInvalidName": "Il nome del file non può contenere nessuno dei seguenti caratteri:", "Common.Views.ReviewChanges.hintNext": "Alla modifica successiva", "Common.Views.ReviewChanges.hintPrev": "Alla modifica precedente", "Common.Views.ReviewChanges.strFast": "Rapido", "Common.Views.ReviewChanges.strFastDesc": "Co-editing in teampo reale. <PERSON>tte le modifiche vengono salvate automaticamente.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "Usa il pulsante 'Salva' per sincronizzare le tue modifiche con quelle effettuate da altri.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accetta la modifica corrente", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON>mposta modalità co-editing", "Common.Views.ReviewChanges.tipCommentRem": "Rimuovi i commenti", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Rimuovi i commenti correnti", "Common.Views.ReviewChanges.tipCommentResolve": "Risolvere i commenti", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Risolvere i commenti presenti", "Common.Views.ReviewChanges.tipHistory": "Mostra Cronologia versioni", "Common.Views.ReviewChanges.tipRejectCurrent": "Annulla la modifica attuale", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON> camb<PERSON>i", "Common.Views.ReviewChanges.tipReviewView": "Selezionare il modo in cui si desidera visualizzare le modifiche", "Common.Views.ReviewChanges.tipSetDocLang": "Imposta la lingua del documento", "Common.Views.ReviewChanges.tipSetSpelling": "Controllo ortografico", "Common.Views.ReviewChanges.tipSharing": "Gestisci i diritti di accesso al documento", "Common.Views.ReviewChanges.txtAccept": "Accetta", "Common.Views.ReviewChanges.txtAcceptAll": "Accetta tutte le modifiche", "Common.Views.ReviewChanges.txtAcceptChanges": "Accetta modifiche", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accetta la modifica corrente", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modalità di co-editing", "Common.Views.ReviewChanges.txtCommentRemAll": "Rimuovi tutti i commenti", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Rimuovi i commenti correnti", "Common.Views.ReviewChanges.txtCommentRemMy": "Rimuovi i miei commenti", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Rimuovi i miei commenti attuali", "Common.Views.ReviewChanges.txtCommentRemove": "Elimina", "Common.Views.ReviewChanges.txtCommentResolve": "Risolvere", "Common.Views.ReviewChanges.txtCommentResolveAll": "‎Risolvere tutti i commenti‎", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Risolvere i commenti presenti", "Common.Views.ReviewChanges.txtCommentResolveMy": "Risolvere i miei commenti", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "‎Risolvere i miei commenti presenti", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> le modifiche accettate (anteprima)", "Common.Views.ReviewChanges.txtFinalCap": "Finale", "Common.Views.ReviewChanges.txtHistory": "Cronologia delle versioni", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> le modifiche (Modifica)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcatura", "Common.Views.ReviewChanges.txtNext": "Successivo", "Common.Views.ReviewChanges.txtOriginal": "<PERSON>tti le modifiche rifiutate (Anteprima)", "Common.Views.ReviewChanges.txtOriginalCap": "Originale", "Common.Views.ReviewChanges.txtPrev": "Precedente", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON> tutte le modifiche", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON> modific<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Annulla la modifica attuale", "Common.Views.ReviewChanges.txtSharing": "Condivisione", "Common.Views.ReviewChanges.txtSpelling": "Controllo ortografico", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON> camb<PERSON>i", "Common.Views.ReviewChanges.txtView": "Modalità visualizzazione", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "Aggiungi risposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+mention fornirà l'accesso al documento e invierà un'e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+mention avviserà l'utente via e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Apri di nuovo", "Common.Views.ReviewPopover.textReply": "Rispondi", "Common.Views.ReviewPopover.textResolve": "Risolvere", "Common.Views.ReviewPopover.textViewResolved": "Non sei autorizzato a riaprire il commento", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminare", "Common.Views.ReviewPopover.txtEditTip": "Modificare", "Common.Views.SaveAsDlg.textLoading": "Caricamento", "Common.Views.SaveAsDlg.textTitle": "Cartella di salvataggio", "Common.Views.SearchPanel.textByColumns": "Per colonne", "Common.Views.SearchPanel.textByRows": "Per righe", "Common.Views.SearchPanel.textCaseSensitive": "Sensibile alle maiuscole", "Common.Views.SearchPanel.textCell": "Cella", "Common.Views.SearchPanel.textCloseSearch": "Chiudere la ricerca", "Common.Views.SearchPanel.textFind": "Ricerca", "Common.Views.SearchPanel.textFindAndReplace": "Trovare e sostituire", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formule", "Common.Views.SearchPanel.textItemEntireCell": "Intero contenuto della cella", "Common.Views.SearchPanel.textLookIn": "Cercare in", "Common.Views.SearchPanel.textMatchUsingRegExp": "Corrispondenza usando espressioni regolari", "Common.Views.SearchPanel.textName": "Nome", "Common.Views.SearchPanel.textNoMatches": "<PERSON>ess<PERSON> corrispondenza", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON>essun risultato di ricerca", "Common.Views.SearchPanel.textReplace": "Sost<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Sost<PERSON><PERSON><PERSON> tutto", "Common.Views.SearchPanel.textReplaceWith": "Sostituire con", "Common.Views.SearchPanel.textSearch": "Ricerca", "Common.Views.SearchPanel.textSearchAgain": "{0}Esegui una nuova ricerca{1} per ottenere risultati accurati.", "Common.Views.SearchPanel.textSearchHasStopped": "La ricerca è stata interrotta", "Common.Views.SearchPanel.textSearchOptions": "Opzioni di ricerca", "Common.Views.SearchPanel.textSearchResults": "Risultati della ricerca: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "Selezionare intervallo di dati", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Intervallo specifico", "Common.Views.SearchPanel.textTooManyResults": "Ci sono troppi risultati per essere mostrati qui", "Common.Views.SearchPanel.textValue": "Valore", "Common.Views.SearchPanel.textValues": "Valori", "Common.Views.SearchPanel.textWholeWords": "Solo parole intere", "Common.Views.SearchPanel.textWithin": "Entro", "Common.Views.SearchPanel.textWorkbook": "Cartella di lavoro", "Common.Views.SearchPanel.tipNextResult": "Risultato successivo", "Common.Views.SearchPanel.tipPreviousResult": "Risultato precedente", "Common.Views.SelectFileDlg.textLoading": "Caricamento", "Common.Views.SelectFileDlg.textTitle": "Seleziona sorgente Dati", "Common.Views.SignDialog.textBold": "Grassetto", "Common.Views.SignDialog.textCertificate": "Certificato", "Common.Views.SignDialog.textChange": "Cambia", "Common.Views.SignDialog.textInputName": "Inserisci nome firmatario", "Common.Views.SignDialog.textItalic": "Corsivo", "Common.Views.SignDialog.textNameError": "Il nome firmatario non può essere vuoto.", "Common.Views.SignDialog.textPurpose": "Motivo della firma del documento", "Common.Views.SignDialog.textSelect": "Seleziona", "Common.Views.SignDialog.textSelectImage": "Seleziona Immagine", "Common.Views.SignDialog.textSignature": "La firma appare come", "Common.Views.SignDialog.textTitle": "Firma Documento", "Common.Views.SignDialog.textUseImage": "oppure clicca 'Scegli immagine' per utilizzare un'immagine come firma", "Common.Views.SignDialog.textValid": "Valido dal %1 al %2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON> carattere", "Common.Views.SignDialog.tipFontSize": "Dimensione carattere", "Common.Views.SignSettingsDialog.textAllowComment": "Consenti al firmatario di aggiungere commenti nella finestra di dialogo della firma", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON> del Firmatario", "Common.Views.SignSettingsDialog.textInstructions": "Istruzioni per i firmatari", "Common.Views.SignSettingsDialog.textShowDate": "Mostra la data nella riga di Firma", "Common.Views.SignSettingsDialog.textTitle": "Impostazioni firma", "Common.Views.SignSettingsDialog.txtEmpty": "Campo obbligatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "valore Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Segno di copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON> virgolette alte doppie", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON>i virgolette alte doppie", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON>si oriz<PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Lineetta emme", "Common.Views.SymbolTableDialog.textEmSpace": "Spazio emme", "Common.Views.SymbolTableDialog.textEnDash": "Lineetta enne", "Common.Views.SymbolTableDialog.textEnSpace": "Spazio enne", "Common.Views.SymbolTableDialog.textFont": "Tipo di carattere", "Common.Views.SymbolTableDialog.textNBHyphen": "Trattino senza interruzioni", "Common.Views.SymbolTableDialog.textNBSpace": "Spazio senza interruzioni", "Common.Views.SymbolTableDialog.textPilcrow": "Piede di mosca", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 spazio emme", "Common.Views.SymbolTableDialog.textRange": "Intervallo", "Common.Views.SymbolTableDialog.textRecent": "Simboli usati di recente", "Common.Views.SymbolTableDialog.textRegistered": "Firma registarta", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON> virgolette alte singole", "Common.Views.SymbolTableDialog.textSection": "Sezione firma", "Common.Views.SymbolTableDialog.textShortcut": "Tasto di scelta rapida", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON> morbido", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON>i virgolette alte singole", "Common.Views.SymbolTableDialog.textSpecial": "Caratteri speciali", "Common.Views.SymbolTableDialog.textSymbols": "Simboli", "Common.Views.SymbolTableDialog.textTitle": "Simbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Simbolo del marchio", "Common.Views.UserNameDialog.textDontShow": "‎Non chiedere di nuovo‎", "Common.Views.UserNameDialog.textLabel": "Etichetta:", "Common.Views.UserNameDialog.textLabelError": "L'etichetta non deve essere vuota.", "SSE.Controllers.DataTab.textColumns": "Colonne", "SSE.Controllers.DataTab.textEmptyUrl": "Devi specificare l'URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Testo a colonne", "SSE.Controllers.DataTab.txtDataValidation": "Validazione dati", "SSE.Controllers.DataTab.txtExpand": "Espandi", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "I dati accanto alla selezione non verranno rimossi. Vuoi espandere la selezione per includere i dati adiacenti o continuare solo con le celle attualmente selezionate?", "SSE.Controllers.DataTab.txtExtendDataValidation": "La selezione contiene alcune celle senza impostazioni di convalida dei dati.<br>Desideri estendere la convalida dei dati a queste celle?", "SSE.Controllers.DataTab.txtImportWizard": "Procedura guidata di importazione del testo", "SSE.Controllers.DataTab.txtRemDuplicates": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtRemoveDataValidation": "La selezione contiene più di un tipo di convalida.<br>Cancellare le impostazioni correnti e continuare?", "SSE.Controllers.DataTab.txtRemSelected": "Rimuovi nella selezione", "SSE.Controllers.DataTab.txtUrlTitle": "Incolla un URL dati", "SSE.Controllers.DocumentHolder.alignmentText": "Allineamento", "SSE.Controllers.DocumentHolder.centerText": "Centrato", "SSE.Controllers.DocumentHolder.deleteColumnText": "Elimina colonna", "SSE.Controllers.DocumentHolder.deleteRowText": "Elimina riga", "SSE.Controllers.DocumentHolder.deleteText": "Elimina", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Il collegamento a cui si fa riferimento non esiste. Correggere il collegamento o rimuoverlo.", "SSE.Controllers.DocumentHolder.guestText": "Ospite", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Colonna a sinistra", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Colonna a destra", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Riga sopra", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Riga sotto", "SSE.Controllers.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.leftText": "A sinistra", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Avviso", "SSE.Controllers.DocumentHolder.rightText": "A destra", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opzioni di correzione automatica", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Larghezza colonne {0} simboli ({1} pixel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Altezza righe {0} punti ({1} pixel)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Fare clic sul collegamento per aprirlo o fare clic e tenere premuto il pulsante del mouse per selezionare la cella.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Inserisci a sinistra", "SSE.Controllers.DocumentHolder.textInsertTop": "<PERSON><PERSON><PERSON><PERSON> in alto", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Incolla speciale", "SSE.Controllers.DocumentHolder.textStopExpand": "Interrompi l'espansione automatica delle tabelle", "SSE.Controllers.DocumentHolder.textSym": "sim", "SSE.Controllers.DocumentHolder.tipIsLocked": "Questo elemento viene modificato da un altro utente.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Sopra la media", "SSE.Controllers.DocumentHolder.txtAddBottom": "Aggiungi bordo inferiore", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Aggiungi barra di frazione", "SSE.Controllers.DocumentHolder.txtAddHor": "Aggiungi linea orizzontale", "SSE.Controllers.DocumentHolder.txtAddLB": "Aggiungi linea inferiore sinistra", "SSE.Controllers.DocumentHolder.txtAddLeft": "Aggiungi bordo sinistro", "SSE.Controllers.DocumentHolder.txtAddLT": "Aggiungi linea superiore sinistra", "SSE.Controllers.DocumentHolder.txtAddRight": "Agg<PERSON>ngi bordo destro", "SSE.Controllers.DocumentHolder.txtAddTop": "Aggiungi bordo superiore", "SSE.Controllers.DocumentHolder.txtAddVer": "Aggiungi linea verticale", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Allinea al carattere", "SSE.Controllers.DocumentHolder.txtAll": "(<PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Restituisce l'intero contenuto della tabella o delle colonne della tabella specificate, comprese le intestazioni di colonna, i dati e le righe totali", "SSE.Controllers.DocumentHolder.txtAnd": "e", "SSE.Controllers.DocumentHolder.txtBegins": "Inizia con", "SSE.Controllers.DocumentHolder.txtBelowAve": "sotto la media", "SSE.Controllers.DocumentHolder.txtBlanks": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Proprietà bordo", "SSE.Controllers.DocumentHolder.txtBottom": "In basso", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON>onna", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Allineamento colonna", "SSE.Controllers.DocumentHolder.txtContains": "contiene", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copiato negli appunti", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Restituisce le celle di dati della tabella o le colonne della tabella specificate", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Diminuisci dimensione argomento", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Elimina argomento", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Elimina interruzione manuale", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Elimina i caratteri racchiusi", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Elimina caratteri e separatori inclusi", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Elimina equazione", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON>mina char", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Elimina radicale", "SSE.Controllers.DocumentHolder.txtEnds": "finisce con", "SSE.Controllers.DocumentHolder.txtEquals": "uguali", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Uguale al colore della cella", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Uguale al colore del carattere", "SSE.Controllers.DocumentHolder.txtExpand": "Espandi e ordina", "SSE.Controllers.DocumentHolder.txtExpandSort": "I dati vicini alla selezione non saranno ordinati. Vuoi estendere la selezione in modo da includere i dati adiacenti o lasciare la selezione invariata?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "In basso", "SSE.Controllers.DocumentHolder.txtFilterTop": "In alto", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Modifica a frazione lineare", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Modifica a frazione obliqua", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Modifica a frazione impilata", "SSE.Controllers.DocumentHolder.txtGreater": "Più grande di", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Maggiore o uguale a", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Char sul testo", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char sotto il testo", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Restituisce le intestazioni di colonna per la tabella o le colonne di tabella specificate", "SSE.Controllers.DocumentHolder.txtHeight": "Altezza", "SSE.Controllers.DocumentHolder.txtHideBottom": "Nascondi il bordo inferiore", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Nascondi il limite inferiore", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Nascondi parentesi chiusa", "SSE.Controllers.DocumentHolder.txtHideDegree": "Nascondi grado", "SSE.Controllers.DocumentHolder.txtHideHor": "Nascondi linea orizzontale", "SSE.Controllers.DocumentHolder.txtHideLB": "Nascondi linea inferiore sinistra", "SSE.Controllers.DocumentHolder.txtHideLeft": "Nascondi bordo sinistro", "SSE.Controllers.DocumentHolder.txtHideLT": "Nascondi linea superiore sinistra", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Nascondi parentesi aperta", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Nascondi segnaposto", "SSE.Controllers.DocumentHolder.txtHideRight": "Nascondi bordo destro", "SSE.Controllers.DocumentHolder.txtHideTop": "Nascondi bordo superiore", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Nascondi limite superiore", "SSE.Controllers.DocumentHolder.txtHideVer": "Nascondi linea verticale", "SSE.Controllers.DocumentHolder.txtImportWizard": "Procedura guidata di importazione del testo", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Aumenta dimensione argomento", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Inserisci argomento dopo", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Inserisci argomento prima", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Inserisci interruzione manuale", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Inserisci equazione dopo", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Inserisci equazione prima", "SSE.Controllers.DocumentHolder.txtItems": "elementi", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON> solo il testo", "SSE.Controllers.DocumentHolder.txtLess": "Meno di", "SSE.Controllers.DocumentHolder.txtLessEquals": "Minore o uguale a ", "SSE.Controllers.DocumentHolder.txtLimitChange": "Modifica posizione dei limiti", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limite sul testo", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limite sotto il testo", "SSE.Controllers.DocumentHolder.txtLockSort": "I dati si trovano accanto alla tua selezione, ma non sei autorizzato per modificare le celle.<br>Vuoi continuare con la selezione corrente?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Adatta le parentesi all'altezza dell'argomento", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Allineamento matrice", "SSE.Controllers.DocumentHolder.txtNoChoices": "Non ci sono alternative per riempire la cella.<br>Solo i valori testo dalla colonna possono essere selezionati per la sostituzione", "SSE.Controllers.DocumentHolder.txtNotBegins": "non inizia con", "SSE.Controllers.DocumentHolder.txtNotContains": "non contiene", "SSE.Controllers.DocumentHolder.txtNotEnds": "non finisce con", "SSE.Controllers.DocumentHolder.txtNotEquals": "non è uguale", "SSE.Controllers.DocumentHolder.txtOr": "o", "SSE.Controllers.DocumentHolder.txtOverbar": "Barra sopra al testo", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula senza bordi", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + larghezza colonna", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formattazione destinazione", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Incolla solo formattazione", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + formato numero", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "<PERSON>olla solo la formula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + tutta la formattazione", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON>olla collegamento", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Immagine collegata", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Unisci formattazione condizionale", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formattazione di origine", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Valore + tutta la formattazione", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Valore + formato numero", "SSE.Controllers.DocumentHolder.txtPasteValues": "Incolla solo il valore", "SSE.Controllers.DocumentHolder.txtPercent": "Percento", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Ripristina l'espansione automatica della tabella", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Rimuovi la barra di frazione", "SSE.Controllers.DocumentHolder.txtRemLimit": "Rimuovi limite", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "R<PERSON>uovi accento carattere", "SSE.Controllers.DocumentHolder.txtRemoveBar": "<PERSON><PERSON> barra", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Vuoi rimuovere questa firma?<br>Non può essere annullata.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Rimuovi gli script", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Elimina pedice", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Elimina apice", "SSE.Controllers.DocumentHolder.txtRowHeight": "Altezza riga", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Script dopo il testo", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Script prima del testo", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Mostra limite inferiore", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Mostra parentesi quadra di chiusura", "SSE.Controllers.DocumentHolder.txtShowDegree": "Mostra grado", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Mostra parentesi quadra aperta", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Mostra segna<PERSON>o", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Mostra limite superiore", "SSE.Controllers.DocumentHolder.txtSorting": "Ordinamento", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ordina selezionati", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Seleziona solo questa riga della colonna specificata", "SSE.Controllers.DocumentHolder.txtTop": "In alto", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Restituisce le righe totali per la tabella o le colonne della tabella specificate", "SSE.Controllers.DocumentHolder.txtUnderbar": "<PERSON>a sotto al testo", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Annulla l'espansione automatica della tabella", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Usa la procedura guidata di importazione del testo", "SSE.Controllers.DocumentHolder.txtWarnUrl": "C<PERSON>care questo link può essere dannoso per il tuo dispositivo e i dati.<br>Sei sicuro di voler continuare?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Database", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Data e ora", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Ingegneria", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finanziario", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informazioni", "SSE.Controllers.FormulaDialog.sCategoryLast10": "Gli ultimi 10 utilizzati", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logico", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Ricerca e riferimento", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematica e trigonometria", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistico", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Testo e dati", "SSE.Controllers.LeftMenu.newDocumentTitle": "Foglio di calcolo senza nome", "SSE.Controllers.LeftMenu.textByColumns": "Per colonne", "SSE.Controllers.LeftMenu.textByRows": "Per righe", "SSE.Controllers.LeftMenu.textFormulas": "Formule", "SSE.Controllers.LeftMenu.textItemEntireCell": "Intero contenuto della cella", "SSE.Controllers.LeftMenu.textLoadHistory": "Caricamento di cronologia delle versioni...", "SSE.Controllers.LeftMenu.textLookin": "Cerca in", "SSE.Controllers.LeftMenu.textNoTextFound": "I dati da cercare non sono stati trovati. Modifica i parametri di ricerca.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "La sostituzione è stata effettuata. {0} casi sono stati saltati.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "La ricerca è stata effettuata. Casi sostituiti: {0}", "SSE.Controllers.LeftMenu.textSearch": "Cerca", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Valori", "SSE.Controllers.LeftMenu.textWarning": "Avviso", "SSE.Controllers.LeftMenu.textWithin": "Entro", "SSE.Controllers.LeftMenu.textWorkbook": "Cartella di lavoro", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON>za titolo", "SSE.Controllers.LeftMenu.warnDownloadAs": "Se continui a salvare in questo formato tutte le funzioni eccetto il testo andranno perse.<br>Sei sicuro di voler continuare?", "SSE.Controllers.Main.confirmMoveCellRange": "La cella di destinazione può contenere i dati. Continuare l'operazione?", "SSE.Controllers.Main.confirmPutMergeRange": "I dati sorgente contenevano delle celle raggruppate<br>Sono state incollate alla tabella singolarmente.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Le formule nella riga dell'intestazione verranno rimosse e convertite in testo statico. <br>Vuoi continuare?", "SSE.Controllers.Main.convertationTimeoutText": "È stato superato il tempo limite della conversione.", "SSE.Controllers.Main.criticalErrorExtText": "Clicca su \"OK\" per ritornare all'elenco dei documenti", "SSE.Controllers.Main.criticalErrorTitle": "Errore", "SSE.Controllers.Main.downloadErrorText": "Scaricamento fallito.", "SSE.Controllers.Main.downloadTextText": "Scaricamento del foglio di calcolo in corso...", "SSE.Controllers.Main.downloadTitleText": "Scaricamento del foglio di calcolo", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON>un valore duplicato trovato.", "SSE.Controllers.Main.errorAccessDeny": "Stai tentando di eseguire un'azione per la quale non disponi di permessi sufficienti.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "SSE.Controllers.Main.errorArgsRange": "Un errore nella formula inserita.<br>È stato utilizzato un intervallo di argomenti non corretto.", "SSE.Controllers.Main.errorAutoFilterChange": "Operazione non consentita. Si sta tentando di spostare le celle nella tabella del tuo foglio di lavoro.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Impossibile effettuare questa operazione per le celle selezionate perché è impossibile spostare una parte della tabella.<br> Seleziona un altro intervallo dati per spostare tutta la tabella e riprova.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Impossibile eseguire l'operazione sull'intervallo celle selezionato.<br>Selezionare un intervallo di dati uniforme diverso da quello esistente e riprovare.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "L'operazione non può essere eseguita perché l'area contiene celle filtrate.<br><PERSON><PERSON><PERSON> gli elementi filtrati e riprova.", "SSE.Controllers.Main.errorBadImageUrl": "URL dell'immagine non corretto", "SSE.Controllers.Main.errorCannotUngroup": "Impossibile separare. Per iniziare una struttura, seleziona le righe o le colonne interessate e raggruppale.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Non è possibile utilizzare questo comando su un foglio protetto. Per utilizzare questo comando, sproteggi il foglio.<br>Po<PERSON>bbe essere richiesto di inserire una password.", "SSE.Controllers.Main.errorChangeArray": "Non è possibile modificare parte di una matrice", "SSE.Controllers.Main.errorChangeFilteredRange": "Questo cambierà un intervallo filtrato nel tuo foglio di lavoro.<br>Per completare questa attività, rimuovi i filtri automatici.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "La cella o il grafico che stai cercando di cambiare si trova sul foglio protetto.<br>Per apportare una modifica, togli la protezione del foglio. Potrebbe esserti richiesto di inserire una password.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Connessione al server persa. Il documento non può essere modificato in questo momento.", "SSE.Controllers.Main.errorConnectToServer": "Il documento non può essere salvato. Controllare le impostazioni di rete o contattare l'Amministratore.<br>Quando fai clic sul pulsante 'OK', ti verrà richiesto di scaricare il documento.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Questo comando non può essere applicato a selezioni multiple.<br>Seleziona un intervallo singolo e riprova.", "SSE.Controllers.Main.errorCountArg": "Un errore nella formula inserita.<br>E' stato utilizzato un numero di argomento scorretto.", "SSE.Controllers.Main.errorCountArgExceed": "Un errore nella formula inserita.<br>E' stato superato il numero di argomenti.", "SSE.Controllers.Main.errorCreateDefName": "Gli intervalli denominati esistenti non possono essere modificati e quelli nuovi non possono essere creati<br>al momento alcuni di essi sono in fase di modifica.", "SSE.Controllers.Main.errorDatabaseConnection": "Errore esterno.<br>Errore di connessione a banca dati. Si prega di contattare l'assistenza tecnica nel caso in cui l'errore persiste.", "SSE.Controllers.Main.errorDataEncrypted": "Le modifiche crittografate sono state ricevute, non possono essere decifrate.", "SSE.Controllers.Main.errorDataRange": "Intervallo di dati non corretto.", "SSE.Controllers.Main.errorDataValidate": "Il valore inserito non è valido. <br>Un utente ha valori limitati che possono essere inseriti in questa cella.", "SSE.Controllers.Main.errorDefaultMessage": "Codice errore: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Stai tentando di eliminare una colonna che contiene una cella bloccata. Le celle bloccate non possono essere eliminate se il foglio di calcolo è protetto.<br>Per eliminare una cella bloccata, devi rimuovere la protezione del foglio. Potrebbe esserti richiesto di inserire una password.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Stai tentando di eliminare una riga che contiene una cella bloccata. Le celle bloccate non possono essere eliminate se il foglio di calcolo è protetto.<br>Per eliminare una cella bloccata, devi rimuovere la protezione del foglio. Potrebbe esserti richiesto di inserire una password.", "SSE.Controllers.Main.errorDirectUrl": "Si prega di verificare il link al documento. <br>Quest<PERSON> collegamento deve essere un collegamento diretto al file da scaricare.", "SSE.Controllers.Main.errorEditingDownloadas": "Si è verificato un errore durante il lavoro con il documento.<br>Utilizza l'opzione 'Scaricare come' per salvare la copia di backup del file sul disco rigido del computer.", "SSE.Controllers.Main.errorEditingSaveas": "Si è verificato un errore durante il lavoro con il documento.<br>Utilizza l'opzione 'Salvare come ...' per salvare la copia di backup del file sul disco rigido del computer.", "SSE.Controllers.Main.errorEditView": "La vista del foglio esistente non possono essere modificati, e quelli nuovi non possono essere creati al momento poiché alcuni di essi sono in fase di modifica.", "SSE.Controllers.Main.errorEmailClient": "Non è stato trovato nessun client di posta elettronica.", "SSE.Controllers.Main.errorFilePassProtect": "Il file è protetto da password e non può essere aperto.", "SSE.Controllers.Main.errorFileRequest": "Errore esterno.<br>Errore di richiesta di file. Si prega di contattare l'assistenza nel caso in cui l'errore persista.", "SSE.Controllers.Main.errorFileSizeExceed": "La dimensione del file supera la limitazione impostata per il tuo server.<br>Per i dettagli, contatta l'amministratore del Document Server.", "SSE.Controllers.Main.errorFileVKey": "Errore esterno.<br><PERSON><PERSON> di sicurezza scorretta. Si prega di contattare l'assistenza nel caso in cui l'errore persista.", "SSE.Controllers.Main.errorFillRange": "Impossibile riempire l'intervallo di celle selezionato.<br><PERSON><PERSON> le celle unite devono avere le stesse dimensioni.", "SSE.Controllers.Main.errorForceSave": "Si è verificato un errore durante il salvataggio del file. Utilizza l'opzione 'Scaricare come' per salvare il file sul disco rigido del computer o riprova più tardi.", "SSE.Controllers.Main.errorFormulaName": "Un errore nella formula inserita.<br>È stato usato un nome errato per la formula.", "SSE.Controllers.Main.errorFormulaParsing": "Si è verificato un errore durante l'analisi della formula.", "SSE.Controllers.Main.errorFrmlMaxLength": "La lunghezza della formula supera il limite di 8192 caratteri.<br>Modificala e riprova.", "SSE.Controllers.Main.errorFrmlMaxReference": "Non puoi inserire dati questa formula perché contiene troppi valori,<br>riferimenti di cella e/o nomi.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "I valori di testo nelle formule sono limitati a 255 caratteri.<br>Utilizzare la funzione CONCATENATA o l'operatore di concatenazione(&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "La funzione si riferisce a un foglio inesistente.<br>Verifica i dati e riprova.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Impossibile completare l'operazione per l'intervallo di celle selezionato.<br> Seleziona un intervallo in modo che la prima riga della tabella si trovi sulla stessa riga <br> e la tabella risultante si sovrapponga a quella corrente.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Impossibile completare l'operazione per l'intervallo di celle selezionato.<br>Selezionare un intervallo che non include altre tabelle.", "SSE.Controllers.Main.errorInvalidRef": "Immettere un nome corretto per la selezione o un riferimento valido a cui accedere.", "SSE.Controllers.Main.errorKeyEncrypt": "Descrittore di chiave scon<PERSON>uto", "SSE.Controllers.Main.errorKeyExpire": "Descrittore di chiave scaduto", "SSE.Controllers.Main.errorLabledColumnsPivot": "Per creare una tabella pivot, è necessario utilizzare dati organizzati come un elenco con colonne etichettate.", "SSE.Controllers.Main.errorLoadingFont": "I caratteri non sono caricati.<br>Si prega di contattare il tuo amministratore di Document Server.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Il riferimento per la posizione o l'intervallo di dati non è valido.", "SSE.Controllers.Main.errorLockedAll": "L'operazione non può essere portata a termine fino a che il foglio è bloccato da un altro utente.", "SSE.Controllers.Main.errorLockedCellPivot": "Non è possibile modificare i dati all'interno di una tabella pivot.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Il foglio non può essere rinominato al momento in quanto viene rinominato da un altro utente.", "SSE.Controllers.Main.errorMaxPoints": "Il numero massimo di punti in serie per grafico è di 4096.", "SSE.Controllers.Main.errorMoveRange": "Impossibile modificare parte di una cella unita", "SSE.Controllers.Main.errorMoveSlicerError": "I filtri dei dati della tabella non possono essere copiati da una cartella di lavoro a un'altra. <br>R<PERSON>rova selezionando l'intera tabella e i filtri dei dati.", "SSE.Controllers.Main.errorMultiCellFormula": "Le formule di matrice multi-cella non sono consentite nelle tabelle.", "SSE.Controllers.Main.errorNoDataToParse": "<PERSON><PERSON><PERSON> dato selezionato per l'analisi.", "SSE.Controllers.Main.errorOpenWarning": "Una delle formule del file supera il limite di 8192 caratteri.<br>La formula è stata rimossa.", "SSE.Controllers.Main.errorOperandExpected": "La sintassi per la funzione inserita non è corretta. Controlla se hai dimenticato una delle parentesi - '(' oppure ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "La password che hai fornito non è corretta.<br>Verifica che il tasto CAPS LOCK sia disattivato e assicurati di utilizzare maiuscole corrette.", "SSE.Controllers.Main.errorPasteMaxRange": "l'area di copia-incolla non coincide.<br>Selezionare un'area con le stesse dimensioni o fare click sulla prima cella in una riga per incollare le celle copiate.", "SSE.Controllers.Main.errorPasteMultiSelect": "Questa azione non può essere eseguita su una selezione di più intervalli.<br>Seleziona un singolo intervallo e riprova.", "SSE.Controllers.Main.errorPasteSlicerError": "I filtri dei dati della tabella non possono essere copiati da una cartella di lavoro a un'altra.", "SSE.Controllers.Main.errorPivotGroup": "Impossibile raggruppare quella selezione.", "SSE.Controllers.Main.errorPivotOverlap": "Un report di tabella pivot non può sovrapporsi a una tabella.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Il rapporto della tabella pivot è stato salvato senza i dati sottostanti.<br><PERSON><PERSON><PERSON><PERSON> il pulsante \"Aggiorna\" per aggiornare il relativo rapporto.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Purtroppo non è possibile stampare più di 1500 pagine alla volta con la versione attuale del programma.<br>Questa limitazione sarà rimossa nelle prossime versioni del programma.", "SSE.Controllers.Main.errorProcessSaveResult": "Salvataggio non riuscito", "SSE.Controllers.Main.errorServerVersion": "La versione dell'editor è stata aggiornata. La pagina verrà ricaricata per applicare le modifiche.", "SSE.Controllers.Main.errorSessionAbsolute": "La sessione di modifica del documento è scaduta. Si prega di ricaricare la pagina.", "SSE.Controllers.Main.errorSessionIdle": "È passato troppo tempo dall'ultima modifica apportata al documento. Si prega di ricaricare la pagina.", "SSE.Controllers.Main.errorSessionToken": "La connessione al server è stata interrotta. Si prega di ricaricare la pagina.", "SSE.Controllers.Main.errorSetPassword": "Impossibile impostare la password.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Il riferimento alla posizione non è valido perché le celle non sono tutte nella stessa colonna o riga.<br>Seleziona celle che sono tutte in una singola colonna o riga.", "SSE.Controllers.Main.errorStockChart": "Ordine di righe scorretto. Per creare un grafico in pila posiziona i dati nel foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "SSE.Controllers.Main.errorToken": "Il token di sicurezza del documento non è stato creato correttamente.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "SSE.Controllers.Main.errorTokenExpire": "Il token di sicurezza del documento è scaduto.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "SSE.Controllers.Main.errorUnexpectedGuid": "Errore esterno.<br>GUID inaspettato. Si prega di contattare l'assistenza tecnica nel caso in cui l'errore persiste.", "SSE.Controllers.Main.errorUpdateVersion": "La versione del file è stata modificata. La pagina verrà ricaricata.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "La connessione Internet è stata ripristinata e la versione del file è stata modificata.<br>Prima di poter continuare a lavorare, devi scaricare il file o copiarne il contenuto per assicurarti che nulla vada perso, quindi ricarica questa pagina.", "SSE.Controllers.Main.errorUserDrop": "Impossibile accedere al file in questo momento.", "SSE.Controllers.Main.errorUsersExceed": "È stato superato il numero di utenti consentito dal piano tariffario", "SSE.Controllers.Main.errorViewerDisconnect": "La connessione è stata persa. È ancora possibile visualizzare il documento, <br> ma non sarà possibile scaricarlo o stamparlo fino a quando la connessione non sarà ripristinata e la pagina sarà ricaricata.", "SSE.Controllers.Main.errorWrongBracketsCount": "Un errore nella formula inserita.<br>E' stato utilizzato un numero errato tra parentesi.", "SSE.Controllers.Main.errorWrongOperator": "Un errore nella formula inserita. È stato utilizzato un operatore errato.<br>Correggere per continuare.", "SSE.Controllers.Main.errorWrongPassword": "La password che hai fornito non è corretta.", "SSE.Controllers.Main.errRemDuplicates": "Valori duplicati trovati ed eliminati: {0}, valori univoci rimasti: {1}.", "SSE.Controllers.Main.leavePageText": "Ci sono delle modifiche non salvate in questo foglio di calcolo. Clicca su 'Rimani in questa pagina', poi su 'Salva' per salvarle. Clicca su 'Esci da questa pagina' per scartare tutte le modifiche non salvate.", "SSE.Controllers.Main.leavePageTextOnClose": "Tutte le modifiche non salvate in questo foglio di calcolo andranno perse. Clicca su \"Cancellare\" poi \"Salvare\" per salvarle. Clicca su \"OK\" per eliminare tutte le modifiche non salvate.", "SSE.Controllers.Main.loadFontsTextText": "Caricamento dei dati in corso...", "SSE.Controllers.Main.loadFontsTitleText": "Caricamento dei dati", "SSE.Controllers.Main.loadFontTextText": "Caricamento dei dati in corso...", "SSE.Controllers.Main.loadFontTitleText": "Caricamento dei dati", "SSE.Controllers.Main.loadImagesTextText": "Caricamento delle immagini in corso...", "SSE.Controllers.Main.loadImagesTitleText": "Caricamento delle immagini", "SSE.Controllers.Main.loadImageTextText": "Caricamento dell'immagine in corso...", "SSE.Controllers.Main.loadImageTitleText": "Caricamento dell'immagine", "SSE.Controllers.Main.loadingDocumentTitleText": "Caricamento del foglio di calcolo", "SSE.Controllers.Main.notcriticalErrorTitle": "Avviso", "SSE.Controllers.Main.openErrorText": "Si è verificato un errore durante l'apertura del file", "SSE.Controllers.Main.openTextText": "Apertura del foglio di calcolo in corso...", "SSE.Controllers.Main.openTitleText": "Apertura del foglio di calcolo", "SSE.Controllers.Main.pastInMergeAreaError": "Impossibile modificare parte di una cella unita", "SSE.Controllers.Main.printTextText": "Stampa del foglio di calcolo in corso...", "SSE.Controllers.Main.printTitleText": "Stampa del foglio di calcolo", "SSE.Controllers.Main.reloadButtonText": "Ricarica la pagina", "SSE.Controllers.Main.requestEditFailedMessageText": "Qualcuno sta modificando questo documento in questo momento. Si prega di provare più tardi.", "SSE.Controllers.Main.requestEditFailedTitleText": "Accesso negato", "SSE.Controllers.Main.saveErrorText": "Si è verificato un errore durante il salvataggio del file", "SSE.Controllers.Main.saveErrorTextDesktop": "Questo file non può essere salvato o creato.<br>I possibili motivi sono:<br>1. Il file è di sola lettura.<br>2. Il file è in fase di modifica da parte di altri utenti.<br>3. Il disco è pieno oppure è danneggiato.", "SSE.Controllers.Main.saveTextText": "Salvataggio del foglio di calcolo in corso...", "SSE.Controllers.Main.saveTitleText": "Salvataggio del foglio di calcolo", "SSE.Controllers.Main.scriptLoadError": "La connessione è troppo lenta, alcuni componenti non possono essere caricati. Si prega di ricaricare la pagina.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Applicare a tutte le equazioni", "SSE.Controllers.Main.textBuyNow": "Visita il sito web", "SSE.Controllers.Main.textChangesSaved": "Tutte le modifiche sono state salvate", "SSE.Controllers.Main.textClose": "<PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Fai clic per chiudere il consiglio", "SSE.Controllers.Main.textConfirm": "Conferma", "SSE.Controllers.Main.textContactUs": "Contatta il reparto vendite.", "SSE.Controllers.Main.textContinue": "Continua", "SSE.Controllers.Main.textConvertEquation": "Questa equazione è stata creata in una vecchia versione dell'editor di equazioni che non è più supportata. Per modific<PERSON>, devi convertire l'equazione nel formato ML di Office Math.<br>Convertire ora?", "SSE.Controllers.Main.textCustomLoader": "Si prega di notare che, in base ai termini della licenza, non si ha il diritto di modificare il caricatore.<br>Si prega di contattare il nostro reparto vendite per ottenere un preventivo.", "SSE.Controllers.Main.textDisconnect": "La connessione è stata persa", "SSE.Controllers.Main.textFillOtherRows": "Riempire altre righe", "SSE.Controllers.Main.textFormulaFilledAllRows": "La formula ha riempito {0} righe che  contengono i dati. Il riempimento di altre righe vuote potrebbe richiedere qualche minuto.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "La formula ha riempito le prime {0} righe. Il riempimento di altre righe vuote potrebbe richiedere qualche minuto.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "La formula ha compilato solo le prime {0} righe che contengono i dati per risparmiare la memoria. Ci sono altre {1} righe che contengono dati in questo foglio. Puoi riempirli manualmente.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "La formula ha riempito solo le prime {0} righe per risparmiare la memoria. Altre righe in questo foglio non contengono dati.", "SSE.Controllers.Main.textGuest": "Ospite", "SSE.Controllers.Main.textHasMacros": "Il file contiene macro automatiche. <br> Vuoi eseguire le macro?", "SSE.Controllers.Main.textLearnMore": "Scopri di più", "SSE.Controllers.Main.textLoadingDocument": "Caricamento del foglio di calcolo", "SSE.Controllers.Main.textLongName": "Si prega di immettere un nome che contenga meno di 128 caratteri.", "SSE.Controllers.Main.textNeedSynchronize": "Ci sono aggiornamenti disponibili", "SSE.Controllers.Main.textNo": "No", "SSE.Controllers.Main.textNoLicenseTitle": "Limite di licenza raggiunto", "SSE.Controllers.Main.textPaidFeature": "Funzionalità a pagamento", "SSE.Controllers.Main.textPleaseWait": "L'operazione può richiedere più tempo. Per favore, attendi...", "SSE.Controllers.Main.textReconnect": "Connessione ripristinata", "SSE.Controllers.Main.textRemember": "Ricorda la mia scelta per tutti i file", "SSE.Controllers.Main.textRememberMacros": "Ricordare la mia scelta per tutte le macro", "SSE.Controllers.Main.textRenameError": "Il nome utente non può essere vuoto.", "SSE.Controllers.Main.textRenameLabel": "Immettere un nome da utilizzare per la collaborazione", "SSE.Controllers.Main.textRequestMacros": "Una macro effettua una richiesta all'URL. Vuoi consentire la richiesta al %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "Modalità Rigorosa", "SSE.Controllers.Main.textText": "<PERSON><PERSON>", "SSE.Controllers.Main.textTryUndoRedo": "Le funzioni Annulla/Ripristina sono disabilitate per la Modalità di Co-editing Veloce.<br><PERSON><PERSON><PERSON> il pulsante 'Modalità Rigorosa' per passare alla Modalità di Co-editing Rigorosa per poter modificare il file senza l'interferenza di altri utenti e inviare le modifiche solamente dopo averle salvate. Puoi passare da una modalità all'altra di co-editing utilizzando le Impostazioni avanzate dell'editor.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Le funzioni Annulla/Ripeti sono disattivate nella modalità rapida di co-editing", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textYes": "Sì", "SSE.Controllers.Main.titleLicenseExp": "La licenza è scaduta", "SSE.Controllers.Main.titleServerVersion": "L'editor è stato aggiornato", "SSE.Controllers.Main.txtAccent": "Accento", "SSE.Controllers.Main.txtAll": "(<PERSON><PERSON>)", "SSE.Controllers.Main.txtArt": "Il tuo testo qui", "SSE.Controllers.Main.txtBasicShapes": "Forme di base", "SSE.Controllers.Main.txtBlank": "(vuoto)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 di %2", "SSE.Controllers.Main.txtCallouts": "Callout", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Elimina filtro", "SSE.Controllers.Main.txtColLbls": "<PERSON><PERSON><PERSON><PERSON> colonna", "SSE.Controllers.Main.txtColumn": "<PERSON>onna", "SSE.Controllers.Main.txtConfidential": "Riservato", "SSE.Controllers.Main.txtDate": "Data", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Titolo del grafico", "SSE.Controllers.Main.txtEditingMode": "Imposta la modalità di modifica...", "SSE.Controllers.Main.txtErrorLoadHistory": "Caricamento della cronologia non riuscito", "SSE.Controllers.Main.txtFiguredArrows": "Frecce figurate", "SSE.Controllers.Main.txtFile": "File", "SSE.Controllers.Main.txtGrandTotal": "Totale complessivo", "SSE.Controllers.Main.txtGroup": "Gruppo", "SSE.Controllers.Main.txtHours": "Ore", "SSE.Controllers.Main.txtLines": "Linee", "SSE.Controllers.Main.txtMath": "Matematica", "SSE.Controllers.Main.txtMinutes": "Minuti", "SSE.Controllers.Main.txtMonths": "Me<PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Selezione multipla", "SSE.Controllers.Main.txtOr": "%1 o %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Pagina %1 di %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "<PERSON>par<PERSON> <PERSON>", "SSE.Controllers.Main.txtPrintArea": "Area di stampa", "SSE.Controllers.Main.txtQuarter": "Quarter", "SSE.Controllers.Main.txtQuarters": "Quarti", "SSE.Controllers.Main.txtRectangles": "Rettangoli", "SSE.Controllers.Main.txtRow": "Riga", "SSE.Controllers.Main.txtRowLbls": "Etichette di riga", "SSE.Controllers.Main.txtSeconds": "Secondi", "SSE.Controllers.Main.txtSeries": "Serie", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Callout linea 1 (bordo e barra degli accenti)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Linea callout 2 (bordo e barra degli accenti)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Callout linea 3 (bordo e barra degli accenti)", "SSE.Controllers.Main.txtShape_accentCallout1": "Callout linea 1 (barra di accento)", "SSE.Controllers.Main.txtShape_accentCallout2": "Callout linea 2 (barra di accento)", "SSE.Controllers.Main.txtShape_accentCallout3": "Callout linea 3 (barra di accento)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Indietro o Pulsante Precedente", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Pulsante di Inizio", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Pulsan<PERSON> Vuoto", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Pulsante Documento", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Pulsante Fine", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Pulsante Avanti o Successivo", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "Pulsante Home", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Pulsante Informazioni", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Pulsante filmato", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Pulsante Invio", "SSE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_arc": "Arco", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> piegata", "SSE.Controllers.Main.txtShape_bentConnector5": "<PERSON><PERSON><PERSON> a gomito", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "<PERSON><PERSON><PERSON> freccia a gomito", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "<PERSON><PERSON><PERSON> a doppia freccia a gomito", "SSE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON><PERSON> curva in alto", "SSE.Controllers.Main.txtShape_bevel": "Smussat<PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "<PERSON>o a tutto sesto", "SSE.Controllers.Main.txtShape_borderCallout1": "Callout Linea 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Callout Linea 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Callout linea 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON> parentesi graffa", "SSE.Controllers.Main.txtShape_callout1": "Callout linea 1 (nessun bordo)", "SSE.Controllers.Main.txtShape_callout2": "Callout linea 2 (ness<PERSON> bordo)", "SSE.Controllers.Main.txtShape_callout3": "Callout linea 3 (ness<PERSON> bordo)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "freccia a Gallone", "SSE.Controllers.Main.txtShape_chord": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON> circolare", "SSE.Controllers.Main.txtShape_cloud": "Nuvola", "SSE.Controllers.Main.txtShape_cloudCallout": "Callout <PERSON>", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON> curvo", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "<PERSON><PERSON><PERSON> a freccia curva", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON> a doppia freccia curva", "SSE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON> curva in basso", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Freccia curva a sinistra", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Freccia curva a destra", "SSE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON> curva in alto", "SSE.Controllers.Main.txtShape_decagon": "Decagono", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonale", "SSE.Controllers.Main.txtShape_diamond": "Diamante", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecagono", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON> onda", "SSE.Controllers.Main.txtShape_downArrow": "Freccia in giù", "SSE.Controllers.Main.txtShape_downArrowCallout": "Callout <PERSON> in basso", "SSE.Controllers.Main.txtShape_ellipse": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Nastro curvo e inclinato in basso", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Nastro curvato in alto", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagramma di flusso: processo alternativo", "SSE.Controllers.Main.txtShape_flowChartCollate": "Diagramma di flusso: Fascicolo", "SSE.Controllers.Main.txtShape_flowChartConnector": "Diagramma di flusso: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Diagramma di flusso: Decisione", "SSE.Controllers.Main.txtShape_flowChartDelay": "Diagramma di flusso: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Diagramma di flusso: Visualizza", "SSE.Controllers.Main.txtShape_flowChartDocument": "Diagramma di flusso: Documento", "SSE.Controllers.Main.txtShape_flowChartExtract": "Diagramma di flusso: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Diagramma di flusso: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagramma di flusso: Memoria interna", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagramma di flusso: Disco magnetico", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagramma di flusso: Memoria ad accesso diretto", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagramma di flusso: Memoria ad accesso sequenziale", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Diagramma di flusso: Input manuale", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Diagramma di flusso: Operazione manuale", "SSE.Controllers.Main.txtShape_flowChartMerge": "Diagramma di flusso: Unione", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Diagramma di flusso: Multidocumento", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagramma di flusso: <PERSON><PERSON><PERSON> fuori pagina", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagramma di flusso: <PERSON><PERSON> sal<PERSON>i", "SSE.Controllers.Main.txtShape_flowChartOr": "Diagramma di flusso: O", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagramma di flusso: Elaborazione predefinita", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Diagramma di flusso: Preparazione", "SSE.Controllers.Main.txtShape_flowChartProcess": "Diagramma di flusso: Elaborazione", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagramma di flusso: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagramma di flusso: Nastro perforato", "SSE.Controllers.Main.txtShape_flowChartSort": "Diagramma di flusso: Ordinamento", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagramma di flusso: Giunzione di somma", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Diagramma di flusso: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_foldedCorner": "angolo ripiegato", "SSE.Controllers.Main.txtShape_frame": "Cornice", "SSE.Controllers.Main.txtShape_halfFrame": "Mezza Cornice", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Ettagono", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Pentagono", "SSE.Controllers.Main.txtShape_horizontalScroll": "Scorrimento orizzontale", "SSE.Controllers.Main.txtShape_irregularSeal1": "Esplosione 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Esplosione 2", "SSE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Callout <PERSON><PERSON><PERSON> a sinistra", "SSE.Controllers.Main.txtShape_leftBrace": "Parentesi graffa aperta", "SSE.Controllers.Main.txtShape_leftBracket": "Parentesi quadra aperta", "SSE.Controllers.Main.txtShape_leftRightArrow": "Freccia bidirezionale sinistra destra", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Callout <PERSON><PERSON><PERSON> bidirezionane sinistra destra", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Freccia tridirezionale sinistra destra alto", "SSE.Controllers.Main.txtShape_leftUpArrow": "<PERSON><PERSON>cia in alto a sinistra", "SSE.Controllers.Main.txtShape_lightningBolt": "Fulmine", "SSE.Controllers.Main.txtShape_line": "Linea", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> doppia", "SSE.Controllers.Main.txtShape_mathDivide": "Divisione", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Moltiplicazione", "SSE.Controllers.Main.txtShape_mathNotEqual": "Non uguale", "SSE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_moon": "Luna", "SSE.Controllers.Main.txtShape_noSmoking": "Simbolo \"No\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "<PERSON><PERSON>cia dentellata a destra ", "SSE.Controllers.Main.txtShape_octagon": "Ottagono", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogram<PERSON>", "SSE.Controllers.Main.txtShape_pentagon": "Pentagono", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "Firma", "SSE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline2": "Forma libera", "SSE.Controllers.Main.txtShape_quadArrow": "Freccia a incrocio", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Callout <PERSON><PERSON> a incrocio", "SSE.Controllers.Main.txtShape_rect": "Rettangolo", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON>stro inclinato in basso", "SSE.Controllers.Main.txtShape_ribbon2": "Nastro in alto", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> destra", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Callout <PERSON><PERSON><PERSON> a destra", "SSE.Controllers.Main.txtShape_rightBrace": "Parentesi graffa chiusa", "SSE.Controllers.Main.txtShape_rightBracket": "Parentesi quadra chiusa", "SSE.Controllers.Main.txtShape_round1Rect": "Rettangolo ad angolo singolo smussato", "SSE.Controllers.Main.txtShape_round2DiagRect": "Rettangolo ad angolo diagonale smussato", "SSE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON><PERSON> smussato dallo stesso lato", "SSE.Controllers.Main.txtShape_roundRect": "Rettangolo ad angoli smussati", "SSE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_smileyFace": "Faccia sorridente", "SSE.Controllers.Main.txtShape_snip1Rect": "Ritaglia singolo angolo del rettangolo", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Ritaglia angoli in diagonale del rettangolo", "SSE.Controllers.Main.txtShape_snip2SameRect": "Ritaglia angoli dallo stesso lato del retta<PERSON>lo", "SSE.Controllers.Main.txtShape_snipRoundRect": "Ritaglia e smussa singolo angolo retta<PERSON>lo", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "Stella a 10 punte", "SSE.Controllers.Main.txtShape_star12": "Stella a 12 punte", "SSE.Controllers.Main.txtShape_star16": "Stella a 16 punte", "SSE.Controllers.Main.txtShape_star24": "Stella a 24 punte", "SSE.Controllers.Main.txtShape_star32": "Stella a 32 punte", "SSE.Controllers.Main.txtShape_star4": "Stella a 4 punte", "SSE.Controllers.Main.txtShape_star5": "Stella a 5 punte", "SSE.Controllers.Main.txtShape_star6": "Stella a 6 punte", "SSE.Controllers.Main.txtShape_star7": "Stella a 7 punte", "SSE.Controllers.Main.txtShape_star8": "Stella a 8 punte", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Freccia a strisce verso destra ", "SSE.Controllers.Main.txtShape_sun": "Sole", "SSE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_textRect": "Casella di testo", "SSE.Controllers.Main.txtShape_trapezoid": "Trapezio", "SSE.Controllers.Main.txtShape_triangle": "Triangolo", "SSE.Controllers.Main.txtShape_upArrow": "Freccia su", "SSE.Controllers.Main.txtShape_upArrowCallout": "Callout <PERSON> in alto", "SSE.Controllers.Main.txtShape_upDownArrow": "Freccia bidirezionale su giù", "SSE.Controllers.Main.txtShape_uturnArrow": "Freccia a inversione", "SSE.Controllers.Main.txtShape_verticalScroll": "Scorrimento verticale", "SSE.Controllers.Main.txtShape_wave": "On<PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Callout <PERSON>", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Callout <PERSON>", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Callout <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStarsRibbons": "Stelle e nastri", "SSE.Controllers.Main.txtStyle_Bad": "rovinato", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Controlla cella", "SSE.Controllers.Main.txtStyle_Comma": "Virgola", "SSE.Controllers.Main.txtStyle_Currency": "Valuta", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Testo esplicativo", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Titolo 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Titolo 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Titolo 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Titolo 4", "SSE.Controllers.Main.txtStyle_Input": "Input", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Cella collegata", "SSE.Controllers.Main.txtStyle_Neutral": "Neutro", "SSE.Controllers.Main.txtStyle_Normal": "Normale", "SSE.Controllers.Main.txtStyle_Note": "<PERSON>a", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "Percento", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Totale", "SSE.Controllers.Main.txtStyle_Warning_Text": "Testo di Avviso", "SSE.Controllers.Main.txtTab": "Scheda", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "<PERSON>a", "SSE.Controllers.Main.txtUnlock": "Sbloccare", "SSE.Controllers.Main.txtUnlockRange": "<PERSON><PERSON><PERSON><PERSON>lo", "SSE.Controllers.Main.txtUnlockRangeDescription": "Inserisci la password per modificare questo intervallo:", "SSE.Controllers.Main.txtUnlockRangeWarning": "L'intervallo che cerchi di cambiare è protetto con password.", "SSE.Controllers.Main.txtValues": "Valori", "SSE.Controllers.Main.txtXAxis": "Asse <PERSON>", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> sconos<PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Il tuo browser non è supportato.", "SSE.Controllers.Main.uploadDocExtMessage": "Formato documento sconosciuto.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nessun documento caricato.", "SSE.Controllers.Main.uploadDocSizeMessage": "Il limite massimo delle dimensioni del documento è stato superato.", "SSE.Controllers.Main.uploadImageExtMessage": "Formato immagine sconosciuto.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Nessuna immagine caricata.", "SSE.Controllers.Main.uploadImageSizeMessage": "L'immagine è troppo grande. La dimensione massima è 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Caricamento immagine in corso...", "SSE.Controllers.Main.uploadImageTitleText": "Caricamento dell'immagine", "SSE.Controllers.Main.waitText": "Per favore, attendi...", "SSE.Controllers.Main.warnBrowserIE9": "L'applicazione è poco compatibile con IE9. Usa IE10 o più recente", "SSE.Controllers.Main.warnBrowserZoom": "Le impostazioni correnti di zoom del tuo browser non sono completamente supportate. Per favore, ritorna allo zoom predefinito premendo Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "Hai raggiunto il limite per le connessioni simultanee agli editor %1. Questo documento verrà aperto in sola lettura.<br>Contatta l’amministratore per saperne di più.", "SSE.Controllers.Main.warnLicenseExp": "La tua licenza è scaduta.<br>Si prega di aggiornare la licenza e ricaricare la pagina.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licenza scaduta.<br>Non puoi modificare il documento.<br>Contatta l'amministratore.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "La licenza dev'essere rinnovata<br>Hai un accesso limitato alle funzioni di modifica del documento<br>Contatta l'amministratore per ottenere l'accesso completo", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Hai raggiunto il limite per gli utenti con accesso agli editor %1. Contatta l’amministratore per saperne di più.", "SSE.Controllers.Main.warnNoLicense": "Hai raggiunto il limite per le connessioni simultanee agli editor %1. Questo documento verrà aperto in sola lettura.<br>Contatta il team di vendita di %1 per i termini di aggiornamento personali.", "SSE.Controllers.Main.warnNoLicenseUsers": "Hai raggiunto il limite per gli utenti con accesso agli editor %1. Contatta il team di vendita di %1 per i termini di aggiornamento personali.", "SSE.Controllers.Main.warnProcessRightsChange": "Ti è stato negato il diritto di modificare il file.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON> i <PERSON>", "SSE.Controllers.Print.textFirstCol": "Prima colonna", "SSE.Controllers.Print.textFirstRow": "Prima riga", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON> bloc<PERSON>", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON> bloc<PERSON>", "SSE.Controllers.Print.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Controllers.Print.textNoRepeat": "Non ripetere", "SSE.Controllers.Print.textRepeat": "R<PERSON>eti...", "SSE.Controllers.Print.textSelectRange": "Seleziona intervallo", "SSE.Controllers.Print.textWarning": "Avviso", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.warnCheckMargings": "<PERSON>gini non corretti", "SSE.Controllers.Search.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Controllers.Search.textNoTextFound": "Impossibile trovare i dati che stavi cercando. Ti preghiamo di modificare le opzioni di ricerca.", "SSE.Controllers.Search.textReplaceSkipped": "La sostituzione è stata effettuata. {0} occorrenze sono state saltate.", "SSE.Controllers.Search.textReplaceSuccess": "La ricerca è stata effettuata. {0} occorrenze sono state sostituite", "SSE.Controllers.Statusbar.errorLastSheet": "La cartella di lavoro deve contenere almeno un foglio di lavoro visibile.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Impossibile eliminare il foglio di lavoro.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connessione persa</b><br>Tentativo di connessione in corso. Si prega di controllare le impostazioni di connessione.", "SSE.Controllers.Statusbar.textSheetViewTip": "Sei in modalità di visualizzazione del foglio di calcolo. I filtri e l'ordinamento sono visibili solo a te e a coloro che sono ancora in questa visualizzazione.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Sei in modalità di visualizzazione del foglio di calcolo. I filtri sono visibili solo a te e a coloro che sono ancora in questa visualizzazione.", "SSE.Controllers.Statusbar.warnDeleteSheet": "I fogli di lavoro selezionati potrebbero contenere dati. Sei sicuro di voler procedere?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Il carattere che vuoi salvare non è accessibile su questo dispositivo.<br>Lo stile di testo sarà visualizzato usando uno dei caratteri di sistema, il carattere salvato sarà usato quando accessibile.<br>Vuoi continuare?", "SSE.Controllers.Toolbar.errorComboSeries": "Per creare un grafico a combinazione, seleziona almeno due serie di dati.", "SSE.Controllers.Toolbar.errorMaxRows": "ERRORE! Il numero massimo di serie di dati per grafico è 255.", "SSE.Controllers.Toolbar.errorStockChart": "Ordine di righe scorretto. Per creare un grafico in pila posiziona i dati nel foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "SSE.Controllers.Toolbar.textAccent": "Accenti", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Direzionale", "SSE.Controllers.Toolbar.textFontSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore numerico compreso tra 1 e 409", "SSE.Controllers.Toolbar.textFraction": "Frazioni", "SSE.Controllers.Toolbar.textFunction": "Funzioni", "SSE.Controllers.Toolbar.textIndicator": "Indicatori", "SSE.Controllers.Toolbar.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIntegral": "Integrali", "SSE.Controllers.Toolbar.textLargeOperator": "Operatori di grandi dimensioni", "SSE.Controllers.Toolbar.textLimitAndLog": "Limiti e Logaritmi", "SSE.Controllers.Toolbar.textLongOperation": "Operazione lunga", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operatori", "SSE.Controllers.Toolbar.textPivot": "<PERSON>bella pivot", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Valutazioni", "SSE.Controllers.Toolbar.textRecentlyUsed": "Usati di recente", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Forme", "SSE.Controllers.Toolbar.textSymbols": "Simboli", "SSE.Controllers.Toolbar.textWarning": "Avviso", "SSE.Controllers.Toolbar.txtAccent_Accent": "Acuto", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON>-Sinistra in alto", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Freccia verso sinistra sopra", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Freccia a destra alta", "SSE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferiore", "SSE.Controllers.Toolbar.txtAccent_BarTop": "barra sopra", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Formula racchiusa (con segnaposto)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula racchiusa (Esempio)", "SSE.Controllers.Toolbar.txtAccent_Check": "Controlla", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON><PERSON><PERSON> graffa in basso", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vettore A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC con barra superiore", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y con barra sopra", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Punto triplo", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON> punto", "SSE.Controllers.Toolbar.txtAccent_Dot": "Punt<PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Doppia barra superiore", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Raggruppa<PERSON> carattere sotto", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Raggruppamento carattere sopra", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpione verso sinistra sopra", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpione verso destra sopra", "SSE.Controllers.Toolbar.txtAccent_Hat": "Cir<PERSON>fle<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parentesi con separatori", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parentesi con separatori", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parentesi con separatori", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Casi (due condizioni)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Casi (tre condizioni)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON>mp<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON>mp<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Esempio di casi", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Coefficiente binomiale", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Coefficiente binomiale", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentesi con separatori", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON><PERSON> sing<PERSON>", "SSE.Controllers.Toolbar.txtDeleteCells": "Elimina celle", "SSE.Controllers.Toolbar.txtExpand": "Espandi e ordina", "SSE.Controllers.Toolbar.txtExpandSort": "I dati vicini alla selezione non saranno ordinati. Vuoi estendere la selezione in modo da includere i dati adiacenti o lasciare la selezione invariata?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Frazione obliqua", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Differenziale", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Differenziale", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Differenziale", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Differenziale", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Frazione lineare", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi diviso 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Frazione piccola", "SSE.Controllers.Toolbar.txtFractionVertical": "Frazione impilata", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Funzione coseno inversa", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Funzione coseno iperbolica inversa", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Funzione cotangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Funzione cotangente iperbolica inversa", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Funzione cosecante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Funzione cosecante iperbolica inversa ", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Funzione secante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Funziono secante iperbolica inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Funzione seno inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Funzione seno iperbolica inversa", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Funzione tangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Funzione tangente iperbolica inversa", "SSE.Controllers.Toolbar.txtFunction_Cos": "Funzione Coseno", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Funzione coseno iperbolica", "SSE.Controllers.Toolbar.txtFunction_Cot": "Funzione Cotangente", "SSE.Controllers.Toolbar.txtFunction_Coth": "Funzione Cotangente iperbolica", "SSE.Controllers.Toolbar.txtFunction_Csc": "Funzione cosecante", "SSE.Controllers.Toolbar.txtFunction_Csch": "Funzione cosecante iperbolica", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> the<PERSON>", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Formula tangente", "SSE.Controllers.Toolbar.txtFunction_Sec": "Funzione secante", "SSE.Controllers.Toolbar.txtFunction_Sech": "Funzione secante iperbolica inversa", "SSE.Controllers.Toolbar.txtFunction_Sin": "Funzione seno", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Funzione seno iperbolica", "SSE.Controllers.Toolbar.txtFunction_Tan": "Funzione tangente", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Funzione tangente iperbolica", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Formato del numero", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Chiaro", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medio", "SSE.Controllers.Toolbar.txtInsertCells": "Inserisci celle", "SSE.Controllers.Toolbar.txtIntegral": "Integrale", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differenziale theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differenziale x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differenziale y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrale", "SSE.Controllers.Toolbar.txtIntegralDouble": "Doppio integrale", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Doppio integrale", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Doppio integrale", "SSE.Controllers.Toolbar.txtIntegralOriented": "Integrazione di contorno", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integrazione di contorno", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integrale di superficie", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integrale di superficie", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integrale di superficie", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integrazione di contorno", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integrale di volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integrale di volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integrale di volume", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integrale", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON> Integrale", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integrale triplo", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integrale triplo", "SSE.Controllers.Toolbar.txtInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Congiunzione logica ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Congiunzione logica ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Congiunzione logica ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Congiunzione logica ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Congiunzione logica ", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unione", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersezione", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersezione", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersezione", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersezione", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersezione", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Somma", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Unione", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unione", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unione", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unione", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unione", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Esempio limite", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Esemp<PERSON> massimo", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo naturale", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimo", "SSE.Controllers.Toolbar.txtLockSort": "I dati si trovano accanto alla tua selezione, ma non sei autorizzato per modificare le celle.<br>Vuoi continuare con la selezione corrente?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Matrice vuota", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 <PERSON><PERSON> vuota", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 <PERSON>rice vuota", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Matrice vuota", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON> vuota con parentesi", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON> vuota con parentesi", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON> vuota con parentesi", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON> vuota con parentesi", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 <PERSON><PERSON> vuota", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Matrice vuota", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Matrice vuota", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 <PERSON><PERSON> vuota", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Punti di base", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Punti linea mediana", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> diagonali", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Punti verticali", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> sparsa", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> sparsa", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Matrice identità", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Matrice identità", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Matrice identità", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Matrice identità", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> in basso", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON>-Sinistra in alto", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Freccia verso sinistra sotto", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Freccia verso sinistra sopra", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Freccia a destra bassa", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Freccia a destra alta", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Due punti uguali", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Rendimenti", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Rendimenti delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Uguale a per definizione", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta uguale a", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> in basso", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON>-Sinistra in alto", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Freccia verso sinistra sotto", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Freccia verso sinistra sopra", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Freccia a destra bassa", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Freccia a destra alta", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON> con", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radicale", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radicale", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Radice quadrata con grado", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radicale con grado", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSup": "Pedice-Apice", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Pedice-Apice sinistro", "SSE.Controllers.Toolbar.txtScriptSup": "Apice", "SSE.Controllers.Toolbar.txtSorting": "Ordinamento", "SSE.Controllers.Toolbar.txtSortSelected": "Ordina selezionati", "SSE.Controllers.Toolbar.txtSymbol_about": "Approssimativamente", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON>uasi uguale a", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operatore asterisco", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operatore elenco puntato", "SSE.Controllers.Toolbar.txtSymbol_cap": "Intersezione", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON>dice cubica", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Ellissi orizzontale di linea mediana", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Approssimativamente uguale a", "SSE.Controllers.Toolbar.txtSymbol_cup": "Unione", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON> diagonale in basso a destra", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Segno di divisione", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Freccia in giù", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Insieme vuoto", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identico a", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Fattoriale", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Gradi Fah<PERSON>he<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_forall": "Per tutti", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Maggiore o uguale a", "SSE.Controllers.Toolbar.txtSymbol_gg": "Molto più grande di", "SSE.Controllers.Toolbar.txtSymbol_greater": "Più grande di", "SSE.Controllers.Toolbar.txtSymbol_in": "Elemento Di", "SSE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON>cia sinistra-destra", "SSE.Controllers.Toolbar.txtSymbol_leq": "Minore o uguale a ", "SSE.Controllers.Toolbar.txtSymbol_less": "Meno di", "SSE.Controllers.Toolbar.txtSymbol_ll": "Molto meno di", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON>o più", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Non uguale a", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON> come <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_not": "Segno no", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Non esiste", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Differenziale parziale", "SSE.Controllers.Toolbar.txtSymbol_percent": "Percent<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporzionale a", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON>dice quarta", "SSE.Controllers.Toolbar.txtSymbol_qed": "Fine della dimostrazione", "SSE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON> diagonale in alto a destra", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> destra", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Segno Radicale", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Dunque", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "Segno di moltiplicazione", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Freccia su", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Variante Epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Variante Sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Ellissi verticale", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Stile tabella scuro", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Stile tabella chiaro", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Stile tabella medio", "SSE.Controllers.Toolbar.warnLongOperation": "L'operazione che stai per intraprendere potrebbe richiedere molto tempo per essere completata.<br>Vuoi continuare?", "SSE.Controllers.Toolbar.warnMergeLostData": "Solo i dati dalla cella sinistra superiore rimangono nella cella unita.<br>Sei sicuro di voler continuare?", "SSE.Controllers.Viewport.textFreezePanes": "Blocca riquad<PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "Mostra l'ombra dei riquadri bloccati", "SSE.Controllers.Viewport.textHideFBar": "Nascondi barra della formula", "SSE.Controllers.Viewport.textHideGridlines": "Nascondi griglia", "SSE.Controllers.Viewport.textHideHeadings": "Nascondi titoli", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separatore decimale", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Separatore delle migliaia", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Impostazioni utilizzate per riconoscere i dati numerici", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Qualificatore di testo", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Impostazioni avanzate", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(nessuna)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.textAddSelection": "Aggiungi la selezione corrente al filtro", "SSE.Views.AutoFilterDialog.textEmptyItem": "{<PERSON><PERSON> vuoti}", "SSE.Views.AutoFilterDialog.textSelectAll": "Se<PERSON><PERSON>na tutto", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Seleziona tutti i risultati della ricerca", "SSE.Views.AutoFilterDialog.textWarning": "Avviso", "SSE.Views.AutoFilterDialog.txtAboveAve": "Sopra la media", "SSE.Views.AutoFilterDialog.txtBegins": "Inizia con...", "SSE.Views.AutoFilterDialog.txtBelowAve": "sotto la media", "SSE.Views.AutoFilterDialog.txtBetween": "tra...", "SSE.Views.AutoFilterDialog.txtClear": "Svuota", "SSE.Views.AutoFilterDialog.txtContains": "Contiene...", "SSE.Views.AutoFilterDialog.txtEmpty": "Inserisci parametri del filtro", "SSE.Views.AutoFilterDialog.txtEnds": "Finisce con...", "SSE.Views.AutoFilterDialog.txtEquals": "Uguali...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtra per colore delle celle", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtra per colore dei carattere", "SSE.Views.AutoFilterDialog.txtGreater": "Più grande di...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Più grande o uguale a...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtro etichetta", "SSE.Views.AutoFilterDialog.txtLess": "Meno di...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Minore o uguale a ", "SSE.Views.AutoFilterDialog.txtNotBegins": "Non inizia con...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Non tra...", "SSE.Views.AutoFilterDialog.txtNotContains": "Non contiene...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Non finisce con...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Non è uguale...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Filtro numerico", "SSE.Views.AutoFilterDialog.txtReapply": "Riapplica", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Ordina per colore delle celle", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Ordina per colore del carattere", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Ordina dal più alto al più basso", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Ordina dal più basso al più alto", "SSE.Views.AutoFilterDialog.txtSortOption": "Altre opzioni di ordinamento...", "SSE.Views.AutoFilterDialog.txtTextFilter": "<PERSON><PERSON><PERSON>o", "SSE.Views.AutoFilterDialog.txtTitle": "Filtro", "SSE.Views.AutoFilterDialog.txtTop10": "Primi 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "<PERSON><PERSON> filtro", "SSE.Views.AutoFilterDialog.warnFilterError": "È necessario almeno un campo nell'area Valori per applicare un filtro valori.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Devi selezionare almeno un valore", "SSE.Views.CellEditor.textManager": "<PERSON>est<PERSON> nomi", "SSE.Views.CellEditor.tipFormula": "Inserisci funzione", "SSE.Views.CellRangeDialog.errorMaxRows": "ERRORE! Il numero massimo di serie di dati per grafico è 255.", "SSE.Views.CellRangeDialog.errorStockChart": "Ordine di righe scorretto. Per creare un grafico in pila posiziona i dati nel foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "SSE.Views.CellRangeDialog.txtEmpty": "Campo obbligatorio", "SSE.Views.CellRangeDialog.txtInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.CellRangeDialog.txtTitle": "Seleziona intervallo dati", "SSE.Views.CellSettings.strShrink": "Riduci e adatta", "SSE.Views.CellSettings.strWrap": "<PERSON><PERSON><PERSON> testo", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Colore sfondo", "SSE.Views.CellSettings.textBackground": "Colore di sfondo", "SSE.Views.CellSettings.textBorderColor": "Colore", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON> bordo", "SSE.Views.CellSettings.textClearRule": "Cancellare le regole", "SSE.Views.CellSettings.textColor": "Colore di riempimento", "SSE.Views.CellSettings.textColorScales": "Scale cromatiche", "SSE.Views.CellSettings.textCondFormat": "Formattazione condizionale", "SSE.Views.CellSettings.textControl": "Controllo del testo", "SSE.Views.CellSettings.textDataBars": "Barre di dati", "SSE.Views.CellSettings.textDirection": "Direzione", "SSE.Views.CellSettings.textFill": "Riempimento", "SSE.Views.CellSettings.textForeground": "Colore primo piano", "SSE.Views.CellSettings.textGradient": "Punti di sfumatura", "SSE.Views.CellSettings.textGradientColor": "Colore", "SSE.Views.CellSettings.textGradientFill": "Riempimento sfumato", "SSE.Views.CellSettings.textIndent": "Rientro", "SSE.Views.CellSettings.textItems": "elementi", "SSE.Views.CellSettings.textLinear": "Lineare", "SSE.Views.CellSettings.textManageRule": "Gestire le regole", "SSE.Views.CellSettings.textNewRule": "Nuova regola", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textOrientation": "Orientamento del testo", "SSE.Views.CellSettings.textPattern": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Posizione", "SSE.Views.CellSettings.textRadial": "Radiale", "SSE.Views.CellSettings.textSelectBorders": "Seleziona i bordi che desideri modificare applicando lo stile scelto sopra", "SSE.Views.CellSettings.textSelection": "Dalla selezione corrente", "SSE.Views.CellSettings.textThisPivot": "Da questo pivot", "SSE.Views.CellSettings.textThisSheet": "Da questo foglio di calcolo", "SSE.Views.CellSettings.textThisTable": "Da questa tabella", "SSE.Views.CellSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "SSE.Views.CellSettings.tipAll": "Imposta bordo esterno e tutte le linee interne", "SSE.Views.CellSettings.tipBottom": "Imposta solo bordo esterno inferiore", "SSE.Views.CellSettings.tipDiagD": "<PERSON>mpo<PERSON> il bordo diagonale in basso", "SSE.Views.CellSettings.tipDiagU": "<PERSON>mpo<PERSON> il bordo diagonale in alto", "SSE.Views.CellSettings.tipInner": "<PERSON><PERSON><PERSON> solo linee interne", "SSE.Views.CellSettings.tipInnerHor": "Imposta solo linee interne orizzontali", "SSE.Views.CellSettings.tipInnerVert": "<PERSON>mpo<PERSON> solo linee interne verticali", "SSE.Views.CellSettings.tipLeft": "Imposta solo bordo esterno sinistro", "SSE.Views.CellSettings.tipNone": "Non impostare bordi", "SSE.Views.CellSettings.tipOuter": "<PERSON>mpo<PERSON> solo bordi esterni", "SSE.Views.CellSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "SSE.Views.CellSettings.tipRight": "Impo<PERSON> solo bordo esterno destro", "SSE.Views.CellSettings.tipTop": "Imposta solo bordo esterno superiore", "SSE.Views.ChartDataDialog.errorInFormula": "C'è un errore nella formula che hai inserito.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Il riferimento non è valido. Il riferimento deve essere a un foglio di lavoro aperto.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Il numero massimo di punti in serie per grafico è di 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Il numero massimo di serie di dati per grafico è di 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Il riferimento non è valido. I riferimenti per titoli, valori, dimensioni o etichette dati devono essere una singola cella, riga o colonna.", "SSE.Views.ChartDataDialog.errorNoValues": "Per creare un grafico, la serie deve contenere almeno un valore.", "SSE.Views.ChartDataDialog.errorStockChart": "<PERSON>ighe ordinate in modo errato. Per creare un grafico azionario posizionare i dati sul foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "Etichette asse orizzontale (categoria)", "SSE.Views.ChartDataDialog.textData": "Intervallo dati del grafico", "SSE.Views.ChartDataDialog.textDelete": "Elimina", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "Modifica", "SSE.Views.ChartDataDialog.textInvalidRange": "Intervallo di celle non valido", "SSE.Views.ChartDataDialog.textSelectData": "Seleziona dati", "SSE.Views.ChartDataDialog.textSeries": "Voci legenda (serie)", "SSE.Views.ChartDataDialog.textSwitch": "Cambia riga/colonna", "SSE.Views.ChartDataDialog.textTitle": "<PERSON><PERSON> gra<PERSON>o", "SSE.Views.ChartDataDialog.textUp": "Vai verso l'alto", "SSE.Views.ChartDataRangeDialog.errorInFormula": "C'è un errore nella formula che hai inserito.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Il riferimento non è valido. Il riferimento deve essere a un foglio di lavoro aperto.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Il numero massimo di punti in serie per grafico è di 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Il numero massimo di serie di dati per grafico è di 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Il riferimento non è valido. I riferimenti per titoli, valori, dimensioni o etichette dati devono essere una singola cella, riga o colonna.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Per creare un grafico, la serie deve contenere almeno un valore.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "<PERSON>ighe ordinate in modo errato. Per creare un grafico azionario posizionare i dati sul foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Intervallo di celle non valido", "SSE.Views.ChartDataRangeDialog.textSelectData": "Seleziona dati", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Intervallo di etichette degli assi", "SSE.Views.ChartDataRangeDialog.txtChoose": "Scegli l'intervallo", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Nome serie", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Etichette degli assi", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Modifica serie", "SSE.Views.ChartDataRangeDialog.txtValues": "Valori", "SSE.Views.ChartDataRangeDialog.txtXValues": "Valori X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Valori Y", "SSE.Views.ChartSettings.errorMaxRows": "Il numero massimo di serie di dati per grafico è 255.", "SSE.Views.ChartSettings.strLineWeight": "Lunghezza linea", "SSE.Views.ChartSettings.strSparkColor": "Colore", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textAdvanced": "Mostra impostazioni avanzate", "SSE.Views.ChartSettings.textBorderSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 0 pt e 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Cambia tipo", "SSE.Views.ChartSettings.textChartType": "Cambia tipo di grafico", "SSE.Views.ChartSettings.textDown": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textEditData": "Modifica Dati e Posizione", "SSE.Views.ChartSettings.textFirstPoint": "Primo punto", "SSE.Views.ChartSettings.textHeight": "Altezza", "SSE.Views.ChartSettings.textHighPoint": "Punto alto", "SSE.Views.ChartSettings.textKeepRatio": "Proporzioni costanti", "SSE.Views.ChartSettings.textLastPoint": "<PERSON><PERSON><PERSON>o", "SSE.Views.ChartSettings.textLeft": "A sinistra", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON>o basso", "SSE.Views.ChartSettings.textMarkers": "Indicatori", "SSE.Views.ChartSettings.textNegativePoint": "Punto negativo", "SSE.Views.ChartSettings.textPerspective": "Prospettiva", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRight": "A destra", "SSE.Views.ChartSettings.textSelectData": "Seleziona dati", "SSE.Views.ChartSettings.textShow": "Visualizza", "SSE.Views.ChartSettings.textSize": "Dimensione", "SSE.Views.ChartSettings.textStyle": "Stile", "SSE.Views.ChartSettings.textSwitch": "Cambiare riga/colonna", "SSE.Views.ChartSettings.textType": "Tipo", "SSE.Views.ChartSettings.textUp": "Verso l'alto", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERRORE! Il numero massimo di punti in serie per grafico è 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ERRORE! Il numero massimo di serie di dati per grafico è 255.", "SSE.Views.ChartSettingsDlg.errorStockChart": "Ordine di righe scorretto. Per creare un grafico in pila posiziona i dati nel foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Non spostare o ridimensionare con le celle", "SSE.Views.ChartSettingsDlg.textAlt": "Testo alternativo", "SSE.Views.ChartSettingsDlg.textAltDescription": "Descrizione", "SSE.Views.ChartSettingsDlg.textAltTip": "La rappresentazione alternativa testuale basata delle informazioni sull'oggetto visivo, che verrà letta alle persone con disabilità visive o cognitive per aiutarle a capire meglio quali informazioni ci sono nell'immagine, nella forma automatica, nel grafico o nella tabella.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "<PERSON>tta per ognuno", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Intersezione asse", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Opzioni asse", "SSE.Views.ChartSettingsDlg.textAxisPos": "Posizione asse", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Impostazioni asse", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Tra segni di spunta", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "In basso", "SSE.Views.ChartSettingsDlg.textCategoryName": "Nome categoria", "SSE.Views.ChartSettingsDlg.textCenter": "Centrato", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementi di grafico e<br>legenda di grafico", "SSE.Views.ChartSettingsDlg.textChartTitle": "Titolo del grafico", "SSE.Views.ChartSettingsDlg.textCross": "Interseca", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "in colonne", "SSE.Views.ChartSettingsDlg.textDataLabels": "<PERSON><PERSON><PERSON><PERSON> dati", "SSE.Views.ChartSettingsDlg.textDataRows": "in righe", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Visualizza legenda", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Celle nascoste e vuote", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Collega punti dei dati con la linea", "SSE.Views.ChartSettingsDlg.textFit": "<PERSON><PERSON> alla larghezza", "SSE.Views.ChartSettingsDlg.textFixed": "Bloccato", "SSE.Views.ChartSettingsDlg.textFormat": "Formato dell'etichetta", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Linee griglia", "SSE.Views.ChartSettingsDlg.textGroup": "Raggruppa Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Nascondi", "SSE.Views.ChartSettingsDlg.textHideAxis": "Nascondi assi", "SSE.Views.ChartSettingsDlg.textHigh": "In alto", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON>se or<PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON> oriz<PERSON>i secondari", "SSE.Views.ChartSettingsDlg.textHorizontal": "Orizzontale", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Centinaia", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "All'interno", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Fondo interno", "SSE.Views.ChartSettingsDlg.textInnerTop": "Alto interno", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.ChartSettingsDlg.textLabelDist": "Distanza dell'etichetta dell'asse", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervallo tra le etichette", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Opzioni etichetta", "SSE.Views.ChartSettingsDlg.textLabelPos": "Posizione etichetta", "SSE.Views.ChartSettingsDlg.textLayout": "Layout di <PERSON>gina", "SSE.Views.ChartSettingsDlg.textLeft": "A sinistra", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Sovrapposizione a sinistra", "SSE.Views.ChartSettingsDlg.textLegendBottom": "In basso", "SSE.Views.ChartSettingsDlg.textLegendLeft": "A sinistra", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "A destra", "SSE.Views.ChartSettingsDlg.textLegendTop": "In alto", "SSE.Views.ChartSettingsDlg.textLines": "Linee", "SSE.Views.ChartSettingsDlg.textLocationRange": "Campo di ubicazione", "SSE.Views.ChartSettingsDlg.textLogScale": "Scala logaritmica", "SSE.Views.ChartSettingsDlg.textLow": "In basso", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Principali o secondarie", "SSE.Views.ChartSettingsDlg.textMajorType": "Tipo principale", "SSE.Views.ChartSettingsDlg.textManual": "Manuale", "SSE.Views.ChartSettingsDlg.textMarkers": "Indicatori", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervallo tra i segni", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON> massimo", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON><PERSON>o secondario", "SSE.Views.ChartSettingsDlg.textMinValue": "Valore minimo", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Vicine all'asse", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Nessuna sovrapposizione", "SSE.Views.ChartSettingsDlg.textOneCell": "Sposta ma non ridimensionare con le celle", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Sui segni di graduazione", "SSE.Views.ChartSettingsDlg.textOut": "All'esterno", "SSE.Views.ChartSettingsDlg.textOuterTop": "Alto esterno", "SSE.Views.ChartSettingsDlg.textOverlay": "Sovrapposizione", "SSE.Views.ChartSettingsDlg.textReverse": "Valori in ordine inverso", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Ordine inverso", "SSE.Views.ChartSettingsDlg.textRight": "A destra", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Sovrapposizione a destra", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON>uo<PERSON>o", "SSE.Views.ChartSettingsDlg.textSameAll": "Uguale per tutti", "SSE.Views.ChartSettingsDlg.textSelectData": "Seleziona dati", "SSE.Views.ChartSettingsDlg.textSeparator": "Separatore etichette dati", "SSE.Views.ChartSettingsDlg.textSeriesName": "Nome serie", "SSE.Views.ChartSettingsDlg.textShow": "Visualizza", "SSE.Views.ChartSettingsDlg.textShowBorders": "Visualizzare i bordi del grafico", "SSE.Views.ChartSettingsDlg.textShowData": "Mostra i dati nelle righe e colonne nascoste", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON>ra celle vuote come", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Mostra asse", "SSE.Views.ChartSettingsDlg.textShowValues": "Visualizza valori del grafico", "SSE.Views.ChartSettingsDlg.textSingle": "Sparkline Singola", "SSE.Views.ChartSettingsDlg.textSmooth": "Sfumate", "SSE.Views.ChartSettingsDlg.textSnap": "Aggancia celle", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Intervalli di Sparkline ", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "Stile", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Migliaia", "SSE.Views.ChartSettingsDlg.textTickOptions": "Opzioni segni di spunta", "SSE.Views.ChartSettingsDlg.textTitle": "Grafico - Impostazioni avanzate", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Impostazioni avanzate", "SSE.Views.ChartSettingsDlg.textTop": "In alto", "SSE.Views.ChartSettingsDlg.textTrillions": "Migliaia di miliardi", "SSE.Views.ChartSettingsDlg.textTwoCell": "Sposta e ridimensiona con le celle", "SSE.Views.ChartSettingsDlg.textType": "Tipo", "SSE.Views.ChartSettingsDlg.textTypeData": "Tipo e dati", "SSE.Views.ChartSettingsDlg.textUnits": "Mostra unità", "SSE.Views.ChartSettingsDlg.textValue": "Valore", "SSE.Views.ChartSettingsDlg.textVertAxis": "Asse verticale", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Assi verticali secondari", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "<PERSON><PERSON> asse X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "<PERSON><PERSON> asse Y", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "Campo obbligatorio", "SSE.Views.ChartTypeDialog.errorComboSeries": "Per creare un grafico a combinazione, seleziona almeno due serie di dati.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Il tipo di grafico selezionato richiede l'asse secondario utilizzato da un grafico esistente. Seleziona un altro tipo di grafico.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON> secondari", "SSE.Views.ChartTypeDialog.textSeries": "Serie", "SSE.Views.ChartTypeDialog.textStyle": "Stile", "SSE.Views.ChartTypeDialog.textTitle": "Tipo di grafico", "SSE.Views.ChartTypeDialog.textType": "Tipo", "SSE.Views.CreatePivotDialog.textDataRange": "Intervallo dati di origine", "SSE.Views.CreatePivotDialog.textDestination": "Scegli dove posizionare la tabella", "SSE.Views.CreatePivotDialog.textExist": "Foglio di lavoro esistente", "SSE.Views.CreatePivotDialog.textInvalidRange": "Intervallo di celle non valido", "SSE.Views.CreatePivotDialog.textNew": "Nuovo foglio di lavoro", "SSE.Views.CreatePivotDialog.textSelectData": "Seleziona dati", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON> tabella pivot", "SSE.Views.CreatePivotDialog.txtEmpty": "Campo obbligatorio", "SSE.Views.CreateSparklineDialog.textDataRange": "Fonte di intervallo di dati", "SSE.Views.CreateSparklineDialog.textDestination": "Scegli dove posizionare i grafici sparkline", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Intervallo di celle non valido", "SSE.Views.CreateSparklineDialog.textSelectData": "Selezionare i dati", "SSE.Views.CreateSparklineDialog.textTitle": "Creare grafico sparkline", "SSE.Views.CreateSparklineDialog.txtEmpty": "Questo campo è obbligatorio", "SSE.Views.DataTab.capBtnGroup": "Raggruppa", "SSE.Views.DataTab.capBtnTextCustomSort": "Ordinamento personalizzato", "SSE.Views.DataTab.capBtnTextDataValidation": "Validazione dati", "SSE.Views.DataTab.capBtnTextRemDuplicates": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextToCol": "Testo a colonne", "SSE.Views.DataTab.capBtnUngroup": "Separa", "SSE.Views.DataTab.capDataFromText": "Ottieni i dati", "SSE.Views.DataTab.mniFromFile": "Dal locale TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Dall'indirizzo web di TXT/CSV", "SSE.Views.DataTab.textBelow": "<PERSON><PERSON><PERSON> di riepilogo sotto i dettagli", "SSE.Views.DataTab.textClear": "Elimina contorno", "SSE.Views.DataTab.textColumns": "Separare le colonne", "SSE.Views.DataTab.textGroupColumns": "Raggruppa colonne", "SSE.Views.DataTab.textGroupRows": "Raggruppa righe", "SSE.Views.DataTab.textRightOf": "Colonne di riepilogo a destra dei dettagli", "SSE.Views.DataTab.textRows": "Separare le righe", "SSE.Views.DataTab.tipCustomSort": "Ordinamento personalizzato", "SSE.Views.DataTab.tipDataFromText": "Ottenere i dati da file di testo o CSV", "SSE.Views.DataTab.tipDataValidation": "Validazione dati", "SSE.Views.DataTab.tipGroup": "Raggruppa intervallo di celle", "SSE.Views.DataTab.tipRemDuplicates": "R<PERSON><PERSON><PERSON> le righe duplicate da un foglio", "SSE.Views.DataTab.tipToColumns": "Separa il testo della cella in colonne", "SSE.Views.DataTab.tipUngroup": "Separare l'intervallo di celle", "SSE.Views.DataValidationDialog.errorFormula": "Il valore attualmente restituisce un errore. Vuoi continuare?", "SSE.Views.DataValidationDialog.errorInvalid": "Il valore inserito per il campo \"{0}\" non è valido.", "SSE.Views.DataValidationDialog.errorInvalidDate": "la data inserita per il campo \"{0}\" non è valida.", "SSE.Views.DataValidationDialog.errorInvalidList": "L'elenco sorgente dev'essere un elenco delimitato oppure un riferimento a una singola riga o colonna", "SSE.Views.DataValidationDialog.errorInvalidTime": "Il valore ora/tempo inserito per il campo \"{0}\" non è valido.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Il campo \"{1}\" dev'essere maggiore o uguale al campo \"{0}\"", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "È necessario immettere un valore sia nel campo \"{0}\" che nel campo \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Devi inserire un valore nel campo \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Impossibile trovare un intervallo col nome specificato", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Nelle condizioni \"{0}\" non possono essere utilizzati valori negativi", "SSE.Views.DataValidationDialog.errorNotNumeric": "Il campo \"{0}\" deve essere un valore numerico, un'espressione numerica o fare riferimento a una cella contenente un valore numerico.", "SSE.Views.DataValidationDialog.strError": "Messaggio di errore", "SSE.Views.DataValidationDialog.strInput": "Messaggio di input", "SSE.Views.DataValidationDialog.strSettings": "Impostazioni", "SSE.Views.DataValidationDialog.textAlert": "Avviso", "SSE.Views.DataValidationDialog.textAllow": "Autorizza", "SSE.Views.DataValidationDialog.textApply": "Applica questi cambiamenti a tutte le altre celle con le stesse impostazioni", "SSE.Views.DataValidationDialog.textCellSelected": "Quando la cella è selezionata, mostra questo messaggio di inserimento", "SSE.Views.DataValidationDialog.textCompare": "Confronta con", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Data di fine", "SSE.Views.DataValidationDialog.textEndTime": "Orario di fine", "SSE.Views.DataValidationDialog.textError": "Messaggio di errore", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Ignora vuoto", "SSE.Views.DataValidationDialog.textInput": "Messaggio di input", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "Messaggio", "SSE.Views.DataValidationDialog.textMin": "Minimo", "SSE.Views.DataValidationDialog.textSelectData": "Seleziona dati", "SSE.Views.DataValidationDialog.textShowDropDown": "Mostra elenco a tendina nella cella", "SSE.Views.DataValidationDialog.textShowError": "Mostra avviso di errore dopo aver immesso dati non validi", "SSE.Views.DataValidationDialog.textShowInput": "Mostra il messaggio di immissione quando la cella è selezionata", "SSE.Views.DataValidationDialog.textSource": "Origine", "SSE.Views.DataValidationDialog.textStartDate": "Data di Inizio", "SSE.Views.DataValidationDialog.textStartTime": "Tempo di Inzio", "SSE.Views.DataValidationDialog.textStop": "Termina", "SSE.Views.DataValidationDialog.textStyle": "Stile", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Quando l'utente inserisce dati non validi, mostra questo avviso di errore", "SSE.Views.DataValidationDialog.txtAny": "Qualsiasi valore", "SSE.Views.DataValidationDialog.txtBetween": "tra", "SSE.Views.DataValidationDialog.txtDate": "Data", "SSE.Views.DataValidationDialog.txtDecimal": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtElTime": "Tempo trascorso", "SSE.Views.DataValidationDialog.txtEndDate": "Data di fine", "SSE.Views.DataValidationDialog.txtEndTime": "Orario di fine", "SSE.Views.DataValidationDialog.txtEqual": "equivale a", "SSE.Views.DataValidationDialog.txtGreaterThan": "Maggiore di", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "maggiore o uguale a ", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "minore di", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "minore o uguale a", "SSE.Views.DataValidationDialog.txtList": "Elenco", "SSE.Views.DataValidationDialog.txtNotBetween": "non tra", "SSE.Views.DataValidationDialog.txtNotEqual": "non è uguale", "SSE.Views.DataValidationDialog.txtOther": "Altro", "SSE.Views.DataValidationDialog.txtStartDate": "Data di inizio", "SSE.Views.DataValidationDialog.txtStartTime": "Tempo di inzio", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON><PERSON><PERSON> testo", "SSE.Views.DataValidationDialog.txtTime": "<PERSON>a", "SSE.Views.DataValidationDialog.txtWhole": "Numero intero", "SSE.Views.DigitalFilterDialog.capAnd": "E", "SSE.Views.DigitalFilterDialog.capCondition1": "uguali", "SSE.Views.DigitalFilterDialog.capCondition10": "non finisce con", "SSE.Views.DigitalFilterDialog.capCondition11": "contiene", "SSE.Views.DigitalFilterDialog.capCondition12": "non contiene", "SSE.Views.DigitalFilterDialog.capCondition2": "non è uguale", "SSE.Views.DigitalFilterDialog.capCondition3": "è superiore a", "SSE.Views.DigitalFilterDialog.capCondition4": "è superiore o uguale a", "SSE.Views.DigitalFilterDialog.capCondition5": "è inferiore a", "SSE.Views.DigitalFilterDialog.capCondition6": "è inferiore o uguale a", "SSE.Views.DigitalFilterDialog.capCondition7": "inizia con", "SSE.Views.DigitalFilterDialog.capCondition8": "non inizia con", "SSE.Views.DigitalFilterDialog.capCondition9": "finisce con", "SSE.Views.DigitalFilterDialog.capOr": "O", "SSE.Views.DigitalFilterDialog.textNoFilter": "nessun filtro", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON> righe dove", "SSE.Views.DigitalFilterDialog.textUse1": "Utilizza ? per sostituire un singolo carattere", "SSE.Views.DigitalFilterDialog.textUse2": "Utilizza * per sostituire più caratteri", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedImgText": "Impostazioni avanzate dell'immagine", "SSE.Views.DocumentHolder.advancedShapeText": "Impostazioni avanzate forma", "SSE.Views.DocumentHolder.advancedSlicerText": "Impostazioni avanzate filtro dati", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON>a in basso", "SSE.Views.DocumentHolder.bulletsText": "Elenchi puntati e numerati", "SSE.Views.DocumentHolder.centerCellText": "Allinea in mezzo", "SSE.Views.DocumentHolder.chartDataText": "Selezionare i dati del grafico", "SSE.Views.DocumentHolder.chartText": "Impostazioni avanzate grafico", "SSE.Views.DocumentHolder.chartTypeText": "Cambiare tipo di grafico", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON>onna", "SSE.Views.DocumentHolder.deleteRowText": "Riga", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Ruota testo verso l'alto", "SSE.Views.DocumentHolder.direct90Text": "Ruota testo verso il basso", "SSE.Views.DocumentHolder.directHText": "Orizzontale", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON> del testo", "SSE.Views.DocumentHolder.editChartText": "Modifica dati", "SSE.Views.DocumentHolder.editHyperlinkText": "Modifica collegamento ipertestuale", "SSE.Views.DocumentHolder.insertColumnLeftText": "Colonna a sinistra", "SSE.Views.DocumentHolder.insertColumnRightText": "Colonna a destra", "SSE.Views.DocumentHolder.insertRowAboveText": "Riga sopra", "SSE.Views.DocumentHolder.insertRowBelowText": "Riga sotto", "SSE.Views.DocumentHolder.originalSizeText": "Dimensione reale", "SSE.Views.DocumentHolder.removeHyperlinkText": "Elimina collegamento ipertestuale", "SSE.Views.DocumentHolder.selectColumnText": "Colonna intera", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON> colonna", "SSE.Views.DocumentHolder.selectRowText": "Riga", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strDetails": "Dettagli firma", "SSE.Views.DocumentHolder.strSetup": "Impostazioni firma", "SSE.Views.DocumentHolder.strSign": "Firma", "SSE.Views.DocumentHolder.textAlign": "Allinea", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> in secondo piano", "SSE.Views.DocumentHolder.textArrangeBackward": "Porta indietro", "SSE.Views.DocumentHolder.textArrangeForward": "Porta avanti", "SSE.Views.DocumentHolder.textArrangeFront": "<PERSON>a in primo piano", "SSE.Views.DocumentHolder.textAverage": "Media", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON> puntati", "SSE.Views.DocumentHolder.textCount": "Conteggio", "SSE.Views.DocumentHolder.textCrop": "Rita<PERSON>", "SSE.Views.DocumentHolder.textCropFill": "Riempimento", "SSE.Views.DocumentHolder.textCropFit": "Adatta", "SSE.Views.DocumentHolder.textEditPoints": "Modifica punti", "SSE.Views.DocumentHolder.textEntriesList": "Seleziona dal menù a cascata", "SSE.Views.DocumentHolder.textFlipH": "Capovolgi orizzontalmente", "SSE.Views.DocumentHolder.textFlipV": "Capovolgi verticalmente", "SSE.Views.DocumentHolder.textFreezePanes": "Blocca riquad<PERSON>", "SSE.Views.DocumentHolder.textFromFile": "Da file", "SSE.Views.DocumentHolder.textFromStorage": "Da spazio di archiviazione", "SSE.Views.DocumentHolder.textFromUrl": "Da URL", "SSE.Views.DocumentHolder.textListSettings": "Impostazioni elenco", "SSE.Views.DocumentHolder.textMacro": "Assegnare macro", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "Altre funzioni", "SSE.Views.DocumentHolder.textMoreFormats": "Altri formati", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numerazione", "SSE.Views.DocumentHolder.textReplace": "Sostituisci immagine", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Ruota 90° a sinistra", "SSE.Views.DocumentHolder.textRotate90": "Ruota 90° a destra", "SSE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>a in basso", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Allinea al centro", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Allinea a sinistra", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Allinea in mezzo", "SSE.Views.DocumentHolder.textShapeAlignRight": "Allinea a destra", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON>a in alto", "SSE.Views.DocumentHolder.textStdDev": "Deviazione standard", "SSE.Views.DocumentHolder.textSum": "Somma", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Sblocca i riquadri", "SSE.Views.DocumentHolder.textVar": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersArrow": "Punti elenco a freccia", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Punti elenco a segno di spunta", "SSE.Views.DocumentHolder.tipMarkersDash": "Punti elenco a trattino", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Punti elenco a rombo pieno", "SSE.Views.DocumentHolder.tipMarkersFRound": "Punti elenco rotondi pieni", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Punti elenco quadrati pieni", "SSE.Views.DocumentHolder.tipMarkersHRound": "Punti elenco rotondi vuoti", "SSE.Views.DocumentHolder.tipMarkersStar": "Punti elenco a stella", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON>a in alto", "SSE.Views.DocumentHolder.txtAccounting": "Contabilità", "SSE.Views.DocumentHolder.txtAddComment": "Aggiungi commento", "SSE.Views.DocumentHolder.txtAddNamedRange": "Definisci nome", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Adatta automaticamente la larghezza della colonna", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Adatta automaticamente l'altezza della riga", "SSE.Views.DocumentHolder.txtClear": "Svuota", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Commenti", "SSE.Views.DocumentHolder.txtClearFormat": "Formato", "SSE.Views.DocumentHolder.txtClearHyper": "Collegamenti ipertestuali", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Cancella i Gruppi di Sparkline selezionati", "SSE.Views.DocumentHolder.txtClearSparklines": "Elimina Sparklines selezionate", "SSE.Views.DocumentHolder.txtClearText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumn": "Colonna intera", "SSE.Views.DocumentHolder.txtColumnWidth": "Imposta larghezza colonna", "SSE.Views.DocumentHolder.txtCondFormat": "Formattazione condizionale", "SSE.Views.DocumentHolder.txtCopy": "Copia", "SSE.Views.DocumentHolder.txtCurrency": "Valuta", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON><PERSON> larghezza colonna", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Personalizza altezza riga", "SSE.Views.DocumentHolder.txtCustomSort": "Ordinamento personalizzato", "SSE.Views.DocumentHolder.txtCut": "Taglia", "SSE.Views.DocumentHolder.txtDate": "Data", "SSE.Views.DocumentHolder.txtDelete": "Elimina", "SSE.Views.DocumentHolder.txtDescending": "Decrescente", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuisci orizzontalmente", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuisci verticalmente", "SSE.Views.DocumentHolder.txtEditComment": "Modifica commento", "SSE.Views.DocumentHolder.txtFilter": "Filtro", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtra per colore della cella", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtra per colore dei carattere", "SSE.Views.DocumentHolder.txtFilterValue": "Filtra per il valore della cella selezionato", "SSE.Views.DocumentHolder.txtFormula": "Inserisci funzione", "SSE.Views.DocumentHolder.txtFraction": "Frazione", "SSE.Views.DocumentHolder.txtGeneral": "Generale", "SSE.Views.DocumentHolder.txtGetLink": "Ottenere il link a questa gamma", "SSE.Views.DocumentHolder.txtGroup": "Raggruppa", "SSE.Views.DocumentHolder.txtHide": "Nascondi", "SSE.Views.DocumentHolder.txtInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "Collegamento ipertestuale", "SSE.Views.DocumentHolder.txtNumber": "Numero", "SSE.Views.DocumentHolder.txtNumFormat": "Formato numero", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercentage": "Percentage", "SSE.Views.DocumentHolder.txtReapply": "Riapplica", "SSE.Views.DocumentHolder.txtRow": "Riga intera", "SSE.Views.DocumentHolder.txtRowHeight": "Imposta Altezza Riga", "SSE.Views.DocumentHolder.txtScientific": "Scientifico", "SSE.Views.DocumentHolder.txtSelect": "Seleziona", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON> celle in basso", "SSE.Views.DocumentHolder.txtShiftLeft": "Sposta celle a sinistra", "SSE.Views.DocumentHolder.txtShiftRight": "Sposta celle a destra", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON>a celle in alto", "SSE.Views.DocumentHolder.txtShow": "Visualizza", "SSE.Views.DocumentHolder.txtShowComment": "Mostra commento", "SSE.Views.DocumentHolder.txtSort": "Ordina", "SSE.Views.DocumentHolder.txtSortCellColor": "Colore della cella selezionata in cima", "SSE.Views.DocumentHolder.txtSortFontColor": "Colore del carattere selezionato in cima", "SSE.Views.DocumentHolder.txtSparklines": "Sparkline", "SSE.Views.DocumentHolder.txtText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTextAdvanced": "Impostazioni avanzate di paragrafo", "SSE.Views.DocumentHolder.txtTime": "<PERSON>a", "SSE.Views.DocumentHolder.txtUngroup": "Separa", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.vertAlignText": "Allineamento verticale", "SSE.Views.ExternalLinksDlg.closeButtonText": "<PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textSource": "Origine", "SSE.Views.FieldSettingsDialog.strLayout": "Layout di <PERSON>gina", "SSE.Views.FieldSettingsDialog.strSubtotals": "Totali Parziali", "SSE.Views.FieldSettingsDialog.textReport": "Modulo di rapporto", "SSE.Views.FieldSettingsDialog.textTitle": "Campo Impostazioni", "SSE.Views.FieldSettingsDialog.txtAverage": "Media", "SSE.Views.FieldSettingsDialog.txtBlank": "Inser<PERSON>ci righe vuote dopo ogni elemento", "SSE.Views.FieldSettingsDialog.txtBottom": "Mostra in fondo al gruppo", "SSE.Views.FieldSettingsDialog.txtCompact": "Compatta", "SSE.Views.FieldSettingsDialog.txtCount": "Conteggio", "SSE.Views.FieldSettingsDialog.txtCountNums": "Conta numeri", "SSE.Views.FieldSettingsDialog.txtCustomName": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtEmpty": "Mostra elementi senza dati", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Contorno", "SSE.Views.FieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "R<PERSON><PERSON> le etichette degli elementi a ogni riga", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Mostra totali parziali", "SSE.Views.FieldSettingsDialog.txtSourceName": "Nome origine:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Deviazione standard", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Deviazione standard di popolazione", "SSE.Views.FieldSettingsDialog.txtSum": "Somma", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funzioni per totali parziali", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabulare", "SSE.Views.FieldSettingsDialog.txtTop": "Mostra in cima al gruppo", "SSE.Views.FieldSettingsDialog.txtVar": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtVarp": "Varianza di popolazione", "SSE.Views.FileMenu.btnBackCaption": "Apri percorso file", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON>rea una nuova didascalia", "SSE.Views.FileMenu.btnDownloadCaption": "Scarica come", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnFileOpenCaption": "Apr<PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Cronologia delle versioni", "SSE.Views.FileMenu.btnInfoCaption": "Informazioni foglio di calcolo", "SSE.Views.FileMenu.btnPrintCaption": "Stampa", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON>tegg<PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON>i recenti", "SSE.Views.FileMenu.btnRenameCaption": "Rinomina", "SSE.Views.FileMenu.btnReturnCaption": "Torna al foglio di calcolo", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON> di accesso", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON>va come", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Salva copia come", "SSE.Views.FileMenu.btnSettingsCaption": "Impostazioni avanzate", "SSE.Views.FileMenu.btnToEditCaption": "Modifica foglio di calcolo", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Folio di calcolo vuoto", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Creare nuovo", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Applica", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Aggiungi Autore", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Aggiu<PERSON>i testo", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applicazione", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autore", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Modifica diritti di accesso", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Commento", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Ultima modifica di", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Ultima modifica", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Proprietario", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persone che hanno diritti", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Caricato", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Modifica diritti di accesso", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Persone che hanno diritti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Applica", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Modalità di co-editing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Usare il sistema data 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separatore decimale", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Lingua del dizionario", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Rapido", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Suggerimento per i caratteri", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Lingua della Formula", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Esempio: SOMMA; MINIMO; MASSIMO; CONTEGGIO", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorare le parole in MAIUSCOLO", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignorare le parole con i numeri", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Impostazioni macro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Mostra il pulsante opzioni Incolla quando il contenuto viene incollato", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Stile di riferimento R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Impostazioni Regionali", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Esempio: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Mostrare commenti nel foglio", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Mostrare le modifiche degli altri utenti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Mostrare i commenti risolti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Tema dell'interfaccia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Separatore delle migliaia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unità di misura", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Usa i separatori in base alle impostazioni internazionali", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Valore di zoom predefinito", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Ogni 10 minuti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Ogni 30 minuti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Ogni 5 minuti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Ogni ora", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Recupero automatico", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Salvataggio automatico", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Disattivat<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Salvare versioni intermedie", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Ogni minuto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Stile di riferimento", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opzioni di correzione automatica ...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Bielorusso", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgaro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Modalità cache predefinita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Cal<PERSON>li", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimetro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Collaborazione", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Ceco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Tedesco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Modifica e salvataggio", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "‎Greco‎", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Co-editing in tempo reale. <PERSON><PERSON> le modifiche vengono salvate automaticamente", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finlandese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "‎<PERSON><PERSON><PERSON><PERSON>‎", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Pollice", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italiano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "‎Giapponese‎", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "‎<PERSON><PERSON>‎", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "‎Let<PERSON>‎", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "come su OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norvegese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polacco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Correzione", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Punt<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON> (Brasile)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON>gh<PERSON> (Portogallo)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Regione", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "R<PERSON>no", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "<PERSON><PERSON><PERSON> tutto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Abilita tutte le macro senza una notifica", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovacco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Sloveno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON> tutto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Disabilita tutte le macro senza notifica", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Utilizza il pulsante \"Salvare\" per sincronizzare le modifiche apportate da te e da altri", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ucraino", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Utilizzare il tasto Alt per navigare nell'interfaccia utente usando la tastiera", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Utilizzare il tasto Opzione per navigare nell'interfaccia utente usando la tastiera", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Mostra notifica", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Disabilita tutte le macro con notifica", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "come su Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Spazio di lavoro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Cinese", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Avviso", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "con Password", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteggi foglio di calcolo", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "con Firma", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Modifica foglio di calcolo", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "La modifica rimuoverà le firme dal foglio di calcolo.<br>Vuoi continuare?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON><PERSON> foglio di calcolo è protetto con password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Questo foglio di calcolo necessita di essere firmato.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Le firme valide sono state aggiunte al foglio di calcolo. Il foglio di calcolo è protetto dalla modifica.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Alcune delle firme digitali presenti nel foglio di calcolo non sono valide o non possono essere verificate. Il foglio di calcolo è protetto dalla modifica.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Mostra firme", "SSE.Views.FormatRulesEditDlg.fillColor": "Colore di riempimento", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Avviso", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 Scala cromatica", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 Scala cromatica", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON> i bordi", "SSE.Views.FormatRulesEditDlg.textAppearance": "Aspet<PERSON> di <PERSON>ra", "SSE.Views.FormatRulesEditDlg.textApply": "Applicare all'intervallo", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatico", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Direzione di barra", "SSE.Views.FormatRulesEditDlg.textBold": "Grassetto", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "‎Colore bordi‎", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON> bordo", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bordi inferiori", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Impossibile aggiungere formattazione condizionale", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Punto medio di cella", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Bordi interni verticali", "SSE.Views.FormatRulesEditDlg.textClear": "Cancellare", "SSE.Views.FormatRulesEditDlg.textColor": "Colore del testo", "SSE.Views.FormatRulesEditDlg.textContext": "contesto", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON> diagonale discendente", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "<PERSON><PERSON> diagonale ascendente", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Inserisci una formula valida.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "La formula inserita non valuta un numero, una data, un'ora o una stringa.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Inserisci un valore.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Il valore inserito non è un numero, una data, un'ora o una stringa validi.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Il valore per il {0} deve essere maggiore del valore per il {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Inserisci un numero tra {0} e {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Riempire", "SSE.Views.FormatRulesEditDlg.textFormat": "Formato", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradiente", "SSE.Views.FormatRulesEditDlg.textIconLabel": "quando {0} {1} e", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "quando {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "quando valore è", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Sovrapposizione di uno o più intervalli di dati delle icone.<br>A<PERSON><PERSON><PERSON> i valori dell'intervallo di dati delle icone in modo che gli intervalli non si sovrappongano.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Stile di icone", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "<PERSON><PERSON> inter<PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Intervallo di dati non valido", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.FormatRulesEditDlg.textItalic": "Corsivo", "SSE.Views.FormatRulesEditDlg.textItem": "Elemento", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "<PERSON><PERSON> a destra", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON> sin<PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "<PERSON>a più lunga", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "<PERSON>unto massimo", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Bordi interni orizzontali", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Punto medio", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimo", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Punto minimo", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativo", "SSE.Views.FormatRulesEditDlg.textNewColor": "Aggiungi Colore personalizzato", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON> bordi", "SSE.Views.FormatRulesEditDlg.textNone": "niente", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Uno o più dei valori specificati non è una percentuale valida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Il valore specificato non è una percentuale valida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Uno o più dei valori specificati non è una percentuale valida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Il valore specificato {0} non è un percentile valido.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON> esterni", "SSE.Views.FormatRulesEditDlg.textPercent": "Percento", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentile", "SSE.Views.FormatRulesEditDlg.textPosition": "Posizione", "SSE.Views.FormatRulesEditDlg.textPositive": "Positivo", "SSE.Views.FormatRulesEditDlg.textPresets": "Preimpostazioni", "SSE.Views.FormatRulesEditDlg.textPreview": "Anteprima", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Non è possibile utilizzare riferimenti relativi in criteri di formattazione condizionale per scale cromatiche, barre di dati e set di icone.", "SSE.Views.FormatRulesEditDlg.textReverse": "Ordine di icone inverso", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Da destra a sinistra", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "Regola", "SSE.Views.FormatRulesEditDlg.textSameAs": "Uguale a positivo", "SSE.Views.FormatRulesEditDlg.textSelectData": "Selezionare i dati", "SSE.Views.FormatRulesEditDlg.textShortBar": "Barra più breve", "SSE.Views.FormatRulesEditDlg.textShowBar": "<PERSON>rare solo la barra", "SSE.Views.FormatRulesEditDlg.textShowIcon": "<PERSON><PERSON>e solo le icone", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Questo tipo di riferimento non può essere utilizzato in una formula di formattazione condizionale.<br>Cambia il riferimento ad una cella singola o usa il riferimento con una funzione di foglio di calcolo come =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solido", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Barrato", "SSE.Views.FormatRulesEditDlg.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Apice", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Bordo superiore", "SSE.Views.FormatRulesEditDlg.textUnderline": "Sottolineato", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Formato del numero", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Conteggio", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Valuta", "SSE.Views.FormatRulesEditDlg.txtDate": "Data", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Questo campo è obbligatorio", "SSE.Views.FormatRulesEditDlg.txtFraction": "Frazione", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Generale", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON>ess<PERSON> icona", "SSE.Views.FormatRulesEditDlg.txtNumber": "Numero", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percent<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientifico", "SSE.Views.FormatRulesEditDlg.txtText": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON>a", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Modificare la regola di formattazione", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nuova regola di formattazione", "SSE.Views.FormatRulesManagerDlg.guestText": "Ospite", "SSE.Views.FormatRulesManagerDlg.lockText": "Bloccato", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 deviazione standard sopra la media", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 deviazione standard sotto la media", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 deviazioni standard sopra la media", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 deviazioni standard sotto la media", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 deviazioni standard sopra la media", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 deviazioni standard sotto la media", "SSE.Views.FormatRulesManagerDlg.textAbove": "Sopra la media", "SSE.Views.FormatRulesManagerDlg.textApply": "Applica a", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Valore della cella inizia con", "SSE.Views.FormatRulesManagerDlg.textBelow": "Sotto la media", "SSE.Views.FormatRulesManagerDlg.textBetween": "è tra {0} e {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON> della cella", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Scala di colore graduale", "SSE.Views.FormatRulesManagerDlg.textContains": "<PERSON>ore della cella contiene", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cella contiene un valore vuoto", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Cella contiene un errore", "SSE.Views.FormatRulesManagerDlg.textDelete": "Eliminare", "SSE.Views.FormatRulesManagerDlg.textDown": "Spostare la regola verso il basso", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Dup<PERSON><PERSON> valori", "SSE.Views.FormatRulesManagerDlg.textEdit": "Modificare", "SSE.Views.FormatRulesManagerDlg.textEnds": "Valore della cella termina con", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Uguale o superiore alla media", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Uguale o inferiore alla media", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formato", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Set di icone", "SSE.Views.FormatRulesManagerDlg.textNew": "Nuovo", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "non è tra {0} e {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Valore della cella non contiene", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cella non contiene un valore vuoto", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cella non contiene un errore", "SSE.Views.FormatRulesManagerDlg.textRules": "Regole", "SSE.Views.FormatRulesManagerDlg.textScope": "Mostrare le regole di formattazione per", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Selezionare i dati", "SSE.Views.FormatRulesManagerDlg.textSelection": "Selezione corrente", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Questo pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "<PERSON><PERSON> foglio di calcolo", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> tabella", "SSE.Views.FormatRulesManagerDlg.textUnique": "Valori unici", "SSE.Views.FormatRulesManagerDlg.textUp": "Spostare la regola verso l'alto", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Questo elemento si sta modificando da un altro utente.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Formattazione condizionale", "SSE.Views.FormatSettingsDialog.textCategory": "Categoria", "SSE.Views.FormatSettingsDialog.textDecimal": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textFormat": "Formato", "SSE.Views.FormatSettingsDialog.textLinked": "Collegato alla sorgente", "SSE.Views.FormatSettingsDialog.textSeparator": "Usa 1000 separatori", "SSE.Views.FormatSettingsDialog.textSymbols": "Simboli", "SSE.Views.FormatSettingsDialog.textTitle": "Formato numero", "SSE.Views.FormatSettingsDialog.txtAccounting": "Contabilità", "SSE.Views.FormatSettingsDialog.txtAs10": "decimi (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Quarti (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Valuta", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Inserisci attentamente il formato numerico personalizzato. Spreadsheet Editor non controlla potenziali errori al file xlsx causati dai formati personalizzati.", "SSE.Views.FormatSettingsDialog.txtDate": "Data", "SSE.Views.FormatSettingsDialog.txtFraction": "Frazione", "SSE.Views.FormatSettingsDialog.txtGeneral": "Generale", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "Numero", "SSE.Views.FormatSettingsDialog.txtPercentage": "Percent<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtSample": "Campione:", "SSE.Views.FormatSettingsDialog.txtScientific": "Scientifico", "SSE.Views.FormatSettingsDialog.txtText": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON>a", "SSE.Views.FormatSettingsDialog.txtUpto1": "Fino a una cifra (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Fino a due cifre", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON>o a tre cifre (131/135)", "SSE.Views.FormulaDialog.sDescription": "Descrizione", "SSE.Views.FormulaDialog.textGroupDescription": "Seleziona gruppo di funzioni", "SSE.Views.FormulaDialog.textListDescription": "Seleziona funzione", "SSE.Views.FormulaDialog.txtRecommended": "Consigliato", "SSE.Views.FormulaDialog.txtSearch": "Cerca", "SSE.Views.FormulaDialog.txtTitle": "Inserisci funzione", "SSE.Views.FormulaTab.textAutomatic": "Automatico", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calcola il foglio corrente", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calcola cartella di lavoro", "SSE.Views.FormulaTab.textManual": "Manuale", "SSE.Views.FormulaTab.tipCalculate": "Calcola", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calcola l'intera cartella di lavoro", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Somma automatica", "SSE.Views.FormulaTab.txtAutosumTip": "Somma", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Funzione", "SSE.Views.FormulaTab.txtFormulaTip": "Inserisci funzione", "SSE.Views.FormulaTab.txtMore": "Altre funzioni", "SSE.Views.FormulaTab.txtRecent": "Usati di recente", "SSE.Views.FormulaWizard.textAny": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textArgument": "Parametro", "SSE.Views.FormulaWizard.textFunction": "Funzione", "SSE.Views.FormulaWizard.textFunctionRes": "Risultato della funzione", "SSE.Views.FormulaWizard.textHelp": "Aiuto su questa funzione", "SSE.Views.FormulaWizard.textLogical": "logico", "SSE.Views.FormulaWizard.textNoArgs": "Questa funzione non ha argomenti", "SSE.Views.FormulaWizard.textNumber": "numero", "SSE.Views.FormulaWizard.textRef": "riferimento", "SSE.Views.FormulaWizard.textText": "testo", "SSE.Views.FormulaWizard.textTitle": "Argomenti della funzione", "SSE.Views.FormulaWizard.textValue": "Risultato della formula", "SSE.Views.HeaderFooterDialog.textAlign": "Allinea con i margini della pagina", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON> le pagine", "SSE.Views.HeaderFooterDialog.textBold": "Grassetto", "SSE.Views.HeaderFooterDialog.textCenter": "Centrato", "SSE.Views.HeaderFooterDialog.textColor": "Colore del testo", "SSE.Views.HeaderFooterDialog.textDate": "Data", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Diversi per la prima pagina", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Diversi per pagine pari e dispari", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON>gina pari", "SSE.Views.HeaderFooterDialog.textFileName": "Nome file", "SSE.Views.HeaderFooterDialog.textFirst": "Prima pagina", "SSE.Views.HeaderFooterDialog.textFooter": "Piè di pagina", "SSE.Views.HeaderFooterDialog.textHeader": "Intestazione", "SSE.Views.HeaderFooterDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textItalic": "Corsivo", "SSE.Views.HeaderFooterDialog.textLeft": "A sinistra", "SSE.Views.HeaderFooterDialog.textMaxError": "La stringa di testo inserita è troppo lunga. Ridurre il numero di caratteri utilizzati.", "SSE.Views.HeaderFooterDialog.textNewColor": "Aggiungi Colore personalizzato", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON>a dispari", "SSE.Views.HeaderFooterDialog.textPageCount": "Conteggio pagine", "SSE.Views.HeaderFooterDialog.textPageNum": "Numero di pagina", "SSE.Views.HeaderFooterDialog.textPresets": "Preimpostati", "SSE.Views.HeaderFooterDialog.textRight": "A destra", "SSE.Views.HeaderFooterDialog.textScale": "Scala con il documento", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textStrikeout": "Barrato", "SSE.Views.HeaderFooterDialog.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSuperscript": "Apice", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON>a", "SSE.Views.HeaderFooterDialog.textTitle": "Impostazioni intestazione/piè di pagina", "SSE.Views.HeaderFooterDialog.textUnderline": "Sottolineato", "SSE.Views.HeaderFooterDialog.tipFontName": "Tipo di carattere", "SSE.Views.HeaderFooterDialog.tipFontSize": "Dimensione carattere", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Visualizza", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Collega a", "SSE.Views.HyperlinkSettingsDialog.strRange": "Intervallo", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copia", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Intervallo selezionato", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> did<PERSON>calia qui", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON><PERSON> collegamento qui", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserisci descrizione comando qui", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Collegamento esterno", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "<PERSON><PERSON><PERSON> colle<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Intervallo di dati interno", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON> definiti", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Seleziona dati", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Testo del suggerimento", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Impostazioni collegamento ipertestuale", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Campo obbligatorio", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Il formato URL richiesto è \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Questo campo è limitato a 2083 caratteri", "SSE.Views.ImageSettings.textAdvanced": "Mostra impostazioni avanzate", "SSE.Views.ImageSettings.textCrop": "Rita<PERSON>", "SSE.Views.ImageSettings.textCropFill": "Riempimento", "SSE.Views.ImageSettings.textCropFit": "Adatta", "SSE.Views.ImageSettings.textCropToShape": "Ritaglia forma", "SSE.Views.ImageSettings.textEdit": "Modifica", "SSE.Views.ImageSettings.textEditObject": "Modifica oggetto", "SSE.Views.ImageSettings.textFlip": "Capovolgi", "SSE.Views.ImageSettings.textFromFile": "Da file", "SSE.Views.ImageSettings.textFromStorage": "Da spazio di archiviazione", "SSE.Views.ImageSettings.textFromUrl": "Da URL", "SSE.Views.ImageSettings.textHeight": "Altezza", "SSE.Views.ImageSettings.textHint270": "Ruota 90° a sinistra", "SSE.Views.ImageSettings.textHint90": "Ruota 90° a destra", "SSE.Views.ImageSettings.textHintFlipH": "Capovolgi orizzontalmente", "SSE.Views.ImageSettings.textHintFlipV": "Capovolgi verticalmente", "SSE.Views.ImageSettings.textInsert": "Sostituisci immagine", "SSE.Views.ImageSettings.textKeepRatio": "Proporzioni costanti", "SSE.Views.ImageSettings.textOriginalSize": "Dimensione reale", "SSE.Views.ImageSettings.textRecentlyUsed": "Usati di recente", "SSE.Views.ImageSettings.textRotate90": "Ruota di 90°", "SSE.Views.ImageSettings.textRotation": "Rotazione", "SSE.Views.ImageSettings.textSize": "Dimensione", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Non spostare o ridimensionare con le celle", "SSE.Views.ImageSettingsAdvanced.textAlt": "Testo alternativo", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Descrizione", "SSE.Views.ImageSettingsAdvanced.textAltTip": "La rappresentazione alternativa testuale basata delle informazioni sull'oggetto visivo, che verrà letta alle persone con disabilità visive o cognitive per aiutarle a capire meglio quali informazioni ci sono nell'immagine, nella forma automatica, nel grafico o nella tabella.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Capovolto", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Orizzontalmente", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Sposta ma non ridimensionare con le celle", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotazione", "SSE.Views.ImageSettingsAdvanced.textSnap": "Aggancia celle", "SSE.Views.ImageSettingsAdvanced.textTitle": "Immagine - Impostazioni avanzate", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Sposta e ridimensiona con le celle", "SSE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.LeftMenu.tipAbout": "Informazioni su", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Commenti", "SSE.Views.LeftMenu.tipFile": "File", "SSE.Views.LeftMenu.tipPlugins": "Plugin", "SSE.Views.LeftMenu.tipSearch": "Cerca", "SSE.Views.LeftMenu.tipSpellcheck": "Controllo ortografico", "SSE.Views.LeftMenu.tipSupport": "Feedback & Supporto", "SSE.Views.LeftMenu.txtDeveloper": "MODALITÀ SVILUPPATORE", "SSE.Views.LeftMenu.txtEditor": "Editor di fogli di calcolo", "SSE.Views.LeftMenu.txtLimit": "‎<PERSON><PERSON>‎", "SSE.Views.LeftMenu.txtTrial": "Modalità di prova", "SSE.Views.LeftMenu.txtTrialDev": "Prova Modalità sviluppatore", "SSE.Views.MacroDialog.textMacro": "Nome di macro", "SSE.Views.MacroDialog.textTitle": "Assegnare macro", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "In basso", "SSE.Views.MainSettingsPrint.strLandscape": "Orizzontale", "SSE.Views.MainSettingsPrint.strLeft": "A sinistra", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Verticale", "SSE.Views.MainSettingsPrint.strPrint": "Stampa", "SSE.Views.MainSettingsPrint.strPrintTitles": "Stampa titoli", "SSE.Views.MainSettingsPrint.strRight": "A destra", "SSE.Views.MainSettingsPrint.strTop": "In alto", "SSE.Views.MainSettingsPrint.textActualSize": "Dimensione reale", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "Opzioni personalizzate", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON> tutte le colonne in una pagina", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON> foglio in una pagina", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON> tutte le righe in una pagina", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientamento pagina", "SSE.Views.MainSettingsPrint.textPageScaling": "Ridimensionamento", "SSE.Views.MainSettingsPrint.textPageSize": "Dimensione pagina", "SSE.Views.MainSettingsPrint.textPrintGrid": "Stampa griglia", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Stampa intestazioni di riga e colonna", "SSE.Views.MainSettingsPrint.textRepeat": "R<PERSON>eti...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "R<PERSON><PERSON> colonne a sinistra", "SSE.Views.MainSettingsPrint.textRepeatTop": "<PERSON><PERSON><PERSON> righe in alto", "SSE.Views.MainSettingsPrint.textSettings": "Impostazioni per", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Gli intervalli denominati esistenti non possono essere modificati e quelli nuovi non possono essere creati<br>al momento alcuni di essi sono in fase di modifica.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defined name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Avviso", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Cartella di lavoro", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textExistName": "Errore! Intervallo con tale nome esiste già", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Il nome deve iniziare con una lettera o un underscore e non deve contenere caratteri non validi.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.NamedRangeEditDlg.textIsLocked": "Errore! Questo elemento viene modificato da un altro utente.", "SSE.Views.NamedRangeEditDlg.textName": "Name", "SSE.Views.NamedRangeEditDlg.textReservedName": "Il nome che stai cercando di utilizzare è già riferito nella cella formule. Prego rinominare", "SSE.Views.NamedRangeEditDlg.textScope": "Ambito", "SSE.Views.NamedRangeEditDlg.textSelectData": "Seleziona dati", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Campo obbligatorio", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Modifica nome", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nuovo nome", "SSE.Views.NamedRangePasteDlg.textNames": "Intervalli denominati", "SSE.Views.NamedRangePasteDlg.txtTitle": "Incolla nome", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Guest", "SSE.Views.NameManagerDlg.lockText": "Bloccato", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDelete": "Elimina", "SSE.Views.NameManagerDlg.textEdit": "Modifica", "SSE.Views.NameManagerDlg.textEmpty": "Non sono ancora stati creati intervalli con nome.<br><PERSON>rea almeno un intervallo con nome e apparirà in questo campo.", "SSE.Views.NameManagerDlg.textFilter": "Filtro", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON> definiti", "SSE.Views.NameManagerDlg.textFilterSheet": "Nomi inseriti nel foglio", "SSE.Views.NameManagerDlg.textFilterTableNames": "Nomi delle tabelle", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Nomi inseriti nella cartella di lavoro", "SSE.Views.NameManagerDlg.textNew": "Nuovo", "SSE.Views.NameManagerDlg.textnoNames": "Non è stato possibile trovare intervalli denominati corrispondenti al filtro.", "SSE.Views.NameManagerDlg.textRanges": "Intervalli denominati", "SSE.Views.NameManagerDlg.textScope": "Ambito", "SSE.Views.NameManagerDlg.textWorkbook": "Cartella di lavoro", "SSE.Views.NameManagerDlg.tipIsLocked": "Questo elemento viene modificato da un altro utente.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON>est<PERSON> nomi", "SSE.Views.NameManagerDlg.warnDelete": "Sei sicuro di voler eliminare il nome {0}?", "SSE.Views.PageMarginsDialog.textBottom": "In basso", "SSE.Views.PageMarginsDialog.textLeft": "A sinistra", "SSE.Views.PageMarginsDialog.textRight": "A destra", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "In alto", "SSE.Views.ParagraphSettings.strLineHeight": "Interlinea", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Spaziatura del paragrafo", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "Prima", "SSE.Views.ParagraphSettings.textAdvanced": "Mostra impostazioni avanzate", "SSE.Views.ParagraphSettings.textAt": "A", "SSE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAuto": "Multipla", "SSE.Views.ParagraphSettings.textExact": "Esat<PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Le schede specificate appariranno in questo campo", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Barrato do<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "R<PERSON>ri", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "A sinistra", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interlinea", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "A destra", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Prima", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciale", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "per", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Tipo di carattere", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Rientri e spaziatura", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Spaziatura", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Barrato", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Apice", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Schede", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Allineamento", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multipla", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Spaziatura caratteri", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Scheda predefinita", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Esatto", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prima riga", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Sospensione", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Giustificato", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nessuna)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Elimina", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON> tutto", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Specifica", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centrato", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "A sinistra", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posizione scheda", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "A destra", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragrafo - Impostazioni avanzate", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "uguali", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "non finisce con", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "contiene", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "non contiene", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "tra", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "non tra", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "non è uguale", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "è superiore a", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "è superiore o uguale a", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "è inferiore a", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "è inferiore o uguale a", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "inizia con", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "non inizia con", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "finisce con", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Mostra gli elementi per i quali l'etichetta:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Mostra elementi per i quali:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Utilizza ? per sostituire un singolo carattere", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Utilizza * per sostituire più caratteri", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "e", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtro etichetta", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON> filtro", "SSE.Views.PivotGroupDialog.textAuto": "Automatico", "SSE.Views.PivotGroupDialog.textBy": "Di", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "<PERSON><PERSON><PERSON> alle", "SSE.Views.PivotGroupDialog.textError": "Questo campo deve essere un valore numerico", "SSE.Views.PivotGroupDialog.textGreaterError": "Il numero finale deve essere maggiore del numero iniziale", "SSE.Views.PivotGroupDialog.textHour": "Ore", "SSE.Views.PivotGroupDialog.textMin": "Minuti", "SSE.Views.PivotGroupDialog.textMonth": "Me<PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "Numero di giorni", "SSE.Views.PivotGroupDialog.textQuart": "Quarti", "SSE.Views.PivotGroupDialog.textSec": "Secondi", "SSE.Views.PivotGroupDialog.textStart": "A partire da", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Raggruppa", "SSE.Views.PivotSettings.textAdvanced": "Mostra impostazioni avanzate", "SSE.Views.PivotSettings.textColumns": "Colonne", "SSE.Views.PivotSettings.textFields": "Seleziona campi", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Valori", "SSE.Views.PivotSettings.txtAddColumn": "Aggiungi alle colonne", "SSE.Views.PivotSettings.txtAddFilter": "Aggiungi ai filtri", "SSE.Views.PivotSettings.txtAddRow": "Aggiungi alle righe", "SSE.Views.PivotSettings.txtAddValues": "Aggiungi ai valori", "SSE.Views.PivotSettings.txtFieldSettings": "Campo Impostazioni", "SSE.Views.PivotSettings.txtMoveBegin": "Muovi all'inizio", "SSE.Views.PivotSettings.txtMoveColumn": "Sposta in colonne", "SSE.Views.PivotSettings.txtMoveDown": "Sposta verso il basso", "SSE.Views.PivotSettings.txtMoveEnd": "<PERSON><PERSON>i alla fine", "SSE.Views.PivotSettings.txtMoveFilter": "Sposta in filtri", "SSE.Views.PivotSettings.txtMoveRow": "Sposta in righe", "SSE.Views.PivotSettings.txtMoveUp": "Sposta verso l'alto", "SSE.Views.PivotSettings.txtMoveValues": "Sposta in valori", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON> campo", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nome e layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Testo alternativo", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Descrizione", "SSE.Views.PivotSettingsAdvanced.textAltTip": "La rappresentazione alternativa del testo delle informazioni riguardanti gli oggetti visivi, che verrà letta alle persone con deficit visivi o cognitivi per aiutarli a comprendere meglio quali informazioni sono contenute nell'immagine, nella forma, nel grafico o nella tabella.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Origine dati", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Visualizza i campi nell'area filtro riportata", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON><PERSON>, poi sopra", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Totali complessivi", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Campo Intestazioni", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.PivotSettingsAdvanced.textOver": "Sopra, poi giù", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Seleziona dati", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Mostra per colonne", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Mostra il campo intestazioni per righe e colonne", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Mostra per righe", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tabella pivot - Impostazioni avanzate", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Segnala campi filtro per colonna", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Segnala campi filtro per riga", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Campo obbligatorio", "SSE.Views.PivotSettingsAdvanced.txtName": "Nome", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON> vuote", "SSE.Views.PivotTable.capGrandTotals": "Totali complessivi", "SSE.Views.PivotTable.capLayout": "Layout del rapporto", "SSE.Views.PivotTable.capSubtotals": "Totali Parziali", "SSE.Views.PivotTable.mniBottomSubtotals": "Mostra tutti i totali parziali nella parte inferiore del gruppo", "SSE.Views.PivotTable.mniInsertBlankLine": "Inserisci riga vuota dopo ogni elemento", "SSE.Views.PivotTable.mniLayoutCompact": "Mostra in forma compatta", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Non ripetere tutte le etichette degli elementi", "SSE.Views.PivotTable.mniLayoutOutline": "Visualizza sotto forma di struttura", "SSE.Views.PivotTable.mniLayoutRepeat": "<PERSON><PERSON><PERSON> tutte le etichette degli elementi", "SSE.Views.PivotTable.mniLayoutTabular": "Mostra in forma tabulare", "SSE.Views.PivotTable.mniNoSubtotals": "Non mostrare Totali Parziali", "SSE.Views.PivotTable.mniOffTotals": "Disattivo per le righe e le colonne", "SSE.Views.PivotTable.mniOnColumnsTotals": "Attivo solo per le colonne", "SSE.Views.PivotTable.mniOnRowsTotals": "Attivo solo per le righe", "SSE.Views.PivotTable.mniOnTotals": "Attivo per le righe e le colonne", "SSE.Views.PivotTable.mniRemoveBlankLine": "Rimuovi riga vuota dopo ogni elemento", "SSE.Views.PivotTable.mniTopSubtotals": "Mostra tutti i totali parziali in cima al gruppo", "SSE.Views.PivotTable.textColBanded": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textColHeader": "Intestazioni Colonna", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON> rag<PERSON>", "SSE.Views.PivotTable.textRowHeader": "Intestazioni di riga", "SSE.Views.PivotTable.tipCreatePivot": "<PERSON>ser<PERSON><PERSON> tabella pivot", "SSE.Views.PivotTable.tipGrandTotals": "Mostra o Nascondi somma globale", "SSE.Views.PivotTable.tipRefresh": "Aggiorna le informazioni dall'origine dati", "SSE.Views.PivotTable.tipSelect": "<PERSON><PERSON><PERSON>na tutta la tabella pivot", "SSE.Views.PivotTable.tipSubtotals": "Mostra o nascondi i totali parziali", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON><PERSON> tabella", "SSE.Views.PivotTable.txtGroupPivot_Custom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Dark": "<PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Light": "Chiaro", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medio", "SSE.Views.PivotTable.txtPivotTable": "<PERSON>bella pivot", "SSE.Views.PivotTable.txtRefresh": "Aggiorna", "SSE.Views.PivotTable.txtSelect": "Seleziona", "SSE.Views.PrintSettings.btnDownload": "Salva e scarica", "SSE.Views.PrintSettings.btnPrint": "Salva e stampa", "SSE.Views.PrintSettings.strBottom": "In basso", "SSE.Views.PrintSettings.strLandscape": "Orizzontale", "SSE.Views.PrintSettings.strLeft": "A sinistra", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Verticale", "SSE.Views.PrintSettings.strPrint": "Stampa", "SSE.Views.PrintSettings.strPrintTitles": "Stampa titoli", "SSE.Views.PrintSettings.strRight": "A destra", "SSE.Views.PrintSettings.strShow": "Visualizza", "SSE.Views.PrintSettings.strTop": "In alto", "SSE.Views.PrintSettings.textActualSize": "Dimensione reale", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON> i <PERSON>", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON>og<PERSON>", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "Opzioni personalizzate", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON> tutte le colonne in una pagina", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON> foglio in una pagina", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON> tutte le righe in una pagina", "SSE.Views.PrintSettings.textHideDetails": "Nascondi dettagli", "SSE.Views.PrintSettings.textIgnore": "Ignora area di stampa", "SSE.Views.PrintSettings.textLayout": "Layout di <PERSON>gina", "SSE.Views.PrintSettings.textPageOrientation": "Orientamento pagina", "SSE.Views.PrintSettings.textPageScaling": "Ridimensionamento", "SSE.Views.PrintSettings.textPageSize": "Dimensione pagina", "SSE.Views.PrintSettings.textPrintGrid": "Stampa griglia", "SSE.Views.PrintSettings.textPrintHeadings": "Stampa intestazioni di riga e colonna", "SSE.Views.PrintSettings.textPrintRange": "Intervallo di stampa", "SSE.Views.PrintSettings.textRange": "Intervallo", "SSE.Views.PrintSettings.textRepeat": "R<PERSON>eti...", "SSE.Views.PrintSettings.textRepeatLeft": "R<PERSON><PERSON> colonne a sinistra", "SSE.Views.PrintSettings.textRepeatTop": "<PERSON><PERSON><PERSON> righe in alto", "SSE.Views.PrintSettings.textSelection": "Selezione", "SSE.Views.PrintSettings.textSettings": "Impostazioni Foglio", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "Mostra griglia", "SSE.Views.PrintSettings.textShowHeadings": "Mostra intestazioni di righe e colonne", "SSE.Views.PrintSettings.textTitle": "Impostazioni stampa", "SSE.Views.PrintSettings.textTitlePDF": "Impostazioni PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "Prima colonna", "SSE.Views.PrintTitlesDialog.textFirstRow": "Prima riga", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON> bloc<PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON> bloc<PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.PrintTitlesDialog.textLeft": "R<PERSON><PERSON> colonne a sinistra", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Non ripetere", "SSE.Views.PrintTitlesDialog.textRepeat": "R<PERSON>eti...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Seleziona intervallo", "SSE.Views.PrintTitlesDialog.textTitle": "Stampa titoli", "SSE.Views.PrintTitlesDialog.textTop": "<PERSON><PERSON><PERSON> righe in alto", "SSE.Views.PrintWithPreview.txtActualSize": "Predefinita", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON> i <PERSON>", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Applica a tutti i fogli", "SSE.Views.PrintWithPreview.txtBottom": "In basso", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON>og<PERSON>", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "Opzioni personalizzate", "SSE.Views.PrintWithPreview.txtEmptyTable": "Non c'è niente da stampare perché la tabella è vuota", "SSE.Views.PrintWithPreview.txtFitCols": "<PERSON><PERSON> tutte le colonne in una pagina", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON><PERSON> foglio in una pagina", "SSE.Views.PrintWithPreview.txtFitRows": "<PERSON><PERSON> tutte le righe in una pagina", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Linee griglia e intestazioni", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Impostazioni intestazione/piè di pagina", "SSE.Views.PrintWithPreview.txtIgnore": "Ignora area di stampa", "SSE.Views.PrintWithPreview.txtLandscape": "Panorama", "SSE.Views.PrintWithPreview.txtLeft": "A sinistra", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "di {0}", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Numero pagina non valido", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientamento pagina", "SSE.Views.PrintWithPreview.txtPageSize": "Dimensione pagina", "SSE.Views.PrintWithPreview.txtPortrait": "Verticale", "SSE.Views.PrintWithPreview.txtPrint": "Stampa", "SSE.Views.PrintWithPreview.txtPrintGrid": "Stampa griglia", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Stampa intestazioni di riga e colonna", "SSE.Views.PrintWithPreview.txtPrintRange": "Area di stampa", "SSE.Views.PrintWithPreview.txtPrintTitles": "Stampa titoli", "SSE.Views.PrintWithPreview.txtRepeat": "R<PERSON>eti...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "R<PERSON><PERSON> colonne a sinistra", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "<PERSON><PERSON><PERSON> righe in alto", "SSE.Views.PrintWithPreview.txtRight": "A destra", "SSE.Views.PrintWithPreview.txtSave": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtScaling": "Ridimensionamento", "SSE.Views.PrintWithPreview.txtSelection": "Selezione", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Impostazioni foglio", "SSE.Views.PrintWithPreview.txtSheet": "Foglio: {0}", "SSE.Views.PrintWithPreview.txtTop": "In alto", "SSE.Views.ProtectDialog.textExistName": "ERRORE! Un intervallo con un tale titolo già esiste", "SSE.Views.ProtectDialog.textInvalidName": "Il titolo dell'intervallo deve iniziare con una lettera e può contenere solo lettere, numeri e spazi.", "SSE.Views.ProtectDialog.textInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.ProtectDialog.textSelectData": "Selezionare i dati", "SSE.Views.ProtectDialog.txtAllow": "Permettere a tutti gli utenti del foglio di", "SSE.Views.ProtectDialog.txtAutofilter": "Usare filtro automatico", "SSE.Views.ProtectDialog.txtDelCols": "Eliminare colonne", "SSE.Views.ProtectDialog.txtDelRows": "Eliminare righe", "SSE.Views.ProtectDialog.txtEmpty": "Questo campo è obbligatorio", "SSE.Views.ProtectDialog.txtFormatCells": "Formattare celle", "SSE.Views.ProtectDialog.txtFormatCols": "Formattare colonne", "SSE.Views.ProtectDialog.txtFormatRows": "Formattare righe", "SSE.Views.ProtectDialog.txtIncorrectPwd": "La password di conferma non corrisponde", "SSE.Views.ProtectDialog.txtInsCols": "Inserire colonne", "SSE.Views.ProtectDialog.txtInsHyper": "Inserire collegamento ipertestuale", "SSE.Views.ProtectDialog.txtInsRows": "Inserire righe", "SSE.Views.ProtectDialog.txtObjs": "Modificare oggetti", "SSE.Views.ProtectDialog.txtOptional": "opzionale", "SSE.Views.ProtectDialog.txtPassword": "Password", "SSE.Views.ProtectDialog.txtPivot": "Usare tabella pivot e grafico pivot", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Intervallo", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "Ripetere password", "SSE.Views.ProtectDialog.txtScen": "Modificare scenari", "SSE.Views.ProtectDialog.txtSelLocked": "Selezionare celle bloccate", "SSE.Views.ProtectDialog.txtSelUnLocked": "Selezionare celle sbloccate", "SSE.Views.ProtectDialog.txtSheetDescription": "Impedisci cambiamenti indesiderati da altri limitando la loro capacità di modifica.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSort": "Ordinare", "SSE.Views.ProtectDialog.txtWarning": "Attenzione: una volta persa o dimenticata, la password non potrà più essere recuperata. Ti preghiamo di conservala in un luogo sicuro.", "SSE.Views.ProtectDialog.txtWBDescription": "Per impedire ad altri utenti di visualizzare fogli di calcolo nascosti, agg<PERSON><PERSON><PERSON>, spostare, eliminare o nascondere fogli di calcolo e rinominare fogli di calcolo, è possibile proteggere la struttura del libro di lavoro con una password.", "SSE.Views.ProtectDialog.txtWBTitle": "Proteggere la struttura del libro di lavoro", "SSE.Views.ProtectRangesDlg.guestText": "Ospite", "SSE.Views.ProtectRangesDlg.lockText": "Bloccato", "SSE.Views.ProtectRangesDlg.textDelete": "Elimina", "SSE.Views.ProtectRangesDlg.textEdit": "Modifica", "SSE.Views.ProtectRangesDlg.textEmpty": "Nessun intervallo consentito per la modifica.", "SSE.Views.ProtectRangesDlg.textNew": "Nuovo", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textPwd": "Password", "SSE.Views.ProtectRangesDlg.textRange": "Intervallo", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Intervalli del foglio protetto apribili con password (funziona solo per le celle bloccate)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Questo elemento si sta modificando da un altro utente.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Modificare intervallo", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nuovo intervallo", "SSE.Views.ProtectRangesDlg.txtNo": "No", "SSE.Views.ProtectRangesDlg.txtTitle": "Permettere agli utenti di cambiare gli intervalli", "SSE.Views.ProtectRangesDlg.txtYes": "Sì", "SSE.Views.ProtectRangesDlg.warnDelete": "Sei sicuro che vuoi eliminare il nome {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Colonne", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Per eliminare i valori duplicati, selezionare una o più colonne che contengono duplicati.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "I miei dati hanno intestazioni", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Se<PERSON><PERSON>na tutto", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtCellSettings": "impostazioni cella", "SSE.Views.RightMenu.txtChartSettings": "Impostazioni grafico", "SSE.Views.RightMenu.txtImageSettings": "Impostazioni immagine", "SSE.Views.RightMenu.txtParagraphSettings": "Impostazioni di paragrafo", "SSE.Views.RightMenu.txtPivotSettings": "Impostazioni tabella Pivot", "SSE.Views.RightMenu.txtSettings": "Impostazioni generali", "SSE.Views.RightMenu.txtShapeSettings": "Impostazioni forma", "SSE.Views.RightMenu.txtSignatureSettings": "Impostazioni della Firma", "SSE.Views.RightMenu.txtSlicerSettings": "Impostazioni filtro dati", "SSE.Views.RightMenu.txtSparklineSettings": "Impostazioni Sparkline", "SSE.Views.RightMenu.txtTableSettings": "Impostazioni tabella", "SSE.Views.RightMenu.txtTextArtSettings": "Impostazioni Text Art", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "Il valore inserito non è corretto.", "SSE.Views.ScaleDialog.textFewPages": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Adatta a", "SSE.Views.ScaleDialog.textHeight": "Altezza", "SSE.Views.ScaleDialog.textManyPages": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Scala a", "SSE.Views.ScaleDialog.textTitle": "Impostazioni di scala", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "Il valore massimo per questo campo è {0}", "SSE.Views.SetValueDialog.txtMinText": "Il valore minimo per questo campo è {0}", "SSE.Views.ShapeSettings.strBackground": "Colore sfondo", "SSE.Views.ShapeSettings.strChange": "Modifica forma automatica", "SSE.Views.ShapeSettings.strColor": "Colore", "SSE.Views.ShapeSettings.strFill": "Riempimento", "SSE.Views.ShapeSettings.strForeground": "Colore primo piano", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "Mostra ombra", "SSE.Views.ShapeSettings.strSize": "Dimensione", "SSE.Views.ShapeSettings.strStroke": "Linea", "SSE.Views.ShapeSettings.strTransparency": "Opacità", "SSE.Views.ShapeSettings.strType": "Tipo", "SSE.Views.ShapeSettings.textAdvanced": "Mostra impostazioni avanzate", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 0 pt e 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Colore di riempimento", "SSE.Views.ShapeSettings.textDirection": "Direzione", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "Capovolgi", "SSE.Views.ShapeSettings.textFromFile": "Da file", "SSE.Views.ShapeSettings.textFromStorage": "Da spazio di archiviazione", "SSE.Views.ShapeSettings.textFromUrl": "Da URL", "SSE.Views.ShapeSettings.textGradient": "Punti di sfumatura", "SSE.Views.ShapeSettings.textGradientFill": "Riempimento sfumato", "SSE.Views.ShapeSettings.textHint270": "Ruota 90° a sinistra", "SSE.Views.ShapeSettings.textHint90": "Ruota 90° a destra", "SSE.Views.ShapeSettings.textHintFlipH": "Capovolgi orizzontalmente", "SSE.Views.ShapeSettings.textHintFlipV": "Capovolgi verticalmente", "SSE.Views.ShapeSettings.textImageTexture": "Immagine o trama", "SSE.Views.ShapeSettings.textLinear": "Lineare", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textOriginalSize": "Dimensione originale", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Posizione", "SSE.Views.ShapeSettings.textRadial": "Radiale", "SSE.Views.ShapeSettings.textRecentlyUsed": "Usati di recente", "SSE.Views.ShapeSettings.textRotate90": "Ruota di 90°", "SSE.Views.ShapeSettings.textRotation": "Rotazione", "SSE.Views.ShapeSettings.textSelectImage": "Seleziona immagine", "SSE.Views.ShapeSettings.textSelectTexture": "Seleziona", "SSE.Views.ShapeSettings.textStretch": "Estendi", "SSE.Views.ShapeSettings.textStyle": "Stile", "SSE.Views.ShapeSettings.textTexture": "Da trama", "SSE.Views.ShapeSettings.textTile": "Tegola", "SSE.Views.ShapeSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON>ta da pacchi", "SSE.Views.ShapeSettings.txtCanvas": "Tela", "SSE.Views.ShapeSettings.txtCarton": "Cartone", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON> scuro", "SSE.Views.ShapeSettings.txtGrain": "Grano", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON>ta grigia", "SSE.Views.ShapeSettings.txtKnit": "A maglia", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "Nessuna linea", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Colonne", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Spaziatura interna testo", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Non spostare o ridimensionare con le celle", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Testo alternativo", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrizione", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "La rappresentazione alternativa testuale basata delle informazioni sull'oggetto visivo, che verrà letta alle persone con disabilità visive o cognitive per aiutarle a capire meglio quali informazioni ci sono nell'immagine, nella forma automatica, nel grafico o nella tabella.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Adatta", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Dimensioni inizio", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON> inizio", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Smussat<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "In basso", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Tipo estremità", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Numero di colonne", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Dimensione finale", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Stile finale", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Uniforme", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Capovolto", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Altezza", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Orizzontalmente", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo giunzione", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporzioni costanti", "SSE.Views.ShapeSettingsAdvanced.textLeft": "A sinistra", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Stile linea", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Acuto", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Sposta ma non ridimensionare con le celle", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Consentire al testo di eccedere la forma", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Ridimensiona forma per adattarla al testo", "SSE.Views.ShapeSettingsAdvanced.textRight": "A destra", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotazione", "SSE.Views.ShapeSettingsAdvanced.textRound": "Rotondo", "SSE.Views.ShapeSettingsAdvanced.textSize": "Dimensione", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Aggancia celle", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "spaziatura fra le colonne", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Quadrato", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Casella di testo", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma - Impostazioni avanzate", "SSE.Views.ShapeSettingsAdvanced.textTop": "In alto", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Sposta e ridimensiona con le celle", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Spessori e frecce", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Avviso", "SSE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDetails": "Dettagli firma", "SSE.Views.SignatureSettings.strInvalid": "Firme non valide", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Impostazioni firma", "SSE.Views.SignatureSettings.strSign": "Firma", "SSE.Views.SignatureSettings.strSignature": "Firma", "SSE.Views.SignatureSettings.strSigner": "Firmatario", "SSE.Views.SignatureSettings.strValid": "Firme valide", "SSE.Views.SignatureSettings.txtContinueEditing": "Modifica comunque", "SSE.Views.SignatureSettings.txtEditWarning": "La modifica rimuoverà le firme dal foglio di calcolo.<br>Vuoi continuare?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Vuoi rimuovere questa firma?<br>Non può essere annullata.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Questo foglio di calcolo necessita di essere firmato.", "SSE.Views.SignatureSettings.txtSigned": "Le firme valide sono state aggiunte al foglio di calcolo. Il foglio di calcolo è protetto dalla modifica.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Alcune delle firme digitali presenti nel foglio di calcolo non sono valide o non possono essere verificate. Il foglio di calcolo è protetto dalla modifica.", "SSE.Views.SlicerAddDialog.textColumns": "Colonne", "SSE.Views.SlicerAddDialog.txtTitle": "Inserisci filtri dati", "SSE.Views.SlicerSettings.strHideNoData": "Nascondi elementi senza dati", "SSE.Views.SlicerSettings.strIndNoData": "Indica visivamente gli elementi senza dati", "SSE.Views.SlicerSettings.strShowDel": "Mostra gli elementi eliminati dall'origine dati", "SSE.Views.SlicerSettings.strShowNoData": "Mostra gli elementi senza dati per ultimi", "SSE.Views.SlicerSettings.strSorting": "Ordinamento e filtraggio", "SSE.Views.SlicerSettings.textAdvanced": "Mostra impostazioni avanzate", "SSE.Views.SlicerSettings.textAsc": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textAZ": "dalla A alla Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "Colonne", "SSE.Views.SlicerSettings.textDesc": "Decrescente", "SSE.Views.SlicerSettings.textHeight": "Altezza", "SSE.Views.SlicerSettings.textHor": "Orizzontale", "SSE.Views.SlicerSettings.textKeepRatio": "Proporzioni costanti", "SSE.Views.SlicerSettings.textLargeSmall": "dal più grande al più piccolo", "SSE.Views.SlicerSettings.textLock": "Disabilita ridimensionamento o spostamento", "SSE.Views.SlicerSettings.textNewOld": "dal più nuovo al più vecchio", "SSE.Views.SlicerSettings.textOldNew": "dal più vecchio al più nuovo", "SSE.Views.SlicerSettings.textPosition": "Posizione", "SSE.Views.SlicerSettings.textSize": "Dimensione", "SSE.Views.SlicerSettings.textSmallLarge": "dal più piccolo al più grande", "SSE.Views.SlicerSettings.textStyle": "Stile", "SSE.Views.SlicerSettings.textVert": "Verticale", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "dalla Z alla A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Colonne", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Altezza", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Nascondi elementi senza dati", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Indica visivamente gli elementi senza dati", "SSE.Views.SlicerSettingsAdvanced.strReferences": "R<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Mostra gli elementi eliminati dall'origine dati", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Visualizza intestazione", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Mostra gli elementi senza dati per ultimi", "SSE.Views.SlicerSettingsAdvanced.strSize": "Dimensione", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Ordinamento e filtraggio", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stile", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Stile e dimensione", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Non spostare o ridimensionare con le celle", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Testo alternativo", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Descrizione", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "La rappresentazione alternativa del testo delle informazioni riguardanti gli oggetti visivi, che verrà letta alle persone con deficit visivi o cognitivi per aiutarli a comprendere meglio quali informazioni sono contenute nell'immagine, nella forma, nel grafico o nella tabella.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAZ": "dalla A alla Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Decrescente", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Nome da utilizzare nelle formule", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Intestazione", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Proporzioni costanti", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "dal più grande al più piccolo", "SSE.Views.SlicerSettingsAdvanced.textName": "Nome", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "dal più nuovo al più vecchio", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "dal più vecchio al più nuovo", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Sposta ma non ridimensionare con le celle", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "dal più piccolo al più grande", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Aggancia celle", "SSE.Views.SlicerSettingsAdvanced.textSort": "Ordina", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Nome origine", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Filtro dati - Impostazioni avanzate", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Sposta e ridimensiona con le celle", "SSE.Views.SlicerSettingsAdvanced.textZA": "dalla Z alla A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Campo obbligatorio", "SSE.Views.SortDialog.errorEmpty": "Tutti i criteri di ordinamento devono avere una colonna o una riga specificata.", "SSE.Views.SortDialog.errorMoreOneCol": "È stata selezionata più di una colonna.", "SSE.Views.SortDialog.errorMoreOneRow": "È stata selezionata più di una riga.", "SSE.Views.SortDialog.errorNotOriginalCol": "La colonna selezionata non si trova nell'intervallo selezionato originale.", "SSE.Views.SortDialog.errorNotOriginalRow": "La riga selezionata non si trova nell'intervallo selezionato originale.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 viene ordinato per lo stesso colore più di una volta.<br>Elimina i criteri di ordinamento duplicati e riprova.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 viene ordinato per valore più di una volta.<br>Elimina i criteri di ordinamento duplicati e riprova.", "SSE.Views.SortDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON> livello", "SSE.Views.SortDialog.textAsc": "<PERSON><PERSON>", "SSE.Views.SortDialog.textAuto": "Automatico", "SSE.Views.SortDialog.textAZ": "dalla A alla Z", "SSE.Views.SortDialog.textBelow": "sotto", "SSE.Views.SortDialog.textCellColor": "colore Cella", "SSE.Views.SortDialog.textColumn": "<PERSON>onna", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON> livello", "SSE.Views.SortDialog.textDelete": "<PERSON><PERSON> livello", "SSE.Views.SortDialog.textDesc": "Decrescente", "SSE.Views.SortDialog.textDown": "<PERSON><PERSON><PERSON> livello in basso", "SSE.Views.SortDialog.textFontColor": "Colore del carattere", "SSE.Views.SortDialog.textLeft": "A sinistra", "SSE.Views.SortDialog.textMoreCols": "(Altre colonne ...)", "SSE.Views.SortDialog.textMoreRows": "(Altre righe ...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Opzioni", "SSE.Views.SortDialog.textOrder": "Ordina", "SSE.Views.SortDialog.textRight": "A destra", "SSE.Views.SortDialog.textRow": "Riga", "SSE.Views.SortDialog.textSort": "Ordina per", "SSE.Views.SortDialog.textSortBy": "Ordina per", "SSE.Views.SortDialog.textThenBy": "<PERSON><PERSON> da", "SSE.Views.SortDialog.textTop": "In alto", "SSE.Views.SortDialog.textUp": "<PERSON><PERSON><PERSON> livello in alto", "SSE.Views.SortDialog.textValues": "Valori", "SSE.Views.SortDialog.textZA": "dalla Z alla A", "SSE.Views.SortDialog.txtInvalidRange": "Intervallo celle non valido.", "SSE.Views.SortDialog.txtTitle": "Ordina", "SSE.Views.SortFilterDialog.textAsc": "Ascendente (dalla A alla Z) di", "SSE.Views.SortFilterDialog.textDesc": "Decrescente (dalla Z alla A) di", "SSE.Views.SortFilterDialog.txtTitle": "Ordina", "SSE.Views.SortOptionsDialog.textCase": "Sensibile al maiuscolo/minuscolo", "SSE.Views.SortOptionsDialog.textHeaders": "I miei dati hanno intestazioni", "SSE.Views.SortOptionsDialog.textLeftRight": "Ordina da sinistra a destra", "SSE.Views.SortOptionsDialog.textOrientation": "Orientamento", "SSE.Views.SortOptionsDialog.textTitle": "Opzioni di ordinamento", "SSE.Views.SortOptionsDialog.textTopBottom": "Ordina dall'alto verso il basso", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "Salta gli spazi vuoti", "SSE.Views.SpecialPasteDialog.textColWidth": "Larghezze colonna", "SSE.Views.SpecialPasteDialog.textComments": "Commenti", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formule e formattazione", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formule e formati numerici", "SSE.Views.SpecialPasteDialog.textFormats": "Formati", "SSE.Views.SpecialPasteDialog.textFormulas": "Formule", "SSE.Views.SpecialPasteDialog.textFWidth": "Formule e larghezze di colonna", "SSE.Views.SpecialPasteDialog.textMult": "Moltiplicazione", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operazione", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "Sottrai", "SSE.Views.SpecialPasteDialog.textTitle": "Incolla speciale", "SSE.Views.SpecialPasteDialog.textTranspose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textValues": "Valori", "SSE.Views.SpecialPasteDialog.textVFormat": "Valori e formattazione", "SSE.Views.SpecialPasteDialog.textVNFormat": "Valori e formati numerici", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON><PERSON> tranne i bordi", "SSE.Views.Spellcheck.noSuggestions": "<PERSON><PERSON>un suggerimento ortografico", "SSE.Views.Spellcheck.textChange": "Cambia", "SSE.Views.Spellcheck.textChangeAll": "Cambia tutto", "SSE.Views.Spellcheck.textIgnore": "Ignora", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON> tutto", "SSE.Views.Spellcheck.txtAddToDictionary": "Aggiungi al dizionario", "SSE.Views.Spellcheck.txtClosePanel": "Chiudere il correttore ortografico", "SSE.Views.Spellcheck.txtComplete": "Il controllo ortografico è stato completato", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Lingua del dizionario", "SSE.Views.Spellcheck.txtNextTip": "Vai alla prossima parola", "SSE.Views.Spellcheck.txtSpelling": "Ortografia", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON> alla fine)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Sposta alla fine)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Incolla prima del foglio", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Sposta prima del foglio", "SSE.Views.Statusbar.filteredRecordsText": "{0} di {1} record filtrati", "SSE.Views.Statusbar.filteredText": "Modalità filtro", "SSE.Views.Statusbar.itemAverage": "Media", "SSE.Views.Statusbar.itemCopy": "Copia", "SSE.Views.Statusbar.itemCount": "Conteggio", "SSE.Views.Statusbar.itemDelete": "Elimina", "SSE.Views.Statusbar.itemHidden": "Nascosto", "SSE.Views.Statusbar.itemHide": "Nascondi", "SSE.Views.Statusbar.itemInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimo", "SSE.Views.Statusbar.itemMove": "Sposta", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "Rinomina", "SSE.Views.Statusbar.itemStatus": "Stato di salvataggio", "SSE.Views.Statusbar.itemSum": "Somma", "SSE.Views.Statusbar.itemTabColor": "Colore scheda", "SSE.Views.Statusbar.itemUnProtect": "Rimuovere la protezione", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Un foglio di lavoro con questo nome esiste già.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Il nome foglio non può contenere i seguenti caratteri: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON>", "SSE.Views.Statusbar.selectAllSheets": "Seleziona tutti i fogli", "SSE.Views.Statusbar.sheetIndexText": "<PERSON><PERSON><PERSON> {0} di {1}", "SSE.Views.Statusbar.textAverage": "Media", "SSE.Views.Statusbar.textCount": "Conteggio", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Aggiungi Colore personalizzato", "SSE.Views.Statusbar.textNoColor": "Nessun colore", "SSE.Views.Statusbar.textSum": "Somma", "SSE.Views.Statusbar.tipAddTab": "Aggiungi foglio di lavoro", "SSE.Views.Statusbar.tipFirst": "Scorri verso il primo foglio", "SSE.Views.Statusbar.tipLast": "Scorri verso l'ultimo foglio", "SSE.Views.Statusbar.tipListOfSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON> elenco fogli a destra", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON> elenco fogli a sinistra", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "Zoom avanti", "SSE.Views.Statusbar.tipZoomOut": "Zoom indietro", "SSE.Views.Statusbar.ungroupSheets": "Separa i fogli", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Impossibile eseguire l'operazione sull'intervallo celle selezionato.<br>Selezionare un intervallo di dati uniforme diverso da quello esistente e riprovare.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Impossibile completare l'operazione per l'intervallo di celle selezionato.<br> Seleziona un intervallo in modo che la prima riga della tabella si trovi sulla stessa riga <br> e la tabella risultante si sovrapponga a quella corrente.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Impossibile completare l'operazione per l'intervallo di celle selezionato.<br>Selezionare un intervallo che non include altre tabelle.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Le formule di matrice multi-cella non sono consentite nelle tabelle.", "SSE.Views.TableOptionsDialog.txtEmpty": "Campo obbligatorio", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON> tabella", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ERRORE! Intervallo di celle non valido", "SSE.Views.TableOptionsDialog.txtNote": "Le intestazioni devono rimanere nella stessa riga e l'intervallo di tabelle risultante deve sovrapporsi all'intervallo di tabella originale.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "Elimina colonna", "SSE.Views.TableSettings.deleteRowText": "Elimina riga", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON> tabella", "SSE.Views.TableSettings.insertColumnLeftText": "Inserisci colonna a sinistra", "SSE.Views.TableSettings.insertColumnRightText": "Inserisci colonna a destra", "SSE.Views.TableSettings.insertRowAboveText": "Inserisci riga sopra", "SSE.Views.TableSettings.insertRowBelowText": "Inser<PERSON>ci riga sotto", "SSE.Views.TableSettings.notcriticalErrorTitle": "Avviso", "SSE.Views.TableSettings.selectColumnText": "Seleziona colonna intera", "SSE.Views.TableSettings.selectDataText": "Seleziona dati della colonna ", "SSE.Views.TableSettings.selectRowText": "Seleziona riga", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabella", "SSE.Views.TableSettings.textActions": "Azioni tabella", "SSE.Views.TableSettings.textAdvanced": "Mostra impostazioni avanzate", "SSE.Views.TableSettings.textBanded": "A strisce", "SSE.Views.TableSettings.textColumns": "Colonne", "SSE.Views.TableSettings.textConvertRange": "Converti in intervallo", "SSE.Views.TableSettings.textEdit": "Righe e colonne", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textExistName": "ERRORE! Esiste già un intervallo con questo nome", "SSE.Views.TableSettings.textFilter": "Pulsante Filtro", "SSE.Views.TableSettings.textFirst": "Primo", "SSE.Views.TableSettings.textHeader": "Intestazione", "SSE.Views.TableSettings.textInvalidName": "ERRORE! Nome tabella non valido", "SSE.Views.TableSettings.textIsLocked": "Questo elemento viene modificato da un altro utente.", "SSE.Views.TableSettings.textLast": "Ultimo", "SSE.Views.TableSettings.textLongOperation": "Operazione lunga", "SSE.Views.TableSettings.textPivot": "<PERSON>ser<PERSON><PERSON> tabella pivot", "SSE.Views.TableSettings.textRemDuplicates": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textReservedName": "Il nome che stai cercando di utilizzare è già riferito nella cella formule. Prego rinominare", "SSE.Views.TableSettings.textResize": "R<PERSON><PERSON><PERSON><PERSON><PERSON> tabella", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Seleziona dati", "SSE.Views.TableSettings.textSlicer": "Inserisci filtro dati", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON>", "SSE.Views.TableSettings.textTemplate": "Seleziona da modello", "SSE.Views.TableSettings.textTotal": "Totale", "SSE.Views.TableSettings.warnLongOperation": "L'operazione che stai per intraprendere potrebbe richiedere molto tempo per essere completata.<br>Vuoi continuare?", "SSE.Views.TableSettingsAdvanced.textAlt": "Testo alternativo", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Descrizione", "SSE.Views.TableSettingsAdvanced.textAltTip": "La rappresentazione alternativa del testo delle informazioni riguardanti gli oggetti visivi, che verrà letta alle persone con deficit visivi o cognitivi per aiutarli a comprendere meglio quali informazioni sono contenute nell'immagine, nella forma, nel grafico o nella tabella.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabella - Impostazioni avanzate", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "Chiaro", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "Medio", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "Stile tabella scuro", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "Stile tabella chiaro", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "Stile tabella medio", "SSE.Views.TextArtSettings.strBackground": "Colore sfondo", "SSE.Views.TextArtSettings.strColor": "Colore", "SSE.Views.TextArtSettings.strFill": "Riempimento", "SSE.Views.TextArtSettings.strForeground": "Colore primo piano", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "Dimensione", "SSE.Views.TextArtSettings.strStroke": "Linea", "SSE.Views.TextArtSettings.strTransparency": "Opacità", "SSE.Views.TextArtSettings.strType": "Tipo", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 0 pt e 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Colore di riempimento", "SSE.Views.TextArtSettings.textDirection": "Direzione", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromFile": "From File", "SSE.Views.TextArtSettings.textFromUrl": "Da URL", "SSE.Views.TextArtSettings.textGradient": "Punti di sfumatura", "SSE.Views.TextArtSettings.textGradientFill": "Riempimento sfumato", "SSE.Views.TextArtSettings.textImageTexture": "Immagine o trama", "SSE.Views.TextArtSettings.textLinear": "Lineare", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Posizione", "SSE.Views.TextArtSettings.textRadial": "Radiale", "SSE.Views.TextArtSettings.textSelectTexture": "Seleziona", "SSE.Views.TextArtSettings.textStretch": "Estendi", "SSE.Views.TextArtSettings.textStyle": "Stile", "SSE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTexture": "Da trama", "SSE.Views.TextArtSettings.textTile": "Tegola", "SSE.Views.TextArtSettings.textTransform": "Trasformazione", "SSE.Views.TextArtSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON>ta da pacchi", "SSE.Views.TextArtSettings.txtCanvas": "Tela", "SSE.Views.TextArtSettings.txtCarton": "Cartone", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON> scuro", "SSE.Views.TextArtSettings.txtGrain": "Grano", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON>ta grigia", "SSE.Views.TextArtSettings.txtKnit": "A maglia", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "Nessuna linea", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Aggiungi commento", "SSE.Views.Toolbar.capBtnColorSchemas": "Schema di colori", "SSE.Views.Toolbar.capBtnComment": "Commento", "SSE.Views.Toolbar.capBtnInsHeader": "Intestazione/Piè di pagina", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON><PERSON> dati", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Simbolo", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientamento", "SSE.Views.Toolbar.capBtnPageSize": "Dimensione", "SSE.Views.Toolbar.capBtnPrintArea": "Area di stampa", "SSE.Views.Toolbar.capBtnPrintTitles": "Stampa titoli", "SSE.Views.Toolbar.capBtnScale": "Adatta", "SSE.Views.Toolbar.capImgAlign": "Allinea", "SSE.Views.Toolbar.capImgBackward": "Porta indietro", "SSE.Views.Toolbar.capImgForward": "Porta avanti", "SSE.Views.Toolbar.capImgGroup": "Raggruppa", "SSE.Views.Toolbar.capInsertChart": "Grafico", "SSE.Views.Toolbar.capInsertEquation": "Equazione", "SSE.Views.Toolbar.capInsertHyperlink": "Collegamento ipertestuale", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Grafico sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Casella di testo", "SSE.Views.Toolbar.mniImageFromFile": "Imma<PERSON>e da file", "SSE.Views.Toolbar.mniImageFromStorage": "Immagine da spazio di archiviazione", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON><PERSON> da URL", "SSE.Views.Toolbar.textAddPrintArea": "Aggiungi all'area di stampa", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON>a in basso", "SSE.Views.Toolbar.textAlignCenter": "Allinea al centro", "SSE.Views.Toolbar.textAlignJust": "Giustificato", "SSE.Views.Toolbar.textAlignLeft": "Allinea a sinistra", "SSE.Views.Toolbar.textAlignMiddle": "Allinea in mezzo", "SSE.Views.Toolbar.textAlignRight": "Allinea a destra", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON>a in alto", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON> i bordi", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatico", "SSE.Views.Toolbar.textBold": "Grassetto", "SSE.Views.Toolbar.textBordersColor": "Colore bordo", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON> bordo", "SSE.Views.Toolbar.textBottom": "In basso: ", "SSE.Views.Toolbar.textBottomBorders": "Bordi inferiori", "SSE.Views.Toolbar.textCenterBorders": "Bordi verticali interni", "SSE.Views.Toolbar.textClearPrintArea": "Pulisci area di stampa", "SSE.Views.Toolbar.textClearRule": "Cancellare le regole", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON> in senso orario", "SSE.Views.Toolbar.textColorScales": "Scale cromatiche", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON> in senso antiorario", "SSE.Views.Toolbar.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDataBars": "Barre di dati", "SSE.Views.Toolbar.textDelLeft": "Sposta celle a sinistra", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON>a celle in alto", "SSE.Views.Toolbar.textDiagDownBorder": "Bordo diagonale inferiore", "SSE.Views.Toolbar.textDiagUpBorder": "Bordo diagonale superiore", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "Modificare area visibile", "SSE.Views.Toolbar.textEntireCol": "Colonna intera", "SSE.Views.Toolbar.textEntireRow": "Riga intera", "SSE.Views.Toolbar.textFewPages": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHeight": "Altezza", "SSE.Views.Toolbar.textHideVA": "Nascondere area visibile", "SSE.Views.Toolbar.textHorizontal": "Testo <PERSON>", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON> celle in basso", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON> inter<PERSON>", "SSE.Views.Toolbar.textInsRight": "Sposta celle a destra", "SSE.Views.Toolbar.textItalic": "Corsivo", "SSE.Views.Toolbar.textItems": "elementi", "SSE.Views.Toolbar.textLandscape": "Orizzontale", "SSE.Views.Toolbar.textLeft": "Sinistra:", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON> sin<PERSON>", "SSE.Views.Toolbar.textManageRule": "Gestire le regole", "SSE.Views.Toolbar.textManyPages": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Ultima personalizzazione", "SSE.Views.Toolbar.textMarginsNarrow": "Stret<PERSON>", "SSE.Views.Toolbar.textMarginsNormal": "Normale", "SSE.Views.Toolbar.textMarginsWide": "Ampio", "SSE.Views.Toolbar.textMiddleBorders": "Bordi orizzontali interni", "SSE.Views.Toolbar.textMoreFormats": "Altri formati", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON> pagine", "SSE.Views.Toolbar.textNewColor": "Aggiungi Colore personalizzato", "SSE.Views.Toolbar.textNewRule": "Nuova regola", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON> bordo", "SSE.Views.Toolbar.textOnePage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON> esterni", "SSE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPortrait": "Verticale", "SSE.Views.Toolbar.textPrint": "Stampa", "SSE.Views.Toolbar.textPrintGridlines": "Stampa griglia", "SSE.Views.Toolbar.textPrintHeadings": "Stampa intestazioni", "SSE.Views.Toolbar.textPrintOptions": "Impostazioni stampa", "SSE.Views.Toolbar.textRight": "Destra:", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Ruota testo verso il basso", "SSE.Views.Toolbar.textRotateUp": "Ruota testo verso l'alto", "SSE.Views.Toolbar.textScale": "Ridimensiona", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSelection": "Dalla selezione corrente", "SSE.Views.Toolbar.textSetPrintArea": "Imposta area di stampa", "SSE.Views.Toolbar.textShowVA": "Mostrare area visibile", "SSE.Views.Toolbar.textStrikeout": "Barrato", "SSE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubSuperscript": "Pedice/Apice", "SSE.Views.Toolbar.textSuperscript": "Apice", "SSE.Views.Toolbar.textTabCollaboration": "Collaborazione", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "File", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "Home", "SSE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabLayout": "Layout di <PERSON>gina", "SSE.Views.Toolbar.textTabProtect": "Protezione", "SSE.Views.Toolbar.textTabView": "Visualizza", "SSE.Views.Toolbar.textThisPivot": "Da questo pivot", "SSE.Views.Toolbar.textThisSheet": "Da questo foglio di calcolo", "SSE.Views.Toolbar.textThisTable": "Da questa tabella", "SSE.Views.Toolbar.textTop": "In alto:", "SSE.Views.Toolbar.textTopBorders": "Bordi superiori", "SSE.Views.Toolbar.textUnderline": "Sottolineato", "SSE.Views.Toolbar.textVertical": "Testo verticale", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON>a in basso", "SSE.Views.Toolbar.tipAlignCenter": "Allinea al centro", "SSE.Views.Toolbar.tipAlignJust": "Giustificato", "SSE.Views.Toolbar.tipAlignLeft": "Allinea a sinistra", "SSE.Views.Toolbar.tipAlignMiddle": "Allinea in mezzo", "SSE.Views.Toolbar.tipAlignRight": "Allinea a destra", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON>a in alto", "SSE.Views.Toolbar.tipAutofilter": "Ordina e filtra", "SSE.Views.Toolbar.tipBack": "Indietro", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Stile cella", "SSE.Views.Toolbar.tipChangeChart": "Cambia tipo di grafico", "SSE.Views.Toolbar.tipClearStyle": "Svuota", "SSE.Views.Toolbar.tipColorSchemas": "Cambia combinazione colori", "SSE.Views.Toolbar.tipCondFormat": "Formattazione condizionale", "SSE.Views.Toolbar.tipCopy": "Copia", "SSE.Views.Toolbar.tipCopyStyle": "Copia stile", "SSE.Views.Toolbar.tipCut": "Tagliare", "SSE.Views.Toolbar.tipDecDecimal": "Diminuisci decimali", "SSE.Views.Toolbar.tipDecFont": "Riduci dimensione caratteri", "SSE.Views.Toolbar.tipDeleteOpt": "Elimina celle", "SSE.Views.Toolbar.tipDigStyleAccounting": "Stile di contabilità", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON>ile valuta", "SSE.Views.Toolbar.tipDigStylePercent": "Stile percentuale", "SSE.Views.Toolbar.tipEditChart": "Modifica grafico", "SSE.Views.Toolbar.tipEditChartData": "Seleziona dati", "SSE.Views.Toolbar.tipEditChartType": "Cambia tipo di grafico", "SSE.Views.Toolbar.tipEditHeader": "Modifica intestazione o piè di pagina", "SSE.Views.Toolbar.tipFontColor": "Colore del carattere", "SSE.Views.Toolbar.tipFontName": "Tipo di carattere", "SSE.Views.Toolbar.tipFontSize": "Dimensione carattere", "SSE.Views.Toolbar.tipHAlighOle": "Allineamento orizzontale", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipImgGroup": "Raggruppa oggetti", "SSE.Views.Toolbar.tipIncDecimal": "Aumenta decimali", "SSE.Views.Toolbar.tipIncFont": "Aumenta dimensione caratteri ", "SSE.Views.Toolbar.tipInsertChart": "Inserisci grafico", "SSE.Views.Toolbar.tipInsertChartSpark": "Inserisci Grafico o Sparkline", "SSE.Views.Toolbar.tipInsertEquation": "Inserisci equazione", "SSE.Views.Toolbar.tipInsertHyperlink": "Aggiungi collegamento ipertestuale", "SSE.Views.Toolbar.tipInsertImage": "Inserisci immagine", "SSE.Views.Toolbar.tipInsertOpt": "Inserisci celle", "SSE.Views.Toolbar.tipInsertShape": "Inserisci forma automatica", "SSE.Views.Toolbar.tipInsertSlicer": "Inserisci filtro dati", "SSE.Views.Toolbar.tipInsertSpark": "Inserire grafico sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Inserisci simbolo", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON><PERSON> tabella", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON> casella di testo", "SSE.Views.Toolbar.tipInsertTextart": "Inserisci Text Art", "SSE.Views.Toolbar.tipMerge": "Unisci e centra", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "Formato numero", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON>i della pagina", "SSE.Views.Toolbar.tipPageOrient": "Orientamento pagina", "SSE.Views.Toolbar.tipPageSize": "Dimensione pagina", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "Colore di riempimento", "SSE.Views.Toolbar.tipPrint": "Stampa", "SSE.Views.Toolbar.tipPrintArea": "Area di stampa", "SSE.Views.Toolbar.tipPrintTitles": "Stampa titoli", "SSE.Views.Toolbar.tipRedo": "R<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "Salva i tuoi cambiamenti per renderli disponibili agli altri utenti.", "SSE.Views.Toolbar.tipScale": "Adatta", "SSE.Views.Toolbar.tipSelectAll": "Se<PERSON><PERSON><PERSON><PERSON> tutto", "SSE.Views.Toolbar.tipSendBackward": "Porta indietro", "SSE.Views.Toolbar.tipSendForward": "Porta avanti", "SSE.Views.Toolbar.tipSynchronize": "Il documento è stato modificato da un altro utente. Clicca per salvare le modifiche e ricaricare gli aggiornamenti.", "SSE.Views.Toolbar.tipTextFormatting": "Più strumenti per la formattazione del testo", "SSE.Views.Toolbar.tipTextOrientation": "Orientamento", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Allineamento verticale", "SSE.Views.Toolbar.tipVisibleArea": "Area visibile", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON> testo", "SSE.Views.Toolbar.txtAccounting": "Contabilità", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "Somma", "SSE.Views.Toolbar.txtCellStyle": "Stile cella", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Commenti", "SSE.Views.Toolbar.txtClearFilter": "Svuota filtro", "SSE.Views.Toolbar.txtClearFormat": "Formato", "SSE.Views.Toolbar.txtClearFormula": "Funzione", "SSE.Views.Toolbar.txtClearHyper": "Collegamenti ipertestuali", "SSE.Views.Toolbar.txtClearText": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCurrency": "Valuta", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "Data", "SSE.Views.Toolbar.txtDateTime": "Data e ora", "SSE.Views.Toolbar.txtDescending": "Decrescente", "SSE.Views.Toolbar.txtDollar": "$ Dollaro", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Esponenziale", "SSE.Views.Toolbar.txtFilter": "Filtro", "SSE.Views.Toolbar.txtFormula": "Inserisci funzione", "SSE.Views.Toolbar.txtFraction": "Frazione", "SSE.Views.Toolbar.txtFranc": "CHF Franco svizzero", "SSE.Views.Toolbar.txtGeneral": "Generale", "SSE.Views.Toolbar.txtInteger": "Numero intero", "SSE.Views.Toolbar.txtManageRange": "<PERSON>est<PERSON> nomi", "SSE.Views.Toolbar.txtMergeAcross": "Unisci in ciascuna riga", "SSE.Views.Toolbar.txtMergeCells": "Unisci celle", "SSE.Views.Toolbar.txtMergeCenter": "Unisci e centra", "SSE.Views.Toolbar.txtNamedRange": "Intervalli denominati", "SSE.Views.Toolbar.txtNewRange": "Definisci nome", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "SSE.Views.Toolbar.txtNumber": "Numero", "SSE.Views.Toolbar.txtPasteRange": "Incolla nome", "SSE.Views.Toolbar.txtPercentage": "Percent<PERSON><PERSON>", "SSE.Views.Toolbar.txtPound": "£ Sterlina britannica", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON>lo", "SSE.Views.Toolbar.txtScheme1": "Ufficio", "SSE.Views.Toolbar.txtScheme10": "Luna", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme14": "Oriel", "SSE.Views.Toolbar.txtScheme15": "Satellite", "SSE.Views.Toolbar.txtScheme16": "Carta", "SSE.Views.Toolbar.txtScheme17": "Solstizio", "SSE.Views.Toolbar.txtScheme18": "Tecnologia", "SSE.Views.Toolbar.txtScheme19": "Terra", "SSE.Views.Toolbar.txtScheme2": "Scala di grigi", "SSE.Views.Toolbar.txtScheme20": "Tramonto", "SSE.Views.Toolbar.txtScheme21": "Verve", "SSE.Views.Toolbar.txtScheme22": "Nuovo ufficio", "SSE.Views.Toolbar.txtScheme3": "Apice", "SSE.Views.Toolbar.txtScheme4": "Astro", "SSE.Views.Toolbar.txtScheme5": "Città", "SSE.Views.Toolbar.txtScheme6": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme7": "Azionario", "SSE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme9": "Galassia", "SSE.Views.Toolbar.txtScientific": "Scientifico", "SSE.Views.Toolbar.txtSearch": "Cerca", "SSE.Views.Toolbar.txtSort": "Ordina", "SSE.Views.Toolbar.txtSortAZ": "Ordine crescente", "SSE.Views.Toolbar.txtSortZA": "Ordine decrescente", "SSE.Views.Toolbar.txtSpecial": "Speciale", "SSE.Views.Toolbar.txtTableTemplate": "Formatta come modello di tabella", "SSE.Views.Toolbar.txtText": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtTime": "<PERSON>a", "SSE.Views.Toolbar.txtUnmerge": "Dividi celle", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "Visualizza", "SSE.Views.Top10FilterDialog.txtBottom": "In basso", "SSE.Views.Top10FilterDialog.txtBy": "di", "SSE.Views.Top10FilterDialog.txtItems": "Elemento", "SSE.Views.Top10FilterDialog.txtPercent": "Percento", "SSE.Views.Top10FilterDialog.txtSum": "Somma", "SSE.Views.Top10FilterDialog.txtTitle": "Filtra automaticamente i primi 10", "SSE.Views.Top10FilterDialog.txtTop": "In alto", "SSE.Views.Top10FilterDialog.txtValueTitle": "Primi 10 filtri", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Impostazioni campo valore", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Media", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Campo base", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Oggetto base", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 di %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Conteggio", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Conta numeri", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "La differenza da", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Indice", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON><PERSON> cal<PERSON>lo", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Percentuale di", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Differenza percentuale da", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Percentuale di colonna", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Percentuale del totale", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Percentuale di riga", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Esegui il totale in", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "<PERSON>ra valori come", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Nome origine:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Deviazione standard", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Deviazione standard di popolazione", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Somma", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Riepiloga campo valore per", "SSE.Views.ValueFieldSettingsDialog.txtVar": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "Varianza di popolazione", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Ospite", "SSE.Views.ViewManagerDlg.lockText": "Bloccato", "SSE.Views.ViewManagerDlg.textDelete": "Elimina", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplica", "SSE.Views.ViewManagerDlg.textEmpty": "Non è stata ancora creata alcuna vista.", "SSE.Views.ViewManagerDlg.textGoTo": "Vai alla vista", "SSE.Views.ViewManagerDlg.textLongName": "Inserisci un nome da meno di 128 caratteri", "SSE.Views.ViewManagerDlg.textNew": "Nuovo", "SSE.Views.ViewManagerDlg.textRename": "Rinomina", "SSE.Views.ViewManagerDlg.textRenameError": "Il nome della vista non può essere vuoto.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Rinomina vista", "SSE.Views.ViewManagerDlg.textViews": "Visualizzazioni foglio", "SSE.Views.ViewManagerDlg.tipIsLocked": "Questo elemento è in fase di modifica da parte di un altro utente.", "SSE.Views.ViewManagerDlg.txtTitle": "Manager visual<PERSON><PERSON><PERSON>lio", "SSE.Views.ViewManagerDlg.warnDeleteView": "Stai tentando di eliminare la vista attualmente abilitata '%1'.<br>Desideri chiudere questa visualizzazione ed eliminarla?", "SSE.Views.ViewTab.capBtnFreeze": "Blocca riquad<PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "Visualizzazione foglio", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Mostra sempre barra degli strumenti ", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combinare le barre di foglio e di stato", "SSE.Views.ViewTab.textCreate": "Nuovo", "SSE.Views.ViewTab.textDefault": "Predefinito", "SSE.Views.ViewTab.textFormula": "Barra della formula", "SSE.Views.ViewTab.textFreezeCol": "Blocca prima colonna", "SSE.Views.ViewTab.textFreezeRow": "Blocca riga superiore", "SSE.Views.ViewTab.textGridlines": "Linee griglia", "SSE.Views.ViewTab.textHeadings": "Intestazioni", "SSE.Views.ViewTab.textInterfaceTheme": "Tema dell'interfaccia", "SSE.Views.ViewTab.textManager": "Visualizza la gestione", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Mostra l'ombra dei riquadri bloccati", "SSE.Views.ViewTab.textUnFreeze": "Sblocca riquadri", "SSE.Views.ViewTab.textZeros": "<PERSON>ra zeri", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Chiudi visualizzazione foglio", "SSE.Views.ViewTab.tipCreate": "Crea vista del foglio", "SSE.Views.ViewTab.tipFreeze": "Blocca riquad<PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "Tema dell'interfaccia", "SSE.Views.ViewTab.tipSheetView": "Visualizzazione foglio", "SSE.Views.WatchDialog.closeButtonText": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textCell": "Cella", "SSE.Views.WatchDialog.textDeleteAll": "<PERSON><PERSON> tutto", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Nome", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "Valore", "SSE.Views.WBProtection.hintAllowRanges": "Permettere di cambiare gli intervalli", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectWB": "Proteggere libro di lavoro", "SSE.Views.WBProtection.txtAllowRanges": "Permettere di cambiare gli intervalli", "SSE.Views.WBProtection.txtHiddenFormula": "Formule nascoste", "SSE.Views.WBProtection.txtLockedCell": "Cella bloccata", "SSE.Views.WBProtection.txtLockedShape": "Forma bloccata", "SSE.Views.WBProtection.txtLockedText": "<PERSON><PERSON> testo", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtProtectWB": "Proteggere libro di lavoro", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Inserisci una password per rimuovere la protezione del foglio", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Rimuovere la protezione del foglio", "SSE.Views.WBProtection.txtWBUnlockDescription": "Inserisci una password per rimuovere la protezione del libro di lavoro", "SSE.Views.WBProtection.txtWBUnlockTitle": "Rimuovere la protezione del libro di lavoro"}