﻿<!DOCTYPE html>
<html>
	<head>
		<title>Formatos compatibles de presentaciones electrónicas</title>
		<meta charset="utf-8" />
		<meta name="description" content="The list of presentation formats supported by Presentation Editor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Formatos compatibles de presentaciones electrónicas</h1>
			<p>La presentación es un conjunto de diapositivas que puede incluir distintos tipos de contenido por como imágenes, archivos multimedia, texto, efectos etc. <b>El editor de presentaciones</b> es compatible con los siguientes formatos de presentaciones:</p>
            <p class="note">Mientras usted sube o abra el archivo para edición, se convertirá al formato Office Open XML (PPTX). Se hace para acelerar el procesamiento de archivos y aumentar la interoperabilidad.</p>
            <p>La siguiente tabla contiene los formatos que pueden abrirse para su visualización y/o edición.</p>
            <table>
				<tr>
					<td><b>Formatos</b></td>
					<td><b>Descripción</b></td>
					<td>Ver de forma nativa</td>
                    <td>Ver después de la conversión a OOXML</td>
					<td>Editar de forma nativa</td>
                    <td>Editar después de la conversión a OOXML</td>
				</tr>
                <tr>
                    <td>ODP</td>
                    <td>Presentación OpenDocument<br /> El formato que representa un documento de presentación creado por la aplicación Impress, que es parte de las oficinas suites basadas en OpenOffice</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>OTP</td>
                    <td>Plantilla de presentaciones OpenDocument<br />Formato de archivo OpenDocument para plantillas de presentación. Una plantilla OTP contiene ajustes de formato o estilos, entre otros y se puede usar para crear múltiples presentaciones con el mismo formato.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>POTX</td>
                    <td>Plantilla de documento PowerPoint Open XML<br />Formato de archivo comprimido, basado en XML, desarrollado por Microsoft para plantillas de presentación. Una plantilla DOTX contiene ajustes de formato o estilos, entre otros y se puede usar para crear múltiples presentaciones con el mismo formato.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PPSX</td>
                    <td>Microsoft PowerPoint Slide Show<br />Formato de archivo de presentación utilizado para la reproducción de presentaciones de diapositivas</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PPT</td>
                    <td>Formato de archivo usado por Microsoft PowerPoint</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
                    <td>PPTX</td>
					<td>Presentación Office Open XML<br /> El formato de archivo comprimido, basado en XML desarrollado por Microsoft para representación de hojas de cálculo, gráficos, presentaciones, y documentos de procesamiento de texto</td>
					<td>+</td>
                    <td></td>
					<td>+</td>
                    <td></td>
				</tr>
			</table>
            <p>La siguiente tabla contiene los formatos en los que se puede descargar una presentación desde el menú <b>Archivo</b> -> <b>Descargar como</b>.</p>
            <table>
                <tr>
                    <td><b>Formato de entrada</b></td>
                    <td><b>Puede descargarse como</b></td>
                </tr>
                <tr>
                    <td>ODP</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX</td>
                </tr>
                <tr>
                    <td>OTP</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX</td>
                </tr>
                <tr>
                    <td>POTX</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX</td>
                </tr>
                <tr>
                    <td>PPSX</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX</td>
                </tr>
                <tr>
                    <td>PPT</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX</td>
                </tr>
                <tr>
                    <td>PPTX</td>
                    <td>JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX</td>
                </tr>
            </table>
            <p>También puede consultar la matriz de conversión en la página web <a href="https://api.onlyoffice.com/editors/conversionapi#presentation-matrix" target="_blank" onclick="onhyperlinkclick(this)"><b>api.onlyoffice.com</b></a> para ver la posibilidad de convertir sus presentaciones a los formatos de archivo más conocidos.</p>
		</div>
	</body>
</html>