﻿<!DOCTYPE html>
<html>
	<head>
		<title>BRTEILJAHRE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>BRTEILJAHRE-Funktion</h1>
			<p>Die Funktion <b>BRTEILJAHRE</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie wandelt die Anzahl der ganzen Tage zwischen Ausgangsdatum und Enddatum in Bruchteile von Jahren um.</p>
			<p>Die Formelsyntax der Funktion <b>BRTEILJAHRE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>BRTEILJAHRE(Ausgangsdatum;Enddatum;[Basis])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Ausgangsdatum</em></b> ist eine Zahl, die das erste Datum des Zeitraums darstellt, das mit der Funktion <a href="Date.htm" onclick="onhyperlinkclick(this)">Datum</a> oder einer anderen Datums- und Uhrzeitfunktion eingegeben wurde.</p>
			<p style="text-indent: 50px;"><b><em>Enddatum</em></b> ist eine Zahl, die das letzte Datum des Zeitraums darstellt, das mit der Funktion <a href="Date.htm" onclick="onhyperlinkclick(this)">Datum</a> oder einer anderen Datums- und Uhrzeitfunktion eingegeben wurde.</p>
			<p style="text-indent: 50px;"><b><em>Basis</em></b> ist die zu verwendende Jahresbasis. Ein nummerischer Wert, größer oder gleich 0 und kleiner oder gleich 4. Mögliche Basiswerte:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Basis</b></td>
					<td><b>Datumssystem</b></td>
				</tr>
				<tr>
					<td>0</td>
					<td>US (NASD) 30/360</td>
				</tr>
				<tr>
					<td>1</td>
					<td>Tatsächlich/tatsächlich</td>
				</tr>
				<tr>
					<td>2</td>
					<td>Tatsächlich/360</td>
				</tr>
				<tr>
					<td>3</td>
					<td>Tatsächlich/365</td>
				</tr>
				<tr>
					<td>4</td>
					<td>Europäisch 30/360</td>
				</tr>
			</table>
			<p class="note"><b>Hinweis</b>: wenn das <b><em>Startdatum</em></b>, das <b><em>Enddatum</em></b> oder die <b><em>Basis</em></b> ein Dezimalwert ist, ignoriert die Funktion die Zahlen rechts vom Dezimalpunkt.</p>
			<p>Anwendung der Funktion <b>BRTEILJAHRE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>BRTEILJAHRE</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="BRTEILJAHRE-Funktion" src="../images/yearfrac.png" /></p>
		</div>
	</body>
</html>