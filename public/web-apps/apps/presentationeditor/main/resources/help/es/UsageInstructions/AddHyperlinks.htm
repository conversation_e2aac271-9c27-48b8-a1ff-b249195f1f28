﻿<!DOCTYPE html>
<html>
	<head>
		<title>A<PERSON><PERSON> hipere<PERSON>lace</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add hyperlinks to a word or text fragment leading to an external website or to another slide in the same presentation" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>A<PERSON><PERSON> hiperenlace</h1>
			<p>Para añadir un hiperenlace,</p>
			<ol>
				<li>coloque el cursor en la posición dentro del cuadro de texto donde quiere añadir un hiperenlace,</li>
                <li>cambie a al pestaña <b>Insertar</b> en la barra de herramientas superior,</li>
				<li>pulse el icono <div class = "icon icon-addhyperlink"></div> <b>Hiperenlace</b>en la barra de herramientas superior,</li>
				<li>Después de estos pasos, la ventana <b>Configuración de hiperenlace</b> se abrirá, y usted podrá especificar los parámetros del hiperenlace:<ul>
				    <li>seleccione el tipo de enlace que quiere insertar:<ul>
				    <li>Use la opción <b>Enlace externo</b> e introduzca una URL en el formato http://www.example.com en el campo debajo de <b>Enlace a</b> si usted necesita añadir un hiperenlace a un sitio web externo.<p><img alt="Ventana Ajustes de hiperenlace" src="../../../../../../common/main/resources/help/es/images/hyperlinkwindow.png" /></p>
				    </li>
				    <li>Use la opción <b>Diapositiva en esta presentación</b> y seleccione una de las opciones de debajo si usted necesita añadir un hiperenlace a una cierta diapositiva de la misma presentación. Usted puede verificar una de las siguientes casillas: Diapositiva siguiente, Diapositiva anterior, Primera diapositiva, Última diapositiva, Diapositiva con el número especificado.<p><img alt="Ventana Ajustes de hiperenlace" src="../images/hyperlinkwindow2.png" /></p>
				    </li>
				    </ul>
				    </li>
					<li><b>Mostrar</b> - introduzca texto sobre el que se pueda hacer clic para ir a la dirección web/diapositiva especificada en el campo de arriba.</li>
					<li><b>Información en pantalla</b> - introduzca un texto que se hará visible en una ventana emergente y que le proporciona una nota breve o una etiqueta al enlace que se indica.</li>
				</ul>
				</li>
				<li>Pulse el botón <b>OK</b>.</li>
			</ol>
			<p>Para añadir un hiperenlace, usted también puede hacer clic con el botón derecho del ratón en la posición donde se insertará el hiperenlace y seleccionar la opción <b>Hiperenlace</b> en el menú contextual o usar la combinación de teclas <b>Ctrl+K</b>.</p>
			<p class="note"><b>Nota</b>: también es posible seleccionar un símbolo, palabra o combinación de palabras con el ratón o <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">usando el teclado</a> y pulsar el icono <div class = "icon icon-addhyperlink"></div> <b>hiperenlace</b>  en la pestaña <b>Insertar</b> en la barra de herramientas superior o hacer clic derecho sobre la selección y elegir la opción <b>Hiperenlace</b> del menú. Después, la ventana que se muestra arriba se abrirá con el campo de <b>Mostrar</b>, completado con el texto del fragmento seleccionado.</p>
			<p>Si mantiene el cursor encima del hiperenlace añadido, aparecerá la Información en pantalla con el texto que ha especificado. Usted puede seguir el enlace pulsando la tecla <b>CTRL</b> y haciendo clic sobre el enlace en su presentación.</p>
			<p>Para editar o borrar el hiperenlace añadido, haga clic en este mismo con el botón derecho del ratón, seleccione la opción <b>Hiperenlace</b> en el menú contextual y la acción que quiere realizar - <b>Editar hiperenlace</b> o <b>Eliminar hiperenlace</b>.</p>
		</div>
	</body>
</html>
