﻿<!DOCTYPE html>
<html>
	<head>
		<title>POTENZREIHE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>POTENZREIHE-Funktion</h1>
			<p>Die Funktion <b>POTENZREIHE</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie gibt die Summe von Potenzen zurück.</p>
			<p>Die Formelsyntax der Funktion <b>POTENZREIHE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>POTENZREIHE(x;n;m;Koeffizienten)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>x</em></b> ist der Wert der unabhängigen Variablen der Potenzreihe.</p>
				<p style="text-indent: 50px;"><b><em>n</em></b> ist die Anfangspotenz, in die Sie <b><em>x</em></b> erheben möchten.</p>
				<p style="text-indent: 50px;"><b><em>m</em></b> ist das Inkrement, um das Sie <b><em>n</em></b> in jedem Glied der Reihe vergrößern möchten.</p>
				<p style="text-indent: 50px;"><b><em>Koeffizienten</em></b> ist eine Gruppe von Koeffizienten, mit denen die aufeinander folgenden Potenzen der Variablen <b><em>x</em></b> multipliziert werden. Die Anzahl der in <b><em>Koeffizienten</em></b> angegebenen Werte bestimmt, wie viele Glieder (Potenzen) die jeweilige Potenzreihe umfasst.</p>
				<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			
			
			<p>Anwendung der Funktion <b>POTENZREIHE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>POTENZREIHE</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="POTENZREIHE-Funktion" src="../images/seriessum.png" /></p>
		</div>
	</body>
</html>