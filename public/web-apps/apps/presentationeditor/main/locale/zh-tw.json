{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Chat.textEnterMessage": "在這裡輸入您的信息", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名", "Common.Controllers.ExternalDiagramEditor.textClose": "關閉", "Common.Controllers.ExternalDiagramEditor.warningText": "該對像被禁用，因為它正在由另一個用戶編輯。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名", "Common.Controllers.ExternalOleEditor.textClose": "關閉", "Common.Controllers.ExternalOleEditor.warningText": "該物件被禁用，因為它正在由另一個帳戶編輯。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.define.chartData.textArea": "區域", "Common.define.chartData.textAreaStacked": "堆叠面積", "Common.define.chartData.textAreaStackedPer": "100% 堆疊面積圖", "Common.define.chartData.textBar": "槓", "Common.define.chartData.textBarNormal": "劇集柱形", "Common.define.chartData.textBarNormal3d": "3D劇集柱形", "Common.define.chartData.textBarNormal3dPerspective": "3-D 直式長條圖", "Common.define.chartData.textBarStacked": "堆叠柱形", "Common.define.chartData.textBarStacked3d": "3D堆叠柱形", "Common.define.chartData.textBarStackedPer": "100%堆叠柱形", "Common.define.chartData.textBarStackedPer3d": "3-D 100% 堆疊直式長條圖", "Common.define.chartData.textCharts": "圖表", "Common.define.chartData.textColumn": "欄", "Common.define.chartData.textCombo": "組合", "Common.define.chartData.textComboAreaBar": "堆叠面積 - 劇集柱形", "Common.define.chartData.textComboBarLine": "劇集柱形 - 綫", "Common.define.chartData.textComboBarLineSecondary": "劇集柱形 - 副軸綫", "Common.define.chartData.textComboCustom": "自訂組合", "Common.define.chartData.textDoughnut": "甜甜圈圖", "Common.define.chartData.textHBarNormal": "劇集條形", "Common.define.chartData.textHBarNormal3d": "3D劇集條形", "Common.define.chartData.textHBarStacked": "堆叠條形", "Common.define.chartData.textHBarStacked3d": "3D堆叠條形", "Common.define.chartData.textHBarStackedPer": "100%堆叠條形", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% 堆疊橫式長條圖", "Common.define.chartData.textLine": "線", "Common.define.chartData.textLine3d": "3-D 直線圖", "Common.define.chartData.textLineMarker": "直線加標記", "Common.define.chartData.textLineStacked": "堆叠綫", "Common.define.chartData.textLineStackedMarker": "堆積綫及標記", "Common.define.chartData.textLineStackedPer": "100%堆叠綫", "Common.define.chartData.textLineStackedPerMarker": "100% 堆疊直線圖加標記", "Common.define.chartData.textPie": "餅", "Common.define.chartData.textPie3d": "3-D 圓餅圖", "Common.define.chartData.textPoint": "XY（散點圖）", "Common.define.chartData.textScatter": "散佈圖", "Common.define.chartData.textScatterLine": "散佈圖同直線", "Common.define.chartData.textScatterLineMarker": "散佈圖同直線及標記", "Common.define.chartData.textScatterSmooth": "散佈圖同平滑線", "Common.define.chartData.textScatterSmoothMarker": "散佈圖同平滑線及標記", "Common.define.chartData.textStock": "庫存", "Common.define.chartData.textSurface": "表面", "Common.define.effectData.textAcross": "橫向", "Common.define.effectData.textAppear": "顯示", "Common.define.effectData.textArcDown": "弧形下彎", "Common.define.effectData.textArcLeft": "弧形左彎", "Common.define.effectData.textArcRight": "弧形右彎", "Common.define.effectData.textArcs": "圓弧", "Common.define.effectData.textArcUp": "弧形上彎", "Common.define.effectData.textBasic": "基本的", "Common.define.effectData.textBasicSwivel": "簡版漩渦", "Common.define.effectData.textBasicZoom": "基礎縮放", "Common.define.effectData.textBean": "豆豆", "Common.define.effectData.textBlinds": "百葉窗", "Common.define.effectData.textBlink": "閃爍", "Common.define.effectData.textBoldFlash": "加深閃爍", "Common.define.effectData.textBoldReveal": "加深顯示", "Common.define.effectData.textBoomerang": "回飛鏢", "Common.define.effectData.textBounce": "反彈", "Common.define.effectData.textBounceLeft": "左反彈", "Common.define.effectData.textBounceRight": "右反彈", "Common.define.effectData.textBox": "方形", "Common.define.effectData.textBrushColor": "畫筆色彩", "Common.define.effectData.textCenterRevolve": "中心旋轉", "Common.define.effectData.textCheckerboard": "棋盤", "Common.define.effectData.textCircle": "圓形", "Common.define.effectData.textCollapse": "縮小", "Common.define.effectData.textColorPulse": "色彩頻率", "Common.define.effectData.textComplementaryColor": "互補色", "Common.define.effectData.textComplementaryColor2": "互補色2", "Common.define.effectData.textCompress": "壓縮", "Common.define.effectData.textContrast": "對比", "Common.define.effectData.textContrastingColor": "對比色", "Common.define.effectData.textCredits": "來源出處", "Common.define.effectData.textCrescentMoon": "弦月形", "Common.define.effectData.textCurveDown": "弧形向下", "Common.define.effectData.textCurvedSquare": "圓角正方形", "Common.define.effectData.textCurvedX": "曲線X", "Common.define.effectData.textCurvyLeft": "自左曲線", "Common.define.effectData.textCurvyRight": "自右曲線", "Common.define.effectData.textCurvyStar": "曲形星星", "Common.define.effectData.textCustomPath": "客制路徑", "Common.define.effectData.textCuverUp": "弧形向上", "Common.define.effectData.textDarken": "加深", "Common.define.effectData.textDecayingWave": "弱減波", "Common.define.effectData.textDesaturate": "色彩不飽和", "Common.define.effectData.textDiagonalDownRight": "對角向右下", "Common.define.effectData.textDiagonalUpRight": "對角向右上", "Common.define.effectData.textDiamond": "鑽石", "Common.define.effectData.textDisappear": "消失", "Common.define.effectData.textDissolveIn": "平滑淡出", "Common.define.effectData.textDissolveOut": "淡出切換", "Common.define.effectData.textDown": "下", "Common.define.effectData.textDrop": "落下", "Common.define.effectData.textEmphasis": "加強特效", "Common.define.effectData.textEntrance": "進場特效", "Common.define.effectData.textEqualTriangle": "等邊三角形", "Common.define.effectData.textExciting": "華麗", "Common.define.effectData.textExit": "離開特效", "Common.define.effectData.textExpand": "擴大", "Common.define.effectData.textFade": "褪", "Common.define.effectData.textFigureFour": "雙8圖", "Common.define.effectData.textFillColor": "填色", "Common.define.effectData.textFlip": "翻轉", "Common.define.effectData.textFloat": "漂浮", "Common.define.effectData.textFloatDown": "向下浮動", "Common.define.effectData.textFloatIn": "漂浮進入", "Common.define.effectData.textFloatOut": "漂浮出去", "Common.define.effectData.textFloatUp": "向上浮動", "Common.define.effectData.textFlyIn": "飛入", "Common.define.effectData.textFlyOut": "飛出", "Common.define.effectData.textFontColor": "字體顏色", "Common.define.effectData.textFootball": "足球形", "Common.define.effectData.textFromBottom": "自下而上", "Common.define.effectData.textFromBottomLeft": "從左下", "Common.define.effectData.textFromBottomRight": "從右下", "Common.define.effectData.textFromLeft": "從左邊", "Common.define.effectData.textFromRight": "從右邊", "Common.define.effectData.textFromTop": "自上", "Common.define.effectData.textFromTopLeft": "自左上", "Common.define.effectData.textFromTopRight": "自右上", "Common.define.effectData.textFunnel": "漏斗", "Common.define.effectData.textGrowShrink": "放大/縮小", "Common.define.effectData.textGrowTurn": "延展及翻轉", "Common.define.effectData.textGrowWithColor": "顏色伸展", "Common.define.effectData.textHeart": "心", "Common.define.effectData.textHeartbeat": "心跳圖", "Common.define.effectData.textHexagon": "六邊形", "Common.define.effectData.textHorizontal": "水平", "Common.define.effectData.textHorizontalFigure": "水平數字8", "Common.define.effectData.textHorizontalIn": "水平", "Common.define.effectData.textHorizontalOut": "水平向外", "Common.define.effectData.textIn": "在", "Common.define.effectData.textInFromScreenCenter": "從螢幕中心展開", "Common.define.effectData.textInSlightly": "微量放大", "Common.define.effectData.textInToScreenBottom": "放大到螢幕底部", "Common.define.effectData.textInvertedSquare": "反向正方形", "Common.define.effectData.textInvertedTriangle": "反向三角形", "Common.define.effectData.textLeft": "左", "Common.define.effectData.textLeftDown": "左下", "Common.define.effectData.textLeftUp": "左上", "Common.define.effectData.textLighten": "淡化", "Common.define.effectData.textLineColor": "線條顏色", "Common.define.effectData.textLines": "線", "Common.define.effectData.textLinesCurves": "直線曲線", "Common.define.effectData.textLoopDeLoop": "漣漪", "Common.define.effectData.textLoops": "循環", "Common.define.effectData.textModerate": "中等", "Common.define.effectData.textNeutron": "中子", "Common.define.effectData.textObjectCenter": "物件中心", "Common.define.effectData.textObjectColor": "物件色彩", "Common.define.effectData.textOctagon": "八邊形", "Common.define.effectData.textOut": "外", "Common.define.effectData.textOutFromScreenBottom": "從螢幕下縮小", "Common.define.effectData.textOutSlightly": "微量縮小", "Common.define.effectData.textOutToScreenCenter": "縮小到螢幕中央", "Common.define.effectData.textParallelogram": "平行四邊形", "Common.define.effectData.textPath": "動態路徑", "Common.define.effectData.textPathCurve": "曲線", "Common.define.effectData.textPathScribble": "塗", "Common.define.effectData.textPeanut": "花生", "Common.define.effectData.textPeekIn": "向內", "Common.define.effectData.textPeekOut": "向外", "Common.define.effectData.textPentagon": "五角形", "Common.define.effectData.textPinwheel": "風車", "Common.define.effectData.textPlus": "加", "Common.define.effectData.textPointStar": "點星", "Common.define.effectData.textPointStar4": "4點星", "Common.define.effectData.textPointStar5": "5點星", "Common.define.effectData.textPointStar6": "6點星", "Common.define.effectData.textPointStar8": "8點星", "Common.define.effectData.textPulse": "頻率", "Common.define.effectData.textRandomBars": "隨機線條", "Common.define.effectData.textRight": "右", "Common.define.effectData.textRightDown": "右下", "Common.define.effectData.textRightTriangle": "直角三角形", "Common.define.effectData.textRightUp": "右上", "Common.define.effectData.textRiseUp": "升起", "Common.define.effectData.textSCurve1": "S曲線1", "Common.define.effectData.textSCurve2": "S曲線2", "Common.define.effectData.textShape": "形状", "Common.define.effectData.textShapes": "形狀", "Common.define.effectData.textShimmer": "閃爍", "Common.define.effectData.textShrinkTurn": "縮小並旋轉", "Common.define.effectData.textSineWave": "正弦波", "Common.define.effectData.textSinkDown": "向下漂浮", "Common.define.effectData.textSlideCenter": "投影片中心", "Common.define.effectData.textSpecial": "特殊", "Common.define.effectData.textSpin": "旋轉", "Common.define.effectData.textSpinner": "迴旋鏢", "Common.define.effectData.textSpiralIn": "螺旋向內", "Common.define.effectData.textSpiralLeft": "自左螺旋", "Common.define.effectData.textSpiralOut": "螺旋向外", "Common.define.effectData.textSpiralRight": "自右螺旋", "Common.define.effectData.textSplit": "分割", "Common.define.effectData.textSpoke1": "1輪幅條", "Common.define.effectData.textSpoke2": "2輪幅條", "Common.define.effectData.textSpoke3": "3輪幅條", "Common.define.effectData.textSpoke4": "4輪幅條", "Common.define.effectData.textSpoke8": "8輪幅條", "Common.define.effectData.textSpring": "彈跳", "Common.define.effectData.textSquare": "正方形", "Common.define.effectData.textStairsDown": "向下階梯", "Common.define.effectData.textStretch": "延伸", "Common.define.effectData.textStrips": "階梯狀", "Common.define.effectData.textSubtle": "輕微", "Common.define.effectData.textSwivel": "漩渦", "Common.define.effectData.textSwoosh": "旋轉", "Common.define.effectData.textTeardrop": "淚珠", "Common.define.effectData.textTeeter": "落下", "Common.define.effectData.textToBottom": "自下", "Common.define.effectData.textToBottomLeft": "自左下", "Common.define.effectData.textToBottomRight": "自右下", "Common.define.effectData.textToLeft": "自左", "Common.define.effectData.textToRight": "自右", "Common.define.effectData.textToTop": "自上", "Common.define.effectData.textToTopLeft": "從左上", "Common.define.effectData.textToTopRight": "從右上", "Common.define.effectData.textTransparency": "透明度", "Common.define.effectData.textTrapezoid": "梯形", "Common.define.effectData.textTurnDown": "向下轉", "Common.define.effectData.textTurnDownRight": "向右下轉", "Common.define.effectData.textTurns": "翻轉", "Common.define.effectData.textTurnUp": "向上轉", "Common.define.effectData.textTurnUpRight": "向右上轉", "Common.define.effectData.textUnderline": "底線", "Common.define.effectData.textUp": "上", "Common.define.effectData.textVertical": "垂直", "Common.define.effectData.textVerticalFigure": "垂直數字8", "Common.define.effectData.textVerticalIn": "垂直", "Common.define.effectData.textVerticalOut": "由中向左右", "Common.define.effectData.textWave": "波", "Common.define.effectData.textWedge": "楔", "Common.define.effectData.textWheel": "輪型", "Common.define.effectData.textWhip": "猛然挪動", "Common.define.effectData.textWipe": "擦去", "Common.define.effectData.textZigzag": "鋸齒邊緣", "Common.define.effectData.textZoom": "放大", "Common.Translation.textMoreButton": "更多", "Common.Translation.warnFileLocked": "該文件正在另一個應用程序中進行編輯。您可以繼續編輯並將其另存為副本。", "Common.Translation.warnFileLockedBtnEdit": "\n建立副本", "Common.Translation.warnFileLockedBtnView": "打開查看", "Common.UI.ButtonColored.textAutoColor": "自動", "Common.UI.ButtonColored.textNewColor": "新增自訂顏色", "Common.UI.ComboBorderSize.txtNoBorders": "無邊框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "無邊框", "Common.UI.ComboDataView.emptyComboText": "無樣式", "Common.UI.ExtendedColorDialog.addButtonText": "增加", "Common.UI.ExtendedColorDialog.textCurrent": "當前", "Common.UI.ExtendedColorDialog.textHexErr": "輸入的值不正確。<br>請輸入一個介於000000和FFFFFF之間的值。", "Common.UI.ExtendedColorDialog.textNew": "新", "Common.UI.ExtendedColorDialog.textRGBErr": "輸入的值不正確。<br>請輸入0到255之間的數字。", "Common.UI.HSBColorPicker.textNoColor": "無顏色", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "不顯示密碼", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "顯示密碼", "Common.UI.SearchBar.textFind": "尋找", "Common.UI.SearchBar.tipCloseSearch": "關閉搜索", "Common.UI.SearchBar.tipNextResult": "下一個結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "開啟進階設置", "Common.UI.SearchBar.tipPreviousResult": "上一個結果", "Common.UI.SearchDialog.textHighlight": "強調結果", "Common.UI.SearchDialog.textMatchCase": "區分大小寫", "Common.UI.SearchDialog.textReplaceDef": "輸入替換文字", "Common.UI.SearchDialog.textSearchStart": "在這裡輸入您的文字", "Common.UI.SearchDialog.textTitle": "尋找與取代", "Common.UI.SearchDialog.textTitle2": "尋找", "Common.UI.SearchDialog.textWholeWords": "僅全字", "Common.UI.SearchDialog.txtBtnHideReplace": "隱藏替換", "Common.UI.SearchDialog.txtBtnReplace": "取代", "Common.UI.SearchDialog.txtBtnReplaceAll": "取代全部", "Common.UI.SynchronizeTip.textDontShow": "不再顯示此消息", "Common.UI.SynchronizeTip.textSynchronize": "該文檔已被其他用戶更改。<br>請單擊以保存更改並重新加載更新。", "Common.UI.ThemeColorPalette.textRecentColors": "近期顏色", "Common.UI.ThemeColorPalette.textStandartColors": "標準顏色", "Common.UI.ThemeColorPalette.textThemeColors": "主題顏色", "Common.UI.Themes.txtThemeClassicLight": "傳統亮色", "Common.UI.Themes.txtThemeContrastDark": "暗色對比", "Common.UI.Themes.txtThemeDark": "暗", "Common.UI.Themes.txtThemeLight": "淺色主題", "Common.UI.Themes.txtThemeSystem": "和系統一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "關閉", "Common.UI.Window.noButtonText": "沒有", "Common.UI.Window.okButtonText": "確定", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "不再顯示此消息", "Common.UI.Window.textError": "錯誤", "Common.UI.Window.textInformation": "資訊", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "公分", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "地址:", "Common.Views.About.txtLicensee": "被許可人", "Common.Views.About.txtLicensor": "許可人", "Common.Views.About.txtMail": "電子郵件：", "Common.Views.About.txtPoweredBy": "於支援", "Common.Views.About.txtTel": "電話: ", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "增加", "Common.Views.AutoCorrectDialog.textApplyText": "鍵入時同時申請", "Common.Views.AutoCorrectDialog.textAutoCorrect": "自動更正", "Common.Views.AutoCorrectDialog.textAutoFormat": "鍵入時自動調整規格", "Common.Views.AutoCorrectDialog.textBulleted": "自動項目符號列表", "Common.Views.AutoCorrectDialog.textBy": "通過", "Common.Views.AutoCorrectDialog.textDelete": "刪除", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "按兩下空白鍵自動增加一個句點(.)符號", "Common.Views.AutoCorrectDialog.textFLCells": "儲存格首字母大寫", "Common.Views.AutoCorrectDialog.textFLSentence": "句子第一個字母大寫", "Common.Views.AutoCorrectDialog.textHyperlink": "網絡路徑超連結", "Common.Views.AutoCorrectDialog.textHyphens": "帶連字符（-）的連字符（-）", "Common.Views.AutoCorrectDialog.textMathCorrect": "數學自動更正", "Common.Views.AutoCorrectDialog.textNumbered": "自動編號列表", "Common.Views.AutoCorrectDialog.textQuotes": "“直引號”與“智能引號”", "Common.Views.AutoCorrectDialog.textRecognized": "公認的功能", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表達式是公認的數學表達式。它們不會自動斜體顯示。", "Common.Views.AutoCorrectDialog.textReplace": "替換:", "Common.Views.AutoCorrectDialog.textReplaceText": "鍵入時替換", "Common.Views.AutoCorrectDialog.textReplaceType": "鍵入時替換文字", "Common.Views.AutoCorrectDialog.textReset": "重設", "Common.Views.AutoCorrectDialog.textResetAll": "重置為預設", "Common.Views.AutoCorrectDialog.textRestore": "回復", "Common.Views.AutoCorrectDialog.textTitle": "自動更正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "公認的函數只能包含字母A到Z，大寫或小寫。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "您添加的所有表達式都將被刪除，被刪除的表達式將被恢復。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnReplace": "％1的自動更正條目已存在。您要更換嗎？", "Common.Views.AutoCorrectDialog.warnReset": "您添加的所有自動更正將被刪除，更改後的自動更正將恢復為其原始值。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnRestore": "％1的自動更正條目將被重置為其原始值。你想繼續嗎？", "Common.Views.Chat.textSend": "傳送", "Common.Views.Comments.mniAuthorAsc": "作者排行A到Z", "Common.Views.Comments.mniAuthorDesc": "作者排行Z到A", "Common.Views.Comments.mniDateAsc": "從最老的", "Common.Views.Comments.mniDateDesc": "從最新的", "Common.Views.Comments.mniFilterGroups": "依群組篩選", "Common.Views.Comments.mniPositionAsc": "自上", "Common.Views.Comments.mniPositionDesc": "自下而上", "Common.Views.Comments.textAdd": "增加", "Common.Views.Comments.textAddComment": "增加評論", "Common.Views.Comments.textAddCommentToDoc": "在文檔中添加評論", "Common.Views.Comments.textAddReply": "加入回覆", "Common.Views.Comments.textAll": "全部", "Common.Views.Comments.textAnonym": "來賓帳戶", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "關閉", "Common.Views.Comments.textClosePanel": "關註釋", "Common.Views.Comments.textComments": "評論", "Common.Views.Comments.textEdit": "確定", "Common.Views.Comments.textEnterCommentHint": "在這裡輸入您的評論", "Common.Views.Comments.textHintAddComment": "增加評論", "Common.Views.Comments.textOpenAgain": "重新打開", "Common.Views.Comments.textReply": "回覆", "Common.Views.Comments.textResolve": "解決", "Common.Views.Comments.textResolved": "已解決", "Common.Views.Comments.textSort": "註釋分類", "Common.Views.Comments.textViewResolved": "您沒有權限來重新開啟這個註解", "Common.Views.Comments.txtEmpty": "文件裡沒有註解。", "Common.Views.CopyWarningDialog.textDontShow": "不再顯示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用編輯器工具欄按鈕進行複制，剪切和粘貼操作以及上下文選單操作僅在此編輯器選項卡中執行。<br> <br>要在“編輯器”選項卡之外的應用程序之間進行複製或粘貼，請使用以下鍵盤組合：", "Common.Views.CopyWarningDialog.textTitle": "複製, 剪下, 與貼上之動作", "Common.Views.CopyWarningDialog.textToCopy": "複印", "Common.Views.CopyWarningDialog.textToCut": "切", "Common.Views.CopyWarningDialog.textToPaste": "粘貼", "Common.Views.DocumentAccessDialog.textLoading": "載入中...", "Common.Views.DocumentAccessDialog.textTitle": "分享設定", "Common.Views.ExternalDiagramEditor.textTitle": "圖表編輯器", "Common.Views.ExternalOleEditor.textTitle": "試算表編輯器", "Common.Views.Header.labelCoUsersDescr": "正在編輯文件的用戶：", "Common.Views.Header.textAddFavorite": "標記為最愛收藏", "Common.Views.Header.textAdvSettings": "進階設定", "Common.Views.Header.textBack": "打開文件所在位置", "Common.Views.Header.textCompactView": "隱藏工具欄", "Common.Views.Header.textHideLines": "隱藏標尺", "Common.Views.Header.textHideNotes": "隐藏笔记", "Common.Views.Header.textHideStatusBar": "隱藏狀態欄", "Common.Views.Header.textRemoveFavorite": "\n從最愛收藏夾中刪除", "Common.Views.Header.textSaveBegin": "存檔中...", "Common.Views.Header.textSaveChanged": "已更改", "Common.Views.Header.textSaveEnd": "所有更改已保存", "Common.Views.Header.textSaveExpander": "所有更改已保存", "Common.Views.Header.textShare": "分享", "Common.Views.Header.textZoom": "放大", "Common.Views.Header.tipAccessRights": "管理文檔存取權限", "Common.Views.Header.tipDownload": "下載文件", "Common.Views.Header.tipGoEdit": "編輯當前文件", "Common.Views.Header.tipPrint": "列印文件", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipSave": "儲存", "Common.Views.Header.tipSearch": "搜尋", "Common.Views.Header.tipUndo": "復原", "Common.Views.Header.tipUndock": "移至單獨的視窗", "Common.Views.Header.tipUsers": "查看用戶", "Common.Views.Header.tipViewSettings": "查看設定", "Common.Views.Header.tipViewUsers": "查看用戶並管理文檔存取權限", "Common.Views.Header.txtAccessRights": "更改存取權限", "Common.Views.Header.txtRename": "重新命名", "Common.Views.History.textCloseHistory": "關閉歷史紀錄", "Common.Views.History.textHide": "縮小", "Common.Views.History.textHideAll": "隱藏詳細的更改", "Common.Views.History.textRestore": "恢復", "Common.Views.History.textShow": "擴大", "Common.Views.History.textShowAll": "顯示詳細的更改歷史", "Common.Views.History.textVer": "版本", "Common.Views.ImageFromUrlDialog.textUrl": "粘貼圖片網址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "這是必填欄", "Common.Views.ImageFromUrlDialog.txtNotUrl": "此字段應為“ http://www.example.com”格式的網址", "Common.Views.InsertTableDialog.textInvalidRowsCols": "您需要指定有效的行數和列數。", "Common.Views.InsertTableDialog.txtColumns": "列數", "Common.Views.InsertTableDialog.txtMaxText": "此字段的最大值為{0}。", "Common.Views.InsertTableDialog.txtMinText": "此字段的最小值為{0}。", "Common.Views.InsertTableDialog.txtRows": "行數", "Common.Views.InsertTableDialog.txtTitle": "表格大小", "Common.Views.InsertTableDialog.txtTitleSplit": "分割儲存格", "Common.Views.LanguageDialog.labelSelect": "選擇文件語言", "Common.Views.ListSettingsDialog.textBulleted": "已加入項目點", "Common.Views.ListSettingsDialog.textFromStorage": "從存儲", "Common.Views.ListSettingsDialog.textFromUrl": "從 URL", "Common.Views.ListSettingsDialog.textNumbering": "已編號", "Common.Views.ListSettingsDialog.tipChange": "更改項目點", "Common.Views.ListSettingsDialog.txtBullet": "項目點", "Common.Views.ListSettingsDialog.txtColor": "顏色", "Common.Views.ListSettingsDialog.txtImage": "圖像", "Common.Views.ListSettingsDialog.txtImport": "匯入", "Common.Views.ListSettingsDialog.txtNewBullet": "新子彈點", "Common.Views.ListSettingsDialog.txtNone": "無", "Common.Views.ListSettingsDialog.txtOfText": "文字百分比", "Common.Views.ListSettingsDialog.txtSize": "大小", "Common.Views.ListSettingsDialog.txtStart": "開始", "Common.Views.ListSettingsDialog.txtSymbol": "符號", "Common.Views.ListSettingsDialog.txtTitle": "清單設定", "Common.Views.ListSettingsDialog.txtType": "類型", "Common.Views.OpenDialog.closeButtonText": "關閉檔案", "Common.Views.OpenDialog.txtEncoding": "編碼", "Common.Views.OpenDialog.txtIncorrectPwd": "密碼錯誤。", "Common.Views.OpenDialog.txtOpenFile": "輸入檔案密碼", "Common.Views.OpenDialog.txtPassword": "密碼", "Common.Views.OpenDialog.txtProtected": "輸入密碼並打開文件後，該文件的當前密碼將被重置。", "Common.Views.OpenDialog.txtTitle": "選擇％1個選項", "Common.Views.OpenDialog.txtTitleProtected": "受保護的文件", "Common.Views.PasswordDialog.txtDescription": "設置密碼以保護此文檔", "Common.Views.PasswordDialog.txtIncorrectPwd": "確認密碼不相同", "Common.Views.PasswordDialog.txtPassword": "密碼", "Common.Views.PasswordDialog.txtRepeat": "重複輸入密碼", "Common.Views.PasswordDialog.txtTitle": "設置密碼", "Common.Views.PasswordDialog.txtWarning": "警告：如果失去密碼，將無法取回。請妥善保存。", "Common.Views.PluginDlg.textLoading": "載入中", "Common.Views.Plugins.groupCaption": "外掛程式", "Common.Views.Plugins.strPlugins": "外掛程式", "Common.Views.Plugins.textClosePanel": "關閉插件", "Common.Views.Plugins.textLoading": "載入中", "Common.Views.Plugins.textStart": "開始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Protection.hintAddPwd": "用密碼加密", "Common.Views.Protection.hintPwd": "更改或刪除密碼", "Common.Views.Protection.hintSignature": "添加數字簽名或簽名行", "Common.Views.Protection.txtAddPwd": "新增密碼", "Common.Views.Protection.txtChangePwd": "變更密碼", "Common.Views.Protection.txtDeletePwd": "刪除密碼", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "添加數字簽名", "Common.Views.Protection.txtSignature": "簽名", "Common.Views.Protection.txtSignatureLine": "添加簽名行", "Common.Views.RenameDialog.textName": "\n文件名", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "到下一個變化", "Common.Views.ReviewChanges.hintPrev": "到之前的變化", "Common.Views.ReviewChanges.strFast": "快", "Common.Views.ReviewChanges.strFastDesc": "即時共同編輯。所有更改將自動存檔。", "Common.Views.ReviewChanges.strStrict": "嚴格", "Common.Views.ReviewChanges.strStrictDesc": "使用“保存”按鈕同步您和其他人所做的更改。", "Common.Views.ReviewChanges.tipAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.tipCoAuthMode": "設定共同編輯模式", "Common.Views.ReviewChanges.tipCommentRem": "刪除評論", "Common.Views.ReviewChanges.tipCommentRemCurrent": "刪除當前評論", "Common.Views.ReviewChanges.tipCommentResolve": "標記註解為已解決", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "將註解標記為已解決", "Common.Views.ReviewChanges.tipHistory": "顯示版本歷史", "Common.Views.ReviewChanges.tipRejectCurrent": "拒絕當前變化", "Common.Views.ReviewChanges.tipReview": "跟蹤變化", "Common.Views.ReviewChanges.tipReviewView": "選擇您要顯示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "設定文件語言", "Common.Views.ReviewChanges.tipSetSpelling": "拼字檢查", "Common.Views.ReviewChanges.tipSharing": "管理文檔存取權限", "Common.Views.ReviewChanges.txtAccept": "同意", "Common.Views.ReviewChanges.txtAcceptAll": "接受全部的更改", "Common.Views.ReviewChanges.txtAcceptChanges": "同意更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "關閉", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編輯模式", "Common.Views.ReviewChanges.txtCommentRemAll": "刪除所有評論", "Common.Views.ReviewChanges.txtCommentRemCurrent": "刪除當前評論", "Common.Views.ReviewChanges.txtCommentRemMy": "刪除我的評論", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "刪除我當前的評論", "Common.Views.ReviewChanges.txtCommentRemove": "移除", "Common.Views.ReviewChanges.txtCommentResolve": "解決", "Common.Views.ReviewChanges.txtCommentResolveAll": "將所有註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "將註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMy": "將所有自己的註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "將自己的註解標記為已解決", "Common.Views.ReviewChanges.txtDocLang": "語言", "Common.Views.ReviewChanges.txtFinal": "更改已全部接受（預覽）", "Common.Views.ReviewChanges.txtFinalCap": "最後", "Common.Views.ReviewChanges.txtHistory": "版本歷史", "Common.Views.ReviewChanges.txtMarkup": "全部的更改(編輯中)", "Common.Views.ReviewChanges.txtMarkupCap": "標記", "Common.Views.ReviewChanges.txtNext": "下一個", "Common.Views.ReviewChanges.txtOriginal": "全部更改被拒絕（預覽）", "Common.Views.ReviewChanges.txtOriginalCap": "原始", "Common.Views.ReviewChanges.txtPrev": "前一個", "Common.Views.ReviewChanges.txtReject": "拒絕", "Common.Views.ReviewChanges.txtRejectAll": "拒絕所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "拒絕更改", "Common.Views.ReviewChanges.txtRejectCurrent": "拒絕當前變化", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼字檢查", "Common.Views.ReviewChanges.txtTurnon": "跟蹤變化", "Common.Views.ReviewChanges.txtView": "顯示模式", "Common.Views.ReviewPopover.textAdd": "增加", "Common.Views.ReviewPopover.textAddReply": "加入回覆", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "關閉", "Common.Views.ReviewPopover.textEdit": "確定", "Common.Views.ReviewPopover.textMention": "+提及將提供對文檔的存取權限並發送電子郵件", "Common.Views.ReviewPopover.textMentionNotify": "+提及將通過電子郵件通知用戶", "Common.Views.ReviewPopover.textOpenAgain": "重新打開", "Common.Views.ReviewPopover.textReply": "回覆", "Common.Views.ReviewPopover.textResolve": "解決", "Common.Views.ReviewPopover.textViewResolved": "您沒有權限來重新開啟這個註解", "Common.Views.ReviewPopover.txtDeleteTip": "刪除", "Common.Views.ReviewPopover.txtEditTip": "編輯", "Common.Views.SaveAsDlg.textLoading": "載入中", "Common.Views.SaveAsDlg.textTitle": "保存文件夾", "Common.Views.SearchPanel.textCaseSensitive": "區分大小寫", "Common.Views.SearchPanel.textCloseSearch": "關閉搜索", "Common.Views.SearchPanel.textFind": "尋找", "Common.Views.SearchPanel.textFindAndReplace": "尋找與取代", "Common.Views.SearchPanel.textMatchUsingRegExp": "用正規表達式進行匹配", "Common.Views.SearchPanel.textNoMatches": "無匹配", "Common.Views.SearchPanel.textNoSearchResults": "查無搜索结果", "Common.Views.SearchPanel.textReplace": "取代", "Common.Views.SearchPanel.textReplaceAll": "全部替換", "Common.Views.SearchPanel.textReplaceWith": "替換為", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "因數量過多而無法顯示部分結果", "Common.Views.SearchPanel.textWholeWords": "僅全字", "Common.Views.SearchPanel.tipNextResult": "下一個結果", "Common.Views.SearchPanel.tipPreviousResult": "上一個結果", "Common.Views.SelectFileDlg.textLoading": "載入中", "Common.Views.SelectFileDlg.textTitle": "選擇資料來源", "Common.Views.SignDialog.textBold": "粗體", "Common.Views.SignDialog.textCertificate": "證書", "Common.Views.SignDialog.textChange": "變更", "Common.Views.SignDialog.textInputName": "輸入簽名者名稱", "Common.Views.SignDialog.textItalic": "斜體", "Common.Views.SignDialog.textNameError": "簽名人姓名不能留空。", "Common.Views.SignDialog.textPurpose": "簽署本文件的目的", "Common.Views.SignDialog.textSelect": "選擇", "Common.Views.SignDialog.textSelectImage": "選擇圖片", "Common.Views.SignDialog.textSignature": "簽名看起來像", "Common.Views.SignDialog.textTitle": "簽署文件", "Common.Views.SignDialog.textUseImage": "或單擊“選擇圖像”以使用圖片作為簽名", "Common.Views.SignDialog.textValid": "從％1到％2有效", "Common.Views.SignDialog.tipFontName": "字體名稱", "Common.Views.SignDialog.tipFontSize": "字體大小", "Common.Views.SignSettingsDialog.textAllowComment": "允許簽名者在簽名對話框中添加註釋", "Common.Views.SignSettingsDialog.textInfoEmail": "電子郵件", "Common.Views.SignSettingsDialog.textInfoName": "名稱", "Common.Views.SignSettingsDialog.textInfoTitle": "簽名人稱號", "Common.Views.SignSettingsDialog.textInstructions": "簽名者說明", "Common.Views.SignSettingsDialog.textShowDate": "在簽名行中顯示簽名日期", "Common.Views.SignSettingsDialog.textTitle": "簽名設置", "Common.Views.SignSettingsDialog.txtEmpty": "這是必填欄", "Common.Views.SymbolTableDialog.textCharacter": "字符", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX 值", "Common.Views.SymbolTableDialog.textCopyright": "版權標誌", "Common.Views.SymbolTableDialog.textDCQuote": "結束雙引號", "Common.Views.SymbolTableDialog.textDOQuote": "開頭雙引號", "Common.Views.SymbolTableDialog.textEllipsis": "水平橢圓", "Common.Views.SymbolTableDialog.textEmDash": "空槓", "Common.Views.SymbolTableDialog.textEmSpace": "空白空間", "Common.Views.SymbolTableDialog.textEnDash": "En 橫槓", "Common.Views.SymbolTableDialog.textEnSpace": "En 空白", "Common.Views.SymbolTableDialog.textFont": "字體", "Common.Views.SymbolTableDialog.textNBHyphen": "不間斷連字符", "Common.Views.SymbolTableDialog.textNBSpace": "不間斷空間", "Common.Views.SymbolTableDialog.textPilcrow": "稻草人標誌", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 空白空間", "Common.Views.SymbolTableDialog.textRange": "範圍", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符號", "Common.Views.SymbolTableDialog.textRegistered": "註冊標誌", "Common.Views.SymbolTableDialog.textSCQuote": "結束單引號", "Common.Views.SymbolTableDialog.textSection": "分區標誌", "Common.Views.SymbolTableDialog.textShortcut": "快捷鍵", "Common.Views.SymbolTableDialog.textSHyphen": "軟連字符", "Common.Views.SymbolTableDialog.textSOQuote": "開單報價", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符號", "Common.Views.SymbolTableDialog.textTitle": "符號", "Common.Views.SymbolTableDialog.textTradeMark": "商標符號", "Common.Views.UserNameDialog.textDontShow": "不要再顯示", "Common.Views.UserNameDialog.textLabel": "標籤：", "Common.Views.UserNameDialog.textLabelError": "標籤不能為空。", "PE.Controllers.LeftMenu.leavePageText": "該文檔中所有未保存的更改都將丟失。<br>單擊“取消”，然後單擊“保存”以保存它們。單擊“確定”，放棄所有未保存的更改。", "PE.Controllers.LeftMenu.newDocumentTitle": "未命名的演講", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "PE.Controllers.LeftMenu.requestEditRightsText": "正在請求編輯權限...", "PE.Controllers.LeftMenu.textLoadHistory": "正在載入版本歷史記錄...", "PE.Controllers.LeftMenu.textNoTextFound": "找不到您一直在搜索的數據。請調整您的搜索選項。", "PE.Controllers.LeftMenu.textReplaceSkipped": "替換已完成。 {0}個事件被跳過。", "PE.Controllers.LeftMenu.textReplaceSuccess": "搜索已完成。發生的事件已替換：{0}", "PE.Controllers.LeftMenu.txtUntitled": "無標題", "PE.Controllers.Main.applyChangesTextText": "加載數據中...", "PE.Controllers.Main.applyChangesTitleText": "加載數據中", "PE.Controllers.Main.convertationTimeoutText": "轉換逾時。", "PE.Controllers.Main.criticalErrorExtText": "按“確定”返回文檔列表。", "PE.Controllers.Main.criticalErrorTitle": "錯誤", "PE.Controllers.Main.downloadErrorText": "下載失敗", "PE.Controllers.Main.downloadTextText": "下載簡報中...", "PE.Controllers.Main.downloadTitleText": "下載簡報中", "PE.Controllers.Main.errorAccessDeny": "您嘗試進行未被授權的動作。<br> 請聯繫您的文件伺服器主機的管理者。", "PE.Controllers.Main.errorBadImageUrl": "不正確的圖像 URL", "PE.Controllers.Main.errorCoAuthoringDisconnect": "服務器連接丟失。該文檔目前無法編輯。", "PE.Controllers.Main.errorComboSeries": "新增組合圖表需選兩個以上的數據系列。", "PE.Controllers.Main.errorConnectToServer": "此文件無法儲存。請檢查連線設定或聯絡您的管理者 <br> 當您點選'OK'按鈕, 您將會被提示來進行此文件的下載。", "PE.Controllers.Main.errorDatabaseConnection": "外部錯誤。<br>數據庫連接錯誤。如果錯誤仍然存在，請聯繫支持。", "PE.Controllers.Main.errorDataEncrypted": "已收到加密的更改，無法解密。", "PE.Controllers.Main.errorDataRange": "不正確的資料範圍", "PE.Controllers.Main.errorDefaultMessage": "錯誤編號：%1", "PE.Controllers.Main.errorEditingDownloadas": "在處理文檔期間發生錯誤。<br>使用\"下載為\"選項將文件備份副本保存到計算機硬碟驅動器中。", "PE.Controllers.Main.errorEditingSaveas": "使用文檔期間發生錯誤。<br>使用\"另存為...\"選項將文件備份副本保存到計算機硬碟驅動器中。", "PE.Controllers.Main.errorEmailClient": "找不到電子郵件客戶端。", "PE.Controllers.Main.errorFilePassProtect": "該文件受密碼保護，無法打開。", "PE.Controllers.Main.errorFileSizeExceed": "此檔案超過這一主機限制的大小<br> 進一步資訊，請聯絡您的文件服務主機的管理者。", "PE.Controllers.Main.errorForceSave": "保存文件時發生錯誤。請使用\"下載為\"選項將文件保存到電腦機硬碟中，或稍後再試。", "PE.Controllers.Main.errorKeyEncrypt": "未知密鑰描述符", "PE.Controllers.Main.errorKeyExpire": "密鑰描述符已過期", "PE.Controllers.Main.errorLoadingFont": "字體未加載。<br>請聯繫文件服務器管理員。", "PE.Controllers.Main.errorProcessSaveResult": "儲存失敗", "PE.Controllers.Main.errorServerVersion": "編輯器版本已更新。該頁面將被重新加載以應用更改。", "PE.Controllers.Main.errorSessionAbsolute": "此文件編輯的會期已經過時。請重新載入此頁面。", "PE.Controllers.Main.errorSessionIdle": "此文件已經在編輯狀態有很長時間, 請重新載入此頁面。", "PE.Controllers.Main.errorSessionToken": "與服務器的連接已中斷。請重新加載頁面。", "PE.Controllers.Main.errorSetPassword": "無法設定密碼。", "PE.Controllers.Main.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "PE.Controllers.Main.errorToken": "文檔安全令牌的格式不正確。<br>請與您的Document Server管理員聯繫。", "PE.Controllers.Main.errorTokenExpire": "文檔安全令牌已過期。<br>請與您的Document Server管理員聯繫。", "PE.Controllers.Main.errorUpdateVersion": "文件版本已更改。該頁面將重新加載。", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "網路連接已恢復，文件版本已更改。<br>在繼續工作之前，您需要下載文件或複制其內容以確保沒有丟失，然後重新加載此頁面。", "PE.Controllers.Main.errorUserDrop": "目前無法存取該文件。", "PE.Controllers.Main.errorUsersExceed": "超出了定價計劃所允許的用戶數量", "PE.Controllers.Main.errorViewerDisconnect": "連接斷開。您仍然可以查看該文檔，但在恢復連接並重新加載頁面之前將無法下載或打印該文檔。", "PE.Controllers.Main.leavePageText": "您尚未保存此演示文稿中的更改。單擊“保留在此頁面上”，然後單擊“保存”以保存它們。單擊“離開此頁面”以放棄所有未保存的更改。", "PE.Controllers.Main.leavePageTextOnClose": "該文檔中所有未保存的更改都將丟失。<br>單擊“取消”，然後單擊“保存”以保存它們。單擊“確定”，放棄所有未保存的更改。", "PE.Controllers.Main.loadFontsTextText": "加載數據中...", "PE.Controllers.Main.loadFontsTitleText": "加載數據中", "PE.Controllers.Main.loadFontTextText": "加載數據中...", "PE.Controllers.Main.loadFontTitleText": "加載數據中", "PE.Controllers.Main.loadImagesTextText": "正在載入圖片...", "PE.Controllers.Main.loadImagesTitleText": "正在載入圖片", "PE.Controllers.Main.loadImageTextText": "正在載入圖片...", "PE.Controllers.Main.loadImageTitleText": "正在載入圖片", "PE.Controllers.Main.loadingDocumentTextText": "正在載入簡報...", "PE.Controllers.Main.loadingDocumentTitleText": "正在載入簡報", "PE.Controllers.Main.loadThemeTextText": "載入主題中...", "PE.Controllers.Main.loadThemeTitleText": "載入主題中", "PE.Controllers.Main.notcriticalErrorTitle": "警告", "PE.Controllers.Main.openErrorText": "開啟檔案時發生錯誤", "PE.Controllers.Main.openTextText": "開幕演講...", "PE.Controllers.Main.openTitleText": "開幕演講", "PE.Controllers.Main.printTextText": "列印簡報...", "PE.Controllers.Main.printTitleText": "列印簡報", "PE.Controllers.Main.reloadButtonText": "重新載入頁面", "PE.Controllers.Main.requestEditFailedMessageText": "有人正在編輯此演示文稿。請稍後再試。", "PE.Controllers.Main.requestEditFailedTitleText": "存取被拒", "PE.Controllers.Main.saveErrorText": "儲存檔案時發生錯誤", "PE.Controllers.Main.saveErrorTextDesktop": "無法保存或創建此文件。<br>可能的原因是：<br> 1。該文件是只讀的。 <br> 2。該文件正在由其他用戶編輯。 <br> 3。磁碟已滿或損壞。", "PE.Controllers.Main.saveTextText": "正在保存演示文稿...", "PE.Controllers.Main.saveTitleText": "保存演示文稿", "PE.Controllers.Main.scriptLoadError": "連接速度太慢，某些組件無法加載。請重新加載頁面。", "PE.Controllers.Main.splitDividerErrorText": "行數必須是％1的除數。", "PE.Controllers.Main.splitMaxColsErrorText": "列數必須少於％1。", "PE.Controllers.Main.splitMaxRowsErrorText": "行數必須少於％1。", "PE.Controllers.Main.textAnonymous": "匿名", "PE.Controllers.Main.textApplyAll": "適用於所有方程式", "PE.Controllers.Main.textBuyNow": "訪問網站", "PE.Controllers.Main.textChangesSaved": "所有更改已保存", "PE.Controllers.Main.textClose": "關閉", "PE.Controllers.Main.textCloseTip": "點擊關閉提示", "PE.Controllers.Main.textContactUs": "聯絡銷售人員", "PE.Controllers.Main.textConvertEquation": "該方程式是使用不再受支持的方程式編輯器的舊版本創建的。要對其進行編輯，請將等式轉換為Office Math ML格式。<br>立即轉換？", "PE.Controllers.Main.textCustomLoader": "請注意，根據許可條款，您無權更換裝載機。<br>請聯繫我們的銷售部門以獲取報價。", "PE.Controllers.Main.textDisconnect": "失去網絡連接", "PE.Controllers.Main.textGuest": "來賓帳戶", "PE.Controllers.Main.textHasMacros": "此檔案包含自動的", "PE.Controllers.Main.textLearnMore": "了解更多", "PE.Controllers.Main.textLoadingDocument": "正在載入簡報", "PE.Controllers.Main.textLongName": "輸入少於128個字符的名稱。", "PE.Controllers.Main.textNoLicenseTitle": "達到許可限制", "PE.Controllers.Main.textPaidFeature": "付費功能", "PE.Controllers.Main.textReconnect": "連線恢復", "PE.Controllers.Main.textRemember": "記住我對所有文件的選擇", "PE.Controllers.Main.textRememberMacros": "記住我所有巨集的選擇", "PE.Controllers.Main.textRenameError": "使用者名稱無法留空。", "PE.Controllers.Main.textRenameLabel": "輸入合作名稱", "PE.Controllers.Main.textRequestMacros": "有一個巨集指令要求連結至URL。是否允許該要求至%1?", "PE.Controllers.Main.textShape": "形狀", "PE.Controllers.Main.textStrict": "嚴格模式", "PE.Controllers.Main.textText": "文字", "PE.Controllers.Main.textTryUndoRedo": "快速共同編輯模式禁用了“撤消/重做”功能。<br>單擊“嚴格模式”按鈕切換到“嚴格共同編輯”模式以編輯文件而不會受到其他用戶的干擾，並且僅在保存後發送更改他們。您可以使用編輯器的“高級”設置在共同編輯模式之間切換。", "PE.Controllers.Main.textTryUndoRedoWarn": "在快速共同編輯模式下，撤消/重做功能被禁用。", "PE.Controllers.Main.titleLicenseExp": "證件過期", "PE.Controllers.Main.titleServerVersion": "編輯器已更新", "PE.Controllers.Main.txtAddFirstSlide": "點擊以新增第一張投影片", "PE.Controllers.Main.txtAddNotes": "點擊添加筆記", "PE.Controllers.Main.txtArt": "在這輸入文字", "PE.Controllers.Main.txtBasicShapes": "基本形狀", "PE.Controllers.Main.txtButtons": "按鈕", "PE.Controllers.Main.txtCallouts": "標註", "PE.Controllers.Main.txtCharts": "圖表", "PE.Controllers.Main.txtClipArt": "剪貼畫", "PE.Controllers.Main.txtDateTime": "日期和時間", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "圖表標題", "PE.Controllers.Main.txtEditingMode": "設定編輯模式...", "PE.Controllers.Main.txtErrorLoadHistory": "歷史記錄加載失敗", "PE.Controllers.Main.txtFiguredArrows": "圖箭", "PE.Controllers.Main.txtFooter": "頁尾", "PE.Controllers.Main.txtHeader": "標頭", "PE.Controllers.Main.txtImage": "圖像", "PE.Controllers.Main.txtLines": "線數", "PE.Controllers.Main.txtLoading": "載入中...", "PE.Controllers.Main.txtMath": "數學", "PE.Controllers.Main.txtMedia": "媒體", "PE.Controllers.Main.txtNeedSynchronize": "您有更新", "PE.Controllers.Main.txtNone": "無", "PE.Controllers.Main.txtPicture": "圖片", "PE.Controllers.Main.txtRectangles": "長方形", "PE.Controllers.Main.txtSeries": "系列", "PE.Controllers.Main.txtShape_accentBorderCallout1": "線路標註1（邊框和強調欄）", "PE.Controllers.Main.txtShape_accentBorderCallout2": "線路標註2（邊框和強調欄）", "PE.Controllers.Main.txtShape_accentBorderCallout3": "線路標註3（邊框和強調欄）", "PE.Controllers.Main.txtShape_accentCallout1": "線路標註1（強調欄）", "PE.Controllers.Main.txtShape_accentCallout2": "線路標註2（強調欄）", "PE.Controllers.Main.txtShape_accentCallout3": "線路標註3（強調欄）", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "後退或上一步按鈕", "PE.Controllers.Main.txtShape_actionButtonBeginning": "開始按鈕", "PE.Controllers.Main.txtShape_actionButtonBlank": "空白按鈕", "PE.Controllers.Main.txtShape_actionButtonDocument": "文件按鈕", "PE.Controllers.Main.txtShape_actionButtonEnd": "結束按鈕", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "前進或後退按鈕", "PE.Controllers.Main.txtShape_actionButtonHelp": "幫助按鈕", "PE.Controllers.Main.txtShape_actionButtonHome": "首頁按鈕", "PE.Controllers.Main.txtShape_actionButtonInformation": "資訊按鈕", "PE.Controllers.Main.txtShape_actionButtonMovie": "電影按鈕", "PE.Controllers.Main.txtShape_actionButtonReturn": "返回按鈕", "PE.Controllers.Main.txtShape_actionButtonSound": "聲音按鈕", "PE.Controllers.Main.txtShape_arc": "弧", "PE.Controllers.Main.txtShape_bentArrow": "彎曲箭頭", "PE.Controllers.Main.txtShape_bentConnector5": "彎頭接頭", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "彎頭箭頭連接器", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "彎頭雙箭頭連接器", "PE.Controllers.Main.txtShape_bentUpArrow": "向上彎曲箭頭", "PE.Controllers.Main.txtShape_bevel": "斜角", "PE.Controllers.Main.txtShape_blockArc": "圓弧", "PE.Controllers.Main.txtShape_borderCallout1": "線路標註1", "PE.Controllers.Main.txtShape_borderCallout2": "線路標註2", "PE.Controllers.Main.txtShape_borderCallout3": "線路標註3", "PE.Controllers.Main.txtShape_bracePair": "雙括號", "PE.Controllers.Main.txtShape_callout1": "線路標註1（無邊框）", "PE.Controllers.Main.txtShape_callout2": "線路標註2（無邊框）", "PE.Controllers.Main.txtShape_callout3": "線路標註3（無邊框）", "PE.Controllers.Main.txtShape_can": "能夠", "PE.Controllers.Main.txtShape_chevron": "雪佛龍", "PE.Controllers.Main.txtShape_chord": "弦", "PE.Controllers.Main.txtShape_circularArrow": "圓形箭頭", "PE.Controllers.Main.txtShape_cloud": "雲", "PE.Controllers.Main.txtShape_cloudCallout": "雲標註", "PE.Controllers.Main.txtShape_corner": "角", "PE.Controllers.Main.txtShape_cube": "　立方體", "PE.Controllers.Main.txtShape_curvedConnector3": "彎曲連接器", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "彎曲箭頭連接器", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "彎曲雙箭頭連接器", "PE.Controllers.Main.txtShape_curvedDownArrow": "彎曲的向下箭頭", "PE.Controllers.Main.txtShape_curvedLeftArrow": "彎曲的左箭頭", "PE.Controllers.Main.txtShape_curvedRightArrow": "彎曲的右箭頭", "PE.Controllers.Main.txtShape_curvedUpArrow": "彎曲的向上箭頭", "PE.Controllers.Main.txtShape_decagon": "十邊形", "PE.Controllers.Main.txtShape_diagStripe": "斜條紋", "PE.Controllers.Main.txtShape_diamond": "鑽石", "PE.Controllers.Main.txtShape_dodecagon": "十二邊形", "PE.Controllers.Main.txtShape_donut": "甜甜圈", "PE.Controllers.Main.txtShape_doubleWave": "雙波", "PE.Controllers.Main.txtShape_downArrow": "下箭頭", "PE.Controllers.Main.txtShape_downArrowCallout": "向下箭頭標註", "PE.Controllers.Main.txtShape_ellipse": "橢圓", "PE.Controllers.Main.txtShape_ellipseRibbon": "彎下絲帶", "PE.Controllers.Main.txtShape_ellipseRibbon2": "彎曲絲帶", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程圖：替代過程", "PE.Controllers.Main.txtShape_flowChartCollate": "流程圖：整理", "PE.Controllers.Main.txtShape_flowChartConnector": "流程圖：連接器", "PE.Controllers.Main.txtShape_flowChartDecision": "流程圖：決策", "PE.Controllers.Main.txtShape_flowChartDelay": "流程圖：延遲", "PE.Controllers.Main.txtShape_flowChartDisplay": "流程圖：顯示", "PE.Controllers.Main.txtShape_flowChartDocument": "流程圖：文件", "PE.Controllers.Main.txtShape_flowChartExtract": "流程圖：提取", "PE.Controllers.Main.txtShape_flowChartInputOutput": "流程圖：數據", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "流程圖：內部存儲", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程圖：磁碟", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程圖：直接存取存儲", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "流程圖：順序存取存儲", "PE.Controllers.Main.txtShape_flowChartManualInput": "流程圖：手動輸入", "PE.Controllers.Main.txtShape_flowChartManualOperation": "流程圖：手動操作", "PE.Controllers.Main.txtShape_flowChartMerge": "流程圖：合併", "PE.Controllers.Main.txtShape_flowChartMultidocument": "流程圖：多文檔", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程圖：頁外連接器", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程圖：存儲的數據", "PE.Controllers.Main.txtShape_flowChartOr": "流程圖：或", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程圖：預定義流程", "PE.Controllers.Main.txtShape_flowChartPreparation": "流程圖：準備", "PE.Controllers.Main.txtShape_flowChartProcess": "流程圖：流程", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "流程圖：卡", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "流程圖：穿孔紙帶", "PE.Controllers.Main.txtShape_flowChartSort": "流程圖：排序", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "流程圖：求和結點", "PE.Controllers.Main.txtShape_flowChartTerminator": "流程圖：終結者", "PE.Controllers.Main.txtShape_foldedCorner": "折角", "PE.Controllers.Main.txtShape_frame": "框", "PE.Controllers.Main.txtShape_halfFrame": "半框", "PE.Controllers.Main.txtShape_heart": "心", "PE.Controllers.Main.txtShape_heptagon": "七邊形", "PE.Controllers.Main.txtShape_hexagon": "六邊形", "PE.Controllers.Main.txtShape_homePlate": "五角形", "PE.Controllers.Main.txtShape_horizontalScroll": "水平滾動", "PE.Controllers.Main.txtShape_irregularSeal1": "爆炸1", "PE.Controllers.Main.txtShape_irregularSeal2": "爆炸2", "PE.Controllers.Main.txtShape_leftArrow": "左箭頭", "PE.Controllers.Main.txtShape_leftArrowCallout": "向左箭頭標註", "PE.Controllers.Main.txtShape_leftBrace": "左括號", "PE.Controllers.Main.txtShape_leftBracket": "左括號", "PE.Controllers.Main.txtShape_leftRightArrow": "左右箭頭", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭頭標註", "PE.Controllers.Main.txtShape_leftRightUpArrow": "左右上箭頭", "PE.Controllers.Main.txtShape_leftUpArrow": "左上箭頭", "PE.Controllers.Main.txtShape_lightningBolt": "閃電", "PE.Controllers.Main.txtShape_line": "線", "PE.Controllers.Main.txtShape_lineWithArrow": "箭頭", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "雙箭頭", "PE.Controllers.Main.txtShape_mathDivide": "分裂", "PE.Controllers.Main.txtShape_mathEqual": "等於", "PE.Controllers.Main.txtShape_mathMinus": "減去", "PE.Controllers.Main.txtShape_mathMultiply": "乘", "PE.Controllers.Main.txtShape_mathNotEqual": "不平等", "PE.Controllers.Main.txtShape_mathPlus": "加", "PE.Controllers.Main.txtShape_moon": "月亮", "PE.Controllers.Main.txtShape_noSmoking": "“否”符號", "PE.Controllers.Main.txtShape_notchedRightArrow": "缺口右箭頭", "PE.Controllers.Main.txtShape_octagon": "八邊形", "PE.Controllers.Main.txtShape_parallelogram": "平行四邊形", "PE.Controllers.Main.txtShape_pentagon": "五角形", "PE.Controllers.Main.txtShape_pie": "餅", "PE.Controllers.Main.txtShape_plaque": "簽名", "PE.Controllers.Main.txtShape_plus": "加", "PE.Controllers.Main.txtShape_polyline1": "塗", "PE.Controllers.Main.txtShape_polyline2": "自由形式", "PE.Controllers.Main.txtShape_quadArrow": "四箭頭", "PE.Controllers.Main.txtShape_quadArrowCallout": "四箭頭標註", "PE.Controllers.Main.txtShape_rect": "長方形", "PE.Controllers.Main.txtShape_ribbon": "下絨帶", "PE.Controllers.Main.txtShape_ribbon2": "上色帶", "PE.Controllers.Main.txtShape_rightArrow": "右箭頭", "PE.Controllers.Main.txtShape_rightArrowCallout": "右箭頭標註", "PE.Controllers.Main.txtShape_rightBrace": "右括號", "PE.Controllers.Main.txtShape_rightBracket": "右括號", "PE.Controllers.Main.txtShape_round1Rect": "圓形單角矩形", "PE.Controllers.Main.txtShape_round2DiagRect": "圓斜角矩形", "PE.Controllers.Main.txtShape_round2SameRect": "圓同一邊角矩形", "PE.Controllers.Main.txtShape_roundRect": "圓角矩形", "PE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "PE.Controllers.Main.txtShape_smileyFace": "笑臉", "PE.Controllers.Main.txtShape_snip1Rect": "剪斷單角矩形", "PE.Controllers.Main.txtShape_snip2DiagRect": "剪裁對角線矩形", "PE.Controllers.Main.txtShape_snip2SameRect": "剪斷同一邊角矩形", "PE.Controllers.Main.txtShape_snipRoundRect": "剪斷和圓形單角矩形", "PE.Controllers.Main.txtShape_spline": "曲線", "PE.Controllers.Main.txtShape_star10": "十點星", "PE.Controllers.Main.txtShape_star12": "十二點星", "PE.Controllers.Main.txtShape_star16": "十六點星", "PE.Controllers.Main.txtShape_star24": "24點星", "PE.Controllers.Main.txtShape_star32": "32點星", "PE.Controllers.Main.txtShape_star4": "4點星", "PE.Controllers.Main.txtShape_star5": "5點星", "PE.Controllers.Main.txtShape_star6": "6點星", "PE.Controllers.Main.txtShape_star7": "7點星", "PE.Controllers.Main.txtShape_star8": "8點星", "PE.Controllers.Main.txtShape_stripedRightArrow": "條紋右箭頭", "PE.Controllers.Main.txtShape_sun": "太陽", "PE.Controllers.Main.txtShape_teardrop": "淚珠", "PE.Controllers.Main.txtShape_textRect": "文字框", "PE.Controllers.Main.txtShape_trapezoid": "梯形", "PE.Controllers.Main.txtShape_triangle": "三角形", "PE.Controllers.Main.txtShape_upArrow": "向上箭頭", "PE.Controllers.Main.txtShape_upArrowCallout": "向上箭頭標註", "PE.Controllers.Main.txtShape_upDownArrow": "上下箭頭", "PE.Controllers.Main.txtShape_uturnArrow": "掉頭箭頭", "PE.Controllers.Main.txtShape_verticalScroll": "垂直滾動", "PE.Controllers.Main.txtShape_wave": "波", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "橢圓形標註", "PE.Controllers.Main.txtShape_wedgeRectCallout": "矩形標註", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圓角矩形標註", "PE.Controllers.Main.txtSldLtTBlank": "空白", "PE.Controllers.Main.txtSldLtTChart": "圖表", "PE.Controllers.Main.txtSldLtTChartAndTx": "圖表和文字", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "剪貼畫和文字", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "剪貼畫和垂直文字", "PE.Controllers.Main.txtSldLtTCust": "自訂", "PE.Controllers.Main.txtSldLtTDgm": "圖表", "PE.Controllers.Main.txtSldLtTFourObj": "四個對象", "PE.Controllers.Main.txtSldLtTMediaAndTx": "媒體和文字", "PE.Controllers.Main.txtSldLtTObj": "標題和物件", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "物件和兩個物件", "PE.Controllers.Main.txtSldLtTObjAndTx": "物件和文字", "PE.Controllers.Main.txtSldLtTObjOnly": "物件", "PE.Controllers.Main.txtSldLtTObjOverTx": "物件覆蓋文字", "PE.Controllers.Main.txtSldLtTObjTx": "標題，物件和標題", "PE.Controllers.Main.txtSldLtTPicTx": "圖片和標題", "PE.Controllers.Main.txtSldLtTSecHead": "區塊標題", "PE.Controllers.Main.txtSldLtTTbl": "表格", "PE.Controllers.Main.txtSldLtTTitle": "標題", "PE.Controllers.Main.txtSldLtTTitleOnly": "僅標題", "PE.Controllers.Main.txtSldLtTTwoColTx": "兩欄文字", "PE.Controllers.Main.txtSldLtTTwoObj": "兩個物件", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "兩個物件和物件", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "兩個物件和文字", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "兩個物件覆蓋文字", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "兩個文字和兩個物件", "PE.Controllers.Main.txtSldLtTTx": "文字", "PE.Controllers.Main.txtSldLtTTxAndChart": "文字和圖表", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "文字和剪貼畫", "PE.Controllers.Main.txtSldLtTTxAndMedia": "文字和媒體", "PE.Controllers.Main.txtSldLtTTxAndObj": "文字和物件", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "文字和兩個物件", "PE.Controllers.Main.txtSldLtTTxOverObj": "文字覆蓋物件", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "垂直標題和文字", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "垂直標題和圖表上方的文字", "PE.Controllers.Main.txtSldLtTVertTx": "垂直文字", "PE.Controllers.Main.txtSlideNumber": "投影片頁碼", "PE.Controllers.Main.txtSlideSubtitle": "投影片副標題", "PE.Controllers.Main.txtSlideText": "投影片字幕", "PE.Controllers.Main.txtSlideTitle": "投影片標題", "PE.Controllers.Main.txtStarsRibbons": "星星和絲帶", "PE.Controllers.Main.txtTheme_basic": "基本的", "PE.Controllers.Main.txtTheme_blank": "空白", "PE.Controllers.Main.txtTheme_classic": "經典", "PE.Controllers.Main.txtTheme_corner": "角", "PE.Controllers.Main.txtTheme_dotted": "點", "PE.Controllers.Main.txtTheme_green": "綠色", "PE.Controllers.Main.txtTheme_green_leaf": "綠葉", "PE.Controllers.Main.txtTheme_lines": "線數", "PE.Controllers.Main.txtTheme_office": "辦公室", "PE.Controllers.Main.txtTheme_office_theme": "辦公室主題", "PE.Controllers.Main.txtTheme_official": "官方", "PE.Controllers.Main.txtTheme_pixel": "像素點", "PE.Controllers.Main.txtTheme_safari": "蘋果瀏覽器", "PE.Controllers.Main.txtTheme_turtle": "龜", "PE.Controllers.Main.txtXAxis": "X軸", "PE.Controllers.Main.txtYAxis": "Y軸", "PE.Controllers.Main.unknownErrorText": "未知錯誤。", "PE.Controllers.Main.unsupportedBrowserErrorText": "不支援您的瀏覽器", "PE.Controllers.Main.uploadImageExtMessage": "圖片格式未知。", "PE.Controllers.Main.uploadImageFileCountMessage": "沒有上傳圖片。", "PE.Controllers.Main.uploadImageSizeMessage": "圖像超出最大大小限制。最大大小為25MB。", "PE.Controllers.Main.uploadImageTextText": "正在上傳圖片...", "PE.Controllers.Main.uploadImageTitleText": "上載圖片", "PE.Controllers.Main.waitText": "請耐心等待...", "PE.Controllers.Main.warnBrowserIE9": "該應用程序在IE9上具有較低的功能。使用IE10或更高版本", "PE.Controllers.Main.warnBrowserZoom": "瀏覽器當前的縮放設置不受完全支持。請按Ctrl + 0重置為預設縮放。", "PE.Controllers.Main.warnLicenseExceeded": "您的系統已經達到同時編輯連線的 %1 編輯者。只能以檢視模式開啟此文件。<br> 進一步訊息, 請聯繫您的管理者。", "PE.Controllers.Main.warnLicenseExp": "您的授權證已過期.<br>請更新您的授權證並重新整理頁面。", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "授權過期<br>您已沒有編輯文件功能的授權<br> 請與您的管理者聯繫。", "PE.Controllers.Main.warnLicenseLimitedRenewed": "授權證書需要更新<br> 您只有部分的文件編輯功能的存取權限<br>請與您的管理者聯繫來取得完整的存取權限。", "PE.Controllers.Main.warnLicenseUsersExceeded": "您已達到％1個編輯器的用戶限制。請與您的管理員聯繫以了解更多信息。", "PE.Controllers.Main.warnNoLicense": "您的系統已經達到同時編輯連線的 %1 編輯者。只能以檢視模式開啟此文件。<br> 請聯繫 %1 銷售團隊來取得個人升級的需求。", "PE.Controllers.Main.warnNoLicenseUsers": "您已達到％1個編輯器的用戶限制。與％1銷售團隊聯繫以了解個人升級條款。", "PE.Controllers.Main.warnProcessRightsChange": "您被拒絕編輯文件的權利。", "PE.Controllers.Search.notcriticalErrorTitle": "警告", "PE.Controllers.Search.textNoTextFound": "找不到您一直在搜索的數據。請調整您的搜索選項。", "PE.Controllers.Search.textReplaceSkipped": "替換已完成。 {0}個事件被跳過。", "PE.Controllers.Search.textReplaceSuccess": "搜尋完成。 {0}個符合結果已被取代", "PE.Controllers.Search.warnReplaceString": "{0}不是有效的字元", "PE.Controllers.Statusbar.textDisconnect": "<b>連線失敗</b><br>正在嘗試連線。請檢查網路連線設定。", "PE.Controllers.Statusbar.zoomText": "放大{0}%", "PE.Controllers.Toolbar.confirmAddFontName": "您要保存的字體在當前設備上不可用。<br>文本樣式將使用一種系統字體顯示，保存的字體將在可用時使用。<br>要繼續嗎？ ？", "PE.Controllers.Toolbar.textAccent": "強調", "PE.Controllers.Toolbar.textBracket": "括號", "PE.Controllers.Toolbar.textEmptyImgUrl": "您必須輸入圖檔的URL.", "PE.Controllers.Toolbar.textFontSizeErr": "輸入的值不正確。<br>請輸入1到300之間的數字值", "PE.Controllers.Toolbar.textFraction": "分數", "PE.Controllers.Toolbar.textFunction": "功能", "PE.Controllers.Toolbar.textInsert": "插入", "PE.Controllers.Toolbar.textIntegral": "積分", "PE.Controllers.Toolbar.textLargeOperator": "大型運營商", "PE.Controllers.Toolbar.textLimitAndLog": "極限和對數", "PE.Controllers.Toolbar.textMatrix": "矩陣", "PE.Controllers.Toolbar.textOperator": "經營者", "PE.Controllers.Toolbar.textRadical": "激進單數", "PE.Controllers.Toolbar.textScript": "腳本", "PE.Controllers.Toolbar.textSymbols": "符號", "PE.Controllers.Toolbar.textWarning": "警告", "PE.Controllers.Toolbar.txtAccent_Accent": "尖銳", "PE.Controllers.Toolbar.txtAccent_ArrowD": "上方的左右箭頭", "PE.Controllers.Toolbar.txtAccent_ArrowL": "上方的向左箭頭", "PE.Controllers.Toolbar.txtAccent_ArrowR": "上方向右箭頭", "PE.Controllers.Toolbar.txtAccent_Bar": "槓", "PE.Controllers.Toolbar.txtAccent_BarBot": "底橫槓", "PE.Controllers.Toolbar.txtAccent_BarTop": "橫槓", "PE.Controllers.Toolbar.txtAccent_BorderBox": "盒裝公式（帶佔位符）", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "盒裝公式（示例）", "PE.Controllers.Toolbar.txtAccent_Check": "檢查", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "底括號", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "大括號", "PE.Controllers.Toolbar.txtAccent_Custom_1": "向量A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "帶橫線的ABC", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x X或y與橫槓", "PE.Controllers.Toolbar.txtAccent_DDDot": "三點", "PE.Controllers.Toolbar.txtAccent_DDot": "雙點", "PE.Controllers.Toolbar.txtAccent_Dot": "點", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "雙橫槓", "PE.Controllers.Toolbar.txtAccent_Grave": "墓", "PE.Controllers.Toolbar.txtAccent_GroupBot": "下面的分組字符", "PE.Controllers.Toolbar.txtAccent_GroupTop": "上面的分組字符", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "上方的向左魚叉", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "右上方的魚叉", "PE.Controllers.Toolbar.txtAccent_Hat": "帽子", "PE.Controllers.Toolbar.txtAccent_Smile": "布雷夫", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "括號", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "帶分隔符的括號", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "帶分隔符的括號", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_Curve": "括號", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "帶分隔符的括號", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_Custom_1": "案件（兩件條件）", "PE.Controllers.Toolbar.txtBracket_Custom_2": "案件（三件條件）", "PE.Controllers.Toolbar.txtBracket_Custom_3": "堆疊物件", "PE.Controllers.Toolbar.txtBracket_Custom_4": "堆疊物件", "PE.Controllers.Toolbar.txtBracket_Custom_5": "案件例子", "PE.Controllers.Toolbar.txtBracket_Custom_6": "二項式係數", "PE.Controllers.Toolbar.txtBracket_Custom_7": "二項式係數", "PE.Controllers.Toolbar.txtBracket_Line": "括號", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_LineDouble": "括號", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_LowLim": "括號", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "單括號", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_Round": "括號", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "帶分隔符的括號", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_Square": "括號", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括號", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括號", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括號", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "括號", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "單括號", "PE.Controllers.Toolbar.txtBracket_UppLim": "括號", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "單括號", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "單括號", "PE.Controllers.Toolbar.txtFractionDiagonal": "偏斜分數", "PE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "PE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "PE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "PE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "PE.Controllers.Toolbar.txtFractionHorizontal": "線性分數", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi超過2", "PE.Controllers.Toolbar.txtFractionSmall": "小分數", "PE.Controllers.Toolbar.txtFractionVertical": "堆積分數", "PE.Controllers.Toolbar.txtFunction_1_Cos": "反餘弦函數", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "雙曲餘弦函數", "PE.Controllers.Toolbar.txtFunction_1_Cot": "反正切函數", "PE.Controllers.Toolbar.txtFunction_1_Coth": "雙曲反正切函數", "PE.Controllers.Toolbar.txtFunction_1_Csc": "餘割函數反", "PE.Controllers.Toolbar.txtFunction_1_Csch": "雙曲反餘割函數", "PE.Controllers.Toolbar.txtFunction_1_Sec": "反割線功能", "PE.Controllers.Toolbar.txtFunction_1_Sech": "雙曲反正割函數", "PE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦函數", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "雙曲反正弦函數", "PE.Controllers.Toolbar.txtFunction_1_Tan": "反正切函數", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "雙曲反正切函數", "PE.Controllers.Toolbar.txtFunction_Cos": "Cosine 函數", "PE.Controllers.Toolbar.txtFunction_Cosh": "雙曲餘弦函數", "PE.Controllers.Toolbar.txtFunction_Cot": "Cotangent 函數", "PE.Controllers.Toolbar.txtFunction_Coth": "雙曲餘切函數", "PE.Controllers.Toolbar.txtFunction_Csc": "餘割函數", "PE.Controllers.Toolbar.txtFunction_Csch": "雙曲餘割函數", "PE.Controllers.Toolbar.txtFunction_Custom_1": "正弦波", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "切線公式", "PE.Controllers.Toolbar.txtFunction_Sec": "正割功能", "PE.Controllers.Toolbar.txtFunction_Sech": "雙曲正割函數", "PE.Controllers.Toolbar.txtFunction_Sin": "正弦函數", "PE.Controllers.Toolbar.txtFunction_Sinh": "雙曲正弦函數", "PE.Controllers.Toolbar.txtFunction_Tan": "切線公式", "PE.Controllers.Toolbar.txtFunction_Tanh": "雙曲正切函數", "PE.Controllers.Toolbar.txtIntegral": "積分", "PE.Controllers.Toolbar.txtIntegral_dtheta": "微分塞塔", "PE.Controllers.Toolbar.txtIntegral_dx": "差分　x", "PE.Controllers.Toolbar.txtIntegral_dy": "差分　y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "積分", "PE.Controllers.Toolbar.txtIntegralDouble": "雙積分", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "雙積分", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "雙積分", "PE.Controllers.Toolbar.txtIntegralOriented": "輪廓積分", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "輪廓積分", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "表面積分", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "表面積分", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "表面積分", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "輪廓積分", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "體積積分", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "體積積分", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "體積積分", "PE.Controllers.Toolbar.txtIntegralSubSup": "積分", "PE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重積分", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重積分", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "楔", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "聯產品", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "聯產品", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "聯產品", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "聯產品", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "聯產品", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "產品", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "聯合", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "交叉點", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交叉點", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交叉點", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交叉點", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交叉點", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "產品", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "產品", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "產品", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "產品", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "產品", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "求和", "PE.Controllers.Toolbar.txtLargeOperator_Union": "聯合", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "聯合", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "聯合", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "聯合", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "聯合", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "限制例子", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大例子", "PE.Controllers.Toolbar.txtLimitLog_Lim": "限制", "PE.Controllers.Toolbar.txtLimitLog_Ln": "自然對數", "PE.Controllers.Toolbar.txtLimitLog_Log": "對數", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "對數", "PE.Controllers.Toolbar.txtLimitLog_Max": "最大", "PE.Controllers.Toolbar.txtLimitLog_Min": "最低", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2的空矩陣", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 空矩陣", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空矩陣", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空矩陣", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "帶括號的空矩陣", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "帶括號的空矩陣", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "帶括號的空矩陣", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "帶括號的空矩陣", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空矩陣", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空矩陣", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空矩陣", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空矩陣", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基準點", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "中線點", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "對角點", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直點", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "稀疏矩陣", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "稀疏矩陣", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 單位矩陣", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 單位矩陣", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 單位矩陣", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 單位矩陣", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "下方的左右箭頭", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "上方的左右箭頭", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "下方的向左箭頭", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "上方的向左箭頭", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下方向右箭頭", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "上方向右箭頭", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "冒號相等", "PE.Controllers.Toolbar.txtOperator_Custom_1": "產量", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta 收益", "PE.Controllers.Toolbar.txtOperator_Definition": "等同於定義", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta 等於", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "下方的左右箭頭", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "上方的左右箭頭", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "下方的向左箭頭", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "上方的向左箭頭", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下方向右箭頭", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "上方向右箭頭", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "等於 等於", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "負等於", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "加等於", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "測量者", "PE.Controllers.Toolbar.txtRadicalCustom_1": "激進", "PE.Controllers.Toolbar.txtRadicalCustom_2": "激進", "PE.Controllers.Toolbar.txtRadicalRoot_2": "平方根", "PE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "PE.Controllers.Toolbar.txtRadicalRoot_n": "自由基度", "PE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "PE.Controllers.Toolbar.txtScriptCustom_1": "腳本", "PE.Controllers.Toolbar.txtScriptCustom_2": "腳本", "PE.Controllers.Toolbar.txtScriptCustom_3": "腳本", "PE.Controllers.Toolbar.txtScriptCustom_4": "腳本", "PE.Controllers.Toolbar.txtScriptSub": "下標", "PE.Controllers.Toolbar.txtScriptSubSup": "下標-上標", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "左下標-上標", "PE.Controllers.Toolbar.txtScriptSup": "上標", "PE.Controllers.Toolbar.txtSymbol_about": "大約", "PE.Controllers.Toolbar.txtSymbol_additional": "補充", "PE.Controllers.Toolbar.txtSymbol_aleph": "Ａ", "PE.Controllers.Toolbar.txtSymbol_alpha": "Α", "PE.Controllers.Toolbar.txtSymbol_approx": "幾乎等於", "PE.Controllers.Toolbar.txtSymbol_ast": "星號運算符", "PE.Controllers.Toolbar.txtSymbol_beta": "貝塔", "PE.Controllers.Toolbar.txtSymbol_beth": "賭注", "PE.Controllers.Toolbar.txtSymbol_bullet": "項目點操作者", "PE.Controllers.Toolbar.txtSymbol_cap": "交叉點", "PE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "PE.Controllers.Toolbar.txtSymbol_cdots": "中線水平省略號", "PE.Controllers.Toolbar.txtSymbol_celsius": "攝氏度", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "大約等於", "PE.Controllers.Toolbar.txtSymbol_cup": "聯合", "PE.Controllers.Toolbar.txtSymbol_ddots": "右下斜省略號", "PE.Controllers.Toolbar.txtSymbol_degree": "度", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "分裂標誌", "PE.Controllers.Toolbar.txtSymbol_downarrow": "下箭頭", "PE.Controllers.Toolbar.txtSymbol_emptyset": "空組集", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "等於", "PE.Controllers.Toolbar.txtSymbol_equiv": "相同", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "存在", "PE.Controllers.Toolbar.txtSymbol_factorial": "階乘", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏度", "PE.Controllers.Toolbar.txtSymbol_forall": "對所有人", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "大於或等於", "PE.Controllers.Toolbar.txtSymbol_gg": "比大得多", "PE.Controllers.Toolbar.txtSymbol_greater": "更佳", "PE.Controllers.Toolbar.txtSymbol_in": "元素", "PE.Controllers.Toolbar.txtSymbol_inc": "增量", "PE.Controllers.Toolbar.txtSymbol_infinity": "無限", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "拉姆達", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭頭", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭頭", "PE.Controllers.Toolbar.txtSymbol_leq": "小於或等於", "PE.Controllers.Toolbar.txtSymbol_less": "少於", "PE.Controllers.Toolbar.txtSymbol_ll": "遠遠少於", "PE.Controllers.Toolbar.txtSymbol_minus": "減去", "PE.Controllers.Toolbar.txtSymbol_mp": "減加", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "不等於", "PE.Controllers.Toolbar.txtSymbol_ni": "包含為成員", "PE.Controllers.Toolbar.txtSymbol_not": "不簽名", "PE.Controllers.Toolbar.txtSymbol_notexists": "不存在", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "PE.Controllers.Toolbar.txtSymbol_percent": "百分比", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "加", "PE.Controllers.Toolbar.txtSymbol_pm": "加減", "PE.Controllers.Toolbar.txtSymbol_propto": "成比例", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "第四根", "PE.Controllers.Toolbar.txtSymbol_qed": "證明結束", "PE.Controllers.Toolbar.txtSymbol_rddots": "右上斜省略號", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭頭", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "激進標誌", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "因此", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "乘法符號", "PE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭頭", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon 變體", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi 變體", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi變體", "PE.Controllers.Toolbar.txtSymbol_varrho": "Rho變體", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma 變體", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Theta變體", "PE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略號", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "與投影片切合", "PE.Controllers.Viewport.textFitWidth": "切合至寬度", "PE.Views.Animation.str0_5": "0.5秒 (超快速)", "PE.Views.Animation.str1": "1秒 (快速)", "PE.Views.Animation.str2": "2秒 (中速)", "PE.Views.Animation.str20": "20秒 (超慢速)", "PE.Views.Animation.str3": "3秒 (慢速)", "PE.Views.Animation.str5": "5秒 (超慢速)", "PE.Views.Animation.strDelay": "延遲", "PE.Views.Animation.strDuration": "持續時間", "PE.Views.Animation.strRepeat": "重複", "PE.Views.Animation.strRewind": "倒退", "PE.Views.Animation.strStart": "開始", "PE.Views.Animation.strTrigger": "觸發程序", "PE.Views.Animation.textAutoPreview": "自動預覽", "PE.Views.Animation.textMoreEffects": "顯示其他特效", "PE.Views.Animation.textMoveEarlier": "向前移動", "PE.Views.Animation.textMoveLater": "向後移動", "PE.Views.Animation.textMultiple": "多項", "PE.Views.Animation.textNone": "無", "PE.Views.Animation.textNoRepeat": "（空）", "PE.Views.Animation.textOnClickOf": "點擊：", "PE.Views.Animation.textOnClickSequence": "一鍵排序", "PE.Views.Animation.textStartAfterPrevious": "在前一個動畫後", "PE.Views.Animation.textStartOnClick": "點擊", "PE.Views.Animation.textStartWithPrevious": "與上一個動畫一起", "PE.Views.Animation.textUntilEndOfSlide": "直到投影片最後", "PE.Views.Animation.textUntilNextClick": "直到下一次點擊", "PE.Views.Animation.txtAddEffect": "新增動畫", "PE.Views.Animation.txtAnimationPane": "動畫面版", "PE.Views.Animation.txtParameters": "參量", "PE.Views.Animation.txtPreview": "預覽", "PE.Views.Animation.txtSec": "秒", "PE.Views.AnimationDialog.textPreviewEffect": "預覽效果", "PE.Views.AnimationDialog.textTitle": "其他特效", "PE.Views.ChartSettings.textAdvanced": "顯示進階設定", "PE.Views.ChartSettings.textChartType": "更改圖表類型", "PE.Views.ChartSettings.textEditData": "編輯資料", "PE.Views.ChartSettings.textHeight": "高度", "PE.Views.ChartSettings.textKeepRatio": "比例不變", "PE.Views.ChartSettings.textSize": "大小", "PE.Views.ChartSettings.textStyle": "樣式", "PE.Views.ChartSettings.textWidth": "寬度", "PE.Views.ChartSettingsAdvanced.textAlt": "替代文字", "PE.Views.ChartSettingsAdvanced.textAltDescription": "描述", "PE.Views.ChartSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "PE.Views.ChartSettingsAdvanced.textAltTitle": "標題", "PE.Views.ChartSettingsAdvanced.textCenter": "中心", "PE.Views.ChartSettingsAdvanced.textFrom": "自", "PE.Views.ChartSettingsAdvanced.textHeight": "\n高度", "PE.Views.ChartSettingsAdvanced.textHorizontal": "水平的", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "比例不變", "PE.Views.ChartSettingsAdvanced.textPlacement": "放置", "PE.Views.ChartSettingsAdvanced.textPosition": "職務", "PE.Views.ChartSettingsAdvanced.textSize": "大小", "PE.Views.ChartSettingsAdvanced.textTitle": "圖表-進階設置", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.ChartSettingsAdvanced.textVertical": "垂直", "PE.Views.ChartSettingsAdvanced.textWidth": "寬度", "PE.Views.DateTimeDialog.confirmDefault": "設置{0}的預設格式：“ {1}”", "PE.Views.DateTimeDialog.textDefault": "設為預設", "PE.Views.DateTimeDialog.textFormat": "格式", "PE.Views.DateTimeDialog.textLang": "語言", "PE.Views.DateTimeDialog.textUpdate": "自動更新", "PE.Views.DateTimeDialog.txtTitle": "日期和時間", "PE.Views.DocumentHolder.aboveText": "以上", "PE.Views.DocumentHolder.addCommentText": "增加評論", "PE.Views.DocumentHolder.addToLayoutText": "添加到佈局", "PE.Views.DocumentHolder.advancedImageText": "圖像進階設置", "PE.Views.DocumentHolder.advancedParagraphText": "文字進階設定", "PE.Views.DocumentHolder.advancedShapeText": "形狀進階設定", "PE.Views.DocumentHolder.advancedTableText": "表格進階設定", "PE.Views.DocumentHolder.alignmentText": "對齊", "PE.Views.DocumentHolder.belowText": "之下", "PE.Views.DocumentHolder.cellAlignText": "單元格垂直對齊", "PE.Views.DocumentHolder.cellText": "單元格", "PE.Views.DocumentHolder.centerText": "中心", "PE.Views.DocumentHolder.columnText": "欄", "PE.Views.DocumentHolder.deleteColumnText": "刪除欄位", "PE.Views.DocumentHolder.deleteRowText": "刪除行列", "PE.Views.DocumentHolder.deleteTableText": "刪除表格", "PE.Views.DocumentHolder.deleteText": "刪除", "PE.Views.DocumentHolder.direct270Text": "向上旋轉文字", "PE.Views.DocumentHolder.direct90Text": "向下旋轉文字", "PE.Views.DocumentHolder.directHText": "水平", "PE.Views.DocumentHolder.directionText": "文字方向", "PE.Views.DocumentHolder.editChartText": "編輯資料", "PE.Views.DocumentHolder.editHyperlinkText": "編輯超連結", "PE.Views.DocumentHolder.hyperlinkText": "超連結", "PE.Views.DocumentHolder.ignoreAllSpellText": "忽略所有", "PE.Views.DocumentHolder.ignoreSpellText": "忽視", "PE.Views.DocumentHolder.insertColumnLeftText": "欄位以左", "PE.Views.DocumentHolder.insertColumnRightText": "欄位以右", "PE.Views.DocumentHolder.insertColumnText": "插入欄位", "PE.Views.DocumentHolder.insertRowAboveText": "上行", "PE.Views.DocumentHolder.insertRowBelowText": "下行", "PE.Views.DocumentHolder.insertRowText": "插入行", "PE.Views.DocumentHolder.insertText": "插入", "PE.Views.DocumentHolder.langText": "選擇語言", "PE.Views.DocumentHolder.leftText": "左", "PE.Views.DocumentHolder.loadSpellText": "正在加載變體...", "PE.Views.DocumentHolder.mergeCellsText": "合併單元格", "PE.Views.DocumentHolder.mniCustomTable": "插入自訂表格", "PE.Views.DocumentHolder.moreText": "更多變體...", "PE.Views.DocumentHolder.noSpellVariantsText": "沒有變體", "PE.Views.DocumentHolder.originalSizeText": "實際大小", "PE.Views.DocumentHolder.removeHyperlinkText": "刪除超連結", "PE.Views.DocumentHolder.rightText": "右", "PE.Views.DocumentHolder.rowText": "行", "PE.Views.DocumentHolder.selectText": "選擇", "PE.Views.DocumentHolder.spellcheckText": "拼字檢查", "PE.Views.DocumentHolder.splitCellsText": "分割儲存格...", "PE.Views.DocumentHolder.splitCellTitleText": "分割儲存格", "PE.Views.DocumentHolder.tableText": "表格", "PE.Views.DocumentHolder.textArrangeBack": "傳送到背景", "PE.Views.DocumentHolder.textArrangeBackward": "向後發送", "PE.Views.DocumentHolder.textArrangeForward": "向前帶進", "PE.Views.DocumentHolder.textArrangeFront": "移到前景", "PE.Views.DocumentHolder.textCopy": "複製", "PE.Views.DocumentHolder.textCrop": "修剪", "PE.Views.DocumentHolder.textCropFill": "填滿", "PE.Views.DocumentHolder.textCropFit": "切合", "PE.Views.DocumentHolder.textCut": "剪下", "PE.Views.DocumentHolder.textDistributeCols": "分配列", "PE.Views.DocumentHolder.textDistributeRows": "分配行", "PE.Views.DocumentHolder.textEditPoints": "編輯點", "PE.Views.DocumentHolder.textFlipH": "水平翻轉", "PE.Views.DocumentHolder.textFlipV": "垂直翻轉", "PE.Views.DocumentHolder.textFromFile": "從檔案", "PE.Views.DocumentHolder.textFromStorage": "從存儲", "PE.Views.DocumentHolder.textFromUrl": "從 URL", "PE.Views.DocumentHolder.textNextPage": "下一張投影片", "PE.Views.DocumentHolder.textPaste": "貼上", "PE.Views.DocumentHolder.textPrevPage": "上一張投影片", "PE.Views.DocumentHolder.textReplace": "取代圖片", "PE.Views.DocumentHolder.textRotate": "旋轉", "PE.Views.DocumentHolder.textRotate270": "逆時針旋轉90°", "PE.Views.DocumentHolder.textRotate90": "順時針旋轉90°", "PE.Views.DocumentHolder.textShapeAlignBottom": "底部對齊", "PE.Views.DocumentHolder.textShapeAlignCenter": "居中對齊", "PE.Views.DocumentHolder.textShapeAlignLeft": "對齊左側", "PE.Views.DocumentHolder.textShapeAlignMiddle": "中央對齊", "PE.Views.DocumentHolder.textShapeAlignRight": "對齊右側", "PE.Views.DocumentHolder.textShapeAlignTop": "上方對齊", "PE.Views.DocumentHolder.textSlideSettings": "投影片設定", "PE.Views.DocumentHolder.textUndo": "復原", "PE.Views.DocumentHolder.tipIsLocked": "該元素當前正在由另一個用戶編輯。", "PE.Views.DocumentHolder.toDictionaryText": "添加到字典", "PE.Views.DocumentHolder.txtAddBottom": "添加底部邊框", "PE.Views.DocumentHolder.txtAddFractionBar": "添加分數欄", "PE.Views.DocumentHolder.txtAddHor": "添加水平線", "PE.Views.DocumentHolder.txtAddLB": "添加左底邊框", "PE.Views.DocumentHolder.txtAddLeft": "添加左邊框", "PE.Views.DocumentHolder.txtAddLT": "添加左上頂行", "PE.Views.DocumentHolder.txtAddRight": "加入右邊框", "PE.Views.DocumentHolder.txtAddTop": "加入上邊框", "PE.Views.DocumentHolder.txtAddVer": "加入垂直線", "PE.Views.DocumentHolder.txtAlign": "對齊", "PE.Views.DocumentHolder.txtAlignToChar": "與字符對齊", "PE.Views.DocumentHolder.txtArrange": "安排", "PE.Views.DocumentHolder.txtBackground": "背景", "PE.Views.DocumentHolder.txtBorderProps": "邊框屬性", "PE.Views.DocumentHolder.txtBottom": "底部", "PE.Views.DocumentHolder.txtChangeLayout": "變更版面", "PE.Views.DocumentHolder.txtChangeTheme": "改變主題", "PE.Views.DocumentHolder.txtColumnAlign": "欄位對準", "PE.Views.DocumentHolder.txtDecreaseArg": "減小參數大小", "PE.Views.DocumentHolder.txtDeleteArg": "刪除參數", "PE.Views.DocumentHolder.txtDeleteBreak": "刪除手動休息", "PE.Views.DocumentHolder.txtDeleteChars": "刪除封閉字符", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "刪除括起來的字符和分隔符", "PE.Views.DocumentHolder.txtDeleteEq": "刪除方程式", "PE.Views.DocumentHolder.txtDeleteGroupChar": "刪除字元", "PE.Views.DocumentHolder.txtDeleteRadical": "刪除部首", "PE.Views.DocumentHolder.txtDeleteSlide": "刪除投影片", "PE.Views.DocumentHolder.txtDistribHor": "水平分佈", "PE.Views.DocumentHolder.txtDistribVert": "垂直分佈", "PE.Views.DocumentHolder.txtDuplicateSlide": "投影片複製", "PE.Views.DocumentHolder.txtFractionLinear": "更改為線性分數", "PE.Views.DocumentHolder.txtFractionSkewed": "更改為傾斜分數", "PE.Views.DocumentHolder.txtFractionStacked": "更改為堆積分數", "PE.Views.DocumentHolder.txtGroup": "群組", "PE.Views.DocumentHolder.txtGroupCharOver": "字符至文字的上方", "PE.Views.DocumentHolder.txtGroupCharUnder": "字符至文字的下方", "PE.Views.DocumentHolder.txtHideBottom": "隱藏底部邊框", "PE.Views.DocumentHolder.txtHideBottomLimit": "隱藏下限", "PE.Views.DocumentHolder.txtHideCloseBracket": "隱藏右括號", "PE.Views.DocumentHolder.txtHideDegree": "隱藏度", "PE.Views.DocumentHolder.txtHideHor": "隱藏水平線", "PE.Views.DocumentHolder.txtHideLB": "隱藏左底線", "PE.Views.DocumentHolder.txtHideLeft": "隱藏左邊框", "PE.Views.DocumentHolder.txtHideLT": "隱藏左頂行", "PE.Views.DocumentHolder.txtHideOpenBracket": "隱藏開口支架", "PE.Views.DocumentHolder.txtHidePlaceholder": "隱藏佔位符", "PE.Views.DocumentHolder.txtHideRight": "隱藏右邊框", "PE.Views.DocumentHolder.txtHideTop": "隱藏頂部邊框", "PE.Views.DocumentHolder.txtHideTopLimit": "隱藏最高限額", "PE.Views.DocumentHolder.txtHideVer": "隱藏垂直線", "PE.Views.DocumentHolder.txtIncreaseArg": "增加參數大小", "PE.Views.DocumentHolder.txtInsertArgAfter": "在後面插入參數", "PE.Views.DocumentHolder.txtInsertArgBefore": "在前面插入參數", "PE.Views.DocumentHolder.txtInsertBreak": "插入手動中斷", "PE.Views.DocumentHolder.txtInsertEqAfter": "在後面插入方程式", "PE.Views.DocumentHolder.txtInsertEqBefore": "在前面插入方程式", "PE.Views.DocumentHolder.txtKeepTextOnly": "僅保留文字", "PE.Views.DocumentHolder.txtLimitChange": "更改限制位置", "PE.Views.DocumentHolder.txtLimitOver": "文字限制", "PE.Views.DocumentHolder.txtLimitUnder": "文字下的限制", "PE.Views.DocumentHolder.txtMatchBrackets": "將括號匹配到參數高度", "PE.Views.DocumentHolder.txtMatrixAlign": "矩陣對齊", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "移至投影片至片尾", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "移至投影片至片頭", "PE.Views.DocumentHolder.txtNewSlide": "新增投影片", "PE.Views.DocumentHolder.txtOverbar": "槓覆蓋文字", "PE.Views.DocumentHolder.txtPasteDestFormat": "使用目標主題", "PE.Views.DocumentHolder.txtPastePicture": "圖片", "PE.Views.DocumentHolder.txtPasteSourceFormat": "保持源格式", "PE.Views.DocumentHolder.txtPressLink": "按{0}並單擊連結", "PE.Views.DocumentHolder.txtPreview": "開始投影片放映", "PE.Views.DocumentHolder.txtPrintSelection": "列印選擇", "PE.Views.DocumentHolder.txtRemFractionBar": "刪除分數欄", "PE.Views.DocumentHolder.txtRemLimit": "取消限制", "PE.Views.DocumentHolder.txtRemoveAccentChar": "刪除強調字符", "PE.Views.DocumentHolder.txtRemoveBar": "移除欄", "PE.Views.DocumentHolder.txtRemScripts": "刪除腳本", "PE.Views.DocumentHolder.txtRemSubscript": "刪除下標", "PE.Views.DocumentHolder.txtRemSuperscript": "刪除上標", "PE.Views.DocumentHolder.txtResetLayout": "重設投影片", "PE.Views.DocumentHolder.txtScriptsAfter": "文字後的腳本", "PE.Views.DocumentHolder.txtScriptsBefore": "文字前的腳本", "PE.Views.DocumentHolder.txtSelectAll": "全選 選擇全部", "PE.Views.DocumentHolder.txtShowBottomLimit": "顯示底限", "PE.Views.DocumentHolder.txtShowCloseBracket": "顯示結束括號", "PE.Views.DocumentHolder.txtShowDegree": "顯示程度", "PE.Views.DocumentHolder.txtShowOpenBracket": "顯示開口支架", "PE.Views.DocumentHolder.txtShowPlaceholder": "顯示佔位符", "PE.Views.DocumentHolder.txtShowTopLimit": "顯示最高限額", "PE.Views.DocumentHolder.txtSlide": "滑動", "PE.Views.DocumentHolder.txtSlideHide": "隱藏投影片", "PE.Views.DocumentHolder.txtStretchBrackets": "延伸括號", "PE.Views.DocumentHolder.txtTop": "上方", "PE.Views.DocumentHolder.txtUnderbar": "槓至文字底下", "PE.Views.DocumentHolder.txtUngroup": "解開組合", "PE.Views.DocumentHolder.txtWarnUrl": "這鏈接有可能對您的設備和數據造成損害。<br> 您確定要繼續嗎？", "PE.Views.DocumentHolder.vertAlignText": "垂直對齊", "PE.Views.DocumentPreview.goToSlideText": "轉到投影片", "PE.Views.DocumentPreview.slideIndexText": "在{1}投影片中的第{0}張", "PE.Views.DocumentPreview.txtClose": "關閉投影片", "PE.Views.DocumentPreview.txtEndSlideshow": "結束投影片", "PE.Views.DocumentPreview.txtExitFullScreen": "退出全螢幕", "PE.Views.DocumentPreview.txtFinalMessage": "投影片預覽已結尾。點擊退出。", "PE.Views.DocumentPreview.txtFullScreen": "全螢幕", "PE.Views.DocumentPreview.txtNext": "下一張投影片", "PE.Views.DocumentPreview.txtPageNumInvalid": "無效的投影片編號", "PE.Views.DocumentPreview.txtPause": "暫停演示", "PE.Views.DocumentPreview.txtPlay": "開始演講", "PE.Views.DocumentPreview.txtPrev": "上一張投影片", "PE.Views.DocumentPreview.txtReset": "重設", "PE.Views.FileMenu.btnAboutCaption": "關於", "PE.Views.FileMenu.btnBackCaption": "打開文件所在位置", "PE.Views.FileMenu.btnCloseMenuCaption": "關閉選單", "PE.Views.FileMenu.btnCreateNewCaption": "創建新的", "PE.Views.FileMenu.btnDownloadCaption": "下載為", "PE.Views.FileMenu.btnExitCaption": "離開", "PE.Views.FileMenu.btnFileOpenCaption": "開啟", "PE.Views.FileMenu.btnHelpCaption": "幫助", "PE.Views.FileMenu.btnHistoryCaption": "版本歷史", "PE.Views.FileMenu.btnInfoCaption": "演示信息", "PE.Views.FileMenu.btnPrintCaption": "列印", "PE.Views.FileMenu.btnProtectCaption": "保護", "PE.Views.FileMenu.btnRecentFilesCaption": "打開最近", "PE.Views.FileMenu.btnRenameCaption": "改名", "PE.Views.FileMenu.btnReturnCaption": "返回簡報", "PE.Views.FileMenu.btnRightsCaption": "存取權限", "PE.Views.FileMenu.btnSaveAsCaption": "另存為", "PE.Views.FileMenu.btnSaveCaption": "儲存", "PE.Views.FileMenu.btnSaveCopyAsCaption": "另存為", "PE.Views.FileMenu.btnSettingsCaption": "進階設定", "PE.Views.FileMenu.btnToEditCaption": "編輯簡報", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "空白演示文稿", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "創建新的", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "套用", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "添加作者", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "添加文字", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "應用程式", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "更改存取權限", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "評論", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已建立", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最後修改者", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上一次更改", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "擁有者", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "有權利的人", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主旨", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "標題", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "\n已上傳", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "更改存取權限", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "有權利的人", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "帶密碼", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "保護演示", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "帶簽名", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "編輯簡報", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編輯將刪除演示文稿中的簽名。<br>確定要繼續嗎？", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此演示文稿已受密碼保護", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效的簽名已添加到演示文稿中。演示文稿受到保護，無法編輯。", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "演示文稿中的某些數字簽名無效或無法驗證。演示文稿受到保護，無法編輯。", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "查看簽名", "PE.Views.FileMenuPanels.Settings.okButtonText": "套用", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同編輯模式", "PE.Views.FileMenuPanels.Settings.strFast": "快", "PE.Views.FileMenuPanels.Settings.strFontRender": "字體提示", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "忽略大寫單詞", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "忽略帶數字的單詞", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "巨集設定", "PE.Views.FileMenuPanels.Settings.strPasteButton": "粘貼內容時顯示“粘貼選項”按鈕", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "顯示其他用戶的更改", "PE.Views.FileMenuPanels.Settings.strStrict": "嚴格", "PE.Views.FileMenuPanels.Settings.strTheme": "介面主題", "PE.Views.FileMenuPanels.Settings.strUnit": "測量單位", "PE.Views.FileMenuPanels.Settings.strZoom": "預設縮放值", "PE.Views.FileMenuPanels.Settings.text10Minutes": "每10分鐘", "PE.Views.FileMenuPanels.Settings.text30Minutes": "每30分鐘", "PE.Views.FileMenuPanels.Settings.text5Minutes": "每5分鐘", "PE.Views.FileMenuPanels.Settings.text60Minutes": "每一小時", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "對齊指南", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "自動恢復", "PE.Views.FileMenuPanels.Settings.textAutoSave": "自動保存", "PE.Views.FileMenuPanels.Settings.textDisabled": "已停用", "PE.Views.FileMenuPanels.Settings.textForceSave": "儲存到伺服器", "PE.Views.FileMenuPanels.Settings.textMinute": "每一分鐘", "PE.Views.FileMenuPanels.Settings.txtAll": "查看全部", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "自動更正選項...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "預設緩存模式", "PE.Views.FileMenuPanels.Settings.txtCm": "公分", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "協作", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "編輯並儲存", "PE.Views.FileMenuPanels.Settings.txtFastTip": "實時共同編輯，所有變更將自動儲存。", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "與投影片切合", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "切合至寬度", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "特殊符號", "PE.Views.FileMenuPanels.Settings.txtInch": "吋", "PE.Views.FileMenuPanels.Settings.txtLast": "查看最後", "PE.Views.FileMenuPanels.Settings.txtMac": "作為OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "本機", "PE.Views.FileMenuPanels.Settings.txtProofing": "打樣", "PE.Views.FileMenuPanels.Settings.txtPt": "點", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "全部啟用", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "不用提示啟用全部巨集", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "拼字檢查", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "全部停用", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "不用提示停用全部巨集", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "使用儲存鍵來同步你和其他用戶的變更", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "使用Alt鍵來操控用戶介面", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "使用Option鍵來操控用戶介面", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "顯示通知", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "以提示停用全部巨集", "PE.Views.FileMenuPanels.Settings.txtWin": "作為Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "工作空間", "PE.Views.HeaderFooterDialog.applyAllText": "全部應用", "PE.Views.HeaderFooterDialog.applyText": "套用", "PE.Views.HeaderFooterDialog.diffLanguage": "您不能使用與投影片母片不同的語言的日期格式。<br>要更改母片，請點擊“全部應用”而不是“應用”", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "警告", "PE.Views.HeaderFooterDialog.textDateTime": "日期和時間", "PE.Views.HeaderFooterDialog.textFixed": "固定", "PE.Views.HeaderFooterDialog.textFooter": "頁腳中的文字", "PE.Views.HeaderFooterDialog.textFormat": "格式", "PE.Views.HeaderFooterDialog.textLang": "語言", "PE.Views.HeaderFooterDialog.textNotTitle": "不顯示在標題投影片上", "PE.Views.HeaderFooterDialog.textPreview": "預覽", "PE.Views.HeaderFooterDialog.textSlideNum": "投影片頁碼", "PE.Views.HeaderFooterDialog.textTitle": "頁腳設置", "PE.Views.HeaderFooterDialog.textUpdate": "自動更新", "PE.Views.HyperlinkSettingsDialog.strDisplay": "顯示", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "連結至", "PE.Views.HyperlinkSettingsDialog.textDefault": "所選文字片段", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "在此處輸入標題", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "在此處輸入連結", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "在此處輸入工具提示", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "外部連結", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "這個簡報中的投影片", "PE.Views.HyperlinkSettingsDialog.textSlides": "投影片", "PE.Views.HyperlinkSettingsDialog.textTipText": "屏幕提示文字", "PE.Views.HyperlinkSettingsDialog.textTitle": "超連結設置", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "這是必填欄", "PE.Views.HyperlinkSettingsDialog.txtFirst": "第一張投影片", "PE.Views.HyperlinkSettingsDialog.txtLast": "最後一張投影片", "PE.Views.HyperlinkSettingsDialog.txtNext": "下一張投影片", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "此字段應為“ http://www.example.com”格式的網址", "PE.Views.HyperlinkSettingsDialog.txtPrev": "上一張投影片", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此欄位限2083字符", "PE.Views.HyperlinkSettingsDialog.txtSlide": "滑動", "PE.Views.ImageSettings.textAdvanced": "顯示進階設定", "PE.Views.ImageSettings.textCrop": "修剪", "PE.Views.ImageSettings.textCropFill": "填滿", "PE.Views.ImageSettings.textCropFit": "切合", "PE.Views.ImageSettings.textCropToShape": "剪裁成圖形", "PE.Views.ImageSettings.textEdit": "編輯", "PE.Views.ImageSettings.textEditObject": "編輯物件", "PE.Views.ImageSettings.textFitSlide": "與投影片切合", "PE.Views.ImageSettings.textFlip": "翻轉", "PE.Views.ImageSettings.textFromFile": "從檔案", "PE.Views.ImageSettings.textFromStorage": "從存儲", "PE.Views.ImageSettings.textFromUrl": "從 URL", "PE.Views.ImageSettings.textHeight": "高度", "PE.Views.ImageSettings.textHint270": "逆時針旋轉90°", "PE.Views.ImageSettings.textHint90": "順時針旋轉90°", "PE.Views.ImageSettings.textHintFlipH": "水平翻轉", "PE.Views.ImageSettings.textHintFlipV": "垂直翻轉", "PE.Views.ImageSettings.textInsert": "取代圖片", "PE.Views.ImageSettings.textOriginalSize": "實際大小", "PE.Views.ImageSettings.textRecentlyUsed": "最近使用", "PE.Views.ImageSettings.textRotate90": "旋轉90°", "PE.Views.ImageSettings.textRotation": "旋轉", "PE.Views.ImageSettings.textSize": "大小", "PE.Views.ImageSettings.textWidth": "寬度", "PE.Views.ImageSettingsAdvanced.textAlt": "替代文字", "PE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "PE.Views.ImageSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "PE.Views.ImageSettingsAdvanced.textAltTitle": "標題", "PE.Views.ImageSettingsAdvanced.textAngle": "角度", "PE.Views.ImageSettingsAdvanced.textCenter": "中心", "PE.Views.ImageSettingsAdvanced.textFlipped": "已翻轉", "PE.Views.ImageSettingsAdvanced.textFrom": "自", "PE.Views.ImageSettingsAdvanced.textHeight": "高度", "PE.Views.ImageSettingsAdvanced.textHorizontal": "水平的", "PE.Views.ImageSettingsAdvanced.textHorizontally": "水平地", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "比例不變", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "實際大小", "PE.Views.ImageSettingsAdvanced.textPlacement": "放置", "PE.Views.ImageSettingsAdvanced.textPosition": "位置", "PE.Views.ImageSettingsAdvanced.textRotation": "旋轉", "PE.Views.ImageSettingsAdvanced.textSize": "大小", "PE.Views.ImageSettingsAdvanced.textTitle": "圖像-進階設置", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.ImageSettingsAdvanced.textVertical": "垂直", "PE.Views.ImageSettingsAdvanced.textVertically": "垂直", "PE.Views.ImageSettingsAdvanced.textWidth": "寬度", "PE.Views.LeftMenu.tipAbout": "關於", "PE.Views.LeftMenu.tipChat": "聊天", "PE.Views.LeftMenu.tipComments": "評論", "PE.Views.LeftMenu.tipPlugins": "外掛程式", "PE.Views.LeftMenu.tipSearch": "搜尋", "PE.Views.LeftMenu.tipSlides": "投影片", "PE.Views.LeftMenu.tipSupport": "反饋與支持", "PE.Views.LeftMenu.tipTitles": "標題", "PE.Views.LeftMenu.txtDeveloper": "開發者模式", "PE.Views.LeftMenu.txtEditor": "簡報編輯器", "PE.Views.LeftMenu.txtLimit": "限制存取", "PE.Views.LeftMenu.txtTrial": "試用模式", "PE.Views.LeftMenu.txtTrialDev": "試用開發人員模式", "PE.Views.ParagraphSettings.strLineHeight": "行間距", "PE.Views.ParagraphSettings.strParagraphSpacing": "段落間距", "PE.Views.ParagraphSettings.strSpacingAfter": "之後", "PE.Views.ParagraphSettings.strSpacingBefore": "之前", "PE.Views.ParagraphSettings.textAdvanced": "顯示進階設定", "PE.Views.ParagraphSettings.textAt": "在", "PE.Views.ParagraphSettings.textAtLeast": "至少", "PE.Views.ParagraphSettings.textAuto": "多項", "PE.Views.ParagraphSettings.textExact": "準確", "PE.Views.ParagraphSettings.txtAutoText": "自動", "PE.Views.ParagraphSettingsAdvanced.noTabs": "指定的標籤將出現在此字段中", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大寫", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "雙刪除線", "PE.Views.ParagraphSettingsAdvanced.strIndent": "縮進", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行間距", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "之後", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "之前", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字體", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "縮進和間距", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小大寫", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "間距", "PE.Views.ParagraphSettingsAdvanced.strStrike": "刪除線", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "下標", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "上標", "PE.Views.ParagraphSettingsAdvanced.strTabs": "標籤", "PE.Views.ParagraphSettingsAdvanced.textAlign": "對齊", "PE.Views.ParagraphSettingsAdvanced.textAuto": "多項", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字符間距", "PE.Views.ParagraphSettingsAdvanced.textDefault": "預設分頁", "PE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "PE.Views.ParagraphSettingsAdvanced.textExact": "準確", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "第一行", "PE.Views.ParagraphSettingsAdvanced.textHanging": "懸掛式", "PE.Views.ParagraphSettingsAdvanced.textJustified": "合理的", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（空）", "PE.Views.ParagraphSettingsAdvanced.textRemove": "移除", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "移除所有", "PE.Views.ParagraphSettingsAdvanced.textSet": "指定", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "標籤位置", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "PE.Views.ParagraphSettingsAdvanced.textTitle": "段落-進階設置", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "自動", "PE.Views.RightMenu.txtChartSettings": "圖表設定", "PE.Views.RightMenu.txtImageSettings": "影像設定", "PE.Views.RightMenu.txtParagraphSettings": "文字設定", "PE.Views.RightMenu.txtShapeSettings": "形狀設定", "PE.Views.RightMenu.txtSignatureSettings": "簽名設置", "PE.Views.RightMenu.txtSlideSettings": "投影片設定", "PE.Views.RightMenu.txtTableSettings": "表格設定", "PE.Views.RightMenu.txtTextArtSettings": "文字藝術設定", "PE.Views.ShapeSettings.strBackground": "背景顏色", "PE.Views.ShapeSettings.strChange": "更改自動形狀", "PE.Views.ShapeSettings.strColor": "顏色", "PE.Views.ShapeSettings.strFill": "填滿", "PE.Views.ShapeSettings.strForeground": "前景色", "PE.Views.ShapeSettings.strPattern": "模式", "PE.Views.ShapeSettings.strShadow": "顯示陰影", "PE.Views.ShapeSettings.strSize": "大小", "PE.Views.ShapeSettings.strStroke": "筆鋒", "PE.Views.ShapeSettings.strTransparency": "透明度", "PE.Views.ShapeSettings.strType": "類型", "PE.Views.ShapeSettings.textAdvanced": "顯示進階設定", "PE.Views.ShapeSettings.textAngle": "角度", "PE.Views.ShapeSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入0 pt至1584 pt之間的值。", "PE.Views.ShapeSettings.textColor": "填充顏色", "PE.Views.ShapeSettings.textDirection": "方向", "PE.Views.ShapeSettings.textEmptyPattern": "無模式", "PE.Views.ShapeSettings.textFlip": "翻轉", "PE.Views.ShapeSettings.textFromFile": "從檔案", "PE.Views.ShapeSettings.textFromStorage": "從存儲", "PE.Views.ShapeSettings.textFromUrl": "從 URL", "PE.Views.ShapeSettings.textGradient": "漸變點", "PE.Views.ShapeSettings.textGradientFill": "漸層填充", "PE.Views.ShapeSettings.textHint270": "逆時針旋轉90°", "PE.Views.ShapeSettings.textHint90": "順時針旋轉90°", "PE.Views.ShapeSettings.textHintFlipH": "水平翻轉", "PE.Views.ShapeSettings.textHintFlipV": "垂直翻轉", "PE.Views.ShapeSettings.textImageTexture": "圖片或紋理", "PE.Views.ShapeSettings.textLinear": "線性的", "PE.Views.ShapeSettings.textNoFill": "沒有填充", "PE.Views.ShapeSettings.textPatternFill": "模式", "PE.Views.ShapeSettings.textPosition": "位置", "PE.Views.ShapeSettings.textRadial": "徑向的", "PE.Views.ShapeSettings.textRecentlyUsed": "最近使用", "PE.Views.ShapeSettings.textRotate90": "旋轉90°", "PE.Views.ShapeSettings.textRotation": "旋轉", "PE.Views.ShapeSettings.textSelectImage": "選擇圖片", "PE.Views.ShapeSettings.textSelectTexture": "選擇", "PE.Views.ShapeSettings.textStretch": "延伸", "PE.Views.ShapeSettings.textStyle": "樣式", "PE.Views.ShapeSettings.textTexture": "從紋理", "PE.Views.ShapeSettings.textTile": "磚瓦", "PE.Views.ShapeSettings.tipAddGradientPoint": "添加漸變點", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "刪除漸變點", "PE.Views.ShapeSettings.txtBrownPaper": "牛皮紙", "PE.Views.ShapeSettings.txtCanvas": "帆布", "PE.Views.ShapeSettings.txtCarton": "紙箱", "PE.Views.ShapeSettings.txtDarkFabric": "深色面料", "PE.Views.ShapeSettings.txtGrain": "紋", "PE.Views.ShapeSettings.txtGranite": "花崗岩", "PE.Views.ShapeSettings.txtGreyPaper": "灰紙", "PE.Views.ShapeSettings.txtKnit": "編織", "PE.Views.ShapeSettings.txtLeather": "皮革", "PE.Views.ShapeSettings.txtNoBorders": "無線條", "PE.Views.ShapeSettings.txtPapyrus": "紙莎草紙", "PE.Views.ShapeSettings.txtWood": "木頭", "PE.Views.ShapeSettingsAdvanced.strColumns": "欄", "PE.Views.ShapeSettingsAdvanced.strMargins": "文字填充", "PE.Views.ShapeSettingsAdvanced.textAlt": "替代文字", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "描述", "PE.Views.ShapeSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "標題", "PE.Views.ShapeSettingsAdvanced.textAngle": "角度", "PE.Views.ShapeSettingsAdvanced.textArrows": "箭頭", "PE.Views.ShapeSettingsAdvanced.textAutofit": "自動調整", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "開始大小", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "開始樣式", "PE.Views.ShapeSettingsAdvanced.textBevel": "斜角", "PE.Views.ShapeSettingsAdvanced.textBottom": "底部", "PE.Views.ShapeSettingsAdvanced.textCapType": "Cap 類型", "PE.Views.ShapeSettingsAdvanced.textCenter": "中心", "PE.Views.ShapeSettingsAdvanced.textColNumber": "列數", "PE.Views.ShapeSettingsAdvanced.textEndSize": "端部尺寸", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "結束樣式", "PE.Views.ShapeSettingsAdvanced.textFlat": "平面", "PE.Views.ShapeSettingsAdvanced.textFlipped": "已翻轉", "PE.Views.ShapeSettingsAdvanced.textFrom": "自", "PE.Views.ShapeSettingsAdvanced.textHeight": "高度", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "水平的", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "水平地", "PE.Views.ShapeSettingsAdvanced.textJoinType": "加入類型", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "比例不變", "PE.Views.ShapeSettingsAdvanced.textLeft": "左", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "線型", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "不要自動調整", "PE.Views.ShapeSettingsAdvanced.textPlacement": "放置", "PE.Views.ShapeSettingsAdvanced.textPosition": "職務", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "調整形狀以適合文本", "PE.Views.ShapeSettingsAdvanced.textRight": "右", "PE.Views.ShapeSettingsAdvanced.textRotation": "旋轉", "PE.Views.ShapeSettingsAdvanced.textRound": "圓", "PE.Views.ShapeSettingsAdvanced.textShrink": "溢出文字縮小", "PE.Views.ShapeSettingsAdvanced.textSize": "大小", "PE.Views.ShapeSettingsAdvanced.textSpacing": "欄之前的距離", "PE.Views.ShapeSettingsAdvanced.textSquare": "正方形", "PE.Views.ShapeSettingsAdvanced.textTextBox": "文字框", "PE.Views.ShapeSettingsAdvanced.textTitle": "形狀 - 進階設定", "PE.Views.ShapeSettingsAdvanced.textTop": "上方", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.ShapeSettingsAdvanced.textVertical": "垂直", "PE.Views.ShapeSettingsAdvanced.textVertically": "垂直", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "重量和箭頭", "PE.Views.ShapeSettingsAdvanced.textWidth": "寬度", "PE.Views.ShapeSettingsAdvanced.txtNone": "無", "PE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "PE.Views.SignatureSettings.strDelete": "刪除簽名", "PE.Views.SignatureSettings.strDetails": "簽名細節", "PE.Views.SignatureSettings.strInvalid": "無效的簽名", "PE.Views.SignatureSettings.strSign": "簽名", "PE.Views.SignatureSettings.strSignature": "簽名", "PE.Views.SignatureSettings.strValid": "有效簽名", "PE.Views.SignatureSettings.txtContinueEditing": "仍要編輯", "PE.Views.SignatureSettings.txtEditWarning": "編輯將刪除演示文稿中的簽名。<br>確定要繼續嗎？", "PE.Views.SignatureSettings.txtRemoveWarning": "確定移除此簽名?<br>這動作無法復原.", "PE.Views.SignatureSettings.txtSigned": "有效的簽名已添加到演示文稿中。演示文稿受到保護，無法編輯。", "PE.Views.SignatureSettings.txtSignedInvalid": "演示文稿中的某些數字簽名無效或無法驗證。演示文稿受到保護，無法編輯。", "PE.Views.SlideSettings.strBackground": "背景顏色", "PE.Views.SlideSettings.strColor": "顏色", "PE.Views.SlideSettings.strDateTime": "顯示日期和時間", "PE.Views.SlideSettings.strFill": "背景", "PE.Views.SlideSettings.strForeground": "前景色", "PE.Views.SlideSettings.strPattern": "模式", "PE.Views.SlideSettings.strSlideNum": "顯示投影片編號", "PE.Views.SlideSettings.strTransparency": "透明度", "PE.Views.SlideSettings.textAdvanced": "顯示進階設定", "PE.Views.SlideSettings.textAngle": "角度", "PE.Views.SlideSettings.textColor": "填充顏色", "PE.Views.SlideSettings.textDirection": "方向", "PE.Views.SlideSettings.textEmptyPattern": "無模式", "PE.Views.SlideSettings.textFromFile": "從檔案", "PE.Views.SlideSettings.textFromStorage": "從存儲", "PE.Views.SlideSettings.textFromUrl": "從 URL", "PE.Views.SlideSettings.textGradient": "漸變點", "PE.Views.SlideSettings.textGradientFill": "漸層填充", "PE.Views.SlideSettings.textImageTexture": "圖片或紋理", "PE.Views.SlideSettings.textLinear": "線性的", "PE.Views.SlideSettings.textNoFill": "沒有填充", "PE.Views.SlideSettings.textPatternFill": "模式", "PE.Views.SlideSettings.textPosition": "位置", "PE.Views.SlideSettings.textRadial": "徑向的", "PE.Views.SlideSettings.textReset": "重設變更", "PE.Views.SlideSettings.textSelectImage": "選擇圖片", "PE.Views.SlideSettings.textSelectTexture": "選擇", "PE.Views.SlideSettings.textStretch": "延伸", "PE.Views.SlideSettings.textStyle": "樣式", "PE.Views.SlideSettings.textTexture": "從紋理", "PE.Views.SlideSettings.textTile": "磚瓦", "PE.Views.SlideSettings.tipAddGradientPoint": "添加漸變點", "PE.Views.SlideSettings.tipRemoveGradientPoint": "刪除漸變點", "PE.Views.SlideSettings.txtBrownPaper": "牛皮紙", "PE.Views.SlideSettings.txtCanvas": "帆布", "PE.Views.SlideSettings.txtCarton": "紙箱", "PE.Views.SlideSettings.txtDarkFabric": "深色面料", "PE.Views.SlideSettings.txtGrain": "紋", "PE.Views.SlideSettings.txtGranite": "花崗岩", "PE.Views.SlideSettings.txtGreyPaper": "灰紙", "PE.Views.SlideSettings.txtKnit": "編織", "PE.Views.SlideSettings.txtLeather": "皮革", "PE.Views.SlideSettings.txtPapyrus": "紙莎草紙", "PE.Views.SlideSettings.txtWood": "木頭", "PE.Views.SlideshowSettings.textLoop": "連續循環直到按下“ Esc”", "PE.Views.SlideshowSettings.textTitle": "顯示設置", "PE.Views.SlideSizeSettings.strLandscape": "景觀", "PE.Views.SlideSizeSettings.strPortrait": "纵向", "PE.Views.SlideSizeSettings.textHeight": "高度", "PE.Views.SlideSizeSettings.textSlideOrientation": "滑動方向", "PE.Views.SlideSizeSettings.textSlideSize": "投影片大小", "PE.Views.SlideSizeSettings.textTitle": "投影片大小設置", "PE.Views.SlideSizeSettings.textWidth": "寬度", "PE.Views.SlideSizeSettings.txt35": "35公釐投影片", "PE.Views.SlideSizeSettings.txtA3": "A3紙（297x420毫米）", "PE.Views.SlideSizeSettings.txtA4": "A4紙（210x297毫米）", "PE.Views.SlideSizeSettings.txtB4": "B4（ICO）紙（250x353毫米）", "PE.Views.SlideSizeSettings.txtB5": "B5（ICO）紙（176x250毫米）", "PE.Views.SlideSizeSettings.txtBanner": "幡", "PE.Views.SlideSizeSettings.txtCustom": "自訂", "PE.Views.SlideSizeSettings.txtLedger": "分類帳紙（11x17英寸）", "PE.Views.SlideSizeSettings.txtLetter": "信紙（8.5x11英寸）", "PE.Views.SlideSizeSettings.txtOverhead": "高架", "PE.Views.SlideSizeSettings.txtStandard": "標準(4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "寬屏", "PE.Views.Statusbar.goToPageText": "轉到投影片", "PE.Views.Statusbar.pageIndexText": "在{1}投影片中的第{0}張", "PE.Views.Statusbar.textShowBegin": "從頭開始展示", "PE.Views.Statusbar.textShowCurrent": "從目前投影片顯示", "PE.Views.Statusbar.textShowPresenterView": "顯示演示者視圖", "PE.Views.Statusbar.tipAccessRights": "管理文檔存取權限", "PE.Views.Statusbar.tipFitPage": "與投影片切合", "PE.Views.Statusbar.tipFitWidth": "切合至寬度", "PE.Views.Statusbar.tipPreview": "開始投影片放映", "PE.Views.Statusbar.tipSetLang": "設定文字語言", "PE.Views.Statusbar.tipZoomFactor": "放大", "PE.Views.Statusbar.tipZoomIn": "放大", "PE.Views.Statusbar.tipZoomOut": "縮小", "PE.Views.Statusbar.txtPageNumInvalid": "無效的投影片編號", "PE.Views.TableSettings.deleteColumnText": "刪除欄位", "PE.Views.TableSettings.deleteRowText": "刪除行列", "PE.Views.TableSettings.deleteTableText": "刪除表格", "PE.Views.TableSettings.insertColumnLeftText": "向左插入列", "PE.Views.TableSettings.insertColumnRightText": "向右插入列", "PE.Views.TableSettings.insertRowAboveText": "在上方插入行", "PE.Views.TableSettings.insertRowBelowText": "在下方插入行", "PE.Views.TableSettings.mergeCellsText": "合併單元格", "PE.Views.TableSettings.selectCellText": "選擇儲存格", "PE.Views.TableSettings.selectColumnText": "選擇欄", "PE.Views.TableSettings.selectRowText": "選擇列", "PE.Views.TableSettings.selectTableText": "選擇表格", "PE.Views.TableSettings.splitCellsText": "分割儲存格...", "PE.Views.TableSettings.splitCellTitleText": "分割儲存格", "PE.Views.TableSettings.textAdvanced": "顯示進階設定", "PE.Views.TableSettings.textBackColor": "背景顏色", "PE.Views.TableSettings.textBanded": "帶狀", "PE.Views.TableSettings.textBorderColor": "顏色", "PE.Views.TableSettings.textBorders": "邊框樣式", "PE.Views.TableSettings.textCellSize": "單元格大小", "PE.Views.TableSettings.textColumns": "欄", "PE.Views.TableSettings.textDistributeCols": "分配列", "PE.Views.TableSettings.textDistributeRows": "分配行", "PE.Views.TableSettings.textEdit": "行和列", "PE.Views.TableSettings.textEmptyTemplate": "\n沒有模板", "PE.Views.TableSettings.textFirst": "第一", "PE.Views.TableSettings.textHeader": "標頭", "PE.Views.TableSettings.textHeight": "高度", "PE.Views.TableSettings.textLast": "最後", "PE.Views.TableSettings.textRows": "行列", "PE.Views.TableSettings.textSelectBorders": "選擇您要更改上面選擇的應用樣式的邊框", "PE.Views.TableSettings.textTemplate": "從範本中選擇", "PE.Views.TableSettings.textTotal": "總計", "PE.Views.TableSettings.textWidth": "寬度", "PE.Views.TableSettings.tipAll": "設置外邊界和所有內線", "PE.Views.TableSettings.tipBottom": "僅設置外底邊框", "PE.Views.TableSettings.tipInner": "僅設置內線", "PE.Views.TableSettings.tipInnerHor": "僅設置水平內線", "PE.Views.TableSettings.tipInnerVert": "僅設置垂直內線", "PE.Views.TableSettings.tipLeft": "僅設置左外邊框", "PE.Views.TableSettings.tipNone": "設置無邊界", "PE.Views.TableSettings.tipOuter": "僅設置外部框線", "PE.Views.TableSettings.tipRight": "僅設置右外框", "PE.Views.TableSettings.tipTop": "僅設置外部頂部邊框", "PE.Views.TableSettings.txtNoBorders": "無邊框", "PE.Views.TableSettings.txtTable_Accent": "強調", "PE.Views.TableSettings.txtTable_DarkStyle": "黑暗風格", "PE.Views.TableSettings.txtTable_LightStyle": "燈光風格", "PE.Views.TableSettings.txtTable_MediumStyle": "中型樣式", "PE.Views.TableSettings.txtTable_NoGrid": "無網格", "PE.Views.TableSettings.txtTable_NoStyle": "沒樣式", "PE.Views.TableSettings.txtTable_TableGrid": "表格網格", "PE.Views.TableSettings.txtTable_ThemedStyle": "主題風格", "PE.Views.TableSettingsAdvanced.textAlt": "替代文字", "PE.Views.TableSettingsAdvanced.textAltDescription": "描述", "PE.Views.TableSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "PE.Views.TableSettingsAdvanced.textAltTitle": "標題", "PE.Views.TableSettingsAdvanced.textBottom": "底部", "PE.Views.TableSettingsAdvanced.textCenter": "中心", "PE.Views.TableSettingsAdvanced.textCheckMargins": "使用預設邊距", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "預設邊框", "PE.Views.TableSettingsAdvanced.textFrom": "自", "PE.Views.TableSettingsAdvanced.textHeight": "\n高度", "PE.Views.TableSettingsAdvanced.textHorizontal": "水平的", "PE.Views.TableSettingsAdvanced.textKeepRatio": "比例不變", "PE.Views.TableSettingsAdvanced.textLeft": "左", "PE.Views.TableSettingsAdvanced.textMargins": "單元格內邊距", "PE.Views.TableSettingsAdvanced.textPlacement": "放置", "PE.Views.TableSettingsAdvanced.textPosition": "職務", "PE.Views.TableSettingsAdvanced.textRight": "右", "PE.Views.TableSettingsAdvanced.textSize": "大小", "PE.Views.TableSettingsAdvanced.textTitle": "表格 - 進階設定", "PE.Views.TableSettingsAdvanced.textTop": "上方", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.TableSettingsAdvanced.textVertical": "垂直", "PE.Views.TableSettingsAdvanced.textWidth": "寬度", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "邊框", "PE.Views.TextArtSettings.strBackground": "背景顏色", "PE.Views.TextArtSettings.strColor": "顏色", "PE.Views.TextArtSettings.strFill": "填滿", "PE.Views.TextArtSettings.strForeground": "前景色", "PE.Views.TextArtSettings.strPattern": "模式", "PE.Views.TextArtSettings.strSize": "大小", "PE.Views.TextArtSettings.strStroke": "筆鋒", "PE.Views.TextArtSettings.strTransparency": "透明度", "PE.Views.TextArtSettings.strType": "類型", "PE.Views.TextArtSettings.textAngle": "角度", "PE.Views.TextArtSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入0 pt至1584 pt之間的值。", "PE.Views.TextArtSettings.textColor": "填充顏色", "PE.Views.TextArtSettings.textDirection": "方向", "PE.Views.TextArtSettings.textEmptyPattern": "無模式", "PE.Views.TextArtSettings.textFromFile": "從檔案", "PE.Views.TextArtSettings.textFromUrl": "從 URL", "PE.Views.TextArtSettings.textGradient": "漸變點", "PE.Views.TextArtSettings.textGradientFill": "漸層填充", "PE.Views.TextArtSettings.textImageTexture": "圖片或紋理", "PE.Views.TextArtSettings.textLinear": "線性的", "PE.Views.TextArtSettings.textNoFill": "沒有填充", "PE.Views.TextArtSettings.textPatternFill": "模式", "PE.Views.TextArtSettings.textPosition": "位置", "PE.Views.TextArtSettings.textRadial": "徑向的", "PE.Views.TextArtSettings.textSelectTexture": "選擇", "PE.Views.TextArtSettings.textStretch": "延伸", "PE.Views.TextArtSettings.textStyle": "樣式", "PE.Views.TextArtSettings.textTemplate": "樣板", "PE.Views.TextArtSettings.textTexture": "從紋理", "PE.Views.TextArtSettings.textTile": "磚瓦", "PE.Views.TextArtSettings.textTransform": "轉變", "PE.Views.TextArtSettings.tipAddGradientPoint": "添加漸變點", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "刪除漸變點", "PE.Views.TextArtSettings.txtBrownPaper": "牛皮紙", "PE.Views.TextArtSettings.txtCanvas": "帆布", "PE.Views.TextArtSettings.txtCarton": "紙箱", "PE.Views.TextArtSettings.txtDarkFabric": "深色面料", "PE.Views.TextArtSettings.txtGrain": "紋", "PE.Views.TextArtSettings.txtGranite": "花崗岩", "PE.Views.TextArtSettings.txtGreyPaper": "灰紙", "PE.Views.TextArtSettings.txtKnit": "編織", "PE.Views.TextArtSettings.txtLeather": "皮革", "PE.Views.TextArtSettings.txtNoBorders": "無線條", "PE.Views.TextArtSettings.txtPapyrus": "紙莎草紙", "PE.Views.TextArtSettings.txtWood": "木頭", "PE.Views.Toolbar.capAddSlide": "新增投影片", "PE.Views.Toolbar.capBtnAddComment": "增加評論", "PE.Views.Toolbar.capBtnComment": "評論", "PE.Views.Toolbar.capBtnDateTime": "日期和時間", "PE.Views.Toolbar.capBtnInsHeader": "頁尾", "PE.Views.Toolbar.capBtnInsSymbol": "符號", "PE.Views.Toolbar.capBtnSlideNum": "投影片頁碼", "PE.Views.Toolbar.capInsertAudio": "音訊", "PE.Views.Toolbar.capInsertChart": "圖表", "PE.Views.Toolbar.capInsertEquation": "方程式", "PE.Views.Toolbar.capInsertHyperlink": "超連結", "PE.Views.Toolbar.capInsertImage": "圖像", "PE.Views.Toolbar.capInsertShape": "形狀", "PE.Views.Toolbar.capInsertTable": "表格", "PE.Views.Toolbar.capInsertText": "文字框", "PE.Views.Toolbar.capInsertTextArt": "文字藝術", "PE.Views.Toolbar.capInsertVideo": "影片", "PE.Views.Toolbar.capTabFile": "檔案", "PE.Views.Toolbar.capTabHome": "首頁", "PE.Views.Toolbar.capTabInsert": "插入", "PE.Views.Toolbar.mniCapitalizeWords": "每個單字字首大寫", "PE.Views.Toolbar.mniCustomTable": "插入自訂表格", "PE.Views.Toolbar.mniImageFromFile": "圖片來自文件", "PE.Views.Toolbar.mniImageFromStorage": "來自存儲的圖像", "PE.Views.Toolbar.mniImageFromUrl": "來自網址的圖片", "PE.Views.Toolbar.mniInsertSSE": "插入計算表", "PE.Views.Toolbar.mniLowerCase": "小寫", "PE.Views.Toolbar.mniSentenceCase": "大寫句子頭", "PE.Views.Toolbar.mniSlideAdvanced": "進階設定", "PE.Views.Toolbar.mniSlideStandard": "標準(4:3)", "PE.Views.Toolbar.mniSlideWide": "寬螢幕(16:9)", "PE.Views.Toolbar.mniToggleCase": "轉換大小寫", "PE.Views.Toolbar.mniUpperCase": "大寫", "PE.Views.Toolbar.strMenuNoFill": "沒有填充", "PE.Views.Toolbar.textAlignBottom": "將文字對齊到底部", "PE.Views.Toolbar.textAlignCenter": "中心文字", "PE.Views.Toolbar.textAlignJust": "證明", "PE.Views.Toolbar.textAlignLeft": "左對齊文字", "PE.Views.Toolbar.textAlignMiddle": "文字居中對齊", "PE.Views.Toolbar.textAlignRight": "右對齊文字", "PE.Views.Toolbar.textAlignTop": "將文字對齊到頂部", "PE.Views.Toolbar.textArrangeBack": "傳送到背景", "PE.Views.Toolbar.textArrangeBackward": "向後發送", "PE.Views.Toolbar.textArrangeForward": "向前帶進", "PE.Views.Toolbar.textArrangeFront": "移到前景", "PE.Views.Toolbar.textBold": "粗體", "PE.Views.Toolbar.textColumnsCustom": "自定欄", "PE.Views.Toolbar.textColumnsOne": "一欄", "PE.Views.Toolbar.textColumnsThree": "三欄", "PE.Views.Toolbar.textColumnsTwo": "兩欄", "PE.Views.Toolbar.textItalic": "斜體", "PE.Views.Toolbar.textListSettings": "清單設定", "PE.Views.Toolbar.textRecentlyUsed": "最近使用", "PE.Views.Toolbar.textShapeAlignBottom": "底部對齊", "PE.Views.Toolbar.textShapeAlignCenter": "居中對齊", "PE.Views.Toolbar.textShapeAlignLeft": "對齊左側", "PE.Views.Toolbar.textShapeAlignMiddle": "中央對齊", "PE.Views.Toolbar.textShapeAlignRight": "對齊右側", "PE.Views.Toolbar.textShapeAlignTop": "上方對齊", "PE.Views.Toolbar.textShowBegin": "從頭開始展示", "PE.Views.Toolbar.textShowCurrent": "從目前投影片顯示", "PE.Views.Toolbar.textShowPresenterView": "顯示演示者視圖", "PE.Views.Toolbar.textShowSettings": "顯示設置", "PE.Views.Toolbar.textStrikeout": "刪除線", "PE.Views.Toolbar.textSubscript": "下標", "PE.Views.Toolbar.textSuperscript": "上標", "PE.Views.Toolbar.textTabAnimation": "動畫", "PE.Views.Toolbar.textTabCollaboration": "協作", "PE.Views.Toolbar.textTabFile": "檔案", "PE.Views.Toolbar.textTabHome": "首頁", "PE.Views.Toolbar.textTabInsert": "插入", "PE.Views.Toolbar.textTabProtect": "保護", "PE.Views.Toolbar.textTabTransitions": "過渡", "PE.Views.Toolbar.textTabView": "檢視", "PE.Views.Toolbar.textTitleError": "錯誤", "PE.Views.Toolbar.textUnderline": "底線", "PE.Views.Toolbar.tipAddSlide": "新增投影片", "PE.Views.Toolbar.tipBack": "返回", "PE.Views.Toolbar.tipChangeCase": "改大小寫", "PE.Views.Toolbar.tipChangeChart": "更改圖表類型", "PE.Views.Toolbar.tipChangeSlide": "更改投影片版面配置", "PE.Views.Toolbar.tipClearStyle": "清晰的風格", "PE.Views.Toolbar.tipColorSchemas": "更改配色方案", "PE.Views.Toolbar.tipColumns": "插入欄", "PE.Views.Toolbar.tipCopy": "複製", "PE.Views.Toolbar.tipCopyStyle": "複製樣式", "PE.Views.Toolbar.tipCut": "剪下", "PE.Views.Toolbar.tipDateTime": "插入當前日期和時間", "PE.Views.Toolbar.tipDecFont": "減少字體大小", "PE.Views.Toolbar.tipDecPrLeft": "減少縮進", "PE.Views.Toolbar.tipEditHeader": "編輯頁腳", "PE.Views.Toolbar.tipFontColor": "字體顏色", "PE.Views.Toolbar.tipFontName": "字體", "PE.Views.Toolbar.tipFontSize": "字體大小", "PE.Views.Toolbar.tipHAligh": "水平對齊", "PE.Views.Toolbar.tipHighlightColor": "熒光色選", "PE.Views.Toolbar.tipIncFont": "增量字體大小", "PE.Views.Toolbar.tipIncPrLeft": "增加縮進", "PE.Views.Toolbar.tipInsertAudio": "插入音頻", "PE.Views.Toolbar.tipInsertChart": "插入圖表", "PE.Views.Toolbar.tipInsertEquation": "插入方程式", "PE.Views.Toolbar.tipInsertHyperlink": "新增超連結", "PE.Views.Toolbar.tipInsertImage": "插入圖片", "PE.Views.Toolbar.tipInsertShape": "插入自動形狀", "PE.Views.Toolbar.tipInsertSymbol": "插入符號", "PE.Views.Toolbar.tipInsertTable": "插入表格", "PE.Views.Toolbar.tipInsertText": "插入文字框", "PE.Views.Toolbar.tipInsertTextArt": "插入文字藝術", "PE.Views.Toolbar.tipInsertVideo": "插入影片", "PE.Views.Toolbar.tipLineSpace": "行間距", "PE.Views.Toolbar.tipMarkers": "項目符號", "PE.Views.Toolbar.tipMarkersArrow": "箭頭項目符號", "PE.Views.Toolbar.tipMarkersCheckmark": "核取記號項目符號", "PE.Views.Toolbar.tipMarkersDash": "連字號項目符號", "PE.Views.Toolbar.tipMarkersFRhombus": "實心菱形項目符號", "PE.Views.Toolbar.tipMarkersFRound": "實心圓項目符號", "PE.Views.Toolbar.tipMarkersFSquare": "實心方形項目符號", "PE.Views.Toolbar.tipMarkersHRound": "空心圓項目符號", "PE.Views.Toolbar.tipMarkersStar": "星星項目符號", "PE.Views.Toolbar.tipNone": "無", "PE.Views.Toolbar.tipNumbers": "編號", "PE.Views.Toolbar.tipPaste": "貼上", "PE.Views.Toolbar.tipPreview": "開始投影片放映", "PE.Views.Toolbar.tipPrint": "列印", "PE.Views.Toolbar.tipRedo": "重做", "PE.Views.Toolbar.tipSave": "儲存", "PE.Views.Toolbar.tipSaveCoauth": "保存您的更改，以供其他用戶查看。", "PE.Views.Toolbar.tipSelectAll": "全選", "PE.Views.Toolbar.tipShapeAlign": "對齊形狀", "PE.Views.Toolbar.tipShapeArrange": "排列形狀", "PE.Views.Toolbar.tipSlideNum": "插入投影片編號", "PE.Views.Toolbar.tipSlideSize": "選擇投影片大小", "PE.Views.Toolbar.tipSlideTheme": "投影片主題", "PE.Views.Toolbar.tipUndo": "復原", "PE.Views.Toolbar.tipVAligh": "垂直對齊", "PE.Views.Toolbar.tipViewSettings": "查看設定", "PE.Views.Toolbar.txtDistribHor": "水平分佈", "PE.Views.Toolbar.txtDistribVert": "垂直分佈", "PE.Views.Toolbar.txtDuplicateSlide": "投影片複製", "PE.Views.Toolbar.txtGroup": "群組", "PE.Views.Toolbar.txtObjectsAlign": "對齊所選物件", "PE.Views.Toolbar.txtScheme1": "辦公室", "PE.Views.Toolbar.txtScheme10": "中位數", "PE.Views.Toolbar.txtScheme11": " 地鐵", "PE.Views.Toolbar.txtScheme12": "模組", "PE.Views.Toolbar.txtScheme13": "豐富的", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "起源", "PE.Views.Toolbar.txtScheme16": "紙", "PE.Views.Toolbar.txtScheme17": "冬至", "PE.Views.Toolbar.txtScheme18": "技術", "PE.Views.Toolbar.txtScheme19": "跋涉", "PE.Views.Toolbar.txtScheme2": "灰階", "PE.Views.Toolbar.txtScheme20": "市區", "PE.Views.Toolbar.txtScheme21": "感染力", "PE.Views.Toolbar.txtScheme22": "新的Office", "PE.Views.Toolbar.txtScheme3": "頂尖", "PE.Views.Toolbar.txtScheme4": "方面", "PE.Views.Toolbar.txtScheme5": "思域", "PE.Views.Toolbar.txtScheme6": "大堂", "PE.Views.Toolbar.txtScheme7": "產權", "PE.Views.Toolbar.txtScheme8": "流程", "PE.Views.Toolbar.txtScheme9": "鑄造廠", "PE.Views.Toolbar.txtSlideAlign": "對齊投影片", "PE.Views.Toolbar.txtUngroup": "解開組合", "PE.Views.Transitions.strDelay": "延遲", "PE.Views.Transitions.strDuration": "持續時間", "PE.Views.Transitions.strStartOnClick": "點選後開始", "PE.Views.Transitions.textBlack": "通過黑", "PE.Views.Transitions.textBottom": "底部", "PE.Views.Transitions.textBottomLeft": "左下方", "PE.Views.Transitions.textBottomRight": "右下方", "PE.Views.Transitions.textClock": "時鐘", "PE.Views.Transitions.textClockwise": "順時針", "PE.Views.Transitions.textCounterclockwise": "逆時針", "PE.Views.Transitions.textCover": "覆蓋", "PE.Views.Transitions.textFade": "褪", "PE.Views.Transitions.textHorizontalIn": "水平輸入", "PE.Views.Transitions.textHorizontalOut": "水平輸出", "PE.Views.Transitions.textLeft": "左", "PE.Views.Transitions.textNone": "無", "PE.Views.Transitions.textPush": "推", "PE.Views.Transitions.textRight": "右", "PE.Views.Transitions.textSmoothly": "順的", "PE.Views.Transitions.textSplit": "分割", "PE.Views.Transitions.textTop": "上方", "PE.Views.Transitions.textTopLeft": "左上方", "PE.Views.Transitions.textTopRight": "右上方", "PE.Views.Transitions.textUnCover": "揭露", "PE.Views.Transitions.textVerticalIn": "垂直輸入", "PE.Views.Transitions.textVerticalOut": "由中向左右", "PE.Views.Transitions.textWedge": "楔", "PE.Views.Transitions.textWipe": "擦去", "PE.Views.Transitions.textZoom": "放大", "PE.Views.Transitions.textZoomIn": "放大", "PE.Views.Transitions.textZoomOut": "縮小", "PE.Views.Transitions.textZoomRotate": "放大與旋轉", "PE.Views.Transitions.txtApplyToAll": "應用於所有投影片", "PE.Views.Transitions.txtParameters": "參量", "PE.Views.Transitions.txtPreview": "預覽", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.textAlwaysShowToolbar": "永遠顯示工具欄", "PE.Views.ViewTab.textFitToSlide": "與投影片切合", "PE.Views.ViewTab.textFitToWidth": "配合寬度", "PE.Views.ViewTab.textInterfaceTheme": "介面主題", "PE.Views.ViewTab.textNotes": "備忘稿", "PE.Views.ViewTab.textRulers": "尺規", "PE.Views.ViewTab.textStatusBar": "狀態欄", "PE.Views.ViewTab.textZoom": "放大", "PE.Views.ViewTab.tipFitToSlide": "與投影片切合", "PE.Views.ViewTab.tipFitToWidth": "配合寬度", "PE.Views.ViewTab.tipInterfaceTheme": "介面主題"}