﻿<!DOCTYPE html>
<html>
	<head>
		<title>PROGNOSE.LINEAR-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>PROGNOSE.LINEAR-Funktion</h1>
			<p>Die Funktion <b>PROGNOSE.LINEAR</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den Schätzwert für einen linearen Trend zurück. Der Vorhersagewert ist ein Y-Wert bei einem gegebenen X-Wert. Bei den bekannten Werten handelt es sich um vorhandene X- und Y-Werte, und der neue Wert wird, ausgehend von einer linearen Regression, vorhergesagt.</p>
			<p>Die Formelsyntax der Funktion <b>PROGNOSE.LINEAR</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>PROGNOSE.LINEAR(x;Y_Werte;X_Werte)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
				<p style="text-indent: 50px;"><b><em>x</em></b> ist der Datenpunkt, dessen Wert Sie schätzen möchten. Ein nummerischer Wert, der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p>
				<p style="text-indent: 50px;">wo <b><em>Y_Werte</em></b> ist eine abhängige Matrix oder ein abhängiger Datenbereich.</p>
            <p style="text-indent: 50px;">wo <b><em>X_Werte</em></b> ist eine unabhängige Matrix oder ein unabhängiger Datenbereich.</p>
			<p>Anwendung der Funktion <b>PROGNOSE.LINEAR</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>PROGNOSE.LINEAR</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="PROGNOSE.LINEAR-Funktion" src="../images/forecast-linear.png" /></p>
		</div>
	</body>
</html>