{"DATE": "DATUM", "DATEDIF": "DATEDIF", "DATEVALUE": "DATUMHODN", "DAY": "DEN", "DAYS": "DAYS", "DAYS360": "ROK360", "EDATE": "EDATE", "EOMONTH": "EOMONTH", "HOUR": "HODINA", "ISOWEEKNUM": "ISOWEEKNUM", "MINUTE": "MINUTA", "MONTH": "MĚSÍC", "NETWORKDAYS": "NETWORKDAYS", "NETWORKDAYS.INTL": "NETWORKDAYS.INTL", "NOW": "NYNÍ", "SECOND": "SEKUNDA", "TIME": "ČAS", "TIMEVALUE": "ČASHODN", "TODAY": "DNES", "WEEKDAY": "DENTÝDNE", "WEEKNUM": "WEEKNUM", "WORKDAY": "WORKDAY", "WORKDAY.INTL": "WORKDAY.INTL", "YEAR": "ROK", "YEARFRAC": "YEARFRAC", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN2DEC", "BIN2HEX": "BIN2HEX", "BIN2OCT": "BIN2OCT", "BITAND": "BITAND", "BITLSHIFT": "BITLSHIFT", "BITOR": "BITOR", "BITRSHIFT": "BITRSHIFT", "BITXOR": "BITXOR", "COMPLEX": "COMPLEX", "CONVERT": "CONVERT", "DEC2BIN": "DEC2BIN", "DEC2HEX": "DEC2HEX", "DEC2OCT": "DEC2OCT", "DELTA": "DELTA", "ERF": "ERF", "ERF.PRECISE": "ERF.PRECISE", "ERFC": "ERFC", "ERFC.PRECISE": "ERFC.PRECISE", "GESTEP": "GESTEP", "HEX2BIN": "HEX2BIN", "HEX2DEC": "HEX2DEC", "HEX2OCT": "HEX2OCT", "IMABS": "IMABS", "IMAGINARY": "IMAGINARY", "IMARGUMENT": "IMARGUMENT", "IMCONJUGATE": "IMCONJUGATE", "IMCOS": "IMCOS", "IMCOSH": "IMCOSH", "IMCOT": "IMCOT", "IMCSC": "IMCSC", "IMCSCH": "IMCSCH", "IMDIV": "IMDIV", "IMEXP": "IMEXP", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMPOWER", "IMPRODUCT": "IMPRODUCT", "IMREAL": "IMREAL", "IMSEC": "IMSEC", "IMSECH": "IMSECH", "IMSIN": "IMSIN", "IMSINH": "IMSINH", "IMSQRT": "IMSQRT", "IMSUB": "IMSUB", "IMSUM": "IMSUM", "IMTAN": "IMTAN", "OCT2BIN": "OCT2BIN", "OCT2DEC": "OCT2DEC", "OCT2HEX": "OCT2HEX", "DAVERAGE": "DPRŮMĚR", "DCOUNT": "DPOČET", "DCOUNTA": "DPOČET2", "DGET": "DZÍSKAT", "DMAX": "DMAX", "DMIN": "DMIN", "DPRODUCT": "DSOUČIN", "DSTDEV": "DSMODCH.VÝBĚR", "DSTDEVP": "DSMODCH", "DSUM": "DSUMA", "DVAR": "DVAR.VÝBĚR", "DVARP": "DVAR", "CHAR": "ZNAK", "CLEAN": "VYČISTIT", "CODE": "KÓD", "CONCATENATE": "CONCATENATE", "CONCAT": "CONCAT", "DOLLAR": "KČ", "EXACT": "STEJNÉ", "FIND": "NAJÍT", "FINDB": "FINDB", "FIXED": "ZAOKROUHLIT.NA.TEXT", "LEFT": "ZLEVA", "LEFTB": "LEFTB", "LEN": "DÉLKA", "LENB": "LENB", "LOWER": "MALÁ", "MID": "ČÁST", "MIDB": "MIDB", "NUMBERVALUE": "NUMBERVALUE", "PROPER": "VELKÁ2", "REPLACE": "NAHRADIT", "REPLACEB": "REPLACEB", "REPT": "OPAKOVAT", "RIGHT": "ZPRAVA", "RIGHTB": "RIGHTB", "SEARCH": "HLEDAT", "SEARCHB": "SEARCHB", "SUBSTITUTE": "DOSADIT", "T": "T", "T.TEST": "T.TEST", "TEXT": "HODNOTA.NA.TEXT", "TEXTJOIN": "TEXTJOIN", "TREND": "LINTREND", "TRIM": "PROČISTIT", "TRIMMEAN": "TRIMMEAN", "TTEST": "TTEST", "UNICHAR": "UNICHAR", "UNICODE": "UNICODE", "UPPER": "VELKÁ", "VALUE": "HODNOTA", "AVEDEV": "PRŮMODCHYLKA", "AVERAGE": "PRŮMĚR", "AVERAGEA": "AVERAGEA", "AVERAGEIF": "AVERAGEIF", "AVERAGEIFS": "AVERAGEIFS", "BETADIST": "BETADIST", "BETAINV": "BETAINV", "BETA.DIST": "BETA.DIST", "BETA.INV": "BETA.INV", "BINOMDIST": "BINOMDIST", "BINOM.DIST": "BINOM.DIST", "BINOM.DIST.RANGE": "BINOM.DIST.RANGE", "BINOM.INV": "BINOM.INV", "CHIDIST": "CHIDIST", "CHIINV": "CHIINV", "CHITEST": "CHITEST", "CHISQ.DIST": "CHISQ.DIST", "CHISQ.DIST.RT": "CHISQ.DIST.RT", "CHISQ.INV": "CHISQ.INV", "CHISQ.INV.RT": "CHISQ.INV.RT", "CHISQ.TEST": "CHISQ.TEST", "CONFIDENCE": "CONFIDENCE", "CONFIDENCE.NORM": "CONFIDENCE.NORM", "CONFIDENCE.T": "CONFIDENCE.T", "CORREL": "CORREL", "COUNT": "POČET", "COUNTA": "POČET2", "COUNTBLANK": "COUNTBLANK", "COUNTIF": "COUNTIF", "COUNTIFS": "COUNTIFS", "COVAR": "COVAR", "COVARIANCE.P": "COVARIANCE.P", "COVARIANCE.S": "COVARIANCE.S", "CRITBINOM": "CRITBINOM", "DEVSQ": "DEVSQ", "EXPON.DIST": "EXPON.DIST", "EXPONDIST": "EXPONDIST", "FDIST": "FDIST", "FINV": "FINV", "FTEST": "FTEST", "F.DIST": "F.DIST", "F.DIST.RT": "F.DIST.RT", "F.INV": "F.INV", "F.INV.RT": "F.INV.RT", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "FORECAST", "FORECAST.ETS": "FORECAST.ETS", "FORECAST.ETS.CONFINT": "FORECAST.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "FORECAST.ETS.SEASONALITY", "FORECAST.ETS.STAT": "FORECAST.ETS.STAT", "FORECAST.LINEAR": "FORECAST.LINEAR", "FREQUENCY": "ČETNOSTI", "GAMMA": "GAMMA", "GAMMADIST": "GAMMADIST", "GAMMA.DIST": "GAMMA.DIST", "GAMMAINV": "GAMMAINV", "GAMMA.INV": "GAMMA.INV", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.PRECISE", "GAUSS": "GAUSS", "GEOMEAN": "GEOMEAN", "GROWTH": "LOGLINTREND", "HARMEAN": "HARMEAN", "HYPGEOM.DIST": "HYPGEOM.DIST", "HYPGEOMDIST": "HYPGEOMDIST", "INTERCEPT": "INTERCEPT", "KURT": "KURT", "LARGE": "LARGE", "LINEST": "LINREGRESE", "LOGEST": "LOGLINREGRESE", "LOGINV": "LOGINV", "LOGNORM.DIST": "LOGNORM.DIST", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOGNORMDIST", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAXIFS", "MEDIAN": "MEDIAN", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MINIFS", "MODE": "MODE", "MODE.MULT": "MODE.MULT", "MODE.SNGL": "MODE.SNGL", "NEGBINOM.DIST": "NEGBINOM.DIST", "NEGBINOMDIST": "NEGBINOMDIST", "NORM.DIST": "NORM.DIST", "NORM.INV": "NORM.INV", "NORM.S.DIST": "NORM.S.DIST", "NORM.S.INV": "NORM.S.INV", "NORMDIST": "NORMDIST", "NORMINV": "NORMINV", "NORMSDIST": "NORMSDIST", "NORMSINV": "NORMSINV", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTIL", "PERCENTILE.EXC": "PERCENTIL.EXC", "PERCENTILE.INC": "PERCENTIL.INC", "PERCENTRANK": "PERCENTRANK", "PERCENTRANK.EXC": "PERCENTRANK.EXC", "PERCENTRANK.INC": "PERCENTRANK.INC", "PERMUT": "PERMUTACE", "PERMUTATIONA": "PERMUTATIONA", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.DIST", "PROB": "PROB", "QUARTILE": "QUARTIL", "QUARTILE.INC": "QUARTIL.INC", "QUARTILE.EXC": "QUARTIL.EXC", "RANK.AVG": "RANK.AVG", "RANK.EQ": "RANK.EQ", "RANK": "RANK", "RSQ": "RKQ", "SKEW": "SKEW", "SKEW.P": "SKEW.P", "SLOPE": "SLOPE", "SMALL": "SMALL", "STANDARDIZE": "STANDARDIZE", "STDEV": "SMODCH.VÝBĚR", "STDEV.P": "SMODCH.P", "STDEV.S": "SMODCH.VÝBĚR.S", "STDEVA": "STDEVA", "STDEVP": "SMODCH", "STDEVPA": "STDEVPA", "STEYX": "STEYX", "TDIST": "TDIST", "TINV": "TINV", "T.DIST": "T.DIST", "T.DIST.2T": "T.DIST.2T", "T.DIST.RT": "T.DIST.RT", "T.INV": "T.INV", "T.INV.2T": "T.INV.2T", "VAR": "VAR.VÝBĚR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VAR", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.DIST", "Z.TEST": "Z.TEST", "ZTEST": "ZTEST", "ACCRINT": "ACCRINT", "ACCRINTM": "ACCRINTM", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "COUPDAYBS", "COUPDAYS": "COUPDAYS", "COUPDAYSNC": "COUPDAYSNC", "COUPNCD": "COUPNCD", "COUPNUM": "COUPNUM", "COUPPCD": "COUPPCD", "CUMIPMT": "CUMIPMT", "CUMPRINC": "CUMPRINC", "DB": "ODPIS.ZRYCH", "DDB": "ODPIS.ZRYCH2", "DISC": "DISC", "DOLLARDE": "DOLLARDE", "DOLLARFR": "DOLLARFR", "DURATION": "DURATION", "EFFECT": "EFFECT", "FV": "BUDHODNOTA", "FVSCHEDULE": "FVSCHEDULE", "INTRATE": "INTRATE", "IPMT": "PLATBA.ÚROK", "IRR": "MÍRA.VÝNOSNOSTI", "ISPMT": "ISPMT", "MDURATION": "MDURATION", "MIRR": "MOD.MÍRA.VÝNOSNOSTI", "NOMINAL": "NOMINAL", "NPER": "POČET.OBDOBÍ", "NPV": "ČISTÁ.SOUČHODNOTA", "ODDFPRICE": "ODDFPRICE", "ODDFYIELD": "ODDFYIELD", "ODDLPRICE": "ODDLPRICE", "ODDLYIELD": "ODDLYIELD", "PDURATION": "PDURATION", "PMT": "PLATBA", "PPMT": "PLATBA.ZÁKLAD", "PRICE": "PRICE", "PRICEDISC": "PRICEDISC", "PRICEMAT": "PRICEMAT", "PV": "SOUČHODNOTA", "RATE": "ÚROKOVÁ.MÍRA", "RECEIVED": "RECEIVED", "RRI": "RRI", "SLN": "ODPIS.LIN", "SYD": "ODPIS.NELIN", "TBILLEQ": "TBILLEQ", "TBILLPRICE": "TBILLPRICE", "TBILLYIELD": "TBILLYIELD", "VDB": "ODPIS.ZA.INT", "XIRR": "XIRR", "XNPV": "XNPV", "YIELD": "YIELD", "YIELDDISC": "YIELDDISC", "YIELDMAT": "YIELDMAT", "ABS": "ABS", "ACOS": "ARCCOS", "ACOSH": "ARCCOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "AGGREGATE", "ARABIC": "ARABIC", "ASC": "ASC", "ASIN": "ARCSIN", "ASINH": "ARCSINH", "ATAN": "ARCTG", "ATAN2": "ARCTG2", "ATANH": "ARCTGH", "BASE": "BASE", "CEILING": "ZAOKR.NAHORU", "CEILING.MATH": "CEILING.MATH", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "KOMBINACE", "COMBINA": "COMBINA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DECIMAL", "DEGREES": "DEGREES", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "ZAOKROUHLIT.NA.SUDÉ", "EXP": "EXP", "FACT": "FAKTORIÁL", "FACTDOUBLE": "FACTDOUBLE", "FLOOR": "ZAOKR.DOLŮ", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "FLOOR.MATH", "GCD": "GCD", "INT": "CELÁ.ČÁST", "ISO.CEILING": "ISO.CEILING", "LCM": "LCM", "LN": "LN", "LOG": "LOGZ", "LOG10": "LOG", "MDETERM": "DETERMINANT", "MINVERSE": "INVERZE", "MMULT": "SOUČIN.MATIC", "MOD": "MOD", "MROUND": "MROUND", "MULTINOMIAL": "MULTINOMIAL", "MUNIT": "MUNIT", "ODD": "ZAOKROUHLIT.NA.LICHÉ", "PI": "PI", "POWER": "POWER", "PRODUCT": "SOUČIN", "QUOTIENT": "QUOTIENT", "RADIANS": "RADIANS", "RAND": "NÁHČÍSLO", "RANDARRAY": "RANDARRAY", "RANDBETWEEN": "RANDBETWEEN", "ROMAN": "ROMAN", "ROUND": "ZAOKROUHLIT", "ROUNDDOWN": "ROUNDDOWN", "ROUNDUP": "ROUNDUP", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SERIESSUM", "SIGN": "SIGN", "SIN": "SIN", "SINH": "SINH", "SQRT": "ODMOCNINA", "SQRTPI": "SQRTPI", "SUBTOTAL": "SUBTOTAL", "SUM": "SUMA", "SUMIF": "SUMIF", "SUMIFS": "SUMIFS", "SUMPRODUCT": "SOUČIN.SKALÁRNÍ", "SUMSQ": "SUMA.ČTVERCŮ", "SUMX2MY2": "SUMX2MY2", "SUMX2PY2": "SUMX2PY2", "SUMXMY2": "SUMXMY2", "TAN": "TG", "TANH": "TGH", "TRUNC": "USEKNOUT", "ADDRESS": "ODKAZ", "CHOOSE": "ZVOLIT", "COLUMN": "SLOUPEC", "COLUMNS": "SLOUPCE", "FORMULATEXT": "FORMULATEXT", "HLOOKUP": "VVYHLEDAT", "HYPERLINK": "HYPERTEXTOVÝ.ODKAZ", "INDEX": "INDEX", "INDIRECT": "NEPŘÍMÝ.ODKAZ", "LOOKUP": "VYHLEDAT", "MATCH": "POZVYHLEDAT", "OFFSET": "POSUN", "ROW": "ŘÁDEK", "ROWS": "ŘÁDKY", "TRANSPOSE": "TRANSPOZICE", "UNIQUE": "UNIQUE", "VLOOKUP": "SVYHLEDAT", "XLOOKUP": "XLOOKUP", "CELL": "CELL", "ERROR.TYPE": "CHYBA.TYP", "ISBLANK": "JE.PRÁZDNÉ", "ISERR": "JE.<PERSON>Y<PERSON>", "ISERROR": "JE.<PERSON>Y<PERSON>H<PERSON>", "ISEVEN": "ISEVEN", "ISFORMULA": "ISFORMULA", "ISLOGICAL": "JE.LOGHODN", "ISNA": "JE.NEDEF", "ISNONTEXT": "JE.NETEXT", "ISNUMBER": "JE.ČISLO", "ISODD": "ISODD", "ISREF": "JE.ODKAZ", "ISTEXT": "JE.TEXT", "N": "N", "NA": "NEDEF", "SHEET": "SHEET", "SHEETS": "SHEETS", "TYPE": "TYP", "AND": "A", "FALSE": "NEPRAVDA", "IF": "KDYŽ", "IFS": "IFS", "IFERROR": "IFERROR", "IFNA": "IFNA", "NOT": "NE", "OR": "NEBO", "SWITCH": "SWITCH", "TRUE": "PRAVDA", "XOR": "XOR", "TEXTBEFORE": "TEXTPŘED", "TEXTAFTER": "TEXTZA", "TEXTSPLIT": "ROZDĚLIT.TEXT", "WRAPROWS": "ZABALŘÁDKY", "VSTACK": "SROVNAT.SVISLE", "HSTACK": "SROVNAT.VODOROVNĚ", "CHOOSEROWS": "ZVOLITŘÁDKY", "CHOOSECOLS": "ZVOLITSLOUPCE", "TOCOL": "DO.SLOUPCE", "TOROW": "DO.ŘÁDKU", "WRAPCOLS": "ZABALSLOUPCE", "TAKE": "VZÍT", "DROP": "ZAHODIT", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}