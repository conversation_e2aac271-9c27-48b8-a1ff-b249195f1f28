{"common.view.modals.txtCopy": "In die Zwischenablage kopieren", "common.view.modals.txtEmbed": "Einbetten", "common.view.modals.txtHeight": "<PERSON><PERSON><PERSON>", "common.view.modals.txtShare": "<PERSON> te<PERSON>n", "common.view.modals.txtWidth": "Breite", "common.view.SearchBar.textFind": "<PERSON><PERSON>", "SSE.ApplicationController.convertationErrorText": "Konvertierung ist fehlgeschlagen.", "SSE.ApplicationController.convertationTimeoutText": "Zeitüberschreitung bei der Konvertierung.", "SSE.ApplicationController.criticalErrorTitle": "<PERSON><PERSON>", "SSE.ApplicationController.downloadErrorText": "Herunterladen ist fehlgeschlagen.", "SSE.ApplicationController.downloadTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wird heruntergeladen...", "SSE.ApplicationController.errorAccessDeny": "<PERSON><PERSON>, eine Aktion durchzuführen, für die Si<PERSON> keine Rechte haben.<br><PERSON>te wenden Sie sich an Ihren Document Serveradministrator.", "SSE.ApplicationController.errorDefaultMessage": "Fehlercode: %1", "SSE.ApplicationController.errorFilePassProtect": "Das Dokument ist kennwortgeschützt und kann nicht geöffnet werden.", "SSE.ApplicationController.errorFileSizeExceed": "Die Dateigröße überschreitet die für Ihren Server festgelegte Größe.<br>Weitere Informationen können Si<PERSON> von Ihrem Document Server-Administrator er<PERSON><PERSON>.", "SSE.ApplicationController.errorForceSave": "<PERSON>im Speichern der Datei ist ein Fehler aufgetreten. Verwenden Sie die Option \"Herunterladen als\", um die Datei auf Ihrer Computerfestplatte zu speichern oder versuchen Sie es später erneut.", "SSE.ApplicationController.errorInconsistentExt": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei stimmt nicht mit der Dateierweiterung überein.", "SSE.ApplicationController.errorInconsistentExtDocx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Textdokumenten (z.B. docx), aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.ApplicationController.errorInconsistentExtPdf": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht einem der folgenden Formate: pdf/djvu/xps/oxps, aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.ApplicationController.errorInconsistentExtPptx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Präsentationen (z.B. pptx), aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.ApplicationController.errorInconsistentExtXlsx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Tabellenkalkulationen (z.B. xlsx), aber die Datei hat die inkonsistente Erweiterung: %1.", "SSE.ApplicationController.errorLoadingFont": "Schriftarten nicht hochgeladen.<br><PERSON><PERSON> wenden <PERSON> sich an <PERSON><PERSON> von Ihrem Document Server.", "SSE.ApplicationController.errorTokenExpire": "Sicherheitstoken des Dokuments ist abgelaufen.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "SSE.ApplicationController.errorUpdateVersionOnDisconnect": "Die Internetverbindung wurde wiederhergestellt und die Dateiversion wurde geändert.<br><PERSON><PERSON> weiterarbeiten können, müssen Sie die Datei herunterladen oder den Inhalt kopieren, um sicherzustellen, dass nichts verloren geht, und diese Seite anschließend neu laden.", "SSE.ApplicationController.errorUserDrop": "<PERSON><PERSON> auf diese Date<PERSON> möglich.", "SSE.ApplicationController.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.ApplicationController.openErrorText": "<PERSON><PERSON> dieser Datei ist ein Fehler aufgetreten.", "SSE.ApplicationController.scriptLoadError": "Die Verbindung ist zu langsam, einige der Komponenten konnten nicht geladen werden. Bitte laden Sie die Se<PERSON> erneut.", "SSE.ApplicationController.textAnonymous": "Anonym", "SSE.ApplicationController.textGuest": "Gas<PERSON>", "SSE.ApplicationController.textLoadingDocument": "<PERSON><PERSON>e wird geladen", "SSE.ApplicationController.textOf": "von", "SSE.ApplicationController.txtClose": "Schließen", "SSE.ApplicationController.unknownErrorText": "Unbek<PERSON><PERSON> Fehler.", "SSE.ApplicationController.unsupportedBrowserErrorText": "<PERSON><PERSON> wird nicht unterstützt.", "SSE.ApplicationController.waitText": "Bitte warten...", "SSE.ApplicationView.txtDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.ApplicationView.txtEmbed": "Einbetten", "SSE.ApplicationView.txtFileLocation": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "SSE.ApplicationView.txtFullScreen": "Vollbild-Modus", "SSE.ApplicationView.txtPrint": "<PERSON><PERSON><PERSON>", "SSE.ApplicationView.txtSearch": "<PERSON><PERSON>", "SSE.ApplicationView.txtShare": "Freigeben"}