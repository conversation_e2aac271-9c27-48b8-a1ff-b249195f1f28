{"common.view.modals.txtCopy": "<PERSON><PERSON>in ke papan klip", "common.view.modals.txtEmbed": "Melekatkan", "common.view.modals.txtHeight": "Tingg<PERSON>", "common.view.modals.txtShare": "<PERSON><PERSON>", "common.view.modals.txtWidth": "<PERSON><PERSON>", "common.view.SearchBar.textFind": "Temukan", "SSE.ApplicationController.convertationErrorText": "<PERSON><PERSON><PERSON><PERSON> gagal.", "SSE.ApplicationController.convertationTimeoutText": "<PERSON><PERSON>tu konversi habis.", "SSE.ApplicationController.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.ApplicationController.downloadErrorText": "<PERSON><PERSON><PERSON> gagal.", "SSE.ApplicationController.downloadTextText": "Mengunduh spread sheet", "SSE.ApplicationController.errorAccessDeny": "<PERSON><PERSON> mencoba melakukan", "SSE.ApplicationController.errorDefaultMessage": "Kode kesalahan %1", "SSE.ApplicationController.errorFilePassProtect": "Dokumen dilindungi dengan kata sandi dan tidak dapat dibuka.", "SSE.ApplicationController.errorFileSizeExceed": "<PERSON><PERSON><PERSON> me<PERSON><PERSON> uk<PERSON>n ", "SSE.ApplicationController.errorForceSave": "<PERSON> kesalahan saat menyimpan file. <PERSON><PERSON><PERSON> gunakan opsi 'Download sebagai' untuk menyimpan file ke komputer Anda dan coba lagi.", "SSE.ApplicationController.errorInconsistentExt": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file tidak cocok dengan ekstensi file.", "SSE.ApplicationController.errorInconsistentExtDocx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan dokumen teks (mis. docx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.ApplicationController.errorInconsistentExtPdf": "<PERSON><PERSON><PERSON> kesalahan terjadi ketika membuka file.<br>Isi file berhubungan dengan satu dari format berikut: pdf/djvu/xps/oxps, tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.ApplicationController.errorInconsistentExtPptx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan presentasi (mis. pptx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.ApplicationController.errorInconsistentExtXlsx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan spreadsheet (mis. xlsx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "SSE.ApplicationController.errorLoadingFont": "Font tidak bisa dimuat.<br><PERSON><PERSON><PERSON> kontak admin Server <PERSON><PERSON><PERSON>.", "SSE.ApplicationController.errorTokenExpire": "Token keamanan dokumen sudah kadaluwarsa.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "SSE.ApplicationController.errorUpdateVersionOnDisconnect": "Koneksi internet sudah kembali dan versi file sudah diganti.<br>Sebelum Anda bisa melanjutkan kerja, Anda perlu mengunduh file atau salin konten untuk memastikan tidak ada yang hilang, lalu muat ulang halaman ini.", "SSE.ApplicationController.errorUserDrop": "File tidak dapat di akses", "SSE.ApplicationController.notcriticalErrorTitle": "Peringatan", "SSE.ApplicationController.openErrorText": "Kesalahan terjadi ketika membuka file.", "SSE.ApplicationController.scriptLoadError": "Hubungan terlalu lambat", "SSE.ApplicationController.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.ApplicationController.textGuest": "<PERSON><PERSON>", "SSE.ApplicationController.textLoadingDocument": "Memuat spread sheet", "SSE.ApplicationController.textOf": "<PERSON><PERSON>", "SSE.ApplicationController.txtClose": "<PERSON><PERSON><PERSON>", "SSE.ApplicationController.unknownErrorText": "<PERSON><PERSON>r tidak diken<PERSON>.", "SSE.ApplicationController.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> kamu tidak didukung", "SSE.ApplicationController.waitText": "<PERSON><PERSON><PERSON>", "SSE.ApplicationView.txtDownload": "<PERSON><PERSON><PERSON>", "SSE.ApplicationView.txtEmbed": "Melekatkan", "SSE.ApplicationView.txtFileLocation": "<PERSON><PERSON> Dokumen", "SSE.ApplicationView.txtFullScreen": "<PERSON><PERSON> penuh", "SSE.ApplicationView.txtPrint": "Cetak", "SSE.ApplicationView.txtSearch": "<PERSON><PERSON>", "SSE.ApplicationView.txtShare": "Bagikan"}