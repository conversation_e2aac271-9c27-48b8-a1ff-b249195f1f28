{"common.view.modals.txtCopy": "Copy to clipboard", "common.view.modals.txtEmbed": "Embed", "common.view.modals.txtHeight": "Height", "common.view.modals.txtShare": "Share Link", "common.view.modals.txtWidth": "<PERSON><PERSON><PERSON>", "common.view.SearchBar.textFind": "Find", "SSE.ApplicationController.convertationErrorText": "Conversion failed.", "SSE.ApplicationController.convertationTimeoutText": "Conversion timeout exceeded.", "SSE.ApplicationController.criticalErrorTitle": "Error", "SSE.ApplicationController.downloadErrorText": "Download failed.", "SSE.ApplicationController.downloadTextText": "Downloading spreadsheet...", "SSE.ApplicationController.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "SSE.ApplicationController.errorDefaultMessage": "Error code: %1", "SSE.ApplicationController.errorFilePassProtect": "The file is password protected and cannot be opened.", "SSE.ApplicationController.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "SSE.ApplicationController.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to your computer hard drive or try again later.", "SSE.ApplicationController.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.ApplicationController.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.ApplicationController.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.ApplicationController.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.ApplicationController.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.ApplicationController.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "SSE.ApplicationController.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "SSE.ApplicationController.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "SSE.ApplicationController.errorUserDrop": "The file cannot be accessed right now.", "SSE.ApplicationController.notcriticalErrorTitle": "Warning", "SSE.ApplicationController.openErrorText": "An error has occurred while opening the file.", "SSE.ApplicationController.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "SSE.ApplicationController.textAnonymous": "Anonymous", "SSE.ApplicationController.textGuest": "Guest", "SSE.ApplicationController.textLoadingDocument": "Loading spreadsheet", "SSE.ApplicationController.textOf": "of", "SSE.ApplicationController.txtClose": "Close", "SSE.ApplicationController.unknownErrorText": "Unknown error.", "SSE.ApplicationController.unsupportedBrowserErrorText": "Your browser is not supported.", "SSE.ApplicationController.waitText": "Please, wait...", "SSE.ApplicationView.txtDownload": "Download", "SSE.ApplicationView.txtEmbed": "Embed", "SSE.ApplicationView.txtFileLocation": "Open file location", "SSE.ApplicationView.txtFullScreen": "Full Screen", "SSE.ApplicationView.txtPrint": "Print", "SSE.ApplicationView.txtSearch": "Search", "SSE.ApplicationView.txtShare": "Share"}