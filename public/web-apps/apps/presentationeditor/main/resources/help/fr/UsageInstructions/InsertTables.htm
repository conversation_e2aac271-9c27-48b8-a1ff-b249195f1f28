﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insérer et mettre en forme des tableaux</title>
		<meta charset="utf-8" />
		<meta name="description" content="Ajouter un tableau à votre présentation et configurer ses paramètres"/>
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insérer et mettre en forme des tableaux</h1>
            <h3>Insérer un tableau</h3>
			<p>Pour insérer un tableau sur une diapositive dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Présentations</b></a>,</p>
			<ol>
				<li>sélectionnez la diapositive où le tableau sera ajouté,</li>
                <li>passez à l'onglet <b>Insertion</b> de la barre d'outils supérieure,</li>
				<li>cliquez sur l'icône <div class = "icon icon-inserttable"></div> <b>Tableau</b> sur la la barre d'outils supérieure,</li>
				<li>
                    sélectionnez l'une des options suivantes pour créer le tableau :
					<ul>
						<li>
                            <p>soit un tableau avec le nombre prédéfini de cellules (10 par 8 cellules maximum)</p>
						    <p>Si vous voulez ajouter rapidement un tableau, il vous suffit de sélectionner le nombre de lignes (8 au maximum) et de colonnes (10 au maximum).</p>
                        </li>
						<li>
                            <p>soit un tableau personnalisé</p>
						    <p>Si vous avez besoin d'un tableau de plus de 10 par 8 cellules, sélectionnez l'option <b>Insérer un tableau personnalisé</b> pour ouvrir la fenêtre et spécifiez le nombre nécessaire de lignes et de colonnes, ensuite cliquez sur le bouton <b>OK</b>.</p>
                        </li>
					</ul>
				</li>
                <li>
                    lorsque vous voulez insérer un tableau comme un objet OLE :
                    <ol>
                        <li>Sélectionnez l'option <b>Insérer la feuille de calcul</b> au menu <b>Tableau</b> dans l'onglet <b>Insérer</b>.</li>
                        <li>
                            La fenêtre correspondante s'ouvre dans laquelle vous pouvez saisir les données requises et les modifier en utilisant les outils de formatage du Tableur, par exemple <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/FontTypeSizeStyle.htm">sélectionner la police, le type et le style</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/ChangeNumberFormat.htm">saisir le format de nombre</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/InsertFunction.htm">insérer des functions</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/FormattedTables.htm">mettre en forme les tableaux</a> etc.
                            <p><img alt="OLE table" src="../../../../../../common/main/resources/help/fr/images/ole_table.png" /></p>
                        </li>
                        <li>L'en-tête contient le bouton <span class="icon icon-visible_area"></span> <b>Zone visible</b> dans le coin supérieur droit de la fenêtre. Choisissez l'option <b>Modifier la zone visible</b> afin de sélectionner la zone qui sera affichée quand l'objet est inséré au sein de la présentation ; les autres données ne sont pas perdues mais seulement masquées. Cliquez sur <b>Terminé</b> lorsque c'est prêt.</li>
                        <li>Cliquez sur le bouton <b>Afficher la zone visible</b> afin d'afficher la zone sélectionnée qui aura une bordure bleue.</li>
                        <li>Quand tout est prêt, cliquez sur le bouton <b>Enregistrer et quitter</b>.</li>
                    </ol>
                </li>
				<li>après avoir ajouté le tableau, vous pouvez modifier ses paramètres et son position.</li>
			</ol>
            <p>On peut aussi ajouter un tableau dans l'espace réservé en cliquant sur l'icône <span class="icon icon-placeholder_table"></span> <b>Tableau</b> à l'intérieur de l'espace et spécifier le nombre de cellules ou en utilisant l'option <b>Insérer un tableau personnalisé</b> :</p>
            <p><img alt="Ajouter un tableau dans l'espace réservé" src="../images/placeholder_object.png" /></p>
            <p>Pour déplacer une tableau, faites glisser les poignées <span class="icon icon-resize_square"></span>  situés sur ses bords jusqu'à ce que la taille du tableau soit atteinte.</p>
            <p><span class="big big-resizetable"></span></p>
            <p>Vous pouvez également modifier manuellement la largeur d'une certaine colonne ou la hauteur d'une ligne. Déplacez le curseur de la souris sur la bordure droite de la colonne de sorte que le curseur se transforme en flèche bidirectionnelle <span class="icon icon-changecolumnwidth"></span> et faites glisser la bordure vers la gauche ou la droite pour définir la largeur nécessaire. Pour modifier manuellement la hauteur d'une seule ligne, déplacez le curseur de la souris sur la bordure inférieure de la ligne afin que le curseur devienne la flèche bidirectionnelle <span class="icon icon-changerowheight"></span> et déplacez la bordure vers le haut ou le bas.</p>
			<p>Vous pouvez définir <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">la position du tableau</a> sur la diapositive en le faisant glisser horizontalement ou verticalement.</p>
            <p class="note">
                <b>Remarque</b> : pour vous déplacer dans un tableau, vous pouvez utiliser <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithtables" onclick="onhyperlinkclick(this)">des raccourcis clavier</a>.
            </p>
            <p>On peut aussi ajouter un tableau à la disposition de diapositive. Pour en savoir plus, veuillez consulter <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">cet article</a>.</p>
			<hr />
            <h3>Ajuster les paramètres du tableau</h3>
			<img class="floatleft"alt="Table settings tab" src="../images/tablesettingstab.png" />
			<p>Plusieurs paramètres du tableau ainsi que sa structure peuvent être modifiés à l'aide de la barre latérale droite. Pour l'activer, cliquez sur le tableau et sélectionnez l'icône <b>Paramètres du graphique </b><span class="icon icon-table_settings_icon"></span> à droite.</p>
			<p>Les sections <b>Lignes</b> et <b>Colonnes</b> en haut vous permettent de mettre en évidence certaines lignes/colonnes en leur appliquant une mise en forme spécifique ou de mettre en évidence différentes lignes/colonnes avec les différentes couleurs d'arrière-plan pour les distinguer clairement. Les options suivantes sont disponibles :</p>
			<ul style="margin-left: 280px;">
				<li><b>En-tête</b> - accentue la ligne la plus haute du tableau avec un formatage spécial.</li>
				<li><b>Total</b> -accentue la ligne la plus basse du tableau avec un formatage spécial.</li>
				<li><b>Bordé</b> - permet l'alternance des couleurs d'arrière-plan pour les lignes paires et impaires.</li>
				<li><b>Première</b> - accentue la colonne la plus à gauche du tableau avec un formatage spécial.</li>
				<li><b>Dernière</b> - accentue la colonne la plus à droite du tableau avec un formatage spécial.</li>
				<li><b>Bordé</b> - permet l'alternance des couleurs d'arrière-plan pour les colonnes paires et impaires.</li>
			</ul>
			<p>
                La section <b>Sélectionner à partir d'un modèle</b> vous permet de choisir l'un des styles de tableaux prédéfinis. Chaque modèle combine certains paramètres de formatage, tels qu'une couleur d'arrière-plan, un style de bordure, des lignes/colonnes en bandes, etc.
                Selon les options cochées dans les sections <b>Lignes</b> et/ou <b>Colonnes ci-dessus</b>, l'ensemble de modèles sera affiché différemment. Par exemple, si vous avez coché l'option <b>En-tête</b> dans la section <b>Lignes</b> et l'option <b>Bordé</b> dans la section <b>Colonnes</b>, la liste des modèles affichés inclura uniquement les modèles avec la ligne d'en-tête et les colonnes en bandes activées :
            </p>
			<p><span class="big big-templateslist"></span></p>
			<p>La section <b>Style de bordure</b> vous permet de modifier la mise en forme appliquée qui correspond au modèle sélectionné. Vous pouvez sélectionner toute la table ou une certaine plage de cellules dont vous souhaitez modifier la mise en forme et définir tous les paramètres manuellement.</p>
			<ul>
				<li>
                    Paramètres de <b>Bordure</b> - définissez la largeur de la bordure en utilisant la liste <div class = "big big-bordersize"></div> (ou choisissez l'option <b>Aucune bordure</b>), sélectionnez sa <b>Couleur</b> dans les palettes disponibles et déterminez la façon dont elle sera affichée dans les cellules en cliquant sur les icônes : 
				    <p><span class="big big-bordertype"></span></p>
				</li>
				<li><b>Couleur d'arrière-plan</b> - sélectionnez la couleur de l'arrière-plan dans les cellules sélectionnées.</li>
			</ul>
			<p>La section <b>Lignes et colonnes</b> <span class="icon icon-rowsandcolumns"></span> vous permet d'effectuer les opérations suivantes :</p>
			<ul>
				<li><b>Sélectionner</b> une ligne, une colonne, une cellule (en fonction de la position du curseur) ou la totalité du tableau.</li>
				<li><b>Insérer</b> une nouvelle ligne au-dessus ou en dessous de celle sélectionnée ainsi qu'une nouvelle colonne à gauche ou à droite de celle sélectionnée.</li>
				<li><b>Supprimer</b> une ligne, une colonne (en fonction de la position du curseur ou de la sélection) ou la totalité du tableau.</li>
				<li><b>Fusionner les cellules</b> - pour fusionner les cellules précédemment sélectionnées en une seule.</li>
				<li>
                    <b>Fractionner la cellule...</b> - scinder une cellule précédemment sélectionnée en un certain nombre de lignes et de colonnes. Cette option ouvre la fenêtre suivante :
				    <p><img alt="Fenêtre Scinder les cellules" src="../images/split_cells.png" /></p>
				    <p>Entrez le <b>Nombre de colonnes</b> et le <b>Nombre de lignes</b> en lesquelles la cellule sélectionnée doit être divisée et appuyez sur <b>OK</b>.</p>
				</li>
			</ul>
			<p class="note"><b>Remarque :</b> les options de la section <b>Lignes et colonnes</b> sont également accessibles depuis le <b>menu contextuel</b>.</p>
            <p><b>Taille de la cellule</b> est utilisée pour ajuster la largeur et la hauteur de la cellule actuellement sélectionnée. Dans cette section, vous pouvez également <b>Distribuer les lignes</b> afin que toutes les cellules sélectionnées aient la même hauteur ou <b>Distribuer les colonnes</b> de sorte que toutes les cellules sélectionnées aient la même largeur. Les options <b>Distribuer les lignes</b>/<b>les colonnes</b> sont également accessibles depuis le <b>menu contextuel</b>.</p>
			<hr />
            <h3>Configurer les paramètres avancés du tableau</h3>
			<p>Pour modifier les paramètres du tableau avancés, cliquez sur le tableau avec le clic droit de la souris et sélectionnez l'option <b>Paramètres avancés du tableau</b> du menu contextuel ou utilisez le lien <b>Afficher les paramètres avancés</b> sur la barre latérale droite. La fenêtre paramètres de l'image s'ouvre :</p>
			<p><img alt="Paramètres du tableau" src="../images/table_properties.png" /></p>
            <p>L'onglet <b>Emplacement</b> permet de saisir les paramètres du tableau suivants :</p>
            <ul>
                <li><b>Taille</b> - utilisez cette option pour modifier la largeur et/ou la hauteur du tableau. Si le bouton <b>Proportions constantes</b> <div class="icon icon-constantproportions"></div> est activé (dans ce cas, il ressemble à ceci <div class="icon icon-constantproportionsactivated"></div>), la largeur et la hauteur seront changées en même temps, le ratio d'aspect de l'image originale sera préservé.</li>
                <li><b>Position</b> - saisissez la position exacte en utilisant les champs <b>Horizontalement</b> et <b>Verticalement</b>, ainsi que le champ <b>De</b> dans lesquels vous pouvez accéder aux paramètres tels que <b>Coin supérieur gauche</b> et <b>Au centre</b>.</li>
            </ul>
            <p><img alt="Paramètres du tableau" src="../images/table_properties.png" /></p>
			<p>La section <b>Marges</b> permet d'ajuster l'espace entre le texte dans les cellules et la bordure de la cellule :</p>
			<ul>
				<li>entrez manuellement les valeurs de <b>Marges de cellule</b>, ou</li>
				<li>cochez la case <b>Utiliser les marges par défaut</b> pour appliquer les valeurs prédéfinies (si nécessaire, elles peuvent également être ajustées).</li>
			</ul>
            <p><img alt="Paramètres du tableau" src="../images/table_properties1.png" /></p>
            <p>L'onglet <b>Texte de remplacement</b> permet de spécifier un <b>Titre</b> et une <b>Description</b> qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du tableau.</p>
			<hr />
			<p>Pour <b>mettre en forme le texte saisi</b> dans les cellules du tableau, vous pouvez utiliser <a href="../UsageInstructions/InsertText.htm#formatfont" onclick="onhyperlinkclick(this)">les icônes dans l'onglet Accueil de la barre d'outils supérieure</a>. Le <b>menu contextuel</b> qui s'affiche lorsque vous cliquez sur la table avec le bouton droit de la souris inclut deux options supplémentaires : </p>
			<ul>
			    <li><b>Alignement vertical des cellules</b> - vous permet de définir le type préféré d'alignement vertical du texte dans les cellules sélectionnées : <b>Aligner en haut</b>, <b>Aligner au centre</b> ou <b>Aligner en bas</b>.</li>
			    <li><b>Lien hypertexte</b> - vous permet d'insérer un lien hypertexte dans la cellule sélectionnée.</li>
			</ul>
		</div>
	</body>
</html>
