{"Common.Controllers.Chat.notcriticalErrorTitle": "Avviso", "Common.Controllers.Chat.textEnterMessage": "inserisci il tuo messaggio qui", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "L'oggetto è disabilitato perché un altro utente lo sta modificando.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Avviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "L'oggetto è disabilitato perché è stato modificato da un altro utente.", "Common.Controllers.ExternalOleEditor.warningTitle": "Avvertimento", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Area impilata", "Common.define.chartData.textAreaStackedPer": "Area impilata al 100%", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal3d": "Colonna 3D raggruppata", "Common.define.chartData.textBarNormal3dPerspective": "Colonna in 3D", "Common.define.chartData.textBarStacked": "Colonna in pila", "Common.define.chartData.textBarStacked3d": "Colonna 3D impilata", "Common.define.chartData.textBarStackedPer": "Colonna in pila 100%", "Common.define.chartData.textBarStackedPer3d": "Colonna 3D al 100% con impilamento", "Common.define.chartData.textCharts": "<PERSON><PERSON>", "Common.define.chartData.textColumn": "<PERSON>onna", "Common.define.chartData.textCombo": "Combinato", "Common.define.chartData.textComboAreaBar": "Area in pila - colonna raggruppata", "Common.define.chartData.textComboBarLine": "<PERSON>onna rag<PERSON> - riga", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON>na rag<PERSON> - linea sull'asse secondario", "Common.define.chartData.textComboCustom": "Combinazione personalizzata", "Common.define.chartData.textDoughnut": "Grafico a doppia ciambella", "Common.define.chartData.textHBarNormal": "Barra raggruppata", "Common.define.chartData.textHBarNormal3d": "Barra 3D raggruppata", "Common.define.chartData.textHBarStacked": "Barra in pila", "Common.define.chartData.textHBarStacked3d": "Barra 3D impilata", "Common.define.chartData.textHBarStackedPer": "Barra in pila al 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D 100% impilata", "Common.define.chartData.textLine": "A linee", "Common.define.chartData.textLine3d": "Linea in 3D", "Common.define.chartData.textLineMarker": "Linea con pennarelli", "Common.define.chartData.textLineStacked": "Linea impilata", "Common.define.chartData.textLineStackedMarker": "Linea impilata con pennarelli", "Common.define.chartData.textLineStackedPer": "Linea impilata al 100%", "Common.define.chartData.textLineStackedPerMarker": "Linea impilata al 100% con pennarelli", "Common.define.chartData.textPie": "A torta", "Common.define.chartData.textPie3d": "Grafico a torta in 3D", "Common.define.chartData.textPoint": "XY (A dispersione)", "Common.define.chartData.textScatter": "Grafico a dispersione", "Common.define.chartData.textScatterLine": "Grafico a dispersione con linee rette", "Common.define.chartData.textScatterLineMarker": "Grafico a dispersione con linee rette e pennarelli", "Common.define.chartData.textScatterSmooth": "Grafico a dispersione con linee leggere", "Common.define.chartData.textScatterSmoothMarker": "Grafico a dispersione con linee e indicatori", "Common.define.chartData.textStock": "Azionario", "Common.define.chartData.textSurface": "Superficie", "Common.define.effectData.textAcross": "Orizzontalmente", "Common.define.effectData.textAppear": "Apparire", "Common.define.effectData.textArcDown": "Arco verso il basso", "Common.define.effectData.textArcLeft": "Arco a sinistra", "Common.define.effectData.textArcRight": "Arco a destra", "Common.define.effectData.textArcs": "<PERSON><PERSON>", "Common.define.effectData.textArcUp": "<PERSON><PERSON> in alto", "Common.define.effectData.textBasic": "Di base", "Common.define.effectData.textBasicSwivel": "Girevole di base", "Common.define.effectData.textBasicZoom": "Zoom di base", "Common.define.effectData.textBean": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBlinds": "Persiane", "Common.define.effectData.textBlink": "Lampeggiante", "Common.define.effectData.textBoldFlash": "Flash grassetto", "Common.define.effectData.textBoldReveal": "Rivelazione in grassetto", "Common.define.effectData.textBoomerang": "Boomerang", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "Rimbalzo a sinistra", "Common.define.effectData.textBounceRight": "Rim<PERSON>zo a destra", "Common.define.effectData.textBox": "Riquadro", "Common.define.effectData.textBrushColor": "Colore del pennello", "Common.define.effectData.textCenterRevolve": "Rotazione intorno al centro", "Common.define.effectData.textCheckerboard": "Scacchiera", "Common.define.effectData.textCircle": "Cerchio", "Common.define.effectData.textCollapse": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textColorPulse": "Impulso di colore", "Common.define.effectData.textComplementaryColor": "Colore complementare", "Common.define.effectData.textComplementaryColor2": "Colore complementare 2", "Common.define.effectData.textCompress": "Comprimi", "Common.define.effectData.textContrast": "Contrasto", "Common.define.effectData.textContrastingColor": "Colore a contrasto", "Common.define.effectData.textCredits": "Crediti", "Common.define.effectData.textCrescentMoon": "Luna crescente", "Common.define.effectData.textCurveDown": "Curva verso il basso", "Common.define.effectData.textCurvedSquare": "Quadrato curvato", "Common.define.effectData.textCurvedX": "X curvata", "Common.define.effectData.textCurvyLeft": "Curva verso sinistra", "Common.define.effectData.textCurvyRight": "Curva verso destra", "Common.define.effectData.textCurvyStar": "Stella curvata", "Common.define.effectData.textCustomPath": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCuverUp": "Curva verso alto", "Common.define.effectData.textDarken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDecayingWave": "<PERSON><PERSON> smorzata", "Common.define.effectData.textDesaturate": "Desaturare", "Common.define.effectData.textDiagonalDownRight": "Diagonale verso l'angolo destro inferiore", "Common.define.effectData.textDiagonalUpRight": "Diagonale verso l'angolo destro superiore", "Common.define.effectData.textDiamond": "Diamante", "Common.define.effectData.textDisappear": "Scomparire", "Common.define.effectData.textDissolveIn": "Sciogliere dentro", "Common.define.effectData.textDissolveOut": "Sciogliere fuori", "Common.define.effectData.textDown": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDrop": "<PERSON><PERSON>", "Common.define.effectData.textEmphasis": "Effetti di enfasi", "Common.define.effectData.textEntrance": "E<PERSON>tti di ingresso", "Common.define.effectData.textEqualTriangle": "Triangolo equilatero", "Common.define.effectData.textExciting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textExit": "Effetti di uscita", "Common.define.effectData.textExpand": "Espandi", "Common.define.effectData.textFade": "Dissolvenza", "Common.define.effectData.textFigureFour": "Figura quattro volte 8", "Common.define.effectData.textFillColor": "Colore di riempimento", "Common.define.effectData.textFlip": "Capovolgere", "Common.define.effectData.textFloat": "Fluttuare", "Common.define.effectData.textFloatDown": "Fluttuare verso il basso", "Common.define.effectData.textFloatIn": "Fluttuare dentro", "Common.define.effectData.textFloatOut": "Fluttuare fuori", "Common.define.effectData.textFloatUp": "Fluttuare verso altro", "Common.define.effectData.textFlyIn": "Volare dentro", "Common.define.effectData.textFlyOut": "Volare fuori", "Common.define.effectData.textFontColor": "Colore caratteri", "Common.define.effectData.textFootball": "Calcio", "Common.define.effectData.textFromBottom": "<PERSON> basso", "Common.define.effectData.textFromBottomLeft": "Dal basso a sinistra", "Common.define.effectData.textFromBottomRight": "Dal basso a destra", "Common.define.effectData.textFromLeft": "Da sinistra a destra", "Common.define.effectData.textFromRight": "Da destra a sinistra", "Common.define.effectData.textFromTop": "Dall'alto al basso", "Common.define.effectData.textFromTopLeft": "Da in alto a sinistra", "Common.define.effectData.textFromTopRight": "Da in alto a destra", "Common.define.effectData.textFunnel": "Imbuto", "Common.define.effectData.textGrowShrink": "Aumentare/Restringere", "Common.define.effectData.textGrowTurn": "Aumentare e girare", "Common.define.effectData.textGrowWithColor": "Aumentare con colore", "Common.define.effectData.textHeart": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHeartbeat": "Pulsazione", "Common.define.effectData.textHexagon": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textHorizontal": "Orizzontale", "Common.define.effectData.textHorizontalFigure": "Figura 8 orizzontale", "Common.define.effectData.textHorizontalIn": "Orizzontale dentro", "Common.define.effectData.textHorizontalOut": "Orizzontale fuori", "Common.define.effectData.textIn": "All'interno", "Common.define.effectData.textInFromScreenCenter": "Aumentare da centro schermo", "Common.define.effectData.textInSlightly": "<PERSON><PERSON>", "Common.define.effectData.textInToScreenBottom": "Zoom verso la parte inferiore dello schermo", "Common.define.effectData.textInvertedSquare": "Quadrato invertito", "Common.define.effectData.textInvertedTriangle": "Triangolo invertito", "Common.define.effectData.textLeft": "A sinistra", "Common.define.effectData.textLeftDown": "<PERSON><PERSON><PERSON> in basso", "Common.define.effectData.textLeftUp": "Sinistra in alto", "Common.define.effectData.textLighten": "Illuminare", "Common.define.effectData.textLineColor": "Colore linea", "Common.define.effectData.textLines": "Linee", "Common.define.effectData.textLinesCurves": "Linee Curve", "Common.define.effectData.textLoopDeLoop": "<PERSON><PERSON><PERSON> continuo", "Common.define.effectData.textLoops": "Loop", "Common.define.effectData.textModerate": "Moderato", "Common.define.effectData.textNeutron": "Neutrone", "Common.define.effectData.textObjectCenter": "Centro dell'oggetto", "Common.define.effectData.textObjectColor": "Colore dell'oggetto", "Common.define.effectData.textOctagon": "Ottagono", "Common.define.effectData.textOut": "All'esterno", "Common.define.effectData.textOutFromScreenBottom": "Allontanare dal fondo dello schermo", "Common.define.effectData.textOutSlightly": "<PERSON><PERSON> diminuz<PERSON>", "Common.define.effectData.textOutToScreenCenter": "Diminuire verso il centro dello schermo", "Common.define.effectData.textParallelogram": "Parallelogram<PERSON>", "Common.define.effectData.textPath": "Percorsi di movimento", "Common.define.effectData.textPathCurve": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPathLine": "Linea", "Common.define.effectData.textPathScribble": "<PERSON><PERSON>", "Common.define.effectData.textPeanut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPeekIn": "<PERSON><PERSON><PERSON><PERSON> den<PERSON>", "Common.define.effectData.textPeekOut": "Sbirciare fuori", "Common.define.effectData.textPentagon": "Pentagono", "Common.define.effectData.textPinwheel": "Girandola", "Common.define.effectData.textPlus": "<PERSON><PERSON>", "Common.define.effectData.textPointStar": "<PERSON> a punta", "Common.define.effectData.textPointStar4": "Stella a 4 punte", "Common.define.effectData.textPointStar5": "Stella a 5 punte", "Common.define.effectData.textPointStar6": "Stella a 6 punte", "Common.define.effectData.textPointStar8": "Stella a 8 punte", "Common.define.effectData.textPulse": "Pulsazione", "Common.define.effectData.textRandomBars": "Barre casuali", "Common.define.effectData.textRight": "A destra", "Common.define.effectData.textRightDown": "<PERSON><PERSON> in basso", "Common.define.effectData.textRightTriangle": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRightUp": "<PERSON><PERSON> in alto", "Common.define.effectData.textRiseUp": "Sollevare", "Common.define.effectData.textSCurve1": "Curva S 1", "Common.define.effectData.textSCurve2": "Curva S 2", "Common.define.effectData.textShape": "Forma", "Common.define.effectData.textShapes": "Forme", "Common.define.effectData.textShimmer": "<PERSON><PERSON>", "Common.define.effectData.textShrinkTurn": "Restringere e girare", "Common.define.effectData.textSineWave": "Onda sinusoidale", "Common.define.effectData.textSinkDown": "Affondare", "Common.define.effectData.textSlideCenter": "Centro della diapositiva", "Common.define.effectData.textSpecial": "Speciali", "Common.define.effectData.textSpin": "G<PERSON><PERSON>", "Common.define.effectData.textSpinner": "Centrifuga", "Common.define.effectData.textSpiralIn": "Spirale verso dentro", "Common.define.effectData.textSpiralLeft": "Spirale verso sinistra", "Common.define.effectData.textSpiralOut": "Spirale verso fuori", "Common.define.effectData.textSpiralRight": "Spirale verso destra", "Common.define.effectData.textSplit": "Dividere", "Common.define.effectData.textSpoke1": "1 settore", "Common.define.effectData.textSpoke2": "2 settori", "Common.define.effectData.textSpoke3": "3 settori", "Common.define.effectData.textSpoke4": "4 settori", "Common.define.effectData.textSpoke8": "8 settori", "Common.define.effectData.textSpring": "<PERSON><PERSON>", "Common.define.effectData.textSquare": "Quadrato", "Common.define.effectData.textStairsDown": "Scale verso il basso", "Common.define.effectData.textStretch": "Estendere", "Common.define.effectData.textStrips": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSubtle": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSwivel": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSwoosh": "Swoosh", "Common.define.effectData.textTeardrop": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textTeeter": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textToBottom": "Verso il basso", "Common.define.effectData.textToBottomLeft": "Verso il basso a sinistra", "Common.define.effectData.textToBottomRight": "Verso il basso a destra", "Common.define.effectData.textToLeft": "A sinistra", "Common.define.effectData.textToRight": "A destra", "Common.define.effectData.textToTop": "Verso l'alto", "Common.define.effectData.textToTopLeft": "Verso l'alto a sinistra", "Common.define.effectData.textToTopRight": "Verso l'alto a destra", "Common.define.effectData.textTransparency": "Trasparenza", "Common.define.effectData.textTrapezoid": "Trapezio", "Common.define.effectData.textTurnDown": "G<PERSON><PERSON> verso il basso", "Common.define.effectData.textTurnDownRight": "Girare verso il basso e destra", "Common.define.effectData.textTurns": "Giri", "Common.define.effectData.textTurnUp": "Girare verso l'alto", "Common.define.effectData.textTurnUpRight": "Girare verso l'alto a destra", "Common.define.effectData.textUnderline": "Sottolineare", "Common.define.effectData.textUp": "Verso l'alto", "Common.define.effectData.textVertical": "Verticale", "Common.define.effectData.textVerticalFigure": "Figura 8 verticale", "Common.define.effectData.textVerticalIn": "Verticale dentro", "Common.define.effectData.textVerticalOut": "Verticale fuori", "Common.define.effectData.textWave": "On<PERSON>", "Common.define.effectData.textWedge": "Cuneo", "Common.define.effectData.textWheel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWhip": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWipe": "Apparizione", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textBalance": "Equilibri", "Common.define.smartArt.textEquation": "Equazione", "Common.define.smartArt.textFunnel": "Imbuto", "Common.define.smartArt.textOther": "Altro", "Common.define.smartArt.textPicture": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.textMoreButton": "più", "Common.Translation.warnFileLocked": "Il file è in fase di modifica in un'altra applicazione. Puoi continuare a modificarlo e salvarlo come copia.", "Common.Translation.warnFileLockedBtnEdit": "Crea una copia", "Common.Translation.warnFileLockedBtnView": "‎Aperto per la visualizzazione‎", "Common.UI.ButtonColored.textAutoColor": "Automatico", "Common.UI.ButtonColored.textNewColor": "Aggiungi Colore personalizzato", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON> stile", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Attuale", "Common.UI.ExtendedColorDialog.textHexErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nuovo", "Common.UI.ExtendedColorDialog.textRGBErr": "Il valore inserito non è corretto.<br>Inserisci un valore numerico tra 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Nessun colore", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Nascondere la password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostra password", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Chiudi la ricerca", "Common.UI.SearchBar.tipNextResult": "Successivo", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Aprire le impostazioni avanzate", "Common.UI.SearchBar.tipPreviousResult": "Precedente", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON> ris<PERSON>ati", "Common.UI.SearchDialog.textMatchCase": "Sensibile al maiuscolo/minuscolo", "Common.UI.SearchDialog.textReplaceDef": "Inserisci testo sostitutivo", "Common.UI.SearchDialog.textSearchStart": "Inserisci il tuo testo qui", "Common.UI.SearchDialog.textTitle": "Trova e sostituisci", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Solo parole intere", "Common.UI.SearchDialog.txtBtnHideReplace": "Nascondi Sostituzione", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Sostit<PERSON><PERSON><PERSON> tutto", "Common.UI.SynchronizeTip.textDontShow": "Non mostrare più questo messaggio", "Common.UI.SynchronizeTip.textSynchronize": "Il documento è stato modificato da un altro utente.<br><PERSON><PERSON><PERSON> per salvare le modifiche e ricaricare gli aggiornamenti.", "Common.UI.ThemeColorPalette.textRecentColors": "Colori recenti", "Common.UI.ThemeColorPalette.textStandartColors": "Colori standard", "Common.UI.ThemeColorPalette.textThemeColors": "Colori del tema", "Common.UI.Themes.txtThemeClassicLight": "<PERSON> leggera", "Common.UI.Themes.txtThemeContrastDark": "Contrasto scuro", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Chiaro", "Common.UI.Themes.txtThemeSystem": "Uguale al sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON>", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Conferma", "Common.UI.Window.textDontShow": "Non mostrare più questo messaggio", "Common.UI.Window.textError": "Errore", "Common.UI.Window.textInformation": "Informazione", "Common.UI.Window.textWarning": "Avviso", "Common.UI.Window.yesButtonText": "Sì", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "indirizzo: ", "Common.Views.About.txtLicensee": "LICENZIATARIO", "Common.Views.About.txtLicensor": "CONCEDENTE", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Con tecnologia", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versione ", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Applica durante la digitazione", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Correzione automatica di testo", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formattazione automatica durante la scrittura", "Common.Views.AutoCorrectDialog.textBulleted": "Elenchi puntati automatici", "Common.Views.AutoCorrectDialog.textBy": "Di", "Common.Views.AutoCorrectDialog.textDelete": "Elimina", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Aggiungere punto con doppio spazio", "Common.Views.AutoCorrectDialog.textFLCells": "Rendere maiuscola la prima lettera delle celle della tabella", "Common.Views.AutoCorrectDialog.textFLSentence": "‎In maiuscolo la prima lettera delle frasi‎", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e percorsi di rete con i collegamenti ipertestuali", "Common.Views.AutoCorrectDialog.textHyphens": "‎<PERSON><PERSON><PERSON><PERSON> (--) con t<PERSON><PERSON> (-)‎", "Common.Views.AutoCorrectDialog.textMathCorrect": "Correzione automatica matematica", "Common.Views.AutoCorrectDialog.textNumbered": "Elenchi numerati automatici", "Common.Views.AutoCorrectDialog.textQuotes": "‎\"Citazioni dritte\" con \"citazioni intelligenti\"‎", "Common.Views.AutoCorrectDialog.textRecognized": "Funzioni riconosciute", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Le seguenti espressioni sono espressioni matematiche riconosciute. Non verranno automaticamente scritte in corsivo.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Sostituisci testo con quello digitato", "Common.Views.AutoCorrectDialog.textReplaceType": "Sostituisci testo con quello digitato", "Common.Views.AutoCorrectDialog.textReset": "Reimposta", "Common.Views.AutoCorrectDialog.textResetAll": "Ripristina valori predefiniti", "Common.Views.AutoCorrectDialog.textRestore": "R<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Correzione automatica", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Le funzioni riconosciute possono contenere soltanto lettere da A a Z, maiuscole o minuscole.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "‎Qualsiasi espressione aggiunta verrà rimossa e quelle rimosse verranno ripristinate. Vuoi continuare?‎", "Common.Views.AutoCorrectDialog.warnReplace": "La voce di correzione automatica per %1 esiste già. Vuoi sostituirla?", "Common.Views.AutoCorrectDialog.warnReset": "Qualsiasi auto correzione automatica aggiunta verrà rimossa e quelle modificate verranno ripristinate ai valori originali. Vuoi continuare?", "Common.Views.AutoCorrectDialog.warnRestore": "La voce di correzione automatica per %1 verrà reimpostata al valore originale. Vuoi continuare?", "Common.Views.Chat.textSend": "Invia", "Common.Views.Comments.mniAuthorAsc": "Autore dalla A alla Z", "Common.Views.Comments.mniAuthorDesc": "Autore dalla Z alla A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> vecchio", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON> recente", "Common.Views.Comments.mniFilterGroups": "Filtra per gruppo", "Common.Views.Comments.mniPositionAsc": "Dall'alto", "Common.Views.Comments.mniPositionDesc": "<PERSON> basso", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "Aggiungi commento", "Common.Views.Comments.textAddCommentToDoc": "Aggiungi commento al documento", "Common.Views.Comments.textAddReply": "Aggiungi risposta", "Common.Views.Comments.textAll": "<PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Ospite", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> commenti", "Common.Views.Comments.textComments": "Commenti", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Inserisci commento qui", "Common.Views.Comments.textHintAddComment": "Aggiungi commento", "Common.Views.Comments.textOpenAgain": "Apri di nuovo", "Common.Views.Comments.textReply": "Rispondi", "Common.Views.Comments.textResolve": "<PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Ordinare commenti", "Common.Views.Comments.textViewResolved": "Non sei autorizzato a riaprire il commento", "Common.Views.Comments.txtEmpty": "Non ci sono commenti nel documento.", "Common.Views.CopyWarningDialog.textDontShow": "Non mostrare più questo messaggio", "Common.Views.CopyWarningDialog.textMsg": "Le azioni di copia, taglia e incolla utilizzando i pulsanti della barra degli strumenti dell'editor e le azioni del menu di scelta rapida verranno eseguite solo all'interno di questa scheda dell'editor. <br><br>Per copiare o incollare in o da applicazioni esterne alla scheda dell'editor, utilizzare le seguenti combinazioni di tasti:", "Common.Views.CopyWarningDialog.textTitle": "Funzioni copia/taglia/incolla", "Common.Views.CopyWarningDialog.textToCopy": "per copiare", "Common.Views.CopyWarningDialog.textToCut": "per tagliare", "Common.Views.CopyWarningDialog.textToPaste": "per incollare", "Common.Views.DocumentAccessDialog.textLoading": "Caricamento in corso...", "Common.Views.DocumentAccessDialog.textTitle": "Impostazioni di condivisione", "Common.Views.ExternalDiagramEditor.textTitle": "Editor di grafici", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Salva ed esci", "Common.Views.ExternalOleEditor.textTitle": "Editor di fogli di calcolo", "Common.Views.Header.labelCoUsersDescr": "Utenti che stanno modificando il file:", "Common.Views.Header.textAddFavorite": "Segna come preferito", "Common.Views.Header.textAdvSettings": "Impostazioni avanzate", "Common.Views.Header.textBack": "Apri percorso file", "Common.Views.Header.textCompactView": "Nascondi barra degli strumenti", "Common.Views.Header.textHideLines": "Nascondi righelli", "Common.Views.Header.textHideNotes": "‎Nascondi note‎", "Common.Views.Header.textHideStatusBar": "Nascondi barra di stato", "Common.Views.Header.textRemoveFavorite": "Rimuovi dai preferiti", "Common.Views.Header.textSaveBegin": "Salvataggio in corso...", "Common.Views.Header.textSaveChanged": "Modificato", "Common.Views.Header.textSaveEnd": "Tutte le modifiche sono state salvate", "Common.Views.Header.textSaveExpander": "Tutte le modifiche sono state salvate", "Common.Views.Header.textShare": "Condi<PERSON><PERSON>", "Common.Views.Header.textZoom": "Ingrandimento", "Common.Views.Header.tipAccessRights": "Gestisci i diritti di accesso al documento", "Common.Views.Header.tipDownload": "Scarica file", "Common.Views.Header.tipGoEdit": "Modifica il file corrente", "Common.Views.Header.tipPrint": "Stampa file", "Common.Views.Header.tipRedo": "R<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON>", "Common.Views.Header.tipSearch": "Cerca", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Sgancia in una finestra separata", "Common.Views.Header.tipUsers": "Visualizzare gli utenti", "Common.Views.Header.tipViewSettings": "Mostra impostazioni", "Common.Views.Header.tipViewUsers": "Mostra gli utenti e gestisci i diritti di accesso al documento", "Common.Views.Header.txtAccessRights": "Modifica diritti di accesso", "Common.Views.Header.txtRename": "Rinomina", "Common.Views.History.textCloseHistory": "Chiudere cronologia", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Nascondi le modifiche dettagliate", "Common.Views.History.textRestore": "R<PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Espandi", "Common.Views.History.textShowAll": "Mostra modifiche dettagliate", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Incolla URL immagine:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Campo obbligatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Il formato URL richiesto è \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Specifica il numero di righe e colonne valido.", "Common.Views.InsertTableDialog.txtColumns": "Numero di colonne", "Common.Views.InsertTableDialog.txtMaxText": "Il valore massimo di questo campo è {0}.", "Common.Views.InsertTableDialog.txtMinText": "Il valore minimo di questo campo è {0}.", "Common.Views.InsertTableDialog.txtRows": "Numero di righe", "Common.Views.InsertTableDialog.txtTitle": "Dimensioni tabella", "Common.Views.InsertTableDialog.txtTitleSplit": "Dividi cella", "Common.Views.LanguageDialog.labelSelect": "Seleziona la lingua del documento", "Common.Views.ListSettingsDialog.textBulleted": "Elenco puntato", "Common.Views.ListSettingsDialog.textFromFile": "Da file", "Common.Views.ListSettingsDialog.textFromStorage": "Da spazio di archiviazione", "Common.Views.ListSettingsDialog.textFromUrl": "Da URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerato", "Common.Views.ListSettingsDialog.tipChange": "Modifica elenco puntato", "Common.Views.ListSettingsDialog.txtBullet": "Elenco puntato", "Common.Views.ListSettingsDialog.txtColor": "Colore", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "Importa", "Common.Views.ListSettingsDialog.txtNewBullet": "Nuovo elenco puntato", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% del testo", "Common.Views.ListSettingsDialog.txtSize": "Dimensione", "Common.Views.ListSettingsDialog.txtStart": "Inizia da", "Common.Views.ListSettingsDialog.txtSymbol": "Simbolo", "Common.Views.ListSettingsDialog.txtTitle": "Impostazioni elenco", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Codifica", "Common.Views.OpenDialog.txtIncorrectPwd": "Password errata", "Common.Views.OpenDialog.txtOpenFile": "Immettere la password per aprire il file", "Common.Views.OpenDialog.txtPassword": "Password", "Common.Views.OpenDialog.txtProtected": "Una volta inserita la password e aperto il file, verrà ripristinata la password corrente sul file.", "Common.Views.OpenDialog.txtTitle": "Seleziona %1 opzioni", "Common.Views.OpenDialog.txtTitleProtected": "File protetto", "Common.Views.PasswordDialog.txtDescription": "Impostare una password per proteggere questo documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "La password di conferma non corrisponde", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Ripeti password", "Common.Views.PasswordDialog.txtTitle": "Imposta password", "Common.Views.PasswordDialog.txtWarning": "Importante: una volta persa o dimenticata, la password non potrà più essere recuperata. Conservalo in un luogo sicuro.", "Common.Views.PluginDlg.textLoading": "Caricamento", "Common.Views.Plugins.groupCaption": "Plugin", "Common.Views.Plugins.strPlugins": "Plugin", "Common.Views.Plugins.textClosePanel": "Chiudere plugin", "Common.Views.Plugins.textLoading": "Caricamento", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "Termina", "Common.Views.Protection.hintAddPwd": "Crittografa con password", "Common.Views.Protection.hintDelPwd": "Elimina password", "Common.Views.Protection.hintPwd": "Modifica o rimuovi password", "Common.Views.Protection.hintSignature": "Aggiungi firma digitale o riga di firma", "Common.Views.Protection.txtAddPwd": "Aggiungi password", "Common.Views.Protection.txtChangePwd": "Modifica password", "Common.Views.Protection.txtDeletePwd": "Elimina password", "Common.Views.Protection.txtEncrypt": "Crittografare", "Common.Views.Protection.txtInvisibleSignature": "Aggiungi firma digitale", "Common.Views.Protection.txtSignature": "Firma", "Common.Views.Protection.txtSignatureLine": "Aggiungi riga di firma", "Common.Views.RenameDialog.textName": "Nome del file", "Common.Views.RenameDialog.txtInvalidName": "Il nome del file non può contenere nessuno dei seguenti caratteri:", "Common.Views.ReviewChanges.hintNext": "Alla modifica successiva", "Common.Views.ReviewChanges.hintPrev": "Alla modifica precedente", "Common.Views.ReviewChanges.strFast": "Rapido", "Common.Views.ReviewChanges.strFastDesc": "Co-editing in teampo reale. <PERSON>tte le modifiche vengono salvate automaticamente.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "Usa il pulsante 'Salva' per sincronizzare le tue modifiche con quelle effettuate da altri.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accetta la modifica corrente", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON>mposta modalità co-editing", "Common.Views.ReviewChanges.tipCommentRem": "Rimuovi i commenti", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Rimuovi i commenti correnti", "Common.Views.ReviewChanges.tipCommentResolve": "‎Risolvere i commenti‎", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "‎Risolvere i commenti correnti‎", "Common.Views.ReviewChanges.tipHistory": "Mostra Cronologia versioni", "Common.Views.ReviewChanges.tipRejectCurrent": "Annulla la modifica attuale", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON> camb<PERSON>i", "Common.Views.ReviewChanges.tipReviewView": "Selezionare il modo in cui si desidera visualizzare le modifiche", "Common.Views.ReviewChanges.tipSetDocLang": "Imposta la lingua del documento", "Common.Views.ReviewChanges.tipSetSpelling": "Controllo ortografia", "Common.Views.ReviewChanges.tipSharing": "Gestisci i diritti di accesso al documento", "Common.Views.ReviewChanges.txtAccept": "Accetta", "Common.Views.ReviewChanges.txtAcceptAll": "Accetta tutte le modifiche", "Common.Views.ReviewChanges.txtAcceptChanges": "Accetta modifiche", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accetta la modifica corrente", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modalità di co-editing", "Common.Views.ReviewChanges.txtCommentRemAll": "Rimuovi tutti i commenti", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Rimuovi i commenti correnti", "Common.Views.ReviewChanges.txtCommentRemMy": "Rimuovi i miei commenti", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Rimuovi i miei commenti attuali", "Common.Views.ReviewChanges.txtCommentRemove": "Elimina", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "‎Risolvere tutti i commenti‎", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "‎Risolvere i commenti correnti‎", "Common.Views.ReviewChanges.txtCommentResolveMy": "‎Risolvere i commenti‎", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "‎Risolvere i commenti correnti‎", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> le modifiche accettate (anteprima)", "Common.Views.ReviewChanges.txtFinalCap": "Finale", "Common.Views.ReviewChanges.txtHistory": "Cronologia delle versioni", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> le modifiche (revisione)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcatura", "Common.Views.ReviewChanges.txtNext": "Successivo", "Common.Views.ReviewChanges.txtOriginal": "<PERSON>tti le modifiche rifiutate (Anteprima)", "Common.Views.ReviewChanges.txtOriginalCap": "Originale", "Common.Views.ReviewChanges.txtPrev": "Precedente", "Common.Views.ReviewChanges.txtReject": "Re<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON> tutte le modifiche", "Common.Views.ReviewChanges.txtRejectChanges": "Rifiuta modifiche", "Common.Views.ReviewChanges.txtRejectCurrent": "Annulla la modifica attuale", "Common.Views.ReviewChanges.txtSharing": "Condivisione", "Common.Views.ReviewChanges.txtSpelling": "Controllo ortografia", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON> camb<PERSON>i", "Common.Views.ReviewChanges.txtView": "Modalità visualizzazione", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "Aggiungi risposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+mention fornirà l'accesso al documento e invierà un'e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+mention avviserà l'utente via e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Apri di nuovo", "Common.Views.ReviewPopover.textReply": "Rispondi", "Common.Views.ReviewPopover.textResolve": "Risolvere", "Common.Views.ReviewPopover.textViewResolved": "Non sei autorizzato a riaprire il commento", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminare", "Common.Views.ReviewPopover.txtEditTip": "Modificare", "Common.Views.SaveAsDlg.textLoading": "Caricamento", "Common.Views.SaveAsDlg.textTitle": "Cartella di salvataggio", "Common.Views.SearchPanel.textCaseSensitive": "Sensibile al maiuscolo/minuscolo", "Common.Views.SearchPanel.textCloseSearch": "Chiudi la ricerca", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Trova e sostituisci", "Common.Views.SearchPanel.textMatchUsingRegExp": "Corrispondenza usando espressioni regolari", "Common.Views.SearchPanel.textNoMatches": "<PERSON>ess<PERSON> corrispondenza", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON>essun risultato di ricerca", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Sostit<PERSON><PERSON><PERSON> tutto", "Common.Views.SearchPanel.textReplaceWith": "Sostituire con", "Common.Views.SearchPanel.textSearchHasStopped": "La ricerca è stata interrotta", "Common.Views.SearchPanel.textSearchResults": "Risultati della ricerca: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Ci sono troppi risultati per essere mostrati qui", "Common.Views.SearchPanel.textWholeWords": "Solo parole intere", "Common.Views.SearchPanel.tipNextResult": "Successivo", "Common.Views.SearchPanel.tipPreviousResult": "Precedente", "Common.Views.SelectFileDlg.textLoading": "Caricamento", "Common.Views.SelectFileDlg.textTitle": "Seleziona Sorgente Dati", "Common.Views.SignDialog.textBold": "Grassetto", "Common.Views.SignDialog.textCertificate": "Certificato", "Common.Views.SignDialog.textChange": "Cambia", "Common.Views.SignDialog.textInputName": "Inserisci nome firmatario", "Common.Views.SignDialog.textItalic": "Corsivo", "Common.Views.SignDialog.textNameError": "Il nome firmatario non può essere vuoto.", "Common.Views.SignDialog.textPurpose": "Motivo della firma del documento", "Common.Views.SignDialog.textSelect": "Seleziona", "Common.Views.SignDialog.textSelectImage": "Seleziona Immagine", "Common.Views.SignDialog.textSignature": "La firma appare come", "Common.Views.SignDialog.textTitle": "Firma Documento", "Common.Views.SignDialog.textUseImage": "oppure clicca 'Scegli immagine' per utilizzare un'immagine come firma", "Common.Views.SignDialog.textValid": "Valido dal %1 al %2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON> carattere", "Common.Views.SignDialog.tipFontSize": "Dimensione carattere", "Common.Views.SignSettingsDialog.textAllowComment": "Consenti al firmatario di aggiungere commenti nella finestra di dialogo della firma", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON> del Firmatario", "Common.Views.SignSettingsDialog.textInstructions": "Istruzioni per i Firmatari", "Common.Views.SignSettingsDialog.textShowDate": "Mostra la data nella riga di Firma", "Common.Views.SignSettingsDialog.textTitle": "Impostazioni firma", "Common.Views.SignSettingsDialog.txtEmpty": "Campo obbligatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "valore Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Segno di copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON> virgolette alte doppie", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON>i virgolette alte doppie", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON>si oriz<PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Lineetta emme", "Common.Views.SymbolTableDialog.textEmSpace": "Spazio emme", "Common.Views.SymbolTableDialog.textEnDash": "Lineetta enne", "Common.Views.SymbolTableDialog.textEnSpace": "Spazio enne", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Trattino senza interruzioni", "Common.Views.SymbolTableDialog.textNBSpace": "Spazio senza interruzioni", "Common.Views.SymbolTableDialog.textPilcrow": "Piede di mosca", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 spazio emme", "Common.Views.SymbolTableDialog.textRange": "Intervallo", "Common.Views.SymbolTableDialog.textRecent": "Simboli usati di recente", "Common.Views.SymbolTableDialog.textRegistered": "Firma registarta", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON> virgolette alte singole", "Common.Views.SymbolTableDialog.textSection": "Sezione firma", "Common.Views.SymbolTableDialog.textShortcut": "Tasto di scelta rapida", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON> morbido", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON>i virgolette alte singole", "Common.Views.SymbolTableDialog.textSpecial": "Caratteri speciali", "Common.Views.SymbolTableDialog.textSymbols": "Simboli", "Common.Views.SymbolTableDialog.textTitle": "Simbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Simbolo del marchio", "Common.Views.UserNameDialog.textDontShow": "‎Non chiedere di nuovo‎", "Common.Views.UserNameDialog.textLabel": "Etichetta:", "Common.Views.UserNameDialog.textLabelError": "L'etichetta non deve essere vuota.", "PE.Controllers.LeftMenu.leavePageText": "Tutte le modifiche non salvate nel documento verranno perse.<br> <PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" e poi \"Salva\" per salvarle. Clicca \"OK\" per annullare tutte le modifiche non salvate.", "PE.Controllers.LeftMenu.newDocumentTitle": "Presentazione senza nome", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Avviso", "PE.Controllers.LeftMenu.requestEditRightsText": "Richiesta di autorizzazione di modifica...", "PE.Controllers.LeftMenu.textLoadHistory": "Caricamento Cronologia in corso....", "PE.Controllers.LeftMenu.textNoTextFound": "I dati da cercare non sono stati trovati. Modifica i parametri di ricerca.", "PE.Controllers.LeftMenu.textReplaceSkipped": "La sostituzione è stata effettuata. {0} casi sono stati saltati.", "PE.Controllers.LeftMenu.textReplaceSuccess": "La ricerca è stata effettuata. casi sostituiti: {0}", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON>za titolo", "PE.Controllers.Main.applyChangesTextText": "Caricamento dei dati in corso...", "PE.Controllers.Main.applyChangesTitleText": "Caricamento dei dati", "PE.Controllers.Main.convertationTimeoutText": "È stato superato il tempo limite della conversione.", "PE.Controllers.Main.criticalErrorExtText": "Clicca su \"OK\" per ritornare all'elenco dei documenti.", "PE.Controllers.Main.criticalErrorTitle": "Errore", "PE.Controllers.Main.downloadErrorText": "Scaricamento fallito.", "PE.Controllers.Main.downloadTextText": "Scaricamento della presentazione in corso...", "PE.Controllers.Main.downloadTitleText": "Scaricamento della presentazione", "PE.Controllers.Main.errorAccessDeny": "Stai tentando di eseguire un'azione per la quale non disponi di permessi sufficienti.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "PE.Controllers.Main.errorBadImageUrl": "URL dell'immagine errato", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Connessione al server persa. Il documento non può essere modificato in questo momento.", "PE.Controllers.Main.errorComboSeries": "Per creare un grafico a combinazione, seleziona almeno due serie di dati.", "PE.Controllers.Main.errorConnectToServer": "Il documento non può essere salvato. Controllare le impostazioni di rete o contatta l'Amministratore.<br><PERSON>uan<PERSON> clic<PERSON> 'OK' Ti verrà richiesto di scaricare il documento.", "PE.Controllers.Main.errorDatabaseConnection": "Errore esterno. <br> Errore di connessione al database. Si prega di contattare l'assistenza nel caso in cui l'errore persista.", "PE.Controllers.Main.errorDataEncrypted": "Le modifiche crittografate sono state ricevute, non possono essere decifrate.", "PE.Controllers.Main.errorDataRange": "Intervallo di dati non corretto.", "PE.Controllers.Main.errorDefaultMessage": "Codice errore: %1", "PE.Controllers.Main.errorDirectUrl": "Si prega di verificare il link al documento. <br>Quest<PERSON> collegamento deve essere un collegamento diretto al file da scaricare.", "PE.Controllers.Main.errorEditingDownloadas": "Si è verificato un errore durante il lavoro con il documento.<br>Utilizza l'opzione 'Scaricare come' per salvare la copia di backup del file sul disco rigido del computer.", "PE.Controllers.Main.errorEditingSaveas": "Si è verificato un errore durante il lavoro con il documento.<br>Utilizza l'opzione 'Salvare come ...' per salvare la copia di backup del file sul disco rigido del computer.", "PE.Controllers.Main.errorEmailClient": "Non è stato trovato nessun client di posta elettronica.", "PE.Controllers.Main.errorFilePassProtect": "Il file è protetto da una password. Impossibile aprirlo.", "PE.Controllers.Main.errorFileSizeExceed": "La dimensione del file supera la limitazione impostata per il tuo server.<br>Per i dettagli, contatta l'amministratore del Document server.", "PE.Controllers.Main.errorForceSave": "Si è verificato un errore durante il salvataggio del file. Utilizza l'opzione 'Scaricare come' per salvare il file sul disco rigido del computer o riprova più tardi.", "PE.Controllers.Main.errorKeyEncrypt": "Descrittore di chiave scon<PERSON>uto", "PE.Controllers.Main.errorKeyExpire": "Descrittore di chiave scaduto", "PE.Controllers.Main.errorLoadingFont": "I caratteri non sono caricati.<br>Si prega di contattare il tuo amministratore di Document Server.", "PE.Controllers.Main.errorProcessSaveResult": "Salvataggio non riuscito", "PE.Controllers.Main.errorServerVersion": "La versione dell'editor è stata aggiornata. La pagina verrà ricaricata per applicare le modifiche.", "PE.Controllers.Main.errorSessionAbsolute": "La sessione di modifica del documento è scaduta. Si prega di ricaricare la pagina.", "PE.Controllers.Main.errorSessionIdle": "È passato troppo tempo dall'ultima modifica apportata al documento. Si prega di ricaricare la pagina.", "PE.Controllers.Main.errorSessionToken": "La connessione al server è stata interrotta. Si prega di ricaricare la pagina.", "PE.Controllers.Main.errorSetPassword": "Impossibile impostare la password.", "PE.Controllers.Main.errorStockChart": "<PERSON>ighe ordinate in modo errato. Per creare un grafico azionario posizionare i dati sul foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "PE.Controllers.Main.errorToken": "Il token di sicurezza del documento non è stato creato correttamente.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "PE.Controllers.Main.errorTokenExpire": "Il token di sicurezza del documento è scaduto.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "PE.Controllers.Main.errorUpdateVersion": "La versione del file è stata modificata. La pagina verrà ricaricata.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "La connessione Internet è stata ripristinata e la versione del file è stata modificata.<br>Prima di poter continuare a lavorare, devi scaricare il file o copiarne il contenuto per assicurarti che nulla vada perso, quindi ricarica questa pagina.", "PE.Controllers.Main.errorUserDrop": "Impossibile accedere al file in questo momento.", "PE.Controllers.Main.errorUsersExceed": "È stato superato il numero di utenti consentito dal piano tariffario", "PE.Controllers.Main.errorViewerDisconnect": "La connessione è stata persa. È ancora possibile visualizzare il documento, <br> ma non sarà possibile scaricarlo o stamparlo fino a quando la connessione non sarà ripristinata e la pagina sarà ricaricata.", "PE.Controllers.Main.leavePageText": "Ci sono delle modifiche non salvate in questa presentazione. Clicca su \"Rimani in questa pagina\", poi su \"Salva\" per salvarle. Clicca su \"Esci da questa pagina\" per scartare tutte le modifiche non salvate.", "PE.Controllers.Main.leavePageTextOnClose": "‎Tutte le modifiche non salvate in questa presentazione verranno perse.‎<br> <PERSON>lick \"<PERSON><PERSON><PERSON>\" poi \"Salva\" per salvarle. Fare click su \"OK\" per eliminare tutte le modifiche non salvate.‎", "PE.Controllers.Main.loadFontsTextText": "Caricamento dei dati in corso...", "PE.Controllers.Main.loadFontsTitleText": "Caricamento dei dati", "PE.Controllers.Main.loadFontTextText": "Caricamento dei dati in corso...", "PE.Controllers.Main.loadFontTitleText": "Caricamento dei dati", "PE.Controllers.Main.loadImagesTextText": "Caricamento delle immagini in corso...", "PE.Controllers.Main.loadImagesTitleText": "Caricamento delle immagini", "PE.Controllers.Main.loadImageTextText": "Caricamento dell'immagine in corso...", "PE.Controllers.Main.loadImageTitleText": "Caricamento dell'immagine", "PE.Controllers.Main.loadingDocumentTextText": "Caricamento della presentazione in corso...", "PE.Controllers.Main.loadingDocumentTitleText": "Caricamento della presentazione", "PE.Controllers.Main.loadThemeTextText": "Caricamento del tema in corso...", "PE.Controllers.Main.loadThemeTitleText": "Caricamento del tema", "PE.Controllers.Main.notcriticalErrorTitle": "Avviso", "PE.Controllers.Main.openErrorText": "Si è verificato un errore durante l'apertura del file", "PE.Controllers.Main.openTextText": "Apertura della presentazione in corso...", "PE.Controllers.Main.openTitleText": "Apertura della presentazione", "PE.Controllers.Main.printTextText": "Stampa della presentazione in corso...", "PE.Controllers.Main.printTitleText": "Stampa della presentazione", "PE.Controllers.Main.reloadButtonText": "Ricarica pagina", "PE.Controllers.Main.requestEditFailedMessageText": "Qualcuno sta modificando questa presentazione. Si prega di provare più tardi.", "PE.Controllers.Main.requestEditFailedTitleText": "Accesso negato", "PE.Controllers.Main.saveErrorText": "Si è verificato un errore durante il salvataggio del file", "PE.Controllers.Main.saveErrorTextDesktop": "Questo file non può essere salvato o creato. <br>I possibili motivi sono: <br>1. Il file è di sola lettura. <br>2. Il file è in fase di modifica da parte di altri utenti. <br>3. Il disco è pieno oppure è danneggiato.", "PE.Controllers.Main.saveTextText": "Salvataggio della presentazione in corso...", "PE.Controllers.Main.saveTitleText": "Salvataggio della presentazione", "PE.Controllers.Main.scriptLoadError": "La connessione è troppo lenta, alcuni componenti non possono essere caricati. Si prega di ricaricare la pagina.", "PE.Controllers.Main.splitDividerErrorText": "Il numero di righe deve essere un divisore di %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Il numero di colonne deve essere inferiore a %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Il numero di righe deve essere inferiore a %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "Applicare a tutte le equazioni", "PE.Controllers.Main.textBuyNow": "Visita il sito web", "PE.Controllers.Main.textChangesSaved": "Tutte le modifiche sono state salvate", "PE.Controllers.Main.textClose": "<PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Fai clic per chiudere il consiglio", "PE.Controllers.Main.textContactUs": "Contatta il reparto vendite.", "PE.Controllers.Main.textContinue": "Continua", "PE.Controllers.Main.textConvertEquation": "Questa equazione è stata creata in una vecchia versione dell'editor di equazioni che non è più supportata. Per modific<PERSON>, devi convertire l'equazione nel formato ML di Office Math.<br>Convertire ora?", "PE.Controllers.Main.textCustomLoader": "Si prega di notare che, in base ai termini della licenza, non si ha il diritto di modificare il caricatore.<br>Si prega di contattare il nostro reparto vendite per ottenere un preventivo.", "PE.Controllers.Main.textDisconnect": "Connessione persa", "PE.Controllers.Main.textGuest": "Ospite", "PE.Controllers.Main.textHasMacros": "Il file contiene macro automatiche. <br> Vuoi eseguire le macro?", "PE.Controllers.Main.textLearnMore": "Per saperne di più", "PE.Controllers.Main.textLoadingDocument": "Caricamento della presentazione", "PE.Controllers.Main.textLongName": "Si prega di immettere un nome che contenga meno di 128 caratteri.", "PE.Controllers.Main.textNoLicenseTitle": "Limite di licenza raggiunto", "PE.Controllers.Main.textPaidFeature": "Caratteristica a pagamento", "PE.Controllers.Main.textReconnect": "Connessione ripristinata", "PE.Controllers.Main.textRemember": "Ricorda la mia scelta", "PE.Controllers.Main.textRememberMacros": "Ricordare la mia scelta per tutte le macro", "PE.Controllers.Main.textRenameError": "Il nome utente non può essere vuoto.", "PE.Controllers.Main.textRenameLabel": "Immettere un nome da utilizzare per la collaborazione", "PE.Controllers.Main.textRequestMacros": "Una macro effettua una richiesta all'URL. Vuoi consentire la richiesta al %1?", "PE.Controllers.Main.textShape": "Forma", "PE.Controllers.Main.textStrict": "Modalità Rigorosa", "PE.Controllers.Main.textText": "<PERSON><PERSON>", "PE.Controllers.Main.textTryUndoRedo": "Le funzioni Annulla/Ripristina sono disabilitate per la Modalità di Co-editing Veloce.<br><PERSON><PERSON><PERSON> il pulsante 'Modalità Rigorosa' per passare alla Modalità di Co-editing Rigorosa per poter modificare il file senza l'interferenza di altri utenti e inviare le modifiche solamente dopo averle salvate. Puoi passare da una modalità all'altra di co-editing utilizzando le Impostazioni avanzate dell'editor.", "PE.Controllers.Main.textTryUndoRedoWarn": "Le funzioni Annulla/Ripeti sono disattivate nella modalità rapida in modifica collaborativa.", "PE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.titleLicenseExp": "La licenza è scaduta", "PE.Controllers.Main.titleServerVersion": "L'editor è stato aggiornato", "PE.Controllers.Main.txtAddFirstSlide": "Fare clic per aggiungere la prima diapositiva", "PE.Controllers.Main.txtAddNotes": "Fai clic per aggiungere note", "PE.Controllers.Main.txtArt": "Il tuo testo qui", "PE.Controllers.Main.txtBasicShapes": "Forme di base", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "Callout", "PE.Controllers.Main.txtCharts": "<PERSON><PERSON>", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Data e ora", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Titolo del grafico", "PE.Controllers.Main.txtEditingMode": "Impostazione modo di modifica...", "PE.Controllers.Main.txtErrorLoadHistory": "Caricamento della cronologia non riuscito", "PE.Controllers.Main.txtFiguredArrows": "Frecce figurate", "PE.Controllers.Main.txtFooter": "Piè di pagina", "PE.Controllers.Main.txtHeader": "Intestazione", "PE.Controllers.Main.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLines": "Linee", "PE.Controllers.Main.txtLoading": "Caricamento in corso...", "PE.Controllers.Main.txtMath": "Matematica", "PE.Controllers.Main.txtMedia": "Multimedia", "PE.Controllers.Main.txtNeedSynchronize": "Ci sono aggiornamenti disponibili", "PE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtPicture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtRectangles": "Rettangoli", "PE.Controllers.Main.txtSeries": "Serie", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Callout Linea con bordo e barra in risalto", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Callout Linea piegata con bordo e barra in risalto", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Callout Doppia linea piegata con barra e bordo in risalto", "PE.Controllers.Main.txtShape_accentCallout1": "Callout Linea con barra in risalto", "PE.Controllers.Main.txtShape_accentCallout2": "Callout Linea piegata con barra in risalto", "PE.Controllers.Main.txtShape_accentCallout3": "Callout Doppia linea piegata con barra in risalto", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Indietro o Pulsante Precedente", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Pulsante di Inizio", "PE.Controllers.Main.txtShape_actionButtonBlank": "Pulsan<PERSON> Vuoto", "PE.Controllers.Main.txtShape_actionButtonDocument": "Pulsante Documento", "PE.Controllers.Main.txtShape_actionButtonEnd": "Pulsante Fine", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Pulsante Avanti o Successivo", "PE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonHome": "Pulsante Home", "PE.Controllers.Main.txtShape_actionButtonInformation": "Pulsante Informazioni", "PE.Controllers.Main.txtShape_actionButtonMovie": "Pulsante Film", "PE.Controllers.Main.txtShape_actionButtonReturn": "Pulsante Invio", "PE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_arc": "Arco", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> piegata", "PE.Controllers.Main.txtShape_bentConnector5": "<PERSON><PERSON><PERSON> a gomito", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "<PERSON><PERSON><PERSON> freccia a gomito", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "<PERSON><PERSON><PERSON> a doppia freccia a gomito", "PE.Controllers.Main.txtShape_bentUpArrow": "Freccia piegata verso l'alto", "PE.Controllers.Main.txtShape_bevel": "Smussat<PERSON>", "PE.Controllers.Main.txtShape_blockArc": "<PERSON>o a tutto sesto", "PE.Controllers.Main.txtShape_borderCallout1": "Callout <PERSON>a", "PE.Controllers.Main.txtShape_borderCallout2": "Callout Linea piegata", "PE.Controllers.Main.txtShape_borderCallout3": "Callout Doppia linea piegata", "PE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON> parentesi graffa", "PE.Controllers.Main.txtShape_callout1": "Callout Linea senza bordo", "PE.Controllers.Main.txtShape_callout2": "Callout Linea piegata senza bordo ", "PE.Controllers.Main.txtShape_callout3": "Callout Doppia linea piegata senza bordo", "PE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_chevron": "freccia a Gallone", "PE.Controllers.Main.txtShape_chord": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON> circolare", "PE.Controllers.Main.txtShape_cloud": "Nuvola", "PE.Controllers.Main.txtShape_cloudCallout": "Callout <PERSON>", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON> curvo", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "<PERSON><PERSON><PERSON> a freccia curva", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON> a doppia freccia curva", "PE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON> curva in basso", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Freccia curva a sinistra", "PE.Controllers.Main.txtShape_curvedRightArrow": "Freccia curva a destra", "PE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON> curva in alto", "PE.Controllers.Main.txtShape_decagon": "Decagono", "PE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonale", "PE.Controllers.Main.txtShape_diamond": "Diamante", "PE.Controllers.Main.txtShape_dodecagon": "Dodecagono", "PE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON> onda", "PE.Controllers.Main.txtShape_downArrow": "Freccia in giù", "PE.Controllers.Main.txtShape_downArrowCallout": "Callout <PERSON> in basso", "PE.Controllers.Main.txtShape_ellipse": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_ellipseRibbon": "Nastro curvo in basso", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Nastro curvato in alto", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagramma di flusso: processo alternativo", "PE.Controllers.Main.txtShape_flowChartCollate": "Diagramma di flusso: Fascicolo", "PE.Controllers.Main.txtShape_flowChartConnector": "Diagramma di flusso: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDecision": "Diagramma di flusso: Decisione", "PE.Controllers.Main.txtShape_flowChartDelay": "Diagramma di flusso: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDisplay": "Diagramma di flusso: Visualizza", "PE.Controllers.Main.txtShape_flowChartDocument": "Diagramma di flusso: Documento", "PE.Controllers.Main.txtShape_flowChartExtract": "Diagramma di flusso: Estrai", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Diagramma di flusso: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagramma di flusso: Memoria interna", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagramma di flusso: Disco magnetico", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagramma di flusso: Memoria ad accesso diretto", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagramma di flusso: Memoria ad accesso sequenziale", "PE.Controllers.Main.txtShape_flowChartManualInput": "Diagramma di flusso: Input manuale", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Diagramma di flusso: Operazione manuale", "PE.Controllers.Main.txtShape_flowChartMerge": "Diagramma di flusso: Unione", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Diagramma di flusso: Multidocumento", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagramma di flusso: <PERSON><PERSON><PERSON> fuori pagina", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagramma di flusso: <PERSON><PERSON> sal<PERSON>i", "PE.Controllers.Main.txtShape_flowChartOr": "Diagramma di flusso: O", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagramma di flusso: Elaborazione predefinita", "PE.Controllers.Main.txtShape_flowChartPreparation": "Diagramma di flusso: Preparazione", "PE.Controllers.Main.txtShape_flowChartProcess": "Diagramma di flusso: Elaborazione", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagramma di flusso: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagramma di flusso: Nastro perforato", "PE.Controllers.Main.txtShape_flowChartSort": "Diagramma di flusso: Ordinamento", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagramma di flusso: Giunzione di somma", "PE.Controllers.Main.txtShape_flowChartTerminator": "Diagramma di flusso: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_foldedCorner": "angolo ripiegato", "PE.Controllers.Main.txtShape_frame": "Cornice", "PE.Controllers.Main.txtShape_halfFrame": "Mezza Cornice", "PE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_heptagon": "Ettagono", "PE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_homePlate": "Pentagono", "PE.Controllers.Main.txtShape_horizontalScroll": "Scorrimento orizzontale", "PE.Controllers.Main.txtShape_irregularSeal1": "Esplosione 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Esplosione 2", "PE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftArrowCallout": "Callout <PERSON><PERSON><PERSON> a sinistra", "PE.Controllers.Main.txtShape_leftBrace": "Parentesi graffa aperta", "PE.Controllers.Main.txtShape_leftBracket": "Parentesi quadra aperta", "PE.Controllers.Main.txtShape_leftRightArrow": "Freccia bidirezionale sinistra destra", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Callout <PERSON><PERSON><PERSON> bidirezionane sinistra destra", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Freccia tridirezionale sinistra destra alto", "PE.Controllers.Main.txtShape_leftUpArrow": "<PERSON><PERSON>cia in alto a sinistra", "PE.Controllers.Main.txtShape_lightningBolt": "Fulmine", "PE.Controllers.Main.txtShape_line": "Linea", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> doppia", "PE.Controllers.Main.txtShape_mathDivide": "Divisione", "PE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Moltiplicazione", "PE.Controllers.Main.txtShape_mathNotEqual": "Non uguale", "PE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_moon": "Luna", "PE.Controllers.Main.txtShape_noSmoking": "Simbolo \"No\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "<PERSON><PERSON>cia dentellata a destra ", "PE.Controllers.Main.txtShape_octagon": "Ottagono", "PE.Controllers.Main.txtShape_parallelogram": "Parallelogram<PERSON>", "PE.Controllers.Main.txtShape_pentagon": "Pentagono", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "Firma", "PE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline2": "Forma libera", "PE.Controllers.Main.txtShape_quadArrow": "Freccia a incrocio", "PE.Controllers.Main.txtShape_quadArrowCallout": "Callout <PERSON><PERSON> a incrocio", "PE.Controllers.Main.txtShape_rect": "Rettangolo", "PE.Controllers.Main.txtShape_ribbon": "<PERSON>stro inclinato in basso", "PE.Controllers.Main.txtShape_ribbon2": "Nastro inclinato in alto", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> destra", "PE.Controllers.Main.txtShape_rightArrowCallout": "Callout <PERSON><PERSON><PERSON> a destra", "PE.Controllers.Main.txtShape_rightBrace": "Parentesi graffa chiusa", "PE.Controllers.Main.txtShape_rightBracket": "Parentesi quadra chiusa", "PE.Controllers.Main.txtShape_round1Rect": "Rettangolo ad angolo singolo smussato", "PE.Controllers.Main.txtShape_round2DiagRect": "Rettangolo ad angolo diagonale smussato", "PE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON><PERSON> smussato dallo stesso lato", "PE.Controllers.Main.txtShape_roundRect": "Rettangolo ad angoli smussati", "PE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_smileyFace": "Faccia sorridente", "PE.Controllers.Main.txtShape_snip1Rect": "Ritaglia rettangolo ad angolo singolo", "PE.Controllers.Main.txtShape_snip2DiagRect": "Ritaglia rettangolo ad angolo diagonale", "PE.Controllers.Main.txtShape_snip2SameRect": "<PERSON><PERSON>lo smussato dallo stesso lato", "PE.Controllers.Main.txtShape_snipRoundRect": "Ritaglia e smussa singolo angolo retta<PERSON>lo", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "Stella a 10 punte", "PE.Controllers.Main.txtShape_star12": "Stella a 12 punte", "PE.Controllers.Main.txtShape_star16": "Stella a 16 punte", "PE.Controllers.Main.txtShape_star24": "Stella a 24 punte", "PE.Controllers.Main.txtShape_star32": "Stella a 32 punte", "PE.Controllers.Main.txtShape_star4": "Stella a 4 punte", "PE.Controllers.Main.txtShape_star5": "Stella a 5 punte", "PE.Controllers.Main.txtShape_star6": "Stella a 6 punte", "PE.Controllers.Main.txtShape_star7": "Stella a 7 punte", "PE.Controllers.Main.txtShape_star8": "Stella a 8 punte", "PE.Controllers.Main.txtShape_stripedRightArrow": "Freccia a strisce verso destra ", "PE.Controllers.Main.txtShape_sun": "Sole", "PE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_textRect": "Casella di testo", "PE.Controllers.Main.txtShape_trapezoid": "Trapezio", "PE.Controllers.Main.txtShape_triangle": "Triangolo", "PE.Controllers.Main.txtShape_upArrow": "Freccia su", "PE.Controllers.Main.txtShape_upArrowCallout": "Callout <PERSON> in alto", "PE.Controllers.Main.txtShape_upDownArrow": "Freccia bidirezionale su giù", "PE.Controllers.Main.txtShape_uturnArrow": "Freccia a inversione", "PE.Controllers.Main.txtShape_verticalScroll": "Scorrimento verticale", "PE.Controllers.Main.txtShape_wave": "On<PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Callout <PERSON>", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Callout <PERSON>", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Callout <PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTBlank": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChart": "Grafico", "PE.Controllers.Main.txtSldLtTChartAndTx": "Grafico e testo", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "C<PERSON> e testo", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art e testo verticale", "PE.Controllers.Main.txtSldLtTCust": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTDgm": "Di<PERSON>ram<PERSON>", "PE.Controllers.Main.txtSldLtTFourObj": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Multimedia e testo", "PE.Controllers.Main.txtSldLtTObj": "<PERSON>lo e oggetto", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Oggetto e due oggetti", "PE.Controllers.Main.txtSldLtTObjAndTx": "Oggetto e testo", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "Oggetto su testo", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON>, oggetto e did<PERSON>ia", "PE.Controllers.Main.txtSldLtTPicTx": "Immagine e didascalia", "PE.Controllers.Main.txtSldLtTSecHead": "Intestazione sezione", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitleOnly": "Solo titolo", "PE.Controllers.Main.txtSldLtTTwoColTx": "Testo su due colonne", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON> og<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Due oggetti e oggetto", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Due oggetti e testo", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Due oggetti su testo", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Due testi e due oggetti", "PE.Controllers.Main.txtSldLtTTx": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTxAndChart": "Testo e grafico", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Testo e Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Testo e multimedia", "PE.Controllers.Main.txtSldLtTTxAndObj": "Testo e oggetto", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Testo e due oggetti", "PE.Controllers.Main.txtSldLtTTxOverObj": "Testo su oggetto", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Titolo e testo verticali", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Titolo e Testo verticali su grafico", "PE.Controllers.Main.txtSldLtTVertTx": "Testo verticale", "PE.Controllers.Main.txtSlideNumber": "Numero diapositiva", "PE.Controllers.Main.txtSlideSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> diapositi<PERSON>", "PE.Controllers.Main.txtSlideText": "Testo diapositiva", "PE.Controllers.Main.txtSlideTitle": "<PERSON><PERSON>", "PE.Controllers.Main.txtStarsRibbons": "Stelle e nastri", "PE.Controllers.Main.txtTheme_basic": "Basic", "PE.Controllers.Main.txtTheme_blank": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_classic": "Classico", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "Verde", "PE.Controllers.Main.txtTheme_green_leaf": "Foglia verde", "PE.Controllers.Main.txtTheme_lines": "Linee", "PE.Controllers.Main.txtTheme_office": "Ufficio", "PE.Controllers.Main.txtTheme_office_theme": "Tema Office", "PE.Controllers.Main.txtTheme_official": "Ufficiale", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Tartaruga", "PE.Controllers.Main.txtXAxis": "Asse <PERSON>", "PE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> sconos<PERSON>.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Il tuo browser non è supportato.", "PE.Controllers.Main.uploadImageExtMessage": "Formato immagine sconosciuto.", "PE.Controllers.Main.uploadImageFileCountMessage": "Nessuna immagine caricata.", "PE.Controllers.Main.uploadImageSizeMessage": "L'immagine è troppo grande. La dimensione massima è 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Caricamento immagine in corso...", "PE.Controllers.Main.uploadImageTitleText": "Caricamento dell'immagine", "PE.Controllers.Main.waitText": "Per favore, attendi...", "PE.Controllers.Main.warnBrowserIE9": "L'applicazione è poco compatibile con IE9. Usa IE10 o più recente", "PE.Controllers.Main.warnBrowserZoom": "Le impostazioni correnti di zoom del tuo browser non sono completamente supportate. Per favore, ritorna allo zoom predefinito premendo Ctrl+0.", "PE.Controllers.Main.warnLicenseExceeded": "Hai raggiunto il limite per le connessioni simultanee agli editor %1. Questo documento verrà aperto in sola lettura.<br>Contatta l’amministratore per saperne di più.", "PE.Controllers.Main.warnLicenseExp": "La tua licenza è scaduta.<br>Si prega di aggiornare la licenza e ricaricare la pagina.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licenza scaduta. <br>Non hai accesso alla funzionalità di modifica dei documenti.<br>Contatta l'amministratore.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "La licenza deve essere rinnovata. <br>Hai un accesso limitato alle funzionalità di modifica dei documenti.<br>Contatta l'amministratore per ottenere l'accesso completo", "PE.Controllers.Main.warnLicenseUsersExceeded": "Hai raggiunto il limite per gli utenti con accesso agli editor %1. Contatta l’amministratore per saperne di più.", "PE.Controllers.Main.warnNoLicense": "Hai raggiunto il limite per le connessioni simultanee agli editor %1. Questo documento verrà aperto in sola lettura.<br>Contatta il team di vendita di %1 per i termini di aggiornamento personali.", "PE.Controllers.Main.warnNoLicenseUsers": "Hai raggiunto il limite per gli utenti con accesso agli editor %1. Contatta il team di vendita di %1 per i termini di aggiornamento personali.", "PE.Controllers.Main.warnProcessRightsChange": "Ti è stato negato il diritto di modificare il file.", "PE.Controllers.Search.notcriticalErrorTitle": "Avviso", "PE.Controllers.Search.textNoTextFound": "Impossibile trovare i dati che stavi cercando. Ti preghiamo di modificare le opzioni di ricerca.", "PE.Controllers.Search.textReplaceSkipped": "La sostituzione è stata effettuata. {0} ricorrenze sono state saltate.", "PE.Controllers.Search.textReplaceSuccess": "La ricerca è stata effettuata. {0} occorrenze sono state sostituite", "PE.Controllers.Search.warnReplaceString": "{0} non è un carattere speciale valido per la casella Sostituire con.", "PE.Controllers.Statusbar.textDisconnect": "<b>Connessione persa</b><br>Tentativo di connessione in corso. Si prega di controllare le impostazioni di connessione.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Il carattere che vuoi salvare non è accessibile su questo dispositivo.<br>Lo stile di testo sarà visualizzato usando uno dei caratteri di sistema, il carattere salvato sarà usato quando accessibile.<br>Vuoi continuare?", "PE.Controllers.Toolbar.textAccent": "Accenti", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textEmptyImgUrl": "Specifica URL immagine.", "PE.Controllers.Toolbar.textFontSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore numerico compreso tra 1 e 300", "PE.Controllers.Toolbar.textFraction": "Frazioni", "PE.Controllers.Toolbar.textFunction": "Funzioni", "PE.Controllers.Toolbar.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textIntegral": "Integrali", "PE.Controllers.Toolbar.textLargeOperator": "Operatori di grandi dimensioni", "PE.Controllers.Toolbar.textLimitAndLog": "Limiti e Logaritmi", "PE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textOperator": "Operatori", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Simboli", "PE.Controllers.Toolbar.textWarning": "Avviso", "PE.Controllers.Toolbar.txtAccent_Accent": "Acuto", "PE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON>-Sinistra in alto", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Freccia verso sinistra sopra", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Freccia a destra alta", "PE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferiore", "PE.Controllers.Toolbar.txtAccent_BarTop": "barra sopra", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Formula scatenata (con segnaposto)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula scatenata (esempio)", "PE.Controllers.Toolbar.txtAccent_Check": "Controlla", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "sottoparentesi", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vettore A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC con barra superiore", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y con barra sopra", "PE.Controllers.Toolbar.txtAccent_DDDot": "Punto triplo", "PE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON> punto", "PE.Controllers.Toolbar.txtAccent_Dot": "Punt<PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Doppia barra superiore", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Raggruppa<PERSON> carattere sotto", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Raggruppamento carattere sopra", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpione verso sinistra sopra", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Messa freccia verso destra in alto", "PE.Controllers.Toolbar.txtAccent_Hat": "Cir<PERSON>fle<PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parentesi con separatori", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parentesi con separatori", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parentesi con separatori", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Casi (due condizioni)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Casi (tre condizioni)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Stack Object", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Stack Object", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Esempio di casi", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Coefficiente binomiale", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Coefficiente binomiale", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentesi con separatori", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Parentesi quadra singola", "PE.Controllers.Toolbar.txtFractionDiagonal": "Frazione obliqua", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Differenziale", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Differenziale", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Differenziale", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Differenziale", "PE.Controllers.Toolbar.txtFractionHorizontal": "Frazione lineare", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi diviso 2", "PE.Controllers.Toolbar.txtFractionSmall": "Frazione piccola", "PE.Controllers.Toolbar.txtFractionVertical": "Frazione impilata", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Funzione coseno inversa", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Funzione coseno iperbolica inversa", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Funzione cotangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Funzione cotangente iperbolica inversa", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Funzione cosecante inversa", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Funzione cosecante iperbolica inversa ", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Funzione secante inversa", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Funziono secante iperbolica inversa", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Funzione seno inversa", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Funzione seno iperbolica inversa", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Funzione tangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Funzione tangente iperbolica inversa", "PE.Controllers.Toolbar.txtFunction_Cos": "Funzione coseno", "PE.Controllers.Toolbar.txtFunction_Cosh": "Funzione coseno iperbolica", "PE.Controllers.Toolbar.txtFunction_Cot": "Funzione cotangente", "PE.Controllers.Toolbar.txtFunction_Coth": "Funzione cotangente iperbolica", "PE.Controllers.Toolbar.txtFunction_Csc": "Funzione cosecante", "PE.Controllers.Toolbar.txtFunction_Csch": "Funzione cosecante iperbolica", "PE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> the<PERSON>", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "formula tangente", "PE.Controllers.Toolbar.txtFunction_Sec": "Funzione secante", "PE.Controllers.Toolbar.txtFunction_Sech": "Funzione secante iperbolica inversa", "PE.Controllers.Toolbar.txtFunction_Sin": "Funzione seno", "PE.Controllers.Toolbar.txtFunction_Sinh": "Funzione seno iperbolica", "PE.Controllers.Toolbar.txtFunction_Tan": "Funzione tangente", "PE.Controllers.Toolbar.txtFunction_Tanh": "Funzione tangente iperbolica", "PE.Controllers.Toolbar.txtIntegral": "Integrale", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Differenziale theta", "PE.Controllers.Toolbar.txtIntegral_dx": "Differenziale x", "PE.Controllers.Toolbar.txtIntegral_dy": "Differenziale y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrale", "PE.Controllers.Toolbar.txtIntegralDouble": "Doppio integrale", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Doppio integrale", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Doppio integrale", "PE.Controllers.Toolbar.txtIntegralOriented": "Contorno integrale", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contorno integrale", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Superficie Integrale", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Superficie Integrale", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Superficie Integrale", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contorno integrale", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume Integrale", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume Integrale", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume Integrale", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integrale", "PE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON> Integrale", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON> Integrale", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON> Integrale", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Porzione", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Porzione", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Porzione", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Porzione", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Porzione", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unione", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersezione", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersezione", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersezione", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersezione", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersezione", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Somma", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Unione", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unione", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unione", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unione", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unione", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Esempio limite", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Esemp<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo naturale", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimo", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Matrice vuota", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 <PERSON><PERSON> vuota", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 <PERSON>rice vuota", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Matrice vuota", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON> vuota con parentesi", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON> vuota con parentesi", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON> vuota con parentesi", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON> vuota con parentesi", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 <PERSON><PERSON> vuota", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Matrice vuota", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Matrice vuota", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 <PERSON><PERSON> vuota", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Punti di base", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Punti linea mediana", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> diagonali", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Punti verticali", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> sparsa", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> sparsa", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Matrice di identità", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Matrice di identità", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Matrice di identità", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Matrice di identità", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> in basso", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON>-Sinistra in alto", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Freccia verso sinistra sotto", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Freccia verso sinistra sopra", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Freccia a destra bassa", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Freccia a destra alta", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Due punti uguali", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Rendimenti", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Rendimenti delta", "PE.Controllers.Toolbar.txtOperator_Definition": "Uguale a Per definizione", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta uguale a", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> in basso", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON>-Sinistra in alto", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Freccia verso sinistra sotto", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Freccia verso sinistra sopra", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Freccia a destra bassa", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Freccia a destra alta", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON> con", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radicale", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radicale", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Radice quadrata con Grado", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON>dice cubica", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radicale con Grado", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSubSup": "Pedice-Apice", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Pedice-Apice sinistro", "PE.Controllers.Toolbar.txtScriptSup": "Apice", "PE.Controllers.Toolbar.txtSymbol_about": "Approssimativamente", "PE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON>uasi uguale a", "PE.Controllers.Toolbar.txtSymbol_ast": "Operatore asterisco", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Operatore elenco puntato", "PE.Controllers.Toolbar.txtSymbol_cap": "Intersezione", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON>dice cubica", "PE.Controllers.Toolbar.txtSymbol_cdots": "Ellissi orizzontale di linea mediana", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Approssimativamente uguale a", "PE.Controllers.Toolbar.txtSymbol_cup": "Unione", "PE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON> diagonale in basso a destra", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Segno di divisione", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Freccia in giù", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Insieme vuoto", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identico a", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_factorial": "Fattoriale", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Gradi Fah<PERSON>he<PERSON>", "PE.Controllers.Toolbar.txtSymbol_forall": "Per tutti", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "Maggiore o uguale a", "PE.Controllers.Toolbar.txtSymbol_gg": "Molto più grande di", "PE.Controllers.Toolbar.txtSymbol_greater": "Più grande di", "PE.Controllers.Toolbar.txtSymbol_in": "Elemento di", "PE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON>cia sinistra-destra", "PE.Controllers.Toolbar.txtSymbol_leq": "Minore o uguale a ", "PE.Controllers.Toolbar.txtSymbol_less": "Inferiore a", "PE.Controllers.Toolbar.txtSymbol_ll": "Molto meno di", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "<PERSON>o più", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON><PERSON> <PERSON>", "PE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON> come <PERSON>", "PE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_notexists": "Non esiste", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Differenziale Parziale", "PE.Controllers.Toolbar.txtSymbol_percent": "Percent<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_propto": "Proporzionale a", "PE.Controllers.Toolbar.txtSymbol_psi": "psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_qed": "Fine della dimostrazione", "PE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON> diagonale in alto a destra", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> destra", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Segno Radicale", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Dunque", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "Segno di moltiplicazione", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Freccia su", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Variante Epsilon", "PE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Variante Sigma", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vdots": "Ellissi verticale", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "<PERSON>tta alla diapositiva", "PE.Controllers.Viewport.textFitWidth": "<PERSON><PERSON> alla larghezza", "PE.Views.Animation.str0_5": "0,5 s (molto veloce)", "PE.Views.Animation.str1": "1 s (veloce)", "PE.Views.Animation.str2": "2 s (medio)", "PE.Views.Animation.str20": "20 s (estremamente lento)", "PE.Views.Animation.str3": "3 s (lento)", "PE.Views.Animation.str5": "5 s (molto lento)", "PE.Views.Animation.strDelay": "<PERSON><PERSON>", "PE.Views.Animation.strDuration": "<PERSON><PERSON>", "PE.Views.Animation.strRepeat": "Ripetere", "PE.Views.Animation.strRewind": "Riavvolgere", "PE.Views.Animation.strStart": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strTrigger": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "Anteprima automatica", "PE.Views.Animation.textMoreEffects": "Mostrare più e<PERSON>tti", "PE.Views.Animation.textMoveEarlier": "Spostare avanti", "PE.Views.Animation.textMoveLater": "Spostare di seguito", "PE.Views.Animation.textMultiple": "Multipli", "PE.Views.Animation.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textNoRepeat": "(nessuna)", "PE.Views.Animation.textOnClickOf": "Al clic di", "PE.Views.Animation.textOnClickSequence": "Alla sequenza di clic", "PE.Views.Animation.textStartAfterPrevious": "<PERSON><PERSON> il <PERSON>e", "PE.Views.Animation.textStartOnClick": "Al clic", "PE.Views.Animation.textStartWithPrevious": "Con il precedente", "PE.Views.Animation.textUntilEndOfSlide": "Fino alla fine della diapositiva", "PE.Views.Animation.textUntilNextClick": "Fino al prossimo clic", "PE.Views.Animation.txtAddEffect": "Aggiungi animazione", "PE.Views.Animation.txtAnimationPane": "Riquadro animazione", "PE.Views.Animation.txtParameters": "Parametri", "PE.Views.Animation.txtPreview": "Anteprima", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Anteprima dell'effetto", "PE.Views.AnimationDialog.textTitle": "<PERSON><PERSON>", "PE.Views.ChartSettings.textAdvanced": "Mostra impostazioni avanzate", "PE.Views.ChartSettings.textChartType": "Cambia tipo di grafico", "PE.Views.ChartSettings.textDown": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textEditData": "Modifica dati", "PE.Views.ChartSettings.textHeight": "Altezza", "PE.Views.ChartSettings.textKeepRatio": "Proporzioni costanti", "PE.Views.ChartSettings.textLeft": "A sinistra", "PE.Views.ChartSettings.textRight": "A destra", "PE.Views.ChartSettings.textSize": "Dimensione", "PE.Views.ChartSettings.textStyle": "Stile", "PE.Views.ChartSettings.textUp": "Verso l'alto", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "Testo alternativo", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Descrizione", "PE.Views.ChartSettingsAdvanced.textAltTip": "La rappresentazione testuale alternativa delle informazioni riguardanti l'oggetto visivo, che verrà letta alle persone con deficit visivi o cognitivi per aiutarli a comprendere meglio quali informazioni sono contenute nell'immagine, nella forma, nel grafico o nella tabella.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textCenter": "Centrato", "PE.Views.ChartSettingsAdvanced.textFrom": "Da", "PE.Views.ChartSettingsAdvanced.textHeight": "Altezza", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Orizzontale", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Proporzioni costanti", "PE.Views.ChartSettingsAdvanced.textPlacement": "Posizionamento", "PE.Views.ChartSettingsAdvanced.textPosition": "Posizione", "PE.Views.ChartSettingsAdvanced.textSize": "Dimensione", "PE.Views.ChartSettingsAdvanced.textTitle": "Grafico - Impostazioni avanzate", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON> in alto a sinistra", "PE.Views.ChartSettingsAdvanced.textVertical": "Verticale", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Imposta formato predefinito per {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Imposta come predefinito", "PE.Views.DateTimeDialog.textFormat": "Formati", "PE.Views.DateTimeDialog.textLang": "<PERSON><PERSON>", "PE.Views.DateTimeDialog.textUpdate": "Aggiorna automaticamente", "PE.Views.DateTimeDialog.txtTitle": "Data e ora", "PE.Views.DocumentHolder.aboveText": "Al di sopra", "PE.Views.DocumentHolder.addCommentText": "Aggiungi commento", "PE.Views.DocumentHolder.addToLayoutText": "Aggiungi al layout", "PE.Views.DocumentHolder.advancedChartText": "Impostazioni grafico avanzate", "PE.Views.DocumentHolder.advancedImageText": "Impostazioni avanzate dell'immagine", "PE.Views.DocumentHolder.advancedParagraphText": "Impostazioni avanzate del paragrafo", "PE.Views.DocumentHolder.advancedShapeText": "Impostazioni avanzate forma", "PE.Views.DocumentHolder.advancedTableText": "Impostazioni avanzate della tabella", "PE.Views.DocumentHolder.alignmentText": "Allineamento", "PE.Views.DocumentHolder.belowText": "Al di sotto", "PE.Views.DocumentHolder.cellAlignText": "Allineamento verticale celle", "PE.Views.DocumentHolder.cellText": "Cella", "PE.Views.DocumentHolder.centerText": "Al centro", "PE.Views.DocumentHolder.columnText": "<PERSON>onna", "PE.Views.DocumentHolder.deleteColumnText": "Elimina colonna", "PE.Views.DocumentHolder.deleteRowText": "Elimina riga", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON> tabella", "PE.Views.DocumentHolder.deleteText": "Elimina", "PE.Views.DocumentHolder.direct270Text": "Ruota testo verso l'alto", "PE.Views.DocumentHolder.direct90Text": "Ruota testo verso il basso", "PE.Views.DocumentHolder.directHText": "Orizzontale", "PE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON> del testo", "PE.Views.DocumentHolder.editChartText": "Modifica dati", "PE.Views.DocumentHolder.editHyperlinkText": "Modifica collegamento ipertestuale", "PE.Views.DocumentHolder.hyperlinkText": "Collegamento ipertestuale", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> tutto", "PE.Views.DocumentHolder.ignoreSpellText": "Ignora", "PE.Views.DocumentHolder.insertColumnLeftText": "Colonna a sinistra", "PE.Views.DocumentHolder.insertColumnRightText": "Colonna a destra", "PE.Views.DocumentHolder.insertColumnText": "Inserisci colonna", "PE.Views.DocumentHolder.insertRowAboveText": "Riga sopra", "PE.Views.DocumentHolder.insertRowBelowText": "Riga sotto", "PE.Views.DocumentHolder.insertRowText": "Inserisci riga", "PE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.langText": "Seleziona lingua", "PE.Views.DocumentHolder.leftText": "A sinistra", "PE.Views.DocumentHolder.loadSpellText": "Caricamento varianti in corso...", "PE.Views.DocumentHolder.mergeCellsText": "Unisci celle", "PE.Views.DocumentHolder.mniCustomTable": "Inserisci tabella personalizzata", "PE.Views.DocumentHolder.moreText": "<PERSON>ù varianti...", "PE.Views.DocumentHolder.noSpellVariantsText": "Nessuna variante", "PE.Views.DocumentHolder.originalSizeText": "Dimensione reale", "PE.Views.DocumentHolder.removeHyperlinkText": "Elimina collegamento ipertestuale", "PE.Views.DocumentHolder.rightText": "destra", "PE.Views.DocumentHolder.rowText": "Riga", "PE.Views.DocumentHolder.selectText": "Seleziona", "PE.Views.DocumentHolder.spellcheckText": "Controllo ortografia", "PE.Views.DocumentHolder.splitCellsText": "Dividi cella...", "PE.Views.DocumentHolder.splitCellTitleText": "Dividi cella", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON> in secondo piano", "PE.Views.DocumentHolder.textArrangeBackward": "Porta indietro", "PE.Views.DocumentHolder.textArrangeForward": "Porta avanti", "PE.Views.DocumentHolder.textArrangeFront": "<PERSON>a in primo piano", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "Copia", "PE.Views.DocumentHolder.textCrop": "Rita<PERSON>", "PE.Views.DocumentHolder.textCropFill": "Riempimento", "PE.Views.DocumentHolder.textCropFit": "Adatta", "PE.Views.DocumentHolder.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCut": "Taglia", "PE.Views.DocumentHolder.textDistributeCols": "Distribuisci colonne", "PE.Views.DocumentHolder.textDistributeRows": "Distribuisci righe", "PE.Views.DocumentHolder.textEditPoints": "Modifica punti", "PE.Views.DocumentHolder.textFlipH": "Capovolgi orizzontalmente", "PE.Views.DocumentHolder.textFlipV": "Capovolgi verticalmente", "PE.Views.DocumentHolder.textFromFile": "Da file", "PE.Views.DocumentHolder.textFromStorage": "Da spazio di archiviazione", "PE.Views.DocumentHolder.textFromUrl": "Da URL", "PE.Views.DocumentHolder.textNextPage": "Diapositiva successiva", "PE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textPrevPage": "Diapositiva precedente", "PE.Views.DocumentHolder.textReplace": "Sostituisci immagine", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Ruota 90° a sinistra", "PE.Views.DocumentHolder.textRotate90": "Ruota 90° a destra", "PE.Views.DocumentHolder.textRulers": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>a in basso", "PE.Views.DocumentHolder.textShapeAlignCenter": "Allinea al centro", "PE.Views.DocumentHolder.textShapeAlignLeft": "Allinea a sinistra", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Allinea in mezzo", "PE.Views.DocumentHolder.textShapeAlignRight": "Allinea a destra", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON>a in alto", "PE.Views.DocumentHolder.textSlideSettings": "Impostazioni diapositiva", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "Questo elemento è attualmente in fase di modifica da un altro utente.", "PE.Views.DocumentHolder.toDictionaryText": "Aggiungi al Dizionario", "PE.Views.DocumentHolder.txtAddBottom": "Aggiungi bordo inferiore", "PE.Views.DocumentHolder.txtAddFractionBar": "Aggiungi barra di frazione", "PE.Views.DocumentHolder.txtAddHor": "Aggiungi linea orizzontale", "PE.Views.DocumentHolder.txtAddLB": "Aggiungi linea inferiore sinistra", "PE.Views.DocumentHolder.txtAddLeft": "Aggiungi bordo sinistro", "PE.Views.DocumentHolder.txtAddLT": "Aggiungi linea superiore sinistra", "PE.Views.DocumentHolder.txtAddRight": "Agg<PERSON>ngi bordo destro", "PE.Views.DocumentHolder.txtAddTop": "Aggiungi bordo superiore", "PE.Views.DocumentHolder.txtAddVer": "Aggiungi linea verticale", "PE.Views.DocumentHolder.txtAlign": "Allinea", "PE.Views.DocumentHolder.txtAlignToChar": "Allinea al carattere", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Sfondo", "PE.Views.DocumentHolder.txtBorderProps": "Proprietà bordo", "PE.Views.DocumentHolder.txtBottom": "In basso", "PE.Views.DocumentHolder.txtChangeLayout": "Cambia layout", "PE.Views.DocumentHolder.txtChangeTheme": "Modifica Tema", "PE.Views.DocumentHolder.txtColumnAlign": "Allineamento colonna", "PE.Views.DocumentHolder.txtDecreaseArg": "Diminuisci dimensione argomento", "PE.Views.DocumentHolder.txtDeleteArg": "Elimina argomento", "PE.Views.DocumentHolder.txtDeleteBreak": "Elimina interruzione manuale", "PE.Views.DocumentHolder.txtDeleteChars": "Elimina i caratteri racchiusi", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Elimina caratteri e separatori inclusi", "PE.Views.DocumentHolder.txtDeleteEq": "Elimina equazione", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON>mina char", "PE.Views.DocumentHolder.txtDeleteRadical": "Elimina radicale", "PE.Views.DocumentHolder.txtDeleteSlide": "Elimina diapositiva", "PE.Views.DocumentHolder.txtDistribHor": "Distribuisci orizzontalmente", "PE.Views.DocumentHolder.txtDistribVert": "Distribuisci verticalmente", "PE.Views.DocumentHolder.txtDuplicateSlide": "Duplica diapositiva", "PE.Views.DocumentHolder.txtFractionLinear": "Modifica a frazione lineare", "PE.Views.DocumentHolder.txtFractionSkewed": "Modifica a frazione obliqua", "PE.Views.DocumentHolder.txtFractionStacked": "Modifica a frazione impilata", "PE.Views.DocumentHolder.txtGroup": "Raggruppa", "PE.Views.DocumentHolder.txtGroupCharOver": "Char sul testo", "PE.Views.DocumentHolder.txtGroupCharUnder": "Char sotto il testo", "PE.Views.DocumentHolder.txtHideBottom": "Nascondi il bordo inferiore", "PE.Views.DocumentHolder.txtHideBottomLimit": "Nascondi il limite inferiore", "PE.Views.DocumentHolder.txtHideCloseBracket": "Nascondi parentesi chiusa", "PE.Views.DocumentHolder.txtHideDegree": "Nascondi grado", "PE.Views.DocumentHolder.txtHideHor": "Nascondi linea orizzontale", "PE.Views.DocumentHolder.txtHideLB": "Nascondi linea inferiore sinistra", "PE.Views.DocumentHolder.txtHideLeft": "Nascondi bordo sinistro", "PE.Views.DocumentHolder.txtHideLT": "Nascondi linea superiore sinistra", "PE.Views.DocumentHolder.txtHideOpenBracket": "Nascondi parentesi aperta", "PE.Views.DocumentHolder.txtHidePlaceholder": "Nascondi segnaposto", "PE.Views.DocumentHolder.txtHideRight": "Nascondi bordo destro", "PE.Views.DocumentHolder.txtHideTop": "Nascondi bordo superiore", "PE.Views.DocumentHolder.txtHideTopLimit": "Nascondi limite superiore", "PE.Views.DocumentHolder.txtHideVer": "Nascondi linea verticale", "PE.Views.DocumentHolder.txtIncreaseArg": "Aumenta dimensione argomento", "PE.Views.DocumentHolder.txtInsertArgAfter": "Inserisci argomento dopo", "PE.Views.DocumentHolder.txtInsertArgBefore": "Inserisci argomento prima", "PE.Views.DocumentHolder.txtInsertBreak": "Inserisci interruzione manuale", "PE.Views.DocumentHolder.txtInsertEqAfter": "Inserisci equazione dopo", "PE.Views.DocumentHolder.txtInsertEqBefore": "Inserisci equazione prima", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON> solo il testo", "PE.Views.DocumentHolder.txtLimitChange": "Modifica posizione dei limiti", "PE.Views.DocumentHolder.txtLimitOver": "Limite sul testo", "PE.Views.DocumentHolder.txtLimitUnder": "Limite sotto il testo", "PE.Views.DocumentHolder.txtMatchBrackets": "Adatta le parentesi all'altezza dell'argomento", "PE.Views.DocumentHolder.txtMatrixAlign": "Allineamento Matrice", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Spostare la diapositiva alla fine", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Spostare la diapositiva all'inizio", "PE.Views.DocumentHolder.txtNewSlide": "Nuova diapositiva", "PE.Views.DocumentHolder.txtOverbar": "Barra sopra al testo", "PE.Views.DocumentHolder.txtPasteDestFormat": "Usa tema di destinazione", "PE.Views.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Mantieni la formattazione sorgente", "PE.Views.DocumentHolder.txtPressLink": "Premi {0} e clicca sul collegamento", "PE.Views.DocumentHolder.txtPreview": "Avvia presentazione", "PE.Views.DocumentHolder.txtPrintSelection": "Stampa Selezione", "PE.Views.DocumentHolder.txtRemFractionBar": "Rimuovi la barra di frazione", "PE.Views.DocumentHolder.txtRemLimit": "Rimuovi limite", "PE.Views.DocumentHolder.txtRemoveAccentChar": "R<PERSON>uovi accento carattere", "PE.Views.DocumentHolder.txtRemoveBar": "<PERSON><PERSON> barra", "PE.Views.DocumentHolder.txtRemScripts": "Rimuovi gli script", "PE.Views.DocumentHolder.txtRemSubscript": "Elimina pedice", "PE.Views.DocumentHolder.txtRemSuperscript": "Elimina apice", "PE.Views.DocumentHolder.txtResetLayout": "Reimposta diapositiva", "PE.Views.DocumentHolder.txtScriptsAfter": "Script dopo il testo", "PE.Views.DocumentHolder.txtScriptsBefore": "Script prima del testo", "PE.Views.DocumentHolder.txtSelectAll": "Se<PERSON><PERSON>na tutto", "PE.Views.DocumentHolder.txtShowBottomLimit": "Mostra limite inferiore", "PE.Views.DocumentHolder.txtShowCloseBracket": "Mostra parentesi quadra di chiusura", "PE.Views.DocumentHolder.txtShowDegree": "Mostra grado", "PE.Views.DocumentHolder.txtShowOpenBracket": "Mostra parentesi quadra di apertura", "PE.Views.DocumentHolder.txtShowPlaceholder": "Mostra segna<PERSON>o", "PE.Views.DocumentHolder.txtShowTopLimit": "Mostra limite alto", "PE.Views.DocumentHolder.txtSlide": "Diapositiva", "PE.Views.DocumentHolder.txtSlideHide": "Nascondi Diapositiva", "PE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtTop": "In alto", "PE.Views.DocumentHolder.txtUnderbar": "<PERSON>a sotto al testo", "PE.Views.DocumentHolder.txtUngroup": "Separa", "PE.Views.DocumentHolder.txtWarnUrl": "C<PERSON>care questo link può essere dannoso per il tuo dispositivo e i dati.<br>Sei sicuro di voler continuare?", "PE.Views.DocumentHolder.vertAlignText": "Allineamento verticale", "PE.Views.DocumentPreview.goToSlideText": "Vai alla diapositiva", "PE.Views.DocumentPreview.slideIndexText": "Diapositiva {0} di {1}", "PE.Views.DocumentPreview.txtClose": "Chiudi presentazione", "PE.Views.DocumentPreview.txtEndSlideshow": "Fine della presentazione", "PE.Views.DocumentPreview.txtExitFullScreen": "Esci dalla modalità schermo intero", "PE.Views.DocumentPreview.txtFinalMessage": "Fine dell'anteprima della diapositiva. Fai clic per uscire.", "PE.Views.DocumentPreview.txtFullScreen": "Schermo intero", "PE.Views.DocumentPreview.txtNext": "Diapositiva successiva", "PE.Views.DocumentPreview.txtPageNumInvalid": "Numero diapositiva non corretto", "PE.Views.DocumentPreview.txtPause": "Sospendi presentazione", "PE.Views.DocumentPreview.txtPlay": "Avvia presentazione", "PE.Views.DocumentPreview.txtPrev": "Diapositiva precedente", "PE.Views.DocumentPreview.txtReset": "Reimposta", "PE.Views.FileMenu.btnAboutCaption": "Informazioni su", "PE.Views.FileMenu.btnBackCaption": "Apri percorso file", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON> il <PERSON>ù", "PE.Views.FileMenu.btnCreateNewCaption": "Crea nuovo", "PE.Views.FileMenu.btnDownloadCaption": "Scarica come", "PE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "Apr<PERSON>", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Cronologia delle versioni", "PE.Views.FileMenu.btnInfoCaption": "Informazioni presentazione", "PE.Views.FileMenu.btnPrintCaption": "Stampa", "PE.Views.FileMenu.btnProtectCaption": "<PERSON>tegg<PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "<PERSON>i recenti", "PE.Views.FileMenu.btnRenameCaption": "Rinomina", "PE.Views.FileMenu.btnReturnCaption": "Torna alla presentazione", "PE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON> di accesso", "PE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON> con Nome", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Salva copia come", "PE.Views.FileMenu.btnSettingsCaption": "Impostazioni avanzate", "PE.Views.FileMenu.btnToEditCaption": "Modifica presentazione", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Presentazione vuota", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Crea nuovo", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Applica", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Aggiungi Autore", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Aggiu<PERSON>i testo", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applicazione", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autore", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Modifica diritti di accesso", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Commento", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Ultima modifica di", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Ultima modifica", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Proprietario", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persone con diritti", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Caricato", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Modifica diritti di accesso", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Persone con diritti", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Avviso", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "con Password", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteggi Presentazione", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "con Firma", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Modifica presentazione", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "La modifica eliminerà le firme dalla presentazione. <br>Sei sicuro di voler continuare?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Questa presentazione è protetta con password", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Le firme valide sono state aggiunte alla presentazione. La presentazione è protetta dalla modifica.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Alcune delle firme digitali nella presentazione non sono valide o non possono essere verificate. La presentazione è protetta dalla modifica.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Mostra firme", "PE.Views.FileMenuPanels.Settings.okButtonText": "Applica", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modalità di co-editing", "PE.Views.FileMenuPanels.Settings.strFast": "Rapido", "PE.Views.FileMenuPanels.Settings.strFontRender": "Suggerimento per i caratteri", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignora le parole in MAIUSCOLO", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignora le parole con i numeri", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Impostazioni macro", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Mostra il pulsante opzioni Incolla quando il contenuto viene incollato", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Mostrare le modifiche degli altri utenti", "PE.Views.FileMenuPanels.Settings.strStrict": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strTheme": "Tema dell'interfaccia", "PE.Views.FileMenuPanels.Settings.strUnit": "Unità di misura", "PE.Views.FileMenuPanels.Settings.strZoom": "Valore di zoom predefinito", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Ogni 10 minuti", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Ogni 30 minuti", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Ogni 5 minuti", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Ogni ora", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Guide di allineamento", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Recupero automatico", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Salvataggio automatico", "PE.Views.FileMenuPanels.Settings.textDisabled": "Disattivat<PERSON>", "PE.Views.FileMenuPanels.Settings.textForceSave": "Salva versioni intermedie", "PE.Views.FileMenuPanels.Settings.textMinute": "Ogni minuto", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opzioni di correzione automatica ...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Modalità cache predefinita", "PE.Views.FileMenuPanels.Settings.txtCm": "Centimetro", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaborazione", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Modifica e salvataggio", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Co-editing in tempo reale. <PERSON><PERSON> le modifiche vengono salvate automaticamente", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "<PERSON>tta alla diapositiva", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "<PERSON><PERSON> alla larghezza", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Geroglifici", "PE.Views.FileMenuPanels.Settings.txtInch": "Pollice", "PE.Views.FileMenuPanels.Settings.txtLast": "Ultime", "PE.Views.FileMenuPanels.Settings.txtMac": "come OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "PE.Views.FileMenuPanels.Settings.txtProofing": "Correzione", "PE.Views.FileMenuPanels.Settings.txtPt": "Punt<PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "<PERSON><PERSON><PERSON> tutto", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Abilita tutte le macro senza notifica", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Controllo ortografia", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON> tutto", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Disabilita tutte le macro senza notifica", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilizza il pulsante \"Salvare\" per sincronizzare le modifiche apportate da te e da altri", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utilizzare il tasto Alt per navigare nell'interfaccia utente usando la tastiera", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utilizzare il tasto Opzione per navigare nell'interfaccia utente usando la tastiera", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostra notifica", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Disabilita tutte le macro con notifica", "PE.Views.FileMenuPanels.Settings.txtWin": "come Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Spazio di lavoro", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.GridSettings.textSpacing": "Spaziatura", "PE.Views.HeaderFooterDialog.applyAllText": "Applica a tutti", "PE.Views.HeaderFooterDialog.applyText": "Applica", "PE.Views.HeaderFooterDialog.diffLanguage": "Non è possibile utilizzare un formato data in una lingua diversa da quella della diapositiva.<br>Per cambiare il master, fare clic su \"Applica a tutto\" anziché \"Applica\"", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Avviso", "PE.Views.HeaderFooterDialog.textDateTime": "Data e ora", "PE.Views.HeaderFooterDialog.textFixed": "Bloccato", "PE.Views.HeaderFooterDialog.textFooter": "Testo a piè di pagina", "PE.Views.HeaderFooterDialog.textFormat": "Formati", "PE.Views.HeaderFooterDialog.textLang": "<PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textNotTitle": "Non mostrare sul titolo della diapositiva", "PE.Views.HeaderFooterDialog.textPreview": "Anteprima", "PE.Views.HeaderFooterDialog.textSlideNum": "Numero diapositiva", "PE.Views.HeaderFooterDialog.textTitle": "Impostazioni piè di pagina", "PE.Views.HeaderFooterDialog.textUpdate": "Aggiorna automaticamente", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Visualizza", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Collega a", "PE.Views.HyperlinkSettingsDialog.textDefault": "Frammento di testo selezionato", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> did<PERSON>calia qui", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON><PERSON> collegamento qui", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserisci descrizione comando qui", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Collegamento esterno", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Diapositiva in questa presentazione", "PE.Views.HyperlinkSettingsDialog.textSlides": "Diapositive", "PE.Views.HyperlinkSettingsDialog.textTipText": "Testo del suggerimento", "PE.Views.HyperlinkSettingsDialog.textTitle": "Impostazioni collegamento ipertestuale", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Campo obbligatorio", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Prima diapositiva", "PE.Views.HyperlinkSettingsDialog.txtLast": "Ultima diapositiva", "PE.Views.HyperlinkSettingsDialog.txtNext": "Diapositiva successiva", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Il formato URL richiesto è \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Diapositiva precedente", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Questo campo è limitato a 2083 caratteri", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Diapositiva", "PE.Views.ImageSettings.textAdvanced": "Mostra impostazioni avanzate", "PE.Views.ImageSettings.textCrop": "Rita<PERSON>", "PE.Views.ImageSettings.textCropFill": "Riempimento", "PE.Views.ImageSettings.textCropFit": "Adatta", "PE.Views.ImageSettings.textCropToShape": "Ritagliare a forma", "PE.Views.ImageSettings.textEdit": "Modifica", "PE.Views.ImageSettings.textEditObject": "Modifica oggetto", "PE.Views.ImageSettings.textFitSlide": "<PERSON>tta alla diapositiva", "PE.Views.ImageSettings.textFlip": "Capovolgere", "PE.Views.ImageSettings.textFromFile": "Da file", "PE.Views.ImageSettings.textFromStorage": "Da spazio di archiviazione", "PE.Views.ImageSettings.textFromUrl": "Da URL", "PE.Views.ImageSettings.textHeight": "Altezza", "PE.Views.ImageSettings.textHint270": "Ruota 90° a sinistra", "PE.Views.ImageSettings.textHint90": "Ruota 90° a destra", "PE.Views.ImageSettings.textHintFlipH": "Capovolgi orizzontalmente", "PE.Views.ImageSettings.textHintFlipV": "Capovolgi verticalmente", "PE.Views.ImageSettings.textInsert": "Sostituisci immagine", "PE.Views.ImageSettings.textOriginalSize": "Dimensione reale", "PE.Views.ImageSettings.textRecentlyUsed": "Usati di recente", "PE.Views.ImageSettings.textRotate90": "Ruota di 90°", "PE.Views.ImageSettings.textRotation": "Rotazione", "PE.Views.ImageSettings.textSize": "Dimensione", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Testo alternativo", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Descrizione", "PE.Views.ImageSettingsAdvanced.textAltTip": "La rappresentazione testuale alternativa delle informazioni riguardanti l'oggetto visivo, che verrà letta alle persone con deficit visivi o cognitivi per aiutarli a comprendere meglio quali informazioni sono contenute nell'immagine, nella forma, nel grafico o nella tabella.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Centrato", "PE.Views.ImageSettingsAdvanced.textFlipped": "Capovolto", "PE.Views.ImageSettingsAdvanced.textFrom": "Da", "PE.Views.ImageSettingsAdvanced.textHeight": "Altezza", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Orizzontale", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Orizzontalmente", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporzioni costanti", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Dimensione reale", "PE.Views.ImageSettingsAdvanced.textPlacement": "Posizionamento", "PE.Views.ImageSettingsAdvanced.textPosition": "Posizione", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotazione", "PE.Views.ImageSettingsAdvanced.textSize": "Dimensione", "PE.Views.ImageSettingsAdvanced.textTitle": "Immagine - Impostazioni avanzate", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON> in alto a sinistra", "PE.Views.ImageSettingsAdvanced.textVertical": "Verticale", "PE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "Informazioni su", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "Commenti", "PE.Views.LeftMenu.tipPlugins": "Plugin", "PE.Views.LeftMenu.tipSearch": "Ricerca", "PE.Views.LeftMenu.tipSlides": "Diapositive", "PE.Views.LeftMenu.tipSupport": "Feedback & Supporto", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON>", "PE.Views.LeftMenu.txtDeveloper": "MODALITÀ SVILUPPATORE", "PE.Views.LeftMenu.txtEditor": "Presentation Editor", "PE.Views.LeftMenu.txtLimit": "‎<PERSON><PERSON>‎", "PE.Views.LeftMenu.txtTrial": "Modalità di prova", "PE.Views.LeftMenu.txtTrialDev": "Prova Modalità sviluppatore", "PE.Views.ParagraphSettings.strLineHeight": "Interlinea", "PE.Views.ParagraphSettings.strParagraphSpacing": "Spaziatura del paragrafo", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "Prima", "PE.Views.ParagraphSettings.textAdvanced": "Mostra impostazioni avanzate", "PE.Views.ParagraphSettings.textAt": "A", "PE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAuto": "Multiplo", "PE.Views.ParagraphSettings.textExact": "Esattamente", "PE.Views.ParagraphSettings.txtAutoText": "Auto", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Le schede specificate appariranno in questo campo", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Barrato do<PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndent": "R<PERSON>ri", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "A sinistra", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interlinea", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "A destra", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Prima", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciale", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Rientri e spaziatura", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Spaziatura", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Barrato", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Apice", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulazione", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Allineamento", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Multiplo", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Spaziatura caratteri", "PE.Views.ParagraphSettingsAdvanced.textDefault": "scheda predefinita", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "Esat<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prima riga", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Sospensione", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Giustificato", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nessuna)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Elimina", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON> tutto", "PE.Views.ParagraphSettingsAdvanced.textSet": "Specifica", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Al centro", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "A sinistra", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posizione", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "A destra", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Paragrafo - Impostazioni avanzate", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "PE.Views.RightMenu.txtChartSettings": "Impostazioni grafico", "PE.Views.RightMenu.txtImageSettings": "Impostazioni immagine", "PE.Views.RightMenu.txtParagraphSettings": "Impostazioni paragrafo", "PE.Views.RightMenu.txtShapeSettings": "Impostazioni forma", "PE.Views.RightMenu.txtSignatureSettings": "Impostazioni della Firma", "PE.Views.RightMenu.txtSlideSettings": "Impostazioni diapositiva", "PE.Views.RightMenu.txtTableSettings": "Impostazioni tabella", "PE.Views.RightMenu.txtTextArtSettings": "Impostazioni Text Art", "PE.Views.ShapeSettings.strBackground": "Colore sfondo", "PE.Views.ShapeSettings.strChange": "Modifica forma automatica", "PE.Views.ShapeSettings.strColor": "Colore", "PE.Views.ShapeSettings.strFill": "Riempimento", "PE.Views.ShapeSettings.strForeground": "Colore primo piano", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strShadow": "Mostra ombra", "PE.Views.ShapeSettings.strSize": "Dimensione", "PE.Views.ShapeSettings.strStroke": "Linea", "PE.Views.ShapeSettings.strTransparency": "Opacità", "PE.Views.ShapeSettings.strType": "Tipo", "PE.Views.ShapeSettings.textAdvanced": "Mostra impostazioni avanzate", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 0 pt e 1584 pt.", "PE.Views.ShapeSettings.textColor": "Colore di riempimento", "PE.Views.ShapeSettings.textDirection": "Direzione", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFlip": "Capovolgere", "PE.Views.ShapeSettings.textFromFile": "Da file", "PE.Views.ShapeSettings.textFromStorage": "Da spazio di archiviazione", "PE.Views.ShapeSettings.textFromUrl": "Da URL", "PE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textGradientFill": "Riempimento sfumato", "PE.Views.ShapeSettings.textHint270": "Ruota 90° a sinistra", "PE.Views.ShapeSettings.textHint90": "Ruota 90° a destra", "PE.Views.ShapeSettings.textHintFlipH": "Capovolgi orizzontalmente", "PE.Views.ShapeSettings.textHintFlipV": "Capovolgi verticalmente", "PE.Views.ShapeSettings.textImageTexture": "Immagine o trama", "PE.Views.ShapeSettings.textLinear": "Lineare", "PE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textPosition": "Posizione", "PE.Views.ShapeSettings.textRadial": "Radiale", "PE.Views.ShapeSettings.textRecentlyUsed": "Usati di recente", "PE.Views.ShapeSettings.textRotate90": "Ruota di 90°", "PE.Views.ShapeSettings.textRotation": "Rotazione", "PE.Views.ShapeSettings.textSelectImage": "Seleziona immagine", "PE.Views.ShapeSettings.textSelectTexture": "Seleziona", "PE.Views.ShapeSettings.textStretch": "Estendi", "PE.Views.ShapeSettings.textStyle": "Stile", "PE.Views.ShapeSettings.textTexture": "Da trama", "PE.Views.ShapeSettings.textTile": "Tela", "PE.Views.ShapeSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON>ta da pacchi", "PE.Views.ShapeSettings.txtCanvas": "Tela", "PE.Views.ShapeSettings.txtCarton": "Cartone", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON> scuro", "PE.Views.ShapeSettings.txtGrain": "Grano", "PE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON>ta grigia", "PE.Views.ShapeSettings.txtKnit": "A maglia", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "Nessuna linea", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "Colonne", "PE.Views.ShapeSettingsAdvanced.strMargins": "Spaziatura interna", "PE.Views.ShapeSettingsAdvanced.textAlt": "Testo alternativo", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrizione", "PE.Views.ShapeSettingsAdvanced.textAltTip": "La rappresentazione testuale alternativa delle informazioni riguardanti l'oggetto visivo, che verrà letta alle persone con deficit visivi o cognitivi per aiutarli a comprendere meglio quali informazioni sono contenute nell'immagine, nella forma, nel grafico o nella tabella.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Adatta", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Dimensioni inizio", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON> inizio", "PE.Views.ShapeSettingsAdvanced.textBevel": "Smussat<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "In basso", "PE.Views.ShapeSettingsAdvanced.textCapType": "Tipo estremità", "PE.Views.ShapeSettingsAdvanced.textCenter": "Centrato", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Numero di colonne", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Dimensione finale", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Stile finale", "PE.Views.ShapeSettingsAdvanced.textFlat": "Uniforme", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Capovolto", "PE.Views.ShapeSettingsAdvanced.textFrom": "Da", "PE.Views.ShapeSettingsAdvanced.textHeight": "Altezza", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Orizzontale", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Orizzontalmente", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo giunzione", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporzioni costanti", "PE.Views.ShapeSettingsAdvanced.textLeft": "A sinistra", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Stile linea", "PE.Views.ShapeSettingsAdvanced.textMiter": "Acuto", "PE.Views.ShapeSettingsAdvanced.textNofit": "Non adattare", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Posizionamento", "PE.Views.ShapeSettingsAdvanced.textPosition": "Posizione", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Ridimensiona forma per adattarla al testo", "PE.Views.ShapeSettingsAdvanced.textRight": "A destra", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotazione", "PE.Views.ShapeSettingsAdvanced.textRound": "Rotondo", "PE.Views.ShapeSettingsAdvanced.textShrink": "<PERSON><PERSON><PERSON><PERSON> il testo in eccesso", "PE.Views.ShapeSettingsAdvanced.textSize": "Dimensione", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Spaziatura fra le colonne", "PE.Views.ShapeSettingsAdvanced.textSquare": "Quadrato", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Casella di testo", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forma - Impostazioni avanzate", "PE.Views.ShapeSettingsAdvanced.textTop": "In alto", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON> in alto a sinistra", "PE.Views.ShapeSettingsAdvanced.textVertical": "Verticale", "PE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Spessori e frecce", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Avviso", "PE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strDetails": "Dettagli firma", "PE.Views.SignatureSettings.strInvalid": "Firme non valide", "PE.Views.SignatureSettings.strSign": "Firma", "PE.Views.SignatureSettings.strSignature": "Firma", "PE.Views.SignatureSettings.strValid": "Firme valide", "PE.Views.SignatureSettings.txtContinueEditing": "Modifica comunque", "PE.Views.SignatureSettings.txtEditWarning": "La modifica rimuoverà le firme dalla presentazione.<br>Vuoi continuare?", "PE.Views.SignatureSettings.txtRemoveWarning": "Vuoi rimuovere questa firma?<br>Non può essere annullata.", "PE.Views.SignatureSettings.txtSigned": "Le firme valide sono state aggiunte alla presentazione. La presentazione è protetta dalla modifica.", "PE.Views.SignatureSettings.txtSignedInvalid": "Alcune delle firme digitali nella presentazione non sono valide o non possono essere verificate. La presentazione è protetta dalla modifica.", "PE.Views.SlideSettings.strBackground": "Colore sfondo", "PE.Views.SlideSettings.strColor": "Colore", "PE.Views.SlideSettings.strDateTime": "Visualizza data e ora", "PE.Views.SlideSettings.strFill": "Sfondo", "PE.Views.SlideSettings.strForeground": "Colore primo piano", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON>", "PE.Views.SlideSettings.strSlideNum": "Mostra numero diapositiva", "PE.Views.SlideSettings.strTransparency": "Opacità", "PE.Views.SlideSettings.textAdvanced": "Mostra impostazioni avanzate", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Colore di riempimento", "PE.Views.SlideSettings.textDirection": "Direzione", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textFromFile": "Da file", "PE.Views.SlideSettings.textFromStorage": "Da spazio di archiviazione", "PE.Views.SlideSettings.textFromUrl": "Da URL", "PE.Views.SlideSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textGradientFill": "Riempimento sfumato", "PE.Views.SlideSettings.textImageTexture": "Immagine o trama", "PE.Views.SlideSettings.textLinear": "Lineare", "PE.Views.SlideSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON>", "PE.Views.SlideSettings.textPosition": "Posizione", "PE.Views.SlideSettings.textRadial": "Radiale", "PE.Views.SlideSettings.textReset": "Reimposta modifiche", "PE.Views.SlideSettings.textSelectImage": "Seleziona immagine", "PE.Views.SlideSettings.textSelectTexture": "Seleziona", "PE.Views.SlideSettings.textStretch": "Estendi", "PE.Views.SlideSettings.textStyle": "Stile", "PE.Views.SlideSettings.textTexture": "Da trama", "PE.Views.SlideSettings.textTile": "Tela", "PE.Views.SlideSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "PE.Views.SlideSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON>ta da pacchi", "PE.Views.SlideSettings.txtCanvas": "Tela", "PE.Views.SlideSettings.txtCarton": "Cartone", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON> scuro", "PE.Views.SlideSettings.txtGrain": "Grano", "PE.Views.SlideSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON>ta grigia", "PE.Views.SlideSettings.txtKnit": "A maglia", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "<PERSON><PERSON>", "PE.Views.SlideshowSettings.textLoop": "<PERSON><PERSON><PERSON> continuo fino a quando non viene premuto 'Esc'", "PE.Views.SlideshowSettings.textTitle": "Mostra Impostazioni", "PE.Views.SlideSizeSettings.strLandscape": "Orizzontale", "PE.Views.SlideSizeSettings.strPortrait": "Verticale", "PE.Views.SlideSizeSettings.textHeight": "Altezza", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientamento diapositiva", "PE.Views.SlideSizeSettings.textSlideSize": "Dimensione diapositiva", "PE.Views.SlideSizeSettings.textTitle": "Impostazioni dimensione diapositiva", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Diapositive 35 mm", "PE.Views.SlideSizeSettings.txtA3": "Carta A3(297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Carta A4(210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Carta B4 (ICO)(250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "Carta B5 (ICO)(176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtLedger": "Ledger Paper (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Carta lettera (8,5x11 pollici)", "PE.Views.SlideSizeSettings.txtOverhead": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Schermo intero", "PE.Views.Statusbar.goToPageText": "Vai alla diapositiva", "PE.Views.Statusbar.pageIndexText": "Diapositiva {0} di {1}", "PE.Views.Statusbar.textShowBegin": "<PERSON>ra dall'inizio", "PE.Views.Statusbar.textShowCurrent": "Mostra dalla diapositiva corrente", "PE.Views.Statusbar.textShowPresenterView": "Mostra la visualizzazione del relatore", "PE.Views.Statusbar.tipAccessRights": "Gestisci i diritti di accesso al documento", "PE.Views.Statusbar.tipFitPage": "<PERSON>tta alla diapositiva", "PE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON> alla larghezza", "PE.Views.Statusbar.tipPreview": "Avvia presentazione", "PE.Views.Statusbar.tipSetLang": "Imposta lingua del testo", "PE.Views.Statusbar.tipZoomFactor": "Ingrandimento", "PE.Views.Statusbar.tipZoomIn": "Zoom avanti", "PE.Views.Statusbar.tipZoomOut": "Zoom indietro", "PE.Views.Statusbar.txtPageNumInvalid": "Numero diapositiva non corretto", "PE.Views.TableSettings.deleteColumnText": "Elimina colonna", "PE.Views.TableSettings.deleteRowText": "Elimina riga", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON> tabella", "PE.Views.TableSettings.insertColumnLeftText": "Inserisci colonna a sinistra", "PE.Views.TableSettings.insertColumnRightText": "Inserisci colonna a destra", "PE.Views.TableSettings.insertRowAboveText": "Inserisci riga sopra", "PE.Views.TableSettings.insertRowBelowText": "Inser<PERSON>ci riga sotto", "PE.Views.TableSettings.mergeCellsText": "Unisci celle", "PE.Views.TableSettings.selectCellText": "Seleziona cella", "PE.Views.TableSettings.selectColumnText": "Seleziona colonna", "PE.Views.TableSettings.selectRowText": "Seleziona riga", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabella", "PE.Views.TableSettings.splitCellsText": "Dividi cella...", "PE.Views.TableSettings.splitCellTitleText": "Dividi cella", "PE.Views.TableSettings.textAdvanced": "Mostra impostazioni avanzate", "PE.Views.TableSettings.textBackColor": "Colore sfondo", "PE.Views.TableSettings.textBanded": "Altera", "PE.Views.TableSettings.textBorderColor": "Colore", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON> bordo", "PE.Views.TableSettings.textCellSize": "Dimensioni cella", "PE.Views.TableSettings.textColumns": "Colonne", "PE.Views.TableSettings.textDistributeCols": "Distribuisci colonne", "PE.Views.TableSettings.textDistributeRows": "Distribuisci righe", "PE.Views.TableSettings.textEdit": "Righe e colonne", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textFirst": "Prima", "PE.Views.TableSettings.textHeader": "Intestazione", "PE.Views.TableSettings.textHeight": "Altezza", "PE.Views.TableSettings.textLast": "Ultima", "PE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Seleziona i bordi che desideri modificare applicando lo stile scelto sopra", "PE.Views.TableSettings.textTemplate": "Seleziona da modello", "PE.Views.TableSettings.textTotal": "Totale", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Imposta bordo esterno e tutte le linee interne", "PE.Views.TableSettings.tipBottom": "Imposta solo bordo esterno inferiore", "PE.Views.TableSettings.tipInner": "<PERSON><PERSON><PERSON> solo linee interne", "PE.Views.TableSettings.tipInnerHor": "Imposta solo linee interne orizzontali", "PE.Views.TableSettings.tipInnerVert": "<PERSON>mpo<PERSON> solo linee interne verticali", "PE.Views.TableSettings.tipLeft": "Imposta solo bordo esterno sinistro", "PE.Views.TableSettings.tipNone": "Non impostare bordi", "PE.Views.TableSettings.tipOuter": "Imposta solo il bordo esterno", "PE.Views.TableSettings.tipRight": "Impo<PERSON> solo bordo esterno destro", "PE.Views.TableSettings.tipTop": "Imposta solo bordo esterno superiore", "PE.Views.TableSettings.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Light": "Chiaro", "PE.Views.TableSettings.txtGroupTable_Medium": "Medio", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "PE.Views.TableSettings.txtTable_Accent": "Accento", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_MediumStyle": "Stile Medio", "PE.Views.TableSettings.txtTable_NoGrid": "Nessuna griglia", "PE.Views.TableSettings.txtTable_NoStyle": "<PERSON><PERSON><PERSON> stile", "PE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON> griglia", "PE.Views.TableSettings.txtTable_ThemedStyle": "Stile a tema", "PE.Views.TableSettingsAdvanced.textAlt": "Testo alternativo", "PE.Views.TableSettingsAdvanced.textAltDescription": "Descrizione", "PE.Views.TableSettingsAdvanced.textAltTip": "La rappresentazione testuale alternativa delle informazioni riguardanti l'oggetto visivo, che verrà letta alle persone con deficit visivi o cognitivi per aiutarli a comprendere meglio quali informazioni sono contenute nell'immagine, nella forma, nel grafico o nella tabella.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "In basso", "PE.Views.TableSettingsAdvanced.textCenter": "Centrato", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Utilizza margini predefiniti", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Margini predefiniti", "PE.Views.TableSettingsAdvanced.textFrom": "Da", "PE.Views.TableSettingsAdvanced.textHeight": "Altezza", "PE.Views.TableSettingsAdvanced.textHorizontal": "Orizzontale", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Proporzioni costanti", "PE.Views.TableSettingsAdvanced.textLeft": "A sinistra", "PE.Views.TableSettingsAdvanced.textMargins": "Margini cella", "PE.Views.TableSettingsAdvanced.textPlacement": "Posizionamento", "PE.Views.TableSettingsAdvanced.textPosition": "Posizione", "PE.Views.TableSettingsAdvanced.textRight": "A destra", "PE.Views.TableSettingsAdvanced.textSize": "Dimensione", "PE.Views.TableSettingsAdvanced.textTitle": "Tabella - Impostazioni avanzate", "PE.Views.TableSettingsAdvanced.textTop": "In alto", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON> in alto a sinistra", "PE.Views.TableSettingsAdvanced.textVertical": "Verticale", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "Colore sfondo", "PE.Views.TextArtSettings.strColor": "Colore", "PE.Views.TextArtSettings.strFill": "Riempimento", "PE.Views.TextArtSettings.strForeground": "Colore primo piano", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strSize": "Dimensione", "PE.Views.TextArtSettings.strStroke": "Linea", "PE.Views.TextArtSettings.strTransparency": "Opacità", "PE.Views.TextArtSettings.strType": "Tipo", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 0 pt e 1584 pt.", "PE.Views.TextArtSettings.textColor": "Colore di riempimento", "PE.Views.TextArtSettings.textDirection": "Direction", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textFromFile": "Da file", "PE.Views.TextArtSettings.textFromUrl": "Da URL", "PE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textGradientFill": "Riempimento sfumato", "PE.Views.TextArtSettings.textImageTexture": "Immagine o trama", "PE.Views.TextArtSettings.textLinear": "Lineare", "PE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textPosition": "Posizione", "PE.Views.TextArtSettings.textRadial": "Radiale", "PE.Views.TextArtSettings.textSelectTexture": "Seleziona", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "Stile", "PE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTexture": "Da trama", "PE.Views.TextArtSettings.textTile": "Tile", "PE.Views.TextArtSettings.textTransform": "Trasformazione", "PE.Views.TextArtSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON>ta da pacchi", "PE.Views.TextArtSettings.txtCanvas": "Tela", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON> scuro", "PE.Views.TextArtSettings.txtGrain": "Grano", "PE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON>ta grigia", "PE.Views.TextArtSettings.txtKnit": "A maglia", "PE.Views.TextArtSettings.txtLeather": "Leather", "PE.Views.TextArtSettings.txtNoBorders": "Nessuna linea", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "PE.Views.Toolbar.capAddSlide": "Aggiungi diapositiva", "PE.Views.Toolbar.capBtnAddComment": "Aggiungi commento", "PE.Views.Toolbar.capBtnComment": "Commento", "PE.Views.Toolbar.capBtnDateTime": "Data e ora", "PE.Views.Toolbar.capBtnInsHeader": "Piè di pagina", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Simbolo", "PE.Views.Toolbar.capBtnSlideNum": "Numero diapositiva", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Grafico", "PE.Views.Toolbar.capInsertEquation": "Equazione", "PE.Views.Toolbar.capInsertHyperlink": "Collegamento ipertestuale", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Casella di testo", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "File", "PE.Views.Toolbar.capTabHome": "Home", "PE.Views.Toolbar.capTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON> in maiuscolo ogni parola", "PE.Views.Toolbar.mniCustomTable": "Inserisci tabella personalizzata", "PE.Views.Toolbar.mniImageFromFile": "Imma<PERSON>e da file", "PE.Views.Toolbar.mniImageFromStorage": "Immagine dallo spazio di archiviazione", "PE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON><PERSON> da URL", "PE.Views.Toolbar.mniInsertSSE": "Inserire foglio di calcolo", "PE.Views.Toolbar.mniLowerCase": "minuscolo", "PE.Views.Toolbar.mniSentenceCase": "Sentenza della frase", "PE.Views.Toolbar.mniSlideAdvanced": "Impostazioni avanzate", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.mniToggleCase": "mAIUSCOLO mINUSCOLO", "PE.Views.Toolbar.mniUpperCase": "MAIUSCOLO", "PE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textAlignBottom": "All<PERSON>a testo in basso", "PE.Views.Toolbar.textAlignCenter": "Centra testo", "PE.Views.Toolbar.textAlignJust": "Giustifica", "PE.Views.Toolbar.textAlignLeft": "Allinea testo a sinistra", "PE.Views.Toolbar.textAlignMiddle": "Allinea testo in mezzo", "PE.Views.Toolbar.textAlignRight": "Allinea testo a destra", "PE.Views.Toolbar.textAlignTop": "All<PERSON>a testo in alto", "PE.Views.Toolbar.textArrangeBack": "<PERSON><PERSON><PERSON> in secondo piano", "PE.Views.Toolbar.textArrangeBackward": "Porta indietro", "PE.Views.Toolbar.textArrangeForward": "Porta avanti", "PE.Views.Toolbar.textArrangeFront": "<PERSON>a in primo piano", "PE.Views.Toolbar.textBold": "Grassetto", "PE.Views.Toolbar.textColumnsCustom": "Colonne <PERSON>", "PE.Views.Toolbar.textColumnsOne": "Una colonna", "PE.Views.Toolbar.textColumnsThree": "<PERSON>re colonne", "PE.Views.Toolbar.textColumnsTwo": "Due colonne", "PE.Views.Toolbar.textItalic": "Corsivo", "PE.Views.Toolbar.textListSettings": "Impostazioni elenco", "PE.Views.Toolbar.textRecentlyUsed": "Usati di recente", "PE.Views.Toolbar.textShapeAlignBottom": "<PERSON><PERSON>a in basso", "PE.Views.Toolbar.textShapeAlignCenter": "Allinea al centro", "PE.Views.Toolbar.textShapeAlignLeft": "Allinea a sinistra", "PE.Views.Toolbar.textShapeAlignMiddle": "Allinea in mezzo", "PE.Views.Toolbar.textShapeAlignRight": "Allinea a destra", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON>a in alto", "PE.Views.Toolbar.textShowBegin": "<PERSON>ra dall'inizio", "PE.Views.Toolbar.textShowCurrent": "Mostra dalla diapositiva corrente", "PE.Views.Toolbar.textShowPresenterView": "Mostra la visualizzazione del relatore", "PE.Views.Toolbar.textShowSettings": "Mostra Impostazioni", "PE.Views.Toolbar.textStrikeout": "Barrato", "PE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSuperscript": "Apice", "PE.Views.Toolbar.textTabAnimation": "Animazione", "PE.Views.Toolbar.textTabCollaboration": "Collaborazione", "PE.Views.Toolbar.textTabFile": "File", "PE.Views.Toolbar.textTabHome": "Home", "PE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabProtect": "Protezione", "PE.Views.Toolbar.textTabTransitions": "Transizioni", "PE.Views.Toolbar.textTabView": "Visualizza", "PE.Views.Toolbar.textTitleError": "Errore", "PE.Views.Toolbar.textUnderline": "Sottolineato", "PE.Views.Toolbar.tipAddSlide": "Aggiungi diapositiva", "PE.Views.Toolbar.tipBack": "Indietro", "PE.Views.Toolbar.tipChangeCase": "Cambia caso", "PE.Views.Toolbar.tipChangeChart": "Cambia tipo di grafico", "PE.Views.Toolbar.tipChangeSlide": "Cambia layout diapositiva", "PE.Views.Toolbar.tipClearStyle": "Cancella stile", "PE.Views.Toolbar.tipColorSchemas": "Cambia combinazione colori", "PE.Views.Toolbar.tipColumns": "Inserisci colonne", "PE.Views.Toolbar.tipCopy": "Copia", "PE.Views.Toolbar.tipCopyStyle": "Copia stile", "PE.Views.Toolbar.tipCut": "Tagliare", "PE.Views.Toolbar.tipDateTime": "Inserisci data e ora correnti", "PE.Views.Toolbar.tipDecFont": "Riduci dimensione caratteri", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.Toolbar.tipEditHeader": "Modifica piè di pagina", "PE.Views.Toolbar.tipFontColor": "Colore caratteri", "PE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipFontSize": "Dimensione carattere", "PE.Views.Toolbar.tipHAligh": "Allineamento orizzontale", "PE.Views.Toolbar.tipHighlightColor": "Evidenziatore con colore", "PE.Views.Toolbar.tipIncFont": "Aumenta dimensione caratteri ", "PE.Views.Toolbar.tipIncPrLeft": "Aumenta rientro", "PE.Views.Toolbar.tipInsertAudio": "Inserisci audio", "PE.Views.Toolbar.tipInsertChart": "Inserisci grafico", "PE.Views.Toolbar.tipInsertEquation": "Inserisci equazione", "PE.Views.Toolbar.tipInsertHyperlink": "Aggiungi collegamento ipertestuale", "PE.Views.Toolbar.tipInsertImage": "Inserisci immagine", "PE.Views.Toolbar.tipInsertShape": "Inserisci forma", "PE.Views.Toolbar.tipInsertSymbol": "Inserisci simbolo", "PE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON><PERSON> tabella", "PE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON> casella di testo", "PE.Views.Toolbar.tipInsertTextArt": "Inserisci Text Art", "PE.Views.Toolbar.tipInsertVideo": "Inserisci video", "PE.Views.Toolbar.tipLineSpace": "Interlinea", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON> puntati", "PE.Views.Toolbar.tipMarkersArrow": "Punti elenco a freccia", "PE.Views.Toolbar.tipMarkersCheckmark": "Punti elenco a segno di spunta", "PE.Views.Toolbar.tipMarkersDash": "Punti elenco a trattino", "PE.Views.Toolbar.tipMarkersFRhombus": "Punti elenco a rombo pieno", "PE.Views.Toolbar.tipMarkersFRound": "Punti elenco rotondi pieni", "PE.Views.Toolbar.tipMarkersFSquare": "Punti elenco quadrati pieni", "PE.Views.Toolbar.tipMarkersHRound": "Punti elenco rotondi vuoti", "PE.Views.Toolbar.tipMarkersStar": "Punti elenco a stella", "PE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "<PERSON><PERSON><PERSON> numerati", "PE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPreview": "Avvia presentazione", "PE.Views.Toolbar.tipPrint": "Stampa", "PE.Views.Toolbar.tipRedo": "R<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "Salva i tuoi cambiamenti per renderli disponibili agli altri utenti.", "PE.Views.Toolbar.tipSelectAll": "Se<PERSON><PERSON><PERSON><PERSON> tutto", "PE.Views.Toolbar.tipShapeAlign": "Allinea forma", "PE.Views.Toolbar.tipShapeArrange": "Disponi forma", "PE.Views.Toolbar.tipSlideNum": "Inserisci numero diapositiva", "PE.Views.Toolbar.tipSlideSize": "Seleziona dimensione diapositiva", "PE.Views.Toolbar.tipSlideTheme": "<PERSON><PERSON> diapositiva", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Allineamento verticale", "PE.Views.Toolbar.tipViewSettings": "Mostra impostazioni", "PE.Views.Toolbar.txtDistribHor": "Distribuisci orizzontalmente", "PE.Views.Toolbar.txtDistribVert": "Distribuisci verticalmente", "PE.Views.Toolbar.txtDuplicateSlide": "Duplica diapositiva", "PE.Views.Toolbar.txtGroup": "Raggruppa", "PE.Views.Toolbar.txtObjectsAlign": "Allinea oggetti selezionati", "PE.Views.Toolbar.txtScheme1": "Ufficio", "PE.Views.Toolbar.txtScheme10": "Luna", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Satellite", "PE.Views.Toolbar.txtScheme16": "Carta", "PE.Views.Toolbar.txtScheme17": "Solstizio", "PE.Views.Toolbar.txtScheme18": "Tecnologia", "PE.Views.Toolbar.txtScheme19": "Terra", "PE.Views.Toolbar.txtScheme2": "Scala di grigi", "PE.Views.Toolbar.txtScheme20": "Tramonto", "PE.Views.Toolbar.txtScheme21": "Verve", "PE.Views.Toolbar.txtScheme22": "Nuovo ufficio", "PE.Views.Toolbar.txtScheme3": "Apice", "PE.Views.Toolbar.txtScheme4": "Aspetto", "PE.Views.Toolbar.txtScheme5": "Città", "PE.Views.Toolbar.txtScheme6": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme7": "Azionario", "PE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme9": "Galassia", "PE.Views.Toolbar.txtSlideAlign": "Allinea alla diapositiva", "PE.Views.Toolbar.txtUngroup": "Separa", "PE.Views.Transitions.strDelay": "<PERSON><PERSON>", "PE.Views.Transitions.strDuration": "<PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "Inizia al clic del mouse", "PE.Views.Transitions.textBlack": "Attraverso il nero", "PE.Views.Transitions.textBottom": "In basso", "PE.Views.Transitions.textBottomLeft": "In basso a sinistra", "PE.Views.Transitions.textBottomRight": "In basso a destra", "PE.Views.Transitions.textClock": "Orologio", "PE.Views.Transitions.textClockwise": "In senso orario", "PE.Views.Transitions.textCounterclockwise": "In senso antiorario", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "Dissolvenza", "PE.Views.Transitions.textHorizontalIn": "Orizzontale dentro", "PE.Views.Transitions.textHorizontalOut": "Orizzontale fuori", "PE.Views.Transitions.textLeft": "Sinistra", "PE.Views.Transitions.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textPush": "Spinta", "PE.Views.Transitions.textRight": "Destra", "PE.Views.Transitions.textSmoothly": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSplit": "Dividere", "PE.Views.Transitions.textTop": "In alto", "PE.Views.Transitions.textTopLeft": "In alto a sinistra", "PE.Views.Transitions.textTopRight": "In alto a destra", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "Verticale dentro", "PE.Views.Transitions.textVerticalOut": "Verticale fuori", "PE.Views.Transitions.textWedge": "Cuneo", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "Zoomare", "PE.Views.Transitions.textZoomIn": "Ingrandire", "PE.Views.Transitions.textZoomOut": "Rimpic<PERSON>lire", "PE.Views.Transitions.textZoomRotate": "Zoomare e Ruotare", "PE.Views.Transitions.txtApplyToAll": "Applicare a tutte le diapositive", "PE.Views.Transitions.txtParameters": "Parametri", "PE.Views.Transitions.txtPreview": "Anteprima", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textAlwaysShowToolbar": "Mostra sempre barra degli strumenti ", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textFitToSlide": "<PERSON>tta alla diapositiva", "PE.Views.ViewTab.textFitToWidth": "<PERSON><PERSON> alla larghezza", "PE.Views.ViewTab.textInterfaceTheme": "Tema dell'interfaccia", "PE.Views.ViewTab.textNotes": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textStatusBar": "Barra di stato", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "<PERSON>tta alla diapositiva", "PE.Views.ViewTab.tipFitToWidth": "<PERSON><PERSON> alla larghezza", "PE.Views.ViewTab.tipInterfaceTheme": "Tema dell'interfaccia"}