﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insérer et mettre en forme des formes automatiques</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insérer les formes automatiques et modifier ses paramètres." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Insérer et mettre en forme des formes automatiques</h1>
            <h3>Insérer une forme automatique</h3>
            <p>Pour <b>ajouter</b> une forme automatique à une diapositive dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Présentations</b></a>,</p>
            <ol>
                <li>sélectionnez la diapositive à laquelle vous voulez ajouter une forme automatique dans la liste des diapositives à gauche.</li>
                <li>
                    cliquez sur l'icône <div class = "icon icon-insertautoshape"></div> <b>Forme</b> dans l'onglet <b>Accueil</b> ou sur la flèche déroulante de la <div class = "big big-shapegallery"></div> <b>Galerie de formes</b> dans l'onglet <b>Insertion</b> de la barre d'outils supérieure,
                </li>
                <li>sélectionnez l'un des groupes des formes automatiques disponibles dans la <b>Galerie des formes</b> : <em>Récemment utilisé</em>, <em>Formes de base</em>, <em>Flèches figurées</em>, <em>Maths</em>, <em>Graphiques</em>, <em>Étoiles et rubans</em>, <em>Légendes</em>, <em>Boutons</em>, <em>Rectangles</em>, <em>Lignes</em>,</li>
                <li>cliquez sur la forme automatique voulue du groupe sélectionné,</li>
                <li>
                    dans la zone d'édition de la diapositive, placez le curseur de la souris là où vous voulez insérer la forme,
                    <p class="note"><b>Remarque</b> : vous pouvez cliquer et faire glisser pour étirer la forme.</p>
                </li>
                <li>
                    après avoir ajouté la forme automatique vous pouvez modifier <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">sa taille, sa position</a> et ses propriétés.
                    <p class="note"><b>Remarque</b> : pour ajouter une légende à la forme, assurez-vous que la forme est sélectionnée et commencez à taper le texte. Le texte que vous ajoutez fait partie de la forme (ainsi si vous déplacez ou faites pivoter la forme, le texte change de position lui aussi).
                </li>
            </ol>
            <p>Il est possible d'ajouter une forme automatique à la disposition d'une diapositive. Pour en savoir plus, veuillez consulter <a href="../UsageInstructions/SetSlideParameters.htm#addtolayout" onclick="onhyperlinkclick(this)">cet article</a>.</p>
            <hr />
            <h3>Modifier les paramètres de la forme automatique</h3>
            <p>Certains paramètres de la forme automatique peuvent être modifiés en utilisant l'onglet <b>Paramètres de la forme</b> de la barre latérale droite. Pour l'activer, sélectionnez la forme ajoutée avec la souris et sélectionnez l'icône <b>Paramètres de la forme</b> <span class="icon icon-shape_settings_icon"></span> à droite. Vous pouvez y modifier les paramètres suivants :</p>
            <p><img class="floatleft" alt="Onglet Paramètres de la forme" src="../images/shapesettingstab.png" /></p>
            <ul style="margin-left: 280px;">
            <li><b>Remplissage</b> - utilisez cette section pour sélectionner le remplissage de la forme automatique. Les options disponibles sont les suivantes :
            <ul>
                <li><b>Couleur de remplissage</b> - sélectionnez cette option pour spécifier la couleur unie à appliquer aux diapositives sélectionnées.</li>
                <li><b>Remplissage en dégradé</b> - sélectionnez cette option pour spécifier deux couleurs et remplir la forme avec une transition douce entre elles.</li>
                <li><b>Image ou texture</b> - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que arrière-plan de la forme.</li>
                <li><b>Modèle</b> - sélectionnez cette option pour choisir un modèle à deux couleurs composé d'éléments répétés.</li>
                <li><b>Pas de remplissage</b> - sélectionnez cette option si vous ne voulez pas utiliser un remplissage.</li>
            </ul>
            <p>Pour en savoir plus consultez le chapitre <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">Remplir les objets et sélectionner les couleurs</a>.</p>
            </li>
            <li id="shapestroke"><b>Ligne</b> - utilisez cette section pour changer la largeur et la couleur du ligne de la forme automatique.
                <ul>
                <li>Pour modifier la <b>largeur</b> du contour, sélectionnez une des options disponibles depuis la liste déroulante <b>Taille</b>. Les options disponibles sont les suivantes : 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt ou <b>Pas de ligne</b> si vous ne voulez pas de contour.</li>
                <li>Pour changer la <b>couleur</b> du contour, cliquez sur la case colorée et <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">sélectionnez la couleur voulue</a>. Vous pouvez utiliser la <b>couleur de thème</b> sélectionnée, une <b>couleur standard</b> ou choisir une <b>couleur personnalisée</b>.</li>
                <li>Pour modifier le <b>type</b> de contour, sélectionnez l'option voulue dans la liste déroulante correspondante (une ligne continue est appliquée par défaut, vous pouvez la remplacer par l'une des lignes pointillées disponibles).</li>
                </ul>
            </li>
                <li>
                    <b>Rotation</b> permet de faire pivoter la forme de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner la forme horizontalement ou verticalement. Cliquez sur l'un des boutons :
                    <ul>
                        <li><div class = "icon icon-rotatecounterclockwise"></div> pour faire pivoter la forme de 90 degrés dans le sens inverse des aiguilles d'une montre</li>
                        <li><div class = "icon icon-rotateclockwise"></div> pour faire pivoter la forme de 90 degrés dans le sens des aiguilles d'une montre</li>
                        <li><div class = "icon icon-fliplefttoright"></div> pour retourner la forme horizontalement (de gauche à droite)</li>
                        <li><div class = "icon icon-flipupsidedown"></div> pour retourner la forme verticalement (à l'envers)</li>
                    </ul>
                </li>
                <li><b>Modifier la forme</b> - utilisez cette section pour remplacer la forme automatique insérée par une autre sélectionnée de la liste déroulante.</li>
                <li><b>Ajouter une ombre</b> - cochez cette case pour affichage de la forme ombré.</li>
            </ul>
            <hr />
            <p>Pour changer les <b>paramètres avancés</b> de la forme automatique, cliquez sur la forme avec le bouton droit et sélectionnez l'option <b>Paramètres avancés</b> dans le menu contextuel ou cliquez avec le bouton gauche et utilisez le lien <b>Afficher paramètres avancés</b> sur la barre latérale droite. La fenêtre Propriétés de la forme s'ouvre :</p>
            <p><img alt="Paramètres de la forme - onglet Taille" src="../images/shape_properties1.png" /></p>
            <p>L'onglet <b>Emplacement</b> vous permet de modifier la <b>Largeur</b> et/ou <b>Hauteur</b> de la forme automatique. Si le bouton <b>Proportions constantes</b> <span class="icon icon-constantproportions"></span> est activé (auquel cas il ressemble à ceci <span class="icon icon-constantproportionsactivated"></span>), la largeur et la hauteur seront changées en même temps et le ratio d'aspect de la forme automatique originale sera préservé.</p>
            <p>Vous pouvez également saisir la position exacte en utilisant les champs <b>Horizontalement</b> et <b>Verticalement</b> ainsi que le champ <b>De</b> dans lesquels vous pouvez accéder aux paramètres tels que <b>Coin supérieur gauche</b> et <b>Au centre</b>.</p>
            <p><img alt="Forme - Paramètres avancés" src="../images/shape_properties5.png" /></p>
            <p>L'onglet <b>Rotation</b> comporte les paramètres suivants :</p>
            <ul>
                <li><b>Angle</b> - utilisez cette option pour faire pivoter la forme d'un angle exactement spécifié. Entrez la valeur souhaitée mesurée en degrés dans le champ ou réglez-la à l'aide des flèches situées à droite.</li>
                <li><b>Retourné</b> - cochez la case <b>Horizontalement</b> pour retourner la forme horizontalement (de gauche à droite) ou la case <b>Verticalement</b> pour retourner la forme verticalement (à l'envers).</li>
            </ul>
            <p><img alt="Paramètres de la forme - onglet Poids et flèches" src="../images/shape_properties.png" /></p>
            <p>L'onglet <b>Poids et flèches</b> contient les paramètres suivants :</p>
            <ul>
                <li><b>Style de ligne</b> - ce groupe d'options vous permet de spécifier les paramètres suivants :
                <ul>
                <li><b>Type de lettrine</b> - cette option permet de définir le style de la fin de la ligne, ainsi elle peut être appliquée seulement aux formes avec un contour ouvert telles que des lignes, des polylignes etc. :
                    <ul>
                    <li><b>Plat</b> - les points finaux seront plats.</li>
                    <li><b>Arrondi</b> - les points finaux seront arrondis.</li>
                    <li><b>Carré</b> - les points finaux seront carrés.</li>
                    </ul>
                </li>
                <li><b>Type de jointure</b> - cette option permet de définir le style de l'intersection de deux lignes, par exemple, une polyligne, les coins du triangle ou le contour du rectangle :
                    <ul>
                    <li><b>Arrondi</b> - le coin sera arrondi.</li>
                    <li><b>Plaque</b> - le coin sera coupé d'une manière angulaire.</li>
                    <li><b>Onglet</b> - l'angle sera aiguisé. Bien adapté pour les formes à angles vifs.</li>
                    </ul>
                    <p class="note"><b>Remarque</b> : l'effet sera plus visible si vous utilisez un contour plus épais.</p>
                </li>
                </ul>
                </li>
                <li><b>Flèches</b> - ce groupe d'options est disponible pour les formes du groupe <b>Lignes</b>. Il permet de définir le <b>Style de début</b> et <b>Style de fin</b> aussi bien que la <b>Taille</b> des flèches en sélectionnant l'option appropriée des listes déroulantes.</li>
            </ul>
            <p id="internalmargins"><img alt="Paramètres de la forme - onglet Marge de texte" src="../images/shape_properties3.png" /></p>
            <p>L'onglet <b>Zone de texte</b> contient les paramètres suivants :
            <ul>
                <li><b>Ajuster automatiquement</b> - pour modifier la façon dont le texte est affiché à l'intérieur de la forme : <b>Ne pas ajuster automatiquement</b>, <b>Rétrécir le texte dans la zone de débordement uniquement</b>, <b>Redimensionner la forme pour ajuster le texte</b>.</li>
                <li>L'onglet <b>Marges intérieures</b> - afin de changer les marges internes <b>En haut</b>, <b>En bas</b>, <b>A gauche</b> et <b>A droite</b> de la forme automatique (c'est-à-dire la distance entre le texte à l'intérieur de la forme et les bordures de la forme automatique).</li>
                </ul>
            </p>
            <p class="note"><b>Remarque</b> : cet onglet n'est disponible que si tu texte est ajouté dans la forme automatique, sinon l'onglet est désactivé.</p>
            <p id="columns"><img alt="Paramètres de la forme - onglet Colonnes" src="../images/shape_properties2.png" /></p>
            <p>L'onglet <b>Colonnes</b> permet d'ajouter des colonnes de texte dans la forme automatique en spécifiant le <b>Nombre de colonnes</b> nécessaires (jusqu'à 16) et l'<b>Espacement entre les colonnes</b>. Une fois que vous avez cliqué sur <b>OK</b>, le texte qui existe déjà ou tout autre texte que vous entrez dans la forme automatique apparaîtra dans les colonnes et circulera d'une colonne à l'autre.</p>
            <p><img alt="Paramètres de la forme - onglet Texte alternatif" src="../images/shape_properties4.png" /></p>
            <p>L'onglet <b>Texte de remplacement</b> permet de spécifier un <b>Titre</b> et une <b>Description</b> qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du tableau.</p>
            <hr />
            <p>Pour <b>remplacer</b> la forme automatique, cliquez dessus avec le bouton gauche de la souris et utilisez la liste déroulante <b>Modifier la forme automatique</b> dans l'onglet <b>Paramètres de forme</b> de la barre latérale droite.</p>
            <p>Pour <b>supprimer</b> la forme automatique ajoutée, cliquez avec le bouton gauche de la souris et appuyez sur la touche <b>Supprimer</b>.</p>
            <p>Pour apprendre à <b>aligner</b> une forme automatique sur la diapositive ou à <b>organiser</b> plusieurs formes, reportez-vous à la section <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">Aligner et organiser les objets dans une diapositive</a>.</p>
            <hr />
            <h3>Joindre des formes automatiques à l'aide de connecteurs</h3>
            <p>Vous pouvez connecter des formes automatiques à l'aide de lignes munies de points de connexion pour démontrer les dépendances entre les objets (par exemple, si vous souhaitez créer un diagramme). Pour le faire,</p>
            <ol>
                <li>cliquez sur l'icône <div class = "icon icon-insertautoshape"></div> <b>Forme</b> dans l'onglet <b>Accueil</b> ou <b>Insertion</b> de la barre d'outils supérieure,</li>
                <li>
                    sélectionnez le groupe <b>Lignes</b> dans le menu,
                    <p><img alt="Formes - Lignes" src="../images/connectors.png" /></p>
                </li>
                <li>cliquez sur la forme souhaitée dans le groupe sélectionné (à l'exception des trois dernières formes qui ne sont pas des connecteurs, à savoir les formes <em>Courbe</em>, <em>Dessin à main levée</em> et <em>Forme libre</em>),</li>
                <li>
                    passez le curseur de la souris sur la première forme automatique et cliquez sur l'un des points de connexions <div class = "icon icon-connectionpoint"></div> apparaissant sur le contour,
                    <p><span class="big big-connectors_firstshape"></span></p>
                </li>
                <li>
                    faites glisser le curseur de la souris vers la deuxième forme automatique et cliquez sur le point de connexion voulu sur son contour.
                    <p><span class="big big-connectors_secondshape"></span></p>
                </li>
            </ol>
            <p>Si vous déplacez les formes automatiques jointes, le connecteur reste attaché aux formes et se déplace avec elles.</p>
            <p><span class="big big-connectors_moveshape"></span></p>
            <p>Vous pouvez également détacher le connecteur des formes, puis l'attacher à d'autres points de connexion.</p>
        </div>
	</body>
</html>