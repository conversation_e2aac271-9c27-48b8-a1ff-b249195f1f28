﻿<!DOCTYPE html>
<html>
	<head>
		<title>EXP-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>EXP-Funktion</h1>
			<p>Die Funktion <b>EXP</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um die Basis <b>e</b> mit der als Argument angegebenen Zahl zu potenzieren. Die Konstante <b>e</b> hat den Wert <b>2,71828182845904</b>.</p>
			<p>Die Formelsyntax der Funktion <b>EXP</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>EXP(Zahl)</em></b></p> 
			<p><b><em>Zahl</em></b> ist der Exponent zur Basis <b>e</b>, ein nummerischer Wert, der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>EXP</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>EXP</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="EXP-Funktion" src="../images/exp.png" /></p>
		</div>
	</body>
</html>