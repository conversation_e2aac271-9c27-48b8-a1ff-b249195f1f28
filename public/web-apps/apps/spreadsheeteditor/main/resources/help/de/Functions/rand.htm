﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZUFALLSZAHL-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ZUFALLSZAHL-Funktion</h1>
			<p>Die Funktion <b>ZUFALLSZAHL</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Diese Funktion gibt eine gleichmäßig verteilte reelle Zufallszahl zurück, die größer oder gleich <b>0</b> und kleiner als <b>1</b> ist. Die Syntax der Funktion ZUFALLSZAHL erfordert <b>keine</b> Argumente.</p>
			<p>Die Syntax der Funktion <b>ZUFALLSZAHL</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ZUFALLSZAHL()</em></b></p> 
			<p>Anwendung der Funktion <b>ZUFALLSZAHL</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ZUFALLSZAHL</b>.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ZUFALLSZAHL-Funktion" src="../images/rand.png" /></p>
		</div>
	</body>
</html>