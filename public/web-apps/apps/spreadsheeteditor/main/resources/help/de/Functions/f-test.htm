﻿<!DOCTYPE html>
<html>
	<head>
		<title>F.TEST-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>F.TEST-Funktion</h1>
			<p>Die Funktion <b>F.TEST</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt die Teststatistik eines F-Tests zurück, die zweiseitige Wahrscheinlichkeit, dass sich die Varianzen in <b><em>Matrix1</em></b> und <b><em>Matrix2</em></b> nicht signifikant unterscheiden. Mit dieser Funktion können Sie feststellen, ob zwei Stichproben unterschiedliche Varianzen haben.</p>
			<p>Formelsyntax der Funktion <b>F.TEST</b>:</p> 
			<p style="text-indent: 150px;"><b><em>F.TEST(Matrix1;Matrix2)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Matrix1</em></b> ist die erste Matrix oder der erste Wertebereich.</p>
			<p style="text-indent: 50px;"><b><em>Matrix2</em></b> ist die zweite Matrix oder der zweite Wertebereich.</p>
			<p>Die Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen. Text, Wahrheitswerte oder leere Zellen werden ignoriert. Zellen, die den Wert 0 enthalten, werden dagegen berücksichtigt. Enthält eines der Argumente weniger als 2 Datenpunkte, oder ist die Varianz eines Wertebereichs gleich 0, gibt die Funktion den Fehlerwert #DIV/0! zurück.</p>
 			<p>Anwendung der Funktion <b>F.TEST</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>F.TEST</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="F.TEST-Funktion" src="../images/f-test.png" /></p>
		</div>
	</body>
</html>