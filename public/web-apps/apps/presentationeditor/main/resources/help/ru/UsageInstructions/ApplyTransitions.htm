<!DOCTYPE html>
<html>
	<head>
		<title>Применение переходов</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавляйте эффекты анимации между слайдами" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Применение переходов</h1>
			<p><b>Переход</b> - это эффект, который появляется между двумя слайдами при смене одного слайда другим во время показа. В <a target="_blank" href="https://www.onlyoffice.com/ru/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Редакторе презентаций</b></a> можно применить один и тот же переход ко всем слайдам или разные переходы к каждому отдельному слайду и настроить свойства перехода.</p>
			<p><b>Для применения перехода к отдельному слайду</b> или нескольким выделенным слайдам:</p>
			<ol>
				<li>Откройте вкладку <b>Переходы</b> на верхней панели инструментов.
				<p><img alt="Вкладка Переходы" src="../images/interface/transitionstab.png" /></p>
				</li>
				<li>Выберите слайд (или несколько слайдов в списке слайдов), к которым вы хотите применить переход.</li>
				<li>Выберите один из доступных эффектов перехода: <b>Нет</b>, <b>Выцветание</b>, <b>Задвигание</b>, <b>Появление</b>, <b>Панорама</b>, <b>Открывание</b>, <b>Наплыв</b>, <b>Часы</b>, <b>Масштабирование</b>.</li>
				<li>Нажмите кнопку <b>Параметры</b>, чтобы выбрать один из доступных вариантов эффекта. Они определяют, как конкретно проявляется эффект. Например, если выбран переход <b>Масштабирование</b>, доступны варианты <b>Увеличение</b>, <b>Уменьшение</b> и <b>Увеличение с поворотом</b>.</li>
				<li>Укажите, как долго должен длиться переход. В поле <b>Длит.</b> (длительность), введите или выберите нужное временное значение, измеряемое в секундах.</li>
				<li>Нажмите кнопку <b>Просмотр</b>, чтобы просмотреть слайд с примененным переходом в области редактирования слайда.</li>
				<li>Укажите, как долго должен отображаться слайд, пока не сменится другим:
				    <ul>
				    <li><b>Запускать щелчком</b> – установите этот флажок, если не требуется ограничивать время отображения выбранного слайда. Слайд будет сменяться другим только при щелчке по нему мышью.</li>
				    <li><b>Задержка</b> – используйте эту опцию, если выбранный слайд должен отображаться в течение заданного времени, пока не сменится другим. Установите этот флажок и введите или выберите нужное временное значение, измеряемое в секундах.
				    <p class="note"><b>Примечание</b>: если установить только флажок <b>Задержка</b>, слайды будут сменяться автоматически через заданный промежуток времени. Если установить и флажок <b>Запускать щелчком</b>, и флажок <b>Задержка</b> и задать время задержки, слайды тоже будут сменяться автоматически, но вы также сможете щелкнуть по слайду, чтобы перейти от него к следующему.</p>
				    </li>
				    </ul>
				</li>
			</ol>
			<p><b>Для применения перехода ко всем слайдам</b> в презентации: выполните все действия, описанные выше, и нажмите на кнопку <b>Применить ко всем слайдам</b>.</p>
			<p><b>Для удаления перехода</b>: выделите нужный слайд и выберите пункт <b>Нет</b> в среди вариантов <b>Эффекта</b> перехода.</p>
			<p><b>Для удаления всех переходов</b>: выделите любой слайд, выберите пункт <b>Нет</b> среди вариантов <b>Эффекта</b> и нажмите на кнопку <b>Применить ко всем слайдам</b>.</p>
		</div>
	</body>
</html>
