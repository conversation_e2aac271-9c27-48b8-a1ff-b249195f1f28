﻿<!DOCTYPE html>
<html>
	<head>
        <title>Folienparameter festlegen</title>
		<meta charset="utf-8" />
		<meta name="description" content="Folienparameter einstellen: Hintergrundfüllung, Themen, Farbschemata, Folienlayouts auswählen" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Folienparameter festlegen</h1>
        <p>Um Ihre Präsentation zu personalisieren im <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a>, können Sie ein Thema und ein Farbschema sowie die Foliengröße und -ausrichtung für die gesamte Präsentation auswählen, Hintergrundfarbe oder Folienlayout für jede einzelne Folie ändern und Übergänge zwischen den Folien hinzufügen. Es ist außerdem möglich Notizen zu jeder Folie einzufüllen, die hilfreich sein können, wenn Sie die Präsentation in der <b>Referentenansicht</b> wiedergeben.</p>
            <ul>
                <li>
                    Mit <b>Themen</b> können Sie das Präsentationsdesign schnell ändern, insbesondere das Aussehen des Folienhintergrunds, vordefinierte Schriftarten für Titel und Texte und das Farbschema, das für die Präsentationselemente verwendet wird.
                    <b>Um ein Thema für die Präsentation auszuwählen</b>, klicken Sie auf das erforderliche vordefinierte Thema aus der Themengalerie rechts in der oberen Symbolleiste auf der Registerkarte <b>Startseite</b>. Das ausgewählte Thema wird auf alle Folien angewendet, wenn Sie nicht zuvor bestimmte Folien zum Anwenden des Themas ausgewählt haben.
                    <p><span class="big big-themes"></span></p>
                    <p>Um das ausgewählte Thema für eine oder mehrere Folien zu ändern, können Sie mit der rechten Maustaste auf die ausgewählten Folien in der Liste links klicken (oder mit der rechten Maustaste auf eine Folie im Bearbeitungsbereich klicken), die Option <b>Thema ändern</b> auswählen aus dem Kontextmenü und wählen Sie das gewünschte Thema.</p>
                </li>
                <li>
                    <b>Farbschema</b> wirkt sich auf die vordefinierten Farben der Präsentationselemente (Schriften, Linien, Füllungen usw.) aus und ermöglichen Ihnen, die Farbkonsistenz während der gesamten Präsentation beizubehalten.
                    <b>Um ein Farbschema zu ändern</b>, klicken Sie auf das Symbol <div class = "icon icon-changecolorscheme"></div> <b>Farbschema ändern</b> auf der Registerkarte <b>Startseite</b> der oberen Symbolleiste und wählen Sie das erforderliche Schema aus der Drop-Down-Liste aus. Das ausgewählte Farbschema wird in der Liste hervorgehoben und auf alle Folien angewendet.
                    <p><img alt="Farbschema" src="../images/colorscheme.png" /></p>
                </li>
                <li>
                    <b>Um die Größe aller Folien in der Präsentation zu ändern</b>, klicken Sie auf <div class = "icon icon-selectslidesizeicon"></div> <b>Foliegröße wählen</b> auf der Registerkarte <b>Startseite</b> der oberen Symbolleiste und wählen Sie die erforderliche Option aus der Drop-Down-Liste aus. Sie können wählen:
                    <ul>
                        <li>eine der beiden Schnellzugriffs-Voreinstellungen - <b>Standard (4:3)</b> oder <b>Breitbildschirm (16:9)</b>,</li>
                        <li>
                            die Option <b>Erweiterte Einstellungen</b>, die das Fenster <b>Einstellungen der Foliengröße</b> öffnet, in dem Sie eine der verfügbaren Voreinstellungen auswählen oder eine <b>benutzerdefinierte</b>-Größe (<b>Breite</b> und <b>Höhe</b>) festlegen können,
                            <p><img alt="Einstellungen der Foliengröße" src="../images/slidesizesettingswindow.png" /></p>
                            <p>die verfügbaren Voreinstellungen sind: Standard (4:3), Breitbild (16:9), Breitbild (16:10), Letter Paper (8.5x11 in), Ledger Blatt (11x17 in), A3 Blatt (297x420 mm), A4 Blatt (210x297 mm), B4 (ICO) Blatt (250x353 mm), B5 (ICO) Blatt (176x250 mm), 35 mm Folien, Overheadfolien, Banner,</p>
                            <p>das Menü <b>Folienausrichtung</b> ermöglicht das Ändern des aktuell ausgewählten Ausrichtungstyps. Der Standardausrichtungstyp ist <b>Querformat</b>, der auf <b>Hochformat</b> umgestellt werden kann.</p>
                        </li>
                    </ul>
                </li>
                <li>
                    <b>Um eine Hintergrundfüllung zu ändern,</b>
                    <ol>
                        <li>wählen Sie in der Folienliste auf der linken Seite die Folien aus, auf die Sie die Füllung anwenden möchten. Oder klicken Sie auf eine beliebige Leerstelle innerhalb der aktuell bearbeiteten Folie im Folienbearbeitungsbereich, um den Fülltyp für diese separate Folie zu ändern.</li>
                        <li>
                            Wählen Sie auf der Registerkarte <b>Folien-Einstellungen</b> der rechten Seitenleiste die erforderliche Option aus:
                                <ul>
                                    <li><b>Farbfüllung</b> - wählen Sie diese Option, um die Farbe festzulegen, die Sie auf die ausgwählten Folien anwenden wollen.</li>
                                    <li><b>Füllung mit Farbverlauf</b> - wählen Sie diese Option, um die Folie in zwei Farben zu gestalten, die sanft ineinander übergehen.</li>
                                    <li><b>Bild oder Textur</b> - wählen Sie diese Option, um ein Bild oder eine vorgegebene Textur als Folienhintergrund festzulegen.</li>
                                    <li><b>Muster</b> - wählen Sie diese Option, um die Folie mit dem zweifarbigen Design, das aus regelmässig wiederholten Elementen besteht, zu füllen.</li>
                                    <li><b>Keine Füllung</b> - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten.</li>
                                    <li><b>Undurchsichtigkeit</b> - ziehen Sie den Schieberegler oder geben Sie den Prozentwert manuell ein. Der Standardwert ist <b>100%</b>. Er entspricht der vollen Undurchsichtigkeit. Der Wert <b>0%</b> entspricht der vollen Transparenz.</li>
                                </ul>
                                <p>Weitere Informationen zu diesen Optionen finden Sie im Abschnitt <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">Objekte ausfüllen und Farben auswählen</a>.</p>
                        </li>
                    </ol>
                </li>
                <li>
                    Die <b>Übergänge</b> helfen dabei, Ihre Präsentation dynamischer zu gestalten und die Aufmerksamkeit Ihres Publikums zu erhalten. <b>Um einen Übergang anzuwenden</b>,
                    <ol>
                        <li>wählen Sie in der Folienliste auf der linken Seite die Folien aus, auf die Sie einen Übergang anwenden möchten,</li>
                        <li>
                            wählen Sie in der Drop-Down-Liste <b>Effekt</b> auf der Registerkarte <b>Folien-Einstellungen</b> einen Übergang aus,
                            <p class="note">Um die Registerkarte <b>Folien-Einstellungen</b> zu öffnen, klicken Sie im Folienbearbeitungsbereich auf das Symbol <b>Folien-Einstellungen</b> <span class="icon icon-slide_settings_icon"></span> oder öffnen Sie das Rechtsklickmenü und wählen Sie <b>Folien-Einstellungen</b> aus dem Kontextmenü aus.</p>
                        </li>
                        <li>passen Sie die Übergangseigenschaften an: Wählen Sie einen Übergang, Dauer und die Art und Weise, wie die Folien vorgerückt werden,</li>
                        <li>
                            klicken Sie auf die Schaltfläche <b>Auf alle Folien anwenden</b>, um den gleichen Übergang auf alle Folien in der Präsentation anzuwenden.
                            <p>Weitere Informationen über diese Optionen finden Sie im Abschnitt <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Übergänge anwenden</a>.</p>
                        </li>
                    </ol>
                </li>
                <li>
                    <a id="changelayout"></a><b>Um Folienlayout zu ändern,</b>
                    <ol>
                        <li>wählen Sie in der Folienliste auf der linken Seite die Folien aus, auf die Sie ein neues Layout anwenden möchten,</li>
                        <li>klicken Sie auf das Symbol <div class = "icon icon-changelayout"></div> <b>Folienlayout ändern</b> auf der Registerkarte <b>Startseite</b> der obere Symbolleiste,</li>
                        <li>
                            wählen Sie das gewünschte Layout aus dem Menü aus.
                            <p>Sie können auch mit der rechten Maustaste auf die gewünschte Folie in der Liste links klicken, die Option <b>Layout ändern</b> im Kontextmenü auswählen und das gewünschte Layout bestimmen.</p>
                            <p class="note">Derzeit sind die folgenden Layouts verfügbar: Title Slide, Title and Content, Section Header, Two Content, Comparison, Title Only, Blank, Content with Caption, Picture with Caption, Title and Vertical Text, Vertical Title and Text.</p>
                        </li>
                    </ol>
                </li>
                <li>
                    <a id="addtolayout"></a><b>Um einem Folienlayout Objekte hinzuzufügen</b>,
                    <ol>
                        <li>Klicken Sie auf das Symbol <b>Folienlayout ändern</b> <div class = "icon icon-changelayout"></div> und wählen Sie ein Layout aus, zu dem Sie ein Objekt hinzufügen möchten,</li>
                        <li>
                            verwenden Sie die Registerkarte <b>Einfügen</b> der oberen Symbolleiste, fügen Sie das erforderliche Objekt zur Folie hinzu (<em>Bild</em>, <em>Tabelle</em>, <em>Diagramm</em> , <em>Form</em>), dann <b>mit der rechten Maustaste</b> auf dieses Objekt klicken und die Option <b>Zum Layout hinzufügen</b> auswählen,
                            <br /><img alt="Zum Layout hinzufügen" src="../images/addtolayout.png" />
                        </li>
                        <li>
                            klicken Sie auf der Registerkarte <b>Startseite</b> auf <b>Folienlayout ändern</b> <div class = "icon icon-changelayout"></div> und wenden Sie die geänderte Anordnung an.
                            <p><img alt="Layout anwenden" src="../images/applylayout.png" /></p>
                            <p>Die ausgewählten Objekte werden dem Layout des aktuellen Themas hinzugefügt.</p>
                            <p class="note">Objekte, die auf diese Weise auf einer Folie platziert wurden, können nicht ausgewählt, in der Größe geändert oder verschoben werden.</p>
                        </li>
                    </ol>
                </li>
                <li>
                    <a id="resetslide"></a><b>Um das Folienlayout in den ursprünglichen Zustand zurückzusetzen</b>,
                    <ol>
                        <li>
                            wählen Sie in der Folienliste auf der linken Seite die Folien aus, die Sie in den Standardzustand zurücksetzen möchten.
                            <p class="note">Halten Sie die Strg-Taste gedrückt und wählen Sie jeweils eine Folie aus, um mehrere Folien gleichzeitig auszuwählen, oder halten Sie die Umschalttaste gedrückt, um alle Folien von der aktuellen bis zur ausgewählten auszuwählen.</p>
                        </li>
                        <li>
                            klicken Sie mit der rechten Maustaste auf eine der Folien und wählen Sie im Kontextmenü die Option <b>Folie zurücksetzen</b>,
                            <p>Alle auf Folien befindlichen Textrahmen und Objekte werden zurückgesetzt und entsprechend dem Folienlayout angeordnet.</p>
                        </li>
                    </ol>
                </li>
                <li>
                    <a id="slidenote"></a><b>Um einer Folie Notizen hinzuzufügen</b>,
                    <ol>
                        <li>wählen Sie in der Folienliste auf der linken Seite die Folie aus, zu der Sie eine Notiz hinzufügen möchten,</li>
                        <li>klicken Sie unterhalb des Folienbearbeitungsbereichs auf die Beschriftung <b>Klicken Sie, um Notizen hinzuzufügen</b>,</li>
                        <li>
                            geben Sie den Text Ihrer Notiz ein.
                            <p class="note">Sie können den Text mithilfe der Symbole auf der Registerkarte <b>Startseite</b> der oberen Symbolleiste formatieren.</p>
                        </li>
                    </ol>
                    <p>Wenn Sie die Präsentation in der <a href="../UsageInstructions/PreviewPresentation.htm#presenter" onclick="onhyperlinkclick(this)"><b>Referentenansicht</b> starten</a>, werden Ihnen alle vorhandenen Notizen unterhalb der Vorschauansicht angezeigt.</p>
                </li>
            </ul>			
		</div>
	</body>
</html>