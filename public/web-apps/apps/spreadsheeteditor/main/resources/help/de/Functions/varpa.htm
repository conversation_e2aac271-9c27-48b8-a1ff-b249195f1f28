﻿<!DOCTYPE html>
<html>
	<head>
		<title>VARIANZENA-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>VARIANZENA-Funktion</h1>
			<p>Die Funktion <b>VARIANZENA</b> gehört zur Gruppe der statistischen Funktionen. Sie wird verwendet, um die angegebenen Werten zu analysieren und die Varianz einer gesamten Population zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>VARIANZENA</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>VARIANZENA(Zahl1;[Zahl2];...)</em></b></p> 
			<p><b><em>Zahl1/2/...</em></b> sind die Wertargumente, die einer Stichprobe einer Grundgesamtheit entsprechen.</p>
			<p class="note"><b>Hinweis:</b> Text und FALSCH-Werte werden als 0 berücksichtigt. WAHR-Werte werden als 1 berücksichtigt. Leere Zellen werden ignoriert.</p>
			<p>Anwendung der Funktion <b>VARIANZENA</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>VARIANZENA</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="VARIANZENA-Funktion" src="../images/varpa.png" /></p>
		</div>
	</body>
</html>