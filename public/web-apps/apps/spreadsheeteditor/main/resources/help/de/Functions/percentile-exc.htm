﻿<!DOCTYPE html>
<html>
	<head>
		<title>QUANTIL.EXKL-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>QUANTIL.EXKL-Funktion</h1>
			<p>Die Funktion <b>QUANTIL.EXKL</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt das <b><em>k</em></b>-<PERSON><PERSON><PERSON> in einem Bereich zurück, wobei <b><em>k</em></b> im Bereich von 0..1 ausschließlich liegt.</p>
			<p>Die Formelsyntax der Funktion <b>QUANTIL.EXKL</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>QUANTIL.EXKL(Matrix;k)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Matrix</em></b> ist der ausgewählte Zellbereich, für den Sie das <b><em>k</em></b>-Quantil berechnen möchten.</p>
				<p style="text-indent: 50px;"><b><em>k</em></b> ist der Quantilwert aus dem geschlossenen Intervall. Ein nummerischer Wert, der größer ist als 0 und kleiner als 1 und manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>QUANTIL.EXKL</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>QUANTIL.EXKL</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="QUANTIL.EXKL-Funktion" src="../images/percentile-exc.png" /></p>
		</div>
	</body>
</html>