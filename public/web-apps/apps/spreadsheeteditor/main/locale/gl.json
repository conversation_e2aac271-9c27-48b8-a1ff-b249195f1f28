{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Insira a súa mensaxe aquí", "Common.Controllers.History.notcriticalErrorTitle": "Aviso", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON>", "Common.define.chartData.textAreaStackedPer": "Área amoroada 100% ", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "<PERSON>umna a<PERSON>", "Common.define.chartData.textBarNormal3d": "Columna 3D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Columna 3D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON> am<PERSON>", "Common.define.chartData.textBarStacked3d": "Columna 3D amoreada", "Common.define.chartData.textBarStackedPer": "Columna amontoada 100%", "Common.define.chartData.textBarStackedPer3d": "Columna 3D amoreada 100%", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Columna", "Common.define.chartData.textColumnSpark": "Columna", "Common.define.chartData.textCombo": "Combinado", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> - Columna a<PERSON>", "Common.define.chartData.textComboBarLine": "Columna <PERSON>", "Common.define.chartData.textComboBarLineSecondary": "Columna agrupada - Liña no eixo secundario", "Common.define.chartData.textComboCustom": "Combinación personalizada", "Common.define.chartData.textDoughnut": "<PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Barra agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3D agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON> amontoada", "Common.define.chartData.textHBarStacked3d": "Barra 3D apilada", "Common.define.chartData.textHBarStackedPer": "Barra amontoada 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D amoreada 100%", "Common.define.chartData.textLine": "Liña", "Common.define.chartData.textLine3d": "Liña 3D", "Common.define.chartData.textLineMarker": "Liña con marcadores", "Common.define.chartData.textLineSpark": "Liña", "Common.define.chartData.textLineStacked": "<PERSON><PERSON>", "Common.define.chartData.textLineStackedMarker": "Liña amontoada con marcadores", "Common.define.chartData.textLineStackedPer": "Liña amontoada 100%", "Common.define.chartData.textLineStackedPerMarker": "Liña amontoada con marcadores 100%", "Common.define.chartData.textPie": "Sector do círculo", "Common.define.chartData.textPie3d": "Circular 3D", "Common.define.chartData.textPoint": "XY (Dispersión)", "Common.define.chartData.textScatter": "Dispersión", "Common.define.chartData.textScatterLine": "Dispersión coas liñas rectas", "Common.define.chartData.textScatterLineMarker": "Dispersión coas liñas rectas e marcadores", "Common.define.chartData.textScatterSmooth": "Dispersión con liñas suavizadas", "Common.define.chartData.textScatterSmoothMarker": "Dispersión coas liñas suavizadas e marcadores", "Common.define.chartData.textSparks": "Minigráficos", "Common.define.chartData.textStock": "De cotizacións", "Common.define.chartData.textSurface": "Superficie", "Common.define.chartData.textWinLossSpark": "Ganancia/perda", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Sen formato establecido", "Common.define.conditionalData.text1Above": "1 por enrima de des. est.", "Common.define.conditionalData.text1Below": "1 por debaixo de des. est.", "Common.define.conditionalData.text2Above": "2 por enriba de des. est.", "Common.define.conditionalData.text2Below": "2 por debaixo de des. est.", "Common.define.conditionalData.text3Above": "3 por enriba de des. est.", "Common.define.conditionalData.text3Below": "3 por debaixo de des. est.", "Common.define.conditionalData.textAbove": "Enriba", "Common.define.conditionalData.textAverage": "Promedio", "Common.define.conditionalData.textBegins": "comeza con", "Common.define.conditionalData.textBelow": "Abaixo", "Common.define.conditionalData.textBetween": "entre", "Common.define.conditionalData.textBlank": "En branco", "Common.define.conditionalData.textBlanks": "<PERSON><PERSON>n celdas en branco", "Common.define.conditionalData.textBottom": "Inferior", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Barra de datos", "Common.define.conditionalData.textDate": "Data", "Common.define.conditionalData.textDuplicate": "Duplicar", "Common.define.conditionalData.textEnds": "Remata con", "Common.define.conditionalData.textEqAbove": "Igual ou superior a", "Common.define.conditionalData.textEqBelow": "Igual ou inferior a", "Common.define.conditionalData.textEqual": "Igual a", "Common.define.conditionalData.textError": "Erro", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON> erros", "Common.define.conditionalData.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreater": "Superior a", "Common.define.conditionalData.textGreaterEq": "Superior a ou igual a", "Common.define.conditionalData.textIconSets": "Conxunto de iconas", "Common.define.conditionalData.textLast7days": "Nos últimos 7 días", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON> pasado", "Common.define.conditionalData.textLastWeek": "Semana pasada", "Common.define.conditionalData.textLess": "Inferior a", "Common.define.conditionalData.textLessEq": "Inferior a ou igual a", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON>", "Common.define.conditionalData.textNextWeek": "Próxima semana", "Common.define.conditionalData.textNotBetween": "Non está entre", "Common.define.conditionalData.textNotBlanks": "Non contén celdas en branco", "Common.define.conditionalData.textNotContains": "Non contén", "Common.define.conditionalData.textNotEqual": "Non igual a", "Common.define.conditionalData.textNotErrors": "Non contén erros", "Common.define.conditionalData.textText": "Тexto", "Common.define.conditionalData.textThisMonth": "<PERSON>ste mes", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> semana", "Common.define.conditionalData.textToday": "Hoxe", "Common.define.conditionalData.textTomorrow": "Mañ<PERSON>", "Common.define.conditionalData.textTop": "Parte superior", "Common.define.conditionalData.textUnique": "Único", "Common.define.conditionalData.textValue": "O valor é", "Common.define.conditionalData.textYesterday": "Onte", "Common.Translation.warnFileLocked": "O ficheiro está a editarse noutro aplicativo. Podes continuar editándoo e gardándoo como copia.", "Common.Translation.warnFileLockedBtnEdit": "Crear unha copia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Nova cor personalizada", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON> b<PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON> b<PERSON>", "Common.UI.ComboDataView.emptyComboText": "<PERSON> estilo", "Common.UI.ExtendedColorDialog.addButtonText": "Engadir", "Common.UI.ExtendedColorDialog.textCurrent": "Actual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor inserido é incorrecto. <br> Insira un valor do 000000 a FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor introducido é incorrecto. <br> Insira un valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sen cor", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Agochar o contrasinal", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Amosar o contrasinal", "Common.UI.SearchBar.textFind": "At<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON> busca", "Common.UI.SearchBar.tipNextResult": "Seguinte resultado", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir a configuración avanzada", "Common.UI.SearchBar.tipPreviousResult": "Anterior resultado", "Common.UI.SearchDialog.textHighlight": "<PERSON>zar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto da substitución", "Common.UI.SearchDialog.textSearchStart": "Insira o seu texto aquí", "Common.UI.SearchDialog.textTitle": "Buscar e substituír", "Common.UI.SearchDialog.textTitle2": "Buscar", "Common.UI.SearchDialog.textWholeWords": "Só palabras completas", "Common.UI.SearchDialog.txtBtnHideReplace": "Agochar substitución", "Common.UI.SearchDialog.txtBtnReplace": "Substituír", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituír todo", "Common.UI.SynchronizeTip.textDontShow": "Non volver a amosar esta mensaxe", "Common.UI.SynchronizeTip.textSynchronize": "Outro usuario cambiou o documento. <br> Prema para gardar os cambios e recarga as actualizacións.", "Common.UI.ThemeColorPalette.textRecentColors": "Colores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores do tema", "Common.UI.Themes.txtThemeClassicLight": "Clásico claro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Igual que o sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Non", "Common.UI.Window.okButtonText": "Aceptar", "Common.UI.Window.textConfirmation": "Confirmación", "Common.UI.Window.textDontShow": "Non volver a amosar esta mensaxe", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Información", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "Si", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "enderezo:", "Common.Views.About.txtLicensee": "LICENZA", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "correo electrónico:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versión", "Common.Views.AutoCorrectDialog.textAdd": "Engadir", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Aplicar mentres traballa", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorrección", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformato mentres escribe", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textHyperlink": "Rutas da rede e Internet por hiperligazóns", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorrección matemática", "Common.Views.AutoCorrectDialog.textNewRowCol": "Incluír novas filas e columnas na táboa", "Common.Views.AutoCorrectDialog.textRecognized": "Funcións recoñecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expresións son expresións matemáticas recoñecidas. Non se cursarán automaticamente.", "Common.Views.AutoCorrectDialog.textReplace": "Substituír", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON><PERSON> mentres escribe", "Common.Views.AutoCorrectDialog.textReplaceType": "<PERSON><PERSON><PERSON><PERSON> o texto mentres escribe", "Common.Views.AutoCorrectDialog.textReset": "Restablecer", "Common.Views.AutoCorrectDialog.textResetAll": "Restablecer valores predeterminados", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorrección", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funcións recoñecidas deben conter só letras do A ao Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Calquera expresión que engadise, eliminarase e as eliminadas restauraranse. Desexa continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A corrección automática para %1 xa existe. Quere substituíla?", "Common.Views.AutoCorrectDialog.warnReset": "Calquera autocorrección que engadise eliminase e os modificados serán restaurados aos seus valores orixinais. Desexa continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A entrada de autocorrección de% 1 restablecerase ao seu valor orixinal. Queres continuar?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor do A ao Z", "Common.Views.Comments.mniAuthorDesc": "Autor do Z ao A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "Desde enriba", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON>", "Common.Views.Comments.textAdd": "Engadir", "Common.Views.Comments.textAddComment": "Engadir comentario", "Common.Views.Comments.textAddCommentToDoc": "Engadir comentario ao documento", "Common.Views.Comments.textAddReply": "Engadir resposta", "Common.Views.Comments.textAll": "Todo", "Common.Views.Comments.textAnonym": "Convidado(a)", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> os comentarios", "Common.Views.Comments.textComments": "Comentarios", "Common.Views.Comments.textEdit": "Aceptar", "Common.Views.Comments.textEnterCommentHint": "Insira o seu comentario aquí", "Common.Views.Comments.textHintAddComment": "Engadir comentario", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolto", "Common.Views.Comments.textSort": "Ordenar comentarios", "Common.Views.Comments.textViewResolved": "Non ten permiso para volver a abrir o documento", "Common.Views.Comments.txtEmpty": "Sen comentarios na folla.", "Common.Views.CopyWarningDialog.textDontShow": "Non volver a amosar esta mensaxe", "Common.Views.CopyWarningDialog.textMsg": "As accións de copiar, cortar e pegar empregando os botóns da barra de ferramentas do editor e as accións do menú contextual realizaranse só nesta pestana do editor. <br> <br> Para copiar ou pegar en ou desde aplicativos fóra da pestana do editor, use as seguintes combinacións de teclado:", "Common.Views.CopyWarningDialog.textTitle": "Accións de copiar, cortar e pegar", "Common.Views.CopyWarningDialog.textToCopy": "para Copiar", "Common.Views.CopyWarningDialog.textToCut": "para Cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Pegar", "Common.Views.DocumentAccessDialog.textLoading": "Cargando...", "Common.Views.DocumentAccessDialog.textTitle": "Configuración para compartir", "Common.Views.EditNameDialog.textLabel": "Etiqueta:", "Common.Views.EditNameDialog.textLabelError": "A etiqueta non debe estar vacía.", "Common.Views.Header.labelCoUsersDescr": "Usuarios que están editando o ficheiro:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Configuración avanzada", "Common.Views.Header.textBack": "Abrir ubicación do ficheiro", "Common.Views.Header.textCompactView": "Agochar barra de ferramentas", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON> regras", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON><PERSON>r as barras de folla e de estado", "Common.Views.Header.textRemoveFavorite": "Eliminar dos Favoritos", "Common.Views.Header.textSaveBegin": "Gardando...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "Todos os cambios foron gardados", "Common.Views.Header.textSaveExpander": "Todos os cambios foron gardados", "Common.Views.Header.textShare": "Compartir", "Common.Views.Header.textZoom": "Ampliar", "Common.Views.Header.tipAccessRights": "Xestionar dereitos de acceso ao documento", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "<PERSON><PERSON>", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Gardar", "Common.Views.Header.tipSearch": "Buscar", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Abrir nunha xanela independente", "Common.Views.Header.tipUsers": "Ver usuarios", "Common.Views.Header.tipViewSettings": "Amosar configuración", "Common.Views.Header.tipViewUsers": "Ver usuarios e administrar dereitos de acceso ao documento", "Common.Views.Header.txtAccessRights": "Cambiar dereitos de acceso", "Common.Views.Header.txtRename": "Renomear", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON> historial", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Agochar cambios detal<PERSON>", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Amosar cambios detallad<PERSON>", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "<PERSON><PERSON><PERSON> da imaxe:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo debe ser unha URL no formato \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Con viñetas", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.tipChange": "Cambiar viñeta", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>ñ<PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Cor", "Common.Views.ListSettingsDialog.txtNewBullet": "Nova viñeta", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% do texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON> en", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Configuración da lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "<PERSON>ngo de celdas non válido", "Common.Views.OpenDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "<PERSON><PERSON> puntos", "Common.Views.OpenDialog.txtComma": "Coma", "Common.Views.OpenDialog.txtDelimiter": "Delimitador", "Common.Views.OpenDialog.txtDestData": "Escolla onde poñer os datos", "Common.Views.OpenDialog.txtEmpty": "Este campo é obrigatorio", "Common.Views.OpenDialog.txtEncoding": "Codificación", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtOpenFile": "Insira o contrasinal para abrir o ficheiro", "Common.Views.OpenDialog.txtOther": "Outro", "Common.Views.OpenDialog.txtPassword": "Contrasinal", "Common.Views.OpenDialog.txtPreview": "Vista previa", "Common.Views.OpenDialog.txtProtected": "Unha vez que se inseriu o contrasinal e aberto o ficheiro, o contrasinal actual ao ficheiro restablecerase", "Common.Views.OpenDialog.txtSemicolon": "Punto e coma", "Common.Views.OpenDialog.txtSpace": "Espazo", "Common.Views.OpenDialog.txtTab": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtTitle": "Elixir opcións de %1", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON>cheiro protexido", "Common.Views.PasswordDialog.txtDescription": "Estableza un contrasinal para protexer este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "O contrasinal de confirmación non é idéntico", "Common.Views.PasswordDialog.txtPassword": "Contrasinal", "Common.Views.PasswordDialog.txtRepeat": "Repetir o contrasinal", "Common.Views.PasswordDialog.txtTitle": "Insira un contrasinal", "Common.Views.PasswordDialog.txtWarning": "Aviso: se perde ou esquece o contrasinal, non se poderá recuperar. Consérvao nun lugar seguro.", "Common.Views.PluginDlg.textLoading": "Cargando", "Common.Views.Plugins.groupCaption": "Extensións", "Common.Views.Plugins.strPlugins": "Extensións", "Common.Views.Plugins.textLoading": "Cargando", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Encriptar con contrasinal", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> ou elimine o contrasinal", "Common.Views.Protection.hintSignature": "Engadir sinatura dixital ou liña de sinatura", "Common.Views.Protection.txtAddPwd": "Engadir contrasinal", "Common.Views.Protection.txtChangePwd": "Cambiar contrasinal", "Common.Views.Protection.txtDeletePwd": "Eliminar contrasinal", "Common.Views.Protection.txtEncrypt": "Encriptar", "Common.Views.Protection.txtInvisibleSignature": "Engadir sinatura dixital", "Common.Views.Protection.txtSignature": "Asinatura", "Common.Views.Protection.txtSignatureLine": "Engadir liña de sinatura", "Common.Views.RenameDialog.textName": "Nome do ficheiro", "Common.Views.RenameDialog.txtInvalidName": "O nome do ficheiro non debe conter os seguintes símbolos:", "Common.Views.ReviewChanges.hintNext": "Ao seguinte cambio", "Common.Views.ReviewChanges.hintPrev": "Ao cambio anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Co-edición a tempo real. Todos os cambios gárdanse de forma automática.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Use o bot<PERSON> \"Gardar\" para sincronizar os cambios que ou ti ou outros realizastes", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.tipCoAuthMode": "Estableza o modo de co-edición", "Common.Views.ReviewChanges.tipCommentRem": "Eliminar comentarios", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Eliminar comentarios actuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentarios", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver os comentarios actuais", "Common.Views.ReviewChanges.tipHistory": "Amosar a versión do historial", "Common.Views.ReviewChanges.tipRejectCurrent": "Rexeitar cambios actuais", "Common.Views.ReviewChanges.tipReview": "Seguemento de cambios", "Common.Views.ReviewChanges.tipReviewView": "Seleccionar o modo no que quere que se presenten os cambios", "Common.Views.ReviewChanges.tipSetDocLang": "Establecer o idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.tipSharing": "Xestionar dereitos de acceso ao documento", "Common.Views.ReviewChanges.txtAccept": "Aceptar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceptar todos os cambios", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceptar cambios", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.txtChat": "Conversa", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "O modo Co-edición", "Common.Views.ReviewChanges.txtCommentRemAll": "Eliminar todos os comentarios", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Eliminar comentarios actuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Eliminar os meus comentarios", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Eliminar os meus comentarios actuais", "Common.Views.ReviewChanges.txtCommentRemove": "Eliminar", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentarios", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver os comentarios actuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver os meus comentarios", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver os meus comentarios actuais", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtFinal": "Todos os cambios aceptados (vista previa)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historial de versións", "Common.Views.ReviewChanges.txtMarkup": "Todos os cambios (Edición)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcación", "Common.Views.ReviewChanges.txtNext": "Se<PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Todos os cambios rexeitados (Vista previa)", "Common.Views.ReviewChanges.txtOriginalCap": "Orixinal", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Rexeitar todos os cambios", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON> camb<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Rexeitar cambios actuais", "Common.Views.ReviewChanges.txtSharing": "Compartir", "Common.Views.ReviewChanges.txtSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.txtTurnon": "Seguemento de cambios", "Common.Views.ReviewChanges.txtView": "Modo de visualización", "Common.Views.ReviewPopover.textAdd": "Engadir", "Common.Views.ReviewPopover.textAddReply": "Engadir resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "Aceptar", "Common.Views.ReviewPopover.textMention": "+mención proporcionará acceso ao documento e enviará un correo electrónico", "Common.Views.ReviewPopover.textMentionNotify": "+mención notificará o usuario por correo electrónico", "Common.Views.ReviewPopover.textOpenAgain": "Abrir novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Non ten permiso para volver a abrir o documento", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Cargando", "Common.Views.SaveAsDlg.textTitle": "Cartafol para gardar", "Common.Views.SearchPanel.textByColumns": "Por columnas", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON> <PERSON>las", "Common.Views.SearchPanel.textCaseSensitive": "Diferenciar maiús<PERSON>s de minúsculas", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON> busca", "Common.Views.SearchPanel.textFind": "At<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Buscar e substituír", "Common.Views.SearchPanel.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.Views.SearchPanel.textItemEntireCell": "Todo o contido da celda", "Common.Views.SearchPanel.textLookIn": "Buscar en ", "Common.Views.SearchPanel.textMatchUsingRegExp": "Relacionar con expresións regulares", "Common.Views.SearchPanel.textName": "Nome", "Common.Views.SearchPanel.textNoMatches": "Non hai coincidencias", "Common.Views.SearchPanel.textNoSearchResults": "Non se obtiveron resultados", "Common.Views.SearchPanel.textReplace": "Substituír", "Common.Views.SearchPanel.textReplaceAll": "Substituír todo", "Common.Views.SearchPanel.textReplaceWith": "Substituír con", "Common.Views.SearchPanel.textSearch": "Buscar", "Common.Views.SearchPanel.textSearchOptions": "Opcións de busca", "Common.Views.SearchPanel.textSearchResults": "Resultados da busca: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "Selección de rango de datos", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Especificar intervalo", "Common.Views.SearchPanel.textTooManyResults": "Hai moitos resultados para amosar aquí", "Common.Views.SearchPanel.textValue": "Valor", "Common.Views.SearchPanel.textValues": "Valores", "Common.Views.SearchPanel.textWholeWords": "Só palabras completas", "Common.Views.SearchPanel.textWithin": "<PERSON><PERSON> de", "Common.Views.SearchPanel.textWorkbook": "Libro de traballo", "Common.Views.SearchPanel.tipNextResult": "Seguinte resultado", "Common.Views.SearchPanel.tipPreviousResult": "Anterior resultado", "Common.Views.SelectFileDlg.textLoading": "Cargando", "Common.Views.SelectFileDlg.textTitle": "Seleccionar fonte de <PERSON>", "Common.Views.SignDialog.textBold": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "Certificar", "Common.Views.SignDialog.textChange": "Cambiar", "Common.Views.SignDialog.textInputName": "Inserir nome de quen asina", "Common.Views.SignDialog.textItalic": "Cursiva", "Common.Views.SignDialog.textNameError": "O nome do asinante non debe estar vacío.", "Common.Views.SignDialog.textPurpose": "Propósito ao asinar este documento", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> imaxe", "Common.Views.SignDialog.textSignature": "A sinatura vese como", "Common.Views.SignDialog.textTitle": "Asinar documento", "Common.Views.SignDialog.textUseImage": "ou premer 'Selecci<PERSON>r I<PERSON>' para usar unha imaxe como sinatura", "Common.Views.SignDialog.textValid": "Válido desde %1 ata %2", "Common.Views.SignDialog.tipFontName": "<PERSON>me da fonte", "Common.Views.SignDialog.tipFontSize": "<PERSON><PERSON><PERSON>e", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir a quen asine engadir comentarios no diálogo de sinatura", "Common.Views.SignSettingsDialog.textInfoEmail": "Correo electrónico", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON><PERSON><PERSON> de quen asina", "Common.Views.SignSettingsDialog.textInstructions": "Instrucións para o asinante", "Common.Views.SignSettingsDialog.textShowDate": "Presentar data da sinatura", "Common.Views.SignSettingsDialog.textTitle": "Preparación da sinatura", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo é obrigatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Signo de Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "Aspas dobres de peche", "Common.Views.SymbolTableDialog.textDOQuote": "Aspas dobres de apertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Espazo longo", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON> curto", "Common.Views.SymbolTableDialog.textEnSpace": "Espazo curto", "Common.Views.SymbolTableDialog.textFont": "Fonte", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON> sen ruptura", "Common.Views.SymbolTableDialog.textNBSpace": "Espazo de non separación", "Common.Views.SymbolTableDialog.textPilcrow": "Sinal de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 do espazo", "Common.Views.SymbolTableDialog.textRange": "Ra<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Símbolos utilizados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Sinal da marca rexistrada", "Common.Views.SymbolTableDialog.textSCQuote": "Aspas simples de peche", "Common.Views.SymbolTableDialog.textSection": "Sinal da sección", "Common.Views.SymbolTableDialog.textShortcut": "Teclas de atallo", "Common.Views.SymbolTableDialog.textSHyphen": "G<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Aspas simples de apertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca comercial", "Common.Views.UserNameDialog.textDontShow": "Non volver a preguntarme", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "A etiqueta non debe estar vacía.", "SSE.Controllers.DataTab.textColumns": "Columnas", "SSE.Controllers.DataTab.textEmptyUrl": "Debe especificar a URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Texto en columnas", "SSE.Controllers.DataTab.txtDataValidation": "Validación de datos", "SSE.Controllers.DataTab.txtExpand": "Expandir", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Non se eliminarán os datos xunto á selección. Quere ampliar a selección para incluír os datos adxacentes ou continuar só coas celas seleccionadas actualmente?", "SSE.Controllers.DataTab.txtExtendDataValidation": "A selección contén algunhas celas sen axustes de validación de datos. <br> Quere ampliar a validación de datos a estas celas?", "SSE.Controllers.DataTab.txtImportWizard": "Asistente para importar texto", "SSE.Controllers.DataTab.txtRemDuplicates": "Eliminar duplicados", "SSE.Controllers.DataTab.txtRemoveDataValidation": "A selección contén máis dun tipo de validación.<br> Borrar a configuración actuais e continuar?", "SSE.Controllers.DataTab.txtRemSelected": "Eliminar en seleccionado", "SSE.Controllers.DataTab.txtUrlTitle": "<PERSON><PERSON>ar unha URL de datos", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Ao centro", "SSE.Controllers.DocumentHolder.deleteColumnText": "Eliminar columna", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> fila", "SSE.Controllers.DocumentHolder.deleteText": "Eliminar", "SSE.Controllers.DocumentHolder.errorInvalidLink": "A referencia da ligazón non existe. Corrixa a ligazón ou elimínaa.", "SSE.Controllers.DocumentHolder.guestText": "Convidado(a)", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON>na <PERSON>", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> dereita", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Fila de enriba", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Fila de abaixo", "SSE.Controllers.DocumentHolder.insertText": "Inserir", "SSE.Controllers.DocumentHolder.leftText": "E<PERSON>rda", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Aviso", "SSE.Controllers.DocumentHolder.rightText": "Dereita", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opciones de Autocorrección", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON> da columna {0} símbolos ({1} píxeles)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON> da fila {0} puntos ({1} píxeles)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Presione CTRL e na ligazón", "SSE.Controllers.DocumentHolder.textInsertLeft": "Inserir á esquerda", "SSE.Controllers.DocumentHolder.textInsertTop": "Inserir enriba", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Pegado especial", "SSE.Controllers.DocumentHolder.textStopExpand": "Pare de expandir táboas automaticamente", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Este elemento está sendo editado por outro usuario.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Superior á media", "SSE.Controllers.DocumentHolder.txtAddBottom": "Engadir bordo inferior", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Engadir barra de fracción", "SSE.Controllers.DocumentHolder.txtAddHor": "Engadir liña horizontal", "SSE.Controllers.DocumentHolder.txtAddLB": "Engadir liña inferior esquerda", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> bordo es<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "Engadir liña superior esquerda", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON> bordo dereito", "SSE.Controllers.DocumentHolder.txtAddTop": "Engadir bordo superior", "SSE.Controllers.DocumentHolder.txtAddVer": "Engadir liña vertical", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> ao <PERSON>", "SSE.Controllers.DocumentHolder.txtAll": "(Todos)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Devolve todo o contido da táboa ou das columnas da táboa especificadas, incluíndo as cabeceiras das columnas, os datos e as filas totais", "SSE.Controllers.DocumentHolder.txtAnd": "e", "SSE.Controllers.DocumentHolder.txtBegins": "comeza con", "SSE.Controllers.DocumentHolder.txtBelowAve": "Por debaixo da media", "SSE.Controllers.DocumentHolder.txtBlanks": "(Vacíos)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Propiedades do bordo", "SSE.Controllers.DocumentHolder.txtBottom": "Inferior", "SSE.Controllers.DocumentHolder.txtColumn": "Columna", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Aliñación da columna", "SSE.Controllers.DocumentHolder.txtContains": "con<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDataTableHint": "<PERSON><PERSON><PERSON> as celas de datos da táboa ou das columnas da táboa especificadas", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>o", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Eliminar argumento", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Borrar salto manual", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Eliminar caracteres encerrados", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminar caracteres encerrados e separadores", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Eliminar ecuación", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Eliminar caracter", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Eliminar radical", "SSE.Controllers.DocumentHolder.txtEnds": "Remata con", "SSE.Controllers.DocumentHolder.txtEquals": "Igua<PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Igual á cor da celda", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Igual á cor da fonte", "SSE.Controllers.DocumentHolder.txtExpand": "Expandir e ordenar", "SSE.Controllers.DocumentHolder.txtExpandSort": "Os datos xunto á selección non se ordenarán. Quere ampliar a selección para incluír os datos adxacentes ou continuar ordenando só as celas seleccionadas actualmente?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Inferior", "SSE.Controllers.DocumentHolder.txtFilterTop": "Parte superior", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Cambiar á fracción lineal", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Cambiar á fracción inclinada", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Cambiar á fracción amontonada", "SSE.Controllers.DocumentHolder.txtGreater": "Superior a", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Superior a ou igual a", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Caracter sobre o texto", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Caracter debaixo do texto", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Devolve as cabeceiras das columnas da táboa ou das columnas da táboa especificadas", "SSE.Controllers.DocumentHolder.txtHeight": "Altura", "SSE.Controllers.DocumentHolder.txtHideBottom": "Agochar bordo inferior", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Agochar límite inferior", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Agochar corchete de peche", "SSE.Controllers.DocumentHolder.txtHideDegree": "Agochar grao", "SSE.Controllers.DocumentHolder.txtHideHor": "Agochar li<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLB": "Agochar liña inferior esquerda", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON> bordo es<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "Agochar liña superior esquerda", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Agochar corchete de apertura", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Agochar marcador de posición", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON> bordo der<PERSON>o", "SSE.Controllers.DocumentHolder.txtHideTop": "Agochar bordo superior", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Agochar límite superior", "SSE.Controllers.DocumentHolder.txtHideVer": "Agochar liña vertical", "SSE.Controllers.DocumentHolder.txtImportWizard": "Asistente para importar texto", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Aumentar o tamaño do argumento", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Inserir argumento despois", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Inserir salto manual", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Inserir ecuación despois", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Inserir ecuación antes", "SSE.Controllers.DocumentHolder.txtItems": "elementos", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> só o texto", "SSE.Controllers.DocumentHolder.txtLess": "Inferior a", "SSE.Controllers.DocumentHolder.txtLessEquals": "Inferior a ou igual a", "SSE.Controllers.DocumentHolder.txtLimitChange": "Cambiar ubicación dos límites", "SSE.Controllers.DocumentHolder.txtLimitOver": "Límite sobre o texto", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Límite debaixo do texto", "SSE.Controllers.DocumentHolder.txtLockSort": "Atópanse datos xunto á súa selección, mais non ten permisos suficientes para modificar esas celdas.<br>Desexa continuar coa selección actual?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Coincidir corchetes co alto dos argumentos", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Aliñamento de matriz", "SSE.Controllers.DocumentHolder.txtNoChoices": "Non hai opcións para encher a cela. <br> <PERSON><PERSON> se poden seleccionar os valores de texto da columna para substituílos.", "SSE.Controllers.DocumentHolder.txtNotBegins": "non comeza con", "SSE.Controllers.DocumentHolder.txtNotContains": "Non contén", "SSE.Controllers.DocumentHolder.txtNotEnds": "Non remata con", "SSE.Controllers.DocumentHolder.txtNotEquals": "Non é igual a", "SSE.Controllers.DocumentHolder.txtOr": "ou", "SSE.Controllers.DocumentHolder.txtOverbar": "Barra sobre texto", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula sen bordos", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + ancho da columna", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formato de destino", "SSE.Controllers.DocumentHolder.txtPasteFormat": "<PERSON><PERSON><PERSON> s<PERSON>o ", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + formato do número", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "<PERSON><PERSON><PERSON> s<PERSON> fórmula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + todo formateo", "SSE.Controllers.DocumentHolder.txtPasteLink": "Pegar ligazón", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Imaxe vinculada", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Combinar o formato condicional", "SSE.Controllers.DocumentHolder.txtPastePicture": "Imaxe", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formato da orixe", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Valor + todo formato", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Valor + formato de número", "SSE.Controllers.DocumentHolder.txtPasteValues": "<PERSON><PERSON><PERSON> só valor", "SSE.Controllers.DocumentHolder.txtPercent": "Por cento", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Refacer táboa de expansión automática", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Eliminar a barra de fracción", "SSE.Controllers.DocumentHolder.txtRemLimit": "Eliminar límite", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Eliminar caracter do acento", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Eliminar barra", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Desexa eliminar esta sinatura?<br> Non se pode desfacer.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Eliminar texto", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Eliminar subíndice", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Eliminar superíndice", "SSE.Controllers.DocumentHolder.txtRowHeight": "Altura da fila", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Índices despois do texto", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Índices antes do texto", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Amosar límite inferior", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Amosar corchete de peche", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON> grao", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Amosar corchete de apertura", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Amosar marcador de posición", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Amosar límite superior", "SSE.Controllers.DocumentHolder.txtSorting": "Ordenación", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ordenar os obxectos seleccionados", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Alongar corchetes", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Elixa só esta fila da columna especificada", "SSE.Controllers.DocumentHolder.txtTop": "Parte superior", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Devolve o total das filas da táboa ou das columnas da táboa especificadas", "SSE.Controllers.DocumentHolder.txtUnderbar": "Barra abaixo de texto", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Desfacer a expansión automática da táboa", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Usar o asistente para importar texto", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Prema nesta ligazón pode ser prexudicial para o seu dispositivo e os seus datos.<br> Ten a certeza de que quere continuar?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "Todos", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Base de datos", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Data e hora", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Enxeñaría", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financeiro", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Información", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 usados por última vez", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Lóxica", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Busca e referencia", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matemáticas e trigonometría", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Estatística", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Texto e datos", "SSE.Controllers.LeftMenu.newDocumentTitle": "Folla de cálculo sen nome", "SSE.Controllers.LeftMenu.textByColumns": "Por columnas", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON> <PERSON>las", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.LeftMenu.textItemEntireCell": "Todo o contido da celda", "SSE.Controllers.LeftMenu.textLoadHistory": "Cargando historial de versións...", "SSE.Controllers.LeftMenu.textLookin": "Buscar en ", "SSE.Controllers.LeftMenu.textNoTextFound": "Non se puideron atopar os datos que estabas buscando. Axuste as opcións de busca.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "A substitución foi realizada. {0} ocorrencias foron ignoradas.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "A busca foi realizada. Ocorrencias substituídas: {0}", "SSE.Controllers.LeftMenu.textSearch": "Buscar", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Valores", "SSE.Controllers.LeftMenu.textWarning": "Aviso", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON> de", "SSE.Controllers.LeftMenu.textWorkbook": "Libro de traballo", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON> t<PERSON>", "SSE.Controllers.LeftMenu.warnDownloadAs": "Se segue gardando neste formato, todas as características a excepción do texto perderanse.<br>Te na certeza de que desexa continuar?", "SSE.Controllers.Main.confirmMoveCellRange": "O intervalo da celda de destino pode conter datos. Continuar a operación?", "SSE.Controllers.Main.confirmPutMergeRange": "Os datos de orixe contiñan celas combinadas. <br> Non se fusionaron antes de pegalas na táboa.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "As fórmulas da fila da cabeceira eliminaranse e converteranse en texto estático.<br>Desexa continuar?", "SSE.Controllers.Main.convertationTimeoutText": "Excedeu o tempo límite de conversión.", "SSE.Controllers.Main.criticalErrorExtText": "Prema \"Aceptar\" para volver á lista de documentos.", "SSE.Controllers.Main.criticalErrorTitle": "Erro", "SSE.Controllers.Main.downloadErrorText": "Fallou a descarga.", "SSE.Controllers.Main.downloadTextText": "Descargando folla de c<PERSON>...", "SSE.Controllers.Main.downloadTitleText": "Cargando folla de <PERSON>", "SSE.Controllers.Main.errNoDuplicates": "Non se atoparon valores duplicados.", "SSE.Controllers.Main.errorAccessDeny": "Estás intentando realizar unha acción que non tes permitido facer.<br>Por favor, contacta co teu administrador de Servidor de Documentos.", "SSE.Controllers.Main.errorArgsRange": "Un erro na fórmula inserida.<br><PERSON><PERSON>se usando un intervalo de argumentos incorrecto.", "SSE.Controllers.Main.errorAutoFilterChange": "A operación non está permitida xa que está intentando cambiar celdas nunha táboa da súa folla de cálculo.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "A operación non se puido facer para as celas seleccionadas xa que non pode mover unha parte da táboa. <br> Seleccione outro intervalo de datos para que a táboa completa se desprazase e tente de novo.", "SSE.Controllers.Main.errorAutoFilterDataRange": "A operación non se puido facer para o intervalo de celas seleccionado. <br> Seleccione un intervalo de datos uniforme diferente ao existente e inténteo de novo.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "A operación non se pode realizar porque a área contén celas filtradas. <br> Por favor, mostra os elementos filtrados e inténtao de novo.", "SSE.Controllers.Main.errorBadImageUrl": "A URL da imaxe é incorrecta", "SSE.Controllers.Main.errorCannotUngroup": "Non se pode desagrupar. Para crear un esquema do docuemnto, seleccione filas ou columnas e agrupalas.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Non pode usar esta orde nunha folla protexida. Para usar esta orde, desprotexa a folla. <br> É posible que che pidan un contrasinal.", "SSE.Controllers.Main.errorChangeArray": "Non podes cambiar parte dunha matriz.", "SSE.Controllers.Main.errorChangeFilteredRange": "Isto cambiará un intervalo filtrado na súa folla de traballo. <br> Para completar esta tarefa, elimine os filtros automáticos.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "A cela ou o gráfico que está intentando cambiar está nunha folla protexida. <br> Para facer un cambio, desprotexa a folla. Pódeselle solicitar que introduza un contrasinal.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Perdeuse a conexión co servidor. O documento non pode ser editado agora.", "SSE.Controllers.Main.errorConnectToServer": "Non se puido gardar o documento. Comprobe a configuración de conexión ou contacte co administrador. <br> <PERSON><PERSON> premas no botón \"Aceptar\", solicitaráselle que descargue o documento.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Este comando non se pode usar con varias seleccións. <br> Seleccione un único rango e inténteo de novo.", "SSE.Controllers.Main.errorCountArg": "Un erro na fórmula inserida.<br><PERSON><PERSON><PERSON> usando un número de argumentos incorrecto.", "SSE.Controllers.Main.errorCountArgExceed": "Un erro na fórmula inserida.<br>O número de argumentos excedeu.", "SSE.Controllers.Main.errorCreateDefName": "Os intervalos nomeados existentes non se poden editar e os novos non se poden crear <br> neste momento, xa que algúns deles están sendo editados.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON>rro externo.<br><PERSON>rro de conexión de base de datos.Por favor, póñase en contacto co soporte se o erro se mantén.", "SSE.Controllers.Main.errorDataEncrypted": "Recibíronse cambios cifrados, e non poden ser cifrados", "SSE.Controllers.Main.errorDataRange": "Intervalo de datos incorrecto.", "SSE.Controllers.Main.errorDataValidate": "O valor que introduciu non é válido. <br> Un usuario ten valores restrinxidos que se poden introducir nesta cela.", "SSE.Controllers.Main.errorDefaultMessage": "Código do erro: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Estás intentando eliminar unha columna que conteña unha cela bloqueada. Non se poden eliminar as celas bloqueadas mentres a folla de traballo está protexida. <br> Para eliminar unha cela bloqueada, desprotexa a folla. Pódeselle solicitar que introduza un contrasinal.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Estás intentando eliminar unha fila que conteña unha cela bloqueada. Non se poden eliminar as celas bloqueadas mentres a folla de traballo está protexida. <br> Para eliminar unha cela bloqueada, desprotexa a folla. Pódeselle solicitar que introduza un contrasinal.", "SSE.Controllers.Main.errorEditingDownloadas": "Ocorreu un erro ao traballar no documento.<br>Utilice a opción 'Descargar como' para gardar unha copia do ficheiro no seu computador.", "SSE.Controllers.Main.errorEditingSaveas": "Ocorreu un erro ao traballar no documento.<br>utilice a opción 'Gardar como...\" para gardar unha copia do ficheiro no seu computador.", "SSE.Controllers.Main.errorEditView": "A vista de folla existente non se pode editar e as novas non se poden crear no momento en que se están editando algunhas delas.", "SSE.Controllers.Main.errorEmailClient": "Non se puido atopar ningún cliente de correo electrónico", "SSE.Controllers.Main.errorFilePassProtect": "O documento está protexido por un contrasinal e non pode ser aberto.", "SSE.Controllers.Main.errorFileRequest": "<PERSON>rro externo.<br><PERSON><PERSON> da solicitude do ficheiro.Por favor, póñase en contacto co soporte se o erro se mantén.", "SSE.Controllers.Main.errorFileSizeExceed": "O tamaño do ficheiro excede os límites establecidos para o teu servidor.<br>Por favor, contacte co seu administrador de Servidor de Documentos para máis detalles.", "SSE.Controllers.Main.errorFileVKey": "Erro externo. <br> <PERSON>ve de seguridade incorrecta. Póñase en contacto co servizo de asistencia se o erro persiste.", "SSE.Controllers.Main.errorFillRange": "Non se pode encher o rango de celdas seleccionado.<br><PERSON><PERSON> as celdas combinadas deben ter o mesmo tamaño.", "SSE.Controllers.Main.errorForceSave": "Ocorreu un erro ao gardar o ficheiro. Utilice a opción 'Descargar como' para gardar o ficheiro no seu computador ou inténtao máis tarde.", "SSE.Controllers.Main.errorFormulaName": "Un erro na fórmula inserida.<br>Nombre de fórmula incorrecto.", "SSE.Controllers.Main.errorFormulaParsing": "Erro interno ao analizar a fórmula.", "SSE.Controllers.Main.errorFrmlMaxLength": "A lonxitude da túa fórmula supera o límite de 8192 caracteres. <br> Edítaa e inténtao de novo.", "SSE.Controllers.Main.errorFrmlMaxReference": "Non pode introducir esta fórmula porque ten demasiados valores, referencias de celas <br> e / ou nomes.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Os valores do texto nas fórmulas están limitados a 255 caracteres. <br> Use a función CONCATENAR ou o operador de concatenación (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "A función fai referencia a unha folla que non existe. <br> Comp<PERSON> os datos e inténteo de novo.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Non se puido completar a operación para o intervalo de celas seleccionado. <br> Seleccione un intervalo para que a primeira fila da táboa estea na mesma fila <br> e a táboa resultante superpuxese á actual.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Non se puido completar a operación para o intervalo de celas seleccionado. <br> Seleccione un intervalo que non inclúa outras táboas.", "SSE.Controllers.Main.errorInvalidRef": "Inserir un nome correcto para a selección ou unha referencia válida á que dirixirse.", "SSE.Controllers.Main.errorKeyEncrypt": "Descritor da clave descoñecido", "SSE.Controllers.Main.errorKeyExpire": "O descritor de clave expirou", "SSE.Controllers.Main.errorLabledColumnsPivot": "Para crear unha táboa dinámica, use datos que estean organizados coma unha lista coas columnas etiquetadas.", "SSE.Controllers.Main.errorLoadingFont": "Tipos de letra non cargados.<br>Por favor contacte o administrador do servidor de documentos.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "A referencia á ubicación ou ao rango de datos non é válida.", "SSE.Controllers.Main.errorLockedAll": "Non se puido realizar a operación xa que outro usuario bloqueou a folla.", "SSE.Controllers.Main.errorLockedCellPivot": "Non podes cambiar os datos dentro dunha táboa dinámica.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Non se pode renomear a folla neste momento xa que o está a cambiar o nome doutro usuario", "SSE.Controllers.Main.errorMaxPoints": "O número máximo de puntos en serie por gráfico é de 4096.", "SSE.Controllers.Main.errorMoveRange": "É imposible cambiar unha parte da celda unida", "SSE.Controllers.Main.errorMoveSlicerError": "As segmentacións da táboa non se poden copiar dun libro a outro. <br> Inténtao de novo seleccionando a táboa completa e as segmentacións.", "SSE.Controllers.Main.errorMultiCellFormula": "As fórmulas de matriz de varias celdas non está permitidas nas táboas.", "SSE.Controllers.Main.errorNoDataToParse": "Non se seleccionaron datos para redistribuír.", "SSE.Controllers.Main.errorOpenWarning": "Unha das fórmulas do ficheiro supera o límite de 8192 caracteres. <br> <PERSON><PERSON><PERSON> a fórmula.", "SSE.Controllers.Main.errorOperandExpected": "A sintaxe da función introducida non é correcta. Comprobe se lle falta un dos parénteses - '(' ou ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "O contrasinal que forneceu non é correcto. <br> Verifique que a tecla BLOQUEO MAIÚSCULAS estea desactivada e asegúrese de usar a maiúscula correcta.", "SSE.Controllers.Main.errorPasteMaxRange": "A área de copia e pegado non coincide. <br> Seleccione unha área do mesmo tamaño ou faga clic na primeira cela seguida para pegar as celas copiadas.", "SSE.Controllers.Main.errorPasteMultiSelect": "Esta acción non se pode facer nunha selección de rango múltiple. <br> Seleccione un único rango e inténteo de novo.", "SSE.Controllers.Main.errorPasteSlicerError": "As segmentacións da táboa non poden ser copiadas dun libro ao outro.", "SSE.Controllers.Main.errorPivotGroup": "Non se pode agrupar esta selección.", "SSE.Controllers.Main.errorPivotOverlap": "O informe da táboa dinámica non pode sobrepoñerse á táboa.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "O informe da táboa dinámica gardouse sen os datos subxacentes. <br> Util<PERSON> o botón \"Actualizar\" para actualizar o informe.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON>r desgraza, non é posible imprimir máis de 1500 páxinas á vez na versión actual do programa. <br> Esta restrición eliminarase nas próximas versións.", "SSE.Controllers.Main.errorProcessSaveResult": "Problemas ao gardar", "SSE.Controllers.Main.errorServerVersion": "A versión do editor foi actualizada. A páxina será recargada para aplicar os cambios.", "SSE.Controllers.Main.errorSessionAbsolute": "A sesión de edición de documentos caducou. Recarga a páxina.", "SSE.Controllers.Main.errorSessionIdle": "O documento leva pouco tempo editándose. Recarga a páxina.", "SSE.Controllers.Main.errorSessionToken": "A conexión co servidor interrompeuse. Recarga a páxina.", "SSE.Controllers.Main.errorSetPassword": "Non se puido establecer o contrasinal.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "A referencia de localización non é válida porque as celas non están todas na mesma columna ou fila. <br> <PERSON><PERSON><PERSON><PERSON> as celas que están nunha única columna ou fila.", "SSE.Controllers.Main.errorStockChart": "Orde das filas incorrecta. Para crear un gráfico de cotizacións introduza os datos na folla de tal modo:<br> prezo de apertura, prezo máximo, prezo mínimo e prezo do peche.", "SSE.Controllers.Main.errorToken": "O token de seguridade do documento non está formado correctamente. <br> Póñase en contacto co administrador do servidor de documentos.", "SSE.Controllers.Main.errorTokenExpire": "O token de seguridade do documento caducou. <br> Póñase en contacto co administrador do servidor de documentos.", "SSE.Controllers.Main.errorUnexpectedGuid": "Erro externo. <br> GUID inesperado. Póñase en contacto co servizo de asistencia se o erro persiste.", "SSE.Controllers.Main.errorUpdateVersion": "Cambiouse a versión do ficheiro. A páxina será actualizada.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Restaurouse a conexión a Internet. A versión do ficheiro cambiou.<br>Antes de continuar traballando, necesitas descargar o ficheiro ou copiar o seu contido para estar seguro de que nada se perde e despois recargar a páxina.", "SSE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON> mesmo, non se pode acceder ao ficheiro.", "SSE.Controllers.Main.errorUsersExceed": "Superouse a cantidade de usuarios permitidos polo plan de prezos", "SSE.Controllers.Main.errorViewerDisconnect": "Perdeuse a conexión. Aínda pode visualizar o documento,<br>pero on pode descargalo ou imprimilo ata que a conexión sexa restaurada e a páxina recargada.", "SSE.Controllers.Main.errorWrongBracketsCount": "Un erro na fórmula inserida.<br>Está usando un número incorrecto de parénteses.", "SSE.Controllers.Main.errorWrongOperator": "Un erro na fórmula inserida. Un operador non válido está sendo usado.<br>Por favor, corrixa o erro.", "SSE.Controllers.Main.errorWrongPassword": "O contrasinal que proporcionou non é correcto.", "SSE.Controllers.Main.errRemDuplicates": "Duplicar valores atopados e eliminados: {0}, valores únicos restantes: {1}.", "SSE.Controllers.Main.leavePageText": "Ten cambios sen gardar nesta folla de cálculo. Preme en \"Permanecer nesta páxina\" e despois en \"Gardar\" para gardalos. Preme en \"Deixar esta páxina\" para descartar todos os cambios sen gardar.", "SSE.Controllers.Main.leavePageTextOnClose": "Perderanse todos os cambios non gardados nesta folla de cálculo. <br> Fai clic en \"Cancelar\" e logo en \"Gardar\" para gardalos. Fai clic en \"Aceptar\" para descartar todos os cambios sen gardar.", "SSE.Controllers.Main.loadFontsTextText": "Cargando datos...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadFontTextText": "Cargando datos...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadImagesTextText": "Cargando imaxes...", "SSE.Controllers.Main.loadImagesTitleText": "Cargando imaxes", "SSE.Controllers.Main.loadImageTextText": "Cargando imaxe...", "SSE.Controllers.Main.loadImageTitleText": "Cargando imaxe", "SSE.Controllers.Main.loadingDocumentTitleText": "Cargando a folla de cálculo", "SSE.Controllers.Main.notcriticalErrorTitle": "Aviso", "SSE.Controllers.Main.openErrorText": "Ocorreu un erro ao abrir o ficheiro.", "SSE.Controllers.Main.openTextText": "Abrindo folla de cálculo...", "SSE.Controllers.Main.openTitleText": "Abrindo folla de cá<PERSON>culo", "SSE.Controllers.Main.pastInMergeAreaError": "É imposible cambiar unha parte da celda unida", "SSE.Controllers.Main.printTextText": "Imprimindo folla de c<PERSON>culo...", "SSE.Controllers.Main.printTitleText": "Imprimindo folla de c<PERSON>culo", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON>a", "SSE.Controllers.Main.requestEditFailedMessageText": "Alguén está editando este documento neste momento. Por favor inténtao de novo máis tarde.", "SSE.Controllers.Main.requestEditFailedTitleText": "Acceso denegado", "SSE.Controllers.Main.saveErrorText": "Ocorreu un erro ao gardar o ficheiro.", "SSE.Controllers.Main.saveErrorTextDesktop": "Este ficheiro non se pode gardar nin crear. <br> Os motivos posibles son: <br> 1. O ficheiro é de só lectura. <br> 2. O ficheiro está a ser editado por outros usuarios. <br> 3. O disco está cheo ou corrompido.", "SSE.Controllers.Main.saveTextText": "Gardando folla de <PERSON>...", "SSE.Controllers.Main.saveTitleText": "Gardando folla de <PERSON>", "SSE.Controllers.Main.scriptLoadError": "A conexión é lenta e algúns dos compoñentes non foron cargados. Debe recargar a páxina.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Aplicar a todas as ecuacións", "SSE.Controllers.Main.textBuyNow": "Visitar sitio web", "SSE.Controllers.Main.textChangesSaved": "Todos os cambios foron gardados", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Prema para pechar o consello", "SSE.Controllers.Main.textConfirm": "Confirmación", "SSE.Controllers.Main.textContactUs": "Contactar co equipo de vendas", "SSE.Controllers.Main.textConvertEquation": "Esta ecuación creouse cunha versión antiga do editor de ecuacións que xa non é compatible. Para editalo, converte a ecuación ao formato Office Math ML. <br> Converter agora?", "SSE.Controllers.Main.textCustomLoader": "Observe que, de acordo cos termos da licenza, non ten dereito a cambiar o cargador.<br>Entre en contacto co noso Departamento de Vendas para obter unha cotización.", "SSE.Controllers.Main.textDisconnect": "Perdeuse a conexión", "SSE.Controllers.Main.textFillOtherRows": "Encher outras filas", "SSE.Controllers.Main.textFormulaFilledAllRows": "A fórmula encheu {0} filas que teñen datos. Encher outras filas vacías pode requerir uns minutos.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "A fórmula encheu as primeiras{0}filas. Encher outras filas vacías pode requerir uns minutos.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "A fórmula encheu só as primeiras {0} filas que teñen datos por razóns de aforro de memoria. Hai outras {1} filas con datos nesta folla. Podes volver enchelas manualmente.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "A fórmula encheu só as primeiras {0} filas por razóns de aforro de memoria. As demais filas desta folla non teñen datos.", "SSE.Controllers.Main.textGuest": "Convidado(a)", "SSE.Controllers.Main.textHasMacros": "O ficheiro contén macros automáticas.<br>Quere executar macros?", "SSE.Controllers.Main.textLearnMore": "Para saber máis", "SSE.Controllers.Main.textLoadingDocument": "Cargando a folla de cálculo", "SSE.Controllers.Main.textLongName": "Insira un nome que teña menos de 128 caracteres.", "SSE.Controllers.Main.textNeedSynchronize": "Ten actualizacións", "SSE.Controllers.Main.textNo": "Non", "SSE.Controllers.Main.textNoLicenseTitle": "Alcanzouse o límite da licenza", "SSE.Controllers.Main.textPaidFeature": "Característica de pago", "SSE.Controllers.Main.textPleaseWait": "A operación pode tomar máis tiempo do esperado. Agarde...", "SSE.Controllers.Main.textReconnect": "Restaurouse a conexión", "SSE.Controllers.Main.textRemember": "Recordar a miña elección para todos os ficheiros", "SSE.Controllers.Main.textRememberMacros": "Recorda a miña selección por todas as macros", "SSE.Controllers.Main.textRenameError": "O nome de usuario non pode estar vacío.", "SSE.Controllers.Main.textRenameLabel": "Insira un nome que se usará para a colaboración", "SSE.Controllers.Main.textRequestMacros": "Unha macro fai unha petición á URL. Quere permitirlla a %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "Modo estrito", "SSE.Controllers.Main.textText": "Тexto", "SSE.Controllers.Main.textTryUndoRedo": "As funcións Desfacer / Refacer están desactivadas para o modo de coedición rápida. <br> Faga clic no botón \"Modo estrito\" para cambiar ao modo de coedición estricto para editar o ficheiro sen interferencias doutros usuarios e enviar os seus cambios só despois de gardalos. eles. Pode cambiar entre os modos de coedición usando o editor Configuración avanzada.", "SSE.Controllers.Main.textTryUndoRedoWarn": "As funcións Desfacer/Refacer están activadas para o modo de coedición rápido.", "SSE.Controllers.Main.textYes": "Si", "SSE.Controllers.Main.titleLicenseExp": "A licenza expirou", "SSE.Controllers.Main.titleServerVersion": "Editor actual<PERSON><PERSON>", "SSE.Controllers.Main.txtAccent": "Ace<PERSON>", "SSE.Controllers.Main.txtAll": "(Todos)", "SSE.Controllers.Main.txtArt": "O teu texto aquí", "SSE.Controllers.Main.txtBasicShapes": "Formas básicas", "SSE.Controllers.Main.txtBlank": "(en branco)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 de %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON><PERSON> filtro", "SSE.Controllers.Main.txtColLbls": "Etiquetas da columna", "SSE.Controllers.Main.txtColumn": "Columna", "SSE.Controllers.Main.txtConfidential": "Confidencial", "SSE.Controllers.Main.txtDate": "Data", "SSE.Controllers.Main.txtDays": "Días", "SSE.Controllers.Main.txtDiagramTitle": "Título do gráfico", "SSE.Controllers.Main.txtEditingMode": "Establecer o modo de edición...", "SSE.Controllers.Main.txtErrorLoadHistory": "Erro ao cargar o historial", "SSE.Controllers.Main.txtFiguredArrows": "Formas da frecha", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Total xeral", "SSE.Controllers.Main.txtGroup": "Grupo", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON>", "SSE.Controllers.Main.txtLines": "Liña<PERSON>", "SSE.Controllers.Main.txtMath": "Matemáticas", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "Meses", "SSE.Controllers.Main.txtMultiSelect": "Sele<PERSON><PERSON> múl<PERSON>", "SSE.Controllers.Main.txtOr": "%1 ou %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Páxina %1 de %2", "SSE.Controllers.Main.txtPages": "Páxinas", "SSE.Controllers.Main.txtPreparedBy": "Preparado por", "SSE.Controllers.Main.txtPrintArea": "Área_de_impresión", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtQuarters": "Trimestres", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "Etiquetas da fila", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "Serie", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Chamada coa liña 1 (bordo e barra de énfase)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Chamada coa liña 2 (bordo e barra de énfase)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Chamada coa liña 3 (bordo e barra de énfase)", "SSE.Controllers.Main.txtShape_accentCallout1": "Chamada coa liña 1 (barra de énfase)", "SSE.Controllers.Main.txtShape_accentCallout2": "Chamada coa liña 2 (barra de énfa<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "Chamada coa liña 3 (barra de énfase)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON><PERSON> ou bot<PERSON> anterior", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Botón de inicio", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Botón en branco", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Botón Documento", "SSE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Botón Adiante ou Seguinte", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Botón A<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "Botón Inicio", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Botón Información", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Botón Vídeo ", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Botón <PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "Bo<PERSON><PERSON> Son", "SSE.Controllers.Main.txtShape_arc": "Arco", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> do<PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector angular de frecha", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector angular de frecha dobre", "SSE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON>a dobrada cara arriba", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Arco de bloque", "SSE.Controllers.Main.txtShape_borderCallout1": "Chamada coa liña 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Chamada coa liña 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Chamada coa liña 3", "SSE.Controllers.Main.txtShape_bracePair": "Clave dobre", "SSE.Controllers.Main.txtShape_callout1": "Chamada coa liñea 1 (sen bordo)", "SSE.Controllers.Main.txtShape_callout2": "Chamada coa liña 2 (sen bordo)", "SSE.Controllers.Main.txtShape_callout3": "Chamada coa liña 3 (sen bordo)", "SSE.Controllers.Main.txtShape_can": "Сilindro", "SSE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON> angulares", "SSE.Controllers.Main.txtShape_chord": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_circularArrow": "Frecha circular", "SSE.Controllers.Main.txtShape_cloud": "Nube", "SSE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON> de nube", "SSE.Controllers.Main.txtShape_corner": "Canto", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector curvado da frecha", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector curvado de frecha dobre", "SSE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON>echa curvada cara abaixo", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Frecha curvada cara á esquerda", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Frecha curvada cara á dereita", "SSE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON>echa curvada cara arriba", "SSE.Controllers.Main.txtShape_decagon": "Decágono", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "SSE.Controllers.Main.txtShape_diamond": "Diamante", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON> onda", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>a cara abaixo", "SSE.Controllers.Main.txtShape_downArrowCallout": "<PERSON><PERSON> da frecha cara abaixo", "SSE.Controllers.Main.txtShape_ellipse": "Elipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Cinta curvada cara abaixo", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Cinta curvada cara arriba", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Proceso alternativo", "SSE.Controllers.Main.txtShape_flowChartCollate": "Intercalar", "SSE.Controllers.Main.txtShape_flowChartConnector": "Conector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Decisión", "SSE.Controllers.Main.txtShape_flowChartDelay": "Atraso", "SSE.Controllers.Main.txtShape_flowChartDisplay": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Documento", "SSE.Controllers.Main.txtShape_flowChartExtract": "Extracto", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Datos", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Almacenamento interno", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Disco magnético", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Almacenamento de acceso directo", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Almacenamento de acceso secuencial", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Entrada manual", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Operación manual", "SSE.Controllers.Main.txtShape_flowChartMerge": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Multidocumento", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Conector f<PERSON><PERSON> da páxin<PERSON>", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Datos almacenados", "SSE.Controllers.Main.txtShape_flowChartOr": "Diagrama do fluxo: Ou", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Proceso predefinido", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Preparación", "SSE.Controllers.Main.txtShape_flowChartProcess": "Proceso", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Tarxeta", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Cinta perforada", "SSE.Controllers.Main.txtShape_flowChartSort": "Ordenar", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "E", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Terminador", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_frame": "<PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "Medio marco", "SSE.Controllers.Main.txtShape_heart": "Corazón", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "Hexágono", "SSE.Controllers.Main.txtShape_homePlate": "Pentágono", "SSE.Controllers.Main.txtShape_horizontalScroll": "Desprazamento horizontal", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosión 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosión 2", "SSE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON><PERSON> es<PERSON>", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Chamada de frecha á esquerda", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON> clave", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON> corchete", "SSE.Controllers.Main.txtShape_leftRightArrow": "Frecha esquerda e dereita", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Chamada de frecha esquerda e dereita", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON><PERSON>a <PERSON>, dereita e arriba", "SSE.Controllers.Main.txtShape_leftUpArrow": "Frecha esquerda e enriba", "SSE.Controllers.Main.txtShape_lightningBolt": "Raio", "SSE.Controllers.Main.txtShape_line": "Liña", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> do<PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "División", "SSE.Controllers.Main.txtShape_mathEqual": "Igual", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "SSE.Controllers.Main.txtShape_mathNotEqual": "Non é igual", "SSE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_moon": "Lúa", "SSE.Controllers.Main.txtShape_noSmoking": "Símbolo \"Non\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Frecha á dereita entallada", "SSE.Controllers.Main.txtShape_octagon": "Octágono", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelograma", "SSE.Controllers.Main.txtShape_pentagon": "Pentágono", "SSE.Controllers.Main.txtShape_pie": "Sector do círculo", "SSE.Controllers.Main.txtShape_plaque": "Asinar", "SSE.Controllers.Main.txtShape_plus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON> man alzada", "SSE.Controllers.Main.txtShape_polyline2": "Forma libre", "SSE.Controllers.Main.txtShape_quadArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Chamada de frecha cuá<PERSON>", "SSE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "Faixa cara abaixo", "SSE.Controllers.Main.txtShape_ribbon2": "Cinta cara arriba", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> dereita", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Chamada de frecha á dereita", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON> clave", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON> corchete", "SSE.Controllers.Main.txtShape_round1Rect": "Redondar rectángulo de esquina sinxela", "SSE.Controllers.Main.txtShape_round2DiagRect": "Redondar rectángulo de esquina diagonal", "SSE.Controllers.Main.txtShape_round2SameRect": "Redondar rectángulo de esquina do mesmo lado", "SSE.Controllers.Main.txtShape_roundRect": "Rectángulo con esquinas redondadas", "SSE.Controllers.Main.txtShape_rtTriangle": "Triángulo rectángulo", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON> sorrinte", "SSE.Controllers.Main.txtShape_snip1Rect": "Recortar rectángulo de esquina sinxela", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Recortar rectángulo de esquina diagonal", "SSE.Controllers.Main.txtShape_snip2SameRect": "Recortar rectángulo de esquina do mesmo lado", "SSE.Controllers.Main.txtShape_snipRoundRect": "Recortar e redondar rectángulo de esquina sinxela", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "Estrela de 10 puntos", "SSE.Controllers.Main.txtShape_star12": "Estrela de 12 puntos", "SSE.Controllers.Main.txtShape_star16": "Estrela de 16 puntos", "SSE.Controllers.Main.txtShape_star24": "Estrela de 24 puntos", "SSE.Controllers.Main.txtShape_star32": "Estrela de 32 puntos", "SSE.Controllers.Main.txtShape_star4": "Estrela de 4 puntos", "SSE.Controllers.Main.txtShape_star5": "Estrela de 5 puntos", "SSE.Controllers.Main.txtShape_star6": "Estrela de 6 puntos", "SSE.Controllers.Main.txtShape_star7": "Estrela de 7 puntos", "SSE.Controllers.Main.txtShape_star8": "Estrela de 8 puntos", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Frecha á dereita con bandas", "SSE.Controllers.Main.txtShape_sun": "Sol", "SSE.Controllers.Main.txtShape_teardrop": "Bágoa", "SSE.Controllers.Main.txtShape_textRect": "Caixa de texto", "SSE.Controllers.Main.txtShape_trapezoid": "Trapecio", "SSE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON>a cara arriba", "SSE.Controllers.Main.txtShape_upArrowCallout": "Chamada de frecha cara arriba", "SSE.Controllers.Main.txtShape_upDownArrow": "Frecha cara arriba e abaixo", "SSE.Controllers.Main.txtShape_uturnArrow": "Frecha en U", "SSE.Controllers.Main.txtShape_verticalScroll": "Desprazamento vertical", "SSE.Controllers.Main.txtShape_wave": "On<PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Chamada rectangular", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Chamada rectangular redondeada", "SSE.Controllers.Main.txtStarsRibbons": "Cintas e estrelas", "SSE.Controllers.Main.txtStyle_Bad": "Malo", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Celda de comprobación", "SSE.Controllers.Main.txtStyle_Comma": "Coma", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Texto explicativo", "SSE.Controllers.Main.txtStyle_Good": "<PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Título 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Título 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Título 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Título 4", "SSE.Controllers.Main.txtStyle_Input": "Entrada", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Celda ligada", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON>a", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Por cento", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Texto de aviso", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "Táboa", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "Desb<PERSON>que<PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Desbloquear rango", "SSE.Controllers.Main.txtUnlockRangeDescription": "Insira o contrasinal para cambiar este rango:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Un rango que está tratando de cambiar está protexido por contrasinal.", "SSE.Controllers.Main.txtValues": "Valores", "SSE.Controllers.Main.txtXAxis": "Eixo X", "SSE.Controllers.Main.txtYAxis": "Eixo Y", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "O seu navegador non é compatible.", "SSE.Controllers.Main.uploadDocExtMessage": "Formato de documento descoñecido", "SSE.Controllers.Main.uploadDocFileCountMessage": "Non hai documentos subidos", "SSE.Controllers.Main.uploadDocSizeMessage": "Límite de tamaño máximo do documento excedido.", "SSE.Controllers.Main.uploadImageExtMessage": "Formato da imaxe descoñecido.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Non hai imaxes subidas.", "SSE.Controllers.Main.uploadImageSizeMessage": "A imaxe é demasiado grande. O tamaño máximo é de 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Subindo imaxe...", "SSE.Controllers.Main.uploadImageTitleText": "Subindo imaxe", "SSE.Controllers.Main.waitText": "Agarde...", "SSE.Controllers.Main.warnBrowserIE9": "O aplicativo ten baixa capacidade no IE9. Use IE10 ou superior", "SSE.Controllers.Main.warnBrowserZoom": "A configuración da ampliación actual do seu navegador non é totalmente compatible. Restableza a ampliación predeterminada premendo Ctrl + 0.", "SSE.Controllers.Main.warnLicenseExceeded": "Alcanzou o límite de conexións simultáneas con %1 editores. Este documento abrirase só para a súa visualización.<br><PERSON>r favor, contacte co seu administrador para recibir máis información.", "SSE.Controllers.Main.warnLicenseExp": "A túa licenza caducou. <br> Actualiza a túa licenza e actualiza a páxina.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licenza expirada.<br>Non ten acceso á funcionalidade de edición de documentos.<br>por favor, póñase en contacto co seu administrador.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "A licenza precisa ser renovada.<br>Ten un acceso limitado á funcionalidade de edición de documentos.<br>Por favor, póñase en contacto co seu administrador para obter un acceso completo", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Alcanzou o límite de usuarios para os editores de %1. <PERSON><PERSON> favor, contacte co se uadministrador para recibir máis información.", "SSE.Controllers.Main.warnNoLicense": "Alcanzou o límite de conexións simultáneas con %1 editores. Este documento abrirase só para as úa visualización.<br>Contacte co equipo de vendas de %1 para coñecer os termos de actualización persoal.", "SSE.Controllers.Main.warnNoLicenseUsers": "Alcanzou o límite de usuarios para os editores de %1.<br>Contacte co equipo de vendas de %1 para coñecer os termos de actualización persoal.", "SSE.Controllers.Main.warnProcessRightsChange": "O dereito de edición do ficheiro é denegado.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON> as follas", "SSE.Controllers.Print.textFirstCol": "Primeira columna", "SSE.Controllers.Print.textFirstRow": "Primeira fila", "SSE.Controllers.Print.textFrozenCols": "Columnas inmobilizadas", "SSE.Controllers.Print.textFrozenRows": "Filas inmobilizadas ", "SSE.Controllers.Print.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Controllers.Print.textNoRepeat": "Non repetir", "SSE.Controllers.Print.textRepeat": "Repetir...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>o", "SSE.Controllers.Print.textWarning": "Aviso", "SSE.Controllers.Print.txtCustom": "Personalizar", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON> son incorrectas", "SSE.Controllers.Search.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Controllers.Search.textNoTextFound": "Non se puideron atopar os datos que estabas buscando. Axuste as opcións de busca.", "SSE.Controllers.Search.textReplaceSkipped": "A substitución foi realizada. {0} ocorrencias foron ignoradas.", "SSE.Controllers.Search.textReplaceSuccess": "Fíxose a busca. Substituíronse {0} ocorrencias", "SSE.Controllers.Statusbar.errorLastSheet": "Un libro debe conter ao menos unha folla visible.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Imposible borrar a folla de cálculo.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Perdeuse a conexión</b><br>Intentando conectarse. Comproba a configuración de conexión.", "SSE.Controllers.Statusbar.textSheetViewTip": "Está en modo Vista de follas. Os filtros e a clasificación só son visibles para vostede e os que aínda están nesta vista.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Está en modo Vista de follas. Os filtros só son visibles para vostede e os que aínda están nesta vista.", "SSE.Controllers.Statusbar.warnDeleteSheet": "As follas de traballo seleccionadas ppoden conter datos. Ten a certeza de que quere proceder?", "SSE.Controllers.Statusbar.zoomText": "Ampliar {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "A fonte que vai gardar non está dispoñible no dispositivo actual. <br> O estilo de texto amosarase empregando unha das fontes do sistema, a fonte gardada utilizarase cando estea dispoñible. <br> Quere continuar?", "SSE.Controllers.Toolbar.errorComboSeries": "Para crear un gráfico combinado, seleccione ao menos dúas series de datos.", "SSE.Controllers.Toolbar.errorMaxRows": "ERRO! O número máximo de series de datos por gráfico é 255", "SSE.Controllers.Toolbar.errorStockChart": "Orde das filas incorrecta. Para crear un gráfico de cotizacións introduza os datos na folla de tal modo:<br> prezo de apertura, prezo máximo, prezo mínimo e prezo do peche.", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "Parénteses", "SSE.Controllers.Toolbar.textDirectional": "Direccional", "SSE.Controllers.Toolbar.textFontSizeErr": "O valor inserido é incorrecto. <br> Insira un valor numérico entre 1 e 409", "SSE.Controllers.Toolbar.textFraction": "Fraccións", "SSE.Controllers.Toolbar.textFunction": "Funcións", "SSE.Controllers.Toolbar.textIndicator": "Indicadores", "SSE.Controllers.Toolbar.textInsert": "Inserir", "SSE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Operadores grandes", "SSE.Controllers.Toolbar.textLimitAndLog": "Límites e logaritmos", "SSE.Controllers.Toolbar.textLongOperation": "Operación longa", "SSE.Controllers.Toolbar.textMatrix": "Matrices", "SSE.Controllers.Toolbar.textOperator": "Operadores", "SSE.Controllers.Toolbar.textPivot": "Táboa pivote", "SSE.Controllers.Toolbar.textRadical": "Radicais", "SSE.Controllers.Toolbar.textRating": "Clasificacións", "SSE.Controllers.Toolbar.textRecentlyUsed": "Usados recentemente", "SSE.Controllers.Toolbar.textScript": "Índices", "SSE.Controllers.Toolbar.textShapes": "Formas", "SSE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "Aviso", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Frecha superior dereita e esquerda", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Frecha superior esquerda", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Frecha superior dereita", "SSE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Fórmula encadrada (con marcador de posición)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Fórmula encadrada (exemplo)", "SSE.Controllers.Toolbar.txtAccent_Check": "Verificar", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave inferior", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Clave superior", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC con barra sobreposta", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR e con barra superposta", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON> puntos", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON> puntos", "SSE.Controllers.Toolbar.txtAccent_Dot": "Punt<PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra dobre sobreposta", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Caracter de agrupación inferior", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Caracter de agrupación superior", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpón superior cara á esquerda", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpón superior cara á dereita", "SSE.Controllers.Toolbar.txtAccent_Hat": "Circunflexo", "SSE.Controllers.Toolbar.txtAccent_Smile": "Acento breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Til", "SSE.Controllers.Toolbar.txtBracket_Angle": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parénteses con separadores", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parénteses con separadores", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Curve": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parénteses con separadores", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (dúas condicións)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (tres condicións)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Obxecto de pila", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Obxecto de pila", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente de binomio", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente de binomio", "SSE.Controllers.Toolbar.txtBracket_Line": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Round": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parénteses con separadores", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Square": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Parénteses", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Corchete único", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Corchete único", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON><PERSON> celdas", "SSE.Controllers.Toolbar.txtExpand": "Expandir e ordenar", "SSE.Controllers.Toolbar.txtExpandSort": "Os datos xunto á selección non se ordenarán. Quere ampliar a selección para incluír os datos adxacentes ou continuar ordenando só as celas seleccionadas actualmente?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Fracción nesgada", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Der<PERSON>da", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Der<PERSON>da", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Der<PERSON>da", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Der<PERSON>da", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Fracción lineal", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi dividir a 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Fracción pequena", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Función de coseno inversa", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Función de coseno inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Función de cotangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Función de cotangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Función de cosecante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Función de cosecante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Función de secante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Función de secante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Función de seno inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Función de seno inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Función de tanxente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Función de tangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Cos": "Función de coseno", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Función de coseno hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Cot": "Función de cotangente", "SSE.Controllers.Toolbar.txtFunction_Coth": "Función de cotangente hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Csc": "Función de cosecante", "SSE.Controllers.Toolbar.txtFunction_Csch": "Función de cosecante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Seno zeta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sec": "Función de secante", "SSE.Controllers.Toolbar.txtFunction_Sech": "Función de secante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Sin": "Función de seno", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Función de seno hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Tan": "Función da tanxente", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Función de tanxente hiperbólica", "SSE.Controllers.Toolbar.txtInsertCells": "Inserir celdas", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Zeta diferencial", "SSE.Controllers.Toolbar.txtIntegral_dx": "Derivada x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Derivada y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Integral dobre", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral dobre", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral dobre", "SSE.Controllers.Toolbar.txtIntegralOriented": "Integral de contorno", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de superficie", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superficie", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superficie", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de contorno", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de volume", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Integral triple", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral triple", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral triple", "SSE.Controllers.Toolbar.txtInvalidRange": "ERRO! Intervalo de celda inválido", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Cuña", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Cuña", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Cuña", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Cuña", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Cuña", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-produto", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-produto", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-produto", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-produto", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-produto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unión", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Letra V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Letra V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Letra V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Letra V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Letra V", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersección", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersección", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersección", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersección", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersección", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Unión", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unión", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unión", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unión", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unión", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo do límite", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo do máximo", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Límite", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "SSE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLockSort": "Atópanse datos xunto á súa selección, mais non ten permisos suficientes para modificar esas celdas.<br>Desexa continuar coa selección actual?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Matriz vacía 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Matriz vacía 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Matriz vacía 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Matriz vacía 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz vacía con corchetes", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Matriz vacía con corchetes", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz vacía con corchetes", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz vacía con corchetes", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Matriz vacía 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Matriz vacía 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Matriz vacía 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Matriz vacía 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Puntos en liña de base", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Puntos en liña media", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Puntos diagonais", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Puntos verticais", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Matriz de identidade 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz de identidade 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz de identidade 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz de identidade 3x3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Frecha inferior dereita e esquerda", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Frecha superior dereita e esquerda", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Frecha inferior esquerda", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Frecha superior esquerda", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Frecha inferior dereita", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Frecha superior dereita", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dous puntos igual", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Produce con delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Igual por definición", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Frecha inferior dereita e esquerda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Frecha superior dereita e esquerda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Frecha inferior esquerda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Frecha superior esquerda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Frecha inferior dereita", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Frecha superior dereita", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Igual igual", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Menos igual", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Máis igual", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> cadrada con índice", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radical con índice", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> cadrada", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Subscrito", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subscrito-Sobrescrito", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Subíndice-superíndice es<PERSON>do", "SSE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "SSE.Controllers.Toolbar.txtSorting": "Ordenación", "SSE.Controllers.Toolbar.txtSortSelected": "Ordenar os obxectos seleccionados", "SSE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Case igual a", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operador asterisco", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operador de viñeta", "SSE.Controllers.Toolbar.txtSymbol_cap": "Intersección", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Elipse horizontal da liña media", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "SSE.Controllers.Toolbar.txtSymbol_cup": "Unión", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Elipse en diagonal de dereita a esquerda", "SSE.Controllers.Toolbar.txtSymbol_degree": "Graos", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Signo de división", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON>a cara abaixo", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Conxunto vacío", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Épsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Igual", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Idéntico a", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Existe", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Graos Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "SSE.Controllers.Toolbar.txtSymbol_gg": "Moito superior a", "SSE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "SSE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "SSE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> es<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON> es<PERSON>-der<PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "SSE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "SSE.Controllers.Toolbar.txtSymbol_ll": "Moito inferior a", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON> m<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Non igual a", "SSE.Controllers.Toolbar.txtSymbol_ni": "Contén como membro", "SSE.Controllers.Toolbar.txtSymbol_not": "Non entrar", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Non existe", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_o": "Ómicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "SSE.Controllers.Toolbar.txtSymbol_percent": "Porcentaxe", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_qed": "Fin da proba", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Elipse en diagonal de esquerda a dereita", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ro", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> dereita", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de radical", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON>r tanto", "SSE.Controllers.Toolbar.txtSymbol_theta": "Zeta", "SSE.Controllers.Toolbar.txtSymbol_times": "Signo de multiplicación", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON>a cara arriba", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Épsilon (variante)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Variante fi", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Variante ro", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Variante zeta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Elipse vertical", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Estilo es<PERSON> da táboa", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "<PERSON><PERSON><PERSON> c<PERSON> da táboa", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Estilo da táboa media", "SSE.Controllers.Toolbar.warnLongOperation": "A operación que está a punto de realizar podería tomar moito tempo para completarse.<br> Ten a certeza de que desexa continuar?", "SSE.Controllers.Toolbar.warnMergeLostData": "Na celda unida permanecerán só os datos da celda da esquina superior esquerda.<br>Ten a certeza de que quere continuar?", "SSE.Controllers.Viewport.textFreezePanes": "Inmobilizar paneis", "SSE.Controllers.Viewport.textFreezePanesShadow": "Amosar a sombra de paneis inmobilizados", "SSE.Controllers.Viewport.textHideFBar": "Agochar barra das fórmulas", "SSE.Controllers.Viewport.textHideGridlines": "Agochar li<PERSON> da cuadrícula", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separador decimal", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Separador de miles", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Configuracións usadas para recoñecer datos numéricos", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Calificador do texto", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Configuración avanzada", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(ningún)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Filtro personalizado", "SSE.Views.AutoFilterDialog.textAddSelection": "Engadir selección actual para filtración", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Brancos}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Seleccionar todos os resultados da busca", "SSE.Views.AutoFilterDialog.textWarning": "Aviso", "SSE.Views.AutoFilterDialog.txtAboveAve": "Sobre a media", "SSE.Views.AutoFilterDialog.txtBegins": "Comeza con...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Por debaixo da media", "SSE.Views.AutoFilterDialog.txtBetween": "Entre...", "SSE.Views.AutoFilterDialog.txtClear": "Limpar", "SSE.Views.AutoFilterDialog.txtContains": "Con<PERSON>n...", "SSE.Views.AutoFilterDialog.txtEmpty": "Inserir filtro para celda", "SSE.Views.AutoFilterDialog.txtEnds": "Remata con...", "SSE.Views.AutoFilterDialog.txtEquals": "É igual a...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrar por cor das celdas", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrar por cor da fonte", "SSE.Views.AutoFilterDialog.txtGreater": "Superior a...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Superior a ou igual a...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtrar por etiqueta", "SSE.Views.AutoFilterDialog.txtLess": "Inferior a...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Inferior a ou igual a...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Non comeza con...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Non está entre...", "SSE.Views.AutoFilterDialog.txtNotContains": "Non contén...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Non remata con...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Non é igual...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Número do filtro", "SSE.Views.AutoFilterDialog.txtReapply": "Reaplicar", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Ordenar pola cor das celdas", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Ordenar por cor da fonte", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Ordenar de maior a menor", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Ordenar de menor a maior ", "SSE.Views.AutoFilterDialog.txtSortOption": "Máis opcións de ordenación...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtro do texto", "SSE.Views.AutoFilterDialog.txtTitle": "Filtro", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtro de valor", "SSE.Views.AutoFilterDialog.warnFilterError": "Necesita ao menos un campo na área Valores para aplicar o filtro de valor.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Debe elixir polo menos un valor", "SSE.Views.CellEditor.textManager": "Xestor do nome", "SSE.Views.CellEditor.tipFormula": "Inserir función", "SSE.Views.CellRangeDialog.errorMaxRows": "ERRO! O número máximo de series de datos por gráfico é 255", "SSE.Views.CellRangeDialog.errorStockChart": "Orde das filas incorrecta. Para crear un gráfico de cotizacións introduza os datos na folla de tal modo:<br> prezo de apertura, prezo máximo, prezo mínimo e prezo do peche.", "SSE.Views.CellRangeDialog.txtEmpty": "Este campo é obrigatorio", "SSE.Views.CellRangeDialog.txtInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.CellRangeDialog.txtTitle": "Selección de rango de datos", "SSE.Views.CellSettings.strShrink": "Reducir para axustar", "SSE.Views.CellSettings.strWrap": "Axustar texto", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "<PERSON><PERSON> <PERSON> fondo", "SSE.Views.CellSettings.textBackground": "<PERSON><PERSON> <PERSON> fondo", "SSE.Views.CellSettings.textBorderColor": "Cor", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON><PERSON> bord<PERSON>", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON> regras", "SSE.Views.CellSettings.textColor": "Cor para encher", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textCondFormat": "Formato condicional", "SSE.Views.CellSettings.textControl": "Control do texto", "SSE.Views.CellSettings.textDataBars": "Barras de datos", "SSE.Views.CellSettings.textDirection": "Dirección ", "SSE.Views.CellSettings.textFill": "Encher", "SSE.Views.CellSettings.textForeground": "Cor de primeiro plano", "SSE.Views.CellSettings.textGradient": "Puntos de degradado ", "SSE.Views.CellSettings.textGradientColor": "Cor", "SSE.Views.CellSettings.textGradientFill": "Recheo degradado", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "elementos", "SSE.Views.CellSettings.textLinear": "Lineal", "SSE.Views.CellSettings.textManageRule": "Xestionar regras", "SSE.Views.CellSettings.textNewRule": "Nova regra", "SSE.Views.CellSettings.textNoFill": "Sen encher", "SSE.Views.CellSettings.textOrientation": "Orientación do texto", "SSE.Views.CellSettings.textPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Posición", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Seleccione bordos que desea cambiar aplicando estilo seleccionado", "SSE.Views.CellSettings.textSelection": "Desde a selección actual", "SSE.Views.CellSettings.textThisPivot": "Desde esta táboa pivote", "SSE.Views.CellSettings.textThisSheet": "Desde esta folla de cálculo", "SSE.Views.CellSettings.textThisTable": "Desde esta tabla", "SSE.Views.CellSettings.tipAddGradientPoint": "Engadir punto de degradado", "SSE.Views.CellSettings.tipAll": "Fixar bordo exterior e todas as liñas interiores", "SSE.Views.CellSettings.tipBottom": "Fixar só bordo exterior inferior", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON><PERSON> bordo diagonal abaixo", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON> bordo diagonal cara arriba", "SSE.Views.CellSettings.tipInner": "Fixar só liñas interiores", "SSE.Views.CellSettings.tipInnerHor": "Fixar só liñas horizontais interiores", "SSE.Views.CellSettings.tipInnerVert": "Fixar só liñas verticais interiores", "SSE.Views.CellSettings.tipLeft": "Fixar só bordo exterior esquerdo", "SSE.Views.CellSettings.tipNone": "Non fixar bordos", "SSE.Views.CellSettings.tipOuter": "<PERSON><PERSON><PERSON> só bordo exterior", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "SSE.Views.CellSettings.tipRight": "<PERSON><PERSON>r só bordo exterior dereito", "SSE.Views.CellSettings.tipTop": "Fixar só bordo exterior superior", "SSE.Views.ChartDataDialog.errorInFormula": "Hai un erro na fórmula inserida.", "SSE.Views.ChartDataDialog.errorInvalidReference": "A referencia non é válida. Debe facer referencia a unha folla de traballo aberta.", "SSE.Views.ChartDataDialog.errorMaxPoints": "O número máximo de puntos en serie por gráfico é de 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "O número máximo de series de datos por gráfico é 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "A referencia non é válida. As referencias para títulos, valores, tamaños ou etiquetas de datos deben ser unha única cela, fila ou columna.", "SSE.Views.ChartDataDialog.errorNoValues": "Para crear un gráfico, as series deben conter ao menos un valor.", "SSE.Views.ChartDataDialog.errorStockChart": "Orde das filas incorrecta. Para crear un gráfico de cotizacións introduza os datos na folla de tal modo:<br> prezo de apertura, prezo máximo, prezo mínimo e prezo do peche.", "SSE.Views.ChartDataDialog.textAdd": "Engadir", "SSE.Views.ChartDataDialog.textCategory": "Etiqueta do eixo horizontal (categoría)", "SSE.Views.ChartDataDialog.textData": "Rango de datos do gráfico", "SSE.Views.ChartDataDialog.textDelete": "Eliminar", "SSE.Views.ChartDataDialog.textDown": "Abaixo", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "<PERSON>ngo de celdas non válido", "SSE.Views.ChartDataDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textSeries": "Entradas da lenda (serie)", "SSE.Views.ChartDataDialog.textSwitch": "Cambiar Fila/Columna", "SSE.Views.ChartDataDialog.textTitle": "Datos do gráfico", "SSE.Views.ChartDataDialog.textUp": "Arriba", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Hai un erro na fórmula inserida.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "A referencia non é válida. Debe facer referencia a unha folla de traballo aberta.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "O número máximo de puntos en serie por gráfico é de 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "O número máximo de series de datos por gráfico é 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "A referencia non é válida. As referencias para títulos, valores, tamaños ou etiquetas de datos deben ser unha única cela, fila ou columna.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Para crear un gráfico, as series deben conter ao menos un valor.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Orde das filas incorrecta. Para crear un gráfico de cotizacións introduza os datos na folla de tal modo:<br> prezo de apertura, prezo máximo, prezo mínimo e prezo do peche.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "<PERSON>ngo de celdas non válido", "SSE.Views.ChartDataRangeDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Rango da etiqueta do eixo", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Nome da serie", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Etiquetas do eixo", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Editar series", "SSE.Views.ChartDataRangeDialog.txtValues": "Valores", "SSE.Views.ChartDataRangeDialog.txtXValues": "Valores X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Valores Y", "SSE.Views.ChartSettings.errorMaxRows": "O número máximo de series de datos por gráfico é 255.", "SSE.Views.ChartSettings.strLineWeight": "G<PERSON>or da liña", "SSE.Views.ChartSettings.strSparkColor": "Cor", "SSE.Views.ChartSettings.strTemplate": "Cadro do persoal", "SSE.Views.ChartSettings.textAdvanced": "Amosar configuración avanzada", "SSE.Views.ChartSettings.textBorderSizeErr": "O valor introducido é incorrecto. <br> Insira un valor numérico entre 0 e 1584 puntos.", "SSE.Views.ChartSettings.textChangeType": "Cambiar tipo", "SSE.Views.ChartSettings.textChartType": "Cambiar tipo de gráfico", "SSE.Views.ChartSettings.textEditData": "Editar datos e ubicación", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON> punto", "SSE.Views.ChartSettings.textHeight": "Altura", "SSE.Views.ChartSettings.textHighPoint": "Punto alto", "SSE.Views.ChartSettings.textKeepRatio": "Proporcións constantes", "SSE.Views.ChartSettings.textLastPoint": "<PERSON><PERSON><PERSON>o", "SSE.Views.ChartSettings.textLowPoint": "<PERSON>unto baixo", "SSE.Views.ChartSettings.textMarkers": "Marcadores", "SSE.Views.ChartSettings.textNegativePoint": "Punto negativo", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Cambiar Fila/Columna", "SSE.Views.ChartSettings.textType": "Tipo", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERRO! O número máximo de puntos en serie por gráfico é 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ERRO! O número máximo de series de datos por gráfico é 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Orde das filas incorrecta. Para crear un gráfico de cotizacións introduza os datos na folla de tal modo:<br> prezo de apertura, prezo máximo, prezo mínimo e prezo do peche.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Non mover nin cambiar tamaño con celdas", "SSE.Views.ChartSettingsDlg.textAlt": "Texto alternativo", "SSE.Views.ChartSettingsDlg.textAltDescription": "Descrición", "SSE.Views.ChartSettingsDlg.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "Automático", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automático para cada", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Cruces do eixo", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Opcións dos eixos", "SSE.Views.ChartSettingsDlg.textAxisPos": "Posición do eixo", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Axustes do eixo", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Entre marcas de gradación", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "Inferior", "SSE.Views.ChartSettingsDlg.textCategoryName": "Nome da categoría", "SSE.Views.ChartSettingsDlg.textCenter": "Ao centro", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementos do gráfico y <br>lenda do gráfico", "SSE.Views.ChartSettingsDlg.textChartTitle": "Título do gráfico", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Personalizar", "SSE.Views.ChartSettingsDlg.textDataColumns": "en columnas", "SSE.Views.ChartSettingsDlg.textDataLabels": "Etiquetas de datos", "SSE.Views.ChartSettingsDlg.textDataRows": "en filas", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON> lenda", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Celdas agochadas e vacías", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Conectar puntos de datos con liñas", "SSE.Views.ChartSettingsDlg.textFit": "Axustar <PERSON> anchura", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Formato da etiqueta", "SSE.Views.ChartSettingsDlg.textGaps": "Intervalos", "SSE.Views.ChartSettingsDlg.textGridLines": "Liñas da cuadrícula", "SSE.Views.ChartSettingsDlg.textGroup": "Agrupar minigráfico", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>o", "SSE.Views.ChartSettingsDlg.textHigh": "Alto", "SSE.Views.ChartSettingsDlg.textHorAxis": "Eixo horizontal", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Eixo horizontal secundario", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100.000.000 ", "SSE.Views.ChartSettingsDlg.textHundreds": "Centenares", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100.000 ", "SSE.Views.ChartSettingsDlg.textIn": "En", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Parte inferior interna", "SSE.Views.ChartSettingsDlg.textInnerTop": "Parte superior interna", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.ChartSettingsDlg.textLabelDist": "Distancia da etiqueta do eixo", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervalo entre etiquetas", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Opcións da etiqueta", "SSE.Views.ChartSettingsDlg.textLabelPos": "Posición da etiqueta", "SSE.Views.ChartSettingsDlg.textLayout": "Deseño", "SSE.Views.ChartSettingsDlg.textLeft": "E<PERSON>rda", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Superposición esquerda", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Inferior", "SSE.Views.ChartSettingsDlg.textLegendLeft": "E<PERSON>rda", "SSE.Views.ChartSettingsDlg.textLegendPos": "Len<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendRight": "Dereita", "SSE.Views.ChartSettingsDlg.textLegendTop": "Parte superior", "SSE.Views.ChartSettingsDlg.textLines": "Liña<PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Intervalo da ubicación", "SSE.Views.ChartSettingsDlg.textLogScale": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "Baixo", "SSE.Views.ChartSettingsDlg.textMajor": "Principal", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON> e menor", "SSE.Views.ChartSettingsDlg.textMajorType": "Tipo principal", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "Marcadores", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervalo entre marcas", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON>or máxi<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON>ipo menor", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON> m<PERSON>", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Xunto ao eixo", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Sen superposición", "SSE.Views.ChartSettingsDlg.textOneCell": "Mover sen cambiar tamaño con celdas", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Marcas de gradación", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "Enriba no exterior", "SSE.Views.ChartSettingsDlg.textOverlay": "Superposición", "SSE.Views.ChartSettingsDlg.textReverse": "Valores en orde inversa", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Orde inverso", "SSE.Views.ChartSettingsDlg.textRight": "Dereita", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Superposición dereita", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "O mesmo para todos", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSeparator": "Separador de etiquetas de datos", "SSE.Views.ChartSettingsDlg.textSeriesName": "Nome da serie", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON> bordos", "SSE.Views.ChartSettingsDlg.textShowData": "Amosar datos en filas e columnas agochadas", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Amosar celdas vacías como", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON> eixo", "SSE.Views.ChartSettingsDlg.textShowValues": "Amosar os valores do gráfico", "SSE.Views.ChartSettingsDlg.textSingle": "Minigráfico único", "SSE.Views.ChartSettingsDlg.textSmooth": "Suave", "SSE.Views.ChartSettingsDlg.textSnap": "Romper a celda", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Intervalos de brillos", "SSE.Views.ChartSettingsDlg.textStraight": "Recto", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10.000.000 ", "SSE.Views.ChartSettingsDlg.textTenThousands": "10.000 ", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Parámetros de marcas de gradación", "SSE.Views.ChartSettingsDlg.textTitle": "Gráfico - Configuración avanzada", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Minigráfico - Configuración avanzada", "SSE.Views.ChartSettingsDlg.textTop": "Parte superior", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Mover e cambiar tamaño con celdas", "SSE.Views.ChartSettingsDlg.textType": "Tipo", "SSE.Views.ChartSettingsDlg.textTypeData": "Tipo e datos", "SSE.Views.ChartSettingsDlg.textUnits": "Unidades de visualización", "SSE.Views.ChartSettingsDlg.textValue": "Valor", "SSE.Views.ChartSettingsDlg.textVertAxis": "Eixo vertical", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Eixo vertical secundario", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Título do eixo X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Título do eixo Y", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Este campo é obrigatorio", "SSE.Views.ChartTypeDialog.errorComboSeries": "Para crear un gráfico combinado, seleccione ao menos dúas series de datos.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "O tipo de gráfico seleccionado require o exio secundario que está usando un gráfico existente. Seleccione outro tipo de gráfico.", "SSE.Views.ChartTypeDialog.textSecondary": "Eixo secundario", "SSE.Views.ChartTypeDialog.textSeries": "Serie", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Tipo de gráfico", "SSE.Views.ChartTypeDialog.textType": "Tipo", "SSE.Views.CreatePivotDialog.textDataRange": "<PERSON><PERSON> de da<PERSON> da orixe", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON><PERSON><PERSON> onde poñer a táboa", "SSE.Views.CreatePivotDialog.textExist": "Folla de cálculo existente", "SSE.Views.CreatePivotDialog.textInvalidRange": "<PERSON>ngo de celdas non válido", "SSE.Views.CreatePivotDialog.textNew": "Folla de cálculo nova", "SSE.Views.CreatePivotDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "SSE.Views.CreatePivotDialog.txtEmpty": "Este campo é obrigatorio", "SSE.Views.CreateSparklineDialog.textDataRange": "<PERSON><PERSON> de da<PERSON> da orixe", "SSE.Views.CreateSparklineDialog.textDestination": "Elixa onde poñer os minográficos", "SSE.Views.CreateSparklineDialog.textInvalidRange": "<PERSON>ngo de celdas non válido", "SSE.Views.CreateSparklineDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CreateSparklineDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.CreateSparklineDialog.txtEmpty": "Este campo é obrigatorio", "SSE.Views.DataTab.capBtnGroup": "Grupo", "SSE.Views.DataTab.capBtnTextCustomSort": "Clasificación personalizada", "SSE.Views.DataTab.capBtnTextDataValidation": "Validación de datos", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Eliminar duplicados", "SSE.Views.DataTab.capBtnTextToCol": "Texto en columnas", "SSE.Views.DataTab.capBtnUngroup": "Desagrupar", "SSE.Views.DataTab.capDataFromText": "Obter datos", "SSE.Views.DataTab.mniFromFile": "Desde o ficheiro TXT/CSV local", "SSE.Views.DataTab.mniFromUrl": "Desde a dirección web do ficheiro TXT/CSV", "SSE.Views.DataTab.textBelow": "Filas resumo debaixo do detalle", "SSE.Views.DataTab.textClear": "<PERSON><PERSON><PERSON> esque<PERSON>", "SSE.Views.DataTab.textColumns": "Desagrupar columnas", "SSE.Views.DataTab.textGroupColumns": "Agrupar columnas", "SSE.Views.DataTab.textGroupRows": "Agrupar filas", "SSE.Views.DataTab.textRightOf": "Columnas resumo á dereita do detalle", "SSE.Views.DataTab.textRows": "Desagrupar filas", "SSE.Views.DataTab.tipCustomSort": "Clasificación personalizada", "SSE.Views.DataTab.tipDataFromText": "Obter datos do ficheiro de texto/CSV", "SSE.Views.DataTab.tipDataValidation": "Validación de datos", "SSE.Views.DataTab.tipGroup": "Agrupar intervalo das células", "SSE.Views.DataTab.tipRemDuplicates": "Eliminar filas duplicadas da folla", "SSE.Views.DataTab.tipToColumns": "Dividir texto de celda en columnas", "SSE.Views.DataTab.tipUngroup": "Desagrupar rango de celdas", "SSE.Views.DataValidationDialog.errorFormula": "O valor avaliado actualmente é un erro. Quere continuar?", "SSE.Views.DataValidationDialog.errorInvalid": "O valor inserido para o campo \"{0}\" é inválido.", "SSE.Views.DataValidationDialog.errorInvalidDate": "A data inserida para o campo \"{0}\" é inválida.", "SSE.Views.DataValidationDialog.errorInvalidList": "A fonte da lista debe ser unha lista delimitada ou unha referencia a unha única fila ou columna.", "SSE.Views.DataValidationDialog.errorInvalidTime": "O tempo inserido para o campo \"{0}\" é inválido.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "O campo \"{1}\" debe ser maior ou igual ao campo \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Debe inserir un valor tanto no campo \"{0}\" como no campo \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Debe inserir un valor no campo \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Non se pode atopar un dos rangos especificados.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Os valores negativos non poden ser utilizados nas condicións \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "O campo \"{0}\" debe ser un valor numérico, expresión numérica ou referirse a unha celda contendo un valor numérico.", "SSE.Views.DataValidationDialog.strError": "Alerta do erro", "SSE.Views.DataValidationDialog.strInput": "Mensaxe da entrada", "SSE.Views.DataValidationDialog.strSettings": "Configuración", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Aplicar estes cambios a todas as demais celdas coa mesma configuración", "SSE.Views.DataValidationDialog.textCellSelected": "Cando a celda está seleccionada, amosar esta mensaxe de entrada", "SSE.Views.DataValidationDialog.textCompare": "Comparar con", "SSE.Views.DataValidationDialog.textData": "Datos", "SSE.Views.DataValidationDialog.textEndDate": "Data final", "SSE.Views.DataValidationDialog.textEndTime": "Hora final", "SSE.Views.DataValidationDialog.textError": "Mensaxe de erro", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "Ignorar o espazo en branco", "SSE.Views.DataValidationDialog.textInput": "Mensaxe da entrada", "SSE.Views.DataValidationDialog.textMax": "Máximo", "SSE.Views.DataValidationDialog.textMessage": "Mensaxe", "SSE.Views.DataValidationDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textShowDropDown": "Amosar a lista despregable na celda", "SSE.Views.DataValidationDialog.textShowError": "Amosar a alerta de erro despois da introducción de datos non válidos", "SSE.Views.DataValidationDialog.textShowInput": "Amosar a mensaxe de entrada cando a celda está seleccionada", "SSE.Views.DataValidationDialog.textSource": "Fonte", "SSE.Views.DataValidationDialog.textStartDate": "Data de inicio", "SSE.Views.DataValidationDialog.textStartTime": "Hora de inicio", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Cando o usuario insire datos non válidos, amosar esta alerta de erro", "SSE.Views.DataValidationDialog.txtAny": "Calquera valor", "SSE.Views.DataValidationDialog.txtBetween": "entre", "SSE.Views.DataValidationDialog.txtDate": "Data", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Tempo transcorrido", "SSE.Views.DataValidationDialog.txtEndDate": "Data final", "SSE.Views.DataValidationDialog.txtEndTime": "Hora final", "SSE.Views.DataValidationDialog.txtEqual": "é igual a", "SSE.Views.DataValidationDialog.txtGreaterThan": "Superior a", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Superior a ou igual a", "SSE.Views.DataValidationDialog.txtLength": "Lonxitude", "SSE.Views.DataValidationDialog.txtLessThan": "inferior a", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Inferior a ou igual a", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "non está entre", "SSE.Views.DataValidationDialog.txtNotEqual": "non é igual a", "SSE.Views.DataValidationDialog.txtOther": "Outro", "SSE.Views.DataValidationDialog.txtStartDate": "Data de inicio", "SSE.Views.DataValidationDialog.txtStartTime": "Hora de inicio", "SSE.Views.DataValidationDialog.txtTextLength": "Lonxitude do texto", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "E", "SSE.Views.DigitalFilterDialog.capCondition1": "i<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition10": "non remata con", "SSE.Views.DigitalFilterDialog.capCondition11": "con<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "non contén", "SSE.Views.DigitalFilterDialog.capCondition2": "non é igual a", "SSE.Views.DigitalFilterDialog.capCondition3": "é superior a", "SSE.Views.DigitalFilterDialog.capCondition4": "é superior ou igual a", "SSE.Views.DigitalFilterDialog.capCondition5": "é inferior a", "SSE.Views.DigitalFilterDialog.capCondition6": "é inferior ou igual a", "SSE.Views.DigitalFilterDialog.capCondition7": "comeza con", "SSE.Views.DigitalFilterDialog.capCondition8": "non comeza con", "SSE.Views.DigitalFilterDialog.capCondition9": "remata con", "SSE.Views.DigitalFilterDialog.capOr": "Ou", "SSE.Views.DigitalFilterDialog.textNoFilter": "sen filtro", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON> filas onde", "SSE.Views.DigitalFilterDialog.textUse1": "Use ? para presentar un caracter", "SSE.Views.DigitalFilterDialog.textUse2": "Use *  para presentar unha serie de caracteres", "SSE.Views.DigitalFilterDialog.txtTitle": "Filtro personalizado", "SSE.Views.DocumentHolder.advancedImgText": "Configuración avanzada da imaxe", "SSE.Views.DocumentHolder.advancedShapeText": "Configuración avanzada da forma", "SSE.Views.DocumentHolder.advancedSlicerText": "Configuración avanzada da segmentación dos datos", "SSE.Views.DocumentHolder.bottomCellText": "Aliñar á parte inferior", "SSE.Views.DocumentHolder.bulletsText": "Viñetas e numeración", "SSE.Views.DocumentHolder.centerCellText": "Aliñar ao medio", "SSE.Views.DocumentHolder.chartDataText": "Seleccionar datos do gráfico", "SSE.Views.DocumentHolder.chartText": "Configuración avanzada do gráfico", "SSE.Views.DocumentHolder.chartTypeText": "Cambiar tipo de gráfico", "SSE.Views.DocumentHolder.deleteColumnText": "Columna", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "Táboa", "SSE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON> texto cara arriba", "SSE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON> texto cara abaixo", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Dirección do texto", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON>na <PERSON>", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> dereita", "SSE.Views.DocumentHolder.insertRowAboveText": "Fila de enriba", "SSE.Views.DocumentHolder.insertRowBelowText": "Fila de abaixo", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "Eliminar hiperligazón", "SSE.Views.DocumentHolder.selectColumnText": "Toda a columna", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON> da <PERSON>a", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "Táboa", "SSE.Views.DocumentHolder.strDelete": "Elimine a sinatura", "SSE.Views.DocumentHolder.strDetails": "Detalles da sinatura", "SSE.Views.DocumentHolder.strSetup": "Preparación da sinatura", "SSE.Views.DocumentHolder.strSign": "Asinar", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "Organizar", "SSE.Views.DocumentHolder.textArrangeBack": "Enviar ao fondo", "SSE.Views.DocumentHolder.textArrangeBackward": "Enviar atrás", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON> adiante", "SSE.Views.DocumentHolder.textArrangeFront": "Traer á fronte", "SSE.Views.DocumentHolder.textAverage": "Promedio", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "Contar", "SSE.Views.DocumentHolder.textCrop": "Recortar", "SSE.Views.DocumentHolder.textCropFill": "Encher", "SSE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textEntriesList": "Seleccionar da lista despregable", "SSE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "SSE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "SSE.Views.DocumentHolder.textFreezePanes": "Inmobilizar paneis", "SSE.Views.DocumentHolder.textFromFile": "Do fi<PERSON>iro", "SSE.Views.DocumentHolder.textFromStorage": "Desde almacenamento", "SSE.Views.DocumentHolder.textFromUrl": "Da URL", "SSE.Views.DocumentHolder.textListSettings": "Configuración da lista", "SSE.Views.DocumentHolder.textMacro": "Asignar macro", "SSE.Views.DocumentHolder.textMax": "Máx", "SSE.Views.DocumentHolder.textMin": "<PERSON><PERSON>.", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON><PERSON> fun<PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "Outros formatos", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numeración", "SSE.Views.DocumentHolder.textReplace": "Substituír imaxe", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Xirar 90° á esquerda", "SSE.Views.DocumentHolder.textRotate90": "Xirar 90° á dereita", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Aliñar á parte inferior", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Aliñar ao centro", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Aliñar á esquerda", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Aliñar ao medio", "SSE.Views.DocumentHolder.textShapeAlignRight": "Aliñar á <PERSON>eita", "SSE.Views.DocumentHolder.textShapeAlignTop": "Aliñar á parte superior", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Mobilizar <PERSON>", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Viñetas de flecha", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Viñetas de marca de verificación", "SSE.Views.DocumentHolder.tipMarkersDash": "Viñetas g<PERSON>", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Rombos recheos", "SSE.Views.DocumentHolder.tipMarkersFRound": "Viñetas redondas recheas", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Viñetas cadradas recheas", "SSE.Views.DocumentHolder.tipMarkersHRound": "Viñetas redondas ocas", "SSE.Views.DocumentHolder.tipMarkersStar": "Viñetas de estrela", "SSE.Views.DocumentHolder.topCellText": "Aliñar á parte superior", "SSE.Views.DocumentHolder.txtAccounting": "Contabilidade", "SSE.Views.DocumentHolder.txtAddComment": "Engadir comentario", "SSE.Views.DocumentHolder.txtAddNamedRange": "Definir nome", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Ascendente", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Axuste automático do ancho da columna", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Axuste automático da altura da fila ", "SSE.Views.DocumentHolder.txtClear": "Limpar", "SSE.Views.DocumentHolder.txtClearAll": "Todos", "SSE.Views.DocumentHolder.txtClearComments": "Comentarios", "SSE.Views.DocumentHolder.txtClearFormat": "Formato", "SSE.Views.DocumentHolder.txtClearHyper": "Hiperligazóns", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Limpar grupos de minigráfico seleccionados", "SSE.Views.DocumentHolder.txtClearSparklines": "Limpar minigráficos seleccionados", "SSE.Views.DocumentHolder.txtClearText": "Тexto", "SSE.Views.DocumentHolder.txtColumn": "Toda a columna", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON><PERSON> ancho de <PERSON>a", "SSE.Views.DocumentHolder.txtCondFormat": "Formato condicional", "SSE.Views.DocumentHolder.txtCopy": "Copiar", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON><PERSON> la<PERSON> da <PERSON>a", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Personalizar altura da columna", "SSE.Views.DocumentHolder.txtCustomSort": "Clasificación personalizada", "SSE.Views.DocumentHolder.txtCut": "Cortar", "SSE.Views.DocumentHolder.txtDate": "Data", "SSE.Views.DocumentHolder.txtDelete": "Eliminar", "SSE.Views.DocumentHolder.txtDescending": "Descendente", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuír horizontalmente", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuír verticalmente", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON> comenta<PERSON>", "SSE.Views.DocumentHolder.txtFilter": "Filtro", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrar por cor da celda", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrar por cor da fonte", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrar por valor da celda seleccionada", "SSE.Views.DocumentHolder.txtFormula": "Inserir función", "SSE.Views.DocumentHolder.txtFraction": "Fracción", "SSE.Views.DocumentHolder.txtGeneral": "Xeral", "SSE.Views.DocumentHolder.txtGroup": "Grupo", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsert": "Inserir", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperligazón", "SSE.Views.DocumentHolder.txtNumber": "Número", "SSE.Views.DocumentHolder.txtNumFormat": "Formato numérico", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercentage": "Porcentaxe", "SSE.Views.DocumentHolder.txtReapply": "Reaplicar", "SSE.Views.DocumentHolder.txtRow": "Toda a fila", "SSE.Views.DocumentHolder.txtRowHeight": "Axustar altura de fila", "SSE.Views.DocumentHolder.txtScientific": "Científico", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON> celdas cara abaixo", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON> celd<PERSON> á esquerda", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON> celd<PERSON> dereita", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON> celdas cara arriba", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON> comenta<PERSON>", "SSE.Views.DocumentHolder.txtSort": "Ordenar", "SSE.Views.DocumentHolder.txtSortCellColor": "Cor seleccionado na parte superior da celda", "SSE.Views.DocumentHolder.txtSortFontColor": "Cor da letra seleccionado na parte superior", "SSE.Views.DocumentHolder.txtSparklines": "Minigráficos", "SSE.Views.DocumentHolder.txtText": "Тexto", "SSE.Views.DocumentHolder.txtTextAdvanced": "Configuración avanzada do parágrafo", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtUngroup": "Desagrupar", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.vertAlignText": "Aliñamento vertical", "SSE.Views.FieldSettingsDialog.strLayout": "Deseño", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotais", "SSE.Views.FieldSettingsDialog.textReport": "Formulario do informe", "SSE.Views.FieldSettingsDialog.textTitle": "Configuración do campo", "SSE.Views.FieldSettingsDialog.txtAverage": "Promedio", "SSE.Views.FieldSettingsDialog.txtBlank": "Inserir filas en branco despois de cada elemento", "SSE.Views.FieldSettingsDialog.txtBottom": "Amosar na parte inferior do grupo", "SSE.Views.FieldSettingsDialog.txtCompact": "Compactar", "SSE.Views.FieldSettingsDialog.txtCount": "Contar", "SSE.Views.FieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Nome personalizado", "SSE.Views.FieldSettingsDialog.txtEmpty": "Amosar elementos sen datos", "SSE.Views.FieldSettingsDialog.txtMax": "Máx", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON><PERSON>.", "SSE.Views.FieldSettingsDialog.txtOutline": "Esquema", "SSE.Views.FieldSettingsDialog.txtProduct": "Produ<PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repetir etiquetas de elementos en cada fila", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Amosar subtotais", "SSE.Views.FieldSettingsDialog.txtSourceName": "<PERSON>me da fonte:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funcións para subtotais", "SSE.Views.FieldSettingsDialog.txtTabular": "Forma da táboa", "SSE.Views.FieldSettingsDialog.txtTop": "Amosar na parte superior do grupo", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Abrir ubicación do ficheiro", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "Crear novo", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> como", "SSE.Views.FileMenu.btnExitCaption": "Saír", "SSE.Views.FileMenu.btnFileOpenCaption": "Abrir", "SSE.Views.FileMenu.btnHelpCaption": "Axuda", "SSE.Views.FileMenu.btnHistoryCaption": "Historial de versións", "SSE.Views.FileMenu.btnInfoCaption": "Info sobre folla de cálculo", "SSE.Views.FileMenu.btnPrintCaption": "Imprimir", "SSE.Views.FileMenu.btnProtectCaption": "Protexer", "SSE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "SSE.Views.FileMenu.btnRenameCaption": "Renomear", "SSE.Views.FileMenu.btnReturnCaption": "Volver á folla de cálculo", "SSE.Views.FileMenu.btnRightsCaption": "Dereitos de acceso", "SSE.Views.FileMenu.btnSaveAsCaption": "Gardar como", "SSE.Views.FileMenu.btnSaveCaption": "Gardar", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Gardar copia como", "SSE.Views.FileMenu.btnSettingsCaption": "Configuracións avanzadas", "SSE.Views.FileMenu.btnToEditCaption": "Editar folla de <PERSON>lo", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Folla de cálculo en branco", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Crear novo", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON> autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Engadir texto", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicativo", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Cambiar dereitos de acceso", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comentario", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificación por", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificación", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Propietario", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Ubicación", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persoas que teñen dereitos", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Subido", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Cambiar dereitos de acceso", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Persoas que teñen dereitos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "O modo Co-edición", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separador decimal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Idioma do dicionario", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Busca das fontes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Idioma da fórmula", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Exemplo: SUMA; MÍNIMO; MÁXIMO; CONTAR", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Omitir p<PERSON> en MAIÚSCULAS", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON> con números", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Configuración das macros", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Amosar o botón Opcións de pegado cando se pegue contido", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Estilo de referencia R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Configuración rexional", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Exemplo: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "<PERSON><PERSON> os comentarios nunha folla", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Amosar os comentarios resoltos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Estrito", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Separador de miles", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unidade de medida", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Usar separadores baseados na configuración rexional", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Valor de ampliación predeterminado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "A cada 10 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "A cada 30 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "A cada 5 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "A cada hora", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Recuperación automática", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Gardar automaticamente", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Desactivado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Gardar versións intermedias", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "A cada minuto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Estilo <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opcións de autocorrección", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Bieloruso", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Búlgaro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Modo de caché predeterminado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centímetro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Colaboración", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Checo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Alemán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editar e gardar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Inglés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Español", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Coedición en tempo real. Todos os cambios se gardarán automaticamente", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesio", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Pulgada", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italiano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Xaponés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Letón", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "coma OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Noruegu<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Ho<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polaco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Corrección", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Punt<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON>ug<PERSON><PERSON> (Brasil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON>ug<PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Rexión", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Activar todo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Activar todas as macros sen notificación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Eslovaco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Esloveno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Desactivar todo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Desactivar todas as macros sen notificación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Usar o bot<PERSON> \"Gardar\" para sincronizar os teus cambios e os dos demais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Sueco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ucraniano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Usar a tecla Alt para navegar pola interface do usuario usando o teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Usar a tecla Opcións para navegar pola interface do usuario usando o teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Amosar notificación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Desactivar todas as macros con notificación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "coma Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON> de t<PERSON>allo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Con contrasinal", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protexer folla de cálculo", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Con sinatura", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar folla de <PERSON>lo", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "A edición eliminará as sinaturas da folla de cálculo<br>Ten a certeza de que quere continuar?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Esta folla de cálculo protexeuse cun contrasinal", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Esta folla de cálculo debe asinarse.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Engadíronse sinaturas válidas á folla de cálculo. A folla de cálculo está protexida contra a edición.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algunhas das sinaturas dixitais da folla de cálculo non son válidas ou non se puideron verificar. A folla de cálculo está protexida contra a edición.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver sinaturas", "SSE.Views.FormatRulesEditDlg.fillColor": "Cor para encher", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Aviso", "SSE.Views.FormatRulesEditDlg.text2Scales": "Escala de 2 cores", "SSE.Views.FormatRulesEditDlg.text3Scales": "Escala de 3 cores", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Todos os bordos", "SSE.Views.FormatRulesEditDlg.textAppearance": "Apariencia da barra", "SSE.Views.FormatRulesEditDlg.textApply": "Aplicar al rango", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automático", "SSE.Views.FormatRulesEditDlg.textAxis": "Eixo", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Dirección da barra", "SSE.Views.FormatRulesEditDlg.textBold": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON> dos bordos", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Estilo do contorno", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bordos inferiores", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Non se pode engadir o formato condicional.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Punto medio de celda", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Bordos verticais interiores", "SSE.Views.FormatRulesEditDlg.textClear": "Limpar", "SSE.Views.FormatRulesEditDlg.textColor": "Cor do texto", "SSE.Views.FormatRulesEditDlg.textContext": "Contexto", "SSE.Views.FormatRulesEditDlg.textCustom": "Personalizado", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON> diagonal descendente", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "<PERSON><PERSON> diagonal ascendente", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Escriba unha fórmula válida.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "A fórmula que inseriu non avalía para un número, data, hora ou cadea.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Escriba un valor.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "O valor inserido non é un número, data, hora ou cadea válidos.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "O valor para o {0} debe ser maior que o valor para o {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Escriba un número entre {0} e {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Encher", "SSE.Views.FormatRulesEditDlg.textFormat": "Formato", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradiente", "SSE.Views.FormatRulesEditDlg.textIconLabel": "cando {0} {1} e", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "cando {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "cando o valor é", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Un ou máis rangos de datos de iconas se superpoñen. <br> Axuste os valores do rango de datos de iconas para que os rangos non se superpoñan.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Bordos interiores", "SSE.Views.FormatRulesEditDlg.textInvalid": "<PERSON><PERSON> de datos inválido", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.FormatRulesEditDlg.textItalic": "Cursiva", "SSE.Views.FormatRulesEditDlg.textItem": "Elemento", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Da esquerda para a dereita", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "<PERSON>a má<PERSON> longa", "SSE.Views.FormatRulesEditDlg.textMaximum": "Máximo", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Punto máxi<PERSON>", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Bordos horizontais interiores", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Punto medio", "SSE.Views.FormatRulesEditDlg.textMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativo", "SSE.Views.FormatRulesEditDlg.textNewColor": "Nova cor personalizada", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON> b<PERSON>", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Un ou máis dos valores especificados non é unha porcentaxe válida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "O valor {0} especificado non é unha porcentaxe válida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Un ou máis dos valores especificados non é un percentil válido.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "O valor especificado {0} non é un percentil válido.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON> externos", "SSE.Views.FormatRulesEditDlg.textPercent": "Por cento", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Posición", "SSE.Views.FormatRulesEditDlg.textPositive": "Positivo", "SSE.Views.FormatRulesEditDlg.textPresets": "Preestablecidos", "SSE.Views.FormatRulesEditDlg.textPreview": "Vista previa", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Non pode empregar referencias relativas en criterios de formato condicional para escalas de cores, barras de datos e conxuntos de iconas.", "SSE.Views.FormatRulesEditDlg.textReverse": "Inverter criterio de ordenación das iconas", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Da dereita á esquerda", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "Regra", "SSE.Views.FormatRulesEditDlg.textSameAs": "Igual que positivo", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textShortBar": "Barra máis curta", "SSE.Views.FormatRulesEditDlg.textShowBar": "<PERSON>ar só a barra", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Amosar icona unicamente", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Este tipo de referencia non se pode usar nunha fórmula de formato condicional. <br> <PERSON>bie a referencia a unha única cela ou use a referencia cunha función de folla de traballo, como = SUM (A1: B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "<PERSON><PERSON><PERSON> nesta p<PERSON>a", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscrito", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Sobrescrito", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Bordos superiores", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Formato numérico", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Contabilidade", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Data", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Este campo é obrigatorio", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fracción", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Xeral", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON>a", "SSE.Views.FormatRulesEditDlg.txtNumber": "Número", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Porcentaxe", "SSE.Views.FormatRulesEditDlg.txtScientific": "Científico", "SSE.Views.FormatRulesEditDlg.txtText": "Тexto", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Editar regrla de formato", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nova regra de formato", "SSE.Views.FormatRulesManagerDlg.guestText": "Convidado(a)", "SSE.Views.FormatRulesManagerDlg.lockText": "Bloqueado", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 desv. est. por enriba do promedio", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 desv. est. por debaixo o promedio", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 desv. est. por enriba do promedio", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 desv. est. por debaixo do promedio", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 desv. est. por enriba do promedio", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 desv. est. por debaixo do promedio", "SSE.Views.FormatRulesManagerDlg.textAbove": "Por enriba do promedio", "SSE.Views.FormatRulesManagerDlg.textApply": "Aplicar a", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "O valor da celda comeza por", "SSE.Views.FormatRulesManagerDlg.textBelow": "Por debaixo da media", "SSE.Views.FormatRulesManagerDlg.textBetween": "<PERSON><PERSON> entre {0} e {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON> da celda", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON><PERSON> de cor graduada", "SSE.Views.FormatRulesManagerDlg.textContains": "O valor da celda contén", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "A celda contén un valor en branco", "SSE.Views.FormatRulesManagerDlg.textContainsError": "A celda contén un erro", "SSE.Views.FormatRulesManagerDlg.textDelete": "Eliminar", "SSE.Views.FormatRulesManagerDlg.textDown": "Mover regra cara abaixo", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicar valores", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "O valor da celda remata con", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Igual ou enriba da media", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Igual ou inferior á média", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formato", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Conxunto de iconas", "SSE.Views.FormatRulesManagerDlg.textNew": "Novo", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "non está entre {0} e {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "O valor da celda non contén", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "A celda non contén un valor en branco", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "A celda non contén ningún erro", "SSE.Views.FormatRulesManagerDlg.textRules": "Regras", "SSE.Views.FormatRulesManagerDlg.textScope": "Amosar regras de formato para", "SSE.Views.FormatRulesManagerDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textSelection": "Selección actual", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Esta tabla pivote", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Esta folla de cálculo", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Esta táboa", "SSE.Views.FormatRulesManagerDlg.textUnique": "Valores únicos", "SSE.Views.FormatRulesManagerDlg.textUp": "Mover regra cara arriba", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Este elemento está sendo editado por outro usuario.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Formato condicional", "SSE.Views.FormatSettingsDialog.textCategory": "Categoría", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Formato", "SSE.Views.FormatSettingsDialog.textLinked": "Vinculado á orixe", "SSE.Views.FormatSettingsDialog.textSeparator": "Usar un separador de 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "Formato numérico", "SSE.Views.FormatSettingsDialog.txtAccounting": "Contabilidade", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Сentésimas (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Metades (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Cuartos (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Octavos (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Personalizar", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Insira coidadosamente o formato de número personalizado. O Editor de follas de cálculo non verifica os formatos personalizados por erros que poidan afectar ao ficheiro xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Data", "SSE.Views.FormatSettingsDialog.txtFraction": "Fracción", "SSE.Views.FormatSettingsDialog.txtGeneral": "Xeral", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "Número", "SSE.Views.FormatSettingsDialog.txtPercentage": "Porcentaxe", "SSE.Views.FormatSettingsDialog.txtSample": "Exemplo:", "SSE.Views.FormatSettingsDialog.txtScientific": "Científico", "SSE.Views.FormatSettingsDialog.txtText": "Тexto", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Ata un díxito (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Ata dous díxitos (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Ata tres díxitos (131/135)", "SSE.Views.FormulaDialog.sDescription": "Descrición", "SSE.Views.FormulaDialog.textGroupDescription": "Seleccionar grupo de función", "SSE.Views.FormulaDialog.textListDescription": "Seleccionar función", "SSE.Views.FormulaDialog.txtRecommended": "Recomendado", "SSE.Views.FormulaDialog.txtSearch": "Buscar", "SSE.Views.FormulaDialog.txtTitle": "Inserir función", "SSE.Views.FormulaTab.textAutomatic": "Automático", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calcular a folla actual", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calcular libro de traballo", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Calcular", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calcular todo o libro de traballo", "SSE.Views.FormulaTab.txtAdditional": "Adicional", "SSE.Views.FormulaTab.txtAutosum": "Autosuma", "SSE.Views.FormulaTab.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Función", "SSE.Views.FormulaTab.txtFormulaTip": "Inserir función", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON><PERSON> fun<PERSON>", "SSE.Views.FormulaTab.txtRecent": "Usados recientemente", "SSE.Views.FormulaWizard.textAny": "calquera", "SSE.Views.FormulaWizard.textArgument": "Argumento", "SSE.Views.FormulaWizard.textFunction": "Función", "SSE.Views.FormulaWizard.textFunctionRes": "Resultado da función", "SSE.Views.FormulaWizard.textHelp": "Axuda sobre esta función", "SSE.Views.FormulaWizard.textLogical": "Lóxica", "SSE.Views.FormulaWizard.textNoArgs": "Esta función non ten argumentos", "SSE.Views.FormulaWizard.textNumber": "Número", "SSE.Views.FormulaWizard.textRef": "referencia", "SSE.Views.FormulaWizard.textText": "texto", "SSE.Views.FormulaWizard.textTitle": "Argumentos da función", "SSE.Views.FormulaWizard.textValue": "Resultado da fórmula", "SSE.Views.HeaderFooterDialog.textAlign": "<PERSON><PERSON><PERSON> coas marxes da páxina", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON> as p<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textBold": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "Ao centro", "SSE.Views.HeaderFooterDialog.textColor": "Cor do texto", "SSE.Views.HeaderFooterDialog.textDate": "Data", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Primeira páxina diferente", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Páxinas pares e impares diferentes", "SSE.Views.HeaderFooterDialog.textEven": "Páxina par", "SSE.Views.HeaderFooterDialog.textFileName": "Nome do ficheiro", "SSE.Views.HeaderFooterDialog.textFirst": "Primeira páxina", "SSE.Views.HeaderFooterDialog.textFooter": "Rodapé", "SSE.Views.HeaderFooterDialog.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textInsert": "Inserir", "SSE.Views.HeaderFooterDialog.textItalic": "Cursiva", "SSE.Views.HeaderFooterDialog.textLeft": "E<PERSON>rda", "SSE.Views.HeaderFooterDialog.textMaxError": "O texto é demasiado longo. Reduza o número de caracteres usados.", "SSE.Views.HeaderFooterDialog.textNewColor": "Nova cor personalizada", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON>xina impar", "SSE.Views.HeaderFooterDialog.textPageCount": "Número de páxinas", "SSE.Views.HeaderFooterDialog.textPageNum": "Númer<PERSON> da páxina", "SSE.Views.HeaderFooterDialog.textPresets": "Preestablecidos", "SSE.Views.HeaderFooterDialog.textRight": "Dereita", "SSE.Views.HeaderFooterDialog.textScale": "Escalar con documento", "SSE.Views.HeaderFooterDialog.textSheet": "Nome da folla", "SSE.Views.HeaderFooterDialog.textStrikeout": "Riscado", "SSE.Views.HeaderFooterDialog.textSubscript": "Subscrito", "SSE.Views.HeaderFooterDialog.textSuperscript": "Sobrescrito", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Configuración da cabeceira/rodapé", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "Fonte", "SSE.Views.HeaderFooterDialog.tipFontSize": "<PERSON><PERSON><PERSON>e", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Vincular a", "SSE.Views.HyperlinkSettingsDialog.strRange": "Ra<PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copiar", "SSE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Insira lenda aquí", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Insira ligazón aquí", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserir información sobre ferramentas aquí", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Ligazón externa", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Obter ligazón", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Rango de datos interno", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON> definidos", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Información en pantalla", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Configuración da hiperligazón", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatorio", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo debe ser unha URL no formato \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres. ", "SSE.Views.ImageSettings.textAdvanced": "Amosar configuración avanzada", "SSE.Views.ImageSettings.textCrop": "Recortar", "SSE.Views.ImageSettings.textCropFill": "Encher", "SSE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropToShape": "Cortar para dar forma", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Editar obxecto", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "Do fi<PERSON>iro", "SSE.Views.ImageSettings.textFromStorage": "Desde almacenamento", "SSE.Views.ImageSettings.textFromUrl": "Da URL", "SSE.Views.ImageSettings.textHeight": "Altura", "SSE.Views.ImageSettings.textHint270": "Xirar 90° á esquerda", "SSE.Views.ImageSettings.textHint90": "Xirar 90° á dereita", "SSE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "SSE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "SSE.Views.ImageSettings.textInsert": "Substituír imaxe", "SSE.Views.ImageSettings.textKeepRatio": "Proporcións constantes", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "Usados recentemente", "SSE.Views.ImageSettings.textRotate90": "Xirar 90°", "SSE.Views.ImageSettings.textRotation": "Rotación", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Non mover nin cambiar tamaño con celdas", "SSE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Descrición", "SSE.Views.ImageSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Volteado", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Mover sen cambiar tamaño con celdas", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotación", "SSE.Views.ImageSettingsAdvanced.textSnap": "Romper a celda", "SSE.Views.ImageSettingsAdvanced.textTitle": "Imaxe - configuración avanzada", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Mover e cambiar tamaño con celdas", "SSE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.LeftMenu.tipAbout": "Sobre", "SSE.Views.LeftMenu.tipChat": "Conversa", "SSE.Views.LeftMenu.tipComments": "Comentarios", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Extensións", "SSE.Views.LeftMenu.tipSearch": "Buscar", "SSE.Views.LeftMenu.tipSpellcheck": "Сorrección ortográfica", "SSE.Views.LeftMenu.tipSupport": "Comentarios e soporte", "SSE.Views.LeftMenu.txtDeveloper": "MODO DO DESENVOLVEDOR", "SSE.Views.LeftMenu.txtEditor": "Editor de follas de cálculo", "SSE.Views.LeftMenu.txtLimit": "Limitar acceso", "SSE.Views.LeftMenu.txtTrial": "MODO DE PROBA", "SSE.Views.LeftMenu.txtTrialDev": "Modo de programador de proba", "SSE.Views.MacroDialog.textMacro": "Nome da macro", "SSE.Views.MacroDialog.textTitle": "Asignar macro", "SSE.Views.MainSettingsPrint.okButtonText": "Gardar", "SSE.Views.MainSettingsPrint.strBottom": "Inferior", "SSE.Views.MainSettingsPrint.strLandscape": "Horizontal", "SSE.Views.MainSettingsPrint.strLeft": "E<PERSON>rda", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Retrato ", "SSE.Views.MainSettingsPrint.strPrint": "Imprimir", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strRight": "Dereita", "SSE.Views.MainSettingsPrint.strTop": "Parte superior", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "Personalizado", "SSE.Views.MainSettingsPrint.textCustomOptions": "Opcións personalizadas", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON><PERSON><PERSON> as columnas nunha páxina", "SSE.Views.MainSettingsPrint.textFitPage": "Caber a folla nunha páxina", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON><PERSON> as filas nunha p<PERSON>a", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientación da páxina", "SSE.Views.MainSettingsPrint.textPageScaling": "Escala", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON> da páxina", "SSE.Views.MainSettingsPrint.textPrintGrid": "Imprim<PERSON> cu<PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Imprimir títulos de filas e columnas", "SSE.Views.MainSettingsPrint.textRepeat": "Repetir...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repetir columnas á esquerda", "SSE.Views.MainSettingsPrint.textRepeatTop": "Repetir filas na parte superior", "SSE.Views.MainSettingsPrint.textSettings": "Configuración para", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Os intervalos nomeados existentes non se poden editar e os novos non se poden crear <br> neste momento, xa que algúns deles están sendo editados.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Nome definido", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Aviso", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Libro de traballo", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textExistName": "ERROR! Intervalo con este nome xa existe", "SSE.Views.NamedRangeEditDlg.textInvalidName": "O nome debe comezar cunha letra ou un guión baixo e non debe conter caracteres non válidos.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERRO! Intervalo de celda inválido", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERRO! O elemento está sendo editado por outro usuario.", "SSE.Views.NamedRangeEditDlg.textName": "Nome", "SSE.Views.NamedRangeEditDlg.textReservedName": "O nome que está tratando de usar xa se fai referencia nas fórmulas da celda. Por favor, seleccione outro nome.", "SSE.Views.NamedRangeEditDlg.textScope": "Alcance", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Este campo é obrigatorio", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Editar nome", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Novo nome", "SSE.Views.NamedRangePasteDlg.textNames": "Intervalos nomeados", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON>egar nome", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Convidado(a)", "SSE.Views.NameManagerDlg.lockText": "Bloqueado", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDelete": "Eliminar", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "<PERSON><PERSON><PERSON> non se crearon rangos con nome. <br> Cree polo menos un rango con nome e aparecerá neste campo.", "SSE.Views.NameManagerDlg.textFilter": "Filtro", "SSE.Views.NameManagerDlg.textFilterAll": "Todos", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON> definidos", "SSE.Views.NameManagerDlg.textFilterSheet": "Nomes reducidos a folla", "SSE.Views.NameManagerDlg.textFilterTableNames": "Nomes da táboa", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Nomes aplicados ao libro de traballo", "SSE.Views.NameManagerDlg.textNew": "Novo", "SSE.Views.NameManagerDlg.textnoNames": "Non se atopou ningún intervalo con nome que coincida co seu filtro.", "SSE.Views.NameManagerDlg.textRanges": "Intervalos nomeados", "SSE.Views.NameManagerDlg.textScope": "Alcance", "SSE.Views.NameManagerDlg.textWorkbook": "Libro de traballo", "SSE.Views.NameManagerDlg.tipIsLocked": "Este elemento está sendo editado por outro usuario.", "SSE.Views.NameManagerDlg.txtTitle": "Xestor do nome", "SSE.Views.NameManagerDlg.warnDelete": "Ten a certeza de que quere borrar o nome {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Inferior", "SSE.Views.PageMarginsDialog.textLeft": "E<PERSON>rda", "SSE.Views.PageMarginsDialog.textRight": "Dereita", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Parte superior", "SSE.Views.ParagraphSettings.strLineHeight": "Espazo entre liñas", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Espazo entre parágrafos", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Amosar configuración avanzada", "SSE.Views.ParagraphSettings.textAt": "En", "SSE.Views.ParagraphSettings.textAtLeast": "Polo menos", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Exactamente", "SSE.Views.ParagraphSettings.txtAutoText": "Automático", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "As lapelas especificadas aparecerán neste campo", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Todas en maiúsculas", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Dobre riscado", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Reti<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espazo entre liñas", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Dereita", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Por", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fonte", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Sangrías e espazo", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Maiús<PERSON>s pequenas", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Espazo", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Riscado", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espazo entre caracteres", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON><PERSON> predeterminada", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efectos", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exactamente", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira liña", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendido", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Xustificado", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ningún)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Eliminar", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Eliminar todo", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Ao centro", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posición da lapela", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Dereita", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Configuración avanzada", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "é igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "non remata con", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "con<PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "non contén", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "entre", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "non está entre", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "non é igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "é superior a", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "é superior ou igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "é inferior a", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "é inferior ou igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "comeza con", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "non comeza con", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "remata con", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Amosar elementos para os que a etiqueta:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Amosar elementos para os que:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Use ? para presentar un caracter", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Use *  para presentar unha serie de caracteres", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "e", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtrar por etiqueta", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtro de valor", "SSE.Views.PivotGroupDialog.textAuto": "Automático", "SSE.Views.PivotGroupDialog.textBy": "Por", "SSE.Views.PivotGroupDialog.textDays": "Días", "SSE.Views.PivotGroupDialog.textEnd": "Rematar en", "SSE.Views.PivotGroupDialog.textError": "Este campo debe ser un valor numérico", "SSE.Views.PivotGroupDialog.textGreaterError": "O número final debe ser maior que o número inicial", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "Meses", "SSE.Views.PivotGroupDialog.textNumDays": "Número dos días", "SSE.Views.PivotGroupDialog.textQuart": "Trimestres", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "<PERSON><PERSON> en", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Agrupación", "SSE.Views.PivotSettings.textAdvanced": "Amosar configuración avanzada", "SSE.Views.PivotSettings.textColumns": "Columnas", "SSE.Views.PivotSettings.textFields": "Seleccione campos", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Valores", "SSE.Views.PivotSettings.txtAddColumn": "Engadir a Columnas", "SSE.Views.PivotSettings.txtAddFilter": "Engadir a Filtros", "SSE.Views.PivotSettings.txtAddRow": "Engadir a Filas", "SSE.Views.PivotSettings.txtAddValues": "Engadir a Valores", "SSE.Views.PivotSettings.txtFieldSettings": "Configuración do campo", "SSE.Views.PivotSettings.txtMoveBegin": "Mover ao principio", "SSE.Views.PivotSettings.txtMoveColumn": "Mover <PERSON>a", "SSE.Views.PivotSettings.txtMoveDown": "Mover cara a<PERSON><PERSON>o", "SSE.Views.PivotSettings.txtMoveEnd": "Mover á fin", "SSE.Views.PivotSettings.txtMoveFilter": "Mover a filtros", "SSE.Views.PivotSettings.txtMoveRow": "Mover a filas", "SSE.Views.PivotSettings.txtMoveUp": "Mover cara arriba", "SSE.Views.PivotSettings.txtMoveValues": "Mover a valores", "SSE.Views.PivotSettings.txtRemove": "Eliminar campo", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nome e deseño", "SSE.Views.PivotSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Descrición", "SSE.Views.PivotSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Fonte de datos", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Amosar campos en área de filtro de informe", "SSE.Views.PivotSettingsAdvanced.textDown": "Cara a<PERSON>o, despois horizontalmente", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Totais", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Cabeceira do campo", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.PivotSettingsAdvanced.textOver": "Horizontalmente, despois cara abaixo", "SSE.Views.PivotSettingsAdvanced.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Amosar para columnas", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Amosar cabeceiras de campo para filas e columnas", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Amosar para filas", "SSE.Views.PivotSettingsAdvanced.textTitle": "Táboa dinámica - Configuración avanzada", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Campos de filtro de informe por columna", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Campos de filtro de informe por fila", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Este campo é obrigatorio", "SSE.Views.PivotSettingsAdvanced.txtName": "Nome", "SSE.Views.PivotTable.capBlankRows": "<PERSON>las en branco", "SSE.Views.PivotTable.capGrandTotals": "Totais", "SSE.Views.PivotTable.capLayout": "<PERSON><PERSON><PERSON> des<PERSON>", "SSE.Views.PivotTable.capSubtotals": "Subtotais", "SSE.Views.PivotTable.mniBottomSubtotals": "Amosar todos os subtotais á fin do grupo", "SSE.Views.PivotTable.mniInsertBlankLine": "Inserir liña en branco despois de cada obxecto", "SSE.Views.PivotTable.mniLayoutCompact": "Amosar de forma compacta", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Non repita as etiquetas dos obxectos", "SSE.Views.PivotTable.mniLayoutOutline": "Amosar en forma de esquema", "SSE.Views.PivotTable.mniLayoutRepeat": "<PERSON><PERSON><PERSON> to<PERSON> as etiquetas dos elementos", "SSE.Views.PivotTable.mniLayoutTabular": "Amosar en forma de táboa", "SSE.Views.PivotTable.mniNoSubtotals": "Non mostre os subtotais", "SSE.Views.PivotTable.mniOffTotals": "Desactivado para filas e columnas", "SSE.Views.PivotTable.mniOnColumnsTotals": "Activado só para columnas", "SSE.Views.PivotTable.mniOnRowsTotals": "Activado só para filas", "SSE.Views.PivotTable.mniOnTotals": "Activado para filas e columnas", "SSE.Views.PivotTable.mniRemoveBlankLine": "Eliminar liña en branco despois de cada obxecto", "SSE.Views.PivotTable.mniTopSubtotals": "Amosar todos os subtotais ao principio do grupo", "SSE.Views.PivotTable.textColBanded": "Columnas con bandas", "SSE.Views.PivotTable.textColHeader": "Cabeceira da columna", "SSE.Views.PivotTable.textRowBanded": "Filas con bandas", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>la", "SSE.Views.PivotTable.tipCreatePivot": "Inserir táboa dinámica", "SSE.Views.PivotTable.tipGrandTotals": "<PERSON><PERSON> ou agochar totais", "SSE.Views.PivotTable.tipRefresh": "Actualizar a información da orixe dos datos", "SSE.Views.PivotTable.tipSelect": "Seleccione toda a táboa de pivote", "SSE.Views.PivotTable.tipSubtotals": "<PERSON><PERSON> ou agochar subtotais", "SSE.Views.PivotTable.txtCreate": "Inserir táboa", "SSE.Views.PivotTable.txtPivotTable": "Táboa pivote", "SSE.Views.PivotTable.txtRefresh": "Actualizar", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.btnDownload": "Guardar e descargar", "SSE.Views.PrintSettings.btnPrint": "Guardar e imprimir", "SSE.Views.PrintSettings.strBottom": "Inferior", "SSE.Views.PrintSettings.strLandscape": "Horizontal", "SSE.Views.PrintSettings.strLeft": "E<PERSON>rda", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Retrato ", "SSE.Views.PrintSettings.strPrint": "Imprimir", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "Dereita", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "Parte superior", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON> as follas", "SSE.Views.PrintSettings.textCurrentSheet": "Folla actual", "SSE.Views.PrintSettings.textCustom": "Personalizado", "SSE.Views.PrintSettings.textCustomOptions": "Opcións personalizadas", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON><PERSON><PERSON> as columnas nunha páxina", "SSE.Views.PrintSettings.textFitPage": "Caber a folla nunha páxina", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON><PERSON> as filas nunha p<PERSON>a", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorar área de impresión", "SSE.Views.PrintSettings.textLayout": "Deseño", "SSE.Views.PrintSettings.textPageOrientation": "Orientación da páxina", "SSE.Views.PrintSettings.textPageScaling": "Escala", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON> da páxina", "SSE.Views.PrintSettings.textPrintGrid": "Imprim<PERSON> cu<PERSON>", "SSE.Views.PrintSettings.textPrintHeadings": "Imprimir títulos de filas e columnas", "SSE.Views.PrintSettings.textPrintRange": "Área de impresión", "SSE.Views.PrintSettings.textRange": "Ra<PERSON>", "SSE.Views.PrintSettings.textRepeat": "Repetir...", "SSE.Views.PrintSettings.textRepeatLeft": "Repetir columnas á esquerda", "SSE.Views.PrintSettings.textRepeatTop": "Repetir filas na parte superior", "SSE.Views.PrintSettings.textSelection": "Selección", "SSE.Views.PrintSettings.textSettings": "Configuración da folla", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "Amosar liñas de cuadrícula", "SSE.Views.PrintSettings.textShowHeadings": "Amosar títulos de filas e columnas", "SSE.Views.PrintSettings.textTitle": "Opcións de impresión", "SSE.Views.PrintSettings.textTitlePDF": "Configuración do PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "Primeira columna", "SSE.Views.PrintTitlesDialog.textFirstRow": "Primeira fila", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Columnas inmobilizadas", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Filas inmobilizadas ", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.PrintTitlesDialog.textLeft": "Repetir columnas á esquerda", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Non repetir", "SSE.Views.PrintTitlesDialog.textRepeat": "Repetir...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>o", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTop": "Repetir filas na parte superior", "SSE.Views.PrintWithPreview.txtActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON> as follas", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Aplicar a todas as follas", "SSE.Views.PrintWithPreview.txtBottom": "Inferior", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Folla actual", "SSE.Views.PrintWithPreview.txtCustom": "Personalizar", "SSE.Views.PrintWithPreview.txtCustomOptions": "Opcións personalizadas", "SSE.Views.PrintWithPreview.txtEmptyTable": "Non hai nada para imprimir porque a táboa está baleira", "SSE.Views.PrintWithPreview.txtFitCols": "<PERSON><PERSON><PERSON><PERSON> as columnas nunha páxina", "SSE.Views.PrintWithPreview.txtFitPage": "Caber a folla nunha páxina", "SSE.Views.PrintWithPreview.txtFitRows": "<PERSON><PERSON><PERSON> as filas nunha p<PERSON>a", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Liña de cuadrículas e cabeceiras", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Configuración da cabeceira/rodapé", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorar área de impresión", "SSE.Views.PrintWithPreview.txtLandscape": "Orientación horizontal", "SSE.Views.PrintWithPreview.txtLeft": "Á esquerda", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "de {0}", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Número da páxina inválido", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientación da páxina", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON> da páxina", "SSE.Views.PrintWithPreview.txtPortrait": "Vertical", "SSE.Views.PrintWithPreview.txtPrint": "Imprimir", "SSE.Views.PrintWithPreview.txtPrintGrid": "Imprim<PERSON> cu<PERSON>", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Imprimir títulos de filas e columnas", "SSE.Views.PrintWithPreview.txtPrintRange": "Área de impresión", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtRepeat": "Repetir...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repetir columnas á esquerda", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repetir filas na parte superior", "SSE.Views.PrintWithPreview.txtRight": "<PERSON> dereita", "SSE.Views.PrintWithPreview.txtSave": "Gardar", "SSE.Views.PrintWithPreview.txtScaling": "Escala", "SSE.Views.PrintWithPreview.txtSelection": "Selección", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Configuración da folla", "SSE.Views.PrintWithPreview.txtSheet": "Folla: {0}", "SSE.Views.PrintWithPreview.txtTop": "Parte superior", "SSE.Views.ProtectDialog.textExistName": "ERRO! Xa existe un intervalo con este título", "SSE.Views.ProtectDialog.textInvalidName": "O título do intervalo debe comezar cunha letra e só pode conter letras, números e espazos.", "SSE.Views.ProtectDialog.textInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.ProtectDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtAllow": "Permitir a todos os usuarios desta folla", "SSE.Views.ProtectDialog.txtAutofilter": "Usar Autofiltro", "SSE.Views.ProtectDialog.txtDelCols": "Eliminar columnas", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "Este campo é obrigatorio", "SSE.Views.ProtectDialog.txtFormatCells": "Aplicar formato a celdas", "SSE.Views.ProtectDialog.txtFormatCols": "Aplicar formato a columnas", "SSE.Views.ProtectDialog.txtFormatRows": "Aplicar formato a filas", "SSE.Views.ProtectDialog.txtIncorrectPwd": "O contrasinal de confirmación non é idéntico", "SSE.Views.ProtectDialog.txtInsCols": "Inserir columnas", "SSE.Views.ProtectDialog.txtInsHyper": "Inserir <PERSON>", "SSE.Views.ProtectDialog.txtInsRows": "Inserir filas", "SSE.Views.ProtectDialog.txtObjs": "Editar obxectos", "SSE.Views.ProtectDialog.txtOptional": "opcional", "SSE.Views.ProtectDialog.txtPassword": "Contrasinal", "SSE.Views.ProtectDialog.txtPivot": "Utilizar táboa e gráfico dinámicos", "SSE.Views.ProtectDialog.txtProtect": "Protexer", "SSE.Views.ProtectDialog.txtRange": "Ra<PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "Repetir o contrasinal", "SSE.Views.ProtectDialog.txtScen": "<PERSON>ar es<PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "Seleccionar celdas bloqueadas", "SSE.Views.ProtectDialog.txtSelUnLocked": "Seleccionar celdas desbloque<PERSON>s", "SSE.Views.ProtectDialog.txtSheetDescription": "Evita os cambios non desexados doutras persoas limitando a súa capacidade de edición.", "SSE.Views.ProtectDialog.txtSheetTitle": "Protexer folla", "SSE.Views.ProtectDialog.txtSort": "Ordenar", "SSE.Views.ProtectDialog.txtWarning": "Aviso: se perde ou esquece o contrasinal, non se poderá recuperar. Consérvao nun lugar seguro.", "SSE.Views.ProtectDialog.txtWBDescription": "Para evitar que outros usuarios vexan follas de traballo ocultas, engadan, movan, eliminen ou oculten follas de traballo e cambien o nome de follas de traballo, pode protexer a estrutura do seu libro cun contrasinal.", "SSE.Views.ProtectDialog.txtWBTitle": "Protexer a estrutura do libro", "SSE.Views.ProtectRangesDlg.guestText": "Convidado(a)", "SSE.Views.ProtectRangesDlg.lockText": "Bloqueado", "SSE.Views.ProtectRangesDlg.textDelete": "Eliminar", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Non hai ningún intervalo permitido para a edición.", "SSE.Views.ProtectRangesDlg.textNew": "Novo", "SSE.Views.ProtectRangesDlg.textProtect": "Protexer folla", "SSE.Views.ProtectRangesDlg.textPwd": "Contrasinal", "SSE.Views.ProtectRangesDlg.textRange": "Ra<PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Rangos desbloqueados por un contrasinal cando a folla está protexida (isto funciona só para as celdas bloqueadas)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Este elemento está sendo editado por outro usuario.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "Novo rango", "SSE.Views.ProtectRangesDlg.txtNo": "Non", "SSE.Views.ProtectRangesDlg.txtTitle": "Permitir aos usuarios editar rangos", "SSE.Views.ProtectRangesDlg.txtYes": "Si", "SSE.Views.ProtectRangesDlg.warnDelete": "Ten a certeza de que quere borrar o nome {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Columnas", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Para eliminar os valores duplicados, seleccione unha ou máis columnas que conteñen duplicados.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Os meus datos teñen cabeceiras", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Eliminar duplicados", "SSE.Views.RightMenu.txtCellSettings": "Configuración da celda", "SSE.Views.RightMenu.txtChartSettings": "Configuración do gráfico", "SSE.Views.RightMenu.txtImageSettings": "Configuración da imaxe", "SSE.Views.RightMenu.txtParagraphSettings": "Configuración do parágrafo", "SSE.Views.RightMenu.txtPivotSettings": "Configuración da táboa dinámica", "SSE.Views.RightMenu.txtSettings": "Configuración común", "SSE.Views.RightMenu.txtShapeSettings": "Configuración da forma", "SSE.Views.RightMenu.txtSignatureSettings": "Configuración da sinatura", "SSE.Views.RightMenu.txtSlicerSettings": "Configuración da segmentación dos datos", "SSE.Views.RightMenu.txtSparklineSettings": "Configuración de minigráfico", "SSE.Views.RightMenu.txtTableSettings": "Configuración da táboa", "SSE.Views.RightMenu.txtTextArtSettings": "Configuración do Text Art ", "SSE.Views.ScaleDialog.textAuto": "Automático", "SSE.Views.ScaleDialog.textError": "O valor inserido é incorreto.", "SSE.Views.ScaleDialog.textFewPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Axustar a", "SSE.Views.ScaleDialog.textHeight": "Altura", "SSE.Views.ScaleDialog.textManyPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Axustar a", "SSE.Views.ScaleDialog.textTitle": "Configuración da escala", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "O valor máximo para este campo é {0}", "SSE.Views.SetValueDialog.txtMinText": "O valor mínimo para este campo é {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON> <PERSON> fondo", "SSE.Views.ShapeSettings.strChange": "Cambiar autoforma", "SSE.Views.ShapeSettings.strColor": "Cor", "SSE.Views.ShapeSettings.strFill": "Encher", "SSE.Views.ShapeSettings.strForeground": "Cor de primeiro plano", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON> sombra", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "Liña", "SSE.Views.ShapeSettings.strTransparency": "Opacidade", "SSE.Views.ShapeSettings.strType": "Tipo", "SSE.Views.ShapeSettings.textAdvanced": "Amosar configuración avanzada", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "O valor inserido é incorrecto. <br> Insira un valor numérico entre 0 e 1584 puntos.", "SSE.Views.ShapeSettings.textColor": "Cor para encher", "SSE.Views.ShapeSettings.textDirection": "Dirección ", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON>", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "Do fi<PERSON>iro", "SSE.Views.ShapeSettings.textFromStorage": "Desde almacenamento", "SSE.Views.ShapeSettings.textFromUrl": "Da URL", "SSE.Views.ShapeSettings.textGradient": "Puntos de degradado ", "SSE.Views.ShapeSettings.textGradientFill": "Recheo degradado", "SSE.Views.ShapeSettings.textHint270": "Xirar 90° á esquerda", "SSE.Views.ShapeSettings.textHint90": "Xirar 90° á dereita", "SSE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "SSE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "SSE.Views.ShapeSettings.textImageTexture": "Imaxe ou textura", "SSE.Views.ShapeSettings.textLinear": "Lineal", "SSE.Views.ShapeSettings.textNoFill": "Sen encher", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Posición", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Usados recentemente", "SSE.Views.ShapeSettings.textRotate90": "Xirar 90°", "SSE.Views.ShapeSettings.textRotation": "Rotación", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON><PERSON> imaxe", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "De textura", "SSE.Views.ShapeSettings.textTile": "Mosaico", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Engadir punto de degradado", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "Cartón", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "SSE.Views.ShapeSettings.txtGrain": "Grano", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Papel gris", "SSE.Views.ShapeSettings.txtKnit": "Texido", "SSE.Views.ShapeSettings.txtLeather": "Coiro", "SSE.Views.ShapeSettings.txtNoBorders": "Sen liña", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Madeira", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Columnas", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Marxes interiores", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Non mover nin cambiar tamaño con celdas", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrición", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Autoaxustar", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Tam<PERSON>ño inicial", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Inferior", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Número de columnas", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Tamaño final", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Volteado", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de combinación", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporcións constantes", "SSE.Views.ShapeSettingsAdvanced.textLeft": "E<PERSON>rda", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Mover sen cambiar tamaño con celdas", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Permitir que o texto exceda a forma", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "A<PERSON>ust<PERSON> ta<PERSON> da forma ao texto", "SSE.Views.ShapeSettingsAdvanced.textRight": "Dereita", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotación", "SSE.Views.ShapeSettingsAdvanced.textRound": "Redondado", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Romper a celda", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Espazo entre columnas", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Cadrado", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Caixa de texto", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma - Configuración avanzada", "SSE.Views.ShapeSettingsAdvanced.textTop": "Parte superior", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Mover e cambiar tamaño con celdas", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Grosores e frechas", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "SSE.Views.SignatureSettings.strDelete": "Elimine a sinatura", "SSE.Views.SignatureSettings.strDetails": "Detalles da sinatura", "SSE.Views.SignatureSettings.strInvalid": "Sinaturas inválidas", "SSE.Views.SignatureSettings.strRequested": "Asinaturas requiridas", "SSE.Views.SignatureSettings.strSetup": "Preparación da sinatura", "SSE.Views.SignatureSettings.strSign": "Asinar", "SSE.Views.SignatureSettings.strSignature": "Asinatura", "SSE.Views.SignatureSettings.strSigner": "Asinante", "SSE.Views.SignatureSettings.strValid": "Sinaturas válidas", "SSE.Views.SignatureSettings.txtContinueEditing": "Editar de todas maneira<PERSON>", "SSE.Views.SignatureSettings.txtEditWarning": "A edición eliminará as sinaturas da folla de cálculo<br>Ten a certeza de que quere continuar?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Desexa eliminar esta sinatura?<br> Non se pode desfacer.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Esta folla de cálculo debe asinarse.", "SSE.Views.SignatureSettings.txtSigned": "Engadíronse sinaturas válidas á folla de cálculo. A folla de cálculo está protexida contra a edición.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Algunhas das sinaturas dixitais da folla de cálculo non son válidas ou non se puideron verificar. A folla de cálculo está protexida contra a edición.", "SSE.Views.SlicerAddDialog.textColumns": "Columnas", "SSE.Views.SlicerAddDialog.txtTitle": "Inserir segmentacións dos datos", "SSE.Views.SlicerSettings.strHideNoData": "Agochar elementos sen datos", "SSE.Views.SlicerSettings.strIndNoData": "Indicar visualmente os elementos sen datos", "SSE.Views.SlicerSettings.strShowDel": "Amosar elementos eliminados da orixe de datos", "SSE.Views.SlicerSettings.strShowNoData": "Amosar elementos sen datos á fin", "SSE.Views.SlicerSettings.strSorting": "Ordenar e filtrar", "SSE.Views.SlicerSettings.textAdvanced": "Amosar configuración avanzada", "SSE.Views.SlicerSettings.textAsc": "Ascendente", "SSE.Views.SlicerSettings.textAZ": "De A a Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "Columnas", "SSE.Views.SlicerSettings.textDesc": "Descendente", "SSE.Views.SlicerSettings.textHeight": "Altura", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Proporcións constantes", "SSE.Views.SlicerSettings.textLargeSmall": "do maior para o menor", "SSE.Views.SlicerSettings.textLock": "Desactivar cambiar o tamaño ou mover", "SSE.Views.SlicerSettings.textNewOld": "dos máis recentes aos máis antigos", "SSE.Views.SlicerSettings.textOldNew": "do máis antigo ao máis novo", "SSE.Views.SlicerSettings.textPosition": "Posición", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "do menor para o maior", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Do Z ao A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Columnas", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Altura", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Agochar elementos sen datos", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Indicar visualmente os elementos sen datos", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Referencias", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Amosar elementos eliminados da orixe de datos", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Amosar elementos sen datos á fin", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Ordenar e filtrar", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Estilo e tamaño", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Non mover nin cambiar tamaño con celdas", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Descrición", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Ascendente", "SSE.Views.SlicerSettingsAdvanced.textAZ": "De A a Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Descendente", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Nome para usar en fórmulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Proporcións constantes", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "do maior para o menor", "SSE.Views.SlicerSettingsAdvanced.textName": "Nome", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "dos máis recentes aos máis antigos", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "do máis antigo ao máis novo", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Mover sen cambiar tamaño con celdas", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "do menor para o maior", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Romper a celda", "SSE.Views.SlicerSettingsAdvanced.textSort": "Ordenar", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON>me da fonte", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Segmentación de datos - Configuración avanzada", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Mover e cambiar tamaño con celdas", "SSE.Views.SlicerSettingsAdvanced.textZA": "Do Z ao A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Este campo é obrigatorio", "SSE.Views.SortDialog.errorEmpty": "Todos os criterios de clasificación deben ter unha columna ou fila especificada.", "SSE.Views.SortDialog.errorMoreOneCol": "Seleccionouse máis dunha columna.", "SSE.Views.SortDialog.errorMoreOneRow": "Seleccionouse máis dunha fila.", "SSE.Views.SortDialog.errorNotOriginalCol": "A columna que seleccionou non está no rango orixinal seleccionado.", "SSE.Views.SortDialog.errorNotOriginalRow": "A fila que seleccionou non está no rango seleccionado orixinalmente.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 está a ser ordenada pola mesma cor máis dunha vez.<br>Elimine o criterio duplicado e intente de novo.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 está a ser ordenado máis dunha vez por valores.<br>Elimine os criterios duplicados e intente novamente.", "SSE.Views.SortDialog.textAdd": "<PERSON><PERSON><PERSON> nivel", "SSE.Views.SortDialog.textAsc": "Ascendente", "SSE.Views.SortDialog.textAuto": "Automático", "SSE.Views.SortDialog.textAZ": "De A a Z", "SSE.Views.SortDialog.textBelow": "Abaixo", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> <PERSON> celda", "SSE.Views.SortDialog.textColumn": "Columna", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON><PERSON> nivel", "SSE.Views.SortDialog.textDelete": "Eliminar nivel", "SSE.Views.SortDialog.textDesc": "Descendente", "SSE.Views.SortDialog.textDown": "Mover o nivel cara abaixo", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON> da fonte", "SSE.Views.SortDialog.textLeft": "E<PERSON>rda", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON> columnas...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON> li<PERSON>...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Opcións", "SSE.Views.SortDialog.textOrder": "Ordenar", "SSE.Views.SortDialog.textRight": "Dereita", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Orden<PERSON> segundo", "SSE.Views.SortDialog.textSortBy": "Ordenar por", "SSE.Views.SortDialog.textThenBy": "Logo por", "SSE.Views.SortDialog.textTop": "Parte superior", "SSE.Views.SortDialog.textUp": "Mover o nivel cara arriba", "SSE.Views.SortDialog.textValues": "Valores", "SSE.Views.SortDialog.textZA": "Do Z ao A", "SSE.Views.SortDialog.txtInvalidRange": "<PERSON><PERSON> de celdas inválido.", "SSE.Views.SortDialog.txtTitle": "Ordenar", "SSE.Views.SortFilterDialog.textAsc": "Ascendente (de A a Z) por", "SSE.Views.SortFilterDialog.textDesc": "Descendente (de Z a A) por", "SSE.Views.SortFilterDialog.txtTitle": "Ordenar", "SSE.Views.SortOptionsDialog.textCase": "Diferenciar maiús<PERSON>s de minúsculas", "SSE.Views.SortOptionsDialog.textHeaders": "Os meus datos teñen cabeceiras", "SSE.Views.SortOptionsDialog.textLeftRight": "Ordenar da esquerda á dereita", "SSE.Views.SortOptionsDialog.textOrientation": "Orientación", "SSE.Views.SortOptionsDialog.textTitle": "Opcións de ordenación", "SSE.Views.SortOptionsDialog.textTopBottom": "Ordenar de arriba cara abaixo", "SSE.Views.SpecialPasteDialog.textAdd": "Engadir", "SSE.Views.SpecialPasteDialog.textAll": "Todos", "SSE.Views.SpecialPasteDialog.textBlanks": "Saltar brancos", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>a", "SSE.Views.SpecialPasteDialog.textComments": "Comentarios", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Fórmulas e formato", "SSE.Views.SpecialPasteDialog.textFNFormat": "Fórmulas e formatos do número", "SSE.Views.SpecialPasteDialog.textFormats": "Formatos", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SpecialPasteDialog.textFWidth": "Fórmulas e anchos da columna", "SSE.Views.SpecialPasteDialog.textMult": "Multiplicar", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operación", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "Pegado especial", "SSE.Views.SpecialPasteDialog.textTranspose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textValues": "Valores", "SSE.Views.SpecialPasteDialog.textVFormat": "Valores e formato", "SSE.Views.SpecialPasteDialog.textVNFormat": "Valores e formatos de número", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON>do excepto bordos", "SSE.Views.Spellcheck.noSuggestions": "Non hai suxestións", "SSE.Views.Spellcheck.textChange": "Cambiar", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON><PERSON> todo", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON> todo", "SSE.Views.Spellcheck.txtAddToDictionary": "Engadir ao Dicionario", "SSE.Views.Spellcheck.txtClosePanel": "Pechar or<PERSON>", "SSE.Views.Spellcheck.txtComplete": "A corrección ortográfica completouse", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Idioma do dicionario", "SSE.Views.Spellcheck.txtNextTip": "Ir <PERSON> seguinte palabra", "SSE.Views.Spellcheck.txtSpelling": "Ortografía", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Copiar ao final)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Mover á fin)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Copiar antes da folla", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Mover di<PERSON> da folla", "SSE.Views.Statusbar.filteredRecordsText": "<PERSON><PERSON><PERSON> filtrados: {0} de {1}", "SSE.Views.Statusbar.filteredText": "Modo do filtro", "SSE.Views.Statusbar.itemAverage": "Promedio", "SSE.Views.Statusbar.itemCopy": "Copiar", "SSE.Views.Statusbar.itemCount": "Contar", "SSE.Views.Statusbar.itemDelete": "Eliminar", "SSE.Views.Statusbar.itemHidden": "Agochado", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Inserir", "SSE.Views.Statusbar.itemMaximum": "Máximo", "SSE.Views.Statusbar.itemMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMove": "Mover", "SSE.Views.Statusbar.itemProtect": "Protexer", "SSE.Views.Statusbar.itemRename": "Renomear", "SSE.Views.Statusbar.itemStatus": "Gardando estado", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON> <PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Quitar a protección", "SSE.Views.Statusbar.RenameDialog.errNameExists": "A folla de cálculo con tal nome xa existe", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "O nome de folla non pode conter os seguintes caracteres: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Nome da folla", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON> as follas", "SSE.Views.Statusbar.sheetIndexText": "Folla {0} de {1}", "SSE.Views.Statusbar.textAverage": "Promedio", "SSE.Views.Statusbar.textCount": "Contar", "SSE.Views.Statusbar.textMax": "Máx", "SSE.Views.Statusbar.textMin": "<PERSON><PERSON>.", "SSE.Views.Statusbar.textNewColor": "Nova cor personalizada", "SSE.Views.Statusbar.textNoColor": "Sen cor", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Engadir folla de cálculo", "SSE.Views.Statusbar.tipFirst": "<PERSON><PERSON><PERSON> ata a primeira folla", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON> ata a última folla", "SSE.Views.Statusbar.tipListOfSheets": "Lista de follas", "SSE.Views.Statusbar.tipNext": "Desprazar a lista de folla á dereita", "SSE.Views.Statusbar.tipPrev": "Desprazar a lista de folla á esquerda", "SSE.Views.Statusbar.tipZoomFactor": "Ampliar", "SSE.Views.Statusbar.tipZoomIn": "Achegar", "SSE.Views.Statusbar.tipZoomOut": "Alonxar", "SSE.Views.Statusbar.ungroupSheets": "Desagrupar follas", "SSE.Views.Statusbar.zoomText": "Ampliar {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "A operación non se puido facer para o intervalo de celas seleccionado. <br> Seleccione un intervalo de datos uniforme diferente ao existente e inténteo de novo.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Non se puido completar a operación para o intervalo de celas seleccionado. <br> Seleccione un intervalo para que a primeira fila da táboa estea na mesma fila <br> e a táboa resultante superpuxese á actual.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Non se puido completar a operación para o intervalo de celas seleccionado. <br> Seleccione un intervalo que non inclúa outras táboas.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "As fórmulas de matriz de varias celdas non está permitidas nas táboas.", "SSE.Views.TableOptionsDialog.txtEmpty": "Este campo é obrigatorio", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ERRO! Intervalo de celdas inválido", "SSE.Views.TableOptionsDialog.txtNote": "As cabeceiras deben permanecer na mesma fila e o intervalo de táboa resultante debe superpoñerse ao intervalo de táboa orixinal.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "Eliminar columna", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> fila", "SSE.Views.TableSettings.deleteTableText": "Eliminar táboa", "SSE.Views.TableSettings.insertColumnLeftText": "Inserir columna á esquerda", "SSE.Views.TableSettings.insertColumnRightText": "Inserir columna á dereita", "SSE.Views.TableSettings.insertRowAboveText": "Inserir fila enriba", "SSE.Views.TableSettings.insertRowBelowText": "Inserir fila abaixo", "SSE.Views.TableSettings.notcriticalErrorTitle": "Aviso", "SSE.Views.TableSettings.selectColumnText": "Seleccionar toda a columna enteira", "SSE.Views.TableSettings.selectDataText": "Seleccionar da<PERSON> de columna", "SSE.Views.TableSettings.selectRowText": "Seleccionar fila", "SSE.Views.TableSettings.selectTableText": "Seleccionar táboa", "SSE.Views.TableSettings.textActions": "Accións da táboa", "SSE.Views.TableSettings.textAdvanced": "Amosar configuración avanzada", "SSE.Views.TableSettings.textBanded": "Con bandas", "SSE.Views.TableSettings.textColumns": "Columnas", "SSE.Views.TableSettings.textConvertRange": "Converter para intervalo", "SSE.Views.TableSettings.textEdit": "Filas e columnas", "SSE.Views.TableSettings.textEmptyTemplate": "Sen modelos", "SSE.Views.TableSettings.textExistName": "ERRO! Un intervalo con este nome xa existe", "SSE.Views.TableSettings.textFilter": "Botón do filtro", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "ERRO! Nome da táboa inválido", "SSE.Views.TableSettings.textIsLocked": "Este elemento está sendo editado por outro usuario.", "SSE.Views.TableSettings.textLast": "Último", "SSE.Views.TableSettings.textLongOperation": "Operación longa", "SSE.Views.TableSettings.textPivot": "Inserir táboa dinámica", "SSE.Views.TableSettings.textRemDuplicates": "Eliminar duplicados", "SSE.Views.TableSettings.textReservedName": "O nome que está tratando de usar xa se fai referencia nas fórmulas da celda. Por favor, seleccione outro nome.", "SSE.Views.TableSettings.textResize": "Tamaño da táboa", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSlicer": "Inserir segmentación dos datos", "SSE.Views.TableSettings.textTableName": "Nome da táboa", "SSE.Views.TableSettings.textTemplate": "Seleccionar a partir do modelo", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.warnLongOperation": "A operación que está a punto de realizar podería tomar moito tempo para completarse.<br> Ten a certeza de que desexa continuar?", "SSE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Descrición", "SSE.Views.TableSettingsAdvanced.textAltTip": "A representación alternativa baseada en texto da información do obxecto visual, que se lerá ás persoas con problemas de visión ou cognitivos para axudalos a comprender mellor que información hai na imaxe, forma automática, gráfico ou táboa.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Táboa - Configuración avanzada", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON> <PERSON> fondo", "SSE.Views.TextArtSettings.strColor": "Cor", "SSE.Views.TextArtSettings.strFill": "Encher", "SSE.Views.TextArtSettings.strForeground": "Cor de primeiro plano", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "Liña", "SSE.Views.TextArtSettings.strTransparency": "Opacidade", "SSE.Views.TextArtSettings.strType": "Tipo", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "O valor inserido é incorrecto. <br> Insira un valor numérico entre 0 e 1584 puntos.", "SSE.Views.TextArtSettings.textColor": "Cor para encher", "SSE.Views.TextArtSettings.textDirection": "Dirección ", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON>", "SSE.Views.TextArtSettings.textFromFile": "Do fi<PERSON>iro", "SSE.Views.TextArtSettings.textFromUrl": "Da URL", "SSE.Views.TextArtSettings.textGradient": "Puntos de degradado ", "SSE.Views.TextArtSettings.textGradientFill": "Recheo degradado", "SSE.Views.TextArtSettings.textImageTexture": "Imaxe ou textura", "SSE.Views.TextArtSettings.textLinear": "Lineal", "SSE.Views.TextArtSettings.textNoFill": "Sen encher", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Posición", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Cadro do persoal", "SSE.Views.TextArtSettings.textTexture": "De textura", "SSE.Views.TextArtSettings.textTile": "Mosaico", "SSE.Views.TextArtSettings.textTransform": "Transformar", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Engadir punto de degradado", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "Cartón", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "SSE.Views.TextArtSettings.txtGrain": "Grano", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Papel gris", "SSE.Views.TextArtSettings.txtKnit": "Texido", "SSE.Views.TextArtSettings.txtLeather": "Coiro", "SSE.Views.TextArtSettings.txtNoBorders": "Sen liña", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Madeira", "SSE.Views.Toolbar.capBtnAddComment": "Engadir comentario", "SSE.Views.Toolbar.capBtnColorSchemas": "Esquema de cores", "SSE.Views.Toolbar.capBtnComment": "Comentario", "SSE.Views.Toolbar.capBtnInsHeader": "Cabeceira/Rodapé", "SSE.Views.Toolbar.capBtnInsSlicer": "Segmentación de datos", "SSE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientación", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Área de impresión", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnScale": "Axustar área de impresión", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Enviar atrás", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON> adiante", "SSE.Views.Toolbar.capImgGroup": "Grupo", "SSE.Views.Toolbar.capInsertChart": "Gráfico", "SSE.Views.Toolbar.capInsertEquation": "Ecuación", "SSE.Views.Toolbar.capInsertHyperlink": "Hiperligazón", "SSE.Views.Toolbar.capInsertImage": "Imaxe", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Minigráfico", "SSE.Views.Toolbar.capInsertTable": "Táboa", "SSE.Views.Toolbar.capInsertText": "Caixa de texto", "SSE.Views.Toolbar.mniImageFromFile": "Imaxe do ficheiro", "SSE.Views.Toolbar.mniImageFromStorage": "Imaxe do Almacenamento", "SSE.Views.Toolbar.mniImageFromUrl": "Imaxe da URL", "SSE.Views.Toolbar.textAddPrintArea": "Engadir a Área de Impresión", "SSE.Views.Toolbar.textAlignBottom": "Aliñar á parte inferior", "SSE.Views.Toolbar.textAlignCenter": "Aliñar ao centro", "SSE.Views.Toolbar.textAlignJust": "Xustificado", "SSE.Views.Toolbar.textAlignLeft": "Aliñar á esquerda", "SSE.Views.Toolbar.textAlignMiddle": "Aliñar ao medio", "SSE.Views.Toolbar.textAlignRight": "Aliñar á <PERSON>eita", "SSE.Views.Toolbar.textAlignTop": "Aliñar á parte superior", "SSE.Views.Toolbar.textAllBorders": "Todos os bordos", "SSE.Views.Toolbar.textAuto": "Automático", "SSE.Views.Toolbar.textAutoColor": "Automático", "SSE.Views.Toolbar.textBold": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersColor": "Cor do bordo", "SSE.Views.Toolbar.textBordersStyle": "Estilo do contorno", "SSE.Views.Toolbar.textBottom": "Inferior:", "SSE.Views.Toolbar.textBottomBorders": "Bordos inferiores", "SSE.Views.Toolbar.textCenterBorders": "Bordos verticais interiores", "SSE.Views.Toolbar.textClearPrintArea": "Limpar área de impresión", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON> regras", "SSE.Views.Toolbar.textClockwise": "Ángulo no sentido horario", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCounterCw": "Ángulo no sentido antihorario", "SSE.Views.Toolbar.textDataBars": "Barras de datos", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON><PERSON> celd<PERSON> á esquerda", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON> celdas cara arriba", "SSE.Views.Toolbar.textDiagDownBorder": "<PERSON><PERSON> diagonal descendente", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON><PERSON> diagonal ascendente", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "<PERSON><PERSON>", "SSE.Views.Toolbar.textEntireCol": "Toda a columna", "SSE.Views.Toolbar.textEntireRow": "Toda a fila", "SSE.Views.Toolbar.textFewPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHeight": "Altura", "SSE.Views.Toolbar.textHideVA": "<PERSON><PERSON><PERSON><PERSON> visible", "SSE.Views.Toolbar.textHorizontal": "Texto horizontal", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON> celdas cara abaixo", "SSE.Views.Toolbar.textInsideBorders": "Bordos interiores", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON> celd<PERSON> dereita", "SSE.Views.Toolbar.textItalic": "Cursiva", "SSE.Views.Toolbar.textItems": "Elementos", "SSE.Views.Toolbar.textLandscape": "Horizontal", "SSE.Views.Toolbar.textLeft": "Esquerda:", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textManageRule": "Xestionar regras", "SSE.Views.Toolbar.textManyPages": "Páxinas", "SSE.Views.Toolbar.textMarginsLast": "Último personalizado", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Amplo", "SSE.Views.Toolbar.textMiddleBorders": "Bordos horizontais interiores", "SSE.Views.Toolbar.textMoreFormats": "Outros formatos", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNewColor": "Nova cor personalizada", "SSE.Views.Toolbar.textNewRule": "Nova regra", "SSE.Views.Toolbar.textNoBorders": "<PERSON> b<PERSON>", "SSE.Views.Toolbar.textOnePage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON> externos", "SSE.Views.Toolbar.textPageMarginsCustom": "Marxes personalizadas", "SSE.Views.Toolbar.textPortrait": "Retrato ", "SSE.Views.Toolbar.textPrint": "Imprimir", "SSE.Views.Toolbar.textPrintGridlines": "Imprim<PERSON> cu<PERSON>", "SSE.Views.Toolbar.textPrintHeadings": "Imprimir <PERSON>", "SSE.Views.Toolbar.textPrintOptions": "Opcións de impresión", "SSE.Views.Toolbar.textRight": "Dereita:", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "<PERSON><PERSON> texto cara abaixo", "SSE.Views.Toolbar.textRotateUp": "<PERSON><PERSON> texto cara arriba", "SSE.Views.Toolbar.textScale": "Escala", "SSE.Views.Toolbar.textScaleCustom": "Personalizado", "SSE.Views.Toolbar.textSelection": "Desde a selección actual", "SSE.Views.Toolbar.textSetPrintArea": "Establecer <PERSON> de impresión", "SSE.Views.Toolbar.textStrikeout": "Riscado", "SSE.Views.Toolbar.textSubscript": "Subscrito", "SSE.Views.Toolbar.textSubSuperscript": "Subscrito/Sobrescrito", "SSE.Views.Toolbar.textSuperscript": "Sobrescrito", "SSE.Views.Toolbar.textTabCollaboration": "Colaboración", "SSE.Views.Toolbar.textTabData": "Datos", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON>o", "SSE.Views.Toolbar.textTabInsert": "Inserir", "SSE.Views.Toolbar.textTabLayout": "Deseño", "SSE.Views.Toolbar.textTabProtect": "Protección", "SSE.Views.Toolbar.textTabView": "Vista", "SSE.Views.Toolbar.textThisPivot": "Desde esta táboa pivote", "SSE.Views.Toolbar.textThisSheet": "Desde esta folla de cálculo", "SSE.Views.Toolbar.textThisTable": "Desde esta tabla", "SSE.Views.Toolbar.textTop": "Parte superior: ", "SSE.Views.Toolbar.textTopBorders": "Bordos superiores", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textVertical": "Texto vertical", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Ampliar", "SSE.Views.Toolbar.tipAlignBottom": "Aliñar á parte inferior", "SSE.Views.Toolbar.tipAlignCenter": "Aliñar ao centro", "SSE.Views.Toolbar.tipAlignJust": "Xustificado", "SSE.Views.Toolbar.tipAlignLeft": "Aliñar á esquerda", "SSE.Views.Toolbar.tipAlignMiddle": "Aliñar ao medio", "SSE.Views.Toolbar.tipAlignRight": "Aliñar á <PERSON>eita", "SSE.Views.Toolbar.tipAlignTop": "Aliñar á parte superior", "SSE.Views.Toolbar.tipAutofilter": "Ordenar e filtrar", "SSE.Views.Toolbar.tipBack": "Volver", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeChart": "Cambiar tipo de gráfico", "SSE.Views.Toolbar.tipClearStyle": "Limpar", "SSE.Views.Toolbar.tipColorSchemas": "Cambiar combinación de cores", "SSE.Views.Toolbar.tipCondFormat": "Formato condicional", "SSE.Views.Toolbar.tipCopy": "Copiar", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecFont": "Diminuír o tamaño da fonte", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON> celdas", "SSE.Views.Toolbar.tipDigStyleAccounting": "Est<PERSON> da contabilidade", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON> moe<PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON><PERSON>orcent<PERSON>", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartType": "Cambiar tipo de gráfico", "SSE.Views.Toolbar.tipEditHeader": "Editar cabeceira e rodapé", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> da fonte", "SSE.Views.Toolbar.tipFontName": "Fonte", "SSE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>e", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> obxe<PERSON>", "SSE.Views.Toolbar.tipImgGroup": "Agrupar obxectos", "SSE.Views.Toolbar.tipIncDecimal": "Aumentar deci<PERSON>is", "SSE.Views.Toolbar.tipIncFont": "Aumentar ta<PERSON> da fonte", "SSE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "SSE.Views.Toolbar.tipInsertChartSpark": "Inserir g<PERSON>", "SSE.Views.Toolbar.tipInsertEquation": "Inserir ecuación", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON>ga<PERSON>", "SSE.Views.Toolbar.tipInsertImage": "Inserir imaxe", "SSE.Views.Toolbar.tipInsertOpt": "Inserir celdas", "SSE.Views.Toolbar.tipInsertShape": "Inserir forma automática", "SSE.Views.Toolbar.tipInsertSlicer": "Inserir segmentación dos datos", "SSE.Views.Toolbar.tipInsertSpark": "Inserir minigráfico", "SSE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "SSE.Views.Toolbar.tipInsertTable": "Inserir táboa", "SSE.Views.Toolbar.tipInsertText": "Inserir caixa do texto", "SSE.Views.Toolbar.tipInsertTextart": "Inserir arte do texto", "SSE.Views.Toolbar.tipMerge": "Combinar e centrar", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "Formato numérico", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON> da páxina", "SSE.Views.Toolbar.tipPageOrient": "Orientación da páxina", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON> da páxina", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "Cor para encher", "SSE.Views.Toolbar.tipPrint": "Imprimir", "SSE.Views.Toolbar.tipPrintArea": "Área de impresión", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "Gardar", "SSE.Views.Toolbar.tipSaveCoauth": "Garde os cambios para que outros usuarios os poidan ver.", "SSE.Views.Toolbar.tipScale": "Axustar área de impresión", "SSE.Views.Toolbar.tipSendBackward": "Enviar atrás", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON> adiante", "SSE.Views.Toolbar.tipSynchronize": "O documento cambiouno outro usuario. Prema para gardar os teus cambios e recarga as actualizacións.", "SSE.Views.Toolbar.tipTextFormatting": "Máis ferramentas de formato de texto", "SSE.Views.Toolbar.tipTextOrientation": "Orientación", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVisibleArea": "Área visible", "SSE.Views.Toolbar.tipWrap": "Axustar texto", "SSE.Views.Toolbar.txtAccounting": "Contabilidade", "SSE.Views.Toolbar.txtAdditional": "Adicional", "SSE.Views.Toolbar.txtAscending": "Ascendente", "SSE.Views.Toolbar.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "Todos", "SSE.Views.Toolbar.txtClearComments": "Comentarios", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON><PERSON> filtro", "SSE.Views.Toolbar.txtClearFormat": "Formato", "SSE.Views.Toolbar.txtClearFormula": "Función", "SSE.Views.Toolbar.txtClearHyper": "Hiperligazóns", "SSE.Views.Toolbar.txtClearText": "Texto", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Personalizar", "SSE.Views.Toolbar.txtDate": "Data", "SSE.Views.Toolbar.txtDateTime": "Data e hora", "SSE.Views.Toolbar.txtDescending": "Descendente", "SSE.Views.Toolbar.txtDollar": "$ Dólar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponencial", "SSE.Views.Toolbar.txtFilter": "Filtro", "SSE.Views.Toolbar.txtFormula": "Inserir función", "SSE.Views.Toolbar.txtFraction": "Fracción", "SSE.Views.Toolbar.txtFranc": "CHF Franco Suízo", "SSE.Views.Toolbar.txtGeneral": "Xeral", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Xestor do nome", "SSE.Views.Toolbar.txtMergeAcross": "Unir horizontalmente", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Unir e centrar", "SSE.Views.Toolbar.txtNamedRange": "Intervalos nomeadas", "SSE.Views.Toolbar.txtNewRange": "Definir nome", "SSE.Views.Toolbar.txtNoBorders": "<PERSON> b<PERSON>", "SSE.Views.Toolbar.txtNumber": "Número", "SSE.Views.Toolbar.txtPasteRange": "<PERSON>egar nome", "SSE.Views.Toolbar.txtPercentage": "Porcentaxe", "SSE.Views.Toolbar.txtPound": "£ Libra", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON>lo", "SSE.Views.Toolbar.txtScheme1": "Oficina", "SSE.Views.Toolbar.txtScheme10": "Mediana", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Opulento", "SSE.Views.Toolbar.txtScheme14": "Mirador", "SSE.Views.Toolbar.txtScheme15": "Or<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme16": "Papel", "SSE.Views.Toolbar.txtScheme17": "Solsticio", "SSE.Views.Toolbar.txtScheme18": "Técnico", "SSE.Views.Toolbar.txtScheme19": "Viaxes", "SSE.Views.Toolbar.txtScheme2": "Escala de grises", "SSE.Views.Toolbar.txtScheme20": "Urbano", "SSE.Views.Toolbar.txtScheme21": "Inspiración", "SSE.Views.Toolbar.txtScheme22": "Nova oficina", "SSE.Views.Toolbar.txtScheme3": "Vértice", "SSE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "SSE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme6": "Concorrencia", "SSE.Views.Toolbar.txtScheme7": "Equidade", "SSE.Views.Toolbar.txtScheme8": "Fluxo", "SSE.Views.Toolbar.txtScheme9": "Fundición", "SSE.Views.Toolbar.txtScientific": "Científico", "SSE.Views.Toolbar.txtSearch": "Buscar", "SSE.Views.Toolbar.txtSort": "Ordenar", "SSE.Views.Toolbar.txtSortAZ": "Clasificar pola orde ascendente", "SSE.Views.Toolbar.txtSortZA": "Clasificar pola orde descendente", "SSE.Views.Toolbar.txtSpecial": "Especial", "SSE.Views.Toolbar.txtTableTemplate": "Formato como modelo da táboa", "SSE.Views.Toolbar.txtText": "Тexto", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON><PERSON> celdas", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "Inferior", "SSE.Views.Top10FilterDialog.txtBy": "por", "SSE.Views.Top10FilterDialog.txtItems": "Elemento", "SSE.Views.Top10FilterDialog.txtPercent": "Por cento", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 de Autofiltro", "SSE.Views.Top10FilterDialog.txtTop": "Parte superior", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtro dos 10 principais", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Configuración do campo de valor", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Promedio", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Campo base", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Elemento base", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 de %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Contar", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Nome personalizado", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "A diferenza de", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Máx", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON><PERSON>.", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Porcentaxe de", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Diferenza porcentual de", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Porcentaxe da columna", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Porcentaxe do total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Porcentaxe das filas", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produ<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Total en", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "<PERSON><PERSON> valores coma", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "<PERSON>me da fonte:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON><PERSON><PERSON> campo de valor por", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Convidado(a)", "SSE.Views.ViewManagerDlg.lockText": "Bloqueado", "SSE.Views.ViewManagerDlg.textDelete": "Eliminar", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplicar", "SSE.Views.ViewManagerDlg.textEmpty": "Aínda non hai crearon vistas.", "SSE.Views.ViewManagerDlg.textGoTo": "Ir a <PERSON>", "SSE.Views.ViewManagerDlg.textLongName": "Insira un nome que teña menos de 128 caracteres.", "SSE.Views.ViewManagerDlg.textNew": "Novo", "SSE.Views.ViewManagerDlg.textRename": "Renomear", "SSE.Views.ViewManagerDlg.textRenameError": "O nome da vista non debe estar vacío.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Renomear vista", "SSE.Views.ViewManagerDlg.textViews": "Vistas da folla", "SSE.Views.ViewManagerDlg.tipIsLocked": "Este elemento está sendo editado por outro usuario.", "SSE.Views.ViewManagerDlg.txtTitle": "Xestionador da vista de folla", "SSE.Views.ViewManagerDlg.warnDeleteView": "Está tratando de eliminar a vista actualmente habilitada '%1'. Pechar esta vista e eliminala?", "SSE.Views.ViewTab.capBtnFreeze": "Inmobilizar paneis", "SSE.Views.ViewTab.capBtnSheetView": "Vista da folla", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Amosar sempre a barra de ferramentas", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "<PERSON><PERSON><PERSON>r as barras de folla e de estado", "SSE.Views.ViewTab.textCreate": "Novo", "SSE.Views.ViewTab.textDefault": "Predeterminado", "SSE.Views.ViewTab.textFormula": "Barra das fórmulas", "SSE.Views.ViewTab.textFreezeCol": "Inmobilizar primeira columna", "SSE.Views.ViewTab.textFreezeRow": "Inmobilizar fila superior", "SSE.Views.ViewTab.textGridlines": "Liñas da cuadrícula", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "Xestionador da vista", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Amosar a sombra de paneis inmobilizados", "SSE.Views.ViewTab.textUnFreeze": "Mobilizar <PERSON>", "SSE.Views.ViewTab.textZeros": "<PERSON><PERSON> ceros", "SSE.Views.ViewTab.textZoom": "Ampliar", "SSE.Views.ViewTab.tipClose": "Pechar vista de folla", "SSE.Views.ViewTab.tipCreate": "<PERSON>rear vista da folla", "SSE.Views.ViewTab.tipFreeze": "Inmobilizar paneis", "SSE.Views.ViewTab.tipSheetView": "Vista da folla", "SSE.Views.WBProtection.hintAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectSheet": "Protexer folla", "SSE.Views.WBProtection.hintProtectWB": "Protexer libro", "SSE.Views.WBProtection.txtAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON> blo<PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Forma bloqueada", "SSE.Views.WBProtection.txtLockedText": "Bloquear texto", "SSE.Views.WBProtection.txtProtectSheet": "Protexer folla", "SSE.Views.WBProtection.txtProtectWB": "Protexer libro", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Introduza un contrasinal para quitarlle a protección á folla", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Desprote<PERSON>r folla", "SSE.Views.WBProtection.txtWBUnlockDescription": "Introduza un contrasinal para quitarlle a protección ao libro", "SSE.Views.WBProtection.txtWBUnlockTitle": "Desprotexer libro"}