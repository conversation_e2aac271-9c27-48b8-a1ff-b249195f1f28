﻿<!DOCTYPE html>
<html>
	<head>
		<title>SIN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>SIN-Funktion</h1>
			<p>Die Funktion <b>SIN</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um den Sinus eines Winkels zurückzugeben.</p>
			<p>Die Syntax der Funktion <b>SIN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>SIN(Zahl)</em></b></p> 
			<p>Das Argument <b><em>Zahl</em></b> ist ein numerischer Wert, der manuell eingegeben oder in die Zelle eingefügt wird, auf die Sie verweisen.</p>
			<p><b>SIN</b>-Funktion anwenden:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie die Funktion <b>SIN</b>.</li>
			<li>Geben Sie das gewünschte Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="SIN-Funktion" src="../images/sin.png" /></p>
		</div>
	</body>
</html>