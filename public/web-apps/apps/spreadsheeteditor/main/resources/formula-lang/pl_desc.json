{"DATE": {"a": "(rok; miesiąc; dzień)", "d": "Zwraca liczbę reprezentującą datę w kodzie data-godzina"}, "DATEDIF": {"a": "(data_początkowa; data_końcowa; jednostka)", "d": "Oblicza liczbę dni, mi<PERSON><PERSON><PERSON> lub lat między dwiema datami"}, "DATEVALUE": {"a": "(data_tekst)", "d": "Konwertuje datę w postaci tekstu na liczbę reprezentującą datę w kodzie data-godzina"}, "DAY": {"a": "(kolejna_liczba)", "d": "Zwraca dzień miesiąca, liczbę od 1 do 31."}, "DAYS": {"a": "(data_końcowa; data_początkowa)", "d": "Zwraca liczbę dni zawartych między dwiema datami."}, "DAYS360": {"a": "(data_początkowa; data_końcowa; [metoda])", "d": "Oblicza liczbę dni zawartych między dwiema datami przyjmując rok liczący 360 dni (dwanaście 30-dniowych miesięcy)"}, "EDATE": {"a": "(data_pocz; miesiące)", "d": "Zwraca warto<PERSON>ć liczby seryjnej daty, przypadaj<PERSON><PERSON>j podaną liczbę miesięcy przed lub po dacie początkowej"}, "EOMONTH": {"a": "(data_pocz; miesiące)", "d": "Zwraca warto<PERSON> liczby seryjnej daty ostatniego dnia miesiąca przed lub po podanej liczbie miesięcy"}, "HOUR": {"a": "(kolejna_liczba)", "d": "Zwraca godzinę jako liczbę od 0 (0:00) do 23 (23:00)."}, "ISOWEEKNUM": {"a": "(data)", "d": "Zwraca dla danej daty numer tygodnia roku w formacie ISO."}, "MINUTE": {"a": "(kolejna_liczba)", "d": "<PERSON><PERSON><PERSON><PERSON> min<PERSON>ę, <PERSON><PERSON><PERSON><PERSON> od 0 do 59."}, "MONTH": {"a": "(kolejna_liczba)", "d": "Zw<PERSON><PERSON>, liczbę od 1 (styczeń) do 12 (grudzień)."}, "NETWORKDAYS": {"a": "(data_pocz; data_końc; [świ<PERSON>ta])", "d": "Zwraca liczbę dni roboczych pomiędzy dwiema datami"}, "NETWORKDAYS.INTL": {"a": "(data_pocz; data_końc; [weekend]; [świ<PERSON>ta])", "d": "Zwraca liczbę dni roboczych między dwiema datami z niestandardowymi parametrami dotyczącymi weekendów"}, "NOW": {"a": "()", "d": "Zwraca bieżącą datę i godzinę sformatowane jako data i godzina."}, "SECOND": {"a": "(kolejna_liczba)", "d": "Zw<PERSON><PERSON>ę, liczbę od 0 do 59."}, "TIME": {"a": "(god<PERSON>a; minuta; sekunda)", "d": "Konwer<PERSON><PERSON>ziny, minuty i sekundy dane jako liczby na liczby kolejne, sformatowane za pomocą formatu czasu"}, "TIMEVALUE": {"a": "(godzin<PERSON>_tekst)", "d": "Konwertuje czas w formacie tekstowym na kolejną liczbę czasu: liczbę od 0 (00:00:00) do 0,999988426 (23:59:59). Liczbę należy formatować za pomocą formatu czasu po wprowadzeniu formuły"}, "TODAY": {"a": "()", "d": "Zwraca datę bieżącą sformatowaną jako datę."}, "WEEKDAY": {"a": "(licz<PERSON>_kolejna; [zwracany_typ])", "d": "Zwraca liczbę od 1 do 7, określającą numer dnia tygodnia na podstawie daty."}, "WEEKNUM": {"a": "(licz<PERSON>_seryjna; [typ_wyniku])", "d": "Zwraca numer tygodnia w roku"}, "WORKDAY": {"a": "(data_pocz; dni; [ś<PERSON><PERSON>ta])", "d": "Zwrac<PERSON> warto<PERSON> liczby seryjnej daty przed lub po podanej liczbie dni roboczych"}, "WORKDAY.INTL": {"a": "(data_pocz; dni; [weekend]; [święta])", "d": "Zwraca liczbę seryjną daty przypadającej przed lub po określonej liczbie dni roboczych z niestandardowymi parametrami weekendów"}, "YEAR": {"a": "(kolejna_liczba)", "d": "Zwraca rok z daty, liczbę całkowitą z zakresu 1900-9999."}, "YEARFRAC": {"a": "(data_pocz; data_końc; [podstawa])", "d": "<PERSON><PERSON><PERSON>, jak<PERSON> czę<PERSON>ć roku stanowi pełna liczba dni pomiędzy datą początkową i końcową"}, "BESSELI": {"a": "(x; n)", "d": "Zwrac<PERSON> war<PERSON> zmodyfikowanej funkcji Bessela In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Zwrac<PERSON> funkcji Bessela Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Zwrac<PERSON> zmodyfikowanej funkcji Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Zwrac<PERSON> funkcji Bessela Yn(x)"}, "BIN2DEC": {"a": "(liczba)", "d": "Przekształca liczbę dwójkową na dziesiętną"}, "BIN2HEX": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dwójkową na liczbę w kodzie szesnastkowym"}, "BIN2OCT": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dwójkową na liczbę w kodzie ósemkowym"}, "BITAND": {"a": "(liczba1; liczba2)", "d": "Zwraca wynik operacji bitowej AND (ORAZ) dwóch liczb."}, "BITLSHIFT": {"a": "(liczba; liczba_przesunięć)", "d": "Zwraca liczbę przesuniętą w lewo o liczbę bitów liczba_przesunięć."}, "BITOR": {"a": "(liczba1; liczba2)", "d": "Zwraca wynik operacji bitowej OR (LUB) dwóch liczb."}, "BITRSHIFT": {"a": "(liczba; liczba_przesunięć)", "d": "Zwraca liczbę przesuniętą w prawo o liczbę bitów liczba_przesunięć."}, "BITXOR": {"a": "(liczba1; liczba2)", "d": "Zwraca wynik operacji bitowej XOR (wyłączne LUB) dwóch liczb."}, "COMPLEX": {"a": "(cz<PERSON><PERSON><PERSON>_rzecz; cz<PERSON><PERSON><PERSON>_uroj; [jednost<PERSON>_uroj])", "d": "Przekształca współczynniki rzeczywisty i urojony w liczbę zespoloną"}, "CONVERT": {"a": "(l<PERSON><PERSON>; jednostka_we; jednostka_wy)", "d": "Zamienia liczbę z jednego systemu miar na inny"}, "DEC2BIN": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dziesiętną na liczbę w kodzie dwójkowym"}, "DEC2HEX": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dziesiętną na liczbę w kodzie szesnastkowym"}, "DEC2OCT": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę dziesiętną na liczbę w kodzie ósemkowym"}, "DELTA": {"a": "(liczba1; [liczba2])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy dwie liczby są równe"}, "ERF": {"a": "(dolna_granica; [górna_granica])", "d": "Zwraca funk<PERSON>ję błędu"}, "ERF.PRECISE": {"a": "(X)", "d": "Zwraca funk<PERSON>ję błędu"}, "ERFC": {"a": "(x)", "d": "Zwraca komplementarną funk<PERSON>ję błędu"}, "ERFC.PRECISE": {"a": "(X)", "d": "Zwraca komplementarną funk<PERSON>ję błędu"}, "GESTEP": {"a": "(liczba; [próg])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy liczba jest większa niż podana wartość progowa"}, "HEX2BIN": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę szesnastkową na liczbę w kodzie dwójkowym"}, "HEX2DEC": {"a": "(liczba)", "d": "Przekształca liczbę szesnastkową na dziesiętną"}, "HEX2OCT": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę szesnastkową na liczbę w kodzie ósemkowym"}, "IMABS": {"a": "(liczba_zesp)", "d": "Zwrac<PERSON> warto<PERSON> bezwzględną (moduł) liczby zespolonej"}, "IMAGINARY": {"a": "(liczba_zesp)", "d": "Zwraca część urojoną liczby zespolonej"}, "IMARGUMENT": {"a": "(liczba_zesp)", "d": "Zwraca wartość argumentu liczby zespolonej, kąta wyrażonego w radianach"}, "IMCONJUGATE": {"a": "(liczba_zesp)", "d": "Zwraca wartość sprzężoną liczby zespolonej"}, "IMCOS": {"a": "(liczba_zesp)", "d": "Zwrac<PERSON> war<PERSON> cosinusa liczby zespolonej"}, "IMCOSH": {"a": "(liczba_zespolona)", "d": "Zwraca cosinus hiperboliczny liczby zespolonej."}, "IMCOT": {"a": "(liczba_zespolona)", "d": "Zwraca cotangens liczby zespolonej."}, "IMCSC": {"a": "(liczba_zespolona)", "d": "Zwraca cosecans liczby zespolonej."}, "IMCSCH": {"a": "(liczba_zespolona)", "d": "Zwraca cosecans hiperboliczny liczby zespolonej."}, "IMDIV": {"a": "(liczba_zesp1; liczba_zesp2)", "d": "Zwraca iloraz dwóch liczb zespolonych"}, "IMEXP": {"a": "(liczba_zesp)", "d": "Zwraca wartość wykładniczą liczby zespolonej"}, "IMLN": {"a": "(liczba_zesp)", "d": "Zwraca wartość logarytmu naturalnego liczby zespolonej"}, "IMLOG10": {"a": "(liczba_zesp)", "d": "Zwraca wartość logarytmu dziesiętnego liczby zespolonej"}, "IMLOG2": {"a": "(liczba_zesp)", "d": "Zwraca wartość logarytmu przy podstawie 2 z liczby zespolonej"}, "IMPOWER": {"a": "(liczba_zesp; liczba)", "d": "Zwraca wartość liczby zespolonej podniesionej do potęgi całkowitej"}, "IMPRODUCT": {"a": "(liczba_zespolona1; [liczba_zespolona2]; ...)", "d": "Zwraca iloczyn od 1 do 255 liczb zespolonych"}, "IMREAL": {"a": "(liczba_zesp)", "d": "Zwraca część rzeczywistą liczby zespolonej"}, "IMSEC": {"a": "(liczba_zespolona)", "d": "Zwraca secans liczby zespolonej."}, "IMSECH": {"a": "(liczba_zespolona)", "d": "Zwraca secans hiperboliczny liczby zespolonej."}, "IMSIN": {"a": "(liczba_zesp)", "d": "Zwraca wartość sinusa liczby zespolonej"}, "IMSINH": {"a": "(liczba_zespolona)", "d": "Zwraca sinus hiperboliczny liczby zespolonej."}, "IMSQRT": {"a": "(liczba_zesp)", "d": "Zwraca warto<PERSON>ć pierwiastka kwadratowego liczby zespolonej"}, "IMSUB": {"a": "(liczba_zesp1; liczba_zesp2)", "d": "Zwraca różnicę dwóch liczb zespolonych"}, "IMSUM": {"a": "(liczba_zespolona1; [liczba_zespolona2]; ...)", "d": "Zwraca sumę liczb zespolonych"}, "IMTAN": {"a": "(liczba_zespolona)", "d": "Zwraca tangens liczby zespolonej."}, "OCT2BIN": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę ósemkową na liczbę w kodzie dwójkowym"}, "OCT2DEC": {"a": "(liczba)", "d": "Przekształca liczbę ósemkową na dziesiętną"}, "OCT2HEX": {"a": "(l<PERSON><PERSON>; [miej<PERSON>])", "d": "Zamienia liczbę ósemkową na liczbę w kodzie szesnastkowym"}, "DAVERAGE": {"a": "(baza_danych; pole; kryteria)", "d": "Oblicza wartość średnią w kolumnie listy lub bazy danych, która spełnia określone kryteria"}, "DCOUNT": {"a": "(baza; pole; kryteria)", "d": "Zlicza komórki zawierające liczby we wskazanym polu (kolumnie) rekordów bazy danych, które spełniają podane warunki"}, "DCOUNTA": {"a": "(baza; pole; kryteria)", "d": "Zwraca liczbę niepustych komórek w polu (kolumnie) rekordów bazy danych spełniających podane kryteria"}, "DGET": {"a": "(baza; pole; kryteria)", "d": "Wydziela z bazy danych pojedynczy rekord, spełniający podane kryteria"}, "DMAX": {"a": "(baza_danych; pole; kryteria)", "d": "Zwraca największą liczbę w polu (kolumnie) rekordów bazy danych, które spełniają określone warunki"}, "DMIN": {"a": "(baza; pole; kryteria)", "d": "Zwraca minimalną wartość z pola (kolumny) rekordów bazy danych, które spełniają podane kryteria"}, "DPRODUCT": {"a": "(baza; pole; kryteria)", "d": "Mnoży wartości umieszczone w danym polu (kolumnie) tych rekordów w bazie danych, które spełniają podane kryteria"}, "DSTDEV": {"a": "(baza; pole; kryteria)", "d": "Oblicza odchylenie standardowe próbki składającej się z zaznaczonych pozycji bazy danych"}, "DSTDEVP": {"a": "(baza; pole; kryteria)", "d": "Oblicza odchylenie standardowe całej populacji składającej się z zaznaczonych pozycji bazy danych"}, "DSUM": {"a": "(baza; pole; kryteria)", "d": "Dodaje liczby umieszczone w polach (kolumnie) tych rekordów bazy danych, które spełniają podane kryteria"}, "DVAR": {"a": "(baza; pole; kryteria)", "d": "Oblicza wariancję próbki składającej się z zaznaczonych pozycji bazy danych"}, "DVARP": {"a": "(baza; pole; kryteria)", "d": "Oblicza wariancję całej populacji składającej się z zaznaczonych pozycji bazy danych"}, "CHAR": {"a": "(liczba)", "d": "Zwraca znak określony przez numer w kodzie zestawu znaków używanego w komputerze"}, "CLEAN": {"a": "(tekst)", "d": "Usuwa z tekstu wszystkie znaki, które nie mogą by<PERSON> drukowane"}, "CODE": {"a": "(tekst)", "d": "Zwraca kod liczbowy pierwszego znaku w tekście odpowiadający zestawowi znaków używanemu w komputerze"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "Łączy kilka ciągów tekstowych w jeden ciąg"}, "CONCAT": {"a": "(tekst1; ...)", "d": "Łącz<PERSON> listę lub zakres ciągów tekstowych"}, "DOLLAR": {"a": "(l<PERSON>ba; [miej<PERSON>_d<PERSON><PERSON>])", "d": "Konwertuje liczbę na tekst, korzystając z formatu walutowego"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy dwa ciągi tekstowe są identyczne, i zwraca wartość PRAWDA albo FAŁSZ. Funkcja PORÓWNAJ uwzględnia wielkość znaków"}, "FIND": {"a": "(szukany_tekst; w_tekście; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Zwraca pozycję początkową jednego ciągu tekstowego w drugim ciągu tekstowym. Funkcja ZNAJDŹ uwzględnia wielkość liter"}, "FINDB": {"a": "(szukany_tekst; w_tekście; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Lokalizują ciąg tekstowy wewnątrz innego ciągu tekstowego i zwracają pozycję początkową pierwszego ciągu, licząc od pierwszego znaku drugiego ciągu, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański"}, "FIXED": {"a": "(liczba; [mi<PERSON><PERSON>_d<PERSON><PERSON>]; [bez_<PERSON>rz<PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę do określonej liczby miejsc po przecinku i zwraca wynik jako tekst ze spacjami lub bez"}, "LEFT": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca określoną liczbę znaków od początku ciągu tekstowego"}, "LEFTB": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca pierwsze znaki w ciągu tekstowym na podstawie określonej liczby znaków, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański"}, "LEN": {"a": "(tekst)", "d": "Zwraca liczbę znaków w ciągu znaków"}, "LENB": {"a": "(tekst)", "d": "Zwraca liczbę bajtów reprezentujących znaki w ciągu tekstowym, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański"}, "LOWER": {"a": "(tekst)", "d": "Konwertuje wszystkie litery w ciągu tekstowym na małe litery"}, "MID": {"a": "(tekst; liczba_początkowa; liczba_znaków)", "d": "Zwraca znaki ze środka ciągu tekstowego przy danej pozycji początkowej i długości"}, "MIDB": {"a": "(tekst; liczba_początkowa; liczba_znaków)", "d": "Zwraca określoną liczbę znaków z ciągu tekstowego, począwszy od określonej pozycji, na podstawie podanej liczby bajtów, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański"}, "NUMBERVALUE": {"a": "(tekst; [separator_d<PERSON><PERSON>ny]; [separator_grup])", "d": "Konwertuje tekst na liczbę w sposób niezależny od ustawień regionalnych."}, "PROPER": {"a": "(tekst)", "d": "Konwertuje ciąg tekstowy na litery właściwej wielkości; pierwszą literę w każdym wyrazie na wielką literę, a wszystkie inne litery na małe litery"}, "REPLACE": {"a": "(stary_tekst; liczba_początkowa; liczba_znaków; nowy_tekst)", "d": "Zamienia część ciągu znaków innym ciągiem znaków"}, "REPLACEB": {"a": "(stary_tekst; liczba_początkowa; liczba_znaków; nowy_tekst)", "d": "Zamienia część ciągu tekstowego na inny ciąg tekstowy z uwzględnieniem określonej liczby bajtów, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański"}, "REPT": {"a": "(tekst; ile_razy)", "d": "Powtarza tekst podaną liczbę razy. Używaj funkcji POWT do wypełnienia komórki podaną liczbą wystąpień ciągu tekstowego"}, "RIGHT": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca określoną liczbę znaków od końca ciągu tekstowego"}, "RIGHTB": {"a": "(tekst; [licz<PERSON>_znak<PERSON>])", "d": "Zwraca ostatnie znaki w ciągu tekstowym, na podstawie określonej liczby bajtów, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański"}, "SEARCH": {"a": "(szukany_tekst; obejmuj<PERSON>cy_tekst; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Zwraca numer znaku, w którym jeden ciąg znaków został znaleziony po raz pierwszy w drugim, począwszy od lewej strony (nie rozróżniając liter małych i dużych)"}, "SEARCHB": {"a": "(szukany_tekst; obejmuj<PERSON>cy_tekst; [licz<PERSON>_pocz<PERSON>tkowa])", "d": "Służy do odnajdywania jednego ciągu tekstowego wewnątrz innego ciągu tekstowego i zwracania pozycji początkowej szukanego tekstu liczonej od pierwszego znaku tekstu przeszukiwanego, do używania z językami o zestawach znaków dwubajtowych (DBCS) -  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> i koreański"}, "SUBSTITUTE": {"a": "(tekst; stary_tekst; nowy_tekst; [wystapienie_liczba])", "d": "Zamienia istniejący tekst w ciągu nowym tekstem"}, "T": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> war<PERSON> to tekst i zwraca ten tekst, g<PERSON> warto<PERSON> jest tekstem, albo podwójny cudzysłów (pusty tekst), je<PERSON><PERSON> warto<PERSON> nie jest tekstem"}, "TEXT": {"a": "(wartość; format_tekst)", "d": "Konwertuje wartość na tekst w podanym formacie liczbowym"}, "TEXTJOIN": {"a": "(og<PERSON><PERSON><PERSON>; ignoruj_puste; tekst1; ...)", "d": "Łą<PERSON><PERSON> listę lub zakres ciągów tekstowych przy użyciu ogranicznika"}, "TRIM": {"a": "(tekst)", "d": "Usuwa wszystkie spacje z podanego tekstu poza pojedynczymi spacjami rozdzielającymi słowa"}, "UNICHAR": {"a": "(liczba)", "d": "Zwraca znak Unicode, do którego odwołuje się dana wartoś<PERSON> liczbowa."}, "UNICODE": {"a": "(tekst)", "d": "Zwraca liczbę (punkt kodowy) odpowiadającą pierwszemu znakowi tekstu."}, "UPPER": {"a": "(tekst)", "d": "Konwertuje ciąg tekstowy na wielkie litery"}, "VALUE": {"a": "(tekst)", "d": "Konwertuje ciąg tekstowy reprezentujący liczbę na liczbę"}, "AVEDEV": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca odchylenie średnie (średnia z odchyleń bezwzględnych) punktów danych od ich wartości średniej. Argumentami mogą być liczby lub nazwy, tablice albo odwołania zawierające liczby"}, "AVERAGE": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwrac<PERSON> warto<PERSON> śred<PERSON> (śred<PERSON>ą arytmetyczną) podanych argumentów, które mogą być liczbami lub naz<PERSON>, tablicami albo odwołaniami zawierającymi liczby"}, "AVERAGEA": {"a": "(wartość1; [wartość2]; ...)", "d": "Zwraca wartość średniej arytmetycznej argumentów. Tekst i wartości logiczne FAŁSZ są przyjmowane jako 0; wartości logiczne PRAWDA są przyjmowane jako 1. Argumenty mogą by<PERSON>, na<PERSON><PERSON>, tablicami lub od<PERSON>"}, "AVERAGEIF": {"a": "(zakres; kryteria; [średnia_zakres])", "d": "Oblicza średnią arytmetyczną dla komórek spełniających podany warunek lub kryteria"}, "AVERAGEIFS": {"a": "(średnia_zakres; kryteria_zakres; kryteria; ...)", "d": "Znajduje średnią arytmetyczną dla komórek spełniających podany zestaw warunków lub kryteriów"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Zwraca funkcję gęstości skumulowanego rozkładu beta"}, "BETAINV": {"a": "(prawdopodobieństwo; alfa; beta; [A]; [B])", "d": "Zwraca odwrotność funkcji gęstości skumulowanego rozkładu beta (ROZKŁAD.BETA)"}, "BETA.DIST": {"a": "(x; alfa; beta; skumulowany; [A]; [B])", "d": "Zwraca funkcję rozkładu prawdopodobieństwa beta"}, "BETA.INV": {"a": "(prawdopodobieństwo; alfa; beta; [A]; [B])", "d": "Zwraca odwrotność funkcji gęstości prawdopodobieństwa skumulowanego rozkładu beta (ROZKŁ.BETA)"}, "BINOMDIST": {"a": "(liczba_s; próby; prawdopodob_s; sku<PERSON><PERSON><PERSON>)", "d": "Zwraca pojedynczy składnik dwumianowego rozkładu prawdopodobieństwa"}, "BINOM.DIST": {"a": "(liczba_s; próby; prawdopodob_s; sku<PERSON><PERSON><PERSON>)", "d": "Zwraca pojedynczy składnik dwumianowego rozkładu prawdopodobieństwa"}, "BINOM.DIST.RANGE": {"a": "(próby; prawdopodob_s; liczba_s; [liczba_s2])", "d": "Zwraca prawdopodobieństwo wyniku próby przy użyciu rozkładu dwumianowego."}, "BINOM.INV": {"a": "(próby; prawdopodob_s; alfa)", "d": "Zwraca najmnie<PERSON><PERSON><PERSON>, dla której skumulowany rozkład dwumianowy jest większy lub równy podanej wartości progowej"}, "CHIDIST": {"a": "(x; stopnie_swobody)", "d": "Zwraca prawostronne prawdopodobieństwo rozkładu chi-kwadrat"}, "CHIINV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca odwrot<PERSON>ść prawostronnego prawdopodobieństwa rozkładu chi-kwadrat"}, "CHITEST": {"a": "(zakres_bieżący; zakres_przewidywany)", "d": "Zwraca test na niezależność: warto<PERSON><PERSON> z rozkładu chi-kwadrat dla statystyki i odpowiednich stopni swobody"}, "CHISQ.DIST": {"a": "(x; stopnie_swobody; skumulowany)", "d": "Zwraca lewostronne prawdopodobieństwo rozkładu chi-kwadrat"}, "CHISQ.DIST.RT": {"a": "(x; stopnie_swobody)", "d": "Zwraca prawostronne prawdopodobieństwo rozkładu chi-kwadrat"}, "CHISQ.INV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca odw<PERSON><PERSON>ć lewostronnego prawdopodobieństwa rozkładu chi-kwadrat"}, "CHISQ.INV.RT": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca odwrot<PERSON>ść prawostronnego prawdopodobieństwa rozkładu chi-kwadrat"}, "CHISQ.TEST": {"a": "(zakres_bieżący; zakres_przewidywany)", "d": "Zwraca test na niezależność: warto<PERSON><PERSON> z rozkładu chi-kwadrat dla statystyki i odpowiednich stopni swobody"}, "CONFIDENCE": {"a": "(alfa; odchylenie_std; rozmiar)", "d": "Zwraca przedział ufności dla średniej populacji, używając rozkładu normalnego"}, "CONFIDENCE.NORM": {"a": "(alfa; odchylenie_std; rozmiar)", "d": "Zwraca przedział ufności dla średniej populacji, używając rozkładu normalnego"}, "CONFIDENCE.T": {"a": "(alfa; odchylenie_std; rozmiar)", "d": "Zwraca przedział ufności dla średniej populacji, używając rozkładu t-Studenta"}, "CORREL": {"a": "(tablica1; tablica2)", "d": "Oblicza współczynnik korelacji pomiędzy dwoma zbiorami danych"}, "COUNT": {"a": "(wartość1; [wartość2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ile komórek w zakresie zawiera liczby"}, "COUNTA": {"a": "(wartość1; [wartość2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ile niepustych komórek w zakresie "}, "COUNTBLANK": {"a": "(zakres)", "d": "Zlicza liczbę pustych komórek w określonym zakresie komórek"}, "COUNTIF": {"a": "(zakres; kryteria)", "d": "Oblicza liczbę komórek we wskazanym zakresie spełniających podane kryteria"}, "COUNTIFS": {"a": "(kryteria_zak<PERSON>; kryteria; ...)", "d": "Oblicza liczbę komórek spełniających podany zestaw warunków lub kryteriów"}, "COVAR": {"a": "(tablica1; tablica2)", "d": "Zwraca kowarian<PERSON>ję, średnią z iloczynów odchyleń dla każdej pary punktów w dwóch zbiorach"}, "COVARIANCE.P": {"a": "(tablica1; tablica2)", "d": "Zwraca kowariancj<PERSON> pop<PERSON>, czyli średnią iloczynów odchyleń dla każdej pary punktów danych w dwóch zbiorach danych"}, "COVARIANCE.S": {"a": "(tablica1; tablica2)", "d": "Zwraca kowariancję <PERSON>, czyli średnią iloczynów odchyleń dla każdej pary punktów danych w dwóch zbiorach danych"}, "CRITBINOM": {"a": "(próby; prawdopodob_s; alfa)", "d": "Zwraca najmnie<PERSON><PERSON><PERSON>, dla której skumulowany rozkład dwumianowy jest większy lub równy podanej wartości progowej"}, "DEVSQ": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca sumę kwadratów odchyleń punktów danych od średniej arytmetycznej z próbki"}, "EXPONDIST": {"a": "(x; lambda; skumu<PERSON>any)", "d": "Zwraca rozkład wykładniczy"}, "EXPON.DIST": {"a": "(x; lambda; skumu<PERSON>any)", "d": "Zwraca rozkład wykładniczy"}, "FDIST": {"a": "(x; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca (prawostronny) rozkład F prawdopodobieństwa (stopień zróżnicowania) dla dwóch zbiorów danych"}, "FINV": {"a": "(prawdopodobieństwo; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca odw<PERSON> (prawostronnego) rozkładu F prawdopodobieństwa: jeśli p = ROZKŁAD.F(x,...), wówczas ROZKŁAD.F.ODW(p,...) = x"}, "FTEST": {"a": "(tablica1; tablica2)", "d": "Zwraca wynik testu F, d<PERSON><PERSON><PERSON><PERSON> prawdopodobieństwa, że wariancje w Tablicy1 i Tablicy2 nie są istotnie różne"}, "F.DIST": {"a": "(x; stopnie_swobody1; stopnie_swobody2; skumu<PERSON>any)", "d": "Zwraca lewostronny rozkład F prawdopodobieństwa (stopień zróżnicowania) dla dwóch zbiorów danych"}, "F.DIST.RT": {"a": "(x; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca prawostronny rozkład F prawdopodobieństwa (stopień zróżnicowania) dla dwóch zbiorów danych"}, "F.INV": {"a": "(prawdopodobieństwo; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca odwrotność lewostronnego rozkładu F prawdopodobieństwa: jeśli p = ROZKŁ.F(x,...), wówczas ROZKŁ.F.ODW(p,...) = x"}, "F.INV.RT": {"a": "(prawdopodobieństwo; stopnie_swobody1; stopnie_swobody2)", "d": "Zwraca odwrotność prawostronnego rozkładu F prawdopodobieństwa: jeśli p = ROZKŁ.F.PS(x,...), wówczas ROZKŁ.F.ODWR.PS(p,...) = x"}, "F.TEST": {"a": "(tablica1; tablica2)", "d": "Zwraca wynik testu F, d<PERSON><PERSON><PERSON><PERSON> prawdopodobieństwa, że wariancje w Tablicy1 i Tablicy2 nie są istotnie różne"}, "FISHER": {"a": "(x)", "d": "Zwraca transformatę Fishera"}, "FISHERINV": {"a": "(y)", "d": "Zwraca odwrotną transformatę Fishera: jeśli y = ROZKŁAD.FISHER(x), wówczas ROZKŁAD.FISHER.ODW(y) = x"}, "FORECAST": {"a": "(x; znane_y; znane_x)", "d": "Oblicza lub przewid<PERSON><PERSON> warto<PERSON>ć przyszłą przy założeniu trendu liniowego i przy użyciu istniejących wartości"}, "FORECAST.ETS": {"a": "(data_docelowa; warto<PERSON>ci; oś_czasu; [se<PERSON><PERSON><PERSON><PERSON><PERSON>]; [komplet<PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca prognozowaną wartość dla konkretnej daty docelowej w przyszłości przy użyciu wykładniczej metody wygładzania."}, "FORECAST.ETS.CONFINT": {"a": "(data_docelowa; wartości; oś_czasu; [poziom_uf<PERSON>]; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]; [komple<PERSON><PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca przedział ufności dla wartości prognozy dla określonej daty docelowej."}, "FORECAST.ETS.SEASONALITY": {"a": "(war<PERSON><PERSON><PERSON>; oś_czasu; [komplet<PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca długość powtarzającego się wzorca wykrywanego przez program dla określonego szeregu czasowego."}, "FORECAST.ETS.STAT": {"a": "(war<PERSON><PERSON><PERSON>; oś_czasu; typ_statystyki; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]; [komplet<PERSON><PERSON><PERSON>_danych]; [agrega<PERSON><PERSON>])", "d": "Zwraca żądaną statystykę dla prognozy."}, "FORECAST.LINEAR": {"a": "(x; znane_y; znane_x)", "d": "Oblicza lub przewid<PERSON><PERSON> warto<PERSON>ć przyszłą przy założeniu trendu liniowego i przy użyciu istniejących wartości"}, "FREQUENCY": {"a": "(tablica_dane; tablica_przedziały)", "d": "Oblicza rozkład częstości występowania wartości w zakresie wartości i zwraca w postaci pionowej tablicy liczby, które mają o jeden element więcej niż tablica_bin"}, "GAMMA": {"a": "(x)", "d": "Zwraca wartość funkcji Gamma."}, "GAMMADIST": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca rozkład gamma"}, "GAMMA.DIST": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca rozkład gamma"}, "GAMMAINV": {"a": "(prawdopodobieństwo; alfa; beta)", "d": "Zwraca odwrotność skumulowanego rozkładu gamma: jeśli p = ROZKŁAD.GAMMA(x,...), wówczas ROZKŁAD.GAMMA.ODW(p,...) = x"}, "GAMMA.INV": {"a": "(prawdopodobieństwo; alfa; beta)", "d": "Zwraca odwrotność skumulowanego rozkładu gamma: jeśli p = ROZKŁ.GAMMA(x,...), to ROZKŁ.GAMMA.ODW(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Zwraca logarytm naturalny funkcji gamma"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Zwraca logarytm naturalny funkcji gamma"}, "GAUSS": {"a": "(x)", "d": "Zwraca rozkład prawdopodobieństwa o 0,5 mniejszy od standardowego skumulowanego rozkładu normalnego."}, "GEOMEAN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwrac<PERSON> warto<PERSON> średniej <PERSON>cznej dla tablicy lub zakresu dodatnich danych liczbowych"}, "GROWTH": {"a": "(znane_y; [znane_x]; [nowe_x]; [stała])", "d": "Zwraca liczby wykładniczego trendu wzrostu, dopasowane do znanych punktów danych"}, "HARMEAN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca wartość średnią harmoniczną zbioru danych liczb dodatnich: odwrot<PERSON>ść średniej arytmetycznej odwrotności"}, "HYPGEOM.DIST": {"a": "(próbka_s; wielk_próbki; populacja_s; wielk_populacji; skumulowany)", "d": "Zwraca rozkład hipergeometryczny"}, "HYPGEOMDIST": {"a": "(próbka_s; wielk_próbki; populacja_s; wielk_populacji)", "d": "Wyznacza rozkład hipergeometryczny"}, "INTERCEPT": {"a": "(znane_y; znane_x)", "d": "Oblicza miejsce przecięcia się linii z osią y, używając linii najlepszego dopasowania przechodzącej przez znane wartości x i y."}, "KURT": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca kurtozę zbioru da<PERSON>ch"}, "LARGE": {"a": "(tablica; k)", "d": "Zwraca k-tą najwięks<PERSON><PERSON> wartość w zbiorze danych, na przykład piątą najwięks<PERSON><PERSON> wartość"}, "LINEST": {"a": "(znane_y; [znane_x]; [stała]; [statystyka])", "d": "Zwraca statystykę opisującą trend liniowy, dopasowany do znanych punktów danych, dopasowując linię prostą przy użyciu metody najmniejszych kwadratów"}, "LOGEST": {"a": "(znane_y; [znane_x]; [stała]; [statystyka])", "d": "Zwraca statystykę, która opisuje krzywą wykładniczą dopasowaną do znanych punktów danych"}, "LOGINV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrotność skumulowanego rozkładu logarytmiczno-normalnego x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odchylenie_std"}, "LOGNORM.DIST": {"a": "(x; średnia; odchylenie_std; skumulowany)", "d": "Zwraca rozkład logarytmiczno-normalny dla wartości x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odchylenie_std"}, "LOGNORM.INV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrotność skumulowanego rozkładu logarytmiczno-normalnego x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odch_stand"}, "LOGNORMDIST": {"a": "(x; średnia; odchylenie_std)", "d": "Zwraca skumulowany rozkład logarytmiczno-normalny x, gdzie ln(x) ma rozkład normalny o parametrach Średnia i Odchylenie_std"}, "MAX": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca największą wartość ze zbioru wartości. Ignoruje wartości logiczne i tekst"}, "MAXA": {"a": "(wartość1; [wartość2]; ...)", "d": "Zwraca największą wartość ze zbioru wartości. Nie pomija wartości logicznych i tekstu"}, "MAXIFS": {"a": "(zakres_maks; zakres_kryteriów; kryteria; ...)", "d": "Zwraca warto<PERSON>ć maksymalną wśród komórek spełniających podany zestaw warunków lub kryteriów"}, "MEDIAN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca medianę lub liczbę w środku zbioru podanych liczb"}, "MIN": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najmniejszą wartość ze zbioru wartości. Ignoruje wartości logiczne i tekst"}, "MINA": {"a": "(wartość1; [wartość2]; ...)", "d": "Zwraca najmniejszą wartość ze zbioru wartości. Nie pomija wartości logicznych i tekstu"}, "MINIFS": {"a": "(zakres_min; zakres_kryteriów; kryteria; ...)", "d": "Zwraca wartość minimalną wśród komórek spełniających podany zestaw warunków lub kryteriów"}, "MODE": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najczęściej występującą lub powtarzaj<PERSON><PERSON>ą się wartoś<PERSON> w tablicy albo zakresie danych"}, "MODE.MULT": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca pionową tablicę zawierającą najczęściej występujące lub powtarzające się wartości w tablicy lub zakresie danych. W przypadku tablicy poziomej należy użyć funkcji =TRANSPONUJ(WYST.NAJCZĘŚCIEJ.TABL(liczba1;liczba2;...))"}, "MODE.SNGL": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najczęściej występującą lub powtarzaj<PERSON><PERSON>ą się wartoś<PERSON> w tablicy albo zakresie danych"}, "NEGBINOM.DIST": {"a": "(liczba_p; liczba_s; prawdopodobieństwo_s; sku<PERSON>lowany)", "d": "Zwraca ujemny rozkład dwu<PERSON>owy (prawdopodobieństwo, że wystąpi Liczba_p porażek przed sukcesem o numerze Liczba_s, z prawdopodobieństwem sukcesu równym Prawdopodobieństwo_s)"}, "NEGBINOMDIST": {"a": "(liczba_p; liczba_s; prawdopodob_s)", "d": "Zwraca rozkład dwumianowy ujemny, prawdopodobieństwo, że wystąpi Liczba_p porażek przed sukcesem nr Liczba_s z prawdopodobieństwem sukcesu Prawdopodob_s"}, "NORM.DIST": {"a": "(x; średnia; odchylenie_std; skumulowany)", "d": "Zwraca rozkład normalny dla podanej średniej i odchylenia standardowego"}, "NORMDIST": {"a": "(x; średnia; odchylenie_std; skumulowany)", "d": "Zwraca skumulowany rozkład normalny dla podanej średniej i odchylenia standardowego"}, "NORM.INV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrot<PERSON>ść skumulowanego rozkładu normalnego dla podanej średniej i odchylenia standardowego"}, "NORMINV": {"a": "(prawdopodobieństwo; średnia; odchylenie_std)", "d": "Zwraca odwrot<PERSON>ść skumulowanego rozkładu normalnego dla podanej średniej i odchylenia standardowego"}, "NORM.S.DIST": {"a": "(z; sku<PERSON><PERSON><PERSON>)", "d": "Zwraca standardowy rozkład normalny (o średniej zero i odchyleniu standardowym jeden)"}, "NORMSDIST": {"a": "(z)", "d": "Zwraca standardowy skumulowany rozkład normalny (o średniej zero i odchyleniu standardowym jeden)"}, "NORM.S.INV": {"a": "(prawdopodobieństwo)", "d": "Zwraca odwrotność standardowego skumulowanego rozkładu normalnego (o średniej zero i odchyleniu standardowym jeden)"}, "NORMSINV": {"a": "(prawdopodobieństwo)", "d": "Zwraca odwrotność standardowego skumulowanego rozkładu normalnego (o średniej zero i odchyleniu standardowym jeden)"}, "PEARSON": {"a": "(tablica1; tablica2)", "d": "Zwraca współczynnik korelacji momentów iloczynu Pearsona, r"}, "PERCENTILE": {"a": "(tablica; k)", "d": "Wyznacza k-ty percentyl wartości w zakresie"}, "PERCENTILE.EXC": {"a": "(tablica; k)", "d": "Zwraca k-ty percentyl wartości w zakresie, gdzie k należy do zakresu od 0 do 1 (bez wartości granicznych)"}, "PERCENTILE.INC": {"a": "(tablica; k)", "d": "Zwraca k-ty percentyl wartości w zakresie, gdzie k należy do zakresu od 0 do 1 włącznie"}, "PERCENTRANK": {"a": "(tablica; x; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Wyznacza pozycję procentową wartości w zbiorze danych"}, "PERCENTRANK.EXC": {"a": "(tablica; x; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zwraca pozycję procentową wartości w zbiorze danych, należącą do zakresu od 0 do 1 (bez wartości granicznych)"}, "PERCENTRANK.INC": {"a": "(tablica; x; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zwraca pozycję procentową wartości w zbiorze danych, należącą do zakresu od 0 do 1 włącznie"}, "PERMUT": {"a": "(liczba; wybór_liczba)", "d": "Zwraca liczbę permutacji dla podanej liczby obiektów, które można wybrać ze wszystkich obiektów"}, "PERMUTATIONA": {"a": "(liczba; liczba_wybrana)", "d": "Zwraca liczbę permutacji dla podanej liczby obiektów (z powtórzeniami), które można wybrać ze wszystkich obiektów."}, "PHI": {"a": "(x)", "d": "Zwraca wartość funkcji gęstości dla standardowego rozkładu normalnego."}, "POISSON": {"a": "(x; średnia; skumulowany)", "d": "Zwraca rozkład <PERSON>"}, "POISSON.DIST": {"a": "(x; średnia; skumulowany)", "d": "Zwraca rozkład <PERSON>"}, "PROB": {"a": "(zakres_x; zakres_prawdop; dolna_granica; [górna_granica])", "d": "Zwraca prawdopodobieństwo, że wartości w zakresie znajdują się między dwoma granicami lub są równe granicy dolnej"}, "QUARTILE": {"a": "(tablica; kwarty)", "d": "Wyznacza kwartyl zbioru da<PERSON>ch"}, "QUARTILE.INC": {"a": "(tablica; kwartyl)", "d": "Zwraca kwartyl zbioru danych na podstawie wartości percentylu z zakresu od 0 do 1 włącznie"}, "QUARTILE.EXC": {"a": "(tablica; kwartyl)", "d": "Zwraca kwartyl zbioru danych na podstawie wartości percentylu z zakresu od 0 do 1 (bez wartości granicznych)"}, "RANK": {"a": "(liczba; lista; [lp])", "d": "Zwraca pozycję liczby na liście liczb: jej rozmiar względem innych wartości na liście"}, "RANK.AVG": {"a": "(l<PERSON><PERSON>; odwołanie; [lp])", "d": "Zwraca pozycję liczby na liście liczb: jej wiel<PERSON> względem innych wartości na liście; jeśli więcej niż jedna wartość ma taką samą pozycję, jest zwracana średnia pozycja"}, "RANK.EQ": {"a": "(l<PERSON><PERSON>; odwołanie; [lp])", "d": "Zwraca pozycję liczby na liście liczb: jej wiel<PERSON> względem innych wartości na liście; jeśli więcej niż jedna wartość ma taką samą pozycję, jest zwracana najwyższa pozycja zbioru warto<PERSON>ci"}, "RSQ": {"a": "(znane_y; znane_x)", "d": "Zwraca kwadrat współczynnika Pearsona korelacji iloczynu momentów dla zadanych punktów danych"}, "SKEW": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca skośność rozkładu prawdopodobieństwa: charakteryzują<PERSON>ą stopień asymetrii rozkładu wokół średniej"}, "SKEW.P": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca skośność rozkładu prawdopodobieństwa na podstawie populacji: charakteryzującą stopień asymetrii rozkładu wokół średniej."}, "SLOPE": {"a": "(znane_y; znane_x)", "d": "Zwraca nachylenie wykresu regresji liniowej przez zadane punkty danych"}, "SMALL": {"a": "(tablica; k)", "d": "Zwraca k-tą najmniej<PERSON><PERSON> warto<PERSON> w zbiorze danych, na przykład piątą najmniejszą liczbę"}, "STANDARDIZE": {"a": "(x; średnia; odchylenie_std)", "d": "Zwraca wartość znormalizowaną z rozkładu scharakteryzowanego przez średnią i odchylenie standardowe"}, "STDEV": {"a": "(liczba1; [liczba2]; ...)", "d": "Dokonuje oszacowania odchylenia standardowego dla podanej próbki (pomija wartości logiczne i tekstowe w próbce)"}, "STDEV.P": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza odchylenie standardowe w oparciu o całą populację zadaną jako argument (pomija wartości logiczne i tekstowe)"}, "STDEV.S": {"a": "(liczba1; [liczba2]; ...)", "d": "Dokonuje oszacowania odchylenia standardowego dla podanej próbki (pomija wartości logiczne i tekstowe w próbce)"}, "STDEVA": {"a": "(wartość1; [wartość2]; ...)", "d": "Szacuje odchylenie standardowe na podstawie próbki uwzględniając wartości logiczne oraz tekst. Wartość logiczna FAŁSZ i wartości tekstowe są traktowane jako 0, a wartość logiczna PRAWDA jako 1."}, "STDEVP": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza odchylenie standardowe na podstawie całej populacji zadanej jako argument (pomija wartości logiczne i tekstowe)"}, "STDEVPA": {"a": "(wartość1; [wartość2]; ...)", "d": "Oblicza odchylenie standardowe w oparciu o całą populację, włącznie z wartościami logicznymi i tekstem. Teksty i wartości logiczne FAŁSZ są traktowane jako 0; <PERSON><PERSON><PERSON> wartość PRAWDA jest traktowana jako 1"}, "STEYX": {"a": "(znane_y; znane_x)", "d": "Zwraca błąd standardowy przewidywanej wartości y dla każdej wartości x w regresji"}, "TDIST": {"a": "(x; stopnie_swobody; strony)", "d": "Zwraca rozkład t-Studenta"}, "TINV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca dwustronną odwrotność rozkładu t-Studenta"}, "T.DIST": {"a": "(x; stopnie_swobody; skumulowany)", "d": "Zwraca lewostronny rozkład t-Studenta"}, "T.DIST.2T": {"a": "(x; stopnie_swobody)", "d": "Zwraca dwustronny rozkład t-Studenta"}, "T.DIST.RT": {"a": "(x; stopnie_swobody)", "d": "Zwraca prawostronny rozkład t-Studenta"}, "T.INV": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca lewostronną odwrotność rozkładu t-Studenta"}, "T.INV.2T": {"a": "(prawdopodobieństwo; stopnie_swobody)", "d": "Zwraca dwustronną odwrotność rozkładu t-Studenta"}, "T.TEST": {"a": "(tablica1; tablica2; strony; typ)", "d": "Zwraca prawdopodobieństwo związane z testem t-Studenta"}, "TREND": {"a": "(znane_y; [znane_x]; [nowe_x]; [stała])", "d": "Zwraca liczby trendu liniowego dopasowane do znanych punktów danych przy użyciu metody najmniejszych kwadratów"}, "TRIMMEAN": {"a": "(tablica; procent)", "d": "Zwraca wartość średnią z wewnętrznej części zbioru wartości danych"}, "TTEST": {"a": "(tablica1; tablica2; strony; typ)", "d": "Zwraca prawdopodobieństwo związane z testem t-Studenta"}, "VAR": {"a": "(liczba1; [liczba2]; ...)", "d": "Wyznacza wariancję na podstawie próbki (pomija wartości logiczne i tekstowe w próbce)"}, "VAR.P": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza wariancję na podstawie całej populacji (pomija wartości logiczne i tekstowe w próbce)"}, "VAR.S": {"a": "(liczba1; [liczba2]; ...)", "d": "Wyznacza wariancję na podstawie próbki (pomija wartości logiczne i tekstowe w próbce)"}, "VARA": {"a": "(wartość1; [wartość2]; ...)", "d": "Szacuje wariancję na podstawie próbki uwzględniając wartości logiczne oraz tekst. Wartość logiczna FAŁSZ i wartości tekstowe są traktowane jako 0, a wartość logiczna PRAWDA jako 1."}, "VARP": {"a": "(liczba1; [liczba2]; ...)", "d": "Oblicza wariancję na podstawie całej populacji (pomija wartości logiczne i tekstowe w próbce)"}, "VARPA": {"a": "(wartość1; [wartość2]; ...)", "d": "Oblicza wariancję w oparciu o całą populację, włącznie z wartościami logicznymi i tekstem. Teksty i wartości logiczne FAŁSZ są traktowane jako 0; <PERSON><PERSON><PERSON> wartość PRAWDA jest traktowana jako 1"}, "WEIBULL": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca r<PERSON><PERSON><PERSON><PERSON>"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; skumulowany)", "d": "Zwraca r<PERSON><PERSON><PERSON><PERSON>"}, "Z.TEST": {"a": "(tablica; x; [sigma])", "d": "Zwraca wartość P o jednej stronie oraz test z"}, "ZTEST": {"a": "(tablica; x; [sigma])", "d": "Zwraca wartość P o jednej stronie oraz test z"}, "ACCRINT": {"a": "(em<PERSON><PERSON>; pier<PERSON><PERSON>_ods<PERSON>ki; roz<PERSON><PERSON><PERSON>; stopa; cena_nom; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa]; [metoda_obliczeń])", "d": "Oblicza naliczone odsetki dla papieru wartościowego oprocentowanego okresowo"}, "ACCRINTM": {"a": "(emis<PERSON>; roz<PERSON><PERSON><PERSON>; stopa; cena_nom; [podstawa])", "d": "Zwrac<PERSON> warto<PERSON>ć procentu składanego dla papieru wartościowego oprocentowanego przy wykupie"}, "AMORDEGRC": {"a": "(cena; data_zakupu; pierwszy_okres; odzysk; okres; stopa; [podstawa])", "d": "Zwrac<PERSON> war<PERSON> amortyzacji dla każdego okresu rozliczeniowego"}, "AMORLINC": {"a": "(cena; data_zakupu; pierwszy_okres; odzysk; okres; stopa; [podstawa])", "d": "Zwrac<PERSON> war<PERSON> amortyzacji dla każdego okresu rozliczeniowego"}, "COUPDAYBS": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza liczbę dni od początku okresu dywidendy do daty rozliczenia"}, "COUPDAYS": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza liczbę dni w okresie dywidendy obejmującym datę rozliczenia"}, "COUPDAYSNC": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca liczbę dni do daty następnej dywidendy"}, "COUPNCD": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca datę następnej dywidendy po dacie roz<PERSON>ia"}, "COUPNUM": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": " Zwraca liczbę wypłacanych dywidend między datą rozliczenia i datą spłaty"}, "COUPPCD": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza datę dywidendy poprzedzającej datę rozliczenia"}, "CUMIPMT": {"a": "(stopa; liczba_okresów; wb; okres_pocz; okres_końc; typ)", "d": "Zwraca wartość skumulowanych odsetek zapłaconych w czasie między podanymi okresami"}, "CUMPRINC": {"a": "(stopa; liczba_okresów; wb; okres_pocz; okres_końc; typ)", "d": "Zwraca wartość skumulowanych odsetek zapłaconych w czasie między podanymi okresami"}, "DB": {"a": "(koszt; odzysk; czas_życia; okres; [mi<PERSON><PERSON><PERSON>])", "d": "Zwraca amortyzację środka trwałego za podany okres metodą równomiernie malejącego salda"}, "DDB": {"a": "(koszt; odzysk; czas_życia; okres; [współczynnik])", "d": "Zwraca amortyzację środka trwałego za podany okres obliczoną metodą podwójnego spadku lub inną metodą określoną przez użytkownika"}, "DISC": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kwota; wykup; [podstawa])", "d": "Zwrac<PERSON> warto<PERSON> stopy dyskontowej papieru wartościowego"}, "DOLLARDE": {"a": "(wartoś<PERSON>_ułamkowa; ułamek)", "d": "Zamienia cenę w postaci ułamkowej na cenę w postaci dziesiętnej"}, "DOLLARFR": {"a": "(wartość_dziesiętna; ułamek)", "d": "Zamienia cenę w postaci dziesiętnej na cenę w postaci ułamkowej"}, "DURATION": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kupon; rent<PERSON>; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca wartość rocznego przychodu z papieru wartościowego o okresowych wypłatach odsetek"}, "EFFECT": {"a": "(stopa_nominalna; okresy)", "d": "Zwraca wartość efektywnej rocznej stopy oprocentowania"}, "FV": {"a": "(stopa; liczba_okresów; p<PERSON><PERSON><PERSON><PERSON><PERSON>; [wb]; [typ])", "d": "Oblicza przyszłą wartość inwestycji na podstawie okresowych, stałych płatności i stałej stopy procentowej"}, "FVSCHEDULE": {"a": "(kapitał; stopy)", "d": "Zwraca wartość przyszłą kapitału początkowego wraz z szeregiem rat procentu składanego"}, "INTRATE": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; lokata; wykup; [podstawa])", "d": "Zwraca warto<PERSON>ć stopy procentowej papieru wartościowego całkowicie ulokowanego"}, "IPMT": {"a": "(stopa; okres; liczba_okresów; wb; [wp]; [typ])", "d": "Oblicza wysokość odsetek z inwestycji w danym okresie przy założeniu okresowych, stałych płatności i stałej stopy procentowej"}, "IRR": {"a": "(war<PERSON><PERSON><PERSON>; [wynik])", "d": "Oblicza wewnętrzną stopę zwrotu dla przepływów gotówkowych"}, "ISPMT": {"a": "(stopa; okres; liczba_okresów; wb)", "d": "Oblicza wartość odsetek zapłaconych w trakcie określonego okresu inwestycji"}, "MDURATION": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kupon; rent<PERSON>; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca warto<PERSON>ć zmodyfikowanego okresu Macauleya dla papieru wartościowego o założonej wartości 100 jednostek"}, "MIRR": {"a": "(warto<PERSON><PERSON>; stopa_finansowa; stopa_reinwestycji)", "d": "Oblicza wewnętrzną stopę zwrotu dla serii okresowych przepływów gotówkowych przy uwzględnieniu kosztu inwestycji i stopy procentowej reinwestycji gotówki"}, "NOMINAL": {"a": "(stopa_efektywna; okresy)", "d": "Zwraca warto<PERSON>ć minimalnej rocznej stopy oprocentowania"}, "NPER": {"a": "(stopa; płat<PERSON><PERSON><PERSON>; wb; [wp]; [typ])", "d": "Oblicza liczbę okresów dla inwestycji opartej na okresowych, stałych płatnościach przy stałym oprocentowaniu"}, "NPV": {"a": "(stopa; wartość1; [wartość2]; ...)", "d": "Oblicza wartość bieżącą netto inwestycji w oparciu o okresowe przepływy środków pieniężnych przy określonej stopie dyskontowej oraz serii przyszłych płatności (wartości ujemne) i wpływów (wartości dodatnie)"}, "ODDFPRICE": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; emisja; pierwszy_kupon; stopa; rent<PERSON>; wykup; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego z nietypowym pierwszym okresem"}, "ODDFYIELD": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; emisja; pierwszy_kupon; stopa; kwota; wykup; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa])", "d": "Oblicza rentowność papieru wartościowego z nietypowym pierwszym okresem"}, "ODDLPRICE": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; ostatnia_wypłata; stopa; rent<PERSON>; wykup; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego z nietypowym ostatnim okresem"}, "ODDLYIELD": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; ostatnia_wypłata; stopa; kwota; wykup; c<PERSON><PERSON><PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca rentowno<PERSON>ć papieru wartościowego z nietypowym ostatnim okresem"}, "PDURATION": {"a": "(stopa; wb; wp)", "d": "Zwraca liczbę okresów wymaganych przez inwestycję do osiągnięcia określonej wartości."}, "PMT": {"a": "(stopa; liczba_okresów; wb; [wp]; [typ])", "d": "Oblicza ratę spłaty pożyczki opartej na stałych ratach i stałym oprocentowaniu"}, "PPMT": {"a": "(stopa; okres; liczba_okresów; wb; [wp]; [typ])", "d": "O<PERSON>lic<PERSON> wartość spłaty kapitału dla danej inwestycji przy założeniu okresowych, stałych płatności i stałego oprocentowania"}, "PRICE": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; stopa; rentown<PERSON>; wykup; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego o okresowym oprocentowaniu"}, "PRICEDISC": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; dyskonto; wykup; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego zdyskontowanego"}, "PRICEMAT": {"a": "(roz<PERSON><PERSON><PERSON>; data_spłaty; emisja; stopa; rentown<PERSON>; [podstawa])", "d": "Zwraca cenę za 100 jednostek wartości nominalnej papieru wartościowego oprocentowanego przy wykupie"}, "PV": {"a": "(stopa; liczba_okresów; p<PERSON><PERSON><PERSON><PERSON><PERSON>; [wp]; [typ])", "d": "Oblic<PERSON> wartość bieżącą inwestycji — całkowitą obecną wartość serii przyszłych pła<PERSON>ci"}, "RATE": {"a": "(liczba_okresów; p<PERSON><PERSON><PERSON><PERSON><PERSON>; wb; [wp]; [typ]; [wynik])", "d": "Oblicza stopę procentową dla okresu pożyczki lub inwestycji. Np. użyj stopy 6%/4 dla płatności kwartalnych w przypadku 6% stopy rocznej"}, "RECEIVED": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; lokata; dyskonto; [podstawa])", "d": "Zwraca wartość kapitału otrzymanego przy wykupie papieru wartościowego całkowicie ulokowanego"}, "RRI": {"a": "(liczba_rat; wb; wp)", "d": "Zwraca równoważną stopę procentową dla wzrostu inwestycji."}, "SLN": {"a": "(koszt; odzysk; czas_życia)", "d": "Zwraca amortyzację środka trwałego za pojedynczy okres metodą liniową"}, "SYD": {"a": "(koszt; odzysk; czas_życia; okres)", "d": "Oblicza amortyzację środka trwałego za podany okres metodą sumy cyfr wszystkich lat amortyzacji"}, "TBILLEQ": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; dyskonto)", "d": "Zwrac<PERSON> ekwiwalentu obligacji dla bonu skarbowego"}, "TBILLPRICE": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; dyskonto)", "d": "Zwraca cenę za 100 jednostek wartości nominalnej bonu skar<PERSON>ego"}, "TBILLYIELD": {"a": "(roz<PERSON>zenie; data_spłaty; kwota)", "d": "<PERSON><PERSON><PERSON><PERSON> bonu skar<PERSON>"}, "VDB": {"a": "(koszt; odzysk; czas_życia; początek; koniec; [współczynnik]; [bez_prz<PERSON>ł<PERSON><PERSON><PERSON>])", "d": "Zwraca amortyzację środka trwałego za podany okres lub jego część obliczoną metodą podwójnie malejącego salda lub inną podaną metodą"}, "XIRR": {"a": "(war<PERSON><PERSON><PERSON>; daty; [wynik])", "d": "O<PERSON>lic<PERSON> warto<PERSON>ć wewnętrznej stopy zwrotu dla serii rozłożonych w czasie przepływów gotówkowych"}, "XNPV": {"a": "(stopa; wartości; daty)", "d": "Oblicza wartość bieżącą netto serii rozłożonych w czasie przepływów gotówkowych"}, "YIELD": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; stopa; kwota; wykup; cz<PERSON><PERSON><PERSON>; [podstawa])", "d": "Zwraca rentown<PERSON>ć papieru wartościowego o okresowym oprocentowaniu"}, "YIELDDISC": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; kwota; wykup; [podstawa])", "d": "Zwraca roczną rent<PERSON> zdyskontowanego papieru war<PERSON>ściowego, np. bonu skarbowego"}, "YIELDMAT": {"a": "(roz<PERSON><PERSON>ie; data_spłaty; emisja; stopa; kwota; [podstawa])", "d": "Zwraca roczną rentown<PERSON> papieru wartościowego oprocentowanego przy wykupie"}, "ABS": {"a": "(liczba)", "d": "Zwraca wartość bezwzględną liczby, warto<PERSON><PERSON> bez znaku"}, "ACOS": {"a": "(liczba)", "d": "Zwraca arcus cosinus liczby w radianach w zakresie od 0 do Pi. Arcus cosinus jest kątem, którego cosinus daje liczbę"}, "ACOSH": {"a": "(liczba)", "d": "<PERSON>wrac<PERSON> arcus cosinus hip<PERSON> liczby"}, "ACOT": {"a": "(liczba)", "d": "Zwraca arcus cotangens liczby w radianach w zakresie od 0 do Pi."}, "ACOTH": {"a": "(liczba)", "d": "Zwraca arcus cotangens hiperboliczny liczby."}, "AGGREGATE": {"a": "(nr_<PERSON><PERSON><PERSON>; opcje; odw1; ...)", "d": "Zwrac<PERSON> war<PERSON> zagregowaną z listy lub bazy danych"}, "ARABIC": {"a": "(tekst)", "d": "Konwertuje liczbę rzymską na arabską."}, "ASC": {"a": "(tekst)", "d": "W językach korzystających z dwubajtowego zestawu znaków (DBCS) funkcja zmienia znaki o pełnej szerokości (dwubajtowe) na znaki o połówkowej szerokości (jednobajtowe)"}, "ASIN": {"a": "(liczba)", "d": "Zwraca arcus sinus liczby w radianach w zakresie od -Pi/2 do Pi/2"}, "ASINH": {"a": "(liczba)", "d": "Zwraca arcus sinus hiperboliczny liczby"}, "ATAN": {"a": "(liczba)", "d": "Zwraca arcus tangens liczby w radianach w zakresie od -Pi/2 do Pi/2"}, "ATAN2": {"a": "(x_liczba; y_liczba)", "d": "Zwraca na podstawie współrzędnych x i y arcus tangens wyrażony w radianach w zakresie od -Pi do Pi z wyłączeniem -Pi"}, "ATANH": {"a": "(liczba)", "d": "Zwraca arcus tangens hiperboliczny liczby"}, "BASE": {"a": "(l<PERSON><PERSON>; podstawa; [dług<PERSON><PERSON><PERSON>_min])", "d": "Konwertuje liczbę na reprezentację tekstową o podanej podstawie."}, "CEILING": {"a": "(l<PERSON><PERSON>; istotność)", "d": "Zaokrągla liczbę w górę do najbliższej wielokrotności podanej istotności"}, "CEILING.MATH": {"a": "(liczba; [isto<PERSON><PERSON><PERSON><PERSON>]; [tryb])", "d": "Zaokrągla liczbę w górę do najbliższej liczby całkowitej lub najbliż<PERSON>ej wielokrotności istotności."}, "CEILING.PRECISE": {"a": "(l<PERSON><PERSON>; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę w górę do najbliższ<PERSON> wartości całkowitej lub wielokrotności podanej istotności"}, "COMBIN": {"a": "(liczba; liczba_wybrana)", "d": "Zwraca liczbę kombinacji dla danej liczby elementów"}, "COMBINA": {"a": "(liczba; liczba_wybrana)", "d": "Zwraca liczbę kombinacji z powtórzeniami dla podanej liczby elementów."}, "COS": {"a": "(liczba)", "d": "Zwraca cosinus k<PERSON>ta"}, "COSH": {"a": "(liczba)", "d": "Zwraca cosinus hip<PERSON>bolic<PERSON> l<PERSON>by"}, "COT": {"a": "(liczba)", "d": "Zwraca cotangens kąta."}, "COTH": {"a": "(liczba)", "d": "Zwraca cotangens hiperboliczny liczby."}, "CSC": {"a": "(liczba)", "d": "Zwraca cosecans kąta."}, "CSCH": {"a": "(liczba)", "d": "Zwraca cosecans hiperboliczny kąta."}, "DECIMAL": {"a": "(liczba; podstawa)", "d": "Konwertuje reprezentację tekstową liczby o podanej podstawie na liczbę dziesiętną."}, "DEGREES": {"a": "(kąt)", "d": "Konwertuje radiany na stopnie"}, "ECMA.CEILING": {"a": "(l<PERSON><PERSON>; istotność)", "d": "Zaokrągla liczbę w górę do najbliższej wielokrotności podanej istotności"}, "EVEN": {"a": "(liczba)", "d": "Zaokrągla liczbę dodatnią w górę, a liczbę ujemną w dół do najbliższej parzystej liczby całkowitej"}, "EXP": {"a": "(liczba)", "d": "Oblicza wartość liczby e podniesionej do potęgi określonej przez podaną liczbę"}, "FACT": {"a": "(liczba)", "d": "Oblicza silnię podanej liczby równą 1*2*3...* Liczba"}, "FACTDOUBLE": {"a": "(liczba)", "d": "Zwraca dwukrotną silnię liczby"}, "FLOOR": {"a": "(l<PERSON><PERSON>; istotność)", "d": "Zaokrągla liczbę w dół do najbliższej wielokrotności podanej istotności"}, "FLOOR.PRECISE": {"a": "(l<PERSON><PERSON>; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę w dół do najbli<PERSON><PERSON><PERSON> wartości całkowitej lub wielokrotności podanej istotności"}, "FLOOR.MATH": {"a": "(liczba; [isto<PERSON><PERSON><PERSON><PERSON>]; [tryb])", "d": "Zaokrągla liczbę w dół do najbliższej liczby całkowitej lub najbliż<PERSON>ej wielokrotności istotności."}, "GCD": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca największy wspólny dzielnik"}, "INT": {"a": "(liczba)", "d": "Zaokrągla liczbę w dół do najbliższej liczby całkowitej"}, "ISO.CEILING": {"a": "(l<PERSON><PERSON>; [isto<PERSON><PERSON><PERSON><PERSON>])", "d": "Zaokrągla liczbę w górę do najb<PERSON><PERSON>sz<PERSON> wartości całkowitej lub wielokrotności podanej istotności. Zaokrąglenie następuje w górę niezależnie od znaku liczby. <PERSON><PERSON><PERSON> liczba lub isto<PERSON><PERSON>ć wynosi zero, jest zwracana wartość zero."}, "LCM": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca najmniejszą wspólną wielokrotność"}, "LN": {"a": "(liczba)", "d": "Zwraca logarytm naturalny podanej liczby"}, "LOG": {"a": "(liczba; [podstawa])", "d": "Zwraca logarytm liczby przy podanej podstawie"}, "LOG10": {"a": "(liczba)", "d": "Oblicza logarytm dziesiętny podanej liczby"}, "MDETERM": {"a": "(tablica)", "d": "Zwraca wyznacznik podanej tablicy"}, "MINVERSE": {"a": "(tablica)", "d": "Zwraca macierz odwrotną do macierzy przechowywanej w tablicy"}, "MMULT": {"a": "(tablica1; tablica2)", "d": "Zwraca iloczyn dwóch tablic, tablica o tej samej liczbie wierszy, co tablica1 i tej samej liczbie kolumn, co tablica2"}, "MOD": {"a": "(liczba; dzielnik)", "d": "Zwraca resztę z dzielenia"}, "MROUND": {"a": "(l<PERSON><PERSON>; wielokrotność)", "d": "Zwraca wartość liczby zaokrąglonej do podanej wielokrotności"}, "MULTINOMIAL": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca wielomian dla zestawu liczb"}, "MUNIT": {"a": "(wymiar)", "d": "Zwraca macierz jednostkową dla określonego wymiaru."}, "ODD": {"a": "(liczba)", "d": "Zaokrągla liczbę dodatnią w górę, a liczbę ujemną w dół do najbliższej liczby nieparzystej całkowitej"}, "PI": {"a": "()", "d": "Z<PERSON><PERSON><PERSON> liczby Pi, 3,14159265358979 z dokładnością do 15 cyfr po przecinku"}, "POWER": {"a": "(liczba; potęga)", "d": "Zwraca liczbę podniesioną do potęgi"}, "PRODUCT": {"a": "(liczba1; [liczba2]; ...)", "d": "Mnoży wszystkie liczby dane jako argumenty"}, "QUOTIENT": {"a": "(dzielna; dzielnik)", "d": "Zwraca część całkowitą z dzielenia"}, "RADIANS": {"a": "(kąt)", "d": "Konwertuje stopnie na radiany"}, "RAND": {"a": "()", "d": "Zwraca liczbę losową o równomiernym rozkładzie, która jest większa lub równa 0 i mniejsza niż 1 (zmienia się przy ponownym obliczaniu)"}, "RANDARRAY": {"a": "([wiersze]; [kolumny]; [min]; [maks]; [licz<PERSON>_całkowita])", "d": "Zwraca tablicę liczb losowych"}, "RANDBETWEEN": {"a": "(dół; góra)", "d": "Zwraca liczbę losową z przedziału pomiędzy podanymi wartościami"}, "ROMAN": {"a": "(liczba; [forma])", "d": "Konwertuje liczbę arabską na rzymską jako tekst"}, "ROUND": {"a": "(liczba; liczba_cyfr)", "d": "Zaokrągla liczbę do określonej liczby cyfr"}, "ROUNDDOWN": {"a": "(liczba; liczba_cyfr)", "d": "Zaokrągla liczbę w dół (w kierunku: do zera)"}, "ROUNDUP": {"a": "(liczba; liczba_cyfr)", "d": "Zaokrągla liczbę w górę (w kierunku: od zera)"}, "SEC": {"a": "(liczba)", "d": "Zwraca secans kąta."}, "SECH": {"a": "(liczba)", "d": "Zwraca secans hiperboliczny kąta."}, "SERIESSUM": {"a": "(x; n; m; współcz<PERSON>iki)", "d": "Oblicza sumę szeregu potęgowego wg odpowiedniego wzoru"}, "SIGN": {"a": "(liczba)", "d": "Zwraca znak podanej liczby: 1, je<PERSON><PERSON> liczba jest doda<PERSON>nia, zero, jeśli jest równa zero lub -1, je<PERSON>li jest ujemna"}, "SIN": {"a": "(liczba)", "d": "Zwraca sinus kąta"}, "SINH": {"a": "(liczba)", "d": "Oblicza sinus hiperboliczny liczby"}, "SQRT": {"a": "(liczba)", "d": "Zwraca pierwiastek kwadratowy liczby"}, "SQRTPI": {"a": "(liczba)", "d": "Zwraca pierwiastek kwadratowy z wartości (liczba * pi)"}, "SUBTOTAL": {"a": "(funkcja_nr; adres1; ...)", "d": "Oblicza sumę częściową listy lub bazy danych"}, "SUM": {"a": "(liczba1; [liczba2]; ...)", "d": "Dodaje wszystkie liczby w zakresie komórek"}, "SUMIF": {"a": "(zakres; kryteria; [suma_zakres])", "d": "Dodaje komórki spełniające podane warunki lub kryteria"}, "SUMIFS": {"a": "(suma_zakres; kryteria_zakres; kryteria; ...)", "d": "Oblicza sumę komórek spełniających dany zestaw warunków lub kryteriów"}, "SUMPRODUCT": {"a": "(tablica1; [tablica2]; [tablica3]; ...)", "d": "Zwraca sumę iloczynów odpowiadających sobie zakresów lub tablic"}, "SUMSQ": {"a": "(liczba1; [liczba2]; ...)", "d": "Zwraca sumę kwadratów argumentów. Argumenty mogą by<PERSON> l<PERSON>, tab<PERSON>mi, nazwami lub odwołaniami do komórek zawierających liczby"}, "SUMX2MY2": {"a": "(tablica_x; tablica_y)", "d": "Sumuje różnice między kwadratami dwóch odpowiadających sobie zakresów lub tablic"}, "SUMX2PY2": {"a": "(tablica_x; tablica_y)", "d": "Zwraca sumę końcową sum kwadratów liczb w dwóch odpowiadających sobie zakresach lub tablicach"}, "SUMXMY2": {"a": "(tablica_x; tablica_y)", "d": "Sumuje kwadraty różnic w dwóch odpowiadających sobie zakresach lub tablicach"}, "TAN": {"a": "(liczba)", "d": "Zwraca tangens kąta"}, "TANH": {"a": "(liczba)", "d": "Zwraca tangens hiperboliczny liczby"}, "TRUNC": {"a": "(liczba; [liczba_cyfr])", "d": "Obcina liczbę do liczby całkowitej, usuwając część dziesiętną lub ułamkową"}, "ADDRESS": {"a": "(nr_w<PERSON><PERSON>; nr_kolumny; [typ_adresu]; [a1]; [tekst_a<PERSON><PERSON><PERSON>])", "d": "Tworzy tekst odwołania do komórki z podanego numeru wiersza i numeru komórki"}, "CHOOSE": {"a": "(nr_arg; wartość1; [wartość2]; ...)", "d": "Wybiera z listy wartość lub czynność do wykonania na podstawie numeru wskaźnika."}, "COLUMN": {"a": "([odwo<PERSON>nie])", "d": "Zwraca numer kolumny odpowiadający podanemu odwołaniu"}, "COLUMNS": {"a": "(tablica)", "d": "Zwraca liczbę kolumn w tablicy lub odwołaniu"}, "FORMULATEXT": {"a": "(odwołanie)", "d": "Zwraca formułę jako ciąg."}, "HLOOKUP": {"a": "(odniesienie; tablica; nr_wiersza; [wiersz])", "d": "<PERSON>yszu<PERSON><PERSON> wartość w górnym wierszu tabeli lub tablicy wartości i zwraca wartość z tej samej kolumny ze wskazanego wiersza"}, "HYPERLINK": {"a": "(łącze_lokalizacja; [przy<PERSON>zna_nazwa])", "d": "Tworzy skrót lub skok, który otwiera dokument przechowywany na dysku twardym, serwerze sieciowym lub w Internecie"}, "INDEX": {"a": "(tablica; nr_wiersza; [nr_kolumny]!odwołanie; nr_wiersza; [nr_kolumny]; [nr_obszaru])", "d": "Zwraca wartość lub odwołanie do komórki na przecięciu określonego wiersza i kolumny w danym zakresie"}, "INDIRECT": {"a": "(adres_tekst; [a1])", "d": "Zwraca adres wskazany przez wartość tekstową"}, "LOOKUP": {"a": "(szukana_warto<PERSON><PERSON>; prz<PERSON><PERSON><PERSON><PERSON>_wektor; [wektor_wyniko<PERSON>]!szukana_wartość; tablica)", "d": "Wyszuku<PERSON> wartość z zakresu jednowierszowego lub jednokolumnowego albo z tablicy. Zapewnia zgodność z poprzednimi wersjami"}, "MATCH": {"a": "(s<PERSON><PERSON>_war<PERSON>; przes<PERSON>wana_tab; [typ_porównania])", "d": "Zwraca względną pozycję elementu w tablicy, odpowiadającą określonej wartości przy podanej kolejności"}, "OFFSET": {"a": "(od<PERSON><PERSON><PERSON>; wiersze; kolumny; [wys<PERSON><PERSON><PERSON>]; [szer<PERSON><PERSON><PERSON>])", "d": "Zwraca odwołanie do zakresu, który jest daną liczbą wierszy lub kolumn z danego odwołania"}, "ROW": {"a": "([odwo<PERSON>nie])", "d": "Zwraca numer wiersza odpowiadający podanemu odwołaniu"}, "ROWS": {"a": "(tablica)", "d": "Zwraca liczbę wierszy odpowiadających podanemu odwołaniu lub tablicy"}, "TRANSPOSE": {"a": "(tablica)", "d": "Konwertuje pionowy zakres komórek do zakresu poziomego lub na odwrót"}, "UNIQUE": {"a": "(Tablica; [by_col]; [exactly_once])", "d": " Zwraca wartości unikatowe z zakresu lub tablicy."}, "VLOOKUP": {"a": "(s<PERSON><PERSON>_war<PERSON>; tabela_tablica; nr_indeksu_kolumny; [przes<PERSON><PERSON><PERSON>_zak<PERSON>])", "d": "Wyszu<PERSON><PERSON> wartość w pierwszej od lewej kolumnie tabeli i zwraca wartość z tego samego wiersza w kolumnie określonej przez użytkownika. Domyślnie tabela musi być sortowana w kolejności rosnącej"}, "XLOOKUP": {"a": "(szuka<PERSON>_war<PERSON><PERSON><PERSON>; szukana_tablica; zwracana_tablica; [je<PERSON><PERSON>_nie_znaleziono]; [tryb_dopasowywania]; [tryb_wyszukiwania])", "d": "Przeszukuje zakres lub tablicę pod kątem dopasowania i zwraca odpowiedni element z drugiego zakresu lub drugiej tablicy. Domyślnie jest używane dokładne dopasowanie"}, "CELL": {"a": "(typ_info; [odwołanie])", "d": "Zwraca informacje o formatowaniu, położeniu lub zawartości komórki"}, "ERROR.TYPE": {"a": "(bł<PERSON><PERSON>_war<PERSON><PERSON>)", "d": "Zwraca numer odpowiadający jednej z wartości błędu."}, "ISBLANK": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy odwołanie następuje do pustej komórki i zwraca wartość PRAWDA albo FAŁSZ"}, "ISERR": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> to błąd inny niż #N/D, i zwraca wartość PRAWDA albo FAŁSZ"}, "ISERROR": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON>wdza, c<PERSON> warto<PERSON> to błąd, i zwraca wartość PRAWDA albo FAŁSZ"}, "ISEVEN": {"a": "(liczba)", "d": "Zw<PERSON><PERSON> PRAWDA, je<PERSON><PERSON> liczba jest parzysta"}, "ISFORMULA": {"a": "(odwołanie)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy odwołanie jest odwołaniem do komórki zawierającej formułę, i zwraca wartość PRAWDA albo FAŁSZ."}, "ISLOGICAL": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> jest wartością logiczną (PRAWDA albo FAŁSZ) i zwraca wartość PRAWDA albo FAŁSZ"}, "ISNA": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> warto<PERSON> to #N/D i zwraca wartość PRAWDA albo FAŁSZ"}, "ISNONTEXT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> nie jest tekstem (puste komórki nie są tekstem) i zwraca wartość PRAWDA albo FAŁSZ"}, "ISNUMBER": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> to liczba i zwraca wartość PRAWDA albo FAŁSZ"}, "ISODD": {"a": "(liczba)", "d": "Zw<PERSON><PERSON> PRAWDA, je<PERSON><PERSON> liczba jest nieparzysta"}, "ISREF": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> jest odwołaniem i zwraca wartość PRAWDA albo FAŁSZ"}, "ISTEXT": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warto<PERSON> to tekst i zwraca wartość PRAWDA albo FAŁSZ"}, "N": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Konwertuje wartości nieliczbowe na liczby, daty na liczby kolejne, wartość PRAWDA na 1, wszystko inne na 0 (zero)"}, "NA": {"a": "()", "d": "Zwraca wartość błędu #N/D (wartość niedostępna)"}, "SHEET": {"a": "([war<PERSON><PERSON><PERSON>])", "d": "Zwraca numer arkusza, którego dotyczy odwołanie."}, "SHEETS": {"a": "([odwo<PERSON>nie])", "d": "Zwraca liczbę arkuszy w odwołaniu."}, "TYPE": {"a": "(<PERSON><PERSON><PERSON><PERSON>)", "d": "Zwraca liczbę całkowitą reprezentującą typ danych wartości: liczba = 1; tekst = 2; warto<PERSON><PERSON> logicz<PERSON> = 4; warto<PERSON><PERSON> błędu = 16; tablica = 64; dane <PERSON>ł<PERSON>ż<PERSON> = 128"}, "AND": {"a": "(logiczna1; [logiczna2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy wszystkie argumenty mają wartość PRAWDA, i zwraca wartość PRAWDA, jeśli wszystkie argumenty mają wartość PRAWDA"}, "FALSE": {"a": "()", "d": "Zwraca wartość logiczną FAŁSZ"}, "IF": {"a": "(test_logiczny; [warto<PERSON><PERSON>_je<PERSON><PERSON>_prawda]; [warto<PERSON><PERSON>_je<PERSON><PERSON>_fałsz])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c<PERSON> warunek jest speł<PERSON>, i zwraca jedn<PERSON> war<PERSON>, jeśli PRAWDA, a <PERSON><PERSON> warto<PERSON>, jeśli FAŁSZ"}, "IFS": {"a": "(test_logiczny; war<PERSON><PERSON><PERSON>_jeś<PERSON>_prawda; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy jest spełniony co najmniej jeden warunek, i zwraca wartość odpowiadającą pierwszemu spełnionemu warunkowi"}, "IFERROR": {"a": "(warto<PERSON><PERSON>; warto<PERSON><PERSON>_je<PERSON><PERSON>_błąd)", "d": "<PERSON><PERSON><PERSON><PERSON> warto<PERSON> warto<PERSON>_je<PERSON><PERSON>_b<PERSON><PERSON><PERSON>, je<PERSON><PERSON> wyrażenie jest błędne, l<PERSON> warto<PERSON><PERSON> wyrażenia w przeciwnym razie"}, "IFNA": {"a": "(warto<PERSON><PERSON>; warto<PERSON><PERSON>_je<PERSON>eli_nd)", "d": "Zwraca okreś<PERSON>, je<PERSON><PERSON> rozpoznawanie wyrażenia zakończy się błędem #N/D. W przeciwnym razie zostanie zwrócony wynik wyrażenia."}, "NOT": {"a": "(logicz<PERSON>)", "d": "Zmienia wartość FAŁSZ na PRAWDA albo wartość PRAWDA na FAŁSZ"}, "OR": {"a": "(logiczna1; [logiczna2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy którykolwiek z argumentów ma wartość PRAWDA i zwraca wartość PRAWDA albo FAŁSZ. Zwraca wartość FAŁSZ tylko wówczas, gdy wszystkie argumenty mają wartość FAŁSZ"}, "SWITCH": {"a": "(wyra<PERSON><PERSON><PERSON>; wartość1; wynik1; [domy<PERSON>lne_lub_wartość2]; [wynik2]; ...)", "d": "Ocenia wyrażenie względem listy wartości i zwraca wynik odpowiadający pierwszej pasującej wartości. W razie braku dopasowania zwracana jest opcjonalna wartość domyślna"}, "TRUE": {"a": "()", "d": "Zwraca wartość logiczną PRAWDA"}, "XOR": {"a": "(logiczna1; [logiczna2]; ...)", "d": "Zwraca wartość logiczną XOR (wyłączne LUB) wszystkich argumentów."}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Zwraca tekst, który znajduje się przed znakami ograniczającymi."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Zwraca tekst, który znajduje się po znakach ograniczających."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Dzieli tekst na wiersze lub kolumny przy użyciu ograniczników."}, "WRAPROWS": {"a": "(wekt<PERSON>, wrap_count, [pad_with])", "d": " Zawija wektor wiersza lub kolumny po określonej liczbie wartości. Wektor lub odwołanie do zawijania."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": " Układa tablice w pionie tworząc jedną tablicę."}, "HSTACK": {"a": "(tablica1, [tablica2], ...)", "d": " Układa tablice w poziomie w jedną tablicę."}, "CHOOSEROWS": {"a": "(tablica, row_num1, [row_num2], ...)", "d": " Zwraca wiersze z tablicy lub odwołania."}, "CHOOSECOLS": {"a": "(tablica, col_num1, [col_num2], ...)", "d": " Zwraca kolumny z tablicy lub odwołania."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": " Zwraca tablicę jako jedną kolumnę."}, "TOROW": {"a": "(tablica, [ignoruj], [skanuj_według_kolumn])", "d": "Zwraca tablicę jako jeden w<PERSON>z."}, "WRAPCOLS": {"a": "(wekt<PERSON>, wrap_count, [pad_with])", "d": " Zawija wektor wiersza lub kolumny po określonej liczbie wartości."}, "TAKE": {"a": "(array, rows, [columns])", "d": " Zwraca wiersze lub kolumny z początku lub końca tablicy."}, "DROP": {"a": "(array, rows, [columns])", "d": " Porzuca wiersze lub kolumny z początku lub końca tablicy."}}