﻿<!DOCTYPE html>
<html>
	<head>
		<title>Communicating in real time</title>
		<meta charset="utf-8" />
		<meta name="description" content="Tips on using the Chat tool" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type="text/css" rel="stylesheet" href="../../images/sprite.css" />
		<script type="text/javascript" src="../callback.js"></script>
		<script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Communicating in real time</h1>
			<p>The <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> allows you to maintain constant team-wide approach to work flow: <a href="https://helpcenter.onlyoffice.com/userguides/groups-guides-documents-share-documents.aspx" onclick="onhyperlinkclick(this)">share</a> files and folders, <a href="../HelpfulHints/CollaborativeEditing.htm" onclick="onhyperlinkclick(this)">collaborate</a> on presentations in real time, <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">comment</a> certain parts of your presentations that require additional third-party input, save <a href="../HelpfulHints/VersionHistory.htm" onclick="onhyperlinkclick(this)">presentation versions</a> for future use.</p>
			<p>In <b>Presentation Editor</b> you can communicate with your co-editors in real time using the built-in <b>Chat</b> tool as well as a number of useful plugins, i.e. <a href="../UsageInstructions/CommunicationPlugins.htm" onclick="onhyperlinkclick(this)">Telegram or Rainbow</a>.</p>
			<p>To access the <b>Chat</b> tool and leave a message for other users,</p>
			<ol>
				<li>click the <div class="icon icon-chaticon"></div> icon at the left sidebar,</li>
				<li>enter your text into the corresponding field below,</li>
				<li>press the <b>Send</b> button.</li>
			</ol>
			<p class="note">The chat messages are stored during one session only. To discuss the presentation content, it is better to use <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">comments</a> which are stored until they are deleted.</p>
			<p>All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - <span class="icon icon-chaticon_new"></span>.</p>
			<p>To close the panel with chat messages, click the <span class="icon icon-chaticon"></span> icon once again.</p>
		</div>
	</body>
</html>