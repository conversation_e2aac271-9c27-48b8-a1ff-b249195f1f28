﻿<!DOCTYPE html>
<html>
	<head>
		<title>UNTERGRENZE.MATHEMATIK-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>UNTERGRENZE.MATHEMATIK-Funktion</h1>
			<p>Die Funktion <b>UNTERGRENZE.MATHEMATIK </b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie rundet eine Zahl auf die nächste ganze Zahl oder auf das nächste Vielfache von Schritt ab.</p>
			<p>Die Formelsyntax der Funktion <b>UNTERGRENZE.MATHEMATIK</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>UNTERGRENZE.MATHEMATIK(Zahl;Schritt;Modus)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist die Zahl, die abgerundet werden soll.</p>
			<p style="text-indent: 50px;"><b><em>Schritt</em></b> ist das Vielfache auf das Sie runden möchten. Dieses Argument ist optional. Wird kein Wert angegeben, verwendet die Funktion den Standardwert 1.</p>
            <p style="text-indent: 50px;"><b><em>Modus</em></b> legt für eine negative Zahl fest, ob „Zahl“ n Richtung des größeren oder des kleineren Werts gerundet wird. Dieses Argument ist optional. Wird kein Wert angegeben oder wird der Wert mit 0 angegeben, werden negative Zahlen gegen Null gerundet. Wenn ein anderer numerischer Wert angegeben wird, werden negative Zahlen gegenüber Null aufgerundet.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>UNTERGRENZE.MATHEMATIK</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>UNTERGRENZE.MATHEMATIK</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="UNTERGRENZE.MATHEMATIK-Funktion" src="../images/floormath.png" /></p>
		</div>
	</body>
</html>