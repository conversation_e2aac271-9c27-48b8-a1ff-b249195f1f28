﻿<!DOCTYPE html>
<html>
	<head>
		<title>KLEIN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>KLEIN-Funktion</h1>
			<p>Die Funktion <b>KLEIN</b> gehört zur Gruppe der Text- und Datenfunktionen. Mit dieser Funktion lässt sich der Text in einer ausgewählten Zelle in Kleinbuchstaben umwandeln.</p>
			<p>Die Syntax der Funktion <b>KLEIN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>KLEIN(Text)</em></b></p> 
			<p>Dabei ist <b><em>Text</em></b> der Text auf den Sie verweisen, der in Kleinbuchstaben umgewandelt werden soll.</p>
			<p><b>KLEIN</b>-Funktion anwenden:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>KLEIN</b>.</li>
			<li>Geben Sie das gewünschte Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="KLEIN-Funktion" src="../images/lower.png" /></p>
		</div>
	</body>
</html>