﻿<!DOCTYPE html>
<html>
	<head>
		<title>Manipuler des objets</title>
		<meta charset="utf-8" />
		<meta name="description" content="Déplacer, faire pivoter, redimentionner et remodeler des formes automatiques, des images, des graphiques." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Manipuler des objets</h1>
			<p>Dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Présentations</b></a>, vous pouvez redimensionner, déplacer, faire pivoter différents objets manuellement sur une diapositive à l'aide des poignées spéciales. Vous pouvez également spécifier les dimensions et la position de certains objets à l'aide de la barre latérale droite ou de la fenêtre <b>Paramètres avancés</b>.</p>
			<p class="note">
				<b>Remarque</b> : la liste des raccourcis clavier qui peuvent être utilisés lorsque vous travaillez avec des objets est disponible <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">ici</a>.
			</p>
			<h3>Redimensionner des objets</h3>
			<p>Pour changer la taille d'une <b>forme automatique/image/graphique/zone de texte</b>, faites glisser les petits carreaux <span class="icon icon-resize_square"></span> situés sur les bords de l'objet. Pour garder les proportions de l'objet sélectionné lors du redimensionnement, maintenez la touche <b>Shift</b> enfoncée et faites glisser l'une des icônes de coin.</p>
			<p><span class="big big-maintain_proportions"></span></p>
			<p>Pour spécifier la largeur et la hauteur précise d'un <b>graphique</b>, sélectionnez-le avec la souris et utilisez la section <b>Taille</b> de la barre latérale droite activée.</p>
			<p>Pour spécifier les dimensions précises d'une <b>image</b> ou d'une <b>forme automatique</b>, cliquez avec le bouton droit de la souris sur l'objet nécessaire et sélectionnez l'option <b>Paramètres avancés de l'image/forme automatique</b> du menu contextuel. Réglez les valeurs nécessaires dans l'onglet <b>Taille</b> de la fenêtre <b>Paramètres avancés</b> et cliquez sur le bouton <b>OK</b>.</p>
			<h3>Modifier des formes automatiques</h3>
			<p>Lors de la modification des formes, par exemple des Flèches figurées ou les Légendes, l'icône jaune en forme de diamant <span class="icon icon-yellowdiamond"></span> est aussi disponible. Elle vous permet d'ajuster certains aspects de la forme, par exemple, la longueur de la pointe d'une flèche.</p>
			<p><span class="big big-reshaping"></span></p>
			<p>Pour modifier une forme, vous pouvez également utiliser l'option <b>Modifier les points</b> dans le <b>menu contextuel</b>.</p>
			<p><b>Modifier les points</b> sert à personnaliser ou modifier le contour d'une forme.</p>
			<ol>
				<li>
					Pour activer les points d'ancrage modifiables, faites un clic droit sur la forme et sélectionnez Modifier les points dans le menu. Les carrés noirs qui apparaissent sont les points de rencontre entre deux lignes et la ligne rouge trace le contour de la forme. Cliquez sur l'un de ces points et faites-le glisser pour repositionner et modifier le contour de la forme.
					<p><img alt="Modifier les points" src="../images/editpoints_rightclick.png" /></p>
				</li>
				<li>
					Lorsque vous cliquez sur le point d'ancrage, deux lignes bleus avec des carrés blanches apparaissent. Ce sont les points de contrôle Bézier permettant de créer une courbe et de modifier la finesse de la courbe.
					<p><span class="big big-editpoints_example"></span></p>
				</li>
				<li>
					Autant que les points d'ancrage sont actifs, vous pouvez les modifier et supprimer :
					<ul>
						<li>Pour ajouter un point de contrôle à une forme, maintenez la touche <b>Ctrl</b> enfoncée et cliquez sur l'emplacement du point de contrôle souhaité.</li>
						<li>Pour supprimer un point, maintenez la touche <b>Ctrl</b> enfoncée et cliquez sur le point superflu.</li>
					</ul>
				</li>
			</ol>
			<h3>Déplacer des objets</h3>
			<p>
                Pour modifier la position d'une <b>forme automatique/image/tableau/graphique/bloc de texte</b>, utilisez l'icône <span class="icon icon-arrow"></span> qui apparaît si vous placez le curseur de votre souris sur l'objet. Faites glisser l'objet vers la position nécessaire sans relâcher le bouton de la souris.
                Pour déplacer l'objet par incrément équivaut à un pixel, maintenez la touche <b>Ctrl</b> enfoncée et utilisez les flèches du clavier.
                Pour déplacer l'objet strictement à l'horizontale/verticale et l'empêcher de se déplacer dans une direction perpendiculaire, maintenez la touche <b>Shift</b> enfoncée lors du glissement.
            </p>
			<p>Pour spécifier les dimensions précises d'une <b>image</b>, cliquez avec le bouton droit de la souris sur l'objet nécessaire et sélectionnez l'option <b>Paramètres avancés de l'image</b> du menu contextuel. Réglez les valeurs nécessaires dans l'onglet <b>Taille</b> de la fenêtre <b>Paramètres avancés</b> et cliquez sur le bouton <b>OK</b>.</p>
			<h3>Faire pivoter des objets</h3>
			<p>Pour faire pivoter manuellement une <b>forme automatique/image/bloc de texte</b>, placez le curseur de la souris sur la poignée de rotation <span class="icon icon-greencircle"></span> et faites-la glisser vers la droite ou vers la gauche. Pour limiter la rotation de l'angle à des incréments de 15 degrés, maintenez la touche Shift enfoncée. Pour faire pivoter par incréments de 15 degrés, maintenez enfoncée la touche <b>Shift</b> tout en faisant pivoter.</p>
			<p>Pour faire pivoter l'objet de 90 degrés dans le sens inverse des aiguilles d'une montre/dans le sens des aiguilles d'une montre ou le retourner horizontalement/verticalement, vous pouvez utiliser la section <b>Rotation</b> de la barre latérale droite qui sera activée lorsque vous aurez sélectionné l'objet nécessaire. Pour l'ouvrir, cliquez sur l'icône <b>Paramètres de la forme</b> <span class="icon icon-shape_settings_icon"></span> ou <b>Paramètres de l'image</b> <span class="icon icon-image_settings_icon"></span> à droite. Cliquez sur l'un des boutons :</p>
			<ul>
				<li><div class = "icon icon-rotatecounterclockwise"></div> pour faire pivoter l'objet de 90 degrés dans le sens inverse des aiguilles d'une montre</li>
				<li><div class = "icon icon-rotateclockwise"></div> pour faire pivoter l'objet de 90 degrés dans le sens des aiguilles d'une montre</li>
				<li><div class = "icon icon-fliplefttoright"></div> pour retourner l'objet horizontalement (de gauche à droite)</li>
				<li><div class = "icon icon-flipupsidedown"></div> pour retourner l'objet verticalement (à l'envers)</li>
			</ul>
			<p>Il est également possible de cliquer avec le bouton droit de la souris sur l'objet, de choisir l'option <b>Faire pivoter</b> dans le menu contextuel, puis d'utiliser une des options de rotation disponibles.</p>
			<p>Pour faire pivoter l'objet selon un angle exactement spécifié, cliquez sur le lien <b>Afficher les paramètres avancés</b> dans la barre latérale droite et utilisez l'onglet <b>Rotation</b> de la fenêtre <b>Paramètres avancés</b>. Spécifiez la valeur nécessaire mesurée en degrés dans le champ <b>Angle </b>et cliquez sur <b>OK</b>.</p>

		</div>
	</body>
</html>