﻿<!DOCTYPE html>
<html>
	<head>
		<title>QUOTIENT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>QUOTIENT-Funktion</h1>
			<p>Die Funktion <b>QUOTIENT</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um den ganzzahligen Anteil einer Division zurückzugeben.</p>
			<p>Formelsyntax der Funktion <b>QUOTIENT</b>:</p> 
			<p style="text-indent: 150px;"><b><em>QUOTIENT(Zähler;Nenner)</em></b></p> 
			<p><b><em>Zähler</em></b> und <b><em>Nenner</em></b> sind nummerische Werte, die manuell eingegeben werden oder in die Zelle eingeschlossen sind, auf die Sie Bezug nehmen.</p>
			<p>Anwenden der Funktion <b>QUOTIENT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>QUOTIENT</b>.</li>
			<li>Geben Sie die gewünschten Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="QUOTIENT-Funktion" src="../images/quotient.png" /></p>
		</div>
	</body>
</html>