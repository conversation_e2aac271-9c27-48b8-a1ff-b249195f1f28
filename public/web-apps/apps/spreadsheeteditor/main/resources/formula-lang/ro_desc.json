{"DATE": {"a": "(an; lună; zi)", "d": "Returnează numărul care reprezintă data în cod dată-oră."}, "DATEDIF": {"a": "(dată_început; dată_sfârșit; unitate)", "d": "Calculează numărul de zile, luni sau ani dintre două date calendaristice"}, "DATEVALUE": {"a": "(text_dată)", "d": "Transformă o dată sub formă de text într-un număr care reprezintă data în cod dată-oră"}, "DAY": {"a": "(număr_serie)", "d": "Returnează ziua din lună, un număr de la 1 la 31."}, "DAYS": {"a": "(dată_sfârșit; dată_început)", "d": "Returnează numărul de zile dintre cele două date."}, "DAYS360": {"a": "(dată_start; dată_sfârșit; [metodă])", "d": "Returnează numărul de zile dintre două date bazat pe un an de 360 de zile (12 luni de 30 de zile)"}, "EDATE": {"a": "(dată_start; luni)", "d": "Returnează numărul serial al datei care este numărul indicat de luni înainte sau după data de început"}, "EOMONTH": {"a": "(dată_start; luni)", "d": "Returnează numărul serial al ultimei zile din ultima lună înainte sau după un anumit număr de luni specificate"}, "HOUR": {"a": "(număr_serie)", "d": "Returnează ora ca număr de la 0 (12:00 A.M.) la 23 (11:00 P.M.)."}, "ISOWEEKNUM": {"a": "(dată)", "d": "Returnează numărul de săptămână ISO din an pentru o anumită dată"}, "MINUTE": {"a": "(număr_serie)", "d": "Returnează minutul ca număr de la 0 la 59."}, "MONTH": {"a": "(număr_serie)", "d": "<PERSON><PERSON><PERSON><PERSON> luna, un număr de la 1 (ianuarie) la 12 (dece<PERSON><PERSON>)."}, "NETWORKDAYS": {"a": "(dată_start; dată_sfârșit; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul total de zile lucrătoare între două date"}, "NETWORKDAYS.INTL": {"a": "(dată_start; dată_sfârșit; [weekend]; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul total de zile lucrătoare între două date cu parametri sfârșit de săptămână particularizați"}, "NOW": {"a": "()", "d": "Returnează data și ora curente formatate ca dată și oră."}, "SECOND": {"a": "(număr_serie)", "d": "Returnează secunda ca număr în intervalul de la 0 la 59."}, "TIME": {"a": "(oră; minut; secundă)", "d": "Transformă ore, minut și secunde date ca numere în numere seriale, formatate cu un format de oră"}, "TIMEVALUE": {"a": "(text_oră)", "d": "Transformă o oră din format text într-un număr serial pentru oră, un număr de la 0 (12:00:00 AM) la 0,999988426 (11:59:59 PM). Formatați numărul cu format de oră după introducerea formulei"}, "TODAY": {"a": "()", "d": "Returnează data curentă formatată ca dată."}, "WEEKDAY": {"a": "(număr_serie; [tip_returnare])", "d": "Returnează un număr de la 1 la 7 care identifică ziua din săptămână a unei date calendaristice."}, "WEEKNUM": {"a": "(număr_serie; [tip_returnare])", "d": "Returnează numărul de săptămâni dintr-un an"}, "WORKDAY": {"a": "(dată_start; zile; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul serial al datei înainte sau după un număr specificat de zile lucrătoare"}, "WORKDAY.INTL": {"a": "(dată_start; zile; [weekend]; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul serial al datei înainte sau după un număr specificat de zile lucrătoare cu parametri sfârșit de săptămână particularizați"}, "YEAR": {"a": "(număr_serie)", "d": "<PERSON><PERSON><PERSON><PERSON> anul, un întreg în intervalul 1900 - 9999."}, "YEARFRAC": {"a": "(dată_start; dată_sfârșit; [bază])", "d": "Returnează fracțiunea de an care reprezintă numărul de zile întregi între data_de început și data_de sfârșit"}, "BESSELI": {"a": "(x; n)", "d": "Returnează funcția Bessel modificată In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Returnează funcția Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Returnează funcția Bessel modificată Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Returnează funcția Bessel  Yn(x)"}, "BIN2DEC": {"a": "(număr)", "d": "Se efectuează conversia unui număr binar într-un număr zec<PERSON>l"}, "BIN2HEX": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr binar într-un număr hexazecimal"}, "BIN2OCT": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr binar într-un octal"}, "BITAND": {"a": "(număr1; număr2)", "d": "Returnează un „And” de nivel de bit din două numere"}, "BITLSHIFT": {"a": "(număr; volum_deplasare)", "d": "Returnează un număr deplasat la stânga la volum_deplasare biți"}, "BITOR": {"a": "(număr1; număr2)", "d": "Returnează un „Or” de nivel de bit din două numere"}, "BITRSHIFT": {"a": "(număr; volum_deplasare)", "d": "Returnează un număr deplasat la dreapta la volum_deplasare biți"}, "BITXOR": {"a": "(număr1; număr2)", "d": "Returnează un „Sau exclusiv” la nivel de bit din două numere"}, "COMPLEX": {"a": "(num_real; num_i; [sufix])", "d": "Se efectuează conversia coeficienților reali și imaginari într-un număr complex"}, "CONVERT": {"a": "(număr; din_unit; la_unit)", "d": "Se efectuează conversia unui număr de la un sistem de măsurare la altul"}, "DEC2BIN": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr zecimal într-un număr binar"}, "DEC2HEX": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr zecimal într-un număr hexazecimal"}, "DEC2OCT": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr zecimal într-un octal"}, "DELTA": {"a": "(număr1; [număr2])", "d": "Se testează dacă două numere sunt egale"}, "ERF": {"a": "(limită_inf; [limită_sup])", "d": "Returnează funcția de eroare"}, "ERF.PRECISE": {"a": "(X)", "d": "Returnează funcția de eroare"}, "ERFC": {"a": "(x)", "d": "Returnează funcția de eroare complementară"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returnează funcția complementară de eroare"}, "GESTEP": {"a": "(număr; [prag])", "d": "Se testează dacă numărul este mai mare decât valoarea de prag"}, "HEX2BIN": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr hexazecimal într-un număr binar"}, "HEX2DEC": {"a": "(număr)", "d": "Se efectuează conversia unui număr hexazecimal într-un număr zecimal"}, "HEX2OCT": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr hexazecimal într-un octal"}, "IMABS": {"a": "(număr_i)", "d": "Returnează valoarea absolută (modulul) a unui număr complex"}, "IMAGINARY": {"a": "(număr_i)", "d": "Returnează coeficientul imaginar al unui număr complex"}, "IMARGUMENT": {"a": "(număr_i)", "d": "Returnează argumentul q, un unghi exprimat în radiani"}, "IMCONJUGATE": {"a": "(număr_i)", "d": "Returnează conjugata complexă a unui număr complex"}, "IMCOS": {"a": "(număr_i)", "d": "Returnează cosinusul unui număr complex"}, "IMCOSH": {"a": "(număr)", "d": "Returnează cosinusul hiperbolic al unui număr complex"}, "IMCOT": {"a": "(număr)", "d": "Returnează cotangenta unui număr complex"}, "IMCSC": {"a": "(număr)", "d": "Returnează cosecanta unui număr complex"}, "IMCSCH": {"a": "(număr)", "d": "Returnează cosecanta hiperbolică a unui număr complex"}, "IMDIV": {"a": "(număr_i1; număr_i2)", "d": "Returnează câtul a două numere complexe"}, "IMEXP": {"a": "(număr_i)", "d": "Returnează exponentul unui număr complex"}, "IMLN": {"a": "(număr_i)", "d": "Returnează logaritmul natural al unui număr complex"}, "IMLOG10": {"a": "(număr_i)", "d": "Returnează un logaritm în baza 10 al unui număr complex"}, "IMLOG2": {"a": "(număr_i)", "d": "Returnează un logaritm în baza 2 al unui număr complex"}, "IMPOWER": {"a": "(număr_i; număr)", "d": "Returnează un număr complex ridicat la o putere întreagă"}, "IMPRODUCT": {"a": "(număr_i1; [număr_i2]; ...)", "d": "Returnează produsul numerelor complexe de la 1 la 255"}, "IMREAL": {"a": "(număr_i)", "d": "Returnează coeficientul real al unui număr complex"}, "IMSEC": {"a": "(număr)", "d": "Returnează secanta unui număr complex"}, "IMSECH": {"a": "(număr)", "d": "Returnează secanta hiperbolică a unui număr complex"}, "IMSIN": {"a": "(număr_i)", "d": "Returnează sinusul unui număr complex"}, "IMSINH": {"a": "(număr)", "d": "Returnează sinusul hiperbolic al unui număr complex"}, "IMSQRT": {"a": "(număr_i)", "d": "Returnează rădăcina pătrată a numărului complex"}, "IMSUB": {"a": "(număr_i1; număr_i2)", "d": "Returnează diferența dintre două numere complexe"}, "IMSUM": {"a": "(număr_i1; [număr_i2]; ...)", "d": "Returnează suma numerelor complexe"}, "IMTAN": {"a": "(număr)", "d": "Returnează tangenta unui număr complex"}, "OCT2BIN": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr octal într-un număr binar"}, "OCT2DEC": {"a": "(număr)", "d": "Se efectuează conversia unui număr octal într-un număr zecimal"}, "OCT2HEX": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr octal într-un număr hexazecimal"}, "DAVERAGE": {"a": "(bază_de_date; câmp; criterii)", "d": "Face media valorilor dintr-o coloană dintr-o listă sau bază de date care corespund condițiilor precizate"}, "DCOUNT": {"a": "(bază_de_date; câmp; criterii)", "d": "Numără celulele câmpului (coloanei) care conțin numere. Numărarea se face pentru înregistrările bazei de date care corespund condițiilor specificate"}, "DCOUNTA": {"a": "(bază_de_date; câmp; criterii)", "d": "Numără celulele completate în câmp (coloană) pentru înregistrările din baza de date care corespund condițiilor specificate"}, "DGET": {"a": "(bază_de_date; câmp; criterii)", "d": "Extrage dintr-o bază de date o singură înregistrare care se potrivește condițiilor specificate"}, "DMAX": {"a": "(bază_de_date; câmp; criterii)", "d": "Returnează cel mai mare număr din câmpul (coloana) de înregistrări din baza de date care corespund condițiilor precizate"}, "DMIN": {"a": "(bază_de_date; câmp; criterii)", "d": "Returnează în câmp (coloană) cel mai mic număr din înregistrările din baza de date care corespund condițiilor specificate"}, "DPRODUCT": {"a": "(bază_de_date; câmp; criterii)", "d": "Înmulțește valorile câmpului (coloanei) pentru înregistrările bazei de date care corespund condițiilor specificate"}, "DSTDEV": {"a": "(bază_de_date; câmp; criterii)", "d": "Estimează deviația standard bazată pe un eșantion din intrările selectate din baza de date"}, "DSTDEVP": {"a": "(bază_de_date; câmp; criterii)", "d": "Calculează deviația standard bazată pe întreaga populație a intrărilor selectate din baza de date"}, "DSUM": {"a": "(bază_de_date; câmp; criterii)", "d": "Adună în câmp (coloană) numerele din înregistrările din baza de date care corespund condițiilor specificate"}, "DVAR": {"a": "(bază_de_date; câmp; criterii)", "d": "Estimează varianța bazată pe un eșantion din intrările selectate ale bazei de date"}, "DVARP": {"a": "(bază_de_date; câmp; criterii)", "d": "Calculează varianța bazată pe întreaga populație a intrărilor selectate din baza de date"}, "CHAR": {"a": "(număr)", "d": "Returnează caracterul specificat de numărul codului din setul de caractere al computerului"}, "CLEAN": {"a": "(text)", "d": "Elimină toate caracterele neimprimabile din text"}, "CODE": {"a": "(text)", "d": "Returnează un cod numeric pentru primul caracter dintr-un șir text, în setul de caractere utilizat de computer"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Unește mai multe șiruri text într-un singur șir text"}, "CONCAT": {"a": "(text1; ...)", "d": "Concatenează o listă sau o zonă de șiruri de text"}, "DOLLAR": {"a": "(număr; [zec<PERSON><PERSON>])", "d": "Transformă un număr în text, utilizând formatul monedă"}, "EXACT": {"a": "(text1; text2)", "d": "Verifică dacă două șiruri text sunt identice și returnează TRUE sau FALSE. EXACT diferențiază literele mari de literele mici"}, "FIND": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Returnează numărul poziției de început a unui șir text găsit în cadrul altui șir text. FIND diferențiază literele mari de literele mici"}, "FINDB": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Găsesc un șir text într-un al doilea șir text, apoi returnează numărul poziției de început a primului șir text începând cu primul caracter al celui de-al doilea șir text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană"}, "FIXED": {"a": "(număr; [zecimale]; [nr_virgule])", "d": "Rotunjește un număr la numărul specificat de  zecimale și returnează rezultatul ca text cu sau fără virgule"}, "LEFT": {"a": "(text; [car_num])", "d": "Returnează numărul precizat de caractere de la începutul unui șir text"}, "LEFTB": {"a": "(text; [car_num])", "d": "Returnează primul caracter sau primele caractere dintr-un șir text, în funcție de numărul de byți specificat, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană"}, "LEN": {"a": "(text)", "d": "Returnează numărul de caractere într-un șir text"}, "LENB": {"a": "(text)", "d": "Returnează numărul de byți utilizați pentru reprezentarea caracterelor dintr-un șir text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană"}, "LOWER": {"a": "(text)", "d": "Transformă toate literele dintr-un șir text în litere mici"}, "MID": {"a": "(text; num_start; car_num)", "d": "Returnează caracterele din mijlocul unui șir text fiind date poziția de început și lungimea"}, "MIDB": {"a": "(text; num_start; car_num)", "d": "Returnează un anumit număr de caractere dintr-un șir de text, începând din poziția specificată, pe baza numărului de byți specificat, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană"}, "NUMBERVALUE": {"a": "(text; [separator_zecimale]; [separator_grup])", "d": "Efectuează conversia textului în număr într-o manieră independentă de setările regionale"}, "PROPER": {"a": "(text)", "d": "Transformă un șir text astfel: scrie cu majusculă prima literă din fiecare cuvânt și transformă toate celelalte litere în litere mici"}, "REPLACE": {"a": "(text_vechi; num_start; car_num; text_nou)", "d": "Înlocuiește o parte a unui șir text cu un șir text diferit"}, "REPLACEB": {"a": "(text_vechi; num_start; car_num; text_nou)", "d": "Înlocuiește o parte dintr-un șir de text, pe baza numărului de byți specificat, cu un alt șir de text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană"}, "REPT": {"a": "(text; număr_ori)", "d": "Repetă un text de un număr de ori dat. Utilizați REPT pentru a umple o celulă cu un număr de instanțe ale unui șir text"}, "RIGHT": {"a": "(text; [car_num])", "d": "Returnează numărul precizat de caractere de la sfârșitul unui șir text"}, "RIGHTB": {"a": "(text; [car_num])", "d": "Întoarce ultimul caracter sau caractere dintr-un șir de text, pe baza unui număr de byți specificat, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană"}, "SEARCH": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Returnează numărul caracterului de la care este găsit prima dată un caracter sau un șir text precizat, citind de la stânga la dreapta (nu se diferențiază literele mari și mici)"}, "SEARCHB": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Găsesc un șir text într-un al doilea șir text, apoi returnează numărul poziției de început a primului șir text începând cu primul caracter al celui de-al doilea șir text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană"}, "SUBSTITUTE": {"a": "(text; text_vechi; text_nou; [num_instanță])", "d": "Înlocuiește textul existent cu text nou într-un șir text"}, "T": {"a": "(valoare)", "d": "Verifică dacă o valoare este text și, în caz că este, returnează text, iar în caz contrar, returneaz<PERSON> ghil<PERSON><PERSON> dub<PERSON> (text gol)"}, "TEXT": {"a": "(valoare; format_text)", "d": "Transformă o valoare în text cu un format de număr precizat"}, "TEXTJOIN": {"a": "(delimitator; ignorare_gol; text1; ...)", "d": "Concatenează o listă sau o zonă de șiruri de text folosind un delimitator"}, "TRIM": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> toate spațiile dintr-un șir text except<PERSON>d spațiile simple dintre cuvinte"}, "UNICHAR": {"a": "(număr)", "d": "Returnează caracterul Unicode menționat de o valoare numerică dată"}, "UNICODE": {"a": "(text)", "d": "Returnează numărul (punctul de cod) corespunzător primului caracter al textului"}, "UPPER": {"a": "(text)", "d": "Transformă un șir text în majuscule"}, "VALUE": {"a": "(text)", "d": "Transformă un șir text care reprezintă un număr într-un număr"}, "AVEDEV": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media deviațiilor absolute ale punctelor de date față de media lor. Argumentele pot fi numere sau nume, matrice sau referințe care conțin numere"}, "AVERAGE": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media (aritmetică) a argumentelor sale, care pot fi numere sau nume, matrice sau referințe care conțin numere"}, "AVERAGEA": {"a": "(valoare1; [valoare2]; ...)", "d": "Returnează media (aritmetică) a argumentelor sale, evaluând textul și valorile FALSE din argumente ca 0; TRUE se evaluează ca 1. Argumentele pot fi numere, nume, matrice sau referințe"}, "AVERAGEIF": {"a": "(zon<PERSON>; criterii; [zonă_medie])", "d": "Găsește media(aritmetică) pentru celulele specificate printr-o condiție sau criteriu date"}, "AVERAGEIFS": {"a": "(zon<PERSON>_medie; zonă_criterii; criterii; ...)", "d": "Găsește media(aritmetică) pentru celulele specificate printr-un set de condiții sau criterii"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Returnează funcția densitate de probabilitate beta cumulativă"}, "BETAINV": {"a": "(probabilitate; alfa; beta; [A]; [B])", "d": "Returnează inversa funcției de densitate de probabilitate beta cumulativă (BETADIST)"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativ; [A]; [B])", "d": "Returnează funcția de distribuție a probabilității beta"}, "BETA.INV": {"a": "(probabilitate; alfa; beta; [A]; [B])", "d": "Returnează inversa funcției de densitate de probabilitate beta cumulativă (BETA.DIST)"}, "BINOMDIST": {"a": "(număr_s; înce<PERSON><PERSON><PERSON>; probabilitate_s; cumulativ)", "d": "Returnează probabilitatea distribuției binomiale pentru o variabilă discretă"}, "BINOM.DIST": {"a": "(număr_s; înce<PERSON><PERSON><PERSON>; probabilitate_s; cumulativ)", "d": "Returnează probabilitatea distribuției binomiale pentru o variabilă discretă"}, "BINOM.DIST.RANGE": {"a": "(înce<PERSON><PERSON><PERSON>; probabilitate_s; număr_s; [număr_s2])", "d": "Returnează probabilitatea ca un rezultat de încercare să utilizeze o distribuție binomială"}, "BINOM.INV": {"a": "(încerc<PERSON>ri; probabilitate_s; alfa)", "d": "Returnează valoarea cea mai mică pentru care distribuția binomială cumulativă este mai mare sau egală cu o valoare criteriu"}, "CHIDIST": {"a": "(x; grade_libertate)", "d": "Returnează probabilitatea distribuției hi-pătrat unilaterale dreapta"}, "CHIINV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa probabilității distribuției hi-pătrat unilaterale dreapta"}, "CHITEST": {"a": "(zonă_actuale; zonă_așteptate)", "d": "Returnează testul de independență: valoarea din distribuția hi-pătrat pentru statistică și gradele de libertate corespunzătoare"}, "CHISQ.DIST": {"a": "(x; grade_libertate; cumulativ)", "d": "Returnează probabilitatea distribuției Hi-pătrat a cozii din stânga"}, "CHISQ.DIST.RT": {"a": "(x; grade_libertate)", "d": "Returnează probabilitatea distribuției Hi-pătrat a cozii din dreapta"}, "CHISQ.INV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa probabilității distribuției Hi-pătrat la stânga"}, "CHISQ.INV.RT": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa probabilității distribuției Hi-pătrat la dreapta"}, "CHISQ.TEST": {"a": "(zonă_actuale; zonă_așteptate)", "d": "Returnează testul de independență: valoarea din distribuția Hi-pătrat pentru statistică și gradele de libertate corespunzătoare"}, "CONFIDENCE": {"a": "(alfa; dev_standard; dimens)", "d": "Returnează intervalul de încredere pentru o medie a populației, utilizând o distribuție normală"}, "CONFIDENCE.NORM": {"a": "(alfa; dev_standard; dimensiune)", "d": "Returnează intervalul de încredere pentru o medie a populației, utilizând o distribuție normală"}, "CONFIDENCE.T": {"a": "(alfa; dev_standard; dimensiune)", "d": "Returnează intervalul de încredere pentru o medie a populației, utilizând o distribuție t-student"}, "CORREL": {"a": "(matrice1; matrice2)", "d": "Returnează coeficientul de corelație dintre două seturi de date"}, "COUNT": {"a": "(valoare1; [valoare2]; ...)", "d": "Numără câte celule dintr-un interval conțin numere"}, "COUNTA": {"a": "(valoare1; [valoare2]; ...)", "d": "Num<PERSON><PERSON><PERSON> celulele dintr-un interval care nu sunt goale"}, "COUNTBLANK": {"a": "(zonă)", "d": "Numără celulele goale dintr-o zonă precizată de celule"}, "COUNTIF": {"a": "(zonă; criterii)", "d": "Numără celulele dintr-o zonă care îndeplinesc condiția dată"}, "COUNTIFS": {"a": "(zonă_criterii; criterii; ...)", "d": "Contorizează numărul de celule specificat într-un set dat de condiții sau criterii"}, "COVAR": {"a": "(matrice1; matrice2)", "d": "<PERSON><PERSON><PERSON><PERSON>, media produselor deviațiilor pentru fiecare pereche de puncte de date din două seturi de date"}, "COVARIANCE.P": {"a": "(matrice1; matrice2)", "d": "Returnează covarianța populației, media produselor deviațiilor pentru fiecare pereche de puncte din două seturi de date"}, "COVARIANCE.S": {"a": "(matrice1; matrice2)", "d": "Returnează covarianța eșantion, media produselor deviațiilor pentru fiecare pereche de puncte din două seturi de date"}, "CRITBINOM": {"a": "(încerc<PERSON>ri; probabilitate_s; alfa)", "d": "Returnează valoarea cea mai mică pentru care distribuția binomială cumulativă este mai mare sau egală cu o valoare criteriu"}, "DEVSQ": {"a": "(număr1; [număr2]; ...)", "d": "Returnează suma pătratelor deviațiilor punctelor de date față de media eșantionului"}, "EXPONDIST": {"a": "(x; lambda; cumulativ)", "d": "Returnează distribuția exponențială"}, "EXPON.DIST": {"a": "(x; lambda; cumulativ)", "d": "Returnează distribuția exponențială"}, "FDIST": {"a": "(x; grade_libertate1; grade_libertate2)", "d": "Returnează distribuția de probabilitate F (unilaterale dreapta) (grad de diversitate) pentru două seturi de date"}, "FINV": {"a": "(probabilitate; grade_libertate1; grade_libertate2)", "d": "Returnează inversa distribuției de probabilitate F (unilaterale dreapta): dacă p = FDIST(x;...), atunci FINV(p;....) = x"}, "FTEST": {"a": "(matrice1; matrice2)", "d": "Returnează rezultatul unui test F, probabilitatea bilaterală ca varianțele din Matrice1 și Matrice2 să nu fie semnificativ diferite"}, "F.DIST": {"a": "(x; grade_libertate1; grade_libertate2; cumulativ)", "d": "Returnează distribuția de probabilitate F (coada din stânga) (grad de diversitate) pentru două seturi de date"}, "F.DIST.RT": {"a": "(x; grade_libertate1; grade_libertate2)", "d": "Returnează distribuția de probabilitate F (coada din dreapta) (grad de diversitate) pentru două seturi de date"}, "F.INV": {"a": "(probabilitate; grade_libertate1; grade_libertate2)", "d": "Returnează inversa distribuției de probabilitate F (coada din stânga): dacă p = F.DIST(x;...), atunci F.INV(p;....) = x"}, "F.INV.RT": {"a": "(probabilitate; grade_libertate1; grade_libertate2)", "d": "Returnează inversa distribuției de probabilitate F (coada din dreapta): dacă p = F.DIST.RT(x;...), atunci F.INV.RT(p;....) = x"}, "F.TEST": {"a": "(matrice1; matrice2)", "d": "Returnează rezultatul unui test F, probabilitatea bilaterală ca varianțele din Matrice1 și Matrice2 să nu fie semnificativ diferite"}, "FISHER": {"a": "(x)", "d": "Returnează transformata Fisher."}, "FISHERINV": {"a": "(y)", "d": "Returnează inversa transformatei Fisher: dacă y = FISHER(x), atunci FISHERINV(y) = x"}, "FORECAST": {"a": "(x; y_cunoscute; x_cunoscute)", "d": "Calculează sau prognozează o valoare viitoare de-a lungul unei tendințe liniare, utilizând valorile existente"}, "FORECAST.ETS": {"a": "(data_țintă; valori; cronologie; [sezonalitate]; [date_complete]; [agregare])", "d": "Returnează valoarea prognozată pentru o dată țintă viitoare specificată utilizând metoda ponderată exponențială."}, "FORECAST.ETS.CONFINT": {"a": "(data_țintă; valori; cronologie; [nivel_încredere]; [sezonalitate]; [date_complete]; [agregare])", "d": "Returnează un interval de încredere pentru valoarea prognozată, la data țintă specificată."}, "FORECAST.ETS.SEASONALITY": {"a": "(valori; cronologie; [date_complete]; [agregare])", "d": "Returnează lungimea modelului repetitiv pe care aplicația îl detectează pentru seria de timp specificată."}, "FORECAST.ETS.STAT": {"a": "(valori; cronologie; tip_statistic; [sezonalitate]; [date_complete]; [agregare])", "d": "Returnează datele statistice solicitate pentru prognoză."}, "FORECAST.LINEAR": {"a": "(x; y_cunoscute; x_cunoscute)", "d": "Calculează sau prognozează o valoare viitoare de-a lungul unei tendințe liniare, utilizând valori existente"}, "FREQUENCY": {"a": "(matrice_date; matrice_clasă)", "d": "Calculează cât de des apar valorile într-o zonă de valori, apoi returnează o matrice verticală de numere având un element mai mult decât matrice_clasă"}, "GAMMA": {"a": "(x)", "d": "Returnează valoarea funcției Gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuția gamma"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuția gamma"}, "GAMMAINV": {"a": "(probabilitate; alfa; beta)", "d": "Returnează inversa distribuției cumulativ gamma: dacă p = GAMMADIST(x,...), atunci GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(probabilitate; alfa; beta)", "d": "Returnează inversa distribuției cumulativ gamma: dacă p = GAMMA.DIST(x,...), atunci GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Returnează logaritmul natural al funcției gamma"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnează logaritmul natural al funcției gamma"}, "GAUSS": {"a": "(x)", "d": "Returnează cu 0,5 mai puțin decât distribuția cumulativă normală standard"}, "GEOMEAN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media geometrică a unei matrice sau zone de date numerice pozitive"}, "GROWTH": {"a": "(y_cunoscute; [x_cunoscute]; [x_noi]; [const])", "d": "Returnează numere cu o tendință de creștere exponențială corespunzător punctelor de date cunoscute"}, "HARMEAN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media armonică a unui set de date de numere pozitive: reciproca mediei aritmetice a reciprocelor"}, "HYPGEOM.DIST": {"a": "(eșantion_s; număr_eșantion; populație_s; număr_pop; cumulativ)", "d": "Returnează distribuția hipergeometrică"}, "HYPGEOMDIST": {"a": "(eșantion_s; număr_eșantion; populație_s; număr_pop)", "d": "Returnează distribuția hipergeometrică"}, "INTERCEPT": {"a": "(cunoscute_y; cunoscute_x)", "d": "Calculează punctul în care o linie va intersecta axa y utilizând linia de regresie care se potrivește cel mai bine. Linia este trasată printre valorile x și y cunoscute"}, "KURT": {"a": "(număr1; [număr2]; ...)", "d": "Returnează coeficientul kurtosis pentru un set de date"}, "LARGE": {"a": "(matrice; k)", "d": "Returnează a k-a din cele mai mari valori dintr-un set de date. De exemplu, al cincilea din cele mai mari numere"}, "LINEST": {"a": "(y_cunoscute; [x_cunoscute]; [const]; [statistică])", "d": "Returnează statistica ce descrie o tendință liniară care se potrivește cel mai bine punctelor de date, prin potrivirea unei linii drepte utilizând metoda celor mai mici pătrate"}, "LOGEST": {"a": "(y_cunoscute; [x_cunoscute]; [const]; [statistică])", "d": "Returnează o statistică ce descrie o curbă exponențială care corespunde punctelor de date cunoscute"}, "LOGINV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa funcției distribuție cumulativă lognormală a lui x, unde ln(x) este distribuit normal cu parametrii media și dev_standard"}, "LOGNORM.DIST": {"a": "(x; media; dev_standard; cumulativ)", "d": "Returnează distribuția lognormală a lui x, în care ln(x) este distribuit normal cu parametrii media și dev_standard"}, "LOGNORM.INV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa funcției distribuție cumulativă lognormală a lui x, unde ln(x) este distribuit normal cu parametrii media și dev_standard"}, "LOGNORMDIST": {"a": "(x; media; dev_standard)", "d": "Returnează distribuția lognormală cumulativă a lui x, în care ln(x) este distribuit normal cu parametrii media și dev_standard"}, "MAX": {"a": "(număr1; [număr2]; ...)", "d": "Returnează cea mai mare valoare dintr-un set de valori. Ignoră valorile logice și textul"}, "MAXA": {"a": "(valoare1; [valoare2]; ...)", "d": "Returnează cea mai mare valoare dintr-un set de valori. Nu ignoră valorile logice și text"}, "MAXIFS": {"a": "(zonă_max; zonă_criterii; criterii; ...)", "d": "Returnează valoarea maximă dintre celulele specificate după un set dat de condiții sau criterii"}, "MEDIAN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează mediana sau numărul din mijlocul unui set de numere date"}, "MIN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează cel mai mic număr dintr-un set de valori. Ignoră valorile logice și textul"}, "MINA": {"a": "(valoare1; [valoare2]; ...)", "d": "Returnează cea mai mică valoare dintr-un set de valori. Nu ignoră valori logice și text"}, "MINIFS": {"a": "(zonă_min; zonă_criterii; criterii; ...)", "d": "Returnează valoarea minimă dintre celulele specificate după un set dat de condiții sau criterii"}, "MODE": {"a": "(număr1; [număr2]; ...)", "d": "Returnează valoarea cea mai frecventă sau repetitivă dintr-o matrice sau zonă de date"}, "MODE.MULT": {"a": "(număr1; [număr2]; ...)", "d": "Returnează o matrice verticală cu valorile cele mai frecvente sau repetitive dintr-o matrice sau zonă de date. Pentru o matrice orizontală, utilizați =TRANSPOSE(MODE.MULT(număr1,număr2,...))"}, "MODE.SNGL": {"a": "(număr1; [număr2]; ...)", "d": "Returnează valoarea cea mai frecventă sau repetitivă dintr-o matrice sau zonă de date"}, "NEGBINOM.DIST": {"a": "(număr_f; număr_s; probabilitate_s; cumulativ)", "d": "Returnează distribuția binomială negativă, probabilitatea că vor exista un număr (număr_f) eșecuri înaintea succesului cu numărul număr_s, cu probabilitatea probabilitate_s a unui succes"}, "NEGBINOMDIST": {"a": "(număr_f; număr_s; probabilitate_s)", "d": "Returnează distribuția binomială negativă, probabilitatea că vor exista un număr (număr_f) eșecuri înaintea succesului cu numărul număr_s, cu probabilitatea probabilitate_s a unui succes"}, "NORM.DIST": {"a": "(x; media; dev_standard; cumulativ)", "d": "Returnează distribuția normală pentru media specificată și deviația standard"}, "NORMDIST": {"a": "(x; media; dev_standard; cumulativ)", "d": "Returnează distribuția cumulativă normală pentru media și deviația standard specificate"}, "NORM.INV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa distribuției cumulativ normale pentru media specificată și deviația standard"}, "NORMINV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa distribuției cumulativ normale pentru media și deviația standard specificate"}, "NORM.S.DIST": {"a": "(z; cumulativ)", "d": "Returnează distribuția normală standard (are o medie de zero și o deviație standard de unu)"}, "NORMSDIST": {"a": "(z)", "d": "Returnează distribuția cumulativă normală standard (are o medie de zero și o deviație standard de unu)"}, "NORM.S.INV": {"a": "(probabilitate)", "d": "Returnează inversa distribuției cumulativ normale standard (are o medie de zero și o deviație standard de unu)"}, "NORMSINV": {"a": "(probabilitate)", "d": "Returnează inversa distribuției cumulativ normale standard (are o medie de zero și o deviație standard de unu)"}, "PEARSON": {"a": "(matrice1; matrice2)", "d": "Returnează coeficientul Pearson de corelație a momentelor pro<PERSON>, r"}, "PERCENTILE": {"a": "(matrice; k)", "d": "Returnează a k-a procentilă de valori dintr-o zonă"}, "PERCENTILE.EXC": {"a": "(matrice; k)", "d": "Returnează a k-a procentilă de valori dintr-o zonă, unde k este intervalul 0..1, exclusiv"}, "PERCENTILE.INC": {"a": "(matrice; k)", "d": "Returnează a k-a procentilă de valori dintr-o zonă, unde k este intervalul 0..1, inclusiv"}, "PERCENTRANK": {"a": "(matrice; x; [semnifica<PERSON>ie])", "d": "Returnează rangul unei valori dintr-un set de date ca procentaj din setul de date"}, "PERCENTRANK.EXC": {"a": "(matrice; x; [semnifica<PERSON>ie])", "d": "Returnează rangul unei valori dintr-un set de date ca procentaj din setul de date (0..1, exclusiv)"}, "PERCENTRANK.INC": {"a": "(matrice; x; [semnifica<PERSON>ie])", "d": "Returnează rangul unei valori dintr-un set de date ca procentaj din setul de date (0..1, inclusiv)"}, "PERMUT": {"a": "(număr; număr_ales)", "d": "Returnează numărul de permutări pentru un număr dat de obiecte care pot fi selectate din totalul de obiecte"}, "PERMUTATIONA": {"a": "(număr; număr_ales)", "d": "Returnează numărul de permutări pentru un număr dat de obiecte (cu repetiții) care pot fi selectate din totalul de obiecte"}, "PHI": {"a": "(x)", "d": "Returnează valoarea funcției de densitate pentru o distribuție normală standard"}, "POISSON": {"a": "(x; media; cumulativ)", "d": "Returnează distribuția Poisson"}, "POISSON.DIST": {"a": "(x; media; cumulativ)", "d": "Returnează distribuția Poisson"}, "PROB": {"a": "(zonă_x; zonă_prob; limită_inf; [limită_sup])", "d": "Returnează probabilitatea ca valorile dintr-o zonă să fie între două limite sau egale cu limita inferioară"}, "QUARTILE": {"a": "(matrice; quart)", "d": "Returnează cuartila unui set de date"}, "QUARTILE.INC": {"a": "(matrice; quart)", "d": "Returnează cuartila unui set de date în baza valorile procentilei din 0..1, inclusiv"}, "QUARTILE.EXC": {"a": "(matrice; quart)", "d": "Returnează cuartila unui set de date în baza valorile procentilei din 0..1, exclusiv"}, "RANK": {"a": "(număr; ref; [ordine])", "d": "Returnează rangul unui număr dintr-o listă de numere: este dimensiunea relativ la alte valori din listă"}, "RANK.AVG": {"a": "(număr; ref; [ordine])", "d": "Returnează rangul unui număr dintr-o listă de numere: este dimensiunea relativ la alte valori din listă; dacă mai multe valori au același rang, se returnează rangul mediu"}, "RANK.EQ": {"a": "(număr; ref; [ordine])", "d": "Returnează rangul unui număr dintr-o listă de numere: este dimensiunea relativ la alte valori din listă; dacă mai multe valori au același rang, se returnează rangul maxim al acelui set de valori"}, "RSQ": {"a": "(cunoscute_y; cunoscute_x)", "d": "Returnează pătratul coeficientului Pearson de corelație moment-produs printre punctele de date cunoscute"}, "SKEW": {"a": "(număr1; [număr2]; ...)", "d": "Returnează panta unei distribuții: o caracterizare a gradului de asimetrie a unei distribuții în jurul mediei sale"}, "SKEW.P": {"a": "(număr1; [număr2]; ...)", "d": "Returnează panta unei distribuții pe baza unei populații: o caracterizare a gradului de asimetrie a unei distribuții în jurul mediei sale"}, "SLOPE": {"a": "(cunoscute_y; cunoscute_x)", "d": "Returnează panta unei linii de regresie liniară printre punctele de date cunoscute"}, "SMALL": {"a": "(matrice; k)", "d": "Returnează a k-a din cele mai mici valori dintr-un set de date. De exemplu, al cincilea dintre cele mai mici numere"}, "STANDARDIZE": {"a": "(x; media; dev_standard)", "d": "Returnează o valoare normalizată dintr-o distribuție caracterizată de o medie și o deviație standard"}, "STDEV": {"a": "(număr1; [număr2]; ...)", "d": "Estimează deviația standard pe baza unui eșantion (ignoră valorile logice și textul din eșantion)"}, "STDEV.P": {"a": "(număr1; [număr2]; ...)", "d": "Calculează deviația standard bazată pe întreaga populație dată ca argumente (ignoră valorile logice și text)"}, "STDEV.S": {"a": "(număr1; [număr2]; ...)", "d": "Estimează deviația standard bazată pe un eșantion (ignoră valorile logice și text din  eșantion)"}, "STDEVA": {"a": "(valoare1; [valoare2]; ...)", "d": "Estimează deviația standard bazat pe un eșantion, incluzând valori logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1"}, "STDEVP": {"a": "(număr1; [număr2]; ...)", "d": "Calculează deviația standard pe baza populației totale dată ca argumente (ignoră valorile logice și textul)"}, "STDEVPA": {"a": "(valoare1; [valoare2]; ...)", "d": "Calculează deviația standard pe baza întregii populații, incluzând valori logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1"}, "STEYX": {"a": "(cunoscute_y; cunoscute_x)", "d": "Returnează eroarea standard a valorii y prezise pentru fiecare x dintr-o regresie"}, "TDIST": {"a": "(x; grade_libertate; cozi)", "d": "Returnează distribuția t Student"}, "TINV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa distribuției t Student"}, "T.DIST": {"a": "(x; grade_libertate; cumulativ)", "d": "Returnează distribuția din coada stângă t-student"}, "T.DIST.2T": {"a": "(x; grade_libertate)", "d": "Returnează distribuția t-student cu două cozi"}, "T.DIST.RT": {"a": "(x; grade_libertate)", "d": "Returnează distribuția t-student a cozii din stânga"}, "T.INV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa distribuției t Student a cozii din dreapta"}, "T.INV.2T": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa cu două cozi a distribuției t Student"}, "T.TEST": {"a": "(matrice1; matrice2; cozi; tip)", "d": "Returnează probabilitatea asociată cu un test t Student"}, "TREND": {"a": "(y_cunoscute; [x_cunoscute]; [x_noi]; [const])", "d": "Returnează numere într-o tendință liniară care se potrivește punctelor de date cunoscute, utilizând metoda celor mai mici pătrate"}, "TRIMMEAN": {"a": "(matrice; procent)", "d": "Returnează media porțiuni interioare a unui set de valori de date"}, "TTEST": {"a": "(matrice1; matrice2; cozi; tip)", "d": "Returnează probabilitatea asociată cu un test t Student"}, "VAR": {"a": "(număr1; [număr2]; ...)", "d": "Estimează varianța pe baza unui eșantion (ignoră valorile logice și textul din eșantion)"}, "VAR.P": {"a": "(număr1; [număr2]; ...)", "d": "Calculează varianța bazată pe întreaga populație (ignoră  valorile logice și text din populație)"}, "VAR.S": {"a": "(număr1; [număr2]; ...)", "d": "Estimează varianța bazată pe un eșantion (ignoră valorile logice și text din eșantion)"}, "VARA": {"a": "(valoare1; [valoare2]; ...)", "d": "Estimează varianța pe baza unui eșantion, incluzând valorile logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1"}, "VARP": {"a": "(număr1; [număr2]; ...)", "d": "Calculează varianța pe baza populației totale (ignoră valorile logice și textul din populație)"}, "VARPA": {"a": "(valoare1; [valoare2]; ...)", "d": "Calculează varianța pe baza întregii populații, incluzând valori logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1"}, "WEIBULL": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuț<PERSON>bull"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuț<PERSON>bull"}, "Z.TEST": {"a": "(matrice; x; [sigma])", "d": "Returnează valoarea P unilaterală a unui test z"}, "ZTEST": {"a": "(matrice; x; [sigma])", "d": "Returnează valoarea P unilaterală a unui test z"}, "ACCRINT": {"a": "(emis; prim_cupon; tranzacție; rată; par; frecvență; [bază]; [calc_metodă])", "d": "Returnează dobânda acumulată pentru o asigurare care plătește periodic dobândă."}, "ACCRINTM": {"a": "(emis; tranzacție; rată; par; [bază])", "d": "Returnează dobânda acumulată pentru o asigurare care plătește dobândă la maturitate"}, "AMORDEGRC": {"a": "(cost; data_cump; prima_per; recuperat; perioadă; rată; [bază])", "d": "Returnează deprecierea lineară proporțională pentru un bun pentru fiecare perioadă de contabilizare."}, "AMORLINC": {"a": "(cost; data_cump; prima_per; recuperat; perioadă; rată; [bază])", "d": "Returnează deprecierea lineară proporțională pentru un bun pentru fiecare perioadă de contabilizare."}, "COUPDAYBS": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de zile de la începutul perioadei de cupon la data de lichidare"}, "COUPDAYS": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de zile în perioada de cupon care conține și data de lichidare"}, "COUPDAYSNC": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de zile de la data de stabilire a asigurării la data următorului cupon"}, "COUPNCD": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează următoarea dată de pe cupon după data de stabilire a asigurării"}, "COUPNUM": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de cupoane de plată între data de stabilire a asigurării și data de maturitate"}, "COUPPCD": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează data de pe cuponul precedent înainte de data de stabilire a asigurării"}, "CUMIPMT": {"a": "(rată; nper; pv; per_start; per_ultima; tip)", "d": "Returnează dobânda cumulativă plătită între două perioade"}, "CUMPRINC": {"a": "(rată; nper; pv; per_start; per_ultima; tip)", "d": "Returnează suma cumulativă plătită dintr-un împrumut între două perioade"}, "DB": {"a": "(cost; recuperat; viață; perioadă; [lună])", "d": "Returnează amortizarea unui mijloc fix pentru o perioadă specificată utilizând metoda balanței cu amortizare fixă"}, "DDB": {"a": "(cost; val_reziduală; viață; perioadă; [factor])", "d": "Returnează amortizarea unui mijloc fix pentru o perioadă specificată utilizând metoda balanței amortizare dublă sau altă metodă specificată"}, "DISC": {"a": "(tranzac<PERSON>ie; maturitate; pr; rambursare; [bază])", "d": "Returnează rata de reducere pentru o asigurare"}, "DOLLARDE": {"a": "(preț_fracție; fracție)", "d": "Se efectuează conversia unui preț în dolari, exprimat ca fracție, într-un preț în dolari, exprimat ca număr zec<PERSON>l"}, "DOLLARFR": {"a": "(preț_zecimal; fracție)", "d": "Se efectuează conversia unui preț în dolari, exprimat ca număr z<PERSON>, într-un preț în dolari, exprimat ca fracție"}, "DURATION": {"a": "(tranzacție; maturitate; cupon; produs; frecvență; [bază])", "d": "Returnează durata anuală a unei asigurări cu plăți periodice ale dobânzii"}, "EFFECT": {"a": "(rată_nominală; n_per)", "d": "Returnează rata anuală efectivă a dobânzii"}, "FV": {"a": "(rată; nper; pmt; [pv]; [tip])", "d": "Returnează valoarea viitoare a unei investiții bazate pe plăți constante periodice și o rată constantă a dobânzii"}, "FVSCHEDULE": {"a": "(principal; plan)", "d": "Returnează valoarea viitoare a unui principal inițial, după aplicarea unei serii de rate ale dobânzii compuse"}, "INTRATE": {"a": "(tranzacție; maturitate; investiție; rambursare; [bază])", "d": "Returnează rata dobânzii pentru o asigurare în care s-a investit integral"}, "IPMT": {"a": "(rată; per; nper; pv; [fv]; [tip])", "d": "Returnează pentru o perioadă dată plata dobânzii unei investiții, bazată pe plăți constante periodice și pe o rată constantă a dobânzii"}, "IRR": {"a": "(valori; [estim])", "d": "Returnează rata de rentabilitate internă pentru o serie de fluxuri de numerar"}, "ISPMT": {"a": "(rată; per; nper; pv)", "d": "Returnează dobânda plătită într-o anumită perioadă pentru o investiție"}, "MDURATION": {"a": "(tranzacție; maturitate; cupon; produs; frecvență; [bază])", "d": "Returnează durata Macauley modificată pentru o asigurare cu o valoare nominală de 100 lei"}, "MIRR": {"a": "(valori; rată_finan; rată_reinvest)", "d": "Returnează rata de rentabilitate internă pentru o serie de fluxuri periodice de numerar, luând în considerare costul investiției și dobânda din reinvestirea numerarului"}, "NOMINAL": {"a": "(rată_efectivă; n_per)", "d": "Returnează rata anuală a dobânzii nominale"}, "NPER": {"a": "(rată; pmt; pv; [fv]; [tip])", "d": "Returnează numărul de perioade pentru o investiție bazată pe plăți constante periodice și o rată constantă a dobânzii"}, "NPV": {"a": "(rată; valoare1; [valoare2]; ...)", "d": "Returnează valoarea netă prezentă a unei investiții bazată pe o rată de actualizare și o serie de plăți viitoare (valori negative) și venituri  (valori pozitive)"}, "ODDFPRICE": {"a": "(tranzac<PERSON>ie; maturitate; emis; prim_cupon; rată; produs; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează prețul pentru 100 lei valoare reală pentru o asigurare cu o primă perioadă impară"}, "ODDFYIELD": {"a": "(tranzac<PERSON>ie; maturitate; emis; prim_cupon; rată; pr; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează produsul unei asigurări cu o primă perioadă impară"}, "ODDLPRICE": {"a": "(tranzac<PERSON>ie; maturitate; ultim_cupon; rată; produs; rambursare; frecvență; [bază])", "d": "Returnează prețul pentru 100 lei valoare reală a unei asigurări cu o ultimă perioadă impară"}, "ODDLYIELD": {"a": "(tranzac<PERSON>ie; maturitate; ultim_cupon; rată; pr; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează produsul unei asigurări cu o perioadă ultimă impară"}, "PDURATION": {"a": "(rată; pv; fv)", "d": "Returnează numărul de perioade necesare pentru ca o investiție să atingă o valoare specificată"}, "PMT": {"a": "(rată; nper; pv; [fv]; [tip])", "d": "Calculează plata pentru un împrumut bazat pe plăți constante și o rată constantă a dobânzii"}, "PPMT": {"a": "(rată; per; nper; pv; [fv]; [tip])", "d": "Returnează plata efectivă pentru o investiție dată bazată pe plăți constante periodice și o rată constantă a dobânzii"}, "PRICE": {"a": "(tranzacție; maturitate; rată; produs; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează prețul la 100 lei valoare reală al unei asigurări care plătește dobândă periodic"}, "PRICEDISC": {"a": "(tranzac<PERSON>ie; maturitate; reducere; rambursare; [bază])", "d": "Returnează prețul la 100 lei valoare reală dintr-o asigurare cu reducere"}, "PRICEMAT": {"a": "(tranzac<PERSON>ie; maturitate; emis; rată; produs; [bază])", "d": "Returnează prețul la 100 lei valoare reală a unei asigurări care plătește dobânda la maturitate"}, "PV": {"a": "(rată; nper; pmt; [fv]; [tip])", "d": "Returnează valoarea prezentă a unei investiții: valoarea totală actuală a unei serii de plăți viitoare"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [tip]; [estim])", "d": "Returnează rata dobânzii pe perioadă pentru un împrumut sau o investiție. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%"}, "RECEIVED": {"a": "(tranzac<PERSON>ie; maturitate; investiție; reducere; [bază])", "d": "Returnează suma primită la maturitate pentru o asigurare plătită integral"}, "RRI": {"a": "(nper; pv; fv)", "d": "Returnează o rată a dobânzii echivalentă pentru creșterea unei investiții"}, "SLN": {"a": "(cost; recuperat; viață)", "d": "Returnează amortizarea liniară a unui mijloc fix pe o perioadă"}, "SYD": {"a": "(cost; recuperat; viață; per)", "d": "Returnează amortizarea în regresie aritmetică a unui mijloc fix pentru o perioadă specificată"}, "TBILLEQ": {"a": "(tranzac<PERSON>ie; maturitate; reducere)", "d": "Returnează produsul echivalent cu un bond pentru un certificat de trezorerie"}, "TBILLPRICE": {"a": "(tranzac<PERSON>ie; maturitate; reducere)", "d": "Returnează prețul pentru 100 lei valoare reală a unui certificat de trezorerie"}, "TBILLYIELD": {"a": "(tranzacție; maturitate; pr)", "d": "Returnează produsul pentru un certificat de trezorerie"}, "VDB": {"a": "(cost; recuperat; viață; per_start; per_ultima; [factor]; [nr_comutare])", "d": "Returnează amortizarea unui mijloc fix pentru orice perioadă specificată, incluzând perioade parțiale, utilizând metoda balanței amortizare dublă sau altă metodă specificată"}, "XIRR": {"a": "(valori; date; [estim])", "d": "Returnează rata internă de întoarcere pentru un calendar de fluxuri monetare"}, "XNPV": {"a": "(rată; valori; date)", "d": "Returnează valoarea netă prezentă a fluxurilor monetare"}, "YIELD": {"a": "(tranzac<PERSON>ie; maturitate; rată; pr; rambursare; frecven<PERSON><PERSON>; [bază])", "d": "Returnează produsul unei asigurări care plătește dobândă periodic"}, "YIELDDISC": {"a": "(tranzac<PERSON>ie; maturitate; pr; rambursare; [bază])", "d": "Returnează produsul anual pentru o asigurare cu reducere. De exemplu, certificatele de trezorerie"}, "YIELDMAT": {"a": "(tranzac<PERSON>ie; maturitate; emis; rată; pr; [bază])", "d": "Returnează produsul anual al unei asigurări care plătește dobândă la maturizare"}, "ABS": {"a": "(număr)", "d": "Returnează valoarea absolută a unui număr, un număr fără semnul său"}, "ACOS": {"a": "(număr)", "d": "Returnează arccosinusul unui număr, în radiani în intervalul 0 Pi. Arccosinusul este unghiul al cărui cosinus este număr"}, "ACOSH": {"a": "(număr)", "d": "Returnează inversa cosinusului hiperbolic al unui număr"}, "ACOT": {"a": "(număr)", "d": "Returnează arccotangenta unui număr, exprimată în radiani, din zona de la 0 la Pi."}, "ACOTH": {"a": "(număr)", "d": "Returnează cotangenta hiperbolică inversată a unui număr"}, "AGGREGATE": {"a": "(num_func<PERSON>ie; opțiuni; ref1; ...)", "d": "Returnează o interogare agregată dintr-o listă sau bază de date"}, "ARABIC": {"a": "(text)", "d": "Efectuează conversia unui număr roman în număr arab"}, "ASC": {"a": "(text)", "d": "Pentru limbile cu set de caractere pe doi octeți (DBCS), funcția modifică caracterele cu lățime întreagă (doi octeți) în caractere cu lățime pe jumătate (un octet)"}, "ASIN": {"a": "(număr)", "d": "Returnează arcsinusul unui număr în radiani, în intervalul -Pi/2 Pi/2"}, "ASINH": {"a": "(număr)", "d": "Returnează inversa sinusului hiperbolic al unui număr"}, "ATAN": {"a": "(număr)", "d": "Returnează arctangenta unui număr în radiani, în intervalul -Pi/2  Pi/2"}, "ATAN2": {"a": "(num_x; num_y)", "d": "Returnează arctangenta coordonatelor x și y specificate, în radiani între -Pi și Pi, excluzând -Pi"}, "ATANH": {"a": "(număr)", "d": "Returnează inversa tangentei hiperbolice a unui număr"}, "BASE": {"a": "(număr; bază; [lungime_min])", "d": "Efectuează conversia unui număr într-o reprezentare text cu baza dată"}, "CEILING": {"a": "(număr; semnificație)", "d": "Rotunjește prin adaos un număr, la cel mai apropiat multiplu de argument de precizie"}, "CEILING.MATH": {"a": "(număr; [semnificație]; [mod])", "d": "Rotunjește prin adaos un număr, la cel mai apropiat întreg sau la cel mai apropiat multiplu de semnificație"}, "CEILING.PRECISE": {"a": "(număr; [semnificație])", "d": "Returnează un număr care este rotunjit la cel mai apropiat întreg sau la cel mai apropiat multiplu semnificativ"}, "COMBIN": {"a": "(număr; număr_ales)", "d": "Returnează numărul de combinări pentru un număr dat de elemente"}, "COMBINA": {"a": "(număr; număr_ales)", "d": "Returnează numărul de combinări cu repetiții pentru un număr dat de elemente"}, "COS": {"a": "(număr)", "d": "Returnează cosinusul unui unghi"}, "COSH": {"a": "(număr)", "d": "Returnează cosinusul hiperbolic al unui număr"}, "COT": {"a": "(număr)", "d": "Returnează cotangenta unui unghi"}, "COTH": {"a": "(număr)", "d": "Returnează cotangenta hiperbolică a unui număr"}, "CSC": {"a": "(număr)", "d": "Returnează cosecanta unui unghi"}, "CSCH": {"a": "(număr)", "d": "Returnează cosecanta hiperbolică a unui unghi"}, "DECIMAL": {"a": "(număr; bază)", "d": "Efectuează conversia unei reprezentări text a unui număr într-o bază dată în număr cu zecimale"}, "DEGREES": {"a": "(unghi)", "d": "Transformă radiani în grade"}, "ECMA.CEILING": {"a": "(număr; semnificație)", "d": "Rotunjește prin adaos un număr, la cel mai apropiat multiplu de argument de precizie"}, "EVEN": {"a": "(număr)", "d": "Rotunjește prin adaos un număr pozitiv și prin lipsă un număr negativ până la cel mai apropiat întreg par"}, "EXP": {"a": "(număr)", "d": "Returnează e ridicat la o putere dată"}, "FACT": {"a": "(număr)", "d": "Returnează produsul factorial al unui număr, egal cu 1*2*3*...* număr"}, "FACTDOUBLE": {"a": "(număr)", "d": "Returnează factorialul dublu al unui număr"}, "FLOOR": {"a": "(număr; semnificație)", "d": "Rotunjește prin lipsă un număr, la cel mai apropiat multiplu de semnificație"}, "FLOOR.PRECISE": {"a": "(număr; [semnificație])", "d": "Returnează un număr care este rotunjit prin lipsă la cel mai apropiat întreg sau la cel mai apropiat multiplu al semnificației"}, "FLOOR.MATH": {"a": "(număr; [semnificație]; [mod])", "d": "Rotunjește prin scădere un număr, la cel mai apropiat întreg sau la cel mai apropiat multiplu de semnificație"}, "GCD": {"a": "(număr1; [număr2]; ...)", "d": "Returnează cel mai mare divizor comun"}, "INT": {"a": "(număr)", "d": "Rotunjește un număr prin lipsă la cel mai apropiat întreg"}, "ISO.CEILING": {"a": "(număr; [semnif])", "d": "Returnează un număr care este rotunjit la cel mai apropiat întreg sau la cel mai apropiat multiplu semnificativ. Indiferent de semnul numărului, numărul este rotunjit. Însă, dacă numărul sau semnificația este zero, se returnează zero."}, "LCM": {"a": "(număr1; [număr2]; ...)", "d": "Returnează ultimul multiplu comun"}, "LN": {"a": "(număr)", "d": "Returnează logaritmul natural al unui număr"}, "LOG": {"a": "(număr; [baza])", "d": "Returnează logaritmul unui număr în baza specificată"}, "LOG10": {"a": "(număr)", "d": "Returnează logaritmul în baza 10 a unui număr"}, "MDETERM": {"a": "(matrice)", "d": "Returnează determinantul matriceal al matricei"}, "MINVERSE": {"a": "(matrice)", "d": "Returnează inversa matricei pentru matricea stocată într-o zonă"}, "MMULT": {"a": "(matrice1; matrice2)", "d": "Returnează matricea produs a două matrice, o matrice cu același număr de rânduri ca matrice1 și același număr de coloane ca matrice2"}, "MOD": {"a": "(num<PERSON>r; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Returnează restul după ce un număr este împărțit la un împărțitor"}, "MROUND": {"a": "(număr; multiplu)", "d": "Returnează un număr care este rotunjit la valoarea dorită"}, "MULTINOMIAL": {"a": "(număr1; [număr2]; ...)", "d": "Returnează setul multinomial de numere"}, "MUNIT": {"a": "(dimensiune)", "d": "Returnează matricea de unitate pentru dimensiunea specificată"}, "ODD": {"a": "(număr)", "d": "Rotunjește prin adaos un număr pozitiv și prin lipsă un număr negativ la cel mai apropiat întreg impar"}, "PI": {"a": "()", "d": "Return<PERSON><PERSON><PERSON> valoarea lui Pi, 3,14159265358979 cu precizie de 15 cifre"}, "POWER": {"a": "(număr; exponent)", "d": "Returnează rezultatul unui număr ridicat la o putere"}, "PRODUCT": {"a": "(număr1; [număr2]; ...)", "d": "Înmulț<PERSON>ște toate numerele date ca argumente"}, "QUOTIENT": {"a": "(num<PERSON><PERSON><PERSON>tor; numitor)", "d": "Returnează porțiunea întreagă a unei împărțiri"}, "RADIANS": {"a": "(unghi)", "d": "Transformă gradele în radiani"}, "RAND": {"a": "()", "d": "Returnează un număr aleator mai mare sau egal cu 0 și mai mic decât 1, distribuit uniform (se modifică la recalculare)"}, "RANDARRAY": {"a": "([rân<PERSON><PERSON>]; [coloane]; [min]; [max]; [întreg])", "d": "Returnează o matrice de numere aleatorii"}, "RANDBETWEEN": {"a": "(inf; sup)", "d": "Returnează un număr aleatoriu dintre numerele specificate"}, "ROMAN": {"a": "(număr; [formă])", "d": "Transformă un numeral arab în roman, ca text"}, "ROUND": {"a": "(număr; num_cifre)", "d": "Rotunjește un număr la un număr specificat de cifre"}, "ROUNDDOWN": {"a": "(număr; num_cifre)", "d": "Rotunjește un număr p<PERSON>, spre zero"}, "ROUNDUP": {"a": "(număr; num_cifre)", "d": "Rotunjește un număr prin adao<PERSON>, dinspre zero"}, "SEC": {"a": "(număr)", "d": "Returnează secanta unui unghi"}, "SECH": {"a": "(număr)", "d": "Returnează secanta hiper<PERSON>ă a unui unghi"}, "SERIESSUM": {"a": "(x; n; m; coeficien<PERSON>i)", "d": "Returnează suma unor serii de puteri bazate pe formulă"}, "SIGN": {"a": "(număr)", "d": "Returnează semnul unui număr: 1 dacă numărul este pozitiv, zero dacă numărul este zero, sau -1 dacă numărul este negativ"}, "SIN": {"a": "(număr)", "d": "Returnează sinusul unui unghi"}, "SINH": {"a": "(număr)", "d": "Returnează sinusul hiperbolic al unui număr"}, "SQRT": {"a": "(număr)", "d": "Returnează rădăcina pătrată a unui număr"}, "SQRTPI": {"a": "(număr)", "d": "Returnează rădăcina pătrată a (numărului * Pi)"}, "SUBTOTAL": {"a": "(num_funcție; ref1; ...)", "d": "Returnează un subtotal într-o listă sau bază de date"}, "SUM": {"a": "(număr1; [număr2]; ...)", "d": "<PERSON><PERSON><PERSON> toate numerele dintr-o zonă de celule"}, "SUMIF": {"a": "(zonă; criterii; [zonă_sumă])", "d": "Adună celulele specificate de o condiție sau criteriu dat"}, "SUMIFS": {"a": "(zonă_sumă; zonă_criterii; criterii; ...)", "d": "Adaugă celulele specificate dintr-un set de condiții sau criterii"}, "SUMPRODUCT": {"a": "(matrice1; [matrice2]; [matrice3]; ...)", "d": "Returnează suma produselor zonelor sau matricelor corespondente"}, "SUMSQ": {"a": "(număr1; [număr2]; ...)", "d": "Returnează suma pătratelor argumentelor. Argumentele pot fi numere, matrice, nume sau referințe la celule care conțin numere"}, "SUMX2MY2": {"a": "(matrice_x; matrice_y)", "d": "Însumează diferențele pătratelor a două zone sau matrice corespondente"}, "SUMX2PY2": {"a": "(matrice_x; matrice_y)", "d": "Returnează suma totală a sumelor pătratelor numerelor din două zone sau matrice corespondente"}, "SUMXMY2": {"a": "(matrice_x; matrice_y)", "d": "Însumează pătratele diferențelor dintre două zone sau matrice corespondente"}, "TAN": {"a": "(număr)", "d": "Returnează tangenta unui unghi"}, "TANH": {"a": "(număr)", "d": "Returnează tangenta hiperbolică a unui număr"}, "TRUNC": {"a": "(număr; [num_cifre])", "d": "Trunchiază un număr la un întreg eliminând partea zecimală, sau fracționară a numărului"}, "ADDRESS": {"a": "(num_rând; num_coloană; [num_abs]; [a1]; [text_foaie])", "d": "Creează o referință la celulă ca text, având date numerele de rând și coloană"}, "CHOOSE": {"a": "(index_num; valoare1; [valoare2]; ...)", "d": "Alege o valoare sau acțiune de efectuat dintr-o listă de valori, bazată pe un număr index"}, "COLUMN": {"a": "([referin<PERSON><PERSON>])", "d": "Returnează numărul coloanei pentru o referință"}, "COLUMNS": {"a": "(matrice)", "d": "Returnează numărul de coloane dintr-o matrice sau referință"}, "FORMULATEXT": {"a": "(referință)", "d": "Returnează o formulă ca șir"}, "HLOOKUP": {"a": "(căutare_valoare; matrice_tabel; num_index_rând; [zon<PERSON>_căutare])", "d": "Caută o valoare în rândul de sus al unei tabele sau matrice de valori și returnează valoarea în aceeași coloană dintr-un rând specificat"}, "HYPERLINK": {"a": "(locație_link; [nume_prietenos])", "d": "Creează o comandă rapidă sau un salt care deschide un document stocat pe hard disc, un server de rețea, sau pe Internet"}, "INDEX": {"a": "(matrice; num_rând; [num_coloană]!referinț<PERSON>; num_rând; [num_coloană]; [num_suprafață])", "d": "Returnează o valoare sau referința la o celulă de la intersecția unui rând și unei coloane particulare, dintr-o zonă dată"}, "INDIRECT": {"a": "(text_ref; [a1])", "d": "Returnează referința specificată de un șir text"}, "LOOKUP": {"a": "(căutare_valoare; căutare_vector; [rezultat_vector]!căutare_valoare; matrice)", "d": "Caută o valoare fie într-o zonă de un rând sau de o coloană, fie într-o matrice. Este furnizată pentru compatibilitate cu versiunile anterioare"}, "MATCH": {"a": "(căutare_valoare; căutare_matrice; [tip_ret])", "d": "Returnează poziția relativă a unui element dintr-o matrice care corespunde unei valori precizate într-o ordine precizată"}, "OFFSET": {"a": "(refer<PERSON><PERSON><PERSON>; rânduri; col; [înalt]; [lat])", "d": "Returnează o referință la o zonă care este un număr precizat de rânduri și coloane dintr-o referință dată"}, "ROW": {"a": "([referin<PERSON><PERSON>])", "d": "Returnează numărul rândului pentru o referință"}, "ROWS": {"a": "(matrice)", "d": "Returnează numărul de rânduri dintr-o referință sau matrice"}, "TRANSPOSE": {"a": "(matrice)", "d": "Transformă o zonă verticală de celule în zonă orizontală sau viceversa"}, "UNIQUE": {"a": "(matrice; [dup<PERSON>_coloană]; [exact_o_dată])", "d": "Returnează valorile unice dintr-o zonă sau dintr-o matrice."}, "VLOOKUP": {"a": "(căutare_valoare; matrice_tabel; num_index_col; [zon<PERSON>_căutare])", "d": "Caută o valoare în coloana cea mai din stânga a unui tabel, apoi returnează o valoare în același rând dintr-o coloană precizată. Implicit, tabelul trebuie sortat în ordine ascendentă"}, "XLOOKUP": {"a": "(valoare_căutare; matrice_căutare; matrice­_returnată; [dacă_nu_se_g<PERSON><PERSON><PERSON><PERSON>]; [mod_potrivire]; [mod_căutare])", "d": "Caută într-o zonă sau într-o matrice pentru o potrivire și returnează elementul corespunzător dintr-o a doua zonă sau matrice. În mod implicit, se utilizează o potrivire exactă"}, "CELL": {"a": "(tip_info; [referin<PERSON><PERSON>])", "d": "Returnează informații despre formatare, locație și conținutul unei celule"}, "ERROR.TYPE": {"a": "(val_er)", "d": "Returnează un număr care corespunde unei valori de eroare."}, "ISBLANK": {"a": "(valoare)", "d": "Verifică dacă o referință este către o celulă goală și returnează TRUE sau FALSE"}, "ISERR": {"a": "(valoare)", "d": "Verifică dacă o valoare este o altă eroare decât #N/A și returnează TRUE sau FALSE"}, "ISERROR": {"a": "(valoare)", "d": "Verifică dacă o valoare este o eroare și returnează TRUE sau FALSE"}, "ISEVEN": {"a": "(număr)", "d": "Returnează TRUE dacă numărul este par"}, "ISFORMULA": {"a": "(referință)", "d": "Verifică dacă o referință este la o celulă ce conține o formulă și returnează TRUE sau FALSE"}, "ISLOGICAL": {"a": "(valoare)", "d": "Verifică dacă o valoare este logică (TRUE sau FALSE) și returnează TRUE sau FALSE"}, "ISNA": {"a": "(valoare)", "d": "Verifică dacă o valoare este #N/A și returnează TRUE sau FALSE"}, "ISNONTEXT": {"a": "(valoare)", "d": "Verifică dacă o valoare este text (celulele necompletate nu conțin text) și returnează TRUE sau FALSE"}, "ISNUMBER": {"a": "(valoare)", "d": "Verifică dacă o valoare este număr și returnează TRUE sau FALSE"}, "ISODD": {"a": "(număr)", "d": "Returnează TRUE dacă numărul este impar"}, "ISREF": {"a": "(valoare)", "d": "Verifică dacă o valoare este o referință și returnează TRUE sau FALSE"}, "ISTEXT": {"a": "(valoare)", "d": "Verifică dacă o valoare este text și returnează TRUE sau FALSE"}, "N": {"a": "(valoare)", "d": "Transformă o valoare nenumerică într-un număr, date sau numere seriale, TRUE în 1, orice altceva în 0 (zero)"}, "NA": {"a": "()", "d": "Returnează valoarea de eroare #N/A (valoare indisponibilă)"}, "SHEET": {"a": "([valoare])", "d": "Returnează numărul de foaie al foii menționate"}, "SHEETS": {"a": "([referin<PERSON><PERSON>])", "d": "Returnează numărul de foi dintr-o referință"}, "TYPE": {"a": "(valoare)", "d": "Returnează un întreg care reprezintă tipul de dată al unei valori: număr = 1; text = 2; valoare logică = 4; valoare de eroare = 16; matrice = 64; date compuse = 128"}, "AND": {"a": "(logic1; [logic2]; ...)", "d": "Verifică dacă toate argumentele sunt TRUE și întoarce TRUE dacă toate argumentele sunt TRUE"}, "FALSE": {"a": "()", "d": "Returnează valoarea logică FALSE"}, "IF": {"a": "(test_logic; [valoare_dac<PERSON>_adevărat]; [valoare_dacă_fals])", "d": "Verifică dacă este îndeplinită o condiție și returnează o valoare dacă aceasta este TRUE și altă valoare dacă este FALSE"}, "IFS": {"a": "(test_logic; valoare_dacă_adevărat; ...)", "d": "Verifică dacă sunt îndeplinite una sau mai multe condiții și returnează o valoare care corespunde primei condiții TRUE"}, "IFERROR": {"a": "(valoare; valoare_dacă_eroare)", "d": "Return<PERSON>z<PERSON> valoare_dacă_eroare dacă expresia este o eroare și valoarea expresiei în sine nu este o eroare"}, "IFNA": {"a": "(valoare; valoare_dacă_na)", "d": "Returnează valoarea pe care o specificați dacă expresia se rezolvă la #N/A, altfel, returnează rezultatul expresiei"}, "NOT": {"a": "(logic)", "d": "Modifică FALSE în TRUE sau TRUE în FALSE"}, "OR": {"a": "(logic1; [logic2]; ...)", "d": "Verifică dacă există argumente TRUE și întoarce TRUE sau FALSE. Returnează FALSE numai dacă toate argumentele sunt FALSE"}, "SWITCH": {"a": "(expresie; valoare1; rezultat1; [implicit_sau_valoare2]; [rezultat2]; ...)", "d": "Evaluează o expresie în raport cu o listă de valori și returnează rezultatul corespunzător valorii primei potriviri. Dacă nu există nicio potrivire, returnează o valoare opțională implicită"}, "TRUE": {"a": "()", "d": "Returnează valoarea logică TRUE"}, "XOR": {"a": "(logic1; [logic2]; ...)", "d": "Returnează un „Sau exclusiv” logic al tuturor argumentelor"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnează textul care este înainte de caracterele de delimitare."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnează textul care este după caracterele de delimitare."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Scindează textul în rânduri sau coloane utilizând delimitatori."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Încadrează un vector de rând sau de coloană după un număr specificat de valori."}, "VSTACK": {"a": "(matrice1, [matrice2], ...)", "d": " Stivuiește pe verticală matricele într-o singură matrice."}, "HSTACK": {"a": "(matrice1, [matrice2], ...)", "d": " Stivuiește pe orizontală matricele într-o singură matrice."}, "CHOOSEROWS": {"a": "(matrice, row_num1, [row_num2], ...)", "d": " Returnează rânduri dintr-o matrice sau referință."}, "CHOOSECOLS": {"a": "(matrice, col_num1, [col_num2], ...)", "d": " Returnează coloane dintr-o matrice sau referință."}, "TOCOL": {"a": "(matrice, [ignorare], [scan_by_column])", "d": " Returnează matricea ca o singură coloană."}, "TOROW": {"a": "(matrice, [ignorare], [scanare_dup<PERSON>_coloană])", "d": " Returnează matricea ca un singur rând."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Încadrează un vector de rând sau de coloană după un număr specificat de valori."}, "TAKE": {"a": "(matrice, r<PERSON><PERSON><PERSON>, [coloane])", "d": " Returnează rânduri sau coloane de la începutul sau sfârșitul matricei."}, "DROP": {"a": "(matrice, r<PERSON><PERSON><PERSON>, [coloane])", "d": " Elimină rânduri sau coloane de la începutul sau sfârșitul matricei."}}