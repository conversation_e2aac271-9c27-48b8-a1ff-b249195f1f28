﻿<!DOCTYPE html>
<html>
	<head>
		<title>Onglet Insertion</title>
		<meta charset="utf-8" />
        <meta name="description" content="Présentation de l'interface utilisateur de l''Éditeur de Présentations - l'onglet Insertion" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Onglet Insertion</h1>
            <p>L'onglet <B>Insertion</B> dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Présentations</b></a> permet d'ajouter des objets visuels et des commentaires dans votre présentation.</p>
            <div class="onlineDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Présentations en ligne :</p>
                <p><img alt="Onglet Insertion" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Présentations de bureau :</p>
                <p><img alt="Onglet Insertion" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>Sous cet onglet vous pouvez</p>
            <ul>
                <li>insérer des <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tableaux</a>,</li>
                <li>insérer des <a href="../UsageInstructions/InsertText.htm" onclick="onhyperlinkclick(this)">zones de texte et des objets Text Art</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">des images</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)"> des formes</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">des graphiques</a>,</li>
                <li>insérer des <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">commentaires</a> et des <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">liens hypertexte</a>,</li>
                <li>insérer des <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">pieds de page, de la date et l'heure, du numéro de diapositive.</a>.</li>
                <li>insérer des <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">équations</a>, <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">symboles</a>,</li>
                <li class="desktopDocumentFeatures">ajouter des enregistrements audio et vidéo stockés sur votre ordinateur (disponible en <I>version de bureau</I> seulement, n'est pas disponible sur Mac OS),
                    <p class="note">
                        <b>Remarque</b> : Il est nécessaire d'installer un pack de codec permettant de lire les fichiers vidéos, par exemple, <B>K-Lite</B>.
                    </p>
                </li>
            </ul>
		</div>
	</body>
</html>