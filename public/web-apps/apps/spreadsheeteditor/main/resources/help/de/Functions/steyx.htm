﻿<!DOCTYPE html>
<html>
	<head>
		<title>STFEHLERYX-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>STFEHLERYX-Funktion</h1>
			<p>Die Funktion <b>STFEHLERYX</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den Standardfehler der geschätzten y-Werte für alle x-Werte der Regression zurück.</p>
			<p>Die Formelsyntax der Funktion <b>STFEHLERYX</b>:</p> 
			<p style="text-indent: 150px;"><b><em>STFEHLERYX(Y_Werte;X_Werte)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Y_Werte</em></b> ist eine Matrix oder ein Bereich abhängiger Datenpunkte.</p> 
				<p style="text-indent: 50px;"><b><em>X_Werte</em></b> ist eine Matrix oder ein Bereich unabhängiger Datenpunkte.</p>
				<p>Die Datenpunkte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen. Leere Zellen, Wahrheitswerte, Texte oder Fehlerwerte in Zellenbezügen oder Matrizen werden ignoriert. Wahrheitswerte und Zahlen in Textform, die Sie direkt in die Liste der Argumente eingeben, werden berücksichtigt.</p>
				<p class="note"> die <b><em>Y_Werte</em></b> und <b><em>X_Werte</em></b> müssen die gleiche Anzahl an Datenpukten enthalten, ansonsten gibt die Funktion den Fehlerwert <b>#NV</b> zurück.</p> 
			<p>Anwendung der Funktion <b>STFEHLERYX</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>STFEHLERYX</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="STFEHLERYX-Funktion" src="../images/steyx.png" /></p>
		</div>
	</body>
</html>