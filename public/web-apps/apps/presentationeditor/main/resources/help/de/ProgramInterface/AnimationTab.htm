﻿	<!DOCTYPE html>
	<html>
	<head>
		<title>Registerkarte Animation</title>
		<meta charset="utf-8" />
		<meta name="description" content="Benutzeroberfläche - Registerkarte Animation - Präsentationseditor" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<script type="text/javascript" src="../callback.js"></script>
		<script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Registerkarte Animation</h1>
			<p>
				Die Registerkarte <b>Animation</b> im <a href="https://www.onlyoffice.com/presentation-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> ermöglicht Ihnen die Verwaltung von Animationseffekten. Sie können Animationseffekte hinzufügen, bestimmen, wie sich die Animationseffekte bewegen, und andere Parameter für Animationseffekte konfigurieren, um Ihre Präsentation anzupassen.
			</p>
			<div class="onlineDocumentFeatures">
				<p>Dialogbox Online-Präsentationseditor:</p>
				<p><img alt="Registerkarte Animation" src="../images/interface/animationtab.png" /></p>
			</div>
			<div class="desktopDocumentFeatures">
				<p>Dialogbox Desktop-Präsentationseditor:</p>
				<p><img alt="Registerkarte Animation" src="../images/interface/desktop_animationtab.png" /></p>
			</div>
			<p>Sie können:</p>
            <ul>
                <li><a href="../UsageInstructions/AddingAnimations.htm#applyanimation">Animationseffekte</a> auswählen,</li>
				<li>für jeden Animationseffekt die geeigneten <b>Bewegungsparameter</b> festlegen,</li>
				<li><a href="../UsageInstructions/AddingAnimations.htm#multipleanimations">mehrere Animationen</a> hinzufügen,</li>
				<li>die <a href="../UsageInstructions/AddingAnimations.htm#animationsorder">Reihenfolge</a> der Animationseffekte mit den Optionen <b>Aufwärts schweben</b> oder <b>Abwärts schweben</b> ändern,</li>
				<li><b>Vorschau</b> des Animationseffekts aktivieren,</li>
                <li>die <a href="../UsageInstructions/AddingAnimations.htm#timing">Timing-Optionen</a> wie <b>Dauer</b>, <b>Verzögern</b> und <b>Wiederholen</b> für Animationseffekte festlegen,</li>
				<li>die Option <b>Zurückspulen</b> aktivieren und deaktivieren.</li>
            </ul>
		</div>
	</body>
</html>