﻿<!DOCTYPE html>
<html>
	<head>
		<title>WERT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WERT-Funktion</h1>
			<p>Die Funktion <b>WERT</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie wandelt einen für eine Zahl stehenden Text in eine Zahl um. Handelt es sich bei dem zu konvertierenden Text nicht um eine Zahl, gibt die Funktion den Fehlerwert <b>#WERT!</b> zurück.</p>
			<p>Die Formelsyntax der Funktion <b>WERT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>WERT(Text)</em></b></p> 
			<p><b><em>Text</em></b> gibt den Text bzw. einen entsprechenden Zellbezug an, der in eine Zahl umgewandelt werden soll.</p>
			<p>Anwendung der Funktion <b>WERT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WERT</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="WERT-Funktion" src="../images/value.png" /></p>
		</div>
	</body>
</html>