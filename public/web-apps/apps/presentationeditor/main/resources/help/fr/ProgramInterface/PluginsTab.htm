﻿<!DOCTYPE html>
<html>
	<head>
		<title>Onglet Modules complémentaires</title>
		<meta charset="utf-8" />
        <meta name="description" content="Présentation de l'interface utilisateur de l'Éditeur de Présentations - l'onglet Modules complémentaires" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Onglet Modules complémentaires</h1>
            <p>L'onglet <b>Modules complémentaires</b> dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Présentations</b></a> permet d'accéder à des fonctions d'édition avancées à l'aide de composants tiers disponibles. Ici vous pouvez également utiliser des macros pour simplifier les opérations de routine.</p>
            <div class="onlineDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Présentations en ligne :</p>
                <p><img alt="Onglet Modules complémentaires" src="../images/interface/pluginstab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Présentations de bureau :</p>
                <p><img alt="Onglet Modules complémentaires" src="../images/interface/desktop_pluginstab.png" /></p>
            </div>
            <p class="desktopDocumentFeatures">Le bouton<b> Paramètres</b> permet d'ouvrir la fenêtre où vous pouvez visualiser et gérer toutes les extensions installées et ajouter vos propres modules.</p>
            <p>Le bouton <b>Macros</b> permet d'ouvrir la fenêtre où vous pouvez créer vos propres macros et les exécuter. Pour en savoir plus sur les macros, veuillez vous référer à la <a target="_blank" href="https://api.onlyoffice.com/plugin/macros" onclick="onhyperlinkclick(this)">documentation de notre API</a>.</p>
            <p>Actuellement, les modules suivants sont disponibles :</p>
            <ul>
                <li class="desktopDocumentFeatures"><b>Envoyer</b> permet d'envoyer la présentation par e-mail à l'aide d'un client de messagerie installé sur votre ordinateur (disponible en <I>version de bureau</I> seulement),</li>
                <li><a href="../UsageInstructions/HighlightedCode.htm" onclick="onhyperlinkclick(this)">Code en surbrillance</a> sert à surligner la syntaxe du code en sélectionnant la langue, le style, la couleur de fond approprié etc.,</li>
                <li><a href="../UsageInstructions/PhotoEditor.htm" onclick="onhyperlinkclick(this)">Éditeur de photos</a> sert à modifier les images : rogner, retourner, pivoter, dessiner les lignes et le formes, ajouter des icônes et du texte, charger l'image de masque et appliquer des filtres comme Niveaux de gris, Inverser, Sépia, Flou, Embosser, Affûter etc.,</li>
                <li><a href="../UsageInstructions/Thesaurus.htm" onclick="onhyperlinkclick(this)">Thésaurus</a> sert à trouver les synonymes et les antonymes et les utiliser à remplacer le mot sélectionné,</li>
                <li><a href="../UsageInstructions/Translator.htm" onclick="onhyperlinkclick(this)">Traducteur</a> sert à traduire le texte sélectionné dans des langues disponibles,
                    <p class="note"><b>Remarque</b> : ce module complémentaire ne fonctionne pas sur Internet Explorer.</p>
                </li>
                <li><a href="../UsageInstructions/YouTube.htm" onclick="onhyperlinkclick(this)">You Tube</a> permet d'ajouter les vidéos YouTube dans votre présentation.</li>
            </ul>
            <p>Pour en savoir plus sur les modules complémentaires, veuillez vous référer à la <a target="_blank" href="https://api.onlyoffice.com/plugin/basic" onclick="onhyperlinkclick(this)">documentation de notre API</a>. Tous les exemples de modules open source actuels sont disponibles sur <a target="_blank" href="https://github.com/ONLYOFFICE/sdkjs-plugins" onclick="onhyperlinkclick(this)">GitHub</a>.</p>
		</div>
	</body>
</html>