{"Common.Controllers.Chat.notcriticalErrorTitle": "Xəbərdarlıq", "Common.Controllers.Chat.textEnterMessage": "Mesajınızı buraya daxil edin", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "Bağla", "Common.Controllers.ExternalDiagramEditor.warningText": "Obyekt deaktiv edilib, <PERSON><PERSON><PERSON><PERSON> o, başqa istifadəçi tərəfindən redaktə olunur.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Xəbərdarlıq", "Common.define.chartData.textArea": "Sahə", "Common.define.chartData.textAreaStacked": "<PERSON>s<PERSON><PERSON><PERSON>i sahə", "Common.define.chartData.textAreaStackedPer": "100% Qruplaşmış Sahə", "Common.define.chartData.textBar": "Zolaq", "Common.define.chartData.textBarNormal": "Qruplaşdırılmış sütun", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Qruplaşmış sütun", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON>", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tun", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Nisbətli Sütun", "Common.define.chartData.textBarStackedPer": "100% Nisbətli sütun", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Nisbətli s<PERSON>tun", "Common.define.chartData.textCharts": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Nisbətli sahə-qruplaşmış sütun", "Common.define.chartData.textComboBarLine": "Qruplaşdırılm<PERSON>ş sütun - sətir", "Common.define.chartData.textComboBarLineSecondary": "İkinci oxda qruplaşmış sütun - sətir", "Common.define.chartData.textComboCustom": "Fərdi kombinasiya", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Qruplaşmış Zolaq", "Common.define.chartData.textHBarNormal3d": "3D Qruplaşmış Zolaq", "Common.define.chartData.textHBarStacked": "Nisbətli zolaq", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Nisbətli Zolaq", "Common.define.chartData.textHBarStackedPer": "100% Nisbətli zolaq", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Nisbətli zolaq", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLineMarker": "<PERSON><PERSON><PERSON><PERSON><PERSON> ilə Qrafik xətt", "Common.define.chartData.textLineStacked": "Nisbətli xətt", "Common.define.chartData.textLineStackedMarker": "<PERSON><PERSON><PERSON><PERSON>r ilə nisbətli xətt", "Common.define.chartData.textLineStackedPer": "100% Nisbətli xətt", "Common.define.chartData.textLineStackedPerMarker": "Markerlərlə 100% Nisbətli xətt", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textPie3d": "3-<PERSON> diaqramı", "Common.define.chartData.textPoint": "XY (Nöqtəvi)", "Common.define.chartData.textScatter": "Nöq<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON> xətlər ilə <PERSON>öq<PERSON>ə<PERSON>", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON><PERSON> xətlər və markerlər ilə Nöqtəvi", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON> xətlər ilə <PERSON>öq<PERSON>ə<PERSON>", "Common.define.chartData.textScatterSmoothMarker": "<PERSON>ar xətlər və markerlər ilə Nöqtəvi", "Common.define.chartData.textStock": "Ehtiyat", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON>", "Common.Translation.warnFileLocked": "<PERSON><PERSON> başqa proqramda redaktə olunur. Siz redaktə etməyə davam edə və onu surət kimi saxlaya bilərsiniz.", "Common.Translation.warnFileLockedBtnEdit": "Kopyasını yaradın", "Common.Translation.warnFileLockedBtnView": "Baxmaq üçün açın", "Common.UI.ButtonColored.textAutoColor": "Avtomatik", "Common.UI.ButtonColored.textNewColor": "Yeni Fərdi Rəng Əlavə Edin", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "Common.UI.ComboDataView.emptyComboText": "Üslub yoxdur", "Common.UI.ExtendedColorDialog.addButtonText": "Əlavə et", "Common.UI.ExtendedColorDialog.textCurrent": "Hazırki", "Common.UI.ExtendedColorDialog.textHexErr": "Daxil edilmiş dəyər yanlışdır.<br>000000 və FFFFFF arasında dəyər daxil edin.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Daxil edilmiş dəyər yanlışdır.<br>0 və 255 arasında rəqəmsal dəyər daxil edin.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON> yoxdur", "Common.UI.SearchDialog.textHighlight": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textMatchCase": "Böyük-kiçik hərflərə diqqət", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> edilmiş mətni daxil edin", "Common.UI.SearchDialog.textSearchStart": "Mətninizi buraya daxil edin", "Common.UI.SearchDialog.textTitle": "Tapın və Əvəz edin", "Common.UI.SearchDialog.textTitle2": "Tap", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON>z tam sözlər", "Common.UI.SearchDialog.txtBtnHideReplace": "Əv<PERSON>zet<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON>vəz edin", "Common.UI.SearchDialog.txtBtnReplaceAll": "Hamısını Əvəz edin", "Common.UI.SynchronizeTip.textDontShow": "Bu mesajı bir daha göstərmə", "Common.UI.SynchronizeTip.textSynchronize": "<PERSON>ənəd başqa istifadəçi tərəfindən dəyişdirilib.<br>Dəyişikliklərinizi yadda saxlamaq və yeniləmələri yenidən yükləmək üçün klikləyin.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klassik İşıq", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Açıq", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON>ğ<PERSON> et", "Common.UI.Window.closeButtonText": "Bağla", "Common.UI.Window.noButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Təsdiq", "Common.UI.Window.textDontShow": "Bu mesajı bir daha göstərmə", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "İnformasiya", "Common.UI.Window.textWarning": "Xəbərdarlıq", "Common.UI.Window.yesButtonText": "<PERSON><PERSON><PERSON>", "Common.Utils.Metric.txtCm": "sm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "ünvan:", "Common.Views.About.txtLicensee": "LİSENZİYA", "Common.Views.About.txtLicensor": "Lisenziyaçı", "Common.Views.About.txtMail": "e-poçt:", "Common.Views.About.txtPoweredBy": "Dəstəklənib:", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Əlavə et", "Common.Views.AutoCorrectDialog.textApplyText": "Yazdıqca Tətbiq Edin", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Avtomatik mətn korreksiyası", "Common.Views.AutoCorrectDialog.textAutoFormat": "Yazdıqca AvtomaFormat edin", "Common.Views.AutoCorrectDialog.textBulleted": "Avtomatik markerli siyahılar", "Common.Views.AutoCorrectDialog.textBy": ":", "Common.Views.AutoCorrectDialog.textDelete": "Sil", "Common.Views.AutoCorrectDialog.textFLSentence": "Cümlənin ilk hərfini böyük hərflə yaz", "Common.Views.AutoCorrectDialog.textHyperlink": "Hiperlinklərlə İnternet və şəbəkə yolları", "Common.Views.AutoCorrectDialog.textHyphens": "tire (—) ilə defis (--)", "Common.Views.AutoCorrectDialog.textMathCorrect": "R<PERSON>zi Avto<PERSON>", "Common.Views.AutoCorrectDialog.textNumbered": "Avtomatik nömrəli <PERSON>", "Common.Views.AutoCorrectDialog.textQuotes": "\"<PERSON>üz dırnaq işarəsi\" ilə \"ağıllı dırnaq\"", "Common.Views.AutoCorrectDialog.textRecognized": "Tanınmış Funksiyalar", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Aşağıdakı ifadələr tanınan riyazi ifadələrdir. Onlar avtomatik kursivləşdirilməyəcək.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON>vəz edin", "Common.Views.AutoCorrectDialog.textReplaceText": "Yazdıqca Əvəz edin", "Common.Views.AutoCorrectDialog.textReplaceType": "Yaz<PERSON><PERSON>qca mətni əvəz edin", "Common.Views.AutoCorrectDialog.textReset": "Sıfırla", "Common.Views.AutoCorrectDialog.textResetAll": "Defolta Sıfırla", "Common.Views.AutoCorrectDialog.textRestore": "Bərpa et", "Common.Views.AutoCorrectDialog.textTitle": "AvtoDüzəliş", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Tanınmış funksiyalar yalnız A-dan Z, böyük və ya kiçik hərflərdən ibarət olmalıdır.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON>ə etdiyiniz hər hansı ifadə silinəcək və sildiyiniz ifadə bərpa olunacaq. Davam etmək istəyirsiniz?", "Common.Views.AutoCorrectDialog.warnReplace": "1% üçün avtomatik düzəliş girişi artıq mövcuddur. Onu əvəz etmək istəyirsiniz?", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON>lavə etdiyiniz hər hansı avtodüzəlişlər silinəcək və dəyişdirdiyiniz avtomatik düzəlişlər orijinal dəyərlərinə sıfırlanacaq. Davam etmək istəyirsiniz?", "Common.Views.AutoCorrectDialog.warnRestore": "1% üçün avtomatik düzəliş girişi orijinal dəyərinə sıfırlanacaq. Davam etmək istəyirsiniz?", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> A-dan Z-ə", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>ya", "Common.Views.Comments.mniDateAsc": "Ən köhnə", "Common.Views.Comments.mniDateDesc": "<PERSON>n yeni", "Common.Views.Comments.mniPositionAsc": "Yu<PERSON>r<PERSON>dan", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "Əlavə et", "Common.Views.Comments.textAddComment": "Şərh yaz", "Common.Views.Comments.textAddCommentToDoc": "Sənədə şərh əlavə et", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON> əlavə edin", "Common.Views.Comments.textAnonym": "Qonaq", "Common.Views.Comments.textCancel": "<PERSON><PERSON>ğ<PERSON> et", "Common.Views.Comments.textClose": "Bağla", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>", "Common.Views.Comments.textComments": "Şə<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Şərhinizi buraya daxil edin", "Common.Views.Comments.textHintAddComment": "Şərh yaz", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON> ver", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON> edin", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CopyWarningDialog.textDontShow": "Bu mesajı bir daha göstərmə", "Common.Views.CopyWarningDialog.textMsg": "Alətlər paneli düymələrindən istifadə edərək və yalnız bu tabdakı kontekst menyusundan istifadə edərək kopyalama, kəsmə və yapışdırma əməliyyatlarını yerinə yetirə bilərsiniz. <br> <br> Redaktor tabından kənar proqramları köçürmək və ya yapışdırmaq üçün aşağıdakı düymə kombinasiyalarından istifadə edin:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>, Kəsm<PERSON> və Yapışdırma Əməliyyatları", "Common.Views.CopyWarningDialog.textToCopy": "Kopyalama üçün", "Common.Views.CopyWarningDialog.textToCut": "Kəsmək üçün", "Common.Views.CopyWarningDialog.textToPaste": "Yerləşdirmək üçün", "Common.Views.DocumentAccessDialog.textLoading": "Yüklənir...", "Common.Views.DocumentAccessDialog.textTitle": "Paylaşma <PERSON>rlə<PERSON>", "Common.Views.ExternalDiagramEditor.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.Header.labelCoUsersDescr": "Faylı redaktə edən istifadəçilər:", "Common.Views.Header.textAddFavorite": "<PERSON><PERSON><PERSON><PERSON> kimi işarələ", "Common.Views.Header.textAdvSettings": "Qabaqcıl Parametrlər", "Common.Views.Header.textBack": "<PERSON><PERSON> a<PERSON>", "Common.Views.Header.textCompactView": "<PERSON><PERSON><PERSON><PERSON><PERSON> gizlədin", "Common.Views.Header.textHideLines": "Xətkeşi gizlət", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>t", "Common.Views.Header.textHideStatusBar": "Status panelini gizlədin", "Common.Views.Header.textRemoveFavorite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n silin", "Common.Views.Header.textSaveBegin": "<PERSON><PERSON><PERSON>...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>ər yadda sa<PERSON>lıb", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>ər yadda sa<PERSON>lıb", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "Common.Views.Header.tipAccessRights": "Sənə<PERSON>ə giriş hüquqlarını idarə edin", "Common.Views.Header.tipDownload": "Faylı endirin", "Common.Views.Header.tipGoEdit": "Cari faylı redaktə edin", "Common.Views.Header.tipPrint": "Faylı çap edin", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON> edin", "Common.Views.Header.tipSave": "<PERSON><PERSON>a saxla", "Common.Views.Header.tipUndo": "<PERSON><PERSON>", "Common.Views.Header.tipUndock": "Ayrı pəncərəyə çıxarın", "Common.Views.Header.tipViewSettings": "Görünüş parametrləri", "Common.Views.Header.tipViewUsers": "İstifadəçilərə baxın və sənədə giriş hüquqlarını idarə edin", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON>ü<PERSON>ını dəyiş", "Common.Views.Header.txtRename": "Adını dəyiş", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON> qapat", "Common.Views.History.textHide": "Y<PERSON>ğcamlaşdır", "Common.Views.History.textHideAll": "Ətraflı dəyişik<PERSON><PERSON><PERSON><PERSON> giz<PERSON>ədin", "Common.Views.History.textRestore": "Bərpa et", "Common.Views.History.textShow": "Genişləndir", "Common.Views.History.textShowAll": "Detallı dəyişik<PERSON>l<PERSON><PERSON>", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Təsvir URL-ni yerləşdirin:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Bu sahə tələb olunur", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Bu sahə \"http://www.example.com\" formatında URL olmalıdır", "Common.Views.InsertTableDialog.textInvalidRowsCols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sətir və sütun cəmini göstərin.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON> sayı", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON>u sahə üçün maksimum dəyər {0}-dir.", "Common.Views.InsertTableDialog.txtMinText": "Bu sahə üçün minimum dəyər {0}-dir.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayı", "Common.Views.InsertTableDialog.txtTitle": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> Ö<PERSON>çü<PERSON>ü", "Common.Views.InsertTableDialog.txtTitleSplit": "Bölünmüş xana", "Common.Views.LanguageDialog.labelSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> se<PERSON>", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textNumbering": "Nömrələnmiş", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "Yeni marker", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "mətnin %-i", "Common.Views.ListSettingsDialog.txtSize": "Ölçü", "Common.Views.ListSettingsDialog.txtStart": "Başlayın", "Common.Views.ListSettingsDialog.txtSymbol": "Simvol", "Common.Views.ListSettingsDialog.txtTitle": "Siyahı Parametrləri", "Common.Views.ListSettingsDialog.txtType": "Növ", "Common.Views.OpenDialog.closeButtonText": "Faylı Bağla", "Common.Views.OpenDialog.txtEncoding": "Kodlaşdırma", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON> s<PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Faylı açmaq üçün parol daxil edin", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Şifrəni daxil edib faylı açdıqdan sonra faylın cari parolu sıfırlanacaq.", "Common.Views.OpenDialog.txtTitle": "Seçimlərin %1-ni seçin", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtDescription": "Bu sənədi qorumaq üçün parol təyin edin", "Common.Views.PasswordDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> parolu eyni deyil", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON> et", "Common.Views.PasswordDialog.txtWarning": "Xəbərdarlıq: Əgər parolu itirsəniz və ya unutsanız, onu bərpa etmək mümkün olmayacaq. Zə<PERSON>ət olmasa onu təhlükəsiz yerdə saxlayın.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "Qoşmalar", "Common.Views.Plugins.strPlugins": "Qoşmalar", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Başla", "Common.Views.Plugins.textStop": "Dayandır", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON> ilə ş<PERSON>ə<PERSON>əyin", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> də<PERSON> və ya sil", "Common.Views.Protection.hintSignature": "<PERSON>ə<PERSON>ə<PERSON>al imza və ya imza sətri əlavə edin", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON> ə<PERSON>ə edin", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON> sil", "Common.Views.Protection.txtEncrypt": "Şifrələ", "Common.Views.Protection.txtInvisibleSignature": "Rə<PERSON><PERSON><PERSON>al imza əlavə et", "Common.Views.Protection.txtSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON>za sətri əlavə edin", "Common.Views.RenameDialog.textName": "<PERSON><PERSON> adı", "Common.Views.RenameDialog.txtInvalidName": "<PERSON><PERSON> adında a<PERSON>ğıdakı simvollar ola bilməz:", "Common.Views.ReviewChanges.hintNext": "Növbəti də<PERSON>ş<PERSON>yə", "Common.Views.ReviewChanges.hintPrev": "Əvvəlki dəyişikliyə", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Real vaxtda birgə redaktə. Bütün dəyişikliklər avtomatik olaraq yadda saxlanılır.", "Common.Views.ReviewChanges.strStrict": "Mə<PERSON><PERSON>dlaşdır", "Common.Views.ReviewChanges.strStrictDesc": "Sizin və başqalarının etdiyi dəyişiklikləri sinxronlaşdırmaq üçün \"Saxla\" düyməsindən istifadə edin.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON> də<PERSON> qəbul et", "Common.Views.ReviewChanges.tipCoAuthMode": "Birgə redaktə rejimini təyin edin", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> həll edin", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON> şə<PERSON><PERSON><PERSON>ri həll edin", "Common.Views.ReviewChanges.tipHistory": "<PERSON>ersiya tarixçəsini <PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON> də<PERSON>yi rədd edin", "Common.Views.ReviewChanges.tipReview": "Dəyişik<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Dəyişik<PERSON><PERSON><PERSON><PERSON> göstəril<PERSON><PERSON><PERSON>i istədiyiniz rejimi seçin", "Common.Views.ReviewChanges.tipSetDocLang": "<PERSON>ə<PERSON><PERSON>d dilini təyin edin", "Common.Views.ReviewChanges.tipSetSpelling": "Orfoqrafiyanın <PERSON>", "Common.Views.ReviewChanges.tipSharing": "Sənə<PERSON>ə giriş hüquqlarını idarə edin", "Common.Views.ReviewChanges.txtAccept": "<PERSON>ə<PERSON> et", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> qəbul edin", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qəbul et", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON> də<PERSON> qəbul et", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Bağla", "Common.Views.ReviewChanges.txtCoAuthMode": "Birgə redaktə Rejimi", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON>i <PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemove": "Yığışdır", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON> edin", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON> şə<PERSON><PERSON><PERSON>ri həll edin", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Həll edin", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "<PERSON>i Şərhlə<PERSON> Həll edin", "Common.Views.ReviewChanges.txtDocLang": "Dil", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> qəbul edildi (Ön baxış)", "Common.Views.ReviewChanges.txtFinalCap": "Son", "Common.Views.ReviewChanges.txtHistory": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> (Redaktə)", "Common.Views.ReviewChanges.txtMarkupCap": "Düzəlişlər", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> rədd <PERSON>il<PERSON> (Ön baxış)", "Common.Views.ReviewChanges.txtOriginalCap": "Orijinal", "Common.Views.ReviewChanges.txtPrev": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON> edin", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>in", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rədd edin", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON> də<PERSON>yi rədd edin", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Orfoqrafiyanın <PERSON>", "Common.Views.ReviewChanges.txtTurnon": "Dəyişik<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON> rejimi", "Common.Views.ReviewPopover.textAdd": "Əlavə et", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON> əlavə edin", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON>ğ<PERSON> et", "Common.Views.ReviewPopover.textClose": "Bağla", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+ qeyd sənədə girişi təmin edəcək və e-poçt göndərəcək", "Common.Views.ReviewPopover.textMentionNotify": "+ qeyd e-mail vasitəsilə istifadəçiyə bildiriş verəcək", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON> ver", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON> edin", "Common.Views.ReviewPopover.txtDeleteTip": "Sil", "Common.Views.ReviewPopover.txtEditTip": "Redaktə et", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Yadda sax<PERSON>a <PERSON><PERSON> qovluq", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "Veri<PERSON>ən<PERSON>ər mənbəyini seçin", "Common.Views.SignDialog.textBold": "Qalın", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "İmzalayanın adını daxil edin", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "İmzalayanın adı boş o<PERSON>malıdır", "Common.Views.SignDialog.textPurpose": "<PERSON>u sənədin im<PERSON>ının məqsədi", "Common.Views.SignDialog.textSelect": "Seç", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "<PERSON><PERSON><PERSON> kimi <PERSON>", "Common.Views.SignDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textUseImage": "və ya şəkili imza kimi istifadə etmək üçün \"Şəkil Seçin\" hissəsinin üzərinə klikləyin", "Common.Views.SignDialog.textValid": "%1 ilə %2 arasında etibarlıdır", "Common.Views.SignDialog.tipFontName": "Şrift Adı", "Common.Views.SignDialog.tipFontSize": "Şrift Ölçüsü", "Common.Views.SignSettingsDialog.textAllowComment": "İmzalayana imza dialoq qutusuna şərh əlavə etmək icazəsi verin", "Common.Views.SignSettingsDialog.textInfoEmail": "E-poçt", "Common.Views.SignSettingsDialog.textInfoName": "Ad", "Common.Views.SignSettingsDialog.textInfoTitle": "İmzalayanın Adı", "Common.Views.SignSettingsDialog.textInstructions": "İmzalayan üçün tə<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON>za sətirində imza ta<PERSON> g<PERSON>ərin", "Common.Views.SignSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "Bu sahə tələb olunur", "Common.Views.SymbolTableDialog.textCharacter": "Simvol", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX dəyəri", "Common.Views.SymbolTableDialog.textCopyright": "<PERSON><PERSON><PERSON><PERSON><PERSON> hü<PERSON>", "Common.Views.SymbolTableDialog.textDCQuote": "Cüt Dırnaq Bağlanır", "Common.Views.SymbolTableDialog.textDOQuote": "Qoşa Dırnaq Açılır", "Common.Views.SymbolTableDialog.textEllipsis": "Üfüqi Ellips", "Common.Views.SymbolTableDialog.textEmDash": "Uzun Tire", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "Tire", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textFont": "Şrift", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBSpace": "Kə<PERSON>lmənin olmadığı Boşluq", "Common.Views.SymbolTableDialog.textPilcrow": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 M Boşluğu", "Common.Views.SymbolTableDialog.textRange": "Aralıq", "Common.Views.SymbolTableDialog.textRecent": "Son zamanlarda istifadə olunmuş simvollar", "Common.Views.SymbolTableDialog.textRegistered": "Qeydiyyatdan keçmə işarəsi", "Common.Views.SymbolTableDialog.textSCQuote": "Cüt Dırnaq Bağlanır", "Common.Views.SymbolTableDialog.textSection": "Bölmə İşarəsi", "Common.Views.SymbolTableDialog.textShortcut": "Qısayol dü<PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Tək Dırnaq Açılır", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON><PERSON> simvo<PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simvollar", "Common.Views.SymbolTableDialog.textTitle": "Simvol", "Common.Views.SymbolTableDialog.textTradeMark": "Ticarət nişanı simvolu", "Common.Views.UserNameDialog.textDontShow": "<PERSON>ir daha soruşma<PERSON>ın", "Common.Views.UserNameDialog.textLabel": "Etiket:", "Common.Views.UserNameDialog.textLabelError": "Etiket boş olmamalıdır.", "PE.Controllers.LeftMenu.leavePageText": "Bu sənəddə yadda saxlanmayan hər hansı dəyişikliklər silinəcək. <br> Onları yadda saxlamaq üçün \"Ləğv et\", sonra \"Yadda saxla\" üzərinə klikləyin. Yadda saxlanmamış dəyişiklikləri ləğv etmək üçün \"OK\" düyməsini basın.", "PE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON><PERSON> təqdimat", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Xəbərdarlıq", "PE.Controllers.LeftMenu.requestEditRightsText": "Dəyişiklik hüquqları tələbi...", "PE.Controllers.LeftMenu.textLoadHistory": "Versiya tarixçəsi yüklənir...", "PE.Controllers.LeftMenu.textNoTextFound": "Axtardığınız verilən tapılmadı. Axtarış seçimlərinizi tənzimləyin.", "PE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON>vəz edilir. {0} hal nəzər<PERSON> alınmadı.", "PE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {0}", "PE.Controllers.LeftMenu.txtUntitled": "Başlıqsız", "PE.Controllers.Main.applyChangesTextText": "Məlumat yüklənir...", "PE.Controllers.Main.applyChangesTitleText": "Məlumat Yüklənir", "PE.Controllers.Main.convertationTimeoutText": "Çevrilmə vaxtı maksimumu keçdi.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON>ə<PERSON><PERSON><PERSON> siyahı<PERSON>ına qayıtmaq üçün \"OK\" dü<PERSON><PERSON><PERSON><PERSON> basın.", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "Endirmə prosesi uğ<PERSON>uz oldu.", "PE.Controllers.Main.downloadTextText": "<PERSON>ə<PERSON><PERSON><PERSON> yü<PERSON>...", "PE.Controllers.Main.downloadTitleText": "<PERSON>ə<PERSON><PERSON><PERSON> yü<PERSON>ə<PERSON>", "PE.Controllers.Main.errorAccessDeny": "Hüquqlarınız olmayan əməliyyatı yerinə yetirməyə çalışırsınız.<br>Sənəd Serveri inzibatçınızla əlaqə saxlayın.", "PE.Controllers.Main.errorBadImageUrl": "Təsvir URL-i yanlışdır", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Server ba<PERSON><PERSON><PERSON><PERSON><PERSON> itdi. Sənədi hazırda redaktə etmək mümkün deyil.", "PE.Controllers.Main.errorComboSeries": "Birləşdirilmiş diaqram yaratmaq üçün ən azı iki məlumat seriyası seçin.", "PE.Controllers.Main.errorConnectToServer": "Sənədi saxlamaq mümkün olmadı. Lü<PERSON><PERSON>ə<PERSON>, əlaqə parametrlərini yoxlayın və ya inzibatçınızla əlaqə saxlayın.<br>'OK' düyməsini kliklədiyiniz zaman sizdən sənədi endirmək təklif olunacaq.", "PE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON>ci xəta. <br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bazasının bağlantı xətası. Xəta davam edərsə, dəstəklə əlaqə saxlayın.", "PE.Controllers.Main.errorDataEncrypted": "Şif<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dəyişik<PERSON>lər qəbul edildi, onları deşifrə etmək mümkün deyil.", "PE.Controllers.Main.errorDataRange": "Yanlış məlumat diapazonu.", "PE.Controllers.Main.errorDefaultMessage": "<PERSON><PERSON><PERSON> kodu: %1", "PE.Controllers.Main.errorEditingDownloadas": "Sən<PERSON><PERSON>ə işləyərkən xəta baş verdi. <br> <PERSON><PERSON><PERSON><PERSON> ehtiyat nüsxəsini kompüterinizin sərtt diskində saxlamaq üçün \"Kimi Endirin\" seçimindən istifadə edin.", "PE.Controllers.Main.errorEditingSaveas": "Sənədlə işləyərkən xəta baş verdi. <br> Yed<PERSON><PERSON> nüsxəsini kompüterinizin sərt diskində saxlamaq üçün \"... kimi yadda saxla\" seçimindən istifadə edin.", "PE.Controllers.Main.errorEmailClient": "<PERSON>ç bir e-poçt müştərisi tapılmadı.", "PE.Controllers.Main.errorFilePassProtect": "<PERSON>l parolla qorunur və onu açmaq mümkün de<PERSON>l.", "PE.Controllers.Main.errorFileSizeExceed": "Faylın ölçüsü serveriniz üçün təyin edilmiş məhdudiyyəti keçir.<br>Təfərrüatlar üçün Sənəd Serveri inzibatçınızla əlaqə saxlayın.", "PE.Controllers.Main.errorForceSave": "Faylı saxlayarkən xəta baş verdi. <PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, faylı kompüterinizin sərtt diskində saxlamaq üçün “... kimi endir” seçimindən istifadə edin və ya sonra yenidən cəhd edin.", "PE.Controllers.Main.errorKeyEncrypt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON> vaxtı keçib", "PE.Controllers.Main.errorLoadingFont": "Ş<PERSON><PERSON><PERSON>r yüklənməyib.<br> S<PERSON>nəd Serveri administratorunuzla əlaqə saxlayın.", "PE.Controllers.Main.errorProcessSaveResult": "Saxlama prosesi uğ<PERSON> oldu", "PE.Controllers.Main.errorServerVersion": "Redaktor versiyası yeniləndi. Dəyişiklikləri tətbiq etmək üçün səhifə yenidən yüklənəcək.", "PE.Controllers.Main.errorSessionAbsolute": "Sən<PERSON>din redaktə sessiyasının vaxtı bitdi. Səhifəni yenidən yükləyin.", "PE.Controllers.Main.errorSessionIdle": "Sənəd uzun müddətdir ki, redaktə olunmayıb. Səhifəni yenidən yükləyin.", "PE.Controllers.Main.errorSessionToken": "Server ilə əlaqə kəsildi. Səhifəni yenidən yükləyin.", "PE.Controllers.Main.errorSetPassword": "<PERSON><PERSON> təyin edilə bilmədi.", "PE.Controllers.Main.errorStockChart": "Yanlış sıra ardıcıllığı. Səhm qrafikini yaratmaq üçün məlumatları vərəqdə aşağıdakı ardıcıllıqla yerləşdirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti, maks<PERSON>um qiymət, minimum qiymət, ba<PERSON><PERSON><PERSON><PERSON> qiyməti.", "PE.Controllers.Main.errorToken": "<PERSON>ə<PERSON><PERSON><PERSON> təhlükəsizlik nişanı düzgün formalaşmayıb.<br>Sənəd Server inzibatçısı ilə əlaqə saxlayın.", "PE.Controllers.Main.errorTokenExpire": "<PERSON>ə<PERSON><PERSON><PERSON> təhlükəsizlik nişanının vaxtı bitib.<br> Sənəd Server inzibatçısı ilə əlaqə saxlayın.", "PE.Controllers.Main.errorUpdateVersion": "Fayl versiyası dəyişdirilib. Səhifə yenidən yüklənəcək.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "İnternet bağlantısı bərpa edildi və fayl versiyası dəyişdirildi.<br>İşə davam etməzdən əvvəl heç nəyin itirilmədiyindən əmin olmaq üçün faylı endirməli və ya onun məzmununu kopyalamalı, sonra bu səhifəni yenidən yükləməlisiniz.", "PE.Controllers.Main.errorUserDrop": "<PERSON><PERSON> ha<PERSON> daxil olmaq mü<PERSON>.", "PE.Controllers.Main.errorUsersExceed": "Qiymət planı ilə icazə verilən istifadəçilərin sayı maksimumu keçdi", "PE.Controllers.Main.errorViewerDisconnect": "Bağlantı kəsildi. Siz hələ də sənədə baxa bilərs<PERSON>z, lakin əlaqə yenidən qurulana və səhifə təzələnənə qədər onu endirə və ya çap edə bilməzsiniz.", "PE.Controllers.Main.leavePageText": "Bu təqdimatda saxlanmamış dəyişiklikləriniz var. Dəyişiklikləri saxlamaq üçün \"Bu səhifədə qalın\", sonra \"Saxla\" düyməsini basın. Yadda saxlanmamış dəyişiklikləri ləğv etmək üçün \"Bu Səhifədən Çıx\" düyməsini klikləyin.", "PE.Controllers.Main.leavePageTextOnClose": "Bütün yadda saxlanmamış dəyişikliklər silinəcək. <br> Onları yadda saxlamaq üçün \"Ləğv et\", sonra \"Yadda saxla\" üzərinə klikləyin. Bütün saxlanmamış dəyişiklikləri silmək üçün \"OK\" düyməsini basın.", "PE.Controllers.Main.loadFontsTextText": "Məlumat yüklənir...", "PE.Controllers.Main.loadFontsTitleText": "Məlumat Yüklənir", "PE.Controllers.Main.loadFontTextText": "Məlumat yüklənir...", "PE.Controllers.Main.loadFontTitleText": "Məlumat Yüklənir", "PE.Controllers.Main.loadImagesTextText": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON> yüklə<PERSON>...", "PE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadImageTextText": "<PERSON>ə<PERSON><PERSON> yük<PERSON>ə<PERSON>...", "PE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadingDocumentTextText": "Təq<PERSON>mat yklnir...", "PE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadThemeTextText": "<PERSON><PERSON><PERSON><PERSON> yüklənir...", "PE.Controllers.Main.loadThemeTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.notcriticalErrorTitle": "Xəbərdarlıq", "PE.Controllers.Main.openErrorText": "Faylı açan zaman xəta baş verdi.", "PE.Controllers.Main.openTextText": "<PERSON>ə<PERSON><PERSON><PERSON> a<PERSON>ılır...", "PE.Controllers.Main.openTitleText": "<PERSON><PERSON>q<PERSON><PERSON>ı<PERSON>", "PE.Controllers.Main.printTextText": "Təqdimat çap edilir...", "PE.Controllers.Main.printTitleText": "<PERSON>əq<PERSON>mat çap edilir", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n", "PE.Controllers.Main.requestEditFailedMessageText": "Kimsə bu təqdimatı redaktə edir. Zəhmət olmasa bir az sonra yenə cəhd edin.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> rədd edildi", "PE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> yadda saxlayarkən xəta baş verdi.", "PE.Controllers.Main.saveErrorTextDesktop": "Bu faylı saxlamaq və ya yaratmaq mümkün deyil.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> səbəblər: <br>1. <PERSON><PERSON> yalnız oxumaq üçündür. <br>2. <PERSON><PERSON> digər istifadəçilər tərəfindən redaktə olunur. <br>3. Disk doludur və ya xarabdır.", "PE.Controllers.Main.saveTextText": "<PERSON>ə<PERSON><PERSON><PERSON> yadda sa<PERSON>ı<PERSON>...", "PE.Controllers.Main.saveTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> yadda <PERSON>", "PE.Controllers.Main.scriptLoadError": "Bağlantı çox yavaşdır, bə<PERSON> komponent<PERSON><PERSON><PERSON> yükləmək mümkün deyil. Səhifəni yenidən yükləyin.", "PE.Controllers.Main.splitDividerErrorText": "Sətirlərin sayı %1-ə bölən olmalıdır.", "PE.Controllers.Main.splitMaxColsErrorText": "Sü<PERSON>ların sayı 1%-dən az olmalıdır.", "PE.Controllers.Main.splitMaxRowsErrorText": "Sə<PERSON>rl<PERSON>rin sayı 1%-dən az olmalıdır.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "<PERSON><PERSON><PERSON><PERSON>n tənliklərə tətbiq edin", "PE.Controllers.Main.textBuyNow": "<PERSON>eb <PERSON>ta daxil olun", "PE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>ər yadda sa<PERSON>lıb", "PE.Controllers.Main.textClose": "Bağla", "PE.Controllers.Main.textCloseTip": "İpucunu bağlamaq üçün k<PERSON>ləyin", "PE.Controllers.Main.textContactUs": "Satışlarla əlaqə", "PE.Controllers.Main.textConvertEquation": "Bu tənlik tənlik redaktorunun artıq mövcud olmayan köhnə versiyası ilə yaradılmışdır. Bu tənliyi dəyişmək üçün onu Office Math ML formatına çevirin. <br> İndi çevrilsin?", "PE.Controllers.Main.textCustomLoader": "Nə<PERSON><PERSON>rə alın ki, lisenziyanın şərtlərinə görə sizin yükləyicini dəyişmək hüququnuz yoxdur.<br>Qiymət almaq üçün Satış Departamentimizlə əlaqə saxlayın.", "PE.Controllers.Main.textDisconnect": "Bağlantı itib", "PE.Controllers.Main.textGuest": "Qonaq", "PE.Controllers.Main.textHasMacros": "Faylda avtomatik makrolar var.<br><PERSON><PERSON><PERSON><PERSON><PERSON> işə salmaq istəyirsiniz?", "PE.Controllers.Main.textLearnMore": "Ətraflı məlumat", "PE.Controllers.Main.textLoadingDocument": "Chargement de présentation", "PE.Controllers.Main.textLongName": "128 simvoldan az olan ad daxil edin.", "PE.Controllers.Main.textNoLicenseTitle": "Lisenziya limitinə çatdı", "PE.Controllers.Main.textPaidFeature": "Ödənişli funksiya", "PE.Controllers.Main.textRemember": "<PERSON><PERSON><PERSON><PERSON><PERSON> fayllar üçün seçimimi yadda saxla", "PE.Controllers.Main.textRenameError": "İstifadəçi adı boş olmamalıdır.", "PE.Controllers.Main.textRenameLabel": "Əməkdaşlıq üçün istifadə ediləcək ad daxil edin", "PE.Controllers.Main.textShape": "Forma", "PE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textText": "Mətn", "PE.Controllers.Main.textTryUndoRedo": "Qaytar / Təkrarla funksiyaları sürətli birgə redaktə rejimi üçün qeyri-aktiv edilib. <br> <PERSON>g<PERSON>r istifadəçilərin müdaxiləsi olmadan faylı redaktə etmək və fayllarınızı göndərmək üçün ciddi birgə redaktə rejiminə keçmək üçün \"Ciddi rejim\" düyməsinə klikləyin. Dəyişikliklər yalnız siz onları saxladıqdan sonra baş verir. Qabaqcıl redaktor parametrlərindən istifadə edərək birgə redaktə rejimləri arasında keçid edə bilərsiniz.", "PE.Controllers.Main.textTryUndoRedoWarn": "Qaytar/Təkrarla funksiyaları Sürətli birgə redaktə rejimi üçün qeyri-aktiv edilib.", "PE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> müddə<PERSON>di", "PE.Controllers.Main.titleServerVersion": "Redaktor <PERSON>", "PE.Controllers.Main.txtAddFirstSlide": "İlk slaydı əlavə etmək üçün klikləyin", "PE.Controllers.Main.txtAddNotes": "<PERSON><PERSON><PERSON>ər əlavə etmək üçün k<PERSON>ləyin", "PE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "Çıxarışlar", "PE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Tarix və saat", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtEditingMode": "Redaktə rejimini təyin edin...", "PE.Controllers.Main.txtErrorLoadHistory": "Tarixçəni yükləmək prosesi uğursuz oldu", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtFooter": "Alt-başlıq", "PE.Controllers.Main.txtHeader": "Başlıq", "PE.Controllers.Main.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLines": "Sə<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Yüklənir...", "PE.Controllers.Main.txtMath": "Riyaziyyat", "PE.Controllers.Main.txtMedia": "Media", "PE.Controllers.Main.txtNeedSynchronize": "Ye<PERSON>l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> var", "PE.Controllers.Main.txtNone": "<PERSON><PERSON>", "PE.Controllers.Main.txtPicture": "Şəkil", "PE.Controllers.Main.txtRectangles": "Düzbucaqlılar", "PE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Xətt Çıxarışı 1 (Sərhəd və Vurğu Zolağı)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Xətt Çıxarışı 2 (Sərhəd və Vurğu Zolağı)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Sətir Çıxarışı 3 (Sərhəd və Vurğu Zolağı)", "PE.Controllers.Main.txtShape_accentCallout1": "Xətt Çıxarışı 1 (Vurğu Zolağı)", "PE.Controllers.Main.txtShape_accentCallout2": "Xətt Çıxarışı 2 (Vurğu Zolağı)", "PE.Controllers.Main.txtShape_accentCallout3": "Sətir Çıxarışı 3 (Vurğu Zolağı)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Geri və ya Əvvəlki Düyməsi", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Başlama <PERSON>ü<PERSON>ə<PERSON>", "PE.Controllers.Main.txtShape_actionButtonBlank": "Boş Düymə", "PE.Controllers.Main.txtShape_actionButtonDocument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonEnd": "Bitmə Düyməsi", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON>rəli və ya Növbəti Düyməsi", "PE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonInformation": "İnformasiya <PERSON>", "PE.Controllers.Main.txtShape_actionButtonMovie": "Film Düyməsi", "PE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_arc": "<PERSON>ö<PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentArrow": "Əyilmiş Ox", "PE.Controllers.Main.txtShape_bentConnector5": "Dirsəkvari Birləşdirici", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Dirsəkvari Oxlu Birləşdirici", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Dirsəkvari İkili Ox Birləşdiricisi", "PE.Controllers.Main.txtShape_bentUpArrow": "Yuxarı Əyilmiş Ox", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Qövs Bloku", "PE.Controllers.Main.txtShape_borderCallout1": "Sətir Çıxarışı 1", "PE.Controllers.Main.txtShape_borderCallout2": "Sətir Çıxarışı 2", "PE.Controllers.Main.txtShape_borderCallout3": "Sətir Çıxarışı 3", "PE.Controllers.Main.txtShape_bracePair": "İkiqat Fiqurlu Mötərizə", "PE.Controllers.Main.txtShape_callout1": "Xətt Çıxarışı 1 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur)", "PE.Controllers.Main.txtShape_callout2": "Sətir Çıxarışı 2 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur)", "PE.Controllers.Main.txtShape_callout3": "Sətir Çıxarışı 3 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur)", "PE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "Dairə", "PE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cloud": "Bulud", "PE.Controllers.Main.txtShape_cloudCallout": "Bulud Formalı Çıxarış", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Əyri Birləşdirici", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Əyri Ox birləşdiricisi", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "İki oxlu Əyri Ox Birləşdiricisi", "PE.Controllers.Main.txtShape_curvedDownArrow": "Aşağı əyilmiş ox", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Sola Əyilmiş Ox", "PE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON>ğa Ə<PERSON>lmiş Ox", "PE.Controllers.Main.txtShape_curvedUpArrow": "Yuxarı Əyilmiş Ox", "PE.Controllers.Main.txtShape_decagon": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_diagStripe": "Diaqonal Zolağı", "PE.Controllers.Main.txtShape_diamond": "Romb", "PE.Controllers.Main.txtShape_dodecagon": "Onikibucaqlı", "PE.Controllers.Main.txtShape_donut": "Donut", "PE.Controllers.Main.txtShape_doubleWave": "İkiqat Dalğa", "PE.Controllers.Main.txtShape_downArrow": "Aşağı Ox", "PE.Controllers.Main.txtShape_downArrowCallout": "Aşağı Oxlu Çıxarış", "PE.Controllers.Main.txtShape_ellipse": "Ellips", "PE.Controllers.Main.txtShape_ellipseRibbon": "Aşağı Əyilmiş Lent", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Yuxarı Əyilmiş Lent", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Axın diaqramı: Alternativ Proses", "PE.Controllers.Main.txtShape_flowChartCollate": "Axın diaqramı: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartConnector": "Axın diaqramı: Birləşdirici", "PE.Controllers.Main.txtShape_flowChartDecision": "Axın diaqramı: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDelay": "Axın diaqramı: Gecikmə", "PE.Controllers.Main.txtShape_flowChartDisplay": "Axın diaqramı: Ekran", "PE.Controllers.Main.txtShape_flowChartDocument": "Axın diaqramı: Sənəd", "PE.Controllers.Main.txtShape_flowChartExtract": "Axın diaqramı: <PERSON><PERSON><PERSON>rtma", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Axın diaqramı: Məlumatlar", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Axın diaqramı: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Axın diaqramı: Maqnit Disk", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Axın diaqramı: Birbaşa Giriş Yaddaşı", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "<PERSON><PERSON><PERSON> diaqramı: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> giriş yaddaşı", "PE.Controllers.Main.txtShape_flowChartManualInput": "Axın diaqramı: Əl ilə Daxiletmə", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Axın diaqramı: Əl ilə <PERSON>ə<PERSON>yyat", "PE.Controllers.Main.txtShape_flowChartMerge": "Axın diaqramı: B<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Axın diaqramı: Çoxlu Sənəd", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Axın diaqramı: Səhi<PERSON>ə Xaricində Birləşdirici", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Axın diaqramı: Saxlanılan Məlumatlar", "PE.Controllers.Main.txtShape_flowChartOr": "Axın diaqramı: Və ya", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Axın diaqramı: Əvvəlcədən Müəyyən Edilmiş Proses", "PE.Controllers.Main.txtShape_flowChartPreparation": "Axın diaqramı: <PERSON><PERSON><PERSON>rl<PERSON>q", "PE.Controllers.Main.txtShape_flowChartProcess": "Axın diaqramı: Proses", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Axın diaqramı: Kart", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Axın diaqramı: <PERSON><PERSON><PERSON><PERSON> lent", "PE.Controllers.Main.txtShape_flowChartSort": "Axın diaqrmı: Çeşidlə", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Axın diaqramı: <PERSON>əm<PERSON>əmə qovşağı", "PE.Controllers.Main.txtShape_flowChartTerminator": "Axın diaqramı: Sonlandırıcı", "PE.Controllers.Main.txtShape_foldedCorner": "Qatlanmış Künc", "PE.Controllers.Main.txtShape_frame": "Ç<PERSON><PERSON><PERSON><PERSON>ə", "PE.Controllers.Main.txtShape_halfFrame": "Çərçivənin Yarısı", "PE.Controllers.Main.txtShape_heart": "Ürək", "PE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_homePlate": "Beşbucaqlı", "PE.Controllers.Main.txtShape_horizontalScroll": "Üfüqi Sürüşdürmə", "PE.Controllers.Main.txtShape_irregularSeal1": "Partlayış 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Partlayış 2", "PE.Controllers.Main.txtShape_leftArrow": "Sol Ox", "PE.Controllers.Main.txtShape_leftArrowCallout": "Sola Ox Çıxarışı", "PE.Controllers.Main.txtShape_leftBrace": "Sol Fiqurlu Mötərizə", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON>", "PE.Controllers.Main.txtShape_leftRightArrow": "Sol sağ ox", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Soldan Sağa Ox Çıxarışı", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Soldan Sağa Yuxarı Ox", "PE.Controllers.Main.txtShape_leftUpArrow": "Sol Yuxarı Ox", "PE.Controllers.Main.txtShape_lightningBolt": "İldırım", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "Ox", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "İkitərəfli Ox", "PE.Controllers.Main.txtShape_mathDivide": "<PERSON><PERSON><PERSON>ə", "PE.Controllers.Main.txtShape_mathEqual": "B<PERSON><PERSON>ə<PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "Minus", "PE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "Ay", "PE.Controllers.Main.txtShape_noSmoking": "\"No\" simvolu", "PE.Controllers.Main.txtShape_notchedRightArrow": "Kəsikli Sağa Ox", "PE.Controllers.Main.txtShape_octagon": "Səkkizbucaqlı", "PE.Controllers.Main.txtShape_parallelogram": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_pentagon": "Beşbucaqlı", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "İşarə", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "Cızma-qara", "PE.Controllers.Main.txtShape_polyline2": "Sərbəst forma", "PE.Controllers.Main.txtShape_quadArrow": "D<PERSON><PERSON><PERSON><PERSON> O<PERSON>", "PE.Controllers.Main.txtShape_quadArrowCallout": "Dördoxlu Çıxarış", "PE.Controllers.Main.txtShape_rect": "Düzbucaqlı", "PE.Controllers.Main.txtShape_ribbon": "Aşağı Lent", "PE.Controllers.Main.txtShape_ribbon2": "Üzü Yuxarı Lent", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON>ğ <PERSON>", "PE.Controllers.Main.txtShape_rightArrowCallout": "Sağ Oxlu Çıxarış", "PE.Controllers.Main.txtShape_rightBrace": "Sağ fiqurlu mötərizə", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON>ğ <PERSON>", "PE.Controllers.Main.txtShape_round1Rect": "Dairəvi Tək Künc Düzbucaqlı", "PE.Controllers.Main.txtShape_round2DiagRect": "Dairəvi Diaqonal Künc Düzbucaqlı", "PE.Controllers.Main.txtShape_round2SameRect": "Dairəvi Eyni Yan Künc Düzbucaqlı", "PE.Controllers.Main.txtShape_roundRect": "Dai<PERSON>əvi <PERSON>nc <PERSON>ü<PERSON>bu<PERSON>qlı", "PE.Controllers.Main.txtShape_rtTriangle": "Düzbucaqlı Üçbucaq", "PE.Controllers.Main.txtShape_smileyFace": "Gülən Üz", "PE.Controllers.Main.txtShape_snip1Rect": "Bir Küncü Kəsik Düzbucaqlı", "PE.Controllers.Main.txtShape_snip2DiagRect": "Diaqonal Küncləri Kəsik Düzbucaqlı", "PE.Controllers.Main.txtShape_snip2SameRect": "<PERSON><PERSON><PERSON> Tərəfdə Küncü Kəsik Düzbucaqlı", "PE.Controllers.Main.txtShape_snipRoundRect": "Diaqonal Küncləri olan K<PERSON> və Dəyirmi Düzbucaqlı", "PE.Controllers.Main.txtShape_spline": "Əyr<PERSON>", "PE.Controllers.Main.txtShape_star10": "10 Guşəli Ulduz", "PE.Controllers.Main.txtShape_star12": "12 Guşəli Ulduz", "PE.Controllers.Main.txtShape_star16": "16 Guşəli Ulduz", "PE.Controllers.Main.txtShape_star24": "24 <PERSON><PERSON><PERSON><PERSON><PERSON>lduz", "PE.Controllers.Main.txtShape_star32": "32 Guşəli Ulduz", "PE.Controllers.Main.txtShape_star4": "4 gu<PERSON>əli Ulduz", "PE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star6": "6 G<PERSON>ş<PERSON>li <PERSON>", "PE.Controllers.Main.txtShape_star7": "7 <PERSON><PERSON>ş<PERSON>li ulduz", "PE.Controllers.Main.txtShape_star8": "8 Guşəli Ulduz", "PE.Controllers.Main.txtShape_stripedRightArrow": "Ştrixli Sağa Ox", "PE.Controllers.Main.txtShape_sun": "Gün<PERSON>ş", "PE.Controllers.Main.txtShape_teardrop": "Damcı", "PE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_trapezoid": "Trapesiya", "PE.Controllers.Main.txtShape_triangle": "Üçbucaq", "PE.Controllers.Main.txtShape_upArrow": "Yuxarı Ox", "PE.Controllers.Main.txtShape_upArrowCallout": "Yuxarı Oxlu Çıxarış", "PE.Controllers.Main.txtShape_upDownArrow": "Yuxarı Aşağı Ox", "PE.Controllers.Main.txtShape_uturnArrow": "U-Dönmə Oxu", "PE.Controllers.Main.txtShape_verticalScroll": "Şaquli Sürüşdürmə", "PE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Çıxarış", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Düzbucaqlı Çıxarış", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Dairəvi Düzbucaqlı Çıxarış", "PE.Controllers.Main.txtSldLtTBlank": "Boş ", "PE.Controllers.Main.txtSldLtTChart": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChartAndTx": "<PERSON><PERSON><PERSON> və Mətn", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "KlipArt və Mətn", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "KlipArt və Şaquli Mətn", "PE.Controllers.Main.txtSldLtTCust": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTDgm": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTFourObj": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media və Mətn", "PE.Controllers.Main.txtSldLtTObj": "Başlıq və Obyekt", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Obyekt və İki Obyekt", "PE.Controllers.Main.txtSldLtTObjAndTx": "Obyekt vı Mətn", "PE.Controllers.Main.txtSldLtTObjOnly": "Obyekt", "PE.Controllers.Main.txtSldLtTObjOverTx": "Mətn üzərində Obyekt", "PE.Controllers.Main.txtSldLtTObjTx": "Başlıq, Obyekt və Altyazı", "PE.Controllers.Main.txtSldLtTPicTx": "Şəkil və Başlıq", "PE.Controllers.Main.txtSldLtTSecHead": "Bölüm Başlığı", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "PE.Controllers.Main.txtSldLtTTitle": "Başlıq", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>q", "PE.Controllers.Main.txtSldLtTTwoColTx": "İki sütunlu mətn", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "İki Obyekt və Obyekt", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "İki Obyekt və Mətn", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Mətn üzərində iki obyekt", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "İki Mətn və İki Obyekt", "PE.Controllers.Main.txtSldLtTTx": "Mətn", "PE.Controllers.Main.txtSldLtTTxAndChart": "Mətn və Diaqram", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Mətn və KlipArt", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Mətn və Media", "PE.Controllers.Main.txtSldLtTTxAndObj": "Mətn və Obyekt", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Mətn və İki Obyekt", "PE.Controllers.Main.txtSldLtTTxOverObj": "Obyekt üzərində Mətn", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Şaquli başlıq və mətn", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Şaquli Başlıq və Diaqram Üzərində Mətn", "PE.Controllers.Main.txtSldLtTVertTx": "Şaquli <PERSON>", "PE.Controllers.Main.txtSlideNumber": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSlideSubtitle": "Slaydın altbaşlığı", "PE.Controllers.Main.txtSlideText": "<PERSON><PERSON><PERSON>ə<PERSON>", "PE.Controllers.Main.txtSlideTitle": "<PERSON>layd b<PERSON><PERSON>ı<PERSON>ı", "PE.Controllers.Main.txtStarsRibbons": "Ulduzlar və Lentlər", "PE.Controllers.Main.txtTheme_basic": "Əsas", "PE.Controllers.Main.txtTheme_blank": "<PERSON><PERSON> fay<PERSON>n", "PE.Controllers.Main.txtTheme_classic": "Klassik", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_lines": "Sə<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "<PERSON>is", "PE.Controllers.Main.txtTheme_office_theme": "Office Mövzusu", "PE.Controllers.Main.txtTheme_official": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_pixel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Tısbağa", "PE.Controllers.Main.txtXAxis": "X Oxu", "PE.Controllers.Main.txtYAxis": "Y Oxu", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> xəta.", "PE.Controllers.Main.unsupportedBrowserErrorText": "B<PERSON><PERSON><PERSON>z dəstəklənmir.", "PE.Controllers.Main.uploadImageExtMessage": "<PERSON><PERSON><PERSON> təsvir formatı.", "PE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON> bir təsvir yü<PERSON>.", "PE.Controllers.Main.uploadImageSizeMessage": "<PERSON>ə<PERSON><PERSON> çox böyükdür. Maksimum ölçü 25 MB-dır.", "PE.Controllers.Main.uploadImageTextText": "<PERSON>ə<PERSON><PERSON> yük<PERSON>ə<PERSON>...", "PE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gözləyin...", "PE.Controllers.Main.warnBrowserIE9": "Tətbiqin IE9-da aşağı imkanları var. IE10 və ya daha yüksək olandan istifadə edin", "PE.Controllers.Main.warnBrowserZoom": "Brauzerinizin cari böyütmə parametri tam dəstəklənmir. Ctrl+0 d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> basaraq defolt böyütməyə sıfırlayın.", "PE.Controllers.Main.warnLicenseExceeded": "Siz %1 redaktorlarına eyni vaxtda qoşulma limitinə çatdınız. Bu sənəd yalnız baxmaq üçün açılacaq.<br>Ətraflı məlumat üçün inzibatçınızla əlaqə saxlayın.", "PE.Controllers.Main.warnLicenseExp": "Lisenziyanızın vaxtı bitib.<br>Lisenziyanızı yeniləyin və səhifəni yeniləyin.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Lisenziyanın müddəti bitdi.<br><PERSON>ə<PERSON><PERSON>d redaktə funksiyasına giriş<PERSON>z yoxdur.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, inzibatçınızla əlaqə saxlayın.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Lisenziya yenilənməlidir.<br><PERSON>ənəd redaktə funksiyasına məhdud giriş<PERSON> var.<br><PERSON> giriş əldə etmək üçün inzibatçınızla əlaqə saxlayın.", "PE.Controllers.Main.warnLicenseUsersExceeded": "%1 redaktor üçün istifadəçi limitinə çatdınız. Ətraflı öyrənmək üçün admininizlə əlaqə saxlayın.", "PE.Controllers.Main.warnNoLicense": "Siz %1 redaktorlarına eyni vaxtda qoşulma limitinə çatdınız. Bu sənəd yalnız baxmaq üçün açılacaq.<br>Şəxsi təkmilləşdirmə şərtləri üçün %1 satış komandası ilə əlaqə saxlayın.", "PE.Controllers.Main.warnNoLicenseUsers": "%1 redaktor üçün istifadəçi limitinə çatdınız. Şəxsi təkmilləşdirmə şərtləri üçün %1 satış komandası ilə əlaqə saxlayın.", "PE.Controllers.Main.warnProcessRightsChange": "Siz faylı redaktə etmək hüququnuzdan məhrum oldunuz.", "PE.Controllers.Statusbar.zoomText": "<PERSON><PERSON><PERSON><PERSON> dəyi<PERSON> {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Yadda saxlayacağınız şrift cari cihazda mövcud deyil. <br> Mətn üslubu sistem şriftlərindən biri ilə göstəriləcək, mövcud olduqda saxlanmış şrift istifadə olunacaq. <br> Davam etmək istəyirsiniz?", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textBracket": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.textEmptyImgUrl": "Təsvir URL-ni göstərmə<PERSON>.", "PE.Controllers.Toolbar.textFontSizeErr": "Daxil edilmiş dəyər yanlışdır. <br> 1 ilə 300 arasında rəqəmsal dəyər daxil edin", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textInsert": "Daxil edin", "PE.Controllers.Toolbar.textIntegral": "İnteqrallar", "PE.Controllers.Toolbar.textLargeOperator": "Böyük Operatorlar", "PE.Controllers.Toolbar.textLimitAndLog": "Limitlər və Loqarifmlər", "PE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "PE.Controllers.Toolbar.textOperator": "Operatorlar", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "Skriptlər", "PE.Controllers.Toolbar.textSymbols": "Simvollar", "PE.Controllers.Toolbar.textWarning": "Xəbərdarlıq", "PE.Controllers.Toolbar.txtAccent_Accent": "Dəqiq", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Yuxarıda sağ-sol ox", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Sola Yuxarı Ox", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Sağa yuxarı ox", "PE.Controllers.Toolbar.txtAccent_Bar": "Zolaq", "PE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> xətt", "PE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> xətt", "PE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> düstur (yer tutucu ilə)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> düstur (nümunə)", "PE.Controllers.Toolbar.txtAccent_Check": "İşarələ", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Alt fiqurlu mötərizə", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Üst fiqurlu mötərizə", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "Üstündə xətt olan <PERSON>", "PE.Controllers.Toolbar.txtAccent_Custom_3": "Yuxarıdan xətt ilə x XOR y", "PE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON>ç <PERSON>", "PE.Controllers.Toolbar.txtAccent_DDot": "İki Nöqtə", "PE.Controllers.Toolbar.txtAccent_Dot": "Nöqtə", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Yu<PERSON>rıdan ikiqat xətt", "PE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Aşağıdakı simvolu qruplaşdır", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Yuxarıdakı simvolu qruplaşdır", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Sola Yuxarı Harpun", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON>ğa yuxarı <PERSON>un", "PE.Controllers.Toolbar.txtAccent_Hat": "Qapaq", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON> i<PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "Tilda", "PE.Controllers.Toolbar.txtBracket_Angle": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Curve": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Byük/kiçik hərflər (iki şərt)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Böyük/kiçik hərflər (üç şərt)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Obyek<PERSON><PERSON>r yığı<PERSON>ı", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Obyek<PERSON><PERSON>r yığı<PERSON>ı", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Böyük/kiçik hərf nümunəsi", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Binom əmsalı", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binom əmsalı", "PE.Controllers.Toolbar.txtBracket_Line": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_LineDouble": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_LowLim": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Round": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Square": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_UppLim": "Mötə<PERSON><PERSON>lə<PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Tək mötərizə", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Tək mötərizə", "PE.Controllers.Toolbar.txtFractionDiagonal": "Əyri fraksiya", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Diferensial", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Diferensial", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Diferensial", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Diferensial", "PE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON><PERSON> frak<PERSON>", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi böl 2", "PE.Controllers.Toolbar.txtFractionSmall": "Kiçik fraksiya", "PE.Controllers.Toolbar.txtFractionVertical": "Şaquli adi kəsr", "PE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON> kos<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperbolik tərs kosinus <PERSON>ı", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Tərs kotangent funksiyası", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperbolik tərs kotangent funksiyası", "PE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON>ə<PERSON> kosekant <PERSON>ı", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperbolik tərs kosekant funksiyası", "PE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sekant <PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperbolik tərs sekant <PERSON>ı", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Tərs sinus funksiyası", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperbolik tərs sinus funksiyası", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Tərs tangens funksiyası", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperbolik tərs tangens funksiyası", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hiperbolik kosinus <PERSON>", "PE.Controllers.Toolbar.txtFunction_Cot": "Kotangent funksiyası", "PE.Controllers.Toolbar.txtFunction_Coth": "Hiperbolik kotangent funksiyası", "PE.Controllers.Toolbar.txtFunction_Csc": "Kosekant funksiyası", "PE.Controllers.Toolbar.txtFunction_Csch": "Hiperbolik kosekant funksiyası", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sin teta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON> dü<PERSON>", "PE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sech": "Hiperbolik sekant funksiyası", "PE.Controllers.Toolbar.txtFunction_Sin": "Sinus funksiyası", "PE.Controllers.Toolbar.txtFunction_Sinh": "Hiperbolik sinus funksiyası", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangens funksiyası", "PE.Controllers.Toolbar.txtFunction_Tanh": "Hiperbolik tangens funksiyası", "PE.Controllers.Toolbar.txtIntegral": "İnteqral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Diferensial teta", "PE.Controllers.Toolbar.txtIntegral_dx": "Differensial x", "PE.Controllers.Toolbar.txtIntegral_dy": "Differensial y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "İnteqral", "PE.Controllers.Toolbar.txtIntegralDouble": "İkiqat inteqral", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "İkiqat inteqral", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "İkiqat inteqral", "PE.Controllers.Toolbar.txtIntegralOriented": "Kontur inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Kontur inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON>əth inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON>əth inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON>əth inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Kontur inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Həcm inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Həcm inteqralı", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Həcm inteqralı", "PE.Controllers.Toolbar.txtIntegralSubSup": "İnteqral", "PE.Controllers.Toolbar.txtIntegralTriple": "Üçqat inteqral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Üçqat inteqral", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Üçqat inteqral", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>ır", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>ır", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>ır", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>ır", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>ır", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON>ə məhsul", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON>ə məhsul", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON>ə məhsul", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON>ə məhsul", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON>ə məhsul", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Birlik", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "K<PERSON>sişmə", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "K<PERSON>sişmə", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "K<PERSON>sişmə", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "K<PERSON>sişmə", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "K<PERSON>sişmə", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Cəmləmə", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Birlik", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Birlik", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Birlik", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Birlik", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Birlik", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON> nümunə", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "PE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON><PERSON> loqari<PERSON>m", "PE.Controllers.Toolbar.txtLimitLog_Log": "Loqarifm", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Loqarifm", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 boş matris", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 boş matris", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 boş matris", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 boş matris", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Mötərizədə boş matris", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Mötərizədə boş matris", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Mötərizədə boş matris", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Mötərizədə boş matris", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 boş matris", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 boş matris", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 boş matris", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 boş matris", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> xətt nö<PERSON>ə<PERSON>ə<PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Ortaxətt nöqtələri", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diaqonal nöqtələri", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Şaquli <PERSON>ələ<PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Mötərizədə Seyrək Matris", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Mötərizədə Seyrək Matris", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 şəxsiyyət matrisi", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 şəxsiyyət matrisi", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 şəxsiyyət matrisi", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 şəxsiyyət matrisi", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON>şağ<PERSON>da sağ-sol ox", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Yuxarıda sağ-sol ox", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Sola Yuxarı Ox", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Sola Yuxarı Ox", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Sağa aşağı ox", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Sağa yuxarı ox", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "İki nöqtə bərabər", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Axınlar", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta Axınlar", "PE.Controllers.Toolbar.txtOperator_Definition": "Tə<PERSON><PERSON>ə görə bərabərdir", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta bərabərdir:", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON>şağ<PERSON>da sağ-sol ox", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Yuxarıda sağ-sol ox", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Sola Yuxarı Ox", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Sola Yuxarı Ox", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Sağa aşağı ox", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Sağa yuxarı ox", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON> bərabər", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Bərabərdir", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Ölçülür:", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Dərəcə ilə kvadrat kök", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Dərə<PERSON>ə ilə radikal", "PE.Controllers.Toolbar.txtRadicalSqrt": "Kvadrat kök", "PE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Aşağı İndeks", "PE.Controllers.Toolbar.txtScriptSubSup": "Aşağı İndeks-Yuxarı İndeks", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Sol aşağı indeks-yuxarı indeks", "PE.Controllers.Toolbar.txtScriptSup": "Yuxarı İndeks", "PE.Controllers.Toolbar.txtSymbol_about": "<PERSON>ə<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON><PERSON> olar ki bərabərdir:", "PE.Controllers.Toolbar.txtSymbol_ast": "Asterisk Operatoru", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON>er operatoru", "PE.Controllers.Toolbar.txtSymbol_cap": "K<PERSON>sişmə", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON> xətt ü<PERSON>üqi ellips", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Təx<PERSON>ən bərabə<PERSON>r:", "PE.Controllers.Toolbar.txtSymbol_cup": "Birlik", "PE.Controllers.Toolbar.txtSymbol_ddots": "Aşağıya sağa diaqonal üç nöqtə", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON>lmə işarəsi", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Aşağı Ox", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Boş çoxluq", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "B<PERSON><PERSON>ə<PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Oxşardır", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Mövcuddur", "PE.Controllers.Toolbar.txtSymbol_factorial": "Faktorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Fahrenheit dərəc<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_forall": "Hamı ü<PERSON>ün", "PE.Controllers.Toolbar.txtSymbol_gamma": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_geq": "Böyük və ya ona bərabər:", "PE.Controllers.Toolbar.txtSymbol_gg": "-dən çox böyükdür", "PE.Controllers.Toolbar.txtSymbol_greater": "-də<PERSON>", "PE.Controllers.Toolbar.txtSymbol_in": "Element:", "PE.Controllers.Toolbar.txtSymbol_inc": "Artım", "PE.Controllers.Toolbar.txtSymbol_infinity": "Sonsuzluq", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Sol Ox", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Sol-sağ ox", "PE.Controllers.Toolbar.txtSymbol_leq": "-dən az və ya bərabərdir", "PE.Controllers.Toolbar.txtSymbol_less": "-dən az", "PE.Controllers.Toolbar.txtSymbol_ll": "-dən çox kiçikdir", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus və Plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_ni": "Üzv kimi da<PERSON>ir", "PE.Controllers.Toolbar.txtSymbol_not": "İşarə yoxdur", "PE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omikron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omeqa", "PE.Controllers.Toolbar.txtSymbol_partial": "Qismən diferensial", "PE.Controllers.Toolbar.txtSymbol_percent": "Faiz", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Mütənasibdir:", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kök", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_rddots": "Yuxarıya Sağa Diaqonal Üç Nöqtə", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON>ğ <PERSON>", "PE.Controllers.Toolbar.txtSymbol_sigma": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Radikal işarə", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON>una görə də", "PE.Controllers.Toolbar.txtSymbol_theta": "Teta", "PE.Controllers.Toolbar.txtSymbol_times": "Çoxalma i<PERSON>arə<PERSON>", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Yuxarı Ox", "PE.Controllers.Toolbar.txtSymbol_upsilon": "İpsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variantı", "PE.Controllers.Toolbar.txtSymbol_varphi": "Fi variantı", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>ı", "PE.Controllers.Toolbar.txtSymbol_varrho": "Ro variantı", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Siqma variantı", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vdots": "Şaquli ellips", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "<PERSON><PERSON><PERSON> uyğun tənzimlə", "PE.Controllers.Viewport.textFitWidth": "Enə uyğun tənzimlə", "PE.Views.Animation.textNoRepeat": "(yoxdur)", "PE.Views.Animation.txtParameters": "Parametreler", "PE.Views.ChartSettings.textAdvanced": "Qabaqcıl <PERSON>", "PE.Views.ChartSettings.textChartType": "<PERSON><PERSON><PERSON><PERSON>n növünü dəyiş", "PE.Views.ChartSettings.textEditData": "Məlumatları Redaktə edin", "PE.Views.ChartSettings.textHeight": "Hündürlük", "PE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON>", "PE.Views.ChartSettings.textSize": "Ölçü", "PE.Views.ChartSettings.textStyle": "Üslub", "PE.Views.ChartSettings.textWidth": "Genişlik", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternativ Mətn", "PE.Views.ChartSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAltTip": "Şəkildə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya idrak qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Başlıq", "PE.Views.ChartSettingsAdvanced.textTitle": "Diaqram - Təkmilləşdirilmiş Parametrlər", "PE.Views.DateTimeDialog.confirmDefault": "{0} <PERSON><PERSON><PERSON><PERSON> defolt formatı təyin edin: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "De<PERSON>lt olaraq təyin edin", "PE.Views.DateTimeDialog.textFormat": "Formatlar", "PE.Views.DateTimeDialog.textLang": "Dil", "PE.Views.DateTimeDialog.textUpdate": "Avtomatik yeniləyin", "PE.Views.DateTimeDialog.txtTitle": "Tarix və Vaxt", "PE.Views.DocumentHolder.aboveText": "y<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.addCommentText": "Şərh yaz", "PE.Views.DocumentHolder.addToLayoutText": "Ajouter dans une mise en page", "PE.Views.DocumentHolder.advancedImageText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedParagraphText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedShapeText": "Forma Təkmil Parametrlər", "PE.Views.DocumentHolder.advancedTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Təkmil <PERSON>", "PE.Views.DocumentHolder.alignmentText": "Düzülüş", "PE.Views.DocumentHolder.belowText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.cellAlignText": "Xananın Şaquli Düzülüşü", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "Mərkəz", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteText": "Sil", "PE.Views.DocumentHolder.direct270Text": "Mətni Yuxarı fırladın", "PE.Views.DocumentHolder.direct90Text": "Mətni Aşağı fırladın", "PE.Views.DocumentHolder.directHText": "Üfüqi", "PE.Views.DocumentHolder.directionText": "Mə<PERSON>n İstiq<PERSON>ə<PERSON>", "PE.Views.DocumentHolder.editChartText": "Məlumatları Redaktə edin", "PE.Views.DocumentHolder.editHyperlinkText": "Hiperlinki redaktə edin", "PE.Views.DocumentHolder.hyperlinkText": "Hiperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON> birinə Əhəmiyyət vermə", "PE.Views.DocumentHolder.ignoreSpellText": "Ə<PERSON>ə<PERSON><PERSON><PERSON>ət vermə", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON>", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnText": "<PERSON><PERSON><PERSON> daxil edin", "PE.Views.DocumentHolder.insertRowAboveText": "Yuxarı Sətir", "PE.Views.DocumentHolder.insertRowBelowText": "Aşağı Sətir", "PE.Views.DocumentHolder.insertRowText": "Cərgə Əlavə Edin", "PE.Views.DocumentHolder.insertText": "Daxil edin", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.leftText": "Sol", "PE.Views.DocumentHolder.loadSpellText": "Variantlar yüklənir...", "PE.Views.DocumentHolder.mergeCellsText": "Xanaları Birləşdir", "PE.Views.DocumentHolder.mniCustomTable": "<PERSON><PERSON>rdi Cədvəl Daxil Edin", "PE.Views.DocumentHolder.moreText": "Daha çox variant...", "PE.Views.DocumentHolder.noSpellVariantsText": "<PERSON>ariant yoxdur", "PE.Views.DocumentHolder.originalSizeText": "Faktiki Ölçü", "PE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rightText": "Sağ", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "Seç", "PE.Views.DocumentHolder.spellcheckText": "Orfoqrafiya yoxlanışı", "PE.Views.DocumentHolder.splitCellsText": "Bölünmüş Xana...", "PE.Views.DocumentHolder.splitCellTitleText": "Bölünmüş xana", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "PE.Views.DocumentHolder.textArrangeBack": "Fona göndərin", "PE.Views.DocumentHolder.textArrangeBackward": "Geriyə Göndərin", "PE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.DocumentHolder.textArrangeFront": "Ön plana çıxarın", "PE.Views.DocumentHolder.textCopy": "Kopyala", "PE.Views.DocumentHolder.textCrop": "<PERSON><PERSON>s", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "<PERSON>ə<PERSON><PERSON><PERSON>ə", "PE.Views.DocumentHolder.textCut": "<PERSON><PERSON>s", "PE.Views.DocumentHolder.textDistributeCols": "Sütunları bölüşdürün", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipH": "Üfüqi olaraq çevir", "PE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON><PERSON> çevir", "PE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFromUrl": "URL-dən", "PE.Views.DocumentHolder.textNextPage": "<PERSON>ö<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textPaste": "Yapışdır", "PE.Views.DocumentHolder.textPrevPage": "Əvvəlki Slayd", "PE.Views.DocumentHolder.textReplace": "Təsviri əvəz edin", "PE.Views.DocumentHolder.textRotate": "D<PERSON>nd<PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "90° Saat əqrəbinin əksi istiqamətində döndər", "PE.Views.DocumentHolder.textRotate90": "90° saat əqrəbi istiqamətində döndər", "PE.Views.DocumentHolder.textShapeAlignBottom": "Aşağı Düzləndir", "PE.Views.DocumentHolder.textShapeAlignCenter": "Mərkəzə Düzləndir", "PE.Views.DocumentHolder.textShapeAlignLeft": "Sola nizamlayın", "PE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignTop": "Yuxarı Düzləndir", "PE.Views.DocumentHolder.textSlideSettings": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "Bu element başqa istifadəçi tərəfindən redaktə olunur.", "PE.Views.DocumentHolder.toDictionaryText": "Lüğətə əlavə edin", "PE.Views.DocumentHolder.txtAddBottom": "Aşağı haşiyə əlavə et", "PE.Views.DocumentHolder.txtAddFractionBar": "<PERSON>ə<PERSON>r xətti əlavə edin", "PE.Views.DocumentHolder.txtAddHor": "Üfüqi xətt əlavə edin", "PE.Views.DocumentHolder.txtAddLB": "Sol haşiyə xətti əlavə edin", "PE.Views.DocumentHolder.txtAddLeft": "Sol haşiyə əlavə edin", "PE.Views.DocumentHolder.txtAddLT": "Sol yuxarı xətir əlavə edin", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON> haşiyə əlavə edin", "PE.Views.DocumentHolder.txtAddTop": "Yuxarı haşiyə əlavə edin", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON> sətir əlavə edin", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "Sim<PERSON>la uyğun düzləndir", "PE.Views.DocumentHolder.txtArrange": "Düzənlə", "PE.Views.DocumentHolder.txtBackground": "<PERSON><PERSON><PERSON> fon", "PE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON><PERSON> xü<PERSON>iy<PERSON>ətləri", "PE.Views.DocumentHolder.txtBottom": "Aşağı", "PE.Views.DocumentHolder.txtChangeLayout": "Tərtibatı dəyiş", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> dü<PERSON>", "PE.Views.DocumentHolder.txtDecreaseArg": "Arqument ölçüsünü azaldın", "PE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON>q<PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteBreak": "Əl ilə kəsilməni silin", "PE.Views.DocumentHolder.txtDeleteChars": "Ətrafdakı simvolları silin", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Ətrafdakı simvolları və ayırıcıları silin", "PE.Views.DocumentHolder.txtDeleteEq": "<PERSON>ə<PERSON><PERSON><PERSON> silin", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Simvolu silin", "PE.Views.DocumentHolder.txtDeleteRadical": "Radikalı sil", "PE.Views.DocumentHolder.txtDeleteSlide": "Slaydı Silin", "PE.Views.DocumentHolder.txtDistribHor": "Üfüqi olaraq Bölüşdürün", "PE.Views.DocumentHolder.txtDistribVert": "Şaquli o<PERSON>aq Bölüşdürün", "PE.Views.DocumentHolder.txtDuplicateSlide": "Dublikat Slayd", "PE.Views.DocumentHolder.txtFractionLinear": "<PERSON><PERSON><PERSON> frak<PERSON>", "PE.Views.DocumentHolder.txtFractionSkewed": "Diaqonal Fraksiyaya <PERSON>", "PE.Views.DocumentHolder.txtFractionStacked": "Şaquli sadə fraksiyaya də<PERSON>", "PE.Views.DocumentHolder.txtGroup": "Qrup", "PE.Views.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON>nin yuxarısında işarə", "PE.Views.DocumentHolder.txtGroupCharUnder": "Mə<PERSON>nin aşağısında işarə", "PE.Views.DocumentHolder.txtHideBottom": "Aşağı haşiyəni gizlət", "PE.Views.DocumentHolder.txtHideBottomLimit": "Aşağı limiti gizlədin", "PE.Views.DocumentHolder.txtHideCloseBracket": "Bağlı mötərizəni gizlədin", "PE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>ədin", "PE.Views.DocumentHolder.txtHideHor": "Üfüqi sətri giz<PERSON>ədin", "PE.Views.DocumentHolder.txtHideLB": "Sol aşağı sətri gizlədin", "PE.Views.DocumentHolder.txtHideLeft": "Sol haşiyəni gizlət", "PE.Views.DocumentHolder.txtHideLT": "Sol yuxarı sətri gizlədin", "PE.Views.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ə<PERSON> giz<PERSON>ədin", "PE.Views.DocumentHolder.txtHidePlaceholder": "<PERSON>r tutucunu g<PERSON>ədin", "PE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON> haşi<PERSON><PERSON><PERSON> giz<PERSON>ədin", "PE.Views.DocumentHolder.txtHideTop": "Yuxarı haşi<PERSON>əni gizlədin", "PE.Views.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON> həddi gizlədin", "PE.Views.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON> s<PERSON> g<PERSON>", "PE.Views.DocumentHolder.txtIncreaseArg": "Arqument ölçüsünü artırın", "PE.Views.DocumentHolder.txtInsertArgAfter": "Arqumenti sonra daxil edin", "PE.Views.DocumentHolder.txtInsertArgBefore": "Arqumenti əvvəl daxil edin", "PE.Views.DocumentHolder.txtInsertBreak": "Əl ilə səhifə sonunu əlavə edin", "PE.Views.DocumentHolder.txtInsertEqAfter": "<PERSON>ra tənlik daxil edin", "PE.Views.DocumentHolder.txtInsertEqBefore": "Əvvəl tənlik daxil edin", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON>z mətni saxlayın", "PE.Views.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON> də<PERSON>", "PE.Views.DocumentHolder.txtLimitOver": "Mətn üzərində limit", "PE.Views.DocumentHolder.txtLimitUnder": "Mətn altında limit", "PE.Views.DocumentHolder.txtMatchBrackets": "Mötə<PERSON><PERSON><PERSON><PERSON><PERSON> arqumentin h<PERSON>ü<PERSON>ə uyğunlaşdırın", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtNewSlide": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtOverbar": "Mə<PERSON>nin üstündə xətt", "PE.Views.DocumentHolder.txtPasteDestFormat": "Təyi<PERSON> mövzusundan istifadə edin", "PE.Views.DocumentHolder.txtPastePicture": "Şəkil", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Mənbə formatını saxlayın", "PE.Views.DocumentHolder.txtPressLink": "{0} d<PERSON><PERSON><PERSON><PERSON><PERSON> basıb linkə klikləyin", "PE.Views.DocumentHolder.txtPreview": "Slay<PERSON>ı başladın", "PE.Views.DocumentHolder.txtPrintSelection": "Seçimi çap edin", "PE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON><PERSON> xəttini silin", "PE.Views.DocumentHolder.txtRemLimit": "<PERSON><PERSON> si<PERSON>", "PE.Views.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON><PERSON> simvolu<PERSON> silin", "PE.Views.DocumentHolder.txtRemoveBar": "Zolağı Sil", "PE.Views.DocumentHolder.txtRemScripts": "İndeksi Silin", "PE.Views.DocumentHolder.txtRemSubscript": "Sətiraltı işarəni silin", "PE.Views.DocumentHolder.txtRemSuperscript": "Sətiraltı işarəni silin", "PE.Views.DocumentHolder.txtResetLayout": "Slaydı Sıfırlayın", "PE.Views.DocumentHolder.txtScriptsAfter": "<PERSON><PERSON><PERSON><PERSON><PERSON>n sonra skriptlər", "PE.Views.DocumentHolder.txtScriptsBefore": "<PERSON>ə<PERSON><PERSON>ən əvvəl skriptlər", "PE.Views.DocumentHolder.txtSelectAll": "Hamısını seç", "PE.Views.DocumentHolder.txtShowBottomLimit": "Haş<PERSON><PERSON>ə limitini gö<PERSON>ərin", "PE.Views.DocumentHolder.txtShowCloseBracket": "Bağlama mötərizə<PERSON>i g<PERSON>", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>ş mötərizəsini gö<PERSON>ərin", "PE.Views.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "PE.Views.DocumentHolder.txtShowTopLimit": "Üst <PERSON>i g<PERSON>ərin", "PE.Views.DocumentHolder.txtSlide": "Slayd", "PE.Views.DocumentHolder.txtSlideHide": "Slaydı gizlət", "PE.Views.DocumentHolder.txtStretchBrackets": "Mötə<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.DocumentHolder.txtTop": "Yuxarı", "PE.Views.DocumentHolder.txtUnderbar": "Mə<PERSON>nin altında xətt", "PE.Views.DocumentHolder.txtUngroup": "Qruplaşdırmanı ləğv et", "PE.Views.DocumentHolder.txtWarnUrl": "Bu keçidə klikləmək cihazınız və məlumatlarınız üçün təhlükəli ola bilər. <br> Davam etmək istədiyinizdən əminsiniz?", "PE.Views.DocumentHolder.vertAlignText": "Şaquli <PERSON>", "PE.Views.DocumentPreview.goToSlideText": "Slayda keçin", "PE.Views.DocumentPreview.slideIndexText": "{0} / {1} slayd", "PE.Views.DocumentPreview.txtClose": "Slaydı bağlayın", "PE.Views.DocumentPreview.txtEndSlideshow": "Slayd <PERSON><PERSON><PERSON>u", "PE.Views.DocumentPreview.txtExitFullScreen": "Tam ekrandan çıx", "PE.Views.DocumentPreview.txtFinalMessage": "Slayda önbaxışın sonu. Çıxmaq üçün k<PERSON>ləyin.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON>", "PE.Views.DocumentPreview.txtNext": "<PERSON>ö<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtPageNumInvalid": "Yanlış slayd <PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtPause": "T<PERSON>q<PERSON><PERSON><PERSON> dayandır", "PE.Views.DocumentPreview.txtPlay": "<PERSON>ə<PERSON><PERSON><PERSON> ba<PERSON><PERSON>ın", "PE.Views.DocumentPreview.txtPrev": "<PERSON>v<PERSON><PERSON><PERSON><PERSON> slayd", "PE.Views.DocumentPreview.txtReset": "Sıfırla", "PE.Views.FileMenu.btnAboutCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON> a<PERSON>", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>n", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnExitCaption": "Çı<PERSON>ın", "PE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHelpCaption": "Yardım", "PE.Views.FileMenu.btnHistoryCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnInfoCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnPrintCaption": "Çap", "PE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnRenameCaption": "Adını Dəyişdir", "PE.Views.FileMenu.btnReturnCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> qayıt", "PE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveAsCaption": "kimi yadda saxla", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>a saxla", "PE.Views.FileMenu.btnSaveCopyAsCaption": "<PERSON><PERSON><PERSON> kimi yadda saxla", "PE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> parametrlər", "PE.Views.FileMenu.btnToEditCaption": "Təqdimatı redaktə edin", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Boş Təqdimat", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Tətbiq et", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Mətn əlavə edin", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Tətbiq", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON><PERSON>ü<PERSON>ını dəyiş", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Şərh", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Yaradıldı", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON> <PERSON><PERSON><PERSON><PERSON>:", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Sahib", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Yer", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Hüq<PERSON><PERSON><PERSON>ı olan <PERSON>ə<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Başlıq", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Yükləndi", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON><PERSON>ü<PERSON>ını dəyiş", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Hüq<PERSON><PERSON><PERSON>ı olan <PERSON>ə<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Xəbərdarlıq", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON> il<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON><PERSON> ilə", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Təqdimatı redaktə edin", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Sənədi redaktə etməklə imzalar silinəcək. <br> Davam etmək istədiyinizə əminsiniz?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Bu təqdimat parolla qorunur", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Sənədə etibarlı imzalar əlavə edilib. Sənəd dəyişikliklərdən qorunur.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Elektron imzalar etibarsızdır və ya yoxlanıla bilməz. Sənəd dəyişikliklərdən qorunur.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "İmzalara baxın", "PE.Views.FileMenuPanels.Settings.okButtonText": "Tətbiq et", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Birgə redaktə Rejimi", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Şrift Hamarlaşdırma", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strPasteButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> yapış<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>da Yapış<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> g<PERSON>", "PE.Views.FileMenuPanels.Settings.strStrict": "Mə<PERSON><PERSON>dlaşdır", "PE.Views.FileMenuPanels.Settings.strTheme": "İnterfeys mövzusu", "PE.Views.FileMenuPanels.Settings.strUnit": "Ölçü Vahidi", "PE.Views.FileMenuPanels.Settings.strZoom": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Hər 10 dəqiqə", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Hər 30 dəqiqədən bir", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Hər 5 Dəqiqədən bir", "PE.Views.FileMenuPanels.Settings.text60Minutes": "hər saat", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Düzülüş Bələdçiləri", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "<PERSON>v<PERSON><PERSON><PERSON> bərpa", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Avtomatik yadda saxlama", "PE.Views.FileMenuPanels.Settings.textDisabled": "Deaktiv", "PE.Views.FileMenuPanels.Settings.textForceSave": "Ara versiyaların saxlan<PERSON>ı", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON>ər dəqiqə", "PE.Views.FileMenuPanels.Settings.txtAll": "Hamısını gö<PERSON>ər", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "AvtoDüzəliş seçimləri...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Defolt keş rejimi", "PE.Views.FileMenuPanels.Settings.txtCm": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "<PERSON><PERSON><PERSON> uyğun tənzimlə", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Enə uyğun tənzimlə", "PE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "PE.Views.FileMenuPanels.Settings.txtMac": "OS X kimi", "PE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Hamısını Aktivləşdirin", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Bütün makroları bildiriş olmadan aktivləşdirin", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Orfoqrafiyanın <PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Hamısını Deaktiv edin", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Bütün makroları bildiriş olmadan deaktiv edin", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Bütün makroları bildirişlə deaktiv edin", "PE.Views.FileMenuPanels.Settings.txtWin": "Windows kimi", "PE.Views.HeaderFooterDialog.applyAllText": "Hamısına Tətbiq Et", "PE.Views.HeaderFooterDialog.applyText": "Tətbiq et", "PE.Views.HeaderFooterDialog.diffLanguage": "<PERSON>z ə<PERSON><PERSON> slayddan başqa tarix dilindən istifadə edə bilməzsiniz. <br> Əsası dəyişmək üçün \"Tətbiq et\" əvəzinə \"Hamısına tətbiq et\" üzərinə klikləyin.", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Xəbərdarlıq", "PE.Views.HeaderFooterDialog.textDateTime": "Tarix və saat", "PE.Views.HeaderFooterDialog.textFixed": "Sabit", "PE.Views.HeaderFooterDialog.textFooter": "Altbaşlıqda mətn", "PE.Views.HeaderFooterDialog.textFormat": "Formatlar", "PE.Views.HeaderFooterDialog.textLang": "Dil", "PE.Views.HeaderFooterDialog.textNotTitle": "Başlıq slaydında gö<PERSON>ə<PERSON>ə", "PE.Views.HeaderFooterDialog.textPreview": "Önbaxış", "PE.Views.HeaderFooterDialog.textSlideNum": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textTitle": "Alt-başlıq <PERSON>", "PE.Views.HeaderFooterDialog.textUpdate": "Avtomatik yeniləyin", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON>qə", "PE.Views.HyperlinkSettingsDialog.textDefault": "Seçilmiş mətn fraqmentini", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Başlığı bura daxil edin", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Buraya keçid daxil edin", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Alətİzahını bura daxil edin", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Xarici Link", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON> <PERSON>ə<PERSON><PERSON>matda Slayd", "PE.Views.HyperlinkSettingsDialog.textSlides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTipText": "Ekran İzahı Mətn", "PE.Views.HyperlinkSettingsDialog.textTitle": "Hiperlink Parametrləri", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Bu sahə tələb olunur", "PE.Views.HyperlinkSettingsDialog.txtFirst": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.txtLast": "<PERSON>", "PE.Views.HyperlinkSettingsDialog.txtNext": "<PERSON>ö<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Bu sahə \"http://www.example.com\" formatında URL olmalıdır", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Əvvəlki Slayd", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Bu sahə 2083 simvolla məhdudlaşır", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slayd", "PE.Views.ImageSettings.textAdvanced": "Qabaqcıl <PERSON>", "PE.Views.ImageSettings.textCrop": "<PERSON><PERSON>s", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "<PERSON>ə<PERSON><PERSON><PERSON>ə", "PE.Views.ImageSettings.textEdit": "Redaktə et", "PE.Views.ImageSettings.textEditObject": "Obyekti redaktə edin", "PE.Views.ImageSettings.textFitSlide": "<PERSON><PERSON><PERSON> uyğun tənzimlə", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromUrl": "URL-dən", "PE.Views.ImageSettings.textHeight": "Hündürlük", "PE.Views.ImageSettings.textHint270": "90° Saat əqrəbinin əksi istiqamətində döndər", "PE.Views.ImageSettings.textHint90": "90° saat əqrəbi istiqamətində döndər", "PE.Views.ImageSettings.textHintFlipH": "Üfüqi olaraq çevir", "PE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON><PERSON> çevir", "PE.Views.ImageSettings.textInsert": "Təsviri əvəz edin", "PE.Views.ImageSettings.textOriginalSize": "Faktiki Ölçü", "PE.Views.ImageSettings.textRotate90": " 90° Döndər", "PE.Views.ImageSettings.textRotation": "Döndərmə", "PE.Views.ImageSettings.textSize": "Ölçü", "PE.Views.ImageSettings.textWidth": "En", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternativ Mətn", "PE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAltTip": "Şəkildə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya idrak qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Başlıq", "PE.Views.ImageSettingsAdvanced.textAngle": "Bucaq", "PE.Views.ImageSettingsAdvanced.textFlipped": "Çevrilmiş", "PE.Views.ImageSettingsAdvanced.textHeight": "Hündürlük", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Üfüqi o<PERSON>aq", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Faktiki Ölçü", "PE.Views.ImageSettingsAdvanced.textPlacement": "Ye<PERSON><PERSON>ş<PERSON>ə", "PE.Views.ImageSettingsAdvanced.textPosition": "Mövq<PERSON>", "PE.Views.ImageSettingsAdvanced.textRotation": "Döndərmə", "PE.Views.ImageSettingsAdvanced.textSize": "Ölçü", "PE.Views.ImageSettingsAdvanced.textTitle": "Təsvir-Təkmilləşdirilmiş Parametrlər", "PE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textWidth": "En", "PE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipComments": "Şə<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipPlugins": "Qoşmalar", "PE.Views.LeftMenu.tipSearch": "Axtarış", "PE.Views.LeftMenu.tipSlides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSupport": "Rəy və Dəstək", "PE.Views.LeftMenu.tipTitles": "Başlıqlar", "PE.Views.LeftMenu.txtDeveloper": "DEVELOPER REJİMİ", "PE.Views.LeftMenu.txtLimit": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "PE.Views.LeftMenu.txtTrial": "SINAQ REJİMİ", "PE.Views.LeftMenu.txtTrialDev": "Sınaq Tərtibatçı Rejimi", "PE.Views.ParagraphSettings.strLineHeight": "Sətirarası İnterval", "PE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingAfter": "Sonra", "PE.Views.ParagraphSettings.strSpacingBefore": "Əvvəl", "PE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textAt": ":", "PE.Views.ParagraphSettings.textAtLeast": "Ən azı", "PE.Views.ParagraphSettings.textAuto": "Çoxsaylı", "PE.Views.ParagraphSettings.textExact": "<PERSON>", "PE.Views.ParagraphSettings.txtAutoText": "Avtomatik", "PE.Views.ParagraphSettingsAdvanced.noTabs": "G<PERSON><PERSON><PERSON><PERSON>ən nişanlar bu sahədə göstərilir", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Hamısı böyük hərflə", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "İkiqat Üstüxətli", "PE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Sol", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Sətirarası İnterval", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Sağ", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Sonra", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Əvvəl", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Şrift", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "A<PERSON>zas və İnterval", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON> baş hərflər", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "İnterval", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Üstüxətli", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Aşağı İndeks", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Yuxarı İndeks", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tablar", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Düzülüş", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Çoxsaylı", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Simvol intervalı", "PE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Çıxıntı", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Kənarlara düzləndirilmiş", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(yoxdur)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Sil", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Hamısını Sil", "PE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON>üə<PERSON>yən edin", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Mərkəz", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Sol", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Sağ", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Paraqraf - <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Avtomatik", "PE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "PE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtShapeSettings": "Forma parametrləri", "PE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtSlideSettings": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtTableSettings": "<PERSON>ə<PERSON><PERSON><PERSON>l parametrləri", "PE.Views.RightMenu.txtTextArtSettings": "Mətn Şəkli parametrləri", "PE.Views.ShapeSettings.strBackground": "<PERSON><PERSON><PERSON> fon rəngi", "PE.Views.ShapeSettings.strChange": "Avtoformanı dəyiş", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Ön plan rəngi", "PE.Views.ShapeSettings.strPattern": "Nümunə", "PE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strSize": "Ölçü", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strType": "Növ", "PE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textAngle": "Bucaq", "PE.Views.ShapeSettings.textBorderSizeErr": "Daxil edilmiş dəyər yanlışdır.<br>0 pt ilə 1584 pt arasında dəyər daxil edin.", "PE.Views.ShapeSettings.textColor": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textDirection": "İstiqamət", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON>ümu<PERSON><PERSON> yoxdur", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromUrl": "URL-dən", "PE.Views.ShapeSettings.textGradient": "Qradiyent nöqtələri", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textHint270": "90° Saat əqrəbinin əksi istiqamətində döndər", "PE.Views.ShapeSettings.textHint90": "90° saat əqrəbi istiqamətində döndər", "PE.Views.ShapeSettings.textHintFlipH": "Üfüqi olaraq çevir", "PE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON><PERSON> çevir", "PE.Views.ShapeSettings.textImageTexture": "Şəkil və ya Tekstura", "PE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textNoFill": "Doldurulmasın", "PE.Views.ShapeSettings.textPatternFill": "Nümunə", "PE.Views.ShapeSettings.textPosition": "Mövq<PERSON>", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRotate90": " 90° Döndər", "PE.Views.ShapeSettings.textRotation": "Döndərmə", "PE.Views.ShapeSettings.textSelectImage": "Şək<PERSON>", "PE.Views.ShapeSettings.textSelectTexture": "Seç", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>ır", "PE.Views.ShapeSettings.textStyle": "Üslub", "PE.Views.ShapeSettings.textTexture": "Teksturdan", "PE.Views.ShapeSettings.textTile": "Lövhəcik", "PE.Views.ShapeSettings.tipAddGradientPoint": "Qradiyent nöqtəsi əlavə edin", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Qradiyent nöqtəni silin", "PE.Views.ShapeSettings.txtBrownPaper": "Qəhvəyi Kağız", "PE.Views.ShapeSettings.txtCanvas": "Lövhə", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGrain": "Dən", "PE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON>z <PERSON>", "PE.Views.ShapeSettings.txtKnit": "Bərkitmək", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "Taxta", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "Mətn <PERSON>ı", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternativ Mətn", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Şəkildə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya idrak qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Başlıq", "PE.Views.ShapeSettingsAdvanced.textAngle": "Bucaq", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "AvtoTənzimləmə", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Başlanğıc Ölçü", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Başlanğıc Üslub", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Aşağı", "PE.Views.ShapeSettingsAdvanced.textCapType": "Başlıq Növü", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON> sayı", "PE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Son Üslub", "PE.Views.ShapeSettingsAdvanced.textFlat": "Düz", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Çevrilmiş", "PE.Views.ShapeSettingsAdvanced.textHeight": "Hündürlük", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Üfüqi o<PERSON>aq", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Qoşulma Növü", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLeft": "Sol", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "Şaquli kəsişmə nöqtəsi", "PE.Views.ShapeSettingsAdvanced.textNofit": "Avtomatik Tənzimləmə", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Mətnə uyğunlaşdırmaq üçün formanın ölçüsünü dəyişdirin", "PE.Views.ShapeSettingsAdvanced.textRight": "Sağ", "PE.Views.ShapeSettingsAdvanced.textRotation": "Döndərmə", "PE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShrink": "Daşma vəziyyətində mətni kiçildin", "PE.Views.ShapeSettingsAdvanced.textSize": "Ölçü", "PE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forma-<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTop": "Yuxarı", "PE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Çəkilər və Oxlar", "PE.Views.ShapeSettingsAdvanced.textWidth": "En", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Xəbərdarlıq", "PE.Views.SignatureSettings.strDelete": "İmzanı Silin", "PE.Views.SignatureSettings.strDetails": "<PERSON><PERSON>za Detalları", "PE.Views.SignatureSettings.strInvalid": "Etibarsız <PERSON>", "PE.Views.SignatureSettings.strSign": "İşarə", "PE.Views.SignatureSettings.strSignature": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strValid": "Etibarlı imzalar", "PE.Views.SignatureSettings.txtContinueEditing": "İstə<PERSON><PERSON>ən halda redaktə edin", "PE.Views.SignatureSettings.txtEditWarning": "Sənədi redaktə etməklə imzalar silinəcək. <br> Davam etmək istədiyinizə əminsiniz?", "PE.Views.SignatureSettings.txtRemoveWarning": "Bu imzanı silmək istəyirsiniz?<br>Bunu geri qaytarmaq mümkün deyil.", "PE.Views.SignatureSettings.txtSigned": "Sənədə etibarlı imzalar əlavə edilib. Sənəd dəyişikliklərdən qorunur.", "PE.Views.SignatureSettings.txtSignedInvalid": "Elektron imzalar etibarsızdır və ya yoxlanıla bilməz. Sənəd dəyişikliklərdən qorunur.", "PE.Views.SlideSettings.strBackground": "<PERSON><PERSON><PERSON> fon rəngi", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "Tarix və Vaxtı Göstər", "PE.Views.SlideSettings.strFill": "<PERSON><PERSON><PERSON> fon", "PE.Views.SlideSettings.strForeground": "Ön plan rəngi", "PE.Views.SlideSettings.strPattern": "Nümunə", "PE.Views.SlideSettings.strSlideNum": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textAngle": "Bucaq", "PE.Views.SlideSettings.textColor": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textDirection": "İstiqamət", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON>ümu<PERSON><PERSON> yoxdur", "PE.Views.SlideSettings.textFromFile": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textFromUrl": "URL-dən", "PE.Views.SlideSettings.textGradient": "Qradiyent nöqtələri", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textImageTexture": "Şəkil və ya Tekstura", "PE.Views.SlideSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textNoFill": "Doldurulmasın", "PE.Views.SlideSettings.textPatternFill": "Nümunə", "PE.Views.SlideSettings.textPosition": "Mövq<PERSON>", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Dəyişik<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textSelectImage": "Şək<PERSON>", "PE.Views.SlideSettings.textSelectTexture": "Seç", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON>ır", "PE.Views.SlideSettings.textStyle": "Üslub", "PE.Views.SlideSettings.textTexture": "Teksturdan", "PE.Views.SlideSettings.textTile": "Lövhəcik", "PE.Views.SlideSettings.tipAddGradientPoint": "Qradiyent nöqtəsi əlavə edin", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Qradiyent nöqtəni silin", "PE.Views.SlideSettings.txtBrownPaper": "Qəhvəyi Kağız", "PE.Views.SlideSettings.txtCanvas": "Lövhə", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGrain": "Dən", "PE.Views.SlideSettings.txtGranite": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON>z <PERSON>", "PE.Views.SlideSettings.txtKnit": "Bərkitmək", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "Taxta", "PE.Views.SlideshowSettings.textLoop": "'<PERSON>s<PERSON>' d<PERSON><PERSON><PERSON><PERSON> basılana qədər davamlı göstər", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Albom", "PE.Views.SlideSizeSettings.strPortrait": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.textHeight": "Hündürlük", "PE.Views.SlideSizeSettings.textSlideOrientation": "<PERSON>layd oriyentasi<PERSON>ı", "PE.Views.SlideSizeSettings.textSlideSize": "Slayd ölçüsü", "PE.Views.SlideSizeSettings.textTitle": "<PERSON>layd <PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textWidth": "En", "PE.Views.SlideSizeSettings.txt35": "35 mm Slaydlar", "PE.Views.SlideSizeSettings.txtA3": "A3 Kağızı (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Kağızı (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Kağıt (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Kağıt (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtLedger": "Led<PERSON> (11x17 dyüm)", "PE.Views.SlideSizeSettings.txtLetter": "<PERSON><PERSON><PERSON><PERSON> (8.5x11 dyüm)", "PE.Views.SlideSizeSettings.txtOverhead": "Yuxarıda", "PE.Views.SlideSizeSettings.txtStandard": "<PERSON><PERSON> (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Geniş ekran", "PE.Views.Statusbar.goToPageText": "Slayda keçin", "PE.Views.Statusbar.pageIndexText": "{0} / {1} slayd", "PE.Views.Statusbar.textShowBegin": "Başlanğıcdan <PERSON>", "PE.Views.Statusbar.textShowCurrent": "<PERSON><PERSON>", "PE.Views.Statusbar.textShowPresenterView": "Təqdimatçı görünüşündə baxın", "PE.Views.Statusbar.tipAccessRights": "Sənə<PERSON>ə giriş hüquqlarını idarə edin", "PE.Views.Statusbar.tipFitPage": "<PERSON><PERSON><PERSON> uyğun tənzimlə", "PE.Views.Statusbar.tipFitWidth": "Enə uyğun tənzimlə", "PE.Views.Statusbar.tipPreview": "Slay<PERSON>ı başladın", "PE.Views.Statusbar.tipSetLang": "<PERSON>ətn dilini müəyyən<PERSON>", "PE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "PE.Views.Statusbar.tipZoomIn": "B<PERSON><PERSON>üdün", "PE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.txtPageNumInvalid": "Yanlış slayd <PERSON><PERSON><PERSON>", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.insertColumnLeftText": "<PERSON> sütun daxil edin", "PE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON> daxil edin", "PE.Views.TableSettings.insertRowAboveText": "Yu<PERSON><PERSON><PERSON>dan <PERSON>rgə Əlavə edin", "PE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Əlavə edin", "PE.Views.TableSettings.mergeCellsText": "Xanaları Birləşdir", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON>", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.splitCellsText": "Bölünmüş Xana...", "PE.Views.TableSettings.splitCellTitleText": "Bölünmüş xana", "PE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBackColor": "<PERSON><PERSON><PERSON> fon rəngi", "PE.Views.TableSettings.textBanded": "Zolaqlı", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "Haşiyə Üslubu", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "Sütunları bölüşdürün", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textEdit": "Sə<PERSON>rl<PERSON>r və Sütunlar", "PE.Views.TableSettings.textEmptyTemplate": "Şablon yoxdur", "PE.Views.TableSettings.textFirst": "İlk", "PE.Views.TableSettings.textHeader": "Başlıq", "PE.Views.TableSettings.textHeight": "Hündürlük", "PE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textRows": "Sə<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Yu<PERSON>r<PERSON>da seçilmiş tətbiq üslubunu dəyişdirmək istədiyiniz sərhədləri seçin", "PE.Views.TableSettings.textTemplate": "Şablondan Seçin", "PE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textWidth": "En", "PE.Views.TableSettings.tipAll": "Xarici sərhədi və bütün daxili xətləri təyin edin", "PE.Views.TableSettings.tipBottom": "<PERSON><PERSON><PERSON><PERSON> xarici alt sərhədi təyin edin", "PE.Views.TableSettings.tipInner": "<PERSON><PERSON><PERSON>z daxili xətləri təyin edin", "PE.Views.TableSettings.tipInnerHor": "<PERSON><PERSON><PERSON>z üfüqi daxili xətləri təyin edin", "PE.Views.TableSettings.tipInnerVert": "<PERSON><PERSON><PERSON>z şaquli daxili xətləri təyin edin", "PE.Views.TableSettings.tipLeft": "<PERSON><PERSON><PERSON><PERSON> xarici sol sərhədi təyin edin", "PE.Views.TableSettings.tipNone": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> olmamasını təyin edin", "PE.Views.TableSettings.tipOuter": "<PERSON><PERSON><PERSON><PERSON> xarici sərhədi təyin edin", "PE.Views.TableSettings.tipRight": "<PERSON><PERSON><PERSON>z xarici sağ sərhədi təyin edin", "PE.Views.TableSettings.tipTop": "<PERSON><PERSON><PERSON><PERSON> xarici üst sərhədi təyin edin", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "PE.Views.TableSettings.txtTable_Accent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_DarkStyle": "Tünd Üslub", "PE.Views.TableSettings.txtTable_LightStyle": "Açıq Üslub", "PE.Views.TableSettings.txtTable_MediumStyle": "Orta Üslub", "PE.Views.TableSettings.txtTable_NoGrid": "Şəbəkə yoxdur", "PE.Views.TableSettings.txtTable_NoStyle": "Üslub yoxdur", "PE.Views.TableSettings.txtTable_TableGrid": "<PERSON>əd<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_ThemedStyle": "Mövzulu Üslub", "PE.Views.TableSettingsAdvanced.textAlt": "Alternativ Mətn", "PE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAltTip": "Şəkildə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya idrak qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Başlıq", "PE.Views.TableSettingsAdvanced.textBottom": "Aşağı", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Defolt kənarları istifadə edin", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textLeft": "Sol", "PE.Views.TableSettingsAdvanced.textMargins": "Xananın <PERSON>", "PE.Views.TableSettingsAdvanced.textRight": "Sağ", "PE.Views.TableSettingsAdvanced.textTitle": "Cədvəl-Tək<PERSON>l Parametrlər", "PE.Views.TableSettingsAdvanced.textTop": "Yuxarı", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "<PERSON><PERSON><PERSON> fon rəngi", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "Ön plan rəngi", "PE.Views.TextArtSettings.strPattern": "Nümunə", "PE.Views.TextArtSettings.strSize": "Ölçü", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strType": "Növ", "PE.Views.TextArtSettings.textAngle": "Bucaq", "PE.Views.TextArtSettings.textBorderSizeErr": "Daxil edilmiş dəyər yanlışdır.<br>0 pt ilə 1584 pt arasında dəyər daxil edin.", "PE.Views.TextArtSettings.textColor": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textDirection": "İstiqamət", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON>ümu<PERSON><PERSON> yoxdur", "PE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textFromUrl": "URL-dən", "PE.Views.TextArtSettings.textGradient": "Qradiyent nöqtələri", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textImageTexture": "Şəkil və ya Tekstura", "PE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textNoFill": "Doldurulmasın", "PE.Views.TextArtSettings.textPatternFill": "Nümunə", "PE.Views.TextArtSettings.textPosition": "Mövq<PERSON>", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "Seç", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON>ır", "PE.Views.TextArtSettings.textStyle": "Üslub", "PE.Views.TextArtSettings.textTemplate": "Şablon", "PE.Views.TextArtSettings.textTexture": "Teksturdan", "PE.Views.TextArtSettings.textTile": "Lövhəcik", "PE.Views.TextArtSettings.textTransform": "Çev<PERSON><PERSON>", "PE.Views.TextArtSettings.tipAddGradientPoint": "Qradiyent nöqtəsi əlavə edin", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Qradiyent nöqtəni silin", "PE.Views.TextArtSettings.txtBrownPaper": "Qəhvəyi Kağız", "PE.Views.TextArtSettings.txtCanvas": "Lövhə", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGrain": "Dən", "PE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON>z <PERSON>", "PE.Views.TextArtSettings.txtKnit": "Bərkitmək", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "Taxta", "PE.Views.Toolbar.capAddSlide": "<PERSON><PERSON>d əlav<PERSON> et", "PE.Views.Toolbar.capBtnAddComment": "Şərh Əlavə Et", "PE.Views.Toolbar.capBtnComment": "Şərh", "PE.Views.Toolbar.capBtnDateTime": "Tarix və Vaxt", "PE.Views.Toolbar.capBtnInsHeader": "Alt-başlıq", "PE.Views.Toolbar.capBtnInsSymbol": "Simvol", "PE.Views.Toolbar.capBtnSlideNum": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertEquation": "Tənlik", "PE.Views.Toolbar.capInsertHyperlink": "Hiperlink", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "PE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "<PERSON><PERSON>", "PE.Views.Toolbar.capTabHome": "<PERSON> səhifə", "PE.Views.Toolbar.capTabInsert": "Daxil edin", "PE.Views.Toolbar.mniCapitalizeWords": "Hər Sözü Böyük Hərflə Yazın", "PE.Views.Toolbar.mniCustomTable": "<PERSON><PERSON>rdi Cədvəl Daxil Edin", "PE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniImageFromUrl": "URL-dən <PERSON>", "PE.Views.Toolbar.mniLowerCase": "kiçik hərf", "PE.Views.Toolbar.mniSentenceCase": "Cümlənin əvvəlində böyük hərf.", "PE.Views.Toolbar.mniSlideAdvanced": "Qabaqcıl Parametrlər", "PE.Views.Toolbar.mniSlideStandard": "<PERSON><PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "bAŞ hƏRFİ DƏYİŞ", "PE.Views.Toolbar.mniUpperCase": "BÖYÜK HƏRF", "PE.Views.Toolbar.strMenuNoFill": "Doldurulmasın", "PE.Views.Toolbar.textAlignBottom": "Mətni aşağıya düzləndir", "PE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON> mətn", "PE.Views.Toolbar.textAlignJust": "Kənarları düzləşdir", "PE.Views.Toolbar.textAlignLeft": "<PERSON>ə<PERSON>ni sola düzləndir", "PE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON><PERSON> orta<PERSON>", "PE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>a düzləndi<PERSON>", "PE.Views.Toolbar.textAlignTop": "Mətni yuxarıya düzün", "PE.Views.Toolbar.textArrangeBack": "Fona göndərin", "PE.Views.Toolbar.textArrangeBackward": "Geriyə Göndərin", "PE.Views.Toolbar.textArrangeForward": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.Toolbar.textArrangeFront": "Ön plana çıxarın", "PE.Views.Toolbar.textBold": "Qalın", "PE.Views.Toolbar.textColumnsCustom": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsOne": "Bir <PERSON>", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON>", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textListSettings": "Siyahı Parametrləri", "PE.Views.Toolbar.textShapeAlignBottom": "Aşağı Düzləndir", "PE.Views.Toolbar.textShapeAlignCenter": "Mərkəzə Düzləndir", "PE.Views.Toolbar.textShapeAlignLeft": "Sola nizamlayın", "PE.Views.Toolbar.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignTop": "Yuxarı Düzləndir", "PE.Views.Toolbar.textShowBegin": "Başlanğıcdan <PERSON>", "PE.Views.Toolbar.textShowCurrent": "<PERSON><PERSON>", "PE.Views.Toolbar.textShowPresenterView": "Təqdimatçı görünüşündə baxın", "PE.Views.Toolbar.textShowSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textStrikeout": "Üstüxətli", "PE.Views.Toolbar.textSubscript": "Aşağı İndeks", "PE.Views.Toolbar.textSuperscript": "Yuxarı İndeks", "PE.Views.Toolbar.textTabCollaboration": "Əməkdaşlıq", "PE.Views.Toolbar.textTabFile": "<PERSON><PERSON>", "PE.Views.Toolbar.textTabHome": "<PERSON> səhifə", "PE.Views.Toolbar.textTabInsert": "Daxil edin", "PE.Views.Toolbar.textTabProtect": "<PERSON>orum<PERSON>", "PE.Views.Toolbar.textTabTransitions": "Ke<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabView": "G<PERSON>rünüş", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textUnderline": "Altından xətt çə<PERSON>lmiş", "PE.Views.Toolbar.tipAddSlide": "<PERSON><PERSON>d əlav<PERSON> et", "PE.Views.Toolbar.tipBack": "<PERSON><PERSON>", "PE.Views.Toolbar.tipChangeCase": "Böyük/kiçik hərfi dəyiş", "PE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON><PERSON><PERSON>n növünü dəyiş", "PE.Views.Toolbar.tipChangeSlide": "Slay<PERSON>ın tərtibatını dəyiş", "PE.Views.Toolbar.tipClearStyle": "Üslubu təmizləyin", "PE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON><PERSON> sxemini də<PERSON>", "PE.Views.Toolbar.tipColumns": "<PERSON>ü<PERSON><PERSON> daxil edin", "PE.Views.Toolbar.tipCopy": "Kopyala", "PE.Views.Toolbar.tipCopyStyle": "Üslubu kopyala", "PE.Views.Toolbar.tipDateTime": "Cari tarix və vaxtı daxil edin", "PE.Views.Toolbar.tipDecFont": "Şriftin ölçüsünü azaldın", "PE.Views.Toolbar.tipDecPrLeft": "Abzası azaldın", "PE.Views.Toolbar.tipEditHeader": "Alt-başlığı redaktə edin", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipFontName": "Şrift", "PE.Views.Toolbar.tipFontSize": "Şrift Ölçüsü", "PE.Views.Toolbar.tipHAligh": "Üfüqi düzülüş", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipIncFont": "Şriftin ölçüsünü artırın", "PE.Views.Toolbar.tipIncPrLeft": "Abzası artır", "PE.Views.Toolbar.tipInsertAudio": "Audio daxil edin", "PE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> daxil edin", "PE.Views.Toolbar.tipInsertEquation": "Tənlik daxil edin", "PE.Views.Toolbar.tipInsertHyperlink": "Hiperlink əlavə edin", "PE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON>i Daxil edin", "PE.Views.Toolbar.tipInsertShape": "AvtoFiquru daxil edin", "PE.Views.Toolbar.tipInsertSymbol": "Simvol əlavə edin", "PE.Views.Toolbar.tipInsertTable": "Cədvəl əlavə edin", "PE.Views.Toolbar.tipInsertText": "Mətn qutusu əlavə edin", "PE.Views.Toolbar.tipInsertTextArt": "Mətn Şəkli Əlavə edin", "PE.Views.Toolbar.tipInsertVideo": "Video daxil edin", "PE.Views.Toolbar.tipLineSpace": "Sətirarası İnterval", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "Nömr<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPaste": "Yapışdır", "PE.Views.Toolbar.tipPreview": "Slay<PERSON>ı başladın", "PE.Views.Toolbar.tipPrint": "Çap", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON> edin", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON>a saxla", "PE.Views.Toolbar.tipSaveCoauth": "Dəyişikliklərinizi digər istifadəçilərin görməsi üçün yadda saxlayın.", "PE.Views.Toolbar.tipShapeAlign": "Formanı düzləndir", "PE.Views.Toolbar.tipShapeArrange": "Formanı düzənlə", "PE.Views.Toolbar.tipSlideNum": "<PERSON>lay<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i daxil edin", "PE.Views.Toolbar.tipSlideSize": "Slayd ölçüs<PERSON>ü <PERSON>", "PE.Views.Toolbar.tipSlideTheme": "Slayd <PERSON>", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Şaquli dü<PERSON>", "PE.Views.Toolbar.tipViewSettings": "Görünüş parametrləri", "PE.Views.Toolbar.txtDistribHor": "Üfüqi olaraq Bölüşdürün", "PE.Views.Toolbar.txtDistribVert": "Şaquli o<PERSON>aq Bölüşdürün", "PE.Views.Toolbar.txtGroup": "Qrup", "PE.Views.Toolbar.txtObjectsAlign": "Seçilmiş Elementlə<PERSON>", "PE.Views.Toolbar.txtScheme1": "<PERSON>is", "PE.Views.Toolbar.txtScheme10": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Mənbə", "PE.Views.Toolbar.txtScheme16": "Kağız", "PE.Views.Toolbar.txtScheme17": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme18": "Texnika", "PE.Views.Toolbar.txtScheme19": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme2": "Boz rəng çalarları", "PE.Views.Toolbar.txtScheme20": "Ş<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme21": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme22": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Aspekt", "PE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme6": "Konkurs", "PE.Views.Toolbar.txtScheme7": "Bərabərlik", "PE.Views.Toolbar.txtScheme8": "Axın", "PE.Views.Toolbar.txtScheme9": "Emalatxana", "PE.Views.Toolbar.txtSlideAlign": "<PERSON><PERSON><PERSON> uyğun dü<PERSON>ləndi<PERSON>", "PE.Views.Toolbar.txtUngroup": "Qruplaşdırmanı ləğv et", "PE.Views.Transitions.strDelay": "Gecikmə", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "Kliklədikdə Başlat", "PE.Views.Transitions.textBlack": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textBottom": "Aşağı", "PE.Views.Transitions.textBottomLeft": "Aşağı-Sol", "PE.Views.Transitions.textBottomRight": "Aşağı-Sağ", "PE.Views.Transitions.textClock": "Saat", "PE.Views.Transitions.textClockwise": "Saat əqrəbi istiqamətində", "PE.Views.Transitions.textCounterclockwise": "Saat əqrə<PERSON>in əksi istiqamətində", "PE.Views.Transitions.textCover": "Örtük", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "Üfüqi Artırma", "PE.Views.Transitions.textHorizontalOut": "Üfüqi Azaltma", "PE.Views.Transitions.textLeft": "Sol", "PE.Views.Transitions.textNone": "<PERSON><PERSON>", "PE.Views.Transitions.textPush": "Bas", "PE.Views.Transitions.textRight": "Sağ", "PE.Views.Transitions.textSmoothly": "Yavaşca", "PE.Views.Transitions.textSplit": "Bölgü", "PE.Views.Transitions.textTop": "Yuxarı", "PE.Views.Transitions.textTopLeft": "Yuxarı-Sol", "PE.Views.Transitions.textTopRight": "Yuxarı-Sağ", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "Şaquli Artırma", "PE.Views.Transitions.textVerticalOut": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textWedge": "<PERSON><PERSON>ır", "PE.Views.Transitions.textWipe": "Görünmə", "PE.Views.Transitions.textZoom": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "PE.Views.Transitions.textZoomIn": "B<PERSON><PERSON>üdün", "PE.Views.Transitions.textZoomOut": "Uzaqlaşdır", "PE.Views.Transitions.textZoomRotate": "Miqyası dəyiş və Fırlat", "PE.Views.Transitions.txtApplyToAll": "Bütün Slaydlara Tətbiq et", "PE.Views.Transitions.txtParameters": "Parametreler", "PE.Views.Transitions.txtPreview": "Önbaxış", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textInterfaceTheme": "İnterfeys mövzusu", "PE.Views.ViewTab.tipInterfaceTheme": "İnterfeys mövzusu"}