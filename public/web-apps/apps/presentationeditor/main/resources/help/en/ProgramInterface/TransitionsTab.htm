﻿<!DOCTYPE html>
<html>
	<head>
		<title>Transitions tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface - Transitions tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Transitions tab</h1>
            <p>The <b>Transitions</b> tab in the <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> allows you to manage slide transitions. You can add transition effects, set the transition speed and configure other slide transition parameters to customize your presentation.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Presentation Editor:</p>
                <p><img alt="Transitions tab" src="../images/interface/transitionstab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Presentation Editor:</p>
                <p><img alt="Transitions tab" src="../images/interface/desktop_transitionstab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li>select a <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">transition effect</a>,</li>
                <li>set appropriate <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">parameter values</a> for each transition effect,</li>
                <li>define a <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">transition duration</a>,</li>
                <li> <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">preview a transition</a> after setup,</li>
                <li>specify how long you want the slide to be displayed by checking the <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Start on click and Delay options</a>,</li>
                <li>apply the transition to all slides by clicking the <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Apply to all Slides</a> button.</li>
            </ul>
		</div>
	</body>
</html>