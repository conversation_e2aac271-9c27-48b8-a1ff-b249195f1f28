{"Common.Controllers.Chat.notcriticalErrorTitle": "Avertissement", "Common.Controllers.Chat.textEnterMessage": "Entrez votre message ici", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonyme", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "L'objet est désactivé car il est en cours d'être modifié par un autre utilisateur.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Avertissement", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonyme", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "L'objet est désactivé, car il est en cours de modification par un autre utilisateur.", "Common.Controllers.ExternalOleEditor.warningTitle": "Avertissement", "Common.define.chartData.textArea": "En aires", "Common.define.chartData.textAreaStacked": "Zone empilée", "Common.define.chartData.textAreaStackedPer": "Aire Empilée 100%", "Common.define.chartData.textBar": "En barre", "Common.define.chartData.textBarNormal": "colonne groupée ", "Common.define.chartData.textBarNormal3d": "Barres groupées en 3D", "Common.define.chartData.textBarNormal3dPerspective": "Colonne 3D", "Common.define.chartData.textBarStacked": "Colonne empi<PERSON>", "Common.define.chartData.textBarStacked3d": "Histogramme empilé en 3D", "Common.define.chartData.textBarStackedPer": "100% colonne empilée", "Common.define.chartData.textBarStackedPer3d": "3-D 100% colonne empilée", "Common.define.chartData.textCharts": "Graphiques", "Common.define.chartData.textColumn": "Colonne", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Zone empilée - colonne en cluster ", "Common.define.chartData.textComboBarLine": "Colonne - ligne groupée", "Common.define.chartData.textComboBarLineSecondary": "Colonne ligne groupée sur le second axe", "Common.define.chartData.textComboCustom": "Combinaison personnalisée", "Common.define.chartData.textDoughnut": "Donut", "Common.define.chartData.textHBarNormal": "barre groupée ", "Common.define.chartData.textHBarNormal3d": "Barres groupées en 3D", "Common.define.chartData.textHBarStacked": "Barre empilée", "Common.define.chartData.textHBarStacked3d": "Barres empilées en 3D", "Common.define.chartData.textHBarStackedPer": "100% barre empilée", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% barre empilée", "Common.define.chartData.textLine": "Graphique en ligne", "Common.define.chartData.textLine3d": "ligne 3D", "Common.define.chartData.textLineMarker": "Ligne avec marqueurs", "Common.define.chartData.textLineStacked": "ligne empilée", "Common.define.chartData.textLineStackedMarker": "ligne empilée avec marqueurs", "Common.define.chartData.textLineStackedPer": "100% ligne empilée", "Common.define.chartData.textLineStackedPerMarker": "100% ligne empilée avec marqueurs", "Common.define.chartData.textPie": "Graphiques à secteurs", "Common.define.chartData.textPie3d": "<PERSON>mbert 3D", "Common.define.chartData.textPoint": "Nuages de points (XY)", "Common.define.chartData.textScatter": "Disperser", "Common.define.chartData.textScatterLine": "disperser avec des lignes droites ", "Common.define.chartData.textScatterLineMarker": "disperser avec des lignes droites", "Common.define.chartData.textScatterSmooth": "disperser avec des lignes lisses ", "Common.define.chartData.textScatterSmoothMarker": "disperser avec des lignes lisses et marqueurs", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "Surface", "Common.define.effectData.textAcross": "Horizontalement", "Common.define.effectData.textAppear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textArcDown": "Arc vers le bas", "Common.define.effectData.textArcLeft": "Arc à gauche", "Common.define.effectData.textArcRight": "Arc à droite", "Common.define.effectData.textArcs": "Arcs", "Common.define.effectData.textArcUp": "Arc vers le haut", "Common.define.effectData.textBasic": "Simple", "Common.define.effectData.textBasicSwivel": "Rotation de base", "Common.define.effectData.textBasicZoom": "Zoom simple", "Common.define.effectData.textBean": "<PERSON><PERSON>", "Common.define.effectData.textBlinds": "Stores", "Common.define.effectData.textBlink": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBoldFlash": "<PERSON>", "Common.define.effectData.textBoldReveal": "Faire ressortir le gras", "Common.define.effectData.textBoomerang": "Boomerang", "Common.define.effectData.textBounce": "Rebondir", "Common.define.effectData.textBounceLeft": "Rebondir à gauche", "Common.define.effectData.textBounceRight": "Rebondir à droite", "Common.define.effectData.textBox": "Carré", "Common.define.effectData.textBrushColor": "<PERSON><PERSON><PERSON> <PERSON>", "Common.define.effectData.textCenterRevolve": "Rotation au centre", "Common.define.effectData.textCheckerboard": "<PERSON><PERSON>", "Common.define.effectData.textCircle": "Cercle", "Common.define.effectData.textCollapse": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textColorPulse": "Impulsion couleur", "Common.define.effectData.textComplementaryColor": "Couleur complémentaire", "Common.define.effectData.textComplementaryColor2": "Couleur complémentaire 2", "Common.define.effectData.textCompress": "Compresser", "Common.define.effectData.textContrast": "Contraste", "Common.define.effectData.textContrastingColor": "<PERSON><PERSON><PERSON> de contraste", "Common.define.effectData.textCredits": "Crédits", "Common.define.effectData.textCrescentMoon": "Croissant de lune", "Common.define.effectData.textCurveDown": "Courbe vers le bas", "Common.define.effectData.textCurvedSquare": "<PERSON><PERSON> cour<PERSON>", "Common.define.effectData.textCurvedX": "X courbé", "Common.define.effectData.textCurvyLeft": "Gauche courbée", "Common.define.effectData.textCurvyRight": "<PERSON><PERSON><PERSON> cour<PERSON>", "Common.define.effectData.textCurvyStar": "<PERSON><PERSON><PERSON> co<PERSON>", "Common.define.effectData.textCustomPath": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCuverUp": "Courbe vers le haut", "Common.define.effectData.textDarken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDecayingWave": "Vague tombante", "Common.define.effectData.textDesaturate": "Désaturer", "Common.define.effectData.textDiagonalDownRight": "Diagonale bas à droite", "Common.define.effectData.textDiagonalUpRight": "Diagonal haut à droite", "Common.define.effectData.textDiamond": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDisappear": "<PERSON>sp<PERSON><PERSON><PERSON>", "Common.define.effectData.textDissolveIn": "Dissolution interne", "Common.define.effectData.textDissolveOut": "Dissolution externe", "Common.define.effectData.textDown": "Bas", "Common.define.effectData.textDrop": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textEmphasis": "Effets d’accentuation", "Common.define.effectData.textEntrance": "Effets d’entrée", "Common.define.effectData.textEqualTriangle": "Triangle équilatéral", "Common.define.effectData.textExciting": "Captivant", "Common.define.effectData.textExit": "Effets de sortie", "Common.define.effectData.textExpand": "Développer", "Common.define.effectData.textFade": "Fond<PERSON>", "Common.define.effectData.textFigureFour": "Figure quatre 8", "Common.define.effectData.textFillColor": "Couleur de remplissage", "Common.define.effectData.textFlip": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFloat": "Flottant", "Common.define.effectData.textFloatDown": "Flottant vers le bas", "Common.define.effectData.textFloatIn": "Flottant entrant", "Common.define.effectData.textFloatOut": "Flottant sortant", "Common.define.effectData.textFloatUp": "Flottant vers le haut", "Common.define.effectData.textFlyIn": "Passage vers l’intérieur", "Common.define.effectData.textFlyOut": "Passage vers l’extérieur", "Common.define.effectData.textFontColor": "Couleur de police", "Common.define.effectData.textFootball": "Football", "Common.define.effectData.textFromBottom": "À partir du bas", "Common.define.effectData.textFromBottomLeft": "À partir du coin inférieur gauche", "Common.define.effectData.textFromBottomRight": "À partir du coin inférieur droit", "Common.define.effectData.textFromLeft": "À partir de la gauche", "Common.define.effectData.textFromRight": "À partir de la droite", "Common.define.effectData.textFromTop": "À partir du haut", "Common.define.effectData.textFromTopLeft": "À partir du coin supérieur gauche", "Common.define.effectData.textFromTopRight": "À partir du coin supérieur droit", "Common.define.effectData.textFunnel": "Entonnoir", "Common.define.effectData.textGrowShrink": "Agrandir/Rétr<PERSON>", "Common.define.effectData.textGrowTurn": "Agrandir et tourner", "Common.define.effectData.textGrowWithColor": "Agrandir avec de la couleur", "Common.define.effectData.textHeart": "Coeur", "Common.define.effectData.textHeartbeat": "Pulsation", "Common.define.effectData.textHexagon": "Hexagone", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Figure 8 horizontal", "Common.define.effectData.textHorizontalIn": "Horizontal intérieur", "Common.define.effectData.textHorizontalOut": "Horizontal extérieur", "Common.define.effectData.textIn": "Vers l’intérieur", "Common.define.effectData.textInFromScreenCenter": "Avant depuis le centre de l’écran", "Common.define.effectData.textInSlightly": "Avant léger", "Common.define.effectData.textInToScreenBottom": "Zoomer vers le bas de l'écran", "Common.define.effectData.textInvertedSquare": "Carré inversé", "Common.define.effectData.textInvertedTriangle": "Triangle inversé", "Common.define.effectData.textLeft": "G<PERSON><PERSON>", "Common.define.effectData.textLeftDown": "De gauche vers le bas", "Common.define.effectData.textLeftUp": "De gauche vers le haut", "Common.define.effectData.textLighten": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textLineColor": "<PERSON><PERSON><PERSON> du <PERSON>", "Common.define.effectData.textLines": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLoopDeLoop": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLoops": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textModerate": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "Centre de l’objet", "Common.define.effectData.textObjectColor": "Couleur de l’objet", "Common.define.effectData.textOctagon": "Octogone", "Common.define.effectData.textOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOutFromScreenBottom": "Arrière depuis le bas de l’écran", "Common.define.effectData.textOutSlightly": "<PERSON><PERSON><PERSON> l<PERSON>", "Common.define.effectData.textOutToScreenCenter": "Diminuer au centre de l'écran", "Common.define.effectData.textParallelogram": "Parallélogramme", "Common.define.effectData.textPath": "Trajectoires du mouvement", "Common.define.effectData.textPathCurve": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPathLine": "Ligne", "Common.define.effectData.textPathScribble": "Dessin à main levée", "Common.define.effectData.textPeanut": "Cacahuète", "Common.define.effectData.textPeekIn": "Insertion furtive", "Common.define.effectData.textPeekOut": "Sortie furtive", "Common.define.effectData.textPentagon": "Pentagone", "Common.define.effectData.textPinwheel": "Moulin à vent", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPointStar4": "Étoile à 4 branches", "Common.define.effectData.textPointStar5": "Étoile à 5 branches", "Common.define.effectData.textPointStar6": "Étoile à 6 branches", "Common.define.effectData.textPointStar8": "Étoile à 8 branches", "Common.define.effectData.textPulse": "Pulsation", "Common.define.effectData.textRandomBars": "Barres aléatoires", "Common.define.effectData.textRight": "À droite", "Common.define.effectData.textRightDown": "De droite vers le bas", "Common.define.effectData.textRightTriangle": "Triangle rectangle", "Common.define.effectData.textRightUp": "De droite vers le haut", "Common.define.effectData.textRiseUp": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSCurve1": "Courbe S 1", "Common.define.effectData.textSCurve2": "Courbe S 2", "Common.define.effectData.textShape": "Forme", "Common.define.effectData.textShapes": "Formes", "Common.define.effectData.textShimmer": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShrinkTurn": "Rétrécir et faire pivoter", "Common.define.effectData.textSineWave": "Vague sinuso<PERSON>dale", "Common.define.effectData.textSinkDown": "Chute", "Common.define.effectData.textSlideCenter": "Centre de la diapositive", "Common.define.effectData.textSpecial": "Spécial", "Common.define.effectData.textSpin": "Rotation", "Common.define.effectData.textSpinner": "Tourbillon", "Common.define.effectData.textSpiralIn": "Spirale vers l'intérieur", "Common.define.effectData.textSpiralLeft": "Spirale à gauche", "Common.define.effectData.textSpiralOut": "Spirale vers l'extérieur", "Common.define.effectData.textSpiralRight": "Spirale à droite", "Common.define.effectData.textSplit": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpoke1": "1 rayon", "Common.define.effectData.textSpoke2": "2 rayons", "Common.define.effectData.textSpoke3": "3 rayons", "Common.define.effectData.textSpoke4": "4 rayons", "Common.define.effectData.textSpoke8": "8 rayons", "Common.define.effectData.textSpring": "Ressort", "Common.define.effectData.textSquare": "Carré", "Common.define.effectData.textStairsDown": "Escalier descendant", "Common.define.effectData.textStretch": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textStrips": "Bandes", "Common.define.effectData.textSubtle": "Discret", "Common.define.effectData.textSwivel": "Pivotant", "Common.define.effectData.textSwoosh": "Swoosh", "Common.define.effectData.textTeardrop": "<PERSON><PERSON><PERSON> ", "Common.define.effectData.textTeeter": "<PERSON><PERSON>", "Common.define.effectData.textToBottom": "Vers le bas", "Common.define.effectData.textToBottomLeft": "Vers le coin inférieur gauche", "Common.define.effectData.textToBottomRight": "Vers le coin inférieur droit", "Common.define.effectData.textToLeft": "Vers la gauche", "Common.define.effectData.textToRight": "Vers la droite", "Common.define.effectData.textToTop": "Vers le haut", "Common.define.effectData.textToTopLeft": "Vers le coin supérieur gauche", "Common.define.effectData.textToTopRight": "Vers le coins supérieur droit", "Common.define.effectData.textTransparency": "Transparence", "Common.define.effectData.textTrapezoid": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textTurnDown": "<PERSON>ner vers le bas", "Common.define.effectData.textTurnDownRight": "<PERSON>ner vers le bas à droite", "Common.define.effectData.textTurns": "Tours", "Common.define.effectData.textTurnUp": "<PERSON>ner vers le haut", "Common.define.effectData.textTurnUpRight": "<PERSON>ner vers le haut à droite", "Common.define.effectData.textUnderline": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textUp": "En haut", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Figure 8 vertical", "Common.define.effectData.textVerticalIn": "Verticalement vers l’avant", "Common.define.effectData.textVerticalOut": "Verticalement vers l’arrière", "Common.define.effectData.textWave": "<PERSON>de", "Common.define.effectData.textWedge": "Coin", "Common.define.effectData.textWheel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWhip": "Fouet", "Common.define.effectData.textWipe": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "Image accentuée", "Common.define.smartArt.textAccentProcess": "Processus accentué", "Common.define.smartArt.textAlternatingFlow": "Flux interactif", "Common.define.smartArt.textAlternatingHexagons": "Hexagones alternés", "Common.define.smartArt.textAlternatingPictureBlocks": "Blocs d'images alternées", "Common.define.smartArt.textAlternatingPictureCircles": "Cercles d'images alternées", "Common.define.smartArt.textArchitectureLayout": "Disposition architecture", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON> fl<PERSON>", "Common.define.smartArt.textAscendingPictureAccentProcess": "Processus accentué dans un ordre croissant avec images", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Processus en lacet simple", "Common.define.smartArt.textBasicBlockList": "Liste de blocs simple", "Common.define.smartArt.textBasicChevronProcess": "Processus en chevrons simple", "Common.define.smartArt.textBasicCycle": "Cycle simple", "Common.define.smartArt.textBasicMatrix": "Matrice simple", "Common.define.smartArt.textBasicPie": "Graphique en secteurs simple", "Common.define.smartArt.textBasicProcess": "Processus simple", "Common.define.smartArt.textBasicPyramid": "Pyramide simple", "Common.define.smartArt.textBasicRadial": "Radial simple", "Common.define.smartArt.textBasicTarget": "Cible simple", "Common.define.smartArt.textBasicTimeline": "Chronologie simple", "Common.define.smartArt.textBasicVenn": "Venn simple", "Common.define.smartArt.textBendingPictureAccentList": "Liste accentuée en lacet avec images", "Common.define.smartArt.textBendingPictureBlocks": "Blocs en lacet avec images", "Common.define.smartArt.textBendingPictureCaption": "Images en lacet avec légendes", "Common.define.smartArt.textBendingPictureCaptionList": "Liste d’images en lacet avec légendes", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Texte semi-transparent en lacet avec images", "Common.define.smartArt.textBlockCycle": "Cycle en blocs", "Common.define.smartArt.textBubblePictureList": "Liste d’images avec bulles", "Common.define.smartArt.textCaptionedPictures": "Images avec légende", "Common.define.smartArt.textChevronAccentProcess": "Processus accentué en chevrons", "Common.define.smartArt.textChevronList": "Liste de chevrons", "Common.define.smartArt.textCircleAccentTimeline": "Barre de planning accentuée avec cercles", "Common.define.smartArt.textCircleArrowProcess": "Processus en flèches circulaires", "Common.define.smartArt.textCirclePictureHierarchy": "Hiérarchie avec images rondes", "Common.define.smartArt.textCircleProcess": "Processus en cercles", "Common.define.smartArt.textCircleRelationship": "Relation circulaire", "Common.define.smartArt.textCircularBendingProcess": "Processus en lacets avec bulles", "Common.define.smartArt.textCircularPictureCallout": "Images circulaires avec légende", "Common.define.smartArt.textClosedChevronProcess": "Processus en chevrons fermés", "Common.define.smartArt.textContinuousArrowProcess": "Processus en flèche continue", "Common.define.smartArt.textContinuousBlockProcess": "Processus en bloc continu", "Common.define.smartArt.textContinuousCycle": "Cycle continu", "Common.define.smartArt.textContinuousPictureList": "Liste continue avec images", "Common.define.smartArt.textConvergingArrows": "Flèches convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergeant", "Common.define.smartArt.textConvergingText": "Texte convergent", "Common.define.smartArt.textCounterbalanceArrows": "Flèches d’équilibrage", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "<PERSON>rice de <PERSON>", "Common.define.smartArt.textDescendingBlockList": "Liste de blocs décroissante", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDivergingArrows": "Flèches divergentes", "Common.define.smartArt.textDivergingRadial": "Radial convergeant", "Common.define.smartArt.textEquation": "Équation", "Common.define.smartArt.textFramedTextPicture": "Images avec texte en encadré", "Common.define.smartArt.textFunnel": "Entonnoir", "Common.define.smartArt.textGear": "Engrenage", "Common.define.smartArt.textGridMatrix": "Matrice avec grille", "Common.define.smartArt.textGroupedList": "Liste groupée", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organigramme avec demi-cercles", "Common.define.smartArt.textHexagonCluster": "Groupe d’hexagones", "Common.define.smartArt.textHexagonRadial": "Hexagone radial", "Common.define.smartArt.textHierarchy": "Hiéra<PERSON>ie", "Common.define.smartArt.textHierarchyList": "Liste hiérarchique", "Common.define.smartArt.textHorizontalBulletList": "Liste à puces horizontale", "Common.define.smartArt.textHorizontalHierarchy": "Hiérarchie horizontale", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hiérarchie horizontale avec étiquettes", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hiérarchie horizontale à plusieurs niveaux", "Common.define.smartArt.textHorizontalOrganizationChart": "Organigramme horizontal", "Common.define.smartArt.textHorizontalPictureList": "Liste horizontale avec images", "Common.define.smartArt.textIncreasingArrowProcess": "Processus en flèches croissant", "Common.define.smartArt.textIncreasingCircleProcess": "Processus ascendant avec cercles", "Common.define.smartArt.textInterconnectedBlockProcess": "Processus en blocs interconnectés", "Common.define.smartArt.textInterconnectedRings": "Anneaux interconnectés", "Common.define.smartArt.textInvertedPyramid": "Pyramide inversée", "Common.define.smartArt.textLabeledHierarchy": "Hiérarchie libellée", "Common.define.smartArt.textLinearVenn": "Venn lin<PERSON>", "Common.define.smartArt.textLinedList": "Liste alignée", "Common.define.smartArt.textList": "Liste", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Cycle multidirectionnel", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigramme avec titre et nom", "Common.define.smartArt.textNestedTarget": "Cible imbriquée", "Common.define.smartArt.textNondirectionalCycle": "Cycle non directionnel", "Common.define.smartArt.textOpposingArrows": "Flèches opposées", "Common.define.smartArt.textOpposingIdeas": "Idées opposées", "Common.define.smartArt.textOrganizationChart": "Organigramme", "Common.define.smartArt.textOther": "<PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Processus à phases", "Common.define.smartArt.textPicture": "Image", "Common.define.smartArt.textPictureAccentBlocks": "Blocs accentués avec images", "Common.define.smartArt.textPictureAccentList": "Liste accentuée avec images", "Common.define.smartArt.textPictureAccentProcess": "Processus accentué avec images", "Common.define.smartArt.textPictureCaptionList": "Liste de légendes d'images", "Common.define.smartArt.textPictureFrame": "Cadre de l’image", "Common.define.smartArt.textPictureGrid": "Grille d’images", "Common.define.smartArt.textPictureLineup": "Alignement d’images", "Common.define.smartArt.textPictureOrganizationChart": "Organigramme avec images", "Common.define.smartArt.textPictureStrips": "Bandes avec images", "Common.define.smartArt.textPieProcess": "Processus à secteurs", "Common.define.smartArt.textPlusAndMinus": "Plus et moins", "Common.define.smartArt.textProcess": "Processus", "Common.define.smartArt.textProcessArrows": "Processus en flèches", "Common.define.smartArt.textProcessList": "Liste de processus", "Common.define.smartArt.textPyramid": "Pyramide", "Common.define.smartArt.textPyramidList": "Liste pyramidale", "Common.define.smartArt.textRadialCluster": "Groupe en rayon", "Common.define.smartArt.textRadialCycle": "Cycle radial", "Common.define.smartArt.textRadialList": "Liste radiale", "Common.define.smartArt.textRadialPictureList": "Liste radiale avec images", "Common.define.smartArt.textRadialVenn": "Venn radial", "Common.define.smartArt.textRandomToResultProcess": "Processus d’idées aléatoires avec résultat", "Common.define.smartArt.textRelationship": "Relation", "Common.define.smartArt.textRepeatingBendingProcess": "Processus en lacets", "Common.define.smartArt.textReverseList": "Liste inversée", "Common.define.smartArt.textSegmentedCycle": "Cycle segmenté", "Common.define.smartArt.textSegmentedProcess": "Processus segmenté", "Common.define.smartArt.textSegmentedPyramid": "Pyramide segmentée", "Common.define.smartArt.textSnapshotPictureList": "Liste d’images instantanées", "Common.define.smartArt.textSpiralPicture": "Images en spirale", "Common.define.smartArt.textSquareAccentList": "Liste accentuée avec carrés", "Common.define.smartArt.textStackedList": "Liste empilée", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepDownProcess": "Processus descendant", "Common.define.smartArt.textStepUpProcess": "Processus ascendant", "Common.define.smartArt.textSubStepProcess": "Processus avec sous-étapes", "Common.define.smartArt.textTabbedArc": "Arc à onglets", "Common.define.smartArt.textTableHierarchy": "Hiérarchie de tables", "Common.define.smartArt.textTableList": "Liste de tables", "Common.define.smartArt.textTabList": "Liste des onglets", "Common.define.smartArt.textTargetList": "Liste cible", "Common.define.smartArt.textTextCycle": "Cycle de texte", "Common.define.smartArt.textThemePictureAccent": "Images de thème accentué", "Common.define.smartArt.textThemePictureAlternatingAccent": "Images de thème alternées accentué", "Common.define.smartArt.textThemePictureGrid": "Grille d’images de thème", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON> avec titres", "Common.define.smartArt.textTitledPictureAccentList": "Liste accentuée avec images et titre", "Common.define.smartArt.textTitledPictureBlocks": "Blocs d’images avec titre", "Common.define.smartArt.textTitlePictureLineup": "Alignement d’images avec titre", "Common.define.smartArt.textTrapezoidList": "Liste trapézo<PERSON>dale", "Common.define.smartArt.textUpwardArrow": "Flèche vers le haut", "Common.define.smartArt.textVaryingWidthList": "Liste à largeur variable", "Common.define.smartArt.textVerticalAccentList": "Liste accentuée verticale", "Common.define.smartArt.textVerticalArrowList": "Liste verticale avec flèches", "Common.define.smartArt.textVerticalBendingProcess": "Processus vertical en lacet", "Common.define.smartArt.textVerticalBlockList": "Liste de blocs verticale", "Common.define.smartArt.textVerticalBoxList": "Liste de zones verticale", "Common.define.smartArt.textVerticalBracketList": "Liste de crochets verticale", "Common.define.smartArt.textVerticalBulletList": "Liste à puces verticale", "Common.define.smartArt.textVerticalChevronList": "Liste de chevrons verticale", "Common.define.smartArt.textVerticalCircleList": "Liste de cercles verticale", "Common.define.smartArt.textVerticalCurvedList": "Liste courbe verticale", "Common.define.smartArt.textVerticalEquation": "Équation verticale", "Common.define.smartArt.textVerticalPictureAccentList": "Liste accentuée verticale avec images", "Common.define.smartArt.textVerticalPictureList": "Liste d’images verticale", "Common.define.smartArt.textVerticalProcess": "Processus vertical", "Common.Translation.textMoreButton": "Plus", "Common.Translation.tipFileLocked": "Le document est verrouillé pour l'édition. Vous pouvez apporter des modifications et l'enregistrer comme copie locale ultérieurement.", "Common.Translation.tipFileReadOnly": "Le fichier est disponible en lecture seule. Pour sauvegarder vos modifications, enregistrez le fichier sous un nouveau nom ou à un autre endroit.", "Common.Translation.warnFileLocked": "Ce fichier a été modifié avec une autre application. Vous pouvez continuer à le modifier et l'enregistrer comme une copie.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>er une copie", "Common.Translation.warnFileLockedBtnView": "Ouvrir pour visualisation", "Common.UI.ButtonColored.textAutoColor": "Automatique", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> person<PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Pas de bordures", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Pas de bordures", "Common.UI.ComboDataView.emptyComboText": "Aucun style", "Common.UI.ExtendedColorDialog.addButtonText": "Ajouter", "Common.UI.ExtendedColorDialog.textCurrent": "Actuel", "Common.UI.ExtendedColorDialog.textHexErr": "La valeur saisie est incorrecte. <br>Entrez une valeur de 000000 à FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nouveau", "Common.UI.ExtendedColorDialog.textRGBErr": "La valeur saisie est incorrecte. <br>Entrez une valeur numérique de 0 à 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> de couleur", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Masquer le mot de passe", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Afficher le mot de passe", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON>er la recherche", "Common.UI.SearchBar.tipNextResult": "Résultat suivant", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres avancés", "Common.UI.SearchBar.tipPreviousResult": "Résultat précédent", "Common.UI.SearchDialog.textHighlight": "Surligner les résultats", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON> sensible", "Common.UI.SearchDialog.textReplaceDef": "Saisis<PERSON>z le texte de remplacement", "Common.UI.SearchDialog.textSearchStart": "Entrez votre texte ici", "Common.UI.SearchDialog.textTitle": "Re<PERSON><PERSON> et remplacer", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Seulement les mots entiers", "Common.UI.SearchDialog.txtBtnHideReplace": "Masquer le champ de remplacement", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON>lace<PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Remplacer tout", "Common.UI.SynchronizeTip.textDontShow": "Ne plus afficher ce message", "Common.UI.SynchronizeTip.textSynchronize": "Le document a été modifié par un autre utilisateur.<br>Cliquez pour enregistrer vos modifications et recharger les mises à jour.", "Common.UI.ThemeColorPalette.textRecentColors": "Couleurs récentes", "Common.UI.ThemeColorPalette.textStandartColors": "Couleurs standard", "Common.UI.ThemeColorPalette.textThemeColors": "Couleurs de thème", "Common.UI.Themes.txtThemeClassicLight": "Classique clair", "Common.UI.Themes.txtThemeContrastDark": "Contraste élevé sombre", "Common.UI.Themes.txtThemeDark": "Sombre", "Common.UI.Themes.txtThemeLight": "<PERSON>", "Common.UI.Themes.txtThemeSystem": "Identique à système", "Common.UI.Window.cancelButtonText": "Annuler", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Non", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Ne plus afficher ce message", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Avertissement", "Common.UI.Window.yesButtonText": "O<PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "adresse: ", "Common.Views.About.txtLicensee": "CESSIONNAIRE", "Common.Views.About.txtLicensor": "CONCÉDANT", "Common.Views.About.txtMail": "émail: ", "Common.Views.About.txtPoweredBy": "Réalisation", "Common.Views.About.txtTel": "tél.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Ajouter", "Common.Views.AutoCorrectDialog.textApplyText": "Appliquer pendant la frappe", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Correction automatique de texte", "Common.Views.AutoCorrectDialog.textAutoFormat": "Mise en forme automatique au cours de la frappe", "Common.Views.AutoCorrectDialog.textBulleted": "Listes à puces automatiques", "Common.Views.AutoCorrectDialog.textBy": "Par", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Ajouter un point avec un double espace", "Common.Views.AutoCorrectDialog.textFLCells": "Mettre la première lettre des cellules du tableau en majuscule", "Common.Views.AutoCorrectDialog.textFLSentence": "Majuscule en début de phrase", "Common.Views.AutoCorrectDialog.textHyperlink": "Adresses Internet et réseau avec des liens hypertextes", "Common.Views.AutoCorrectDialog.textHyphens": "Traits d’union (--) par un tiret (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "AutoMaths", "Common.Views.AutoCorrectDialog.textNumbered": "Listes numérotées automatiquement", "Common.Views.AutoCorrectDialog.textQuotes": "Guillemets dactylographiques par guillemets typographiques", "Common.Views.AutoCorrectDialog.textRecognized": "Fonctions reconnues", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Les expressions suivantes sont les expressions mathématiques reconnues. Elles ne seront pas mises en italique automatiquement.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON>lace<PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Remplacer pendant la frappe", "Common.Views.AutoCorrectDialog.textReplaceType": "Remp<PERSON><PERSON> le texte au cours de la frappe", "Common.Views.AutoCorrectDialog.textReset": "Réinitialiser", "Common.Views.AutoCorrectDialog.textResetAll": "Rétablir paramètres par défaut", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Correction automatique", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Fonctions reconnues doivent contenir seulement des lettres de A à Z, majuscules et minuscules.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Toute expression que vous avez ajoutée sera supprimée et toute expression que vous avez supprimée sera restaurée. Voulez-vous continuer ?", "Common.Views.AutoCorrectDialog.warnReplace": "Ce symbol d'autocorrection pour 1% existe déjà. Voulez-vous le remplacer ?", "Common.Views.AutoCorrectDialog.warnReset": "Toutes les autocorrections que vous avez ajouté seront supprimées et celles que vous avez modifiées seront réinitialisées à leurs valeurs originales. Voulez-vous continuer ?", "Common.Views.AutoCorrectDialog.warnRestore": "Ce symbol d'autocorrection pour 1% sera réinitialisé à sa valeur initiale. Voulez-vous continuer ?", "Common.Views.Chat.textSend": "Envoyer", "Common.Views.Comments.mniAuthorAsc": "Auteur de A à Z", "Common.Views.Comments.mniAuthorDesc": "Auteur de Z à A", "Common.Views.Comments.mniDateAsc": "Plus ancien", "Common.Views.Comments.mniDateDesc": "Plus récent", "Common.Views.Comments.mniFilterGroups": "Filtrer par groupe", "Common.Views.Comments.mniPositionAsc": "Du haut", "Common.Views.Comments.mniPositionDesc": "Du bas", "Common.Views.Comments.textAdd": "Ajouter", "Common.Views.Comments.textAddComment": "Ajouter", "Common.Views.Comments.textAddCommentToDoc": "Ajouter un commentaire au document", "Common.Views.Comments.textAddReply": "Ajouter une réponse", "Common.Views.Comments.textAll": "<PERSON>ut", "Common.Views.Comments.textAnonym": "Invi<PERSON>", "Common.Views.Comments.textCancel": "Annuler", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> les commentaires", "Common.Views.Comments.textComments": "Commentaires", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Entrez votre commentaire ici", "Common.Views.Comments.textHintAddComment": "Ajouter un commentaire", "Common.Views.Comments.textOpenAgain": "Ouvrir à nouveau", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "R<PERSON>ol<PERSON>", "Common.Views.Comments.textSort": "Trier les commentaires", "Common.Views.Comments.textViewResolved": "Vous n'avez pas la permission de rouvrir le commentaire", "Common.Views.Comments.txtEmpty": "Il n'y a pas de commentaires dans le document.", "Common.Views.CopyWarningDialog.textDontShow": "Ne plus afficher ce message", "Common.Views.CopyWarningDialog.textMsg": "Vous pouvez réaliser les actions de copier, couper et coller en utilisant les boutons de la barre d'outils et à l'aide du menu contextuel à partir de cet onglet uniquement.<br><br>Pour copier ou coller de / vers les applications en dehors de l'onglet de l'éditeur, utilisez les combinaisons de touches suivantes :", "Common.Views.CopyWarningDialog.textTitle": "Actions copier, couper et coller", "Common.Views.CopyWarningDialog.textToCopy": "pour <PERSON><PERSON>r", "Common.Views.CopyWarningDialog.textToCut": "pour Couper", "Common.Views.CopyWarningDialog.textToPaste": "pour <PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "Chargement en cours...", "Common.Views.DocumentAccessDialog.textTitle": "Paramètres de partage", "Common.Views.ExternalDiagramEditor.textTitle": "Éditeur de graphique", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Enregistrer et quitter", "Common.Views.ExternalOleEditor.textTitle": "Tableur", "Common.Views.Header.labelCoUsersDescr": "Le document est en cours de modification par les utilisateurs suivants :", "Common.Views.Header.textAddFavorite": "Marquer en tant que favori", "Common.Views.Header.textAdvSettings": "Paramètres avancés", "Common.Views.Header.textBack": "<PERSON>u<PERSON><PERSON>r l'emplacement du fichier", "Common.Views.Header.textCompactView": "Masquer la barre d'outils", "Common.Views.Header.textHideLines": "Masquer les règles", "Common.Views.Header.textHideNotes": "Masquer les notes", "Common.Views.Header.textHideStatusBar": "Masquer la barre d'état", "Common.Views.Header.textReadOnly": "Lecture seule", "Common.Views.Header.textRemoveFavorite": "Enlever des favoris", "Common.Views.Header.textSaveBegin": "Enregistrement en cours...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Toutes les modifications sont enregistrées", "Common.Views.Header.textSaveExpander": "Toutes les modifications sont enregistrées", "Common.Views.Header.textShare": "Partager", "Common.Views.Header.textZoom": "Grossissement", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON>rer les droits d'accès au document", "Common.Views.Header.tipDownload": "Télécharger le fichier", "Common.Views.Header.tipGoEdit": "Modifier le fichier courant", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON> le fi<PERSON>er", "Common.Views.Header.tipPrintQuick": "Impression rapide", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Enregistrer", "Common.Views.Header.tipSearch": "Recherche", "Common.Views.Header.tipUndo": "Annuler", "Common.Views.Header.tipUndock": "Détacher en fenêtre séparée", "Common.Views.Header.tipUsers": "Afficher les utilisateurs", "Common.Views.Header.tipViewSettings": "Paramètres d'affichage", "Common.Views.Header.tipViewUsers": "Afficher les utilisateurs et gérer les droits d'accès aux documents", "Common.Views.Header.txtAccessRights": "Modifier les droits d'accès", "Common.Views.Header.txtRename": "<PERSON>mmer", "Common.Views.History.textCloseHistory": "Fermer l'historique", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Masquer les modifications détaillées", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Développer", "Common.Views.History.textShowAll": "Afficher les modifications détaillées", "Common.Views.History.textVer": "ver. ", "Common.Views.ImageFromUrlDialog.textUrl": "Coller URL d'image:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Champ obligatoire", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ce champ doit être une URL au format \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Specifiez les lignes valides et le total des colonnes.", "Common.Views.InsertTableDialog.txtColumns": "Nombre de colonnes", "Common.Views.InsertTableDialog.txtMaxText": "La valeur maximale pour ce champ est {0}.", "Common.Views.InsertTableDialog.txtMinText": "La valeur minimale pour ce champ est {0}.", "Common.Views.InsertTableDialog.txtRows": "Nombre de lignes", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON>au", "Common.Views.InsertTableDialog.txtTitleSplit": "Fractionner la cellule", "Common.Views.LanguageDialog.labelSelect": "Sélectionner la langue du document", "Common.Views.ListSettingsDialog.textBulleted": "à puces", "Common.Views.ListSettingsDialog.textFromFile": "Depuis un fichier", "Common.Views.ListSettingsDialog.textFromStorage": "À partir de l'espace de stockage", "Common.Views.ListSettingsDialog.textFromUrl": "D'une URL", "Common.Views.ListSettingsDialog.textNumbering": "Numéroté", "Common.Views.ListSettingsDialog.textSelect": "Sélectionner à partir de", "Common.Views.ListSettingsDialog.tipChange": "Changer de puce", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Importer", "Common.Views.ListSettingsDialog.txtNewBullet": "Nouvelle puce", "Common.Views.ListSettingsDialog.txtNewImage": "Nouvelle image", "Common.Views.ListSettingsDialog.txtNone": "Rien", "Common.Views.ListSettingsDialog.txtOfText": "% de texte", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Commencer par", "Common.Views.ListSettingsDialog.txtSymbol": "Symbole", "Common.Views.ListSettingsDialog.txtTitle": "Paramètres de la liste", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>er", "Common.Views.OpenDialog.txtEncoding": "Codage ", "Common.Views.OpenDialog.txtIncorrectPwd": "Le mot de passe est incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Entrer le mot de passe pour ouvrir le fichier", "Common.Views.OpenDialog.txtPassword": "Mot de passe", "Common.Views.OpenDialog.txtProtected": "Une fois le mot de passe saisi et le fichier ouvert, le mot de passe actuel de fichier sera réinitialisé.", "Common.Views.OpenDialog.txtTitle": "Choisir les options %1", "Common.Views.OpenDialog.txtTitleProtected": "Fichier protégé", "Common.Views.PasswordDialog.txtDescription": "Indiquez un mot de passe pour protéger ce document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Le mot de passe de confirmation n'est pas identique", "Common.Views.PasswordDialog.txtPassword": "Mot de passe", "Common.Views.PasswordDialog.txtRepeat": "Confirmer le mot de passe", "Common.Views.PasswordDialog.txtTitle": "Définir un mot de passe", "Common.Views.PasswordDialog.txtWarning": "Attention : si vous oubliez ou perdez votre mot de passe, il sera impossible de le récupérer. Conservez-le en lieu sûr.", "Common.Views.PluginDlg.textLoading": "Chargement", "Common.Views.Plugins.groupCaption": "Modules complémentaires", "Common.Views.Plugins.strPlugins": "Plug-ins", "Common.Views.Plugins.textClosePanel": "Ferm<PERSON> le plugin", "Common.Views.Plugins.textLoading": "Chargement", "Common.Views.Plugins.textStart": "Lancer", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Chiffrer avec mot de passe", "Common.Views.Protection.hintDelPwd": "Supprimer le mot de passe", "Common.Views.Protection.hintPwd": "Modifier ou supprimer", "Common.Views.Protection.hintSignature": "Ajouter une signature numérique ou une ligne de signature", "Common.Views.Protection.txtAddPwd": "Ajouter un mot de passe", "Common.Views.Protection.txtChangePwd": "Modifier le mot de passe", "Common.Views.Protection.txtDeletePwd": "Supprimer le mot de passe", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Ajouter une signature numérique", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Ajouter la zone de signature", "Common.Views.RenameDialog.textName": "Nom de fi<PERSON>er", "Common.Views.RenameDialog.txtInvalidName": "Un nom de fichier ne peut pas contenir les caractères suivants :", "Common.Views.ReviewChanges.hintNext": "À la modification suivante", "Common.Views.ReviewChanges.hintPrev": "À la modification précédente", "Common.Views.ReviewChanges.strFast": "Rapide", "Common.Views.ReviewChanges.strFastDesc": "Co-édition en temps réel. Tous les changements sont enregistrés automatiquement.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Utilisez le bouton \"Enregistrer\" pour synchroniser les modifications que vous et d'autres personnes faites.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accepter la modification actuelle", "Common.Views.ReviewChanges.tipCoAuthMode": "Définir le mode de co-édition", "Common.Views.ReviewChanges.tipCommentRem": "Supprimer les commentaires", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Supprimer les commentaires existants", "Common.Views.ReviewChanges.tipCommentResolve": "Résoudre les commentaires", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Résoudre les commentaires actuels", "Common.Views.ReviewChanges.tipHistory": "Afficher versions", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeter cette modification", "Common.Views.ReviewChanges.tipReview": "Suivre des modifications", "Common.Views.ReviewChanges.tipReviewView": "Sélectionner le mode souhaité.", "Common.Views.ReviewChanges.tipSetDocLang": "Définir la langue du document", "Common.Views.ReviewChanges.tipSetSpelling": "Vérification de l'orthographe", "Common.Views.ReviewChanges.tipSharing": "<PERSON><PERSON>rer les droits d'accès au document", "Common.Views.ReviewChanges.txtAccept": "Accepter", "Common.Views.ReviewChanges.txtAcceptAll": "Accepter toutes les modifications", "Common.Views.ReviewChanges.txtAcceptChanges": "Accepter les modifications", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accepter la modification actuelle", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Mode de co-édition ", "Common.Views.ReviewChanges.txtCommentRemAll": "Supprimer tous les commentaires", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Supprimer les commentaires actuels", "Common.Views.ReviewChanges.txtCommentRemMy": "Supprimer mes commentaires", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Supprimer mes commentaires actuels", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON> tous les commentaires comme résolus", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Marquer les commentaires actuels comme résolus", "Common.Views.ReviewChanges.txtCommentResolveMy": "Marquer mes commentaires comme résolus", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Marquer mes commentaires comme résolus", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "Toutes les modifications acceptées (aperçu)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historique des versions", "Common.Views.ReviewChanges.txtMarkup": "Toutes les modifications (édition)", "Common.Views.ReviewChanges.txtMarkupCap": "Balisage", "Common.Views.ReviewChanges.txtNext": "Suivante", "Common.Views.ReviewChanges.txtOriginal": "Toutes les modifications rejetées (Aperçu)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Précédente", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Rejeter toutes les modifications", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeter les modifications", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeter cette modification", "Common.Views.ReviewChanges.txtSharing": "Partage", "Common.Views.ReviewChanges.txtSpelling": "Vérification de l'orthographe", "Common.Views.ReviewChanges.txtTurnon": "Suivre des modifications", "Common.Views.ReviewChanges.txtView": "Mode d'affichage", "Common.Views.ReviewPopover.textAdd": "Ajouter", "Common.Views.ReviewPopover.textAddReply": "Ajouter Réponse", "Common.Views.ReviewPopover.textCancel": "Annuler", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Saisissez votre commentaire ici", "Common.Views.ReviewPopover.textMention": "+mention donne l'accès au document et notifie par courriel  ", "Common.Views.ReviewPopover.textMentionNotify": "+mention notifie l'utilisateur par courriel", "Common.Views.ReviewPopover.textOpenAgain": "Ouvrir à nouveau", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Vous n'avez pas la permission de rouvrir le commentaire", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Modifier", "Common.Views.SaveAsDlg.textLoading": "Chargement", "Common.Views.SaveAsDlg.textTitle": "Dossier pour enregistrement", "Common.Views.SearchPanel.textCaseSensitive": "Sensible à la casse", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON>er la recherche", "Common.Views.SearchPanel.textContentChanged": "Document modifié.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Re<PERSON><PERSON> et remplacer", "Common.Views.SearchPanel.textMatchUsingRegExp": "Correspondance à l'aide d'expressions régulières", "Common.Views.SearchPanel.textNoMatches": "Aucune correspondance", "Common.Views.SearchPanel.textNoSearchResults": "Aucun résultat de recherche", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON>lace<PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Remplacer tout", "Common.Views.SearchPanel.textReplaceWith": "Remplacer par", "Common.Views.SearchPanel.textSearchAgain": "{0}Effectuer une nouvelle recherche{1} pour obtenir des résultats précis.", "Common.Views.SearchPanel.textSearchHasStopped": "La recherche a été arrêtée", "Common.Views.SearchPanel.textSearchResults": "Résultats de la recherche : {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Il y a trop de résultats pour les montrer ici", "Common.Views.SearchPanel.textWholeWords": "Mots entiers uniquement", "Common.Views.SearchPanel.tipNextResult": "Résultat suivant", "Common.Views.SearchPanel.tipPreviousResult": "Résultat précédent", "Common.Views.SelectFileDlg.textLoading": "Chargement", "Common.Views.SelectFileDlg.textTitle": "Sélectionner la source de données", "Common.Views.SignDialog.textBold": "Gras", "Common.Views.SignDialog.textCertificate": "Certificat", "Common.Views.SignDialog.textChange": "Changer", "Common.Views.SignDialog.textInputName": "Saisir le nom du signataire", "Common.Views.SignDialog.textItalic": "Italique", "Common.Views.SignDialog.textNameError": "Veuillez indiquer le nom du signataire.", "Common.Views.SignDialog.textPurpose": "But de la signature du document", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Sélectionner une image", "Common.Views.SignDialog.textSignature": "L'aspect de votre signature", "Common.Views.SignDialog.textTitle": "Signer le document", "Common.Views.SignDialog.textUseImage": "ou cliquer sur \"Sélectionner une image\" afin d'utiliser une image en tant que signature", "Common.Views.SignDialog.textValid": "Valide de %1 à %2", "Common.Views.SignDialog.tipFontName": "Nom de police", "Common.Views.SignDialog.tipFontSize": "Taille de police", "Common.Views.SignSettingsDialog.textAllowComment": "Autoriser le signataire à ajouter un commentaire dans la boîte de dialogue de la signature", "Common.Views.SignSettingsDialog.textDefInstruction": "Avant de signer un document, vérifiez que le contenu que vous signez est correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail du signataire suggéré", "Common.Views.SignSettingsDialog.textInfoName": "Signataire suggéré", "Common.Views.SignSettingsDialog.textInfoTitle": "Titre du signataire suggéré", "Common.Views.SignSettingsDialog.textInstructions": "Instructions pour les signataires", "Common.Views.SignSettingsDialog.textShowDate": "Afficher la date de signature à côté de la signature", "Common.Views.SignSettingsDialog.textTitle": "Configuration de signature", "Common.Views.SignSettingsDialog.txtEmpty": "Ce champ est obligatoire", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valeur hexadécimale Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Symbole de copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON><PERSON> double fermant", "Common.Views.SymbolTableDialog.textDOQuote": "Double guillemet ouvrant", "Common.Views.SymbolTableDialog.textEllipsis": "Points de suspension", "Common.Views.SymbolTableDialog.textEmDash": "Tiret cadratin", "Common.Views.SymbolTableDialog.textEmSpace": "Espace cadratin", "Common.Views.SymbolTableDialog.textEnDash": "Tiret demi-cadratin", "Common.Views.SymbolTableDialog.textEnSpace": "Espace demi-cadratin", "Common.Views.SymbolTableDialog.textFont": "Police", "Common.Views.SymbolTableDialog.textNBHyphen": "Trait d’union insécable", "Common.Views.SymbolTableDialog.textNBSpace": "Espace insécable", "Common.Views.SymbolTableDialog.textPilcrow": "Pied-de-mouche", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Espace cadratin", "Common.Views.SymbolTableDialog.textRange": "Plage", "Common.Views.SymbolTableDialog.textRecent": "Caractères spéciaux récemment utilisés", "Common.Views.SymbolTableDialog.textRegistered": "Symbole de marque déposée", "Common.Views.SymbolTableDialog.textSCQuote": "G<PERSON><PERSON>et simple fermant", "Common.Views.SymbolTableDialog.textSection": "Paragraphe", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON>e <PERSON> r<PERSON>ci", "Common.Views.SymbolTableDialog.textSHyphen": "Trait d'union conditionnel", "Common.Views.SymbolTableDialog.textSOQuote": "G<PERSON><PERSON><PERSON> simple ouvrant", "Common.Views.SymbolTableDialog.textSpecial": "symboles spéciaux", "Common.Views.SymbolTableDialog.textSymbols": "Symboles", "Common.Views.SymbolTableDialog.textTitle": "Symbole", "Common.Views.SymbolTableDialog.textTradeMark": "Symbole de marque", "Common.Views.UserNameDialog.textDontShow": "Ne plus me demander à nouveau", "Common.Views.UserNameDialog.textLabel": "Étiquette :", "Common.Views.UserNameDialog.textLabelError": "Étiquette ne doit pas être vide", "PE.Controllers.LeftMenu.leavePageText": "Toutes les modifications non enregistrées dans ce document seront perdues.<br> <PERSON><PERSON><PERSON> sur \"Annuler\", puis \"Enregistrer\" pour les sauvegarder. Cliquez sur \"OK\" pour annuler toutes les modifications non enregistrées.", "PE.Controllers.LeftMenu.newDocumentTitle": "Présentation sans nom", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Avertissement", "PE.Controllers.LeftMenu.requestEditRightsText": "Demande des droits de modification...", "PE.Controllers.LeftMenu.textLoadHistory": "Chargement de l'historique des versions...", "PE.Controllers.LeftMenu.textNoTextFound": "Votre recherche n'a donné aucun résultat.S'il vous plaît, modifiez vos critères de recherche.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Le remplacement est fait. {0} occurrences ont été ignorées.", "PE.Controllers.LeftMenu.textReplaceSuccess": "La recherche est effectuée. Occurrences ont été remplacées:{0}", "PE.Controllers.LeftMenu.txtUntitled": "Sans titre", "PE.Controllers.Main.applyChangesTextText": "Chargement des données...", "PE.Controllers.Main.applyChangesTitleText": "Chargement des données", "PE.Controllers.Main.confirmMaxChangesSize": "La taille des actions dépasse la limitation fixée pour votre serveur.<br><PERSON><PERSON><PERSON><PERSON> sur \"Annuler\" pour annuler votre dernière action ou sur \"Continuer\" pour maintenir l'action en local (vous devez télécharger le fichier ou copier son contenu pour vous assurer que rien n'est perdu).", "PE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON> de conversion expiré.", "PE.Controllers.Main.criticalErrorExtText": "Cliquez sur \"OK\" pour revenir à la liste des documents.", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "Échec du téléchargement.", "PE.Controllers.Main.downloadTextText": "Téléchargement de la présentation...", "PE.Controllers.Main.downloadTitleText": "Téléchargement de la présentation", "PE.Controllers.Main.errorAccessDeny": "Vous tentez d'exéсuter une action pour laquelle vous ne disposez pas des droits.<br><PERSON><PERSON><PERSON>z contacter l'administrateur de Document Server.", "PE.Controllers.Main.errorBadImageUrl": "L'URL de l'image est incorrecte", "PE.Controllers.Main.errorCannotPasteImg": "Nous ne pouvons pas coller cette image à partir du Presse-papiers, mais vous pouvez l'enregistrer sur votre appareil et l'insérer à partir de là, ou vous pouvez copier l'image sans texte et la coller dans la présentation.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Connexion au serveur perdue. Le document ne peut être modifié en ce moment.", "PE.Controllers.Main.errorComboSeries": "Pour créer un graphique combiné, sélectionnez au moins deux séries de données. ", "PE.Controllers.Main.errorConnectToServer": "Impossible d'enregistrer le document. Veuillez vérifier vos paramètres de connexion ou contactez l'administrateur.<br><PERSON><PERSON><PERSON> vous cliquez sur le bouton 'OK', vous serez invité à télécharger le document.", "PE.Controllers.Main.errorDatabaseConnection": "Erreur externe.<br>Erreur de connexion à la base de données. Si l'erreur persiste veillez contactez l'assistance technique.", "PE.Controllers.Main.errorDataEncrypted": "Les modifications chiffrées ont été reçues, elle ne peuvent pas être déchiffrées.", "PE.Controllers.Main.errorDataRange": "Plage de donn<PERSON>.", "PE.Controllers.Main.errorDefaultMessage": "Code d'erreur: %1", "PE.Controllers.Main.errorDirectUrl": "Veuillez vérifier le lien vers le document.<br>Ce lien doit être un lien direct vers le fichier à télécharger.", "PE.Controllers.Main.errorEditingDownloadas": "Une erreur s'est produite lors du travail avec le document.<br>Utilisez l'option 'Télécharger comme' pour enregistrer une copie de sauvegarde du fichier sur le disque dur de votre ordinateur.", "PE.Controllers.Main.errorEditingSaveas": "Une erreur s'est produite lors du travail avec le document.<br>Utilisez l'option 'Télécharger comme...' pour enregistrer une copie de sauvegarde sur le disque dur de votre ordinateur. ", "PE.Controllers.Main.errorEmailClient": "Pas de client messagerie trouvé", "PE.Controllers.Main.errorFilePassProtect": "Le fichier est protégé par le mot de passe et ne peut être ouvert.", "PE.Controllers.Main.errorFileSizeExceed": "La taille du fichier dépasse les limites établies sur votre serveur.<br><PERSON><PERSON><PERSON><PERSON> contacter votre administrateur de Document Server pour obtenir plus d'information.  ", "PE.Controllers.Main.errorForceSave": "Une erreur est survenue lors de l'enregistrement du fichier. Veuillez utiliser l'option \"Télécharger comme\" pour enregistrer le fichier sur le disque dur de votre ordinateur ou réessayer plus tard.", "PE.Controllers.Main.errorInconsistentExt": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier ne correspond pas à l'extension du fichier.", "PE.Controllers.Main.errorInconsistentExtDocx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des documents texte (par exemple docx), mais le fichier a une extension incohérente : %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à l'un des formats suivants : pdf/djvu/xps/oxps, mais le fichier a l'extension incohérente : %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des présentations (par exemple pptx), mais le fichier a une extension incohérente : %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des feuilles de calcul (par exemple xlsx), mais le fichier a une extension incohérente : %1.", "PE.Controllers.Main.errorKeyEncrypt": "Descripteur de clés inconnu", "PE.Controllers.Main.errorKeyExpire": "Descripteur de clés expiré", "PE.Controllers.Main.errorLoadingFont": "Les polices ne sont pas téléchargées.<br><PERSON><PERSON>illez contacter l'administrateur de Document Server.", "PE.Controllers.Main.errorProcessSaveResult": "Échec de l'enregistrement", "PE.Controllers.Main.errorServerVersion": "La version de l'éditeur a été mise à jour. La page sera rechargée pour appliquer les modifications.", "PE.Controllers.Main.errorSessionAbsolute": "Votre session a expiré. Veuillez recharger la page.", "PE.Controllers.Main.errorSessionIdle": "Le document n'a pas été modifié depuis trop longtemps. Veuillez recharger la page.", "PE.Controllers.Main.errorSessionToken": "La connexion au serveur a été interrompue. Veuillez recharger la page.", "PE.Controllers.Main.errorSetPassword": "Le mot de passe ne peut pas être configuré", "PE.Controllers.Main.errorStockChart": "Ordre lignes incorrect. <PERSON><PERSON> créer un diagramme boursier, positionnez les données sur la feuille de calcul dans l'ordre suivant :<br>cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "PE.Controllers.Main.errorToken": "Le jeton de sécurité du document n’était pas formé correctement.<br>V<PERSON>illez contacter l'administrateur de Document Server.", "PE.Controllers.Main.errorTokenExpire": "Le jeton de sécurité du document a expiré.<br>Veuillez contactez l'administrateur de Document Server.", "PE.Controllers.Main.errorUpdateVersion": "La version du fichier a été changée. La page sera rechargée.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "La connexion a été rétablie et la version du fichier a été modifiée.<br>Avant de pouvoir continuer à travailler, vous devez télécharger le fichier ou copier son contenu pour vous assurer que rien n'est perdu, puis recharger cette page.", "PE.Controllers.Main.errorUserDrop": "Impossible d'accéder au fichier", "PE.Controllers.Main.errorUsersExceed": "Le nombre d'utilisateurs autorisés par le plan tarifaire a été dépassé", "PE.Controllers.Main.errorViewerDisconnect": "La connexion a été perdue. Vous pouvez toujours afficher le document,<br>mais ne pouvez pas le télécharger ou l'imprimer jusqu'à ce que la connexion soit rétablie et que la page soit actualisée.", "PE.Controllers.Main.leavePageText": "Vous avez des modifications non enregistrées dans cette présentation. Cliquez sur \"Rester sur cette page\", ensuite sur \"Enregistrer\" pour enregistrer les modifications. Cliquez sur \"Quitter cette page\" pour annuler toutes les modifications non enregistrées.", "PE.Controllers.Main.leavePageTextOnClose": "Toutes les modifications non-enregistrée seront perdues.<br> <PERSON><PERSON><PERSON> \"Annuler\", ensuite \"Enregistrer\" pour les sauvegarder. Cliquez \"OK\" pour effacer toutes les modifications non-enregistrées.", "PE.Controllers.Main.loadFontsTextText": "Chargement des données...", "PE.Controllers.Main.loadFontsTitleText": "Chargement des données", "PE.Controllers.Main.loadFontTextText": "Chargement des données...", "PE.Controllers.Main.loadFontTitleText": "Chargement des données", "PE.Controllers.Main.loadImagesTextText": "Chargement des images en cours...", "PE.Controllers.Main.loadImagesTitleText": "Chargement des images", "PE.Controllers.Main.loadImageTextText": "Chargement d'une image...", "PE.Controllers.Main.loadImageTitleText": "Chargement d'une image", "PE.Controllers.Main.loadingDocumentTextText": "Chargement de présentation...", "PE.Controllers.Main.loadingDocumentTitleText": "Chargement de présentation", "PE.Controllers.Main.loadThemeTextText": "Chargement du thème en cours...", "PE.Controllers.Main.loadThemeTitleText": "Chargement du thème", "PE.Controllers.Main.notcriticalErrorTitle": "Avertissement", "PE.Controllers.Main.openErrorText": "Une erreur s’est produite lors de l’ouverture du fichier", "PE.Controllers.Main.openTextText": "Ouverture de la présentation...", "PE.Controllers.Main.openTitleText": "Ouverture de la présentation", "PE.Controllers.Main.printTextText": "Impression de la présentation...", "PE.Controllers.Main.printTitleText": "Impression de la présentation", "PE.Controllers.Main.reloadButtonText": "Recharger la page", "PE.Controllers.Main.requestEditFailedMessageText": "Quelqu'un est en train de modifier cette présentation. Veuillez réessayer plus tard.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>ès refusé", "PE.Controllers.Main.saveErrorText": "Une erreur s'est produite lors de l'enregistrement du fichier.", "PE.Controllers.Main.saveErrorTextDesktop": "Le fichier ne peut pas être sauvé ou créé.<br>Les raisons possible sont :<br>1. Le fichier est en lecture seule. <br>2. Les fichier est en cours d'éditions par d'autres utilisateurs. <br>3. Le disque dur est plein ou corrompu.", "PE.Controllers.Main.saveTextText": "Enregistrement de la présentation...", "PE.Controllers.Main.saveTitleText": "Enregistrement de la présentation", "PE.Controllers.Main.scriptLoadError": "La connexion est trop lente, certains  éléments ne peuvent pas être chargés. Veuillez recharger la page.", "PE.Controllers.Main.splitDividerErrorText": "Le nombre de lignes doit être un diviseur de %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Le nombre de colonnes doivent être inférieure à %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Le nombre de lignes doit être inférieure à %1.", "PE.Controllers.Main.textAnonymous": "Anonyme", "PE.Controllers.Main.textApplyAll": "Appliquer à toutes les équations", "PE.Controllers.Main.textBuyNow": "Visiter le site web", "PE.Controllers.Main.textChangesSaved": "Toutes les modifications sont enregistrées", "PE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Cliquez pour fermer la conseil", "PE.Controllers.Main.textContactUs": "Contacter l'équipe de ventes", "PE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textConvertEquation": "Cette équation a été créée avec une ancienne version de l'éditeur des équations qui n'est plus disponible. Pour modifier cette équation, convertissez-la au format Office Math ML.<br>Convertir maintenant ?", "PE.Controllers.Main.textCustomLoader": "Veuillez noter que conformément aux clauses du contrat de licence vous n'êtes pas autorisé à changer le chargeur.<br>Veuillez contacter notre Service des Ventes pour obtenir le devis.", "PE.Controllers.Main.textDisconnect": "La connexion est perdue", "PE.Controllers.Main.textGuest": "Invi<PERSON>", "PE.Controllers.Main.textHasMacros": "Le fichier contient des macros automatiques.<br><PERSON><PERSON><PERSON><PERSON><PERSON>vous exécuter les macros ?", "PE.Controllers.Main.textLearnMore": "En savoir plus", "PE.Controllers.Main.textLoadingDocument": "Chargement de présentation", "PE.Controllers.Main.textLongName": "Entrez un nom qui a moins de 128 caractères.", "PE.Controllers.Main.textNoLicenseTitle": "La limite de la licence est atteinte", "PE.Controllers.Main.textPaidFeature": "Fonction payante", "PE.Controllers.Main.textReconnect": "La connexion est restaurée", "PE.Controllers.Main.textRemember": "Se souvenir de mon choix", "PE.Controllers.Main.textRememberMacros": "Retenir mes choix pour toutes les macros", "PE.Controllers.Main.textRenameError": "Le nom d'utilisateur ne peut être vide.", "PE.Controllers.Main.textRenameLabel": "Entrez un nom à utiliser pour la collaboration", "PE.Controllers.Main.textRequestMacros": "Une macro fait une demande à l'URL. Voulez-vous autoriser la demande à l'adresse %1 ?", "PE.Controllers.Main.textShape": "Forme", "PE.Controllers.Main.textStrict": "Mode strict", "PE.Controllers.Main.textText": "Texte", "PE.Controllers.Main.textTryQuickPrint": "Vous avez sélectionné Impression rapide : l'ensemble du document sera imprimé sur la dernière imprimante sélectionnée ou celle par défaut.<br><PERSON><PERSON><PERSON><PERSON>-vous continuer ?", "PE.Controllers.Main.textTryUndoRedo": "Les fonctions annuler/rétablir sont désactivées pour le mode de co-édition rapide.<br><PERSON><PERSON><PERSON> sur le bouton \"Mode strict\" pour passer au mode de la co-édition stricte pour modifier le fichier sans interférence d'autres utilisateurs et envoyer vos modifications seulement après que vous les enregistrez. Vous pouvez basculer entre les modes de co-édition à l'aide de paramètres avancés d'éditeur.", "PE.Controllers.Main.textTryUndoRedoWarn": "Les fonctions Annuler/Rétablir sont désactivées pour le mode de co-édition rapide.", "PE.Controllers.Main.textUndo": "Annuler", "PE.Controllers.Main.titleLicenseExp": "Licence expirée", "PE.Controllers.Main.titleServerVersion": "L'éditeur est mis à jour", "PE.Controllers.Main.txtAddFirstSlide": "Cliquez pour ajouter la première diapositive", "PE.Controllers.Main.txtAddNotes": "Cliquez pour ajouter des notes", "PE.Controllers.Main.txtArt": "Entrez votre texte", "PE.Controllers.Main.txtBasicShapes": "Formes de base", "PE.Controllers.Main.txtButtons": "Boutons", "PE.Controllers.Main.txtCallouts": "Légendes", "PE.Controllers.Main.txtCharts": "Graphiques", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Date et heure", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Titre du graphique", "PE.Controllers.Main.txtEditingMode": "Réglage mode d'édition...", "PE.Controllers.Main.txtErrorLoadHistory": "Chargement de l'historique a échoué", "PE.Controllers.Main.txtFiguredArrows": "Flèches figurées", "PE.Controllers.Main.txtFooter": "Pied de page", "PE.Controllers.Main.txtHeader": "<PERSON>-tête", "PE.Controllers.Main.txtImage": "Image", "PE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Chargement en cours...", "PE.Controllers.Main.txtMath": "Maths", "PE.Controllers.Main.txtMedia": "Média", "PE.Controllers.Main.txtNeedSynchronize": "<PERSON><PERSON> avez des mises à jour", "PE.Controllers.Main.txtNone": "Rien", "PE.Controllers.Main.txtPicture": "Image", "PE.Controllers.Main.txtRectangles": "Rectangles", "PE.Controllers.Main.txtSeries": "Série", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Légende encadrée avec une bordure 1", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Légende encadrée avec une bordure 2", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Légende encadrée avec une bordure 3", "PE.Controllers.Main.txtShape_accentCallout1": "Légende à une bordure 1", "PE.Controllers.Main.txtShape_accentCallout2": "Légende à une bordure 2", "PE.Controllers.Main.txtShape_accentCallout3": "Légende à une bordure 3", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Bouton Précédent", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Bouton Au Commencement", "PE.Controllers.Main.txtShape_actionButtonBlank": "Bouton vide", "PE.Controllers.Main.txtShape_actionButtonDocument": "Bouton Document", "PE.Controllers.Main.txtShape_actionButtonEnd": "Bouton à la fin", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Bouton Suivant", "PE.Controllers.Main.txtShape_actionButtonHelp": "Bouton Aide", "PE.Controllers.Main.txtShape_actionButtonHome": "Bouton \"Page d'accueil\"", "PE.Controllers.Main.txtShape_actionButtonInformation": "Bouton Informations", "PE.Controllers.Main.txtShape_actionButtonMovie": "Bouton Vidéo", "PE.Controllers.Main.txtShape_actionButtonReturn": "Bouton Retour", "PE.Controllers.Main.txtShape_actionButtonSound": "Bouton Son", "PE.Controllers.Main.txtShape_arc": "Arc", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON> cour<PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Connecteur en angle", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Connecteur en angle avec flèche\t", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Connecteur en angle avec deux flèches", "PE.Controllers.Main.txtShape_bentUpArrow": "Flèche à angle droit", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "<PERSON>", "PE.Controllers.Main.txtShape_borderCallout1": "Légende encadrée 1", "PE.Controllers.Main.txtShape_borderCallout2": "Légende encadrée 2", "PE.Controllers.Main.txtShape_borderCallout3": "Légende encadrée 3", "PE.Controllers.Main.txtShape_bracePair": "Accolades", "PE.Controllers.Main.txtShape_callout1": "Légende encadrée sans bordure 1", "PE.Controllers.Main.txtShape_callout2": "Légende encadrée sans bordure 2", "PE.Controllers.Main.txtShape_callout3": "Légende encadrée sans bordure 3", "PE.Controllers.Main.txtShape_can": "Cylindre", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "Corde", "PE.Controllers.Main.txtShape_circularArrow": "Flèche en arc\t", "PE.Controllers.Main.txtShape_cloud": "Cloud", "PE.Controllers.Main.txtShape_cloudCallout": "Pensées", "PE.Controllers.Main.txtShape_corner": "Coin", "PE.Controllers.Main.txtShape_cube": "C<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Connecteur en arc ", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Connecteur en arc avec flèche", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Connecteur en arc avec deux flèches", "PE.Controllers.Main.txtShape_curvedDownArrow": "Flèche courbée vers le bas", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Flèche courbée vers la gauche", "PE.Controllers.Main.txtShape_curvedRightArrow": "Flèche courbée vers la droite", "PE.Controllers.Main.txtShape_curvedUpArrow": "Flèche courbée vers le haut", "PE.Controllers.Main.txtShape_decagon": "Décagone", "PE.Controllers.Main.txtShape_diagStripe": "Bande diagonale", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "Dodécagone", "PE.Controllers.Main.txtShape_donut": "Bo<PERSON><PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "Double vague", "PE.Controllers.Main.txtShape_downArrow": "Flèche vers le bas", "PE.Controllers.Main.txtShape_downArrowCallout": "Rectangle avec flèche vers le bas", "PE.Controllers.Main.txtShape_ellipse": "Ellipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "R<PERSON>n courbé vers le bas", "PE.Controllers.Main.txtShape_ellipseRibbon2": "R<PERSON>n courbé vers le haut", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Organigramme : Alternative", "PE.Controllers.Main.txtShape_flowChartCollate": "Organigramme: <PERSON><PERSON><PERSON>\t", "PE.Controllers.Main.txtShape_flowChartConnector": "Organigramme: Connecteur", "PE.Controllers.Main.txtShape_flowChartDecision": "Organigramme: Decision", "PE.Controllers.Main.txtShape_flowChartDelay": "Organigramme: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDisplay": "Organigramme : Affichage", "PE.Controllers.Main.txtShape_flowChartDocument": "Organigramme: Document", "PE.Controllers.Main.txtShape_flowChartExtract": "Organigramme: Extraire", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Organigramme: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Organigramme : Stockage interne", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Organigramme : Disque magnétique", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Organigramme : Stockage à accès direct", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Organigramme: Stockage à accès séquentiel\t", "PE.Controllers.Main.txtShape_flowChartManualInput": "Organigramme: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Organigramme: Opération manuelle", "PE.Controllers.Main.txtShape_flowChartMerge": "Organigramme: Fusion\t", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Organigramme: Multidocument", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Organigramme: Connecteur page suivante", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Organigramme: Donn<PERSON> stockées", "PE.Controllers.Main.txtShape_flowChartOr": "Organigramme: OU", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Organigramme: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPreparation": "Organigramme: Préparation", "PE.Controllers.Main.txtShape_flowChartProcess": "Organigramme: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Organigramme: <PERSON><PERSON> perfo<PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Organigramme: Bande perforée", "PE.Controllers.Main.txtShape_flowChartSort": "Organigramme : <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Organigramme: <PERSON><PERSON> de sommaire", "PE.Controllers.Main.txtShape_flowChartTerminator": "Organigramme: Terminaison", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_frame": "Cadre", "PE.Controllers.Main.txtShape_halfFrame": "Demi-cadre", "PE.Controllers.Main.txtShape_heart": "Coeur", "PE.Controllers.Main.txtShape_heptagon": "Heptagone", "PE.Controllers.Main.txtShape_hexagon": "Hexagon", "PE.Controllers.Main.txtShape_homePlate": "Pentagone", "PE.Controllers.Main.txtShape_horizontalScroll": "Parchemin horizontal", "PE.Controllers.Main.txtShape_irregularSeal1": "Éclatement 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Éclatement 2", "PE.Controllers.Main.txtShape_leftArrow": "Flèche gauche", "PE.Controllers.Main.txtShape_leftArrowCallout": "Rectangle avec flèche vers la gauche\t", "PE.Controllers.Main.txtShape_leftBrace": "Accolade ouvrante", "PE.Controllers.Main.txtShape_leftBracket": "Parenthèse ouvrante", "PE.Controllers.Main.txtShape_leftRightArrow": "Flèche bilatérale", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Rectangle horizontal à deux flèches", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Flèche à trois pointes", "PE.Controllers.Main.txtShape_leftUpArrow": "Double flèche horizontale", "PE.Controllers.Main.txtShape_lightningBolt": "Éclair", "PE.Controllers.Main.txtShape_line": "Ligne", "PE.Controllers.Main.txtShape_lineWithArrow": "Flèche", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Flèche à deux pointes", "PE.Controllers.Main.txtShape_mathDivide": "Division ", "PE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON>ins", "PE.Controllers.Main.txtShape_mathMultiply": "Multiplication", "PE.Controllers.Main.txtShape_mathNotEqual": "<PERSON>ff<PERSON><PERSON> de", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "Symbole \"non\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Flèche droite à entaille\t", "PE.Controllers.Main.txtShape_octagon": "Octogone", "PE.Controllers.Main.txtShape_parallelogram": "Parallélogramme", "PE.Controllers.Main.txtShape_pentagon": "Pentagone", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "<PERSON>e", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "Dessin à main levée", "PE.Controllers.Main.txtShape_polyline2": "Forme libre", "PE.Controllers.Main.txtShape_quadArrow": "Flèche à quatre pointes", "PE.Controllers.Main.txtShape_quadArrowCallout": "Rectangle à quatre flèches\t", "PE.Controllers.Main.txtShape_rect": "Rectangle", "PE.Controllers.Main.txtShape_ribbon": "R<PERSON>n vers le bas", "PE.Controllers.Main.txtShape_ribbon2": "R<PERSON><PERSON> vers le haut", "PE.Controllers.Main.txtShape_rightArrow": "Flèche droite", "PE.Controllers.Main.txtShape_rightArrowCallout": "Rectangle avec flèche vers le droit", "PE.Controllers.Main.txtShape_rightBrace": "Accolade fermante", "PE.Controllers.Main.txtShape_rightBracket": "Parenthèse fermante", "PE.Controllers.Main.txtShape_round1Rect": "Rectangle arrondi à un seul coin", "PE.Controllers.Main.txtShape_round2DiagRect": "Rectangle avec un coin diagonal rond", "PE.Controllers.Main.txtShape_round2SameRect": "Rectangle arrondi avec un coin du même côté", "PE.Controllers.Main.txtShape_roundRect": "Rectangle à coins arrondis", "PE.Controllers.Main.txtShape_rtTriangle": "Triangle rectangle", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Rogner un rectangle à un seul coin", "PE.Controllers.Main.txtShape_snip2DiagRect": "Couper un rectangle avec un coin diagonal", "PE.Controllers.Main.txtShape_snip2SameRect": "Couper un rectangle avec un coin du même côté", "PE.Controllers.Main.txtShape_snipRoundRect": "<PERSON><PERSON><PERSON> et arrondir un rectangle à un seul coin", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "Étoile à 10 branches", "PE.Controllers.Main.txtShape_star12": "Étoile à 12 branches", "PE.Controllers.Main.txtShape_star16": "Étoile à 16 branches", "PE.Controllers.Main.txtShape_star24": "Étoile à 24 branches", "PE.Controllers.Main.txtShape_star32": "Étoile à 32 branches", "PE.Controllers.Main.txtShape_star4": "Étoile à 4 branches", "PE.Controllers.Main.txtShape_star5": "Étoile à 5 branches", "PE.Controllers.Main.txtShape_star6": "Étoile à 6 branches", "PE.Controllers.Main.txtShape_star7": "Étoile à 7 branches", "PE.Controllers.Main.txtShape_star8": "Étoile à 8 branches", "PE.Controllers.Main.txtShape_stripedRightArrow": "Flèche vers la droite  rayée", "PE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON> ", "PE.Controllers.Main.txtShape_textRect": "Zone de texte", "PE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_triangle": "Triangle", "PE.Controllers.Main.txtShape_upArrow": "Flèche vers le haut", "PE.Controllers.Main.txtShape_upArrowCallout": "Rectangle avec flèche vers le haut", "PE.Controllers.Main.txtShape_upDownArrow": "Double flèche verticale", "PE.Controllers.Main.txtShape_uturnArrow": "Demi-tour", "PE.Controllers.Main.txtShape_verticalScroll": "Parchemin vertical", "PE.Controllers.Main.txtShape_wave": "<PERSON>de", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Bulle ronde", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangle", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rectangle à coins arrondis", "PE.Controllers.Main.txtSldLtTBlank": "Vide", "PE.Controllers.Main.txtSldLtTChart": "Graphique", "PE.Controllers.Main.txtSldLtTChartAndTx": "Graphique et texte", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art et texte", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art et texte vertical", "PE.Controllers.Main.txtSldLtTCust": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTDgm": "Diagramme", "PE.Controllers.Main.txtSldLtTFourObj": "Quatre objets", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Média et texte", "PE.Controllers.Main.txtSldLtTObj": "Titre et objet", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objet et deux objets", "PE.Controllers.Main.txtSldLtTObjAndTx": "Objet et texte", "PE.Controllers.Main.txtSldLtTObjOnly": "Objet", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objet sur texte", "PE.Controllers.Main.txtSldLtTObjTx": "Titre, objet et légende", "PE.Controllers.Main.txtSldLtTPicTx": "Image et légende", "PE.Controllers.Main.txtSldLtTSecHead": "En-tête de section", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Titre", "PE.Controllers.Main.txtSldLtTTitleOnly": "Titre seulement", "PE.Controllers.Main.txtSldLtTTwoColTx": "Texte de deux colonnes", "PE.Controllers.Main.txtSldLtTTwoObj": "Deux objets", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Deux objets et objet", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Deux objet et texte", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Deux objets sur texte", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Deux textes et deux objets", "PE.Controllers.Main.txtSldLtTTx": "Texte", "PE.Controllers.Main.txtSldLtTTxAndChart": "Texte et graphique", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Texte et Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Texte et média", "PE.Controllers.Main.txtSldLtTTxAndObj": "Texte et objet", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Texte et deux objets", "PE.Controllers.Main.txtSldLtTTxOverObj": "Texte sur objet", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Titre vertical et texte", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Titre vertical et texte sur graphique", "PE.Controllers.Main.txtSldLtTVertTx": "Texte vertical", "PE.Controllers.Main.txtSlideNumber": "Numéro de diapositive", "PE.Controllers.Main.txtSlideSubtitle": "Sous-titre de diapositive", "PE.Controllers.Main.txtSlideText": "Texte de la diapositive", "PE.Controllers.Main.txtSlideTitle": "Titre de la diapositive", "PE.Controllers.Main.txtStarsRibbons": "Étoiles et rubans", "PE.Controllers.Main.txtTheme_basic": "Simple", "PE.Controllers.Main.txtTheme_blank": "Vide", "PE.Controllers.Main.txtTheme_classic": "Classique", "PE.Controllers.Main.txtTheme_corner": "Angulaire", "PE.Controllers.Main.txtTheme_dotted": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "<PERSON>ert", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON> verte", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "Office", "PE.Controllers.Main.txtTheme_office_theme": "Thème Office", "PE.Controllers.Main.txtTheme_official": "officielle", "PE.Controllers.Main.txtTheme_pixel": "Pixé<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "<PERSON><PERSON><PERSON> ", "PE.Controllers.Main.txtXAxis": "Axe X", "PE.Controllers.Main.txtYAxis": "Axe Y", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> inconnue.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Votre navigateur n'est pas pris en charge.", "PE.Controllers.Main.uploadImageExtMessage": "Format d'image inconnu.", "PE.Controllers.Main.uploadImageFileCountMessage": "Pas d'images chargées.", "PE.Controllers.Main.uploadImageSizeMessage": "L'image est trop grande. La taille limite est de 25 Mo.", "PE.Controllers.Main.uploadImageTextText": "Chargement d'une image en cours...", "PE.Controllers.Main.uploadImageTitleText": "Chargement d'une image", "PE.Controllers.Main.waitText": "Veuillez patienter...", "PE.Controllers.Main.warnBrowserIE9": "L'application est peu compatible avec IE9. Utilisez IE10 ou version plus récente", "PE.Controllers.Main.warnBrowserZoom": "Le paramètre actuel de zoom de votre navigateur n'est pas accepté. Veuillez rétablir le niveau de zoom par défaut en appuyant sur Ctrl+0.", "PE.Controllers.Main.warnLicenseExceeded": "Vous avez dépassé le nombre maximal de connexions simultanées aux éditeurs %1. Ce document sera ouvert à la lecture seulement.<br>Contactez votre administrateur pour en savoir davantage.", "PE.Controllers.Main.warnLicenseExp": "Votre licence a expiré.<br>Veuillez mettre à jour votre licence et actualisez la page.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "La licence est expirée.<br>V<PERSON> n'avez plus d'accès aux outils d'édition.<br><PERSON><PERSON><PERSON>z contacter votre administrateur.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Il est indispensable de renouveler la licence.<br>Vous avez un accès limité aux outils d'édition des documents.<br><PERSON><PERSON><PERSON>z contacter votre administrateur pour obtenir un accès complet", "PE.Controllers.Main.warnLicenseUsersExceeded": "Vous avez dépassé le nombre maximal d’utilisateurs des éditeurs %1. Contactez votre administrateur pour en savoir davantage.", "PE.Controllers.Main.warnNoLicense": "Vous avez dépassé le nombre maximal de connexions simultanées aux éditeurs %1. Ce document sera ouvert à la lecture seulement.<br>Contactez l’équipe des ventes %1 pour mettre à jour les termes de la licence.", "PE.Controllers.Main.warnNoLicenseUsers": "Vous avez dépassé le nombre maximal d’utilisateurs des éditeurs %1. Contactez l’équipe des ventes %1 pour mettre à jour les termes de la licence.", "PE.Controllers.Main.warnProcessRightsChange": "Le droit d'édition du fichier vous a été refusé.", "PE.Controllers.Print.txtPrintRangeInvalid": "Plage d'impression non valide", "PE.Controllers.Print.txtPrintRangeSingleRange": "Saisissez un numéro de diapositive unique ou une plage de diapositives unique (par exemple, 5-12). Vous pouvez également imprimer en PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Avertissement", "PE.Controllers.Search.textNoTextFound": "Les données que vous recherchez n'ont pas pu être trouvées. Veuillez modifier vos options de recherche.", "PE.Controllers.Search.textReplaceSkipped": "Le remplacement a été effectué. {0} occurrences ont été sautées.", "PE.Controllers.Search.textReplaceSuccess": "La recherche a été effectuée. {0} occurrences ont été remplacées", "PE.Controllers.Search.warnReplaceString": "{0} n'est pas un caractère spécial valide pour la case Remplacer par.", "PE.Controllers.Statusbar.textDisconnect": "<b>La connexion est perdue</b><br>Tentative de connexion. Veuillez vérifier les paramètres de connexion.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "La police que vous allez enregistrer n'est pas disponible sur l'appareil actuel.<br>Le style du texte sera affiché à l'aide de l'une des polices de système, la police sauvée sera utilisée lorsqu'elle est disponible.<br>V<PERSON><PERSON><PERSON>-vous continuer?", "PE.Controllers.Toolbar.textAccent": "Types d'accentuation", "PE.Controllers.Toolbar.textBracket": "Crochets", "PE.Controllers.Toolbar.textEmptyImgUrl": "Spécifiez l'URL de l'image", "PE.Controllers.Toolbar.textFontSizeErr": "La valeur entrée est incorrecte.<br>Entrez une valeur numérique entre 1 et 300", "PE.Controllers.Toolbar.textFraction": "Fractions", "PE.Controllers.Toolbar.textFunction": "Fonctions", "PE.Controllers.Toolbar.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textIntegral": "Intégrales", "PE.Controllers.Toolbar.textLargeOperator": "Grands opérateurs", "PE.Controllers.Toolbar.textLimitAndLog": "Limites et logarithmes ", "PE.Controllers.Toolbar.textMatrix": "Matrices", "PE.Controllers.Toolbar.textOperator": "Opérateurs", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Symboles", "PE.Controllers.Toolbar.textWarning": "Avertissement", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Flèche gauche-droite au-dessus", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Flèche vers la gauche au-dessus", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Flèche vers la droite au-dessus", "PE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Barre inférieure", "PE.Controllers.Toolbar.txtAccent_BarTop": "Barre supérieure", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Formule encadrée (avec espace réservé)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formule encadrée (exemple)", "PE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Accolade inférieure", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Accolade supérieure", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vecteur A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC avec barre supérieure", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y avec barre supérieure", "PE.Controllers.Toolbar.txtAccent_DDDot": "Point triple", "PE.Controllers.Toolbar.txtAccent_DDot": "Point double", "PE.Controllers.Toolbar.txtAccent_Dot": "Point", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Barre supérieure double", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Regroupement de caractère en dessus", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Regroupement de caractère au-dessus", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpon gauche au-dessus", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpon droite au-dessus", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON> pointus", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Crochets pointus avec séparateur", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Crochets pointus avec deux séparateurs", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "C<PERSON>chet angulaire droite", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Crochet angulaire à gauche", "PE.Controllers.Toolbar.txtBracket_Curve": "Accolades", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Accolades avec séparateur", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Accolade droite", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Accolade gauche", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Cas (deux conditions)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Cas (trois conditions)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Objet empilé", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Objet de pile entre parenthèses", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Exemple de cas", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Coefficient binomial", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Coefficient binomial entre crochets pointus", "PE.Controllers.Toolbar.txtBracket_Line": "Barres verticales", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Barre verticale droite", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Barre verticale gauche", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON> verticales doubles", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Double barre verticale droite", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Double barre verticale gauche", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Plancher à droite", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Plancher à gauche", "PE.Controllers.Toolbar.txtBracket_Round": "Parenthèses", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parenthèses avec séparateur", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parenthèse droite", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Parenthèse gauche", "PE.Controllers.Toolbar.txtBracket_Square": "Crochets", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Placeholder entre deux crochets droits", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Crochets inversés", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Crochet droit", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Crochet gauche", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Placeholder entre deux crochets gauches", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Double crochets", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Double crochet droit", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Double crochet gauche", "PE.Controllers.Toolbar.txtBracket_UppLim": "Plafond", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Plafond à droite", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Plafond à gauche", "PE.Controllers.Toolbar.txtFractionDiagonal": "Fraction oblique", "PE.Controllers.Toolbar.txtFractionDifferential_1": "dx sur dy", "PE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y sur cap delta x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "partielle y sur partielle x", "PE.Controllers.Toolbar.txtFractionDifferential_4": "y delta sur delta x", "PE.Controllers.Toolbar.txtFractionHorizontal": "Fraction sur une ligne", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi divisé par 2", "PE.Controllers.Toolbar.txtFractionSmall": "Petite fraction", "PE.Controllers.Toolbar.txtFractionVertical": "Fraction sur deux lignes", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Cosinus inverse", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Сosinus inverse hyperbolique", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Cotangente inverse", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Сotangente inverse hyperbolique", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Cosécante inverse", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Сosécante inverse hyperbolique", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Sécante inverse", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Sécante inverse hyperbolique", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Sinus inverse", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Sinus inverse hyperbolique", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Tangente inverse", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Tangente inverse hyperbolique", "PE.Controllers.Toolbar.txtFunction_Cos": "Fonction cosinus", "PE.Controllers.Toolbar.txtFunction_Cosh": "Cosinus hyperbolique", "PE.Controllers.Toolbar.txtFunction_Cot": "Fonction cotangente", "PE.Controllers.Toolbar.txtFunction_Coth": "Cotangente hyperbolique", "PE.Controllers.Toolbar.txtFunction_Csc": "Fonction cosécante", "PE.Controllers.Toolbar.txtFunction_Csch": "Fonction cosécante hyperbolique", "PE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> thêta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cosinus 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Formule de la tangente", "PE.Controllers.Toolbar.txtFunction_Sec": "Fonction sécante", "PE.Controllers.Toolbar.txtFunction_Sech": "Sécante hyperbolique", "PE.Controllers.Toolbar.txtFunction_Sin": "Fonction sinus", "PE.Controllers.Toolbar.txtFunction_Sinh": "Sinus hyperbolique", "PE.Controllers.Toolbar.txtFunction_Tan": "Formule de la tangente", "PE.Controllers.Toolbar.txtFunction_Tanh": "Tangente hyperbolique", "PE.Controllers.Toolbar.txtIntegral": "Intégrale", "PE.Controllers.Toolbar.txtIntegral_dtheta": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "PE.Controllers.Toolbar.txtIntegral_dx": "Différent<PERSON> x", "PE.Controllers.Toolbar.txtIntegral_dy": "<PERSON><PERSON><PERSON><PERSON><PERSON> y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Intégrale avec limites empilées", "PE.Controllers.Toolbar.txtIntegralDouble": "Double intégrale", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Intégrale double avec limites empilées", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Intégrale double avec limites", "PE.Controllers.Toolbar.txtIntegralOriented": "Intégrale de contour", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Intégrale de contour avec limites empilées", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Intégrale de surface", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Intégrale de surface avec limites empilées", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Intégrale de surface avec limites", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Intégrale de contour avec limites", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Intégrale de volume", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Intégrale de volume avec limites empilées", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Intégrale de volume avec limites", "PE.Controllers.Toolbar.txtIntegralSubSup": "Intégrale avec limites", "PE.Controllers.Toolbar.txtIntegralTriple": "Triple intégrale", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Intégrale triple avec limites empilées", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Intégrale triple avec limites", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Et logique", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Et logique avec limite inférieure", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Et logique avec limites", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Et logique avec limite inférieure en indice", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Et logique avec limites en indice/exposant", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-produit", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproduit avec limite inférieure", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproduit avec limites", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coproduit avec limite inférieure en indice", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproduit avec limites en indice/en exposant", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Somme sur k de n choix k", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Somme de i égal à zéro à n", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Exemple de somme utilisant deux indices", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Exemple de produit", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Exemple d’union", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Ou logique", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Ou logique avec limite inférieure", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Ou logique avec limites", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Ou logique avec limite inférieure en indice", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Ou logique avec limites en indice/exposant", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection avec limite inférieure", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection avec limites", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection avec limite inférieure en indice", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection avec limites en indice/exposant", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produit", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produit avec limite inférieure", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produit avec limites", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produit avec limite inférieure en indice", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produit avec limites en indice/exposant", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Somme", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Somme avec limite inférieure", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Somme avec limites", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Somme avec limite inférieure en indice", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Somme avec limites en indice/exposant", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union avec limite inférieure", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union avec limites", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union avec limite inférieure en indice", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union avec limites en indice/exposant", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemple de limite", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemple de maximum", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logarithme naturel", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logarithme", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithme", "PE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "Matrice vide 1x2 ", "PE.Controllers.Toolbar.txtMatrix_1_3": "Matrice vide 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "Matrice vide 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "Matrice vide 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matrice 2x2 vide avec doubles barres verticales", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Déterminant 2x2 vide", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matrice d’identité 2x2 vide entre parenthèses", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matrice d’identité 2x2 vide entre crochets", "PE.Controllers.Toolbar.txtMatrix_2_3": "Matrice vide 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "Matrice vide 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "Matrice vide 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "Matrice vide 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Points de ligne de base", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Points d'interligne", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Points diagonaux", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Points verticaux", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Matrice avec pointillés entre parenthèses", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Matrice avec pointillés entre crochets", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "Matrice d’identité 2x2 avec zéros", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matrice d’identité 2x2 avec cellules hors diagonale vides", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Matrice d’identité 3x3 avec zéros", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matrice d’identité 3x3 avec cellules hors diagonale vides", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Flèche gauche-droite en dessous", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Flèche gauche-droite au-dessus", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Flèche vers la gauche en dessous", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Flèche vers la gauche au-dessus", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Flèche vers la droite en dessous", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Flèche vers la droite au-dessus", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Deux-points É<PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Produits", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Produits delta", "PE.Controllers.Toolbar.txtOperator_Definition": "Égal par définition à", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta égal à", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Double flèche gauche-droite au-dessous", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Double flèche gauche-droite au-dessus", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Flèche vers la gauche en dessous", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Flèche vers la gauche au-dessus", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Flèche vers la droite en dessous", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Flèche vers la droite au-dessus", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Égal", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Mesuré(e) par", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Côté droit de la formule quadratique", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON> car<PERSON> de a au carré plus b au carré", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON> avec degré", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Racine cubique", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radical avec degré", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_1": "x indice y au carré", "PE.Controllers.Toolbar.txtScriptCustom_2": "e au i négatif oméga t", "PE.Controllers.Toolbar.txtScriptCustom_3": "x au carré", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y exposant gauche n indice gauche un", "PE.Controllers.Toolbar.txtScriptSub": "Indice", "PE.Controllers.Toolbar.txtScriptSubSup": "Indice-Exposant", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Indice-Exposant gauche", "PE.Controllers.Toolbar.txtScriptSup": "Exposant", "PE.Controllers.Toolbar.txtSymbol_about": "Approximativement", "PE.Controllers.Toolbar.txtSymbol_additional": "Complément", "PE.Controllers.Toolbar.txtSymbol_aleph": "Aleph", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "PE.Controllers.Toolbar.txtSymbol_approx": "Presque égale à", "PE.Controllers.Toolbar.txtSymbol_ast": "Opérateur astérisque", "PE.Controllers.Toolbar.txtSymbol_beta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_beth": "Beth", "PE.Controllers.Toolbar.txtSymbol_bullet": "Opérateur puce", "PE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Racine cubique", "PE.Controllers.Toolbar.txtSymbol_cdots": "Trois points médians", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Approximativement égal à", "PE.Controllers.Toolbar.txtSymbol_cup": "Union", "PE.Controllers.Toolbar.txtSymbol_ddots": "Trois points diagonaux vers le coin bas à droite", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Signe de division", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Flèche vers le bas", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Ensemble vide", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identique à", "PE.Controllers.Toolbar.txtSymbol_eta": "Êta", "PE.Controllers.Toolbar.txtSymbol_exists": "Existant", "PE.Controllers.Toolbar.txtSymbol_factorial": "Factorielle", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Degrés Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "Pour tous", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "Est supérieur ou égal à", "PE.Controllers.Toolbar.txtSymbol_gg": "Plus grand que", "PE.Controllers.Toolbar.txtSymbol_greater": "Sup<PERSON>ur à", "PE.Controllers.Toolbar.txtSymbol_in": "Élément de", "PE.Controllers.Toolbar.txtSymbol_inc": "Incrément", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infini", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Flèche gauche", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Double flèche horizontale", "PE.Controllers.Toolbar.txtSymbol_leq": "Est inférieur ou égal à", "PE.Controllers.Toolbar.txtSymbol_less": "Inférieur à", "PE.Controllers.Toolbar.txtSymbol_ll": "Plus moins que", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON>ins", "PE.Controllers.Toolbar.txtSymbol_mp": "Moins plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "N'est pas égal à", "PE.Controllers.Toolbar.txtSymbol_ni": "Contient comme élément", "PE.Controllers.Toolbar.txtSymbol_not": "Signe négation", "PE.Controllers.Toolbar.txtSymbol_notexists": "Inexistant", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Différentielle partielle", "PE.Controllers.Toolbar.txtSymbol_percent": "Pourcentage", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus moins", "PE.Controllers.Toolbar.txtSymbol_propto": "Proportionnel à", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON> quatri<PERSON>", "PE.Controllers.Toolbar.txtSymbol_qed": "Ce qu'il fallait démontrer", "PE.Controllers.Toolbar.txtSymbol_rddots": "Trois points diagonaux vers le coin haut à droite", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Flèche droite", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Symbole de radical", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Par conséquent", "PE.Controllers.Toolbar.txtSymbol_theta": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_times": "Signe de multiplication", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Flèche vers le haut", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Variante epsilon", "PE.Controllers.Toolbar.txtSymbol_varphi": "Variante phi", "PE.Controllers.Toolbar.txtSymbol_varpi": "Variante pi", "PE.Controllers.Toolbar.txtSymbol_varrho": "Variante rho", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON> th<PERSON>ta", "PE.Controllers.Toolbar.txtSymbol_vdots": "Trois points verticaux", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Viewport.textFitPage": "Ajuster à la diapositive", "PE.Controllers.Viewport.textFitWidth": "Ajuster à la largeur", "PE.Views.Animation.str0_5": "0,5 s (très rapide)", "PE.Views.Animation.str1": "1 s (Rapide)", "PE.Views.Animation.str2": "2 s (<PERSON>yen)", "PE.Views.Animation.str20": "20 s (Extrêmement lent)", "PE.Views.Animation.str3": "3 s (Lent)", "PE.Views.Animation.str5": "5 s (<PERSON><PERSON><PERSON> lent)", "PE.Views.Animation.strDelay": "Retard", "PE.Views.Animation.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strRewind": "Rembobiner", "PE.Views.Animation.strStart": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strTrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "Aperçu partiel", "PE.Views.Animation.textMoreEffects": "Afficher plus d'effets", "PE.Views.Animation.textMoveEarlier": "<PERSON><PERSON><PERSON><PERSON> avant", "PE.Views.Animation.textMoveLater": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.Animation.textMultiple": "Multiple ", "PE.Views.Animation.textNone": "Aucun", "PE.Views.Animation.textNoRepeat": "(aucun)", "PE.Views.Animation.textOnClickOf": "Au clic sur", "PE.Views.Animation.textOnClickSequence": "<PERSON><PERSON><PERSON> de c<PERSON>s", "PE.Views.Animation.textStartAfterPrevious": "Après la précédente", "PE.Views.Animation.textStartOnClick": "Au clic", "PE.Views.Animation.textStartWithPrevious": "Avec la précédente", "PE.Views.Animation.textUntilEndOfSlide": "Jusqu’à la fin de la diapositive", "PE.Views.Animation.textUntilNextClick": "Jusqu’au clic suivant", "PE.Views.Animation.txtAddEffect": "Ajouter une animation", "PE.Views.Animation.txtAnimationPane": "Volet Animation", "PE.Views.Animation.txtParameters": "Paramètres", "PE.Views.Animation.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Aperçu de l'effet", "PE.Views.AnimationDialog.textTitle": "Autres effets", "PE.Views.ChartSettings.text3dDepth": "Profondeur (% de la base)", "PE.Views.ChartSettings.text3dHeight": "Hauteur (% de la base)", "PE.Views.ChartSettings.text3dRotation": "Rotation 3D", "PE.Views.ChartSettings.textAdvanced": "Afficher les paramètres avancés", "PE.Views.ChartSettings.textAutoscale": "Mise à l'échelle automatique", "PE.Views.ChartSettings.textChartType": "Modifier le type de graphique", "PE.Views.ChartSettings.textDefault": "Rotation par défaut", "PE.Views.ChartSettings.textDown": "Bas", "PE.Views.ChartSettings.textEditData": "Modifier les données", "PE.Views.ChartSettings.textHeight": "<PERSON><PERSON>", "PE.Views.ChartSettings.textKeepRatio": "Proportions constantes", "PE.Views.ChartSettings.textLeft": "À gauche", "PE.Views.ChartSettings.textNarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le champ de vision", "PE.Views.ChartSettings.textPerspective": "Perspective", "PE.Views.ChartSettings.textRight": "À droite", "PE.Views.ChartSettings.textRightAngle": "Axes à angle droit", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "Style", "PE.Views.ChartSettings.textUp": "En haut", "PE.Views.ChartSettings.textWiden": "<PERSON><PERSON><PERSON><PERSON> le champ de vision", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ChartSettings.textX": "Rotation X", "PE.Views.ChartSettings.textY": "Rotation Y", "PE.Views.ChartSettingsAdvanced.textAlt": "Texte de remplacement", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Description", "PE.Views.ChartSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme automatique, du graphique ou du tableau.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Titre", "PE.Views.ChartSettingsAdvanced.textCenter": "Au centre", "PE.Views.ChartSettingsAdvanced.textFrom": "De", "PE.Views.ChartSettingsAdvanced.textHeight": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontalement", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Proportions constantes", "PE.Views.ChartSettingsAdvanced.textPlacement": "Emplacement", "PE.Views.ChartSettingsAdvanced.textPosition": "Position", "PE.Views.ChartSettingsAdvanced.textSize": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textTitle": "Graphique - Paramètres avancés", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Coin supérieur gauche", "PE.Views.ChartSettingsAdvanced.textVertical": "Verticalement", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Définir le format par défaut pour {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Définir par défaut", "PE.Views.DateTimeDialog.textFormat": "Formats", "PE.Views.DateTimeDialog.textLang": "<PERSON><PERSON>", "PE.Views.DateTimeDialog.textUpdate": "Mettre à jour automatiquement", "PE.Views.DateTimeDialog.txtTitle": "Date et heure", "PE.Views.DocumentHolder.aboveText": "Au-dessus", "PE.Views.DocumentHolder.addCommentText": "Ajouter un commentaire", "PE.Views.DocumentHolder.addToLayoutText": "Ajouter dans une mise en page", "PE.Views.DocumentHolder.advancedChartText": "Paramètres avancés du graphique ", "PE.Views.DocumentHolder.advancedEquationText": "Paramètres d'équations", "PE.Views.DocumentHolder.advancedImageText": "Paramètres avancés de l'image", "PE.Views.DocumentHolder.advancedParagraphText": "Paramètres avancés du texte", "PE.Views.DocumentHolder.advancedShapeText": "Paramètres avancés de la forme", "PE.Views.DocumentHolder.advancedTableText": "Paramètres avancés du tableau", "PE.Views.DocumentHolder.alignmentText": "Alignement", "PE.Views.DocumentHolder.allLinearText": "Toutes - Linéaire", "PE.Views.DocumentHolder.allProfText": "Toutes - Professionnel", "PE.Views.DocumentHolder.belowText": "En dessous", "PE.Views.DocumentHolder.cellAlignText": "Alignement vertical de cellule", "PE.Views.DocumentHolder.cellText": "Cellule", "PE.Views.DocumentHolder.centerText": "Au centre", "PE.Views.DocumentHolder.columnText": "Colonne", "PE.Views.DocumentHolder.currLinearText": "Actuelles - Linéaire", "PE.Views.DocumentHolder.currProfText": "Actuelles - Professionnel", "PE.Views.DocumentHolder.deleteColumnText": "Supprimer la colonne", "PE.Views.DocumentHolder.deleteRowText": "Supprimer la ligne", "PE.Views.DocumentHolder.deleteTableText": "Su<PERSON><PERSON><PERSON> le tableau", "PE.Views.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.direct270Text": "Rotation du texte vers le haut", "PE.Views.DocumentHolder.direct90Text": "Rotation du texte vers le bas", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "Orientation du texte", "PE.Views.DocumentHolder.editChartText": "Modifier les données", "PE.Views.DocumentHolder.editHyperlinkText": "Modifier le lien hypertexte", "PE.Views.DocumentHolder.hyperlinkText": "Lien hypertexte", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON>gno<PERSON> tout", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Colonne à gauche", "PE.Views.DocumentHolder.insertColumnRightText": "Colonne à droite", "PE.Views.DocumentHolder.insertColumnText": "Insérer une colonne", "PE.Views.DocumentHolder.insertRowAboveText": "Ligne au-dessus", "PE.Views.DocumentHolder.insertRowBelowText": "Ligne en dessous", "PE.Views.DocumentHolder.insertRowText": "Ins<PERSON>rer une ligne", "PE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.langText": "Sélectionner la langue", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "À gauche", "PE.Views.DocumentHolder.loadSpellText": "Chargement des variantes en cours...", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON>ner les cellules", "PE.Views.DocumentHolder.mniCustomTable": "Inserer un tableau personnalisé", "PE.Views.DocumentHolder.moreText": "Plus de variantes...", "PE.Views.DocumentHolder.noSpellVariantsText": "Pas de variantes", "PE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON> act<PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "Supprimer le lien hypertexte", "PE.Views.DocumentHolder.rightText": "À droite", "PE.Views.DocumentHolder.rowText": "Ligne", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.spellcheckText": "Vérification orthographique", "PE.Views.DocumentHolder.splitCellsText": "Fractionner la cellule...", "PE.Views.DocumentHolder.splitCellTitleText": "Fractionner la cellule", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textAddHGuides": "Ajouter un repère horizontal", "PE.Views.DocumentHolder.textAddVGuides": "Ajouter un repère vertical", "PE.Views.DocumentHolder.textArrangeBack": "Mettre en arrière-plan", "PE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeForward": "Déplacer vers l'avant", "PE.Views.DocumentHolder.textArrangeFront": "Mettre au premier plan", "PE.Views.DocumentHolder.textClearGuides": "Effacer les guides", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Ajuster", "PE.Views.DocumentHolder.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCut": "Couper", "PE.Views.DocumentHolder.textDeleteGuide": "Supp<PERSON><PERSON> le repère", "PE.Views.DocumentHolder.textDistributeCols": "Distribuer les colonnes", "PE.Views.DocumentHolder.textDistributeRows": "Distribuer les lignes", "PE.Views.DocumentHolder.textEditPoints": "Modifier les points", "PE.Views.DocumentHolder.textFlipH": "Retourner horizontalement", "PE.Views.DocumentHolder.textFlipV": "Retourner verticalement", "PE.Views.DocumentHolder.textFromFile": "<PERSON>'un fichier", "PE.Views.DocumentHolder.textFromStorage": "À partir de l'espace de stockage", "PE.Views.DocumentHolder.textFromUrl": "D'une URL", "PE.Views.DocumentHolder.textGridlines": "Quadrillage", "PE.Views.DocumentHolder.textGuides": "Repères", "PE.Views.DocumentHolder.textNextPage": "Diapositive suivante", "PE.Views.DocumentHolder.textPaste": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textPrevPage": "Diapositive précédente", "PE.Views.DocumentHolder.textReplace": "Remplacer l’image", "PE.Views.DocumentHolder.textRotate": "Rotation", "PE.Views.DocumentHolder.textRotate270": "Faire pivoter à gauche de 90°", "PE.Views.DocumentHolder.textRotate90": "Faire pivoter à droite de 90°", "PE.Views.DocumentHolder.textRulers": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textSaveAsPicture": "Enregistrer en tant qu'image", "PE.Views.DocumentHolder.textShapeAlignBottom": "Aligner en bas", "PE.Views.DocumentHolder.textShapeAlignCenter": "Aligner au centre", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON> à gauche", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Aligner au milieu", "PE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "PE.Views.DocumentHolder.textShapeAlignTop": "Aligner en haut", "PE.Views.DocumentHolder.textShowGridlines": "<PERSON><PERSON><PERSON><PERSON> le quadrillage", "PE.Views.DocumentHolder.textShowGuides": "Afficher les repères", "PE.Views.DocumentHolder.textSlideSettings": "Paramètres de diapositive", "PE.Views.DocumentHolder.textSmartGuides": "Repères actifs", "PE.Views.DocumentHolder.textSnapObjects": "Fixer l'objet à la grille", "PE.Views.DocumentHolder.textUndo": "Annuler", "PE.Views.DocumentHolder.tipGuides": "Afficher les repères", "PE.Views.DocumentHolder.tipIsLocked": "Cet élément est en cours d'édition par un autre utilisateur.", "PE.Views.DocumentHolder.toDictionaryText": "Ajouter au dictionnaire", "PE.Views.DocumentHolder.txtAddBottom": "Ajouter bordure inférieure", "PE.Views.DocumentHolder.txtAddFractionBar": "Ajouter barre de fraction", "PE.Views.DocumentHolder.txtAddHor": "Ajouter une ligne horizontale", "PE.Views.DocumentHolder.txtAddLB": "Ajouter une ligne en bas à gauche", "PE.Views.DocumentHolder.txtAddLeft": "A<PERSON>ter bordure gauche", "PE.Views.DocumentHolder.txtAddLT": "Ajouter une ligne supérieure gauche", "PE.Views.DocumentHolder.txtAddRight": "Ajouter bordure droite", "PE.Views.DocumentHolder.txtAddTop": "Ajouter bordure supérieure", "PE.Views.DocumentHolder.txtAddVer": "Ajouter une ligne verticale", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> à caractère", "PE.Views.DocumentHolder.txtArrange": "Organiser", "PE.Views.DocumentHolder.txtBackground": "Arrière-plan", "PE.Views.DocumentHolder.txtBorderProps": "Propriétés de la bordure", "PE.Views.DocumentHolder.txtBottom": "En bas", "PE.Views.DocumentHolder.txtChangeLayout": "Modifier la disposition", "PE.Views.DocumentHolder.txtChangeTheme": "Modifier le thème", "PE.Views.DocumentHolder.txtColumnAlign": "L'alignement de la colonne", "PE.Views.DocumentHolder.txtDecreaseArg": "Diminuer la taille de l'argument", "PE.Views.DocumentHolder.txtDeleteArg": "Supprimer l'argument", "PE.Views.DocumentHolder.txtDeleteBreak": "Supprimer un saut manuel", "PE.Views.DocumentHolder.txtDeleteChars": "Supprimer caractères enserrant", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Supprimer caractères et séparateurs qui entourent", "PE.Views.DocumentHolder.txtDeleteEq": "Supprimer l'équation", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Supprimer caractère", "PE.Views.DocumentHolder.txtDeleteRadical": "Supprimer radical", "PE.Views.DocumentHolder.txtDeleteSlide": "Supprimer la diapositive", "PE.Views.DocumentHolder.txtDistribHor": "Distribuer horizontalement", "PE.Views.DocumentHolder.txtDistribVert": "Distribuer verticalement", "PE.Views.DocumentHolder.txtDuplicateSlide": "Dup<PERSON>r la diapositive", "PE.Views.DocumentHolder.txtFractionLinear": "Remplacer par une fraction sur une ligne", "PE.Views.DocumentHolder.txtFractionSkewed": "Remplacer par une fraction oblique", "PE.Views.DocumentHolder.txtFractionStacked": "Remplacer par une fraction sur deux lignes", "PE.Views.DocumentHolder.txtGroup": "Grouper", "PE.Views.DocumentHolder.txtGroupCharOver": "Char au-dessus du texte", "PE.Views.DocumentHolder.txtGroupCharUnder": "Char en-dessus du texte", "PE.Views.DocumentHolder.txtHideBottom": "Masquer bordure inférieure", "PE.Views.DocumentHolder.txtHideBottomLimit": "Cacher limite inférieure", "PE.Views.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON> le crochet de fermeture", "PE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON> de<PERSON>", "PE.Views.DocumentHolder.txtHideHor": "Cacher ligne horizontale", "PE.Views.DocumentHolder.txtHideLB": "Cacher la ligne en bas à gauche", "PE.Views.DocumentHolder.txtHideLeft": "Cacher la bordure gauche", "PE.Views.DocumentHolder.txtHideLT": "Cacher la ligne en haut à gauche", "PE.Views.DocumentHolder.txtHideOpenBracket": "<PERSON>acher crochet d'ouverture", "PE.Views.DocumentHolder.txtHidePlaceholder": "Cacher espace réservé", "PE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON> bordure droite", "PE.Views.DocumentHolder.txtHideTop": "Cacher bordure supérieure", "PE.Views.DocumentHolder.txtHideTopLimit": "Cacher limite supérieure", "PE.Views.DocumentHolder.txtHideVer": "Cacher ligne verticale", "PE.Views.DocumentHolder.txtIncreaseArg": "Augmenter la taille de l'argument", "PE.Views.DocumentHolder.txtInsertArgAfter": "Insérer l'argument après", "PE.Views.DocumentHolder.txtInsertArgBefore": "Insérer un argument avant", "PE.Views.DocumentHolder.txtInsertBreak": "In<PERSON><PERSON><PERSON> pause manuelle", "PE.Views.DocumentHolder.txtInsertEqAfter": "Insérer équation après", "PE.Views.DocumentHolder.txtInsertEqBefore": "Insérer l'équation avant", "PE.Views.DocumentHolder.txtKeepTextOnly": "Garder le texte seulement", "PE.Views.DocumentHolder.txtLimitChange": "Modifier l'emplacement des limites", "PE.Views.DocumentHolder.txtLimitOver": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> du texte", "PE.Views.DocumentHolder.txtLimitUnder": "Limite en dessous du texte", "PE.Views.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON>er crochets à la hauteur de l'argument", "PE.Views.DocumentHolder.txtMatrixAlign": "Alignement de la matrice", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Déplacer la diapositive vers la fin", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Déplacer la diapositive au début", "PE.Views.DocumentHolder.txtNewSlide": "Nouvelle diapositive", "PE.Views.DocumentHolder.txtOverbar": "<PERSON>e au-dessus d'un texte", "PE.Views.DocumentHolder.txtPasteDestFormat": "Utiliser le thème de destination", "PE.Views.DocumentHolder.txtPastePicture": "Image", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Garder la mise en forme source", "PE.Views.DocumentHolder.txtPressLink": "Appuyez sur {0} et cliquez sur le lien", "PE.Views.DocumentHolder.txtPreview": "Démarrer le diaporama", "PE.Views.DocumentHolder.txtPrintSelection": "Imprimer la sélection", "PE.Views.DocumentHolder.txtRemFractionBar": "Supprimer la barre de fraction", "PE.Views.DocumentHolder.txtRemLimit": "Supprimer la limite", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Supp<PERSON>er le caractère d'accent", "PE.Views.DocumentHolder.txtRemoveBar": "Supprimer la barre", "PE.Views.DocumentHolder.txtRemScripts": "Supprimer scripts", "PE.Views.DocumentHolder.txtRemSubscript": "Supprimer la souscription", "PE.Views.DocumentHolder.txtRemSuperscript": "Supprimer la suscription", "PE.Views.DocumentHolder.txtResetLayout": "Réinitialiser la diapositive", "PE.Views.DocumentHolder.txtScriptsAfter": "Scripts après le texte", "PE.Views.DocumentHolder.txtScriptsBefore": "Scripts avant le texte", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "PE.Views.DocumentHolder.txtShowBottomLimit": "Montrer limite inférieure", "PE.Views.DocumentHolder.txtShowCloseBracket": "Afficher crochet de fermeture", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "PE.Views.DocumentHolder.txtShowOpenBracket": "Afficher crochet d'ouverture", "PE.Views.DocumentHolder.txtShowPlaceholder": "Afficher espace réservé", "PE.Views.DocumentHolder.txtShowTopLimit": "Afficher limite supérieure", "PE.Views.DocumentHolder.txtSlide": "Diapositive", "PE.Views.DocumentHolder.txtSlideHide": "Masquer la diapositive", "PE.Views.DocumentHolder.txtStretchBrackets": "Allonger des crochets", "PE.Views.DocumentHolder.txtTop": "En haut", "PE.Views.DocumentHolder.txtUnderbar": "Barre en dessous d'un texte", "PE.Views.DocumentHolder.txtUngroup": "Dissocier", "PE.Views.DocumentHolder.txtWarnUrl": "Cliquer sur ce lien peut être dangereux pour votre appareil et vos données. <br>Êtes-vous sûr de vouloir continuer ?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "Alignement vertical", "PE.Views.DocumentPreview.goToSlideText": "Atteindre la diapositive", "PE.Views.DocumentPreview.slideIndexText": "Diapositive {0} de {1}", "PE.Views.DocumentPreview.txtClose": "<PERSON><PERSON><PERSON> le diaporama", "PE.Views.DocumentPreview.txtEndSlideshow": "Fin du diaporame", "PE.Views.DocumentPreview.txtExitFullScreen": "<PERSON><PERSON><PERSON> le mode plein écran", "PE.Views.DocumentPreview.txtFinalMessage": "La fin de l'aperçu de la diapositive. Cliquez pour quitter.", "PE.Views.DocumentPreview.txtFullScreen": "Plein écran", "PE.Views.DocumentPreview.txtNext": "Diapositive suivante", "PE.Views.DocumentPreview.txtPageNumInvalid": "Numéro de la diapositive non valide", "PE.Views.DocumentPreview.txtPause": "Pauser la présentation", "PE.Views.DocumentPreview.txtPlay": "Démarrer la présentation", "PE.Views.DocumentPreview.txtPrev": "Diapositive précédente", "PE.Views.DocumentPreview.txtReset": "Remettre à zéro", "PE.Views.FileMenu.btnAboutCaption": "À propos", "PE.Views.FileMenu.btnBackCaption": "<PERSON>u<PERSON><PERSON>r l'emplacement du fichier", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON> le menu", "PE.Views.FileMenu.btnCreateNewCaption": "Nouvelle présentation", "PE.Views.FileMenu.btnDownloadCaption": "Télécharger comme", "PE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHelpCaption": "Aide", "PE.Views.FileMenu.btnHistoryCaption": "Historique des versions", "PE.Views.FileMenu.btnInfoCaption": "Descriptif", "PE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON>ger", "PE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnRenameCaption": "<PERSON>mmer", "PE.Views.FileMenu.btnReturnCaption": "Retour à la présentation", "PE.Views.FileMenu.btnRightsCaption": "Droits d'accès", "PE.Views.FileMenu.btnSaveAsCaption": "Enregistrer sous", "PE.Views.FileMenu.btnSaveCaption": "Enregistrer", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Enregistrer une copie comme", "PE.Views.FileMenu.btnSettingsCaption": "Paramètres avancés", "PE.Views.FileMenu.btnToEditCaption": "Modifier la présentation", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Présentation vide", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Créer nouvelle", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Appliquer", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Ajouter un auteur", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "A<PERSON>ter du texte", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Changer les droits d'accès", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Commentaire", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Dernière modification par", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Date de dernière modification", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Emplacement", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personnes qui ont des droits", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Objet", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titre", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Changer les droits d'accès", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Personnes qui ont des droits", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Avertissement", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Avec mot de passe", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protéger la présentation", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Avec signature", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Modifier la présentation", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "La modification du document va entrainer la suppression des signatures.<br>Êtes-vous sur de vouloir continuer ?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Cette présentation a été protégé par mot de passe", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Des signatures valables ont été ajouté au document. Le document est protégé contre les modifications.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Certaines signatures numériques sont non valides ou n'ont pas pu être vérifiées. Le document est protégé contre la modification.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Voir les signatures", "PE.Views.FileMenuPanels.Settings.okButtonText": "Appliquer", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Mode de co-édition ", "PE.Views.FileMenuPanels.Settings.strFast": "Rapide", "PE.Views.FileMenuPanels.Settings.strFontRender": "Hinting de la police", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorer les mots en MAJUSCULES", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignorer les mots contenant des chiffres", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Réglages macros", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Afficher le bouton Options de collage lorsque le contenu est collé ", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Afficher les modifications apportées par d'autres utilisateurs", "PE.Views.FileMenuPanels.Settings.strStrict": "Strict", "PE.Views.FileMenuPanels.Settings.strTheme": "Thème d’interface", "PE.Views.FileMenuPanels.Settings.strUnit": "Unité de mesure", "PE.Views.FileMenuPanels.Settings.strZoom": "Valeur de zoom par défaut", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Toutes les 10 minutes", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Toutes les 30 minutes", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Toutes les 5 minutes", "PE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON> he<PERSON>", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Guides d'alignement", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Récupération automatique", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Enregistrement automatique", "PE.Views.FileMenuPanels.Settings.textDisabled": "Désactivé", "PE.Views.FileMenuPanels.Settings.textForceSave": "Enregistrer des versions intermédiaires", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON> minute", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON><PERSON><PERSON> toutes les modifications", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Options de correction automatique", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Mise en cache par défaut", "PE.Views.FileMenuPanels.Settings.txtCm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaboration", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Édition et sauvegarde", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Coédition en temps réel. Toutes les modifications sont enregistrées automatiquement", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Ajuster à la diapositive", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Ajuster à la largeur", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtMac": "comme OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "Vérification", "PE.Views.FileMenuPanels.Settings.txtPt": "Point", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Afficher le bouton d'impression rapide dans l'en-tête de l'éditeur", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Le document sera imprimé sur la dernière imprimante sélectionnée ou par défaut", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Activer tout", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Activer toutes les macros sans notification", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Vérification de l'orthographe", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON> tout", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Désactiver toutes les macros sans notification", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilise<PERSON> le bouton \"Enregistrer\" pour synchroniser les changements apportés par vous et d'autres personnes", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utiliser la touche Alt pour naviguer dans l'interface utilisateur à l'aide du clavier", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utiliser la touche Option pour naviguer dans l'interface utilisateur à l'aide du clavier", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Montrer  la notification", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Désactiver toutes les macros avec notification", "PE.Views.FileMenuPanels.Settings.txtWin": "comme Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Espace de travail", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.GridSettings.textSpacing": "Espacement", "PE.Views.GridSettings.textTitle": "Paramètres de la grille", "PE.Views.HeaderFooterDialog.applyAllText": "Appliquer à tous", "PE.Views.HeaderFooterDialog.applyText": "Appliquer", "PE.Views.HeaderFooterDialog.diffLanguage": "Vous ne pouvez pas utiliser la langue de date différente de celle du masque des diapositives.<br>Pour modifier le masque, cliquez \" Appliquer à toutes \" au lieu de \" Appliquer \"", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Avertissement", "PE.Views.HeaderFooterDialog.textDateTime": "Date et heure", "PE.Views.HeaderFooterDialog.textFixed": "Corrigé", "PE.Views.HeaderFooterDialog.textFooter": "Texte en pied de page", "PE.Views.HeaderFooterDialog.textFormat": "Formats", "PE.Views.HeaderFooterDialog.textLang": "<PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textNotTitle": "Ne pas afficher sur la diapositive titre", "PE.Views.HeaderFooterDialog.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textSlideNum": "Numéro de diapositive", "PE.Views.HeaderFooterDialog.textTitle": "Paramètres du pied de page", "PE.Views.HeaderFooterDialog.textUpdate": "Mettre à jour automatiquement", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON> <PERSON>", "PE.Views.HyperlinkSettingsDialog.textDefault": "Fragment du texte sélectionné", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Entrez une légende ici", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Entrez un lien ici", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Enterez une info-bulle ici", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Lien externe", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Diapositive dans cette présentation", "PE.Views.HyperlinkSettingsDialog.textSlides": "Diapositives", "PE.Views.HyperlinkSettingsDialog.textTipText": "Texte de l'info-bulle", "PE.Views.HyperlinkSettingsDialog.textTitle": "Paramètres du lien hypertexte", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Champ obligatoire", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Première diapositive", "PE.Views.HyperlinkSettingsDialog.txtLast": "Dernière diapositive", "PE.Views.HyperlinkSettingsDialog.txtNext": "Diapositive suivante", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ce champ doit être une URL au format \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Diapositive précédente", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Ce champ est limité à 2083 caractères.", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Diapositive", "PE.Views.ImageSettings.textAdvanced": "Afficher les paramètres avancés", "PE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFill": "Remplissage", "PE.Views.ImageSettings.textCropFit": "Ajuster", "PE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON><PERSON> à la forme", "PE.Views.ImageSettings.textEdit": "Modifier", "PE.Views.ImageSettings.textEditObject": "Modifier l'objet", "PE.Views.ImageSettings.textFitSlide": "Ajuster à la diapositive", "PE.Views.ImageSettings.textFlip": "Retournement", "PE.Views.ImageSettings.textFromFile": "<PERSON>'un fichier", "PE.Views.ImageSettings.textFromStorage": "À partir de l'espace de stockage", "PE.Views.ImageSettings.textFromUrl": "D'une URL", "PE.Views.ImageSettings.textHeight": "<PERSON><PERSON>", "PE.Views.ImageSettings.textHint270": "Faire pivoter à gauche de 90°", "PE.Views.ImageSettings.textHint90": "Faire pivoter à droite de 90°", "PE.Views.ImageSettings.textHintFlipH": "Retourner horizontalement", "PE.Views.ImageSettings.textHintFlipV": "Retourner verticalement", "PE.Views.ImageSettings.textInsert": "Remplacer l’image", "PE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON> act<PERSON>", "PE.Views.ImageSettings.textRecentlyUsed": "Récemment utilisé", "PE.Views.ImageSettings.textRotate90": "Faire pivoter de 90°", "PE.Views.ImageSettings.textRotation": "Rotation", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Texte de remplacement", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Description", "PE.Views.ImageSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme automatique, du graphique ou du tableau.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Titre", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Au centre", "PE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textFrom": "De", "PE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontalement", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalement", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Proportions constantes", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON> r<PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "Emplacement", "PE.Views.ImageSettingsAdvanced.textPosition": "Position", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "Image - Paramètres avancés", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Coin supérieur gauche", "PE.Views.ImageSettingsAdvanced.textVertical": "Verticalement", "PE.Views.ImageSettingsAdvanced.textVertically": "Verticalement", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "À propos", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "Commentaires", "PE.Views.LeftMenu.tipPlugins": "Plug-ins", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "Diapositives", "PE.Views.LeftMenu.tipSupport": "Commentaires & assistance", "PE.Views.LeftMenu.tipTitles": "Titres", "PE.Views.LeftMenu.txtDeveloper": "MODE DEVELOPPEUR", "PE.Views.LeftMenu.txtEditor": "Éditeur de Présentation", "PE.Views.LeftMenu.txtLimit": "Accès limité", "PE.Views.LeftMenu.txtTrial": "MODE DEMO", "PE.Views.LeftMenu.txtTrialDev": "Essai en mode Développeur", "PE.Views.ParagraphSettings.strLineHeight": "Interligne", "PE.Views.ParagraphSettings.strParagraphSpacing": "Espacement de paragraphe", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "Avant", "PE.Views.ParagraphSettings.textAdvanced": "Afficher les paramètres avancés", "PE.Views.ParagraphSettings.textAt": "A", "PE.Views.ParagraphSettings.textAtLeast": "Au moins", "PE.Views.ParagraphSettings.textAuto": "Multiple ", "PE.Views.ParagraphSettings.textExact": "Exactement", "PE.Views.ParagraphSettings.txtAutoText": "Auto", "PE.Views.ParagraphSettingsAdvanced.noTabs": "The specified tabs will appear in this field", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Barré double", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Retraits", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "À gauche", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interligne", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "À droite", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Avant", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Spécial", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Police", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Retraits et espacement", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Petites majuscules", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Espacement", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Indice", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Exposant", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulation", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Alignement", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple ", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espacement des caractères", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Onglet par défaut", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "Exactement", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Première ligne", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendu", "PE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(aucun)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Supp<PERSON>er tout", "PE.Views.ParagraphSettingsAdvanced.textSet": "Spécifier", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Au centre", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "À gauche", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Position de l'onglet", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "À droite", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraphe - Paramètres avancés", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "PE.Views.PrintWithPreview.txtAllPages": "Toutes les diapositives", "PE.Views.PrintWithPreview.txtCurrentPage": "Diapositive actuelle", "PE.Views.PrintWithPreview.txtCustomPages": "Impression personnalisée", "PE.Views.PrintWithPreview.txtEmptyTable": "Il n'y a rien à imprimer car la présentation est vide.", "PE.Views.PrintWithPreview.txtOf": "de {0}", "PE.Views.PrintWithPreview.txtPage": "Diapositive", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Numéro de diapositive invalide", "PE.Views.PrintWithPreview.txtPages": "Diapositives", "PE.Views.PrintWithPreview.txtPaperSize": "Format de papier", "PE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtPrintPdf": "Imprimer au format PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Zone d'impression", "PE.Views.RightMenu.txtChartSettings": "Paramètres du graphique", "PE.Views.RightMenu.txtImageSettings": "Paramètres de l'image", "PE.Views.RightMenu.txtParagraphSettings": "Paramètres du paragraphe", "PE.Views.RightMenu.txtShapeSettings": "Paramètres de la forme", "PE.Views.RightMenu.txtSignatureSettings": "Paramètres de signature", "PE.Views.RightMenu.txtSlideSettings": "Paramètres de la diapositive", "PE.Views.RightMenu.txtTableSettings": "Paramètres du tableau", "PE.Views.RightMenu.txtTextArtSettings": "Paramètres de texte d'art", "PE.Views.ShapeSettings.strBackground": "Couleur d'arrière-plan", "PE.Views.ShapeSettings.strChange": "Modifier la forme automatique", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "Remplissage", "PE.Views.ShapeSettings.strForeground": "Couleur de premier plan", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strShadow": "Ajouter une ombre", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "Ligne", "PE.Views.ShapeSettings.strTransparency": "Opacité", "PE.Views.ShapeSettings.strType": "Type", "PE.Views.ShapeSettings.textAdvanced": "Afficher les paramètres avancés", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "La valeur saisie est incorrecte. <br>En<PERSON>z une valeur de 0 à 1584 points.", "PE.Views.ShapeSettings.textColor": "Couleur de remplissage", "PE.Views.ShapeSettings.textDirection": "Direction", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON> <PERSON> mod<PERSON>", "PE.Views.ShapeSettings.textFlip": "Retournement", "PE.Views.ShapeSettings.textFromFile": "<PERSON>'un fichier", "PE.Views.ShapeSettings.textFromStorage": "À partir de l'espace de stockage", "PE.Views.ShapeSettings.textFromUrl": "D'une URL", "PE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textGradientFill": "Remplissage en dégradé", "PE.Views.ShapeSettings.textHint270": "Faire pivoter à gauche de 90°", "PE.Views.ShapeSettings.textHint90": "Faire pivoter à droite de 90°", "PE.Views.ShapeSettings.textHintFlipH": "Retourner horizontalement", "PE.Views.ShapeSettings.textHintFlipV": "Retourner verticalement", "PE.Views.ShapeSettings.textImageTexture": "Image ou texture", "PE.Views.ShapeSettings.textLinear": "Linéaire", "PE.Views.ShapeSettings.textNoFill": "Pas de remplissage", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPosition": "Position", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Récemment utilisés", "PE.Views.ShapeSettings.textRotate90": "Faire pivoter de 90°", "PE.Views.ShapeSettings.textRotation": "Rotation", "PE.Views.ShapeSettings.textSelectImage": "Sélectionner l'image", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "Étirement", "PE.Views.ShapeSettings.textStyle": "Style", "PE.Views.ShapeSettings.textTexture": "D'une texture", "PE.Views.ShapeSettings.textTile": "Mosaïque", "PE.Views.ShapeSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "PE.Views.ShapeSettings.txtBrownPaper": "Papier brun", "PE.Views.ShapeSettings.txtCanvas": "Toile", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "Tissu foncé", "PE.Views.ShapeSettings.txtGrain": "Grain", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON>r gris", "PE.Views.ShapeSettings.txtKnit": "Tricot", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> de ligne", "PE.Views.ShapeSettings.txtPapyrus": "Papyrus", "PE.Views.ShapeSettings.txtWood": "<PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "Colonnes", "PE.Views.ShapeSettingsAdvanced.strMargins": "Marges intérieures", "PE.Views.ShapeSettingsAdvanced.textAlt": "Texte de remplacement", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Description", "PE.Views.ShapeSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme automatique, du graphique ou du tableau.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Titre", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "Flèches", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Ajuster automatiquement", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Style de début", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "En bas", "PE.Views.ShapeSettingsAdvanced.textCapType": "Type de lettrine", "PE.Views.ShapeSettingsAdvanced.textCenter": "Au centre", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Nombre de colonnes", "PE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON> de <PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Style final", "PE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFrom": "De", "PE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontalement", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalement", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Type de connexion", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proportions constantes", "PE.Views.ShapeSettingsAdvanced.textLeft": "À gauche", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Style de la ligne", "PE.Views.ShapeSettingsAdvanced.textMiter": "Onglet", "PE.Views.ShapeSettingsAdvanced.textNofit": "Ne pas ajuster automatiquement", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Emplacement", "PE.Views.ShapeSettingsAdvanced.textPosition": "Position", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Redimensionner la forme pour ajuster le texte", "PE.Views.ShapeSettingsAdvanced.textRight": "À droite", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "PE.Views.ShapeSettingsAdvanced.textRound": "Arrondi", "PE.Views.ShapeSettingsAdvanced.textShrink": "<PERSON><PERSON><PERSON><PERSON><PERSON>r le texte dans la zone de débordement uniquement", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Espacement entre les colonnes", "PE.Views.ShapeSettingsAdvanced.textSquare": "Carré", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Zone de texte", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forme - Paramètres avancés", "PE.Views.ShapeSettingsAdvanced.textTop": "En haut", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Coin supérieur gauche", "PE.Views.ShapeSettingsAdvanced.textVertical": "Verticalement", "PE.Views.ShapeSettingsAdvanced.textVertically": "Verticalement", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Poids et flèches", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "Aucun", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Avertissement", "PE.Views.SignatureSettings.strDelete": "Supprimer la signature", "PE.Views.SignatureSettings.strDetails": "<PERSON><PERSON><PERSON> de la signature", "PE.Views.SignatureSettings.strInvalid": "Signatures non valides", "PE.Views.SignatureSettings.strSign": "Signer", "PE.Views.SignatureSettings.strSignature": "Signature", "PE.Views.SignatureSettings.strValid": "Signatures valables", "PE.Views.SignatureSettings.txtContinueEditing": "Modifier quand même", "PE.Views.SignatureSettings.txtEditWarning": "La modification du document va entrainer la suppression des signatures.<br>Êtes-vous sur de vouloir continuer ?", "PE.Views.SignatureSettings.txtRemoveWarning": "V<PERSON><PERSON>z vous supprimer cette signature ? <br> <PERSON><PERSON> ne peut pas être r<PERSON>.", "PE.Views.SignatureSettings.txtSigned": "Des signatures valables ont été ajouté au document. Le document est protégé contre les modifications.", "PE.Views.SignatureSettings.txtSignedInvalid": "Certaines signatures numériques sont non valides ou n'ont pas pu être vérifiées. Le document est protégé contre la modification.", "PE.Views.SlideSettings.strBackground": "Couleur d'arrière-plan", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "Afficher la date et l'heure", "PE.Views.SlideSettings.strFill": "Arrière-plan", "PE.Views.SlideSettings.strForeground": "Couleur de premier plan", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strSlideNum": "<PERSON><PERSON><PERSON><PERSON> le numéro de diapositive", "PE.Views.SlideSettings.strTransparency": "Opacité", "PE.Views.SlideSettings.textAdvanced": "Afficher les paramètres avancés", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Couleur de remplissage", "PE.Views.SlideSettings.textDirection": "Direction", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON> <PERSON> mod<PERSON>", "PE.Views.SlideSettings.textFromFile": "<PERSON>'un fichier", "PE.Views.SlideSettings.textFromStorage": "À partir de l'espace de stockage", "PE.Views.SlideSettings.textFromUrl": "D'une URL", "PE.Views.SlideSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textGradientFill": "Remplissage en dégradé", "PE.Views.SlideSettings.textImageTexture": "Image ou Texture", "PE.Views.SlideSettings.textLinear": "Linéaire", "PE.Views.SlideSettings.textNoFill": "Pas de remplissage", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPosition": "Position", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Annuler modifications", "PE.Views.SlideSettings.textSelectImage": "Sélectionner l'image", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "Étirement", "PE.Views.SlideSettings.textStyle": "Style", "PE.Views.SlideSettings.textTexture": "D'une texture", "PE.Views.SlideSettings.textTile": "Mosaïque", "PE.Views.SlideSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "PE.Views.SlideSettings.txtBrownPaper": "Papier brun", "PE.Views.SlideSettings.txtCanvas": "Toile", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "Tissu foncé", "PE.Views.SlideSettings.txtGrain": "Grain", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON>r gris", "PE.Views.SlideSettings.txtKnit": "Tricot", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "Papyrus", "PE.Views.SlideSettings.txtWood": "<PERSON>", "PE.Views.SlideshowSettings.textLoop": "Boucle continue jusqu'à ce que le bouton «Esc» soit pressé", "PE.Views.SlideshowSettings.textTitle": "Afficher les paramètres", "PE.Views.SlideSizeSettings.strLandscape": "Paysage", "PE.Views.SlideSizeSettings.strPortrait": "Portrait", "PE.Views.SlideSizeSettings.textHeight": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientation des diapositives", "PE.Views.SlideSizeSettings.textSlideSize": "Taille des diapositives", "PE.Views.SlideSizeSettings.textTitle": "Paramètres de la taille des diapositives", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Diapositives 35 mm", "PE.Views.SlideSizeSettings.txtA3": "A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Format A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Bannière", "PE.Views.SlideSizeSettings.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtLedger": "Ledger Paper (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "<PERSON><PERSON><PERSON> à lettres (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Transparent", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Plein écran", "PE.Views.Statusbar.goToPageText": "Atteindre la diapositive", "PE.Views.Statusbar.pageIndexText": "Diapositive {0} de {1}", "PE.Views.Statusbar.textShowBegin": "Afficher dès le début", "PE.Views.Statusbar.textShowCurrent": "<PERSON><PERSON><PERSON><PERSON> de la diapositive actuelle", "PE.Views.Statusbar.textShowPresenterView": "Afficher en mode présentateur", "PE.Views.Statusbar.tipAccessRights": "<PERSON><PERSON><PERSON> des droits d'accès aux documents ", "PE.Views.Statusbar.tipFitPage": "Ajuster à la diapositive", "PE.Views.Statusbar.tipFitWidth": "Ajuster à la largeur", "PE.Views.Statusbar.tipPreview": "Démarrer le diaporama", "PE.Views.Statusbar.tipSetLang": "Définir la langue du texte", "PE.Views.Statusbar.tipZoomFactor": "Zoom", "PE.Views.Statusbar.tipZoomIn": "Zoom avant", "PE.Views.Statusbar.tipZoomOut": "Zoom arri<PERSON>", "PE.Views.Statusbar.txtPageNumInvalid": "Numéro de la diapositive non valide", "PE.Views.TableSettings.deleteColumnText": "Supprimer la colonne", "PE.Views.TableSettings.deleteRowText": "Supprimer la ligne", "PE.Views.TableSettings.deleteTableText": "Su<PERSON><PERSON><PERSON> le tableau", "PE.Views.TableSettings.insertColumnLeftText": "Insérer une colonne à gauche", "PE.Views.TableSettings.insertColumnRightText": "Insérer une colonne à droite", "PE.Views.TableSettings.insertRowAboveText": "Insérer une ligne au-dessus", "PE.Views.TableSettings.insertRowBelowText": "Insérer une ligne en dessous", "PE.Views.TableSettings.mergeCellsText": "<PERSON>ner les cellules", "PE.Views.TableSettings.selectCellText": "Sélectionner la cellule", "PE.Views.TableSettings.selectColumnText": "Sélectionner la colonne", "PE.Views.TableSettings.selectRowText": "Sélectionner la ligne", "PE.Views.TableSettings.selectTableText": "Sélect<PERSON>ner le tableau", "PE.Views.TableSettings.splitCellsText": "Fractionner la cellule...", "PE.Views.TableSettings.splitCellTitleText": "Fractionner la cellule", "PE.Views.TableSettings.textAdvanced": "Afficher les paramètres avancés", "PE.Views.TableSettings.textBackColor": "Couleur d'arrière-plan", "PE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "Style des bordures", "PE.Views.TableSettings.textCellSize": "Taille de la cellule", "PE.Views.TableSettings.textColumns": "Colonnes", "PE.Views.TableSettings.textDistributeCols": "Distribuer les colonnes", "PE.Views.TableSettings.textDistributeRows": "Distribuer les lignes", "PE.Views.TableSettings.textEdit": "Lignes et colonnes", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON> mod<PERSON>", "PE.Views.TableSettings.textFirst": "Premier", "PE.Views.TableSettings.textHeader": "<PERSON>-tête", "PE.Views.TableSettings.textHeight": "<PERSON><PERSON>", "PE.Views.TableSettings.textLast": "<PERSON><PERSON>", "PE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Sélectionnez les bordures à modifier en appliquant le style choisi ci-dessus", "PE.Views.TableSettings.textTemplate": "Sélectionner à partir d'un modèle", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Bordure extérieure et la totalité des lignes intérieures", "PE.Views.TableSettings.tipBottom": "Seulement bordure extérieure inférieure", "PE.Views.TableSettings.tipInner": "Seulement lignes intérieures", "PE.Views.TableSettings.tipInnerHor": "Seulement lignes intérieures horizontales", "PE.Views.TableSettings.tipInnerVert": "Seulement lignes verticales intérieures", "PE.Views.TableSettings.tipLeft": "Seulement bordure extérieure gauche", "PE.Views.TableSettings.tipNone": "Pas de bordures", "PE.Views.TableSettings.tipOuter": "Seulement bordure extérieure", "PE.Views.TableSettings.tipRight": "Seulement bordure extérieure droite", "PE.Views.TableSettings.tipTop": "Seulement bordure extérieure supérieure", "PE.Views.TableSettings.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Dark": "Sombre", "PE.Views.TableSettings.txtGroupTable_Light": "<PERSON>", "PE.Views.TableSettings.txtGroupTable_Medium": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Optimal": "Meilleure correspondance pour le document", "PE.Views.TableSettings.txtNoBorders": "Pas de bordures", "PE.Views.TableSettings.txtTable_Accent": "Accentuation", "PE.Views.TableSettings.txtTable_DarkStyle": "Style Foncé", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON>", "PE.Views.TableSettings.txtTable_MediumStyle": "Style Moyen", "PE.Views.TableSettings.txtTable_NoGrid": "Pas de grille", "PE.Views.TableSettings.txtTable_NoStyle": "Pas de style", "PE.Views.TableSettings.txtTable_TableGrid": "Grille du tableau", "PE.Views.TableSettings.txtTable_ThemedStyle": "Style à thème", "PE.Views.TableSettingsAdvanced.textAlt": "Texte de remplacement", "PE.Views.TableSettingsAdvanced.textAltDescription": "Description", "PE.Views.TableSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme automatique, du graphique ou du tableau.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Titre", "PE.Views.TableSettingsAdvanced.textBottom": "En bas", "PE.Views.TableSettingsAdvanced.textCenter": "Au centre", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Utiliser marges par défaut", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Marges par défaut", "PE.Views.TableSettingsAdvanced.textFrom": "De", "PE.Views.TableSettingsAdvanced.textHeight": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontalement", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Proportions constantes", "PE.Views.TableSettingsAdvanced.textLeft": "À gauche", "PE.Views.TableSettingsAdvanced.textMargins": "Marges de cellule", "PE.Views.TableSettingsAdvanced.textPlacement": "Emplacement", "PE.Views.TableSettingsAdvanced.textPosition": "Position", "PE.Views.TableSettingsAdvanced.textRight": "À droite", "PE.Views.TableSettingsAdvanced.textSize": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTitle": "Tableau - Paramètres avancés", "PE.Views.TableSettingsAdvanced.textTop": "En haut", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Coin supérieur gauche", "PE.Views.TableSettingsAdvanced.textVertical": "Verticalement", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Marges", "PE.Views.TextArtSettings.strBackground": "Couleur d'arrière-plan", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "Remplissage", "PE.Views.TextArtSettings.strForeground": "Couleur de premier plan", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strSize": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strStroke": "Ligne", "PE.Views.TextArtSettings.strTransparency": "Opacité", "PE.Views.TextArtSettings.strType": "Type", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "PE.Views.TextArtSettings.textColor": "Couleur de remplissage", "PE.Views.TextArtSettings.textDirection": "Direction", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON> <PERSON> mod<PERSON>", "PE.Views.TextArtSettings.textFromFile": "<PERSON>'un fichier", "PE.Views.TextArtSettings.textFromUrl": "D'une URL", "PE.Views.TextArtSettings.textGradient": "Gradient", "PE.Views.TextArtSettings.textGradientFill": "Remplissage en dégradé", "PE.Views.TextArtSettings.textImageTexture": "Image ou texture", "PE.Views.TextArtSettings.textLinear": "Linéaire", "PE.Views.TextArtSettings.textNoFill": "Pas de remplissage", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPosition": "Position", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "Select", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "Style", "PE.Views.TextArtSettings.textTemplate": "Template", "PE.Views.TextArtSettings.textTexture": "From Texture", "PE.Views.TextArtSettings.textTile": "Tile", "PE.Views.TextArtSettings.textTransform": "Transformer", "PE.Views.TextArtSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "PE.Views.TextArtSettings.txtBrownPaper": "Papier brun", "PE.Views.TextArtSettings.txtCanvas": "Toile", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "Tissu foncé", "PE.Views.TextArtSettings.txtGrain": "Grain", "PE.Views.TextArtSettings.txtGranite": "Granit", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON>r gris", "PE.Views.TextArtSettings.txtKnit": "Tricot", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> de ligne", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "<PERSON>", "PE.Views.Toolbar.capAddSlide": "Ajouter une diapositive", "PE.Views.Toolbar.capBtnAddComment": "Ajouter un commentaire", "PE.Views.Toolbar.capBtnComment": "Commentaire", "PE.Views.Toolbar.capBtnDateTime": "Date & heure", "PE.Views.Toolbar.capBtnInsHeader": "Pied de page", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Symbole", "PE.Views.Toolbar.capBtnSlideNum": "Numéro de diapositive", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Graphique", "PE.Views.Toolbar.capInsertEquation": "Équation", "PE.Views.Toolbar.capInsertHyperlink": "Lien hypertexte", "PE.Views.Toolbar.capInsertImage": "Image", "PE.Views.Toolbar.capInsertShape": "Forme", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Zone de texte", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "vidéo", "PE.Views.Toolbar.capTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capTabHome": "Accueil", "PE.Views.Toolbar.capTabInsert": "Insertion", "PE.Views.Toolbar.mniCapitalizeWords": "Mettre en majuscule chaque mot", "PE.Views.Toolbar.mniCustomTable": "Inserer un tableau personnalisé", "PE.Views.Toolbar.mniImageFromFile": "Image à partir d'un fichier", "PE.Views.Toolbar.mniImageFromStorage": "Image de stockage", "PE.Views.Toolbar.mniImageFromUrl": "Image à partir d'une URL", "PE.Views.Toolbar.mniInsertSSE": "Insérer la feuille de calcul", "PE.Views.Toolbar.mniLowerCase": "minuscule", "PE.Views.Toolbar.mniSentenceCase": "Majuscule en début de phrase.", "PE.Views.Toolbar.mniSlideAdvanced": "Paramètres avancés", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON> <PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "Inverser la casse", "PE.Views.Toolbar.mniUpperCase": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.strMenuNoFill": "Aucun remplissage", "PE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON><PERSON> le texte en bas", "PE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON> le texte", "PE.Views.Toolbar.textAlignJust": "Justifier", "PE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> le texte à gauche", "PE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON> le texte au milieu", "PE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON> le texte à droite", "PE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON> le texte en haut", "PE.Views.Toolbar.textArrangeBack": "Amener en arrière plan", "PE.Views.Toolbar.textArrangeBackward": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textArrangeForward": "Avancer", "PE.Views.Toolbar.textArrangeFront": "Amener au premier plan", "PE.Views.Toolbar.textBold": "Gras", "PE.Views.Toolbar.textColumnsCustom": "Colonnes personnalisées", "PE.Views.Toolbar.textColumnsOne": "Une colonne", "PE.Views.Toolbar.textColumnsThree": "Trois colonnes", "PE.Views.Toolbar.textColumnsTwo": "Deux colonnes", "PE.Views.Toolbar.textItalic": "Italique", "PE.Views.Toolbar.textListSettings": "Paramètres de la liste", "PE.Views.Toolbar.textRecentlyUsed": "Récemment utilisé", "PE.Views.Toolbar.textShapeAlignBottom": "Aligner en bas", "PE.Views.Toolbar.textShapeAlignCenter": "Aligner au centre", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON><PERSON> à gauche", "PE.Views.Toolbar.textShapeAlignMiddle": "Aligner au milieu", "PE.Views.Toolbar.textShapeAlignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "PE.Views.Toolbar.textShapeAlignTop": "Aligner en haut", "PE.Views.Toolbar.textShowBegin": "Afficher dès le début", "PE.Views.Toolbar.textShowCurrent": "<PERSON><PERSON><PERSON><PERSON> de la diapositive actuelle", "PE.Views.Toolbar.textShowPresenterView": "Afficher en mode présentateur", "PE.Views.Toolbar.textShowSettings": "Afficher les paramètres", "PE.Views.Toolbar.textStrikeout": "<PERSON><PERSON>", "PE.Views.Toolbar.textSubscript": "Indice", "PE.Views.Toolbar.textSuperscript": "Exposant", "PE.Views.Toolbar.textTabAnimation": "Animation", "PE.Views.Toolbar.textTabCollaboration": "Collaboration", "PE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabHome": "Accueil", "PE.Views.Toolbar.textTabInsert": "Insertion", "PE.Views.Toolbar.textTabProtect": "Protection", "PE.Views.Toolbar.textTabTransitions": "Transitions", "PE.Views.Toolbar.textTabView": "Affichage", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipAddSlide": "Ajouter diapositive", "PE.Views.Toolbar.tipBack": "En arrière", "PE.Views.Toolbar.tipChangeCase": "Modifier la casse", "PE.Views.Toolbar.tipChangeChart": "Modifier le type de graphique", "PE.Views.Toolbar.tipChangeSlide": "Modifier la disposition de diapositive", "PE.Views.Toolbar.tipClearStyle": "Effacer le style", "PE.Views.Toolbar.tipColorSchemas": "Modifier le jeu de couleurs", "PE.Views.Toolbar.tipColumns": "Insérer des colonnes ", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "Co<PERSON><PERSON> le style", "PE.Views.Toolbar.tipCut": "Couper", "PE.Views.Toolbar.tipDateTime": "Inserer la date et l'heure actuelle", "PE.Views.Toolbar.tipDecFont": "Diminuer la taille de police", "PE.Views.Toolbar.tipDecPrLeft": "Diminuer le retrait", "PE.Views.Toolbar.tipEditHeader": "Modifier le pied de page", "PE.Views.Toolbar.tipFontColor": "Couleur de la police", "PE.Views.Toolbar.tipFontName": "Police", "PE.Views.Toolbar.tipFontSize": "Taille de la police", "PE.Views.Toolbar.tipHAligh": "Alignement horizontal", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON><PERSON> de surlignage", "PE.Views.Toolbar.tipIncFont": "Augmenter la taille de police", "PE.Views.Toolbar.tipIncPrLeft": "Augmenter le retrait", "PE.Views.Toolbar.tipInsertAudio": "Insérer audio", "PE.Views.Toolbar.tipInsertChart": "Insérer un graphique", "PE.Views.Toolbar.tipInsertEquation": "Insérer une équation", "PE.Views.Toolbar.tipInsertHorizontalText": "Insérer une zone de texte horizontale", "PE.Views.Toolbar.tipInsertHyperlink": "Ajouter un lien hypertexte", "PE.Views.Toolbar.tipInsertImage": "Insérer une image", "PE.Views.Toolbar.tipInsertShape": "Insérer une forme automatique", "PE.Views.Toolbar.tipInsertSmartArt": "Insérer un graphique SmartArt", "PE.Views.Toolbar.tipInsertSymbol": "Insérer un symbole", "PE.Views.Toolbar.tipInsertTable": "Insérer un tableau", "PE.Views.Toolbar.tipInsertText": "Insérez zone de texte", "PE.Views.Toolbar.tipInsertTextArt": "Insérer Text Art", "PE.Views.Toolbar.tipInsertVerticalText": "Insérer une zone de texte verticale", "PE.Views.Toolbar.tipInsertVideo": "Insérer vidéo", "PE.Views.Toolbar.tipLineSpace": "Interligne", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersArrow": "<PERSON><PERSON><PERSON> fl<PERSON>", "PE.Views.Toolbar.tipMarkersCheckmark": "<PERSON><PERSON>s coches", "PE.Views.Toolbar.tipMarkersDash": "<PERSON>ire<PERSON>", "PE.Views.Toolbar.tipMarkersFRhombus": "<PERSON><PERSON><PERSON> remplis", "PE.Views.Toolbar.tipMarkersFRound": "<PERSON><PERSON>s arrondies remplies", "PE.Views.Toolbar.tipMarkersFSquare": "<PERSON><PERSON>s car<PERSON> remplies", "PE.Views.Toolbar.tipMarkersHRound": "Puces rondes vides", "PE.Views.Toolbar.tipMarkersStar": "Puces en étoile", "PE.Views.Toolbar.tipNone": "Aucun", "PE.Views.Toolbar.tipNumbers": "Numérotation", "PE.Views.Toolbar.tipPaste": "<PERSON><PERSON>", "PE.Views.Toolbar.tipPreview": "Démarrer le diaporama", "PE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPrintQuick": "Impression rapide", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "Enregistrer", "PE.Views.Toolbar.tipSaveCoauth": "Enregistrez vos modifications pour que les autres utilisateurs puissent les voir.", "PE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON>r une forme", "PE.Views.Toolbar.tipShapeArrange": "Organiser une forme", "PE.Views.Toolbar.tipSlideNum": "Inserer le numéro de diapositive", "PE.Views.Toolbar.tipSlideSize": "Sélectionner la taille de la diapositive", "PE.Views.Toolbar.tipSlideTheme": "Thème de diapositive", "PE.Views.Toolbar.tipUndo": "Annuler", "PE.Views.Toolbar.tipVAligh": "Alignement vertical", "PE.Views.Toolbar.tipViewSettings": "Paramètres d'affichage", "PE.Views.Toolbar.txtDistribHor": "Distribuer horizontalement", "PE.Views.Toolbar.txtDistribVert": "Distribuer verticalement", "PE.Views.Toolbar.txtDuplicateSlide": "Dupliquer diapositive", "PE.Views.Toolbar.txtGroup": "Grouper", "PE.Views.Toolbar.txtObjectsAlign": "Aligner les objets sélectionnés", "PE.Views.Toolbar.txtScheme1": "Office", "PE.Views.Toolbar.txtScheme10": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme11": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "Opulent", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Origine", "PE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme17": "Solstice", "PE.Views.Toolbar.txtScheme18": "Technique", "PE.Views.Toolbar.txtScheme19": "Trek", "PE.Views.Toolbar.txtScheme2": "Niveaux de gris", "PE.Views.Toolbar.txtScheme20": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme21": "Verve", "PE.Views.Toolbar.txtScheme22": "New Office", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Proportions", "PE.Views.Toolbar.txtScheme5": "Civil", "PE.Views.Toolbar.txtScheme6": "Rotonde", "PE.Views.Toolbar.txtScheme7": "Capitaux", "PE.Views.Toolbar.txtScheme8": "Flux", "PE.Views.Toolbar.txtScheme9": "Fonderie", "PE.Views.Toolbar.txtSlideAlign": "<PERSON><PERSON><PERSON> sur la diapositive", "PE.Views.Toolbar.txtUngroup": "Dissocier", "PE.Views.Transitions.strDelay": "Retard", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "<PERSON><PERSON><PERSON><PERSON> en cliquant", "PE.Views.Transitions.textBlack": "A travers le noir", "PE.Views.Transitions.textBottom": "En bas", "PE.Views.Transitions.textBottomLeft": "En bas à gauche", "PE.Views.Transitions.textBottomRight": "En bas à droite", "PE.Views.Transitions.textClock": "Horlog<PERSON>", "PE.Views.Transitions.textClockwise": "Dans le sens des aiguilles d'une montre", "PE.Views.Transitions.textCounterclockwise": "Dans le sens inverse des aiguilles d'une montre", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "Fond<PERSON>", "PE.Views.Transitions.textHorizontalIn": "Horizontal intérieur", "PE.Views.Transitions.textHorizontalOut": "Horizontal vers l'extérieur", "PE.Views.Transitions.textLeft": "A gauche", "PE.Views.Transitions.textNone": "Aucune", "PE.Views.Transitions.textPush": "Expulsion", "PE.Views.Transitions.textRight": "À droite", "PE.Views.Transitions.textSmoothly": "Transition douce", "PE.Views.Transitions.textSplit": "Diviser", "PE.Views.Transitions.textTop": "En haut", "PE.Views.Transitions.textTopLeft": "Haut à gauche", "PE.Views.Transitions.textTopRight": "Haut à droite", "PE.Views.Transitions.textUnCover": "Découvrir", "PE.Views.Transitions.textVerticalIn": "Vertical vers l'intérieur", "PE.Views.Transitions.textVerticalOut": "Vertical vers l'extérieur ", "PE.Views.Transitions.textWedge": "Coin", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "Zoom", "PE.Views.Transitions.textZoomIn": "Zoom avant", "PE.Views.Transitions.textZoomOut": "Zoom arri<PERSON>", "PE.Views.Transitions.textZoomRotate": "Zoom et rotation", "PE.Views.Transitions.txtApplyToAll": "Appliquer à toutes les diapositives", "PE.Views.Transitions.txtParameters": "Paramètres", "PE.Views.Transitions.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textAddHGuides": "Ajouter un repère horizontal", "PE.Views.ViewTab.textAddVGuides": "Ajouter un repère vertical", "PE.Views.ViewTab.textAlwaysShowToolbar": "Toujours afficher la barre d'outils", "PE.Views.ViewTab.textClearGuides": "Effacer les guides", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textFitToSlide": "Ajuster à la diapositive", "PE.Views.ViewTab.textFitToWidth": "Ajuster à la largeur", "PE.Views.ViewTab.textGridlines": "Quadrillage", "PE.Views.ViewTab.textGuides": "Repères", "PE.Views.ViewTab.textInterfaceTheme": "Thème d'interface", "PE.Views.ViewTab.textLeftMenu": "Panneau gauche", "PE.Views.ViewTab.textNotes": "Notes", "PE.Views.ViewTab.textRightMenu": "Panneau droit", "PE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textShowGridlines": "<PERSON><PERSON><PERSON><PERSON> le quadrillage", "PE.Views.ViewTab.textShowGuides": "Afficher les guides", "PE.Views.ViewTab.textSmartGuides": "Repères actifs", "PE.Views.ViewTab.textSnapObjects": "Fixer l'objet à la grille", "PE.Views.ViewTab.textStatusBar": "Barre d'état", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "Ajuster à la diapositive", "PE.Views.ViewTab.tipFitToWidth": "Ajuster à la largeur", "PE.Views.ViewTab.tipGridlines": "<PERSON><PERSON><PERSON><PERSON> le quadrillage", "PE.Views.ViewTab.tipGuides": "Afficher les guides", "PE.Views.ViewTab.tipInterfaceTheme": "Thème d'interface"}