﻿<!DOCTYPE html>
<html>
<head>
    <title>TREND-Funktion</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>TREND-Funktion</h1>
        <p>Die Funktion <b>TREND</b> ist eine der statistischen Funktionen. Sie wird verwendet, um eine lineare Trendlinie zu berechnen und Werte unter der Methode der kleinsten Quadrate zurückzugeben.</p>
        <p>Die Formelsyntax der Funktion <b>TREND</b> ist:</p>
        <p style="text-indent: 150px;"><b><em>TREND(bekannte_y-Werte, [bekannte_x-Werte], [neue_x-Werte], [Konstante])</em></b></p>
        <p>dabei gilt</p>
        <p><b><em>bekannte_y-Werte</em></b> ist die Gruppe von y-Werten, die Sie bereits in der Gleichung <em>y = mx + b</em> kennen.</p>
        <p><b><em>bekannte_x-Werte</em></b> ist die optionale Gruppe von x-Werten, die Sie möglicherweise in der Gleichung <em>y = mx + b</em> kennen.</p>
        <p><b><em>neue_x-Werte</em></b> ist die optionale Gruppe von x-Werten, für die die y-Werte liefern sollen.</p>
        <p><b><em>Konstante</em></b> ist ein optionales Argument. Es ist ein WAHR oder FALSCH Wert, bei dem WAHR oder das Fehlen des Arguments die normale Berechnung von <em>b</em> gibt und FALSCH <em>b</em> in der Gleichung <em>y = mx + b</em> auf 0 setzt und die m-Werte in der Gleichung <em>y = mx</em> entsprechen.</p>

        <p>Anwendung der Funktion <b>TREND</b>:</p>
        <ol>
            <li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
            <li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
            <li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
            <li>Klicken Sie auf die Funktion <b>TREND</b>.</li>
            <li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
            <li>Drücken Sie die <b>Eingabetaste</b>.</li>
        </ol>
        <p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
        <p style="text-indent: 150px;"><img alt="TREND-Funktion" src="../images/trend.png" /></p>
    </div>
</body>
</html>