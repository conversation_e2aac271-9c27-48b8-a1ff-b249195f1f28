﻿<!DOCTYPE html>
<html>
	<head>
		<title>PDURATION-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>PDURATION-Funktion</h1>
            <p>Die Funktion <b>PDURATION</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie gibt die Anzahl von Perioden zurück, die erforderlich sind, bis eine Investition einen angegebenen Wert erreicht hat</p>
            <p>Die Formelsyntax der Funktion <b>PDURATION</b> ist:</p>
            <p style="text-indent: 150px;"><b><em>PDURATION(Zins;Bw;Zw)</em></b></p>
            <p><em>Dabei ist:</em></p>
            <p style="text-indent: 50px;"><b><em>Zins</em></b> ist der Zinssatz pro Periode.</p>
            <p style="text-indent: 50px;"><b><em>BW</em></b> ist der aktuelle Wert der Investition.</p>
            <p style="text-indent: 50px;"><b><em>Zw</em></b> ist der angestrebte Zukunftswert der Investition.</p>
            
            <p class="note"><b>Hinweis:</b> Alle Argumente müssen in Form von positiven Zahlen dargestellt werden.</p>
            <p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p>Anwendung der Funktion <b>PDURATION</b>:</p>
            <ol>
                <li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
                <li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
                <li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
                <li>Klicken Sie auf die Funktion <b>PDURATION</b>.</li>
                <li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
                <li>Drücken Sie die <b>Eingabetaste</b>.</li>
            </ol>
            <p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
            <p style="text-indent: 150px;"><img alt="PDURATION-Funktion" src="../images/pduration.png" /></p>
        </div>
	</body>
</html>