﻿<!DOCTYPE html>
<html>
	<head>
		<title>Collaborative Presentation Editing</title>
		<meta charset="utf-8" />
		<meta name="description" content="Tips on collaborative editing" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Collaborative Presentation Editing</h1>
			<p><b>Presentation Editor</b> offers you the possibility to work at a presentation collaboratively with other users. This feature includes:</p>
			<ul>
				<li class="onlineDocumentFeatures">simultaneous multi-user access to the edited presentation</li>
				<li class="onlineDocumentFeatures">visual indication of objects that are being edited by other users</li>
				<li class="onlineDocumentFeatures">real-time changes display or synchronization of changes with one button click</li>
				<li class="onlineDocumentFeatures">chat to share ideas concerning particular presentation parts</li>
				<li>comments containing the description of a task or problem that should be solved (it's also possible to work with comments in the offline mode, without connecting to the <em>online version</em>)</li>
			</ul>
            <div class="desktopDocumentFeatures">
                <h3>Connecting to the online version</h3>
                <p>In the <em>desktop editor</em>, open the <b>Connect to cloud</b> option of the left-side menu in the main program window. Connect to your cloud office specifying your account login and password. <!--В окне ввода логина и пароля также доступна ссылка для регистрации в Облаке.--></p>
            </div>
            <div class="onlineDocumentFeatures">
                <h3>Co-editing</h3>
                <p><b>Presentation Editor</b> allows to select one of the two available co-editing modes:</p>
                <ul>
                    <li><b>Fast</b> is used by default and shows the changes made by other users in real time.</li>
                    <li><b>Strict</b> is selected to hide other user changes until you click the <b>Save</b> <div class = "icon icon-saveupdate"></div> icon to save your own changes and accept the changes made by others.</li>
                </ul>
                <p>The mode can be selected in the <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Advanced Settings</a>. It's also possible to choose the necessary mode using the <span class="icon icon-coeditingmode"></span> <b>Co-editing Mode</b> icon at the <b>Collaboration</b> tab of the top toolbar:</p>
                <p><img alt="Co-editing Mode menu" src="../../../../../../common/main/resources/help/it/images/coeditingmodemenu.png" /></p>
                <p class="note">
                    <b>Note</b>: when you co-edit a presentation in the <b>Fast</b> mode, the possibility to <b>Redo</b> the last undone operation is not available.
                </p>
                <p>When a presentation is being edited by several users simultaneously in the <b>Strict</b> mode, the edited objects (autoshapes, text objects, tables, images, charts) are marked with dashed lines of different colors. The object that you are editing is surrounded by the green dashed line. Red dashed lines indicate that objects are being edited by other users. By hovering the mouse cursor over one of the edited passages, the name of the user who is editing it at the moment is displayed. The <b>Fast</b> mode will show the actions and the names of the co-editors once they are editing the text.</p>
                <p>The number of users who are working at the current presentation is specified on the right side of the editor header - <span class="icon icon-usersnumber"></span>. If you want to see who exactly are editing the file now, you can click this icon or open the <b>Chat</b> panel with the full list of the users.</p>
                <p>When no users are viewing or editing the file, the icon in the editor header will look like <span class="icon icon-access_rights"></span> allowing you to manage the users who have access to the file right from the document: invite new users giving them permissions to <em>edit</em>, <em>read</em> or <em>comment</em> the presentation, or <em>deny</em> some users access rights to the file. Click this icon to manage the access to the file; this can be done both when there are no other users who view or co-edit the document at the moment and when there are other users and the icon looks like <span class="icon icon-usersnumber"></span>. It's also possible to set access rights using the <span class="icon icon-sharingicon"></span> <b>Sharing</b> icon at the <b>Collaboration</b> tab of the top toolbar.</p>
                <p>As soon as one of the users saves his/her changes by clicking the <span class="icon icon-savewhilecoediting"></span> icon, the others will see a note within the status bar stating that they have updates. To save the changes you made, so that other users can view them, and get the updates saved by your co-editors, click the <span class="icon icon-saveupdate"></span> icon in the left upper corner of the top toolbar. The updates will be highlighted for you to check what exactly has been changed.</p>
                <h3>Chat</h3>
                <p>You can use this tool to coordinate the co-editing process on-the-fly, for example, to arrange with your collaborators about who is doing what, which paragraph you are going to edit now etc.</p>
                <p>The chat messages are stored during one session only. To discuss the document content it is better to use comments which are stored until you decide to delete them.</p>
                <p>To access the chat and leave a message for other users,</p>
                <ol>
                    <li>
                        click the <div class = "icon icon-chaticon"></div> icon at the left sidebar, or <br />
                        switch to the <b>Collaboration</b> tab of the top toolbar and click the <div class = "icon icon-chat_toptoolbar"></div> <b>Chat</b> button,
                    </li>
                    <li>enter your text into the corresponding field below,</li>
                    <li>press the <b>Send</b> button.</li>
                </ol>
                <p>All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - <span class="icon icon-chaticon_new"></span>.</p>
                <p>To close the panel with chat messages, click the <span class="icon icon-chaticon"></span> icon at the left sidebar or the <span class="icon icon-chat_toptoolbar"></span> <b>Chat</b> button at the top toolbar once again.</p>
            </div>
            <h3>Comments</h3>
            <p>It's possible to work with comments in the offline mode, without connecting to the <em>online version</em>.</p>
                <p>To leave a comment to a certain object (text box, shape etc.):</p>
                <ol>
                    <li>select an object where you think there is an error or problem,</li>
                    <li>switch to the <b>Insert</b> or <b>Collaboration</b> tab of the top toolbar and click the <div class = "icon icon-comment_toptoolbar"></div> <b>Comment</b> button, or<br />
                        right-click the selected object and select the <b>Add Сomment</b> option from the menu,<br />
                    </li>
                    <li>enter the needed text,</li>
                    <li>click the <b>Add Comment/Add</b> button.</li>
                </ol>
                <p>The object you commented will be marked with the <span class="icon icon-added_comment_icon"></span> icon. To view the comment, just click on this icon.</p>
                <p>To add a comment to a certain slide, select the slide and use the <span class="icon icon-comment_toptoolbar"></span> <b>Comment</b> button at the <b>Insert</b> or <b>Collaboration</b> tab of the top toolbar. The added comment will be displayed in the upper left corner of the slide.</p>
                <p>To create a presentation-level comment which is not related to a certain object or slide, click the <span class="icon icon-commentsicon"></span> icon at the left sidebar to open the <b>Comments</b> panel and use the <b>Add Comment to Document</b> link. The presentation-level comments can be viewed at the <b>Comments</b> panel. Comments related to objects and slides are also available here.</p>
                <p>Any other user can answer to the added comment asking questions or reporting on the work he/she has done. For this purpose, click the <b>Add Reply</b> link situated under the comment, type in your reply text in the entry field and press the <b>Reply</b> button.</p>
                <p>If you are using the <b>Strict</b> co-editing mode, new comments added by other users will become visible only after you click the <span class="icon icon-saveupdate"></span> icon in the left upper corner of the top toolbar.</p>
                <p>You can manage the added comments using the icons in the comment balloon or at the <b>Comments</b> panel on the left:</p>
                <ul>
                    <li>edit the currently selected by clicking the <div class = "icon icon-editcommenticon"></div> icon,</li>
                    <li>delete the currently selected by clicking the <div class = "icon icon-deletecommenticon"></div> icon,</li>
                    <li>close the currently selected discussion by clicking the <div class = "icon icon-resolveicon"></div> icon if the task or problem you stated in your comment was solved, after that the discussion you opened with your comment gets the resolved status. To open it again, click the <div class = "icon icon-resolvedicon"></div> icon.</li>
                </ul>
                <h4>Adding mentions</h4>
            <p>When entering comments, you can use the <b>mentions</b> feature that allows to attract somebody's attention to the comment and send a notification to the mentioned user via email and <b>Talk</b>.</p>
            <p>To add a mention enter the "+" or "@" sign anywhere in the comment text - a list of the portal users will open. To simplify the search process, you can start typing a name in the comment field - the user list will change as you type. Select the necessary person from the list. If the file has not yet been shared with the mentioned user, the <b>Sharing Settings</b> window will open. <b>Read only</b> access type is selected by default. Change it if necessary and click <b>OK</b>.</p>
            <p>The mentioned user will receive an email notification that he/she has been mentioned in a comment. If the file has been shared, the user will also receive a corresponding notification.</p>    
            <p>To remove comments,</p>
            <ol>
                <li>click the <div class = "icon icon-removecomment_toptoolbar"></div> <b>Remove</b> button at the <b>Collaboration</b> tab of the top toolbar,</li>
                <li>
                    select the necessary option from the menu:
                    <ul>
                        <li><b>Remove Current Comments</b> - to remove the currently selected comment. If some replies have beed added to the comment, all its replies will be removed as well.</li>
                        <li><b>Remove My Comments</b> - to remove comments you added without removing comments added by other users. If some replies have beed added to your comment, all its replies will be removed as well.</li>
                        <li><b>Remove All Comments</b> - to remove all the comments in the presentation that you and other users added.</li>
                    </ul>
                </li>
            </ol>
                <p>To close the panel with comments, click the <span class="icon icon-commentsicon"></span> icon at the left sidebar once again.</p>
            </div>
	</body>
</html>