﻿<!DOCTYPE html>
<html>
	<head>
		<title>VARIATIONEN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>VARIATIONEN-Funktion</h1>
			<p>Die Funktion <b>VARIATIONEN</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt Werte zurück, die sich aus einem exponentiellen Trend ergeben</p>
			<p>Die Formelsyntax der Funktion <b>VARIATIONEN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>VARIATIONEN(Zahl, gewählte Zahl)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
				<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist eine Anzahl von Werten.</p> 
				<p style="text-indent: 50px;"><b><em>gewählte Zahl</em></b> ist eine Anzahl von Werten in einer Trendlinie.</p> 
				<p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>VARIATIONEN</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>VARIATIONEN</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="VARIATIONEN-Funktion" src="../images/permut.png" /></p>
		</div>
	</body>
</html>