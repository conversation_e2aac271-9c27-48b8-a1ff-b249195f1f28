﻿<!DOCTYPE html>
<html>
<head>
    <title>Zufallsmatrix-Funktion</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>ZUFALLSMATRIX-Funktion</h1>
        <p>Die Funktion <b>ZUFALLSMATRIX</b> ist eine der mathematischen und trigonometrischen Funktionen. Sie wird verwendet, um ein Array von Zufallszahlen zurückzugeben. Geben Sie die Anzahl der auszufüllenden Zeilen und Spalten, legen Sie die Mindest- und Höchstwerte fest und geben Sie an, ob ganze Zahlen oder Dezimalwerte zurückgegeben werden sollen.</p>
        <p>Die Formelsyntax der Funktion <b>ZUFALLSMATRIX</b> ist:</p>
        <p style="text-indent: 150px;"><b><em>ZUFALLSMATRIX([Zeilen],[Spalten],[min], [max], [ganze_Zahl])</em></b></p>
        <p>dabei gilt</p>
        <p><b><em>Zeilen</em></b> ist ein optionales Argument, das die Anzahl der Zeilen konfiguriert, die zurückgegeben werden sollen.</p>
        <p><b><em>Spalten</em></b> ist ein optionales Argument, das die Anzahl der Spalten konfiguriert, die zurückgegeben werden sollen.</p>
        <p><b><em>min</em></b> ist ein optionales Argument, das den Mindestwert konfiguriert. Wenn das Argument nicht angegeben wird, wird die Funktion standardmäßig auf 0 und 1 gesetzt.</p>
        <p><b><em>max</em></b> ist ein optionales Argument, das den Höchstwert konfiguriert. Wenn das Argument nicht angegeben wird, wird die Funktion standardmäßig auf 0 und 1 gesetzt.</p>
        <p><b><em>ganze_Zahl</em></b> ist ein optionales WAHR oder FALSCH Argument. Bei WAHR gibt die Funktion eine ganze Zahl zurück, bei FALSCH gibt sie eine Dezimalzahl zurück.</p>

        <p>Anwendung der Funktion <b>ZUFALLSMATRIX</b>:</p>
        <ol>
            <li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
            <li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
            <li>Wählen Sie die Gruppe <b>Mathematik und Trigonometrie</b> Funktionen aus der Liste aus.</li>
            <li>Klicken Sie auf die Funktion <b>ZUFALLSMATRIX</b>.</li>
            <li>Geben Sie die Argumente ein und trennen Sie diese durch Kommas.</li>
            <li>Drücken Sie die <b>Eingabetaste</b>.</li>
        </ol>
        <p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
        <p style="text-indent: 150px;"><img alt="ZUFALLSMATRIX-Funktion" src="../images/randarray.png"/></p>
    </div>
</body>
</html>