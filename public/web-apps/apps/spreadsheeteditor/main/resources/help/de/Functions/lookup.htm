﻿<!DOCTYPE html>
<html>
	<head>
		<title>VERWEIS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="Erf<PERSON><PERSON> Si<PERSON>, wie Sie die VERWEIS-Formel in Excel-Tabellen und kompatiblen Dateien in ONLYOFFICE verwenden." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>VERWEIS-Funktion</h1>
			<p>Die Funktion <b>VERWEIS</b> gehört zur Gruppe der Nachschlage- und Verweisfunktionen. Sie gibt einen Wert aus einem ausgewählten Bereich zurück (Zeile oder Spalte mit Daten in aufsteigender Reihenfolge).</p>
			<p>Die Formelsyntax der Funktion <b>VERWEIS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>VERWEIS(Suchkriterium, Suchvektor, [Ergebnisvektor])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Suchkriterium</em></b> ist der Wert nach dem gesucht wird.</p> 
				<p style="text-indent: 50px;"><b><em>Suchvektor</em></b> ist eine einzelne Zeile oder Spalte mit Daten in aufsteigender Reihenfolge.</p> 
				<p style="text-indent: 50px;"><b><em>Ergebnisvektor</em></b> ist eine einzelne Zeile oder Spalte mit der gleichen Größe wie <b><em>Suchvektor</em></b>.</p> 
			<p>Die Funktion sucht im <b><em>Suchvektor</em></b> nach dem <b><em>Suchkriterium</em></b> und gibt den Wert von der gleichen Postion im <b><em>Ergebnisvektor</em></b> zurück.</p>
			<p class="note">Wenn das <b>Suchkriterium</b> kleiner ist als alle Werte im <b>Ergebnisvektor</b>, gibt die Funktion den Fehlerwert <b>#NV</b> zurück. Kann die Funktion keinen Wert finden, der mit dem jeweiligen Wert von <b>Suchkriterium</b> übereinstimmt, verwendet die Funktion den größten Wert in <b>Suchvektor</b>, der kleiner oder gleich dem Wert von Suchkriterium ist.</p>
			<h2>Wie funktioniert VERWEIS</h2>
			<p>Anwendung der Funktion <b>VERWEIS</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Nachschlage- und Verweisfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>VERWEIS</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<img class="gif" alt="lVERWEIS-Funktion gif" src="../images/lookup_function.gif" />
		</div>
	</body>
</html>