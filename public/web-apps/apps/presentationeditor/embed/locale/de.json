{"common.view.modals.txtCopy": "In die Zwischenablage kopieren", "common.view.modals.txtEmbed": "Einbetten", "common.view.modals.txtHeight": "<PERSON><PERSON><PERSON>", "common.view.modals.txtShare": "<PERSON> te<PERSON>n", "common.view.modals.txtWidth": "Breite", "common.view.SearchBar.textFind": "<PERSON><PERSON>", "PE.ApplicationController.convertationErrorText": "Konvertierung ist fehlgeschlagen.", "PE.ApplicationController.convertationTimeoutText": "Zeitüberschreitung bei der Konvertierung.", "PE.ApplicationController.criticalErrorTitle": "<PERSON><PERSON>", "PE.ApplicationController.downloadErrorText": "Herunterladen ist fehlgeschlagen.", "PE.ApplicationController.downloadTextText": "Präsentation wird heruntergeladen...", "PE.ApplicationController.errorAccessDeny": "<PERSON><PERSON>, eine Aktion durchzuführen, für die Si<PERSON> keine Rechte haben.<br><PERSON>te wenden Sie sich an Ihren Document Serveradministrator.", "PE.ApplicationController.errorDefaultMessage": "Fehlercode: %1", "PE.ApplicationController.errorFilePassProtect": "Das Dokument ist kennwortgeschützt und kann nicht geöffnet werden.", "PE.ApplicationController.errorFileSizeExceed": "Die Dateigröße überschreitet die für Ihren Server festgelegte Einschränkung.<br>Weitere Informationen können Sie von Ihrem Document Server-Administrator er<PERSON><PERSON>.", "PE.ApplicationController.errorForceSave": "<PERSON>im Speichern der Datei ist ein Fehler aufgetreten. Verwenden Sie die Option \"Herunterladen als\", um die Datei auf Ihrer Computerfestplatte zu speichern oder versuchen Sie es später erneut.", "PE.ApplicationController.errorInconsistentExt": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei stimmt nicht mit der Dateierweiterung überein.", "PE.ApplicationController.errorInconsistentExtDocx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Textdokumenten (z.B. docx), aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.ApplicationController.errorInconsistentExtPdf": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht einem der folgenden Formate: pdf/djvu/xps/oxps, aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.ApplicationController.errorInconsistentExtPptx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Präsentationen (z.B. pptx), aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.ApplicationController.errorInconsistentExtXlsx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Tabellenkalkulationen (z.B. xlsx), aber die Datei hat die inkonsistente Erweiterung: %1.", "PE.ApplicationController.errorLoadingFont": "Schriftarten nicht hochgeladen.<br><PERSON><PERSON> wenden <PERSON> sich an <PERSON><PERSON> von Ihrem Document Server.", "PE.ApplicationController.errorTokenExpire": "Sicherheitstoken des Dokuments ist abgelaufen.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "PE.ApplicationController.errorUpdateVersionOnDisconnect": "Die Internetverbindung wurde wiederhergestellt und die Dateiversion wurde geändert.<br><PERSON><PERSON> weiterarbeiten können, müssen Sie die Datei herunterladen oder den Inhalt kopieren, um sicherzustellen, dass nichts verloren geht, und diese Seite anschließend neu laden.", "PE.ApplicationController.errorUserDrop": "<PERSON><PERSON> auf diese Datei ist möglich.", "PE.ApplicationController.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.ApplicationController.openErrorText": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.", "PE.ApplicationController.scriptLoadError": "Die Verbindung ist zu langsam, einige der Komponenten konnten nicht geladen werden. Bitte laden Sie die Se<PERSON> erneut.", "PE.ApplicationController.textAnonymous": "Anonym", "PE.ApplicationController.textGuest": "Gas<PERSON>", "PE.ApplicationController.textLoadingDocument": "Präsentation wird geladen", "PE.ApplicationController.textOf": "von", "PE.ApplicationController.txtClose": "Schließen", "PE.ApplicationController.unknownErrorText": "Unbek<PERSON><PERSON> Fehler.", "PE.ApplicationController.unsupportedBrowserErrorText": "<PERSON><PERSON> wird nicht unterstützt.", "PE.ApplicationController.waitText": "Bitte warten...", "PE.ApplicationView.txtDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.ApplicationView.txtEmbed": "Einbetten", "PE.ApplicationView.txtFileLocation": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "PE.ApplicationView.txtFullScreen": "Vollbild-Modus", "PE.ApplicationView.txtPrint": "<PERSON><PERSON><PERSON>", "PE.ApplicationView.txtSearch": "<PERSON><PERSON>", "PE.ApplicationView.txtShare": "Freigeben"}