{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Chat.textEnterMessage": "在这里输入你的信息", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名", "Common.Controllers.ExternalDiagramEditor.textClose": "关闭", "Common.Controllers.ExternalDiagramEditor.warningText": "该对象被禁用，因为它被另一个用户编辑。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名用户", "Common.Controllers.ExternalOleEditor.textClose": "关闭", "Common.Controllers.ExternalOleEditor.warningText": "该对象被禁用，因为它在被另一个用户编辑。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.define.chartData.textArea": "面积图", "Common.define.chartData.textAreaStacked": "堆积面积图", "Common.define.chartData.textAreaStackedPer": "百分比堆积面积图", "Common.define.chartData.textBar": "条形图", "Common.define.chartData.textBarNormal": "簇状柱形图", "Common.define.chartData.textBarNormal3d": "三维簇状柱形图", "Common.define.chartData.textBarNormal3dPerspective": "三维柱形图", "Common.define.chartData.textBarStacked": "堆积柱形图", "Common.define.chartData.textBarStacked3d": "三维堆积柱形图", "Common.define.chartData.textBarStackedPer": "百分比堆积柱形图", "Common.define.chartData.textBarStackedPer3d": "三维百分比堆积柱形图", "Common.define.chartData.textCharts": "流程图", "Common.define.chartData.textColumn": "柱状图", "Common.define.chartData.textCombo": "组合图", "Common.define.chartData.textComboAreaBar": "堆积面积图 - 簇状柱形图", "Common.define.chartData.textComboBarLine": "簇状柱形图 - 折线图", "Common.define.chartData.textComboBarLineSecondary": "簇状柱形图 - 次坐标轴上的折线图", "Common.define.chartData.textComboCustom": "自定义组合", "Common.define.chartData.textDoughnut": "圆环图​​", "Common.define.chartData.textHBarNormal": "簇状条形图", "Common.define.chartData.textHBarNormal3d": "三维簇状条形图", "Common.define.chartData.textHBarStacked": "堆积条形图", "Common.define.chartData.textHBarStacked3d": "三维堆积条形图", "Common.define.chartData.textHBarStackedPer": "百分比堆积条形图", "Common.define.chartData.textHBarStackedPer3d": "三维百分比堆积条形图", "Common.define.chartData.textLine": "折线图", "Common.define.chartData.textLine3d": "三维折线图", "Common.define.chartData.textLineMarker": "带数据标记的折线图", "Common.define.chartData.textLineStacked": "堆积折线图", "Common.define.chartData.textLineStackedMarker": "带数据标记的堆积折线图", "Common.define.chartData.textLineStackedPer": "百分比堆积折线图", "Common.define.chartData.textLineStackedPerMarker": "带数据标记的百分比堆积折线图", "Common.define.chartData.textPie": "饼图", "Common.define.chartData.textPie3d": "三维饼图", "Common.define.chartData.textPoint": "散点图", "Common.define.chartData.textScatter": "散点图​​", "Common.define.chartData.textScatterLine": "带直线的散点图", "Common.define.chartData.textScatterLineMarker": "带直线和数据标记的散点图", "Common.define.chartData.textScatterSmooth": "带平滑线的散点图", "Common.define.chartData.textScatterSmoothMarker": "带平滑线和数据标记的散点图", "Common.define.chartData.textStock": "股价图", "Common.define.chartData.textSurface": "平面", "Common.define.effectData.textAcross": "横向", "Common.define.effectData.textAppear": "出现", "Common.define.effectData.textArcDown": "向下弧线", "Common.define.effectData.textArcLeft": "向左弧线", "Common.define.effectData.textArcRight": "向右弧线", "Common.define.effectData.textArcs": "弧形", "Common.define.effectData.textArcUp": "向上弧线", "Common.define.effectData.textBasic": "基本", "Common.define.effectData.textBasicSwivel": "基本旋转", "Common.define.effectData.textBasicZoom": "基本缩放", "Common.define.effectData.textBean": "豆荚", "Common.define.effectData.textBlinds": "百叶窗", "Common.define.effectData.textBlink": "闪烁", "Common.define.effectData.textBoldFlash": "加粗闪烁", "Common.define.effectData.textBoldReveal": "加粗展示", "Common.define.effectData.textBoomerang": "飞旋", "Common.define.effectData.textBounce": "弹跳", "Common.define.effectData.textBounceLeft": "向左弹跳", "Common.define.effectData.textBounceRight": "向右弹跳", "Common.define.effectData.textBox": "盒状", "Common.define.effectData.textBrushColor": "画笔颜色", "Common.define.effectData.textCenterRevolve": "中心旋转", "Common.define.effectData.textCheckerboard": "棋盘", "Common.define.effectData.textCircle": "圆形", "Common.define.effectData.textCollapse": "折叠", "Common.define.effectData.textColorPulse": "彩色脉冲", "Common.define.effectData.textComplementaryColor": "补色", "Common.define.effectData.textComplementaryColor2": "补色2", "Common.define.effectData.textCompress": "压缩", "Common.define.effectData.textContrast": "对比度", "Common.define.effectData.textContrastingColor": "对比色", "Common.define.effectData.textCredits": "字幕式", "Common.define.effectData.textCrescentMoon": "新月形", "Common.define.effectData.textCurveDown": "向下曲线", "Common.define.effectData.textCurvedSquare": "圆角正方形", "Common.define.effectData.textCurvedX": "弯曲的X", "Common.define.effectData.textCurvyLeft": "向左曲线", "Common.define.effectData.textCurvyRight": "向右曲线", "Common.define.effectData.textCurvyStar": "弯曲星形", "Common.define.effectData.textCustomPath": "自定义路径", "Common.define.effectData.textCuverUp": "向上曲线", "Common.define.effectData.textDarken": "加深", "Common.define.effectData.textDecayingWave": "衰减波", "Common.define.effectData.textDesaturate": "不饱和", "Common.define.effectData.textDiagonalDownRight": "对角线向右下", "Common.define.effectData.textDiagonalUpRight": "对角线向右上", "Common.define.effectData.textDiamond": "菱形", "Common.define.effectData.textDisappear": "消失", "Common.define.effectData.textDissolveIn": "向内溶解", "Common.define.effectData.textDissolveOut": "向外溶解", "Common.define.effectData.textDown": "向下", "Common.define.effectData.textDrop": "掉落", "Common.define.effectData.textEmphasis": "强调效果", "Common.define.effectData.textEntrance": "进入效果", "Common.define.effectData.textEqualTriangle": "等边三角形", "Common.define.effectData.textExciting": "华丽", "Common.define.effectData.textExit": "退出效果", "Common.define.effectData.textExpand": "展开", "Common.define.effectData.textFade": "淡化", "Common.define.effectData.textFigureFour": "双八串接", "Common.define.effectData.textFillColor": "填充颜色", "Common.define.effectData.textFlip": "翻转", "Common.define.effectData.textFloat": "浮动", "Common.define.effectData.textFloatDown": "下浮", "Common.define.effectData.textFloatIn": "浮入", "Common.define.effectData.textFloatOut": "浮出", "Common.define.effectData.textFloatUp": "上浮", "Common.define.effectData.textFlyIn": "飞入", "Common.define.effectData.textFlyOut": "飞出", "Common.define.effectData.textFontColor": "字体颜色", "Common.define.effectData.textFootball": "橄榄球形", "Common.define.effectData.textFromBottom": "自底部", "Common.define.effectData.textFromBottomLeft": "从左下部", "Common.define.effectData.textFromBottomRight": "从右下部", "Common.define.effectData.textFromLeft": "从左侧", "Common.define.effectData.textFromRight": "从右侧", "Common.define.effectData.textFromTop": "从顶部", "Common.define.effectData.textFromTopLeft": "从左上部", "Common.define.effectData.textFromTopRight": "从右上部", "Common.define.effectData.textFunnel": "漏斗", "Common.define.effectData.textGrowShrink": "放大/缩小", "Common.define.effectData.textGrowTurn": "翻转式由远及近", "Common.define.effectData.textGrowWithColor": "彩色延伸", "Common.define.effectData.textHeart": "心形", "Common.define.effectData.textHeartbeat": "心跳", "Common.define.effectData.textHexagon": "六边形", "Common.define.effectData.textHorizontal": "水平的", "Common.define.effectData.textHorizontalFigure": "水平数字8", "Common.define.effectData.textHorizontalIn": "上下向中央收缩", "Common.define.effectData.textHorizontalOut": "中央向上下展开", "Common.define.effectData.textIn": "在", "Common.define.effectData.textInFromScreenCenter": "从屏幕中心放大", "Common.define.effectData.textInSlightly": "轻微放大", "Common.define.effectData.textInToScreenBottom": "放大到屏幕底部", "Common.define.effectData.textInvertedSquare": "正方形结", "Common.define.effectData.textInvertedTriangle": "三角结", "Common.define.effectData.textLeft": "左侧", "Common.define.effectData.textLeftDown": "左下", "Common.define.effectData.textLeftUp": "左上", "Common.define.effectData.textLighten": "变淡", "Common.define.effectData.textLineColor": "线条颜色", "Common.define.effectData.textLines": "行", "Common.define.effectData.textLinesCurves": "直线曲线", "Common.define.effectData.textLoopDeLoop": "涟漪", "Common.define.effectData.textLoops": "循环", "Common.define.effectData.textModerate": "中等", "Common.define.effectData.textNeutron": "中子", "Common.define.effectData.textObjectCenter": "对象中心", "Common.define.effectData.textObjectColor": "对象颜色", "Common.define.effectData.textOctagon": "八边形", "Common.define.effectData.textOut": "向外", "Common.define.effectData.textOutFromScreenBottom": "从屏幕底部缩小", "Common.define.effectData.textOutSlightly": "轻微缩小", "Common.define.effectData.textOutToScreenCenter": "缩小到屏幕中心", "Common.define.effectData.textParallelogram": "平行四边形", "Common.define.effectData.textPath": "动作路径", "Common.define.effectData.textPathCurve": "曲线", "Common.define.effectData.textPathLine": "线条", "Common.define.effectData.textPathScribble": "自由曲线", "Common.define.effectData.textPeanut": "花生", "Common.define.effectData.textPeekIn": "切入", "Common.define.effectData.textPeekOut": "切出", "Common.define.effectData.textPentagon": "五边形", "Common.define.effectData.textPinwheel": "玩具风车", "Common.define.effectData.textPlus": "加号", "Common.define.effectData.textPointStar": "点星", "Common.define.effectData.textPointStar4": "四角形", "Common.define.effectData.textPointStar5": "五角星", "Common.define.effectData.textPointStar6": "六角星", "Common.define.effectData.textPointStar8": "八角星", "Common.define.effectData.textPulse": "脉冲", "Common.define.effectData.textRandomBars": "随机线条", "Common.define.effectData.textRight": "右侧", "Common.define.effectData.textRightDown": "右下", "Common.define.effectData.textRightTriangle": "直角三角形", "Common.define.effectData.textRightUp": "右上", "Common.define.effectData.textRiseUp": "升起", "Common.define.effectData.textSCurve1": "S形曲线1", "Common.define.effectData.textSCurve2": "S形曲线2", "Common.define.effectData.textShape": "形状", "Common.define.effectData.textShapes": "形状", "Common.define.effectData.textShimmer": "闪现", "Common.define.effectData.textShrinkTurn": "收缩并旋转", "Common.define.effectData.textSineWave": "正弦波", "Common.define.effectData.textSinkDown": "下沉", "Common.define.effectData.textSlideCenter": "幻灯片中心", "Common.define.effectData.textSpecial": "特殊", "Common.define.effectData.textSpin": "旋转", "Common.define.effectData.textSpinner": "回旋", "Common.define.effectData.textSpiralIn": "螺旋飞入", "Common.define.effectData.textSpiralLeft": "螺旋向左", "Common.define.effectData.textSpiralOut": "螺旋飞出", "Common.define.effectData.textSpiralRight": "螺旋向右", "Common.define.effectData.textSplit": "拆分", "Common.define.effectData.textSpoke1": "1轮辐图案", "Common.define.effectData.textSpoke2": "2轮辐图案", "Common.define.effectData.textSpoke3": "3轮辐图案", "Common.define.effectData.textSpoke4": "4轮辐图案", "Common.define.effectData.textSpoke8": "8轮辐图案", "Common.define.effectData.textSpring": "弹簧", "Common.define.effectData.textSquare": "正方形", "Common.define.effectData.textStairsDown": "向下阶梯", "Common.define.effectData.textStretch": "伸展", "Common.define.effectData.textStrips": "阶梯状", "Common.define.effectData.textSubtle": "细微", "Common.define.effectData.textSwivel": "旋转", "Common.define.effectData.textSwoosh": "飘扬形", "Common.define.effectData.textTeardrop": "泪珠形", "Common.define.effectData.textTeeter": "跷跷板", "Common.define.effectData.textToBottom": "到底部", "Common.define.effectData.textToBottomLeft": "到左下部", "Common.define.effectData.textToBottomRight": "到右下部", "Common.define.effectData.textToLeft": "到左侧", "Common.define.effectData.textToRight": "到右侧", "Common.define.effectData.textToTop": "到顶部", "Common.define.effectData.textToTopLeft": "到左上部", "Common.define.effectData.textToTopRight": "到右上部", "Common.define.effectData.textTransparency": "透明", "Common.define.effectData.textTrapezoid": "梯形", "Common.define.effectData.textTurnDown": "向下转", "Common.define.effectData.textTurnDownRight": "向右下转", "Common.define.effectData.textTurns": "转弯", "Common.define.effectData.textTurnUp": "向上转", "Common.define.effectData.textTurnUpRight": "向右上转", "Common.define.effectData.textUnderline": "下划线", "Common.define.effectData.textUp": "上", "Common.define.effectData.textVertical": "垂直", "Common.define.effectData.textVerticalFigure": "垂直数字8", "Common.define.effectData.textVerticalIn": "左右向中央收缩", "Common.define.effectData.textVerticalOut": "中央向左右展开", "Common.define.effectData.textWave": "波浪", "Common.define.effectData.textWedge": "楔形", "Common.define.effectData.textWheel": "轮子", "Common.define.effectData.textWhip": "挥鞭式", "Common.define.effectData.textWipe": "擦除", "Common.define.effectData.textZigzag": "弯弯曲曲", "Common.define.effectData.textZoom": "缩放", "Common.define.gridlineData.txtCm": "厘米", "Common.define.gridlineData.txtPt": "像素", "Common.define.smartArt.textAccentedPicture": "重音图片", "Common.define.smartArt.textAccentProcess": "重点流程", "Common.define.smartArt.textAlternatingFlow": "交替流", "Common.define.smartArt.textAlternatingHexagons": "交替六边形", "Common.define.smartArt.textAlternatingPictureBlocks": "交替图片块", "Common.define.smartArt.textAlternatingPictureCircles": "交替图片圆形", "Common.define.smartArt.textArchitectureLayout": "结构布局", "Common.define.smartArt.textArrowRibbon": "带形箭头", "Common.define.smartArt.textAscendingPictureAccentProcess": "升序图片重点流程", "Common.define.smartArt.textBalance": "平衡", "Common.define.smartArt.textBasicBendingProcess": "基本蛇形流程", "Common.define.smartArt.textBasicBlockList": "基本列表", "Common.define.smartArt.textBasicChevronProcess": "基本 V 形流程", "Common.define.smartArt.textBasicCycle": "基本循环", "Common.define.smartArt.textBasicMatrix": "基本矩阵", "Common.define.smartArt.textBasicPie": "基本饼图", "Common.define.smartArt.textBasicProcess": "基本流程", "Common.define.smartArt.textBasicPyramid": "基本棱锥图", "Common.define.smartArt.textBasicRadial": "基本射线图", "Common.define.smartArt.textBasicTarget": "基本目标图", "Common.define.smartArt.textBasicTimeline": "基本时间线", "Common.define.smartArt.textBasicVenn": "基本维恩图", "Common.define.smartArt.textBendingPictureAccentList": "蛇形图片重点列表", "Common.define.smartArt.textBendingPictureBlocks": "蛇形图片块", "Common.define.smartArt.textBendingPictureCaption": "蛇形图片题注", "Common.define.smartArt.textBendingPictureCaptionList": "蛇形图片题注列表", "Common.define.smartArt.textBendingPictureSemiTranparentText": "蛇形图片半透明文本", "Common.define.smartArt.textBlockCycle": "块循环", "Common.define.smartArt.textBubblePictureList": "气泡图片列表", "Common.define.smartArt.textCaptionedPictures": "题注图片", "Common.define.smartArt.textChevronAccentProcess": "V 形重点流程", "Common.define.smartArt.textChevronList": "V 型列表", "Common.define.smartArt.textCircleAccentTimeline": "圆形重点日程表", "Common.define.smartArt.textCircleArrowProcess": "圆箭头流程", "Common.define.smartArt.textCirclePictureHierarchy": "圆形图片层次结构", "Common.define.smartArt.textCircleProcess": "循环流程", "Common.define.smartArt.textCircleRelationship": "循环关系", "Common.define.smartArt.textCircularBendingProcess": "环状蛇形流程", "Common.define.smartArt.textCircularPictureCallout": "圆形图片标注", "Common.define.smartArt.textClosedChevronProcess": "闭合 V 形流程", "Common.define.smartArt.textContinuousArrowProcess": "连续箭头流程", "Common.define.smartArt.textContinuousBlockProcess": "连续块状流程", "Common.define.smartArt.textContinuousCycle": "连续循环", "Common.define.smartArt.textContinuousPictureList": "连续图片列表", "Common.define.smartArt.textConvergingArrows": "汇聚箭头", "Common.define.smartArt.textConvergingRadial": "聚合射线", "Common.define.smartArt.textConvergingText": "聚合文本", "Common.define.smartArt.textCounterbalanceArrows": "平衡箭头", "Common.define.smartArt.textCycle": "循环", "Common.define.smartArt.textCycleMatrix": "循环矩阵", "Common.define.smartArt.textDescendingBlockList": "降序块列表", "Common.define.smartArt.textDescendingProcess": "降序流程", "Common.define.smartArt.textDetailedProcess": "详细流程", "Common.define.smartArt.textDivergingArrows": "分叉箭头", "Common.define.smartArt.textDivergingRadial": "分离射线", "Common.define.smartArt.textEquation": "方程", "Common.define.smartArt.textFramedTextPicture": "带框架的文本图片", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "齿轮", "Common.define.smartArt.textGridMatrix": "网格矩阵", "Common.define.smartArt.textGroupedList": "分组列表", "Common.define.smartArt.textHalfCircleOrganizationChart": "半圆组织结构图", "Common.define.smartArt.textHexagonCluster": "六边形群集", "Common.define.smartArt.textHexagonRadial": "放射状六边形", "Common.define.smartArt.textHierarchy": "层次结构", "Common.define.smartArt.textHierarchyList": "层次结构列表", "Common.define.smartArt.textHorizontalBulletList": "水平项目符号列表", "Common.define.smartArt.textHorizontalHierarchy": "水平层次结构", "Common.define.smartArt.textHorizontalLabeledHierarchy": "水平标记的层次结构", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "水平多层层次结构", "Common.define.smartArt.textHorizontalOrganizationChart": "水平组织结构图", "Common.define.smartArt.textHorizontalPictureList": "水平图片列表", "Common.define.smartArt.textIncreasingArrowProcess": "递增箭头流程", "Common.define.smartArt.textIncreasingCircleProcess": "递增循环流程", "Common.define.smartArt.textInterconnectedBlockProcess": "互联块流程", "Common.define.smartArt.textInterconnectedRings": "互联环", "Common.define.smartArt.textInvertedPyramid": "倒棱锥图", "Common.define.smartArt.textLabeledHierarchy": "标记的层次结构", "Common.define.smartArt.textLinearVenn": "线性维恩图", "Common.define.smartArt.textLinedList": "线型列表", "Common.define.smartArt.textList": "列表", "Common.define.smartArt.textMatrix": "矩阵", "Common.define.smartArt.textMultidirectionalCycle": "多向循环", "Common.define.smartArt.textNameAndTitleOrganizationChart": "姓名和职务组织结构图", "Common.define.smartArt.textNestedTarget": "嵌套目标图", "Common.define.smartArt.textNondirectionalCycle": "不定向循环", "Common.define.smartArt.textOpposingArrows": "反向箭头", "Common.define.smartArt.textOpposingIdeas": "对立观点", "Common.define.smartArt.textOrganizationChart": "组织结构图", "Common.define.smartArt.textOther": "其他", "Common.define.smartArt.textPhasedProcess": "分阶段流程", "Common.define.smartArt.textPicture": "图片", "Common.define.smartArt.textPictureAccentBlocks": "图片重点块", "Common.define.smartArt.textPictureAccentList": "图片重点列表", "Common.define.smartArt.textPictureAccentProcess": "图片重点流程", "Common.define.smartArt.textPictureCaptionList": "图片题注列表", "Common.define.smartArt.textPictureFrame": "图片框", "Common.define.smartArt.textPictureGrid": "图片网格", "Common.define.smartArt.textPictureLineup": "图片排列", "Common.define.smartArt.textPictureOrganizationChart": "图片组织结构图", "Common.define.smartArt.textPictureStrips": "图片条纹", "Common.define.smartArt.textPieProcess": "饼图流程", "Common.define.smartArt.textPlusAndMinus": "加号和减号", "Common.define.smartArt.textProcess": "流程", "Common.define.smartArt.textProcessArrows": "流程箭头", "Common.define.smartArt.textProcessList": "流程列表", "Common.define.smartArt.textPyramid": "棱锥型", "Common.define.smartArt.textPyramidList": "棱锥型列表", "Common.define.smartArt.textRadialCluster": "射线群集", "Common.define.smartArt.textRadialCycle": "射线循环", "Common.define.smartArt.textRadialList": "射线列表", "Common.define.smartArt.textRadialPictureList": "放射状图片列表", "Common.define.smartArt.textRadialVenn": "射线维恩图", "Common.define.smartArt.textRandomToResultProcess": "随机至结果流程", "Common.define.smartArt.textRelationship": "关系", "Common.define.smartArt.textRepeatingBendingProcess": "重复蛇形流程", "Common.define.smartArt.textReverseList": "反转列表", "Common.define.smartArt.textSegmentedCycle": "分段循环", "Common.define.smartArt.textSegmentedProcess": "分段流程", "Common.define.smartArt.textSegmentedPyramid": "分段棱锥图", "Common.define.smartArt.textSnapshotPictureList": "快照图片列表", "Common.define.smartArt.textSpiralPicture": "螺旋图", "Common.define.smartArt.textSquareAccentList": "方形重点列表", "Common.define.smartArt.textStackedList": "堆叠列表", "Common.define.smartArt.textStackedVenn": "堆叠维恩图", "Common.define.smartArt.textStaggeredProcess": "交错流程", "Common.define.smartArt.textStepDownProcess": "步骤下移流程", "Common.define.smartArt.textStepUpProcess": "升级流程", "Common.define.smartArt.textSubStepProcess": "子步骤流程", "Common.define.smartArt.textTabbedArc": "拱状", "Common.define.smartArt.textTableHierarchy": "表层次结构", "Common.define.smartArt.textTableList": "表格列表", "Common.define.smartArt.textTabList": "选项卡列表", "Common.define.smartArt.textTargetList": "目标图列表", "Common.define.smartArt.textTextCycle": "文本循环", "Common.define.smartArt.textThemePictureAccent": "主题图片重点", "Common.define.smartArt.textThemePictureAlternatingAccent": "主题图片交替重点", "Common.define.smartArt.textThemePictureGrid": "主题图片网格", "Common.define.smartArt.textTitledMatrix": "带标题的矩阵", "Common.define.smartArt.textTitledPictureAccentList": "标题图片重点列表", "Common.define.smartArt.textTitledPictureBlocks": "标题图片块", "Common.define.smartArt.textTitlePictureLineup": "标题图片排列", "Common.define.smartArt.textTrapezoidList": "梯形列表", "Common.define.smartArt.textUpwardArrow": "向上箭头", "Common.define.smartArt.textVaryingWidthList": "不同宽度列表", "Common.define.smartArt.textVerticalAccentList": "垂直重点列表", "Common.define.smartArt.textVerticalArrowList": "垂直箭头列表", "Common.define.smartArt.textVerticalBendingProcess": "垂直蛇形流程", "Common.define.smartArt.textVerticalBlockList": "垂直块列表", "Common.define.smartArt.textVerticalBoxList": "垂直框列表", "Common.define.smartArt.textVerticalBracketList": "垂直括弧列表", "Common.define.smartArt.textVerticalBulletList": "垂直项目符号列表", "Common.define.smartArt.textVerticalChevronList": "垂直 V 形列表", "Common.define.smartArt.textVerticalCircleList": "垂直圆形列表", "Common.define.smartArt.textVerticalCurvedList": "垂直曲形列表", "Common.define.smartArt.textVerticalEquation": "垂直公式", "Common.define.smartArt.textVerticalPictureAccentList": "垂直图片重点列表", "Common.define.smartArt.textVerticalPictureList": "垂直图片列表", "Common.define.smartArt.textVerticalProcess": "垂直流程", "Common.Translation.textMoreButton": "更多", "Common.Translation.tipFileLocked": "文件被锁定，无法编辑。您可以进行修改并将其另存为本地副本。", "Common.Translation.tipFileReadOnly": "该文件具有只读访问权限。要保留您的改动，请重新名或在不同的位置保存该文件。", "Common.Translation.warnFileLocked": "另一个应用程序正在编辑本文件。您在可以继续编辑，并另存为副本。", "Common.Translation.warnFileLockedBtnEdit": "创建拷贝", "Common.Translation.warnFileLockedBtnView": "打开以查看", "Common.UI.ButtonColored.textAutoColor": "自动", "Common.UI.ButtonColored.textNewColor": "添加新的自定义颜色", "Common.UI.ComboBorderSize.txtNoBorders": "没有边框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "没有边框", "Common.UI.ComboDataView.emptyComboText": "没有风格", "Common.UI.ExtendedColorDialog.addButtonText": "添加", "Common.UI.ExtendedColorDialog.textCurrent": "当前", "Common.UI.ExtendedColorDialog.textHexErr": "输入的值不正确。<br>请输入000000和FFFFFF之间的值。", "Common.UI.ExtendedColorDialog.textNew": "新", "Common.UI.ExtendedColorDialog.textRGBErr": "输入的值不正确。<br>请输入介于0和255之间的数值。", "Common.UI.HSBColorPicker.textNoColor": "没有颜色", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "隐藏密码", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "显示密码", "Common.UI.SearchBar.textFind": "查找", "Common.UI.SearchBar.tipCloseSearch": "关闭搜索", "Common.UI.SearchBar.tipNextResult": "下一个", "Common.UI.SearchBar.tipOpenAdvancedSettings": "打开高级设置", "Common.UI.SearchBar.tipPreviousResult": "上一个", "Common.UI.SearchDialog.textHighlight": "高亮效果", "Common.UI.SearchDialog.textMatchCase": "区分大小写", "Common.UI.SearchDialog.textReplaceDef": "输入替换文字", "Common.UI.SearchDialog.textSearchStart": "在这里输入你的文字", "Common.UI.SearchDialog.textTitle": "查找和替换", "Common.UI.SearchDialog.textTitle2": "发现", "Common.UI.SearchDialog.textWholeWords": "只有整个字", "Common.UI.SearchDialog.txtBtnHideReplace": "隐藏替换", "Common.UI.SearchDialog.txtBtnReplace": "替换", "Common.UI.SearchDialog.txtBtnReplaceAll": "全部替换", "Common.UI.SynchronizeTip.textDontShow": "不要再显示此消息", "Common.UI.SynchronizeTip.textSynchronize": "该文档已被其他用户更改。<br>请点击保存更改并重新加载更新。", "Common.UI.ThemeColorPalette.textRecentColors": "最近的颜色", "Common.UI.ThemeColorPalette.textStandartColors": "标准颜色", "Common.UI.ThemeColorPalette.textThemeColors": "主题颜色", "Common.UI.Themes.txtThemeClassicLight": "经典浅色", "Common.UI.Themes.txtThemeContrastDark": "反差暗色", "Common.UI.Themes.txtThemeDark": "暗色模式", "Common.UI.Themes.txtThemeLight": "浅色主題", "Common.UI.Themes.txtThemeSystem": "和系统一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "关闭", "Common.UI.Window.noButtonText": "否", "Common.UI.Window.okButtonText": "确定", "Common.UI.Window.textConfirmation": "确认", "Common.UI.Window.textDontShow": "不要再显示此消息", "Common.UI.Window.textError": "错误:", "Common.UI.Window.textInformation": "信息", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "厘米", "Common.Utils.Metric.txtPt": "像素", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "地址：", "Common.Views.About.txtLicensee": "被许可人", "Common.Views.About.txtLicensor": "许可", "Common.Views.About.txtMail": "电子邮件：", "Common.Views.About.txtPoweredBy": "技术支持", "Common.Views.About.txtTel": "电话：", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "新增", "Common.Views.AutoCorrectDialog.textApplyText": "输入时自动应用", "Common.Views.AutoCorrectDialog.textAutoCorrect": "自动修正", "Common.Views.AutoCorrectDialog.textAutoFormat": "输入时自动调整格式", "Common.Views.AutoCorrectDialog.textBulleted": "自动项目符号列表", "Common.Views.AutoCorrectDialog.textBy": "依据", "Common.Views.AutoCorrectDialog.textDelete": "删除", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "按两下空白键自动增加一个句点(.)符号", "Common.Views.AutoCorrectDialog.textFLCells": "表格单元格的首字母大写", "Common.Views.AutoCorrectDialog.textFLSentence": "句首字母大写", "Common.Views.AutoCorrectDialog.textHyperlink": "带超链接的互联网与网络路径", "Common.Views.AutoCorrectDialog.textHyphens": "连带字符（-）改为破折号（-）", "Common.Views.AutoCorrectDialog.textMathCorrect": "数学自动修正", "Common.Views.AutoCorrectDialog.textNumbered": "自动编号列表", "Common.Views.AutoCorrectDialog.textQuotes": "“直引号”替换为“弯引号”", "Common.Views.AutoCorrectDialog.textRecognized": "可识别的函数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表达式被识别为数学公式。这些表达式不会被自动设为斜体。", "Common.Views.AutoCorrectDialog.textReplace": "替换", "Common.Views.AutoCorrectDialog.textReplaceText": "输入时自动替换", "Common.Views.AutoCorrectDialog.textReplaceType": "输入时自动替换文字", "Common.Views.AutoCorrectDialog.textReset": "重置", "Common.Views.AutoCorrectDialog.textResetAll": "重置为默认", "Common.Views.AutoCorrectDialog.textRestore": "恢复", "Common.Views.AutoCorrectDialog.textTitle": "自动修正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "识别的函数只能包含字母A到Z，大写或小写。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "由您新增的表达式将被移除，由您移除的将被恢复。是否继续？", "Common.Views.AutoCorrectDialog.warnReplace": "关于％1的自动更正条目已存在。确认替换？", "Common.Views.AutoCorrectDialog.warnReset": "即将移除对自动更正功能所做的自定义设置，并恢复默认值。是否继续？", "Common.Views.AutoCorrectDialog.warnRestore": "关于％1的自动更正条目将恢复默认值。确认继续？", "Common.Views.Chat.textSend": "发送", "Common.Views.Comments.mniAuthorAsc": "作者 A到Z", "Common.Views.Comments.mniAuthorDesc": "作者 Z到A", "Common.Views.Comments.mniDateAsc": "最旧", "Common.Views.Comments.mniDateDesc": "最新", "Common.Views.Comments.mniFilterGroups": "按组筛选", "Common.Views.Comments.mniPositionAsc": "自上而下", "Common.Views.Comments.mniPositionDesc": "自下而上", "Common.Views.Comments.textAdd": "添加", "Common.Views.Comments.textAddComment": "添加批注", "Common.Views.Comments.textAddCommentToDoc": "添加批注到文档", "Common.Views.Comments.textAddReply": "添加回复", "Common.Views.Comments.textAll": "所有", "Common.Views.Comments.textAnonym": "游客", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "关闭", "Common.Views.Comments.textClosePanel": "关闭注释", "Common.Views.Comments.textComments": "评论", "Common.Views.Comments.textEdit": "确定", "Common.Views.Comments.textEnterCommentHint": "在此输入您的批注", "Common.Views.Comments.textHintAddComment": "添加批注", "Common.Views.Comments.textOpenAgain": "再次打开", "Common.Views.Comments.textReply": "回复", "Common.Views.Comments.textResolve": "解决", "Common.Views.Comments.textResolved": "已解决", "Common.Views.Comments.textSort": "评论排序", "Common.Views.Comments.textViewResolved": "您没有重开评论的权限", "Common.Views.Comments.txtEmpty": "文件中没有任何评论。", "Common.Views.CopyWarningDialog.textDontShow": "不要再显示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用编辑器工具栏按钮和上下文菜单操作复制，剪切和粘贴操作将仅在此编辑器选项卡中执行。<br> <br>要在编辑器选项卡之外复制或粘贴到应用程序，请使用以下键盘组合：", "Common.Views.CopyWarningDialog.textTitle": "复制，剪切和粘贴操作", "Common.Views.CopyWarningDialog.textToCopy": "复制", "Common.Views.CopyWarningDialog.textToCut": "剪切", "Common.Views.CopyWarningDialog.textToPaste": "粘贴", "Common.Views.DocumentAccessDialog.textLoading": "载入中……", "Common.Views.DocumentAccessDialog.textTitle": "共享设置", "Common.Views.ExternalDiagramEditor.textTitle": "图表编辑器", "Common.Views.ExternalEditor.textClose": "关闭", "Common.Views.ExternalEditor.textSave": "保存并退出", "Common.Views.ExternalOleEditor.textTitle": "电子表格编辑器", "Common.Views.Header.labelCoUsersDescr": "正在编辑文件的用户：", "Common.Views.Header.textAddFavorite": "记入收藏夹", "Common.Views.Header.textAdvSettings": "高级设置", "Common.Views.Header.textBack": "打开文件所在位置", "Common.Views.Header.textCompactView": "查看紧凑工具栏", "Common.Views.Header.textHideLines": "隐藏标尺", "Common.Views.Header.textHideNotes": "隐藏注释", "Common.Views.Header.textHideStatusBar": "隐藏状态栏", "Common.Views.Header.textReadOnly": "只读", "Common.Views.Header.textRemoveFavorite": "从收藏夹中删除", "Common.Views.Header.textSaveBegin": "正在保存...", "Common.Views.Header.textSaveChanged": "已修改", "Common.Views.Header.textSaveEnd": "所有更改已保存", "Common.Views.Header.textSaveExpander": "所有更改已保存", "Common.Views.Header.textShare": "共享", "Common.Views.Header.textZoom": "放大", "Common.Views.Header.tipAccessRights": "管理文档访问权限", "Common.Views.Header.tipDownload": "下载文件", "Common.Views.Header.tipGoEdit": "编辑当前文件", "Common.Views.Header.tipPrint": "打印文件", "Common.Views.Header.tipPrintQuick": "快速打印", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipSave": "保存", "Common.Views.Header.tipSearch": "搜索", "Common.Views.Header.tipUndo": "复原", "Common.Views.Header.tipUndock": "打开单独的窗口", "Common.Views.Header.tipUsers": "查看用户", "Common.Views.Header.tipViewSettings": "视图设置", "Common.Views.Header.tipViewUsers": "查看用户和管理文档访问权限", "Common.Views.Header.txtAccessRights": "更改访问权限", "Common.Views.Header.txtRename": "重命名", "Common.Views.History.textCloseHistory": "关闭历史记录", "Common.Views.History.textHide": "折叠", "Common.Views.History.textHideAll": "隐藏详细的更改", "Common.Views.History.textRestore": "恢复", "Common.Views.History.textShow": "展开", "Common.Views.History.textShowAll": "显示详细的更改", "Common.Views.History.textVer": "版本", "Common.Views.ImageFromUrlDialog.textUrl": "粘贴图片网址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "这是必填栏", "Common.Views.ImageFromUrlDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "Common.Views.InsertTableDialog.textInvalidRowsCols": "您需要指定有效的行和列号", "Common.Views.InsertTableDialog.txtColumns": "列数", "Common.Views.InsertTableDialog.txtMaxText": "该字段的最大值为{0}。", "Common.Views.InsertTableDialog.txtMinText": "该字段的最小值为{0}。", "Common.Views.InsertTableDialog.txtRows": "行数", "Common.Views.InsertTableDialog.txtTitle": "表格大小", "Common.Views.InsertTableDialog.txtTitleSplit": "拆分单元格", "Common.Views.LanguageDialog.labelSelect": "选择文档语言", "Common.Views.ListSettingsDialog.textBulleted": "已添加项目符号", "Common.Views.ListSettingsDialog.textFromFile": "从文件导入", "Common.Views.ListSettingsDialog.textFromStorage": "来自存储设备", "Common.Views.ListSettingsDialog.textFromUrl": "从URL", "Common.Views.ListSettingsDialog.textNumbering": "标号", "Common.Views.ListSettingsDialog.textSelect": "选择", "Common.Views.ListSettingsDialog.tipChange": "修改项目点", "Common.Views.ListSettingsDialog.txtBullet": "项目符号", "Common.Views.ListSettingsDialog.txtColor": "颜色", "Common.Views.ListSettingsDialog.txtImage": "图片", "Common.Views.ListSettingsDialog.txtImport": "导入", "Common.Views.ListSettingsDialog.txtNewBullet": "添加一个新的项目点", "Common.Views.ListSettingsDialog.txtNewImage": "新图片", "Common.Views.ListSettingsDialog.txtNone": "无", "Common.Views.ListSettingsDialog.txtOfText": "文本的%", "Common.Views.ListSettingsDialog.txtSize": "大小", "Common.Views.ListSettingsDialog.txtStart": "开始", "Common.Views.ListSettingsDialog.txtSymbol": "符号", "Common.Views.ListSettingsDialog.txtTitle": "列表设置", "Common.Views.ListSettingsDialog.txtType": "类型", "Common.Views.OpenDialog.closeButtonText": "关闭文件", "Common.Views.OpenDialog.txtEncoding": "编码", "Common.Views.OpenDialog.txtIncorrectPwd": "密码错误", "Common.Views.OpenDialog.txtOpenFile": "输入密码来打开文件", "Common.Views.OpenDialog.txtPassword": "密码", "Common.Views.OpenDialog.txtProtected": "在您输入密码和打开文件后，该文件的当前密码将被重置。", "Common.Views.OpenDialog.txtTitle": "选择%1个选项", "Common.Views.OpenDialog.txtTitleProtected": "受保护的文件", "Common.Views.PasswordDialog.txtDescription": "设置密码以保护此文档", "Common.Views.PasswordDialog.txtIncorrectPwd": "确认密码不一致", "Common.Views.PasswordDialog.txtPassword": "密码", "Common.Views.PasswordDialog.txtRepeat": "重复输入密码", "Common.Views.PasswordDialog.txtTitle": "设置密码", "Common.Views.PasswordDialog.txtWarning": "警告: 如果丢失或忘记密码，则无法将其恢复。将其保存在安全位置。", "Common.Views.PluginDlg.textLoading": "载入中", "Common.Views.Plugins.groupCaption": "插件", "Common.Views.Plugins.strPlugins": "插件", "Common.Views.Plugins.textClosePanel": "关闭插件", "Common.Views.Plugins.textLoading": "载入中", "Common.Views.Plugins.textStart": "开始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Protection.hintAddPwd": "使用密码加密", "Common.Views.Protection.hintDelPwd": "删除密码", "Common.Views.Protection.hintPwd": "修改或删除密码", "Common.Views.Protection.hintSignature": "添加数字签名或签名行", "Common.Views.Protection.txtAddPwd": "添加密码", "Common.Views.Protection.txtChangePwd": "修改密码", "Common.Views.Protection.txtDeletePwd": "删除密码", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "添加数字签名", "Common.Views.Protection.txtSignature": "签名", "Common.Views.Protection.txtSignatureLine": "添加签名行", "Common.Views.RenameDialog.textName": "文件名", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "下一个变化", "Common.Views.ReviewChanges.hintPrev": "以前的变化", "Common.Views.ReviewChanges.strFast": "自动", "Common.Views.ReviewChanges.strFastDesc": "自动共同编辑模式，自动保存修改痕迹。", "Common.Views.ReviewChanges.strStrict": "手动", "Common.Views.ReviewChanges.strStrictDesc": "使用“保存”按钮同步你和其他人的修改。", "Common.Views.ReviewChanges.tipAcceptCurrent": "接受当前的变化", "Common.Views.ReviewChanges.tipCoAuthMode": "设置共同编辑模式", "Common.Views.ReviewChanges.tipCommentRem": "移除批注", "Common.Views.ReviewChanges.tipCommentRemCurrent": "移除当前批注", "Common.Views.ReviewChanges.tipCommentResolve": "解决评论", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "解决该评论", "Common.Views.ReviewChanges.tipHistory": "显示历史版本", "Common.Views.ReviewChanges.tipRejectCurrent": "拒绝当前的变化", "Common.Views.ReviewChanges.tipReview": "跟踪变化", "Common.Views.ReviewChanges.tipReviewView": "选择您希望显示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "设置文档语言", "Common.Views.ReviewChanges.tipSetSpelling": "拼写检查", "Common.Views.ReviewChanges.tipSharing": "管理文档访问权限", "Common.Views.ReviewChanges.txtAccept": "接受", "Common.Views.ReviewChanges.txtAcceptAll": "接受所有更改", "Common.Views.ReviewChanges.txtAcceptChanges": "接受更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "接受当前的变化", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "关闭", "Common.Views.ReviewChanges.txtCoAuthMode": "共同编辑模式", "Common.Views.ReviewChanges.txtCommentRemAll": "移除所有批注", "Common.Views.ReviewChanges.txtCommentRemCurrent": "移除当前批注", "Common.Views.ReviewChanges.txtCommentRemMy": "移除我的批注", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "移除我的当前批注", "Common.Views.ReviewChanges.txtCommentRemove": "删除", "Common.Views.ReviewChanges.txtCommentResolve": "解决", "Common.Views.ReviewChanges.txtCommentResolveAll": "解决全体评论", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "解决该评论", "Common.Views.ReviewChanges.txtCommentResolveMy": "解决我的评论", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "解决我当前的评论", "Common.Views.ReviewChanges.txtDocLang": "语言", "Common.Views.ReviewChanges.txtFinal": "已接受所有更改（预览）", "Common.Views.ReviewChanges.txtFinalCap": "最终版", "Common.Views.ReviewChanges.txtHistory": "版本历史", "Common.Views.ReviewChanges.txtMarkup": "所有更改（编辑）", "Common.Views.ReviewChanges.txtMarkupCap": "标记", "Common.Views.ReviewChanges.txtNext": "下一个变化", "Common.Views.ReviewChanges.txtOriginal": "已拒绝所有更改（预览）", "Common.Views.ReviewChanges.txtOriginalCap": "原始版", "Common.Views.ReviewChanges.txtPrev": "以前的变化", "Common.Views.ReviewChanges.txtReject": "拒绝", "Common.Views.ReviewChanges.txtRejectAll": "拒绝所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "拒绝更改", "Common.Views.ReviewChanges.txtRejectCurrent": "拒绝当前的变化", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼写检查", "Common.Views.ReviewChanges.txtTurnon": "跟踪变化", "Common.Views.ReviewChanges.txtView": "显示模式", "Common.Views.ReviewPopover.textAdd": "添加", "Common.Views.ReviewPopover.textAddReply": "添加回复", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "关闭", "Common.Views.ReviewPopover.textEdit": "确定", "Common.Views.ReviewPopover.textEnterComment": "在此输入您的批注", "Common.Views.ReviewPopover.textMention": "+提供访问文档和发送电子邮件的权限", "Common.Views.ReviewPopover.textMentionNotify": "+通知将通过电子邮件通知用户", "Common.Views.ReviewPopover.textOpenAgain": "再次打开", "Common.Views.ReviewPopover.textReply": "回复", "Common.Views.ReviewPopover.textResolve": "解决", "Common.Views.ReviewPopover.textViewResolved": "您没有重开评论的权限", "Common.Views.ReviewPopover.txtDeleteTip": "删除", "Common.Views.ReviewPopover.txtEditTip": "编辑", "Common.Views.SaveAsDlg.textLoading": "载入中", "Common.Views.SaveAsDlg.textTitle": "保存文件夹", "Common.Views.SearchPanel.textCaseSensitive": "区分大小写", "Common.Views.SearchPanel.textCloseSearch": "关闭搜索", "Common.Views.SearchPanel.textContentChanged": "文件已更改。", "Common.Views.SearchPanel.textFind": "查找", "Common.Views.SearchPanel.textFindAndReplace": "查找和替换", "Common.Views.SearchPanel.textMatchUsingRegExp": "正则匹配", "Common.Views.SearchPanel.textNoMatches": "找不到匹配信息", "Common.Views.SearchPanel.textNoSearchResults": "找不到搜索结果", "Common.Views.SearchPanel.textReplace": "替换", "Common.Views.SearchPanel.textReplaceAll": "全部替换", "Common.Views.SearchPanel.textReplaceWith": "替换为", "Common.Views.SearchPanel.textSearchAgain": "{0}进行新的搜索{1}以获得准确结果。", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "结果太多了", "Common.Views.SearchPanel.textWholeWords": "整词匹配", "Common.Views.SearchPanel.tipNextResult": "下一个", "Common.Views.SearchPanel.tipPreviousResult": "上一个", "Common.Views.SelectFileDlg.textLoading": "载入中", "Common.Views.SelectFileDlg.textTitle": "选择数据源", "Common.Views.SignDialog.textBold": "加粗", "Common.Views.SignDialog.textCertificate": "证书", "Common.Views.SignDialog.textChange": "修改", "Common.Views.SignDialog.textInputName": "输入签名者姓名", "Common.Views.SignDialog.textItalic": "斜体", "Common.Views.SignDialog.textNameError": "签名者名称不能为空", "Common.Views.SignDialog.textPurpose": "签署本文档的目的", "Common.Views.SignDialog.textSelect": "请选择", "Common.Views.SignDialog.textSelectImage": "选择图像", "Common.Views.SignDialog.textSignature": "签名类似", "Common.Views.SignDialog.textTitle": "签署文件", "Common.Views.SignDialog.textUseImage": "或单击“选择图像”将图像用作签名", "Common.Views.SignDialog.textValid": "从%1到%2有效", "Common.Views.SignDialog.tipFontName": "字体名称", "Common.Views.SignDialog.tipFontSize": "字体大小", "Common.Views.SignSettingsDialog.textAllowComment": "允许签名者在签名对话框中添加批注", "Common.Views.SignSettingsDialog.textDefInstruction": "在签署本文件之前，请确保您所签署的内容是正确。", "Common.Views.SignSettingsDialog.textInfoEmail": "建议签署人电邮", "Common.Views.SignSettingsDialog.textInfoName": "建议签署人", "Common.Views.SignSettingsDialog.textInfoTitle": "建议签署人职务", "Common.Views.SignSettingsDialog.textInstructions": "给签名者的说明", "Common.Views.SignSettingsDialog.textShowDate": "在签名行中显示签名日期", "Common.Views.SignSettingsDialog.textTitle": "签名设置", "Common.Views.SignSettingsDialog.txtEmpty": "这是必填栏", "Common.Views.SymbolTableDialog.textCharacter": "字符", "Common.Views.SymbolTableDialog.textCode": "Unicode十六进制值", "Common.Views.SymbolTableDialog.textCopyright": "版权所有标识", "Common.Views.SymbolTableDialog.textDCQuote": "后双引号", "Common.Views.SymbolTableDialog.textDOQuote": "前双引号", "Common.Views.SymbolTableDialog.textEllipsis": "横向省略号", "Common.Views.SymbolTableDialog.textEmDash": "破折号", "Common.Views.SymbolTableDialog.textEmSpace": "全角空格", "Common.Views.SymbolTableDialog.textEnDash": "半破折号", "Common.Views.SymbolTableDialog.textEnSpace": "半角空格", "Common.Views.SymbolTableDialog.textFont": "字体 ", "Common.Views.SymbolTableDialog.textNBHyphen": "不换行连词符", "Common.Views.SymbolTableDialog.textNBSpace": "不换行空格", "Common.Views.SymbolTableDialog.textPilcrow": "段落标识", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4全角空格", "Common.Views.SymbolTableDialog.textRange": "子集", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符号", "Common.Views.SymbolTableDialog.textRegistered": "注册商标标识", "Common.Views.SymbolTableDialog.textSCQuote": "后单引号", "Common.Views.SymbolTableDialog.textSection": "章节标识", "Common.Views.SymbolTableDialog.textShortcut": "快捷键", "Common.Views.SymbolTableDialog.textSHyphen": "软连词符", "Common.Views.SymbolTableDialog.textSOQuote": "前单引号", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符号", "Common.Views.SymbolTableDialog.textTitle": "符号", "Common.Views.SymbolTableDialog.textTradeMark": "商标标识", "Common.Views.UserNameDialog.textDontShow": "不要再问我", "Common.Views.UserNameDialog.textLabel": "标签：", "Common.Views.UserNameDialog.textLabelError": "标签不能空", "PE.Controllers.LeftMenu.leavePageText": "此文档中的所有未保存的更改都将丢失。<br>单击“取消”，然后单击“保存”以保存好。单击“确定”以放弃所有未保存的更改。", "PE.Controllers.LeftMenu.newDocumentTitle": "未命名的演示", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "PE.Controllers.LeftMenu.requestEditRightsText": "请求编辑权限..", "PE.Controllers.LeftMenu.textLoadHistory": "载入版本历史记录...", "PE.Controllers.LeftMenu.textNoTextFound": "您搜索的数据无法找到。请调整您的搜索选项。", "PE.Controllers.LeftMenu.textReplaceSkipped": "已经更换了。{0}次跳过。", "PE.Controllers.LeftMenu.textReplaceSuccess": "他的搜索已经完成。发生次数：{0}", "PE.Controllers.LeftMenu.txtUntitled": "无标题", "PE.Controllers.Main.applyChangesTextText": "数据加载中…", "PE.Controllers.Main.applyChangesTitleText": "数据加载中", "PE.Controllers.Main.confirmMaxChangesSize": "行动的大小超过了对您服务器设置的限制。<br>按 \"撤消\"取消您的最后一次行动，或按\"继续\"在本地保留该行动（您需要下载文件或复制其内容以确保没有任何损失）。", "PE.Controllers.Main.convertationTimeoutText": "转换超时", "PE.Controllers.Main.criticalErrorExtText": "按“确定”返回该文件列表。", "PE.Controllers.Main.criticalErrorTitle": "错误:", "PE.Controllers.Main.downloadErrorText": "下载失败", "PE.Controllers.Main.downloadTextText": "正在下载演示文稿...", "PE.Controllers.Main.downloadTitleText": "下载文件", "PE.Controllers.Main.errorAccessDeny": "您正在尝试执行您没有权限的操作。<br>请联系您的文档服务器管理员。", "PE.Controllers.Main.errorBadImageUrl": "图片地址不正确", "PE.Controllers.Main.errorCannotPasteImg": "我们无法从剪贴板上粘贴这个图片，您可以把它保存到您的设备上，然后从那里进行插入。\n或者您可以不复制文本而只复制图片，并将它粘贴到演示文稿中。", "PE.Controllers.Main.errorCoAuthoringDisconnect": "服务器连接丢失。该文档现在无法编辑", "PE.Controllers.Main.errorComboSeries": "要创建联立图表，请选中至少两组数据。", "PE.Controllers.Main.errorConnectToServer": "这份文件无法保存。请检查连接设置或联系您的管理员。<br>当你点击“OK”按钮,系统将提示您下载文档。", "PE.Controllers.Main.errorDatabaseConnection": "外部错误。<br>数据库连接错误。如果错误仍然存​​在，请联系支持人员。", "PE.Controllers.Main.errorDataEncrypted": "加密更改已收到，无法对其解密。", "PE.Controllers.Main.errorDataRange": "数据范围不正确", "PE.Controllers.Main.errorDefaultMessage": "错误代码：%1", "PE.Controllers.Main.errorDirectUrl": "请验证指向文档的链接。<br>此链接必须是指向要下载的文件的直接链接。", "PE.Controllers.Main.errorEditingDownloadas": "在处理文档期间发生错误。<br>使用“下载为”选项将文件备份复制到您的计算机硬盘中。", "PE.Controllers.Main.errorEditingSaveas": "在处理文档期间发生错误。<br>使用“另存为…”选项将文件备份复制到计算机硬盘中。", "PE.Controllers.Main.errorEmailClient": "未找到电子邮件客户端。", "PE.Controllers.Main.errorFilePassProtect": "该文档受密码保护，无法被打开。", "PE.Controllers.Main.errorFileSizeExceed": "文件大小超出了为服务器设置的限制.<br>有关详细信息，请与文档服务器管理员联系。", "PE.Controllers.Main.errorForceSave": "保存文件时发生错误请使用“下载为”选项将文件保存到计算机硬盘中或稍后重试。", "PE.Controllers.Main.errorInconsistentExt": "在打开文件时发生了错误。<br> 该文件的扩展名不一致：%1。", "PE.Controllers.Main.errorInconsistentExtDocx": "在打开文件时发生了错误。<br>文件内容对应于文本文档（如 docx），但该文件的扩展名不一致：%1。", "PE.Controllers.Main.errorInconsistentExtPdf": "在打开文件时发生了错误。<br>文件内容对应于以下格式之一：pdf/djvu/xps/oxps，但该文件的扩展名不一致：%1。", "PE.Controllers.Main.errorInconsistentExtPptx": "在打开文件时发生了错误。<br>文件内容对应于演示文稿（如 pptx），但该文件的扩展名不一致：%1。", "PE.Controllers.Main.errorInconsistentExtXlsx": "在打开文件时发生了错误。<br>文件内容对应于电子表格（如 xlsx），但该文件的扩展名不一致：%1。", "PE.Controllers.Main.errorKeyEncrypt": "未知密钥描述", "PE.Controllers.Main.errorKeyExpire": "密钥过期", "PE.Controllers.Main.errorLoadingFont": "字体未加载。<br>请与文档服务器管理员联系。", "PE.Controllers.Main.errorProcessSaveResult": "保存失败", "PE.Controllers.Main.errorServerVersion": "该编辑版本已经更新。该页面将被重新加载以应用更改。", "PE.Controllers.Main.errorSessionAbsolute": "文档编辑会话已过期。请重新加载页面。", "PE.Controllers.Main.errorSessionIdle": "该文件尚未编辑相当长的时间。请重新加载页面。", "PE.Controllers.Main.errorSessionToken": "与服务器的连接已中断。请重新加载页面。", "PE.Controllers.Main.errorSetPassword": "未能成功设置密码", "PE.Controllers.Main.errorStockChart": "行顺序不正确。建立股票图表将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "PE.Controllers.Main.errorToken": "文档安全令牌未正确形成。<br>请与您的文件服务器管理员联系。", "PE.Controllers.Main.errorTokenExpire": "文档安全令牌已过期。<br>请与您的文档服务器管理员联系。", "PE.Controllers.Main.errorUpdateVersion": "该文件版本已经改变了。该页面将被重新加载。", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "网络连接已恢复，文件版本已变更。<br>在继续工作之前，需要下载文件或复制其内容以避免丢失数据，然后重新加载此页。", "PE.Controllers.Main.errorUserDrop": "该文件现在无法访问。", "PE.Controllers.Main.errorUsersExceed": "超过了定价计划允许的用户数", "PE.Controllers.Main.errorViewerDisconnect": "连接丢失了。您仍然可以查看文档，<br>但在连接恢复并页面被重新加载之前无法下载或打印。", "PE.Controllers.Main.leavePageText": "您在本演示文稿中有未保存的更改。点击“留在这个页面”，然后点击“保存”保存。点击“离开此页面”，放弃所有未保存的更改。", "PE.Controllers.Main.leavePageTextOnClose": "此文档中所有未保存的更改都将丟失。<br>点击“取消”，然后点击“保存”以保存。点击“确定”，放弃所有未保存的更改。", "PE.Controllers.Main.loadFontsTextText": "数据加载中…", "PE.Controllers.Main.loadFontsTitleText": "数据加载中", "PE.Controllers.Main.loadFontTextText": "数据加载中…", "PE.Controllers.Main.loadFontTitleText": "数据加载中", "PE.Controllers.Main.loadImagesTextText": "图片加载中…", "PE.Controllers.Main.loadImagesTitleText": "图片加载中", "PE.Controllers.Main.loadImageTextText": "图片加载中…", "PE.Controllers.Main.loadImageTitleText": "图片加载中", "PE.Controllers.Main.loadingDocumentTextText": "载入演示文稿...", "PE.Controllers.Main.loadingDocumentTitleText": "载入演示", "PE.Controllers.Main.loadThemeTextText": "装载主题", "PE.Controllers.Main.loadThemeTitleText": "装载主题", "PE.Controllers.Main.notcriticalErrorTitle": "警告", "PE.Controllers.Main.openErrorText": "打开文件时出现错误", "PE.Controllers.Main.openTextText": "开幕式...", "PE.Controllers.Main.openTitleText": "开幕式", "PE.Controllers.Main.printTextText": "打印演示文稿", "PE.Controllers.Main.printTitleText": "打印演示", "PE.Controllers.Main.reloadButtonText": "重新加载页面", "PE.Controllers.Main.requestEditFailedMessageText": "有人正在编辑此演示文稿。请稍后再试。", "PE.Controllers.Main.requestEditFailedTitleText": "访问被拒绝", "PE.Controllers.Main.saveErrorText": "保存文件时出现错误", "PE.Controllers.Main.saveErrorTextDesktop": "无法保存或创建此文件。<br>可能的原因是：<br> 1.此文件是只读的。 <br> 2.此文件正在由其他用户编辑。 <br> 3.磁盘已满或损坏。", "PE.Controllers.Main.saveTextText": "保存演示…", "PE.Controllers.Main.saveTitleText": "保存演示", "PE.Controllers.Main.scriptLoadError": "连接速度过慢，部分组件无法被加载。请重新加载页面。", "PE.Controllers.Main.splitDividerErrorText": "行数必须为%1的除数。", "PE.Controllers.Main.splitMaxColsErrorText": "列数必须小于%1。", "PE.Controllers.Main.splitMaxRowsErrorText": "行数必须小于%1。", "PE.Controllers.Main.textAnonymous": "匿名", "PE.Controllers.Main.textApplyAll": "应用到所有公式", "PE.Controllers.Main.textBuyNow": "访问网站", "PE.Controllers.Main.textChangesSaved": "所有更改已保存", "PE.Controllers.Main.textClose": "关闭", "PE.Controllers.Main.textCloseTip": "点击关闭提示", "PE.Controllers.Main.textContactUs": "联系销售", "PE.Controllers.Main.textContinue": "发送", "PE.Controllers.Main.textConvertEquation": "这个公式是由一个早期版本的公式编辑器创建的。这个版本现在不受支持了。要想编辑这个公式，你需要将其转换成 Office Math ML 格式.<br>现在转换吗？", "PE.Controllers.Main.textCustomLoader": "请注意，根据许可条款您无权更改加载程序。<br>请联系我们的销售部门获取报价。", "PE.Controllers.Main.textDisconnect": "网络连接失败", "PE.Controllers.Main.textGuest": "来宾", "PE.Controllers.Main.textHasMacros": "这个文件带有自动宏。<br> 是否要运行宏？", "PE.Controllers.Main.textLearnMore": "了解更多", "PE.Controllers.Main.textLoadingDocument": "载入演示", "PE.Controllers.Main.textLongName": "输入名称，要求小于128字符。", "PE.Controllers.Main.textNoLicenseTitle": "已达到许可证限制", "PE.Controllers.Main.textPaidFeature": "付费功能", "PE.Controllers.Main.textReconnect": "连接已恢复", "PE.Controllers.Main.textRemember": "针对所有的文件记住我的选择", "PE.Controllers.Main.textRememberMacros": "记住我的选择", "PE.Controllers.Main.textRenameError": "用户名不能留空。", "PE.Controllers.Main.textRenameLabel": "输入名称，可用于协作。", "PE.Controllers.Main.textRequestMacros": "宏发起一个请求至URL。你是否允许请求到％1？", "PE.Controllers.Main.textShape": "形状", "PE.Controllers.Main.textStrict": "手动模式", "PE.Controllers.Main.textText": "文本", "PE.Controllers.Main.textTryQuickPrint": "您已经选择了快速打印：整个文件将用最新选定的打印机或默认的打印机被打印。<br>您想继续吗？", "PE.Controllers.Main.textTryUndoRedo": "对于自动的协同编辑模式，取消/重做功能是禁用的。< br >单击“手动模式”按钮切换到手动协同编辑模式，这样，编辑该文件时只有您保存修改之后，其他用户才能访问这些修改。您可以使用编辑器高级设置易于切换编辑模式。", "PE.Controllers.Main.textTryUndoRedoWarn": "自动共同编辑模式下，撤销/重做功能被禁用。", "PE.Controllers.Main.textUndo": "撤消", "PE.Controllers.Main.titleLicenseExp": "许可证过期", "PE.Controllers.Main.titleServerVersion": "编辑器已更新", "PE.Controllers.Main.txtAddFirstSlide": "单击添加第一张幻灯片", "PE.Controllers.Main.txtAddNotes": "单击添加备注", "PE.Controllers.Main.txtArt": "你的文本在此", "PE.Controllers.Main.txtBasicShapes": "基本形状", "PE.Controllers.Main.txtButtons": "按钮", "PE.Controllers.Main.txtCallouts": "标注", "PE.Controllers.Main.txtCharts": "流程图", "PE.Controllers.Main.txtClipArt": "剪贴画", "PE.Controllers.Main.txtDateTime": "日期与时间", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "图表标题", "PE.Controllers.Main.txtEditingMode": "设置编辑模式..", "PE.Controllers.Main.txtErrorLoadHistory": "历史记录加载失败", "PE.Controllers.Main.txtFiguredArrows": "箭头汇总", "PE.Controllers.Main.txtFooter": "页脚", "PE.Controllers.Main.txtHeader": "页眉", "PE.Controllers.Main.txtImage": "图片", "PE.Controllers.Main.txtLines": "线条", "PE.Controllers.Main.txtLoading": "加载中…", "PE.Controllers.Main.txtMath": "公式形状", "PE.Controllers.Main.txtMedia": "媒体", "PE.Controllers.Main.txtNeedSynchronize": "你有更新", "PE.Controllers.Main.txtNone": "无", "PE.Controllers.Main.txtPicture": "图片", "PE.Controllers.Main.txtRectangles": "矩形", "PE.Controllers.Main.txtSeries": "系列", "PE.Controllers.Main.txtShape_accentBorderCallout1": "线形标注1（边框和强调线）", "PE.Controllers.Main.txtShape_accentBorderCallout2": "线形标注2（边框和强调线）", "PE.Controllers.Main.txtShape_accentBorderCallout3": "线形标注3（边框和强调线）", "PE.Controllers.Main.txtShape_accentCallout1": "线形标注1（强调线）", "PE.Controllers.Main.txtShape_accentCallout2": "线形标注2（强调线）", "PE.Controllers.Main.txtShape_accentCallout3": "线形标注3（强调线）", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "后退或上一个按钮", "PE.Controllers.Main.txtShape_actionButtonBeginning": "开始按钮", "PE.Controllers.Main.txtShape_actionButtonBlank": "空白按钮", "PE.Controllers.Main.txtShape_actionButtonDocument": "文件按钮", "PE.Controllers.Main.txtShape_actionButtonEnd": "结束按钮", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "转发或下一个按钮", "PE.Controllers.Main.txtShape_actionButtonHelp": "帮助按钮", "PE.Controllers.Main.txtShape_actionButtonHome": "主页按钮", "PE.Controllers.Main.txtShape_actionButtonInformation": "信息按钮", "PE.Controllers.Main.txtShape_actionButtonMovie": "视频按钮", "PE.Controllers.Main.txtShape_actionButtonReturn": "返回按钮", "PE.Controllers.Main.txtShape_actionButtonSound": "声音按钮", "PE.Controllers.Main.txtShape_arc": "弧", "PE.Controllers.Main.txtShape_bentArrow": "圆角右箭头", "PE.Controllers.Main.txtShape_bentConnector5": "肘形连接符", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "肘形箭头连接符", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "肘形双箭头连接符", "PE.Controllers.Main.txtShape_bentUpArrow": "直角上箭头", "PE.Controllers.Main.txtShape_bevel": "斜角", "PE.Controllers.Main.txtShape_blockArc": "空心弧", "PE.Controllers.Main.txtShape_borderCallout1": "线形标注1", "PE.Controllers.Main.txtShape_borderCallout2": "线形标注2", "PE.Controllers.Main.txtShape_borderCallout3": "线形标注3", "PE.Controllers.Main.txtShape_bracePair": "双大括号", "PE.Controllers.Main.txtShape_callout1": "线形标注1（无边框）", "PE.Controllers.Main.txtShape_callout2": "线形标注2（无边框）", "PE.Controllers.Main.txtShape_callout3": "线形标注3（无边框）", "PE.Controllers.Main.txtShape_can": "圆柱", "PE.Controllers.Main.txtShape_chevron": "V形", "PE.Controllers.Main.txtShape_chord": "弦形", "PE.Controllers.Main.txtShape_circularArrow": "环形箭头", "PE.Controllers.Main.txtShape_cloud": "云形", "PE.Controllers.Main.txtShape_cloudCallout": "云形标注", "PE.Controllers.Main.txtShape_corner": "角", "PE.Controllers.Main.txtShape_cube": "正方体", "PE.Controllers.Main.txtShape_curvedConnector3": "曲线连接符", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "曲线箭头连接符", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "曲线双箭头连接符", "PE.Controllers.Main.txtShape_curvedDownArrow": "下弧形箭头", "PE.Controllers.Main.txtShape_curvedLeftArrow": "左弧形箭头", "PE.Controllers.Main.txtShape_curvedRightArrow": "右弧形箭头", "PE.Controllers.Main.txtShape_curvedUpArrow": "上弧形箭头", "PE.Controllers.Main.txtShape_decagon": "十边形", "PE.Controllers.Main.txtShape_diagStripe": "斜纹", "PE.Controllers.Main.txtShape_diamond": "菱形", "PE.Controllers.Main.txtShape_dodecagon": "十二边形", "PE.Controllers.Main.txtShape_donut": "环形", "PE.Controllers.Main.txtShape_doubleWave": "双波形", "PE.Controllers.Main.txtShape_downArrow": "下箭头", "PE.Controllers.Main.txtShape_downArrowCallout": "下箭头标注", "PE.Controllers.Main.txtShape_ellipse": "椭圆", "PE.Controllers.Main.txtShape_ellipseRibbon": "下凸弯带形", "PE.Controllers.Main.txtShape_ellipseRibbon2": "上凸弯带形", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程图：可选流程", "PE.Controllers.Main.txtShape_flowChartCollate": "流程图：对照", "PE.Controllers.Main.txtShape_flowChartConnector": "流程图：接点", "PE.Controllers.Main.txtShape_flowChartDecision": "流程图：决策", "PE.Controllers.Main.txtShape_flowChartDelay": "流程图：延迟", "PE.Controllers.Main.txtShape_flowChartDisplay": "流程图：展示", "PE.Controllers.Main.txtShape_flowChartDocument": "流程图：文档", "PE.Controllers.Main.txtShape_flowChartExtract": "流程图：摘录", "PE.Controllers.Main.txtShape_flowChartInputOutput": "流程图：数据", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "流程图：内部贮存", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程图：磁盘", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程图：直接访问存储器", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "流程图：顺序访问存储器", "PE.Controllers.Main.txtShape_flowChartManualInput": "流程图：手动输入", "PE.Controllers.Main.txtShape_flowChartManualOperation": "流程图：手动操作", "PE.Controllers.Main.txtShape_flowChartMerge": "流程图：合并", "PE.Controllers.Main.txtShape_flowChartMultidocument": "流程图：多文档 ", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程图：离页连接符", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程图：存储数据", "PE.Controllers.Main.txtShape_flowChartOr": "流程图：或", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程图：预定义过程", "PE.Controllers.Main.txtShape_flowChartPreparation": "流程图：准备", "PE.Controllers.Main.txtShape_flowChartProcess": "流程图：过程", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "流程图：卡片", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "流程图：资料带", "PE.Controllers.Main.txtShape_flowChartSort": "流程图：分类", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "流程图：汇总连接", "PE.Controllers.Main.txtShape_flowChartTerminator": "流程图：终止", "PE.Controllers.Main.txtShape_foldedCorner": "折角形", "PE.Controllers.Main.txtShape_frame": "框架", "PE.Controllers.Main.txtShape_halfFrame": "半闭框", "PE.Controllers.Main.txtShape_heart": "心形", "PE.Controllers.Main.txtShape_heptagon": "七边形", "PE.Controllers.Main.txtShape_hexagon": "六边形", "PE.Controllers.Main.txtShape_homePlate": "五边形", "PE.Controllers.Main.txtShape_horizontalScroll": "横卷形", "PE.Controllers.Main.txtShape_irregularSeal1": "爆炸形1", "PE.Controllers.Main.txtShape_irregularSeal2": "爆炸形2", "PE.Controllers.Main.txtShape_leftArrow": "左箭头", "PE.Controllers.Main.txtShape_leftArrowCallout": "左箭头标注", "PE.Controllers.Main.txtShape_leftBrace": "左括号", "PE.Controllers.Main.txtShape_leftBracket": "左中括号", "PE.Controllers.Main.txtShape_leftRightArrow": "左右箭头", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭头标注", "PE.Controllers.Main.txtShape_leftRightUpArrow": "丁字箭头", "PE.Controllers.Main.txtShape_leftUpArrow": "左上箭头", "PE.Controllers.Main.txtShape_lightningBolt": "闪电形", "PE.Controllers.Main.txtShape_line": "线", "PE.Controllers.Main.txtShape_lineWithArrow": "箭头", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "双箭头", "PE.Controllers.Main.txtShape_mathDivide": "除", "PE.Controllers.Main.txtShape_mathEqual": "平等", "PE.Controllers.Main.txtShape_mathMinus": "减去", "PE.Controllers.Main.txtShape_mathMultiply": "乘", "PE.Controllers.Main.txtShape_mathNotEqual": "不等于", "PE.Controllers.Main.txtShape_mathPlus": "加", "PE.Controllers.Main.txtShape_moon": "月形", "PE.Controllers.Main.txtShape_noSmoking": "“NO”符号", "PE.Controllers.Main.txtShape_notchedRightArrow": "燕尾形箭头", "PE.Controllers.Main.txtShape_octagon": "八边形", "PE.Controllers.Main.txtShape_parallelogram": "平行四边形", "PE.Controllers.Main.txtShape_pentagon": "五边形", "PE.Controllers.Main.txtShape_pie": "饼图", "PE.Controllers.Main.txtShape_plaque": "符号", "PE.Controllers.Main.txtShape_plus": "加", "PE.Controllers.Main.txtShape_polyline1": "自由曲线", "PE.Controllers.Main.txtShape_polyline2": "任意多边形", "PE.Controllers.Main.txtShape_quadArrow": "十字箭头", "PE.Controllers.Main.txtShape_quadArrowCallout": "十字箭头标注", "PE.Controllers.Main.txtShape_rect": "矩形", "PE.Controllers.Main.txtShape_ribbon": "下凸带形", "PE.Controllers.Main.txtShape_ribbon2": "上凸带形", "PE.Controllers.Main.txtShape_rightArrow": "右箭头", "PE.Controllers.Main.txtShape_rightArrowCallout": "右箭头标注", "PE.Controllers.Main.txtShape_rightBrace": "右括号", "PE.Controllers.Main.txtShape_rightBracket": "右中括号", "PE.Controllers.Main.txtShape_round1Rect": "单圆角矩形", "PE.Controllers.Main.txtShape_round2DiagRect": "对角圆角矩形", "PE.Controllers.Main.txtShape_round2SameRect": "同侧圆角矩形", "PE.Controllers.Main.txtShape_roundRect": "圆角矩形", "PE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "PE.Controllers.Main.txtShape_smileyFace": "笑脸", "PE.Controllers.Main.txtShape_snip1Rect": "剪去单角的矩形", "PE.Controllers.Main.txtShape_snip2DiagRect": "剪去对角的矩形", "PE.Controllers.Main.txtShape_snip2SameRect": "剪去同侧角的矩形", "PE.Controllers.Main.txtShape_snipRoundRect": "剪去单圆角的矩形", "PE.Controllers.Main.txtShape_spline": "曲线", "PE.Controllers.Main.txtShape_star10": "十角星", "PE.Controllers.Main.txtShape_star12": "十二角星", "PE.Controllers.Main.txtShape_star16": "十六角星", "PE.Controllers.Main.txtShape_star24": "二十四角星", "PE.Controllers.Main.txtShape_star32": "三十二角星", "PE.Controllers.Main.txtShape_star4": "四角星", "PE.Controllers.Main.txtShape_star5": "五角星", "PE.Controllers.Main.txtShape_star6": "六角星", "PE.Controllers.Main.txtShape_star7": "七角星", "PE.Controllers.Main.txtShape_star8": "八角星", "PE.Controllers.Main.txtShape_stripedRightArrow": "虚尾箭头", "PE.Controllers.Main.txtShape_sun": "太阳形", "PE.Controllers.Main.txtShape_teardrop": "泪珠形", "PE.Controllers.Main.txtShape_textRect": "文本框", "PE.Controllers.Main.txtShape_trapezoid": "梯形", "PE.Controllers.Main.txtShape_triangle": "三角形", "PE.Controllers.Main.txtShape_upArrow": "上箭头", "PE.Controllers.Main.txtShape_upArrowCallout": "上箭头标注", "PE.Controllers.Main.txtShape_upDownArrow": "上下箭头", "PE.Controllers.Main.txtShape_uturnArrow": "手杖形箭头", "PE.Controllers.Main.txtShape_verticalScroll": "竖卷形", "PE.Controllers.Main.txtShape_wave": "波浪线", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "椭圆形标注", "PE.Controllers.Main.txtShape_wedgeRectCallout": "矩形标注", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圆角矩形标注", "PE.Controllers.Main.txtSldLtTBlank": "空白", "PE.Controllers.Main.txtSldLtTChart": "图表", "PE.Controllers.Main.txtSldLtTChartAndTx": "图表和文字", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "剪贴画和文字", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "剪贴画和垂直文本", "PE.Controllers.Main.txtSldLtTCust": "自定义", "PE.Controllers.Main.txtSldLtTDgm": "图", "PE.Controllers.Main.txtSldLtTFourObj": "四个对象", "PE.Controllers.Main.txtSldLtTMediaAndTx": "媒体和文字", "PE.Controllers.Main.txtSldLtTObj": "标题和对象", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "对象和两个对象", "PE.Controllers.Main.txtSldLtTObjAndTx": "对象和文本", "PE.Controllers.Main.txtSldLtTObjOnly": "目的", "PE.Controllers.Main.txtSldLtTObjOverTx": "对象在文本之上", "PE.Controllers.Main.txtSldLtTObjTx": "标题，对象和标题", "PE.Controllers.Main.txtSldLtTPicTx": "图片和标题", "PE.Controllers.Main.txtSldLtTSecHead": "段首", "PE.Controllers.Main.txtSldLtTTbl": "表格", "PE.Controllers.Main.txtSldLtTTitle": "标题", "PE.Controllers.Main.txtSldLtTTitleOnly": "只有标题", "PE.Controllers.Main.txtSldLtTTwoColTx": "两列文本", "PE.Controllers.Main.txtSldLtTTwoObj": "两个对象", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "两个对象和对象", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "两个对象和文本", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "文本上的两个对象", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "两个文本和两个对象", "PE.Controllers.Main.txtSldLtTTx": "文本", "PE.Controllers.Main.txtSldLtTTxAndChart": "文字和图表", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "文字和剪贴画", "PE.Controllers.Main.txtSldLtTTxAndMedia": "文本和媒体", "PE.Controllers.Main.txtSldLtTTxAndObj": "文本和对象", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "文本和两个对象", "PE.Controllers.Main.txtSldLtTTxOverObj": "文本对象", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "垂直标题和文字", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "垂直标题和文字在图表上", "PE.Controllers.Main.txtSldLtTVertTx": "垂直文本", "PE.Controllers.Main.txtSlideNumber": "幻灯片编号", "PE.Controllers.Main.txtSlideSubtitle": "幻灯片副标题", "PE.Controllers.Main.txtSlideText": "幻灯片文本", "PE.Controllers.Main.txtSlideTitle": "幻灯片标题", "PE.Controllers.Main.txtStarsRibbons": "星星和旗帜", "PE.Controllers.Main.txtTheme_basic": "基本的", "PE.Controllers.Main.txtTheme_blank": "空白", "PE.Controllers.Main.txtTheme_classic": "古典", "PE.Controllers.Main.txtTheme_corner": "角", "PE.Controllers.Main.txtTheme_dotted": "点划线", "PE.Controllers.Main.txtTheme_green": "绿色", "PE.Controllers.Main.txtTheme_green_leaf": "绿叶", "PE.Controllers.Main.txtTheme_lines": "线条", "PE.Controllers.Main.txtTheme_office": "公司地址", "PE.Controllers.Main.txtTheme_office_theme": "办公室主题", "PE.Controllers.Main.txtTheme_official": "官方", "PE.Controllers.Main.txtTheme_pixel": "像素", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "龟", "PE.Controllers.Main.txtXAxis": "X轴", "PE.Controllers.Main.txtYAxis": "Y轴", "PE.Controllers.Main.unknownErrorText": "未知错误", "PE.Controllers.Main.unsupportedBrowserErrorText": "你的浏览器不支持", "PE.Controllers.Main.uploadImageExtMessage": "未知图像格式", "PE.Controllers.Main.uploadImageFileCountMessage": "没有图片上传", "PE.Controllers.Main.uploadImageSizeMessage": "图像太大。最大的大小为25MB。", "PE.Controllers.Main.uploadImageTextText": "上传图片...", "PE.Controllers.Main.uploadImageTitleText": "图片上传中", "PE.Controllers.Main.waitText": "请稍候...", "PE.Controllers.Main.warnBrowserIE9": "该应用程序在IE9上的功能很差。使用IE10或更高版本", "PE.Controllers.Main.warnBrowserZoom": "您的浏览器当前缩放设置不完全支持。请按Ctrl + 0重设为默认缩放。", "PE.Controllers.Main.warnLicenseExceeded": "您已达到同时连接%1编辑器的限制。该文档将被打开仅供查看。<br>请联系您的管理员以了解更多。", "PE.Controllers.Main.warnLicenseExp": "您的许可证已过期。<br>请更新您的许可证并刷新页面。", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "授权过期<br>您不具备文件编辑功能的授权<br>请联系管理员。", "PE.Controllers.Main.warnLicenseLimitedRenewed": "授权需更新<br>您只有文件编辑功能的部分权限<br>请联系管理员以取得完整权限。", "PE.Controllers.Main.warnLicenseUsersExceeded": "您已达到%1编辑器的用户限制。请联系您的管理员以了解更多。", "PE.Controllers.Main.warnNoLicense": "您已达到同时连接%1编辑器的限制。该文档将被打开仅供查看。<br>请联系%1销售团队以了解个人升级条款。", "PE.Controllers.Main.warnNoLicenseUsers": "您已达到%1编辑器的用户限制。请联系%1销售团队以了解个人升级条款。", "PE.Controllers.Main.warnProcessRightsChange": "您被拒绝编辑文件的权限。", "PE.Controllers.Print.txtPrintRangeInvalid": "打印范围无效", "PE.Controllers.Print.txtPrintRangeSingleRange": "输入单个幻灯片编号或单个幻灯片范围（例如，5-12）。或者您可以打印为 PDF。", "PE.Controllers.Search.notcriticalErrorTitle": "警告", "PE.Controllers.Search.textNoTextFound": "您搜索的数据无法找到。请调整您的搜索选项。", "PE.Controllers.Search.textReplaceSkipped": "替换已完成。 {0}个符合结果被跳过。", "PE.Controllers.Search.textReplaceSuccess": "搜寻已完成。 {0}个符合结果已被取代", "PE.Controllers.Search.warnReplaceString": "{0}不是有效的规范", "PE.Controllers.Statusbar.textDisconnect": "<b>连接失败</b><br>正在尝试连接。请检查连接设置。", "PE.Controllers.Statusbar.zoomText": "缩放％{0}", "PE.Controllers.Toolbar.confirmAddFontName": "您要保存的字体在当前设备上不可用。<br>使用其中一种系统字体显示文本样式，当可用时将使用保存的字体。<br>是否要继续？", "PE.Controllers.Toolbar.textAccent": "导数符号", "PE.Controllers.Toolbar.textBracket": "括号", "PE.Controllers.Toolbar.textEmptyImgUrl": "您需要指定图像URL。", "PE.Controllers.Toolbar.textFontSizeErr": "输入的值不正确。<br>请输入1到300之间的数值", "PE.Controllers.Toolbar.textFraction": "分数", "PE.Controllers.Toolbar.textFunction": "三角函数", "PE.Controllers.Toolbar.textInsert": "插入", "PE.Controllers.Toolbar.textIntegral": "积分", "PE.Controllers.Toolbar.textLargeOperator": "大型运算符", "PE.Controllers.Toolbar.textLimitAndLog": "极限和对数", "PE.Controllers.Toolbar.textMatrix": "矩阵", "PE.Controllers.Toolbar.textOperator": "运算符", "PE.Controllers.Toolbar.textRadical": "根式", "PE.Controllers.Toolbar.textScript": "上下标", "PE.Controllers.Toolbar.textSymbols": "符号", "PE.Controllers.Toolbar.textWarning": "警告", "PE.Controllers.Toolbar.txtAccent_Accent": "急性", "PE.Controllers.Toolbar.txtAccent_ArrowD": "右上方的箭头在上方", "PE.Controllers.Toolbar.txtAccent_ArrowL": "向左箭头", "PE.Controllers.Toolbar.txtAccent_ArrowR": "向右箭头上方", "PE.Controllers.Toolbar.txtAccent_Bar": "条", "PE.Controllers.Toolbar.txtAccent_BarBot": "下划线", "PE.Controllers.Toolbar.txtAccent_BarTop": "划线", "PE.Controllers.Toolbar.txtAccent_BorderBox": "盒装配方（带占位符)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "盒装配方（示例）", "PE.Controllers.Toolbar.txtAccent_Check": "检查", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "过度", "PE.Controllers.Toolbar.txtAccent_Custom_1": "A的矢量", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC带横杠", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y With Overbar", "PE.Controllers.Toolbar.txtAccent_DDDot": "三个点", "PE.Controllers.Toolbar.txtAccent_DDot": "双点", "PE.Controllers.Toolbar.txtAccent_Dot": "点", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "双重横杠", "PE.Controllers.Toolbar.txtAccent_Grave": "严重", "PE.Controllers.Toolbar.txtAccent_GroupBot": "分组字符下面", "PE.Controllers.Toolbar.txtAccent_GroupTop": "分组字符以上", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "向左鱼叉以上", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "向右上方的鱼叉", "PE.Controllers.Toolbar.txtAccent_Hat": "帽子", "PE.Controllers.Toolbar.txtAccent_Smile": "短音符", "PE.Controllers.Toolbar.txtAccent_Tilde": "波浪号", "PE.Controllers.Toolbar.txtBracket_Angle": "括号", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "带分隔符的方括号", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "带分隔符的方括号", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_Curve": "括号", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "带分隔符的方括号", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_Custom_1": "案件（两个条件）", "PE.Controllers.Toolbar.txtBracket_Custom_2": "案件（三个条件）", "PE.Controllers.Toolbar.txtBracket_Custom_3": "堆栈对象", "PE.Controllers.Toolbar.txtBracket_Custom_4": "堆栈对象", "PE.Controllers.Toolbar.txtBracket_Custom_5": "案例示例", "PE.Controllers.Toolbar.txtBracket_Custom_6": "二项系数", "PE.Controllers.Toolbar.txtBracket_Custom_7": "二项系数", "PE.Controllers.Toolbar.txtBracket_Line": "括号", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_LineDouble": "括号", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_LowLim": "括号", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "单括号", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_Round": "括号", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "带分隔符的方括号", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_Square": "括号", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括号", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括号", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括号", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "括号", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "单括号", "PE.Controllers.Toolbar.txtBracket_UppLim": "括号", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "单括号", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "单括号", "PE.Controllers.Toolbar.txtFractionDiagonal": "倾斜分数", "PE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "PE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "PE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "PE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "PE.Controllers.Toolbar.txtFractionHorizontal": "线性分数", "PE.Controllers.Toolbar.txtFractionPi_2": "π/ 2", "PE.Controllers.Toolbar.txtFractionSmall": "小分数", "PE.Controllers.Toolbar.txtFractionVertical": "堆积分数", "PE.Controllers.Toolbar.txtFunction_1_Cos": "反余弦", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "双曲反余弦函数", "PE.Controllers.Toolbar.txtFunction_1_Cot": "反余切", "PE.Controllers.Toolbar.txtFunction_1_Coth": "双曲反正弦函数", "PE.Controllers.Toolbar.txtFunction_1_Csc": "反余割", "PE.Controllers.Toolbar.txtFunction_1_Csch": "双曲逆乘功能", "PE.Controllers.Toolbar.txtFunction_1_Sec": "反正割", "PE.Controllers.Toolbar.txtFunction_1_Sech": "双曲线逆分割函数", "PE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲反正弦函数", "PE.Controllers.Toolbar.txtFunction_1_Tan": "反切线", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲反正切函数", "PE.Controllers.Toolbar.txtFunction_Cos": "余弦", "PE.Controllers.Toolbar.txtFunction_Cosh": "双曲余弦函数", "PE.Controllers.Toolbar.txtFunction_Cot": "余切", "PE.Controllers.Toolbar.txtFunction_Coth": "双曲正交函数", "PE.Controllers.Toolbar.txtFunction_Csc": "余割", "PE.Controllers.Toolbar.txtFunction_Csch": "双曲余弦函数", "PE.Controllers.Toolbar.txtFunction_Custom_1": "正弦θ", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "切线公式", "PE.Controllers.Toolbar.txtFunction_Sec": "正割", "PE.Controllers.Toolbar.txtFunction_Sech": "双曲正割函数", "PE.Controllers.Toolbar.txtFunction_Sin": "正弦", "PE.Controllers.Toolbar.txtFunction_Sinh": "双曲正弦函数", "PE.Controllers.Toolbar.txtFunction_Tan": "切线", "PE.Controllers.Toolbar.txtFunction_Tanh": "双曲正切函数", "PE.Controllers.Toolbar.txtIntegral": "积分", "PE.Controllers.Toolbar.txtIntegral_dtheta": "差分θ", "PE.Controllers.Toolbar.txtIntegral_dx": "差分x", "PE.Controllers.Toolbar.txtIntegral_dy": "差分y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "积分", "PE.Controllers.Toolbar.txtIntegralDouble": "双积分", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "双积分", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "双积分", "PE.Controllers.Toolbar.txtIntegralOriented": "轮廓积分", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "轮廓积分", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "曲面积分", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "曲面积分", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "曲面积分", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "轮廓积分", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "体积积分", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "体积积分", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "体积积分", "PE.Controllers.Toolbar.txtIntegralSubSup": "积分", "PE.Controllers.Toolbar.txtIntegralTriple": "三重积分", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重积分", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重积分", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "楔", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "楔", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "副积", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "副积", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "副积", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "副积", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "副积", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "乘积", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "并集", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "三角", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "三角", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "三角", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "三角", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "三角", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "交集", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交集", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交集", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交集", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交集", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "乘积", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "乘积", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "乘积", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "乘积", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "乘积", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "合计", "PE.Controllers.Toolbar.txtLargeOperator_Union": "并集", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "并集", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "并集", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "并集", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "并集", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "极限示例", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大范例", "PE.Controllers.Toolbar.txtLimitLog_Lim": "极限", "PE.Controllers.Toolbar.txtLimitLog_Ln": "自然对数", "PE.Controllers.Toolbar.txtLimitLog_Log": "对数", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "对数", "PE.Controllers.Toolbar.txtLimitLog_Max": "最大值", "PE.Controllers.Toolbar.txtLimitLog_Min": "最小值", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2空矩阵", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3空矩阵", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1空矩阵", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2空矩阵", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "括号空矩阵", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "括号空矩阵", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "括号空矩阵", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "括号空矩阵", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3空矩阵", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1空矩阵", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2空矩阵", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3空矩阵", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基线点", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "中线点", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "对角点", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直点", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "稀疏矩阵", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "稀疏矩阵", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2单位矩阵", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3单位矩阵", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3单位矩阵", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3单位矩阵", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "右左箭头下面", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "右上方的箭头在上方", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "向左箭头", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "向左箭头", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下面箭头向右", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "向右箭头上方", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "冒号相等", "PE.Controllers.Toolbar.txtOperator_Custom_1": "统一", "PE.Controllers.Toolbar.txtOperator_Custom_2": "三角形区域", "PE.Controllers.Toolbar.txtOperator_Definition": "相等于定义", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "数据相当于", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "右左箭头下面", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "右上方的箭头在上方", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "向左箭头", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "向左箭头", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下面箭头向右", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "向右箭头上方", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "等于等于", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "减号相等", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "加上等于", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "被测量", "PE.Controllers.Toolbar.txtRadicalCustom_1": "部首", "PE.Controllers.Toolbar.txtRadicalCustom_2": "部首", "PE.Controllers.Toolbar.txtRadicalRoot_2": "平方根", "PE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "PE.Controllers.Toolbar.txtRadicalRoot_n": "带有次数的根式", "PE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "PE.Controllers.Toolbar.txtScriptCustom_1": "脚本", "PE.Controllers.Toolbar.txtScriptCustom_2": "脚本", "PE.Controllers.Toolbar.txtScriptCustom_3": "脚本", "PE.Controllers.Toolbar.txtScriptCustom_4": "脚本", "PE.Controllers.Toolbar.txtScriptSub": "下标", "PE.Controllers.Toolbar.txtScriptSubSup": "下标", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "左标-标注", "PE.Controllers.Toolbar.txtScriptSup": "上标", "PE.Controllers.Toolbar.txtSymbol_about": "大约", "PE.Controllers.Toolbar.txtSymbol_additional": "补集", "PE.Controllers.Toolbar.txtSymbol_aleph": "阿列夫", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "PE.Controllers.Toolbar.txtSymbol_approx": "几乎相等", "PE.Controllers.Toolbar.txtSymbol_ast": "星号运算符", "PE.Controllers.Toolbar.txtSymbol_beta": "测试版", "PE.Controllers.Toolbar.txtSymbol_beth": "打赌", "PE.Controllers.Toolbar.txtSymbol_bullet": "加重号运算符", "PE.Controllers.Toolbar.txtSymbol_cap": "交集", "PE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "PE.Controllers.Toolbar.txtSymbol_cdots": "中线水平省略号", "PE.Controllers.Toolbar.txtSymbol_celsius": "摄氏度", "PE.Controllers.Toolbar.txtSymbol_chi": "驰", "PE.Controllers.Toolbar.txtSymbol_cong": "大致相等", "PE.Controllers.Toolbar.txtSymbol_cup": "并集", "PE.Controllers.Toolbar.txtSymbol_ddots": "向右对角线省略号", "PE.Controllers.Toolbar.txtSymbol_degree": "度", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "除号", "PE.Controllers.Toolbar.txtSymbol_downarrow": "向下箭头", "PE.Controllers.Toolbar.txtSymbol_emptyset": "空集", "PE.Controllers.Toolbar.txtSymbol_epsilon": "小量", "PE.Controllers.Toolbar.txtSymbol_equals": "平等", "PE.Controllers.Toolbar.txtSymbol_equiv": "相同", "PE.Controllers.Toolbar.txtSymbol_eta": "和", "PE.Controllers.Toolbar.txtSymbol_exists": "有存在", "PE.Controllers.Toolbar.txtSymbol_factorial": "阶乘", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "华氏度", "PE.Controllers.Toolbar.txtSymbol_forall": "全部", "PE.Controllers.Toolbar.txtSymbol_gamma": "伽马", "PE.Controllers.Toolbar.txtSymbol_geq": "大于或等于", "PE.Controllers.Toolbar.txtSymbol_gg": "远大于", "PE.Controllers.Toolbar.txtSymbol_greater": "大于", "PE.Controllers.Toolbar.txtSymbol_in": "元素", "PE.Controllers.Toolbar.txtSymbol_inc": "增量", "PE.Controllers.Toolbar.txtSymbol_infinity": "无限", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭头", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭头", "PE.Controllers.Toolbar.txtSymbol_leq": "小于或等于", "PE.Controllers.Toolbar.txtSymbol_less": "小于", "PE.Controllers.Toolbar.txtSymbol_ll": "远小于", "PE.Controllers.Toolbar.txtSymbol_minus": "减去", "PE.Controllers.Toolbar.txtSymbol_mp": "减号加", "PE.Controllers.Toolbar.txtSymbol_mu": "亩", "PE.Controllers.Toolbar.txtSymbol_nabla": "微分算符", "PE.Controllers.Toolbar.txtSymbol_neq": "不等于", "PE.Controllers.Toolbar.txtSymbol_ni": "包含作为成员", "PE.Controllers.Toolbar.txtSymbol_not": "未签名", "PE.Controllers.Toolbar.txtSymbol_notexists": "没有存在", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "奥米克", "PE.Controllers.Toolbar.txtSymbol_omega": "欧米茄", "PE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "PE.Controllers.Toolbar.txtSymbol_percent": "百分比", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "加", "PE.Controllers.Toolbar.txtSymbol_pm": "加减", "PE.Controllers.Toolbar.txtSymbol_propto": "成比例", "PE.Controllers.Toolbar.txtSymbol_psi": "PSI", "PE.Controllers.Toolbar.txtSymbol_qdrt": "第四根", "PE.Controllers.Toolbar.txtSymbol_qed": "审校结束", "PE.Controllers.Toolbar.txtSymbol_rddots": "向右对角线省略号", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭头", "PE.Controllers.Toolbar.txtSymbol_sigma": "适马", "PE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "PE.Controllers.Toolbar.txtSymbol_tau": "战斗", "PE.Controllers.Toolbar.txtSymbol_therefore": "因此", "PE.Controllers.Toolbar.txtSymbol_theta": "西塔", "PE.Controllers.Toolbar.txtSymbol_times": "乘法符号", "PE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭头", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon变体", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi 变量", "PE.Controllers.Toolbar.txtSymbol_varpi": "π变量", "PE.Controllers.Toolbar.txtSymbol_varrho": "Rho 变量", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma变量", "PE.Controllers.Toolbar.txtSymbol_vartheta": "西塔", "PE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略号", "PE.Controllers.Toolbar.txtSymbol_xsi": "粒子", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "适合幻灯片", "PE.Controllers.Viewport.textFitWidth": "适合宽度", "PE.Views.Animation.str0_5": "0.5秒（非常快）", "PE.Views.Animation.str1": "1秒（快）", "PE.Views.Animation.str2": "2秒（中）", "PE.Views.Animation.str20": "20秒（极慢）", "PE.Views.Animation.str3": "3秒（慢）", "PE.Views.Animation.str5": "5秒（非常慢）", "PE.Views.Animation.strDelay": "延迟", "PE.Views.Animation.strDuration": "持续时间", "PE.Views.Animation.strRepeat": "重复", "PE.Views.Animation.strRewind": "后退", "PE.Views.Animation.strStart": "开始", "PE.Views.Animation.strTrigger": "触发器", "PE.Views.Animation.textAutoPreview": "自动预览", "PE.Views.Animation.textMoreEffects": "显示其他效果", "PE.Views.Animation.textMoveEarlier": "向前移动", "PE.Views.Animation.textMoveLater": "向后移动", "PE.Views.Animation.textMultiple": "多个", "PE.Views.Animation.textNone": "无", "PE.Views.Animation.textNoRepeat": "（无）", "PE.Views.Animation.textOnClickOf": "单击:", "PE.Views.Animation.textOnClickSequence": "在单击序列中", "PE.Views.Animation.textStartAfterPrevious": "上一动画之后", "PE.Views.Animation.textStartOnClick": "单击", "PE.Views.Animation.textStartWithPrevious": "与上一动画同时", "PE.Views.Animation.textUntilEndOfSlide": "直到幻灯片末尾", "PE.Views.Animation.textUntilNextClick": "直到下一次单击", "PE.Views.Animation.txtAddEffect": "添加动画", "PE.Views.Animation.txtAnimationPane": "动画窗格", "PE.Views.Animation.txtParameters": "参数", "PE.Views.Animation.txtPreview": "预览", "PE.Views.Animation.txtSec": "秒", "PE.Views.AnimationDialog.textPreviewEffect": "预览效果", "PE.Views.AnimationDialog.textTitle": "其他效果", "PE.Views.ChartSettings.text3dDepth": "深度(原始深度百分比)", "PE.Views.ChartSettings.text3dHeight": "高度(原始高度百分比)", "PE.Views.ChartSettings.text3dRotation": "三维旋转", "PE.Views.ChartSettings.textAdvanced": "显示高级设置", "PE.Views.ChartSettings.textAutoscale": "自动缩放", "PE.Views.ChartSettings.textChartType": "更改图表类型", "PE.Views.ChartSettings.textDefault": "默认旋转", "PE.Views.ChartSettings.textDown": "向下", "PE.Views.ChartSettings.textEditData": "编辑数据", "PE.Views.ChartSettings.textHeight": "高度", "PE.Views.ChartSettings.textKeepRatio": "不变比例", "PE.Views.ChartSettings.textLeft": "向左", "PE.Views.ChartSettings.textNarrow": "缩小视野", "PE.Views.ChartSettings.textPerspective": "透视", "PE.Views.ChartSettings.textRight": "向右", "PE.Views.ChartSettings.textRightAngle": "直角坐标轴", "PE.Views.ChartSettings.textSize": "大小", "PE.Views.ChartSettings.textStyle": "类型", "PE.Views.ChartSettings.textUp": "向上", "PE.Views.ChartSettings.textWiden": "扩大视野", "PE.Views.ChartSettings.textWidth": "宽度", "PE.Views.ChartSettings.textX": "X 旋转", "PE.Views.ChartSettings.textY": "Y 旋转", "PE.Views.ChartSettingsAdvanced.textAlt": "可选文本", "PE.Views.ChartSettingsAdvanced.textAltDescription": "描述", "PE.Views.ChartSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "PE.Views.ChartSettingsAdvanced.textAltTitle": "标题", "PE.Views.ChartSettingsAdvanced.textCenter": "中心", "PE.Views.ChartSettingsAdvanced.textFrom": "从", "PE.Views.ChartSettingsAdvanced.textHeight": "高度", "PE.Views.ChartSettingsAdvanced.textHorizontal": "水平", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "不变比例", "PE.Views.ChartSettingsAdvanced.textPlacement": "放置", "PE.Views.ChartSettingsAdvanced.textPosition": "位置", "PE.Views.ChartSettingsAdvanced.textSize": "大小", "PE.Views.ChartSettingsAdvanced.textTitle": "图 - 高级设置", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.ChartSettingsAdvanced.textVertical": "垂直", "PE.Views.ChartSettingsAdvanced.textWidth": "宽度", "PE.Views.DateTimeDialog.confirmDefault": "设置{0}：{1}的默认格式", "PE.Views.DateTimeDialog.textDefault": "设为默认值", "PE.Views.DateTimeDialog.textFormat": "格式", "PE.Views.DateTimeDialog.textLang": "语言", "PE.Views.DateTimeDialog.textUpdate": "自动更新", "PE.Views.DateTimeDialog.txtTitle": "日期和时间", "PE.Views.DocumentHolder.aboveText": "以上", "PE.Views.DocumentHolder.addCommentText": "添加批注", "PE.Views.DocumentHolder.addToLayoutText": "添加到布局", "PE.Views.DocumentHolder.advancedChartText": "图表高级设置", "PE.Views.DocumentHolder.advancedEquationText": "方程式设置", "PE.Views.DocumentHolder.advancedImageText": "高级图像设置", "PE.Views.DocumentHolder.advancedParagraphText": "段落高级设置", "PE.Views.DocumentHolder.advancedShapeText": "形状高级设置", "PE.Views.DocumentHolder.advancedTableText": "高级表设置", "PE.Views.DocumentHolder.alignmentText": "对齐", "PE.Views.DocumentHolder.allLinearText": "全部 - 线性", "PE.Views.DocumentHolder.allProfText": "全部 - 专业", "PE.Views.DocumentHolder.belowText": "下面", "PE.Views.DocumentHolder.cellAlignText": "单元格垂直对齐", "PE.Views.DocumentHolder.cellText": "元件", "PE.Views.DocumentHolder.centerText": "中心", "PE.Views.DocumentHolder.columnText": "列", "PE.Views.DocumentHolder.currLinearText": "当前 - 线性", "PE.Views.DocumentHolder.currProfText": "当前 - 专业", "PE.Views.DocumentHolder.deleteColumnText": "删除列", "PE.Views.DocumentHolder.deleteRowText": "删除行", "PE.Views.DocumentHolder.deleteTableText": "删除表", "PE.Views.DocumentHolder.deleteText": "删除", "PE.Views.DocumentHolder.direct270Text": "旋转270°", "PE.Views.DocumentHolder.direct90Text": "旋转90°", "PE.Views.DocumentHolder.directHText": "水平的", "PE.Views.DocumentHolder.directionText": "文字方向", "PE.Views.DocumentHolder.editChartText": "编辑数据", "PE.Views.DocumentHolder.editHyperlinkText": "编辑超链接", "PE.Views.DocumentHolder.hyperlinkText": "超链接", "PE.Views.DocumentHolder.ignoreAllSpellText": "忽略所有", "PE.Views.DocumentHolder.ignoreSpellText": "忽视", "PE.Views.DocumentHolder.insertColumnLeftText": "列左", "PE.Views.DocumentHolder.insertColumnRightText": "右列", "PE.Views.DocumentHolder.insertColumnText": "插入列", "PE.Views.DocumentHolder.insertRowAboveText": "上面的行", "PE.Views.DocumentHolder.insertRowBelowText": "下面的行", "PE.Views.DocumentHolder.insertRowText": "插入行", "PE.Views.DocumentHolder.insertText": "插入", "PE.Views.DocumentHolder.langText": "选择语言", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "左", "PE.Views.DocumentHolder.loadSpellText": "加载变体...", "PE.Views.DocumentHolder.mergeCellsText": "合并单元格", "PE.Views.DocumentHolder.mniCustomTable": "插入自定义表", "PE.Views.DocumentHolder.moreText": "更多变体...", "PE.Views.DocumentHolder.noSpellVariantsText": "没有变量", "PE.Views.DocumentHolder.originalSizeText": "实际大小", "PE.Views.DocumentHolder.removeHyperlinkText": "删除超链接", "PE.Views.DocumentHolder.rightText": "右侧", "PE.Views.DocumentHolder.rowText": "行", "PE.Views.DocumentHolder.selectText": "请选择", "PE.Views.DocumentHolder.spellcheckText": "拼写检查", "PE.Views.DocumentHolder.splitCellsText": "拆分单元格", "PE.Views.DocumentHolder.splitCellTitleText": "拆分单元格", "PE.Views.DocumentHolder.tableText": "表格", "PE.Views.DocumentHolder.textAddHGuides": "添加水平参考线", "PE.Views.DocumentHolder.textAddVGuides": "添加垂直参考线", "PE.Views.DocumentHolder.textArrangeBack": "放到最下面", "PE.Views.DocumentHolder.textArrangeBackward": "下移一层", "PE.Views.DocumentHolder.textArrangeForward": "上移一层", "PE.Views.DocumentHolder.textArrangeFront": "放到最上面", "PE.Views.DocumentHolder.textClearGuides": "清除参考线", "PE.Views.DocumentHolder.textCm": "厘米", "PE.Views.DocumentHolder.textCopy": "复制", "PE.Views.DocumentHolder.textCrop": "裁剪", "PE.Views.DocumentHolder.textCropFill": "填满", "PE.Views.DocumentHolder.textCropFit": "最佳", "PE.Views.DocumentHolder.textCustom": "自定义", "PE.Views.DocumentHolder.textCut": "剪切", "PE.Views.DocumentHolder.textDeleteGuide": "删除参考线", "PE.Views.DocumentHolder.textDistributeCols": "分布列", "PE.Views.DocumentHolder.textDistributeRows": "分布行", "PE.Views.DocumentHolder.textEditPoints": "编辑点", "PE.Views.DocumentHolder.textFlipH": "水平翻转", "PE.Views.DocumentHolder.textFlipV": "垂直翻转", "PE.Views.DocumentHolder.textFromFile": "从文件导入", "PE.Views.DocumentHolder.textFromStorage": "来自存储设备", "PE.Views.DocumentHolder.textFromUrl": "从URL", "PE.Views.DocumentHolder.textGridlines": "网格线", "PE.Views.DocumentHolder.textGuides": "参考线", "PE.Views.DocumentHolder.textNextPage": "下一张幻灯片", "PE.Views.DocumentHolder.textPaste": "粘贴", "PE.Views.DocumentHolder.textPrevPage": "上一张幻灯片", "PE.Views.DocumentHolder.textReplace": "替换图像", "PE.Views.DocumentHolder.textRotate": "旋转", "PE.Views.DocumentHolder.textRotate270": "逆时针旋转90°", "PE.Views.DocumentHolder.textRotate90": "顺时针旋转90°", "PE.Views.DocumentHolder.textRulers": "标尺", "PE.Views.DocumentHolder.textSaveAsPicture": "另存为图片", "PE.Views.DocumentHolder.textShapeAlignBottom": "靠下对齐", "PE.Views.DocumentHolder.textShapeAlignCenter": "居中对齐", "PE.Views.DocumentHolder.textShapeAlignLeft": "左对齐", "PE.Views.DocumentHolder.textShapeAlignMiddle": "居中对齐", "PE.Views.DocumentHolder.textShapeAlignRight": "右对齐", "PE.Views.DocumentHolder.textShapeAlignTop": "靠上对齐", "PE.Views.DocumentHolder.textShowGridlines": "显示网格线", "PE.Views.DocumentHolder.textShowGuides": "显示参考线", "PE.Views.DocumentHolder.textSlideSettings": "幻灯片设置", "PE.Views.DocumentHolder.textSmartGuides": "智能参考线", "PE.Views.DocumentHolder.textSnapObjects": "将对象对齐网格", "PE.Views.DocumentHolder.textUndo": "复原", "PE.Views.DocumentHolder.tipGuides": "显示参考线", "PE.Views.DocumentHolder.tipIsLocked": "此元素正在由其他用户编辑。", "PE.Views.DocumentHolder.toDictionaryText": "添加到词典", "PE.Views.DocumentHolder.txtAddBottom": "添加底部边框", "PE.Views.DocumentHolder.txtAddFractionBar": "添加分数栏", "PE.Views.DocumentHolder.txtAddHor": "在相同样式的段落之间添加间隔", "PE.Views.DocumentHolder.txtAddLB": "添加左下线", "PE.Views.DocumentHolder.txtAddLeft": "添加左边框", "PE.Views.DocumentHolder.txtAddLT": "添加左上线", "PE.Views.DocumentHolder.txtAddRight": "添加右边框", "PE.Views.DocumentHolder.txtAddTop": "添加顶部边框", "PE.Views.DocumentHolder.txtAddVer": "添加垂直线", "PE.Views.DocumentHolder.txtAlign": "对齐", "PE.Views.DocumentHolder.txtAlignToChar": "字符对齐", "PE.Views.DocumentHolder.txtArrange": "安排", "PE.Views.DocumentHolder.txtBackground": "背景", "PE.Views.DocumentHolder.txtBorderProps": "边框属性", "PE.Views.DocumentHolder.txtBottom": "底部", "PE.Views.DocumentHolder.txtChangeLayout": "更改布局", "PE.Views.DocumentHolder.txtChangeTheme": "更改主题", "PE.Views.DocumentHolder.txtColumnAlign": "列对齐", "PE.Views.DocumentHolder.txtDecreaseArg": "减少大小参数", "PE.Views.DocumentHolder.txtDeleteArg": "删除参数", "PE.Views.DocumentHolder.txtDeleteBreak": "删除手动的断点", "PE.Views.DocumentHolder.txtDeleteChars": "删除包围字符", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "删除封闭字符和分隔符", "PE.Views.DocumentHolder.txtDeleteEq": "删除方程", "PE.Views.DocumentHolder.txtDeleteGroupChar": "删除字符", "PE.Views.DocumentHolder.txtDeleteRadical": "彻底删除", "PE.Views.DocumentHolder.txtDeleteSlide": "删除幻灯片", "PE.Views.DocumentHolder.txtDistribHor": "水平分布", "PE.Views.DocumentHolder.txtDistribVert": "垂直分布", "PE.Views.DocumentHolder.txtDuplicateSlide": "重复幻灯片", "PE.Views.DocumentHolder.txtFractionLinear": "改为线性分数", "PE.Views.DocumentHolder.txtFractionSkewed": "改为倾斜分数", "PE.Views.DocumentHolder.txtFractionStacked": "改为堆积分数", "PE.Views.DocumentHolder.txtGroup": "组合", "PE.Views.DocumentHolder.txtGroupCharOver": "字符在文字上", "PE.Views.DocumentHolder.txtGroupCharUnder": "文字下的Char", "PE.Views.DocumentHolder.txtHideBottom": "隐藏底部边框", "PE.Views.DocumentHolder.txtHideBottomLimit": "隐藏下限", "PE.Views.DocumentHolder.txtHideCloseBracket": "隐藏右括号", "PE.Views.DocumentHolder.txtHideDegree": "隐藏学位", "PE.Views.DocumentHolder.txtHideHor": "隐藏水平线", "PE.Views.DocumentHolder.txtHideLB": "隐藏左侧底线", "PE.Views.DocumentHolder.txtHideLeft": "隐藏左边界", "PE.Views.DocumentHolder.txtHideLT": "隐藏左顶线", "PE.Views.DocumentHolder.txtHideOpenBracket": "隐藏开放支架", "PE.Views.DocumentHolder.txtHidePlaceholder": "隐藏占位符", "PE.Views.DocumentHolder.txtHideRight": "隐藏右边框", "PE.Views.DocumentHolder.txtHideTop": "隐藏顶部边框", "PE.Views.DocumentHolder.txtHideTopLimit": "隐藏上限", "PE.Views.DocumentHolder.txtHideVer": "隐藏垂直线", "PE.Views.DocumentHolder.txtIncreaseArg": "增加参数大小", "PE.Views.DocumentHolder.txtInsertArgAfter": "插入参数", "PE.Views.DocumentHolder.txtInsertArgBefore": "之前插入参数", "PE.Views.DocumentHolder.txtInsertBreak": "插入手动中断", "PE.Views.DocumentHolder.txtInsertEqAfter": "插入方程后", "PE.Views.DocumentHolder.txtInsertEqBefore": "之前插入方程式", "PE.Views.DocumentHolder.txtKeepTextOnly": "仅保留文字", "PE.Views.DocumentHolder.txtLimitChange": "更改限制位置", "PE.Views.DocumentHolder.txtLimitOver": "限制文字", "PE.Views.DocumentHolder.txtLimitUnder": "在文本限制", "PE.Views.DocumentHolder.txtMatchBrackets": "匹配括号到参数高度", "PE.Views.DocumentHolder.txtMatrixAlign": "矩阵对齐", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "将幻灯片移至终点", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "将幻灯片移至开头", "PE.Views.DocumentHolder.txtNewSlide": "新幻灯片", "PE.Views.DocumentHolder.txtOverbar": "文本上一条", "PE.Views.DocumentHolder.txtPasteDestFormat": "使用目标主题", "PE.Views.DocumentHolder.txtPastePicture": "图片", "PE.Views.DocumentHolder.txtPasteSourceFormat": "保持源格式", "PE.Views.DocumentHolder.txtPressLink": "按{0}并单击链接", "PE.Views.DocumentHolder.txtPreview": "开始幻灯片放映", "PE.Views.DocumentHolder.txtPrintSelection": "打印选择", "PE.Views.DocumentHolder.txtRemFractionBar": "删除分数栏", "PE.Views.DocumentHolder.txtRemLimit": "删除限制", "PE.Views.DocumentHolder.txtRemoveAccentChar": "删除重音字符", "PE.Views.DocumentHolder.txtRemoveBar": "删除顶/底线", "PE.Views.DocumentHolder.txtRemScripts": "删除上下标", "PE.Views.DocumentHolder.txtRemSubscript": "删除标", "PE.Views.DocumentHolder.txtRemSuperscript": "除去上标", "PE.Views.DocumentHolder.txtResetLayout": "重置幻灯片", "PE.Views.DocumentHolder.txtScriptsAfter": "文字后的上下标", "PE.Views.DocumentHolder.txtScriptsBefore": "文字前的上下标", "PE.Views.DocumentHolder.txtSelectAll": "全选", "PE.Views.DocumentHolder.txtShowBottomLimit": "显示下限", "PE.Views.DocumentHolder.txtShowCloseBracket": "显示关闭括号", "PE.Views.DocumentHolder.txtShowDegree": "显示学位", "PE.Views.DocumentHolder.txtShowOpenBracket": "显示开放支架", "PE.Views.DocumentHolder.txtShowPlaceholder": "显示占位符", "PE.Views.DocumentHolder.txtShowTopLimit": "显示上限", "PE.Views.DocumentHolder.txtSlide": "滑动", "PE.Views.DocumentHolder.txtSlideHide": "隐藏幻灯片", "PE.Views.DocumentHolder.txtStretchBrackets": "拉伸支架", "PE.Views.DocumentHolder.txtTop": "顶部", "PE.Views.DocumentHolder.txtUnderbar": "在文本栏", "PE.Views.DocumentHolder.txtUngroup": "取消组合", "PE.Views.DocumentHolder.txtWarnUrl": "点击该链接可能会损害你的设备或数据。<br>你确定要继续吗？", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "垂直对齐", "PE.Views.DocumentPreview.goToSlideText": "转到幻灯片", "PE.Views.DocumentPreview.slideIndexText": "幻灯片第{0}张，共{1}张", "PE.Views.DocumentPreview.txtClose": "关闭幻灯片", "PE.Views.DocumentPreview.txtEndSlideshow": "结束幻灯片放映", "PE.Views.DocumentPreview.txtExitFullScreen": "退出全屏", "PE.Views.DocumentPreview.txtFinalMessage": "幻灯片预览的结束。单击退出。", "PE.Views.DocumentPreview.txtFullScreen": "全屏", "PE.Views.DocumentPreview.txtNext": "下一张幻灯片", "PE.Views.DocumentPreview.txtPageNumInvalid": "幻灯片编号无效", "PE.Views.DocumentPreview.txtPause": "暂停演示", "PE.Views.DocumentPreview.txtPlay": "开始演示", "PE.Views.DocumentPreview.txtPrev": "上一张幻灯片", "PE.Views.DocumentPreview.txtReset": "重置", "PE.Views.FileMenu.btnAboutCaption": "关于", "PE.Views.FileMenu.btnBackCaption": "打开文件所在位置", "PE.Views.FileMenu.btnCloseMenuCaption": "关闭菜单", "PE.Views.FileMenu.btnCreateNewCaption": "新建", "PE.Views.FileMenu.btnDownloadCaption": "下载为", "PE.Views.FileMenu.btnExitCaption": "关闭", "PE.Views.FileMenu.btnFileOpenCaption": "打开", "PE.Views.FileMenu.btnHelpCaption": "帮助", "PE.Views.FileMenu.btnHistoryCaption": "版本历史", "PE.Views.FileMenu.btnInfoCaption": "演示信息", "PE.Views.FileMenu.btnPrintCaption": "打印", "PE.Views.FileMenu.btnProtectCaption": "保护", "PE.Views.FileMenu.btnRecentFilesCaption": "打开最近", "PE.Views.FileMenu.btnRenameCaption": "改名", "PE.Views.FileMenu.btnReturnCaption": "回到演示", "PE.Views.FileMenu.btnRightsCaption": "访问权", "PE.Views.FileMenu.btnSaveAsCaption": "另存为", "PE.Views.FileMenu.btnSaveCaption": "保存", "PE.Views.FileMenu.btnSaveCopyAsCaption": "将副本另存为", "PE.Views.FileMenu.btnSettingsCaption": "高级设置", "PE.Views.FileMenu.btnToEditCaption": "编辑演示文稿", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "空白演示文稿", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新建", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "应用", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "添加作者", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "添加文本", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "应用", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "更改访问权限", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "评论", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已创建", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "上次修改时间", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上次修改时间", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "创建者", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "有权利的人", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主题", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "标签", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "标题", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "上载", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "更改访问权限", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "有权利的人", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "使用密码", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "保护演示文稿", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "使用签名", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "编辑演示文稿", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "编辑将删除演示文稿中的签名。<br>您确定要继续吗？", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此演示文稿受密码保护", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效签名已添加到演示文稿中。该演示文稿已限制编辑。", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "演示文稿中的某些数字签名无效或无法验证。该演示文稿已限制编辑。", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "查看签名", "PE.Views.FileMenuPanels.Settings.okButtonText": "应用", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同编辑模式", "PE.Views.FileMenuPanels.Settings.strFast": "自动", "PE.Views.FileMenuPanels.Settings.strFontRender": "字体微调方式", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "忽略大写单词", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "忽略带数字的单词", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "宏设置", "PE.Views.FileMenuPanels.Settings.strPasteButton": "在执行粘贴操作后显示“粘贴选项”按钮", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "显示其他用户的更改", "PE.Views.FileMenuPanels.Settings.strStrict": "手动", "PE.Views.FileMenuPanels.Settings.strTheme": "界面主題", "PE.Views.FileMenuPanels.Settings.strUnit": "测量单位", "PE.Views.FileMenuPanels.Settings.strZoom": "默认缩放比率", "PE.Views.FileMenuPanels.Settings.text10Minutes": "每10分钟", "PE.Views.FileMenuPanels.Settings.text30Minutes": "每30分钟", "PE.Views.FileMenuPanels.Settings.text5Minutes": "每5分钟", "PE.Views.FileMenuPanels.Settings.text60Minutes": "每隔一小时", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "对齐辅助线", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "自动恢复", "PE.Views.FileMenuPanels.Settings.textAutoSave": "自动保存", "PE.Views.FileMenuPanels.Settings.textDisabled": "已禁用", "PE.Views.FileMenuPanels.Settings.textForceSave": "在保存中间版本", "PE.Views.FileMenuPanels.Settings.textMinute": "每一分钟", "PE.Views.FileMenuPanels.Settings.txtAll": "查看全部", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "自动修正选项...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "默认缓存模式", "PE.Views.FileMenuPanels.Settings.txtCm": "厘米", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "协作", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "编辑并保存", "PE.Views.FileMenuPanels.Settings.txtFastTip": "实时协同编辑。所有", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "适合幻灯片", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "适合宽度", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "特殊符号", "PE.Views.FileMenuPanels.Settings.txtInch": "寸", "PE.Views.FileMenuPanels.Settings.txtLast": "最后查看", "PE.Views.FileMenuPanels.Settings.txtMac": "仿照 OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "本机系统原生", "PE.Views.FileMenuPanels.Settings.txtProofing": "校对", "PE.Views.FileMenuPanels.Settings.txtPt": "点", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "在编辑器标题中显示快速打印按钮", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "文件将用最新选定的打印机或默认的打印机被打印出来。", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "启动所有项目", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "启动所有不带通知的宏", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "拼写检查", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "解除所有项目", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "解除所有不带通知的宏", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "使用“保存\"按钮同步你和其他人的修改", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "使用键盘上的Alt键导航至用户界面", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "使用键盘上的Option键导航至用户界面", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "显示通知", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "解除所有带通知的宏", "PE.Views.FileMenuPanels.Settings.txtWin": "仿照 Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "工作空间", "PE.Views.GridSettings.textCm": "厘米", "PE.Views.GridSettings.textCustom": "自定义", "PE.Views.GridSettings.textSpacing": "间距", "PE.Views.GridSettings.textTitle": "网格设置", "PE.Views.HeaderFooterDialog.applyAllText": "全部应用", "PE.Views.HeaderFooterDialog.applyText": "应用", "PE.Views.HeaderFooterDialog.diffLanguage": "不能使用与幻灯片母版不同语言的日期格式。<br>若要更改母版，请单击“全部应用”而不是“应用”", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "警告", "PE.Views.HeaderFooterDialog.textDateTime": "日期和时间", "PE.Views.HeaderFooterDialog.textFixed": "固定", "PE.Views.HeaderFooterDialog.textFooter": "页脚中的文本", "PE.Views.HeaderFooterDialog.textFormat": "格式", "PE.Views.HeaderFooterDialog.textLang": "语言", "PE.Views.HeaderFooterDialog.textNotTitle": "不在标题幻灯片上显示", "PE.Views.HeaderFooterDialog.textPreview": "预览", "PE.Views.HeaderFooterDialog.textSlideNum": "幻灯片编号", "PE.Views.HeaderFooterDialog.textTitle": "页脚设置", "PE.Views.HeaderFooterDialog.textUpdate": "自动更新", "PE.Views.HyperlinkSettingsDialog.strDisplay": "展示", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "链接到", "PE.Views.HyperlinkSettingsDialog.textDefault": "所选文本片段", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "在这里输入标题", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "在这里输入链接", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "在这里输入工具提示", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "外部链接", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "幻灯片在本演示文稿", "PE.Views.HyperlinkSettingsDialog.textSlides": "幻灯片", "PE.Views.HyperlinkSettingsDialog.textTipText": "屏幕提示文字", "PE.Views.HyperlinkSettingsDialog.textTitle": "超链接设置", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "这是必填栏", "PE.Views.HyperlinkSettingsDialog.txtFirst": "第一张幻灯片", "PE.Views.HyperlinkSettingsDialog.txtLast": "最后一张幻灯片", "PE.Views.HyperlinkSettingsDialog.txtNext": "下一张幻灯片", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "PE.Views.HyperlinkSettingsDialog.txtPrev": "上一张幻灯片", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "该域字符限制为2803个。", "PE.Views.HyperlinkSettingsDialog.txtSlide": "滑动", "PE.Views.ImageSettings.textAdvanced": "显示高级设置", "PE.Views.ImageSettings.textCrop": "裁剪", "PE.Views.ImageSettings.textCropFill": "填满", "PE.Views.ImageSettings.textCropFit": "最佳", "PE.Views.ImageSettings.textCropToShape": "裁剪为形状", "PE.Views.ImageSettings.textEdit": "修改", "PE.Views.ImageSettings.textEditObject": "编辑对象", "PE.Views.ImageSettings.textFitSlide": "适合幻灯片", "PE.Views.ImageSettings.textFlip": "翻转", "PE.Views.ImageSettings.textFromFile": "从文件导入", "PE.Views.ImageSettings.textFromStorage": "来自存储设备", "PE.Views.ImageSettings.textFromUrl": "从URL", "PE.Views.ImageSettings.textHeight": "高低", "PE.Views.ImageSettings.textHint270": "逆时针旋转90°", "PE.Views.ImageSettings.textHint90": "顺时针旋转90°", "PE.Views.ImageSettings.textHintFlipH": "水平翻转", "PE.Views.ImageSettings.textHintFlipV": "垂直翻转", "PE.Views.ImageSettings.textInsert": "替换图像", "PE.Views.ImageSettings.textOriginalSize": "实际大小", "PE.Views.ImageSettings.textRecentlyUsed": "最近使用的", "PE.Views.ImageSettings.textRotate90": "旋转90°", "PE.Views.ImageSettings.textRotation": "旋转", "PE.Views.ImageSettings.textSize": "大小", "PE.Views.ImageSettings.textWidth": "宽度", "PE.Views.ImageSettingsAdvanced.textAlt": "替代文本", "PE.Views.ImageSettingsAdvanced.textAltDescription": "说明", "PE.Views.ImageSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "PE.Views.ImageSettingsAdvanced.textAltTitle": "标题", "PE.Views.ImageSettingsAdvanced.textAngle": "角度", "PE.Views.ImageSettingsAdvanced.textCenter": "中心", "PE.Views.ImageSettingsAdvanced.textFlipped": "已翻转", "PE.Views.ImageSettingsAdvanced.textFrom": "从", "PE.Views.ImageSettingsAdvanced.textHeight": "高度", "PE.Views.ImageSettingsAdvanced.textHorizontal": "水平", "PE.Views.ImageSettingsAdvanced.textHorizontally": "水平", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "不变比例", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "实际大小", "PE.Views.ImageSettingsAdvanced.textPlacement": "放置", "PE.Views.ImageSettingsAdvanced.textPosition": "位置", "PE.Views.ImageSettingsAdvanced.textRotation": "旋转", "PE.Views.ImageSettingsAdvanced.textSize": "大小", "PE.Views.ImageSettingsAdvanced.textTitle": "图片 - 高级设置", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.ImageSettingsAdvanced.textVertical": "垂直", "PE.Views.ImageSettingsAdvanced.textVertically": "垂直", "PE.Views.ImageSettingsAdvanced.textWidth": "宽度", "PE.Views.LeftMenu.tipAbout": "关于", "PE.Views.LeftMenu.tipChat": "聊天", "PE.Views.LeftMenu.tipComments": "评论", "PE.Views.LeftMenu.tipPlugins": "插件", "PE.Views.LeftMenu.tipSearch": "搜索", "PE.Views.LeftMenu.tipSlides": "幻灯片", "PE.Views.LeftMenu.tipSupport": "反馈和支持", "PE.Views.LeftMenu.tipTitles": "标题", "PE.Views.LeftMenu.txtDeveloper": "开发者模式", "PE.Views.LeftMenu.txtEditor": "表格编辑器", "PE.Views.LeftMenu.txtLimit": "限制访问", "PE.Views.LeftMenu.txtTrial": "试用模式", "PE.Views.LeftMenu.txtTrialDev": "试用开发者模式", "PE.Views.ParagraphSettings.strLineHeight": "行间距", "PE.Views.ParagraphSettings.strParagraphSpacing": "段落间距", "PE.Views.ParagraphSettings.strSpacingAfter": "段后", "PE.Views.ParagraphSettings.strSpacingBefore": "段前", "PE.Views.ParagraphSettings.textAdvanced": "显示高级设置", "PE.Views.ParagraphSettings.textAt": "在", "PE.Views.ParagraphSettings.textAtLeast": "至少", "PE.Views.ParagraphSettings.textAuto": "多", "PE.Views.ParagraphSettings.textExact": "精确", "PE.Views.ParagraphSettings.txtAutoText": "自动", "PE.Views.ParagraphSettingsAdvanced.noTabs": "指定的选项卡将显示在此字段中", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大写字母", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "双删除线", "PE.Views.ParagraphSettingsAdvanced.strIndent": "缩进", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行间距", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右侧", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "段后", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "段前", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊格式", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字体", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "缩进和间距", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型大写字母", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "间距", "PE.Views.ParagraphSettingsAdvanced.strStrike": "删除线", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "下标", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "上标", "PE.Views.ParagraphSettingsAdvanced.strTabs": "标签", "PE.Views.ParagraphSettingsAdvanced.textAlign": "对齐", "PE.Views.ParagraphSettingsAdvanced.textAuto": "倍数", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字符间距", "PE.Views.ParagraphSettingsAdvanced.textDefault": "默认选项", "PE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "PE.Views.ParagraphSettingsAdvanced.textExact": "精确", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "首行缩进", "PE.Views.ParagraphSettingsAdvanced.textHanging": "悬挂缩进", "PE.Views.ParagraphSettingsAdvanced.textJustified": "正当", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（无）", "PE.Views.ParagraphSettingsAdvanced.textRemove": "删除", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "删除所有", "PE.Views.ParagraphSettingsAdvanced.textSet": "指定", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "标签的位置", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "右侧", "PE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 高级设置", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "自动", "PE.Views.PrintWithPreview.txtAllPages": "所有幻灯片", "PE.Views.PrintWithPreview.txtCurrentPage": "当前幻灯片", "PE.Views.PrintWithPreview.txtCustomPages": "自定义打印", "PE.Views.PrintWithPreview.txtEmptyTable": "演示文稿不包含可以打印的内容，演示文稿为空", "PE.Views.PrintWithPreview.txtOf": "/ {0}", "PE.Views.PrintWithPreview.txtPage": "幻灯片", "PE.Views.PrintWithPreview.txtPageNumInvalid": "幻灯片编号无效", "PE.Views.PrintWithPreview.txtPages": "幻灯片", "PE.Views.PrintWithPreview.txtPaperSize": "纸张大小", "PE.Views.PrintWithPreview.txtPrint": "打印", "PE.Views.PrintWithPreview.txtPrintPdf": "打印为 PDF 格式", "PE.Views.PrintWithPreview.txtPrintRange": "打印范围", "PE.Views.RightMenu.txtChartSettings": "图表设置", "PE.Views.RightMenu.txtImageSettings": "图像设置", "PE.Views.RightMenu.txtParagraphSettings": "段落设置", "PE.Views.RightMenu.txtShapeSettings": "形状设置", "PE.Views.RightMenu.txtSignatureSettings": "签名设置", "PE.Views.RightMenu.txtSlideSettings": "幻灯片设置", "PE.Views.RightMenu.txtTableSettings": "表设置", "PE.Views.RightMenu.txtTextArtSettings": "艺术字设置", "PE.Views.ShapeSettings.strBackground": "背景颜色", "PE.Views.ShapeSettings.strChange": "更改自动形状", "PE.Views.ShapeSettings.strColor": "颜色", "PE.Views.ShapeSettings.strFill": "填满", "PE.Views.ShapeSettings.strForeground": "前景色", "PE.Views.ShapeSettings.strPattern": "模式", "PE.Views.ShapeSettings.strShadow": "显示阴影", "PE.Views.ShapeSettings.strSize": "大小", "PE.Views.ShapeSettings.strStroke": "线条", "PE.Views.ShapeSettings.strTransparency": "不透明度", "PE.Views.ShapeSettings.strType": "类型", "PE.Views.ShapeSettings.textAdvanced": "显示高级设置", "PE.Views.ShapeSettings.textAngle": "角度", "PE.Views.ShapeSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "PE.Views.ShapeSettings.textColor": "颜色填充", "PE.Views.ShapeSettings.textDirection": "方向", "PE.Views.ShapeSettings.textEmptyPattern": "无图案", "PE.Views.ShapeSettings.textFlip": "翻转", "PE.Views.ShapeSettings.textFromFile": "从文件导入", "PE.Views.ShapeSettings.textFromStorage": "来自存储设备", "PE.Views.ShapeSettings.textFromUrl": "从URL", "PE.Views.ShapeSettings.textGradient": "渐变点", "PE.Views.ShapeSettings.textGradientFill": "渐变填充", "PE.Views.ShapeSettings.textHint270": "逆时针旋转90°", "PE.Views.ShapeSettings.textHint90": "顺时针旋转90°", "PE.Views.ShapeSettings.textHintFlipH": "水平翻转", "PE.Views.ShapeSettings.textHintFlipV": "垂直翻转", "PE.Views.ShapeSettings.textImageTexture": "图片或纹理", "PE.Views.ShapeSettings.textLinear": "线性", "PE.Views.ShapeSettings.textNoFill": "没有填充", "PE.Views.ShapeSettings.textPatternFill": "模式", "PE.Views.ShapeSettings.textPosition": "位置", "PE.Views.ShapeSettings.textRadial": "径向", "PE.Views.ShapeSettings.textRecentlyUsed": "最近使用的", "PE.Views.ShapeSettings.textRotate90": "旋转90°", "PE.Views.ShapeSettings.textRotation": "旋转", "PE.Views.ShapeSettings.textSelectImage": "选取图片", "PE.Views.ShapeSettings.textSelectTexture": "请选择", "PE.Views.ShapeSettings.textStretch": "伸展", "PE.Views.ShapeSettings.textStyle": "类型", "PE.Views.ShapeSettings.textTexture": "从纹理", "PE.Views.ShapeSettings.textTile": "瓦", "PE.Views.ShapeSettings.tipAddGradientPoint": "新增渐变点", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "删除渐变点", "PE.Views.ShapeSettings.txtBrownPaper": "牛皮纸", "PE.Views.ShapeSettings.txtCanvas": "画布", "PE.Views.ShapeSettings.txtCarton": "纸板", "PE.Views.ShapeSettings.txtDarkFabric": "深色织物", "PE.Views.ShapeSettings.txtGrain": "颗粒", "PE.Views.ShapeSettings.txtGranite": "花岗岩", "PE.Views.ShapeSettings.txtGreyPaper": "灰纸", "PE.Views.ShapeSettings.txtKnit": "针织", "PE.Views.ShapeSettings.txtLeather": "Leather", "PE.Views.ShapeSettings.txtNoBorders": "没有线", "PE.Views.ShapeSettings.txtPapyrus": "纸莎草", "PE.Views.ShapeSettings.txtWood": "木头", "PE.Views.ShapeSettingsAdvanced.strColumns": "列", "PE.Views.ShapeSettingsAdvanced.strMargins": "文字填充", "PE.Views.ShapeSettingsAdvanced.textAlt": "可选文本", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "描述", "PE.Views.ShapeSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "标题", "PE.Views.ShapeSettingsAdvanced.textAngle": "角度", "PE.Views.ShapeSettingsAdvanced.textArrows": "箭头", "PE.Views.ShapeSettingsAdvanced.textAutofit": "自动适应", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "初始大小", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "初始风格", "PE.Views.ShapeSettingsAdvanced.textBevel": "斜角", "PE.Views.ShapeSettingsAdvanced.textBottom": "底部", "PE.Views.ShapeSettingsAdvanced.textCapType": "帽类型", "PE.Views.ShapeSettingsAdvanced.textCenter": "中心", "PE.Views.ShapeSettingsAdvanced.textColNumber": "列数", "PE.Views.ShapeSettingsAdvanced.textEndSize": "结束尺寸", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "结束样式", "PE.Views.ShapeSettingsAdvanced.textFlat": "平面", "PE.Views.ShapeSettingsAdvanced.textFlipped": "已翻转", "PE.Views.ShapeSettingsAdvanced.textFrom": "从", "PE.Views.ShapeSettingsAdvanced.textHeight": "高度", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "水平", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "水平", "PE.Views.ShapeSettingsAdvanced.textJoinType": "加入类型", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "不变比例", "PE.Views.ShapeSettingsAdvanced.textLeft": "左", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "线样式", "PE.Views.ShapeSettingsAdvanced.textMiter": "米特", "PE.Views.ShapeSettingsAdvanced.textNofit": "不要使用自动适应", "PE.Views.ShapeSettingsAdvanced.textPlacement": "放置", "PE.Views.ShapeSettingsAdvanced.textPosition": "位置", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "调整形状以适应文本", "PE.Views.ShapeSettingsAdvanced.textRight": "右侧", "PE.Views.ShapeSettingsAdvanced.textRotation": "旋转", "PE.Views.ShapeSettingsAdvanced.textRound": "圆", "PE.Views.ShapeSettingsAdvanced.textShrink": "超出时压缩文本", "PE.Views.ShapeSettingsAdvanced.textSize": "大小", "PE.Views.ShapeSettingsAdvanced.textSpacing": "列之间的间距", "PE.Views.ShapeSettingsAdvanced.textSquare": "四周型环绕", "PE.Views.ShapeSettingsAdvanced.textTextBox": "文本框", "PE.Views.ShapeSettingsAdvanced.textTitle": "形状 - 高级设置", "PE.Views.ShapeSettingsAdvanced.textTop": "顶部", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.ShapeSettingsAdvanced.textVertical": "垂直", "PE.Views.ShapeSettingsAdvanced.textVertically": "垂直", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "重量和箭头", "PE.Views.ShapeSettingsAdvanced.textWidth": "宽度", "PE.Views.ShapeSettingsAdvanced.txtNone": "无", "PE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "PE.Views.SignatureSettings.strDelete": "删除签名", "PE.Views.SignatureSettings.strDetails": "签名详情", "PE.Views.SignatureSettings.strInvalid": "签名无效", "PE.Views.SignatureSettings.strSign": "符号", "PE.Views.SignatureSettings.strSignature": "签名", "PE.Views.SignatureSettings.strValid": "有效签名", "PE.Views.SignatureSettings.txtContinueEditing": "继续编辑", "PE.Views.SignatureSettings.txtEditWarning": "编辑将删除演示文稿中的签名。<br>您确定要继续吗？", "PE.Views.SignatureSettings.txtRemoveWarning": "要删除该签名吗？<br>这一操作不能被撤销。", "PE.Views.SignatureSettings.txtSigned": "有效签名已添加到演示文稿中。该演示文稿已限制编辑。", "PE.Views.SignatureSettings.txtSignedInvalid": "演示文稿中的某些数字签名无效或无法验证。该演示文稿已限制编辑。", "PE.Views.SlideSettings.strBackground": "背景颜色", "PE.Views.SlideSettings.strColor": "颜色", "PE.Views.SlideSettings.strDateTime": "显示日期和时间", "PE.Views.SlideSettings.strFill": "背景", "PE.Views.SlideSettings.strForeground": "前景色", "PE.Views.SlideSettings.strPattern": "模式", "PE.Views.SlideSettings.strSlideNum": "显示幻灯片编号", "PE.Views.SlideSettings.strTransparency": "暗度", "PE.Views.SlideSettings.textAdvanced": "显示高级设置", "PE.Views.SlideSettings.textAngle": "角度", "PE.Views.SlideSettings.textColor": "颜色填充", "PE.Views.SlideSettings.textDirection": "方向", "PE.Views.SlideSettings.textEmptyPattern": "无图案", "PE.Views.SlideSettings.textFromFile": "从文件导入", "PE.Views.SlideSettings.textFromStorage": "来自存储设备", "PE.Views.SlideSettings.textFromUrl": "从URL", "PE.Views.SlideSettings.textGradient": "渐变点", "PE.Views.SlideSettings.textGradientFill": "渐变填充", "PE.Views.SlideSettings.textImageTexture": "图片或纹理", "PE.Views.SlideSettings.textLinear": "线性", "PE.Views.SlideSettings.textNoFill": "没有填充", "PE.Views.SlideSettings.textPatternFill": "模式", "PE.Views.SlideSettings.textPosition": "位置", "PE.Views.SlideSettings.textRadial": "径向", "PE.Views.SlideSettings.textReset": "重置更改", "PE.Views.SlideSettings.textSelectImage": "选取图片", "PE.Views.SlideSettings.textSelectTexture": "选择", "PE.Views.SlideSettings.textStretch": "伸展", "PE.Views.SlideSettings.textStyle": "类型", "PE.Views.SlideSettings.textTexture": "从纹理", "PE.Views.SlideSettings.textTile": "瓦", "PE.Views.SlideSettings.tipAddGradientPoint": "新增渐变点", "PE.Views.SlideSettings.tipRemoveGradientPoint": "删除渐变点", "PE.Views.SlideSettings.txtBrownPaper": "牛皮纸", "PE.Views.SlideSettings.txtCanvas": "画布", "PE.Views.SlideSettings.txtCarton": "纸板", "PE.Views.SlideSettings.txtDarkFabric": "深色织物", "PE.Views.SlideSettings.txtGrain": "颗粒", "PE.Views.SlideSettings.txtGranite": "花岗岩", "PE.Views.SlideSettings.txtGreyPaper": "灰纸", "PE.Views.SlideSettings.txtKnit": "针织", "PE.Views.SlideSettings.txtLeather": "Leather", "PE.Views.SlideSettings.txtPapyrus": "纸莎草", "PE.Views.SlideSettings.txtWood": "木头", "PE.Views.SlideshowSettings.textLoop": "连续循环，直到按“Esc”", "PE.Views.SlideshowSettings.textTitle": "显示设置", "PE.Views.SlideSizeSettings.strLandscape": "横向", "PE.Views.SlideSizeSettings.strPortrait": "纵向", "PE.Views.SlideSizeSettings.textHeight": "高度", "PE.Views.SlideSizeSettings.textSlideOrientation": "幻灯片方向", "PE.Views.SlideSizeSettings.textSlideSize": "滑动尺寸", "PE.Views.SlideSizeSettings.textTitle": "幻灯片大小设置", "PE.Views.SlideSizeSettings.textWidth": "宽度", "PE.Views.SlideSizeSettings.txt35": "35毫米幻灯片", "PE.Views.SlideSizeSettings.txtA3": "A3纸（297x420毫米)", "PE.Views.SlideSizeSettings.txtA4": "A4纸（210x297毫米）", "PE.Views.SlideSizeSettings.txtB4": "B4（ICO）纸（250x353毫米）", "PE.Views.SlideSizeSettings.txtB5": "B5（ICO）纸（176x250毫米)", "PE.Views.SlideSizeSettings.txtBanner": "旗帜", "PE.Views.SlideSizeSettings.txtCustom": "自定义", "PE.Views.SlideSizeSettings.txtLedger": "分类纸（11x17英寸）", "PE.Views.SlideSizeSettings.txtLetter": "信纸（8.5x11英寸）", "PE.Views.SlideSizeSettings.txtOverhead": "高架", "PE.Views.SlideSizeSettings.txtStandard": "标准（4：3）", "PE.Views.SlideSizeSettings.txtWidescreen": "宽屏", "PE.Views.Statusbar.goToPageText": "转到幻灯片", "PE.Views.Statusbar.pageIndexText": "幻灯片第{0}张，共{1}张", "PE.Views.Statusbar.textShowBegin": "从开始显示", "PE.Views.Statusbar.textShowCurrent": "从当前幻灯片展示", "PE.Views.Statusbar.textShowPresenterView": "显示演示者视图", "PE.Views.Statusbar.tipAccessRights": "管理文档访问权限", "PE.Views.Statusbar.tipFitPage": "适合幻灯片", "PE.Views.Statusbar.tipFitWidth": "适合宽度", "PE.Views.Statusbar.tipPreview": "开始幻灯片放映", "PE.Views.Statusbar.tipSetLang": "设置文本语言", "PE.Views.Statusbar.tipZoomFactor": "放大", "PE.Views.Statusbar.tipZoomIn": "放大", "PE.Views.Statusbar.tipZoomOut": "缩小", "PE.Views.Statusbar.txtPageNumInvalid": "幻灯片编号无效", "PE.Views.TableSettings.deleteColumnText": "删除列", "PE.Views.TableSettings.deleteRowText": "删除行", "PE.Views.TableSettings.deleteTableText": "删除表", "PE.Views.TableSettings.insertColumnLeftText": "在左侧插入列", "PE.Views.TableSettings.insertColumnRightText": "向右侧插入列", "PE.Views.TableSettings.insertRowAboveText": "插入行以上", "PE.Views.TableSettings.insertRowBelowText": "在下面插入行", "PE.Views.TableSettings.mergeCellsText": "合并单元格", "PE.Views.TableSettings.selectCellText": "选择单元格", "PE.Views.TableSettings.selectColumnText": "选择列", "PE.Views.TableSettings.selectRowText": "选择行", "PE.Views.TableSettings.selectTableText": "选择表", "PE.Views.TableSettings.splitCellsText": "拆分单元格", "PE.Views.TableSettings.splitCellTitleText": "拆分单元格", "PE.Views.TableSettings.textAdvanced": "显示高级设置", "PE.Views.TableSettings.textBackColor": "背景颜色", "PE.Views.TableSettings.textBanded": "带状", "PE.Views.TableSettings.textBorderColor": "颜色", "PE.Views.TableSettings.textBorders": "边框风格", "PE.Views.TableSettings.textCellSize": "单元格大小", "PE.Views.TableSettings.textColumns": "列", "PE.Views.TableSettings.textDistributeCols": "分布列", "PE.Views.TableSettings.textDistributeRows": "分布行", "PE.Views.TableSettings.textEdit": "行和列", "PE.Views.TableSettings.textEmptyTemplate": "没有模板", "PE.Views.TableSettings.textFirst": "第一", "PE.Views.TableSettings.textHeader": "头", "PE.Views.TableSettings.textHeight": "高度", "PE.Views.TableSettings.textLast": "最后", "PE.Views.TableSettings.textRows": "行", "PE.Views.TableSettings.textSelectBorders": "选择您要更改应用样式的边框", "PE.Views.TableSettings.textTemplate": "从模板中选择", "PE.Views.TableSettings.textTotal": "总计", "PE.Views.TableSettings.textWidth": "宽度", "PE.Views.TableSettings.tipAll": "设置外边框和所有内线", "PE.Views.TableSettings.tipBottom": "仅设置外底边框", "PE.Views.TableSettings.tipInner": "仅设置内线", "PE.Views.TableSettings.tipInnerHor": "仅设置水平内线", "PE.Views.TableSettings.tipInnerVert": "仅设置垂直内线", "PE.Views.TableSettings.tipLeft": "仅限外部左边框", "PE.Views.TableSettings.tipNone": "设置无边框", "PE.Views.TableSettings.tipOuter": "仅限外部边框", "PE.Views.TableSettings.tipRight": "仅设置外边界", "PE.Views.TableSettings.tipTop": "仅限外部边框", "PE.Views.TableSettings.txtGroupTable_Custom": "自定义", "PE.Views.TableSettings.txtGroupTable_Dark": "深色的", "PE.Views.TableSettings.txtGroupTable_Light": "浅色的", "PE.Views.TableSettings.txtGroupTable_Medium": "中等", "PE.Views.TableSettings.txtGroupTable_Optimal": "文档的最佳匹配对象", "PE.Views.TableSettings.txtNoBorders": "没有边框", "PE.Views.TableSettings.txtTable_Accent": "强调", "PE.Views.TableSettings.txtTable_DarkStyle": "黑暗样式", "PE.Views.TableSettings.txtTable_LightStyle": "光样式", "PE.Views.TableSettings.txtTable_MediumStyle": "中等风格", "PE.Views.TableSettings.txtTable_NoGrid": "没有网格", "PE.Views.TableSettings.txtTable_NoStyle": "没有样式", "PE.Views.TableSettings.txtTable_TableGrid": "表格的网格", "PE.Views.TableSettings.txtTable_ThemedStyle": "主题样式", "PE.Views.TableSettingsAdvanced.textAlt": "替代文本", "PE.Views.TableSettingsAdvanced.textAltDescription": "说明", "PE.Views.TableSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "PE.Views.TableSettingsAdvanced.textAltTitle": "标题", "PE.Views.TableSettingsAdvanced.textBottom": "底部", "PE.Views.TableSettingsAdvanced.textCenter": "中心", "PE.Views.TableSettingsAdvanced.textCheckMargins": "使用默认页边距", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "默认边距", "PE.Views.TableSettingsAdvanced.textFrom": "从", "PE.Views.TableSettingsAdvanced.textHeight": "高度", "PE.Views.TableSettingsAdvanced.textHorizontal": "水平", "PE.Views.TableSettingsAdvanced.textKeepRatio": "不变比例", "PE.Views.TableSettingsAdvanced.textLeft": "左", "PE.Views.TableSettingsAdvanced.textMargins": "元数据边缘", "PE.Views.TableSettingsAdvanced.textPlacement": "放置", "PE.Views.TableSettingsAdvanced.textPosition": "位置", "PE.Views.TableSettingsAdvanced.textRight": "右侧", "PE.Views.TableSettingsAdvanced.textSize": "大小", "PE.Views.TableSettingsAdvanced.textTitle": "表-高级设置", "PE.Views.TableSettingsAdvanced.textTop": "顶部", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "左上角", "PE.Views.TableSettingsAdvanced.textVertical": "垂直", "PE.Views.TableSettingsAdvanced.textWidth": "宽度", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "边距", "PE.Views.TextArtSettings.strBackground": "背景颜色", "PE.Views.TextArtSettings.strColor": "颜色", "PE.Views.TextArtSettings.strFill": "填满", "PE.Views.TextArtSettings.strForeground": "前景色", "PE.Views.TextArtSettings.strPattern": "模式", "PE.Views.TextArtSettings.strSize": "大小", "PE.Views.TextArtSettings.strStroke": "线条", "PE.Views.TextArtSettings.strTransparency": "不透明度", "PE.Views.TextArtSettings.strType": "类型", "PE.Views.TextArtSettings.textAngle": "角度", "PE.Views.TextArtSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "PE.Views.TextArtSettings.textColor": "颜色填充", "PE.Views.TextArtSettings.textDirection": "方向", "PE.Views.TextArtSettings.textEmptyPattern": "无图案", "PE.Views.TextArtSettings.textFromFile": "从文件导入", "PE.Views.TextArtSettings.textFromUrl": "从URL", "PE.Views.TextArtSettings.textGradient": "渐变点", "PE.Views.TextArtSettings.textGradientFill": "渐变填充", "PE.Views.TextArtSettings.textImageTexture": "图片或纹理", "PE.Views.TextArtSettings.textLinear": "线性", "PE.Views.TextArtSettings.textNoFill": "没有填充", "PE.Views.TextArtSettings.textPatternFill": "模式", "PE.Views.TextArtSettings.textPosition": "位置", "PE.Views.TextArtSettings.textRadial": "径向", "PE.Views.TextArtSettings.textSelectTexture": "请选择", "PE.Views.TextArtSettings.textStretch": "伸展", "PE.Views.TextArtSettings.textStyle": "类型", "PE.Views.TextArtSettings.textTemplate": "模板", "PE.Views.TextArtSettings.textTexture": "从纹理", "PE.Views.TextArtSettings.textTile": "瓦", "PE.Views.TextArtSettings.textTransform": "跟踪变化", "PE.Views.TextArtSettings.tipAddGradientPoint": "新增渐变点", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "删除渐变点", "PE.Views.TextArtSettings.txtBrownPaper": "牛皮纸", "PE.Views.TextArtSettings.txtCanvas": "画布", "PE.Views.TextArtSettings.txtCarton": "纸板", "PE.Views.TextArtSettings.txtDarkFabric": "深色织物", "PE.Views.TextArtSettings.txtGrain": "颗粒", "PE.Views.TextArtSettings.txtGranite": "花岗岩", "PE.Views.TextArtSettings.txtGreyPaper": "灰纸", "PE.Views.TextArtSettings.txtKnit": "针织", "PE.Views.TextArtSettings.txtLeather": "Leather", "PE.Views.TextArtSettings.txtNoBorders": "没有线", "PE.Views.TextArtSettings.txtPapyrus": "纸莎草", "PE.Views.TextArtSettings.txtWood": "木头", "PE.Views.Toolbar.capAddSlide": "添加幻灯片", "PE.Views.Toolbar.capBtnAddComment": "添加批注", "PE.Views.Toolbar.capBtnComment": "评论", "PE.Views.Toolbar.capBtnDateTime": "日期和时间", "PE.Views.Toolbar.capBtnInsHeader": "页脚", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "符号", "PE.Views.Toolbar.capBtnSlideNum": "幻灯片编号", "PE.Views.Toolbar.capInsertAudio": "声音", "PE.Views.Toolbar.capInsertChart": "图表", "PE.Views.Toolbar.capInsertEquation": "方程", "PE.Views.Toolbar.capInsertHyperlink": "超链接", "PE.Views.Toolbar.capInsertImage": "图片", "PE.Views.Toolbar.capInsertShape": "形状", "PE.Views.Toolbar.capInsertTable": "表格", "PE.Views.Toolbar.capInsertText": "文本框", "PE.Views.Toolbar.capInsertTextArt": "艺术字", "PE.Views.Toolbar.capInsertVideo": "视频", "PE.Views.Toolbar.capTabFile": "文件", "PE.Views.Toolbar.capTabHome": "主页", "PE.Views.Toolbar.capTabInsert": "插入", "PE.Views.Toolbar.mniCapitalizeWords": "首字母大写", "PE.Views.Toolbar.mniCustomTable": "插入自定义表", "PE.Views.Toolbar.mniImageFromFile": "图片文件", "PE.Views.Toolbar.mniImageFromStorage": "图片来自存储", "PE.Views.Toolbar.mniImageFromUrl": "图片来自网络", "PE.Views.Toolbar.mniInsertSSE": "插入表格", "PE.Views.Toolbar.mniLowerCase": "小写", "PE.Views.Toolbar.mniSentenceCase": "句子大小写。", "PE.Views.Toolbar.mniSlideAdvanced": "高级设置", "PE.Views.Toolbar.mniSlideStandard": "标准（4：3）", "PE.Views.Toolbar.mniSlideWide": "宽屏（16：9）", "PE.Views.Toolbar.mniToggleCase": "切换大小写", "PE.Views.Toolbar.mniUpperCase": "大写", "PE.Views.Toolbar.strMenuNoFill": "无填充", "PE.Views.Toolbar.textAlignBottom": "靠下对齐", "PE.Views.Toolbar.textAlignCenter": "居中对齐", "PE.Views.Toolbar.textAlignJust": "两端对齐", "PE.Views.Toolbar.textAlignLeft": "左对齐", "PE.Views.Toolbar.textAlignMiddle": "垂直居中", "PE.Views.Toolbar.textAlignRight": "右对齐", "PE.Views.Toolbar.textAlignTop": "靠上对齐", "PE.Views.Toolbar.textArrangeBack": "放到最下面", "PE.Views.Toolbar.textArrangeBackward": "下移一层", "PE.Views.Toolbar.textArrangeForward": "上移一层", "PE.Views.Toolbar.textArrangeFront": "放到最上面", "PE.Views.Toolbar.textBold": "加粗", "PE.Views.Toolbar.textColumnsCustom": "自定义栏", "PE.Views.Toolbar.textColumnsOne": "一列", "PE.Views.Toolbar.textColumnsThree": "三列", "PE.Views.Toolbar.textColumnsTwo": "两列", "PE.Views.Toolbar.textItalic": "斜体", "PE.Views.Toolbar.textListSettings": "列表设置", "PE.Views.Toolbar.textRecentlyUsed": "最近使用的", "PE.Views.Toolbar.textShapeAlignBottom": "靠下对齐", "PE.Views.Toolbar.textShapeAlignCenter": "居中对齐", "PE.Views.Toolbar.textShapeAlignLeft": "左对齐", "PE.Views.Toolbar.textShapeAlignMiddle": "居中对齐", "PE.Views.Toolbar.textShapeAlignRight": "右对齐", "PE.Views.Toolbar.textShapeAlignTop": "靠上对齐", "PE.Views.Toolbar.textShowBegin": "从开始显示", "PE.Views.Toolbar.textShowCurrent": "从当前幻灯片展示", "PE.Views.Toolbar.textShowPresenterView": "显示演示者视图", "PE.Views.Toolbar.textShowSettings": "显示设置", "PE.Views.Toolbar.textStrikeout": "加删除线", "PE.Views.Toolbar.textSubscript": "下标", "PE.Views.Toolbar.textSuperscript": "上标", "PE.Views.Toolbar.textTabAnimation": "动画", "PE.Views.Toolbar.textTabCollaboration": "协作", "PE.Views.Toolbar.textTabFile": "文件", "PE.Views.Toolbar.textTabHome": "主页", "PE.Views.Toolbar.textTabInsert": "插入", "PE.Views.Toolbar.textTabProtect": "保护", "PE.Views.Toolbar.textTabTransitions": "切换", "PE.Views.Toolbar.textTabView": "视图", "PE.Views.Toolbar.textTitleError": "错误", "PE.Views.Toolbar.textUnderline": "下划线", "PE.Views.Toolbar.tipAddSlide": "添加幻灯片", "PE.Views.Toolbar.tipBack": "返回", "PE.Views.Toolbar.tipChangeCase": "修改大小写", "PE.Views.Toolbar.tipChangeChart": "更改图表类型", "PE.Views.Toolbar.tipChangeSlide": "更改幻灯片布局", "PE.Views.Toolbar.tipClearStyle": "清除样式", "PE.Views.Toolbar.tipColorSchemas": "更改配色方案", "PE.Views.Toolbar.tipColumns": "插入列", "PE.Views.Toolbar.tipCopy": "复制", "PE.Views.Toolbar.tipCopyStyle": "复制样式", "PE.Views.Toolbar.tipCut": "剪切", "PE.Views.Toolbar.tipDateTime": "插入当前日期和时间", "PE.Views.Toolbar.tipDecFont": "缩小字号", "PE.Views.Toolbar.tipDecPrLeft": "减少缩进", "PE.Views.Toolbar.tipEditHeader": "编辑页脚", "PE.Views.Toolbar.tipFontColor": "字体颜色", "PE.Views.Toolbar.tipFontName": "字体 ", "PE.Views.Toolbar.tipFontSize": "字体大小", "PE.Views.Toolbar.tipHAligh": "水平对齐", "PE.Views.Toolbar.tipHighlightColor": "突出显示", "PE.Views.Toolbar.tipIncFont": "增大字号", "PE.Views.Toolbar.tipIncPrLeft": "增加缩进", "PE.Views.Toolbar.tipInsertAudio": "插入声音", "PE.Views.Toolbar.tipInsertChart": "插入图表", "PE.Views.Toolbar.tipInsertEquation": "插入方程", "PE.Views.Toolbar.tipInsertHorizontalText": "插入横排文本框", "PE.Views.Toolbar.tipInsertHyperlink": "添加超链接", "PE.Views.Toolbar.tipInsertImage": "插入图片", "PE.Views.Toolbar.tipInsertShape": "自动插入形状", "PE.Views.Toolbar.tipInsertSmartArt": "插入 SmartArt", "PE.Views.Toolbar.tipInsertSymbol": "插入符号", "PE.Views.Toolbar.tipInsertTable": "插入表", "PE.Views.Toolbar.tipInsertText": "插入文字", "PE.Views.Toolbar.tipInsertTextArt": "插入艺术字", "PE.Views.Toolbar.tipInsertVerticalText": "插入竖排文本框", "PE.Views.Toolbar.tipInsertVideo": "插入视频", "PE.Views.Toolbar.tipLineSpace": "行间距", "PE.Views.Toolbar.tipMarkers": "项目符号", "PE.Views.Toolbar.tipMarkersArrow": "箭头项目符号", "PE.Views.Toolbar.tipMarkersCheckmark": "选中标记项目符号", "PE.Views.Toolbar.tipMarkersDash": "划线项目符号", "PE.Views.Toolbar.tipMarkersFRhombus": "实心菱形项目符号", "PE.Views.Toolbar.tipMarkersFRound": "实心圆形项目符号", "PE.Views.Toolbar.tipMarkersFSquare": "实心方形项目符号", "PE.Views.Toolbar.tipMarkersHRound": "空心圆形项目符号", "PE.Views.Toolbar.tipMarkersStar": "星形项目符号", "PE.Views.Toolbar.tipNone": "无", "PE.Views.Toolbar.tipNumbers": "编号", "PE.Views.Toolbar.tipPaste": "粘贴", "PE.Views.Toolbar.tipPreview": "开始幻灯片放映", "PE.Views.Toolbar.tipPrint": "打印", "PE.Views.Toolbar.tipPrintQuick": "快速打印", "PE.Views.Toolbar.tipRedo": "重做", "PE.Views.Toolbar.tipSave": "保存", "PE.Views.Toolbar.tipSaveCoauth": "保存您的更改以供其他用户查看", "PE.Views.Toolbar.tipSelectAll": "全选", "PE.Views.Toolbar.tipShapeAlign": "对齐形状", "PE.Views.Toolbar.tipShapeArrange": "排列形状", "PE.Views.Toolbar.tipSlideNum": "插入幻灯片编号", "PE.Views.Toolbar.tipSlideSize": "选择幻灯片大小", "PE.Views.Toolbar.tipSlideTheme": "幻灯片主题", "PE.Views.Toolbar.tipUndo": "复原", "PE.Views.Toolbar.tipVAligh": "垂直对齐", "PE.Views.Toolbar.tipViewSettings": "视图设置", "PE.Views.Toolbar.txtDistribHor": "水平分布", "PE.Views.Toolbar.txtDistribVert": "垂直分布", "PE.Views.Toolbar.txtDuplicateSlide": "复制幻灯片", "PE.Views.Toolbar.txtGroup": "组合", "PE.Views.Toolbar.txtObjectsAlign": "对齐所选对象", "PE.Views.Toolbar.txtScheme1": "公司地址", "PE.Views.Toolbar.txtScheme10": "中位数", "PE.Views.Toolbar.txtScheme11": "组件", "PE.Views.Toolbar.txtScheme12": "组件", "PE.Views.Toolbar.txtScheme13": "富裕的", "PE.Views.Toolbar.txtScheme14": "奥丽尔", "PE.Views.Toolbar.txtScheme15": "原来的", "PE.Views.Toolbar.txtScheme16": "纸", "PE.Views.Toolbar.txtScheme17": "冬至", "PE.Views.Toolbar.txtScheme18": "技术", "PE.Views.Toolbar.txtScheme19": "行进", "PE.Views.Toolbar.txtScheme2": "灰度", "PE.Views.Toolbar.txtScheme20": "城市的", "PE.Views.Toolbar.txtScheme21": "气势", "PE.Views.Toolbar.txtScheme22": "新的Office", "PE.Views.Toolbar.txtScheme3": "顶点", "PE.Views.Toolbar.txtScheme4": "方面", "PE.Views.Toolbar.txtScheme5": "公民", "PE.Views.Toolbar.txtScheme6": "中央大厅", "PE.Views.Toolbar.txtScheme7": "公平", "PE.Views.Toolbar.txtScheme8": "流动", "PE.Views.Toolbar.txtScheme9": "发现", "PE.Views.Toolbar.txtSlideAlign": "与幻灯片对齐", "PE.Views.Toolbar.txtUngroup": "取消组合", "PE.Views.Transitions.strDelay": "延迟", "PE.Views.Transitions.strDuration": "持续时间", "PE.Views.Transitions.strStartOnClick": "开始点击", "PE.Views.Transitions.textBlack": "全黑", "PE.Views.Transitions.textBottom": "底部", "PE.Views.Transitions.textBottomLeft": "左下", "PE.Views.Transitions.textBottomRight": "右下", "PE.Views.Transitions.textClock": "时钟", "PE.Views.Transitions.textClockwise": "顺时针", "PE.Views.Transitions.textCounterclockwise": "逆时针", "PE.Views.Transitions.textCover": "覆盖", "PE.Views.Transitions.textFade": "淡化", "PE.Views.Transitions.textHorizontalIn": "上下向中央收缩", "PE.Views.Transitions.textHorizontalOut": "中央向上下展开", "PE.Views.Transitions.textLeft": "左", "PE.Views.Transitions.textNone": "无", "PE.Views.Transitions.textPush": "推送", "PE.Views.Transitions.textRight": "右侧", "PE.Views.Transitions.textSmoothly": "平滑", "PE.Views.Transitions.textSplit": "拆分", "PE.Views.Transitions.textTop": "顶部", "PE.Views.Transitions.textTopLeft": "左上", "PE.Views.Transitions.textTopRight": "右上", "PE.Views.Transitions.textUnCover": "揭开", "PE.Views.Transitions.textVerticalIn": "左右向中央收缩", "PE.Views.Transitions.textVerticalOut": "中央向左右展开", "PE.Views.Transitions.textWedge": "楔形", "PE.Views.Transitions.textWipe": "擦除", "PE.Views.Transitions.textZoom": "缩放", "PE.Views.Transitions.textZoomIn": "放大", "PE.Views.Transitions.textZoomOut": "缩小", "PE.Views.Transitions.textZoomRotate": "缩放并旋转", "PE.Views.Transitions.txtApplyToAll": "适用于所有幻灯片", "PE.Views.Transitions.txtParameters": "参数", "PE.Views.Transitions.txtPreview": "预览", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.textAddHGuides": "添加水平参考线", "PE.Views.ViewTab.textAddVGuides": "添加垂直参考线", "PE.Views.ViewTab.textAlwaysShowToolbar": "始终显示工具栏", "PE.Views.ViewTab.textClearGuides": "清除参考线", "PE.Views.ViewTab.textCm": "厘米", "PE.Views.ViewTab.textCustom": "自定义", "PE.Views.ViewTab.textFitToSlide": "适合幻灯片", "PE.Views.ViewTab.textFitToWidth": "适合宽度", "PE.Views.ViewTab.textGridlines": "网格线", "PE.Views.ViewTab.textGuides": "参考线", "PE.Views.ViewTab.textInterfaceTheme": "界面主题", "PE.Views.ViewTab.textLeftMenu": "左面板", "PE.Views.ViewTab.textNotes": "备注", "PE.Views.ViewTab.textRightMenu": "右面板", "PE.Views.ViewTab.textRulers": "标尺", "PE.Views.ViewTab.textShowGridlines": "显示网格线", "PE.Views.ViewTab.textShowGuides": "显示参考线", "PE.Views.ViewTab.textSmartGuides": "智能参考线", "PE.Views.ViewTab.textSnapObjects": "将对象对齐网格", "PE.Views.ViewTab.textStatusBar": "状态栏", "PE.Views.ViewTab.textZoom": "缩放", "PE.Views.ViewTab.tipFitToSlide": "适合幻灯片", "PE.Views.ViewTab.tipFitToWidth": "适合宽度", "PE.Views.ViewTab.tipGridlines": "显示网格线", "PE.Views.ViewTab.tipGuides": "显示参考线", "PE.Views.ViewTab.tipInterfaceTheme": "界面主题"}