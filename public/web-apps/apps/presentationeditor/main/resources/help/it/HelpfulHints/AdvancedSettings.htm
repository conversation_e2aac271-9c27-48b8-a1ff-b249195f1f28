﻿<!DOCTYPE html>
<html>
	<head>
		<title>Advanced Settings of Presentation Editor</title>
		<meta charset="utf-8" />
		<meta name="description" content="The advanced settings of Presentation Editor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Advanced Settings of Presentation Editor</h1>
			<p><b>Presentation Editor</b> lets you change its advanced settings. To access them, open the <b>File</b> tab at the top toolbar and select the <b>Advanced Settings...</b> option. You can also click the <b>View settings</b> <span class="icon icon-viewsettingsicon"></span> icon on the right side of the editor header and select the <b>Advanced settings</b> option.</p>
			<p>The advanced settings are:</p>
			<ul>
                <li><b>Spell Checking</b> is used to turn on/off the spell checking option.</li>
				<li><b>Alternate Input</b> is used to turn on/off hieroglyphs.</li>
                <li><b>Alignment Guides</b> is used to turn on/off alignment guides that appear when you move objects and allow you to position them on the slide precisely.</li>
                <li><span class="onlineDocumentFeatures"><b>Autosave</b> is used in the <em>online version</em> to turn on/off automatic saving of changes you make while editing.</span> </li>
                <li><span class="desktopDocumentFeatures"><b>Autorecover</b> - is used in the <em>desktop version</em> to turn on/off the option that allows to automatically recover documents in case of the unexpected program closing.</span></li>
                <li class="onlineDocumentFeatures"><b>Co-editing Mode</b> is used to select the display of the changes made during the co-editing:
                    <ul>
                        <li>By default the <b>Fast</b> mode is selected, the users who take part in the document co-editing will see the changes in real time once they are made by other users.</li>
                        <li>If you prefer not to see other user changes (so that they do not disturb you, or for some other reason), select the <b>Strict</b> mode and all the changes will be shown only after you click the <b>Save</b> <div class = "icon icon-saveupdate"></div> icon notifying you that there are changes from other users.</li>
                    </ul>
                </li>
                <li><b>Default Zoom Value</b> is used to set the default zoom value selecting it in the list of available options from 50% to 200%. You can also choose the <b>Fit to Slide</b> or <b>Fit to Width</b> option.</li>
                <li>
                    <b>Font Hinting</b> is used to select the type a font is displayed in Presentation Editor:
                    <ul>
                        <li>Choose <b>As Windows</b> if you like the way fonts are usually displayed on Windows, i.e. using Windows font hinting.</li>
                        <li>Choose <b>As OS X</b> if you like the way fonts are usually displayed on a Mac, i.e. without any font hinting at all.</li>
                        <li>Choose <b>Native</b> if you want your text to be displayed with the hinting embedded into font files.</li>
                    </ul>
                </li>
                <li>
                    <b>Default cache mode</b> - used to select the cache mode for the font characters. It’s not recommended to switch it without any reason. It can be helpful in some cases only, for example, when an issue in the Google Chrome browser with the enabled hardware acceleration occurs.
                    <p>Presentation Editor has two cache modes:</p>
                    <ol>
                        <li>In the <b>first cache mode</b>, each letter is cached as a separate picture.</li>
                        <li>In the <b>second cache mode</b>, a picture of a certain size is selected where letters are placed dynamically and a mechanism of allocating/removing memory in this picture is also implemented. If there is not enough memory, a second picture is created, etc.</li>
                    </ol>
                    <p>The <b>Default cache mode</b> setting applies two above mentioned cache modes separately for different browsers:</p>
                    <ul>
                        <li>When the <b>Default cache mode</b> setting is enabled, Internet Explorer (v. 9, 10, 11) uses the <b>second cache mode</b>, other browsers use the <b>first cache mode</b>.</li>
                        <li>When the <b>Default cache mode</b> setting is disabled, Internet Explorer (v. 9, 10, 11) uses the <b>first cache mode</b>, other browsers use the <b>second cache mode</b>.</li>
                    </ul>
                </li>
				<li><b>Unit of Measurement</b> is used to specify what units are used on the rulers and in properties windows for measuring elements parameters such as width, height, spacing, margins etc. You can select the <b>Centimeter</b>, <b>Point</b>, or <b>Inch</b> option.</li>
			</ul>
			<p>To save the changes you made, click the <b>Apply</b> button.</p>
		</div>
	</body>
</html>