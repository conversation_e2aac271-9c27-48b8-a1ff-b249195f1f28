﻿<!DOCTYPE html>
<html>
	<head>
		<title>N-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>N-Funktion</h1>
			<p>Die Funktion <b>N</b> gehört zur Gruppe der Informationsfunktionen. Sie wandelt eine Zahl in Text um.</p>
			<p>Die Formelsyntax der Funktion <b>N</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>N(Wert)</em></b></p> 
			<p><b><em>Wert</em></b> ist der Wert, den Sie in eine Zahl umwandeln möchten, manuell eingegeben oder in die Zelle eingeschlossen, auf die Sie Bezug nehmen. Die Funktion wandelt Werte gemäß der folgenden Tabelle um.</p>
			<table  style="width: 30%">
				<tr>
					<td><b>Wert</b></td>
					<td><b>Zahl</b></td>
				</tr>
				<tr>
					<td>Zahl</td>
					<td>Zahl</td>
				</tr>
				<tr>
					<td>Datum</td>
					<td>Datum als fortlaufende Zahl.</td>
				</tr>
				<tr>
					<td>WAHR</td>
					<td>1</td>
				</tr>
				<tr>
					<td>FALSCH</td>
					<td>0</td>
				</tr>
				<tr>
					<td>Fehler</td>
					<td>Fehlerwert</td>
				</tr>
				<tr>
					<td>Sonstige</td>
					<td>0</td>
				</tr>
			</table>
			<p>Anwendung der Funktion <b>N</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>N</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="N-Funktion" src="../images/n.png" /></p>
		</div>
	</body>
</html>