{"DATE": {"a": "(έτος; μήνας; ημέρα)", "d": "Αποδίδει τον αριθμό που αναπ<PERSON><PERSON>ιστά την ημερομηνία στον κώδικα ημερομηνίας-<PERSON>ρας"}, "DATEDIF": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; μονάδα)", "d": "Υπολογίζει τον αριθμό των ημερών, των μηνών ή των ετών μεταξύ δύο ημερομηνιών"}, "DATEVALUE": {"a": "(κείμενο_ημερομηνίας)", "d": "Μετατρέπει μια ημερομηνία που έχει μορφή κειμένου σε αριθμό που αναπαριστά την ημερομηνία στον κώδικα ημερομηνίας-ώρας"}, "DAY": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει την ημέρα του μήνα, έν<PERSON>ς αριθμός από το 1 έως το 31."}, "DAYS": {"a": "(τελική_ημερομηνία; αρχική_ημερομηνία)", "d": "Αποδίδει τον αριθμό ημερών μεταξύ των δύο ημερομηνιών."}, "DAYS360": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [μέθοδος])", "d": "Υπολογίζει το πλήθος των ημερών ανάμεσα σε δύο ημερομηνίες, β<PERSON><PERSON><PERSON><PERSON> έτους 360 ημερών (δώδεκα μήνες των 30 ημερών)"}, "EDATE": {"a": "(ημερομηνία_έναρξης; μήνες)", "d": "Αποδίδει τον αύξοντα αριθμό μιας ημερομηνίας που υποδεικνύεται από το πλήθος των μηνών πριν ή μετά την αρχική ημερομηνία"}, "EOMONTH": {"a": "(ημερομηνία_έναρξης; μήνες)", "d": "Αποδίδει τον αύξοντα αριθμό της τελευταίας ημέρας του μήνα πριν ή μετά από ένα συγκεκριμένο αριθμό μηνών"}, "HOUR": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει την ώρα με τη μορφή αριθμού από το 0 (12:00 ΠΜ) έως το 23 (11:00 ΜΜ)."}, "ISOWEEKNUM": {"a": "(ημερομηνία)", "d": "Αποδίδει τον αριθμό εβδομάδας κατά ISO του έτους για μια δεδομένη ημερομηνία"}, "MINUTE": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το λεπτό, έν<PERSON><PERSON> αριθμός από το 0 έως το 59."}, "MONTH": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το μήνα, <PERSON><PERSON><PERSON><PERSON> αριθμός από το 1 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) έως το 12 (<PERSON><PERSON><PERSON><PERSON><PERSON>βρι<PERSON>)."}, "NETWORKDAYS": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [αργίες])", "d": "Αποδίδει τον αριθμό των ολόκληρων ημερών εργασίας μεταξύ δύο ημερομηνιών"}, "NETWORKDAYS.INTL": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [σαββατοκύριακο]; [αργίες])", "d": "Αποδίδει τον αριθμό των ολόκληρων ημερών εργασίας μεταξύ δύο ημερομηνιών με προσαρμοσμένες παραμέτρους σαββατοκύριακου"}, "NOW": {"a": "()", "d": "Αποδίδει την τρέχουσα ημερομηνία και ώρα με τη μορφή ημερομηνίας και ώρας."}, "SECOND": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το δευτερ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, έν<PERSON><PERSON> αριθμός από το 0 έως το 59."}, "TIME": {"a": "(ώρα; λεπτό; δευτερόλεπτο)", "d": "Μετατρέπει τις ώρες, τα λεπτά και τα δευτερόλεπτα που δίνονται με τη μορφή αριθμών σε έναν αύξοντα αριθμό, με μορφή ώρας"}, "TIMEVALUE": {"a": "(κείμενο_ώρας)", "d": "Μετατρέπει μια ώρα με τη μορφή κειμένου σε αύξοντα αριθμό ώρας, α<PERSON><PERSON> το 0 (12:00:00 ΠΜ) έως το 0,999988426 (11:59:59 ΜΜ). Μορφοποιεί τον αριθμό με μορφή ώρας μετά την πληκτρολόγηση του τύπου"}, "TODAY": {"a": "()", "d": "Αποδίδει την τρέχουσα ημερομηνία με μορφή ημερομηνίας."}, "WEEKDAY": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός; [τύπος_επιστροφής])", "d": "Αποδίδει έναν αριθμό από το 1 έως το 7 ο οποίος προσδιορίζει την ημέρα της εβδομάδας που αντιστοιχεί σε μια ημερομηνία."}, "WEEKNUM": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός; [τύπος_επιστροφής])", "d": "Αποδίδει τον αριθμό της εβδομάδας μέσα στο έτος"}, "WORKDAY": {"a": "(ημερομηνία_έναρξης; ημέρες; [αργίες])", "d": "Αποδίδει τον αύξοντα αριθμό της ημερομηνίας πριν ή έπειτα από έναν συγκεκριμένο αριθμό ημερών εργασίας"}, "WORKDAY.INTL": {"a": "(ημερομηνία_έναρξης; ημέρες; [σαββατοκύριακο]; [αργίες])", "d": "Αποδίδει τον αύξοντα αριθμό της ημερομηνίας πριν ή έπειτα από έναν συγκεκριμένο αριθμό ημερών εργασίας με προσαρμοσμένες παραμέτρους σαββατοκύριακου"}, "YEAR": {"a": "(σειρια<PERSON><PERSON>ς_αριθμός)", "d": "Αποδίδει το έτος μιας ημερομηνίας, ένας ακέραιος μεταξύ 1900 και 9999."}, "YEARFRAC": {"a": "(ημερομηνία_έναρξης; ημερομηνία_λήξης; [βάση])", "d": "Αποδίδει το κλάσμα του έτους που αναπαριστά τον αριθμό των ολόκληρων ημερών μεταξύ των παραμέτρων \"ημερομηνία_έναρξης\" και \"ημερομηνία_λήξης\""}, "BESSELI": {"a": "(x; n)", "d": "Αποδίδει την τροποποιημένη συνάρτηση Bessel In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Αποδίδει τη συνάρτηση Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Αποδίδει την τροποποιημένη συνάρτηση Bessel Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Αποδίδει τη συνάρτηση Bessel Yn(x)"}, "BIN2DEC": {"a": "(αριθμός)", "d": "Μετατρέ<PERSON><PERSON>ι έναν δυαδικό αριθμό σε δεκαδικό"}, "BIN2HEX": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δυαδικό αριθμό σε δεκαεξαδικό"}, "BIN2OCT": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δυαδικό αριθμό σε οκταδικό"}, "BITAND": {"a": "(αριθμός1; αριθμός2)", "d": "Αποδίδει το λογικό 'και' με βάση τα bit δύο αριθμών"}, "BITLSHIFT": {"a": "(αριθμός; πλήθος_μετατόπισης)", "d": "Αποδίδει έναν αριθμό ο οποίος έχει μετατοπιστεί προς τα αριστερά κατά τόσα bit όσα το  πλήθος_μετατόπισης"}, "BITOR": {"a": "(αριθμός1; αριθμός2)", "d": "Αποδίδει το λογικό 'ή' με βάση τα bit δύο αριθμών"}, "BITRSHIFT": {"a": "(αριθμός; πλήθος_μετατόπισης)", "d": "Αποδίδει έναν αριθμό ο οποίος έχει μετατοπιστεί προς τα δεξιά κατά τόσα bit όσα το πλήθος_μετατόπισης"}, "BITXOR": {"a": "(αριθμός1; αριθμός2)", "d": "Αποδίδει το λογικό 'αποκλειστικό ή' με βάση τα bit δύο αριθμών"}, "COMPLEX": {"a": "(πραγματικ<PERSON>ς_αριθμός; μιγαδικός_αριθμός; [επίθημα])", "d": "Μετατρέπει τον πραγματικό και τον φανταστικό συντελεστή σε μιγαδικό αριθμό"}, "CONVERT": {"a": "(αριθμός; από_μονάδα; σε_μονάδα)", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν αριθμό από ένα σύστημα μέτρησης σε άλλο"}, "DEC2BIN": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαδικό αριθμό σε δυαδικό"}, "DEC2HEX": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαδικό αριθμό σε δεκαεξαδικό"}, "DEC2OCT": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαδικό αριθμό σε οκταδικό"}, "DELTA": {"a": "(αριθμός1; [αριθμός2])", "d": "Ελέγχει αν δύο αριθμοί είναι ίσοι"}, "ERF": {"a": "(κατώτερο_όριο; [ανώτερο_όριο])", "d": "Αποδίδει τη συνάρτηση σφάλματος"}, "ERF.PRECISE": {"a": "(X)", "d": "Αποδίδει τη συνάρτηση σφάλματος"}, "ERFC": {"a": "(x)", "d": "Αποδίδει τη συμπληρωματική συνάρτηση σφάλματος"}, "ERFC.PRECISE": {"a": "(X)", "d": "Αποδίδει τη συμπληρωματική συνάρτηση σφάλματος"}, "GESTEP": {"a": "(αριθμός; [βήμα])", "d": "Ελέγχει αν ένας αριθμός είναι μεγαλύτερος από μια τιμή κατωφλίου"}, "HEX2BIN": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαεξαδικό αριθμό σε δυαδικό"}, "HEX2DEC": {"a": "(αριθμός)", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαεξαδικό αριθμό σε δεκαδικό"}, "HEX2OCT": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON>ι έναν δεκαεξαδικό αριθμό σε οκταδικό"}, "IMABS": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την απόλυτη τιμή (μέτρο) ενός μιγαδικού αριθμού"}, "IMAGINARY": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον φανταστικό συντελεστή ενός μιγαδικού αριθμού"}, "IMARGUMENT": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το όρισμα q, μια γωνία εκφρασμένη σε ακτίνια"}, "IMCONJUGATE": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον συζυγή μιγαδικ<PERSON> ενός μιγαδικού αριθμού"}, "IMCOS": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το συνημίτονο ενός μιγαδικού αριθμού"}, "IMCOSH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το υπερβολικό συνημίτονο ενός μιγαδικού αριθμού"}, "IMCOT": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τη συνεφαπτομένη ενός μιγαδικού αριθμού"}, "IMCSC": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τη συντέμνουσα ενός μιγαδικού αριθμού"}, "IMCSCH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την υπερβολική συντέμνουσα ενός μιγαδικού αριθμού"}, "IMDIV": {"a": "(μιγαδικ<PERSON>ς_αριθμός1; μιγαδικός_αριθμός2)", "d": "Αποδίδει το πηλίκο δύο μιγαδικών αριθμών"}, "IMEXP": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την εκθετική δύναμη ενός μιγαδικού αριθμού"}, "IMLN": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον φυσικό λογάριθμο ενός μιγαδικού αριθμού"}, "IMLOG10": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον δεκαδικό λογάριθμο ενός μιγαδικού αριθμού"}, "IMLOG2": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το λογάριθμο με βάση 2 ενός μιγαδικού αριθμού"}, "IMPOWER": {"a": "(μιγαδικ<PERSON>ς_αριθμός; αριθμός)", "d": "Αποδίδει έναν μιγαδικό αριθμό υψωμένο σε ακέραια δύναμη"}, "IMPRODUCT": {"a": "(μιγαδικός_αριθμός1; [μιγαδικός_αριθμός2]; ...)", "d": "Επιστρέφει το γινόμενο 1 έως 255 μιγαδικών αριθμών"}, "IMREAL": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει τον πραγματικό συντελεστή ενός μιγαδικού αριθμού"}, "IMSEC": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την τέμνουσα ενός μιγαδικού αριθμού"}, "IMSECH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την υπερβολική τέμνουσα ενός μιγαδικού αριθμού"}, "IMSIN": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το ημίτονο ενός μιγαδικού αριθμού"}, "IMSINH": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει το υπερβολικ<PERSON> ημίτονο ενός μιγαδικού αριθμού"}, "IMSQRT": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την τετραγωνική ρίζα ενός μιγαδικού αριθμού"}, "IMSUB": {"a": "(μιγαδικ<PERSON>ς_αριθμός1; μιγαδικός_αριθμός2)", "d": "Αποδίδει τη διαφορά δύο μιγαδικών αριθμών"}, "IMSUM": {"a": "(μιγαδικός_αριθμός1; [μιγαδικός_αριθμός2]; ...)", "d": "Επιστρέφει το άθροισμα μιγαδικών αριθμών"}, "IMTAN": {"a": "(μιγαδικ<PERSON>ς_αριθμός)", "d": "Αποδίδει την εφαπτομένη ενός μιγαδικού αριθμού"}, "OCT2BIN": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν οκταδικό αριθμό σε δυαδικό"}, "OCT2DEC": {"a": "(αριθμός)", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν οκταδικό αριθμό σε δεκαδικό"}, "OCT2HEX": {"a": "(αριθμός; [θέσεις])", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν οκταδικό αριθμό σε δεκαεξαδικό"}, "DAVERAGE": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Αποδίδει τον μέσο όρο των τιμών μιας στήλης σε μια λίστα ή βάση δεδομένων που ικανοποιεί τις καθορισμένες συνθήκες"}, "DCOUNT": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Μετράει τα κελιά που περιέχουν αριθμούς στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων, τα οποία ικανοποιούν τις καθορισμένες συνθήκες"}, "DCOUNTA": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Μετράει μη κενά κελιά στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων που ικανοποιούν τις καθορισμένες συνθήκες"}, "DGET": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Εξαγάγει από βάση δεδομένων μια μόνο εγγραφή που ικανοποιεί τις καθορισμένες συνθήκες"}, "DMAX": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Αποδίδει τον μεγαλύτερο αριθμό που ικανοποιεί τις καθορισμένες συνθήκες στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων"}, "DMIN": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Αποδίδει τον μικρότερο αριθμό που ικανοποιεί τις καθορισμένες συνθήκες στο πεδίο (στήλη) εγγραφών μιας βάσης δεδομένων"}, "DPRODUCT": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Πολλαπλασιάζει τις τιμές που ικανοποιούν τις καθορισμένες συνθήκες στο πεδίο (στήλη) εγγραφών της βάσης δεδομένων"}, "DSTDEV": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει την τυπική απόκλιση βάσει δείγματος από επιλεγμένες καταχωρήσεις της βάσης δεδομένων"}, "DSTDEVP": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει την τυπική απόκλιση βάσει ολόκληρου του πληθυσμού από τις επιλεγμένες καταχωρήσεις της βάσης δεδομένων"}, "DSUM": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Προσθέτει τους αριθμούς στο πεδίο (στήλη) εγγραφών της βάσης δεδομένων που ικανοποιούν τις καθορισμένες συνθήκες"}, "DVAR": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει τη διακύμανση βάσει δείγματος από επιλεγμένες καταχωρήσεις βάσης δεδομένων"}, "DVARP": {"a": "(βάση_δεδομένων; πεδί<PERSON>; κριτήρια)", "d": "Υπολογίζει τη διακύμανση βάσει ολόκληρου του πληθυσμού επιλεγμένων καταχωρήσεων βάσης δεδομένων"}, "CHAR": {"a": "(αριθμός)", "d": "Αποδίδει το χαρακτήρα που καθορίζεται από τον κωδικό αριθμό του συνόλου χαρακτήρων του υπολογιστή σας"}, "CLEAN": {"a": "(κείμενο)", "d": "Καταρ<PERSON><PERSON><PERSON> όλους τους μη εκτυπώσιμους χαρακτήρες από το κείμενο"}, "CODE": {"a": "(κείμενο)", "d": "Αποδίδει έναν αριθμητικ<PERSON> κωδικό για τον πρώτο χαρακτήρα μιας ακολουθίας, στο σύνολο χαρακτήρων του υπολογιστή σας"}, "CONCATENATE": {"a": "(κείμενο1; [κείμενο2]; ...)", "d": "Ενοποιεί συμβολοσειρές κειμένου σε μια συμβολοσειρά"}, "CONCAT": {"a": "(κείμενο1; ...)", "d": "Συνενώνει μια λίστα ή μια περιοχή με συμβολοσειρές κειμένου"}, "DOLLAR": {"a": "(αριθμός; [δεκαδικοί])", "d": "Μετατρέπει αριθμό σε κείμενο με χρήση νομισματικής μορφής"}, "EXACT": {"a": "(κείμενο1; κείμενο2)", "d": "Ελέγχει αν δύο ακολουθίες χαρακτήρων κειμένου είναι πανομοιότυπες και επιστρέφει TRUE ή FALSE. Η παράμετρος EXACT κάνει διάκριση πεζών-κεφαλαίων"}, "FIND": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Επιστρέφει τη θέση έναρξης μιας ακολουθίας χαρακτήρων κειμένου μέσα σε μια άλλη ακολουθία χαρακτήρων κειμένου. Η παράμετρος FIND κάνει διάκριση πεζών-κεφαλαίων"}, "FINDB": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Εντοπίζουν μία συμβολοσειρά κειμένου εντός μιας δεύτερης συμβολοσειράς κειμένου και επιστρέφουν τον αριθμό της θέσης έναρξης της πρώτης συμβολοσειράς κειμένου από τον πρώτο χαρακτήρα της δεύτερης συμβολοσειράς κειμένου, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά"}, "FIXED": {"a": "(αριθμός; [δεκαδικά_ψηφία]; [χωρίς_τελείες])", "d": "Στρογγυλοπ<PERSON><PERSON><PERSON><PERSON> έναν αριθμό στο καθορισμένο πλήθος δεκαδικών και επιστρέφει το αποτέλεσμα ως κείμενο με ή χωρίς τελείες"}, "LEFT": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Αποδίδει το καθορισμένο πλήθος χαρακτήρων από την αρχή μιας ακολουθίας χαρακτήρων κειμένου"}, "LEFTB": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Eπιστρέφει τον πρώτο χαρακτήρα ή τους χαρακτήρες μιας συμβολοσειράς κειμένου, με βάση τον αριθμό των byte που καθορίζετε, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>κ<PERSON>, Κινεζικά και Κορεατικά"}, "LEN": {"a": "(κείμενο)", "d": "Αποδίδει το πλήθος των χαρακτήρων σε μια ακολουθία χαρακτήρων κειμένου"}, "LENB": {"a": "(κείμενο)", "d": "Eπιστρέφει το πλήθος των byte που χρησιμοποιούνται για την απεικόνιση των χαρακτήρων σε μια συμβολοσειρά κειμένου., προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά"}, "LOWER": {"a": "(κείμενο)", "d": "Μετατρέπει όλα τα γράμματα μιας ακολουθίας χαρακτήρων κειμένου σε πεζά"}, "MID": {"a": "(κείμεν<PERSON>; αριθμός_έναρξης; αριθμός_χαρακτήρων)", "d": "Αποδίδει τους χαρακτήρες από το μέσο μιας ακολουθίας χαρακτήρων κειμένου, εφ<PERSON><PERSON><PERSON><PERSON> καθοριστεί η αρχική θέση και το μήκος"}, "MIDB": {"a": "(κείμεν<PERSON>; αριθμός_έναρξης; αριθμός_χαρακτήρων)", "d": "Eπιστρέφει έναν συγκεκριμένο αριθμό χαρακτήρων από μια συμβολοσειρά κειμένου, αρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ας από μια καθορισμένη θέση, βά<PERSON>ει του αριθμού των byte που καθορίζετε., προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά"}, "NUMBERVALUE": {"a": "(κείμενο; [διαχωριστικ<PERSON>_δεκαδικών]; [διαχωριστικό_ομάδων])", "d": "Μετατρέπει κείμενο σε αριθμό με τρόπο ανεξάρτητο από τις τοπικές ρυθμίσεις του συστήματος"}, "PROPER": {"a": "(κείμενο)", "d": "Μετατρέπει το πρώτο γράμμα όλων των λέξεων μιας ακολουθίας χαρακτήρων κειμένου σε κεφαλαίο και όλα τα υπόλοιπα γράμματα σε πεζά"}, "REPLACE": {"a": "(παλιό_κείμενο; αριθμός_έναρξης; αριθμός_χαρακτήρων; νέο_κείμενο)", "d": "Αντικαθιστά χαρακτήρες μέσα σε κείμενο"}, "REPLACEB": {"a": "(παλιό_κείμενο; αριθμός_έναρξης; αριθμός_χαρακτήρων; νέο_κείμενο)", "d": "Aντι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μέρος μιας συμβολοσειράς κειμένου με άλλη συμβολοσειρά, βάσει του αριθμού των byte που καθορίζετε, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά"}, "REPT": {"a": "(κείμενο; αριθμός_επαναλήψεων)", "d": "Επαναλαμβάνει κείμενο όσες φορές έχει οριστεί. Χρησιμοποιήστε την REPT, για να συμπληρώσετε ένα κελί, ε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>βάνοντας μια ακολουθία χαρακτήρων κειμένου."}, "RIGHT": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Αποδίδει το καθορισμένο πλήθος χαρακτήρων από το τέλος μιας ακολουθίας χαρακτήρων κειμένου"}, "RIGHTB": {"a": "(κείμενο; [αριθμός_χαρακτήρων])", "d": "Αποδίδει τους τελευταίους χαρακτήρες μιας συμβολοσειράς κειμένου, με βάση τον αριθμό των byte που καθορίζετε, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά"}, "SEARCH": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Αποδίδει τον αριθμό του χαρακτήρα όπου εντοπίζεται για πρώτη φορά ένας χαρακτήρας ή συμβολοσειρά κειμένου, απ<PERSON> αριστερά προς δεξιά (χωρίς διάκριση πεζών-κεφαλαίων)"}, "SEARCHB": {"a": "(εύρεση_κειμένου; εντός_κειμένου; [αριθμός_έναρξης])", "d": "Εντοπίζουν μία συμβολοσειρά κειμένου εντός μιας δεύτερης συμβολοσειράς κειμένου και επιστρέφουν τον αριθμό της θέσης έναρξης της πρώτης συμβολοσειράς κειμένου από τον πρώτο χαρακτήρα της δεύτερης συμβολοσειράς κειμένου, προορίζεται για χρήση με γλώσσες που χρησιμοποιούν σύνολα χαρακτήρων των δύο byte (DBCS) - Ιαπωνικά, Κινεζικά και Κορεατικά"}, "SUBSTITUTE": {"a": "(κείμενο; παλιό_κείμενο; νέο_κείμενο; [αριθμός_παρουσίας])", "d": "Αντικαθιστά παλιό κείμενο με νέο κείμενο σε ακολουθία χαρακτήρων κειμένου"}, "T": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι κείμενο και, αν ναι, αποδίδει το κείμενο, αλλ<PERSON><PERSON><PERSON> αποδίδει εισαγωγικά (κενό κείμενο)"}, "TEXT": {"a": "(τιμή; μορφοποίηση_κειμένου)", "d": "Μετατρέπει μια τιμή σε κείμενο με μια συγκεκριμένη μορφή αριθμού"}, "TEXTJOIN": {"a": "(οριοθέτης; παράβλεψη_κενό; κείμενο1; ...)", "d": "Συνενώνει μια λίστα ή μια περιοχή με συμβολοσειρές κειμένου με τη χρήση οριοθέτη"}, "TRIM": {"a": "(κείμενο)", "d": "Καταργεί τα διαστήματα από το κείμενο, διατηρώντας όμως τα μονά διαστήματα μεταξύ των λέξεων"}, "UNICHAR": {"a": "(αριθμός)", "d": "Αποδίδει τον χαρακτήρα Unicode που αντιστοιχεί στην δεδομένη αριθμητική τιμή"}, "UNICODE": {"a": "(κείμενο)", "d": "Αποδίδει τον αριθμό (σημε<PERSON><PERSON> κώδικα) που αντιστοιχεί στον πρώτο χαρακτήρα του κειμένου"}, "UPPER": {"a": "(κείμενο)", "d": "Μετατρέπει κείμενο σε κεφαλα<PERSON>α γράμματα"}, "VALUE": {"a": "(κείμενο)", "d": "Μετατρέπει σε αριθμό μια ακολουθία χαρακτήρων κειμένου που αναπαριστά αριθμό"}, "AVEDEV": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον μέσο όρο των απόλυτων αποκλίσεων των σημείων δεδομένων από τον μέσο τους. Τα ορίσματα μπορεί να είναι αριθμοί ή ονόματα, καθώς και πίνακες ή αναφορές που περιέχουν αριθμούς."}, "AVERAGE": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον αριθμητικό μέσο όρο των ορισμάτων του, τα οποία μπορεί να είναι αριθμοί ή ονόματα, πίνακες ή αναφορές που περιέχουν αριθμούς"}, "AVERAGEA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Αποδίδει τον μέσο όρο (αριθμητικό μέσο) των ορισμάτων του, θεωρώντας το κείμενο και τις τιμές FALSE ως 0 και τις τιμές TRUE ως 1. Τα ορίσματα μπορεί να είναι αριθμοί, ονόματα πίνακες ή αναφορές"}, "AVERAGEIF": {"a": "(περιοχή; κριτήρια; [περιοχή_μέσου_όρου])", "d": "Βρίσκει τον μέσο όρο (αριθμητικ<PERSON> μέσο όρο) για τα κελιά που καθορίζονται από μια δεδομένη συνθήκη ή κριτήριο"}, "AVERAGEIFS": {"a": "(περιοχή_μέσου_όρου; περιοχή_κριτηρίων; κριτήρια; ...)", "d": "Βρίσκει τον μέσο όρο (αριθμητικ<PERSON> μέσο όρο) για τα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων"}, "BETADIST": {"a": "(x; άλφα; βήτα; [A]; [B])", "d": "Αποδίδει τη συνάρτηση πυκνότητας αθροιστικής πιθανότητας βήτα"}, "BETAINV": {"a": "(πιθανότητα; άλφα; βήτα; [A]; [B])", "d": "Αποδίδει το αντίστροφο της συνάρτησης αθροιστικής πυκνότητας πιθανότητας βήτα (BETADIST)"}, "BETA.DIST": {"a": "(x; άλφα; βήτα; αθροιστική; [A]; [B])", "d": "Αποδίδει τη συνάρτηση κατανομής πιθανότητας βήτα"}, "BETA.INV": {"a": "(πιθανότητα; άλφα; βήτα; [A]; [B])", "d": "Αποδίδει το αντίστροφο της συνάρτησης αθροιστικής πυκνότητας πιθανότητας βήτα (BETA.DIST)"}, "BINOMDIST": {"a": "(αριθμός; δοκιμές; πιθανότητα; αθροιστική)", "d": "Αποδίδει την πιθανότητα διωνυμικής κατανομής μεμονωμένου όρου"}, "BINOM.DIST": {"a": "(αριθμός_επιτυχιών; δοκιμές; πιθανότητα_επιτυχίας; αθροιστική)", "d": "Αποδίδει την πιθανότητα διωνυμικής κατανομής μεμονωμένου όρου"}, "BINOM.DIST.RANGE": {"a": "(δοκιμές; πιθανότητα_ε; πλήθος_ε; [πλήθος_ε2])", "d": "Αποδίδει την πιθανότητα ενός αποτελέσματος δοκιμής που προκύπτει από τη χρήση διωνυμικής κατανομής"}, "BINOM.INV": {"a": "(δοκιμές; πιθανότητα_επιτυχίας; άλφα)", "d": "Αποδίδει τη μικρότερη τιμή της οποίας η αθροιστική διωνυμική κατανομή είναι μεγαλύτερη ή ίση της τιμής ενός κριτηρίου"}, "CHIDIST": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει την πιθανότητα της δεξιάς πλευράς της κατανομής Χ-τετράγωνο"}, "CHIINV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της πιθανότητας της δεξιάς πλευράς της κατανομής Χ-τετράγωνο"}, "CHITEST": {"a": "(πραγματικ<PERSON>_εύρος; αναμενόμενο_εύρος)", "d": "Επιστρέφει τον έλεγχο της ανεξαρτησίας: την τιμή από την κατανομή x-τετράγωνο για τη στατιστική και τους ανάλογους βαθμούς ελευθερίας"}, "CHISQ.DIST": {"a": "(x; βαθμοί_ελευθερίας; αθροιστική)", "d": "Αποδίδει την πιθανότητα αριστερής πλευράς της κατανομής Χ-τετράγωνο"}, "CHISQ.DIST.RT": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει την πιθανότητα δεξιάς πλευράς της κατανομής Χ-τετράγωνο"}, "CHISQ.INV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της πιθανότητας αριστερής πλευράς της κατανομής Χ-τετράγωνο"}, "CHISQ.INV.RT": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της πιθανότητας δεξιάς πλευράς της κατανομής Χ-τετράγωνο"}, "CHISQ.TEST": {"a": "(πραγματικ<PERSON>_εύρος; αναμενόμενο_εύρος)", "d": "Αποδίδει τον έλεγχο της ανεξαρτησίας: την τιμή από την κατανομή x-τετράγωνο για τη στατιστική και τους ανάλογους βαθμούς ελευθερίας"}, "CONFIDENCE": {"a": "(άλφα; τυπική_απόκλιση; μέγεθος)", "d": "Αποδίδει το διάστημα εμπιστοσύνης για τον αριθμητικό μέσο ενός πληθυσμού χρησιμοποιώντας μια κανονική κατανομή"}, "CONFIDENCE.NORM": {"a": "(άλφα; τυπική_απόκλιση; μέγεθος)", "d": "Αποδίδει το διάστημα εμπιστοσύνης για τον αριθμητικό μέσο ενός πληθυσμού, χρησιμοποιώντας κανονική κατανομή"}, "CONFIDENCE.T": {"a": "(άλφα; τυπική_απόκλιση; μέγεθος)", "d": "Αποδίδει το διάστημα εμπιστοσύνης για τον αριθμητικό μέσο ενός πληθυσμού, χρησιμοποιώντας κατανομή T Student"}, "CORREL": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το συντελεστή συσχέτισης δύο συνόλων δεδομένων"}, "COUNT": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Μετράει το πλήθος των κελιών σε μια περιοχή που περιέχει αριθμούς"}, "COUNTA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Μετράει το πλήθος των κελιών μιας περιοχής που δεν είναι κενά"}, "COUNTBLANK": {"a": "(περιοχή)", "d": "Μετρά το πλήθος των κενών κελιών σε μια καθορισμένη περιοχή"}, "COUNTIF": {"a": "(περιοχή; κριτήρια)", "d": "Μετράει το πλήθος των κελιών, σε μια περιοχή που ικανοποιεί την καθορισμένη συνθήκη"}, "COUNTIFS": {"a": "(περιοχή_κριτηρίων; κριτήρια; ...)", "d": "Μετράει τα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων"}, "COVAR": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει τη συνδιακύμανση, το μέσο όρο των γινομένων των αποκλίσεων για κάθε ζεύγος σημείων δεδομένων σε δύο σύνολα δεδομένων"}, "COVARIANCE.P": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει τη συνδιακύμανση, το μέσο όρο των γινομένων των αποκλίσεων για κάθε ζεύγος σημείων δεδομένων σε δύο σύνολα δεδομένων"}, "COVARIANCE.S": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει τη συνδιακύμανση δείγματος, το μέσο όρο των γινομένων των αποκλίσεων για κάθε ζεύγος σημείων δεδομένων σε δύο σύνολα δεδομένων"}, "CRITBINOM": {"a": "(δοκιμές; πιθανότητα; άλφα)", "d": "Αποδίδει τη μικρότερη τιμή της οποίας η αθροιστική διωνυμική κατανομή είναι μεγαλύτερη ή ίση της τιμής ενός κριτηρίου"}, "DEVSQ": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το άθροισμα των τετραγώνων των αποκλίσεων των σημείων δεδομένων από τον αριθμητικό μέσο του δείγματός τους"}, "EXPONDIST": {"a": "(x; λάμδα; αθροιστική)", "d": "Αποδίδει την εκθετική κατανομή"}, "EXPON.DIST": {"a": "(x; λάμδα; αθροιστική)", "d": "Αποδίδει την εκθετική κατανομή"}, "FDIST": {"a": "(x; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει την κατανομή πιθανοτήτων F (δεξιάς πλευράς) (βαθμός διαφοροποίησης) για δύο ομάδες δεδομένων"}, "FINV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει το αντίστροφο της κατανομής πιθανοτήτων F (δεξιάς πλευράς). Εάν p = FDIST(x,...), τότε FINV(p,...) = x."}, "FTEST": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει αποτέλεσμα ελέγχου F, τη δίπλευρη πιθανότητα ότι οι διακυμάνσεις στους πίνακες 1 και 2 δεν παρουσιάζουν σημαντικές διαφορές"}, "F.DIST": {"a": "(x; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2; αθροιστική)", "d": "Αποδίδει την κατανομή πιθανοτήτων F (αριστερής πλευράς) (βαθμός διαφοροποίησης) για δύο σύνολα δεδομένων"}, "F.DIST.RT": {"a": "(x; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει την κατανομή πιθανοτήτων F (δεξιάς πλευράς) (βαθμός διαφοροποίησης) για δύο σύνολα δεδομένων"}, "F.INV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει το αντίστροφο της κατανομής πιθανοτήτων F (αριστερής πλευράς). Εάν p = F.DIST(x,...), τότε F.INV(p,...) = x"}, "F.INV.RT": {"a": "(πιθανότητα; βαθμοί_ελευθερίας1; βαθμοί_ελευθερίας2)", "d": "Αποδίδει το αντίστροφο της κατανομής πιθανοτήτων F (δεξιάς πλευράς). Εάν p = F.DIST.RT(x,...), τότε F.INV.RT(p,...) = x."}, "F.TEST": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το αποτέλεσμα ενός ελέγχου F, τη δίπλευρη πιθανότητα ότι οι διακυμάνσεις στον πίνακα 1 και στον πίνακα 2 δεν παρουσιάζουν σημαντικές διαφορές"}, "FISHER": {"a": "(x)", "d": "Αποδίδει το μετασχηματισμ<PERSON> Fisher"}, "FISHERINV": {"a": "(y)", "d": "Αποδίδει το αντίστροφο του μετασχηματισμού Fisher: αν y = FISHER(x) τότε FISHERINV(y) = x"}, "FORECAST": {"a": "(x; γνωστά_y; γνωστά_x)", "d": "Υπολογίζει ή προβλέπει μια μελλοντική τιμή σε μια γραμμική τάση, χρησιμοποιώντας υπάρχουσες τιμές"}, "FORECAST.ETS": {"a": "(ημερομηνία_στόχου; τιμές; λωρίδα_χρόνου; [εποχικότητα]; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει την προβλεπόμενη τιμή για μια συγκεκριμένη μελλοντική ημερομηνία στόχου, με τη χρήση μιας μεθόδου εκθετικής εξομάλυνσης."}, "FORECAST.ETS.CONFINT": {"a": "(ημερομηνία_στόχου; τιμές; λωρίδα_χρόνου; [επίπεδο_εμπιστοσύνης]; [εποχικότητα]; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει ένα διάστημα εμπιστοσύνης για την τιμή πρόβλεψης κατά την καθορισμένη ημερομηνία στόχου."}, "FORECAST.ETS.SEASONALITY": {"a": "(τιμές; λωρίδα_χρόνου; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει τη διάρκεια του επαναληπτικού μοντέλου που εντοπίζεται από μια εφαρμογή για την καθορισμένη χρονική σειρά."}, "FORECAST.ETS.STAT": {"a": "(τιμές; λωρίδα_χρόνου; στατιστικ<PERSON>ς_τύπος; [εποχικότητα]; [ολοκλήρωση_δεδομένων]; [συνάθροιση])", "d": "Επιστρέφει τα ζητούμενα στατιστικά στοιχεία για την πρόβλεψη."}, "FORECAST.LINEAR": {"a": "(x; γνωστά_y; γνωστά_x)", "d": "Υπολογίζει ή προβλέπει μια μελλοντική τιμή σε μια γραμμική τάση, χρησιμοποιώντας υπάρχουσες τιμές"}, "FREQUENCY": {"a": "(πίνακ<PERSON><PERSON>_δεδομένων; πίνακας_κατηγοριών)", "d": "Υπολογίζει τη συχνότητα τιμών σε μια περιοχή και αποδίδει έναν κατακόρυφο πίνακα αριθμών, ο οπ<PERSON><PERSON>ος έχει ένα στοιχείο παραπάνω από την παράμετρο πίνακας_κατηγοριών"}, "GAMMA": {"a": "(x)", "d": "Αποδίδει την τιμή της συνάρτησης γάμμα"}, "GAMMADIST": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατανομή γάμμα"}, "GAMMA.DIST": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατανομή γάμμα"}, "GAMMAINV": {"a": "(πιθανότητα; άλφα; βήτα)", "d": "Αποδίδει το αντίστροφο της αθροιστικής κατανομής γάμμα. Εάν p = GAMMADIST(x,...), τότε GAMMAINV(p,...) = x."}, "GAMMA.INV": {"a": "(πιθανότητα; άλφα; βήτα)", "d": "Αποδίδει το αντίστροφο της αθροιστικής κατανομής γάμμα. Εάν p = GAMMADIST(x,...), τότε GAMMA.INV(p,...) = x."}, "GAMMALN": {"a": "(x)", "d": "Αποδίδει το φυσικό λογάριθμο της συνάρτησης γάμμα"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Αποδίδει το φυσικό λογάριθμο της συνάρτησης γάμμα"}, "GAUSS": {"a": "(x)", "d": "Αποδίδει κατά 0,5 λιγότερο από την τυπική κανονική αθροιστική κατανομή"}, "GEOMEAN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον γεωμετρικό μέσο ενός πίνακα ή μιας περιοχής θετικών αριθμητικών δεδομένων"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Επιστρέφει αριθμούς σε μια εκθετική τάση ανάπτυξης η οποία διέρχεται από γνωστά σημεία δεδομένων"}, "HARMEAN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τον αρμονικό μέσο μιας ομάδας θετικών αριθμών: το αντίστροφο του αριθμητικού μέσου των αντίστροφων."}, "HYPGEOM.DIST": {"a": "(δείγμα_επιτυχιών; αριθμός_δείγματος; πληθυσμός_επιτυχίες; αριθμός_πληθυσμός; αθροιστική)", "d": "Αποδίδει την υπεργεωμετρική κατανομή"}, "HYPGEOMDIST": {"a": "(δείγμα_επιτυχιών; αριθμός_δείγματος; πληθυσμός_επιτυχίες; αριθμός_πληθυσμός)", "d": "Αποδίδει την υπεργεωμετρική κατανομή"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Υπολογίζει το σημείο τομής μιας γραμμής με τον άξονα y, χρησιμοποιώντας τη γραμμή παλινδρόμησης βέλτιστης προσαρμογής που διέρχεται από τις γνωστές τιμές x και y"}, "KURT": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει την κύρτωση ενός συνόλου δεδομένων"}, "LARGE": {"a": "(πίνακας; k)", "d": "Αποδίδει την k μεγαλύτερη τιμή σε ένα σύνολο δεδομένων. Για παράδειγμα, τον πέμπτο μεγαλύτερο αριθμό"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Επιστρέφει τα στατιστικά στοιχεία που περιγράφουν μια γραμμική τάση η οποία διέρχεται από γνωστά σημεία δεδομένων, προσαρμόζοντας μια ευθεία γραμμή με τη χρήση της μεθόδου των ελαχίστων τετραγώνων"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Επιστρέφει τα στατιστικ<PERSON> στοιχεία που περιγράφουν μια εκθετική καμπύλη η οποία διέρχεται από γνωστά σημεία δεδομένων"}, "LOGINV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει την αντίστροφη κανονική λογαριθμική συνάρτηση της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους Mean και Standard_dev"}, "LOGNORM.DIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση; αθροιστική)", "d": "Αποδίδει την κανονική λογαριθμική συνάρτηση της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους μέσος_όρος και τυπική_απόκλιση"}, "LOGNORM.INV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει το αντίστροφο της κανονικής λογαριθμικής συνάρτησης της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους μέσος_όρος και τυπική_απόκλιση"}, "LOGNORMDIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει την κανονική λογαριθμική συνάρτηση της αθροιστικής κατανομής του x, όπου η ln(x) κατανέμεται κανονικά με παραμέτρους Mean και Standard_dev"}, "MAX": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη μεγαλύτερη τιμή ενός συνόλου ορισμάτων. Παραβλέπει λογικές τιμές και κείμενο."}, "MAXA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Αποδίδει τη μέγιστη τιμή ενός συνόλου τιμών. Δεν παραβλέπει λογικές τιμές και κείμενο"}, "MAXIFS": {"a": "(μεγ_περιοχή; κριτήρια_περιοχή; κριτήρια; ...)", "d": "Επιστρέφει τη μέγιστη τιμή στα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων"}, "MEDIAN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το διάμεσο ή τον αριθμό που βρίσκεται στη μέση του συνόλου των καθορισμένων αριθμών"}, "MIN": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη μικρότερη τιμή ενός συνόλου ορισμάτων. Παραβλέπει λογικές τιμές και κείμενο."}, "MINA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Αποδίδει την ελάχιστη τιμή μιας ομάδας τιμών. Δεν παραβλέπει λογικές τιμές και κείμενο"}, "MINIFS": {"a": "(ελαχ_περιοχή; κριτήρια_περιοχή; κριτήρια; ...)", "d": "Επιστρέφει την ελάχιστη τιμή στα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων"}, "MODE": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη συνηθέστερη ή συχνότερα επαναλαμβανόμενη τιμή σε έναν πίνακα ή μια περιοχή δεδομένων"}, "MODE.MULT": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει έναν κατακόρυφο πίνακα των συνηθέστερων ή συχνότερα επαναλαμβανόμενων τιμών σε έναν πίνακα ή μια περιοχή δεδομένων.  Για οριζόντιο πίνακα, χρησιμοποιήστε =TRANSPOSE(MODE.MULT(αριθμός1,αριθμός2,...))"}, "MODE.SNGL": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη συνηθέστερη ή συχνότερα επαναλαμβανόμενη τιμή σε έναν πίνακα ή μια περιοχή δεδομένων"}, "NEGBINOM.DIST": {"a": "(αριθμός_αποτυχιών; αριθμός_επιτυχιών; πιθανότητα_επιτυχίας; αθροιστική)", "d": "Αποδίδει την αρνητική διωνυμική κατανομή, την πιθανότητα να υπάρχουν αριθμός_αποτυχιών αποτυχίες πριν από τις αριθμός_επιτυχιών επιτυχίες, με πιθανότητα_επιτυχίας πιθανότητα επιτυχίας"}, "NEGBINOMDIST": {"a": "(αριθμός_αποτυχιών; αριθμός_επιτυχιών; πιθανότητα_επιτυχίας)", "d": "Αποδίδει αρνητική διωνυμική κατανομή, την πιθανότητα να υπάρχει αριθμός_αποτυχιών αποτυχιών πριν από το τακτικό αριθμητικό επιτυχιών, με την πιθανότητα_επιτυχίας πιθανότητα επιτυχίας"}, "NORM.DIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση; αθροιστική)", "d": "Αποδίδει την κανονική κατανομή για τον αριθμητικό μέσο και τη μέση απόκλιση τετραγώνου που καθορίστηκαν"}, "NORMDIST": {"a": "(x; μέση_τιμή; τυπική_απόκλιση; αθροιστική)", "d": "Αποδίδει την κανονική αθροιστική κατανομή για τον αριθμητικό μέσο και τη μέση απόκλιση τετραγώνου που καθορίστηκαν"}, "NORM.INV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει το αντίστροφο της κανονικής αθροιστικής κατανομής για τον αριθμητικό μέσο και την τυπική απόκλιση που καθορίσατε"}, "NORMINV": {"a": "(πιθανότητα; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει το αντίστροφο της κανονικής αθροιστικής κατανομής για τον αριθμητικό μέσο και την τυπική απόκλιση που καθορίσατε"}, "NORM.S.DIST": {"a": "(z; αθροιστική)", "d": "Αποδίδει την τυπική κανονική κατανομή (ο αριθμητικός μέσος ισούται με μηδέν και η τυπική απόκλιση με ένα)"}, "NORMSDIST": {"a": "(z)", "d": "Αποδίδει την τυπική κανονική αθροιστική κατανομή (ο αριθμητικός μέσος ισούται με μηδέν και η τυπική απόκλιση με ένα)"}, "NORM.S.INV": {"a": "(πιθανότητα)", "d": "Αποδίδει το αντίστροφο της τυπικής κανονικής αθροιστικής κατανομής (ο αριθμητικός μέσος ισούται με μηδέν και η μέση απόκλιση με ένα)"}, "NORMSINV": {"a": "(πιθανότητα)", "d": "Αποδίδει το αντίστροφο της τυπικής κανονικής αθροιστικής κατανομής (ο αριθμητικός μέσος ισούται με μηδέν και η μέση απόκλιση με ένα)"}, "PEARSON": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το συντελεστή συσχέτιση<PERSON> Pearson του γινομένου των ροπών, r"}, "PERCENTILE": {"a": "(πίνακας; k)", "d": "Αποδίδει το k εκατοστημόριο τιμών μιας περιοχής"}, "PERCENTILE.EXC": {"a": "(πίνακας; k)", "d": "Αποδίδει το k εκατοστημόριο τιμών μιας περιοχής, όπου k είναι στο ανοιχτό διάστημα 0..1"}, "PERCENTILE.INC": {"a": "(πίνακας; k)", "d": "Αποδίδει το k εκατοστημόριο τιμών μιας περιοχής, όπου k είναι η κλειστή περιοχή 0..1"}, "PERCENTRANK": {"a": "(πίνακας; x; [σημαντικότητα])", "d": "Αποδίδει τη σειρά κατάταξης μιας τιμής ως ποσοστό επί του συνόλου δεδομένων"}, "PERCENTRANK.EXC": {"a": "(πίνακας; x; [σημαντικότητα])", "d": "Αποδίδει τη σειρά κατάταξης μιας τιμής σε ένα σύνολο δεδομένων ως ποσοστό του συνόλου δεδομένων ως ποσοστό (ανοιχτό διάστημα 0..1) του συνόλου δεδομένων"}, "PERCENTRANK.INC": {"a": "(πίνακας; x; [σημαντικότητα])", "d": "Αποδίδει τη σειρά κατάταξης μιας τιμής σε ένα σύνολο δεδομένων ως ποσοστό του συνόλου δεδομένων ως ποσοστό (στο κλειστό διάστημα 0..1) του συνόλου δεδομένων"}, "PERMUT": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό των διατάξεων για δεδομένο αριθμό αντικειμένων, τα οποία μπορούν να επιλεχθούν από το σύνολο των αντικειμένων"}, "PERMUTATIONA": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό των μεταθέσεων για δεδομένο αριθμό αντικειμένων (με επαναλήψεις), τα οποία μπορούν να επιλεχθούν από το σύνολο των αντικειμένων"}, "PHI": {"a": "(x)", "d": "Αποδίδει την τιμή της συνάρτησης πυκνότητας για μια τυπική κανονική κατανομή"}, "POISSON": {"a": "(x; μέση_τιμή; αθροιστική)", "d": "Αποδίδει την κατανομή Poisson"}, "POISSON.DIST": {"a": "(x; μέση_τιμή; αθροιστική)", "d": "Αποδίδει την κατανομή Poisson"}, "PROB": {"a": "(x_εύρος; εύρος_πιθανοτήτων; κατώτερο_όριο; [ανώτερο_όριο])", "d": "Αποδίδει την πιθανότητα ότι οι τιμές μιας περιοχής βρίσκονται μεταξύ δύο ορίων ή ότι είναι ίσες με ένα κατώτερο όριο"}, "QUARTILE": {"a": "(πίνακας; τεταρτημόριο)", "d": "Αποδίδει το τεταρτημόρι<PERSON> ενός συνόλου δεδομένων"}, "QUARTILE.INC": {"a": "(πίνακας; τεταρτημόριο)", "d": "Αποδίδει το τεταρτημόρι<PERSON> ενός συνόλου δεδομένων, βάσει τιμών εκατοστημορίου στο κλειστό διάστημα 0..1"}, "QUARTILE.EXC": {"a": "(πίνακας; τεταρτημόριο)", "d": "Αποδίδει το τεταρτημόρι<PERSON> ενός συνόλου δεδομένων, βάσει τιμών εκατοστημορίου στο ανοιχτό διάστημα 0..1"}, "RANK": {"a": "(αριθμός; αναφορά; [σειρά])", "d": "Αποδίδει τη σειρά ενός αριθμού μέσα σε μια λίστα αριθμών. Η τιμή είναι σχετική με άλλες τιμές στη λίστα."}, "RANK.AVG": {"a": "(αριθμός; αναφορά; [σειρά])", "d": "Αποδίδει τη σειρά κατάταξης ενός αριθμού σε μια λίστα αριθμών: το μέγεθός του σε σχέση με τις άλλες τιμές της λίστας. Εάν περισσότερες από μία τιμές έχουν την ίδια σειρά κατάταξης, αποδ<PERSON>δεται η μέση σειρά κατάταξης"}, "RANK.EQ": {"a": "(αριθμός; αναφορά; [σειρά])", "d": "Αποδίδει τη σειρά κατάταξης ενός αριθμού σε μια λίστα αριθμών: το μέγεθός του σε σχέση με τις άλλες τιμές της λίστας. Εάν περισσότερες από μία τιμές έχουν την ίδια σειρά κατάταξης, απο<PERSON><PERSON>δ<PERSON>ται η ανώτατη θέση κατάταξης αυτού του συνόλου τιμών"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Επιστρέφει το τετράγωνο του συντελεστή συσχέτισης Pearson του γινομένου ροπών μέσω των καθορισμένων σημείων δεδομένων"}, "SKEW": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει την ασυμμετρία μιας κατανομής: ένα<PERSON> χαρακτηρισμός του βαθμού ασυμμετρίας μιας κατανομής γύρω από τον αριθμητικό μέσο της."}, "SKEW.P": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει τη λοξότητα μιας κατανομής με βάση έναν πληθυσμό: χαρακτηρισμός του βαθμού ασυμμετρίας μιας κατανομής γύρω από το μέσο όρο της"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Επιστρέφει την κλίση της γραμμής γραμμικής παλινδρόμησης που διέρχεται από τα καθορισμένα σημεία δεδομένων"}, "SMALL": {"a": "(πίνακας; k)", "d": "Αποδίδει την k μικρότερη τιμή σε ένα σύνολο δεδομένων. Για παράδειγμα, τον πέμπτο μικρότερο αριθμό"}, "STANDARDIZE": {"a": "(x; μέση_τιμή; τυπική_απόκλιση)", "d": "Αποδίδει μια κανονικοποιημένη τιμή από μια κατανομή, η οποία χαρακτηρίζεται από έναν αριθμητικό μέσο και μια μέση απόκλιση τετραγώνου"}, "STDEV": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη μέση απόκλιση τετραγώνου βάσει ενός δείγματος (παραβλέπει τις λογικές τιμές και το κείμενο μέσα στο δείγμα)"}, "STDEV.P": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει την τυπική απόκλιση βάσει ολόκληρου του πληθυσμού των ορισμάτων (παραβλέπει λογικές τιμές και κείμενο)"}, "STDEV.S": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Εκτιμά την τυπική απόκλιση με βάση ένα δείγμα (παραβλέπει λογικές τιμές και κείμενο στο δείγμα)"}, "STDEVA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Εκτιμά την τυπική απόκλιση βάσει δείγματος, μαζί με λογικές τιμές και κείμενο. Κείμενο και λογική τιμή FALSE = 0, λογική τιμή TRUE = 1"}, "STDEVP": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει την τυπική απόκλιση βάσει ολόκληρου του πληθυσμού των ορισμάτων (παραβλέπει λογικές τιμές και κείμενο)"}, "STDEVPA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Υπολογίζει την τυπική απόκλιση βάσει όλου του πληθυσμού, μαζί με λογικές τιμές και κείμενο. Κείμενο και λογική τιμή FALSE = 0, λογική τιμή TRUE = 1."}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Επιστρέφει το τυπικό σφάλμα της προβλεπόμενης τιμής y για κάθε x σε μια παλινδρόμηση"}, "TDIST": {"a": "(x; βαθμοί_ελευθερίας; ουρές)", "d": "Αποδίδει την κατανομή t Student"}, "TINV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο της κατανομής δύο ουρών t Student"}, "T.DIST": {"a": "(x; βαθμοί_ελευθερίας; αθροιστική)", "d": "Αποδίδει την κατανομή t Student αριστερής πλευράς"}, "T.DIST.2T": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει τη δίπλευρη κατανομή t Student"}, "T.DIST.RT": {"a": "(x; βαθμοί_ελευθερίας)", "d": "Αποδίδει την κατανομή t Student δεξιάς πλευράς"}, "T.INV": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το αντίστροφο αριστερής πλευράς της κατανομής t Student"}, "T.INV.2T": {"a": "(πιθανότητα; βαθμοί_ελευθερίας)", "d": "Αποδίδει το δίπλευρο αντίστροφο της κατανομής t Student"}, "T.TEST": {"a": "(πίνακας1; πίνακας2; ουρές; τύπος)", "d": "Αποδίδει την πιθανότητα που σχετίζεται με έναν έλεγχο t Student"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Επιστρέφει αριθμούς σε μια γραμμική τάση που διέρχεται από γνωστά σημεία δεδομένων, χρησιμοποιώντας τη μέθοδο των ελαχίστων τετραγώνων"}, "TRIMMEAN": {"a": "(πίνακας; ποσοστό)", "d": "Αποδίδει τον αριθμητικό μέσο του εσωτερικού μιας ομάδας τιμών δεδομένων"}, "TTEST": {"a": "(πίνακας1; πίνακας2; ουρές; τύπος)", "d": "Αποδίδει την πιθανότητα που σχετίζεται με έναν έλεγχο t Student"}, "VAR": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει ενός δείγματος (παραβλέπει λογικές τιμές και κείμενο στο δείγμα)"}, "VAR.P": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει ολόκληρου του πληθυσμού (παραβλέπει λογικές τιμές και κείμενο στον πληθυσμό)"}, "VAR.S": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Εκτιμά τη διακύμανση βάσει ενός δείγματος (παραβλέπει λογικές τιμές και κείμενο στο δείγμα)"}, "VARA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Εκτιμά τη διακύμανση βάσει ενός δείγματος, συμπεριλαμβάνοντας τις λογικές τιμές και το κείμενο. Το κείμενο και η λογική τιμή FALSE αντιστοιχούν στο 0, ενώ η λογική τιμή TRUE αντιστοιχεί στην τιμή 1"}, "VARP": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει ολόκληρου του πληθυσμού (παραβλέπει λογικές τιμές και κείμενο στον πληθυσμό)"}, "VARPA": {"a": "(τιμή1; [τιμή2]; ...)", "d": "Υπολογίζει τη διακύμανση βάσει όλου του πληθυσμού, μαζί με λογικές τιμές και κείμενο. Κείμενο και λογική τιμή FALSE = 0, λογική τιμή TRUE = 1"}, "WEIBULL": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατα<PERSON><PERSON><PERSON><PERSON>"}, "WEIBULL.DIST": {"a": "(x; άλφα; βήτα; αθροιστική)", "d": "Αποδίδει την κατα<PERSON><PERSON><PERSON><PERSON>"}, "Z.TEST": {"a": "(πίνακας; x; [σίγμα])", "d": "Αποδίδει τη μονόπλευρη τιμή P ενός ελέγχου z"}, "ZTEST": {"a": "(πίνακας; x; [σίγμα])", "d": "Αποδίδει τη μονόπλευρη τιμή P ενός ελέγχου z"}, "ACCRINT": {"a": "(έκδοση; πρώτο_επιτόκιο; διακανονισμός; επιτ<PERSON>κι<PERSON>; ονομαστική_αξία; συχνότητα; [βάση]; [μέθοδος_υπολογισμού])", "d": "Αποδίδει τον πληρωτέο τόκο ενός χρεογράφου που δίνει περιοδικό τόκο."}, "ACCRINTM": {"a": "(έκδοση; διακανονισμός; επιτ<PERSON>κι<PERSON>; ονομαστική_αξία; [βάση])", "d": "Αποδίδει τον πληρωτέο τόκο ενός χρεογράφου που δίνει τόκο κατά τη λήξη"}, "AMORDEGRC": {"a": "(κόστος; ημερομηνία_αγοράς; πρώτη_περίοδος; υπολειμματική; περίοδος; συντελεστής; [βάση])", "d": "Αποδίδει την αναλογική γραμμική απόσβεση περιουσιακού στοιχείου για κάθε λογιστική περίοδο"}, "AMORLINC": {"a": "(κόστος; ημερομηνία_αγοράς; πρώτη_περίοδος; υπολειμματική; περίοδος; συντελεστής; [βάση])", "d": "Αποδίδει την αναλογική γραμμική απόσβεση περιουσιακού στοιχείου για κάθε λογιστική περίοδο"}, "COUPDAYBS": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των ημερών από την αρχή της περιόδου τοκομεριδίων μέχρι την ημερομηνία διακανονισμού"}, "COUPDAYS": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των ημερών της περιόδου τοκομεριδίων που περιλαμβάνει την ημερομηνία διακανονισμού"}, "COUPDAYSNC": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των ημερών από την ημερομηνία διακανονισμού έως την ημερομηνία του επόμενου τοκομεριδίου"}, "COUPNCD": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει την επόμενη ημερομηνία τοκομεριδίου μετά την ημερομηνία διακανονισμού"}, "COUPNUM": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει τον αριθμό των πληρωτέων τοκομεριδίων μεταξύ της ημερομηνίας διακανονισμού και της ημερομηνίας λήξης"}, "COUPPCD": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; συχνότητα; [βάση])", "d": "Αποδίδει την ημερομηνία του προηγούμενου τοκομεριδίου πριν από την ημερομηνία διακανονισμού"}, "CUMIPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; αριθμός_περιόδων; παρούσα_αξία; περίοδος_έναρξης; περίοδος_λήξης; τύπος)", "d": "Αποδίδει τον σωρευτικό τόκο πληρωμής μεταξύ δύο περιόδων"}, "CUMPRINC": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; αριθμός_περιόδων; παρούσα_αξία; περίοδος_έναρξης; περίοδος_λήξης; τύπος)", "d": "Αποδίδει το σωρευτικ<PERSON> κεφάλαιο πληρωμής μεταξύ δύο περιόδων"}, "DB": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος; [μήνας])", "d": "Αποδίδει την απόσβεση περιουσιακού στοιχείου για καθορισμένη περίοδο με τη μέθοδο του σταθερά φθίνοντος υπολοίπου"}, "DDB": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος; [παράγοντας])", "d": "Αποδίδει την απόσβεση περιουσιακού στοιχείου για μια συγκεκριμένη περίοδο, με τη μέθοδο του διπλά φθίνοντος υπολοίπου ή με άλλη μέθοδο που καθορίζετε"}, "DISC": {"a": "(διακαν<PERSON><PERSON><PERSON>σμ<PERSON><PERSON>; λήξη; τιμή; εξαργύρωση; [βάση])", "d": "Αποδίδει το προεξοφλητικ<PERSON> επιτόκιο ενός χρεογράφου"}, "DOLLARDE": {"a": "(κλασματικ<PERSON>ς_δολάριο; κλάσμα)", "d": "Μετατρέπει μια τιμή δολαρίων εκφρασμένη με κλάσμα, σε τιμή δολαρίων εκφρασμένη σε δεκαδικό αριθμό"}, "DOLLARFR": {"a": "(δεκαδι<PERSON><PERSON><PERSON>_δολάριο; κλάσμα)", "d": "Μετατρέπει μια τιμή δολαρίων εκφρασμένη σε δεκαδικό αριθμό, σε τιμή δολαρίων εκφρασμένη σε κλάσμα"}, "DURATION": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; τοκομερίδιο; απόδοση; συχνότητα; [βάση])", "d": "Αποδίδει την ετήσια διάρκεια ενός χρεογράφου με πληρωμές περιοδικού τόκου"}, "EFFECT": {"a": "(ονομαστι<PERSON><PERSON>_επιτόκιο; αριθμός_περιόδων_ανά_έτος)", "d": "Αποδίδει το πραγματικό ετήσιο επιτόκιο"}, "FV": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; αριθμός_περιόδων; πληρωμή; [παρούσα_αξία]; [τύπος])", "d": "Αποδίδει τη μελλοντική αξία μιας επένδυσης βάσει περιοδικών, σταθερών πληρωμών και ενός σταθερού επιτοκίου"}, "FVSCHEDULE": {"a": "(κεφά<PERSON><PERSON><PERSON><PERSON>; χρονοδιάγραμμα)", "d": "Αποδίδει τη μελλοντική αξία ενός αρχικού κεφαλαίου μετά την εφαρμογή μιας σειράς επιτοκίων ανατοκισμού"}, "INTRATE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; επένδυση; εξαργύρωση; [βάση])", "d": "Αποδίδει το επιτό<PERSON>ι<PERSON> ενός πλήρως επενδεδυμένου χρεογράφου"}, "IPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; περίοδος; αριθμός_περιόδων; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει τον τόκο μιας επένδυσης για μια δεδομένη περίοδο βάσει περιοδικών, σταθερών πληρωμών και ενός σταθερού επιτοκίου"}, "IRR": {"a": "(τιμές; [εκτίμηση])", "d": "Αποδίδει τον εσωτερικό ρυθμό απόδοσης μιας σειράς ταμειακών ροών"}, "ISPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; περίοδος; αριθμός_περιόδων; παρούσα_αξία)", "d": "Επιστρέφει τον τόκο που καταβάλλεται σε μια συγκεκριμένη περίοδο μιας επένδυσης"}, "MDURATION": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; τοκομερίδιο; απόδοση; συχνότητα; [βάση])", "d": "Αποδίδει την τροποποιημένη διάρκε<PERSON><PERSON>ley για ένα χρεόγραφο με υποθετική ονομαστική αξία 100 €"}, "MIRR": {"a": "(τιμές; απόδοση; επιτόκιο_επανεπένδυσης)", "d": "Αποδίδει τον εσωτερικό ρυθμό απόδοσης για μια σειρά περιοδικών ταμειακών ροών, λα<PERSON><PERSON><PERSON>νοντας υπόψη το κόστος της επένδυσης και του επιτοκίου κατά την εκ νέου επένδυση των ροών"}, "NOMINAL": {"a": "(πραγματι<PERSON><PERSON>_επιτόκιο; αριθμός_περιόδων_ανά_έτος)", "d": "Αποδίδει το ετήσιο ονομαστικ<PERSON> επιτόκιο"}, "NPER": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; πληρωμή; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει το πλήθος των περιόδων μιας επένδυσης, β<PERSON><PERSON><PERSON><PERSON> περιοδικών, σταθ<PERSON>ρών πληρωμών και ενός σταθερού επιτοκίου"}, "NPV": {"a": "(επιτόκι<PERSON>; αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει την καθαρή παρούσα αξία μιας επένδυσης, β<PERSON><PERSON><PERSON><PERSON> ενός προεξοφλητικού επιτοκίου και μιας σειράς από μελλοντικές πληρωμές (αρνητικές τιμές) και εισοδήματα (θετικές τιμές)"}, "ODDFPRICE": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; έκδοση; πρώτο_τοκομερίδιο; επιτόκιο; απόδοση; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας για ένα χρεόγραφο με μη τακτική πρώτη περίοδο"}, "ODDFYIELD": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; έκδοση; πρώτο_τοκομερίδιο; επιτόκιο; τιμή; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την απόδοση ενός χρεογράφου με μη τακτική πρώτη περίοδο"}, "ODDLPRICE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; τελευτα<PERSON><PERSON>; επιτόκι<PERSON>; απόδοση; αποζημίωση; συχνότητα; [βάση])", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας ενός χρεογράφου με μη τακτική τελευταία περίοδο"}, "ODDLYIELD": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON>μ<PERSON>ς; λήξη; τελευτα<PERSON><PERSON>; επιτόκιο; τιμή; αποζημίωση; συχνότητα; [βάση])", "d": "Αποδίδει την απόδοση ενός χρεογράφου με μη τακτική τελευταία περίοδο"}, "PDURATION": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; παρούσα_αξία; μελλοντική_αξία)", "d": "Αποδίδει τον αριθμό περιόδων που απαιτούνται προκειμένου μια επένδυση να φτάσει σε μια καθορισμένη τιμή"}, "PMT": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; πληρωμή; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει την πληρωμή για ένα δάνειο, βά<PERSON><PERSON>ι σταθερών πληρωμών και ενός σταθερού επιτοκίου"}, "PPMT": {"a": "(επιτ<PERSON><PERSON><PERSON><PERSON>; περίοδος; αριθμός_περιόδων; παρούσα_αξία; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει την πληρωμή επί του αρχικού κεφαλαίου μιας επένδυσης, βά<PERSON><PERSON><PERSON> περιοδικών, σταθ<PERSON><PERSON><PERSON><PERSON> πληρωμών και ενός σταθερού επιτοκίου"}, "PRICE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; επιτ<PERSON><PERSON>ι<PERSON>; απόδοση; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας ενός χρεογράφου που δίνει περιοδικό τόκο"}, "PRICEDISC": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; προεξόφληση; εξαργύρωση; [βάση])", "d": "Αποδίδει την τιμή ανά 100 €   ονομαστικής αξίας ενός προεξοφληθέντος χρεογράφου"}, "PRICEMAT": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; έκδοση; επιτόκιο; απόδοση; [βάση])", "d": "Αποδίδει τιμή ανά 100 € ονομαστικής αξίας χρεογράφου που δίνει περιοδικό τόκο"}, "PV": {"a": "(επιτ<PERSON><PERSON>ι<PERSON>; αριθμός_περιόδων; πληρωμή; [μελλοντική_αξία]; [τύπος])", "d": "Αποδίδει την παρούσα αξία μιας επένδυσης, δηλαδή το συνολικό ποσό στο οποίο ανέρχεται αυτήν τη στιγμή μια σειρά μελλοντικών πληρωμών"}, "RATE": {"a": "(αριθμός_περιόδων; πληρωμή; παρούσα_αξία; [μελλοντική_αξία]; [τύπος]; [εκτίμηση])", "d": "Αποδίδει το επιτόκιο ενός δανείου ή μιας επένδυσης ανά περίοδο. Για παράδειγμα, χρησιμοποιήστε 6%/4 για τριμηνιαίες πληρωμές με Ετήσιο Ποσοστό Επιβαρύνσεων 6%"}, "RECEIVED": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; επένδυση; προεξόφληση; [βάση])", "d": "Αποδίδει το ποσό που παραλήφθηκε κατά τη λήξη ενός πλήρως επενδεδυμένου χρεογράφου"}, "RRI": {"a": "(αριθμός_περιόδων; παρούσα_αξία; μελλοντική_αξία)", "d": "Αποδίδει το ισοδύναμο επιτόκιο για την αύξηση μιας επένδυσης"}, "SLN": {"a": "(κόστος; υπολειμματική; ζωή)", "d": "Αποδίδει τη σταθερή απόσβεση ενός περιουσιακού στοιχείου για μία περίοδο"}, "SYD": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος)", "d": "Αποδίδει την απόσβεση ενός περιουσιακού στοιχείου για μια καθορισμένη περίοδο με τη μέθοδο του αθροίσματος των ετών της ζωής του"}, "TBILLEQ": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; προεξόφληση)", "d": "Αποδίδει την απόδοση που είναι ισοδύναμη με τις ομολογίες ενός εντόκου γραμματίου"}, "TBILLPRICE": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; προεξόφληση)", "d": "Αποδίδει την τιμή ανά 100 € ονομαστικής αξίας για ένα έντοκο γραμμάτιο"}, "TBILLYIELD": {"a": "(διακανονισμός; λήξη; τιμή)", "d": "Αποδίδει την απόδοση ενός εντόκου γραμματίου"}, "VDB": {"a": "(κόστος; υπολειμματική; ζωή; περίοδος_έναρξης; περίοδος_λήξης; [παράγοντας]; [χωρίς_μετάβαση])", "d": "Αποδίδει την απόσβεση περιουσιακού στοιχείου για καθορισμένη περίοδο ή τμήμα περιόδου, με τη μέθοδο φθίνοντος υπολοίπου ή με άλλη μέθοδο που καθορίζετε"}, "XIRR": {"a": "(τιμές; ημερομηνίες; [εκτίμηση])", "d": "Αποδίδει το εσωτερικό ρυθμό απόδοσης για ένα χρονοδιάγραμμα ταμειακών ροών"}, "XNPV": {"a": "(επιτόκι<PERSON>; τιμές; ημερομηνίες)", "d": "Αποδίδει την καθαρή παρούσα αξία ενός χρονοδιαγράμματος"}, "YIELD": {"a": "(διακαν<PERSON><PERSON><PERSON><PERSON>μ<PERSON><PERSON>; λήξη; επιτόκιο; τιμή; εξαργύρωση; συχνότητα; [βάση])", "d": "Αποδίδει την απόδοση ενός χρεογράφου που δίνει περιοδικό τόκο"}, "YIELDDISC": {"a": "(διακαν<PERSON><PERSON><PERSON>σμ<PERSON><PERSON>; λήξη; τιμή; εξαργύρωση; [βάση])", "d": "Αποδίδει την ετήσια απόδοση ενός προεξοφλημένου χρεογράφου. Για παράδειγμα, έ<PERSON><PERSON> <PERSON>ντοκο γραμμάτιο"}, "YIELDMAT": {"a": "(διακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; λήξη; έκδοση; επιτόκιο; παρούσα_αξία; [βάση])", "d": "Αποδίδει την ετήσια απόδοση ενός χρεογράφου που δίνει τόκο κατά τη λήξη"}, "ABS": {"a": "(αριθμός)", "d": "Αποδίδει την απόλυτη τιμή ενός αριθμού, ενός αριθμού χωρίς πρόσημο"}, "ACOS": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο συνημίτονου ενός αριθμού σε ακτίνια από το 0 έως το π. Το τόξο συνημίτονου είναι η γωνία της οποίας το συνημίτονο είναι αριθμός"}, "ACOSH": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο υπερβολικού συνημίτονου ενός αριθμού"}, "ACOT": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο συνεφαπτομένης ενός αριθμού, σε ακτίνια στην περιοχή 0 έως π."}, "ACOTH": {"a": "(αριθμός)", "d": "Αποδίδει την αντίστροφη υπερβολική συνεφαπτομένη ενός αριθμού"}, "AGGREGATE": {"a": "(αριθμός_συνάρτησης; επιλογές; αναφορά1; ...)", "d": "Αποδίδει ένα συγκεντρωτικ<PERSON> αποτέλεσμα σε μια λίστα ή βάση δεδομένων"}, "ARABIC": {"a": "(κείμενο)", "d": "Μετατρέπει ένα ρωμαϊκό νούμερο σε αραβικά ψηφία"}, "ASC": {"a": "(κείμενο)", "d": "Σε γλώσσες με σύνολα χαρακτήρων διπλού byte (Double-Byte Character Set - DBCS), μετατρέπει τους χαρακτήρες πλήρους πλάτους (δύο byte) σε χαρακτήρες μισού πλάτους (ενός byte)"}, "ASIN": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο ημίτονου ενός αριθμού σε ακτίνια, με πεδίο τιμών από το -π/2 έως το π/2"}, "ASINH": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο υπερβολικού ημίτονου ενός αριθμού"}, "ATAN": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο εφαπτομένης ενός αριθμού σε ακτίνια, με πεδίο τιμών από το -π/2 έως το π/2"}, "ATAN2": {"a": "(αριθμός_x; αριθμός_y)", "d": "Αποδίδει το τόξο εφαπτομένης των καθορισμένων συντεταγμένων x και y σε ακτίνια, με πεδίο τιμών από -π έως π, εκτός του -π"}, "ATANH": {"a": "(αριθμός)", "d": "Αποδίδει το τόξο υπερβολικής εφαπτομένης ενός αριθμού"}, "BASE": {"a": "(αριθμός; βάση; [ελάχιστο_μήκος])", "d": "Μετατρέ<PERSON><PERSON><PERSON> έναν αριθμό σε αναπαράσταση κειμένου με την δεδομένη βάση"}, "CEILING": {"a": "(αριθμός; σημαντικότητα)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα πάνω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο"}, "CEILING.MATH": {"a": "(αριθμός; [σημαντικότητα]; [τρόπος])", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα επάνω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο πολλαπλάσιο των σημαντικών ψηφίων"}, "CEILING.PRECISE": {"a": "(αριθμός; [σημαντικότητα])", "d": "Επιστρέφει έναν αριθμό που είναι στρογγυλοποιημένος προς τα επάνω στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο"}, "COMBIN": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό συνδυασμών για δεδομένο πλήθος στοιχείων"}, "COMBINA": {"a": "(αριθμός; επιλεγμένος_αριθμός)", "d": "Αποδίδει τον αριθμό συνδυασμών με επαναλήψεις για δεδομένο πλήθος στοιχείων"}, "COS": {"a": "(αριθμός)", "d": "Αποδίδει το συνημίτονο μιας γωνίας"}, "COSH": {"a": "(αριθμός)", "d": "Αποδίδει το υπερβολικό συνημίτονο ενός αριθμού"}, "COT": {"a": "(αριθμός)", "d": "Αποδίδει την συνεφαπτομένη μιας γωνίας"}, "COTH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική συνεφαπτομένη ενός αριθμού"}, "CSC": {"a": "(αριθμός)", "d": "Αποδίδει τη συντέμνουσα μιας γωνίας"}, "CSCH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική συντέμνουσα μιας γωνίας"}, "DECIMAL": {"a": "(αριθμός; βάση)", "d": "Μετατρέπει μια αναπαράσταση κειμένου ενός αριθμού δεδομένης βάσης σε δεκαδικό αριθμό"}, "DEGREES": {"a": "(γωνία)", "d": "Μετατρέπει τα ακτίνια σε μοίρες"}, "ECMA.CEILING": {"a": "(αριθμός; σημαντικότητα)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα πάνω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο"}, "EVEN": {"a": "(αριθμός)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν θετικό αριθμό προς τα επάνω και έναν αρνητικό αριθμό προς τα κάτω στον πλησιέστερο άρτιο ακέραιο"}, "EXP": {"a": "(αριθμός)", "d": "Αποδίδει το e υψωμένο στη δύναμη του καθορισμένου αριθμού"}, "FACT": {"a": "(αριθμός)", "d": "Αποδίδει το παραγοντικ<PERSON> ενός αριθμού που ισοδυναμεί με 1*2*3*...*αριθμός"}, "FACTDOUBLE": {"a": "(αριθμός)", "d": "Αποδίδει το διπλό παραγοντικ<PERSON> ενός αριθμού"}, "FLOOR": {"a": "(αριθμός; σημαντικότητα)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα κάτω, προς το πλησιέστερο σημαντικό πολλαπλάσιο"}, "FLOOR.PRECISE": {"a": "(αριθμός; [σημαντικότητα] )", "d": "Επιστρέφει έναν αριθμό που είναι στρογγυλοποιημένος προς τα κάτω στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο"}, "FLOOR.MATH": {"a": "(αριθμός; [σημαντικότητα]; [τρόπος])", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα κάτω, στον πλησιέστερο ακέραιο ή στο πλησιέστερο πολλαπλάσιο των σημαντικών ψηφίων"}, "GCD": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Επιστρέφει τον μέγιστο κοινό διαιρέτη"}, "INT": {"a": "(αριθμός)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> προς τα κάτω έναν αριθμό στον πλησιέστερο ακέραιο"}, "ISO.CEILING": {"a": "(αριθμός; [σημαντικότητα])", "d": "Επιστρέφει έναν αριθμό που είναι στρογγυλοποιημένος προς τα επάνω στον πλησιέστερο ακέραιο ή στο πλησιέστερο σημαντικό πολλαπλάσιο. Ανεξάρτητα από το πρόσημο του αριθμού, ο αριθμός στρογγυλοποιείται προς τα επάνω. Εάν το όρισμα αριθμός ή το όρισμα σημαντικότητα είναι μηδέν, η συνάρτηση επιστρέφει την τιμή μηδέν."}, "LCM": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Επιστρέφει τον ελάχιστο κοινό πολλαπλάσιο"}, "LN": {"a": "(αριθμός)", "d": "Αποδίδει το φυσικό λογάριθμο ενός αριθμού"}, "LOG": {"a": "(αριθμός; [βάση])", "d": "Αποδίδει το λογάριθμο ενός αριθμού με την καθορισμένη βάση"}, "LOG10": {"a": "(αριθμός)", "d": "Αποδίδει τον δεκαδικό λογάριθμο ενός αριθμού"}, "MDETERM": {"a": "(πίνακας)", "d": "Αποδίδει την ορίζουσα ενός πίνακα"}, "MINVERSE": {"a": "(πίνακας)", "d": "Αποδίδει τον ανάστροφο ενός επιλεγμένου πίνακα"}, "MMULT": {"a": "(πίνακας1; πίνακας2)", "d": "Αποδίδει το γινόμενο δύο πινάκων, έν<PERSON>ν πίνακα που διαθέτει το ίδιο πλήθος γραμμών με τον πίνακα 1 και τον ίδιο αριθμό στηλών με τον πίνακα 2"}, "MOD": {"a": "(αριθμός; διαιρέτης)", "d": "Αποδίδει το υπόλοιπο της διαίρεσης"}, "MROUND": {"a": "(αριθμός; πολλαπλάσιο)", "d": "Αποδίδει έναν αριθμό στρογγυλοποιημένο στο επιθυμητό πολλαπλάσιο"}, "MULTINOMIAL": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Επιστρέφει το πολυώνυμο ενός συνόλου αριθμών"}, "MUNIT": {"a": "(διάσταση)", "d": "Αποδίδει το μοναδι<PERSON><PERSON><PERSON> πίνακα για την καθορισμένη διάσταση"}, "ODD": {"a": "(αριθμός)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν θετικό αριθμό προς τα επάνω και έναν αρνητικό αριθμό προς τα κάτω, στον πλησιέστερο περιττό ακέραιο"}, "PI": {"a": "()", "d": "Αποδίδει την τιμή του π, 3,14159265358979, με ακρίβεια 15 ψηφίων"}, "POWER": {"a": "(αριθμός; δύναμη)", "d": "Αποδίδει το αποτέλεσμα ενός αριθμού υψωμένου σε δύναμη"}, "PRODUCT": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το γινόμενο όλων των αριθμών που έχουν εισαχθεί ως ορίσματα"}, "QUOTIENT": {"a": "(αριθμητής; παρονομαστής)", "d": "Αποδίδει το ακέραιο τμήμα μιας διαίρεσης"}, "RADIANS": {"a": "(γωνία)", "d": "Μετατρέπει τις μοίρες σε ακτίνια"}, "RAND": {"a": "()", "d": "Αποδίδει έναν τυχαίο αριθμό μεγαλύτερο ή ίσο του 0 και μικρότερο του 1, ομοιόμορφα κατανεμημένο (αλλά<PERSON><PERSON><PERSON> κατά την επανάληψη του υπολογισμού)"}, "RANDARRAY": {"a": "([γραμμές]; [στήλες]; [ελάχ.]; [μέγ.]; [ακέραιος])", "d": "Επιστρέφει έναν πίνακα τυχαίων αριθμών"}, "RANDBETWEEN": {"a": "(μικρότερος; μεγαλύτερος)", "d": "Αποδίδει έναν τυχαίο αριθμό μεταξύ των αριθμών που καθορίζετε"}, "ROMAN": {"a": "(αριθμός; [φόρμα])", "d": "Μετατρέπει ένα αραβικό αριθμητικό ψηφίο σε λατινικό, ως κείμενο"}, "ROUND": {"a": "(αριθμός; αριθμός_ψηφίων)", "d": "Στρογγυλοπ<PERSON>ι<PERSON><PERSON> έναν αριθμό σε καθορισμένο αριθμό ψηφίων"}, "ROUNDDOWN": {"a": "(αριθμός; ψηφία_αριθμού)", "d": "Στρογγυλοποιεί προς τα κάτω έναν αριθμό, προς το μηδέν"}, "ROUNDUP": {"a": "(αριθμός; ψηφία_αριθμού)", "d": "Στρογγυλο<PERSON><PERSON><PERSON><PERSON><PERSON> έναν αριθμό προς τα επάνω"}, "SEC": {"a": "(αριθμός)", "d": "Αποδίδει την τέμνουσα μιας γωνίας"}, "SECH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική τέμνουσα μιας γωνίας"}, "SERIESSUM": {"a": "(x; n; m; συντελεστές)", "d": "Αποδίδει το άθροισμα μιας δυναμοσειράς βάσει του τύπου"}, "SIGN": {"a": "(αριθμός)", "d": "Αποδίδει το πρόσημο ενός αριθμού. Παίρνει την τιμή 1 αν ο αριθμός είναι θετικός, την τιμή 0 αν ο αριθμός είναι μηδέν ή την τιμή -1 αν ο αριθμός είναι αρνητικός"}, "SIN": {"a": "(αριθμός)", "d": "Αποδίδει το ημίτονο μιας γωνίας"}, "SINH": {"a": "(αριθμός)", "d": "Αποδίδει το υπερβολικ<PERSON> ημίτονο ενός αριθμού"}, "SQRT": {"a": "(αριθμός)", "d": "Αποδίδει την τετραγωνική ρίζα ενός αριθμού"}, "SQRTPI": {"a": "(αριθμός)", "d": "Αποδίδει την τετραγωνική ρίζα του (αριθμός * π)"}, "SUBTOTAL": {"a": "(αριθμός_αναφοράς; αναφορά1; ...)", "d": "Αποδίδει το μερικό άθροισμα μέσα σε μια λίστα ή μια βάση δεδομένων"}, "SUM": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Προσθέτει όλους τους αριθμούς σε μια περιοχή κελιών"}, "SUMIF": {"a": "(περιοχή; κριτήρια; [περιοχή_αθροίσματος])", "d": "Προσθέτει τα κελιά που καθορίζονται από μια συνθήκη ή από δεδομένα κριτήρια"}, "SUMIFS": {"a": "(περιοχή_αθροίσματος; περιοχή_κριτηρίων; κριτήρια; ...)", "d": "Προσθέτει τα κελιά που καθορίζονται από ένα δεδομένο σύνολο συνθηκών ή κριτηρίων"}, "SUMPRODUCT": {"a": "(πίνακας1; [πίνακας2]; [πίνακας3]; ...)", "d": "Αποδίδει το άθροισμα των γινομένων των αντίστοιχων αριθμητικών στοιχείων πίνακα ή περιοχής"}, "SUMSQ": {"a": "(αριθμός1; [αριθμός2]; ...)", "d": "Αποδίδει το άθροισμα των τετραγώνων των ορισμάτων. Τα ορίσματα μπορεί να είναι αριθμοί, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ον<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> αναφορές σε κελιά που περιέχουν αριθμούς."}, "SUMX2MY2": {"a": "(πίνακας_x; πίνακας_y)", "d": "Αποδίδει το άθροισμα των διαφορών των τετραγώνων για τις αντίστοιχες τιμές δύο περιοχών ή πινάκων"}, "SUMX2PY2": {"a": "(πίνακας_x; πίνακας_y)", "d": "Αποδίδει το σύνολο των αθροισμάτων των τετραγώνων για τις αντίστοιχες τιμές δύο περιοχών ή πινάκων"}, "SUMXMY2": {"a": "(πίνακας_x; πίνακας_y)", "d": "Αποδίδει το άθροισμα των τετραγώνων των διαφορών για τις αντίστοιχες τιμές δύο περιοχών ή πινάκων"}, "TAN": {"a": "(αριθμός)", "d": "Αποδίδει την εφαπτομένη μιας γωνίας"}, "TANH": {"a": "(αριθμός)", "d": "Αποδίδει την υπερβολική εφαπτομένη ενός αριθμού"}, "TRUNC": {"a": "(αριθμός; [ψηφία_αριθμού])", "d": "Αποκοπή του δεκαδικού ή κλασματικού μέρους ενός αριθμού"}, "ADDRESS": {"a": "(αριθμός_γραμμής; αριθμός_στήλης; [απόλυτος_αριθμός]; [a1]; [κείμενο_φύλλου])", "d": "Δημιουργεί μια αναφορά κελιού με μορφή κειμένου, για καθορισμένους αριθμούς γραμμών και στηλών"}, "CHOOSE": {"a": "(αριθμός_δείκτη; τιμή1; [τιμή2]; ...)", "d": "Επιλέγει μια τιμή ή μια ενέργεια προς εκτέλεση από λίστα τιμών, βάσ<PERSON>ι ενός αριθμού ευρετηρίου"}, "COLUMN": {"a": "([αναφορά])", "d": "Αποδίδει τον αριθμό στήλης μιας αναφοράς"}, "COLUMNS": {"a": "(πίνακας)", "d": "Αποδίδει τον αριθμό στηλών που περιέχονται σε έναν πίνακα ή μια αναφορά"}, "FORMULATEXT": {"a": "(αναφορά)", "d": "Αποδίδει έναν τύπο ως συμβολοσειρά"}, "HLOOKUP": {"a": "(τιμή_αναζήτησης; πίν<PERSON><PERSON><PERSON><PERSON>; αριθμός_δείκτη_γραμμής; [περιοχή_αναζήτησης])", "d": "Αναζητά μια τιμή στην πρώτη γραμμή ενός πίνακα ή μιας περιοχής τιμών και επιστρέφει την τιμή στην ίδια στήλη από μια καθορισμένη γραμμή"}, "HYPERLINK": {"a": "(τοποθεσία_σύνδεσης; [φιλικό_όνομα])", "d": "Δημιουργεί μια συντόμευση ή μεταπήδηση, η οποία ανοίγει ένα έγγραφο που είναι αποθηκευμένο στον σκληρό σας δίσκο, σε ένα διακομιστή δικτύου ή στο Internet"}, "INDEX": {"a": "(πίνακας; αριθμός_γραμμής; [αριθμός_στήλης]!αναφορά; αριθμός_γραμμής; [αριθμός_στήλης]; [αριθμός_περιοχής])", "d": "Αποδίδει μια τιμή ή αναφορά του κελιού στην τομή μιας συγκεκριμένης γραμμής και στήλης, σε μια δεδομένη περιοχή"}, "INDIRECT": {"a": "(κείμενο_αναφοράς; [a1])", "d": "Αποδίδει την αναφορά που καθορίζεται από μια ακολουθία χαρακτήρων κειμένου"}, "LOOKUP": {"a": "(τιμή_αναζήτησης; άνυσμα_αναζήτησης; [άνυσμα_αποτελέσματος]!τιμή_αναζήτησης; πίνακα<PERSON>)", "d": "Αναζητά μια τιμή από πίνακα ή από περιοχή που αποτελείται από μία μόνο γραμμή ή στήλη. Παρέχεται για λόγους αντίστροφης συμβατότητας"}, "MATCH": {"a": "(τιμή_αναζήτησης; πίνακ<PERSON><PERSON>_αναζήτησης; [τύπος_ταιριάσματος])", "d": "Αποδίδει τη σχετική θέση ενός στοιχείου σε έναν πίνακα που ταιριάζει με μια καθορισμένη τιμή σε μια καθορισμένη σειρά"}, "OFFSET": {"a": "(αναφορά; γραμμές; στήλες; [ύψος]; [πλάτος])", "d": "Αποδίδει μια αναφορά σε μια περιοχή που αποτελε<PERSON> έναν καθορισμένο αριθμό γραμμών και στηλών από μια δεδομένη αναφορά"}, "ROW": {"a": "([αναφορά])", "d": "Επιστρέφει τον αριθμό γραμμής μιας αναφοράς"}, "ROWS": {"a": "(πίνακας)", "d": "Αποδίδει το πλήθος γραμμών σε αναφορά ή πίνακα"}, "TRANSPOSE": {"a": "(πίνακας)", "d": "Αποδίδει τον ανάστροφο ενός πίνακα-στήλης ή αντίστροφα"}, "UNIQUE": {"a": "(πίνακας; [κατ<PERSON>_στήλη]; [ακριβώς_μία_φορά])", "d": "Επιστρέφει τις μοναδικές τιμές από ένα εύρος ή έναν πίνακα."}, "VLOOKUP": {"a": "(τιμή_αναζήτησης; πίν<PERSON><PERSON><PERSON><PERSON>; αριθμός_δείκτη_στήλης; [περιοχή_αναζήτησης])", "d": "Αναζητά μια τιμή στην πρώτη στήλη ενός πίνακα και επιστρέφει μια τιμή στην ίδια γραμμή από μια καθορισμένη στήλη. Εξ ορισμού, ο πίνακας θα πρέπει να είναι ταξινομημένος κατά αύξουσα σειρά"}, "XLOOKUP": {"a": "(τιμή_αναζήτησης; πίν<PERSON><PERSON><PERSON><PERSON>_αναζήτησης; πίνακ<PERSON>ς_επιστροφής; [εάν_δεν_βρεθεί]; [λειτουργία_αντιστοίχισης]; [λειτουργία_αναζήτησης])", "d": "Κάνει αναζήτηση σε ένα εύρος ή έναν πίνακα για μια αντιστοίχιση και επιστρέφει το αντίστοιχο στοιχείο από ένα δεύτερο εύρος ή πίνακα. Από προεπιλογή, χρησιμοποιείται ακριβής αντιστοίχιση"}, "CELL": {"a": "(τύπος_πληροφοριών; [αναφορά])", "d": "Επιστρέφει πληροφορίες σχετικά με τη μορφοποίηση, τη θέση ή τα περιεχόμενα ενός κελιού"}, "ERROR.TYPE": {"a": "(τιμή_σφάλματος)", "d": "Αποδίδει έναν αριθμό που αντιστοιχεί σε μια τιμή σφάλματος."}, "ISBLANK": {"a": "(τιμή)", "d": "Ελέγχει αν μια αναφορά γίνεται σε κενό κελί και επιστρέφει TRUE ή FALSE"}, "ISERR": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι σφάλμα διαφορετικό από #Δ/Υ και αποδίδει TRUE ή FALSE"}, "ISERROR": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι σφάλμα και αποδίδει TRUE ή FALSE"}, "ISEVEN": {"a": "(αριθμός)", "d": "Αποδίδει TRUE εάν ο αριθμός είναι ζυγός"}, "ISFORMULA": {"a": "(αναφορά)", "d": "Ελέγχει εάν μια αναφορ<PERSON> δείχνει ένα κελί που περιέχει τύπο και αποδίδει TRUE ή FALSE"}, "ISLOGICAL": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι μία από τις λογικές τιμές (TRUΕ ή FALSE) και επιστρέφει TRUE ή FALSE"}, "ISNA": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι #Δ/Υ και αποδίδει TRUE ή FALSE"}, "ISNONTEXT": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι κείμενο (τα κενά κελιά δεν είναι κείμενο) και επιστρέφει TRUE ή FALSE"}, "ISNUMBER": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι αριθμός και επιστρέφει TRUE ή FALSE"}, "ISODD": {"a": "(αριθμός)", "d": "Αποδίδει TRUE εάν ο αριθμός είναι μονός"}, "ISREF": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι αναφορά και αποδίδει TRUE ή FALSE"}, "ISTEXT": {"a": "(τιμή)", "d": "Ελέγχει αν μια τιμή είναι κείμενο και επιστρέφει TRUE ή FALSE"}, "N": {"a": "(τιμή)", "d": "Μετατρέπει μη αριθμητικές τιμές σε αριθμό, ημερομηνίες σε αύξοντες αριθμούς, την τιμή TRUE σε 1 και οτιδήποτε άλλο σε 0 (μηδέν)"}, "NA": {"a": "()", "d": "Αποδίδει την τιμή σφάλματος #Δ/Υ (η τιμή δεν υπάρχει)"}, "SHEET": {"a": "([τιμή])", "d": "Αποδίδει τον αριθμό φύλλου του φύλλου στο οποίο γίνεται αναφορά"}, "SHEETS": {"a": "([αναφορά])", "d": "Αποδίδει τον αριθμό φύλλων σε μια αναφορά"}, "TYPE": {"a": "(τιμή)", "d": "Επιστρέφει έναν ακέραιο που δηλώνει τον τύπο δεδομένων μιας τιμής: 1 αν είναι αριθμός, 2 αν είναι κείμενο, 4 αν είναι λογική τιμή, 16 αν είναι τιμή σφάλματος, 64 αν είναι πίνακας, 128 αν είναι σύνθετα δεδομένα"}, "AND": {"a": "(λογική1; [λογική2]; ...)", "d": "Ελέγχει αν όλα τα ορίσματα είναι TRUE και αποδίδει TRUE αν όλα τα ορίσματα είναι TRUE"}, "FALSE": {"a": "()", "d": "Αποδίδει τη λογική τιμή FALSE"}, "IF": {"a": "(λογική_έλεγχος; [τιμή_αν_true]; [τιμή_αν_false])", "d": "Ελέγχει αν ικανοποιείται μια συνθήκη και αποδίδει μία τιμή αν η συνθήκη είναι TRUE και μία άλλη τιμή αν είναι FALSE"}, "IFS": {"a": "(λογική_έλεγχος; τιμή_αν_true; ...)", "d": "Ελέγχει αν πληρούνται μία ή περισσότερες συνθήκες και επιστρέφει μια τιμή που αντιστοιχεί στην πρώτη συνθήκη με τιμή TRUE"}, "IFERROR": {"a": "(τιμή; τιμή_εάν_σφάλμα)", "d": "Επιστρέφει την τιμή τιμή_εάν_σφάλμα, εάν η παράσταση είναι σφάλμα και η τιμή της παράστασης είναι διαφορετική"}, "IFNA": {"a": "(τιμή; τιμή_εάν_δυ)", "d": "Αποδίδει την τιμή που καθορίζετε εάν η παράσταση αποδώσει #Δ/Υ, διαφορετι<PERSON><PERSON> αποδίδει το αποτέλεσμα της παράστασης"}, "NOT": {"a": "(λογική)", "d": "Αλλάζει την τιμή TRUE σε FALSE ή την τιμή FALSE σε TRUE"}, "OR": {"a": "(λογική1; [λογική2]; ...)", "d": "Ελέγχει αν κάποιο από τα ορίσματα είναι TRUE και αποδίδει TRUE ή FALSE. Αποδίδει FALSE μόνο αν όλα τα ορίσματα είναι FALSE"}, "SWITCH": {"a": "(παράσταση; τιμή1; αποτέλεσμα1; [προεπιλογή_ή_τιμή2]; [αποτέλεσμα2]; ...)", "d": "Αξιολογεί μια παράσταση έναντι μιας λίστας τιμών και επιστρέφει το αποτέλεσμα που αντιστοιχεί στον πρώτο κανόνα αντιστοίχισης. Ε<PERSON>ν δεν υπάρχει συμφωνία, επιστρέφεται μια προαιρετική προεπιλεγμένη τιμή"}, "TRUE": {"a": "()", "d": "Αποδίδει τη λογική τιμή TRUE"}, "XOR": {"a": "(λογική1; [λογική2]; ...)", "d": "Αποδίδει το λογικό 'αποκλειστικ<PERSON> ή' όλων των ορισμάτων"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Επιστρέφει κείμενο που είναι πριν από την οριοθέτηση χαρακτήρων."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Επιστρέφει κείμενο που είναι μετά την οριοθέτηση χαρακτήρων."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Διαχωρίζει το κείμενο σε σειρές ή στήλες χρησιμοποιώντας οριοθέτες."}, "WRAPROWS": {"a": "(διάνυσμα, wrap_count, [pad_with])", "d": " Αναδιπλώνει ένα διάνυσμα γραμμής ή στήλης μετά από έναν καθορισμένο αριθμό τιμών."}, "VSTACK": {"a": "(πίνακας1, [πίνακας2], ...)", "d": " Στοιβάζ<PERSON>ι κατακόρυφα πίνακες σε έναν πίνακα."}, "HSTACK": {"a": "(πίνακας1, [πίνακας2], ...)", "d": " Στοιχίζει οριζόντια πίνακες σε έναν πίνακα."}, "CHOOSEROWS": {"a": "(πίν<PERSON>κα<PERSON>, row_num1, [row_num2], ...)", "d": " Επιστρέφει γραμμές από έναν πίνακα ή αναφορά."}, "CHOOSECOLS": {"a": "(πίν<PERSON><PERSON><PERSON><PERSON>, col_num1, [col_num2], ...)", "d": " Επιστρέφει στήλες από έναν πίνακα ή αναφορά."}, "TOCOL": {"a": "(πίνακας, [παράβλεψη], [scan_by_column])", "d": " Επιστρέφει τον πίνακα ως μία στήλη."}, "TOROW": {"a": "(πίνακας, [παράβλεψη], [scan_by_column])", "d": " Επιστρέφει τον πίνακα ως μία γραμμή."}, "WRAPCOLS": {"a": "(διάνυσμα, wrap_count, [pad_with])", "d": " Αναδιπλώνει ένα διάνυσμα γραμμής ή στήλης μετά από έναν καθορισμένο αριθμό τιμών."}, "TAKE": {"a": "(πίνακας, γραμμές, [στήλες])", "d": " Επιστρέφει γραμμές ή στήλες από την αρχή ή το τέλος του πίνακα."}, "DROP": {"a": "(πίνακας, γραμμές, [στήλες])", "d": " Αποθέτει γραμμές ή στήλες από την αρχή ή το τέλος του πίνακα."}}