﻿<!DOCTYPE html>
<html>
	<head>
		<title>GGANZZAHL-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>GGANZZAHL-Funktion</h1>
			<p>Die Funktion <b>GGANZZAHL</b> gehört zur Gruppe der technischen Funktionen. Mit dieser Funktion lässt sich testen, ob eine Zahl größer als ein Schwellenwert ist. Die Funktion gibt 1 zurück, wenn die Zahl größer oder gleich Schritt ist, andernfalls 0.</p>
			<p>Die Formelsyntax der Funktion <b>GGANZZAHL</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>GGANZZAHL(Zahl;[Schritt])</em></b></p> 
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Zahl</em></b> ist der Wert der gegen <b><em>Schritt</em></b> geprüft werden soll.</p>
            <p style="text-indent: 50px;"><b><em>Schritt</em></b> ist der Schwellenwert. Dieses Argument ist optional. Wird für <b><em>Schritt</em></b> kein Wert angegeben, nimmt die Funktion für Schritt 0 an.</p>
            <p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>GGANZZAHL</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Technische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>GGANZZAHL</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="GGANZZAHL-Funktion" src="../images/gestep.png" /></p>
		</div>
	</body>
</html>