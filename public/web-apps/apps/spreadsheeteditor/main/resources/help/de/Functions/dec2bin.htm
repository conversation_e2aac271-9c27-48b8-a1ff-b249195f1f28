﻿<!DOCTYPE html>
<html>
	<head>
		<title>DEZINBIN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>DEZINBIN-Funktion</h1>
			<p>Die Funktion <b>DEZINBIN</b> gehört zur Gruppe der technischen Funktionen. Sie wandelt eine dezimale Zahl in eine binäre Zahl (Dualzahl) um.</p>
			<p>Die Formelsyntax der Funktion <b>DEZINBIN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>DEZINBIN(Zahl;[Stellen])</em></b></p>
			<p><em>Dabei gilt:</em></p>  
			    <p style="text-indent: 50px;"><b><em>Zahl</em></b> ist eine dezimale ganze Zahl, die manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p> 
				<p style="text-indent: 50px;"><b><em>Stellen</em></b> gibt an, wie viele Zeichen angezeigt werden sollen. Fehlt das Argument Stellen, verwendet die Funktion die mindestens erforderliche Anzahl von Zeichen.</p> 
			<p class="note"><b>Hinweis:</b> Ist <b><em>Stellen</em></b> weniger oder gleich 0, gibt die Funktion den Fehlerwert <b>#ZAHL!</b> zurück.</p>
			<p>Anwendung der Funktion <b>DEZINBIN</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Technische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>DEZINBIN</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="DEZINBIN-Funktion" src="../images/dec2bin.png" /></p>
		</div>
	</body>
</html>