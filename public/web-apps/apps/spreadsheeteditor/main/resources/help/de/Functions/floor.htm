﻿<!DOCTYPE html>
<html>
	<head>
		<title>UNTERGRENZE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>UNTERGRENZE-Funktion</h1>
			<p>Die Funktion <b>UNTERGRENZE</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie rundet eine Zahl auf das nächste Vielfache ab.</p>
			<p>Die Formelsyntax der Funktion <b>UNTERGRENZE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>UNTERGRENZE(Zahl;Schritt)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist der numerische Wert, den Sie runden möchten.</p> 
				<p style="text-indent: 50px;"><b><em>Schritt</em></b> ist das Vielfache, auf das Sie abrunden möchten.</p>
			<p class="note"><b>Hinweis</b>: Falls <b><em>Zahl</em></b> und <b><em>Schritt</em></b> unterschiedliche Vorzeichen haben, gibt die Funktion den Fehlerwert <b>#NUM!</b> zurück.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>UNTERGRENZE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>UNTERGRENZE</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="UNTERGRENZE-Funktion" src="../images/floor.png" /></p>
		</div>
	</body>
</html>