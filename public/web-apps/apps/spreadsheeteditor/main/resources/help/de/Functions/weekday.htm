﻿<!DOCTYPE html>
<html>
	<head>
		<title>WOCHENTAG-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WOCHENTAG-Funktion</h1>
			<p>Die Funktion <b>WOCHENTAG</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie wird verwendet, um zu bestimmen, an welchem Wochentag das angegebene Datum liegt.</p>
			<p>Die Formelsyntax der Funktion <b>WOCHENTAG</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>WOCHENTAG(Zahl;[Typ])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist eine fortlaufende Zahl, die den Tag des gesuchten Datums darstellt. Datumsangaben sollten mithilfe der Funktion <a href="Date.htm" onclick="onhyperlinkclick(this)">DATUM</a> oder als Ergebnisse anderer Formeln oder Funktionen eingegeben werden.</p>
			<p style="text-indent: 50px;"><b><em>Typ</em></b> ist eine Zahl, mit der der Typ des Rückgabewerts bestimmt wird. Mögliche Basiswerte:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Zahlenwert</b></td>
					<td><b>Erklärung</b></td>
				</tr>
				<tr>
					<td>1 oder nicht angegeben</td>
					<td>Gibt eine Zahl von Zahl 1 (Sonntag) bis 7 (Samstag) wieder</td>
				</tr>
				<tr>
					<td>2</td>
					<td>Gibt eine Zahl von Zahl 1 (Montag) bis 7 (Sonntag) wieder</td>
				</tr>
				<tr>
					<td>3</td>
					<td>Gibt eine Zahl von Zahl 0 (Montag) bis 6 (Sonntag) wieder</td>
				</tr>
			</table>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WOCHENTAG</b>:</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="WOCHENTAG-Funktion" src="../images/weekday.png" /></p>
		</div>
	</body>
</html>