﻿<!DOCTYPE html>
<html>
	<head>
		<title>DELTA-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>DELTA-Funktion</h1>
			<p>Die Funktion <b>DELTA</b> gehört zur Gruppe der technischen Funktionen. Sie überprüft, ob zwei Werte gleich sind. Sind die Werte gleich, gibt die Funktion 1 zurück. Andernfalls gibt sie 0 zurück</p>
			<p>Die Formelsyntax der Funktion <b>DELTA</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>DELTA(Zahl1;[Zahl2];...)</em></b></p> 
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Zahl1</em></b> ist die erste Zahl.</p>
            <p style="text-indent: 50px;"><b><em>Zahl2</em></b> ist die zweite Zahl. Dieses Argument ist optional. Fehlt das Argument <b><em>Zahl2</em></b> wird es als 0 angenommen.</p>
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p>Anwendung der Funktion <b>DELTA</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Technische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>DELTA</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><div class = "smb smb-delta"></div></p>
		</div>
	</body>
</html>