﻿<!DOCTYPE html>
<html>
	<head>
		<title>IMCOSECHYP-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>IMCOSECHYP-Funktion</h1>
			<p>Die Funktion <b>IMCOSECHYP</b> gehört zur Gruppe der technischen Funktionen. Sie wird genutzt, um den hyperbolischen Kosekans einer komplexen Zahl zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>IMCOSECHYP</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>IMCOSECHYP(Komplexe_Zahl)</em></b></p> 
			<p>Dabei ist <b><em>Komplexe_Zahl</em></b> eine komplexe Zahl, die manuell im Format x + yi oder x + yj eingegeben wird oder in der Zelle beinhaltet ist, auf die Sie Bezug nehmen.</p>
			<p>Anwenden der Funktion <b>IMCOSECHYP</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Technische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>IMCOSECHYP</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="IMCOSECHYP-Funktion" src="../images/imcsch.png" /></p>
		</div>
	</body>
</html>