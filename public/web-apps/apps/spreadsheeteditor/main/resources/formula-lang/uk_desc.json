{"DATE": {"a": "(рік; місяць; день)", "d": "Повертає число, що відповідає коду дати-часу"}, "DATEDIF": {"a": "(дата_початку; дата_завершення; одиниця)", "d": "Обчислює кількість днів, місяців або років між двома датами"}, "DATEVALUE": {"a": "(дата_в_текстовому_форматі)", "d": "Перетворює дату в текстовому форматі на число, що відповідає даті в коді дати-часу"}, "DAY": {"a": "(дата_в_числовому_форматі)", "d": "Повертає день місяця (число від 1 до 31)."}, "DAYS": {"a": "(кінц_дата; поч_дата)", "d": "Повертає кількість днів між двома датами."}, "DAYS360": {"a": "(поч_дата; кін_дата; [метод])", "d": "Повертає кількість днів між двома датами на основі 360-денного року (12 місяців по 30 днів)"}, "EDATE": {"a": "(поч_дата; кількість_місяців)", "d": "Повертає порядковий номер дати, яка передує початковій даті на вказану кількість місяців або йде через указану кількість місяців після початкової дати"}, "EOMONTH": {"a": "(поч_дата; кількість_місяців)", "d": "Повертає порядковий номер останнього дня місяця до або після вказаної кількості місяців"}, "HOUR": {"a": "(час_у_числовому_форматі)", "d": "Повертає години як число від 0 до 23."}, "ISOWEEKNUM": {"a": "(дата)", "d": "Повертає номер тижня в році за системою ISO для вказаної дати"}, "MINUTE": {"a": "(час_у_числовому_форматі)", "d": "Повертає хвилини як число від 0 до 59."}, "MONTH": {"a": "(дата_в_числовому_форматі)", "d": "Повертає місяць (число від 1 (січень) до 12 (грудень))."}, "NETWORKDAYS": {"a": "(поч_дата; кінц_дата; [свята])", "d": "Повертає кількість цілих робочих днів між двома датами"}, "NETWORKDAYS.INTL": {"a": "(дата_початку; дата_завершення; [вихідний]; [свята])", "d": "Повертає кількість цілих робочих днів між двома датами зі спеціальними параметрами вихідного дня"}, "NOW": {"a": "()", "d": "Повертає поточну дату й час у форматі дати й часу."}, "SECOND": {"a": "(час_у_числовому_форматі)", "d": "Повертає секунди як число від 0 до 59."}, "TIME": {"a": "(години; хвилини; секунди)", "d": "Перетворює години, хвилини та секунди, задані як числа, на число в коді часу"}, "TIMEVALUE": {"a": "(час_у_текстовому_форматі)", "d": "Перетворює час із текстового формату на число в коді часу (число в інтервалі від 0 (0:00:00) до 0,999988426 (23:59:59))."}, "TODAY": {"a": "()", "d": "Повертає поточну дату у форматі дати."}, "WEEKDAY": {"a": "(дата_в_числовому_форматі; [тип])", "d": "Повертає число від 1 до 7, яке відповідає дню тижня для вказаної дати."}, "WEEKNUM": {"a": "(порядковий_номер; [тип_повернення])", "d": "Повертає номер тижня в році"}, "WORKDAY": {"a": "(поч_дата; дні; [свята])", "d": "Повертає порядковий номер дати, віддаленої в минулому або майбутньому на вказану кількість робочих днів"}, "WORKDAY.INTL": {"a": "(дата_початку; дні; [вихідний]; [свята])", "d": "Повертає порядковий номер дати, віддаленої в минулому або майбутньому на вказану кількість робочих днів зі спеціальними параметрами вихідного дня"}, "YEAR": {"a": "(дата_в_числовому_форматі)", "d": "Повертає рік (ціле число від 1900 до 9999)."}, "YEARFRAC": {"a": "(поч_дата; кінц_дата; [базис])", "d": "Повертає частину року, яка складається з кількості повних днів між початковою та кінцевою датами"}, "BESSELI": {"a": "(x; n)", "d": "Повертає модифіковану функцію Бесселя Іn(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Повертає функцію Бесселя Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Повертає модифіковану функцію Бесселя Кn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Повертає функцію Бесселя Yn(x)"}, "BIN2DEC": {"a": "(число)", "d": "Перетворює двійкове число на десяткове"}, "BIN2HEX": {"a": "(число; [розряди])", "d": "Перетворює двійкове число на шістнадцяткове"}, "BIN2OCT": {"a": "(число; [розряди])", "d": "Перетворює двійкове число на вісімкове"}, "BITAND": {"a": "(число1; число2)", "d": "Повертає побітове \"ТА\" двох чисел"}, "BITLSHIFT": {"a": "(число; велич_зсуву)", "d": "Повертає число, зсунуте ліворуч на велич_зсуву в бітах"}, "BITOR": {"a": "(число1; число2)", "d": "Повертає побітове \"АБО\" двох чисел"}, "BITRSHIFT": {"a": "(число; велич_зсуву)", "d": "Повертає число, зсунуте праворуч на велич_зсуву в бітах"}, "BITXOR": {"a": "(число1; число2)", "d": "Повертає побітове \"виключне АБО\" двох чисел"}, "COMPLEX": {"a": "(дійсна_частина; уявна_частина; [суфікс])", "d": "Перетворює коефіцієнти дійсної й уявної частин на комплексне число"}, "CONVERT": {"a": "(число; стара_одиниця; нова_одиниця)", "d": "Перетворює число з однієї системи вимірювання в іншу"}, "DEC2BIN": {"a": "(число; [розряди])", "d": "Перетворює десяткове число на двійкове"}, "DEC2HEX": {"a": "(число; [розряди])", "d": "Перетворює десяткове число на шістнадцяткове"}, "DEC2OCT": {"a": "(число; [розряди])", "d": "Перетворює десяткове число на вісімкове"}, "DELTA": {"a": "(число1; [число2])", "d": "Перевіряє, чи рівні два числа"}, "ERF": {"a": "(нижня_межа; [верхня_межа])", "d": "Повертає функцію помилки"}, "ERF.PRECISE": {"a": "(X)", "d": "Повертає функцію помилки"}, "ERFC": {"a": "(x)", "d": "Повертає додаткову функцію помилки"}, "ERFC.PRECISE": {"a": "(X)", "d": "Повертає додаткову функцію помилки"}, "GESTEP": {"a": "(число; [поріг])", "d": "Перевіряє, чи є число більшим за граничне значення"}, "HEX2BIN": {"a": "(число; [розряди])", "d": "Перетворює шістнадцяткове число на двійкове"}, "HEX2DEC": {"a": "(число)", "d": "Перетворює шістнадцяткове число на десяткове"}, "HEX2OCT": {"a": "(число; [розряди])", "d": "Перетворює шістнадцяткове число на вісімкове"}, "IMABS": {"a": "(компл_число)", "d": "Повертає абсолютне значення (модуль) комплексного числа"}, "IMAGINARY": {"a": "(компл_число)", "d": "Повертає коефіцієнт уявної частини комплексного числа"}, "IMARGUMENT": {"a": "(компл_число)", "d": "Повертає аргумент q, кут, виражений у радіанах"}, "IMCONJUGATE": {"a": "(компл_число)", "d": "Повертає комплексне спряжене комплексного числа"}, "IMCOS": {"a": "(компл_число)", "d": "Повертає косинус комплексного числа"}, "IMCOSH": {"a": "(компл_число)", "d": "Повертає гіперболічний косинус комплексного числа"}, "IMCOT": {"a": "(компл_число)", "d": "Повертає котангенс комплексного числа"}, "IMCSC": {"a": "(компл_число)", "d": "Повертає косеканс комплексного числа"}, "IMCSCH": {"a": "(компл_число)", "d": "Повертає гіперболічний косеканс комплексного числа"}, "IMDIV": {"a": "(компл_число1; компл_число2)", "d": "Повертає частку двох комплексних чисел"}, "IMEXP": {"a": "(компл_число)", "d": "Повертає експоненту комплексного числа"}, "IMLN": {"a": "(компл_число)", "d": "Повертає натуральний логарифм комплексного числа"}, "IMLOG10": {"a": "(компл_число)", "d": "Повертає десятковий логарифм комплексного числа"}, "IMLOG2": {"a": "(компл_число)", "d": "Повертає логарифм комплексного числа за основою 2"}, "IMPOWER": {"a": "(компл_число; число)", "d": "Повертає комплексне число, піднесене до цілого степеня"}, "IMPRODUCT": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Повертає добуток від 1 до 255 комплексних чисел"}, "IMREAL": {"a": "(компл_число)", "d": "Повертає коефіцієнт дійсної частини комплексного числа"}, "IMSEC": {"a": "(компл_число)", "d": "Повертає секанс комплексного числа"}, "IMSECH": {"a": "(компл_число)", "d": "Повертає гіперболічний секанс комплексного числа"}, "IMSIN": {"a": "(компл_число)", "d": "Повертає синус комплексного числа"}, "IMSINH": {"a": "(компл_число)", "d": "Повертає гіперболічний синус комплексного числа"}, "IMSQRT": {"a": "(компл_число)", "d": "Повертає квадратний корінь комплексного числа"}, "IMSUB": {"a": "(компл_число1; компл_число2)", "d": "Повертає різницю двох комплексних чисел"}, "IMSUM": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Повертає суму комплексних чисел"}, "IMTAN": {"a": "(компл_число)", "d": "Повертає тангенс комплексного числа"}, "OCT2BIN": {"a": "(число; [розряди])", "d": "Перетворює вісімкове число на двійкове"}, "OCT2DEC": {"a": "(число)", "d": "Перетворює вісімкове число на десяткове"}, "OCT2HEX": {"a": "(число; [розряди])", "d": "Перетворює вісімкове число на шістнадцяткове"}, "DAVERAGE": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює середнє всіх значень стовпця списку або бази даних, які відповідають указаним умовам"}, "DCOUNT": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Підраховує кількість клітинок з числами у стовпці записів бази даних, які відповідають указаним умовам"}, "DCOUNTA": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Підраховує кількість непустих клітинок у стовпці записів бази даних, які відповідають указаним умовам"}, "DGET": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Витягає з бази даних один запис, який відповідає вказаним умовам"}, "DMAX": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Повертає найбільше значення у стовпці записів бази даних, які відповідають указаним умовам"}, "DMIN": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Повертає найменше значення у стовпці записів бази даних, які відповідають указаним умовам"}, "DPRODUCT": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Перемножує значення у стовпці записів бази даних, які відповідають указаним умовам"}, "DSTDEV": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює стандартне відхилення на основі вибірки з виділених записів бази даних"}, "DSTDEVP": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності з виділених записів бази даних"}, "DSUM": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Складає числа у стовпці записів бази даних, які відповідають указаним умовам"}, "DVAR": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює дисперсію на основі вибірки з виділених записів бази даних"}, "DVARP": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює дисперсію на основі генеральної сукупності з виділених записів бази даних"}, "CHAR": {"a": "(число)", "d": "Повертає символ з указаним кодом із набору символів, установленого для вашого комп'ютера"}, "CLEAN": {"a": "(текст)", "d": "Видаляє з тексту всі недруковані символи"}, "CODE": {"a": "(текст)", "d": "Повертає числовий код першого символу в текстовому рядку для набору символів, що використовується у вашому комп'ютері"}, "CONCATENATE": {"a": "(текст1; [текст2]; ...)", "d": "Поєднує кілька текстових рядків у один"}, "CONCAT": {"a": "(текст1; ...)", "d": "Об’єднання списку або діапазону текстових рядків"}, "DOLLAR": {"a": "(число; [десяткові_знаки])", "d": "Перетворює число на текст, використовуючи грошовий формат"}, "EXACT": {"a": "(текст1; текст2)", "d": "Перевіряє, чи збігаються два текстові рядки, і повертає значення TRUE або FALSE. Великі й малі букви розрізняються"}, "FIND": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Повертає позицію початку шуканого текстового рядка в тексті перегляду, який його містить. Великі й малі букви розрізняються"}, "FINDB": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Знаходить один рядок тексту у другому рядку тексту та повертає позицію початку першого рядка тексту від першого символу другого рядка тексту, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська"}, "FIXED": {"a": "(число; [кількість_знаків]; [без_розділювачів])", "d": "Округлює число до заданої кількості десяткових знаків і перетворює його на текст"}, "LEFT": {"a": "(текст; [кількість_символів])", "d": "Повертає задану кількість символів, вибрану з початку текстового рядка"}, "LEFTB": {"a": "(текст; [кількість_символів])", "d": "Повертає перший символ або символи в текстовому рядку, залежно від заданої кількості байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська"}, "LEN": {"a": "(текст)", "d": "Повертає кількість символів у текстовому рядку"}, "LENB": {"a": "(текст)", "d": "Повертає кількість байтів, використаних для відображення символів у текстовому рядку, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська"}, "LOWER": {"a": "(текст)", "d": "Перетворює всі букви в текстовому рядку на малі"}, "MID": {"a": "(текст; поч_позиція; кількість_символів)", "d": "Повертає задану кількість символів, вибрану з рядка тексту, починаючи з указаної позиції"}, "MIDB": {"a": "(текст; поч_позиція; кількість_символів)", "d": "Повертає задану кількість символів із рядка тексту, починаючи з указаної позиції, на основі заданої кількості байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська"}, "NUMBERVALUE": {"a": "(текст; [десятковий_роздільник]; [груповий_роздільник])", "d": "Перетворює текст на число незалежно від локалізації"}, "PROPER": {"a": "(текст)", "d": "Перетворює регістр текстового рядка; регістр першої букви в кожному слові перетворено на верхній, а регістр решти букв у слові – на нижній"}, "REPLACE": {"a": "(старий_текст; поч_поз; кількість_символів; новий_текст)", "d": "Замінює частину текстового рядка на інший текст"}, "REPLACEB": {"a": "(старий_текст; поч_поз; кількість_символів; новий_текст)", "d": "Замінює частину текстового рядка на інший текст, виходячи з кількості вказаних байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська"}, "REPT": {"a": "(текст; кількість_повторів)", "d": "Повторює текст задану кількість разів. Функція REPT використовується для заповнення клітинки повторюваними текстовими рядками"}, "RIGHT": {"a": "(текст; [кількість_символів])", "d": "Повертає задану кількість символів, вибрану з кінця текстового рядка"}, "RIGHTB": {"a": "(текст; [кількість_символів])", "d": "Повертає останній символ або символи в текстовому рядку, залежно від указаної кількості байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська"}, "SEARCH": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Повертає позицію першого входження символу або рядка тексту (без урахування регістру), якщо читати зліва направо"}, "SEARCHB": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Знаходить один текстовий рядок у межах другого рядка та повертає число стартової позиції першого текстового рядка з першого символу другого текстового рядка, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська"}, "SUBSTITUTE": {"a": "(текст; стар_текст; нов_текст; [номер_входження])", "d": "Замінює в текстовому рядку старий текст на новий"}, "T": {"a": "(значення)", "d": "Перевіряє, чи є значення текстом, і повертає цей текст, якщо так, або дві лапки (пустий рядок), якщо ні"}, "TEXT": {"a": "(значення; формат)", "d": "Перетворює значення на текст у певному форматі числа"}, "TEXTJOIN": {"a": "(роздільник; пропускати_пусті; текст1; ...)", "d": "Об’єднання списку або діапазону текстових рядків за допомогою роздільника"}, "TRIM": {"a": "(текст)", "d": "Видаляє з тексту всі пробіли, крім одиночних пробілів між словами"}, "UNICHAR": {"a": "(число)", "d": "Повертає символ Юнікод, на який посилається задане числове значення"}, "UNICODE": {"a": "(текст)", "d": "Повертає число (кодову точку), відповідне першому символу тексту"}, "UPPER": {"a": "(текст)", "d": "Перетворює всі букви в текстовому рядку на великі"}, "VALUE": {"a": "(текст)", "d": "Перетворює текстовий рядок, який представляє число, на число"}, "AVEDEV": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє абсолютних значень відхилень точок даних від середнього. Аргументи можуть бути числами або іменами, масивами або посиланнями на клітинки з числами"}, "AVERAGE": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє (арифметичне) аргу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, які можуть бути числами або іменами, масивами або посиланнями на клітинки з числами"}, "AVERAGEA": {"a": "(значення1; [значення2]; ...)", "d": "Повертає середнє (арифметичне) аргу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, причому текст і логічні значення FALSE приймаються за 0, а логічні значення TRUE - за 1. Аргументи можуть бути числами, іменами, масивами або посиланнями"}, "AVERAGEIF": {"a": "(діа<PERSON><PERSON>з<PERSON><PERSON>; крите<PERSON><PERSON><PERSON>; [діапазон_середн])", "d": "Повертає середнє (арифметичне) для клітинок, визначених цією умовою або критерієм"}, "AVERAGEIFS": {"a": "(діапазон_середн; діапазон_критерію; критер<PERSON>й; ...)", "d": "Обчислює середнє (арифметичне) для клітинок, визначених певним набором умов або критеріїв"}, "BETADIST": {"a": "(x; альфа; бета; [A]; [Б])", "d": "Повертає інтегральну функцію щільності бета-ймовірності"}, "BETAINV": {"a": "(імовірність; альфа; бета; [A]; [Б])", "d": "Повертає обернену функцію до інтегральної функції щільності бета-ймовірності (BETADIST)"}, "BETA.DIST": {"a": "(x; альфа; бета; сукупне; [A]; [B])", "d": "Повертає функцію бета-ймовірності розподілу"}, "BETA.INV": {"a": "(імовірність; альфа; бета; [A]; [B])", "d": "Повертає обернену функцію до інтегральної функції щільності бета-ймовірності (BETA.DIST)"}, "BINOMDIST": {"a": "(кількість_успіхів; кількість_випробувань; імовірність_успіху; функція)", "d": "Повертає окреме значення біноміального розподілу"}, "BINOM.DIST": {"a": "(кількість_успіхів; кількість_випробувань; імовірність_успіху; функція)", "d": "Повертає окреме значення біноміального розподілу"}, "BINOM.DIST.RANGE": {"a": "(кількість_випробувань; імовірність_успіху; кількість_успіхів; [кількість_успіхів2])", "d": "Повертає імовірність результатів випробувань за допомогою біномного розподілу"}, "BINOM.INV": {"a": "(кількість_випробувань; імовірність_успіху; альфа)", "d": "Повертає найменше значення, для якого інтегральна біноміальна функція розподілу більша або дорівнює значенню критерію"}, "CHIDIST": {"a": "(x; ступені_вільності)", "d": "Повертає правобічну ймовірність розподілу хі-квадрат"}, "CHIINV": {"a": "(імовірність; ступені_вільності)", "d": "Повертає обернену величину правобічної ймовірності розподілу хі-квадрат"}, "CHITEST": {"a": "(фактичний_діапазон; очікуваний_діапазон)", "d": "Повертає тест на незалежність: значення розподілу хі-квадрат для статистики та відповідних ступенів вільності"}, "CHISQ.DIST": {"a": "(x; ступінь_свободи; сукупне)", "d": "Повертає лівобічну ймовірність розподілу хі-квадрат"}, "CHISQ.DIST.RT": {"a": "(x; ступінь_свободи)", "d": "Повертає лівобічну ймовірність розподілу хі-квадрат"}, "CHISQ.INV": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернене значення лівобічної ймовірності розподілу хі-квадрат"}, "CHISQ.INV.RT": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернене значення правобічної ймовірності розподілу хі-квадрат"}, "CHISQ.TEST": {"a": "(фактичний_інтервал; очікуваний_інтервал)", "d": "Повертає тест на незалежність: значення розподілу хі-квадрат для статистичного розподілу та відповідної кількості ступенів вільності"}, "CONFIDENCE": {"a": "(альфа; станд_відхил; розмір)", "d": "Повертає довірчий інтервал для середнього генеральної сукупності, застосовуючи нормальний розподіл"}, "CONFIDENCE.NORM": {"a": "(альфа; станд_відхилення; розмір)", "d": "Повертає довірчий інтервал для середнього генеральної сукупності за допомогою нормального розподілу"}, "CONFIDENCE.T": {"a": "(альфа; станд_відхилення; розмір)", "d": "Повертає довірчий інтервал для середнього сукупності за допомогою Т-розподілу Ст'юдента"}, "CORREL": {"a": "(масив1; масив2)", "d": "Повертає коефіцієнт кореляції між двома сукупностями даних"}, "COUNT": {"a": "(значення1; [значення2]; ...)", "d": "Підраховує кількість клітинок у діапазоні з числами"}, "COUNTA": {"a": "(значення1; [значення2]; ...)", "d": "Підраховує кількість непустих клітинок у діапазоні"}, "COUNTBLANK": {"a": "(діапазон)", "d": "Підраховує кількість пустих клітинок у діапазоні"}, "COUNTIF": {"a": "(діа<PERSON><PERSON><PERSON><PERSON><PERSON>; критер<PERSON><PERSON>)", "d": "Підраховує в діапазоні кількість непустих клітинок, які відповідають заданій умові"}, "COUNTIFS": {"a": "(діапазон_критерію; критер<PERSON><PERSON>; ...)", "d": "Перелічує кількість клітинок, визначених наявним набором умов"}, "COVAR": {"a": "(масив1; масив2)", "d": "Повертає коваріацію – середнє добутків відхилень для кожної пари точок даних двох наборів даних"}, "COVARIANCE.P": {"a": "(масив1; масив2)", "d": "Повертає коваріацію сукупності — середнє попарних добутків відхилень"}, "COVARIANCE.S": {"a": "(масив1; масив2)", "d": "Повертає коваріацію зразка — середнє попарне добутків відхилень"}, "CRITBINOM": {"a": "(кількість_випробувань; імовірність_успіху; альфа)", "d": "Повертає найменше значення, для якого інтегральна біноміальна функція розподілу більша або дорівнює значенню критерію"}, "DEVSQ": {"a": "(число1; [число2]; ...)", "d": "Повертає суму квадратів відхилень точок даних від середнього з вибірки"}, "EXPONDIST": {"a": "(x; лямбда; інтегральна)", "d": "Повертає експоненційний розподіл"}, "EXPON.DIST": {"a": "(x; лямбда; сукупне)", "d": "Повертає експоненційний розподіл"}, "FDIST": {"a": "(x; ступені_вільності1; ступені_вільності2)", "d": "Повертає (правобічний) F-розподіл імовірності (ступінь відхилення) для двох наборів даних"}, "FINV": {"a": "(імовірність; ступені_вільності1; ступені_вільності2)", "d": "Повертає обернене значення для (правобічного) F-розподілу ймовірностей: якщо p = FDIST(x,...), то FINV(p,...) = x"}, "FTEST": {"a": "(масив1; масив2)", "d": "Повертає результат F-тесту – двобічну імовірність того, що відхилення двох масивів різняться незначно"}, "F.DIST": {"a": "(x; ступінь_свободи1; ступінь_свободи2; сукупне)", "d": "Повертає (лівобічний) F-розподіл імовірності (ступінь відхилення) для двох наборів даних"}, "F.DIST.RT": {"a": "(x; ступінь_свободи1; ступінь_свободи2)", "d": "Повертає (правобічний) F-розподіл імовірності (ступінь відхилення) для двох наборів даних"}, "F.INV": {"a": "(імовірність; ступінь_свободи1; ступінь_свободи2)", "d": "Повертає обернене значення (лівобічного) F-розподілу ймовірності: якщо p = F.DIST(x,...), то F.INV(p,...) = x"}, "F.INV.RT": {"a": "(імовірність; ступінь_свободи1; ступінь_свободи2)", "d": "Повертає обернене значення (правобічного) F-розподілу ймовірності: якщо p = F.DIST.RT(x,...), то F.INV.RT(p,...) = x"}, "F.TEST": {"a": "(масив1; масив2)", "d": "Повертає результат F-тесту - двоб<PERSON>чну ймовірність того, що дисперсії двох масивів різняться незначно"}, "FISHER": {"a": "(x)", "d": "Повертає перетворення Фішера"}, "FISHERINV": {"a": "(y)", "d": "Повертає обернене перетворення Фішера: якщо y = FISHER(x), то FISHERINV(y) = x"}, "FORECAST": {"a": "(x; відомі_значення_y; відомі_значення_x)", "d": "Прогнозує значення відповідно до лінійного наближення, обчисленого за відомими значеннями"}, "FORECAST.ETS": {"a": "(цільова_дата; значення; часова_шкала; [сезонний_фактор]; [доповнення_даних]; [агрегація])", "d": "Повертає передбачене значення для вказаної цільової дати в майбутньому за допомогою методу експоненційного згладжування."}, "FORECAST.ETS.CONFINT": {"a": "(цільова_дата; значення; часова_шкала; [довірчий_рівень]; [сезонний_фактор]; [доповнення_даних]; [агрегація])", "d": "Повертає довірчий інтервал для передбаченого значення та вказаної цільової дати."}, "FORECAST.ETS.SEASONALITY": {"a": "(значення; часова_шкала; [доповнення_даних]; [агрегація])", "d": "Повертає тривалість повторюваної закономірності, яку програма виявила для вказаного часового ряду."}, "FORECAST.ETS.STAT": {"a": "(значення; часова_шкала; тип_статистики; [сезонність]; [доповнення_даних]; [агрегація])", "d": "Повертає запитану статистику для передбачення."}, "FORECAST.LINEAR": {"a": "(x; відомі_значення_y; відомі_значення_x)", "d": "Прогнозує значення відповідно до лінійного наближення, обчисленого за відомими значеннями"}, "FREQUENCY": {"a": "(масив_даних; масив_секцій)", "d": "Обчислює частоту появи значень в іншому діапазоні значень і повертає вертикальний масив кількості екземплярів, що містить на один елемент більше ніж масив_секцій"}, "GAMMA": {"a": "(x)", "d": "Повертає значення гамма-функції"}, "GAMMADIST": {"a": "(x; альфа; бета; функція)", "d": "Повертає гамма-розподіл"}, "GAMMA.DIST": {"a": "(x; альфа; бета; сукупне)", "d": "Повертає гамма-розподіл"}, "GAMMAINV": {"a": "(імовірність; альфа; бета)", "d": "Повертає обернений інтегральний гамма-розподіл: якщо p = GAMMADIST(x,...), то GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(імовірність; альфа; бета)", "d": "Повертає обернений гамма-розподіл: якщо p = GAMMA.DIST(x,...), то GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Повертає натуральний логарифм гамма-функції"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Повертає натуральний логарифм гамма-функції"}, "GAUSS": {"a": "(x)", "d": "Повертає значення на 0,5 менше, ніж стандартний нормальний інтегральний розподіл"}, "GEOMEAN": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє геометричне елементів масиву або діапазону з додатних чисел"}, "GROWTH": {"a": "(відомі_значення_y; [відомі_значення_x]; [нові_значення_x]; [конст])", "d": "Повертає значення відповідно до експоненційного наближення, обчисленого на основі відомих даних"}, "HARMEAN": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє гармонічне сукупності додатних чисел: обернену величину середнього арифметичного обернених величин"}, "HYPGEOM.DIST": {"a": "(кількість_успіхів_у_вибірці; розмір_виборки; кількість_успіхів_у_сукупності; розмір_сукупності; сукупне)", "d": "Повертає гіпергеометричний розподіл"}, "HYPGEOMDIST": {"a": "(кількість_успіхів_у_вибірці; кількість_вибірок; кількість_успіхів_у_сукупності; кількість_сукупностей)", "d": "Повертає гіпергеометричний розподіл"}, "INTERCEPT": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Знаходить точку перетину осі Y із лінією лінійної регресії, обчисленої на основі відомих значень x і y"}, "KURT": {"a": "(число1; [число2]; ...)", "d": "Повертає ексцес сукупності даних"}, "LARGE": {"a": "(масив; k)", "d": "Повертає k-е найбільше значення у сукупності даних (наприклад, п'яте за величиною)"}, "LINEST": {"a": "(відомі_значення_y; [відомі_значення_x]; [конст]; [статистика])", "d": "Повертає параметри лінійного наближення, обчислені за методом найменших квадратів"}, "LOGEST": {"a": "(відомі_значення_y; [відомі_значення_x]; [конст]; [статистика])", "d": "Повертає параметри експоненційного наближення"}, "LOGINV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернену функцію інтегрального логарифмічно-стандартного розподілу x, де ln(x) стандартно розподілено з параметрами ''середнє'' та ''станд_відхил''"}, "LOGNORM.DIST": {"a": "(x; середнє; станд_відхилення; сукупне)", "d": "Повертає логарифмічно-нормальний розподіл x, де ln(x) – нормальний розподіл із параметрами \"середнє\" та \"станд_відхилення\""}, "LOGNORM.INV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернений інтегральний логарифмічно-нормальний розподіл x, де ln(x) - нормальний розподіл із параметрами ''середнє'' та ''станд_відхил''"}, "LOGNORMDIST": {"a": "(x; середнє; стандартне_відхил)", "d": "Повертає інтегральний логарифмічно-нормальний розподіл x, де ln(x) стандартно розподілено з параметрами ''середнє'' та ''стандартне_відхил''"}, "MAX": {"a": "(число1; [число2]; ...)", "d": "Повертає найбільше значення зі списку аргументів. Логічні значення та текст ігноруються"}, "MAXA": {"a": "(значення1; [значення2]; ...)", "d": "Повертає найбільше значення з набору значень. Логічні значення та текст не ігноруються"}, "MAXIFS": {"a": "(діапазон_максимумів; діапазон_критеріїв; критерії; ...)", "d": "Повертає максимальне значення серед клітинок, що визначаються за допомогою набору умов або критеріїв"}, "MEDIAN": {"a": "(значення1; [значення2]; ...)", "d": "Повертає медіану вказаних чисел (тобто число, яке міститься посередині їх списку)"}, "MIN": {"a": "(число1; [число2]; ...)", "d": "Повертає найменше значення зі списку аргументів. Логічні значення та текст ігноруються"}, "MINA": {"a": "(значення1; [значення2]; ...)", "d": "Повертає найменше значення з набору значень. Логічні значення та текст не ігноруються"}, "MINIFS": {"a": "(діапазон_мінімумів; діапазон_критеріїв; критерії; ...)", "d": "Повертає мінімальне значення серед клітинок, що визначаються за допомогою набору умов або критеріїв"}, "MODE": {"a": "(число1; [число2]; ...)", "d": "Повертає моду (найчастіше повторюване значення) масиву або діапазону даних"}, "MODE.MULT": {"a": "(число1; [число2]; ...)", "d": "Повертає вертикальний масив найчастіше повторюваних значень у масиві або діапазоні даних. Для горизонтального масиву використовуйте =TRANSPOSE(MODE.MULT(число1,число2,...))"}, "MODE.SNGL": {"a": "(число1; [число2]; ...)", "d": "Повертає моду (найчастіше повторюване значення) сукупності даних"}, "NEGBINOM.DIST": {"a": "(кількість_невдач; кількість_успіхів; імовірність_успіхів; сукупне)", "d": "Повертає від'ємний біноміальний розподіл – імовірність виникнення певної кількості невдач до досягнення певної кількості успіхів із певною ймовірністю успіху"}, "NEGBINOMDIST": {"a": "(кількість_невдач; кількість_успіхів; імовірність_успіху)", "d": "Повертає від'ємний біноміальний розподіл – імовірність певної кількості невдач до досягнення певної кількості успіхів, з певною імовірністю успіху"}, "NORM.DIST": {"a": "(x; середнє; станд_відхилення; сукупне)", "d": "Повертає нормальний розподіл з указаними параметрами"}, "NORMDIST": {"a": "(x; середнє; стандартне_відхил; інтегральна)", "d": "Повертає нормальний інтегральний розподіл для указаного середнього та стандартного відхилення"}, "NORM.INV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернений нормальний розподіл з указаними параметрами"}, "NORMINV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернений нормальний розподіл для указаного середнього та стандартного відхилення"}, "NORM.S.DIST": {"a": "(z; сукупне)", "d": "Повертає значення стандартного нормального розподілу (з нульовим середнім і одиничним стандартним відхиленням)"}, "NORMSDIST": {"a": "(z)", "d": "Повертає стандартний нормальний інтегральний розподіл (з нульовим середнім і одиничним стандартним відхиленням)"}, "NORM.S.INV": {"a": "(імовірність)", "d": "Повертає обернене значення стандартного нормального розподілу (з нульовим середнім і одиничним стандартним відхиленням)"}, "NORMSINV": {"a": "(імовірність)", "d": "Повертає обернене значення стандартного нормального розподілу (з нульовим середнім і одиничним стандартним відхиленням)"}, "PEARSON": {"a": "(масив1; масив2)", "d": "Повертає коефіцієнт кореля<PERSON><PERSON><PERSON> Пірсона, r"}, "PERCENTILE": {"a": "(масив; k)", "d": "Повертає k-й процентиль значень діапазону"}, "PERCENTILE.EXC": {"a": "(масив; k)", "d": "Повертає k-й процентиль для значень діапазону, де k в інтервалі від 0 до 1, виключаючи"}, "PERCENTILE.INC": {"a": "(масив; k)", "d": "Повертає k-й процентиль для значень в діапазоні, де k в інтервалі від 0 до 1, виключаючи"}, "PERCENTRANK": {"a": "(масив; x; [точність])", "d": "Повертає ранг значення в наборі даних як відсоток набору даних"}, "PERCENTRANK.EXC": {"a": "(масив; x; [точність])", "d": "Повертає ранг (відсоткову норму) значення в сукупності даних (від 0 до 1, виключаючи)"}, "PERCENTRANK.INC": {"a": "(масив; x; [точність])", "d": "Повертає ранг (відсоткову норму) значення в сукупності даних (від 0 до 1 включно)"}, "PERMUT": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість перестановок для заданої кількості об'єктів, які можна вибрати з загального числа об'єктів"}, "PERMUTATIONA": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість перестановок для заданої кількості об’єктів (з повторами), які можна вибрати із загального числа об’єктів"}, "PHI": {"a": "(x)", "d": "Повертає значення функції щільності ймовірності для стандартного нормального розподілу"}, "POISSON": {"a": "(x; середнє; інтегральна)", "d": "Повертає розподіл Пуассона"}, "POISSON.DIST": {"a": "(x; середнє; сукупне)", "d": "Повертає розподіл Пуассона"}, "PROB": {"a": "(інтервал_x; інтервал_імовірностей; нижня_межа; [верхня_межа])", "d": "Повертає ймовірність того, що значення діапазону знаходяться в указаних межах або на нижній межі"}, "QUARTILE": {"a": "(масив; частка)", "d": "Повертає квартиль набору даних"}, "QUARTILE.INC": {"a": "(масив; частка)", "d": "Повертає квартиль сукупності даних на основі значень процентилю в інтервалі від 0 до 1 включно"}, "QUARTILE.EXC": {"a": "(масив; частка)", "d": "Повертає квартиль сукупності даних на основі значень процентилю в інтервалі від 0 до 1, виключаючи"}, "RANK": {"a": "(число; посилання; [порядок])", "d": "Повертає ранг числа у списку чисел: його місце за величиною відносно інших значень у списку"}, "RANK.AVG": {"a": "(число; посилання; [порядок])", "d": "Повертає ранг числа у списку чисел: його місце за величиною серед інших значень у списку; якщо кілька значень мають такий самий ранг, повертається середній ранг"}, "RANK.EQ": {"a": "(число; посилання; [порядок])", "d": "Повертає ранг числа у списку чисел: його місце за величиною серед інших значень у списку; якщо кілька значень мають такий самий ранг, повертається найбільший ранг"}, "RSQ": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Повертає квадрат коефіцієнта кореляції Пірсона, обчисленого за вказаними точками"}, "SKEW": {"a": "(число1; [число2]; ...)", "d": "Повертає асиметрію розподілу: характеристика ступеня асиметрії розподілу за середнім значенням"}, "SKEW.P": {"a": "(число1; [число2]; ...)", "d": "Повертає асиметрію розподілу на основі сукупності: характеристика ступеня асиметрії розподілу навколо середнього значення"}, "SLOPE": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Повертає нахил лінії лінійної регресії за вказаними точками даних"}, "SMALL": {"a": "(масив; k)", "d": "Повертає k-е найменше значення у сукупності даних (наприклад, п'яте з кінця за величиною)"}, "STANDARDIZE": {"a": "(x; середнє; стандартне_відхил)", "d": "Повертає нормалізоване значення розподілу з указаними параметрами"}, "STDEV": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі вибірки (ігноруючи логічні значення й текст)"}, "STDEV.P": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності (ігноруючи логічні значення й текст)"}, "STDEV.S": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі вибірки (ігноруючи логічні значення й текст)"}, "STDEVA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює стандартне відхилення на основі вибірки, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1"}, "STDEVP": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності (ігноруючи логічні значення та текст)"}, "STDEVPA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1"}, "STEYX": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Повертає стандартну помилку прогнозованих значень y для кожного значення y в регресії"}, "TDIST": {"a": "(x; ступені_вільності; боки)", "d": "Повертає t-розподіл студента"}, "TINV": {"a": "(імовірність; ступені_вільності)", "d": "Повертає обернений двобічний t-розподіл студента"}, "T.DIST": {"a": "(x; ступінь_свободи; сукупне)", "d": "Повертає лівобічний t-розподіл Ст'юдента"}, "T.DIST.2T": {"a": "(x; ступінь_свободи)", "d": "Повертає двобічний t-розподіл Ст'юдента"}, "T.DIST.RT": {"a": "(x; ступінь_свободи)", "d": "Повертає правобічний t-розподіл Ст'юдента"}, "T.INV": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернений лівобічний t-розподіл Ст'юдента"}, "T.INV.2T": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернений двобічний t-розподіл Ст'юдента"}, "T.TEST": {"a": "(масив1; масив2; боки; тип)", "d": "Повертає ймовірність, яка відповідає t-тесту Ст'юдента"}, "TREND": {"a": "(відомі_значення_y; [відомі_значення_x]; [нові_значення_x]; [конст])", "d": "Повертає значення відповідно до лінійного наближення, обчисленого методом найменших квадратів на основі відомих даних"}, "TRIMMEAN": {"a": "(масив; частка)", "d": "Повертає середнє внутрішньої частки набору даних"}, "TTEST": {"a": "(масив1; масив2; боки; тип)", "d": "Повертає імовірність, яка пов'язана із t-тестом студента"}, "VAR": {"a": "(число1; [число2]; ...)", "d": "Обчислює відхилення на основі вибірки (ігноруючи логічні значення та текст)"}, "VAR.P": {"a": "(число1; [число2]; ...)", "d": "Обчислює дисперсію на основі генеральної сукупності (ігноруючи логічні значення й текст)"}, "VAR.S": {"a": "(число1; [число2]; ...)", "d": "Обчислює дисперсію на основі вибірки (ігноруючи логічні значення й текст)"}, "VARA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює дисперсію на основі вибірки, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1"}, "VARP": {"a": "(число1; [число2]; ...)", "d": "Обчислює відхилення на основі генеральної сукупності (ігноруючи логічні значення та текст)"}, "VARPA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює дисперсію на основі генеральної сукупності, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1"}, "WEIBULL": {"a": "(x; альфа; бета; інтегральна)", "d": "Повертає розподіл Вейбулла"}, "WEIBULL.DIST": {"a": "(x; альфа; бета; сукупне)", "d": "Повертає розподіл Вейбулла"}, "Z.TEST": {"a": "(масив; x; [сигма])", "d": "Повертає двобічне P-значення z-тесту"}, "ZTEST": {"a": "(масив; x; [сигма])", "d": "Повертає двобічне P-значення z-тесту"}, "ACCRINT": {"a": "(дата_випуску; перша_виплата; дата_угоди; ставка; номінал; частота; [базис]; [метод_обчисл])", "d": "Повертає накопичений відсоток за цінними паперами з періодичною виплатою відсотків."}, "ACCRINTM": {"a": "(дата_випуску; дата_угоди; ставка; номінал; [базис])", "d": "Повертає накопичений відсоток для цінних паперів із виплатою відсотків у момент погашення"}, "AMORDEGRC": {"a": "(поч_вартість; дата_придбан; перш_період; зал_вартість; період; ставка; [базис])", "d": "Повертає пропорційну лінійну амортизацію активів для кожного звітного періоду"}, "AMORLINC": {"a": "(поч_вартість; дата_придбан; перш_період; зал_вартість; період; ставка; [базис])", "d": "Повертає пропорційну лінійну амортизацію активів для кожного звітного періоду"}, "COUPDAYBS": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість днів від початку купонного періоду до дня розрахунку"}, "COUPDAYS": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість днів у купонному періоді, який містить дату розрахунку"}, "COUPDAYSNC": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість днів від дати розрахунку до наступної купонної дати"}, "COUPNCD": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає наступну купонну дату після дати розрахунку"}, "COUPNUM": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість купонів, які можна оплатити між датою розрахунку та датою погашення"}, "COUPPCD": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає попередню купонну дату перед датою розрахунку"}, "CUMIPMT": {"a": "(ставка; кількість_періодів; поточна_сума; поч_період; кінц_період; тип)", "d": "Повертає сукупну суму відсотків, що виплачується між двома періодами"}, "CUMPRINC": {"a": "(ставка; кількість_періодів; поточна_сума; поч_період; кінц_період; тип)", "d": "Повертає сукупну суму, виплачувану за позикою між двома періодами"}, "DB": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; період; [місяці])", "d": "Повертає величину амортизації активу за вказаний період із використанням методу фіксованого зменшення залишку"}, "DDB": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; період; [коефіцієнт])", "d": "Повертає величину амортизації активу за вказаний період із використанням методу подвійного зменшення залишку або іншого вказаного методу"}, "DISC": {"a": "(дата_угоди; дата_погаш; ціна; погашення; [базис])", "d": "Повертає дисконтну ставку для цінних паперів"}, "DOLLARDE": {"a": "(дріб_грн; дріб)", "d": "Перетворює ціну в гривнях, виражену як дріб, на ціну в гривнях, виражену як десяткове число"}, "DOLLARFR": {"a": "(дес_грн; дріб)", "d": "Перетворює ціну в гривнях, виражену як десяткове число, на ціну в гривнях, виражену як дріб"}, "DURATION": {"a": "(дата_угоди; дата_погаш; купон; прибуток; частота; [базис])", "d": "Повертає річну дюрацію для цінних паперів із періодичною виплатою відсотків"}, "EFFECT": {"a": "(номін_ставка; кількість_пер)", "d": "Повертає річну ефективну відсоткову ставку"}, "FV": {"a": "(ставка; кількість_періодів; виплата; [поточна_сума]; [тип])", "d": "Повертає майбутню вартість інвестиції на основі постійних періодичних виплат і постійної відсоткової ставки"}, "FVSCHEDULE": {"a": "(сума; розклад)", "d": "Повертає майбутнє значення початкової суми після застосування ряду складних процентних ставок"}, "INTRATE": {"a": "(дата_угоди; дата_погаш; інвестиція; погашення; [базис])", "d": "Повертає відсоткову ставку для повністю інвестованих цінних паперів"}, "IPMT": {"a": "(ставка; період; кількість_періодів; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає суму сплати відсотків за інвестицією за вказаний період на основі постійних періодичних виплат і постійної відсоткової ставки"}, "IRR": {"a": "(значення; [припущення])", "d": "Повертає внутрішню ставку прибутковості для ряду періодичних грошових переміщень"}, "ISPMT": {"a": "(ставка; період; кількість_періодів; поточна_сума)", "d": "Повертає відсотки, що сплачуються за певний період інвестиції"}, "MDURATION": {"a": "(дата_угоди; дата_погаш; купон; прибуток; частота; [базис])", "d": "Повертає модифіковану дюрацію Маколея для цінних паперів із очікуваною номінальною вартістю 100 грн."}, "MIRR": {"a": "(значення; ставка_фінанс; ставка_реінвест)", "d": "Повертає внутрішню ставку прибутковості від постійного руху грошових коштів з урахуванням як витрат на інвестування, так і відсотка реінвестування для надходжень"}, "NOMINAL": {"a": "(ефект_ставка; кількість_пер)", "d": "Повертає річну номінальну відсоткову ставку"}, "NPER": {"a": "(ставка; виплата; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає кількість періодів сплати за інвестицією на основі постійних періодичних виплат і постійної відсоткової ставки"}, "NPV": {"a": "(ставка; значення1; [значення2]; ...)", "d": "Повертає чисту поточну вартість інвестиції на основі дисконтної ставки та вартості майбутніх виплат (від'ємні значення) і надходжень (додатні значення)"}, "ODDFPRICE": {"a": "(дата_угоди; дата_погаш; дата_випуску; перша_виплата; ставка; прибуток; погашення; частота; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із нерегулярним першим періодом"}, "ODDFYIELD": {"a": "(дата_угоди; дата_погаш; дата_випуску; перша_виплата; ставка; ціна; погашення; частота; [базис])", "d": "Повертає прибуток для цінних паперів із нерегулярним першим періодом"}, "ODDLPRICE": {"a": "(дата_угоди; дата_погаш; ост_виплата; ставка; прибуток; погашення; частота; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із нерегулярним останнім періодом"}, "ODDLYIELD": {"a": "(дата_угоди; дата_погаш; ост_виплата; ставка; ціна; погашення; частота; [базис])", "d": "Повертає прибуток для цінних паперів із нерегулярним останнім періодом"}, "PDURATION": {"a": "(ставка; поточне_значення; майбутнє_значення)", "d": "Повертає число період<PERSON>в, потр<PERSON>бних, щоб інвестиція досягла вказаного значення"}, "PMT": {"a": "(ставка; кількість_періодів; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає суму чергової виплати за позичкою на основі постійних періодичних виплат і постійної відсоткової ставки"}, "PPMT": {"a": "(ставка; період; кількість_періодів; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає величину плати на погашення основної суми за інвестицією на основі постійних періодичних виплат і незмінної відсоткової ставки"}, "PRICE": {"a": "(дата_угоди; дата_погаш; ставка; прибуток; погашення; частота; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із періодичною виплатою відсотків"}, "PRICEDISC": {"a": "(дата_угоди; дата_погаш; знижка; погашення; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості дисконтованих цінних паперів"}, "PRICEMAT": {"a": "(дата_угоди; дата_погаш; дата_випуску; ставка; прибуток; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із виплатою відсотків у момент погашення"}, "PV": {"a": "(ставка; кількість_періодів; виплата; [майбутня_сума]; [тип])", "d": "Повертає поточну вартість інвестиції – загальну суму, яка на цей час дорівнює сукупності майбутніх виплат"}, "RATE": {"a": "(кількість_періодів; виплата; поточна_сума; [майбутня_сума]; [тип]; [прогноз])", "d": "Повертає відсоткову ставку за позичкою або інвестицією за один період (наприклад, використовується значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)"}, "RECEIVED": {"a": "(дата_угоди; дата_погаш; інвестиція; знижка; [базис])", "d": "Повертає суму, отриману на момент погашення повністю інвестованих цінних паперів"}, "RRI": {"a": "(кількість_періодів; поточне_значення; майбутнє_значення)", "d": "Повертає еквівалентне значення відсоткової ставки для приросту інвестиції"}, "SLN": {"a": "(поч_вартість; зал_вартість; термін_експлуатації)", "d": "Повертає величину амортизації активу за один період із використанням лінійного методу"}, "SYD": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; період)", "d": "Повертає величину амортизації активу за вказаний період із використанням методу підсумовування річних чисел"}, "TBILLEQ": {"a": "(дата_угоди; дата_погаш; знижка)", "d": "Повертає еквівалентний облігації прибуток за казначейським векселем"}, "TBILLPRICE": {"a": "(дата_угоди; дата_погаш; знижка)", "d": "Повертає ціну за 100 грн. номінальної вартості для казначейського векселя"}, "TBILLYIELD": {"a": "(дата_угоди; дата_погаш; ціна)", "d": "Повертає прибуток за казначейським векселем"}, "VDB": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; поч_період; кін_період; [коефіцієнт]; [не_переходити])", "d": "Повертає величину амортизації активу за будь-який указаний період, зокрема за часткові періоди, з використанням методу подвійного зменшення залишку або іншого вказаного методу"}, "XIRR": {"a": "(значення; дати; [прогноз])", "d": "Повертає внутрішнє значення прибутковості для запланованого руху грошових коштів"}, "XNPV": {"a": "(ставка; значення; дати)", "d": "Повертає чисту вартість для графіку руху грошових коштів"}, "YIELD": {"a": "(дата_угоди; дата_погаш; ставка; ціна; погашення; частота; [базис])", "d": "Повертає прибуток за цінними паперами з періодичною виплатою відсотків"}, "YIELDDISC": {"a": "(дата_угоди; дата_погаш; ціна; погашення; [базис])", "d": "Повертає річний прибуток для дисконтних цінних паперів (наприклад, казначейський вексель)"}, "YIELDMAT": {"a": "(дата_угоди; дата_погаш; дата_випуску; ставка; ціна; [базис])", "d": "Повертає річний прибуток за цінними паперами з виплатою відсотків у момент погашення"}, "ABS": {"a": "(число)", "d": "Повертає модуль (абсолютне значення) числа, тобто число без знака"}, "ACOS": {"a": "(число)", "d": "Повертає арккосинус числа в радіанах, у діапазоні від 0 до Пі. Арккосинус числа – це кут, косинус якого дорівнює числу"}, "ACOSH": {"a": "(число)", "d": "Повертає обернений гіперболічний косинус (ареа-косинус) числа"}, "ACOT": {"a": "(число)", "d": "Повертає арккотангенс числа в радіанах у діапазоні від 0 до Пі"}, "ACOTH": {"a": "(число)", "d": "Повертає обернений гіперболічний котангенс числа"}, "AGGREGATE": {"a": "(номер_функції; параметри; посилання1; ...)", "d": "Повертає сукупність для списку або бази даних"}, "ARABIC": {"a": "(текст)", "d": "Перетворює римську цифру на арабську"}, "ASC": {"a": "(текст)", "d": "Для мов із двобайтними наборами символів (DBCS) ця функція перетворює двобайтні символи на однобайтні"}, "ASIN": {"a": "(число)", "d": "Повертає арксинус числа в радіанах, у діапазоні від -Пі/2 до Пі/2"}, "ASINH": {"a": "(число)", "d": "Повертає обернений гіперболічний синус (ареа-синус) числа"}, "ATAN": {"a": "(число)", "d": "Повертає арктангенс числа в радіанах, у діапазоні від -Пі/2 до Пі/2"}, "ATAN2": {"a": "(x; y)", "d": "Повертає арктангенс для вказаних координат x та y, в радіанах від -Пі до Пі, виключаючи -Пі"}, "ATANH": {"a": "(число)", "d": "Повертає обернений гіперболічний тангенс (ареа-тангенс) числа"}, "BASE": {"a": "(число; система_числення; [мін_довжина])", "d": "Перетворює число на текстовий вираз за заданою системою (основою) числення"}, "CEILING": {"a": "(число; точність)", "d": "Округлює число найближчого більшого кратного точності"}, "CEILING.MATH": {"a": "(число; [точність]; [модуль])", "d": "Округлює число до найближчого більшого за модулем цілого числа або кратного значенню точності"}, "CEILING.PRECISE": {"a": "(число; [точність])", "d": "Повертає число, округлене до найближчого більшого цілого або до кратного значенню точності"}, "COMBIN": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість комбінацій для заданого числа елементів"}, "COMBINA": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість комбінацій (з повторами) для заданої кількості елементів"}, "COS": {"a": "(число)", "d": "Повертає косинус кута"}, "COSH": {"a": "(число)", "d": "Повертає гіперболічний косинус числа"}, "COT": {"a": "(число)", "d": "Повертає котангенс кута"}, "COTH": {"a": "(число)", "d": "Повертає гіперболічний котангенс числа"}, "CSC": {"a": "(число)", "d": "Повертає косеканс кута"}, "CSCH": {"a": "(число)", "d": "Повертає гіперболічний косеканс кута"}, "DECIMAL": {"a": "(число; система_числення)", "d": "Перетворює текстове вираження числа за заданою основою на десяткове число"}, "DEGREES": {"a": "(кут)", "d": "Перетворює радіани на градуси"}, "ECMA.CEILING": {"a": "(число; точність)", "d": "Округлює число найближчого більшого кратного точності"}, "EVEN": {"a": "(число)", "d": "Округлює число до найближчого парного цілого, більшого (додатні числа) або меншого (від’ємні числа)"}, "EXP": {"a": "(число)", "d": "Повертає експоненту заданого числа"}, "FACT": {"a": "(число)", "d": "Повертає факторіал числа, який дорівнює 1*2*3*...*число"}, "FACTDOUBLE": {"a": "(число)", "d": "Повертає подвійний факторіал числа"}, "FLOOR": {"a": "(число; точність)", "d": "Округлює число до найближчого меншого кратного точності"}, "FLOOR.PRECISE": {"a": "(число; [точність])", "d": "Повертає число, округлене до найближчого меншого цілого або до кратного значенню точності"}, "FLOOR.MATH": {"a": "(число; [точність]; [модуль])", "d": "Округлює число до найближчого меншого за модулем цілого числа або кратного значенню точності"}, "GCD": {"a": "(число1; [число2]; ...)", "d": "Повертає найбільший спільний дільник"}, "INT": {"a": "(число)", "d": "Округлює число до найближчого меншого цілого"}, "ISO.CEILING": {"a": "(число; [точність])", "d": "Повертає число, округлене до найближчого більшого цілого або до кратного значенню точності. Число округлюється незалежно від його знака. Однак, якщо число – нуль або його значення точності дорівнює нулю, буде повернуто нуль."}, "LCM": {"a": "(число1; [число2]; ...)", "d": "Повертає найменше спільне кратне"}, "LN": {"a": "(число)", "d": "Повертає натуральний логарифм числа"}, "LOG": {"a": "(число; [основа])", "d": "Повертає логарифм числа за вказаною основою"}, "LOG10": {"a": "(число)", "d": "Повертає десятковий логарифм числа"}, "MDETERM": {"a": "(масив)", "d": "Повертає визначник матриці, яка зберігається в масиві"}, "MINVERSE": {"a": "(масив)", "d": "Повертає обернену матрицю для матриці, яка зберігається в масиві"}, "MMULT": {"a": "(масив1; масив2)", "d": "Повертає добуток матриць, які зберігаються у двох масивах, – масив із кількістю рядків першого масиву і кількістю стовпців другого масиву"}, "MOD": {"a": "(число; дільник)", "d": "Повертає залишок від ділення"}, "MROUND": {"a": "(число; точність)", "d": "Повертає число, округлене з потрібною точністю"}, "MULTINOMIAL": {"a": "(число1; [число2]; ...)", "d": "Повертає багаточлен набору чисел"}, "MUNIT": {"a": "(вимір)", "d": "Повертає матрицю одиниці для вказаного виміру"}, "ODD": {"a": "(число)", "d": "Округлює число до найближчого непарного цілого, більшого (додатні числа) або меншого (від’ємні числа)"}, "PI": {"a": "()", "d": "Повертає число Пі, округлене до 15 знаків після коми (значення 3,14159265358979)"}, "POWER": {"a": "(число; степінь)", "d": "Повертає результат піднесення числа до степеня"}, "PRODUCT": {"a": "(число1; [число2]; ...)", "d": "Повертає добуток усіх аргументів"}, "QUOTIENT": {"a": "(чисельник; знаменник)", "d": "Повертає цілу частину частки"}, "RADIANS": {"a": "(кут)", "d": "Перетворює градуси на радіани"}, "RAND": {"a": "()", "d": "Повертає рівномірно розподілене випадкове число, яке більше або дорівнює 0 і менше за 1 (змінюється в разі переобчислення)"}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "Повертає масив випадкових чисел"}, "RANDBETWEEN": {"a": "(нижн_межа; верх_межа)", "d": "Повертає випадкове число між двома вказаними числами"}, "ROMAN": {"a": "(число; [форма])", "d": "Перетворює арабські числа на римські, у текстовому форматі"}, "ROUND": {"a": "(число; кількість_розрядів)", "d": "Округлює число до заданої кількості десяткових знаків"}, "ROUNDDOWN": {"a": "(число; кількість_розрядів)", "d": "Округлює число до найближчого меншого за модулем"}, "ROUNDUP": {"a": "(число; кількість_розрядів)", "d": "Округлює число до найближчого більшого за модулем"}, "SEC": {"a": "(число)", "d": "Повертає секанс кута"}, "SECH": {"a": "(число)", "d": "Повертає гіперболічний секанс кута"}, "SERIESSUM": {"a": "(x; n; m; коефіцієнти)", "d": "Повертає суму степеневого ряду на основі формули"}, "SIGN": {"a": "(число)", "d": "Повертає знак числа: 1 – якщо число додатне, 0 – якщо воно дорівнює нулю, –1 – якщо число від'ємне"}, "SIN": {"a": "(число)", "d": "Повертає синус кута"}, "SINH": {"a": "(число)", "d": "Повертає гіперболічний синус числа"}, "SQRT": {"a": "(число)", "d": "Повертає квадратний корінь числа"}, "SQRTPI": {"a": "(число)", "d": "Повертає квадратний корінь виразу (число * пі)"}, "SUBTOTAL": {"a": "(номер_функції; посилання1; ...)", "d": "Повертає проміжні підсумки списку або бази даних"}, "SUM": {"a": "(число1; [число2]; ...)", "d": "Підсумовує всі числа в діапазоні клітинок"}, "SUMIF": {"a": "(діа<PERSON><PERSON><PERSON><PERSON><PERSON>; крите<PERSON><PERSON><PERSON>; [діапазон_для_суми])", "d": "Підсумовує клітинки, задані вказаним критерієм"}, "SUMIFS": {"a": "(діапазон_суми; діапазон_критерію; критер<PERSON>й; ...)", "d": "Додає клітинки, визначені наявним набором умов або критеріїв"}, "SUMPRODUCT": {"a": "(масив1; [масив2]; [масив3]; ...)", "d": "Повертає суму добутків елементів відповідних масивів або діапазонів"}, "SUMSQ": {"a": "(число1; [число2]; ...)", "d": "Повертає суму квадра<PERSON><PERSON>в аргументів. Аргументи можуть бути числами, масивами, іменами або посиланнями на клітинки з числами"}, "SUMX2MY2": {"a": "(масив_x; масив_y)", "d": "Повертає суму різниць квадратів відповідних значень двох масивів"}, "SUMX2PY2": {"a": "(масив_x; масив_y)", "d": "Повертає суму сум квадратів відповідних значень двох масивів"}, "SUMXMY2": {"a": "(масив_x; масив_y)", "d": "Повертає суму квадратів різниць відповідних значень двох масивів"}, "TAN": {"a": "(число)", "d": "Повертає тангенс кута"}, "TANH": {"a": "(число)", "d": "Повертає гіперболічний тангенс числа"}, "TRUNC": {"a": "(число; [кількість_розрядів])", "d": "Відтинає дробову частину числа, залишаючи цілу частину"}, "ADDRESS": {"a": "(номер_рядка; номер_стовпця; [тип_посилання]; [a1]; [ім'я_аркуша])", "d": "Повертає в текстовому вигляді посилання на клітинку з указаними номерами рядка й стовпця"}, "CHOOSE": {"a": "(індекс; значення1; [значення2]; ...)", "d": "Вибирає значення або виконувану дію зі списку значень за індексом"}, "COLUMN": {"a": "([посилання])", "d": "Повертає номер стовпця, визначеного посиланням"}, "COLUMNS": {"a": "(масив)", "d": "Повертає кількість стовпців у посиланні або масиві"}, "FORMULATEXT": {"a": "(посилання)", "d": "Повертає формулу як рядок"}, "HLOOKUP": {"a": "(значення_підстановки; масив_таблиці; номер_рядка; [точність_пошуку])", "d": "Шукає значення у верхньому рядку таблиці або масиву значень і повертає значення з того ж стовпця та вказаного рядка"}, "HYPERLINK": {"a": "(адреса; [ім'я])", "d": "Створює посилання, яке відкриває документ на жорсткому диску, на сервері мережі або в Інтернеті"}, "INDEX": {"a": "(масив; номер_рядка; [номер_стовпця]!посилання; номер_рядка; [номер_стовпця]; [номер_області])", "d": "Повертає значення або посилання на клітинку на перетині певних рядка й стовпця в указаному діапазоні"}, "INDIRECT": {"a": "(посилання_на_клітинку; [a1])", "d": "Повертає посилання, указане текстовим рядком"}, "LOOKUP": {"a": "(шукане_значення; вектор_перегляду; [вектор_результатів]!шукане_значення; масив)", "d": "Шукає значення в одному рядку, в одному стовпці або в масиві. Забезпечує зворотну сумісність"}, "MATCH": {"a": "(шукане_значення; масив_який_переглядається; [тип_зіставлення])", "d": "Повертає відносну позицію в масиві елемента, який відповідає вказаному значенню з урахуванням указаного порядку"}, "OFFSET": {"a": "(посилання; рядки; стовпці; [висота]; [ширина])", "d": "Повертає посилання на діапазон, віддалений від указаного посилання на вказану кількість рядків і стовпців"}, "ROW": {"a": "([посилання])", "d": "Повертає номер рядка, визначеного посиланням"}, "ROWS": {"a": "(масив)", "d": "Повертає кількість рядків у посиланні або масиві"}, "TRANSPOSE": {"a": "(масив)", "d": "Перетворює вертикальний діапазон клітинок на горизонтальний або навпаки"}, "UNIQUE": {"a": "(масив; [за_стовпцем]; [рівно_один_раз])", "d": "Повертає унікальні значення з діапазону або масиву."}, "VLOOKUP": {"a": "(значення_підстановки; масив_таблиці; номер_стовпця; [точність_пошуку])", "d": "Шукає значення в найлівішому стовпці таблиці та повертає значення з того ж рядка та вказаного стовпця. За замовчуванням таблиця має бути відсортована за зростанням"}, "XLOOKUP": {"a": "(значення_для_пошуку; масив_для_пошуку; масив_для_повернення; [якщо_не_знайдено]; [режим_зіставлення]; [режим_пошуку])", "d": "Пошук збігів у масиві або діапазоні та повернення відповідного елемента з другого масиву або діапазону. За замовчуванням ведеться пошук точних збігів"}, "CELL": {"a": "(тип_відомостей; [посилання])", "d": "Повертає інформацію про форматування, розташування або вміст клітинки"}, "ERROR.TYPE": {"a": "(значення_помилки)", "d": "Повертає код, який відповідає значенню помилки."}, "ISBLANK": {"a": "(значення)", "d": "Перевіряє, чи вказує посилання на пусту клітинку, і повертає значення TRUE або FALSE"}, "ISERR": {"a": "(значення)", "d": "Перевіряє, чи значення є помилкою, відмінною від #N/A, і повертає значення TRUE або FALSE"}, "ISERROR": {"a": "(значення)", "d": "Перевіряє, чи значення є помилкою, і повертає значення TRUE або FALSE"}, "ISEVEN": {"a": "(число)", "d": "Повертає значення TRUE, якщо число парне"}, "ISFORMULA": {"a": "(посилання)", "d": "Перевіряє, чи посилання надано на клітинку з формулою, і повертає значення ІСТИНА або ХИБНІСТЬ"}, "ISLOGICAL": {"a": "(значення)", "d": "Перевіряє, чи значення логічне (TRUE або FALSE), і повертає TRUE або FALSE"}, "ISNA": {"a": "(значення)", "d": "Перевіряє, чи значення недоступне (#N/A), і повертає значення TRUE або FALSE"}, "ISNONTEXT": {"a": "(значення)", "d": "Повертає TRUE, якщо значення не текст, і FALSE, якщо так (пусті клітинки не вважаються текстом)"}, "ISNUMBER": {"a": "(значення)", "d": "Перевіряє, чи значення число, і повертає значення TRUE або FALSE"}, "ISODD": {"a": "(число)", "d": "Повертає значення TRUE, якщо число непарне"}, "ISREF": {"a": "(значення)", "d": "Перевіряє, чи значення посилання, і повертає значення TRUE або FALSE"}, "ISTEXT": {"a": "(значення)", "d": "Перевіряє, чи значення текст, і повертає значення TRUE або FALSE"}, "N": {"a": "(значення)", "d": "Перетворює нечислові значення на числа, дати – на дати у вигляді чисел, значення TRUE на 1, решту значень – на 0 (нуль)"}, "NA": {"a": "()", "d": "Повертає значення помилки #N/A (значення недоступне)"}, "SHEET": {"a": "([значення])", "d": "Повертає номер аркуша для аркуша, на який надано посилання"}, "SHEETS": {"a": "([посилання])", "d": "Повертає кількість аркушів у посиланні"}, "TYPE": {"a": "(значення)", "d": "Повертає ціле число, яке позначає тип даних указаного значення: число = 1; текст = 2; логічне значення = 4; помилка = 16; масив = 64; комбінований тип = 128"}, "AND": {"a": "(лог_значення1; [лог_значення2]; ...)", "d": "Перевіряє, чи всі аргументи істинні, і повертає значення TRUE, якщо це так"}, "FALSE": {"a": "()", "d": "Повертає логічне значення FALSE"}, "IF": {"a": "(лог_вираз; [значення_якщо_істина]; [значення_якщо_хибність])", "d": "Перевіряє, чи виконується умова, і повертає одне значення, якщо вона виконується, та інше значення, якщо ні"}, "IFS": {"a": "(логічна_перевірка; значення_якщо_істина; ...)", "d": "Перевірка однієї або кількох умов і повернення значення, що відповідає першій умові зі значенням ІСТИНА"}, "IFERROR": {"a": "(значення; значення_якщо_помилка)", "d": "Повертає значення значення_якщо_помилка, якщо вираз містить помилку, або значення виразу, якщо помилки немає"}, "IFNA": {"a": "(значення; значення_якщо_немає_даних)", "d": "Повертає значення, яке вказується, якщо значення виразу – \"#N/A\"; в іншому випадку повертає результат виразу"}, "NOT": {"a": "(лог_значення)", "d": "Змінює значення TRUE на FALSE, а FALSE на TRUE"}, "OR": {"a": "(лог_значення1; [лог_значення2]; ...)", "d": "Перевіряє, чи має принаймні один аргумент значення TRUE, і повертає значення TRUE або FALSE. Значення FALSE повертається, лише якщо всі аргументи мають значення FALSE"}, "SWITCH": {"a": "(вираз; значення1; результат1; [за_замовчуванням_або_значення2]; [результат2]; ...)", "d": "Порівняння виразу зі списком значень і повернення першого збігу як результату. Якщо збігів немає, повертається необов’язкове значення за замовчуванням"}, "TRUE": {"a": "()", "d": "Повертає логічне значення TRUE"}, "XOR": {"a": "(лог_значення1; [лог_значення2]; ...)", "d": "Повертає логічний об’єкт \"виключне АБО\" для всіх аргументів"}, "TEXTBEFORE": {"a": "(текст, роздільник, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Повертає текст, який перед розділенням символів."}, "TEXTAFTER": {"a": "(текст, роздільник, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Повертає текст після розділення символів."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Розділяє текст на рядки або стовпці за допомогою роздільників."}, "WRAPROWS": {"a": "(вектор, wrap_count, [pad_with])", "d": "Переносить вектор рядка або стовпця після вказаної кількості значень."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Вертикально укладає масиви в один масив."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Горизонтально укладає масиви в один масив."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Повертає рядки з масиву або посилання."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Повертає стовпці з масиву або посилання."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Повертає масив як один стовпець."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Повертає масив як один рядок. "}, "WRAPCOLS": {"a": "(вектор, wrap_count, [pad_with])", "d": "Переносить вектор рядка або стовпця після вказаної кількості значень."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Повертає рядки або стовпці з початку або кінця масиву."}, "DROP": {"a": "(array, rows, [columns])", "d": "Видаляє рядки або стовпці з початку або кінця масиву."}}