﻿<!DOCTYPE html>
<html>
	<head>
		<title>Registerkarte Übergänge</title>
		<meta charset="utf-8" />
        <meta name="description" content="Einführung in die Benutzeroberfläche des Präsentationseditors – Registerkarte Übergänge" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Registerkarte Übergänge</h1>
        <p><PERSON>f der Register<PERSON> <b>Übergänge</b> im <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> können Sie Folienübergänge verwalten. Sie können Übergangseffekte hinzufügen, die Übergangsgeschwindigkeit einstellen und andere Folienübergangsparameter konfigurieren, um Ihre Präsentation anzupassen.</p>
            <div class="onlineDocumentFeatures">
                <p>Dialogbox Online-Präsentationseditor:</p>
                <p><img alt="Registerkarte Übergänge" src="../images/interface/transitionstab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Dialogbox Desktop-Präsentationseditor:</p>
                <p><img alt="Registerkarte Übergänge" src="../images/interface/desktop_transitionstab.png" /></p>
            </div>
            <p>Sie können:</p>
            <ul>
                <li>einen <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Übergangseffekt</a> wählen,</li>
                <li>für jeden Übergangseffekt geeignete <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Parameter</a> festlegen,</li>
                <li><a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Übergangsdauer</a> festlegen,</li>
                <li>einen <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Übergang in der Vorschau</a> anzeigen,</li>
                <li>angeben, wie lange die Folie angezeigt werden soll, indem Sie die Optionen <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Bei Klicken beginnen und Verzögern</a> aktivieren,</li>
                <li>den Übergang auf alle Folien anwenden, indem Sie auf die Schaltfläche <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Auf alle Folien anwenden</a> klicken.</li>
            </ul>
		</div>
	</body>
</html>