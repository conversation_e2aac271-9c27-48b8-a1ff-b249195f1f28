﻿<!DOCTYPE html>
<html>
	<head>
		<title>View Settings and Navigation Tools</title>
		<meta charset="utf-8" />
		<meta name="description" content="The description of view settings and navigation tools such as zoom, previous/next slide buttons" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>View Settings and Navigation Tools</h1>
			<p><b>Presentation Editor</b> offers several tools to help you view and navigate through your presentation: zoom, previous/next slide buttons, slide number indicator.</p>
			<h3>Adjust the View Settings</h3>
			<p>To adjust default view settings and set the most convenient mode to work with the presentation, click the <b>View settings</b> <span class="icon icon-viewsettingsicon"></span> icon on the right side of the editor header and select which interface elements you want to be hidden or shown.
			You can select the following options from the <b>View settings</b> drop-down list:
			</p>
			<ul>
			<li><b>Hide Toolbar</b> -  hides the top toolbar that contains commands while tabs remain visible. When this option is enabled, you can click any tab to display the toolbar. The toolbar is displayed until you click anywhere outside it. To disable this mode, click the <b>View settings</b> <div class = "icon icon-viewsettingsicon"></div> icon and click the <b>Hide Toolbar</b> option once again. The top toolbar will be displayed all the time. 
                <p class="note"><b>Note</b>: alternatively, you can just double-click any tab to hide the top toolbar or display it again.</p>
            </li>
			<li><b>Hide Status Bar</b> - hides the bottommost bar where the <b>Slide Number Indicator</b> and <b>Zoom</b> buttons are situated. To show the hidden <b>Status Bar</b> click this option once again.</li>
            <li><b>Hide Rulers</b> - hides rulers which are used to set up tab stops and paragraph indents within the text boxes. To show the hidden <b>Rulers</b> click this option once again.</li>
			</ul>
			<p>The right sidebar is minimized by default. To expand it, select any object/slide and click the icon of the currently activated tab on the right. To minimize the right sidebar, click the icon once again. The left sidebar width is adjusted by simple drag-and-drop: 
			move the mouse cursor over the left sidebar border so that it turns into the bidirectional arrow and drag the border to the left to reduce the sidebar width or to the right to extend it.</p>
			<h3>Use the Navigation Tools</h3>
			<p>To navigate through your presentation, use the following tools:</p>
			<p>The <b>Zoom</b> buttons are situated in the right lower corner and are used to zoom in and out the current presentation.  
			To change the currently selected zoom value that is displayed in percent, click it and select one of the available zoom options from the list 
			or use the <b>Zoom in</b> <span class="icon icon-zoomin"></span> or <b>Zoom out</b> <span class="icon icon-zoomout"></span> buttons. 
			Click the <b>Fit width</b> <span class="icon icon-fitwidth"></span> icon to fit the slide width to the visible part of the working area.
			To fit the whole slide to the visible part of the working area, click the <b>Fit slide</b> <span class="icon icon-fitslide"></span> icon.
			Zoom settings are also available in the <b>View settings</b> <span class="icon icon-viewsettingsicon"></span> drop-down list that can be useful if you decide to hide the <b>Status Bar</b>.</p>
			<p class="note"><b>Note</b>: you can set a default zoom value. Switch to the <b>File</b> tab of the top toolbar, go to the <b>Advanced Settings...</b> section, choose the necessary <b>Default Zoom Value</b> from the list and click the <b>Apply</b> button.</p>
			<p>To go to the previous or next slide when editing the presentation, you can use the <span class="icon icon-previouspage"></span> and <span class="icon icon-nextpage"></span> buttons at the top and bottom of the vertical scroll bar located to the right of the slide.</p>
			<p>The <b>Slide Number Indicator</b> shows the current slide as a part of all the slides in the current presentation (slide 'n' of 'nn'). 
			Click this caption to open the window where you can enter the slide number and quickly go to it. If you decide to hide the <b>Status Bar</b>, this tool will become inaccessible.</p>
			
			
		</div>
	</body>
</html>