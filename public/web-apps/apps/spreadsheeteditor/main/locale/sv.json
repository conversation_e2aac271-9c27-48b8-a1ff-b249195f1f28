{"cancelButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Varning", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON><PERSON> ditt meddelande här", "Common.Controllers.History.notcriticalErrorTitle": "Varning", "Common.define.chartData.textArea": "<PERSON><PERSON>r<PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Staplad yta", "Common.define.chartData.textAreaStackedPer": "100% staplat område", "Common.define.chartData.textBar": "<PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Grupperad kolumn", "Common.define.chartData.textBarNormal3d": "3-D grupperad kolumn", "Common.define.chartData.textBarNormal3dPerspective": "3-D kolumn", "Common.define.chartData.textBarStacked": "Staplad kolumn", "Common.define.chartData.textBarStacked3d": "3-D staplad kolumn", "Common.define.chartData.textBarStackedPer": "100% staplad kolumn", "Common.define.chartData.textBarStackedPer3d": "3-D 100% staplad kolumn", "Common.define.chartData.textCharts": "Diagram", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Staplat område - grupperad kolumn", "Common.define.chartData.textComboBarLine": "Grupperad kolumn - rad", "Common.define.chartData.textComboBarLineSecondary": "Grupperad kolumn - rad på andra axeln", "Common.define.chartData.textComboCustom": "Anpassad kombination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Grupperad stapel", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> grupperad stapel", "Common.define.chartData.textHBarStacked": "Stap<PERSON> stapel", "Common.define.chartData.textHBarStacked3d": "3-D staplad stapel", "Common.define.chartData.textHBarStackedPer": "100% staplad stapel", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% staplad stapel", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-<PERSON> linje", "Common.define.chartData.textLineMarker": "<PERSON>je med markö<PERSON>", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON> linje", "Common.define.chartData.textLineStackedMarker": "Staplad linje med <PERSON>ingar", "Common.define.chartData.textLineStackedPer": "100% staplad linje", "Common.define.chartData.textLineStackedPerMarker": "100% staplad linje med markörer", "Common.define.chartData.textPie": "<PERSON><PERSON>", "Common.define.chartData.textPie3d": "3-<PERSON> paj", "Common.define.chartData.textPoint": "XY (Spridning)", "Common.define.chartData.textScatter": "Sprida ut", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON> med raka linjer", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON><PERSON> med raka linjer och marker<PERSON>r", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON><PERSON> med mjuka linjer", "Common.define.chartData.textScatterSmoothMarker": "<PERSON><PERSON><PERSON> med mjuka linjer och markeringar", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "Lager", "Common.define.chartData.textSurface": "Yta", "Common.define.chartData.textWinLossSpark": "Vinst / förlust", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Inget format inställt", "Common.define.conditionalData.text1Above": "1: a dev över", "Common.define.conditionalData.text1Below": "1: a dev under", "Common.define.conditionalData.text2Above": "2 std dev över", "Common.define.conditionalData.text2Below": "2 std dev under", "Common.define.conditionalData.text3Above": "3 std dev över", "Common.define.conditionalData.text3Below": "3 std dev under", "Common.define.conditionalData.textAbove": "<PERSON><PERSON>", "Common.define.conditionalData.textAverage": "Genomsnitt", "Common.define.conditionalData.textBegins": "<PERSON><PERSON><PERSON><PERSON> med", "Common.define.conditionalData.textBelow": "Under", "Common.define.conditionalData.textBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Töm", "Common.define.conditionalData.textBlanks": "<PERSON><PERSON><PERSON><PERSON> tomma", "Common.define.conditionalData.textBottom": "<PERSON><PERSON>", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Data stapel", "Common.define.conditionalData.textDate": "Datum", "Common.define.conditionalData.textDuplicate": "Du<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Slutar med", "Common.define.conditionalData.textEqAbove": "Lika med eller högre", "Common.define.conditionalData.textEqBelow": "Lika med eller lägre", "Common.define.conditionalData.textEqual": "Lika med", "Common.define.conditionalData.textError": "<PERSON><PERSON>", "Common.define.conditionalData.textErrors": "Inneh<PERSON>ller fel", "Common.define.conditionalData.textFormula": "Formel", "Common.define.conditionalData.textGreater": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreaterEq": "<PERSON><PERSON><PERSON> än eller lika med", "Common.define.conditionalData.textIconSets": "Ikonuppsättningar", "Common.define.conditionalData.textLast7days": "Senaste 7 dagarna", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLastWeek": "se<PERSON>e veckan", "Common.define.conditionalData.textLess": "<PERSON><PERSON> än", "Common.define.conditionalData.textLessEq": "<PERSON><PERSON> än eller lika med", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON><PERSON> m<PERSON>", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON><PERSON> ve<PERSON>a", "Common.define.conditionalData.textNotBetween": "inte mellan", "Common.define.conditionalData.textNotBlanks": "<PERSON><PERSON><PERSON><PERSON> inga <PERSON>", "Common.define.conditionalData.textNotContains": "Innehåller inte", "Common.define.conditionalData.textNotEqual": "Inte lika med", "Common.define.conditionalData.textNotErrors": "<PERSON><PERSON><PERSON><PERSON> inga fel", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON>", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> vecka", "Common.define.conditionalData.textToday": "<PERSON><PERSON>", "Common.define.conditionalData.textTomorrow": "I morgon", "Common.define.conditionalData.textTop": "Ö<PERSON><PERSON>", "Common.define.conditionalData.textUnique": "Unik", "Common.define.conditionalData.textValue": "Värdet är", "Common.define.conditionalData.textYesterday": "Igår", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.warnFileLocked": "Dokumentet redigeras i en annan applikation. Du kan fortsätta redigera och spara det som en kopia.", "Common.Translation.warnFileLockedBtnEdit": "Skapa en kopia", "Common.Translation.warnFileLockedBtnView": "Öppna skrivskyddad", "Common.UI.ButtonColored.textAutoColor": "Automatisk", "Common.UI.ButtonColored.textNewColor": "Lägg till ny egen färg", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> ramar", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> ramar", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON> stilar", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON> till", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett värde mellan 000000 och FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Ny", "Common.UI.ExtendedColorDialog.textRGBErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett numeriskt värde mellan 0 och 255", "Common.UI.HSBColorPicker.textNoColor": "Ingen färg", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Visa lösenord", "Common.UI.SearchBar.textFind": "<PERSON>ö<PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Stäng sökning", "Common.UI.SearchBar.tipNextResult": "Nästa resultat", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Öppna avancerade inställningar", "Common.UI.SearchBar.tipPreviousResult": "Föregående resultat", "Common.UI.SearchDialog.textHighlight": "Markera resultat", "Common.UI.SearchDialog.textMatchCase": "Skiftlägeskänslig", "Common.UI.SearchDialog.textReplaceDef": "Ange ersättningstext", "Common.UI.SearchDialog.textSearchStart": "Skriv din text här", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON> och er<PERSON>t", "Common.UI.SearchDialog.textTitle2": "<PERSON>ö<PERSON>", "Common.UI.SearchDialog.textWholeWords": "Endast hela ord", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON><PERSON> alla", "Common.UI.SynchronizeTip.textDontShow": "Visa inte detta meddelande igen", "Common.UI.SynchronizeTip.textSynchronize": "Dokumentet har ändrats av en annan användare. <br> <PERSON><PERSON><PERSON> för att spara dina ändringar och ladda uppdateringarna.", "Common.UI.ThemeColorPalette.textRecentColors": "Senaste färger", "Common.UI.ThemeColorPalette.textStandartColors": "Standardfärger", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Mörk kontrast", "Common.UI.Themes.txtThemeDark": "M<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Samma som systemet", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Stäng", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bekräftelse", "Common.UI.Window.textDontShow": "Visa inte detta meddelande igen", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Varning", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Views.About.txtAddress": "adress:", "Common.Views.About.txtLicensee": "LICENSINNEHAVARE", "Common.Views.About.txtLicensor": "LICENSGIVARE", "Common.Views.About.txtMail": "e-post:", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Version", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON> till", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Tillämpa när du arbetar", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorrigering", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformat när du skriver", "Common.Views.AutoCorrectDialog.textBy": "Av", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textHyperlink": "Sökvägar för internet och nätverk med hyperlänk", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematisk autokorrigering", "Common.Views.AutoCorrectDialog.textNewRowCol": "Inkludera nya rader och kolumner i tabellen", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Följande uttryck är igenkända matematiska uttryck. De kommer inte att kursiveras automatiskt.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON><PERSON> när du skriver", "Common.Views.AutoCorrectDialog.textReplaceType": "<PERSON><PERSON><PERSON><PERSON> text när du skriver", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "Återställ till standard", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Autokorrigering", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Erkända funktioner får endast innehålla bokstäverna A till Z, versaler eller gemener.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Alla uttryck du har lagt till kommer att tas bort och de borttagna kommer att återställas. Vill du fortsätta?", "Common.Views.AutoCorrectDialog.warnReplace": "Autokorrigeringsposten för %1 finns redan. Vill du ersätta den?", "Common.Views.AutoCorrectDialog.warnReset": "All autokorrigering som du lagt till kommer att tas bort och ändringar kommer att återställas till sina ursprungliga värden. Vill du fortsätta?", "Common.Views.AutoCorrectDialog.warnRestore": "Autokorrigeringen för %1 återställs till sitt ursprungliga värde. Vill du fortsätta?", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Författare A till Ö", "Common.Views.Comments.mniAuthorDesc": "Författare Ö till A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Nyaste", "Common.Views.Comments.mniFilterGroups": "Filtrera via grupp", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON> till", "Common.Views.Comments.textAddComment": "Lägg till kommentar", "Common.Views.Comments.textAddCommentToDoc": "Lägg till kommentar till dokumentet", "Common.Views.Comments.textAddReply": "Lägg till svar", "Common.Views.Comments.textAll": "<PERSON>a", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Stäng", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON>ng kommentarer", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Skriv din kommentar här", "Common.Views.Comments.textHintAddComment": "Lägg till kommentar", "Common.Views.Comments.textOpenAgain": "Öppna igen", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "So<PERSON>a kommentarer", "Common.Views.Comments.textViewResolved": "Du har inte behörighet att öppna kommentaren igen", "Common.Views.CopyWarningDialog.textDontShow": "Visa inte detta meddelande igen", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> ut och klistra in -åtgärder med redigeringsknapparna i verktygsfältet och snabbmenyn kommer endast att utföras inom denna flik.<br><PERSON><PERSON><PERSON> att kopiera eller klistra in från applikationer utanför fliken, använd följande kortkommandon:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, klipp ut och klistra in åtgärder", "Common.Views.CopyWarningDialog.textToCopy": "till kopia", "Common.Views.CopyWarningDialog.textToCut": "till urk<PERSON>p", "Common.Views.CopyWarningDialog.textToPaste": "till klistra in", "Common.Views.DocumentAccessDialog.textLoading": "Laddar...", "Common.Views.DocumentAccessDialog.textTitle": "Delningsinställningar", "Common.Views.EditNameDialog.textLabel": "Etikett:", "Common.Views.EditNameDialog.textLabelError": "<PERSON><PERSON><PERSON><PERSON> får inte vara tom.", "Common.Views.Header.labelCoUsersDescr": "Användare som redigera filen:", "Common.Views.Header.textAddFavorite": "Markera som favorit", "Common.Views.Header.textAdvSettings": "Avancerade inställningar", "Common.Views.Header.textBack": "Öppna filens plats", "Common.Views.Header.textCompactView": "D<PERSON>lj verktygsrad", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "Kombinera kalkylblad och statusfält", "Common.Views.Header.textRemoveFavorite": "<PERSON> bort från favoriter", "Common.Views.Header.textSaveBegin": "Sparar...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Alla ändringar sparade", "Common.Views.Header.textSaveExpander": "Alla ändringar sparade", "Common.Views.Header.textShare": "Dela", "Common.Views.Header.textZoom": "Zooma", "Common.Views.Header.tipAccessRights": "Hantera åtkomsträttighet för dokument", "Common.Views.Header.tipDownload": "Ladda ner fil", "Common.Views.Header.tipGoEdit": "<PERSON>igera nuvarande fil", "Common.Views.Header.tipPrint": "Skriv ut fil", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON> om", "Common.Views.Header.tipSave": "Spara", "Common.Views.Header.tipSearch": "<PERSON>ö<PERSON>", "Common.Views.Header.tipUndo": "Å<PERSON><PERSON>", "Common.Views.Header.tipUndock": "Docka ifrån till separata fönster", "Common.Views.Header.tipUsers": "Visa användare", "Common.Views.Header.tipViewSettings": "Visa inställningar", "Common.Views.Header.tipViewUsers": "Visa användare och hantera dokumentbehörigheter", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON>", "Common.Views.Header.txtRename": "Döp om", "Common.Views.History.textCloseHistory": "Stäng historik", "Common.Views.History.textHide": "Dra ihop", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON> ändring<PERSON>", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Expandera", "Common.Views.History.textShowAll": "Visa detaljerade ändringar", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Klistra in en bilds URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Detta fält är obligatoriskt", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON>ta fält bör vara en URL i formatet \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON><PERSON><PERSON> fil", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "Common.Views.ListSettingsDialog.textFromUrl": "Från URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "Ändra punktsymbol", "Common.Views.ListSettingsDialog.txtBullet": "Bullet", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Bild", "Common.Views.ListSettingsDialog.txtImport": "Importera", "Common.Views.ListSettingsDialog.txtNewBullet": "New bullet", "Common.Views.ListSettingsDialog.txtNewImage": "<PERSON><PERSON> bild", "Common.Views.ListSettingsDialog.txtNone": "ingen", "Common.Views.ListSettingsDialog.txtOfText": "% av text", "Common.Views.ListSettingsDialog.txtSize": "Storlek", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "Inställningar lista", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "Stäng fil", "Common.Views.OpenDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> cellområ<PERSON>", "Common.Views.OpenDialog.textSelectData": "Välj data", "Common.Views.OpenDialog.txtAdvanced": "Avancerad", "Common.Views.OpenDialog.txtColon": "Kolon", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Avgränsare", "Common.Views.OpenDialog.txtDestData": "Välj plats för data", "Common.Views.OpenDialog.txtEmpty": "Detta fält är obligatoriskt", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON><PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Skriv in lösenord för att öppna filen", "Common.Views.OpenDialog.txtOther": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "L<PERSON>senord", "Common.Views.OpenDialog.txtPreview": "Förhandsgranska", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON>r du har angett lösenordet och öppnat filen så återställs det aktuella lösenordet till filen.", "Common.Views.OpenDialog.txtSemicolon": "Semikolon", "Common.Views.OpenDialog.txtSpace": "Mellanslag", "Common.Views.OpenDialog.txtTab": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtTitle": "Välj %1 alternativ", "Common.Views.OpenDialog.txtTitleProtected": "Skyddad fil", "Common.Views.PasswordDialog.txtDescription": "Ange ett lösenord för att skydda detta dokument", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bekräftelse av lösenordet är inte identisk", "Common.Views.PasswordDialog.txtPassword": "L<PERSON>senord", "Common.Views.PasswordDialog.txtRepeat": "Repetera lösenord", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Varning! Om du glömmer lösenordet kan det inte återskapas.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textClosePanel": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Kryptera med lösenord", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> eller radera l<PERSON>", "Common.Views.Protection.hintSignature": "Lägg till digital signatur eller rad", "Common.Views.Protection.txtAddPwd": "Lägg till lösenord", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtEncrypt": "Kryptera", "Common.Views.Protection.txtInvisibleSignature": "Lägg till digital signatur", "Common.Views.Protection.txtSignature": "Signatur", "Common.Views.Protection.txtSignatureLine": "Lägg till signaturrad", "Common.Views.RenameDialog.textName": "Filnamn", "Common.Views.RenameDialog.txtInvalidName": "Filnamnet får inte innehålla något av följande tecken:", "Common.Views.ReviewChanges.hintNext": "Till nästa ä<PERSON>", "Common.Views.ReviewChanges.hintPrev": "Till föregående ändring", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>bb", "Common.Views.ReviewChanges.strFastDesc": "Realtidssamarbete. Alla ändringar sparas automatiskt.", "Common.Views.ReviewChanges.strStrict": "Strikt", "Common.Views.ReviewChanges.strStrictDesc": "Använd '<PERSON>ra'-knap<PERSON> för att synkronisera de förändringar du och andra gör.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Acceptera nuvarande ändring", "Common.Views.ReviewChanges.tipCoAuthMode": "Ställ in samredigeringsläge", "Common.Views.ReviewChanges.tipCommentRem": "Ta bort kommentarer", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Ta bort aktuella kommentarer", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON> kom<PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON><PERSON> aktuella kommentarer", "Common.Views.ReviewChanges.tipHistory": "Visa versionshistorik", "Common.Views.ReviewChanges.tipRejectCurrent": "Avvisa nuvarande ändring", "Common.Views.ReviewChanges.tipReview": "Spåra ändringar", "Common.Views.ReviewChanges.tipReviewView": " <PERSON><PERSON><PERSON><PERSON> det läge du vill att ändringarna ska visas", "Common.Views.ReviewChanges.tipSetDocLang": "Välj dokumentspråk", "Common.Views.ReviewChanges.tipSetSpelling": "Stavningskontroll", "Common.Views.ReviewChanges.tipSharing": "Hantera åtkomsträttighet för dokument", "Common.Views.ReviewChanges.txtAccept": "Acceptera", "Common.Views.ReviewChanges.txtAcceptAll": "Acceptera alla ändringar", "Common.Views.ReviewChanges.txtAcceptChanges": "Acceptera ändringar", "Common.Views.ReviewChanges.txtAcceptCurrent": "Acceptera nuvarande ändring", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Stäng", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "Ta bort alla kommentarer", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Ta bort aktuella kommentarer", "Common.Views.ReviewChanges.txtCommentRemMy": "Ta bort mina kommentarer", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Ta bort mina aktuella kommentarer", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON> bort", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON> alla kommentarer", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON><PERSON> aktuella kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON> mina kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "<PERSON><PERSON><PERSON> mina nuvarande kommentarer", "Common.Views.ReviewChanges.txtDocLang": "Språk", "Common.Views.ReviewChanges.txtFinal": "Alla ändringar accepterade (Förhandsvisning)", "Common.Views.ReviewChanges.txtFinalCap": "Slutlig", "Common.Views.ReviewChanges.txtHistory": "Versionshistorik", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> (Redigering)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Alla ändringar avvisade (Förhandsgranska)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Föregående", "Common.Views.ReviewChanges.txtReject": "Avvisa", "Common.Views.ReviewChanges.txtRejectAll": "Avvisa alla ändringar", "Common.Views.ReviewChanges.txtRejectChanges": "Avvisa ändringar", "Common.Views.ReviewChanges.txtRejectCurrent": "Avvisa nuvarande ändring", "Common.Views.ReviewChanges.txtSharing": "Delning", "Common.Views.ReviewChanges.txtSpelling": "Stavningskontroll", "Common.Views.ReviewChanges.txtTurnon": "Spåra ändringar", "Common.Views.ReviewChanges.txtView": "Visningsläge", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON> till", "Common.Views.ReviewPopover.textAddReply": "Lägg till svar", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Stäng", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+omnämnande ger åtkomst till dokumentet och skickar ett e-postmeddelande", "Common.Views.ReviewPopover.textMentionNotify": "+omnämning meddelar användaren via e-post", "Common.Views.ReviewPopover.textOpenAgain": "Öppna igen", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Du har inte behörighet att öppna kommentaren igen", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Rediger<PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Mapp att spara i", "Common.Views.SearchPanel.textCaseSensitive": "Skiftlägeskänslig", "Common.Views.SearchPanel.textCell": "Cell", "Common.Views.SearchPanel.textCloseSearch": "Stäng sökning", "Common.Views.SearchPanel.textContentChanged": "Do<PERSON><PERSON> ändrat.", "Common.Views.SearchPanel.textFind": "<PERSON>ö<PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON><PERSON> och er<PERSON>t", "Common.Views.SearchPanel.textFormula": "Formel", "Common.Views.SearchPanel.textFormulas": "<PERSON><PERSON>", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON>", "Common.Views.SearchPanel.textLookIn": "Sök i", "Common.Views.SearchPanel.textMatchUsingRegExp": "Matcha med regulj<PERSON><PERSON>", "Common.Views.SearchPanel.textName": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON> träffar", "Common.Views.SearchPanel.textNoSearchResults": "Inga sökresultat", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON><PERSON><PERSON><PERSON> alla", "Common.Views.SearchPanel.textReplaceWith": "<PERSON><PERSON>ätt med", "Common.Views.SearchPanel.textSearch": "<PERSON>ö<PERSON>", "Common.Views.SearchPanel.textSearchHasStopped": "Sökningen har stoppats", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON>", "Common.Views.SearchPanel.textTooManyResults": "Det finns för många resultat för att visa här", "Common.Views.SearchPanel.textValue": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textValues": "Värden", "Common.Views.SearchPanel.textWholeWords": "Endast hela ord", "Common.Views.SearchPanel.textWithin": "Inom", "Common.Views.SearchPanel.textWorkbook": "Arbetsbok", "Common.Views.SearchPanel.tipNextResult": "Nästa resultat", "Common.Views.SearchPanel.tipPreviousResult": "Föregående resultat", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textBold": "Fet", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Infoga undertecknares namn", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Underteck<PERSON>e får inte vara tom.", "Common.Views.SignDialog.textPurpose": "Syfte för att underteckna det här dokumentet", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> bild", "Common.Views.SignDialog.textSignature": "Signaturen ser ut som", "Common.Views.SignDialog.textTitle": "Underteckna dokument", "Common.Views.SignDialog.textUseImage": "eller klicka på 'Välj bild' för att använda en bild som signatur", "Common.Views.SignDialog.textValid": "Giltig från %1 till %2", "Common.Views.SignDialog.tipFontName": "Fontnamn", "Common.Views.SignDialog.tipFontSize": "Fontstorlek", "Common.Views.SignSettingsDialog.textAllowComment": "Tillåt undertecknare att lägga till kommentar i signaturdialogrutan", "Common.Views.SignSettingsDialog.textInfoEmail": "E-post", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Undertecknare titel", "Common.Views.SignSettingsDialog.textInstructions": "Instruktioner för <PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "Visa datum för signatur på signaturraden", "Common.Views.SignSettingsDialog.textTitle": "Skapa signatur", "Common.Views.SignSettingsDialog.txtEmpty": "Detta fält är obligatoriskt", "Common.Views.SymbolTableDialog.textCharacter": "Tecken", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX värde", "Common.Views.SymbolTableDialog.textCopyright": "Upphovsrättstecken", "Common.Views.SymbolTableDialog.textDCQuote": "Avslutande dubbelt citattecken", "Common.Views.SymbolTableDialog.textDOQuote": "Opening Double Quote", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON><PERSON> ellips", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Em Space", "Common.Views.SymbolTableDialog.textEnDash": "En Dash", "Common.Views.SymbolTableDialog.textEnSpace": "En Space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON>-brytande bindestreck", "Common.Views.SymbolTableDialog.textNBSpace": "No-break Space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow tecken", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em Space", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON>r<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Tid<PERSON>re använda symboler", "Common.Views.SymbolTableDialog.textRegistered": "Registrerat tecken", "Common.Views.SymbolTableDialog.textSCQuote": "Avslutande enkelt citattecken", "Common.Views.SymbolTableDialog.textSection": "Avsnittstecken", "Common.Views.SymbolTableDialog.textShortcut": "Snabbtangent", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Opening Single Quote", "Common.Views.SymbolTableDialog.textSpecial": "Specialtecken", "Common.Views.SymbolTableDialog.textSymbols": "Symboler", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Varumärkesymbol", "Common.Views.UserNameDialog.textDontShow": "Fråga inte igen", "Common.Views.UserNameDialog.textLabel": "Etikett:", "Common.Views.UserNameDialog.textLabelError": "<PERSON><PERSON><PERSON><PERSON> får inte vara tom.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "Du måste ange URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Text till kolumner", "SSE.Controllers.DataTab.txtDataValidation": "Datavalidering", "SSE.Controllers.DataTab.txtExpand": "Expandera", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Data bredvid markeringen tas inte bort. Vill du utöka urvalet till att inkludera intilliggande data eller bara fortsätta med de markerade cellerna?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Markeringen innehåller några celler utan datavalideringsinställningar. <br> Vill du utvidga datavalidering till des<PERSON> celler?", "SSE.Controllers.DataTab.txtImportWizard": "Textimport-guide", "SSE.Controllers.DataTab.txtRemDuplicates": "<PERSON> bort dubbletter", "SSE.Controllers.DataTab.txtRemoveDataValidation": "<PERSON>t innehåller mer än en typ av validering. <br> <PERSON><PERSON><PERSON> nuvarande inställningar och fortsätt?", "SSE.Controllers.DataTab.txtRemSelected": "Ta bort i vald", "SSE.Controllers.DataTab.txtUrlTitle": "Klistra in en data-URL", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Centrera", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON> kol<PERSON>n", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> rad", "SSE.Controllers.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Länkreferensen finns inte. Korrigera länken eller ta bort den.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Kolumn vänster", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Rad under", "SSE.Controllers.DocumentHolder.insertText": "Infoga", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Varning", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Inställningar autokorrigering", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Kolumnbredd {0} symboler ({1} pixlar)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Radhöjd {0} punkter ({1} pixlar)", "SSE.Controllers.DocumentHolder.textCtrlClick": "<PERSON>licka på länken för att öppna den eller klicka och håll ner musknappen för att välja cellen.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Infoga vänster", "SSE.Controllers.DocumentHolder.textInsertTop": "Infoga topp", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON><PERSON> in special", "SSE.Controllers.DocumentHolder.textStopExpand": "Stanna automatiskt", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Detta element redigeras av en annan användare.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON> medel", "SSE.Controllers.DocumentHolder.txtAddBottom": "Lägg till bottenlinje", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Lägg till fraktionbar", "SSE.Controllers.DocumentHolder.txtAddHor": "Lägg till horisontell linje", "SSE.Controllers.DocumentHolder.txtAddLB": "Lägg till bottenlinje", "SSE.Controllers.DocumentHolder.txtAddLeft": "Lägg till vänster ram", "SSE.Controllers.DocumentHolder.txtAddLT": "Lägg till vänster övre linje", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON> till höger ram", "SSE.Controllers.DocumentHolder.txtAddTop": "Lägg till övre ram", "SSE.Controllers.DocumentHolder.txtAddVer": "Lägg till horisontell linje", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON> te<PERSON>n", "SSE.Controllers.DocumentHolder.txtAll": "(Allt)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returnerar hela tabellens innehåll eller de angivna tabellkolumnerna inklusive kolumnrubriker, data och det totala antalet rader", "SSE.Controllers.DocumentHolder.txtAnd": "och", "SSE.Controllers.DocumentHolder.txtBegins": "<PERSON><PERSON><PERSON><PERSON> med", "SSE.Controllers.DocumentHolder.txtBelowAve": "Under medel", "SSE.Controllers.DocumentHolder.txtBlanks": "(Blanksteg)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Ramegenskaper", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Kolumnjustering", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Länken har kopierats till urklipp", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returnerar datacellerna i tabellen eller specificerade tabellkolumnerna", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Minska <PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> manuell brytning", "SSE.Controllers.DocumentHolder.txtDeleteChars": "<PERSON><PERSON>a o<PERSON><PERSON>ande tecken", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Radera omslutande tecken och separatorer", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON>a tecken", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> radikal", "SSE.Controllers.DocumentHolder.txtEnds": "Slutar med", "SSE.Controllers.DocumentHolder.txtEquals": "Lika med", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Lika med cellfärg", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Lika med teckensnittsfärg", "SSE.Controllers.DocumentHolder.txtExpand": "Expandera och sortera", "SSE.Controllers.DocumentHolder.txtExpandSort": "Data bredvid markeringen kommer inte att sorteras. Vill du utöka markeringen så att den inkluderar intilliggande data eller fortsätta att sortera de markerade cellerna?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Ö<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Byt till linjärt bråk", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Byt till skev fraktion", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Byt till staplad fraktion", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON><PERSON> än eller lika med", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Cha<PERSON> ö<PERSON> text", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char under text", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returnerar kolumnrubrikerna i tabellen eller specificerade tabellkolumner", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON> nedre ramen", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "G<PERSON><PERSON> stängningsparantes", "SSE.Controllers.DocumentHolder.txtHideDegree": "Göm grad", "SSE.Controllers.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>je", "SSE.Controllers.DocumentHolder.txtHideLB": "Dölj vänstra bottenlinjen", "SSE.Controllers.DocumentHolder.txtHideLeft": "Dölj vänster ram", "SSE.Controllers.DocumentHolder.txtHideLT": "Dölj vänstra topplinjen", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON> ram", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON> toppramen", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON> vertikal linje", "SSE.Controllers.DocumentHolder.txtImportWizard": "Textimport-guide", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Öka argumentets storlek", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Infoga argument efter", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Infoga argument före", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Infoga manuell brytning", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Infoga ekvation efter", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Infoga ekvation före", "SSE.Controllers.DocumentHolder.txtItems": "objekt", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON><PERSON> endast text", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON> än", "SSE.Controllers.DocumentHolder.txtLessEquals": "<PERSON><PERSON> än eller lika med", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON><PERSON> gr<PERSON> plats", "SSE.Controllers.DocumentHolder.txtLimitOver": "Begränsa över text", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Begr<PERSON><PERSON><PERSON> under text", "SSE.Controllers.DocumentHolder.txtLockSort": "Data finns bredvid ditt val men du har inte tillräckliga behörigheter för att ändra dessa celler.<br>Vill du fortsätta med nuvarande val?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON> parenteser till argumentets höjd", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Matrisjustering", "SSE.Controllers.DocumentHolder.txtNoChoices": "Det finns inga val för att fylla cellen. <br> Endast textvärden från kolumnen kan väljas för ers<PERSON>.", "SSE.Controllers.DocumentHolder.txtNotBegins": "B<PERSON><PERSON>jar inte med", "SSE.Controllers.DocumentHolder.txtNotContains": "Innehåller inte", "SSE.Controllers.DocumentHolder.txtNotEnds": "Slutar inte med", "SSE.Controllers.DocumentHolder.txtNotEquals": "Är inte lika med", "SSE.Controllers.DocumentHolder.txtOr": "eller", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON> text", "SSE.Controllers.DocumentHolder.txtPaste": "Klistra in", "SSE.Controllers.DocumentHolder.txtPasteBorders": "<PERSON>el utan ramar", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formel + kolumnbredd", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Destinationsformatering", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Klistra in endast formatering", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formel + numeriskt format", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Klistra in endast formel", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formel + all formatering", "SSE.Controllers.DocumentHolder.txtPasteLink": "Klistra in länk", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Länkad bild", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Sammanfoga villkorlig formatering", "SSE.Controllers.DocumentHolder.txtPastePicture": "Bild", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Källformatering", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transponera", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Värde + all formatering", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Värde + nummerformat", "SSE.Controllers.DocumentHolder.txtPasteValues": "Klistra in endast värde", "SSE.Controllers.DocumentHolder.txtPercent": "procent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Tabell autoutökning klar", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Ta bort fraktionslinje", "SSE.Controllers.DocumentHolder.txtRemLimit": "Ta bort begr<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Ta bort tecken med accenter", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Ta bort linje", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Vill du ta bort den här signaturen?<br>Åtgärden kan inte ångras.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Ta bort script", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Ta bort nedsä<PERSON>t", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "<PERSON> bort upphöjt", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "<PERSON><PERSON><PERSON><PERSON> efter text", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Skript före text", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Visa nedre gräns", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Visa avslutande parentes", "SSE.Controllers.DocumentHolder.txtShowDegree": "Visa grad", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Visa vänsterparentes", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Visa platshållare", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Visa övre gräns", "SSE.Controllers.DocumentHolder.txtSorting": "So<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtSortSelected": "Sortering vald", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "SSE.Controllers.DocumentHolder.txtThisRowHint": "<PERSON><PERSON><PERSON><PERSON> endast denna raden i den angiven kolumnen", "SSE.Controllers.DocumentHolder.txtTop": "Ö<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "<PERSON><PERSON><PERSON> det totala antalet rader för tabellen eller för de angivna kolumnerna", "SSE.Controllers.DocumentHolder.txtUnderbar": "Linje under text", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Ångra automatisk tabellexpansion", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Använd guiden för textimport", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Att klicka på denna länk kan skada din utrustning och dess innehåll.<br>Är du säker på att du vill fortsätta?", "SSE.Controllers.DocumentHolder.txtWidth": "Bredd", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON>a", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Databas", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Datum och tid", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Teknik", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finansiell", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Information", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 sist använda", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logisk", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Sökning och referens", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematik och trigonometri", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistisk", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text och data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Namnlöst kalkylark", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON> kolumner", "SSE.Controllers.LeftMenu.textByRows": "Av rader", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON> inn<PERSON> i cellen", "SSE.Controllers.LeftMenu.textLoadHistory": "Laddar versionshistorik...", "SSE.Controllers.LeftMenu.textLookin": "Sök i", "SSE.Controllers.LeftMenu.textNoTextFound": "De data som du har letat efter kunde inte hittas. Ändra dina sökalternativ.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Ersättningen har gjorts. {0} händelser hoppades över.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Sökningen har gjorts. Förekomster som ersatts: {0}", "SSE.Controllers.LeftMenu.textSearch": "<PERSON>ö<PERSON>", "SSE.Controllers.LeftMenu.textSheet": "Flik", "SSE.Controllers.LeftMenu.textValues": "Värden", "SSE.Controllers.LeftMenu.textWarning": "Varning", "SSE.Controllers.LeftMenu.textWithin": "Inom", "SSE.Controllers.LeftMenu.textWorkbook": "Arbetsbok", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.warnDownloadAs": "Om du fortsätter att spara i det här formatet kommer alla funktioner utom texten att gå förlorade.<br>Är du säker på att du vill fortsätta?", "SSE.Controllers.Main.confirmMoveCellRange": "Destinationsområdet kan innehålla data. Fortsätta operationen?", "SSE.Controllers.Main.confirmPutMergeRange": "Källinformationen innehöll sammanslagna celler. <br> De hade inte slagits samman innan de klistrats in i tabellen.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formler i rubrikraden tas bort och konverteras till statisk text. <br> Vill du fortsätta?", "SSE.Controllers.Main.convertationTimeoutText": "Omvandlingstiden har överskridits.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> på \"OK\" för att komma tillbaka till dokumentlistan.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.downloadTextText": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>d...", "SSE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON>tar kalk<PERSON>blad", "SSE.Controllers.Main.errNoDuplicates": "Inga dubletter funna.", "SSE.Controllers.Main.errorAccessDeny": "Du försöker utföra en åtgärd som du inte har rättighet till.<br>Vänligen kontakta din systemadministratör.", "SSE.Controllers.Main.errorArgsRange": "<PERSON>tt fel i den angivna formeln.<br><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorAutoFilterChange": "Operationen inte tillåten", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Åtgärden kunde inte utföras för de valda cellerna eftersom du inte kan flytta en del av tabellen. <br> <PERSON><PERSON><PERSON>j ett annat dataområde så att hela tabellen flyttas och försök igen.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Åtgärden kunde inte göras för det valda cellområdet. <br> <PERSON><PERSON><PERSON><PERSON> ett enhetligt dataområde som skiljer sig från det befintliga och försök igen.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Åtgärden kan inte utföras eftersom området innehåller filtrerade celler. <br> Ta bort de filtrerade elementen och försök igen.", "SSE.Controllers.Main.errorBadImageUrl": "Bildens URL är felaktig", "SSE.Controllers.Main.errorCannotUngroup": "Det går inte att gruppera. <PERSON><PERSON><PERSON> att starta en kontur, välj detaljrader eller kolumner och gruppera dem.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Du kan inte använda detta kommando i ett skyddat kalkylblad. För att använda detta kommando så måste först kalkylbladet låsas upp.<br>Du kan behöva ange ett lösenord för detta.", "SSE.Controllers.Main.errorChangeArray": "Du kan inte ändra en del av en matris.", "SSE.Controllers.Main.errorChangeFilteredRange": "Detta ändrar ett filtrerat intervall i kalkylbladet. <br> Ta bort automatiska filter för att slutföra uppgiften.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Cellen eller tabellen du försöker ändra tillhör ett skyddat kalkylblad.<br><PERSON><PERSON><PERSON> att göra en ändring ta då bort kalkylbladets skydd. Du kan komma att behöva ange ett lösenord.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Serveranslutning förlorade. Dokumentet kan inte redigeras just nu.", "SSE.Controllers.Main.errorConnectToServer": "Dokumentet kunde inte sparas. . Kontrollera anslutningsinställningarna eller kontakta administratören <br> <PERSON><PERSON><PERSON> du klickar på \"OK\" -knap<PERSON>, kommer du att bli ombedd att ladda ner dokumentet.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Det här kommandot kan inte användas med flera val. <br> <PERSON><PERSON><PERSON><PERSON> ett enda område och försök igen.", "SSE.Controllers.Main.errorCountArg": "Ett fel i den angivna formeln.<br><PERSON><PERSON><PERSON><PERSON> antal argument.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON>tt fel i den angivna formeln.<br><PERSON><PERSON><PERSON> argument.", "SSE.Controllers.Main.errorCreateDefName": "De befintliga namngivna områdena kan inte redigeras och de nya kan inte skapas <br> just nu eftersom några av dem redigeras.", "SSE.Controllers.Main.errorDatabaseConnection": "Externt fel.<br>Databasanslutningsfel. Kontakta support om felet kvarstår.", "SSE.Controllers.Main.errorDataEncrypted": "Krypterade ändringar har tagits emot, de kan inte avkodas.", "SSE.Controllers.Main.errorDataRange": "<PERSON><PERSON><PERSON><PERSON>få<PERSON>", "SSE.Controllers.Main.errorDataValidate": "Värdet du angav är inte giltigt. <br> En användare har begränsade värden som kan matas in i den här cellen.", "SSE.Controllers.Main.errorDefaultMessage": "Felkod: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Du försöker radera en kolumn som innehåller en låst cell. Låsta celler kan inte raderas när arbetsboken är skyddad.<br><PERSON><PERSON>r att radera en låst cell så måste först kalkylbladet låsas upp. Du kan behöva ange ett lösenord för detta.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Du försöker radera en rad som innehåller en låst cell. Låsta celler kan inte raderas när arbetsboken är skyddad.<br><PERSON><PERSON>r att radera en låst cell så måste först kalkylbladet låsas upp. Du kan behöva ange ett lösenord för detta.", "SSE.Controllers.Main.errorEditingDownloadas": "Ett fel har inträffat.<br><PERSON><PERSON><PERSON><PERSON> \"Ladda ned som\" för att spara en säkerhetskopia på din dator.", "SSE.Controllers.Main.errorEditingSaveas": "Ett fel har inträffat.<br><PERSON><PERSON><PERSON><PERSON> \"Ladda ned som...\" för att spara en säkerhetskopia på din dator.", "SSE.Controllers.Main.errorEditView": "Den befintliga vyn kan inte redigeras och den nya kan inte skapas just nu eftersom vissa av dem redigeras.", "SSE.Controllers.Main.errorEmailClient": "Ingen e-postklient kunde hittas.", "SSE.Controllers.Main.errorFilePassProtect": "Dokumentet är lösenordsskyddat och kunde inte öppnas. ", "SSE.Controllers.Main.errorFileRequest": "Externt fel.<br>Filförfråganfel. Kontakta support om felet kvarstår.", "SSE.Controllers.Main.errorFileSizeExceed": "Filstorleken överskrider gränsen för din server.<br>Var snäll och kontakta administratören för dokumentservern för mer information.", "SSE.Controllers.Main.errorFileVKey": "Externt fel.<br>Felaktig säkerhetsnyckel. Kontakta support om felet kvarstår.", "SSE.Controllers.Main.errorFillRange": "Kunde inte fylla i det valda cellområdet.<br><PERSON>a sammanslagna celler måste vara lika stora.", "SSE.Controllers.Main.errorForceSave": "Ett fel uppstod när filen sparades. Använd alternativet \"Spara som\" för att spara filen till din lokala hårddisk eller försök igen senare.", "SSE.Controllers.Main.errorFormulaName": "Ett fel i den angivna formeln.<br><PERSON><PERSON><PERSON><PERSON> formelnamn.", "SSE.Controllers.Main.errorFormulaParsing": "Internt fel vid analys av formeln.", "SSE.Controllers.Main.errorFrmlMaxLength": "Formelns längd överskrider gränsen på 8192 tecken. <br> Redigera den och försök igen.", "SSE.Controllers.Main.errorFrmlMaxReference": "Du kan inte ange den här formeln eftersom den har för många värden, <br> cellreferenser och / eller namn.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Textvärden i formler är begränsade till 255 tecken. <br> Använd CONCATENATE-funktionen eller samkopplingsoperatören (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funktionen hänvisar till en flik som inte finns. <br> Kontrollera data och försök igen.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Åtgärden kunde inte slutföras för det valda cellområdet. <br><PERSON><PERSON><PERSON><PERSON> ett intervall så att den första tabellraden var i samma rad<br>och den resulterande tabellen överlappade den aktuella.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Åtgärden kunde inte slutföras för det valda cellområdet.<br><PERSON><PERSON><PERSON><PERSON> ett intervall som inte innehåller andra tabeller.", "SSE.Controllers.Main.errorInvalidRef": "<PERSON>e ett korrekt namn för valet eller en giltig referens att gå till.", "SSE.Controllers.Main.errorKeyEncrypt": "Okänd nyckelbeskrivare", "SSE.Controllers.Main.errorKeyExpire": "Nyckelns beskrivning har utgått", "SSE.Controllers.Main.errorLabledColumnsPivot": "<PERSON><PERSON><PERSON> att skapa en pivottabell använder du data som är organiserade som en lista med märkta kolumner.", "SSE.Controllers.Main.errorLoadingFont": "Typsnittet är inte tillgängligt.<br>Vänligen kontakta dokumentserverns administratör.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Referensen för platsen eller dataområdet är inte giltig.", "SSE.Controllers.Main.errorLockedAll": "Åtgärden kunde inte utföras eftersom arket har låsts av en annan användare.", "SSE.Controllers.Main.errorLockedCellPivot": "Kan inte ändra data i en pivottabell", "SSE.Controllers.Main.errorLockedWorksheetRename": "Fliken kan inte bytas namn på just nu eftersom en annan användare redigerar nu", "SSE.Controllers.Main.errorMaxPoints": "Max antal punkter i serie är 4096.", "SSE.Controllers.Main.errorMoveRange": "Kan inte ändra del av en sammanslagen cell", "SSE.Controllers.Main.errorMoveSlicerError": "Tabellskivor kan inte kopieras från en arbetsbok till en annan. <br> Försök igen genom att välja hela tabellen och skivorna.", "SSE.Controllers.Main.errorMultiCellFormula": "Multi-cell array är inte tillåtna i tabeller", "SSE.Controllers.Main.errorNoDataToParse": "Ingen data vald att bearbeta", "SSE.Controllers.Main.errorOpenWarning": "Längden på en av formlerna i filen överskred gränsen på 8192 tecken.<br>Formeln togs bort.", "SSE.Controllers.Main.errorOperandExpected": "Den angivna funktionssyntaxen är inte korrekt. Kontrollera om du saknar en av parenteserna - '(' eller ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Lösenordet som du angav är felaktigt.<br>Se till att CAPS LOCK inte är aktiverat och säkerställ att du använder rätt storlek på tecknen i lösenordet.", "SSE.Controllers.Main.errorPasteMaxRange": "Kopiera- och klistraområdet matchar inte. <br> <PERSON><PERSON><PERSON><PERSON> ett område med samma storlek eller klicka på den första cellen i rad för att klistra in de kopierade cellerna.", "SSE.Controllers.Main.errorPasteMultiSelect": "Den här åtgärden kan inte göras i ett val av flera intervall. <br> V<PERSON>lj ett enda område och försök igen.", "SSE.Controllers.Main.errorPasteSlicerError": "Tabellskivor kan inte kopieras från en arbetsbok till en annan.", "SSE.Controllers.Main.errorPivotGroup": "Det går inte att gruppera det valet.", "SSE.Controllers.Main.errorPivotOverlap": "En pivottabellrapport kan inte ö<PERSON>lappa en tabell.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Pivottabellrapporten sparades utan underliggande data. <br> <PERSON><PERSON><PERSON><PERSON> knappen 'Uppdatera' för att uppdatera rapporten.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON><PERSON><PERSON><PERSON> är det inte möjligt att skriva ut mer än 1500 sidor samtidigt i den aktuella programversionen. <br> <PERSON><PERSON> begrä<PERSON>ning kommer att tas bort i de kommande utgåvorna.", "SSE.Controllers.Main.errorProcessSaveResult": "Fel vid spara", "SSE.Controllers.Main.errorServerVersion": "Textredigerarens version har uppdaterats. <PERSON><PERSON> kommer att laddas om för att verkställa ändringarna.", "SSE.Controllers.Main.errorSessionAbsolute": "Dokumentet redigeringssession har löpt ut. Vänligen ladda om sidan.", "SSE.Controllers.Main.errorSessionIdle": "Dokumentet har inte redigerats under en ganska lång tid. Var vänlig att ladda om sidan.", "SSE.Controllers.Main.errorSessionToken": "Anslutningen till servern har avbrutits. Vänligen ladda om sidan.", "SSE.Controllers.Main.errorSetPassword": "Lösenord kunde inte ställas in.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Platsreferens är inte giltig eftersom cellerna inte alla finns i samma kolumn eller rad. <br> <PERSON><PERSON><PERSON><PERSON> celler som alla finns i en enda kolumn eller rad.", "SSE.Controllers.Main.errorStockChart": "Felaktig ordningsföljd. F<PERSON>r att bygga ett aktiediagram placerar du uppgifterna på arket i följande ordning:<br> öppning<PERSON>ris, maxpris, minipris, slutkurs.", "SSE.Controllers.Main.errorToken": "Dokumentets säkerhetstoken är inte korrekt. <br>Vänligen kontakta din dokumentserver administratör.", "SSE.Controllers.Main.errorTokenExpire": "Dokumentets säkerhetstoken har upphört att gälla.<br>Var vänlig och Kontakta din dokumentserver administratör.", "SSE.Controllers.Main.errorUnexpectedGuid": "Externt fel.<br>Oväntad GUID. Kontakta support om felet kvarstår.", "SSE.Controllers.Main.errorUpdateVersion": "Filens version har ändrats. Sidan kommer laddas om.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internetanslutningen har återställts och filversionen har ändrats.<br><PERSON><PERSON> du fortsätter arbeta, ladda ner filen eller kopiera innehållet för att försäkra dig om att inte förlora något, ladda sedan om denna sida.", "SSE.Controllers.Main.errorUserDrop": "<PERSON>n kan inte nås för till<PERSON>. ", "SSE.Controllers.Main.errorUsersExceed": "Antalet användare som tillåts av licensen överskreds", "SSE.Controllers.Main.errorViewerDisconnect": "Anslutningen avbröts. Du kan fortfarande se dokumentet,<br>men du kommer inte att kunna ladda ner eller skriva ut det innan anslutningen är återställd och sidan har laddats om.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON> i den angivna formeln.<br><PERSON><PERSON><PERSON><PERSON> antal parenteser.", "SSE.Controllers.Main.errorWrongOperator": "Ett fel i den angivna formeln. Felaktig operator används.<br>Var god korrigera felet.", "SSE.Controllers.Main.errorWrongPassword": "Lösenordet som angavs är felaktigt.", "SSE.Controllers.Main.errRemDuplicates": "Duplicerade värden hittades och raderades: {0}, unika värden kvar: {1}.", "SSE.Controllers.Main.leavePageText": "Du har osparade ändringar i det här kalkylarket. <PERSON>licka på \"Stanna på den här sidan\" och sedan på \"Spara\" för att spara dem. <PERSON>licka på \"Lämna den här sidan\" om du vill ta bort alla ändringar som inte har sparats.", "SSE.Controllers.Main.leavePageTextOnClose": "Alla ändringar som inte har sparats i detta kalkylblad går förlorade. <br> <PERSON><PERSON><PERSON> på \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" och sedan på \"Spara\" för att spara dem. <PERSON>lick<PERSON> på \"OK\" för att kassera alla ändringar som inte sparats.", "SSE.Controllers.Main.loadFontsTextText": "Laddar data...", "SSE.Controllers.Main.loadFontsTitleText": "Laddar data", "SSE.Controllers.Main.loadFontTextText": "Laddar data...", "SSE.Controllers.Main.loadFontTitleText": "Laddar data", "SSE.Controllers.Main.loadImagesTextText": "Laddar bilder...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON>dd<PERSON> bilder", "SSE.Controllers.Main.loadImageTextText": "Laddar bild...", "SSE.Controllers.Main.loadImageTitleText": "Laddar bild", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON>tar kalk<PERSON>blad", "SSE.Controllers.Main.notcriticalErrorTitle": "Varning", "SSE.Controllers.Main.openErrorText": "Ett fel uppstod vid öppnandet av filen.", "SSE.Controllers.Main.openTextText": "Öppnar kalkylblad...", "SSE.Controllers.Main.openTitleText": "Öppnar kalkylblad", "SSE.Controllers.Main.pastInMergeAreaError": "Kan inte ändra del av en sammanslagen cell", "SSE.Controllers.Main.printTextText": "Skriver ut kalkylblad...", "SSE.Controllers.Main.printTitleText": "Skriver ut kalkylblad", "SSE.Controllers.Main.reloadButtonText": "Ladda om sidan", "SSE.Controllers.Main.requestEditFailedMessageText": "Någon redigerar det här dokumentet just nu. Vänligen försök igen senare.", "SSE.Controllers.Main.requestEditFailedTitleText": "Ingen behörighet", "SSE.Controllers.Main.saveErrorText": "<PERSON>tt fel uppstod när filen sparades.", "SSE.Controllers.Main.saveErrorTextDesktop": "Den här filen kan inte sparas eller skapas. <br> Möjliga orsaker är: <br> 1. Filen är skrivskyddad. <br> 2. Filen red<PERSON> av andra använda<PERSON>. <br> 3. Disken är full eller skadad.", "SSE.Controllers.Main.saveTextText": "Sparar arbetsbok...", "SSE.Controllers.Main.saveTitleText": "Sparar arbetsbok", "SSE.Controllers.Main.scriptLoadError": "Anslutningen är för <PERSON>, vissa av komponenterna kunde inte laddas. Vänligen ladda om sidan.", "SSE.Controllers.Main.textAnonymous": "Anonym", "SSE.Controllers.Main.textApplyAll": "<PERSON><PERSON><PERSON> alla ek<PERSON>er", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON><PERSON> webbp<PERSON>s", "SSE.Controllers.Main.textChangesSaved": "Alla änd<PERSON>ar har sparats", "SSE.Controllers.Main.textClose": "Stäng", "SSE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON> för att stänga tipset", "SSE.Controllers.Main.textConfirm": "Bekräftelse", "SSE.Controllers.Main.textContactUs": "Kontakta <PERSON>jare", "SSE.Controllers.Main.textConvertEquation": "Denna ekvation skapades med en gammal version av ekvationsredigeraren som inte längre stöds. Om du vill redigera den konverterar du ekvationen till Office Math ML-format.<br>Konvertera nu?", "SSE.Controllers.Main.textCustomLoader": "Observera att enligt licensvillkoren har du inte rätt att byta laddaren. <br> Kontakta vår försäljningsavdelning för att få en offert.", "SSE.Controllers.Main.textDisconnect": "Anslutningen förlorades", "SSE.Controllers.Main.textFillOtherRows": "<PERSON><PERSON><PERSON> and<PERSON> rader", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formel ifylld {0} rader har data. Att fylla de andra tomma raderna kan ta några minuter.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formel ifylld i de första {0} raderna. Att fylla de andra tomma raderna kan ta några minuter.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formel är bara ifylld i de första {0} raderna av minnesbesparings skäl. Det finns {1} andra rader med data i detta kalkylblad. Du kan fylla i dem manuellt.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formel är bara ifylld i de {0} rader av minnesutrymmes skäl. De andra raderna i detta kalkylblad har inga data.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "<PERSON>n innehåller automatiska makron. <br> Vill du köra makron?", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON> dig mer", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON>tar kalk<PERSON>blad", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON> na<PERSON>n (max 128 tecken).", "SSE.Controllers.Main.textNeedSynchronize": "Du har upp<PERSON><PERSON>ar", "SSE.Controllers.Main.textNo": "<PERSON><PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "Licensens gräns är nådd", "SSE.Controllers.Main.textPaidFeature": "Betald funktion", "SSE.Controllers.Main.textPleaseWait": "Åtgärden kan ta mer tid än väntat. Vänta...", "SSE.Controllers.Main.textReconnect": "Anslutningen återställdes", "SSE.Controllers.Main.textRemember": "<PERSON><PERSON> ih<PERSON>g mitt val för alla filer", "SSE.Controllers.Main.textRememberMacros": "<PERSON><PERSON> ih<PERSON>g mitt val för alla makron", "SSE.Controllers.Main.textRenameError": "Användarnamn får inte var tomt.", "SSE.Controllers.Main.textRenameLabel": "<PERSON><PERSON> namn för <PERSON>e", "SSE.Controllers.Main.textShape": "Form", "SSE.Controllers.Main.textStrict": "Strikt läge", "SSE.Controllers.Main.textText": "Text", "SSE.Controllers.Main.textTryUndoRedo": "Ångra/Återställ-funktionerna är inaktiva i snabbt samredigeringsläget.<br><PERSON><PERSON><PERSON> på knappen 'Strikt läge' för att växla till strikt samredigeringsläge och redigera filen utan andra användares påverkan och skicka dina ändringar först efter att du har sparat dem. Du kan växla mellan samredigeringslägena med hjälp av avancerade inställningar.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Ångra-funktionerna är inaktiverade för snabb samredigeringsläge.", "SSE.Controllers.Main.textYes": "<PERSON>a", "SSE.Controllers.Main.titleLicenseExp": "Licensen har gått ut", "SSE.Controllers.Main.titleServerVersion": "Editor uppdaterad", "SSE.Controllers.Main.txtAccent": "Accent", "SSE.Controllers.Main.txtAll": "(Allt)", "SSE.Controllers.Main.txtArt": "Din text här", "SSE.Controllers.Main.txtBasicShapes": "Grundläggande former", "SSE.Controllers.Main.txtBlank": "(tom)", "SSE.Controllers.Main.txtButtons": "Knappar", "SSE.Controllers.Main.txtByField": "%1 av %2", "SSE.Controllers.Main.txtCallouts": "Länktext", "SSE.Controllers.Main.txtCharts": "Diagram", "SSE.Controllers.Main.txtClearFilter": "Rensa filter", "SSE.Controllers.Main.txtColLbls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtConfidential": "Konfidentiell", "SSE.Controllers.Main.txtDate": "Datum", "SSE.Controllers.Main.txtDays": "Dagar", "SSE.Controllers.Main.txtDiagramTitle": "Diagramtitel", "SSE.Controllers.Main.txtEditingMode": "St<PERSON>ll in redigeringsläge...", "SSE.Controllers.Main.txtErrorLoadHistory": "Inläsning av historik misslyckades", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "Fil", "SSE.Controllers.Main.txtGrandTotal": "Totalsumma", "SSE.Controllers.Main.txtGroup": "Grupp", "SSE.Controllers.Main.txtHours": "timmar", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Multi-Select", "SSE.Controllers.Main.txtOr": "%1 eller %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Sida %1 av %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "Förberedd av", "SSE.Controllers.Main.txtPrintArea": "Utskriftsområde", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "Rektanglar", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "Serier", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Line Callout 1 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Line Callout 2 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Line Callout 3 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout1": "Line Callout 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "Line Callout 2 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout3": "Line Callout 3 (Accent Bar)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tillbaka eller föregående knapp", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Startknapp", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Tömknapp", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Dokumentknapp", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Slutknapp", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON><PERSON> eller nästa knapp", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Informationsknapp", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Film knapp", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Återknapp", "SSE.Controllers.Main.txtShape_actionButtonSound": "Ljudknapp", "SSE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>ge", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON> pil", "SSE.Controllers.Main.txtShape_bentConnector5": "Armbågskontakt", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Armbågspilkontakt", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Armbågsdubbelpil", "SSE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "<PERSON> båge", "SSE.Controllers.Main.txtShape_borderCallout1": "Pratbubbla linje 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Line Callout 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Line Callout 3", "SSE.Controllers.Main.txtShape_bracePair": "Dubbelklammer", "SSE.Controllers.Main.txtShape_callout1": "Line Callout 1 (No Border)", "SSE.Controllers.Main.txtShape_callout2": "Line Callout 2 (No Border)", "SSE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No Border)", "SSE.Controllers.Main.txtShape_can": "Bur<PERSON>", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Chord", "SSE.Controllers.Main.txtShape_circularArrow": "C<PERSON><PERSON>är pil", "SSE.Controllers.Main.txtShape_cloud": "Mo<PERSON>", "SSE.Controllers.Main.txtShape_cloudCallout": "Pratbubbla moln", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "B<PERSON>jd p<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Böjd vänsterpil", "SSE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_decagon": "Decagon", "SSE.Controllers.Main.txtShape_diagStripe": "Diagon<PERSON> rand", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "SSE.Controllers.Main.txtShape_donut": "Donut", "SSE.Controllers.Main.txtShape_doubleWave": "Double Wave", "SSE.Controllers.Main.txtShape_downArrow": "Nedåtpil", "SSE.Controllers.Main.txtShape_downArrowCallout": "Pratbubbla nedåtpil", "SSE.Controllers.Main.txtShape_ellipse": "Ellips", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Curved Down Ribbon", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Curved Up Ribbon", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Födesschema: Alternativ process", "SSE.Controllers.Main.txtShape_flowChartCollate": "Flödesschema: Collate", "SSE.Controllers.Main.txtShape_flowChartConnector": "Flödesschema: Anslutning", "SSE.Controllers.Main.txtShape_flowChartDecision": "Flödesschema: Decision", "SSE.Controllers.Main.txtShape_flowChartDelay": "Flödesschema: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Flödesschema: Display", "SSE.Controllers.Main.txtShape_flowChartDocument": "Flödesschema: Dokument", "SSE.Controllers.Main.txtShape_flowChartExtract": "Flödesschema: Uttag", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Flödesschema:Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Flödesschema: Intern lagring", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flödesschema: Magnetisk disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flödesschema: Direktansluten lagring", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Flödesschema: <PERSON><PERSON><PERSON><PERSON><PERSON> lagring", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Flödesschema: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Flödesschema: Manuell operation", "SSE.Controllers.Main.txtShape_flowChartMerge": "Flödesschema: <PERSON><PERSON><PERSON> samman", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Flödesschema: Multipla dokument", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flödesschema: <PERSON><PERSON><PERSON><PERSON> utanför sidan", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flödesschema: Lagrad data", "SSE.Controllers.Main.txtShape_flowChartOr": "Flödesschema: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flödesschema: Fördefinierad process", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Flödesschema: Förberedelse", "SSE.Controllers.Main.txtShape_flowChartProcess": "Flödesschema: Process", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Flödesschema: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Flödesschema: <PERSON><PERSON><PERSON> tejp", "SSE.Controllers.Main.txtShape_flowChartSort": "Flödesschema: Soretera", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Flödesschema: Summering", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Flödesschema: Avslutning", "SSE.Controllers.Main.txtShape_foldedCorner": "Skyddat hörn", "SSE.Controllers.Main.txtShape_frame": "Ram", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON>v ram", "SSE.Controllers.Main.txtShape_heart": "Hjärta", "SSE.Controllers.Main.txtShape_heptagon": "Heptagon", "SSE.Controllers.Main.txtShape_hexagon": "Sexhörning", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON><PERSON> rullning", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "SSE.Controllers.Main.txtShape_leftArrow": "Vänsterpil", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Pratbubbla vänsterpil", "SSE.Controllers.Main.txtShape_leftBrace": "Vänsterklammer", "SSE.Controllers.Main.txtShape_leftBracket": "Vänster hakparentes", "SSE.Controllers.Main.txtShape_leftRightArrow": "Vänster-höger-pil", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Pratbubbla vänster-höger-pil", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "V<PERSON>nst<PERSON> höger uppåtpil", "SSE.Controllers.Main.txtShape_leftUpArrow": "Vänster uppåtpil", "SSE.Controllers.Main.txtShape_lightningBolt": "Blixt", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON>l", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON> pil", "SSE.Controllers.Main.txtShape_mathDivide": "Division", "SSE.Controllers.Main.txtShape_mathEqual": "Lika med", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiplicera", "SSE.Controllers.Main.txtShape_mathNotEqual": "Inte lika", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "\"Ingen\" symbol", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Skårad högerpil", "SSE.Controllers.Main.txtShape_octagon": "Oktagon", "SSE.Controllers.Main.txtShape_parallelogram": "Parallellogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "Underteckna", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline2": "Fri form", "SSE.Controllers.Main.txtShape_quadArrow": "F<PERSON><PERSON>d pil", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Fyrdelad pil med pratbubbla", "SSE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "Down Ribbon", "SSE.Controllers.Main.txtShape_ribbon2": "Uppåt band", "SSE.Controllers.Main.txtShape_rightArrow": "Högerpil", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Höger<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBrace": "Högerklammer", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "Rektangel med ett rundat hörn", "SSE.Controllers.Main.txtShape_round2DiagRect": "Rektangel med rundade hörn diagonalt", "SSE.Controllers.Main.txtShape_round2SameRect": "Re<PERSON><PERSON>el med rundade hörn samma sida", "SSE.Controllers.Main.txtShape_roundRect": "Rektangel med avrundade hörn", "SSE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON> triangel", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON> gubbe", "SSE.Controllers.Main.txtShape_snip1Rect": "Rektangel med ett klippt hörn", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Rektangel med klippta hörn diagonalt", "SSE.Controllers.Main.txtShape_snip2SameRect": "Rektangel med klippta hörn på en sida", "SSE.Controllers.Main.txtShape_snipRoundRect": "Rektangel med ett klippt och rundat hörn", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON> stjärna", "SSE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON> stjärna", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON> stjärna", "SSE.Controllers.Main.txtShape_star24": "24-<PERSON>ts stjärna", "SSE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON> stjärna", "SSE.Controllers.Main.txtShape_star4": "4-<PERSON>ts stjärna", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON> stjärna", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON> stjä<PERSON>", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON> stjärna", "SSE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON> stjärna", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Genomstruken högerpil", "SSE.Controllers.Main.txtShape_sun": "Sol", "SSE.Controllers.Main.txtShape_teardrop": "T<PERSON>r", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_trapezoid": "Trapes", "SSE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "Pil upp", "SSE.Controllers.Main.txtShape_upArrowCallout": "Pratbubbla uppåtpil", "SSE.Controllers.Main.txtShape_upDownArrow": "Upp-<PERSON>er pil", "SSE.Controllers.Main.txtShape_uturnArrow": "U-sväng pil", "SSE.Controllers.Main.txtShape_verticalScroll": "Vertikal rullning", "SSE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Callout", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Pratbubbla rektangel", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rundad rektangel pratbubbla", "SSE.Controllers.Main.txtStarsRibbons": "Stjärnor & banner", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "Be<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Markera cell", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Valuta", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Förklarande text", "SSE.Controllers.Main.txtStyle_Good": "Bra", "SSE.Controllers.Main.txtStyle_Heading_1": "Rubrik 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Rubrik 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Rubrik 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Rubrik 4", "SSE.Controllers.Main.txtStyle_Input": "Inmatning", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Länkad cell", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "Procent", "SSE.Controllers.Main.txtStyle_Title": "Titel", "SSE.Controllers.Main.txtStyle_Total": "Totalt", "SSE.Controllers.Main.txtStyle_Warning_Text": "Varningstext", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Tid", "SSE.Controllers.Main.txtUnlock": "<PERSON><PERSON><PERSON> upp", "SSE.Controllers.Main.txtUnlockRange": "<PERSON><PERSON><PERSON> upp intervall", "SSE.Controllers.Main.txtUnlockRangeDescription": "<PERSON><PERSON> ett lösenord för att ändra intervallet:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Ett intervall du försöker ändra är skyddat av lösenord.", "SSE.Controllers.Main.txtValues": "Värden", "SSE.Controllers.Main.txtXAxis": "X-axel", "SSE.Controllers.Main.txtYAxis": "Y-axel", "SSE.Controllers.Main.txtYears": "<PERSON>r", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON>nt fel.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>e stöds ej.", "SSE.Controllers.Main.uploadDocExtMessage": "Okänt dokumentformat.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Inga dokument uppladdade.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maximal gräns för dokumentstorlek har överskridits.", "SSE.Controllers.Main.uploadImageExtMessage": "Okänt bildformat.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Inga bilder uppladdade.", "SSE.Controllers.Main.uploadImageSizeMessage": "Bilden är för stor. Den maximala storleken är 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Laddar upp bild...", "SSE.Controllers.Main.uploadImageTitleText": "Laddar upp bild", "SSE.Controllers.Main.waitText": "Vänta...", "SSE.Controllers.Main.warnBrowserIE9": "Fungerar dåligt med Internet Explorer 9. Använd version 10 eller högre.", "SSE.Controllers.Main.warnBrowserZoom": "<PERSON> webbläsares nuvarande zoominställningar stöds inte fullt ut. Återställ till standard zoom genom att trycka på Ctrl + 0.", "SSE.Controllers.Main.warnLicenseExceeded": "Gränsen är nådd för antalet %1 samtidiga anslutna redigerare. Dokumentet öppnas som skrivskyddat.<br>Kontakta administratören för mer information.", "SSE.Controllers.Main.warnLicenseExp": "Din licens har gått ut.<br>Förnya din licens och uppdatera sidan.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licensen ogiltig. <br> Ingen access till redigeringsfunktioner.<br>Kontakta din administratör.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Licensen måste förnyas.<br>Endast begränsad funktionalitet.<br>Kontakta din administratör för full funktionalitet.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Gränsen är nådd för antalet %1 redigerare.<br>Kontakta administratören för mer information.", "SSE.Controllers.Main.warnNoLicense": "Gränsen är nådd för antalet %1 samtidiga anslutna redigerare. Dokumentet öppnas som skrivskyddat.<br>Kontakta %1 försäljningsteamet för personliga uppgraderings villkor.", "SSE.Controllers.Main.warnNoLicenseUsers": "Gränsen är nådd för antalet %1 redigerare.<br>Kontakta %1 försäljningsteamet för personliga uppgraderings villkor.", "SSE.Controllers.Main.warnProcessRightsChange": "Du har nekats rätten att redigera filen.", "SSE.Controllers.Print.strAllSheets": "Alla ka<PERSON>d", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON>a kolumnen", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>n", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON> kolumner", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON> reder", "SSE.Controllers.Print.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Controllers.Print.textNoRepeat": "Upprepa inte", "SSE.Controllers.Print.textRepeat": "Upprepa...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textWarning": "Varning", "SSE.Controllers.Print.txtCustom": "Anpassad", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON><PERSON><PERSON> marginaler", "SSE.Controllers.Search.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Controllers.Search.textNoTextFound": "De data som du har letat efter kunde inte hittas. Ändra dina sökalternativ.", "SSE.Controllers.Search.textReplaceSkipped": "Ersättningen har gjorts. {0} händelser hoppades över.", "SSE.Controllers.Search.textReplaceSuccess": "Sök<PERSON> har gjorts. {0} förekomster har ersatts", "SSE.Controllers.Statusbar.errorLastSheet": "Arbetsboken måste ha minst en synlig flik.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Kan inte radera kalkylbladet.", "SSE.Controllers.Statusbar.strSheet": "Flik", "SSE.Controllers.Statusbar.textDisconnect": "<br>Anslutningen förlorades</br><br>Försöker återansluta. Vänligen kontroller anslutningens inställningar.", "SSE.Controllers.Statusbar.textSheetViewTip": "Du är i Sheet View-läge. Filter och sortering är endast synliga för dig och de som fortfarande är i den här vyn.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Du är i Sheet View-läge. Filter är endast synliga för dig och de som fortfarande är i den här vyn.", "SSE.Controllers.Statusbar.warnDeleteSheet": "De valda kalkylarken kan innehålla data. Är du säker på att du vill fortsätta?", "SSE.Controllers.Statusbar.zoomText": "Zooma {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Teckensnittet du kommer att spara finns inte på den aktuella enheten. <br>Textstilen kommer att visas med ett av systemets teckensnitt, sparade teckensnitt kommer att användas när det är tillgängligt. <br> Vill du fortsätta ?", "SSE.Controllers.Toolbar.errorComboSeries": "Välj minst två serier med data för att skapa ett kombinationsschema.", "SSE.Controllers.Toolbar.errorMaxRows": "FEL! Det maximala antalet dataserier per diagram är 255", "SSE.Controllers.Toolbar.errorStockChart": "Felaktig radordning. Att bygga en börsdiagram plats data på arket i följande ordning: <br> <PERSON><PERSON><PERSON><PERSON><PERSON>, max pris, min pris, slutkurs.", "SSE.Controllers.Toolbar.textAccent": "Accenter", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Rik<PERSON>ning", "SSE.Controllers.Toolbar.textFontSizeErr": "Felaktigt värde.<br>Ange numeriskt värde mellan 1 och 409", "SSE.Controllers.Toolbar.textFraction": "Frak<PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "Indikatorer", "SSE.Controllers.Toolbar.textInsert": "Infoga", "SSE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Stora operatorer", "SSE.Controllers.Toolbar.textLimitAndLog": "Begränsningar och logaritmer", "SSE.Controllers.Toolbar.textLongOperation": "Lång operation", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operatorer", "SSE.Controllers.Toolbar.textPivot": "Pi<PERSON><PERSON>bell", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Betyg", "SSE.Controllers.Toolbar.textRecentlyUsed": "Nyligen använda", "SSE.Controllers.Toolbar.textScript": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Former", "SSE.Controllers.Toolbar.textSymbols": "Symboler", "SSE.Controllers.Toolbar.textWarning": "Varning", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Höger-vänster pil ovanför", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Vänster pil ovanför", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Högerriktad pil ovanför", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON> formel (med hållare)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Boxad formel (exempel)", "SSE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC med overbar", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y med overbar", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Trippel punkt", "SSE.Controllers.Toolbar.txtAccent_DDot": "Dubbelpunkt", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON> overbar", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grav", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Gruppera tecken under", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Gruppera tecken ovanför", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON> harp<PERSON> o<PERSON>", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Högerrik<PERSON> harp<PERSON>l ovanför", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parentes med separator", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parentes med separator", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parentes med separator", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Fall (tv<PERSON> villkor)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Fall (tre villkor)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Stapla objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Stapla objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Fall exempel", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial koefficient", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial koefficient", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentes med separator", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Enkel parentes", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Enkel parentes", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON><PERSON> celler", "SSE.Controllers.Toolbar.txtExpand": "Expandera och sortera", "SSE.Controllers.Toolbar.txtExpandSort": "Data bredvid markeringen kommer inte att sorteras. Vill du utöka markeringen så att den inkluderar intilliggande data eller fortsätta att sortera de markerade cellerna?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Skev fraktion", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Differentiell", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Differentiell", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Differentiell", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Differentiell", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Linjär fraktion", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi över 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Liten fraktion", "SSE.Controllers.Toolbar.txtFractionVertical": "Staplade fraktioner", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverterad cosinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperboliska inverterad cosinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverterad cotangent funktion", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperboliska inverterad cotangent funktion", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverterad cosecant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperboliska inverterad cosecant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverterad secant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperboliska inverterad secant funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Inverterad sinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperboliska inverterad sinus funktion", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverterad tangent funktion", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperboliska inverterad tangent funktion", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON>sin<PERSON> funktion", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hyperboliska cosinus funktion", "SSE.Controllers.Toolbar.txtFunction_Cot": "Cotangens funktion", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hyperboliska cotangent funktion", "SSE.Controllers.Toolbar.txtFunction_Csc": "Cosekant funktion", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hyperboliska cosekanten funktion", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangentformel", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hyperboliska secant funktion", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sine funktion", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hyperboliska sinus funktion", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangentfunktion", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hyperboliska tangent function", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON>ga celler", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differentiell theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differentiell x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differentiell y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Dubbelintegral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dubbelintegral", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dubbelintegral", "SSE.Controllers.Toolbar.txtIntegralOriented": "Konturintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Konturintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Ytliga integraler", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Ytliga integraler", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Ytliga integraler", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Konturintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volymintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volymintegral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volymintegral", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Trippel integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Trippel integral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Trippel integral", "SSE.Controllers.Toolbar.txtInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Samprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Samprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Samprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Samprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Samprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Koppla ihop", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Genomskärning", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Genomskärning", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Genomskärning", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Genomskärning", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Genomskärning", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summering", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Koppla ihop", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Koppla ihop", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Koppla ihop", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Koppla ihop", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Koppla ihop", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON><PERSON> exempel", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Max exempel", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Naturlig logaritm", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritm", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritm", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Max", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minst", "SSE.Controllers.Toolbar.txtLockSort": "Data finns bredvid ditt val men du har inte tillräckliga behörigheter för att ändra dessa celler.<br>Vill du fortsätta med nuvarande val?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 tom matris", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 tom matris", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 tom matris", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 tom matris", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON> matris med parenteser", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON> matris med parenteser", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON> matris med parenteser", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON> matris med parenteser", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 tom matris", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 tom matris", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 tom matris", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 tom matris", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON> i mitten", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonala punkter", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON> punkter", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON>les matris", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON>les matris", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identitetsmatris", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 identitetsmatris", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identitetsmatris", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identitetsmatris", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Höger vänsterpil under", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Höger-vänster pil ovanför", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Vänster pil nedanför", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Vänster pil ovanför", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Högerriktad pil nedanför", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Högerriktad pil ovanför", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Kolon är lika med", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Avkastning", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta-utbyten", "SSE.Controllers.Toolbar.txtOperator_Definition": "Lika med enligt definition", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta lika med", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Höger vänsterpil under", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Höger-vänster pil ovanför", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Vänster pil nedanför", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Vänster pil ovanför", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Högerriktad pil nedanför", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Högerriktad pil ovanför", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Lika med lika", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus lika med", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus lika med", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Mätt av", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Roten ur med grad", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radikal med grad", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> ur", "SSE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSup": "Nedsänkt-upphöjd", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Vänster subskript-superscript", "SSE.Controllers.Toolbar.txtScriptSup": "Upph<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSorting": "So<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSortSelected": "Sortering vald", "SSE.Controllers.Toolbar.txtSymbol_about": "Ungefär", "SSE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON> lika med", "SSE.Controllers.Toolbar.txtSymbol_ast": "Asterisk operator", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Slå vad", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Punktlista typ", "SSE.Controllers.Toolbar.txtSymbol_cap": "Genomskärning", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON><PERSON><PERSON> elips i mitten", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Grader celsius", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Ungefär lika med", "SSE.Controllers.Toolbar.txtSymbol_cup": "Koppla ihop", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON> <PERSON> diagonal ellips", "SSE.Controllers.Toolbar.txtSymbol_degree": "Grader", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Divisionstecken", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Nedåtpil", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Lika med", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identisk med", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Det existerar", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktoriell", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grader fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON><PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> än eller lika med", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON> s<PERSON>n", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_in": "Element av", "SSE.Controllers.Toolbar.txtSymbol_inc": "Öka", "SSE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Vänsterpil", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Vänster-höger pil", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> än eller lika med", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON> än", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> mindre än", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Inte lika med", "SSE.Controllers.Toolbar.txtSymbol_ni": "Fortsätt som medlem", "SSE.Controllers.Toolbar.txtSymbol_not": "Inte signerad", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Det existerar inte", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Partiell differential", "SSE.Controllers.Toolbar.txtSymbol_percent": "Procentsats", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proportionell till", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON> roten", "SSE.Controllers.Toolbar.txtSymbol_qed": "Slut på bevis", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Diagonal ellips", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Högerpil", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Ra<PERSON><PERSON> skylt", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Därför", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "Multiplikationstecken", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Pil upp", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Vertikal ellips", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "<PERSON><PERSON> mörk stil", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "<PERSON><PERSON> ljus stil", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Tabell medium stil", "SSE.Controllers.Toolbar.warnLongOperation": "Den operation du ska utföra kan ta ganska lång tid att slutföra. <br> Är du säker på att du vill fortsätta?", "SSE.Controllers.Toolbar.warnMergeLostData": "Endast data från den övre vänstra cellen kommer att förbli i den sammanfogade cellen. <br>Är du säker på att du vill fortsätta?", "SSE.Controllers.Viewport.textFreezePanes": "<PERSON><PERSON><PERSON> paneler", "SSE.Controllers.Viewport.textFreezePanesShadow": "Visa skugga för frysta rutor", "SSE.Controllers.Viewport.textHideFBar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textHideGridlines": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON> rubriker", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Decimaltecken", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Tusentals-separator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Inställningar som används för att känna igen numeriska data", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Betydelseindikat<PERSON> för text", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Avancerade inställningar", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(inget)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Anpassat filter", "SSE.Views.AutoFilterDialog.textAddSelection": "Lägg till aktuellt urval till filter", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanksteg}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON> allt", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Välj alla sökresultat", "SSE.Views.AutoFilterDialog.textWarning": "Varning", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON> medel", "SSE.Views.AutoFilterDialog.txtBegins": "B<PERSON>rjar med...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Under medel", "SSE.Views.AutoFilterDialog.txtBetween": "<PERSON>lan...", "SSE.Views.AutoFilterDialog.txtClear": "Ren<PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "Inneh<PERSON>ller...", "SSE.Views.AutoFilterDialog.txtEmpty": "<PERSON><PERSON> cellfilter", "SSE.Views.AutoFilterDialog.txtEnds": "Slutar med...", "SSE.Views.AutoFilterDialog.txtEquals": "Lika...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrera efter cellfärg", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrera efter typsnittsfärg", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON><PERSON> än eller lika med...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Etikettfilter", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON> än...", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON><PERSON> än eller lika med...", "SSE.Views.AutoFilterDialog.txtNotBegins": "B<PERSON>rjar inte med...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Inte mellan...", "SSE.Views.AutoFilterDialog.txtNotContains": "Innehåller inte...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Slutar inte med...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Är inte lika med...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Nummerfilter", "SSE.Views.AutoFilterDialog.txtReapply": "Tillämpa", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Sortera efter cell-färg", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Sortera efter font-färg", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Sortera högst till lägst", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Sortera Lägsta till Högsta", "SSE.Views.AutoFilterDialog.txtSortOption": "Flera sorteringsalternativ...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Textfilter", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtervärde", "SSE.Views.AutoFilterDialog.warnFilterError": "Du behöver minst ett fält i data-området för att kunna använda ett värdefilter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Du måste välja minst ett värde", "SSE.Views.CellEditor.textManager": "Namnhanterare", "SSE.Views.CellEditor.tipFormula": "Infoga funktion", "SSE.Views.CellRangeDialog.errorMaxRows": "FEL! Det maximala antalet dataserier per diagram är 255", "SSE.Views.CellRangeDialog.errorStockChart": "Felaktig ordningsföljd. F<PERSON>r att bygga ett aktiediagram placerar du uppgifterna på arket i följande ordning:<br> öppning<PERSON>ris, maxpris, minipris, slutkurs.", "SSE.Views.CellRangeDialog.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.CellRangeDialog.txtInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.strShrink": "Krymp för att passa", "SSE.Views.CellSettings.strWrap": "Radbryt text", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Bakgrundsfärg", "SSE.Views.CellSettings.textBackground": "Bakgrundsfärg", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Ramutseende", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON>", "SSE.Views.CellSettings.textColor": "Färgfyllnad", "SSE.Views.CellSettings.textColorScales": "Färgskalor", "SSE.Views.CellSettings.textCondFormat": "Villkorlig formatering", "SSE.Views.CellSettings.textControl": "Textkontroll", "SSE.Views.CellSettings.textDataBars": "Data staplar", "SSE.Views.CellSettings.textDirection": "Rik<PERSON>ning", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Förgrundsfärg", "SSE.Views.CellSettings.textGradient": "Triangulära punkter", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "F<PERSON><PERSON>ing", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "objekt", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "<PERSON><PERSON> regler", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "Ingen fyllning", "SSE.Views.CellSettings.textOrientation": "Text orientering", "SSE.Views.CellSettings.textPattern": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Position", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> hur kantlinjer ska tillämpas", "SSE.Views.CellSettings.textSelection": "Från aktuell markering", "SSE.Views.CellSettings.textThisPivot": "<PERSON><PERSON><PERSON> denna pivot", "SSE.Views.CellSettings.textThisSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textThisTable": "<PERSON><PERSON><PERSON> denna tabell", "SSE.Views.CellSettings.tipAddGradientPoint": "Lägg till lutningspunkt", "SSE.Views.CellSettings.tipAll": "<PERSON><PERSON>kant och alla inre linjer", "SSE.Views.CellSettings.tipBottom": "Endast yttre ram i botten", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON> diagonal ner ram", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON> diagonal upp ram", "SSE.Views.CellSettings.tipInner": "Endast inre linjer", "SSE.Views.CellSettings.tipInnerHor": "<PERSON><PERSON><PERSON> ho<PERSON> inre linjer", "SSE.Views.CellSettings.tipInnerVert": "Endast inre vertikala linjer", "SSE.Views.CellSettings.tipLeft": "Yttre vänstra ram endast", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON> ramar", "SSE.Views.CellSettings.tipOuter": "Endast ytterkant", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Ta bort lutningspunkten", "SSE.Views.CellSettings.tipRight": "Endast yttre höger ram", "SSE.Views.CellSettings.tipTop": "Endast yttre övre ram", "SSE.Views.ChartDataDialog.errorInFormula": "Det finns ett fel i formeln du angav.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Referensen är inte giltig. Hänvisningen måste vara till ett öppet kalkylblad.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Max antal punkter i serie är 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Det maximala antalet dataserier per diagram är 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Referensen är inte giltig. Referenser fö<PERSON> titlar, <PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON> eller datatiketter måste vara en enda cell, rad eller kolumn.", "SSE.Views.ChartDataDialog.errorNoValues": "<PERSON><PERSON><PERSON> att skapa ett diagram måste serien innehålla minst ett värde.", "SSE.Views.ChartDataDialog.errorStockChart": "Felaktig ordningsföljd. F<PERSON>r att bygga ett aktiediagram placerar du uppgifterna på kalkylarket i följande ordning:<br> öppningspris, maxpris, minipris, slutkurs.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON> till", "SSE.Views.ChartDataDialog.textCategory": "<PERSON><PERSON><PERSON><PERSON> horis<PERSON>ll axel", "SSE.Views.ChartDataDialog.textData": "Diagram data-område", "SSE.Views.ChartDataDialog.textDelete": "<PERSON> bort", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "Rediger<PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> cellområ<PERSON>", "SSE.Views.ChartDataDialog.textSelectData": "Välj data", "SSE.Views.ChartDataDialog.textSeries": "Legend Entries (Series)", "SSE.Views.ChartDataDialog.textSwitch": "Byt rad / kolumn", "SSE.Views.ChartDataDialog.textTitle": "Diagram data", "SSE.Views.ChartDataDialog.textUp": "Upp", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Det finns ett fel i formeln du angav.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Referensen är inte giltig. Hänvisningen måste vara till ett öppet kalkylblad.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Max antal punkter i serie är 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Det maximala antalet dataserier per diagram är 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Referensen är inte giltig. Referenser fö<PERSON> titlar, <PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON> eller datatiketter måste vara en enda cell, rad eller kolumn.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "<PERSON><PERSON><PERSON> att skapa ett diagram måste serien innehålla minst ett värde.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Felaktig ordningsföljd. F<PERSON>r att bygga ett aktiediagram placerar du uppgifterna på kalkylarket i följande ordning:<br> öppningspris, maxpris, minipris, slutkurs.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> cellområ<PERSON>", "SSE.Views.ChartDataRangeDialog.textSelectData": "Välj data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "<PERSON>", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Serienamn", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "<PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON><PERSON><PERSON> se<PERSON>", "SSE.Views.ChartDataRangeDialog.txtValues": "Värden", "SSE.Views.ChartDataRangeDialog.txtXValues": "X-värden", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y-värden", "SSE.Views.ChartSettings.errorMaxRows": "Det maximala antalet dataserier per diagram är 255.", "SSE.Views.ChartSettings.strLineWeight": "Linjetjocklek", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Mall", "SSE.Views.ChartSettings.textAdvanced": "Visa avancerade inställningar", "SSE.Views.ChartSettings.textBorderSizeErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett värde mellan 0 och 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON> typ", "SSE.Views.ChartSettings.textChartType": "<PERSON><PERSON> diagramtyp", "SSE.Views.ChartSettings.textEditData": "Redigera data och plats", "SSE.Views.ChartSettings.textFirstPoint": "Första punkten", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textKeepRatio": "Konstanta proportioner", "SSE.Views.ChartSettings.textLastPoint": "<PERSON>sta punkten", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNegativePoint": "Negativ punkt", "SSE.Views.ChartSettings.textRanges": "Dataomr<PERSON><PERSON>", "SSE.Views.ChartSettings.textSelectData": "Välj data", "SSE.Views.ChartSettings.textShow": "Visa", "SSE.Views.ChartSettings.textSize": "Storlek", "SSE.Views.ChartSettings.textStyle": "Stil", "SSE.Views.ChartSettings.textSwitch": "Byt rad/kolumn", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textWidth": "Bredd", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "FEL! Det maximala antalet punkter i serie per diagram är 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "FEL! Det maximala antalet dataserier per diagram är 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Felaktig ordningsföljd. F<PERSON>r att bygga ett aktiediagram placerar du uppgifterna på arket i följande ordning:<br> öppning<PERSON>ris, maxpris, minipris, slutkurs.", "SSE.Views.ChartSettingsDlg.textAbsolute": "<PERSON><PERSON> eller ändra inte storlek på celler", "SSE.Views.ChartSettingsDlg.textAlt": "Alternativ text", "SSE.Views.ChartSettingsDlg.textAltDescription": "Beskrivning", "SSE.Views.ChartSettingsDlg.textAltTip": "Den alternativa textbaserad förekomsten av visuell objektinformation som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar för att hjälpa dem att bättre förstå vilken information som finns i bilden, figuren, diagrammet eller tabellen.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Titel", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Auto för varje", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Axel alternativ", "SSE.Views.ChartSettingsDlg.textAxisPos": "Axel position", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Inställning axlar", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Titel", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "<PERSON><PERSON>-m<PERSON><PERSON>en", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "Kategorinamn", "SSE.Views.ChartSettingsDlg.textCenter": "Centrera", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Diagramelement &<br>Diagramförklaring", "SSE.Views.ChartSettingsDlg.textChartTitle": "Diagramtitel", "SSE.Views.ChartSettingsDlg.textCross": "Korsa", "SSE.Views.ChartSettingsDlg.textCustom": "Anpassad", "SSE.Views.ChartSettingsDlg.textDataColumns": "i kolumner", "SSE.Views.ChartSettingsDlg.textDataLabels": "Data-etiketter", "SSE.Views.ChartSettingsDlg.textDataRows": "i rader", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Visa förklaring", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON> och tomma celler", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Anslut datapunkter med linje", "SSE.Views.ChartSettingsDlg.textFit": "Anpassa till bredd", "SSE.Views.ChartSettingsDlg.textFixed": "Fast", "SSE.Views.ChartSettingsDlg.textFormat": "Etikettformat", "SSE.Views.ChartSettingsDlg.textGaps": "luckor", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Gruppera Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "G<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON><PERSON> axel", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON> ho<PERSON>lla axeln", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horisontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "in", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON>re botten", "SSE.Views.ChartSettingsDlg.textInnerTop": "Inre topp", "SSE.Views.ChartSettingsDlg.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.ChartSettingsDlg.textLabelDist": "<PERSON> Avstånd", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervall mellan etiketter", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Etikettalternativ", "SSE.Views.ChartSettingsDlg.textLabelPos": "Etikettposition", "SSE.Views.ChartSettingsDlg.textLayout": "Layout", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Vänster Overlay", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "Teckenförklaring", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Ö<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Platsintervall", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON><PERSON> och mindre", "SSE.Views.ChartSettingsDlg.textMajorType": "Huvudtyp", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervall mellan <PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON>tals", "SSE.Views.ChartSettingsDlg.textMinor": "Mindre", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON><PERSON> typ", "SSE.Views.ChartSettingsDlg.textMinValue": "Minsta värde", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Intill axel", "SSE.Views.ChartSettingsDlg.textNone": "Ingen", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Ingen överlagring", "SSE.Views.ChartSettingsDlg.textOneCell": "Flytta men ändra inte storlek på celler", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "On-<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOut": "Ut", "SSE.Views.ChartSettingsDlg.textOuterTop": "<PERSON><PERSON><PERSON> topp", "SSE.Views.ChartSettingsDlg.textOverlay": "Overlay", "SSE.Views.ChartSettingsDlg.textReverse": "Värden i omvänd ordning", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Omvänd ordning", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON><PERSON> overlay", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "Lika för alla", "SSE.Views.ChartSettingsDlg.textSelectData": "Välj data", "SSE.Views.ChartSettingsDlg.textSeparator": "Datatiketter Separator", "SSE.Views.ChartSettingsDlg.textSeriesName": "Serienamn", "SSE.Views.ChartSettingsDlg.textShow": "Visa", "SSE.Views.ChartSettingsDlg.textShowBorders": "Visa diagramkanter", "SSE.Views.ChartSettingsDlg.textShowData": "Visa data i dolda rader och kolumner", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Visa tomma celler som", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Visa axlar", "SSE.Views.ChartSettingsDlg.textShowValues": "Visa diagramvärden", "SSE.Views.ChartSettingsDlg.textSingle": "<PERSON>kel Sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "<PERSON><PERSON><PERSON> till cell", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparkline områden", "SSE.Views.ChartSettingsDlg.textStraight": "Rak", "SSE.Views.ChartSettingsDlg.textStyle": "Stil", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Markera alternativ", "SSE.Views.ChartSettingsDlg.textTitle": "Diagram - avancerade inställningar", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Avancerade inställningar", "SSE.Views.ChartSettingsDlg.textTop": "Ö<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrillions": "Trillions", "SSE.Views.ChartSettingsDlg.textTwoCell": "<PERSON><PERSON> och ändra storlek på celler", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Typ & data", "SSE.Views.ChartSettingsDlg.textUnits": "Visa enheter", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "Vertikal axel", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "<PERSON><PERSON> vertikala axeln", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X-axel titel", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y-axel titel", "SSE.Views.ChartSettingsDlg.textZero": "Noll", "SSE.Views.ChartSettingsDlg.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.ChartTypeDialog.errorComboSeries": "Välj minst två serier med data för att skapa ett kombinationsschema.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Den valda diagramtypen kräver den sekundära axeln som ett befintligt diagram använder. Välj en annan diagramtyp.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON> axeln", "SSE.Views.ChartTypeDialog.textSeries": "Serier", "SSE.Views.ChartTypeDialog.textStyle": "Stil", "SSE.Views.ChartTypeDialog.textTitle": "Diagramtyp", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.CreatePivotDialog.textDestination": "Välj tabellens plats", "SSE.Views.CreatePivotDialog.textExist": "Befintligt kalkylblad", "SSE.Views.CreatePivotDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> cellområ<PERSON>", "SSE.Views.CreatePivotDialog.textNew": "Nytt kalkylblad", "SSE.Views.CreatePivotDialog.textSelectData": "Välj data", "SSE.Views.CreatePivotDialog.textTitle": "Ska<PERSON> pivottabell", "SSE.Views.CreatePivotDialog.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.CreateSparklineDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.CreateSparklineDialog.textDestination": "V<PERSON><PERSON>j var du vill placera sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> cellområ<PERSON>", "SSE.Views.CreateSparklineDialog.textSelectData": "Välj data", "SSE.Views.CreateSparklineDialog.textTitle": "Skapa Sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.DataTab.capBtnGroup": "Grupp", "SSE.Views.DataTab.capBtnTextCustomSort": "Anpassad sortering", "SSE.Views.DataTab.capBtnTextDataValidation": "Datavalidering", "SSE.Views.DataTab.capBtnTextRemDuplicates": "<PERSON> bort dubbletter", "SSE.Views.DataTab.capBtnTextToCol": "Text till kolumner", "SSE.Views.DataTab.capBtnUngroup": "Dela upp", "SSE.Views.DataTab.capDataFromText": "Hämta data", "SSE.Views.DataTab.mniFromFile": "Från lokal TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Från TXT/CSV webbadress", "SSE.Views.DataTab.textBelow": "Summeringsrader under <PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textClear": "Rensa disposition", "SSE.Views.DataTab.textColumns": "<PERSON><PERSON><PERSON> kolumner", "SSE.Views.DataTab.textGroupColumns": "Gruppera kolumner", "SSE.Views.DataTab.textGroupRows": "<PERSON><PERSON><PERSON><PERSON> rader", "SSE.Views.DataTab.textRightOf": "Summeringskolumner till höger om detaljer", "SSE.Views.DataTab.textRows": "Avgruppera rader", "SSE.Views.DataTab.tipCustomSort": "Anpassad sortering", "SSE.Views.DataTab.tipDataFromText": "Hämta data från Text/csv fil", "SSE.Views.DataTab.tipDataValidation": "Datavalidering", "SSE.Views.DataTab.tipGroup": "<PERSON><PERSON><PERSON><PERSON> celler", "SSE.Views.DataTab.tipRemDuplicates": "Ta bort dubbla rader från ett kalk<PERSON>rk", "SSE.Views.DataTab.tipToColumns": "Separera celltext i kolumner", "SSE.Views.DataTab.tipUngroup": "Ta bort gruppering av celler", "SSE.Views.DataValidationDialog.errorFormula": "V<PERSON>rdet valideras för nä<PERSON> till ett fel. Vill du fortsätta?", "SSE.Views.DataValidationDialog.errorInvalid": "Värdet du angav för fältet \"{0}\" är ogiltigt.", "SSE.Views.DataValidationDialog.errorInvalidDate": "Datumet du angav för fältet \"{0}\" är ogiltigt.", "SSE.Views.DataValidationDialog.errorInvalidList": "Listkällan måste vara en avgränsad lista eller en hänvisning till en rad eller kolumn.", "SSE.Views.DataValidationDialog.errorInvalidTime": "<PERSON>n du angav för fältet \"{0}\" är ogiltig.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Fältet \"{1}\" måste vara större än eller lika med fältet \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Du måste ange ett värde i både fältet \"{0}\" och fältet \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Du måste ange ett värde i fältet \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Det angivna intervallet som du angav kan inte hittas.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negativa värden kan inte användas under villkor \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Fältet \"{0}\" måste vara ett numeriskt värde, ett numeriskt uttryck eller hänvisa till en cell som innehåller ett numeriskt värde.", "SSE.Views.DataValidationDialog.strError": "Felmeddelande", "SSE.Views.DataValidationDialog.strInput": "Inmatningsmeddelande", "SSE.Views.DataValidationDialog.strSettings": "Inställningar", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Tillämpa dessa ändringar på alla andra celler med samma inställningar", "SSE.Views.DataValidationDialog.textCellSelected": "<PERSON><PERSON><PERSON> cellen är vald, visa detta inmatningsmeddelande", "SSE.Views.DataValidationDialog.textCompare": "Jämför med", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "Slut<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndTime": "S<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textError": "Felmeddelande", "SSE.Views.DataValidationDialog.textFormula": "Formel", "SSE.Views.DataValidationDialog.textIgnore": "<PERSON><PERSON><PERSON> tomma", "SSE.Views.DataValidationDialog.textInput": "Inmatningsmeddelande", "SSE.Views.DataValidationDialog.textMax": "Max", "SSE.Views.DataValidationDialog.textMessage": "Meddelande", "SSE.Views.DataValidationDialog.textMin": "Minst", "SSE.Views.DataValidationDialog.textSelectData": "Välj data", "SSE.Views.DataValidationDialog.textShowDropDown": "Visa drop-down-list i cell", "SSE.Views.DataValidationDialog.textShowError": "Visa felvarning efter att ogiltiga data har angetts", "SSE.Views.DataValidationDialog.textShowInput": "Visa inmatningsmeddelande när cell är vald", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Startdatum", "SSE.Views.DataValidationDialog.textStartTime": "Starttid", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "Stil", "SSE.Views.DataValidationDialog.textTitle": "Titel", "SSE.Views.DataValidationDialog.textUserEnters": "När användaren anger ogiltiga data, visa detta felmeddelande", "SSE.Views.DataValidationDialog.txtAny": "Valfritt värde", "SSE.Views.DataValidationDialog.txtBetween": "mellan", "SSE.Views.DataValidationDialog.txtDate": "Datum", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> tid", "SSE.Views.DataValidationDialog.txtEndDate": "Slut<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndTime": "S<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEqual": "lika", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "<PERSON><PERSON><PERSON> än eller lika med", "SSE.Views.DataValidationDialog.txtLength": "Längd", "SSE.Views.DataValidationDialog.txtLessThan": "<PERSON><PERSON>n", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "<PERSON>re än eller lika med", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "inte mellan", "SSE.Views.DataValidationDialog.txtNotEqual": "Är inte lika med", "SSE.Views.DataValidationDialog.txtOther": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "Startdatum", "SSE.Views.DataValidationDialog.txtStartTime": "Starttid", "SSE.Views.DataValidationDialog.txtTextLength": "Textlängd", "SSE.Views.DataValidationDialog.txtTime": "Tid", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "<PERSON>ch", "SSE.Views.DigitalFilterDialog.capCondition1": "lika", "SSE.Views.DigitalFilterDialog.capCondition10": "slutar inte med", "SSE.Views.DigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "innehåller inte", "SSE.Views.DigitalFilterDialog.capCondition2": "är inte lika med", "SSE.Views.DigitalFilterDialog.capCondition3": "är st<PERSON><PERSON> än", "SSE.Views.DigitalFilterDialog.capCondition4": "är större än eller lika med", "SSE.Views.DigitalFilterDialog.capCondition5": "är mindre än", "SSE.Views.DigitalFilterDialog.capCondition6": "är mindre än eller lika med", "SSE.Views.DigitalFilterDialog.capCondition7": "b<PERSON><PERSON><PERSON> med", "SSE.Views.DigitalFilterDialog.capCondition8": "b<PERSON><PERSON><PERSON> inte med", "SSE.Views.DigitalFilterDialog.capCondition9": "slutar med", "SSE.Views.DigitalFilterDialog.capOr": "Eller", "SSE.Views.DigitalFilterDialog.textNoFilter": "inget filter", "SSE.Views.DigitalFilterDialog.textShowRows": "Visa rader där", "SSE.Views.DigitalFilterDialog.textUse1": "Använda sig av ? att presentera varje enskilt tecken", "SSE.Views.DigitalFilterDialog.textUse2": "Använd * för att presentera alla serier av tecken", "SSE.Views.DigitalFilterDialog.txtTitle": "Anpassat filter", "SSE.Views.DocumentHolder.advancedImgText": "Bild avancerade inställningar", "SSE.Views.DocumentHolder.advancedShapeText": "Form - Avancerade inställningar", "SSE.Views.DocumentHolder.advancedSlicerText": "Slicer avancerade inställningar", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON> botten", "SSE.Views.DocumentHolder.bulletsText": "<PERSON><PERSON> och numrering", "SSE.Views.DocumentHolder.centerCellText": "Centrera", "SSE.Views.DocumentHolder.chartText": "Diagram avancerade inställningar", "SSE.Views.DocumentHolder.chartTypeText": "<PERSON><PERSON> diagramtyp", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Rotera text uppåt", "SSE.Views.DocumentHolder.direct90Text": "Rotera text nedåt", "SSE.Views.DocumentHolder.directHText": "Horisontal", "SSE.Views.DocumentHolder.directionText": "Textriktning", "SSE.Views.DocumentHolder.editChartText": "Redigera data", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Views.DocumentHolder.insertColumnLeftText": "Kolumn vänster", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowBelowText": "Rad under", "SSE.Views.DocumentHolder.originalSizeText": "Verklig storlek", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON> bort länk", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.strDelete": "Ta bort signatur", "SSE.Views.DocumentHolder.strDetails": "Signaturdetaljer", "SSE.Views.DocumentHolder.strSetup": "Skapa signatur", "SSE.Views.DocumentHolder.strSign": "Underteckna", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "Ordna", "SSE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> l<PERSON> bak", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON> b<PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON> f<PERSON>", "SSE.Views.DocumentHolder.textArrangeFront": "Flytta fram", "SSE.Views.DocumentHolder.textAverage": "Genomsnitt", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCrop": "Beskär", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Anpass<PERSON>", "SSE.Views.DocumentHolder.textEditPoints": "Redigera punkter", "SSE.Views.DocumentHolder.textEntriesList": "<PERSON><PERSON><PERSON><PERSON> fr<PERSON><PERSON> listan", "SSE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Views.DocumentHolder.textFlipV": "Vänd vertikalt", "SSE.Views.DocumentHolder.textFreezePanes": "<PERSON><PERSON><PERSON> paneler", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON><PERSON> fil", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "SSE.Views.DocumentHolder.textFromUrl": "Från URL", "SSE.Views.DocumentHolder.textListSettings": "Inställningar lista", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON> makro", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON> funk<PERSON>er", "SSE.Views.DocumentHolder.textMoreFormats": "Flera format", "SSE.Views.DocumentHolder.textNone": "Ingen", "SSE.Views.DocumentHolder.textNumbering": "Numrering", "SSE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON><PERSON> bild", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Rotera 90° moturs", "SSE.Views.DocumentHolder.textRotate90": "Rotera 90° medsols", "SSE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON> botten", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Centrera", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Vänsterjustera", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Centrera", "SSE.Views.DocumentHolder.textShapeAlignRight": "Högerjustera", "SSE.Views.DocumentHolder.textShapeAlignTop": "Justera till toppen", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Summa", "SSE.Views.DocumentHolder.textUndo": "Å<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "<PERSON><PERSON><PERSON>p paneler", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Pil punkter", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "<PERSON>ck punkt", "SSE.Views.DocumentHolder.tipMarkersDash": "Sträck punkter", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "<PERSON><PERSON><PERSON> romb punkter", "SSE.Views.DocumentHolder.tipMarkersFRound": "<PERSON><PERSON><PERSON> runda <PERSON>ter", "SSE.Views.DocumentHolder.tipMarkersFSquare": "<PERSON><PERSON><PERSON> k<PERSON>dratiska punkter", "SSE.Views.DocumentHolder.tipMarkersHRound": "<PERSON><PERSON><PERSON> runda <PERSON>ter", "SSE.Views.DocumentHolder.tipMarkersStar": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.topCellText": "Justera till toppen", "SSE.Views.DocumentHolder.txtAccounting": "Redovisning", "SSE.Views.DocumentHolder.txtAddComment": "Lägg till kommentar", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON> namn", "SSE.Views.DocumentHolder.txtArrange": "Ordna", "SSE.Views.DocumentHolder.txtAscending": "Stigande", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automatisk kolumnbredd", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automatisk radhöjd", "SSE.Views.DocumentHolder.txtClear": "Ren<PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON>a", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Hyperlänkar", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Rensa valda Sparkline-grupper", "SSE.Views.DocumentHolder.txtClearSparklines": "Rensa valda Sparklines", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON> kol<PERSON>nbredd", "SSE.Views.DocumentHolder.txtCondFormat": "Villkorlig formatering", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCurrency": "Valuta", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Anpassad kolumnbredd", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Anpassad radhöjd", "SSE.Views.DocumentHolder.txtCustomSort": "Anpassad sortering", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON> ut", "SSE.Views.DocumentHolder.txtDate": "Datum", "SSE.Views.DocumentHolder.txtDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuera horisontellt", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuera vertikalt", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON><PERSON> kommentar", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrera efter cellens färg", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrera efter typsnittsfärg", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrera efter vald cells värde", "SSE.Views.DocumentHolder.txtFormula": "Infoga funktion", "SSE.Views.DocumentHolder.txtFraction": "Fraktion", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGroup": "Grupp", "SSE.Views.DocumentHolder.txtHide": "G<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsert": "Infoga", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hyperlänk", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "Sifferformat", "SSE.Views.DocumentHolder.txtPaste": "Klistra in", "SSE.Views.DocumentHolder.txtPercentage": "Procentsats", "SSE.Views.DocumentHolder.txtReapply": "Tillämpa", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON> r<PERSON>n", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtScientific": "Vetenskaplig", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON> cell<PERSON>", "SSE.Views.DocumentHolder.txtShiftLeft": "Flytta celler åt vänster", "SSE.Views.DocumentHolder.txtShiftRight": "Flytta celler <PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON> cell<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShow": "Visa", "SSE.Views.DocumentHolder.txtShowComment": "Visa kommentar", "SSE.Views.DocumentHolder.txtSort": "Sortera", "SSE.Views.DocumentHolder.txtSortCellColor": "<PERSON>d cellf<PERSON>", "SSE.Views.DocumentHolder.txtSortFontColor": "Välj font-f<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Styckets avancerade inställningar", "SSE.Views.DocumentHolder.txtTime": "Tid", "SSE.Views.DocumentHolder.txtUngroup": "Dela upp", "SSE.Views.DocumentHolder.txtWidth": "Bredd", "SSE.Views.DocumentHolder.vertAlignText": "Vertikal anpassning", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Delsumma", "SSE.Views.FieldSettingsDialog.textReport": "Rapportformulär", "SSE.Views.FieldSettingsDialog.textTitle": "Fältinställningar", "SSE.Views.FieldSettingsDialog.txtAverage": "Genomsnitt", "SSE.Views.FieldSettingsDialog.txtBlank": "Infoga tom rad efter varje objekt", "SSE.Views.FieldSettingsDialog.txtBottom": "Visa längst ner i gruppen", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompakt", "SSE.Views.FieldSettingsDialog.txtCount": "R<PERSON>k<PERSON>", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON> nummer", "SSE.Views.FieldSettingsDialog.txtCustomName": "Anpassat namn", "SSE.Views.FieldSettingsDialog.txtEmpty": "Visa objekt utan data", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Översikt", "SSE.Views.FieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.FieldSettingsDialog.txtRepeat": "Upprepa etiketter på varje rad", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Visa delsummor", "SSE.Views.FieldSettingsDialog.txtSourceName": "Källnamn:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Summa", "SSE.Views.FieldSettingsDialog.txtSummarize": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabellformat", "SSE.Views.FieldSettingsDialog.txtTop": "Visa överst i gruppen", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Öppna filens plats", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON>ng meny", "SSE.Views.FileMenu.btnCreateNewCaption": "Skapa ny", "SSE.Views.FileMenu.btnDownloadCaption": "Ladda ner som", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnFileOpenCaption": "Öppna", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Versionshistorik", "SSE.Views.FileMenu.btnInfoCaption": "Info arbetsbok", "SSE.Views.FileMenu.btnPrintCaption": "Skriv ut", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON>dda", "SSE.Views.FileMenu.btnRecentFilesCaption": "Ö<PERSON>na senaste", "SSE.Views.FileMenu.btnRenameCaption": "Döp om", "SSE.Views.FileMenu.btnReturnCaption": "Tillbaka till kalkylblad", "SSE.Views.FileMenu.btnRightsCaption": "Behörigheter", "SSE.Views.FileMenu.btnSaveAsCaption": "Spara som", "SSE.Views.FileMenu.btnSaveCaption": "Spara", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Spara kopia som", "SSE.Views.FileMenu.btnSettingsCaption": "Avancerade inställningar", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Skapa ny", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Tillämpa", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Lägg till författare", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON>gg till text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Program", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Skapad ", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Senast ändrad av", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Ägare", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Plats", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON>er som har beh<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Ämne", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titel", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uppladdad", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON>er som har beh<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Tillämpa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Decimaltecken", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Rättstavningsspråk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON>bb", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Fontförslag", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formelspråk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "SUMMA; MIN; MAX; ANTAL", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorera ord med VERSALER", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignorera ord med siffror", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Makroinställningar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Visa knappen Klistra in alternativ när innehållet klistras in", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regionala inställningar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Exempel:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Visa ändringar från andra anv<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Visa lösta kommentarer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strikt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Gränssnittstema", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Tusentals-separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Måttenhet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Använd separatorer baserat på regionala inställningar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Standard zoomvärde", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Var 10:e minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Var 30:e minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Var 5:e minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON> timme", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Återskapa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Autospara", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Inaktiverad", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "<PERSON><PERSON> me<PERSON>ggande versioner", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON>ut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Referensstil", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Inställning autokorrigering...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Katalanska", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Standard cache-läge", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Samarbete", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Czech", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Dansk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Tysk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Redigera och spara", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Greek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Engelsk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanska", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Samredigering i realtid. Alla ändringar sparas automatiskt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Slutför", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Franska", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "som OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Orginal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Dutch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portugisiska (Brasilien)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romanian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Rysk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Aktivera alla", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Aktivera alla makron utan avisering", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Inaktivera allt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Inaktivera alla makron utan avisering", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "<PERSON><PERSON><PERSON><PERSON> knap<PERSON> \"<PERSON>ra\" för att synkronisera ändringarna du och andra gör", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Swedish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Turkish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukrainian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Använd Alt-tangenten för att navigera i användargränssnittet med tangentbordet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Använd Option-tangenten för att navigera i användargränssnittet med tangentbordet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Visa meddelanden", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Inaktivera alla makron med en avisering", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "som Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "A<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Chinese", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Varning", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Med <PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON>dda <PERSON>d", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Med signatur", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Redigering tar bort signaturerna från kalkylbladet.<br><PERSON>r du säker på att du vill fortsätta?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON><PERSON> ka<PERSON>blad har skyddats med lösenord", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "<PERSON><PERSON>blad måste signeras.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Giltiga signaturer har lagts till i kalkylbladet. Kalkylarket är skyddat från redigering.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Vissa av de digitala signaturerna i kalkylarket är ogiltiga eller kunde inte verifieras. Kalkylarket är skyddat från redigering.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Visa signaturer", "SSE.Views.FormatRulesEditDlg.fillColor": "Bakgrundsfärg", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Varning", "SSE.Views.FormatRulesEditDlg.text2Scales": "2-<PERSON><PERSON><PERSON><PERSON> skala", "SSE.Views.FormatRulesEditDlg.text3Scales": "3-<PERSON><PERSON><PERSON><PERSON> skala", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON>a ramar", "SSE.Views.FormatRulesEditDlg.textAppearance": "Stapel u<PERSON>de", "SSE.Views.FormatRulesEditDlg.textApply": "Tillämpa på intervall", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatisk", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Stapel rik<PERSON>ning", "SSE.Views.FormatRulesEditDlg.textBold": "Fet", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Ramutseende", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Det går inte att lägga till den villkorliga formateringen.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Cell mittpunkt", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>rt<PERSON>", "SSE.Views.FormatRulesEditDlg.textClear": "Ren<PERSON>", "SSE.Views.FormatRulesEditDlg.textColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textContext": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCustom": "Anpassad", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON><PERSON> nedre ram", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diagonal upp ram", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Ange en giltig formel.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Formeln du angav resulterar inte till nummer, datum, tid eller sträng.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Ange ett värde.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Det angivna värdet är inte ett giltigt nummer, datum, tid eller sträng.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "<PERSON><PERSON>rde<PERSON> för {0} måste vara större än värdet för {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "<PERSON><PERSON> tal mellan {0} och {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formel", "SSE.Views.FormatRulesEditDlg.textGradient": "F<PERSON><PERSON>ing", "SSE.Views.FormatRulesEditDlg.textIconLabel": "när {0} {1} och", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "när {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "när värdet är", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "En eller flera ikondataområden överlappar varandra. <br> Justera värdena för dataintervall för ikoner så att intervallen inte överlappar varandra.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Ikonstil", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Ogiltigt dataområde", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Element", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Vänster till höger", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "längsta stapeln", "SSE.Views.FormatRulesEditDlg.textMaximum": "Max", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpunkt", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Mittpunkt", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minst", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Lägsta punkten", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativ", "SSE.Views.FormatRulesEditDlg.textNewColor": "Lägg till ny egen färg", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON> ramar", "SSE.Views.FormatRulesEditDlg.textNone": "ingen", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "<PERSON><PERSON> eller flera av de angivna värdena är inte en giltig procentsats.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Det angivna {0} värdet är inte en giltig procentsats.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "<PERSON><PERSON> eller flera av de angivna värdena är inte en giltig percentil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Det angivna {0} -värdet är inte en giltig percentil.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "Procent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "Positiv", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "Förhandsvisa", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Du kan inte använda relativa referenser i villkorliga formateringskriterier för färgskalor, datafält och ikonuppsättningar.", "SSE.Views.FormatRulesEditDlg.textReverse": "Omvänd ikonordning", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Höger till vänster", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "Samma som positivt", "SSE.Views.FormatRulesEditDlg.textSelectData": "Välj data", "SSE.Views.FormatRulesEditDlg.textShortBar": "kortaste stapeln", "SSE.Views.FormatRulesEditDlg.textShowBar": "Visa endast stapel", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Visa endast ikon", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Den här typen av referens kan inte användas i en villkorlig formateringsformel. <br> <PERSON><PERSON> referensen till en enda cell eller använd referensen med en kalkylbladfunktion, till exempel = SUM (A1: B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Fast", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Genomstryk", "SSE.Views.FormatRulesEditDlg.textSubscript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Upph<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON>", "SSE.Views.FormatRulesEditDlg.textUnderline": "Understrykning", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Sifferformat", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Redovisning", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Valuta", "SSE.Views.FormatRulesEditDlg.txtDate": "Datum", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fraktion", "SSE.Views.FormatRulesEditDlg.txtGeneral": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Ingen ikon", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Procentsats", "SSE.Views.FormatRulesEditDlg.txtScientific": "Vetenskaplig", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Tid", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Redigera formateringsregel", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Ny formateringsregel", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1: a dev över genomsnittet", "SSE.Views.FormatRulesManagerDlg.text1Below": "1: a dev under geno<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev över genomsnitt", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev under genomsnitt", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev över genomsnitt", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev under genomsnitt", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON> medel", "SSE.Views.FormatRulesManagerDlg.textApply": "Tillämpa på", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "<PERSON><PERSON><PERSON><PERSON> bö<PERSON><PERSON> med", "SSE.Views.FormatRulesManagerDlg.textBelow": "Under medel", "SSE.Views.FormatRulesManagerDlg.textBetween": "<PERSON>r mellan {0} och {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Cell värde", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Graderad färgskala", "SSE.Views.FormatRulesManagerDlg.textContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cellen innehåller ett tomt värde", "SSE.Views.FormatRulesManagerDlg.textContainsError": "<PERSON><PERSON> innehåller ett fel", "SSE.Views.FormatRulesManagerDlg.textDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textDown": "<PERSON><PERSON> regel ned", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicera värden", "SSE.Views.FormatRulesManagerDlg.textEdit": "Rediger<PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Cellvärdet slutar med", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Lika med eller högre än genomsnitt", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Lika med eller lägre än medel", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Ikonuppsättning", "SSE.Views.FormatRulesManagerDlg.textNew": "Ny", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "är inte mellan {0} och {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Cellvärdet innehåller inte", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cellen innehåller inte ett tomt värde", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cell innehåller inte något fel", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Visa formateringsregler för", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Välj data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Aktuell markering", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON> pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> tabell", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unika värden", "SSE.Views.FormatRulesManagerDlg.textUp": "<PERSON>tta regel upp", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Detta element redigeras av en annan användare.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Villkorlig formatering", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Kopplad till källa", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON><PERSON><PERSON><PERSON> tusentals-separator", "SSE.Views.FormatSettingsDialog.textSymbols": "Symboler", "SSE.Views.FormatSettingsDialog.textTitle": "Sifferformat", "SSE.Views.FormatSettingsDialog.txtAccounting": "Redovisning", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON> tion<PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON> sex<PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Som halv (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON> <PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Valuta", "SSE.Views.FormatSettingsDialog.txtCustom": "Anpassad", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Ange det anpassade nummerformatet noggrant. Kalkylredigeraren kontrollerar inte anpassade format för fel som kan påverka xlsx-filen.", "SSE.Views.FormatSettingsDialog.txtDate": "Datum", "SSE.Views.FormatSettingsDialog.txtFraction": "Fraktion", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "Ingen", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Procentsats", "SSE.Views.FormatSettingsDialog.txtSample": "Exempel:", "SSE.Views.FormatSettingsDialog.txtScientific": "Vetenskaplig", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Tid", "SSE.Views.FormatSettingsDialog.txtUpto1": "Upp till en siffra (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Upp till två siffror (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Upp till tre siffror (131/135)", "SSE.Views.FormulaDialog.sDescription": "Beskrivning", "SSE.Views.FormulaDialog.textGroupDescription": "Välj funktionsgrupp", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Rekommenderad", "SSE.Views.FormulaDialog.txtSearch": "<PERSON>ö<PERSON>", "SSE.Views.FormulaDialog.txtTitle": "Infoga funktion", "SSE.Views.FormulaTab.textAutomatic": "Automatisk", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Beräkna det aktuella arket", "SSE.Views.FormulaTab.textCalculateWorkbook": "Räkna om arbetsboken", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Räkna om hela arbetsbladet", "SSE.Views.FormulaTab.txtAdditional": "Extra", "SSE.Views.FormulaTab.txtAutosum": "Autosumma", "SSE.Views.FormulaTab.txtAutosumTip": "Summering", "SSE.Views.FormulaTab.txtCalculation": "Be<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Funktion", "SSE.Views.FormulaTab.txtFormulaTip": "Infoga funktion", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON> funk<PERSON>er", "SSE.Views.FormulaTab.txtRecent": "Tidigare använda", "SSE.Views.FormulaWizard.textAny": "Någon", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Funktion", "SSE.Views.FormulaWizard.textFunctionRes": "Funktionsresultat", "SSE.Views.FormulaWizard.textHelp": "Hjälp med den här funktionen", "SSE.Views.FormulaWizard.textLogical": "logisk", "SSE.Views.FormulaWizard.textNoArgs": "<PERSON><PERSON> funktion har inga argument", "SSE.Views.FormulaWizard.textNumber": "nummer", "SSE.Views.FormulaWizard.textRef": "referens", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Funktionsargument", "SSE.Views.FormulaWizard.textValue": "Formelresultat", "SSE.Views.HeaderFooterDialog.textAlign": "Anpassa till sidbredd", "SSE.Views.HeaderFooterDialog.textAll": "Alla sidor", "SSE.Views.HeaderFooterDialog.textBold": "Fet", "SSE.Views.HeaderFooterDialog.textCenter": "Centrera", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDate": "Datum", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Annan förstasida", "SSE.Views.HeaderFooterDialog.textDiffOdd": "<PERSON><PERSON><PERSON> udda och jämna sidor", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON><PERSON> sidor", "SSE.Views.HeaderFooterDialog.textFileName": "Filnamn", "SSE.Views.HeaderFooterDialog.textFirst": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFooter": "Sidfot", "SSE.Views.HeaderFooterDialog.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textInsert": "Infoga", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "Texten är för lång. Minska antalet tecken.", "SSE.Views.HeaderFooterDialog.textNewColor": "Lägg till ny egen färg", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON> sidor", "SSE.Views.HeaderFooterDialog.textPageCount": "Sidantal", "SSE.Views.HeaderFooterDialog.textPageNum": "Sidnummer", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Skala till dokumentet", "SSE.Views.HeaderFooterDialog.textSheet": "Fliknamn", "SSE.Views.HeaderFooterDialog.textStrikeout": "Genomstruken", "SSE.Views.HeaderFooterDialog.textSubscript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSuperscript": "Upph<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTime": "Tid", "SSE.Views.HeaderFooterDialog.textTitle": "Inställningar sidhuvud och fot", "SSE.Views.HeaderFooterDialog.textUnderline": "Understrykning", "SSE.Views.HeaderFooterDialog.tipFontName": "Font", "SSE.Views.HeaderFooterDialog.tipFontSize": "Teckensnittsstorlek", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Visa", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Lä<PERSON> till", "SSE.Views.HyperlinkSettingsDialog.strRange": "Omgång", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Flik", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Valt intervall", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON> rubrik här", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON> länk här", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Ange verktygstips här", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Extern länk", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Häm<PERSON> länk", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Internt <PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.HyperlinkSettingsDialog.textNames": "Definierade namn", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Välj data", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Skärmtext", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlänkinställningar", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON>ta fält bör vara en URL i formatet \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Detta fält är begränsat till 2083 tecken", "SSE.Views.ImageSettings.textAdvanced": "Visa avancerade inställningar", "SSE.Views.ImageSettings.textCrop": "Beskär", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Anpass<PERSON>", "SSE.Views.ImageSettings.textCropToShape": "Beskär till form", "SSE.Views.ImageSettings.textEdit": "Rediger<PERSON>", "SSE.Views.ImageSettings.textEditObject": "Redigera objekt", "SSE.Views.ImageSettings.textFlip": "Vänd", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON><PERSON> fil", "SSE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "SSE.Views.ImageSettings.textFromUrl": "Från URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "Rotera 90° moturs", "SSE.Views.ImageSettings.textHint90": "Rotera 90° medsols", "SSE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Views.ImageSettings.textHintFlipV": "Vänd vertikalt", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON><PERSON> bild", "SSE.Views.ImageSettings.textKeepRatio": "Konstanta proportioner", "SSE.Views.ImageSettings.textOriginalSize": "Verklig storlek", "SSE.Views.ImageSettings.textRecentlyUsed": "Nyligen använda", "SSE.Views.ImageSettings.textRotate90": "Rotera 90°", "SSE.Views.ImageSettings.textRotation": "Rotation", "SSE.Views.ImageSettings.textSize": "Storlek", "SSE.Views.ImageSettings.textWidth": "Bredd", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "<PERSON><PERSON> eller ändra inte storlek på celler", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternativ text", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Beskrivning", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Den alternativa textbaserad förekomsten av visuell objektinformation som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar för att hjälpa dem att bättre förstå vilken information som finns i bilden, figuren, diagrammet eller tabellen.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Vänd", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Flytta men ändra inte storlek på celler", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ImageSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON> till cell", "SSE.Views.ImageSettingsAdvanced.textTitle": "Bild - avancerade inställningar", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "<PERSON><PERSON> och ändra storlek på celler", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipAbout": "Om", "SSE.Views.LeftMenu.tipChat": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "Fil", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "<PERSON>ö<PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Stavningskontroll", "SSE.Views.LeftMenu.tipSupport": "Feedback & support", "SSE.Views.LeftMenu.txtDeveloper": "UTVECKLARLÄGE", "SSE.Views.LeftMenu.txtLimit": "Begränsad åtkomst", "SSE.Views.LeftMenu.txtTrial": "TESTLÄGE", "SSE.Views.LeftMenu.txtTrialDev": "Testutvecklarläge", "SSE.Views.MacroDialog.textMacro": "Makronamn", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON> makro", "SSE.Views.MainSettingsPrint.okButtonText": "Spara", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Landskap", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "Marginaler", "SSE.Views.MainSettingsPrint.strPortrait": "Portr<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrint": "Skriv ut", "SSE.Views.MainSettingsPrint.strPrintTitles": "Skriv ut titlar", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Ö<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "Anpassad", "SSE.Views.MainSettingsPrint.textCustomOptions": "Anpassade alternativ", "SSE.Views.MainSettingsPrint.textFitCols": "Anpassa alla kolumner på en sida", "SSE.Views.MainSettingsPrint.textFitPage": "Anpassa ark på en sida", "SSE.Views.MainSettingsPrint.textFitRows": "Anpassa alla rader på en sida", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientering", "SSE.Views.MainSettingsPrint.textPageScaling": "Skalning", "SSE.Views.MainSettingsPrint.textPageSize": "Sidstorlek", "SSE.Views.MainSettingsPrint.textPrintGrid": "Skriv rutnät", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Skriv ut rad och kolumn", "SSE.Views.MainSettingsPrint.textRepeat": "Upprepa...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Upprepa kolumner till vänster", "SSE.Views.MainSettingsPrint.textRepeatTop": "Upprepa raderna överst", "SSE.Views.MainSettingsPrint.textSettings": "Inställningar för", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "De befintliga namngivna områdena kan inte redigeras och de nya kan inte skapas <br> just nu eftersom några av dem redigeras.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Definierat namn", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Varning", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Arbetsbok", "SSE.Views.NamedRangeEditDlg.textDataRange": "Dataomr<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textExistName": "FEL! Ett område med samma namn finns redan", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Namnet måste börja med en bokstav eller understrykning och får inte innehålla ogiltiga tecken.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.NamedRangeEditDlg.textIsLocked": "FEL! Detta element redigeras av en annan användare.", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "Namnet du försöker använda refereras redan i cellformler. Använd något annat namn.", "SSE.Views.NamedRangeEditDlg.textScope": "Omfattning", "SSE.Views.NamedRangeEditDlg.textSelectData": "Välj data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "<PERSON><PERSON>a namn", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nytt namn", "SSE.Views.NamedRangePasteDlg.textNames": "Namngivna områden", "SSE.Views.NamedRangePasteDlg.txtTitle": "Klistra in namn", "SSE.Views.NameManagerDlg.closeButtonText": "Stäng", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.lockText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Dataomr<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEdit": "Rediger<PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Inga namngivna intervall har skapats än. <br> <PERSON><PERSON><PERSON> minst ett namngivet intervall och det kommer visas i det här fältet.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON>a", "SSE.Views.NameManagerDlg.textFilterDefNames": "Definierade namn", "SSE.Views.NameManagerDlg.textFilterSheet": "Namn inom kalkylbladet", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON> namn", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Namn inom arbetsboken", "SSE.Views.NameManagerDlg.textNew": "Ny", "SSE.Views.NameManagerDlg.textnoNames": "Inga namngivna intervall som matchar ditt filter kunde hittas.", "SSE.Views.NameManagerDlg.textRanges": "Namngivna områden", "SSE.Views.NameManagerDlg.textScope": "Omfattning", "SSE.Views.NameManagerDlg.textWorkbook": "Arbetsbok", "SSE.Views.NameManagerDlg.tipIsLocked": "Detta element redigeras av en annan användare.", "SSE.Views.NameManagerDlg.txtTitle": "Namnhanterare", "SSE.Views.NameManagerDlg.warnDelete": "<PERSON>r du säker på att du vill ta bort namnet {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "Marginaler", "SSE.Views.PageMarginsDialog.textTop": "Ö<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strLineHeight": "Radavstånd", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Styckets mellanrum", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Visa avancerade inställningar", "SSE.Views.ParagraphSettings.textAt": "På", "SSE.Views.ParagraphSettings.textAtLeast": "Minst", "SSE.Views.ParagraphSettings.textAuto": "Flera", "SSE.Views.ParagraphSettings.textExact": "Exakt", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "De angivna flikarna kommer att visas i det här fältet", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON>a versaler", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Dubbel genomstrykning", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Radavstånd", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Av", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indrag & mellanrum", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Gemener", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Avstånd", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Genomstruken", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Upph<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Ta<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "flera", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Teckenmellanrum", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Standardflik", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exakt", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>n", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Hängande", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ingen)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON> bort", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Ta bort alla", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Specificera", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centrera", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tabbstopp", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Stycke - avancerade inställningar", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "lika", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "Slutar inte med", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "Innehåller inte", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "mellan", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "inte mellan", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "Är inte lika med", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "är st<PERSON><PERSON> än", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "är större än eller lika med", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "är mindre än", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "är mindre än eller lika med", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "b<PERSON><PERSON><PERSON> med", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "B<PERSON><PERSON>jar inte med", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "Slutar med", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Visa objekt för vilka etiketten:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Visa objekt för vilka:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Använda sig av ? att presentera varje enskilt tecken", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Använd * för att presentera alla serier av tecken", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "och", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Etikettfilter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtervärde", "SSE.Views.PivotGroupDialog.textAuto": "auto", "SSE.Views.PivotGroupDialog.textBy": "Av", "SSE.Views.PivotGroupDialog.textDays": "dagar", "SSE.Views.PivotGroupDialog.textEnd": "slutar vid", "SSE.Views.PivotGroupDialog.textError": "Detta fält är numeriskt", "SSE.Views.PivotGroupDialog.textGreaterError": "Slutnumret måste vara större än startnumret", "SSE.Views.PivotGroupDialog.textHour": "timmar", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textQuart": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "Börjar vid", "SSE.Views.PivotGroupDialog.textYear": "<PERSON>r", "SSE.Views.PivotGroupDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textAdvanced": "Visa avancerade inställningar", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON><PERSON> fält", "SSE.Views.PivotSettings.textFilters": "Filter", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Värden", "SSE.Views.PivotSettings.txtAddColumn": "Lägg till i kolumner", "SSE.Views.PivotSettings.txtAddFilter": "Lägg till i filter", "SSE.Views.PivotSettings.txtAddRow": "<PERSON><PERSON><PERSON> till i rader", "SSE.Views.PivotSettings.txtAddValues": "Lägg till i värden", "SSE.Views.PivotSettings.txtFieldSettings": "Fältinställningar", "SSE.Views.PivotSettings.txtMoveBegin": "Flytta till början", "SSE.Views.PivotSettings.txtMoveColumn": "Flytta till kolumner", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON> ner", "SSE.Views.PivotSettings.txtMoveEnd": "Flytta till slutet", "SSE.Views.PivotSettings.txtMoveFilter": "Flytta till filter", "SSE.Views.PivotSettings.txtMoveRow": "Flytta till rader", "SSE.Views.PivotSettings.txtMoveUp": "Flytta upp", "SSE.Views.PivotSettings.txtMoveValues": "Flytta till värden", "SSE.Views.PivotSettings.txtRemove": "Ta bort fält", "SSE.Views.PivotSettingsAdvanced.strLayout": "Namn och layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternativ text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Beskrivning", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Den alternativa textbaserade representation av den visuella informationsobjektet, som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar att hjälpa dem att bättre förstå vilken information som finns i bilden, figur eller, diagram eller en tabell.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Dataomr<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Visa fält i rapportfilterområdet", "SSE.Views.PivotSettingsAdvanced.textDown": "Ner, sedan över", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Totalsumma", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Fältrubriker", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.PivotSettingsAdvanced.textOver": "Över, sedan ner", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Välj data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Visa för kolumner", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Visa fältrubriker för rader och kolumner", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Visa för rader", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivottabell avancerad", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Rapportfilter per kolumn", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Rapportfilter per rad", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.PivotSettingsAdvanced.txtName": "<PERSON><PERSON>", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON> rader", "SSE.Views.PivotTable.capGrandTotals": "Totalsumma", "SSE.Views.PivotTable.capLayout": "Rapportlayout", "SSE.Views.PivotTable.capSubtotals": "Delsumma", "SSE.Views.PivotTable.mniBottomSubtotals": "Visa delsumma efter varje grupp", "SSE.Views.PivotTable.mniInsertBlankLine": "<PERSON><PERSON><PERSON> in blank linje efter varje objekt", "SSE.Views.PivotTable.mniLayoutCompact": "Visa kompakt", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Repetera inte alla objektetiketter", "SSE.Views.PivotTable.mniLayoutOutline": "Visa i dispositionsformulär", "SSE.Views.PivotTable.mniLayoutRepeat": "Upprepa alla objektetiketter", "SSE.Views.PivotTable.mniLayoutTabular": "Visa i tabellform", "SSE.Views.PivotTable.mniNoSubtotals": "Visa inte subtotaler", "SSE.Views.PivotTable.mniOffTotals": "<PERSON><PERSON> för rader och kolumner", "SSE.Views.PivotTable.mniOnColumnsTotals": "<PERSON><PERSON><PERSON> för k<PERSON>", "SSE.Views.PivotTable.mniOnRowsTotals": "<PERSON><PERSON> för rader", "SSE.Views.PivotTable.mniOnTotals": "<PERSON><PERSON> för rader och kolumner", "SSE.Views.PivotTable.mniRemoveBlankLine": "Ta bort tomrad efter varje objekt", "SSE.Views.PivotTable.mniTopSubtotals": "Visa delsumma ovanför grupp", "SSE.Views.PivotTable.textColBanded": "Randiga kolumner", "SSE.Views.PivotTable.textColHeader": "Kolumnrubriker", "SSE.Views.PivotTable.textRowBanded": "Randiga rader", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.tipCreatePivot": "Infoga pivottabell", "SSE.Views.PivotTable.tipGrandTotals": "Visa / dölj <PERSON>", "SSE.Views.PivotTable.tipRefresh": "Uppdatera informationen från datakällan", "SSE.Views.PivotTable.tipSelect": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> pivo<PERSON>", "SSE.Views.PivotTable.tipSubtotals": "Visa / dölj delsumma", "SSE.Views.PivotTable.txtCreate": "Infoga tabell", "SSE.Views.PivotTable.txtPivotTable": "Pi<PERSON><PERSON>bell", "SSE.Views.PivotTable.txtRefresh": "Uppdatera", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.btnDownload": "Spara & ladda ner", "SSE.Views.PrintSettings.btnPrint": "Spara & skriv ut", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "Landskap", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "Marginaler", "SSE.Views.PrintSettings.strPortrait": "Portr<PERSON><PERSON>", "SSE.Views.PrintSettings.strPrint": "Skriv ut", "SSE.Views.PrintSettings.strPrintTitles": "Skriv ut titlar", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Visa", "SSE.Views.PrintSettings.strTop": "Ö<PERSON><PERSON>", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textAllSheets": "Alla ka<PERSON>d", "SSE.Views.PrintSettings.textCurrentSheet": "Aktuellt kalkylblad", "SSE.Views.PrintSettings.textCustom": "Anpassad", "SSE.Views.PrintSettings.textCustomOptions": "Anpassade alternativ", "SSE.Views.PrintSettings.textFitCols": "Anpassa alla kolumner på en sida", "SSE.Views.PrintSettings.textFitPage": "Anpassa ark på en sida", "SSE.Views.PrintSettings.textFitRows": "Anpassa alla rader på en sida", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorera utskrift<PERSON>mrå<PERSON>", "SSE.Views.PrintSettings.textLayout": "Layout", "SSE.Views.PrintSettings.textPageOrientation": "Orientering", "SSE.Views.PrintSettings.textPageScaling": "Skalning", "SSE.Views.PrintSettings.textPageSize": "Sidstorlek", "SSE.Views.PrintSettings.textPrintGrid": "Skriv rutnät", "SSE.Views.PrintSettings.textPrintHeadings": "Skriv ut rad och kolumn", "SSE.Views.PrintSettings.textPrintRange": "Utskriftsområde", "SSE.Views.PrintSettings.textRange": "Omgång", "SSE.Views.PrintSettings.textRepeat": "Upprepa...", "SSE.Views.PrintSettings.textRepeatLeft": "Upprepa kolumner till vänster", "SSE.Views.PrintSettings.textRepeatTop": "Upprepa raderna överst", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Flikinställningar", "SSE.Views.PrintSettings.textShowDetails": "Visa detaljer", "SSE.Views.PrintSettings.textShowGrid": "Visa rutnät", "SSE.Views.PrintSettings.textShowHeadings": "Visa rad och kolumnrubriker", "SSE.Views.PrintSettings.textTitle": "Skrivarinställningar", "SSE.Views.PrintSettings.textTitlePDF": "PDF-inställningar", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON>a kolumnen", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>n", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON> kolumner", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON> reder", "SSE.Views.PrintTitlesDialog.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.PrintTitlesDialog.textLeft": "Upprepa kolumner till vänster", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Upprepa inte", "SSE.Views.PrintTitlesDialog.textRepeat": "Upprepa...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "Skriv ut titlar", "SSE.Views.PrintTitlesDialog.textTop": "Upprepa raderna överst", "SSE.Views.PrintWithPreview.txtActualSize": "Verklig storlek", "SSE.Views.PrintWithPreview.txtAllSheets": "Alla ka<PERSON>d", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "<PERSON><PERSON><PERSON> alla kalkylblad", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Aktuellt kalkylblad", "SSE.Views.PrintWithPreview.txtCustom": "Anpassad", "SSE.Views.PrintWithPreview.txtCustomOptions": "Anpassade alternativ", "SSE.Views.PrintWithPreview.txtEmptyTable": "Det finns inget att skriva ut eftersom tabellen är tom", "SSE.Views.PrintWithPreview.txtFitCols": "Anpassa alla kolumner till en sida", "SSE.Views.PrintWithPreview.txtFitPage": "Anpassa kalkylbladet på en sida", "SSE.Views.PrintWithPreview.txtFitRows": "Anpassa alla rader på en sida", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Rutnät och rubriker", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Inställningar för sidhuvud och sidfot", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorera utskrift<PERSON>mrå<PERSON>", "SSE.Views.PrintWithPreview.txtLandscape": "Liggande", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "Marginaler", "SSE.Views.PrintWithPreview.txtOf": "av {0}", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Sidnumret är felaktigt", "SSE.Views.PrintWithPreview.txtPageOrientation": "Sidans orientering", "SSE.Views.PrintWithPreview.txtPageSize": "Sidans storlek", "SSE.Views.PrintWithPreview.txtPortrait": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrint": "Skriv ut", "SSE.Views.PrintWithPreview.txtPrintGrid": "Skriv ut rutnät", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Skriv ut rad- och kolumnrubriker", "SSE.Views.PrintWithPreview.txtPrintRange": "Utskriftsområde", "SSE.Views.PrintWithPreview.txtPrintTitles": "Skriv ut titlar", "SSE.Views.PrintWithPreview.txtRepeat": "Upprepa...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Upprepa kolumner till vänster", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Upprepa de översta raderna", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Spara", "SSE.Views.PrintWithPreview.txtScaling": "Skalning", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Kalkylbladets inställningar", "SSE.Views.PrintWithPreview.txtSheet": "Kalkylblad: {0}", "SSE.Views.PrintWithPreview.txtTop": "Ö<PERSON><PERSON>", "SSE.Views.ProtectDialog.textExistName": "FEL! Intervall med detta namn finns redan", "SSE.Views.ProtectDialog.textInvalidName": "Intervalltiteln måste börja med en bokstav och får endast innehålla bokstäver, siffror och mellanslag.", "SSE.Views.ProtectDialog.textInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.ProtectDialog.textSelectData": "Välj data", "SSE.Views.ProtectDialog.txtAllow": "Tillåt alla användare av detta kalkylblad att", "SSE.Views.ProtectDialog.txtAutofilter": "Använd autofilter", "SSE.Views.ProtectDialog.txtDelCols": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON> rader", "SSE.Views.ProtectDialog.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.ProtectDialog.txtFormatCells": "<PERSON><PERSON><PERSON> celler", "SSE.Views.ProtectDialog.txtFormatCols": "Formatera kolumner", "SSE.Views.ProtectDialog.txtFormatRows": "<PERSON><PERSON><PERSON> rader", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Bekräftelselösenordet är inte identiskt", "SSE.Views.ProtectDialog.txtInsCols": "In<PERSON>ga kolumner", "SSE.Views.ProtectDialog.txtInsHyper": "Infoga hyperlänk", "SSE.Views.ProtectDialog.txtInsRows": "Infoga rader", "SSE.Views.ProtectDialog.txtObjs": "Redigera objekt", "SSE.Views.ProtectDialog.txtOptional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPassword": "L<PERSON>senord", "SSE.Views.ProtectDialog.txtPivot": "<PERSON><PERSON><PERSON><PERSON> pivottabell och pivotdiagram", "SSE.Views.ProtectDialog.txtProtect": "<PERSON>dda", "SSE.Views.ProtectDialog.txtRange": "<PERSON><PERSON>r<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "Dokumenttitel", "SSE.Views.ProtectDialog.txtRepeat": "Upprepa lösenord", "SSE.Views.ProtectDialog.txtScen": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "<PERSON><PERSON><PERSON><PERSON>er", "SSE.Views.ProtectDialog.txtSelUnLocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "SSE.Views.ProtectDialog.txtSheetDescription": "F<PERSON>rhindrar oönskade ändringar av andra genom att begränsa deras möjlighet till ändringar.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON>dda <PERSON>d", "SSE.Views.ProtectDialog.txtSort": "Sortera", "SSE.Views.ProtectDialog.txtWarning": "Varning! Om du glömmer lösenordet kan det inte återskapas. Vänligen förvara det på en säker plats.", "SSE.Views.ProtectDialog.txtWBDescription": "<PERSON><PERSON><PERSON> att fö<PERSON><PERSON><PERSON> andra användare att se dolda arbets<PERSON>öcker, l<PERSON>gg<PERSON> till, fly<PERSON>, rader<PERSON>, d<PERSON><PERSON><PERSON> eller döpa om arbetsböcker så kan du skydda deras struktur med ett lösenord.", "SSE.Views.ProtectDialog.txtWBTitle": "Skydda arbetsbokens struktur", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEdit": "Rediger<PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Inga intervaller är tillgängliga för redigering.", "SSE.Views.ProtectRangesDlg.textNew": "Ny", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON>dda <PERSON>d", "SSE.Views.ProtectRangesDlg.textPwd": "L<PERSON>senord", "SSE.Views.ProtectRangesDlg.textRange": "<PERSON><PERSON>r<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Området låses upp med lösenord när kalkylbladet är skyddat (detta gäller bara för lå<PERSON> celler)", "SSE.Views.ProtectRangesDlg.textTitle": "Dokumenttitel", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Detta element redigeras av en annan användare.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Redigera intervall", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nytt intervall", "SSE.Views.ProtectRangesDlg.txtNo": "Nov", "SSE.Views.ProtectRangesDlg.txtTitle": "<PERSON><PERSON>t användare att ändra intervaller", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON>a", "SSE.Views.ProtectRangesDlg.warnDelete": "<PERSON>r du säker på att du vill ta bort namnet {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Om du vill ta bort dubbletter av värden väljer du en eller flera kolumner som innehåller dubbletter.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Mina data har rubriker", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON> allt", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "<PERSON> bort dubbletter", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON><PERSON> celler", "SSE.Views.RightMenu.txtChartSettings": "Diagraminställningar", "SSE.Views.RightMenu.txtImageSettings": "Bildinställningar", "SSE.Views.RightMenu.txtParagraphSettings": "Styckets inställningar", "SSE.Views.RightMenu.txtPivotSettings": "Inställningar pivottabell", "SSE.Views.RightMenu.txtSettings": "Allmänna inställningar", "SSE.Views.RightMenu.txtShapeSettings": "Form inställningar", "SSE.Views.RightMenu.txtSignatureSettings": "Signaturinställningar", "SSE.Views.RightMenu.txtSlicerSettings": "Slicer inställningar", "SSE.Views.RightMenu.txtSparklineSettings": "Sparkline inställningar", "SSE.Views.RightMenu.txtTableSettings": "Tabell inställningar", "SSE.Views.RightMenu.txtTextArtSettings": "Text Art inställningar", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "Ogiltigt värde.", "SSE.Views.ScaleDialog.textFewPages": "sidor", "SSE.Views.ScaleDialog.textFitTo": "Anpassa till", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "sidor", "SSE.Views.ScaleDialog.textOnePage": "sida", "SSE.Views.ScaleDialog.textScaleTo": "Skala till", "SSE.Views.ScaleDialog.textTitle": "Inställning skalning", "SSE.Views.ScaleDialog.textWidth": "Bredd", "SSE.Views.SetValueDialog.txtMaxText": "Maxvärde för fältet är {0}", "SSE.Views.SetValueDialog.txtMinText": "Minv<PERSON>rde för fältet är {0}", "SSE.Views.ShapeSettings.strBackground": "Bakgrundsfärg", "SSE.Views.ShapeSettings.strChange": "Ändra autoform", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Förgrundsfärg", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "Visa skugga", "SSE.Views.ShapeSettings.strSize": "Storlek", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Opacitet", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdvanced": "Visa avancerade inställningar", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett värde mellan 0 och 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Färgfyllnad", "SSE.Views.ShapeSettings.textDirection": "Rik<PERSON>ning", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON> m<PERSON>", "SSE.Views.ShapeSettings.textFlip": "Vänd", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON><PERSON> fil", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "SSE.Views.ShapeSettings.textFromUrl": "Från URL", "SSE.Views.ShapeSettings.textGradient": "Triangulära punkter", "SSE.Views.ShapeSettings.textGradientFill": "F<PERSON><PERSON>ing", "SSE.Views.ShapeSettings.textHint270": "Rotera 90° moturs", "SSE.Views.ShapeSettings.textHint90": "Rotera 90° medsols", "SSE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Views.ShapeSettings.textHintFlipV": "Vänd vertikalt", "SSE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON><PERSON> eller textur", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoFill": "Ingen fyllning", "SSE.Views.ShapeSettings.textOriginalSize": "Originalstorlek", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Position", "SSE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRecentlyUsed": "Nyligen använda", "SSE.Views.ShapeSettings.textRotate90": "Rotera 90°", "SSE.Views.ShapeSettings.textRotation": "Rotation", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> bild", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "Stil", "SSE.Views.ShapeSettings.textTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Lägg till lutningspunkt", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Ta bort lutningspunkten", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papper", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Mörkt tyg", "SSE.Views.ShapeSettings.txtGrain": "Gryn", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> papper", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "Ingen rad", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "Trä", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Text padding", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "<PERSON><PERSON> eller ändra inte storlek på celler", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternativ text", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Beskrivning", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Den alternativa textbaserad förekomsten av visuell objektinformation som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar för att hjälpa dem att bättre förstå vilken information som finns i bilden, figuren, diagrammet eller tabellen.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Anpassa automatiskt", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Startutseende", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Startutseende", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Typ av skiftläge", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Slutstorlek", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Avslutningsstil", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Vänd", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Typ av sammanfogning", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Konstanta proportioner", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Linjestil", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Flytta men ändra inte storlek på celler", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "<PERSON><PERSON><PERSON> texten rinna <PERSON>ver", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON> storlek så den passar texten", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ShapeSettingsAdvanced.textRound": "Rund", "SSE.Views.ShapeSettingsAdvanced.textSize": "Storlek", "SSE.Views.ShapeSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON> till cell", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON> mellan kolumner", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Fyrkant", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Form - avancerade inställningar", "SSE.Views.ShapeSettingsAdvanced.textTop": "Ö<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "<PERSON><PERSON> och ändra storlek på celler", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "<PERSON><PERSON><PERSON> & pilar", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Bredd", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Varning", "SSE.Views.SignatureSettings.strDelete": "Ta bort signatur", "SSE.Views.SignatureSettings.strDetails": "Signaturdetaljer", "SSE.Views.SignatureSettings.strInvalid": "Felaktiga signaturer", "SSE.Views.SignatureSettings.strRequested": "Beg<PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Skapa signatur", "SSE.Views.SignatureSettings.strSign": "Underteckna", "SSE.Views.SignatureSettings.strSignature": "Signatur", "SSE.Views.SignatureSettings.strSigner": "Undertecknare", "SSE.Views.SignatureSettings.strValid": "Giltiga signaturer", "SSE.Views.SignatureSettings.txtContinueEditing": "Redigera ändå", "SSE.Views.SignatureSettings.txtEditWarning": "Redigering tar bort signaturerna från kalkylbladet.<br><PERSON>r du säker på att du vill fortsätta?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Vill du ta bort den här signaturen? <br> <PERSON>n inte å<PERSON>.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "<PERSON><PERSON>blad måste signeras.", "SSE.Views.SignatureSettings.txtSigned": "Giltiga signaturer har lagts till i kalkylbladet. Kalkylarket är skyddat från redigering.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Vissa av de digitala signaturerna i kalkylarket är ogiltiga eller kunde inte verifieras. Kalkylarket är skyddat från redigering.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Infoga Slicers", "SSE.Views.SlicerSettings.strHideNoData": "Dölj objekt utan data", "SSE.Views.SlicerSettings.strIndNoData": "Ange visuellt objekt utan data", "SSE.Views.SlicerSettings.strShowDel": "Visa objekt som raderats från datakällan", "SSE.Views.SlicerSettings.strShowNoData": "Visa objekt utan data senast", "SSE.Views.SlicerSettings.strSorting": "Sortering och filter", "SSE.Views.SlicerSettings.textAdvanced": "Visa avancerade inställningar", "SSE.Views.SlicerSettings.textAsc": "Stigande", "SSE.Views.SlicerSettings.textAZ": "A till Z", "SSE.Views.SlicerSettings.textButtons": "Knappar", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textKeepRatio": "Konstanta proportioner", "SSE.Views.SlicerSettings.textLargeSmall": "största till minsta", "SSE.Views.SlicerSettings.textLock": "Inaktivera storleksändring eller flyttning", "SSE.Views.SlicerSettings.textNewOld": "nyast till äldst", "SSE.Views.SlicerSettings.textOldNew": "äldsta till nyaste", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "Storlek", "SSE.Views.SlicerSettings.textSmallLarge": "minsta till största", "SSE.Views.SlicerSettings.textStyle": "Stil", "SSE.Views.SlicerSettings.textVert": "Vertikal", "SSE.Views.SlicerSettings.textWidth": "Bredd", "SSE.Views.SlicerSettings.textZA": "Z till A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Knappar", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Dölj objekt utan data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Ange visuellt objekt utan data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Visa objekt som raderats från datakällan", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Visa rubrik", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Visa objekt utan data senast", "SSE.Views.SlicerSettingsAdvanced.strSize": "Storlek", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sortering och filter", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stil", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "<PERSON>il och storlek", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Bredd", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "<PERSON><PERSON> eller ändra inte storlek på celler", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternativ text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Beskrivning", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Den alternativa textbaserade representation av den visuella informationsobjektet, som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar att hjälpa dem att bättre förstå vilken information som finns i bilden, figur eller, diagram eller en tabell.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Stigande", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A till Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Namn som ska användas i formler", "SSE.Views.SlicerSettingsAdvanced.textHeader": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Konstanta proportioner", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "största till minsta", "SSE.Views.SlicerSettingsAdvanced.textName": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "nyast till äldst", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "äldsta till nyaste", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Flytta men ändra inte storlek på celler", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "minsta till största", "SSE.Views.SlicerSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON> till cell", "SSE.Views.SlicerSettingsAdvanced.textSort": "Sortera", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON><PERSON><PERSON><PERSON> namn", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - Avancerade inställningar", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "<PERSON><PERSON> och ändra storlek på celler", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z till A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.SortDialog.errorEmpty": "Alla sorteringskriterier måste ha en kolumn eller rad specificerad.", "SSE.Views.SortDialog.errorMoreOneCol": "Mer än en kolumn är vald.", "SSE.Views.SortDialog.errorMoreOneRow": "Mer än en rad är vald.", "SSE.Views.SortDialog.errorNotOriginalCol": "Den valda kolumnen finns inte i det ursprungliga valda intervallet.", "SSE.Views.SortDialog.errorNotOriginalRow": "Raden du valt finns inte i det ursprungliga valda intervallet.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 sorteras efter samma färg mer än en gång. <br> Ta bort duplikatsorteringskriterierna och försök igen.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 sorteras efter värden mer än en gång. <br> <PERSON><PERSON>a duplikatsorteringskriterierna och försök igen.", "SSE.Views.SortDialog.textAdd": "Lägg till nivå", "SSE.Views.SortDialog.textAsc": "Stigande", "SSE.Views.SortDialog.textAuto": "Automatisk", "SSE.Views.SortDialog.textAZ": "A till Z", "SSE.Views.SortDialog.textBelow": "Under", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "<PERSON><PERSON> ner nivå", "SSE.Views.SortDialog.textFontColor": "Teckensnittsfärg", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON> kolumner...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON> rader...)", "SSE.Views.SortDialog.textNone": "ingen", "SSE.Views.SortDialog.textOptions": "Alternativ", "SSE.Views.SortDialog.textOrder": "Ordning", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Sortering på", "SSE.Views.SortDialog.textSortBy": "Sortera efter", "SSE.Views.SortDialog.textThenBy": "Sedan av", "SSE.Views.SortDialog.textTop": "Ö<PERSON><PERSON>", "SSE.Views.SortDialog.textUp": "Flytta upp nivå", "SSE.Views.SortDialog.textValues": "Värden", "SSE.Views.SortDialog.textZA": "Z till A", "SSE.Views.SortDialog.txtInvalidRange": "Ogiltigt cell-område", "SSE.Views.SortDialog.txtTitle": "Sortera", "SSE.Views.SortFilterDialog.textAsc": "Stigande (A till Z ) ", "SSE.Views.SortFilterDialog.textDesc": "Omvänd sortering (Z - A) med", "SSE.Views.SortFilterDialog.txtTitle": "Sortera", "SSE.Views.SortOptionsDialog.textCase": "Skiftlägeskänslig", "SSE.Views.SortOptionsDialog.textHeaders": "Mina data har rubriker", "SSE.Views.SortOptionsDialog.textLeftRight": "Sortera från vänster till höger", "SSE.Views.SortOptionsDialog.textOrientation": "Orientering", "SSE.Views.SortOptionsDialog.textTitle": "Sorteringsalternativ", "SSE.Views.SortOptionsDialog.textTopBottom": "Sortera från topp till botten", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON> till", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON>a", "SSE.Views.SpecialPasteDialog.textBlanks": "<PERSON><PERSON> över tomma", "SSE.Views.SpecialPasteDialog.textColWidth": "Kolumnbredd", "SSE.Views.SpecialPasteDialog.textComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "Dela upp", "SSE.Views.SpecialPasteDialog.textFFormat": "Formler och formatering", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formler och nummerformat", "SSE.Views.SpecialPasteDialog.textFormats": "Format", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "<PERSON>ler och kolumnbredder", "SSE.Views.SpecialPasteDialog.textMult": "Multiplicera", "SSE.Views.SpecialPasteDialog.textNone": "ingen", "SSE.Views.SpecialPasteDialog.textOperation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textPaste": "Klistra in", "SSE.Views.SpecialPasteDialog.textSub": "Subtrahera", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON><PERSON> in special", "SSE.Views.SpecialPasteDialog.textTranspose": "Transponera", "SSE.Views.SpecialPasteDialog.textValues": "Värden", "SSE.Views.SpecialPasteDialog.textVFormat": "Värden och formatering", "SSE.Views.SpecialPasteDialog.textVNFormat": "Värden & nummerformat", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON>t u<PERSON> ramar", "SSE.Views.Spellcheck.noSuggestions": "Inga stavningsförslag", "SSE.Views.Spellcheck.textChange": "<PERSON><PERSON>", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON> alla", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "Ignorera alla", "SSE.Views.Spellcheck.txtAddToDictionary": "Lägg till i ordlista", "SSE.Views.Spellcheck.txtClosePanel": "Stäng stavning", "SSE.Views.Spellcheck.txtComplete": "Stavningskontroll har slutförts", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Rättstavningsspråk", "SSE.Views.Spellcheck.txtNextTip": "Gå till nästa ord", "SSE.Views.Spellcheck.txtSpelling": "Rättstavning", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON><PERSON> till slutet)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON> till slutet)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Klistra in före kalkylarket", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Flytta före ark", "SSE.Views.Statusbar.filteredRecordsText": "{0} av {1} poster filtrerade", "SSE.Views.Statusbar.filteredText": "Filterläge", "SSE.Views.Statusbar.itemAverage": "Genomsnitt", "SSE.Views.Statusbar.itemCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemCount": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "G<PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Infoga", "SSE.Views.Statusbar.itemMaximum": "Max", "SSE.Views.Statusbar.itemMinimum": "Minst", "SSE.Views.Statusbar.itemMove": "Flytta", "SSE.Views.Statusbar.itemProtect": "<PERSON>dda", "SSE.Views.Statusbar.itemRename": "Döp om", "SSE.Views.Statusbar.itemStatus": "Sparar status", "SSE.Views.Statusbar.itemSum": "Summa", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON><PERSON> upp", "SSE.Views.Statusbar.RenameDialog.errNameExists": "En arbetsbok med samma namn finns redan.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Namnet på ett blad kan inte innehålla följande tecken:: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Fliknamn", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON> alla blad", "SSE.Views.Statusbar.sheetIndexText": "Kalkylblad {0} av {1}", "SSE.Views.Statusbar.textAverage": "Genomsnitt", "SSE.Views.Statusbar.textCount": "R<PERSON>k<PERSON>", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Lägg till ny egen färg", "SSE.Views.Statusbar.textNoColor": "Ingen färg", "SSE.Views.Statusbar.textSum": "Summa", "SSE.Views.Statusbar.tipAddTab": "Lägg till kalkylblad", "SSE.Views.Statusbar.tipFirst": "Scrolla till första bladet", "SSE.Views.Statusbar.tipLast": "Scrolla till sista bladet", "SSE.Views.Statusbar.tipListOfSheets": "Lista med kalkylblad", "SSE.Views.Statusbar.tipNext": "Bläddra listan till höger", "SSE.Views.Statusbar.tipPrev": "Bläddra listan till vänster", "SSE.Views.Statusbar.tipZoomFactor": "Zooma", "SSE.Views.Statusbar.tipZoomIn": "Zooma in", "SSE.Views.Statusbar.tipZoomOut": "Zooma ut", "SSE.Views.Statusbar.ungroupSheets": "Avgruppera kalkylblad", "SSE.Views.Statusbar.zoomText": "Zooma {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Åtgärden kunde inte göras för det valda cellområdet. <br> <PERSON><PERSON><PERSON><PERSON> ett enhetligt dataområde som skiljer sig från det befintliga och försök igen.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Åtgärden kunde inte slutföras för det valda cellområdet. <br><PERSON><PERSON><PERSON><PERSON> ett intervall så att den första tabellraden var i samma rad<br>och den resulterande tabellen överlappade den aktuella.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Åtgärden kunde inte slutföras för det valda cellområdet.<br><PERSON><PERSON><PERSON><PERSON> ett intervall som inte innehåller andra tabeller.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Multi-cell array formler är inte tillåtna i tabeller", "SSE.Views.TableOptionsDialog.txtEmpty": "Detta fält är obligatoriskt", "SSE.Views.TableOptionsDialog.txtFormat": "Skapa tabell", "SSE.Views.TableOptionsDialog.txtInvalidRange": "FEL! Ogiltigt cellintervall", "SSE.Views.TableOptionsDialog.txtNote": "Rubrikerna måste förbli i samma rad och det resulterande tabellintervallet måste överlappa det ursprungliga tabellintervallet.", "SSE.Views.TableOptionsDialog.txtTitle": "Titel", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON> kol<PERSON>n", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> rad", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON>a tabell", "SSE.Views.TableSettings.insertColumnLeftText": "Infoga kolumn till vänster", "SSE.Views.TableSettings.insertColumnRightText": "Infoga kolumn till höger", "SSE.Views.TableSettings.insertRowAboveText": "Infoga rad ovanför", "SSE.Views.TableSettings.insertRowBelowText": "Infoga rad nedanför", "SSE.Views.TableSettings.notcriticalErrorTitle": "Varning", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> hel kol<PERSON>n", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectRowText": "Vä<PERSON>j rad", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabell", "SSE.Views.TableSettings.textActions": "Tabellåtgärder", "SSE.Views.TableSettings.textAdvanced": "Visa avancerade inställningar", "SSE.Views.TableSettings.textBanded": "Banded", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Konvertera till intervall", "SSE.Views.TableSettings.textEdit": "Ra<PERSON> & kolumner", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON>ga mallar", "SSE.Views.TableSettings.textExistName": "FEL! Ett intervall med ett sådant namn finns redan", "SSE.Views.TableSettings.textFilter": "<PERSON><PERSON>k<PERSON><PERSON>", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "FEL! Ogiltigt tabellnamn", "SSE.Views.TableSettings.textIsLocked": "Detta element redigeras av en annan användare.", "SSE.Views.TableSettings.textLast": "Senaste", "SSE.Views.TableSettings.textLongOperation": "Lång operation", "SSE.Views.TableSettings.textPivot": "Infoga pivottabell", "SSE.Views.TableSettings.textRemDuplicates": "<PERSON> bort dubbletter", "SSE.Views.TableSettings.textReservedName": "Namnet du försöker använda refereras redan i cellformler. Använd något annat namn.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON> storlek på tabell", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Välj data", "SSE.Views.TableSettings.textSlicer": "Infoga Slicer", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON> namn", "SSE.Views.TableSettings.textTemplate": "Välj från mall", "SSE.Views.TableSettings.textTotal": "Totalt", "SSE.Views.TableSettings.warnLongOperation": "Den operation du ska utföra kan ta ganska lång tid att slutföra. <br> Är du säker på att du vill fortsätta?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternativ text", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Beskrivning", "SSE.Views.TableSettingsAdvanced.textAltTip": "Den alternativa textbaserade representation av den visuella informationsobjektet, som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar att hjälpa dem att bättre förstå vilken information som finns i bilden, figur eller, diagram eller en tabell.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabell - Avancerade inställningar", "SSE.Views.TextArtSettings.strBackground": "Bakgrundsfärg", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "Förgrundsfärg", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "Storlek", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Opacitet", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett värde mellan 0 och 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Färgfyllnad", "SSE.Views.TextArtSettings.textDirection": "Rik<PERSON>ning", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON> m<PERSON>", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON><PERSON> fil", "SSE.Views.TextArtSettings.textFromUrl": "Från URL", "SSE.Views.TextArtSettings.textGradient": "Triangulära punkter", "SSE.Views.TextArtSettings.textGradientFill": "F<PERSON><PERSON>ing", "SSE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON><PERSON> eller textur", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Ingen fyllning", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Position", "SSE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "Stil", "SSE.Views.TextArtSettings.textTemplate": "Mall", "SSE.Views.TextArtSettings.textTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "Omvandla", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Lägg till lutningspunkt", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Ta bort lutningspunkten", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papper", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Mörkt tyg", "SSE.Views.TextArtSettings.txtGrain": "Gryn", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> papper", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "Ingen rad", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "Trä", "SSE.Views.Toolbar.capBtnAddComment": "Lägg till kommentar", "SSE.Views.Toolbar.capBtnColorSchemas": "Färgschema", "SSE.Views.Toolbar.capBtnComment": "Kommentar", "SSE.Views.Toolbar.capBtnInsHeader": "Sidhuvud & Sidfot", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "Marginaler", "SSE.Views.Toolbar.capBtnPageOrient": "Orientering", "SSE.Views.Toolbar.capBtnPageSize": "Storlek", "SSE.Views.Toolbar.capBtnPrintArea": "Utskriftsområde", "SSE.Views.Toolbar.capBtnPrintTitles": "Skriv ut titlar", "SSE.Views.Toolbar.capBtnScale": "Anpassad skalning", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "<PERSON><PERSON> b<PERSON>", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON> f<PERSON>", "SSE.Views.Toolbar.capImgGroup": "Grupp", "SSE.Views.Toolbar.capInsertChart": "Diagram", "SSE.Views.Toolbar.capInsertEquation": "Ekvation", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlänk", "SSE.Views.Toolbar.capInsertImage": "Bild", "SSE.Views.Toolbar.capInsertShape": "Form", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromFile": "Bild fr<PERSON><PERSON> fil", "SSE.Views.Toolbar.mniImageFromStorage": "<PERSON>ild fr<PERSON><PERSON> lagring", "SSE.Views.Toolbar.mniImageFromUrl": "Bild från URL", "SSE.Views.Toolbar.textAddPrintArea": "Lägg till utskriftsområde", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON> botten", "SSE.Views.Toolbar.textAlignCenter": "Centrera", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "Vänsterjustera", "SSE.Views.Toolbar.textAlignMiddle": "Centrera", "SSE.Views.Toolbar.textAlignRight": "Högerjustera", "SSE.Views.Toolbar.textAlignTop": "Justera till toppen", "SSE.Views.Toolbar.textAllBorders": "<PERSON>a ramar", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatisk", "SSE.Views.Toolbar.textBold": "Fet", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "Ramutseende", "SSE.Views.Toolbar.textBottom": "Botten:", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>rt<PERSON>", "SSE.Views.Toolbar.textClearPrintArea": "Nollställ utskriftsområde", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON>", "SSE.Views.Toolbar.textClockwise": "Vinkel medurs", "SSE.Views.Toolbar.textColorScales": "Färgskalor", "SSE.Views.Toolbar.textCounterCw": "Vinkel moturs", "SSE.Views.Toolbar.textDataBars": "Data staplar", "SSE.Views.Toolbar.textDelLeft": "Flytta celler åt vänster", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON> cell<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDiagDownBorder": "<PERSON><PERSON><PERSON> nedre ram", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonal upp ram", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON> r<PERSON>n", "SSE.Views.Toolbar.textFewPages": "sidor", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON>ll text", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON> cell<PERSON>", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInsRight": "Flytta celler <PERSON><PERSON>", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "objekt", "SSE.Views.Toolbar.textLandscape": "Landskap", "SSE.Views.Toolbar.textLeft": "Vänster:", "SSE.Views.Toolbar.textLeftBorders": "<PERSON>", "SSE.Views.Toolbar.textManageRule": "<PERSON><PERSON> regler", "SSE.Views.Toolbar.textManyPages": "sidor", "SSE.Views.Toolbar.textMarginsLast": "Senast anpassade", "SSE.Views.Toolbar.textMarginsNarrow": "Smal", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreFormats": "Flera format", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON> sidor", "SSE.Views.Toolbar.textNewColor": "Lägg till ny egen färg", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON> ramar", "SSE.Views.Toolbar.textOnePage": "sida", "SSE.Views.Toolbar.textOutBorders": "<PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "Anpassade marginaler", "SSE.Views.Toolbar.textPortrait": "Portr<PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "Skriv ut", "SSE.Views.Toolbar.textPrintGridlines": "Skriv ut rutnät", "SSE.Views.Toolbar.textPrintHeadings": "Skriv ut rubriker", "SSE.Views.Toolbar.textPrintOptions": "Skrivarinställningar", "SSE.Views.Toolbar.textRight": "Höger:", "SSE.Views.Toolbar.textRightBorders": "<PERSON>", "SSE.Views.Toolbar.textRotateDown": "Rotera text nedåt", "SSE.Views.Toolbar.textRotateUp": "Rotera text uppåt", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Anpassad", "SSE.Views.Toolbar.textSelection": "Från aktuell markering", "SSE.Views.Toolbar.textSetPrintArea": "<PERSON><PERSON> u<PERSON>r<PERSON>", "SSE.Views.Toolbar.textStrikeout": "Genomstruken", "SSE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubSuperscript": "Nedsänkt / upphöjt", "SSE.Views.Toolbar.textSuperscript": "Upph<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabCollaboration": "Samarbeta", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabFile": "Arkiv", "SSE.Views.Toolbar.textTabFormula": "Formel", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "Infoga", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "Visa", "SSE.Views.Toolbar.textThisPivot": "<PERSON><PERSON><PERSON> denna pivot", "SSE.Views.Toolbar.textThisSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textThisTable": "<PERSON><PERSON><PERSON> denna tabell", "SSE.Views.Toolbar.textTop": "Överst:", "SSE.Views.Toolbar.textTopBorders": "<PERSON>", "SSE.Views.Toolbar.textUnderline": "Understrykning", "SSE.Views.Toolbar.textVertical": "Vertikal text", "SSE.Views.Toolbar.textWidth": "Bredd", "SSE.Views.Toolbar.textZoom": "Zooma", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON> botten", "SSE.Views.Toolbar.tipAlignCenter": "Centrera", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "Vänsterjustera", "SSE.Views.Toolbar.tipAlignMiddle": "Centrera", "SSE.Views.Toolbar.tipAlignRight": "Högerjustera", "SSE.Views.Toolbar.tipAlignTop": "Justera till toppen", "SSE.Views.Toolbar.tipAutofilter": "Sortering och filter", "SSE.Views.Toolbar.tipBack": "Tillbaka", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Cellstil", "SSE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON> diagramtyp", "SSE.Views.Toolbar.tipClearStyle": "Ren<PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCondFormat": "Villkorlig formatering", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> stil", "SSE.Views.Toolbar.tipCut": "<PERSON><PERSON><PERSON> ut", "SSE.Views.Toolbar.tipDecDecimal": "Minska decimal", "SSE.Views.Toolbar.tipDecFont": "Minska typsnittstorlek", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON> celler", "SSE.Views.Toolbar.tipDigStyleAccounting": "Redovisningsstil", "SSE.Views.Toolbar.tipDigStyleCurrency": "Valutastil", "SSE.Views.Toolbar.tipDigStylePercent": "Procent-stil", "SSE.Views.Toolbar.tipEditChart": "Redigera diagram", "SSE.Views.Toolbar.tipEditChartData": "Välj data", "SSE.Views.Toolbar.tipEditChartType": "<PERSON><PERSON> diagramtyp", "SSE.Views.Toolbar.tipEditHeader": "<PERSON><PERSON> sidh<PERSON>vud och fot", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "Font", "SSE.Views.Toolbar.tipFontSize": "Fontstorlek", "SSE.Views.Toolbar.tipHAlighOle": "<PERSON><PERSON><PERSON><PERSON> justering", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON> obje<PERSON>", "SSE.Views.Toolbar.tipImgGroup": "Gruppobjekt", "SSE.Views.Toolbar.tipIncDecimal": "Öka decimal", "SSE.Views.Toolbar.tipIncFont": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertChart": "Infoga diagram", "SSE.Views.Toolbar.tipInsertChartSpark": "Infoga diagram", "SSE.Views.Toolbar.tipInsertEquation": "Infoga ekvation", "SSE.Views.Toolbar.tipInsertHyperlink": "Lägg till hyperlänk", "SSE.Views.Toolbar.tipInsertImage": "Infoga bild", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON>ga celler", "SSE.Views.Toolbar.tipInsertShape": "Infoga autoform", "SSE.Views.Toolbar.tipInsertSlicer": "Infoga Slicer", "SSE.Views.Toolbar.tipInsertSpark": "Infoga Sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Infoga symbol", "SSE.Views.Toolbar.tipInsertTable": "Infoga tabell", "SSE.Views.Toolbar.tipInsertText": "Infoga textruta", "SSE.Views.Toolbar.tipInsertTextart": "Infoga Text Art", "SSE.Views.Toolbar.tipMerge": "Slå samman och centrera", "SSE.Views.Toolbar.tipNone": "Inga", "SSE.Views.Toolbar.tipNumFormat": "Sifferformat", "SSE.Views.Toolbar.tipPageMargins": "Sidmarginaler", "SSE.Views.Toolbar.tipPageOrient": "Orientering", "SSE.Views.Toolbar.tipPageSize": "Sidstorlek", "SSE.Views.Toolbar.tipPaste": "Klistra in", "SSE.Views.Toolbar.tipPrColor": "Fyllnadsfärg", "SSE.Views.Toolbar.tipPrint": "Skriv ut", "SSE.Views.Toolbar.tipPrintArea": "Utskriftsområde", "SSE.Views.Toolbar.tipPrintTitles": "Skriv ut titlar", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON> om", "SSE.Views.Toolbar.tipSave": "Spara", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON>ra <PERSON> för andra användare ska se dem.", "SSE.Views.Toolbar.tipScale": "Anpassad skalning", "SSE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON> allt", "SSE.Views.Toolbar.tipSendBackward": "<PERSON><PERSON> b<PERSON>", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON> f<PERSON>", "SSE.Views.Toolbar.tipSynchronize": "Dokumentet har ändrats av en annan användare. Klicka för att spara dina ändringar och ladda uppdateringarna.", "SSE.Views.Toolbar.tipTextOrientation": "Orientering", "SSE.Views.Toolbar.tipUndo": "Å<PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Vertikal anpassning", "SSE.Views.Toolbar.tipWrap": "Radbryt text", "SSE.Views.Toolbar.txtAccounting": "Redovisning", "SSE.Views.Toolbar.txtAdditional": "Extra", "SSE.Views.Toolbar.txtAscending": "Stigande", "SSE.Views.Toolbar.txtAutosumTip": "Summering", "SSE.Views.Toolbar.txtClearAll": "<PERSON>a", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Rensa filter", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Funktion", "SSE.Views.Toolbar.txtClearHyper": "Hyperlänkar", "SSE.Views.Toolbar.txtClearText": "Text", "SSE.Views.Toolbar.txtCurrency": "Valuta", "SSE.Views.Toolbar.txtCustom": "Anpassad", "SSE.Views.Toolbar.txtDate": "Datum", "SSE.Views.Toolbar.txtDateTime": "Datum & Tid", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Dollar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponentiell", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Infoga funktion", "SSE.Views.Toolbar.txtFraction": "Fraktion", "SSE.Views.Toolbar.txtFranc": "CHF Swiss franc", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Namnhanterare", "SSE.Views.Toolbar.txtMergeAcross": "Sammanfoga över", "SSE.Views.Toolbar.txtMergeCells": "Slå ihop celler", "SSE.Views.Toolbar.txtMergeCenter": "Slå ihop & centrera", "SSE.Views.Toolbar.txtNamedRange": "Namngivna områden", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON> namn", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON> ramar", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Klistra in namn", "SSE.Views.Toolbar.txtPercentage": "Procentsats", "SSE.Views.Toolbar.txtPound": "£ pund", "SSE.Views.Toolbar.txtRouble": "₽ Rubel", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "Median", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Välmående", "SSE.Views.Toolbar.txtScheme14": "Oriel", "SSE.Views.Toolbar.txtScheme15": "Ursprung", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Solstånd", "SSE.Views.Toolbar.txtScheme18": "Teknik", "SSE.Views.Toolbar.txtScheme19": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme2": "Gråskala", "SSE.Views.Toolbar.txtScheme20": "Urban", "SSE.Views.Toolbar.txtScheme21": "Fart", "SSE.Views.Toolbar.txtScheme22": "New Office", "SSE.Views.Toolbar.txtScheme3": "Apex", "SSE.Views.Toolbar.txtScheme4": "Aspekt", "SSE.Views.Toolbar.txtScheme5": "Medborgerlig", "SSE.Views.Toolbar.txtScheme6": "Öppen plats", "SSE.Views.Toolbar.txtScheme7": "Rimlighet", "SSE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme9": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "Vetenskaplig", "SSE.Views.Toolbar.txtSearch": "<PERSON>ö<PERSON>", "SSE.Views.Toolbar.txtSort": "Sortera", "SSE.Views.Toolbar.txtSortAZ": "Sortera stigande", "SSE.Views.Toolbar.txtSortZA": "So<PERSON>a fallande", "SSE.Views.Toolbar.txtSpecial": "Special", "SSE.Views.Toolbar.txtTableTemplate": "Formatera som tabellmall", "SSE.Views.Toolbar.txtText": "Text", "SSE.Views.Toolbar.txtTime": "Tid", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON> celler", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "Visa", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "av", "SSE.Views.Top10FilterDialog.txtItems": "Element", "SSE.Views.Top10FilterDialog.txtPercent": "Procent", "SSE.Views.Top10FilterDialog.txtSum": "Summa", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 autofilter", "SSE.Views.Top10FilterDialog.txtTop": "Ö<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filter", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Inställning datafält", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Genomsnitt", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Basfält", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "<PERSON>s objekt", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 av %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "R<PERSON>k<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON> nummer", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Anpassat namn", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Skillnaden från", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Ingen beräkning", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Procent av", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Procent av kolumn", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Procent av total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Procent av rad", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Running Total In", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Visa värden som", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Källnamn:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Summa", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Sammanfatta värdefältet med", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Stäng", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.lockText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDuplicate": "Du<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "<PERSON>ga vyer har skapats än.", "SSE.Views.ViewManagerDlg.textGoTo": "Gå till vy", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON> na<PERSON>n (max 128 tecken).", "SSE.Views.ViewManagerDlg.textNew": "Ny", "SSE.Views.ViewManagerDlg.textRename": "Döp om", "SSE.Views.ViewManagerDlg.textRenameError": "Visningsnamnet får inte vara tomt.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Döp om vy", "SSE.Views.ViewManagerDlg.textViews": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.tipIsLocked": "Detta element redigeras av en annan användare.", "SSE.Views.ViewManagerDlg.txtTitle": "Vyhanterare", "SSE.Views.ViewManagerDlg.warnDeleteView": "Du försöker ta bort den för närvarande aktiverade vyn %1. <br> <PERSON>äng den här vyn och ta bort den?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON><PERSON> paneler", "SSE.Views.ViewTab.capBtnSheetView": "<PERSON><PERSON>", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Visa alltid verktygsfältet", "SSE.Views.ViewTab.textClose": "Stäng", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Kombinera kalkylblad och statusfält", "SSE.Views.ViewTab.textCreate": "Ny", "SSE.Views.ViewTab.textDefault": "Standard", "SSE.Views.ViewTab.textFormula": "Formelfält", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON><PERSON> först<PERSON> kolumnen", "SSE.Views.ViewTab.textFreezeRow": "<PERSON><PERSON><PERSON> ö<PERSON> raden", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Gränssnittstema", "SSE.Views.ViewTab.textManager": "Vyhanterare", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Visa skugga för frysta rutor", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON><PERSON>p paneler", "SSE.Views.ViewTab.textZeros": "Visa nollor", "SSE.Views.ViewTab.textZoom": "Zooma", "SSE.Views.ViewTab.tipClose": "Stäng arkvy", "SSE.Views.ViewTab.tipCreate": "Skapa a<PERSON>vy", "SSE.Views.ViewTab.tipFreeze": "<PERSON><PERSON><PERSON> paneler", "SSE.Views.ViewTab.tipInterfaceTheme": "Gränssnittstema", "SSE.Views.ViewTab.tipSheetView": "<PERSON><PERSON>", "SSE.Views.WBProtection.hintAllowRanges": "<PERSON><PERSON><PERSON> av intervaller", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON>dda <PERSON>d", "SSE.Views.WBProtection.hintProtectWB": "Skydda arbetsbok", "SSE.Views.WBProtection.txtAllowRanges": "<PERSON><PERSON><PERSON> av intervaller", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedCell": "Låst cell", "SSE.Views.WBProtection.txtLockedShape": "Form låst", "SSE.Views.WBProtection.txtLockedText": "<PERSON><PERSON><PERSON> text", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON>dda <PERSON>d", "SSE.Views.WBProtection.txtProtectWB": "Skydda arbetsbok", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Ange ett lösenord för att låsa upp kalkylarkets skydd", "SSE.Views.WBProtection.txtSheetUnlockTitle": "<PERSON><PERSON><PERSON> upp kalkylbladet", "SSE.Views.WBProtection.txtWBUnlockDescription": "Ange ett lösenord för att låsa upp arbetsbokens skydd", "SSE.Views.WBProtection.txtWBUnlockTitle": "L<PERSON>s upp arbetsboken"}