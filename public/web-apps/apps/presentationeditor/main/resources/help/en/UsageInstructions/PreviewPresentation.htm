﻿<!DOCTYPE html>
<html>
	<head>
		<title>Preview your presentation</title>
		<meta charset="utf-8" />
		<meta name="description" content="Preview your presentation" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Preview your presentation</h1>
            <h3>Start the preview</h3>
            <p class="note">Note: If you download a presentation that was created using a third-party application, you can see a preview of the animation effects, if any.</p>
			<p>To preview the current presentation in the <a target="_blank" href="https://www.onlyoffice.com/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a>, you can:</p>
            <ul>
                <li>click the <b>Start slideshow</b> <div class = "icon icon-startpreview"></div> icon on the <b>Home</b> tab of the top toolbar or on the left side of the status bar, or</li>
                <li>select a certain slide within the slide list on the left, right-click it and choose the <b>Start Slideshow</b> option from the contextual menu.</li>
            </ul>
            <p>The preview will start from the currently selected slide. </p>
            <p>You can also click the arrow next to the <b>Start slideshow</b> <span class = "icon icon-startpreview"></span> icon on the <b>Home</b> tab of the top toolbar and select one of the available options:</p>
            <ul>
                <li><b>Show from Beginning</b> - to start the preview from the very first slide,</li>
                <li><b>Show from Current slide</b> - to start the preview from the currently selected slide,</li>
                <li><b>Show presenter view</b> - to start the preview in the <b>Presenter</b> mode that allows you to show the presentation to your audience without slide notes while viewing the presentation with the slide notes on a different monitor.</li>
                <li><b>Show Settings</b> - to open the settings window that allows you to set only one option: <b>Loop continuously until 'Esc' is pressed</b>. Check this option if necessary and click <b>OK</b>. If you enable this option, the presentation will be displayed until you press the <b>Escape</b> key, i.e. when the last slide of the presentation is reached, you will be able to go to the first slide again, etc. If you disable this option, once the last slide of the presentation is reached, a black screen will appear indicating that the presentation is finished, and you can exit from the <b>Preview</b>.
                <p><img alt="Show Settings window" src="../images/showsettings.png" /></p>
                </li>
            </ul>
            <h3>Use the Preview mode</h3>
            <p>In the <b>Preview</b> mode, you can use the following controls at the bottom left corner:</p>
            <p><img alt="Preview Mode controls" src="../images/preview_mode.png" /></p>
            <ul>
                <li>the <b>Previous slide</b> <div class = "icon icon-previousslide"></div> button allows you to return to the previous slide.</li>
                <li>the <b>Pause presentation</b> <div class = "icon icon-pausepresentation"></div> button allows you to stop previewing. When the button is pressed, it turns into the <div class = "icon icon-startpresentation"></div> button.</li>
                <li>the <b>Start presentation</b> <div class = "icon icon-startpresentation"></div> button allows you to resume previewing. When the button is pressed, it turns into the <div class = "icon icon-pausepresentation"></div> button.</li>
                <li>the <b>Next slide</b> <div class = "icon icon-nextslide"></div> button allows you to advance to the following slide.</li>
                <li>the <b>Slide number indicator</b> displays the current slide number as well as the overall number of slides in the presentation. To go to a certain slide in the preview mode, click on the <b>Slide number indicator</b>, enter the necessary slide number in the opened window and press <b>Enter</b>.</li>
                <li>the <b>Full screen</b> <div class = "icon icon-fullscreen"></div> button allows you to switch to the full screen mode.</li>
                <li>the <b>Exit full screen</b> <div class = "icon icon-exitfullscreen"></div> button allows you to exit the full screen mode.</li>
                <li>the <b>Close slideshow</b> <div class = "icon icon-closepreview"></div> button allows you to exit the preview mode.</li>
            </ul>
			<p>You can also <a href="../HelpfulHints/KeyboardShortcuts.htm#preview" onclick="onhyperlinkclick(this)">use the keyboard shortcuts</a> to navigate between the slides in the preview mode.</p>
            <h3 id="presenter">Use the Presenter mode</h3>
            <p class="note"><b>Note</b>: in the <em>desktop version</em>, the presenter mode can be activated only if the second monitor is connected.</p>
            <p>In the <b>Presenter</b> mode, you can view your presentations with slide notes in a separate window, while demonstrating it without notes on a different monitor. The notes for each slide are displayed below the slide preview area.</p>
            <p>To navigate among the slides, you can use the <span class="icon icon-previousslide"></span> and <span class="icon icon-nextslide"></span> buttons or click slides in the list on the left. The hidden slide numbers are crossed out in the slide list on the left. If you wish to show a slide marked as hidden to others, just click it in the slide list on the left - the slide will be displayed.</p>
            <p>You can use the following controls below the slide preview area:</p>
            <p><img alt="Presenter Mode controls" src="../images/presenter_mode.png" /></p>
            <ul>
                <li>the <b>Timer</b> displays the elapsed time of the presentation in the <em>hh.mm.ss</em> format.</li>
                <li>the <b>Pause presentation</b> <div class = "icon icon-presenter_pausepresentation"></div> button allows you to stop previewing. When the button is pressed, it turns into the <div class = "icon icon-presenter_startpresentation"></div> button.</li>
                <li>the <b>Start presentation</b> <div class = "icon icon-presenter_startpresentation"></div> button allows you to resume previewing. When the button is pressed, it turns into the <div class = "icon icon-presenter_pausepresentation"></div> button.</li>
                <li>the <b>Reset</b> button allows to reset the elapsed time of the presentation.</li>
                <li>the <b>Previous slide</b> <div class = "icon icon-presenter_previousslide"></div> button allows you to return to the previous slide.</li>
                <li>the <b>Next slide</b> <div class = "icon icon-presenter_nextslide"></div> button allows you to advance to the following slide.</li>
                <li>the <b>Slide number indicator</b> displays the current slide number as well as the overall number of slides in the presentation.</li>
                <li>the <b>Pointer</b> <div class = "icon icon-pointer"></div> button allows you to highlight something on the screen when showing the presentation. When this option is enabled, the button looks like this: <div class = "icon icon-pointer_enabled"></div>. To point some objects, hover your mouse pointer over the slide preview area and move the pointer around the slide. The pointer will look the following way: <div class = "icon icon-pointer_screen"></div>. To disable this option, click the <div class = "icon icon-pointer_enabled"></div> button once again.</li>
                <li>the <b>End slideshow</b> button allows you to exit the <b>Presenter</b> mode.</li>
            </ul>			
		</div>
	</body>
</html>