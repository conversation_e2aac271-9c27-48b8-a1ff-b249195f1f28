﻿<!DOCTYPE html>
<html>
	<head>
		<title>Introducing the Presentation Editor user interface</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Presentation Editor user interface" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Introducing the Presentation Editor user interface</h1>
			<p><a href="https://www.onlyoffice.com/it/presentation-editor.aspx"><b>Presentation Editor</b></a> uses a tabbed interface where editing commands are grouped into tabs by functionality.</p>
            <div class="onlineDocumentFeatures">
                <p>Online Presentation Editor window:</p>
                <p><img alt="Online Presentation Editor window" src="../images/interface/editorwindow.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Desktop Presentation Editor window:</p>
                <p><img alt="Desktop Presentation Editor window" src="../images/interface/desktop_editorwindow.png" /></p>
            </div>
            <p>The editor interface consists of the following main elements:</p>
            <ol>
                <li>
                    <b>Editor header</b> displays the logo, <span class="desktopDocumentFeatures">opened documents tabs, </span>presentation name and menu tabs.
                    <p>In the left part of the <b>Editor header</b> there are the <b>Save</b>, <b>Print file</b>, <b>Undo</b> and <b>Redo</b> buttons.</p>
                    <p><span class="big big-leftpart"></span></p>
                    <p>In the right part of the <b>Editor header</b> the user name is displayed as well as the following icons:</p>
                    <ul>
                        <li><div class = "icon icon-gotodocuments"></div> <b>Open file location</b> - <span class="desktopDocumentFeatures">in the <em>desktop version</em>, it allows to open the folder where the file is stored in the <b>File explorer</b> window.</span> <span class="onlineDocumentFeatures"> In the <em>online version</em>, it allows to open the folder of the <b>Documents</b> module where the file is stored in a new browser tab.</span></li>
                        <li><div class = "icon icon-viewsettingsicon"></div> - allows to adjust <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">View Settings</a> and access the editor <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Advanced Settings</a>.</li>
                        <li class="onlineDocumentFeatures"><div class = "icon icon-access_rights"></div> <b>Manage document access rights</b> - (available in the <em>online version</em> only) allows to <a href="../HelpfulHints/CollaborativeEditing.htm" onclick="onhyperlinkclick(this)">set access rights</a> for the documents stored in the cloud.</li>
                    </ul>
                </li>
                <li>
                    <b>Top toolbar</b> displays a set of editing commands depending on the selected menu tab. Currently, the following tabs are available: <a href="../ProgramInterface/FileTab.htm" onclick="onhyperlinkclick(this)">File</a>, <a href="../ProgramInterface/HomeTab.htm" onclick="onhyperlinkclick(this)">Home</a>, <a href="../ProgramInterface/InsertTab.htm" onclick="onhyperlinkclick(this)">Insert</a>, <a href="../ProgramInterface/CollaborationTab.htm" onclick="onhyperlinkclick(this)">Collaboration</a>, <span class="desktopDocumentFeatures">Protection,</span> <a href="../ProgramInterface/PluginsTab.htm" onclick="onhyperlinkclick(this)">Plugins</a>.
                    <p>The <span class="icon icon-copy"></span> <b>Copy</b> and <span class="icon icon-paste"></span> <b>Paste</b> options are always available at the left part of the <b>Top toolbar</b> regardless of the selected tab.</p>
                </li>
                <li><b>Status bar</b> at the bottom of the editor window contains the <a href="../UsageInstructions/PreviewPresentation.htm" onclick="onhyperlinkclick(this)">Start slideshow</a> icon, some <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">navigation tools</a>: slide number indicator and zoom buttons. The <b>Status bar</b> also displays some notifications (such as "All changes saved" etc.) and allows to <a href="../HelpfulHints/SpellChecking.htm" onclick="onhyperlinkclick(this)">set text language and enable spell checking</a>.</li>
                <li>
                    <b>Left sidebar</b> contains the following icons:
                    <ul>
                        <li><div class = "icon icon-searchicon"></div> - allows to use the <a href="../HelpfulHints/Search.htm" onclick="onhyperlinkclick(this)">Search and Replace</a> tool,</li>
                        <li><div class = "icon icon-commentsicon"></div> - allows to open the <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">Comments</a> panel,</li>
                        <li class="onlineDocumentFeatures"><div class = "icon icon-chaticon"></div> - (available in the <em>online version</em> only) allows to open the <a href="../HelpfulHints/CollaborativeEditing.htm#chat" onclick="onhyperlinkclick(this)">Chat</a> panel, as well as the icons that allow to contact our support team and view the information about the program.</li>
                    </ul>
                </li>
                <li><b>Right sidebar</b> allows to adjust additional parameters of different objects. When you select a particular object on a slide, the corresponding icon is activated at the right sidebar. Click this icon to expand the right sidebar.</li>
                <li>Horizontal and vertical <b>Rulers</b> help you place objects on a slide and allow to set up tab stops and paragraph indents within the text boxes.</li>
                <li><b>Working area</b> allows to view presentation content, enter and edit data.</li>
                <li><b>Scroll bar</b> on the right allows to scroll the presentation up and down.</li>
            </ol>
            <p>For your convenience you can hide some components and display them again when it is necessary. To learn more on how to adjust view settings please refer to <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">this page</a>.</p>
           
		</div>
	</body>
</html>