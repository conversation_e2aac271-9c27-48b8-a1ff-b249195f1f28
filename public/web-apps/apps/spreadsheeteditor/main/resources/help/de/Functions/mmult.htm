﻿<!DOCTYPE html>
<html>
	<head>
		<title>MMULT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>MMULT-Funktion</h1>
			<p>Die Funktion <b>MMULT</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie liefert das Produkt zweier Matrizen und zeigt den ersten Wert der zurückgegebenen Zahlenanordnungen an.</p>
			<p>Die Formelsyntax der Funktion <b>MMULT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>MMULT(Matrix1;Matrix2)</em></b></p> 
			<p><b><em>Matrix1;Matrix2</em></b> sind die Matrizen, die Sie multiplizieren möchten.</p>
			<p class="note"><b>Hinweis</b>: Wenn die Zellen in der Matrix leer sind oder Text enthalten, gibt die Funktion den Fehlerwert <b>#NV</b> zurück.<br /> Wenn die Anzahl der Zeilen in <b><em>Matrix1</em></b> und der Spalten in <b><em>Matrix2</em></b> nicht gleich ist, gibt die Funktion den Fehlerwert <b>#WERT!</b> zurück.</p>
			<p>Anwendung der Funktion <b>MMULT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>MMULT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="MMULT-Funktion" src="../images/mmult.png" /></p>
		</div>
	</body>
</html>