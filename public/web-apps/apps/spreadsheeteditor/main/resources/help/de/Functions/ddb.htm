﻿<!DOCTYPE html>
<html>
	<head>
		<title>GDA-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>GDA-Funktion</h1>
            <p>Die Funktion <b>GDA</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie gibt die Abschreibung eines Anlagegutes für einen angegebenen Zeitraum unter Verwendung der degressiven Doppelraten-Abschreibung zurück.</p>
            <p>Die Formelsyntax der Funktion <b>GDA</b> ist:</p>
            <p style="text-indent: 150px;"><b><em>GDA(Ansch_Wert;Restwert;Nutzungsdauer;Periode;[Faktor])</em></b></p>
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Ansch_Wert</em></b> sind die Anschaffungskosten des Anlageguts.</p>
            <p style="text-indent: 50px;"><b><em>Restwert</em></b> ist der Restwert, den das Anlagegut am Ende der Nutzungsdauer hat.</p>
            <p style="text-indent: 50px;"><b><em>Nutzungsdauer</em></b> ist die Anzahl der Perioden, über die das Wirtschaftsgut abgeschrieben wird.</p>
            <p style="text-indent: 50px;"><b><em>Periode</em></b> ist der Zeitraum für den Sie die Abschreibung berechnen wollen. Der Wert muss in derselben Zeiteinheit vorliegen wie <em>Nutzungsdauer</em>.</p>
            <p style="text-indent: 50px;"><b><em>Faktor</em></b> ist die Rate, um die der Restbuchwert abnimmt. Dieses Argument ist optional. Fehlt das Argument Faktor, nimmt die Funktion den <b><em>Faktor</em></b> 2 an.</p>
            <p class="note"><b>Hinweis:</b> Alle Argumente müssen positive Zahlen enthalten.</p>
            <p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
            <p>Anwendung der Funktion <b>GDA</b>:</p>
            <ol>
                <li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
                <li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
                <li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
                <li>Klicken Sie auf die Funktion <b>GDA</b>.</li>
                <li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
                <li>Drücken Sie die <b>Eingabetaste</b>.</li>
            </ol>
            <p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
            <p style="text-indent: 150px;"><img alt="GDA-Funktion" src="../images/ddb.png" /></p>
        </div>
	</body>
</html>