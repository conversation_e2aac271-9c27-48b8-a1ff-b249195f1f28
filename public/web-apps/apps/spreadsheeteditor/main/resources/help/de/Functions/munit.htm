﻿<!DOCTYPE html>
<html>
<head>
    <title>MEINHEIT-Funktion</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>MEINHEIT-Funktion</h1>
        <p>Die Funktion <b>MEINHEIT</b> ist eine der mathematischen und trigonometrischen Funktionen. Sie wird verwendet, um eine Einheitsmatrix für die angegebene Dimension zurückzugeben.</p>
        <p>Die Formelsyntax der Funktion <b>MEINHEIT</b> ist:</p>
        <p style="text-indent: 150px;"><b><em>MEINHEIT(Größe)</em></b></p>
        <p>dabei gilt</p>
        <p><b><em>Größe</em></b> ist ein erforderliches Argument. Es ist eine Ganzzahl, die die Größe der Einheitsmatrix angibt, die Sie zurückgeben möchten, und eine Matrix zurückgibt.</p>
        <p>Anwendung der Funktion <b>MEINHEIT</b>:</p>
        <ol>
            <li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
            <li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
            <li>Wählen Sie die Gruppe <b>Mathematik und Trigonometrie</b> Funktionen aus der Liste aus.</li>
            <li>Klicken Sie auf die Funktion <b>MEINHEIT</b>.</li>
            <li>Geben Sie das Argument ein.</li>
            <li>Drücken Sie die <b>Eingabetaste</b>.</li>
        </ol>
        <p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
        <p style="text-indent: 150px;"><img alt="MEINHEIT-Funktion" src="../images/munit.png"/></p>
    </div>
</body>
</html>