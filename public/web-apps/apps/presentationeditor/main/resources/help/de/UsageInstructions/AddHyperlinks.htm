﻿<!DOCTYPE html>
<html>
	<head>
		<title>Hyperlink einfügen</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add hyperlinks to a word or text fragment leading to an external website or to another slide in the same presentation" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Hyperlink einfügen</h1>
			<p>Einfügen eines Hyperlinks im <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a></p>
			<ol>
				<li>Positionieren Sie Ihren Mauszeiger an der Stelle im Textfeld, die Sie in den Link integrieren möchten.</li>
                <li>Wechseln Sie in der oberen Symbolleiste auf die Registerkarte <b>Einfügen</b>.</li>
				<li>Klicken Sie auf das Symbol <div class = "icon icon-addhyperlink"></div> <b>Hyperlink</b> in der oberen Symbolleiste.</li>
				<li>Sie können nun Fenster <b>Einstellungen Hyperlink</b>, die Parameter für den Hyperlink festlegen:
				<ul>
				    <li>wählen Sie den Linktyp, den Sie einfügen möchten:
					<ul>
						<li>
							Verwenden Sie die Option <b>Externer Link</b> und geben Sie eine URL im Format <em>http://www.example.com</em> in das Feld <b>Verknüpfen mit</b> unten ein, wenn Sie möchten einen Hyperlink hinzufügen, der zu einer <b>externen</b> Website führt. Wenn Sie einen Hyperlink zu einer <b>lokalen</b> Datei hinzufügen müssen, geben Sie die URL in den <em>Datei://Pfad/Präsentation.pptx</em> (für Windows) oder <em>Datei:///Pfad/Präsentation.pptx</em> (für MacOS und Linux) Format in das Feld <b>Verknüpfen mit</b> unten ein.
							<p class="note">Der Hyperlink <em>Datei://Pfad/Präsentation.pptx</em> oder <em>Datei:///Pfad/Präsentation.pptx</em> kann nur in der Desktop-Version des Editors geöffnet werden. Im Web-Editor können Sie den Link nur hinzufügen, ohne ihn öffnen zu können.</p>
							<p><img alt="Fenster Einstellungen Hyperlink" src="../../../../../../common/main/resources/help/de/images/hyperlinkwindow.png" /></p>
						</li>
						<li>Wenn Sie einen Hyperlink hinzufügen möchten, der zu einer bestimmten Folie in der aktuellen Präsentation führt, wählen Sie die Option <b>Folie aus dieser Präsentation</b> und geben Sie die gewünschte Folie an. Die folgenden Optionen stehen Ihnen zur Verfügung: Nächste Folie, Vorherige Folie, Erste Folie, Letze Folie, Folie
						<p><img alt="Fenster Einstellungen Hyperlink" src="../images/hyperlinkwindow2.png" /></p>
						</li>
					</ul>
					</li>
					<li><b>Angezeigter Text</b> - geben Sie einen Text ein, der klickbar wird und zu der im oberen Feld angegebenen Webadresse/Folie führt.</li>
					<li><b>QuickInfo</b> - geben Sie einen Text ein, der in einem kleinen Dialogfenster angezeigt wird und den Nutzer über den Inhalt des Verweises informiert.</li>
				</ul>
				</li>
				<li>Klicken Sie auf <b>OK</b>.</li>
			</ol>
			<p>Um einen Hyperlink hinzuzufügen und die Einstellungen zu öffnen, können Sie auch mit der rechten Maustaste an die gewünschte Stelle klicken und die Option <b>Hyperlink</b> im Kontextmenü auswählen oder Sie positionieren den Cursor an der gewünschten Position und drücken die Tastenkombination <b>STRG+K</b>.</p>
			<p class="note">Es ist auch möglich, ein Zeichen, Wort oder eine Wortverbindung mit der Maus oder über die <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">Tastatur</a> auszuwählen. 
			Klicken Sie anschließend in der Registerkarte <b>Einfügen</b> auf <div class = "icon icon-addhyperlink"></div> <b>Hyperlink</b> oder klicken Sie mit der rechten Maustaste auf die Auswahl und wählen Sie im Kontextmenü die Option <b>Hyperlink</b> aus. Danach öffnet sich das oben dargestellte Fenster und im Feld <b>Angezeigter Text</b> erscheint der ausgewählte Textabschnitt.</p>
			<p>Wenn Sie den Mauszeiger über den eingefügten Hyperlink bewegen, wird der von Ihnen im Feld QuickInfo eingebene Text angezeigt. 
			Sie können dem Link folgen, indem Sie die Taste <b>STRG</b> drücken und dann auf den Link in Ihrer Präsentation klicken.</p>
			<p>Um den hinzugefügten Hyperlink zu bearbeiten oder zu entfernen, klicken Sie diesen mit der rechten Maustaste an, wählen Sie die Option <b>Hyperlink</b> im Kontextmenü und wählen Sie anschließend den gewünschten Vorgang aus - <b>Hyperlink bearbeiten</b> oder <b>Hyperlink entfernen</b>.</p>
		</div>
	</body>
</html>
