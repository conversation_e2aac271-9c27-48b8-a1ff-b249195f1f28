{"cancelButtonText": "Zrušiť", "Common.Controllers.Chat.notcriticalErrorTitle": "Upozornenie", "Common.Controllers.Chat.textEnterMessage": "Zadať svoju správu tu", "Common.Controllers.History.notcriticalErrorTitle": "Upozornenie", "Common.define.chartData.textArea": "Plošný graf", "Common.define.chartData.textAreaStacked": "Skladaná oblasť", "Common.define.chartData.textAreaStackedPer": "100% stohovaná o<PERSON>ť", "Common.define.chartData.textBar": "Pruhov<PERSON> graf", "Common.define.chartData.textBarNormal": "Klastrovaný stĺpec", "Common.define.chartData.textBarNormal3d": "3-<PERSON> klastrovaný stĺpec", "Common.define.chartData.textBarNormal3dPerspective": "3D stĺpec", "Common.define.chartData.textBarStacked": "Skladaný stĺpec", "Common.define.chartData.textBarStacked3d": "3-<PERSON> <PERSON><PERSON>aný stĺpec", "Common.define.chartData.textBarStackedPer": "100% stohovaný stĺpec", "Common.define.chartData.textBarStackedPer3d": "3-D 100% stohovaný stĺpec", "Common.define.chartData.textCharts": "<PERSON><PERSON>", "Common.define.chartData.textColumn": "Stĺpec", "Common.define.chartData.textColumnSpark": "Stĺpec", "Common.define.chartData.textCombo": "Kombo", "Common.define.chartData.textComboAreaBar": "Skladaná oblasť - zoskupené", "Common.define.chartData.textComboBarLine": "Zoskupený stĺpec - riadok", "Common.define.chartData.textComboBarLineSecondary": "Zoskupený stĺpec - čiara na sekundárnej osi", "Common.define.chartData.textComboCustom": "Vlastná kombinácia", "Common.define.chartData.textDoughnut": "Šiška", "Common.define.chartData.textHBarNormal": "Zoskupená lišta", "Common.define.chartData.textHBarNormal3d": "3-D zoskupená lišta", "Common.define.chartData.textHBarStacked": "Skladaný bar", "Common.define.chartData.textHBarStacked3d": "3-D zoskupená lišta", "Common.define.chartData.textHBarStackedPer": "100% stohovaná lišta", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% sto<PERSON><PERSON><PERSON> lišta", "Common.define.chartData.textLine": "Čiara/líniový graf", "Common.define.chartData.textLine3d": "3-D línia", "Common.define.chartData.textLineMarker": "Líniový so značkami", "Common.define.chartData.textLineSpark": "Čiara", "Common.define.chartData.textLineStacked": "Skladaná linka", "Common.define.chartData.textLineStackedMarker": "Skladaná linka so značkami", "Common.define.chartData.textLineStackedPer": "100% stohovaná čiara", "Common.define.chartData.textLineStackedPerMarker": "100% stohovaná línia so značkami", "Common.define.chartData.textPie": "Koláčový graf", "Common.define.chartData.textPie3d": "3-<PERSON> koláč", "Common.define.chartData.textPoint": "Bodov<PERSON> graf", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Bodový s rovn<PERSON><PERSON> linkami", "Common.define.chartData.textScatterLineMarker": "Bodový s rovnými linkami a značkami", "Common.define.chartData.textScatterSmooth": "Bodový s vyhladenými linkami", "Common.define.chartData.textScatterSmoothMarker": "Bodový s vyhladenými linkami a značkami", "Common.define.chartData.textSparks": "Mikro-grafy", "Common.define.chartData.textStock": "Akcie/burzový graf", "Common.define.chartData.textSurface": "Povrch", "Common.define.chartData.textWinLossSpark": "Zisk/strata", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Žiaden formát nie je nasta<PERSON>ý", "Common.define.conditionalData.text1Above": "Na 1 štandardnú odchýlku vyššie", "Common.define.conditionalData.text1Below": "Na 1 štandardnú odchýlku nižšie", "Common.define.conditionalData.text2Above": "2 Smerodajná odchýlka nad", "Common.define.conditionalData.text2Below": "2 Smerodajná odchýlka pod", "Common.define.conditionalData.text3Above": "3 Smerodajná odchýlka nad", "Common.define.conditionalData.text3Below": "3 Smerodajná odchýlka pod", "Common.define.conditionalData.textAbove": "Nad", "Common.define.conditionalData.textAverage": "Priemerne", "Common.define.conditionalData.textBegins": "Začať s", "Common.define.conditionalData.textBelow": "Pod", "Common.define.conditionalData.textBetween": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Prázdny", "Common.define.conditionalData.textBlanks": "Obsahuje prázdne bunky", "Common.define.conditionalData.textBottom": "<PERSON><PERSON>", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Dátový riadok", "Common.define.conditionalData.textDate": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Končí s", "Common.define.conditionalData.textEqAbove": "Väčšie alebo rovné", "Common.define.conditionalData.textEqBelow": "R<PERSON><PERSON><PERSON> alebo <PERSON>", "Common.define.conditionalData.textEqual": "Rovná sa", "Common.define.conditionalData.textError": "Chyba", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textFormula": "Vzorec", "Common.define.conditionalData.textGreater": "Väčšie ako", "Common.define.conditionalData.textGreaterEq": "Väčšie alebo rovná sa", "Common.define.conditionalData.textIconSets": "<PERSON><PERSON> <PERSON>", "Common.define.conditionalData.textLast7days": "V uplynulých 7 dňoch", "Common.define.conditionalData.textLastMonth": "posledný mesiac", "Common.define.conditionalData.textLastWeek": "Posledný týždeň", "Common.define.conditionalData.textLess": "<PERSON><PERSON> a<PERSON>", "Common.define.conditionalData.textLessEq": "<PERSON>ej alebo rovná sa", "Common.define.conditionalData.textNextMonth": "Ďalší mesiac", "Common.define.conditionalData.textNextWeek": "Ďalší týždeň", "Common.define.conditionalData.textNotBetween": "<PERSON><PERSON> med<PERSON>", "Common.define.conditionalData.textNotBlanks": "Neobsahuje prázdne komôrky", "Common.define.conditionalData.textNotContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotEqual": "Nerovná sa", "Common.define.conditionalData.textNotErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "Tento mesiac", "Common.define.conditionalData.textThisWeek": "<PERSON>to <PERSON> ", "Common.define.conditionalData.textToday": "Dnes", "Common.define.conditionalData.textTomorrow": "Zajtra", "Common.define.conditionalData.textTop": "Hore", "Common.define.conditionalData.textUnique": "Unik<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textValue": "Hodnota je", "Common.define.conditionalData.textYesterday": "Včera", "Common.Translation.textMoreButton": "Viac", "Common.Translation.warnFileLocked": "Súbor je upravovaný v inej aplikácií. Môžete pokračovať v úpravách a uložiť ho ako kópiu.", "Common.Translation.warnFileLockedBtnEdit": "Vytvoriť kópiu", "Common.Translation.warnFileLockedBtnView": "Otvoriť pre náhľad", "Common.UI.ButtonColored.textAutoColor": "<PERSON>ky", "Common.UI.ButtonColored.textNewColor": "Pridať novú vlastnú farbu", "Common.UI.ComboBorderSize.txtNoBorders": "Bez orámovania", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Bez orámovania", "Common.UI.ComboDataView.emptyComboText": "Žiadn<PERSON>", "Common.UI.ExtendedColorDialog.addButtonText": "Pridať", "Common.UI.ExtendedColorDialog.textCurrent": "Aktuálny", "Common.UI.ExtendedColorDialog.textHexErr": "Zadaná hodnota je nesprávna.<br><PERSON><PERSON><PERSON><PERSON>, zadaj<PERSON> číselnú hodnotu medzi 000000 a FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nový", "Common.UI.ExtendedColorDialog.textRGBErr": "Zadaná hodnota je nesprávna.<br><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> číselnú hodnotu medzi 0 a 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Zobraziť heslo", "Common.UI.SearchDialog.textHighlight": "Zvýrazniť výsledky", "Common.UI.SearchDialog.textMatchCase": "Rozlišovať veľkosť písmen", "Common.UI.SearchDialog.textReplaceDef": "Zadať náhradný text", "Common.UI.SearchDialog.textSearchStart": "<PERSON>ada<PERSON> svoj text tu", "Common.UI.SearchDialog.textTitle": "Nájsť a nahradiť", "Common.UI.SearchDialog.textTitle2": "Nájsť", "Common.UI.SearchDialog.textWholeWords": "Len celé slová", "Common.UI.SearchDialog.txtBtnHideReplace": "Skryť náhradu", "Common.UI.SearchDialog.txtBtnReplace": "Nahradiť", "Common.UI.SearchDialog.txtBtnReplaceAll": "Nahradiť všetko", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>ž nezobrazovať túto správu", "Common.UI.SynchronizeTip.textSynchronize": "Dokument bol zmenený ďalším používateľom.<br>Prosím, kliknite na uloženie zmien a opätovne načítajte aktualizácie.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON><PERSON><PERSON> farby", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Štandardná svetlosť", "Common.UI.Themes.txtThemeDark": "Tmavý", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.cancelButtonText": "Zrušiť", "Common.UI.Window.closeButtonText": "Zatvoriť", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Potvrdenie", "Common.UI.Window.textDontShow": "<PERSON>ž nezobrazovať túto správu", "Common.UI.Window.textError": "Chyba", "Common.UI.Window.textInformation": "Informácie", "Common.UI.Window.textWarning": "Upozornenie", "Common.UI.Window.yesButtonText": "Á<PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "adresa:", "Common.Views.About.txtLicensee": "DRŽITEĽ LICENCIE", "Common.Views.About.txtLicensor": "UDEĽOVATEĽ LICENCIE", "Common.Views.About.txtMail": "e-mail: ", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Verzia", "Common.Views.AutoCorrectDialog.textAdd": "Pridať", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Použite počas práce", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Automatická oprava", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformátovať počas písania", "Common.Views.AutoCorrectDialog.textBy": "Od", "Common.Views.AutoCorrectDialog.textDelete": "Odstrániť", "Common.Views.AutoCorrectDialog.textHyperlink": "Internetové a sieťové prístupy s hypertextovými odkazmi", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autokorekcia pre matematiku", "Common.Views.AutoCorrectDialog.textNewRowCol": "Zahrnúť nové riadky a stĺpce v tabuľke", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Nasledujúce výrazy sú rozpoznané ako matematické funkcie. Nebudú aplikované pravidlá týkajúce sa veľkých a malých písmen.", "Common.Views.AutoCorrectDialog.textReplace": "Nahradiť", "Common.Views.AutoCorrectDialog.textReplaceText": "Nahrádzať počas písania", "Common.Views.AutoCorrectDialog.textReplaceType": "Nahrádzať text počas písania", "Common.Views.AutoCorrectDialog.textReset": "Obnoviť", "Common.Views.AutoCorrectDialog.textResetAll": "Obnoviť pôvodné nastavenia", "Common.Views.AutoCorrectDialog.textRestore": "Obnoviť", "Common.Views.AutoCorrectDialog.textTitle": "Automatická oprava", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Uznané funkcie musia obsahovať iba písmená A až Z, horný index alebo dolný index.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Akýkoľvek vami pridaný výraz bude odstránený a tie, ktoré ste odstránili budú prinavrátené späť. Chcete pokračovať?", "Common.Views.AutoCorrectDialog.warnReplace": "Samooprava pre %1 už existuje. Chcete ju nahradiť?", "Common.Views.AutoCorrectDialog.warnReset": "Akákoľvek automatická oprava bude odstránená a zmeny budú vrátené na pôvodné hodnoty. Chcete pokračovať?", "Common.Views.AutoCorrectDialog.warnRestore": "Samooprava pre %1 bude resetovaná na pôvodnú hodnotu. Chcete pokračovať?", "Common.Views.Chat.textSend": "Poslať", "Common.Views.Comments.mniAuthorAsc": "Autor A až Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z až A", "Common.Views.Comments.mniDateAsc": "Najstarší", "Common.Views.Comments.mniDateDesc": "Najnovší", "Common.Views.Comments.mniFilterGroups": "Filtrovať podľa skupiny", "Common.Views.Comments.mniPositionAsc": "Zhora ", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "Pridať", "Common.Views.Comments.textAddComment": "Pridať komentár", "Common.Views.Comments.textAddCommentToDoc": "Pridať komentár k dokumentu", "Common.Views.Comments.textAddReply": "Pridať odpoveď", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Návštevník", "Common.Views.Comments.textCancel": "Zrušiť", "Common.Views.Comments.textClose": "Zatvoriť", "Common.Views.Comments.textClosePanel": "Zavrieť komentáre", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Zadať svoj komentár tu", "Common.Views.Comments.textHintAddComment": "Pridať komentár", "Common.Views.Comments.textOpenAgain": "Znova otvoriť", "Common.Views.Comments.textReply": "Odpoveď", "Common.Views.Comments.textResolve": "Vyriešiť", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Triediť komentáre", "Common.Views.Comments.textViewResolved": "Nemáte povolenie pre opätovné otvorenie komentáru", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>ž nezobrazovať túto správu", "Common.Views.CopyWarningDialog.textMsg": "Kop<PERSON>rovať, vystrihovať a priliepať pomocou tlačidiel panela nástrojov editora a kontextovej ponuky sa vykonajú iba v rámci tejto karty editora.<br><br>Ak chcete kopírovať alebo priliepať do alebo z aplikácií mimo editora, použite nasledujúce klávesové skratky: ", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON> k<PERSON>, vystrihnúť a prilepiť", "Common.Views.CopyWarningDialog.textToCopy": "pre kopírovanie", "Common.Views.CopyWarningDialog.textToCut": "pre vystrihnutie", "Common.Views.CopyWarningDialog.textToPaste": "pre vloženie", "Common.Views.DocumentAccessDialog.textLoading": "Načítava.....", "Common.Views.DocumentAccessDialog.textTitle": "Nastavenie zdieľania", "Common.Views.EditNameDialog.textLabel": "Štítok:", "Common.Views.EditNameDialog.textLabelError": "Etiketa nesmie byť prázdna.", "Common.Views.Header.labelCoUsersDescr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> s<PERSON> práve upravujú:", "Common.Views.Header.textAddFavorite": "Označiť ako obľúbené", "Common.Views.Header.textAdvSettings": "Pokročilé nastavenia", "Common.Views.Header.textBack": "Otvoriť umiestnenie súboru", "Common.Views.Header.textCompactView": "Skryť panel s n<PERSON><PERSON>jmi", "Common.Views.Header.textHideLines": "Skryť pravítka", "Common.Views.Header.textHideStatusBar": "Skombinovať list a stavový riadok", "Common.Views.Header.textRemoveFavorite": "Odstrániť z obľúbených", "Common.Views.Header.textSaveBegin": "Ukladanie...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Všetky zmeny boli uložené", "Common.Views.Header.textSaveExpander": "Všetky zmeny boli uložené", "Common.Views.Header.textZoom": "Priblíženie", "Common.Views.Header.tipAccessRights": "Spravovať prístupové práva k dokumentom", "Common.Views.Header.tipDownload": "Stiahnuť súbor", "Common.Views.Header.tipGoEdit": "Editovať aktuálny súbor", "Common.Views.Header.tipPrint": "Vytlačiť súbor", "Common.Views.Header.tipRedo": "<PERSON><PERSON>", "Common.Views.Header.tipSave": "Uložiť", "Common.Views.Header.tipUndo": "<PERSON><PERSON>", "Common.Views.Header.tipUndock": "Oddeliť do samostatného okna", "Common.Views.Header.tipViewSettings": "Zobraziť nastavenia", "Common.Views.Header.tipViewUsers": "Zobraziť používateľov a spravovať prístupové práva k dokumentom", "Common.Views.Header.txtAccessRights": "Zmeniť prístupové práva", "Common.Views.Header.txtRename": "Premenovať", "Common.Views.History.textCloseHistory": "Zatvoriť históriu", "Common.Views.History.textHide": "Stiahnuť/zbaliť/zvinúť", "Common.Views.History.textHideAll": "Skryť podrobné zmeny", "Common.Views.History.textRestore": "Obnoviť", "Common.Views.History.textShow": "Expandovať/rozšíriť", "Common.Views.History.textShowAll": "Zobraziť detailné z<PERSON>y", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Vložte URL adresu obrázka:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Toto pole sa vyžaduje", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Toto pole by malo by<PERSON> vo formá<PERSON> 'http://www.example.com'", "Common.Views.ListSettingsDialog.textBulleted": "S odrážkami", "Common.Views.ListSettingsDialog.textNumbering": "Číslovaný", "Common.Views.ListSettingsDialog.tipChange": "Zmeniť odrážku", "Common.Views.ListSettingsDialog.txtBullet": "Odr<PERSON>ž<PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Farba", "Common.Views.ListSettingsDialog.txtNewBullet": "Nová odrážka", "Common.Views.ListSettingsDialog.txtNone": "žiadny", "Common.Views.ListSettingsDialog.txtOfText": "% textu", "Common.Views.ListSettingsDialog.txtSize": "Veľkosť", "Common.Views.ListSettingsDialog.txtStart": "Začať na", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "Nastavenia zoznamu", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "Zatvoriť súbor", "Common.Views.OpenDialog.textInvalidRange": "Neplatn<PERSON> roz<PERSON> b<PERSON>", "Common.Views.OpenDialog.textSelectData": "Vybrať údaje", "Common.Views.OpenDialog.txtAdvanced": "Pokroč<PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Dvojbodka ", "Common.Views.OpenDialog.txtComma": "Čiarka", "Common.Views.OpenDialog.txtDelimiter": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDestData": "Zvoľte kam umiestniť dáta", "Common.Views.OpenDialog.txtEmpty": "Toto pole sa vyžaduje", "Common.Views.OpenDialog.txtEncoding": "Kódovanie", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> je nesp<PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Zadajte heslo na otvorenie súboru", "Common.Views.OpenDialog.txtOther": "Ostatné", "Common.Views.OpenDialog.txtPassword": "He<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Náhľad", "Common.Views.OpenDialog.txtProtected": "Po zadaní hesla a otvorení súboru bude súčasné heslo k súboru resetované.", "Common.Views.OpenDialog.txtSemicolon": "Bodkočiarka", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtTab": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtTitle": "Vyberte %1 možností", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.PasswordDialog.txtDescription": "Nastaviť heslo na ochranu tohto dokumentu", "Common.Views.PasswordDialog.txtIncorrectPwd": "Heslá sa nezhodujú", "Common.Views.PasswordDialog.txtPassword": "He<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON>op<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtTitle": "Nastaviť heslo", "Common.Views.PasswordDialog.txtWarning": "Upozornenie: Ak stratíte alebo zabudnete heslo, nemožno ho obnoviť. Uschovajte ho na bezpečnom mieste.", "Common.Views.PluginDlg.textLoading": "Nahrávanie", "Common.Views.Plugins.groupCaption": "Pluginy", "Common.Views.Plugins.strPlugins": "Pluginy", "Common.Views.Plugins.textLoading": "Nahrávanie", "Common.Views.Plugins.textStart": "Začať/začiatok", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Protection.hintAddPwd": "Šifrovať heslom", "Common.Views.Protection.hintPwd": "Zmeniť alebo odstrániť heslo", "Common.Views.Protection.hintSignature": "Pridať digitálny podpis alebo riadok na podpis", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "Zmeniť heslo", "Common.Views.Protection.txtDeletePwd": "Odstrániť heslo", "Common.Views.Protection.txtEncrypt": "Šifrovať", "Common.Views.Protection.txtInvisibleSignature": "Pridajte digitálny pod<PERSON>", "Common.Views.Protection.txtSignature": "Podpis", "Common.Views.Protection.txtSignatureLine": "Pridať riadok na podpis", "Common.Views.RenameDialog.textName": "Názov súboru", "Common.Views.RenameDialog.txtInvalidName": "Názov súboru nemôže obsahovať žiadny z nasledujúcich znakov:", "Common.Views.ReviewChanges.hintNext": "K ďalšej zmene", "Common.Views.ReviewChanges.hintPrev": "K predošlej zmene", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Spoločné úpravy v reálnom čase. Všetky zmeny sú ukladané automaticky.", "Common.Views.ReviewChanges.strStrict": "Prís<PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "Pre synchroniz<PERSON><PERSON><PERSON> z<PERSON>, ktor<PERSON> ste urobili vy a ostatný, pou<PERSON><PERSON> tlačí<PERSON>ko \"Uložiť\".", "Common.Views.ReviewChanges.tipAcceptCurrent": "Akceptovať aktuálnu zmenu", "Common.Views.ReviewChanges.tipCoAuthMode": "Nastaviť mód spoloč<PERSON>ých úprav", "Common.Views.ReviewChanges.tipCommentRem": "Odstrániť komentáre", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Odstrániť aktuálne komentáre", "Common.Views.ReviewChanges.tipCommentResolve": "Vyriešiť komentáre", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Vyriešiť aktuálne komentáre", "Common.Views.ReviewChanges.tipHistory": "Zobraziť históriu verzií", "Common.Views.ReviewChanges.tipRejectCurrent": "Odmietnuť aktuálnu zmenu", "Common.Views.ReviewChanges.tipReview": "Sledovať zmeny", "Common.Views.ReviewChanges.tipReviewView": "Vyberte režim, v ktorom chcete zobraziť zmeny", "Common.Views.ReviewChanges.tipSetDocLang": "Nastaviť jazyk dokumentu", "Common.Views.ReviewChanges.tipSetSpelling": "Kontrola pra<PERSON>pisu", "Common.Views.ReviewChanges.tipSharing": "Spravovať prístupové práva k dokumentom", "Common.Views.ReviewChanges.txtAccept": "Prijať", "Common.Views.ReviewChanges.txtAcceptAll": "Akceptovať všetky zmeny", "Common.Views.ReviewChanges.txtAcceptChanges": "Akceptovať zmeny", "Common.Views.ReviewChanges.txtAcceptCurrent": "Akceptovať aktuálnu zmenu", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Zatvoriť", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON><PERSON>j <PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "Odstrániť všetky komentáre", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Odstrániť aktuálne komentáre", "Common.Views.ReviewChanges.txtCommentRemMy": "Odstrániť moje komentáre", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Odstrániť moje aktuálne komentáre", "Common.Views.ReviewChanges.txtCommentRemove": "Odstrániť", "Common.Views.ReviewChanges.txtCommentResolve": "Vyriešiť", "Common.Views.ReviewChanges.txtCommentResolveAll": "Vyriešiť všetky komentáre", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Vyriešiť aktuálne komentáre", "Common.Views.ReviewChanges.txtCommentResolveMy": "Vyriešiť moje komentáre", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Vyriešiť moje aktuálne komentáre", "Common.Views.ReviewChanges.txtDocLang": "Jazyk", "Common.Views.ReviewChanges.txtFinal": "Všetky zmeny prijaté (ukážka)", "Common.Views.ReviewChanges.txtFinalCap": "Posledný", "Common.Views.ReviewChanges.txtHistory": "História verzií", "Common.Views.ReviewChanges.txtMarkup": "Všetky zmeny (upravovanie)", "Common.Views.ReviewChanges.txtMarkupCap": "Vyznačenie", "Common.Views.ReviewChanges.txtNext": "Na<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Všetky zmeny boli zamietnuté (ukážka)", "Common.Views.ReviewChanges.txtOriginalCap": "Originál", "Common.Views.ReviewChanges.txtPrev": "Pre<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Odmietnuť všetky zmeny", "Common.Views.ReviewChanges.txtRejectChanges": "Odmietnuť zmeny", "Common.Views.ReviewChanges.txtRejectCurrent": "Odmietnuť aktuálnu zmenu", "Common.Views.ReviewChanges.txtSharing": "Zdieľanie", "Common.Views.ReviewChanges.txtSpelling": "Kontrola pra<PERSON>pisu", "Common.Views.ReviewChanges.txtTurnon": "Sledovať zmeny", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON>raze<PERSON>", "Common.Views.ReviewPopover.textAdd": "Pridať", "Common.Views.ReviewPopover.textAddReply": "Pridať odpoveď", "Common.Views.ReviewPopover.textCancel": "Zrušiť", "Common.Views.ReviewPopover.textClose": "Zatvoriť", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+zmienka poskytne prístup k dokumentu a odošle mail", "Common.Views.ReviewPopover.textMentionNotify": "+zmienka upovedomí užívateľa mailom", "Common.Views.ReviewPopover.textOpenAgain": "Znova otvoriť", "Common.Views.ReviewPopover.textReply": "Odpovedať", "Common.Views.ReviewPopover.textResolve": "Vyriešiť", "Common.Views.ReviewPopover.textViewResolved": "Nemáte povolenie pre opätovné otvorenie komentáru", "Common.Views.ReviewPopover.txtDeleteTip": "Odstrániť", "Common.Views.ReviewPopover.txtEditTip": "Upraviť", "Common.Views.SaveAsDlg.textLoading": "Načítavanie", "Common.Views.SaveAsDlg.textTitle": "Priečinok na uloženie", "Common.Views.SelectFileDlg.textLoading": "Načítavanie", "Common.Views.SelectFileDlg.textTitle": "Vybrať zdroj ú<PERSON>jov", "Common.Views.SignDialog.textBold": "Tučn<PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Zmeniť", "Common.Views.SignDialog.textInputName": "Zadať meno sign<PERSON>", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "<PERSON>o podpisovateľa nesmie byť prázdne. ", "Common.Views.SignDialog.textPurpose": "Účel podpisovania tohto dokumentu", "Common.Views.SignDialog.textSelect": "Vybrať", "Common.Views.SignDialog.textSelectImage": "Vybrať obrázok", "Common.Views.SignDialog.textSignature": "Podpis vyzerá ako", "Common.Views.SignDialog.textTitle": "Podpísať dokument", "Common.Views.SignDialog.textUseImage": "alebo kliknite na položku 'Vybrať obrázok' ak chcete použiť obrázok ako podpis", "Common.Views.SignDialog.textValid": "Platný od %1 do %2", "Common.Views.SignDialog.tipFontName": "Názov písma", "Common.Views.SignDialog.tipFontSize": "Veľkosť písma", "Common.Views.SignSettingsDialog.textAllowComment": "Povoliť podpisujúcemu pridať komentár do podpisového dialógu", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Názov signatára", "Common.Views.SignSettingsDialog.textInstructions": "Pokyny pre signatára", "Common.Views.SignSettingsDialog.textShowDate": "Zobraziť dátum podpisu v riadku podpisu", "Common.Views.SignSettingsDialog.textTitle": "Nastavenia podpisu", "Common.Views.SignSettingsDialog.txtEmpty": "Toto pole sa vyžaduje", "Common.Views.SymbolTableDialog.textCharacter": "Symbol", "Common.Views.SymbolTableDialog.textCode": "Hodnota unicode HEX ", "Common.Views.SymbolTableDialog.textCopyright": "Znak autorských práv", "Common.Views.SymbolTableDialog.textDCQuote": "Uzatvárajúca úvodzovka", "Common.Views.SymbolTableDialog.textDOQuote": "Úvodná dvojitá úvodzovka", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontálna elipsa", "Common.Views.SymbolTableDialog.textEmDash": "Dlhá pomlčka", "Common.Views.SymbolTableDialog.textEmSpace": "Dlhá medzera", "Common.Views.SymbolTableDialog.textEnDash": "Krátka pomlčka", "Common.Views.SymbolTableDialog.textEnSpace": "Krátka medzera", "Common.Views.SymbolTableDialog.textFont": "Písmo", "Common.Views.SymbolTableDialog.textNBHyphen": "Pevná pomlčka", "Common.Views.SymbolTableDialog.textNBSpace": "Nezalomiteľná medzera", "Common.Views.SymbolTableDialog.textPilcrow": "Typografický znak odstavca", "Common.Views.SymbolTableDialog.textQEmSpace": "Medzera 1/4 Em", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Nedávno použité symboly", "Common.Views.SymbolTableDialog.textRegistered": "Registrovaná značka", "Common.Views.SymbolTableDialog.textSCQuote": "Uzatvárajúca úvodzovka", "Common.Views.SymbolTableDialog.textSection": "Paragra<PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON>ová skratka", "Common.Views.SymbolTableDialog.textSHyphen": "Mäkký spojovník", "Common.Views.SymbolTableDialog.textSOQuote": "Úvodná jednoduchá úvodzovka", "Common.Views.SymbolTableDialog.textSpecial": "Špeciálne znaky", "Common.Views.SymbolTableDialog.textSymbols": "Symboly", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Symbol ochrannej známky", "Common.Views.UserNameDialog.textDontShow": "Nepýtať sa ma znova", "Common.Views.UserNameDialog.textLabel": "Štítok:", "Common.Views.UserNameDialog.textLabelError": "Etiketa nesmie byť prázdna.", "SSE.Controllers.DataTab.textColumns": "Stĺpce", "SSE.Controllers.DataTab.textEmptyUrl": "Musíte špecifikovať adresu URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Text na stĺpec", "SSE.Controllers.DataTab.txtDataValidation": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpand": "Expandovať/rozšíriť", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Údaje vedľa výberu sa neodstránia. Chcete rozšíriť výber tak, aby zahŕňal susediace údaje, alebo chcete pokračovať len s aktuálne vybratými bunkami?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Výber obsahuje niektoré bunky bez nastavení overenia údajov.<br>Chcete rozšíriť overenie údajov na tieto bunky?", "SSE.Controllers.DataTab.txtImportWizard": "Sprievodca importom textu", "SSE.Controllers.DataTab.txtRemDuplicates": "Odobrať duplicity", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Výber obsahuje viac ako jeden typ overenia.<br>Vymazať aktuálne nastavenia a pokračovať?", "SSE.Controllers.DataTab.txtRemSelected": "Odstrániť vybrané", "SSE.Controllers.DataTab.txtUrlTitle": "Vložiť adresu domény URL", "SSE.Controllers.DocumentHolder.alignmentText": "Zarovnanie", "SSE.Controllers.DocumentHolder.centerText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteColumnText": "Odstrániť stĺpec", "SSE.Controllers.DocumentHolder.deleteRowText": "Odstrániť riadok", "SSE.Controllers.DocumentHolder.deleteText": "Vymazať", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Prepojenie na odkaz neexistuje. Opravte prosím odkaz alebo ho odstráňte.", "SSE.Controllers.DocumentHolder.guestText": "Návštevník", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Stĺpec vľavo", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Stĺpec vpravo", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Riadok nad", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Riadok pod", "SSE.Controllers.DocumentHolder.insertText": "Vložiť", "SSE.Controllers.DocumentHolder.leftText": "Vľavo", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Upozornenie", "SSE.Controllers.DocumentHolder.rightText": "Vpravo", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Možnosti autokorekcie", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Šírka stĺpca {0} symboly ({1} pixely)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Výška riadku {0} bodov ({1} pixelov)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Kliknutím na odkaz ho otvoríte alebo kliknutím a podržaním tlačidla myši vyberte bunku.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Vložiť vľavo", "SSE.Controllers.DocumentHolder.textInsertTop": "Vložiť hore", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Špeciálne prilepiť", "SSE.Controllers.DocumentHolder.textStopExpand": "Zastaviť automatické rozširovanie tabuliek", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Tento prvok upravuje iný používateľ.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Nadp<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddBottom": "Pridať spodné orámovanie", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Pridať liš<PERSON>", "SSE.Controllers.DocumentHolder.txtAddHor": "Pridať vodorovnú čiaru", "SSE.Controllers.DocumentHolder.txtAddLB": "Pridať ľavý spodný riadok", "SSE.Controllers.DocumentHolder.txtAddLeft": "Pridať ľavé orámovanie", "SSE.Controllers.DocumentHolder.txtAddLT": "Pridať ľavý horný riadok", "SSE.Controllers.DocumentHolder.txtAddRight": "Pridať pravé orámovanie", "SSE.Controllers.DocumentHolder.txtAddTop": "Pridať horné orámovanie", "SSE.Controllers.DocumentHolder.txtAddVer": "Pridať zvislú čiaru", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Zarovnať na znak", "SSE.Controllers.DocumentHolder.txtAll": "(všetko)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "<PERSON><PERSON><PERSON><PERSON> celý obsah tabuľky alebo zadaných stĺpcov tabuľky vrátane hlavičiek stĺpcov, údajov a riadkov celkového počtu", "SSE.Controllers.DocumentHolder.txtAnd": "a", "SSE.Controllers.DocumentHolder.txtBegins": "Začať s", "SSE.Controllers.DocumentHolder.txtBelowAve": "Podpriemerný", "SSE.Controllers.DocumentHolder.txtBlanks": "(Prázdne)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Vlastnosti orámovania", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumn": "Stĺpec", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Zarovnanie stĺpcov", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDataTableHint": "<PERSON><PERSON><PERSON><PERSON> bunky tabuľky alebo zadané stĺpce tabuľky", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Zmenšiť veľkosť obsahu", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Odstrániť obsah", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Odstrániť manuálny rozdeľovač", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Odstrániť uzatváracie znaky", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Odstrániť uzatváracie znaky a oddeľovače", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Odstrániť rovnicu", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Odstrániť znak", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Odstrániť odmocninu", "SSE.Controllers.DocumentHolder.txtEnds": "Končí na", "SSE.Controllers.DocumentHolder.txtEquals": "rovná se", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "<PERSON><PERSON><PERSON><PERSON> s farbou bunky", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Zhodné s farbou písma", "SSE.Controllers.DocumentHolder.txtExpand": "Rozbaliť a zoradiť", "SSE.Controllers.DocumentHolder.txtExpandSort": "Údaje vedľa výberu nebudú zoradené. Chcete rozšíriť výber tak, aby zahŕňal priľahlé údaje, alebo pok<PERSON>č<PERSON>ť v triedení len vybraných buniek?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Hore", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Zmeniť na lineárny zlomok", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Zmeniť na skosený zlomok", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Zmeniť na zložený zlomok", "SSE.Controllers.DocumentHolder.txtGreater": "Väčšie ako", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Väčšie alebo rovná sa", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Zadať nad text", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Zadať pod text", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "<PERSON><PERSON><PERSON><PERSON> stĺpcov pre tabuľku alebo určené stĺpce tabuľky", "SSE.Controllers.DocumentHolder.txtHeight": "Výška", "SSE.Controllers.DocumentHolder.txtHideBottom": "Skryť spodné orámovanie", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Skryť dolné ohrani<PERSON>enie", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Skryť konečnú/záverečnú zátvorku", "SSE.Controllers.DocumentHolder.txtHideDegree": "Skryť stupeň", "SSE.Controllers.DocumentHolder.txtHideHor": "Skryť vodorovnú čiaru", "SSE.Controllers.DocumentHolder.txtHideLB": "Skryť ľavý dolný riadok", "SSE.Controllers.DocumentHolder.txtHideLeft": "Skryť ľavé orámovanie", "SSE.Controllers.DocumentHolder.txtHideLT": "Skryť ľavý horný riadok", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Skryť začiatočnú/úvodnú zátvorku", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Skryť vlastníka/náhradu textu", "SSE.Controllers.DocumentHolder.txtHideRight": "Skryť pravé orámovanie", "SSE.Controllers.DocumentHolder.txtHideTop": "S<PERSON><PERSON><PERSON> horné orámovanie", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Skryť horné <PERSON>e", "SSE.Controllers.DocumentHolder.txtHideVer": "Skryť vertikálnu čiaru", "SSE.Controllers.DocumentHolder.txtImportWizard": "Sprievodca importom textu", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Zväčšiť veľkosť obsahu/argumentu", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Vložiť argument/obsah po", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Vložiť argument/obsah pred", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Vložiť manuálny rozdeľovač", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Vložiť rovnicu po", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Vložiť rovnicu pred", "SSE.Controllers.DocumentHolder.txtItems": "Polož<PERSON>", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Ponechať iba text", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON> a<PERSON>", "SSE.Controllers.DocumentHolder.txtLessEquals": "<PERSON>ej alebo rovná sa", "SSE.Controllers.DocumentHolder.txtLimitChange": "Zmeniť polohu obmedzenia", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limita nad textom", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON><PERSON> pod textom", "SSE.Controllers.DocumentHolder.txtLockSort": "Blízko vášho výberu existuj<PERSON> d<PERSON>, ne<PERSON><PERSON><PERSON> však dostatočné oprávnenia k úprave týchto buniek.<br>Chcete pokračovať s aktuálnym výberom? ", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Prispôsobenie zátvoriek k výške obsahu", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Zarovnanie matice", "SSE.Controllers.DocumentHolder.txtNoChoices": "Neexistujú žiadne možnosti na vyplnenie bunky.<br>Len hodnoty textu zo stĺpca môžu byť vybrané na výmenu.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Nezačína s", "SSE.Controllers.DocumentHolder.txtNotContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNotEnds": "Nekončí s", "SSE.Controllers.DocumentHolder.txtNotEquals": "nerovná sa", "SSE.Controllers.DocumentHolder.txtOr": "alebo", "SSE.Controllers.DocumentHolder.txtOverbar": "Čiara nad textom", "SSE.Controllers.DocumentHolder.txtPaste": "Vložiť", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Vzorec bez hraníc", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Vzorec a šírka stĺpca", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Vložiť len formátovanie", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Vzorec a formát čísel", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Vložiť iba vzorec", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Vzorec a všetky formátovania", "SSE.Controllers.DocumentHolder.txtPasteLink": "Vložiť odkaz", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Prepojený obrázok", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Zlúčiť podmienené formátovanie", "SSE.Controllers.DocumentHolder.txtPastePicture": "Obrázok", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formátovanie zdroja", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Premiestňovať", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Hodnota + všetky formátovania", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Hodnota + form<PERSON>t <PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValues": "Vložiť iba hodnotu", "SSE.Controllers.DocumentHolder.txtPercent": "Percento", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Znovu automaticky zväčšiť tabuľku", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Odstrániť zlomok", "SSE.Controllers.DocumentHolder.txtRemLimit": "Odstrániť limitu", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Odstrániť znak akcentu", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Odstrániť vodorovnú čiaru", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Chcete odstrániť tento podpis?<br><PERSON>to krok je nezvrat<PERSON>ý.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Odstrániť skripty", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Odstrániť dolný index", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Odstrániť horný index", "SSE.Controllers.DocumentHolder.txtRowHeight": "Výška riadku", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Zápisy za textom", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "<PERSON><PERSON><PERSON><PERSON> pred textom", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Zobraziť dolnú hranicu", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Zobraziť konečné z<PERSON>vorky", "SSE.Controllers.DocumentHolder.txtShowDegree": "Zobraziť stupeň", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Zobraziť začiatočné zátvorky", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Zobraziť vlastníka", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Zobraziť hornú hranicu", "SSE.Controllers.DocumentHolder.txtSorting": "Zora<PERSON>nie", "SSE.Controllers.DocumentHolder.txtSortSelected": "Zoradiť vybrané", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Zložená zátvorka", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Zadajte iba tento riadok špecifikovaného stĺpca", "SSE.Controllers.DocumentHolder.txtTop": "Hore", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> počet riadkov pre tabuľku alebo zadané stĺpce tabuľky", "SSE.Controllers.DocumentHolder.txtUnderbar": "Čiara pod textom", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Vrátiť späť automatické rozbalenie tabuľky", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Použite sprievodcu importom textu", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Kliknutie na tento odkaz môže byť škodlivé pre Vaše zariadenie a Vaše dáta.<br><PERSON><PERSON> si istý, že chcete pokračovať?", "SSE.Controllers.DocumentHolder.txtWidth": "Šírka", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Databáza", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Dátum a čas", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Inžinierstvo", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finančn<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informácie", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 použit<PERSON><PERSON>ledy", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logické", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Vyhľadávanie a referencie", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematika a trigonometria", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Štatistické", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text a dáta", "SSE.Controllers.LeftMenu.newDocumentTitle": "Nepomenovaný zošit", "SSE.Controllers.LeftMenu.textByColumns": "Podľa stĺpcov", "SSE.Controllers.LeftMenu.textByRows": "Podľ<PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "Vzorce", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON> o<PERSON><PERSON> b<PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Načítavanie histórie verzií ...", "SSE.Controllers.LeftMenu.textLookin": "Prezrieť ", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON>, sa ne<PERSON><PERSON>. Prosím, upravte možnosti vyhľadávania.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Nahradenie bolo uskutočnené. {0} v<PERSON><PERSON><PERSON> bolo presko<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Vyhľadávanie bolo us<PERSON>. Nahradené udalosti: {0}", "SSE.Controllers.LeftMenu.textSearch": "Hľadať", "SSE.Controllers.LeftMenu.textSheet": "List", "SSE.Controllers.LeftMenu.textValues": "Hodnoty", "SSE.Controllers.LeftMenu.textWarning": "Upozornenie", "SSE.Controllers.LeftMenu.textWithin": "V rámci", "SSE.Controllers.LeftMenu.textWorkbook": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ak budete pokračovať v ukladaní v tomto formáte, všetky funkcie okrem textu sa stratia.<br><PERSON><PERSON> si istý, že chcete pokračovať?", "SSE.Controllers.Main.confirmMoveCellRange": "Rozsah cieľových buniek môže obsahovať údaje. Pokračovať v operácii?", "SSE.Controllers.Main.confirmPutMergeRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dáta obsahovali zlúčené bunky.<br><PERSON><PERSON><PERSON><PERSON>, ako boli vložené do tabuľky, boli rozpojené.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Vzorce v riadku hlavičky budú odstránené a skonvertované na statický text.<br>Chcete pokračovať?", "SSE.Controllers.Main.convertationTimeoutText": "Prekročený čas konverzie.", "SSE.Controllers.Main.criticalErrorExtText": "Stlačte \"OK\" pre návrat do zoznamu dokumentov.", "SSE.Controllers.Main.criticalErrorTitle": "Chyba", "SSE.Controllers.Main.downloadErrorText": "Sťahovanie zlyhalo.", "SSE.Controllers.Main.downloadTextText": "Načítavanie tabuľky...", "SSE.Controllers.Main.downloadTitleText": "Načítavanie tabuľky", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnoty neboli n<PERSON>", "SSE.Controllers.Main.errorAccessDeny": "Pokúšate sa vykonať akciu, na ktorú nemáte práva.<br>Prosím, kontaktujte svojho správcu dokumentového servera. ", "SSE.Controllers.Main.errorArgsRange": "Chyba v zadanom vzorci. <br> Používa sa nesprávny rozsah argumentov.", "SSE.Controllers.Main.errorAutoFilterChange": "Operácia nie je povolená, pretože sa pokúša posunúť bunky do tabuľky na pracovnom hárku.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operácia nemôže byť vykonaná pre vybran<PERSON> bunky, pretože nemôžete presunúť časť tabuľky.<br><PERSON><PERSON><PERSON><PERSON> iný rozsah údajov tak, aby sa celá tabuľka posunula a skúste to znova.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operáciu nemožno vykonať pre vybraný rozsah buniek.<br><PERSON><PERSON><PERSON><PERSON> j<PERSON> d<PERSON> roz<PERSON>, in<PERSON> a<PERSON>, a skúste to znova.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operáciu <PERSON> v<PERSON>, pretože oblasť obsahuje filtrované bunky.<br>Odk<PERSON>te filtrované prvky a skúste to znova.", "SSE.Controllers.Main.errorBadImageUrl": "Adresa URL obrázku je nesprávna", "SSE.Controllers.Main.errorCannotUngroup": "Nemožno zrušiť zoskupenie. Pre začatie náčrtu, vyberte podrobné riadky alebo stĺpce a zoskupte ich.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Tento príkaz nemožno použiť na zabezpečený list. Pre použitie príkazu, zrušte zabezpečenie listu.<br><PERSON><PERSON><PERSON><PERSON> <PERSON>ť vyžadované he<PERSON>lo. ", "SSE.Controllers.Main.errorChangeArray": "Nie je možné meniť časť poľa.", "SSE.Controllers.Main.errorChangeFilteredRange": "Týmto sa zmení filtrovaný rozsah vo vašom hárku.<br>Ak chcete dokončiť túto <PERSON>, odstráňte automatické filtre.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "<PERSON><PERSON><PERSON>, alebo graf, k<PERSON><PERSON> sa pokúšate zmeniť je na zabezpečenom liste. Pre uskutočnenie zmien, vypnite zabezpečenie listu. Môže byť vyžadované he<PERSON>lo.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Serverové pripojenie sa stratilo. Práve teraz nie je možné dokument upravovať.", "SSE.Controllers.Main.errorConnectToServer": "Dokument sa nepodarilo uložiť. Prosím, skontrolujte nastavenia pripojenia alebo kontaktujte správcu.<br>Po kliknutí na tlačidlo 'OK' sa zobrazí výzva na prevzatie dokumentu.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Tento príkaz sa nedá použiť s viacerými výbermi.<br><PERSON><PERSON><PERSON><PERSON> jeden roz<PERSON>h a skúste to znova.", "SSE.Controllers.Main.errorCountArg": "Chyba v zadanom vzorci.<br>Používa sa nesprávny počet argumentov.", "SSE.Controllers.Main.errorCountArgExceed": "Chyba v zadanom vzorci.<br>Počet argumentov je prekročený.", "SSE.Controllers.Main.errorCreateDefName": "Existujúce pomenované rozsahy nemožno upraviť a nové nemôžu byť momentálne vytvorené<br>, keďže niektoré z nich sú práve editované.", "SSE.Controllers.Main.errorDatabaseConnection": "Externá chyba.<br>Chyba spojenia databázy. Prosím, kontaktujte podporu ak chyba pretrváva. ", "SSE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON> p<PERSON> zaš<PERSON>rov<PERSON> z<PERSON>, ne<PERSON>žno ich dekódovať.", "SSE.Controllers.Main.errorDataRange": "Nesprávny rozsah <PERSON>v.", "SSE.Controllers.Main.errorDataValidate": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON>, nie je p<PERSON>.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> má obmed<PERSON>é <PERSON>, ktor<PERSON> je možné zadať do tejto bunky.", "SSE.Controllers.Main.errorDefaultMessage": "<PERSON><PERSON><PERSON>: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Pokúšate sa zmazať stĺpec, ktorý obsahuje uzamknutú bunku. Uzamknuté bunky nemôžu byť zmazan<PERSON>, pokiaľ je list zabezpečený.<br>Pre odstránenie uzamknutej bunky, vypnite zabezpečenie listu. Môže byť vyžadované he<PERSON>lo.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Pokúšate sa zmazať riadok, ktorý obsahuje uzamknuté bunky. Uzamknuté bunky môžu byť zmazan<PERSON>, pokiaľ je list zabezpečený.<br>Pre odstránenie uzamknutia bunky, vypnite zabezpečenie listu. Môže byť vyžadované he<PERSON>lo.", "SSE.Controllers.Main.errorEditingDownloadas": "Pri práci s dokumentom došlo k chybe.<br>Použite voľbu \"Stiahnuť ako\" a uložte si záložnú kópiu súboru na svoj počítač.", "SSE.Controllers.Main.errorEditingSaveas": "Pri práci s dokumentom došlo k chybe.<br>Použite voľbu \"Uložiť ako...\" a uložte si záložnú kópiu súboru na svoj počítač.", "SSE.Controllers.Main.errorEditView": "Existujúce zobrazenie hárka nie je možné upravovať a nové nemožno momentálne vytvárať, pretože niektoré z nich sa upravujú.", "SSE.Controllers.Main.errorEmailClient": "Nenašiel sa žiadny emailový klient.", "SSE.Controllers.Main.errorFilePassProtect": "Dokument je chránený heslom a nie je možné ho otvoriť.", "SSE.Controllers.Main.errorFileRequest": "Externá chyba.<br>Chyba požiadavky súboru. Ak chyba pretrváva, kontaktujte podporu.", "SSE.Controllers.Main.errorFileSizeExceed": "Veľkosť súboru prekračuje limity vášho servera.<br> Kontaktujte prosím vášho správcu dokumentového servera o ďalšie podrobnosti.", "SSE.Controllers.Main.errorFileVKey": "Externá chyba.<br>Nesprávny bezpečnostný kľúč. Ak chyba pretrváva, kontaktujte podporu.", "SSE.Controllers.Main.errorFillRange": "Nepodarilo sa vyplniť vybraný rozsah buniek.<br>Všetky zlúčené bunky musia mať rovnakú veľkosť.", "SSE.Controllers.Main.errorForceSave": "Pri ukladaní súboru sa vyskytla chyba. Ak chcete súbor uložiť na pevný disk počítača, použite možnosť 'Prevziať ako' alebo to skúste znova neskôr.", "SSE.Controllers.Main.errorFormulaName": "Chyba v zadanom vzorci. <br> Používa sa nesprávny názov vzorca.", "SSE.Controllers.Main.errorFormulaParsing": "Interná chyba pri analýze vzorca.", "SSE.Controllers.Main.errorFrmlMaxLength": "Dĺžka vášho vzorca presahuje limit 8192 znakov.<br>Upravte ho a skúste to znova.", "SSE.Controllers.Main.errorFrmlMaxReference": "Vzorec nemôžete vložiť, pretože má priveľa hodnôt,<br>odkazov na bunky, a/alebo názvov.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Textové hodnoty vo vzorcoch sú obmedzené na 255 znakov.<br>Použite operáciu CONCATENATE alebo operátor zreťazenia (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funkcia sa týka listu, k<PERSON><PERSON> neexistuje.<br>Skontrolujte prosím údaje a skúste to znova.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operácia sa nedala dokončiť pre zvolený rozsah buniek.<br><PERSON><PERSON><PERSON><PERSON> roz<PERSON> tak, aby prvý riadok tabuľky bol na rovnakom riadku<br>a výsledná tabuľka sa prekrývala s aktuálnou.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operácia sa nedala dokončiť pre zvolený rozsah buniek.<br><PERSON><PERSON><PERSON><PERSON> rozsah, k<PERSON><PERSON> nezahŕňa iné tabuľky.", "SSE.Controllers.Main.errorInvalidRef": "Zadajte správny názov pre výber alebo platný odkaz, na ktorý chcete prejsť.", "SSE.Controllers.Main.errorKeyEncrypt": "Neznámy <PERSON>", "SSE.Controllers.Main.errorKeyExpire": "Kľúč deskriptora vypršal", "SSE.Controllers.Main.errorLabledColumnsPivot": "Ak chcete vytvoriť kontingenčnú tabuľku, p<PERSON><PERSON><PERSON> ú<PERSON>, ktoré sú usporiadané ako zoznam s označenými stĺpcami.", "SSE.Controllers.Main.errorLoadingFont": "Fonty sa nenahrali.<br>Kontaktujte prosím svojho administrátora Servera dokumentov.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Odkaz na umiestnenie alebo rozsah údajov nie je platný.", "SSE.Controllers.Main.errorLockedAll": "Operáciu nemožno vykonať, pretože list bol zamknutý iným používateľom.", "SSE.Controllers.Main.errorLockedCellPivot": "Nemôžete meniť údaje v kontingenčnej tabuľke.", "SSE.Controllers.Main.errorLockedWorksheetRename": "List nemôže by<PERSON> <PERSON><PERSON><PERSON>e <PERSON>, preto<PERSON>e je premenovan<PERSON> iným používateľom", "SSE.Controllers.Main.errorMaxPoints": "Maximálny počet bodov v sérii na jeden graf je 4096.", "SSE.Controllers.Main.errorMoveRange": "Nie je možné zmeniť časť zlúčenej bunky", "SSE.Controllers.Main.errorMoveSlicerError": "Prierezy tabuľky nemôžou byť skopírované z jedného zošitu do druhého.<br>Akciu opakujte vybraním celej tabuľky vrátane prierezov.", "SSE.Controllers.Main.errorMultiCellFormula": "V tabuľkách nie sú dovolené vzorce pre pole s via<PERSON><PERSON><PERSON> bunkami", "SSE.Controllers.Main.errorNoDataToParse": "Neboli vybrané žiadne dáta pre spracovanie.", "SSE.Controllers.Main.errorOpenWarning": "Jeden zo vzorcov súboru prekračuje limit 8192 znakov.<br>Vzorec bol odstránený.", "SSE.Controllers.Main.errorOperandExpected": "Zadaná funkcia syntax nie je správna. Skontrolujte prosím, či chýba jedna zo zátvoriek-'(' alebo ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>lo nie je správne.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že máte vypnutý CAPS LOCK a správnu veľkosť písmen.", "SSE.Controllers.Main.errorPasteMaxRange": "Oblasť kopírovania a prilepovania sa nezhoduje.<br><PERSON>sím, vyberte oblasť s rovnakou veľkosťou alebo kliknite na prvú bunku v rade a vložte skopírované bunky.", "SSE.Controllers.Main.errorPasteMultiSelect": "T<PERSON><PERSON> a<PERSON>u nemožno vykonať s výberom via<PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON><PERSON> jeden roz<PERSON> a skúste to znova.", "SSE.Controllers.Main.errorPasteSlicerError": "Prierezy tabuľky nemôžu byť skopírované z jedného zošitu do druhého.", "SSE.Controllers.Main.errorPivotGroup": "Vybrané objekty nie je možné z<PERSON>", "SSE.Controllers.Main.errorPivotOverlap": "Výstup z kontingenčnej tabuľky nesmie prekrývať tabuľku.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Prehľad kontingenčnej tabuľky bol uložený bez základných údajov.<br>Na aktualizáciu prehľadu použite tlačidlo „Obnoviť“.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nie je možné v aktuálnej verzii programu vytlačiť viac ako 1500 strán naraz.<br>Toto obmedzenie bude odstránené v najbližších vydaniach.", "SSE.Controllers.Main.errorProcessSaveResult": "Ukladanie zlyhalo", "SSE.Controllers.Main.errorServerVersion": "Verzia editora bola aktualizovaná. Stránka sa opätovne načíta, aby sa vykonali zmeny.", "SSE.Controllers.Main.errorSessionAbsolute": "<PERSON><PERSON><PERSON> edit<PERSON>cie dokumentu vypršal. Prosím, načítajte stránku z<PERSON>.", "SSE.Controllers.Main.errorSessionIdle": "Dokument nebol dlho upravovaný. Prosím, načítajte stránku z<PERSON>.", "SSE.Controllers.Main.errorSessionToken": "Spojenie so serverom bolo pre<PERSON>. Prosím, načítajte stránku z<PERSON>.", "SSE.Controllers.Main.errorSetPassword": "<PERSON><PERSON><PERSON> by<PERSON> použ<PERSON>", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Umiestnenie odkazu je chybné, preto<PERSON>e bunky nie sú na rovnakom riadku ale stĺpci.<br>Vyberte bunky na rovnakom riadku alebo stĺpci.", "SSE.Controllers.Main.errorStockChart": "Nesprávne poradie riadkov. Ak chcete vytvoriť burzový graf, umiestnite údaje na hárok v nasledujúcom poradí:<br> z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena, max cena, min cena, konečná cena.", "SSE.Controllers.Main.errorToken": "Rámec platnosti zabezpečenia dokumentu nie je správne vytvorený.<br><PERSON>s<PERSON><PERSON>, kontaktujte svojho správcu dokumentového servera. ", "SSE.Controllers.Main.errorTokenExpire": "Rámec platnosti zabezpečenia dokumentu vypršal.<br>Prosím, kontaktujte svojho správcu dokumentového servera. ", "SSE.Controllers.Main.errorUnexpectedGuid": "Externá chyba.<br>Neočakávaná GUID. Ak chyba pretrváva, kontaktujte podporu.", "SSE.Controllers.Main.errorUpdateVersion": "Verzia súboru bola zmenená. Stránka sa znova načíta.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internetové pripojenie bolo obnovené a verzia súboru bola zmenená.<br><PERSON><PERSON><PERSON><PERSON>, než budete pokračovať v práci, potrebujete si stiahnuť súbor alebo kópiu jeho obsahu, aby sa nič nestratilo. Potom znovu načítajte stránku.", "SSE.Controllers.Main.errorUserDrop": "K súboru nie je možné práve teraz získať prístup.", "SSE.Controllers.Main.errorUsersExceed": "Počet používateľov povolených cenovým plánom bol prekročený", "SSE.Controllers.Main.errorViewerDisconnect": "Spojenie so serverom je prerušené. Dokument môžete zobraziť,<br>ale nemôžete ho stiahn<PERSON>ť ani v<PERSON>lačiť, kým sa spojenie neobnoví a stránka sa znovu nenačíta.", "SSE.Controllers.Main.errorWrongBracketsCount": "Chyba v zadanom vzorci.<br>Používa sa nesprávny počet zátvoriek.", "SSE.Controllers.Main.errorWrongOperator": "Chyba v zadanom vzorci. Používa sa nesprávny operátor. <br> Prosím, opravte chybu.", "SSE.Controllers.Main.errorWrongPassword": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>lo nie je správne.", "SSE.Controllers.Main.errRemDuplicates": "<PERSON><PERSON> a z<PERSON><PERSON> duplicitné hodnoty: {0}, ostáva neopakujúcich sa hodnôt: {1}.", "SSE.Controllers.Main.leavePageText": "V tejto tabuľke máte neuložené zmeny. Kliknite na položku 'Zostať na tejto stránke' a následne na položku 'Uložiť' aby ste uložili zmeny. Kliknutím na položku 'Opustiť  túto stránku' odstránite všetky neuložené zmeny.", "SSE.Controllers.Main.leavePageTextOnClose": "Všetky neuložené zmeny v tomto zošite budú stratené.<br>Pokiaľ ich chcete uložiť, kliknite na \"Storno\" a potom \"Uložiť\". Pokiaľ chcete všetky neuložené zmeny zahodiť, kliknite na \"OK\".", "SSE.Controllers.Main.loadFontsTextText": "Načítavanie dát...", "SSE.Controllers.Main.loadFontsTitleText": "Načítavanie <PERSON>", "SSE.Controllers.Main.loadFontTextText": "Načítavanie dát...", "SSE.Controllers.Main.loadFontTitleText": "Načítavanie <PERSON>", "SSE.Controllers.Main.loadImagesTextText": "Načítavanie o<PERSON>zkov...", "SSE.Controllers.Main.loadImagesTitleText": "Načítani<PERSON>", "SSE.Controllers.Main.loadImageTextText": "Načítanie obrázku ..", "SSE.Controllers.Main.loadImageTitleText": "Načítavanie obrázku", "SSE.Controllers.Main.loadingDocumentTitleText": "Načítanie zošitu", "SSE.Controllers.Main.notcriticalErrorTitle": "Upozornenie", "SSE.Controllers.Main.openErrorText": "Pri otváraní súboru sa vyskytla chyba.", "SSE.Controllers.Main.openTextText": "Otváranie zošitu...", "SSE.Controllers.Main.openTitleText": "Otváranie zošitu", "SSE.Controllers.Main.pastInMergeAreaError": "Nie je možné zmeniť časť zlúčenej bunky", "SSE.Controllers.Main.printTextText": "Tlač zošitu...", "SSE.Controllers.Main.printTitleText": "Tlač zošitu", "SSE.Controllers.Main.reloadButtonText": "Obnoviť stránku", "SSE.Controllers.Main.requestEditFailedMessageText": "Niekto tento dokument práve upravuje. Skúste neskôr prosím.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON>rís<PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "Pri ukladaní súboru sa vyskytla chyba.", "SSE.Controllers.Main.saveErrorTextDesktop": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON>, alebo v<PERSON><br><PERSON><PERSON><PERSON><PERSON>:<br>1.<PERSON><PERSON><PERSON> je možne použiť iba na čítanie.<br>2. <PERSON><PERSON><PERSON> je editovaný iným užívateľom.<br>3.<PERSON><PERSON><PERSON><PERSON><PERSON> je pln<PERSON>, alebo poš<PERSON>.", "SSE.Controllers.Main.saveTextText": "Ukladanie zošitu...", "SSE.Controllers.Main.saveTitleText": "Ukladanie zošitu", "SSE.Controllers.Main.scriptLoadError": "Spojenie je p<PERSON><PERSON><PERSON><PERSON>, niektoré komponenty nemožno nahrať. Obnovte prosím stránku.", "SSE.Controllers.Main.textAnonymous": "Anonymný", "SSE.Controllers.Main.textApplyAll": "Použiť na všetky rovnice", "SSE.Controllers.Main.textBuyNow": "Navštíviť webovú stránku", "SSE.Controllers.Main.textChangesSaved": "Všetky zmeny boli uložené", "SSE.Controllers.Main.textClose": "Zatvoriť", "SSE.Controllers.Main.textCloseTip": "Kliknutím zavrite tip", "SSE.Controllers.Main.textConfirm": "Potvrdenie", "SSE.Controllers.Main.textContactUs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "Táto rovnica bola vytvorená starou verziou editora rovníc, ktor<PERSON> už nie je podporovaná. Pre jej upravenie, preveďte rovnicu do formátu Office Math ML.<br>Previesť teraz?", "SSE.Controllers.Main.textCustomLoader": "Majte na pamäti, že podľa podmienok licencie nie ste oprávnený meniť načítač.<br>Pre získanie ponuky sa obráťte na naše obchodné oddelenie.", "SSE.Controllers.Main.textDisconnect": "Spojenie sa stratilo", "SSE.Controllers.Main.textFillOtherRows": "Vyplniť ostatné riadky", "SSE.Controllers.Main.textFormulaFilledAllRows": "Vzorec zadaný v {0} riadkoch obsahuje dáta. Doplnenie dát do ostatných riadkov, môže trvať niekoľko minút.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Vzorec vyplnil prvých {0} riadkov. Vyplnenie ďalších prázdnych riadkov môže trvať niekoľko minút.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Vzorec vyplnený iba v prvých {0} riadkoch obsahuje údaje z dôvodu šetrenia pamäte. V tomto hárku je ďalších {1} riadkov s údajmi. Môžete ich vyplniť ručne.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Vzorec vyplnil iba prvých {0} <PERSON><PERSON><PERSON><PERSON>, z dôvodu ušetrenia pamäte. Ostatné riadky v tomto hárku neobsahujú údaje.", "SSE.Controllers.Main.textGuest": "Hosť", "SSE.Controllers.Main.textHasMacros": "<PERSON><PERSON><PERSON> o<PERSON> makrá.<br>Chcete spustiť makrá?", "SSE.Controllers.Main.textLearnMore": "Zistiť viac", "SSE.Controllers.Main.textLoadingDocument": "Načítanie zošitu", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON> meno, k<PERSON><PERSON><PERSON> dĺžka je menej ako 128 znakov.", "SSE.Controllers.Main.textNeedSynchronize": "Máte aktualizácie", "SSE.Controllers.Main.textNo": "<PERSON><PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "Bo<PERSON> dos<PERSON> limit licencie", "SSE.Controllers.Main.textPaidFeature": "Platená funkcia", "SSE.Controllers.Main.textPleaseWait": "Operácia môže trvať dlhšie, než sa očakávalo. Prosím čakajte...", "SSE.Controllers.Main.textReconnect": "Spojenie sa obnovilo", "SSE.Controllers.Main.textRemember": "Zapamätaj si moju voľbu pre všetky súbory", "SSE.Controllers.Main.textRenameError": "Meno užívateľa nesmie byť prázdne.", "SSE.Controllers.Main.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON>o, k<PERSON><PERSON> sa bude p<PERSON>žívať pre spoluprá<PERSON>", "SSE.Controllers.Main.textShape": "<PERSON><PERSON>", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textText": "Text", "SSE.Controllers.Main.textTryUndoRedo": "Funkcie späť/zopakovať sú vypnuté pre rýchly spolueditačný režim.<br>Kliknite na tlačítko \"Prísny režim\", aby ste prešli do prísneho spolueditačného režimu a aby ste upravovali súbor bez rušenia ostatných užívateľov a odosielali Vaše zmeny iba po ich uložení. Pomocou Rozšírených nastavení editoru môžete prepínať medzi spolueditačnými režimami.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Funkcia Späť/Znova sú vypnuté pre rých<PERSON> rež<PERSON> s<PERSON>. ", "SSE.Controllers.Main.textYes": "Á<PERSON>", "SSE.Controllers.Main.titleLicenseExp": "Platnosť licencie uplynula", "SSE.Controllers.Main.titleServerVersion": "Editor bol a<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(všetko)", "SSE.Controllers.Main.txtArt": "V<PERSON>š text tu", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON><PERSON><PERSON> tvary", "SSE.Controllers.Main.txtBlank": "(prázdne)", "SSE.Controllers.Main.txtButtons": "Tlačidlá", "SSE.Controllers.Main.txtByField": "%1 z %2", "SSE.Controllers.Main.txtCallouts": "Popisky obrázku", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Vyčistiť filter", "SSE.Controllers.Main.txtColLbls": "Značky stĺpcov", "SSE.Controllers.Main.txtColumn": "Stĺpec", "SSE.Controllers.Main.txtConfidential": "Dôverné", "SSE.Controllers.Main.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDays": "Dni", "SSE.Controllers.Main.txtDiagramTitle": "Názov grafu", "SSE.Controllers.Main.txtEditingMode": "Nastaviť režim <PERSON>...", "SSE.Controllers.Main.txtErrorLoadHistory": "Načítavanie histórie zlyhalo", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "S<PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Celkový súčet", "SSE.Controllers.Main.txtGroup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtLines": "Čiary", "SSE.Controllers.Main.txtMath": "Matematika", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Viacnásobný výber", "SSE.Controllers.Main.txtOr": "%1 alebo %2", "SSE.Controllers.Main.txtPage": "Strán<PERSON>", "SSE.Controllers.Main.txtPageOf": "Stránka %1 z %2", "SSE.Controllers.Main.txtPages": "Strany", "SSE.Controllers.Main.txtPreparedBy": "Pripravil(a)", "SSE.Controllers.Main.txtPrintArea": "Oblasť_tlače", "SSE.Controllers.Main.txtQuarter": "Štvrtina", "SSE.Controllers.Main.txtQuarters": "Štvrtiny", "SSE.Controllers.Main.txtRectangles": "Obdĺžniky", "SSE.Controllers.Main.txtRow": "Riadok", "SSE.Controllers.Main.txtRowLbls": "Štítky riadku", "SSE.Controllers.Main.txtSeconds": "Sekundy", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Bublina s čiarou 1 (ohraničenie a akcentový pruh)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Bublina s čiarou 2 (ohraničenie a zvýraznenie)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Bublina s čiarou 3 (Ohraničenie a zvýraznenie)", "SSE.Controllers.Main.txtShape_accentCallout1": "Bublina s čiarou 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "Bublina s čiarou 2 (zvýraznenie)", "SSE.Controllers.Main.txtShape_accentCallout3": "Bublina s čiarou 3 (zvýraznená)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tlačítko S<PERSON>ť alebo Predchádzajúce", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Tlačít<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Prázdne tlačítko", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Tlačítko Dokument", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Tlač<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Tlačítko vpred alebo ďalej", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Tlačítko Informá<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Tlačítko Film", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Tlačítko <PERSON>v<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "Tlačítko Zvuk", "SSE.Controllers.Main.txtShape_arc": "Oblúk", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Kolenový konektor", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Kolenový konektor so šípkou", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Kolenový dvojšípkový konektor", "SSE.Controllers.Main.txtShape_bentUpArrow": "Šípka ohnutá hore", "SSE.Controllers.Main.txtShape_bevel": "Skosenie", "SSE.Controllers.Main.txtShape_blockArc": "Časť kruhu", "SSE.Controllers.Main.txtShape_borderCallout1": "Bublina s čiarou 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Bublina s čiarou 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Bublina s čiarou 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>vor<PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Bublina s čiarou 1 (bez ohraničenia)", "SSE.Controllers.Main.txtShape_callout2": "Bublina s čiarou 2 (bez orámovania)", "SSE.Controllers.Main.txtShape_callout3": "Bublina s čiarou 3 (bez orámovania)", "SSE.Controllers.Main.txtShape_can": "Môže", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Akord", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloud": "Cloud", "SSE.Controllers.Main.txtShape_cloudCallout": "Upozornenie na cloud", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Zakrivený konektor", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Konektor v tvare zakrivenej šípky", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Konektor v tvare zakrivenej dvojitej šípky", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Šípka zahnutá nadol", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Šípka zahnutá doľava", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Šípka zahnutá doprava", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Šípka zahnutá nahor", "SSE.Controllers.Main.txtShape_decagon": "Desaťuhoľník", "SSE.Controllers.Main.txtShape_diagStripe": "Priečny prúžok", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Dvanásťuhoľník", "SSE.Controllers.Main.txtShape_donut": "Šiška", "SSE.Controllers.Main.txtShape_doubleWave": "Dvojitá vlnovka", "SSE.Controllers.Main.txtShape_downArrow": "Šíp<PERSON> dole", "SSE.Controllers.Main.txtShape_downArrowCallout": "Vyvolanie šípky nadol", "SSE.Controllers.Main.txtShape_ellipse": "Elipsa", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Pruh zahnutý nadol", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Pruh zahnutý nahor", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Vývojový diagram: Vystriedať proces", "SSE.Controllers.Main.txtShape_flowChartCollate": "Vývojový diagram: Collate", "SSE.Controllers.Main.txtShape_flowChartConnector": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDelay": "Vývojový diagram: Ones<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Vývojový diagram: Zobraziť", "SSE.Controllers.Main.txtShape_flowChartDocument": "Vývojový diagram: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartExtract": "Vývojový diagram: Extrahovať", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Vývojový diagram: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Vývojový diagram: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Vývojový diagram: Magnetický disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Vývojový diagram: Úložisko s priamym prístupom", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Vývojový diagram: Úložisko s postupným prístupom", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Vývojový diagram: ru<PERSON><PERSON><PERSON> vstup", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Vývojový diagram: Man<PERSON><PERSON>lna operácia", "SSE.Controllers.Main.txtShape_flowChartMerge": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Vývojový diagram: Viacnásobný dokument", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Vývojový diagram: Spojovací prvok mimo str<PERSON>ky", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartOr": "Vývojový diagram: alebo", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> proces ", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartProcess": "Vývojový diagram: proces", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Vývojový diagram: Karta", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Vývojový diagram: <PERSON><PERSON><PERSON> s <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Vývojový diagram: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uzol", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Vývojový diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON> roh", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "Polovič<PERSON><PERSON> rá<PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Päťuholník", "SSE.Controllers.Main.txtShape_hexagon": "Šesťuholník", "SSE.Controllers.Main.txtShape_homePlate": "Päťuholník", "SSE.Controllers.Main.txtShape_horizontalScroll": "Horizontálny posuvník", "SSE.Controllers.Main.txtShape_irregularSeal1": "Výbuch 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Výbuch 2", "SSE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Vyvolanie šípky vľavo", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON>av<PERSON> zložená zátvorka", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Šípka vľavo vpravo", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Vyvolanie šípky vľavo vpravo", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Šípka doľava doprava nahor", "SSE.Controllers.Main.txtShape_leftUpArrow": "Šípka doľava nahor", "SSE.Controllers.Main.txtShape_lightningBolt": "Blesk", "SSE.Controllers.Main.txtShape_line": "Čiara", "SSE.Controllers.Main.txtShape_lineWithArrow": "Šípka", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "Rozdelenie", "SSE.Controllers.Main.txtShape_mathEqual": "Rovná sa", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Viacnásobný", "SSE.Controllers.Main.txtShape_mathNotEqual": "Nerovná sa", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "Mesiac", "SSE.Controllers.Main.txtShape_noSmoking": "Symbol \"Nie\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Šípka v pravo so zárezom", "SSE.Controllers.Main.txtShape_octagon": "Ose<PERSON><PERSON>ník", "SSE.Controllers.Main.txtShape_parallelogram": "Rovnobežník", "SSE.Controllers.Main.txtShape_pentagon": "Päťuholník", "SSE.Controllers.Main.txtShape_pie": "Koláčový graf", "SSE.Controllers.Main.txtShape_plaque": "Podpísať", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Čmárať", "SSE.Controllers.Main.txtShape_polyline2": "Voľná forma", "SSE.Controllers.Main.txtShape_quadArrow": "Štvorstranná šípka", "SSE.Controllers.Main.txtShape_quadArrowCallout": "<PERSON><PERSON><PERSON> so štvorstrannou šípkou", "SSE.Controllers.Main.txtShape_rect": "Obdĺžnik", "SSE.Controllers.Main.txtShape_ribbon": "Pruh nadol", "SSE.Controllers.Main.txtShape_ribbon2": "Pásik hore", "SSE.Controllers.Main.txtShape_rightArrow": "Pravá šípka", "SSE.Controllers.Main.txtShape_rightArrowCallout": "B<PERSON>lina so šípkou vpravo", "SSE.Controllers.Main.txtShape_rightBrace": "Pravá svorka", "SSE.Controllers.Main.txtShape_rightBracket": "Pravá zátvorka", "SSE.Controllers.Main.txtShape_round1Rect": "Obdĺžnik s jedn<PERSON>m oblým rohom", "SSE.Controllers.Main.txtShape_round2DiagRect": "Obdĺžnik s <PERSON><PERSON><PERSON><PERSON><PERSON> pro<PERSON> rohmi", "SSE.Controllers.Main.txtShape_round2SameRect": "Obdĺžnik s oblými rohmi na rovnakej strane", "SSE.Controllers.Main.txtShape_roundRect": "Obdĺžnik s obl<PERSON><PERSON> rohmi", "SSE.Controllers.Main.txtShape_rtTriangle": "Pravý trojuholník", "SSE.Controllers.Main.txtShape_smileyFace": "Smajlík", "SSE.Controllers.Main.txtShape_snip1Rect": "Obdĺžnik s jedným odstrihnutým rohom", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Obdĺžnik s dvoma protiľahlými odstrihnutými rohmi", "SSE.Controllers.Main.txtShape_snip2SameRect": "Obdĺžnik s dvoma odstrihnutými rohmi na rovnakej strane ", "SSE.Controllers.Main.txtShape_snipRoundRect": "Obdĺžnik s jedným zaobleným a jedným odstrihnutým rohom hore", "SSE.Controllers.Main.txtShape_spline": "Krivka", "SSE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Prúžkovaná šípka vpravo", "SSE.Controllers.Main.txtShape_sun": "Ne", "SSE.Controllers.Main.txtShape_teardrop": "Slza", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON><PERSON> pole", "SSE.Controllers.Main.txtShape_trapezoid": "Lichobežník", "SSE.Controllers.Main.txtShape_triangle": "Trojuholník", "SSE.Controllers.Main.txtShape_upArrow": "Šípka hore", "SSE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON> so ší<PERSON>u hore", "SSE.Controllers.Main.txtShape_upDownArrow": "Šípka hore a dole", "SSE.Controllers.Main.txtShape_uturnArrow": "Šípka s otočkou", "SSE.Controllers.Main.txtShape_verticalScroll": "Vertikálne posúvanie", "SSE.Controllers.Main.txtShape_wave": "Vlnka", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON> bublina", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Obdĺžniková bublina", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Bublina v tvare obdĺžnika so zaoblenými rohmi", "SSE.Controllers.Main.txtStarsRibbons": "Hviez<PERSON> a stuhy", "SSE.Controllers.Main.txtStyle_Bad": "Zlý/chybný", "SSE.Controllers.Main.txtStyle_Calculation": "Ka<PERSON>ulá<PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Skontrolovať bunku", "SSE.Controllers.Main.txtStyle_Comma": "Čiarka", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> text", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Nadpis 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Nadpis 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Nadpis 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Nadpis 4", "SSE.Controllers.Main.txtStyle_Input": "Vstup/vstup<PERSON> jed<PERSON>ka", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Spojená bunka", "SSE.Controllers.Main.txtStyle_Neutral": "Neutrálny", "SSE.Controllers.Main.txtStyle_Normal": "Normálny", "SSE.Controllers.Main.txtStyle_Note": "Poznámka", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Percento", "SSE.Controllers.Main.txtStyle_Title": "N<PERSON>zov", "SSE.Controllers.Main.txtStyle_Total": "Celkovo", "SSE.Controllers.Main.txtStyle_Warning_Text": "Varovný text", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "Tabuľka", "SSE.Controllers.Main.txtTime": "Čas", "SSE.Controllers.Main.txtUnlock": "Odomknúť", "SSE.Controllers.Main.txtUnlockRange": "Odomknúť rozsah", "SSE.Controllers.Main.txtUnlockRangeDescription": "Pokiaľ chcete tento rozsah zmeniť, zadajte heslo:", "SSE.Controllers.Main.txtUnlockRangeWarning": "<PERSON><PERSON><PERSON><PERSON>, ktorú sa pokúšate upraviť je zabezpečená he<PERSON>lom.", "SSE.Controllers.Main.txtValues": "Hodnoty", "SSE.Controllers.Main.txtXAxis": "Os X", "SSE.Controllers.Main.txtYAxis": "Os Y", "SSE.Controllers.Main.txtYears": "Roky", "SSE.Controllers.Main.unknownErrorText": "Neznáma chyba.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> nie je pod<PERSON>ý.", "SSE.Controllers.Main.uploadDocExtMessage": "Nezná<PERSON> form<PERSON> dokumentu", "SSE.Controllers.Main.uploadDocFileCountMessage": "Neboli nahraté žiadne dokumenty.", "SSE.Controllers.Main.uploadDocSizeMessage": "Prekročený limit maximálnej veľkosti dokumentu.", "SSE.Controllers.Main.uploadImageExtMessage": "Nezná<PERSON> o<PERSON>.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Neboli načítané žiadne obrázky.", "SSE.Controllers.Main.uploadImageSizeMessage": "Obrázok je pr<PERSON><PERSON><PERSON> veľ<PERSON>. Maximálna veľkosť je 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Nahrávanie obrázku...", "SSE.Controllers.Main.uploadImageTitleText": "Nahrávanie obrázku", "SSE.Controllers.Main.waitText": "Prosím č<PERSON>...", "SSE.Controllers.Main.warnBrowserIE9": "Aplikácia má na IE9 slabé schopnosti. Použite IE10 alebo vyššie.", "SSE.Controllers.Main.warnBrowserZoom": "Súčasné nastavenie priblíženia nie je plne podporované prehliadačom. Obnovte štandardné priblíženie stlačením klávesov Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "Dosiahli ste limit počtu súbežných spojení %1 editora. Dokument bude otvorený len na náhľad. <br>Pre viac podrobností kontaktujte svojho správcu. ", "SSE.Controllers.Main.warnLicenseExp": "Vaša licencia vypršala.<br>Prosím, aktualizujte si svoju licenciu a obnovte stránku.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licencia vypršala.<br>K funkcii úprav dokumentu už nemáte prístup.<br>Kontaktujte svoj<PERSON> administ<PERSON><PERSON><PERSON>, prosím.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Je potrebné obnoviť licenciu.<br>K funkciám úprav dokumentov máte obmedzený prístup.<br>Pre získanie úplného prístupu kontaktujte prosím svojho administrátora.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Dosiahli ste limit %1 editora v režime spolupráce na úpravách. Ohľadne podrobnosti sa obráťte na svojho správcu. ", "SSE.Controllers.Main.warnNoLicense": "Dosiahli ste limit súběžných pripojení %1 editora. Dokument bude otvorený len na prezeranie.<br>Pre rozšírenie funkcií kontaktujte %1 obchodné oddelenie.", "SSE.Controllers.Main.warnNoLicenseUsers": "Dosiahli ste limit %1 editora. Pre rozšírenie funkcií kontaktujte %1 obchodné oddelenie.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON><PERSON> vám zamietnuté právo upravovať súbor.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON><PERSON><PERSON> listy", "SSE.Controllers.Print.textFirstCol": "Prvý stĺpec", "SSE.Controllers.Print.textFirstRow": "Prvý riadok", "SSE.Controllers.Print.textFrozenCols": "Ukotvené stĺpce", "SSE.Controllers.Print.textFrozenRows": "Ukot<PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Controllers.Print.textNoRepeat": "Neopakovať", "SSE.Controllers.Print.textRepeat": "Opakovať...", "SSE.Controllers.Print.textSelectRange": "Vybrať rozsah", "SSE.Controllers.Print.textWarning": "Upozornenie", "SSE.Controllers.Print.txtCustom": "Vlastný", "SSE.Controllers.Print.warnCheckMargings": "Okraje sú nesprávne", "SSE.Controllers.Statusbar.errorLastSheet": "Pracovný zošit musí mať aspoň jeden viditeľný pracovný list.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Pracovný list sa nedá odstrániť.", "SSE.Controllers.Statusbar.strSheet": "List", "SSE.Controllers.Statusbar.textDisconnect": "<b>Spojenie sa stratilo<b><br>Pokus o opätovné spojenie. Skontrolujte nastavenie pripojenia. ", "SSE.Controllers.Statusbar.textSheetViewTip": "Nachádzate sa v režime zobrazenia hárka. Filtre a zoradenie sú viditeľné iba pre vás a tých, ktorí sú stále v tomto zobrazení.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Nachádzate sa v režime zobrazenia hárka. Filtre sú viditeľné iba pre vás a tých, ktorí sú stále v tomto zobrazení.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Pracovný list môže mať údaje. Vykonať operáciu?", "SSE.Controllers.Statusbar.zoomText": "Priblíženie {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete uloži<PERSON>, nie je dostupné na aktuálnom zariadení.<br>Štýl textu sa zobrazí pomocou jedného zo systémových písiem, ulo<PERSON><PERSON>é písmo sa použije, keď bude k dispozícii.<br>Chcete pokračovať?", "SSE.Controllers.Toolbar.errorComboSeries": "Pre vytvorenie kombinovaného grafu, zvoľte aspoň dve skupiny dát.", "SSE.Controllers.Toolbar.errorMaxRows": "CHYBA! Maximálny počet dátových radov na graf je 255", "SSE.Controllers.Toolbar.errorStockChart": "Nesprávne poradie riadkov. Ak chcete vytvoriť burzový graf, umiestnite údaje na hárok v nasledujúcom poradí:<br> z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena, max cena, min cena, konečná cena.", "SSE.Controllers.Toolbar.textAccent": "Akcenty", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Smerové", "SSE.Controllers.Toolbar.textFontSizeErr": "Zadaná hodnota je nesprávna.<br><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> číselnú hodnotu medzi 1 a 409.", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Funkcie", "SSE.Controllers.Toolbar.textIndicator": "Indikátory", "SSE.Controllers.Toolbar.textInsert": "Vložiť", "SSE.Controllers.Toolbar.textIntegral": "Integ<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Veľké operátory", "SSE.Controllers.Toolbar.textLimitAndLog": "Limity a logaritmy", "SSE.Controllers.Toolbar.textLongOperation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textMatrix": "Mat<PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operátory", "SSE.Controllers.Toolbar.textPivot": "Kontingenčná tabuľka", "SSE.Controllers.Toolbar.textRadical": "Odmo<PERSON>niny", "SSE.Controllers.Toolbar.textRating": "Hodnotenia", "SSE.Controllers.Toolbar.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Tvary", "SSE.Controllers.Toolbar.textSymbols": "Symboly", "SSE.Controllers.Toolbar.textWarning": "Upozornenie", "SSE.Controllers.Toolbar.txtAccent_Accent": "Dĺžeň", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Pravá-ľavá šípka nad", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Ľavá šípka nad", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Pravá šípka nad", "SSE.Controllers.Toolbar.txtAccent_Bar": "Pruhov<PERSON> graf", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Čiara pod", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Čiara nad", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Vzorec v rámčeku (S voľným miestom)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Vzorec v rámčeku (Príklad)", "SSE.Controllers.Toolbar.txtAccent_Check": "Mäkčeň", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Zátvorka pod", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Zátvorka nad", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC s čiarou nad", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y s čiarou nad", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Trojbodka", "SSE.Controllers.Toolbar.txtAccent_DDot": "Dvojbodka", "SSE.Controllers.Toolbar.txtAccent_Dot": "Bodka", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Dvojitá vodorovná čiarka", "SSE.Controllers.Toolbar.txtAccent_Grave": "Opačný dĺžeň", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Znak zoskupenia pod", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Znak zoskupenia nad", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpúna doľava nad", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpúna doprava nad", "SSE.Controllers.Toolbar.txtAccent_Hat": "Strieška", "SSE.Controllers.Toolbar.txtAccent_Smile": "Oblúčik", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Vlnovka", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Zátvorky s oddeľovačom", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Zátvorky s oddeľovačom", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Zátvorky s oddeľovačom", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tri podmienky)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Zložený objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Zložený objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomický koe<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomický koe<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Zátvorky s oddeľovačom", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Zátvorka", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Zátvorka", "SSE.Controllers.Toolbar.txtDeleteCells": "Odstrániť bunky", "SSE.Controllers.Toolbar.txtExpand": "Rozbaliť a zoradiť", "SSE.Controllers.Toolbar.txtExpandSort": "Údaje vedľa výberu nebudú zoradené. Chcete rozšíriť výber tak, aby zahŕňal priľahlé údaje, alebo pok<PERSON>č<PERSON>ť v triedení len vybraných buniek?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Skosený zlomok ", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Diferen<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Lineárny zlomok", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pí lomeno dvoma", "SSE.Controllers.Toolbar.txtFractionSmall": "Malý zlo<PERSON>k", "SSE.Controllers.Toolbar.txtFractionVertical": "Lomený výraz", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverzná funkcia kosínus", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Inverzná funkcia hyperbolický kosínus", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverzná funkcia kotangens", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Inverzná funkcia hyperbolický kotangens", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverzná funkcia kosekans", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Inverzná funkcia hyperbolický kosekans", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverzná funkcia sekans", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Inverzná funkcia hyperbolický sekans", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Inverzná funkcia sínus", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Inverzná funkcia hyperbolický sínus", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverzná funkcia tangens", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Inverzná funkcia hyperbolický tangens", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Funkcia hyperbolický kosínus", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON> kotangens", "SSE.Controllers.Toolbar.txtFunction_Coth": "Funkcia hyperbolický kotangens", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON> kose<PERSON>", "SSE.Controllers.Toolbar.txtFunction_Csch": "Funkcia hyperbolický kosekans", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON><PERSON> theta ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Kosínus 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangentová rovnica", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "Funkcia hyperbolický sekans", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON> s<PERSON>us", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Funkcia hyperbolický sínus", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON> tangens", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Funkcia hyperbolický tangens", "SSE.Controllers.Toolbar.txtInsertCells": "Vložiť bunky", "SSE.Controllers.Toolbar.txtIntegral": "Integrál", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferenci<PERSON>l theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferenciál x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferenci<PERSON><PERSON> y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrál", "SSE.Controllers.Toolbar.txtIntegralDouble": "Dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralOriented": "Krivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Krivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Krivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Priestorový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Priestorový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Priestorový integrál", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integrál", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON>jn<PERSON> integrál", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON>jn<PERSON> integrál", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON>jn<PERSON> integrál", "SSE.Controllers.Toolbar.txtInvalidRange": "CHYBA! Neplatný rozsah bunky", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Konjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Konjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Konjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Konjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Konjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Zjednotenie", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Disjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Disjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Disjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Disjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Disjunkcia", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Zjednotenie", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Zjednotenie", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Zjednotenie", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Zjednotenie", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Zjednotenie", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maximálny príklad", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Prirodzený logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Blízko vášho výberu existuj<PERSON> d<PERSON>, ne<PERSON><PERSON><PERSON> však dostatočné oprávnenia k úprave týchto buniek.<br>Chcete pokračovať s aktuálnym výberom? ", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Prázdna matica so zátvorkami", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Prázdna matica so zátvorkami", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Prázdna matica so zátvorkami", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Prázdna matica so zátvorkami", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Prázdna matica", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON><PERSON> bod<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON><PERSON><PERSON> bodky", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON> bodky", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Riedka matica", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Riedka matica", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Zhodná matica", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Zhodná matica", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Zhodná matica", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Zhodná matica", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Pravá-ľavá šípka pod", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Pravá-ľavá šípka nad", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Ľavá š<PERSON> pod", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Ľavá šípka nad", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Pravá šípka pod", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Pravá šípka nad", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dvojbodka rovná sa", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Výnosy", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta vzniká", "SSE.Controllers.Toolbar.txtOperator_Definition": "Rovná sa podľa definície", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta rovná sa", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Pravá-ľavá šípka pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Pravá-ľavá šípka nad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Ľavá š<PERSON> pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Ľavá šípka nad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Pravá šípka pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Pravá šípka nad", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "D<PERSON><PERSON><PERSON> rovná sa", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON> rov<PERSON> sa", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus rovná sa", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Merať <PERSON>ľa", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Odmo<PERSON>niny", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Odmo<PERSON>niny", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> od<PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON> od<PERSON>a", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "n-tá odmocnina", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> od<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Dolný index", "SSE.Controllers.Toolbar.txtScriptSubSup": "Dolný index - Horný index", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Ľavý dolný index - horný index", "SSE.Controllers.Toolbar.txtScriptSup": "Horný index", "SSE.Controllers.Toolbar.txtSorting": "Zora<PERSON>nie", "SSE.Controllers.Toolbar.txtSortSelected": "Zoradiť vybrané", "SSE.Controllers.Toolbar.txtSymbol_about": "Približne", "SSE.Controllers.Toolbar.txtSymbol_additional": "Doplnok/doplnenie", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Takmer sa rovná", "SSE.Controllers.Toolbar.txtSymbol_ast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON> od<PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Strednica horizontálnej elipsy", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Stupne Celzia", "SSE.Controllers.Toolbar.txtSymbol_chi": "Chí ", "SSE.Controllers.Toolbar.txtSymbol_cong": "Približne sa rovná", "SSE.Controllers.Toolbar.txtSymbol_cup": "Zjednotenie", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Dolná pravá diagonálna elipsa ", "SSE.Controllers.Toolbar.txtSymbol_degree": "Stupeň", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Znak delenia", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Šíp<PERSON> dole", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Prázdna množina", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "epsilon (g<PERSON><PERSON> písmeno E)", "SSE.Controllers.Toolbar.txtSymbol_equals": "Rovná sa", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Rovnaké ako", "SSE.Controllers.Toolbar.txtSymbol_eta": "H (g<PERSON><PERSON> p<PERSON>)", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON>ist<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktor<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Stup<PERSON>ov Fahrenheita", "SSE.Controllers.Toolbar.txtSymbol_forall": "Pre všetko", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "Väčšie alebo rovná sa", "SSE.Controllers.Toolbar.txtSymbol_gg": "Oveľa väčšie ako", "SSE.Controllers.Toolbar.txtSymbol_greater": "Väčšie ako", "SSE.Controllers.Toolbar.txtSymbol_in": "Prvok ", "SSE.Controllers.Toolbar.txtSymbol_inc": "Prírastok/zvýšenie", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Nekonečno", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa (písmeno g<PERSON> a<PERSON>cedy)", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Ľavá-pravá ší<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON>ej alebo rovná sa", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON> a<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ll": "Ove<PERSON><PERSON> men<PERSON> ako", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON> plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Nerovná sa", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_not": "Nepopísať/bez znaku", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Parciálny diferenciál", "SSE.Controllers.Toolbar.txtSymbol_percent": "Percentuálny podiel", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fí", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pí", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus Mínus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Úmerné k", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psí", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Štvrtá odmocnina", "SSE.Controllers.Toolbar.txtSymbol_qed": "Znak koniec dôkazu", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Horná pravá diagonálna elipsa/vypustenie", "SSE.Controllers.Toolbar.txtSymbol_rho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Pravá šípka", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma ", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Znak odmocniny", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Preto/z toho dôvodu", "SSE.Controllers.Toolbar.txtSymbol_theta": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_times": "Znak násobenia", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Šípka hore", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON> epsilon<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pí variant", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ró variant", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Vertikálna elipsa/vypustenie", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ksí ", "SSE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Štýl tabuľky tmavý", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Štýl tabuľky svetlý", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Štýl tabuľky stredný", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete vykonať, môže trvať pomerne dlhý čas na dokončenie.<br>Určite chcete pokračovať?", "SSE.Controllers.Toolbar.warnMergeLostData": "Iba údaje z ľavej hornej bunky zostanú v zlúčenej bunke.<br><PERSON><PERSON> si istý, že chcete pokračovať?", "SSE.Controllers.Viewport.textFreezePanes": "Ukotviť priečky", "SSE.Controllers.Viewport.textFreezePanesShadow": "Zobraziť tieň ukotvených priečok", "SSE.Controllers.Viewport.textHideFBar": "Skryť riadok vzorcov", "SSE.Controllers.Viewport.textHideGridlines": "Skryť mriežku", "SSE.Controllers.Viewport.textHideHeadings": "Skryť záhlavia", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Oddeľovač desatinných miest", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> tisícov", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Nastavenia používané na rozpoznávanie číselných údajov", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Textový kvalifikátor", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Pokročilé nastavenia", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(žiadne)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Špeciálny/vlastný filter", "SSE.Views.AutoFilterDialog.textAddSelection": "Pridať aktuálny výber na filtrovanie", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Vybrať všetko", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Vyberte všetky výsledky vyhľadávania", "SSE.Views.AutoFilterDialog.textWarning": "Upozornenie", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON>d p<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtBegins": "Začať s...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Podpriemerný", "SSE.Views.AutoFilterDialog.txtBetween": "Medzi...", "SSE.Views.AutoFilterDialog.txtClear": "Vyčistiť", "SSE.Views.AutoFilterDialog.txtContains": "O<PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtEmpty": "Zadať filter buniek", "SSE.Views.AutoFilterDialog.txtEnds": "Končí s...", "SSE.Views.AutoFilterDialog.txtEquals": "Rovná sa...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrovať podľa farby buniek", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrovať podľa farby písma", "SSE.Views.AutoFilterDialog.txtGreater": "Väčšie ako...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Väčšie alebo rovná sa...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtrovanie š<PERSON>kov", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON> ne<PERSON>...", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON>ej alebo rovná sa", "SSE.Views.AutoFilterDialog.txtNotBegins": "Nezačína s...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Nie medzi...", "SSE.Views.AutoFilterDialog.txtNotContains": "<PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Nekončí s...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Nerovná sa ...", "SSE.Views.AutoFilterDialog.txtNumFilter": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtReapply": "Opäť použiť", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Zoradiť podľa farieb b<PERSON>ek", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Zoradiť podľa farieb písma", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Zoradiť od najvyššieho po najnižšie", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Zoradiť od najnižšieho po najvyššie", "SSE.Views.AutoFilterDialog.txtSortOption": "Ďalšie mo<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTextFilter": "Textový filter", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "Na použitie filtra hodnôt potrebujete aspoň jedno pole v oblasti Hodnoty.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Musíte vybrať aspoň jednu hodnotu", "SSE.Views.CellEditor.textManager": "Správca názvov", "SSE.Views.CellEditor.tipFormula": "Vložiť funkciu", "SSE.Views.CellRangeDialog.errorMaxRows": "CHYBA! Maximálny počet dátových radov na graf je 255", "SSE.Views.CellRangeDialog.errorStockChart": "Nesprávne poradie riadkov. Ak chcete vytvoriť burzový graf, umiestnite údaje na hárok v nasledujúcom poradí:<br> z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena, max cena, min cena, konečná cena.", "SSE.Views.CellRangeDialog.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.CellRangeDialog.txtInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.CellRangeDialog.txtTitle": "Vybrať rozsah <PERSON>", "SSE.Views.CellSettings.strShrink": "Orezať aby sa vošlo", "SSE.Views.CellSettings.strWrap": "Obtekanie textu", "SSE.Views.CellSettings.textAngle": "Uhol", "SSE.Views.CellSettings.textBackColor": "Farba pozadia", "SSE.Views.CellSettings.textBackground": "Farba pozadia", "SSE.Views.CellSettings.textBorderColor": "Farba", "SSE.Views.CellSettings.textBorders": "Štýl orámovania", "SSE.Views.CellSettings.textClearRule": "Zmazať pravidlá", "SSE.Views.CellSettings.textColor": "Vyplniť farbou", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textCondFormat": "Podmienen<PERSON>", "SSE.Views.CellSettings.textControl": "Kontrola textu", "SSE.Views.CellSettings.textDataBars": "Histogramy", "SSE.Views.CellSettings.textDirection": "Smer", "SSE.Views.CellSettings.textFill": "Vyplniť", "SSE.Views.CellSettings.textForeground": "Farba popredia", "SSE.Views.CellSettings.textGradient": "Body prechodu", "SSE.Views.CellSettings.textGradientColor": "Farba", "SSE.Views.CellSettings.textGradientFill": "Výplň prechodom", "SSE.Views.CellSettings.textIndent": "Odsadiť", "SSE.Views.CellSettings.textItems": "Polož<PERSON>", "SSE.Views.CellSettings.textLinear": "Lineárny", "SSE.Views.CellSettings.textManageRule": "Spravovať pravidlá", "SSE.Views.CellSettings.textNewRule": "Nové pravidlo", "SSE.Views.CellSettings.textNoFill": "Bez výplne", "SSE.Views.CellSettings.textOrientation": "Orientácia textu", "SSE.Views.CellSettings.textPattern": "Vzor", "SSE.Views.CellSettings.textPatternFill": "Vzor", "SSE.Views.CellSettings.textPosition": "Pozícia", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>te zmeniť podľa vyššie uvedeného štýlu", "SSE.Views.CellSettings.textSelection": "Z aktuálneho výberu", "SSE.Views.CellSettings.textThisPivot": "Z tejto kontingenčnej tabuľky", "SSE.Views.CellSettings.textThisSheet": "Z tohto listu", "SSE.Views.CellSettings.textThisTable": "Z tejto tabuľky", "SSE.Views.CellSettings.tipAddGradientPoint": "Pridať spádový bod", "SSE.Views.CellSettings.tipAll": "Nastaviť vonkajšie orámovanie a všetky vnútorné čiary", "SSE.Views.CellSettings.tipBottom": "Nastaviť len spodn<PERSON> von<PERSON>jš<PERSON> orámovanie", "SSE.Views.CellSettings.tipDiagD": "Nastavte okraj diagonálne nadol", "SSE.Views.CellSettings.tipDiagU": "Nastavte okraj diagonálne nahor", "SSE.Views.CellSettings.tipInner": "Nastaviť len vnútorné čiary", "SSE.Views.CellSettings.tipInnerHor": "Nastaviť iba horizontálne vnútorné čiary", "SSE.Views.CellSettings.tipInnerVert": "Nastaviť len vertikálne vnútorné čiary", "SSE.Views.CellSettings.tipLeft": "Nastaviť len ľ<PERSON> orámovanie", "SSE.Views.CellSettings.tipNone": "Nastaviť bez orámovania", "SSE.Views.CellSettings.tipOuter": "Nastaviť len vonkajšie orámovanie", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Odstrániť spádový bod", "SSE.Views.CellSettings.tipRight": "Nastaviť len pravé von<PERSON>jšie orámovanie", "SSE.Views.CellSettings.tipTop": "Nastaviť len horn<PERSON> orámovanie", "SSE.Views.ChartDataDialog.errorInFormula": "V zadanom vzorci je chyba.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Odkaz nie je platný. Odkaz musí byť na otvorený pracovný hárok.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Maximálny počet bodov v sérii na jeden graf je 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Maximálny počet radov údajov na graf je 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Odkaz nie je platný. Odkazy na názvy, ho<PERSON><PERSON><PERSON>, veľkosti alebo označenia údajov musia byť v jednej bunke, riadku alebo stĺpci.", "SSE.Views.ChartDataDialog.errorNoValues": "Ak chcete vytvoriť graf, postupnosť musí obsahovať aspoň jednu hodnotu.", "SSE.Views.ChartDataDialog.errorStockChart": "Nesprávne poradie riadkov. Ak chcete vytvoriť burzový graf, umiestnite údaje na hárok v nasledujúcom poradí:<br> z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena, max cena, min cena, konečná cena.", "SSE.Views.ChartDataDialog.textAdd": "Pridať", "SSE.Views.ChartDataDialog.textCategory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Kategórie) štítky osi ", "SSE.Views.ChartDataDialog.textData": "<PERSON><PERSON><PERSON><PERSON> grafu", "SSE.Views.ChartDataDialog.textDelete": "Odstrániť", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "Upraviť", "SSE.Views.ChartDataDialog.textInvalidRange": "Neplatn<PERSON> roz<PERSON> b<PERSON>", "SSE.Views.ChartDataDialog.textSelectData": "Vybrať údaje", "SSE.Views.ChartDataDialog.textSeries": "Záz<PERSON>y <PERSON>y (Série)", "SSE.Views.ChartDataDialog.textSwitch": "Prehodiť Riadky/Stĺpce", "SSE.Views.ChartDataDialog.textTitle": "Ú<PERSON>je grafu", "SSE.Views.ChartDataDialog.textUp": "Hore", "SSE.Views.ChartDataRangeDialog.errorInFormula": "V zadanom vzorci je chyba.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Odkaz nie je platný. Odkaz musí byť na otvorený pracovný hárok.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Maximálny počet bodov v sérii na jeden graf je 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Maximálny počet radov údajov na graf je 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Odkaz nie je platný. Odkazy na názvy, ho<PERSON><PERSON><PERSON>, veľkosti alebo označenia údajov musia byť v jednej bunke, riadku alebo stĺpci.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Ak chcete vytvoriť graf, postupnosť musí obsahovať aspoň jednu hodnotu.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Nesprávne poradie riadkov. Ak chcete vytvoriť burzový graf, umiestnite údaje na hárok v nasledujúcom poradí:<br> z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena, max cena, min cena, konečná cena.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Neplatn<PERSON> roz<PERSON> b<PERSON>", "SSE.Views.ChartDataRangeDialog.textSelectData": "Vybrať údaje", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Rozsah osovej značky", "SSE.Views.ChartDataRangeDialog.txtChoose": "Vyber rozsah", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>v", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Upraviť série", "SSE.Views.ChartDataRangeDialog.txtValues": "Hodnoty", "SSE.Views.ChartDataRangeDialog.txtXValues": "Hodnoty na osi X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Hodnoty na osi Y", "SSE.Views.ChartSettings.strLineWeight": "Hrúbka čiary", "SSE.Views.ChartSettings.strSparkColor": "Farba", "SSE.Views.ChartSettings.strTemplate": "Šablóna", "SSE.Views.ChartSettings.textAdvanced": "Zobraziť pokročilé nastavenia", "SSE.Views.ChartSettings.textBorderSizeErr": "Zadaná hodnota je nesprávna.<br><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> hodnotu medzi 0 pt a 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Zmeniť typ", "SSE.Views.ChartSettings.textChartType": "Zmeniť typ grafu", "SSE.Views.ChartSettings.textEditData": "Upraviť údaje a umiestnenie", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textHeight": "Výška", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Posledný bod", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSelectData": "Vybrať údaje", "SSE.Views.ChartSettings.textShow": "Zobraziť", "SSE.Views.ChartSettings.textSize": "Veľkosť", "SSE.Views.ChartSettings.textStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textWidth": "Šírka", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "CHYBA! Najvyšší možný počet bodov za sebou v jednom grafu je 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "CHYBA! Maximálny počet dátových radov na graf je 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Nesprávne poradie riadkov. Ak chcete vytvoriť burzový graf, umiestnite údaje na hárok v nasledujúcom poradí:<br> z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena, max cena, min cena, konečná cena.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Neposúvať alebo nemeniť veľkosť s bunkami", "SSE.Views.ChartSettingsDlg.textAlt": "Alternatívny text", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Alternatívne textové zobrazenie informácií o vizuálnych objektoch, ktoré sa prečítajú ľuďom s poruchou videnia alebo kognitívnymi poruchami, aby sa im pomohlo lepšie porozumieť, ak<PERSON> informá<PERSON> sú na obrázku, automatickom tvarovaní, grafe alebo tabuľke. ", "SSE.Views.ChartSettingsDlg.textAltTitle": "N<PERSON>zov", "SSE.Views.ChartSettingsDlg.textAuto": "<PERSON>ky", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automaticky pre každé", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Kríženie os", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Možnosti osi", "SSE.Views.ChartSettingsDlg.textAxisPos": "Umiestnenie osi", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Nastavenia osi", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> roz<PERSON>", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementy grafu &<br>Legenda grafu", "SSE.Views.ChartSettingsDlg.textChartTitle": "Názov grafu", "SSE.Views.ChartSettingsDlg.textCross": "Pretínať", "SSE.Views.ChartSettingsDlg.textCustom": "Vlastný", "SSE.Views.ChartSettingsDlg.textDataColumns": "V stĺpcoch", "SSE.Views.ChartSettingsDlg.textDataLabels": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataRows": "V riadkoch", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Zobraziť legendu", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Skryté a prázdne bunky", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Spojiť dátové body s riadkom", "SSE.Views.ChartSettingsDlg.textFit": "Prispôsobiť na šírku", "SSE.Views.ChartSettingsDlg.textFixed": "Fixný/napravený", "SSE.Views.ChartSettingsDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGaps": "Medzery", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Skupina Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Skryť", "SSE.Views.ChartSettingsDlg.textHideAxis": "Skryť osi", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "Vodorovná os", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Pomocná vodorovná os", "SSE.Views.ChartSettingsDlg.textHorizontal": "V<PERSON><PERSON>vný", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "v", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerTop": "<PERSON><PERSON><PERSON><PERSON> hore", "SSE.Views.ChartSettingsDlg.textInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.ChartSettingsDlg.textLabelDist": "Vzdialenosť popisku osi", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Interval medzi pop<PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Možnosti menoviek", "SSE.Views.ChartSettingsDlg.textLabelPos": "Umiestnenie menoviek", "SSE.Views.ChartSettingsDlg.textLayout": "Rozloženie", "SSE.Views.ChartSettingsDlg.textLeft": "Vľavo", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Vľavo", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "Vpravo", "SSE.Views.ChartSettingsDlg.textLegendTop": "Hore", "SSE.Views.ChartSettingsDlg.textLines": "Čiary", "SSE.Views.ChartSettingsDlg.textLocationRange": "Rozsah <PERSON>nenia", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON>z<PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON>lavný", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Hlavný a vedľajší", "SSE.Views.ChartSettingsDlg.textMajorType": "Hlavná značka", "SSE.Views.ChartSettingsDlg.textManual": "Manuál/ručný", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Interval medzi z<PERSON>č<PERSON>mi", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maximálna hodnota", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Vedľajší", "SSE.Views.ChartSettingsDlg.textMinorType": "Vedľajšia značka", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimálna hodnota", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Vedľa osi", "SSE.Views.ChartSettingsDlg.textNone": "Žiadny", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Žiadne prekrytie", "SSE.Views.ChartSettingsDlg.textOneCell": "Presúvať ale nemeniť veľkosť spoločne s bunkami", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "<PERSON> značkách", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "Mimo hore", "SSE.Views.ChartSettingsDlg.textOverlay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textReverse": "Hodnoty v opačnom poradí", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Obrá<PERSON>é p<PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "Vpravo", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "Rovnaké pre všetky", "SSE.Views.ChartSettingsDlg.textSelectData": "Vybrať údaje", "SSE.Views.ChartSettingsDlg.textSeparator": "Oddeľovače popisiek dát", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>v", "SSE.Views.ChartSettingsDlg.textShow": "Zobraziť", "SSE.Views.ChartSettingsDlg.textShowBorders": "Zobraziť okraje grafu", "SSE.Views.ChartSettingsDlg.textShowData": "Zobraziť údaje v skrytých riadkoch a stĺpcoch", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Zobraziť prázdne bunky ako", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Zobraziť os", "SSE.Views.ChartSettingsDlg.textShowValues": "Zobraziť hodnoty grafu", "SSE.Views.ChartSettingsDlg.textSingle": "Jednoduchý Sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "<PERSON><PERSON><PERSON><PERSON> bunky", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparkline - Rozsahy", "SSE.Views.ChartSettingsDlg.textStraight": "Priamy/rovný", "SSE.Views.ChartSettingsDlg.textStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Možnosti značiek", "SSE.Views.ChartSettingsDlg.textTitle": "Graf - Pokročilé nastavenia", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Pokročilé nastavenia", "SSE.Views.ChartSettingsDlg.textTop": "Hore", "SSE.Views.ChartSettingsDlg.textTrillions": "Bilióny", "SSE.Views.ChartSettingsDlg.textTwoCell": "Presúvať a meniť veľkosť spoločne s bunkami", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Typ a údaje", "SSE.Views.ChartSettingsDlg.textUnits": "Zobrazova<PERSON>", "SSE.Views.ChartSettingsDlg.textValue": "Hodnota", "SSE.Views.ChartSettingsDlg.textVertAxis": "Vertikálna os", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Pomocná zvislá os", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Názov osi X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Názov osi Y", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.ChartTypeDialog.errorComboSeries": "Pre vytvorenie kombinovaného grafu, zvoľte aspoň dve skupiny dát.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Vybratý typ grafu vyžaduje sekundárnu os, k<PERSON><PERSON> p<PERSON>žíva existujúci graf. Vyberte iný typ grafu.", "SSE.Views.ChartTypeDialog.textSecondary": "Pomocná os", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Typ grafu", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON> zdr<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "SSE.Views.CreatePivotDialog.textDestination": "Vybrať kam sa tabuľka umiestni", "SSE.Views.CreatePivotDialog.textExist": "Existuj<PERSON><PERSON> list", "SSE.Views.CreatePivotDialog.textInvalidRange": "Neplatn<PERSON> roz<PERSON> b<PERSON>", "SSE.Views.CreatePivotDialog.textNew": "Nový pracovný list", "SSE.Views.CreatePivotDialog.textSelectData": "Vybrať údaje", "SSE.Views.CreatePivotDialog.textTitle": "Vytvoriť kontingenčnú tabuľku", "SSE.Views.CreatePivotDialog.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.CreateSparklineDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON> zdr<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, kde umiestniť mikro-graf", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Neplatn<PERSON> roz<PERSON> b<PERSON>", "SSE.Views.CreateSparklineDialog.textSelectData": "Vybrať údaje", "SSE.Views.CreateSparklineDialog.textTitle": "Vytvoriť mikro-graf", "SSE.Views.CreateSparklineDialog.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.DataTab.capBtnGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextCustomSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextDataValidation": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Odobrať duplicity", "SSE.Views.DataTab.capBtnTextToCol": "Text na stĺpec", "SSE.Views.DataTab.capBtnUngroup": "Oddeliť", "SSE.Views.DataTab.capDataFromText": "Získať dáta", "SSE.Views.DataTab.mniFromFile": "Z miestneho TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Z webovej adresy TXT/CSV", "SSE.Views.DataTab.textBelow": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> pod <PERSON>om", "SSE.Views.DataTab.textClear": "Vyčistiť obrys", "SSE.Views.DataTab.textColumns": "Zrušte zoskupenie stĺpcov", "SSE.Views.DataTab.textGroupColumns": "Zoskupiť stĺpce", "SSE.Views.DataTab.textGroupRows": "Zoskupiť riadky", "SSE.Views.DataTab.textRightOf": "Súhrnné stĺpce napravo od podrobností", "SSE.Views.DataTab.textRows": "Zrušte zoskupenie riadkov", "SSE.Views.DataTab.tipCustomSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.tipDataFromText": "Získať dáta z textového/CSV súboru", "SSE.Views.DataTab.tipDataValidation": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.tipGroup": "Zoskupiť rozsah buniek", "SSE.Views.DataTab.tipRemDuplicates": "Odobrať z listu duplicitné r<PERSON>dky", "SSE.Views.DataTab.tipToColumns": "Rozdeliť text bunky do stĺpcov", "SSE.Views.DataTab.tipUngroup": "<PERSON><PERSON><PERSON> roz<PERSON> b<PERSON>", "SSE.Views.DataValidationDialog.errorFormula": "Hodnota sa momentálne vyhodnotí ako chyba. <PERSON>ceš p<PERSON>?", "SSE.Views.DataValidationDialog.errorInvalid": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> ste zadali do poľa „{0}“, je nep<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidDate": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> ste zadali do poľa \"{0}“, je ne<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidList": "Dĺžka vášho vzorca presahuje limit 8192 znakov.<br>Upravte ho a skúste to znova.", "SSE.Views.DataValidationDialog.errorInvalidTime": "<PERSON><PERSON>, k<PERSON><PERSON> ste zadali do poľa „{0}“, je ne<PERSON>.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Hodnota poľa \"{1}\" musí byť větší alebo rovný hodnote poľa \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Musíte zadať hodnotu do poľa „{0}“ aj do poľa „{1}“.", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Do poľa „{0}“ musíte zadať hodnotu.", "SSE.Views.DataValidationDialog.errorNamedRange": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON> n<PERSON>ť.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "<PERSON> podmienke „{0}“ nie je možné použ<PERSON>ť záporné hodnoty.", "SSE.Views.DataValidationDialog.errorNotNumeric": "Pole „{0}“ musí byť číselná hodnota, číselný výraz alebo musí odkazovať na bunku obsahujúcu číselnú hodnotu.", "SSE.Views.DataValidationDialog.strError": "Chybové hlásenie", "SSE.Views.DataValidationDialog.strInput": "Správa pri zadávaní", "SSE.Views.DataValidationDialog.strSettings": "Nastavenia", "SSE.Views.DataValidationDialog.textAlert": "Upozornenie", "SSE.Views.DataValidationDialog.textAllow": "Povoliť", "SSE.Views.DataValidationDialog.textApply": "Použiť tieto zmeny na všetky ostatné bunky s rovnakými nastaveniami", "SSE.Views.DataValidationDialog.textCellSelected": "Keď je vybrat<PERSON> bunka, zobrazte túto vstupnú správu", "SSE.Views.DataValidationDialog.textCompare": "Porovnať s ", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndTime": "Čas <PERSON>", "SSE.Views.DataValidationDialog.textError": "Chybové hlásenie", "SSE.Views.DataValidationDialog.textFormula": "Vzorec", "SSE.Views.DataValidationDialog.textIgnore": "Ignorovať prázdne bunky", "SSE.Views.DataValidationDialog.textInput": "Správa pri zadávaní", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Správa", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Vybrať údaje", "SSE.Views.DataValidationDialog.textShowDropDown": "Zobraziť rozbaľovací zoznam v bunke", "SSE.Views.DataValidationDialog.textShowError": "Po zadaní neplatných údajov zobraziť chybové hlásenie", "SSE.Views.DataValidationDialog.textShowInput": "Keď je vybratá bunka, zobraziť vstupnú správu", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "<PERSON><PERSON><PERSON>č<PERSON>", "SSE.Views.DataValidationDialog.textStartTime": "Čas začiatku", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Keď používateľ zadá neplatné <PERSON>, zobraziť toto chybové hlásenie", "SSE.Views.DataValidationDialog.txtAny": "Akákoľvek hodnota", "SSE.Views.DataValidationDialog.txtBetween": "medzi", "SSE.Views.DataValidationDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "Desatinn<PERSON>/desatinný", "SSE.Views.DataValidationDialog.txtElTime": "Uplynutý čas", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndTime": "Čas <PERSON>", "SSE.Views.DataValidationDialog.txtEqual": "rovná sa", "SSE.Views.DataValidationDialog.txtGreaterThan": "väčšie ako", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Väčšie alebo rovná sa", "SSE.Views.DataValidationDialog.txtLength": "Dĺžka", "SSE.Views.DataValidationDialog.txtLessThan": "<PERSON><PERSON> a<PERSON>", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "<PERSON>ej alebo rovná sa", "SSE.Views.DataValidationDialog.txtList": "Zoznam", "SSE.Views.DataValidationDialog.txtNotBetween": "nie medzi", "SSE.Views.DataValidationDialog.txtNotEqual": "nerovná sa", "SSE.Views.DataValidationDialog.txtOther": "Ostatné", "SSE.Views.DataValidationDialog.txtStartDate": "<PERSON><PERSON><PERSON>č<PERSON>", "SSE.Views.DataValidationDialog.txtStartTime": "Čas začiatku", "SSE.Views.DataValidationDialog.txtTextLength": "Dĺžka textu", "SSE.Views.DataValidationDialog.txtTime": "Čas", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "a", "SSE.Views.DigitalFilterDialog.capCondition1": "rovná se", "SSE.Views.DigitalFilterDialog.capCondition10": "Nekončí s", "SSE.Views.DigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition2": "nerovná sa", "SSE.Views.DigitalFilterDialog.capCondition3": "je vä<PERSON><PERSON><PERSON> ako", "SSE.Views.DigitalFilterDialog.capCondition4": "je vä<PERSON><PERSON>ie alebo rovné ", "SSE.Views.DigitalFilterDialog.capCondition5": "Je menšie než", "SSE.Views.DigitalFilterDialog.capCondition6": "je men<PERSON>ie alebo rovné ", "SSE.Views.DigitalFilterDialog.capCondition7": "Začať s", "SSE.Views.DigitalFilterDialog.capCondition8": "Nezačína s", "SSE.Views.DigitalFilterDialog.capCondition9": "Končí s", "SSE.Views.DigitalFilterDialog.capOr": "Alebo", "SSE.Views.DigitalFilterDialog.textNoFilter": "bez filtra", "SSE.Views.DigitalFilterDialog.textShowRows": "Zobraziť riadky kde", "SSE.Views.DigitalFilterDialog.textUse1": "Použite ? na zobrazenie akéhokoľvek jedného znaku", "SSE.Views.DigitalFilterDialog.textUse2": "Použite * na zobrazenie ľubovoľnej série znakov", "SSE.Views.DigitalFilterDialog.txtTitle": "Špeciálny/vlastný filter", "SSE.Views.DocumentHolder.advancedImgText": "Pokročilé nastavenia obrázku", "SSE.Views.DocumentHolder.advancedShapeText": "Pokročilé nastavenia tvaru", "SSE.Views.DocumentHolder.advancedSlicerText": "Prierez – Rozšírené nastavenia", "SSE.Views.DocumentHolder.bottomCellText": "Zarovnať dole", "SSE.Views.DocumentHolder.bulletsText": "Odrážky a číslovanie", "SSE.Views.DocumentHolder.centerCellText": "Zarovnať na stred", "SSE.Views.DocumentHolder.chartText": "Pokročilé nastavenia grafu", "SSE.Views.DocumentHolder.deleteColumnText": "Stĺpec", "SSE.Views.DocumentHolder.deleteRowText": "Riadok", "SSE.Views.DocumentHolder.deleteTableText": "Tabuľka", "SSE.Views.DocumentHolder.direct270Text": "Otočiť text nahor", "SSE.Views.DocumentHolder.direct90Text": "Otočiť text nadol", "SSE.Views.DocumentHolder.directHText": "V<PERSON><PERSON>vný", "SSE.Views.DocumentHolder.directionText": "Smer textu", "SSE.Views.DocumentHolder.editChartText": "Upravovať dáta", "SSE.Views.DocumentHolder.editHyperlinkText": "Upraviť hypertextový odkaz", "SSE.Views.DocumentHolder.insertColumnLeftText": "Stĺpec vľavo", "SSE.Views.DocumentHolder.insertColumnRightText": "Stĺpec vpravo", "SSE.Views.DocumentHolder.insertRowAboveText": "Riadok nad", "SSE.Views.DocumentHolder.insertRowBelowText": "Riadok pod", "SSE.Views.DocumentHolder.originalSizeText": "Aktuálna veľkosť", "SSE.Views.DocumentHolder.removeHyperlinkText": "Odstrániť hypertextový odkaz", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> stĺpec", "SSE.Views.DocumentHolder.selectDataText": "Údaje o stĺpcoch", "SSE.Views.DocumentHolder.selectRowText": "Riadok", "SSE.Views.DocumentHolder.selectTableText": "Tabuľka", "SSE.Views.DocumentHolder.strDelete": "Odstrániť podpis", "SSE.Views.DocumentHolder.strDetails": "Podrobnosti podpisu", "SSE.Views.DocumentHolder.strSetup": "Nastavenia podpisu", "SSE.Views.DocumentHolder.strSign": "Podpísať", "SSE.Views.DocumentHolder.textAlign": "Zarovnať", "SSE.Views.DocumentHolder.textArrange": "Upraviť/usporiadať/zarovnať", "SSE.Views.DocumentHolder.textArrangeBack": "Presunúť do pozadia", "SSE.Views.DocumentHolder.textArrangeBackward": "Posunúť späť", "SSE.Views.DocumentHolder.textArrangeForward": "Posunúť vpred", "SSE.Views.DocumentHolder.textArrangeFront": "Premiestniť do popredia", "SSE.Views.DocumentHolder.textAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "Počet", "SSE.Views.DocumentHolder.textCrop": "Orezať", "SSE.Views.DocumentHolder.textCropFill": "Vyplniť", "SSE.Views.DocumentHolder.textCropFit": "Prispôsobiť", "SSE.Views.DocumentHolder.textEditPoints": "Upraviť body", "SSE.Views.DocumentHolder.textEntriesList": "Vybrať z rolovacieho zoznamu", "SSE.Views.DocumentHolder.textFlipH": "Prevrátiť horizontálne", "SSE.Views.DocumentHolder.textFlipV": "Prevrátiť vertikálne", "SSE.Views.DocumentHolder.textFreezePanes": "Ukotviť priečky", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromStorage": "Z úložiska", "SSE.Views.DocumentHolder.textFromUrl": "Z URL adresy", "SSE.Views.DocumentHolder.textListSettings": "Nastavenia zoznamu", "SSE.Views.DocumentHolder.textMacro": "Priradiť makro", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "Ďalšie funkcie", "SSE.Views.DocumentHolder.textMoreFormats": "Ďalšie formáty", "SSE.Views.DocumentHolder.textNone": "žiadny", "SSE.Views.DocumentHolder.textNumbering": "Číslovanie", "SSE.Views.DocumentHolder.textReplace": "Nahradiť obrázok", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Otočiť o 90° doľava", "SSE.Views.DocumentHolder.textRotate90": "Otočiť o 90° doprava", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Zarovnať dole", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Zarovnať na stred", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Zarovnať doľava", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Zarovnať na stred", "SSE.Views.DocumentHolder.textShapeAlignRight": "Zarovnať doprava", "SSE.Views.DocumentHolder.textShapeAlignTop": "Zarovnať nahor", "SSE.Views.DocumentHolder.textStdDev": "Smerodajná odchylka", "SSE.Views.DocumentHolder.textSum": "SÚČET", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Zrušiť priečky", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Začiarknuteľné <PERSON>", "SSE.Views.DocumentHolder.tipMarkersDash": "Pomlčkové o<PERSON>žky", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Kosoštvorcové odrážky s výplňou", "SSE.Views.DocumentHolder.tipMarkersFRound": "Vyplnené ok<PERSON>úhl<PERSON> o<PERSON>", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Vyplnené š<PERSON>vorcové <PERSON>", "SSE.Views.DocumentHolder.tipMarkersHRound": "Prázdne okrúhle odrážky", "SSE.Views.DocumentHolder.tipMarkersStar": "Hviezdičkové odrážky", "SSE.Views.DocumentHolder.topCellText": "Zarovnať nahor", "SSE.Views.DocumentHolder.txtAccounting": "Účtovníctvo", "SSE.Views.DocumentHolder.txtAddComment": "Pridať komentár", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON><PERSON><PERSON> meno", "SSE.Views.DocumentHolder.txtArrange": "Upraviť/usporiadať/zarovnať", "SSE.Views.DocumentHolder.txtAscending": "Vzostupne", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automaticky prispôsobiť šírku stĺpca", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automaticky prispôsobiť výšku riadku", "SSE.Views.DocumentHolder.txtClear": "Vyčistiť", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearHyper": "Hypertextové odkazy", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Vyčistiť vybrané Sparkline skupiny", "SSE.Views.DocumentHolder.txtClearSparklines": "Vyčistiť vybrané mikro-grafy", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON> stĺpec", "SSE.Views.DocumentHolder.txtColumnWidth": "Nastaviť šírku stĺpca", "SSE.Views.DocumentHolder.txtCondFormat": "<PERSON>d<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCopy": "Kopírovať", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Špeciálna/vlastná šírka stĺpca", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Špeciálna/vlastná výška riadku", "SSE.Views.DocumentHolder.txtCustomSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCut": "Vystrihnúť", "SSE.Views.DocumentHolder.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelete": "Vymazať", "SSE.Views.DocumentHolder.txtDescending": "Zostupne", "SSE.Views.DocumentHolder.txtDistribHor": "Rozložiť horizontálne", "SSE.Views.DocumentHolder.txtDistribVert": "Rozložiť vertikálne", "SSE.Views.DocumentHolder.txtEditComment": "Upraviť komentár", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrovať podľa farby bunky", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrovať podľa farby písma", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrovať podľa hodnoty vybranej bunky", "SSE.Views.DocumentHolder.txtFormula": "Vložiť funkciu", "SSE.Views.DocumentHolder.txtFraction": "Zlomok", "SSE.Views.DocumentHolder.txtGeneral": "Všeobecné", "SSE.Views.DocumentHolder.txtGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtHide": "Skryť", "SSE.Views.DocumentHolder.txtInsert": "Vložiť", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hypertextový odkaz", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPaste": "Vložiť", "SSE.Views.DocumentHolder.txtPercentage": "Percentuálny podiel", "SSE.Views.DocumentHolder.txtReapply": "Opäť použiť", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "Nastaviť výšku riadku", "SSE.Views.DocumentHolder.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSelect": "Vybrať", "SSE.Views.DocumentHolder.txtShiftDown": "Posunú<PERSON> bunky dole", "SSE.Views.DocumentHolder.txtShiftLeft": "Posunúť bunky vľavo", "SSE.Views.DocumentHolder.txtShiftRight": "Posunúť bunky vpravo", "SSE.Views.DocumentHolder.txtShiftUp": "Posunúť bunky hore", "SSE.Views.DocumentHolder.txtShow": "Zobraziť", "SSE.Views.DocumentHolder.txtShowComment": "Zobraziť komentár", "SSE.Views.DocumentHolder.txtSort": "Zoradiť", "SSE.Views.DocumentHolder.txtSortCellColor": "Zvolená farba buniek na vrchu", "SSE.Views.DocumentHolder.txtSortFontColor": "Zvolená farba písma na vrchu", "SSE.Views.DocumentHolder.txtSparklines": "Mikro-grafy", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Pokročilé nastavenia odseku", "SSE.Views.DocumentHolder.txtTime": "Čas", "SSE.Views.DocumentHolder.txtUngroup": "Oddeliť", "SSE.Views.DocumentHolder.txtWidth": "Šírka", "SSE.Views.DocumentHolder.vertAlignText": "Vertikálne zarovnanie", "SSE.Views.FieldSettingsDialog.strLayout": "Rozloženie", "SSE.Views.FieldSettingsDialog.strSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textReport": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textTitle": "Nastavenie poľa", "SSE.Views.FieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtBlank": "Vložiť prázdne riadky za kazždú položku", "SSE.Views.FieldSettingsDialog.txtBottom": "Zobraziť v spodnej časti skupiny", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompaktný", "SSE.Views.FieldSettingsDialog.txtCount": "Počet", "SSE.Views.FieldSettingsDialog.txtCountNums": "Spočítať čísla", "SSE.Views.FieldSettingsDialog.txtCustomName": "Vlastný názov", "SSE.Views.FieldSettingsDialog.txtEmpty": "Zobraziť položky bez dát", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.FieldSettingsDialog.txtRepeat": "Opakovať štítky položiek na každom riadku", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Zobraziť medzisúčty", "SSE.Views.FieldSettingsDialog.txtSourceName": "Názov zdroja:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Smerodajná odchylka", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Populačná smerodajná odchýlka", "SSE.Views.FieldSettingsDialog.txtSum": "SÚČET", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funkcie pre medzisúčty", "SSE.Views.FieldSettingsDialog.txtTabular": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtTop": "Zobraziť v hornej časti skupiny", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Otvoriť umiestnenie súboru", "SSE.Views.FileMenu.btnCloseMenuCaption": "Zatvoriť menu", "SSE.Views.FileMenu.btnCreateNewCaption": "Vytvoriť nový", "SSE.Views.FileMenu.btnDownloadCaption": "Stiahnuť ako", "SSE.Views.FileMenu.btnExitCaption": "Koniec", "SSE.Views.FileMenu.btnFileOpenCaption": "Otvoriť", "SSE.Views.FileMenu.btnHelpCaption": "Pomoc", "SSE.Views.FileMenu.btnHistoryCaption": "História verzií", "SSE.Views.FileMenu.btnInfoCaption": "Informácie o zošite", "SSE.Views.FileMenu.btnPrintCaption": "Tlačiť", "SSE.Views.FileMenu.btnProtectCaption": "Ochrániť", "SSE.Views.FileMenu.btnRecentFilesCaption": "Otvoriť nedávne", "SSE.Views.FileMenu.btnRenameCaption": "Premenovať", "SSE.Views.FileMenu.btnReturnCaption": "Späť do zošitu", "SSE.Views.FileMenu.btnRightsCaption": "Prístupové práva", "SSE.Views.FileMenu.btnSaveAsCaption": "Uložiť ako", "SSE.Views.FileMenu.btnSaveCaption": "Uložiť", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Uložiť kópiu ako", "SSE.Views.FileMenu.btnSettingsCaption": "Pokročilé nastavenia", "SSE.Views.FileMenu.btnToEditCaption": "Upraviť zošit", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Prázdny zošit", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Vytvoriť nový", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplikovať", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Pridať autora", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Pridať text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikácia", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Zmeniť prístupové práva", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Naposledy upravil(a) ", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Na<PERSON><PERSON>dy upravené", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Vlastník", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Umiestnenie", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON> s oprávneniami", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Predmet", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Zmeniť prístupové práva", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON> s oprávneniami", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Použiť", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON><PERSON>j <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Oddeľovač desatinných miest", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Náznak typu písma", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Jazyk vzorcov", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Príklad: SÚČET; MIN; MAX; POČET", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Nastavenia makier", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Po vložení obsahu ukázať tlačítko Možnosti vloženia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Miestne nastavenia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Príklad:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Prís<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> tisícov", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "<PERSON>not<PERSON> merania", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Použite oddeľovače na základe miestnych nastavení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Predvolená hodnota priblíženia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Každých 10 minút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Každých 30 minút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Každých 5 minút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "<PERSON><PERSON><PERSON> obnova", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Zak<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Uložiť dočasnú verziu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Št<PERSON><PERSON> od<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Bielorusky", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulharčina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Prednastavený mód cache", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Čeština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Nemčina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Fínčina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Maďarsky", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Palec (miera 2,54 cm)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Talianský", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Laoský", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Lot<PERSON>šský", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "ako OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Pôvodný", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Nórsky", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Poľština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON>sky (Brazília)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON>sky (Portugalsko)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Povoliť všetko", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Povoliť všetky makrá bez oznámenia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovenčina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Zablokovať všetko", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Zablokovať všetky makrá bez upozornenia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "<PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Ukázať oznámenie", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Zablokovať všetky makrá s upozornením", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "ako <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Upozornenie", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>a pomoci hesla", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Zabezpečiť pracovný list", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Podpis", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Upraviť zošit", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Upravením budú zo zošitu odobrané podpisy.<br><PERSON><PERSON><PERSON>te p<PERSON>? ", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON><PERSON> procesor je zabezpečený heslom", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "<PERSON><PERSON><PERSON> ta<PERSON> je potrebné podpísať.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Do tabuľky boli pridané platné pod<PERSON>. Tabuľka je ch<PERSON>ánená pred úpravami.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Niektoré digitálne podpisy v tabuľke sú neplatné alebo ich nebolo možné overiť. Tabuľka je chránená pred úpravami.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Zobraziť podpisy", "SSE.Views.FormatRulesEditDlg.fillColor": "Farba pozadia", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Upozornenie", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 Farebná škála", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 farebná škála", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Všetky orámovania", "SSE.Views.FormatRulesEditDlg.textAppearance": "Vzhľad pruhu", "SSE.Views.FormatRulesEditDlg.textApply": "Použiť na rozsah", "SSE.Views.FormatRulesEditDlg.textAutomatic": "<PERSON>ky", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "<PERSON>mer pruhu", "SSE.Views.FormatRulesEditDlg.textBold": "Tučn<PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "Orámovanie", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Farba orámovania", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Štýl orámovania", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Nie je možné pridať podmienené formátovanie", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "<PERSON><PERSON> bunky", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Vnútorné vertikálne orámovanie", "SSE.Views.FormatRulesEditDlg.textClear": "Vyčistiť", "SSE.Views.FormatRulesEditDlg.textColor": "Farba textu", "SSE.Views.FormatRulesEditDlg.textContext": "Kontext", "SSE.Views.FormatRulesEditDlg.textCustom": "Vlastný", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Orámovanie diagonálne nadol", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Orámovanie diagonálne nahor", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Zadajte platný vzorec.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON>, sa nevyhodnocuje ako <PERSON>, <PERSON><PERSON><PERSON>, čas ani reťazec.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON><PERSON><PERSON> hodnotu.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, nie je p<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, časom alebo reťazcom.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Hodnota pre {0} musí by<PERSON> väčšia ako hodnota pre {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Zadaj<PERSON> {0} a {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Vyplniť", "SSE.Views.FormatRulesEditDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormula": "Vzorec", "SSE.Views.FormatRulesEditDlg.textGradient": "Prechod", "SSE.Views.FormatRulesEditDlg.textIconLabel": "keď {0} {1} a", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "keď {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "<PERSON>ď hodnota je", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Dochádza k prekrytiu j<PERSON>, alebo via<PERSON><PERSON><PERSON> roz<PERSON>hov dát ikon.<br>Upravte hodnoty rozsahov dát ikon, aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k prekrytiu.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Vnútorné <PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Neplatný roz<PERSON>h d<PERSON>.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Položka", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Zľava doprava ", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Orámovanie vľavo", "SSE.Views.FormatRulesEditDlg.textLongBar": "Najdlhší stĺpec", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "<PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Vnútorné <PERSON>e orámovanie", "SSE.Views.FormatRulesEditDlg.textMidpoint": "<PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textNegative": "Negatívne", "SSE.Views.FormatRulesEditDlg.textNewColor": "Pridať novú vlastnú farbu", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Bez orámovania", "SSE.Views.FormatRulesEditDlg.textNone": "Žiadny", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Jedna alebo viac hodnôt nie sú platným percentuálnym podielom.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Zadaná hodnota {0} nie je platným percentom.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Jedna alebo viac hodnôt nie je platným percentilom.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Zadaná hodnota {0} nie je platným percentilom.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Vonkajšie orámovanie", "SSE.Views.FormatRulesEditDlg.textPercent": "Percento", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Pozícia", "SSE.Views.FormatRulesEditDlg.textPositive": "Pozitívne", "SSE.Views.FormatRulesEditDlg.textPresets": "Prednastavené ", "SSE.Views.FormatRulesEditDlg.textPreview": "Náhľad", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "V kritériách podmieneného formátovania pre fare<PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON> pruhy a sady ikon nemôžete použiť relatívne odkazy.", "SSE.Views.FormatRulesEditDlg.textReverse": "Obrátené poradie ikon", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Sprava doľava", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Orámovanie vpravo", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "Rovnaké ako kladné", "SSE.Views.FormatRulesEditDlg.textSelectData": "Vybrať údaje", "SSE.Views.FormatRulesEditDlg.textShortBar": "najkratší stĺpec", "SSE.Views.FormatRulesEditDlg.textShowBar": "Zobraziť iba stĺpce", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Zobraziť iba ikony", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Tento typ odkazu nemožno použiť vo vzorci podmieneného formátovania.<br>Zmeňte odkaz na jednu bunku alebo použite odkaz s funkciou hárka, ako je napríklad =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Preškrtnutie", "SSE.Views.FormatRulesEditDlg.textSubscript": "Dolný index", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Horný index", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textUnderline": "Podčiarknutie", "SSE.Views.FormatRulesEditDlg.tipBorders": "Orámovania", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Účtovníctvo", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.FormatRulesEditDlg.txtFraction": "Zlomok", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Všeobecný", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Žiadna ikona", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percentuálny podiel", "SSE.Views.FormatRulesEditDlg.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Čas", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Upraviť formátovacie pravidlo", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nové formátovacie pravidlo", "SSE.Views.FormatRulesManagerDlg.guestText": "Hosť", "SSE.Views.FormatRulesManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "Na 1 štandardnú odchýlku nad priemer", "SSE.Views.FormatRulesManagerDlg.text1Below": "Na 1 štandardnú odchýlku pod priemer", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 Smerodajná odchýlka nad priemer", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 Smerodajná odchýlka pod priemer", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 Smerodajná odchýlka nad priemer", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 Smerodajná odchýlka pod priemer", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON>d p<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textApply": "Uplatniť na", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Hodnota bunky začína na", "SSE.Views.FormatRulesManagerDlg.textBelow": "Podpriemerný", "SSE.Views.FormatRulesManagerDlg.textBetween": "je medzi {0} a {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Hodnota v bunke", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Gradačná farebná škála", "SSE.Views.FormatRulesManagerDlg.textContains": "Hodnota bunky obsahuje", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Bunka obsahuje prázdnu hodnotu", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Chybný obsah bunky", "SSE.Views.FormatRulesManagerDlg.textDelete": "Odstrániť", "SSE.Views.FormatRulesManagerDlg.textDown": "Presunúť pravidlo dole", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplikovať hodnoty", "SSE.Views.FormatRulesManagerDlg.textEdit": "Upraviť", "SSE.Views.FormatRulesManagerDlg.textEnds": "Hodnota bunky končí na", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Rovné alebo nadpriemerné", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Rovné alebo podpriemerné", "SSE.Views.FormatRulesManagerDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textIconSet": "<PERSON><PERSON> <PERSON>kon", "SSE.Views.FormatRulesManagerDlg.textNew": "Nový", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nie je medzi {0} a {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Hodnota bunky neobsahuje", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Bunka obsahuje prázdnu hodnotu", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Bunka neobsahuje chybu", "SSE.Views.FormatRulesManagerDlg.textRules": "Pravidlá", "SSE.Views.FormatRulesManagerDlg.textScope": "Zobraziť pravidlá formátovania pre", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Vybrať údaje", "SSE.Views.FormatRulesManagerDlg.textSelection": "Súčasný výber", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON><PERSON>ná tabuľka", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Tento pracovný list", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON><PERSON> ", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unik<PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Views.FormatRulesManagerDlg.textUp": "Presunúť pravidlo hore", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Tento prvok upravuje iný používateľ.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "<PERSON>d<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textCategory": "Kategória", "SSE.Views.FormatSettingsDialog.textDecimal": "Desatinn<PERSON>/desatinný", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textLinked": "Odkaz na zdroj", "SSE.Views.FormatSettingsDialog.textSeparator": "Použiť oddeľovač 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Symboly", "SSE.Views.FormatSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAccounting": "Účtovníctvo", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON> s<PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Vlastný", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Zadajte prosím neštandardný číselný formát obozretne. Tabuľkový editor nekontroluje súčtové hodnoty formátu na chyby, ktoré môžu ovplyvniť súbor xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "Zlomok", "SSE.Views.FormatSettingsDialog.txtGeneral": "Všeobecné", "SSE.Views.FormatSettingsDialog.txtNone": "žiadny", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Percentuálny podiel", "SSE.Views.FormatSettingsDialog.txtSample": "Príklad:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Čas", "SSE.Views.FormatSettingsDialog.txtUpto1": "Až na jednu číslicu (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON>ž na dve číslice (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON>ž na tri číslice (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Vybrať skupinu funkcií", "SSE.Views.FormulaDialog.textListDescription": "Vybrať funkciu", "SSE.Views.FormulaDialog.txtRecommended": "Odporúč<PERSON>", "SSE.Views.FormulaDialog.txtSearch": "Hľadať", "SSE.Views.FormulaDialog.txtTitle": "Vložiť funkciu", "SSE.Views.FormulaTab.textAutomatic": "<PERSON>ky", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Vypočítať aktuálny hárok", "SSE.Views.FormulaTab.textCalculateWorkbook": "Vyrátať pracovný zošit", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "Vyrátať", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Vyrátať celý pracovný zošit", "SSE.Views.FormulaTab.txtAdditional": "Ďalšie", "SSE.Views.FormulaTab.txtAutosum": "Automatický súčet", "SSE.Views.FormulaTab.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "Ka<PERSON>ulá<PERSON>", "SSE.Views.FormulaTab.txtFormula": "Funkcia", "SSE.Views.FormulaTab.txtFormulaTip": "Vložiť funkciu", "SSE.Views.FormulaTab.txtMore": "Ďalšie funkcie", "SSE.Views.FormulaTab.txtRecent": "Nedávno p<PERSON>žité", "SSE.Views.FormulaWizard.textAny": "ktorýkoľvek", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Funkcia", "SSE.Views.FormulaWizard.textFunctionRes": "Výsledok fukcie", "SSE.Views.FormulaWizard.textHelp": "Nápovede k tejto funkcií", "SSE.Views.FormulaWizard.textLogical": "Logické", "SSE.Views.FormulaWizard.textNoArgs": "<PERSON><PERSON><PERSON>cia nemá žiadne argumenty", "SSE.Views.FormulaWizard.textNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textRef": "odkaz", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Argumenty funk<PERSON>", "SSE.Views.FormulaWizard.textValue": "Výsledok vzorca", "SSE.Views.HeaderFooterDialog.textAlign": "Zarovnať s okrajmi stránky", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON><PERSON><PERSON> stránky", "SSE.Views.HeaderFooterDialog.textBold": "Tučn<PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textColor": "Farba textu", "SSE.Views.HeaderFooterDialog.textDate": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Odlišná prvá stránka", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Rozdielne nepárne a pá<PERSON> str<PERSON>", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFileName": "Názov súboru", "SSE.Views.HeaderFooterDialog.textFirst": "Prvá strana", "SSE.Views.HeaderFooterDialog.textFooter": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textHeader": "Hlavička", "SSE.Views.HeaderFooterDialog.textInsert": "Vložiť", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "Vľavo", "SSE.Views.HeaderFooterDialog.textMaxError": "Zadaný textový reťazec je príliš dlhý. Znížte počet použitých znakov.", "SSE.Views.HeaderFooterDialog.textNewColor": "Pridať novú vlastnú farbu", "SSE.Views.HeaderFooterDialog.textOdd": "Nepárna strana", "SSE.Views.HeaderFooterDialog.textPageCount": "Počet stránok", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "Prednastavené ", "SSE.Views.HeaderFooterDialog.textRight": "Vpravo", "SSE.Views.HeaderFooterDialog.textScale": "Škáluj s dokumentom", "SSE.Views.HeaderFooterDialog.textSheet": "Názov listu", "SSE.Views.HeaderFooterDialog.textStrikeout": "Preškrtnutie", "SSE.Views.HeaderFooterDialog.textSubscript": "Dolný index", "SSE.Views.HeaderFooterDialog.textSuperscript": "Horný index", "SSE.Views.HeaderFooterDialog.textTime": "Čas", "SSE.Views.HeaderFooterDialog.textTitle": "Nastavenie Hlavičky/päty", "SSE.Views.HeaderFooterDialog.textUnderline": "Podčiarknuť", "SSE.Views.HeaderFooterDialog.tipFontName": "Písmo", "SSE.Views.HeaderFooterDialog.tipFontSize": "Veľkosť písma", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Zobraziť", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Odkaz na", "SSE.Views.HyperlinkSettingsDialog.strRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "List", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Kopírovať", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Vybraný rozsah", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Tu zadajte popis/nadpis", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Tu zadajte odkaz", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Tu zadajte popisku", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Externý odkaz", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Získať odkaz", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Vybrať údaje", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Listy", "SSE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Nastavenie hypertextového odkazu", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Toto pole by malo by<PERSON> vo formá<PERSON> 'http://www.example.com'", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Toto pole je obmedzené na 2083 znakov", "SSE.Views.ImageSettings.textAdvanced": "Zobraziť pokročilé nastavenia", "SSE.Views.ImageSettings.textCrop": "Orezať", "SSE.Views.ImageSettings.textCropFill": "Vyplniť", "SSE.Views.ImageSettings.textCropFit": "Prispôsobiť", "SSE.Views.ImageSettings.textCropToShape": "Orezať podľa tvaru", "SSE.Views.ImageSettings.textEdit": "Upraviť", "SSE.Views.ImageSettings.textEditObject": "Upraviť objekt", "SSE.Views.ImageSettings.textFlip": "Prevrátiť", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textFromStorage": "Z úložiska", "SSE.Views.ImageSettings.textFromUrl": "Z URL adresy ", "SSE.Views.ImageSettings.textHeight": "Výška", "SSE.Views.ImageSettings.textHint270": "Otočiť o 90° doľava", "SSE.Views.ImageSettings.textHint90": "Otočiť o 90° doprava", "SSE.Views.ImageSettings.textHintFlipH": "Prevrátiť horizontálne", "SSE.Views.ImageSettings.textHintFlipV": "Prevrátiť vertikálne", "SSE.Views.ImageSettings.textInsert": "Nahradiť obrázok", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "Predvolená veľkosť", "SSE.Views.ImageSettings.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Views.ImageSettings.textRotate90": "Otočiť o 90°", "SSE.Views.ImageSettings.textRotation": "Rot<PERSON>cia", "SSE.Views.ImageSettings.textSize": "Veľkosť", "SSE.Views.ImageSettings.textWidth": "Šírka", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Neposúvať alebo nemeniť veľkosť s bunkami", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternatívny text", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Alternatívne textové zobrazenie informácií o vizuálnych objektoch, ktoré sa prečítajú ľuďom s poruchou videnia alebo kognitívnymi poruchami, aby sa im pomohlo lepšie porozumieť, ak<PERSON> informá<PERSON> sú na obrázku, automatickom tvarovaní, grafe alebo tabuľke. ", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "N<PERSON>zov", "SSE.Views.ImageSettingsAdvanced.textAngle": "Uhol", "SSE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontálne", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Presúvať ale nemeniť veľkosť spoločne s bunkami", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rot<PERSON>cia", "SSE.Views.ImageSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON><PERSON> bunky", "SSE.Views.ImageSettingsAdvanced.textTitle": "Obrázok - Pokročilé nastavenia", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Presúvať a meniť veľkosť spoločne s bunkami", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipAbout": "O aplikácii", "SSE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "S<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Pluginy", "SSE.Views.LeftMenu.tipSearch": "Hľadať", "SSE.Views.LeftMenu.tipSpellcheck": "Kontrola pra<PERSON>pisu", "SSE.Views.LeftMenu.tipSupport": "Spätná väzba a podpora", "SSE.Views.LeftMenu.txtDeveloper": "VÝVOJÁRSKY REŽIM", "SSE.Views.LeftMenu.txtLimit": "Obmedziť prístup", "SSE.Views.LeftMenu.txtTrial": "Skúšobn<PERSON> re<PERSON>im", "SSE.Views.LeftMenu.txtTrialDev": "Skúšobný vývojársky režim", "SSE.Views.MacroDialog.textMacro": "Názov makra", "SSE.Views.MacroDialog.textTitle": "Priradiť makro", "SSE.Views.MainSettingsPrint.okButtonText": "Uložiť", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Na šírku", "SSE.Views.MainSettingsPrint.strLeft": "Vľavo", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Na výšku", "SSE.Views.MainSettingsPrint.strPrint": "Tlačiť", "SSE.Views.MainSettingsPrint.strPrintTitles": "Tlač názvu", "SSE.Views.MainSettingsPrint.strRight": "Vpravo", "SSE.Views.MainSettingsPrint.strTop": "Hore", "SSE.Views.MainSettingsPrint.textActualSize": "Aktuálna veľkosť", "SSE.Views.MainSettingsPrint.textCustom": "Vlastný", "SSE.Views.MainSettingsPrint.textCustomOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textFitCols": "Prispôsobiť všetky stĺpce na jednu stránku", "SSE.Views.MainSettingsPrint.textFitPage": "Prispôsobiť list na jednu stranu", "SSE.Views.MainSettingsPrint.textFitRows": "Prispôsobiť všetky riadky na jednu stránku", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Veľkosť stránky", "SSE.Views.MainSettingsPrint.textPrintGrid": "Vytlačiť mriežky", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Vytlačiť nadpisy riadkov a stĺpcov", "SSE.Views.MainSettingsPrint.textRepeat": "Opakovať...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Vľavo <PERSON>ak<PERSON>ť stĺpce", "SSE.Views.MainSettingsPrint.textRepeatTop": "Opakovať riadky na vrchu", "SSE.Views.MainSettingsPrint.textSettings": "Nastavenie pre", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Existujúce pomenované rozsahy nemožno upraviť a nové nemôžu byť momentálne vytvorené<br>, keďže niektoré z nich sú práve editované.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Definova<PERSON><PERSON> n<PERSON>zov", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Upozornenie", "SSE.Views.NamedRangeEditDlg.strWorkbook": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textExistName": "CHYBA! Rozsah s takýmto názvom už existuje", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Názov musí začínať písmenom alebo podčiarknutím a nesmie obsahovať neplatné znaky.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "CHYBA! Neplatný rozsah bunky", "SSE.Views.NamedRangeEditDlg.textIsLocked": "CHYBA! Tento prvok upravuje iný používateľ.", "SSE.Views.NamedRangeEditDlg.textName": "N<PERSON>zov", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, ktorý sa pokúšate použiť, je už uvedený v bunkových vzorcoch. Použite iné meno.", "SSE.Views.NamedRangeEditDlg.textScope": "Rámec/rozsah", "SSE.Views.NamedRangeEditDlg.textSelectData": "Vybrať údaje", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Upraviť meno", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nový názov", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "Vložiť názov", "SSE.Views.NameManagerDlg.closeButtonText": "Zatvoriť", "SSE.Views.NameManagerDlg.guestText": "Návštevník", "SSE.Views.NameManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDelete": "Vymazať", "SSE.Views.NameManagerDlg.textEdit": "Upraviť", "SSE.Views.NameManagerDlg.textEmpty": "Žiadne pomenované rozsahy neboli vytvorené.<br>Vytvorte aspoň jeden pomenovaný rozsah a zobrazí sa v tomto poli.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterSheet": "Rozsah názvov do listu", "SSE.Views.NameManagerDlg.textFilterTableNames": "Názvy tabuliek", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Rozsah názvov do zošita", "SSE.Views.NameManagerDlg.textNew": "Nový", "SSE.Views.NameManagerDlg.textnoNames": "Neboli nájdené žiadne pomenované rozsahy zodpovedajúce vášmu filtrovaniu.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "Rámec/rozsah", "SSE.Views.NameManagerDlg.textWorkbook": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.tipIsLocked": "Tento prvok upravuje iný používateľ.", "SSE.Views.NameManagerDlg.txtTitle": "Správca názvov", "SSE.Views.NameManagerDlg.warnDelete": "Ste si naozaj istý, že chcete názov {0} zmazať?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textLeft": "Vľavo", "SSE.Views.PageMarginsDialog.textRight": "Vpravo", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Hore", "SSE.Views.ParagraphSettings.strLineHeight": "Riadkovanie", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Riadkovanie medzi odstavcami", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON>a", "SSE.Views.ParagraphSettings.strSpacingBefore": "Pred", "SSE.Views.ParagraphSettings.textAdvanced": "Zobraziť pokročilé nastavenia", "SSE.Views.ParagraphSettings.textAt": "V/na", "SSE.Views.ParagraphSettings.textAtLeast": "Najmenej", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Presne", "SSE.Views.ParagraphSettings.txtAutoText": "<PERSON>ky", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Špecifikované tabulátory sa objavia v tomto poli", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Všetko veľkým", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Odsadenia", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Vľavo", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Riadkovanie", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Vpravo", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON>a", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Pred", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Špeciálne", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Od", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Písmo", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Zarážky a medzery", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Medzery", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Prečiarknutie", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Dolný index", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Horný index", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Zarovnanie", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Viacnásobný", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Medzery medzi písmenami", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Predvolený tabulátor", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efekty", "SSE.Views.ParagraphSettingsAdvanced.textExact": "presne", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prvý riadok", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "<PERSON><PERSON><PERSON><PERSON>ý", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Podľ<PERSON>ov", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(žiadne)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Odstrániť", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Odstrániť všetko", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Špecifikovať", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Vľavo", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Pozícia tabulá<PERSON>a", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Vpravo", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Odsek - Pokročilé nastavenia", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON>ky", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "rovná se", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "Nekončí s", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "medzi", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "nie medzi", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "nerovná sa", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "je vä<PERSON><PERSON><PERSON> ako", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "je vä<PERSON><PERSON>ie alebo rovné ", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "Je menšie než", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "je men<PERSON>ie alebo rovné ", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "Začať s", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "Nezačína s", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "Končí s", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Zobraziť položky, ktorých štítok:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Zobraziť položky pre ktoré:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Použite ? na zobrazenie akéhokoľvek jedného znaku", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Použite * na zobrazenie ľubovoľnej série znakov", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "a", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtrovanie š<PERSON>kov", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "<PERSON>ky", "SSE.Views.PivotGroupDialog.textBy": "Od", "SSE.Views.PivotGroupDialog.textDays": "Dni", "SSE.Views.PivotGroupDialog.textEnd": "Končí na", "SSE.Views.PivotGroupDialog.textError": "Toto pole musí by<PERSON> č<PERSON> hodnota", "SSE.Views.PivotGroupDialog.textGreaterError": "Koncov<PERSON> č<PERSON>lo musí byť väčšie ako počiatoč<PERSON><PERSON> číslo", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "Počet dní", "SSE.Views.PivotGroupDialog.textQuart": "Štvrtiny", "SSE.Views.PivotGroupDialog.textSec": "Sekundy", "SSE.Views.PivotGroupDialog.textStart": "Začínajúci pri", "SSE.Views.PivotGroupDialog.textYear": "Roky", "SSE.Views.PivotGroupDialog.txtTitle": "Zoskupovanie", "SSE.Views.PivotSettings.textAdvanced": "Zobraziť pokročilé nastavenia", "SSE.Views.PivotSettings.textColumns": "Stĺpce", "SSE.Views.PivotSettings.textFields": "Vybrať polia", "SSE.Views.PivotSettings.textFilters": "Filtre", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Hodnoty", "SSE.Views.PivotSettings.txtAddColumn": "Pridať k stĺpcom", "SSE.Views.PivotSettings.txtAddFilter": "Pridať k filtrom", "SSE.Views.PivotSettings.txtAddRow": "Pridať k riadkom", "SSE.Views.PivotSettings.txtAddValues": "Pridať k hodnotám", "SSE.Views.PivotSettings.txtFieldSettings": "Nastavenie poľa", "SSE.Views.PivotSettings.txtMoveBegin": "Presunúť na začiatok", "SSE.Views.PivotSettings.txtMoveColumn": "Presunúť do stĺpcov", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON>sun<PERSON><PERSON> dole ", "SSE.Views.PivotSettings.txtMoveEnd": "Presunúť na koniec", "SSE.Views.PivotSettings.txtMoveFilter": "Presunúť do filtrov", "SSE.Views.PivotSettings.txtMoveRow": "Presunúť do riadkov", "SSE.Views.PivotSettings.txtMoveUp": "Presunúť hore", "SSE.Views.PivotSettings.txtMoveValues": "Presunúť do hodnôt", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.PivotSettingsAdvanced.strLayout": "Názov a rozloženie", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternatívny text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Alternatívne textové zobrazenie informácií o vizuálnych objektoch, ktoré sa prečítajú ľuďom s poruchou videnia alebo kognitívnymi poruchami, aby sa im pomohlo lepšie porozumieť, ak<PERSON> informá<PERSON> sú na obrázku, automatickom tvarovaní, grafe alebo tabuľke. ", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "N<PERSON>zov", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Dátový zdroj", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Zobraziť pole v oblasti pre záznam filtru", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON>, potom priečne", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Celk<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "<PERSON> hlav<PERSON> ", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON><PERSON><PERSON>, potom dole", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Vybrať údaje", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Zobraziť pre stĺpce", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Zobraziť hlavičky polí pre riadky a stĺpce", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Zobraziť pre riadky", "SSE.Views.PivotSettingsAdvanced.textTitle": "Kontingenčná tabuľka - pokročilé", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Prehľad polí filtra podľa stĺpca", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Prehľad polí filtra podľa riadku", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.PivotSettingsAdvanced.txtName": "<PERSON><PERSON>", "SSE.Views.PivotTable.capBlankRows": "Prázdne riadky", "SSE.Views.PivotTable.capGrandTotals": "Celk<PERSON><PERSON>", "SSE.Views.PivotTable.capLayout": "Rozloženie hĺásenia", "SSE.Views.PivotTable.capSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Zobraziť všetky medzisúčty na konci skupiny", "SSE.Views.PivotTable.mniInsertBlankLine": "Za každú z položiek vložiť prázdny riadok", "SSE.Views.PivotTable.mniLayoutCompact": "Zobraziť v kompaktnej forme", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Neopakovať štítky všetkých položiek", "SSE.Views.PivotTable.mniLayoutOutline": "Zobraziť ako obrys", "SSE.Views.PivotTable.mniLayoutRepeat": "Zopakovať všetky štítky položky", "SSE.Views.PivotTable.mniLayoutTabular": "Zobraziť v podobe tabuľky", "SSE.Views.PivotTable.mniNoSubtotals": "Nezobrazovať medzisúčty", "SSE.Views.PivotTable.mniOffTotals": "Vypnuté pre riadky a stĺpce", "SSE.Views.PivotTable.mniOnColumnsTotals": "Zapnuté iba pre stĺpce", "SSE.Views.PivotTable.mniOnRowsTotals": "Zapnuté iba pre riadky", "SSE.Views.PivotTable.mniOnTotals": "Zapnuté iba pre riadky a stĺpce", "SSE.Views.PivotTable.mniRemoveBlankLine": "Odobrať prázdny riadok za každou z položiek", "SSE.Views.PivotTable.mniTopSubtotals": "Zobraziť všetky medzisúčty v hornej časti skupiny", "SSE.Views.PivotTable.textColBanded": "Zoskupené stĺpce", "SSE.Views.PivotTable.textColHeader": "Záhlavia stĺpcov", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "Hlavičky riadku", "SSE.Views.PivotTable.tipCreatePivot": "Vložiť Kontingenťnú Tabuľku", "SSE.Views.PivotTable.tipGrandTotals": "Zobraziť alebo skryť celkové súčty", "SSE.Views.PivotTable.tipRefresh": "Aktualizovať informácie z dátového zdroja", "SSE.Views.PivotTable.tipSelect": "Vybrať celú kontingenčnú tabuľku", "SSE.Views.PivotTable.tipSubtotals": "Zobraziť alebo skryť medzisúčty", "SSE.Views.PivotTable.txtCreate": "Vložiť tabuľku", "SSE.Views.PivotTable.txtPivotTable": "Kontingenčná tabuľka", "SSE.Views.PivotTable.txtRefresh": "Znovu načítať", "SSE.Views.PivotTable.txtSelect": "Vybrať", "SSE.Views.PrintSettings.btnDownload": "Uložiť a stiahnuť", "SSE.Views.PrintSettings.btnPrint": "Uložiť a vytlačiť", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "Na šírku", "SSE.Views.PrintSettings.strLeft": "Vľavo", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Na výšku", "SSE.Views.PrintSettings.strPrint": "Tlačiť", "SSE.Views.PrintSettings.strPrintTitles": "Tlač názvu", "SSE.Views.PrintSettings.strRight": "Vpravo", "SSE.Views.PrintSettings.strShow": "Zobraziť", "SSE.Views.PrintSettings.strTop": "Hore", "SSE.Views.PrintSettings.textActualSize": "Aktuálna veľkosť", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON><PERSON><PERSON> listy", "SSE.Views.PrintSettings.textCurrentSheet": "Aktuálny list", "SSE.Views.PrintSettings.textCustom": "Vlastný", "SSE.Views.PrintSettings.textCustomOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textFitCols": "Prispôsobiť všetky stĺpce na jednu stránku", "SSE.Views.PrintSettings.textFitPage": "Prispôsobiť list na jednu stranu", "SSE.Views.PrintSettings.textFitRows": "Prispôsobiť všetky riadky na jednu stránku", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorovať oblasť tlače", "SSE.Views.PrintSettings.textLayout": "Rozloženie", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Veľkosť stránky", "SSE.Views.PrintSettings.textPrintGrid": "Vytlačiť mriežky", "SSE.Views.PrintSettings.textPrintHeadings": "Vytlačiť nadpisy riadkov a stĺpcov", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRepeat": "Opakovať...", "SSE.Views.PrintSettings.textRepeatLeft": "Vľavo <PERSON>ak<PERSON>ť stĺpce", "SSE.Views.PrintSettings.textRepeatTop": "Opakovať riadky na vrchu", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Nastavenie listu", "SSE.Views.PrintSettings.textShowDetails": "Zobraziť detaily", "SSE.Views.PrintSettings.textShowGrid": "Zobraziť čiary mriežky", "SSE.Views.PrintSettings.textShowHeadings": "Zobraziť hlavičky riadkov a stĺpcov", "SSE.Views.PrintSettings.textTitle": "Nastavenia tlače", "SSE.Views.PrintSettings.textTitlePDF": "Nastavení pro PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "Prvý stĺpec", "SSE.Views.PrintTitlesDialog.textFirstRow": "Prvý riadok", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Ukotvené stĺpce", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Ukot<PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.PrintTitlesDialog.textLeft": "Vľavo <PERSON>ak<PERSON>ť stĺpce", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Neopakovať", "SSE.Views.PrintTitlesDialog.textRepeat": "Opakovať...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Vybrať rozsah", "SSE.Views.PrintTitlesDialog.textTitle": "Tlač názvu", "SSE.Views.PrintTitlesDialog.textTop": "Opakovať riadky na vrchu", "SSE.Views.PrintWithPreview.txtActualSize": "Aktuálna veľkosť", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON><PERSON><PERSON> listy", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Použiť na všetky listy", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Aktuálny list", "SSE.Views.PrintWithPreview.txtCustom": "Vlastný", "SSE.Views.PrintWithPreview.txtCustomOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtEmptyTable": "<PERSON>e je čo t<PERSON>, pretože tabuľka je prázdna", "SSE.Views.PrintWithPreview.txtFitCols": "Prispôsobiť všetky stĺpce na jednu stránku", "SSE.Views.PrintWithPreview.txtFitPage": "Prispôsobiť list na jednu stranu", "SSE.Views.PrintWithPreview.txtFitRows": "Prispôsobiť všetky riadky na jednu stránku", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Mriežky a nadpisy", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Nastavenie Hlavičky/päty", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorovať oblasť tlače", "SSE.Views.PrintWithPreview.txtLandscape": "Na šírku", "SSE.Views.PrintWithPreview.txtLeft": "Vľavo", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "z {0}", "SSE.Views.PrintWithPreview.txtPage": "Strán<PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "<PERSON><PERSON><PERSON> je nep<PERSON>", "SSE.Views.PrintWithPreview.txtPageOrientation": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageSize": "Veľkosť stránky", "SSE.Views.PrintWithPreview.txtPortrait": "Na výšku", "SSE.Views.PrintWithPreview.txtPrint": "Tlačiť", "SSE.Views.PrintWithPreview.txtPrintGrid": "Vytlačiť mriežky", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Vytlačiť nadpisy riadkov a stĺpcov", "SSE.Views.PrintWithPreview.txtPrintRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintTitles": "Tlač názvu", "SSE.Views.PrintWithPreview.txtRepeat": "Opakovať...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Vľavo <PERSON>ak<PERSON>ť stĺpce", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Opakovať riadky na vrchu", "SSE.Views.PrintWithPreview.txtRight": "Vpravo", "SSE.Views.PrintWithPreview.txtSave": "Uložiť", "SSE.Views.PrintWithPreview.txtScaling": "Škálovanie", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Nastavenia listu", "SSE.Views.PrintWithPreview.txtSheet": "List: {0}", "SSE.Views.PrintWithPreview.txtTop": "Hore", "SSE.Views.ProtectDialog.textExistName": "CHYBA! Rozsah s takým názvom úž existuje", "SSE.Views.ProtectDialog.textInvalidName": "Názov rozsahu musí začínať písmenom a môže obsahovať iba písmená, čísla a medzery.", "SSE.Views.ProtectDialog.textInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.ProtectDialog.textSelectData": "Vybrať údaje", "SSE.Views.ProtectDialog.txtAllow": "Umožniť všetkým užívateľom tohto listu", "SSE.Views.ProtectDialog.txtAutofilter": "Použiť automatické filtrovanie", "SSE.Views.ProtectDialog.txtDelCols": "Zmazať stĺpce", "SSE.Views.ProtectDialog.txtDelRows": "Zmazať riadky", "SSE.Views.ProtectDialog.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.ProtectDialog.txtFormatCells": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtFormatCols": "Formátovať stĺpce", "SSE.Views.ProtectDialog.txtFormatRows": "Formátovať riadky", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Heslá sa nezhodujú", "SSE.Views.ProtectDialog.txtInsCols": "Vložiť stĺpce", "SSE.Views.ProtectDialog.txtInsHyper": "Vložiť hypertextový odkaz", "SSE.Views.ProtectDialog.txtInsRows": "Vložiť riadky", "SSE.Views.ProtectDialog.txtObjs": "Upraviť objekty", "SSE.Views.ProtectDialog.txtOptional": "voliteľný", "SSE.Views.ProtectDialog.txtPassword": "He<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Použite kontingenčnú tabuľku a kontingenčný graf", "SSE.Views.ProtectDialog.txtProtect": "Ochrániť", "SSE.Views.ProtectDialog.txtRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON>op<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtScen": "Upraviť scenáre", "SSE.Views.ProtectDialog.txtSelLocked": "Vybrať uzamknuté bunky", "SSE.Views.ProtectDialog.txtSelUnLocked": "Vybrať odomknuté bunky", "SSE.Views.ProtectDialog.txtSheetDescription": "Pokiaľ chcete zabrániť nechceným zmenám ostatnými, obmedzte ich možnosť upravovať.", "SSE.Views.ProtectDialog.txtSheetTitle": "Zabezpečiť list", "SSE.Views.ProtectDialog.txtSort": "Zoradiť", "SSE.Views.ProtectDialog.txtWarning": "Upozornenie: Ak stratíte alebo zabudnete heslo, nemožno ho obnoviť. Uschovajte ho na bezpečnom mieste.", "SSE.Views.ProtectDialog.txtWBDescription": "Môžete použiť heslo na zabránenie zobrazenia, pridávania, presúvania, odstránenia, skrytia alebo premenovania súboru inýmí užívateľmi.", "SSE.Views.ProtectDialog.txtWBTitle": "Zabezpečiť štruktúru zošitu", "SSE.Views.ProtectRangesDlg.guestText": "Hosť", "SSE.Views.ProtectRangesDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Odstrániť", "SSE.Views.ProtectRangesDlg.textEdit": "Upraviť", "SSE.Views.ProtectRangesDlg.textEmpty": "Nie sú povolené žiadne rozsahy pre úpravy.", "SSE.Views.ProtectRangesDlg.textNew": "Nový", "SSE.Views.ProtectRangesDlg.textProtect": "Zabezpečiť list", "SSE.Views.ProtectRangesDlg.textPwd": "He<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "<PERSON><PERSON><PERSON><PERSON> odomknuté heslom keď je list zabezpečený(toto funguje iba pre uzamknuté bunky)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Tento prvok upravuje iný používateľ.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Upraviť rozsah", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nový rozsah", "SSE.Views.ProtectRangesDlg.txtNo": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtTitle": "Umožniť užívateľom upravovať rozsahy", "SSE.Views.ProtectRangesDlg.txtYes": "Á<PERSON>", "SSE.Views.ProtectRangesDlg.warnDelete": "Ste si naozaj istý, že chcete názov {0} zmazať?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Stĺpce", "SSE.Views.RemoveDuplicatesDialog.textDescription": "<PERSON>k chcete odstrániť dup<PERSON><PERSON><PERSON> ho<PERSON>, vyberte jeden alebo viacero stĺpcov, ktor<PERSON> obs<PERSON> du<PERSON>.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "<PERSON><PERSON> d<PERSON>ta maj<PERSON> h<PERSON>", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Vybrať všetko", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Odobrať duplicity", "SSE.Views.RightMenu.txtCellSettings": "Nasta<PERSON><PERSON> buniek", "SSE.Views.RightMenu.txtChartSettings": "Nastavenia grafu", "SSE.Views.RightMenu.txtImageSettings": "Nastavenie obrázka", "SSE.Views.RightMenu.txtParagraphSettings": "Nastavenia odseku", "SSE.Views.RightMenu.txtPivotSettings": "Nastavenia kontingenčnej tabuľky", "SSE.Views.RightMenu.txtSettings": "Všeobecné nastavenia", "SSE.Views.RightMenu.txtShapeSettings": "Nastavenia tvaru", "SSE.Views.RightMenu.txtSignatureSettings": "Nastavenia Podpisu", "SSE.Views.RightMenu.txtSlicerSettings": "Nastavenie prierezu", "SSE.Views.RightMenu.txtSparklineSettings": "Nastavenia Sparkline ", "SSE.Views.RightMenu.txtTableSettings": "Nastavenie tabuľky", "SSE.Views.RightMenu.txtTextArtSettings": "Nastavenie Text Art", "SSE.Views.ScaleDialog.textAuto": "<PERSON>ky", "SSE.Views.ScaleDialog.textError": "Zadaná hodnota nie je platná.", "SSE.Views.ScaleDialog.textFewPages": "Strany", "SSE.Views.ScaleDialog.textFitTo": "Prispôsobiť ku", "SSE.Views.ScaleDialog.textHeight": "Výška", "SSE.Views.ScaleDialog.textManyPages": "Strany", "SSE.Views.ScaleDialog.textOnePage": "Strán<PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Škáluj do", "SSE.Views.ScaleDialog.textTitle": "Nastavenia škálovania ", "SSE.Views.ScaleDialog.textWidth": "Šírka", "SSE.Views.SetValueDialog.txtMaxText": "Maximálna hodnota pre toto pole je {0}", "SSE.Views.SetValueDialog.txtMinText": "Minimálna hodnota pre toto pole je {0}", "SSE.Views.ShapeSettings.strBackground": "Farba pozadia", "SSE.Views.ShapeSettings.strChange": "Zmeniť automatický tvar", "SSE.Views.ShapeSettings.strColor": "Farba", "SSE.Views.ShapeSettings.strFill": "Vyplniť", "SSE.Views.ShapeSettings.strForeground": "Farba popredia", "SSE.Views.ShapeSettings.strPattern": "Vzor", "SSE.Views.ShapeSettings.strShadow": "Ukázať tieň", "SSE.Views.ShapeSettings.strSize": "Veľkosť", "SSE.Views.ShapeSettings.strStroke": "Čiara/líniový graf", "SSE.Views.ShapeSettings.strTransparency": "Priehľ<PERSON>nosť", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdvanced": "Zobraziť pokročilé nastavenia", "SSE.Views.ShapeSettings.textAngle": "Uhol", "SSE.Views.ShapeSettings.textBorderSizeErr": "Zadaná hodnota je nesprávna.<br><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> hodnotu medzi 0 pt a 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Vyplniť farbou", "SSE.Views.ShapeSettings.textDirection": "Smer", "SSE.Views.ShapeSettings.textEmptyPattern": "Bez vzoru", "SSE.Views.ShapeSettings.textFlip": "Prevrátiť", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromStorage": "Z úložiska", "SSE.Views.ShapeSettings.textFromUrl": "Z URL adresy ", "SSE.Views.ShapeSettings.textGradient": "Body prechodu", "SSE.Views.ShapeSettings.textGradientFill": "Výplň prechodom", "SSE.Views.ShapeSettings.textHint270": "Otočiť o 90° doľava", "SSE.Views.ShapeSettings.textHint90": "Otočiť o 90° doprava", "SSE.Views.ShapeSettings.textHintFlipH": "Prevrátiť horizontálne", "SSE.Views.ShapeSettings.textHintFlipV": "Prevrátiť vertikálne", "SSE.Views.ShapeSettings.textImageTexture": "Obrázok alebo textúra", "SSE.Views.ShapeSettings.textLinear": "Lineárny/čiarový", "SSE.Views.ShapeSettings.textNoFill": "Bez výplne", "SSE.Views.ShapeSettings.textOriginalSize": "Pôvodná veľkosť", "SSE.Views.ShapeSettings.textPatternFill": "Vzor", "SSE.Views.ShapeSettings.textPosition": "Pozícia", "SSE.Views.ShapeSettings.textRadial": "Kruhový/hviezdicovitý", "SSE.Views.ShapeSettings.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Views.ShapeSettings.textRotate90": "Otočiť o 90°", "SSE.Views.ShapeSettings.textRotation": "Rot<PERSON>cia", "SSE.Views.ShapeSettings.textSelectImage": "Vybrať obrázok", "SSE.Views.ShapeSettings.textSelectTexture": "Vybrať", "SSE.Views.ShapeSettings.textStretch": "Roztiahnuť", "SSE.Views.ShapeSettings.textStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Z textúry", "SSE.Views.ShapeSettings.textTile": "Dlaždica", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Pridať spádový bod", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Odstrániť prechodový bod", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>/baliaci papier", "SSE.Views.ShapeSettings.txtCanvas": "Pl<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "Ka<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Tmavá štruktúra", "SSE.Views.ShapeSettings.txtGrain": "Textúra/zrnitosť", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> pap<PERSON>", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Koža/kožený", "SSE.Views.ShapeSettings.txtNoBorders": "Bez čiary", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "Drevo", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Stĺpce", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Osadenie textu", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Neposúvať alebo nemeniť veľkosť s bunkami", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternatívny text", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Alternatívne textové zobrazenie informácií o vizuálnych objektoch, ktoré sa prečítajú ľuďom s poruchou videnia alebo kognitívnymi poruchami, aby sa im pomohlo lepšie porozumieť, ak<PERSON> informá<PERSON> sú na obrázku, automatickom tvarovaní, grafe alebo tabuľke. ", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "N<PERSON>zov", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Uhol", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Veľkosť začiatku", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Štýl začiatku", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Skosenie", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Typ zakončenia", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Počet stĺpcov", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Veľkosť konca", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Štýl konca", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>ý", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Výška", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontálne", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Typ prip<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Vľavo", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Štýl čiary", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Sklon", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Presúvať ale nemeniť veľkosť spoločne s bunkami", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Povoliť textu presiahnuť tvar", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Zmeniť veľkosť tvaru, aby zodpovedala textu", "SSE.Views.ShapeSettingsAdvanced.textRight": "Vpravo", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rot<PERSON>cia", "SSE.Views.ShapeSettingsAdvanced.textRound": "Z<PERSON><PERSON>n<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Veľkosť", "SSE.Views.ShapeSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON><PERSON> bunky", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Me<PERSON>zera medzi stĺpcami", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Štvorec", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON><PERSON> pole", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Tvar - Pokročilé nastavenia", "SSE.Views.ShapeSettingsAdvanced.textTop": "Hore", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Presúvať a meniť veľkosť spoločne s bunkami", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Nastavenia tvaru", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Šírka", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Upozornenie", "SSE.Views.SignatureSettings.strDelete": "Odstrániť podpis", "SSE.Views.SignatureSettings.strDetails": "Podrobnosti podpisu", "SSE.Views.SignatureSettings.strInvalid": "Neplat<PERSON><PERSON>", "SSE.Views.SignatureSettings.strRequested": "Vyžadované pod<PERSON>y", "SSE.Views.SignatureSettings.strSetup": "Nastavenia podpisu", "SSE.Views.SignatureSettings.strSign": "Podpísať", "SSE.Views.SignatureSettings.strSignature": "Podpis", "SSE.Views.SignatureSettings.strSigner": "Podpisovateľ", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "Aj tak upraviť", "SSE.Views.SignatureSettings.txtEditWarning": "Upravením budú zo zošitu odobrané podpisy.<br><PERSON><PERSON><PERSON>te p<PERSON>? ", "SSE.Views.SignatureSettings.txtRemoveWarning": "Chcete odstrániť tento podpis?<br><PERSON>to krok je nezvrat<PERSON>ý.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "<PERSON><PERSON><PERSON> ta<PERSON> je potrebné podpísať.", "SSE.Views.SignatureSettings.txtSigned": "Do tabuľky boli pridané platné pod<PERSON>. Tabuľka je ch<PERSON>ánená pred úpravami.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Niektoré digitálne podpisy v tabuľke sú neplatné alebo ich nebolo možné overiť. Tabuľka je chránená pred úpravami.", "SSE.Views.SlicerAddDialog.textColumns": "Stĺpce", "SSE.Views.SlicerAddDialog.txtTitle": "Vložiť prierezy", "SSE.Views.SlicerSettings.strHideNoData": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.strIndNoData": "Vizuálne označte položky bez údajov", "SSE.Views.SlicerSettings.strShowDel": "Zobraziť položky odstránené zo zdroja údajov", "SSE.Views.SlicerSettings.strShowNoData": "<PERSON>ko posledné zobraziť položky bez dát", "SSE.Views.SlicerSettings.strSorting": "Triedenie a filtrovanie", "SSE.Views.SlicerSettings.textAdvanced": "Zobraziť pokročilé nastavenia", "SSE.Views.SlicerSettings.textAsc": "Vzostupne", "SSE.Views.SlicerSettings.textAZ": "A po Z", "SSE.Views.SlicerSettings.textButtons": "Tlačidlá", "SSE.Views.SlicerSettings.textColumns": "Stĺpce", "SSE.Views.SlicerSettings.textDesc": "Zostupne", "SSE.Views.SlicerSettings.textHeight": "Výška", "SSE.Views.SlicerSettings.textHor": "V<PERSON><PERSON>vný", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textLargeSmall": "od najvä<PERSON>š<PERSON>ch po najmenších", "SSE.Views.SlicerSettings.textLock": "Vypnúť možnosť zmeny veľkosti a presunu", "SSE.Views.SlicerSettings.textNewOld": "od najnovších po najstaršie", "SSE.Views.SlicerSettings.textOldNew": "od najstaršieho po najnovší", "SSE.Views.SlicerSettings.textPosition": "Pozícia", "SSE.Views.SlicerSettings.textSize": "Veľkosť", "SSE.Views.SlicerSettings.textSmallLarge": "od najmenš<PERSON>ch po najväčšie", "SSE.Views.SlicerSettings.textStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textWidth": "Šírka", "SSE.Views.SlicerSettings.textZA": "Z po A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Tlačidlá", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Stĺpce", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Výška", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Vizuálne označte položky bez údajov", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Referencie", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Zobraziť položky odstránené zo zdroja údajov", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Zobraziť hlavičku", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "<PERSON>ko posledné zobraziť položky bez dát", "SSE.Views.SlicerSettingsAdvanced.strSize": "Veľkosť", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Triedenie a filtrovanie", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Štýl a Veľkosť ", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Šírka", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Neposúvať alebo nemeniť veľkosť s bunkami", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternatívny text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Alternatívne textové zobrazenie informácií o vizuálnych objektoch, ktoré sa prečítajú ľuďom s poruchou videnia alebo kognitívnymi poruchami, aby sa im pomohlo lepšie porozumieť, ak<PERSON> informá<PERSON> sú na obrázku, automatickom tvarovaní, grafe alebo tabuľke. ", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "N<PERSON>zov", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Vzostupne", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A po Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Zostupne", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Názov ktorý sa použije vo vzorcoch", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Hlavička", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "od najvä<PERSON>š<PERSON>ch po najmenších", "SSE.Views.SlicerSettingsAdvanced.textName": "N<PERSON>zov", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "od najnovších po najstaršie", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "od najstaršieho po najnovší", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Presúvať ale nemeniť veľkosť spoločne s bunkami", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "od najmenš<PERSON>ch po najväčšie", "SSE.Views.SlicerSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON><PERSON> bunky", "SSE.Views.SlicerSettingsAdvanced.textSort": "Zoradiť", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Názov zdroja", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer – Rozšírené nastavenia", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Presúvať a meniť veľkosť spoločne s bunkami", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z po A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.SortDialog.errorEmpty": "Všetky kritériá triedenia musia mať špecifikovaný stĺpec alebo riadok.", "SSE.Views.SortDialog.errorMoreOneCol": "Je vybratý viac ako jeden stĺpec", "SSE.Views.SortDialog.errorMoreOneRow": "Je vybratý viac než jeden riadok.", "SSE.Views.SortDialog.errorNotOriginalCol": "Stĺpec, k<PERSON><PERSON> ste vybrali, nie je v pôvodne vybratom rozsahu.", "SSE.Views.SortDialog.errorNotOriginalRow": "Vybratý riadok nie je v pôvodne vybratom rozsahu.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 sa triedi podľa rovnakej farby viac než jeden raz.<br><PERSON><PERSON><PERSON><PERSON><PERSON> duplicitné kritériá triedenia a skúste znova. ", "SSE.Views.SortDialog.errorSameColumnValue": "%1 je zoradené podľa hodnôt viac než jeden kr<PERSON>t.<br>Zmažte duplicitné kritérium zoradenia a skúste to znova.", "SSE.Views.SortDialog.textAdd": "Pridať úroveň", "SSE.Views.SortDialog.textAsc": "Vzostupne", "SSE.Views.SortDialog.textAuto": "<PERSON>ky", "SSE.Views.SortDialog.textAZ": "A po Z", "SSE.Views.SortDialog.textBelow": "pod", "SSE.Views.SortDialog.textCellColor": "Farba bunky", "SSE.Views.SortDialog.textColumn": "Stĺpec", "SSE.Views.SortDialog.textCopy": "Skopírovať úroveň", "SSE.Views.SortDialog.textDelete": "Zmazať úroveň", "SSE.Views.SortDialog.textDesc": "Zostupne", "SSE.Views.SortDialog.textDown": "Presunúť o stu<PERSON>ň dole", "SSE.Views.SortDialog.textFontColor": "Farba písma", "SSE.Views.SortDialog.textLeft": "Vľavo", "SSE.Views.SortDialog.textMoreCols": "(Viac stĺpcov...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON> r<PERSON>kov...)", "SSE.Views.SortDialog.textNone": "žiadny", "SSE.Views.SortDialog.textOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "Objednávka", "SSE.Views.SortDialog.textRight": "Vpravo", "SSE.Views.SortDialog.textRow": "Riadok", "SSE.Views.SortDialog.textSort": "Zoradiť na", "SSE.Views.SortDialog.textSortBy": "Zoradiť podľa", "SSE.Views.SortDialog.textThenBy": "Následne podľa ", "SSE.Views.SortDialog.textTop": "Hore", "SSE.Views.SortDialog.textUp": "Presunúť o stupeň hore", "SSE.Views.SortDialog.textValues": "Hodnoty", "SSE.Views.SortDialog.textZA": "Z po A", "SSE.Views.SortDialog.txtInvalidRange": "Neplatný rozsah buněk.", "SSE.Views.SortDialog.txtTitle": "Zoradiť", "SSE.Views.SortFilterDialog.textAsc": "Vzostupne (od A po Z) podľa", "SSE.Views.SortFilterDialog.textDesc": "Zostupne (od Z do A) podľa", "SSE.Views.SortFilterDialog.txtTitle": "Zoradiť", "SSE.Views.SortOptionsDialog.textCase": "Rozlišovať veľkosť písmen", "SSE.Views.SortOptionsDialog.textHeaders": "<PERSON><PERSON> d<PERSON>ta maj<PERSON> h<PERSON>", "SSE.Views.SortOptionsDialog.textLeftRight": "Zoradiť zľava doprava", "SSE.Views.SortOptionsDialog.textOrientation": "Orientácia", "SSE.Views.SortOptionsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> triedenia", "SSE.Views.SortOptionsDialog.textTopBottom": "Triediť zhora dole", "SSE.Views.SpecialPasteDialog.textAdd": "Pridať", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "Preskočiť prázdne", "SSE.Views.SpecialPasteDialog.textColWidth": "Š<PERSON><PERSON> stĺpcov", "SSE.Views.SpecialPasteDialog.textComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "Rozdeliť", "SSE.Views.SpecialPasteDialog.textFFormat": "Vzorce a formátovanie", "SSE.Views.SpecialPasteDialog.textFNFormat": "Vzorce a formáty čísla", "SSE.Views.SpecialPasteDialog.textFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFormulas": "Vzorce", "SSE.Views.SpecialPasteDialog.textFWidth": "Vzorce a šírky stĺpcov", "SSE.Views.SpecialPasteDialog.textMult": "Viacnásobný", "SSE.Views.SpecialPasteDialog.textNone": "žiadny", "SSE.Views.SpecialPasteDialog.textOperation": "Operácia", "SSE.Views.SpecialPasteDialog.textPaste": "Vložiť", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "Špeciálne prilepiť", "SSE.Views.SpecialPasteDialog.textTranspose": "Premiestňovať", "SSE.Views.SpecialPasteDialog.textValues": "Hodnoty", "SSE.Views.SpecialPasteDialog.textVFormat": "Hodnoty a formátovanie", "SSE.Views.SpecialPasteDialog.textVNFormat": "<PERSON><PERSON><PERSON> a <PERSON>el", "SSE.Views.SpecialPasteDialog.textWBorders": "Všetko okrem okrajov", "SSE.Views.Spellcheck.noSuggestions": "Žiadne odporúčania ohľadne pravopisu", "SSE.Views.Spellcheck.textChange": "Zmeniť", "SSE.Views.Spellcheck.textChangeAll": "Zmeniť všetko", "SSE.Views.Spellcheck.textIgnore": "Ignorovať", "SSE.Views.Spellcheck.textIgnoreAll": "Ignorovať všetko", "SSE.Views.Spellcheck.txtAddToDictionary": "Pridať do slovníka", "SSE.Views.Spellcheck.txtComplete": "Kontrola pravopisu dokončená", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Jazyk slovníka", "SSE.Views.Spellcheck.txtNextTip": "Ísť na ďalšie slovo", "SSE.Views.Spellcheck.txtSpelling": "Hláskovanie", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Kopírovať na koniec)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Presunúť na koniec)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Prilepiť pred list", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Presunúť pred list", "SSE.Views.Statusbar.filteredRecordsText": "{0} z {1} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemCopy": "Kopírovať", "SSE.Views.Statusbar.itemCount": "Počet", "SSE.Views.Statusbar.itemDelete": "Vymazať", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "Skryť", "SSE.Views.Statusbar.itemInsert": "Vložiť", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMove": "Premiestniť", "SSE.Views.Statusbar.itemProtect": "Ochrániť", "SSE.Views.Statusbar.itemRename": "Premenovať", "SSE.Views.Statusbar.itemStatus": "Status ukladania", "SSE.Views.Statusbar.itemSum": "SÚČET", "SSE.Views.Statusbar.itemTabColor": "Farba tabuľky/záložky", "SSE.Views.Statusbar.itemUnProtect": "Zrušiť zabezpečenie", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Pracovný list s takýmto názvom už existuje.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Názov hárku nemôže obsahovať nasledujúce znaky: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Názov listu", "SSE.Views.Statusbar.selectAllSheets": "Vybrať všetky listy", "SSE.Views.Statusbar.sheetIndexText": "List {0} z {1}", "SSE.Views.Statusbar.textAverage": "Priemerne", "SSE.Views.Statusbar.textCount": "Počítať", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Pridať novú vlastnú farbu", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Pridať pracovný hárok", "SSE.Views.Statusbar.tipFirst": "Prejsť na prvý list", "SSE.Views.Statusbar.tipLast": "Prejsť na posledný list", "SSE.Views.Statusbar.tipListOfSheets": "<PERSON><PERSON><PERSON> listov", "SSE.Views.Statusbar.tipNext": "Posunúť zoznam listov vpravo", "SSE.Views.Statusbar.tipPrev": "Posunúť zoznam listov vľavo", "SSE.Views.Statusbar.tipZoomFactor": "Priblíženie", "SSE.Views.Statusbar.tipZoomIn": "Priblížiť", "SSE.Views.Statusbar.tipZoomOut": "Oddialiť", "SSE.Views.Statusbar.ungroupSheets": "Zrušiť zoskupenie hárkov", "SSE.Views.Statusbar.zoomText": "Priblíženie {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operáciu nemožno vykonať pre vybraný rozsah buniek.<br><PERSON><PERSON><PERSON><PERSON> j<PERSON> d<PERSON> roz<PERSON>, in<PERSON> a<PERSON>, a skúste to znova.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operácia sa nedala dokončiť pre zvolený rozsah buniek.<br><PERSON><PERSON><PERSON><PERSON> roz<PERSON> tak, aby prvý riadok tabuľky bol na rovnakom riadku<br>a výsledná tabuľka sa prekrývala s aktuálnou.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operácia sa nedala dokončiť pre zvolený rozsah buniek.<br><PERSON><PERSON><PERSON><PERSON> rozsah, k<PERSON><PERSON> nezahŕňa iné tabuľky.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "V tabuľkách nie sú dovolené vzorce pre pole s via<PERSON><PERSON><PERSON> bunkami", "SSE.Views.TableOptionsDialog.txtEmpty": "Toto pole sa vyžaduje", "SSE.Views.TableOptionsDialog.txtFormat": "Vytvoriť tabuľku", "SSE.Views.TableOptionsDialog.txtInvalidRange": "CHYBA! Neplatný rozsah buniek", "SSE.Views.TableOptionsDialog.txtNote": "Hlavičky musia zostať v rovnakom riadku a výsledný rozsah tabuľky sa musí prekrývať s pôvodným rozsahom tabuľky.", "SSE.Views.TableOptionsDialog.txtTitle": "N<PERSON>zov", "SSE.Views.TableSettings.deleteColumnText": "Odstrániť stĺpec", "SSE.Views.TableSettings.deleteRowText": "Odstrániť riadok", "SSE.Views.TableSettings.deleteTableText": "Odstrániť tabuľku", "SSE.Views.TableSettings.insertColumnLeftText": "Vložiť stĺpec vľavo", "SSE.Views.TableSettings.insertColumnRightText": "Vložiť stĺpec vpravo", "SSE.Views.TableSettings.insertRowAboveText": "Vložiť riadok nad", "SSE.Views.TableSettings.insertRowBelowText": "Vložiť riadok pod", "SSE.Views.TableSettings.notcriticalErrorTitle": "Upozornenie", "SSE.Views.TableSettings.selectColumnText": "Vybrať celý stĺpec", "SSE.Views.TableSettings.selectDataText": "Vybrať údaje stĺpca", "SSE.Views.TableSettings.selectRowText": "Vybrať riadok", "SSE.Views.TableSettings.selectTableText": "Vybrať tabuľku", "SSE.Views.TableSettings.textActions": "Akcie s tabuľkou", "SSE.Views.TableSettings.textAdvanced": "Zobraziť pokročilé nastavenia", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>/pásikovaný", "SSE.Views.TableSettings.textColumns": "Stĺpce", "SSE.Views.TableSettings.textConvertRange": "Premeniť na rozsah", "SSE.Views.TableSettings.textEdit": "Riadky a stĺpce", "SSE.Views.TableSettings.textEmptyTemplate": "Žiadne <PERSON>ón<PERSON>", "SSE.Views.TableSettings.textExistName": "CHYBA! Rozsah s takýmto názvom už existuje", "SSE.Views.TableSettings.textFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON> filtru", "SSE.Views.TableSettings.textFirst": "prvý", "SSE.Views.TableSettings.textHeader": "Hlavička", "SSE.Views.TableSettings.textInvalidName": "CHYBA! Neplatný názov tabuľky", "SSE.Views.TableSettings.textIsLocked": "Tento prvok upravuje iný používateľ.", "SSE.Views.TableSettings.textLast": "Posledný", "SSE.Views.TableSettings.textLongOperation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textPivot": "Vložiť kontingenťnú tabuľku", "SSE.Views.TableSettings.textRemDuplicates": "Odobrať duplicity", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, ktorý sa pokúšate použiť, je už uvedený v bunkových vzorcoch. Použite iné meno.", "SSE.Views.TableSettings.textResize": "Zmeniť veľkosť tabuľky", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Vybrať údaje", "SSE.Views.TableSettings.textSlicer": "Vložiť prierez", "SSE.Views.TableSettings.textTableName": "Názov tabuľky", "SSE.Views.TableSettings.textTemplate": "Vybrať zo šablóny", "SSE.Views.TableSettings.textTotal": "Celkovo", "SSE.Views.TableSettings.warnLongOperation": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete vykonať, môže trvať pomerne dlhý čas na dokončenie.<br>Určite chcete pokračovať?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternatívny text", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Alternatívne textové zobrazenie informácií o vizuálnych objektoch, ktoré sa prečítajú ľuďom s poruchou videnia alebo kognitívnymi poruchami, aby sa im pomohlo lepšie porozumieť, ak<PERSON> informá<PERSON> sú na obrázku, automatickom tvarovaní, grafe alebo tabuľke. ", "SSE.Views.TableSettingsAdvanced.textAltTitle": "N<PERSON>zov", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabuľka - Pokročilé nastavenia", "SSE.Views.TextArtSettings.strBackground": "Farba pozadia", "SSE.Views.TextArtSettings.strColor": "Farba", "SSE.Views.TextArtSettings.strFill": "Vyplniť", "SSE.Views.TextArtSettings.strForeground": "Farba popredia", "SSE.Views.TextArtSettings.strPattern": "Vzor", "SSE.Views.TextArtSettings.strSize": "Veľkosť", "SSE.Views.TextArtSettings.strStroke": "Čiara/líniový graf", "SSE.Views.TextArtSettings.strTransparency": "Priehľ<PERSON>nosť", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "Uhol", "SSE.Views.TextArtSettings.textBorderSizeErr": "Zadaná hodnota je nesprávna.<br><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> hodnotu medzi 0 pt a 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Vyplniť farbou", "SSE.Views.TextArtSettings.textDirection": "Smer", "SSE.Views.TextArtSettings.textEmptyPattern": "Bez vzoru", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromUrl": "Z URL adresy ", "SSE.Views.TextArtSettings.textGradient": "Body prechodu", "SSE.Views.TextArtSettings.textGradientFill": "Výplň prechodom", "SSE.Views.TextArtSettings.textImageTexture": "Obrázok alebo textúra", "SSE.Views.TextArtSettings.textLinear": "Lineárny/čiarový", "SSE.Views.TextArtSettings.textNoFill": "Bez výplne", "SSE.Views.TextArtSettings.textPatternFill": "Vzor", "SSE.Views.TextArtSettings.textPosition": "Pozícia", "SSE.Views.TextArtSettings.textRadial": "Kruhový/hviezdicovitý", "SSE.Views.TextArtSettings.textSelectTexture": "Vybrať", "SSE.Views.TextArtSettings.textStretch": "Roztiahnuť", "SSE.Views.TextArtSettings.textStyle": "Š<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Šablóna", "SSE.Views.TextArtSettings.textTexture": "Z textúry", "SSE.Views.TextArtSettings.textTile": "Dlaždica", "SSE.Views.TextArtSettings.textTransform": "Transformovať", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Pridať spádový bod", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Odstrániť prechodový bod", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>/baliaci papier", "SSE.Views.TextArtSettings.txtCanvas": "Pl<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "Ka<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Tmavá štruktúra", "SSE.Views.TextArtSettings.txtGrain": "Textúra/zrnitosť", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> pap<PERSON>", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Koža/kožený", "SSE.Views.TextArtSettings.txtNoBorders": "Bez čiary", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "Drevo", "SSE.Views.Toolbar.capBtnAddComment": "Pridať komentár", "SSE.Views.Toolbar.capBtnColorSchemas": "Farebná schéma", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Hlavička & Päta", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientácia", "SSE.Views.Toolbar.capBtnPageSize": "Veľkosť", "SSE.Views.Toolbar.capBtnPrintArea": "Oblasť tlače", "SSE.Views.Toolbar.capBtnPrintTitles": "Tlač názvu", "SSE.Views.Toolbar.capBtnScale": "Škáluj do rozmeru", "SSE.Views.Toolbar.capImgAlign": "Zarovnať", "SSE.Views.Toolbar.capImgBackward": "Posunúť späť", "SSE.Views.Toolbar.capImgForward": "Posunúť vpred", "SSE.Views.Toolbar.capImgGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChart": "<PERSON>", "SSE.Views.Toolbar.capInsertEquation": "Rovnica", "SSE.Views.Toolbar.capInsertHyperlink": "Hypertextový odkaz", "SSE.Views.Toolbar.capInsertImage": "Obrázok", "SSE.Views.Toolbar.capInsertShape": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertSpark": "Mikro-graf", "SSE.Views.Toolbar.capInsertTable": "Tabuľka", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON> pole", "SSE.Views.Toolbar.mniImageFromFile": "Obrázok zo súboru", "SSE.Views.Toolbar.mniImageFromStorage": "Obrázok z úložiska", "SSE.Views.Toolbar.mniImageFromUrl": "Obrázok z URL adresy", "SSE.Views.Toolbar.textAddPrintArea": "Pridať k oblasti tlačenia", "SSE.Views.Toolbar.textAlignBottom": "Zarovnať dole", "SSE.Views.Toolbar.textAlignCenter": "Centrovať", "SSE.Views.Toolbar.textAlignJust": "Podľ<PERSON>ov", "SSE.Views.Toolbar.textAlignLeft": "Zarovnať doľava", "SSE.Views.Toolbar.textAlignMiddle": "Zarovnať na stred", "SSE.Views.Toolbar.textAlignRight": "Zarovnať doprava", "SSE.Views.Toolbar.textAlignTop": "Zarovnať nahor", "SSE.Views.Toolbar.textAllBorders": "Všetky orámovania", "SSE.Views.Toolbar.textAuto": "<PERSON>ky", "SSE.Views.Toolbar.textAutoColor": "<PERSON>ky", "SSE.Views.Toolbar.textBold": "Tučn<PERSON>", "SSE.Views.Toolbar.textBordersColor": "Farba orámovania", "SSE.Views.Toolbar.textBordersStyle": "Štýl orámovania", "SSE.Views.Toolbar.textBottom": "<PERSON><PERSON>:", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCenterBorders": "Vnútorné vertikálne orámovanie", "SSE.Views.Toolbar.textClearPrintArea": "Vyčistiť oblasť tlačenia", "SSE.Views.Toolbar.textClearRule": "Zmazať pravidlá", "SSE.Views.Toolbar.textClockwise": "Otočiť v smere hodinových ručičiek", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCounterCw": "Otočiť proti smeru hodinových ručičiek", "SSE.Views.Toolbar.textDataBars": "Histogramy", "SSE.Views.Toolbar.textDelLeft": "Posunúť bunky vľavo", "SSE.Views.Toolbar.textDelUp": "Posunúť bunky hore", "SSE.Views.Toolbar.textDiagDownBorder": "Orámovanie diagonálne nadol", "SSE.Views.Toolbar.textDiagUpBorder": "Orámovanie diagonálne nahor", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON> stĺpec", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.Toolbar.textFewPages": "Strany", "SSE.Views.Toolbar.textHeight": "Výška", "SSE.Views.Toolbar.textHorizontal": "Horizontálny Text", "SSE.Views.Toolbar.textInsDown": "Posunú<PERSON> bunky dole", "SSE.Views.Toolbar.textInsideBorders": "Vnútorné <PERSON>", "SSE.Views.Toolbar.textInsRight": "Posunúť bunky vpravo", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Polož<PERSON>", "SSE.Views.Toolbar.textLandscape": "Na šírku", "SSE.Views.Toolbar.textLeft": "Vľavo:", "SSE.Views.Toolbar.textLeftBorders": "Orámovanie vľavo", "SSE.Views.Toolbar.textManageRule": "Spravovať pravidlá", "SSE.Views.Toolbar.textManyPages": "Strany", "SSE.Views.Toolbar.textMarginsLast": "Posledná úprava", "SSE.Views.Toolbar.textMarginsNarrow": "Úzky", "SSE.Views.Toolbar.textMarginsNormal": "Normálny", "SSE.Views.Toolbar.textMarginsWide": "Široký", "SSE.Views.Toolbar.textMiddleBorders": "Vnútorné <PERSON>e orámovanie", "SSE.Views.Toolbar.textMoreFormats": "Ďalšie formáty", "SSE.Views.Toolbar.textMorePages": "Ďalšie str<PERSON>ky", "SSE.Views.Toolbar.textNewColor": "Pridať novú vlastnú farbu", "SSE.Views.Toolbar.textNewRule": "Nové pravidlo", "SSE.Views.Toolbar.textNoBorders": "Bez orámovania", "SSE.Views.Toolbar.textOnePage": "Strán<PERSON>", "SSE.Views.Toolbar.textOutBorders": "Vonkajšie orámovanie", "SSE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPortrait": "Na výšku", "SSE.Views.Toolbar.textPrint": "Tlačiť", "SSE.Views.Toolbar.textPrintGridlines": "Vytlačiť mriežky", "SSE.Views.Toolbar.textPrintHeadings": "Vytlačiť hlavičku", "SSE.Views.Toolbar.textPrintOptions": "Nastavenia tlače", "SSE.Views.Toolbar.textRight": "Vpravo:", "SSE.Views.Toolbar.textRightBorders": "Orámovanie vpravo", "SSE.Views.Toolbar.textRotateDown": "Otočiť text nadol", "SSE.Views.Toolbar.textRotateUp": "Otočiť text nahor", "SSE.Views.Toolbar.textScale": "Šk<PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Vlastný", "SSE.Views.Toolbar.textSelection": "Z aktuálneho výberu", "SSE.Views.Toolbar.textSetPrintArea": "Nastaviť oblasť tlače", "SSE.Views.Toolbar.textStrikeout": "Preškrtnutie", "SSE.Views.Toolbar.textSubscript": "Dolný index", "SSE.Views.Toolbar.textSubSuperscript": "Dolný/Horný index", "SSE.Views.Toolbar.textSuperscript": "Horný index", "SSE.Views.Toolbar.textTabCollaboration": "Spolupráca", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "S<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Vzorec", "SSE.Views.Toolbar.textTabHome": "Hlavná stránka", "SSE.Views.Toolbar.textTabInsert": "Vložiť", "SSE.Views.Toolbar.textTabLayout": "Rozloženie", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "Zobraziť", "SSE.Views.Toolbar.textThisPivot": "Z tejto kontingenčnej tabuľky", "SSE.Views.Toolbar.textThisSheet": "Z tohto listu", "SSE.Views.Toolbar.textThisTable": "Z tejto tabuľky", "SSE.Views.Toolbar.textTop": "Hore:", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textUnderline": "Podčiarknuť", "SSE.Views.Toolbar.textVertical": "Vertikálny text", "SSE.Views.Toolbar.textWidth": "Šírka", "SSE.Views.Toolbar.textZoom": "Priblíženie", "SSE.Views.Toolbar.tipAlignBottom": "Zarovnať dole", "SSE.Views.Toolbar.tipAlignCenter": "Centrovať", "SSE.Views.Toolbar.tipAlignJust": "Podľ<PERSON>ov", "SSE.Views.Toolbar.tipAlignLeft": "Zarovnať doľava", "SSE.Views.Toolbar.tipAlignMiddle": "Zarovnať na stred", "SSE.Views.Toolbar.tipAlignRight": "Zarovnať doprava", "SSE.Views.Toolbar.tipAlignTop": "Zarovnať nahor", "SSE.Views.Toolbar.tipAutofilter": "Zoradiť a filtrovať", "SSE.Views.Toolbar.tipBack": "Späť", "SSE.Views.Toolbar.tipBorders": "Orámovania", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON><PERSON> bunky", "SSE.Views.Toolbar.tipChangeChart": "Zmeniť typ grafu", "SSE.Views.Toolbar.tipClearStyle": "Vyčistiť", "SSE.Views.Toolbar.tipColorSchemas": "Zmeniť farebnú schému", "SSE.Views.Toolbar.tipCondFormat": "Podmienen<PERSON>", "SSE.Views.Toolbar.tipCopy": "Kopírovať", "SSE.Views.Toolbar.tipCopyStyle": "Kopírovať štýl", "SSE.Views.Toolbar.tipDecDecimal": "Znížiť počet desatinných miest", "SSE.Views.Toolbar.tipDecFont": "Zmenšiť veľkosť písma", "SSE.Views.Toolbar.tipDeleteOpt": "Odstrániť bunky", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON>/štýl meny", "SSE.Views.Toolbar.tipDigStylePercent": "Štýl percent", "SSE.Views.Toolbar.tipEditChart": "Upraviť graf", "SSE.Views.Toolbar.tipEditChartData": "Vybrať údaje", "SSE.Views.Toolbar.tipEditChartType": "Zmeniť typ grafu", "SSE.Views.Toolbar.tipEditHeader": "Upraviť hlavičku alebo pätu", "SSE.Views.Toolbar.tipFontColor": "Farba písma", "SSE.Views.Toolbar.tipFontName": "Písmo", "SSE.Views.Toolbar.tipFontSize": "Veľkosť písma", "SSE.Views.Toolbar.tipImgAlign": "Zoradiť/usporiadať objekty", "SSE.Views.Toolbar.tipImgGroup": "Skupinové objekty", "SSE.Views.Toolbar.tipIncDecimal": "Zvýšiť počet desatinných miest", "SSE.Views.Toolbar.tipIncFont": "Zväčšiť veľkosť písma", "SSE.Views.Toolbar.tipInsertChart": "Vložiť graf", "SSE.Views.Toolbar.tipInsertChartSpark": "Vložiť graf", "SSE.Views.Toolbar.tipInsertEquation": "Vložiť rovnicu", "SSE.Views.Toolbar.tipInsertHyperlink": "Pridať odkaz", "SSE.Views.Toolbar.tipInsertImage": "Vložiť obrázok", "SSE.Views.Toolbar.tipInsertOpt": "Vložiť bunky", "SSE.Views.Toolbar.tipInsertShape": "Vložiť automatický tvar", "SSE.Views.Toolbar.tipInsertSlicer": "Vložiť prierez", "SSE.Views.Toolbar.tipInsertSpark": "Vložiť mikro-graf", "SSE.Views.Toolbar.tipInsertSymbol": "Vložiť symbol", "SSE.Views.Toolbar.tipInsertTable": "Vložiť tabuľku", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON><PERSON> textové pole", "SSE.Views.Toolbar.tipInsertTextart": "Vložiť Text Art", "SSE.Views.Toolbar.tipMerge": "Zlúčiť a vycentrovať", "SSE.Views.Toolbar.tipNone": "Žiadny", "SSE.Views.Toolbar.tipNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageSize": "Veľkosť stránky", "SSE.Views.Toolbar.tipPaste": "Vložiť", "SSE.Views.Toolbar.tipPrColor": "Farba výplne", "SSE.Views.Toolbar.tipPrint": "Tlačiť", "SSE.Views.Toolbar.tipPrintArea": "Oblasť tlače", "SSE.Views.Toolbar.tipPrintTitles": "Tlač názvu", "SSE.Views.Toolbar.tipRedo": "Krok vpred", "SSE.Views.Toolbar.tipSave": "Uložiť", "SSE.Views.Toolbar.tipSaveCoauth": "U<PERSON>žte zmeny, aby ich videli aj ostatní používatelia.", "SSE.Views.Toolbar.tipScale": "Škáluj do rozmeru", "SSE.Views.Toolbar.tipSendBackward": "Posunúť späť", "SSE.Views.Toolbar.tipSendForward": "Posunúť vpred", "SSE.Views.Toolbar.tipSynchronize": "Dokument bol zmenený ďalším používateľom. Prosím, kliknite na uloženie zmien a opätovne načítajte aktualizácie.", "SSE.Views.Toolbar.tipTextOrientation": "Orientácia", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "Obtekanie textu", "SSE.Views.Toolbar.txtAccounting": "Účtovníctvo", "SSE.Views.Toolbar.txtAdditional": "Ďalšie", "SSE.Views.Toolbar.txtAscending": "Vzostupne", "SSE.Views.Toolbar.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Vyčistiť filter", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "Funkcia", "SSE.Views.Toolbar.txtClearHyper": "Hypertextové odkazy", "SSE.Views.Toolbar.txtClearText": "Text", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Vlastný", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDateTime": "Dátum a čas", "SSE.Views.Toolbar.txtDescending": "Zostupne", "SSE.Views.Toolbar.txtDollar": "$ Dolár", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponenciálny", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Vložiť funkciu", "SSE.Views.Toolbar.txtFraction": "Zlomok", "SSE.Views.Toolbar.txtFranc": "CHF švajčiarsky frank", "SSE.Views.Toolbar.txtGeneral": "Všeobecné", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Správca názvov", "SSE.Views.Toolbar.txtMergeAcross": "Zlúčiť naprieč", "SSE.Views.Toolbar.txtMergeCells": "Zlúčiť bunky", "SSE.Views.Toolbar.txtMergeCenter": "Zlúčiť a centrovať", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON><PERSON> meno", "SSE.Views.Toolbar.txtNoBorders": "Bez orámovania", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Vložiť názov", "SSE.Views.Toolbar.txtPercentage": "Percentuálny podiel", "SSE.Views.Toolbar.txtPound": "£ Libra", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "Medián", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme14": "Výklenok", "SSE.Views.Toolbar.txtScheme15": "Pôvod", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Slnovrat", "SSE.Views.Toolbar.txtScheme18": "Technika", "SSE.Views.Toolbar.txtScheme19": "Cestovanie", "SSE.Views.Toolbar.txtScheme2": "Odtiene sivej", "SSE.Views.Toolbar.txtScheme20": "Mestský", "SSE.Views.Toolbar.txtScheme21": "Elán", "SSE.Views.Toolbar.txtScheme22": "Nov<PERSON> kancelária", "SSE.Views.Toolbar.txtScheme3": "Vrchol", "SSE.Views.Toolbar.txtScheme4": "Aspekt", "SSE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme6": "Dav", "SSE.Views.Toolbar.txtScheme7": "Spravodlivosť", "SSE.Views.Toolbar.txtScheme8": "Prietok", "SSE.Views.Toolbar.txtScheme9": "Zlieváreň", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "Hľadať", "SSE.Views.Toolbar.txtSort": "Zoradiť", "SSE.Views.Toolbar.txtSortAZ": "Zoradiť vzostupne", "SSE.Views.Toolbar.txtSortZA": "Zoradiť zostupne", "SSE.Views.Toolbar.txtSpecial": "Špeciálny", "SSE.Views.Toolbar.txtTableTemplate": "Formátovať ako šablónu tabuľky", "SSE.Views.Toolbar.txtText": "Text", "SSE.Views.Toolbar.txtTime": "Čas", "SSE.Views.Toolbar.txtUnmerge": "Zrušiť zlúčenie buniek", "SSE.Views.Toolbar.txtYen": "¥ Jen", "SSE.Views.Top10FilterDialog.textType": "Zobraziť", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "od", "SSE.Views.Top10FilterDialog.txtItems": "Položka", "SSE.Views.Top10FilterDialog.txtPercent": "Percento", "SSE.Views.Top10FilterDialog.txtSum": "SÚČET", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automatického filtra", "SSE.Views.Top10FilterDialog.txtTop": "Hore", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtre \"Top 10\"", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Nastavenia poľa hodnô<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Základná položka", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 z %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Počet", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Spočítať čísla", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Vlastný názov", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Rozdielne oproti", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Bez výpočtu", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Percento z", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Percentá rozdielna od", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Percento stĺpca", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Percento z celku", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Percento riadku", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Priebežný súčet v", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Zobraziť hodnoty ako", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Názov zdroja:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Smerodajná odchylka", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Populačná smerodajná odchýlka", "SSE.Views.ValueFieldSettingsDialog.txtSum": "SÚČET", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Zhrnúť pole hodnoty podľa", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Zatvoriť", "SSE.Views.ViewManagerDlg.guestText": "Hosť", "SSE.Views.ViewManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Odstrániť", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "Žiadne zobrazenia neboli zatiaľ vytvorené.", "SSE.Views.ViewManagerDlg.textGoTo": "Prejsť na zobrazenie", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON><PERSON> meno, k<PERSON><PERSON><PERSON> dĺžka je menej ako 128 znakov.", "SSE.Views.ViewManagerDlg.textNew": "Nový", "SSE.Views.ViewManagerDlg.textRename": "Premenovať", "SSE.Views.ViewManagerDlg.textRenameError": "Názov zobrazenia nesmie byť prázdny.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Premenovať pohľad", "SSE.Views.ViewManagerDlg.textViews": "Zobrazenie zošitu", "SSE.Views.ViewManagerDlg.tipIsLocked": "Tento prvok upravuje iný používateľ.", "SSE.Views.ViewManagerDlg.txtTitle": "Správca zobrazenia zošitu", "SSE.Views.ViewManagerDlg.warnDeleteView": "Pokúšate sa odstrániť aktuálne povolené zobrazenie '%1'.<br>Zavrieť toto zobrazenie a odstrániť ho?", "SSE.Views.ViewTab.capBtnFreeze": "Ukotviť priečky", "SSE.Views.ViewTab.capBtnSheetView": "Zobrazenie zošitu ", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Vždy zobrazovať panel nástrojov", "SSE.Views.ViewTab.textClose": "Zatvoriť", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Skombinovať list a stavový riadok", "SSE.Views.ViewTab.textCreate": "Nový", "SSE.Views.ViewTab.textDefault": "Štandardné", "SSE.Views.ViewTab.textFormula": "Lišta vzorca", "SSE.Views.ViewTab.textFreezeCol": "Ukotviť prvý stĺpec", "SSE.Views.ViewTab.textFreezeRow": "Ukotviť horný riadok", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "Nadpisy", "SSE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ViewTab.textManager": "Správca zobrazení", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Zobraziť tieň ukotvených priečok", "SSE.Views.ViewTab.textUnFreeze": "Zrušiť ukotvenie priečky", "SSE.Views.ViewTab.textZeros": "Zobraziť nuly", "SSE.Views.ViewTab.textZoom": "Priblížiť", "SSE.Views.ViewTab.tipClose": "Zatvoriť zobrazenie hárka", "SSE.Views.ViewTab.tipCreate": "Vytvoriť nové zobrazenie zošitu", "SSE.Views.ViewTab.tipFreeze": "Ukotviť priečky", "SSE.Views.ViewTab.tipSheetView": "Zobrazenie zošitu ", "SSE.Views.WBProtection.hintAllowRanges": "Umožniť editovať rozsahy", "SSE.Views.WBProtection.hintProtectSheet": "Zabezpečiť list", "SSE.Views.WBProtection.hintProtectWB": "Zabezpečiť zošit", "SSE.Views.WBProtection.txtAllowRanges": "Umožniť editovať rozsahy", "SSE.Views.WBProtection.txtHiddenFormula": "Skryté vzorce", "SSE.Views.WBProtection.txtLockedCell": "Uzamknutá bunka", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedText": "Uzamknutý text", "SSE.Views.WBProtection.txtProtectSheet": "Zabezpečiť list", "SSE.Views.WBProtection.txtProtectWB": "Zabezpečiť zošit", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Zadajte he<PERSON>lo pre deaktiváciu zabezpečenia listu", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Zrušte ochranu listu", "SSE.Views.WBProtection.txtWBUnlockDescription": "Vložte heslo pre vstup k zošitu", "SSE.Views.WBProtection.txtWBUnlockTitle": "Zrušte ochranu zošita"}