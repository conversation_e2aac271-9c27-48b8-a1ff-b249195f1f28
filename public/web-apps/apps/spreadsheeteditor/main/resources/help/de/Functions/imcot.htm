﻿<!DOCTYPE html>
<html>
	<head>
		<title>IMCOT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>IMCOT-Funktion</h1>
			<p>Die <b>IMCOT</b>-Funktion gehört zur Gruppe der technischen Funktionen. Mit dieser Funktion lässt sich die Kotangens einer komplexen Zahl zurückgeben.</p>
			<p>Die Formelsyntax der <b>IMCOT</b>-Funktion lautet:</p> 
			<p style="text-indent: 150px;"><b><em>IMCOT(Zahl)</em></b></p> 
			<p>Dabei ist <b><em>Zahl</em></b> eine komplexe Zahl, die manuell im Format x + xi oder x + yj eingegeben wird oder in der Zelle beinhaltet ist, auf die Sie Bezug nehmen.</p>
			<p><b>IMCOT</b>-Funktion anwenden:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Technische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf <b>IMCOT</b>-Funktion.</li>
			<li>Geben Sie das gewünschte Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="IMCOT-Funktion" src="../images/imcot.png" /></p>
		</div>
	</body>
</html>