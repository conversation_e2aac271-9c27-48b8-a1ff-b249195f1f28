{"Common.Controllers.Chat.notcriticalErrorTitle": "Предупреждение", "Common.Controllers.Chat.textEnterMessage": "Введите здесь своё сообщение", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Аноним", "Common.Controllers.ExternalDiagramEditor.textClose": "Закрыть", "Common.Controllers.ExternalDiagramEditor.warningText": "Объект недоступен, так как редактируется другим пользователем.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Предупреждение", "Common.Controllers.ExternalOleEditor.textAnonymous": "Аноним", "Common.Controllers.ExternalOleEditor.textClose": "Закрыть", "Common.Controllers.ExternalOleEditor.warningText": "Объект недоступен, так как редактируется другим пользователем.", "Common.Controllers.ExternalOleEditor.warningTitle": "Внимание", "Common.define.chartData.textArea": "С областями", "Common.define.chartData.textAreaStacked": "Диаграмма с областями с накоплением", "Common.define.chartData.textAreaStackedPer": "Нормированная с областями и накоплением", "Common.define.chartData.textBar": "Лин<PERSON>йчатая", "Common.define.chartData.textBarNormal": "Гистограмма с группировкой", "Common.define.chartData.textBarNormal3d": "Трехмерная гистограмма с группировкой", "Common.define.chartData.textBarNormal3dPerspective": "Трехмерная гистограмма", "Common.define.chartData.textBarStacked": "Гистограмма с накоплением", "Common.define.chartData.textBarStacked3d": "Трехмерная гистограмма с накоплением", "Common.define.chartData.textBarStackedPer": "Нормированная гистограмма с накоплением", "Common.define.chartData.textBarStackedPer3d": "Трехмерная нормированная гистограмма с накоплением", "Common.define.chartData.textCharts": "Диаграммы", "Common.define.chartData.textColumn": "Гистограмма", "Common.define.chartData.textCombo": "Комбинированные", "Common.define.chartData.textComboAreaBar": "С областями с накоплением и гистограмма с группировкой", "Common.define.chartData.textComboBarLine": "Гистограмма с группировкой и график", "Common.define.chartData.textComboBarLineSecondary": "Гистограмма с группировкой и график на вспомогательной оси", "Common.define.chartData.textComboCustom": "Пользовательская комбинация", "Common.define.chartData.textDoughnut": "Кольцевая диаграмма", "Common.define.chartData.textHBarNormal": "Линейчатая с группировкой", "Common.define.chartData.textHBarNormal3d": "Трехмерная линейчатая с группировкой", "Common.define.chartData.textHBarStacked": "Линейчатая с накоплением", "Common.define.chartData.textHBarStacked3d": "Трехмерная линейчатая с накоплением", "Common.define.chartData.textHBarStackedPer": "Нормированная линейчатая с накоплением", "Common.define.chartData.textHBarStackedPer3d": "Трехмерная нормированная линейчатая с накоплением", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "Трехмерный график", "Common.define.chartData.textLineMarker": "График с маркерами", "Common.define.chartData.textLineStacked": "График с накоплением", "Common.define.chartData.textLineStackedMarker": "График с накоплением с маркерами", "Common.define.chartData.textLineStackedPer": "Нормированный график с накоплением", "Common.define.chartData.textLineStackedPerMarker": "Нормированный график с маркерами и накоплением", "Common.define.chartData.textPie": "Круговая", "Common.define.chartData.textPie3d": "Трехмерная круговая диаграмма", "Common.define.chartData.textPoint": "Точечная", "Common.define.chartData.textScatter": "Точечная диаграмма", "Common.define.chartData.textScatterLine": "Точечная с прямыми отрезками", "Common.define.chartData.textScatterLineMarker": "Точечная с прямыми отрезками и маркерами", "Common.define.chartData.textScatterSmooth": "Точечная с гладкими кривыми", "Common.define.chartData.textScatterSmoothMarker": "Точечная с гладкими кривыми и маркерами", "Common.define.chartData.textStock": "Биржевая", "Common.define.chartData.textSurface": "Поверхность", "Common.define.effectData.textAcross": "По горизонтали", "Common.define.effectData.textAppear": "Возникновение", "Common.define.effectData.textArcDown": "Вниз по дуге", "Common.define.effectData.textArcLeft": "Влево по дуге", "Common.define.effectData.textArcRight": "Вправо по дуге", "Common.define.effectData.textArcs": "Дуги", "Common.define.effectData.textArcUp": "Вверх по дуге", "Common.define.effectData.textBasic": "Базовые", "Common.define.effectData.textBasicSwivel": "Простое вращение", "Common.define.effectData.textBasicZoom": "Простое увеличение", "Common.define.effectData.textBean": "<PERSON>об", "Common.define.effectData.textBlinds": "Жалюзи", "Common.define.effectData.textBlink": "Мигание", "Common.define.effectData.textBoldFlash": "Полужирное начертание", "Common.define.effectData.textBoldReveal": "Наложение полужирного", "Common.define.effectData.textBoomerang": "<PERSON>у<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBounce": "Выскакивание", "Common.define.effectData.textBounceLeft": "Выскакивание влево", "Common.define.effectData.textBounceRight": "Выскакивание вправо", "Common.define.effectData.textBox": "Прямоугольник", "Common.define.effectData.textBrushColor": "Перекрашивание", "Common.define.effectData.textCenterRevolve": "Поворот вокруг центра", "Common.define.effectData.textCheckerboard": "Шахматная доска", "Common.define.effectData.textCircle": "Круг", "Common.define.effectData.textCollapse": "Свертывание", "Common.define.effectData.textColorPulse": "Цветовая пульсация", "Common.define.effectData.textComplementaryColor": "Дополнительный цвет", "Common.define.effectData.textComplementaryColor2": "Дополнительный цвет 2", "Common.define.effectData.textCompress": "Сжатие", "Common.define.effectData.textContrast": "Контраст", "Common.define.effectData.textContrastingColor": "Контрастирующий цвет", "Common.define.effectData.textCredits": "Титры", "Common.define.effectData.textCrescentMoon": "Полумесяц", "Common.define.effectData.textCurveDown": "Скачок вниз", "Common.define.effectData.textCurvedSquare": "Скругленный квадрат", "Common.define.effectData.textCurvedX": "Скругленный крестик", "Common.define.effectData.textCurvyLeft": "Влево по кривой", "Common.define.effectData.textCurvyRight": "Вправо по кривой", "Common.define.effectData.textCurvyStar": "Скругленная звезда", "Common.define.effectData.textCustomPath": "Пользовательский путь", "Common.define.effectData.textCuverUp": "Скачок вверх", "Common.define.effectData.textDarken": "Затемнение", "Common.define.effectData.textDecayingWave": "Затухающая волна", "Common.define.effectData.textDesaturate": "Обесцвечивание", "Common.define.effectData.textDiagonalDownRight": "По диагонали в правый нижний угол", "Common.define.effectData.textDiagonalUpRight": "По диагонали в верхний правый угол", "Common.define.effectData.textDiamond": "Ромб", "Common.define.effectData.textDisappear": "Исчезновение", "Common.define.effectData.textDissolveIn": "Растворение", "Common.define.effectData.textDissolveOut": "Растворение", "Common.define.effectData.textDown": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDrop": "Падение", "Common.define.effectData.textEmphasis": "Эффекты выделения", "Common.define.effectData.textEntrance": "Эффекты входа", "Common.define.effectData.textEqualTriangle": "Равносторонний треугольник", "Common.define.effectData.textExciting": "Сложные", "Common.define.effectData.textExit": "Эффекты выхода", "Common.define.effectData.textExpand": "Развертывание", "Common.define.effectData.textFade": "Выцветание", "Common.define.effectData.textFigureFour": "Удвоенный знак 8", "Common.define.effectData.textFillColor": "Цвет заливки", "Common.define.effectData.textFlip": "Переворот", "Common.define.effectData.textFloat": "Плавное приближение", "Common.define.effectData.textFloatDown": "Плавное перемещение вниз", "Common.define.effectData.textFloatIn": "Плавное приближение", "Common.define.effectData.textFloatOut": "Плавное удаление", "Common.define.effectData.textFloatUp": "Плавное перемещение вверх", "Common.define.effectData.textFlyIn": "Вылет", "Common.define.effectData.textFlyOut": "Вылет за край листа", "Common.define.effectData.textFontColor": "Цвет шрифта", "Common.define.effectData.textFootball": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFromBottom": "Снизу вверх", "Common.define.effectData.textFromBottomLeft": "Снизу слева", "Common.define.effectData.textFromBottomRight": "Снизу справа", "Common.define.effectData.textFromLeft": "Слева направо", "Common.define.effectData.textFromRight": "Справа налево", "Common.define.effectData.textFromTop": "Сверху вниз", "Common.define.effectData.textFromTopLeft": "Сверху слева", "Common.define.effectData.textFromTopRight": "Сверху справа", "Common.define.effectData.textFunnel": "Воронка", "Common.define.effectData.textGrowShrink": "Изменение размера", "Common.define.effectData.textGrowTurn": "Увеличение с поворотом", "Common.define.effectData.textGrowWithColor": "Увеличение с изменением цвета", "Common.define.effectData.textHeart": "Сердце", "Common.define.effectData.textHeartbeat": "Пульс", "Common.define.effectData.textHexagon": "Шестиугольник", "Common.define.effectData.textHorizontal": "По горизонтали", "Common.define.effectData.textHorizontalFigure": "Горизонтальный знак 8", "Common.define.effectData.textHorizontalIn": "По горизонтали внутрь", "Common.define.effectData.textHorizontalOut": "По горизонтали наружу", "Common.define.effectData.textIn": "Внутрь", "Common.define.effectData.textInFromScreenCenter": "Увеличение из центра экрана", "Common.define.effectData.textInSlightly": "Небольшое увеличение", "Common.define.effectData.textInToScreenBottom": "Увеличение к нижней части экрана", "Common.define.effectData.textInvertedSquare": "Квадрат наизнанку", "Common.define.effectData.textInvertedTriangle": "Треугольник наизнанку", "Common.define.effectData.textLeft": "Влево", "Common.define.effectData.textLeftDown": "Влево и вниз", "Common.define.effectData.textLeftUp": "Влево и вверх", "Common.define.effectData.textLighten": "Высветление", "Common.define.effectData.textLineColor": "Цвет линии", "Common.define.effectData.textLines": "Линии", "Common.define.effectData.textLinesCurves": "Линии и кривые", "Common.define.effectData.textLoopDeLoop": "Петля", "Common.define.effectData.textLoops": "Петли", "Common.define.effectData.textModerate": "Средние", "Common.define.effectData.textNeutron": "Нейтрон", "Common.define.effectData.textObjectCenter": "Центр объекта", "Common.define.effectData.textObjectColor": "Цвет объекта", "Common.define.effectData.textOctagon": "Восьмиугольник", "Common.define.effectData.textOut": "Наружу", "Common.define.effectData.textOutFromScreenBottom": "Уменьшение из нижней части экрана", "Common.define.effectData.textOutSlightly": "Небольшое уменьшение", "Common.define.effectData.textOutToScreenCenter": "Уменьшение к центру экрана", "Common.define.effectData.textParallelogram": "Параллелограмм", "Common.define.effectData.textPath": "Пути перемещения", "Common.define.effectData.textPathCurve": "Кривая", "Common.define.effectData.textPathLine": "Линия", "Common.define.effectData.textPathScribble": "Рисованная кривая", "Common.define.effectData.textPeanut": "Земляной орех", "Common.define.effectData.textPeekIn": "Сбор", "Common.define.effectData.textPeekOut": "Задвигание", "Common.define.effectData.textPentagon": "Пятиугольник", "Common.define.effectData.textPinwheel": "Колесо", "Common.define.effectData.textPlus": "Плюс", "Common.define.effectData.textPointStar": "Остроконечная звезда", "Common.define.effectData.textPointStar4": "4-конечная звезда", "Common.define.effectData.textPointStar5": "5-конечная звезда", "Common.define.effectData.textPointStar6": "6-конечная звезда", "Common.define.effectData.textPointStar8": "8-конечная звезда", "Common.define.effectData.textPulse": "Пульсация", "Common.define.effectData.textRandomBars": "Случайные полосы", "Common.define.effectData.textRight": "Вправо", "Common.define.effectData.textRightDown": "Вправо и вниз", "Common.define.effectData.textRightTriangle": "Прямоугольный треугольник", "Common.define.effectData.textRightUp": "Вправо и вверх", "Common.define.effectData.textRiseUp": "Подъем", "Common.define.effectData.textSCurve1": "Синусоида 1", "Common.define.effectData.textSCurve2": "Синусоида 2", "Common.define.effectData.textShape": "Фигура", "Common.define.effectData.textShapes": "Фигуры", "Common.define.effectData.textShimmer": "Мерцание", "Common.define.effectData.textShrinkTurn": "Уменьшение с поворотом", "Common.define.effectData.textSineWave": "Частая синусоида", "Common.define.effectData.textSinkDown": "Падение", "Common.define.effectData.textSlideCenter": "Центр слайда", "Common.define.effectData.textSpecial": "Особые", "Common.define.effectData.textSpin": "Вращение", "Common.define.effectData.textSpinner": "Центрифуга", "Common.define.effectData.textSpiralIn": "Спираль", "Common.define.effectData.textSpiralLeft": "Влево по спирали", "Common.define.effectData.textSpiralOut": "Вылет по спирали", "Common.define.effectData.textSpiralRight": "Вправо по спирали", "Common.define.effectData.textSplit": "Панорама", "Common.define.effectData.textSpoke1": "1 сектор", "Common.define.effectData.textSpoke2": "2 сектора", "Common.define.effectData.textSpoke3": "3 сектора", "Common.define.effectData.textSpoke4": "4 сектора", "Common.define.effectData.textSpoke8": "8 секторов", "Common.define.effectData.textSpring": "Пружина", "Common.define.effectData.textSquare": "Ква<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textStairsDown": "Вниз по лестнице", "Common.define.effectData.textStretch": "Растяжение", "Common.define.effectData.textStrips": "Ленты", "Common.define.effectData.textSubtle": "Простые", "Common.define.effectData.textSwivel": "Вращение", "Common.define.effectData.textSwoosh": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textTeardrop": "Капля", "Common.define.effectData.textTeeter": "Качание", "Common.define.effectData.textToBottom": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textToBottomLeft": "Вниз влево", "Common.define.effectData.textToBottomRight": "Вниз вправо", "Common.define.effectData.textToLeft": "Влево", "Common.define.effectData.textToRight": "Вправо", "Common.define.effectData.textToTop": "Ввер<PERSON>", "Common.define.effectData.textToTopLeft": "Вверх влево", "Common.define.effectData.textToTopRight": "Вверх вправо", "Common.define.effectData.textTransparency": "Прозрачность", "Common.define.effectData.textTrapezoid": "Трапеция", "Common.define.effectData.textTurnDown": "Вправо и вниз", "Common.define.effectData.textTurnDownRight": "Вниз и вправо", "Common.define.effectData.textTurns": "Повороты", "Common.define.effectData.textTurnUp": "Вправо и вверх", "Common.define.effectData.textTurnUpRight": "Вверх и вправо", "Common.define.effectData.textUnderline": "Подчёркивание", "Common.define.effectData.textUp": "Ввер<PERSON>", "Common.define.effectData.textVertical": "По вертикали", "Common.define.effectData.textVerticalFigure": "Вертикальный знак 8", "Common.define.effectData.textVerticalIn": "По вертикали внутрь", "Common.define.effectData.textVerticalOut": "По вертикали наружу", "Common.define.effectData.textWave": "Волна", "Common.define.effectData.textWedge": "Симметрично по кругу", "Common.define.effectData.textWheel": "Колесо", "Common.define.effectData.textWhip": "Кнут", "Common.define.effectData.textWipe": "Появление", "Common.define.effectData.textZigzag": "Зиг<PERSON>аг", "Common.define.effectData.textZoom": "Масштабирование", "Common.define.gridlineData.txtCm": "см", "Common.define.gridlineData.txtPt": "пт", "Common.define.smartArt.textAccentedPicture": "Акцентируемый рисунок", "Common.define.smartArt.textAccentProcess": "Процесс со смещением", "Common.define.smartArt.textAlternatingFlow": "Переменный поток", "Common.define.smartArt.textAlternatingHexagons": "Чередующиеся шестиугольники", "Common.define.smartArt.textAlternatingPictureBlocks": "Чередующиеся блоки рисунков", "Common.define.smartArt.textAlternatingPictureCircles": "Чередующиеся круги рисунков", "Common.define.smartArt.textArchitectureLayout": "Архитектурный макет", "Common.define.smartArt.textArrowRibbon": "Лента со стрелками", "Common.define.smartArt.textAscendingPictureAccentProcess": "Процесс со смещенными по возрастанию рисунками", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Простой ломаный процесс", "Common.define.smartArt.textBasicBlockList": "Простой блочный список", "Common.define.smartArt.textBasicChevronProcess": "Простой уголковый процесс", "Common.define.smartArt.textBasicCycle": "Простой цикл", "Common.define.smartArt.textBasicMatrix": "Простая матрица", "Common.define.smartArt.textBasicPie": "Простая круговая", "Common.define.smartArt.textBasicProcess": "Простой процесс", "Common.define.smartArt.textBasicPyramid": "Простая пирамида", "Common.define.smartArt.textBasicRadial": "Простая радиальная", "Common.define.smartArt.textBasicTarget": "Простая целевая", "Common.define.smartArt.textBasicTimeline": "Простая временная шкала", "Common.define.smartArt.textBasicVenn": "Простая Венна", "Common.define.smartArt.textBendingPictureAccentList": "Ломаный список со смещенными рисунками", "Common.define.smartArt.textBendingPictureBlocks": "Нелинейные рисунки с блоками", "Common.define.smartArt.textBendingPictureCaption": "Нелинейные рисунки с подписями", "Common.define.smartArt.textBendingPictureCaptionList": "Ломаный список рисунков с подписями", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Нелинейные рисунки с полупрозрачным текстом", "Common.define.smartArt.textBlockCycle": "Блочный цикл", "Common.define.smartArt.textBubblePictureList": "Список рисунков с выносками", "Common.define.smartArt.textCaptionedPictures": "Подписанные рисунки", "Common.define.smartArt.textChevronAccentProcess": "Уголковый процесс со смещением", "Common.define.smartArt.textChevronList": "Уголковый список", "Common.define.smartArt.textCircleAccentTimeline": "Круглая временная шкала", "Common.define.smartArt.textCircleArrowProcess": "Стрелка процесса с кругами", "Common.define.smartArt.textCirclePictureHierarchy": "Иерархия с круглыми рисунками", "Common.define.smartArt.textCircleProcess": "Процесс с кругами", "Common.define.smartArt.textCircleRelationship": "Круг связей", "Common.define.smartArt.textCircularBendingProcess": "Круглый ломаный процесс", "Common.define.smartArt.textCircularPictureCallout": "Выноска с круглыми рисунками", "Common.define.smartArt.textClosedChevronProcess": "Закрытый уголковый процесс", "Common.define.smartArt.textContinuousArrowProcess": "Стрелка непрерывного процесса", "Common.define.smartArt.textContinuousBlockProcess": "Непрерывный блочный процесс", "Common.define.smartArt.textContinuousCycle": "Непрерывный цикл", "Common.define.smartArt.textContinuousPictureList": "Непрерывный список с рисунками", "Common.define.smartArt.textConvergingArrows": "Сходящиеся стрелки", "Common.define.smartArt.textConvergingRadial": "Сходящаяся радиальная", "Common.define.smartArt.textConvergingText": "Сходящийся текст", "Common.define.smartArt.textCounterbalanceArrows": "Уравновешивающие стрелки", "Common.define.smartArt.textCycle": "<PERSON>и<PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "Циклическая матрица", "Common.define.smartArt.textDescendingBlockList": "Нисходящий блочный список", "Common.define.smartArt.textDescendingProcess": "Убывающий процесс", "Common.define.smartArt.textDetailedProcess": "Подробный процесс", "Common.define.smartArt.textDivergingArrows": "Расходящиеся стрелки", "Common.define.smartArt.textDivergingRadial": "Расходящаяся радиальная", "Common.define.smartArt.textEquation": "Уравнение", "Common.define.smartArt.textFramedTextPicture": "Рисунок с текстом в рамке", "Common.define.smartArt.textFunnel": "Воронка", "Common.define.smartArt.textGear": "Шестеренка", "Common.define.smartArt.textGridMatrix": "Сетчатая матрица", "Common.define.smartArt.textGroupedList": "Сгруппированный список", "Common.define.smartArt.textHalfCircleOrganizationChart": "Полукруглая организационная диаграмма", "Common.define.smartArt.textHexagonCluster": "Кластер шестиугольников", "Common.define.smartArt.textHexagonRadial": "Радиальный шестиугольник", "Common.define.smartArt.textHierarchy": "Иерархия", "Common.define.smartArt.textHierarchyList": "Иерархический список", "Common.define.smartArt.textHorizontalBulletList": "Горизонтальный маркированный список", "Common.define.smartArt.textHorizontalHierarchy": "Горизонтальная иерархия", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Горизонтальная иерархия с подписями", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Горизонтальная многоуровневая иерархия", "Common.define.smartArt.textHorizontalOrganizationChart": "Горизонтальная организационная диаграмма", "Common.define.smartArt.textHorizontalPictureList": "Горизонтальный список рисунков", "Common.define.smartArt.textIncreasingArrowProcess": "Стрелка нарастающего процесса", "Common.define.smartArt.textIncreasingCircleProcess": "Нарастающий процесс с кругами", "Common.define.smartArt.textInterconnectedBlockProcess": "Процесс со взаимосвязанными блоками", "Common.define.smartArt.textInterconnectedRings": "Взаимосвязанные кольца", "Common.define.smartArt.textInvertedPyramid": "Инвертированная пирамида", "Common.define.smartArt.textLabeledHierarchy": "Иерархия с подписями", "Common.define.smartArt.textLinearVenn": "Линейная Венна", "Common.define.smartArt.textLinedList": "Список с линиями", "Common.define.smartArt.textList": "Список", "Common.define.smartArt.textMatrix": "Матрица", "Common.define.smartArt.textMultidirectionalCycle": "Разнонаправленный цикл", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Организационная диаграмма с именами и должностями", "Common.define.smartArt.textNestedTarget": "Вложенная целевая", "Common.define.smartArt.textNondirectionalCycle": "Ненаправленный цикл", "Common.define.smartArt.textOpposingArrows": "Противостоящие стрелки", "Common.define.smartArt.textOpposingIdeas": "Противоположные идеи", "Common.define.smartArt.textOrganizationChart": "Организационная диаграмма", "Common.define.smartArt.textOther": "Другое", "Common.define.smartArt.textPhasedProcess": "Поэтапный процесс", "Common.define.smartArt.textPicture": "Рисунок", "Common.define.smartArt.textPictureAccentBlocks": "Блоки со смещенными рисунками", "Common.define.smartArt.textPictureAccentList": "Список со смещенными рисунками", "Common.define.smartArt.textPictureAccentProcess": "Процесс со смещенными рисунками", "Common.define.smartArt.textPictureCaptionList": "Список названий рисунков", "Common.define.smartArt.textPictureFrame": "Фоторамка", "Common.define.smartArt.textPictureGrid": "Сетка рисунков", "Common.define.smartArt.textPictureLineup": "Линия рисунков", "Common.define.smartArt.textPictureOrganizationChart": "Организационная диаграмма с рисунками", "Common.define.smartArt.textPictureStrips": "Полосы рисунков", "Common.define.smartArt.textPieProcess": "Процесс с круговой диаграммой", "Common.define.smartArt.textPlusAndMinus": "Плюс и минус", "Common.define.smartArt.textProcess": "Процесс", "Common.define.smartArt.textProcessArrows": "Стрелки процесса", "Common.define.smartArt.textProcessList": "Список процессов", "Common.define.smartArt.textPyramid": "Пирамида", "Common.define.smartArt.textPyramidList": "Пирамидальный список", "Common.define.smartArt.textRadialCluster": "Радиал<PERSON>ный кластер", "Common.define.smartArt.textRadialCycle": "Радиальная циклическая", "Common.define.smartArt.textRadialList": "Радиальный список", "Common.define.smartArt.textRadialPictureList": "Радиальный список рисунков", "Common.define.smartArt.textRadialVenn": "Радиальная Венна", "Common.define.smartArt.textRandomToResultProcess": "Процесс от случайности к результату", "Common.define.smartArt.textRelationship": "Связь", "Common.define.smartArt.textRepeatingBendingProcess": "Повторяющийся ломаный процесс", "Common.define.smartArt.textReverseList": "Обратный список", "Common.define.smartArt.textSegmentedCycle": "Сегментированный цикл", "Common.define.smartArt.textSegmentedProcess": "Сегментированный процесс", "Common.define.smartArt.textSegmentedPyramid": "Сегментированная пирамида", "Common.define.smartArt.textSnapshotPictureList": "Список со снимками", "Common.define.smartArt.textSpiralPicture": "Спираль рисунков", "Common.define.smartArt.textSquareAccentList": "Список с квадратиками", "Common.define.smartArt.textStackedList": "Список в столбик", "Common.define.smartArt.textStackedVenn": "Венна в столбик", "Common.define.smartArt.textStaggeredProcess": "Ступенчатый процесс", "Common.define.smartArt.textStepDownProcess": "Нисходящий процесс", "Common.define.smartArt.textStepUpProcess": "Восходящий процесс", "Common.define.smartArt.textSubStepProcess": "Процесс с вложенными шагами", "Common.define.smartArt.textTabbedArc": "Дуга с вкладками", "Common.define.smartArt.textTableHierarchy": "Табличная иерархия", "Common.define.smartArt.textTableList": "Табличный список", "Common.define.smartArt.textTabList": "Список вкладок", "Common.define.smartArt.textTargetList": "Целевой список", "Common.define.smartArt.textTextCycle": "Текстовый цикл", "Common.define.smartArt.textThemePictureAccent": "Смещенные рисунки темы", "Common.define.smartArt.textThemePictureAlternatingAccent": "Чередующиеся смещенные рисунки темы", "Common.define.smartArt.textThemePictureGrid": "Сетка рисунков темы", "Common.define.smartArt.textTitledMatrix": "Матрица с заголовками", "Common.define.smartArt.textTitledPictureAccentList": "Список со смещенными рисунками и заголовком", "Common.define.smartArt.textTitledPictureBlocks": "Блоки рисунков с названиями", "Common.define.smartArt.textTitlePictureLineup": "Линия рисунков с названиями", "Common.define.smartArt.textTrapezoidList": "Трапециевидный список", "Common.define.smartArt.textUpwardArrow": "Восходящая стрелка", "Common.define.smartArt.textVaryingWidthList": "Список переменной ширины", "Common.define.smartArt.textVerticalAccentList": "Вертикальный список со смещением", "Common.define.smartArt.textVerticalArrowList": "Вертикальный список со стрелкой", "Common.define.smartArt.textVerticalBendingProcess": "Вертикальный ломаный процесс", "Common.define.smartArt.textVerticalBlockList": "Вертикальный блочный список", "Common.define.smartArt.textVerticalBoxList": "Вертикальный список", "Common.define.smartArt.textVerticalBracketList": "Вертикальный список со скобками", "Common.define.smartArt.textVerticalBulletList": "Вертикальный маркированный список", "Common.define.smartArt.textVerticalChevronList": "Вертикальный уголковый список", "Common.define.smartArt.textVerticalCircleList": "Вертикальный список с кругами", "Common.define.smartArt.textVerticalCurvedList": "Вертикальный нелинейный список", "Common.define.smartArt.textVerticalEquation": "Вертикальное уравнение", "Common.define.smartArt.textVerticalPictureAccentList": "Вертикальный список со смещенными рисунками", "Common.define.smartArt.textVerticalPictureList": "Вертикальный список рисунков", "Common.define.smartArt.textVerticalProcess": "Вертикальный процесс", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Документ заблокирован на редактирование. Вы можете внести изменения и сохранить его как локальную копию позже.", "Common.Translation.tipFileReadOnly": "Файл доступен только для чтения. Чтобы сохранить изменения, сохраните файл с новым названием или в другом месте.", "Common.Translation.warnFileLocked": "Файл редактируется в другом приложении. Вы можете продолжить редактирование и сохранить его как копию.", "Common.Translation.warnFileLockedBtnEdit": "Создать копию", "Common.Translation.warnFileLockedBtnView": "Открыть на просмотр", "Common.UI.ButtonColored.textAutoColor": "Автоматический", "Common.UI.ButtonColored.textNewColor": "Пользовательский цвет", "Common.UI.ComboBorderSize.txtNoBorders": "Без границ", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Без границ", "Common.UI.ComboDataView.emptyComboText": "Без стилей", "Common.UI.ExtendedColorDialog.addButtonText": "Добавить", "Common.UI.ExtendedColorDialog.textCurrent": "Текущий", "Common.UI.ExtendedColorDialog.textHexErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 000000 до FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Новый", "Common.UI.ExtendedColorDialog.textRGBErr": "Введено некорректное значение.<br>Пожалуйста, введите числовое значение от 0 до 255.", "Common.UI.HSBColorPicker.textNoColor": "Без цвета", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Скрыть пароль", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Показать пароль", "Common.UI.SearchBar.textFind": "Поиск", "Common.UI.SearchBar.tipCloseSearch": "Закрыть поиск", "Common.UI.SearchBar.tipNextResult": "Следующий результат", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Открыть дополнительные параметры", "Common.UI.SearchBar.tipPreviousResult": "Предыдущий результат", "Common.UI.SearchDialog.textHighlight": "Выделить результаты", "Common.UI.SearchDialog.textMatchCase": "С учетом регистра", "Common.UI.SearchDialog.textReplaceDef": "Введите текст для замены", "Common.UI.SearchDialog.textSearchStart": "Введите здесь текст", "Common.UI.SearchDialog.textTitle": "Поиск и замена", "Common.UI.SearchDialog.textTitle2": "Поиск", "Common.UI.SearchDialog.textWholeWords": "Только слово целиком", "Common.UI.SearchDialog.txtBtnHideReplace": "Скрыть поле замены", "Common.UI.SearchDialog.txtBtnReplace": "Заменить", "Common.UI.SearchDialog.txtBtnReplaceAll": "Заменить все", "Common.UI.SynchronizeTip.textDontShow": "Больше не показывать это сообщение", "Common.UI.SynchronizeTip.textSynchronize": "Документ изменен другим пользователем.<br/>Нажмите, чтобы сохранить свои изменения и загрузить обновления.", "Common.UI.ThemeColorPalette.textRecentColors": "Недавние цвета", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартные цвета", "Common.UI.ThemeColorPalette.textThemeColors": "Цвета темы", "Common.UI.Themes.txtThemeClassicLight": "Классическая светлая", "Common.UI.Themes.txtThemeContrastDark": "Контрастная темная", "Common.UI.Themes.txtThemeDark": "Темная", "Common.UI.Themes.txtThemeLight": "Светлая", "Common.UI.Themes.txtThemeSystem": "Системная", "Common.UI.Window.cancelButtonText": "Отмена", "Common.UI.Window.closeButtonText": "Закрыть", "Common.UI.Window.noButtonText": "Нет", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Подтверждение", "Common.UI.Window.textDontShow": "Больше не показывать это сообщение", "Common.UI.Window.textError": "Ошибка", "Common.UI.Window.textInformation": "Информация", "Common.UI.Window.textWarning": "Внимание", "Common.UI.Window.yesButtonText": "Да", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "пт", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "адрес: ", "Common.Views.About.txtLicensee": "ЛИЦЕНЗИАТ", "Common.Views.About.txtLicensor": "ЛИЦЕНЗИАР", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Разработано", "Common.Views.About.txtTel": "тел.: ", "Common.Views.About.txtVersion": "Версия ", "Common.Views.AutoCorrectDialog.textAdd": "Добавить", "Common.Views.AutoCorrectDialog.textApplyText": "Применять при вводе", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Автозамена текста", "Common.Views.AutoCorrectDialog.textAutoFormat": "Автоформат при вводе", "Common.Views.AutoCorrectDialog.textBulleted": "Стили маркированных списков", "Common.Views.AutoCorrectDialog.textBy": "На", "Common.Views.AutoCorrectDialog.textDelete": "Удалить", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Добавлять точку двойным пробелом", "Common.Views.AutoCorrectDialog.textFLCells": "Делать первые буквы ячеек таблиц прописными", "Common.Views.AutoCorrectDialog.textFLSentence": "Делать первые буквы предложений прописными", "Common.Views.AutoCorrectDialog.textHyperlink": "Адреса в Интернете и сетевые пути гиперссылками", "Common.Views.AutoCorrectDialog.textHyphens": "Дефисы (--) на тире (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Автозамена математическими символами", "Common.Views.AutoCorrectDialog.textNumbered": "Стили нумерованных списков", "Common.Views.AutoCorrectDialog.textQuotes": "Прямые кавычки парными", "Common.Views.AutoCorrectDialog.textRecognized": "Распознанные функции", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Следующие выражения являются распознанными математическими функциями. Они не будут автоматически выделяться курсивом.", "Common.Views.AutoCorrectDialog.textReplace": "Заменить", "Common.Views.AutoCorrectDialog.textReplaceText": "Заменять при вводе", "Common.Views.AutoCorrectDialog.textReplaceType": "Заменять текст при вводе", "Common.Views.AutoCorrectDialog.textReset": "Сброс", "Common.Views.AutoCorrectDialog.textResetAll": "Сбросить настройки", "Common.Views.AutoCorrectDialog.textRestore": "Восстановить", "Common.Views.AutoCorrectDialog.textTitle": "Автозамена", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Распознанные функции должны содержать только прописные или строчные буквы от А до Я.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Все добавленные вами выражения будут удалены, а удаленные восстановлены. Вы хотите продолжить?", "Common.Views.AutoCorrectDialog.warnReplace": "Элемент автозамены для %1 уже существует. Вы хотите заменить его?", "Common.Views.AutoCorrectDialog.warnReset": "Все добавленные вами автозамены будут удалены, а для измененных будут восстановлены исходные значения. Вы хотите продолжить?", "Common.Views.AutoCorrectDialog.warnRestore": "Элемент автозамены для %1 будет сброшен на исходное значение. Вы хотите продолжить?", "Common.Views.Chat.textSend": "Отправить", "Common.Views.Comments.mniAuthorAsc": "По автору от А до Я", "Common.Views.Comments.mniAuthorDesc": "По автору от Я до А", "Common.Views.Comments.mniDateAsc": "От старых к новым", "Common.Views.Comments.mniDateDesc": "От новых к старым", "Common.Views.Comments.mniFilterGroups": "Фильтровать по группе", "Common.Views.Comments.mniPositionAsc": "Сверху вниз", "Common.Views.Comments.mniPositionDesc": "Снизу вверх", "Common.Views.Comments.textAdd": "Добавить", "Common.Views.Comments.textAddComment": "Добавить", "Common.Views.Comments.textAddCommentToDoc": "Добавить комментарий к документу", "Common.Views.Comments.textAddReply": "Добавить ответ", "Common.Views.Comments.textAll": "Все", "Common.Views.Comments.textAnonym": "Гость", "Common.Views.Comments.textCancel": "Отмена", "Common.Views.Comments.textClose": "Закрыть", "Common.Views.Comments.textClosePanel": "Закрыть комментарии", "Common.Views.Comments.textComments": "Комментарии", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Введите здесь свой комментарий", "Common.Views.Comments.textHintAddComment": "Добавить комментарий", "Common.Views.Comments.textOpenAgain": "Открыть снова", "Common.Views.Comments.textReply": "Ответить", "Common.Views.Comments.textResolve": "Решить", "Common.Views.Comments.textResolved": "Решено", "Common.Views.Comments.textSort": "Сортировать комментарии", "Common.Views.Comments.textViewResolved": "У вас нет прав для повторного открытия комментария", "Common.Views.Comments.txtEmpty": "В документе нет комментариев.", "Common.Views.CopyWarningDialog.textDontShow": "Больше не показывать это сообщение", "Common.Views.CopyWarningDialog.textMsg": "Операции копирования, вырезания и вставки можно выполнить с помощью кнопок на панели инструментов и команд контекстного меню только в этой вкладке редактора.<br><br>Для копирования в другие приложения и вставки из них используйте следующие сочетания клавиш:", "Common.Views.CopyWarningDialog.textTitle": "Операции копирования, вырезания и вставки", "Common.Views.CopyWarningDialog.textToCopy": "для копирования", "Common.Views.CopyWarningDialog.textToCut": "для вырезания", "Common.Views.CopyWarningDialog.textToPaste": "для вставки", "Common.Views.DocumentAccessDialog.textLoading": "Загрузка...", "Common.Views.DocumentAccessDialog.textTitle": "Настройки совместного доступа", "Common.Views.ExternalDiagramEditor.textTitle": "Редактор диаграмм", "Common.Views.ExternalEditor.textClose": "Закрыть", "Common.Views.ExternalEditor.textSave": "Сохранить и выйти", "Common.Views.ExternalOleEditor.textTitle": "Редактор таблиц", "Common.Views.Header.labelCoUsersDescr": "Пользователи, редактирующие документ:", "Common.Views.Header.textAddFavorite": "Добавить в избранное", "Common.Views.Header.textAdvSettings": "Дополнительные параметры", "Common.Views.Header.textBack": "Открыть расположение файла", "Common.Views.Header.textCompactView": "Скрыть панель инструментов", "Common.Views.Header.textHideLines": "Скрыть линейки", "Common.Views.Header.textHideNotes": "Скрыть заметки", "Common.Views.Header.textHideStatusBar": "Скрыть строку состояния", "Common.Views.Header.textReadOnly": "Только чтение", "Common.Views.Header.textRemoveFavorite": "Удалить из избранного", "Common.Views.Header.textSaveBegin": "Сохранение...", "Common.Views.Header.textSaveChanged": "Изменен", "Common.Views.Header.textSaveEnd": "Все изменения сохранены", "Common.Views.Header.textSaveExpander": "Все изменения сохранены", "Common.Views.Header.textShare": "Доступ", "Common.Views.Header.textZoom": "Масш<PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Управление правами доступа к документу", "Common.Views.Header.tipDownload": "Скачать файл", "Common.Views.Header.tipGoEdit": "Редактировать текущий файл", "Common.Views.Header.tipPrint": "Напечатать файл", "Common.Views.Header.tipPrintQuick": "Быстрая печать", "Common.Views.Header.tipRedo": "Повторить", "Common.Views.Header.tipSave": "Сохранить", "Common.Views.Header.tipSearch": "Поиск", "Common.Views.Header.tipUndo": "Отменить", "Common.Views.Header.tipUndock": "Открепить в отдельном окне", "Common.Views.Header.tipUsers": "Просмотр пользователей", "Common.Views.Header.tipViewSettings": "Параметры вида", "Common.Views.Header.tipViewUsers": "Просмотр пользователей и управление правами доступа к документу", "Common.Views.Header.txtAccessRights": "Изменить права доступа", "Common.Views.Header.txtRename": "Переименовать", "Common.Views.History.textCloseHistory": "Закрыть историю", "Common.Views.History.textHide": "Свернуть", "Common.Views.History.textHideAll": "Скрыть подробные изменения", "Common.Views.History.textRestore": "Восстановить", "Common.Views.History.textShow": "Развернуть", "Common.Views.History.textShowAll": "Показать подробные изменения", "Common.Views.History.textVer": "вер.", "Common.Views.ImageFromUrlDialog.textUrl": "Вставьте URL изображения:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Это поле обязательно для заполнения", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Это поле должно быть URL-адресом в формате \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Необходимо указать допустимое количество строк и столбцов.", "Common.Views.InsertTableDialog.txtColumns": "Количество столбцов", "Common.Views.InsertTableDialog.txtMaxText": "Максимальное значение для этого поля - {0}.", "Common.Views.InsertTableDialog.txtMinText": "Минимальное значение для этого поля - {0}.", "Common.Views.InsertTableDialog.txtRows": "Количество строк", "Common.Views.InsertTableDialog.txtTitle": "Размер таблицы", "Common.Views.InsertTableDialog.txtTitleSplit": "Разделить ячейку", "Common.Views.LanguageDialog.labelSelect": "Выбрать язык документа", "Common.Views.ListSettingsDialog.textBulleted": "Маркированный", "Common.Views.ListSettingsDialog.textFromFile": "Из файла", "Common.Views.ListSettingsDialog.textFromStorage": "Из хранилища", "Common.Views.ListSettingsDialog.textFromUrl": "По URL", "Common.Views.ListSettingsDialog.textNumbering": "Нумерованный", "Common.Views.ListSettingsDialog.textSelect": "Выбрать из", "Common.Views.ListSettingsDialog.tipChange": "Изменить маркер", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Цвет", "Common.Views.ListSettingsDialog.txtImage": "Изображение", "Common.Views.ListSettingsDialog.txtImport": "Импорт", "Common.Views.ListSettingsDialog.txtNewBullet": "Новый маркер", "Common.Views.ListSettingsDialog.txtNewImage": "Новое изображение", "Common.Views.ListSettingsDialog.txtNone": "Нет", "Common.Views.ListSettingsDialog.txtOfText": "% текста", "Common.Views.ListSettingsDialog.txtSize": "Размер", "Common.Views.ListSettingsDialog.txtStart": "Начать с", "Common.Views.ListSettingsDialog.txtSymbol": "Символ", "Common.Views.ListSettingsDialog.txtTitle": "Параметры списка", "Common.Views.ListSettingsDialog.txtType": "Тип", "Common.Views.OpenDialog.closeButtonText": "Закрыть файл", "Common.Views.OpenDialog.txtEncoding": "Кодировка", "Common.Views.OpenDialog.txtIncorrectPwd": "Указан неверный пароль.", "Common.Views.OpenDialog.txtOpenFile": "Введите пароль для открытия файла", "Common.Views.OpenDialog.txtPassword": "Пароль", "Common.Views.OpenDialog.txtProtected": "Как только вы введете пароль и откроете файл, текущий пароль к файлу будет сброшен.", "Common.Views.OpenDialog.txtTitle": "Выбрать параметры %1", "Common.Views.OpenDialog.txtTitleProtected": "Защищенный файл", "Common.Views.PasswordDialog.txtDescription": "Задайте пароль, чтобы защитить этот документ", "Common.Views.PasswordDialog.txtIncorrectPwd": "Пароль и его подтверждение не совпадают", "Common.Views.PasswordDialog.txtPassword": "Пароль", "Common.Views.PasswordDialog.txtRepeat": "Повторить пароль", "Common.Views.PasswordDialog.txtTitle": "Установка пароля", "Common.Views.PasswordDialog.txtWarning": "Внимание: Если пароль забыт или утерян, его нельзя восстановить. Храните его в надежном месте.", "Common.Views.PluginDlg.textLoading": "Загрузка", "Common.Views.Plugins.groupCaption": "Плагины", "Common.Views.Plugins.strPlugins": "Плагины", "Common.Views.Plugins.textClosePanel": "Закрыть плагин", "Common.Views.Plugins.textLoading": "Загрузка", "Common.Views.Plugins.textStart": "Запустить", "Common.Views.Plugins.textStop": "Остановить", "Common.Views.Protection.hintAddPwd": "Зашифровать с помощью пароля", "Common.Views.Protection.hintDelPwd": "Удалить пароль", "Common.Views.Protection.hintPwd": "Изменить или удалить пароль", "Common.Views.Protection.hintSignature": "Добавить цифровую подпись или строку подписи", "Common.Views.Protection.txtAddPwd": "Добавить пароль", "Common.Views.Protection.txtChangePwd": "Изменить пароль", "Common.Views.Protection.txtDeletePwd": "Удалить пароль", "Common.Views.Protection.txtEncrypt": "Шифровать", "Common.Views.Protection.txtInvisibleSignature": "Добавить цифровую подпись", "Common.Views.Protection.txtSignature": "Подпись", "Common.Views.Protection.txtSignatureLine": "Добавить строку подписи", "Common.Views.RenameDialog.textName": "Имя файла", "Common.Views.RenameDialog.txtInvalidName": "Имя файла не должно содержать следующих символов: ", "Common.Views.ReviewChanges.hintNext": "К следующему изменению", "Common.Views.ReviewChanges.hintPrev": "К предыдущему изменению", "Common.Views.ReviewChanges.strFast": "Быстрый", "Common.Views.ReviewChanges.strFastDesc": "Совместное редактирование в режиме реального времени. Все изменения сохраняются автоматически.", "Common.Views.ReviewChanges.strStrict": "Строгий", "Common.Views.ReviewChanges.strStrictDesc": "Используйте кнопку 'Сохранить' для синхронизации изменений, вносимых вами и другими пользователями.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Принять текущее изменение", "Common.Views.ReviewChanges.tipCoAuthMode": "Задать режим совместного редактирования", "Common.Views.ReviewChanges.tipCommentRem": "Удалить комментарии", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Удалить текущие комментарии", "Common.Views.ReviewChanges.tipCommentResolve": "Решить комментарии", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Решить текущие комментарии", "Common.Views.ReviewChanges.tipHistory": "Показать историю версий", "Common.Views.ReviewChanges.tipRejectCurrent": "Отклонить текущее изменение", "Common.Views.ReviewChanges.tipReview": "Отслеживать изменения", "Common.Views.ReviewChanges.tipReviewView": "Выберите режим, в котором вы хотите отображать изменения", "Common.Views.ReviewChanges.tipSetDocLang": "Задать язык документа", "Common.Views.ReviewChanges.tipSetSpelling": "Проверка орфографии", "Common.Views.ReviewChanges.tipSharing": "Управление правами доступа к документу", "Common.Views.ReviewChanges.txtAccept": "Принять", "Common.Views.ReviewChanges.txtAcceptAll": "Принять все изменения", "Common.Views.ReviewChanges.txtAcceptChanges": "Принять изменения", "Common.Views.ReviewChanges.txtAcceptCurrent": "Принять текущее изменение", "Common.Views.ReviewChanges.txtChat": "Чат", "Common.Views.ReviewChanges.txtClose": "Закрыть", "Common.Views.ReviewChanges.txtCoAuthMode": "Режим совместного редактирования", "Common.Views.ReviewChanges.txtCommentRemAll": "Удалить все комментарии", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Удалить текущие комментарии", "Common.Views.ReviewChanges.txtCommentRemMy": "Удалить мои комментарии", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Удалить мои текущие комментарии", "Common.Views.ReviewChanges.txtCommentRemove": "Удалить", "Common.Views.ReviewChanges.txtCommentResolve": "Решить", "Common.Views.ReviewChanges.txtCommentResolveAll": "Решить все комментарии", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Решить текущие комментарии", "Common.Views.ReviewChanges.txtCommentResolveMy": "Решить мои комментарии", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Решить мои текущие комментарии", "Common.Views.ReviewChanges.txtDocLang": "Язык", "Common.Views.ReviewChanges.txtFinal": "Все изменения приняты (просмотр)", "Common.Views.ReviewChanges.txtFinalCap": "Измененный документ", "Common.Views.ReviewChanges.txtHistory": "История версий", "Common.Views.ReviewChanges.txtMarkup": "Все изменения (редактирование)", "Common.Views.ReviewChanges.txtMarkupCap": "Изменения", "Common.Views.ReviewChanges.txtNext": "К следующему", "Common.Views.ReviewChanges.txtOriginal": "Все изменения отклонены (просмотр)", "Common.Views.ReviewChanges.txtOriginalCap": "Исходный документ", "Common.Views.ReviewChanges.txtPrev": "К предыдущему", "Common.Views.ReviewChanges.txtReject": "Отклонить", "Common.Views.ReviewChanges.txtRejectAll": "Отклонить все изменения", "Common.Views.ReviewChanges.txtRejectChanges": "Отклонить изменения", "Common.Views.ReviewChanges.txtRejectCurrent": "Отклонить текущее изменение", "Common.Views.ReviewChanges.txtSharing": "Совместный доступ", "Common.Views.ReviewChanges.txtSpelling": "Проверка орфографии", "Common.Views.ReviewChanges.txtTurnon": "Отслеживание изменений", "Common.Views.ReviewChanges.txtView": "Отображение", "Common.Views.ReviewPopover.textAdd": "Добавить", "Common.Views.ReviewPopover.textAddReply": "Добавить ответ", "Common.Views.ReviewPopover.textCancel": "Отмена", "Common.Views.ReviewPopover.textClose": "Закрыть", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Введите здесь свой комментарий", "Common.Views.ReviewPopover.textMention": "+упоминание предоставит доступ к документу и отправит оповещение по почте", "Common.Views.ReviewPopover.textMentionNotify": "+упоминание отправит пользователю оповещение по почте", "Common.Views.ReviewPopover.textOpenAgain": "Открыть снова", "Common.Views.ReviewPopover.textReply": "Ответить", "Common.Views.ReviewPopover.textResolve": "Решить", "Common.Views.ReviewPopover.textViewResolved": "У вас нет прав для повторного открытия комментария", "Common.Views.ReviewPopover.txtDeleteTip": "Удалить", "Common.Views.ReviewPopover.txtEditTip": "Редактировать", "Common.Views.SaveAsDlg.textLoading": "Загрузка", "Common.Views.SaveAsDlg.textTitle": "Папка для сохранения", "Common.Views.SearchPanel.textCaseSensitive": "С учетом регистра", "Common.Views.SearchPanel.textCloseSearch": "Закрыть поиск", "Common.Views.SearchPanel.textContentChanged": "Документ изменен.", "Common.Views.SearchPanel.textFind": "Поиск", "Common.Views.SearchPanel.textFindAndReplace": "Поиск и замена", "Common.Views.SearchPanel.textMatchUsingRegExp": "Сопоставление с использованием регулярных выражений", "Common.Views.SearchPanel.textNoMatches": "Совпадений нет", "Common.Views.SearchPanel.textNoSearchResults": "Ничего не найдено", "Common.Views.SearchPanel.textReplace": "Заменить", "Common.Views.SearchPanel.textReplaceAll": "Заменить все", "Common.Views.SearchPanel.textReplaceWith": "Заменить на", "Common.Views.SearchPanel.textSearchAgain": "{0}Выполнить новый поиск{1} для получения точных результатов.", "Common.Views.SearchPanel.textSearchHasStopped": "Поиск остановлен", "Common.Views.SearchPanel.textSearchResults": "Результаты поиска: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Слишком много результатов, чтобы отобразить их здесь", "Common.Views.SearchPanel.textWholeWords": "Только слово целиком", "Common.Views.SearchPanel.tipNextResult": "Следующий результат", "Common.Views.SearchPanel.tipPreviousResult": "Предыдущий результат", "Common.Views.SelectFileDlg.textLoading": "Загрузка", "Common.Views.SelectFileDlg.textTitle": "Выбрать источник данных", "Common.Views.SignDialog.textBold": "Полужирный", "Common.Views.SignDialog.textCertificate": "Сертификат", "Common.Views.SignDialog.textChange": "Изменить", "Common.Views.SignDialog.textInputName": "Введите имя подписывающего", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Имя подписывающего не должно быть пустым.", "Common.Views.SignDialog.textPurpose": "Цель подписания документа", "Common.Views.SignDialog.textSelect": "Выбрать", "Common.Views.SignDialog.textSelectImage": "Выбрать изображение", "Common.Views.SignDialog.textSignature": "Как выглядит подпись:", "Common.Views.SignDialog.textTitle": "Подписание документа", "Common.Views.SignDialog.textUseImage": "или нажмите 'Выбрать изображение', чтобы использовать изображение в качестве подписи", "Common.Views.SignDialog.textValid": "Действителен с %1 по %2", "Common.Views.SignDialog.tipFontName": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "Размер шрифта", "Common.Views.SignSettingsDialog.textAllowComment": "Разрешить подписывающему добавлять примечания в окне подписи", "Common.Views.SignSettingsDialog.textDefInstruction": "Перед подписанием документа убедитесь, что подписываемое содержимое является правильным.", "Common.Views.SignSettingsDialog.textInfoEmail": "Адрес электронной почты предложенного подписывающего", "Common.Views.SignSettingsDialog.textInfoName": "Предложенный подписывающий", "Common.Views.SignSettingsDialog.textInfoTitle": "Должность предложенного подписывающего", "Common.Views.SignSettingsDialog.textInstructions": "Инструкции для подписывающего", "Common.Views.SignSettingsDialog.textShowDate": "Показывать дату подписи в строке подписи", "Common.Views.SignSettingsDialog.textTitle": "Настройка подписи", "Common.Views.SignSettingsDialog.txtEmpty": "Это поле необходимо заполнить", "Common.Views.SymbolTableDialog.textCharacter": "Символ", "Common.Views.SymbolTableDialog.textCode": "Код знака из Юникод (шестн.)", "Common.Views.SymbolTableDialog.textCopyright": "Знак авторского права", "Common.Views.SymbolTableDialog.textDCQuote": "Закрывающая двойная кавычка", "Common.Views.SymbolTableDialog.textDOQuote": "Открывающая двойная кавычка", "Common.Views.SymbolTableDialog.textEllipsis": "Горизонтальное многоточие", "Common.Views.SymbolTableDialog.textEmDash": "Длинное тире", "Common.Views.SymbolTableDialog.textEmSpace": "Длинный пробел", "Common.Views.SymbolTableDialog.textEnDash": "Короткое тире", "Common.Views.SymbolTableDialog.textEnSpace": "Короткий пробел", "Common.Views.SymbolTableDialog.textFont": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Неразрывный дефис", "Common.Views.SymbolTableDialog.textNBSpace": "Неразрывный пробел", "Common.Views.SymbolTableDialog.textPilcrow": "Знак абзаца", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 пробела", "Common.Views.SymbolTableDialog.textRange": "Набор", "Common.Views.SymbolTableDialog.textRecent": "Ранее использовавшиеся символы", "Common.Views.SymbolTableDialog.textRegistered": "Зарегистрированный товарный знак", "Common.Views.SymbolTableDialog.textSCQuote": "Закрывающая одинарная кавычка", "Common.Views.SymbolTableDialog.textSection": "Знак раздела", "Common.Views.SymbolTableDialog.textShortcut": "Сочетание клавиш", "Common.Views.SymbolTableDialog.textSHyphen": "Мягкий дефис", "Common.Views.SymbolTableDialog.textSOQuote": "Открывающая одинарная кавычка", "Common.Views.SymbolTableDialog.textSpecial": "Специальные символы", "Common.Views.SymbolTableDialog.textSymbols": "Символы", "Common.Views.SymbolTableDialog.textTitle": "Символ", "Common.Views.SymbolTableDialog.textTradeMark": "Символ товарного знака", "Common.Views.UserNameDialog.textDontShow": "Больше не спрашивать", "Common.Views.UserNameDialog.textLabel": "Подпись:", "Common.Views.UserNameDialog.textLabelError": "Подпись не должна быть пустой.", "PE.Controllers.LeftMenu.leavePageText": "Все несохраненные изменения в этом документе будут потеряны.<br> Нажмите кнопку \"Отмена\", а затем нажмите кнопку \"Сохранить\", чтобы сохранить их. Нажмите кнопку \"OK\", чтобы сбросить все несохраненные изменения.", "PE.Controllers.LeftMenu.newDocumentTitle": "Презентация без имени", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Внимание", "PE.Controllers.LeftMenu.requestEditRightsText": "Запрос прав на редактирование...", "PE.Controllers.LeftMenu.textLoadHistory": "Загрузка истории версий...", "PE.Controllers.LeftMenu.textNoTextFound": "Искомые данные не найдены. Пожалуйста, измените параметры поиска.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Замена выполнена. Пропущено вхождений - {0}.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Поиск выполнен. Заменено вхождений: {0}", "PE.Controllers.LeftMenu.txtUntitled": "Без имени", "PE.Controllers.Main.applyChangesTextText": "Загрузка данных...", "PE.Controllers.Main.applyChangesTitleText": "Загрузка данных", "PE.Controllers.Main.confirmMaxChangesSize": "Размер внесенных изменений превышает ограничение, установленное для вашего сервера.<br>Нажмите \"Отменить\" для отмены последнего действия или нажмите \"Продолжить\", чтобы сохранить действие локально (потребуется скачать файл или скопировать его содержимое чтобы ничего не потерялось).", "PE.Controllers.Main.convertationTimeoutText": "Превышено время ожидания конвертации.", "PE.Controllers.Main.criticalErrorExtText": "Нажмите \"OK\", чтобы вернуться к списку документов.", "PE.Controllers.Main.criticalErrorTitle": "Ошибка", "PE.Controllers.Main.downloadErrorText": "Загрузка не удалась.", "PE.Controllers.Main.downloadTextText": "Загрузка презентации...", "PE.Controllers.Main.downloadTitleText": "Загрузка презентации", "PE.Controllers.Main.errorAccessDeny": "Вы пытаетесь выполнить действие, на которое у вас нет прав.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "PE.Controllers.Main.errorBadImageUrl": "Неправильный URL-адрес изображения", "PE.Controllers.Main.errorCannotPasteImg": "Не удается вставить это изображение из буфера обмена, но вы можете сохранить его на устройстве и вставить оттуда или вы можете скопировать изображение без текста и вставить его в презентацию.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Потеряно соединение с сервером. В данный момент нельзя отредактировать документ.", "PE.Controllers.Main.errorComboSeries": "Для создания комбинированной диаграммы выберите не менее двух рядов данных.", "PE.Controllers.Main.errorConnectToServer": "Не удается сохранить документ. Проверьте параметры подключения или обратитесь к вашему администратору.<br>Когда вы нажмете на кнопку 'OK', вам будет предложено скачать документ.", "PE.Controllers.Main.errorDatabaseConnection": "Внешняя ошибка.<br>Ош<PERSON>бка подключения к базе данных. Если ошибка повторяется, пожалуйста, обратитесь в службу поддержки.", "PE.Controllers.Main.errorDataEncrypted": "Получены зашифрованные изменения, их нельзя расшифровать.", "PE.Controllers.Main.errorDataRange": "Некорректный диапазон данных.", "PE.Controllers.Main.errorDefaultMessage": "Код ошибки: %1", "PE.Controllers.Main.errorDirectUrl": "Проверьте ссылку на документ.<br>Эта ссылка должна быть прямой ссылкой для скачивания файла.", "PE.Controllers.Main.errorEditingDownloadas": "В ходе работы с документом произошла ошибка.<br>Используйте опцию 'Скачать как', чтобы сохранить резервную копию файла на диск.", "PE.Controllers.Main.errorEditingSaveas": "В ходе работы с документом произошла ошибка.<br>Используйте опцию 'Сохранить как...', чтобы сохранить резервную копию файла на диск.", "PE.Controllers.Main.errorEmailClient": "Не найден почтовый клиент", "PE.Controllers.Main.errorFilePassProtect": "Файл защищен паролем и не может быть открыт.", "PE.Controllers.Main.errorFileSizeExceed": "Размер файла превышает ограничение, установленное для вашего сервера.<br>Обратитесь к администратору Сервера документов для получения дополнительной информации.", "PE.Controllers.Main.errorForceSave": "При сохранении файла произошла ошибка. Используйте опцию 'Скачать как', чтобы сохранить файл на диск или повторите попытку позже.", "PE.Controllers.Main.errorInconsistentExt": "При открытии файла произошла ошибка.<br>Содержимое файла не соответствует расширению файла.", "PE.Controllers.Main.errorInconsistentExtDocx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует документам (например, docx), но файл имеет несоответствующее расширение: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует одному из следующих форматов: pdf/djvu/xps/oxps, но файл имеет несоответствующее расширение: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует презентациям (например, pptx), но файл имеет несоответствующее расширение: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует электронным таблицам (например, xlsx), но файл имеет несоответствующее расширение: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Неизвестный дескриптор ключа", "PE.Controllers.Main.errorKeyExpire": "Срок действия дескриптора ключа истек", "PE.Controllers.Main.errorLoadingFont": "Шрифты не загружены.<br>Пож<PERSON><PERSON><PERSON><PERSON>ста, обратитесь к администратору Сервера документов.", "PE.Controllers.Main.errorProcessSaveResult": "Сбой при сохранении.", "PE.Controllers.Main.errorServerVersion": "Версия редактора была обновлена. Страница будет перезагружена, чтобы применить изменения.", "PE.Controllers.Main.errorSessionAbsolute": "Время сеанса редактирования документа истекло. Пожалуйста, обновите страницу.", "PE.Controllers.Main.errorSessionIdle": "Документ долгое время не редактировался. Пожалуйста, обновите страницу.", "PE.Controllers.Main.errorSessionToken": "Подключение к серверу было прервано. Пожалуйста, обновите страницу.", "PE.Controllers.Main.errorSetPassword": "Не удалось задать пароль.", "PE.Controllers.Main.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "PE.Controllers.Main.errorToken": "Токен безопасности документа имеет неправильный формат.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "PE.Controllers.Main.errorTokenExpire": "Истек срок действия токена безопасности документа.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "PE.Controllers.Main.errorUpdateVersion": "Версия файла была изменена. Страница будет перезагружена.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Соединение было восстановлено, и версия файла изменилась.<br>Прежде чем продолжить работу, надо скачать файл или скопировать его содержимое, чтобы обеспечить сохранность данных, а затем перезагрузить страницу.", "PE.Controllers.Main.errorUserDrop": "В настоящий момент файл недоступен.", "PE.Controllers.Main.errorUsersExceed": "Превышено количество пользователей, разрешенных согласно тарифному плану", "PE.Controllers.Main.errorViewerDisconnect": "Подключение прервано. Вы по-прежнему можете просматривать документ,<br>но не сможете скачать или напечатать его до восстановления подключения и обновления страницы.", "PE.Controllers.Main.leavePageText": "Презентация содержит несохраненные изменения. Чтобы сохранить их, нажмите \"Остаться на этой странице\", зат<PERSON>м \"Сохранить\". Нажмите \"Покинуть эту страницу\", чтобы сбросить все несохраненные изменения.", "PE.Controllers.Main.leavePageTextOnClose": "Все несохраненные изменения в этой презентации будут потеряны.<br> Нажмите кнопку \"Отмена\", а затем нажмите кнопку \"Сохранить\", чтобы сохранить их. Нажмите кнопку \"OK\", чтобы сбросить все несохраненные изменения.", "PE.Controllers.Main.loadFontsTextText": "Загрузка данных...", "PE.Controllers.Main.loadFontsTitleText": "Загрузка данных", "PE.Controllers.Main.loadFontTextText": "Загрузка данных...", "PE.Controllers.Main.loadFontTitleText": "Загрузка данных", "PE.Controllers.Main.loadImagesTextText": "Загрузка изображений...", "PE.Controllers.Main.loadImagesTitleText": "Загрузка изображений", "PE.Controllers.Main.loadImageTextText": "Загрузка изображения...", "PE.Controllers.Main.loadImageTitleText": "Загрузка изображения", "PE.Controllers.Main.loadingDocumentTextText": "Загрузка презентации...", "PE.Controllers.Main.loadingDocumentTitleText": "Загрузка презентации", "PE.Controllers.Main.loadThemeTextText": "Загрузка темы...", "PE.Controllers.Main.loadThemeTitleText": "Загрузка темы", "PE.Controllers.Main.notcriticalErrorTitle": "Предупреждение", "PE.Controllers.Main.openErrorText": "При открытии файла произошла ошибка.", "PE.Controllers.Main.openTextText": "Открытие презентации...", "PE.Controllers.Main.openTitleText": "Открытие презентации", "PE.Controllers.Main.printTextText": "Печать презентации...", "PE.Controllers.Main.printTitleText": "Печать презентации", "PE.Controllers.Main.reloadButtonText": "Обновить страницу", "PE.Controllers.Main.requestEditFailedMessageText": "В настоящее время презентация редактируется. Пожалуйста, повторите попытку позже.", "PE.Controllers.Main.requestEditFailedTitleText": "Доступ запрещён", "PE.Controllers.Main.saveErrorText": "При сохранении файла произошла ошибка.", "PE.Controllers.Main.saveErrorTextDesktop": "Нельзя сохранить или создать этот файл.<br>Возможные причины: <br>1. Файл доступен только для чтения. <br>2. Файл редактируется другими пользователями. <br>3. Диск заполнен или поврежден.", "PE.Controllers.Main.saveTextText": "Сохранение презентации...", "PE.Controllers.Main.saveTitleText": "Сохранение презентации", "PE.Controllers.Main.scriptLoadError": "Слишком медленное подключение, некоторые компоненты не удалось загрузить. Пожалуйста, обновите страницу.", "PE.Controllers.Main.splitDividerErrorText": "Число строк должно являться делителем для %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Число столбцов должно быть меньше, чем %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Число строк должно быть меньше, чем %1.", "PE.Controllers.Main.textAnonymous": "Аноним", "PE.Controllers.Main.textApplyAll": "Применить ко всем уравнениям", "PE.Controllers.Main.textBuyNow": "Перейти на сайт", "PE.Controllers.Main.textChangesSaved": "Все изменения сохранены", "PE.Controllers.Main.textClose": "Закрыть", "PE.Controllers.Main.textCloseTip": "Щелкните, чтобы закрыть эту подсказку", "PE.Controllers.Main.textContactUs": "Связаться с отделом продаж", "PE.Controllers.Main.textContinue": "Продолжить", "PE.Controllers.Main.textConvertEquation": "Это уравнение создано в старой версии редактора уравнений, которая больше не поддерживается. Чтобы изменить это уравнение, его необходимо преобразовать в формат Office Math ML.<br>Преобразовать сейчас?", "PE.Controllers.Main.textCustomLoader": "Обратите внимание, что по условиям лицензии у вас нет прав изменять экран, отображаемый при загрузке.<br>Пожалуйста, обратитесь в наш отдел продаж, чтобы сделать запрос.", "PE.Controllers.Main.textDisconnect": "Соединение потеряно", "PE.Controllers.Main.textGuest": "Гость", "PE.Controllers.Main.textHasMacros": "Файл содержит автозапускаемые макросы.<br>Хотите запустить макросы?", "PE.Controllers.Main.textLearnMore": "Подробнее", "PE.Controllers.Main.textLoadingDocument": "Загрузка презентации", "PE.Controllers.Main.textLongName": "Введите имя длиной менее 128 символов.", "PE.Controllers.Main.textNoLicenseTitle": "Лицензионное ограничение", "PE.Controllers.Main.textPaidFeature": "Платная функция", "PE.Controllers.Main.textReconnect": "Соединение восстановлено", "PE.Controllers.Main.textRemember": "Запомнить мой выбор для всех файлов", "PE.Controllers.Main.textRememberMacros": "Запомнить мой выбор для всех макросов", "PE.Controllers.Main.textRenameError": "Имя пользователя не должно быть пустым.", "PE.Controllers.Main.textRenameLabel": "Введите имя, которое будет использоваться для совместной работы", "PE.Controllers.Main.textRequestMacros": "Макрос делает запрос на URL. Вы хотите разрешить запрос на %1?", "PE.Controllers.Main.textShape": "Фигура", "PE.Controllers.Main.textStrict": "Строгий режим", "PE.Controllers.Main.textText": "Текст", "PE.Controllers.Main.textTryQuickPrint": "Вы выбрали быструю печать: весь документ будет напечатан на последнем выбранном принтере или на принтере по умолчанию.<br>Вы хотите продолжить?", "PE.Controllers.Main.textTryUndoRedo": "Функции отмены и повтора действий отключены в Быстром режиме совместного редактирования.<br>Нажмите на кнопку 'Строгий режим' для переключения в Строгий режим совместного редактирования, чтобы редактировать файл без вмешательства других пользователей и отправлять изменения только после того, как вы их сохраните. Переключаться между режимами совместного редактирования можно с помощью Дополнительных параметров редактора.", "PE.Controllers.Main.textTryUndoRedoWarn": "Функции отмены и повтора действий отключены в Быстром режиме совместного редактирования.", "PE.Controllers.Main.textUndo": "Отменить", "PE.Controllers.Main.titleLicenseExp": "Истек срок действия лицензии", "PE.Controllers.Main.titleServerVersion": "Редактор обновлен", "PE.Controllers.Main.txtAddFirstSlide": "Нажмите, чтобы добавить первый слайд", "PE.Controllers.Main.txtAddNotes": "Нажмите, чтобы добавить заметки", "PE.Controllers.Main.txtArt": "Введите ваш текст", "PE.Controllers.Main.txtBasicShapes": "Основные фигуры", "PE.Controllers.Main.txtButtons": "Кнопки", "PE.Controllers.Main.txtCallouts": "Выноски", "PE.Controllers.Main.txtCharts": "Схемы", "PE.Controllers.Main.txtClipArt": "Картинка", "PE.Controllers.Main.txtDateTime": "Дата и время", "PE.Controllers.Main.txtDiagram": "Рисунок SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Заголовок диаграммы", "PE.Controllers.Main.txtEditingMode": "Установка режима редактирования...", "PE.Controllers.Main.txtErrorLoadHistory": "Не удалось загрузить историю", "PE.Controllers.Main.txtFiguredArrows": "Фигурные стрелки", "PE.Controllers.Main.txtFooter": "Нижний колонтитул", "PE.Controllers.Main.txtHeader": "Верхний колонтитул", "PE.Controllers.Main.txtImage": "Образ слайда", "PE.Controllers.Main.txtLines": "Линии", "PE.Controllers.Main.txtLoading": "Загрузка...", "PE.Controllers.Main.txtMath": "Математические знаки", "PE.Controllers.Main.txtMedia": "Клип мультимедиа", "PE.Controllers.Main.txtNeedSynchronize": "Есть обновления", "PE.Controllers.Main.txtNone": "Нет", "PE.Controllers.Main.txtPicture": "Рисунок", "PE.Controllers.Main.txtRectangles": "Прямоугольники", "PE.Controllers.Main.txtSeries": "<PERSON>яд", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Выноска 1 (граница и черта)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Выноска 2 (граница и черта)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Выноска 3 (граница и черта)", "PE.Controllers.Main.txtShape_accentCallout1": "Выноска 1 (черта)", "PE.Controllers.Main.txtShape_accentCallout2": "Выноска 2 (черта)", "PE.Controllers.Main.txtShape_accentCallout3": "Выноска 3 (черта)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Кнопка \"Назад\"", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Кнопка \"В начало\"", "PE.Controllers.Main.txtShape_actionButtonBlank": "Пустая кнопка", "PE.Controllers.Main.txtShape_actionButtonDocument": "Кнопка документа", "PE.Controllers.Main.txtShape_actionButtonEnd": "Кнопка \"В конец\"", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Кнопка \"Вперед\"", "PE.Controllers.Main.txtShape_actionButtonHelp": "Кнопка \"Справка\"", "PE.Controllers.Main.txtShape_actionButtonHome": "Кнопка \"Домой\"", "PE.Controllers.Main.txtShape_actionButtonInformation": "Кнопка сведений", "PE.Controllers.Main.txtShape_actionButtonMovie": "Кнопка видео", "PE.Controllers.Main.txtShape_actionButtonReturn": "Кнопка возврата", "PE.Controllers.Main.txtShape_actionButtonSound": "Кнопка звука", "PE.Controllers.Main.txtShape_arc": "Дуга", "PE.Controllers.Main.txtShape_bentArrow": "Угловая стрелка", "PE.Controllers.Main.txtShape_bentConnector5": "Соединительная линия уступом", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Уступ со стрелкой", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Уступ с двумя стрелками", "PE.Controllers.Main.txtShape_bentUpArrow": "Угловая стрелка вверх", "PE.Controllers.Main.txtShape_bevel": "Багетная рамка", "PE.Controllers.Main.txtShape_blockArc": "Арка", "PE.Controllers.Main.txtShape_borderCallout1": "Выноска 1", "PE.Controllers.Main.txtShape_borderCallout2": "Выноска 2", "PE.Controllers.Main.txtShape_borderCallout3": "Выноска 3", "PE.Controllers.Main.txtShape_bracePair": "Двойные фигурные скобки", "PE.Controllers.Main.txtShape_callout1": "Выноска 1 (без границы)", "PE.Controllers.Main.txtShape_callout2": "Выноска 2 (без границы)", "PE.Controllers.Main.txtShape_callout3": "Выноска 3 (без границы)", "PE.Controllers.Main.txtShape_can": "Цилиндр", "PE.Controllers.Main.txtShape_chevron": "Шевр<PERSON>н", "PE.Controllers.Main.txtShape_chord": "Сегмент круга", "PE.Controllers.Main.txtShape_circularArrow": "Круговая стрелка", "PE.Controllers.Main.txtShape_cloud": "Облако", "PE.Controllers.Main.txtShape_cloudCallout": "Выноска-облако", "PE.Controllers.Main.txtShape_corner": "Угол", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Скругленная соединительная линия", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Скругленная линия со стрелкой", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Скругленная линия с двумя стрелками", "PE.Controllers.Main.txtShape_curvedDownArrow": "Выгнутая вверх стрелка", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Выгнутая вправо стрелка", "PE.Controllers.Main.txtShape_curvedRightArrow": "Выгнутая влево стрелка", "PE.Controllers.Main.txtShape_curvedUpArrow": "Выгнутая вниз стрелка", "PE.Controllers.Main.txtShape_decagon": "Десятиугольник", "PE.Controllers.Main.txtShape_diagStripe": "Диагональная полоса", "PE.Controllers.Main.txtShape_diamond": "Ромб", "PE.Controllers.Main.txtShape_dodecagon": "Двенадцатиугольник", "PE.Controllers.Main.txtShape_donut": "Кольцо", "PE.Controllers.Main.txtShape_doubleWave": "Двойная волна", "PE.Controllers.Main.txtShape_downArrow": "Стрелка вниз", "PE.Controllers.Main.txtShape_downArrowCallout": "Выноска со стрелкой вниз", "PE.Controllers.Main.txtShape_ellipse": "Элли<PERSON><PERSON>", "PE.Controllers.Main.txtShape_ellipseRibbon": "Круглая лента лицом вниз", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Круглая лента лицом вверх", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: альтернативный процесс", "PE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: сопоставление", "PE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: соединительная линия", "PE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: решение", "PE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: задержка", "PE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: дисплей", "PE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: документ", "PE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: извлечение", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: данные", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: внутренняя память", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: магнитный диск", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: память с прямым доступом", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: память с посл. доступом", "PE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: ручной ввод", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: ручное управление", "PE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: объединение", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: несколько документов", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: ссылка на другую страницу", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: сохраненные данные", "PE.Controllers.Main.txtShape_flowChartOr": "Блок-схема: ИЛИ", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: типовой процесс", "PE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: подготовка", "PE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: процесс", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: карточка", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: перфолента", "PE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: сортировка", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: узел суммирования", "PE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: знак завершения", "PE.Controllers.Main.txtShape_foldedCorner": "Загнутый угол", "PE.Controllers.Main.txtShape_frame": "Рамка", "PE.Controllers.Main.txtShape_halfFrame": "Половина рамки", "PE.Controllers.Main.txtShape_heart": "Сердце", "PE.Controllers.Main.txtShape_heptagon": "Семиугольник", "PE.Controllers.Main.txtShape_hexagon": "Шестиугольник", "PE.Controllers.Main.txtShape_homePlate": "Пятиугольник", "PE.Controllers.Main.txtShape_horizontalScroll": "Горизонтальный свиток", "PE.Controllers.Main.txtShape_irregularSeal1": "Вспышка 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Вспышка 2", "PE.Controllers.Main.txtShape_leftArrow": "Стрелка влево", "PE.Controllers.Main.txtShape_leftArrowCallout": "Выноска со стрелкой влево", "PE.Controllers.Main.txtShape_leftBrace": "Левая фигурная скобка", "PE.Controllers.Main.txtShape_leftBracket": "Левая круглая скобка", "PE.Controllers.Main.txtShape_leftRightArrow": "Двойная стрелка влево-вправо", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Выноска со стрелками влево-вправо", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Тройная стрелка влево-вправо-вверх", "PE.Controllers.Main.txtShape_leftUpArrow": "Двойная стрелка влево-вверх", "PE.Controllers.Main.txtShape_lightningBolt": "Молния", "PE.Controllers.Main.txtShape_line": "Линия", "PE.Controllers.Main.txtShape_lineWithArrow": "Стрелка", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Двойная стрелка", "PE.Controllers.Main.txtShape_mathDivide": "Деление", "PE.Controllers.Main.txtShape_mathEqual": "Равно", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Умножение", "PE.Controllers.Main.txtShape_mathNotEqual": "Не равно", "PE.Controllers.Main.txtShape_mathPlus": "Плюс", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "Запрещено", "PE.Controllers.Main.txtShape_notchedRightArrow": "Стрелка вправо с вырезом", "PE.Controllers.Main.txtShape_octagon": "Восьмиугольник", "PE.Controllers.Main.txtShape_parallelogram": "Параллелограмм", "PE.Controllers.Main.txtShape_pentagon": "Пятиугольник", "PE.Controllers.Main.txtShape_pie": "Сектор круга", "PE.Controllers.Main.txtShape_plaque": "Табличка", "PE.Controllers.Main.txtShape_plus": "Плюс", "PE.Controllers.Main.txtShape_polyline1": "Рисованная кривая", "PE.Controllers.Main.txtShape_polyline2": "Произвольная форма", "PE.Controllers.Main.txtShape_quadArrow": "Счетверенная стрелка", "PE.Controllers.Main.txtShape_quadArrowCallout": "Выноска с четырьмя стрелками", "PE.Controllers.Main.txtShape_rect": "Прямоугольник", "PE.Controllers.Main.txtShape_ribbon": "Лента лицом вниз", "PE.Controllers.Main.txtShape_ribbon2": "Лента лицом вверх", "PE.Controllers.Main.txtShape_rightArrow": "Стрелка вправо", "PE.Controllers.Main.txtShape_rightArrowCallout": "Выноска со стрелкой вправо", "PE.Controllers.Main.txtShape_rightBrace": "Правая фигурная скобка", "PE.Controllers.Main.txtShape_rightBracket": "Правая круглая скобка", "PE.Controllers.Main.txtShape_round1Rect": "Прямоугольник с одним скругленным углом", "PE.Controllers.Main.txtShape_round2DiagRect": "Прямоугольник с двумя скругленными противолежащими углами", "PE.Controllers.Main.txtShape_round2SameRect": "Прямоугольник с двумя скругленными соседними углами", "PE.Controllers.Main.txtShape_roundRect": "Прямоугольник со скругленными углами", "PE.Controllers.Main.txtShape_rtTriangle": "Прямоугольный треугольник", "PE.Controllers.Main.txtShape_smileyFace": "Улыбающееся лицо", "PE.Controllers.Main.txtShape_snip1Rect": "Прямоугольник с одним вырезанным углом", "PE.Controllers.Main.txtShape_snip2DiagRect": "Прямоугольник с двумя вырезанными противолежащими углами", "PE.Controllers.Main.txtShape_snip2SameRect": "Прямоугольник с двумя вырезанными соседними углами", "PE.Controllers.Main.txtShape_snipRoundRect": "Прямоугольник с одним вырезанным скругленным углом", "PE.Controllers.Main.txtShape_spline": "Кривая", "PE.Controllers.Main.txtShape_star10": "10-конечная звезда", "PE.Controllers.Main.txtShape_star12": "12-конечная звезда", "PE.Controllers.Main.txtShape_star16": "16-конечная звезда", "PE.Controllers.Main.txtShape_star24": "24-конечная звезда", "PE.Controllers.Main.txtShape_star32": "32-конечная звезда", "PE.Controllers.Main.txtShape_star4": "4-конечная звезда", "PE.Controllers.Main.txtShape_star5": "5-конечная звезда", "PE.Controllers.Main.txtShape_star6": "6-конечная звезда", "PE.Controllers.Main.txtShape_star7": "7-конечная звезда", "PE.Controllers.Main.txtShape_star8": "8-конечная звезда", "PE.Controllers.Main.txtShape_stripedRightArrow": "Штриховая стрелка вправо", "PE.Controllers.Main.txtShape_sun": "Солнце", "PE.Controllers.Main.txtShape_teardrop": "Капля", "PE.Controllers.Main.txtShape_textRect": "Надпись", "PE.Controllers.Main.txtShape_trapezoid": "Трапеция", "PE.Controllers.Main.txtShape_triangle": "Треугольник", "PE.Controllers.Main.txtShape_upArrow": "Стрелка вверх", "PE.Controllers.Main.txtShape_upArrowCallout": "Выноска со стрелкой вверх", "PE.Controllers.Main.txtShape_upDownArrow": "Двойная стрелка вверх-вниз", "PE.Controllers.Main.txtShape_uturnArrow": "Развернутая стрелка", "PE.Controllers.Main.txtShape_verticalScroll": "Вертикальный свиток", "PE.Controllers.Main.txtShape_wave": "Волна", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Овальная выноска", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Прямоугольная выноска", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Скругленная прямоугольная выноска", "PE.Controllers.Main.txtSldLtTBlank": "Пустой слайд", "PE.Controllers.Main.txtSldLtTChart": "Диаграмма", "PE.Controllers.Main.txtSldLtTChartAndTx": "Диаграмма и текст", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Графика и текст", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Графика и вертикальный текст", "PE.Controllers.Main.txtSldLtTCust": "Пользовательский слайд", "PE.Controllers.Main.txtSldLtTDgm": "Схема", "PE.Controllers.Main.txtSldLtTFourObj": "Четыре объекта", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Медиак<PERSON>ип и текст", "PE.Controllers.Main.txtSldLtTObj": "Заголовок и объект", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Объект и два объекта", "PE.Controllers.Main.txtSldLtTObjAndTx": "Объект и текст", "PE.Controllers.Main.txtSldLtTObjOnly": "Объект", "PE.Controllers.Main.txtSldLtTObjOverTx": "Объект над текстом", "PE.Controllers.Main.txtSldLtTObjTx": "Заголовок, объект и подпись", "PE.Controllers.Main.txtSldLtTPicTx": "Рисунок с подписью", "PE.Controllers.Main.txtSldLtTSecHead": "Заголовок раздела", "PE.Controllers.Main.txtSldLtTTbl": "Таблица", "PE.Controllers.Main.txtSldLtTTitle": "Титульный слайд", "PE.Controllers.Main.txtSldLtTTitleOnly": "Только заголовок", "PE.Controllers.Main.txtSldLtTTwoColTx": "Текст в две колонки", "PE.Controllers.Main.txtSldLtTTwoObj": "Два объекта", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Два объекта и объект", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Два объекта и текст", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Два объекта над текстом", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Два текста и два объекта", "PE.Controllers.Main.txtSldLtTTx": "Текст", "PE.Controllers.Main.txtSldLtTTxAndChart": "Текст и диаграмма", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Текст и графика", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Текст и медиаклип", "PE.Controllers.Main.txtSldLtTTxAndObj": "Текст и объект", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Текст и два объекта", "PE.Controllers.Main.txtSldLtTTxOverObj": "Текст над объектом", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Вертикальный заголовок и текст", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Вертикальный заголовок и текст над диаграммой", "PE.Controllers.Main.txtSldLtTVertTx": "Вертикальный текст", "PE.Controllers.Main.txtSlideNumber": "Номер слайда", "PE.Controllers.Main.txtSlideSubtitle": "Подзаголовок слайда", "PE.Controllers.Main.txtSlideText": "Текст слайда", "PE.Controllers.Main.txtSlideTitle": "Заголовок слайда", "PE.Controllers.Main.txtStarsRibbons": "Звезды и ленты", "PE.Controllers.Main.txtTheme_basic": "Базовая", "PE.Controllers.Main.txtTheme_blank": "Пустой слайд", "PE.Controllers.Main.txtTheme_classic": "Классическая", "PE.Controllers.Main.txtTheme_corner": "Угловая", "PE.Controllers.Main.txtTheme_dotted": "Точечная", "PE.Controllers.Main.txtTheme_green": "Зеленая", "PE.Controllers.Main.txtTheme_green_leaf": "Зеленый лист", "PE.Controllers.Main.txtTheme_lines": "Линии", "PE.Controllers.Main.txtTheme_office": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office_theme": "Тема Офис", "PE.Controllers.Main.txtTheme_official": "Официальная", "PE.Controllers.Main.txtTheme_pixel": "Пиксельная", "PE.Controllers.Main.txtTheme_safari": "Сафари", "PE.Controllers.Main.txtTheme_turtle": "Черепаха", "PE.Controllers.Main.txtXAxis": "Ось X", "PE.Controllers.Main.txtYAxis": "Ось Y", "PE.Controllers.Main.unknownErrorText": "Неизвестная ошибка.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Ваш браузер не поддерживается.", "PE.Controllers.Main.uploadImageExtMessage": "Неизвестный формат изображения.", "PE.Controllers.Main.uploadImageFileCountMessage": "Ни одного изображения не загружено.", "PE.Controllers.Main.uploadImageSizeMessage": "Слишком большое изображение. Максимальный размер - 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Загрузка изображения...", "PE.Controllers.Main.uploadImageTitleText": "Загрузка изображения", "PE.Controllers.Main.waitText": "Пожалуйста, подождите...", "PE.Controllers.Main.warnBrowserIE9": "В IE9 приложение имеет низкую производительность. Используйте IE10 или более позднюю версию.", "PE.Controllers.Main.warnBrowserZoom": "Текущее значение масштаба страницы в браузере поддерживается не полностью. Вернитесь к масштабу по умолчанию, нажав Ctrl+0", "PE.Controllers.Main.warnLicenseExceeded": "Вы достигли лимита на одновременные подключения к редакторам %1. Этот документ будет открыт только на просмотр.<br>Свяжитесь с администратором, чтобы узнать больше.", "PE.Controllers.Main.warnLicenseExp": "Истек срок действия лицензии.<br>Обновите лицензию, а затем обновите страницу.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Истек срок действия лицензии.<br>Нет доступа к функциональности редактирования документов.<br>Пожалуйста, обратитесь к администратору.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Необходимо обновить лицензию.<br>У вас ограниченный доступ к функциональности редактирования документов.<br>Пожалуйста, обратитесь к администратору, чтобы получить полный доступ", "PE.Controllers.Main.warnLicenseUsersExceeded": "Вы достигли лимита на количество пользователей редакторов %1.<br>Свяжитесь с администратором, чтобы узнать больше.", "PE.Controllers.Main.warnNoLicense": "Вы достигли лимита на одновременные подключения к редакторам %1. Этот документ будет открыт на просмотр.<br>Напишите в отдел продаж %1, чтобы обсудить индивидуальные условия лицензирования.", "PE.Controllers.Main.warnNoLicenseUsers": "Вы достигли лимита на одновременные подключения к редакторам %1.<br>Напишите в отдел продаж %1, чтобы обсудить индивидуальные условия лицензирования.", "PE.Controllers.Main.warnProcessRightsChange": "Вам было отказано в праве на редактирование этого файла.", "PE.Controllers.Print.txtPrintRangeInvalid": "Неправильный диапазон печати", "PE.Controllers.Print.txtPrintRangeSingleRange": "Введите или один номер слайда, или один диапазон слайдов (например, 5-12). Или вы можете выбрать печать в PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Внимание", "PE.Controllers.Search.textNoTextFound": "Искомые данные не найдены. Пожалуйста, измените параметры поиска.", "PE.Controllers.Search.textReplaceSkipped": "Замена выполнена. Пропущено вхождений - {0}.", "PE.Controllers.Search.textReplaceSuccess": "Поиск выполнен. Заменено совпадений: {0}", "PE.Controllers.Search.warnReplaceString": "{0} нельзя использовать как специальный символ в поле Заменить на.", "PE.Controllers.Statusbar.textDisconnect": "<b>Соединение потеряно</b><br>Попытка подключения. Проверьте настройки подключения.", "PE.Controllers.Statusbar.zoomText": "Мас<PERSON>т<PERSON>б {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Шрифт, который вы хотите сохранить, недоступен на этом устройстве.<br>Стиль текста будет отображаться с помощью одного из системных шрифтов. Сохраненный шрифт будет использоваться, когда он станет доступен.<br>Вы хотите продолжить?", "PE.Controllers.Toolbar.textAccent": "Диакритические знаки", "PE.Controllers.Toolbar.textBracket": "Скобки", "PE.Controllers.Toolbar.textEmptyImgUrl": "Необходимо указать URL изображения.", "PE.Controllers.Toolbar.textFontSizeErr": "Введенное значение некорректно.<br>Введите числовое значение от 1 до 300", "PE.Controllers.Toolbar.textFraction": "Дроби", "PE.Controllers.Toolbar.textFunction": "Функции", "PE.Controllers.Toolbar.textInsert": "Вставить", "PE.Controllers.Toolbar.textIntegral": "Интегралы", "PE.Controllers.Toolbar.textLargeOperator": "Крупные операторы", "PE.Controllers.Toolbar.textLimitAndLog": "Пределы и логарифмы", "PE.Controllers.Toolbar.textMatrix": "Матрицы", "PE.Controllers.Toolbar.textOperator": "Операторы", "PE.Controllers.Toolbar.textRadical": "Радикалы", "PE.Controllers.Toolbar.textScript": "Индексы", "PE.Controllers.Toolbar.textSymbols": "Символы", "PE.Controllers.Toolbar.textWarning": "Предупреждение", "PE.Controllers.Toolbar.txtAccent_Accent": "Ударение", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Стрелка вправо-влево сверху", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Стрелка влево сверху", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Стрелка вправо сверху", "PE.Controllers.Toolbar.txtAccent_Bar": "Черта", "PE.Controllers.Toolbar.txtAccent_BarBot": "Черта снизу", "PE.Controllers.Toolbar.txtAccent_BarTop": "Черта сверху", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Формула в рамке (с заполнителем)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Формула в рамке (пример)", "PE.Controllers.Toolbar.txtAccent_Check": "Галочка", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Фигурная скобка снизу", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Фигурная скобка сверху", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Вектор A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC с чертой сверху", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y с чертой сверху", "PE.Controllers.Toolbar.txtAccent_DDDot": "Три точки", "PE.Controllers.Toolbar.txtAccent_DDot": "Две точки", "PE.Controllers.Toolbar.txtAccent_Dot": "Точка", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Двойная черта сверху", "PE.Controllers.Toolbar.txtAccent_Grave": "Тупое ударение", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Группир<PERSON><PERSON><PERSON>ий знак снизу", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Группир<PERSON>ю<PERSON>ий знак сверху", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Гар<PERSON>ун влево сверху", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Гарпун вправо сверху", "PE.Controllers.Toolbar.txtAccent_Hat": "Крышка", "PE.Controllers.Toolbar.txtAccent_Smile": "Значок краткости", "PE.Controllers.Toolbar.txtAccent_Tilde": "Тильда", "PE.Controllers.Toolbar.txtBracket_Angle": "Угловые скобки", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Угловые скобки с разделителем", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Угловые скобки с двумя разделителями", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Правая угловая скобка", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Левая угловая скобка", "PE.Controllers.Toolbar.txtBracket_Curve": "Фигурные скобки", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Фигурные скобки с разделителем", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Правая фигурная скобка", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Левая фигурная скобка", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Наборы условий (два условия)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Наборы условий (три условия)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Стопка объектов", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Стопка объектов в круглых скобках", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Наборы условий (пример)", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Биномиальный коэффициент", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Биномиальный коэффициент в угловых скобках", "PE.Controllers.Toolbar.txtBracket_Line": "Вертикальные черты", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Правая вертикальная черта", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Левая вертикальная черта", "PE.Controllers.Toolbar.txtBracket_LineDouble": "Двойные вертикальные черты", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Правая двойная вертикальная черта", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Левая двойная вертикальная черта", "PE.Controllers.Toolbar.txtBracket_LowLim": "Закрытые снизу скобки", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Правый предельный уровень снизу", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Левый предельный уровень снизу", "PE.Controllers.Toolbar.txtBracket_Round": "Круглые скобки", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Круглые скобки с разделителем", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Правая круглая скобка", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Левая круглая скобка", "PE.Controllers.Toolbar.txtBracket_Square": "Квадратные скобки", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Заполнитель между двумя правыми квадратными скобками", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Перевернутые квадратные скобки", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Правая квадратная скобка", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Левая квадратная скобка", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Заполнитель между двумя левыми квадратными скобками", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Двойные квадратные скобки", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Правая двойная квадратная скобка", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Левая двойная квадратная скобка", "PE.Controllers.Toolbar.txtBracket_UppLim": "Закрытые сверху скобки", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Правый предельный уровень сверху", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Левый предельный уровень сверху", "PE.Controllers.Toolbar.txtFractionDiagonal": "Диагональная простая дробь", "PE.Controllers.Toolbar.txtFractionDifferential_1": "dy над dx", "PE.Controllers.Toolbar.txtFractionDifferential_2": "пересечение дельты y над пересечением дельты x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "частичная y по частичной x", "PE.Controllers.Toolbar.txtFractionDifferential_4": "дельта y через дельта x", "PE.Controllers.Toolbar.txtFractionHorizontal": "Горизонтальная простая дробь", "PE.Controllers.Toolbar.txtFractionPi_2": "Пи разделить на два", "PE.Controllers.Toolbar.txtFractionSmall": "Маленькая простая дробь", "PE.Controllers.Toolbar.txtFractionVertical": "Вертикальная простая дробь", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Арккосинус", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Гиперболический арккосинус", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Арккотангенс", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Гиперболический арккотангенс", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Арккосеканс", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Гиперболический арккосеканс", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Арксеканс", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Гиперболический арксеканс", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Аркси<PERSON><PERSON>с", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Гиперболический арксинус", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Арк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Гиперболический арктангенс", "PE.Controllers.Toolbar.txtFunction_Cos": "Ко<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cosh": "Гиперболический косинус", "PE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Coth": "Гиперболический котангенс", "PE.Controllers.Toolbar.txtFunction_Csc": "Косеканс", "PE.Controllers.Toolbar.txtFunction_Csch": "Гиперболический косеканс", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Формула тангенса", "PE.Controllers.Toolbar.txtFunction_Sec": "Секанс", "PE.Controllers.Toolbar.txtFunction_Sech": "Гиперболический секанс", "PE.Controllers.Toolbar.txtFunction_Sin": "Син<PERSON>с", "PE.Controllers.Toolbar.txtFunction_Sinh": "Гиперболический синус", "PE.Controllers.Toolbar.txtFunction_Tan": "Та<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Tanh": "Гиперболический тангенс", "PE.Controllers.Toolbar.txtIntegral": "Интеграл", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Дифференциал dθ", "PE.Controllers.Toolbar.txtIntegral_dx": "Дифференциал dx", "PE.Controllers.Toolbar.txtIntegral_dy": "Ди<PERSON><PERSON>еренциал dy", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Интеграл с пределами с накоплением", "PE.Controllers.Toolbar.txtIntegralDouble": "Двойной интеграл", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Двойной интеграл с пределами с накоплением", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Двойной интеграл с пределами", "PE.Controllers.Toolbar.txtIntegralOriented": "Контурный интеграл", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурный интеграл с пределами с накоплением", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Интеграл по поверхности", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Интеграл по поверхности с пределами с накоплением", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Интеграл по поверхности с пределами", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурный интеграл с пределами", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Интеграл по объему", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Интеграл по объему с пределами с накоплением", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Интеграл по объему с пределами", "PE.Controllers.Toolbar.txtIntegralSubSup": "Интеграл с пределами", "PE.Controllers.Toolbar.txtIntegralTriple": "Тройной интеграл", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Тройной интеграл с пределами с накоплением", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Тройной интеграл с пределами", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Логическое И", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Логическое И с нижним пределом", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Логическое И с пределами", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Логическое И с нижним пределом подстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Логическое И с пределом подстрочного/надстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Сопроизведение", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Сопроизведение с нижним пределом", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Сопроизведение с пределами", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Сопроизведение с нижним пределом подстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Сопроизведение с пределами подстрочного/надстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Суммирование от k от n с выбором k", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Суммирование от i равно ноль до n", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Пример суммирования с использованием двух индексов", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Пример произведения", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Пример объединения", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Логическое Или", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Логическое ИЛИ с нижним пределом", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Логическое ИЛИ с пределами", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Логическое ИЛИ с нижним пределом подстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Логическое ИЛИ с пределами подстрочного/надстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Пересечение", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Пересечение с нижним пределом", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Пересечение с пределами", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Пересечение с нижним пределом в виде подстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Пересечение с пределами подстрочного/надстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Произведение", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Произведение с нижним пределом", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Произведение с пределами", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Произведение с нижним пределом подстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Произведение с пределами подстрочного/надстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Сумма", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Суммирование с нижним пределом", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Суммирование с пределами", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Суммирование с нижним пределом подстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Суммирование с пределами подстрочного/надстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Объединение", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Объединение с нижним пределом", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Объединение с пределами", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Объединение с нижним пределом подстрочного знака", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Объединение с пределами подстрочного/надстрочного знака", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Пример предела", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Пример максимума", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Предел", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Натуральный логарифм", "PE.Controllers.Toolbar.txtLimitLog_Log": "Лог<PERSON><PERSON><PERSON><PERSON>м", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Лог<PERSON><PERSON><PERSON><PERSON>м", "PE.Controllers.Toolbar.txtLimitLog_Max": "Максимум", "PE.Controllers.Toolbar.txtLimitLog_Min": "Мини<PERSON>ум", "PE.Controllers.Toolbar.txtMatrix_1_2": "Пустая матрица 1 x 2", "PE.Controllers.Toolbar.txtMatrix_1_3": "Пустая матрица 1 x 3", "PE.Controllers.Toolbar.txtMatrix_2_1": "Пустая матрица 2 x 1", "PE.Controllers.Toolbar.txtMatrix_2_2": "Пустая матрица 2 x 2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Пустая матрица 2 х 2 в двойных вертикальных чертах", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Пустой определитель 2 x 2", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Пустая матрица 2 х 2 в круглых скобках", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Пустая матрица 2 х 2 в скобках", "PE.Controllers.Toolbar.txtMatrix_2_3": "Пустая матрица 2 x 3", "PE.Controllers.Toolbar.txtMatrix_3_1": "Пустая матрица 3 x 1", "PE.Controllers.Toolbar.txtMatrix_3_2": "Пустая матрица 3 x 2", "PE.Controllers.Toolbar.txtMatrix_3_3": "Пустая матрица 3 x 3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Точки на опорной линии", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Точки посередине", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Точки по диагонали", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Точки по вертикали", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Разреженная матрица в круглых скобках", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Разреженная матрица в квадратных скобках", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "Единичная матрица 2 x 2 с нулями", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Единичная матрица 2 x 2 с пустыми ячейками не на диагонали", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Единичная матрица 3 x 3 с нулями", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Единичная матрица 3 x 3 с пустыми ячейками не на диагонали", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Стрелка вправо-влево снизу", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрелка вправо-влево сверху", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрелка влево снизу", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрелка влево сверху", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Стрелка вправо снизу", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрелка вправо сверху", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Двоеточие равно", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Выход", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Дельта выхода", "PE.Controllers.Toolbar.txtOperator_Definition": "Равно по определению", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Дельта равна", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Двойная стрелка вправо-влево снизу", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Двойная стрелка вправо-влево сверху", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрелка влево снизу", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрелка влево сверху", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Стрелка вправо снизу", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрелка вправо сверху", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Равно равно", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Минус равно", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс равно", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Единица измерения", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Правая часть квадратного уравнения", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Квадратный корень из квадрата плюс b квадрат", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Квадратный корень со степенью", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Кубический корень", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Радикал со степенью", "PE.Controllers.Toolbar.txtRadicalSqrt": "Квадратный корень", "PE.Controllers.Toolbar.txtScriptCustom_1": "x в степени квадрата y", "PE.Controllers.Toolbar.txtScriptCustom_2": "e в степени -iωt", "PE.Controllers.Toolbar.txtScriptCustom_3": "квадрат x", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y, надстрочный индекс n слева, подстрочный индекс 1 справа", "PE.Controllers.Toolbar.txtScriptSub": "Нижний индекс", "PE.Controllers.Toolbar.txtScriptSubSup": "Нижний и верхний индексы", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Нижний и верхний индексы слева", "PE.Controllers.Toolbar.txtScriptSup": "Верхний индекс", "PE.Controllers.Toolbar.txtSymbol_about": "Приблизительно", "PE.Controllers.Toolbar.txtSymbol_additional": "Дополнение", "PE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_alpha": "Альфа", "PE.Controllers.Toolbar.txtSymbol_approx": "Почти равно", "PE.Controllers.Toolbar.txtSymbol_ast": "Оператор-звездочка", "PE.Controllers.Toolbar.txtSymbol_beta": "Бета", "PE.Controllers.Toolbar.txtSymbol_beth": "Бет", "PE.Controllers.Toolbar.txtSymbol_bullet": "Оператор-маркер", "PE.Controllers.Toolbar.txtSymbol_cap": "Пересечение", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Кубический корень", "PE.Controllers.Toolbar.txtSymbol_cdots": "Горизонтальное многоточие посередине", "PE.Controllers.Toolbar.txtSymbol_celsius": "Градусы Цельсия", "PE.Controllers.Toolbar.txtSymbol_chi": "Хи", "PE.Controllers.Toolbar.txtSymbol_cong": "Приблизительно равно", "PE.Controllers.Toolbar.txtSymbol_cup": "Объединение", "PE.Controllers.Toolbar.txtSymbol_ddots": "Диагональное многоточие вниз вправо", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>ра<PERSON><PERSON><PERSON>ы", "PE.Controllers.Toolbar.txtSymbol_delta": "Дельта", "PE.Controllers.Toolbar.txtSymbol_div": "Знак деления", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Стрелка вниз", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Пустое множество", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Эп<PERSON>илон", "PE.Controllers.Toolbar.txtSymbol_equals": "Равно", "PE.Controllers.Toolbar.txtSymbol_equiv": "Тождественно", "PE.Controllers.Toolbar.txtSymbol_eta": "Эта", "PE.Controllers.Toolbar.txtSymbol_exists": "Существует", "PE.Controllers.Toolbar.txtSymbol_factorial": "Факториал", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градусы Фаренгейта", "PE.Controllers.Toolbar.txtSymbol_forall": "Для всех", "PE.Controllers.Toolbar.txtSymbol_gamma": "Гамма", "PE.Controllers.Toolbar.txtSymbol_geq": "Больше или равно", "PE.Controllers.Toolbar.txtSymbol_gg": "Много больше", "PE.Controllers.Toolbar.txtSymbol_greater": "Больше", "PE.Controllers.Toolbar.txtSymbol_in": "Является элементом", "PE.Controllers.Toolbar.txtSymbol_inc": "Приращение", "PE.Controllers.Toolbar.txtSymbol_infinity": "Бесконечность", "PE.Controllers.Toolbar.txtSymbol_iota": "Йота", "PE.Controllers.Toolbar.txtSymbol_kappa": "Каппа", "PE.Controllers.Toolbar.txtSymbol_lambda": "Лямбда", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Стрелка влево", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрелка влево и вправо", "PE.Controllers.Toolbar.txtSymbol_leq": "Меньше или равно", "PE.Controllers.Toolbar.txtSymbol_less": "Меньше", "PE.Controllers.Toolbar.txtSymbol_ll": "Много меньше", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Минус и плюс", "PE.Controllers.Toolbar.txtSymbol_mu": "Мю", "PE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "PE.Controllers.Toolbar.txtSymbol_neq": "Не равно", "PE.Controllers.Toolbar.txtSymbol_ni": "Содер<PERSON><PERSON>т как член", "PE.Controllers.Toolbar.txtSymbol_not": "Знак отрицания", "PE.Controllers.Toolbar.txtSymbol_notexists": "Не существует", "PE.Controllers.Toolbar.txtSymbol_nu": "Ню", "PE.Controllers.Toolbar.txtSymbol_o": "Омикрон", "PE.Controllers.Toolbar.txtSymbol_omega": "Омега", "PE.Controllers.Toolbar.txtSymbol_partial": "Частный дифференциал", "PE.Controllers.Toolbar.txtSymbol_percent": "Процент", "PE.Controllers.Toolbar.txtSymbol_phi": "Фи", "PE.Controllers.Toolbar.txtSymbol_pi": "Пи", "PE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "PE.Controllers.Toolbar.txtSymbol_pm": "Плюс и минус", "PE.Controllers.Toolbar.txtSymbol_propto": "Пропорционально", "PE.Controllers.Toolbar.txtSymbol_psi": "Пси", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Корень четвертой степени", "PE.Controllers.Toolbar.txtSymbol_qed": "Что и требовалось доказать", "PE.Controllers.Toolbar.txtSymbol_rddots": "Диагональное многоточие вверх вправо", "PE.Controllers.Toolbar.txtSymbol_rho": "Ро", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Стрелка вправо", "PE.Controllers.Toolbar.txtSymbol_sigma": "Сигма", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Знак радикала", "PE.Controllers.Toolbar.txtSymbol_tau": "Тау", "PE.Controllers.Toolbar.txtSymbol_therefore": "Следовательно", "PE.Controllers.Toolbar.txtSymbol_theta": "Тета", "PE.Controllers.Toolbar.txtSymbol_times": "Знак умножения", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Стрелка вверх", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ипсилон", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (вариант)", "PE.Controllers.Toolbar.txtSymbol_varphi": "Фи (вариант)", "PE.Controllers.Toolbar.txtSymbol_varpi": "Пи (вариант)", "PE.Controllers.Toolbar.txtSymbol_varrho": "Ро (вариант)", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Сигма (вариант)", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Тета (вариант)", "PE.Controllers.Toolbar.txtSymbol_vdots": "Вертикальное многоточие", "PE.Controllers.Toolbar.txtSymbol_xsi": "Кси", "PE.Controllers.Toolbar.txtSymbol_zeta": "Дзета", "PE.Controllers.Viewport.textFitPage": "По размеру слайда", "PE.Controllers.Viewport.textFitWidth": "По ширине", "PE.Views.Animation.str0_5": "0.5 с (очень быстро)", "PE.Views.Animation.str1": "1 s (быстро)", "PE.Views.Animation.str2": "2 c (средне)", "PE.Views.Animation.str20": "20 с (крайне медленно)", "PE.Views.Animation.str3": "3 с (медленно)", "PE.Views.Animation.str5": "5 c (очень медленно)", "PE.Views.Animation.strDelay": "Задержка", "PE.Views.Animation.strDuration": "<PERSON>лит.", "PE.Views.Animation.strRepeat": "Повтор", "PE.Views.Animation.strRewind": "Перемотка назад", "PE.Views.Animation.strStart": "Запуск", "PE.Views.Animation.strTrigger": "Триггер", "PE.Views.Animation.textAutoPreview": "Автопросмотр", "PE.Views.Animation.textMoreEffects": "Показать больше эффектов", "PE.Views.Animation.textMoveEarlier": "Переместить назад", "PE.Views.Animation.textMoveLater": "Переместить вперед", "PE.Views.Animation.textMultiple": "Несколько", "PE.Views.Animation.textNone": "Нет", "PE.Views.Animation.textNoRepeat": "(нет)", "PE.Views.Animation.textOnClickOf": "По щелчку на", "PE.Views.Animation.textOnClickSequence": "По последовательности щелчков", "PE.Views.Animation.textStartAfterPrevious": "После предыдущего", "PE.Views.Animation.textStartOnClick": "По щелчку", "PE.Views.Animation.textStartWithPrevious": "Вместе с предыдущим", "PE.Views.Animation.textUntilEndOfSlide": "До окончания слайда", "PE.Views.Animation.textUntilNextClick": "До следующего щелчка", "PE.Views.Animation.txtAddEffect": "Добавить анимацию", "PE.Views.Animation.txtAnimationPane": "Область анимации", "PE.Views.Animation.txtParameters": "Параметры", "PE.Views.Animation.txtPreview": "Просмотр", "PE.Views.Animation.txtSec": "сек", "PE.Views.AnimationDialog.textPreviewEffect": "Просмотр эффекта", "PE.Views.AnimationDialog.textTitle": "Другие эффекты", "PE.Views.ChartSettings.text3dDepth": "Глубина (% от базовой)", "PE.Views.ChartSettings.text3dHeight": "Высота (% от базовой)", "PE.Views.ChartSettings.text3dRotation": "Трехмерный поворот", "PE.Views.ChartSettings.textAdvanced": "Дополнительные параметры", "PE.Views.ChartSettings.textAutoscale": "Автомасштабирование", "PE.Views.ChartSettings.textChartType": "Изменить тип диаграммы", "PE.Views.ChartSettings.textDefault": "Поворот по умолчанию", "PE.Views.ChartSettings.textDown": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textEditData": "Изменить данные", "PE.Views.ChartSettings.textHeight": "Высота", "PE.Views.ChartSettings.textKeepRatio": "Сохранять пропорции", "PE.Views.ChartSettings.textLeft": "Влево", "PE.Views.ChartSettings.textNarrow": "Сузить поле зрения", "PE.Views.ChartSettings.textPerspective": "Перспектива", "PE.Views.ChartSettings.textRight": "Вправо", "PE.Views.ChartSettings.textRightAngle": "Оси под прямым углом", "PE.Views.ChartSettings.textSize": "Размер", "PE.Views.ChartSettings.textStyle": "Стиль", "PE.Views.ChartSettings.textUp": "Ввер<PERSON>", "PE.Views.ChartSettings.textWiden": "Расширить поле зрения", "PE.Views.ChartSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textX": "По оси X", "PE.Views.ChartSettings.textY": "По оси Y", "PE.Views.ChartSettingsAdvanced.textAlt": "Альтернативный текст", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Описание", "PE.Views.ChartSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, автофигура, диаграмма или таблица.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Заголовок", "PE.Views.ChartSettingsAdvanced.textCenter": "По центру", "PE.Views.ChartSettingsAdvanced.textFrom": "От", "PE.Views.ChartSettingsAdvanced.textHeight": "Высота", "PE.Views.ChartSettingsAdvanced.textHorizontal": "По горизонтали", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Сохранять пропорции", "PE.Views.ChartSettingsAdvanced.textPlacement": "Положение", "PE.Views.ChartSettingsAdvanced.textPosition": "Позиция", "PE.Views.ChartSettingsAdvanced.textSize": "Размер", "PE.Views.ChartSettingsAdvanced.textTitle": "Диаграмма - дополнительные параметры", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Верхний левый угол", "PE.Views.ChartSettingsAdvanced.textVertical": "По вертикали", "PE.Views.ChartSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Задать формат по умолчанию для {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Установить по умолчанию", "PE.Views.DateTimeDialog.textFormat": "Форматы", "PE.Views.DateTimeDialog.textLang": "Язык", "PE.Views.DateTimeDialog.textUpdate": "Обновлять автоматически", "PE.Views.DateTimeDialog.txtTitle": "Дата и время", "PE.Views.DocumentHolder.aboveText": "Выше", "PE.Views.DocumentHolder.addCommentText": "Добавить комментарий", "PE.Views.DocumentHolder.addToLayoutText": "Добавить в макет", "PE.Views.DocumentHolder.advancedChartText": "Дополнительные параметры диаграммы", "PE.Views.DocumentHolder.advancedEquationText": "Параметры уравнений", "PE.Views.DocumentHolder.advancedImageText": "Дополнительные параметры изображения", "PE.Views.DocumentHolder.advancedParagraphText": "Дополнительные параметры абзаца", "PE.Views.DocumentHolder.advancedShapeText": "Дополнительные параметры фигуры", "PE.Views.DocumentHolder.advancedTableText": "Дополнительные параметры таблицы", "PE.Views.DocumentHolder.alignmentText": "Выравнивание", "PE.Views.DocumentHolder.allLinearText": "Все - линейный", "PE.Views.DocumentHolder.allProfText": "Все - профессиональный", "PE.Views.DocumentHolder.belowText": "Ниже", "PE.Views.DocumentHolder.cellAlignText": "Вертикальное выравнивание в ячейках", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "По центру", "PE.Views.DocumentHolder.columnText": "Столбец", "PE.Views.DocumentHolder.currLinearText": "Текущее - линейный", "PE.Views.DocumentHolder.currProfText": "Текущее - профессиональный", "PE.Views.DocumentHolder.deleteColumnText": "Удалить столбец", "PE.Views.DocumentHolder.deleteRowText": "Удалить строку", "PE.Views.DocumentHolder.deleteTableText": "Удалить таблицу", "PE.Views.DocumentHolder.deleteText": "Удалить", "PE.Views.DocumentHolder.direct270Text": "Повернуть текст вверх", "PE.Views.DocumentHolder.direct90Text": "Повернуть текст вниз", "PE.Views.DocumentHolder.directHText": "Горизонтальное", "PE.Views.DocumentHolder.directionText": "Направление текста", "PE.Views.DocumentHolder.editChartText": "Изменить данные", "PE.Views.DocumentHolder.editHyperlinkText": "Изменить гиперссылку", "PE.Views.DocumentHolder.hyperlinkText": "Гиперссылка", "PE.Views.DocumentHolder.ignoreAllSpellText": "Пропустить все", "PE.Views.DocumentHolder.ignoreSpellText": "Пропустить", "PE.Views.DocumentHolder.insertColumnLeftText": "Столбец слева", "PE.Views.DocumentHolder.insertColumnRightText": "Столбец справа", "PE.Views.DocumentHolder.insertColumnText": "Вставить столбец", "PE.Views.DocumentHolder.insertRowAboveText": "Строку выше", "PE.Views.DocumentHolder.insertRowBelowText": "Строку ниже", "PE.Views.DocumentHolder.insertRowText": "Вставить строку", "PE.Views.DocumentHolder.insertText": "Добавить", "PE.Views.DocumentHolder.langText": "Выбрать язык", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "По левому краю", "PE.Views.DocumentHolder.loadSpellText": "Загрузка вариантов...", "PE.Views.DocumentHolder.mergeCellsText": "Объединить ячейки", "PE.Views.DocumentHolder.mniCustomTable": "Пользовательская таблица", "PE.Views.DocumentHolder.moreText": "Больше вариантов...", "PE.Views.DocumentHolder.noSpellVariantsText": "Нет вариа<PERSON><PERSON>ов", "PE.Views.DocumentHolder.originalSizeText": "Реальный размер", "PE.Views.DocumentHolder.removeHyperlinkText": "Удалить гиперссылку", "PE.Views.DocumentHolder.rightText": "По правому краю", "PE.Views.DocumentHolder.rowText": "Строку", "PE.Views.DocumentHolder.selectText": "Выделить", "PE.Views.DocumentHolder.spellcheckText": "Проверка орфографии", "PE.Views.DocumentHolder.splitCellsText": "Разделить ячейку...", "PE.Views.DocumentHolder.splitCellTitleText": "Разделить ячейку", "PE.Views.DocumentHolder.tableText": "Табли<PERSON>у", "PE.Views.DocumentHolder.textAddHGuides": "Добавить горизонтальную направляющую", "PE.Views.DocumentHolder.textAddVGuides": "Добавить вертикальную направляющую", "PE.Views.DocumentHolder.textArrangeBack": "Перенести на задний план", "PE.Views.DocumentHolder.textArrangeBackward": "Перенести назад", "PE.Views.DocumentHolder.textArrangeForward": "Перенести вперед", "PE.Views.DocumentHolder.textArrangeFront": "Перенести на передний план", "PE.Views.DocumentHolder.textClearGuides": "Удалить направляющие", "PE.Views.DocumentHolder.textCm": "см", "PE.Views.DocumentHolder.textCopy": "Копировать", "PE.Views.DocumentHolder.textCrop": "Обрезать", "PE.Views.DocumentHolder.textCropFill": "Заливка", "PE.Views.DocumentHolder.textCropFit": "Вписать", "PE.Views.DocumentHolder.textCustom": "Пользовательские", "PE.Views.DocumentHolder.textCut": "Вырезать", "PE.Views.DocumentHolder.textDeleteGuide": "Удалить направляющую", "PE.Views.DocumentHolder.textDistributeCols": "Выровнять ширину столбцов", "PE.Views.DocumentHolder.textDistributeRows": "Выровнять высоту строк", "PE.Views.DocumentHolder.textEditPoints": "Изменить точки", "PE.Views.DocumentHolder.textFlipH": "Отразить слева направо", "PE.Views.DocumentHolder.textFlipV": "Отразить сверху вниз", "PE.Views.DocumentHolder.textFromFile": "Из файла", "PE.Views.DocumentHolder.textFromStorage": "Из хранилища", "PE.Views.DocumentHolder.textFromUrl": "По URL", "PE.Views.DocumentHolder.textGridlines": "Линии сетки", "PE.Views.DocumentHolder.textGuides": "Направляющие", "PE.Views.DocumentHolder.textNextPage": "Следующий слайд", "PE.Views.DocumentHolder.textPaste": "Вставить", "PE.Views.DocumentHolder.textPrevPage": "Предыдущий слайд", "PE.Views.DocumentHolder.textReplace": "Заменить изображение", "PE.Views.DocumentHolder.textRotate": "Поворот", "PE.Views.DocumentHolder.textRotate270": "Повернуть на 90° против часовой стрелки", "PE.Views.DocumentHolder.textRotate90": "Повернуть на 90° по часовой стрелке", "PE.Views.DocumentHolder.textRulers": "Линейки", "PE.Views.DocumentHolder.textSaveAsPicture": "Сохранить как рисунок", "PE.Views.DocumentHolder.textShapeAlignBottom": "Выровнять по нижнему краю", "PE.Views.DocumentHolder.textShapeAlignCenter": "Выровнять по центру", "PE.Views.DocumentHolder.textShapeAlignLeft": "Выровнять по левому краю", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Выровнять по середине", "PE.Views.DocumentHolder.textShapeAlignRight": "Выровнять по правому краю", "PE.Views.DocumentHolder.textShapeAlignTop": "Выровнять по верхнему краю", "PE.Views.DocumentHolder.textShowGridlines": "Показать линии сетки", "PE.Views.DocumentHolder.textShowGuides": "Показать направляющие", "PE.Views.DocumentHolder.textSlideSettings": "Параметры слайда", "PE.Views.DocumentHolder.textSmartGuides": "Смарт-направляющие", "PE.Views.DocumentHolder.textSnapObjects": "Привязать объект к сетке", "PE.Views.DocumentHolder.textUndo": "Отменить", "PE.Views.DocumentHolder.tipGuides": "Показать направляющие", "PE.Views.DocumentHolder.tipIsLocked": "Этот элемент редактируется другим пользователем.", "PE.Views.DocumentHolder.toDictionaryText": "Добавить в словарь", "PE.Views.DocumentHolder.txtAddBottom": "Добавить нижнюю границу", "PE.Views.DocumentHolder.txtAddFractionBar": "Добавить дробную черту", "PE.Views.DocumentHolder.txtAddHor": "Добавить горизонтальную линию", "PE.Views.DocumentHolder.txtAddLB": "Добавить линию из левого нижнего угла", "PE.Views.DocumentHolder.txtAddLeft": "Добавить левую границу", "PE.Views.DocumentHolder.txtAddLT": "Добавить линию из левого верхнего угла", "PE.Views.DocumentHolder.txtAddRight": "Добавить правую границу", "PE.Views.DocumentHolder.txtAddTop": "Добавить верхнюю границу", "PE.Views.DocumentHolder.txtAddVer": "Добавить вертикальную линию", "PE.Views.DocumentHolder.txtAlign": "Выравнивание", "PE.Views.DocumentHolder.txtAlignToChar": "Выравнивание по символу", "PE.Views.DocumentHolder.txtArrange": "Порядок", "PE.Views.DocumentHolder.txtBackground": "Фон", "PE.Views.DocumentHolder.txtBorderProps": "Свойства границ", "PE.Views.DocumentHolder.txtBottom": "По нижнему краю", "PE.Views.DocumentHolder.txtChangeLayout": "Изменить макет", "PE.Views.DocumentHolder.txtChangeTheme": "Изменить тему", "PE.Views.DocumentHolder.txtColumnAlign": "Выравнивание столбца", "PE.Views.DocumentHolder.txtDecreaseArg": "Уменьшить размер аргумента", "PE.Views.DocumentHolder.txtDeleteArg": "Удалить аргумент", "PE.Views.DocumentHolder.txtDeleteBreak": "Удалить принудительный разрыв", "PE.Views.DocumentHolder.txtDeleteChars": "Удалить вложенные знаки", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Удалить вложенные знаки и разделители", "PE.Views.DocumentHolder.txtDeleteEq": "Удалить уравнение", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Удалить символ", "PE.Views.DocumentHolder.txtDeleteRadical": "Удалить радикал", "PE.Views.DocumentHolder.txtDeleteSlide": "Удалить слайд", "PE.Views.DocumentHolder.txtDistribHor": "Распределить по горизонтали", "PE.Views.DocumentHolder.txtDistribVert": "Распределить по вертикали", "PE.Views.DocumentHolder.txtDuplicateSlide": "Дублировать слайд", "PE.Views.DocumentHolder.txtFractionLinear": "Изменить на горизонтальную простую дробь", "PE.Views.DocumentHolder.txtFractionSkewed": "Изменить на диагональную простую дробь", "PE.Views.DocumentHolder.txtFractionStacked": "Изменить на вертикальную простую дробь", "PE.Views.DocumentHolder.txtGroup": "Сгруппировать", "PE.Views.DocumentHolder.txtGroupCharOver": "Символ над текстом", "PE.Views.DocumentHolder.txtGroupCharUnder": "Символ под текстом", "PE.Views.DocumentHolder.txtHideBottom": "Скрыть нижнюю границу", "PE.Views.DocumentHolder.txtHideBottomLimit": "Скрыть нижний предел", "PE.Views.DocumentHolder.txtHideCloseBracket": "Скрыть закрывающую скобку", "PE.Views.DocumentHolder.txtHideDegree": "Скрыть степень", "PE.Views.DocumentHolder.txtHideHor": "Скрыть горизонтальную линию", "PE.Views.DocumentHolder.txtHideLB": "Скрыть линию из левого нижнего угла", "PE.Views.DocumentHolder.txtHideLeft": "Скрыть левую границу", "PE.Views.DocumentHolder.txtHideLT": "Скрыть линию из левого верхнего угла", "PE.Views.DocumentHolder.txtHideOpenBracket": "Скрыть открывающую скобку", "PE.Views.DocumentHolder.txtHidePlaceholder": "Скрыть поля для заполнения", "PE.Views.DocumentHolder.txtHideRight": "Скрыть правую границу", "PE.Views.DocumentHolder.txtHideTop": "Скрыть верхнюю границу", "PE.Views.DocumentHolder.txtHideTopLimit": "Скрыть верхний предел", "PE.Views.DocumentHolder.txtHideVer": "Скрыть вертикальную линию", "PE.Views.DocumentHolder.txtIncreaseArg": "Увеличить размер аргумента", "PE.Views.DocumentHolder.txtInsertArgAfter": "Вставить аргумент после", "PE.Views.DocumentHolder.txtInsertArgBefore": "Вставить аргумент перед", "PE.Views.DocumentHolder.txtInsertBreak": "Вставить принудительный разрыв", "PE.Views.DocumentHolder.txtInsertEqAfter": "Вставить уравнение после", "PE.Views.DocumentHolder.txtInsertEqBefore": "Вставить уравнение перед", "PE.Views.DocumentHolder.txtKeepTextOnly": "Сохранить только текст", "PE.Views.DocumentHolder.txtLimitChange": "Изменить положение пределов", "PE.Views.DocumentHolder.txtLimitOver": "Предел над текстом", "PE.Views.DocumentHolder.txtLimitUnder": "Предел под текстом", "PE.Views.DocumentHolder.txtMatchBrackets": "Изменить размер скобок в соответствии с высотой аргумента", "PE.Views.DocumentHolder.txtMatrixAlign": "Выравнивание матрицы", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Переместить слайд в конец", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Переместить слайд в начало", "PE.Views.DocumentHolder.txtNewSlide": "Новый слайд", "PE.Views.DocumentHolder.txtOverbar": "Черта над текстом", "PE.Views.DocumentHolder.txtPasteDestFormat": "Использовать конечную тему", "PE.Views.DocumentHolder.txtPastePicture": "Изображение", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Сохранить исходное форматирование", "PE.Views.DocumentHolder.txtPressLink": "Нажмите {0} и щелкните по ссылке", "PE.Views.DocumentHolder.txtPreview": "Начать показ слайдов", "PE.Views.DocumentHolder.txtPrintSelection": "Напечатать выделенное", "PE.Views.DocumentHolder.txtRemFractionBar": "Удалить дробную черту", "PE.Views.DocumentHolder.txtRemLimit": "Удалить предел", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Удалить диакритический знак", "PE.Views.DocumentHolder.txtRemoveBar": "Удалить черту", "PE.Views.DocumentHolder.txtRemScripts": "Удалить индексы", "PE.Views.DocumentHolder.txtRemSubscript": "Удалить нижний индекс", "PE.Views.DocumentHolder.txtRemSuperscript": "Удалить верхний индекс", "PE.Views.DocumentHolder.txtResetLayout": "Сбросить макет слайда", "PE.Views.DocumentHolder.txtScriptsAfter": "Индексы после текста", "PE.Views.DocumentHolder.txtScriptsBefore": "Индексы перед текстом", "PE.Views.DocumentHolder.txtSelectAll": "Выделить все", "PE.Views.DocumentHolder.txtShowBottomLimit": "Показать нижний предел", "PE.Views.DocumentHolder.txtShowCloseBracket": "Показать закрывающую скобку", "PE.Views.DocumentHolder.txtShowDegree": "Показать степень", "PE.Views.DocumentHolder.txtShowOpenBracket": "Показать открывающую скобку", "PE.Views.DocumentHolder.txtShowPlaceholder": "Показать поля для заполнения", "PE.Views.DocumentHolder.txtShowTopLimit": "Показать верхний предел", "PE.Views.DocumentHolder.txtSlide": "Слайд", "PE.Views.DocumentHolder.txtSlideHide": "Скрыть слайд", "PE.Views.DocumentHolder.txtStretchBrackets": "Растянуть скобки", "PE.Views.DocumentHolder.txtTop": "По верхнему краю", "PE.Views.DocumentHolder.txtUnderbar": "Черта под текстом", "PE.Views.DocumentHolder.txtUngroup": "Разгруппировать", "PE.Views.DocumentHolder.txtWarnUrl": "Переход по этой ссылке может нанести вред вашему устройству и данным.<br>Вы действительно хотите продолжить?", "PE.Views.DocumentHolder.unicodeText": "Юникод", "PE.Views.DocumentHolder.vertAlignText": "Вертикальное выравнивание", "PE.Views.DocumentPreview.goToSlideText": "Перейти к слайду", "PE.Views.DocumentPreview.slideIndexText": "Слайд {0} из {1}", "PE.Views.DocumentPreview.txtClose": "Завершить показ слайдов", "PE.Views.DocumentPreview.txtEndSlideshow": "Завершить показ слайдов", "PE.Views.DocumentPreview.txtExitFullScreen": "Выйти из полноэкранного режима", "PE.Views.DocumentPreview.txtFinalMessage": "Просмотр слайдов завершен. Щелкните, чтобы выйти.", "PE.Views.DocumentPreview.txtFullScreen": "Полноэкранный режим", "PE.Views.DocumentPreview.txtNext": "Следующий слайд", "PE.Views.DocumentPreview.txtPageNumInvalid": "Неправильный номер слайда", "PE.Views.DocumentPreview.txtPause": "Приостановить презентацию", "PE.Views.DocumentPreview.txtPlay": "Запустить презентацию", "PE.Views.DocumentPreview.txtPrev": "Предыдущий слайд", "PE.Views.DocumentPreview.txtReset": "Сброс", "PE.Views.FileMenu.btnAboutCaption": "О программе", "PE.Views.FileMenu.btnBackCaption": "Открыть расположение файла", "PE.Views.FileMenu.btnCloseMenuCaption": "Закрыть меню", "PE.Views.FileMenu.btnCreateNewCaption": "Создать новую", "PE.Views.FileMenu.btnDownloadCaption": "Скачать как", "PE.Views.FileMenu.btnExitCaption": "Закрыть", "PE.Views.FileMenu.btnFileOpenCaption": "Открыть", "PE.Views.FileMenu.btnHelpCaption": "Справка", "PE.Views.FileMenu.btnHistoryCaption": "История версий", "PE.Views.FileMenu.btnInfoCaption": "Сведения о презентации", "PE.Views.FileMenu.btnPrintCaption": "Печать", "PE.Views.FileMenu.btnProtectCaption": "Защитить", "PE.Views.FileMenu.btnRecentFilesCaption": "Открыть последние", "PE.Views.FileMenu.btnRenameCaption": "Переименовать", "PE.Views.FileMenu.btnReturnCaption": "Вернуться к презентации", "PE.Views.FileMenu.btnRightsCaption": "Права доступа", "PE.Views.FileMenu.btnSaveAsCaption": "Сохранить как", "PE.Views.FileMenu.btnSaveCaption": "Сохранить", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Сохранить копию как", "PE.Views.FileMenu.btnSettingsCaption": "Дополнительные параметры", "PE.Views.FileMenu.btnToEditCaption": "Редактировать", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Пустая презентация", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Создать новую", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Применить", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Добавить автора", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Добавить текст", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Приложение", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Изменить права доступа", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Комментарий", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Создана", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Автор последнего изменения", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Последнее изменение", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Размещение", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Люди, имеющие права", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Тема", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Теги", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Название", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Загружена", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Изменить права доступа", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Люди, имеющие права", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Внимание", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "C помощью пароля", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Защитить презентацию", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "С помощью подписи", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Редактировать презентацию", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "При редактировании из презентации будут удалены подписи.<br>Продолжить?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Эта презентация защищена паролем", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "В презентацию добавлены действительные подписи. Презентация защищена от редактирования.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Некоторые из цифровых подписей в презентации недействительны или их нельзя проверить. Презентация защищена от редактирования.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Просмотр подписей", "PE.Views.FileMenuPanels.Settings.okButtonText": "Применить", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Режим совместного редактирования", "PE.Views.FileMenuPanels.Settings.strFast": "Быстрый", "PE.Views.FileMenuPanels.Settings.strFontRender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> шрифтов", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Пропускать слова из ПРОПИСНЫХ БУКВ", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Пропускать слова с цифрами", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Настройки макросов", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Показывать кнопку Параметры вставки при вставке содержимого", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Показывать изменения других пользователей", "PE.Views.FileMenuPanels.Settings.strStrict": "Строгий", "PE.Views.FileMenuPanels.Settings.strTheme": "Тема интерфейса", "PE.Views.FileMenuPanels.Settings.strUnit": "Единица измерения", "PE.Views.FileMenuPanels.Settings.strZoom": "Стандартное значение масштаба", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Каждые 10 минут", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Каждые 30 минут", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Каждые 5 минут", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Каждый час", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Направляющие выравнивания", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Автовосстановление", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Автосохранение", "PE.Views.FileMenuPanels.Settings.textDisabled": "Отключено", "PE.Views.FileMenuPanels.Settings.textForceSave": "Сохранение промежуточных версий", "PE.Views.FileMenuPanels.Settings.textMinute": "Каждую минуту", "PE.Views.FileMenuPanels.Settings.txtAll": "Все", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Параметры автозамены...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Режим кэширования по умолчанию", "PE.Views.FileMenuPanels.Settings.txtCm": "Сантиметр", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Совместная работа", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Редактирование и сохранение", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Совместное редактирование в режиме реального времени. Все изменения сохраняются автоматически", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "По размеру слайда", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "По ширине", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Иероглифы", "PE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "Последние", "PE.Views.FileMenuPanels.Settings.txtMac": "как OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Собственный", "PE.Views.FileMenuPanels.Settings.txtProofing": "Правописание", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON>у<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Показывать кнопку Быстрая печать в шапке редактора", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Документ будет напечатан на последнем выбранном принтере или на принтере по умолчанию", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Включить все", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Включить все макросы без уведомления", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Проверка орфографии", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Отключить все", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Отключить все макросы без уведомления", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Используйте кнопку \"Сохранить\" для синхронизации изменений, внесенных вами и другими пользователями.", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Использовать клавишу Alt для навигации по интерфейсу с помощью клавиатуры", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Использовать клавишу Option для навигации по интерфейсу с помощью клавиатуры", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Показывать уведомление", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Отключить все макросы с уведомлением", "PE.Views.FileMenuPanels.Settings.txtWin": "как Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Рабочая область", "PE.Views.GridSettings.textCm": "см", "PE.Views.GridSettings.textCustom": "Пользовательские", "PE.Views.GridSettings.textSpacing": "Интервал", "PE.Views.GridSettings.textTitle": "Параметры сетки", "PE.Views.HeaderFooterDialog.applyAllText": "Применить ко всем", "PE.Views.HeaderFooterDialog.applyText": "Применить", "PE.Views.HeaderFooterDialog.diffLanguage": "Формат даты должен использовать тот же язык, что и образец слайдов.<br>Чтобы изменить образец, вместо кнопки 'Применить' нажмите кнопку 'Применить ко всем'", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Внимание", "PE.Views.HeaderFooterDialog.textDateTime": "Дата и время", "PE.Views.HeaderFooterDialog.textFixed": "Фиксировано", "PE.Views.HeaderFooterDialog.textFooter": "Текст в нижнем колонтитуле", "PE.Views.HeaderFooterDialog.textFormat": "Форматы", "PE.Views.HeaderFooterDialog.textLang": "Язык", "PE.Views.HeaderFooterDialog.textNotTitle": "Не показывать на титульном слайде", "PE.Views.HeaderFooterDialog.textPreview": "Просмотр", "PE.Views.HeaderFooterDialog.textSlideNum": "Номер слайда", "PE.Views.HeaderFooterDialog.textTitle": "Параметры нижнего колонтитула", "PE.Views.HeaderFooterDialog.textUpdate": "Обновлять автоматически", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Отобража<PERSON>ь", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Связать с", "PE.Views.HyperlinkSettingsDialog.textDefault": "Выделенный фрагмент текста", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Введите здесь надпись", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Введите здесь ссылку", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Введите здесь подсказку", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Внешняя ссылка", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Слайд в этой презентации", "PE.Views.HyperlinkSettingsDialog.textSlides": "Слайды", "PE.Views.HyperlinkSettingsDialog.textTipText": "Текст подсказки", "PE.Views.HyperlinkSettingsDialog.textTitle": "Параметры гиперссылки", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Это поле обязательно для заполнения", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Первый слайд", "PE.Views.HyperlinkSettingsDialog.txtLast": "Последний слайд", "PE.Views.HyperlinkSettingsDialog.txtNext": "Следующий слайд", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Это поле должно быть URL-адресом в формате \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Предыдущий слайд", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Это поле может содержать не более 2083 символов", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Слайд", "PE.Views.ImageSettings.textAdvanced": "Дополнительные параметры", "PE.Views.ImageSettings.textCrop": "Обрезать", "PE.Views.ImageSettings.textCropFill": "Заливка", "PE.Views.ImageSettings.textCropFit": "Вписать", "PE.Views.ImageSettings.textCropToShape": "Обрезать по фигуре", "PE.Views.ImageSettings.textEdit": "Редактировать", "PE.Views.ImageSettings.textEditObject": "Редактировать объект", "PE.Views.ImageSettings.textFitSlide": "По размеру слайда", "PE.Views.ImageSettings.textFlip": "Отразить", "PE.Views.ImageSettings.textFromFile": "Из файла", "PE.Views.ImageSettings.textFromStorage": "Из хранилища", "PE.Views.ImageSettings.textFromUrl": "По URL", "PE.Views.ImageSettings.textHeight": "Высота", "PE.Views.ImageSettings.textHint270": "Повернуть на 90° против часовой стрелки", "PE.Views.ImageSettings.textHint90": "Повернуть на 90° по часовой стрелке", "PE.Views.ImageSettings.textHintFlipH": "Отразить слева направо", "PE.Views.ImageSettings.textHintFlipV": "Отразить сверху вниз", "PE.Views.ImageSettings.textInsert": "Заменить изображение", "PE.Views.ImageSettings.textOriginalSize": "Реальный размер", "PE.Views.ImageSettings.textRecentlyUsed": "Последние использованные", "PE.Views.ImageSettings.textRotate90": "Повернуть на 90°", "PE.Views.ImageSettings.textRotation": "Поворот", "PE.Views.ImageSettings.textSize": "Размер", "PE.Views.ImageSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Альтернативный текст", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Описание", "PE.Views.ImageSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, автофигура, диаграмма или таблица.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Заголовок", "PE.Views.ImageSettingsAdvanced.textAngle": "Угол", "PE.Views.ImageSettingsAdvanced.textCenter": "По центру", "PE.Views.ImageSettingsAdvanced.textFlipped": "Отраж<PERSON>но", "PE.Views.ImageSettingsAdvanced.textFrom": "От", "PE.Views.ImageSettingsAdvanced.textHeight": "Высота", "PE.Views.ImageSettingsAdvanced.textHorizontal": "По горизонтали", "PE.Views.ImageSettingsAdvanced.textHorizontally": "По горизонтали", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Сохранять пропорции", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Реальный размер", "PE.Views.ImageSettingsAdvanced.textPlacement": "Положение", "PE.Views.ImageSettingsAdvanced.textPosition": "Положение", "PE.Views.ImageSettingsAdvanced.textRotation": "Поворот", "PE.Views.ImageSettingsAdvanced.textSize": "Размер", "PE.Views.ImageSettingsAdvanced.textTitle": "Изображение - дополнительные параметры", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Верхний левый угол", "PE.Views.ImageSettingsAdvanced.textVertical": "По вертикали", "PE.Views.ImageSettingsAdvanced.textVertically": "По вертикали", "PE.Views.ImageSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "О программе", "PE.Views.LeftMenu.tipChat": "Чат", "PE.Views.LeftMenu.tipComments": "Комментарии", "PE.Views.LeftMenu.tipPlugins": "Плагины", "PE.Views.LeftMenu.tipSearch": "Поиск", "PE.Views.LeftMenu.tipSlides": "Слайды", "PE.Views.LeftMenu.tipSupport": "Обратная связь и поддержка", "PE.Views.LeftMenu.tipTitles": "Заголовки", "PE.Views.LeftMenu.txtDeveloper": "РЕЖИМ РАЗРАБОТЧИКА", "PE.Views.LeftMenu.txtEditor": "Редактор презентаций", "PE.Views.LeftMenu.txtLimit": "Ограниченный доступ", "PE.Views.LeftMenu.txtTrial": "ПРОБНЫЙ РЕЖИМ", "PE.Views.LeftMenu.txtTrialDev": "Пробный режим разработчика", "PE.Views.ParagraphSettings.strLineHeight": "Междустрочный интервал", "PE.Views.ParagraphSettings.strParagraphSpacing": "Интервал между абзацами", "PE.Views.ParagraphSettings.strSpacingAfter": "После", "PE.Views.ParagraphSettings.strSpacingBefore": "Перед", "PE.Views.ParagraphSettings.textAdvanced": "Дополнительные параметры", "PE.Views.ParagraphSettings.textAt": "Значение", "PE.Views.ParagraphSettings.textAtLeast": "Мини<PERSON>ум", "PE.Views.ParagraphSettings.textAuto": "Множитель", "PE.Views.ParagraphSettings.textExact": "Точно", "PE.Views.ParagraphSettings.txtAutoText": "Авто", "PE.Views.ParagraphSettingsAdvanced.noTabs": "В этом поле появятся позиции табуляции, которые вы зададите", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Все прописные", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Двойное зачёркивание", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Отступы", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Слева", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Междустрочный интервал", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Справа", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "После", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Перед", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Первая строка", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON>ри<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Отступы и интервалы", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Малые прописные", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Интервал между абзацами", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Зачёркивание", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Подстрочные", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Надстрочные", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Табуляция", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Выравнивание", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Множитель", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Межзнаковый интервал", "PE.Views.ParagraphSettingsAdvanced.textDefault": "По умолчанию", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Эффекты", "PE.Views.ParagraphSettingsAdvanced.textExact": "Точно", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Отступ", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Выступ", "PE.Views.ParagraphSettingsAdvanced.textJustified": "По ширине", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(нет)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Удалить", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Удалить все", "PE.Views.ParagraphSettingsAdvanced.textSet": "Задать", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "По центру", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "По левому краю", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Позиция", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "По правому краю", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Абзац - дополнительные параметры", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Авто", "PE.Views.PrintWithPreview.txtAllPages": "Все слайды", "PE.Views.PrintWithPreview.txtCurrentPage": "Текущий слайд", "PE.Views.PrintWithPreview.txtCustomPages": "Настраиваемая печать", "PE.Views.PrintWithPreview.txtEmptyTable": "Нечего печатать, так как презентация пустая", "PE.Views.PrintWithPreview.txtOf": "из {0}", "PE.Views.PrintWithPreview.txtPage": "Слайд", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Неправильный номер слайда", "PE.Views.PrintWithPreview.txtPages": "Слайды", "PE.Views.PrintWithPreview.txtPaperSize": "Размер бумаги", "PE.Views.PrintWithPreview.txtPrint": "Печать", "PE.Views.PrintWithPreview.txtPrintPdf": "Печать в PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Диапазон печати", "PE.Views.RightMenu.txtChartSettings": "Параметры диаграммы", "PE.Views.RightMenu.txtImageSettings": "Параметры изображения", "PE.Views.RightMenu.txtParagraphSettings": "Параметры абзаца", "PE.Views.RightMenu.txtShapeSettings": "Параметры фигуры", "PE.Views.RightMenu.txtSignatureSettings": "Настройка подписи", "PE.Views.RightMenu.txtSlideSettings": "Параметры слайда", "PE.Views.RightMenu.txtTableSettings": "Параметры таблицы", "PE.Views.RightMenu.txtTextArtSettings": "Параметры объектов Text Art", "PE.Views.ShapeSettings.strBackground": "Цвет фона", "PE.Views.ShapeSettings.strChange": "Изменить автофигуру", "PE.Views.ShapeSettings.strColor": "Цвет", "PE.Views.ShapeSettings.strFill": "Заливка", "PE.Views.ShapeSettings.strForeground": "Цвет переднего плана", "PE.Views.ShapeSettings.strPattern": "Узор", "PE.Views.ShapeSettings.strShadow": "Отображать тень", "PE.Views.ShapeSettings.strSize": "Толщина", "PE.Views.ShapeSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Непрозрачность", "PE.Views.ShapeSettings.strType": "Тип", "PE.Views.ShapeSettings.textAdvanced": "Дополнительные параметры", "PE.Views.ShapeSettings.textAngle": "Угол", "PE.Views.ShapeSettings.textBorderSizeErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 0 до 1584 пунктов.", "PE.Views.ShapeSettings.textColor": "Заливка цветом", "PE.Views.ShapeSettings.textDirection": "Направление", "PE.Views.ShapeSettings.textEmptyPattern": "Без узора", "PE.Views.ShapeSettings.textFlip": "Отразить", "PE.Views.ShapeSettings.textFromFile": "Из файла", "PE.Views.ShapeSettings.textFromStorage": "Из хранилища", "PE.Views.ShapeSettings.textFromUrl": "По URL", "PE.Views.ShapeSettings.textGradient": "Точки градиента", "PE.Views.ShapeSettings.textGradientFill": "Градиентная заливка", "PE.Views.ShapeSettings.textHint270": "Повернуть на 90° против часовой стрелки", "PE.Views.ShapeSettings.textHint90": "Повернуть на 90° по часовой стрелке", "PE.Views.ShapeSettings.textHintFlipH": "Отразить слева направо", "PE.Views.ShapeSettings.textHintFlipV": "Отразить сверху вниз", "PE.Views.ShapeSettings.textImageTexture": "Изображение или текстура", "PE.Views.ShapeSettings.textLinear": "Линейный", "PE.Views.ShapeSettings.textNoFill": "Без заливки", "PE.Views.ShapeSettings.textPatternFill": "Узор", "PE.Views.ShapeSettings.textPosition": "Положение", "PE.Views.ShapeSettings.textRadial": "Радиальный", "PE.Views.ShapeSettings.textRecentlyUsed": "Последние использованные", "PE.Views.ShapeSettings.textRotate90": "Повернуть на 90°", "PE.Views.ShapeSettings.textRotation": "Поворот", "PE.Views.ShapeSettings.textSelectImage": "Выбрать изображение", "PE.Views.ShapeSettings.textSelectTexture": "Выбрать", "PE.Views.ShapeSettings.textStretch": "Растяжение", "PE.Views.ShapeSettings.textStyle": "Стиль", "PE.Views.ShapeSettings.textTexture": "Из текстуры", "PE.Views.ShapeSettings.textTile": "Плитка", "PE.Views.ShapeSettings.tipAddGradientPoint": "Добавить точку градиента", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Удалить точку градиента", "PE.Views.ShapeSettings.txtBrownPaper": "Крафт-бумага", "PE.Views.ShapeSettings.txtCanvas": "Хол<PERSON>т", "PE.Views.ShapeSettings.txtCarton": "Карт<PERSON>н", "PE.Views.ShapeSettings.txtDarkFabric": "Темная ткань", "PE.Views.ShapeSettings.txtGrain": "Песок", "PE.Views.ShapeSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "Серая бумага", "PE.Views.ShapeSettings.txtKnit": "Вязание", "PE.Views.ShapeSettings.txtLeather": "Кожа", "PE.Views.ShapeSettings.txtNoBorders": "Без контура", "PE.Views.ShapeSettings.txtPapyrus": "Папир<PERSON>с", "PE.Views.ShapeSettings.txtWood": "Дерево", "PE.Views.ShapeSettingsAdvanced.strColumns": "Колонки", "PE.Views.ShapeSettingsAdvanced.strMargins": "Поля вокруг текста", "PE.Views.ShapeSettingsAdvanced.textAlt": "Альтернативный текст", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Описание", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, автофигура, диаграмма или таблица.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Заголовок", "PE.Views.ShapeSettingsAdvanced.textAngle": "Угол", "PE.Views.ShapeSettingsAdvanced.textArrows": "Стрелки", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Автоподбор", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Начальный размер", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Начальный стиль", "PE.Views.ShapeSettingsAdvanced.textBevel": "Скошенный", "PE.Views.ShapeSettingsAdvanced.textBottom": "Снизу", "PE.Views.ShapeSettingsAdvanced.textCapType": "Тип окончания", "PE.Views.ShapeSettingsAdvanced.textCenter": "По центру", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Количество колонок", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Конечный размер", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Конечный стиль", "PE.Views.ShapeSettingsAdvanced.textFlat": "Плоский", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Отраж<PERSON>но", "PE.Views.ShapeSettingsAdvanced.textFrom": "От", "PE.Views.ShapeSettingsAdvanced.textHeight": "Высота", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "По горизонтали", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "По горизонтали", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Тип соединения", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Сохранять пропорции", "PE.Views.ShapeSettingsAdvanced.textLeft": "Слева", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Стиль линии", "PE.Views.ShapeSettingsAdvanced.textMiter": "Прям<PERSON>й", "PE.Views.ShapeSettingsAdvanced.textNofit": "Без автоподбора", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Положение", "PE.Views.ShapeSettingsAdvanced.textPosition": "Позиция", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Подгонять размер фигуры под текст", "PE.Views.ShapeSettingsAdvanced.textRight": "Справа", "PE.Views.ShapeSettingsAdvanced.textRotation": "Поворот", "PE.Views.ShapeSettingsAdvanced.textRound": "Закругленный", "PE.Views.ShapeSettingsAdvanced.textShrink": "Сжать текст при переполнении", "PE.Views.ShapeSettingsAdvanced.textSize": "Размер", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Интервал между колонками", "PE.Views.ShapeSettingsAdvanced.textSquare": "Квадр<PERSON>тный", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Текстовое поле", "PE.Views.ShapeSettingsAdvanced.textTitle": "Фигура - дополнительные параметры", "PE.Views.ShapeSettingsAdvanced.textTop": "Сверху", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Верхний левый угол", "PE.Views.ShapeSettingsAdvanced.textVertical": "По вертикали", "PE.Views.ShapeSettingsAdvanced.textVertically": "По вертикали", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Линии и стрелки", "PE.Views.ShapeSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "Нет", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Внимание", "PE.Views.SignatureSettings.strDelete": "Удалить подпись", "PE.Views.SignatureSettings.strDetails": "Состав подписи", "PE.Views.SignatureSettings.strInvalid": "Недействительные подписи", "PE.Views.SignatureSettings.strSign": "Подписать", "PE.Views.SignatureSettings.strSignature": "Подпись", "PE.Views.SignatureSettings.strValid": "Действительные подписи", "PE.Views.SignatureSettings.txtContinueEditing": "Все равно редактировать", "PE.Views.SignatureSettings.txtEditWarning": "При редактировании из презентации будут удалены подписи.<br>Продолжить?", "PE.Views.SignatureSettings.txtRemoveWarning": "Вы хотите удалить эту подпись?<br>Это нельзя отменить.", "PE.Views.SignatureSettings.txtSigned": "В презентацию добавлены действительные подписи. Презентация защищена от редактирования.", "PE.Views.SignatureSettings.txtSignedInvalid": "Некоторые из цифровых подписей в презентации недействительны или их нельзя проверить. Презентация защищена от редактирования.", "PE.Views.SlideSettings.strBackground": "Цвет фона", "PE.Views.SlideSettings.strColor": "Цвет", "PE.Views.SlideSettings.strDateTime": "Показывать дату и время", "PE.Views.SlideSettings.strFill": "Фон", "PE.Views.SlideSettings.strForeground": "Цвет переднего плана", "PE.Views.SlideSettings.strPattern": "Узор", "PE.Views.SlideSettings.strSlideNum": "Показывать номер слайда", "PE.Views.SlideSettings.strTransparency": "Непрозрачность", "PE.Views.SlideSettings.textAdvanced": "Дополнительные параметры", "PE.Views.SlideSettings.textAngle": "Угол", "PE.Views.SlideSettings.textColor": "Заливка цветом", "PE.Views.SlideSettings.textDirection": "Направление", "PE.Views.SlideSettings.textEmptyPattern": "Без узора", "PE.Views.SlideSettings.textFromFile": "Из файла", "PE.Views.SlideSettings.textFromStorage": "Из хранилища", "PE.Views.SlideSettings.textFromUrl": "По URL", "PE.Views.SlideSettings.textGradient": "Точки градиента", "PE.Views.SlideSettings.textGradientFill": "Градиентная заливка", "PE.Views.SlideSettings.textImageTexture": "Изображение или текстура", "PE.Views.SlideSettings.textLinear": "Линейный", "PE.Views.SlideSettings.textNoFill": "Без заливки", "PE.Views.SlideSettings.textPatternFill": "Узор", "PE.Views.SlideSettings.textPosition": "Положение", "PE.Views.SlideSettings.textRadial": "Радиальный", "PE.Views.SlideSettings.textReset": "Сбросить изменения", "PE.Views.SlideSettings.textSelectImage": "Выбрать изображение", "PE.Views.SlideSettings.textSelectTexture": "Выбрать", "PE.Views.SlideSettings.textStretch": "Растяжение", "PE.Views.SlideSettings.textStyle": "Стиль", "PE.Views.SlideSettings.textTexture": "Из текстуры", "PE.Views.SlideSettings.textTile": "Плитка", "PE.Views.SlideSettings.tipAddGradientPoint": "Добавить точку градиента", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Удалить точку градиента", "PE.Views.SlideSettings.txtBrownPaper": "Крафт-бумага", "PE.Views.SlideSettings.txtCanvas": "Хол<PERSON>т", "PE.Views.SlideSettings.txtCarton": "Карт<PERSON>н", "PE.Views.SlideSettings.txtDarkFabric": "Темная ткань", "PE.Views.SlideSettings.txtGrain": "Песок", "PE.Views.SlideSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "Серая бумага", "PE.Views.SlideSettings.txtKnit": "Вязание", "PE.Views.SlideSettings.txtLeather": "Кожа", "PE.Views.SlideSettings.txtPapyrus": "Папир<PERSON>с", "PE.Views.SlideSettings.txtWood": "Дерево", "PE.Views.SlideshowSettings.textLoop": "Непрерывный цикл до нажатия клавиши 'Esc'", "PE.Views.SlideshowSettings.textTitle": "Параметры показа слайдов", "PE.Views.SlideSizeSettings.strLandscape": "Альбомная", "PE.Views.SlideSizeSettings.strPortrait": "Книжная", "PE.Views.SlideSizeSettings.textHeight": "Высота", "PE.Views.SlideSizeSettings.textSlideOrientation": "Ориентация слайда", "PE.Views.SlideSizeSettings.textSlideSize": "Размер слайда", "PE.Views.SlideSizeSettings.textTitle": "Настройки размера слайда", "PE.Views.SlideSizeSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Слайды 35 мм", "PE.Views.SlideSizeSettings.txtA3": "Лист A3 (297x420 мм)", "PE.Views.SlideSizeSettings.txtA4": "Лист A4 (210x297 мм)", "PE.Views.SlideSizeSettings.txtB4": "Лист B4 (ICO) (250x353 мм)", "PE.Views.SlideSizeSettings.txtB5": "Лист B5 (ICO) (176x250 мм)", "PE.Views.SlideSizeSettings.txtBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtCustom": "Пользовательский", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON><PERSON><PERSON> (11x17 дюймов)", "PE.Views.SlideSizeSettings.txtLetter": "Лист Letter (8.5x11 дюймов)", "PE.Views.SlideSizeSettings.txtOverhead": "Прозрачная пленка", "PE.Views.SlideSizeSettings.txtStandard": "Стандартный (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Широкоэкранный", "PE.Views.Statusbar.goToPageText": "Перейти к слайду", "PE.Views.Statusbar.pageIndexText": "Слайд {0} из {1}", "PE.Views.Statusbar.textShowBegin": "Показ слайдов с начала", "PE.Views.Statusbar.textShowCurrent": "Показ слайдов с текущего слайда", "PE.Views.Statusbar.textShowPresenterView": "Показ слайдов в режиме докладчика", "PE.Views.Statusbar.tipAccessRights": "Управление правами доступа к документу", "PE.Views.Statusbar.tipFitPage": "По размеру слайда", "PE.Views.Statusbar.tipFitWidth": "По ширине", "PE.Views.Statusbar.tipPreview": "Начать показ слайдов", "PE.Views.Statusbar.tipSetLang": "Выбрать язык текста", "PE.Views.Statusbar.tipZoomFactor": "Масш<PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipZoomIn": "Увеличить", "PE.Views.Statusbar.tipZoomOut": "Уменьшить", "PE.Views.Statusbar.txtPageNumInvalid": "Неправильный номер слайда", "PE.Views.TableSettings.deleteColumnText": "Удалить столбец", "PE.Views.TableSettings.deleteRowText": "Удалить строку", "PE.Views.TableSettings.deleteTableText": "Удалить таблицу", "PE.Views.TableSettings.insertColumnLeftText": "Вставить столбец слева", "PE.Views.TableSettings.insertColumnRightText": "Вставить столбец справа", "PE.Views.TableSettings.insertRowAboveText": "Вставить строку выше", "PE.Views.TableSettings.insertRowBelowText": "Вставить строку ниже", "PE.Views.TableSettings.mergeCellsText": "Объединить ячейки", "PE.Views.TableSettings.selectCellText": "Выбрать ячейку", "PE.Views.TableSettings.selectColumnText": "Выбрать столбец", "PE.Views.TableSettings.selectRowText": "Выбрать строку", "PE.Views.TableSettings.selectTableText": "Выбрать таблицу", "PE.Views.TableSettings.splitCellsText": "Разделить ячейку...", "PE.Views.TableSettings.splitCellTitleText": "Разделить ячейку", "PE.Views.TableSettings.textAdvanced": "Дополнительные параметры", "PE.Views.TableSettings.textBackColor": "Цвет фона", "PE.Views.TableSettings.textBanded": "Чередовать", "PE.Views.TableSettings.textBorderColor": "Цвет", "PE.Views.TableSettings.textBorders": "Стиль границ", "PE.Views.TableSettings.textCellSize": "Размер ячейки", "PE.Views.TableSettings.textColumns": "Столбцы", "PE.Views.TableSettings.textDistributeCols": "Выровнять ширину столбцов", "PE.Views.TableSettings.textDistributeRows": "Выровнять высоту строк", "PE.Views.TableSettings.textEdit": "Строки и столбцы", "PE.Views.TableSettings.textEmptyTemplate": "Без ша<PERSON><PERSON><PERSON>нов", "PE.Views.TableSettings.textFirst": "Первый", "PE.Views.TableSettings.textHeader": "Заголовок", "PE.Views.TableSettings.textHeight": "Высота", "PE.Views.TableSettings.textLast": "Последний", "PE.Views.TableSettings.textRows": "Строки", "PE.Views.TableSettings.textSelectBorders": "Выберите границы, к которым надо применить выбранный стиль", "PE.Views.TableSettings.textTemplate": "По шаблону", "PE.Views.TableSettings.textTotal": "Итоговая", "PE.Views.TableSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Задать внешнюю границу и все внутренние линии", "PE.Views.TableSettings.tipBottom": "Задать только внешнюю нижнюю границу", "PE.Views.TableSettings.tipInner": "Задать только внутренние линии", "PE.Views.TableSettings.tipInnerHor": "Задать только горизонтальные внутренние линии", "PE.Views.TableSettings.tipInnerVert": "Задать только вертикальные внутренние линии", "PE.Views.TableSettings.tipLeft": "Задать только внешнюю левую границу", "PE.Views.TableSettings.tipNone": "Не задавать границ", "PE.Views.TableSettings.tipOuter": "Задать только внешнюю границу", "PE.Views.TableSettings.tipRight": "Задать только внешнюю правую границу", "PE.Views.TableSettings.tipTop": "Задать только внешнюю верхнюю границу", "PE.Views.TableSettings.txtGroupTable_Custom": "Пользовательские", "PE.Views.TableSettings.txtGroupTable_Dark": "Темные", "PE.Views.TableSettings.txtGroupTable_Light": "Светлые", "PE.Views.TableSettings.txtGroupTable_Medium": "Средние", "PE.Views.TableSettings.txtGroupTable_Optimal": "Оптимальный для документа", "PE.Views.TableSettings.txtNoBorders": "Без границ", "PE.Views.TableSettings.txtTable_Accent": "акцент", "PE.Views.TableSettings.txtTable_DarkStyle": "Темный стиль", "PE.Views.TableSettings.txtTable_LightStyle": "Светлый стиль", "PE.Views.TableSettings.txtTable_MediumStyle": "Средний стиль", "PE.Views.TableSettings.txtTable_NoGrid": "нет сетки", "PE.Views.TableSettings.txtTable_NoStyle": "Нет стиля", "PE.Views.TableSettings.txtTable_TableGrid": "сетка таблицы", "PE.Views.TableSettings.txtTable_ThemedStyle": "Стиль из темы", "PE.Views.TableSettingsAdvanced.textAlt": "Альтернативный текст", "PE.Views.TableSettingsAdvanced.textAltDescription": "Описание", "PE.Views.TableSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, автофигура, диаграмма или таблица.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Заголовок", "PE.Views.TableSettingsAdvanced.textBottom": "Снизу", "PE.Views.TableSettingsAdvanced.textCenter": "По центру", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Использовать поля по умолчанию", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Поля по умолчанию", "PE.Views.TableSettingsAdvanced.textFrom": "От", "PE.Views.TableSettingsAdvanced.textHeight": "Высота", "PE.Views.TableSettingsAdvanced.textHorizontal": "По горизонтали", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Сохранять пропорции", "PE.Views.TableSettingsAdvanced.textLeft": "Слева", "PE.Views.TableSettingsAdvanced.textMargins": "Поля ячейки", "PE.Views.TableSettingsAdvanced.textPlacement": "Положение", "PE.Views.TableSettingsAdvanced.textPosition": "Позиция", "PE.Views.TableSettingsAdvanced.textRight": "Справа", "PE.Views.TableSettingsAdvanced.textSize": "Размер", "PE.Views.TableSettingsAdvanced.textTitle": "Таблица - дополнительные параметры", "PE.Views.TableSettingsAdvanced.textTop": "Сверху", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Верхний левый угол", "PE.Views.TableSettingsAdvanced.textVertical": "По вертикали", "PE.Views.TableSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Поля", "PE.Views.TextArtSettings.strBackground": "Цвет фона", "PE.Views.TextArtSettings.strColor": "Цвет", "PE.Views.TextArtSettings.strFill": "Заливка", "PE.Views.TextArtSettings.strForeground": "Цвет переднего плана", "PE.Views.TextArtSettings.strPattern": "Узор", "PE.Views.TextArtSettings.strSize": "Толщина", "PE.Views.TextArtSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Непрозрачность", "PE.Views.TextArtSettings.strType": "Тип", "PE.Views.TextArtSettings.textAngle": "Угол", "PE.Views.TextArtSettings.textBorderSizeErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 0 до 1584 пунктов.", "PE.Views.TextArtSettings.textColor": "Заливка цветом", "PE.Views.TextArtSettings.textDirection": "Направление", "PE.Views.TextArtSettings.textEmptyPattern": "Без узора", "PE.Views.TextArtSettings.textFromFile": "Из файла", "PE.Views.TextArtSettings.textFromUrl": "По URL", "PE.Views.TextArtSettings.textGradient": "Точки градиента", "PE.Views.TextArtSettings.textGradientFill": "Градиентная заливка", "PE.Views.TextArtSettings.textImageTexture": "Изображение или текстура", "PE.Views.TextArtSettings.textLinear": "Линейный", "PE.Views.TextArtSettings.textNoFill": "Без заливки", "PE.Views.TextArtSettings.textPatternFill": "Узор", "PE.Views.TextArtSettings.textPosition": "Положение", "PE.Views.TextArtSettings.textRadial": "Радиальный", "PE.Views.TextArtSettings.textSelectTexture": "Выбрать", "PE.Views.TextArtSettings.textStretch": "Растяжение", "PE.Views.TextArtSettings.textStyle": "Стиль", "PE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "PE.Views.TextArtSettings.textTexture": "Из текстуры", "PE.Views.TextArtSettings.textTile": "Плитка", "PE.Views.TextArtSettings.textTransform": "Трансформация", "PE.Views.TextArtSettings.tipAddGradientPoint": "Добавить точку градиента", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Удалить точку градиента", "PE.Views.TextArtSettings.txtBrownPaper": "Крафт-бумага", "PE.Views.TextArtSettings.txtCanvas": "Хол<PERSON>т", "PE.Views.TextArtSettings.txtCarton": "Карт<PERSON>н", "PE.Views.TextArtSettings.txtDarkFabric": "Темная ткань", "PE.Views.TextArtSettings.txtGrain": "Песок", "PE.Views.TextArtSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGreyPaper": "Серая бумага", "PE.Views.TextArtSettings.txtKnit": "Вязание", "PE.Views.TextArtSettings.txtLeather": "Кожа", "PE.Views.TextArtSettings.txtNoBorders": "Без контура", "PE.Views.TextArtSettings.txtPapyrus": "Папир<PERSON>с", "PE.Views.TextArtSettings.txtWood": "Дерево", "PE.Views.Toolbar.capAddSlide": "Добавить слайд", "PE.Views.Toolbar.capBtnAddComment": "Добавить комментарий", "PE.Views.Toolbar.capBtnComment": "Комментарий", "PE.Views.Toolbar.capBtnDateTime": "Дата и время", "PE.Views.Toolbar.capBtnInsHeader": "Колонтитул", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Символ", "PE.Views.Toolbar.capBtnSlideNum": "Номер слайда", "PE.Views.Toolbar.capInsertAudio": "Аудио", "PE.Views.Toolbar.capInsertChart": "Диаграмма", "PE.Views.Toolbar.capInsertEquation": "Уравнение", "PE.Views.Toolbar.capInsertHyperlink": "Гиперссылка", "PE.Views.Toolbar.capInsertImage": "Изображение", "PE.Views.Toolbar.capInsertShape": "Фигура", "PE.Views.Toolbar.capInsertTable": "Таблица", "PE.Views.Toolbar.capInsertText": "Надпись", "PE.Views.Toolbar.capInsertTextArt": "Text Art", "PE.Views.Toolbar.capInsertVideo": "Видео", "PE.Views.Toolbar.capTabFile": "<PERSON>а<PERSON><PERSON>", "PE.Views.Toolbar.capTabHome": "Главная", "PE.Views.Toolbar.capTabInsert": "Вставка", "PE.Views.Toolbar.mniCapitalizeWords": "Каждое Слово С Прописной", "PE.Views.Toolbar.mniCustomTable": "Пользовательская таблица", "PE.Views.Toolbar.mniImageFromFile": "Изображение из файла", "PE.Views.Toolbar.mniImageFromStorage": "Изображение из хранилища", "PE.Views.Toolbar.mniImageFromUrl": "Изображение по URL", "PE.Views.Toolbar.mniInsertSSE": "Вставить таблицу", "PE.Views.Toolbar.mniLowerCase": "нижний регистр", "PE.Views.Toolbar.mniSentenceCase": "Как в предложениях.", "PE.Views.Toolbar.mniSlideAdvanced": "Дополнительные параметры", "PE.Views.Toolbar.mniSlideStandard": "Стандартный (4:3)", "PE.Views.Toolbar.mniSlideWide": "Широкоэкранный (16:9)", "PE.Views.Toolbar.mniToggleCase": "иЗМЕНИТЬ рЕГИСТР", "PE.Views.Toolbar.mniUpperCase": "ВЕРХНИЙ РЕГИСТР", "PE.Views.Toolbar.strMenuNoFill": "Без заливки", "PE.Views.Toolbar.textAlignBottom": "Выравнивание текста по нижнему краю", "PE.Views.Toolbar.textAlignCenter": "Выравнивание текста по центру", "PE.Views.Toolbar.textAlignJust": "Выравнивание текста по ширине", "PE.Views.Toolbar.textAlignLeft": "Выравнивание текста по левому краю", "PE.Views.Toolbar.textAlignMiddle": "Выравнивание текста по середине", "PE.Views.Toolbar.textAlignRight": "Выравнивание текста по правому краю", "PE.Views.Toolbar.textAlignTop": "Выравнивание текста по верхнему краю", "PE.Views.Toolbar.textArrangeBack": "Перенести на задний план", "PE.Views.Toolbar.textArrangeBackward": "Перенести назад", "PE.Views.Toolbar.textArrangeForward": "Перенести вперед", "PE.Views.Toolbar.textArrangeFront": "Перенести на передний план", "PE.Views.Toolbar.textBold": "Полужирный", "PE.Views.Toolbar.textColumnsCustom": "Настраиваемые колонки", "PE.Views.Toolbar.textColumnsOne": "Одна колонка", "PE.Views.Toolbar.textColumnsThree": "Три колонки", "PE.Views.Toolbar.textColumnsTwo": "Две колонки", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textListSettings": "Параметры списка", "PE.Views.Toolbar.textRecentlyUsed": "Последние использованные", "PE.Views.Toolbar.textShapeAlignBottom": "Выровнять по нижнему краю", "PE.Views.Toolbar.textShapeAlignCenter": "Выровнять по центру", "PE.Views.Toolbar.textShapeAlignLeft": "Выровнять по левому краю", "PE.Views.Toolbar.textShapeAlignMiddle": "Выровнять по середине", "PE.Views.Toolbar.textShapeAlignRight": "Выровнять по правому краю", "PE.Views.Toolbar.textShapeAlignTop": "Выровнять по верхнему краю", "PE.Views.Toolbar.textShowBegin": "Показ слайдов с начала", "PE.Views.Toolbar.textShowCurrent": "Показ слайдов с текущего слайда", "PE.Views.Toolbar.textShowPresenterView": "Показ слайдов в режиме докладчика", "PE.Views.Toolbar.textShowSettings": "Параметры показа слайдов", "PE.Views.Toolbar.textStrikeout": "Зачёркнутый", "PE.Views.Toolbar.textSubscript": "Подстрочные знаки", "PE.Views.Toolbar.textSuperscript": "Надстрочные знаки", "PE.Views.Toolbar.textTabAnimation": "Анимация", "PE.Views.Toolbar.textTabCollaboration": "Совместная работа", "PE.Views.Toolbar.textTabFile": "<PERSON>а<PERSON><PERSON>", "PE.Views.Toolbar.textTabHome": "Главная", "PE.Views.Toolbar.textTabInsert": "Вставка", "PE.Views.Toolbar.textTabProtect": "Защита", "PE.Views.Toolbar.textTabTransitions": "Переходы", "PE.Views.Toolbar.textTabView": "Вид", "PE.Views.Toolbar.textTitleError": "Ошибка", "PE.Views.Toolbar.textUnderline": "Подчеркнутый", "PE.Views.Toolbar.tipAddSlide": "Добавить слайд", "PE.Views.Toolbar.tipBack": "Назад", "PE.Views.Toolbar.tipChangeCase": "Изменить регистр", "PE.Views.Toolbar.tipChangeChart": "Изменить тип диаграммы", "PE.Views.Toolbar.tipChangeSlide": "Изменить макет слайда", "PE.Views.Toolbar.tipClearStyle": "Очистить стиль", "PE.Views.Toolbar.tipColorSchemas": "Изменение цветовой схемы", "PE.Views.Toolbar.tipColumns": "Вставить колонки", "PE.Views.Toolbar.tipCopy": "Копировать", "PE.Views.Toolbar.tipCopyStyle": "Копировать стиль", "PE.Views.Toolbar.tipCut": "Вырезать", "PE.Views.Toolbar.tipDateTime": "Вставить текущую дату и время", "PE.Views.Toolbar.tipDecFont": "Уменьшить размер шрифта", "PE.Views.Toolbar.tipDecPrLeft": "Уменьшить отступ", "PE.Views.Toolbar.tipEditHeader": "Изменить нижний колонтитул", "PE.Views.Toolbar.tipFontColor": "Цвет шрифта", "PE.Views.Toolbar.tipFontName": "<PERSON>ри<PERSON><PERSON>", "PE.Views.Toolbar.tipFontSize": "Размер шрифта", "PE.Views.Toolbar.tipHAligh": "Горизонтальное выравнивание", "PE.Views.Toolbar.tipHighlightColor": "Цвет выделения", "PE.Views.Toolbar.tipIncFont": "Увеличить размер шрифта", "PE.Views.Toolbar.tipIncPrLeft": "Увеличить отступ", "PE.Views.Toolbar.tipInsertAudio": "Вставить аудио", "PE.Views.Toolbar.tipInsertChart": "Вставить диаграмму", "PE.Views.Toolbar.tipInsertEquation": "Вставить уравнение", "PE.Views.Toolbar.tipInsertHorizontalText": "Вставить горизонтальную надпись", "PE.Views.Toolbar.tipInsertHyperlink": "Добавить гиперссылку", "PE.Views.Toolbar.tipInsertImage": "Вставить изображение", "PE.Views.Toolbar.tipInsertShape": "Вставить автофигуру", "PE.Views.Toolbar.tipInsertSmartArt": "Вставить SmartArt", "PE.Views.Toolbar.tipInsertSymbol": "Вставить символ", "PE.Views.Toolbar.tipInsertTable": "Вставить таблицу", "PE.Views.Toolbar.tipInsertText": "Вставить надпись", "PE.Views.Toolbar.tipInsertTextArt": "Вставить объект Text Art", "PE.Views.Toolbar.tipInsertVerticalText": "Вставить вертикальную надпись", "PE.Views.Toolbar.tipInsertVideo": "Вставить видео", "PE.Views.Toolbar.tipLineSpace": "Междустрочный интервал", "PE.Views.Toolbar.tipMarkers": "Маркированный список", "PE.Views.Toolbar.tipMarkersArrow": "Маркеры-стрелки", "PE.Views.Toolbar.tipMarkersCheckmark": "Маркеры-галочки", "PE.Views.Toolbar.tipMarkersDash": "Маркеры-тире", "PE.Views.Toolbar.tipMarkersFRhombus": "Заполненные ромбовидные маркеры", "PE.Views.Toolbar.tipMarkersFRound": "Заполненные круглые маркеры", "PE.Views.Toolbar.tipMarkersFSquare": "Заполненные квадратные маркеры", "PE.Views.Toolbar.tipMarkersHRound": "Пустые круглые маркеры", "PE.Views.Toolbar.tipMarkersStar": "Маркеры-звездочки", "PE.Views.Toolbar.tipNone": "Нет", "PE.Views.Toolbar.tipNumbers": "Нумерованный список", "PE.Views.Toolbar.tipPaste": "Вставить", "PE.Views.Toolbar.tipPreview": "Начать показ слайдов", "PE.Views.Toolbar.tipPrint": "Печать", "PE.Views.Toolbar.tipPrintQuick": "Быстрая печать", "PE.Views.Toolbar.tipRedo": "Повторить", "PE.Views.Toolbar.tipSave": "Сохранить", "PE.Views.Toolbar.tipSaveCoauth": "Сохраните свои изменения, чтобы другие пользователи их увидели.", "PE.Views.Toolbar.tipSelectAll": "Выделить всё", "PE.Views.Toolbar.tipShapeAlign": "Выравнивание фигур", "PE.Views.Toolbar.tipShapeArrange": "Порядок фигур", "PE.Views.Toolbar.tipSlideNum": "Вставить номер слайда", "PE.Views.Toolbar.tipSlideSize": "Выбор размеров слайда", "PE.Views.Toolbar.tipSlideTheme": "Тема слайда", "PE.Views.Toolbar.tipUndo": "Отменить", "PE.Views.Toolbar.tipVAligh": "Вертикальное выравнивание", "PE.Views.Toolbar.tipViewSettings": "Параметры вида", "PE.Views.Toolbar.txtDistribHor": "Распределить по горизонтали", "PE.Views.Toolbar.txtDistribVert": "Распределить по вертикали", "PE.Views.Toolbar.txtDuplicateSlide": "Дублировать слайд", "PE.Views.Toolbar.txtGroup": "Сгруппировать", "PE.Views.Toolbar.txtObjectsAlign": "Выровнять выделенные объекты", "PE.Views.Toolbar.txtScheme1": "Стандартная", "PE.Views.Toolbar.txtScheme10": "Обычная", "PE.Views.Toolbar.txtScheme11": "Метро", "PE.Views.Toolbar.txtScheme12": "Модульная", "PE.Views.Toolbar.txtScheme13": "Изящная", "PE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme15": "Начальная", "PE.Views.Toolbar.txtScheme16": "Бумажная", "PE.Views.Toolbar.txtScheme17": "Солнцестояние", "PE.Views.Toolbar.txtScheme18": "Техническая", "PE.Views.Toolbar.txtScheme19": "Трек", "PE.Views.Toolbar.txtScheme2": "Оттенки серого", "PE.Views.Toolbar.txtScheme20": "Городская", "PE.Views.Toolbar.txtScheme21": "Яркая", "PE.Views.Toolbar.txtScheme22": "Новая офисная", "PE.Views.Toolbar.txtScheme3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme4": "Аспект", "PE.Views.Toolbar.txtScheme5": "Официальная", "PE.Views.Toolbar.txtScheme6": "Открытая", "PE.Views.Toolbar.txtScheme7": "Справедливость", "PE.Views.Toolbar.txtScheme8": "Поток", "PE.Views.Toolbar.txtScheme9": "Литейная", "PE.Views.Toolbar.txtSlideAlign": "Выровнять относительно слайда", "PE.Views.Toolbar.txtUngroup": "Разгруппировать", "PE.Views.Transitions.strDelay": "Задержка", "PE.Views.Transitions.strDuration": "<PERSON>лит.", "PE.Views.Transitions.strStartOnClick": "Запускать щелчком", "PE.Views.Transitions.textBlack": "Через черное", "PE.Views.Transitions.textBottom": "Снизу", "PE.Views.Transitions.textBottomLeft": "Снизу слева", "PE.Views.Transitions.textBottomRight": "Снизу справа", "PE.Views.Transitions.textClock": "<PERSON>а<PERSON>ы", "PE.Views.Transitions.textClockwise": "По часовой стрелке", "PE.Views.Transitions.textCounterclockwise": "Против часовой стрелки", "PE.Views.Transitions.textCover": "Наплыв", "PE.Views.Transitions.textFade": "Выцветание", "PE.Views.Transitions.textHorizontalIn": "По горизонтали внутрь", "PE.Views.Transitions.textHorizontalOut": "По горизонтали наружу", "PE.Views.Transitions.textLeft": "Слева", "PE.Views.Transitions.textNone": "Нет", "PE.Views.Transitions.textPush": "Задвигание", "PE.Views.Transitions.textRight": "Справа", "PE.Views.Transitions.textSmoothly": "Плавно", "PE.Views.Transitions.textSplit": "Панорама", "PE.Views.Transitions.textTop": "Сверху", "PE.Views.Transitions.textTopLeft": "Сверху слева", "PE.Views.Transitions.textTopRight": "Сверху справа", "PE.Views.Transitions.textUnCover": "Открывание", "PE.Views.Transitions.textVerticalIn": "По вертикали внутрь", "PE.Views.Transitions.textVerticalOut": "По вертикали наружу", "PE.Views.Transitions.textWedge": "Симметрично по кругу", "PE.Views.Transitions.textWipe": "Появление", "PE.Views.Transitions.textZoom": "Масштабирование", "PE.Views.Transitions.textZoomIn": "Увеличение", "PE.Views.Transitions.textZoomOut": "Уменьшение", "PE.Views.Transitions.textZoomRotate": "Увеличение с поворотом", "PE.Views.Transitions.txtApplyToAll": "Применить ко всем слайдам", "PE.Views.Transitions.txtParameters": "Параметры", "PE.Views.Transitions.txtPreview": "Просмотр", "PE.Views.Transitions.txtSec": "сек", "PE.Views.ViewTab.textAddHGuides": "Добавить горизонтальную направляющую", "PE.Views.ViewTab.textAddVGuides": "Добавить вертикальную направляющую", "PE.Views.ViewTab.textAlwaysShowToolbar": "Всегда показывать панель инструментов", "PE.Views.ViewTab.textClearGuides": "Удалить направляющие", "PE.Views.ViewTab.textCm": "см", "PE.Views.ViewTab.textCustom": "Пользовательские", "PE.Views.ViewTab.textFitToSlide": "По размеру слайда", "PE.Views.ViewTab.textFitToWidth": "По ширине", "PE.Views.ViewTab.textGridlines": "Линии сетки", "PE.Views.ViewTab.textGuides": "Направляющие", "PE.Views.ViewTab.textInterfaceTheme": "Тема интерфейса", "PE.Views.ViewTab.textLeftMenu": "Левая панель", "PE.Views.ViewTab.textNotes": "Заметки", "PE.Views.ViewTab.textRightMenu": "Правая панель", "PE.Views.ViewTab.textRulers": "Линейки", "PE.Views.ViewTab.textShowGridlines": "Показать линии сетки", "PE.Views.ViewTab.textShowGuides": "Показать направляющие", "PE.Views.ViewTab.textSmartGuides": "Смарт-направляющие", "PE.Views.ViewTab.textSnapObjects": "Привязать объект к сетке", "PE.Views.ViewTab.textStatusBar": "Строка состояния", "PE.Views.ViewTab.textZoom": "Масш<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.tipFitToSlide": "По размеру слайда", "PE.Views.ViewTab.tipFitToWidth": "По ширине", "PE.Views.ViewTab.tipGridlines": "Показать линии сетки", "PE.Views.ViewTab.tipGuides": "Показать направляющие", "PE.Views.ViewTab.tipInterfaceTheme": "Тема интерфейса"}