{"common.view.modals.txtCopy": "<PERSON><PERSON> k<PERSON>", "common.view.modals.txtEmbed": "Gömülü", "common.view.modals.txtHeight": "Yükseklik", "common.view.modals.txtShare": "Bağlantıyı Paylaş", "common.view.modals.txtWidth": "Genişlik", "common.view.SearchBar.textFind": "Bul", "SSE.ApplicationController.convertationErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarıs<PERSON>z oldu.", "SSE.ApplicationController.convertationTimeoutText": "<PERSON><PERSON><PERSON><PERSON><PERSON> süresi aşıldı.", "SSE.ApplicationController.criticalErrorTitle": "<PERSON><PERSON>", "SSE.ApplicationController.downloadErrorText": "Yükleme başarısız oldu.", "SSE.ApplicationController.downloadTextText": "Spreadsheet yükleniyor...", "SSE.ApplicationController.errorAccessDeny": "Hakkınız olmayan bir eylem gerçekleştirmeye çalışıyorsunuz.<br>Lütfen Belge Sunucu yöneticinize başvurun.", "SSE.ApplicationController.errorDefaultMessage": "Hata kodu: %1", "SSE.ApplicationController.errorFilePassProtect": "Döküman şifre korumalı ve açılamadı", "SSE.ApplicationController.errorFileSizeExceed": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> i<PERSON>in belirlenen limiti aşıyor.<br>Ayr<PERSON>nt<PERSON>lar için lütfen Doküman Sunucusu yöneticinizle iletişime geçin.", "SSE.ApplicationController.errorForceSave": "Dosya kaydedilirken bir hata oluştu. Dosyayı bilgisayarınıza kaydetmek için lütfen 'Farklı Kaydet' seçeneğini kullanın veya daha sonra tekrar deneyin.", "SSE.ApplicationController.errorInconsistentExt": "<PERSON><PERSON>a açılırken bir hata oluştu.<br><PERSON><PERSON><PERSON>, <PERSON>ya uzantısıyla eşleşmiyor.", "SSE.ApplicationController.errorInconsistentExtDocx": "Dosya açılırken bir hata oluştu.<br>Dosya içeriği metin be<PERSON> (örn. docx) karşılık geliyor, ancak dosyanın uzantısı tutarsız: %1.", "SSE.ApplicationController.errorInconsistentExtPdf": "Dosya açılırken bir hata oluştu.<br>Dosya içeriği şu biçimlerden birine karşılık geliyor: pdf/djvu/xps/oxps, ancak dosyanın uzantısı tutarsız: %1.", "SSE.ApplicationController.errorInconsistentExtPptx": "Dosya açılırken bir hata oluştu.<br>Dosya içeriği sunumlara karşılık geliyor (ör. pptx), ancak dosyanın uzantısı tutarsız: %1.", "SSE.ApplicationController.errorInconsistentExtXlsx": "Dosya açılırken bir hata oluştu.<br>Dosya içeriği e-tablolara (örn. xlsx) karşılık geliyor, ancak dosyanın uzantısı tutarsız: %1.", "SSE.ApplicationController.errorLoadingFont": "Yazı tipleri yüklenmedi.<br>Lütfen Doküman Sunucusu yöneticinize başvurun.", "SSE.ApplicationController.errorTokenExpire": "Belge güvenlik belirtecinin süresi doldu.<br>Lütfen Belge Sunucusu yöneticinize başvurun.", "SSE.ApplicationController.errorUpdateVersionOnDisconnect": "İnternet bağlantısı tekrar sağlandı, ve dosya versiyon değişti.<br>Çalışmanıza devam etmeden önce, veri kaybını önlemeniz için dosyasının bir kopyasını indirmeniz ya da dosya içeriğini kopyalamanız ve sonrasında sayfayı yenilemeniz gerekmektedir.", "SSE.ApplicationController.errorUserDrop": "Belgeye şu an erişilemiyor.", "SSE.ApplicationController.notcriticalErrorTitle": "Uyarı", "SSE.ApplicationController.openErrorText": "<PERSON><PERSON>a a<PERSON>ılırken bir hata oluştu.", "SSE.ApplicationController.scriptLoadError": "Bağlantı çok yavaş, bileş<PERSON><PERSON>in bazıları yüklenemedi. Lütfen sayfayı yenileyin.", "SSE.ApplicationController.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.ApplicationController.textGuest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.ApplicationController.textLoadingDocument": "Spreadsheet yükleniyor", "SSE.ApplicationController.textOf": "'in", "SSE.ApplicationController.txtClose": "Ka<PERSON><PERSON>", "SSE.ApplicationController.unknownErrorText": "Bilinmeyen hata.", "SSE.ApplicationController.unsupportedBrowserErrorText": "Tarayıcınız desteklenmiyor.", "SSE.ApplicationController.waitText": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "SSE.ApplicationView.txtDownload": "<PERSON><PERSON><PERSON>", "SSE.ApplicationView.txtEmbed": "Gömülü", "SSE.ApplicationView.txtFileLocation": "<PERSON><PERSON><PERSON> kon<PERSON>u aç", "SSE.ApplicationView.txtFullScreen": "<PERSON>", "SSE.ApplicationView.txtPrint": "Yazdır", "SSE.ApplicationView.txtSearch": "<PERSON><PERSON>", "SSE.ApplicationView.txtShare": "Paylaş"}