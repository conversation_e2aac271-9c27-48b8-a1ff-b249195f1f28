﻿<!DOCTYPE html>
<html>
	<head>
		<title>ISTFEHLER-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ISTFEHLER-Funktion</h1>
			<p>Die Funktion <b>ISTFEHLER</b> gehört zur Gruppe der Informationsfunktionen. Sie überprüft den ausgewählten Bereich auf einen Fehlerwert. Enthält der Zellbereich einen der folgenden Fehler: #NV, #WERT!, #BEZUG!, #DIV/0!, #ZAHL!, #NAME? oder #NULL!:, gibt die Funktion den Wert WAHR wieder, ansonsten FALSCH.</p>
			<p>Die Formelsyntax der Funktion <b>ISTFEHLER</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ISTFEHLER(Wert)</em></b></p> 
			<p><b><em>Wert</em></b> gibt den Wert wieder der geprüft werden soll. Sie können den zu überprüfenden Bereich manuell eingeben oder mit der Maus auswählen.</p>
			<p>Anwendung der Funktion <b>ISTFEHLER</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ISTFEHLER</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ISTFEHLER-Funktion" src="../images/iserror.png" /></p>
		</div>
	</body>
</html>