﻿<!DOCTYPE html>
<html>
	<head>
		<title>GTEST-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>GTEST-Funktion</h1>
			<p>Die Funktion <b>GTEST</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den einseitigen Wahrscheinlichkeitswert für einen Gaußtest (Normalverteilung) zurück. Für einen Erwartungswert einer Zufallsvariablen, µ0, gibt <b>GTEST</b> die Wahrscheinlichkeit zurück, mit der der Stichprobenmittelwert größer als der Durchschnitt der für diesen Datensatz (Array) durchgeführten Beobachtungen ist - also dem beobachteten Stichprobenmittel.</p>
			<p>Die Formelsyntax der Funktion <b>GTEST</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>GTEST(Matrix;x;[Sigma])</em></b></p> 
            <p><em>Dabei gilt:</em></p>
            <p style="text-indent: 50px;"><b><em>Matrix</em></b> ist die Matrix (Array) oder der Datenbereich, gegen die/den Sie <b><em>x</em></b> testen möchten.</p>
            <p style="text-indent: 50px;"><b><em>x</em></b> ist der zu testende Wert.</p>
            <p style="text-indent: 50px;"><b><em>Sigma</em></b> ist die bekannte Standardabweichung der Grundgesamtheit. Dieses Argument ist optional. Liegt keine Angabe vor, wird die Beispielstandardabweichung verwendet.</p>
            <p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>GTEST</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>GTEST</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="GTEST-Funktion" src="../images/ztest.png" /></p>
		</div>
	</body>
</html>