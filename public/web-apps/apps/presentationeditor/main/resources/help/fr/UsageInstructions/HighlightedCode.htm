﻿<!DOCTYPE html>
<html>
<head>
    <title>Insérer le code en surbrillance</title>
    <meta charset="utf-8" />
    <meta name="description" content="Description de l'extension Code en surbrillance pour les éditeurs ONLYOFFICE permettant d'intégrer dans une présentation le code auquel le style est déjà appliqué" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Insérer le code en surbrillance</h1>
        <p>Dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Présentations</b></a>, vous pouvez intégrer votre code mis en surbrillance auquel le style est déjà appliqué à correspondre avec le langage de programmation et le style de coloration dans le programme choisi.</p>
        <ol>
            <li>Accédez à votre présentation et placez le curseur à l'endroit où le code doit être inséré.</li>
            <li>Passez à l'onglet <b>Modules complémentaires</b> et choisissez <div class = "icon icon-highlight"></div> <b>Code en surbrillance</b>.</li>
            <li>Spécifiez la <b>Langue</b> de programmation.</li>
            <li>Choisissez le <b>Style</b> du code pour qu'il apparaisse de manière à sembler celui dans le programme.</li>
            <li>Spécifiez si on va remplacer les tabulations par des espaces.</li>
            <li>Choisissez la <b>Couleur de fond</b>. Pour le faire manuellement, déplacez le curseur sur la palette de couleurs ou passez la valeur de type <b>RBG</b>/<b>HSL</b>/<b>HEX</b>.</li>
            <li>Cliquez sur <b>OK </b>pour insérer le code.</li>
        </ol>
        <img class="gif" alt="Gif de l'extension Surbrillance" src="../../images/highlight_plugin.gif" width="600" />
    </div>
</body>
</html>