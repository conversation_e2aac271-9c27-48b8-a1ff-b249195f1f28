{"DATE": {"a": "(année; mois; jour)", "d": "Renvoie un numéro qui représente la date dans le code de date et d’heure"}, "DATEDIF": {"a": "(date_début; date_fin; unité)", "d": "Renvoie la différence entre deux dates (date_début et date_fin) sur la base d'un intervalle (unité) spécifié"}, "DATEVALUE": {"a": "(date_texte)", "d": "Convertit une date donnée sous forme de texte en un numéro représentant la date dans le code de date et d’heure"}, "DAY": {"a": "(numéro_de_série)", "d": "<PERSON><PERSON> le jour du mois (un nombre entre 1 et 31)."}, "DAYS": {"a": "(date_fin; date_début)", "d": "Calcule le nombre de jours entre les deux dates"}, "DAYS360": {"a": "(date_début; date_fin; [méthode])", "d": "Calcule le nombre de jours entre deux dates sur la base d'une année de 360 jours (12 mois de 30 jours)"}, "EDATE": {"a": "(date_départ; mois)", "d": "Renvoie le numéro de série de la date située un nombre spécifié de mois dans le passé ou le futur par rapport à une date indiquée"}, "EOMONTH": {"a": "(date_départ; mois)", "d": "<PERSON><PERSON> le numéro de série du dernier jour du mois situé dans un intervalle exprimé en nombre de mois dans le futur ou dans le passé"}, "HOUR": {"a": "(numéro_de_série)", "d": "Renvoie l’heure, un nombre entier entre 0 (12:00 A.M.) et 23 (11:00 P.M.)."}, "ISOWEEKNUM": {"a": "(date)", "d": "Renvoie le numéro ISO de la semaine de l’année correspondant à une date donnée"}, "MINUTE": {"a": "(numéro_de_série)", "d": "Renvoie les minutes, un entier entre 0 et 59."}, "MONTH": {"a": "(numéro_de_série)", "d": "<PERSON><PERSON> le mois, un nombre de 1 (janvier) à 12 (décembre)"}, "NETWORKDAYS": {"a": "(date_départ; date_fin; [jours_féri<PERSON>])", "d": "Renvoie le nombre de jours ouvrés compris entre deux dates"}, "NETWORKDAYS.INTL": {"a": "(date_départ; date_fin; [week-end]; [jours_fériés])", "d": "Renvoie le nombre de jours ouvrés compris entre deux dates avec des paramètres de week-end personnalisés"}, "NOW": {"a": "()", "d": "Renvoie la date du jour et de l'heure du jour, sous la forme d'une date et d'une heure."}, "SECOND": {"a": "(numéro_de_série)", "d": "<PERSON><PERSON> les secondes, un entier entre 0 et 59. "}, "TIME": {"a": "(heure; minute; seconde)", "d": "Convertit les heures, minutes et secondes données sous forme de nombres en un numéro de série, selon un format d'heure"}, "TIMEVALUE": {"a": "(heure_texte)", "d": "Convertit une heure donnée sous forme de texte en un numéro de série (temps_texte)"}, "TODAY": {"a": "()", "d": "Renvoie la date du jour au format de date."}, "WEEKDAY": {"a": "(numéro_de_série; [type_retour])", "d": "Renvoie un chiffre entre 1 et 7 désignant le jour de la semaine."}, "WEEKNUM": {"a": "(numéro_de_série; [type_renvoi])", "d": "Renvoie le numéro de la semaine de l’année"}, "WORKDAY": {"a": "(date_départ; nb_jours; [jours_fériés])", "d": "Renvoie le numéro de série d'une date située un nombre de jours ouvrés avant ou après une date donnée"}, "WORKDAY.INTL": {"a": "(date_départ; nb_jours; [nb_jours_week-end]; [jours_fériés])", "d": "Renvoie le numéro de série d’une date située un nombre de jours ouvrés avant ou après une date donnée avec des paramètres de week-end personnalisés"}, "YEAR": {"a": "(numéro_de_série)", "d": "Renvoie l’année, un entier entre 1900 et 9999."}, "YEARFRAC": {"a": "(date_début; date_fin; [base])", "d": "Renvoie une fraction correspondant au nombre de jours séparant date_début de date_fin par rapport à une année complète"}, "BESSELI": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel modifiée In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel modifiée Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel modifiée Yn(x)"}, "BIN2DEC": {"a": "(nombre)", "d": "Convertit un nombre binaire en nombre décimal"}, "BIN2HEX": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre binaire en nombre hexadécimal"}, "BIN2OCT": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre binaire en nombre octal"}, "BITAND": {"a": "(nombre1; nombre2)", "d": "Renvoie le résultat binaire « Et » de deux nombres"}, "BITLSHIFT": {"a": "(nombre; total_décalé)", "d": "Renvoie un nombre décalé vers la gauche de total_décalage bits"}, "BITOR": {"a": "(nombre1; nombre2)", "d": "Renvoie le résultat binaire « Ou » de deux nombres"}, "BITRSHIFT": {"a": "(nombre; total_décalé)", "d": "Renvoie un nombre décalé vers la droite de total_décalage bits"}, "BITXOR": {"a": "(nombre1; nombre2)", "d": "Renvoie Renvoie le résultat binaire « Ou exclusif » de deux nombres"}, "COMPLEX": {"a": "(partie_réelle; partie_imaginaire; [suffixe])", "d": "Renvoie un nombre complexe construit à partir de ses parties réelle et imaginaire"}, "CONVERT": {"a": "(nombre; de_unité; à_unité)", "d": "Convertit un nombre d'une unité à une autre unité"}, "DEC2BIN": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre décimal en nombre binaire"}, "DEC2HEX": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre décimal en nombre hexadécimal"}, "DEC2OCT": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre décimal en nombre octal"}, "DELTA": {"a": "(nombre1; [nombre2])", "d": "Teste l'égalité de deux nombres"}, "ERF": {"a": "(limite_inf; [limite_sup])", "d": "Renvoie la fonction d'erreur"}, "ERF.PRECISE": {"a": "(X)", "d": "Renvoie la fonction d’erreur"}, "ERFC": {"a": "(x)", "d": "Renvoie la fonction d'erreur complémentaire"}, "ERFC.PRECISE": {"a": "(X)", "d": "Renvoie la fonction d’erreur complémentaire"}, "GESTEP": {"a": "(nombre; [seuil])", "d": "Vérifie si un nombre dépasse une valeur seuil"}, "HEX2BIN": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre hexadécimal en nombre binaire"}, "HEX2DEC": {"a": "(nombre)", "d": "Convertit un nombre hexadécimal en nombre décimal"}, "HEX2OCT": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre hexadécimal en nombre octal"}, "IMABS": {"a": "(nombre_complexe)", "d": "Renvoie le module d'un nombre complexe"}, "IMAGINARY": {"a": "(nombre_complexe)", "d": "Renvoie la partie imaginaire d'un nombre complexe"}, "IMARGUMENT": {"a": "(nombre_complexe)", "d": "Renvoie l'argument thêta, un angle exprimé en radians"}, "IMCONJUGATE": {"a": "(nombre_complexe)", "d": "Renvoie le conjugué d'un nombre complexe"}, "IMCOS": {"a": "(nombre_complexe)", "d": "Ren<PERSON>ie le cosinus d'un nombre complexe"}, "IMCOSH": {"a": "(nombre_complexe)", "d": "Renvoie le cosinus hyperbolique d'un nombre complexe"}, "IMCOT": {"a": "(nombre_complexe)", "d": "Renvoie la cotangente d'un nombre complexe"}, "IMCSC": {"a": "(nombre_complexe)", "d": "Renvoie la cosécante d'un nombre complexe"}, "IMCSCH": {"a": "(nombre_complexe)", "d": "Renvoie la cosécante hyperbolique d'un nombre complexe"}, "IMDIV": {"a": "(nombre_complexe1; nombre_complexe2)", "d": "Renvoie le quotient de deux nombres complexes"}, "IMEXP": {"a": "(nombre_complexe)", "d": "Donne l'exposant d'un nombre complexe"}, "IMLN": {"a": "(nombre_complexe)", "d": "Donne le logarithme népérien d'un nombre complexe"}, "IMLOG10": {"a": "(nombre_complexe)", "d": "Calcule le logarithme en base 10 d'un nombre complexe"}, "IMLOG2": {"a": "(nombre_complexe)", "d": "Calcule le logarithme en base 2 d'un nombre complexe"}, "IMPOWER": {"a": "(nombre_complexe; nombre)", "d": "Renvoie la valeur du nombre complexe élevé à une puissance entière"}, "IMPRODUCT": {"a": "(nb_comp1; [nb_comp2]; ...)", "d": "Renvoie le produit de 1 à 255 nombres complexes"}, "IMREAL": {"a": "(nombre_complexe)", "d": "Renvoie la partie réelle d'un nombre complexe"}, "IMSEC": {"a": "(nombre_complexe)", "d": "Renvoie la sécante d'un nombre complexe"}, "IMSECH": {"a": "(nombre_complexe)", "d": "Renvoie la sécante hyperbolique d'un nombre complexe"}, "IMSIN": {"a": "(nombre_complexe)", "d": "Renvoie le sinus d'un nombre complexe"}, "IMSINH": {"a": "(nombre_complexe)", "d": "Renvoie le sinus hyperbolique d'un nombre complexe"}, "IMSQRT": {"a": "(nombre_complexe)", "d": "Extrait la racine carrée d'un nombre complexe"}, "IMSUB": {"a": "(nombre_complexe1; nombre_complexe2)", "d": "Calcule la différence entre deux nombres complexes"}, "IMSUM": {"a": "(nb_comp1; [nb_comp2]; ...)", "d": "Renvoie la somme de nombres complexes"}, "IMTAN": {"a": "(nombre_complexe)", "d": "Renvoie la tangente d'un nombre complexe"}, "OCT2BIN": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre octal en nombre binaire"}, "OCT2DEC": {"a": "(nombre)", "d": "Convertit un nombre octal en nombre décimal"}, "OCT2HEX": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre octal en nombre hexadécimal"}, "DAVERAGE": {"a": "(base_de_données; champ; critères)", "d": "Don<PERSON> la moyenne des valeurs dans la colonne d'une liste ou une base de données qui correspondent aux conditions que vous avez spécifiées"}, "DCOUNT": {"a": "(base_de_données; champ; critères)", "d": "Compte le nombre de cellules contenant des valeurs numériques satisfaisant les critères spécifiés pour la base de données précisée"}, "DCOUNTA": {"a": "(base_de_données; champ; critères)", "d": "Compte le nombre de cellules non vides dans le champ (colonne) d'enregistrements dans la base de données correspondant aux conditions spécifiées"}, "DGET": {"a": "(base_de_données; champ; critères)", "d": "Extrait d'une base de données l'enregistrement qui correspond aux conditions spécifiées"}, "DMAX": {"a": "(base_de_données; champ; critères)", "d": "Donne la valeur la plus élevée dans le champ (colonne) des enregistrements de la base de données correspondant aux conditions que vous avez spécifiées."}, "DMIN": {"a": "(base_de_données; champ; critères)", "d": "Donne la valeur la moins élevée du champ (colonne) d'enregistrements de la base de données correspondant aux conditions définies"}, "DPRODUCT": {"a": "(base_de_données; champ; critères)", "d": "Multiplie les valeurs dans le champ (colonne) d'enregistrements de la base de données correspondant aux conditions que vous avez spécifiées"}, "DSTDEV": {"a": "(base_de_données; champ; critères)", "d": "Évalue l'écart-type à partir d'un échantillon de population représenté par les entrées de base de données sélectionnées"}, "DSTDEVP": {"a": "(base_de_données; champ; critères)", "d": "Calcule l'écart-type à partir de la population entière représentée par les entrées de base de données sélectionnées"}, "DSUM": {"a": "(base_de_données; champ; critères)", "d": "Additionne les nombres se trouvant dans le champ (colonne) d'enregistrements de la base de données correspondant aux conditions que vous avez spécifiées"}, "DVAR": {"a": "(base_de_données; champ; critères)", "d": "Évalue la variance à partir d'un échantillon de population représenté par des entrées de base de données sélectionnées"}, "DVARP": {"a": "(base_de_données; champ; critères)", "d": "Calcule la variance à partir de la population entière représentée par les entrées de base de données sélectionnées"}, "CHAR": {"a": "(nombre)", "d": "Renvoie le caractère spécifié par le code numérique du jeu de caractères de votre ordinateur"}, "CLEAN": {"a": "(texte)", "d": "Supprime tous les caractères de contrôle du texte"}, "CODE": {"a": "(texte)", "d": "Renvoie le numéro de code du premier caractère du texte, dans le jeu de caractères utilisé par votre ordinateur"}, "CONCATENATE": {"a": "(texte1; [texte2]; ...)", "d": "Assemble plusieurs chaînes de caractères de façon à n'en former qu'une"}, "CONCAT": {"a": "(texte1; ...)", "d": "Assemble une liste ou une plage de chaînes de caractères"}, "DOLLAR": {"a": "(nombre; [décimales])", "d": "Convertit un nombre en texte en utilisant le format monétaire"}, "EXACT": {"a": "(texte1; texte2)", "d": "Compare deux chaînes textuelles et renvoie VRAI si elles sont parfaitement identiques et FAUX si elles sont différentes (distinction majuscules/minuscules)"}, "FIND": {"a": "(texte_cherché; texte; [no_départ])", "d": "Renvoie la position de départ d'une chaîne de texte à l'intérieur d'une autre chaîne de texte. TROUVE distingue les majuscules des minuscules"}, "FINDB": {"a": "(texte_cherché; texte; [no_départ])", "d": "Recherche la sous-chaîne spécifiée (texte_cherché) dans une chaîne (texte) et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc."}, "FIXED": {"a": "(nombre; [décimales]; [no_séparateur])", "d": "Arrondit un nombre au nombre de décimales spécifié et renvoie le résultat sous forme de texte avec ou sans virgule"}, "LEFT": {"a": "(texte; [no_car])", "d": "Extrait le(s) premier(s) caractère(s) à l'extrême gauche d'une chaîne de texte"}, "LEFTB": {"a": "(texte; [no_car])", "d": "Extrait la sous-chaîne d’une chaîne spécifiée à partir du caractère de gauche et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc."}, "LEN": {"a": "(texte)", "d": "Renvoie le nombre de caractères contenus dans une chaîne de texte"}, "LENB": {"a": "(texte)", "d": "Analyse la chaîne spécifiée et renvoyer le nombre de caractères qu’elle contient et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc."}, "LOWER": {"a": "(texte)", "d": "Convertit toutes les lettres majuscules en une chaîne de caractères en minuscules"}, "MID": {"a": "(texte; no_départ; no_car)", "d": "Renvoie un nombre déterminé de caractères d'une chaîne de texte à partir de la position que vous indiquez"}, "MIDB": {"a": "(texte; no_départ; no_car)", "d": "Extrait les caractères d’une chaîne spécifiée à partir de n’importe quelle position et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc."}, "NUMBERVALUE": {"a": "(texte; [séparateur_décimal]; [séparateur_groupe])", "d": "Convertit le texte en nombre quels que soient les paramètres régionaux"}, "PROPER": {"a": "(texte)", "d": "Met en majuscule la première lettre de chaque mot dans une chaîne de texte et met toutes les autres lettres en minuscules"}, "REPLACE": {"a": "(ancien_texte; no_départ; no_car; nouveau_texte)", "d": "Remplace une chaîne de caractères par une autre"}, "REPLACEB": {"a": "(ancien_texte; no_départ; no_car; nouveau_texte)", "d": "Remplace un jeu de caractères, en fonction du nombre de caractères et de la position de départ que vous spécifiez, avec un nouvel ensemble de caractères et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc."}, "REPT": {"a": "(texte; no_fois)", "d": "Répète un texte un certain nombre de fois. Utilisez REPT pour remplir une cellule avec un certain nombre d'occurrences d'une chaîne de caractères"}, "RIGHT": {"a": "(texte; [no_car])", "d": "Extrait les derniers caractères de la fin d'une chaîne de texte"}, "RIGHTB": {"a": "(texte; [no_car])", "d": "Extrait une sous-chaîne d'une chaîne à partir du caractère le plus à droite, en fonction du nombre de caractères spécifié et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc."}, "SEARCH": {"a": "(texte_cherché; texte; [no_départ])", "d": "Renvoie le numéro du caractère au niveau duquel est trouvé un caractère ou le début d'une chaîne de caractères, en lisant de la gauche à droite (pas de distinction entre majuscules et minuscules)"}, "SEARCHB": {"a": "(texte_cherché; texte; [no_départ])", "d": "Renvoie l'emplacement de la sous-chaîne spécifiée dans une chaîne et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc."}, "SUBSTITUTE": {"a": "(texte; ancien_texte; nouveau_texte; [no_position])", "d": "Remplace des caractères dans un texte"}, "T": {"a": "(valeur)", "d": "Contrôle si une valeur fait référence à du texte et renvoie le texte le cas échéant, ou renvoie des guillemets (texte vide) dans le cas contraire"}, "TEXT": {"a": "(valeur; format_text)", "d": "Convertit une valeur en texte en un format de nombre spécifique"}, "TEXTJOIN": {"a": "(dé<PERSON>iteur; ignorer_vide; texte1; ...)", "d": "Assemble une liste ou plage de chaînes de caractères à l’aide d’un délimiteur"}, "TRIM": {"a": "(texte)", "d": "Supprime tous les espaces d'une chaîne de caractères, sauf les espaces simples entre les mots"}, "UNICHAR": {"a": "(nombre)", "d": "Renvoie le caractère Unicode référencé par la valeur numérique donnée"}, "UNICODE": {"a": "(texte)", "d": "Renvoie le nombre (point de code) correspondant au premier caractère du texte"}, "UPPER": {"a": "(texte)", "d": "Convertit une chaîne de caractères en majuscules"}, "VALUE": {"a": "(texte)", "d": "Convertit une chaîne textuelle représentant un nombre en un nombre"}, "AVEDEV": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne des écarts absolus des points de données par rapport à leur moyenne arithmétique. Les arguments peuvent être des nombres, des noms, des matrices ou des références qui contiennent des nombres"}, "AVERAGE": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne (espérance arithmétique) des arguments, qui peuvent être des nombres, des noms, des matrices, ou des références contenant des nombres"}, "AVERAGEA": {"a": "(valeur1; [valeur2]; ...)", "d": "Renvoie la moyenne (moyenne arithmétique) de ses arguments, en considérant que le texte et la valeur logique FAUX dans les arguments = 0 ; VRAI = 1. Les arguments peuvent être des nombres, des noms, des matrices ou des références"}, "AVERAGEIF": {"a": "(plage; critères; [plage_moyenne])", "d": "Détermine la moyenne (espérance arithmétique) des cellules satisfaisant une condition ou des critères particuliers"}, "AVERAGEIFS": {"a": "(plage_moyenne; plage_critères; critères; ...)", "d": "Détermine la moyenne (espérance arithmétique) des cellules spécifiées par un ensemble de conditions ou de critères"}, "BETADIST": {"a": "(x; alpha; bêta; [A]; [B])", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi de probabilité Bêta"}, "BETAINV": {"a": "(probabilité; alpha; bêta; [A]; [B])", "d": "Renvoie l’inverse de la fonction de densité de distribution de la probabilité suivant une loi bêta cumulée (LOI.BÊTA)"}, "BETA.DIST": {"a": "(x; alpha; bêta; cumulative; [A]; [B])", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de probabilité Bêta"}, "BETA.INV": {"a": "(probabilité; alpha; bêta; [A]; [B])", "d": "Renvoie l’inverse de la fonction de densité de distribution de la probabilité suivant une loi bêta cumulée (LOI.BETA.N)"}, "BINOMDIST": {"a": "(nombre_succès; tirages; probabilité_succès; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant la loi binomiale"}, "BINOM.DIST": {"a": "(nombre_succès; tirages; probabilité_succès; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant la loi binomiale"}, "BINOM.DIST.RANGE": {"a": "(tirages; probabilité_succès; nombre_succès; [nombre_succès2])", "d": "Renvoie la probabilité d'un résultat de tirage en suivant une distribution binomiale"}, "BINOM.INV": {"a": "(tirages; probabilité_succès; alpha)", "d": "Renvoie la plus petite valeur pour laquelle la distribution binomiale cumulée est supérieure ou égale à une valeur critère"}, "CHIDIST": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité unilatérale à droite d’une variable aléatoire continue suivant une loi du Khi-deux"}, "CHIINV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité unilatérale à droite donnée, la valeur d’une variable aléatoire suivant une loi du <PERSON>hi-deux"}, "CHITEST": {"a": "(plage_réelle; plage_attendue)", "d": "Renvoie le test d’indépendance : la valeur pour la statistique suivant la loi du Khi-deux et les degrés de liberté appropriés"}, "CHISQ.DIST": {"a": "(x; degrés_liberté; cumulative)", "d": "Renvoie la probabilité unilatérale à gauche d’une variable aléatoire continue suivant une loi du Khi-deux"}, "CHISQ.DIST.RT": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité unilatérale à droite d’une variable aléatoire continue suivant une loi du Khi-deux"}, "CHISQ.INV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité unilatérale à gauche donnée, la valeur d’une variable aléatoire suivant une loi du <PERSON>hi-deux"}, "CHISQ.INV.RT": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité unilatérale à droite donnée, la valeur d’une variable aléatoire suivant une loi du <PERSON>hi-deux"}, "CHISQ.TEST": {"a": "(plage_réelle; plage_prévue)", "d": "Renvoie le test d’indépendance : la valeur de la distribution Khi-deux pour la statistique et les degrés de liberté appropriés"}, "CONFIDENCE": {"a": "(alpha; écart_type; taille)", "d": "Renvoie l’intervalle de confiance pour la moyenne d’une population à l’aide d’une distribution normale. Consultez l’aide sur l’équation utilisée"}, "CONFIDENCE.NORM": {"a": "(alpha; écart_type; taille)", "d": "Renvoie l’intervalle de confiance pour la moyenne d’une population, à l’aide d’une loi normale"}, "CONFIDENCE.T": {"a": "(alpha; écart_type; taille)", "d": "Renvoie l’intervalle de confiance pour la moyenne d’une population, à l’aide de la probabilité d’une variable aléatoire suivant une loi T de Student"}, "CORREL": {"a": "(matrice1; matrice2)", "d": "Renvoie le coefficient de corrélation entre deux séries de données"}, "COUNT": {"a": "(valeur1; [valeur2]; ...)", "d": "<PERSON><PERSON><PERSON>mine le nombre de cellules d'une plage contenant des nombres"}, "COUNTA": {"a": "(valeur1; [valeur2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> le nombre de cellules d'une plage qui ne sont pas vides"}, "COUNTBLANK": {"a": "(plage)", "d": "Compte le nombre de cellules vides à l'intérieur d'une plage spécifique"}, "COUNTIF": {"a": "(plage; critère)", "d": "Détermine le nombre de cellules non vides répondant à la condition à l'intérieur d'une plage"}, "COUNTIFS": {"a": "(plage_critères; critères; ...)", "d": "Compte le nombre de cellules spécifiées par un ensemble de conditions ou de critères"}, "COVAR": {"a": "(matrice1; matrice2)", "d": "Renvoie la covariance, moyenne du produit des écarts à la moyenne de chaque paire de points de deux séries"}, "COVARIANCE.P": {"a": "(matrice1; matrice2)", "d": "Renvoie la covariance de population, moyenne du produit des écarts à la moyenne de chaque paire de points de deux séries"}, "COVARIANCE.S": {"a": "(matrice1; matrice2)", "d": "Renvoie la covariance d’échantillon, moyenne du produit des écarts à la moyenne de chaque paire de points de deux séries"}, "CRITBINOM": {"a": "(tirages; probabilité_succès; alpha)", "d": "Renvoie la plus petite valeur pour laquelle la distribution binomiale cumulée est supérieure ou égale à une valeur critère"}, "DEVSQ": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la somme des carrés des écarts entre les points de données et leur moyenne échantillonnée"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi exponentielle. Consultez l’aide sur l’équation utilisée"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi exponentielle. Consultez l’aide sur l’équation utilisée"}, "FDIST": {"a": "(x; degrés_liberté1; degrés_liberté2)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi F pour deux séries de données"}, "FINV": {"a": "(probabilité; degrés_liberté1; degrés_liberté2)", "d": "Renvoie l’inverse de la distribution de probabilité (unilatérale à droite) suivant une loi F : si p = LOI.F (x,...), alors INVERSE.LOI.F (p,...) = x"}, "FTEST": {"a": "(matrice1; matrice2)", "d": "Renvoie le résultat d’un test F, c’est-à-dire la probabilité d’une variable aléatoire continue que les variances dans Matrice1 et Matrice2 ne soient pas différentes de manière significative"}, "F.DIST": {"a": "(x; degrés_liberté1; degrés_liberté2; cumulée)", "d": "Renvoie la probabilité (unilatérale à gauche) d’une variable aléatoire suivant une loi F pour deux séries de données"}, "F.DIST.RT": {"a": "(x; degrés_liberté1; degrés_liberté2)", "d": "Renvoie la probabilité (unilatérale à droite) d’une variable aléatoire suivant une loi F pour deux séries de données"}, "F.INV": {"a": "(probabilité; degrés_liberté1; degrés_liberté2)", "d": "Renvoie l’inverse de la distribution de probabilité (unilatérale à gauche) suivant une loi F : si p = LOI.F (x,...), alors INVERSE.LOI.F.N (p,...) = x"}, "F.INV.RT": {"a": "(probabilité; degrés_liberté1; degrés_liberté2)", "d": "Renvoie l’inverse de la distribution de probabilité (unilatérale à droite) suivant une loi F : si p = LOI.F.DROITE (x,...), alors INVERSE.LOI.F.DROITE (p,...) = x"}, "F.TEST": {"a": "(matrice1; matrice2)", "d": "Renvoie le résultat d’un test F, la probabilité bilatérale que les variances des matrice1 et matrice2 ne sont pas très différentes"}, "FISHER": {"a": "(x)", "d": "Renvoie la transformation de Fisher. Consultez l'aide sur l'équation utilisée"}, "FISHERINV": {"a": "(y)", "d": "Renvoie la transformation de Fisher inverse : si y = FISHER(x), alors FISHER.INVERSE(y) = x. <PERSON><PERSON>z l'aide sur l'équation utilisée"}, "FORECAST": {"a": "(x; y_connus; x_connus)", "d": "<PERSON><PERSON>, ou prédit, une valeur future suivant une tendance linéaire, en utilisant les valeurs existantes"}, "FORECAST.ETS": {"a": "(date_cible; valeurs; chronologie; [caractère_saisonnier]; [achèvement_données]; [agrégation])", "d": "Renvoie la valeur prévue pour une date cible ultérieure spécifiée à l’aide de la méthode de lissage exponentiel."}, "FORECAST.ETS.CONFINT": {"a": "(date_cible; valeurs; chronologie; [niveau_confiance]; [caractère_saisonnier]; [achèvement_données]; [agrégation])", "d": "Renvoie un intervalle de confiance pour la valeur de prévision à la date cible spécifiée."}, "FORECAST.ETS.SEASONALITY": {"a": "(valeurs; chronologie; [achèvement_données]; [agrégation])", "d": "Renvoie la longueur du motif répétitif détecté par l'application pour la série temporelle spécifiée."}, "FORECAST.ETS.STAT": {"a": "(valeurs; chronologie; type_statistique; [caractère_saisonnier]; [achèvement_données]; [agrégation])", "d": "Renvoie les statistiques demandées pour la prévision."}, "FORECAST.LINEAR": {"a": "(x; y_connus; x_connus)", "d": "<PERSON><PERSON>, ou prédit, une valeur future suivant une tendance linéaire, en utilisant les valeurs existantes"}, "FREQUENCY": {"a": "(tableau_données; matrice_classes)", "d": "Calcule la fréquence à laquelle les valeurs apparaissent dans une plage de valeurs, puis renvoie une matrice verticale de nombres ayant un élément de plus que l’argument matrice_classes"}, "GAMMA": {"a": "(x)", "d": "Renvoie la valeur de la fonction Gamma"}, "GAMMADIST": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi Gamma. Consultez l’aide sur l’équation utilisée"}, "GAMMA.DIST": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi Gamma"}, "GAMMAINV": {"a": "(probabilité; alpha; bêta)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi Gamma : si p = LOI.GAMMA(x,...), alors LOI.GAMMA.INVERSE(p,...) = x"}, "GAMMA.INV": {"a": "(probabilité; alpha; bêta)", "d": "<PERSON><PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi Gamma: si p = LOI.GAMMA.N(x,...), alors LOI.GAMMA.INVERSE.N(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Renvoie le logarithme népérien de la fonction Gamma. Consultez l'aide pour obtenir des informations sur l'équation utilisée"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Renvoie le logarithme népérien de la fonction Gamma"}, "GAUSS": {"a": "(x)", "d": "Renvoie 0,5 de moins que la distribution cumulée suivant une loi normale centrée réduite"}, "GEOMEAN": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne géométrique d'une matrice ou d'une plage de données numériques positives"}, "GROWTH": {"a": "(y_connus; [x_connus]; [x_nouveaux]; [constante])", "d": "Calcule les valeurs de la tendance géométrique exponentielle à partir de valeurs connues"}, "HARMEAN": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne harmonique d'une série de données en nombres positifs : la réciproque de la moyenne arithmétique des réciproques"}, "HYPGEOM.DIST": {"a": "(succès_échantillon; nombre_échantillon; succès_population; nombre_population; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant une loi hypergéométrique"}, "HYPGEOMDIST": {"a": "(succès_échantillon; nombre_échantillon; succès_population; nombre_population)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant une loi hypergéométrique"}, "INTERCEPT": {"a": "(y_connus; x_connus)", "d": "Calcule le point auquel une droite va croiser l’axe des y en traçant une droite de régression linéaire d’après les valeurs connues de x et de y"}, "KURT": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le kurtosis d'une série de données. Consultez l'aide sur l'équation utilisée"}, "LARGE": {"a": "(matrice; k)", "d": "Renvoie la k-ième plus grande valeur d'une série de données"}, "LINEST": {"a": "(y_connus; [x_connus]; [constante]; [statistiques])", "d": "Renvoie une matrice qui décrit une droite de corrélation pour vos données, calculée avec la méthode des moindres carrés"}, "LOGEST": {"a": "(y_connus; [x_connus]; [constante]; [statistiques])", "d": "Renvoie des statistiques qui décrivent une courbe de corrélation exponentielle à partir de valeurs connues"}, "LOGINV": {"a": "(probabilité; espérance; écart_type)", "d": "Renvoie l’inverse de la fonction de distribution de x suivant une loi lognormale cumulée, où In(x) est normalement distribué avec les paramètres Espérance et Écart_type"}, "LOGNORM.DIST": {"a": "(x; espérance; écart_type; cumulative)", "d": "Renvoie la fonction de distribution de x suivant une loi lognormale, où In(x) est normalement distribué avec les paramètres Espérance et Écart_type"}, "LOGNORM.INV": {"a": "(probabilité; espérance; écart_type)", "d": "Renvoie l’inverse de la fonction de distribution de x suivant une loi lognormale cumulée, où In(x) est normalement distribué avec les paramètres Espérance et Écart_type"}, "LOGNORMDIST": {"a": "(x; espérance; écart_type)", "d": "Renvoie la distribution de x suivant une loi lognormale cumulée, où ln(x) est normalement distribué avec les paramètres Espérance et Écart_type"}, "MAX": {"a": "(nombre1; [nombre2]; ...)", "d": "Donne la valeur la plus grande parmi une liste de valeurs. Ignore les valeurs logiques et le texte"}, "MAXA": {"a": "(valeur1; [valeur2]; ...)", "d": "Renvoie le plus grand nombre d'un ensemble de valeurs. Prend en compte les valeurs logiques et le texte"}, "MAXIFS": {"a": "(plage_max; plage_critères; critères; ...)", "d": "Renvoie la valeur maximale parmi les cellules spécifiées par un ensemble de conditions ou de critères donné"}, "MEDIAN": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la valeur médiane ou le nombre qui se trouve au milieu d'une liste de nombres fournie"}, "MIN": {"a": "(nombre1; [nombre2]; ...)", "d": "Donne la valeur la plus petite parmi une liste de valeurs. Ignore les valeurs logiques et le texte"}, "MINA": {"a": "(valeur1; [valeur2]; ...)", "d": "Renvoie la plus petite valeur d'un ensemble de données. Prend en compte des valeurs logiques et le texte"}, "MINIFS": {"a": "(plage_min; plage_critères; critères; ...)", "d": "Renvoie la valeur minimale parmi les cellules spécifiées par un ensemble de conditions ou de critères donné"}, "MODE": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la valeur la plus fréquente d’une série de données"}, "MODE.MULT": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie une matrice verticale des valeurs les plus fréquentes d’une matrice ou série de données. Pour une matrice horizontale, utilisez =TRANSPOSER(MODE.MULTIPLE(nombre1,nombre2,...))"}, "MODE.SNGL": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la valeur la plus fréquente d’une série de données"}, "NEGBINOM.DIST": {"a": "(nombre_échecs; nombre_succès; probabilité_succès; cumulative)", "d": "Renvoie la distribution négative binomiale, probabilité d’obtenir un nombre d’échecs égal à Nombre_échecs avant le succès numéro Nombre_succès, avec une probabilité égale à Probabilité_succès"}, "NEGBINOMDIST": {"a": "(nombre_échecs; nombre_succès; probabilité_succès)", "d": "Renvoie la distribution négative binomiale, probabilité d’obtenir un nombre d’échecs égal à Nombre_échecs avant le succès numéro Nombre_succès, avec une probabilité égale à Probabilité_succès"}, "NORM.DIST": {"a": "(x; espérance; écart_type; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi normale pour l’espérance arithmétique et l’écart-type spécifiés"}, "NORMDIST": {"a": "(x; espérance; écart_type; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi normale pour l’espérance arithmétique et l’écart-type spécifiés"}, "NORM.INV": {"a": "(probabilité; espérance; écart_type)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale pour la moyenne et l’écart-type spécifiés"}, "NORMINV": {"a": "(probabilité; espérance; écart_type)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale pour la moyenne et l’écart-type spécifiés"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "Renvoie la distribution suivant une loi normale centrée réduite (d’espérance nulle et d’écart-type égal à 1)"}, "NORMSDIST": {"a": "(z)", "d": "Renvoie la distribution cumulée suivant une loi normale centrée réduite (d’espérance nulle et d’écart-type égal à 1)"}, "NORM.S.INV": {"a": "(probabilité)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale standard (ou centrée réduite), c’est-à-dire ayant une moyenne de zéro et un écart-type de 1"}, "NORMSINV": {"a": "(probabilité)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale standard (ou centrée réduite), c’est-à-dire ayant une moyenne de zéro et un écart-type de 1"}, "PEARSON": {"a": "(matrice1; matrice2)", "d": "Renvoie le coefficient de corrélation d'échantillonnage de Pearson, r. <PERSON><PERSON><PERSON> l'aide sur l'équation utilisée"}, "PERCENTILE": {"a": "(matrice; k)", "d": "Renvoie le k-ième centile des valeurs d’une plage"}, "PERCENTILE.EXC": {"a": "(matrice; k)", "d": "Renvoie le k-ième centile des valeurs d’une plage, où k se trouve dans la plage de 0 à 1, non compris"}, "PERCENTILE.INC": {"a": "(matrice; k)", "d": "Renvoie le k-ième centile des valeurs d’une plage, où k se trouve dans la plage de 0 à 1, compris"}, "PERCENTRANK": {"a": "(matrice; x; [précision])", "d": "Renvoie le rang en pourcentage d’une valeur d’une série de données"}, "PERCENTRANK.EXC": {"a": "(matrice; x; [précision])", "d": "Renvoie le rang en pourcentage (0..1, non compris) d’une valeur d’une série de données"}, "PERCENTRANK.INC": {"a": "(matrice; x; [précision])", "d": "Renvoie le rang en pourcentage (0..1, inclus) d’une valeur d’une série de données"}, "PERMUT": {"a": "(nombre; nombre_choisi)", "d": "Renvoie le nombre de permutations pour un nombre donné d'objets"}, "PERMUTATIONA": {"a": "(nombre; nombre_choisi)", "d": "Renvoie le nombre de permutations pour un nombre donné d'objets (avec répétitions)"}, "PHI": {"a": "(x)", "d": "Renvoie la valeur de la fonction de densité pour une distribution suivant une loi normale centrée réduite"}, "POISSON": {"a": "(x; espérance; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de Poisson"}, "POISSON.DIST": {"a": "(x; espérance; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de Poisson"}, "PROB": {"a": "(plage_x; plage_probabilité; limite_inf; [limite_sup])", "d": "Renvoie la probabilité pour les valeurs d'une plage d'être comprises entre deux limites ou égales à une limite inférieure"}, "QUARTILE": {"a": "(matrice; quart)", "d": "Renvoie le quartile d’une série de données"}, "QUARTILE.INC": {"a": "(matrice; quart)", "d": "Renvoie le quartile d’une série de données, d’après des valeurs de centile comprises entre 0 et 1 inclus"}, "QUARTILE.EXC": {"a": "(matrice; quart)", "d": "Renvoie le quartile d’une série de données, d’après des valeurs de centile comprises entre 0 et 1 non compris"}, "RANK": {"a": "(nombre; référence; [ordre])", "d": "Renvoie le rang d’un nombre dans une liste d’arguments : sa taille est relative aux autres valeurs de la liste"}, "RANK.AVG": {"a": "(nombre; référence; [ordre])", "d": "Renvoie le rang d’un nombre dans une liste d’arguments : sa taille est relative aux autres valeurs de la liste ; si plusieurs valeurs sont associées au même rang, renvoie le rang supérieur de ce jeu de valeurs"}, "RANK.EQ": {"a": "(nombre; référence; [ordre])", "d": "Renvoie le rang d’un nombre dans une liste d’arguments : sa taille est relative aux autres valeurs de la liste ; si plusieurs valeurs sont associées au même rang, renvoie le rang supérieur de ce jeu de valeurs"}, "RSQ": {"a": "(y_connus; x_connus)", "d": "Renvoie la valeur du coefficient de détermination R^2 d’une régression linéaire"}, "SKEW": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie l'asymétrie d'une distribution : la caractérisation du degré d'asymétrie d'une distribution par rapport à sa moyenne"}, "SKEW.P": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie l'asymétrie d'une distribution basée sur une population : la caractérisation du degré d'asymétrie d'une distribution par rapport à sa moyenne"}, "SLOPE": {"a": "(y_connus; x_connus)", "d": "Renvoie la pente d’une droite de régression linéaire"}, "SMALL": {"a": "(matrice; k)", "d": "Renvoie la k-ième plus petite valeur d'une série de données"}, "STANDARDIZE": {"a": "(x; espérance; écart_type)", "d": "Renvoie une valeur centrée réduite, depuis une distribution caractérisée par une moyenne et un écart-type"}, "STDEV": {"a": "(nombre1; [nombre2]; ...)", "d": "Évalue l’écart-type d’une population en se basant sur un échantillon (ignore les valeurs logiques et le texte de l’échantillon)"}, "STDEV.P": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule l’écart-type d’une population entière sous forme d’arguments (ignore les valeurs logiques et le texte)"}, "STDEV.S": {"a": "(nombre1; [nombre2]; ...)", "d": "Évalue l’écart-type d’une population en se basant sur un échantillon (ignore les valeurs logiques et le texte de l’échantillon)"}, "STDEVA": {"a": "(valeur1; [valeur2]; ...)", "d": "Évalue l'écart-type d'une population en se basant sur un échantillon, incluant les valeurs logiques et le texte. Le texte et les valeurs logiques évalués en tant que FAUX = 0 ; les valeurs logiques évaluées en tant que VRAI = 1"}, "STDEVP": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule l’écart-type d’une population entière sous forme d’arguments (en ignorant les valeurs logiques et le texte)"}, "STDEVPA": {"a": "(valeur1; [valeur2]; ...)", "d": "Calcule l'écart-type d'une population entière, en incluant les valeurs logiques et le texte. Le texte et les valeurs logiques évalués en tant que FAUX = 0 ; les valeurs logiques évaluées en tant que VRAI = 1"}, "STEYX": {"a": "(y_connus; x_connus)", "d": "Renvoie l’erreur type de la valeur y prévue pour chaque x de la régression"}, "TDIST": {"a": "(x; degré<PERSON>_liberté; uni/bilatéral)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi T de Student"}, "TINV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur inverse bilatérale d’une variable aléatoire suivant une loi T de Student"}, "T.DIST": {"a": "(x; degrés_liberté; cumulative)", "d": "Renvoie la probabilité unilatérale à gauche d’une variable aléatoire suivant une loi T de Student"}, "T.DIST.2T": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité bilatérale d’une variable aléatoire suivant une loi T de Student"}, "T.DIST.RT": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité unilatérale à droite d’une variable aléatoire suivant une loi T de Student"}, "T.INV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur inverse unilatérale à gauche d’une variable aléatoire suivant une loi T de Student"}, "T.INV.2T": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur inverse bilatérale d’une variable aléatoire suivant une loi T de Student"}, "T.TEST": {"a": "(matrice1; matrice2; uni/bilatéral; type)", "d": "Renvoie la probabilité associée à un test T de Student"}, "TREND": {"a": "(y_connus; [x_connus]; [x_nouveaux]; [constante])", "d": "Calcule les valeurs de la courbe de tendance linéaire par la méthode des moindres carrés, appliquée aux valeurs connues"}, "TRIMMEAN": {"a": "(matrice; pourcentage)", "d": "Renvoie la moyenne de la partie intérieure d'une série de valeurs données"}, "TTEST": {"a": "(matrice1; matrice2; uni/bilatéral; type)", "d": "Renvoie la probabilité associée à un test T de Student"}, "VAR": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la variance en se basant sur un échantillon (en ignorant les valeurs logiques et le texte de l’échantillon)"}, "VAR.P": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la variance d’une population entière (ignore les valeurs logiques et le texte de la population)"}, "VAR.S": {"a": "(nombre1; [nombre2]; ...)", "d": "Estime la variance en se basant sur un échantillon (ignore les valeurs logiques et le texte de l’échantillon)"}, "VARA": {"a": "(valeur1; [valeur2]; ...)", "d": "Estime la variance d'une population en se basant sur un échantillon, texte et valeurs logiques inclus. Le texte et la valeur logique évalués FAUX = 0 ; la valeur logique VRAI = 1"}, "VARP": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la variance d’une population entière (en ignorant les valeurs logiques et le texte de la population)"}, "VARPA": {"a": "(valeur1; [valeur2]; ...)", "d": "Calcule la variance d'une population en se basant sur une population entière, en incluant le texte et les valeurs logiques. Le texte et les valeurs logiques FAUX ont la valeur 0 ; la valeur logique VRAI = 1"}, "WEIBULL": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de <PERSON>. Consultez l’aide sur l’équation utilisée"}, "WEIBULL.DIST": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de <PERSON>"}, "Z.TEST": {"a": "(tableau; x; [sigma])", "d": "Renvoie la valeur P unilatérale d’un test Z"}, "ZTEST": {"a": "(matrice; x; [sigma])", "d": "Renvoie la valeur unilatérale P du test Z"}, "ACCRINT": {"a": "(émission; prem_coupon; règlement; taux; val_nominale; fréquence; [base]; [méth_calcul])", "d": "Renvoie l'intérêt couru non échu d'un titre dont l'intérêt est perçu périodiquement"}, "ACCRINTM": {"a": "(émission; échéance; taux; val_nominale; [base])", "d": "Renvoie l'intérêt couru non échu d'un titre dont l'intérêt est perçu à l'échéance"}, "AMORDEGRC": {"a": "(coût; date_achat; première_période; val_résiduelle; périodicité; taux; [base])", "d": "Renvoie l'amortissement linéaire proportionnel d'un bien pour chaque période comptable."}, "AMORLINC": {"a": "(coût; date_achat; première_période; val_résiduelle; périodicité; taux; [base])", "d": "Renvoie l'amortissement linéaire proportionnel d'un bien pour chaque période comptable."}, "COUPDAYBS": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule le nombre de jours entre le début de la période de coupon et la date de liquidation"}, "COUPDAYS": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Affiche le nombre de jours pour la période du coupon contenant la date de liquidation"}, "COUPDAYSNC": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule le nombre de jours entre la date de liquidation et la date du coupon suivant la date de liquidation"}, "COUPNCD": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Détermine la date du coupon suivant la date de liquidation"}, "COUPNUM": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule le nombre de coupons entre la date de liquidation et la date d'échéance"}, "COUPPCD": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule la date du coupon précédant la date de liquidation"}, "CUMIPMT": {"a": "(taux; npm; va; période_début; période_fin; type)", "d": "Don<PERSON> le montant cumulé des intérêts payés entre deux périodes données"}, "CUMPRINC": {"a": "(taux; npm; va; période_début; période_fin; type)", "d": "Donne le montant cumulé du principal payé entre deux périodes données"}, "DB": {"a": "(coût; valeur_rés; du<PERSON>e; période; [mois])", "d": "Renvoie l'amortissement d'un bien durant une période spécifiée en utilisant la méthode de l'amortissement dégressif à taux fixe"}, "DDB": {"a": "(co<PERSON>t; valeur_rés; du<PERSON>e; période; [facteur])", "d": "Renvoie l'amortissement d'un bien durant une période spécifiée suivant la méthode de l'amortissement dégressif à taux double ou selon un coefficient à spécifier"}, "DISC": {"a": "(liquidation; échéance; valeur_nominale; valeur_échéance; [base])", "d": "Calcule le taux d'escompte d'un titre"}, "DOLLARDE": {"a": "(prix_fraction; fraction)", "d": "Convertit la valeur des cotations boursières de la forme fractionnaire à la forme décimale"}, "DOLLARFR": {"a": "(prix_décimal; fraction)", "d": "Convertit la valeur des cotations boursières de la forme décimale à la forme fractionnaire"}, "DURATION": {"a": "(liquidation; échéance; taux; rendement; fréquence; [base])", "d": "Calcule la durée d'un titre avec des paiements d'intérêts périodiques"}, "EFFECT": {"a": "(taux_nominal; nb_périodes)", "d": "Calcule le taux effectif à partir du taux nominal et du nombre de périodes"}, "FV": {"a": "(taux; npm; vpm; [va]; [type])", "d": "Calcule la valeur future d'un investissement fondé sur des paiements réguliers et constants, et un taux d'intérêt stable."}, "FVSCHEDULE": {"a": "(va; taux)", "d": "Calcule la valeur future d'un investissement en appliquant une série de taux d'intérêt composites"}, "INTRATE": {"a": "(liquidation; échéance; investissement; valeur_échéance; [base])", "d": "Affiche le taux d'intérêt d'un titre totalement investi"}, "IPMT": {"a": "(taux; pér; npm; va; [vc]; [type])", "d": "Calcule le montant des intérêts d'un investissement pour une période donnée, fondé sur des paiements périodiques et constants, et un taux d'intérêt stable"}, "IRR": {"a": "(valeurs; [estimation])", "d": "Calcule le taux de rentabilité interne d'un investissement pour une succession de trésoreries"}, "ISPMT": {"a": "(taux; pér; npm; va)", "d": "Renvoie les intérêts payés pour une période spécifique d'une opération"}, "MDURATION": {"a": "(liquidation; échéance; taux; rendement; fréquence; [base])", "d": "Renvoie la durée de <PERSON>ley modifiée d'un titre, pour une valeur nominale considérée égale à 100 €"}, "MIRR": {"a": "(valeurs; taux_emprunt; taux_placement)", "d": "Calcule le taux de rentabilité interne pour une série de flux de trésorerie en fonction du coût de l'investissement et de l'intérêt sur le réinvestissement des liquidités"}, "NOMINAL": {"a": "(taux_effectif; nb_périodes)", "d": "Calcule le taux d'intérêt nominal à partir du taux effectif et du nombre de périodes"}, "NPER": {"a": "(taux; vpm; va; [vc]; [type])", "d": "Renvoie le nombre de paiements d'un investissement à versements réguliers et taux d'intérêt constant"}, "NPV": {"a": "(taux; valeur1; [valeur2]; ...)", "d": "Calcule la valeur actuelle nette d'un investissement s'appuyant sur un taux d'escompte et une série de débits futurs (valeurs négatives) et de crédits (valeurs positives)"}, "ODDFPRICE": {"a": "(liquidation; échéance; émission; premier_coupon; taux; rendement; valeur_échéance; fréquence; [base])", "d": "Renvoie le prix pour une valeur nominale de 100 € d'un titre dont la première période est irrégulière"}, "ODDFYIELD": {"a": "(liquidation; échéance; émission; premier_coupon; taux; valeur_nominale; valeur_échéance; fréquence; [base])", "d": "Calcule le rendement d'un titre dont la première période est irrégulière"}, "ODDLPRICE": {"a": "(liquidation; échéance; dernier_coupon; taux; rendement; valeur_échéance; fréquence; [base])", "d": "Renvoie le prix d'un titre d'une valeur nominale de 100 € dont la dernière période est irrégulière"}, "ODDLYIELD": {"a": "(liquidation; échéance; dernier_coupon; taux; valeur_nominale; valeur_échéance; fréquence; [base])", "d": "Calcule le rendement d'un titre dont la dernière période est irrégulière"}, "PDURATION": {"a": "(taux; va; vc)", "d": "Renvoie le nombre de périodes requises par un investissement pour atteindre une valeur spécifiée"}, "PMT": {"a": "(taux; npm; va; [vc]; [type])", "d": "Calcule le montant total de chaque remboursement périodique d'un investissement à remboursements et taux d'intérêt constants"}, "PPMT": {"a": "(taux; pér; npm; va; [vc]; [type])", "d": "Calcule la part de remboursement du principal d'un emprunt, fondée sur des remboursements et un taux d'intérêt constants"}, "PRICE": {"a": "(liquidation; échéance; taux; rendement; valeur_échéance; fréquence; [base])", "d": "Renvoie le prix d'un titre rapportant des intérêts périodiques, pour une valeur nominale de 100 €"}, "PRICEDISC": {"a": "(liquidation; échéance; taux; valeur_échéance; [base])", "d": "Renvoie la valeur d'encaissement d'un escompte commercial, pour une valeur nominale de 100 €"}, "PRICEMAT": {"a": "(liquidation; échéance; émission; taux; rendement; [base])", "d": "Renvoie le prix d'un titre dont la valeur nominale est 100 € et qui rapporte des intérêts à l'échéance"}, "PV": {"a": "(taux; npm; vpm; [vc]; [type])", "d": "Calcule la valeur actuelle d'un investissement : la valeur actuelle du montant total d'une série de remboursements futurs"}, "RATE": {"a": "(npm; vpm; va; [vc]; [type]; [estimation])", "d": "Calcule le taux d'intérêt par période d'un prêt ou d'un investissement. Par exemple, utilisez 1/4 de 6 % pour des paiements trimestriels à 6 % d’APR"}, "RECEIVED": {"a": "(liquidation; échéance; investissement; taux; [base])", "d": "Renvoie la valeur nominale à l'échéance d'un titre entièrement investi"}, "RRI": {"a": "(npm; va; vc)", "d": "Renvoie un taux d'intérêt équivalent pour la croissance d'un investissement"}, "SLN": {"a": "(coût; valeur_rés; durée)", "d": "Calcule l'amortissement linéaire d'un bien pour une période donnée"}, "SYD": {"a": "(coût; valeur_rés; du<PERSON>e; période)", "d": "Calcule l'amortissement d'un bien pour une période donnée sur la base de la méthode américaine Sum-of-Years Digits"}, "TBILLEQ": {"a": "(liquidation; échéance; taux_escompte)", "d": "Renvoie le taux d'escompte rationnel d'un bon du trésor"}, "TBILLPRICE": {"a": "(liquidation; échéance; taux_escompte)", "d": "Renvoie le prix d'un bon du trésor d'une valeur nominale de 100 €"}, "TBILLYIELD": {"a": "(liquidation; échéance; valeur_nominale)", "d": "Calcule le taux de rendement d'un bon du trésor"}, "VDB": {"a": "(coût; valeur_rés; du<PERSON>e; période_début; période_fin; [facteur]; [valeur_log])", "d": "Calcule l'amortissement d'un bien pour toute période que vous spécifiez, même partielle, en utilisant la méthode américaine Double-declining Balance ou toute autre méthode que vous spécifierez"}, "XIRR": {"a": "(valeurs; dates; [estimation])", "d": "Calcule le taux de rentabilité interne d'un ensemble de paiements"}, "XNPV": {"a": "(taux; valeurs; dates)", "d": "Donne la valeur actuelle nette d'un ensemble de paiements planifiés"}, "YIELD": {"a": "(liquidation; échéance; taux; valeur_nominale; valeur_rachat; fréquence; [base])", "d": "Calcule le rendement d'un titre rapportant des intérêts périodiquement"}, "YIELDDISC": {"a": "(liquidation; échéance; valeur_nominale; valeur_rachat; [base])", "d": "Calcule le taux de rendement d'un titre escompté, tel qu'un bon du trésor"}, "YIELDMAT": {"a": "(liquidation; échéance; émission; taux; valeur_nominale; [base])", "d": "Renvoie le rendement annuel d'un titre qui rapporte des intérêts à l'échéance"}, "ABS": {"a": "(nombre)", "d": "Renvoie la valeur absolue d'un nombre, un nombre sans son signe."}, "ACOS": {"a": "(nombre)", "d": "Renvoie l'arccosinus d'un nombre exprimé en radians, de 0 à pi. L'arccosinus est l'angle dont le cosinus est ce nombre"}, "ACOSH": {"a": "(nombre)", "d": "Renvoie le cosinus hyperbolique inverse d'un nombre"}, "ACOT": {"a": "(nombre)", "d": "Renvoie l'arccotangente d'un nombre, en radians, dans la plage 0 à Pi"}, "ACOTH": {"a": "(nombre)", "d": "Renvoie la cotangente hyperbolique inverse d'un nombre"}, "AGGREGATE": {"a": "(no_fonction; options; réf1; ...)", "d": "Renvoie un agrégat dans une liste ou une base de données"}, "ARABIC": {"a": "(texte)", "d": "Convertit un chiffre romain en un chiffre arabe"}, "ASC": {"a": "(texte)", "d": "En ce qui concerne les langues à jeu de caractères codés sur deux octets (DBCS, Double-byte Character Set), la fonction remplace les caractères à pleine chasse (codés sur deux octets) en caractères à demi-chasse (codés sur un octet)"}, "ASIN": {"a": "(nombre)", "d": "Renvoie l'arcsinus d'un nombre en radians, dans la plage -Pi/2 à +Pi/2"}, "ASINH": {"a": "(nombre)", "d": "Renvoie le sinus hyperbolique inverse d'un nombre"}, "ATAN": {"a": "(nombre)", "d": "Renvoie l'arctangente d'un nombre en radians, dans la plage -Pi/2 à Pi/2"}, "ATAN2": {"a": "(no_x; no_y)", "d": "Renvoie l'arctangente des coordonnées x et y, en radians entre -Pi et Pi, -Pi étant exclu"}, "ATANH": {"a": "(nombre)", "d": "Renvoie la tangente hyperbolique inverse d'un nombre"}, "BASE": {"a": "(nombre; base; [longueur_mini])", "d": "Convertit un nombre en représentation textuelle avec la base donnée"}, "CEILING": {"a": "(nombre; précision)", "d": "Arrondit un nombre au multiple le plus proche de l’argument précision en s’éloignant de zéro"}, "CEILING.MATH": {"a": "(nombre; [précision]; [mode])", "d": "Arrondit un nombre à l'entier ou au multiple le plus proche de l'argument précision en s'éloignant de zéro"}, "CEILING.PRECISE": {"a": "(x; [précision])", "d": "Arrondit le nombre à l'excès à l'entier ou au multiple significatif le plus proche."}, "COMBIN": {"a": "(nombre_éléments; nb_éléments_choisis)", "d": "Renvoie le nombre de combinaisons que l'on peut former avec un nombre donné d'éléments. Consultez l'aide pour l'équation utilisée"}, "COMBINA": {"a": "(nombre; nombre_choisi)", "d": "Renvoie le nombre de combinaisons avec répétitions que l'on peut former avec un nombre donné d'éléments"}, "COS": {"a": "(nombre)", "d": "Renvoie le cosinus d'un angle"}, "COSH": {"a": "(nombre)", "d": "Renvoie le cosinus hyperbolique d'un nombre"}, "COT": {"a": "(nombre)", "d": "Renvoie la cotangente d'un angle"}, "COTH": {"a": "(nombre)", "d": "Renvoie la cotangente hyperbolique d'un nombre"}, "CSC": {"a": "(nombre)", "d": "Renvoie la cosécante d'un angle"}, "CSCH": {"a": "(nombre)", "d": "Renvoie la cosécante hyperbolique d'un angle"}, "DECIMAL": {"a": "(nombre; base)", "d": "Convertit la représentation textuelle d'un nombre dans une base donnée en un nombre décimal"}, "DEGREES": {"a": "(angle)", "d": "Convertit des radians en degrés"}, "ECMA.CEILING": {"a": "(x; précision)", "d": "Arrondit le nombre au multiple le plus proche de l'argument de précision."}, "EVEN": {"a": "(nombre)", "d": "Arrondit un nombre au nombre entier pair le plus proche en s'éloignant de zéro"}, "EXP": {"a": "(nombre)", "d": "<PERSON><PERSON> e (2,718) élevé à la puissance spécifiée"}, "FACT": {"a": "(nombre)", "d": "Renvoie la factorielle d'un nombre, égale à 1*2*3*...*nombre"}, "FACTDOUBLE": {"a": "(nombre)", "d": "Renvoie la factorielle double d'un nombre"}, "FLOOR": {"a": "(nombre; précision)", "d": "Arrondit un nombre à l’entier ou au multiple le plus proche de l’argument précision"}, "FLOOR.PRECISE": {"a": "(x; [précision])", "d": "Arrondit le nombre par défaut à l'entier ou au multiple significatif le plus proche."}, "FLOOR.MATH": {"a": "(nombre; [précision]; [mode])", "d": "Arrondit un nombre à l'entier ou au multiple le plus proche de l'argument précision en tendant vers zéro"}, "GCD": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le plus grand dénominateur commun"}, "INT": {"a": "(nombre)", "d": "Arrondit un nombre à l'entier immédiatement inférieur"}, "ISO.CEILING": {"a": "(nombre; [précision])", "d": "Renvoie un nombre arrondi au nombre entier le plus proche ou au multiple le plus proche de l’argument précision en s’éloignant de zéro. Quel que soit son signe, ce nombre est arrondi à l’entier supérieur. <PERSON><PERSON><PERSON><PERSON>, si le nombre ou l’argument précision est égal à zéro, zéro est retourné."}, "LCM": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le plus petit dénominateur commun"}, "LN": {"a": "(nombre)", "d": "Donne le logarithme népérien d'un nombre"}, "LOG": {"a": "(nombre; [base])", "d": "Donne le logarithme d'un nombre dans la base spécifiée"}, "LOG10": {"a": "(nombre)", "d": "Calcule le logarithme en base 10 d'un nombre"}, "MDETERM": {"a": "(matrice)", "d": "Ren<PERSON>ie le déterminant d'une matrice"}, "MINVERSE": {"a": "(matrice)", "d": "Renvoie la matrice inversée de la matrice enregistrée dans un tableau"}, "MMULT": {"a": "(matrice1; matrice2)", "d": "Calcule le produit de deux matrices, sous forme d'une matrice avec le même nombre de ligne que la matrice1 et de colonnes que la matrice2"}, "MOD": {"a": "(nombre; diviseur)", "d": "Renvoie le reste d'une division"}, "MROUND": {"a": "(nombre; multiple)", "d": "Donne l'arrondi d'un nombre au multiple spécifié"}, "MULTINOMIAL": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le polynôme à plusieurs variables d'un ensemble de nombres"}, "MUNIT": {"a": "(dimension)", "d": "Renvoie la matrice d'unités pour la dimension spécifiée"}, "ODD": {"a": "(nombre)", "d": "Arrondit un nombre au nombre entier impair de valeur absolue immédiatement supérieure"}, "PI": {"a": "()", "d": "Renvoie la valeur de pi, 3,14159265358979 avec une précision de 15 chiffres"}, "POWER": {"a": "(nombre; puissance)", "d": "Renvoie la valeur du nombre élevé à une puissance"}, "PRODUCT": {"a": "(nombre1; [nombre2]; ...)", "d": "Donne le produit de la multiplication de tous les nombres donnés comme arguments"}, "QUOTIENT": {"a": "(numérateur; dénominateur)", "d": "Renvoie la partie entière du résultat d'une division"}, "RADIANS": {"a": "(angle)", "d": "Convertit des degrés en radians"}, "RAND": {"a": "()", "d": "Renvoie un nombre aléatoire de distribution normale supérieur ou égal à 0 et inférieur à 1 (différent à chaque calcul)"}, "RANDARRAY": {"a": "([lignes]; [colonnes]; [min]; [max]; [entières])", "d": "Retourne un tableau de nombres aléatoires"}, "RANDBETWEEN": {"a": "(min; max)", "d": "Renvoie un nombre aléatoire entre les nombres que vous spécifiez"}, "ROMAN": {"a": "(nombre; [type])", "d": "Convertit un chiffre arabe en chiffre romain sous forme de texte"}, "ROUND": {"a": "(nombre; no_chiffres)", "d": "Arrondit un nombre au nombre de chiffres indiqué"}, "ROUNDDOWN": {"a": "(nombre; no_chiffres)", "d": "Arrondit un nombre en tendant vers zéro"}, "ROUNDUP": {"a": "(nombre; no_chiffres)", "d": "Arrondit un nombre en s'éloignant de zéro"}, "SEC": {"a": "(nombre)", "d": "Renvoie la sécante d'un angle"}, "SECH": {"a": "(nombre)", "d": "Renvoie la sécante hyperbolique d'un angle"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "Renvoie la somme d'une série géométrique s'appuyant sur la formule"}, "SIGN": {"a": "(nombre)", "d": "Don<PERSON> le signe d'un nombre\\x00a0: 1 si le nombre est zéro, ou -1 si le nombre est négatif"}, "SIN": {"a": "(nombre)", "d": "Renvoie le sinus d'un nombre"}, "SINH": {"a": "(nombre)", "d": "Renvoie le sinus hyperbolique d'un nombre"}, "SQRT": {"a": "(nombre)", "d": "Donne la racine carrée d'un nombre"}, "SQRTPI": {"a": "(nombre)", "d": "<PERSON><PERSON> la racine carrée du produit (nombre * pi)"}, "SUBTOTAL": {"a": "(no_fonction; réf1; ...)", "d": "Renvoie un sous-total dans une liste ou une base de données"}, "SUM": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la somme des nombres dans une plage de cellules"}, "SUMIF": {"a": "(plage; critère; [somme_plage])", "d": "Additionne des cellules spécifiées selon un certain critère"}, "SUMIFS": {"a": "(plage_somme; plage_critères; critères; ...)", "d": "Additionne les cellules indiquées par un ensemble de conditions ou de critères donné"}, "SUMPRODUCT": {"a": "(matrice1; [matrice2]; [matrice3]; ...)", "d": "Donne la somme des produits des plages ou matrices correspondantes"}, "SUMSQ": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la somme des carrés des arguments. Les arguments peuvent être des nombres, des matrices, des noms ou des références à des cellules qui contiennent des nombres"}, "SUMX2MY2": {"a": "(matrice_x; matrice_y)", "d": "Calcule la différence entre les carrés des nombres correspondants dans deux plages ou matrices, puis renvoie la somme des différences. Consultez l'aide sur l'équation utilisée"}, "SUMX2PY2": {"a": "(matrice_x; matrice_y)", "d": "Calcule la somme des carrés des nombres correspondants dans deux plages ou matrices, puis renvoie le total de l'addition des sommes. Consultez l'aide sur l'équation utilisée"}, "SUMXMY2": {"a": "(matrice_x; matrice_y)", "d": "Renvoie la somme des carrés des différences entre les valeurs correspondantes de deux matrices. Consultez l'aide sur l'équation utilisée"}, "TAN": {"a": "(nombre)", "d": "Renvoie la tangente d'un nombre"}, "TANH": {"a": "(nombre)", "d": "Renvoie la tangente hyperbolique d'un nombre"}, "TRUNC": {"a": "(nombre; [no_chiffres])", "d": "Renvoie la partie entière d'un nombre en enlevant la partie décimale ou fractionnaire du nombre"}, "ADDRESS": {"a": "(no_lig; no_col; [no_abs]; [a1]; [feuille_texte])", "d": "Crée une référence de cellule sous forme de texte, en fonction des numéros de lignes et colonnes spécifiées"}, "CHOOSE": {"a": "(no_index; valeur1; [valeur2]; ...)", "d": "Choisit une valeur ou une action à réaliser dans une liste de valeurs, en fonction d'un numéro d'index"}, "COLUMN": {"a": "([réf<PERSON><PERSON><PERSON>])", "d": "Renvoie le numéro de colonne d'une référence"}, "COLUMNS": {"a": "(tableau)", "d": "Renvoie le nombre de colonnes d'une matrice ou d'une référence"}, "FORMULATEXT": {"a": "(référence)", "d": "Renvoie une formule en tant que chaîne"}, "HLOOKUP": {"a": "(valeur_cherchée; tableau; no_index_lig; [valeur_proche])", "d": "Cherche une valeur dans la première ligne d'une matrice de valeurs ou d'un tableau et renvoie la valeur de la même colonne à partir d'une ligne spécifiée"}, "HYPERLINK": {"a": "(emplacement_lien; [nom_convivial])", "d": "Crée un raccourci pour ouvrir un document enregistré sur votre disque dur, un serveur de réseau, ou sur Internet"}, "INDEX": {"a": "(matrice; no_lig; [no_col]!réf; no_lig; [no_col]; [no_zone])", "d": "Renvoie une valeur ou la référence de la cellule à l'intersection d'une ligne et d'une colonne particulières, dans une plage données"}, "INDIRECT": {"a": "(réf_texte; [a1])", "d": "Donne la référence spécifiée par une chaîne de caractères"}, "LOOKUP": {"a": "(valeur_cherchée; vecteur_recherche; [vecteur_résultat]!valeur_cherchée; matrice)", "d": "Renvoie une valeur soit à partir d'une plage d'une ligne ou d'une colonne, soit à partir d'une matrice. Fournie pour la compatibilité ascendante"}, "MATCH": {"a": "(valeur_cherchée; tableau_recherche; [type])", "d": "Renvoie la position relative d'un élément dans une matrice qui correspond à une valeur spécifique dans un ordre spécifique"}, "OFFSET": {"a": "(réf; lignes; colonnes; [hauteur]; [largeur])", "d": "Donne une référence à une plage dont le nombre de colonnes et de lignes est spécifié dans une cellule ou une plage de cellules"}, "ROW": {"a": "([réf<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> le numéro de ligne d'une référence"}, "ROWS": {"a": "(tableau)", "d": "Renvoie le nombre de lignes d'une référence ou d'une matrice."}, "TRANSPOSE": {"a": "(tableau)", "d": "Change une plage de cellules verticale en plage horizontale, et vice-versa"}, "UNIQUE": {"a": "(matrice; [by_col]; [exactly_once])", "d": " Renvoie les valeurs uniques d’une plage ou d’une matrice. "}, "VLOOKUP": {"a": "(valeur_cherchée; table_matrice; no_index_col; [valeur_proche])", "d": "Cherche une valeur dans la première colonne à gauche d'un tableau, puis renvoie une valeur dans la même ligne à partir d'une colonne spécifiée. Pa<PERSON> <PERSON><PERSON><PERSON><PERSON>, le tableau doit être trié par ordre croissant"}, "XLOOKUP": {"a": "(valeur_cherchée; tableau_recherche; tableau_renvoyé; [si_non_trouvé]; [mode_correspondance]; [mode_recherche])", "d": "Recherche une correspondance dans une plage ou un tableau et renvoie l’élément correspondant dans un deuxième tableau ou plage. <PERSON><PERSON> d<PERSON><PERSON><PERSON>, une correspondance exacte est utilisée"}, "CELL": {"a": "(info_type; [reference])", "d": "Renvoie des informations sur la mise en forme, l’emplacement ou le contenu d’une cellule"}, "ERROR.TYPE": {"a": "(valeur)", "d": "Renvoie un numéro qui correspond à une valeur d'erreur."}, "ISBLANK": {"a": "(valeur)", "d": "Contrôle si une référence renvoie à une cellule vide et renvoie VRAI ou FAUX"}, "ISERR": {"a": "(valeur)", "d": "Vérifie si l’argument valeur fait référence à une erreur autre que #N/A (valeur non disponible), et renvoie VRAI ou FAUX selon le résultat"}, "ISERROR": {"a": "(valeur)", "d": "Vérifie si l’argument valeur fait référence à une erreur, et renvoie VRAI ou FAUX selon le résultat"}, "ISEVEN": {"a": "(nombre)", "d": "Renvoie VRAI si le nombre est pair"}, "ISFORMULA": {"a": "(référence)", "d": "Vérifie si une référence renvoie à une cellule contenant une formule, et renvoie TRUE ou FALSE"}, "ISLOGICAL": {"a": "(valeur)", "d": "Renvoie VRAI si l'argument valeur fait référence à une valeur logique, que ce soit VRAI ou FAUX"}, "ISNA": {"a": "(valeur)", "d": "Renvoie VRAI si l'argument valeur fait référence à la valeur d'erreur #N/A (valeur non disponible)"}, "ISNONTEXT": {"a": "(valeur)", "d": "Renvoie VRAI si l'argument valeur fait référence à autre chose que du texte (les cellules vides ne sont pas du texte)"}, "ISNUMBER": {"a": "(valeur)", "d": "Contrôle si la valeur est un nombre et renvoie VRAI ou FAUX"}, "ISODD": {"a": "(nombre)", "d": "Renvoie VRAI si le nombre est impair"}, "ISREF": {"a": "(valeur)", "d": "Renvoie VRAI ou FAUX si l'argument valeur est une référence"}, "ISTEXT": {"a": "(valeur)", "d": "Contrôle si une valeur fait référence à du texte et renvoie VRAI ou FAUX "}, "N": {"a": "(valeur)", "d": "Renvoie une valeur convertie en nombre. Les nombres sont convertis en nombres, les dates en numéros de série, les VRAI en 1, et tout le reste en 0 (zéro)"}, "NA": {"a": "()", "d": "Renvoie la valeur d'erreur #N/A (valeur non disponible)"}, "SHEET": {"a": "([valeur])", "d": "Renvoie le numéro de la feuille référencée"}, "SHEETS": {"a": "([réf<PERSON><PERSON><PERSON>])", "d": "Renvoie le nombre de feuilles dans une référence"}, "TYPE": {"a": "(valeur)", "d": "Renvoie un nombre indiquant le type de données d’une valeur : nombre = 1 ; texte = 2 ; valeur logique = 4 ; valeur d’erreur = 16 ; tableau = 64 ; donn<PERSON> composites = 128"}, "AND": {"a": "(valeur_logique1; [valeur_logique2]; ...)", "d": "Vérifie si tous les arguments sont VRAI et renvoie VRAI si tous les arguments sont VRAI"}, "FALSE": {"a": "()", "d": "Renvoie la valeur logique FAUX"}, "IF": {"a": "(test_logique; [valeur_si_vrai]; [valeur_si_faux])", "d": "Vérifie si la condition est respectée et renvoie une valeur si le résultat d'une condition que vous avez spécifiée est VRAI, et une autre valeur si le résultat est FAUX"}, "IFS": {"a": "(test_logique; valeur_si_vrai; ...)", "d": "Vérifie si une ou plusieurs conditions sont remplies et renvoie une valeur correspondant à la première condition VRAI"}, "IFERROR": {"a": "(valeur; valeur_si_erreur)", "d": "Renvoie « valeur_si_erreur » si l'expression est une erreur et la valeur de l'expression dans le cas contaire"}, "IFNA": {"a": "(valeur; valeur_si_na)", "d": "Renvoie la valeur que vous avez spécifiée si le résultat de l'expression est #N/A, sinon renvoie le résultat de l'expression"}, "NOT": {"a": "(valeur_logique)", "d": "Inverse la valeur logique de l'argument: renvoie FAUX pour un argument VRAI et VRAI pour un argument FAUX"}, "OR": {"a": "(valeur_logique1; [valeur_logique2]; ...)", "d": "Vérifie si un argument est VRAI et renvoie VRAI ou FAUX. Renvoie FAUX uniquement si tous les arguments sont FAUX"}, "SWITCH": {"a": "(expression; valeur1; résultat1; [défaut_ou_valeur2]; [résultat2]; ...)", "d": "Compare une expression avec les valeurs d’une liste et renvoie le résultat égal à la première valeur correspondante. Si aucune correspondance n’est trouvée, une valeur facultative par défaut est renvoyée"}, "TRUE": {"a": "()", "d": "Renvoie la valeur logique VRAI"}, "XOR": {"a": "(valeur_logique1; [valeur_logique2]; ...)", "d": "Renvoie une valeur logique « Ou exclusif » de tous les arguments"}, "TEXTBEFORE": {"a": "(texte, d<PERSON><PERSON><PERSON>ur, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON> le texte qui précède la délimitation des caractères."}, "TEXTAFTER": {"a": "(texte, d<PERSON><PERSON><PERSON>ur, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retourne du texte qui succède à la délimitation des caractères."}, "TEXTSPLIT": {"a": "(texte, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Fractionne le texte en lignes ou colonnes à l’aide de délimiteurs."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Enveloppe un vecteur de ligne ou de colonne après un nombre spécifié de valeurs."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Empile verticalement les tableaux dans un tableau."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Empile horizontalement les tableaux dans un tableau."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Renvoie les lignes d’un tableau ou d’une référence."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Renvoie les colonnes d’un tableau ou d’une référence."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Renvoie le tableau sous la forme d’une colonne."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> le tableau sous la forme d’une ligne."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Enveloppe un vecteur de ligne ou de colonne après un nombre spécifié de valeurs."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Renvoie les lignes ou les colonnes du début ou de la fin du tableau."}, "DROP": {"a": "(array, rows, [columns])", "d": "Supprime les lignes ou les colonnes du début ou de la fin du tableau."}}