﻿<!DOCTYPE html>
<html>
	<head>
		<title>GAUSS-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>GAUSS-Funktion</h1>
			<p>Die Funktion <b>GAUSS</b> gehört zur Gruppe der statistischen Funktionen. Sie berechnet die Wahrscheinlichkeit, dass ein Element einer Standardgrundgesamtheit zwischen dem Mittelwert und <b><em>z</em></b> Standardabweichungen vom Mittelwert liegt.</p>
			<p>Die Formelsyntax der Funktion <b>GAUSS</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>GAUSS(z)</em></b></p> 
			<p>Dabei ist <b><em>z</em></b> ein nummerischer Wert, der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p>
            <p>Anwendung der <b>GAUSS-Funktion</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die <b>GAUSS-Funktion</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="GAUSS-Funktion" src="../images/gauss.png" /></p>
		</div>
	</body>
</html>