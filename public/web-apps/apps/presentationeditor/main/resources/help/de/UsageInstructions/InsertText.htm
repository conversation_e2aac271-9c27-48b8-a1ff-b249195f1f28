﻿<!DOCTYPE html>
<html>
	<head>
		<title>Text einfügen und formatieren</title>
		<meta charset="utf-8" />
		<meta name="description" content="Das Wichtigste über die Textbearbeitung in PowerPoint-Präsentationen: Textformatierung/Hinzufügen, Stil- und Richtungsänderung, Festlegen der Absatzparameter." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Text einfügen und formatieren</h1>
			<h2>Text in der Präsentation einfügen</h2>
			<p>Im <a target="_blank" href="https://www.onlyoffice.com/de/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Präsentationseditor</b></a> für die Eingabe von neuem Text stehen Ihnen drei Möglichkeiten zur Auswahl:</p>
			<ul>
				<li>Textabschnitt in den entsprechenden Textplatzhalter auf der Folie einfügen. Platzieren Sie dazu den Cursor im Platzhalter und geben Sie Ihren Text ein oder fügen Sie diesen mithilfe der Tastenkombination <b>STRG+V</b> ein.</li>
				<li>Textabschnitt an beliebiger Stelle auf einer Folie einfügen. Fügen Sie ein Textfeld (rechteckiger Rahmen, in den ein Text eingegeben werden kann) oder ein TextArtfeld (Textfeld mit einer vordefinierten Schriftart und Farbe, das die Anwendung von Texteffekten ermöglicht) in die Folie ein. Abhängig vom ausgewählten Textobjekt haben Sie folgende Möglichkeiten:
					<ul>
						<li>
							Um ein Textfeld hinzuzufügen, klicken Sie in der oberen Symbolleiste in den Registerkarten <b>Startseite</b> oder <b>Einfügen</b> auf das Symbol <div class="icon icon-inserttexticon"></div> <b>Textfeld</b>, wählen Sie eine der folgenden Optionen: <b>Horizontales Textfeld einfügen</b> oder <b>Vertikales Textfeld einfügen</b>, und dann klicken Sie auf die Stelle, an der Sie das Textfeld einfügen möchten. Halten Sie die Maustaste gedrückt, und ziehen Sie den Rahmen des Textfelds in die gewünschte Größe. Wenn Sie die Maustaste loslassen, erscheint die Einfügemarke im hinzugefügten Textfeld und Sie können Ihren Text eingeben.
							<p class="note">Alternativ können Sie ein Textfeld einfügen, indem Sie in der oberen Symbolleiste auf <span class="icon icon-insertautoshape"></span> <b>Form</b> klicken und das Symbol <span class="icon icon-text_autoshape"></span> aus der Gruppe <b>Standardformen</b> auswählen.</p>
						</li>
						<li>Um ein TextArt-Objekt einzufügen, klicken Sie auf das Symbol <div class = "icon icon-inserttextarticon"></div> <b>TextArt</b> in der Registerkarte <b>Einfügen</b> und klicken Sie dann auf die gewünschte Stilvorlage - das TextArt-Objekt wird in der Mitte der Folie eingefügt. Markieren Sie den Standardtext innerhalb des Textfelds mit der Maus und ersetzen Sie diesen durch Ihren eigenen Text.</li>
					</ul>
				</li>
				<li>Einen Textabschnitt in eine AutoForm einfügen. Wählen Sie eine Form aus und geben Sie Ihren Text ein.</li>
			</ul>
			<p>Klicken Sie in einen Bereich außerhalb des Textobjekts, um die Änderungen anzuwenden und zur Folie zurückzukehren.</p>
			<p>Der Text innerhalb des Textfelds ist Bestandteil der AutoForm (wenn Sie die AutoForm verschieben oder drehen, wird der Text mit ihr verschoben oder gedreht).</p>
			<p>Da ein eingefügtes Textobjekt von einem rechteckigen Rahmen umgeben ist (TextArt-Objekte haben standardmäßig unsichtbare Rahmen) und dieser Rahmen eine allgemeine AutoForm ist, können Sie sowohl die Form als auch die Texteigenschaften ändern.</p>
			<p>Um das hinzugefügte Textobjekt zu löschen, klicken Sie auf den Rand des Textfelds und drücken Sie die Taste <b>ENTF</b> auf der Tastatur. Dadurch wird auch der Text im Textfeld gelöscht.</p>
			<h2>Textfeld formatieren</h2>
			<p>Wählen Sie das entsprechende Textfeld durch Anklicken der Rahmenlinien aus, um die Eigenschaften zu verändern. Wenn das Textfeld markiert ist, werden alle Rahmenlinien als durchgezogene Linien (nicht gestrichelt) angezeigt.</p>
			<p><img alt="Markiertes Textfeld" src="../images/textbox_boxselected.png" /></p>
			<ul>
				<li>Sie können das Textfeld mithilfe der speziellen Ziehpunkte an den Ecken der Form <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">verschieben, drehen und dessen Größe ändern</a>.</li>
				<li>Um das Textfeld zu bearbeiten, <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">mit einer Füllung zu versehen</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">Rahmenlinien zu ändern</a>, das rechteckige Feld mit einer anderen Form zu <b>ersetzen</b> oder auf <a href="InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">Formen - erweiterte Einstellungen</a> zuzugreifen, klicken Sie in der rechten Seitenleiste auf <b>Formeinstellungen</b> <div class = "icon icon-shape_settings_icon"></div> und nutzen Sie die entsprechenden Optionen.</li>
				<li>Um ein <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">Textfeld auf einer Folie auszurichten, zu drehen oder zu spiegeln</a> oder Textfelder mit anderen Objekten zu verknüpfen, klicken Sie mit der rechten Maustaste auf den Feldrand und nutzen Sie die entsprechende Option im geöffneten Kontextmenü.</li>
				<li>Um <b>Textspalten</b> in einem Textfeld zu erzeugen, klicken Sie auf das entsprechende Symbol <div class = "icon icon-insert_columns"></div> in der Symbolleiste für Textformatierung und wählen Sie die gewünschte Option aus, oder klicken Sie mit der rechten Maustaste auf den Feldrand, klicken Sie auf die Option <b>Form - Erweiterte Einstellungen</b> und wechseln Sie im Fenster <b>Form - Erweiterte Einstellungen</b> in die Registerkarte <a href="../UsageInstructions/InsertAutoshapes.htm#columns" onclick="onhyperlinkclick(this)"><b>Spalten</b></a>.</li>
			</ul>
			<h2 id="formattext">Text im Textfeld formatieren</h2>
			<p>Markieren Sie den Text im Textfeld, um die Eigenschaften zu verändern. Wenn der Text markiert ist, werden alle Rahmenlinien als gestrichelte Linien angezeigt.</p>
			<p><img alt="Markierter Text" src="../images/textbox_textselected.png" /></p>
			<p class="note">Es ist auch möglich die Textformatierung zu ändern, wenn das Textfeld (nicht der Text selbst) ausgewählt ist. In einem solchen Fall werden alle Änderungen auf den gesamten Text im Textfeld angewandt. Einige Schriftformatierungsoptionen (Schriftart, -größe, -farbe und -stile) können separat auf einen zuvor ausgewählten Teil des Textes angewendet werden.</p>
			<p><b>Text im Textfeld ausrichten</b></p>
			<p>Der Text kann auf vier Arten horizontal ausgerichtet werden: linksbündig, rechtsbündig, zentriert oder im Blocksatz. Textobjekt einfügen:</p>
			<ol>
				<li>Bewegen Sie den Cursor an die Stelle, an der Sie den Text ausrichten möchten (dabei kann es sich um eine neue Zeile oder um bereits eingegebenen Text handeln).</li>
				<li>Wechseln Sie in der oberen Symbolleiste auf die Registerkarte <b>Startseite</b> und klicken Sie auf <b>Horizontale Ausrichtung</b> <div class = "icon icon-horizontalalign"></div>, um die Auswahlliste zu öffnen.</li>
				<li>Wählen Sie den gewünschten Ausrichtungstyp:
					<ul>
						<li>Die Option <b>Text linksbündig ausrichten</b> <div class = "icon icon-alignleft"></div> lässt den linken Textrand parallel zum linken Rand des Textbereichs verlaufen (der rechte Textrand bleibt unausgerichtet).</li>
						<li>Durch die Option <b>Text zentrieren</b> <div class = "icon icon-aligncenter"></div> wird der Text im Textbereich zentriert ausgerichtet (die rechte und linke Seite bleiben unausgerichtet).</li>
						<li>Die Option <b>Text rechtsbündig ausrichten</b> <div class = "icon icon-alignright"></div> lässt den rechten Textrand parallel zum rechten Rand des Textbereichs verlaufen (der linke Textrand bleibt unausgerichtet).</li>
						<li>Die Option <b>Im Blocksatz ausrichten</b> <div class = "icon icon-justify"></div> lässt den Text parallel zum linken und rechten Rand des Textbereichs verlaufen (zusätzlicher Abstand wird hinzugefügt, wo es notwendig ist, um die Ausrichtung beizubehalten).</li>
					</ul>
				</li>
			</ol>
			<p class="note">Diese Parameter finden Sie auch im Fenster <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Absatz - Erweiterte Einstellungen</b></a>.</p>
			<p>Der Text kann vertikal auf drei Arten ausgerichtet werden: oben, mittig oder unten. Textobjekt einfügen:</p>
			<ol>
				<li>Bewegen Sie den Cursor an die Stelle, an der Sie den Text ausrichten möchten (dabei kann es sich um eine neue Zeile oder um bereits eingegebenen Text handeln).</li>
				<li>Wechseln Sie in der oberen Symbolleiste auf die Registerkarte <b>Startseite</b> und klicken Sie auf <b>Vertikale Ausrichtung</b> <div class = "icon icon-verticalalign"></div>, um die Auswahlliste zu öffnen.</li>
				<li>Wählen Sie den gewünschten Ausrichtungstyp:
					<ul>
						<li>Über die Option <b>Text am oberen Rand ausrichten</b> <div class = "icon icon-aligntop"></div> richten Sie den Text am oberen Rand des Textfelds aus.</li>
						<li>Über die Option <b>Text mittig ausrichten</b> <div class = "icon icon-alignmiddle"></div> richten Sie den Text im Textfeld mittig aus.</li>
						<li>Über die Option <b>Text am unteren Rand ausrichten</b> <div class = "icon icon-alignbottom"></div> richten Sie den Text am unteren Rand des Textfelds aus.</li>
					</ul>
				</li>
			</ol>
			<hr />
			<p><b>Textrichtung ändern</b></p>
			<p>Um den Text innerhalb des Textfeldes zu <b>drehen</b>, klicken Sie mit der rechten Maustaste auf den Text, klicken Sie auf <b>Textausrichtung</b> und wählen Sie eine der verfügbaren Optionen: <b>Horizontal</b> (Standardeinstellung), <b>Text um 180° drehen</b> (vertikale Ausrichtung von oben nach unten) oder <b>Text um 270° drehen</b> (vertikale Ausrichtung von unten nach oben).</p>
			<hr />
			<p id="formatfont"><b>Schriftart, -größe und -farbe anpassen und DekoSchriften anwenden</b></p>
			<p>In der Registerkarte <b>Start</b> können Sie über die Symbole in der oberen Symbolleiste Schriftart, -größe und -Farbe festelgen und verschiedene DekoSchriften anwenden.</p>
			<p class="note">Wenn Sie die Formatierung auf Text anwenden wollen, der bereits in der Präsentation vorhanden ist, wählen Sie diesen mit der Maus oder <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">mithilfe der Tastatur</a> aus und legen Sie die gewünschte Formatierung fest. Sie können den Mauszeiger auch innerhalb des erforderlichen Wortes platzieren, um die Formatierung nur auf dieses Wort anzuwenden.</p>
			<table>
				<tr>
					<td width="10%">Schriftart</td>
					<td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Wird verwendet, um eine Schriftart aus der Liste mit den verfügbaren Schriftarten zu wählen. <span class="desktopDocumentFeatures">Wenn eine gewünschte Schriftart nicht in der Liste verfügbar ist, können Sie diese runterladen und in Ihrem Betriebssystem speichern. Anschließend steht Ihnen diese Schrift in der <em>Desktop-Version</em> zur Nutzung zur Verfügung.</span></td>
				</tr>
				<tr>
					<td>Schriftgröße</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Über diese Option kann der gewünschte Wert für die Schriftgröße aus der Dropdown-List ausgewählt werden (die Standardwerte sind: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 und 96). Sie können auch manuell einen Wert in das Feld für die Schriftgröße eingeben und anschließend die <em>Eingabetaste</em> drücken.</td>
				</tr>
				<tr>
					<td>Schriftgrad vergrößern</td>
					<td><div class = "icon icon-larger"></div></td>
					<td>Wird verwendet, um die Schriftgröße zu ändern, sodass sie bei jedem Drücken der Taste um einen Punkt größer wird.</td>
				</tr>
				<tr>
					<td>Schriftgrad verkleinern</td>
					<td><div class = "icon icon-smaller"></div></td>
					<td>Wird verwendet, um die Schriftgröße zu ändern, sodass sie bei jedem Drücken der Taste um einen Punkt kleiner wird.</td>
				</tr>
				<tr>
					<td>Groß-/Kleinschreibung</td>
					<td><div class = "icon icon-change_case"></div></td>
					<td>Wird verwendet, um die Groß- und Kleinschreibung zu ändern. <em>Ersten Buchstaben im Satz großschreiben</em> - die Buchstaben werden wie in einem Satz geschrieben. <em>Kleinbuchstaben</em> - alle Buchstaben sind klein. <em>GROSSBUCHSTABEN</em> - alle Buchstaben sind groß. <em>Ersten Buchstaben im Wort großschreiben</em> - jedes Wort beginnt mit einem Großbuchstaben. <em>gROSS-/kLEINSCHREIBUNG</em> - kehrt die Groß- und Kleinschreibung des ausgewählten Textes oder des Wortes um, an dem sich der Mauszeiger befindet.</td>
				</tr>
				<tr>
					<td>Texthervorhebungsfarbe</td>
					<td><div class = "icon icon-highlightcolor"></div></td>
					<td>Wird verwendet, um den Hintergrund für einzelne Sätze, Phrasen, Wörter oder sogar Zeichen durch Hinzufügen eines Farbbandes zu markieren, das den Effekt eines Textmarkers um den Text herum imitiert. Wählen Sie dazu den gewünschten Text aus und klicken Sie anschließend auf den Abwärtspfeil neben dem Symbol, um eine Farbe auf der Palette auszuwählen (diese Farbeinstellung ist unabhängig vom ausgewählten <b>Farbschema</b> und enthält 16 Farben) - die Farbe wird auf den ausgewählten Text angewendet. Alternativ können Sie zuerst eine Hervorhebungsfarbe auswählen und dann den Text mit der Maus auswählen - der Mauszeiger sieht in diesem Fall so aus <div class="icon icon-highlight_color_mouse_pointer"></div> und Sie können mehrere verschiedene Teile Ihres Textes nacheinander markieren. Um die Hervorhebung zu beenden, klicken Sie einfach erneut auf das Symbol. Um die Markierungsfarbe zu löschen, klicken Sie auf die Option <b>Keine Füllung</b>.</td>
				</tr>
				<tr>
					<td>Schriftfarbe</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Wird verwendet, um die Farbe der Buchstaben/Zeichen im Text zu ändern. Klicken Sie auf den Abwärtspfeil neben dem Symbol, um eine <a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">Farbe auszuwählen</a>.</td>
				</tr>
				<tr>
					<td>Fett</td>
					<td><div class = "icon icon-bold"></td>
					<td>Der gewählte Textabschnitt wird durch fette Schrift hervorgehoben.</td>
				</tr>
				<tr>
					<td>Kursiv</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Der gewählte Textabschnitt wird durch die Schrägstellung der Zeichen hervorgehoben.</td>
				</tr>
				<tr>
					<td>Unterstrichen</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Der gewählten Textabschnitt wird mit einer Linie unterstrichen.</td>
				</tr>
				<tr>
					<td>Durchgestrichen</td>
					<td><div class = "icon icon-strike"></div></td>
					<td>Der gewählten Textabschnitt wird mit einer Linie durchgestrichen.</td>
				</tr>
				<tr>
					<td>Hochgestellt</td>
					<td><div class = "icon icon-sup"></div></td>
					<td>Der gewählte Textabschnitt wird verkleinert und im oberen Teil der Textzeile platziert z.B. in Bruchzahlen.</td>
				</tr>
				<tr>
					<td>Tiefgestellt</td>
					<td><div class = "icon icon-sub"></div></td>
					<td>Der gewählte Textabschnitt wird verkleinert und im unteren Teil der Textzeile platziert z.B. in chemischen Formeln.</td>
				</tr>
			</table>
			<p><b>Bestimmen Sie den Zeilenabstand und ändern Sie die Absatzeinzüge</b></p>
			<p>Im Präsentationseditor können Sie die Zeilenhöhe für den Text innerhalb der Absätze sowie den Abstand zwischen dem aktuellen Absatz und dem vorherigen oder nächsten Absatz festlegen.</p>
			<p><img class="floatleft" alt="Registerkarte Texteinstellungen" src="../images/textsettingstab.png" /></p>
			<p>Gehen Sie dazu vor wie folgt:</p>
			<ol style="margin-left: 280px;">
				<li>Positionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus.</li>
				<li>Nutzen Sie die entsprechenden Felder der Registerkarte <div class = "icon icon-text_settings_icon"></div> <b>Texteinstellungen</b> in der rechten Seitenleiste, um die gewünschten Ergebnisse zu erzielen:
					<ul>
						<li><b>Zeilenabstand</b> - Zeilenhöhe für die Textzeilen im Absatz festlegen. Sie können unter zwei Optionen wählen: <b>mehrfach</b> (mithilfe dieser Option wird ein Zeilenabstand festgelegt, der ausgehend vom einfachen Zeilenabstand vergrößert wird (Größer als 1)), <b>genau</b> (mithilfe dieser Option wird ein fester Zeilenabstand festgelegt). Sie können den gewünschten Wert im Feld rechts angeben.</li>
						<li><b>Absatzabstand</b> - Auswählen wie groß die Absätze sind, die zwischen Textzeilen und Abständen angezeigt werden.
							<ul>
								<li><b>Vor</b> - Abstand vor dem Absatz festlegen.</li>
								<li><b>Nach</b> - Abstand nach dem Absatz festlegen.</li>
							</ul>
						</li>
					</ul>
				</li>
			</ol>
			<p class="note">Diese Parameter finden Sie auch im Fenster <a href="../UsageInstructions/InsertText.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Absatz - Erweiterte Einstellungen</b></a>.</p>
			<p>Um den aktuellen Zeilenabstand zu ändern, können Sie auch auf der Registerkarte <b>Startseite</b> das Symbol <b>Zeilenabstand</b> <span class="icon icon-linespacing"></span> anklicken und den gewünschten Wert aus der Liste auswählen: 1,0; 1,15; 1,5; 2,0; 2,5; oder 3,0 Zeilen.</p>
			<p>Um den Absatzversatz von der linken Seite des Textfelds zu ändern, positionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus und klicken Sie auf das entsprechende Symbole auf der Registerkarte <b>Startseite</b> in der oberen Symbolleiste: <b>Einzug verkleinern</b> <span class="icon icon-decreaseindent"></span> und <b>Einzug vergrößern</b> <span class="icon icon-increaseindent"></span>.</p>
			<h2 id="textadvancedsettings">Die erweiterten Absatzeinstellungen anpassen</h2>
			<p>Um das Fenster <b>Absatz – Erweiterte Einstellungen</b> zu öffnen, klicken Sie mit der rechten Maustaste auf den Text und wählen Sie die Option <b>Erweiterte Absatzeinstellungen</b> aus dem Menü. Es ist auch möglich, den Cursor innerhalb des gewünschten Absatzes zu platzieren - die Registerkarte <span class="icon icon-text_settings_icon"></span> <b>Absatzeinstellungen</b> wird in der rechten Seitenleiste aktiviert. Klicken Sie auf den Link <b>Erweiterte Einstellungen anzeigen</b>. Das Absatzeinstellungen-Fenster wird geöffnet:</p>
			<img alt="Absatzeigenschaften - Registerkarte Einzüge &amp; Position" src="../images/textadvancedsettings1.png" />
			<p>In der Registerkarte <b>Einzüge &amp; Position</b> können Sie:
			<ul>
				<li>die <b>Ausrichtung</b> für den Absatztext ändern,</li>
				<li>die Absatz-<b>Einzüge</b> in Bezug auf <a href="../UsageInstructions/InsertAutoshapes.htm#internalmargins" onclick="onhyperlinkclick(this)">innere Ränder</a> des Textfelds ändern,
					<ul>
						<li><b>Links</b> - stellen Sie den Abstand des Absatzes vom <b>linken</b> inneren Rand des Textfelds ein, indem Sie den erforderlichen numerischen Wert angeben,</li>
						<li><b>Rechts</b> - setzen Sie den Abstand des Absatzes vom <b>rechten</b> inneren Rand des Textfelds, indem Sie den erforderlichen numerischen Wert angeben,</li>
						<li><b>Speziell</b> - setzen Sie den Einzug für die <b>erste Zeile</b> des Absatzes: Wählen Sie den entsprechenden Menüpunkt (<b>(Kein)</b>, <b>Erste Zeile</b>, <b>Hängend</b>) und ändern Sie den standardmäßigen numerischen Wert, der für <b>Erste Zeile</b> oder <b>Hängend</b> angegeben ist,</li>
					</ul>
				</li>
				<li>den <b>Zeilenabstand</b> des Absatzes ändern.</li>
            </ul>
			<p>Sie können auch das horizontale <b>Lineal</b> verwenden, um Einzüge festzulegen.</p>
            <div class = "big big-indents_ruler"></div>
			<p>Wählen Sie die erforderlichen Absätze aus und ziehen Sie die Einzugsmarkierungen entlang des Lineals.</p>
            <ul>
				<li><b>Markierung für den Einzug der ersten Zeile</b> <div class = "icon icon-firstline_indent"></div> wird verwendet, um den Abstand vom linken inneren Rand des Textfelds für die erste Zeile des Absatzes festzulegen.</li>
				<li><b>Markierung für hängenden Einzug</b> <div class = "icon icon-hanging"></div> wird verwendet, um den Abstand vom linken inneren Rand des Textfelds für die zweite und alle folgenden Zeilen des Absatzes festzulegen.</li>
				<li><b>Markierung für linken Einzug</b> <div class = "icon icon-leftindent"></div> wird verwendet, um den gesamten Absatzversatz vom linken inneren Rand des Textfelds festzulegen.</li>
				<li><b>Markierung für rechten Einzug</b> <div class = "icon icon-right_indent"></div> wird verwendet, um den Abstand des Absatzes vom rechten inneren Rand des Textfelds festzulegen.</li>
            </ul>
			<p class="note">Wenn Sie die Lineale nicht sehen, wechseln Sie zur Registerkarte <b>Startseite</b> in der oberen Symbolleiste und klicken Sie auf <b>Ansichts-Einstellungen</b> <span class="icon icon-viewsettingsicon"></span> in der oberen rechten Ecke und deaktivieren Sie die Option <b>Lineale verbergen</b>, um sie anzuzeigen.</p>
            <img alt="Paragraph Properties - Font tab" src="../images/textadvancedsettings2.png" />
			<p>Die Registerkarte <b>Schriftart</b> enthält die folgenden Parameter:</p>
			<ul>
				<li><b>Durchgestrichen</b> - durchstreichen einer Textstelle mithilfe einer Linie.</li>
				<li><b>Doppelt durchgestrichen</b> - durchstreichen einer Textstelle mithilfe einer doppelten Linie.</li>
				<li><b>Hochgestellt</b> - Textstellen verkleinern und hochstellen, wie beispielsweise in Brüchen.</li>
				<li><b>Tiefgestellt</b> - Textstellen verkleinern und tiefstellen, wie beispielsweise in chemischen Formeln.</li>
				<li><b>Kapitälchen</b> - erzeugt Großbuchstaben in Höhe von Kleinbuchstaben.</li>
				<li><b>Großbuchstaben</b> - alle Buchstaben als Großbuchstaben schreiben.</li>
				<li><b>Zeichenabstand</b> - Abstand zwischen den einzelnen Zeichen festlegen. Erhöhen Sie den Standardwert für den Abstand <b>Erweitert</b> oder verringern Sie den Standardwert für den Abstand <b>Verkürzt</b>. Nutzen Sie die Pfeiltasten oder geben Sie den erforderlichen Wert in das dafür vorgesehene Feld ein.
				<p>Alle Änderungen werden im Feld Vorschau unten angezeigt.</p>
				</li>
			</ul>
			<img alt="Absatzeigenschaften - Registerkarte Tabulator" src="../images/textadvancedsettings3.png" />
			<p>Die Registerkarte <b>Tabulator</b> ermöglicht die Änderung der Tabstopps zu ändern, d.h. die Position des Mauszeigers rückt vor, wenn Sie die <b>Tabulatortaste</b> auf der Tastatur drücken.</p>
			<ul>
				<li><b>Tabulatorposition</b> - Festlegen von benutzerdefinierten Tabstopps. Geben Sie den gewünschten Wert in das angezeigte Feld ein, über die Pfeiltasten können Sie den Wert präzise anpassen, klicken Sie anschließend auf <b>Festlegen</b>. Ihre benutzerdefinierte Tabulatorposition wird der Liste im unteren Feld hinzugefügt.</li>
				<li>Die Standardeinstellung für <b>Tabulatoren</b> ist auf 1,25 cm festgelegt. Sie können den Wert verkleinern oder vergrößern, nutzen Sie dafür die Pfeiltasten oder geben Sie den gewünschten Wert in das dafür vorgesehene Feld ein.</li>
				<li><b>Ausrichtung</b> - legt den gewünschten Ausrichtungstyp für jede der Tabulatorpositionen in der obigen Liste fest. Wählen Sie die gewünschte Tabulatorposition in der Liste aus, Ihnen stehen die Optionen <b>Linksbündig</b>, <b>Zentriert</b> oder <b>Rechtsbündig</b> zur Verfügung, klicken sie anschließend auf <b>Festlegen</b>.
					<ul>
						<li><b>Linksbündig</b> - der Text wird ab der Position des Tabstopps linksbündig ausgerichtet; d.h. der Text verschiebt sich bei der Eingabe nach rechts. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung <div class = "icon icon-tabstopleft_marker"></div> angezeigt.</li>
						<li><b>Zentriert</b> - der Text wird an der Tabstoppposition zentriert. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung <div class = "icon icon-tabstopcenter_marker"></div> angezeigt.</li>
						<li><b>Rechtsbündig</b> - der Text wird ab der Position des Tabstopps rechtsbündig ausgerichtet; d.h. der Text verschiebt sich bei der Eingabe nach links. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung <div class = "icon icon-tabstopright_marker"></div> angezeigt.</li>
					</ul>
					<p>Um Tabstopps aus der Liste zu löschen, wählen Sie einen Tabstopp und drücken Sie <b>Entfernen</b> oder <b>Alle entfernen</b>.</p>
				</li>
			</ul>
			<p>Alternativ können Sie die Tabstopps auch mithilfe des horizontalen Lineals festlegen.</p>
			<ol>
				<li>Klicken Sie zum Auswählen des gewünschten Tabstopps auf das Symbol <div class = "icon icon-tabstopleft"></div> in der oberen linken Ecke des Arbeitsbereichs, um den gewünschten Tabstopp auszuwählen: <b>Links</b> <div class = "icon icon-tabstopleft"></div>, <b>Zentriert</b> <div class = "icon icon-tabstopcenter"></div> oder <b>Rechts</b> <div class = "icon icon-tabstopright"></div>.</li>
				<li>Klicken Sie an der unteren Kante des Lineals auf die Position, an der Sie einen Tabstopp setzen möchten. Ziehen Sie die Markierung nach links oder rechts, um die Position zu ändern. Um den hinzugefügten Tabstopp zu entfernen, ziehen Sie die Markierung aus dem Lineal.
				<p><div class = "big big-tabstops_ruler"></div></p>
				<p class="note">Wenn die Lineale nicht angezeigt werden, wechseln Sie in die Registerkarte <b>Startseite</b>, klicken Sie in der oberen rechten Ecke auf das Symbol <b>Ansichtseinstellungen</b> <span class="icon icon-viewsettingsicon"></span> und deaktivieren Sie die Option <b>Lineale verbergen</b>.</p>
				</li>
			</ol>
			<h2>TextArt-Stil bearbeiten</h2>
			<p>Wählen Sie ein Textobjekt aus und klicken Sie in der rechten Seitenleiste auf das Symbol <b>TextArt-Einstellungen</b> <span class="icon icon-textart_settings_icon"></span>.</p>
			<p><img alt="Registerkarte TextArt-Einstellungen" src="../images/right_textart.png" /></p>
			<ul>
				<li>Ändern Sie den angewandten Textstil, indem Sie eine neue <b>Vorlage</b> aus der Galerie auswählen. Sie können den Grundstil außerdem ändern, indem Sie eine andere Schriftart, -größe usw. auswählen.</li>
				<li><a href="../UsageInstructions/FillObjectsSelectColor.htm" onclick="onhyperlinkclick(this)">Füllung</a> und <a href="../UsageInstructions/InsertAutoshapes.htm#shapestroke" onclick="onhyperlinkclick(this)">Umrandung</a> der Schriftart ändern. Die verfügbaren Optionen sind die gleichen wie für AutoFormen.</li>
				<li>Wenden Sie einen Texteffekt an, indem Sie aus der <b>Galerie</b> mit den verfügbaren Vorlagen die gewünschte Formatierung auswählen. Sie können den Grad der Textverzerrung anpassen, indem Sie den rosafarbenen, rautenförmigen Ziehpunkt in die gewünschte Position ziehen.</li>
			</ul>
			<p><img alt="Texteffekte" src="../images/textart_transformation.png" /></p>
		</div>
	</body>
</html>