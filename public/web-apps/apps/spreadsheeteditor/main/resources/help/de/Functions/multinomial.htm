﻿<!DOCTYPE html>
<html>
	<head>
		<title>POLYNOMIAL-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>POLYNOMIAL-Funktion</h1>
			<p>Die Funktion <b>POLYNOMIAL</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um das Verhältnis der Fakultät von der Summe der Zahlen zum Produkt der Fakultäten zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>POLYNOMIAL</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>POLYNOMIAL(Zahl1; [Zahl2];...)</em></b></p> 
			<p>Die Liste der Argumente <b><em>Zahl1;[Zahl2];...</em></b> kann bis zu 30 nummerische Werte enthalten, die manuell eingegeben werden oder in dem Zellbereich eingeschlossen sind, auf den Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>POLYNOMIAL</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>POLYNOMIAL</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="POLYNOMIAL-Funktion" src="../images/multinomial.png" /></p>
		</div>
	</body>
</html>