﻿<!DOCTYPE html>
<html>
	<head>
		<title>ZEIT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ZEIT-Funktion</h1>
			<p>Die Funktion <b>ZEIT</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie gibt die fortlaufende Zahl einer bestimmten Uhrzeit im ausgewählten Format zurück (Die Standardeinstellung für das Format ist <em>hh:mm tt</em>).</p>
			<p>Die Formelsyntax der Funktion <b>ZEIT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ZEIT(Stunde;Minute;Sekunde)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Stunde</em></b> ist eine Zahl zwischen 0 und 23.</p> 
			<p style="text-indent: 50px;"><b><em>Minute</em></b> ist eine Zahl zwischen 0 und 59.</p> 
			<p style="text-indent: 50px;"><b><em>Sekunde</em></b> ist eine Zahl zwischen 0 und 59.</p> 
			<p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>ZEIT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ZEIT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ZEIT-Funktion" src="../images/time.png" /></p>
		</div>
	</body>
</html>