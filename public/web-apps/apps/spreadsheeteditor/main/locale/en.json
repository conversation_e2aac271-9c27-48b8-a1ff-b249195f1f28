{"cancelButtonText": "Cancel", "Common.Controllers.Chat.notcriticalErrorTitle": "Warning", "Common.Controllers.Chat.textEnterMessage": "Enter your message here", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% Stacked area", "Common.define.chartData.textBar": "Bar", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3-D Clustered column", "Common.define.chartData.textBarNormal3dPerspective": "3-D column", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Stacked column", "Common.define.chartData.textBarStackedPer": "100% Stacked column", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Stacked column", "Common.define.chartData.textCharts": "Charts", "Common.define.chartData.textColumn": "Column", "Common.define.chartData.textColumnSpark": "Column", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3-D Clustered bar", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Stacked bar", "Common.define.chartData.textHBarStackedPer": "100% Stacked bar", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Stacked bar", "Common.define.chartData.textLine": "Line", "Common.define.chartData.textLine3d": "3-D line", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineSpark": "Line", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% Stacked line", "Common.define.chartData.textLineStackedPerMarker": "100% Stacked line with markers", "Common.define.chartData.textPie": "Pie", "Common.define.chartData.textPie3d": "3-D pie", "Common.define.chartData.textPoint": "XY (<PERSON><PERSON><PERSON>)", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "Stock", "Common.define.chartData.textSurface": "Surface", "Common.define.chartData.textWinLossSpark": "Win/Loss", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "No format set", "Common.define.conditionalData.text1Above": "1 std dev above", "Common.define.conditionalData.text1Below": "1 std dev below", "Common.define.conditionalData.text2Above": "2 std dev above", "Common.define.conditionalData.text2Below": "2 std dev below", "Common.define.conditionalData.text3Above": "3 std dev above", "Common.define.conditionalData.text3Below": "3 std dev below", "Common.define.conditionalData.textAbove": "Above", "Common.define.conditionalData.textAverage": "Average", "Common.define.conditionalData.textBegins": "Begins with", "Common.define.conditionalData.textBelow": "Below", "Common.define.conditionalData.textBetween": "Between", "Common.define.conditionalData.textBlank": "Blank", "Common.define.conditionalData.textBlanks": "Contains blanks", "Common.define.conditionalData.textBottom": "Bottom", "Common.define.conditionalData.textContains": "Contains", "Common.define.conditionalData.textDataBar": "Data bar", "Common.define.conditionalData.textDate": "Date", "Common.define.conditionalData.textDuplicate": "Duplicate", "Common.define.conditionalData.textEnds": "Ends with", "Common.define.conditionalData.textEqAbove": "Equal to or above", "Common.define.conditionalData.textEqBelow": "Equal to or below", "Common.define.conditionalData.textEqual": "Equal to", "Common.define.conditionalData.textError": "Error", "Common.define.conditionalData.textErrors": "Contains errors", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "Greater than", "Common.define.conditionalData.textGreaterEq": "Greater than or equal to", "Common.define.conditionalData.textIconSets": "Icon sets", "Common.define.conditionalData.textLast7days": "In the last 7 days", "Common.define.conditionalData.textLastMonth": "Last month", "Common.define.conditionalData.textLastWeek": "Last week", "Common.define.conditionalData.textLess": "Less than", "Common.define.conditionalData.textLessEq": "Less than or equal to", "Common.define.conditionalData.textNextMonth": "Next month", "Common.define.conditionalData.textNextWeek": "Next week", "Common.define.conditionalData.textNotBetween": "Not between", "Common.define.conditionalData.textNotBlanks": "Does not contain blanks", "Common.define.conditionalData.textNotContains": "Does not contain", "Common.define.conditionalData.textNotEqual": "Not equal to", "Common.define.conditionalData.textNotErrors": "Does not contain errors", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "This month", "Common.define.conditionalData.textThisWeek": "This week", "Common.define.conditionalData.textToday": "Today", "Common.define.conditionalData.textTomorrow": "Tomorrow", "Common.define.conditionalData.textTop": "Top", "Common.define.conditionalData.textUnique": "Unique", "Common.define.conditionalData.textValue": "Value is", "Common.define.conditionalData.textYesterday": "Yesterday", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating Flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating Hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating Picture Blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating Picture Circles", "Common.define.smartArt.textArchitectureLayout": "Architecture Layout", "Common.define.smartArt.textArrowRibbon": "Arrow Ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending Picture Accent Process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic Bending Process", "Common.define.smartArt.textBasicBlockList": "Basic Block List", "Common.define.smartArt.textBasicChevronProcess": "Basic Chevron Process", "Common.define.smartArt.textBasicCycle": "Basic Cycle", "Common.define.smartArt.textBasicMatrix": "Basic Matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic Process", "Common.define.smartArt.textBasicPyramid": "Basic Pyramid", "Common.define.smartArt.textBasicRadial": "Basic Radial", "Common.define.smartArt.textBasicTarget": "Basic Target", "Common.define.smartArt.textBasicTimeline": "Basic Timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending Picture Accent List", "Common.define.smartArt.textBendingPictureBlocks": "Bending Picture Blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending Picture Caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending Picture Caption List", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending Picture Semi-Transparent Text", "Common.define.smartArt.textBlockCycle": "Block Cycle", "Common.define.smartArt.textBubblePictureList": "Bubble Picture List", "Common.define.smartArt.textCaptionedPictures": "Captioned Pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron Accent Process", "Common.define.smartArt.textChevronList": "Chevron List", "Common.define.smartArt.textCircleAccentTimeline": "Circle Accent Timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle Arrow Process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle Picture Hierarchy", "Common.define.smartArt.textCircleProcess": "Circle Process", "Common.define.smartArt.textCircleRelationship": "Circle Relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular Bending Process", "Common.define.smartArt.textCircularPictureCallout": "Circular Picture Callout", "Common.define.smartArt.textClosedChevronProcess": "Closed Chevron Process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous Arrow Process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous Block Process", "Common.define.smartArt.textContinuousCycle": "Continuous Cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous Picture List", "Common.define.smartArt.textConvergingArrows": "Converging Arrows", "Common.define.smartArt.textConvergingRadial": "Converging Radial", "Common.define.smartArt.textConvergingText": "Converging Text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance Arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle Matrix", "Common.define.smartArt.textDescendingBlockList": "Descending Block List", "Common.define.smartArt.textDescendingProcess": "Descending Process", "Common.define.smartArt.textDetailedProcess": "Detailed Process", "Common.define.smartArt.textDivergingArrows": "Diverging Arrows", "Common.define.smartArt.textDivergingRadial": "Diverging Radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed Text Picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid Matrix", "Common.define.smartArt.textGroupedList": "Grouped List", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half Circle Organization Chart", "Common.define.smartArt.textHexagonCluster": "Hexagon Cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon Radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy List", "Common.define.smartArt.textHorizontalBulletList": "Horizontal Bullet List", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal Hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal Labeled Hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal Multi-Level Hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal Organization Chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal Picture List", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing Arrow Process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing Circle Process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected Block Process", "Common.define.smartArt.textInterconnectedRings": "Interconnected Rings", "Common.define.smartArt.textInvertedPyramid": "Inverted Pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled Hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined List", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional Cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and Title Organization Chart", "Common.define.smartArt.textNestedTarget": "Nested Target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional Cycle", "Common.define.smartArt.textOpposingArrows": "Opposing Arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing Ideas", "Common.define.smartArt.textOrganizationChart": "Organization Chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased Process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture Accent Blocks", "Common.define.smartArt.textPictureAccentList": "Picture Accent List", "Common.define.smartArt.textPictureAccentProcess": "Picture Accent Process", "Common.define.smartArt.textPictureCaptionList": "Picture Caption List", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture Grid", "Common.define.smartArt.textPictureLineup": "Picture Lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture Organization Chart", "Common.define.smartArt.textPictureStrips": "Picture Strips", "Common.define.smartArt.textPieProcess": "Pie Process", "Common.define.smartArt.textPlusAndMinus": "Plus and Minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process Arrows", "Common.define.smartArt.textProcessList": "Process List", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid List", "Common.define.smartArt.textRadialCluster": "Radial Cluster", "Common.define.smartArt.textRadialCycle": "Radial Cycle", "Common.define.smartArt.textRadialList": "Radial List", "Common.define.smartArt.textRadialPictureList": "Radial Picture List", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to Result Process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating Bending Process", "Common.define.smartArt.textReverseList": "Reverse List", "Common.define.smartArt.textSegmentedCycle": "Segmented Cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented Process", "Common.define.smartArt.textSegmentedPyramid": "Segmented Pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot Picture List", "Common.define.smartArt.textSpiralPicture": "Spiral Picture", "Common.define.smartArt.textSquareAccentList": "Square Accent List", "Common.define.smartArt.textStackedList": "Stacked List", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered Process", "Common.define.smartArt.textStepDownProcess": "Step Down Process", "Common.define.smartArt.textStepUpProcess": "Step Up Process", "Common.define.smartArt.textSubStepProcess": "Sub-Step Process", "Common.define.smartArt.textTabbedArc": "Tabbed <PERSON>", "Common.define.smartArt.textTableHierarchy": "Table Hierarchy", "Common.define.smartArt.textTableList": "Table List", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target List", "Common.define.smartArt.textTextCycle": "Text Cycle", "Common.define.smartArt.textThemePictureAccent": "Theme Picture Accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme Picture Alternating Accent", "Common.define.smartArt.textThemePictureGrid": "Theme Picture Grid", "Common.define.smartArt.textTitledMatrix": "Titled Matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled Picture Accent List", "Common.define.smartArt.textTitledPictureBlocks": "Titled Picture Blocks", "Common.define.smartArt.textTitlePictureLineup": "Title Picture Lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid List", "Common.define.smartArt.textUpwardArrow": "Upward Arrow", "Common.define.smartArt.textVaryingWidthList": "Varying Width List", "Common.define.smartArt.textVerticalAccentList": "Vertical Accent List", "Common.define.smartArt.textVerticalArrowList": "Vertical Arrow List", "Common.define.smartArt.textVerticalBendingProcess": "Vertical Bending Process", "Common.define.smartArt.textVerticalBlockList": "Vertical Block List", "Common.define.smartArt.textVerticalBoxList": "Vertical Box List", "Common.define.smartArt.textVerticalBracketList": "Vertical Bracket List", "Common.define.smartArt.textVerticalBulletList": "Vertical Bullet List", "Common.define.smartArt.textVerticalChevronList": "Vertical Chevron List", "Common.define.smartArt.textVerticalCircleList": "Vertical Circle List", "Common.define.smartArt.textVerticalCurvedList": "Vertical Curved List", "Common.define.smartArt.textVerticalEquation": "Vertical Equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical Picture Accent List", "Common.define.smartArt.textVerticalPictureList": "Vertical Picture List", "Common.define.smartArt.textVerticalProcess": "Vertical Process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "The file is being edited in another app. You can continue editing and save it as a copy.", "Common.Translation.warnFileLockedBtnEdit": "Create a copy", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Automatic", "Common.UI.ButtonColored.textNewColor": "Add new custom color", "Common.UI.ComboBorderSize.txtNoBorders": "No borders", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "No borders", "Common.UI.ComboDataView.emptyComboText": "No styles", "Common.UI.ExtendedColorDialog.addButtonText": "Add", "Common.UI.ExtendedColorDialog.textCurrent": "Current", "Common.UI.ExtendedColorDialog.textHexErr": "The entered value is incorrect.<br>Please enter a value between 000000 and FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "The entered value is incorrect.<br>Please enter a numeric value between 0 and 255.", "Common.UI.HSBColorPicker.textNoColor": "No Color", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close search", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Highlight results", "Common.UI.SearchDialog.textMatchCase": "Case sensitive", "Common.UI.SearchDialog.textReplaceDef": "Enter the replacement text", "Common.UI.SearchDialog.textSearchStart": "Enter your text here", "Common.UI.SearchDialog.textTitle": "Find and replace", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "Whole words only", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "Replace all", "Common.UI.SynchronizeTip.textDontShow": "Don't show this message again", "Common.UI.SynchronizeTip.textSynchronize": "The document has been changed by another user.<br>Please click to save your changes and reload the updates.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "Standard colors", "Common.UI.ThemeColorPalette.textThemeColors": "Theme colors", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Dark", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "Cancel", "Common.UI.Window.closeButtonText": "Close", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Don't show this message again", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Warning", "Common.UI.Window.yesButtonText": "Yes", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "address: ", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Add", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Apply as you work", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat as you type", "Common.Views.AutoCorrectDialog.textBy": "By", "Common.Views.AutoCorrectDialog.textDelete": "Delete", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNewRowCol": "Include new rows and columns in table", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "Replace", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "Rest<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Any expression you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Any autocorrect you added will be removed and the changed ones will be restored to their original values. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textSend": "Send", "Common.Views.Comments.mniAuthorAsc": "Author A to Z", "Common.Views.Comments.mniAuthorDesc": "Author Z to A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "Add", "Common.Views.Comments.textAddComment": "Add Comment", "Common.Views.Comments.textAddCommentToDoc": "Add Comment to Document", "Common.Views.Comments.textAddReply": "Add Reply", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "Guest", "Common.Views.Comments.textCancel": "Cancel", "Common.Views.Comments.textClose": "Close", "Common.Views.Comments.textClosePanel": "Close comments", "Common.Views.Comments.textComments": "Comments", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Enter your comment here", "Common.Views.Comments.textHintAddComment": "Add comment", "Common.Views.Comments.textOpenAgain": "Open Again", "Common.Views.Comments.textReply": "Reply", "Common.Views.Comments.textResolve": "Resolve", "Common.Views.Comments.textResolved": "Resolved", "Common.Views.Comments.textSort": "Sort comments", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the sheet.", "Common.Views.CopyWarningDialog.textDontShow": "Don't show this message again", "Common.Views.CopyWarningDialog.textMsg": "Copy, cut and paste actions using the editor toolbar buttons and context menu actions will be performed within this editor tab only.<br><br>To copy or paste to or from applications outside the editor tab use the following keyboard combinations:", "Common.Views.CopyWarningDialog.textTitle": "Copy, Cut and Paste actions", "Common.Views.CopyWarningDialog.textToCopy": "for Co<PERSON>", "Common.Views.CopyWarningDialog.textToCut": "for Cut", "Common.Views.CopyWarningDialog.textToPaste": "for Paste", "Common.Views.DocumentAccessDialog.textLoading": "Loading...", "Common.Views.DocumentAccessDialog.textTitle": "Sharing settings", "Common.Views.EditNameDialog.textLabel": "Label:", "Common.Views.EditNameDialog.textLabelError": "Label must not be empty.", "Common.Views.Header.labelCoUsersDescr": "Users who are editing the file:", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Advanced settings", "Common.Views.Header.textBack": "Open file location", "Common.Views.Header.textCompactView": "<PERSON><PERSON>", "Common.Views.Header.textHideLines": "Hide Rulers", "Common.Views.Header.textHideStatusBar": "Combine sheet and status bars", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textSaveBegin": "Saving...", "Common.Views.Header.textSaveChanged": "Modified", "Common.Views.Header.textSaveEnd": "All changes saved", "Common.Views.Header.textSaveExpander": "All changes saved", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Manage document access rights", "Common.Views.Header.tipDownload": "Download file", "Common.Views.Header.tipGoEdit": "Edit current file", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipSave": "Save", "Common.Views.Header.tipSearch": "Search", "Common.Views.Header.tipUndo": "Undo", "Common.Views.Header.tipUndock": "Undock into separate window", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "View settings", "Common.Views.Header.tipViewUsers": "View users and manage document access rights", "Common.Views.Header.txtAccessRights": "Change access rights", "Common.Views.Header.txtRename": "<PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Close History", "Common.Views.History.textHide": "Collapse", "Common.Views.History.textHideAll": "Hide detailed changes", "Common.Views.History.textRestore": "Rest<PERSON>", "Common.Views.History.textShow": "Expand", "Common.Views.History.textShowAll": "Show detailed changes", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Paste an image URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "This field is required", "Common.Views.ImageFromUrlDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "Common.Views.ListSettingsDialog.textBulleted": "Bulleted", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Numbered", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Change bullet", "Common.Views.ListSettingsDialog.txtBullet": "Bullet", "Common.Views.ListSettingsDialog.txtColor": "Color", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "New bullet", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "None", "Common.Views.ListSettingsDialog.txtOfText": "% of text", "Common.Views.ListSettingsDialog.txtSize": "Size", "Common.Views.ListSettingsDialog.txtStart": "Start at", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "List settings", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.OpenDialog.closeButtonText": "Close file", "Common.Views.OpenDialog.textInvalidRange": "Invalid cells range", "Common.Views.OpenDialog.textSelectData": "Select data", "Common.Views.OpenDialog.txtAdvanced": "Advanced", "Common.Views.OpenDialog.txtColon": "Colon", "Common.Views.OpenDialog.txtComma": "Comma", "Common.Views.OpenDialog.txtDelimiter": "Delimiter", "Common.Views.OpenDialog.txtDestData": "Choose where to put the data", "Common.Views.OpenDialog.txtEmpty": "This field is required", "Common.Views.OpenDialog.txtEncoding": "Encoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "Password is incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Enter a password to open the file", "Common.Views.OpenDialog.txtOther": "Other", "Common.Views.OpenDialog.txtPassword": "Password", "Common.Views.OpenDialog.txtPreview": "Preview", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtSemicolon": "Semicolon", "Common.Views.OpenDialog.txtSpace": "Space", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Choose %1 options", "Common.Views.OpenDialog.txtTitleProtected": "Protected file", "Common.Views.PasswordDialog.txtDescription": "Set a password to protect this document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Confirmation password is not identical", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Repeat password", "Common.Views.PasswordDialog.txtTitle": "Set password", "Common.Views.PasswordDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "Common.Views.PluginDlg.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Close plugin", "Common.Views.Plugins.textLoading": "Loading", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Protection.hintAddPwd": "Encrypt with password", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Change or delete password", "Common.Views.Protection.hintSignature": "Add digital signature or signature line", "Common.Views.Protection.txtAddPwd": "Add password", "Common.Views.Protection.txtChangePwd": "Change password", "Common.Views.Protection.txtDeletePwd": "Delete password", "Common.Views.Protection.txtEncrypt": "Encrypt", "Common.Views.Protection.txtInvisibleSignature": "Add digital signature", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Add signature line", "Common.Views.RenameDialog.textName": "File name", "Common.Views.RenameDialog.txtInvalidName": "The file name cannot contain any of the following characters: ", "Common.Views.ReviewChanges.hintNext": "To next change", "Common.Views.ReviewChanges.hintPrev": "To previous change", "Common.Views.ReviewChanges.strFast": "Fast", "Common.Views.ReviewChanges.strFastDesc": "Real-time co-editing. All changes are saved automatically.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Use the 'Save' button to sync the changes you and others make.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.tipCoAuthMode": "Set co-editing mode", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON>mo<PERSON> comments", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Remove current comments", "Common.Views.ReviewChanges.tipCommentResolve": "Resolve comments", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.tipHistory": "Show version history", "Common.Views.ReviewChanges.tipRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.tipReview": "Track changes", "Common.Views.ReviewChanges.tipReviewView": "Select the mode you want the changes to be displayed", "Common.Views.ReviewChanges.tipSetDocLang": "Set document language", "Common.Views.ReviewChanges.tipSetSpelling": "Spell checking", "Common.Views.ReviewChanges.tipSharing": "Manage document access rights", "Common.Views.ReviewChanges.txtAccept": "Accept", "Common.Views.ReviewChanges.txtAcceptAll": "Accept All Changes", "Common.Views.ReviewChanges.txtAcceptChanges": "Accept changes", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accept Current Change", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Close", "Common.Views.ReviewChanges.txtCoAuthMode": "Co-editing Mode", "Common.Views.ReviewChanges.txtCommentRemAll": "Remove all comments", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Remove current comments", "Common.Views.ReviewChanges.txtCommentRemMy": "Remove my comments", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Remove My Current Comments", "Common.Views.ReviewChanges.txtCommentRemove": "Remove", "Common.Views.ReviewChanges.txtCommentResolve": "Resolve", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolve all comments", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolve my comments", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolve My Current Comments", "Common.Views.ReviewChanges.txtDocLang": "Language", "Common.Views.ReviewChanges.txtFinal": "All changes accepted (Preview)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Version History", "Common.Views.ReviewChanges.txtMarkup": "All changes (Editing)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Next", "Common.Views.ReviewChanges.txtOriginal": "All changes rejected (Preview)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Previous", "Common.Views.ReviewChanges.txtReject": "Reject", "Common.Views.ReviewChanges.txtRejectAll": "Reject All Changes", "Common.Views.ReviewChanges.txtRejectChanges": "Reject changes", "Common.Views.ReviewChanges.txtRejectCurrent": "Reject Current Change", "Common.Views.ReviewChanges.txtSharing": "Sharing", "Common.Views.ReviewChanges.txtSpelling": "Spell Checking", "Common.Views.ReviewChanges.txtTurnon": "Track Changes", "Common.Views.ReviewChanges.txtView": "Display Mode", "Common.Views.ReviewPopover.textAdd": "Add", "Common.Views.ReviewPopover.textAddReply": "Add Reply", "Common.Views.ReviewPopover.textCancel": "Cancel", "Common.Views.ReviewPopover.textClose": "Close", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+mention will provide access to the document and send an email", "Common.Views.ReviewPopover.textMentionNotify": "+mention will notify the user via email", "Common.Views.ReviewPopover.textOpenAgain": "Open Again", "Common.Views.ReviewPopover.textReply": "Reply", "Common.Views.ReviewPopover.textResolve": "Resolve", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Delete", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textByColumns": "By columns", "Common.Views.SearchPanel.textByRows": "By rows", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCell": "Cell", "Common.Views.SearchPanel.textCloseSearch": "Close search", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formulas", "Common.Views.SearchPanel.textItemEntireCell": "Entire cell contents", "Common.Views.SearchPanel.textLookIn": "Look in", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textName": "Name", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearch": "Search", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchOptions": "Search options", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "Select Data range", "Common.Views.SearchPanel.textSheet": "Sheet", "Common.Views.SearchPanel.textSpecificRange": "Specific range", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textValue": "Value", "Common.Views.SearchPanel.textValues": "Values", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.textWithin": "Within", "Common.Views.SearchPanel.textWorkbook": "Workbook", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "Select data source", "Common.Views.SignDialog.textBold": "Bold", "Common.Views.SignDialog.textCertificate": "Certificate", "Common.Views.SignDialog.textChange": "Change", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Italic", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Purpose for signing this document", "Common.Views.SignDialog.textSelect": "Select", "Common.Views.SignDialog.textSelectImage": "Select image", "Common.Views.SignDialog.textSignature": "Signature looks as", "Common.Views.SignDialog.textTitle": "Sign document", "Common.Views.SignDialog.textUseImage": "or click 'Select Image' to use a picture as signature", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "Font name", "Common.Views.SignDialog.tipFontSize": "Font size", "Common.Views.SignSettingsDialog.textAllowComment": "Allow signer to add comment in the signature dialog", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Suggested signer's e-mail", "Common.Views.SignSettingsDialog.textInfoName": "Suggested signer", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "Instructions for signer", "Common.Views.SignSettingsDialog.textShowDate": "Show sign date in signature line", "Common.Views.SignSettingsDialog.textTitle": "Signature setup", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Character", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "SSE.Controllers.DataTab.strSheet": "Sheet", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "Columns", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "You need to specify URL.", "SSE.Controllers.DataTab.textRows": "Rows", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Text to Columns", "SSE.Controllers.DataTab.txtDataValidation": "Data Validation", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Expand", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "The data next to the selection will not be removed. Do you want to expand the selection to include the adjacent data or continue with the currently selected cells only?", "SSE.Controllers.DataTab.txtExtendDataValidation": "The selection contains some cells without Data Validation settings.<br>Do you want to extend Data Validation to these cells?", "SSE.Controllers.DataTab.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DataTab.txtRemDuplicates": "Remove Duplicates", "SSE.Controllers.DataTab.txtRemoveDataValidation": "The selection contains more than one type of validation.<br>Erase current settings and continue?", "SSE.Controllers.DataTab.txtRemSelected": "Remove in selected", "SSE.Controllers.DataTab.txtUrlTitle": "Paste a data URL", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "Alignment", "SSE.Controllers.DocumentHolder.centerText": "Center", "SSE.Controllers.DocumentHolder.deleteColumnText": "Delete Column", "SSE.Controllers.DocumentHolder.deleteRowText": "Delete Row", "SSE.Controllers.DocumentHolder.deleteText": "Delete", "SSE.Controllers.DocumentHolder.errorInvalidLink": "The link reference does not exist. Please correct the link or delete it.", "SSE.Controllers.DocumentHolder.guestText": "Guest", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Column Left", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Column Right", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Row Above", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Row Below", "SSE.Controllers.DocumentHolder.insertText": "Insert", "SSE.Controllers.DocumentHolder.leftText": "Left", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Warning", "SSE.Controllers.DocumentHolder.rightText": "Right", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "AutoCorrect options", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Column Width {0} symbols ({1} pixels)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Row Height {0} points ({1} pixels)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Click the link to open it or click and hold the mouse button to select the cell.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Insert column to the left", "SSE.Controllers.DocumentHolder.textInsertTop": "Insert row above", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Paste special", "SSE.Controllers.DocumentHolder.textStopExpand": "Stop automatically expanding tables", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "This element is being edited by another user.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Above average", "SSE.Controllers.DocumentHolder.txtAddBottom": "Add bottom border", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Add fraction bar", "SSE.Controllers.DocumentHolder.txtAddHor": "Add horizontal line", "SSE.Controllers.DocumentHolder.txtAddLB": "Add left bottom line", "SSE.Controllers.DocumentHolder.txtAddLeft": "Add left border", "SSE.Controllers.DocumentHolder.txtAddLT": "Add left top line", "SSE.Controllers.DocumentHolder.txtAddRight": "Add right border", "SSE.Controllers.DocumentHolder.txtAddTop": "Add top border", "SSE.Controllers.DocumentHolder.txtAddVer": "Add vertical line", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Align to character", "SSE.Controllers.DocumentHolder.txtAll": "(All)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "SSE.Controllers.DocumentHolder.txtAnd": "and", "SSE.Controllers.DocumentHolder.txtBegins": "Begins with", "SSE.Controllers.DocumentHolder.txtBelowAve": "Below average", "SSE.Controllers.DocumentHolder.txtBlanks": "(Blanks)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Border properties", "SSE.Controllers.DocumentHolder.txtBottom": "Bottom", "SSE.Controllers.DocumentHolder.txtColumn": "Column", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Column alignment", "SSE.Controllers.DocumentHolder.txtContains": "Contains", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copied to the clipboard", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returns the data cells of the table or specified table columns", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Decrease argument size", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Delete argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Delete manual break", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Delete enclosing characters", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Delete enclosing characters and separators", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Delete equation", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Delete char", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Delete radical", "SSE.Controllers.DocumentHolder.txtEnds": "Ends with", "SSE.Controllers.DocumentHolder.txtEquals": "Equals", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Equal to cell color", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Equal to font color", "SSE.Controllers.DocumentHolder.txtExpand": "Expand and sort", "SSE.Controllers.DocumentHolder.txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Bottom", "SSE.Controllers.DocumentHolder.txtFilterTop": "Top", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Change to linear fraction", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Change to skewed fraction", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Change to stacked fraction", "SSE.Controllers.DocumentHolder.txtGreater": "Greater than", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Greater than or equal to", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Char over text", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char under text", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returns the column headers for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtHeight": "Height", "SSE.Controllers.DocumentHolder.txtHideBottom": "Hide bottom border", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Hide bottom limit", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Hide closing bracket", "SSE.Controllers.DocumentHolder.txtHideDegree": "Hide degree", "SSE.Controllers.DocumentHolder.txtHideHor": "Hide horizontal line", "SSE.Controllers.DocumentHolder.txtHideLB": "<PERSON><PERSON> left bottom line", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON> left border", "SSE.Controllers.DocumentHolder.txtHideLT": "<PERSON><PERSON> left top line", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Hide opening bracket", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Hide placeholder", "SSE.Controllers.DocumentHolder.txtHideRight": "Hide right border", "SSE.Controllers.DocumentHolder.txtHideTop": "Hide top border", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Hide top limit", "SSE.Controllers.DocumentHolder.txtHideVer": "Hide vertical line", "SSE.Controllers.DocumentHolder.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Increase argument size", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Insert argument after", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Insert argument before", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Insert manual break", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Insert equation after", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Insert equation before", "SSE.Controllers.DocumentHolder.txtItems": "items", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Keep text only", "SSE.Controllers.DocumentHolder.txtLess": "Less than", "SSE.Controllers.DocumentHolder.txtLessEquals": "Less than or equal to", "SSE.Controllers.DocumentHolder.txtLimitChange": "Change limits location", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limit over text", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limit under text", "SSE.Controllers.DocumentHolder.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Match brackets to argument height", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Matrix alignment", "SSE.Controllers.DocumentHolder.txtNoChoices": "There are no choices for filling the cell.<br>Only text values from the column can be selected for replacement.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Does not begin with", "SSE.Controllers.DocumentHolder.txtNotContains": "Does not contain", "SSE.Controllers.DocumentHolder.txtNotEnds": "Does not end with", "SSE.Controllers.DocumentHolder.txtNotEquals": "Does not equal", "SSE.Controllers.DocumentHolder.txtOr": "or", "SSE.Controllers.DocumentHolder.txtOverbar": "Bar over text", "SSE.Controllers.DocumentHolder.txtPaste": "Paste", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula without borders", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + column width", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Destination formatting", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Paste only formatting", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + number format", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Paste only formula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + all formatting", "SSE.Controllers.DocumentHolder.txtPasteLink": "Paste link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Linked picture", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Merge conditional formatting", "SSE.Controllers.DocumentHolder.txtPastePicture": "Picture", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Source formatting", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpose", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Value + all formatting", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Value + number format", "SSE.Controllers.DocumentHolder.txtPasteValues": "Paste only value", "SSE.Controllers.DocumentHolder.txtPercent": "percent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Redo table autoexpansion", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Remove fraction bar", "SSE.Controllers.DocumentHolder.txtRemLimit": "Remove limit", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Remove accent character", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Remove bar", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Remove scripts", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Remove subscript", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Remove superscript", "SSE.Controllers.DocumentHolder.txtRowHeight": "Row height", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scripts after text", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts before text", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Show bottom limit", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Show closing bracket", "SSE.Controllers.DocumentHolder.txtShowDegree": "Show degree", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Show opening bracket", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Show placeholder", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Show top limit", "SSE.Controllers.DocumentHolder.txtSorting": "Sorting", "SSE.Controllers.DocumentHolder.txtSortSelected": "Sort selected", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Stretch brackets", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choose only this row of the specified column", "SSE.Controllers.DocumentHolder.txtTop": "Top", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returns the total rows for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtUnderbar": "Bar under text", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Undo table autoexpansion", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Use text import wizard", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "All", "SSE.Controllers.FormulaDialog.sCategoryCube": "C<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Database", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Date and time", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Engineering", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financial", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Information", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 last used", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logical", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Lookup and reference", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Math and trigonometry", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistical", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text and data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Unnamed spreadsheet", "SSE.Controllers.LeftMenu.textByColumns": "By columns", "SSE.Controllers.LeftMenu.textByRows": "By rows", "SSE.Controllers.LeftMenu.textFormulas": "Formulas", "SSE.Controllers.LeftMenu.textItemEntireCell": "Entire cell contents", "SSE.Controllers.LeftMenu.textLoadHistory": "Loading version history...", "SSE.Controllers.LeftMenu.textLookin": "Look in", "SSE.Controllers.LeftMenu.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "SSE.Controllers.LeftMenu.textSearch": "Search", "SSE.Controllers.LeftMenu.textSheet": "Sheet", "SSE.Controllers.LeftMenu.textValues": "Values", "SSE.Controllers.LeftMenu.textWarning": "Warning", "SSE.Controllers.LeftMenu.textWithin": "Within", "SSE.Controllers.LeftMenu.textWorkbook": "Workbook", "SSE.Controllers.LeftMenu.txtUntitled": "Untitled", "SSE.Controllers.LeftMenu.warnDownloadAs": "If you continue saving in this format all features except the text will be lost.<br>Are you sure you want to continue?", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "The destination cell range can contain data. Continue the operation?", "SSE.Controllers.Main.confirmPutMergeRange": "The source data contained merged cells.<br>They had been unmerged before they were pasted into the table.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formulas in the header row will be removed and converted to static text.<br>Do you want to continue?", "SSE.Controllers.Main.convertationTimeoutText": "Conversion timeout exceeded.", "SSE.Controllers.Main.criticalErrorExtText": "Press \"OK\" to return to document list.", "SSE.Controllers.Main.criticalErrorTitle": "Error", "SSE.Controllers.Main.downloadErrorText": "Download failed.", "SSE.Controllers.Main.downloadTextText": "Downloading spreadsheet...", "SSE.Controllers.Main.downloadTitleText": "Downloading Spreadsheet", "SSE.Controllers.Main.errNoDuplicates": "No duplicate values found.", "SSE.Controllers.Main.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorArgsRange": "An error in the entered formula.<br>Incorrect argument range is used.", "SSE.Controllers.Main.errorAutoFilterChange": "The operation is not allowed, as it is attempting to shift cells in a table on your worksheet.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "The operation could not be done for the selected cells as you cannot move a part of the table.<br>Select another data range so that the whole table was shifted and try again.", "SSE.Controllers.Main.errorAutoFilterDataRange": "The operation could not be done for the selected range of cells.<br>Select a uniform data range different from the existing one and try again.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "The operation cannot be performed because the area contains filtered cells.<br>Please unhide the filtered elements and try again.", "SSE.Controllers.Main.errorBadImageUrl": "Image URL is incorrect", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Cannot ungroup. To start an outline, select the detail rows or columns and group them.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "SSE.Controllers.Main.errorChangeArray": "You cannot change part of an array.", "SSE.Controllers.Main.errorChangeFilteredRange": "This will change a filtered range on your worksheet.<br>To complete this task, please remove AutoFilters.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "The cell or chart you are trying to change is on a protected sheet.<br>To make a change, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Server connection lost. The document cannot be edited right now.", "SSE.Controllers.Main.errorConnectToServer": "The document could not be saved. Please check connection settings or contact your administrator.<br>When you click the 'OK' button, you will be prompted to download the document.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "This command cannot be used with multiple selections.<br>Select a single range and try again.", "SSE.Controllers.Main.errorCountArg": "An error in the entered formula.<br>Incorrect number of arguments is used.", "SSE.Controllers.Main.errorCountArgExceed": "An error in the entered formula.<br>Number of arguments is exceeded.", "SSE.Controllers.Main.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "External error.<br>Database connection error. Please contact support in case the error persists.", "SSE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "SSE.Controllers.Main.errorDataRange": "Incorrect data range.", "SSE.Controllers.Main.errorDataValidate": "The value you entered is not valid.<br>A user has restricted values that can be entered into this cell.", "SSE.Controllers.Main.errorDefaultMessage": "Error code: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "You are trying to delete a column that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "You are trying to delete a row that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download as' option to save the file backup copy to a drive.", "SSE.Controllers.Main.errorEditingSaveas": "An error occurred during the work with the document.<br>Use the 'Save as...' option to save the file backup copy to a drive.", "SSE.Controllers.Main.errorEditView": "The existing sheet view cannot be edited and the new ones cannot be created at the moment as some of them are being edited.", "SSE.Controllers.Main.errorEmailClient": "No email client could be found.", "SSE.Controllers.Main.errorFilePassProtect": "The file is password protected and cannot be opened.", "SSE.Controllers.Main.errorFileRequest": "External error.<br>File request error. Please contact support in case the error persists.", "SSE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "SSE.Controllers.Main.errorFileVKey": "External error.<br>Incorrect security key. Please contact support in case the error persists.", "SSE.Controllers.Main.errorFillRange": "Could not fill the selected range of cells.<br>All the merged cells need to be the same size.", "SSE.Controllers.Main.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to a drive or try again later.", "SSE.Controllers.Main.errorFormulaName": "An error in the entered formula.<br>Incorrect formula name is used.", "SSE.Controllers.Main.errorFormulaParsing": "Internal error while parsing the formula.", "SSE.Controllers.Main.errorFrmlMaxLength": "The length of your formula exceeds the limit of 8192 characters.<br>Please edit it and try again.", "SSE.Controllers.Main.errorFrmlMaxReference": "You cannot enter this formula because it has too many values,<br>cell references, and/or names.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Text values in formulas are limited to 255 characters.<br>Use the CONCATENATE function or concatenation operator (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "The function refers to a sheet that does not exist.<br>Please check the data and try again.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operation could not be completed for the selected cell range.<br>Select a range so that the first table row was on the same row<br>and the resulting table overlapped the current one.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operation could not be completed for the selected cell range.<br>Select a range which does not include other tables.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInvalidRef": "Enter a correct name for the selection or a valid reference to go to.", "SSE.Controllers.Main.errorKeyEncrypt": "Unknown key descriptor", "SSE.Controllers.Main.errorKeyExpire": "Key descriptor expired", "SSE.Controllers.Main.errorLabledColumnsPivot": "To create a pivot table, use data that is organized as a list with labeled columns.", "SSE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "The reference for the location or data range is not valid.", "SSE.Controllers.Main.errorLockedAll": "The operation could not be done as the sheet has been locked by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "You cannot change data inside a pivot table.", "SSE.Controllers.Main.errorLockedWorksheetRename": "The sheet cannot be renamed at the moment as it is being renamed by another user", "SSE.Controllers.Main.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Main.errorMoveRange": "Cannot change part of a merged cell", "SSE.Controllers.Main.errorMoveSlicerError": "Table slicers cannot be copied from one workbook to another.<br>Try again by selecting the entire table and the slicers.", "SSE.Controllers.Main.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Controllers.Main.errorNoDataToParse": "No data was selected to parse.", "SSE.Controllers.Main.errorOpenWarning": "One of the file formulas exceeds the limit of 8192 characters.<br>The formula was removed.", "SSE.Controllers.Main.errorOperandExpected": "The entered function syntax is not correct. Please check if you are missing one of the parentheses - '(' or ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "SSE.Controllers.Main.errorPasteMaxRange": "The copy and paste area do not match.<br>Please select an area with the same size or click the first cell in a row to paste the copied cells.", "SSE.Controllers.Main.errorPasteMultiSelect": "This action cannot be done on a multiple range selection.<br>Select a single range and try again.", "SSE.Controllers.Main.errorPasteSlicerError": "Table slicers cannot be copied from one workbook to another.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON><PERSON> group that selection.", "SSE.Controllers.Main.errorPivotOverlap": "A pivot table report cannot overlap a table.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "The Pivot Table report was saved without the underlying data.<br>Use the 'Refresh' button to update the report.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Unfortunately, it is not possible to print more than 1500 pages at once in the current program version.<br>This restriction will be removed in the upcoming releases.", "SSE.Controllers.Main.errorProcessSaveResult": "Saving failed", "SSE.Controllers.Main.errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "SSE.Controllers.Main.errorSessionAbsolute": "The document editing session has expired. Please reload the page.", "SSE.Controllers.Main.errorSessionIdle": "The document has not been edited for quite a long time. Please reload the page.", "SSE.Controllers.Main.errorSessionToken": "The connection to the server has been interrupted. Please reload the page.", "SSE.Controllers.Main.errorSetPassword": "Password could not be set.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Location reference is not valid because the cells are not all in the same column or row.<br>Select cells that are all in a single column or row.", "SSE.Controllers.Main.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Controllers.Main.errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorUnexpectedGuid": "External error.<br>Unexpected GUID. Please contact support in case the error persists.", "SSE.Controllers.Main.errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "SSE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "SSE.Controllers.Main.errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "SSE.Controllers.Main.errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but will not be able to download or print it until the connection is restored and page is reloaded.", "SSE.Controllers.Main.errorWrongBracketsCount": "An error in the entered formula.<br>Wrong number of brackets is used.", "SSE.Controllers.Main.errorWrongOperator": "An error in the entered formula. Wrong operator is used.<br>Please correct the error.", "SSE.Controllers.Main.errorWrongPassword": "The password you supplied is not correct.", "SSE.Controllers.Main.errRemDuplicates": "Duplicate values found and deleted: {0}, unique values left: {1}.", "SSE.Controllers.Main.leavePageText": "You have unsaved changes in this spreadsheet. Click 'Stay on this Page' then 'Save' to save them. Click 'Leave this Page' to discard all the unsaved changes.", "SSE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this spreadsheet will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "SSE.Controllers.Main.loadFontsTextText": "Loading data...", "SSE.Controllers.Main.loadFontsTitleText": "Loading Data", "SSE.Controllers.Main.loadFontTextText": "Loading data...", "SSE.Controllers.Main.loadFontTitleText": "Loading Data", "SSE.Controllers.Main.loadImagesTextText": "Loading images...", "SSE.Controllers.Main.loadImagesTitleText": "Loading Images", "SSE.Controllers.Main.loadImageTextText": "Loading image...", "SSE.Controllers.Main.loadImageTitleText": "Loading Image", "SSE.Controllers.Main.loadingDocumentTitleText": "Loading spreadsheet", "SSE.Controllers.Main.notcriticalErrorTitle": "Warning", "SSE.Controllers.Main.openErrorText": "An error has occurred while opening the file.", "SSE.Controllers.Main.openTextText": "Opening spreadsheet...", "SSE.Controllers.Main.openTitleText": "Opening Spreadsheet", "SSE.Controllers.Main.pastInMergeAreaError": "Cannot change part of a merged cell", "SSE.Controllers.Main.printTextText": "Printing spreadsheet...", "SSE.Controllers.Main.printTitleText": "Printing Spreadsheet", "SSE.Controllers.Main.reloadButtonText": "Reload Page", "SSE.Controllers.Main.requestEditFailedMessageText": "Someone is editing this document right now. Please try again later.", "SSE.Controllers.Main.requestEditFailedTitleText": "Access denied", "SSE.Controllers.Main.saveErrorText": "An error has occurred while saving the file.", "SSE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "SSE.Controllers.Main.saveTextText": "Saving spreadsheet...", "SSE.Controllers.Main.saveTitleText": "Saving Spreadsheet", "SSE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "SSE.Controllers.Main.textAnonymous": "Anonymous", "SSE.Controllers.Main.textApplyAll": "Apply to all equations", "SSE.Controllers.Main.textBuyNow": "Visit website", "SSE.Controllers.Main.textChangesSaved": "All changes saved", "SSE.Controllers.Main.textClose": "Close", "SSE.Controllers.Main.textCloseTip": "Click to close the tip", "SSE.Controllers.Main.textConfirm": "Confirmation", "SSE.Controllers.Main.textContactUs": "Contact sales", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "SSE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "SSE.Controllers.Main.textDisconnect": "Connection is lost", "SSE.Controllers.Main.textFillOtherRows": "Fill other rows", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula filled {0} rows have data. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula filled first {0} rows. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula filled only first {0} rows have data by memory save reason. There are other {1} rows have data in this sheet. You can fill them manually.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula filled only first {0} rows by memory save reason. Other rows in this sheet don't have data.", "SSE.Controllers.Main.textGuest": "Guest", "SSE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "SSE.Controllers.Main.textLearnMore": "Learn More", "SSE.Controllers.Main.textLoadingDocument": "Loading spreadsheet", "SSE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "SSE.Controllers.Main.textNeedSynchronize": "You have updates", "SSE.Controllers.Main.textNo": "No", "SSE.Controllers.Main.textNoLicenseTitle": "License limit reached", "SSE.Controllers.Main.textPaidFeature": "Paid feature", "SSE.Controllers.Main.textPleaseWait": "The operation might take more time than expected. Please wait...", "SSE.Controllers.Main.textReconnect": "Connection is restored", "SSE.Controllers.Main.textRemember": "Remember my choice for all files", "SSE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "SSE.Controllers.Main.textRenameError": "User name must not be empty.", "SSE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "SSE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "SSE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textStrict": "Strict mode", "SSE.Controllers.Main.textText": "Text", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "SSE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textYes": "Yes", "SSE.Controllers.Main.titleLicenseExp": "License expired", "SSE.Controllers.Main.titleServerVersion": "Editor updated", "SSE.Controllers.Main.txtAccent": "Accent", "SSE.Controllers.Main.txtAll": "(All)", "SSE.Controllers.Main.txtArt": "Your text here", "SSE.Controllers.Main.txtBasicShapes": "Basic shapes", "SSE.Controllers.Main.txtBlank": "(blank)", "SSE.Controllers.Main.txtButtons": "Buttons", "SSE.Controllers.Main.txtByField": "%1 of %2", "SSE.Controllers.Main.txtCallouts": "Callouts", "SSE.Controllers.Main.txtCharts": "Charts", "SSE.Controllers.Main.txtClearFilter": "Clear Filter", "SSE.Controllers.Main.txtColLbls": "Column Labels", "SSE.Controllers.Main.txtColumn": "Column", "SSE.Controllers.Main.txtConfidential": "Confidential", "SSE.Controllers.Main.txtDate": "Date", "SSE.Controllers.Main.txtDays": "Days", "SSE.Controllers.Main.txtDiagramTitle": "Chart Title", "SSE.Controllers.Main.txtEditingMode": "Set editing mode...", "SSE.Controllers.Main.txtErrorLoadHistory": "History loading failed", "SSE.Controllers.Main.txtFiguredArrows": "Figured arrows", "SSE.Controllers.Main.txtFile": "File", "SSE.Controllers.Main.txtGrandTotal": "Grand Total", "SSE.Controllers.Main.txtGroup": "Group", "SSE.Controllers.Main.txtHours": "Hours", "SSE.Controllers.Main.txtLines": "Lines", "SSE.Controllers.Main.txtMath": "Math", "SSE.Controllers.Main.txtMinutes": "Minutes", "SSE.Controllers.Main.txtMonths": "Months", "SSE.Controllers.Main.txtMultiSelect": "Multi-Select", "SSE.Controllers.Main.txtOr": "%1 or %2", "SSE.Controllers.Main.txtPage": "Page", "SSE.Controllers.Main.txtPageOf": "Page %1 of %2", "SSE.Controllers.Main.txtPages": "Pages", "SSE.Controllers.Main.txtPreparedBy": "Prepared by", "SSE.Controllers.Main.txtPrintArea": "Print_Area", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Quarters", "SSE.Controllers.Main.txtRectangles": "Rectangles", "SSE.Controllers.Main.txtRow": "Row", "SSE.Controllers.Main.txtRowLbls": "Row Labels", "SSE.Controllers.Main.txtSeconds": "Seconds", "SSE.Controllers.Main.txtSeries": "Series", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Line Callout 1 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Line Callout 2 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Line Callout 3 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout1": "Line Callout 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "Line Callout 2 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout3": "Line Callout 3 (Accent Bar)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Back or Previous <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Beginning Button", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Document Button", "SSE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or Next <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Help Button", "SSE.Controllers.Main.txtShape_actionButtonHome": "Home Button", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Information But<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Movie Button", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Return Button", "SSE.Controllers.Main.txtShape_actionButtonSound": "Sound Button", "SSE.Controllers.Main.txtShape_arc": "Arc", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Elbow Connector", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Elbow Arrow Connector", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Elbow Double-Arrow Connector", "SSE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Block Arc", "SSE.Controllers.Main.txtShape_borderCallout1": "Line Callout 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Line Callout 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Line Callout 3", "SSE.Controllers.Main.txtShape_bracePair": "Double Brace", "SSE.Controllers.Main.txtShape_callout1": "Line Callout 1 (No Border)", "SSE.Controllers.Main.txtShape_callout2": "Line Callout 2 (No Border)", "SSE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No Border)", "SSE.Controllers.Main.txtShape_can": "Can", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Chord", "SSE.Controllers.Main.txtShape_circularArrow": "Circular Arrow", "SSE.Controllers.Main.txtShape_cloud": "Cloud", "SSE.Controllers.Main.txtShape_cloudCallout": "Cloud Callout", "SSE.Controllers.Main.txtShape_corner": "Corner", "SSE.Controllers.Main.txtShape_cube": "C<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Curved Connector", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Curved Arrow Connector", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Curved Double-Arrow Connector", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Curved Down Arrow", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Curved Left Arrow", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Curved Right Arrow", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Curved Up Arrow", "SSE.Controllers.Main.txtShape_decagon": "Decagon", "SSE.Controllers.Main.txtShape_diagStripe": "Diagonal Stripe", "SSE.Controllers.Main.txtShape_diamond": "Diamond", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "SSE.Controllers.Main.txtShape_donut": "Donut", "SSE.Controllers.Main.txtShape_doubleWave": "Double Wave", "SSE.Controllers.Main.txtShape_downArrow": "Down Arrow", "SSE.Controllers.Main.txtShape_downArrowCallout": "Down Arrow Callout", "SSE.Controllers.Main.txtShape_ellipse": "Ellipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Curved Down Ribbon", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Curved Up Ribbon", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowchart: Alternate Process", "SSE.Controllers.Main.txtShape_flowChartCollate": "Flowchart: Collate", "SSE.Controllers.Main.txtShape_flowChartConnector": "Flowchart: Connector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Flowchart: Decision", "SSE.Controllers.Main.txtShape_flowChartDelay": "Flowchart: Delay", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Flowchart: Display", "SSE.Controllers.Main.txtShape_flowChartDocument": "Flowchart: Document", "SSE.Controllers.Main.txtShape_flowChartExtract": "Flowchart: Extract", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Flowchart: Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowchart: Internal Storage", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowchart: Magnetic Disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowchart: Direct Access Storage", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowchart: Sequential Access Storage", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Flowchart: Manual Input", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Flowchart: Manual Operation", "SSE.Controllers.Main.txtShape_flowChartMerge": "Flowchart: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Flowchart: Multidocument ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowchart: Off-page Connector", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowchart: Stored Data", "SSE.Controllers.Main.txtShape_flowChartOr": "Flowchart: Or", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowchart: Predefined Process", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Flowchart: Preparation", "SSE.Controllers.Main.txtShape_flowChartProcess": "Flowchart: Process", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowchart: Card", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowchart: Punched Ta<PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Flowchart: Sort", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowchart: Summing Junction", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Flowchart: Terminator", "SSE.Controllers.Main.txtShape_foldedCorner": "Folded Corner", "SSE.Controllers.Main.txtShape_frame": "<PERSON>ame", "SSE.Controllers.Main.txtShape_halfFrame": "Half Frame", "SSE.Controllers.Main.txtShape_heart": "Heart", "SSE.Controllers.Main.txtShape_heptagon": "Heptagon", "SSE.Controllers.Main.txtShape_hexagon": "Hexagon", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON> Scroll", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "SSE.Controllers.Main.txtShape_leftArrow": "Left Arrow", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Left <PERSON> Callout", "SSE.Controllers.Main.txtShape_leftBrace": "Left Brace", "SSE.Controllers.Main.txtShape_leftBracket": "Left Bracket", "SSE.Controllers.Main.txtShape_leftRightArrow": "Left Right Arrow", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Left Right <PERSON> Callout", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Left Right Up Arrow", "SSE.Controllers.Main.txtShape_leftUpArrow": "Left Up Arrow", "SSE.Controllers.Main.txtShape_lightningBolt": "Lightning Bolt", "SSE.Controllers.Main.txtShape_line": "Line", "SSE.Controllers.Main.txtShape_lineWithArrow": "Arrow", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Double Arrow", "SSE.Controllers.Main.txtShape_mathDivide": "Division", "SSE.Controllers.Main.txtShape_mathEqual": "Equal", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiply", "SSE.Controllers.Main.txtShape_mathNotEqual": "Not Equal", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "Moon", "SSE.Controllers.Main.txtShape_noSmoking": "\"No\" Symbol", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Notched Right Arrow", "SSE.Controllers.Main.txtShape_octagon": "Octagon", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon", "SSE.Controllers.Main.txtShape_pie": "Pie", "SSE.Controllers.Main.txtShape_plaque": "Sign", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Scribble", "SSE.Controllers.Main.txtShape_polyline2": "Freeform", "SSE.Controllers.Main.txtShape_quadArrow": "Quad Arrow", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Quad <PERSON> Callout", "SSE.Controllers.Main.txtShape_rect": "Rectangle", "SSE.Controllers.Main.txtShape_ribbon": "Down Ribbon", "SSE.Controllers.Main.txtShape_ribbon2": "Up Ribbon", "SSE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Right <PERSON> Callout", "SSE.Controllers.Main.txtShape_rightBrace": "Right Brace", "SSE.Controllers.Main.txtShape_rightBracket": "Right Bracket", "SSE.Controllers.Main.txtShape_round1Rect": "Round Single Corner Rectangle", "SSE.Controllers.Main.txtShape_round2DiagRect": "Round Diagonal Corner Rectangle", "SSE.Controllers.Main.txtShape_round2SameRect": "Round Same Side Corner Rectangle", "SSE.Controllers.Main.txtShape_roundRect": "Round Corner Rectangle", "SSE.Controllers.Main.txtShape_rtTriangle": "Right Triangle", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Snip Single Corner Rectangle", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Snip Diagonal Corner Rectangle", "SSE.Controllers.Main.txtShape_snip2SameRect": "Snip Same Side Corner Rectangle", "SSE.Controllers.Main.txtShape_snipRoundRect": "Snip and Round Single Corner Rectangle", "SSE.Controllers.Main.txtShape_spline": "Curve", "SSE.Controllers.Main.txtShape_star10": "10-Point Star", "SSE.Controllers.Main.txtShape_star12": "12-Point Star", "SSE.Controllers.Main.txtShape_star16": "16-Point Star", "SSE.Controllers.Main.txtShape_star24": "24-Point Star", "SSE.Controllers.Main.txtShape_star32": "32-Point Star", "SSE.Controllers.Main.txtShape_star4": "4-Point Star", "SSE.Controllers.Main.txtShape_star5": "5-Point Star", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON> Star", "SSE.Controllers.Main.txtShape_star7": "7-Point Star", "SSE.Controllers.Main.txtShape_star8": "8-Point Star", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Striped Right Arrow", "SSE.Controllers.Main.txtShape_sun": "Sun", "SSE.Controllers.Main.txtShape_teardrop": "Teardrop", "SSE.Controllers.Main.txtShape_textRect": "Text Box", "SSE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "SSE.Controllers.Main.txtShape_triangle": "Triangle", "SSE.Controllers.Main.txtShape_upArrow": "Up Arrow", "SSE.Controllers.Main.txtShape_upArrowCallout": "Up Arrow Callout", "SSE.Controllers.Main.txtShape_upDownArrow": "Up Down Arrow", "SSE.Controllers.Main.txtShape_uturnArrow": "U-Turn Arrow", "SSE.Controllers.Main.txtShape_verticalScroll": "Vertical Scroll", "SSE.Controllers.Main.txtShape_wave": "Wave", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Callout", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangular Callout", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rounded Rectangular Callout", "SSE.Controllers.Main.txtStarsRibbons": "Stars & Ribbons", "SSE.Controllers.Main.txtStyle_Bad": "Bad", "SSE.Controllers.Main.txtStyle_Calculation": "Calculation", "SSE.Controllers.Main.txtStyle_Check_Cell": "Check Cell", "SSE.Controllers.Main.txtStyle_Comma": "Comma", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Explanatory Text", "SSE.Controllers.Main.txtStyle_Good": "Good", "SSE.Controllers.Main.txtStyle_Heading_1": "Heading 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Heading 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Heading 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Heading 4", "SSE.Controllers.Main.txtStyle_Input": "Input", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Linked Cell", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "Note", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "Percent", "SSE.Controllers.Main.txtStyle_Title": "Title", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Warning Text", "SSE.Controllers.Main.txtTab": "Tab", "SSE.Controllers.Main.txtTable": "Table", "SSE.Controllers.Main.txtTime": "Time", "SSE.Controllers.Main.txtUnlock": "Unlock", "SSE.Controllers.Main.txtUnlockRange": "Unlock Range", "SSE.Controllers.Main.txtUnlockRangeDescription": "Enter the password to change this range:", "SSE.Controllers.Main.txtUnlockRangeWarning": "A range you are trying to change is password protected.", "SSE.Controllers.Main.txtValues": "Values", "SSE.Controllers.Main.txtXAxis": "X Axis", "SSE.Controllers.Main.txtYAxis": "Y Axis", "SSE.Controllers.Main.txtYears": "Years", "SSE.Controllers.Main.unknownErrorText": "Unknown error.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Your browser is not supported.", "SSE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "SSE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "SSE.Controllers.Main.uploadImageExtMessage": "Unknown image format.", "SSE.Controllers.Main.uploadImageFileCountMessage": "No images uploaded.", "SSE.Controllers.Main.uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Uploading image...", "SSE.Controllers.Main.uploadImageTitleText": "Uploading Image", "SSE.Controllers.Main.waitText": "Please, wait...", "SSE.Controllers.Main.warnBrowserIE9": "The application has low capabilities on IE9. Use IE10 or higher", "SSE.Controllers.Main.warnBrowserZoom": "Your browser current zoom setting is not fully supported. Please reset to the default zoom by pressing Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "SSE.Controllers.Main.warnLicenseExp": "Your license has expired.<br>Please update your license and refresh the page.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "SSE.Controllers.Main.warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "SSE.Controllers.Main.warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact %1 sales team for personal upgrade terms.", "SSE.Controllers.Main.warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "SSE.Controllers.Main.warnProcessRightsChange": "You have been denied the right to edit the file.", "SSE.Controllers.Print.strAllSheets": "All Sheets", "SSE.Controllers.Print.textFirstCol": "First column", "SSE.Controllers.Print.textFirstRow": "First row", "SSE.Controllers.Print.textFrozenCols": "Frozen columns", "SSE.Controllers.Print.textFrozenRows": "Frozen rows", "SSE.Controllers.Print.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Print.textNoRepeat": "Don't repeat", "SSE.Controllers.Print.textRepeat": "Repeat...", "SSE.Controllers.Print.textSelectRange": "Select range", "SSE.Controllers.Print.textWarning": "Warning", "SSE.Controllers.Print.txtCustom": "Custom", "SSE.Controllers.Print.warnCheckMargings": "Margins are incorrect", "SSE.Controllers.Search.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "SSE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "SSE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "SSE.Controllers.Statusbar.errorLastSheet": "Workbook must have at least one visible worksheet.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Cannot delete the worksheet.", "SSE.Controllers.Statusbar.strSheet": "Sheet", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "SSE.Controllers.Statusbar.textSheetViewTip": "You are in Sheet View mode. Filters and sorting are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "You are in Sheet View mode. Filters are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.warnDeleteSheet": "The selected worksheets might contain data. Are you sure you want to proceed?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the system fonts, the saved font will be used when it is available.<br>Do you want to continue?", "SSE.Controllers.Toolbar.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ERROR! The maximum number of data series per chart is 255", "SSE.Controllers.Toolbar.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Controllers.Toolbar.textAccent": "Accents", "SSE.Controllers.Toolbar.textBracket": "Brackets", "SSE.Controllers.Toolbar.textDirectional": "Directional", "SSE.Controllers.Toolbar.textFontSizeErr": "The entered value is incorrect.<br>Please enter a numeric value between 1 and 409", "SSE.Controllers.Toolbar.textFraction": "Fractions", "SSE.Controllers.Toolbar.textFunction": "Functions", "SSE.Controllers.Toolbar.textIndicator": "Indicators", "SSE.Controllers.Toolbar.textInsert": "Insert", "SSE.Controllers.Toolbar.textIntegral": "Integrals", "SSE.Controllers.Toolbar.textLargeOperator": "Large Operators", "SSE.Controllers.Toolbar.textLimitAndLog": "Limits and Logarithms", "SSE.Controllers.Toolbar.textLongOperation": "Long operation", "SSE.Controllers.Toolbar.textMatrix": "Matrices", "SSE.Controllers.Toolbar.textOperator": "Operators", "SSE.Controllers.Toolbar.textPivot": "Pivot Table", "SSE.Controllers.Toolbar.textRadical": "Radicals", "SSE.Controllers.Toolbar.textRating": "Ratings", "SSE.Controllers.Toolbar.textRecentlyUsed": "Recently Used", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textSymbols": "Symbols", "SSE.Controllers.Toolbar.textWarning": "Warning", "SSE.Controllers.Toolbar.txtAccent_Accent": "Acute", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Right-left arrow above", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Leftwards arrow above", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Rightwards arrow above", "SSE.Controllers.Toolbar.txtAccent_Bar": "Bar", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Boxed formula (with placeholder)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Boxed formula (example)", "SSE.Controllers.Toolbar.txtAccent_Check": "Check", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC with overbar", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y with overbar", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Triple dot", "SSE.Controllers.Toolbar.txtAccent_DDot": "Double dot", "SSE.Controllers.Toolbar.txtAccent_Dot": "Dot", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Double overbar", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Grouping character below", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Grouping character above", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards harpoon above", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards harpoon above", "SSE.Controllers.Toolbar.txtAccent_Hat": "Hat", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Angle brackets", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Angle brackets with separator", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Angle brackets with two separators", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Right angle bracket", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Left angle bracket", "SSE.Controllers.Toolbar.txtBracket_Curve": "Curly brackets", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Curly brackets with separator", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Right curly bracket", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Left curly bracket", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Cases (two conditions)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Cases (three conditions)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Stack object", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Stack object in parentheses", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Cases example", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial coefficient", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial coefficient in angle brackets", "SSE.Controllers.Toolbar.txtBracket_Line": "Vertical bars", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Right vertical bar", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Left vertical bar", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Double vertical bars", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Right double vertical bar", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Left double vertical bar", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Floor", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Right floor", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Left floor", "SSE.Controllers.Toolbar.txtBracket_Round": "Parentheses", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentheses with separator", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Right parenthesis", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Left parenthesis", "SSE.Controllers.Toolbar.txtBracket_Square": "Square brackets", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Placeholder between two right square brackets", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Inverted square brackets", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Right square bracket", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Left square bracket", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Placeholder between two left square brackets", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Double square brackets", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Right double square bracket", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Left double square bracket", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Ceiling", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Right ceiling", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Left ceiling", "SSE.Controllers.Toolbar.txtDeleteCells": "Delete cells", "SSE.Controllers.Toolbar.txtExpand": "Expand and sort", "SSE.Controllers.Toolbar.txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Skewed fraction", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx over dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y over cap delta x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "partial y over partial x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "delta y over delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Linear fraction", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi over 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Small fraction", "SSE.Controllers.Toolbar.txtFractionVertical": "Stacked fraction", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverse cosine function", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolic inverse cosine function", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverse cotangent function", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolic inverse cotangent function", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse cosecant function", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolic inverse cosecant function", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverse secant function", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolic inverse secant function", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Inverse sine function", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolic inverse sine function", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverse tangent function", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolic inverse tangent function", "SSE.Controllers.Toolbar.txtFunction_Cos": "Cosine function", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolic cosine function", "SSE.Controllers.Toolbar.txtFunction_Cot": "Cotangent function", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolic cotangent function", "SSE.Controllers.Toolbar.txtFunction_Csc": "Cosecant function", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolic cosecant function", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "SSE.Controllers.Toolbar.txtFunction_Sec": "Secant function", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolic secant function", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sine function", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolic sine function", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangent function", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolic tangent function", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Dark", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Light", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "Insert cells", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralDouble": "Double integral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Double integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Double integral with limits", "SSE.Controllers.Toolbar.txtIntegralOriented": "Contour integral", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contour integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Surface integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Surface integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Surface integral with limits", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contour integral with limits", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume integral with limits", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral with limits", "SSE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral with limits", "SSE.Controllers.Toolbar.txtInvalidRange": "ERROR! Invalid cell range", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logical And", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Logical And with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Logical And with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logical And with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logical And with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-product", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-product with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-product with limits", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-product with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-product with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation over k of n choose k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation from i equal zero to n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation example using two indices", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product example", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union example", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Logical Or", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Logical Or with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Logical Or with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Logical Or with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logical Or with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit example", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maximum example", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Natural logarithm", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarithm", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithm", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Empty 2 by 2 matrix in double vertical bars", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Empty 2 by 2 determinant", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Empty 2 by 2 matrix in parentheses", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Empty 2 by 2 matrix in brackets", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Baseline dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonal dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Vertical dots", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse matrix in parentheses", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse matrix in brackets", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identity matrix with zeros", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 identity matrix with blank off-diagonal cells", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identity matrix with zeros", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identity matrix with blank off-diagonal cells", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Right-left arrow below", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Right-left arrow above", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Leftwards arrow below", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Leftwards arrow above", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Rightwards arrow below", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Rightwards arrow above", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Colon equal", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta yields", "SSE.Controllers.Toolbar.txtOperator_Definition": "Equal to by definition", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta equal to", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Right-left double arrow below", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Right-left double arrow above", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Leftwards arrow below", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Leftwards arrow above", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Rightwards arrow below", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Rightwards arrow above", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal equal", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus equal", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus equal", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Measured by", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Right hand side of quadratic formula", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Square root of a squared plus b squared", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Square root with degree", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Cubic root", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radical with degree", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Square root", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x subscript y squared", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e to the minus i omega t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x squared", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y left superscript n left subscript one", "SSE.Controllers.Toolbar.txtScriptSub": "Subscript", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subscript-superscript", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Left subscript-superscript", "SSE.Controllers.Toolbar.txtScriptSup": "Superscript", "SSE.Controllers.Toolbar.txtSorting": "Sorting", "SSE.Controllers.Toolbar.txtSortSelected": "Sort selected", "SSE.Controllers.Toolbar.txtSymbol_about": "Approximately", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complement", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "SSE.Controllers.Toolbar.txtSymbol_approx": "Almost equal to", "SSE.Controllers.Toolbar.txtSymbol_ast": "Asterisk operator", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Bullet operator", "SSE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Cube root", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Midline horizontal ellipsis", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Degrees Celsius", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Approximately equal to", "SSE.Controllers.Toolbar.txtSymbol_cup": "Union", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Down right diagonal ellipsis", "SSE.Controllers.Toolbar.txtSymbol_degree": "Degrees", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Division sign", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Down arrow", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Empty set", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Equal", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identical to", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "There exist", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Degrees Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "For all", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Greater than or equal to", "SSE.Controllers.Toolbar.txtSymbol_gg": "Much greater than", "SSE.Controllers.Toolbar.txtSymbol_greater": "Greater than", "SSE.Controllers.Toolbar.txtSymbol_in": "Element of", "SSE.Controllers.Toolbar.txtSymbol_inc": "Increment", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinity", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Left arrow", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Left-right arrow", "SSE.Controllers.Toolbar.txtSymbol_leq": "Less than or equal to", "SSE.Controllers.Toolbar.txtSymbol_less": "Less than", "SSE.Controllers.Toolbar.txtSymbol_ll": "Much less than", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Not equal to", "SSE.Controllers.Toolbar.txtSymbol_ni": "Contains as member", "SSE.Controllers.Toolbar.txtSymbol_not": "Not sign", "SSE.Controllers.Toolbar.txtSymbol_notexists": "There does not exist", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Partial differential", "SSE.Controllers.Toolbar.txtSymbol_percent": "Percentage", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proportional to", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Fourth root", "SSE.Controllers.Toolbar.txtSymbol_qed": "End of proof", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Up right diagonal ellipsis", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Right arrow", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Radical sign", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Therefore", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "Multiplication sign", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Up arrow", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Vertical ellipsis", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Table style Dark", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Table style Light", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Table style Medium", "SSE.Controllers.Toolbar.warnLongOperation": "The operation you are about to perform might take rather much time to complete.<br>Are you sure you want to continue?", "SSE.Controllers.Toolbar.warnMergeLostData": "Only the data from the upper-left cell will remain in the merged cell. <br>Are you sure you want to continue?", "SSE.Controllers.Viewport.textFreezePanes": "Freeze Panes", "SSE.Controllers.Viewport.textFreezePanesShadow": "Show Frozen Panes Shadow", "SSE.Controllers.Viewport.textHideFBar": "Hide Formula Bar", "SSE.Controllers.Viewport.textHideGridlines": "Hide Gridlines", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON> Headings", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Decimal separator", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Thousands separator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Settings used to recognize numeric data", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Advanced settings", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(none)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Custom filter", "SSE.Views.AutoFilterDialog.textAddSelection": "Add current selection to filter", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Select all", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Select all search results", "SSE.Views.AutoFilterDialog.textWarning": "Warning", "SSE.Views.AutoFilterDialog.txtAboveAve": "Above average", "SSE.Views.AutoFilterDialog.txtBegins": "Begins with...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Below average", "SSE.Views.AutoFilterDialog.txtBetween": "Between...", "SSE.Views.AutoFilterDialog.txtClear": "Clear", "SSE.Views.AutoFilterDialog.txtContains": "Contains...", "SSE.Views.AutoFilterDialog.txtEmpty": "Enter cell filter", "SSE.Views.AutoFilterDialog.txtEnds": "Ends with...", "SSE.Views.AutoFilterDialog.txtEquals": "Equals...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filter by cells color", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filter by font color", "SSE.Views.AutoFilterDialog.txtGreater": "Greater than...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Greater than or equal to...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Label filter", "SSE.Views.AutoFilterDialog.txtLess": "Less than...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Less than or equal to...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Does not begin with...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Not between...", "SSE.Views.AutoFilterDialog.txtNotContains": "Does not contain...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Does not end with...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Does not equal...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Number filter", "SSE.Views.AutoFilterDialog.txtReapply": "Reapply", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Sort by cells color", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Sort by font color", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Sort highest to lowest", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Sort lowest to highest", "SSE.Views.AutoFilterDialog.txtSortOption": "More sort options...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Text filter", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Value filter", "SSE.Views.AutoFilterDialog.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "You must choose at least one value", "SSE.Views.CellEditor.textManager": "Name Manager", "SSE.Views.CellEditor.tipFormula": "Insert function", "SSE.Views.CellRangeDialog.errorMaxRows": "ERROR! The maximum number of data series per chart is 255", "SSE.Views.CellRangeDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.CellRangeDialog.txtEmpty": "This field is required", "SSE.Views.CellRangeDialog.txtInvalidRange": "ERROR! Invalid cells range", "SSE.Views.CellRangeDialog.txtTitle": "Select data range", "SSE.Views.CellSettings.strShrink": "Shrink to fit", "SSE.Views.CellSettings.strWrap": "Wrap text", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Background Color", "SSE.Views.CellSettings.textBackground": "Background color", "SSE.Views.CellSettings.textBorderColor": "Color", "SSE.Views.CellSettings.textBorders": "Borders Style", "SSE.Views.CellSettings.textClearRule": "Clear Rules", "SSE.Views.CellSettings.textColor": "Color Fill", "SSE.Views.CellSettings.textColorScales": "Color Scales", "SSE.Views.CellSettings.textCondFormat": "Conditional formatting", "SSE.Views.CellSettings.textControl": "Text Control", "SSE.Views.CellSettings.textDataBars": "Data Bars", "SSE.Views.CellSettings.textDirection": "Direction", "SSE.Views.CellSettings.textFill": "Fill", "SSE.Views.CellSettings.textForeground": "Foreground color", "SSE.Views.CellSettings.textGradient": "Gradient points", "SSE.Views.CellSettings.textGradientColor": "Color", "SSE.Views.CellSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.CellSettings.textIndent": "Indent", "SSE.Views.CellSettings.textItems": "Items", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "Manage Rules", "SSE.Views.CellSettings.textNewRule": "New Rule", "SSE.Views.CellSettings.textNoFill": "No Fill", "SSE.Views.CellSettings.textOrientation": "Text Orientation", "SSE.Views.CellSettings.textPattern": "Pattern", "SSE.Views.CellSettings.textPatternFill": "Pattern", "SSE.Views.CellSettings.textPosition": "Position", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "SSE.Views.CellSettings.textSelection": "From current selection", "SSE.Views.CellSettings.textThisPivot": "From this pivot", "SSE.Views.CellSettings.textThisSheet": "From this worksheet", "SSE.Views.CellSettings.textThisTable": "From this table", "SSE.Views.CellSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.CellSettings.tipAll": "Set outer border and all inner lines", "SSE.Views.CellSettings.tipBottom": "Set outer bottom border only", "SSE.Views.CellSettings.tipDiagD": "Set Diagonal Down Border", "SSE.Views.CellSettings.tipDiagU": "Set Diagonal Up Border", "SSE.Views.CellSettings.tipInner": "Set inner lines only", "SSE.Views.CellSettings.tipInnerHor": "Set horizontal inner lines only", "SSE.Views.CellSettings.tipInnerVert": "Set vertical inner lines only", "SSE.Views.CellSettings.tipLeft": "Set outer left border only", "SSE.Views.CellSettings.tipNone": "Set no borders", "SSE.Views.CellSettings.tipOuter": "Set outer border only", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.CellSettings.tipRight": "Set outer right border only", "SSE.Views.CellSettings.tipTop": "Set outer top border only", "SSE.Views.ChartDataDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataDialog.textAdd": "Add", "SSE.Views.ChartDataDialog.textCategory": "Horizontal (category) axis labels", "SSE.Views.ChartDataDialog.textData": "Chart data range", "SSE.Views.ChartDataDialog.textDelete": "Remove", "SSE.Views.ChartDataDialog.textDown": "Down", "SSE.Views.ChartDataDialog.textEdit": "Edit", "SSE.Views.ChartDataDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataDialog.textSelectData": "Select data", "SSE.Views.ChartDataDialog.textSeries": "Legend entries (series)", "SSE.Views.ChartDataDialog.textSwitch": "Switch row/column", "SSE.Views.ChartDataDialog.textTitle": "Chart data", "SSE.Views.ChartDataDialog.textUp": "Up", "SSE.Views.ChartDataRangeDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataRangeDialog.textSelectData": "Select data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Axis label range", "SSE.Views.ChartDataRangeDialog.txtChoose": "Choose range", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Series name", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Axis labels", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Edit series", "SSE.Views.ChartDataRangeDialog.txtValues": "Values", "SSE.Views.ChartDataRangeDialog.txtXValues": "X values", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y values", "SSE.Views.ChartSettings.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartSettings.strLineWeight": "Line Weight", "SSE.Views.ChartSettings.strSparkColor": "Color", "SSE.Views.ChartSettings.strTemplate": "Template", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotation", "SSE.Views.ChartSettings.textAdvanced": "Show advanced settings", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Change type", "SSE.Views.ChartSettings.textChartType": "Change Chart Type", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "Edit Data and Location", "SSE.Views.ChartSettings.textFirstPoint": "First Point", "SSE.Views.ChartSettings.textHeight": "Height", "SSE.Views.ChartSettings.textHighPoint": "High Point", "SSE.Views.ChartSettings.textKeepRatio": "Constant proportions", "SSE.Views.ChartSettings.textLastPoint": "Last Point", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "Low Point", "SSE.Views.ChartSettings.textMarkers": "Markers", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "Negative Point", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Data Range", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right Angle Axes", "SSE.Views.ChartSettings.textSelectData": "Select Data", "SSE.Views.ChartSettings.textShow": "Show", "SSE.Views.ChartSettings.textSize": "Size", "SSE.Views.ChartSettings.textStyle": "Style", "SSE.Views.ChartSettings.textSwitch": "Switch Row/Column", "SSE.Views.ChartSettings.textType": "Type", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERROR! The maximum number of points in series per chart is 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ERROR! The maximum number of data series per chart is 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Don't move or size with cells", "SSE.Views.ChartSettingsDlg.textAlt": "Alternative text", "SSE.Views.ChartSettingsDlg.textAltDescription": "Description", "SSE.Views.ChartSettingsDlg.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart, or table.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Title", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Auto for each", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Axis crosses", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Axis options", "SSE.Views.ChartSettingsDlg.textAxisPos": "Axis position", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Axis settings", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Title", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Between tick marks", "SSE.Views.ChartSettingsDlg.textBillions": "Billions", "SSE.Views.ChartSettingsDlg.textBottom": "Bottom", "SSE.Views.ChartSettingsDlg.textCategoryName": "Category name", "SSE.Views.ChartSettingsDlg.textCenter": "Center", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Chart elements &<br>Chart legend", "SSE.Views.ChartSettingsDlg.textChartTitle": "Chart title", "SSE.Views.ChartSettingsDlg.textCross": "Cross", "SSE.Views.ChartSettingsDlg.textCustom": "Custom", "SSE.Views.ChartSettingsDlg.textDataColumns": "in columns", "SSE.Views.ChartSettingsDlg.textDataLabels": "Data labels", "SSE.Views.ChartSettingsDlg.textDataRows": "in rows", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Display legend", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Hidden and empty cells", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Connect data points with line", "SSE.Views.ChartSettingsDlg.textFit": "Fit to width", "SSE.Views.ChartSettingsDlg.textFixed": "Fixed", "SSE.Views.ChartSettingsDlg.textFormat": "Label format", "SSE.Views.ChartSettingsDlg.textGaps": "Gaps", "SSE.Views.ChartSettingsDlg.textGridLines": "Gridlines", "SSE.Views.ChartSettingsDlg.textGroup": "Group sparkline", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON>de", "SSE.Views.ChartSettingsDlg.textHideAxis": "Hide axis", "SSE.Views.ChartSettingsDlg.textHigh": "High", "SSE.Views.ChartSettingsDlg.textHorAxis": "Horizontal axis", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Secondary horizontal axis", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Hundreds", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "In", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Inner bottom", "SSE.Views.ChartSettingsDlg.textInnerTop": "Inner top", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ChartSettingsDlg.textLabelDist": "Axis label distance", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Interval between labels ", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Label options", "SSE.Views.ChartSettingsDlg.textLabelPos": "Label position", "SSE.Views.ChartSettingsDlg.textLayout": "Layout", "SSE.Views.ChartSettingsDlg.textLeft": "Left", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Left overlay", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Bottom", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Left", "SSE.Views.ChartSettingsDlg.textLegendPos": "Legend", "SSE.Views.ChartSettingsDlg.textLegendRight": "Right", "SSE.Views.ChartSettingsDlg.textLegendTop": "Top", "SSE.Views.ChartSettingsDlg.textLines": "Lines ", "SSE.Views.ChartSettingsDlg.textLocationRange": "Location range", "SSE.Views.ChartSettingsDlg.textLogScale": "Logarithmic scale", "SSE.Views.ChartSettingsDlg.textLow": "Low", "SSE.Views.ChartSettingsDlg.textMajor": "Major", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Major and minor", "SSE.Views.ChartSettingsDlg.textMajorType": "Major type", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "Markers", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Interval between marks", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maximum value", "SSE.Views.ChartSettingsDlg.textMillions": "Millions", "SSE.Views.ChartSettingsDlg.textMinor": "Minor", "SSE.Views.ChartSettingsDlg.textMinorType": "Minor type", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimum value", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Next to axis", "SSE.Views.ChartSettingsDlg.textNone": "None", "SSE.Views.ChartSettingsDlg.textNoOverlay": "No overlay", "SSE.Views.ChartSettingsDlg.textOneCell": "Move but don't size with cells", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "On tick marks", "SSE.Views.ChartSettingsDlg.textOut": "Out", "SSE.Views.ChartSettingsDlg.textOuterTop": "Outer top", "SSE.Views.ChartSettingsDlg.textOverlay": "Overlay", "SSE.Views.ChartSettingsDlg.textReverse": "Values in reverse order", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Reverse order", "SSE.Views.ChartSettingsDlg.textRight": "Right", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Right overlay", "SSE.Views.ChartSettingsDlg.textRotated": "Rotated", "SSE.Views.ChartSettingsDlg.textSameAll": "Same for all", "SSE.Views.ChartSettingsDlg.textSelectData": "Select data", "SSE.Views.ChartSettingsDlg.textSeparator": "Data labels separator", "SSE.Views.ChartSettingsDlg.textSeriesName": "Series name", "SSE.Views.ChartSettingsDlg.textShow": "Show", "SSE.Views.ChartSettingsDlg.textShowBorders": "Display chart borders", "SSE.Views.ChartSettingsDlg.textShowData": "Show data in hidden rows and columns", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Show empty cells as", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Show axis", "SSE.Views.ChartSettingsDlg.textShowValues": "Display chart values", "SSE.Views.ChartSettingsDlg.textSingle": "Single sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "Smooth", "SSE.Views.ChartSettingsDlg.textSnap": "Cell snapping", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparkline ranges", "SSE.Views.ChartSettingsDlg.textStraight": "Straight", "SSE.Views.ChartSettingsDlg.textStyle": "Style", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Thousands", "SSE.Views.ChartSettingsDlg.textTickOptions": "Tick Options", "SSE.Views.ChartSettingsDlg.textTitle": "Chart - Advanced settings", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Advanced settings", "SSE.Views.ChartSettingsDlg.textTop": "Top", "SSE.Views.ChartSettingsDlg.textTrillions": "Trillions", "SSE.Views.ChartSettingsDlg.textTwoCell": "Move and size with cells", "SSE.Views.ChartSettingsDlg.textType": "Type", "SSE.Views.ChartSettingsDlg.textTypeData": "Type & Data", "SSE.Views.ChartSettingsDlg.textUnits": "Display units", "SSE.Views.ChartSettingsDlg.textValue": "Value", "SSE.Views.ChartSettingsDlg.textVertAxis": "Vertical axis", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Secondary vertical axis", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X axis title", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y axis title", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "This field is required", "SSE.Views.ChartTypeDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartTypeDialog.textSecondary": "Secondary axis", "SSE.Views.ChartTypeDialog.textSeries": "Series", "SSE.Views.ChartTypeDialog.textStyle": "Style", "SSE.Views.ChartTypeDialog.textTitle": "Chart type", "SSE.Views.ChartTypeDialog.textType": "Type", "SSE.Views.CreatePivotDialog.textDataRange": "Source data range", "SSE.Views.CreatePivotDialog.textDestination": "Choose where to place the table", "SSE.Views.CreatePivotDialog.textExist": "Existing worksheet", "SSE.Views.CreatePivotDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreatePivotDialog.textNew": "New worksheet", "SSE.Views.CreatePivotDialog.textSelectData": "Select data", "SSE.Views.CreatePivotDialog.textTitle": "Create pivot table", "SSE.Views.CreatePivotDialog.txtEmpty": "This field is required", "SSE.Views.CreateSparklineDialog.textDataRange": "Source data range", "SSE.Views.CreateSparklineDialog.textDestination": "Choose, where to place the sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreateSparklineDialog.textSelectData": "Select data", "SSE.Views.CreateSparklineDialog.textTitle": "Create sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "This field is required", "SSE.Views.DataTab.capBtnGroup": "Group", "SSE.Views.DataTab.capBtnTextCustomSort": "Custom Sort", "SSE.Views.DataTab.capBtnTextDataValidation": "Data Validation", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Remove Duplicates", "SSE.Views.DataTab.capBtnTextToCol": "Text to Columns", "SSE.Views.DataTab.capBtnUngroup": "Ungroup", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Get Data", "SSE.Views.DataTab.mniFromFile": "From Local TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "From TXT/CSV Web Address", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Summary rows below detail", "SSE.Views.DataTab.textClear": "Clear outline", "SSE.Views.DataTab.textColumns": "Ungroup columns", "SSE.Views.DataTab.textGroupColumns": "Group columns", "SSE.Views.DataTab.textGroupRows": "Group rows", "SSE.Views.DataTab.textRightOf": "Summary columns to right of detail", "SSE.Views.DataTab.textRows": "Ungroup rows", "SSE.Views.DataTab.tipCustomSort": "Custom sort", "SSE.Views.DataTab.tipDataFromText": "Get data from file", "SSE.Views.DataTab.tipDataValidation": "Data validation", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGroup": "Group range of cells", "SSE.Views.DataTab.tipRemDuplicates": "Remove duplicate rows from a sheet", "SSE.Views.DataTab.tipToColumns": "Separate cell text into columns", "SSE.Views.DataTab.tipUngroup": "Ungroup range of cells", "SSE.Views.DataValidationDialog.errorFormula": "The value currently evaluates to an error. Do you want to continue?", "SSE.Views.DataValidationDialog.errorInvalid": "The value you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidDate": "The date you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidList": "The list source must be a delimited list, or a reference to single row or column.", "SSE.Views.DataValidationDialog.errorInvalidTime": "The time you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "The \"{1}\" field must be greater than or equal to the \"{0}\" field.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "You must enter a value in both field \"{0}\" and field \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "You must enter a value in field \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "A named range you specified cannot be found.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negative values cannot be used in conditions \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "The field \"{0}\" must be a numeric value, numeric expression, or refer to a cell containing a numeric value.", "SSE.Views.DataValidationDialog.strError": "Error alert", "SSE.Views.DataValidationDialog.strInput": "Input message", "SSE.Views.DataValidationDialog.strSettings": "Settings", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "Allow", "SSE.Views.DataValidationDialog.textApply": "Apply these changes to all other cells with the same settings", "SSE.Views.DataValidationDialog.textCellSelected": "When cell is selected, show this input message", "SSE.Views.DataValidationDialog.textCompare": "Compare to", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "End date", "SSE.Views.DataValidationDialog.textEndTime": "End time", "SSE.Views.DataValidationDialog.textError": "Error message", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Ignore blank", "SSE.Views.DataValidationDialog.textInput": "Input message", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Message", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Select data", "SSE.Views.DataValidationDialog.textShowDropDown": "Show drop-down list in cell", "SSE.Views.DataValidationDialog.textShowError": "Show error alert after invalid data is entered", "SSE.Views.DataValidationDialog.textShowInput": "Show input message when cell is selected", "SSE.Views.DataValidationDialog.textSource": "Source", "SSE.Views.DataValidationDialog.textStartDate": "Start date", "SSE.Views.DataValidationDialog.textStartTime": "Start time", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "Style", "SSE.Views.DataValidationDialog.textTitle": "Title", "SSE.Views.DataValidationDialog.textUserEnters": "When user enters invalid data, show this error alert", "SSE.Views.DataValidationDialog.txtAny": "Any value", "SSE.Views.DataValidationDialog.txtBetween": "between", "SSE.Views.DataValidationDialog.txtDate": "Date", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Elapsed time", "SSE.Views.DataValidationDialog.txtEndDate": "End date", "SSE.Views.DataValidationDialog.txtEndTime": "End time", "SSE.Views.DataValidationDialog.txtEqual": "equals", "SSE.Views.DataValidationDialog.txtGreaterThan": "greater than", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "greater than or equal to", "SSE.Views.DataValidationDialog.txtLength": "Length", "SSE.Views.DataValidationDialog.txtLessThan": "less than", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "less than or equal to", "SSE.Views.DataValidationDialog.txtList": "List", "SSE.Views.DataValidationDialog.txtNotBetween": "not between", "SSE.Views.DataValidationDialog.txtNotEqual": "does not equal", "SSE.Views.DataValidationDialog.txtOther": "Other", "SSE.Views.DataValidationDialog.txtStartDate": "Start date", "SSE.Views.DataValidationDialog.txtStartTime": "Start time", "SSE.Views.DataValidationDialog.txtTextLength": "Text length", "SSE.Views.DataValidationDialog.txtTime": "Time", "SSE.Views.DataValidationDialog.txtWhole": "Whole number", "SSE.Views.DigitalFilterDialog.capAnd": "And", "SSE.Views.DigitalFilterDialog.capCondition1": "equals", "SSE.Views.DigitalFilterDialog.capCondition10": "does not end with", "SSE.Views.DigitalFilterDialog.capCondition11": "contains", "SSE.Views.DigitalFilterDialog.capCondition12": "does not contain", "SSE.Views.DigitalFilterDialog.capCondition2": "does not equal", "SSE.Views.DigitalFilterDialog.capCondition3": "is greater than", "SSE.Views.DigitalFilterDialog.capCondition4": "is greater than or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "is less than", "SSE.Views.DigitalFilterDialog.capCondition6": "is less than or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "begins with", "SSE.Views.DigitalFilterDialog.capCondition8": "does not begin with", "SSE.Views.DigitalFilterDialog.capCondition9": "ends with", "SSE.Views.DigitalFilterDialog.capOr": "Or", "SSE.Views.DigitalFilterDialog.textNoFilter": "no filter", "SSE.Views.DigitalFilterDialog.textShowRows": "Show rows where", "SSE.Views.DigitalFilterDialog.textUse1": "Use ? to present any single character", "SSE.Views.DigitalFilterDialog.textUse2": "Use * to present any series of character", "SSE.Views.DigitalFilterDialog.txtTitle": "Custom filter", "SSE.Views.DocumentHolder.advancedEquationText": "Equation Settings", "SSE.Views.DocumentHolder.advancedImgText": "Image advanced settings", "SSE.Views.DocumentHolder.advancedShapeText": "Shape advanced settings", "SSE.Views.DocumentHolder.advancedSlicerText": "Slicer advanced settings", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "Align Bottom", "SSE.Views.DocumentHolder.bulletsText": "Bullets and Numbering", "SSE.Views.DocumentHolder.centerCellText": "Align Middle", "SSE.Views.DocumentHolder.chartDataText": "Select Chart Data", "SSE.Views.DocumentHolder.chartText": "Chart advanced settings", "SSE.Views.DocumentHolder.chartTypeText": "Change Chart Type", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "Column", "SSE.Views.DocumentHolder.deleteRowText": "Row", "SSE.Views.DocumentHolder.deleteTableText": "Table", "SSE.Views.DocumentHolder.direct270Text": "Rotate Text Up", "SSE.Views.DocumentHolder.direct90Text": "Rotate Text Down", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Text direction", "SSE.Views.DocumentHolder.editChartText": "Edit Data", "SSE.Views.DocumentHolder.editHyperlinkText": "Edit Hyperlink", "SSE.Views.DocumentHolder.insertColumnLeftText": "Column Left", "SSE.Views.DocumentHolder.insertColumnRightText": "Column Right", "SSE.Views.DocumentHolder.insertRowAboveText": "Row Above", "SSE.Views.DocumentHolder.insertRowBelowText": "Row Below", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Actual size", "SSE.Views.DocumentHolder.removeHyperlinkText": "Remove Hyperlink", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON>n", "SSE.Views.DocumentHolder.selectDataText": "Column Data", "SSE.Views.DocumentHolder.selectRowText": "Row", "SSE.Views.DocumentHolder.selectTableText": "Table", "SSE.Views.DocumentHolder.strDelete": "Remove Signature", "SSE.Views.DocumentHolder.strDetails": "Signature Details", "SSE.Views.DocumentHolder.strSetup": "Signature Setup", "SSE.Views.DocumentHolder.strSign": "Sign", "SSE.Views.DocumentHolder.textAlign": "Align", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Send to Background", "SSE.Views.DocumentHolder.textArrangeBackward": "Send Backward", "SSE.Views.DocumentHolder.textArrangeForward": "Bring Forward", "SSE.Views.DocumentHolder.textArrangeFront": "Bring to Foreground", "SSE.Views.DocumentHolder.textAverage": "Average", "SSE.Views.DocumentHolder.textBullets": "Bullets", "SSE.Views.DocumentHolder.textCount": "Count", "SSE.Views.DocumentHolder.textCrop": "Crop", "SSE.Views.DocumentHolder.textCropFill": "Fill", "SSE.Views.DocumentHolder.textCropFit": "Fit", "SSE.Views.DocumentHolder.textEditPoints": "Edit Points", "SSE.Views.DocumentHolder.textEntriesList": "Select from drop-down list", "SSE.Views.DocumentHolder.textFlipH": "Flip Horizontally", "SSE.Views.DocumentHolder.textFlipV": "Flip Vertically", "SSE.Views.DocumentHolder.textFreezePanes": "Freeze panes", "SSE.Views.DocumentHolder.textFromFile": "From File", "SSE.Views.DocumentHolder.textFromStorage": "From Storage", "SSE.Views.DocumentHolder.textFromUrl": "From URL", "SSE.Views.DocumentHolder.textListSettings": "List Settings", "SSE.Views.DocumentHolder.textMacro": "Assign <PERSON>", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "More functions", "SSE.Views.DocumentHolder.textMoreFormats": "More formats", "SSE.Views.DocumentHolder.textNone": "None", "SSE.Views.DocumentHolder.textNumbering": "Numbering", "SSE.Views.DocumentHolder.textReplace": "Replace image", "SSE.Views.DocumentHolder.textRotate": "Rotate", "SSE.Views.DocumentHolder.textRotate270": "Rotate 90° Counterclockwise", "SSE.Views.DocumentHolder.textRotate90": "Rotate 90° Clockwise", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Align Bottom", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Align Center", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Align Left", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Align Middle", "SSE.Views.DocumentHolder.textShapeAlignRight": "Align Right", "SSE.Views.DocumentHolder.textShapeAlignTop": "Align Top", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Sum", "SSE.Views.DocumentHolder.textUndo": "Undo", "SSE.Views.DocumentHolder.textUnFreezePanes": "Unfreeze panes", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Arrow bullets", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Checkmark bullets", "SSE.Views.DocumentHolder.tipMarkersDash": "Dash bullets", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Filled rhombus bullets", "SSE.Views.DocumentHolder.tipMarkersFRound": "Filled round bullets", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Filled square bullets", "SSE.Views.DocumentHolder.tipMarkersHRound": "Hollow round bullets", "SSE.Views.DocumentHolder.tipMarkersStar": "Star bullets", "SSE.Views.DocumentHolder.topCellText": "Align Top", "SSE.Views.DocumentHolder.txtAccounting": "Accounting", "SSE.Views.DocumentHolder.txtAddComment": "Add comment", "SSE.Views.DocumentHolder.txtAddNamedRange": "Define name", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Ascending", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Auto fit column width", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Auto fit row height", "SSE.Views.DocumentHolder.txtClear": "Clear", "SSE.Views.DocumentHolder.txtClearAll": "All", "SSE.Views.DocumentHolder.txtClearComments": "Comments", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Hyperlinks", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Clear selected sparkline groups", "SSE.Views.DocumentHolder.txtClearSparklines": "Clear selected sparklines", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtColumn": "Entire column", "SSE.Views.DocumentHolder.txtColumnWidth": "Set column width", "SSE.Views.DocumentHolder.txtCondFormat": "Conditional formatting", "SSE.Views.DocumentHolder.txtCopy": "Copy", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Custom column width", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Custom row height", "SSE.Views.DocumentHolder.txtCustomSort": "Custom sort", "SSE.Views.DocumentHolder.txtCut": "Cut", "SSE.Views.DocumentHolder.txtDate": "Date", "SSE.Views.DocumentHolder.txtDelete": "Delete", "SSE.Views.DocumentHolder.txtDescending": "Descending", "SSE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "SSE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "SSE.Views.DocumentHolder.txtEditComment": "Edit comment", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filter by cell's color", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filter by font color", "SSE.Views.DocumentHolder.txtFilterValue": "Filter by selected cell's value", "SSE.Views.DocumentHolder.txtFormula": "Insert function", "SSE.Views.DocumentHolder.txtFraction": "Fraction", "SSE.Views.DocumentHolder.txtGeneral": "General", "SSE.Views.DocumentHolder.txtGetLink": "Get link to this range", "SSE.Views.DocumentHolder.txtGroup": "Group", "SSE.Views.DocumentHolder.txtHide": "<PERSON>de", "SSE.Views.DocumentHolder.txtInsert": "Insert", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hyperlink", "SSE.Views.DocumentHolder.txtNumber": "Number", "SSE.Views.DocumentHolder.txtNumFormat": "Number Format", "SSE.Views.DocumentHolder.txtPaste": "Paste", "SSE.Views.DocumentHolder.txtPercentage": "Percentage", "SSE.Views.DocumentHolder.txtReapply": "Reapply", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "Entire row", "SSE.Views.DocumentHolder.txtRowHeight": "Set row height", "SSE.Views.DocumentHolder.txtScientific": "Scientific", "SSE.Views.DocumentHolder.txtSelect": "Select", "SSE.Views.DocumentHolder.txtShiftDown": "Shift cells down", "SSE.Views.DocumentHolder.txtShiftLeft": "Shift cells left", "SSE.Views.DocumentHolder.txtShiftRight": "Shift cells right", "SSE.Views.DocumentHolder.txtShiftUp": "Shift cells up", "SSE.Views.DocumentHolder.txtShow": "Show", "SSE.Views.DocumentHolder.txtShowComment": "Show comment", "SSE.Views.DocumentHolder.txtSort": "Sort", "SSE.Views.DocumentHolder.txtSortCellColor": "Selected cell color on top", "SSE.Views.DocumentHolder.txtSortFontColor": "Selected font color on top", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Paragraph advanced settings", "SSE.Views.DocumentHolder.txtTime": "Time", "SSE.Views.DocumentHolder.txtUngroup": "Ungroup", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Vertical alignment", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotals", "SSE.Views.FieldSettingsDialog.textReport": "Report form", "SSE.Views.FieldSettingsDialog.textTitle": "Field settings", "SSE.Views.FieldSettingsDialog.txtAverage": "Average", "SSE.Views.FieldSettingsDialog.txtBlank": "Insert blank rows after each item", "SSE.Views.FieldSettingsDialog.txtBottom": "Show at bottom of group", "SSE.Views.FieldSettingsDialog.txtCompact": "Compact", "SSE.Views.FieldSettingsDialog.txtCount": "Count", "SSE.Views.FieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.FieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.FieldSettingsDialog.txtEmpty": "Show items with no data", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Outline", "SSE.Views.FieldSettingsDialog.txtProduct": "Product", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repeat items labels at each row", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Show subtotals", "SSE.Views.FieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Sum", "SSE.Views.FieldSettingsDialog.txtSummarize": "Functions for subtotals", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Show at top of group", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Open file location", "SSE.Views.FileMenu.btnCloseMenuCaption": "Close Menu", "SSE.Views.FileMenu.btnCreateNewCaption": "Create New", "SSE.Views.FileMenu.btnDownloadCaption": "Download as", "SSE.Views.FileMenu.btnExitCaption": "Close", "SSE.Views.FileMenu.btnFileOpenCaption": "Open", "SSE.Views.FileMenu.btnHelpCaption": "Help", "SSE.Views.FileMenu.btnHistoryCaption": "Version History", "SSE.Views.FileMenu.btnInfoCaption": "Spreadsheet Info", "SSE.Views.FileMenu.btnPrintCaption": "Print", "SSE.Views.FileMenu.btnProtectCaption": "Protect", "SSE.Views.FileMenu.btnRecentFilesCaption": "Open Recent", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "Back to Spreadsheet", "SSE.Views.FileMenu.btnRightsCaption": "Access Rights", "SSE.Views.FileMenu.btnSaveAsCaption": "Save as", "SSE.Views.FileMenu.btnSaveCaption": "Save", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy as", "SSE.Views.FileMenu.btnSettingsCaption": "Advanced Settings", "SSE.Views.FileMenu.btnToEditCaption": "Edit Spreadsheet", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Spreadsheet", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create New", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Apply", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Add Author", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add Text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Author", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Change access rights", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comment", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last Modified By", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last Modified", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Location", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persons who have rights", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Title", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Change access rights", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Persons who have rights", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Apply", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Co-editing Mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use 1904 date system", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Decimal separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Dictionary language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Fast", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formula Language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignore words with numbers", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Show the Paste Options button when the content is pasted", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 reference style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regional Settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Example: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Show comments in sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Show changes from other users", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Show resolved comments", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Interface theme", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Thousands separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unit of Measurement", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Use separators based on regional settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Default Zoom Value", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Every 10 Minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Every 30 Minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Every 5 Minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Every Hour", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Autorecover", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Autosave", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Disabled", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Saving intermediate versions", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Every Minute", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Reference Style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "AutoCorrect options...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Default cache mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculating", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Collaboration", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Czech", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "German", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editing and saving", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Greek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "English", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Real-time co-editing. All changes are saved automatically", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finnish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "French", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Inch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "as OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Native", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Dutch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Proofing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Point", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portuguese (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portuguese (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romanian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Russian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Enable All", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Enable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Disable All", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Disable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Swedish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Turkish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukrainian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Show Notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Disable all macros with a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "as Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Workspace", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Chinese", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Warning", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "With password", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protect Spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "With signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edit spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This spreadsheet has been protected by password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "View signatures", "SSE.Views.FormatRulesEditDlg.fillColor": "Fill color", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 color scale", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 color scale", "SSE.Views.FormatRulesEditDlg.textAllBorders": "All borders", "SSE.Views.FormatRulesEditDlg.textAppearance": "Bar appearance", "SSE.Views.FormatRulesEditDlg.textApply": "Apply to range", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatic", "SSE.Views.FormatRulesEditDlg.textAxis": "Axis", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Bar direction", "SSE.Views.FormatRulesEditDlg.textBold": "Bold", "SSE.Views.FormatRulesEditDlg.textBorder": "Border", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Borders color", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Border style", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bottom borders", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Cannot add the conditional formatting.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Cell midpoint", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Inside vertical borders", "SSE.Views.FormatRulesEditDlg.textClear": "Clear", "SSE.Views.FormatRulesEditDlg.textColor": "Text color", "SSE.Views.FormatRulesEditDlg.textContext": "Context", "SSE.Views.FormatRulesEditDlg.textCustom": "Custom", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Diagonal down border", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diagonal up border", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Enter a valid formula.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "The formula you entered does not evaluate to a number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Enter a value.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "The value you entered is not a valid number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "The value for the {0} must be greater than the value for the {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Enter a number between {0} and {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Fill", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "when {0} {1} and", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "when {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "when value is", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "One or more icon data ranges overlap.<br>Adjust icon data range values so that the ranges do not overlap.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Icon style", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Inside borders", "SSE.Views.FormatRulesEditDlg.textInvalid": "Invalid data range.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.FormatRulesEditDlg.textItalic": "Italic", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Left to right", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Left borders", "SSE.Views.FormatRulesEditDlg.textLongBar": "longest bar", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Inside horizontal borders", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Midpoint", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minpoint", "SSE.Views.FormatRulesEditDlg.textNegative": "Negative", "SSE.Views.FormatRulesEditDlg.textNewColor": "Add new custom color", "SSE.Views.FormatRulesEditDlg.textNoBorders": "No borders", "SSE.Views.FormatRulesEditDlg.textNone": "None", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "One or more of the specified values is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "The specified {0} value is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "One or more of the specified values is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "The specified {0} value is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Outside borders", "SSE.Views.FormatRulesEditDlg.textPercent": "Percent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentile", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "Positive", "SSE.Views.FormatRulesEditDlg.textPresets": "Presets", "SSE.Views.FormatRulesEditDlg.textPreview": "Preview", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "You cannot use relative references in conditional formatting criteria for color scales, data bars, and icon sets.", "SSE.Views.FormatRulesEditDlg.textReverse": "Reverse icons order", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Right to left", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Right borders", "SSE.Views.FormatRulesEditDlg.textRule": "Rule", "SSE.Views.FormatRulesEditDlg.textSameAs": "Same as positive", "SSE.Views.FormatRulesEditDlg.textSelectData": "Select data", "SSE.Views.FormatRulesEditDlg.textShortBar": "shortest bar", "SSE.Views.FormatRulesEditDlg.textShowBar": "Show bar only", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Show icon only", "SSE.Views.FormatRulesEditDlg.textSingleRef": "This type of reference cannot be used in a conditional formatting formula.<br>Change the reference to a single cell, or use the reference with a worksheet function, such as =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solid", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Strikeout", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscript", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superscript", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Top borders", "SSE.Views.FormatRulesEditDlg.textUnderline": "Underline", "SSE.Views.FormatRulesEditDlg.tipBorders": "Borders", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Number format", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Accounting", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "This field is required", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fraction", "SSE.Views.FormatRulesEditDlg.txtGeneral": "General", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "No icon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Number", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percentage", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientific", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Time", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Edit formatting rule", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "New formatting rule", "SSE.Views.FormatRulesManagerDlg.guestText": "Guest", "SSE.Views.FormatRulesManagerDlg.lockText": "Locked", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev above average", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev below average", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev above average", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev below average", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev above average", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev below average", "SSE.Views.FormatRulesManagerDlg.textAbove": "Above average", "SSE.Views.FormatRulesManagerDlg.textApply": "Apply to", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Cell value begins with", "SSE.Views.FormatRulesManagerDlg.textBelow": "Below average", "SSE.Views.FormatRulesManagerDlg.textBetween": "is between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Cell value", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Graded color scale", "SSE.Views.FormatRulesManagerDlg.textContains": "Cell value contains", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cell contains a blank value", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Cell contains an error", "SSE.Views.FormatRulesManagerDlg.textDelete": "Delete", "SSE.Views.FormatRulesManagerDlg.textDown": "Move rule down", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicate values", "SSE.Views.FormatRulesManagerDlg.textEdit": "Edit", "SSE.Views.FormatRulesManagerDlg.textEnds": "Cell value ends with", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Equal to or above average", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Equal to or below average", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Icon set", "SSE.Views.FormatRulesManagerDlg.textNew": "New", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "is not between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Cell value does not contain", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cell does not contain a blank value", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cell does not contain an error", "SSE.Views.FormatRulesManagerDlg.textRules": "Rules", "SSE.Views.FormatRulesManagerDlg.textScope": "Show formatting rules for", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Select data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Current selection", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "This pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "This worksheet", "SSE.Views.FormatRulesManagerDlg.textThisTable": "This table", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unique values", "SSE.Views.FormatRulesManagerDlg.textUp": "Move rule up", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Conditional formatting", "SSE.Views.FormatSettingsDialog.textCategory": "Category", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Linked to source", "SSE.Views.FormatSettingsDialog.textSeparator": "Use 1000 separator", "SSE.Views.FormatSettingsDialog.textSymbols": "Symbols", "SSE.Views.FormatSettingsDialog.textTitle": "Number format", "SSE.Views.FormatSettingsDialog.txtAccounting": "Accounting", "SSE.Views.FormatSettingsDialog.txtAs10": "As tenths (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "As hundredths (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "As sixteenths (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "As halves (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "As fourths (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "As eighths (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Custom", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Please enter the custom number format carefully. Spreadsheet Editor does not check custom formats for errors that may affect the xlsx file.", "SSE.Views.FormatSettingsDialog.txtDate": "Date", "SSE.Views.FormatSettingsDialog.txtFraction": "Fraction", "SSE.Views.FormatSettingsDialog.txtGeneral": "General", "SSE.Views.FormatSettingsDialog.txtNone": "None", "SSE.Views.FormatSettingsDialog.txtNumber": "Number", "SSE.Views.FormatSettingsDialog.txtPercentage": "Percentage", "SSE.Views.FormatSettingsDialog.txtSample": "Sample:", "SSE.Views.FormatSettingsDialog.txtScientific": "Scientific", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Time", "SSE.Views.FormatSettingsDialog.txtUpto1": "Up to one digit (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Up to two digits (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Up to three digits (131/135)", "SSE.Views.FormulaDialog.sDescription": "Description", "SSE.Views.FormulaDialog.textGroupDescription": "Select function group", "SSE.Views.FormulaDialog.textListDescription": "Select function", "SSE.Views.FormulaDialog.txtRecommended": "Recommended", "SSE.Views.FormulaDialog.txtSearch": "Search", "SSE.Views.FormulaDialog.txtTitle": "Insert function", "SSE.Views.FormulaTab.textAutomatic": "Automatic", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calculate current sheet", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calculate workbook", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Calculate", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calculate the entire workbook", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "Additional", "SSE.Views.FormulaTab.txtAutosum": "Autosum", "SSE.Views.FormulaTab.txtAutosumTip": "Summation", "SSE.Views.FormulaTab.txtCalculation": "Calculation", "SSE.Views.FormulaTab.txtFormula": "Function", "SSE.Views.FormulaTab.txtFormulaTip": "Insert function", "SSE.Views.FormulaTab.txtMore": "More functions", "SSE.Views.FormulaTab.txtRecent": "Recently Used", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "any", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Function", "SSE.Views.FormulaWizard.textFunctionRes": "Function result", "SSE.Views.FormulaWizard.textHelp": "Help on this function", "SSE.Views.FormulaWizard.textLogical": "logical", "SSE.Views.FormulaWizard.textNoArgs": "This function has no arguments", "SSE.Views.FormulaWizard.textNumber": "number", "SSE.Views.FormulaWizard.textRef": "reference", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Function arguments", "SSE.Views.FormulaWizard.textValue": "Formula result", "SSE.Views.HeaderFooterDialog.textAlign": "Align with page margins", "SSE.Views.HeaderFooterDialog.textAll": "All pages", "SSE.Views.HeaderFooterDialog.textBold": "Bold", "SSE.Views.HeaderFooterDialog.textCenter": "Center", "SSE.Views.HeaderFooterDialog.textColor": "Text color", "SSE.Views.HeaderFooterDialog.textDate": "Date", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Different first page", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Different odd and even pages", "SSE.Views.HeaderFooterDialog.textEven": "Even page", "SSE.Views.HeaderFooterDialog.textFileName": "File name", "SSE.Views.HeaderFooterDialog.textFirst": "First page", "SSE.Views.HeaderFooterDialog.textFooter": "Footer", "SSE.Views.HeaderFooterDialog.textHeader": "Header", "SSE.Views.HeaderFooterDialog.textInsert": "Insert", "SSE.Views.HeaderFooterDialog.textItalic": "Italic", "SSE.Views.HeaderFooterDialog.textLeft": "Left", "SSE.Views.HeaderFooterDialog.textMaxError": "The text string you entered is too long. Reduce the number of characters used.", "SSE.Views.HeaderFooterDialog.textNewColor": "Add new custom color", "SSE.Views.HeaderFooterDialog.textOdd": "Odd page", "SSE.Views.HeaderFooterDialog.textPageCount": "Page count", "SSE.Views.HeaderFooterDialog.textPageNum": "Page number", "SSE.Views.HeaderFooterDialog.textPresets": "Presets", "SSE.Views.HeaderFooterDialog.textRight": "Right", "SSE.Views.HeaderFooterDialog.textScale": "Scale with document", "SSE.Views.HeaderFooterDialog.textSheet": "Sheet name", "SSE.Views.HeaderFooterDialog.textStrikeout": "Strikethrough", "SSE.Views.HeaderFooterDialog.textSubscript": "Subscript", "SSE.Views.HeaderFooterDialog.textSuperscript": "Superscript", "SSE.Views.HeaderFooterDialog.textTime": "Time", "SSE.Views.HeaderFooterDialog.textTitle": "Header/Footer settings", "SSE.Views.HeaderFooterDialog.textUnderline": "Underline", "SSE.Views.HeaderFooterDialog.tipFontName": "Font", "SSE.Views.HeaderFooterDialog.tipFontSize": "Font size", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Display", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Link to", "SSE.Views.HyperlinkSettingsDialog.strRange": "Range", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Sheet", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copy", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Selected range", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Enter caption here", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Enter link here", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Enter tooltip here", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "External link", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Get link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Internal data range", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.HyperlinkSettingsDialog.textNames": "Defined names", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Select data", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Sheets", "SSE.Views.HyperlinkSettingsDialog.textTipText": "ScreenTip text", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink settings", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "This field is required", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "SSE.Views.ImageSettings.textAdvanced": "Show advanced settings", "SSE.Views.ImageSettings.textCrop": "Crop", "SSE.Views.ImageSettings.textCropFill": "Fill", "SSE.Views.ImageSettings.textCropFit": "Fit", "SSE.Views.ImageSettings.textCropToShape": "Crop to shape", "SSE.Views.ImageSettings.textEdit": "Edit", "SSE.Views.ImageSettings.textEditObject": "Edit Object", "SSE.Views.ImageSettings.textFlip": "Flip", "SSE.Views.ImageSettings.textFromFile": "From File", "SSE.Views.ImageSettings.textFromStorage": "From Storage", "SSE.Views.ImageSettings.textFromUrl": "From URL", "SSE.Views.ImageSettings.textHeight": "Height", "SSE.Views.ImageSettings.textHint270": "Rotate 90° Counterclockwise", "SSE.Views.ImageSettings.textHint90": "Rotate 90° Clockwise", "SSE.Views.ImageSettings.textHintFlipH": "Flip Horizontally", "SSE.Views.ImageSettings.textHintFlipV": "Flip Vertically", "SSE.Views.ImageSettings.textInsert": "Replace Image", "SSE.Views.ImageSettings.textKeepRatio": "Constant proportions", "SSE.Views.ImageSettings.textOriginalSize": "Actual Size", "SSE.Views.ImageSettings.textRecentlyUsed": "Recently Used", "SSE.Views.ImageSettings.textRotate90": "Rotate 90°", "SSE.Views.ImageSettings.textRotation": "Rotation", "SSE.Views.ImageSettings.textSize": "Size", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Description", "SSE.Views.ImageSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart, or table.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontally", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ImageSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ImageSettingsAdvanced.textTitle": "Image - Advanced settings", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ImageSettingsAdvanced.textVertically": "Vertically", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import Data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.tipAbout": "About", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Comments", "SSE.Views.LeftMenu.tipFile": "File", "SSE.Views.LeftMenu.tipPlugins": "Plugins", "SSE.Views.LeftMenu.tipSearch": "Search", "SSE.Views.LeftMenu.tipSpellcheck": "Spell checking", "SSE.Views.LeftMenu.tipSupport": "Feedback & Support", "SSE.Views.LeftMenu.txtDeveloper": "DEVELOPER MODE", "SSE.Views.LeftMenu.txtEditor": "Spreadsheet Editor", "SSE.Views.LeftMenu.txtLimit": "Limit Access", "SSE.Views.LeftMenu.txtTrial": "TRIAL MODE", "SSE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "SSE.Views.MacroDialog.textMacro": "Macro name", "SSE.Views.MacroDialog.textTitle": "Assign macro", "SSE.Views.MainSettingsPrint.okButtonText": "Save", "SSE.Views.MainSettingsPrint.strBottom": "Bottom", "SSE.Views.MainSettingsPrint.strLandscape": "Landscape", "SSE.Views.MainSettingsPrint.strLeft": "Left", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Portrait", "SSE.Views.MainSettingsPrint.strPrint": "Print", "SSE.Views.MainSettingsPrint.strPrintTitles": "Print Titles", "SSE.Views.MainSettingsPrint.strRight": "Right", "SSE.Views.MainSettingsPrint.strTop": "Top", "SSE.Views.MainSettingsPrint.textActualSize": "Actual Size", "SSE.Views.MainSettingsPrint.textCustom": "Custom", "SSE.Views.MainSettingsPrint.textCustomOptions": "Custom Options", "SSE.Views.MainSettingsPrint.textFitCols": "Fit All Columns on One Page", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON>t Sheet on One Page", "SSE.Views.MainSettingsPrint.textFitRows": "Fit All Rows on One Page", "SSE.Views.MainSettingsPrint.textPageOrientation": "Page Orientation", "SSE.Views.MainSettingsPrint.textPageScaling": "Sc<PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Print Gridlines", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Print Row and Column Headings", "SSE.Views.MainSettingsPrint.textRepeat": "Repeat...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repeat columns at left", "SSE.Views.MainSettingsPrint.textRepeatTop": "Repeat rows at top", "SSE.Views.MainSettingsPrint.textSettings": "Settings for", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defined name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Data range", "SSE.Views.NamedRangeEditDlg.textExistName": "ERROR! Range with such a name already exists", "SSE.Views.NamedRangeEditDlg.textInvalidName": "The name must begin with a letter or an underscore and must not contain invalid characters.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERROR! Invalid cell range", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERROR! This element is being edited by another user.", "SSE.Views.NamedRangeEditDlg.textName": "Name", "SSE.Views.NamedRangeEditDlg.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Select data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "This field is required", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Edit name", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "New name", "SSE.Views.NamedRangePasteDlg.textNames": "Named ranges", "SSE.Views.NamedRangePasteDlg.txtTitle": "Paste name", "SSE.Views.NameManagerDlg.closeButtonText": "Close", "SSE.Views.NameManagerDlg.guestText": "Guest", "SSE.Views.NameManagerDlg.lockText": "Locked", "SSE.Views.NameManagerDlg.textDataRange": "Data range", "SSE.Views.NameManagerDlg.textDelete": "Delete", "SSE.Views.NameManagerDlg.textEdit": "Edit", "SSE.Views.NameManagerDlg.textEmpty": "No named ranges have been created yet.<br>Create at least one named range and it will appear in this field.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "All", "SSE.Views.NameManagerDlg.textFilterDefNames": "Defined names", "SSE.Views.NameManagerDlg.textFilterSheet": "Names scoped to sheet", "SSE.Views.NameManagerDlg.textFilterTableNames": "Table names", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Names scoped to workbook", "SSE.Views.NameManagerDlg.textNew": "New", "SSE.Views.NameManagerDlg.textnoNames": "No named ranges matching your filter could be found.", "SSE.Views.NameManagerDlg.textRanges": "Named ranges", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.NameManagerDlg.txtTitle": "Name manager", "SSE.Views.NameManagerDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Bottom", "SSE.Views.PageMarginsDialog.textLeft": "Left", "SSE.Views.PageMarginsDialog.textRight": "Right", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Top", "SSE.Views.ParagraphSettings.strLineHeight": "Line Spacing", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Paragraph Spacing", "SSE.Views.ParagraphSettings.strSpacingAfter": "After", "SSE.Views.ParagraphSettings.strSpacingBefore": "Before", "SSE.Views.ParagraphSettings.textAdvanced": "Show advanced settings", "SSE.Views.ParagraphSettings.textAt": "At", "SSE.Views.ParagraphSettings.textAtLeast": "At least", "SSE.Views.ParagraphSettings.textAuto": "Multiple", "SSE.Views.ParagraphSettings.textExact": "Exactly", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "The specified tabs will appear in this field", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "All caps", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Double strikethrough", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Indents", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Right", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "After", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Before", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "By", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indents & Spacing", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Small caps", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Strikethrough", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tabs", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Alignment", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Character spacing", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Default tab", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Effects", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exactly", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "First line", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(none)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Remove", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remove all", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Specify", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Left", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tab position", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Right", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraph - Advanced settings", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "equals", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "does not end with", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "contains", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "does not contain", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "between", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "not between", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "does not equal", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "is greater than", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "is greater than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "is less than", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "is less than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "begins with", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "does not begin with", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "ends with", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Show items for which the label:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Show items for which:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Use ? to present any single character", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Use * to present any series of character", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "and", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Label filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Value filter", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "By", "SSE.Views.PivotGroupDialog.textDays": "Days", "SSE.Views.PivotGroupDialog.textEnd": "Ending at", "SSE.Views.PivotGroupDialog.textError": "This field must be a numeric value", "SSE.Views.PivotGroupDialog.textGreaterError": "The end number must be greater than the start number", "SSE.Views.PivotGroupDialog.textHour": "Hours", "SSE.Views.PivotGroupDialog.textMin": "Minutes", "SSE.Views.PivotGroupDialog.textMonth": "Months", "SSE.Views.PivotGroupDialog.textNumDays": "Number of days", "SSE.Views.PivotGroupDialog.textQuart": "Quarters", "SSE.Views.PivotGroupDialog.textSec": "Seconds", "SSE.Views.PivotGroupDialog.textStart": "Starting at", "SSE.Views.PivotGroupDialog.textYear": "Years", "SSE.Views.PivotGroupDialog.txtTitle": "Grouping", "SSE.Views.PivotSettings.textAdvanced": "Show advanced settings", "SSE.Views.PivotSettings.textColumns": "Columns", "SSE.Views.PivotSettings.textFields": "Select fields", "SSE.Views.PivotSettings.textFilters": "Filters", "SSE.Views.PivotSettings.textRows": "Rows", "SSE.Views.PivotSettings.textValues": "Values", "SSE.Views.PivotSettings.txtAddColumn": "Add to columns", "SSE.Views.PivotSettings.txtAddFilter": "Add to filters", "SSE.Views.PivotSettings.txtAddRow": "Add to Rows", "SSE.Views.PivotSettings.txtAddValues": "Add to Values", "SSE.Views.PivotSettings.txtFieldSettings": "Field settings", "SSE.Views.PivotSettings.txtMoveBegin": "Move to beginning", "SSE.Views.PivotSettings.txtMoveColumn": "Move to columns", "SSE.Views.PivotSettings.txtMoveDown": "Move down", "SSE.Views.PivotSettings.txtMoveEnd": "Move to end", "SSE.Views.PivotSettings.txtMoveFilter": "Move to filters", "SSE.Views.PivotSettings.txtMoveRow": "Move to rows", "SSE.Views.PivotSettings.txtMoveUp": "Move up", "SSE.Views.PivotSettings.txtMoveValues": "Move to values", "SSE.Views.PivotSettings.txtRemove": "Remove field", "SSE.Views.PivotSettingsAdvanced.strLayout": "Name and layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Description", "SSE.Views.PivotSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart or table.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Title", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Autofit column widths on update", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Data range", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Data source", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Display fields in report filter area", "SSE.Views.PivotSettingsAdvanced.textDown": "Down, then over", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Grand totals", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Field headers", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PivotSettingsAdvanced.textOver": "Over, then down", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Select data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Show for columns", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Show field headers for rows and columns", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Show for rows", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot Table - Advanced settings", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Report filter fields per column", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Report filter fields per row", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.PivotSettingsAdvanced.txtName": "Name", "SSE.Views.PivotTable.capBlankRows": "Blank Rows", "SSE.Views.PivotTable.capGrandTotals": "Grand Totals", "SSE.Views.PivotTable.capLayout": "Report Layout", "SSE.Views.PivotTable.capSubtotals": "Subtotals", "SSE.Views.PivotTable.mniBottomSubtotals": "Show all subtotals at bottom of group", "SSE.Views.PivotTable.mniInsertBlankLine": "Insert blank line after each item", "SSE.Views.PivotTable.mniLayoutCompact": "Show in compact form", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Don't repeat all item labels", "SSE.Views.PivotTable.mniLayoutOutline": "Show in outline form", "SSE.Views.PivotTable.mniLayoutRepeat": "Repeat all item labels", "SSE.Views.PivotTable.mniLayoutTabular": "Show in tabular form", "SSE.Views.PivotTable.mniNoSubtotals": "Don't show subtotals", "SSE.Views.PivotTable.mniOffTotals": "Off for rows and columns", "SSE.Views.PivotTable.mniOnColumnsTotals": "On for columns only", "SSE.Views.PivotTable.mniOnRowsTotals": "On for rows only", "SSE.Views.PivotTable.mniOnTotals": "On for rows and columns", "SSE.Views.PivotTable.mniRemoveBlankLine": "Remove blank line after each item", "SSE.Views.PivotTable.mniTopSubtotals": "Show all subtotals at top of group", "SSE.Views.PivotTable.textColBanded": "Banded Columns", "SSE.Views.PivotTable.textColHeader": "Column Headers", "SSE.Views.PivotTable.textRowBanded": "Banded Rows", "SSE.Views.PivotTable.textRowHeader": "Row Headers", "SSE.Views.PivotTable.tipCreatePivot": "Insert Pivot Table", "SSE.Views.PivotTable.tipGrandTotals": "Show or hide grand totals", "SSE.Views.PivotTable.tipRefresh": "Update the information from data source", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "Select entire pivot table", "SSE.Views.PivotTable.tipSubtotals": "Show or hide subtotals", "SSE.Views.PivotTable.txtCreate": "Insert Table", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Custom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Dark", "SSE.Views.PivotTable.txtGroupPivot_Light": "Light", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "Pivot Table", "SSE.Views.PivotTable.txtRefresh": "Refresh", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "Select", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Save & Download", "SSE.Views.PrintSettings.btnPrint": "Save & Print", "SSE.Views.PrintSettings.strBottom": "Bottom", "SSE.Views.PrintSettings.strLandscape": "Landscape", "SSE.Views.PrintSettings.strLeft": "Left", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Portrait", "SSE.Views.PrintSettings.strPrint": "Print", "SSE.Views.PrintSettings.strPrintTitles": "Print titles", "SSE.Views.PrintSettings.strRight": "Right", "SSE.Views.PrintSettings.strShow": "Show", "SSE.Views.PrintSettings.strTop": "Top", "SSE.Views.PrintSettings.textActualSize": "Actual size", "SSE.Views.PrintSettings.textAllSheets": "All sheets", "SSE.Views.PrintSettings.textCurrentSheet": "Current sheet", "SSE.Views.PrintSettings.textCustom": "Custom", "SSE.Views.PrintSettings.textCustomOptions": "Custom options", "SSE.Views.PrintSettings.textFitCols": "Fit all columns on one page", "SSE.Views.PrintSettings.textFitPage": "Fit sheet on one page", "SSE.Views.PrintSettings.textFitRows": "Fit all rows on one page", "SSE.Views.PrintSettings.textHideDetails": "Hide details", "SSE.Views.PrintSettings.textIgnore": "Ignore print area", "SSE.Views.PrintSettings.textLayout": "Layout", "SSE.Views.PrintSettings.textPageOrientation": "Page orientation", "SSE.Views.PrintSettings.textPageScaling": "Sc<PERSON>", "SSE.Views.PrintSettings.textPageSize": "Page size", "SSE.Views.PrintSettings.textPrintGrid": "Print gridlines", "SSE.Views.PrintSettings.textPrintHeadings": "Print row and column headings", "SSE.Views.PrintSettings.textPrintRange": "Print range", "SSE.Views.PrintSettings.textRange": "Range", "SSE.Views.PrintSettings.textRepeat": "Repeat...", "SSE.Views.PrintSettings.textRepeatLeft": "Repeat columns at left", "SSE.Views.PrintSettings.textRepeatTop": "Repeat rows at top", "SSE.Views.PrintSettings.textSelection": "Selection", "SSE.Views.PrintSettings.textSettings": "Sheet settings", "SSE.Views.PrintSettings.textShowDetails": "Show details", "SSE.Views.PrintSettings.textShowGrid": "Show gridlines", "SSE.Views.PrintSettings.textShowHeadings": "Show rows and columns headings", "SSE.Views.PrintSettings.textTitle": "Print settings", "SSE.Views.PrintSettings.textTitlePDF": "PDF settings", "SSE.Views.PrintTitlesDialog.textFirstCol": "First column", "SSE.Views.PrintTitlesDialog.textFirstRow": "First row", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Frozen columns", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Frozen rows", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PrintTitlesDialog.textLeft": "Repeat columns at left", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Don't repeat", "SSE.Views.PrintTitlesDialog.textRepeat": "Repeat...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Select range", "SSE.Views.PrintTitlesDialog.textTitle": "Print titles", "SSE.Views.PrintTitlesDialog.textTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtActualSize": "Actual Size", "SSE.Views.PrintWithPreview.txtAllSheets": "All sheets", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Apply to all sheets", "SSE.Views.PrintWithPreview.txtBottom": "Bottom", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Current sheet", "SSE.Views.PrintWithPreview.txtCustom": "Custom", "SSE.Views.PrintWithPreview.txtCustomOptions": "Custom Options", "SSE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the table is empty", "SSE.Views.PrintWithPreview.txtFitCols": "Fit All Columns on One Page", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON>t Sheet on One Page", "SSE.Views.PrintWithPreview.txtFitRows": "Fit All Rows on One Page", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gridlines and headings", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "SSE.Views.PrintWithPreview.txtIgnore": "Ignore print area", "SSE.Views.PrintWithPreview.txtLandscape": "Landscape", "SSE.Views.PrintWithPreview.txtLeft": "Left", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "of {0}", "SSE.Views.PrintWithPreview.txtPage": "Page", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "SSE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "SSE.Views.PrintWithPreview.txtPageSize": "Page size", "SSE.Views.PrintWithPreview.txtPortrait": "Portrait", "SSE.Views.PrintWithPreview.txtPrint": "Print", "SSE.Views.PrintWithPreview.txtPrintGrid": "Print gridlines", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Print row and column headings", "SSE.Views.PrintWithPreview.txtPrintRange": "Print range", "SSE.Views.PrintWithPreview.txtPrintTitles": "Print titles", "SSE.Views.PrintWithPreview.txtRepeat": "Repeat...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repeat columns at left", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtRight": "Right", "SSE.Views.PrintWithPreview.txtSave": "Save", "SSE.Views.PrintWithPreview.txtScaling": "Sc<PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Selection", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Settings of sheet", "SSE.Views.PrintWithPreview.txtSheet": "Sheet: {0}", "SSE.Views.PrintWithPreview.txtTop": "Top", "SSE.Views.ProtectDialog.textExistName": "ERROR! Range with such a title already exists", "SSE.Views.ProtectDialog.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectDialog.textSelectData": "Select data", "SSE.Views.ProtectDialog.txtAllow": "Allow all users of this sheet to", "SSE.Views.ProtectDialog.txtAutofilter": "Use AutoFilter", "SSE.Views.ProtectDialog.txtDelCols": "Delete columns", "SSE.Views.ProtectDialog.txtDelRows": "Delete rows", "SSE.Views.ProtectDialog.txtEmpty": "This field is required", "SSE.Views.ProtectDialog.txtFormatCells": "Format cells", "SSE.Views.ProtectDialog.txtFormatCols": "Format columns", "SSE.Views.ProtectDialog.txtFormatRows": "Format rows", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Confirmation password is not identical", "SSE.Views.ProtectDialog.txtInsCols": "Insert columns", "SSE.Views.ProtectDialog.txtInsHyper": "Insert hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "Insert rows", "SSE.Views.ProtectDialog.txtObjs": "Edit objects", "SSE.Views.ProtectDialog.txtOptional": "optional", "SSE.Views.ProtectDialog.txtPassword": "Password", "SSE.Views.ProtectDialog.txtPivot": "Use PivotTable and PivotChart", "SSE.Views.ProtectDialog.txtProtect": "Protect", "SSE.Views.ProtectDialog.txtRange": "Range", "SSE.Views.ProtectDialog.txtRangeName": "Title", "SSE.Views.ProtectDialog.txtRepeat": "Repeat password", "SSE.Views.ProtectDialog.txtScen": "Edit scenarios", "SSE.Views.ProtectDialog.txtSelLocked": "Select locked cells", "SSE.Views.ProtectDialog.txtSelUnLocked": "Select unlocked cells", "SSE.Views.ProtectDialog.txtSheetDescription": "Prevent unwanted changes from others by limiting their ability to edit.", "SSE.Views.ProtectDialog.txtSheetTitle": "Protect sheet", "SSE.Views.ProtectDialog.txtSort": "Sort", "SSE.Views.ProtectDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "SSE.Views.ProtectDialog.txtWBDescription": "To prevent other users from viewing hidden worksheets, adding, moving, deleting, or hiding worksheets and renaming worksheets, you can protect the structure of your workbook with a password.", "SSE.Views.ProtectDialog.txtWBTitle": "Protect workbook structure", "SSE.Views.ProtectRangesDlg.guestText": "Guest", "SSE.Views.ProtectRangesDlg.lockText": "Locked", "SSE.Views.ProtectRangesDlg.textDelete": "Delete", "SSE.Views.ProtectRangesDlg.textEdit": "Edit", "SSE.Views.ProtectRangesDlg.textEmpty": "No ranges allowed for edit.", "SSE.Views.ProtectRangesDlg.textNew": "New", "SSE.Views.ProtectRangesDlg.textProtect": "Protect sheet", "SSE.Views.ProtectRangesDlg.textPwd": "Password", "SSE.Views.ProtectRangesDlg.textRange": "Range", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Ranges unlocked by a password when sheet is protected (this works only for locked cells)", "SSE.Views.ProtectRangesDlg.textTitle": "Title", "SSE.Views.ProtectRangesDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Edit range", "SSE.Views.ProtectRangesDlg.txtNewRange": "New range", "SSE.Views.ProtectRangesDlg.txtNo": "No", "SSE.Views.ProtectRangesDlg.txtTitle": "Allow users to edit ranges", "SSE.Views.ProtectRangesDlg.txtYes": "Yes", "SSE.Views.ProtectRangesDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Columns", "SSE.Views.RemoveDuplicatesDialog.textDescription": "To delete duplicate values, select one or more columns that contain duplicates.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "My data has headers", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Select all", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Remove duplicates", "SSE.Views.RightMenu.txtCellSettings": "Cell settings", "SSE.Views.RightMenu.txtChartSettings": "Chart settings", "SSE.Views.RightMenu.txtImageSettings": "Image settings", "SSE.Views.RightMenu.txtParagraphSettings": "Paragraph settings", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Table settings", "SSE.Views.RightMenu.txtSettings": "Common Settings", "SSE.Views.RightMenu.txtShapeSettings": "Shape settings", "SSE.Views.RightMenu.txtSignatureSettings": "Signature settings", "SSE.Views.RightMenu.txtSlicerSettings": "Slicer settings", "SSE.Views.RightMenu.txtSparklineSettings": "Sparkline settings", "SSE.Views.RightMenu.txtTableSettings": "Table settings", "SSE.Views.RightMenu.txtTextArtSettings": "Text Art settings", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "The entered value is incorrect.", "SSE.Views.ScaleDialog.textFewPages": "pages", "SSE.Views.ScaleDialog.textFitTo": "Fit to", "SSE.Views.ScaleDialog.textHeight": "Height", "SSE.Views.ScaleDialog.textManyPages": "pages", "SSE.Views.ScaleDialog.textOnePage": "page", "SSE.Views.ScaleDialog.textScaleTo": "Scale to", "SSE.Views.ScaleDialog.textTitle": "Scale settings", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "The maximum value for this field is {0}", "SSE.Views.SetValueDialog.txtMinText": "The minimum value for this field is {0}", "SSE.Views.ShapeSettings.strBackground": "Background color", "SSE.Views.ShapeSettings.strChange": "Change Autoshape", "SSE.Views.ShapeSettings.strColor": "Color", "SSE.Views.ShapeSettings.strFill": "Fill", "SSE.Views.ShapeSettings.strForeground": "Foreground color", "SSE.Views.ShapeSettings.strPattern": "Pattern", "SSE.Views.ShapeSettings.strShadow": "Show shadow", "SSE.Views.ShapeSettings.strSize": "Size", "SSE.Views.ShapeSettings.strStroke": "Line", "SSE.Views.ShapeSettings.strTransparency": "Opacity", "SSE.Views.ShapeSettings.strType": "Type", "SSE.Views.ShapeSettings.textAdvanced": "Show advanced settings", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Color Fill", "SSE.Views.ShapeSettings.textDirection": "Direction", "SSE.Views.ShapeSettings.textEmptyPattern": "No Pattern", "SSE.Views.ShapeSettings.textFlip": "Flip", "SSE.Views.ShapeSettings.textFromFile": "From File", "SSE.Views.ShapeSettings.textFromStorage": "From Storage", "SSE.Views.ShapeSettings.textFromUrl": "From URL", "SSE.Views.ShapeSettings.textGradient": "Gradient points", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.ShapeSettings.textHint270": "Rotate 90° Counterclockwise", "SSE.Views.ShapeSettings.textHint90": "Rotate 90° Clockwise", "SSE.Views.ShapeSettings.textHintFlipH": "Flip Horizontally", "SSE.Views.ShapeSettings.textHintFlipV": "Flip Vertically", "SSE.Views.ShapeSettings.textImageTexture": "Picture or Texture", "SSE.Views.ShapeSettings.textLinear": "Linear", "SSE.Views.ShapeSettings.textNoFill": "No Fill", "SSE.Views.ShapeSettings.textOriginalSize": "Original Size", "SSE.Views.ShapeSettings.textPatternFill": "Pattern", "SSE.Views.ShapeSettings.textPosition": "Position", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "SSE.Views.ShapeSettings.textRotate90": "Rotate 90°", "SSE.Views.ShapeSettings.textRotation": "Rotation", "SSE.Views.ShapeSettings.textSelectImage": "Select Picture", "SSE.Views.ShapeSettings.textSelectTexture": "Select", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "Style", "SSE.Views.ShapeSettings.textTexture": "From Texture", "SSE.Views.ShapeSettings.textTile": "Tile", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.ShapeSettings.txtBrownPaper": "Brown Paper", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.ShapeSettings.txtGrain": "Grain", "SSE.Views.ShapeSettings.txtGranite": "Granite", "SSE.Views.ShapeSettings.txtGreyPaper": "Gray Paper", "SSE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Leather", "SSE.Views.ShapeSettings.txtNoBorders": "No Line", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "<PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Columns", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Text padding", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Description", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart, or table.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Title", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Arrows", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Begin size", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Begin style", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Bottom", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Cap type", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Number of columns", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "End size", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "End style", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Flat", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Flipped", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Height", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontally", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Join type", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Constant proportions", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Left", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Line style", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Allow text to overflow shape", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Resize shape to fit text", "SSE.Views.ShapeSettingsAdvanced.textRight": "Right", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ShapeSettingsAdvanced.textRound": "Round", "SSE.Views.ShapeSettingsAdvanced.textSize": "Size", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Spacing between columns", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Square", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Text box", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Shape - Advanced settings", "SSE.Views.ShapeSettingsAdvanced.textTop": "Top", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Vertically", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Weights & Arrows", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Warning", "SSE.Views.SignatureSettings.strDelete": "Remove Signature", "SSE.Views.SignatureSettings.strDetails": "Signature Details", "SSE.Views.SignatureSettings.strInvalid": "Invalid signatures", "SSE.Views.SignatureSettings.strRequested": "Requested signatures", "SSE.Views.SignatureSettings.strSetup": "Signature Setup", "SSE.Views.SignatureSettings.strSign": "Sign", "SSE.Views.SignatureSettings.strSignature": "Signature", "SSE.Views.SignatureSettings.strSigner": "Signer", "SSE.Views.SignatureSettings.strValid": "Valid signatures", "SSE.Views.SignatureSettings.txtContinueEditing": "Edit anyway", "SSE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.SignatureSettings.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.SlicerAddDialog.textColumns": "Columns", "SSE.Views.SlicerAddDialog.txtTitle": "Insert slicers", "SSE.Views.SlicerSettings.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettings.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettings.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettings.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettings.strSorting": "Sorting and filtering", "SSE.Views.SlicerSettings.textAdvanced": "Show advanced settings", "SSE.Views.SlicerSettings.textAsc": "Ascending", "SSE.Views.SlicerSettings.textAZ": "A to Z", "SSE.Views.SlicerSettings.textButtons": "Buttons", "SSE.Views.SlicerSettings.textColumns": "Columns", "SSE.Views.SlicerSettings.textDesc": "Descending", "SSE.Views.SlicerSettings.textHeight": "Height", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Constant Proportions", "SSE.Views.SlicerSettings.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettings.textLock": "Disable resizing or moving", "SSE.Views.SlicerSettings.textNewOld": "newest to oldest", "SSE.Views.SlicerSettings.textOldNew": "oldest to newest", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "Size", "SSE.Views.SlicerSettings.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettings.textStyle": "Style", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Buttons", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Columns", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Height", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "References", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Display header", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettingsAdvanced.strSize": "Size", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sorting & Filtering", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Style", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Style & Size", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Description", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart or table.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Title", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Ascending", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A to Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Descending", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Name to use in formulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Header", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Constant proportions", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettingsAdvanced.textName": "Name", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "newest to oldest", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "oldest to newest", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.SlicerSettingsAdvanced.textSort": "Sort", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Source name", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - Advanced settings", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.SortDialog.errorEmpty": "All sort criteria must have a column or row specified.", "SSE.Views.SortDialog.errorMoreOneCol": "More than one column is selected.", "SSE.Views.SortDialog.errorMoreOneRow": "More than one row is selected.", "SSE.Views.SortDialog.errorNotOriginalCol": "The column you selected is not in the original selected range.", "SSE.Views.SortDialog.errorNotOriginalRow": "The row you selected is not in the original selected range.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 is being sorted by the same color more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 is being sorted by values more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.textAdd": "Add level", "SSE.Views.SortDialog.textAsc": "Ascending", "SSE.Views.SortDialog.textAuto": "Automatic", "SSE.Views.SortDialog.textAZ": "A to Z", "SSE.Views.SortDialog.textBelow": "Below", "SSE.Views.SortDialog.textCellColor": "Cell color", "SSE.Views.SortDialog.textColumn": "Column", "SSE.Views.SortDialog.textCopy": "Copy level", "SSE.Views.SortDialog.textDelete": "Delete level", "SSE.Views.SortDialog.textDesc": "Descending", "SSE.Views.SortDialog.textDown": "Move level down", "SSE.Views.SortDialog.textFontColor": "Font color", "SSE.Views.SortDialog.textLeft": "Left", "SSE.Views.SortDialog.textMoreCols": "(More columns...)", "SSE.Views.SortDialog.textMoreRows": "(More rows...)", "SSE.Views.SortDialog.textNone": "None", "SSE.Views.SortDialog.textOptions": "Options", "SSE.Views.SortDialog.textOrder": "Order", "SSE.Views.SortDialog.textRight": "Right", "SSE.Views.SortDialog.textRow": "Row", "SSE.Views.SortDialog.textSort": "Sort on", "SSE.Views.SortDialog.textSortBy": "Sort by", "SSE.Views.SortDialog.textThenBy": "Then by", "SSE.Views.SortDialog.textTop": "Top", "SSE.Views.SortDialog.textUp": "Move level up", "SSE.Views.SortDialog.textValues": "Values", "SSE.Views.SortDialog.textZA": "Z to A", "SSE.Views.SortDialog.txtInvalidRange": "Invalid cells range.", "SSE.Views.SortDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.textAsc": "Ascending (A to Z) by", "SSE.Views.SortFilterDialog.textDesc": "Descending (Z to A) by", "SSE.Views.SortFilterDialog.txtTitle": "Sort", "SSE.Views.SortOptionsDialog.textCase": "Case sensitive", "SSE.Views.SortOptionsDialog.textHeaders": "My data has headers", "SSE.Views.SortOptionsDialog.textLeftRight": "Sort left to right", "SSE.Views.SortOptionsDialog.textOrientation": "Orientation", "SSE.Views.SortOptionsDialog.textTitle": "Sort options", "SSE.Views.SortOptionsDialog.textTopBottom": "Sort top to bottom", "SSE.Views.SpecialPasteDialog.textAdd": "Add", "SSE.Views.SpecialPasteDialog.textAll": "All", "SSE.Views.SpecialPasteDialog.textBlanks": "Skip blanks", "SSE.Views.SpecialPasteDialog.textColWidth": "Column widths", "SSE.Views.SpecialPasteDialog.textComments": "Comments", "SSE.Views.SpecialPasteDialog.textDiv": "Divide", "SSE.Views.SpecialPasteDialog.textFFormat": "Formulas & formatting", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formulas & number formats", "SSE.Views.SpecialPasteDialog.textFormats": "Formats", "SSE.Views.SpecialPasteDialog.textFormulas": "Formulas", "SSE.Views.SpecialPasteDialog.textFWidth": "Formulas & column widths", "SSE.Views.SpecialPasteDialog.textMult": "Multiply", "SSE.Views.SpecialPasteDialog.textNone": "None", "SSE.Views.SpecialPasteDialog.textOperation": "Operation", "SSE.Views.SpecialPasteDialog.textPaste": "Paste", "SSE.Views.SpecialPasteDialog.textSub": "Subtract", "SSE.Views.SpecialPasteDialog.textTitle": "Paste special", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpose", "SSE.Views.SpecialPasteDialog.textValues": "Values", "SSE.Views.SpecialPasteDialog.textVFormat": "Values & Formatting", "SSE.Views.SpecialPasteDialog.textVNFormat": "Values & Number formats", "SSE.Views.SpecialPasteDialog.textWBorders": "All except borders", "SSE.Views.Spellcheck.noSuggestions": "No spelling suggestions", "SSE.Views.Spellcheck.textChange": "Change", "SSE.Views.Spellcheck.textChangeAll": "Change All", "SSE.Views.Spellcheck.textIgnore": "Ignore", "SSE.Views.Spellcheck.textIgnoreAll": "Ignore All", "SSE.Views.Spellcheck.txtAddToDictionary": "Add To Dictionary", "SSE.Views.Spellcheck.txtClosePanel": "Close spelling", "SSE.Views.Spellcheck.txtComplete": "Spellcheck has been completed", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Dictionary Language", "SSE.Views.Spellcheck.txtNextTip": "Go to the next word", "SSE.Views.Spellcheck.txtSpelling": "Spelling", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Copy to end)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Move to end)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Paste before sheet", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Move before sheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} of {1} records filtered", "SSE.Views.Statusbar.filteredText": "Filter mode", "SSE.Views.Statusbar.itemAverage": "Average", "SSE.Views.Statusbar.itemCopy": "Copy", "SSE.Views.Statusbar.itemCount": "Count", "SSE.Views.Statusbar.itemDelete": "Delete", "SSE.Views.Statusbar.itemHidden": "Hidden", "SSE.Views.Statusbar.itemHide": "<PERSON>de", "SSE.Views.Statusbar.itemInsert": "Insert", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMove": "Move", "SSE.Views.Statusbar.itemProtect": "Protect", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "Saving status", "SSE.Views.Statusbar.itemSum": "Sum", "SSE.Views.Statusbar.itemTabColor": "Tab Color", "SSE.Views.Statusbar.itemUnProtect": "Unprotect", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Worksheet with such a name already exists.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "A sheet name cannot contain the following characters: \\/*?[]: or the character ' as first or last character", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Sheet name", "SSE.Views.Statusbar.selectAllSheets": "Select All Sheets", "SSE.Views.Statusbar.sheetIndexText": "Sheet {0} of {1}", "SSE.Views.Statusbar.textAverage": "Average", "SSE.Views.Statusbar.textCount": "Count", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Add New Custom Color", "SSE.Views.Statusbar.textNoColor": "No Color", "SSE.Views.Statusbar.textSum": "Sum", "SSE.Views.Statusbar.tipAddTab": "Add worksheet", "SSE.Views.Statusbar.tipFirst": "Scroll to first sheet", "SSE.Views.Statusbar.tipLast": "Scroll to last sheet", "SSE.Views.Statusbar.tipListOfSheets": "List of Sheets", "SSE.Views.Statusbar.tipNext": "Scroll sheet list right", "SSE.Views.Statusbar.tipPrev": "Scroll sheet list left", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "Zoom in", "SSE.Views.Statusbar.tipZoomOut": "Zoom out", "SSE.Views.Statusbar.ungroupSheets": "Ungroup Sheets", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "The operation could not be done for the selected range of cells.<br>Select a uniform data range different from the existing one and try again.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operation could not be completed for the selected cell range.<br>Select a range so that the first table row was on the same row<br>and the resulting table overlapped the current one.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operation could not be completed for the selected cell range.<br>Select a range which does not include other tables.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Views.TableOptionsDialog.txtEmpty": "This field is required", "SSE.Views.TableOptionsDialog.txtFormat": "Create table", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ERROR! Invalid cells range", "SSE.Views.TableOptionsDialog.txtNote": "The headers must remain in the same row, and the resulting table range must overlap the original table range.", "SSE.Views.TableOptionsDialog.txtTitle": "Title", "SSE.Views.TableSettings.deleteColumnText": "Delete Column", "SSE.Views.TableSettings.deleteRowText": "Delete Row", "SSE.Views.TableSettings.deleteTableText": "Delete Table", "SSE.Views.TableSettings.insertColumnLeftText": "Insert Column Left", "SSE.Views.TableSettings.insertColumnRightText": "Insert Column Right", "SSE.Views.TableSettings.insertRowAboveText": "Insert Row Above", "SSE.Views.TableSettings.insertRowBelowText": "Insert Row Below", "SSE.Views.TableSettings.notcriticalErrorTitle": "Warning", "SSE.Views.TableSettings.selectColumnText": "Select Entire Column", "SSE.Views.TableSettings.selectDataText": "Select Column Data", "SSE.Views.TableSettings.selectRowText": "Select Row", "SSE.Views.TableSettings.selectTableText": "Select Table", "SSE.Views.TableSettings.textActions": "Table actions", "SSE.Views.TableSettings.textAdvanced": "Show advanced settings", "SSE.Views.TableSettings.textBanded": "Banded", "SSE.Views.TableSettings.textColumns": "Columns", "SSE.Views.TableSettings.textConvertRange": "Convert to range", "SSE.Views.TableSettings.textEdit": "Rows & Columns", "SSE.Views.TableSettings.textEmptyTemplate": "No templates", "SSE.Views.TableSettings.textExistName": "ERROR! A range with such a name already exists", "SSE.Views.TableSettings.textFilter": "Filter button", "SSE.Views.TableSettings.textFirst": "First", "SSE.Views.TableSettings.textHeader": "Header", "SSE.Views.TableSettings.textInvalidName": "ERROR! Invalid table name", "SSE.Views.TableSettings.textIsLocked": "This element is being edited by another user.", "SSE.Views.TableSettings.textLast": "Last", "SSE.Views.TableSettings.textLongOperation": "Long operation", "SSE.Views.TableSettings.textPivot": "Insert pivot table", "SSE.Views.TableSettings.textRemDuplicates": "Remove duplicates", "SSE.Views.TableSettings.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.TableSettings.textResize": "Resize table", "SSE.Views.TableSettings.textRows": "Rows", "SSE.Views.TableSettings.textSelectData": "Select Data", "SSE.Views.TableSettings.textSlicer": "Insert slicer", "SSE.Views.TableSettings.textTableName": "Table Name", "SSE.Views.TableSettings.textTemplate": "Select From Template", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.warnLongOperation": "The operation you are about to perform might take rather much time to complete.<br>Are you sure you want to continue?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Description", "SSE.Views.TableSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart or table.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Title", "SSE.Views.TableSettingsAdvanced.textTitle": "Table - Advanced settings", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "Custom", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "Dark", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "Light", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TextArtSettings.strBackground": "Background color", "SSE.Views.TextArtSettings.strColor": "Color", "SSE.Views.TextArtSettings.strFill": "Fill", "SSE.Views.TextArtSettings.strForeground": "Foreground color", "SSE.Views.TextArtSettings.strPattern": "Pattern", "SSE.Views.TextArtSettings.strSize": "Size", "SSE.Views.TextArtSettings.strStroke": "Line", "SSE.Views.TextArtSettings.strTransparency": "Opacity", "SSE.Views.TextArtSettings.strType": "Type", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Color Fill", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "No Pattern", "SSE.Views.TextArtSettings.textFromFile": "From File", "SSE.Views.TextArtSettings.textFromUrl": "From URL", "SSE.Views.TextArtSettings.textGradient": "Gradient points", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "No Fill", "SSE.Views.TextArtSettings.textPatternFill": "Pattern", "SSE.Views.TextArtSettings.textPosition": "Position", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "Select", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "Style", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "From Texture", "SSE.Views.TextArtSettings.textTile": "Tile", "SSE.Views.TextArtSettings.textTransform": "Transform", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granite", "SSE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "SSE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Leather", "SSE.Views.TextArtSettings.txtNoBorders": "No Line", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Add Comment", "SSE.Views.Toolbar.capBtnColorSchemas": "Color Scheme", "SSE.Views.Toolbar.capBtnComment": "Comment", "SSE.Views.Toolbar.capBtnInsHeader": "Header & Footer", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientation", "SSE.Views.Toolbar.capBtnPageSize": "Size", "SSE.Views.Toolbar.capBtnPrintArea": "Print Area", "SSE.Views.Toolbar.capBtnPrintTitles": "Print Titles", "SSE.Views.Toolbar.capBtnScale": "Scale To Fit", "SSE.Views.Toolbar.capImgAlign": "Align", "SSE.Views.Toolbar.capImgBackward": "Send Backward", "SSE.Views.Toolbar.capImgForward": "Bring Forward", "SSE.Views.Toolbar.capImgGroup": "Group", "SSE.Views.Toolbar.capInsertChart": "Chart", "SSE.Views.Toolbar.capInsertEquation": "Equation", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "SSE.Views.Toolbar.capInsertImage": "Image", "SSE.Views.Toolbar.capInsertShape": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "Table", "SSE.Views.Toolbar.capInsertText": "Text Box", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.mniImageFromFile": "Image from File", "SSE.Views.Toolbar.mniImageFromStorage": "Image from Storage", "SSE.Views.Toolbar.mniImageFromUrl": "Image from URL", "SSE.Views.Toolbar.textAddPrintArea": "Add to print area", "SSE.Views.Toolbar.textAlignBottom": "Align Bottom", "SSE.Views.Toolbar.textAlignCenter": "Align Center", "SSE.Views.Toolbar.textAlignJust": "Justified", "SSE.Views.Toolbar.textAlignLeft": "Align Left", "SSE.Views.Toolbar.textAlignMiddle": "Align Middle", "SSE.Views.Toolbar.textAlignRight": "Align Right", "SSE.Views.Toolbar.textAlignTop": "Align Top", "SSE.Views.Toolbar.textAllBorders": "All Borders", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatic", "SSE.Views.Toolbar.textBold": "Bold", "SSE.Views.Toolbar.textBordersColor": "Border color", "SSE.Views.Toolbar.textBordersStyle": "Border style", "SSE.Views.Toolbar.textBottom": "Bottom: ", "SSE.Views.Toolbar.textBottomBorders": "Bottom borders", "SSE.Views.Toolbar.textCenterBorders": "Inside vertical borders", "SSE.Views.Toolbar.textClearPrintArea": "Clear print area", "SSE.Views.Toolbar.textClearRule": "Clear rules", "SSE.Views.Toolbar.textClockwise": "Angle clockwise", "SSE.Views.Toolbar.textColorScales": "Color scales", "SSE.Views.Toolbar.textCounterCw": "<PERSON>le counterclockwise", "SSE.Views.Toolbar.textCustom": "Custom", "SSE.Views.Toolbar.textDataBars": "Data bars", "SSE.Views.Toolbar.textDelLeft": "Shift cells left", "SSE.Views.Toolbar.textDelUp": "Shift cells up", "SSE.Views.Toolbar.textDiagDownBorder": "Diagonal down border", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonal up border", "SSE.Views.Toolbar.textDone": "Done", "SSE.Views.Toolbar.textEditVA": "Edit Visible Area", "SSE.Views.Toolbar.textEntireCol": "Entire column", "SSE.Views.Toolbar.textEntireRow": "Entire row", "SSE.Views.Toolbar.textFewPages": "pages", "SSE.Views.Toolbar.textHeight": "Height", "SSE.Views.Toolbar.textHideVA": "Hide Visible Area", "SSE.Views.Toolbar.textHorizontal": "Horizontal text", "SSE.Views.Toolbar.textInsDown": "Shift cells down", "SSE.Views.Toolbar.textInsideBorders": "Inside borders", "SSE.Views.Toolbar.textInsRight": "Shift cells right", "SSE.Views.Toolbar.textItalic": "Italic", "SSE.Views.Toolbar.textItems": "Items", "SSE.Views.Toolbar.textLandscape": "Landscape", "SSE.Views.Toolbar.textLeft": "Left: ", "SSE.Views.Toolbar.textLeftBorders": "Left borders", "SSE.Views.Toolbar.textManageRule": "Manage rules", "SSE.Views.Toolbar.textManyPages": "pages", "SSE.Views.Toolbar.textMarginsLast": "Last Custom", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Wide", "SSE.Views.Toolbar.textMiddleBorders": "Inside horizontal borders", "SSE.Views.Toolbar.textMoreFormats": "More formats", "SSE.Views.Toolbar.textMorePages": "More pages", "SSE.Views.Toolbar.textNewColor": "Add new custom color", "SSE.Views.Toolbar.textNewRule": "New rule", "SSE.Views.Toolbar.textNoBorders": "No borders", "SSE.Views.Toolbar.textOnePage": "page", "SSE.Views.Toolbar.textOutBorders": "Outside borders", "SSE.Views.Toolbar.textPageMarginsCustom": "Custom margins", "SSE.Views.Toolbar.textPortrait": "Portrait", "SSE.Views.Toolbar.textPrint": "Print", "SSE.Views.Toolbar.textPrintGridlines": "Print Gridlines", "SSE.Views.Toolbar.textPrintHeadings": "Print Headings", "SSE.Views.Toolbar.textPrintOptions": "Print Settings", "SSE.Views.Toolbar.textRight": "Right: ", "SSE.Views.Toolbar.textRightBorders": "Right borders", "SSE.Views.Toolbar.textRotateDown": "Rotate text down", "SSE.Views.Toolbar.textRotateUp": "Rotate text up", "SSE.Views.Toolbar.textScale": "Scale", "SSE.Views.Toolbar.textScaleCustom": "Custom", "SSE.Views.Toolbar.textSelection": "From current selection", "SSE.Views.Toolbar.textSetPrintArea": "Set print area", "SSE.Views.Toolbar.textShowVA": "Show Visible Area", "SSE.Views.Toolbar.textStrikeout": "Strikethrough", "SSE.Views.Toolbar.textSubscript": "Subscript", "SSE.Views.Toolbar.textSubSuperscript": "Subscript/Superscript", "SSE.Views.Toolbar.textSuperscript": "Superscript", "SSE.Views.Toolbar.textTabCollaboration": "Collaboration", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabFile": "File", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "Home", "SSE.Views.Toolbar.textTabInsert": "Insert", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "Protection", "SSE.Views.Toolbar.textTabView": "View", "SSE.Views.Toolbar.textThisPivot": "From this pivot", "SSE.Views.Toolbar.textThisSheet": "From this worksheet", "SSE.Views.Toolbar.textThisTable": "From this table", "SSE.Views.Toolbar.textTop": "Top: ", "SSE.Views.Toolbar.textTopBorders": "Top borders", "SSE.Views.Toolbar.textUnderline": "Underline", "SSE.Views.Toolbar.textVertical": "Vertical text", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Align bottom", "SSE.Views.Toolbar.tipAlignCenter": "Align center", "SSE.Views.Toolbar.tipAlignJust": "Justified", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON> left", "SSE.Views.Toolbar.tipAlignMiddle": "Align middle", "SSE.Views.Toolbar.tipAlignRight": "Align right", "SSE.Views.Toolbar.tipAlignTop": "Align top", "SSE.Views.Toolbar.tipAutofilter": "Sort and filter", "SSE.Views.Toolbar.tipBack": "Back", "SSE.Views.Toolbar.tipBorders": "Borders", "SSE.Views.Toolbar.tipCellStyle": "Cell style", "SSE.Views.Toolbar.tipChangeChart": "Change chart type", "SSE.Views.Toolbar.tipClearStyle": "Clear", "SSE.Views.Toolbar.tipColorSchemas": "Change color scheme", "SSE.Views.Toolbar.tipCondFormat": "Conditional formatting", "SSE.Views.Toolbar.tipCopy": "Copy", "SSE.Views.Toolbar.tipCopyStyle": "Copy style", "SSE.Views.Toolbar.tipCut": "Cut", "SSE.Views.Toolbar.tipDecDecimal": "Decrease decimal", "SSE.Views.Toolbar.tipDecFont": "Decrement font size", "SSE.Views.Toolbar.tipDeleteOpt": "Delete cells", "SSE.Views.Toolbar.tipDigStyleAccounting": "Accounting style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Currency style", "SSE.Views.Toolbar.tipDigStylePercent": "Percent style", "SSE.Views.Toolbar.tipEditChart": "Edit Chart", "SSE.Views.Toolbar.tipEditChartData": "Select data", "SSE.Views.Toolbar.tipEditChartType": "Change Chart Type", "SSE.Views.Toolbar.tipEditHeader": "Edit header or footer", "SSE.Views.Toolbar.tipFontColor": "Font color", "SSE.Views.Toolbar.tipFontName": "Font", "SSE.Views.Toolbar.tipFontSize": "Font size", "SSE.Views.Toolbar.tipHAlighOle": "Horizontal align", "SSE.Views.Toolbar.tipImgAlign": "Align objects", "SSE.Views.Toolbar.tipImgGroup": "Group objects", "SSE.Views.Toolbar.tipIncDecimal": "Increase decimal", "SSE.Views.Toolbar.tipIncFont": "Increment font size", "SSE.Views.Toolbar.tipInsertChart": "Insert chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Insert chart", "SSE.Views.Toolbar.tipInsertEquation": "Insert equation", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "Add hyperlink", "SSE.Views.Toolbar.tipInsertImage": "Insert image", "SSE.Views.Toolbar.tipInsertOpt": "Insert cells", "SSE.Views.Toolbar.tipInsertShape": "Insert autoshape", "SSE.Views.Toolbar.tipInsertSlicer": "Insert slicer", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Insert sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "SSE.Views.Toolbar.tipInsertTable": "Insert table", "SSE.Views.Toolbar.tipInsertText": "Insert text box", "SSE.Views.Toolbar.tipInsertTextart": "Insert Text Art", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "Merge and center", "SSE.Views.Toolbar.tipNone": "None", "SSE.Views.Toolbar.tipNumFormat": "Number format", "SSE.Views.Toolbar.tipPageMargins": "Page margins", "SSE.Views.Toolbar.tipPageOrient": "Page orientation", "SSE.Views.Toolbar.tipPageSize": "Page size", "SSE.Views.Toolbar.tipPaste": "Paste", "SSE.Views.Toolbar.tipPrColor": "Fill color", "SSE.Views.Toolbar.tipPrint": "Print", "SSE.Views.Toolbar.tipPrintArea": "Print area", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "Print titles", "SSE.Views.Toolbar.tipRedo": "Redo", "SSE.Views.Toolbar.tipSave": "Save", "SSE.Views.Toolbar.tipSaveCoauth": "Save your changes for the other users to see them.", "SSE.Views.Toolbar.tipScale": "Scale to fit", "SSE.Views.Toolbar.tipSelectAll": "Select all", "SSE.Views.Toolbar.tipSendBackward": "Send backward", "SSE.Views.Toolbar.tipSendForward": "Bring forward", "SSE.Views.Toolbar.tipSynchronize": "The document has been changed by another user. Please click to save your changes and reload the updates.", "SSE.Views.Toolbar.tipTextFormatting": "More text formatting tools", "SSE.Views.Toolbar.tipTextOrientation": "Orientation", "SSE.Views.Toolbar.tipUndo": "Undo", "SSE.Views.Toolbar.tipVAlighOle": "Vertical align", "SSE.Views.Toolbar.tipVisibleArea": "Visible area", "SSE.Views.Toolbar.tipWrap": "Wrap text", "SSE.Views.Toolbar.txtAccounting": "Accounting", "SSE.Views.Toolbar.txtAdditional": "Additional", "SSE.Views.Toolbar.txtAscending": "Ascending", "SSE.Views.Toolbar.txtAutosumTip": "Summation", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "All", "SSE.Views.Toolbar.txtClearComments": "Comments", "SSE.Views.Toolbar.txtClearFilter": "Clear filter", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Function", "SSE.Views.Toolbar.txtClearHyper": "Hyperlinks", "SSE.Views.Toolbar.txtClearText": "Text", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Custom", "SSE.Views.Toolbar.txtDate": "Date", "SSE.Views.Toolbar.txtDateTime": "Date & Time", "SSE.Views.Toolbar.txtDescending": "Descending", "SSE.Views.Toolbar.txtDollar": "$ Dollar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponential", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Insert function", "SSE.Views.Toolbar.txtFraction": "Fraction", "SSE.Views.Toolbar.txtFranc": "CHF Swiss franc", "SSE.Views.Toolbar.txtGeneral": "General", "SSE.Views.Toolbar.txtInteger": "Integer", "SSE.Views.Toolbar.txtManageRange": "Name manager", "SSE.Views.Toolbar.txtMergeAcross": "Merge across", "SSE.Views.Toolbar.txtMergeCells": "Merge cells", "SSE.Views.Toolbar.txtMergeCenter": "Merge & Center", "SSE.Views.Toolbar.txtNamedRange": "Named ranges", "SSE.Views.Toolbar.txtNewRange": "Define name", "SSE.Views.Toolbar.txtNoBorders": "No borders", "SSE.Views.Toolbar.txtNumber": "Number", "SSE.Views.Toolbar.txtPasteRange": "Paste name", "SSE.Views.Toolbar.txtPercentage": "Percentage", "SSE.Views.Toolbar.txtPound": "£ Pound", "SSE.Views.Toolbar.txtRouble": "₽ Rouble", "SSE.Views.Toolbar.txtScheme1": "Office", "SSE.Views.Toolbar.txtScheme10": "Median", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Opulent", "SSE.Views.Toolbar.txtScheme14": "Oriel", "SSE.Views.Toolbar.txtScheme15": "Origin", "SSE.Views.Toolbar.txtScheme16": "Paper", "SSE.Views.Toolbar.txtScheme17": "Solstice", "SSE.Views.Toolbar.txtScheme18": "Technic", "SSE.Views.Toolbar.txtScheme19": "Trek", "SSE.Views.Toolbar.txtScheme2": "Grayscale", "SSE.Views.Toolbar.txtScheme20": "Urban", "SSE.Views.Toolbar.txtScheme21": "Verve", "SSE.Views.Toolbar.txtScheme22": "New Office", "SSE.Views.Toolbar.txtScheme3": "Apex", "SSE.Views.Toolbar.txtScheme4": "Aspect", "SSE.Views.Toolbar.txtScheme5": "Civic", "SSE.Views.Toolbar.txtScheme6": "Concourse", "SSE.Views.Toolbar.txtScheme7": "Equity", "SSE.Views.Toolbar.txtScheme8": "Flow", "SSE.Views.Toolbar.txtScheme9": "Foundry", "SSE.Views.Toolbar.txtScientific": "Scientific", "SSE.Views.Toolbar.txtSearch": "Search", "SSE.Views.Toolbar.txtSort": "Sort", "SSE.Views.Toolbar.txtSortAZ": "Sort ascending", "SSE.Views.Toolbar.txtSortZA": "Sort descending", "SSE.Views.Toolbar.txtSpecial": "Special", "SSE.Views.Toolbar.txtTableTemplate": "Format as table template", "SSE.Views.Toolbar.txtText": "Text", "SSE.Views.Toolbar.txtTime": "Time", "SSE.Views.Toolbar.txtUnmerge": "Unmerge cells", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "Show", "SSE.Views.Top10FilterDialog.txtBottom": "Bottom", "SSE.Views.Top10FilterDialog.txtBy": "by", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "Percent", "SSE.Views.Top10FilterDialog.txtSum": "Sum", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 AutoFilter", "SSE.Views.Top10FilterDialog.txtTop": "Top", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filter", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Value field settings", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Average", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Base field", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Base item", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 of %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Count", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "The difference from", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "No calculation", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Percent of", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Percent difference from", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Percent of column", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Percent of total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Percent of row", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Product", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Running total in", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Show values as", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Sum", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Summarize value field by", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Close", "SSE.Views.ViewManagerDlg.guestText": "Guest", "SSE.Views.ViewManagerDlg.lockText": "Locked", "SSE.Views.ViewManagerDlg.textDelete": "Delete", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplicate", "SSE.Views.ViewManagerDlg.textEmpty": "No views have been created yet.", "SSE.Views.ViewManagerDlg.textGoTo": "Go to view", "SSE.Views.ViewManagerDlg.textLongName": "Enter a name that is less than 128 characters.", "SSE.Views.ViewManagerDlg.textNew": "New", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "View name must not be empty.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Rename view", "SSE.Views.ViewManagerDlg.textViews": "Sheet views", "SSE.Views.ViewManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ViewManagerDlg.txtTitle": "Sheet view manager", "SSE.Views.ViewManagerDlg.warnDeleteView": "You are trying to delete the currently enabled view '%1'.<br>Close this view and delete it?", "SSE.Views.ViewTab.capBtnFreeze": "Freeze Panes", "SSE.Views.ViewTab.capBtnSheetView": "Sheet View", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "SSE.Views.ViewTab.textClose": "Close", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combine Sheet and Status Bars", "SSE.Views.ViewTab.textCreate": "New", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFormula": "Formula Bar", "SSE.Views.ViewTab.textFreezeCol": "Freeze first column", "SSE.Views.ViewTab.textFreezeRow": "Freeze top row", "SSE.Views.ViewTab.textGridlines": "Gridlines", "SSE.Views.ViewTab.textHeadings": "Headings", "SSE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textManager": "View manager", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Show frozen panes shadow", "SSE.Views.ViewTab.textUnFreeze": "Unfreeze panes", "SSE.Views.ViewTab.textZeros": "Show Zeros", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Close sheet view", "SSE.Views.ViewTab.tipCreate": "Create sheet view", "SSE.Views.ViewTab.tipFreeze": "Freeze panes", "SSE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "SSE.Views.ViewTab.tipSheetView": "Sheet view", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.hintProtectSheet": "Protect sheet", "SSE.Views.WBProtection.hintProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtAllowRanges": "Allow Edit Ranges", "SSE.Views.WBProtection.txtHiddenFormula": "Hidden Formulas", "SSE.Views.WBProtection.txtLockedCell": "Locked Cell", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON> Locked", "SSE.Views.WBProtection.txtLockedText": "Lock Text", "SSE.Views.WBProtection.txtProtectSheet": "Protect Sheet", "SSE.Views.WBProtection.txtProtectWB": "Protect Workbook", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Enter a password to unprotect sheet", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Unprotect Sheet", "SSE.Views.WBProtection.txtWBUnlockDescription": "Enter a password to unprotect workbook", "SSE.Views.WBProtection.txtWBUnlockTitle": "Unprotect Workbook"}