{"Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Introduzca su mensaje aquí", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "El objeto está desactivado porque se está editando por otro usuario.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Aviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "Objeto desactivado porque está siendo editado por otro usuario", "Common.Controllers.ExternalOleEditor.warningTitle": "Advertencia", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> a<PERSON>a", "Common.define.chartData.textAreaStackedPer": "<PERSON>rea amontonada 100% ", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "<PERSON>umna a<PERSON>", "Common.define.chartData.textBarNormal3d": "Columna 3D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Columna 3D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON> apilada", "Common.define.chartData.textBarStacked3d": "Columna 3D apilada", "Common.define.chartData.textBarStackedPer": "Columna amontonada 100%", "Common.define.chartData.textBarStackedPer3d": "Columna 3D apilada 100%", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Gráfico de columnas", "Common.define.chartData.textCombo": "Combinado", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> api<PERSON>a - Columna a<PERSON>", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON>na a<PERSON> <PERSON><PERSON><PERSON>", "Common.define.chartData.textComboBarLineSecondary": "Columna agrupada - Línea en eje secundario", "Common.define.chartData.textComboCustom": "Combinación personalizada", "Common.define.chartData.textDoughnut": "<PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Barra agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3D agrupada", "Common.define.chartData.textHBarStacked": "Barra apilada", "Common.define.chartData.textHBarStacked3d": "Barra 3D apilada", "Common.define.chartData.textHBarStackedPer": "Barra amontonada 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D apilada 100%", "Common.define.chartData.textLine": "Lín<PERSON>", "Common.define.chartData.textLine3d": "Línea 3D", "Common.define.chartData.textLineMarker": "Línea con marcadores", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON> apilada", "Common.define.chartData.textLineStackedMarker": "Línea apilada con marcadores", "Common.define.chartData.textLineStackedPer": "Línea amontonada 100%", "Common.define.chartData.textLineStackedPerMarker": "Línea amointonada con marcadores 100%", "Common.define.chartData.textPie": "Gráfico circular", "Common.define.chartData.textPie3d": "Circular 3D", "Common.define.chartData.textPoint": "XY (Dispersión)", "Common.define.chartData.textScatter": "Dispersión", "Common.define.chartData.textScatterLine": "Dispersión con líneas rectas", "Common.define.chartData.textScatterLineMarker": "Dispersión con líneas rectas y marcadores", "Common.define.chartData.textScatterSmooth": "Dispersión con líneas suavizadas", "Common.define.chartData.textScatterSmoothMarker": "Dispersión con líneas suavizadas y marcadores", "Common.define.chartData.textStock": "De cotizaciones", "Common.define.chartData.textSurface": "Superficie", "Common.define.effectData.textAcross": "Horizontal", "Common.define.effectData.textAppear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textArcDown": "Arco hacia abajo", "Common.define.effectData.textArcLeft": "Arco hacia a la izquierda", "Common.define.effectData.textArcRight": "Arco hacia la derecha", "Common.define.effectData.textArcs": "Arcos", "Common.define.effectData.textArcUp": "Arco hacia arriba", "Common.define.effectData.textBasic": "Básico", "Common.define.effectData.textBasicSwivel": "Giro básico", "Common.define.effectData.textBasicZoom": "Zoom básico", "Common.define.effectData.textBean": "Alubia", "Common.define.effectData.textBlinds": "Persianas", "Common.define.effectData.textBlink": "Intermitente", "Common.define.effectData.textBoldFlash": "Flash en negrita", "Common.define.effectData.textBoldReveal": "Revelar en negrita", "Common.define.effectData.textBoomerang": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "Rebote hacia la izquierda", "Common.define.effectData.textBounceRight": "<PERSON><PERSON>e hacia la derecha", "Common.define.effectData.textBox": "Cuadro", "Common.define.effectData.textBrushColor": "Color del pincel", "Common.define.effectData.textCenterRevolve": "G<PERSON>r hacia el centro", "Common.define.effectData.textCheckerboard": "Cuadros bicolores", "Common.define.effectData.textCircle": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCollapse": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textColorPulse": "Pulso de color", "Common.define.effectData.textComplementaryColor": "Color complementario", "Common.define.effectData.textComplementaryColor2": "Color complementario 2", "Common.define.effectData.textCompress": "Comprimir", "Common.define.effectData.textContrast": "Contraste ", "Common.define.effectData.textContrastingColor": "Color de contraste", "Common.define.effectData.textCredits": "C<PERSON>dit<PERSON>", "Common.define.effectData.textCrescentMoon": "Luna creciente", "Common.define.effectData.textCurveDown": "<PERSON><PERSON><PERSON> hacia abajo", "Common.define.effectData.textCurvedSquare": "Cuadrado curvado", "Common.define.effectData.textCurvedX": "X curvada", "Common.define.effectData.textCurvyLeft": "Curvas hacia la izquierda", "Common.define.effectData.textCurvyRight": "<PERSON><PERSON><PERSON> hacia la derecha", "Common.define.effectData.textCurvyStar": "Estrella curvada", "Common.define.effectData.textCustomPath": "<PERSON>uta <PERSON>", "Common.define.effectData.textCuverUp": "Curva hacia arriba", "Common.define.effectData.textDarken": "Oscurecer", "Common.define.effectData.textDecayingWave": "Serpentina", "Common.define.effectData.textDesaturate": "Saturación reducida", "Common.define.effectData.textDiagonalDownRight": "Diagonal hacia abajo derecha", "Common.define.effectData.textDiagonalUpRight": "Diagonal hacia arriba derecha", "Common.define.effectData.textDiamond": "Rombo", "Common.define.effectData.textDisappear": "Desapa<PERSON>cer", "Common.define.effectData.textDissolveIn": "Disolver hacia dentro", "Common.define.effectData.textDissolveOut": "Disolver hacia fuera", "Common.define.effectData.textDown": "Abajo", "Common.define.effectData.textDrop": "Colocar", "Common.define.effectData.textEmphasis": "Efectos de énfasis", "Common.define.effectData.textEntrance": "Efectos de entrada", "Common.define.effectData.textEqualTriangle": "Trián<PERSON><PERSON>", "Common.define.effectData.textExciting": "Llamativo", "Common.define.effectData.textExit": "Efectos de salida", "Common.define.effectData.textExpand": "Expandir", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFigureFour": "Figura 8 cuatro veces", "Common.define.effectData.textFillColor": "Color de relleno", "Common.define.effectData.textFlip": "Voltear", "Common.define.effectData.textFloat": "Flotante", "Common.define.effectData.textFloatDown": "Flotante hacia abajo", "Common.define.effectData.textFloatIn": "Flotante hacia dentro", "Common.define.effectData.textFloatOut": "Flotante hacia fuera", "Common.define.effectData.textFloatUp": "Flotante hacia arriba", "Common.define.effectData.textFlyIn": "Volar hacia dentro", "Common.define.effectData.textFlyOut": "Volar hacia fuera", "Common.define.effectData.textFontColor": "Color de fuente", "Common.define.effectData.textFootball": "Fútbol", "Common.define.effectData.textFromBottom": "<PERSON><PERSON>", "Common.define.effectData.textFromBottomLeft": "Desde la parte inferior izquierda", "Common.define.effectData.textFromBottomRight": "Desde la parte inferior derecha", "Common.define.effectData.textFromLeft": "Desde la izquierda", "Common.define.effectData.textFromRight": "Desde la derecha", "Common.define.effectData.textFromTop": "<PERSON><PERSON>", "Common.define.effectData.textFromTopLeft": "Desde la parte superior izquierda", "Common.define.effectData.textFromTopRight": "Desde la parte superior derecha", "Common.define.effectData.textFunnel": "Embudo", "Common.define.effectData.textGrowShrink": "Aumentar/Encoger", "Common.define.effectData.textGrowTurn": "Aumentar y girar", "Common.define.effectData.textGrowWithColor": "Aumentar con color", "Common.define.effectData.textHeart": "Corazón", "Common.define.effectData.textHeartbeat": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHexagon": "Hexágono", "Common.define.effectData.textHorizontal": "Horizontal ", "Common.define.effectData.textHorizontalFigure": "Figura 8 horizontal", "Common.define.effectData.textHorizontalIn": "Horizontal entrante", "Common.define.effectData.textHorizontalOut": "Horizontal saliente", "Common.define.effectData.textIn": "<PERSON><PERSON> dentro", "Common.define.effectData.textInFromScreenCenter": "Aumentar desde el centro de pantalla", "Common.define.effectData.textInSlightly": "Acercar ligeramente", "Common.define.effectData.textInToScreenBottom": "Acercar hacia parte inferior de la pantalla", "Common.define.effectData.textInvertedSquare": "Cuadrado invertido", "Common.define.effectData.textInvertedTriangle": "Triángulo invertido", "Common.define.effectData.textLeft": "Iz<PERSON>erda", "Common.define.effectData.textLeftDown": "Izquierda y abajo", "Common.define.effectData.textLeftUp": "Izquierda y arriba", "Common.define.effectData.textLighten": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLineColor": "Color de línea", "Common.define.effectData.textLines": "Líneas", "Common.define.effectData.textLinesCurves": "Líneas curvas", "Common.define.effectData.textLoopDeLoop": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textLoops": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textModerate": "Moderado", "Common.define.effectData.textNeutron": "Neutrón", "Common.define.effectData.textObjectCenter": "Centro del objeto", "Common.define.effectData.textObjectColor": "Color de objeto", "Common.define.effectData.textOctagon": "Octágono", "Common.define.effectData.textOut": "<PERSON><PERSON> fuera", "Common.define.effectData.textOutFromScreenBottom": "Alejar desde la zona inferior de la pantalla", "Common.define.effectData.textOutSlightly": "<PERSON><PERSON><PERSON> liger<PERSON>e", "Common.define.effectData.textOutToScreenCenter": "Alejar hacia el centro de la pantalla", "Common.define.effectData.textParallelogram": "Paralelogramo", "Common.define.effectData.textPath": "<PERSON><PERSON><PERSON> de movimiento", "Common.define.effectData.textPathCurve": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPathLine": "Trazo", "Common.define.effectData.textPathScribble": "Garabato", "Common.define.effectData.textPeanut": "Cacahuete", "Common.define.effectData.textPeekIn": "Desplegar hacia arriba", "Common.define.effectData.textPeekOut": "<PERSON><PERSON>gar hacia abajo", "Common.define.effectData.textPentagon": "Pentágono", "Common.define.effectData.textPinwheel": "Remolino", "Common.define.effectData.textPlus": "Más", "Common.define.effectData.textPointStar": "Estrella de puntas", "Common.define.effectData.textPointStar4": "Estrella de 4 puntas", "Common.define.effectData.textPointStar5": "Estrella de 5 puntas", "Common.define.effectData.textPointStar6": "Estrella de 6 puntas", "Common.define.effectData.textPointStar8": "Estrella de 8 puntas", "Common.define.effectData.textPulse": "<PERSON>mpul<PERSON>", "Common.define.effectData.textRandomBars": "Barras aleatorias", "Common.define.effectData.textRight": "A la derecha", "Common.define.effectData.textRightDown": "Derecha y abajo", "Common.define.effectData.textRightTriangle": "Triángulo rectángulo", "Common.define.effectData.textRightUp": "Derecha y arriba", "Common.define.effectData.textRiseUp": "Desplegar hacia arriba", "Common.define.effectData.textSCurve1": "Curva S 1", "Common.define.effectData.textSCurve2": "Curva S 2", "Common.define.effectData.textShape": "Forma", "Common.define.effectData.textShapes": "Formas", "Common.define.effectData.textShimmer": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textShrinkTurn": "<PERSON>uc<PERSON> y girar", "Common.define.effectData.textSineWave": "Sine Wave", "Common.define.effectData.textSinkDown": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSlideCenter": "Centro de la diapositiva", "Common.define.effectData.textSpecial": "Especial", "Common.define.effectData.textSpin": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpinner": "Centrifugado", "Common.define.effectData.textSpiralIn": "Espiral hacia dentro", "Common.define.effectData.textSpiralLeft": "Espiral hacia la izquierda", "Common.define.effectData.textSpiralOut": "Espiral hacia fuera", "Common.define.effectData.textSpiralRight": "Espiral hacia la derecha", "Common.define.effectData.textSplit": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpoke1": "1 radio", "Common.define.effectData.textSpoke2": "2 radios", "Common.define.effectData.textSpoke3": "3 radios", "Common.define.effectData.textSpoke4": "4 radios", "Common.define.effectData.textSpoke8": "8 radios", "Common.define.effectData.textSpring": "<PERSON><PERSON>", "Common.define.effectData.textSquare": "Cuadrado", "Common.define.effectData.textStairsDown": "Escaleras abajo", "Common.define.effectData.textStretch": "<PERSON>st<PERSON><PERSON>", "Common.define.effectData.textStrips": "<PERSON><PERSON>", "Common.define.effectData.textSubtle": "<PERSON><PERSON>", "Common.define.effectData.textSwivel": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSwoosh": "Silbido", "Common.define.effectData.textTeardrop": "Lágrima", "Common.define.effectData.textTeeter": "Tambalear", "Common.define.effectData.textToBottom": "<PERSON><PERSON> abajo", "Common.define.effectData.textToBottomLeft": "Hacia abajo a la izquierda", "Common.define.effectData.textToBottomRight": "Hacia abajo a la derecha", "Common.define.effectData.textToLeft": "A la izquierda", "Common.define.effectData.textToRight": "A la derecha", "Common.define.effectData.textToTop": "<PERSON><PERSON> arriba", "Common.define.effectData.textToTopLeft": "Hacia arriba a la izquierda", "Common.define.effectData.textToTopRight": "Hacia arriba a la derecha", "Common.define.effectData.textTransparency": "Transparencia", "Common.define.effectData.textTrapezoid": "Trapecio", "Common.define.effectData.textTurnDown": "Giro hacia abajo", "Common.define.effectData.textTurnDownRight": "<PERSON><PERSON><PERSON> hacia abajo y a la derecha", "Common.define.effectData.textTurns": "Giros", "Common.define.effectData.textTurnUp": "<PERSON><PERSON><PERSON> hacia arriba", "Common.define.effectData.textTurnUpRight": "<PERSON><PERSON><PERSON> hacia arriba a la derecha", "Common.define.effectData.textUnderline": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textUp": "Arriba", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Figura 8 vertical", "Common.define.effectData.textVerticalIn": "Vertical entrante", "Common.define.effectData.textVerticalOut": "Vertical saliente", "Common.define.effectData.textWave": "On<PERSON>", "Common.define.effectData.textWedge": "Cuña", "Common.define.effectData.textWheel": "Rue<PERSON>", "Common.define.effectData.textWhip": "<PERSON><PERSON>", "Common.define.effectData.textWipe": "Barrido", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.define.smartArt.textAccentedPicture": "Imagen destacada", "Common.define.smartArt.textAccentProcess": "Proceso destacado", "Common.define.smartArt.textAlternatingFlow": "Flujo alternativo", "Common.define.smartArt.textAlternatingHexagons": "Hexágonos alternativos", "Common.define.smartArt.textAlternatingPictureBlocks": "Bloques de imágenes alternativos", "Common.define.smartArt.textAlternatingPictureCircles": "Círculos con imágenes alternativos", "Common.define.smartArt.textArchitectureLayout": "Diseño de arquitectura", "Common.define.smartArt.textArrowRibbon": "<PERSON>inta de flechas", "Common.define.smartArt.textAscendingPictureAccentProcess": "Proceso de imágenes destacadas ascendente", "Common.define.smartArt.textBalance": "<PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Proceso curvo básico", "Common.define.smartArt.textBasicBlockList": "Lista de bloques básica", "Common.define.smartArt.textBasicChevronProcess": "Proceso cheurón básico", "Common.define.smartArt.textBasicCycle": "Ciclo básico", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Circular básico", "Common.define.smartArt.textBasicProcess": "Proceso básico", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textBasicRadial": "Radial básico", "Common.define.smartArt.textBasicTarget": "Objetivo básico", "Common.define.smartArt.textBasicTimeline": "Escala de tiempo básica", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n b<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Lista destacada con círculos abajo", "Common.define.smartArt.textBendingPictureBlocks": "Bloques de imágenes con cuadro", "Common.define.smartArt.textBendingPictureCaption": "Imágenes con títulos", "Common.define.smartArt.textBendingPictureCaptionList": "Lista de títulos de imágenes", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Imágenes con texto semitransparente", "Common.define.smartArt.textBlockCycle": "Ciclo de bloques", "Common.define.smartArt.textBubblePictureList": "Lista de imágenes con burbujas", "Common.define.smartArt.textCaptionedPictures": "Imágenes con títulos", "Common.define.smartArt.textChevronAccentProcess": "Proceso cheurón destacado", "Common.define.smartArt.textChevronList": "Lista de cheurones", "Common.define.smartArt.textCircleAccentTimeline": "Línea de tiempo con círculos", "Common.define.smartArt.textCircleArrowProcess": "Proceso de círculos con flecha", "Common.define.smartArt.textCirclePictureHierarchy": "Jerarquía con imágenes en círculos", "Common.define.smartArt.textCircleProcess": "Proceso de círculos", "Common.define.smartArt.textCircleRelationship": "Relación de círculo", "Common.define.smartArt.textCircularBendingProcess": "Proceso curvo circular", "Common.define.smartArt.textCircularPictureCallout": "Globo de imagen circular", "Common.define.smartArt.textClosedChevronProcess": "Proceso de cheurón cerrado", "Common.define.smartArt.textContinuousArrowProcess": "Proceso de flechas continuo", "Common.define.smartArt.textContinuousBlockProcess": "Proceso de bloque continuo", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON> continuo", "Common.define.smartArt.textContinuousPictureList": "Lista de imágenes continua", "Common.define.smartArt.textConvergingArrows": "<PERSON>le<PERSON><PERSON> convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergente", "Common.define.smartArt.textConvergingText": "Texto convergente", "Common.define.smartArt.textCounterbalanceArrows": "Flechas de contrapeso", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Lista de bloques descendente", "Common.define.smartArt.textDescendingProcess": "Proceso descendente", "Common.define.smartArt.textDetailedProcess": "Proceso detallado", "Common.define.smartArt.textDivergingArrows": "Flechas divergentes", "Common.define.smartArt.textDivergingRadial": "Radial divergente", "Common.define.smartArt.textEquation": "Ecuación", "Common.define.smartArt.textFramedTextPicture": "Imagen de texto enmarcado", "Common.define.smartArt.textFunnel": "Embudo", "Common.define.smartArt.textGear": "Engranaje", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textGroupedList": "Lista agrupada", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organigrama con semicírculos", "Common.define.smartArt.textHexagonCluster": "Grupo de hexágonos", "Common.define.smartArt.textHexagonRadial": "Radial con hexágon<PERSON>", "Common.define.smartArt.textHierarchy": "Jerar<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "Lista de jerarquías", "Common.define.smartArt.textHorizontalBulletList": "Lista de viñetas horizontal", "Common.define.smartArt.textHorizontalHierarchy": "Jerarquía horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Jerarquía etiquetada horizontal", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Jerarquía horizontal de varios niveles", "Common.define.smartArt.textHorizontalOrganizationChart": "Organigrama horizontal", "Common.define.smartArt.textHorizontalPictureList": "Lista horizontal de imágenes", "Common.define.smartArt.textIncreasingArrowProcess": "Proceso de flechas crecientes", "Common.define.smartArt.textIncreasingCircleProcess": "Proceso de círculos crecientes", "Common.define.smartArt.textInterconnectedBlockProcess": "Bloque interconectado", "Common.define.smartArt.textInterconnectedRings": "Anillos interconectados", "Common.define.smartArt.textInvertedPyramid": "Pirámide invertida", "Common.define.smartArt.textLabeledHierarchy": "Jerarquía etiquetada", "Common.define.smartArt.textLinearVenn": "Venn lineal", "Common.define.smartArt.textLinedList": "Lista alineada", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclo multidireccional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigrama con nombres y cargos", "Common.define.smartArt.textNestedTarget": "Objetivo anidado", "Common.define.smartArt.textNondirectionalCycle": "Ciclo sin dirección", "Common.define.smartArt.textOpposingArrows": "Flechas opuestas", "Common.define.smartArt.textOpposingIdeas": "Ideas opuestas", "Common.define.smartArt.textOrganizationChart": "Organigrama", "Common.define.smartArt.textOther": "<PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Proceso en fases", "Common.define.smartArt.textPicture": "Imagen", "Common.define.smartArt.textPictureAccentBlocks": "Imágenes destacadas en bloques", "Common.define.smartArt.textPictureAccentList": "Lista de imágenes destacadas", "Common.define.smartArt.textPictureAccentProcess": "Proceso de imágenes destacadas", "Common.define.smartArt.textPictureCaptionList": "Lista de títulos de imágenes", "Common.define.smartArt.textPictureFrame": "MarcoDeFotos", "Common.define.smartArt.textPictureGrid": "Imágenes en cuadrícula", "Common.define.smartArt.textPictureLineup": "Imágenes en paralelo", "Common.define.smartArt.textPictureOrganizationChart": "Organigrama con imágenes", "Common.define.smartArt.textPictureStrips": "Imágenes en columna", "Common.define.smartArt.textPieProcess": "Proceso circular", "Common.define.smartArt.textPlusAndMinus": "Más y menos", "Common.define.smartArt.textProcess": "Proceso", "Common.define.smartArt.textProcessArrows": "Flechas de proceso", "Common.define.smartArt.textProcessList": "Lista de procesos", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Lista en pirámide", "Common.define.smartArt.textRadialCluster": "Diseño radial", "Common.define.smartArt.textRadialCycle": "Ciclo radial", "Common.define.smartArt.textRadialList": "Lista radial", "Common.define.smartArt.textRadialPictureList": "Lista radial con imágenes", "Common.define.smartArt.textRadialVenn": "Venn radial", "Common.define.smartArt.textRandomToResultProcess": "Proceso de azar a resultado", "Common.define.smartArt.textRelationship": "Relación", "Common.define.smartArt.textRepeatingBendingProcess": "Proceso curvo repetitivo", "Common.define.smartArt.textReverseList": "Lista inversa", "Common.define.smartArt.textSegmentedCycle": "Ciclo segmentado", "Common.define.smartArt.textSegmentedProcess": "Proceso segmentado", "Common.define.smartArt.textSegmentedPyramid": "Pirámide segmentada", "Common.define.smartArt.textSnapshotPictureList": "Lista de imágenes instantáneas", "Common.define.smartArt.textSpiralPicture": "Imagen en espiral", "Common.define.smartArt.textSquareAccentList": "Lista de imágenes con cuadrados", "Common.define.smartArt.textStackedList": "Lista apilada", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "Proceso escalonado", "Common.define.smartArt.textStepDownProcess": "Proceso de nivel inferior", "Common.define.smartArt.textStepUpProcess": "Proceso de nivel superior", "Common.define.smartArt.textSubStepProcess": "Proceso de pasos secundarios", "Common.define.smartArt.textTabbedArc": "Arco con pestañas", "Common.define.smartArt.textTableHierarchy": "Jerarquía de tabla", "Common.define.smartArt.textTableList": "Lista de tablas", "Common.define.smartArt.textTabList": "Lista de pestañas", "Common.define.smartArt.textTargetList": "Lista de objetivo", "Common.define.smartArt.textTextCycle": "Ciclo de texto", "Common.define.smartArt.textThemePictureAccent": "Imágenes temáticas destacadas", "Common.define.smartArt.textThemePictureAlternatingAccent": "Imágenes temáticas destacadas alternativas", "Common.define.smartArt.textThemePictureGrid": "Imágenes temáticas en cuadrícula", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON> con tí<PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Lista de imágenes destacadas con título", "Common.define.smartArt.textTitledPictureBlocks": "Bloques de imágenes con títulos", "Common.define.smartArt.textTitlePictureLineup": "Serie de imágenes con título", "Common.define.smartArt.textTrapezoidList": "Lista de trapezoides", "Common.define.smartArt.textUpwardArrow": "Flecha arriba", "Common.define.smartArt.textVaryingWidthList": "Lista de ancho variable", "Common.define.smartArt.textVerticalAccentList": "Lista con rectángulos en vertical", "Common.define.smartArt.textVerticalArrowList": "Lista vertical de flechas", "Common.define.smartArt.textVerticalBendingProcess": "Proceso curvo vertical", "Common.define.smartArt.textVerticalBlockList": "Lista de bloques verticales", "Common.define.smartArt.textVerticalBoxList": "Lista vertical de cuadros", "Common.define.smartArt.textVerticalBracketList": "Lista vertical con corchetes", "Common.define.smartArt.textVerticalBulletList": "Lista vertical de viñetas", "Common.define.smartArt.textVerticalChevronList": "Lista vertical de cheurones", "Common.define.smartArt.textVerticalCircleList": "Lista con círculos en vertical", "Common.define.smartArt.textVerticalCurvedList": "Lista curvada vertical", "Common.define.smartArt.textVerticalEquation": "Ecuación vertical", "Common.define.smartArt.textVerticalPictureAccentList": "Lista con círculos a la izquierda", "Common.define.smartArt.textVerticalPictureList": "Lista vertical de imágenes", "Common.define.smartArt.textVerticalProcess": "Proceso vertical", "Common.Translation.textMoreButton": "Más", "Common.Translation.tipFileLocked": "El documento está bloqueado para su edición. Puede hacer cambios y guardarlo como copia local más tarde.", "Common.Translation.tipFileReadOnly": "El archivo es de solo lectura. Para no perder los cambios, guarde el archivo con otro nombre o en otra ubicación.", "Common.Translation.warnFileLocked": "El archivo está siendo editado en otra aplicación. Puede continuar editándolo y guardarlo como una copia.", "Common.Translation.warnFileLockedBtnEdit": "Crear copia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Color personalizado", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON> bordes", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON> bordes", "Common.UI.ComboDataView.emptyComboText": "Sin estilo", "Common.UI.ExtendedColorDialog.addButtonText": "Agregar", "Common.UI.ExtendedColorDialog.textCurrent": "Actual", "Common.UI.ExtendedColorDialog.textHexErr": "El valor introducido es incorrecto.<br>Por favor, introduzca un valor de 000000 a FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nuevo", "Common.UI.ExtendedColorDialog.textRGBErr": "El valor introducido es incorrecto.<br><PERSON>r favor, introduzca un valor numérico de 0 a 225.", "Common.UI.HSBColorPicker.textNoColor": "Sin Color", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar la contraseña", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostrar la contraseña", "Common.UI.SearchBar.textFind": "Buscar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Resul<PERSON><PERSON> si<PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir los ajustes avanzados", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Resaltar resultados", "Common.UI.SearchDialog.textMatchCase": "Sensible a mayúsculas y minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Introduzca el texto de sustitución", "Common.UI.SearchDialog.textSearchStart": "Introduzca su texto aquí", "Common.UI.SearchDialog.textTitle": "Buscar y reemplazar", "Common.UI.SearchDialog.textTitle2": "Encontrar", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON> palabras completas", "Common.UI.SearchDialog.txtBtnHideReplace": "Esconder Sustitución", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Common.UI.SynchronizeTip.textDontShow": "No volver a mostrar este mensaje", "Common.UI.SynchronizeTip.textSynchronize": "El documento ha sido cambiado por otro usuario.<br>Por favor haga clic para guardar sus cambios y recargue las actualizaciones.", "Common.UI.ThemeColorPalette.textRecentColors": "Colores recientes", "Common.UI.ThemeColorPalette.textStandartColors": "Colores estándar", "Common.UI.ThemeColorPalette.textThemeColors": "Colores de tema", "Common.UI.Themes.txtThemeClassicLight": "Clásico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste oscuro", "Common.UI.Themes.txtThemeDark": "Oscuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Igual que el sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "Aceptar", "Common.UI.Window.textConfirmation": "Confirmación", "Common.UI.Window.textDontShow": "No volver a mostrar este mensaje", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Información", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "Sí", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Control", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "dirección: ", "Common.Views.About.txtLicensee": "LICENCIATARIO ", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "correo: ", "Common.Views.About.txtPoweredBy": "Desarrollado por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versión ", "Common.Views.AutoCorrectDialog.textAdd": "Agregar", "Common.Views.AutoCorrectDialog.textApplyText": "Aplicar mientras escribe", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorrección de texto", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformato mientras escribe", "Common.Views.AutoCorrectDialog.textBulleted": "Listas con viñetas automáticas", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "<PERSON><PERSON><PERSON> punto con doble espacio", "Common.Views.AutoCorrectDialog.textFLCells": "Poner en mayúsculas la primera letra de las celdas de la tabla", "Common.Views.AutoCorrectDialog.textFLSentence": "Poner en mayúscula la primera letra de una oración", "Common.Views.AutoCorrectDialog.textHyperlink": "Rutas de red e Internet por hipervínculos", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON> (--) con gui<PERSON> largo (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorrección matemática", "Common.Views.AutoCorrectDialog.textNumbered": "Listas con numeración automática", "Common.Views.AutoCorrectDialog.textQuotes": "\"Comillas rectas\" con \"comillas tipográficas\"", "Common.Views.AutoCorrectDialog.textRecognized": "Funciones reconocidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Las siguientes expresiones son expresiones matemáticas reconocidas. No se pondrán en cursiva automáticamente.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON><PERSON><PERSON> escribe", "Common.Views.AutoCorrectDialog.textReplaceType": "Reemplazar texto mientras escribe", "Common.Views.AutoCorrectDialog.textReset": "Restablecer", "Common.Views.AutoCorrectDialog.textResetAll": "Restablecer valores predeterminados", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorrección", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Las funciones reconocidas deben contener solo letras de la A a la Z, mayúsculas o minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Cualquier expresión que haya agregado se eliminará y las eliminadas se restaurarán. ¿Desea continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "La entrada de autocorreción para %1 ya existe. ¿Desea reemplazarla?", "Common.Views.AutoCorrectDialog.warnReset": "Las autocorrecciones que haya agregado se eliminarán y las modificadas recuperarán sus valores originales. ¿Desea continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "La entrada de autocorrección para %1 será restablecida a su valor original. ¿Desea continuar?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor de Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON>ás antiguo", "Common.Views.Comments.mniDateDesc": "Más reciente", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON>", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON>", "Common.Views.Comments.textAdd": "Agregar", "Common.Views.Comments.textAddComment": "Agregar comentario", "Common.Views.Comments.textAddCommentToDoc": "Agregar comentario al documento", "Common.Views.Comments.textAddReply": "Agregar respuesta", "Common.Views.Comments.textAll": "Todo", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON>r comentarios", "Common.Views.Comments.textComments": "Comentarios", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Introduzca su comentario aquí", "Common.Views.Comments.textHintAddComment": "Agregar comentario", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>r de nuevo", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Ordenar comentarios", "Common.Views.Comments.textViewResolved": "No tiene permiso para volver a abrir el documento", "Common.Views.Comments.txtEmpty": "Sin comentarios en el documento", "Common.Views.CopyWarningDialog.textDontShow": "No volver a mostrar este mensaje", "Common.Views.CopyWarningDialog.textMsg": "Se puede realizar las acciones de copiar, cortar y pegar usando los botones en la barra de herramientas y el menú contextual sólo en esta pestaña del editor.<br><br>Si quiere copiar o pegar algo fuera de esta pestaña, usa las combinaciones de teclas siguientes:", "Common.Views.CopyWarningDialog.textTitle": "Acciones de Copiar, Cortar y Pegar", "Common.Views.CopyWarningDialog.textToCopy": "para copiar", "Common.Views.CopyWarningDialog.textToCut": "para cortar", "Common.Views.CopyWarningDialog.textToPaste": "para pegar", "Common.Views.DocumentAccessDialog.textLoading": "Cargando...", "Common.Views.DocumentAccessDialog.textTitle": "Ajustes de uso compartido", "Common.Views.ExternalDiagramEditor.textTitle": "Editor de gráfico", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Guardar y salir", "Common.Views.ExternalOleEditor.textTitle": "Editor de hojas de cálculo", "Common.Views.Header.labelCoUsersDescr": "El documento está siendo editado por usuarios:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "Abrir ubicación del archivo", "Common.Views.Header.textCompactView": "Esconder barra de herramientas", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON> reglas", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON> notas", "Common.Views.Header.textHideStatusBar": "Ocultar barra de estado", "Common.Views.Header.textReadOnly": "Sólo lectura", "Common.Views.Header.textRemoveFavorite": "Eliminar de Favoritos", "Common.Views.Header.textSaveBegin": "Guardando...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "Todos los cambios son guardados", "Common.Views.Header.textSaveExpander": "Todos los cambios son guardados", "Common.Views.Header.textShare": "Compartir", "Common.Views.Header.textZoom": "Ampliación", "Common.Views.Header.tipAccessRights": "Gestionar derechos de acceso al documento", "Common.Views.Header.tipDownload": "Descargar archivo", "Common.Views.Header.tipGoEdit": "Editar el archivo actual", "Common.Views.Header.tipPrint": "Imprimir archivo", "Common.Views.Header.tipPrintQuick": "Impresión rápida", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Guardar", "Common.Views.Header.tipSearch": "Búsqueda", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Desacoplar en una ventana independiente", "Common.Views.Header.tipUsers": "Ver usuarios", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON> a<PERSON>", "Common.Views.Header.tipViewUsers": "Ver usuarios y administrar derechos de acceso al documento", "Common.Views.Header.txtAccessRights": "Cambiar derechos de acceso", "Common.Views.Header.txtRename": "Renombrar", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON> historial", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar cambios detallados", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Mostrar cambios detallados", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Pegar URL de imagen:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo es obligatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo debe ser URL en el formato \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Hay que especificar número válido de filas y columnas.", "Common.Views.InsertTableDialog.txtColumns": "Número de columnas", "Common.Views.InsertTableDialog.txtMaxText": "El valor máximo para este campo es {0}.", "Common.Views.InsertTableDialog.txtMinText": "El valor mínimo para este campo es {0}.", "Common.Views.InsertTableDialog.txtRows": "Número <PERSON>", "Common.Views.InsertTableDialog.txtTitle": "Tamaño de tabla", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON> celda", "Common.Views.LanguageDialog.labelSelect": "Seleccionar el idioma de documento", "Common.Views.ListSettingsDialog.textBulleted": "Con viñetas", "Common.Views.ListSettingsDialog.textFromFile": "Desde archivo", "Common.Views.ListSettingsDialog.textFromStorage": "Desde almacenamiento", "Common.Views.ListSettingsDialog.textFromUrl": "Desde URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "Cambiar viñeta", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>ñ<PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Color", "Common.Views.ListSettingsDialog.txtImage": "Imagen", "Common.Views.ListSettingsDialog.txtImport": "Importación", "Common.Views.ListSettingsDialog.txtNewBullet": "Nueva viñeta", "Common.Views.ListSettingsDialog.txtNewImage": "Imagen nueva", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% de texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> con", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Ajustes de lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "Cerrar archivo", "Common.Views.OpenDialog.txtEncoding": "Codificación", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtOpenFile": "Escribir la contraseña para abrir el archivo", "Common.Views.OpenDialog.txtPassword": "Contraseña", "Common.Views.OpenDialog.txtProtected": "Una vez que se ha introducido la contraseña y abierto el archivo, la contraseña actual al archivo se restablecerá", "Common.Views.OpenDialog.txtTitle": "Elegir opciones de %1", "Common.Views.OpenDialog.txtTitleProtected": "Archivo protegido", "Common.Views.PasswordDialog.txtDescription": "Establezca una contraseña para proteger este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "La contraseña de confirmación no es idéntica", "Common.Views.PasswordDialog.txtPassword": "Contraseña", "Common.Views.PasswordDialog.txtRepeat": "Repita la contraseña", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON> contras<PERSON>", "Common.Views.PasswordDialog.txtWarning": "Precaución: Si pierde u olvida su contraseña, no podrá recuperarla. Guárdalo en un lugar seguro.", "Common.Views.PluginDlg.textLoading": "Cargando", "Common.Views.Plugins.groupCaption": "Extensiones", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Cerrar plugin", "Common.Views.Plugins.textLoading": "Cargando", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "Detener", "Common.Views.Protection.hintAddPwd": "Encriptar con contraseña", "Common.Views.Protection.hintDelPwd": "Eliminar contraseña", "Common.Views.Protection.hintPwd": "Cambie o elimine la contraseña", "Common.Views.Protection.hintSignature": "Agregar firma digital o línea de firma", "Common.Views.Protection.txtAddPwd": "Agregar contraseña", "Common.Views.Protection.txtChangePwd": "Cambiar contraseña", "Common.Views.Protection.txtDeletePwd": "Eliminar contraseña", "Common.Views.Protection.txtEncrypt": "Encriptar", "Common.Views.Protection.txtInvisibleSignature": "Agregar firma digital", "Common.Views.Protection.txtSignature": "Firma", "Common.Views.Protection.txtSignatureLine": "Agregar línea de firma", "Common.Views.RenameDialog.textName": "Nombre de archivo", "Common.Views.RenameDialog.txtInvalidName": "El nombre de archivo no debe contener los símbolos siguientes:", "Common.Views.ReviewChanges.hintNext": "Al siguiente cambio", "Common.Views.ReviewChanges.hintPrev": "Al cambio anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Co-edición a tiempo real. Todos los cambios se guardan de forma automática.", "Common.Views.ReviewChanges.strStrict": "Estricto", "Common.Views.ReviewChanges.strStrictDesc": "Use el botón \"Guardar\" para sincronizar los cambios que o tú u otros habéis realizado", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.tipCoAuthMode": "Establezca el modo de co-edición", "Common.Views.ReviewChanges.tipCommentRem": "Eliminar comentarios", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Eliminar comentarios actuales", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentarios", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver los comentarios actuales", "Common.Views.ReviewChanges.tipHistory": "Mostrar historial de versiones", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON><PERSON> Actual", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON> cambios", "Common.Views.ReviewChanges.tipReviewView": "Seleccionar el modo en el que quiere que se presenten los cambios", "Common.Views.ReviewChanges.tipSetDocLang": "Establecer el idioma de documento", "Common.Views.ReviewChanges.tipSetSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.tipSharing": "Gestionar derechos de acceso al documento", "Common.Views.ReviewChanges.txtAccept": "Aceptar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceptar todos los cambios", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceptar cambios", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de co-edición", "Common.Views.ReviewChanges.txtCommentRemAll": "Eliminar todos los comentarios", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Eliminar comentarios actuales", "Common.Views.ReviewChanges.txtCommentRemMy": "Eliminar mis comentarios", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Eliminar mis actuales comentarios", "Common.Views.ReviewChanges.txtCommentRemove": "Eliminar", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos los comentarios", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentarios actuales", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver mis comentarios", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver mis comentarios actuales", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtFinal": "Todos los cambio aceptados (vista previa)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historial de versiones", "Common.Views.ReviewChanges.txtMarkup": "Todos los cambios (Edición)", "Common.Views.ReviewChanges.txtMarkupCap": "Margen", "Common.Views.ReviewChanges.txtNext": "Siguient<PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Todos los cambios rechazados (Vista previa)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON> todos los cambios", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON><PERSON> Actual", "Common.Views.ReviewChanges.txtSharing": "Compartir", "Common.Views.ReviewChanges.txtSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON> cambios", "Common.Views.ReviewChanges.txtView": "Modo de visualización", "Common.Views.ReviewPopover.textAdd": "Agregar", "Common.Views.ReviewPopover.textAddReply": "Agregar respuesta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Introduzca su comentario aquí", "Common.Views.ReviewPopover.textMention": "+mención proporcionará acceso al documento y enviará un correo", "Common.Views.ReviewPopover.textMentionNotify": "+mención notificará al usuario por correo", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>r de nuevo", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "No tiene permiso para volver a abrir el documento", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Cargando", "Common.Views.SaveAsDlg.textTitle": "Carpeta para guardar", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON><PERSON><PERSON> <PERSON> de minúsculas", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Se ha cambiado el documento", "Common.Views.SearchPanel.textFind": "Buscar", "Common.Views.SearchPanel.textFindAndReplace": "Buscar y reemplazar", "Common.Views.SearchPanel.textMatchUsingRegExp": "Coincidir utilizando expresiones regulares", "Common.Views.SearchPanel.textNoMatches": "No hay coincidencias", "Common.Views.SearchPanel.textNoSearchResults": "No hay resultados de búsqueda", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Common.Views.SearchPanel.textReplaceWith": "<PERSON>em<PERSON><PERSON><PERSON> por", "Common.Views.SearchPanel.textSearchAgain": "{0}Realiza nueva búsqueda{1} para obtener resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "La búsqueda se ha detenido", "Common.Views.SearchPanel.textSearchResults": "Resultados de búsqueda: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Hay demasiados resultados para mostrar aquí", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON> palabras completas", "Common.Views.SearchPanel.tipNextResult": "Resul<PERSON><PERSON> si<PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "Cargando", "Common.Views.SelectFileDlg.textTitle": "Seleccionar origen de datos", "Common.Views.SignDialog.textBold": "Negrita", "Common.Views.SignDialog.textCertificate": "Certificar", "Common.Views.SignDialog.textChange": "Cambiar", "Common.Views.SignDialog.textInputName": "Ingresar nombre de quien firma", "Common.Views.SignDialog.textItalic": "Cursiva", "Common.Views.SignDialog.textNameError": "El nombre del firmante no debe estar vacío.", "Common.Views.SignDialog.textPurpose": "Propósito al firmar este documento", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Seleccionar imagen", "Common.Views.SignDialog.textSignature": "La firma se ve como", "Common.Views.SignDialog.textTitle": "Firmar documento", "Common.Views.SignDialog.textUseImage": "o pulsar 'Seleccionar Imagen' para usar una imagen como firma", "Common.Views.SignDialog.textValid": "Válido desde %1 hasta %2", "Common.Views.SignDialog.tipFontName": "Nombre de fuente", "Common.Views.SignDialog.tipFontSize": "Tamaño de fuente", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir al firmante agregar comentarios en el diálogo de firma", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de firmar este documento, verifique que el contenido que está firmando es correcto.", "Common.Views.SignSettingsDialog.textInfoEmail": "Correo electrónico del firmante sugerido", "Common.Views.SignSettingsDialog.textInfoName": "Firmante sugerido", "Common.Views.SignSettingsDialog.textInfoTitle": "Título del firmante sugerido", "Common.Views.SignSettingsDialog.textInstructions": "Instrucciones para quien firma", "Common.Views.SignSettingsDialog.textShowDate": "Presentar fecha de la firma", "Common.Views.SignSettingsDialog.textTitle": "Configuración de firma", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo es obligatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valor HEX de Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Signo de Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON> do<PERSON> de cierre", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON> do<PERSON> de apertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipsis horizontal", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON> corto", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON><PERSON><PERSON> corto", "Common.Views.SymbolTableDialog.textFont": "Letra ", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON> sin ruptura", "Common.Views.SymbolTableDialog.textNBSpace": "Espacio de no separación", "Common.Views.SymbolTableDialog.textPilcrow": "Signo de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 <PERSON><PERSON>ac<PERSON>", "Common.Views.SymbolTableDialog.textRange": "Ra<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Símbolos utilizados recientemente", "Common.Views.SymbolTableDialog.textRegistered": "Signo de marca registrada", "Common.Views.SymbolTableDialog.textSCQuote": "Comillas simples de cierre", "Common.Views.SymbolTableDialog.textSection": "Signo de sección", "Common.Views.SymbolTableDialog.textShortcut": "Tecla de método abreviado", "Common.Views.SymbolTableDialog.textSHyphen": "G<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Comillas simples de apertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiales", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca comercial", "Common.Views.UserNameDialog.textDontShow": "No volver a preguntarme", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "La etiqueta no debe estar vacía.", "PE.Controllers.LeftMenu.leavePageText": "Todos los cambios no guardados de este documento se perderán.<br> Pulse \"Cancelar\" después \"Guardar\" para guardarlos. Pulse \"OK\" para deshacer todos los cambios no guardados.", "PE.Controllers.LeftMenu.newDocumentTitle": "Presentación sin nombre", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Aviso", "PE.Controllers.LeftMenu.requestEditRightsText": "Solicitando derechos de edición...", "PE.Controllers.LeftMenu.textLoadHistory": "Cargando historial de versiones...", "PE.Controllers.LeftMenu.textNoTextFound": "No se puede encontrar los datos que usted busca. Por favor, ajuste los parámetros de búsqueda.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Se ha realizado el reemplazo. {0} ocurrencias fueron saltadas.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Se ha realizado la búsqueda. Ocurrencias reemplazadas: {0}", "PE.Controllers.LeftMenu.txtUntitled": "Sin título", "PE.Controllers.Main.applyChangesTextText": "Cargando datos...", "PE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.confirmMaxChangesSize": "El tamaño de las acciones excede la limitación establecida para su servidor.<br><PERSON><PERSON> \"Deshacer\" para cancelar su última acción o pulse \"Continuar\" para mantener la acción localmente (debe descargar el archivo o copiar su contenido para asegurarse de que no se pierde nada).", "PE.Controllers.Main.convertationTimeoutText": "Tiempo de conversión está superado.", "PE.Controllers.Main.criticalErrorExtText": "Pulse \"OK\" para regresar a la lista de documentos.", "PE.Controllers.Main.criticalErrorTitle": "Error", "PE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> <PERSON>.", "PE.Controllers.Main.downloadTextText": "Descargando presentación...", "PE.Controllers.Main.downloadTitleText": "Descargando presentación", "PE.Controllers.Main.errorAccessDeny": "Usted no tiene permisos para realizar la acción que está intentando hacer.<br> <PERSON>r favor, contacte con el Administrador del Servidor de Documentos.", "PE.Controllers.Main.errorBadImageUrl": "URL de imagen es incorrecto", "PE.Controllers.Main.errorCannotPasteImg": "No podemos pegar esta imagen desde el Portapapeles, pero puede guardarla en su dispositivo e \ninsertarla desde allí, o puede copiar la imagen sin texto y pegarla en la presentación.", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Se ha perdido la conexión con servidor. El documento no puede ser editado ahora.", "PE.Controllers.Main.errorComboSeries": "Para crear un gráfico combinado, seleccione al menos dos series de datos.", "PE.Controllers.Main.errorConnectToServer": "No se consiguió guardar el documento. Por favor, compruebe los ajustes de conexión o póngase en contacto con su administrador.<br>Al hacer clic en el botón 'OK' se le solicitará que descargue el documento.", "PE.Controllers.Main.errorDatabaseConnection": "Error externo.<br>Error de conexión de base de datos. Por favor póngase en contacto con soporte si el error se mantiene.", "PE.Controllers.Main.errorDataEncrypted": "Se han recibido cambios cifrados, ellos no pueden ser descifrados.", "PE.Controllers.Main.errorDataRange": "<PERSON><PERSON>.", "PE.Controllers.Main.errorDefaultMessage": "Código de error: %1", "PE.Controllers.Main.errorDirectUrl": "Por favor, verifique el vínculo al documento.<br><PERSON><PERSON> vínculo debe ser un vínculo directo al archivo para descargar.", "PE.Controllers.Main.errorEditingDownloadas": "Se produjo un error durante el trabajo con el documento.<br>Use la opción 'Descargar como' para guardar la copia de seguridad de este archivo en el disco duro.", "PE.Controllers.Main.errorEditingSaveas": "Se produjo un error durante el trabajo con el documento.<br>Use la opción 'Guardar como...' para guardar la copia de seguridad de este archivo en el disco duro.", "PE.Controllers.Main.errorEmailClient": "No se pudo encontrar ningun cliente de correo", "PE.Controllers.Main.errorFilePassProtect": "El archivo está protegido por una contraseña y no puede ser abierto.", "PE.Controllers.Main.errorFileSizeExceed": "El tamaño del archivo supera la configuración del servidor.<br>Póngase en contacto con el administrador del servidor para obtener más detalles. ", "PE.Controllers.Main.errorForceSave": "Se produjo un error al guardar el archivo. Utilice la opción \"Descargar como\" para guardar el archivo en el disco duro o inténtelo de nuevo más tarde.", "PE.Controllers.Main.errorInconsistentExt": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo no coincide con la extensión del mismo.", "PE.Controllers.Main.errorInconsistentExtDocx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a documentos de texto (por ejemplo, docx), pero el archivo tiene extensión inconsistente: %1.", "PE.Controllers.Main.errorInconsistentExtPdf": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a uno de los siguientes formatos: pdf/djvu/xps/oxps, pero el archivo tiene extensión inconsistente: %1.", "PE.Controllers.Main.errorInconsistentExtPptx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a presentaciones (por ejemplo, pptx), pero el archivo tiene extensión inconsistente: %1.", "PE.Controllers.Main.errorInconsistentExtXlsx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a hojas de cálculo (por ejemplo, xlsx), pero el archivo tiene extensión inconsistente: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Descriptor de clave desconocido", "PE.Controllers.Main.errorKeyExpire": "Descriptor de clave ha expirado", "PE.Controllers.Main.errorLoadingFont": "Las fuentes no están cargadas.<br><PERSON><PERSON> <PERSON>, póngase en contacto con el administrador del Document Server.", "PE.Controllers.Main.errorProcessSaveResult": "Problemas al guardar", "PE.Controllers.Main.errorServerVersion": "La versión del editor ha sido actualizada. La página será recargada para aplicar los cambios.", "PE.Controllers.Main.errorSessionAbsolute": "Sesión de editar el documento ha expirado. Por favor, recargue la página.", "PE.Controllers.Main.errorSessionIdle": "El documento no ha sido editado durante bastante tiempo. Por favor, recargue la página.", "PE.Controllers.Main.errorSessionToken": "Conexión al servidor ha sido interrumpido. Por favor, recargue la página.", "PE.Controllers.Main.errorSetPassword": "No se pudo establecer la contraseña.", "PE.Controllers.Main.errorStockChart": "Orden de las filas incorrecto. Para crear un gráfico de cotizaciones introduzca los datos en la hoja de tal modo:<br> precio de apertura, precio máxi<PERSON>, precio mín<PERSON>, precio de cierre.", "PE.Controllers.Main.errorToken": "El token de seguridad de documento tiene un formato incorrecto.<br>Por favor, contacte con el Administrador del Servidor de Documentos.", "PE.Controllers.Main.errorTokenExpire": "El token de seguridad de documento ha sido expirado.<br><PERSON>r favor, contacte con el Administrador del Servidor de Documentos.", "PE.Controllers.Main.errorUpdateVersion": "Se ha cambiado la versión del archivo. La página será actualizada.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Se ha restablecido la conexión a Internet y se ha cambiado la versión del archivo. <br>Para poder seguir trabajando, es necesario descargar el archivo o copiar su contenido para asegurarse de que no se ha perdido nada, y luego volver a cargar esta página.", "PE.Controllers.Main.errorUserDrop": "No se puede acceder al archivo ahora.", "PE.Controllers.Main.errorUsersExceed": "El número de usuarios permitido según su plan de precios fue excedido", "PE.Controllers.Main.errorViewerDisconnect": "Se ha perdido la conexión. Usted todavía puede visualizar el documento,<br>pero no puede descargar o imprimirlo hasta que la conexión sea restaurada y la página esté recargada.", "PE.Controllers.Main.leavePageText": "Hay cambios no guardados en esta presentación. Pulse \"Permanecer en esta página\", después \"Guardar\" para guardadarlos. Pulse \"Abandonar esta página\" para descartar todos los cambios no guardados.", "PE.Controllers.Main.leavePageTextOnClose": "Todos los cambios no guardados de esta presentación se perderán.<br> Pulse \"Cancelar\" después \"Guardar\" para guardarlos. Pulse \"OK\" para deshacer todos los cambios no guardados.", "PE.Controllers.Main.loadFontsTextText": "Cargando datos...", "PE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadFontTextText": "Cargando datos...", "PE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadImagesTextText": "Cargando imágenes...", "PE.Controllers.Main.loadImagesTitleText": "<PERSON>gan<PERSON>", "PE.Controllers.Main.loadImageTextText": "Cargando imagen...", "PE.Controllers.Main.loadImageTitleText": "Cargando imagen", "PE.Controllers.Main.loadingDocumentTextText": "Cargando presentación...", "PE.Controllers.Main.loadingDocumentTitleText": "Cargando presentación", "PE.Controllers.Main.loadThemeTextText": "Cargando tema...", "PE.Controllers.Main.loadThemeTitleText": "Cargando tema", "PE.Controllers.Main.notcriticalErrorTitle": "Aviso", "PE.Controllers.Main.openErrorText": "Se ha producido un error al abrir el archivo ", "PE.Controllers.Main.openTextText": "Abriendo presentación...", "PE.Controllers.Main.openTitleText": "Abriendo presentación", "PE.Controllers.Main.printTextText": "Imprimiendo presentación...", "PE.Controllers.Main.printTitleText": "Imprimiendo presentación", "PE.Controllers.Main.reloadButtonText": "<PERSON>ar<PERSON> p<PERSON>a", "PE.Controllers.Main.requestEditFailedMessageText": "Alguien está editando esta presentación ahora. Por favor inténtelo de nuevo más tarde.", "PE.Controllers.Main.requestEditFailedTitleText": "Acceso negado", "PE.Controllers.Main.saveErrorText": "Se ha producido un error al guardar el archivo ", "PE.Controllers.Main.saveErrorTextDesktop": "Este archivo no se puede guardar o crear.<br>Las razones posibles son: <br>1. El archivo es sólo para leer. <br>2. El archivo está siendo editado por otros usuarios. <br>3. El disco está lleno o corrupto.", "PE.Controllers.Main.saveTextText": "Guardando presentación...", "PE.Controllers.Main.saveTitleText": "Guardando presentación", "PE.Controllers.Main.scriptLoadError": "La conexión a Internet es demasiado lenta, no se podía cargar algunos componentes. Por favor, recargue la página.", "PE.Controllers.Main.splitDividerErrorText": "El número de filas debe ser un divisor de %1.", "PE.Controllers.Main.splitMaxColsErrorText": "El número de columnas debe ser menos que %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "El número de filas debe ser menos que %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "Aplicar a todas las ecuaciones", "PE.Controllers.Main.textBuyNow": "Visitar sitio web", "PE.Controllers.Main.textChangesSaved": "Todos los cambios son guardados", "PE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Pulse para cerrar el consejo", "PE.Controllers.Main.textContactUs": "Contactar con equipo de ventas", "PE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textConvertEquation": "Esta ecuación fue creada con una versión antigua del editor de ecuaciones que ya no es compatible. Para editarla, convierta la ecuación al formato ML de Office Math.<br>¿Convertir ahora?", "PE.Controllers.Main.textCustomLoader": "Note, por favor, que según los términos de la licencia Usted no tiene derecho a cambiar el cargador.<br>Por favor, póngase en contacto con nuestro Departamento de Ventas para obtener una cotización.", "PE.Controllers.Main.textDisconnect": "Se ha perdido la conexión", "PE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "El archivo contiene macros automáticas.<br>¿Quiere ejecutar macros?", "PE.Controllers.Main.textLearnMore": "Más información", "PE.Controllers.Main.textLoadingDocument": "Cargando presentación", "PE.Controllers.Main.textLongName": "Escriba un nombre que tenga menos de 128 caracteres.", "PE.Controllers.Main.textNoLicenseTitle": "Se ha alcanzado el límite de licencias", "PE.Controllers.Main.textPaidFeature": "Función de pago", "PE.Controllers.Main.textReconnect": "Se ha restablecido la conexión", "PE.Controllers.Main.textRemember": "Recordar mi elección para todos los archivos", "PE.Controllers.Main.textRememberMacros": "Recordar mi elección para todas las macros", "PE.Controllers.Main.textRenameError": "El nombre de usuario no debe estar vacío.", "PE.Controllers.Main.textRenameLabel": "Escriba un nombre que se utilizará para la colaboración", "PE.Controllers.Main.textRequestMacros": "Una macro realiza una solicitud a la URL. ¿Quiere permitir la solicitud al %1?", "PE.Controllers.Main.textShape": "Forma", "PE.Controllers.Main.textStrict": "<PERSON><PERSON> estricto", "PE.Controllers.Main.textText": "Texto", "PE.Controllers.Main.textTryQuickPrint": "Ha seleccionado Impresión rápida: todo el documento se imprimirá en la última impresora seleccionada o predeterminada.<br>¿Desea continuar?", "PE.Controllers.Main.textTryUndoRedo": "Las funciones Anular/<PERSON><PERSON>cer se desactivan para el modo co-edición rápido.<br><PERSON><PERSON> en el botón \"modo estricto\" para cambiar al modo de co-edición estricta para editar el archivo sin la interferencia de otros usuarios y enviar sus cambios sólo después de guardarlos. Se puede cambiar entre los modos de co-edición usando los ajustes avanzados de edición.", "PE.Controllers.Main.textTryUndoRedoWarn": "Las funciones Deshacer/<PERSON><PERSON><PERSON> están desactivadas en el modo de co-edición rápido.", "PE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.titleLicenseExp": "Licencia ha expirado", "PE.Controllers.Main.titleServerVersion": "Editor ha sido actualizado", "PE.Controllers.Main.txtAddFirstSlide": "Haga clic para agregar la primera diapositiva", "PE.Controllers.Main.txtAddNotes": "Haga clic para agregar notas", "PE.Controllers.Main.txtArt": "Su texto aquí", "PE.Controllers.Main.txtBasicShapes": "Formas básicas", "PE.Controllers.Main.txtButtons": "Botones", "PE.Controllers.Main.txtCallouts": "Llamadas", "PE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "<PERSON><PERSON> y hora", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Título del gráfico", "PE.Controllers.Main.txtEditingMode": "Establecer el modo de edición...", "PE.Controllers.Main.txtErrorLoadHistory": "Error al cargar el historial", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON> figu<PERSON>", "PE.Controllers.Main.txtFooter": "Pie de página", "PE.Controllers.Main.txtHeader": "Encabezado", "PE.Controllers.Main.txtImage": "Imagen", "PE.Controllers.Main.txtLines": "Líneas", "PE.Controllers.Main.txtLoading": "Cargando...", "PE.Controllers.Main.txtMath": "Matemáticas", "PE.Controllers.Main.txtMedia": "Medios", "PE.Controllers.Main.txtNeedSynchronize": "Usted tiene actualizaciones", "PE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtPicture": "Imagen", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSeries": "Serie", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Llamada con línea 1 (borde y barra de énfasis)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Llamada con línea 2 (borde y barra de é<PERSON>fa<PERSON>)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Llamada con línea 3 (borde y barra de énfa<PERSON>)", "PE.Controllers.Main.txtShape_accentCallout1": "Llamada con línea 1 (barra de <PERSON>)", "PE.Controllers.Main.txtShape_accentCallout2": "Llamada con línea 2 (bar<PERSON> <PERSON>)", "PE.Controllers.Main.txtShape_accentCallout3": "Llamada con línea 3 (barra <PERSON>)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Atrás o Botón Anterior", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Botón Al inicio", "PE.Controllers.Main.txtShape_actionButtonBlank": "Botón en blanco", "PE.Controllers.Main.txtShape_actionButtonDocument": "Botón Documento", "PE.Controllers.Main.txtShape_actionButtonEnd": "Botón Al fin", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Adelante o Botón Siguiente", "PE.Controllers.Main.txtShape_actionButtonHelp": "Bo<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonHome": "Botón Inicio", "PE.Controllers.Main.txtShape_actionButtonInformation": "Botón Información", "PE.Controllers.Main.txtShape_actionButtonMovie": "Botón Vídeo ", "PE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonSound": "Botón Sonido", "PE.Controllers.Main.txtShape_arc": "Arco", "PE.Controllers.Main.txtShape_bentArrow": "Flecha do<PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector angular de flecha", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector angular de flecha doble", "PE.Controllers.Main.txtShape_bentUpArrow": "Flecha doblada hacia arriba", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Arco de bloque", "PE.Controllers.Main.txtShape_borderCallout1": "Llamada con línea 1", "PE.Controllers.Main.txtShape_borderCallout2": "Llamada con línea 2", "PE.Controllers.Main.txtShape_borderCallout3": "Llamada con línea 3", "PE.Controllers.Main.txtShape_bracePair": "Llaves", "PE.Controllers.Main.txtShape_callout1": "Llamada con línea 1 (sin borde)", "PE.Controllers.Main.txtShape_callout2": "Llamada con línea 2 (sin borde)", "PE.Controllers.Main.txtShape_callout3": "Llamada con línea 3 (sin borde)", "PE.Controllers.Main.txtShape_can": "Сilindro", "PE.Controllers.Main.txtShape_chevron": "Cheurón", "PE.Controllers.Main.txtShape_chord": "Acorde", "PE.Controllers.Main.txtShape_circularArrow": "Flecha circular", "PE.Controllers.Main.txtShape_cloud": "Nube", "PE.Controllers.Main.txtShape_cloudCallout": "Llamada de nube", "PE.Controllers.Main.txtShape_corner": "Esquina", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector curvado de flecha", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector curvado de flecha doble", "PE.Controllers.Main.txtShape_curvedDownArrow": "Flecha curvada hacia abajo", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Flecha curvada hacia la izquierda", "PE.Controllers.Main.txtShape_curvedRightArrow": "Flecha curvada hacia la derecha", "PE.Controllers.Main.txtShape_curvedUpArrow": "Flecha curvada hacia arriba", "PE.Controllers.Main.txtShape_decagon": "Decágono", "PE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "PE.Controllers.Main.txtShape_diamond": "Rombo", "PE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "PE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "Doble onda", "PE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>cha a<PERSON>", "PE.Controllers.Main.txtShape_downArrowCallout": "Llamada de flecha hacia abajo", "PE.Controllers.Main.txtShape_ellipse": "Elipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "Cinta curvada hacia abajo", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Cinta curvada hacia arriba", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Proceso alternativo", "PE.Controllers.Main.txtShape_flowChartCollate": "Intercalar", "PE.Controllers.Main.txtShape_flowChartConnector": "Conector", "PE.Controllers.Main.txtShape_flowChartDecision": "Decisión", "PE.Controllers.Main.txtShape_flowChartDelay": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDisplay": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDocument": "Documento", "PE.Controllers.Main.txtShape_flowChartExtract": "Extracto", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Datos", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Almacenamiento interno", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Disco magnético", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Almacenamiento de acceso directo", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Almacenamiento de acceso secuencial", "PE.Controllers.Main.txtShape_flowChartManualInput": "Entrada manual", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Operación manual", "PE.Controllers.Main.txtShape_flowChartMerge": "Combinar", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Multidocumento", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Conector fuera de <PERSON>a", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Datos almacenados", "PE.Controllers.Main.txtShape_flowChartOr": "Diagrama de flujo: O", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Proceso predefinido", "PE.Controllers.Main.txtShape_flowChartPreparation": "Preparación", "PE.Controllers.Main.txtShape_flowChartProcess": "Proceso", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Tarjeta", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Cinta perforada", "PE.Controllers.Main.txtShape_flowChartSort": "Ordenar", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Y", "PE.Controllers.Main.txtShape_flowChartTerminator": "Terminador", "PE.Controllers.Main.txtShape_foldedCorner": "Esquina doblada", "PE.Controllers.Main.txtShape_frame": "<PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "Medio marco", "PE.Controllers.Main.txtShape_heart": "Corazón", "PE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_hexagon": "Hexágono", "PE.Controllers.Main.txtShape_homePlate": "Pentágono", "PE.Controllers.Main.txtShape_horizontalScroll": "Pergamino horizontal", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosión 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosión 2", "PE.Controllers.Main.txtShape_leftArrow": "Flecha izquierda", "PE.Controllers.Main.txtShape_leftArrowCallout": "Llamada de flecha a la izquierda", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>r llave", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON> corchete", "PE.Controllers.Main.txtShape_leftRightArrow": "Flecha izquierda y derecha", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Llamada de flecha izquierda y derecha", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Flecha izquierda, derecha y arriba", "PE.Controllers.Main.txtShape_leftUpArrow": "Flecha izquierda y arriba", "PE.Controllers.Main.txtShape_lightningBolt": "Rayo", "PE.Controllers.Main.txtShape_line": "Lín<PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "Fle<PERSON>", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> doble", "PE.Controllers.Main.txtShape_mathDivide": "División", "PE.Controllers.Main.txtShape_mathEqual": "Igual", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "PE.Controllers.Main.txtShape_mathNotEqual": "No igual", "PE.Controllers.Main.txtShape_mathPlus": "Más", "PE.Controllers.Main.txtShape_moon": "Luna", "PE.Controllers.Main.txtShape_noSmoking": "Señal de prohibición", "PE.Controllers.Main.txtShape_notchedRightArrow": "Flecha a la derecha con muesca", "PE.Controllers.Main.txtShape_octagon": "Octágono", "PE.Controllers.Main.txtShape_parallelogram": "Paralelogramo", "PE.Controllers.Main.txtShape_pentagon": "Pentágono", "PE.Controllers.Main.txtShape_pie": "Sector del círculo", "PE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plus": "Más", "PE.Controllers.Main.txtShape_polyline1": "A mano alzada", "PE.Controllers.Main.txtShape_polyline2": "Forma libre", "PE.Controllers.Main.txtShape_quadArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_quadArrowCallout": "Llamada de flecha cu<PERSON>", "PE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "<PERSON>inta hacia abajo", "PE.Controllers.Main.txtShape_ribbon2": "Cinta hacia arriba", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> derecha", "PE.Controllers.Main.txtShape_rightArrowCallout": "Llamada de flecha a la derecha", "PE.Controllers.Main.txtShape_rightBrace": "Ce<PERSON>r llave", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON> corchete", "PE.Controllers.Main.txtShape_round1Rect": "Redondear rectángulo de esquina sencilla", "PE.Controllers.Main.txtShape_round2DiagRect": "Redondear rectángulo de esquina diagonal", "PE.Controllers.Main.txtShape_round2SameRect": "Redondear rectángulo de esquina del mismo lado", "PE.Controllers.Main.txtShape_roundRect": "Rectángulo con esquinas redondeadas", "PE.Controllers.Main.txtShape_rtTriangle": "Triángulo rectángulo", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Recortar rectángulo de esquina sencilla", "PE.Controllers.Main.txtShape_snip2DiagRect": "Recortar rectángulo de esquina diagonal", "PE.Controllers.Main.txtShape_snip2SameRect": "Recortar rectángulo de esquina del mismo lado", "PE.Controllers.Main.txtShape_snipRoundRect": "Recortar y redondear rectángulo de esquina sencilla", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "Estrella de 10 puntas", "PE.Controllers.Main.txtShape_star12": "Estrella de 12 puntas", "PE.Controllers.Main.txtShape_star16": "Estrella de 16 puntas", "PE.Controllers.Main.txtShape_star24": "Estrella de 24 puntas", "PE.Controllers.Main.txtShape_star32": "Estrella de 32 puntas", "PE.Controllers.Main.txtShape_star4": "Estrella de 4 puntas", "PE.Controllers.Main.txtShape_star5": "Estrella de 5 puntas", "PE.Controllers.Main.txtShape_star6": "Estrella de 6 puntas", "PE.Controllers.Main.txtShape_star7": "Estrella de 7 puntas", "PE.Controllers.Main.txtShape_star8": "Estrella de 8 puntas", "PE.Controllers.Main.txtShape_stripedRightArrow": "Flecha a la derecha con bandas", "PE.Controllers.Main.txtShape_sun": "Sol", "PE.Controllers.Main.txtShape_teardrop": "Lágrima", "PE.Controllers.Main.txtShape_textRect": "Cuadro de texto", "PE.Controllers.Main.txtShape_trapezoid": "Trapecio", "PE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upArrow": "Flecha hacia arriba", "PE.Controllers.Main.txtShape_upArrowCallout": "Llamada de flecha hacia arriba", "PE.Controllers.Main.txtShape_upDownArrow": "Flecha hacia arriba y abajo", "PE.Controllers.Main.txtShape_uturnArrow": "Flecha en U", "PE.Controllers.Main.txtShape_verticalScroll": "Pergamino vertical", "PE.Controllers.Main.txtShape_wave": "On<PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Llamada ovalada", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Llamada rectangular", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Llamada rectangular redondeada", "PE.Controllers.Main.txtSldLtTBlank": "En blanco", "PE.Controllers.Main.txtSldLtTChart": "Gráfico", "PE.Controllers.Main.txtSldLtTChartAndTx": "Gráfico y texto", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip <PERSON> y texto", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art y texto vertical", "PE.Controllers.Main.txtSldLtTCust": "Personalizado", "PE.Controllers.Main.txtSldLtTDgm": "Diagrama", "PE.Controllers.Main.txtSldLtTFourObj": "Cuatro objetos", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Multimedia y texto", "PE.Controllers.Main.txtSldLtTObj": "Tí<PERSON>lo y objeto", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objeto y dos objetos", "PE.Controllers.Main.txtSldLtTObjAndTx": "Objeto y texto", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objeto encima de texto", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON><PERSON>, objeto, y leyenda", "PE.Controllers.Main.txtSldLtTPicTx": "Imagen y leyenda", "PE.Controllers.Main.txtSldLtTSecHead": "Encabezado de sección", "PE.Controllers.Main.txtSldLtTTbl": "Tabla", "PE.Controllers.Main.txtSldLtTTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Texto a dos columnas", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON><PERSON> objet<PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Dos objetos y objeto", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Dos objetos y texto", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Dos objetos encima de texto", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Dos textos y dos objetos", "PE.Controllers.Main.txtSldLtTTx": "Texto", "PE.Controllers.Main.txtSldLtTTxAndChart": "Texto y gráfico", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Texto y Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Texto y multimedia", "PE.Controllers.Main.txtSldLtTTxAndObj": "Texto y objeto", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Texto y dos objetos", "PE.Controllers.Main.txtSldLtTTxOverObj": "Texto encima de objeto", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Título vertical y texto", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Título vertical y texto encima de gráfico", "PE.Controllers.Main.txtSldLtTVertTx": "Texto vertical", "PE.Controllers.Main.txtSlideNumber": "Número de diapositiva", "PE.Controllers.Main.txtSlideSubtitle": "Subtítulo de diapositiva", "PE.Controllers.Main.txtSlideText": "Texto de diapositiva", "PE.Controllers.Main.txtSlideTitle": "Tí<PERSON>lo de diapositiva", "PE.Controllers.Main.txtStarsRibbons": "Cintas y estrellas", "PE.Controllers.Main.txtTheme_basic": "Básico", "PE.Controllers.Main.txtTheme_blank": "En blanco", "PE.Controllers.Main.txtTheme_classic": "Clásico", "PE.Controllers.Main.txtTheme_corner": "De esquina", "PE.Controllers.Main.txtTheme_dotted": "<PERSON> <PERSON>", "PE.Controllers.Main.txtTheme_green": "Verde", "PE.Controllers.Main.txtTheme_green_leaf": "Hoja verde", "PE.Controllers.Main.txtTheme_lines": "Líneas", "PE.Controllers.Main.txtTheme_office": "Oficina", "PE.Controllers.Main.txtTheme_office_theme": "Tema de Office", "PE.Controllers.Main.txtTheme_official": "Oficial", "PE.Controllers.Main.txtTheme_pixel": "<PERSON>", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Tortuga", "PE.Controllers.Main.txtXAxis": "<PERSON><PERSON>", "PE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "PE.Controllers.Main.unknownErrorText": "Error des<PERSON>.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Su navegador no está soportado.", "PE.Controllers.Main.uploadImageExtMessage": "Formato de imagen desconocido.", "PE.Controllers.Main.uploadImageFileCountMessage": "No hay imágenes subidas.", "PE.Controllers.Main.uploadImageSizeMessage": "La imagen es demasiado grande. El tamaño máximo es de 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Subiendo imagen...", "PE.Controllers.Main.uploadImageTitleText": "Subiendo imagen", "PE.Controllers.Main.waitText": "Por favor, espere...", "PE.Controllers.Main.warnBrowserIE9": "Este aplicación tiene baja capacidad en IE9. Utilice IE10 o superior", "PE.Controllers.Main.warnBrowserZoom": "La configuración actual de zoom de su navegador no está soportada por completo. Por favor restablezca zoom predeterminado pulsando Ctrl+0.", "PE.Controllers.Main.warnLicenseExceeded": "Usted ha alcanzado el límite de conexiones simultáneas con %1 editores. Este documento se abrirá sólo para su visualización.<br>Por favor, contacte con su administrador para recibir más información.", "PE.Controllers.Main.warnLicenseExp": "Su licencia ha expirado.<br>Por favor, actualice su licencia y después recargue la página.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licencia expirada.<br>No tiene acceso a la funcionalidad de edición de documentos.<br>Por favor, póngase en contacto con su administrador.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "La licencia requiere ser renovada.<br>Tiene un acceso limitado a la funcionalidad de edición de documentos.<br>Por favor, póngase en contacto con su administrador para obtener un acceso completo", "PE.Controllers.Main.warnLicenseUsersExceeded": "Usted ha alcanzado el límite de usuarios para los editores de %1. <PERSON><PERSON> favor, contacte con su administrador para recibir más información.", "PE.Controllers.Main.warnNoLicense": "Usted ha alcanzado el límite de conexiones simultáneas con %1 editores. Este documento se abrirá sólo para su visualización.<br>Contacte con el equipo de ventas de %1 para conocer los términos de actualización personal.", "PE.Controllers.Main.warnNoLicenseUsers": "Usted ha alcanzado el límite de usuarios para los editores de %1. Contacte con el equipo de ventas de %1 para conocer los términos de actualización personal.", "PE.Controllers.Main.warnProcessRightsChange": "El derecho de edición del archivo es denegado.", "PE.Controllers.Print.txtPrintRangeInvalid": "Intervalo de impresión no válido", "PE.Controllers.Print.txtPrintRangeSingleRange": "Introduzca un único número de diapositiva o un único intervalo de diapositivas (por ejemplo, 5-12). O puede imprimir en PDF.", "PE.Controllers.Search.notcriticalErrorTitle": "Advertencia", "PE.Controllers.Search.textNoTextFound": "No se puede encontrar los datos que usted busca. Por favor, ajuste los parámetros de búsqueda.", "PE.Controllers.Search.textReplaceSkipped": "Se ha realizado la sustitución. Se han omitido {0} ocurrencias.", "PE.Controllers.Search.textReplaceSuccess": "Se realizó la búsqueda. {0} ocurrencias se sustituyeron", "PE.Controllers.Search.warnReplaceString": "{0} no es un carácter especial válido para la casilla Reemplazar por.", "PE.Controllers.Statusbar.textDisconnect": "<b>Se ha perdido la conexión</b><br>Intentando conectar. Compruebe la configuración de la conexión.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "El tipo de letra que usted va a guardar no está disponible en este dispositivo.<br>El estilo de letra se mostrará usando uno de los tipos de letra del dispositivo, el tipo de letra guardado va a usarse cuando esté disponible.<br>¿Desea continuar?", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textBracket": "Corchetes", "PE.Controllers.Toolbar.textEmptyImgUrl": "Hay que especificar URL de imagen", "PE.Controllers.Toolbar.textFontSizeErr": "El valor introducido es incorrecto.<br>Por favor, introduzca un valor numérico entre 1 y 300", "PE.Controllers.Toolbar.textFraction": "Fracciones", "PE.Controllers.Toolbar.textFunction": "Funciones", "PE.Controllers.Toolbar.textInsert": "Insertar", "PE.Controllers.Toolbar.textIntegral": "Integrales", "PE.Controllers.Toolbar.textLargeOperator": "Operadores grandes", "PE.Controllers.Toolbar.textLimitAndLog": "Límites y logaritmos ", "PE.Controllers.Toolbar.textMatrix": "Matrices", "PE.Controllers.Toolbar.textOperator": "Operadores", "PE.Controllers.Toolbar.textRadical": "Radicales", "PE.Controllers.Toolbar.textScript": "Índices", "PE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textWarning": "Aviso", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Flecha superior derecha e izquierda", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Flecha superior izquierda", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Flecha superior derecha", "PE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Barra subyacente", "PE.Controllers.Toolbar.txtAccent_BarTop": "Barra superpuesta", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Fórmula encuadrada (con marcador de posición)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Fórmula en<PERSON> (ejemplo)", "PE.Controllers.Toolbar.txtAccent_Check": "Comprobar", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Llave subyacente", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Llave superpuesta", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC con barra superpuesta", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y con barra superpuesta", "PE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON> puntos", "PE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON> puntos", "PE.Controllers.Toolbar.txtAccent_Dot": "Punt<PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra doble superpuesta", "PE.Controllers.Toolbar.txtAccent_Grave": "Acento grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Carácter de agrupación inferior", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Carácter de agrupación superior", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpón superior hacia la izquierda", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpón superior hacia la derecha", "PE.Controllers.Toolbar.txtAccent_Hat": "Circunfle<PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "Acento breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "Corchetes angulares", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Corchetes angulares con separador", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Corchetes angulares con dos separadores", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Corchete angular de cierre", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Corchete angular de apertura", "PE.Controllers.Toolbar.txtBracket_Curve": "Llaves", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Llaves con separador", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Llave de cierre", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Llave de apertura", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (dos condiciones)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (tres condiciones)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Objeto de pila", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Objeto acotado entre paréntesis", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Ejemplo de casos", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente de binomio", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binomial en corchetes angulares", "PE.Controllers.Toolbar.txtBracket_Line": "Plecas", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Pleca de cierre", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Pleca de apertura", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON> dobles", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Pleca doble de cierre", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Pleca doble de apertura", "PE.Controllers.Toolbar.txtBracket_LowLim": "Corchete inferior", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Corchete inferior de cierre", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Corchete inferior de apertura", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Paréntesis con separador", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Paréntesis de cierre", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Paréntesis de apertura", "PE.Controllers.Toolbar.txtBracket_Square": "Corchetes", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Marcador de posición entre dos corchetes de cierre", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Corchetes invertidos", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Corchete de cierre", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Corchete de apertura", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Marcador de posición entre dos corchetes de apertura", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "Corchetes dobles", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Corchete doble de cierre", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Corchete doble de apertura", "PE.Controllers.Toolbar.txtBracket_UppLim": "Corchete de techo", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Corchete de techo de cierre", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Corchete de techo de apertura", "PE.Controllers.Toolbar.txtFractionDiagonal": "Fracción sesgada", "PE.Controllers.Toolbar.txtFractionDifferential_1": "dx sobre dy", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Delta mayúscula y sobre delta mayúscula x", "PE.Controllers.Toolbar.txtFractionDifferential_3": "y parcial sobre x parcial", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Delta y sobre delta x", "PE.Controllers.Toolbar.txtFractionHorizontal": "Fracción lineal", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi dividir a 2", "PE.Controllers.Toolbar.txtFractionSmall": "Fracción pequeña", "PE.Controllers.Toolbar.txtFractionVertical": "Fracción apilada", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Función de coseno inversa", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Función de coseno inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Función de cotangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Función de cotangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Función de cosecante inversa", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Función de cosecante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Función de secante inversa", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Función de secante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Función de seno inversa", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Función de seno inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Función de tangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Función de tangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_Cos": "Función de coseno", "PE.Controllers.Toolbar.txtFunction_Cosh": "Función de coseno hiperbólica", "PE.Controllers.Toolbar.txtFunction_Cot": "Función de cotangente", "PE.Controllers.Toolbar.txtFunction_Coth": "Función de cotangente hiperbólica", "PE.Controllers.Toolbar.txtFunction_Csc": "Función de cosecante", "PE.Controllers.Toolbar.txtFunction_Csch": "Función de cosecante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Seno zeta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Fórmula de tangente", "PE.Controllers.Toolbar.txtFunction_Sec": "Función de secante", "PE.Controllers.Toolbar.txtFunction_Sech": "Función de secante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Sin": "Función de seno", "PE.Controllers.Toolbar.txtFunction_Sinh": "Función de seno hiperbólica", "PE.Controllers.Toolbar.txtFunction_Tan": "Función de tangente", "PE.Controllers.Toolbar.txtFunction_Tanh": "Función de tangente hiperbólica", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Diferencial zeta", "PE.Controllers.Toolbar.txtIntegral_dx": "Diferencial x", "PE.Controllers.Toolbar.txtIntegral_dy": "Diferencial y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral con límites acotados", "PE.Controllers.Toolbar.txtIntegralDouble": "Integral doble", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral doble con límites acotados", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral doble con límites", "PE.Controllers.Toolbar.txtIntegralOriented": "Integral de contorno", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno con límites acotados", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de superficie", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superficie con límites acotados", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superficie con límites", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de contorno con límites", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de volumen", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volumen con límites acotados", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de volumen con límites", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral con límites", "PE.Controllers.Toolbar.txtIntegralTriple": "Integral triple", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral triple con límites acotados", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral triple con límites", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Y lógico", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Y lógico con límite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Y lógico con límites", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Y lógico con límite inferior en subíndice", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Y lógico con límites de subíndice/supraíndice", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-producto", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproducto con límite inferior", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproducto con límites", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coproducto con límite inferior en subíndice", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproducto con límites de subíndice/supraíndice", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Sumatoria sobre k de n sobre k", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Sumatoria de i igual a cero a n", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Ejemplo de suma con dos índices", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Ejemplo del producto", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Ejemplo de unión", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "O lógico", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "O lógico con límite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "O lógico con límites", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "O lógico con límite inferior en subíndice", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "O lógico con límites de subíndice/supraíndice", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersección", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersección con límite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersección con límites", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersección con límite inferior en subíndice", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersección con límites de subíndice/superíndice", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Producto", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Producto con límite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Producto con límites", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Producto con límite inferior en subíndice", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Producto con límites de subíndice/superíndice", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Sumatoria con límite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Sumatoria con límites", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Sumatoria con límite inferior en subíndice", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Sumatoria con límites de subíndice/supraíndice", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Unión", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unión con límite inferior", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unión con límites", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unión con límite inferior en subíndice", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unión con límites de subíndice/superíndice", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Ejemplo de límite", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Ejemplo de máximo", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Límite", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "PE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_1_2": "Matriz vacía de 1x2", "PE.Controllers.Toolbar.txtMatrix_1_3": "Matriz vacía de 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "Matriz vacía de 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "Matriz vacía de 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz de 2 por 2 vacía entre plecas dobles", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Determinante de 2 por 2 vacío", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz de 2 por 2 vacía entre paréntesis", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz de 2 por 2 vacía entre paréntesis", "PE.Controllers.Toolbar.txtMatrix_2_3": "Matriz vacía de 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "Matriz vacía de 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "Matriz vacía de 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "Matriz vacía de 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Puntos en línea base", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Puntos en línea media", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> diagonales", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Puntos verticales", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa entre parén<PERSON>is", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa entre corchetes", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "Matriz de identidad de 2x2 con ceros", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz de identidad de 2x2 con celdas en blanco que no están en la diagonal", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz de identidad de 3x3 con ceros", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz de identidad de 3x3 con celdas en blanco que no están en la diagonal", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Flecha inferior derecha e izquierda", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Flecha superior derecha e izquierda", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Flecha inferior izquierda", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Flecha superior izquierda", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Flecha inferior derecha", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Flecha superior derecha", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Dos puntos igual", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Produce", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Produce con delta", "PE.Controllers.Toolbar.txtOperator_Definition": "Igual por definición", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Flecha doble inferior derecha e izquierda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Flecha doble superior derecha e izquierda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Flecha inferior izquierda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Flecha superior izquierda", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Flecha inferior derecha", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Flecha superior derecha", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Igual igual", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Menos igual", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Más igual", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Lado derecho de la fórmula cuadrática", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON> cuadrada de un cuadrado más b al cuadrado", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> cu<PERSON> con índice", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radical con índice", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_1": "x subíndice y al cuadrado", "PE.Controllers.Toolbar.txtScriptCustom_2": "e elevado a menos i omega t", "PE.Controllers.Toolbar.txtScriptCustom_3": "x al cuadrado", "PE.Controllers.Toolbar.txtScriptCustom_4": "Y superíndice izquierdo n subíndice izquierdo uno", "PE.Controllers.Toolbar.txtScriptSub": "Subíndice", "PE.Controllers.Toolbar.txtScriptSubSup": "Subíndice-Superíndice", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Subíndice-superíndice izquierdo", "PE.Controllers.Toolbar.txtScriptSup": "Sobreíndice", "PE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "PE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "Casi igual a", "PE.Controllers.Toolbar.txtSymbol_ast": "Operador asterisco", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Operador de viñeta", "PE.Controllers.Toolbar.txtSymbol_cap": "Intersección", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "Elipsis horizontal de línea media", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "PE.Controllers.Toolbar.txtSymbol_cup": "Unión", "PE.Controllers.Toolbar.txtSymbol_ddots": "Elipsis en diagonal de derecha a izquierda", "PE.Controllers.Toolbar.txtSymbol_degree": "Grados", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Signo de división", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Flecha hacia abajo", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vacío", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Épsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Igual", "PE.Controllers.Toolbar.txtSymbol_equiv": "Idéntico a", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Existe", "PE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grados Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON> que o igual a", "PE.Controllers.Toolbar.txtSymbol_gg": "Mayor que", "PE.Controllers.Toolbar.txtSymbol_greater": "Mayor que", "PE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "PE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Flecha izquierda", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Flecha izquierda-derecha", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON>or que o igual a", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON> que", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> que", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Menos más", "PE.Controllers.Toolbar.txtSymbol_mu": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "No igual a", "PE.Controllers.Toolbar.txtSymbol_ni": "Contiene como miembro", "PE.Controllers.Toolbar.txtSymbol_not": "Signo de negación", "PE.Controllers.Toolbar.txtSymbol_notexists": "No existe", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_o": "Ómicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "PE.Controllers.Toolbar.txtSymbol_percent": "Po<PERSON>entaj<PERSON>", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Más", "PE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON> menos", "PE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>a", "PE.Controllers.Toolbar.txtSymbol_qed": "Fin de la demostración", "PE.Controllers.Toolbar.txtSymbol_rddots": "Elipsis en diagonal de izquierda a derecha", "PE.Controllers.Toolbar.txtSymbol_rho": "Ro", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> derecha", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Signo de radical", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON>r consiguiente", "PE.Controllers.Toolbar.txtSymbol_theta": "Zeta", "PE.Controllers.Toolbar.txtSymbol_times": "Signo de multiplicación", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Flecha hacia arriba", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Épsilon (variante)", "PE.Controllers.Toolbar.txtSymbol_varphi": "Variante fi", "PE.Controllers.Toolbar.txtSymbol_varpi": "Variante pi", "PE.Controllers.Toolbar.txtSymbol_varrho": "Variante ro", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Variante zeta", "PE.Controllers.Toolbar.txtSymbol_vdots": "Elipsis vertical", "PE.Controllers.Toolbar.txtSymbol_xsi": "Csi", "PE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Viewport.textFitPage": "Ajustar a la diapositiva", "PE.Controllers.Viewport.textFitWidth": "Ajustar al ancho", "PE.Views.Animation.str0_5": "0,5 s (muy rápido)", "PE.Views.Animation.str1": "1 s (rápido)", "PE.Views.Animation.str2": "2 s (medio)", "PE.Views.Animation.str20": "20 s (muy lento)", "PE.Views.Animation.str3": "3 s (lento)", "PE.Views.Animation.str5": "5 s (muy lento)", "PE.Views.Animation.strDelay": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strDuration": "Duración ", "PE.Views.Animation.strRepeat": "<PERSON><PERSON>r", "PE.Views.Animation.strRewind": "Rebobinar", "PE.Views.Animation.strStart": "Iniciar", "PE.Views.Animation.strTrigger": "Desencadenador", "PE.Views.Animation.textAutoPreview": "Vista previa automática", "PE.Views.Animation.textMoreEffects": "Mostrar más efectos", "PE.Views.Animation.textMoveEarlier": "Mover antes", "PE.Views.Animation.textMoveLater": "Mover despu<PERSON>", "PE.Views.Animation.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textNoRepeat": "(ninguno)", "PE.Views.Animation.textOnClickOf": "Al hacer clic con", "PE.Views.Animation.textOnClickSequence": "Secuencia de clics", "PE.Views.Animation.textStartAfterPrevious": "Después de la anterior", "PE.Views.Animation.textStartOnClick": "Al hacer clic", "PE.Views.Animation.textStartWithPrevious": "Con la anterior", "PE.Views.Animation.textUntilEndOfSlide": "Hasta el final de la diapositiva", "PE.Views.Animation.textUntilNextClick": "Hasta el siguiente clic", "PE.Views.Animation.txtAddEffect": "Agregar animación", "PE.Views.Animation.txtAnimationPane": "Panel de animación", "PE.Views.Animation.txtParameters": "Parámetros", "PE.Views.Animation.txtPreview": "Vista previa", "PE.Views.Animation.txtSec": "S", "PE.Views.AnimationDialog.textPreviewEffect": "Vista previa del efecto", "PE.Views.AnimationDialog.textTitle": "Más efectos", "PE.Views.ChartSettings.text3dDepth": "Profundidad (% de la base)", "PE.Views.ChartSettings.text3dHeight": "Altura (% de la base)", "PE.Views.ChartSettings.text3dRotation": "Rotación 3D", "PE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "PE.Views.ChartSettings.textAutoscale": "Escalado automático", "PE.Views.ChartSettings.textChartType": "Cambiar tipo de gráfico", "PE.Views.ChartSettings.textDefault": "Rotación por defecto", "PE.Views.ChartSettings.textDown": "Abajo", "PE.Views.ChartSettings.textEditData": "<PERSON><PERSON>", "PE.Views.ChartSettings.textHeight": "Altura", "PE.Views.ChartSettings.textKeepRatio": "Proporciones constantes", "PE.Views.ChartSettings.textLeft": "Iz<PERSON>erda", "PE.Views.ChartSettings.textNarrow": "Campo de visión estrecho", "PE.Views.ChartSettings.textPerspective": "Perspectiva", "PE.Views.ChartSettings.textRight": "Derecha", "PE.Views.ChartSettings.textRightAngle": "Ejes en ángulo recto", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textUp": "Arriba", "PE.Views.ChartSettings.textWiden": "Campo de visión ancho", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ChartSettings.textX": "Rotación X", "PE.Views.ChartSettings.textY": "Rotación Y", "PE.Views.ChartSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Descripción", "PE.Views.ChartSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textCenter": "Al centro", "PE.Views.ChartSettingsAdvanced.textFrom": "De", "PE.Views.ChartSettingsAdvanced.textHeight": "Altura", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal ", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Proporciones constantes", "PE.Views.ChartSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.ChartSettingsAdvanced.textPosition": "Posición", "PE.Views.ChartSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textTitle": "Gráfico - Ajustes <PERSON>", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Esquina superior izquierda", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertical", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Establecer formato predeterminado para {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Establecer como el valor predeterminado", "PE.Views.DateTimeDialog.textFormat": "Formatos", "PE.Views.DateTimeDialog.textLang": "Idioma", "PE.Views.DateTimeDialog.textUpdate": "Actualizar automáticamente", "PE.Views.DateTimeDialog.txtTitle": "<PERSON><PERSON> y hora", "PE.Views.DocumentHolder.aboveText": "Arriba", "PE.Views.DocumentHolder.addCommentText": "Agregar comentario", "PE.Views.DocumentHolder.addToLayoutText": "Agregar al Diseño", "PE.Views.DocumentHolder.advancedChartText": "<PERSON><PERSON><PERSON>s a<PERSON> de gráfico", "PE.Views.DocumentHolder.advancedEquationText": "Ajustes de la ecuación", "PE.Views.DocumentHolder.advancedImageText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> imagen", "PE.Views.DocumentHolder.advancedParagraphText": "<PERSON><PERSON><PERSON>s a<PERSON> párrafo", "PE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON>s a<PERSON> de forma", "PE.Views.DocumentHolder.advancedTableText": "<PERSON><PERSON><PERSON>s a<PERSON>zad<PERSON> de tabla", "PE.Views.DocumentHolder.alignmentText": "Alineación", "PE.Views.DocumentHolder.allLinearText": "Lineal (todos)", "PE.Views.DocumentHolder.allProfText": "Profesional (todos)", "PE.Views.DocumentHolder.belowText": "Abajo", "PE.Views.DocumentHolder.cellAlignText": "Alineación vertical de celda", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "Al centro", "PE.Views.DocumentHolder.columnText": "Columna", "PE.Views.DocumentHolder.currLinearText": "Lineal (actual)", "PE.Views.DocumentHolder.currProfText": "Profesional (actual)", "PE.Views.DocumentHolder.deleteColumnText": "Bo<PERSON>r columna", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> fila", "PE.Views.DocumentHolder.deleteTableText": "Borrar tabla", "PE.Views.DocumentHolder.deleteText": "Bo<PERSON>r", "PE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON>r texto hacia arriba", "PE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON> texto hacia abajo", "PE.Views.DocumentHolder.directHText": "Horizontal ", "PE.Views.DocumentHolder.directionText": "Dirección de texto", "PE.Views.DocumentHolder.editChartText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.hyperlinkText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> todo", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Columna i<PERSON>erda", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>na derecha", "PE.Views.DocumentHolder.insertColumnText": "Insertar columna", "PE.Views.DocumentHolder.insertRowAboveText": "Fila de arriba", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowText": "Insertar fila", "PE.Views.DocumentHolder.insertText": "Insertar", "PE.Views.DocumentHolder.langText": "Seleccionar idioma", "PE.Views.DocumentHolder.latexText": "LaTeX", "PE.Views.DocumentHolder.leftText": "A la izquierda", "PE.Views.DocumentHolder.loadSpellText": "Cargando variantes...", "PE.Views.DocumentHolder.mergeCellsText": "Unir celdas", "PE.Views.DocumentHolder.mniCustomTable": "Insertar tabla personalizada", "PE.Views.DocumentHolder.moreText": "Más variantes...", "PE.Views.DocumentHolder.noSpellVariantsText": "No hay variantes", "PE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rightText": "A la derecha", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.spellcheckText": "Сorrección ortográfica", "PE.Views.DocumentHolder.splitCellsText": "Di<PERSON><PERSON> celda...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON> celda", "PE.Views.DocumentHolder.tableText": "Tabla", "PE.Views.DocumentHolder.textAddHGuides": "Agregar guía horizontal", "PE.Views.DocumentHolder.textAddVGuides": "Agregar guía vertical", "PE.Views.DocumentHolder.textArrangeBack": "Enviar al fondo", "PE.Views.DocumentHolder.textArrangeBackward": "Enviar atrás", "PE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON> adelante", "PE.Views.DocumentHolder.textArrangeFront": "Traer al frente", "PE.Views.DocumentHolder.textClearGuides": "Borrar g<PERSON>", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "Copiar", "PE.Views.DocumentHolder.textCrop": "Recortar", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCustom": "Personalizado", "PE.Views.DocumentHolder.textCut": "Cortar", "PE.Views.DocumentHolder.textDeleteGuide": "Eliminar guía", "PE.Views.DocumentHolder.textDistributeCols": "Distribuir columnas", "PE.Views.DocumentHolder.textDistributeRows": "Distribuir filas", "PE.Views.DocumentHolder.textEditPoints": "Modificar puntos", "PE.Views.DocumentHolder.textFlipH": "Voltear horizontalmente", "PE.Views.DocumentHolder.textFlipV": "Voltear verticalmente", "PE.Views.DocumentHolder.textFromFile": "De archivo", "PE.Views.DocumentHolder.textFromStorage": "Desde almacenamiento", "PE.Views.DocumentHolder.textFromUrl": "De URL", "PE.Views.DocumentHolder.textGridlines": "Líneas de cuadrícula", "PE.Views.DocumentHolder.textGuides": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textNextPage": "Diapositiva siguiente", "PE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textPrevPage": "Diapositiva anterior", "PE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagen", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Girar 90° a la izquierda", "PE.Views.DocumentHolder.textRotate90": "Girar 90° a la derecha", "PE.Views.DocumentHolder.textRulers": "Reg<PERSON>", "PE.Views.DocumentHolder.textSaveAsPicture": "Guardar como imagen", "PE.Views.DocumentHolder.textShapeAlignBottom": "Alinear en la parte inferior", "PE.Views.DocumentHolder.textShapeAlignCenter": "Alinear al centro", "PE.Views.DocumentHolder.textShapeAlignLeft": "Alinear a la izquierda", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Alinear al medio", "PE.Views.DocumentHolder.textShapeAlignRight": "Alinear a la derecha", "PE.Views.DocumentHolder.textShapeAlignTop": "Alinear en la parte superior", "PE.Views.DocumentHolder.textShowGridlines": "Mostrar líneas de cuadrícula", "PE.Views.DocumentHolder.textShowGuides": "Mostrar guías", "PE.Views.DocumentHolder.textSlideSettings": "Ajustes de diapositiva", "PE.Views.DocumentHolder.textSmartGuides": "Guías inteligentes", "PE.Views.DocumentHolder.textSnapObjects": "Acoplar el objeto a la cuadrícula", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipGuides": "Mostrar guías", "PE.Views.DocumentHolder.tipIsLocked": "Otro usuario está editando este elemento ahora.", "PE.Views.DocumentHolder.toDictionaryText": "Agregar al diccionario", "PE.Views.DocumentHolder.txtAddBottom": "Agregar borde inferior", "PE.Views.DocumentHolder.txtAddFractionBar": "Agregar barra de fracción", "PE.Views.DocumentHolder.txtAddHor": "Agregar línea horizontal", "PE.Views.DocumentHolder.txtAddLB": "Agregar línea inferior izquierda", "PE.Views.DocumentHolder.txtAddLeft": "Agre<PERSON> borde <PERSON>", "PE.Views.DocumentHolder.txtAddLT": "Agregar línea superior izquierda", "PE.Views.DocumentHolder.txtAddRight": "Agregar borde derecho", "PE.Views.DocumentHolder.txtAddTop": "Agregar borde superior", "PE.Views.DocumentHolder.txtAddVer": "Agregar línea vertical", "PE.Views.DocumentHolder.txtAlign": "Alinear", "PE.Views.DocumentHolder.txtAlignToChar": "Alinear a carácter", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Fondo", "PE.Views.DocumentHolder.txtBorderProps": "Propiedades de borde", "PE.Views.DocumentHolder.txtBottom": "Abajo ", "PE.Views.DocumentHolder.txtChangeLayout": "Cambiar dise<PERSON>", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtColumnAlign": "Alineación de columna", "PE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>o", "PE.Views.DocumentHolder.txtDeleteArg": "Eliminar argumento", "PE.Views.DocumentHolder.txtDeleteBreak": "Borrar salto manual", "PE.Views.DocumentHolder.txtDeleteChars": "Eliminar carácteres encerrados", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminar caracteres encerrados y separadores", "PE.Views.DocumentHolder.txtDeleteEq": "Eliminar ecuación", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteRadical": "Eliminar radical", "PE.Views.DocumentHolder.txtDeleteSlide": "Eliminar diapositiva", "PE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "PE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "PE.Views.DocumentHolder.txtDuplicateSlide": "Duplicar diapositiva", "PE.Views.DocumentHolder.txtFractionLinear": "Cambiar a la fracción lineal", "PE.Views.DocumentHolder.txtFractionSkewed": "Cambiar a la fracción sesgada", "PE.Views.DocumentHolder.txtFractionStacked": "Cambiar a la fracción apilada", "PE.Views.DocumentHolder.txtGroup": "Agrupar", "PE.Views.DocumentHolder.txtGroupCharOver": "Char sobre texto", "PE.Views.DocumentHolder.txtGroupCharUnder": "Char debajo de texto", "PE.Views.DocumentHolder.txtHideBottom": "Ocultar borde inferior", "PE.Views.DocumentHolder.txtHideBottomLimit": "Ocultar límite inferior", "PE.Views.DocumentHolder.txtHideCloseBracket": "Ocultar corchete de cierre", "PE.Views.DocumentHolder.txtHideDegree": "Ocultar grado", "PE.Views.DocumentHolder.txtHideHor": "Ocultar línea horizontal", "PE.Views.DocumentHolder.txtHideLB": "Ocultar línea inferior izquierda ", "PE.Views.DocumentHolder.txtHideLeft": "Ocultar borde i<PERSON>", "PE.Views.DocumentHolder.txtHideLT": "Ocultar línea superior izquierda", "PE.Views.DocumentHolder.txtHideOpenBracket": "Ocultar corchete de apertura", "PE.Views.DocumentHolder.txtHidePlaceholder": "Ocultar marcador de posición", "PE.Views.DocumentHolder.txtHideRight": "O<PERSON>ltar borde derecho", "PE.Views.DocumentHolder.txtHideTop": "Ocultar borde superior", "PE.Views.DocumentHolder.txtHideTopLimit": "Ocultar límite superior", "PE.Views.DocumentHolder.txtHideVer": "Ocultar línea vertical", "PE.Views.DocumentHolder.txtIncreaseArg": "Aumentar el tamaño del argumento", "PE.Views.DocumentHolder.txtInsertArgAfter": "Insertar argumento después", "PE.Views.DocumentHolder.txtInsertArgBefore": "Insertar argumento antes", "PE.Views.DocumentHolder.txtInsertBreak": "Insertar salto manual", "PE.Views.DocumentHolder.txtInsertEqAfter": "Insertar ecuación después", "PE.Views.DocumentHolder.txtInsertEqBefore": "Insertar ecuación antes", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON> solo texto", "PE.Views.DocumentHolder.txtLimitChange": "Cambiar ubicación de límites", "PE.Views.DocumentHolder.txtLimitOver": "Límite sobre el texto", "PE.Views.DocumentHolder.txtLimitUnder": "Límite debajo del texto", "PE.Views.DocumentHolder.txtMatchBrackets": "Coincidir corchetes con el alto de los argumentos", "PE.Views.DocumentHolder.txtMatrixAlign": "Alineación de la matriz", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Mover la diapositiva al final", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Mover la diapositiva al principio", "PE.Views.DocumentHolder.txtNewSlide": "Nueva diapositiva", "PE.Views.DocumentHolder.txtOverbar": "Barra sobre texto", "PE.Views.DocumentHolder.txtPasteDestFormat": "Use el tema de destino", "PE.Views.DocumentHolder.txtPastePicture": "Imagen", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Mantener el formato original", "PE.Views.DocumentHolder.txtPressLink": "Pulse {0} y haga clic en el enlace", "PE.Views.DocumentHolder.txtPreview": "Iniciar <PERSON>", "PE.Views.DocumentHolder.txtPrintSelection": "Imp<PERSON><PERSON>", "PE.Views.DocumentHolder.txtRemFractionBar": "Quitar la barra de fracción", "PE.Views.DocumentHolder.txtRemLimit": "Eliminar límite", "PE.Views.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON> car<PERSON>cter de acento", "PE.Views.DocumentHolder.txtRemoveBar": "Eliminar barra", "PE.Views.DocumentHolder.txtRemScripts": "<PERSON><PERSON>ar índices", "PE.Views.DocumentHolder.txtRemSubscript": "<PERSON><PERSON><PERSON> sub<PERSON>", "PE.Views.DocumentHolder.txtRemSuperscript": "<PERSON><PERSON>ar super<PERSON>", "PE.Views.DocumentHolder.txtResetLayout": "Restablecer diapositiva", "PE.Views.DocumentHolder.txtScriptsAfter": "Índices después de texto", "PE.Views.DocumentHolder.txtScriptsBefore": "Índices antes de texto", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "PE.Views.DocumentHolder.txtShowBottomLimit": "Mostrar límite inferior", "PE.Views.DocumentHolder.txtShowCloseBracket": "Mostrar corchete de cierre", "PE.Views.DocumentHolder.txtShowDegree": "Mostrar grado", "PE.Views.DocumentHolder.txtShowOpenBracket": "Mostrar corchete de apertura", "PE.Views.DocumentHolder.txtShowPlaceholder": "Mostrar marcador de posición", "PE.Views.DocumentHolder.txtShowTopLimit": "Mostrar límite superior", "PE.Views.DocumentHolder.txtSlide": "Diapositiva", "PE.Views.DocumentHolder.txtSlideHide": "Ocultar diapositiva", "PE.Views.DocumentHolder.txtStretchBrackets": "Expandir corchetes", "PE.Views.DocumentHolder.txtTop": "Superior", "PE.Views.DocumentHolder.txtUnderbar": "Barra debajo de texto", "PE.Views.DocumentHolder.txtUngroup": "Desagrupar", "PE.Views.DocumentHolder.txtWarnUrl": "Hacer clic en este enlace puede ser perjudicial para su dispositivo y sus datos.<br>¿Está seguro de que quiere continuar?", "PE.Views.DocumentHolder.unicodeText": "Unicode", "PE.Views.DocumentHolder.vertAlignText": "Alineación vertical", "PE.Views.DocumentPreview.goToSlideText": "Ir a diapositiva", "PE.Views.DocumentPreview.slideIndexText": "Diapositiva {0} de {1}", "PE.Views.DocumentPreview.txtClose": "<PERSON><PERSON>r presentac<PERSON> ", "PE.Views.DocumentPreview.txtEndSlideshow": "Terminar presentación", "PE.Views.DocumentPreview.txtExitFullScreen": "Salir de pantalla completa", "PE.Views.DocumentPreview.txtFinalMessage": "Fin de vista previa de diapositiva. Pulse para salir.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtNext": "Diapositiva siguiente", "PE.Views.DocumentPreview.txtPageNumInvalid": "El número de diapositiva inválido", "PE.Views.DocumentPreview.txtPause": "Detener presentación", "PE.Views.DocumentPreview.txtPlay": "Iniciar <PERSON>", "PE.Views.DocumentPreview.txtPrev": "Diapositiva anterior", "PE.Views.DocumentPreview.txtReset": "Restablecer", "PE.Views.FileMenu.btnAboutCaption": "Acerca de programa", "PE.Views.FileMenu.btnBackCaption": "Abrir ubicación del archivo", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON> nuevo", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> como", "PE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "Abrir", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Historial de versiones", "PE.Views.FileMenu.btnInfoCaption": "Info sobre presentación", "PE.Views.FileMenu.btnPrintCaption": "Imprimir", "PE.Views.FileMenu.btnProtectCaption": "Proteger", "PE.Views.FileMenu.btnRecentFilesCaption": "Abrir reciente", "PE.Views.FileMenu.btnRenameCaption": "Renombrar", "PE.Views.FileMenu.btnReturnCaption": "<PERSON>r a presentación", "PE.Views.FileMenu.btnRightsCaption": "Derechos de acceso", "PE.Views.FileMenu.btnSaveAsCaption": "Guardar como", "PE.Views.FileMenu.btnSaveCaption": "Guardar", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Guardar Copia como", "PE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnToEditCaption": "Editar presentación", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Presentación en blanco", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON>r nueva", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Agregar autor", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Agregar texto", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicación", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Cambiar derechos de acceso", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comentario", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificación por", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificación", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Propietario", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Ubicación", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personas que tienen derechos", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etiquetas", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Subido", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Cambiar derechos de acceso", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Personas que tienen derechos", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Con contraseña", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger presentación", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Con firma", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar presentación", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "La edición eliminará las firmas de la presentación.<br>¿Está seguro de que quiere continuar?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Esta presentación se ha protegido con una contraseña", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Se han agregado firmas válidas al documento. El documento está protegido contra la edición.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algunas de las firmas digitales en la presentación son inválidas o no se pudieron verificar. La presentación está protegida y no se puede editar.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver firmas", "PE.Views.FileMenuPanels.Settings.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "El modo Co-edición", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Hinting", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "<PERSON><PERSON><PERSON> en MAYÚSCULAS", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON> con números", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Ajustes de macros", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Mostrar el botón Opciones de pegado cuando se pegue contenido", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Muestra los cambios de otros usuarios", "PE.Views.FileMenuPanels.Settings.strStrict": "Estricto", "PE.Views.FileMenuPanels.Settings.strTheme": "Tema de interfaz", "PE.Views.FileMenuPanels.Settings.strUnit": "Unidad de medida", "PE.Views.FileMenuPanels.Settings.strZoom": "Valor de zoom predeterminado", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Cada 10 minutos ", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Cada 30 minutos", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Cada 5 minutos", "PE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON> hora", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Guías de alineación", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Autorrecuperación", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Guardar automáticamente", "PE.Views.FileMenuPanels.Settings.textDisabled": "Desactivado", "PE.Views.FileMenuPanels.Settings.textForceSave": "Guardar versiones intermedias", "PE.Views.FileMenuPanels.Settings.textMinute": "Cada minuto", "PE.Views.FileMenuPanels.Settings.txtAll": "Ver todo", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opciones de autocorrección", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Modo de caché predeterminado", "PE.Views.FileMenuPanels.Settings.txtCm": "Centímetro", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Colaboración", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editar y guardar", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Co-edición en tiempo real. Todos los cambios se guardan automáticamente", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Ajustar a la diapositiva", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Ajustar al ancho", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Jeroglíficos", "PE.Views.FileMenuPanels.Settings.txtInch": "Pulgada", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON>er últimos", "PE.Views.FileMenuPanels.Settings.txtMac": "como OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "PE.Views.FileMenuPanels.Settings.txtProofing": "Revisión", "PE.Views.FileMenuPanels.Settings.txtPt": "Punt<PERSON>", "PE.Views.FileMenuPanels.Settings.txtQuickPrint": "Mostrar el botón Impresión Rápida en el encabezado del editor", "PE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "El documento se imprimirá en la última impresora seleccionada o predeterminada", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Habil<PERSON>r todo", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Habilitar todas las macros sin notificación ", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Сorrección ortográfica", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON>habili<PERSON> todo", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Deshabilitar todas las macros sin notificación", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilice el botón \"Guardar\" para sincronizar los cambios que usted y los demás realicen", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utilice la tecla Alt para navegar por la interfaz de usuario mediante el teclado", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utilice la tecla Opción para navegar por la interfaz de usuario mediante el teclado", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostrar notificación", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Deshabilitar todas las macros con notificación", "PE.Views.FileMenuPanels.Settings.txtWin": "como Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "Área de trabajo", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Personalizado", "PE.Views.GridSettings.textSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.GridSettings.textTitle": "Ajustes de cuadrícula", "PE.Views.HeaderFooterDialog.applyAllText": "Aplicar a todo", "PE.Views.HeaderFooterDialog.applyText": "Aplicar", "PE.Views.HeaderFooterDialog.diffLanguage": "No se puede usar un formato de fecha en un idioma diferente del patrón de diapositivas.<br> Para cambiar el patrón pulse \"Aplicar a todo\" en vez de \"Aplicar\"", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Aviso", "PE.Views.HeaderFooterDialog.textDateTime": "<PERSON><PERSON> y hora", "PE.Views.HeaderFooterDialog.textFixed": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textFooter": "Texto en pie de página", "PE.Views.HeaderFooterDialog.textFormat": "Formatos", "PE.Views.HeaderFooterDialog.textLang": "Idioma", "PE.Views.HeaderFooterDialog.textNotTitle": "No mostrar en diapositiva de título", "PE.Views.HeaderFooterDialog.textPreview": "Vista previa", "PE.Views.HeaderFooterDialog.textSlideNum": "Número de diapositiva", "PE.Views.HeaderFooterDialog.textTitle": "Ajustes de pie de página", "PE.Views.HeaderFooterDialog.textUpdate": "Actualizar automáticamente", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Mostrar", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Vincular a", "PE.Views.HyperlinkSettingsDialog.textDefault": "Fragmento de texto seleccionado", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Introduzca título aquí", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Introduzca enlace aquí", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Introduzca informacíon sobre herramientas aquí", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Enlace externo", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Diapositiva en esta presentación", "PE.Views.HyperlinkSettingsDialog.textSlides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTipText": "Información en pantalla", "PE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo es obligatorio", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Primera diapositiva", "PE.Views.HyperlinkSettingsDialog.txtLast": "Última diapositiva", "PE.Views.HyperlinkSettingsDialog.txtNext": "Diapositiva siguiente", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo debe ser URL en el formato \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Diapositiva anterior", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Diapositiva", "PE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "PE.Views.ImageSettings.textCrop": "Recortar", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropToShape": "Recortar a la forma", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "PE.Views.ImageSettings.textFitSlide": "Ajustar a la diapositiva", "PE.Views.ImageSettings.textFlip": "Volteo", "PE.Views.ImageSettings.textFromFile": "De archivo", "PE.Views.ImageSettings.textFromStorage": "Desde almacenamiento", "PE.Views.ImageSettings.textFromUrl": "De URL", "PE.Views.ImageSettings.textHeight": "Altura", "PE.Views.ImageSettings.textHint270": "Girar 90° a la izquierda", "PE.Views.ImageSettings.textHint90": "Girar 90° a la derecha", "PE.Views.ImageSettings.textHintFlipH": "Volteo Horizontal", "PE.Views.ImageSettings.textHintFlipV": "Volteo Vertical", "PE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagen", "PE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textRecentlyUsed": "Usados recientemente", "PE.Views.ImageSettings.textRotate90": "Girar 90°", "PE.Views.ImageSettings.textRotation": "Rotación", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Descripción", "PE.Views.ImageSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Al centro", "PE.Views.ImageSettingsAdvanced.textFlipped": "Volteado", "PE.Views.ImageSettingsAdvanced.textFrom": "De", "PE.Views.ImageSettingsAdvanced.textHeight": "Altura", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal ", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporciones constantes", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.ImageSettingsAdvanced.textPosition": "Posición", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotación", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "Imagen - <PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Esquina superior izquierda", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "PE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "Acerca de programa", "PE.Views.LeftMenu.tipChat": "Chateo", "PE.Views.LeftMenu.tipComments": "Comentarios", "PE.Views.LeftMenu.tipPlugins": "Plugins", "PE.Views.LeftMenu.tipSearch": "Buscar", "PE.Views.LeftMenu.tipSlides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSupport": "Feedback y Soporte", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtDeveloper": "MODO DE DESARROLLO", "PE.Views.LeftMenu.txtEditor": "Editor de presentaciones", "PE.Views.LeftMenu.txtLimit": "Limitar acceso", "PE.Views.LeftMenu.txtTrial": "MODO DE PRUEBA", "PE.Views.LeftMenu.txtTrialDev": "Modo de programador de prueba", "PE.Views.ParagraphSettings.strLineHeight": "Espaciado de línea", "PE.Views.ParagraphSettings.strParagraphSpacing": "Espaciado de Párafo ", "PE.Views.ParagraphSettings.strSpacingAfter": "Después", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "PE.Views.ParagraphSettings.textAt": "en", "PE.Views.ParagraphSettings.textAtLeast": "Por lo menos", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "Exactamente", "PE.Views.ParagraphSettings.txtAutoText": "Auto", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Las pestañas especificadas aparecerán en este campo", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Doble tachado", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Reti<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaciado de línea", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Derecho", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Después", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Letra ", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Sangrías y disposición", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON> pequeñas", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subíndice", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobreíndice", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Pestaña", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Alineación", "PE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espacia<PERSON> entre caracteres", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Pestaña predeterminada", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Efectos", "PE.Views.ParagraphSettingsAdvanced.textExact": "Exactamente", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primera línea", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendido", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ninguno)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Eliminar", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Eliminar todo", "PE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Al centro", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posición de tab", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Derecho", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Párrafo - <PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "PE.Views.PrintWithPreview.txtAllPages": "Todas las diapositivas", "PE.Views.PrintWithPreview.txtCurrentPage": "Diapositiva actual", "PE.Views.PrintWithPreview.txtCustomPages": "Impresión personalizada", "PE.Views.PrintWithPreview.txtEmptyTable": "No hay nada que imprimir porque la presentación está vacía", "PE.Views.PrintWithPreview.txtOf": "de {0}", "PE.Views.PrintWithPreview.txtPage": "Diapositiva", "PE.Views.PrintWithPreview.txtPageNumInvalid": "Número de diapositiva no válido", "PE.Views.PrintWithPreview.txtPages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtPaperSize": "<PERSON><PERSON><PERSON>", "PE.Views.PrintWithPreview.txtPrint": "Imprimir", "PE.Views.PrintWithPreview.txtPrintPdf": "Imprimir en PDF", "PE.Views.PrintWithPreview.txtPrintRange": "Intervalo de impresión", "PE.Views.RightMenu.txtChartSettings": "Ajustes de gráfico", "PE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON><PERSON> imagen", "PE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON> de p<PERSON>fo", "PE.Views.RightMenu.txtShapeSettings": "Ajustes de forma", "PE.Views.RightMenu.txtSignatureSettings": "Configuración de firma", "PE.Views.RightMenu.txtSlideSettings": "Ajustes de diapositiva", "PE.Views.RightMenu.txtTableSettings": "Ajustes de tabla", "PE.Views.RightMenu.txtTextArtSettings": "Ajustes de galería de texto", "PE.Views.ShapeSettings.strBackground": "Color de fondo", "PE.Views.ShapeSettings.strChange": "Cambiar autoforma", "PE.Views.ShapeSettings.strColor": "Color", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Color de primer plano", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strShadow": "Mostrar sombra", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "Lín<PERSON>", "PE.Views.ShapeSettings.strTransparency": "Opacidad ", "PE.Views.ShapeSettings.strType": "Tipo", "PE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "El valor numérico es incorrecto.<br><PERSON>r favor, introduzca un valor de 0 a 1584 puntos.", "PE.Views.ShapeSettings.textColor": "Color de relleno", "PE.Views.ShapeSettings.textDirection": "Dirección ", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON> patr<PERSON>", "PE.Views.ShapeSettings.textFlip": "Volteo", "PE.Views.ShapeSettings.textFromFile": "De archivo", "PE.Views.ShapeSettings.textFromStorage": "Desde almacenamiento", "PE.Views.ShapeSettings.textFromUrl": "De URL", "PE.Views.ShapeSettings.textGradient": "Puntos de degradado ", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textHint270": "Girar 90° a la izquierda", "PE.Views.ShapeSettings.textHint90": "Girar 90° a la derecha", "PE.Views.ShapeSettings.textHintFlipH": "Volteo Horizontal", "PE.Views.ShapeSettings.textHintFlipV": "Volteo Vertical", "PE.Views.ShapeSettings.textImageTexture": "Imagen o textura", "PE.Views.ShapeSettings.textLinear": "Lineal", "PE.Views.ShapeSettings.textNoFill": "<PERSON> relleno", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPosition": "Posición", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Usados recientemente", "PE.Views.ShapeSettings.textRotate90": "Girar 90°", "PE.Views.ShapeSettings.textRotation": "Rotación", "PE.Views.ShapeSettings.textSelectImage": "Seleccionar imagen", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "<PERSON>st<PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "De textura", "PE.Views.ShapeSettings.textTile": "Mosaico", "PE.Views.ShapeSettings.tipAddGradientPoint": "Agregar punto de degradado", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "Cartón", "PE.Views.ShapeSettings.txtDarkFabric": "Tela oscura", "PE.Views.ShapeSettings.txtGrain": "Grano", "PE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "Papel gris", "PE.Views.ShapeSettings.txtKnit": "Tejid<PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "Sin líneas", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "Columnas", "PE.Views.ShapeSettingsAdvanced.strMargins": "Espaciado del texto", "PE.Views.ShapeSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Descripción", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Autoajustar", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Tam<PERSON>ño inicial", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBevel": "Biselado", "PE.Views.ShapeSettingsAdvanced.textBottom": "Inferior", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "PE.Views.ShapeSettingsAdvanced.textCenter": "Al centro", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Número de columnas", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Tamaño final", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "PE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Volteado", "PE.Views.ShapeSettingsAdvanced.textFrom": "De", "PE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal ", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de combinación", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporciones constantes", "PE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON>stilo <PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "No autoajustar", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.ShapeSettingsAdvanced.textPosition": "Posición", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Ajustar tamaño de la forma al texto", "PE.Views.ShapeSettingsAdvanced.textRight": "Derecho", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotación", "PE.Views.ShapeSettingsAdvanced.textRound": "Redondeado", "PE.Views.ShapeSettingsAdvanced.textShrink": "Comprimir el texto al desbordarse", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Espacio entre columnas", "PE.Views.ShapeSettingsAdvanced.textSquare": "Cuadrado", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Cuadro de texto", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forma - <PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTop": "Superior", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Esquina superior izquierda", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertical", "PE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Grosores y flechas", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "ning<PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "PE.Views.SignatureSettings.strDelete": "Elimine la firma", "PE.Views.SignatureSettings.strDetails": "Detalles de la firma", "PE.Views.SignatureSettings.strInvalid": "<PERSON><PERSON><PERSON> invalidas", "PE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strSignature": "Firma", "PE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON> valida", "PE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON> de todas maneras", "PE.Views.SignatureSettings.txtEditWarning": "La edición eliminará las firmas de la presentación.<br>¿Está seguro de que quiere continuar?", "PE.Views.SignatureSettings.txtRemoveWarning": "¿Desea eliminar esta firma?<br> No se puede deshacer.", "PE.Views.SignatureSettings.txtSigned": "Se han agregado firmas válidas al documento. El documento está protegido contra la edición.", "PE.Views.SignatureSettings.txtSignedInvalid": "Algunas de las firmas digitales en la presentación son inválidas o no se pudieron verificar. La presentación está protegida y no se puede editar.", "PE.Views.SlideSettings.strBackground": "Color de fondo", "PE.Views.SlideSettings.strColor": "Color", "PE.Views.SlideSettings.strDateTime": "Mostrar fecha y hora", "PE.Views.SlideSettings.strFill": "Fondo", "PE.Views.SlideSettings.strForeground": "Color de primer plano", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strSlideNum": "Mostrar número de diapositiva", "PE.Views.SlideSettings.strTransparency": "Opacidad ", "PE.Views.SlideSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Color de relleno", "PE.Views.SlideSettings.textDirection": "Dirección ", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON> patr<PERSON>", "PE.Views.SlideSettings.textFromFile": "De archivo", "PE.Views.SlideSettings.textFromStorage": "Desde almacenamiento", "PE.Views.SlideSettings.textFromUrl": "De URL", "PE.Views.SlideSettings.textGradient": "Puntos de degradado ", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textImageTexture": "Imagen o textura", "PE.Views.SlideSettings.textLinear": "Lineal", "PE.Views.SlideSettings.textNoFill": "<PERSON> relleno", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPosition": "Posición", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Anular cambios", "PE.Views.SlideSettings.textSelectImage": "Seleccionar imagen", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "<PERSON>st<PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "De textura", "PE.Views.SlideSettings.textTile": "Mosaico", "PE.Views.SlideSettings.tipAddGradientPoint": "Agregar punto de degradado", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "Cartón", "PE.Views.SlideSettings.txtDarkFabric": "Tela oscura", "PE.Views.SlideSettings.txtGrain": "Grano", "PE.Views.SlideSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "Papel gris", "PE.Views.SlideSettings.txtKnit": "Tejid<PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "<PERSON><PERSON>", "PE.Views.SlideshowSettings.textLoop": "Repetir el ciclo hasta presionar 'Esc'", "PE.Views.SlideshowSettings.textTitle": "Mostrar los ajustes", "PE.Views.SlideSizeSettings.strLandscape": "Horizontal", "PE.Views.SlideSizeSettings.strPortrait": "Vertical", "PE.Views.SlideSizeSettings.textHeight": "Altura", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientación de la diapositiva", "PE.Views.SlideSizeSettings.textSlideSize": "Tamaño de diapositiva", "PE.Views.SlideSizeSettings.textTitle": "Ajustes de tamaño de diapositiva", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Diapositivas 35 mm", "PE.Views.SlideSizeSettings.txtA3": "Hoja A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Hoja A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Hoja B4 (ICO) (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "Hoja B5 (ICO) (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Personalizado", "PE.Views.SlideSizeSettings.txtLedger": "Papel de Contabilidad (11x17 en)", "PE.Views.SlideSizeSettings.txtLetter": "<PERSON><PERSON> (8.5x11 en)", "PE.Views.SlideSizeSettings.txtOverhead": "Transparencia", "PE.Views.SlideSizeSettings.txtStandard": "<PERSON><PERSON><PERSON><PERSON> (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Pantalla panorámica", "PE.Views.Statusbar.goToPageText": "Ir a diapositiva", "PE.Views.Statusbar.pageIndexText": "Diapositiva {0} de {1}", "PE.Views.Statusbar.textShowBegin": "Mostrar presentación desde el principio", "PE.Views.Statusbar.textShowCurrent": "Mostrar desde la diapositiva actual", "PE.Views.Statusbar.textShowPresenterView": "Mostrar presentación en el modo presentador", "PE.Views.Statusbar.tipAccessRights": "Administrar los derechos de acceso de documentos", "PE.Views.Statusbar.tipFitPage": "Ajustar a la diapositiva", "PE.Views.Statusbar.tipFitWidth": "Ajustar a ancho", "PE.Views.Statusbar.tipPreview": "Iniciar <PERSON>", "PE.Views.Statusbar.tipSetLang": "Establecer idioma de texto", "PE.Views.Statusbar.tipZoomFactor": "Ampliación", "PE.Views.Statusbar.tipZoomIn": "Acercar", "PE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON>", "PE.Views.Statusbar.txtPageNumInvalid": "El número de diapositiva inválido", "PE.Views.TableSettings.deleteColumnText": "Bo<PERSON>r columna", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> fila", "PE.Views.TableSettings.deleteTableText": "Borrar tabla", "PE.Views.TableSettings.insertColumnLeftText": "Insertar columna a la izquierda", "PE.Views.TableSettings.insertColumnRightText": "Insertar columna a la derecha", "PE.Views.TableSettings.insertRowAboveText": "Insertar fila arriba", "PE.Views.TableSettings.insertRowBelowText": "Insertar fila abajo", "PE.Views.TableSettings.mergeCellsText": "Unir celdas", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON><PERSON><PERSON> celda", "PE.Views.TableSettings.selectColumnText": "Seleccionar columna", "PE.Views.TableSettings.selectRowText": "Seleccionar fila", "PE.Views.TableSettings.selectTableText": "Seleccionar tabla", "PE.Views.TableSettings.splitCellsText": "Di<PERSON><PERSON> celda...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON> celda", "PE.Views.TableSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "PE.Views.TableSettings.textBackColor": "Color de fondo", "PE.Views.TableSettings.textBanded": "Con bandas", "PE.Views.TableSettings.textBorderColor": "Color", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "Columnas", "PE.Views.TableSettings.textDistributeCols": "Distribuir columnas", "PE.Views.TableSettings.textDistributeRows": "Distribuir filas", "PE.Views.TableSettings.textEdit": "Filas y columnas", "PE.Views.TableSettings.textEmptyTemplate": "Sin plantillas", "PE.Views.TableSettings.textFirst": "primero", "PE.Views.TableSettings.textHeader": "Encabezado", "PE.Views.TableSettings.textHeight": "Altura", "PE.Views.TableSettings.textLast": "Último", "PE.Views.TableSettings.textRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Seleccione bordes que usted desea cambiar aplicando estilo seleccionado", "PE.Views.TableSettings.textTemplate": "Seleccionar de plantilla", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "<PERSON>jar borde exterior y todas líneas interiores ", "PE.Views.TableSettings.tipBottom": "<PERSON>jar s<PERSON>lo borde exterior inferior", "PE.Views.TableSettings.tipInner": "Fijar sólo líneas interiores", "PE.Views.TableSettings.tipInnerHor": "Fijar sólo líneas horizontales interiores ", "PE.Views.TableSettings.tipInnerVert": "Fijar sólo líneas verticales interiores", "PE.Views.TableSettings.tipLeft": "<PERSON><PERSON> s<PERSON>lo borde exterior izquierdo", "PE.Views.TableSettings.tipNone": "No fijar bordes", "PE.Views.TableSettings.tipOuter": "<PERSON><PERSON> s<PERSON>lo borde <PERSON>", "PE.Views.TableSettings.tipRight": "<PERSON>jar s<PERSON>lo borde exterior derecho", "PE.Views.TableSettings.tipTop": "<PERSON>jar s<PERSON>lo borde exterior superior", "PE.Views.TableSettings.txtGroupTable_Custom": "Personalizado", "PE.Views.TableSettings.txtGroupTable_Dark": "Oscuro", "PE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Medium": "Medio", "PE.Views.TableSettings.txtGroupTable_Optimal": "Mejor coincidencia de documento", "PE.Views.TableSettings.txtNoBorders": "<PERSON> bordes", "PE.Views.TableSettings.txtTable_Accent": "Acentuación", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON><PERSON> claro", "PE.Views.TableSettings.txtTable_MediumStyle": "Estilo medio", "PE.Views.TableSettings.txtTable_NoGrid": "Sin cuadrícula", "PE.Views.TableSettings.txtTable_NoStyle": "Sin estilo", "PE.Views.TableSettings.txtTable_TableGrid": "Cuadrícula de tabla", "PE.Views.TableSettings.txtTable_ThemedStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "PE.Views.TableSettingsAdvanced.textAltDescription": "Descripción", "PE.Views.TableSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "Inferior", "PE.Views.TableSettingsAdvanced.textCenter": "Al centro", "PE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON><PERSON> márgenes predeterminados", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON><PERSON><PERSON> predeterminados", "PE.Views.TableSettingsAdvanced.textFrom": "De", "PE.Views.TableSettingsAdvanced.textHeight": "Altura", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal ", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Proporciones constantes", "PE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textPlacement": "Ubicación", "PE.Views.TableSettingsAdvanced.textPosition": "Posición", "PE.Views.TableSettingsAdvanced.textRight": "Derecho", "PE.Views.TableSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTitle": "Tabla - <PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTop": "Superior", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Esquina superior izquierda", "PE.Views.TableSettingsAdvanced.textVertical": "Vertical", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "Color de fondo", "PE.Views.TextArtSettings.strColor": "Color", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "Color de primer plano", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strStroke": "Lín<PERSON>", "PE.Views.TextArtSettings.strTransparency": "Opacidad ", "PE.Views.TextArtSettings.strType": "Tipo", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "El valor numérico es incorrecto.<br><PERSON>r favor, introduzca un valor de 0 a 1584 puntos.", "PE.Views.TextArtSettings.textColor": "Color de relleno", "PE.Views.TextArtSettings.textDirection": "Dirección ", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON> patr<PERSON>", "PE.Views.TextArtSettings.textFromFile": "De archivo", "PE.Views.TextArtSettings.textFromUrl": "De URL", "PE.Views.TextArtSettings.textGradient": "Puntos de degradado ", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textImageTexture": "Imagen o textura", "PE.Views.TextArtSettings.textLinear": "Lineal", "PE.Views.TextArtSettings.textNoFill": "<PERSON> relleno", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPosition": "Posición", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "<PERSON>st<PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "Plantilla", "PE.Views.TextArtSettings.textTexture": "De textura", "PE.Views.TextArtSettings.textTile": "Mosaico", "PE.Views.TextArtSettings.textTransform": "Transformar", "PE.Views.TextArtSettings.tipAddGradientPoint": "Agregar punto de degradado", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "Cartón", "PE.Views.TextArtSettings.txtDarkFabric": "Tela oscura", "PE.Views.TextArtSettings.txtGrain": "Grano", "PE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtGreyPaper": "Papel gris", "PE.Views.TextArtSettings.txtKnit": "Tejid<PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "Sin línea", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "PE.Views.Toolbar.capAddSlide": "Agregar diapositiva", "PE.Views.Toolbar.capBtnAddComment": "Agregar comentario", "PE.Views.Toolbar.capBtnComment": "Comentario", "PE.Views.Toolbar.capBtnDateTime": "<PERSON><PERSON> y hora", "PE.Views.Toolbar.capBtnInsHeader": "Pie de página", "PE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "PE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "PE.Views.Toolbar.capBtnSlideNum": "Número de diapositiva", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Diagrama", "PE.Views.Toolbar.capInsertEquation": "Ecuación", "PE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertImage": "Imagen", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "Tabla", "PE.Views.Toolbar.capInsertText": "Cuadro de texto", "PE.Views.Toolbar.capInsertTextArt": "Galería de texto", "PE.Views.Toolbar.capInsertVideo": "Vídeo", "PE.Views.Toolbar.capTabFile": "Archivo", "PE.Views.Toolbar.capTabHome": "<PERSON><PERSON>o", "PE.Views.Toolbar.capTabInsert": "Insertar", "PE.Views.Toolbar.mniCapitalizeWords": "Poner En Mayúsculas Cada Palabra", "PE.Views.Toolbar.mniCustomTable": "Insertar tabla personalizada", "PE.Views.Toolbar.mniImageFromFile": "Imagen de archivo", "PE.Views.Toolbar.mniImageFromStorage": "Imagen de Almacenamiento", "PE.Views.Toolbar.mniImageFromUrl": "Imagen de URL", "PE.Views.Toolbar.mniInsertSSE": "Insertar hoja de cálculo", "PE.Views.Toolbar.mniLowerCase": "minúsculas", "PE.Views.Toolbar.mniSentenceCase": "Tipo oración.", "PE.Views.Toolbar.mniSlideAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniSlideStandard": "<PERSON><PERSON><PERSON><PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "tIPO iNVERSO", "PE.Views.Toolbar.mniUpperCase": "MAYÚSCULAS", "PE.Views.Toolbar.strMenuNoFill": "<PERSON> relleno", "PE.Views.Toolbar.textAlignBottom": "Alinear texto en la parte superior", "PE.Views.Toolbar.textAlignCenter": "Centrar texto", "PE.Views.Toolbar.textAlignJust": "Justificar", "PE.Views.Toolbar.textAlignLeft": "Alinear texto a la izquierda", "PE.Views.Toolbar.textAlignMiddle": "Alinear texto al medio", "PE.Views.Toolbar.textAlignRight": "Alinear texto a la derecha", "PE.Views.Toolbar.textAlignTop": "Alinear texto en la parte superior", "PE.Views.Toolbar.textArrangeBack": "Enviar al fondo", "PE.Views.Toolbar.textArrangeBackward": "Enviar hacia atrás", "PE.Views.Toolbar.textArrangeForward": "<PERSON><PERSON><PERSON> adelante", "PE.Views.Toolbar.textArrangeFront": "Traer al primer plano", "PE.Views.Toolbar.textBold": "Negrita", "PE.Views.Toolbar.textColumnsCustom": "Columnas personalizadas", "PE.Views.Toolbar.textColumnsOne": "<PERSON> columna", "PE.Views.Toolbar.textColumnsThree": "Tres columnas", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON> columnas", "PE.Views.Toolbar.textItalic": "Cursiva", "PE.Views.Toolbar.textListSettings": "Ajustes de lista", "PE.Views.Toolbar.textRecentlyUsed": "Usados recientemente", "PE.Views.Toolbar.textShapeAlignBottom": "Alinear en la parte inferior", "PE.Views.Toolbar.textShapeAlignCenter": "Alinear al centro", "PE.Views.Toolbar.textShapeAlignLeft": "Alinear a la izquierda", "PE.Views.Toolbar.textShapeAlignMiddle": "Alinear al medio", "PE.Views.Toolbar.textShapeAlignRight": "Alinear a la derecha", "PE.Views.Toolbar.textShapeAlignTop": "Alinear en la parte superior", "PE.Views.Toolbar.textShowBegin": "Mostrar presentación desde el principio", "PE.Views.Toolbar.textShowCurrent": "Mostrar desde la diapositiva actual", "PE.Views.Toolbar.textShowPresenterView": "Observar vista del presentador", "PE.Views.Toolbar.textShowSettings": "Mostrar los ajustes", "PE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSubscript": "Subíndice", "PE.Views.Toolbar.textSuperscript": "Sobreíndice", "PE.Views.Toolbar.textTabAnimation": "Animación", "PE.Views.Toolbar.textTabCollaboration": "Colaboración", "PE.Views.Toolbar.textTabFile": "Archivo", "PE.Views.Toolbar.textTabHome": "<PERSON><PERSON>o", "PE.Views.Toolbar.textTabInsert": "Insertar", "PE.Views.Toolbar.textTabProtect": "Protección", "PE.Views.Toolbar.textTabTransitions": "Transiciones", "PE.Views.Toolbar.textTabView": "Vista", "PE.Views.Toolbar.textTitleError": "Error", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipAddSlide": "Agregar diapositiva", "PE.Views.Toolbar.tipBack": "Atrás", "PE.Views.Toolbar.tipChangeCase": "Cambiar mayús<PERSON>s y minúsculas", "PE.Views.Toolbar.tipChangeChart": "Cambiar tipo de gráfico", "PE.Views.Toolbar.tipChangeSlide": "Cambiar diseño de diapositiva", "PE.Views.Toolbar.tipClearStyle": "Limpiar estilo", "PE.Views.Toolbar.tipColorSchemas": "Cambiar combinación de colores", "PE.Views.Toolbar.tipColumns": "Insertar columnas", "PE.Views.Toolbar.tipCopy": "Copiar", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "PE.Views.Toolbar.tipCut": "Cortar", "PE.Views.Toolbar.tipDateTime": "Insertar la fecha y hora actuales", "PE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON> ta<PERSON> de <PERSON>ra", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipEditHeader": "Editar pie de página", "PE.Views.Toolbar.tipFontColor": "Color de letra", "PE.Views.Toolbar.tipFontName": "Letra ", "PE.Views.Toolbar.tipFontSize": "Tam<PERSON>ño de <PERSON>", "PE.Views.Toolbar.tipHAligh": "Alineación horizontal", "PE.Views.Toolbar.tipHighlightColor": "Color de resaltado", "PE.Views.Toolbar.tipIncFont": "Aumentar tamaño de letra", "PE.Views.Toolbar.tipIncPrLeft": "Aumentar <PERSON>", "PE.Views.Toolbar.tipInsertAudio": "Insertar audio", "PE.Views.Toolbar.tipInsertChart": "Insertar gráfico", "PE.Views.Toolbar.tipInsertEquation": "Insertar ecuación", "PE.Views.Toolbar.tipInsertHorizontalText": "Insertar cuadro de texto horizontal", "PE.Views.Toolbar.tipInsertHyperlink": "Agre<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertImage": "Insertar imagen", "PE.Views.Toolbar.tipInsertShape": "Insertar autoforma", "PE.Views.Toolbar.tipInsertSmartArt": "Insertar SmartArt", "PE.Views.Toolbar.tipInsertSymbol": "Insertar symboló", "PE.Views.Toolbar.tipInsertTable": "Insertar tabla", "PE.Views.Toolbar.tipInsertText": "Insertar cuadro de texto", "PE.Views.Toolbar.tipInsertTextArt": "Insertar Galería de Texto", "PE.Views.Toolbar.tipInsertVerticalText": "Insertar cuadro de texto vertical", "PE.Views.Toolbar.tipInsertVideo": "Insertar vídeo", "PE.Views.Toolbar.tipLineSpace": "Espaciado de línea", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersArrow": "Viñetas de flecha", "PE.Views.Toolbar.tipMarkersCheckmark": "Viñetas de marca de verificación", "PE.Views.Toolbar.tipMarkersDash": "Viñetas g<PERSON>", "PE.Views.Toolbar.tipMarkersFRhombus": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersFRound": "Viñetas redondas rellenas", "PE.Views.Toolbar.tipMarkersFSquare": "Viñetas cuadradas rellenas", "PE.Views.Toolbar.tipMarkersHRound": "Viñetas redondas huecas", "PE.Views.Toolbar.tipMarkersStar": "Viñetas de estrella", "PE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "Numeración", "PE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPreview": "Iniciar <PERSON>", "PE.Views.Toolbar.tipPrint": "Imprimir", "PE.Views.Toolbar.tipPrintQuick": "Impresión rápida", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "Guardar", "PE.Views.Toolbar.tipSaveCoauth": "Guarde los cambios para que otros usuarios los puedan ver.", "PE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "PE.Views.Toolbar.tipShapeAlign": "Alinear forma", "PE.Views.Toolbar.tipShapeArrange": "Arreglar forma", "PE.Views.Toolbar.tipSlideNum": "Insertar el número de diapositiva", "PE.Views.Toolbar.tipSlideSize": "Se<PERSON><PERSON><PERSON>r ta<PERSON> de diapositiva", "PE.Views.Toolbar.tipSlideTheme": "Tema de diapositiva", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Alineación vertical", "PE.Views.Toolbar.tipViewSettings": "<PERSON><PERSON> a<PERSON>", "PE.Views.Toolbar.txtDistribHor": "Distribuir horizontalmente", "PE.Views.Toolbar.txtDistribVert": "Distribuir verticalmente", "PE.Views.Toolbar.txtDuplicateSlide": "Duplicar diapositiva", "PE.Views.Toolbar.txtGroup": "Agrupar", "PE.Views.Toolbar.txtObjectsAlign": "Alinear objetos seleccionados", "PE.Views.Toolbar.txtScheme1": "Oficina", "PE.Views.Toolbar.txtScheme10": "Intermedio", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "Opulento", "PE.Views.Toolbar.txtScheme14": "Mirador", "PE.Views.Toolbar.txtScheme15": "Origen", "PE.Views.Toolbar.txtScheme16": "Papel", "PE.Views.Toolbar.txtScheme17": "Solsticio", "PE.Views.Toolbar.txtScheme18": "Técnico", "PE.Views.Toolbar.txtScheme19": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme2": "Escala de grises", "PE.Views.Toolbar.txtScheme20": "Urbano", "PE.Views.Toolbar.txtScheme21": "Brío", "PE.Views.Toolbar.txtScheme22": "Nueva oficina", "PE.Views.Toolbar.txtScheme3": "Vértice", "PE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "PE.Views.Toolbar.txtScheme5": "Civil", "PE.Views.Toolbar.txtScheme6": "Concurrencia", "PE.Views.Toolbar.txtScheme7": "Equidad ", "PE.Views.Toolbar.txtScheme8": "F<PERSON>jo", "PE.Views.Toolbar.txtScheme9": "Fundición", "PE.Views.Toolbar.txtSlideAlign": "Alinear con la diapositiva", "PE.Views.Toolbar.txtUngroup": "Desagrupar", "PE.Views.Transitions.strDelay": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strDuration": "Duración ", "PE.Views.Transitions.strStartOnClick": "Iniciar al hacer clic", "PE.Views.Transitions.textBlack": "En negro", "PE.Views.Transitions.textBottom": "Abajo ", "PE.Views.Transitions.textBottomLeft": "Abajo a la izquierda", "PE.Views.Transitions.textBottomRight": "Abajo a la derecha", "PE.Views.Transitions.textClock": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "En el sentido de las agujas del reloj", "PE.Views.Transitions.textCounterclockwise": "En el sentido contrario a las agujas del reloj", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "Atenuación", "PE.Views.Transitions.textHorizontalIn": "Horizontal entrante", "PE.Views.Transitions.textHorizontalOut": "Horizontal saliente", "PE.Views.Transitions.textLeft": "A la izquierda", "PE.Views.Transitions.textNone": "Ninguna", "PE.Views.Transitions.textPush": "Empuje", "PE.Views.Transitions.textRight": "A la derecha", "PE.Views.Transitions.textSmoothly": "Suavemente", "PE.Views.Transitions.textSplit": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textTop": "Arriba", "PE.Views.Transitions.textTopLeft": "Arriba a la izquierda", "PE.Views.Transitions.textTopRight": "Arriba a la derecha", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "Vertical entrante", "PE.Views.Transitions.textVerticalOut": "Vertical saliente", "PE.Views.Transitions.textWedge": "Cuña", "PE.Views.Transitions.textWipe": "<PERSON><PERSON>", "PE.Views.Transitions.textZoom": "Zoom", "PE.Views.Transitions.textZoomIn": "Acercar", "PE.Views.Transitions.textZoomOut": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomRotate": "Zoom y giro", "PE.Views.Transitions.txtApplyToAll": "Aplicar a todas las diapositivas", "PE.Views.Transitions.txtParameters": "Parámetros", "PE.Views.Transitions.txtPreview": "Vista previa", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.textAddHGuides": "Agregar guía horizontal", "PE.Views.ViewTab.textAddVGuides": "Agregar guía vertical", "PE.Views.ViewTab.textAlwaysShowToolbar": "Mostrar siempre la barra de herramientas", "PE.Views.ViewTab.textClearGuides": "Borrar g<PERSON>", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Personalizado", "PE.Views.ViewTab.textFitToSlide": "Ajustar a la diapositiva", "PE.Views.ViewTab.textFitToWidth": "Ajustar al ancho", "PE.Views.ViewTab.textGridlines": "Líneas de cuadrícula", "PE.Views.ViewTab.textGuides": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textInterfaceTheme": "Tema de la interfaz", "PE.Views.ViewTab.textLeftMenu": "Panel izquierdo", "PE.Views.ViewTab.textNotes": "Notas", "PE.Views.ViewTab.textRightMenu": "Panel derecho", "PE.Views.ViewTab.textRulers": "Reg<PERSON>", "PE.Views.ViewTab.textShowGridlines": "Mostrar líneas de cuadrícula", "PE.Views.ViewTab.textShowGuides": "Mostrar guías", "PE.Views.ViewTab.textSmartGuides": "Guías inteligentes", "PE.Views.ViewTab.textSnapObjects": "Acoplar el objeto a la cuadrícula", "PE.Views.ViewTab.textStatusBar": "Barra de estado", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "Ajustar a la diapositiva", "PE.Views.ViewTab.tipFitToWidth": "Ajustar al ancho", "PE.Views.ViewTab.tipGridlines": "Mostrar líneas de cuadrícula", "PE.Views.ViewTab.tipGuides": "Mostrar guías", "PE.Views.ViewTab.tipInterfaceTheme": "Tema de la interfaz"}