﻿<!DOCTYPE html>
<html>
	<head>
		<title>Onglet Transitions</title>
		<meta charset="utf-8" />
        <meta name="description" content="Présentation de l'interface utilisateur de l'Éditeur de Présentations - l'onglet Transitions" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Onglet Transitions</h1>
            <p>L'onglet <b>Transitions</b> de l'<a target="_blank" href="https://www.onlyoffice.com/fr/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Présentations</b></a> permet de gérer les transitions entre les diapositives. Vous pouvez ajouter des effets de transition, définir la durée de la transition et configurer d'autres paramètres des transitions entre les diapositives.</p>
            <div class="onlineDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Présentations en ligne :</p>
                <p><img alt="Onglet Transitions" src="../images/interface/transitionstab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Présentations de bureau :</p>
                <p><img alt="Onglet Transitions" src="../images/interface/desktop_transitionstab.png" /></p>
            </div>
            <p>En utilisant cet onglet, vous pouvez :</p>
            <ul>
                <li>sélectionner <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">l'effet de transition,</a></li>
                <li>configurer <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">les paramètres</a> pour chaque effet de transition,</li>
                <li>définir <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">la durée de transition,</a></li>
                <li> <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">afficher l'aperçu de transition</a> une fois paramétrée,</li>
                <li>contrôler la durée d'affichage de chaque diapositive à l'aide des options <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Démarrer en cliquant et Retard,</a></li>
                <li>appliquer la même transition à toutes les diapositives de la présentation en cliquant sur le bouton <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Appliquer à toutes les diapositives</a>.</li>
            </ul>
		</div>
	</body>
</html>