﻿<!DOCTYPE html>
<html>
	<head>
		<title>RÖMISCH-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>RÖMISCH-Funktion</h1>
			<p>Die Funktion <b>RÖMISCH</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wandelt eine arabische Zahl in römische Zahlzeichen um.</p>
			<p>Die Formelsyntax der Funktion <b>RÖMISCH</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>RÖMISCH(Zahl;[Typ])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
			<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist ein nummerischer Wert, der größer oder gleich 1 und kleiner als 3999 ist, und der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p> 
			<p style="text-indent: 50px;"><b><em>Typ</em></b> ist eine Zahl, die den Typ der römischen Zahl angibt. Mögliche Typen:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Wert</b></td>
					<td><b>Typ</b></td>
				</tr>
				<tr>
					<td>0</td>
					<td>Klassisch</td>
				</tr>
				<tr>
					<td>1</td>
					<td>Kürzer</td>
				</tr>
				<tr>
					<td>2</td>
					<td>Kürzer</td>
				</tr>
				<tr>
					<td>3</td>
					<td>Kürzer</td>
				</tr>
				<tr>
					<td>4</td>
					<td>Vereinfacht</td>
				</tr>
				<tr>
					<td>WAHR</td>
					<td>Klassisch</td>
				</tr>
				<tr>
					<td>FALSCH</td>
					<td>Vereinfacht</td>
				</tr>
			</table>
			<p>Anwendung der Funktion <b>RÖMISCH</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Mathematische und trigonometrische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>RÖMISCH</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="RÖMISCH-Funktion" src="../images/roman.png" /></p>
		</div>
	</body>
</html>