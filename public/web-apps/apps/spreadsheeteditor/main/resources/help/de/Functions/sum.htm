﻿<!DOCTYPE html>
<html>
	<head>
		<title>SUMME-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="Die Funktion SUMME gehört zur Gruppe der mathematischen Funktionen. Erfahren Sie, wie Sie die SUMME-Formel in Tabellen und kompatiblen Dateien nutzen." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>SUMME-Funktion</h1>
			<p>Die Funktion <b>SUMME</b> gehört zur Gruppe der mathematischen und trigonometrischen Funktionen. Sie wird genutzt, um alle Zahlen im gewählten Zellenbereich zu addieren und das Ergebnis zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>SUMME</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>SUMME(Zahl1;[Zahl2];...)</em></b></p> 
			<p>Die Liste der Argumente <b><em>Zahl1;[Zahl2];...</em></b> ist eine Auswahl nummerischer Werte, die manuell eingegeben werden oder in dem Zellbereich eingeschlossen sind, auf den Sie Bezug nehmen.</p>
			<h2>Wie funktioniert SUMME</h2>
			<p>Anwendung der Funktion <b>SUMME</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste und wählen Sie die Funktion <b>SUMME</b> in der Gruppe <b>Mathematische und trigonometrische</b> Funktionen.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas oder wählen Sie den Zellenbereich mit der Maus aus.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><div class = "smb smb-sum"></div></p>
		</div>
	</body>
</html>