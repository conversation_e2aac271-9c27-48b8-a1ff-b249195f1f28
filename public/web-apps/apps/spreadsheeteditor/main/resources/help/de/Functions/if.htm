﻿<!DOCTYPE html>
<html>
	<head>
		<title>WENN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WENN-Funktion</h1>
			<p>Die Funktion <b>WENN</b> gehört zur Gruppe der logischen Funktionen. Sie wird genutzt, um den logischen Ausdruck zu überprüfen und einen Wert, falls es TRUE ist, oder den anderen, falls es FALSE ist, zurückzugeben.</p>
			<p>Die Formelsyntax der Funktion <b>WENN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>WENN(Prüfung;Dann_Wert;[Sonst_Wert])</em></b></p> 
			<p>Die Werte <b><em>Prüfung</em></b>, <b><em>Dann_Wert</em></b> und <b><em>Sonst_Wert</em></b> werdenmanuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>WENN</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Logisch</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WENN</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p><em>Beispiel:</em></p>
			<p>Es gibt 3 Argumente: <em>Prüfung</em> = <b>A1&lt;100</b>, <em>Dann_Wert</em> = <b>0</b>, <em>Sonst_Wert</em> = <b>1</b>; <b>A1</b> ist gleich <b>12</b>. Dieser logische Ausdruck ist <b>WAHR</b>. Also gibt die Funktion den Wert <b>0</b> zurück.</p>
			<p style="text-indent: 150px;"><img alt="WENN-Funktion WAHR" src="../images/iftrue.png" /></p>
			<p>Ändert man den Wert <b>A1</b> von <b>12</b> auf <b>112</b>, gibt die Funktion den Wert <b>1</b> zurück:</p>
			<p style="text-indent: 150px;"><img alt="WENN-Funktion FALSCH" src="../images/iffalse.png" /></p>
		</div>
	</body>
</html>