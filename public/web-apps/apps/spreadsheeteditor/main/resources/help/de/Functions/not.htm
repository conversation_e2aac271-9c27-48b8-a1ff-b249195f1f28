﻿<!DOCTYPE html>
<html>
	<head>
		<title>NICHT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>NICHT-Funktion</h1>
			<p>Die Funktion <b>NICHT</b> gehört zur Gruppe der logischen Funktionen. Mit der Funktion lässt sich überprüfen, ob ein eingegebener logischer Wert WAHR oder FALSCH ist. Ist das Argument FALSCH ist, gibt die Funktion gibt den Wert WAHR zurück und wenn das Argument WAHR ist gibt die Funktion den Wert FALSCH zurück.</p>
			<p>Die Formelsyntax der Funktion <b>NICHT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>NICHT(Wahrheitswert)</em></b></p> 
			<p><b><em>Wahrheitswert</em></b> wird manuell eingegeben oder ist in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>NICHT</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Logisch</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>NICHT</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p><em>Beispiel:</em></p>
			<p>Es liegt das folgende Argument vor: <em>Wahrheitswert</em> = <b>A1&lt;100</b>, es gilt <b>A1</b> ist <b>12</b>. Dieser logische Ausdruck ist <b>WAHR</b>. Also gibt die Funktion den Wert <b>FALSCH</b> zurück.</p>
			<p style="text-indent: 150px;"><img alt="NICHT-Funktion: FALSCH" src="../images/notfalse.png" /></p>
			<p>Ändert man den Wert <b>A1</b> von <b>12</b> auf <b>112</b>, gibt die Funktion den Wert <b>WAHR</b> zurück:</p>
			<p style="text-indent: 150px;"><img alt="NICHT-Funktion: WAHR" src="../images/nottrue.png" /></p>
		</div>
	</body>
</html>