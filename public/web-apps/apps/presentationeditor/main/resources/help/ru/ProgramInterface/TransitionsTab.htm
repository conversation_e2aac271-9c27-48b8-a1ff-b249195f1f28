<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Переходы</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора презентаций - Вкладка Переходы" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Вкладка Переходы</h1>
            <p>Вкладка <b>Переходы</b> <a target="_blank" href="https://www.onlyoffice.com/ru/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Редактора презентаций</b></a> позволяет управлять переходами между слайдами. Вы можете добавлять эффекты перехода, устанавливать скорость перехода и настраивать другие параметры перехода слайдов.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора презентаций:</p>
                <p><img alt="Вкладка Переходы" src="../images/interface/transitionstab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора презентаций:</p>
                <p><img alt="Вкладка Переходы" src="../images/interface/desktop_transitionstab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>выбирать <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">эффект перехода</a>,</li>
                <li>устанавливать соответствующие <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">значения параметров</a> для каждого эффекта перехода,</li>
                <li>определять a <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">продолжительность перехода</a>,</li>
                <li><a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">предварительно предпросматривать</a> переходы,</li>
                <li>настраивать необходимое время перехода к следующему слайду при помощи опций <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Запускать щелчком и Задержка</a>,</li>
                <li>применять переход ко всем слайдам при помощи кнопки <a href="../UsageInstructions/ApplyTransitions.htm" onclick="onhyperlinkclick(this)">Применить ко всем слайдам</a>.</li>
            </ul>
		</div>
	</body>
</html>