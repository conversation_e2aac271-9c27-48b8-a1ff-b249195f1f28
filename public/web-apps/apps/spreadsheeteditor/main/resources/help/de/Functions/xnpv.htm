﻿<!DOCTYPE html>
<html>
	<head>
		<title>XKAPITALWERT-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>XKAPITALWERT-Funktion</h1>
			<p>Die Funktion <b>XKAPITALWERT</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie gibt den Nettobarwert (Kapitalwert) einer Reihe nicht periodisch anfallender Zahlungen zurück.</p>
			<p>Die Formelsyntax der Funktion <b>XKAPITALWERT</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>XKAPITALWERT(Zins;Werte;Zeitpkte)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zins</em></b> ist der Kalkulationszinsfuß, der für die Zahlungen zu berücksichtigen ist.</p> 
				<p style="text-indent: 50px;"><b><em>Werte</em></b> ist eine Matrix (ein Array), die (das) die Beträge für Einkommen (positive Werte) oder Zahlungen (negative Werte) enthält. Mindestens einer der Werte muss negativ und mindestens einer muss positiv sein.</p> 
				<p style="text-indent: 50px;"><b><em>Zeitpkte</em></b> sind die Zeitpunkte im Zahlungsplan der nicht periodisch anfallenden Zahlungen.</p> 
			<p>Die nummerischen Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>XKAPITALWERT</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>XKAPITALWERT</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="XKAPITALWERT-Funktion" src="../images/xnpv.png" /></p>
		</div>
	</body>
</html>