﻿<!DOCTYPE html>
<html>
	<head>
		<title>DATEDIF-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>DATEDIF-Funktion</h1>
			<p>Die Funktion <b>DATEDIF</b> gehört zur Gruppe der Daten- und Zeitfunktionen. Sie berechnet die Differenz zwischen zwei Datumsangaben (Start- und Enddatum), basierend auf der angegebenen Einheit.</p>
			<p>Die Formelsyntax der Funktion <b>DATEDIF</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>DATEDIF(Ausgangsdatum;Enddatum;Einheit)</em></b></p> 
			<p><em>Dabei gilt:</em></p>
			<p style="text-indent: 50px;"><b><em>Ausgangsdatum</em></b> und <b><em>Enddatum</em></b> sind die beiden Datumswerte, für die Sie die dazwischen liegenden Tage berechnen möchten.</p>
			<p style="text-indent: 50px;"><b><em>Einheit</em></b> ist das angegebene Zeitintervall. Mögliche Einheiten sind:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Einheit</b></td>
					<td><b>Bedeutung</b></td>
				</tr>
				<tr>
					<td>J</td>
					<td>Die Anzahl der vollständigen Jahre im Zeitraum</td>
				</tr>
				<tr>
					<td>M</td>
					<td>Die Anzahl der vollständigen Monate im Zeitraum</td>
				</tr>
				<tr>
					<td>T</td>
					<td>Die Anzahl der vollständigen Tage im Zeitraum</td>
				</tr>
				<tr>
					<td>MT</td>
					<td>Die Differenz zwischen den Tagen (Monate und Jahre werden ignoriert).</td>
				</tr>
				<tr>
					<td>JM</td>
					<td>Die Differenz zwischen den Monaten (Tage und Jahre werden ignoriert).</td>
				</tr>
				<tr>
					<td>JT</td>
					<td>Die Differenz zwischen den Tagen (Jahre werden ignoriert).</td>
				</tr>
			</table>
			<p>Anwendung der Funktion <b>DATEDIF</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Datums- und Uhrzeitfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>DATEDIF</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="DATEDIF-Funktion" src="../images/datedif.png" /></p>
		</div>
	</body>
</html>