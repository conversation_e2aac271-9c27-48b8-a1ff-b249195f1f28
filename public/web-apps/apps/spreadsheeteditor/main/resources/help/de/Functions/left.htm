﻿<!DOCTYPE html>
<html>
	<head>
		<title>LINKS/LINKSB-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>LINKS/LINKSB-Funktion</h1>
			<p>Die Funktionen <b>LINKS/LINKSB</b> gehören zur Gruppe der Text- und Datenfunktionen. Sie gibt auf der Grundlage der Anzahl von Zeichen/Bytes, die Sie angeben, das oder die erste(n) Zeichen in einer Textzeichenfolge zurück. Die Funktion <b>LINKS</b> ist für Sprachen gedacht, die den Single-Byte-Zeichensatz (SBCS) verwenden, während <b>LINKS</b> für Sprachen verwendet wird, die den Doppelbyte-Zeichensatz (DBCS) verwenden, wie Japanisch, Chinesisch, Koreanisch usw.</p>
			<p>Die Formelsyntax der Funktionen <b>LINKS/LINKSB</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>LINKS(Text;[Anzahl_Zeichen])</em></b></p>
			<p style="text-indent: 150px;"><b><em>LINKSB(Text;[Anzahl_Bytes])</em></b></p>
			<p><em>Dabei gilt:</em></p>
				<p style="text-indent: 50px;"><b><em>Text</em></b> ist die Zeichenfolge mit den Zeichen, die Sie extrahieren möchten.</p> 
				<p style="text-indent: 50px;"><b><em>Anzahl_Zeichen</em></b> gibt die Anzahl der Zeichen an, die von der Funktion extrahiert werden sollen. Dieses Argument ist optional. Fehlt das Argument, wird es als 1 angenommen.</p>
			<p>Die Daten können manuell eingegeben werden oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>LINKS/LINKSB</b>.</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Anwendung der Funktion <b>LINKS/LINKSB</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
            <p style="text-indent: 150px;"><img alt="LINKS/LINKSB-Funktion" src="../images/left.png" /></p>
		</div>
	</body>
</html>