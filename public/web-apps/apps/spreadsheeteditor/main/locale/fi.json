{"cancelButtonText": "Peruuta", "Common.Controllers.Chat.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "Common.Controllers.Chat.textEnterMessage": "Syötä viestisi tässä", "Common.UI.ButtonColored.textNewColor": "Lisää uusi mukautettu väri", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON> t<PERSON>", "Common.UI.ExtendedColorDialog.addButtonText": "Lisää", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "Syötetty arvo ei ole o<PERSON>in. Ole hyvä ja syötä arvo välillä 000000 ja FFFFFF", "Common.UI.ExtendedColorDialog.textNew": "<PERSON>us<PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Syötetty arvo ei ole o<PERSON>.<br>Ole hyvä ja syötä numeerinen arvo välillä 0 ja 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> väriä", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON> t<PERSON>", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON><PERSON><PERSON>/pienten kirjainten mukaan", "Common.UI.SearchDialog.textReplaceDef": "Syötä korvaava teksti", "Common.UI.SearchDialog.textSearchStart": "Syötä tekstisi tässä", "Common.UI.SearchDialog.textTitle": "<PERSON>ts<PERSON> ja <PERSON>", "Common.UI.SearchDialog.textTitle2": "Etsi", "Common.UI.SearchDialog.textWholeWords": "<PERSON>ain koko<PERSON>set sanat", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON>", "Common.UI.SynchronizeTip.textDontShow": "Älä näytä tätä viestiä uudelleen", "Common.UI.SynchronizeTip.textSynchronize": "Asiakirja on toisen käyttäjän muuttama.<br>Ole hyvä ja klikkaa tallentaaksesi muutoksesi ja lataa uudelleen muutokset.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Teeman värit", "Common.UI.Window.cancelButtonText": "Peruuta", "Common.UI.Window.closeButtonText": "Sulje", "Common.UI.Window.noButtonText": "<PERSON>i", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "Älä näytä tätä viestiä uudelleen", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textWarning": "Varo<PERSON><PERSON>", "Common.UI.Window.yesButtonText": "K<PERSON><PERSON>ä", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "osoite: ", "Common.Views.About.txtLicensee": "LISENSSINSAAJA", "Common.Views.About.txtLicensor": "LISENSSINANTAJA", "Common.Views.About.txtMail": "sähköposti: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "puh.: ", "Common.Views.About.txtVersion": "Versio", "Common.Views.Chat.textSend": "Lähetä", "Common.Views.Comments.textAdd": "Lisää", "Common.Views.Comments.textAddComment": "Lisää kommentti", "Common.Views.Comments.textAddCommentToDoc": "Lisää kommentti asiakirjaan", "Common.Views.Comments.textAddReply": "Lisää <PERSON>", "Common.Views.Comments.textAnonym": "Vierailija", "Common.Views.Comments.textCancel": "Peruuta", "Common.Views.Comments.textClose": "Sulje", "Common.Views.Comments.textComments": "Ko<PERSON>ntit", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Syötä kommenttisi tässä", "Common.Views.Comments.textHintAddComment": "Lisää kommentti", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>", "Common.Views.Comments.textReply": "Vastaus", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Ratkaist<PERSON>", "Common.Views.CopyWarningDialog.textDontShow": "Älä näytä tätä viestiä uudelleen", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, le<PERSON><PERSON><PERSON><PERSON> ja liittämisen toiminnot muokkaajan työkaluvalikon painikkeilla ja valikoiden toiminnoilla suoritetaan vain tässä muokkaajan välilehdessä.<br><br>Jo<PERSON> haluat kopioida tai liittää muokkaajan ulkopuolisiin sovelluksiin tai sovelluksista, niin voit käyttää seuraavia näppäimistöyhdistelmiä:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, leikkaa ja liitä toim<PERSON>ot", "Common.Views.CopyWarningDialog.textToCopy": "<PERSON><PERSON><PERSON><PERSON> varten", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON><PERSON><PERSON><PERSON> varten", "Common.Views.CopyWarningDialog.textToPaste": "Liittämistä varten", "Common.Views.DocumentAccessDialog.textLoading": "Ladataan...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "<PERSON><PERSON><PERSON>", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON>", "Common.Views.ImageFromUrlDialog.textUrl": "<PERSON>it<PERSON> kuvan verkko-osoite:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Tämä kenttä tarvitaan", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON> tied<PERSON>on tulisi olla verkko-osoite \"http://www.esimerkki.com\" muodossa", "Common.Views.OpenDialog.txtDelimiter": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtTab": "Välilehti", "Common.Views.OpenDialog.txtTitle": "Valitse %1 vaihtoehtoa", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Varoitus: <PERSON><PERSON> ka<PERSON> tai unoh<PERSON>, sitä ei voi palauttaa. Säilytä sitä turvallisessa pai<PERSON>.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Aloita", "Common.Views.Protection.hintSignature": "Lisää digitaalinen allekirjoitus tai", "Common.Views.Protection.txtAddPwd": "Lis<PERSON><PERSON> sa<PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Lisää digitaalinen allekirjoitus", "Common.Views.RenameDialog.textName": "Tiedoston nimi", "Common.Views.RenameDialog.txtInvalidName": "Tiedoston nimessä ei voi olla seuraavia merkkejä:", "Common.Views.ReviewChanges.tipAcceptCurrent": "Hyväksy äskettäiset muutokset", "Common.Views.ReviewChanges.txtAccept": "Hyväksy", "Common.Views.ReviewChanges.txtAcceptAll": "Hyväksy kaikki muutokset", "Common.Views.ReviewChanges.txtAcceptChanges": "Hyväksy muutokset", "Common.Views.ReviewChanges.txtAcceptCurrent": "Hyväksy äskettäiset muutokset", "Common.Views.ReviewPopover.textAdd": "Lisää", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Keskellä", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON>ista sarake", "SSE.Controllers.DocumentHolder.deleteRowText": "Poista rivi", "SSE.Controllers.DocumentHolder.deleteText": "Poista", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Linkin viittausta ei ole o<PERSON>. Ole hyvä ja korjaa linkki tai poista se.", "SSE.Controllers.DocumentHolder.guestText": "Vierailija", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> sarake", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> sarake", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "Lisää", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Sarak<PERSON><PERSON> Leveys {0} symbolit ({1} pikseliä)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON> k<PERSON>us {0} p<PERSON><PERSON><PERSON> ({1} pik<PERSON>i<PERSON>)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Paina CTRL näppäintä ja klikkaa linkkiä", "SSE.Controllers.DocumentHolder.textInsertLeft": "Lisää vasemmalle", "SSE.Controllers.DocumentHolder.textInsertTop": "Lisää ylös", "SSE.Controllers.DocumentHolder.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Controllers.DocumentHolder.txtAboveAve": "Keskimääräistä enemmän", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Lisää murtoluku pylväs", "SSE.Controllers.DocumentHolder.txtAddHor": "Lisää vaakalinja", "SSE.Controllers.DocumentHolder.txtAddLB": "Lisää vasen alaviiva", "SSE.Controllers.DocumentHolder.txtAddLeft": "Lisää vasen reunus", "SSE.Controllers.DocumentHolder.txtAddLT": "Lisää vasen yläviiva", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON>s<PERSON><PERSON> o<PERSON>a reunus", "SSE.Controllers.DocumentHolder.txtAddTop": "Lisää yläreunus", "SSE.Controllers.DocumentHolder.txtAddVer": "Lisää vertikaalinen linja", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAll": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Vähennä <PERSON>in kokoa", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON>ti", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Poista manuaalinen katkos", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Poista sisäänsulkevat kirjaimet", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Poista sisäänsulkevat kirjaimet ja erottimet", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Poista yhtälö", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON>ista kirjain", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON> j<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Vaihda lineaariseen murtolukuun", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON><PERSON> vinoutettuun murtolukuun", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON><PERSON><PERSON> pinottuun murtol<PERSON>un", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON> al<PERSON>us", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON> raj<PERSON>", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON> o<PERSON>", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON> aste", "SSE.Controllers.DocumentHolder.txtHideHor": "Piilota vaakaviiva", "SSE.Controllers.DocumentHolder.txtHideLB": "Piilota vasen alaviiva", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON> vasen reunus", "SSE.Controllers.DocumentHolder.txtHideLT": "Piilota vasen yläviiva", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Piilota vasen ha<PERSON>ulje", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON> p<PERSON>", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON> o<PERSON>a reunus", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON> y<PERSON>", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON> raj<PERSON>", "SSE.Controllers.DocumentHolder.txtHideVer": "Piilota vertikaalinen viiva", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Lisää argumentin kokoa", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Lisää <PERSON>ti jälk<PERSON>", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Lisää argumentti ennen", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Lisää manuaalinen katkaisu", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Lisää yhtälö jälkeen", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Lisää yhtälö ennen", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON><PERSON> r<PERSON> si<PERSON>", "SSE.Controllers.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON> te<PERSON> alla", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON> argumentin k<PERSON>en", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "Ei ole vaiht<PERSON>htoja solun täyttämiseksi.<br><PERSON><PERSON> sarakkeen tekstiarvoja voidaan valita tai korvata.", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Poista murtoluku pylväs", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON><PERSON> r<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Poista kirjainaksentti ", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Poista pylväs", "SSE.Controllers.DocumentHolder.txtRemScripts": "Poista skriptit", "SSE.Controllers.DocumentHolder.txtRemSubscript": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Poista yläindeksi", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skriptit tekstin j<PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Skriptit ennen tekstiä", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON>äyt<PERSON> raj<PERSON>", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Näytä oikea ha<PERSON>je", "SSE.Controllers.DocumentHolder.txtShowDegree": "Näytä aste", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Näytä vasen ha<PERSON>je", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Näytä paikkamerkki", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Näytä yläraj<PERSON>us", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Venyvät Hakasulkeet", "SSE.Controllers.DocumentHolder.txtTop": "Yläosa", "SSE.Controllers.DocumentHolder.txtUnderbar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.newDocumentTitle": "Laskutaulukko on ilman nimeä", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON> solun si<PERSON>", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON> t<PERSON>:", "SSE.Controllers.LeftMenu.textNoTextFound": "Etsimääsi tietoa ei l<PERSON>. Ole hyvä ja muokkaa hakuva<PERSON>eh<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON><PERSON><PERSON> on suoritettu. {0} tap<PERSON><PERSON> oh<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> on suoritettu. Korvaustapahtumia: {0}", "SSE.Controllers.LeftMenu.textSearch": "Etsi", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Arvot", "SSE.Controllers.LeftMenu.textWarning": "Varo<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWithin": "Sisällä", "SSE.Controllers.LeftMenu.textWorkbook": "Työkirja", "SSE.Controllers.LeftMenu.warnDownloadAs": "<PERSON>s jatkat tässä muo<PERSON>, niin kaikki o<PERSON>, p<PERSON><PERSON> te<PERSON>, men<PERSON><PERSON><PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> varma että haluat jatkaa?", "SSE.Controllers.Main.confirmMoveCellRange": "Kohdesolujen tietoalue voi sisältää tietoa. Haluatko jatkaa toimintoa?", "SSE.Controllers.Main.confirmPutMergeRange": "Lähdetiedot sisälsivät yhdistettyjä soluja.<br>Ne on purettu ennenkuin ne liitettiin tauluk<PERSON>on.", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON> \"OK\" niin voit palata asiakirjaluetteloon.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> e<PERSON>.", "SSE.Controllers.Main.downloadTextText": "Ladataan työkirjaa...", "SSE.Controllers.Main.downloadTitleText": "Ladataan työkirjaa", "SSE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>, johon sinulla ei ole k<PERSON>töoikeuk<PERSON>.<br>Ole hyvä ja ota yhteyttä asiakirjan palvelimen pääkäyttäjään.", "SSE.Controllers.Main.errorArgsRange": "<PERSON><PERSON><PERSON> s<PERSON> ka<PERSON>.<br><PERSON><PERSON><PERSON><PERSON> vir<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>.", "SSE.Controllers.Main.errorAutoFilterChange": "Toiminto ei ole sallittu, koska se yrittää vaihtaa soluja työkirjasi tauluk<PERSON>on.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Toimintoa ei voida suorittaa valituille soluille koska et voi siirtää taulukon osaa.<bR><PERSON><PERSON><PERSON> toinen tietoalue niin että koko taulukko siirretään ja yritä uudelleen.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Toimintoa ei voida suorittaa valitulle solujen tietoalueelle.<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tietoalue, joka eroa<PERSON>, ja yrit<PERSON> uude<PERSON>.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Toimintoa ei voida suorittaa, koska alue sisältää suodatettuja soluja.<br>Ole hyvä ja näytä suodatetut elementit ja yritä uudelleen.", "SSE.Controllers.Main.errorBadImageUrl": "<PERSON><PERSON> ve<PERSON>-osoite on virheellinen", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Palvelimen yhteys menetetty. Asiakirjaa ei voida tällä hetkellä muokata.", "SSE.Controllers.Main.errorConnectToServer": "Asiakirjaa ei voitu tallentaa. Ole hyvä ja tarkista yhteysasetukset tai ota yhteyttä pääkäyttäjään.<br>Ku<PERSON> klikkaat 'OK' painiketta, sinua pyydetään lataamaan asiakirja.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Tätä komentoa ei voida käyttää useisiin valintoihin.<br>Valitse yksi tietoalue ja yritä uudelleen.", "SSE.Controllers.Main.errorCountArg": "<PERSON><PERSON>he s<PERSON>ötety<PERSON> kaavassa.<br><PERSON><PERSON><PERSON><PERSON> väärää argumenttien määrää.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON><PERSON><PERSON> s<PERSON> ka<PERSON>.<br><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> y<PERSON>.", "SSE.Controllers.Main.errorCreateDefName": "Olemassaolevia nimettyjä tietoalueita ei voida muokata ja uusia ei voida luoda<br>tällä hetkellä koska jotain niistä muokataan tällä hetkellä.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON><PERSON> virhe.<br>Tietokannan yhteysvirhe. Ole hyvä ja ota yhteyttä asiakastukeen, jos virhe tuntuu pysyvän.", "SSE.Controllers.Main.errorDataRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> tie<PERSON>", "SSE.Controllers.Main.errorDefaultMessage": "Virhekoodi: %1", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON><PERSON><PERSON> on suojattu salasanalla ja sitä ei voitu avata.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON><PERSON> virhe.<br><PERSON>ied<PERSON><PERSON> py<PERSON><PERSON>n virhe. Ole hyvä ja ota yhteyttä asiakastukeen, jos vir<PERSON><PERSON><PERSON> jatku<PERSON>.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON><PERSON> virhe.<br><PERSON><PERSON><PERSON><PERSON><PERSON> turva-avain. Ole hyvä ja ota yhteyttä asiakastukeen, jos vir<PERSON><PERSON><PERSON> jatku<PERSON>.", "SSE.Controllers.Main.errorFillRange": "Ei voitu täyttää valittua solualuetta.<br><PERSON><PERSON><PERSON> y<PERSON>ste<PERSON>t solut tulee olla samaa kokoa.", "SSE.Controllers.Main.errorFormulaName": "<PERSON><PERSON><PERSON> s<PERSON> kaavassa.<br><PERSON><PERSON><PERSON><PERSON> virheellistä kaavan nimeä.", "SSE.Controllers.Main.errorFormulaParsing": "<PERSON>säinen virhe jäsentäess<PERSON> kaavaa", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funktio vii<PERSON><PERSON>, jota ei ole o<PERSON>.<br>Ole hyvä ja tarkista tiedot ja yritä uudelleen.", "SSE.Controllers.Main.errorInvalidRef": "Syötä oikea nimi valinnalle tai oikea viittaus siirtym<PERSON>en.", "SSE.Controllers.Main.errorKeyEncrypt": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorLockedAll": "Toimintoa ei voida suorittaa koska taulukko on toisen käyttäjän lukitsema.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Taulukkoa ei voida nimetä uudelleen, koska sitä ollaan nimeämässä uudelleen toisen käyttäjän toimesta", "SSE.Controllers.Main.errorMoveRange": "Ei voida muuttaa yhdi<PERSON>tyn solun osaa", "SSE.Controllers.Main.errorOpenWarning": "<PERSON><PERSON><PERSON> kaavan pituus tied<PERSON><PERSON> on ylittänyt<br>salli<PERSON> merk<PERSON>r<PERSON>n ja se poiste<PERSON>in.", "SSE.Controllers.Main.errorOperandExpected": "Syötetty funktio ei ole o<PERSON>. Ole hyvä ja tarkista jos puuttuu yksi suluista - '(' tai ')'.", "SSE.Controllers.Main.errorPasteMaxRange": "Ko<PERSON><PERSON>in ja liittämisen alueet eivät vastaa toisiaan.<br>Ole hyvä ja valitse alue, jolla on sama koko tai klikkaa rivin ensimmäistä solua, josta alkaen liität kopioitavat solut.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Valitettavasti ei ole mahdollista tulostaa enempää kuin 1500 sivua kerralla nykyisessä ohjelman versiossa.<br>Tämä rajoitus tulee poistumaan tulevissa versioissa.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON> e<PERSON>", "SSE.Controllers.Main.errorSessionAbsolute": "<PERSON><PERSON><PERSON><PERSON> istunnon aika on erääntynyt. Ole hyvä ja lataa uudelleen sivu.", "SSE.Controllers.Main.errorSessionIdle": "Asiakirjaa ei ole muokattu pitk<PERSON>än a<PERSON>an. Ole hyvä ja lataa sivu uudelleen.", "SSE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON><PERSON> pal<PERSON> on keskeytynyt. Ole hyvä ja lataa uudelleen sivu.", "SSE.Controllers.Main.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorToken": "Asiakirjan turvatunnus ei ole oikeassa muodossa.<br>Ole hyvä ja ota yhteyttä asiakirjan palvelimen pääkäyttäjään.", "SSE.Controllers.Main.errorTokenExpire": "Asiakirjan turvatun<PERSON> on erääntynyt.<br>Ole hyvä ja ota yhteyttä asiakirjan palvelimen pääkäyttäjään.", "SSE.Controllers.Main.errorUnexpectedGuid": "Ulk<PERSON>nen virhe.<br>Odottamaton GUID arvo. Ole hyvä ja ota yhteyttä asiakastukeen, jos vir<PERSON><PERSON><PERSON> jatku<PERSON>.", "SSE.Controllers.Main.errorUpdateVersion": "Tiedoston versio on muuttunut. Sivu ladataan uudelleen.", "SSE.Controllers.Main.errorUserDrop": "Tiedostoon ei ole pääsyä tällä hetkellä.", "SSE.Controllers.Main.errorUsersExceed": "Palvelupaketin sallittu käyttäjämäärä on ylitetty", "SSE.Controllers.Main.errorViewerDisconnect": "Yhteys on menetetty. Voit vielä selailla as<PERSON>,<br>mutta et voi ladata tai tulostaa sitä ennenkuin yhteys on palautettu.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON><PERSON> s<PERSON> ka<PERSON>.<br><PERSON><PERSON><PERSON><PERSON> virheellinen määrä <PERSON>.", "SSE.Controllers.Main.errorWrongOperator": "Virhe syötetyssä kaavassa. Käytetty väärää operaattoria.<br>Ole hyvä ja korjaa virhe.", "SSE.Controllers.Main.leavePageText": "<PERSON><PERSON><PERSON> on tallentamattomia muutoksia tässä taulukossa. Klikkaa '<PERSON><PERSON><PERSON> tälle sivulle', ja sitten 'Tall<PERSON><PERSON>', jotta voit tallentaa muutokset. Klikkaa 'Jätä tämä sivu' niin voit jättää huomioimatta kaikki tallentamattomat muutokset.   ", "SSE.Controllers.Main.loadFontsTextText": "Ladataan tietoa...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadFontTextText": "Ladataan tietoa...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadImagesTextText": "Ladataan kuvia...", "SSE.Controllers.Main.loadImagesTitleText": "Ladataan kuvia", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON><PERSON> kuvaa...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON> kuvaa", "SSE.Controllers.Main.loadingDocumentTitleText": "Ladataan työkirjaa", "SSE.Controllers.Main.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.openTextText": "Avataan ta<PERSON>k<PERSON>laskentaohjelmaa...", "SSE.Controllers.Main.openTitleText": "Avataan ta<PERSON>las<PERSON>oh<PERSON>a", "SSE.Controllers.Main.pastInMergeAreaError": "Ei voida muuttaa yhdi<PERSON>tyn solun osaa", "SSE.Controllers.Main.printTextText": "Tulostetaan <PERSON>k<PERSON>a...", "SSE.Controllers.Main.printTitleText": "Tuloste<PERSON>an <PERSON>", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON> u<PERSON> sivu", "SSE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON> on paraikaa muokkaamassa tätä asiakirjaa. Ole hyvä ja yritä myö<PERSON> uudelleen.", "SSE.Controllers.Main.requestEditFailedTitleText": "Käyttö estetty. ", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> tall<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.saveTextText": "Tallennetaan <PERSON>...", "SSE.Controllers.Main.saveTitleText": "Tallenne<PERSON>an <PERSON>", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON> sivu<PERSON>", "SSE.Controllers.Main.textCloseTip": "Klikkaa ja sulje vinkki", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textContactUs": "<PERSON><PERSON> yht<PERSON> my<PERSON>", "SSE.Controllers.Main.textLoadingDocument": "Ladataan työkirjaa", "SSE.Controllers.Main.textNo": "<PERSON>i", "SSE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE avoimen lähdekoodin versio", "SSE.Controllers.Main.textPleaseWait": "Toiminto voi kestää odotettua kauemmin. Ole hyvä ja odota....", "SSE.Controllers.Main.textShape": "<PERSON><PERSON>", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON><PERSON> tila", "SSE.Controllers.Main.textText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textTryUndoRedo": "Peruutus/tee uudelleen -toiminnot eivät ole käytössä yhteismuokkauksen pikatilassa.<br><PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" tilan painiketta, jotta voit vaihtaa ehdottomaan yhteismuokkauksen tilaan tai muokata tiedostoa ilman että muut käyttäjät häiritsevät sitä. Tässä tilassa lähetät muutokset vain kun olet tallentanut ne. Voit vaihdella yhteismuokkauksen tilojen välillä editorin Laajennetuissa asetuksissa.", "SSE.Controllers.Main.textYes": "K<PERSON><PERSON>ä", "SSE.Controllers.Main.titleLicenseExp": "Lisenssi <PERSON>", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "SSE.Controllers.Main.txtBasicShapes": "Perusmuodot", "SSE.Controllers.Main.txtButtons": "Painikkeet", "SSE.Controllers.Main.txtCallouts": "Huomiotekstit", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtEditingMode": "<PERSON><PERSON> muo<PERSON> tila...", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtLines": "Viivat", "SSE.Controllers.Main.txtMath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "\"Ei\" Symbooli", "SSE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON><PERSON> T<PERSON>", "SSE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON> T<PERSON>i", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtStarsRibbons": "Tähdet & Nauhat", "SSE.Controllers.Main.txtXAxis": "X akseli", "SSE.Controllers.Main.txtYAxis": "<PERSON> akseli", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON><PERSON> virhe.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Selaintasi ei ole tuettu", "SSE.Controllers.Main.uploadImageExtMessage": "<PERSON><PERSON><PERSON><PERSON> kuvan muoto.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON>i ladatt<PERSON>ja kuvia.", "SSE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON><PERSON><PERSON> kuvan koon raj<PERSON>us y<PERSON>.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON> kuvaa...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON> kuvaa", "SSE.Controllers.Main.warnBrowserIE9": "Sovelluksella on alhaiset käyttöominaisuudet IE9 selaimella tai aikaisemalla. Käytä IE10 selainta tai uudempaa", "SSE.Controllers.Main.warnBrowserZoom": "Selaimesi nykyinen suuren<PERSON> asetus ei ole täysin tuettu. Ole hyvä ja aseta päälle selaimesi oletussuurennos näppäinkomennolla Ctrl+0.", "SSE.Controllers.Main.warnLicenseExp": "Lisenssisi on erääntynyt.<br>Ole hyvä ja päivitä lisenssisi ja virkistä sivu.", "SSE.Controllers.Main.warnNoLicense": "Olet käyttämässä %1 avoimen lähdekoodin versiota. Versiolla on rajoituksia yhtäaikaisten yhteyksien määrän suhteen asiakirjan palvelimelle (20 yhteyttä samaan aikaan).<br><PERSON><PERSON> lis<PERSON>, niin voit harkita kaupallista lisenssiä.", "SSE.Controllers.Main.warnProcessRightsChange": "Sinula ei ole riittävästi oikeuksia muokata tiedostoa.", "SSE.Controllers.Print.strAllSheets": "Kaikki taulukot", "SSE.Controllers.Print.textWarning": "Varo<PERSON><PERSON>", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON><PERSON><PERSON> ovat virheellis<PERSON>ä", "SSE.Controllers.Statusbar.errorLastSheet": "Työkirjalla tulee olla vähintään yksi näkyvä taulukko.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Ei voida poistaa tauluk<PERSON>a.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.warnDeleteSheet": "Työkirja voi sisältää tietoja. <PERSON><PERSON><PERSON> varma että haluat jatkaa?", "SSE.Controllers.Statusbar.zoomText": "<PERSON><PERSON><PERSON> {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON>, jota o<PERSON>, ei ole saatavilla nykyisessä laitteessa.<br><PERSON><PERSON><PERSON> tyyli näytetään käyttämällä järjestelmän fontteja, tallennettua fonttia käytetään kun se on saatavilla.<br><PERSON><PERSON><PERSON><PERSON> jatkaa?", "SSE.Controllers.Toolbar.textAccent": "Aksentit", "SSE.Controllers.Toolbar.textBracket": "Hakasulkeet", "SSE.Controllers.Toolbar.textFontSizeErr": "Syötetty arvo ei ole o<PERSON>.<br>Ole hyvä ja syötä numeerinen arvo välillä 1-409", "SSE.Controllers.Toolbar.textFraction": "Murtoluvut", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIntegral": "Integraalit", "SSE.Controllers.Toolbar.textLargeOperator": "Isot operaattorit", "SSE.Controllers.Toolbar.textLimitAndLog": "<PERSON><PERSON><PERSON><PERSON> ja logaritmit", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operaattorit", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "Skriptit", "SSE.Controllers.Toolbar.textSymbols": "Symbolit", "SSE.Controllers.Toolbar.textWarning": "Varo<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Oikea-Vasen nuoli y<PERSON>äällä", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Vasemmalle suunnattu nuoli ylhäällä", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Oikealle suunnattu nuoli ylhäällä ", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Alaviiva", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Yläviiva", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> (paikkamerkillä)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON>ati<PERSON><PERSON><PERSON>(esimerkki)", "SSE.Controllers.Toolbar.txtAccent_Check": "Tarkista", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Alasulku", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Yläsul<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektori A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC yläpalkilla", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y yläviivalla", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Ryhmittelevä kirjain al<PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Ryhmittelevä kirjain y<PERSON>", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Vasemmalle suunnattu harppuuna yläpuolella", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Oikealle suunnattu harppuuna ylhäällä", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Tapauksia (<PERSON><PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Tapauksia (<PERSON><PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Pino-objekti", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Pino-objekti", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binominen kerroin", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binominen kerroin", "SSE.Controllers.Toolbar.txtBracket_Line": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Vinoutunut murtoluku", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Lineaarinen murtoluku", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi yli 2", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> m<PERSON>", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON> murtol<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Käänteinen kosinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolinen Käänteinen Kosinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Käänteinen Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolinen Käänteinen Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Käänteinen Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolinen Käänteinen Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Käänteinen Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolinen Käänteinen Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Käänteinen Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolinen Käänteinen Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Käänteinen Tangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolinen Käänteinen Tangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolinen Kosinifunktio", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolinen Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Csc": "Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolinen Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sini theeta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sec": "Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolinen Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolinen Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolinen Tangenttifunktio", "SSE.Controllers.Toolbar.txtIntegral": "Integraali", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differentiaalinen theeta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differentiaalinen x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differentiaalinen y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integraali", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOriented": "Inte<PERSON><PERSON><PERSON> muoto", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Inte<PERSON><PERSON><PERSON> muoto", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integra<PERSON><PERSON> pinta", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integra<PERSON><PERSON> pinta", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integra<PERSON><PERSON> pinta", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Inte<PERSON><PERSON><PERSON> muoto", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integra<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integra<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integra<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integraali", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "leik<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Liitos", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON><PERSON><PERSON> logaritmi", "SSE.Controllers.Toolbar.txtLimitLog_Log": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Perusviivan pisteet", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON><PERSON> pisteet", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON><PERSON><PERSON> pisteet", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON> pys<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> matriisi", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identiteetin matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 identiteetin matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identiteetin matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identiteetin matriisi", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Oikea-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Oikea-Vasen nuoli y<PERSON>äällä", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Vasemmalle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Vasemmalle suunnattu nuoli ylhäällä", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Oikealle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Oikealle suunnattu nuoli ylhäällä ", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Kaksoispiste yhtäsuuri", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta tuotokset", "SSE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON><PERSON><PERSON> määritelmän mukaan", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta yhtäkuin", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Oikea-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Oikea-Vasen nuoli y<PERSON>äällä", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Vasemmalle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Vasemmalle suunnattu nuoli ylhäällä", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Oikealle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Oikealle suunnattu nuoli ylhäällä ", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Yhtäsuuri", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Mitattuna:", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSup": "Alaindeksi-Yläindeksi", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Vasen AlaIndeksi-Yläindeksi", "SSE.Controllers.Toolbar.txtScriptSup": "Yläindeksi", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "Täydentää", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ast": "Asteriski Operaattori", "SSE.Controllers.Toolbar.txtSymbol_beta": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Pallukka operaattori", "SSE.Controllers.Toolbar.txtSymbol_cap": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Keskilinjan v<PERSON>elli<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON> as<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Suunnilleen y<PERSON>äkuin", "SSE.Controllers.Toolbar.txtSymbol_cup": "Liitos", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Alas oikealla diagonaalinen ellipsi", "SSE.Controllers.Toolbar.txtSymbol_degree": "Astetta", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identtinen:", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "On olemassa", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Kertoma", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Fahrenheit astetta", "SSE.Controllers.Toolbar.txtSymbol_forall": "Kaikille", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Suurempi kuin tai yhtä<PERSON>uri kuin", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON>jon suurempi kuin", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON>ure<PERSON><PERSON> kuin", "SSE.Controllers.Toolbar.txtSymbol_in": "Elementtinä:", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Ääretön", "SSE.Controllers.Toolbar.txtSymbol_iota": "Hitunen", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leq": "Vähemmän kuin tai yhtäkuin", "SSE.Controllers.Toolbar.txtSymbol_less": "Vähemmä<PERSON> kuin", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>jon vähemmän kuin", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON>i ole <PERSON>in", "SSE.Controllers.Toolbar.txtSymbol_ni": "Sisältyy j<PERSON>ä", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON>i merkki", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON> <PERSON>le o<PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_percent": "Prosenttia", "SSE.Controllers.Toolbar.txtSymbol_phi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_pi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus Miinus", "SSE.Controllers.Toolbar.txtSymbol_propto": "<PERSON><PERSON><PERSON><PERSON>:", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON> ju<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON><PERSON><PERSON><PERSON> loppu", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Ylhäällä oikealla diagonaalinen ellipsi", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theeta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon muunnos", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON> m<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON> muunnos", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON> muunnos", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma muunnos", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON> muunnos", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.warnMergeLostData": "Vain tieto ylhäällä vasemmalla olevassa solussa jää yhdistettyyn soluun.<br><PERSON><PERSON><PERSON> varma että haluat jatkaa?", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON> suodatin", "SSE.Views.AutoFilterDialog.textAddSelection": "Lisää n<PERSON>yinen valinta suoda<PERSON>n", "SSE.Views.AutoFilterDialog.textEmptyItem": "{<PERSON><PERSON>j<PERSON>}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON> kaikki", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Valitse kaikki haku<PERSON>", "SSE.Views.AutoFilterDialog.textWarning": "Varo<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAboveAve": "Keskimääräistä enemmän", "SSE.Views.AutoFilterDialog.txtBegins": "alkaa...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Keskimääräistä vähemmän", "SSE.Views.AutoFilterDialog.txtBetween": "Välillä...", "SSE.Views.AutoFilterDialog.txtClear": "Tyhjennä", "SSE.Views.AutoFilterDialog.txtContains": "Sisältää...", "SSE.Views.AutoFilterDialog.txtEmpty": "Syötä solun suodatin", "SSE.Views.AutoFilterDialog.txtEnds": "Loppuu...", "SSE.Views.AutoFilterDialog.txtEquals": "On yhtäkuin...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Suodata solujen värien mukaan", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Suodata fontin v<PERSON><PERSON> mukaan", "SSE.Views.AutoFilterDialog.txtGreater": "Suure<PERSON><PERSON> kuin...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Suurempi kuin tai yhtäsuuri...", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON><PERSON>m<PERSON><PERSON> kuin...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Vähemmän kuin tai yhtäsuuri kuin...", "SSE.Views.AutoFilterDialog.txtNotBegins": "Ei ala...", "SSE.Views.AutoFilterDialog.txtNotContains": "Ei sisäll<PERSON>...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Ei lopu...", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON>i ole <PERSON>in", "SSE.Views.AutoFilterDialog.txtNumFilter": "Numeron suodatin", "SSE.Views.AutoFilterDialog.txtReapply": "Käytä <PERSON>", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Lajittele solujen värin mukaan", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Lajittele fontin värin mukaan", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Lajittele k<PERSON>", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Lajittele matali<PERSON> k<PERSON>", "SSE.Views.AutoFilterDialog.txtTextFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.warnNoSelected": "<PERSON>un tulee valita vähintään yksi arvo", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellEditor.tipFormula": "Lisää funktio", "SSE.Views.CellRangeDialog.errorMaxRows": "VIRHE! Tietosarjojen maksimimäärä kaaviossa on 255", "SSE.Views.CellRangeDialog.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Views.CellRangeDialog.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.CellRangeDialog.txtInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.CellRangeDialog.txtTitle": "Valitse tietoalue", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON><PERSON> paksuus", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.ChartSettings.textBorderSizeErr": "Syötetty arvo ei ole o<PERSON>. Ole hyvä ja syötä arvo välillä 0 pt ja 1684 pt", "SSE.Views.ChartSettings.textChartType": "<PERSON><PERSON> kaavion t<PERSON>", "SSE.Views.ChartSettings.textEditData": "Muokkaa tietoja ja sijaintia", "SSE.Views.ChartSettings.textFirstPoint": "Ensimmmäinen piste", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "Ko<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Viimeinen piste", "SSE.Views.ChartSettings.textLowPoint": "Alapiste", "SSE.Views.ChartSettings.textMarkers": "Merkit", "SSE.Views.ChartSettings.textNegativePoint": "Negatiivinen piste", "SSE.Views.ChartSettings.textRanges": "Tietoalue", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ChartSettings.textShow": "Näytä", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textType": "Tyyppi", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxRows": "VIRHE! Tietosarjojen maksimimäärä kaaviossa on 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Views.ChartSettingsDlg.textAuto": "Automaattinen", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automaattisesti jokaiselle", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Aks<PERSON><PERSON> le<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Valintamerkkien välillä", "SSE.Views.ChartSettingsDlg.textBillions": "Miljardia", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "Keskellä", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "<PERSON><PERSON><PERSON> elementit & <br><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCross": "Ylittää", "SSE.Views.ChartSettingsDlg.textCustom": "Muka<PERSON>ttu", "SSE.Views.ChartSettingsDlg.textDataColumns": "Sarakkeissa", "SSE.Views.ChartSettingsDlg.textDataLabels": "Tieto-otsikot", "SSE.Views.ChartSettingsDlg.textDataRows": "riveissä", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Näytä kuvateksti", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Piilotetut ja Tyhjät solut ", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Yhdistä tietopisteet viivalla", "SSE.Views.ChartSettingsDlg.textFit": "<PERSON><PERSON><PERSON> le<PERSON> mukaan", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Ruudukon viivat", "SSE.Views.ChartSettingsDlg.textGroup": "Kipinäviivojen r<PERSON>", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHigh": "Korkea", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Sisällä", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Sisä<PERSON><PERSON> al<PERSON>a", "SSE.Views.ChartSettingsDlg.textInnerTop": "Sisäreuna ylhäällä", "SSE.Views.ChartSettingsDlg.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.ChartSettingsDlg.textLabelDist": "<PERSON><PERSON>elin otsikon etäisyys", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Otsikoiden väli", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Otsikon vaihtoehdot", "SSE.Views.ChartSettingsDlg.textLabelPos": "Ots<PERSON><PERSON> asema", "SSE.Views.ChartSettingsDlg.textLayout": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Vasen päällekkäin", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "Ku<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Yläosa", "SSE.Views.ChartSettingsDlg.textLines": "Viivat", "SSE.Views.ChartSettingsDlg.textLocationRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON> ja <PERSON>", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON> t<PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "Merkit", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Merkkien väli", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "Miljoonia", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "V<PERSON>häinen tyyppi", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimiarvo", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON> mit<PERSON>n", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Ei päällekkäisyyksiä", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Viivainmerkit", "SSE.Views.ChartSettingsDlg.textOut": "Ulkona", "SSE.Views.ChartSettingsDlg.textOuterTop": "Ulompi Ylhäällä", "SSE.Views.ChartSettingsDlg.textOverlay": "Päällekkäin", "SSE.Views.ChartSettingsDlg.textReverse": "Arvot käänteisessä järjetyksessä", "SSE.Views.ChartSettingsDlg.textReverseOrder": "<PERSON><PERSON> til<PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Päällekkäin o<PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON> ka<PERSON>", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ChartSettingsDlg.textSeparator": "Tieto-otsikoiden erotin", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShow": "Näytä", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON><PERSON><PERSON> kaavion reunukset", "SSE.Views.ChartSettingsDlg.textShowData": "Näytä tiedot piilotetuissa riveissä ja sarakkeissa", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Näytä tyhjät solut kuten", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Näytä akseli", "SSE.Views.ChartSettingsDlg.textShowValues": "Näytä kaavion arvot", "SSE.Views.ChartSettingsDlg.textSingle": "Yksi kipinäviiva", "SSE.Views.ChartSettingsDlg.textSmooth": "Pehmeä", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Kipinäviivan tietoalue", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Rastivaihtoehdot", "SSE.Views.ChartSettingsDlg.textTitle": "Kaavio - Laaj<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Kipinäviiva - Laajennetut asetukset", "SSE.Views.ChartSettingsDlg.textTop": "Yläosa", "SSE.Views.ChartSettingsDlg.textTrillions": "Triljoonia", "SSE.Views.ChartSettingsDlg.textType": "Tyyppi", "SSE.Views.ChartSettingsDlg.textTypeData": "Tyyppi & Tiedot", "SSE.Views.ChartSettingsDlg.textUnits": "Näyttöyksiköt", "SSE.Views.ChartSettingsDlg.textValue": "Arvo", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "A Akselin o<PERSON>ikko", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y Akseli otsikko", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.DigitalFilterDialog.capAnd": "<PERSON>a", "SSE.Views.DigitalFilterDialog.capCondition1": "on yhtäkuin", "SSE.Views.DigitalFilterDialog.capCondition10": "ei lopu", "SSE.Views.DigitalFilterDialog.capCondition11": "sisältää", "SSE.Views.DigitalFilterDialog.capCondition12": "ei sis<PERSON>llä", "SSE.Views.DigitalFilterDialog.capCondition2": "ei ole yhtäkuin", "SSE.Views.DigitalFilterDialog.capCondition3": "on suurempi kuin", "SSE.Views.DigitalFilterDialog.capCondition4": "on suurempi kuin tai yhtä<PERSON>uri kuin", "SSE.Views.DigitalFilterDialog.capCondition5": "on vähemmän kuin", "SSE.Views.DigitalFilterDialog.capCondition6": "on vä<PERSON>män tai yhtä<PERSON>uri kuin", "SSE.Views.DigitalFilterDialog.capCondition7": "alkaa", "SSE.Views.DigitalFilterDialog.capCondition8": "ei ala ", "SSE.Views.DigitalFilterDialog.capCondition9": "loppuu", "SSE.Views.DigitalFilterDialog.capOr": "tai", "SSE.Views.DigitalFilterDialog.textNoFilter": "ei suodatinta", "SSE.Views.DigitalFilterDialog.textShowRows": "Näytä rivit missä", "SSE.Views.DigitalFilterDialog.textUse1": "Käytä ? merkkiä esittämään mitä tahansa yksittäistä kirjainta", "SSE.Views.DigitalFilterDialog.textUse2": "Käytä * merkkiä esittämään mitä tahansa kirjainsar<PERSON>a", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> suodatin", "SSE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON> la<PERSON> as<PERSON>", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON><PERSON> alas", "SSE.Views.DocumentHolder.centerCellText": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.DocumentHolder.chartText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON>ista sarake", "SSE.Views.DocumentHolder.deleteRowText": "Poista rivi", "SSE.Views.DocumentHolder.deleteTableText": "Poista taulukko", "SSE.Views.DocumentHolder.direct270Text": "Käännä 270°", "SSE.Views.DocumentHolder.direct90Text": "Käännä 90°", "SSE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.editChartText": "Muokkaa tie<PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "Muokkaa link<PERSON>ä", "SSE.Views.DocumentHolder.insertColumnLeftText": "Lisää sarake vasemmalle", "SSE.Views.DocumentHolder.insertColumnRightText": "Lisää sarake o<PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "Lisää rivi ylös", "SSE.Views.DocumentHolder.insertRowBelowText": "Lisää rivi alas", "SSE.Views.DocumentHolder.removeHyperlinkText": "Poista linkki", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> koko sarake", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.DocumentHolder.selectRowText": "Valitse rivi", "SSE.Views.DocumentHolder.selectTableText": "Valitse taulukko", "SSE.Views.DocumentHolder.textArrangeBack": "Lähetä taustalle", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textEntriesList": "Valitse tiputusvalikosta", "SSE.Views.DocumentHolder.textFreezePanes": "Jäädytä ruudut", "SSE.Views.DocumentHolder.textUnFreezePanes": "Vapauta ruudut", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "Lisää kommentti", "SSE.Views.DocumentHolder.txtAddNamedRange": "Määrittele nimi", "SSE.Views.DocumentHolder.txtArrange": "Järjestä", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Sovita automaattisesti sa<PERSON>en leveys", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Sovita automaattisesti rivin korkeus", "SSE.Views.DocumentHolder.txtClear": "Tyhjennä", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Ko<PERSON>ntit", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearHyper": "Linkit", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Tyhjennä valitut kipinäviiva ryhmät", "SSE.Views.DocumentHolder.txtClearSparklines": "Tyhjennä valitut kipinäviivat", "SSE.Views.DocumentHolder.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON> sarake", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON> sa<PERSON>en leveys", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Mukautettu sarakkeen leveys", "SSE.Views.DocumentHolder.txtCustomRowHeight": "<PERSON><PERSON><PERSON><PERSON> rivin korkeus", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelete": "Poista", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtEditComment": "Muokkaa kommenttia", "SSE.Views.DocumentHolder.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilterCellColor": "Suodata solun värin mukaan", "SSE.Views.DocumentHolder.txtFilterFontColor": "Suodata fontin v<PERSON><PERSON> mukaan", "SSE.Views.DocumentHolder.txtFilterValue": "<PERSON><PERSON><PERSON> valitun solun arvon mukaan", "SSE.Views.DocumentHolder.txtFormula": "Lisää funktio", "SSE.Views.DocumentHolder.txtGroup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsert": "Lisää", "SSE.Views.DocumentHolder.txtInsHyperlink": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPaste": "Liit<PERSON>", "SSE.Views.DocumentHolder.txtReapply": "Käytä <PERSON>", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON> rivi", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON> rivin korkeus", "SSE.Views.DocumentHolder.txtSelect": "Valitse", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON>da solut alas", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON>da solut vasemmalle", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON>da solut o<PERSON>alle", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON>da solut yl<PERSON>s", "SSE.Views.DocumentHolder.txtShow": "Näytä", "SSE.Views.DocumentHolder.txtSort": "Lajittele", "SSE.Views.DocumentHolder.txtSortCellColor": "Valittu solun värin y<PERSON>häällä", "SSE.Views.DocumentHolder.txtSortFontColor": "Valittu fontin väri y<PERSON>", "SSE.Views.DocumentHolder.txtSparklines": "Kipinäviivat", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON><PERSON><PERSON> as<PERSON>", "SSE.Views.DocumentHolder.txtUngroup": "<PERSON><PERSON> ryhm<PERSON>s", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON> vali<PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON>i", "SSE.Views.FileMenu.btnDownloadCaption": "La<PERSON>a kuten", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnInfoCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "SSE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "Avaa viimeaikainen", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.FileMenu.btnRightsCaption": "Käyttöoikeudet", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSettingsCaption": "Laajennetut <PERSON>", "SSE.Views.FileMenu.btnToEditCaption": "Muokkaa työkirjaa", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON> k<PERSON>öoikeuk<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> ovat o<PERSON>t", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Laskutaulukon otsikko", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON> k<PERSON>öoikeuk<PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> ovat o<PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Käytä", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tila", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Pika", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "<PERSON><PERSON> k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Esimerkki: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Esimerkki:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Mittausyksikkö", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "<PERSON><PERSON> arvo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Joka 10 minuutti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Joka 30 minuutti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Joka 5 minuutti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON> tunti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Automaattinen palautus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Automaattinen talletus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "<PERSON><PERSON> k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Senttimetriä", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "kuten OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON>yn<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Venäjän kieli", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "kuten Windows", "SSE.Views.FormatRulesEditDlg.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Valitse funktioryhmä", "SSE.Views.FormulaDialog.textListDescription": "Valitse funktio", "SSE.Views.FormulaDialog.txtTitle": "Lisää funktio", "SSE.Views.HeaderFooterDialog.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Näyttö", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Linkitä:", "SSE.Views.HyperlinkSettingsDialog.strRange": "Tietoalue", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Syötä kuvateksti tähän", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Syötä linkki tässä", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Syötä työkaluvinkki tähän", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON><PERSON><PERSON> tie<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Näyttövinkin teksti", "SSE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON> tied<PERSON>on tulisi olla verkko-osoite \"http://www.esimerkki.com\" muodossa", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Muokkaa objektia", "SSE.Views.ImageSettings.textFromFile": "Tiedostosta", "SSE.Views.ImageSettings.textFromUrl": "URL-osoitteesta", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON> k<PERSON>va", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipComments": "Ko<PERSON>ntit", "SSE.Views.LeftMenu.tipFile": "Tiedosto", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "Etsi", "SSE.Views.LeftMenu.tipSupport": "Palaute & Tuki", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "Marginaalit", "SSE.Views.MainSettingsPrint.strPortrait": "Pystysuunta", "SSE.Views.MainSettingsPrint.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Yläosa", "SSE.Views.MainSettingsPrint.textActualSize": "Todellinen koko", "SSE.Views.MainSettingsPrint.textFitCols": "So<PERSON>ta kaikki sa<PERSON> yh<PERSON>e sivulle", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON>ta ta<PERSON> sivulle", "SSE.Views.MainSettingsPrint.textFitRows": "Sovita kaikki rivit yh<PERSON>e sivulle", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON><PERSON> su<PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON>n koko", "SSE.Views.MainSettingsPrint.textPrintGrid": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "<PERSON><PERSON><PERSON> ja <PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textSettings": "Asetukset:", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Olemassaolevia nimettyjä tietoalueita ei voida muokata ja uusia ei voida luoda<br>tällä hetkellä koska jotain niistä muokataan tällä hetkellä.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Määritelty nimi", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Työkirja", "SSE.Views.NamedRangeEditDlg.textDataRange": "Tietoalue", "SSE.Views.NamedRangeEditDlg.textExistName": "VIRHE! Samanniminen tietoalue on jo olemassa", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Nimen tulee alkaa kirja<PERSON>lla tai alaviivalla ja se ei saa sisältää virheellisiä merkkejä.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "VIRHE! Virheellinen solun tietoalue", "SSE.Views.NamedRangeEditDlg.textIsLocked": "VIRHE! Toinen käyttäjä muokkaa tätä elementtiä.", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON>, jota yrität kä<PERSON>tää, on jo viitattu solujen kaav<PERSON>sa. Ole hyvä ja käytä muuta nimeä.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "<PERSON><PERSON><PERSON><PERSON> nime<PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.closeButtonText": "Sulje", "SSE.Views.NameManagerDlg.guestText": "Vierailija", "SSE.Views.NameManagerDlg.textDataRange": "Tietoalue", "SSE.Views.NameManagerDlg.textDelete": "Poista", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Ei ole vielä luotu nimettyjä tietoalueita.<br><PERSON><PERSON> v<PERSON>hintään yksi nimetty tietoalue ja se ilmaantuu tähän kenttään.", "SSE.Views.NameManagerDlg.textFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "Määritellyt nimet", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON>lukon nimet", "SSE.Views.NameManagerDlg.textFilterWorkbook": "<PERSON><PERSON> k<PERSON>ttävissä työkirjaan", "SSE.Views.NameManagerDlg.textNew": "<PERSON>us<PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Suodatintasi vastaa<PERSON>a nimett<PERSON>ä tietoal<PERSON>tta ei löyt<PERSON>.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Työkirja", "SSE.Views.NameManagerDlg.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strLineHeight": "Viivan väli", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.ParagraphSettings.textAt": "/", "SSE.Views.ParagraphSettings.textAtLeast": "vähintään", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Täsmälleen", "SSE.Views.ParagraphSettings.txtAutoText": "Automaattinen", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Määritellyt välilehdet ilmaantuvat tässä kentässä", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON> is<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Sisennykset & Asettelut", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Ka<PERSON>eel<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Yliviivaus", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Yläindeksi", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Välilehti", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efektit", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Poista", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Poista kaikki", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Määrittele", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Keskellä", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> asema", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Kappale - Laajenne<PERSON><PERSON>", "SSE.Views.PrintSettings.btnPrint": "Tallenna & Tulosta", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "Marginaalit", "SSE.Views.PrintSettings.strPortrait": "Pystysuunta", "SSE.Views.PrintSettings.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "Yläosa", "SSE.Views.PrintSettings.textActualSize": "Todellinen koko", "SSE.Views.PrintSettings.textAllSheets": "Kaikki taulukot", "SSE.Views.PrintSettings.textCurrentSheet": "Nykyinen taulukko", "SSE.Views.PrintSettings.textFitCols": "So<PERSON>ta kaikki sa<PERSON> yh<PERSON>e sivulle", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON>ta ta<PERSON> sivulle", "SSE.Views.PrintSettings.textFitRows": "Sovita kaikki rivit yh<PERSON>e sivulle", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textLayout": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON><PERSON> su<PERSON>", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON>n koko", "SSE.Views.PrintSettings.textPrintGrid": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.PrintSettings.textPrintHeadings": "<PERSON><PERSON><PERSON> ja <PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Taulukon asetukset", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSparklineSettings": "Kipinäviivan asetukset", "SSE.Views.RightMenu.txtTableSettings": "Taulukon asetukset", "SSE.Views.RightMenu.txtTextArtSettings": "Tai<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON> kentän maksim<PERSON> on {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON><PERSON><PERSON><PERSON> kentän minimiarvo on {0}", "SSE.Views.ShapeSettings.strBackground": "Taustan väri", "SSE.Views.ShapeSettings.strChange": "Muuta automaattista muotoa", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Täytä", "SSE.Views.ShapeSettings.strForeground": "Etualan väri", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "Tikkuviiva", "SSE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strType": "Tyyppi", "SSE.Views.ShapeSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.ShapeSettings.textBorderSizeErr": "Syötetty arvo ei ole o<PERSON>. Ole hyvä ja syötä arvo välillä 0 pt ja 1684 pt", "SSE.Views.ShapeSettings.textColor": "Väritäyttö", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON> ku<PERSON>ta", "SSE.Views.ShapeSettings.textFromFile": "Tiedostosta", "SSE.Views.ShapeSettings.textFromUrl": "URL-osoitteesta", "SSE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>eva täyttö", "SSE.Views.ShapeSettings.textImageTexture": "Kuva tai pintarakenne", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoFill": "Ei tä<PERSON>", "SSE.Views.ShapeSettings.textOriginalSize": "Alkuperäinen koko", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textRadial": "S<PERSON><PERSON><PERSON>äinen", "SSE.Views.ShapeSettings.textSelectTexture": "Valitse", "SSE.Views.ShapeSettings.textStretch": "Venytä", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Pintarakenteesta", "SSE.Views.ShapeSettings.textTile": "Laatta", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>i", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Tumma kangas", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtKnit": "Sid<PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Nahka", "SSE.Views.ShapeSettings.txtNoBorders": "Ei viivaa", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Aloituskoko", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Al<PERSON>ust<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Viiste", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Ison kirjaimen tyyppi", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON><PERSON> koko", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Lo<PERSON><PERSON><PERSON> tyyli", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Liitoksen t<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Viivan tyyli", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRound": "Pyöristä", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Muoto - Laaj<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "Yläosa", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Vahvuudet & Nuolet", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(<PERSON><PERSON><PERSON> lo<PERSON>)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON><PERSON> lo<PERSON>)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Ko<PERSON>i ennen taulu<PERSON>a", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON><PERSON> ennen ta<PERSON>a", "SSE.Views.Statusbar.itemCopy": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemDelete": "Poista", "SSE.Views.Statusbar.itemHidden": "Piilotettu", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Lisää", "SSE.Views.Statusbar.itemMove": "Siirrä", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "Välilehden väri", "SSE.Views.Statusbar.RenameDialog.errNameExists": "<PERSON><PERSON><PERSON> sa<PERSON> on jo luotu.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Taulukon nimessä ei voi olla seuraavia merkkejä: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON><PERSON> nimi", "SSE.Views.Statusbar.textAverage": "KESKIMÄÄRÄINEN", "SSE.Views.Statusbar.textCount": "LASKE", "SSE.Views.Statusbar.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON> väriä", "SSE.Views.Statusbar.textSum": "Summa", "SSE.Views.Statusbar.tipAddTab": "Lisää taulukko", "SSE.Views.Statusbar.tipFirst": "Rullaa ensimmäiseen taulukkoon", "SSE.Views.Statusbar.tipLast": "Rullaa viimeiseen taulukkoon", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON>loa o<PERSON>a", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON> ta<PERSON>luetteloa vasemmalla", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "Lähennä", "SSE.Views.Statusbar.tipZoomOut": "Loitonna", "SSE.Views.Statusbar.zoomText": "<PERSON><PERSON><PERSON> {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Toimintoa ei voida suorittaa valitulle solujen tietoalueelle.<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tietoalue, joka eroa<PERSON>, ja yrit<PERSON> uude<PERSON>.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Toimintoa ei voitu suorittaa valitulle solun tietoalueelle.<br>Valitse tietoalue niin että ensimmäinen taulukon rivi on samalla rivillä<br>ja t<PERSON><PERSON><PERSON><PERSON><PERSON> on limitt<PERSON>in nykyisen kanssa.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Toimintoa ei voitu suorittaa valitulle solun tietoalueelle.<br>Valitse tietoalue mikä ei sisällä muita taulukkoja.", "SSE.Views.TableOptionsDialog.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON>ista sarake", "SSE.Views.TableSettings.deleteRowText": "Poista rivi", "SSE.Views.TableSettings.deleteTableText": "Poista taulukko", "SSE.Views.TableSettings.insertColumnLeftText": "Lisää sarake vasemmalle", "SSE.Views.TableSettings.insertColumnRightText": "Lisää sarake o<PERSON>", "SSE.Views.TableSettings.insertRowAboveText": "Lisää rivi ylös", "SSE.Views.TableSettings.insertRowBelowText": "Lisää rivi alas", "SSE.Views.TableSettings.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON> koko sarake", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.TableSettings.selectRowText": "Valitse rivi", "SSE.Views.TableSettings.selectTableText": "Valitse taulukko", "SSE.Views.TableSettings.textBanded": "Niputettu", "SSE.Views.TableSettings.textColumns": "Sarakkeet", "SSE.Views.TableSettings.textEdit": "Rivit & Sarakkeet", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON>", "SSE.Views.TableSettings.textExistName": "VIRHE: <PERSON><PERSON><PERSON><PERSON> on jo o<PERSON><PERSON>a", "SSE.Views.TableSettings.textFilter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON>mmä<PERSON>", "SSE.Views.TableSettings.textHeader": "Ylävyöhyke", "SSE.Views.TableSettings.textInvalidName": "VRIHE!Virheellinen taulukon nimi", "SSE.Views.TableSettings.textIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.TableSettings.textLast": "Viimeinen", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON>, jota yrität kä<PERSON>tää, on jo viitattu solujen kaav<PERSON>sa. Ole hyvä ja käytä muuta nimeä.", "SSE.Views.TableSettings.textResize": "Taulukon koko", "SSE.Views.TableSettings.textRows": "Rivit", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON><PERSON> nimi", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textTotal": "Yhteensä", "SSE.Views.TextArtSettings.strBackground": "Taustan väri", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Täytä", "SSE.Views.TextArtSettings.strForeground": "Etualan väri", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "Tikkuviiva", "SSE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strType": "Tyyppi", "SSE.Views.TextArtSettings.textBorderSizeErr": "Syötetty arvo ei ole o<PERSON>. Ole hyvä ja syötä arvo välillä 0 pt ja 1684 pt", "SSE.Views.TextArtSettings.textColor": "Väritäyttö", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON> ku<PERSON>ta", "SSE.Views.TextArtSettings.textFromFile": "Tiedostosta", "SSE.Views.TextArtSettings.textFromUrl": "URL-osoitteesta", "SSE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON>eva täyttö", "SSE.Views.TextArtSettings.textImageTexture": "Kuva tai pintarakenne", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Ei tä<PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textRadial": "S<PERSON><PERSON><PERSON>äinen", "SSE.Views.TextArtSettings.textSelectTexture": "Valitse", "SSE.Views.TextArtSettings.textStretch": "Venytä", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTexture": "Pintarakenteesta", "SSE.Views.TextArtSettings.textTile": "Laatta", "SSE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>i", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Tumma kangas", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtKnit": "Sid<PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Nahka", "SSE.Views.TextArtSettings.txtNoBorders": "Ei viivaa", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON> ve<PERSON>-o<PERSON><PERSON>", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON><PERSON> alas", "SSE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> vasen", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON> o<PERSON>a", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBold": "Liha<PERSON>int<PERSON>", "SSE.Views.Toolbar.textBordersColor": "Reunuksen väri", "SSE.Views.Toolbar.textBottomBorders": "Alareunukset", "SSE.Views.Toolbar.textCenterBorders": "Pys<PERSON>reunust<PERSON> si<PERSON>", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON>", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON>", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON><PERSON>da solut vasemmalle", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON>da solut yl<PERSON>s", "SSE.Views.Toolbar.textDiagDownBorder": "Diagonaalinen alareunus", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonaalinen yläreunus", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON> sarake", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON> rivi", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON>da solut alas", "SSE.Views.Toolbar.textInsideBorders": "Sisäreunukset", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON>da solut o<PERSON>alle", "SSE.Views.Toolbar.textItalic": "Kursivoit<PERSON>", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Vaakareunusten sisällä", "SSE.Views.Toolbar.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON> reun<PERSON>", "SSE.Views.Toolbar.textRotateDown": "Käännä teksti alas", "SSE.Views.Toolbar.textRotateUp": "Käännä teksti ylös", "SSE.Views.Toolbar.textTopBorders": "Yläreunukset", "SSE.Views.Toolbar.textUnderline": "Alleviivaus", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON><PERSON> alas", "SSE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON> vasen", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON> o<PERSON>a", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAutofilter": "Lajittele ja Suodata", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON> ty<PERSON>i", "SSE.Views.Toolbar.tipClearStyle": "Tyhjennä", "SSE.Views.Toolbar.tipColorSchemas": "Muuta väriluonnosta", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON> t<PERSON>i", "SSE.Views.Toolbar.tipDecDecimal": "Vähennä desimaalia", "SSE.Views.Toolbar.tipDecFont": "Vähennä fontin kokoa", "SSE.Views.Toolbar.tipDeleteOpt": "Poista solut", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "SSE.Views.Toolbar.tipDigStyleCurrency": "Valuutan tyyli", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON><PERSON> t<PERSON>i", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON> väri", "SSE.Views.Toolbar.tipFontName": "Fontin nimi", "SSE.Views.Toolbar.tipFontSize": "Fonttikoko", "SSE.Views.Toolbar.tipIncDecimal": "Lisää desimaalia", "SSE.Views.Toolbar.tipIncFont": "Lisää fontin kokoa", "SSE.Views.Toolbar.tipInsertChart": "Lisää <PERSON>", "SSE.Views.Toolbar.tipInsertChartSpark": "Lisää kaavio tai kipinäviiva", "SSE.Views.Toolbar.tipInsertEquation": "Lisää yhtälö", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "Lisää solut", "SSE.Views.Toolbar.tipInsertShape": "Lisää automaattinen muoto", "SSE.Views.Toolbar.tipInsertText": "Lisää teksti", "SSE.Views.Toolbar.tipMerge": "Yhdistä", "SSE.Views.Toolbar.tipNumFormat": "Numeron muoto", "SSE.Views.Toolbar.tipPaste": "Liit<PERSON>", "SSE.Views.Toolbar.tipPrColor": "Taustan väri", "SSE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON>, jotta muut käyttäjät näkevät ne.", "SSE.Views.Toolbar.tipSynchronize": "Asiakirja on toisen käyttäjän muuttama. Ole hyvä ja klikkaa tallentaaksesi muutoksesi ja lataa uudelleen muutokset.", "SSE.Views.Toolbar.tipTextOrientation": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "Lisä", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Ko<PERSON>ntit", "SSE.Views.Toolbar.txtClearFilter": "Poista suoda<PERSON>", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "Linkit", "SSE.Views.Toolbar.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Muka<PERSON>ttu", "SSE.Views.Toolbar.txtDate": "Päivämäärä", "SSE.Views.Toolbar.txtDateTime": "Pvm & Kellonaika", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Dollari", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Eksponentiaalinen", "SSE.Views.Toolbar.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFormula": "Lisää funktio", "SSE.Views.Toolbar.txtFraction": "Murtoluku", "SSE.Views.Toolbar.txtFranc": "CHF Sveitsin frangi", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON>ist<PERSON>", "SSE.Views.Toolbar.txtInteger": "Kokonaisluku", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON><PERSON> yli", "SSE.Views.Toolbar.txtMergeCells": "Yhdistä solut", "SSE.Views.Toolbar.txtMergeCenter": "Yhdistä & Keskitä", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "Määrittele nimi", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "SSE.Views.Toolbar.txtNumber": "Numero", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPercentage": "Prosenttia", "SSE.Views.Toolbar.txtPound": "£ Punta", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme10": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Runsas", "SSE.Views.Toolbar.txtScheme15": "Alkuperä", "SSE.Views.Toolbar.txtScheme16": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme17": "Päivänseisaus", "SSE.Views.Toolbar.txtScheme18": "Tekninen", "SSE.Views.Toolbar.txtScheme19": "V<PERSON>la", "SSE.Views.Toolbar.txtScheme2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme20": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme21": "Energia", "SSE.Views.Toolbar.txtScheme3": "Huippu", "SSE.Views.Toolbar.txtScheme4": "Näkö<PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme5": "Civic", "SSE.Views.Toolbar.txtScheme6": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme7": "Pääoma", "SSE.Views.Toolbar.txtScheme8": "<PERSON>irt<PERSON>", "SSE.Views.Toolbar.txtScheme9": "Foundry", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "Etsi", "SSE.Views.Toolbar.txtSort": "Lajittele", "SSE.Views.Toolbar.txtSortAZ": "Lajittele nousevaan järjestykseen", "SSE.Views.Toolbar.txtSortZA": "Lajittele laskevaan järjestykseen", "SSE.Views.Toolbar.txtSpecial": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTableTemplate": "<PERSON><PERSON><PERSON> ta<PERSON> mallip<PERSON>", "SSE.Views.Toolbar.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON>a solut", "SSE.Views.Toolbar.txtYen": "¥ Jeni", "SSE.Views.Top10FilterDialog.textType": "Näytä", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "Prosenttia", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automaattinen suodatin", "SSE.Views.Top10FilterDialog.txtTop": "Yläosa"}