﻿<!DOCTYPE html>
<html>
	<head>
		<title>QUANTILSRANG.EXKL-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>QUANTILSRANG.EXKL-Funktion</h1>
			<p>Die Funktion <b>QUANTILSRANG.EXKL</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt den Rang eines Werts in einem Datensatz als Prozentsatz (0..1, exklusiv) des Datensatzes zurück.</p>
			<p>Die Formelsyntax der Funktion <b>QUANTILSRANG.EXKL</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>QUANTILSRANG.EXKL(Matrix;x;[Genauigkeit])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Matrix</em></b> ist der Zellbereich numerischer Daten, der die relative Lage der Daten beschreibt.</p>
				<p style="text-indent: 50px;"><b><em>x</em></b> ist der Wert, dessen Rang Sie bestimmen möchten. Ein nummerischer wert, der manuell eingegeben wird oder in die Zelle eingeschlossen ist, auf die Sie Bezug nehmen.</p>
				<p style="text-indent: 50px;"><b><em>Genauigkeit</em></b> legt die Anzahl der Nachkommastellen des zurückgegebenen Quantilsrangs fest. Dieses Argument ist optional. Fehlt dieses Argument geht die Funktion von der <b><em>Genauigkeit </em></b> 3 aus (drei Dezimalstellen).</p>
			<p>Anwendung der Funktion <b>QUANTILSRANG.EXKL</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>QUANTILSRANG.EXKL</b>:</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="QUANTILSRANG.EXKL-Funktion" src="../images/percentrank-exc.png" /></p>
		</div>
	</body>
</html>