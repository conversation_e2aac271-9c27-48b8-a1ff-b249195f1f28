{"Common.Controllers.Chat.notcriticalErrorTitle": "Uyarı", "Common.Controllers.Chat.textEnterMessage": "Mesajınızı buraya giriniz", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "Ka<PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "<PERSON><PERSON><PERSON> de<PERSON> dışı bırakıldı, <PERSON><PERSON><PERSON><PERSON> başka kullanıcı tarafından düzenleniyor.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Uyarı", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alan", "Common.define.chartData.textAreaStackedPer": "%100 Yığılmış alan", "Common.define.chartData.textBar": "Çubuk grafik", "Common.define.chartData.textBarNormal": "Kümelenmiş sütun", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Kümelenmiş sütun", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON>", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sütun", "Common.define.chartData.textBarStacked3d": "3-D Yığılmış sütun ", "Common.define.chartData.textBarStackedPer": "%100 Yığılmış sütun", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Yığılmış sütun", "Common.define.chartData.textCharts": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Açılan kutu", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alan - kümelenmiş sütun", "Common.define.chartData.textComboBarLine": "Kümelenmiş sütun - satır", "Common.define.chartData.textComboBarLineSecondary": "Kümelenmiş sütun - ikincil eksendeki çizgi", "Common.define.chartData.textComboCustom": "<PERSON><PERSON> k<PERSON>", "Common.define.chartData.textDoughnut": "Hal<PERSON>", "Common.define.chartData.textHBarNormal": "Kümelenmiş <PERSON>", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> Kümelenmiş çubuk", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Yığılmış çubuk ", "Common.define.chartData.textHBarStackedPer": "%100 Yığılmış çubuk", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Yığılmış çubuk", "Common.define.chartData.textLine": "Çizgi grafiği", "Common.define.chartData.textLine3d": "3-<PERSON>", "Common.define.chartData.textLineMarker": "İşaretli çizgi ", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş çizgi", "Common.define.chartData.textLineStackedMarker": "İşaretleyicilerle yığılmış çizgi", "Common.define.chartData.textLineStackedPer": "%100 Yığılmış çizgi", "Common.define.chartData.textLineStackedPerMarker": "İşaretli %100 Yığılmış çizgi ", "Common.define.chartData.textPie": "<PERSON><PERSON>", "Common.define.chartData.textPie3d": "3-D Dilim grafik", "Common.define.chartData.textPoint": "Nokta grafiği", "Common.define.chartData.textScatter": "Dağılım", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON>le dağılım", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON>z çizgiler ve işaretleyicilerle dağılım", "Common.define.chartData.textScatterSmooth": "Yumuşak çizgilerle dağılım", "Common.define.chartData.textScatterSmoothMarker": "Yumuşak çizgiler ve işaretleyicilerle dağılım", "Common.define.chartData.textStock": "Stok Grafiği", "Common.define.chartData.textSurface": "Yüzey", "Common.define.effectData.textAcross": "Karşısında", "Common.define.effectData.textAppear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textArcDown": "Aşağı Yay", "Common.define.effectData.textArcLeft": "Sol <PERSON>", "Common.define.effectData.textArcRight": "<PERSON><PERSON>", "Common.define.effectData.textArcs": "yaylar", "Common.define.effectData.textArcUp": "Yukarı Yay", "Common.define.effectData.textBasic": "Temel", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON>", "Common.define.effectData.textBasicZoom": "Temel Yakınlaştırma", "Common.define.effectData.textBean": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBlinds": "Güneşlik", "Common.define.effectData.textBlink": "Parlama", "Common.define.effectData.textBoldFlash": "Ka<PERSON>ın <PERSON>", "Common.define.effectData.textBoldReveal": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBoomerang": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>lama", "Common.define.effectData.textBounceLeft": "Sola Zıplama", "Common.define.effectData.textBounceRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBox": "Kutucuk", "Common.define.effectData.textBrushColor": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCenterRevolve": "Merkez<PERSON>", "Common.define.effectData.textCheckerboard": "<PERSON><PERSON>ı", "Common.define.effectData.textCircle": "Daire", "Common.define.effectData.textCollapse": "Dar<PERSON><PERSON>", "Common.define.effectData.textColorPulse": "Renk <PERSON>i", "Common.define.effectData.textComplementaryColor": "Tamamlayıcı Renk", "Common.define.effectData.textComplementaryColor2": "Tamamlayıcı Renk 2", "Common.define.effectData.textCompress": "Sıkıştırılmış", "Common.define.effectData.textContrast": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textContrastingColor": "Kontrast Renk", "Common.define.effectData.textCredits": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCrescentMoon": "<PERSON><PERSON>", "Common.define.effectData.textCurveDown": "Aşağı Eğri", "Common.define.effectData.textCurvedSquare": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurvedX": "Kavisli X", "Common.define.effectData.textCurvyLeft": "Kıvrımlı Sol", "Common.define.effectData.textCurvyRight": "Kıvrımlı Sağ", "Common.define.effectData.textCurvyStar": "Kıvrımlı Yıldız", "Common.define.effectData.textCustomPath": "<PERSON><PERSON>", "Common.define.effectData.textCuverUp": "Yukarı Eğri", "Common.define.effectData.textDarken": "<PERSON><PERSON>", "Common.define.effectData.textDecayingWave": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDesaturate": "Doymamışlık", "Common.define.effectData.textDiagonalDownRight": "Çapraz Aşağı Sağ", "Common.define.effectData.textDiagonalUpRight": "Çapraz Yukarı Sağ", "Common.define.effectData.textDiamond": "<PERSON><PERSON>", "Common.define.effectData.textDisappear": "Kaybolarak", "Common.define.effectData.textDissolveIn": "Çözülerek", "Common.define.effectData.textDissolveOut": "Dışarı Çözülerek", "Common.define.effectData.textDown": "Aşağı", "Common.define.effectData.textDrop": "Düşerek", "Common.define.effectData.textEmphasis": "Vurg<PERSON>kis<PERSON>", "Common.define.effectData.textEntrance": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textEqualTriangle": "Eşkenar Üçgen", "Common.define.effectData.textExciting": "Heyecanlandırıcı", "Common.define.effectData.textExit": "Çıkış Etkisi", "Common.define.effectData.textExpand": "Genişlet", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFigureFour": "Şekil 8 Dört", "Common.define.effectData.textFillColor": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFlip": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFloat": "Yüzdürmek", "Common.define.effectData.textFloatDown": "Aşağı Yüzer", "Common.define.effectData.textFloatIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFloatOut": "Yüzmek", "Common.define.effectData.textFloatUp": "Yüzdür", "Common.define.effectData.textFlyIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFlyOut": "dışarı uç", "Common.define.effectData.textFontColor": "<PERSON><PERSON><PERSON>i", "Common.define.effectData.textFootball": "Futbol", "Common.define.effectData.textFromBottom": "Alttan", "Common.define.effectData.textFromBottomLeft": "Sol Alttan", "Common.define.effectData.textFromBottomRight": "Sağ al<PERSON>n", "Common.define.effectData.textFromLeft": "Soldan", "Common.define.effectData.textFromRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFromTop": "Üstten", "Common.define.effectData.textFromTopLeft": "Sol <PERSON>", "Common.define.effectData.textFromTopRight": "Sağ ü<PERSON>ten", "Common.define.effectData.textFunnel": "<PERSON><PERSON>", "Common.define.effectData.textGrowShrink": "Büyüt/Küçült", "Common.define.effectData.textGrowTurn": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve <PERSON>ö<PERSON>", "Common.define.effectData.textGrowWithColor": "<PERSON><PERSON>", "Common.define.effectData.textHeart": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHeartbeat": "kalp atışı ", "Common.define.effectData.textHexagon": "Altıgen ", "Common.define.effectData.textHorizontal": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHorizontalFigure": "Yatay Şekil 8 ", "Common.define.effectData.textHorizontalIn": "<PERSON><PERSON><PERSON> ", "Common.define.effectData.textHorizontalOut": "<PERSON><PERSON><PERSON> ", "Common.define.effectData.textIn": "içinde ", "Common.define.effectData.textInFromScreenCenter": "Ekran Merkezinden ", "Common.define.effectData.textInSlightly": "<PERSON><PERSON><PERSON>çe", "Common.define.effectData.textInToScreenBottom": "Ekranın <PERSON> ", "Common.define.effectData.textInvertedSquare": "<PERSON><PERSON> ", "Common.define.effectData.textInvertedTriangle": "Ters üçgen ", "Common.define.effectData.textLeft": "Sol", "Common.define.effectData.textLeftDown": "Sol Alt", "Common.define.effectData.textLeftUp": "<PERSON>", "Common.define.effectData.textLighten": "hafifletmek ", "Common.define.effectData.textLineColor": "<PERSON><PERSON><PERSON> ", "Common.define.effectData.textLines": "<PERSON><PERSON><PERSON><PERSON> ", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON><PERSON><PERSON> ", "Common.define.effectData.textLoopDeLoop": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.define.effectData.textLoops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textModerate": "Orta ", "Common.define.effectData.textPath": "Hareket yolları ", "Common.define.effectData.textPointStar4": "4 Köşeli Yıldız", "Common.define.effectData.textPointStar5": "5 Köşeli Yıldız", "Common.define.effectData.textPointStar6": "6 Köşeli Yıldız", "Common.define.effectData.textPointStar8": "8 Köşeli Yıldız", "Common.define.effectData.textRight": "Sağ", "Common.define.effectData.textRightDown": "Sağ Alt", "Common.define.effectData.textRightTriangle": "Sağ Üçgen", "Common.define.effectData.textRightUp": "<PERSON>ğ Ü<PERSON>", "Common.define.effectData.textSpoke1": "1 Konuştu", "Common.define.effectData.textSpoke2": "2 Konuştu", "Common.define.effectData.textSpoke3": "3 Konuştu", "Common.define.effectData.textSpoke4": "4 Konuştu", "Common.define.effectData.textSpoke8": "8 Konuştu", "Common.define.effectData.textUnderline": "Altı çizili", "Common.define.effectData.textUp": "Yukarı", "Common.define.effectData.textVertical": "<PERSON><PERSON>", "Common.define.effectData.textZigzag": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textZoom": "Yakınlaştırma", "Common.Translation.textMoreButton": "<PERSON><PERSON> fazla ", "Common.Translation.warnFileLocked": "Do<PERSON>a başka bir uygulamada düzenleniyor. Düzenlemeye devam edebilir ve kopya olarak kaydedebilirsiniz.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON>", "Common.Translation.warnFileLockedBtnView": "Görüntülemek için aç", "Common.UI.ButtonColored.textAutoColor": "Otomatik", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Sınır yok", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Sınır yok", "Common.UI.ComboDataView.emptyComboText": "Stil yok", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Mevcut", "Common.UI.ExtendedColorDialog.textHexErr": "G<PERSON><PERSON> değer yanlış. <br> Lütfen 000000 ile FFFFFF arasında değer giriniz.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Girilen değer yanlış. <br> Lütfen 0 ile 255 arasında sayısal değer giriniz.", "Common.UI.HSBColorPicker.textNoColor": "Renk yok", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Parolayı gizle", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Parolayı göster", "Common.UI.SearchBar.textFind": "Bul ", "Common.UI.SearchBar.tipCloseSearch": "Aramayı kapat", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textHighlight": "Vurgu sonuçları", "Common.UI.SearchDialog.textMatchCase": "Büyük küçük harfe duyarlı", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON> g<PERSON> metini giriniz", "Common.UI.SearchDialog.textSearchStart": "Metninizi buraya giri<PERSON>z", "Common.UI.SearchDialog.textTitle": "Bul ve Değiştir", "Common.UI.SearchDialog.textTitle2": "Bul", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON> tam keli<PERSON>er", "Common.UI.SearchDialog.txtBtnHideReplace": "Değiştirmeyi Gizle", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON>", "Common.UI.SynchronizeTip.textDontShow": "Bu mesajı bir daha gö<PERSON>me", "Common.UI.SynchronizeTip.textSynchronize": "Döküman başka bir kullanıcı tarafından değiştirildi.<br> Lütfen değişikleri kaydetmek için tıklayın ve güncellemeleri yenileyin.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasik Aydınlık", "Common.UI.Themes.txtThemeDark": "Karanlık", "Common.UI.Themes.txtThemeLight": "Aydınlık", "Common.UI.Window.cancelButtonText": "İptal", "Common.UI.Window.closeButtonText": "Ka<PERSON><PERSON>", "Common.UI.Window.noButtonText": "Hay<PERSON><PERSON>", "Common.UI.Window.okButtonText": "<PERSON><PERSON>", "Common.UI.Window.textConfirmation": "<PERSON><PERSON>", "Common.UI.Window.textDontShow": "Bu mesajı bir daha gö<PERSON>me", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textWarning": "Uyarı", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "adres:", "Common.Views.About.txtLicensee": "LİSANS SAHİBİ", "Common.Views.About.txtLicensor": "LİSANS VEREN", "Common.Views.About.txtMail": "Eposta:", "Common.Views.About.txtPoweredBy": "Tarafından desteklenmektedir", "Common.Views.About.txtTel": "tel:", "Common.Views.About.txtVersion": "S<PERSON>r<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Yazarken Uygula", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Otomatik Düzeltme", "Common.Views.AutoCorrectDialog.textAutoFormat": "Yazarken Otomatik Biçimlendir", "Common.Views.AutoCorrectDialog.textBulleted": "Otomatik madde listesi", "Common.Views.AutoCorrectDialog.textBy": "Tara<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDelete": "Sil", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "\nÇift boşluklu nokta ekle", "Common.Views.AutoCorrectDialog.textFLCells": "Tablo hücrelerinin ilk harfini büyük harf yap", "Common.Views.AutoCorrectDialog.textFLSentence": "Cümlelerin ilk harfini büyük yaz", "Common.Views.AutoCorrectDialog.textHyperlink": "Köprülü internet ve ağ yolları", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON> (--) ve tire (—) ", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematik Otomatik Düzeltme ", "Common.Views.AutoCorrectDialog.textNumbered": "Otomatik numaralı liste", "Common.Views.AutoCorrectDialog.textQuotes": "\"Akıllı tırnak\" ile \"düz tırnak\"", "Common.Views.AutoCorrectDialog.textRecognized": "Bilinen fonksiyonlar", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Aşağıdaki ifadeler tanınan matematik ifadeleridir. Otomatik olarak eğik yazılmazlar.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON> ", "Common.Views.AutoCorrectDialog.textReplaceType": "<PERSON><PERSON>ğiştirin", "Common.Views.AutoCorrectDialog.textReset": "Sıfırla", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Otomatik Düzeltme", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Bilinen fonksiyonlar yalnızca A'dan Z'ye kadar olan harfler<PERSON>, büyük veya küçük harfleri içermelidir.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Eklediğiniz tüm ifadeler kaldırılacak ve kaldırılanlar geri yüklenecektir. Devam etmek istiyor musun?", "Common.Views.AutoCorrectDialog.warnReplace": "%1 için otomatik düzeltme girişi zaten var. Değiştirmek istiyor musun?", "Common.Views.AutoCorrectDialog.warnReset": "Eklediğiniz tüm otomatik düzeltmeler kaldırılacak ve değiştirilenler orijinal değerlerine geri dönecektir. Devam etmek istiyor musun?", "Common.Views.AutoCorrectDialog.warnRestore": "%1 için otomatik düzeltme girişi orijinal değerine sıfırlanacak. <PERSON><PERSON> etmek istiyor musun?", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "<PERSON><PERSON>", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON>", "Common.Views.Comments.mniDateAsc": "En eski", "Common.Views.Comments.mniDateDesc": "En yeni", "Common.Views.Comments.mniFilterGroups": "Gruba göre filtrele", "Common.Views.Comments.mniPositionAsc": "Üstten", "Common.Views.Comments.mniPositionDesc": "Alttan", "Common.Views.Comments.textAdd": "<PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Dökümana yorum ekle", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON> e<PERSON>", "Common.Views.Comments.textAll": "Tümü", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "İptal", "Common.Views.Comments.textClose": "Ka<PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Yorumları kapat", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON><PERSON> buraya giriniz", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textReply": "Yanıtla", "Common.Views.Comments.textResolve": "Çöz", "Common.Views.Comments.textResolved": "Çözüldü", "Common.Views.Comments.textSort": "Yorumları sırala", "Common.Views.CopyWarningDialog.textDontShow": "Bu mesajı bir daha gö<PERSON>me", "Common.Views.CopyWarningDialog.textMsg": "Editör araç çubuğu tuşlarını kullanarak eylemleri kopyala,kes ve yapıştır ve içerik menüsü eylemleri sadece bu editör sekmesiyle yapılabilir. <br><br>Editör sekmesi dışındaki uygulamalara/dan kopyalamak yada yapıştırmak için şu klavye kombinasyonlarını kullanınız:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON> <PERSON>", "Common.Views.CopyWarningDialog.textToCopy": "Kopyalamak için", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "Yapıştırmak için", "Common.Views.DocumentAccessDialog.textLoading": "Yükleniyor...", "Common.Views.DocumentAccessDialog.textTitle": "Paylaşım Ayarları", "Common.Views.ExternalDiagramEditor.textTitle": "<PERSON>ik <PERSON>ö<PERSON>", "Common.Views.Header.labelCoUsersDescr": "Belgeyi düzenleyen kullanıcılar:", "Common.Views.Header.textAddFavorite": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textAdvSettings": "Gelişmiş ayarlar", "Common.Views.Header.textBack": "<PERSON><PERSON><PERSON> kon<PERSON>u aç", "Common.Views.Header.textCompactView": "<PERSON><PERSON> Gizle", "Common.Views.Header.textHideLines": "Cetvelleri Gizle", "Common.Views.Header.textHideNotes": "Notları gizle", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON> Çubuğ<PERSON>u Gizle", "Common.Views.Header.textRemoveFavorite": "<PERSON>av<PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveBegin": "Kay<PERSON>ili<PERSON>r...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON>iklikler kaydedildi", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON>iklikler kaydedildi", "Common.Views.Header.textZoom": "Yakınlaştırma", "Common.Views.Header.tipAccessRights": "Belge eri<PERSON><PERSON> ha<PERSON> yönet", "Common.Views.Header.tipDownload": "Dosyayı indir", "Common.Views.Header.tipGoEdit": "<PERSON><PERSON><PERSON> be<PERSON> d<PERSON>", "Common.Views.Header.tipPrint": "Belgeyi yazdır", "Common.Views.Header.tipRedo": "<PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON>", "Common.Views.Header.tipUndock": "Ayrı pencereye çıkarın", "Common.Views.Header.tipViewSettings": "Ayarları Göster", "Common.Views.Header.tipViewUsers": "Kullanıcıları görüntüle ve belge erişim haklarını yönet", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON><PERSON> değiştir", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON>ı<PERSON>", "Common.Views.History.textCloseHistory": "Geçmişi kapat", "Common.Views.History.textHide": "Dar<PERSON><PERSON>", "Common.Views.History.textHideAll": "Detaylı değişiklikleri sakla", "Common.Views.History.textRestore": "<PERSON><PERSON>", "Common.Views.History.textShow": "Genişlet", "Common.Views.History.textShowAll": "Ayrıntılı değişiklikleri göster", "Common.Views.History.textVer": "sür.", "Common.Views.ImageFromUrlDialog.textUrl": "Resim URL'sini yapıştır:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON>u alan \"http://www.example.com\" formatında URL olmalıdır", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Geçerli satır ve sütun sayısı belirtmelisiniz.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON> <PERSON><PERSON><PERSON>: {0}.", "Common.Views.InsertTableDialog.txtMinText": "<PERSON><PERSON> <PERSON><PERSON> minimum değer: {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "<PERSON><PERSON> di<PERSON>", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON> ", "Common.Views.ListSettingsDialog.textNumbering": "Numaralı", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON> işaret<PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Renk", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON><PERSON> madde", "Common.Views.ListSettingsDialog.txtNone": "Hiç<PERSON>i", "Common.Views.ListSettingsDialog.txtOfText": "Metnin %'si", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Başlangıç", "Common.Views.ListSettingsDialog.txtSymbol": "<PERSON>m<PERSON>", "Common.Views.ListSettingsDialog.txtTitle": "Liste Ayarları", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "Dosyayı Kapat", "Common.Views.OpenDialog.txtEncoding": "Kodlama", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Dosyayı Açmak için Parola Girin", "Common.Views.OpenDialog.txtPassword": "Pa<PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Parolayı girip dosyayı açtığınızda, dosyanın mevcut parolası sıfırlanacaktır.", "Common.Views.OpenDialog.txtTitle": "%1 seçenekleri seçin", "Common.Views.OpenDialog.txtTitleProtected": "Korumalı dosya", "Common.Views.PasswordDialog.txtDescription": "<PERSON><PERSON> be<PERSON><PERSON> korumak i<PERSON>in bir parola beli<PERSON>in", "Common.Views.PasswordDialog.txtIncorrectPwd": "Onay şifresi aynı değil", "Common.Views.PasswordDialog.txtPassword": "Pa<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "Parolayı tekrar girin", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Dikkat: Parol<PERSON>ı ka<PERSON>beder veya unutursanız, kurtarılamaz. Lütfen parolayı güvenli bir yerde saklayın.", "Common.Views.PluginDlg.textLoading": "Yükleniyor", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textClosePanel": "Eklentiyi kapat", "Common.Views.Plugins.textLoading": "Yükleniyor", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Common.Views.Protection.hintPwd": "Parolayı değiştir veya sil", "Common.Views.Protection.hintSignature": "Dijital imza veya imza satırı ekleyin", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "Parolayı Değiştir", "Common.Views.Protection.txtDeletePwd": "Parolayı sil", "Common.Views.Protection.txtEncrypt": "Şifrele", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON> imza ekle", "Common.Views.Protection.txtSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON>za <PERSON>ı<PERSON> e<PERSON>", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON> adı", "Common.Views.RenameDialog.txtInvalidName": "Dosya adı aşağıdaki karakterlerden herhangi birini içeremez:", "Common.Views.ReviewChanges.hintNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.hintPrev": "Önceki değişikliğe", "Common.Views.ReviewChanges.strFast": "Hızlı", "Common.Views.ReviewChanges.strFastDesc": "Gerçek zamanlı ortak çalışma. Bütün değişiklikler otomatik olarak kaydedilir.", "Common.Views.ReviewChanges.strStrict": "Kat<PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "Değişiklikleri kaydetmek için 'Kaydet' butonunu kullanın.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Mevcut değişiklikleri kabul et", "Common.Views.ReviewChanges.tipCoAuthMode": "Birlikte düzenleme modunu a<PERSON>la", "Common.Views.ReviewChanges.tipCommentRem": "Yorumları kaldır", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Mevcut yorumları kaldır", "Common.Views.ReviewChanges.tipCommentResolve": "Yorumları çöz", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Mevcut yorumları çözümle", "Common.Views.ReviewChanges.tipHistory": "Versiyon geçmişini göster", "Common.Views.ReviewChanges.tipRejectCurrent": "Mevcut Değişiklikleri Reddet", "Common.Views.ReviewChanges.tipReview": "Değişiklikleri İzle", "Common.Views.ReviewChanges.tipReviewView": "Değişikliklerin gösterilmesini istediğiniz modu seçin", "Common.Views.ReviewChanges.tipSetDocLang": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON><PERSON> deneti<PERSON>", "Common.Views.ReviewChanges.tipSharing": "Belge eri<PERSON><PERSON> ha<PERSON> yönet", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON>m Değişiklikleri Kabul Et", "Common.Views.ReviewChanges.txtAcceptChanges": "Değişiklikleri Kabul Et", "Common.Views.ReviewChanges.txtAcceptCurrent": "Mevcut Değişiklikleri Kabul Et", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Ka<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Ortak Düzenleme Modu", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON><PERSON> yo<PERSON>ları kaldır", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Mevcut yorumları kaldır", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON>ımı kaldır", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON><PERSON> mevcut yorumlarımı kaldır", "Common.Views.ReviewChanges.txtCommentRemove": "Kaldır", "Common.Views.ReviewChanges.txtCommentResolve": "Çöz", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON>m Yo<PERSON>ları Çöz", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Mevcut yorumları çözümle", "Common.Views.ReviewChanges.txtCommentResolveMy": "Yorumlarımı Çöz", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Mevcut Yorumlarımı Çöz", "Common.Views.ReviewChanges.txtDocLang": "Dil", "Common.Views.ReviewChanges.txtFinal": "T<PERSON>m değişiklikler onaylandı (Önizleme)", "Common.Views.ReviewChanges.txtFinalCap": "Son", "Common.Views.ReviewChanges.txtHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> (Düzenleme)", "Common.Views.ReviewChanges.txtMarkupCap": "İşaretleme", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> reddedildi (Önizleme)", "Common.Views.ReviewChanges.txtOriginalCap": "Orjinal", "Common.Views.ReviewChanges.txtPrev": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON>m Değişiklikleri Reddet", "Common.Views.ReviewChanges.txtRejectChanges": "Değişiklikleri Reddet", "Common.Views.ReviewChanges.txtRejectCurrent": "Mevcut Değişiklikleri Reddet", "Common.Views.ReviewChanges.txtSharing": "Paylaşım", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON> deneti<PERSON>", "Common.Views.ReviewChanges.txtTurnon": "Değişiklikleri İzle", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON> e<PERSON>", "Common.Views.ReviewPopover.textCancel": "İptal", "Common.Views.ReviewPopover.textClose": "Ka<PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textMention": "+bah<PERSON>me belgeye eri<PERSON>im sağlayacak ve bir e-posta gönderecek", "Common.Views.ReviewPopover.textMentionNotify": "+mention kullanıcıyı e-posta yoluyla bilgilendirecek", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "Yanıtla", "Common.Views.ReviewPopover.textResolve": "Çöz", "Common.Views.ReviewPopover.txtDeleteTip": "Sil", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Yükleniyor", "Common.Views.SaveAsDlg.textTitle": "Kaydetmek için <PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "Büyük küçük harfe duyarlı ", "Common.Views.SearchPanel.textCloseSearch": "Aramayı kapat ", "Common.Views.SearchPanel.textFind": "Bul", "Common.Views.SearchPanel.textFindAndReplace": "Bul ve Değiştir", "Common.Views.SearchPanel.textMatchUsingRegExp": "Normal ifadeler kullanarak eşleştir ", "Common.Views.SearchPanel.textNoMatches": "Eşleşme yok", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON><PERSON> son<PERSON>u bulu<PERSON>ı", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON><PERSON> ", "Common.Views.SelectFileDlg.textLoading": "Yükleniyor", "Common.Views.SelectFileDlg.textTitle": "Veri Kaynağını Seçin", "Common.Views.SignDialog.textBold": "Kalı<PERSON>", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "İmzalayan adını girin", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "İmzalayan adı boş bırakılmamalıdır.", "Common.Views.SignDialog.textPurpose": "<PERSON>u belgeyi im<PERSON>ın amacı", "Common.Views.SignDialog.textSelect": "Seç", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "<PERSON><PERSON><PERSON>ü<PERSON>ü<PERSON>r", "Common.Views.SignDialog.textTitle": "Belgeyi <PERSON>a", "Common.Views.SignDialog.textUseImage": "veya bir resmi imza olarak kullanmak için '<PERSON><PERSON><PERSON>i tıklayın", "Common.Views.SignDialog.textValid": "%1 ile %2 arasında geçerlidir", "Common.Views.SignDialog.tipFontName": "Yazı Tipi", "Common.Views.SignDialog.tipFontSize": "Yazı Boyutu", "Common.Views.SignSettingsDialog.textAllowComment": "İmzalayanın imza iletişim kutusuna yorum eklemesine izin ver", "Common.Views.SignSettingsDialog.textInfoEmail": "E-posta", "Common.Views.SignSettingsDialog.textInfoName": "İsim", "Common.Views.SignSettingsDialog.textInfoTitle": "İmzalayan Ünvanı", "Common.Views.SignSettingsDialog.textInstructions": "İmzalayan <PERSON><PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON>ında imzalama ta<PERSON>hini g<PERSON>", "Common.Views.SignSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "<PERSON><PERSON><PERSON> kodu", "Common.Views.SymbolTableDialog.textCopyright": "Telif <PERSON>", "Common.Views.SymbolTableDialog.textDCQuote": "Çift Tırnak (Kapanış)", "Common.Views.SymbolTableDialog.textDOQuote": "Çift Tırnak (Açılış)", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Uzun Tire", "Common.Views.SymbolTableDialog.textEmSpace": "Uzun Boşluk", "Common.Views.SymbolTableDialog.textEnDash": "Kısa Tire", "Common.Views.SymbolTableDialog.textEnSpace": "Boşluk", "Common.Views.SymbolTableDialog.textFont": "Yazı Tipi", "Common.Views.SymbolTableDialog.textNBHyphen": "Bölünemez kısa <PERSON>", "Common.Views.SymbolTableDialog.textNBSpace": "Bölünemez Boşluk", "Common.Views.SymbolTableDialog.textPilcrow": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em boşluk", "Common.Views.SymbolTableDialog.textRange": "Aralık", "Common.Views.SymbolTableDialog.textRecent": "<PERSON><PERSON>k k<PERSON>anılan simgeler", "Common.Views.SymbolTableDialog.textRegistered": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSCQuote": "Tek Tırnak (Kapanış)", "Common.Views.SymbolTableDialog.textSection": "Bölüm", "Common.Views.SymbolTableDialog.textShortcut": "Kısayol tuşu", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Tek Tırnak (Açılış)", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON> karak<PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "<PERSON>m<PERSON>", "Common.Views.SymbolTableDialog.textTradeMark": "Telif <PERSON>", "Common.Views.UserNameDialog.textDontShow": "Bana bir daha sorma", "Common.Views.UserNameDialog.textLabel": "Etiket:", "Common.Views.UserNameDialog.textLabelError": "Etiket boş olamaz.", "PE.Controllers.LeftMenu.leavePageText": "Bu belgedeki kaydedilmemiş tüm değişiklikler kaybolacak.<br> Bunları kaydetmek için \"İptal\"i ve ardından \"Kaydet\"i tıklayın. Kaydedilmemiş tüm değişiklikleri atmak için \"Tamam\" ı tıklayın.", "PE.Controllers.LeftMenu.newDocumentTitle": "İsim veril<PERSON>um", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Uyarı", "PE.Controllers.LeftMenu.requestEditRightsText": "Düzenleme hakları isteniyor...", "PE.Controllers.LeftMenu.textLoadHistory": "Versiyon geçmişi yükleniyor...", "PE.Controllers.LeftMenu.textNoTextFound": "Aradığınız veri bulunamadı. Lütfen arama seçeneklerinizi ayarlayınız.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Değiştirme yapıldı. {0} olay at<PERSON><PERSON>.", "PE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON>ma yapıldı. Değişiklikler: {0}", "PE.Controllers.LeftMenu.txtUntitled": "Başlıksız", "PE.Controllers.Main.applyChangesTextText": "<PERSON><PERSON>...", "PE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON><PERSON><PERSON> süresi aşıldı.", "PE.Controllers.Main.criticalErrorExtText": "Döküman listesine dönmek için \"Tamam\"'a tıklayın", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "İndirme başarısız oldu.", "PE.Controllers.Main.downloadTextText": "Belge indiriliyor...", "PE.Controllers.Main.downloadTitleText": "Belge indiriliyor", "PE.Controllers.Main.errorAccessDeny": "Hakkınız olmayan bir eylem gerçekleştirmeye çalışıyorsunuz.<br>Lütfen Belge Sunucu yöneticinize başvurun.", "PE.Controllers.Main.errorBadImageUrl": "Resim URL'si yanlış", "PE.Controllers.Main.errorCoAuthoringDisconnect": "<PERSON><PERSON><PERSON> bağlantısı kesildi. Döküman şu an düzenlenemez.", "PE.Controllers.Main.errorComboSeries": "Bir kombinasyon grafiği oluşturmak için en az iki veri serisi seçin.", "PE.Controllers.Main.errorConnectToServer": "Belge kaydedilemedi. Lütfen internet bağlantınızı kontrol edin veya yöneticiniz ile iletişime geçin. <br>'Tamam' tuşuna tıkladığınızda belgeyi indirebileceksiniz.", "PE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> hata. <br><PERSON><PERSON> tabanı bağlantı hatası. Hata devam ederse lütfen destek ile iletişime geçin.", "PE.Controllers.Main.errorDataEncrypted": "Şifreli <PERSON>ğişiklikler algılandı, çözülemiyor.", "PE.Controllers.Main.errorDataRange": "Yanlış veri aralığı.", "PE.Controllers.Main.errorDefaultMessage": "Hata kodu: %1", "PE.Controllers.Main.errorEditingDownloadas": "<PERSON><PERSON><PERSON><PERSON>ışırken hata meydana geldi. 'Farklı Kaydet' seçeneğini kullanarak dosyayı bilgisayarınıza yedekleyin.", "PE.Controllers.Main.errorEditingSaveas": "Belgeyle <PERSON>ışma sırasında bir hata oluştu.<br><PERSON><PERSON><PERSON> <PERSON><PERSON> kopyasını bilgisayarınızın sabit diskine kaydetmek için 'Farklı kaydet...' seçeneğini kullanın.", "PE.Controllers.Main.errorEmailClient": "E-posta istemcisi bulunamadı.", "PE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON>a parola k<PERSON>alıdır ve açılamaz.", "PE.Controllers.Main.errorFileSizeExceed": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> i<PERSON>in belirlenen limiti aşıyor.<br>Ayr<PERSON>nt<PERSON>lar için lütfen Doküman Sunucusu yöneticinizle iletişime geçin.", "PE.Controllers.Main.errorForceSave": "Dosya kaydedilirken bir hata oluştu. Dosyayı bilgisayarınıza kaydetmek için lütfen 'Farklı Kaydet' seçeneğini kullanın veya daha sonra tekrar deneyin.", "PE.Controllers.Main.errorInconsistentExtDocx": "Dosya açılırken bir hata oluştu.<br>Dosya içeriği metin be<PERSON> (örn. docx) karşılık geliyor, ancak dosyanın uzantısı tutarsız: %1.", "PE.Controllers.Main.errorKeyEncrypt": "Bilinmeyen anahtar tanımlayıcı", "PE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON>ın süresi doldu", "PE.Controllers.Main.errorLoadingFont": "Yazı tipleri yüklenmedi.<br>Lütfen Doküman Sunucusu yöneticinize başvurun.", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON> başarısız.", "PE.Controllers.Main.errorServerVersion": "Editör versiyonu gü<PERSON>di. <PERSON><PERSON>ğişiklikler uygulanacaktır.", "PE.Controllers.Main.errorSessionAbsolute": "Belge düzenleme oturumu sona erdi. Lütfen sayfayı yeniden yükleyin.", "PE.Controllers.Main.errorSessionIdle": "Belge oldukça uzun süredir düzenlenmedi. Lütfen sayfayı yeniden yükleyin.", "PE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON> bağlantısı yarıda kesildi. Lütfen sayfayı yeniden yükleyin.", "PE.Controllers.Main.errorSetPassword": "<PERSON><PERSON><PERSON>.", "PE.Controllers.Main.errorStockChart": "Yanlış dizi sırası. Stok grafiği oluşturma için tablodaki verileri şu sırada yerleştirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> fiyatı, maks<PERSON><PERSON> fiyat, minimum fiyat, kapan<PERSON>ş fiyatı. ", "PE.Controllers.Main.errorToken": "Belge güvenlik belirteci doğru şekilde oluşturulmamış.<br>Lütfen Belge Sunucu yöneticinize başvurun.", "PE.Controllers.Main.errorTokenExpire": "Belge güvenlik belirteci süresi doldu. <br> Lütfen Belge Sunucusu yöneticinize başvurun.", "PE.Controllers.Main.errorUpdateVersion": "Dosya versi<PERSON>. Sayfa yenilenecektir.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internet bağlantısı sağlandı, dosya versiyonu değiştirildi. <br>Çalışmaya devam etmeden önce, dosyayı indirin veya içeriğini kopyalayıp herhangi bir kayıp olmadığından emin olun ve sayfayı yenileyin.", "PE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "PE.Controllers.Main.errorUsersExceed": "Fiyat planının izin verdiği kullanıcı sayısı aşıldı", "PE.Controllers.Main.errorViewerDisconnect": "Bağlantı kesildi. Belgeyi yine de görüntüleyebilirsiniz,<br>an<PERSON><PERSON> bağlantı yeniden kurulana ve sayfa yeniden yüklenene kadar indiremez veya yazdıramazsınız.", "PE.Controllers.Main.leavePageText": "Sunumda kaydedilmemiş değişiklikler var. Kaydetmek için 'Bu Sayfada Kal'a daha sonra da 'Kaydet'e tıklayınız.Kaydedilmemiş tüm değişiklikleri göz ardı etmek için 'Bu Sayfadan Ayrıl'a tıklayın.", "PE.Controllers.Main.leavePageTextOnClose": "Bu sunudaki kaydedilmemiş tüm değişiklikler kaybolacak.<br> Bunları kaydetmek için \"İptal\"i ve ardından \"Kaydet\"i tıklayın. Kaydedilmemiş tüm değişiklikleri atmak için \"Tamam\" ı tıklayın.", "PE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON>...", "PE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.loadFontTextText": "<PERSON><PERSON>...", "PE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.loadImagesTextText": "Resimler yükleniyor...", "PE.Controllers.Main.loadImagesTitleText": "Resimler yükleniyor", "PE.Controllers.Main.loadImageTextText": "<PERSON>si<PERSON>...", "PE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadingDocumentTextText": "<PERSON><PERSON> y<PERSON>r...", "PE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.loadThemeTextText": "<PERSON><PERSON>...", "PE.Controllers.Main.loadThemeTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.notcriticalErrorTitle": "Uyarı", "PE.Controllers.Main.openErrorText": "<PERSON><PERSON>a a<PERSON>ılırken bir hata oluş<PERSON>", "PE.Controllers.Main.openTextText": "<PERSON><PERSON> açılıyor...", "PE.Controllers.Main.openTitleText": "<PERSON><PERSON> Açılıyor", "PE.Controllers.Main.printTextText": "<PERSON><PERSON> yazdırılıyor...", "PE.Controllers.Main.printTitleText": "<PERSON><PERSON>zdırılıyor", "PE.Controllers.Main.reloadButtonText": "Sayfayı Yenile", "PE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON><PERSON> <PERSON>u anda bu sunumu düzenliyor. Lütfen daha sonra tekrar deneyin.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> ka<PERSON> bir hata <PERSON>", "PE.Controllers.Main.saveErrorTextDesktop": "Bu dosya kaydedilemez veya oluşturulamaz.<br><PERSON><PERSON><PERSON> nedenler şunlardır: <br>1. <PERSON><PERSON><PERSON> salt okunurdur. <br>2. <PERSON><PERSON><PERSON> kullanıcılar tarafından düzenleniyor. <br>3. Disk dolu veya bozuk. ", "PE.Controllers.Main.saveTextText": "<PERSON><PERSON> kaydediliyor...", "PE.Controllers.Main.saveTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.scriptLoadError": "Bağlantı çok yavaş, bileş<PERSON><PERSON>in bazıları yüklenemedi. Lütfen sayfayı yenileyin.", "PE.Controllers.Main.splitDividerErrorText": "Satır sayısı %1'in böleni olmalıdır.", "PE.Controllers.Main.splitMaxColsErrorText": "Sütun sayısı %1'den az olmalıdır.", "PE.Controllers.Main.splitMaxRowsErrorText": "Satır sayısı %1'den az olmalıdır.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textBuyNow": "Websitesini ziyaret edin", "PE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON>iklikler kaydedildi", "PE.Controllers.Main.textClose": "Ka<PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "<PERSON>cu kapamak için tıklayın", "PE.Controllers.Main.textContactUs": "Satış departmanı ile iletişime geçin", "PE.Controllers.Main.textConvertEquation": "<PERSON><PERSON> den<PERSON><PERSON>, denkle<PERSON> düzenleyicinin artık desteklenmeyen eski bir sürümüyle oluşturulmuştur. Düzenlemek için denklemi Office Math ML formatına dönüştürün.<br>Şimdi dönüştürülsün mü?", "PE.Controllers.Main.textCustomLoader": "Lütfen lisans şartlarına göre yükleyiciyi değiştirme hakkınız olmadığını unutmayın.<br>Fiyat teklifi almak için lütfen Satış Departmanımızla iletişime geçin.", "PE.Controllers.Main.textDisconnect": "Bağlantı kesildi", "PE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "Dosya otomatik makrolar içeriyor.<br>Makroları çalıştırmak istiyor musunuz?", "PE.Controllers.Main.textLearnMore": "<PERSON>ha fazlası", "PE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON>", "PE.Controllers.Main.textLongName": "128 Karakterden kısa bir ad girin.", "PE.Controllers.Main.textNoLicenseTitle": "Lisans limitine ulaşıldı.", "PE.Controllers.Main.textPaidFeature": "Ücretli Ö<PERSON>lik", "PE.Controllers.Main.textReconnect": "Yeniden bağlanıldı", "PE.Controllers.Main.textRemember": "Seçimimi tüm dosyalar için hatırla", "PE.Controllers.Main.textRenameError": "Kullanıcı adı boş bırakılmamalıdır.", "PE.Controllers.Main.textRenameLabel": "İşbirliği için kullanılacak bir ad girin", "PE.Controllers.Main.textRequestMacros": "\n<PERSON><PERSON>, URL'ye istekte bulunur.  %1 isteğine izin vermek istiyor musunuz? ", "PE.Controllers.Main.textShape": "Şekil", "PE.Controllers.Main.textStrict": "Strict mode", "PE.Controllers.Main.textText": "<PERSON><PERSON>", "PE.Controllers.Main.textTryUndoRedo": "Geri al/Yinele fonksiyonları hızlı ortak çalışma modunda devre dışı kalır.<br>'Katı mod' tuşuna tıklayarak Katı ortak düzenleme moduna geçebilir ve diğer kullanıcıların müdehalesi olmadan, yalnızca siz belgeyi kaydettikten sonra değişiklik yapılmasını sağlayabilirsiniz. Ortak çalışma moduna tekrar dönmek için Gelişmiş ayarları kullanabilirsiniz.", "PE.Controllers.Main.textTryUndoRedoWarn": "Hızlı ortak düzenleme modunda geri al/yinele fonksiyonları devre dışıdır.", "PE.Controllers.Main.titleLicenseExp": "Lisans süresi doldu", "PE.Controllers.Main.titleServerVersion": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtAddFirstSlide": "İlk sunumu eklemek için tıklayın", "PE.Controllers.Main.txtAddNotes": "Not eklemek için tı<PERSON>ın", "PE.Controllers.Main.txtArt": "<PERSON><PERSON> buraya giri<PERSON>", "PE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON>", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtClipArt": "Küçük Resim", "PE.Controllers.Main.txtDateTime": "Ta<PERSON>h ve saat", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Diagram Başlığı", "PE.Controllers.Main.txtEditingMode": "<PERSON><PERSON><PERSON>leme modunu belirle...", "PE.Controllers.Main.txtErrorLoadHistory": "Geçmiş yüklenemedi", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtFooter": "Altbilgi", "PE.Controllers.Main.txtHeader": "Üst Bilgi", "PE.Controllers.Main.txtImage": "Resim", "PE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Yükleniyor...", "PE.Controllers.Main.txtMath": "Matematik", "PE.Controllers.Main.txtMedia": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtNeedSynchronize": "G<PERSON><PERSON><PERSON><PERSON> var", "PE.Controllers.Main.txtNone": "Hiç<PERSON>i", "PE.Controllers.Main.txtPicture": "Resim", "PE.Controllers.Main.txtRectangles": "Dikdörtgen<PERSON>", "PE.Controllers.Main.txtSeries": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Satır Belirtme Çizgisi 1 (Kenarlık ve Vurgu Çubuğu)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Satır Belirtme Çizgisi 2 (Kenarlık ve Vurgu Çubuğu)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Satır Belirtme Çizgisi 3 (Kenarlık ve Vurgu Çubuğu)", "PE.Controllers.Main.txtShape_accentCallout1": "Satır Belirtme Çizgisi 1 (Vurgu Çubuğu)", "PE.Controllers.Main.txtShape_accentCallout2": "Satır Belirtme Çizgisi 2 (Vurgu Çubuğu)", "PE.Controllers.Main.txtShape_accentCallout3": "Satır Belirtme Çizgisi 3 (Vurgu Çubuğu)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON> <PERSON>ya Ö<PERSON>", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Başlangıç <PERSON>", "PE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>ğ<PERSON>", "PE.Controllers.Main.txtShape_actionButtonDocument": "Belge Düğmesi", "PE.Controllers.Main.txtShape_actionButtonEnd": "Bitir <PERSON>", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "İleri veya İleri Düğmesi", "PE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonInformation": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonMovie": "Film Düğmesi", "PE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_arc": "Yay", "PE.Controllers.Main.txtShape_bentArrow": "Bükülmüş Ok", "PE.Controllers.Main.txtShape_bentConnector5": "Dirsek Konnektörü", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Dirsek Ok Konnektörü", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Dirsek Çift Ok Konnektörü", "PE.Controllers.Main.txtShape_bentUpArrow": "Dışa Bükülmüş Ok", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Blok yay", "PE.Controllers.Main.txtShape_borderCallout1": "Satır Belirtme Çizgisi 1", "PE.Controllers.Main.txtShape_borderCallout2": "Satır Belirtme Çizgisi 2", "PE.Controllers.Main.txtShape_borderCallout3": "Satır Belirtme Çizgisi 3", "PE.Controllers.Main.txtShape_bracePair": "Çift Ayraç", "PE.Controllers.Main.txtShape_callout1": "Satır Belirtme Çizgisi 1 (Kenarlık Yok)", "PE.Controllers.Main.txtShape_callout2": "Satır Belirtme Çizgisi 2 (Kenarlık Yok)", "PE.Controllers.Main.txtShape_callout3": "Satır Belirtme Çizgisi 3 (Kenarlık Yok)", "PE.Controllers.Main.txtShape_can": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_chevron": "Şerit", "PE.Controllers.Main.txtShape_chord": "Ki<PERSON>ş", "PE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cloud": "Bulut", "PE.Controllers.Main.txtShape_cloudCallout": "Bulut <PERSON>irt<PERSON>", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Kavisli Bağlayıcı ", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Kavisli Ok Bağlayıcı ", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Kavisli Çift Ok Bağlayıcı", "PE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedLeftArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedUpArrow": "Kavisli Yukarı OK", "PE.Controllers.Main.txtShape_decagon": "Dekagon", "PE.Controllers.Main.txtShape_diagStripe": "Çapraz Çizgi", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "Onikigen", "PE.Controllers.Main.txtShape_donut": "Hal<PERSON>", "PE.Controllers.Main.txtShape_doubleWave": "Çift Dalga", "PE.Controllers.Main.txtShape_downArrow": "Aşağı <PERSON>", "PE.Controllers.Main.txtShape_downArrowCallout": "Aşağı Ok Belirtme Çizgisi", "PE.Controllers.Main.txtShape_ellipse": "Elips", "PE.Controllers.Main.txtShape_ellipseRibbon": "Kavisli Aşağı Ribbon", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Ka<PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Akış çizelgesi: Alternatif <PERSON>üreç", "PE.Controllers.Main.txtShape_flowChartCollate": "Akış çizelgesi: <PERSON><PERSON>la", "PE.Controllers.Main.txtShape_flowChartConnector": "Akış çizelgesi: Bağlayıcı", "PE.Controllers.Main.txtShape_flowChartDecision": "Akış çizelgesi: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDelay": "Akış çizelgesi: Gecikme", "PE.Controllers.Main.txtShape_flowChartDisplay": "Akış çizelgesi: Ekran", "PE.Controllers.Main.txtShape_flowChartDocument": "Akış çizelgesi: Belge", "PE.Controllers.Main.txtShape_flowChartExtract": "Akış çizelgesi: Özü", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Akış çizelgesi: Veri", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Akış çizelgesi: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Akış çizelgesi: Manyetik Disk", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Akış çizelgesi: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Akış çizelgesi: Sıralı Erişimli <PERSON>", "PE.Controllers.Main.txtShape_flowChartManualInput": "Akış çizelgesi: <PERSON>", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Akış çizelgesi: <PERSON>", "PE.Controllers.Main.txtShape_flowChartMerge": "Akış çizelgesi: Birleştirme", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Akış çizelgesi: Çoklu belge", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Akış çizelgesi: Sayfa Dışı Bağlayıcı", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Akış çizelgesi: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartOr": "Akış şeması: Veya", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Akış çizelgesi: Önceden Tanımlanmış Süreç", "PE.Controllers.Main.txtShape_flowChartPreparation": "Akış şeması: Hazırlık", "PE.Controllers.Main.txtShape_flowChartProcess": "Akış çizelgesi: S<PERSON>reç", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Akış çizelgesi: Kart", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Akış çizelgesi: Delikli Bant", "PE.Controllers.Main.txtShape_flowChartSort": "Akış çizelgesi: Sıralama", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Akış çizelgesi: Toplama Kavşağı", "PE.Controllers.Main.txtShape_flowChartTerminator": "Akış çizelgesi: Sonlandırıcı", "PE.Controllers.Main.txtShape_foldedCorner": "Katlanmış Köşe", "PE.Controllers.Main.txtShape_frame": "Ç<PERSON><PERSON>eve", "PE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_heptagon": "Yedigen", "PE.Controllers.Main.txtShape_hexagon": "Altıgen", "PE.Controllers.Main.txtShape_homePlate": "Beşgen", "PE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_irregularSeal1": "Patlama 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Patlama 2", "PE.Controllers.Main.txtShape_leftArrow": "Sol Ok", "PE.Controllers.Main.txtShape_leftArrowCallout": "Sol Ok Belirtme <PERSON>i", "PE.Controllers.Main.txtShape_leftBrace": "Sol Ayraç", "PE.Controllers.Main.txtShape_leftBracket": "Sol Köşeli Ayraç", "PE.Controllers.Main.txtShape_leftRightArrow": "Sol Sağ Ok", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Sol Sağ Ok Belirtme Çizgisi ", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Sol Sağ Yukarı Ok", "PE.Controllers.Main.txtShape_leftUpArrow": "Sol Yukarı Ok", "PE.Controllers.Main.txtShape_lightningBolt": "Ş<PERSON>şek", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "Ok", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Çift Ok", "PE.Controllers.Main.txtShape_mathDivide": "Bölüm", "PE.Controllers.Main.txtShape_mathEqual": "Eş<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Çoğalt", "PE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathPlus": "Artı", "PE.Controllers.Main.txtShape_moon": "Ay", "PE.Controllers.Main.txtShape_noSmoking": "<PERSON><PERSON><PERSON> \"Yok\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Çentikli Sağ Ok ", "PE.Controllers.Main.txtShape_octagon": "Sekizgen", "PE.Controllers.Main.txtShape_parallelogram": "Paralelkenar", "PE.Controllers.Main.txtShape_pentagon": "Beşgen", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "İmzala", "PE.Controllers.Main.txtShape_plus": "Artı", "PE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline2": "Serbest form", "PE.Controllers.Main.txtShape_quadArrow": "Dörtlü ok", "PE.Controllers.Main.txtShape_quadArrowCallout": "Dörtlü Ok Belirtme Çizgisi", "PE.Controllers.Main.txtShape_rect": "Dikdörtgen", "PE.Controllers.Main.txtShape_ribbon": "Aşağı Ribbon", "PE.Controllers.Main.txtShape_ribbon2": "Yukarı Ribbon", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightArrowCallout": "Sağ Ok Belirtme Çizgisi ", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBracket": "Sağ Köşeli Ayraç", "PE.Controllers.Main.txtShape_round1Rect": "Yuvarlak Tek Köşe Dikdörtgen", "PE.Controllers.Main.txtShape_round2DiagRect": "Yuvarlak Çapraz Köşe Dikdörtgen", "PE.Controllers.Main.txtShape_round2SameRect": "Yuvarlak Aynı Kenar Köşe Dikdörtgen", "PE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON> Köşe Dikdörtgen", "PE.Controllers.Main.txtShape_rtTriangle": "Sağ Üçgen", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Tek Köşeli Dikdörtgen Kes", "PE.Controllers.Main.txtShape_snip2DiagRect": "Çapraz Köşe Dikdörtgen Kes", "PE.Controllers.Main.txtShape_snip2SameRect": "Aynı Yan Köşe Dikdörtgeni Kes", "PE.Controllers.Main.txtShape_snipRoundRect": "Yuvarlak Tek Köşe Dikdörtgen Kes", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "10 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star12": "12 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star16": "16 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star24": "24 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star32": "32 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star4": "4 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star5": "5 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star6": "6 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star7": "7 Köşeli Yıldız", "PE.Controllers.Main.txtShape_star8": "8 Köşeli Yıldız", "PE.Controllers.Main.txtShape_stripedRightArrow": "Çizgili Sağ Ok", "PE.Controllers.Main.txtShape_sun": "<PERSON><PERSON><PERSON>ş", "PE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_triangle": "Üçgen", "PE.Controllers.Main.txtShape_upArrow": "Yu<PERSON><PERSON>", "PE.Controllers.Main.txtShape_upArrowCallout": "Yukarı Ok Belirtme Çizgisi", "PE.Controllers.Main.txtShape_upDownArrow": "Yukarı Aşağı Ok", "PE.Controllers.Main.txtShape_uturnArrow": "U-Dön<PERSON>ş <PERSON>", "PE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_wave": "Dalga", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Belirtme <PERSON>i", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Dikdörtgen Belirtme Çizgisi", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON><PERSON> Dikdörtgen Belirtme ", "PE.Controllers.Main.txtSldLtTBlank": "Boş", "PE.Controllers.Main.txtSldLtTChart": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChartAndTx": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Küçük Resim ve Metin", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Küçük Resim ve Dikey Metin", "PE.Controllers.Main.txtSldLtTCust": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTMediaAndTx": "<PERSON><PERSON><PERSON> <PERSON>", "PE.Controllers.Main.txtSldLtTObj": "Başlık ve Obje", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "<PERSON><PERSON><PERSON> ve İki Obje", "PE.Controllers.Main.txtSldLtTObjAndTx": "<PERSON><PERSON><PERSON> <PERSON>", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "Metin Üstünde Obje", "PE.Controllers.Main.txtSldLtTObjTx": "Başlık, Obje ve Altyazı", "PE.Controllers.Main.txtSldLtTPicTx": "Resim ve Başlık", "PE.Controllers.Main.txtSldLtTSecHead": "Bölüm Üst Bilgisi", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Başlık", "PE.Controllers.Main.txtSldLtTTitleOnly": "Sadece Ba<PERSON>lık", "PE.Controllers.Main.txtSldLtTTwoColTx": "İki <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "İki obje ve obje", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "<PERSON>ki O<PERSON> ve <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Metin üstünde iki obje", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "<PERSON><PERSON> Met<PERSON> ve <PERSON>", "PE.Controllers.Main.txtSldLtTTx": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTxAndChart": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "<PERSON><PERSON> ve Küçük Resim", "PE.Controllers.Main.txtSldLtTTxAndMedia": "<PERSON><PERSON> ve <PERSON>", "PE.Controllers.Main.txtSldLtTTxAndObj": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "<PERSON><PERSON> ve <PERSON>", "PE.Controllers.Main.txtSldLtTTxOverObj": "Obje üstünde Metin", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "<PERSON><PERSON> Başlık ve Metin", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Dikey Başlık ve Grafik Üstünde Metin", "PE.Controllers.Main.txtSldLtTVertTx": "<PERSON><PERSON>", "PE.Controllers.Main.txtSlideNumber": "<PERSON><PERSON><PERSON> numarası", "PE.Controllers.Main.txtSlideSubtitle": "Slayt alt başlığı", "PE.Controllers.Main.txtSlideText": "<PERSON><PERSON><PERSON> metni", "PE.Controllers.Main.txtSlideTitle": "<PERSON><PERSON>t başlığı", "PE.Controllers.Main.txtStarsRibbons": "Yıldızlar & Kurdeleler", "PE.Controllers.Main.txtTheme_basic": "Temel", "PE.Controllers.Main.txtTheme_blank": "Boş", "PE.Controllers.Main.txtTheme_classic": "Klasik", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "Noktalı", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "<PERSON>is", "PE.Controllers.Main.txtTheme_office_theme": "Office Teması", "PE.Controllers.Main.txtTheme_official": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Ka<PERSON>lumbağa", "PE.Controllers.Main.txtXAxis": "X Ekseni", "PE.Controllers.Main.txtYAxis": "Y Ekseni", "PE.Controllers.Main.unknownErrorText": "Bilinmeyen hata.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Tarayıcınız desteklenmiyor.", "PE.Controllers.Main.uploadImageExtMessage": "Bilinmeyen resim formatı", "PE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON><PERSON>.", "PE.Controllers.Main.uploadImageSizeMessage": "Görüntü çok büyük. Ma<PERSON><PERSON><PERSON> boyut 25 MB'dir.", "PE.Controllers.Main.uploadImageTextText": "<PERSON>si<PERSON>...", "PE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "PE.Controllers.Main.warnBrowserIE9": "Uygulama IE9'da düşük yeteneklere sahip. IE10 yada daha yükseğini kullanınız", "PE.Controllers.Main.warnBrowserZoom": "Tarayıcınızın mevcut yakınlaştırma ayarı tam olarak desteklenmiyor. Ctrl+0'a basarak varsayılan yakınlaştırmayı sıfırlayınız.", "PE.Controllers.Main.warnLicenseExceeded": "%1 düzenleyiciye eşzamanlı bağlantı sınırına ulaştınız. Bu doküman yalnızca görüntüleme için açılacaktır.<br>Daha fazla bilgi için yöneticinizle iletişime geçin.", "PE.Controllers.Main.warnLicenseExp": "Lisansınızın süresi doldu.<br>Lütfen lisansınızı güncelleyin ve sayfayı yenileyin.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Lisansın süresi doldu.<br>Belge düzenleme işlevine erişiminiz yok.<br>Lütfen yöneticinizle iletişime geçin.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Lisansın yenilenmesi gerekiyor.<br>Belge düzenleme işlevine sınırlı erişiminiz var.<br>Tam erişim için lütfen yöneticinizle iletişime geçin.", "PE.Controllers.Main.warnLicenseUsersExceeded": "%1 düzenleyici için kullanıcı sınırına ulaştınız. Daha fazla bilgi edinmek için yöneticinizle iletişime geçin.", "PE.Controllers.Main.warnNoLicense": "Düzenleyiciler %1 eşzamanlı bağlantı sınırına ulaştı. Bu belge yalnızca görüntüleme için açılacaktır.<br>Kişisel yükseltme koşulları için %1 satış ekibiyle iletişime geçin.", "PE.Controllers.Main.warnNoLicenseUsers": "%1 düzenleyici için kullanıcı sınırına ulaştınız. Kişisel yükseltme koşulları için %1 satış ekibiyle iletişime geçin.", "PE.Controllers.Main.warnProcessRightsChange": "Dosyayı düzenleme hakkınız reddedildi", "PE.Controllers.Search.warnReplaceString": "\n{0}, <PERSON><PERSON><PERSON><PERSON>tir kutusu için geçerli bir özel karakter değil. ", "PE.Controllers.Statusbar.textDisconnect": "<b>Bağlantı kesildi</b><br>Bağlanmayı deneyin. Lütfen bağlantı ayarlarını kontrol edin.", "PE.Controllers.Statusbar.zoomText": "Büyütme {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Kaydedeceğiniz yazı tipi kullandığınız cihazda mevcut değil.<br><PERSON><PERSON><PERSON> tipi, şimdilik cihazınızda bulunan yazı tiplerinden biri ile gösterilecektir, tercih ettiğiniz yazı tipi cihaza yüklendiğinde kullanılacaktır.<br><PERSON><PERSON> etmek istiyor musunuz?", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textEmptyImgUrl": "Resim URL'si belirtmelisiniz.", "PE.Controllers.Toolbar.textFontSizeErr": "Girilen değer yanlış. <br> Lütfen 1 ile 300 arasında sayısal değer giriniz.", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "Fonksiyonlar", "PE.Controllers.Toolbar.textInsert": "<PERSON><PERSON>", "PE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Büyük Operatörler", "PE.Controllers.Toolbar.textLimitAndLog": "<PERSON>it ve Logaritma", "PE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textOperator": "Operatörler", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textWarning": "Uyarı", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Yukarı Sağ-Sol Ok", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Sola Yukarı Ok", "PE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Bar": "Çubuk grafik", "PE.Controllers.Toolbar.txtAccent_BarBot": "Altçizgi", "PE.Controllers.Toolbar.txtAccent_BarTop": "Üstçizgi", "PE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> (Yer Tutu<PERSON>lu)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON> (örnek)", "PE.Controllers.Toolbar.txtAccent_Check": "İşaretle", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Altparantez", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Üstparantez", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektör A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "Çizgili ABC", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Üstçizgili", "PE.Controllers.Toolbar.txtAccent_DDDot": "Üç Nokta", "PE.Controllers.Toolbar.txtAccent_DDot": "Çift Nokta", "PE.Controllers.Toolbar.txtAccent_Dot": "Nokta", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Çift Üst Çizgi", "PE.Controllers.Toolbar.txtAccent_Grave": "Yavaş", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Aşağı Gruplama Karakteri", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Yukarı Gruplama Karakteri", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Sola Yukarı Süslü Ok", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Sağa Yukarı Süslü Ok", "PE.Controllers.Toolbar.txtAccent_Hat": "Şapka", "PE.Controllers.Toolbar.txtAccent_Smile": "K<PERSON>sa", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Ayırıcılı Köşeli Ayraç", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Ayırıcılı Köşeli Ayraç", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Ayırıcılı Köşeli Ayraç", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON> (İki <PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON> (Üç <PERSON>ullu)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Binom <PERSON>sayı", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binom <PERSON>sayı", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Ayırıcılı Köşeli Ayraç", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Tek Parantez", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Tek Parantez", "PE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Diferansiyel", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Diferansiyel", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Diferansiyel", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Diferansiyel", "PE.Controllers.Toolbar.txtFractionHorizontal": "Doğ<PERSON><PERSON> kesir", "PE.Controllers.Toolbar.txtFractionPi_2": "2 Üzeri Pi", "PE.Controllers.Toolbar.txtFractionSmall": "Küçük Kesir", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperbolik Ters Kosinüs Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperbolik Ters Kotanjant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Ters Kosekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperbolik Ters Kosekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Ters Sekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperbolik Ters Sekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperbolik Ters Sinüs Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON> Tan<PERSON>t <PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperbolik Ters Tanjant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hiperbolik Kosinüs Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Coth": "Hiperbolik Kotanjant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Csc": "Kosekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Csch": "Hiperbolik Kosekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sin teta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sec": "Sekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Sech": "Hiperbolik Sekant Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sinh": "Hiperbolik Sinüs Fonksiyonu", "PE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Tanh": "Hiperbolik Tanjant Fonksiyonu", "PE.Controllers.Toolbar.txtIntegral": "İntegral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Diferansiyel teta", "PE.Controllers.Toolbar.txtIntegral_dx": "Diferansiyel x", "PE.Controllers.Toolbar.txtIntegral_dy": "Diferansiyel y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "İntegral", "PE.Controllers.Toolbar.txtIntegralDouble": "Çift İntegral", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Çift İntegral", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Çift İntegral", "PE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Yüzey İ<PERSON>grali", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Yüzey İ<PERSON>grali", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Yüzey İ<PERSON>grali", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralSubSup": "İntegral", "PE.Controllers.Toolbar.txtIntegralTriple": "Üçlü İntegral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Üçlü İntegral", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Üçlü İntegral", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wedge", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ortak İş", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ortak İş", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ortak İş", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ortak İş", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ortak İş", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Toplama", "PE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "PE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON> logari<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logarit<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarit<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON><PERSON> Mat<PERSON>", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Boş Matris", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diyagonal Noktalar", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Biri<PERSON>", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 <PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 <PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 <PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Aşağı Sağ-Sol Ok", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Yukarı Sağ-Sol Ok", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Sola Aşağı Ok", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Sola Yukarı Ok", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Sağa Aşağı Ok", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta Miktarları", "PE.Controllers.Toolbar.txtOperator_Definition": "Tanımla eşittir", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Eşittir", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Aşağı Sağ-Sol Ok", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Yukarı Sağ-Sol Ok", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Sola Aşağı Ok", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Sola Yukarı Ok", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Sağa Aşağı Ok", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON>şit eşittir", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Artı Eşittir", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Ölçüm", "PE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Altsimge", "PE.Controllers.Toolbar.txtScriptSubSup": "Altsimge-Üstsimge", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Sol altsimge-üstsimge", "PE.Controllers.Toolbar.txtScriptSup": "Üstsimge", "PE.Controllers.Toolbar.txtSymbol_about": "Yaklaşık", "PE.Controllers.Toolbar.txtSymbol_additional": "Tamamlayıcı", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "Yaklaşık <PERSON>", "PE.Controllers.Toolbar.txtSymbol_ast": "Asterisk Operatörü", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Madde Operatörü", "PE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Yaklaşık", "PE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_ddots": "Aşağı Sağ Diyagonal Elips", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Aşağı <PERSON>", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Eş<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Eş<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Mevcut", "PE.Controllers.Toolbar.txtSymbol_factorial": "Faktöriyel", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "PE.Controllers.Toolbar.txtSymbol_geq": "Büyük veya Eşitttir", "PE.Controllers.Toolbar.txtSymbol_gg": "Çok büyüktür", "PE.Controllers.Toolbar.txtSymbol_greater": "Büyüktür", "PE.Controllers.Toolbar.txtSymbol_in": "Elemanı", "PE.Controllers.Toolbar.txtSymbol_inc": "Orantısal", "PE.Controllers.Toolbar.txtSymbol_infinity": "Sonsuz", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Sol Ok", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Sol-sağ ok", "PE.Controllers.Toolbar.txtSymbol_leq": "Küçük eşittir", "PE.Controllers.Toolbar.txtSymbol_less": "Küçüktür", "PE.Controllers.Toolbar.txtSymbol_ll": "Çok Küçüktür", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Eksi Artı", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_ni": "Üye İçerir", "PE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "PE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Parçalı Diferansiyel", "PE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON>z<PERSON>", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Artı", "PE.Controllers.Toolbar.txtSymbol_pm": "Artı Eksi", "PE.Controllers.Toolbar.txtSymbol_propto": "Orantısal", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kök", "PE.Controllers.Toolbar.txtSymbol_qed": "İspat Sonu", "PE.Controllers.Toolbar.txtSymbol_rddots": "Yukarı Sağ Diyagonal Elips", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Kök İşareti", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> ne<PERSON>", "PE.Controllers.Toolbar.txtSymbol_theta": "Teta", "PE.Controllers.Toolbar.txtSymbol_times": "Çarpma İşareti", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Yu<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon varyantı", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi <PERSON>ı", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi <PERSON>ı", "PE.Controllers.Toolbar.txtSymbol_varrho": "Rho <PERSON>antı", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma varyantı", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Teta <PERSON>ı", "PE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "<PERSON><PERSON><PERSON>", "PE.Controllers.Viewport.textFitWidth": "Genişliğe Sığdır", "PE.Views.Animation.str0_5": "0,5 sn (Çok Hızlı) ", "PE.Views.Animation.str1": "1 sn (Hızlı) ", "PE.Views.Animation.str2": "2 sn (Orta) ", "PE.Views.Animation.str20": "20 sn (<PERSON> <PERSON><PERSON><PERSON>) ", "PE.Views.Animation.str3": "3 sn (Yavaş) ", "PE.Views.Animation.str5": "5 sn (Çok Yavaş) ", "PE.Views.Animation.strDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strRepeat": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "Otomatik önizleme ", "PE.Views.Animation.textMultiple": "Çoklu", "PE.Views.Animation.textNone": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textNoRepeat": "(hi<PERSON><PERSON><PERSON>)", "PE.Views.Animation.textStartAfterPrevious": "Öncekinden Sonra", "PE.Views.Animation.txtAddEffect": "An<PERSON><PERSON><PERSON> ekle", "PE.Views.Animation.txtAnimationPane": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.AnimationDialog.textTitle": "Daha Fazla Efekt ", "PE.Views.ChartSettings.textAdvanced": "Gelişmiş Ayarları Göster", "PE.Views.ChartSettings.textChartType": "<PERSON><PERSON>", "PE.Views.ChartSettings.textEditData": "<PERSON><PERSON>", "PE.Views.ChartSettings.textHeight": "Yükseklik", "PE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON>", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "Stil", "PE.Views.ChartSettings.textWidth": "Genişlik", "PE.Views.ChartSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAltTip": "G<PERSON><PERSON>l nesne bi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, otomatik şekil, çizelge veya tabloda hangi bilgilerin olduğunu daha iyi anlamalarına yardımcı olmak için görme veya bilişsel bozukluğu olan kişilere okunacak alternatif metin tabanlı temsili.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Başlık", "PE.Views.ChartSettingsAdvanced.textCenter": "Orta ", "PE.Views.ChartSettingsAdvanced.textFrom": "İtibaren", "PE.Views.ChartSettingsAdvanced.textHeight": "Yükseklik ", "PE.Views.ChartSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textTitle": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "{0} i<PERSON><PERSON> \"{1}\" olarak ata", "PE.Views.DateTimeDialog.textDefault": "Varsayılan olarak ata", "PE.Views.DateTimeDialog.textFormat": "Formatlar", "PE.Views.DateTimeDialog.textLang": "Dil", "PE.Views.DateTimeDialog.textUpdate": "Otomatik güncelle", "PE.Views.DateTimeDialog.txtTitle": "Tarih & Saat", "PE.Views.DocumentHolder.aboveText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.addToLayoutText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedImageText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedParagraphText": "Gelişmiş Paragraf A<PERSON>ı", "PE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedTableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.belowText": "Altında", "PE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "Orta", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteRowText": "Satırı Sil", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteText": "Sil", "PE.Views.DocumentHolder.direct270Text": "<PERSON>ini <PERSON> Döndür", "PE.Views.DocumentHolder.direct90Text": "Metini Aşağı Döndür", "PE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.directionText": "Text Direction", "PE.Views.DocumentHolder.editChartText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.editHyperlinkText": "Köprüyü <PERSON>ü<PERSON>le", "PE.Views.DocumentHolder.hyperlinkText": "Köprü", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> yoksay", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON>", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowAboveText": "Üstteki Satır", "PE.Views.DocumentHolder.insertRowBelowText": "Alttaki Satır", "PE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.leftText": "Sol", "PE.Views.DocumentHolder.loadSpellText": "Öneriler yükleniyor...", "PE.Views.DocumentHolder.mergeCellsText": "Hücreleri birleştir", "PE.Views.DocumentHolder.mniCustomTable": "<PERSON><PERSON>", "PE.Views.DocumentHolder.moreText": "<PERSON><PERSON> fazla <PERSON>...", "PE.Views.DocumentHolder.noSpellVariantsText": "Öneri yok", "PE.Views.DocumentHolder.originalSizeText": "G<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "Köprüyü Kaldır", "PE.Views.DocumentHolder.rightText": "Sağ", "PE.Views.DocumentHolder.rowText": "Satır", "PE.Views.DocumentHolder.selectText": "Seç", "PE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON><PERSON> deneti<PERSON>", "PE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON><PERSON>...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeForward": "İleri Taşı", "PE.Views.DocumentHolder.textArrangeFront": "Önplana <PERSON>", "PE.Views.DocumentHolder.textCopy": "Kopyala", "PE.Views.DocumentHolder.textCrop": "Kırpmak", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Sığdır", "PE.Views.DocumentHolder.textCut": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textDistributeCols": "Sütunları dağıt", "PE.Views.DocumentHolder.textDistributeRows": "Satırları dağıt", "PE.Views.DocumentHolder.textEditPoints": "Noktaları Düzenle", "PE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFromUrl": "URL'den", "PE.Views.DocumentHolder.textNextPage": "<PERSON><PERSON><PERSON> slayt", "PE.Views.DocumentHolder.textPaste": "Yapıştır", "PE.Views.DocumentHolder.textPrevPage": "<PERSON><PERSON><PERSON> slayt", "PE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Döndür 90° Saatyönütersi", "PE.Views.DocumentHolder.textRotate90": "Döndür 90° Saatyönü", "PE.Views.DocumentHolder.textShapeAlignBottom": "Alta Hizala", "PE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON> Hizala", "PE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON> Hizala", "PE.Views.DocumentHolder.textSlideSettings": "Slide Settings", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "Bu element şu an başka bir kullanıcı tarafından düzenleniyor.", "PE.Views.DocumentHolder.toDictionaryText": "Sö<PERSON>l<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddBottom": "Alt kenarlık ekle", "PE.Views.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddLB": "Sol alt çizgi ekle", "PE.Views.DocumentHolder.txtAddLeft": "Sol kenarlık ekle", "PE.Views.DocumentHolder.txtAddLT": "Sol üst çizgi ekle", "PE.Views.DocumentHolder.txtAddRight": "Sağ kenarlık ekle", "PE.Views.DocumentHolder.txtAddTop": "Üst kenarlık ekle", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON> e<PERSON>", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Arka plan", "PE.Views.DocumentHolder.txtBorderProps": "Kenarlık özellikleri", "PE.Views.DocumentHolder.txtBottom": "Alt", "PE.Views.DocumentHolder.txtChangeLayout": "Tasarımı Değiştir", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteArg": "Argümanı sil", "PE.Views.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> sonu sil", "PE.Views.DocumentHolder.txtDeleteChars": "Çevreleyen karakterleri sil", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Çevreleyen karakterleri sil", "PE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON> sil", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON> sil", "PE.Views.DocumentHolder.txtDeleteRadical": "Kökü sil", "PE.Views.DocumentHolder.txtDeleteSlide": "Slaytı Sil", "PE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON> o<PERSON>", "PE.Views.DocumentHolder.txtDuplicateSlide": "Slaytı kopyala", "PE.Views.DocumentHolder.txtFractionLinear": "Lineer <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON><PERSON> frak<PERSON>", "PE.Views.DocumentHolder.txtFractionStacked": "Yığılı fraksiyona değiştir", "PE.Views.DocumentHolder.txtGroup": "Grup", "PE.Views.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON> üstünde char", "PE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON> altında char", "PE.Views.DocumentHolder.txtHideBottom": "Alt sınırı gizle", "PE.Views.DocumentHolder.txtHideBottomLimit": "Alt limiti gizle", "PE.Views.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON><PERSON> parantezini gizle", "PE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON> gizle", "PE.Views.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON>izle", "PE.Views.DocumentHolder.txtHideLB": "Sol alt çizgiyi gizle", "PE.Views.DocumentHolder.txtHideLeft": "Sol kenarlığı gizle", "PE.Views.DocumentHolder.txtHideLT": "Sol üst çizgiyi gizle", "PE.Views.DocumentHolder.txtHideOpenBracket": "Açma parantezini gizle", "PE.Views.DocumentHolder.txtHidePlaceholder": "Yer tutuc<PERSON>u gizle", "PE.Views.DocumentHolder.txtHideRight": "Sağ sınırı gizle", "PE.Views.DocumentHolder.txtHideTop": "Üst sınırı gizle", "PE.Views.DocumentHolder.txtHideTopLimit": "Üst limiti gizle", "PE.Views.DocumentHolder.txtHideVer": "<PERSON><PERSON> gizle", "PE.Views.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON><PERSON>ır", "PE.Views.DocumentHolder.txtInsertArgAfter": "<PERSON><PERSON> argüman <PERSON>kle", "PE.Views.DocumentHolder.txtInsertArgBefore": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtInsertBreak": "<PERSON><PERSON><PERSON> son ekle", "PE.Views.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON> denklem ekle", "PE.Views.DocumentHolder.txtInsertEqBefore": "<PERSON><PERSON><PERSON><PERSON> denklem ekle", "PE.Views.DocumentHolder.txtKeepTextOnly": "Yalnızca metni tut", "PE.Views.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON><PERSON>r konum<PERSON>u <PERSON>r", "PE.Views.DocumentHolder.txtLimitOver": "Metin ü<PERSON>ü<PERSON> limit", "PE.Views.DocumentHolder.txtLimitUnder": "Metin altına limit", "PE.Views.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>luğuyla parantezleri eşle", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtNewSlide": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtOverbar": "Metin üstünde bar", "PE.Views.DocumentHolder.txtPasteDestFormat": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPastePicture": "Resim", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Kaynak biçimlendirmesini koruyun", "PE.Views.DocumentHolder.txtPressLink": "{0}'ye basın ve bağlantıya tıklayın", "PE.Views.DocumentHolder.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPrintSelection": "<PERSON><PERSON><PERSON>i <PERSON>", "PE.Views.DocumentHolder.txtRemFractionBar": "Kesir barını kaldır", "PE.Views.DocumentHolder.txtRemLimit": "<PERSON><PERSON> kaldır", "PE.Views.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON> ka<PERSON> kaldır", "PE.Views.DocumentHolder.txtRemoveBar": "Barı kaldır", "PE.Views.DocumentHolder.txtRemScripts": "<PERSON><PERSON>t kaldır", "PE.Views.DocumentHolder.txtRemSubscript": "Altsimge kaldır", "PE.Views.DocumentHolder.txtRemSuperscript": "Üstsimge kaldır", "PE.Views.DocumentHolder.txtResetLayout": "Slaytı Sıfırla", "PE.Views.DocumentHolder.txtScriptsAfter": "Metinden sonra betik", "PE.Views.DocumentHolder.txtScriptsBefore": "Metinden önce betik", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowBottomLimit": "Alt sınırı göster", "PE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON> parantezini <PERSON>", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowOpenBracket": "Açma parantezini g<PERSON>", "PE.Views.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON> tut<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowTopLimit": "Üst sınırı göster", "PE.Views.DocumentHolder.txtSlide": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtSlideHide": "Slaytı gizle", "PE.Views.DocumentHolder.txtStretchBrackets": "Parantezleri g<PERSON>", "PE.Views.DocumentHolder.txtTop": "Üst", "PE.Views.DocumentHolder.txtUnderbar": "<PERSON><PERSON> altında bar", "PE.Views.DocumentHolder.txtUngroup": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtWarnUrl": "Bu bağlantıyı tıklamak cihazınıza ve verilerinize zarar verebilir.<br><PERSON><PERSON> etmek istediğinizden emin misiniz?", "PE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON>", "PE.Views.DocumentPreview.goToSlideText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.slideIndexText": "Slayt {0}/{1}", "PE.Views.DocumentPreview.txtClose": "Önizlemeyi Kapat", "PE.Views.DocumentPreview.txtEndSlideshow": "<PERSON><PERSON>t gösterisi sonu", "PE.Views.DocumentPreview.txtExitFullScreen": "Tam ekrandan çık", "PE.Views.DocumentPreview.txtFinalMessage": "Slayt önizleminin sonu. Çıkış için tıklayın.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON>", "PE.Views.DocumentPreview.txtNext": "<PERSON><PERSON><PERSON> slayt", "PE.Views.DocumentPreview.txtPageNumInvalid": "Geçersiz slayt numarası", "PE.Views.DocumentPreview.txtPause": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtPlay": "<PERSON><PERSON>", "PE.Views.DocumentPreview.txtPrev": "<PERSON><PERSON><PERSON> slayt", "PE.Views.DocumentPreview.txtReset": "Sıfırla", "PE.Views.FileMenu.btnAboutCaption": "Hakkında", "PE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON><PERSON> kon<PERSON>u aç", "PE.Views.FileMenu.btnCloseMenuCaption": "Men<PERSON><PERSON>ü <PERSON>", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnDownloadCaption": "Farklı İndir", "PE.Views.FileMenu.btnExitCaption": "Çıkış", "PE.Views.FileMenu.btnFileOpenCaption": "Aç", "PE.Views.FileMenu.btnHelpCaption": "Yardım", "PE.Views.FileMenu.btnHistoryCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnInfoCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnPrintCaption": "Yazdır", "PE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "En sonunucuyu aç", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON>ı<PERSON>", "PE.Views.FileMenu.btnReturnCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveAsCaption": "Farklı kaydet", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Kopyasını kaydet", "PE.Views.FileMenu.btnSettingsCaption": "Gelişmiş <PERSON>", "PE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Uygulama", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON> değiştir", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Sahip", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokasyon", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Hakkı olan k<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Başlık", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Yüklendi", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON> değiştir", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Hakkı olan k<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Uyarı", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Parola ile", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON><PERSON> ile", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON><PERSON> imzaları kaldıracak.<br><PERSON><PERSON> edilsin mi?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Bu sunum parola ile korunmuştur", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Sunuma geçerli imzalar eklendi. <PERSON><PERSON>, d<PERSON>zenlemeye karşı korumalıdır.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Sunumdaki bazı dijital imzalar geçersiz veya doğrulanamadı. <PERSON><PERSON>, düzenlemeye karşı korumalıdır.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "İmzaları görüntüle", "PE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Ortak Düzenleme Modu", "PE.Views.FileMenuPanels.Settings.strFast": "Hızlı", "PE.Views.FileMenuPanels.Settings.strFontRender": "Yazı Tipi İpucu", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "BÜYÜK HARF içindeki kelimeleri yoksay ", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON><PERSON> keli<PERSON> yoksay ", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strPasteButton": "İçerik yapıştırıldığında Yapıştırma Seçenekleri düğmesini göster", "PE.Views.FileMenuPanels.Settings.strStrict": "Strict", "PE.Views.FileMenuPanels.Settings.strTheme": "Arayüz teması", "PE.Views.FileMenuPanels.Settings.strUnit": "<PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>i", "PE.Views.FileMenuPanels.Settings.strZoom": "Varsayılan Yakınlaştırma <PERSON>eri", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Her 10 dakika", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Her 30 dakika", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Her 5 Dakika", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Her Saat", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Alignment Guides", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Otomatik <PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Otomatik <PERSON>", "PE.Views.FileMenuPanels.Settings.textDisabled": "Devre Dışı", "PE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON> s<PERSON> kaydet", "PE.Views.FileMenuPanels.Settings.textMinute": "Her Dakika", "PE.Views.FileMenuPanels.Settings.txtAll": "Tümünü <PERSON>ö<PERSON>", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Otomatik Düzeltme Seçenekleri", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Varsayılan önbellek modu", "PE.Views.FileMenuPanels.Settings.txtCm": "Santimetre", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "İşbirliği ", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "\nDüzenleme ve kaydetme", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Genişliğe Sığdır", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtInch": "İnç", "PE.Views.FileMenuPanels.Settings.txtLast": "Sonuncuyu göster", "PE.Views.FileMenuPanels.Settings.txtMac": "OS X olarak", "PE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtPt": "Nokta", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Tüm makroları bildirimde bulunmadan etkinleştir", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON><PERSON> deneti<PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Tümünü Devre Dışı Bırak", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Tüm makroları bildirimde bulunmadan devre dışı bırakın", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Bir bildirimle tüm makroları devre dışı bırakın", "PE.Views.FileMenuPanels.Settings.txtWin": "Windows olarak", "PE.Views.HeaderFooterDialog.applyAllText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.applyText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.diffLanguage": "Asıl slayttan farklı bir dilde bir tarih formatı kullanamazsınız.<br>Kal<PERSON>bı değiştirmek için 'Uygula' yerine 'Tümüne uygula'yı tıklayın.", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Uyarı", "PE.Views.HeaderFooterDialog.textDateTime": "Ta<PERSON>h ve saat", "PE.Views.HeaderFooterDialog.textFixed": "Sabit", "PE.Views.HeaderFooterDialog.textFooter": "<PERSON><PERSON>gi<PERSON><PERSON> metin", "PE.Views.HeaderFooterDialog.textFormat": "Formatlar", "PE.Views.HeaderFooterDialog.textLang": "Dil", "PE.Views.HeaderFooterDialog.textNotTitle": "Başlık slaydında gösterme", "PE.Views.HeaderFooterDialog.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textSlideNum": "<PERSON><PERSON><PERSON> numarası", "PE.Views.HeaderFooterDialog.textTitle": "Altbilgi Ayarları", "PE.Views.HeaderFooterDialog.textUpdate": "Otomatik güncelle", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Şuna bağlantıla:", "PE.Views.HyperlinkSettingsDialog.textDefault": "Seçili metin <PERSON>ı", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Başlığı buraya girin", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Bağlantıyı buraya girin", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON> bi<PERSON> buraya girin", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Do<PERSON>a yada İnternet Sayfası", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON> D<PERSON>kü<PERSON>", "PE.Views.HyperlinkSettingsDialog.textSlides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTipText": "Ekran<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTitle": "Köprü Ayarları", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> alan g<PERSON>", "PE.Views.HyperlinkSettingsDialog.txtFirst": "İlk <PERSON>t", "PE.Views.HyperlinkSettingsDialog.txtLast": "<PERSON>", "PE.Views.HyperlinkSettingsDialog.txtNext": "<PERSON><PERSON><PERSON> slayt", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON>u alan \"http://www.example.com\" formatında URL olmalıdır", "PE.Views.HyperlinkSettingsDialog.txtPrev": "<PERSON><PERSON><PERSON> slayt", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Bu alan 2083 karakterle sınırlıdır", "PE.Views.HyperlinkSettingsDialog.txtSlide": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textAdvanced": "Gelişmiş ayarları göster", "PE.Views.ImageSettings.textCrop": "Kırpmak", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Sığdır", "PE.Views.ImageSettings.textCropToShape": "Şekillendirmek için kırp", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFitSlide": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromUrl": "URL'den", "PE.Views.ImageSettings.textHeight": "Yükseklik", "PE.Views.ImageSettings.textHint270": "Döndür 90° Saatyönütersi", "PE.Views.ImageSettings.textHint90": "Döndür 90° Saatyönü", "PE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON>", "PE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textOriginalSize": "G<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textRecentlyUsed": "<PERSON>", "PE.Views.ImageSettings.textRotate90": "Döndür 90°", "PE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "Genişlik", "PE.Views.ImageSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAltTip": "G<PERSON><PERSON>l nesne bi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, otomatik şekil, çizelge veya tabloda hangi bilgilerin olduğunu daha iyi anlamalarına yardımcı olmak için görme veya bilişsel bozukluğu olan kişilere okunacak alternatif metin tabanlı temsili.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Başlık", "PE.Views.ImageSettingsAdvanced.textAngle": "Açı", "PE.Views.ImageSettingsAdvanced.textCenter": "Orta", "PE.Views.ImageSettingsAdvanced.textFlipped": "Çevrilmiş", "PE.Views.ImageSettingsAdvanced.textFrom": "İtibaren", "PE.Views.ImageSettingsAdvanced.textHeight": "Yükseklik", "PE.Views.ImageSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON> ", "PE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "G<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "<PERSON><PERSON><PERSON><PERSON>rm<PERSON>", "PE.Views.ImageSettingsAdvanced.textPosition": "Pozisyon", "PE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "Resim - <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textWidth": "Genişlik", "PE.Views.LeftMenu.tipAbout": "Hakkında", "PE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSearch": "Ara", "PE.Views.LeftMenu.tipSlides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSupport": "Geri Bildirim & Destek", "PE.Views.LeftMenu.tipTitles": "Başlıklar", "PE.Views.LeftMenu.txtDeveloper": "GELİŞTİRİCİ MODU", "PE.Views.LeftMenu.txtLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtTrial": "DENEME MODU", "PE.Views.LeftMenu.txtTrialDev": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON>r <PERSON>", "PE.Views.ParagraphSettings.strParagraphSpacing": "Paragraf <PERSON>", "PE.Views.ParagraphSettings.strSpacingAfter": "Sonra", "PE.Views.ParagraphSettings.strSpacingBefore": "Önce", "PE.Views.ParagraphSettings.textAdvanced": "Gelişmiş ayarları göster", "PE.Views.ParagraphSettings.textAt": "Şurada:", "PE.Views.ParagraphSettings.textAtLeast": "En az", "PE.Views.ParagraphSettings.textAuto": "Çoklu", "PE.Views.ParagraphSettings.textExact": "<PERSON>", "PE.Views.ParagraphSettings.txtAutoText": "Otomatik", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Belirtilen sekmeler bu alanda görünecektir", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Üstü çift çizili", "PE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Sol", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "<PERSON><PERSON><PERSON>r <PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Sağ", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Sonra", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Önce", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Yazı Tipi", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Girinti & Boşluk", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Küçük harfler", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Boşluk", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Üstü çizili", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Altsimge", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Üstsimge", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Sekme", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Hi<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Çoklu", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Varsayılan Sekme", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "İlk Satır", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Asılı", "PE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON> yana yaslı", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(hi<PERSON><PERSON><PERSON>)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Kaldır", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textSet": "Belirt", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Orta", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Sol", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Sağ", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraf - <PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Otomatik", "PE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON>", "PE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtParagraphSettings": "Paragraf <PERSON>", "PE.Views.RightMenu.txtShapeSettings": "Şekil <PERSON>", "PE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON>za <PERSON>ı", "PE.Views.RightMenu.txtSlideSettings": "Slayt Ayarları", "PE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtTextArtSettings": "Yazı Sanatı ayarları", "PE.Views.ShapeSettings.strBackground": "Arka plan rengi", "PE.Views.ShapeSettings.strChange": "Otomatik Şeklini Değiştir", "PE.Views.ShapeSettings.strColor": "Renk", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Önplan rengi", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strShadow": "Gölgeleri göster", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Opaklık", "PE.Views.ShapeSettings.strType": "Tip", "PE.Views.ShapeSettings.textAdvanced": "Gelişmiş ayarları göster", "PE.Views.ShapeSettings.textAngle": "Açı", "PE.Views.ShapeSettings.textBorderSizeErr": "Girilen değer yanlış. <br> Lütfen 0 ile 1584 pt arasında değer giriniz.", "PE.Views.ShapeSettings.textColor": "Renk Dolgusu", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromUrl": "URL'den", "PE.Views.ShapeSettings.textGradient": "<PERSON>an no<PERSON>ı", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textHint270": "Döndür 90° Saatyönütersi", "PE.Views.ShapeSettings.textHint90": "Döndür 90° Saatyönü", "PE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON><PERSON> ya<PERSON>", "PE.Views.ShapeSettings.textLinear": "Doğrusal", "PE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textPosition": "Pozisyon", "PE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textRecentlyUsed": "<PERSON>", "PE.Views.ShapeSettings.textRotate90": "Döndür 90°", "PE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectTexture": "Seç", "PE.Views.ShapeSettings.textStretch": "Esnet", "PE.Views.ShapeSettings.textStyle": "Stil", "PE.Views.ShapeSettings.textTexture": "Doldurma deseninden", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.tipAddGradientPoint": "<PERSON><PERSON> nokt<PERSON> e<PERSON>", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Gradyan noktasını kaldır", "PE.Views.ShapeSettings.txtBrownPaper": "Kahvereng<PERSON>", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "Koyu Örgü", "PE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "Çizgi yok", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "Ahşap", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAltTip": "G<PERSON><PERSON>l nesne bi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, otomatik şekil, çizelge veya tabloda hangi bilgilerin olduğunu daha iyi anlamalarına yardımcı olmak için görme veya bilişsel bozukluğu olan kişilere okunacak alternatif metin tabanlı temsili.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Başlık", "PE.Views.ShapeSettingsAdvanced.textAngle": "Açı", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Otomatik Sığdır", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Başlama Boyutu", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Başlama Stili", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Alt", "PE.Views.ShapeSettingsAdvanced.textCapType": "Başlık Tipi", "PE.Views.ShapeSettingsAdvanced.textCenter": "Orta", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Bitiş Stili", "PE.Views.ShapeSettingsAdvanced.textFlat": "Düz", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Çevrilmiş", "PE.Views.ShapeSettingsAdvanced.textFrom": "İtibaren", "PE.Views.ShapeSettingsAdvanced.textHeight": "Yükseklik", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON> ", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Katılım Tipi", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLeft": "Sol", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON> stili", "PE.Views.ShapeSettingsAdvanced.textMiter": "G<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "Otomatik Sığdırma Yapma", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Metne sığdırmak için şekli yeniden boyutlandırın", "PE.Views.ShapeSettingsAdvanced.textRight": "Sağ", "PE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShrink": "Taşma durumunda metni kü<PERSON>ült", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Sütunlar arasında boşluk", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTitle": "Şekil - Gelişmiş Ayarlar", "PE.Views.ShapeSettingsAdvanced.textTop": "Üst", "PE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Ağırlık & oklar", "PE.Views.ShapeSettingsAdvanced.textWidth": "Genişlik", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Uyarı", "PE.Views.SignatureSettings.strDelete": "İmzayı kaldır", "PE.Views.SignatureSettings.strDetails": "İmza Ayrıntıları", "PE.Views.SignatureSettings.strInvalid": "Geçersiz im<PERSON>ar", "PE.Views.SignatureSettings.strSign": "İmzala", "PE.Views.SignatureSettings.strSignature": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON>", "PE.Views.SignatureSettings.txtEditWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON><PERSON> imzaları kaldıracak.<br><PERSON><PERSON> edilsin mi?", "PE.Views.SignatureSettings.txtRemoveWarning": "Bu imzayı kaldırmak istiyor musunuz?<br><PERSON><PERSON>.", "PE.Views.SignatureSettings.txtSigned": "Sunuma geçerli imzalar eklendi. <PERSON><PERSON>, d<PERSON>zenlemeye karşı korumalıdır.", "PE.Views.SignatureSettings.txtSignedInvalid": "Sunumdaki bazı dijital imzalar geçersiz veya doğrulanamadı. <PERSON><PERSON>, düzenlemeye karşı korumalıdır.", "PE.Views.SlideSettings.strBackground": "Arka plan rengi", "PE.Views.SlideSettings.strColor": "Renk", "PE.Views.SlideSettings.strDateTime": "<PERSON><PERSON><PERSON> <PERSON>", "PE.Views.SlideSettings.strFill": "Arka Plan", "PE.Views.SlideSettings.strForeground": "Önplan rengi", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON>", "PE.Views.SlideSettings.strSlideNum": "Slayt Numarasını Göster", "PE.Views.SlideSettings.strTransparency": "Opaklık", "PE.Views.SlideSettings.textAdvanced": "Gelişmiş ayarları göster", "PE.Views.SlideSettings.textAngle": "Açı", "PE.Views.SlideSettings.textColor": "Renk Dolgusu", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textFromStorage": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textFromUrl": "URL'den", "PE.Views.SlideSettings.textGradient": "<PERSON>an no<PERSON>ı", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.SlideSettings.textImageTexture": "<PERSON><PERSON><PERSON> ya<PERSON>", "PE.Views.SlideSettings.textLinear": "Doğrusal", "PE.Views.SlideSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON>", "PE.Views.SlideSettings.textPosition": "Pozisyon", "PE.Views.SlideSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textReset": "Değişiklikleri Sıfırla", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textSelectTexture": "Seç", "PE.Views.SlideSettings.textStretch": "Esnet", "PE.Views.SlideSettings.textStyle": "Stil", "PE.Views.SlideSettings.textTexture": "Doldurma deseninden", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON>", "PE.Views.SlideSettings.tipAddGradientPoint": "<PERSON><PERSON> nokt<PERSON> e<PERSON>", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Gradyan noktasını kaldır", "PE.Views.SlideSettings.txtBrownPaper": "Kahvereng<PERSON>", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "Koyu Örgü", "PE.Views.SlideSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "Ahşap", "PE.Views.SlideshowSettings.textLoop": "'Esc' tuşuna basılana kadar loop devam eder", "PE.Views.SlideshowSettings.textTitle": "Ayarları göster", "PE.Views.SlideSizeSettings.strLandscape": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.strPortrait": "Portre", "PE.Views.SlideSizeSettings.textHeight": "Yükseklik", "PE.Views.SlideSizeSettings.textSlideOrientation": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideSize": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textTitle": "<PERSON><PERSON>t Boyut<PERSON>", "PE.Views.SlideSizeSettings.textWidth": "Genişlik", "PE.Views.SlideSizeSettings.txt35": "35 mm slaytlar", "PE.Views.SlideSizeSettings.txtA3": "A3 Kağıt (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Kağıt (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Kağıt (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Kağıt (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "A<PERSON>ş", "PE.Views.SlideSizeSettings.txtCustom": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON><PERSON> (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "<PERSON><PERSON><PERSON> (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtStandard": "<PERSON><PERSON> (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Geniş ekran", "PE.Views.Statusbar.goToPageText": "<PERSON><PERSON><PERSON>", "PE.Views.Statusbar.pageIndexText": "Slayt {0}/{1}", "PE.Views.Statusbar.textShowBegin": "Başlangıçtan itibaren gö<PERSON>", "PE.Views.Statusbar.textShowCurrent": "Mevcut slayttan itiba<PERSON>", "PE.Views.Statusbar.textShowPresenterView": "<PERSON><PERSON><PERSON> g<PERSON> geç", "PE.Views.Statusbar.tipAccessRights": "Belge eri<PERSON><PERSON> ha<PERSON> yönet", "PE.Views.Statusbar.tipFitPage": "<PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipFitWidth": "Genişliğe Sığdır", "PE.Views.Statusbar.tipPreview": "Start Preview", "PE.Views.Statusbar.tipSetLang": "<PERSON><PERSON>", "PE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipZoomIn": "Yakınlaştır", "PE.Views.Statusbar.tipZoomOut": "Uzaklaştır", "PE.Views.Statusbar.txtPageNumInvalid": "Geçersiz slayt numarası", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.deleteRowText": "Satırı Sil", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON>", "PE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.insertRowAboveText": "Yukarı Satır <PERSON>", "PE.Views.TableSettings.insertRowBelowText": "Aşağı Satır E<PERSON>", "PE.Views.TableSettings.mergeCellsText": "Hücreleri birleştir", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON><PERSON>...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "Gelişmiş ayarları göster", "PE.Views.TableSettings.textBackColor": "Arka plan rengi", "PE.Views.TableSettings.textBanded": "Bağlı", "PE.Views.TableSettings.textBorderColor": "Renk", "PE.Views.TableSettings.textBorders": "Kenarlık Stili", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "Sütunları dağıt", "PE.Views.TableSettings.textDistributeRows": "Satırları dağıt", "PE.Views.TableSettings.textEdit": "Satırlar & Sütunlar", "PE.Views.TableSettings.textEmptyTemplate": "Şablon yok", "PE.Views.TableSettings.textFirst": "ilk", "PE.Views.TableSettings.textHeader": "Üst Bilgi", "PE.Views.TableSettings.textHeight": "Yükseklik", "PE.Views.TableSettings.textLast": "Son", "PE.Views.TableSettings.textRows": "Satırlar", "PE.Views.TableSettings.textSelectBorders": "Yukarıda seçilen stili uygulayarak değiştirmek istediğiniz sınırları seçin", "PE.Views.TableSettings.textTemplate": "Şablondan Seç", "PE.Views.TableSettings.textTotal": "Toplam", "PE.Views.TableSettings.textWidth": "Genişlik", "PE.Views.TableSettings.tipAll": "<PERSON><PERSON>ş kenarlığı ve tüm iç çizgileri ayarla", "PE.Views.TableSettings.tipBottom": "Sadece Dış Alt Sınırı Belirle", "PE.Views.TableSettings.tipInner": "Sadece İç Satırları Belirle", "PE.Views.TableSettings.tipInnerHor": "Sadece Yatay İç Satırları Belirle", "PE.Views.TableSettings.tipInnerVert": "Sad<PERSON>e Dikey İç Çizgileri Belirle", "PE.Views.TableSettings.tipLeft": "Sadece Dış Sol Sınırı Belirle", "PE.Views.TableSettings.tipNone": "Kenarlık belirleme", "PE.Views.TableSettings.tipOuter": "Sadece Dış Kenarlığı Ayarla", "PE.Views.TableSettings.tipRight": "Sadece Dış Sağ Sınırı Belirle", "PE.Views.TableSettings.tipTop": "Sadece Dış Üst Sınırı Belirle", "PE.Views.TableSettings.txtNoBorders": "Sınır yok", "PE.Views.TableSettings.txtTable_Accent": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_DarkStyle": "Koyu Stil", "PE.Views.TableSettings.txtTable_LightStyle": "Açık Stil", "PE.Views.TableSettings.txtTable_MediumStyle": "Orta Stil", "PE.Views.TableSettings.txtTable_NoGrid": "Izgara Yok", "PE.Views.TableSettings.txtTable_NoStyle": "Stil yok", "PE.Views.TableSettings.txtTable_TableGrid": "Tablo <PERSON>ı", "PE.Views.TableSettings.txtTable_ThemedStyle": "Temalı Stil", "PE.Views.TableSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAltTip": "G<PERSON><PERSON>l nesne bi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, otomatik şekil, çizelge veya tabloda hangi bilgilerin olduğunu daha iyi anlamalarına yardımcı olmak için görme veya bilişsel bozukluğu olan kişilere okunacak alternatif metin tabanlı temsili.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Başlık", "PE.Views.TableSettingsAdvanced.textBottom": "Alt", "PE.Views.TableSettingsAdvanced.textCenter": "Orta", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Varsayılan kenar boşluklarını kullan", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Varsayılan Kenar Boşlukları", "PE.Views.TableSettingsAdvanced.textFrom": "İtibaren", "PE.Views.TableSettingsAdvanced.textHeight": "Yükseklik", "PE.Views.TableSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON> ", "PE.Views.TableSettingsAdvanced.textKeepRatio": "<PERSON><PERSON> ", "PE.Views.TableSettingsAdvanced.textLeft": "Sol", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textRight": "Sağ", "PE.Views.TableSettingsAdvanced.textTitle": "Tablo - Gelişmiş Ayarlar", "PE.Views.TableSettingsAdvanced.textTop": "Üst", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "Arka plan rengi", "PE.Views.TextArtSettings.strColor": "Renk", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "Foreground color", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strSize": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Opaklık", "PE.Views.TextArtSettings.strType": "Tip", "PE.Views.TextArtSettings.textAngle": "Açı", "PE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "PE.Views.TextArtSettings.textColor": "Renk Dolgusu", "PE.Views.TextArtSettings.textDirection": "Direction", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textFromUrl": "URL'den", "PE.Views.TextArtSettings.textGradient": "<PERSON>an no<PERSON>ı", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "PE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON><PERSON> ya<PERSON>", "PE.Views.TextArtSettings.textLinear": "Doğrusal", "PE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textPosition": "Pozisyon", "PE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textSelectTexture": "Seç", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "Stil", "PE.Views.TextArtSettings.textTemplate": "Template", "PE.Views.TextArtSettings.textTexture": "Doldurma deseninden", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "Transform", "PE.Views.TextArtSettings.tipAddGradientPoint": "<PERSON><PERSON> nokt<PERSON> e<PERSON>", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Gradyan noktasını kaldır", "PE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "Koyu Örgü", "PE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtGranite": "Granit", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "Çizgi yok", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "<PERSON>", "PE.Views.Toolbar.capAddSlide": "<PERSON><PERSON><PERSON> e<PERSON>", "PE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON>", "PE.Views.Toolbar.capBtnComment": "<PERSON>rum yap", "PE.Views.Toolbar.capBtnDateTime": "Tarih & Saat", "PE.Views.Toolbar.capBtnInsHeader": "Altbilgi", "PE.Views.Toolbar.capBtnInsSymbol": "<PERSON>m<PERSON>", "PE.Views.Toolbar.capBtnSlideNum": "<PERSON><PERSON><PERSON> numarası", "PE.Views.Toolbar.capInsertAudio": "Ses", "PE.Views.Toolbar.capInsertChart": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertEquation": "Denklem", "PE.Views.Toolbar.capInsertHyperlink": "Köprü", "PE.Views.Toolbar.capInsertImage": "Resim", "PE.Views.Toolbar.capInsertShape": "Şekil", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capTabHome": "<PERSON>", "PE.Views.Toolbar.capTabInsert": "<PERSON><PERSON>", "PE.Views.Toolbar.mniCapitalizeWords": "Her Kelimeyi Büyük Harf Yap", "PE.Views.Toolbar.mniCustomTable": "<PERSON><PERSON>", "PE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON><PERSON> resim", "PE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniImageFromUrl": "URL'den resim", "PE.Views.Toolbar.mniInsertSSE": "E-tab<PERSON>", "PE.Views.Toolbar.mniLowerCase": "küçük harf", "PE.Views.Toolbar.mniSentenceCase": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>.", "PE.Views.Toolbar.mniSlideAdvanced": "Gelişmiş <PERSON>", "PE.Views.Toolbar.mniSlideStandard": "<PERSON><PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "bAŞ hARFİ kÜÇÜK", "PE.Views.Toolbar.mniUpperCase": "BÜYÜKHARF", "PE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textAlignBottom": "<PERSON>ni aşağı hizala", "PE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON> metin", "PE.Views.Toolbar.textAlignJust": "<PERSON><PERSON> yana yasla", "PE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON>", "PE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON>", "PE.Views.Toolbar.textAlignRight": "<PERSON><PERSON>", "PE.Views.Toolbar.textAlignTop": "<PERSON>ni yukarı hizala", "PE.Views.Toolbar.textArrangeBack": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textArrangeBackward": "<PERSON><PERSON>", "PE.Views.Toolbar.textArrangeForward": "İleri Taşı", "PE.Views.Toolbar.textArrangeFront": "Önplana <PERSON>", "PE.Views.Toolbar.textBold": "Kalı<PERSON>", "PE.Views.Toolbar.textColumnsCustom": "<PERSON><PERSON>", "PE.Views.Toolbar.textColumnsOne": "Bir <PERSON>", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON>", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textListSettings": "Liste Ayarları", "PE.Views.Toolbar.textRecentlyUsed": "<PERSON>", "PE.Views.Toolbar.textShapeAlignBottom": "Alta Hizala", "PE.Views.Toolbar.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON> Hizala", "PE.Views.Toolbar.textShapeAlignMiddle": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON> Hizala", "PE.Views.Toolbar.textShowBegin": "Başlangıçtan itibaren gö<PERSON>", "PE.Views.Toolbar.textShowCurrent": "Mevcut slayttan itiba<PERSON>", "PE.Views.Toolbar.textShowPresenterView": "<PERSON><PERSON><PERSON> g<PERSON> geç", "PE.Views.Toolbar.textShowSettings": "Ayarları göster", "PE.Views.Toolbar.textStrikeout": "Üstü çizili", "PE.Views.Toolbar.textSubscript": "Altsimge", "PE.Views.Toolbar.textSuperscript": "Üstsimge", "PE.Views.Toolbar.textTabAnimation": "Animasyon", "PE.Views.Toolbar.textTabCollaboration": "Ortak Çalışma", "PE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabHome": "<PERSON>", "PE.Views.Toolbar.textTabInsert": "<PERSON><PERSON>", "PE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabTransitions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON>", "PE.Views.Toolbar.textUnderline": "Altı çizili", "PE.Views.Toolbar.tipAddSlide": "<PERSON><PERSON><PERSON> e<PERSON>", "PE.Views.Toolbar.tipBack": "<PERSON><PERSON>", "PE.Views.Toolbar.tipChangeCase": "Büyük/küçük harf değiştir", "PE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON>", "PE.Views.Toolbar.tipChangeSlide": "Slayt Tasarımını Değiştir", "PE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON>", "PE.Views.Toolbar.tipColorSchemas": "Renk Şemasını Değiştir", "PE.Views.Toolbar.tipColumns": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopy": "Kopyala", "PE.Views.Toolbar.tipCopyStyle": "St<PERSON>", "PE.Views.Toolbar.tipDateTime": "Şimdiki tarih ve saati ekle", "PE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON> boy<PERSON><PERSON>u a<PERSON>", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipEditHeader": "Altlığı Düzenle", "PE.Views.Toolbar.tipFontColor": "Yazı Tipi Rengi", "PE.Views.Toolbar.tipFontName": "Yazı Tipi", "PE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> boyutu", "PE.Views.Toolbar.tipHAligh": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON> boyutunu art<PERSON>ır", "PE.Views.Toolbar.tipIncPrLeft": "<PERSON><PERSON><PERSON><PERSON> Arttır", "PE.Views.Toolbar.tipInsertAudio": "<PERSON><PERSON> e<PERSON>", "PE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "PE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertShape": "Otomatik Şekil ekle", "PE.Views.Toolbar.tipInsertSymbol": "<PERSON><PERSON><PERSON> e<PERSON>", "PE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "PE.Views.Toolbar.tipInsertText": "<PERSON><PERSON> kutusu e<PERSON>", "PE.Views.Toolbar.tipInsertTextArt": "Yazı Sanatı Ekle", "PE.Views.Toolbar.tipInsertVideo": "Video ekle", "PE.Views.Toolbar.tipLineSpace": "<PERSON><PERSON><PERSON>r <PERSON>", "PE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersArrow": "Ok işaretleri", "PE.Views.Toolbar.tipMarkersCheckmark": "<PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersDash": "Çizgi işaretleri", "PE.Views.Toolbar.tipMarkersFRhombus": "Dolu eşkenar dörtgen işaretler", "PE.Views.Toolbar.tipMarkersFRound": "<PERSON><PERSON> yu<PERSON>lak işaretler", "PE.Views.Toolbar.tipMarkersFSquare": "<PERSON><PERSON> i<PERSON>", "PE.Views.Toolbar.tipMarkersHRound": "İçi boş daire işaretler", "PE.Views.Toolbar.tipMarkersStar": "Yıldız işaretleri", "PE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPaste": "Yapıştır", "PE.Views.Toolbar.tipPreview": "Önizlemeye Başla", "PE.Views.Toolbar.tipPrint": "Yazdır", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> k<PERSON>ıların görmesi için değişikliklerinizi kaydedin.", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipShapeArrange": "Şekli <PERSON>", "PE.Views.Toolbar.tipSlideNum": "<PERSON><PERSON><PERSON> numarası ekle", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSlideTheme": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "<PERSON><PERSON>", "PE.Views.Toolbar.tipViewSettings": "Ayarları Göster", "PE.Views.Toolbar.txtDistribHor": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtDistribVert": "<PERSON><PERSON> o<PERSON>", "PE.Views.Toolbar.txtDuplicateSlide": "Slaytı kopyala", "PE.Views.Toolbar.txtGroup": "Grup", "PE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme1": "<PERSON>is", "PE.Views.Toolbar.txtScheme10": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme15": "Orjin", "PE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme17": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme18": "Yöntem", "PE.Views.Toolbar.txtScheme19": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme2": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme20": "Kentsel", "PE.Views.Toolbar.txtScheme21": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme22": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Açı", "PE.Views.Toolbar.txtScheme5": "Kentsel", "PE.Views.Toolbar.txtScheme6": "Toplama", "PE.Views.Toolbar.txtScheme7": "<PERSON>", "PE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme9": "Döküm", "PE.Views.Toolbar.txtSlideAlign": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtUngroup": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "Tıkla Başla", "PE.Views.Transitions.textBlack": "<PERSON>yah Üzerinden", "PE.Views.Transitions.textBottom": "Alt", "PE.Views.Transitions.textBottomLeft": "Alt-Sol", "PE.Views.Transitions.textBottomRight": "Alt-Sağ", "PE.Views.Transitions.textClock": "Saat", "PE.Views.Transitions.textClockwise": "Saat yönünde", "PE.Views.Transitions.textCounterclockwise": "Saat Yönü<PERSON>ün <PERSON>", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalOut": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textLeft": "Sol", "PE.Views.Transitions.textNone": "Hiç<PERSON>i", "PE.Views.Transitions.textPush": "İt", "PE.Views.Transitions.textRight": "Sağ", "PE.Views.Transitions.textSmoothly": "Kolayca", "PE.Views.Transitions.textSplit": "<PERSON><PERSON>ır", "PE.Views.Transitions.textTop": "Üst", "PE.Views.Transitions.textTopLeft": "Üst-Sol", "PE.Views.Transitions.textTopRight": "Üst-Sağ", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "<PERSON><PERSON>", "PE.Views.Transitions.textVerticalOut": "<PERSON><PERSON>", "PE.Views.Transitions.textWedge": "Wedge", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomIn": "Yakınlaştır", "PE.Views.Transitions.textZoomOut": "Uzaklaştır", "PE.Views.Transitions.textZoomRotate": "B<PERSON><PERSON>ü<PERSON> ve Döndür", "PE.Views.Transitions.txtApplyToAll": "<PERSON><PERSON><PERSON> Uygu<PERSON>", "PE.Views.Transitions.txtParameters": "Parametreler", "PE.Views.Transitions.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textAlwaysShowToolbar": "Her zaman araç <PERSON>", "PE.Views.ViewTab.textFitToSlide": "Slayda Sığdır", "PE.Views.ViewTab.textFitToWidth": "Genişliğe Sığdır", "PE.Views.ViewTab.textInterfaceTheme": "Arayüz teması ", "PE.Views.ViewTab.textNotes": "notlar", "PE.Views.ViewTab.textZoom": "Yakınlaştırma"}