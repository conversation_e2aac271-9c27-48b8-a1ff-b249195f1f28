﻿<!DOCTYPE html>
<html>
	<head>
		<title>ISTGERADE-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>ISTGERADE-Funktion</h1>
			<p>Die Funktion <b>ISTGERADE</b> gehört zur Gruppe der Informationsfunktionen. Sie überprüft den ausgewählten Bereich auf einen geraden Wert. Ist ein gerader Wert vorhanden, gibt die Funktion WAHR wieder. Wird ein ungerader Wert gefunden, gibt die Funktion FALSCH wieder.</p>
			<p>Die Formelsyntax der Funktion <b>ISTGERADE</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>ISTGERADE(Zahl)</em></b></p> 
			<p><b><em>Zahl</em></b> ist ein Wert der geprüft werden soll. Sie können den zu überprüfenden Bereich manuell eingeben oder mit der Maus auswählen.</p>
			<p class="note"><b>Hinweis</b>: Ist <b><em>Zahl</em></b> kein numerischer Ausdruck, gibt ISTGERADE den Fehlerwert #WERT! zurück.</p>
			<p>Anwendung der Funktion <b>ISTGERADE</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Informationsfunktionen</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>ISTGERADE</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="ISTGERADE-Funktion" src="../images/iseven.png" /></p>
		</div>
	</body>
</html>